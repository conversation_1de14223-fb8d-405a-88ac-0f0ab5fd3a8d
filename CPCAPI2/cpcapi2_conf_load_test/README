HOW TO BUILD

cmake -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo
ninja

objcopy --only-keep-debug cpcapi2_conf_load_test cpcapi2_conf_load_test.debug
objcopy --strip-debug cpcapi2_conf_load_test cpcapi2_conf_load_test
objcopy --add-gnu-debuglink=cpcapi2_conf_load_test.debug cpcapi2_conf_load_test

HOW TO USE

1) Edit the cpcapi2_conf_load_test.config file and specify the number of test endpoints, the PTT service URL, and the PTT channel (or endpoint address) to use.

2) Launch ./cpcapi2_conf_load_test

3) Wait for the endpoints to finish connecting (they connect at a rate of one per second).

4) Use a (real) endpoint to initiate a PTT to the PTT channel / endpoint specified for use with the test tool.

5) The test tool should activate, with all of the test users listening for the duration of the PTT broadcast.

6) When you release the PTT button on the real endpoint, the test endpoints should disconnect and return to a "waiting" state,
i.e. waiting for the next PTT on that channel / endpoint address.

