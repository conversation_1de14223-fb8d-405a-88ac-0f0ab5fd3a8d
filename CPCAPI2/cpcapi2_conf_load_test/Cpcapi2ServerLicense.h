#pragma once

#if defined(_WIN32) || defined(__linux__) || defined(__APPLE__)
#define LICENSE_AOR "<EMAIL>"
#define INVALID_LICENSE_URL "https://invalid.domain.cp"
#ifdef _WIN32
#define LICENSE_KEY_1 "180WTV8PN1AV6RE99MP3N0228"
#define LICENSE_KEY_2 "180T0QK9NCW807N19MP3N0220"
#define LICENSE_KEY_3 "1802TY304QYGVYNK9MP3N0211"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "C:\\Windows\\Temp"
#endif

#ifdef __linux__
#define LICENSE_KEY_1 "180464N3P5BJYP2K9MP3N0230"
#define LICENSE_KEY_2 "180J6WY31SY21PY39MP3N0205"
#define LICENSE_KEY_3 "1802MDVHK7DNGJ7P9MP3N0232"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#endif

#if TARGET_OS_IPHONE
#define LICENSE_KEY_1 "1808998YM4XER2809MP3N0234"
#define LICENSE_KEY_2 "1802CMXWBMTE23B99MP3N0211"
#define LICENSE_KEY_3 "1800NKGSNR3B8JQ69MP3N0210"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION GetTempDir()
#elif TARGET_OS_MAC
#define LICENSE_KEY_1 "180MEKEZSR1T0HB29MP3N0233"
#define LICENSE_KEY_2 "1802QQ7Y36NMJ09B9MP3N0236"
#define LICENSE_KEY_3 "1804KYBYJE0W9JT39MP3N0210"
#define INVALID_LICENSE_KEY "180MXZ3SKS2NRMZJ9MP3N0228"
#define LICENSE_DOCUMENT_LOCATION "/tmp"
#endif
#endif
