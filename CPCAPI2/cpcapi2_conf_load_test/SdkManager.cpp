#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2ServerLicense.h"
#include "rutil/ConfigParse.hxx"
#include "rutil/Random.hxx"

#include <confconnector/ConferenceConnector.h>
#include <confconnector/ConferenceConnectorHandler.h>
#include <confconnector/ConferenceConnectorTypes.h>
#include <orchestration_server/OrchestrationServer.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeJsonApi.h>
#include <confbridge/ConferenceBridgeManager.h>
#include <media/MediaManagerInternal.h>

#include "../CPCAPI2/impl/phone/PhoneInterface.h"

#include <cpcapi2.h>

#include <fstream>
#include <functional>
#include <thread>
#include <future>
#include <mutex>
#include <condition_variable>

#ifndef WIN32
#include <signal.h>
#endif

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::ConferenceConnector;

namespace CPCAPI2
{
namespace ConfBridge
{

class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

std::function<void(int)> signal_callback_wrapper;
void signal_callback_function(int val)
{
   signal_callback_wrapper(val);
}

SdkManager::SdkManager() : mLicensingMgr(NULL)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void SdkManager::sigtermHandler(int signum)
{
   shutdown();
}

class MyConfConnHandler : public CPCAPI2::ConferenceConnector::ConferenceConnectorHandler
{
public:
	MyConfConnHandler(CPCAPI2::ConferenceConnector::ConferenceConnectorManager* confConnMgr) : mTestDone(false), mWaiting(false), mProcessThread(nullptr) {
    	    mProcessThread = new std::thread([&, confConnMgr]() {
			while (!mTestDone) {
				if (mWaiting) {
					confConnMgr->process(CPCAPI2::kBlockingModeNonBlocking);
				}
				std::this_thread::sleep_for(std::chrono::milliseconds(20));
			}
		});
	}
	virtual ~MyConfConnHandler() { mTestDone = true; mProcessThread->join(); delete mProcessThread; }
	
	bool waitFor(const std::string& evtName, unsigned int timeoutMs) {
		mWaiting = true;
		for (;;) {
			std::unique_lock<std::mutex> lck(mWaitMtx);
			if (mWaitCond.wait_for(lck, std::chrono::milliseconds(timeoutMs)) == std::cv_status::timeout) {
                                std::cout << "timeout waiting for " << evtName << std::endl;
				mWaiting = false;
				return false;
			}
			if (mEvtSignalled == evtName) {
				mWaiting = false;
				return true;
			}
		}
		mWaiting = false;
		return false;
	}
	
   int onServiceConnectionStatusChanged(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onServiceConnectionStatusChanged";
	   mServiceConnectionStatusEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceCreated(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceCreated";
	   mConferenceCreatedEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceEnded(ConferenceConnectorHandle conn, const ConferenceEndedEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceEnded";
	   mConferenceEndedEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceListUpdated(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceListUpdated";
	   mConferenceListUpdatedEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceParticipantListUpdated(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceParticipantListUpdated";
	   mConferenceParticipantListUpdatedEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) override {
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceSessionStatusChanged";
	   mConferenceSessionStatusChangedEvent = args;
	   mWaitCond.notify_one();
	   return 0;
   }
   int onConferenceSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args) override {
	   /*
	   std::unique_lock<std::mutex> lck(mWaitMtx);
	   mEvtSignalled = "ConferenceConnectorHandler::onConferenceSessionMediaStatusChanged";
	   mConferenceSessionMediaStatusChangedEvent = args;
	   mWaitCond.notify_one();
	   */
	   return 0;
   }
   ServiceConnectionStatusEvent mServiceConnectionStatusEvent;
   ConferenceCreatedEvent mConferenceCreatedEvent;
   ConferenceEndedEvent mConferenceEndedEvent;
   ConferenceListUpdatedEvent mConferenceListUpdatedEvent;
   ConferenceParticipantListUpdatedEvent mConferenceParticipantListUpdatedEvent;
   ConferenceSessionStatusChangedEvent mConferenceSessionStatusChangedEvent;
   ConferenceSessionMediaStatusChangedEvent mConferenceSessionMediaStatusChangedEvent;

private:
   std::string mEvtSignalled;
   std::mutex mWaitMtx;
   std::condition_variable mWaitCond;
   std::atomic_bool mTestDone;
   std::atomic_bool mWaiting;
   std::thread* mProcessThread;
};

void SdkManager::appInit()
{
#ifndef WIN32
   // OBELISK-4439: prevent SIGPIPE from killing the whole process by ignoring SIGPIPE;
   // write errors will be handled locally instead
   signal(SIGPIPE, SIG_IGN);

   // systemd will send us SIGTERM when it wants us to shutdown
   struct sigaction sthandler;
   signal_callback_wrapper = std::bind(&SdkManager::sigtermHandler, this, std::placeholders::_1);
   sthandler.sa_handler = signal_callback_function;
   sigemptyset(&sthandler.sa_mask);
   sthandler.sa_flags = 0;
   sigaction(SIGTERM, &sthandler, NULL);
#endif

   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_conf_load_test.config");   

   int numTestEndpoints = 10;
   fileConfig.getConfigValue("num_test_endpoints", numTestEndpoints);

   resip::Data targetJoinUrl;
   fileConfig.getConfigValue("join_url", targetJoinUrl);

   resip::Data targetChannel;
   fileConfig.getConfigValue("channel", targetChannel);
   
   bool immediateJoin = false;
   fileConfig.getConfigValue("immediate_join", immediateJoin);
   
   resip::Data participantType = "audio";
   fileConfig.getConfigValue("participant_type", participantType);

   CPCAPI2::Media::MediaTransportsReactorFactory* reactorFac = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   reactorFac->initialize();

   CPCAPI2::Phone* masterPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "/tmp";
   licenseInfo.licenseAor = "";

   masterPhone->initialize(licenseInfo, (CPCAPI2::PhoneErrorHandler*)NULL, false);
   resip::MultiReactor* externalLogger = &dynamic_cast<PhoneInterface*>(masterPhone)->getSdkLoggerThread();
   CPCAPI2::Media::MediaManager* masterMediaManager = dynamic_cast<CPCAPI2::Media::MediaManager*>(CPCAPI2::Media::MediaManagerInternal::getInterface(masterPhone, reactorFac));
   CPCAPI2::Media::MediaStackSettings mediaStackSettings;
   mediaStackSettings.audioLayer = CPCAPI2::Media::AudioLayers_Dummy;
   masterMediaManager->initializeMediaStack(mediaStackSettings);
   
   std::vector<std::future<void> > bobEvents;

   std::cout << "Starting connection phase with " << numTestEndpoints << " endpoint(s)" << std::endl;

   for (int i = 0; i < numTestEndpoints; i++) {
	bobEvents.emplace_back(std::async(std::launch::async, [&, i]() {
	 std::this_thread::sleep_for(std::chrono::seconds(i));
         std::cout << "thread starting ..." << std::endl;
	 // Bob is a client SDK (screenshare participant)
	 CPCAPI2::Phone* testPhone = CPCAPI2::PhoneInterface::create(dynamic_cast<CPCAPI2::PhoneInternal*>(masterPhone), externalLogger);
	 dynamic_cast<CPCAPI2::PhoneInternal*>(testPhone)->initialize(CPCAPI2::Licensing::LicensingClientManager::getInterface(masterPhone), (CPCAPI2::PhoneHandler*)NULL, false);
         std::cout << "phone initialized ..." << std::endl;
	 testPhone->setLogLevel(CPCAPI2::LogLevel_None);
         //testPhone->setLoggingEnabled("cpcconfloadtest", true);

	 dynamic_cast<PhoneInterface*>(testPhone)->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(masterMediaManager));

	 CPCAPI2::ConferenceConnector::ConferenceConnectorManager* confConnMgr = CPCAPI2::ConferenceConnector::ConferenceConnectorManager::getInterface(testPhone);
	 MyConfConnHandler* myConfConnHandler = new MyConfConnHandler(confConnMgr);
	 ConferenceConnectorHandle bobConfConn = confConnMgr->createConferenceConnector();
	 confConnMgr->setHandler(bobConfConn, myConfConnHandler);
	 ConferenceConnectorSettings bobCloudSettings;
	 bobCloudSettings.authServerUrl = "https://auth.softphone.com";
	 bobCloudSettings.authServerApiKey = "-----BEGIN PUBLIC KEY-----\n"
		"MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n"
		"+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n"
		"-----END PUBLIC KEY-----";
	 bobCloudSettings.orchestrationServerUrl = ""; // not needed, we use the joinUrl instead
	 bobCloudSettings.joinUrl = targetJoinUrl.c_str();
	 bobCloudSettings.regionCode = "LOCAL";
	 {
		resip::Data usernameStr;
		{
		   resip::DataStream ds(usernameStr);
		   ds << "load-test-user-" << resip::Random::getCryptoRandomBase64(2);
		}
		bobCloudSettings.username = usernameStr.c_str();
	 }
	 bobCloudSettings.password = "1234";
	 confConnMgr->setConnectionSettings(bobConfConn, bobCloudSettings);
	 confConnMgr->connectToConferenceService(bobConfConn);
         std::cout << "Connecting to " << targetJoinUrl.c_str() << std::endl;

         for (int iconn = 0; iconn < 10; iconn++)
	 {
		ConferenceConnectorHandle conn;
		ServiceConnectionStatusEvent args;
		if (!myConfConnHandler->waitFor("ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000)) {
                    std::cout << "Connection failed; last event=" << myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus << std::endl;
                    return;
		}
                std::cout << "onServiceConnectionStatusChanged: " << myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus << std::endl;
                if (myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus == ConferenceConnector::ServiceConnectionStatus_Connected)
                {
                    std::cout << "Connection successful" << std::endl;
                    break;
                }
                else if (myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus == ConferenceConnector::ServiceConnectionStatus_AuthFailure ||
                         myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus == ConferenceConnector::ServiceConnectionStatus_ConnFailure)
                {
                    std::cout << "Connection failed; status=" << myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus << std::endl;
                    break;
                }
		//ASSERT_TRUE(cpcExpectEvent(confConnMgrEvents, "ConferenceConnectorHandler::onServiceConnectionStatusChanged", 25000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		//ASSERT_EQ(ConferenceConnector::ServiceConnectionStatus_Connecting, args.connectionStatus);
	 }

         if (myConfConnHandler->mServiceConnectionStatusEvent.connectionStatus != ConferenceConnector::ServiceConnectionStatus_Connected)
         {
                std::cout << "Connection failed; aborting run ..." << std::endl;
                return;
         }

	 confConnMgr->queryConferenceList(bobConfConn);
	 CloudConferenceHandle bobScreenShare = (CloudConferenceHandle)(-1);

         std::cout << "Looking for target conference: " << targetChannel.c_str() << std::endl;

	 {
		ConferenceConnectorHandle conn;
		ConferenceListUpdatedEvent args;
		if (!myConfConnHandler->waitFor("ConferenceConnectorHandler::onConferenceListUpdated", 15000)) {
		}
		args = myConfConnHandler->mConferenceListUpdatedEvent;
		//ASSERT_TRUE(cpcExpectEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceListUpdated", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		for (const CloudConferenceInfo& cci : args.conferenceList)
		{
		   if (cci.displayName == cpc::string(targetChannel.c_str()))
		   {
                          std::cout << "Found conference" << std::endl;
			  bobScreenShare = cci.conference;
			  break;
		   }
		}
		//ASSERT_NE(bobScreenShare, (CloudConferenceHandle)(-1));
	 }
	 
	 confConnMgr->queryParticipantList(bobScreenShare);

	for (;;)
        {

	 bool canJoin = false;

	 if (!immediateJoin)
	 {
		 std::cout << "Waiting for someone to take the floor..." << std::endl;

		 while (!canJoin)
		 {
			ConferenceConnectorHandle conn;
			ConferenceParticipantListUpdatedEvent args;
			//if (cpcWaitForEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 1000, CPCAPI2::test::AlwaysTruePred(), conn, args))
			if (myConfConnHandler->waitFor("ConferenceConnectorHandler::onConferenceParticipantListUpdated", 30000))
			{
			   args = myConfConnHandler->mConferenceParticipantListUpdatedEvent;
			   if (args.conference == bobScreenShare)
			   {
				  //safeCout("onConferenceParticipantListUpdated for our conference (wait to join)");
				  if (args.participantList.size() >= 1)
				  {
					 bool someoneHasTheFloor = false;
					 for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
					 {
						if (ccpi.hasFloor)
						{
						   someoneHasTheFloor = true;
						}
					 }
					 if (someoneHasTheFloor)
					 {
						canJoin = true;
					 }
				  }
			   }
			}
		 }

         std::cout << "Someone has the floor! Creating session in " << (i * 20) << " milliseconds..." << std::endl;
	 }

	 std::this_thread::sleep_for(std::chrono::milliseconds(i * 20));

	 CloudConferenceSessionHandle bobSession = confConnMgr->createConferenceSession(bobScreenShare);

	 CloudConferenceSessionSettings bobSessionSettings;
	 bobSessionSettings.role = CloudConferenceRole_Participant;
	 bobSessionSettings.address = bobCloudSettings.username;
	 bobSessionSettings.displayName = bobCloudSettings.username;
	 confConnMgr->setSessionSettings(bobSession, bobSessionSettings);

	 CloudConferenceSessionMediaSettings bobSessionMedia;
	 bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_None;
	 bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_None;
	 if (resip::isEqualNoCase(participantType, "audio") || resip::isEqualNoCase(participantType, "both"))
	 {
		bobSessionMedia.audioDirection = ConferenceConnector::MediaDirection_RecvOnly;
	 }
	 if (resip::isEqualNoCase(participantType, "video") || resip::isEqualNoCase(participantType, "both"))
	 {
	    bobSessionMedia.videoDirection = ConferenceConnector::MediaDirection_RecvOnly;
	 }
	 confConnMgr->setSessionMediaSettings(bobSession, bobSessionMedia);

	 confConnMgr->startSession(bobSession);


                for (int isessconn=0; isessconn < 10; isessconn++)
		{
		   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
		   ConferenceConnectorHandle conn;
		   ConferenceSessionStatusChangedEvent args;
		   myConfConnHandler->waitFor("ConferenceConnectorHandler::onConferenceSessionStatusChanged", 15000);
                   args = myConfConnHandler->mConferenceSessionStatusChangedEvent;
                   std::cout << "onConferenceSessionStatusChanged: " << args.sessionStatus << std::endl;
                   if (args.sessionStatus == CPCAPI2::ConferenceConnector::SessionStatus_Connected)
                   {
                       break;
                   }
		   //EXPECT_TRUE(cpcExpectEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 5000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		   //EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_Connecting);
		}
                if (myConfConnHandler->mConferenceSessionStatusChangedEvent.sessionStatus != CPCAPI2::ConferenceConnector::SessionStatus_Connected)
                {
                   std::cout << "Session failed to connect; aborting..." << std::endl;
                   return;
                }

                std::cout << "Waiting for participant with floor to leave..." << std::endl;

		bool canLeave = false;
		while (!canLeave)
		{
		   ConferenceConnectorHandle conn;
		   ConferenceParticipantListUpdatedEvent args;
		   //if (cpcWaitForEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 1000, CPCAPI2::test::AlwaysTruePred(), conn, args))
		   if (myConfConnHandler->waitFor("ConferenceConnectorHandler::onConferenceParticipantListUpdated", 30000))
		   {
			  args = myConfConnHandler->mConferenceParticipantListUpdatedEvent;
			  if (args.conference == bobScreenShare)
			  {
				 //safeCout("onConferenceParticipantListUpdated for our conference (wait to leave)");
				 bool someoneHasTheFloor = false;
				 for (const CloudConferenceParticipantInfo& ccpi : args.participantList)
				 {
					if (ccpi.hasFloor)
					{
					   someoneHasTheFloor = true;
					}
				 }
				 if (!someoneHasTheFloor)
				 {
					canLeave = true;
				 }
			  }
		   }
		}

                std::cout << "No one has the floor; leaving..." << std::endl;

		confConnMgr->endSession(bobSession);

         }

		std::this_thread::sleep_for(std::chrono::milliseconds(5000));
		//std::this_thread::sleep_for(std::chrono::milliseconds(5000));
		//std::this_thread::sleep_for(std::chrono::milliseconds(5000));

		//{
		//   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
		//   ConferenceConnectorHandle conn;
		//   ConferenceParticipantListUpdatedEvent args;
		//   EXPECT_TRUE(cpcExpectEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceParticipantListUpdated", 15000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		//}

		//std::this_thread::sleep_for(std::chrono::seconds(180));
		//confConnMgr->endSession(bobSession);

		//{
		//   // virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
		//   ConferenceConnectorHandle conn;
		//   ConferenceSessionStatusChangedEvent args;
		//   EXPECT_TRUE(cpcExpectEvent(confConnMgrEvents, "ConferenceConnectorHandler::onConferenceSessionStatusChanged", 180000, CPCAPI2::test::AlwaysTruePred(), conn, args));
		//   EXPECT_EQ(args.sessionStatus, CPCAPI2::ConferenceConnector::SessionStatus_NotConnected);
		//}

		//safeCout("BOB HANGS UP");
		//bob->video->setIncomingVideoRenderTarget(NULL);




	delete myConfConnHandler;


  }));
}

}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}
}
}
