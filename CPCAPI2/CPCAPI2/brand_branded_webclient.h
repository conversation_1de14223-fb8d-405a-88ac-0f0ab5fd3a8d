#define CPCAPI2_BRAND_ACCOUNT_MODULE                           0
#define CPCAPI2_BRAND_ACCOUNT_STATE_MODULE                     0
#define CPCAPI2_BRAND_IM_MODULE                                0
#define CPCAPI2_BRAND_CALL_MODULE                              0
#define CPCAPI2_BRAND_PTT_MODULE                               0
#define CPCAPI2_BRAND_MEDIA_MODULE                             1
#define CPCAPI2_BRAND_VIDEO_MODULE                             1
#define CPCAPI2_BRAND_AUDIO_EXT_MODULE                         0
#define CPCAPI2_BRAND_VIDEO_EXT_MODULE                         1
#define CPCAPI2_BRAND_SIP_EVENT_MODULE                         0
#define CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE                 0
#define CPCAPI2_BRAND_SIP_MWI_MODULE                           0
#define CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE              0
#define CPCAPI2_BRAND_SIP_CHAT_MODULE                          0
#define CPCAPI2_BRAND_CALL_STATE_MODULE                        0
#define CPCAPI2_BRAND_RECORDING_MODULE                         0
#define CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE              0
#define CPCAPI2_BRAND_STRETTO_UEM_MODULE                       0
#define CPCAPI2_BRAND_AUDIO_EXT_MODULE                         0
#define CPCAPI2_BRAND_AUTH_PROVIDER_MOCK_MODULE                0
#define CPCAPI2_BRAND_AUTH_PROVIDER_MODULE                     0

#define CPCAPI2_BRAND_RESOURCE_LIST_MODULE                     0
#define CPCAPI2_BRAND_XCAP_MODULE                              0
#define CPCAPI2_BRAND_PEER_CONNECTION_MODULE                   1
#define CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE          0
#define CPCAPI2_BRAND_RCSPROVISIONING_MODULE                   0
#define CPCAPI2_BRAND_WEB_CALL_MODULE                          0
#define CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE                      0
#define CPCAPI2_BRAND_XMPP_ACCOUNT_STATE_MODULE                0
#define CPCAPI2_BRAND_XMPP_ROSTER_MODULE                       0
#define CPCAPI2_BRAND_XMPP_ROSTER_STATE_MODULE                 0
#define CPCAPI2_BRAND_XMPP_CHAT_MODULE                         0
#define CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE                0
#define CPCAPI2_BRAND_XMPP_VCARD_MODULE                        0
#define CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE              0
#define CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE                   0
#define CPCAPI2_BRAND_NETWORK_CHANGE_MODULE                    1
#define CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE                  0
#define CPCAPI2_BRAND_SIP_REG_EVENT_MODULE                     0
#define CPCAPI2_BRAND_TERADICI_AUDIO_MODULE                    0
#define CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE                0
#define CPCAPI2_BRAND_TERADICI_LOGGER_MODULE                   0
#define CPCAPI2_BRAND_WATCHER_INFO_MODULE                      0
#define CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE               0
#define CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE        0
#define CPCAPI2_BRAND_CONFERENCE_MODULE                        0
#define CPCAPI2_BRAND_LICENSING_MODULE                         1
#define CPCAPI2_BRAND_HTTP_MODULE                              0
#define CPCAPI2_BRAND_GENBAND_SOPI_MODULE                      0
#define CPCAPI2_BRAND_ACME_TSCF_MODULE                         0
#define CPCAPI2_BRAND_REMOTE_SYNC_MODULE                       0
#define CPCAPI2_BRAND_VCCS_MODULE                              0
#define CPCAPI2_BRAND_ANALYTICS_MODULE                         0
#define CPCAPI2_BRAND_SNS_MODULE                               0
#define CPCAPI2_BRAND_BROADSOFT_XSI_MODULE                     0
#define CPCAPI2_BRAND_WATCHDOG_MODULE                          0
#define CPCAPI2_BRAND_REMOTE_CONTROL_MODULE                    0
#define CPCAPI2_BRAND_JSON_API_SERVER_MODULE                   1
#define CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE                   0
#define CPCAPI2_BRAND_PUSH_NOTIFICATION_MODULE                 0
#define CPCAPI2_BRAND_XMPP_AGENT_MODULE                        0
#define CPCAPI2_BRAND_AUTH_SERVER_MODULE                       0
#define CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE             0
#define CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE              1
#define CPCAPI2_BRAND_JSON_API_CLIENT_MODULE                   1
#define CPCAPI2_BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE      1
#define CPCAPI2_BRAND_RAW_VIDEO_WEBSOCKET_SERVER               1
#define CPCAPI2_BRAND_VIDEO_STREAMING_MODULE                   1
#define CPCAPI2_BRAND_SIGNATURE_MODULE                         1
#define CPCAPI2_BRAND_REQUIRED_APP_SIGNATURE                   "e12d507077853b9778414aadce56ccf841b22626"

#define CPCAPI2_BRAND_SDK_PRODUCT_NAME_SHORT                   1
#define CPCAPI2_BRAND_SDK_PRODUCT_NAME_LONG                    1
#define CPCAPI2_BRAND_SDK_PRODUCT_VERSION                      1
#define CPCAPI2_BRAND_SDK_PRODUCT_ID                           1
#define CPCAPI2_BRAND_SDK_FILE_OUTPUT                          1
#define CPCAPI2_BRAND_SDK_PACKAGE_SHORT_NAME                   1
#define CPCAPI2_BRAND_SDK_OFFICIAL_RELEASE_ASSEMBLY_VERSION    1
#define CPCAPI2_BRAND_SDK_LICENSING                            0
#define CPCAPI2_BRAND_USE_LICENSE_SERVER                       0
#define CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS             0
#define CPCAPI2_BRAND_LICENSE_EXPIRY_YEAR                      0
#define CPCAPI2_BRAND_LICENSE_EXPIRY_MONTH                     0
#define CPCAPI2_BRAND_LICENSE_EXPIRY_DAY                       0
#define CPCAPI2_BRAND_LICENSE_BUILTIN_KEY                      ""
#define CPCAPI2_BRAND_LICENSE_FORCE_REMOTE_CHECK               0
#define CPCAPI2_BRAND_CODEC_H264_LICENSE_COUNT                 2
#define CPCAPI2_BRAND_CODEC_OPENH264                           1
#define CPCAPI2_BRAND_CODEC_SPEEX                              0
#define CPCAPI2_BRAND_CODEC_ILBC                               0
#define CPCAPI2_BRAND_CODEC_AMRWB                              0
#define CPCAPI2_BRAND_CODEC_G729                               0
#define CPCAPI2_BRAND_CODEC_OPUS                               1
#define CPCAPI2_BRAND_CODEC_SILK                               0
#define CPCAPI2_BRAND_CODEC_GSM                                0
#define CPCAPI2_BRAND_CODEC_H263                               0
#define CPCAPI2_BRAND_CODEC_VP8                                1
#define CPCAPI2_BRAND_CODEC_VP9                                0
#define CPCAPI2_BRAND_VQMONEP_MOBILE                           0
#define CPCAPI2_BRAND_VIDEO_WEBSOCKET_SERVER                   1
#define CPCAPI2_BRAND_MOZJPEG                                  0
#define CPCAPI2_BRAND_GUID                                     "{781BD57B-F346-494B-9C6D-DAC52D8D9E2C}"
#define CPCAPI2_BRAND_ENCRYPTED_SETTINGS_KEY                   "123456789"
#define CPCAPI2_BRAND_LICENSE_EMBEDDED_CERTIFICATE             "Md3iuAIWog7Rz2uTNlhnvY/501D+ku1O8G8ecXGZxZDHbs2SwZVoRSGcUMQZWPpDgBV422uRa7AvYCOaW9vtvuY4BF0GDXOfZ+fu9CiKQIE3Kh/uZAY0gOTJ2eQ+uNk+WLMVvl6SejLD/weChvh5NU57+chWhjyC79oux3TKaErmS/24ka1iKNXa3KkNI4A6v00/9Hx+OeLx1BZI72k87RM/eg80t1nLOiDc698oE4PIpkCtEgbhk9RYm2SSAeNE7nrBBd1TwrqdcRHn1/mlsCoLJ1YRtMrBLYBIB5dENEeyal/QKn+Vp8qNaJ6FdkqeF0rBF3qhhcGK7MI0wEr2wcfOqtlWNzYi6jABuS/lACg6Sc84OQzeT43dAHQuXiyP0XYVNoBbHk2p1HBRXhJQg9SpCHCiGQHlcgmOmKT/XEQCsAN1I5/CviBHqiFjcWgtUtlYwc3ee44J76MPJYKIf7b8pP4MmRpxtKS/BglfhHjdUijzwi2fCBdLhnSbPRPDeL/PcD0ZuFYXnaFGTxNOfhpGiynZA5/nXSZuP5Uv6+ui/hfhfeBv/1raoWUz7Uox2XKzPe6jFIu90swaHdcwLZfvJk7lPzdfw7S/xVv7EBbmWr1N9EgBz+aOQ/nHOBFdIRn4Kym/V6eCFZJhryl/DV7SNmO/iNkdR0ZUzNqOkhqiNa6Dmb7vs9MwQ7rhV3KMRgOl8eNJqMLvpjhU0j77d5hczzcXXAaE5LGKU7z97F1vgWkRL2r5KCCL5Hmy6CM6mFPIMgv+wTuvfK2MuwgpIavuH7PpBP5U6oQwZb6XEOm6I91xXAOyU+AsjmGwjb2zjXkp6a4TFG7fkCsxlTlFBcR7X8Mdo0Y41Z3ryOd/2QNsSO0jbEKNK0FM1znV/S6ezeDr6vWT4oYL+qQkWoPEHdCqDkegJY3CgOPDRAok8oTOygNeCsRsSSf6NdwrZnG0LL7zLz5TxBj67lmeBzcPm6ZmkD5xnddnDtfdZceHp6+Pf5H5AVvx7f5xT1KMKoe7iJSwnYI3kwQgQYpxaPs4iTkBmEjSKdDseeNWY/oblgbBqTiCFgqRxMRwL7Ne1DV2pKJ3Bn5ht2vAZB43u2eII4teHbq+PgG+0Wl+9hdYRT+NK5Eq246gkB+ZD2fHZF2AdpE3148BZtrl4RYNu4s00EbIWiD79VngnwmpTkv5aTaklxyxeojNkMAYK3b0IZLO94SC1tI7Hzm8zxJevnopouv94rNBCgYZwdfeVQdRiBW9uKtNoymQYSfnDghEi/Im+9JUDsH2Z2azwF4AvHQG9kihEQmZGGj/e/VEZwnf2JBT3qA5uinCJfRixnKJdUP7g6dJlVS9p4gJmTCFj4uwXiVGreP7pPFZwI2PU5sN28dvPdTBD+BG707aD9QSTDl4Q8RMButRFXH3htkp2mANm3D/QBMbHPTJs4j05aDVLDmsi0q2aeolmES91ogEz/SxYqqzE377NNkxWM/QSb0MRtR7agKw32Ta0LbYWGj7qXSwzi5iQD56ec/bCPsONERkf0iUp5upmZ+/v9RNfje9j4ol/Tw5h3gRTKMMTTGYiso="
#define CPCAPI2_BRAND_ANALYTICS_EMBEDDED_CERTIFICATE           "Md3iuAIWog7Rz2uTNlhnvaFMJhQJieCe/ddE78iJokFVe4MB8bS2rDMCeamjfKFWr9YgPR7Gg0ftMDjO1QQPIfP2yKuj1XoSB1KF2RE+mUHdR3naz1zcgyeYzF+X8o7so+UoUQ+4RcTKRQyMZSCK2i9dlncPZqMQaoe1TLl3AUdvk/r/6BKazruHXR9/cIAvml+3ufJwUCNO5YZ9KVSv2lEuzJnS9F7hOe1EYrnn5kphyWKdsJBaSz2ZWRvkUP8QDxmiRJ++vbCfMgz0xlLyM1djhLT5Fss/Xc2usTbQrrlkzE7orDtwVrDHpP7N+Ls5Ak6PdpTaswu4pIpKC2JYM9iDX4Z3Wnxigq3tGx/Z1zHwpV+2BO3I2JX3zP3BVm3HMG0Zw+XGIK7YMNGDn0wXAJwoxWowWv1l0tkjHJutUr/ibPmnC0lqivnM05rtqW0VRXYYBJilH/RM7drfWBZxEReWjfcd1VqDvGHKA1b60CAk3P6CXC8SyGaKL4V9gw/yBZjqYXPTp1sckSlVGgOVcg1UEm+XQDW3jngzyD37t5YZXlieVEcSQCACxLwKk4bPhB2fwL477QHn2vAYhjPI9UGetvQzMrg6Ewm11Ph5xZtsnJQEGU/7FU+hDiuXnH6TyKH3Gs0ftJVpj+3ZuNlyN6b1yUn0rLaDcxsLXLUEt2GeluyrhovvwKGbgewRw4sCB+gNP9x8LQ69w3pwxJpNwSxURxqILTq2oiSN9kTZ/+fN9WpWHzGAgGjgUZIxM8Mv9x7smURUn2qqIT/Cq6+BNoSIOnmoCeAIOwAYG2gwDroOpjwtwUERUMMK1YzCztwym9e5GITx1oZqz1/ky1JUqYtlk9lScDR+nj5d8EZ60q2OIzH8nguqJgZ4Q6J7amLsAYi1Ae6TH0exV5cX8VoF49Rjei8orv4fbammr1K2hgL3JK+zJg0KIj7U5xxHqKwr6dyajaQNGMPHGimJtoGAc5cD2RyTHBAzzQsvU0seM/oh5TF0xHetIK6fOKqAih8E3XvXrtfY2rLaIZe9pLomR1/bekhPPfQSM2AJXr6VoNv2gP/EDlfULK5Koo16HbdkF+e9nORqlrSQjoAhJpQTr/Iey07CXum2cWjuNBm/sFRmm8jqy7zcaOxsoc+V8c40BccphUbkH7VlwA23ho7wUZU117zDojm/0Q02Zj6G3469nRNT8MvQ3GTefz+G22ilG1CGVHIVmKRUokg7H9jzdDa+oPFyUOiR9I0mFnlg9XJ6KQWgozccH5wnCs0awxMUdDo7VHI6eS9LDgcmLp5YGQEPA2O4fJl6K6L70u2whwn3BYypuOY2+uI3SUqssbHhLLcl5Eh6OwHVmRmsagmCfw=="
#define CPCAPI2_BRAND_COMPANY_SHORT_NAME                       "CounterPath"
#define CPCAPI2_BRAND_PRODUCT_NAME                             "SDK"
#define CPCAPI2_BRAND_FULL_NAME                                "brands/CUSTOMERS/counterpath/SDK/Mac"
#define CPCAPI2_BRAND_LICENSE_URL                              "https://secure.counterpath.com/servicev2/validatelicense"
#define CPCAPI2_BRAND_LICENSE_URL_SECONDARY                    "0"
#define CPCAPI2_BRAND_BUILD_STAMP                              "0"
#define CPCAPI2_VER_MAJOR                                      0
#define CPCAPI2_VER_MINOR                                      0
#define CPCAPI2_BUILDSTAMP_SPLIT_1                             0
#define CPCAPI2_BUILDSTAMP_SPLIT_2                             0
#define CPCAPI2_BRAND_BUILD_32BIT_SDK                          0
#define CPCAPI2_BRAND_PROTOBUF_LOGGING                         1
#define CPCAPI2_BRAND_PROVISIONING_URL                         "https://ccs3.cloudprovisioning.com/login"
#define CPCAPI2_BRAND_BIEVENTS_MODULE                          0
#define CPCAPI2_BRAND_MESSAGESTORE_MODULE                      0
#define CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE                    0
#define CPCAPI2_BRAND_PUSH_NOTIFICATION_MODULE                 0
#define CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE                 0
