#pragma once

#ifndef __CPCAPI2_PRECOMPILED_H__
#define __CPCAPI2_PRECOMPILED_H__

// It's a good idea to include any STL files here to improve build times
#ifdef __cplusplus
#include <cassert>
#include <cstring>
#include <deque>
#include <exception>
#include <iosfwd>
#include <iostream>
#include <list>
#include <map>
#include <memory>
#include <set>
#include <stack>
#include <string>
#include <utility>
#include <vector>
#include <cstdlib>

// C++11 includes
#include <functional>
#include <thread>
#endif

// Also, if the platform is windows, there's a number of system includes
// that should be done here
#ifdef WIN32
#include <winsock2.h>
#include <windows.h>
#include <Ws2tcpip.h>
#endif

// Platform independent system includes
#include <time.h>

#ifdef USE_SSL
#ifdef WIN32
#include <wincrypt.h>
#endif // WIN32
#include <openssl/ssl.h>
#endif// USE_SSL

// libxml includes
#include <libxml/tree.h>
#include <libxml/xmlreader.h>
#include <libxml/parser.h>

// reSIP includes
#include <rutil/compat.hxx>
#include <rutil/SharedPtr.hxx>
#include <rutil/Data.hxx>
#include <rutil/Socket.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/MPMCQueue.hxx>
#include <rutil/Reactor.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/Logger.hxx>

#include <resip/stack/HeaderTypes.hxx>
#include <resip/stack/Headers.hxx>

#include <resip/dum/Handle.hxx>

// CPCAPI2 includes
#include <cpcapi2defs.h>
#include <cpcapi2utils.h>

#include <boost/asio.hpp>

#endif /* __CPCAPI2_PRECOMPILED_H__ */
