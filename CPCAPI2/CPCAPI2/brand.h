#if __APPLE__
#include "TargetConditionals.h"
#endif

// modules
#define CPCAPI2_BRAND_ACCOUNT_MODULE                           %BRAND_ACCOUNT_MODULE%
#define CPCAPI2_BRAND_ACCOUNT_STATE_MODULE                     %BRAND_ACCOUNT_STATE_MODULE%
#define CPCAPI2_BRAND_IM_MODULE                                %BRAND_IM_MODULE%
#define CPCAPI2_BRAND_CALL_MODULE                              %BRAND_CALL_MODULE%
#define CPCAPI2_BRAND_PTT_MODULE                               %BRAND_PTT_MODULE%
#define CPCAPI2_BRAND_MEDIA_MODULE                             %BRAND_MEDIA_MODULE%
#define CPCAPI2_BRAND_AUDIO_EXT_MODULE                         %BRAND_AUDIO_EXT_MODULE%
#define CPCAPI2_BRAND_VIDEO_MODULE                             %BRAND_VIDEO_MODULE%
#define CPCAPI2_BRAND_VIDEO_EXT_MODULE                         %BRAND_VIDEO_EXT_MODULE%
#define CPCAPI2_BRAND_SIP_EVENT_MODULE                         %BRAND_SIP_EVENT_MODULE%
#define CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE                 %BRAND_SIP_FILE_TRANSFER_MODULE%
#define CPCAPI2_BRAND_SIP_MWI_MODULE                           %BRAND_SIP_MWI_MODULE%
#define CPCAPI2_BRAND_CAPABILITY_DISCOVERY_MODULE              %BRAND_CAPABILITY_DISCOVERY_MODULE%
#define CPCAPI2_BRAND_SIP_CHAT_MODULE                          %BRAND_SIP_CHAT_MODULE%
#define CPCAPI2_BRAND_CALL_STATE_MODULE                        %BRAND_CALL_STATE_MODULE%
#define CPCAPI2_BRAND_RECORDING_MODULE                         %BRAND_RECORDING_MODULE%
#define CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE              %BRAND_STRETTO_PROVISIONING_MODULE%
#define CPCAPI2_BRAND_STRETTO_UEM_MODULE                       %BRAND_STRETTO_UEM_MODULE%
#define CPCAPI2_BRAND_AUDIO_EXT_MODULE                         %BRAND_AUDIO_EXT_MODULE%
#define CPCAPI2_BRAND_AUTH_PROVIDER_MOCK_MODULE                %BRAND_AUTH_PROVIDER_MOCK_MODULE%
#define CPCPAI2_BRAND_AUTH_PROVIDER_MODULE                     %BRAND_AUTH_PROVIDER_MODULE%
#define CPCAPI2_BRAND_RESOURCE_LIST_MODULE                     %BRAND_RESOURCE_LIST_MODULE%
#define CPCAPI2_BRAND_XCAP_MODULE                              %BRAND_XCAP_MODULE%
#define CPCAPI2_BRAND_PEER_CONNECTION_MODULE                   %BRAND_PEER_CONNECTION_MODULE%
#define CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE          %BRAND_SIP_STANDALONE_MESSAGING_MODULE%
#define CPCAPI2_BRAND_RCSPROVISIONING_MODULE                   %BRAND_RCSPROVISIONING_MODULE%
#define CPCAPI2_BRAND_WEB_CALL_MODULE                          %BRAND_WEB_CALL_MODULE%
#define CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE                      %BRAND_XMPP_ACCOUNT_MODULE%
#define CPCAPI2_BRAND_XMPP_ACCOUNT_STATE_MODULE                %BRAND_XMPP_ACCOUNT_STATE_MODULE%
#define CPCAPI2_BRAND_XMPP_ROSTER_MODULE                       %BRAND_XMPP_ROSTER_MODULE%
#define CPCAPI2_BRAND_XMPP_ROSTER_STATE_MODULE                 %BRAND_XMPP_ROSTER_STATE_MODULE%
#define CPCAPI2_BRAND_XMPP_CHAT_MODULE                         %BRAND_XMPP_CHAT_MODULE%
#define CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE                %BRAND_XMPP_FILE_TRANSFER_MODULE%
#define CPCAPI2_BRAND_XMPP_VCARD_MODULE                        %BRAND_XMPP_VCARD_MODULE%
#define CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE              %BRAND_XMPP_MULTI_USER_CHAT_MODULE%
#define CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE                   %BRAND_XMPP_IM_COMMAND_MODULE%
#define CPCAPI2_BRAND_XMPP_PUSH_MODULE                         %BRAND_XMPP_PUSH_MODULE%
#define CPCAPI2_BRAND_NETWORK_CHANGE_MODULE                    %BRAND_NETWORK_CHANGE_MODULE%
#define CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE                  %BRAND_SIP_DIALOG_EVENT_MODULE%
#define CPCAPI2_BRAND_SIP_REG_EVENT_MODULE                     %BRAND_SIP_REG_EVENT_MODULE%
#define CPCAPI2_BRAND_TERADICI_AUDIO_MODULE                    %BRAND_TERADICI_AUDIO_MODULE%
#define CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE                %BRAND_TERADICI_CALLSTATE_MODULE%
#define CPCAPI2_BRAND_TERADICI_LOGGER_MODULE                   %BRAND_TERADICI_LOGGER_MODULE%
#define CPCAPI2_BRAND_WATCHER_INFO_MODULE                      %BRAND_WATCHER_INFO_MODULE%
#define CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE               %BRAND_SIP_BUSY_LAMP_FIELD_MODULE%
#define CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE        %BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE%
#define CPCAPI2_BRAND_CONFERENCE_MODULE                        %BRAND_CONFERENCE_MODULE%
#define CPCAPI2_BRAND_LICENSING_MODULE                         %BRAND_LICENSING_MODULE%
#define CPCAPI2_BRAND_HTTP_MODULE                              %BRAND_HTTP_MODULE%
#define CPCAPI2_BRAND_GENBAND_SOPI_MODULE                      %BRAND_GENBAND_SOPI_MODULE%
#define CPCAPI2_BRAND_ACME_TSCF_MODULE                         %BRAND_ACME_TSCF_MODULE%
#define CPCAPI2_BRAND_REMOTE_SYNC_MODULE                       %BRAND_REMOTE_SYNC_MODULE%
#define CPCAPI2_BRAND_VCCS_MODULE                              %BRAND_VCCS_MODULE%
#define CPCAPI2_BRAND_ANALYTICS_MODULE                         %BRAND_ANALYTICS_MODULE%
#define CPCAPI2_BRAND_SNS_MODULE                               %BRAND_SNS_MODULE%
#define CPCAPI2_BRAND_BROADSOFT_XSI_MODULE                     %BRAND_BROADSOFT_XSI_MODULE%
#define CPCAPI2_BRAND_WATCHDOG_MODULE                          %BRAND_WATCHDOG_MODULE%
#define CPCAPI2_BRAND_REMOTE_CONTROL_MODULE                    %BRAND_REMOTE_CONTROL_MODULE%
#define CPCAPI2_BRAND_JSON_API_CLIENT_MODULE                   %BRAND_JSON_API_CLIENT_MODULE%
#define CPCAPI2_BRAND_JSON_API_SERVER_MODULE                   %BRAND_JSON_API_SERVER_MODULE%
#define CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE                   %BRAND_JSON_RPC_SERVER_MODULE%
#define CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE             %BRAND_CLOUD_WATCHDOG_SERVER_MODULE%
#define CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE              %BRAND_CLOUD_SERVICE_CONFIG_MODULE%
#define CPCAPI2_BRAND_PUSH_NOTIFICATION_MODULE                 %BRAND_PUSH_NOTIFICATION_MODULE%
#define CPCAPI2_BRAND_XMPP_AGENT_MODULE                        %BRAND_XMPP_AGENT_MODULE%
#define CPCAPI2_BRAND_PCAP_MODULE                              %BRAND_PCAP_MODULE%
#define CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE                   %BRAND_CLOUD_CONNECTOR_MODULE%
#define CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE              %BRAND_CONFERENCE_CONNECTOR_MODULE%
#define CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE                 %BRAND_CONFERENCE_BRIDGE_MODULE%
#define CPCAPI2_BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE      %BRAND_CONFERENCE_BRIDGE_JSON_PROXY_MODULE%
#define CPCAPI2_BRAND_MESSAGESTORE_MODULE                      %BRAND_MESSAGESTORE_MODULE%
#define CPCAPI2_BRAND_OPEN_LDAP_MODULE                         %BRAND_OPEN_LDAP_MODULE%
#define CPCAPI2_BRAND_VIDEO_STREAMING_MODULE                   %BRAND_VIDEO_STREAMING_MODULE%
#define CPCAPI2_BRAND_RAW_VIDEO_WEBSOCKET_SERVER               %BRAND_RAW_VIDEO_WEBSOCKET_SERVER%
#define CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER                %BRAND_RAW_VIDEO_CALLBACK_SERVER%
#define CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE                  %BRAND_WEBSOCKET_SERVER_MODULE%
#define CPCAPI2_BRAND_WEBSOCKET_SERVER_CERTIFICATE_PEM         "%BRAND_WEBSOCKET_SERVER_CERTIFICATE_PEM%"
#define CPCAPI2_BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PEM         "%BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PEM%"
#define CPCAPI2_BRAND_WEBSOCKET_SERVER_URL                     "%BRAND_WEBSOCKET_SERVER_URL%"
#define CPCAPI2_BRAND_CALL_QUALITY_REPORT_MODULE               %BRAND_CALL_QUALITY_REPORT_MODULE%
#define CPCAPI2_BRAND_BIEVENTS_MODULE                          %BRAND_BIEVENTS_MODULE%
#define CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE                    %BRAND_STRETTO_TUNNEL_MODULE%
#define CPCAPI2_BRAND_PUSH_NOTIFICATION_MODULE                 %BRAND_PUSH_NOTIFICATION_MODULE%
#define CPCAPI2_BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE           %BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE%
#define CPCAPI2_BRAND_NODE_MODULE                              %BRAND_NODE_MODULE%
#define CPCAPI2_BRAND_SIGNATURE_MODULE                         %BRAND_SIGNATURE_MODULE%

// misc data
#define CPCAPI2_BRAND_SDK_PRODUCT_NAME_SHORT                   %BRAND_SDK_PRODUCT_NAME_SHORT%
#define CPCAPI2_BRAND_SDK_PRODUCT_NAME_LONG                    %BRAND_SDK_PRODUCT_NAME_LONG%
#define CPCAPI2_BRAND_SDK_PRODUCT_VERSION                      %BRAND_SDK_PRODUCT_VERSION%
#define CPCAPI2_BRAND_SDK_PRODUCT_ID                           %BRAND_SDK_PRODUCT_ID%
#define CPCAPI2_BRAND_SDK_FILE_OUTPUT                          %BRAND_SDK_FILE_OUTPUT%
#define CPCAPI2_BRAND_SDK_PACKAGE_SHORT_NAME                   %BRAND_SDK_PACKAGE_SHORT_NAME%
#define CPCAPI2_BRAND_SDK_OFFICIAL_RELEASE_ASSEMBLY_VERSION    %BRAND_SDK_OFFICIAL_RELEASE_ASSEMBLY_VERSION%
#define CPCAPI2_BRAND_GUID                                     "%BRAND_GUID%"
#define CPCAPI2_BRAND_ENCRYPTED_SETTINGS_KEY                   "%BRAND_ENCRYPTED_SETTINGS_KEY%"
#define CPCAPI2_BRAND_LICENSE_EMBEDDED_CERTIFICATE             "%BRAND_LICENSE_EMBEDDED_CERTIFICATE%"
#define CPCAPI2_BRAND_ANALYTICS_EMBEDDED_CERTIFICATE           "%BRAND_ANALYTICS_EMBEDDED_CERTIFICATE%"
#define CPCAPI2_BRAND_COMPANY_SHORT_NAME                       "%BRAND_COMPANY_SHORT_NAME%"
#define CPCAPI2_BRAND_PRODUCT_NAME                             "%BRAND_PRODUCT_NAME%"
#define CPCAPI2_BRAND_FULL_NAME                                "%BRAND_FULL_NAME%"
#define CPCAPI2_BRAND_LICENSE_URL                              "%BRAND_LICENSE_URL%"
#define CPCAPI2_BRAND_LICENSE_URL_SECONDARY                    "%BRAND_LICENSE_URL_SECONDARY%"
#define CPCAPI2_BRAND_BUILD_STAMP                              "%BRAND_BUILD_STAMP%"
#define CPCAPI2_BRAND_VERSION_NUMBER                           "%BRAND_VERSION_NUMBER%"
#define CPCAPI2_BRAND_BUILD_32BIT_SDK                          %BRAND_BUILD_32BIT_SDK%
#define CPCAPI2_BRAND_PROTOBUF_LOGGING                         %BRAND_PROTOBUF_LOGGING%
#define CPCAPI2_BRAND_PROVISIONING_URL                         "%BRAND_PROVISIONING_URL%"
#define CPCAPI2_BRAND_SIGNATURE_MODULE_CHECKS                  "%BRAND_SIGNATURE_MODULE_CHECKS%"
#define CPCAPI2_BRAND_SIGNATURE_MODULE_CERT_FINGERPRINT        "%BRAND_SIGNATURE_MODULE_CERT_FINGERPRINT%"
#define CPCAPI2_BRAND_BROADSOFT_XSI_MOBILE_ATTR_NAME           "%BRAND_BROADSOFT_XSI_MOBILE_ATTR_NAME%"

// licensing
#define CPCAPI2_BRAND_SDK_LICENSING                            %BRAND_SDK_LICENSING%
#define CPCAPI2_BRAND_USE_LICENSE_SERVER                       %BRAND_USE_LICENSE_SERVER%
#define CPCAPI2_BRAND_LICENSE_IGNORE_SERVER_ERRORS             %BRAND_LICENSE_IGNORE_SERVER_ERRORS%
#define CPCAPI2_BRAND_LICENSE_EXPIRY_YEAR                      %BRAND_LICENSE_EXPIRY_YEAR%
#define CPCAPI2_BRAND_LICENSE_EXPIRY_MONTH                     %BRAND_LICENSE_EXPIRY_MONTH%
#define CPCAPI2_BRAND_LICENSE_EXPIRY_DAY                       %BRAND_LICENSE_EXPIRY_DAY%
#define CPCAPI2_BRAND_LICENSE_EXPIRY_PEROID                    %BRAND_LICENSE_EXPIRY_PEROID%
#define CPCAPI2_BRAND_LICENSE_BUILTIN_KEY                      "%BRAND_LICENSE_BUILTIN_KEY%"
#define CPCAPI2_BRAND_LICENSE_FORCE_REMOTE_CHECK               %BRAND_LICENSE_FORCE_REMOTE_CHECK%
#define CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK                      "%BRAND_LICENSE_DOMAIN_LOCK%"

// codecs
#define CPCAPI2_BRAND_CODEC_OPUS                               %BRAND_CODEC_OPUS%

#if defined(ANDROID)
	// Android 64-bit simulator
	#if defined(__x86_64__)
		// G729 not supported
		// VQmon not supported
	#else
		#define CPCAPI2_BRAND_CODEC_G729                               %BRAND_CODEC_G729%
		#define CPCAPI2_BRAND_VQMONEP_MOBILE                           %BRAND_VQMONEP_MOBILE%
		// AMR-WB no longer supported on Android per OBELISK-6040
	#endif
#else

   #if !(TARGET_OS_OSX || _WIN64)
      // AMR-WB no longer supported on macOS per OBELISK-6112, was never supported
	  // on 64-bit windows per OBELISK-4586
      #define CPCAPI2_BRAND_CODEC_AMRWB                              %BRAND_CODEC_AMRWB%
   #endif

   #define CPCAPI2_BRAND_CODEC_G729                               %BRAND_CODEC_G729%
   #define CPCAPI2_BRAND_VQMONEP_MOBILE                           %BRAND_VQMONEP_MOBILE%
   #define CPCAPI2_BRAND_CODEC_GSM                                %BRAND_CODEC_GSM%
   #define CPCAPI2_BRAND_CODEC_H263                               %BRAND_CODEC_H263%
#endif

#define CPCAPI2_BRAND_CODEC_SILK                               %BRAND_CODEC_SILK%
#define CPCAPI2_BRAND_CODEC_H263                               %BRAND_CODEC_H263%
#define CPCAPI2_BRAND_CODEC_VP8                                %BRAND_CODEC_VP8%
#define CPCAPI2_BRAND_CODEC_VP9                                %BRAND_CODEC_VP9%
#define CPCAPI2_BRAND_CODEC_H264_LICENSE_COUNT                 %BRAND_CODEC_H264_LICENSE_COUNT%
#define CPCAPI2_BRAND_CODEC_OPENH264                           %BRAND_CODEC_OPENH264%
#define CPCAPI2_BRAND_CODEC_SPEEX                              %BRAND_CODEC_SPEEX%
#define CPCAPI2_BRAND_CODEC_ILBC                               %BRAND_CODEC_ILBC%
#define CPCAPI2_BRAND_CODEC_H265_LICENSE_COUNT                 %BRAND_CODEC_H265_LICENSE_COUNT%
#define CPCAPI2_BRAND_CODEC_H265                           	   %BRAND_CODEC_H265%

#define CPCAPI2_BRAND_AUTH_SERVER_MODULE                       %BRAND_AUTH_SERVER_MODULE%
#define CPCAPI2_BRAND_JSON_API_SERVER_MODULE                   %BRAND_JSON_API_SERVER_MODULE%
