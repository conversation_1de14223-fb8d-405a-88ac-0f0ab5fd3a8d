#if defined(CPCAPI2_BRAND_HEADER)
  #include CPCAPI2_BRAND_HEADER
#elif defined(_WIN32)
  #include "brand_branded_win.h"
#elif defined(ANDROID)
#  include "brand_branded_android.h"
#elif defined(__linux__)
#  include "brand_branded_linux.h"
#elif defined(__THREADX)
#  include "brand_branded_teradici_tera2.h"
#elif __APPLE__
#	include "TargetConditionals.h"
#	if TARGET_OS_IPHONE
#		include "brand_branded_ios.h"
#	else
#		include "brand_branded_osx.h"
#	endif
#endif

#if !defined(CPCAPI2_BRAND_VERSION_NUMBER)
#define CPCAPI2_BRAND_VERSION_NUMBER                               "1.0 DEV"
#endif