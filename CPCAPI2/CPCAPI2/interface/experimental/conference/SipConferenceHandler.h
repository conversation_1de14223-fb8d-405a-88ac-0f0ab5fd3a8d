#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_HANDLER_H)
#define CPCAPI2_SIP_CONFERENCE_HANDLER_H

#include "cpcapi2defs.h"
#include "SipConferenceTypes.h"

namespace CPCAPI2
{ 
namespace SipConference
{

/**
* <PERSON><PERSON> for events relating creating, destroying and updating conferences.
* Set in SipConferenceManager::setHandler().
*/
class SipConferenceHandler
{
public:

};

} // SipConference
} // CPCAPI2

#endif // CPCAPI2_SIP_CONFERENCE_HANDLER_H