#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_MANAGER_H)
#define CPCAPI2_SIP_CONFERENCE_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipConferenceTypes.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{ 
class Phone;

namespace SipConference
{
class SipConferenceHandler;

/**
* Manager interface to control incoming, outgoing and established conference calls; 
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SipConferenceManager
{
public:
   /**
   * Get a reference to the %SipConferenceManager interface.
   */   
   static SipConferenceManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for call events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as
   * SipAccountManager::process(..)
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConferenceHandler* handler) = 0;

   /**
   * Allocates a new conference within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new conference.
   */
   virtual CPCAPI2::SipConversation::SipConversationHandle createServerConference(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   *
   */
   virtual int setConferenceFactoryAddress(CPCAPI2::SipConversation::SipConversationHandle conference, const cpc::string& confFactoryAddress) = 0;

   /**
   * Initiates a conference by sending an INVITE to the remote participants (see addParticipant(..)) and the participants send INVITE to focus.
   * It is also used, togheter with addParticipant(...), to add new participants when conference already started.
   */
   virtual int start(CPCAPI2::SipConversation::SipConversationHandle conference) = 0;

   /**
   *
   */
   virtual int addToServerConference(CPCAPI2::SipConversation::SipConversationHandle conference, 
                                        CPCAPI2::SipConversation::SipConversationHandle conversation) = 0;


protected:
   /*
    * The SDK will manage memory life of %SipConferenceManager.
    */
   virtual ~SipConferenceManager() {}

};

}
}

#endif // CPCAPI2_SIP_CONFERENCE_MANAGER_H
