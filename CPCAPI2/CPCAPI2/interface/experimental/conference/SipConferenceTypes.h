#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_TYPES_H)
#define CPCAPI2_SIP_CONFERENCE_TYPES_H

#define CONFERENCE_MIME_TYPE "application"
#define CONFERENCE_MIME_SUBTYPE "conference-info+xml"
#define CONFERENCE_PACKAGE "conference"
#define DEFAULT_MAX_USER_COUNT 100

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
namespace SipConference
{
typedef unsigned int SipConferenceHandle;

/**
* State of the conference data or parts of the conference data. 
* Description of the validity of conference data.
*/
enum SipConferenceStatus
{
	Full,
	Partial,
	Deleted
};

/**
* Available media types in SIP conference. 
*/
enum SipConferenceMediaType
{
	Audio=0,
	Video,
	Text,
	Message
};

/**
* This element indicates the available status of the media
* stream available to the conference participants.
*/
enum SipConferenceMediaStatus
{
	None=0,
	<PERSON>rec<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Inactive
};

/**
* Status of the conference endpoint.
*/
enum SipConferenceEndpointStatus
{
	Connected,
	Disconnected,
	OnHold,
	MutedViaFocus,
	Pending,
	Alerting,
	DialingIn,
	DialingOut,
	Disconnecting
};

/**
* Method that describes how participant joined conference.
*/
enum SipConferenceJoinMethod
{
	DialedIn,
	DialedOut,
	FocusOwner
};

/**
* Method that describes how participant left conference.
*/
enum SipConferenceDisconnectionMethod
{
	Departed,
	Booted,
	Failed,
	Busy
};

/**
* Structure that describes action of an endpoint. 
* It consists of time of action, reason for the action and by whom action is done.
*/
struct SipConferenceEndpointAction
{
	cpc::string time;
	cpc::string reason;
	cpc::string byUser;
};

/**
* Basic information about call from endpoint.
*/
struct SipConferenceCallInfo
{
	cpc::string displayText;
	cpc::string callId;
	cpc::string fromTag;
	cpc::string toTag;
};

/**
* Structure that describes media that participant uses in conference.
*/
struct SipConferenceMedia
{
	cpc::string label;
	cpc::string displayText;
	cpc::string srcId;
	SipConferenceMediaType type; // "audio", "video", "text", and "message".
	SipConferenceMediaStatus status; // "none", "sendrecv", "sendonly", "recvonly", or "inactive"
};

/**
* Structure that describes endpoint in conference.
*/
struct SipConferenceEndpoint
{
	cpc::string uri; //entity attribute
	SipConferenceStatus state; // state attribute
	cpc::string displayText;
	SipConferenceEndpointAction reffered;
	SipConferenceEndpointStatus status;
	SipConferenceJoinMethod joinMethod;
	SipConferenceEndpointAction joinInfo;
	SipConferenceDisconnectionMethod disconnectionMethod;
	SipConferenceEndpointAction disconnectionInfo;
	SipConferenceMedia media;
	SipConferenceCallInfo callInfo;
};

/**
* Structure that describes URI that participates in conference. 
* uri - must be present. Containes URI in string form.
*/
struct SipConferenceUri
{
	cpc::string uri;
	cpc::string displayText;
	cpc::string purpose;
};

/**
* Structure that describes participant in the conference.
*/
struct SipConferenceParticipant
{
	cpc::string uri; // entity attribute
	SipConferenceStatus status;  //state attribute

	cpc::string displayText;
	cpc::vector<SipConferenceUri> associatedAors;
	cpc::string roles;
	cpc::string languages;
	cpc::string cascadedFocus;
	cpc::vector<SipConferenceEndpoint> endpoints;
};

/**
* Description of a conference.  Information about conference functionality and its participants.
*/
struct SipConferenceDescription
{
	cpc::string displayText;
	cpc::string subject;
	cpc::string freeText;
	cpc::string keywords;
	cpc::vector<SipConferenceUri> conferenceUris;
	cpc::vector<SipConferenceUri> serviceUris;
	unsigned int maxUserCount;
	cpc::vector<SipConferenceMedia> availableMedia;
};

/**
* Information about host of the conference.
*/
struct SipConferenceHostInfo
{
	cpc::string displayText;
	cpc::string webPage;
	cpc::vector<cpc::string> uris;
};

/**
* Basic information about conference state.
*/
struct SipConferenceState
{
	unsigned int userCount;
	bool active;
	bool locked;
};

/**
*
*/
struct SipConferenceSidebarRef
{
	cpc::string uri;
	cpc::string displayText;
};

/**
*
*/
struct SipConferenceSidebarByRef
{
	SipConferenceStatus status;
	cpc::vector<SipConferenceSidebarRef> sidebarConfs;
};

/**
*
*/
struct SipConferenceSidebarVal
{
	cpc::string uri;
	SipConferenceStatus status;
	cpc::vector<cpc::string> users;
};

/**
*
*/
struct SipConferenceSidebarByVal
{
	SipConferenceStatus status;
	cpc::vector<SipConferenceSidebarVal> sidebarConfs;
};

/*
* Structure that represents conference state and information necessary for conference functioning.
*/
struct SipConferenceInfo
{
	cpc::string focusUri;
	unsigned int expirationTime;
	unsigned int version;
	SipConferenceStatus conferenceStatus;
	SipConferenceDescription conferenceDescription;
	SipConferenceHostInfo hostInfo;
	SipConferenceState conferenceState;
	cpc::vector<SipConferenceParticipant> participants;
	SipConferenceSidebarByRef sidebarsByReference;
	SipConferenceSidebarByVal sidebarsByValue;
};

/**
* Conference policy. 
* Predefined set of rules that describes how conference will be conducted.
*/
struct SipConferencePolicy
{
	unsigned int maxUserCount;
	unsigned int duration;
	bool passwordProtected;
	cpc::vector<cpc::string> notAllowedParticipants;
	cpc::vector<cpc::string> needApprovalParticipants;
	SipConferencePolicy()
	{
		maxUserCount = 25;
		duration = 0;
		passwordProtected = false;
	}
};

} // SipConference
} // CPCAPI2

#endif // CPCAPI2_SIP_CONFERENCE_TYPES_H
