#pragma once

#if !defined(CPCAPI2_VIDEO_STREAMING_H)
#define CPCAPI2_VIDEO_STREAMING_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "orchestration_server/OrchestrationServer.h"

namespace CPCAPI2
{
class Phone;

namespace VideoStreaming
{
typedef unsigned int VideoStreamHandle;

enum ServerType
{
   ServerType_Websocket = 0,
   ServerType_CallbackFunction = 1
};

struct VideoStreamingServerConfig
{
   VideoStreamingServerConfig() : serverType(CPCAPI2::VideoStreaming::ServerType_Websocket), listenPort(9005) {}
   CPCAPI2::VideoStreaming::ServerType serverType;
   int listenPort;
   cpc::string wssCertificateFilePath;
   cpc::string wssPrivateKeyFilePath;
   cpc::string wssDiffieHellmanParamsFilePath;
};

enum StreamFormat
{
   StreamFormat_H264 = 0,
   StreamFormat_I420 = 1
};

struct VideoStreamSettings
{
   VideoStreamSettings() : streamFormat(StreamFormat_H264), videoReceiveChannel(-1), videoSendChannel(-1), cameraCaptureId(-1), audioReceiveChannel(-1), audioSendChannel(-1) {}
   StreamFormat streamFormat;
   int videoReceiveChannel;
   int videoSendChannel;
   int cameraCaptureId;
   int audioReceiveChannel;
   int audioSendChannel;
};

class VideoStreamHandler
{
public:
   virtual ~VideoStreamHandler() {}

};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API VideoStreamingManager
{
public:
   /**
   * Get a reference to the %VideoStreamingManager interface.
   */   
   static VideoStreamingManager* getInterface(Phone* cpcPhone);

   virtual int startVideoStreamingServer(const VideoStreamingServerConfig& serverConfig) = 0;
   virtual int stopVideoStreamingServer() = 0;
   virtual int setVideoStreamingServer(VideoStreamingManager* masterVideoStreamingManager) = 0;

   virtual VideoStreamHandle createVideoStream() = 0;
   virtual int setVideoStreamSettings(VideoStreamHandle videoStream, const VideoStreamSettings& settings) = 0;
   virtual int setVideoStreamHandler(VideoStreamHandle videoStream, VideoStreamHandler* handler) = 0;
   virtual int startVideoStream(VideoStreamHandle videoStream) = 0;
   virtual int stopVideoStream(VideoStreamHandle videoStream) = 0;
   virtual int destroyVideoStream(VideoStreamHandle videoStream) = 0;
   virtual int getStreamCount(VideoStreamHandle videoStream) = 0;

   virtual int addVideoStreamCallback(VideoStreamHandle videoStream, void* cbState, void(*funcToRun)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)) = 0;
   virtual int removeVideoStreamCallback(VideoStreamHandle videoStream) = 0;

   virtual void setOnPrivilegedAccessCompleted(void(*onPrivilegedAccessCompleted)(void*), void* context) {}

};

}
}

#endif // CPCAPI2_VIDEO_STREAMING_H
