#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_JSON_API_H)
#define CPCAPI2_SIP_CONVERSATION_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace SipConversation
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API SipConversationJsonApi
{
public:
   /**
   * Get a reference to the %SipConversationJsonApi interface.
   */
   static SipConversationJsonApi* getInterface(Phone* cpcPhone);
};

}
}

#endif // CPCAPI2_SIP_CONVERSATION_JSON_API_H
