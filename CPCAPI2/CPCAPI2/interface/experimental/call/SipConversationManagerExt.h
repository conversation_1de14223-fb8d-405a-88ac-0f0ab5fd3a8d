#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_MANAGER_EXT_H)
#define CPCAPI2_SIP_CONVERSATION_MANAGER_EXT_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{ 
class Phone;

namespace SipConversation 
{
struct NackSettings
{
   int maxNackListSize = -1;
   int maxPacketAgeToNack = -1;
   int maxIncompleteTimeMs = -1;
   int nackHistorySizeSender = -1;
};
/**
*/
class CPCAPI2_SHAREDLIBRARY_API SipConversationManagerExt
{
public:
   /**
   * Get a reference to the %SipConversationManagerExt interface.
   */   
   static SipConversationManagerExt* getInterface(Phone* cpcPhone);

   virtual int setVideoNackSettings(CPCAPI2::SipConversation::SipConversationHandle conversation, const NackSettings& nackSettings) = 0;
   
   virtual int sendInfo(SipConversationHandle conversation, const cpc::string& body) = 0;

   virtual int setForceTarget(CPCAPI2::SipConversation::SipConversationHandle conversation, const cpc::string& target) = 0;

protected:
   /*
    * The SDK will manage memory life of %SipConversationManagerExt.
    */
   virtual ~SipConversationManagerExt() {}
};

}
}

#endif // CPCAPI2_SIP_CONVERSATION_MANAGER_EXT_H
