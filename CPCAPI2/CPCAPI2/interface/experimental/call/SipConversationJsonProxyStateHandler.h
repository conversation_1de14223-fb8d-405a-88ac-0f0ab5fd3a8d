#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_SIP_CONVERSATION_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "../jsonapi/JsonApiClient.h"
#include "call/SipConversationState.h"

namespace CPCAPI2
{
namespace SipConversation
{
struct JsonProxyConversationStateEvent
{
   cpc::vector<SipConversationState> conversationState;
};

class SipConversationJsonProxyStateHandler
{
public:
   virtual int onConversationState(CPCAPI2::JsonApi::JsonApiConnectionHandle conn, const JsonProxyConversationStateEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_CONVERSATION_JSON_PROXY_STATE_HANDLER_H
