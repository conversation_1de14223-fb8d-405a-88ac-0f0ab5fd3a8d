#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_JSON_PROXY_H)
#define CPCAPI2_SIP_CONVERSATION_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "call/SipConversationManager.h"
#include "call/SipConversationManagerExt.h"

namespace CPCAPI2
{
class Phone;
namespace SipConversation
{
class SipConversationJsonProxyStateHandler;

enum RemoteMediaMode
{
   RemoteMediaMode_Relay,
   RemoteMediaMode_Bypass
};

class CPCAPI2_SHAREDLIBRARY_API SipConversationManagerJsonProxy : public CPCAPI2::SipConversation::SipConversationManager
{
public:
   /**
   * Get a reference to the SipConversationManager interface.
   */
   static SipConversationManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(SipConversationJsonProxyStateHandler* handler) = 0;
   virtual int joinRemoteConversation(SipConversationHandle conversation, RemoteMediaMode remoteMediaMode) = 0;

   // experimental -- do not use
   virtual int createMediaBridge() {
      return -1;
   }

   virtual int requestStateAllConversations() = 0;

   // experimental -- do not use
   virtual int setVideoNackSettings(SipConversationHandle conversation, const NackSettings& nackSettings) = 0;

protected:
   /*
    * The SDK will manage memory life of %SipConversationManager.
    */
   virtual ~SipConversationManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_SIP_CONVERSATION_JSON_PROXY_H
