#pragma once

#if !defined(CPCAPI2_JSON_DATA_H)
#define CPCAPI2_JSON_DATA_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Json
{
class JsonData
{
public:
   virtual int32_t getMessageSize() const = 0;
   virtual int32_t getBinarySize() const = 0;
   virtual const char* getMessageData() const = 0;
   virtual const char* getBinaryData() const = 0;

   virtual bool isBinary() const = 0;
   virtual void setBinaryData(const char* buffer, uint32_t size) = 0;
   
   virtual ~JsonData() {}
};

class JsonDataPointer
{
    void* count;
    JsonData* ptr;

    void increment();
    void decrement();

public:
   JsonDataPointer();
   JsonDataPointer(const JsonDataPointer& rhs);
   ~JsonDataPointer();
   JsonDataPointer& operator=(const JsonDataPointer& s);

   JsonData* operator->() const { return ptr; }
   JsonData* get() const  { return ptr; }
   JsonData& operator*() const { return *ptr; }
};

JsonDataPointer MakeJsonDataPointer();

} // namespace Json
} // namespace CPCAPI2

#endif // CPCAPI2_JSON_DATA_H
