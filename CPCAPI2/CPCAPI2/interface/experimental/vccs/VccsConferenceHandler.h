#pragma once

#ifndef CPCAPI2_VCCS_CONFERENCE_HANDLER_H
#define CPCAPI2_VCCS_CONFERENCE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "VccsAccountTypes.h"
#include "VccsConferenceTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
        
         typedef enum ChangeType
         {
            ChangeType_Add,
            ChangeType_Update,
            ChangeType_Query
         } ChangeType;

         /**
          * Enumeration listing different possible error codes coming from the
          * server for conference operations.
          */
         typedef enum ErrorCode
         {
            /**
             * For errors generated by the client (locally)
             */
            ErrorCode_LocallyGenerated,

            /**
             * <PERSON> received a message with an unknown cmd value.
             */
            ErrorCode_UnknownCommand,

            /**
             * A message was sent but the user was not authenticated.
             */
            ErrorCode_Unauthenticated,

            /**
             * Authentication/Authorization error
             */
            ErrorCode_LoginFailed,

            /**
             * The user has been "locked out" for too many authorization errors,
             * or possibly other causes.
             */
            ErrorCode_LockedOut,

            /**
             * An unexpected server error (possibly exception).
             */
            ErrorCode_UnexpectedError,

            /**
             * Returned when server is misconfigured and therefore cannot process
             * an operation/message
             */
            ErrorCode_ConfigurationError,

            /**
             * Returned when there was a database error or failure to persist
             * information. Unlikely.
             */
            ErrorCode_DatabasePersistError,

            /**
             * Some aspect of the request is invalid; missing a required field or
             * otherwise incorrect. Used for operation validation.
             */
            ErrorCode_InvalidRequest,

            /**
             * Error issued when a non-moderator attempts to perform an operation
             * which is only allowed by moderators.
             */
            ErrorCode_ModeratorOnly,

            /**
             * Error issued when the conference ID either could not be found, or is
             * a conference ID of a conference which is 'not visible' to the current
             * user.
             */
            ErrorCode_ConferenceNotFound,

            /**
             * An operation was attempted to be done to a conference which is not
             * currently running/active (an operation which would normally require
             * an active conference).
             */
            ErrorCode_ConferenceNotActive,

            /**
             * No physical bridges are available to be used, can happen on a subscribe
             * failure.
             */
            ErrorCode_NoBridgesAvailable,

            /**
             * The participant ID is invalid.
             */
            ErrorCode_ParticipantNotFound,
            
            /**
             * A PIN is required in order for the operation to proceed further.
             */
            ErrorCode_PinRequired,

            /**
             * The operation failed because the conference was 'locked'
             */
            ErrorCode_ConferenceLocked,

            /**
             * The operation failed because the conference reached the participant limit
             */
            ErrorCode_TooManyParticipants,

            /**
             * General "catch all" error code which is used by the SDK in the situation
             * where the server responds with a heretofore unknown error code.
             */
            ErrorCode_Unknown
         } ErrorCode;

         typedef struct ConferenceListChange
         {
            ChangeType changeType;
            ConferenceDetails conference;
         } ConferenceListChange;

         typedef struct ConferenceListUpdatedEvent
         {
            cpc::vector<ConferenceListChange> changes;
         } ConferenceListUpdatedEvent;

         typedef struct ParticipantListChange
         {
            ChangeType changeType;
            ParticipantStatus participant;
         } ParticipantListChange;

         typedef struct ParticipantListUpdatedEvent
         {
            cpc::vector<ParticipantListChange> changes;
         } ParticipantListUpdatedEvent;

         typedef struct SubscribeEvent
         {
            /**
             * The conference ID to which the subscribe was performed
             */
            VccsConferenceHandle hConference;

            /**
             * The address for which direct SIP calling should be used.
             */
            cpc::string directSIPAddress;

            /**
             * Configuration for direct dialing, note that these are
             * deprecated, the application should only use them in the
             * event that the provisioning document is not specified.
             */
            DEPRECATED cpc::string directSIPUsername;
            DEPRECATED cpc::string directSIPPassword;
            DEPRECATED cpc::string directSIPProxy;
            DEPRECATED cpc::string directSIPWebsocketServer;

            /**
             * A JSON-formatted string which may be parsed by other SDK modules
             * in order to provision them via the "decodeProvisioningResponse"
             * methods, for example account and call modules support this
             * format.
             */
            cpc::string directSIPProvisioning;

         } SubscribeEvent;

         typedef struct UnsubscribeEvent
         {
         } UnsubscribeEvent;

         typedef struct ConferenceInviteEvent
         {
            /**
             * Text format invite request for the conference.
             */
            cpc::string textInvite;

            /**
             * HTML format invite request for the conference.
             */
            cpc::string htmlInvite;

            /**
             * URL to join the conference directly.
             */
            cpc::string joinUrl;
         } ConferenceInviteEvent;

         /**
          * Event fired upon successful retrieval of the conference config.
          */
         typedef struct ConferenceConfigEvent
         {
            ConferenceConfiguration config;
         } ConferenceConfigEvent;

         /**
          * Placeholder event
          */
         typedef struct SetConferenceConfigEvent
         {
         } SetConferenceConfigEvent;

         /**
          * Event fired when requests fail for conference operations.
          */
         typedef struct ConferenceFailureEvent
         {
            /**
             * Reason text supplied for the failure from the websocketpp
             * library
             */
            cpc::string reasonText;

            /**
             * Error code provided in enum form (in many cases this is just a
             * straight translation of the reasonText, but nevertheless should
             * avoid the application having to depend on string formats).
             */
            ErrorCode errorCode;
         } ConferenceFailureEvent;

         /**
          * Event fired when requests fail for participant operations.
          */
         typedef struct ParticipantFailureEvent
         {
            /**
             * Reason text supplied for the failure from the websocketpp library
             */
            cpc::string reasonText;

            /**
             * Error code provided in enum form (in many cases this is just a
             * straight translation of the reasonText, but nevertheless should
             * avoid the application having to depend on string formats).
             */
            ErrorCode errorCode;

            /**
             * Participant handle for the failed operation
             */
            VccsConferenceParticipantHandle hParticipant;
         } ParticipantFailureEvent;

         typedef struct XMPPAccountInfoEvent
         {
            /**
             * The Domain of the XMPP server
             */
            cpc::string domain;

            /**
             * The proxy of the XMPP server, if any (empty if none)
             */
            cpc::string proxy;

            /**
             * The port to connect to (0 by default)
             */
            int port;

            /**
             * The JID (jabber ID) of the chat room to use for XMPP
             */
            cpc::string chatRoomJid;

            /**
             * The assigned (possibly temporary) username
             */
            cpc::string username;

            /**
             * The assigned (possibly temporary) password.
             * Note this may not always be available.
             */
            cpc::string password;

            /**
             * The displayName of the user in the chat room
             * (if empty, use 'username')
             */
            cpc::string displayName;
         } XMPPAccountInfoEvent;

         typedef struct ScreenSharePresenterEvent
         {
            /**
             * The participant ID which was selected as presenter
             */
            VccsConferenceParticipantHandle hParticipant;
         } PresenterChangedEvent;

         typedef struct ScreenShareConfigEvent
         {
            /**
             * The presenter participant ID
             */
            VccsConferenceParticipantHandle hParticipant;

            /**
             * Boolean indicating whether or not the screensharing
             * session is active.
             */
            bool screenShareActive;

            /**
             * The URL which describes the screen sharing resource
             */
            cpc::string screenShareURL;

            /**
             * Additional configuration data to help in making
             * the screensharing connection.
             */
            ScreenSharingInfoList config;
         } ScreenShareConfigEvent;

         /**
          * Voice Activity Detection
          */
         typedef struct VoiceActivityEvent
         {
            /**
             * The participant with voice activity
             */
            VccsConferenceParticipantHandle hParticipant;

            /**
             * Indicates the specified participant is yammering on
             */
            bool isTalking;

            /**
             * Indicates the specified participant has the audio floor
             */
            bool hasAudioFloor;

            /**
             * Voice energy level, value from 0 to 15, log scale
             */
            int energyLevel;
         } VoiceActivityEvent;

         typedef struct ConferenceHistoryEvent
         {
            cpc::vector< ConferenceHistoryEntry > historyEntries;
         } ConferenceHistoryEvent;

         typedef struct DeleteConferenceHistoryEvent
         {
         } DeleteConferenceHistoryEvent;

         /**
          * Event which may be fired by the SDK in response to the server
          * sending a notification to the SDK about some of the conference
          * attributes being changed. These specific conference attributes are
          * known as the "conference mode" In the VCCS parlance. The
          * application should take these values as the new values for this
          * conference.
          */
         typedef struct ConferenceModeEvent
         {
            ConferenceModeEvent() :
               participantLockChanged( false ),
               muteLockChanged( false ),
               hostedChanged( false ),
               muteAllChanged( false ),
               entryExitTonesEnabledChanged( false ),
               recordingChanged( false ),
               audioOnlyRecordingChanged( false ),
               participantLock( false ),
               muteLock( false ),
               hosted( false ),
               muteAll( false ),
               entryExitTonesEnabled( false ),
               audioOnlyRecording( false ) {}

            // Flags indicating whether data changed
            bool participantLockChanged;
            bool muteLockChanged;
            bool hostedChanged;
            bool muteAllChanged;
            bool entryExitTonesEnabledChanged;
            bool recordingChanged;
            bool audioOnlyRecordingChanged;

            // Actual data
            bool participantLock;
            bool muteLock;
            bool hosted;
            bool muteAll;
            bool entryExitTonesEnabled;
            bool recording;
            bool audioOnlyRecording;
         } ConferenceModeEvent;

         /**
          * This structure contains information pertaining to the method
          * and parameters which should be used to connect to a specific
          * conference.
          *
          * Some of the information mirrors information which may be
          * obtained through 'cracking' the VCCS URL. However there is
          * additional information here which may be of import.
          */
         typedef struct ConferenceConnectionInfoEvent
         {
            /**
             * The conference ID for which the info was obtained
             */
            VccsConferenceHandle hConference;

            /**
             * The "group" string associated with this conference. May
             * be used to match SIP domains.
             */
            cpc::string group;

            /**
             * The "lobby" string (most likely a number) which may be
             * dialed in order to land in the lobby of the conference.
             */
            cpc::string lobby;

            /**
             * The "bridge" string (also most likely a number) which may
             * be dialed in order to land directly into the conference.
             */
            cpc::string bridge;

            /**
             * A boolean indicating whether or not a PIN will be required
             * for entry into this conference.
             */
            bool pinRequired;
         } ConferenceConnectionInfoEvent;

         /**
          * Handler for VCCS conference operations
          */
         class VccsConferenceHandler
         {
         public:

            /**
             * This event in response to the application calling
             * getConferenceConnectionInfo
             *
             * @param hAccount the VCCS Account used to obtain the information
             * @param args the ConferenceConnectionInfoEvent containing the information
             * @see ConferenceConnectionInfoEvent
             */
            virtual int onConferenceConnectionInfo( Account::VccsAccountHandle hAccount, const ConferenceConnectionInfoEvent& args ) = 0;

            /**
             * Callback fired when the conference connection info could not be performed.
             */
            virtual int onConferenceConnectionInfoFailure( Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args ) = 0;

            /**
             * Called when a response to a SUBSCRIBE message is received
             * (success)
             */
            virtual int onSubscribe(Account::VccsAccountHandle hAccount, const SubscribeEvent& args) = 0;

            /**
             * Called when a SUBSCRIBE request has failed.
             */
            virtual int onSubscribeFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) = 0;

            /**
             * Called when a response to a UNSUBSCRIBE message is received
             * (success)
             */
            virtual int onUnsubscribe(Account::VccsAccountHandle hAccount, const UnsubscribeEvent& args) = 0;

            /**
             * Called when a UNSUBSCRIBE request has failed.
             */
            virtual int onUnsubscribeFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) = 0;

            /**
             * Called when VccsConferenceManager::queryConferenceList(..)
             * succeeds, when the list of conferences tracked by the VCCS
             * changes, or when the details of a conference change for any
             * reason.
             */
            virtual int onConferenceListUpdated(Account::VccsAccountHandle hAccount, const ConferenceListUpdatedEvent& args) = 0;
         
            /**
             * Called when VccsConferenceManager::queryConferenceList(..)
             * fails, OR when queryConferenceDetails(..) fails.
             */
            virtual int onQueryConferenceListFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) = 0;

            /**
             * Called when the list of participants tracked by the VCCS
             * changes, or when the details of a participant change for any
             * reason.
             */
            virtual int onParticipantListUpdated(VccsConferenceHandle hConference, const ParticipantListUpdatedEvent& args) = 0;

            /**
             * Called when there is a failure while trying to set a flag on the
             * conference.  This currently includes the "muteLock" flag, the
             * "participantLock" flag, the "hosted" flag, and the
             * "muteAll/muteOthers" flag.
             */
            virtual int onSetConferenceModeFailure(VccsConferenceHandle hConference, const ConferenceFailureEvent& args) = 0;

            /**
             * Called when the conference mode was updated from another client
             * (and the values need to be pushed to all registered clients).
             */
            virtual int onConferenceModeUpdated(VccsConferenceHandle hConference, const ConferenceModeEvent& args) = 0;

            /**
             * Called when the kick participant method fails.
             */
            virtual int onKickParticipantFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) = 0;

            /**
             * Called when the mute participant method fails.
             */
            virtual int onMuteParticipantFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) = 0;

            /**
             * Called when setting the recording status fails.
             */
            virtual int onSetIsRecordingFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) = 0;

            /**
             * Called when the account information for XMPP has been returned
             * from the VCCS.
             */
            virtual int onXMPPAccountInfo( VccsConferenceHandle hConference, const XMPPAccountInfoEvent& args ) = 0;

            /**
             * Called when some error resulted in a failure to obtain the XMPP
             * configuration information.
             */
            virtual int onXMPPAccountInfoFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) = 0;

            /**
             * Called when the result is obtained for the queryConferenceInvite
             * call on the VccsConferenceManager
             */
            virtual int onQueryConferenceInvite( VccsConferenceHandle hConference, const ConferenceInviteEvent& args ) = 0;

            /**
             * Called when an error occurs while processing a
             * queryConferenceInvite request.
             */
            virtual int onQueryConferenceInviteFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) = 0;

            /**
             * Called when VCCS server sends a screen share update message with
             * a new presenter flag.  the application should ask the recipient
             * whether they want to accept the presentation request.
             */
            virtual int onSetScreenSharePresenter( VccsConferenceHandle hConference, const ScreenSharePresenterEvent& args ) = 0;

            /**
             * New configuration information about the screen sharing aspect of
             * this conference is available. The application should inspect the
             * data to see whether any measures need to be taken.
             */
            virtual int onScreenShareConfigChanged( VccsConferenceHandle hConference, const ScreenShareConfigEvent& args ) = 0;

            /**
             * Callback invoked when any of setScreenSharePresenter,
             * startScreenShare, or stopScreenShare fail.
             */
            virtual int onScreenShareCommandFailure( VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) = 0;

            /**
             * Callback invoked when any of the mixer options method fails.
             * Currently this includes setVideoFrameRate,
             * setVideoFloorParticipant and setVideoLayout.
             */
            virtual int onMixerOptionsCommandFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) = 0;

            /**
             * Called when VCCS server indicates that the mixer has detected a
             * change in voice activity from one of the participants
             */
            virtual int onVoiceActivityChanged(VccsConferenceHandle hConference, const VoiceActivityEvent& args) = 0;

            /**
             * Callback invoked when the conference configuration was
             * successfully obtained.
             */
            virtual int onQueryConferenceConfig( VccsConferenceHandle hConference, const ConferenceConfigEvent& args ) = 0;

            /**
             * Callback invoked when there was a failure while trying to obtain
             * the conference config.
             */
            virtual int onQueryConferenceConfigFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) = 0;

            /**
             * Callback invoked when the conference config was successfully
             * set. (NB: the actual conference configuration is not available
             * in this event. If that is required, queryConferenceConfig should
             * be performed).
             */
            virtual int onSetConferenceConfig( VccsConferenceHandle hConference, const SetConferenceConfigEvent& args ) = 0;

            /**
             * Callback invoked when the conference config failed to be set.
             */
            virtual int onSetConferenceConfigFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) = 0;

            virtual int onQueryConferenceHistory( VccsConferenceHandle hConference, const ConferenceHistoryEvent& args ) = 0;
            virtual int onQueryConferenceHistoryFailure( VccsConferenceHandle hconference, const ConferenceFailureEvent& args ) = 0;

            virtual int onDeleteConferenceHistory( VccsConferenceHandle hConference, const DeleteConferenceHistoryEvent& args ) = 0;
            virtual int onDeleteConferenceHistoryFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) = 0;
         };

         /**
          * "Stub" Handler. An application may choose to derive from the stub
          * instead of implementing the interface directly. The advantage to
          * doing so is, new methods won't break the implementation of the
          * implementing class as they will be stubbed out here. The
          * disadvantage is, that the application won't be forced to implement
          * new methods immediately (which might be important). Therefore the
          * choice is left to the application.
          */
         class VccsConferenceHandlerStub : public VccsConferenceHandler
         {
         public:
            virtual int onConferenceConnectionInfo( Account::VccsAccountHandle hAccount, const ConferenceConnectionInfoEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onConferenceConnectionInfoFailure( Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onSubscribe(Account::VccsAccountHandle hAccount, const SubscribeEvent& args) OVERRIDE { return kSuccess; }
            virtual int onSubscribeFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) OVERRIDE { return kSuccess; }
            virtual int onUnsubscribe(Account::VccsAccountHandle hAccount, const UnsubscribeEvent& args) OVERRIDE { return kSuccess; }
            virtual int onUnsubscribeFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) OVERRIDE { return kSuccess; }
            virtual int onConferenceListUpdated(Account::VccsAccountHandle hAccount, const ConferenceListUpdatedEvent& args) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceListFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) OVERRIDE { return kSuccess; }
            virtual int onParticipantListUpdated(VccsConferenceHandle hConference, const ParticipantListUpdatedEvent& args) OVERRIDE { return kSuccess; }
            virtual int onSetConferenceModeFailure(VccsConferenceHandle hConference, const ConferenceFailureEvent& args) OVERRIDE { return kSuccess; }
            virtual int onConferenceModeUpdated(VccsConferenceHandle hConference, const ConferenceModeEvent& args) OVERRIDE { return kSuccess; }
            virtual int onKickParticipantFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onMuteParticipantFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onSetIsRecordingFailure(VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onXMPPAccountInfo( VccsConferenceHandle hConference, const XMPPAccountInfoEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onXMPPAccountInfoFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceInvite( VccsConferenceHandle hConference, const ConferenceInviteEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceInviteFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onSetScreenSharePresenter( VccsConferenceHandle hConference, const ScreenSharePresenterEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onScreenShareConfigChanged( VccsConferenceHandle hConference, const ScreenShareConfigEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onScreenShareCommandFailure( VccsConferenceHandle hConference, const ParticipantFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onMixerOptionsCommandFailure(Account::VccsAccountHandle hAccount, const ConferenceFailureEvent& args) OVERRIDE { return kSuccess; }
            virtual int onVoiceActivityChanged(VccsConferenceHandle hConference, const VoiceActivityEvent& args) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceConfig( VccsConferenceHandle hConference, const ConferenceConfigEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceConfigFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onSetConferenceConfig( VccsConferenceHandle hConference, const SetConferenceConfigEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onSetConferenceConfigFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceHistory( VccsConferenceHandle hConference, const ConferenceHistoryEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onQueryConferenceHistoryFailure( VccsConferenceHandle hconference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onDeleteConferenceHistory( VccsConferenceHandle hConference, const DeleteConferenceHistoryEvent& args ) OVERRIDE { return kSuccess; }
            virtual int onDeleteConferenceHistoryFailure( VccsConferenceHandle hConference, const ConferenceFailureEvent& args ) OVERRIDE { return kSuccess; }
         };
      }
   }
}
#endif // CPCAPI2_VCCS_CONFERENCE_HANDLER_H

