#pragma once
#ifndef __CPCAPI2_VCCS_CONFERENCE_TYPES_H__
#define __CPCAPI2_VCCS_CONFERENCE_TYPES_H__

#include "cpcapi2defs.h"
#include <stdint.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
         typedef int32_t VccsConferenceHandle;
         typedef int32_t VccsConferenceParticipantHandle;
         typedef int64_t ServerID;
         typedef int64_t Timestamp;
         typedef int64_t HistoryID;

         typedef enum CallStatus
         {
            CallStatus_InConference, // IN_CONFERENCE,
            CallStatus_Ended,        // ENDED,
            CallStatus_LockedOut,    // LOCKED_OUT, (tried to join a locked conference)
            CallStatus_Kicked,       // KICKED,
            CallStatus_None          // NONE
         } CallStatus;

         /**
          * Different kinds of participants; this enum is not really functional
          * but it is used to provide a visual indicator to the end user about
          * which method a remote participant is using to join the conference.
          *
          * The type of a participant may be "upgraded" or "downgraded"
          * depending on what the server is able to deduce based on current
          * information.
          */
         typedef enum ParticipantType
         {
            ParticipantType_Unknown,
            ParticipantType_DialIn,
            ParticipantType_Web,
            ParticipantType_BriaDesktop,
            ParticipantType_BriaMobile,
            ParticipantType_BriaTablet
         } ParticipantType;

         typedef struct ParticipantStatus 
         {
            ParticipantStatus() :
               participantNumber( -1 ),
               userId( -1 ),
               xmppUserId( -1 ),
               participantType( ParticipantType_Unknown ),
               isRecording( false ),
               startTime( 0 ),
               endTime( 0 ),
               callStatus( CallStatus_None ),
               isMuted( false ),
               isModerator( false ),
               hasVideoFloor( false ),
               capabilities( 0 ) {}

            // A unique number assigned to the participant
            // (note, this number is only unique within a given conference)
	         VccsConferenceParticipantHandle participantNumber;			
	
	         // The following IDs are used to locate the Stretto User, if any.
	         // They are in order of preference
	         ServerID    userId;
	         ServerID    xmppUserId;
	         cpc::string xmppUsername;
	         cpc::string sipUsername;
	
            // Some indication of what mechanism the participant is using to
            // join
            ParticipantType participantType;

	         // Other items
	         cpc::string displayName;
	         cpc::string emailAddress;
	
	         cpc::string callSessionID;
	         cpc::string sipAddress;

	         // The name they recorded
	         cpc::string recordingURI;
            bool isRecording; // true if the participant is recording the conference

	         // Current call data
	         Timestamp  startTime;
	         Timestamp  endTime;
	         CallStatus callStatus;
	         bool       isMuted;
	         bool       isModerator;

            // Indicates the specified participants video stream is
            // prominently placed.  Only one participant will have this set to
            // true at a time.
            bool       hasVideoFloor;
	
	         //
	         // Capabilities
	         //
	         const static int32_t CLIENT_IS_BRIA            = 0x00000001; // <-- deprecated, use participantType instead
	         const static int32_t SUPPORTS_SEND_AUDIO       = 0x00000002;
	         const static int32_t SUPPORTS_SEND_VIDEO       = 0x00000004;
	         const static int32_t SUPPORTS_SEND_SCREENSHARE = 0x00000008;
            const static int32_t SUPPORTS_SEND_IM          = 0x00000010;
	         int32_t capabilities;
         } ParticipantStatus;

         /**
          * This structure contains all of the information which the collab
          * server expects in order to create a new subscription.
          */
         typedef struct SubscriptionInfo
         {
            /**
             * Either the bridge or the conferenceCode should be specified, but
             * not both. If both are specified, the conferenceCode will be
             * used.
             */
            cpc::string bridge;

            /**
             * Either the bridge or the conferenceCode should be specified, but
             * not both. If both are specified, the conferenceCode will be
             * used.
             */
            cpc::string conferenceCode;

            /**
             * The password required to enter into the conference. Note that if
             * the conferenceCode is set, the pin may or may not be required
             * depending on the conference configuration. The method
             * getConferenceConnectionInfo should be used to make this
             * determination.
             */
            cpc::string pin;

            /**
             * A unique string/ID representing a specific application
             * installation.  Note that this is not a deviceID, in the sense
             * that the same application could be installed multiple times on a
             * single device.  Hence, this key should be unique to the specific
             * app install. It is used by the server for matching calls, under
             * the assumption that different application installations will
             * behave in a singleton manner (only one launch per install,
             * running a second time results in the previous app being raised
             * to the foreground).
             *
             * This field is optional, but highly recommended for a good
             * user experience.
             *
             * The appId field should between 12 and 64 characters inclusive.
             * It may use characters A-Z, a-z, and 0-9. Any other characters
             * will be bypassed.
             */
            cpc::string applicationID;

            /**
             * The SIP Address-Of-Record (a URI) which indicates the pairing of
             * SIP account to this subscription. Used by the server for call
             * matching purposes.
             */
            cpc::string sipAor;

            /**
             * The type of participant (as defined above). This is defaulted to
             * "Bria Desktop", so Mobile clients will have to explicitly set
             * this field.
             */
            ParticipantType participantType;

            /**
             * The capabilities of the application in terms of what may be
             * shared in the collab session. This is defaulted to the full list
             * of media.
             */
            int capabilities;

            /**
             * Locale which is used for voice prompts (If not specified the
             * default will be chosen by the collab server).
             */
            cpc::string locale;

            // default ctor
            SubscriptionInfo() :
               participantType( ParticipantType_BriaDesktop ),
               capabilities( ParticipantStatus::CLIENT_IS_BRIA |
                             ParticipantStatus::SUPPORTS_SEND_AUDIO |
                             ParticipantStatus::SUPPORTS_SEND_VIDEO |
                             ParticipantStatus::SUPPORTS_SEND_SCREENSHARE ) {}
         } SubscriptionInfo;

         typedef enum VideoLayout
         {
            VideoLayout_Grid,
            VideoLayout_Focus,
            VideoLayout_Ribbon,
            VideoLayout_Townhall
         } VideoLayout;

         typedef enum VideoResolution
         {
            VideoResolution_640x400 = 0,
            VideoResolution_640x480 = 1,
            VideoResolution_800x600 = 2,
            VideoResolution_1024x576 = 3,
            VideoResolution_1024x768 = 4,
            VideoResolution_1280x720 = 5,
            VideoResolution_1280x960 = 6,
            VideoResolution_1440x1080 = 7,
            VideoResolution_1600x900 = 8,
            VideoResolution_1600x1200 = 9,
            VideoResolution_1920x1080 = 10,
            VideoResolution_1920x1440 = 11,
            VideoResolution_848x480 = 12,
            VideoResolution_854x480 = 13
         } VideoResolution;

         /**
          * Struct containing information about the configuration of a
          * conference.  ConferenceDetails can be provided for active or
          * inactive conferences.
          */
         typedef struct ConferenceDetails
         {
            ConferenceDetails() :
               id( -1 ),
               createdDate( 0 ),
               socketModerator( false ),
               noModeratorOverrunMinutes( 0 ),
               recordNames( false ),
               supportAudio( false ),
               supportVideo( false ),
               supportScreensharing( false ),
               supportMessaging( false ),
               entryTone( true ),
               exitTone( true ),
               isConferenceLive( false ),
               presenterNumber( -1 ),
               started( 0 ),
               updated( 0 ),
               ended( 0 ),
               hosted( false ),
               hostInConference( false ),
               participantLocked( false ),
               muteLocked( false ),
               active( true ),
               audioActive( true ),
               videoActive( false ),
               isRecording( false ),
               layout( VideoLayout_Focus ),
               socketParticipantNumber( -1 ),
               joinMuted( false ),
               videoResolution(VideoResolution_1280x720) {}

            VccsConferenceHandle id;
	
            // When was the conference first created
            Timestamp   createdDate;
            cpc::string title;
            cpc::string description;
            cpc::string participantPin;
	
            // In order to support multi-tenancy, we need to be able to look up
            // a specific bridge by its bridgeNumber AND its lobby SIP address.
            // (ie., bridge 098765432 @ sip:<EMAIL>) This
            // will allow multiple customers to have the same bridge.
            cpc::string lobbySipAddress;
            cpc::string bridgeNumber;

            // Direct bridge access URL - used to bypass "Please enter your
            // bridge number"
            cpc::string sipAddress;	
            cpc::string moderatorPin;

            // Moderator SIP Address allows a caller to bypass entering a PIN.
            cpc::string moderatorSipAddress;

            // True if the sender of the message is also the moderator of this
            // conference
            bool socketModerator;

            // How long to leave the conference up, after the moderator leaves
            int  noModeratorOverrunMinutes;
	
            bool        recordNames;
            bool        supportAudio;
            bool        supportVideo;
            bool        supportScreensharing;
            bool        supportMessaging;
            cpc::string screenshareUrl;
            cpc::string xmppChatRoomJid;

            // Entry/Exit chime
            bool        entryTone;
            bool        exitTone;

            // Set to true if the conference is "active", and the following
            // struct should be consulted for additional information.
            bool isConferenceLive;

            // **Addition information about "live" conferences follows:**
            // ==========================================================

            // The list of all particpants, including those no longer in the
            // conference.
            //
            // NB: This is deprecated as of protocol version 1005
	         DEPRECATED cpc::vector< ParticipantStatus > participants;
	
	         // Index into the participants list of the presenter
	         VccsConferenceParticipantHandle presenterNumber; // -1 indicates no presenter

	         Timestamp started;
	         Timestamp updated;  // Time of last change
	         Timestamp ended;    // Time the conference ended.  Null if currently active.
	
	         bool hosted;
	         bool hostInConference;
	         bool participantLocked;
            bool muteLocked;
	         bool active;
	         bool audioActive;
	         bool videoActive;

            // true if the conference is being recorded on the server. Note the
            // difference between this and the attribute of the same name in the
            // ParticipantDetails.
            bool isRecording;

            // The currently selected video layout
            VideoLayout layout;

            // The known video mixer layouts. These values may be used in the
            // mixer options command to change the video layout.
            cpc::vector< VideoLayout > videoLayouts;

            // The participant number of the client, as determined by the
            // server (this may be used to find the information for yourself,
            // in the list of participants above). Set to -1 in the event that
            // the server cannot supply the number.
            VccsConferenceParticipantHandle socketParticipantNumber;

            // True if the conference should be automatically muted upon joining
            // (by default, false)
            bool joinMuted;

            //The active video resolution
            VideoResolution videoResolution;
         } ConferenceDetails;

         /**
          * Struct which contains all conference configuration, this can be
          * queried from the conference and/or set depending on whether or not
          * you are the "owner" of the conference.
          */
         typedef struct ConferenceConfiguration
         {
            cpc::string m_ParticipantPin;
            cpc::string m_ModeratorPin;
            VideoLayout m_VideoLayout;
            int         m_FrameRate;
            bool        m_IsModerated;
            int         m_NoModeratorOverrunMinutes;
            cpc::string m_DropBoxToken;
            bool        m_SendSummaryEmail;
            bool        m_RecordAudioOnly;
            bool        m_AutoRecord;
            bool        m_JoinMuted;

            // Initialize to sensible default values
            ConferenceConfiguration() :
               m_VideoLayout( VideoLayout_Focus ),
               m_FrameRate( 10 ),
               m_IsModerated( false ),
               m_NoModeratorOverrunMinutes( 0 ),
               m_SendSummaryEmail( true ),
               m_RecordAudioOnly( false ),
               m_AutoRecord( false ),
               m_JoinMuted( false ) {}
         } ConferenceConfiguration;

         /**
          * This structure allows the SDK to determine which fields from the
          * ConferenceConfiguration to set on the server. i.e. all fields are
          * optional and the application can choose to set any one, several, or
          * all at the same time.
          *
          * The names are all the same as ConferenceConfiguration. Set the value
          * to true in order to set the value on the server, false if the value
          * should not be changed on the server from its previous value.
          */
         typedef struct ConferenceConfigurationSet
         {
            bool m_ParticipantPin;
            bool m_ModeratorPin;
            bool m_VideoLayout;
            bool m_FrameRate; // no longer supported since websocket API version 1006
            bool m_IsModerated;
            bool m_NoModeratorOverrunMinutes;
            bool m_DropBoxToken;
            bool m_SendSummaryEmail;
            bool m_RecordAudioOnly;
            bool m_AutoRecord;
            bool m_JoinMuted;

            // Initialize everything to false in the ctor
            ConferenceConfigurationSet() :
               m_ParticipantPin( false ),
               m_ModeratorPin( false ),
               m_VideoLayout( false ),
               m_FrameRate( false ),
               m_IsModerated( false ),
               m_NoModeratorOverrunMinutes( false ),
               m_DropBoxToken( false ),
               m_SendSummaryEmail( false ),
               m_RecordAudioOnly( false ),
               m_AutoRecord( false ),
               m_JoinMuted( false ) {}

         } ConferenceConfigurationSet;

         /**
          * Arbitrary key/value pairs which are used to pass between
          * applications to allow exchange of information for (potentially)
          * differing implementations of screensharing protocols.
          */
         typedef struct ScreenSharingInfo
         {
            cpc::string name;
            cpc::string value;
         } ScreenSharingInfo;

         /**
          * A List of ScreenSharingInfo
          */
         typedef cpc::vector< ScreenSharingInfo > ScreenSharingInfoList;

         typedef struct ParticipantHistoryEntry
         {
            VccsConferenceParticipantHandle participantNumber;
            cpc::string                     userName;
            ParticipantType                 participantType;
            int64_t                         capabilities;
            cpc::string                     sipAddress;
            cpc::string                     displayName;
            cpc::string                     xmppJid;
            Timestamp                       callStartTime;       // millis since epoch
            Timestamp                       callEndTime;         // millis since epoch
            bool                            moderator;
            bool                            owner;
            bool                            recorded;
            bool                            presenter;
         } ParticipantHistoryEntry;

         typedef struct ConferenceHistoryEntry
         {
            HistoryID   historyID;
            Timestamp   conferenceStart;        // millis since epoch
            Timestamp   conferenceEnd;          // millis since epoch
            int64_t     webParticipantCount;
            int64_t     dialInParticipantCount;
            int64_t     desktopCount;
            int64_t     mobileCount;
            int64_t     tabletCount;
            int64_t     totalParticipants;
            int64_t     kickedParticipants;
            int64_t     screenshareUsage;
            cpc::string recordingUrl;
            cpc::vector< ParticipantHistoryEntry > participants;
         } ConferenceHistoryEntry;
      }
   }
}

#endif //  __CPCAPI2_VCCS_CONFERENCE_TYPES_H__
