#pragma once

#ifndef CPCAPI2_VCCS_CONFERENCE_MANAGER_H
#define CPCAPI2_VCCS_CONFERENCE_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "VccsAccountTypes.h"
#include "VccsConferenceTypes.h"

namespace CPCAPI2
{
   class Phone;

   namespace VCCS
   {
      namespace Conference
      {
         class VccsConferenceHandler;

         /**
          * Manager interface that provides access to VCCS conferences. Any
          * actions undertaken on the conference manager, must be performed
          * while the account is in a registered state (otherwise they will be
          * rejected).
          */
         class CPCAPI2_SHAREDLIBRARY_API VccsConferenceManager
         {
         public:

            /**
             * Get a reference to the %VccsConferenceManager interface.
             */   
            static VccsConferenceManager* getInterface(Phone* cpcPhone);

            /**
             * Set the handler for conference events on the specified account.
             * Set the handler immediately after creating the account.
             *
             * To un-register the handler, pass NULL for handler. Must be
             * called on the same thread as VccsAccount::process(..)
             *
             * NB: Calling setHandler with NULL as the handler will result in
             * the calling thread being blocked until the handler has been
             * removed from the system.
             */
            virtual int setHandler(
               Account::VccsAccountHandle account,
               VccsConferenceHandler* handler) = 0;

            /**
             * Preliminary method for retrieving information about how to
             * connect to the conference, when a conference code is the only
             * piece of information currently in hand. Should be done before
             * the call to subscribe in order to know (for example) whether
             * or not a pin needs to be obtained from the user.
             */
            virtual int getConferenceConnectionInfo(
               Account::VccsAccountHandle account,
               const cpc::string& conferenceCode ) = 0;

            /**
             * Subscribe to a conference in order to receive asynchronous
             * updates including conference and participant updates.
             *
             * See the documentation for the SubscriptionInfo structure in
             * order to determine which fields to populate, and how.
             *
             * The application will be able to obtain the VccsConferenceHandle
             * by listening to the ConferenceEvent (if it does not have this
             * already).
             */
            virtual int subscribe(
               Account::VccsAccountHandle hAccount,
               const SubscriptionInfo& info ) = 0;

            /**
             * ***NOTE: This API has been deprecated in favour of the subscribe
             * method which takes a structure above***
             *
             * Subscribes to a bridge in order to receive asynchronous updates
             * including conference and participant updates.
             *
             * The application will be able to obtain the VccsConferenceHandle
             * by listening to the ConferenceEvent (if it does not have this
             * already).
             */
            DEPRECATED virtual int subscribe(
               Account::VccsAccountHandle hAccount,
               const cpc::string& bridge,
               const cpc::string& pin,
               const cpc::string& sipAor = "",
               ParticipantType participantType = ParticipantType_BriaDesktop,
               int capabilities = ParticipantStatus::CLIENT_IS_BRIA |
                                  ParticipantStatus::SUPPORTS_SEND_AUDIO |
                                  ParticipantStatus::SUPPORTS_SEND_VIDEO |
                                  ParticipantStatus::SUPPORTS_SEND_SCREENSHARE ) = 0;

            /**
             * ***NOTE: This API has been deprecated in favour of the subscribe
             * method which takes a structure above***
             *
             * Subscribes to a bridge, in this case the bridge and pin are
             * contained within the "conferenceCode". This type of subscription
             * is useful when the application doesn't have the bridge or pin
             * information yet, but has a VCCS URL.
             *
             * The application will be able to obtain the VccsConferenceHandle
             * by listening to the ConferenceEvent (if it does not have this
             * already).
             */
            DEPRECATED virtual int subscribe2(
               Account::VccsAccountHandle hAccount,
               const cpc::string& conferenceCode,
               const cpc::string& pin,
               const cpc::string& sipAor = "",
               ParticipantType participantType = ParticipantType_BriaDesktop,
               int capabilities = ParticipantStatus::CLIENT_IS_BRIA |
                                  ParticipantStatus::SUPPORTS_SEND_AUDIO |
                                  ParticipantStatus::SUPPORTS_SEND_VIDEO |
                                  ParticipantStatus::SUPPORTS_SEND_SCREENSHARE ) = 0;

            /**
             * Allows the application to stop receiving updates for a specific
             * conference/bridge.
             */
            virtual int unsubscribe(
               Account::VccsAccountHandle hAccount,
               const cpc::string& bridge) = 0;

            /* Conference Management */
            virtual int queryConferenceList(
               Account::VccsAccountHandle hAccount) = 0;

            /**
             * Performs a "one-time" fetch/query for conference details, given
             * a specific conference handle.
             *
             * The result will be returned asynchronously in an
             * onConferenceListUpdated callback/event pair.
             */
            virtual int queryConferenceDetails(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference) = 0;

            /**
             * Retrieves a text (and HTML) "invitation" to the conference which
             * has been created by the administrator. The text is meant to be
             * sent over another transport (e-mail), such that a remote party
             * can follow some steps in order to join this particular
             * conference.
             *
             * The invitation will be returned asynchronously in an
             * onQueryConferenceInvite callback, otherwise if there is an
             * error, an onQueryConferenceInviteFailure will be invoked.
             */
            virtual int queryConferenceInvite(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference) = 0;

            /**
             * Controls whether new callers can join the conference. If null,
             * the conference�s participant lock will be unaffected.  If true,
             * new callers will be prevented from joining the conference.  If
             * false, new callers are permitted to join the conference.
             */
            virtual int setParticipantLock(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool locked ) = 0;

            /**
             * Controls participant mute capabilities.  If set to true, normal
             * participants will not be allowed to unmute themselves.  New
             * participants will join muted.  Additionally, when �true� is
             * specified, all non-moderator participants will be muted.  When
             * set to false, participants are once again allowed to mute and
             * unmute themselves.
             */
            virtual int setMuteLock(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool locked ) = 0;

            /**
             * Controls whether the conference is �hosted� or �unhosted�.
             * Unhosted conferences may start and continue without a host.
             * Hosted conferences will not start until a moderator joins, and
             * will immediately terminate when the last moderator leaves. 
             */
            virtual int setHosted(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool hosted ) = 0;

            /**
             * Performs a "network mute" on all participants currently in
             * the conference. Will not impact newly joining participants.
             * Setting the value to false will perform a network unmute. Note
             * that this is less of a state and more of a one-off action.
             */
            virtual int setMuteOthers(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool mute ) = 0;

            /**
             * Controls whether participant entry and exit tones are audible in
             * the conference.
             */
            virtual int setEntryExitTonesEnabled(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool enabled ) = 0;

            /**
             * Controls server-side recording of the conference (on/off).
             */
            virtual int setRecording(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool enabled ) = 0;

            /**
             * Toggles whether audio only is recorded (or whether it is
             * audio+video).  The default is audio+video. This is controlled
             * independently of the actual starting and stopping of the
             * recording.
             */
            virtual int setAudioOnlyRecording(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool enabled ) = 0;

            /**
             * When set to true, new participants will automatically join
             * the conference as muted. Note that mute Lock overrides this.
             */
            virtual int setJoinMuted(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool enabled ) = 0;

            /**
             * Forces one participant to be removed from the conference
             * (their leg of the call will be dropped).
             */
            virtual int kickParticipant(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant) = 0;

            /**
             * Sets the mute state on a single participant (either muted, or
             * unmuted)
             */
            virtual int muteParticipant(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant,
               bool mute) = 0;

            /**
             * Sets the recording state for a participant (note, this API is
             * generally called by the participants themselves instead of the
             * host, as opposed to muteParticipant for example which is the
             * other way around).
             */
            virtual int setIsRecording(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant,
               bool isRecording ) = 0;

            /**
             * Retrieves XMPP Account information from the VCCS, which allows
             * the application to configure an XMPP account which may be used
             * to chat with other people in any given conference (group chat).
             *
             * It is expected that this method will be used more for 'guest
             * VCCS' situations rather than 'home VCCS' situations (in which
             * the user's pre-created and configured XMPP account should be
             * good enough).
             *
             * The configuration will be returned in an event.
             */
            virtual int getXMPPAccountInfo(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               bool guestAccount = false ) = 0;

            /**
             * Called by the host/moderator: sets the
             * screensharing presenter for the session.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             * @param hParticipant the ID of the participant who is being
             *        selected as the presenter for the screen sharing session.
             */
            virtual int setScreenSharePresenter(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant ) = 0;

            /**
             * Called by the participant(s) after receiving a notification
             * to start the screen sharing session (called to accept the session).
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             * @param hParticipant the ID of the participant who is being
             *        selected as the presenter for the screen sharing session.
             * @param screenSharingURL a URL pointing to the screen-sharing
             *        server/resource
             * @param infoList a list of name/value pairs of information which
             *        will be passed to all the conference participants to
             *        enable them to connect to the screensharing session
             *        (which may require connecting to an outside server).
             */
            virtual int startScreenShare(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant,
               const cpc::string& screenSharingURL,
               const ScreenSharingInfoList& infoList ) = 0;

            /**
             * Called by either the host or participant which is sharing.
             * will result in the termination of any presently running
             * screen sharing session. Note that the protocol for the 
             * session itsef should also be closed at application level.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             * @param hParticipant the ID of the participant who is being
             *        selected as the presenter for the screen sharing session.
             */
            virtual int stopScreenShare(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant ) = 0;

            /**
             * No longer supported by the server since websocket API level 1006
             *
             * Set the frame rate of the mixer�s outgoing video, in frames per
             * second (multiples of 5, up to 30)
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            DEPRECATED virtual int setVideoFrameRate(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               int videoFrameRate ) = 0;

            /**
             * Assign the prominent video tile to the specified participant. 
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            virtual int setVideoFloorParticipant(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VccsConferenceParticipantHandle hParticipant ) = 0;

            /**
             * Change the mixer�s video layout to one of the several modes
             * available from the ConferenceDetails object.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            virtual int setVideoLayout(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VideoLayout videoLayoutType ) = 0;

            /**
             * Change the mixer�s video resolution to one of the several
             * available from the ConferenceDetails object.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            virtual int setVideoResolution(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               VideoResolution videoResolutionType) = 0;
            
            /**
             * Sends a message to the server, which will result in the
             * conference configuration being returned asynchronously
             * in an event. The config returned will be complete, in
             * that each field will be specified.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            virtual int queryConferenceConfig(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference) = 0;

            /**
             * Changes the specified conference configuration, using the
             * information provided in conferenceConfig, and passed to
             * the server depending on the flags in conferenceConfigSet.
             *
             * @param hAccount the VCCS account to use for the operation
             * @param hConference the conference in question
             */
            virtual int setConferenceConfig(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               const ConferenceConfiguration& conferenceConfig,
               const ConferenceConfigurationSet& conferenceConfigSet ) = 0;

            /**
             * Queries the server for the conference history list. Imagine this
             * functioning a bit like reddit (for lack of a better example).
             * Each "page" contains a number of articles (the "count"), and you
             * can step through the pages one at a time (the "offset"). In
             * order to drill down into the history, you can either query a
             * range using offset+count, or include a specific historyID, which
             * is an integer from the top of the list (the same set as offset
             * and count).
             *
             * If "includeParticipants" is set, the history will include even
             * more detailed information about all the participants and what
             * they did during the course of the conference.
             */
            virtual int queryConferenceHistory(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               int offset = 0,
               int count = 5,
               bool includeParticipants = false,
               HistoryID historyID = -1 ) = 0;

            /**
             * Deletes a list of history entries from the server. User must have
             * permission (being the conference owner) in order to do this.
             */
            virtual int deleteHistory(
               Account::VccsAccountHandle hAccount,
               VccsConferenceHandle hConference,
               const cpc::vector< HistoryID >& historyIDs ) = 0;

         protected:
            /*
             * The SDK will manage memory life of %VccsConferenceManager.
             */
            virtual ~VccsConferenceManager() {}
         };
      }
   }
}

#endif // CPCAPI2_VCCS_CONFERENCE_MANAGER_H
