#pragma once

#ifndef CPCAPI2_VCCSACCOUNT_SETTINGS_H
#define CPCAPI2_VCCSACCOUNT_SETTINGS_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <websocket/WebSocketTypes.h>
#include "VccsAccountTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Account
      {
         /**
          * A struct. Contains data used by the account; passed in
          * VccsAccount::create(); the data includes information that allows the
          * application to connect to the network.
          */
         struct VccsAccountSettings
         {
            /**
             * Settings which are used for the websocket
             */
            WebSocket::WebSocketSettings wsSettings;

            /**
             * Authentication group/domain parameter
             */
            cpc::string group;

            /**
             * Authentication username parameter
             */
            cpc::string userName;

            /**
             * Authentication password parameter
             */
            cpc::string password;

            /**
             * Your username (as you'd like it to be seen remotely).
             */
            cpc::string displayName;

            /**
             * XMPP username which is associated with the VCCS instance.
             */
            cpc::string xmppUserName;

            /**
            * SDK to send out a SUBSCRIBE after a network change
            * if enabled, do not manualy re-join the conference from the app layer
            * this setting should be deprecated after app layers
            * transition to behaviour described in CPR-389/OBELISK-5563
            */
            bool autoSubscribeAfterNetworkChange;

            VccsAccountSettings()
            {
               autoSubscribeAfterNetworkChange = false;
            };
         };
      }
   }
}
#endif // CPCAPI2_SIP_ACCOUNT_SETTINGS_H
