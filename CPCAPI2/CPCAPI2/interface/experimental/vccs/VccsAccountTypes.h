#pragma once
#ifndef __CPCAPI2_VCCS_ACCOUNT_TYPES_H__
#define __CPCAPI2_VCCS_ACCOUNT_TYPES_H__

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Account
      {
         typedef unsigned int VccsAccountHandle;

         typedef enum VccsAccountState
         {
            VccsAccountState_Unregistered,
            VccsAccountState_Registering,
            VccsAccountState_Registered,
            VccsAccountState_WaitingToRegister,
            VccsAccountState_Suspended
         } VccsAccountState;
      }
   }
}

#endif //  __CPCAPI2_VCCS_ACCOUNT_TYPES_H__
