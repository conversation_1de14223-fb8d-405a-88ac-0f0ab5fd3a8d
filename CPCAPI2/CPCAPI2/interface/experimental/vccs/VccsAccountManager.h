#pragma once

#ifndef CPCAPI2_VCCSACCOUNTMANAGER_H
#define CPCAPI2_VCCSACCOUNTMANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "VccsAccountTypes.h"

namespace CPCAPI2
{
   class Phone;

   namespace VCCS
   {
      namespace Account
      {
         class VccsAccountHandler;
         struct VccsAccountSettings;

         /**
          * Accounts can be in one of several states, depending on the connectivity
          * to the server. See %VccsAcccountState.
          *
          * Operations performed to the VCCS will only be successful if the account
          * is currently in the "Registered" state. Other states are provided to the
          * application in order to give feedback to the user.
          *                                                                             
          * +---------------------------------------------------------------------------------------+
          * |                                                                                       |
          * |                          disable                                                      |
          * | disable   +------------------------------------------------------+                    |
          * |           v                                                      |                    |
          * |         +--------------+  enable    +-------------+  success   +-------------------+  |
          * +-------> | Unregistered | ---------> |             | ---------> |    Registered     |  |
          *           +--------------+            |             |            +-------------------+  |
          *             ^              disable    |             |              |                    |
          *             +------------------------ | Registering |              | failure            |
          *                                       |             |              v                    |
          *                                       |             |  failure   +-------------------+  |
          *                                       |             | ---------> | WaitingToRegister | -+
          *                                       +-------------+            +-------------------+
          *                                         ^             timer        |
          *                                         +--------------------------+
          *
          * Above diagram generated using Graph::Easy CPAN Perl module with the
          * following input:
          *
          * [ Unregistered ] -- enable --> [ Registering ]
          * [ Registering ] -- success --> [ Registered ]
          * [ Registering ] -- failure --> [ WaitingToRegister ]
          * [ Registering ] -- disable --> [ Unregistered ]
          * [ Registered ] -- failure --> [ WaitingToRegister ]
          * [ Registered ] -- disable --> [ Unregistered ]
          * [ WaitingToRegister ] -- timer --> [ Registering ]
          * [ WaitingToRegister ] -- disable --> [ Unregistered ]
          */
         class CPCAPI2_SHAREDLIBRARY_API VccsAccountManager
         {
         public:
            /**
             * Get a reference to the VccsAccountManager interface.
             */
            static VccsAccountManager* getInterface(Phone* cpcPhone);

            /**
             * Create an account.
             */
            virtual VccsAccountHandle create() = 0;

            /**
             * Configure the default account settings.
             * Call configureDefaultAccountSettings() and then applySettings().
             *
             * @param account the account handle.
             * @param vccsAccountSettings the VccsAccountSettings that holds 
             *        configuration information for this account.
             */
            virtual int configureDefaultAccountSettings(
               VccsAccountHandle account,
               const VccsAccountSettings& vccsAccountSettings) = 0;

            /**
             * Apply the account settings configured with
             * configureDefaultAccountSettings(),
             * configureTransportAccountSettings()
             *
             * @param account the account handle
             */
            virtual int applySettings(VccsAccountHandle hAccount) = 0;

            virtual int decodeProvisioningResponse(const cpc::string & provisioningResponse, cpc::vector<Account::VccsAccountSettings>& outVccsAccountSettings) = 0;
            
            /**
             * Deletes an account, uses the handle obtained from create.
             */
            virtual int destroy(VccsAccountHandle hAccount) = 0;

            /**
             * Set the handler for events on the specified account. Set the
             * handler immediately after calling VccsAccountManager::create()
             * and VccsAccountManager::applySettings(..)
             *
             * To un-register the handler, pass NULL for handler. Must be
             * called on the same thread as process(..).
             *
             * NB: Calling setHandler with NULL as the handler will result in
             * the calling thread being blocked until the handler has been
             * removed from the system.
             */
            virtual int setHandler(
               VccsAccountHandle hAccount,
               VccsAccountHandler* handler) = 0;

            /**
             * Enable the specified account so that it can be used with the
             * CounterPath VCCS.
             *
             * You should use
             * CPCAPI2::VccsAccount::VccsAccountHandler::onAccountStateChanged()
             * to be notified of progress after invoking %enable().
             */
            virtual int enable(VccsAccountHandle hAccount) = 0;

            /**
             * Disable the specified account. You should use
             * CPCAPI2::VccsAccount::VccsAccountHandler::onAccountStateChanged()
             * to be notified of progress after invoking %disable().
             */
            virtual int disable(VccsAccountHandle hAccount) = 0;

            /**
             * Original (now deprecated) function for parsing the VCCS URL.
             * see method below for new parameter.
             */
            DEPRECATED virtual bool crackVCCSURL(
               const cpc::string& inURL,
               const bool&        secureWebSocketRequired,
               cpc::string&       outWebSocketURL,
               cpc::string&       outServerName,
               int&               outPortNumber,
               cpc::string&       outGroupName,
               cpc::string&       outSubscriptionCode ) = 0;

            /**
             * Returns information which is encoded inside of a VCCS URL (the
             * URL which is obtained generally from a VCCS e-mail or XMPP
             * invite)
             *
             * @return true if the URL could be parsed correctly.
             * @param inURL the VCCS URL under consideration
             * @param expectedScheme, normally "vccs", however this string is
             *        brandable and therefore needs to be controlled by the application.
             *        If the string is empty, "vccs" will be assumed.
             * @param secureWebSocketRequired if true, the outWebSocketURL will be
             *        returned using the "wss://" scheme instead of "ws://"
             * @param outWebSocketURL the web socket derived from the input URL
             * @param outServerName the name of the VCCS server contained in the URL
             * @param outPortNumber the port specified in the URL (or a default port
             *        number if nothing was supplied)
             * @param outGroupName the group/domain for the VCCS server
             * @param outSubscriptionCode a string which may be used to subscribe
             *        to the server directly (if present)
             */
            virtual bool crackVCCSURL(
               const cpc::string& inURL,
               const cpc::string& expectedScheme,
               const bool&        secureWebSocketRequired,
               cpc::string&       outWebSocketURL,
               cpc::string&       outServerName,
               int&               outPortNumber,
               cpc::string&       outGroupName,
               cpc::string&       outSubscriptionCode ) = 0;

            /**
             * Allows the connection to be torn down during inactive periods.
             * The application will have to call setSuspendable( false ) in
             * order to wake up the service again (or to avoid having it
             * automatically suspend). The recommended use of this mechanism is
             * in conjunction with the notification service in order to receive
             * any unsolicited notifications from the server, but this may not
             * be required depending on how the flag is used.
             */
            virtual int setSuspendable(
               VccsAccountHandle hAccount,
               bool isSuspendable ) = 0;

            /**
             * The blocking modes for the process() function. See that function
             * for details.
             */
            static const int kBlockingModeNonBlocking = -1;
            static const int kBlockingModeInfinite = 0;

            // constants
            static const int kAccountModuleDisabled = -1;

            /**
             * Allows the application code to receive callback notifications
             * from the SDK.
             *
             * These callbacks will happen synchronously, in the same thread of
             * execution as that in which %process() is invoked.  Depending on
             * the application threading model, process() can be used in two
             * different ways:
             * <ol>
             * <li>blocking mode ?Typically in this mode, %process() is called by the 
             * application from a background (worker) thread.  The call to process() 
             * blocks until a callback function needs to be invoked.
             * <li>non-blocking mode ?In this mode, %process() is called by the application 
             * from the main (GUI) thread of the application, typically from the 
             * main message/event loop.  In this mode, %process() returns immediately 
             * and so must be called frequently enough that the application can receive 
             * its callback notifications in a timely manner.
             *
             * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
             *                representing the time the call to process(..) will block waiting for a callback
             *                from the SDK
             * </ol>
             */
            virtual int process(unsigned int timeout) = 0;

            /**
             * Posts a functor to the SDK callback queue; this allows the
             * application to "unblock" the thread calling
             * VccsAccountManager::process(..) if it is blocked waiting for an
             * SDK callback.  Unblocking the process() thread is useful at
             * SDK/application shutdown in certain applications.
             */
            virtual void postToProcessThread(void (*pfun)(void*), void* obj) = 0;
            
         protected:
            /*
             * The SDK will manage memory life of %VccsAccountManager.
             */
            virtual ~VccsAccountManager() {}
         };
      }
   }
}
#endif // CPCAPI2_VCCSACCOUNTMANAGER_H
