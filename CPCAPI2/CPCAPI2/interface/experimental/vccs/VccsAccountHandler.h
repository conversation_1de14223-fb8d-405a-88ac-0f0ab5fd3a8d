#pragma once

#ifndef CPCAPI2_VCCSACCOUNT_HANDLER_H
#define CPCAPI2_VCCSACCOUNT_HANDLER_H

#include <cpcstl/string.h>
#include "VccsAccountTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Account
      {
         typedef enum VccsStateReason
         {
            VccsStateReason_None               = 0,
            VccsStateReason_InvalidCredentials = 4100,
            VccsStateReason_LockedOut          = 4101,
            VccsStateReason_UserGroupSuspended = 4102,
            VccsStateReason_GroupUnconfigured  = 4103,
            VccsStateReason_ModeratorKicked    = 4104
         } VccsStateReason;

         /**
          * VccsAccountStateChangedEvent, encapsultes the state transition
          * by saying which state transitioned to which other state.
          */
         typedef struct VccsAccountStateChangedEvent
         {
            VccsAccountState oldState; // the 'from' state
            VccsAccountState newState; // the 'to' state
            VccsStateReason  reason; // Optional additional information
         } VccsAccountStateChangedEvent;

         /**
          * Event passed in VccsAccountHandler::onError().
          */
         struct ErrorEvent
         {
            cpc::string errorText;
         };

         /**
          * The handler for events on VccsAccount; passed in VccsAccount::setHandler().
          */
         class VccsAccountHandler
         {
         public:
            /**
             * Notifies the application when the %VccsAccount has changed to a
             * specific state.
             */
            virtual int onAccountStateChanged(VccsAccountHandle account, const VccsAccountStateChangedEvent& args) = 0;

            /**
             * Notifies the application when an account error has occurred
             */
            virtual int onError(VccsAccountHandle account, const ErrorEvent& args) = 0;
         };
      }
   }
}
#endif // CPCAPI2_VCCSACCOUNT_HANDLER_H
