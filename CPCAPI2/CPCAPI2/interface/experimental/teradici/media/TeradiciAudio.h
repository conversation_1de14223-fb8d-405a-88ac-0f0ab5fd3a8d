#pragma once

#if !defined(CPCAPI2_TERADICI_AUDIO_H)
#define CPCAPI2_TERADICI_AUDIO_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "TeradiciAudioHandler.h"

namespace CPCAPI2
{
namespace Media
{
class MediaManager;
typedef unsigned int PlaySoundHandle;

/**
* Manager interface to control audio and audio devices; 
* get a reference to the interface using the static method getInterface().
* Provides audio device enumeration, selection, and control-related features.
*/
class CPCAPI2_SHAREDLIBRARY_API TeradiciAudio
{
public:
   /**
   * Get a reference to the %Audio interface.
   */   
   static TeradiciAudio* getInterface(MediaManager* cpcMediaManager);
   
   /**
   * Initializes the HID related functionality.
   * Must be called after MediaManager::initializeMediaStack(..)
   **/
   virtual int initializeHid() = 0;

   /**
   * Sets the callback handler for audio related events.
   */
   virtual int setHandler(TeradiciAudioHandler* handler) = 0;

   /**
    * Queries the list of audio capture and playout devices available on the system.
    * The set of devices reported is dependent on OS support.
    * See AudioHandler::onAudioDeviceListUpdated(..)  for the results of the query.
    */
   virtual int queryDeviceList() = 0;

   /**
    * Selects the audio capture/render device used for the voice path during an audio call.
    * The deviceId corresponds to the AudioDeviceInfo::id returned by calling queryDeviceList().
    */
   virtual int setCaptureDevice(unsigned int deviceId, TeradiciAudioDeviceRole role) = 0;
   virtual int setRenderDevice(unsigned int deviceId, TeradiciAudioDeviceRole role) = 0;

   /**
    * Plays the specified sound on the specified audio render device.
    * The resourceUri specifies the location of the audio data to be played.
    * 
    * Supported URI formats:
    *
    * DTMF tones, e.g. tone:n;duration=ms where n is 0..15 and ms is the duration in milliseconds
    * Resource URIs, e.g. ms-appx:///Assets/ALERT_asterik.wav
    *
    * Windows 8 'Windows Store' Apps - Resource URIs must be in the format ms-appx:///Assets/ALERT_asterik.wav
    */
   virtual PlaySoundHandle playSound(TeradiciAudioDeviceRole role, const cpc::string& resourceUri, bool repeat = false) = 0;

   /**
    * Stop playing the specified sound.  Note that it is only necessary to call this if you wish to prematurely stop
    * the playing sound, or if you wish to stop playback when playSound was called with 'repeat' set to 'true'.
    */
   virtual int stopPlaySound(PlaySoundHandle sound) = 0;

   /**
    * Queries the list of audio codecs available in this version of the SDK.
    * The set of codecs reported is dependant on OS support.
    * See AudioHandler::onAudioCodecListUpdated(..) for the results of the query.
    */
   virtual int queryCodecList() = 0;

   /**
    * Enables or disables a codec.  Changes take effect for subsequent new incoming/outgoing
    * calls.
    * @param codecId The id of the codec returned by AudioHandler::onAudioCodecListUpdated(..)
    * @param enabled True to enable the codec, false to disable
    */
   virtual int setCodecEnabled(unsigned int codecId, bool enabled) = 0;

   /**
    * Sets the priority of a codec. Higher value indicates a higher priority codec which should be used if possible
    * @param codecId The id of the codec returned by #AudioHandler::onAudioCodecListUpdated(..)
    * @param priority The priority of the codec. Higher value = greater priority to be used
    */
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) = 0;

   /**
    * Mutes or unmutes the active microphone device.
    */
   virtual int setMicMute(bool enabled) = 0;

   /**
    * Mutes or unmutes the active speaker device.
    */
   virtual int setSpeakerMute(bool enabled) = 0;

   /**
    * Sets the active microphone device volume.
    * @param level The desired level in the range 0 ... 100
    */
   virtual int setMicVolume(unsigned int level) = 0;

   /**
    * Sets the active speaker device volume.
    * @param level The desired level in the range 0 ... 100
    */
   virtual int setSpeakerVolume(unsigned int level) = 0;

   /**
    * Queries the current status of microphone and speaker mute and volume levels.
    * See AudioHandler::onAudioDeviceVolume(..) for the results of the query.
    */
   virtual int queryDeviceVolume() = 0;

   virtual int setEchoCancellationMode(TeradiciAudioDeviceRole role, TeradiciEchoCancellationMode mode) = 0;
   virtual int setNoiseSuppressionMode(TeradiciAudioDeviceRole role, TeradiciNoiseSuppressionMode mode) = 0;


   //// hid-ish related ////

   virtual int queryOnHook() = 0;

   virtual int setOnHook(bool onHook) = 0;
   virtual int setRinging(bool ringingOn) = 0;

};

}
}
#endif // CPCAPI2_TERADICI_AUDIO_H
