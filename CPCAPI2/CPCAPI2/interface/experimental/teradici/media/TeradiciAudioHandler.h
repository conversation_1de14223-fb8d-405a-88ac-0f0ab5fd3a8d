#pragma once

#if !defined(CPCAPI2_TERADICI_AUDIO_HANDLER_H)
#define CPCAPI2_TERADICI_AUDIO_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace Media
{
typedef unsigned int PlaySoundHandle;

enum TeradiciAudioDeviceRole
{
   TeradiciAudioDeviceRole_None          = 0,
   TeradiciAudioDeviceRole_Headset       = 1,
   TeradiciAudioDeviceRole_SpeakerPhone  = 2
};

enum TeradiciAudioDeviceType
{
   TeradiciMediaDeviceType_Capture       = 1,
   TeradiciMediaDeviceType_Render        = 2
};

enum TeradiciEchoCancellationMode
{
   TeradiciEchoCancellationMode_None                            = 0,
   TeradiciEchoCancellationMode_AggressiveEchoCancellation      = 1,
   TeradiciEchoCancellationMode_DefaultEchoCancellation         = 2,
   TeradiciEchoCancellationMode_LoudSpeakerphoneEchoSuppression = 3,
   TeradiciEchoCancellationMode_SpeakerphoneEchoSuppression     = 4,
   TeradiciEchoCancellationMode_LoudEarpieceEchoSuppression     = 5,
   TeradiciEchoCancellationMode_EarpieceEchoSuppression         = 6
};

enum TeradiciNoiseSuppressionMode
{
   TeradiciNoiseSuppressionMode_None                             = 0,
   TeradiciNoiseSuppressionMode_Low                              = 1,
   TeradiciNoiseSuppressionMode_Moderate                         = 2,
   TeradiciNoiseSuppressionMode_High                             = 3,
   TeradiciNoiseSuppressionMode_VeryHigh                         = 4
};

/**
* A struct. 
*/ 
struct TeradiciAudioDeviceInfo
{
   cpc::string      friendlyName;
   unsigned int      id;
   TeradiciAudioDeviceRole   role;
   TeradiciAudioDeviceType   deviceType;

   bool internalVolumeSupported;
   bool internalRingSupported;
   bool internalMuteSupported;
};

/**
* Event passed in AudioHandler::onAudioDeviceListUpdated().
*/
struct TeradiciAudioDeviceListUpdatedEvent
{
   cpc::vector<TeradiciAudioDeviceInfo> deviceInfo;
};

/**
* A struct. 
*/ 
struct TeradiciAudioCodecInfo
{
   cpc::string      codecName;
   unsigned int      id;
   bool              enabled;
   unsigned int      samplingRate;
   unsigned int      minBandwidth;
   unsigned int      maxBandwidth;
   unsigned int      priority;
};

/**
* Event passed in AudioHandler::onAudioCodecListUpdated().
*/
struct TeradiciAudioCodecListUpdatedEvent
{
   cpc::vector<TeradiciAudioCodecInfo> codecInfo;
};

/**
* Event passed in AudioHandler::onAudioDeviceVolume().
*/
struct TeradiciAudioDeviceVolumeEvent
{
   bool              micMuted;
   bool              speakerMuted;
   unsigned int      micVolumeLevel;
   unsigned int      speakerVolumeLevel;
};


enum TeradiciAudioDeviceKeyPressType
{
   TeradiciAudioDeviceKeyPressType_OnHook,
   TeradiciAudioDeviceKeyPressType_OffHook,
   TeradiciAudioDeviceKeyPressType_OnMute,
   TeradiciAudioDeviceKeyPressType_OffMute,
   TeradiciAudioDeviceKeyPressType_VolumeUp,
   TeradiciAudioDeviceKeyPressType_VolumeDown,
   TeradiciAudioDeviceKeyPressType_Flash,
   TeradiciAudioDeviceKeyPressType_Other
};

struct TeradiciAudioDeviceKeyPressEvent
{
   TeradiciAudioDeviceKeyPressType keyPressed;
};

struct TeradiciAudioDeviceHookStateEvent
{
  bool                onHook;
};

/**
* Handler for events relating to audio and audio devices; 
* set in TeradiciAudio::setHandler().
*/
class TeradiciAudioHandler
{
public:
   /**
    * Called when the list of media devices on the system changes, or as a result of
    * calling Audio::queryDeviceList().
    */
   virtual int onAudioDeviceListUpdated(const TeradiciAudioDeviceListUpdatedEvent& args) = 0;

   /**
    * Called when the audio specified in the call to MediaManager::playSound(..) has 
    * successfully finished playing.
    */
   virtual int onPlaySoundComplete(PlaySoundHandle soundClip) = 0;

   /**
    * Called when the audio specified in the call to MediaManager::playSound(..) could
    * not be played.  Possible reasons include:
    *  - resource specified by resourceUri not found
    */
   virtual int onPlaySoundFailure(PlaySoundHandle soundClip) = 0;

   /**
    * Called as a result of calling Audio::queryCodecList().
    */
   virtual int onAudioCodecListUpdated(const TeradiciAudioCodecListUpdatedEvent& args) = 0;

   /**
    * Called as a result of calling Audio::queryDeviceVolume().
    */
   virtual int onAudioDeviceVolume(const TeradiciAudioDeviceVolumeEvent& args) = 0;

   /**
    * Called as a result of a physical key press on the USB audio device
    */
   virtual int onAudioDeviceKeyPress(const TeradiciAudioDeviceKeyPressEvent& args) = 0;

   /**
    * Called as a result of calling TeradiciAudio::queryOnHook  
    */
   virtual int onAudioDeviceOnHookQueryResult(const TeradiciAudioDeviceHookStateEvent& args) = 0;
};

}
}
#endif // CPCAPI2_AUDIO_HANDLER_H
