#pragma once

#if !defined (CPCAPI2_TERADICI_LOGGER_INTERFACE_H)
#define CPCAPI2_TERADICI_LOGGER_INTERFACE_H

#include "cpcapi2defs.h"



namespace CPCAPI2
{

class Phone;

namespace Teradici
{

class TeradiciLoggerHandler;

class CPCAPI2_SHAREDLIBRARY_API TeradiciLoggerInterface
{
public:
   static TeradiciLoggerInterface* getInterface(Phone* cpcPhone);

   virtual int startLogging(bool verbose = false) = 0;
   virtual int stopLogging() = 0;
   virtual int requestZeroClientNetworkInfo() = 0;

   virtual int setHandler(TeradiciLoggerHandler* handler) = 0;
};

}
}

#endif // CPCAPI2_TERADICI_LOGGER_INTERFACE_H