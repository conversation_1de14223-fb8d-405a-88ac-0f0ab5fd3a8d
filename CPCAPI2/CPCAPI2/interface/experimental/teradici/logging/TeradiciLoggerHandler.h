#pragma once

#if !defined(CPCAPI2_TERADICI_LOGGER_HANDLER_H)
#define CPCAPI2_TERADICI_LOGGER_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace Teradici
{


enum ZeroClientLogLevel
{
   ZeroClientLogLevel_Default,
   ZeroClientLogLevel_Enhanced
};

class TeradiciLoggerHandler
{
public:
  virtual int onZeroClientLogLevelChanged(ZeroClientLogLevel newLevel) = 0;
  virtual int onLogMessageForAppLog(cpc::string logMessage) = 0;

};

}

}



#endif // CPCAPI2_TERADICI_LOGGER_HANDLER_H
