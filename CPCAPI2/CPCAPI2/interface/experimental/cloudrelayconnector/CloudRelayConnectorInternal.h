#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERNAL_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERNAL_H

#include "cpcapi2defs.h"
#include "CloudRelayConnector.h"

namespace CPCAPI2
{
namespace CloudRelayConnector
{
   class CPCAPI2_SHAREDLIBRARY_API CloudRelayConnectorInternal : public CloudRelayConnector
   {
   public:
      static CloudRelayConnectorInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
   };  
}
}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_INTERNAL_H
