#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "CloudRelayConnectorTypes.h"

namespace CPCAPI2
{
class Phone;
namespace CloudRelayConnector
{
class CPCAPI2_SHAREDLIBRARY_API CloudRelayConnector
{
public:
   /**
   * Get a reference to the %CloudRelayConnector interface.
   */   
   static CloudRelayConnector* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kCloudRelayConnectorModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling CloudRelayConnector::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};
   
   /**
   * Allocates a handle to control a client's interactions with a set of server-hosted
   * back-end services that work together as a system (i.e. a "cloud").
   */
   virtual CloudRelayConnectorHandle createCloudRelayConnector() = 0;

   virtual int setHandler(CloudRelayConnectorHandle conn, CloudRelayConnectorHandler* handler) = 0;

   virtual int destroyCloudRelayConnector(CloudRelayConnectorHandle conn) = 0;

   /**
    * Sets connection settings; required before any cloud-based services can be requested/accessed.
    */
   virtual int setConnectionSettings(CloudRelayConnectorHandle conn, const CloudRelayConnectorSettings& settings) = 0;

   /**
    * Connect to the back-end services requested by prior calls to requestService(..).
    * The CloudRelayConnector follows this sequence:
    * 1) request an auth token from the authentication server (per username/password and URL provided in setConnectionSettings(..))
    * 2) use the auth token to request back-end service URLs from the orchestration server
    * 3) connect to the URLs provided by the orchestration server
    * 
    * Progress is reported via the CloudRelayConnectorHandler::onServiceConnectionStatusChanged(..) callback function.
    */
   virtual int connectToCloudRelay(CloudRelayConnectorHandle conn) = 0;
   virtual int disconnect(CloudRelayConnectorHandle conn) = 0;

   virtual int broadcast(CloudRelayConnectorHandle conn, const cpc::string& msg) = 0;
   virtual int sendTo(CloudRelayConnectorHandle conn, CloudRelayEndpointId destinationId, const cpc::string& msg) = 0;
};

}

}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_H
