#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_HANDLER_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "CloudRelayConnectorTypes.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{
namespace CloudRelayConnector
{
struct ServiceConnectionStatusEvent
{
   cpc::string serverUri;
   ServiceConnectionStatus connectionStatus;
   cpc::string statusDesc;
   CPCAPI2::JsonApi::AuthToken authToken;
};

struct BroadcastEvent
{
   CloudRelayConnectorHandle relay;
   CloudRelayEndpointId senderId;
   cpc::string msg;
};

struct MessageEvent
{
   CloudRelayConnectorHandle relay;
   CloudRelayEndpointId senderId;
   cpc::string msg;
};

class CloudRelayConnectorHandler
{
public:
   virtual ~CloudRelayConnectorHandler() {}
   virtual int onServiceConnectionStatusChanged(CloudRelayConnectorHandle conn, const ServiceConnectionStatusEvent& args) = 0;
   virtual int onBroadcast(CloudRelayConnectorHandle conn, const BroadcastEvent& args) = 0;
   virtual int onMessage(CloudRelayConnectorHandle conn, const MessageEvent& args) = 0;
};
   
}
   
}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_HANDLER_H
