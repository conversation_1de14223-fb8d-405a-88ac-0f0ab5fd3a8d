#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_CONNECTOR_TYPES_H)
#define CPCAPI2_CLOUD_RELAY_CONNECTOR_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace CloudRelayConnector
{
typedef unsigned int CloudRelayConnectorHandle;
typedef unsigned int CloudRelayEndpointId;

class CloudRelayConnectorHandler;

struct CloudRelayConnectorSettings
{
   cpc::string username;
   cpc::string password;
   cpc::string regionCode;
   cpc::string authServerUrl;
   cpc::string authServerApiKey;
   cpc::string orchestrationServerUrl;
   bool ignoreCertVerification;
   
   CloudRelayConnectorSettings() : ignoreCertVerification( false ) {}
};

enum ServiceConnectionStatus
{
   ServiceConnectionStatus_Disconnecting = 0,
   ServiceConnectionStatus_Disconnected = 1,
   ServiceConnectionStatus_Connecting = 2,
   ServiceConnectionStatus_Authenticating = 3,
   ServiceConnectionStatus_Connected = 4,
   ServiceConnectionStatus_ConnFailure = 5,
   ServiceConnectionStatus_AuthFailure = 6
};

}
}

#endif // CPCAPI2_CLOUD_RELAY_CONNECTOR_TYPES_H
