#pragma once

#if !defined(CPCAPI2_SIP_MWI_JSON_API_H)
#define CPCAPI2_SIP_MWI_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace SipMessageWaitingIndication
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API SipMWIJsonApi
{
public:
   /**
   * Get a reference to the %SipMWIJsonApi interface.
   */
   static SipMWIJsonApi* getInterface(Phone* cpcPhone);
};

}
}

#endif // CPCAPI2_SIP_MWI_JSON_API_H
