#pragma once

#ifndef CPCAPI2_BROADOFT_XSI_HANDLER_H
#define CPCAPI2_BROADOFT_XSI_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "BroadsoftXsiTypes.h"
#include <stdint.h>

namespace CPCAPI2
{
   class Phone;

   namespace BroadsoftXsi
   {
      struct QueryServicesSuccessEvent
      {
         cpc::vector<Service> services;
      };

      /**
      * Event which is fired when the server returns call logs.
      */
      typedef struct QueryCallLogsSuccessEvent
      {
         cpc::vector<CallLog> callLogs;
         bool enhanced;
      } QueryCallLogsSuccessEvent;

      /**
      * Event which is fired when the call logs are successfully deleted.
      */
      typedef struct DeleteCallLogsSuccessEvent
      {
      } DeleteCallLogsSuccessEvent;

      /**
      * Event which is fired when the call log is successfully deleted.
      */
      typedef struct DeleteCallLogSuccessEvent
      {
         cpc::string callLogId;
      } DeleteCallLogSuccessEvent;

      /**
      * Event which is fired when the server returns the enterprise directory.
      */
      typedef struct QueryEnterpriseDirectorySuccessEvent
      {
         cpc::vector<DirectoryDetail> details;
         bool moreResultsAvailable;

         QueryEnterpriseDirectorySuccessEvent()
         {
            moreResultsAvailable = false;
         }
      } QueryEnterpriseDirectorySuccessEvent;

      /**
      * Event which is fired when the server returns the Broadworks Anywhere service settings.
      */
      typedef struct QueryBroadworksAnywhereSettingsSuccessEvent
      {
         BroadworksAnywhere settings;
      } QueryBroadworksAnywhereSettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Call Forward Always service settings.
      */
      typedef struct QueryCallForwardAlwaysSettingsSuccessEvent
      {
         CallForwardAlways settings;
      } QueryCallForwardAlwaysSettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Call Forward Busy service settings.
      */
      typedef struct QueryCallForwardBusySettingsSuccessEvent
      {
         CallForwardBusy settings;
      } QueryCallForwardBusySettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Call Forward No Answer service settings.
      */
      typedef struct QueryCallForwardNoAnswerSettingsSuccessEvent
      {
         CallForwardNoAnswer settings;
      } QueryCallForwardNoAnswerSettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Do Not Disturb service settings.
      */
      typedef struct QueryDoNotDisturbSettingsSuccessEvent
      {
         DoNotDisturb settings;
      } QueryDoNotDisturbSettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Remote Office service settings.
      */
      typedef struct QueryRemoteOfficeSettingsSuccessEvent
      {
         RemoteOffice settings;
      } QueryRemoteOfficeSettingsSuccessEvent;

      /**
      * Event which is fired when the server returns the Simultaneous Ring service settings.
      */
      typedef struct QuerySimultaneousRingSettingsSuccessEvent
      {
         SimultaneousRing settings;
      } QuerySimultaneousRingSettingsSuccessEvent;

      /**
      * Event which is fired when the service settings are successfully set.
      */
      typedef struct SetServiceSettingsSuccessEvent
      {
         ServiceType serviceType;
      } SetServiceSettingsSuccessEvent;
   
   /**
   * Event which is fired when the call log is successfully deleted.
   */
   typedef struct CreateCallSuccessEvent
   {
      cpc::string callId;
      cpc::string externalTrackingId;
   } CreateCallSuccessEvent;

      /**
       * A bit more detail about what kind of failure has happened
       */
      typedef enum BroadsoftXsiFailureReason
      {
         BroadsoftXsiFailureReason_None,      // No error (default case)
         BroadsoftXsiFailureReason_Resolve,   // DNS resolution failure
         BroadsoftXsiFailureReason_Connect,   // Server unreachable
         BroadsoftXsiFailureReason_Cert,      // Cert verification failed
         BroadsoftXsiFailureReason_Auth,      // User authentication failed on server
         BroadsoftXsiFailureReason_HTTP,      // HTTP Error
         BroadsoftXsiFailureReason_Server,    // Server or request error
         BroadsoftXsiFailureReason_Malformed  // Bad response data
      } BroadsoftXsiFailureReason;

      /**
       * Event which is fired when there is a failure while trying to communicate
       * with the Broadsoft XSI server.
       */
      typedef struct BroadsoftXsiFailureEvent
      {
         BroadsoftXsiFailureReason failureReason;

         /**
          * If failureReason is BroadsoftXsiFailure_HTTP, the matching response code.
          * This code will be zero if failureReason is anything else.
          */
         unsigned int httpResponseCode;

         /**
          * If failureReason is BroadsoftXsiFailureReason_Server, the message
          * received from server indicating failure reason. Otherwise, it
          * might be set to provide an error determined internally.
          */
         cpc::string serverErrorMessage;
        
         /**
         * contains the text of the error either from the server or SDK generated
         */
         cpc::string errorMessageText;

         // Only set if callback method is for a service settings request
         // Used as work around to avoid adjust callback framework
         ServiceType serviceType;

         BroadsoftXsiFailureEvent() : failureReason(BroadsoftXsiFailureReason_None), httpResponseCode(0) {} // ctor
      } BroadsoftXsiFailureEvent;

      /**
      * Event passed in BroadsoftXsiHandler::onError().
      */
      typedef struct ErrorEvent
      {
         int errNo; // as defined in errno.h
         cpc::string errorText;

         ErrorEvent() : errNo(0) {} // default ctor
      } ErrorEvent;

      /**
       * The handler for events on the Broadsoft XSI Service
       */
      class BroadsoftXsiHandler
      {
      public:
         virtual int onQueryServices(const BroadsoftXsiHandle& c, const QueryServicesSuccessEvent&) = 0;
         virtual int onQueryServicesFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onQueryCallLogs(const BroadsoftXsiHandle& c, const QueryCallLogsSuccessEvent& e) = 0;
         virtual int onQueryCallLogsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onDeleteCallLogsSuccess(const BroadsoftXsiHandle& c, const DeleteCallLogsSuccessEvent& e) = 0;
         virtual int onDeleteCallLogsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onDeleteCallLogSuccess(const BroadsoftXsiHandle& c, const DeleteCallLogSuccessEvent& e) = 0;
         virtual int onDeleteCallLogFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onQueryEnterpriseDirectory(const BroadsoftXsiHandle& c, const QueryEnterpriseDirectorySuccessEvent& e) = 0;
         virtual int onQueryEnterpriseDirectoryFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onQueryBroadworksAnywhereSettings(const BroadsoftXsiHandle& c, const QueryBroadworksAnywhereSettingsSuccessEvent& e) = 0;
         virtual int onQueryCallForwardAlwaysSettings(const BroadsoftXsiHandle& c, const QueryCallForwardAlwaysSettingsSuccessEvent& e) = 0;
         virtual int onQueryCallForwardBusySettings(const BroadsoftXsiHandle& c, const QueryCallForwardBusySettingsSuccessEvent& e) = 0;
         virtual int onQueryCallForwardNoAnswerSettings(const BroadsoftXsiHandle& c, const QueryCallForwardNoAnswerSettingsSuccessEvent& e) = 0;
         virtual int onQueryDoNotDisturbSettings(const BroadsoftXsiHandle& c, const QueryDoNotDisturbSettingsSuccessEvent& e) = 0;
         virtual int onQueryRemoteOfficeSettings(const BroadsoftXsiHandle& c, const QueryRemoteOfficeSettingsSuccessEvent& e) = 0;
         virtual int onQuerySimultaneousRingSettings(const BroadsoftXsiHandle& c, const QuerySimultaneousRingSettingsSuccessEvent& e) = 0;
         virtual int onQueryServiceSettingsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onSetServiceSettings(const BroadsoftXsiHandle& c, const SetServiceSettingsSuccessEvent& e) = 0;
         virtual int onSetServiceSettingsFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onCreateCallSuccess(const BroadsoftXsiHandle& c, const CreateCallSuccessEvent& e) = 0;
         virtual int onCreateCallFailure(const BroadsoftXsiHandle& c, const BroadsoftXsiFailureEvent& e) = 0;
         virtual int onError(const BroadsoftXsiHandle& c, const ErrorEvent& e) = 0;
      };
   }
}
#endif // CPCAPI2_BROADOFT_XSI_HANDLER_H
