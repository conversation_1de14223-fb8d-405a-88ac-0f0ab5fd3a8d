#pragma once

#ifndef CPCAPI2_BROADSOFT_XSI_H
#define CPCAPI2_BROADSOFT_XSI_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "BroadsoftXsiTypes.h"
#include "BroadsoftXsiHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace BroadsoftXsi
   {
      /**
       * The Broadsoft XSI module is used to communicate with Broadsoft XSI
       * servers. Initial implementation to support call history, contact
       * directories and system settings.
       */
      class CPCAPI2_SHAREDLIBRARY_API BroadsoftXsi
      {
      public:
         /**
          * Get a reference to the Broadsoft XSI interface.
          */
         static BroadsoftXsi* getInterface(Phone* cpcPhone);

         /**
          * Creates a connection to communicate with the Broadsoft XSI server
          */
         virtual BroadsoftXsiHandle create(void) = 0;

         /**
          * Configure the default connection settings.
          * Call configureSettings() and then applySettings().
          *
          * @param connection the connection handle.
          * @param settings the structure that holds configuration information
          *                 for the Broadsoft XSI connection.
          */
         virtual int configureSettings(
            const BroadsoftXsiHandle& connection,
            const LocalSettings& settings) = 0;

         /**
          * Apply the connection settings configured with configureSettings()
          *
          * @param connection the connection handle
          */
         virtual int applySettings( const BroadsoftXsiHandle& connection ) = 0;

         /**
          * Retrieve all assigned services.
          */
         virtual int queryServices(const BroadsoftXsiHandle& connection) = 0;

         /**
          * Query the call logs from the server.
          */
         virtual int queryCallLogs( const BroadsoftXsiHandle& connection,
            const CallLogType& callLogType = CallLogType_All ) = 0;

         /**
         * Delete call logs from the server.
         */
         virtual int deleteCallLogs( const BroadsoftXsiHandle& connection,
            const CallLogType& callLogType = CallLogType_All ) = 0;

         /**
         * Delete a specific call log from the server. Due to a limitation of
         * XSI a call log type must be provided. (Missed, Placed or Received)
         */
         virtual int deleteCallLog( const BroadsoftXsiHandle& connection,
            const CallLogType& callLogType, const cpc::string& callLogId) = 0;

         /**
         * Query the enterprise directory from the server.
         * Search parameters are wildcarded.
         */
         virtual int queryEnterpriseDirectory( const BroadsoftXsiHandle& connection, const DirectorySearchParameters& searchParameters ) = 0;

         /**
         * Query the service settings currently on the Broadsoft XSI server.
         */
         virtual int queryServiceSettings( const BroadsoftXsiHandle& connection, const ServiceType ) = 0;

         /**
         * Set the settings for the Broadworks Anywhere service on the Broadsoft XSI server.
         */
         virtual int setBroadworksAnywhereSettings(const BroadsoftXsiHandle& connection, const BroadworksAnywhere&) = 0;

         /**
         * Set the settings for the Call Forward Always service on the Broadsoft XSI server.
         */
         virtual int setCallForwardAlwaysSettings(const BroadsoftXsiHandle& connection, const CallForwardAlways&) = 0;

         /**
         * Set the settings for the Call Forward Busy service on the Broadsoft XSI server.
         */
         virtual int setCallForwardBusySettings(const BroadsoftXsiHandle& connection, const CallForwardBusy&) = 0;

         /**
         * Set the settings for the Call Forward No Answer service on the Broadsoft XSI server.
         */
         virtual int setCallForwardNoAnswerSettings(const BroadsoftXsiHandle& connection, const CallForwardNoAnswer&) = 0;

         /**
         * Set the settings for the Do Not Disturb service on the Broadsoft XSI server.
         */
         virtual int setDoNotDisturbSettings(const BroadsoftXsiHandle& connection, const DoNotDisturb&) = 0;

         /**
         * Set the settings for the Remote Office service on the Broadsoft XSI server.
         */
         virtual int setRemoteOfficeSettings(const BroadsoftXsiHandle& connection, const RemoteOffice&) = 0;
         
         /**
         * TODO
         */
         virtual int createCall( const BroadsoftXsiHandle& connection, const cpc::string& number ) = 0;

         /**
         * Set the settings for the Simultaneous Ring service on the Broadsoft XSI server.
         */
         virtual int setSimultaneousRingSettings(const BroadsoftXsiHandle& connection, const SimultaneousRing&) = 0;

         /**
          * Deletes a Broadsoft XSI connection, uses the handle obtained from create.
          */
         virtual int destroy( const BroadsoftXsiHandle& connection ) = 0;

         /**
          * Set the handler for events on the specified connection. Set the
          * handler immediately after calling BroadsoftXsi::create()
          * and BroadsoftXsi::applySettings(..)
          *
          * To un-register the handler, pass NULL for handler. Must be
          * called on the same thread as process(..).
          *
          * NB: Calling setHandler with NULL as the handler will result in
          * the calling thread being blocked until the handler has been
          * removed from the system.
          */
         virtual int setHandler( const BroadsoftXsiHandle& connection,
            BroadsoftXsiHandler* handler) = 0;

         /**
          * The blocking modes for the process() function. See that function
          * for details.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Allows the application code to receive callback notifications
          * from the SDK.
          *
          * These callbacks will happen synchronously, in the same thread of
          * execution as that in which %process() is invoked.  Depending on the
          * application threading model, process() can be used in two different
          * ways:
          * <ol>
          * <li>blocking mode ?Typically in this mode, %process() is called by
          * the application from a background (worker) thread.  The call to
          * process() blocks until a callback function needs to be invoked.
          * <li>non-blocking mode ?In this mode, %process() is called by the
          * application from the main (GUI) thread of the application,
          * typically from the main message/event loop.  In this mode,
          * %process() returns immediately and so must be called frequently
          * enough that the application can receive its callback notifications
          * in a timely manner.
          *
          * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
          *                representing the time the call to process(..) will block waiting for a callback
          *                from the SDK
          * </ol>
          */
         virtual int process(unsigned int timeout) = 0;

         /**
          * Posts a functor to the SDK callback queue; this allows the
          * application to "unblock" the thread calling
          * BroadsoftXsi::process(..) if it is blocked waiting for an
          * SDK callback.  Unblocking the process() thread is useful at
          * SDK/application shutdown in certain applications.
          */
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %BroadsoftXsi.
          */
         virtual ~BroadsoftXsi() {}
      };
   }
}
#endif // CPCAPI2_BROADSOFT_XSI_H
