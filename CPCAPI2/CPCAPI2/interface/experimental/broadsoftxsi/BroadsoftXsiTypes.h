#pragma once
#ifndef __CPCAPI2_BROADSOFT_XSI_TYPES_H__
#define __CPCAPI2_BROADSOFT_XSI_TYPES_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
   namespace BroadsoftXsi
   {
      typedef unsigned int BroadsoftXsiHandle;

      /**
       * State of connection to Broadsoft XSI server. Not used at the moment. Might be used if we implement polling.
       */
      //typedef enum ConnectionState
      //{
      //   ConnectionState_Disconnected, // obvs.
      //   ConnectionState_Connecting,   // trying to connect
      //   ConnectionState_Connected,    // succeeded connecting
      //   ConnectionState_Failed        // connection failed (will either retry or disconnect)
      //} ConnectionState;

      /**
       * Structure containing all relevant settings for the connection.
       * (subdivided into "info" blocks).
       */
      typedef struct LocalSettings
      {
         cpc::string userName; // e.g. <EMAIL>
         cpc::string password;
         cpc::string domain; // The domain:port portion of the XSI URL e.g. xsp1.iop1.broadworks.net
         bool useHttps;
         cpc::string cookieFile; // Won't use if empty string

         /**
          * If set to true the XML protocol content will be written to the
          * logfile at "Info" level. Default set to false.
          */
         bool logXMLPayload;

         bool logParsedResponse;

         // Default ctor with reasonable values
         LocalSettings()
         {
            useHttps = true;
            logXMLPayload = false;
         }
      } LocalSettings;


      /**
      * Service type
      */
      typedef enum ServiceType
      {
         ServiceType_BroadworksAnywhere,
         ServiceType_CallForwardAlways,
         ServiceType_CallForwardBusy,
         ServiceType_CallForwardNoAnswer,
         ServiceType_DoNotDisturb,
         ServiceType_RemoteOffice,
         ServiceType_SimultaneousRing
      } ServiceType;

      struct Service
      {
         cpc::string name;
         cpc::string url;
      };

      /**
      * Broadworks Anywhere Location configuration
      */
      typedef struct BroadworksAnywhereLocation
      {
         bool active;
         cpc::string phoneNumber;
         cpc::string description;

         BroadworksAnywhereLocation()
         {
            active = false;
         }

         void reset()
         {
            active = false;
            phoneNumber = "";
            description = "";
         }
      } BroadworksAnywhereLocation;

      /**
      * Broadworks Anywhere configuration
      */
      typedef struct BroadworksAnywhere
      {
         bool alertAllLocationsForClickToDialCalls;
         bool alertAllLocationsForGroupPagingCalls;
         cpc::vector<BroadworksAnywhereLocation> locations;

         BroadworksAnywhere()
         {
            alertAllLocationsForClickToDialCalls = false;
            alertAllLocationsForGroupPagingCalls = false;
         }
      } BroadworksAnywhere;

      /**
      * Call Forward Always configuration
      */
      typedef struct CallForwardAlways
      {
         bool enabled;
         cpc::string phoneNumber;
         bool ringSplash;

         CallForwardAlways()
         {
            enabled = false;
            ringSplash = false;
         }
      } CallForwardAlways;

      /**
      * Call Forward Busy configuration
      */
      typedef struct CallForwardBusy
      {
         bool enabled;
         cpc::string phoneNumber;

         CallForwardBusy()
         {
            enabled = false;
         }
      } CallForwardBusy;

      /**
      * Call Forward No Answer configuration
      */
      typedef struct CallForwardNoAnswer
      {
         bool enabled;
         cpc::string phoneNumber;
         unsigned int numberOfRings;

         CallForwardNoAnswer()
         {
            enabled = false;
            numberOfRings = 2; // rcc: Copied from my account, assumed default
         }
      } CallForwardNoAnswer;

      /**
      * Do Not Disturb configuration
      */
      typedef struct DoNotDisturb
      {
         bool enabled;
         bool ringSplash;

         DoNotDisturb()
         {
            enabled = false;
            ringSplash = false;
         }
      } DoNotDisturb;

      /**
      * Remote Office configuration
      * Not quite sure what this is for.
      */
      typedef struct RemoteOffice
      {
         bool enabled;
         cpc::string phoneNumber;

         RemoteOffice()
         {
            enabled = false;
         }
      } RemoteOffice;

      /**
      * Simultaneous Ring Location configuration
      */
      typedef struct SimultaneousRingLocation
      {
         cpc::string phoneNumber;
         bool answerConfirmationRequired;

         SimultaneousRingLocation()
         {
            answerConfirmationRequired = false;
         }

         void reset()
         {
            phoneNumber = "";
         }
      } SimultaneousRingLocation;

      /**
      * Simultaneous Ring configuration
      */
      typedef struct SimultaneousRing
      {
         bool enabled;
         bool dontRingWhenOnCall;
         cpc::vector<SimultaneousRingLocation> locations;

         SimultaneousRing()
         {
            enabled = false;
            dontRingWhenOnCall = false;
         }
      } SimultaneousRing;

      /**
      * The type of call log provided from the server.
      */
      typedef enum CallLogType
      {
         CallLogType_Missed,
         CallLogType_Placed,
         CallLogType_Received,
         CallLogType_All,
         CallLogType_Unknown
      } CallLogType;

      /**
      * Structure containing a call log's information.
      */
      typedef struct CallLog
      {
         cpc::string callLogId;
         cpc::string name;
         cpc::string phoneNumber;
         cpc::string countryCode;
         int64_t time; // time_t. Set to 0 if not provided.
         int duration; // seconds. If -1, unable to obtain.
         CallLogType callLogType;

         CallLog()
         {
            callLogType = CallLogType_Unknown;
            time = 0;
            duration = -1;
         }
         void reset()
         {
            callLogId = "";
            name = "";
            phoneNumber = "";
            countryCode = "";
            time = 0;
            duration = -1;
            callLogType = CallLogType_Unknown;
         }
      } CallLog;

      /**
      * The column for directory query results to be sorted by.
      */
      typedef enum SortColumn
      {
         SortColumn_LastName,
         SortColumn_FirstName,
         SortColumn_Department
      } SortColumn;

      /**
       * Structure containing the search parameters to use with queryEnterpriseDirectory().
       * Parameters set to empty string will be ignored.
       */
      typedef struct DirectorySearchParameters
      {
         cpc::string bridgeId;
         cpc::string department;
         cpc::string emailAddress;
         cpc::string extension;
         cpc::string firstName;
         cpc::string groupId;
         cpc::string impId; // IM & Presence Id
         cpc::string lastName;
         cpc::string mobileNo;
         cpc::string number;
         cpc::string roomId;
         cpc::string userId;
         cpc::string yahooId;
         /* The search criteria parameters can be logically combined with an
          * AND operation or an OR operation.
          *  - If the searchCriteriaModeOr is set to "true", then any result
          *    that matches any of the criteria is included in the result.
          *  - If the searchCriteriaModeOr is set to "false", only results that
          *    match all the search criteria are included in the results.
          */
         bool searchCriteriaModeOr;
         SortColumn sortColumn;
         // The maximum count of results to return. Maximum is 1000.
         unsigned int results;

         DirectorySearchParameters()
         {
            searchCriteriaModeOr = false;
            sortColumn = SortColumn_LastName;
            results = 1000;
         }
      } DirectorySearchParameters;

      /**
      * Structure containing an "additional detail" directory entry
      */
      typedef struct DirectoryAdditionalDetail
      {
         cpc::string name;
         cpc::string value;
      } DirectoryAdditionalDetail;

      /**
      * Structure containing a directory entry's information.
      */
      typedef struct DirectoryDetail
      {
         cpc::string bridgeId;
         cpc::string department;
         cpc::string emailAddress;
         cpc::string extension;
         cpc::string firstName;
         cpc::string groupId;
         cpc::string impId; // IM & Presence Id
         cpc::string lastName;
         cpc::string mobileNo;
         cpc::string number;
         cpc::string roomId;
         cpc::string userId;
         cpc::string yahooId;
         cpc::vector<DirectoryAdditionalDetail> additionalDetails;

         void reset()
         {
            bridgeId = "";
            department = "";
            emailAddress = "";
            extension = "";
            firstName = "";
            groupId = "";
            impId = "";
            lastName = "";
            mobileNo = "";
            number = "";
            roomId = "";
            userId = "";
            yahooId = "";
            additionalDetails.clear();
         }
      } DirectoryDetail;

   }
}

#endif //  __CPCAPI2_BROADSOFT_XSI_TYPES_H__
