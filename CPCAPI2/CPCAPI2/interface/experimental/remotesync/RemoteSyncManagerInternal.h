#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_MANAGER_INTERNAL_H)
#define CPCAPI2_REMOTE_SYNC_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "RemoteSyncManager.h"

namespace CPCAPI2
{

namespace RemoteSync
{

   class CPCAPI2_SHAREDLIBRARY_API RemoteSyncManagerInternal : public RemoteSyncManager
   {

   public:

      static RemoteSyncManagerInternal* getInternalInterface(CPCAPI2::Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void addEventObserver(RemoteSyncHandler* handler) = 0;
      virtual void removeEventObserver(RemoteSyncHandler* handler) = 0;

      // For request handle predictibility in unit tests
      static void resetRequestHandleCount();
   };

}

}

#endif // CPCAPI2_REMOTE_SYNC_MANAGER_INTERNAL_H
