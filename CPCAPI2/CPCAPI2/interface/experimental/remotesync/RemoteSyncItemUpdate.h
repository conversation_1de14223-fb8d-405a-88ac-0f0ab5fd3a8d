#pragma once
#ifndef __CPCAPI2_REMOTESYNC_SYNC_ITEM_UPDATE_H_
#define __CPCAPI2_REMOTESYNC_SYNC_ITEM_UPDATE_H_

#include <cpcstl/string.h>
#include <stdint.h>
#include "RemoteSyncTypes.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {

      struct RemoteSyncItemUpdate
      {
         ServerID    serverID;
         cpc::string clientID;

         cpc::string originalID;
         cpc::string stableID;
         bool        itemRead;
         bool        itemDeleted;
         bool        itemEdited;
         bool        itemUserDeleted;
         int         itemState;
         bool        preexists;
         int64_t     clientCreatedTime;
         int64_t     deliveryTimestamp;
         int64_t     readTimestamp;

         int statusCode;
         int callDuration;
         cpc::string primaryDeviceHash;
         
         RemoteSyncItemUpdate()
            : serverID( -1 ),
              itemRead( false ),
              itemDeleted( false ),
              itemEdited( false ),
              itemUserDeleted( false),
              itemState( 0 ),
              preexists( false ),
              clientCreatedTime( 0 ),
              statusCode( 0 ),
              callDuration( 0 ),
              deliveryTimestamp( 0 ),
              readTimestamp( 0 )
         {
         }

         RemoteSyncItemUpdate(ServerID serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, int64_t clientCreatedTime, bool isRead, bool isDeleted, bool isEdited, bool isUserDeleted, int itemState, bool preexists, 
                              int callDuration = 0, int statusCode = 0, const cpc::string &primaryDeviceHash = "", int64_t deliveryTimestamp = 0, int64_t readTimestamp = 0)
            : deliveryTimestamp(deliveryTimestamp), readTimestamp(readTimestamp)
         {
            this->serverID          = serverID;
            this->clientID          = clientID;
            this->originalID        = originalID;
            this->stableID          = stableID;
            this->itemRead          = isRead;
            this->itemDeleted       = isDeleted;
            this->itemEdited        = isEdited;
            this->itemUserDeleted   = isUserDeleted;
            this->itemState         = itemState;
            this->preexists         = preexists;
            this->clientCreatedTime = clientCreatedTime;
            this->statusCode = statusCode;
            this->callDuration = callDuration;
            this->primaryDeviceHash = primaryDeviceHash;
         }
      };

   }
}

#endif // __CPCAPI2_REMOTESYNC_SYNC_ITEM_UPDATE_H_
