#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_JSON_API_H)
#define CPCAPI2_REMOTE_SYNC_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{

class Phone;

namespace RemoteSync
{

/**
*/
class CPCAPI2_SHAREDLIBRARY_API RemoteSyncJsonApi
{

public:

   /**
    * Get a reference to the %RemoteSyncJsonApi interface.
   */
   static RemoteSyncJsonApi* getInterface(Phone* cpcPhone);

};

}

}

#endif // CPCAPI2_REMOTE_SYNC_JSON_API_H
