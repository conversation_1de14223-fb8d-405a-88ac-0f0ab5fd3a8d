#pragma once

#ifndef __SYNC_CONVERSATIONTHREADITEM_H__
#define __SYNC_CONVERSATIONTHREADITEM_H__

#include "cpcstl/string.h"
#include "RemoteSyncItem.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {

      struct RemoteSyncConversationThreadItem
      {
         RemoteSyncGroupChatItem latestChatInfo;
         RemoteSyncItem latestMessage;

         int unreadMessages;
         int totalMessages;

         bool hasLatestMessage;
         bool hasLatestChatInfo;

         RemoteSyncConversationThreadItem()
            : unreadMessages( -1 ),
            totalMessages( -1 ),
            hasLatestMessage( false ),
            hasLatestChatInfo( false )
         {
         }
      };
      
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& item);
   }
}

#endif
