#pragma once
#ifndef __CPCAPI2_REMOTESYNC_SYNC_ITEM_H_
#define __CPCAPI2_REMOTESYNC_SYNC_ITEM_H_

#include <map>

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include "RemoteSyncItemUpdate.h"
#include "RemoteSyncTypes.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      struct CPCAPI2_SHAREDLIBRARY_API RemoteSyncCallHistory
      {
      public:
         enum CallHistoryType
         {
            CallHistoryTypeUnknown = 0,
            CallHistoryTypeDialed = 1,
            CallHistoryTypeAnswered = 2,
            CallHistoryTypeMissed = 3,
            CallHistoryTypeForwarded = 4,
            CallHistoryTypeCompletedElsewhere = 5,
            CallHistoryTypeBlocked = 6,
            CallHistoryTypeReserved0 = 7,
            CallHistoryTypeReserved1 = 8,
            CallHistoryTypeReserved2 = 9,
            CallHistoryTypeConference = 10,
            CallHistoryTypeConferenceHosted = 11
         };

         static int const ITEM_STATE_MASK_CALLHISTORY_TYPE = 0x000001E0;

         static int const ITEM_STATE_CALLHISTORY_TYPE_UNKNOWN              = (0x0 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_DIALED               = (0x1 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_ANSWERED             = (0x2 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_MISSED               = (0x3 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_FORWARDED            = (0x4 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_COMPLETED_ELSEWHERE  = (0x5 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_BLOCKED              = (0x6 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_RESERVED_0           = (0x7 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_RESERVED_1           = (0x8 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_RESERVED_2           = (0x9 << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE           = (0xA << 5);
         static int const ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE_HOSTED    = (0xB << 5);

         enum CallFeatureUtilization
         {
            CallFeatureUtilizationNone = 0,
            CallFeatureUtilizationVideo = 1,
            CallFeatureUtilizationLocalMixer = 2,
            CallFeatureUtilizationAudioRecording = 4,
            CallFeatureUtilizationScreenshareHost = 8,
            CallFeatureUtilizationScreenshareViewer = 16
         };

         static int const ITEM_STATE_MASK_CALL_UTILIZATION = 0x0000FE00;

         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_NONE               = (0x00 << 9);
         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_VIDEO              = (0x01 << 9);
         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_LOCAL_MIXER        = (0x02 << 9);
         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_AUDIO_RECORDING    = (0x04 << 9);
         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_HOST   = (0x08 << 9);
         static int const ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_VIEWER = (0x10 << 9);

         RemoteSyncCallHistory();
         RemoteSyncCallHistory(RemoteSyncCallHistory const &other);
         RemoteSyncCallHistory(
            cpc::string remoteName,
            cpc::string associatedUri,
            cpc::string associatedNumber,
            int callDuration,
            cpc::string primaryDeviceHash,
            cpc::string primaryDevicePlatform,
            cpc::string primaryDeviceName,
            int statusCode = 0);

         // The remote party name
         cpc::string remoteName;
         // Depends on call history type (e.g. could be forwarded URI or conference URI)
         cpc::string associatedUri;
         // Depends on call history type (e.g. could be conference bridge number)
         cpc::string associatedNumber;
         int callDuration;
         // filled in by sync server. Value taken from client_device_hash of current session
         cpc::string primaryDeviceHash;
         // filled in by sync server. Value taken from client_device_platform of current session
         cpc::string primaryDevicePlatform;
         // filled in by sync server. Value taken from client_device_name of current session
         cpc::string primaryDeviceName;

         // leave as 0 for now
         int statusCode;

         static void setCallHistoryTypeBits(CallHistoryType callHistoryType, int& itemState);
         static CallHistoryType getCallHistoryType(int itemState);

         static void setCallFeatureUtilizationBits(CallFeatureUtilization callFeatureUtilization, int& itemState);
         static bool hasCallFeatureUtilization(CallFeatureUtilization callFeatureUtilization, int itemState);
      };

      struct CPCAPI2_SHAREDLIBRARY_API RemoteSyncReaction
      {
      public:
         RemoteSyncReaction();
         RemoteSyncReaction(RemoteSyncReaction const &other);
         RemoteSyncReaction(
            int64_t created_time,
            int64_t rev,
            cpc::string address,
            int64_t server_id,
            cpc::string value);

         int64_t created_time;
         int64_t rev;
         cpc::string address;
         int64_t server_id;
         cpc::string value;
      };

      struct CPCAPI2_SHAREDLIBRARY_API RemoteSyncItem
      {
         // The supported synchronization item types.
         // Phase 1 supports "im" and "chatinfo".
         enum ItemType
         {
            ItemType_null = -1,
            im,
            chatinfo,
            sms,
            callhistory,
            contact
         };

         enum Source
         {
            Source_null = -1,
            unknown,
            original,
            copy
         };

         enum DeliveryStatus
         {
            DeliveryStatusUnknown = 0,
            DeliveryStatusError = 1,
            DeliveryStatusQueued = 2,
            DeliveryStatusDelivered = 3,
            DeliveryStatusReceived = 4,
            DeliveryStatusRead = 5
         };

         // Possible states for a synchronized item (eg., XMPP message: inbound or outbound).
         // Used with the getState() and setState() methods.
         //
         // Note that "isRead" is handled separately, as it requires special
         // handling on the server for fetching conversation lists.
         //
         static int const ITEM_STATE_MASK_INBOUND   = 0x00000001;

         static int const ITEM_STATE_MASK_DELIVERY_STATUS = 0x0000001E;
         static int const ITEM_STATE_DELIVERY_STATUS_UNKNOWN   = 0x0;
         static int const ITEM_STATE_DELIVERY_STATUS_ERROR     = 0x1;
         static int const ITEM_STATE_DELIVERY_STATUS_QUEUED    = 0x2;
         static int const ITEM_STATE_DELIVERY_STATUS_DELIVERED = 0x3;
         static int const ITEM_STATE_DELIVERY_STATUS_RECEIVED  = 0x4;
         static int const ITEM_STATE_DELIVERY_STATUS_READ      = 0x5;
         /* other values reserved */

         // Set to 0 to indicate NULL.
         ServerID serverID;

         /**
         * This value is specified by the client when submitting a request.
         * The client may then match records from the response using this value against it's local record ID.
         *
         * If the server is issuing a spontaneous record, this value will be NULL.
         */
         cpc::string clientID;

         /**
         * The account this item is associated with.  This is whatever string Bria wishes to use to identify an account.
         */
         cpc::string account;

         /**
         * The source of the item
         */
         Source source;

         /**
         * The item type of this item: im, contact, etc...
         */
         ItemType itemType;

         /**
         * Has this item been read by the user?
         */
         bool itemRead;

	      /**
	      * Has this item been deleted by the user?
	      */
         bool itemDeleted;

         /**
	      * Has this item been edited by the user?
	      */
         bool itemEdited;

         /**
         * Has this item been deleted for both local and remote parties?
         */
         bool itemUserDeleted;

         /**
         * The very first message ID for message Editing and Reactions.
         */
         cpc::string originalID;

         /**
         * The "origin ID" or "stanza ID" depending on 1-1 or MUC chat.
         */
         cpc::string stableID;

         /**
         * The current state bitmask.
         */
         int state;

         /**
         * The timestamp of the initial event. (eg., XMPP message)
         */
         int64_t clientTimestamp;

         /**
         * The timestamp of the delivery confirmation. (eg., XMPP message)
         */
         int64_t deliveryTimestamp;

         /**
         * The timestamp of the read receipt. (eg., XMPP message)
         */
         int64_t readTimestamp;

         cpc::string from;

         cpc::string to;

         /**
         * The conversation ID of the message
         */
         cpc::string conversationID;

         /**
         * The MIME-type of the content?
         */
         cpc::string contentType;

         /**
         * A string representing the actual content of this sync item.
         */
         cpc::string content;

         /**
         * A string to uniquely identify a message, if available.
         */
         cpc::string uniqueID;

         /**
         * RemoteSyncCallHistory data, if available.
         */
         RemoteSyncCallHistory callHistory;

         /**
         * Reactions changes, if available.
         */
         cpc::vector<RemoteSyncReaction> reactions;

         RemoteSyncItem();

         /**
         * Copy constructor
         *
         * @param other
         */
         RemoteSyncItem(RemoteSyncItem const &other);

         // DEPRECATED; do not use
         RemoteSyncItem(ServerID serverId,
            cpc::string const & clientId,
            cpc::string const & account,
            Source source,
            cpc::string const & from,
            cpc::string const & to,
            cpc::string const & contentType,
            cpc::string const & message,
            cpc::string const & threadId,
            cpc::string const & uniqueID,
            cpc::string const & originalID,
            cpc::string const & stableID,
            bool isOutbound,
            bool isRead,
            bool isDeleted,
            bool isEdited,
            bool isUserDeleted,
            bool isDelivered,            
            cpc::vector<RemoteSyncReaction> const & reactions,
            int64_t messageTimestamp,
            int64_t deliveryTimestamp = 0,
            int64_t readTimestamp = 0);

         RemoteSyncItem(ServerID serverId,
            cpc::string const & clientId,
            cpc::string const & account,
            Source source,
            cpc::string const & from,
            cpc::string const & to,
            cpc::string const & contentType,
            cpc::string const & message,
            cpc::string const & threadId,
            cpc::string const & uniqueID,
            cpc::string const & originalID,
            cpc::string const & stableID,
            bool isOutbound,
            bool isRead,
            bool isDeleted,
            bool isEdited,
            bool isUserDeleted,
            cpc::vector<RemoteSyncReaction> const & reactions,
            DeliveryStatus deliveryStatus,
            int64_t messageTimestamp,
            int64_t deliveryTimestamp = 0,
            int64_t readTimestamp = 0);

         /**
          * ctor variant taking ivars directly
          */
         RemoteSyncItem(ServerID serverId,
            cpc::string const & clientId,
            cpc::string const & account,
            Source source,
            ItemType itemType,
            cpc::string const & from,
            cpc::string const & to,
            cpc::string const & contentType,
            cpc::string const & content,
            cpc::string const & conversationID,
            cpc::string const & uniqueID,
            cpc::string const & originalID,
            cpc::string const & stableID,
            bool isRead,
            bool isDeleted,
            bool isEdited,
            bool isUserDeleted,
            cpc::vector<RemoteSyncReaction> const & reactions,
            int  state,
            int64_t messageTimestamp,
            int64_t deliveryTimestamp = 0,
            int64_t readTimestamp = 0);

         RemoteSyncItem(ServerID serverId,
            cpc::string const & account,
            Source source,
            ItemType itemType,
            cpc::string const & from,
            cpc::string const & to,
            cpc::string const & conversationID,
            cpc::string const & uniqueID,
            cpc::string const & originalID,
            cpc::string const & stableID,
            bool isRead,
            bool isDeleted,
            bool isEdited,
            bool isUserDeleted,
            cpc::vector<RemoteSyncReaction> const & reactions,
            int  state,
            int64_t messageTimestamp,
            cpc::string remoteName,
            cpc::string associatedUri,
            cpc::string associatedNumber,
            int callDuration,
            cpc::string primaryDeviceHash,
            cpc::string primaryDevicePlatform,
            cpc::string primaryDeviceName,
            int64_t deliveryTimestamp = 0,
            int64_t readTimestamp = 0);

         /**
         * Set the item state delivery bits from a DeliveryStatus enum value.
         */
         static void setStateDeliveryBits(DeliveryStatus deliveryStatus, int& itemState);
         /**
         * Get the DeliveryStatus value from RemoteSyncItem's state bits.
         */
         static DeliveryStatus getStateDeliveryStatus(int itemState);
      };


      struct RemoteSyncGroupChatItem : public RemoteSyncItem
      {
      public:
         cpc::vector<cpc::string> getOccupants();
         void setOccupants(cpc::vector<cpc::string> occupants);
      };

      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem::Source& source);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem::ItemType& type);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem& item);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItemUpdate& item);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& item);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncCallHistory& item);
      cpc::string get_debug_string(const cpc::vector<CPCAPI2::RemoteSync::RemoteSyncReaction>& items);
   }
}

#endif // __CPCAPI2_REMOTESYNC_SYNC_ITEM_H_
