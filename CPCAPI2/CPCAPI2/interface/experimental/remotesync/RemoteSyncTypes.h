#pragma once
#ifndef __CPCAPI2_REMOTESYNC_TYPES_H__
#define __CPCAPI2_REMOTESYNC_TYPES_H__

#include <stdint.h>
#include <cpcstl/string.h>

// This macro is used to identify events/requests that are either
// initiated remotely or from websocketpp and have no corresponding
// "real" request ID
#define CPCAPI2_REMOTESYNC_NO_REQUEST_ID (-1)

namespace CPCAPI2
{
   namespace RemoteSync
   {
      typedef int32_t SessionHandle;
      typedef int64_t RequestHandle;
      typedef int64_t ServerID;
      typedef int64_t Revision;

      /**
       * Various states in which the connection to the remote sync server
       * may find itself.
       */
      typedef enum ConnectionState
      {
         ConnectionState_Disconnected, // The connection is either closed, or was never connected.
         ConnectionState_Connecting,   // The connection is in process of attempting to connect.
         ConnectionState_Connected,    // The connection was established successfully
         ConnectionState_Failed        // A previous connection attempt failed, and will be retried soon
      } ConnectionState;
      
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::ConnectionState& state);
   }
}

#endif //  __CPCAPI2_REMOTESYNC_TYPES_H__
