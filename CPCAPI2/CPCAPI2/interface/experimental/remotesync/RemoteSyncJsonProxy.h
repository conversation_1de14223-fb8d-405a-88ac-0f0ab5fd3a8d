#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_JSON_PROXY_H)
#define CPCAPI2_REMOTE_SYNC_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "remotesync/RemoteSyncManager.h"

namespace CPCAPI2
{

class Phone;

namespace RemoteSync
{

class CPCAPI2_SHAREDLIBRARY_API RemoteSyncJsonProxy : public CPCAPI2::RemoteSync::RemoteSyncManager
{

public:

   /**
    * Get a reference to the RemoteSyncManager interface.
   */
   static RemoteSyncJsonProxy* getInterface(Phone* cpcPhone);

protected:

   /*
    * The SDK will manage memory life of %RemoteSyncManager.
   */
   virtual~ RemoteSyncJsonProxy() {}

};

}

}

#endif // CPCAPI2_REMOTE_SYNC_JSON_PROXY_H
