#pragma once
#ifndef _CPCAPI2_REMOTESYNC_SYNC_SERVER_INTERFACE_H_
#define _CPCAPI2_REMOTESYNC_SYNC_SERVER_INTERFACE_H_

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include "cpcapi2defs.h"

#include <websocket/WebSocketTypes.h>

#include "RemoteSyncItem.h"
#include "RemoteSyncConversationThreadItem.h"
#include "RemoteSyncTypes.h"

namespace CPCAPI2
{
   // Forward declaration
   class Phone;

   namespace RemoteSync
   {
      // Forward declaration
      class RemoteSyncHandler;

      /**
      * Device specific information
      */
      struct ClientDeviceInfo
      {
         cpc::string clientDeviceHash;
         cpc::string clientDevicePlatform;
         cpc::string clientDeviceName;
         ClientDeviceInfo() : clientDeviceHash(NULL), clientDevicePlatform(NULL), clientDeviceName(NULL) {}
         ClientDeviceInfo(const cpc::string& hash, const cpc::string& platform, const cpc::string& name) : clientDeviceHash(hash), clientDevicePlatform(platform), clientDeviceName(name) {}
      };

      /**
       * Settings which are associated with each remote sync session
       */
      struct RemoteSyncSettings
      {
         /**
          * Generic web socket settings which apply to remote sync service.
          */
         WebSocket::WebSocketSettings wsSettings;

         /**
          * Authentication password parameter(username is in the webSocketURL
          * parameter)
          */
         cpc::string password;

         /**
          * List of accounts which will be used for remote sync purposes.
          */
         cpc::vector<cpc::string> accounts;

         /**
         * Device specific information
         */
         ClientDeviceInfo clientDeviceInfo;
      };

      class CPCAPI2_SHAREDLIBRARY_API RemoteSyncManager
      {
      public:
         /**
          * Get a reference to the %RemoteSyncManager interface.
          */   
         static RemoteSyncManager* getInterface(Phone* cpcPhone);

         /**
          * Creates a new RemoteSync session, for use with the following APIs.
          */
         virtual SessionHandle create( void ) = 0;

         /**
          * Pushes the settings down to the supplied session. Also initializes
          * some resources based on the settings.
          */
         virtual int configureSettings(
            const SessionHandle& syncSession,
            const struct RemoteSyncSettings& settings ) = 0;

         /**
          * Method to add a notification listener to the Client API, to receive
          * responses, errors, and updates.
          * 
          * @param syncSession the remote sync session which will be used
          * @param notificationListener
          */
         virtual int setHandler(
            const SessionHandle& syncSession,
            RemoteSyncHandler * notificationHandler) = 0;

         /**
          * Results in the SDK performing a connection to the remote sync
          * server (which includes LOGIN). Connections will be retried where
          * needed, subject to a common retry mechanism.
          *
          * @param syncSession the remote sync session which will be used
          */
         virtual int connect( const SessionHandle& syncSession ) = 0;

         /**
          * Disconnects and logs out from the remote sync server.
          */
         virtual int disconnect( const SessionHandle& syncSession ) = 0;
         
         /**
          * Destroys a remote sync session
          */
         virtual int destroy( const SessionHandle& syncSession ) = 0;
         
         /**
          * @param syncSession the remote sync session which will be used
          */
         virtual RequestHandle setAccounts(
            const SessionHandle& syncSession,
            const cpc::vector<cpc::string>& accounts) = 0;

         /**
          * Method to send local data to the server to be synchronized.
          * 
          * @param syncSession the remote sync session which will be used
          * @param itemsToSync - The items the client wishes to synchronize with
          * the server; These are the items that have arrived at the client by
          * other means (XMPP, SIP, etc...) or have been modified on the client
          * (eg., message change unread to read).
          */
         virtual RequestHandle syncItem(
            const SessionHandle& syncSession,
            const RemoteSyncItem& itemToSync) = 0;

         /**
          * @param syncSession the remote sync session which will be used
          */
         virtual RequestHandle syncItems(
            const SessionHandle& syncSession,
            const cpc::vector<RemoteSyncItem>& itemsToSync) = 0;

         /**
          * A method to specify a range of items to be pulled from the database, by revision
          * 
          * @param syncSession     The remote sync session which will be used
          * @param lowestRevision
          * @param highestRevision
          * @param itemTypes		 Allows limiting response to a set of specific item types
          * @param conversationID	 Allows limiting response to a specific conversation
          * @param account			 Allows the response to be limited to a specific account
          * @param count				 Maximum number of results to return
          * @param offset			 Where to start in the result set
          * @param ascending		 Sort order; sorted by last modified timestamp
          */
         virtual RequestHandle fetchRangeRevision(
            const SessionHandle& syncSession,
            Revision lowestRevision,
            Revision highestRevision,
            const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
            const cpc::string& conversationID,
            const cpc::string& account,
            bool includeDeleted,
            int count,
            int offset,
            bool ascending) = 0;

         /**
          * A method to specify a range of items to be pulled from the database, by revision
          * 
          * @param syncSession        the remote sync session which will be used
          * @param lowestCreatedTime  Specify 0 for unlimited
          * @param highestCreatedTime Specify 0 for unlimited
          * @param itemTypes			 Allows limiting response to a set of specific item types
          * @param conversationID	    Allows limiting response to a specific conversation
          * @param account			    Allows the response to be limited to a specific account
          * @param count				    Maximum number of results to return
          * @param offset			    Where to start in the result set
          * @param ascending			 Sort order; sorted by last modified timestamp
          */
         virtual RequestHandle fetchRangeCreatedTime(
            const SessionHandle& syncSession,
            int64_t lowestCreatedTime,
            int64_t highestCreatedTime,
            const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
            const cpc::string& conversationID,
            const cpc::string& account,
            bool includeDeleted,
            int count,
            int offset,
            bool ascending) = 0;

         /**
          * This method will return the newest item for each known threadID for
          * instant messages.  This is intended to allow the GUI to update the
          * conversation list without having to pull every complete
          * converstation.
          * 
          * Each conversation's single newest item will be returned.
          * 
          * Items are ordered by last-modified timestamp, descending.
          * 
          * This will result in the onFetchConversationsComplete callback being
          * invoked.
          *
          * @param syncSession the remote sync session which will be used
          * @param lowestClientCreatedTime  - Specify 0 for unlimited
          * @param highestClientCreatedTime - Specify 0 for unlimited
          * @param count
          * @param offset
          */
         virtual RequestHandle fetchConversations(
            const SessionHandle& syncSession,
            int64_t lowestClientCreatedTime,
            int64_t highestClientCreatedTime,
            int count,
            int offset) = 0;

         /**
          * This version of fetchConversations will return the newest item for
          * each conversation specified in the ID list.
          * 
          * Each conversation's single newest item will be returned. 
          * 
          * Items are ordered by last-modified timestamp, descending, meaning
          * the order of conversations returned.
          * 
          * This will result in the onFetchConversationsComplete callback being
          * invoked.
          * 
          * @param syncSession the remote sync session which will be used
          * @param conversationIDs - The list of conversations to pull
          */
         virtual RequestHandle fetchConversations(
            const SessionHandle& syncSession,
            const cpc::vector< cpc::string >& conversationIDs) = 0;
	
         /**
          * This method will update all items in the specified conversation,
          * that are older than or equal to the specified timestamp.
          * 
          * This will result in the onConversationUpdated callback being
          * invoked.  If anything rows are affected, it will also cause changes
          * to be forwarded to other clients connected to the same address.
          * (For other clients, the RequestID will be 0.)
          * 
          * Note that setting an item to "deleted" also implies the that item is
          * set to 'read'.
          * 
          * @param syncSession the remote sync session which will be used
          * @param accountID - The account the conversation is to be deleted on
          * @param conversationID - The conversation's identifier
          * @param highestClientCreatedTime - Modify anything created on or before
          *        this time. Specify 0 (zero) for unlimited
          * @param setItemsRead - Set all matching items 'read' state to true
          * @param setItemsDeleted - Set all matching items to be deleted (and read)
          */
         virtual RequestHandle updateConversation(
            const SessionHandle& syncSession,
            const cpc::string& accountID,
            const cpc::string& conversationID,
            int64_t highestClientCreatedTime,
            bool setItemsRead,
            bool setItemsDeleted,
            bool setItemsUserDeleted) = 0;

         /**
          * Method to determine the total number of unread / read messages for a
          * specific account.
          * 
          * This will result in the onMessageCount callback being invoked.
          * 
          * @param syncSession the remote sync session which will be used
          * @param accountID The account for which the count should be obtained
          * @param types The types of SyncItem objects to include in the response, or null for all types.
          * 
          * @return
          */
         virtual RequestHandle getMessageCount(
            const SessionHandle& syncSession,
            const cpc::string& accountID,
            const cpc::vector< RemoteSyncItem::ItemType >& types) = 0;

         /**
          * A method to change dynamic parts of a sync item.  This will cause
          * updates to be sent to other clients connected to the same service.
          * @param syncSession the remote sync session which will be used
          * @param clientID is not used for lookup; it is merely echoed back in the onSyncItemComplete callback.
          */
         virtual RequestHandle updateItem(
            const SessionHandle& syncSession,
            const ServerID& serverID,
            const cpc::string& clientID,
            const cpc::string& originalID,
            const cpc::string& stableID,
            bool itemRead,
            bool itemDeleted,
            bool itemEdited,
            bool itemUserDeleted,
            int itemState,
            int callDuration = 0,
            int64_t deliveryTimestamp = 0,
            int64_t readTimestamp = 0) = 0;

         /**
          * A method to add a reaction to a sync item.  This will cause
          * updates to be sent to other clients connected to the same service.
          * @param syncSession the remote sync session which will be used
          */
         virtual RequestHandle sendReaction(
            const SessionHandle& syncSession,
            const RemoteSyncReaction& reaction) = 0;

         /**
          * A method to request reactions from the server. Will result in 
          * onFetchMessagesReactionsComplete() event.
          * @param syncSession the remote sync session which will be used
          */
         virtual RequestHandle fetchMessagesReactions(
            const SessionHandle& syncSession, 
            bool ascending, 
            int offset, 
            int count) = 0;

         virtual RequestHandle updateItems(
            const SessionHandle& syncSession,
            const cpc::vector<cpc::string>& accounts,
            const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
            const cpc::vector<cpc::string>& conversationIDs,
            const cpc::vector<int64_t>& serverIDs,
            bool isRead,
            bool isDeleted,
            bool isEdited,
            int64_t readTimestamp = 0) = 0;

         /**
          * The blocking modes for the process() function.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Method which must be called by the application in order to
          * 'service' events coming from the remote sync handler.
          */
         virtual int process(int timeout) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %RemoteSyncManager.
          */
         virtual ~RemoteSyncManager() {}
      };
      
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncSettings& settings);
   }
}

#endif // _CPCAPI2_REMOTESYNC_SYNC_SERVER_INTERFACE_H_
