#pragma once
#ifndef _CPCAPI2_REMOTESYNC_HANDLER_H_
#define _CPCAPI2_REMOTESYNC_HANDLER_H_

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <iostream>
#include "RemoteSyncTypes.h"
#include "RemoteSyncItem.h"
#include "RemoteSyncConversationThreadItem.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      struct SetAccountsEvent
      {
         RequestHandle requestID;
      };

      struct NotificationUpdateEvent
      {
         Revision rev; // highest revision for this user
         cpc::vector<RemoteSyncItem> items;
      };

      struct MessageReactionsEvent
      {
         RequestHandle requestID;
         Revision rev;
         int64_t created_time;
         int64_t server_id;
         cpc::string address;
         cpc::string value;
      };

      struct FetchMessagesReactionsCompleteEvent
      {
         RequestHandle requestID;
         Revision rev; // highest revision for this user
         cpc::vector<RemoteSyncReaction> reactions;
         int request_offset;
         int request_maxcount;
      }; 

      struct SyncItemsCompleteEvent
      {
         RequestHandle requestID;
         Revision rev; // revision number for this change set
         cpc::vector<RemoteSyncItemUpdate> items;
      };

      struct UpdateItemCompleteEvent
      {
         RequestHandle requestID;
         Revision rev; // revision of the updated item. (only updated if the item is modified)
         RemoteSyncItemUpdate delta;
      };

      struct FetchRangeCompleteEvent
      {
         RequestHandle requestID;
         cpc::vector<RemoteSyncItem> items;
         int request_offset;
         int request_count;
      };

      struct FetchConversationsCompleteEvent
      {
         RequestHandle requestID;
         cpc::vector<RemoteSyncConversationThreadItem> items;
         int request_offset;
         int request_count;
      };

      struct ConversationUpdatedEvent
      {
         RequestHandle requestID;
         Revision      rev;
         cpc::string   conversationID;

         int64_t highestClientCreatedTime; // The highest createdTime of affected messages (millis since unix epoch)
         bool setItemsRead;
         bool setItemsDeleted;
      };

      struct ItemsUpdatedEvent
      {
         bool isRead;
         bool isDeleted;
         bool isEdited;
         cpc::vector<cpc::string> accounts;
         cpc::vector<RemoteSyncItem::ItemType> itemTypes;
         cpc::vector<cpc::string> conversationIDs;
         cpc::vector<int64_t> serverIDs;
         int64_t readTimestamp;
      };

      struct UpdateItemsCompleteEvent
      {
         RequestHandle requestID;
         bool isRead;
         bool isDeleted;
         bool isEdited;
         cpc::vector<cpc::string> accounts;
         cpc::vector<RemoteSyncItem::ItemType> itemTypes;
         cpc::vector<cpc::string> conversationIDs;
         cpc::vector<int64_t> serverIDs;
         int64_t readTimestamp;
      };

      struct MessageCountEvent
      {
         RequestHandle requestID;
         int unread;
         int total;
         int unreadConversations;
         int totalConversations;
      };

      struct OnErrorEvent
      {
         RequestHandle requestID;
         cpc::string errorCode;
         cpc::string errorMessage;
      };

      struct OnConnectionStateEvent
      {
         ConnectionState previousState;
         ConnectionState currentState;
      };

      /**
       * Struct containing the amount of time by which the client
       * should adjust its timestamps before sending to the server
       * as determined through querying the server.
       */
      struct OnTimestampDeltaEvent
      {
         int64_t timestampDelta;
      };

      class RemoteSyncHandler
      {
      public:
         /**
          * Called by the SDK when the accounts were set correctly on the session.
          */
         virtual int onSetAccounts( const SessionHandle& sessionHandle, const SetAccountsEvent& evt ) = 0;

         /**
          * Method to be called by the API when an update arrives, due to
          * activity from another client.
          * 
          * In the case of this  method, the sessionHandle should generally be
          * set to CPCAPI2_REMOTESYNC_NO_REQUEST_ID (which is defined as being
          * -1).
          */
         virtual int onNotificationUpdate( const SessionHandle& sessionHandle, const NotificationUpdateEvent& evt ) = 0;

         /**
          * Method to be called by the API when a reaction is applied to a message 
          * in another client.
          * 
          * In the case of this  method, the sessionHandle should generally be
          * set to CPCAPI2_REMOTESYNC_NO_REQUEST_ID (which is defined as being
          * -1).
          */
         virtual int onMessageReactions( const SessionHandle& sessionHandle, const MessageReactionsEvent& evt ) = 0;

         /**
          * Method to be called when a fetchMessagesReactions response is received from the
          * server.
          * 
          * @param items
          */
         virtual int onFetchMessagesReactionsComplete( const SessionHandle& sessionHandle, const FetchMessagesReactionsCompleteEvent& evt ) = 0;

         /**
          * Method to be called when a SyncItems response is received from the
          * server.
          * 
          * @param items
          */
         virtual int onSyncItemsComplete( const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& evt ) = 0;

         virtual int onUpdateItemComplete( const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& evt ) = 0;

         /**
          * Method to be called upon receiving a response to a fetchRange
          * command
          */
         virtual int onFetchRangeComplete( const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& evt ) = 0;

         virtual int onFetchConversationsComplete( const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& evt ) = 0;

         /**
          * Method to be called due to a connected client calling
          * updateConversation.
          * 
          * If this client made the original call, then requestID will be
          * popuplated with the request's RequestID.  If this callback is being
          * invoked as a result of a different client making the call, then
          * RequestID will be null. 
          */
         virtual int onConversationUpdated( const SessionHandle& sessionHandle, const ConversationUpdatedEvent& evt ) = 0;

         /**
         * Method to be called upon receiving a response to an updateItems
         * command
         */
         virtual int onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& evt) = 0;

         /**
         * Method to be called due to a connected client calling
         * updateItems.
         */
         virtual int onItemsUpdated(const SessionHandle& sessionHandle, const ItemsUpdatedEvent& evt) = 0;

         /**
          * Method to be called upon receiving a response to the
          * getMessageCount command
          */
         virtual int onMessageCount( const SessionHandle& sessionHandle, const MessageCountEvent& evt ) = 0;

         virtual int onError( const SessionHandle& sessionHandle, const OnErrorEvent& evt ) = 0;

         /**
          * Method invoked when the state of the connection to the remote sync
          * server changes.
          */
         virtual int onConnectionState( const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt ) = 0;

         /**
          * Method fired which allows the client to update its "timestamp
          * delta". The delta should be added to the currently generated
          * timestamp before sending any new timestamps to the server.
          */
         virtual int onTimestampDelta( const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& evt ) = 0;
      };

      cpc::string get_debug_string(const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::NotificationUpdateEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::MessageReactionsEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::ConversationUpdatedEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::SetAccountsEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::MessageCountEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnConnectionStateEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& event);
      cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnErrorEvent& event);
      
   }
}

#endif /* _CPCAPI2_REMOTESYNC_HANDLER_H_ */
