#pragma once

#if !defined(CPCAPI2_SIGNATURE_MANAGER_H)
#define CPCAPI2_SIGNATURE_MANAGER_H

#include "cpcapi2defs.h"


namespace CPCAPI2
{
class Phone;
namespace Signature
{
/**
* Manager interface to control call quality reporting for phone calls;
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SignatureManager
{
public:
   /**
   * Get a reference to the %SignatureManager interface.
   */   
   static SignatureManager* getInterface(Phone* cpcPhone);

   virtual bool brandedSignatureCheck() = 0;
};

}

}

#endif // CPCAPI2_SIGNATURE_MANAGER_H
