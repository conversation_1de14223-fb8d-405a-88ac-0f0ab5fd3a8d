#pragma once

#include "cpcapi2.h"
#include "cpcstl/string.h"
#include <functional>

#ifndef CPCAPI2_AGENT_API
#ifdef CPCAPI2_AGENT_EXPORT
#define CPCAPI2_AGENT_API CPCAPI2_SHAREDLIBRARY_EXPORT
#else
#define CPCAPI2_AGENT_API CPCAPI2_SHAREDLIBRARY_API
#endif
#endif

namespace CPCAPI2
{
namespace Agent
{
class Cpcapi2Runner;
class CPCAPI2_AGENT_API SdkManagerExt
{
public:
   static SdkManagerExt* create();
   void destroy();

   virtual void run(std::function<void(Cpcapi2Runner*)> callbackFn) = 0;
   virtual int invokeJsonApi(const cpc::string& jsonApiCall) = 0;
   virtual void setJsonApiCallback(void(*cb)(const cpc::string&)) = 0;
   virtual void addVideoStreamCallback(int videoStreamHandle, void* cbState, void(*cb)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)) = 0;
   virtual void removeVideoStreamCallback(int videoStreamHandle) = 0;
   virtual bool isProdBuild() const = 0;

protected:
   SdkManagerExt() {};
   virtual ~SdkManagerExt() {}

};
}
}
