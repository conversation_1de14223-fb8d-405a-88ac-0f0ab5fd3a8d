#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_HANDLER_H__)
#define __CPCAPI2_SIP_CHAT_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipChatTypes.h"
#include "cpm/CpmTypes.h"
#include "iscomposing/IsComposingTypes.h"

#include <ctime>

using namespace CPCAPI2::CPM;
using namespace CPCAPI2::IsComposing;

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace SipChat
{
/**
 * Type of chat session.
 */
enum ChatType
{
	ChatType_Incoming = 1000,
	ChatType_Outgoing	= 1010,
};

/**
 * Possible reasons for a terminated chat session.
 */
enum ChatEndReason
{
   ChatEndReason_Unknown                = 1100,
	ChatEndReason_UserTerminatedLocally  = 1110,
	ChatEndReason_UserTerminatedRemotely = 1120,
   ChatEndReason_Rejected      			 = 1130
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK to signal a new chat session.
 */
struct NewChatEvent
{
   ChatType chatType; // Type of chat session
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when the other participant of the
 * chat session is composing a new message.
 */
struct IsComposingMessageEvent
{
   IsComposingMessageState state; // The state of the 'isComposing' indicator
   CPM::MimeType mimeType;        // MIME type of the message being composed
   struct std::tm lastActive;          // date/time the 'isComposing' indicator was last active
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK to signal a new message within a chat session.
 */
struct NewMessageEvent
{
   SipChatMessageHandle message; // The message
   cpc::string from;            // The originator of the message
   cpc::string to;              // The destination of the message
   struct std::tm datetime;           // date/time the message was sent, in local time
   CPM::MimeType mimeType;       // MIME type of the message
   cpc::string messageContent;   // The content of the message, represented as a byte array
   cpc::vector<DispositionNotificationType> dispositionNotifications; // Types of disposition notifications requested
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message was successfully
 * delivered to the other participant's device(s).
 */
struct MessageDeliveredEvent
{
   SipChatMessageHandle notification;           // The notification
   SipChatMessageHandle message;                // The delivered message
   MessageDeliveryStatus messageDeliveryStatus; // The status of the message delivery
   struct std::tm datetime;                          // date/time the message was sent, in local time
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message was successfully
 * displayed on the other participant's device(s).
 */
struct MessageDisplayedEvent
{
   SipChatMessageHandle notification;         // The notification
   SipChatMessageHandle message;              // The displayed message
   MessageDisplayStatus messageDisplayStatus; // The status of the message display
   struct std::tm datetime;                        // date/time the message was sent, in local time
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a new message was successfully
 * sent.
 */
struct SendMessageSuccessEvent
{
   SipChatMessageHandle message; // The new message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a new message failed to send.
 */
struct SendMessageFailureEvent
{
   SipChatMessageHandle message; // The new message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message delivered notification
 * was successfully sent.
 */
struct NotifyMessageDeliveredSuccessEvent
{
   SipChatMessageHandle notification; // The notification
   SipChatMessageHandle message;      // The delivered message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message displayed notification
 * was successfully sent.
 */
struct NotifyMessageDisplayedSuccessEvent
{
   SipChatMessageHandle notification; // The notification
   SipChatMessageHandle message;      // The displayed message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a 'message being composed' notification
 * was successfully sent.
 */
struct SetIsComposingMessageSuccessEvent
{
   IsComposingMessageState state; // The state of the 'isComposing' indicator
};

/**
 * Event fired by the SDK to signal the termination of a chat session.
 */
struct ChatEndedEvent
{
   SipChatHandle chat;      // The chat session that ended
   ChatEndReason endReason; // Reason for the termination of a chat session
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Used to report general SDK error conditions, such as invalid handles, or cases
 * where the transfer is not in a valid state for the requested operation.
 */
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Handles all SDK callbacks related to the chat feature.
 */
class SipChatHandler
{
public:
   /**
    * Callback invoked by the SDK when a new chat session is established.
    *
    * @param chat the chat session that was established.
    * @param args information about the new chat session.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewChat(SipChatHandle chat, const NewChatEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the other participant is composing a new message.
    *
    * @param chat the chat session associated with the message being composed.
    * @param args information about the message being composed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIsComposingMessage(SipChatHandle chat, const IsComposingMessageEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a new message is received within a chat session.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the new message.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewMessage(SipChatHandle chat, const NewMessageEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message was successful.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the success.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageSuccess(SipChatHandle chat, const SendMessageSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message failed.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the failure.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageFailure(SipChatHandle chat, const SendMessageFailureEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was successfully delivered
    * to the other participant's device(s).
    *
    * @param chat the chat session that the new message was sent against.
    * @param args information about the message delivered.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDelivered(SipChatHandle chat, const MessageDeliveredEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was successfully displayed
    * on the other pariticpant's device(s).
    *
    * @param chat the chat session that the new message was sent against.
    * @param args information about the message displayed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDisplayed(SipChatHandle chat, const MessageDisplayedEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message delivered notification was successfuly sent.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the message delivered notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDeliveredSuccess(SipChatHandle chat, const NotifyMessageDeliveredSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message displayed notification was successfuly sent.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the message displayed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDisplayedSuccess(SipChatHandle chat, const NotifyMessageDisplayedSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a 'message being composed' notification was successfuly sent.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the message being composed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSetIsComposingMessageSuccess(SipChatHandle chat, const SetIsComposingMessageSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a chat session was terminated.
    *
    * @param chat the chat session that was terminated.
    * @param args information about the terminated chat session.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onChatEnded(SipChatHandle chat, const ChatEndedEvent& event) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param chat the chat session the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(SipChatHandle chat, const ErrorEvent& event) = 0;
};

}
}

#endif // __CPCAPI2_SIP_CHAT_HANDLER_H__
