#pragma once

#if !defined(__CPCAPI2_SIP_CHAT_MANAGER_H__)
#define __CPCAPI2_SIP_CHAT_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipChatTypes.h"
#include "cpm/CpmTypes.h"

#include <ctime>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace SipChat
{
// Forward declarations
class SipChatHandler;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Interface for all chat related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API SipChatManager
{
public:
   /**
    * Get a reference to the %SipChatManager interface.
    *
    * @param cpcPhone the phone object to be associated with this manager.
    *
    * @return the %SipChatManager interface.
    */
   static SipChatManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for chat related events on the specified account.
    * Set the handler immediately after getting a reference to this manager.
    *
    * To un-register the handler, pass NULL for handler. Must be called on
    * the same thread as <PERSON><PERSON><PERSON>ccount::process(..)
    *
    * @param account the user account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipChatHandler* handler) = 0;

   /**
    * Allocates a chat session within the SDK. This function is
    * used in concert with addParticipant(..) and start(..) to begin a
    * new chat session. Use end..() to later terminate the chat session.
    *
    * @param account the user account of the creator of the
    *                new chat session.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual SipChatHandle createChat(SipAccount::SipAccountHandle account) = 0;

   /**
    * Adds a participant to the chat session. Call this
    * function after createChat(..) and before start(..).
    *
    * NOTE: Only the first participant will be used, as only 1-1 chat is
    * supported at the moment. Additional participants will be
    * ignored.
    *
    * @param chat the chat session to add a participant to.
    * @param participantSipAddress the SIP address of the participant. The
    *                              format of the targetAddress parameter is
    *                              sip:<EMAIL>.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int addParticipant(
         SipChatHandle chat,
         const cpc::string& participantSipAddress) = 0;

   /**
    * Initiates a chat session by sending an INVITE to the
    * remote participant.
    *
    * @param chat The chat session to initiate.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int start(SipChatHandle chat) = 0;

   /**
    * Ends a chat session. Sends an outgoing BYE.
    *
    * @param chat The chat session to terminate.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int end(SipChatHandle chat) = 0;

   /**
    * Sends a message to the other party that is part of the chat session.
    *
    * @param chat The chat session to send the message against.
    * @param messageContent the content of message to send.
    * @param mimeType the MIME type of the message to send, defauts to text/plain if not specified.
    * @param datetime the date/time at which the message was sent, defaults to the current date/time if not specified.
    * @param dispositionNotifications the types of notifications requested, none if not specified.
    *
    * @return the handle of the message being sent.
    */
   virtual SipChatMessageHandle sendMessage(
         SipChatHandle chat,
         const cpc::string& messageContent,
         CPM::MimeType mimeType = MimeType_TextPlain,
         struct std::tm* datetime = 0,
         const cpc::vector<DispositionNotificationType>& dispositionNotifications
            = cpc::vector<DispositionNotificationType>()) = 0;

   /**
    * Used after receiving an incoming chat session to accept the
    * INVITE offered by the remote party.
    *
    * @param chat The incoming chat session to accept.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int accept(SipChatHandle chat) = 0;

   /**
    * Used after receiving an incoming chat session to reject the
    * INVITE offered by the remote party.
    *
    * @param chat The incoming chat session to reject.
    * @param rejectReason The SIP response code sent to the
    *                     originating party.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int reject(
         SipChatHandle chat,
         unsigned int rejectReason) = 0;

   /**
    * Notifies the other participant that the message has been successfully
    * delivered.
    *
    * @param chat the chat session associated with the message delivered.
    * @param message the delivered message.
    * @param messageDeliveryStatus the status of the message delivery.
    *
    * @return the handle of the notification being sent.
    */
   virtual SipChatMessageHandle notifyMessageDelivered(SipChatHandle chat,
         SipChatMessageHandle message,
         MessageDeliveryStatus messageDeliveryStatus) = 0;

   /**
    * Notifies the other participant that the message has been successfully
    * displayed.
    *
    * @param chat the chat session associated with the message being composed.
    * @param message the displayed message.
    * @param messageDisplayStatus the status of the message display
    *
    * @return the handle of the notification being sent.
    */
   virtual SipChatMessageHandle notifyMessageDisplayed(SipChatHandle chat,
      SipChatMessageHandle message,
      MessageDisplayStatus messageDisplayStatus) = 0;

   /**
    * Indicates that a new message is being typed/composed.
    *
    * Notes:
    *
    * 1) It does not necessarily mean that a notification will be sent to the other
    *    participant of the chat session. It depends on the internal state and timers.
    *
    * 2) However, this method must be called often while the participant is actively
    *    typing/composing the message in order to maintain an accurate internal state.
    *
    * @param chat the chat session associated with the message being composed.
    * @param mimeType the MIME type of the message being composed, defauts to text/plain if not specified.
    * @param datetime the date/time at which the message is being composed, defaults to the current date/time if not specified.
    * @param refreshInterval the refresh interval to use (in seconds), defaults to 90 seconds.
    * @param idleInterval the idle interval to use (in seconds), defaults to 15 seconds.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setIsComposingMessage(SipChatHandle chat,
      CPM::MimeType mimeType = CPM::MimeType_TextPlain,
      struct std::tm* datetime = 0,
      int refreshInterval = 90,
      int idleInterval = 15) = 0;

protected:
   /*
    * The SDK will manage memory life of %SipChatManager.
    */
   virtual ~SipChatManager() {}
};

}
}

#endif // __CPCAPI2_SIP_CHAT_MANAGER_H__
