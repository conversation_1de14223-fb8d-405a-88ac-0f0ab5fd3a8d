//
//  GenbandWatcherinfoExt.h
//  CPCObjAPI2
//
//  Created by <PERSON> on 8/14/15.
//  Copyright (c) 2015 CounterPath. All rights reserved.
//

#ifndef CPCObjAPI2_GenbandWatcherinfoExt_h
#define CPCObjAPI2_GenbandWatcherinfoExt_h

#include "cpcapi2defs.h"
#include <event/SipEventManager.h>

namespace CPCAPI2 {

namespace SipEvent {

   class CPCAPI2_SHAREDLIBRARY_API GenbandWatcherinfoExt
   {
   public:
      virtual int addSupportedEowHeader(SipEventSubscriptionHandle subscription) = 0;
   };
}
   
}


#endif
