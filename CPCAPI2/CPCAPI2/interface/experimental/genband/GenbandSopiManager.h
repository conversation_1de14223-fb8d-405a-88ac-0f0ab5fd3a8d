#pragma once

#if !defined(CPCAPI2_GENBAND_SOPI_CLIENT_MANAGER_H)
#define CPCAPI2_SOPI_CLIENT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "GenbandSopiTypes.h"

namespace CPCAPI2
{

class Phone;

namespace GenbandSopi
{

class GenbandSopiHandler;

/**
* Manager interface for Genband Personal/Global Address Book SOPI service.
* get a reference to the interface using the static method getInterface().
*/

class CPCAPI2_SHAREDLIBRARY_API GenbandSopiManager
{
public:
   /**
   * Get a reference to the %GenbandSopiManager interface.
   */
   static GenbandSopiManager* getInterface(Phone* cpcPhone);

   /**
   * Create a Genband SOPI client
   */
   virtual GenbandSopiClientHandle createClient(const GenbandSopiClientSettings& settings) = 0;

   /**
   * Set the handler for Genband SOPI events for the specified client handle.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
   */   
   virtual int setHandler(
         GenbandSopiClientHandle handle,
         GenbandSopiHandler* handler) = 0;

   /**
   * Request to retrieve the client's Personal Address Book (PAB). Results will
   * be provided via the GenbandSopiHandler::onPersonalAddressBookUpdated callback.
   */
   virtual int requestAddressBook(GenbandSopiClientHandle handle) = 0;

   /**
   * Add a new entry to the PAB. No callback will be invoked except in case of error.
   */
   virtual int addAddressBookEntry(GenbandSopiClientHandle handle, const AddressBookEntry& entry) = 0;

   /**
   * Update an existing PAB entry. No callback will be invoked except in case of error.
   */
   virtual int updateAddressBookEntry(GenbandSopiClientHandle handle, const cpc::string& entryId, const AddressBookEntry& entry) = 0;

   /**
   * Remove an existing PAB entry. No callback will be invoked except in case of error.
   */
   virtual int deleteAddressBookEntry(GenbandSopiClientHandle handle, const cpc::string entryName) = 0;

   /**
   * Request to retrieve the user's PAB groups. Results will be provided via 
   * the GenbandSopiHandler::onPersonalAddressBookGroupsUpdated.
   */
   virtual int getAddressBookGroups(GenbandSopiClientHandle handle) = 0;

   /**
   * Add a new group to the PAB. No callback will be invoked except in case of error.
   */
   virtual int addAddressBookGroup(GenbandSopiClientHandle, const cpc::string& groupName) = 0;

   /**
   * Update an existing PAB group. No callback will be invoked except in case of error.
   */
   virtual int updateAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& oldGroupName, const cpc::string& newGroupName) = 0;

   /**
   * Delete an existing PAB group. No callback will be invoked except in case of error.
   */
   virtual int deleteAddressBookGroup(GenbandSopiClientHandle handle, const cpc::string& groupName) = 0;

   /**
   * Search the Global Address Book (GAB) for specified string. Can be name, username or phone number.
   * Results will be provided via the GenbandSopiHandler::onGlobalAddressBookSearchResult callback, up to maxCount.
   */
   virtual int searchGlobalDirectory(GenbandSopiClientHandle handle, const cpc::string& searchQuery, unsigned int maxCount) = 0;

   /**
   * This method is used to add a user to the authorize list of a user's addressbook which would 
   * prevent the authorized user from subscribing to this user's presence information
   */
   virtual int addAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to remove a user from the list of users authorized from subscribing 
   * to this user's presence information from the authorize list in the addressbook.
   */
   virtual int removeAuthorizedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to get all the users authorized from subscribing to this 
   * user's presence information from the authorize list in the addressbook.
   */
   virtual int getAuthorizedUsers(GenbandSopiClientHandle handle) = 0;   

   /**
   * This method is used to add a user to the ban list of a user's addressbook 
   * which would prevent the banned user from subscribing to this user's presence information
   */
   virtual int addBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to remove a user from the list of users banned from subscribing 
   * to this user's presence information from the ban list in the addressbook.
   */
   virtual int removeBannedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to get all the users banned from subscribing 
   * to this user's presence information from the ban list in the addressbook.
   */
   virtual int getBannedUsers(GenbandSopiClientHandle handle) = 0;   

   /**
   * This method is used to add multiple user's to the politeblock list of a user's addressbook 
   * which would prevent the politeblocked user from subscribing to this user's presence information
   */
   virtual int addPoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to remove a user from the list of users politeblocked from subscribing 
   * to this user's presence information from the politeblock list in the addressbook.
   */
   virtual int removePoliteBlockedUser(GenbandSopiClientHandle handle, const cpc::string& userName) = 0;
   
   /**
   * This method is used to get all the users politeblocked from subscribing 
   * to this user's presence information from the politeblock list in the addressbook.
   */
   virtual int getPoliteBlockedUsers(GenbandSopiClientHandle handle) = 0;   
   
   /**
   * Set subscription handler for account. The method should b invoker before account will be enabled.
   */
   virtual int setSubscriptionHandler(CPCAPI2::SipAccount::SipAccountHandle account) = 0;
   
   /**
   * Subscribe an existing account to get notification then Personal Address Book (PAB) is changed. 
   * No callback will be invoked except in case of error.
   */
   virtual int startSubscription(GenbandSopiClientHandle handle,CPCAPI2::SipAccount::SipAccountHandle account) = 0;
   
   /**
   * Unsubscribe an existing client to get notification then Personal Address Book (PAB) is changed. 
   * No callback will be invoked except in case of error.
   */
   virtual int stopSubscription(GenbandSopiClientHandle handle) = 0;

   /**
   * Destroy a Genband SOPI client.
   *
   * @param handle the client handle
   */
   virtual int destroy(GenbandSopiClientHandle handle) = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kModuleDisabled = -1;

   /**
    * Functionality is replaced by Phone::process()
    */
   DEPRECATED virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling GenbandSopiClientManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
protected:
   /* 
    * The SDK will manage memory life of %GenbandSopiManager.
    */
   virtual ~GenbandSopiManager() {}

};
}
}

#endif
