#pragma once

#if !defined(CPCAPI2_GENBAND_REST_API_HANDLER_H)
#define CPCAPI2_GENBAND_REST_API_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace Genband
{

struct GenbandNotification
{
	cpc::string Header;
	cpc::string Body;
};

using CPCAPI2::SipEvent::SipEventSubscriptionHandle;

class GenbandRestAPIHandler
{
public:
	virtual int onIncomingCommand(SipEventSubscriptionHandle subscription, const CPCAPI2::Genband::GenbandNotification& args) = 0;
};

}// namespace Genband
}// namespace CPCAPI2
#endif
