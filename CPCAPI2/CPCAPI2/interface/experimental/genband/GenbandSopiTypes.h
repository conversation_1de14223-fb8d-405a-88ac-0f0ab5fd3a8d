#pragma once

#if !defined(__CPCAPI2_GENBAND_SOPI_TYPES_H__)
#define __CPCAPI2_GENBAND_SOPI_TYPES_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace GenbandSopi
{
typedef unsigned int GenbandSopiClientHandle;

struct GenbandSopiClientSettings
{
   cpc::string username;
   cpc::string password;
   cpc::string serverUrl;
   cpc::string userAgent;
   bool ignoreCertValidation;
};

struct AddressBookEntry
{
   cpc::string firstName;
   cpc::string lastName;
   cpc::string nickName;
   cpc::string email;
   cpc::string home;
   cpc::string business;
   cpc::string mobile;
   cpc::string pager;
   cpc::string fax;
   cpc::string photoURL;
   cpc::string primaryContact;
   cpc::string group;
   bool        buddy;

   AddressBookEntry()
   {
      buddy = false;
   }
};

struct PresenceAuthorizationEntry
{
    cpc::string userName;
};

}
}
#endif
