#pragma once

#if !defined(CPCAPI2_GENBAND_SOPI_HANDLER_H)
#define CPCAPI2_GENBAND_SOPI_HANDLER_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include "GenbandSopiTypes.h"

namespace CPCAPI2
{
namespace GenbandSopi
{

/**
* Event passed in GenbandSopiHandler::onGetAddressBookResult containing the result
* of a GenbandSopiManager::requestAddressBook operation
*/
struct AddressBookUpdatedEvent
{
   cpc::vector<AddressBookEntry> entries;
};

struct AddressBookGroupsUpdatedEvent
{
   cpc::vector<cpc::string> groups;
};

struct SearchGlobalAddressBookResultEvent
{
   cpc::string searchQuery;
   cpc::vector<AddressBookEntry> entries;
};

struct GetAuthorizedUsersEvent
{
	cpc::vector<PresenceAuthorizationEntry> entries;
};

struct GetBannedUsersEvent
{
	cpc::vector<PresenceAuthorizationEntry> entries;
};

struct GetPoliteBlockedUsersEvent
{
	cpc::vector<PresenceAuthorizationEntry> entries;
};


/**
* Event passed in GenbandSopiHandler::onError(), used to report errors
* related to asynchronous SOPI operations.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

class GenbandSopiHandler
{
public:
   virtual int onPersonalAddressBookUpdated(GenbandSopiClientHandle client, const AddressBookUpdatedEvent& args) = 0;
   virtual int onPersonalAddressBookGroupsUpdated(GenbandSopiClientHandle client, const AddressBookGroupsUpdatedEvent& args) = 0;
   virtual int onGlobalAddressBookSearchResult(GenbandSopiClientHandle client, const SearchGlobalAddressBookResultEvent& args) = 0;
   virtual int onGetAuthorizedUsers(GenbandSopiClientHandle client,const GetAuthorizedUsersEvent& args) = 0;
   virtual int onGetBannedUsers(GenbandSopiClientHandle client,const GetBannedUsersEvent& args) = 0;
   virtual int onGetPoliteBlockedUsers(GenbandSopiClientHandle client,const GetPoliteBlockedUsersEvent& args) = 0;
   virtual int onError(GenbandSopiClientHandle client, const ErrorEvent& args) = 0;
};
}

}
#endif
