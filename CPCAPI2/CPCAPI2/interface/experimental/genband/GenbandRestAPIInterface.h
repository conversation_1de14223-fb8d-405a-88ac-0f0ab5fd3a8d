#pragma once

#if !defined(CPCAPI2_GENBAND_REST_API_INTERFACE_H)
#define CPCAPI2_GENBAND_REST_API_INTERFACE_H
#endif

//includes
#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace Genband
{

struct GenbandResult
{
	long ResultCode;
	cpc::string ResultMessage;
};

struct GenbandHeader
{
	cpc::string UserAgent;
	cpc::string Authorization;
	cpc::string Username;
	cpc::string Password;
	cpc::string Domain;
};
struct GenbandBody
{
	cpc::string Url;
	cpc::string Body;
};

/**
* Internal interface for genband call functionality in the SDK.
*/
class GenbandRestAPIHandler;

class CPCAPI2_SHAREDLIBRARY_API GenbandRestAPIInterface
{
public:
	/**
	* Get a reference to the %GenbandRestAPIInterface interface.
	*/
   static GenbandRestAPIInterface* getInterface(Phone* cpcPhone);
	virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::Genband::GenbandRestAPIHandler* handler) = 0;

	virtual GenbandResult makeCall(GenbandHeader header, GenbandBody body) = 0; // - Make a call
	virtual GenbandResult answerCall(GenbandHeader header, GenbandBody body) = 0; // - Answer a call
	virtual GenbandResult rejectCall(GenbandHeader header, GenbandBody body) = 0; // - Reject or forward a call
	virtual GenbandResult forwardCall(GenbandHeader header, GenbandBody body) = 0; //
	virtual GenbandResult placeOnHold(GenbandHeader header, GenbandBody body) = 0; // - Place a call on hold
	virtual GenbandResult endCall(GenbandHeader header, GenbandBody body) = 0; //- End a call
	virtual GenbandResult conferenceCall(GenbandHeader header, GenbandBody body) = 0; // Conference in calls
	virtual GenbandResult subscribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account) = 0; // - Subscribe
	virtual GenbandResult extendSubscribtion(GenbandHeader header, GenbandBody body) = 0; // - Extend subscribtion
	virtual GenbandResult unsubcribe(GenbandHeader header, GenbandBody body, CPCAPI2::SipAccount::SipAccountHandle account) = 0; // - Unsubscribe
   
protected:
   /* 
    * The SDK will manage memory life of %GenbandRestAPIInterface.
    */
   virtual ~GenbandRestAPIInterface() {}
};
}
}
