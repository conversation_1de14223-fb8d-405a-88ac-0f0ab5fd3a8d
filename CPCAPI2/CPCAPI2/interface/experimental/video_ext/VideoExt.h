#pragma once

#if !defined(CPCAPI2_VIDEOEXT_H)
#define CPCAPI2_VIDEOEXT_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "media/MediaManager.h"

namespace CPCAPI2
{
namespace Media
{
struct ScreenshareDeviceInfo
{
   unsigned int deviceId;
   cpc::string deviceDescription;
   bool isWindow;
};

struct ScreenshareDeviceListEvent
{
   cpc::vector<ScreenshareDeviceInfo> devices;
};

struct VideoWebsocketServerStartedEvent
{
};

class VideoWebsocketServerHandler
{
public:
   virtual void onVideoWebsocketServerStarted(int videoStreamId, const VideoWebsocketServerStartedEvent& args) = 0;
};

struct VideoSnapshotEvent
{
   bool success;
};

class VideoSnapshotHandler
{
public:
   virtual void onSnapshotComplete(int videoStreamId, const VideoSnapshotEvent& args) = 0;
};

class ScreenshareDeviceListHandler
{
public:
   virtual void onScreenshareDeviceList(const ScreenshareDeviceListEvent& evt) = 0;
};

class ScreenShareHandler
{
public:
   virtual void onScreenShareError() = 0;
};

/**
* Experimental.
*/
class CPCAPI2_SHAREDLIBRARY_API VideoExt
{
public:
   /**
   * Get a reference to the %Video interface.
   */   
   static VideoExt* getInterface(CPCAPI2::Media::MediaManager* cpcPhone);

   /**
   * Experimental
   */
   //virtual int connectReceiveStreamToMp4Output(int videoStreamId, VideoBitstreamHandler* bitstreamHandler) = 0;
   virtual int set1080pEnabled(bool enable) = 0;
   virtual int startScreenshare(ScreenShareHandler* handler) = 0;
   virtual int stopScreenshare() = 0;
   virtual int startWebsocketServer(VideoWebsocketServerHandler* handler) = 0;
   virtual int startWebsocketServerForReceiveStream(int videoStreamId) = 0;
   virtual int stopWebsocketServer() = 0;
   virtual int createSnapshotForReceiveStream(int videoStreamId, const cpc::string& fileNameUtf8, VideoSnapshotHandler* snapshotHandler) = 0;
   virtual int setBrokenKeyFrameSupportEnabled(bool enable) = 0;
   virtual int setKeyFramePeriod(int periodInFrames) = 0;
   virtual int queryScreenshareDeviceList(ScreenshareDeviceListHandler* handler, bool includeMonitors, bool includeWindows) = 0;
   virtual int setScreenshareCaptureDevice(unsigned int deviceId) = 0;
   virtual int setScreenshareCaptureMaxFramerate(unsigned int maxFrameRate, bool forceOverride = false) = 0;
   virtual int setInterleavedModeEnabled(bool enabled) = 0;
};

}
}
#endif // CPCAPI2_VIDEOEXT_H
