#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_H)
#define CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipDialogEventModel.h"

namespace CPCAPI2
{
class Phone;

namespace SipDialogEvent
{
class SipDialogEventSubscriptionHandler;

/**
* Subscription settings.
*/
struct SipDialogEventSubscriptionSettings
{
   unsigned int expiresSeconds;                 // Expires value to use in outgoing SUBSCRIBEs
   bool includeSessionDescription;              // Indicates whether the SDP should be included or not in the dialog-info document
   bool enableSharedAppearanceDialogExtensions; // Indicates whether the dialog-info XML schema extensions described in section 5.2 of draft-bliss-shared-apperances-15 are supported
   bool enableNotifyTerminationRetryHandling;   // Indicates whether the subscription retry logic should be handled for termination via NOTIFY, since another module may have alternate handling for it
   cpc::string additionalEventParameterName;    // Additional event package parameter to use in outgoing subscriptions

   SipDialogEventSubscriptionSettings()
   {
      expiresSeconds = 3600;
      includeSessionDescription = false;
      enableSharedAppearanceDialogExtensions = false;
      enableNotifyTerminationRetryHandling = true;
   }
};

/**
* Manager interface to create and manage dialog event subsciptions (RFC 4235). The interface
* provides client functionality (i.e. sending SUBSCRIBEs and handling NOTIFYs) as well 
* as server functionality (i.e. handling SUBSCRIBES and sending NOTIFYs); get a reference 
* to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SipDialogEventSubscriptionManager
{
public:
  /**
   * Get a reference to the %SipDialogEventSubscriptionManager interface.
   */   
   static SipDialogEventSubscriptionManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for subscription events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as SipAccountManager::process(..)
   *
   * @param account the user account to be associated with the handler.
   * @param handler the handler to register
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipDialogEventSubscriptionHandler* handler) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   *
   * @param account the user account of the creator of the new subscription.
   *
   * @return the ougoing subscription.
   */
   virtual SipDialogEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Allocates a new subscription within the SDK based on a pre-existing subscription.  This function is used in concert 
   * with addParticipant(..) and start(..) to initiate a re-subscribe for an existing subscription session.
   *
   * @param account the user account of the creator of the new subscription.
   * @param subscription the handle of the existing subscription.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int recreateSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipDialogEventSubscriptionHandle handle) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Must be invoked prior to calling start(..) so that subscription parameters are configured.
   *
   * @param subscription the outgoing subscription the settings are going to be applied to.
   * @param settings the settings.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int applySubscriptionSettings(SipDialogEventSubscriptionHandle subscription, const SipDialogEventSubscriptionSettings& settings) = 0;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   *
   * @param subscription the outgoing subscription the participant is going to be added to.
   * @param targetAddress the address of the participant. The format is sip:<EMAIL>
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int addParticipant(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Sets the address of the event server responsible for sending event notifications.  For example, this may be
   * the address of a resource list server.  If an event server is set on a subscription, the outgoing SUBSCRIBE
   * is sent to the targetAddress for the event server specified in the targetAddress parameter and the URIs in 
   * the resource list are taken from the list of URIs passed with the addParticipant(..) function.
   * Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int setEventServer(SipDialogEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   *
   * @param subscription the outgoing subscription to start.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int start(SipDialogEventSubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session. Sends an outgoing SUBSCRIBE with Expires == 0.
   *
   * @param subscription the subscription to terminate.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int end(SipDialogEventSubscriptionHandle subscription) = 0;

   /**
   * Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
   *
   * @param subscription The incoming subscription session to reject.
   * @param rejectReason The SIP response code sent to the originating party.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int reject(SipDialogEventSubscriptionHandle subscription, unsigned int rejectReason) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK).
   *
   * @param subscription The incoming subscription session to accept.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int accept(SipDialogEventSubscriptionHandle subscription) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipDialogEventSubscriptionManager.
    */
   virtual ~SipDialogEventSubscriptionManager() {}
};

}
}

#endif // CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_MANAGER_H
