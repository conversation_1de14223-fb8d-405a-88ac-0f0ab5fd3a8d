#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_HANDLER_H)
#define CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipDialogEventTypes.h"
#include "event/SipEventPublicationHandler.h"

namespace CPCAPI2
{
namespace SipDialogEvent
{
/**
* Event passed in SipDialogEventPublicationHandler::onPublicationSuccess().
 */
struct DialogEventPublicationSuccessEvent
{
};

/**
* Event passed in SipDialogEventPublicationHandler::onPublicationFailure().
 */
struct DialogEventPublicationFailureEvent
{
   CPCAPI2::SipEvent::SipPublicationFailureReason reason;
   DialogEventPublicationFailureEvent() 
      : reason(CPCAPI2::SipEvent::SipPublicationFailureReason_Unknown)
   {}
};

/**
* Event passed in SipDialogEventPublicationHandler::onPublicationRemove().
 */
struct DialogEventPublicationRemoveEvent
{
};

/**
* Event passed in SipDialogEventPublicationHandler::onError(); 
* used to report general SDK error conditions, such as invalid handles, or cases
* where the subscription is not in a valid state for the requested
* operation.
*/
struct DialogEventPublicationErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to dialog event subscriptions; 
* set in SipDialogEventPublicationManager::setHandler().
*/
class SipDialogEventPublicationHandler
{
public:
   virtual int onPublicationSuccess(SipDialogEventPublicationHandle publication, const DialogEventPublicationSuccessEvent& args) = 0;
   virtual int onPublicationFailure(SipDialogEventPublicationHandle publication, const DialogEventPublicationFailureEvent& args) = 0;
   virtual int onPublicationRemove(SipDialogEventPublicationHandle publication, const DialogEventPublicationRemoveEvent & args) = 0;
   virtual int onError(SipDialogEventPublicationHandle publication, const DialogEventPublicationErrorEvent& args) = 0;
};

}
};

#endif // CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_HANDLER_H
