#pragma once

#if !defined(__CPCAPI2_SIP_DIALOG_EVENT_MODEL_H__)
#define __CPCAPI2_SIP_DIALOG_EVENT_MODEL_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipDialogEventTypes.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace SipDialogEvent
{

/**
 * State of the dialog info (XML) document.
 */
enum DialogInfoDocumentState
{
   DialogInfoDocumentState_NotSpecified = 1000,
   DialogInfoDocumentState_Partial      = 1010,
   DialogInfoDocumentState_Full         = 1020
};

/**
 * State of the dialog.
 */
enum DialogState
{
   DialogState_NotSpecified = 1100,
   DialogState_Trying       = 1110,
   DialogState_Proceeding   = 1120,
   DialogState_Early        = 1130,
   DialogState_Confirmed    = 1140,
   DialogState_Terminated   = 1150,
};

/**
* Reason for the dialog state change.
*/
enum DialogStateReason
{
   DialogStateReason_NotSpecified = 1200,
   DialogStateReason_Cancelled    = 1210,
   DialogStateReason_Rejected     = 1220,
   DialogStateReason_Replaced     = 1230,
   DialogStateReason_LocalBye     = 1240,
   DialogStateReason_RemoteBye    = 1250,
   DialogStateReason_Error        = 1260,
   DialogStateReason_Timeout      = 1270
};

/**
* Direction of the dialog.
*/
enum DialogDirection
{
   DialogDirection_NotSpecified = 1300,
   DialogDirection_Initiator    = 1310,
   DialogDirection_Recipient    = 1320
};

/**
* Information on the state of the dialog.
*/
struct DialogStateInfo
{
   DialogState state;        // Dialog state
   DialogStateReason reason; // Reason for change of dialog state
   int code;                 // Error code (if available)

   DialogStateInfo()
   {
      state = DialogState_NotSpecified;
      reason = DialogStateReason_NotSpecified;
      code = 0;
   }
};

/**
* Information on the participant's target.
*/
struct TargetInfo
{
   cpc::string uri;               // Address of the target
   cpc::vector<Parameter> params; // Parameters provided with the address of the target
};

/**
* Information on the participant identity of a dialog.
*/
struct IdentityInfo
{
   cpc::string address;    // the original entry including any parameters
   cpc::string uri;        // the above with the parameters removed
   cpc::vector<Parameter> uriParams;
   cpc::string displayName;
};

/**
* Information on the participant of a dialog.
*/
struct ParticipantInfo
{
   DEPRECATED NameAddress identity; // Address of the participant - this information is now available in the first entry of "identities" below
   cpc::vector<IdentityInfo> identities;  // Addresses of the participant
   TargetInfo target;               // Address of the participant's target
   cpc::string contentType;         // Content type of the session description
   cpc::string sessionDescription;  // Session description of the participant
   int cseq;                        // Current CSeq od the participant

   ParticipantInfo()
   {
      cseq = 0;
   }
};

/**
* Information on the dialog.
*/
struct DialogInfo
{
   cpc::string id;                    // Id of this dialog
   CPCAPI2::DialogId dialogId;        // Dialog-id
   DialogStateInfo stateInfo;         // Information on dialog state
   unsigned long duration;            // Duration of the dialog
   DialogDirection direction;         // Direction of the dialog
   DialogId replaces;                 // Dialog-id being replaced
   NameAddress referredBy;            // Address in the Referred-By header
   cpc::vector<cpc::string> routeSet; // List of routes associated with this dialog
   ParticipantInfo localParticipant;  // Information on the local participant
   ParticipantInfo remoteParticipant; // Information on the remote participant
   ParticipantInfo parkedParticipant; // Information on the participant of a parked call
   int appearance;                    // Shared Appearance Dialog Extension: appearance number
   bool exclusive;                    // Shared Appearance Dialog Extension: exclusive (true/false)

   DialogInfo()
   {
      duration = 0;
      direction = DialogDirection_NotSpecified;
      appearance = 0;
      exclusive = false;
   }
};

/**
* Parsed content of the dialog-info (XML) document.
*/
struct DialogInfoDocument
{
   int version;                     // Version of the document
   DialogInfoDocumentState state;   // State of the document
   cpc::string entity;              // Entity
   cpc::vector<DialogInfo> dialogs; // Affected dialogs

   DialogInfoDocument()
   {
      version = 0;
      state = DialogInfoDocumentState_NotSpecified;
   }
};

/**
* Information on a single instance from resource list content.
*/
struct DialogResourceState
{
   cpc::string uri;                       // SIP address of the resource
   SipEvent::SipSubscriptionState state;  // Subscription state provided in resource list
   DialogInfoDocument dialogInfoDoc;      // The parsed content of the instance's dialog-info
};

}
}

#endif // __CPCAPI2_SIP_DIALOG_EVENT_MODEL_H__
