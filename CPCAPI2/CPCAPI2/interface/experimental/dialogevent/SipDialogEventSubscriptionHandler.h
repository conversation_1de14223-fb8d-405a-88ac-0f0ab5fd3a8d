#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_HANDLER_H)
#define CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipDialogEventModel.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace SipDialogEvent
{
/**
 * Event passed in SipDialogEventSubscriptionHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
 * an outgoing SUBSCRIBE has forked, OR
 * an incoming SUBSCRIBE has been received.
 */
struct NewDialogEventSubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;          // The user account
   CPCAPI2::SipEvent::SubscriptionType   subscriptionType; // Outgoing or incoming subscription
   cpc::string                           remoteAddress;    // The address of the  remote endpoint
};

/**
 * Event passed in Sip<PERSON>ialogEventHandler::onSubscriptionEnded().
 * The client subscription session is terminated (for any reason).
 */
struct DialogEventSubscriptionEndedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionEndReason endReason; // The reason for the subsciption termination
   CPCAPI2::SipEvent::SubscriptionType         subscriptionType;
   int                                         statusCode; // Error code
   int                                         retryAfter; // value of Retry-Header if present
   bool                                        initialSubscribe; // Initial Subscribe failed
   bool                                        isNotifyTerminated; // Terminated because of NOTIFY
   cpc::string                                 remoteAddress; // Buddy address
   cpc::string                                 reason; // subscription end reason
};

/**
 * Event passed in SipDialogEventHandler::onSubscriptionStateChanged().
 * The state of the outgoing subscription has changed.
 */
struct DialogEventSubscriptionStateChangedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionState subscriptionState; // The new state of the subscription
};

/**
 * Event passed in SipDialogEventSubscriptionHandler::onIncomingDialogInfo().
 * An incoming NOTIFY was received.
 */
struct IncomingDialogInfoEvent
{
   DialogInfoDocument dialogInfoDoc; // Parsed dialog-info document
};

/**
* Event passed in SipDialogEventSubscriptionHandler::onMultipleDialogInfo().
* Contains resource list meta information received in an incoming NOTIFY
*/

struct DialogResourceListEvent
{
   cpc::vector<DialogResourceState> resources;
};

/**
 * Event passed in SipDialogEventSubscriptionHandler::onNotifyDialogInfoFailure().
 * SIP error response received upon sending a NOTIFY.
 */
struct NotifyDialogInfoFailureEvent
{
   int sipResponseCode;
};

/**
* Event passed in SipDialogEventSubscriptionHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to dialog event subscriptions; 
* set in SipDialogEventPublicationManager::setHandler().
*/
class SipDialogEventSubscriptionHandler
{
public:
   virtual int onNewSubscription(SipDialogEventSubscriptionHandle subscription, const NewDialogEventSubscriptionEvent& args) = 0;
   virtual int onSubscriptionEnded(SipDialogEventSubscriptionHandle subscription, const DialogEventSubscriptionEndedEvent& args) = 0;
   virtual int onIncomingDialogInfo(SipDialogEventSubscriptionHandle subscription, const IncomingDialogInfoEvent& args) = 0;
   virtual int onDialogResourceListUpdated(SipDialogEventSubscriptionHandle subscription, const DialogResourceListEvent& args) = 0;
   virtual int onSubscriptionStateChanged(SipDialogEventSubscriptionHandle subscription, const DialogEventSubscriptionStateChangedEvent& args) = 0;
   virtual int onNotifyDialogInfoFailure(SipDialogEventSubscriptionHandle subscription, const NotifyDialogInfoFailureEvent& args) = 0;
   virtual int onError(SipDialogEventSubscriptionHandle subscription, const ErrorEvent& args) = 0;
};

}
};

#endif // CPCAPI2_SIP_DIALOG_EVENT_SUBSCRIPTION_HANDLER_H
