#pragma once

#if !defined(CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_H)
#define CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipDialogEventModel.h"

namespace CPCAPI2
{
class Phone;

namespace SipDialogEvent
{
class SipDialogEventPublicationHandler;

/**
* Publication settings.
*/
struct SipDialogEventPublicationSettings
{
   unsigned int expiresSeconds; // Expires value to use in outgoing PUBLISHes

   SipDialogEventPublicationSettings()
   {
      expiresSeconds = 3600;
   }
};

/**
* Manager interface to create and manage dialog event publications (draft-bliss-shared-apperances-15). The interface
* provides client functionality (i.e. sending PUBLISHes); get a reference 
* to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SipDialogEventPublicationManager
{
public:
  /**
   * Get a reference to the %SipDialogEventPublicationManager interface.
   */   
   static SipDialogEventPublicationManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for publication events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as SipAccountManager::process(..)
   *
   * @param account the user account to be associated with the handler.
   * @param handler the handler to register
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipDialogEventPublicationHandler* handler) = 0;

   /**
   * Allocates a new publication within the SDK.  This function is used in concert with addParticipant(..) and publish(..)
   * to begin a new outgoing (client) publication session.
   *
   * @param account the user account of the creator of the new publication.
   * @param settings the settings.
   *
   * @return the ougoing publication.
   */
   virtual SipDialogEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipDialogEventPublicationSettings& settings) = 0;

   /**
   * Sets the target of the publication session. Call this function after createPublication(..) and before publish(..).
   *
   * @param publication the outgoing publication the target is going to be set against.
   * @param targetAddress the address of the participant. The format is sip:<EMAIL>
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int setTarget(SipDialogEventPublicationHandle publication, const cpc::string& targetAddress) = 0;

  /**
   * Sends a PUBLISH request with content as specified by the dialogInfoDoc parameter.
   *
   * @param publication the outgoing publication.
   * @param dialogInfoDoc the dialog-info document to send.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int publish(SipDialogEventPublicationHandle publication, const DialogInfoDocument& dialogInfoDoc) = 0;

   /**
   * Removes event state for the publication per section 4.5 of RFC 3903.
   *
   * @param publication the outgoing publication.
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int end(SipDialogEventPublicationHandle publication) = 0;
   
   
protected:
   /*
    * The SDK will manage memory life of %SipDialogEventPublicationManager.
    */
   virtual ~SipDialogEventPublicationManager() {}
};

}
}

#endif // CPCAPI2_SIP_DIALOG_EVENT_PUBLICATION_MANAGER_H
