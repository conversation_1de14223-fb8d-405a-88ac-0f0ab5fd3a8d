#pragma once

#if !defined(CPCAPI2_LDAP_HANDLER_H)
#define CPCAPI2_LDAP_HANDLER_H

#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "LdapTypes.h"

namespace CPCAPI2
{
  namespace OpenLdap
  {
	
  /**
  * Event passed in LdapHandler::onSearchCompleted containing the result
  * of a LdapManager::search operation
  */	
  struct LdapDataEvent{
     cpc::vector<LdapDataEntry> entries;
  };
  
  /**
  * Event passed in LdapHandler::OnStateChangedEvent containing current
  * state of ldap module
  */	
  struct OnStateChangedEvent
  {
     LdapState state;
  };
	
  /**
  * Event passed in LdapHandler::onError(), used to report errors
  * related to asynchronous ldap operations.
  * @param errGroup discribe error type 
  * @param errorText ldap error description 
  */
  struct ErrorEvent
  {
	 LdapErrorType errorType;
     cpc::string errorText;
  };

  class LdapHandler
  {
  public:
     virtual int onSearchCompleted(const LdapClientHandle handle, const LdapDataEvent& args) = 0;
     virtual int onStateChanged(const LdapClientHandle handle, const OnStateChangedEvent& args) = 0;
     virtual int onError(const LdapClientHandle handle, const ErrorEvent& args) = 0;
  };
	
  }//namespace CPCAPI2
}//namespace OpenLdap
#endif
