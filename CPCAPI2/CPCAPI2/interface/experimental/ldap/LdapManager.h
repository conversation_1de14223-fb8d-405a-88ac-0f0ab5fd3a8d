#pragma once

#if !defined(CPCAPI2_LDAP_MANAGER_H)
#define CPCAPI2_LDAP_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "LdapTypes.h"

namespace CPCAPI2
{

class Phone;

namespace OpenLdap
{

class LdapHandler;

/**
* Manager interface for Open Ldap service.
* get a reference to the interface using the static method getInterface().
*/

class CPCAPI2_SHAREDLIBRARY_API LdapManager
{
public:
   /**
   * Get a reference to the %LdapManager interface.
   */
   DEPRECATED static LdapManager* getInterface(Phone* cpcPhone, bool useSeparateThread);

   /**
   * Get a reference to the %LdapManager interface.
   */
   static LdapManager* getInterface(Phone* cpcPhone);

   /**
   * Create a Ldap client
   */
   virtual LdapClientHandle createClient() = 0;
   
   /**
   * Apply Connection ldap settings
   */
   virtual int applySettings(LdapClientHandle handle,const LdapClientSettings& settings) = 0;

   /**
   * Set the handler for Ldap events for the specified client handle.
   *
   * @param handle the ldap handle which will be used
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
   */   
   virtual int setHandler(
         LdapClientHandle handle,
         LdapHandler* handler) = 0;
	

   /**
   * Results in the SDK performing a connection to the ldap server. 
   *
   * @param handle the ldap handle which will be used
   */
	virtual int connect(LdapClientHandle handle) = 0;
   
   /**
   * Results in the SDK performing a disconnect from the ldap server. 
   *
   * @param handle the ldap handle which will be used
   */	 
	virtual int disconnect(LdapClientHandle handle)  = 0;
  
  
   /**
   *  set maping for searching data at ldap server. 
   *
   * @param handle the ldap handle which will be used
   * @param map data map which will be used in parsing search results
   */
	virtual int setDataMap(LdapClientHandle handle,LdapDataMap map) = 0;
  
   /**
   * This routine are used to perform LDAP search operations.
   *
   * @param handle the client handle
   * @param searchPattern is a string representation of the filter to apply in the search.
   * @param rootDn is the DN of the entry at which to start the search.
   * @param scopeSetting is the scope of the search
   * @param timeout search timout
   * @param maxsize max records in search
   * @param opErrIgnore operations error ignore - a work around for the issue BRACE-10852
   
   */   	 	
   virtual int search(
         LdapClientHandle handle,
         cpc::string searchPattern, 
		 cpc::string rootDn, 
		 LdapSearchScope scopeSetting,
		 int timeout,
		 int maxsize,
	     bool opErrIgnore=false
		 ) = 0;
		 
		 
		
   /**
   * Destroy a Ldap client.
   *
   * @param handle the client handle
   */
   //virtual int destroy(LdapClientHandle handle) = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

protected:
   /* 
    * The SDK will manage memory life of %LdapManager.
    */
   virtual ~LdapManager() {}

   
};

}//namespace OpenLdap
}//namespace CPCAPI2
#endif
