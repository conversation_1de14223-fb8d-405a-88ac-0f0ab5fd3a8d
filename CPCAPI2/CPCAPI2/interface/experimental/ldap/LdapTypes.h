#pragma once

#if !defined(__CPCAPI2_LDAP_TYPES_H__)
#define __CPCAPI2_LDAP_TYPES_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
  namespace OpenLdap
  {
    typedef unsigned int LdapClientHandle;

    typedef enum LdapEncryption
    {
      LdapEncryption_None = 0,
      LdapEncryption_Ldaps,
      LdapEncryption_Starttls
    } LdapEncryption;

    typedef enum LdapCertStrategy
    {
      LdapCertStrategy_Never  = 0,
      LdapCertStrategy_Hard   = 1,
      LdapCertStrategy_Demand = 2,
      LdapCertStrategy_Allow  = 3,
      LdapCertStrategy_Try    = 4
    } LdapCertStrategy;

    typedef enum LdapSearchScope
    {
      LdapSearchScope_Base = 0,
      LdapSearchScope_OneLevel,
      LdapSearchScope_SubTree,
      LdapSearchScope_Children
    } LdapSearchScope;

    typedef enum LdapErrorType
    {
      LdapNoError = 0,
      LdapConnectionError,
      LdapSearchError,
      LdapNoResults,
      LdapSizeLimitExceeded
    } LdapErrorType;

    typedef enum LdapState
    {
      LdapDisconnected = 0,
      LdapConnecting = 1,
      LdapConnected  = 2
    }LdapState;

    struct LdapClientSettings
    { 
      LdapClientSettings()
      {
          username = "";
          password = "";
          serverUrl = "";
          encryption = LdapEncryption_None;
          cert_strategy = LdapCertStrategy_Never;
          connection_timeout = 0; // 0 - disabled
          chase_referrals = true;
          arec_exclusive = false;
      }
      cpc::string username;
      cpc::string password;
      cpc::string serverUrl;
  
      LdapEncryption encryption;
      LdapCertStrategy cert_strategy; 
      int connection_timeout;
      bool chase_referrals;
      bool arec_exclusive; // Windows setting
    };

    struct LdapDataEntry
    {
       LdapDataEntry()
       {
          displayName = "";
          firstName = "";
          lastName = "";
          softphone = "";
          company = "";
          jobTitle = "";
          department = "";
          workPhone = "";
          officePhone = "";
          homePhone = "";
          mobilePhone = "";
          email = "";
          jabber = "";
          street = "";
          city = "";
          state = "";
          zip = "";
          country = "";
       }
      cpc::string  displayName;
      cpc::string  firstName;
      cpc::string  lastName;
      cpc::string  softphone;
      cpc::string  company;
      cpc::string  jobTitle;
      cpc::string  department;
      cpc::string  workPhone;
      cpc::string  officePhone;
      cpc::string  homePhone;
      cpc::string  mobilePhone;
      cpc::string  email;
      cpc::string  jabber;
      cpc::string  street;
      cpc::string  city;
      cpc::string  state;
      cpc::string  zip;
      cpc::string  country;
    };

    struct LdapDataMap
    {
      LdapDataMap()
      {
         displayName = "";
         firstName = "";
         lastName = "";
         softphone = "";
         company = "";
         jobTitle = "";
         department = "";
         workPhone = "";
         officePhone = "";
         homePhone = "";
         mobilePhone = "";
         email = "";
         jabber = "";
         street = "";
         city = "";
         state = "";
         zip = "";
         country = "";
      }
      cpc::string  displayName;
      cpc::string  firstName;
      cpc::string  lastName;
      cpc::string  softphone;
      cpc::string  company;
      cpc::string  jobTitle;
      cpc::string  department;
      cpc::string  workPhone;
      cpc::string  officePhone;
      cpc::string  homePhone;
      cpc::string  mobilePhone;
      cpc::string  email;
      cpc::string  jabber;
      cpc::string  street;
      cpc::string  city;
      cpc::string  state;
      cpc::string  zip;
      cpc::string  country;
    };

  }//namespace CPCAPI2
}//namespace OpenLdap
#endif
