#pragma once
#ifndef __CPCAPI2_BIEVENTS_TYPES_H__
#define __CPCAPI2_BIEVENTS_TYPES_H__

#include <stdint.h>
#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
   namespace BIEvents
   {
      typedef int32_t     BIEventsHandle;
      typedef cpc::string EventID;

      /**
       * Basic types only (plus string), for the time being.
       */
      typedef enum BIValueType
      {
         BIValueType_bool,
         BIValueType_int,
         BIValueType_double,
         BIValueType_string
      } BIValueType;

      /**
       * A structure to encapsulate a "value", which may take the form of
       * a number of different primitive types.
       */
      typedef struct BIValue
      {
         BIValueType ValueType;   // type of value used
         bool        BoolValue;   // used if ValueType == BIValueType_bool
         int64_t     IntValue;    // used if ValueType == BIValueType_int
         double      DoubleValue; // used if ValueType == BIValueType_double
         cpc::string StringValue; // used if ValueType == BIValueType_string
      } BIValue;

      /**
       * Name/Value pair used in the collection. It will be used by the SDK to
       * properly build a JSON structure to be sent "over the wire".
       */
      typedef struct BIPair
      {
         cpc::string Name;
         BIValue     Value;
      } BIPair;

      /**
       * The portion of data which remains unchanged regardless
       * of the event type.
       */
      typedef struct BIEventHeader
      {
         /**
          * Identifies the source of the event. Should be a registered source.
          * Only the first 5 chars will be used. The naming convention is to
          * use upper case chars and numbers + some special chars.
          */
         cpc::string Source;

         /**
          * Identifies the type of the event. Should be a registered type. Only
          * the first 5 chars will be used. The naming convention is to use
          * upper case chars and numbers + some special chars.
          */
         cpc::string Type;

         /**
          * Optional integer group (defaults to 0). The "Group" is a way to
          * indicate that several events belong together into some kind of
          * category. Events in the same logical category would therefore have
          * the same group number. NB: *This is not the same as an event name
          * (which describes the schema of the event).* Rather it is used for
          * logical groupings of events within an event name schema.
          *
          * NB: These IDs are defined and managed by the application.
          */
         int Group;

         /**
          * Optional context relation (defaults to 0). The "Context" is another
          * number which is in the same logical space as the "Group" field. It
          * generally is set to a pre-existing "Group" number. It is used to
          * define relationships between events. One could think of this field
          * as a "ParentGroup" ID, however it is more of a 1:1 relationship,
          * and thus could be used to build structures other than a tree.
          *
          * NB: These IDs are defined and managed by the application.
          */
         int Context;

         /**
          * Optional "human-readable" summary of up to 96 characters.
          */
         cpc::string Summary;

         /**
          * Timestamp for the event. By default this will be initialized to
          * zero. If the SDK sees that this field is zero, it will fill it with
          * a value at the time of posting.
          */
         int64_t MillisSinceEpoch;

         /**
          * Searchable string labels (aka "tags") which will be searchable from
          * the front-end of the database. This is used to tag events with
          * meta-data that will allow us to find relevant information later.
          */
         cpc::vector< cpc::string > Labels;

         BIEventHeader( void ) : Group( 0 ), Context( 0 ), MillisSinceEpoch( 0 ) {}
      } BIEventHeader;


      typedef cpc::vector< BIPair > BIEventBody;
   }
}

#endif //  __CPCAPI2_BIEVENTS_TYPES_H__
