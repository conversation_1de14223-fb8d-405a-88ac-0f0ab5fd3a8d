#pragma once
#ifndef __CPCAPI2_BIEVENTS_HANDLER_H__
#define __CPCAPI2_BIEVENTS_HANDLER_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include "BIEventsTypes.h"

namespace CPCAPI2
{
   namespace BIEvents
   {
      typedef struct OnErrorEvent
      {
         int64_t     errorCode;
         cpc::string errorMessage;
      } OnErrorEvent;

      typedef struct OnPostSuccessEvent
      {
         EventID id;
      } OnPostSuccessEvent;

      typedef struct OnPostFailureEvent
      {
         EventID id;
      } OnPostFailureEvent;

      typedef struct OnDestroyEvent
      {
         // placeholder in case of more information
      } OnDestroyEvent;

      typedef struct OnServerInfoEvent
      {
         /**
          * "Device Reasonably Unique Identifier", provided by the server
          */
         cpc::string druid;
      } OnServerInfoEvent;

      class BIEventsHandler
      {
      public:
         virtual int onError( const BIEventsHandle& hEvents, const OnErrorEvent& evt ) = 0;
         virtual int onEventSuccess( const BIEventsHandle& hEvents, const OnPostSuccessEvent& evt ) = 0;
         virtual int onEventFailure( const BIEventsHandle& hEvents, const OnPostFailureEvent& evt ) = 0;

         /**
          * Fired when the BIEventsHandle has been properly destroyed and the session termination
          * message was sent to the server.
          */
         virtual int onDestroy( const BIEventsHandle& hEvents, const OnDestroyEvent& evt ) = 0;

         /**
          * Fired when the server provides additional information for the application.
          */
         virtual int onServerInfo( const BIEventsHandle& hEvents, const OnServerInfoEvent& evt ) = 0;
      };

      /**
       * If the application subclasses this instead of implementing BIEventsHandler directly,
       * it will be protected from compilation errors due to changes in the handler interface
       */
      class BIEventsHandlerStub : public BIEventsHandler
      {
      public:
         virtual int onError( const BIEventsHandle& hEvents, const OnErrorEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onEventSuccess( const BIEventsHandle& hEvents, const OnPostSuccessEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onEventFailure( const BIEventsHandle& hEvents, const OnPostFailureEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onDestroy( const BIEventsHandle& hEvents, const OnDestroyEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onServerInfo( const BIEventsHandle& hEvents, const OnServerInfoEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}

#endif /* __CPCAPI2_BIEVENTS_HANDLER_H__ */
