#pragma once
#ifndef __CPCAPI2_BIEVENTS_HELPER_H__
#define __CPCAPI2_BIEVENTS_HELPER_H__

#include "BIEventsManager.h"

#include "cpcstl/string.h"
#include "cpcstl/vector.h"

// String constants which are defined here, will be used for event parameters
//===========================================================================
#define CPCAPI2_BIEVENTS_UIVERB_ACTION                 "Action" // clicked, poked, or selected with a mouse, finger, or stylus
#define CPCAPI2_BIEVENTS_UIVERB_SHOW                   "Show"   // UI element is displayed to the user
#define CPCAPI2_BIEVENTS_UIVERB_HIDE                   "Hide"   // UI element is hidden from the user (possibly disposed)
#define CPCAPI2_BIEVENTS_UIVERB_TEXT                   "Text"   // Some text is entered on the (possibly virtual) keyboard

// Model verbs follow a "CRUD" paradigm
#define CPCAPI2_BIEVENTS_MODELVERB_CREATED             "Created"
#define CPCAPI2_BIEVENTS_MODELVERB_RENAMED             "Renamed"
#define CPCAPI2_BIEVENTS_MODELVERB_UPDATED             "Updated"
#define CPCAPI2_BIEVENTS_MODELVERB_DELETED             "Deleted"

#define CPCAPI2_BIEVENTS_MODELID_REMOTE_SYNC           "RemoteSync"        // Example: add others

#define CPCAPI2_BIEVENTS_SYSTEMVERB_MEMORY_LOW         "MemoryLow"         // OS's (or virtual machine) memory is low
#define CPCAPI2_BIEVENTS_SYSTEMVERB_PERMISSION_REQUEST "PermissionRequest" // App requested permissions from the OS
#define CPCAPI2_BIEVENTS_SYSTEMVERB_PERMISSION_RESULT  "PermissionResult"  // App granted (or denied) permissions

#define CPCAPI2_BIEVENTS_COMMERCEVERB_PURCHASED        "Purchased" // Some item was purchased, or attempted to be purchased

#define CPCAPI2_BIEVENTS_DEBUGVERB_TRACE               "Trace"    // Purely informational messages of little consequence (use sparingly)
#define CPCAPI2_BIEVENTS_DEBUGVERB_DEBUG               "Debug"    // Somewhat more useful messages for debugging field problems
#define CPCAPI2_BIEVENTS_DEBUGVERB_INFO                "Info"     // Relevant Information which is not shown to the user (i.e. protocol messages)
#define CPCAPI2_BIEVENTS_DEBUGVERB_WARNING             "Warning"  // A recoverable Error, which was avoided or dealt with
#define CPCAPI2_BIEVENTS_DEBUGVERB_ERROR               "Error"    // A non-recoverable Error, impacting a portion of the program
#define CPCAPI2_BIEVENTS_DEBUGVERB_CRITICAL            "Critical" // A non-recoverable Error, impacting the entire program
//===========================================================================

namespace CPCAPI2
{
   namespace BIEvents
   {

      // Predefined list of Model IDs
      typedef enum ModelID
      {
      } ModelID_t;

      /**
       * "Sub-interface" under the BIEventsManager which deals explicitly with adding
       * functions to help to make the creation of events more consistent between
       * applications.
       */
      class CPCAPI2_SHAREDLIBRARY_API BIEventsHelper
      {
      public:
         static BIEventsHelper* getInterface( BIEventsManager* bim );

         /**
          * Creates a new, unique group ID, which may be used for the eventGroup and
          * eventContext fields below. The ID is unique per session, so these IDs will
          * be reset in between client restarts.
          */
         virtual int createEventGroupID( void ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param parentID unique ID for the parent UI component, empty if none (root window)
          * @param ID unique ID for the component in this event
          * @param verb the action which was performed on this component. See above definitions.
          * @param text (optional) Textual data associated with the component. This
          *        could be either the text displayed on the component itself, or the
          *        text entered by the user in the case of a text entry field. Set to ""
          *        if there's no text.
          * @param extraInfo a list of name/value pairs which could be used to extend
          *        the information contained in the event (optional). I recommend this is
          *        used sparingly since it represents a deviation from the standard event
          *        definition, however it is provided for those that need it.
          */
         virtual int postUIEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const cpc::string& parentID,
            const cpc::string& ID,
            const char *uiVerb,
            const cpc::string& text,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param modelID the numeric ID of the model. See ModelID_t.
          * @param verb the action which was performed by the model. See above definitions.
          * @param hResource the integer ID ("handle") manipulated by the verb and model
          * @param extraInfo a list of name/value pairs which could be used to extend
          *        the information contained in the event (optional). I recommend this is
          *        used sparingly since it represents a deviation from the standard event
          *        definition, however it is provided for those that need it.
          */
         virtual int postModelEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *modelID,
            const char *modelVerb,
            const int& hResource,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param systemVerb Additional system event specification. See above definitions.
          * @param comment (optional) Additional
          * @param extraInfo a list of name/value pairs which could be used to extend
          *        the information contained in the event (optional). I recommend this is
          *        used sparingly since it represents a deviation from the standard event
          *        definition, however it is provided for those that need it.
          */
         DEPRECATED virtual int postSystemEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *systemVerb,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param verb the action taken by this commerce event. See above definitions.
          * @param hResource the item on which the action applies (TODO: is 'int' good enough?)
          * @param extraInfo a list of name/value pairs which could be used to extend
          *        the information contained in the event (optional). I recommend this is
          *        used sparingly since it represents a deviation from the standard event
          *        definition, however it is provided for those that need it.
          */
         virtual int postCommerceEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *commerceVerb,
            const int& hResource,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param labels A list of strings, which function similarly to a gmail label
          *        (or twitter hashtag)
          * @param extraInfo a list of name/value pairs. In the case of SDK events, this
          *        will be the main way to set information, since most of the data will
          *        not have a large degree of overlapping/common fields.
          */
         DEPRECATED virtual int postSDKEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const cpc::vector< cpc::string >& labels,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;

         /**
          * @param eventSource the "Event Source" as defined by the EVT-API protocol and
          *        registered to the server.
          * @param verb the additional specification for the debug event. See above definitions.
          * @param threadID the "ID" of the thread which generated the DebugEvent
          * @param message a textual message to include along with the event
          * @param stackTrace (optional) program "stack trace" to log at the same time.
          *        NB: NOT the same as EventVerb_Trace. Set to "" if there's no trace.
          * @param extraInfo a list of name/value pairs which could be used to extend
          *        the information contained in the event (optional). I recommend this is
          *        used sparingly since it represents a deviation from the standard event
          *        definition, however it is provided for those that need it.
          */
         DEPRECATED virtual int postDebugEvent(
            const BIEventsHandle& hEvents,
            const cpc::string& eventSource,
            const char *debugVerb,
            int threadID,
            const cpc::string& message,
            const cpc::string& stackTrace,
            const BIEventBody& extraInfo,
            EventID& outEventID,
            const int eventGroup = 0,
            const int eventContext = 0,
            const cpc::string& eventSummary = "" ) = 0;
      };
   }
}

#endif // __CPCAPI2_BIEVENTS_HELPER_H__
