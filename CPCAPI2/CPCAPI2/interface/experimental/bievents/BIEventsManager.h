#pragma once
#ifndef __CPCAPI2_BIEVENTS_MANAGER_H__
#define __CPCAPI2_BIEVENTS_MANAGER_H__

#include "cpcapi2defs.h"
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include "BIEventsTypes.h"

namespace CPCAPI2
{
   // Forward declaration
   class Phone;

   namespace BIEvents
   {
      // Forward declaration
      class BIEventsHandler;

      /**
       * Settings which are associated with each BI session. These are probably
       * provisioned through the Stretto platform (but not necessarily so).
       */
      typedef struct BIEventsSettings
      {
         /**
          * HTTP(s) location of the BI server
          */
         cpc::string serverLocationURL;

         /**
          * Optional HTTP auth username (empty string by default)
          */
         cpc::string httpAuthUser;

         /**
          * Optional HTTP auth password (empty string by default)
          */
         cpc::string httpAuthPasswd;
         
         /**
          * Verbose logging of HTTP requests/responses
          */
         bool httpVerboseLogging;

         /**
          * "BI" Authentication username
          */
         cpc::string authUser;

         /**
          * "BI" Authentication password
          */
         cpc::string authPass;

         /**
          * Application Loadbuild information (for informational purposes)
          */
         cpc::string buildStamp;

         /**
          * Link to stretto username or Identifier
          */
         cpc::string strettoUserNameOrID;

         /**
          * This value controls how often the reports are sent. As long as each
          * event arrives within slidingWindowDeltaMillis of each other, they
          * will be batched together into a single post to the server. A larger
          * value will result in greater bandwidth efficiency, a smaller value
          * will result in less latency.
          */
         unsigned int slidingWindowDeltaMillis;

         /**
          * The sliding window has a second trigger, the amount of overall
          * time.  Events will continue to be batched together as described
          * above, until the max millis time is hit, at which point the events
          * will be sent, regardless if more events are arriving within the
          * delta. Think of this as a release valve to ensure something is sent
          * regularly, in the (hopefully rare) event of an unrelenting event
          * stream.
          */
         unsigned int slidingWindowMaxMillis;

         /**
          * The number of files which the cache will keep in file rotation
          * (default to 4)
          */
         int cacheNumFiles;

         /**
          * The size file limit, in bytes, for each cache file (the total size
          * used will be cacheFileSize * cacheNumFiles).
          */
         int cacheFileSize;

         /**
          * Application-provided directory in which the application has
          * read-write privileges. The SDK BI Events module will store a
          * (hopefully small) cache file at this location which will be used to
          * temporarily house event information during periods of
          * disconnected-ness.
          */
         cpc::string cacheDirectory;

         /**
          * Application-provided base filename for the cache files. If not
          * provided, "ecache" will be used.
          */
         cpc::string cacheFileName;

         /**
          * The number of events which will be bundled into a single message
          * sent to the server side (by default this is 100 at most). Note
          * that the actual number of events may be less than this but never
          * more.
          */
         int chunkSize;

         // Default ctor for initializing items
         BIEventsSettings()
            : httpVerboseLogging( false ),
              slidingWindowDeltaMillis( 5000 ),
              slidingWindowMaxMillis( 60000 ),
              cacheNumFiles( 4 ),
              cacheFileSize( 4000000 ),
              chunkSize( 100 ) {}

      } BIEventsSettings;

      /**
       * The BIEventsManager is used for posting events to the "BI Server", (BI
       * stands for "Business Intelligence". The application posts events which
       * are of interest for marketing and/or business development types, in
       * order to determine which direction to move the product in the future,
       * for targeted marketing campaigns, and etc.
       */
      class CPCAPI2_SHAREDLIBRARY_API BIEventsManager
      {
      public:
         /**
          * Get a reference to the %BIEventsManager interface.
          */   
         static BIEventsManager* getInterface(Phone* cpcPhone);

         /**
          * Creates a new session, for use with the following APIs.
          */
         virtual BIEventsHandle create( void ) = 0;

         /**
          * Pushes the settings down to the supplied session. Also initializes
          * some resources based on the settings.
          */
         virtual int configureSettings(
            const BIEventsHandle& hEvents,
            const struct BIEventsSettings& settings ) = 0;

         /**
          * Method to add a listener to the BI API, the main reason to have
          * a listener is to a) know when a particular event was delivered,
          * or b) to know if there was some error during delivery. If neither
          * of these situations is of interest, a listener may optionally
          * not be installed.
          * 
          * @param hEvents the event source handle to listen to
          * @param notificationListener
          */
         virtual int setHandler(
            const BIEventsHandle& hEvents,
            BIEventsHandler * notificationHandler ) = 0;

         /**
          * Once enable is invoked by the application, the SDK will try to
          * connect to the B.I. server and send reports. This allows the
          * application to control the periods under which the SDK will
          * send information through use of enable/disable. The first time
          * enable is called, the connection and authentication steps
          * will be performed.
          *
          * @param hEvents the context which will be used
          */
         virtual int enable( const BIEventsHandle& hEvents ) = 0;

         /**
          * The SDK will stop sending events to the B.I. server. Events
          * will instead be stored locally. disable<=>enable can be toggled
          * multiple times within the same session.
          */
         virtual int disable( const BIEventsHandle& hEvents ) = 0;
         
         /**
          * Closes the BIEventsHandle session. recommended to do this for
          * a proper shutdown. Results in an onDestroy event fired in the
          * handler to indicate when the operation is finished. It is important
          * to wait for the event to ensure the proper messages are sent to
          * the server.
          *
          * NB: the above statement implies that the handler should not be
          * removed prior to calling destroy.
          *
          * @param hEvents the event source to destroy
          */
         virtual int destroy( const BIEventsHandle& hEvents ) = 0;

         /**
          * Posts an event of interest (a "business event" to the BI server).
          * An event receipt will be returned which can be used to track the
          * progress of the event, or match it to a failure event, if need be.
          *
          * An event "ID" will be returned, the application can use
          * this to track the process of events. Note that the events may be
          * persisted across restarts, so it is possible to receive events
          * containing IDs for a previous run of the application.
          */
         virtual int postEvent(
            const BIEventsHandle& hEvents,
            const BIEventHeader& eventHeader,
            const BIEventBody& eventBody,
            EventID& outEventID ) = 0;

         /**
          * Erases any events which are stored in the cache. This is mainly
          * used by the unit tests, however it could be used in the event
          * that the cache file becomes damaged, or some other situation(s).
          */
         virtual int purgeCache( const BIEventsHandle& hEvents ) = 0;

         /**
          * The blocking modes for the process() function.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Method which must be called by the application in order to
          * 'service' events coming from the handler.
          */
         virtual int process(int timeout) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %BIEventsManager.
          */
         virtual ~BIEventsManager() {}
      };

   }
}

#endif // __CPCAPI2_BIEVENTS_MANAGER_H__
