#pragma once

#ifndef CPCAPI2_PUSHTOTALK_HANDLER_H
#define CPCAPI2_PUSHTOTALK_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "PushToTalkTypes.h"
#include <phone/NetworkChangeManager.h>

namespace CPCAPI2
{

namespace PushToTalk
{

enum PttSessionStateType
{
   PttSessionState_Idle,
   PttSessionState_Initiated,
   PttSessionState_Active,
   PttSessionState_Talking,
   PttSessionState_Ending,
   PttSessionState_InitialActive // Currently only used internally
};

// DEPRECATED - Use PttServiceStatusChangedEvent instead
struct PttServiceStartedEvent
{
   DEPRECATED unsigned int endpointsDiscovered;
   DEPRECATED unsigned int discoveryRequestsSent;
   DEPRECATED unsigned int discoveryResponsesReceived;
   DEPRECATED bool startupSuccessful;

   PttServiceStartedEvent() : endpointsDiscovered(0), discoveryRequestsSent(0), discoveryResponsesReceived(0), startupSuccessful(false) {}
   void reset()
   {
      /*
      endpointsDiscovered = 0;
      discoveryRequestsSent = 0;
      discoveryResponsesReceived = 0;
      startupSuccessful = false;
      */
   }
};

struct PttServiceStatusChangedEvent
{
/* WAN
                                               +-------------------------+
                                               |                         |
                                               |                         |
+--------------------------------------------->+         DISABLED        |
|                                              |                         |
|                                              |                         |
|                                              +------------+------------+
|                                                           |
|                                                           |
|                                                           |  startService
|                                                           |                  ConferenceConnectorHandler::
|                                                           v                  onServiceConnectionStatusChanged
|                                              +------------+------------+           (Connection Failure)
|                                              |                         +---------------------------------------------+
|                                              |                         |                                             |
|                                              |        CONNECTING       +---------------------------->+               |
|                                              |                         |  shutdownService            |               |
|                                              |                         |                             |               |
|                                              +------------+------------+                             |               |
|                                                           |                                          |               |
|                                                           |  ConferenceConnectorHandler::            |               |
|                                                           |  onServiceConnectionStatusChanged        |               |
|                                                           |          (Authenticating)                |               |
|             ConferenceConnectorHandler::                  v                                          |               |
|             onServiceConnectionStatusChanged +------------+------------+                             |               |
|                  (Authentication Failure)    |                         |                             |               |
|                                              |                         |                             |               |
|                +<----------------------------+      AUTHENTICATING     +---------------------------->+               |
|                |                             |                         |  shutdownService            |               |
|                |                             |                         |                             |               |
|                |                             +------------+------------+                             |               |
|                |                                          |                                          |               |
|                |                                          |  ConferenceConnectorHandler::            |               |
|                |                                          |  onServiceConnectionStatusChanged        |               |
|                |                                          |            (Connected)                   |               |
|                |                                          v                                          |               |
|                |                             +------------+------------+                             |               |
|                |                             |                         |                             |               |
|                |                             |                         |                             |               |
|                |                             |        CONNECTED        |                             |               |
|                |                             |                         |                             |               |
|                |                             |                         |                             |               |
|                |                             +------------+------------+                             |               |
|                |                                          |                                          |               |
|                |                                          |  ConferenceConnectorHandler::            |               |
|                |                                          |  onConferenceListUpdated                 |               |
|                |                                          |                                          |               |
|                |                                          v                                          |               |
|                |                             +------------+------------+                             |               |
|                |                             |                         |                             |               |
|                |                             |                         |                             |               |
|                |                             |          READY          |                             |               |
|                |                             |                         |                             |               |
|                |                             |                         |                             |               |
|                |                             +------------+------------+                             |               |
|                |                                          |                                          |               |
|                |                                          |                                          |               |
|                |                                          | shutdownService                          |               |
|                |                                          |                                          |               |
|                |                                          v                                          |               |
|                |                             +------------+------------+                             |               |
|                |                             |                         |                             |               |
|                |                             |                         |                             |               |
|                |                             |      DISCONNECTING      +<----------------------------+               |
|                |                             |                         |                                             |
|                |                             |                         |                                             |
|                |                             +------------+------------+                                             |
|                |                                          |                                                          |
|                |                                          |                                                          |
|                |                                          |                                                          |
|                |                                          |                                                          |
|                |                                          v                                                          |
|                |                             +------------+------------+                                             |
|                |                             |                         |                                             |
|                |                             |                         |                                             |
+<---------------------------------------------+       DISCONNECTED      +<--------------------------------------------+
|                |             shutdownService |                         |
|                |                             |                         |
|                |                             +-------------------------+
|                |
|                |
|                |
|                |
|                |
|                |                             +-------------------------+
|                +---------------------------->+                         |
|                                              |                         |
+<---------------------------------------------+         FAILURE         |
                               shutdownService |                         |
                                               |                         |
                                               +-------------------------+
*/

/* LAN

     startService (failed)
      - Invalid Transport                     +-------------------------+
      - Failure Initializing Unicast Receiver |                         |
      - Failure Initializing Unicast Sender   |                         |
+---------------------------------------------+         DISABLED        +<----------------------------------------------------------+
|                                             |                         |                                                           |
|               +<----------------------------+                         |                                                           |
|               |         Discovery Disabled  +------------+------------+                                                           |
|               |                                          |                                                                        |
|               |                                          |                                                                        |
|               |                                          |  startService (success)                                                |
|               |                                          |                                                                        |
|               |                                          v                                                                        |
|               |                             +------------+------------+                                                           |
|               |                             |                         +<--------------------------------------------+             |
|               |                             |                         |                                             |             |
|               |                             |        CONNECTING       +---------------------------->+               |             |
|               |                             |                         |                             |               |             |
|               |                             |                         |                             |               |             |
|               |                             +------------+------------+                             |               |             |
|               |                                          | Discovery Process Completed:             |               |             |
|               |                                          |  - SenderHandler::onSenderStarted        |               |             |
|               |                                          |  - SenderHandler::onOutgoingSendCompleted|               |             |
|               |                                          |  - Discovery Endpoint Report Sent        |               |             |
|               |                                          v                                          |               |             |
|               |                             +------------+------------+                             |               |             |
|               |                             |                         |                             |               |             |
|               |                             |                         |                             |               |             |
|               +---------------------------->+     CONNECTED/READY     |                             |               |             |
|                                             |                         |                             |               |             |
|                                             |                         |                             |               |             |
|                                             +------------+------------+                             |               |             |
|                                                          |                                          |               |             |
|                                                          |  shutdownService                         |               |             |
|                                                          |  onNetworkChange                         |               |             |
|                                                          |                                          |               |             |
|                                                          v                                          |               |             |
|                                             +------------+------------+                             |               |             |
|                                             |                         |  shutdownService            |               |             |
|                                             |                         |  onNetworkChange            |               |             |
|                                             |      DISCONNECTING      +<----------------------------+               |             |
|                                             |                         |                                             |             |
|                                             |                         |                                             |             |
|                                             +------------+------------+                                             |             |
|                                                          |                                                          |             |
|                                                          |                                                          |             |
|                                                          | Transport Shutdown                                       |             |
|                                                          |                                                          |             |
|                                                          v                                                          |             |
|                                             +------------+------------+   Disconnect Triggered to                   |             |
|                                             |                         |   handle Network Change or if               |             |
|                                             |                         |   onNetworkChange is called                 |             |
+-------------------------------------------->+       DISCONNECTED      +---------------------------------------------+             |
                                              |                         |                                                           |
                                              |                         +-----------------------------------------------------------+
                                              +-------------------------+   Disconnect Triggered to
                                                                            Shutdown Service or if
                                             Service stays in disconnected  shutdownService is called
                                             state if network change is to
                                             an invalid transport or if an
                                             error occurs when restarting
                                             the service

*/

   enum Status
   {
      Status_Disabled,                /** Service disabled as startService has not been called or shutdownService has been called and handled */
      Status_Connecting,              /** Service connecting as startService has been called or a network change has occurred, indicates attempt to establish connection for wan ptt, or undergoinggoing peer discovery for lan ptt */
      Status_Authenticating,          /** Service going through authentication with server - wan ptt only */
      Status_Connected,               /** Service enabled, indicates connection established with server for wan ptt, and discovery completed for lan ptt */
      Status_Disconnecting,           /** Service disconnecting, shutdownService has been called or network change has occurred. */
      Status_Disconnected,            /** Service disconnected but not disabled, sdk will attempt to reestablish connection if possible, e.g. handling network changes */
      Status_Failure,                 /** Service could not be activated and will require manual intervention, sdk will not attempt to reestablish connection, e.g. handling authentication failures */
      Status_Ready                    /** Service enabled and connected, indicates ptt conferences in-sync with server for wan ptt */
   };

   enum Reason
   {
      Reason_None,                    /** No explicit reason for service status change */
      Reason_ConnectionFailure,       /** Service status changed due to a connection failure, e.g. no network, no response to dns queries, no response to connection from server, etc. */
      Reason_AuthenticationFailure,   /** Service status changed due to a authentication failure */
      Reason_LocalError,              /** Service status changed due to local errors */
      Reason_ServerError,             /** Service status changed due to error response from server */
      Reason_NetworkChange            /** Service status changed because client went through a network change */
   };

   Status status;
   Reason reason;
   NetworkTransport transport;
   cpc::string statusDescription;
};

struct PttEndpointListEvent
{
   PttIdentity localIdentity;
   cpc::vector<PttIdentity> endpoints; // Endpoints from the full endpoint list included in this event
   unsigned int totalEndpointCount;    // Endpoint count of the full endpoint list
   unsigned int offset;                // Where this list starts in the full endpoint list
};

struct PttServiceConfiguredEvent
{
   PushToTalkServiceSettings settings;
};

struct PttSessionStateChangedEvent
{
   PushToTalkServiceHandle service;
   PttSessionStateType currentState;
   PttSessionStateType previousState;
   cpc::string channelId;
   cpc::vector<PttIdentity> recipients;
   unsigned int connectedCalls;
   unsigned int mediaInfoSent;
   unsigned int mediaInfoReceived;
   unsigned int totalCalls;

   PttSessionStateChangedEvent() :
      currentState(PttSessionState_Idle),
      previousState(PttSessionState_Idle),
      channelId(""),
      connectedCalls(0),
      mediaInfoSent(0),
      mediaInfoReceived(0),
      totalCalls(0) {}
};

struct PttIncomingCallEvent
{
   PttSessionStateType currentState;
   PttIdentity callerIdentity;
   cpc::string channelId;
   PushToTalkServiceHandle service;
   PushToTalkSessionHandle incomingPtt;

   PttIncomingCallEvent() :
   currentState(PttSessionState_Idle),
   channelId(""),
   service(0),
   incomingPtt((PushToTalkSessionHandle)0) {}

   // Answer-Mode Attributes
   /*
   bool autoAnswer;
   bool privileged;
   bool required;
   */
};

enum PttSessionError
{
   PttSessionError_None,
   PttSessionError_SetupTimeout,
   PttSessionError_ConnectionTimeout,
   PttSessionError_CallOverride,
   PttSessionError_ChannelOverride,
   PttSessionError_OfferResponseTimeout,
   PttSessionError_InitRecordingFailure
#ifdef CPCAPI2_AUTO_TEST
   , PttSessionError_UnicastDiscard
#endif
};

struct PttSessionErrorEvent
{
   PttSessionError errorCode;
   PttIdentity callerIdentity;
   cpc::string channelId;
   PushToTalkServiceHandle service;

   PttSessionErrorEvent() : channelId(""), errorCode(PttSessionError_None) {}
};

enum PttReceiverEndReasonType
{
   PttReceiverEndReasonType_None,                    /** No explicit reason */
   PttReceiverEndReasonType_SetupTimeout,            /** No response from app timeout */
   PttReceiverEndReasonType_WaitForAnswerTimeout,    /** No response from sender timeout */
   PttReceiverEndReasonType_App,                     /** Ended by app */
   PttReceiverEndReasonType_Reject,                  /** Rejected */
   PttReceiverEndReasonType_MediaInactive,           /** No media activity */
   PttReceiverEndReasonType_NetworkChange,           /** Network change */
   PttReceiverEndReasonType_ChannelOverride          /** Channel override */
};

struct PttReceiverDisconnectedEvent
{
   PttIdentity receiverIdentity;
   cpc::string channelId;
   unsigned int connectedCalls;
   unsigned int totalCalls;
   PttSessionStateType currentState;
   PttReceiverEndReasonType reason;
   PushToTalkServiceHandle service;

   PttReceiverDisconnectedEvent() :
      channelId(""),
      connectedCalls(0),
      totalCalls(0),
      currentState(PttSessionState_Idle),
      reason(PttReceiverEndReasonType_None) {}
};

class PushToTalkHandler
{

public:

   DEPRECATED virtual int onPttServiceStarted(PushToTalkServiceHandle service, const PttServiceStartedEvent& args) { return 0; }
   virtual int onPttServiceStatusChanged(PushToTalkServiceHandle service, const PttServiceStatusChangedEvent& args) = 0;
   virtual int onPttEndpointList(PushToTalkServiceHandle service, const PttEndpointListEvent& args) = 0;
   virtual int onPttServiceConfigured(PushToTalkServiceHandle service, const PttServiceConfiguredEvent& args) { return 0; }
   virtual int onPttSessionStateChanged(PushToTalkSessionHandle ptt, const PttSessionStateChangedEvent& args) = 0;
   virtual int onPttIncomingCall(PushToTalkSessionHandle ptt, const PttIncomingCallEvent& args) = 0;
   virtual int onPttSessionError(PushToTalkSessionHandle ptt, const PttSessionErrorEvent& args) = 0;
   virtual int onPttReceiverDisconnected(PushToTalkSessionHandle ptt, const PttReceiverDisconnectedEvent& args) = 0;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_HANDLER_H
