#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MANAGER_H
#define CPCAPI2_PUSHTOTALK_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "PushToTalkTypes.h"

namespace CPCAPI2
{

class Phone;

namespace PushToTalk
{

class PushToTalkHandler;

/**
===================================================================================================
PTT SENDER
                                          +----------------+
                                          |                |
                                          |    Idle        +<---+
                                          |                |    |
                                          +-------+--------+    |
                                                  |             |
                                         start()  |             |
                                                  v             |
                                          +-------+--------+    |
                                          |                |    |
                                          |    Initiated   |    |
                                          |                |    |
                                          +-------+--------+    |
                       audio input device ready,  |             |
                       INVITE sessions connected  |             |
                                                  v             |
+----------------+    startTalkSpurt()    +-------+--------+    |
|                +<-----------------------+                |    |
|   Talking      |                        |    Active      |    |
|                +----------------------->+                |    |
+-------+--------+     endTalkSpurt()     +-------+--------+    |
        |                                         |             |
        |                                  end()  |             |
  end() |                                         v             |
        |                                 +-------+--------+    |  audio playout finished,
        |                                 |                |    |  BYEs sent
        +-------------------------------->+    Ending      +----+
                                          |                |
                                          +----------------+


==================================================================================================
PTT RECEIVER

                                                       +----------------+
                                                       |                |
                                                       |     Idle       +<---+
                                                       |                |    |
                                                       +-------+--------+    |
                                                               |             |
                                     incoming INVITE received  |             |
                                                               v             |
                                                       +-------+--------+    |
                                                       |                |    |
                                                       |   Initiated    |    |
                                                       |                |    |
                                                       +-------+--------+    |
                                    audio input device ready,  |             |
                                    INVITE sessions connected  |             | audio playout finished
                                                               v             |
             +----------------+    startTalkSpurt()    +-------+--------+    |
             |                +<-----------------------+                |    |
             |   Talking      |                        |    Active      |    |
             |                +----------------------->+                |    |
             +-------+--------+     endTalkSpurt()     +-------+--------+    |
                     |                                         |             |
                     |                   end() / received BYE  |             |
end() / received BYE |                                         v             |
                     |                                 +-------+--------+    |
                     |                                 |                |    |
                     +-------------------------------->+    Ending      +----+
                                                       |                |
                                                       +----------------+

*/
class CPCAPI2_SHAREDLIBRARY_API PushToTalkManager
{

public:

   /**
    * Get a reference to the %PushToTalkManager interface.
   */
   static PushToTalkManager* getInterface(Phone* cpcPhone);

   /**
    * Instantiate the PTT services.
   */
   virtual PushToTalkServiceHandle createPttService() = 0;

   /**
    * Set the PTT event handler to receive the PTT events.
   */
   virtual int setHandler(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler) = 0;

   virtual int process(unsigned int timeout) = 0;
   virtual void interruptProcess() = 0;

   /**
    * Configure the PTT services.
   */
   virtual int configureService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings) = 0;

   /**
    * Async query for the discovered endpoints. The endpoint-list is provided in the PttEndpointListEvent event.
   */
   virtual int queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;

   /**
    * Enable or disable the endpoint-list auto-update. Can be called even after the service has been started.
    * Will trigger a PttEndpointListEvent event(s) whenever there is a change in the endpoint-list.
   */
   virtual int configureEndpointListAutoUpdate(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate) = 0;

   /**
    * Set the list of subscribed channels (only needed for server-based mode).
    */
   virtual int setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels) = 0;

   /**
    * Start up the PTT services, triggers start of the unicast receiver and initiates the endpoint discovery.
   */
   virtual int startService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;

   /**
    * Shutdown the PTT services, triggers shutdown of the unicast receiver, and disables the PTT event handler callbacks
   */
   virtual int shutdownService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;

   /**
    * Create the PTT session, returns the PTT session handle, required for all session specific PTT interfaces.
   */
   virtual PushToTalkSessionHandle createPttSession(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;

   /**
    * Add a receipient to a particular PTT session. Can be called multiple times to add additional users in the PTT group.
   */
   virtual int addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity) = 0;

   /**
    * Specify the channel for a particular PTT session.
   */
   virtual int setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel) = 0;

   /**
    * Start the PTT session, triggers the PTT initiate request to be sent on the unicast port, individual calls are
    * established on the same peer connection based on the client sdp offers received in response to the initiate request.
    *
    * Called after the PTT channel or the recipient list has been specified.
   */
   virtual int start(PushToTalkSessionHandle ptt) = 0;

   /**
    * End the PTT session, triggers the PTT end request to be sent on the unicast port.
   */
   virtual int end(PushToTalkSessionHandle ptt) = 0;

   /**
    * Expected to be called  right after calling start (i.e. PTT channel has already been selected) but before calling starting to speak. Equivalent to a PTT key-press.
   */
   virtual int startTalkSpurt(PushToTalkSessionHandle ptt) = 0;

   /**
    * Expected to be called when the user has finished speaking. Equivalent to a PTT key-release.
   */
   virtual int endTalkSpurt(PushToTalkSessionHandle ptt) = 0;

   /**
    * Used to reject an incoming PTT call, results in the SDK discarding the ptt session
   */
   virtual int reject(PushToTalkSessionHandle ptt) = 0;

   /**
    * Used to accept an incoming PTT call, triggers the SDK to send an SDP offer to the caller
   */
   virtual int accept(PushToTalkSessionHandle ptt) = 0;

   DEPRECATED virtual int setAudioInputDevice(unsigned int deviceId) = 0;
   DEPRECATED virtual int setAudioOutputDevice(unsigned int deviceId) = 0;

   /**
    * Sets the delay after the end of a call before the audio device is closed.
    * audioDeviceCloseDelay: delay in milliseconds
    */
   virtual int setAudioDeviceCloseDelay(int audioDeviceCloseDelay) = 0;

   static int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<PushToTalkServiceSettings>& outAccountSettings);

   virtual int mutePttSession(PushToTalkSessionHandle ptt) = 0;
   virtual int unmutePttSession(PushToTalkSessionHandle ptt) = 0;
   virtual int mutePttService(PushToTalkServiceHandle service) = 0;
   virtual int unmutePttService(PushToTalkServiceHandle service) = 0;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MANAGER_H
