#pragma once

#ifndef CPCAPI2_PUSHTOTALK_TYPES_H
#define CPCAPI2_PUSHTOTALK_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

#define PTT_MULTICAST_DEFAULT_ADDRESS                                     "***********"
#define PTT_MULTICAST_DEFAULT_PORT                                        30001
#define PTT_UNICAST_DEFAULT_PORT                                          0
#define PTT_WEBSOCKETSERVER_HTTP_PORT                                     2114
#define PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT                            50
#define PTT_SESSION_MEDIA_INACTIVITY_INTERVAL_SECONDS                     5
#define PTT_SERVICE_SIGNALLING_DSCP                                       26 // AF31
#define PTT_SERVICE_MEDIA_DSCP                                            46 // EF

namespace CPCAPI2
{

namespace PushToTalk
{

typedef unsigned int PushToTalkServiceHandle;
typedef unsigned int PushToTalkSessionHandle;

enum PttServiceType
{
   PttServiceType_LAN,           // PTT client-based service localized to LAN environment
   PttServiceType_WAN            // PTT server-based service across multiple networks
};

enum PttIdentityType
{
   PttIdentityType_SIP,
   PttIdentityType_XMPP
};

struct PttIdentity
{
   PttIdentityType identityType;
   cpc::string userName;
   cpc::string displayName;
   cpc::string ipAddress; // Debug only, not required when provisioning identity
   cpc::string getIdentityString() const;

   PttIdentity() : identityType(PttIdentityType_XMPP), userName(""), displayName(""), ipAddress("") {}
   PttIdentity(PttIdentityType identityType_, cpc::string userName_, cpc::string displayName_, cpc::string ipAddress_ = "") : identityType(identityType_), userName(userName_), displayName(displayName_), ipAddress(ipAddress_) {}
};

struct PushToTalkIpAddressRange
{
   cpc::string ipAddrStart;
   cpc::string ipAddrEnd;

   PushToTalkIpAddressRange() : ipAddrStart(""), ipAddrEnd("") {}
};

struct PushToTalkServiceSettings
{
   PttServiceType serviceType;
   cpc::vector<PttIdentity> localIdentities;
   cpc::vector<cpc::string> subscribedChannels;
   DEPRECATED PttIdentity senderIdentity;
   DEPRECATED cpc::string multicastAddress;
   DEPRECATED int multicastPort;
   cpc::vector<PushToTalkIpAddressRange> unicastIpRanges;
   cpc::string unicastBindAddress;
   int unicastPort;
   DEPRECATED int httpPort;
   bool unicastDiscoveryEnabled;
   bool keepAliveEnabled;
   bool endpointListAutoUpdateEnabled;
   unsigned int endpointListFetchLimit;
   cpc::string authServiceAddress;
   cpc::string confServiceAddress;
   cpc::string username;
   cpc::string password;
   cpc::string authServiceApiKey;
   bool ignoreCertVerification = false;
   int mediaInactivityIntervalSeconds = PTT_SESSION_MEDIA_INACTIVITY_INTERVAL_SECONDS;
   int signallingDscp = PTT_SERVICE_SIGNALLING_DSCP; // AF31
   int mediaDscp = PTT_SERVICE_MEDIA_DSCP; // EF

   cpc::string getPrimaryLocalIdentity() const;

#ifdef ANDROID
   PushToTalkServiceSettings() : serviceType(PttServiceType_LAN), multicastAddress(""), multicastPort(0), unicastPort(0), httpPort(0), unicastDiscoveryEnabled(true), keepAliveEnabled(true), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) {}
   DEPRECATED PushToTalkServiceSettings(PttIdentity senderIdentity_, cpc::string multicastAddress_, int multicastPort_, int httpPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(multicastAddress_), multicastPort(multicastPort_), unicastPort(0), httpPort(httpPort_), unicastDiscoveryEnabled(true), keepAliveEnabled(true), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
   DEPRECATED PushToTalkServiceSettings(PttIdentity senderIdentity_, int unicastPort_, int httpPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(""), multicastPort(0), unicastPort(unicastPort_), httpPort(httpPort_), unicastDiscoveryEnabled(true), keepAliveEnabled(true), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
   PushToTalkServiceSettings(PttIdentity senderIdentity_, int unicastPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(""), multicastPort(0), unicastPort(unicastPort_), httpPort(0), unicastDiscoveryEnabled(true), keepAliveEnabled(true), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
#else
   PushToTalkServiceSettings() : serviceType(PttServiceType_LAN), multicastAddress(""), multicastPort(0), unicastPort(0), httpPort(0), unicastDiscoveryEnabled(true), keepAliveEnabled(false), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) {}
   DEPRECATED PushToTalkServiceSettings(PttIdentity senderIdentity_, cpc::string multicastAddress_, int multicastPort_, int httpPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(multicastAddress_), multicastPort(multicastPort_), unicastPort(0), httpPort(httpPort_), unicastDiscoveryEnabled(true), keepAliveEnabled(false), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
   DEPRECATED PushToTalkServiceSettings(PttIdentity senderIdentity_, int unicastPort_, int httpPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(""), multicastPort(0), unicastPort(unicastPort_), httpPort(httpPort_), unicastDiscoveryEnabled(true), keepAliveEnabled(false), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
   PushToTalkServiceSettings(PttIdentity senderIdentity_, int unicastPort_) : serviceType(PttServiceType_LAN), senderIdentity(senderIdentity_), multicastAddress(""), multicastPort(0), unicastPort(unicastPort_), httpPort(0), unicastDiscoveryEnabled(true), keepAliveEnabled(false), endpointListAutoUpdateEnabled(false), endpointListFetchLimit(PTT_UNICAST_ENDPOINT_QUERY_FETCH_LIMIT) { localIdentities.push_back(senderIdentity_); }
#endif
};

}

}

#endif // CPCAPI2_PUSHTOTALK_TYPES_H
