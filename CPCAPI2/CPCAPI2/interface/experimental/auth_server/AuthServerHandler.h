#pragma once

#if !defined(CPCAPI2_AUTH_SERVER_HANDLER_H)
#define CPCAPI2_AUTH_SERVER_HANDLER_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace AuthServer
{

struct AuthServerStartedEvent
{
};

/**
*/
class AuthServerHandler
{
public:

   virtual int onAuthServerStarted(const AuthServerStartedEvent& evt) { return kSuccess; };

   virtual ~AuthServerHandler() {}
};

}
}

#endif // CPCAPI2_AUTH_SERVER_HANDLER_H
