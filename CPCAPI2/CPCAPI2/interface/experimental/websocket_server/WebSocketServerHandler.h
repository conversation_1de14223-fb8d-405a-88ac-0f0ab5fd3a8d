#pragma once

#if !defined(WEBSOCKET_SERVER_HANDLER_H)
#define WEBSOCKET_SERVER_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "WebSocketServerTypes.h"

namespace CPCAPI2
{
namespace WebSocketServer
{
   enum ServerState
   {
      ServerState_Stopped = 0,
      ServerState_Running
   };
   struct ConnectionOpenedEvent
   {
      ConnectionHandle connection;
      cpc::string requestHost;
      cpc::string requestPort;
      cpc::string requestResource;
      ConnectionOpenedEvent() {}
      ConnectionOpenedEvent(ConnectionHandle c) : connection(c) {}
   };
   struct ConnectionClosedEvent
   {
      ConnectionHandle connection;
      ConnectionClosedEvent() {}
      ConnectionClosedEvent(ConnectionHandle c) : connection(c) {}
   };
   struct MessageReceivedEvent
   {
      ConnectionHandle connection;
      cpc::string data;
      MessageReceivedEvent() {}
      MessageReceivedEvent(ConnectionHandle c, cpc::string d) : connection(c), data(d) {}
   };
   struct ServerStateChangeEvent
   {
      ServerState currentState;
      cpc::string message;
      ServerStateChangeEvent() {}
      ServerStateChangeEvent(ServerState s, cpc::string m) : currentState(s), message(m) {}
   };
   struct ErrorEvent
   {
      ConnectionHandle connection;
      cpc::string errorText;
      ErrorEvent() {}
      ErrorEvent(ConnectionHandle c, cpc::string e) : connection(c), errorText(e) {}
   };

class WebSocketServerHandler
{
public:
   virtual int onConnectionOpened(WebSocketServerHandle handle, const ConnectionOpenedEvent& evt) = 0;
   virtual int onConnectionClosed(WebSocketServerHandle handle, const ConnectionClosedEvent& evt) = 0;
   virtual int onMessageReceived(WebSocketServerHandle handle, const MessageReceivedEvent& evt) = 0;
   virtual int onServerStateChange(WebSocketServerHandle handle, const ServerStateChangeEvent& evt) = 0;
   virtual int onError(WebSocketServerHandle handle, const ErrorEvent& evt) = 0;
};

}
}

#endif // WEBSOCKET_SERVER_HANDLER_H
