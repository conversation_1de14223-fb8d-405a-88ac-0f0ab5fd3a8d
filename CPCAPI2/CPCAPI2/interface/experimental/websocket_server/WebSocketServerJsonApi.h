#pragma once

#if !defined(CPCAPI2_WEBSOCKET_SERVER_JSON_API_H)
#define CPCAPI2_WEBSOCKET_SERVER_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace WebSocketServer
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API WebSocketServerJsonApi
{
public:
   /**
   * Get a reference to the %WebSocketServerJsonApi interface.
   */
   static WebSocketServerJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_WEBSOCKET_SERVER_JSON_API_H
