#pragma once
#if !defined(WEBSOCKET_SERVER_TYPES_H)
#define WEBSOCKET_SERVER_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "phone/SslCipherOptions.h"

#define CPCAPI2_WEBSOCKET_SERVER_DEFAULT_PORT 9003;

namespace CPCAPI2
{
namespace WebSocketServer
{
   typedef unsigned int WebSocketServerHandle;
   typedef unsigned int ConnectionHandle;

   enum CloseReason
   {
      CloseReason_Normal = 0,
      CloseReason_Unsupported,
   };

   struct WebSocketServerSettings
   {
      cpc::string certificatePem;
      cpc::string privateKeyPem;
      //cpc::string password;
      CipherSuite ciphers;
      cpc::string url;
      
      WebSocketServerSettings()
      {
         ciphers = CipherSuiteAdvanced;
      }
   };
}

}

#endif //WEBSOCKET_SERVER_TYPES_H
