#pragma once

#if !defined(WEBSOCKET_SERVER_MANAGER_H)
#define WEBSOCKET_SERVER_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "WebSocketServerTypes.h"

namespace CPCAPI2
{ 
class Phone;

namespace WebSocketServer
{
class WebSocketServerHandler;

/**
* Manager interface to send and receive websocket data;
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API WebSocketServerManager
{
public:
   /**
   * Get a reference to the %WebSocketServerManager interface.
   */   
   static WebSocketServerManager* getInterface(Phone* cpcPhone);

   virtual int process(unsigned int timeout) = 0;
   virtual void interruptProcess() = 0;
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   virtual int setHandler(WebSocketServerHandler* handler) = 0;
   virtual int start(WebSocketServerSettings* settings = NULL) = 0;
   virtual int stop() = 0;
   virtual int send(ConnectionHandle connection, const cpc::string& data) = 0;
   virtual int close(ConnectionHandle connection, CloseReason closeReason) = 0;
};

}
}

#endif // WEBSOCKET_SERVER_MANAGER_H
