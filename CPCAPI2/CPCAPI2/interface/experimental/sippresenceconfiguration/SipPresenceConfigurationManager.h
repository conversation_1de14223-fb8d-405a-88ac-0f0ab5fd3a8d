#if !defined(CPCAPI2_SIPPRESENCE_SIPPRESENCECONFIGURATIONMANAGER_H)
#define CPCAPI2_SIPPRESENCE_SIPPRESENCECONFIGURATIONMANAGER_H

#include "cpcapi2defs.h"

namespace CPCAPI2 {
   class Phone;

namespace SipPresence {

   enum SipPresenceModelType 
   {
      DEFAULT_PRESENCE_MODEL, // Use the default Sip Presence Model. Most applications should use this
      GENBAND_PRESENCE_MODEL  // Use the GENBAND Sip Presence Model. Only use this for GENBAND specific applications
   };

   /*
    * Manages the Sip Presence Configuration for this entity
    *
    * Allows the application to choose a specific Presence model to be used
    *
    */

   class CPCAPI2_SHAREDLIBRARY_API SipPresenceConfigurationManager {
   public:
      /**
      * Get a reference to the $SipPresenceConfigurationManager interface
      */
      static SipPresenceConfigurationManager* getInterface(Phone* phone);

      /**
       * Set the Sip Presence Model to use for this application
       *
       * @param model the Sip presence model to use
       */
      virtual void setSipPresenceModel(const SipPresenceModelType model) = 0;

      virtual SipPresenceModelType getSipPresenceModel() const = 0;
      
   protected:
      /*
       * The SDK will manage memory life of %SipPresenceConfigurationManager.
       */
      virtual ~SipPresenceConfigurationManager() {}
   
   };

} // namespace SipPresence
} // namespace CPCAPI2

#endif