#ifndef LogcatHandler_h
#define LogcatHandler_h

namespace CPCAPI2
{
namespace Logcat
{

struct LogcatAudioModeChangeEvent
{
   int newMode;
};

struct LogcatTestMonitorSuccessEvent
{
};

struct LogcatConnectedEvent
{
};

class LogcatHandler
{
public:
   virtual int onAudioModeChange(int dummyHandle, const LogcatAudioModeChangeEvent& event) = 0;
   
   /**
    * Fired if unique logcat event generated by testLogcatMonitor was seen
    */
   virtual int onLogcatTestMonitorSuccess(int dummyHandle, const LogcatTestMonitorSuccessEvent& event) = 0;
   
   /**
    * Fired when connected to logcat server
    */
   virtual int onLogcatConnected(int dummyHandle, const LogcatConnectedEvent& event) = 0;
};

class LogcatSyncHandler : public LogcatHandler
{
};

}
}

#endif /* LogcatHandler_h */
