#ifndef LogcatMonitor_h
#define LogcatMonitor_h

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace Logcat
{

class LogcatHandler;

struct LogcatMonitorSettings
{
   cpc::string ip;
   unsigned int port;
};

class CPCAPI2_SHAREDLIBRARY_API LogcatMonitor
{
public:
   static LogcatMonitor* getInterface();

   virtual int configureSettings(const LogcatMonitorSettings& settings) = 0;
   virtual int startMonitoring() = 0;

   /**
    * Prints a unique logcat message and waits for it to be received; if received,
    * fires a onLogcatMonitorSuccess event.
    */
   virtual int logcatTestMonitor() = 0;


   virtual int setHandler(LogcatHandler* handler) = 0;

protected:
   LogcatMonitor() {}

};

}
}


#endif
