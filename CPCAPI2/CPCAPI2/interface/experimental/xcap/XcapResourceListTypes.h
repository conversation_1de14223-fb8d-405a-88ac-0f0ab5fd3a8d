#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_TYPES_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "xcap/XcapSettings.h"

namespace CPCAPI2
{
namespace XCAP
{
typedef unsigned int XcapResourceListUpdateRequestHandle;
typedef unsigned int XcapResourceListHandle;
typedef unsigned int XcapResourceListIterator;

/**
* Type of items that can be found in resource list.
*/
enum ItemType
{
   Entry,
   Entry_Ref,
   List,
   External
};

struct Field
{
   cpc::string name;
   cpc::string value;
};

/**
* Structure that represents item from resource list. 
* @displayName - Human readable name of an item.
* @itemType - Type of items that can be found in resource list.
* @id - ID of an item. Depending on ItemType it can be URI for entry, name for list, ref for entry-ref or anchor for external.
* @list - This member is used only if ResourceListItem is a resource list (ItemType is list), otherwise it's empty.
*/
struct ResourceListItem
{
   cpc::string displayName;
   ItemType itemType;
   cpc::string id;
   cpc::vector<CPCAPI2::XCAP::ResourceListItem> list;
   cpc::vector<CPCAPI2::XCAP::Field> fields;

   ResourceListItem()
   {
      displayName = "";
      itemType = Entry;
      id = "";
   }
};

/**
* Representation of resource list as described in RFC 4825. This structure is adjusted to be used by application clients. 
* @name - value of a name identifier that uniqly identifies resource list.
* @items - vector containing @ResourceListItem members of resource list.
*/
struct ResourceList
{
   cpc::string name;
   CPCAPI2::XCAP::ResourceListItem list;
};

/**
* Type of change to be made to resource list.
*/
enum ChangeType
{
   ChangeType_Add,
   ChangeType_Remove,
   ChangeType_Update,
   ChangeType_Query
};

/**
* Item that represents change to be made to resource list with @XcapResourceListManager.
* @changedItem - data changes to be made represented by ResourceListItem.
* @changeType - type of a change.
*/
struct ChangeItem
{
   ResourceListItem changedItem;
   ChangeType changeType;
};

/**
* Event structure for applying change to resource list.
* @account - handle of account that applies changes
* @resourceList - handle of resource list that changes are applied to.
* @changes - list of changes to be applied to resource list.
*/
struct XcapResourceListUpdateEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;
   XcapResourceListHandle resourceList;
   cpc::vector<ChangeItem> changes;
};

/**
* Event structure that describes update failed event. 
*/
struct XcapResourceListUpdateFailedEvent
{
   unsigned int errorCode;
   cpc::string errorText;
};

/**
* Event structure that describes error event.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

}
}
#endif // CPCAPI2_XCAP_RESOURCE_LIST_TYPES_H
