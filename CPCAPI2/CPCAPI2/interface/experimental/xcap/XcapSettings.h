#pragma once

#if !defined(CPCAPI2_XCAP_SETTINGS_H)
#define CPCAPI2_XCAP_SETTINGS_H

#define XCAP_ELEMENT_MIME "application/xcap-el+xml"
#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace XCAP
{
/**
* Settings used for connection to XCAP server. Account specific and module specific. Used in xcap path creation.
* XCAP path http://+domain+xcap root+auid+user definition+document selector+/~~/+node selector
*/
struct XcapSettings
{
   //
   // Users xcap domain, for example xcap.counterpath.com
   cpc::string domain;
   // xcap root loacation inside domain. Part of the path that goes between domain and AUID
   cpc::string xcapRoot;
   // xcap server username
   cpc::string username;
   // xcap server password
   cpc::string password;
   // port that is xcap request sent to.
   cpc::string port;
};

} // XCAP
} // CPCAPI2
#endif // CPCAPI2_XCAP_SETTINGS_H
