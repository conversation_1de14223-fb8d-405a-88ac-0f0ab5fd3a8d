#pragma once

#if !defined(CPCAPI2_XCAP_SUBSCRIPTION_HANDLER_H)
#define CPCAPI2_XCAP_SUBSCRIPTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace XCAP
{
using CPCAPI2::SipEvent::SipEventSubscriptionHandle;

/**
 * Event passed in XcapSubscriptionHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
 * an outgoing SUBSCRIBE has forked, OR
 * an incoming SUBSCRIBE has been received.
 */
struct NewXcapSubscriptionEvent
{
   CPCAPI2::SipEvent::SubscriptionType   subscriptionType;
   cpc::string                           remoteAddress;
   cpc::string                           remoteDisplayName;
   cpc::string                           eventPackage;
   cpc::vector<CPCAPI2::MimeType>        supportedMimeTypes;
   CPCAPI2::SipAccount::Sip<PERSON><PERSON>unt<PERSON>and<PERSON> account;
};

/**
 * Event passed in XcapSubscriptionHandler::onSubscriptionEnded(). 
 * Indicates the client or server subscription session has terminated.
 */
struct XcapSubscriptionEndedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionEndReason      endReason;
   CPCAPI2::SipEvent::SubscriptionType              subscriptionType;
   int                                              statusCode; // Error code
   int                                              retryAfter; // value of Retry-Header if present
   bool                                             initialSubscribe; // Initial Subscribe failed
   bool                                             isNotifyTerminated; // Terminated because of NOTIFY
   cpc::string                                      remoteAddress; // Buddy address
   cpc::string                                      reason; // subscription end reason
};

/**
 * Event passed in SipEventSubscriptionHandler::onIncomingEventState().
 * Indicates an incoming NOTIFY was received.
 */
struct IncomingXcapEventStateEvent
{
   CPCAPI2::SipEvent::SipEventState     eventState;
};

/**
 * Event passed in XcapSubscriptionHandler::onSubscriptionStateChanged().
 * The state of the incoming or outgoing subscription has changed.
 * This might happen for any of the following reasons:
 * <ul>
 * <li>The remote party accepted the subscription request
 * <li>The remote party rejected or terminated the subscription
 * <li>The event server is transitioning to an out-of-service state
 * </ul>
 */
struct XcapSubscriptionStateChangedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionState    subscriptionState;
};



class XcapSubscriptionHandler
{
public:
	virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const NewXcapSubscriptionEvent& args) = 0;
	virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const XcapSubscriptionEndedEvent& args) = 0;
	virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const IncomingXcapEventStateEvent& args) = 0;
	virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const XcapSubscriptionStateChangedEvent& args) = 0;

	virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args) = 0;
};

} // XCAP
} // CPCAPI2
#endif // CPCAPI2_XCAP_SUBSCRIPTION_HANDLER_H
