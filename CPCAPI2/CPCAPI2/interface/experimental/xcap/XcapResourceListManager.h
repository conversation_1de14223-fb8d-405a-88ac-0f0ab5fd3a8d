#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XcapResourceListTypes.h"

namespace CPCAPI2
{
class Phone;

namespace XCAP
{

class XcapResourceListHandler;
class XcapInternalInterface;
class XcapSubscriptionHandler;
class XcapPublicationHandler;

/**
 */
class CPCAPI2_SHAREDLIBRARY_API XcapResourceListManager
{
public:
   /**
   * Get a reference to the XcapResourceListManager interface. 
   * cpcPhone - phone interface
   * docSelector - name of the xcap document(module, manager) to be created on xcap server for this resource list manager.
   * return value - resource list manager instance.
   */
   static XcapResourceListManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for events on the specified account. Set the handler immediately after creating the account.
   */
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapResourceListHandler* handler) = 0;

   /**
   *
   */
   virtual int setXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings) = 0;

   /**
   * Add new resource list to the document represented by the @documentSelector and @account. If resource list already exists it will be overwritten.
   * @account - Account handle.
   * @resourceList - Resource list structure that defines new list.
   * @documentSelector - XCAP document selector path. Location of a document on a server inside xcap root.
   * return value - handle to the newly created list.
   */
   virtual XcapResourceListHandle addResourceList(CPCAPI2::SipAccount::SipAccountHandle account, const ResourceList& resourceList, const cpc::string& documentSelector) = 0;

   /**
   * Remove resource list to the document represented by the manager.
   * listHandle - handle to the list to be deleted.
   */
   virtual int removeResourceList(XcapResourceListHandle resourceListHandle) = 0;

   /**
   * force the SDK to give you a onResourceListUpdate(..) callback right now
   */
   virtual int queryResourceList(XcapResourceListUpdateRequestHandle requestHandle, XcapResourceListHandle resourceListHandle) = 0;

   /**
   * (synchronous) interface to resource list items
   */
   virtual int getResourceList(XcapResourceListHandle resourceListHandle, ResourceList& resourceList) = 0;
   
   /**
   * call this, make your changes to the resource list, then call start(..); you will then get a (single) onResourceListUpdate(..) callback
   */
   virtual XcapResourceListUpdateRequestHandle createUpdateRequest(XcapResourceListHandle resourceListHandle) = 0;
   
   /**
   * Add ResourceListItem to resource list
   */
   virtual int addItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem) = 0;
   
   /**
   * Remove ResourceListItem from resource list
   */
   virtual int removeItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem) = 0;
   
   /**
   * Update ResourceListItem from resource list
   */
   virtual int updateItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem) = 0;
   
   /**
   * Execute resource list update request with all commands inside it.
   */
   virtual int start(XcapResourceListUpdateRequestHandle rosterRequest) = 0;
   

protected:
   /*
    * The SDK will manage memory life of %XcapResourceListManager.
    */
   virtual ~XcapResourceListManager() {}

};

} // XCAP namespace
} // CPCAPI2 namespace
#endif // CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_H
