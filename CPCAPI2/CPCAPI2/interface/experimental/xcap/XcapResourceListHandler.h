#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_HANDLER_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_HANDLER_H

#include "cpcapi2defs.h"
#include "XcapResourceListTypes.h"

namespace CPCAPI2
{
namespace XCAP
{


/**
 * The handler for events on XcapResourceListManager; passed in XcapResourceListManager::setHandler().
 */
class XcapResourceListHandler
{
public:
   /**
   * Notifies the application when the %XcapResourceListManager has been updated on the server.
   * May result from a local operation, or a change initiated by a remote client logged in with the same credentials.
   */
   virtual int onResourceListUpdate(CPCAPI2::XCAP::XcapResourceListHandle resourceList, const XcapResourceListUpdateEvent& args) = 0;

   /**
   * Notifies the application when a resource list update request initiated by this client has failed.
   */
   virtual int onResourceListUpdateFailed(CPCAPI2::XCAP::XcapResourceListHandle resourceList, const XcapResourceListUpdateFailedEvent& args) = 0;

   /**
   * Notifies the application when an account error has occurred
   */
   virtual int onError(CPCAPI2::XCAP::XcapResourceListHandle resourceList, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_XCAP_RESOURCE_LIST_HANDLER_H
