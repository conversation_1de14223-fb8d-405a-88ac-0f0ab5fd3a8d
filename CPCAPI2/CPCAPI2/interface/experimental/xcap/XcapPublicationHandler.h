#pragma once

#if !defined(CPCAPI2_XCAP_PUBLICATION_HANDLER_H)
#define CPCAPI2_XCAP_PUBLICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "event/SipEventPublicationHandler.h"

namespace CPCAPI2
{
namespace XCAP
{

/**
* Event passed in XcapPublicationHandler::onPublicationSuccess().
*/
struct XcapPublicationSuccessEvent
{
};

/**
* Event passed in XcapPublicationHandler::onPublicationFailure().
*/
struct XcapPublicationFailureEvent
{
   CPCAPI2::SipEvent::SipPublicationFailureReason reason;
};

/**
* Event passed in XcapPublicationHandler::onPublicationRemove().
*/
struct XcapPublicationRemoveEvent
{
};

/**
* Event passed in XcapPublicationHandler::onError().
*/
struct XcapPublicationErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to publishing the local user's xcap events; 
*/
class XcapPublicationHandler
{
public:
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const XcapPublicationSuccessEvent& args) = 0;
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const XcapPublicationFailureEvent& args) = 0;
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const XcapPublicationRemoveEvent & args) = 0;
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const XcapPublicationErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_XCAP_PUBLICATION_HANDLER_H
