#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERNAL_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERNAL_H

#include <xmpp/XmppMultiUserChatManager.h>
#include "cpcstl/string.h"

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

   class CPCAPI2_SHAREDLIBRARY_API XmppMultiUserChatManagerInternal : public CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager
   {

   public:

      static XmppMultiUserChatManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void createMultiUserChat(CPCAPI2::XmppAccount::XmppAccountHandle account, const cpc::string& room) = 0;

   };

}

}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERNAL_H
