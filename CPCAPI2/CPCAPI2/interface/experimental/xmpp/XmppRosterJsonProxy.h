#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_JSON_PROXY_H)
#define CPCAPI2_XMPP_ROSTER_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp/XmppRoster.h"

namespace CPCAPI2
{

class Phone;

namespace XmppRoster
{

class XmppRosterJsonProxyStateHandler;

class CPCAPI2_SHAREDLIBRARY_API XmppRosterManagerJsonProxy : public CPCAPI2::XmppRoster::XmppRosterManager
{

public:

   /**
    * Get a reference to the XmppRosterManager interface.
    */
   static XmppRosterManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(XmppRosterJsonProxyStateHandler* handler) = 0;
   virtual int requestRosterState(XmppRosterHandle roster) = 0;
   virtual int requestRosterStateForAccount(XmppAccount::XmppAccountHandle account) = 0;
   virtual int requestAllRosterState() = 0;

protected:

   /*
    * The SDK will manage memory life of %XmppRosterManager.
    */
   virtual ~XmppRosterManagerJsonProxy() {}

};

}
}

#endif // CPCAPI2_XMPP_ROSTER_JSON_PROXY_H
