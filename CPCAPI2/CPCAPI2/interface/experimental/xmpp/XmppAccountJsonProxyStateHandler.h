#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "jsonapi/JsonApiClient.h"
#include "xmpp/XmppAccountState.h"

namespace CPCAPI2
{
namespace XmppAccount
{
struct JsonProxyAccountStateEvent
{
   cpc::vector<XmppAccountState> accountState;
};

class XmppAccountJsonProxyStateHandler
{
public:
   virtual int onAccountState(CPCAPI2::JsonApi::JsonApiConnectionHandle conn, const JsonProxyAccountStateEvent& args) = 0;
};

}
}
#endif // CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H
