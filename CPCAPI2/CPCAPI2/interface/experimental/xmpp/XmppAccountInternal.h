#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_MANAGER_INTERNAL_H)
#define CPCAPI2_XMPP_ACCOUNT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include <xmpp/XmppAccount.h>

namespace CPCAPI2
{
namespace XmppAccount
{

// currently only used in HTTP file upload
struct XmppAccountSettingsInternal
{
   enum CertVerification
   {
      CertVerification_Unspecified, // no action is specified and XmppAccountSettings.ignoreCertVerification shall determine the outcome
      CertVerification_Verify,      // verify certificate
      CertVerification_Ignore       // ignore certification
   };

   CertVerification httpFileUploadCertVerification;
   int acceptableFailures;
   cpc::string certFolder;
   bool useCertFolderOnly;

   XmppAccountSettingsInternal()
      : httpFileUploadCertVerification(CertVerification_Unspecified)
      , acceptableFailures(0)
      , useCertFolderOnly(false)
   {}
};

class CPCAPI2_SHAREDLIBRARY_API XmppAccountManagerInternal : public XmppAccountManager
{
public:
   static XmppAccountManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
   virtual void create(XmppAccountHandle account, const XmppAccountSettings& settings) = 0;
   virtual void simulateNetworkLoss(XmppAccountHandle account) = 0;
   virtual void simulateNetworkRestriction(XmppAccountHandle account, bool restricted) = 0;
   virtual void setAccountSettingsInternal(XmppAccountHandle account, const XmppAccountSettingsInternal& settings) = 0;
};

}
}

#endif // CPCAPI2_XMPP_ACCOUNT_MANAGER_INTERNAL_H
