#pragma once

#if !defined(__CPCAPI2_XMPP_PUSH_MANAGER_H__)
#define __CPCAPI2_XMPP_PUSH_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppPushTypes.h"
#include "XmppPushHandler.h"

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace XmppAccount
{
typedef unsigned int XmppAccountHandle;
}

namespace XmppPush
{

/**
 * Interface for XMPP push related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppPushManager
{
public:

   static XmppPushManager* getInterface(Phone* cpcPhone);

   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppPushHandler* handler) = 0;

   virtual int configurePushSettings(XmppAccount::XmppAccountHandle account, const XmppPushSettings& settings) = 0;

   virtual int applyPushSettings(XmppAccount::XmppAccountHandle account) = 0;

   virtual int pushRegister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken) = 0;

   virtual int pushUnregister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken) = 0;

   virtual int setIgnoreUnknownSender(XmppAccount::XmppAccountHandle account, bool ignoreUnknown) = 0;

   virtual int setSendNotificationsWhileAway(XmppAccount::XmppAccountHandle account, bool sendWhileAway) = 0;

   virtual int setContactMuted(XmppAccount::XmppAccountHandle account, const cpc::string& contactJid, bool muted = true) = 0;

   virtual int setGroupChatFilter(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick = "") = 0;

   virtual int enableNotifications(XmppAccount::XmppAccountHandle account) = 0;

   virtual int disableNotifications(XmppAccount::XmppAccountHandle account) = 0;
   
   virtual int registerMucOfflineMessageDelivery(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, const cpc::string& nick) = 0;

protected:
   virtual ~XmppPushManager() {}
};

}
}

#endif // __CPCAPI2_XMPP_PUSH_MANAGER_H__
