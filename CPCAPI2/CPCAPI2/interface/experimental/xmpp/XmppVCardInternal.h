#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_MANAGER_INTERNAL_H)
#define CPCAPI2_XMPP_VCARD_MANAGER_INTERNAL_H

#include <xmpp/XmppVCardManager.h>

namespace CPCAPI2
{

namespace XmppVCard
{

class CPCAPI2_SHAREDLIBRARY_API XmppVCardManagerInternal : public CPCAPI2::XmppVCard::XmppVCardManager
{

public:

   static XmppVCardManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
   virtual void createVCard(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;

};

}

}

#endif // CPCAPI2_XMPP_VCARD_MANAGER_INTERNAL_H
