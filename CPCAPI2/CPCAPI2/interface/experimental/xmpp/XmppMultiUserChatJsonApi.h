#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_API_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppMultiUserChat
{
/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppMultiUserChatJsonApi
{
public:
   /**
    * Get a reference to the %XmppMultiUserChatJsonApi interface.
    */
   static XmppMultiUserChatJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_API_H
