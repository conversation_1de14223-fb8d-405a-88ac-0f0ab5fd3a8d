#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_MANAGER_INTERNAL_H)
#define CPCAPI2_XMPP_ROSTER_MANAGER_INTERNAL_H

#include <xmpp/XmppRoster.h>

namespace CPCAPI2
{

namespace XmppRoster
{

   class CPCAPI2_SHAREDLIBRARY_API XmppRosterManagerInternal : public CPCAPI2::XmppRoster::XmppRosterManager
   {

   public:

      static XmppRosterManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void create(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;

   };

}

}

#endif // CPCAPI2_XMPP_ROSTER_MANAGER_INTERNAL_H
