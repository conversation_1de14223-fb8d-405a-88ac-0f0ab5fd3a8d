#pragma once

#if !defined(__CPCAPI2_XMPP_PUSH_HANDLER_H__)
#define __CPCAPI2_XMPP_PUSH_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppPushTypes.h"
#include <xmpp/XmppAccount.h>

namespace CPCAPI2
{
namespace XmppPush
{
enum Error
{
   Error_AdhocDiscovery    = 1,
   Error_AdhocExecute      = 2,
   Error_PushRegister      = 3,
   Error_PushUnregister    = 4,
   Error_PushSetup         = 5
};

struct PushConfigEvent
{
   cpc::string pushNotificationsComponent;
};

struct PushEvent
{
   cpc::string deviceToken;
};

struct PushErrorEvent
{
   cpc::string errorText;
   Error errorCode;
};

struct PushMucRegistrationEvent
{
   cpc::string roomJid;
   bool success;
   cpc::string errorText;
};

class XmppPushHandler
{
public:
   virtual int onPushConfigured(XmppAccount::XmppAccountHandle account, const PushConfigEvent& args) = 0;

   virtual int onPushConfigError(XmppAccount::XmppAccountHandle account, const PushErrorEvent& args) = 0;

   virtual int onPushRegistered(XmppAccount::XmppAccountHandle account, const PushEvent& args) = 0;

   virtual int onPushUnregistered(XmppAccount::XmppAccountHandle account, const PushEvent& args) = 0;

   virtual int onMucRegistrationResult(XmppAccount::XmppAccountHandle account, const PushMucRegistrationEvent& args) = 0;
};

}
}

#endif // __CPCAPI2_XMPP_PUSH_HANDLER_H__
