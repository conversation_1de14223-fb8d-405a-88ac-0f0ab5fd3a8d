#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_JSON_API_H)
#define CPCAPI2_XMPP_VCARD_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppVCard
{

/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppVCardJsonApi
{

public:

   /**
    * Get a reference to the %XmppVCardJsonApi interface.
    */
   static XmppVCardJsonApi* getInterface(Phone* cpcPhone);

};

}

}

#endif // CPCAPI2_XMPP_VCARD_JSON_API_H
