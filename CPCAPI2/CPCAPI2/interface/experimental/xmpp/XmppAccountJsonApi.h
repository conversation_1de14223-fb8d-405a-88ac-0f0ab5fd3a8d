#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_JSON_API_H)
#define CPCAPI2_XMPP_ACCOUNT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppAccount
{
/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppAccountJsonApi
{
public:
   /**
    * Get a reference to the %XmppAccountJsonApi interface.
    */
   static XmppAccountJsonApi* getInterface(Phone* cpcPhone);
};

}
}

#endif // CPCAPI2_XMPP_ACCOUNT_JSON_API_H
