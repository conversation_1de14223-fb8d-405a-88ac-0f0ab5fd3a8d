#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_JSON_PROXY_H)
#define CPCAPI2_XMPP_VCARD_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp/XmppVCardManager.h"

namespace CPCAPI2
{

class Phone;

namespace XmppVCard
{

class XmppVCardJsonProxyStateHandler;

class CPCAPI2_SHAREDLIBRARY_API XmppVCardManagerJsonProxy : public CPCAPI2::XmppVCard::XmppVCardManager
{

public:

   /**
    * Get a reference to the XmppVCardManager interface.
    */
   static XmppVCardManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(XmppVCardJsonProxyStateHandler* handler) = 0;

   /**
    * Query the vcard state for the particular vcard handle, response provided in the onVCardState callback.
    */
   virtual int requestVCardState(XmppVCardHandle vcard) = 0;

   /**
    * Query the vcard state for the particular xmpp account, response provided in the onVCardState callback.
    */
   virtual int requestVCardStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Query the complete vcard state for all accounts, response provided in the onVCardState callback.
    */
   virtual int requestAllVCardState() = 0;

protected:

   /**
    * The SDK will manage memory life of %XmppVCardManager.
    */
   virtual ~XmppVCardManagerJsonProxy() {}

};

}
}

#endif // CPCAPI2_XMPP_VCARD_JSON_PROXY_H
