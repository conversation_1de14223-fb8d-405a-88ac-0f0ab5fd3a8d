#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_JSON_API_H)
#define CPCAPI2_XMPP_ROSTER_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppRoster
{
/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppRosterJsonApi
{
public:
   /**
    * Get a reference to the %XmppRosterJsonApi interface.
    */
   static XmppRosterJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_XMPP_ROSTER_JSON_API_H
