#pragma once

#if !defined(CPCAPI2_XMPP_CHAT_JSON_PROXY_H)
#define CPCAPI2_XMPP_CHAT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp/XmppChatManager.h"

namespace CPCAPI2
{
class Phone;

namespace XmppChat
{
class CPCAPI2_SHAREDLIBRARY_API XmppChatManagerJsonProxy : public CPCAPI2::XmppChat::XmppChatManager
{
public:
   /**
    * Get a reference to the XmppChatManager interface.
    */
   static XmppChatManagerJsonProxy* getInterface(Phone* cpcPhone);


protected:
   /*
    * The SDK will manage memory life of %XmppChatManager.
    */
   virtual ~XmppChatManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_XMPP_CHAT_JSON_PROXY_H
