#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_H)
#define CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp/XmppAccount.h"

namespace CPCAPI2
{
class Phone;

namespace XmppAccount
{
class XmppAccountJsonProxyStateHandler;
class XmppAccountHandler;
typedef unsigned int XmppAccountHandle;

/**
 * Main entry point for XMPP-related functionality.
 *
 * Usage:
 * <ol>
 *    <li> Get a reference to the XmppAccount module using XmppAccountManager::getInterface(phone)
 *    <li> Create the XmppAccount using XmppAccountManager::create(accountSettings)
 *    <li> Set an account handler using XmppAccountManager::setHandler(..)
 *    <li> Enable the account using XmppAccountManager::enable(..)
 * </ol>
 */
class CPCAPI2_SHAREDLIBRARY_API XmppAccountManagerJsonProxy : public CPCAPI2::XmppAccount::XmppAccountManager
{
public:
   /**
    * Get a reference to the XmppAccountManager interface.
    */
   static XmppAccountManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(XmppAccountJsonProxyStateHandler* handler) = 0;
   virtual int requestStateAllAccounts() = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppAccountManager.
    */
   virtual ~XmppAccountManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_H
