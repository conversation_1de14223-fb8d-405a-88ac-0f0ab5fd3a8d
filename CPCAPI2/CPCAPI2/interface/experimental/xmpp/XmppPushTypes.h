#pragma once

#if !defined(__CPCAPI2_XMPP_PUSH_TYPES_H__)
#define __CPCAPI2_XMPP_PUSH_TYPES_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
namespace XmppPush
{

enum GroupChatFilterRule
{
   Always      = 1,
   Never       = 2,
   Mentioned   = 3
};

struct GroupChatFilter
{
   GroupChatFilterRule  rule;
   cpc::string          jid;
   cpc::string          nick;
};

struct XmppPushSettings
{
   cpc::string                   provider;
   bool                          ignoreUnknownSender;
   bool                          sendNotificationsWhileAway;
   cpc::vector<cpc::string>      mutedContacts;
   cpc::vector<GroupChatFilter>  groupChatFilter;
};

}//namespace XmppPush
} //namespace CPCAPI2

#endif // __CPCAPI2_XMPP_PUSH_TYPES_H__