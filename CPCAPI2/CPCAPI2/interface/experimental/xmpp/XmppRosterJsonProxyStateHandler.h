#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_XMPP_ROSTER_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "../jsonapi/JsonApiClient.h"
#include "xmpp/XmppRosterTypes.h"
#include "xmpp/XmppRosterState.h"

namespace CPCAPI2
{

namespace XmppRoster
{
   
struct JsonProxyRosterStateEvent
{
   cpc::vector<XmppRosterState> rosterState;
};

struct JsonProxyRosterItemsEvent
{
   XmppRosterHandle roster;
   cpc::vector<RosterItem> rosterItems;
   
   JsonProxyRosterItemsEvent() : roster(0) {}
};

class XmppRosterJsonProxyStateHandler
{

public:

   /**
    * Notifies the proxy handler in response to the the request state queries.
   */
   virtual int onRosterState(const JsonProxyRosterStateEvent& args) = 0;
   
   /**
    * Notifies the proxy handler in response to the the getRosterState query, mimics the blocking synchronous query provided for the local SDK.
   */
   virtual int onRosterItems(const JsonProxyRosterItemsEvent& args) = 0;

};

}

}

#endif // CPCAPI2_XMPP_ROSTER_JSON_PROXY_STATE_HANDLER_H
