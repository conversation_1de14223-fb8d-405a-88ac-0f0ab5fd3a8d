#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp/XmppMultiUserChatManager.h"

namespace CPCAPI2
{

class Phone;

namespace XmppMultiUserChat
{

class XmppMultiUserChatJsonProxyStateHandler;

class CPCAPI2_SHAREDLIBRARY_API XmppMultiUserChatManagerJsonProxy : public CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager
{

public:

   /**
    * Get a reference to the XmppMultiUserChatManager interface.
    */
   static XmppMultiUserChatManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(XmppMultiUserChatJsonProxyStateHandler* handler) = 0;

   /**
    * Query the multi user chat state for the particular multi user chat handle, response provided in the onMultiUserChatState callback.
    */
   virtual int requestMultiUserChatState(XmppMultiUserChatHandle muc) = 0;

   /**
    * Query the multi user chat states for the particular xmpp account, response provided in the onMultiUserChatState callback.
    */
   virtual int requestMultiUserChatStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Query the complete multi user chat states for all accounts, response provided in the onMultiUserChatState callback.
    */
   virtual int requestAllMultiUserChatState() = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppMultiUserChatManager.
    */
   virtual ~XmppMultiUserChatManagerJsonProxy() {}
};

}
}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_H
