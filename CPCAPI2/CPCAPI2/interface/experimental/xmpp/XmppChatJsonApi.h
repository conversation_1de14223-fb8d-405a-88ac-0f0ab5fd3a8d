#pragma once

#if !defined(CPCAPI2_XMPP_CHAT_JSON_API_H)
#define CPCAPI2_XMPP_CHAT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppChat
{
/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppChatJsonApi
{
public:
   /**
    * Get a reference to the %XmppChatJsonApi interface.
    */
   static XmppChatJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_XMPP_CHAT_JSON_API_H
