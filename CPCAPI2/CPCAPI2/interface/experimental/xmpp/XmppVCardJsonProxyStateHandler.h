#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_XMPP_VCARD_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "../jsonapi/JsonApiClient.h"
#include "xmpp/XmppVCardTypes.h"
#include "xmpp/XmppVCardState.h"

namespace CPCAPI2
{

namespace XmppVCard
{
   
struct JsonProxyVCardStateEvent
{
   cpc::vector<XmppVCardStateInfo> states;
};

class XmppVCardJsonProxyStateHandler
{

public:

   /**
    * Notifies the proxy handler in response to the the request state query.
   */
   virtual int onVCardState(const JsonProxyVCardStateEvent& args) = 0;

};

}

}

#endif // CPCAPI2_XMPP_VCARD_JSON_PROXY_STATE_HANDLER_H
