#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "../jsonapi/JsonApiClient.h"
#include "xmpp/XmppMultiUserChatTypes.h"
#include "xmpp/XmppMultiUserChatState.h"

namespace CPCAPI2
{

namespace XmppMultiUserChat
{
   
struct JsonProxyMultiUserChatStateEvent
{
   cpc::vector<XmppMultiUserChatStateInfo> states;
};

class XmppMultiUserChatJsonProxyStateHandler
{

public:

   /**
    * Notifies the proxy handler in response to the the request state query.
   */
   virtual int onMultiUserChatState(const JsonProxyMultiUserChatStateEvent& args) = 0;

};

}

}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_STATE_HANDLER_H
