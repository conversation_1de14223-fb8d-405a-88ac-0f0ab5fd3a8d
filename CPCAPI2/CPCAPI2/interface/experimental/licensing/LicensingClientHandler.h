#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_HANDLER_H__)
#define __CPCAPI2_LICENSING_CLIENT_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "LicensingClientTypes.h"

namespace CPCAPI2
{
namespace Licensing
{

/**
* Information on a valid license.
*/
struct LicenseInfo
{
   cpc::string key;
   cpc::string type;
   cpc::vector<cpc::string> features;
   uint64_t expiry;
   uint64_t gracePeriod;
};

/**
* Information on an invalid license.
*/
struct InvalidLicenseInfo : public LicenseInfo
{
   cpc::string id;
   int code;
   cpc::string message;
};

/**
* Event passed in LicensingClientHandler::onValidateLicensesSuccess().
*
* This event is fired when time limit license check completed and/or license key validation completed. 
*/
struct ValidateLicensesSuccessEvent
{
   // Information related to time limit license
   bool timeLimitLicense;     // True if a timit license was specified, false otherwise.
   double timeLimitRemaining; // Time left (in seconds) before time limit license expires, 0 if expired (or time limit license not specified).

   // Information related to license key(s)
   cpc::string licenseUrl;
   cpc::string hardwareId;
   cpc::vector<LicenseInfo> licenses;
   cpc::vector<InvalidLicenseInfo> invalidLicenses;
};

/**
* Event passed in LicensingClientHandler::onValidateLicensesFailure().
* 
* This event is fired when the license key validation process failed (e.g. error reported by licensing server). 
*/
struct ValidateLicensesFailureEvent
{
   // Information related to license key(s)
   uint64_t errorCode;
   LicenseStatus status;
};

/**
* Event passed in LicensingClientHandler::onError().
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * The handler for events on LicensingClientManager; passed in LicensingClientManager::setHandler().
 */
class LicensingClientHandler
{
public:
   /**
   * Notifies the application when license validation has completed.
   */
   virtual int onValidateLicensesSuccess(LicensingClientHandle client, const ValidateLicensesSuccessEvent& args) = 0;

   /**
   * Notifies the application when license validation has failed.
   */
   virtual int onValidateLicensesFailure(LicensingClientHandle client, const ValidateLicensesFailureEvent& args) = 0;

   /**
   * Notifies the application when an account error has occurred.
   */
   virtual int onError(LicensingClientHandle client, const ErrorEvent& args) = 0;
};

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_HANDLER_H__
