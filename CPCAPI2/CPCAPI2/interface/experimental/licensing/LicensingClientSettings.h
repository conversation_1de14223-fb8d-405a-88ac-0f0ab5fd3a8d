#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_SETTINGS_H__)
#define __CPCAPI2_LICENSING_CLIENT_SETTINGS_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace Licensing
{

/**
* Information on accounts.
*/
struct AccountInfo
{
   cpc::string type;    // Type of account e.g. SIP, XMPP.
   bool enabled;        // True, if the account is enabled, false otherwise.
   bool defaultAccount; // True if it is a default account, false otherwise.
   cpc::string user;    // User
};

/**
* User can specify either license key(s) or a time limit license (aka timebomb) or both.
* A call to LicensingClientManager::validateLicenses() with validate all licenses specified in these settings.
*/
struct LicensingClientSettings
{
   // Information on the license key(s)
   cpc::string brand;                    // The brand to use (optional). Defaults to CPCAPI2_BRAND_FULL_NAME if not specified.
   cpc::string provisioningId;           // The provisioning username/id (optional). Defaults to empty.
   int maximumSubscribers;               // The maximum number of subscribers per license key (optional). Defaults to 0.
   cpc::string keySourceUrl;             // The source URL of the key (optional). Defaults to empty.
   cpc::string licenseUrl;               // URL of the license server (optional). Defaults to CPCAPI2_BRAND_LICENSE_URL if not specified.
   cpc::string licenseUrlSecondary;      // URL of the secondary license server (optional). Defaults to CPCAPI2_BRAND_LICENSE_URL_SECONDARY.
   cpc::string licenseDocumentLocation;  // The location for the license file (mandatory if license key is provided).
   cpc::string proxyServerAddress;       // Address of web proxy to use for request. Optional, overrides default system detected HTTP proxy.
   cpc::string domain;                   // The domain (optional). Defaults to empty.
   cpc::string osVersion;                // The operating system version (optional). Defaults to empty.
   cpc::string clientVersion;            // The client version to include in license request. Defaults to CPCAPI2_BRAND_VERSION_NUMBER + CPCAPI2_BRAND_BUILD_STAMP.
   cpc::vector<AccountInfo> accounts;    // The accounts provisioned in the client (optional).
   cpc::vector<cpc::string> licenseKeys; // The license keys.

   // Information on the time limit license (aka timebomb)
   int expiryYear;  // Year
   int expiryMonth; // Month
   int expiryDay;   // Day

#ifdef CPCAPI2_AUTO_TEST
   bool forceRemoteCheck;
#endif

   LicensingClientSettings() : maximumSubscribers( 0 ), expiryYear( 0 ), expiryMonth( 0 ), expiryDay( 0 )
   {
#ifdef CPCAPI2_AUTO_TEST
      forceRemoteCheck = false;
#endif
   }
};

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_SETTINGS_H__
