#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_TYPES_H__)
#define __CPCAPI2_LICENSING_CLIENT_TYPES_H__

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Licensing
{

typedef unsigned int LicensingClientHandle;

enum LicenseStatus
{
   LicenseStatus_NoLicense = 0,
   LicenseStatus_Pending = 1,
   LicenseStatus_Valid = 2,
   LicenseStatus_Expired = 3,
   LicenseStatus_Invalid = 4,
   LicenseStatus_Grace = 5,
   LicenseStatus_Error = 6,
   LicenseStatus_ServerUnreachable = 7,
   LicenseStatus_ServerBadData = 8,
   LicenseStatus_ServerReject = 9,
   LicenseStatus_ServerAuthFailed = 10,
   LicenseStatus_ServerEmbeddedCert = 11,
   LicenseStatus_TrialMode = 12,
   LicenseStatus_BadAppResourceSignature = 13
};

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_TYPES_H__
