#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_MANAGER_H__)
#define __CPCAPI2_LICENSING_CLIENT_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "LicensingClientTypes.h"
#include "LicensingClientSettings.h"

namespace CPCAPI2
{
class Phone;

namespace Licensing
{
class LicensingClientHandler;

class CPCAPI2_SHAREDLIBRARY_API LicensingClientManager
{
public:
   /**
   * Get a reference to the LicensingClientManager interface.
   */
   static LicensingClientManager* getInterface(Phone* cpcPhone);
   
   /**
   * Create a new licensing client. First call setHandler(...) followed by applySettings(...) 
   * then validateLicenses() next. Call destroy(...) to dispose of the client.
   *
   * @return the handle to the new licensing client.
   */
   virtual LicensingClientHandle create() = 0;

   /**
   * Set the handler for events on the specified client.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
   *
   * @param client the licensing client.
   * @param handler the handler to register.
   *
   * @return kSuccess if operation is successful, false otherwise.
   */
   virtual int setHandler(LicensingClientHandle client, LicensingClientHandler* handler) = 0;

   /**
   * Configure the client with the specified settings. Call validateLicenses(...) next.
   * The applySettings(...) and validateLicenses(...) combination can be called multiple times. Call
   * destroy() to dispose of the client.
   *
   * @param client the licensing client.
   * @param settings the settings.
   *
   * @return the handle to the new licensing client.
   */
   virtual int applySettings(LicensingClientHandle client, const LicensingClientSettings& settings) = 0;

   /**
   * Validate the time limit and/or license keys specified in the settings.
   *
   * @param client the licensing client.
   *
   * @return kSuccess if operation is successful, false otherwise.
   */
   virtual int validateLicenses(LicensingClientHandle client) = 0;

   /**
   * Destroy a licensing client.
   *
   * @param client the licensing client.
   *
   * @return kSuccess if operation is successful, false otherwise.
   */
   virtual int destroy(LicensingClientHandle client) = 0;

   /**
   * Perform local license key sanity check for length and format.
   * Helper method only. Does not validate the key.
   *
   * @param licensekey the license key to verify
   *
   * @return kSuccess is key is valid, false otherwise 
   */
   virtual int checkValidLicenseKeyFormat(const cpc::string& licenseKey) = 0;

   /**
   * DEPRECATED: Use the version that does not require a client handle parameter.
   * Retrieve the hardware ID for this client.
   *
   * @return the hardware ID.
   */
   virtual int getHardwareId(LicensingClientHandle client, cpc::string& hardwareId) = 0;

   /**
   * Helper method to retrieve the hardware ID for this machine
   *
   * @return the hardware ID.
   */
   virtual int getHardwareId(cpc::string& hardwareId) = 0;

   /**
   * Helper method to retrieve the hardware hash (duui) for this machine
   *
   * @return the hardware hash
   */
   virtual int getHardwareHash(cpc::string& hardwareHash) = 0;

   /**
   * Fills out the expiry information (Year/Month/Day) based on the values branded into the SDK.
   */
   virtual int setBrandedExpiry(LicensingClientSettings& settings) const = 0;
   
   /**
   * Whether or not the licensing module will enforce branded app/resource signature check;
   * i.e. checking the applicaiton has a valid digital signature matching what we expect, along
   * with any signed resources
   */
   virtual bool appResourceSignatureCheckEnabled() const = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling MediaManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

protected:
   /*
    * The SDK will manage memory life of %LicensingClientManager.
    */
   virtual ~LicensingClientManager() {}
};

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_MANAGER_H__
