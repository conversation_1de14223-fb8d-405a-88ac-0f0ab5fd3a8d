#pragma once

#if !defined(CPCAPI2_CONFERENCE_REGISTRAR_HANDLER_H)
#define CPCAPI2_CONFERENCE_REGISTRAR_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace ConferenceBridge
{
typedef unsigned int ConferenceRegistrarHandle;

struct ConferenceRegistrarStartupResult
{
   bool success = false;
   bool localIsLeader = false;
};

struct ConferenceRegistrarAddFollowerResult
{
   bool success = false;
};

struct RegisterConferenceResult
{
   bool success = false;
};

class RegisterConferenceResultHandler
{
public:
   virtual int onRegisterConferenceComplete(ConferenceRegistrarHandle registrar, const RegisterConferenceResult& args) = 0;
   virtual bool synchronous() const {
      return false;
   }
};

struct LookupConferenceResult
{
   bool success = false;
   bool shouldRetry = false;
   cpc::string wsUrl;
   cpc::string redirect;
};

class LookupConferenceResultHandler
{
public:
   virtual int onLookupConferenceComplete(ConferenceRegistrarHandle registrar, const LookupConferenceResult& args) = 0;
   virtual bool synchronous() const {
      return false;
   }
};

struct ListNodesResult
{
   bool success = false;
   cpc::vector<cpc::string> nodes;
};

class ListNodesHandler
{
public:
   virtual int onListNodesComplete(ConferenceRegistrarHandle registrar, const ListNodesResult& args) = 0;
   virtual bool synchronous() const {
      return false;
   }
};

class ConferenceRegistrarHandler
{
public:
   virtual int onStartupComplete(ConferenceRegistrarHandle registrar, const ConferenceRegistrarStartupResult& args) = 0;
   virtual int onAddFollowerComplete(ConferenceRegistrarHandle registrar, const ConferenceRegistrarAddFollowerResult& args) = 0;
};

}
}
#endif // CPCAPI2_CONFERENCE_REGISTRAR_HANDLER_H
