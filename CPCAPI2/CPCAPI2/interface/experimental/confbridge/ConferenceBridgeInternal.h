#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_MANAGER_INTERNAL_H)
#define CPCAPI2_CONFERENCE_BRIDGE_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "ConferenceBridgeManager.h"

namespace CPCAPI2
{
namespace ConferenceBridge
{
   class CPCAPI2_SHAREDLIBRARY_API ConferenceBridgeManagerInternal : public ConferenceBridgeManager
   {
   public:
      static ConferenceBridgeManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

      virtual int getConferenceSummary(cpc::string& summary) = 0;
      virtual int queryMediaStatistics(ConferenceHandle conference) = 0;
   };  
}
}
#endif // CPCAPI2_CONFERENCE_BRIDGE_MANAGER_INTERNAL_H
