#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_TYPES_H)
#define CPCAPI2_CONFERENCE_BRIDGE_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "call/SipConversationTypes.h"
#include "media/Mixer.h"
#include "peerconnection/PeerConnectionTypes.h"

namespace CPCAPI2
{
namespace ConferenceBridge
{
   typedef unsigned int ConferenceHandle;
   typedef unsigned int ConferenceParticipantHandle;

   enum ParticipantType
   {
      ParticipantType_Web,
      ParticipantType_SIP
   };

   enum ParticipantMediaState
   {
      ParticipantMediaState_SendRecv,
      ParticipantMediaState_SendOnly,
      ParticipantMediaState_RecvOnly,
      ParticipantMediaState_Inactive
   };

   enum ParticipantMediaStreamType
   {
      ParticipantMediaStreamType_Audio,
      ParticipantMediaStreamType_Video,
      ParticipantMediaStreamType_Screenshare
   };

   struct ParticipantMediaStream
   {
      ParticipantMediaStreamType mediaType = ParticipantMediaStreamType_Audio;
      ParticipantMediaState mediaState = ParticipantMediaState_Inactive;
   };

   struct WebParticipantDetails
   {
      CPCAPI2::PeerConnection::PeerConnectionHandle peerConnection = 0;
   };

   struct SIPParticipantDetails
   {
      CPCAPI2::SipConversation::SipConversationHandle conversation = 0;
   };

   struct ParticipantPermissions
   {
      bool canCreateConference = false;
   };

   struct ParticipantInfo
   {
      ConferenceParticipantHandle participant = 0;
      ParticipantType participantType = ParticipantType_Web;
      cpc::string address;
      cpc::string displayName;
      cpc::vector<int> tags;
      cpc::vector<ParticipantMediaStream> media;
      //cpc::string snapshotFilenameUtf8;
      bool hasFloor = false;
      WebParticipantDetails webParticipantDetails;
      SIPParticipantDetails sipParticipantDetails;
      ParticipantPermissions permissions;
      uint64_t joinTimestampMsecs;
   };

   struct WebParticipantIdentity
   {
      cpc::string address;
      cpc::string displayName;
      bool owner;
      cpc::vector<int> tags;

      WebParticipantIdentity()
      {
         owner = false;
      }
   };

   enum ConferenceMixMode
   {
      ConferenceMixMode_NoMixing = 0,
      ConferenceMixMode_MCU = 1,
      ConferenceMixMode_SFU = 2,
      ConferenceMixMode_Legacy = 3,
      ConferenceMixMode_SFU_BiDi = 4
   };

   enum ConferenceMediaEncryptionMode
   {
      ConferenceMediaEncryptionMode_Unencrypted = 0,
      ConferenceMediaEncryptionMode_SRTP_SDES = 1,
      ConferenceMediaEncryptionMode_SRTP_DTLS = 2
   };

   struct ConferenceNatTraversalServerInfo
   {
      enum NatTraversalServerType
      {
         NatTraversalServerType_StunAndTurn = 0,
         NatTraversalServerType_StunOnly = 1,
         NatTraversalServerType_TurnOnly = 2
      };
      cpc::string natTraversalServerHostname;
      cpc::string natTraversalServerUsername;
      cpc::string natTraversalServerPassword;
      int natTraversalServerPort;
      NatTraversalServerType natTraversalServerType;
      NatTraversalServerType natTraversalServerType2;
      cpc::string serverPublicIpAddress;

      ConferenceNatTraversalServerInfo()
      {
         natTraversalServerPort = 3478;
         natTraversalServerType = ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn;
         natTraversalServerType2 = ConferenceNatTraversalServerInfo::NatTraversalServerType_StunOnly;
      }
   };

   struct ConferenceBitrateConfig
   {
      int videoMaxBitrateKbps;
      int videoTargetBitrateKbps;
      int videoStartBitrateKbps;
      int videoMinBitrateKbps;
      ConferenceBitrateConfig() :
         videoMaxBitrateKbps(2000),
         videoTargetBitrateKbps(2000),
         videoStartBitrateKbps(2000),
         videoMinBitrateKbps(500)
      {
      }
   };

   struct ConferenceSettings
   {
      cpc::string participantJoinSound;
      cpc::string participantLeaveSound;
      ConferenceMixMode mixMode;
      bool persistent;
      bool isPublic;
      bool adaptVideoCodecOnRemotePacketLoss;
      cpc::string label;
      cpc::vector<int> tags;
      cpc::string conferenceToken;
      CPCAPI2::Media::MixerSettings mixerSettings;
      int mediaDscp;

      ConferenceSettings()
      {
         mixMode = ConferenceMixMode_MCU;
         persistent = true;
         isPublic = false;
         adaptVideoCodecOnRemotePacketLoss = true;
         mediaDscp = 46; // EF
      }
   };

   struct ConferenceInfo
   {
      ConferenceInfo()
      {
         conference = 0;
         mediaEncryptionMode = ConferenceMediaEncryptionMode_Unencrypted;
         streamId = -1;
         transcriptionEnabled = false;
         numParticipants = 0;
      }
      cpc::string label;
      ConferenceParticipantHandle owner = (ConferenceParticipantHandle)(-1);
      cpc::vector<int> tags;
      cpc::string conferenceToken;
      ConferenceHandle conference;
      cpc::string description;
      cpc::string creatorDisplayName;
      cpc::vector<ConferenceInfo> associatedConferences;
      int streamId;
      bool transcriptionEnabled;
      int numParticipants;
      ConferenceNatTraversalServerInfo natTraversalServerInfo;
      ConferenceBitrateConfig bitrateConfig;
      ConferenceMediaEncryptionMode mediaEncryptionMode;
   };

   enum VideoLayout
   {
      VideoLayout_Auto,
      VideoLayout_Individual,
      VideoLayout_SharedGrid
   };

   struct ConferenceBridgeConfig
   {
      ConferenceBridgeConfig()
      {
         mediaEncryptionMode = ConferenceMediaEncryptionMode_Unencrypted;
         mediaInactivityTimeoutMs = -1;
         useServerUidInJoinUrls = false;
      }
      cpc::string userContext;
      cpc::string httpJoinUrlBase;
      cpc::string wsUrlBase;
      cpc::string serverUid;
      bool useServerUidInJoinUrls;
      ConferenceNatTraversalServerInfo natTraversalServerInfo;
      ConferenceBitrateConfig screenshareBitrateConfig;
      ConferenceBitrateConfig cameraBitrateConfig;
      ConferenceMediaEncryptionMode mediaEncryptionMode;
      int mediaInactivityTimeoutMs;
   };

}
}

#endif // CPCAPI2_CONFERENCE_BRIDGE_TYPES_H
