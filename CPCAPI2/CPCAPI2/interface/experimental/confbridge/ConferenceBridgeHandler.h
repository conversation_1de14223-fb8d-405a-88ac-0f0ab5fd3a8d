#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_HANDLER_H)
#define CPCAPI2_CONFERENCE_BRIDGE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "ConferenceBridgeTypes.h"
#include "peerconnection/PeerConnectionTypes.h"

namespace CPCAPI2
{
namespace ConferenceBridge
{

struct ConferenceDetailsResult
{
   ConferenceHandle conference;
   cpc::string conferenceToken;
   ConferenceInfo conferenceInfo;
   cpc::string conferenceJoinUrl;
   bool persistent = false;
};

struct ConferenceNotFoundResult
{
};

struct ConferenceListResult
{
   cpc::vector<ConferenceDetailsResult> conferences;
};

class ConferenceDetailsHandler
{
public:
   virtual int onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args) = 0;
   virtual int onConferenceNotFound(ConferenceHandle conference, const ConferenceNotFoundResult& args) = 0;
};

class ConferenceListHandler
{
public:
   virtual int onConferenceList(const ConferenceListResult& args) = 0;
};

struct ParticipantListState
{
   bool isPartialUpdate = false;
   bool isLastChunk = false;
   cpc::vector<ParticipantInfo> participants;
   cpc::vector<ParticipantInfo> addedParticipants;
   cpc::vector<ParticipantInfo> updatedParticipants;
   cpc::vector<ParticipantInfo> removedParticipants;
};

class ParticipantListHandler
{
public:
   virtual int onParticipantListState(ConferenceHandle conference, const ParticipantListState& args) = 0;
};

struct PeerConnectionAnswerEvent
{
   ConferenceParticipantHandle participant;
   CPCAPI2::PeerConnection::SessionDescription sdpAnswer;
};

class PeerConnectionAnswerHandler
{
public:
   virtual int onPeerConnectionAnswer(ConferenceHandle conference, const PeerConnectionAnswerEvent& args) = 0;
};

struct ConferenceTranscriptionEvent
{
   ConferenceParticipantHandle participant;
   cpc::string transcriptionResult;
   int resultSetId;
};

struct ConferenceEndedEvent
{
};

struct WebParticipantCreatedEvent
{
   bool success = false;
   ConferenceHandle conference;
   ConferenceParticipantHandle participant;
   bool hasFloor = false;
};

class CreateWebParticipantHandler
{
public:
   virtual int onWebParticipantCreated(ConferenceHandle conference, const WebParticipantCreatedEvent& args) = 0;
};

class ConferenceBridgeHandler
{
public:
   virtual int onConferenceDetails(ConferenceHandle conference, const ConferenceDetailsResult& args) = 0;
   virtual int onParticipantListState(ConferenceHandle conference, const ParticipantListState& args) = 0;
   virtual int onConferenceTranscriptionResult(ConferenceHandle conference, const ConferenceTranscriptionEvent& args) = 0;
   virtual int onConferenceEnded(ConferenceHandle conference, const ConferenceEndedEvent& args) = 0;
};

}
}
#endif // CPCAPI2_CONFERENCE_BRIDGE_HANDLER_H
