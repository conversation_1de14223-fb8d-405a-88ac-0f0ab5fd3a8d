#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_JSON_API_H)
#define CPCAPI2_CONFERENCE_BRIDGE_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace ConferenceBridge
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API ConferenceBridgeJsonApi
{
public:
   /**
   * Get a reference to the %ConferenceBridgeJsonApi interface.
   */
   static ConferenceBridgeJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_CONFERENCE_BRIDGE_JSON_API_H
