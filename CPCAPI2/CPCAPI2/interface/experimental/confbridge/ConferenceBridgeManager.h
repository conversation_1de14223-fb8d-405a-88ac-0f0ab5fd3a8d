#pragma once

#if !defined(CPCAPI2_ConferenceBridge_MANAGER_H)
#define CPCAPI2_ConferenceBridge_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "ConferenceBridgeTypes.h"
#include "peerconnection/PeerConnectionTypes.h"

namespace CPCAPI2
{
class Phone;

namespace ConferenceBridge
{
class ConferenceBridgeHandler;
class ConferenceDetailsHandler;
class ParticipantListHandler;
class ConferenceListHandler;
class PeerConnectionAnswerHandler;
class CreateWebParticipantHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API ConferenceBridgeManager
{
public:
   /**
   * Get a reference to the %ConferenceBridgeManager interface.
   */   
   static ConferenceBridgeManager* getInterface(Phone* cpcPhone);

   static const cpc::string& getServiceId();

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kConferenceBridgeModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling ConferenceBridgeManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};

   virtual int start(const ConferenceBridgeConfig& bridgeConfig) = 0;
   virtual int shutdown() = 0;

   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings) = 0;
   virtual ConferenceHandle createConference(const ConferenceSettings& conferenceSettings, ConferenceBridgeHandler* handler) = 0;
   virtual int destroyConference(ConferenceHandle conference) = 0;

   virtual int setHandler(ConferenceBridgeHandler* handler) = 0;
   virtual int setHandler(ConferenceHandle conference, ConferenceBridgeHandler* handler) = 0;

   virtual int addSipEndpoint(ConferenceHandle conference, CPCAPI2::SipAccount::SipAccountHandle sipAccount) = 0;

   virtual int queryConferenceDetails(ConferenceHandle conference) = 0;
   virtual int queryConferenceDetails(ConferenceHandle conference, ConferenceDetailsHandler* handler) = 0;
   virtual int queryConferenceDetails(const cpc::string& conferenceToken, ConferenceDetailsHandler* handler) = 0;
   virtual int queryConferenceList(ConferenceListHandler* handler) = 0;

   virtual int queryParticipantList(ConferenceHandle conference, ParticipantListHandler* handler) = 0;

   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference) = 0;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, CreateWebParticipantHandler* handler) = 0;
   virtual ConferenceParticipantHandle createWebParticipant(ConferenceHandle conference, const WebParticipantIdentity& identityInfo, bool addToFloor, CreateWebParticipantHandler* handler) = 0;
   virtual int setWebParticipantIdentity(ConferenceParticipantHandle webParticipant, const WebParticipantIdentity& identityInfo) = 0;
   virtual int takeParticipantSnapshot(ConferenceParticipantHandle participant) = 0;
   virtual int setParticipantPhoto(ConferenceParticipantHandle participant, const cpc::string& photoFileNameUtf8) = 0;
   virtual int destroyWebParticipant(ConferenceParticipantHandle webParticipant) = 0;
   virtual int setParticipantPermissions(ConferenceParticipantHandle participant, const ParticipantPermissions& permissionsInfo) = 0;

   virtual int sendPeerConnectionOffer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpOffer, PeerConnectionAnswerHandler* handler) = 0;
   virtual int sendPeerConnectionAnswer(ConferenceParticipantHandle webParticipant, const CPCAPI2::PeerConnection::SessionDescription& sdpAnswer) = 0;
   virtual int generateLocalOffer(ConferenceParticipantHandle webParticipant, PeerConnectionAnswerHandler* handler) = 0;

   virtual int addAssociatedConference(ConferenceHandle conference, ConferenceHandle associatedConference) = 0;

   virtual int requestFloor(ConferenceParticipantHandle requestor) = 0;
   virtual int addToFloor(ConferenceParticipantHandle participant) = 0;
   virtual int removeFromFloor(ConferenceParticipantHandle participant) = 0;

   virtual int setVideoLayout(ConferenceHandle conference, VideoLayout layout) = 0;

   virtual int setStreamingEnabled(ConferenceHandle conference, bool enabled) = 0;
   virtual int setRecordingEnabled(ConferenceHandle conference, bool enabled) = 0;
   virtual int setTranscriptionEnabled(ConferenceHandle conference, bool enabled) = 0;
   virtual bool isValidJoinUrl(const cpc::string& joinUrl) = 0;
   virtual int updateJoinUrlBase(const cpc::string& joinUrl) = 0;
};

}
}

#endif // CPCAPI2_ConferenceBridge_MANAGER_H
