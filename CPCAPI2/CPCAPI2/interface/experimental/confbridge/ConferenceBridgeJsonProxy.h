#pragma once

#if !defined(CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_H)
#define CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "confbridge/ConferenceBridgeManager.h"

namespace CPCAPI2
{
class Phone;
namespace ConferenceBridge
{
class CPCAPI2_SHAREDLIBRARY_API ConferenceBridgeManagerJsonProxy : public CPCAPI2::ConferenceBridge::ConferenceBridgeManager
{
public:
   /**
   * Get a reference to the ConferenceBridgeManagerJsonProxy interface.
   */
   static ConferenceBridgeManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %ConferenceBridgeManagerJsonProxy.
    */
   virtual ~ConferenceBridgeManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_CONFERENCE_BRIDGE_JSON_PROXY_H
