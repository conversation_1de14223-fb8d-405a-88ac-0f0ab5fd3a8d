#pragma once

#if !defined(CPCAPI2_CONFERENCE_REGISTRAR_H)
#define CPCAPI2_CONFERENCE_REGISTRAR_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "confbridge/ConferenceRegistrarHandler.h"

#define CONF_REGISTRAR_AUTH_TIMEOUT_SECONDS 5
#define CONF_REGISTRAR_ORCH_TIMEOUT_SECONDS 15

namespace CPCAPI2
{
class Phone;

namespace ConferenceBridge
{
   struct ConferenceRegistrarConfig
   {
      int nodeId = -1;
      cpc::string conferenceRegistrarServiceIp;
      int conferenceRegistrarServicePort = 2114;
      cpc::string wsUrlBase;

      cpc::string joinClusterUrl;
      cpc::string authServiceUrl;
      cpc::string authServiceApiKey;
      cpc::string jsonApiHostname;
      cpc::string urlMapFilename;
      int authenticationTimeoutSeconds = CONF_REGISTRAR_AUTH_TIMEOUT_SECONDS;
      int orchestrationTimeoutSeconds = CONF_REGISTRAR_ORCH_TIMEOUT_SECONDS;
   };

   struct ConferenceRegistrationInfo
   {
      cpc::string joinUrl;
      cpc::string wsUrl;
   };

   /**
   */
   class CPCAPI2_SHAREDLIBRARY_API ConferenceRegistrar
   {
   public:
      /**
      * Get a reference to the %ConferenceBridgeManager interface.
      */
      static ConferenceRegistrar* getInterface(Phone* cpcPhone);

      static const int kBlockingModeNonBlocking = -1;
      static const int kBlockingModeInfinite = 0;
      static const int kConferenceBridgeModuleDisabled = -1;

      /**
      * Allows the application code to receive callback notifications from the SDK.
      *
      * These callbacks will happen synchronously, in the same thread of execution as that in which
      * %process() is invoked.  Depending on the application threading model,
      * process() can be used in two different ways:
      * <ol>
      * <li>blocking mode ?Typically in this mode, %process() is called by the
      * application from a background (worker) thread.  The call to process()
      * blocks until a callback function needs to be invoked.
      * <li>non-blocking mode ?In this mode, %process() is called by the application
      * from the main (GUI) thread of the application, typically from the
      * main message/event loop.  In this mode, %process() returns immediately
      * and so must be called frequently enough that the application can receive
      * its callback notifications in a timely manner.
      *
      * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
      *                representing the time the call to process(..) will block waiting for a callback
      *                from the SDK
      *</ol>
      */
      virtual int process(unsigned int timeout) = 0;

      /**
      * Allows the application to "unblock" the
      * thread calling ConferenceBridgeManager::process(..) if it is blocked waiting for an SDK callback.
      * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
      */
      virtual void interruptProcess() = 0;

      virtual void setHandler(ConferenceRegistrarHandler* handler) = 0;

      virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};

      virtual int setExternalConferenceRegistrar(ConferenceRegistrar* confRegistrar) = 0;

      virtual int start(const ConferenceRegistrarConfig& confRegistrarConfig) = 0;
      virtual int shutdown() = 0;

      virtual int lookupConference(const cpc::string& joinUrl, LookupConferenceResultHandler* handler) = 0;

      virtual int listNodes(ListNodesHandler* handler) = 0;

      virtual bool isInService() = 0;
   };

}
}

#endif // CPCAPI2_CONFERENCE_REGISTRAR_H
