#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_SERVER_MANAGER_H)
#define CPCAPI2_PUSH_NOTIFICATION_SERVER_MANAGER_H

#include "cpcapi2defs.h"
#include "pushnotification/PushNotificationTypes.h"

namespace CPCAPI2
{
class Phone;

namespace PushNotification
{
class PushNotificationHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API PushNotificationServerManager
{
public:
   /**
   * Get a reference to the %PushNotificationServerManager interface.
   */   
   static PushNotificationServerManager* getInterface(Phone* cpcPhone);

   virtual int setHandler(PushNotificationHandler* handler) = 0;

   /**
    * Call configurePushProvider once for each PushNetworkType that you want to
    * configure (i.e. a new settings object with a different "pushNetworkType"
    * attribute as well as other settings.
    */
   virtual int configurePushProvider(const PushProviderSettings& pushProviderSettings) = 0;

   virtual int sendPushNotification(PushNotificationDeviceHandle device, const PushNotificationRequest& pushRequest) = 0;

   /**
    * Sends the push request to all known device handles. Use this with
    * caution, and sparingly.
    */
   virtual int broadcast( const PushNotificationRequest& pushRequest ) = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kPushNotificationServerModuleDisabled = -1;

   /**
    * Allows the application code to receive callback notifications from
    * the SDK.
    *
    * These callbacks will happen synchronously, in the same thread of
    * execution as that in which %process() is invoked.  Depending on the
    * application threading model, process() can be used in two different
    * ways:
    * <ol>
    * <li>blocking mode ?Typically in this mode, %process() is called by the 
    * application from a background (worker) thread.  The call to process() 
    * blocks until a callback function needs to be invoked.
    * <li>non-blocking mode ?In this mode, %process() is called by the application 
    * from the main (GUI) thread of the application, typically from the 
    * main message/event loop.  In this mode, %process() returns immediately 
    * and so must be called frequently enough that the application can receive 
    * its callback notifications in a timely manner.
    *
    * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
    *                representing the time the call to process(..) will block waiting for a callback
    *                from the SDK
    *</ol>
    */
   virtual int process(unsigned int timeout) = 0;

   /**
    * Allows the application to "unblock" the thread calling
    * PushNotificationManager::process(..) if it is blocked waiting for
    * an SDK callback.  Unblocking the process() thread is useful at
    * SDK/application shutdown in certain applications.
    */
   virtual void interruptProcess() = 0;
};
}
}

#endif // CPCAPI2_PUSH_NOTIFICATION_SERVER_MANAGER_H
