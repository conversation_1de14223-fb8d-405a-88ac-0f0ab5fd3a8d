#pragma once
#ifndef __CPCAPI2_ANALYTICSTYPES__
#define __CPCAPI2_ANALYTICSTYPES__

#include <cpcstl/string.h>
#include <cpcstl/unordered_map.h>

namespace CPCAPI2
{
   namespace Analytics
   {
      typedef unsigned int AnalyticsHandle;

      /**
       * Settings (which are usually provisioned on the client) for the location of
       * the stretto server, and various credentials required.
       */
      typedef struct AnalyticsSettings
      {
         cpc::string serverURL;       // Location of the Stretto server
         cpc::string httpUserName;    // the username (if any) used for HTTP-Auth
         cpc::string httpPassword;    // the password (if any) used for HTTP-Auth
         cpc::string strettoUserName; // Username for the stretto server
      } AnalyticsSettings;

      /**
       * All of this information is required from the client, before a session
       * can be opened to the analytics server.
       */
      typedef struct GeneralStats
      {
         cpc::string deviceUUID;        /* Unique hash which identifies the device/installation */
         cpc::string clientVersion;     /* client version number (release number) */
         unsigned int installationDate; /* Date of installation of the client (seconds since UNIX epoch) */
         cpc::string osType;
         cpc::string osVersion;
         cpc::string hardwareModel;
         cpc::string publicIPAddress;
         cpc::string launchTime;
         cpc::string language;
         cpc::string timezone;         /* In a form like: UTC-5:00 */
         cpc::string serialNumber;

         GeneralStats() : installationDate( 0 ) {}
      } GeneralStats;

      /**
       * Statistics information (optional) provided from the application
       */
      typedef struct PresenceStats
      {
         int numContacts;
         int numContactsWithPresence;

         PresenceStats() : numContacts( 0 ), numContactsWithPresence( 0 ) {}
      } PresenceStats;

      /**
       * Statistics information (optional) provided from the application
       */
      typedef struct ProvisioningStats
      {
         int successfulProvisionAttempts;
         int failedProvisionAttempts;

         ProvisioningStats() : successfulProvisionAttempts( 0 ), failedProvisionAttempts( 0 ) {}
      } ProvisioningStats;

      /**
       * Statistics information (optional) provided from the application
       */
      typedef struct StabilityStats
      {
         int numCrashes;

         StabilityStats() : numCrashes( 0 ) {}
      } StabilityStats;


      /**
       * Statistics information (optional) provided from the application to be placed in the settings_data report section
       */
      typedef cpc::unordered_map < cpc::string, cpc::string > SettingsStats;
   }
}

#endif // __CPCAPI2_ANALYTICSTYPES__

