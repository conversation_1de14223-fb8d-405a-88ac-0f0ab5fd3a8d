#ifndef AnalyticsManagerInt_hpp
#define AnalyticsManagerInt_hpp

#include "cpcstl/string.h"
#include "AnalyticsManager.h"
#include "AnalyticsHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace Analytics
   {

      struct OnReportCreatedSuccessEvent
      {
         cpc::string content;   // Final XML Doc Content
      };

      class AnalyticsManagerInt : public AnalyticsManager
      {
      public:

         /**
         * gets data from xmpp account SDK module regarding status change
         *
         * @param account a handle to the xmpp account
         * @param args XmppAccountStatusChangedEvent object with relevant status info
         */
         virtual int xmppAccountStatusChangeFired(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) = 0;

         /**
         * gets data from the ptt SDK module regarding service status change
         *
         * @param service a handle to the ptt service
         * @param args PttServiceStatusChangedEvent object with relevant status info
         */
         virtual int pttServiceStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PttServiceStatusChangedEvent& args) = 0;

         /**
         * gets data from the ptt SDK module regarding session status change
         *
         * @param session a handle to the ptt session
         * @param args PttSessionStatusChangedEvent object with relevant status info
         */
         virtual int pttSessionStatusChangeFired(CPCAPI2::PushToTalk::PushToTalkSessionHandle session, const CPCAPI2::PushToTalk::PttSessionStateChangedEvent& args) = 0;

         /**
         * gets the latest video device name when setCapture for video is called
         *
         * @param devInfo object with video device info
         */
         virtual int CurrentVideoDeviceUpdatedFired(CPCAPI2::Media::VideoDeviceInfo devInfo) = 0;

         /**
         * gets data from SIP/XMPP IM SDK modules regarding incoming/outgoing IM
         *
         * @param accountHandle a handle to the xmpp account
         * @param incoming tells you if the IM is incoming or outgoing
         * @param isSip teslls you if the event was fired from SIP IM or XMPP IM
         */
         virtual int instantMessageInfoFired(unsigned int accountHandle, const bool incoming, const bool isSip) = 0;

         /**
         * gets data from Call Recording SDK moduls regarding which conversations have started being recorded
         *
         * @param conversationHandle a handle to the conversation
         */
         virtual int ConvRecordingStarted(CPCAPI2::SipConversation::SipConversationHandle conversationHandle) = 0;

      protected:
         virtual ~AnalyticsManagerInt() {}
      };
      
      class AnalyticsHandlerInt : public AnalyticsHandler
      {
      public:
      
         /**
         * Event fired to application to indicate success in creating the final XML document, and exposes doc if need
         * This event is only for AUTOMATED TEST cases and is not ever fired to the application in real scenario.
         */
         virtual int onReportCreatedSuccess(const AnalyticsHandle& serverHandle, const OnReportCreatedSuccessEvent& evt) = 0;
      };
   }
}

#endif /* AnalyticsManagerInt_h */
