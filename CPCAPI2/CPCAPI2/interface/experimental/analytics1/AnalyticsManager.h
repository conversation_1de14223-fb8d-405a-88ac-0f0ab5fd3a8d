#pragma once
#ifndef __CPCAPI2_ANALYTICSMANAGER__
#define __CPCAPI2_ANALYTICSMANAGER__

#include "cpcapi2defs.h"
#include <cpcstl/string.h>

#include "AnalyticsTypes.h"
#include "xmpp/XmppAccountHandler.h"
#include "ptt/PushToTalkHandler.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"
#include "call/SipConversationTypes.h"
#include "media/audio/AudioHandler.h"
#include "media/video/VideoHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace Analytics
   {
      class AnalyticsHandler;

      /**
       * The "StrettoManager" manages interactions to the CounterPath Stretto
       * server, at least any which are not already covered by the RemoteSync
       * module (in general, that means provisioning and analytics functions
       * for the time being).
       */
      class CPCAPI2_SHAREDLIBRARY_API AnalyticsManager
      {
      public:
         /**
          * Get a reference to the AnalyticsManager interface.
          * This interface for Analytics needs to be called / created as early as possibe, maybe 
          * right after the Phone is created, and before accounts are created / initialized, because
          * Analytics module needs to listen to onAccountConfigured to get the account settings for the Doc
          * Both getInterface() and open() from below need to be called before any account is created/init
          */   
         static AnalyticsManager* getInterface(Phone* cpcPhone);

         /**
          * Method to add a notification listener to the Client API, to receive
          * responses, errors, and updates.
          * 
          * @param notificationListener
          */
         virtual void setHandler(AnalyticsHandler * notificationHandler) = 0;

         /**
          * Opens a connection to the stretto server, returned handle may be
          * used for further operations on the server.
          * Note: the serverHandle parameter is optional and should not be passed in ever by app layer. The parameter was only necessary for automated test cases.
          */
         virtual AnalyticsHandle open(const AnalyticsSettings& settings, const GeneralStats& general, const AnalyticsHandle& serverHandle=0) = 0;

         /**
          * Closes any open connections to the server and disposes of the
          * handles.
          */
         virtual int close(const AnalyticsHandle& serverHandle) = 0;

         /**
          * Sends a blob of analytics data up to the server for storage and
          * reporting  (analytics data is write-only). A side effect of sending the
          * report is that a new reporting interval is started, and the report data is
          * cleared, i.e. a new data set will begin after sending is completed.
          *
          * @param serverHandle a handle to an open stretto server
          */
         virtual int sendReport(const AnalyticsHandle& serverHandle) = 0;

         /**
          * Optional statistics around presence, provisioning, stability, and settings: all provided by the application.
          */
         virtual int setPresenceStats(const AnalyticsHandle& serverHandle, const PresenceStats& stats) = 0;
         virtual int setProvisioningStats(const AnalyticsHandle& serverHandle, const ProvisioningStats& stats) = 0;
         virtual int setStabilityStats(const AnalyticsHandle& serverHandle, const StabilityStats& stats) = 0;
         virtual int setSettingsStats(const AnalyticsHandle& serverHandle, const SettingsStats& stats) = 0;

         /**
          * The blocking modes for the process() function.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Method which must be called by the application in order to
          * 'service' events coming from the remote sync handler.
          */
         virtual int process(unsigned int timeout) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %AnalyticsManager.
          */
         virtual ~AnalyticsManager() {}
      };
   }
}

#endif // __CPCAPI2_ANALYTICSMANAGER__

