#pragma once
#ifndef __CPCAPI2_ANALYTICSHANDLER_H__
#define __CPCAPI2_ANALYTICSHANDLER_H__

#include <cpcstl/string.h>
#include "AnalyticsTypes.h"

namespace CPCAPI2
{
   namespace Analytics
   {
      typedef struct OnConnectionFailedEvent
      {
         long errNo; // The system-dependent error number, set when the connection failed
         cpc::string errorValue;
      } OnConnectionFailedEvent;

      typedef struct OnReportResponseEvent
      {
         long responseCode;   // HTTP response code
         cpc::string errorValue;
      } OnReportResponseEvent;

      class AnalyticsHandler
      {
      public:

         /**
          * Event fired to the application in order to indicate the failure of
          * connecting to the stretto server.
          */
         virtual int onConnectionFailed( const AnalyticsHandle& serverHandle, const OnConnectionFailedEvent& evt ) = 0;

         /**
          * Event fired to application to indicate success or failure of the report.
          * The response code is a number returned from the server.
          */
         virtual int onReportResponse( const AnalyticsHandle& serverHandle, const OnReportResponseEvent& evt ) = 0;
      };
   }
}

#endif // __CPCAPI2_ANALYTICSHANDLER_H__
