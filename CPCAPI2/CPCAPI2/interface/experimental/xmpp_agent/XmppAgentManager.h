#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_MANAGER_H)
#define CPCAPI2_XMPP_AGENT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
//#include "cpcstl/vector.h"
#include "XmppAgentTypes.h"
#include "jsonapi/JsonApiTypes.h"
#include "remotesync/RemoteSyncManager.h"

namespace CPCAPI2
{
class Phone;

namespace PushService
{
class PushNotificationServiceManager;
}

namespace JsonApi
{
class JsonApiServer;
}

namespace XmppAgent
{
class XmppAgentHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API XmppAgentManager
{
public:

   /**
    * Get a reference to the %XmppAgentManager interface.
   */

   static XmppAgentManager* getInterface(Phone* cpcPhone);

   static const cpc::string& getServiceId();

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kXmppAgentModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling XmppAgentManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

   virtual int setHandler(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler) = 0;

   virtual int setPushNotificationManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager) = 0;
   virtual int setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonServer) = 0;

   virtual XmppPushRegistrationHandle createXmppPushRegistration() = 0;
   virtual XmppPushRegistrationHandle createXmppPushRegistration(const CPCAPI2::JsonApi::AuthToken& authToken) = 0;
   virtual int registerForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo) = 0;
   virtual int unregisterForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration) = 0;

   virtual int requestEventHistory(XmppPushRegistrationHandle xmppPushRegistration) = 0;

   /**
    * Allows the application to pass the sync provisioning parameters to the XMPP Agent. The
    * request is ignored if the sync funcationality has already been instantiated. Make sure
    * to call registerForXmppPushNotifications prior to registering for sync.
   */
   virtual int registerForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings) = 0;
   virtual int unregisterForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration) = 0;

   /**
    * Logout initiates the following actions:
    *
    *    - de-registration of IM sync on the xmpp agent
    *    - de-registration of push notifications on the xmpp agent
    *    - disables and destroys the associated xmpp account
    *    - destroys associated account state instance on the xmpp agent manager
    *    - destroys associated account state instance on the xmpp state managers - account, vcard, roster, muc
    *
    * Triggers the onLogout callback once the logout steps have been completed, also results in the
    * XmppAccountHandler::onAccountStatusChanged callback with an account destroyed status.
   */
   virtual int logout(XmppPushRegistrationHandle xmppPushRegistration) = 0;
   virtual int logoutAccount(XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Application is responsible for allocating and deallocating the memory required to manage the complete list.
    * Query response is received in the onXmppAgentQueryListResult event.
   */
   // virtual int queryRegistrationList(cpc::vector<XmppPushRegistrationHandle>*& registrationList) { return 0; };
   virtual int queryXmppRegistrationList() { return 0; };

   /**
    * Query response is received in the onXmppAgentQueryInfoResult event.
   */
   virtual int queryXmppRegistrationInfo(XmppPushRegistrationHandle xmppPushRegistration) { return 0; };

};

}

}

#endif // CPCAPI2_XMPP_AGENT_MANAGER_H
