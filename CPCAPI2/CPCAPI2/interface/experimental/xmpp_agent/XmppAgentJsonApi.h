#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_JSON_API_H)
#define CPCAPI2_XMPP_AGENT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace XmppAgent
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API XmppAgentJsonApi
{
public:
   /**
   * Get a reference to the %XmppAgentJsonApi interface.
   */
   static XmppAgentJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_XMPP_AGENT_JSON_API_H
