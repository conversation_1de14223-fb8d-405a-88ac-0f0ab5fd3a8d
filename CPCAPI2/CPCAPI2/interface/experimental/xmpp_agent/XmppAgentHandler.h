#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_HANDLER_H)
#define CPCAPI2_XMPP_AGENT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "XmppAgentTypes.h"
#include "jsonapi/JsonApiTypes.h"
#include <remotesync/RemoteSyncTypes.h>

namespace CPCAPI2
{

namespace XmppAgent
{

struct XmppPushRegistrationSuccessEvent
{
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushEndpointId;
   CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle;

   XmppPushRegistrationSuccessEvent() : pushEndpointId(""), xmppAccountHandle(0) {}
};

struct XmppPushRegistrationFailureEvent
{
   cpc::string errorText;
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushEndpointId;
   CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle;

   XmppPushRegistrationFailureEvent() : errorText(""), pushEndpointId(""), xmppAccountHandle(0) {}
};

struct XmppEventHistory
{
   cpc::vector<XmppChatEvent> chatEventHistory;
};

struct XmppAgentRemoteSyncRegisterResult
{
   RemoteSync::SessionHandle sessionHandle;
};

struct LogoutResult
{
   bool success;
};

struct XmppAgentQueryListResult
{
   cpc::vector<CPCAPI2::XmppAgent::XmppPushRegistrationHandle> registrationList;
};

struct XmppAgentQueryInfoResult
{
   cpc::string xmppAccount;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle xmppPushRegistration;
   CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle;
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushNotificationDev;
   CPCAPI2::PushService::PushNotificationServiceHandle pushNotificationServiceHandle;
   CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle;
   CPCAPI2::RemoteSync::SessionHandle syncSessionHandle;
   bool isRegisteredForPush;
   bool isLoggedOut;
   uint64_t serviceDownAtMs;

   XmppAgentQueryInfoResult() { reset(); }

   void reset()
   {
      xmppAccount = "";
      xmppPushRegistration = 0;
      xmppAccountHandle = 0;
      pushNotificationDev = 0;
      pushNotificationServiceHandle = 0;
      jsonUserHandle = 0;
      syncSessionHandle = 0;
      isRegisteredForPush = false;
      isLoggedOut = false;
      serviceDownAtMs = 0;
   }
};

class XmppAgentHandler
{

public:

   virtual int onPushRegistrationSuccess(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationSuccessEvent& args) = 0;
   virtual int onPushRegistrationFailure(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationFailureEvent& args) = 0;
   virtual int onEventHistory(XmppPushRegistrationHandle pushRegistration, const XmppEventHistory& args) = 0;
   virtual int onRemoteSyncRegisterResult(XmppPushRegistrationHandle pushRegistration, const XmppAgentRemoteSyncRegisterResult& args) = 0;
   virtual int onLogout(XmppPushRegistrationHandle pushRegistration, const LogoutResult& args) = 0;
   virtual int onXmppAgentQueryListResult(const CPCAPI2::XmppAgent::XmppAgentQueryListResult& args) { return 0; };
   virtual int onXmppAgentQueryInfoResult(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppAgentQueryInfoResult& args) { return 0; };

};

}

}

#endif // CPCAPI2_XMPP_AGENT_HANDLER_H
