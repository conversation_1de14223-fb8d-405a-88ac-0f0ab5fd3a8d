#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_TYPES_H)
#define CPCAPI2_XMPP_AGENT_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "push_endpoint/PushNotificationCommonTypes.h"
#include "push_service/PushNotificationServiceTypes.h"
#include "jsonapi/JsonApiTypes.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppChatHandler.h"

namespace CPCAPI2
{
namespace XmppAgent
{
typedef uint64_t XmppPushRegistrationHandle;

struct XmppPushRegistrationInfo
{
   CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle;
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushNotificationDev;
   CPCAPI2::PushService::PushNotificationServiceHandle pushNotificationServiceHandle; // Applicable to json server side only
   CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle; // Applicable to json server side only
   cpc::string pushServerUrl;

   XmppPushRegistrationInfo()
   {
      xmppAccountHandle = 0;
      pushNotificationDev = 0;
      pushNotificationServiceHandle = 0;
      jsonUserHandle = 0;
   }
};

enum XmppChatEventType
{
   XmppChatEventType_NewChat,
   XmppChatEventType_NewMessage,
   XmppChatEventType_ChatEnded
};

struct XmppChatEvent
{
   cpc::string eventId;
   XmppChatEventType eventType;
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   CPCAPI2::XmppChat::XmppChatHandle chat;

   CPCAPI2::XmppChat::NewChatEvent newChatEvent;
   CPCAPI2::XmppChat::NewMessageEvent newMessageEvent;
   CPCAPI2::XmppChat::ChatEndedEvent chatEndedEvent;
};
   
cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& info);
cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppChatEvent& evt);
cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppChatEventType& type);
cpc::string get_debug_string(const cpc::vector<CPCAPI2::XmppAgent::XmppChatEvent>& events);
   
}
   
}

#endif // CPCAPI2_XMPP_AGENT_TYPES_H
