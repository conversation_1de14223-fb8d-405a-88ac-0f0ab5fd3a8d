#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_MANAGER_INTERNAL_H)
#define CPCAPI2_XMPP_AGENT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "XmppAgentManager.h"

namespace CPCAPI2
{

namespace XmppAgent
{

   class CPCAPI2_SHAREDLIBRARY_API XmppAgentManagerInternal : public XmppAgentManager
   {

   public:

      static XmppAgentManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void addSdkObserver(XmppAgentHandler* observer) {};
      virtual void removeSdkObserver(XmppAgentHandler* observer) {};

   };

}

}

#endif // CPCAPI2_XMPP_AGENT_MANAGER_INTERNAL_H
