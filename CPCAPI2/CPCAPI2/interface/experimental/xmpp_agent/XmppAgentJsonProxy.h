#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_JSON_PROXY_H)
#define CPCAPI2_XMPP_AGENT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "xmpp_agent/XmppAgentManager.h"

namespace CPCAPI2
{
class Phone;
namespace XmppAgent
{
class CPCAPI2_SHAREDLIBRARY_API XmppAgentManagerJsonProxy : public CPCAPI2::XmppAgent::XmppAgentManager
{
public:
   /**
   * Get a reference to the XmppAgentManager interface.
   */
   static XmppAgentManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %XmppAgentManager.
    */
   virtual ~XmppAgentManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_XMPP_AGENT_JSON_PROXY_H
