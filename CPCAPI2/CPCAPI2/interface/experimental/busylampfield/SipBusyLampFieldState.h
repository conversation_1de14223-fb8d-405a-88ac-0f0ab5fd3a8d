#pragma once

#if !defined(CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_H)
#define CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipBusyLampFieldTypes.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"

namespace CPCAPI2
{ 
namespace SipBusyLampField 
{
class SipBusyLampFieldManager;

/**
 * State information of a remote line.
 */ 
struct SipBusyLampFieldRemoteLineState
{
   SipBusyLampFieldRemoteLineHandle remoteLine;               // URI of the affected remote line
   bool subscriptionStarted;                                  // True, subscription is started, false otherwise
   CPCAPI2::SipEvent::SipSubscriptionState subscriptionState; // State of the subscription
   cpc::vector<SipBusyLampFieldRemoteLineCallInfo> calls;     // Calls on the remote line
};

/**
 * State information of a set of remote lines.
 */ 
struct SipBusyLampFieldRemoteLineSetState
{
   CPCAPI2::SipAccount::SipAccountHandle account;                 // Account associated with the set
   SipBusyLampFieldRemoteLineSetHandle remoteLineSet;             // The set    
   cpc::vector<SipBusyLampFieldRemoteLineState> remoteLineStates; // The state information of the remote lines part of the set
};

/*
 * Manager interface that provides a mechanism for obtaining up-to-date cached information
 * about a specific remote line or a set of remote lines; get a reference to the interface 
 * using the static method getInterface(), then call getState() or getCall() to obtain the 
 * information.
 */
class CPCAPI2_SHAREDLIBRARY_API SipBusyLampFieldStateManager
{
public:
   /**
    * Get a reference to the %SipBusyLampFieldStateManager interface.
    *
    * @param blfManager the BLF manager instance.
    */   
   static SipBusyLampFieldStateManager* getInterface(SipBusyLampFieldManager* blfManager);

   /**
    * Retrieve the state information of a remote line set.
    *
    * @param remoteLineSet the set to query state information from.
    * @param remoteLinSetState the state information of the set (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getState(
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
      SipBusyLampFieldRemoteLineSetState& remoteLineSetState) = 0;

   /**
    * Retrieve the state information of a remote line in a given line set.
    *
    * @param remoteLineSet the set that contains the remote line.
    * @param remoteLine the remote line to query state from.
    * @param remoteLineState the state of the remote line (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getState(
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet,
      SipBusyLampFieldRemoteLineHandle remoteLine, 
      SipBusyLampFieldRemoteLineState& remoteLineState) = 0;

   /**
    * Retrieve the call information of a remote line.
    *
    * @param remoteLineSet the set that contains the remote line.
    * @param remoteLine the remote line that has the call.
    * @param dialogId the id of the call/dialog to query call information from.
    * @param call the call information of the remote line (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getCall(
      SipBusyLampFieldRemoteLineSetHandle remoteLineSet,
      SipBusyLampFieldRemoteLineHandle remoteLine, 
      const cpc::string& dialogId,
      SipBusyLampFieldRemoteLineCallInfo& call) = 0;
};

}
}

#endif // CPCAPI2_SIP_BUSY_LAMP_FIELD_STATE_H
