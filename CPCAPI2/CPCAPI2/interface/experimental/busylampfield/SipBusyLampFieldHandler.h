#pragma once

#if !defined(CPCAPI2_SIP_BUSY_LAMP_FIELD_HANDLER_H)
#define CPCAPI2_SIP_BUSY_LAMP_FIELD_HANDLER_H

#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipBusyLampFieldTypes.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"
#include "call/SipConversationTypes.h"
#include "account/SipAccount.h"

namespace CPCAPI2
{
namespace SipBusyLampField
{

/**
 * Event passed in SipBusyLampFieldHandler::onRemoteLineNewSubscription().
 *
 * A new subscription for a remote line has been created.
 */
struct RemoteLineNewSubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with the remote line
   SipBusyLampFieldRemoteLineHandle remoteLine;   // URI of the affected remote line
   cpc::string remoteLineName;                    // name of the remote line if known
};

/**
 * Event passed in SipBusyLampFieldHandler::onRemoteLineSubscriptionStateChanged().
 *
 * The state of the subscription for a remote line has changed.
 */
struct RemoteLineSubscriptionStateChangedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;             // Account associated with the remote line
   SipBusyLampFieldRemoteLineHandle remoteLine;               // URI of the affected remote line
   CPCAPI2::SipEvent::SipSubscriptionState subscriptionState; // The new state of the subscription
};

/**
 * Event passed in SipBusyLampFieldHandler::onRemoteLineSubscriptionTerminated().
 *
 * The subscription session for a remote line is terminated (for any reason).
 */
struct RemoteLineSubscriptionEndedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;         // Account associated with the remote line
   SipBusyLampFieldRemoteLineHandle remoteLine;           // URI of the affected remote line
   CPCAPI2::SipEvent::SipSubscriptionEndReason endReason; // The reason for the subsciption termination
};

/**
 * Event passed in SipBusyLampFieldHandler::onRemoteLineStateChanged().
 *
 * The dialog state information of a remote line has changed.
 */
struct RemoteLineStateChangedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;         // Account associated with the remote line
   SipBusyLampFieldRemoteLineHandle remoteLine;           // URI of the affected remote line
   cpc::vector<SipBusyLampFieldRemoteLineCallInfo> calls; // Calls on the remote line
   int orderNumber;                                       // 1 based order number of line in the resource, 0 means unknown / no change
};

/**
 * Event passed in SipBusyLampFieldHandler::onRemoteLineEmptySubscriptions().
 *
 * The dialog state information of all remote line has been removed.
 */
struct RemoteLineEmptySubscriptionsEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;         // Account associated with all remote lines removed
};

/**
* Event passed in SipBusyLampFieldHandler::onError().
*
* Used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with the remote line
   SipBusyLampFieldRemoteLineHandle remoteLine;   // URI of the affected remote line (optional)
   cpc::string errorText;
};

/**
* Handler for events related to BLF remote line subscriptions; 
* set in SipBusyLampFieldManager::setHandler().
*/
class SipBusyLampFieldHandler
{
public:
   /**
    * ALU15.
    *
    * Callback invoked by the SDK when a new subscription for a remote line has been created.
    *
    * @param remoteLineSet the affected remote line set.
    * @param args information about the new subscription state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onRemoteLineNewSubscription(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const RemoteLineNewSubscriptionEvent& args) = 0;

   /**
    * ALU15.
    *
    * Callback invoked by the SDK when the state of subscription for a remote line has changed.
    *
    * @param remoteLineSet the affected remote line set.
    * @param args information about the subscription state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onRemoteLineSubscriptionStateChanged(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const RemoteLineSubscriptionStateChangedEvent& args) = 0;

   /**
    * ALU15.
    *
    * Callback invoked by the SDK when the subscription for a remote line has ended.
    *
    * @param remoteLineSet the affected remote line set.
    * @param args information about the terminated subscription.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onRemoteLineSubscriptionEnded(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const RemoteLineSubscriptionEndedEvent& args) = 0;

   /**
    * ALU15.
    *
    * Callback invoked by the SDK when the state of a remote line has changed.
    *
    * @param remoteLineSet the remote line set that contains the affected remote line.
    * @param args information about the state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onRemoteLineStateChanged(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const RemoteLineStateChangedEvent& args) = 0;

   /**
    * ALU15.
    *
    * Callback invoked by the SDK when the all remote line has been removed.
    *
    * @param remoteLineSet the remote line set that contains the affected remote line.
    * @param args information about the state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onRemoteLineEmptySubscriptions(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet,
         const RemoteLineEmptySubscriptionsEvent& args) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param remoteLineSet the remote line set the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const ErrorEvent& args) = 0;
};

}
}

#endif // CPCAPI2_SIP_BUSY_LAMP_FIELD_HANDLER_H
