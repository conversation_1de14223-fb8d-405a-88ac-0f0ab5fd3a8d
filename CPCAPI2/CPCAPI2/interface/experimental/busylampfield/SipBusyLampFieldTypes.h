#pragma once

#if !defined(CPCAPI2_SIP_BUSY_LAMP_FIELD_TYPES_H)
#define CPCAPI2_SIP_BUSY_LAMP_FIELD_TYPES_H

#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "dialogevent/SipDialogEventModel.h"

namespace CPCAPI2
{
namespace SipBusyLampField
{

// Typedefs
typedef unsigned int SipBusyLampFieldRemoteLineSetHandle;
typedef cpc::string SipBusyLampFieldRemoteLineHandle;

/**
 * ALU27.
 *
 * List of supported barge-in modes.
 */
enum BargeInMode
{
   BargeInMode_Normal,
   BargeInMode_Whisper,
   BargeInMode_Listen
};

/**
 * ALU28.
 *
 * List of supported alert modes.
 */
enum AlertMode
{
   AlertMode_Silent,
   AlertMode_Normal
};

/**
 * ALU15.
 *
 * Settings that apply to a set of remote lines.
 *
 * Used with SipBusyLampFieldManager::createBusyLampFieldRemoteLineSet(..) to control the
 * behaviour of the SDK with respect to BLF related functionality.
 */
struct SipBusyLampFieldRemoteLineSetSettings
{
   int expires;                                  // Expires value to use for subscriptions on remote lines
   cpc::vector<BargeInMode> allowedBargeInModes; // ALU27: List of allowed barge-in modes  (when joining a call).
   bool allowedSilentAlertMode;                  // ALU28: Whether silent alert mode is allowed or not (when joining a call).
   cpc::string resourceListAddress;              // URI of resource list server if applicable (RFC 4662)
         
   SipBusyLampFieldRemoteLineSetSettings()
   {
      expires = 3600;
      allowedBargeInModes.push_back(BargeInMode_Normal);
      allowedBargeInModes.push_back(BargeInMode_Whisper);
      allowedBargeInModes.push_back(BargeInMode_Listen);
      allowedSilentAlertMode= true;
   }
};

/**
 * Dialog/call information on a remote line.
 * The id of the dialog is stored as an attribute of the dialog.
 */
struct SipBusyLampFieldRemoteLineCallInfo
{
   SipBusyLampFieldRemoteLineHandle remoteLine; // Remote line URI
   CPCAPI2::SipDialogEvent::DialogInfo dialog;  // Information on the call associated with the remote line
   bool isHeld;                                 // True, the call is held, false otherwise
   bool isParked;                               // True, the call is parked, false otherwise
};

}
}

#endif // CPCAPI2_SIP_BUSY_LAMP_FIELD_TYPES_H
