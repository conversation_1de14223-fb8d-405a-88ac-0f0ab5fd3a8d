#pragma once

#if !defined(CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_H)
#define CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipBusyLampFieldTypes.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{
class Phone;

namespace SipBusyLampField
{
class SipBusyLampFieldHandler;

/**
 * Manages the subscriptions for all the sets of remote lines created 
 * with this entity. Each set contains a list of remote line URIs to subscribe to. 
 *
 * A remote line can have multiple calls. A call is characterized by its
 * remote line URI and dialog id.
 *
 * Call features can be performed on a remote line such as answering a ringing call
 * and joining/barging into an established call.
 *
 * The subscription mechanism allows this entity to monitor remote lines. Notifications 
 * are generated as a result of call features being carried out on remote lines, both
 * locally and remotely.
 *
 * Please note that a remote line URI can be a SCAP PUID, which allows SCAP monitoring
 * entities to take advantage of the barge-in call feature.
 */
class CPCAPI2_SHAREDLIBRARY_API SipBusyLampFieldManager
{
public:
   /**
    * Get a reference to the %SipBusyLampFieldManager interface.
    */   
   static SipBusyLampFieldManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for remote line related events on the specified account. Set the handler
    * immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the same thread as 
    * SipAccountManager::process(..)
    *
    * @param account the user account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipBusyLampFieldHandler* handler) = 0;

   /**
    * ALU15.
    *
    * Create a set that contains a list of remote lines to monitor.
    *
    * @param account the user account to be associated with the set.
    * @param settings the settings.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual SipBusyLampFieldRemoteLineSetHandle createBusyLampFieldRemoteLineSet(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const SipBusyLampFieldRemoteLineSetSettings& settings) = 0;

   /**
    * ALU15.
    *
    * Add a remote line to be monitored to a set.
    * When using resource lists, this method is not necessary. The resource
    * list address should be provided in SipBusyLampFieldRemoteLineSetSettings.
    *
    * @param remoteLineSet the set of remote lines to add the line to.
    * @param remoteLineUri the URI of the remote line to monitor.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int addRemoteLine(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const SipBusyLampFieldRemoteLineHandle& remoteLine) = 0;

   /**
    * ALU15.
    *
    * Start subscriptions on all the remote lines included in the set.
    *
    * @param remoteLineSet the set of remote lines to use.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int start(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) = 0;

   /**
    * ALU15.
    *
    * End subscriptions on all the remote lines included in the set.
    *
    * @param remoteLineSet the set of remote lines to use.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int end(SipBusyLampFieldRemoteLineSetHandle remoteLineSet) = 0;

   /**
    * ALU25.
    *
    * Answer a ringing call (incoming) on a remote line. This results into a local call 
    * being created.
    *
    * A new conversation is created and started using SipConversationManager::start().
    * The caller of this method must be prepared to handle all SipConversationManager 
    * related events fired as a result. 
    *
    * A notification (BusyLampFieldRemoteLineStateChanged) is generated which indicates that
    * the call on remote line in question is terminated.
    *
    * @param remoteLineSet the set that contains the remote line that contains the call to answer.
    * @param remoteLine the remote line that has the call to answer.
    * @param dialogId the id of the call/dialog to answer.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle answerCall(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const SipBusyLampFieldRemoteLineHandle& remoteLine,
         const cpc::string& dialogId,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) = 0;

   /**
    * ALU26.
    *
    * Join/barge into an established/connected call on a remote line. The remote call in question 
    * must be in the 'Confirmed' state. This results into a conference being setup and local call
    * being created. 
    *
    * A new conversation is created and started using SipConversationManager::start().
    * The caller of this method must be prepared to handle all SipConversationManager related events 
    * fired as a result. 
    *
    * A notification (BusyLampFieldRemoteLineStateChanged) is generated which indicates that
    * the remote call in question is still in the 'Confirmed' state.
    *
    * @param remoteLineSet the set that contains the remote line that contains the call to join.
    * @param remoteLine the remote line that has the call to join.
    * @param dialogId the id of the call/dialog to join.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    * @param bargeInMode specifies the type of the barge-in mode. It is one of barge-in modes
    *                    from the following list: SipBusyLampFieldRemoteLineSetSettings::bargeInModes. 
    *                    Defaults to normal barge-in mode. 
    * @param alertMode indicates the type of alert mode to use during the barge-in. Defaults to normal alert mode.
    *
    * @return a handle to the new conversation or 0 if failure creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle joinCall(
         SipBusyLampFieldRemoteLineSetHandle remoteLineSet, 
         const SipBusyLampFieldRemoteLineHandle& remoteLine,
         const cpc::string& dialogId,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo(),
         BargeInMode bargeInMode = BargeInMode_Normal, 
         AlertMode alertMode = AlertMode_Normal) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipBusyLampFieldManager.
    */
   virtual ~SipBusyLampFieldManager() {}
};

}
}

#endif // CPCAPI2_SIP_BUSY_LAMP_FIELD_MANAGER_H
