#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_HANDLER_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "pushnotification/PushNotificationTypes.h"

namespace CPCAPI2
{
   namespace PushNotification
   {
      /**
       * The format of the json document provided for the direct push
       * notifications will follow the format outlined for google FCM (I had to
       * pick a standard, so that one made sense).
       *
       * @see https://firebase.google.com/docs/cloud-messaging/http-server-ref
       */
      typedef struct PushNotificationEvent
      {
         cpc::string jsonDocument;
      } PushNotificationEvent;

      /**
       * The "client" handler is used primarily for sending a push notification
       * directly to the client (via websocket or conceivably some other
       * mechanism in the future). If an application sets the client handler,
       * it will be notified directly via this mechanism over the jsonAPI
       * channel. This is meant primarily for platforms which cannot or will
       * not support the usual push networks, however in theory it is not
       * limited only to those platforms.
       *
       * IMPORTANT: This type of "push" notification will not wake up any
       * application which is suspended on mobile devices, unlike APN and FCM.
       */
      class PushNotificationClientHandler
      {
      public:
         /**
          * A "push" notification is sent directly to the client from the agent.
          *
          * @param deviceToken the registered device token for the client. This
          *        should not be a surprise to the application as it is the same
          *        token provided by the client.
          * @param evt the PushNotificationEvent which contains the json document.
          *        This is provided as a json document in order to mirror the
          *        behavior of the other push network types.
          */
         virtual int onPushNotification(
            PushNotificationDeviceHandle h,
            const PushNotificationEvent& evt ) = 0;
      };

      /**
       * "stub" class which implements each method in the interface. An
       * application may choose to extend this rather than implement the
       * interface directly, in order to avoid breakages when new methods are
       * added/removed.
       */
      class PushNotificationClientHandlerStub : public PushNotificationClientHandler
      {
      public:
         virtual int onPushNotification(
            PushNotificationDeviceHandle h,
            const PushNotificationEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}

#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_HANDLER_H
