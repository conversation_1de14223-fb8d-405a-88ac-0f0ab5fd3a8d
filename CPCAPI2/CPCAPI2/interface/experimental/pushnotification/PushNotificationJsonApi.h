#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_JSON_API_H)
#define CPCAPI2_PUSH_NOTIFICATION_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"
#include "pushnotification/PushNotificationClientManager.h"

namespace CPCAPI2
{

class Phone;

namespace PushNotification
{

/**
*/
class CPCAPI2_SHAREDLIBRARY_API PushNotificationJsonApi
{
public:
   /**
   * Get a reference to the %PushNotificationJsonApi interface.
   */
   static PushNotificationJsonApi* getInterface(Phone* cpcPhone);

   virtual int setPushNotificationClientManager(CPCAPI2::PushNotification::PushNotificationClientManager* pushManager) = 0;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_JSON_API_H
