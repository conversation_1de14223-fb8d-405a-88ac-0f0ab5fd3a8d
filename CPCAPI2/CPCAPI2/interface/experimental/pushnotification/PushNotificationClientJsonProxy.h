#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "pushnotification/PushNotificationClientManager.h"

namespace CPCAPI2
{
class Phone;
namespace PushNotification
{
class CPCAPI2_SHAREDLIBRARY_API PushNotificationClientManagerJsonProxy : public CPCAPI2::PushNotification::PushNotificationClientManager
{
public:
   /**
   * Get a reference to the PushNotificationClientManagerJsonProxy interface.
   */
   static PushNotificationClientManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %PushNotificationClientManagerJsonProxy.
    */
   virtual ~PushNotificationClientManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H
