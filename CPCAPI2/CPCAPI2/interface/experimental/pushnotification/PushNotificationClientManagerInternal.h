#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_MANAGER_INTERNAL_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "PushNotificationClientManager.h"

namespace CPCAPI2
{

namespace PushNotification
{

   class CPCAPI2_SHAREDLIBRARY_API PushNotificationClientManagerInternal : public PushNotificationClientManager
   {

   public:

      static PushNotificationClientManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   };  

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_MANAGER_INTERNAL_H
