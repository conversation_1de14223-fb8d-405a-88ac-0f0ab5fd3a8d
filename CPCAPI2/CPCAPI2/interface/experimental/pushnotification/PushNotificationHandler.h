#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_HANDLER_H)
#define CPCAPI2_PUSH_NOTIFICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "pushnotification/PushNotificationTypes.h"

namespace CPCAPI2
{
   namespace PushNotification
   {
      typedef struct NotificationSuccessEvent
      {
      } NotificationSuccessEvent;

      typedef struct NotificationFailureEvent
      {
      } NotificationFailureEvent;

      /**
      */
      class PushNotificationHandler
      {
      public:
         /**
          * Posted by the SDK when the notification was successfully sent
          */
         virtual int onNotificationSuccess(PushNotificationDeviceHandle device, const NotificationSuccessEvent& evt ) = 0;
         virtual int onNotificationFailure(PushNotificationDeviceHandle device, const NotificationFailureEvent& evt ) = 0;
      };

      /**
       * "stub" class which implements each method in the interface. An
       * application may choose to extend this rather than implement the
       * interface directly, in order to avoid breakages when new methods are
       * added/removed.
       */
      class PushNotificationHandlerStub : public PushNotificationHandler
      {
      public:
         virtual int onNotificationSuccess(PushNotificationDeviceHandle device, const NotificationSuccessEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onNotificationFailure(PushNotificationDeviceHandle device, const NotificationFailureEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}

#endif // CPCAPI2_PUSH_NOTIFICATION_HANDLER_H
