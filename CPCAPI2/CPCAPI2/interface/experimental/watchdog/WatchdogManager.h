#pragma once
#ifndef WATCHDOG_MANAGER_h
#define WATCHDOG_MANAGER_h

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "phone/Phone.h"

namespace CPCAPI2
{
namespace Watchdog
{
   struct WatchdogSettings
   {
      /**
       * the path to save memory dumps to. Trailing slash not necessary
       */
      cpc::string dumpFolder;
      
      WatchdogSettings()
      {
      }
   };

   class CPCAPI2_SHAREDLIBRARY_API WatchdogManager
   {
   public:
      static WatchdogManager* getInterface(Phone* cpcPhone);

      virtual int configureSettings(const WatchdogSettings&) = 0;
      
      virtual int startThreadWatchdog() = 0;
      
   protected:
         /*
          * The SDK will manage memory life of %WatchdogManager.
          */
      virtual ~WatchdogManager() {}
   };
}
}



#endif /* WATCHDOG_MANAGER */
