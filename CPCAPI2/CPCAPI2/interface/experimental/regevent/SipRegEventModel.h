#if !defined(CPCAPI2_SIP_REG_EVENT_MODEL_H)
#define CPCAPI2_SIP_REG_EVENT_MODEL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace SipRegEvent
{

enum ContactState
{
   ContactState_Active     = 1000,
   ContactState_Terminated = 1010
};

enum RegistrationState
{
   RegistrationState_Init        = 1100,
   RegistrationState_Active      = 1110,
   RegistrationState_Terminated  = 1120
};

enum RegEvent
{
   RegEvent_Registered     = 1200,
   RegEvent_Created        = 1210,
   RegEvent_Refreshed      = 1220,
   RegEvent_Shortened      = 1230,
   RegEvent_Expired        = 1240,
   RegEvent_Deactivated    = 1250,
   RegEvent_Probation      = 1260,
   RegEvent_Unregistered   = 1270,
   RegEvent_Rejected       = 1280
};

struct Contact
{
   cpc::string id;
   cpc::string uri;
   cpc::string displayName;
   ContactState state;
   cpc::string callId;
};

struct Registration
{
   cpc::string addressOfRecord;
   cpc::string id;
   cpc::vector<Contact> contacts;
   RegistrationState state;
};

struct ChangeItem
{
   Registration registration;
   RegEvent registrationEvent;
};

struct RegistrationEventInfo
{
   cpc::vector<ChangeItem> changes;
};
}
}
#endif
