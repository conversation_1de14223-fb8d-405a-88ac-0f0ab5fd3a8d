#pragma once

#if !defined(CPCAPI2_SIP_REG_EVENT_HANDLER_H)
#define CPCAPI2_SIP_REG_EVENT_HANDLER_H

#include "cpcapi2defs.h"
#include "event/SipEventSubscriptionHandler.h"
#include "SipRegEventManager.h"
#include "SipRegEventModel.h"

namespace CPCAPI2
{
namespace SipRegEvent
{

/**
 * Event passed in SipRegEventHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission.
 */
struct NewRegEventSubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;
};

/**
 * Event passed in SipRegEventHandler::onSubscriptionEnded().
 * The client subscription session is terminated (for any reason).
 */
struct RegEventSubscriptionEndedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionEndReason      endReason;
};


/**
 * Event passed in SipRegEventHandler::onRegStateChanged().
 * An incoming NOTIFY was received.
 */
struct RegEventUpdatedEvent
{
   RegistrationEventInfo registrationEventInfo;
};

/**
 * Event passed in SipRegEventHandler::onSubscriptionStateChanged().
 * The state of the outgoing subscription has changed.
 */
struct RegEventSubscriptionStateChangedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionState    subscriptionState;
};

/**
* Event passed in SipRegEventHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to MWI functionality; 
* set in SipMessageWaitingIndication::setHandler().
*/
class SipRegEventHandler
{
public:
   virtual int onNewSubscription(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const NewRegEventSubscriptionEvent& args) = 0;
   virtual int onSubscriptionEnded(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const RegEventSubscriptionEndedEvent& args) = 0;
   virtual int onRegStateUpdated(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const RegEventUpdatedEvent& args) = 0;
   virtual int onSubscriptionStateChanged(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const RegEventSubscriptionStateChangedEvent& args) = 0;
   virtual int onError(CPCAPI2::SipRegEvent::SipRegEventSubscriptionHandle subscription, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_HANDLER_H
