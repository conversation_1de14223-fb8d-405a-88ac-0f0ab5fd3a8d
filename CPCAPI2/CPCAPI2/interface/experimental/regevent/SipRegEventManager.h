#pragma once

#if !defined(CPCAPI2_SIP_REG_EVENT_MANAGER_H)
#define CPCAPI2_SIP_REG_EVENT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace SipRegEvent
{

class SipRegEventHandler;

typedef unsigned int SipRegEventSubscriptionHandle;

/**
* A struct. 
*/ 
struct SipRegEventSubscriptionSettings
{
   unsigned int                     expiresSeconds;

   SipRegEventSubscriptionSettings()
   {
      expiresSeconds = 3600;
   }
};

/**
* Manager interface to subscribe for "reg" registration event 
* information that is stored on a registrar server; 
* the interface conforms with RFC 3680; get a reference to the 
* interface using the static method getInterface(). 
*/

class CPCAPI2_SHAREDLIBRARY_API SipRegEventManager
{
public:

      /**
   * Get a reference to the %SipRegEventManager interface.
   */   
   static SipRegEventManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for subscription events on the specified account. Set the handler
   * immediately after creating the account.
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipRegEventHandler* handler) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipRegEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Invoked prior to calling start(..) to customize the default subscription parameters @see SipRegEventSubscriptionSettings.
   */
   virtual int applySubscriptionSettings(SipRegEventSubscriptionHandle subscription, const SipRegEventSubscriptionSettings& settings) = 0;


    /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   * This function is optional - by default the participant will be the account URI
   *
   * @param subscription the outgoing subscription the participant is going to be added to.
   * @param targetAddress the address of the participant. The format is sip:<EMAIL>
   *
   * @return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int addParticipant(SipRegEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE.
   */
   virtual int start(SipRegEventSubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
   */
   virtual int end(SipRegEventSubscriptionHandle subscription) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipRegEventManager.
    */
   virtual ~SipRegEventManager() {}
};


}
}


#endif
