#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_XMPP_HELPER_H)
#define CPCAPI2_REMOTE_SYNC_XMPP_HELPER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "xmpp/XmppChatManager.h"

namespace CPCAPI2
{
class Phone;
namespace RemoteSyncXmppHelper
{
struct RemoteSyncXmppHelperConfig
{
   cpc::string username;
   cpc::string domain;
};

class CPCAPI2_SHAREDLIBRARY_API RemoteSyncXmppHelper
{
public:
   /**
   * Get a reference to the %ConferenceConnectorManager interface.
   */   
   static RemoteSyncXmppHelper* getInterface(Phone* cpcPhone);

   // The following (synchronous) methods are related to the use of XMPP with the RemoteSync module.
   virtual cpc::string getRemoteSyncFromID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message) = 0;
   virtual cpc::string getRemoteSyncToID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message) = 0;
   virtual cpc::string getRemoteSyncConversationID(CPCAPI2::XmppChat::XmppChatHandle chat) = 0;
   DEPRECATED virtual cpc::string getRemoteSyncUniqueID(CPCAPI2::XmppChat::XmppChatHandle chat, const cpc::string& stanzaID) = 0;
   virtual cpc::string getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID = "") = 0;
   virtual cpc::string getRemoteSyncAccountID(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;
   virtual CPCAPI2::XmppAccount::XmppAccountHandle getAccountHandleFromRemoteSyncID(const cpc::string& accountID) = 0;

   /**
   * Returns a text/xml document with both of the messageContent (plaintext)
   * and htmlText (HTML) encoded into the string. Note that it is required to
   * have *at least* a plaintext string. The Content-Type of the resulting
   * RemoteSync string will be text/xml.
   */
   virtual cpc::string getRemoteSyncEncodedContent(const cpc::string& plainText, const cpc::string& htmlText = "") = 0;

   /**
   * Method parses the RemoteSync text/xml body and returns in two outparams,
   * the plaintext and HTML fragment embedded inside of it.
   */
   virtual int getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML) = 0;


};

}

}

#endif // CPCAPI2_REMOTE_SYNC_XMPP_HELPER_H
