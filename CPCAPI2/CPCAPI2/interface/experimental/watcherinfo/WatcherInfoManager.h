#pragma once

#if !defined(CPCAPI2_WATCHER_INFO_MANAGER_H)
#define CPCAPI2_WATCHER_INFO_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "WatcherInfoModel.h"
#include "event/SipEventManager.h"

namespace CPCAPI2
{
class Phone;

namespace WatcherInfo
{
class WatcherInfoSubscriptionHandler;

/**
* Subscription settings.
*/
struct WatcherInfoEventSubscriptionSettings
{
   unsigned int expiresSeconds;     // Expires value to use in outgoing SUBSCRIBEs
   cpc::string package;             // package to subscribe to
   
   //GENBAND specific flags
   bool eow;
   
   WatcherInfoEventSubscriptionSettings()
   {
      expiresSeconds = 3600;
      package = "presence.winfo";
   }
};

/***/
struct WatcherInfoEventState
{
   cpc::string                         mimeType;
   cpc::string                         mimeSubType;
   cpc::string                         eventPackage;
   unsigned int                        expiresTimeMs;
   WatcherInformation                  winfo;

   WatcherInfoEventState()
   {
      mimeType = "";
      mimeSubType = "";
      eventPackage = "";
      expiresTimeMs = 0;
   }
};

/**
* Manager interface to create and manage dialog event subsciptions (RFC 4235). The interface
* provides client functionality (i.e. sending SUBSCRIBEs and handling NOTIFYs) as well 
* as server functionality (i.e. handling SUBSCRIBES and sending NOTIFYs); get a reference 
* to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API WatcherInfoManager
{
public:
  /**
   * Get a reference to the %WatcherInfoManager interface.
   */   
   static WatcherInfoManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for subscription events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * @param account the user account to be associated with the handler.
   * @param handler the handler to register
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandler* handler) = 0;

   /**
    * Removes an existing handler. Must be called on the same thread as SipAccountManager::process(..)
    */
   virtual int removeHandler(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   *
   * @param account the user account of the creator of the new subscription.
   */
   virtual WatcherInfoSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Must be invoked prior to calling start(..) so that the event package and other subscription parameters are configured.
   *
   * @param subscription the outgoing subscription the settings are going to be applied to.
   * @param settings the settings.
   */                                                               //SipEventSubscriptionSettings
   virtual int applySubscriptionSettings(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventSubscriptionSettings& settings) = 0;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   *
   * @param subscription the outgoing subscription the participant is going to be added to.
   * @param targetAddress the address of the participant. The format is sip:<EMAIL>
   */
   virtual int addParticipant(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Sets the address of the event server responsible for sending event notifications.  For example, this may be
   * the address of a resource list server.  If an event server is set on a subscription, the outgoing SUBSCRIBE
   * is sent to the targetAddress for the event server specified in the targetAddress parameter..
   * Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int setEventServer(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   *
   * @param subscription the outgoing subscription to start.
   */
   virtual int start(WatcherInfoSubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session. Sends an outgoing SUBSCRIBE with Expires == 0.
   *
   * @param subscription the subscription to terminate.
   */
   virtual int end(WatcherInfoSubscriptionHandle subscription) = 0;

   /**
   * Used to refresh an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant or
   * to the event/resource-list server.
   * @param subscription The outgoing subscription to refresh.
   */
   virtual int refresh(WatcherInfoSubscriptionHandle subscription) = 0;

   /**
   * Used to send outgoing event state on an incoming (server) subscription session.
   * Sends a NOTIFY request with content as specified by the eventState parameter.
   */
   virtual int notify(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState) = 0;

   /**
   * Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
   * @param subscription The incoming subscription session to reject.
   * @param rejectReason The SIP response code sent to the originating party.
   */
   virtual int reject(WatcherInfoSubscriptionHandle subscription, unsigned int rejectReason) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK).
   */
   virtual int accept(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState) = 0;

   /**
   * Function for setting pres-rules document that defines xcap server presence behavior. It is used in combination 
   * with @addWatcher and @removeWatcher. First @addWatcher or @removeWatcher is called to make changes to user 
   * presence list and then @setPresenceAuthenticationRules is called to save changes to XCAP server.
   * Needs to be called immediately after creation of watcher info module.
   * @param account The account handle for the user that will use this presence authentication rules.
   */
   virtual int setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Function for setting pres-rules document that defines xcap server presence behavior. Needs to be called immediately after creation of watcher info module.
   * @param account The account handle for the user that will use this presence authentication rules.
   * @param path File that contains presence authentication rules.
   */
   virtual int setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& presRulesXml) = 0;

   /**
   * Function that adds subscriber (watcher) defined with @param uri to list of allowed watchers for account represented with handle @param account
   * @param account The account handle for the user that received subscription from @param uri
   * @param uri URI of a subscriber
   */
   virtual int addWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri) = 0;

   /**
   * Function that removes subscriber (watcher), defined with @param uri, from list of allowed watchers for account represented with handle @param account
   * @param account The account handle for the user that received subscription end from @param uri
   * @param uri URI of a subscriber
   */
   virtual int removeWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %WatcherInfoManager.
    */
   virtual ~WatcherInfoManager() {}
};

} // WatcherInfo namespace
} // CPCAPI2 namespace

#endif // CPCAPI2_WATCHER_INFO_MANAGER_H
