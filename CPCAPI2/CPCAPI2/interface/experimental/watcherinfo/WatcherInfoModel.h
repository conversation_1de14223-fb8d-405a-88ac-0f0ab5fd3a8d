#pragma once

#if !defined(__CPCAPI2_WATCHER_INFO_MODEL_H__)
#define __CPCAPI2_WATCHER_INFO_MODEL_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "WatcherInfoTypes.h"

namespace CPCAPI2
{
namespace WatcherInfo
{

struct Watcher
{
   cpc::string id;
   cpc::string status;
   cpc::string event;
   cpc::string displayName;
   cpc::string watcherURI;
   unsigned int expiration;
   unsigned int durationSubscribed;

   Watcher()
   {
      id = "";
      status = "";
      event = "";
      displayName = "";
      watcherURI = "";
      expiration = 3600;
      durationSubscribed = 0;
   }
};

struct WatcherList
{
   cpc::string resource;
   cpc::string package;
   cpc::vector<Watcher> watchers;

   WatcherList()
   {
      resource = "";
      package = "";
   }
};

struct WatcherInformation
{
   unsigned int version;
   cpc::string state; // full or partial
   cpc::vector<WatcherList> watcherLists;
   
   //GENBAND specific flags
   bool eow;

   WatcherInformation()
   {
      version = 0;
      state = WINFO_STATE_FULL;
      eow = false;
   }
};

}
}

#endif // __CPCAPI2_WATCHER_INFO_MODEL_H__
