#pragma once

#if !defined(CPCAPI2_WATCHER_INFO_SUBSCRIPTION_HANDLER_H)
#define CPCAPI2_WATCHER_INFO_SUBSCRIPTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "WatcherInfoManager.h"
#include "WatcherInfoModel.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace WatcherInfo
{

/**
 * Event passed in WatcherInfoSubscriptionHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
 * an outgoing SUBSCRIBE has forked, OR
 * an incoming SUBSCRIBE has been received.
 */
struct NewWatcherInfoSubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle  account;          // The user account
   CPCAPI2::SipEvent::SubscriptionType    subscriptionType; // Outgoing or incoming subscription
   cpc::string                            remoteAddress;    // The address of the  remote endpoint
};

/**
 * Event passed in WatcherInfoSubscriptionHandler::onSubscriptionEnded().
 * The client subscription session is terminated (for any reason).
 */
struct WatcherInfoSubscriptionEndedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionEndReason endReason; // The reason for the subsciption termination
};

enum WatcherInfoSubscriptionState
{
   WatcherInfoSubscriptionState_NotStarted  = 1400,
   WatcherInfoSubscriptionState_Pending     = 1410,
   WatcherInfoSubscriptionState_Active      = 1420,
   WatcherInfoSubscriptionState_Terminated  = 1430,
   WatcherInfoSubscriptionState_Waiting     = 1440
};

/**
 * Event passed in WatcherInfoSubscriptionHandler::onSubscriptionStateChanged().
 * The state of the outgoing subscription has changed.
 */
struct WatcherInfoSubscriptionStateChangedEvent
{
   WatcherInfoSubscriptionState subscriptionState; // The new state of the subscription
};

/**
 * Event passed in WatcherInfoSubscriptionHandler::onIncomingWatcherInfoEvent().
 * An incoming NOTIFY was received.
 */
struct IncomingWatcherInfoEvent
{
	 WatcherInfoEventState     eventState;
};

/**
 * Event passed in WatcherInfoSubscriptionHandler::onNotifyWatcherInfoSuccess().
 * SIP response received upon sending a NOTIFY.
 */
struct NotifyWatcherInfoSuccessEvent
{
   int sipResponseCode;
};

/**
 * Event passed in WatcherInfoSubscriptionHandler::onNotifyWatcherInfoFailure().
 * SIP error response received upon sending a NOTIFY.
 */
struct NotifyWatcherInfoFailureEvent
{
   int sipResponseCode;
};

/**
* Event passed in WatcherInfoSubscriptionHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to presence subscriptions; 
* set in WatcherInfoManager::setHandler().
*/
class WatcherInfoSubscriptionHandler
{
public:
   virtual int onNewSubscription(WatcherInfoSubscriptionHandle subscription, const NewWatcherInfoSubscriptionEvent& args) = 0;
   virtual int onSubscriptionEnded(WatcherInfoSubscriptionHandle subscription, const WatcherInfoSubscriptionEndedEvent& args) = 0;
   virtual int onIncomingWatcherInfo(WatcherInfoSubscriptionHandle subscription, const IncomingWatcherInfoEvent& args) = 0;
   virtual int onSubscriptionStateChanged(WatcherInfoSubscriptionHandle subscription, const WatcherInfoSubscriptionStateChangedEvent& args) = 0;

   virtual int onNotifyWatcherInfoSuccess(WatcherInfoSubscriptionHandle subscription, const NotifyWatcherInfoSuccessEvent& args) = 0;
   virtual int onNotifyWatcherInfoFailure(WatcherInfoSubscriptionHandle subscription, const NotifyWatcherInfoFailureEvent& args) = 0;

   virtual int onError(WatcherInfoSubscriptionHandle subscription, const ErrorEvent& args) = 0;
};

}
}

#endif // CPCAPI2_WATCHER_INFO_SUBSCRIPTION_HANDLER_H
