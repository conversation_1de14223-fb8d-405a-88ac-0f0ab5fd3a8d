#pragma once

#if !defined(__CPCAPI2_WATCHER_INFO_TYPES_H__)
#define __CPCAPI2_WATCHER_INFO_TYPES_H__

#define WINFO_AUID "watchers" 
#define WINFO_MIME_TYPE "application"
#define WINFO_MIME_SUBTYPE "watcherinfo+xml"
#define WINFO_PACKAGE "presence.winfo"

#define WINFO_NS "urn:ietf:params:xml:ns:watcherinfo"
#define WATCHER_INFO_ELEM "watcherinfo"
#define WATCHER_LIST_ELEM "watcher-list"
#define WATCHER_ELEM "watcher"

#define WINFO_VERSION_ATTRIB "version"
#define WINFO_STATE_ATTRIB "state"
// GENBAND specific watcherinfo attribute
#define WINFO_EOW_ATTRIB "eow"

#define WINFO_STATE_FULL "full"
#define WINFO_STATE_PARTIAL "partial"

#define WINFO_RESOURCE_ATTRIB "resource"
#define WINFO_PACKAGE_ATTRIB "package"

#define WINFO_STATUS_ATTRIB "status"
#define WINFO_ID_ATTRIB "id"
#define WINFO_EVENT_ATTRIB "event"
#define WINFO_DISPLAY_NAME_ATTRIB "display-name"
#define WINFO_EXPIRATION_ATTRIB "expiration"
#define WINFO_DURATION_SUB_ATTRIB "duration-subscribed"

#define PRES_RULES_AUID "pres-rules";
#define PRES_RULES_MIME "application/auth-policy+xml";
#define PRES_RULES_TRANSFORMATIONS "<transformations><pr:provide-services><pr:all-services/></pr:provide-services><pr:provide-persons><pr:all-persons/></pr:provide-persons><pr:provide-devices><pr:all-devices/></pr:provide-devices><pr:provide-all-attributes/></transformations>"
#define PRES_RULES_ACTIONS "<actions><pr:sub-handling>allow</pr:sub-handling></actions>"

#include "xcap/XcapSettings.h"

namespace CPCAPI2
{
namespace WatcherInfo
{
// Type definitions
typedef unsigned int WatcherInfoSubscriptionHandle;
}
}

#endif // __CPCAPI2_WATCHER_INFO_TYPES_H__