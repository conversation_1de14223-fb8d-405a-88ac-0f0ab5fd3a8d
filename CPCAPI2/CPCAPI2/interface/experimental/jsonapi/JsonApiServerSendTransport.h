#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_H)
#define CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiTransport.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace JsonApi
{
/**
*/

class CPCAPI2_SHAREDLIBRARY_API JsonApiServerSendTransport : public JsonApiTransport
{
public:
   /**
   * Get a reference to the %JsonApiServerSendTransport interface.
   */
   static JsonApiServerSendTransport* getInterface(Phone* cpcPhone);

   virtual int setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonApiServer) = 0;
   virtual int send(const CPCAPI2::Json::JsonDataPointer& eventData, bool suppressLog=false) = 0;
};

}
}

#endif // CPCAPI2_JSON_API_SERVER_SEND_TRANSPORT_H
