#pragma once

#if !defined(CPCAPI2_HTTP_SERVER_INTERNAL_H)
#define CPCAPI2_HTTP_SERVER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
   namespace JsonApi
   {
      struct AuthTokenInfo
      {
         bool isValid = false;
         cpc::string userIdentity;
         cpc::string deviceId;
         cpc::vector<cpc::string> requestedResources;
      };

      struct QueryStringParam
      {
         cpc::string name;
         cpc::string value;
      };

      struct CPCAPI2_SHAREDLIBRARY_API HttpServerRequestHandler
      {
         virtual int processRequest(const cpc::string& path, const cpc::string& body, const AuthTokenInfo& authTokenInfo, const cpc::vector<QueryStringParam>& queryString) {
            return 0;
         }
         virtual cpc::string output() = 0;
         virtual int response_code() {
            return 200;
         }
         virtual bool requiresAuth() {
            return true;
         }
      };

      /**
      */

      class CPCAPI2_SHAREDLIBRARY_API HttpServerInternal
      {
      public:
         /**
         * path: need / for root, e.g. "/statusApi/remotesync"
         */
         virtual int addRequestHandler(const cpc::string& path, HttpServerRequestHandler* handler) = 0;
      };

   }
}

#endif // CPCAPI2_HTTP_SERVER_INTERNAL_H
