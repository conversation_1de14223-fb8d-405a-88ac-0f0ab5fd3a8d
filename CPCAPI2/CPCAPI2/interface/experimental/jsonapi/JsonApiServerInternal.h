#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_INTERNAL_H)
#define CPCAPI2_JSON_API_SERVER_INTERNAL_H

#include "cpcapi2defs.h"
#include "phone/PhoneHandler.h"
#include "JsonApiServer.h"

namespace CPCAPI2
{

namespace JsonApi
{

class CPCAPI2_SHAREDLIBRARY_API JsonApiServerInternal : public JsonApiServer
{

public:

   static JsonApiServerInternal* getInternalInterface(CPCAPI2::Phone* cpcPhone);
   virtual void addEventObserver(JsonApiServerHandler* handler) = 0;
};

}

}

#endif // CPCAPI2_JSON_API_SERVER_INTERNAL_H

