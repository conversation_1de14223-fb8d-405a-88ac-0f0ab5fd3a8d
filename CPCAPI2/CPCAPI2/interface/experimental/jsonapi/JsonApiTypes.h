#pragma once

#if !defined(CPCAPI2_JSONAPI_TYPES_H)
#define CPCAPI2_JSONAPI_TYPES_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
namespace JsonApi
{
typedef unsigned int JsonApiUserHandle;

struct AuthToken
{
   cpc::string authToken;
   cpc::string cp_user;
   cpc::string device_uuid;
   cpc::string remoteAddress; // for debug only
   cpc::vector<cpc::string> requestedResources;

   AuthToken() : authToken(""), cp_user(""), device_uuid(""), remoteAddress("") {}
};
} // namespace JsonA<PERSON>
} // namespace CPCAPI2

#endif // CPCAPI2_JSONAPI_TYPES_H
