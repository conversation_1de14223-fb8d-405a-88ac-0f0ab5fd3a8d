#pragma once

#if !defined(CPCAPI2_JSON_API_CLIENT_HANDLER_H)
#define CPCAPI2_JSON_API_CLIENT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{

namespace JsonApi
{

typedef int JsonApiConnectionHandle;
typedef int JsonApiLoginHandle;

struct ConnectResultEvent
{
};

struct LoginResultEvent
{
   bool success;
   unsigned int requestId;

   LoginResultEvent() : success(false), requestId(0) {}
};

struct LogoutResultEvent
{
   bool success;
   unsigned int requestId;

   LogoutResultEvent() : success(false), requestId(0) {}
};

struct StatusChangedEvent
{
   enum JsonApiClientStatus
   {
      Status_Connected = 1,
      Status_Connecting = 2,
      Status_Failed = 3, // module will auto retry at some point in the future
      Status_Disconnected = 4
   };

   JsonApiClientStatus status;

   static cpc::string get_debug_string(const JsonApiClientStatus& status);

};

/**
*/
class JsonApiClientHandler
{

public:

   DEPRECATED virtual int onConnectResult(JsonApiConnectionHandle h, const ConnectResultEvent& args) { return 0; }
   virtual int onLoginResult(JsonApiLoginHandle h, const LoginResultEvent& args) = 0;
   virtual int onLogoutResult(JsonApiLoginHandle h, const LogoutResultEvent& args) = 0;
   virtual int onStatusChanged(JsonApiLoginHandle h, const StatusChangedEvent& args) = 0;

   virtual ~JsonApiClientHandler() {}

};

}

}

#endif // CPCAPI2_JSON_API_CLIENT_HANDLER_H
