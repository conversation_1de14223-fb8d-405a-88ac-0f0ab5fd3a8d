#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_H)
#define CPCAPI2_JSON_API_SERVER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{

class Phone;

namespace JsonApi
{
   class JsonApiServerHandler;
   class OutgoingJsonHandler;
   struct LoginResultEvent;
   struct LogoutResultEvent;
   struct UpdateUserEvent;

   struct JsonApiServerConfig
   {
      JsonApiServerConfig() :
         websocketPort(9003),
         httpPort(18080),
         outgoingJsonHandler(NULL),
         jsonApiVersion(0),
         numThreads( -1 )
      {
      }

      JsonApiServerConfig(int websocketPort_, int httpPort_, cpc::string& certificateFilePath_) :
         websocketPort(websocketPort_),
         httpPort(httpPort_),
         outgoingJ<PERSON><PERSON><PERSON><PERSON>(NULL),
         jsonApiVersion(0),
         certificateFilePath(certificateFilePath_),
         numThreads( -1 )
      {
      }

      int websocketPort;
      int httpPort;
      OutgoingJsonHandler* outgoingJsonHandler;
      int jsonApiVersion;
      cpc::string certificateFilePath;
      cpc::string wssCertificateFilePath;
      cpc::string wssPrivateKeyFilePath;
      cpc::string wssDiffieHellmanParamsFilePath;
      cpc::string httpsCertificateFilePath;
      cpc::string httpsPrivateKeyFilePath;
      cpc::string httpsDiffieHellmanParamsFilePath;

      /**
       * If set to -1, numThreads will be initialized to
       * the number of cores on the machine
       */
      int numThreads;

      bool apiLoggingEnabled = false;
   };

   struct LogoutProceedEvent
   {
      JsonApiServerHandler* observer;
   };

   /**
   */
   class CPCAPI2_SHAREDLIBRARY_API JsonApiServer
   {
   public:
      /**
      * Get a reference to the %JsonApiServer interface.
      */
      static JsonApiServer* getInterface(Phone* cpcPhone);

      static const int kBlockingModeNonBlocking = -1;
      static const int kBlockingModeInfinite = 0;
      static const int kRemoteControlModuleDisabled = -1;

      /**
      * Allows the application code to receive callback notifications from the SDK.
      *
      * These callbacks will happen synchronously, in the same thread of execution as that in which
      * %process() is invoked.  Depending on the application threading model,
      * process() can be used in two different ways:
      * <ol>
      * <li>blocking mode ?Typically in this mode, %process() is called by the
      * application from a background (worker) thread.  The call to process()
      * blocks until a callback function needs to be invoked.
      * <li>non-blocking mode ?In this mode, %process() is called by the application
      * from the main (GUI) thread of the application, typically from the
      * main message/event loop.  In this mode, %process() returns immediately
      * and so must be called frequently enough that the application can receive
      * its callback notifications in a timely manner.
      *
      * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
      *                representing the time the call to process(..) will block waiting for a callback
      *                from the SDK
      *</ol>
      */
      virtual int process(unsigned int timeout) = 0;

      /**
      * Allows the application to "unblock" the
      * thread calling JsonApiServer::process(..) if it is blocked waiting for an SDK callback.
      * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
      */
      virtual void interruptProcess() = 0;

      virtual int start(const JsonApiServerConfig& serverConfig = JsonApiServerConfig()) = 0;
      virtual int shutdown() = 0;

      virtual int setHandler(JsonApiServerHandler* handler) = 0;
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void setOnPrivilegedAccessCompleted(void(*onPrivilegedAccessCompleted)(void*), void* context) {}

      virtual int setJsonApiUserContext(JsonApiUserHandle jsonApiUser, CPCAPI2::Phone* sdkContext, const cpc::vector<cpc::string>& sdkModules) = 0;

      virtual int sendLoginResult(JsonApiUserHandle jsonApiUser, LoginResultEvent loginResult) = 0;
      virtual int sendLogoutResult(JsonApiUserHandle jsonApiUser, LogoutResultEvent logoutResult) = 0;
      virtual int proceedWithLogout(JsonApiUserHandle jsonApiUser, LogoutProceedEvent proceedEvent) = 0;
      virtual int destroyUser(JsonApiUserHandle jsonApiUser) = 0;

      virtual int processIncomingJson(const cpc::string& json) = 0;
   
   };

}

}

#endif // CPCAPI2_JSON_API_SERVER_H
