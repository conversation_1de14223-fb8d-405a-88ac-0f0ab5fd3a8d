#pragma once

#if !defined(CPCAPI2_JSON_API_TRANSPORT_H)
#define CPCAPI2_JSON_API_TRANSPORT_H

#include "json/JsonData.h"

namespace CPCAPI2
{
namespace JsonApi
{

class JsonApiTransport
{
public:
   virtual int send(const CPCAPI2::Json::JsonDataPointer& eventData, bool suppressLog=false) = 0;
}; // class JsonApiTransport

} // namespace JsonApi
} // namespace CPCAPI2

#endif // CPCAPI2_JSON_API_TRANSPORT_H
