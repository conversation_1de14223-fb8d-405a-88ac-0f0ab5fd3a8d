#pragma once

#if !defined(CPCAPI2_JSON_API_SERVER_HANDLER_H)
#define CPCAPI2_JSON_API_SERVER_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{

namespace JsonApi
{

struct NewLoginEvent
{
   cpc::string authToken;
   cpc::string userIdentity;
   cpc::string userIdentityAnonymized;
   cpc::string resource;
   cpc::vector<cpc::string> requestedResources;
   unsigned int requestId = 0;
   bool isHttp = false;
   bool isOrchestration = false;
   cpc::string realm;
};

struct LogoutEvent
{
   cpc::string authToken;
   cpc::string userIdentity;
   // cpc::string resource;
};

struct ConnectionClosedEvent
{
   cpc::string authToken;
   cpc::string userIdentity;
};

/**
*/
class JsonApiServerHandler
{
public:
   virtual int onNewLogin(JsonApiUserHandle jsonApiUser, const NewLoginEvent& args) = 0;
   virtual int onLogout(JsonApiUserHandle jsonApiUser, const LogoutEvent& args) = 0;
   virtual int onConnectionClosed(JsonApiUserHandle jsonApiUser, const ConnectionClosedEvent& args) {
      return -1;
   }

   virtual ~JsonApiServerHandler() {}

};

class OutgoingJsonHandler
{
public:
   virtual int onJson(const cpc::string& json) = 0;
   virtual ~OutgoingJsonHandler() {}
};

}

}

#endif // CPCAPI2_JSON_API_SERVER_HANDLER_H
