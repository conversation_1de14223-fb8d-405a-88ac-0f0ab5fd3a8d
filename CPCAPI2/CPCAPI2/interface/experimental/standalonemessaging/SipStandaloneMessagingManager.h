#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipStandaloneMessagingTypes.h"
#include "cpm/CpmTypes.h"

#include <ctime>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace SipStandaloneMessaging
{
// Forward declarations
class SipStandaloneMessagingHandler;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Interface for all standalone messaging related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API SipStandaloneMessagingManager
{
public:
   /**
    * Get a reference to the %SipStandaloneManager interface.
    *
    * @param cpcPhone the phone object to be associated with this manager.
    */
   static SipStandaloneMessagingManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for standalone messaging related events on the specified account.
    * Set the handler immediately after getting a reference to this manager.
    *
    * @param account the user account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipStandaloneMessagingHandler* handler) = 0;

   /**
    * Sends a standalone message to the specified URI, using the specified account.
    *
    * @param account The %Account to use for delivery.
    * @param targetAddress the SIP address of the destination. The format of the
    *                      targetAddress parameter is sip:<EMAIL>.
    * @param messageContent the content of message to send.
    * @param mimeType the MIME type of the message to send, defauts to text/plain if not specified.
    * @param datetime the date/time at which the message was sent, defaults to the current date/time if not specified.
    * @param dispositionNotifications the types of notifications requested, none if not specified.
    * @param forceLargeMode force the message to be sent in large mode, defaults to false if not specified.
    *
    * @return the handle of the standalone message being sent.
    */
   virtual SipStandaloneMessageHandle sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const cpc::string& messageContent,
         CPM::MimeType mimeType = MimeType_TextPlain,
         struct std::tm* datetime = 0,
         const cpc::vector<DispositionNotificationType>& dispositionNotifications
            = cpc::vector<DispositionNotificationType>(),
         bool forceLargeMode = false) = 0;

   /**
    * Notifies the other participant that the message has been successfully
    * delivered.
    *
    * @param account The %Account to use for delivery.
    * @param message the delivered message.
    * @param messageDeliveryStatus the status of the message delivery.
    * @param targetAddress the SIP address of the destination. The format of the
    *                      targetAddress parameter is sip:<EMAIL>.
    * @param datetimeString the datetime value in the delivered message.
    *
    * @return the handle of the notification being sent.
    */
   virtual SipStandaloneMessageHandle notifyMessageDelivered(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipStandaloneMessageHandle message,
         MessageDeliveryStatus messageDeliveryStatus,
         const cpc::string& targetAddress,
         const cpc::string& datetimeString) = 0;

   /**
    * Notifies the other participant that the message has been successfully
    * displayed.
    *
    * @param account The %Account to use for delivery.
    * @param message the displayed message.
    * @param messageDisplayStatus the status of the message display
    * @param targetAddress the SIP address of the destination. The format of the
    *                      targetAddress parameter is sip:<EMAIL>.
    * @param datetimeString the datetime value in the delivered message.
    *
    * @return the handle of the notification being sent.
    */
   virtual SipStandaloneMessageHandle notifyMessageDisplayed(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipStandaloneMessageHandle message,
      MessageDisplayStatus messageDisplayStatus,
      const cpc::string& targetAddress,
      const cpc::string& datetimeString) = 0;
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_H__
