#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_HANDLER_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipStandaloneMessagingTypes.h"
#include <cpm/CpmTypes.h>

#include <ctime>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace SipStandaloneMessaging
{
/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK to signal a new incoming message.
 */
struct NewMessageEvent
{
   SipStandaloneMessageHandle message; // The message
   cpc::string from;            // The originator of the message
   cpc::string to;              // The destination of the message
   struct std::tm datetime;           // date/time the message was sent, in local time
   cpc::string datetimeString;  // The datetime the message was sent, the original string
   CPM::MimeType mimeType;       // MIME type of the message
   cpc::string messageContent;   // The content of the message, represented as a byte array
   cpc::vector<DispositionNotificationType> dispositionNotifications; // Types of disposition notifications requested
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a new message was successfully
 * sent.
 */
struct SendMessageSuccessEvent
{
   SipStandaloneMessageHandle message; // The new message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a new message failed to send.
 */
struct SendMessageFailureEvent
{
   SipStandaloneMessageHandle message; // The new message
   int errorCode;                      // The error code
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message was successfully
 * delivered to the other participant's device(s).
 */
struct MessageDeliveredEvent
{
   SipStandaloneMessageHandle notification;     // The notification's message ID
   SipStandaloneMessageHandle message;          // The delivered message
   MessageDeliveryStatus messageDeliveryStatus; // The status of the message delivery
   struct std::tm datetime;                          // date/time the message was sent, in local time
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message was successfully
 * displayed on the other participant's device(s).
 */
struct MessageDisplayedEvent
{
   SipStandaloneMessageHandle notification;   // The notification's message ID
   SipStandaloneMessageHandle message;        // The displayed message
   MessageDisplayStatus messageDisplayStatus; // The status of the message display
   struct std::tm datetime;                        // date/time the message was sent, in local time
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message delivered notification
 * was successfully sent.
 */
struct NotifyMessageDeliveredSuccessEvent
{
   SipStandaloneMessageHandle notification;  // The notification's message ID
   SipStandaloneMessageHandle message;       // The delivered message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message delivered notification
 * failed to be sent.
 */
struct NotifyMessageDeliveredFailureEvent
{
   SipStandaloneMessageHandle notification;  // The notification's message ID
   SipStandaloneMessageHandle message; // The delivered message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message displayed notification
 * was successfully sent.
 */
struct NotifyMessageDisplayedSuccessEvent
{
   SipStandaloneMessageHandle notification;  // The notification's message ID
   SipStandaloneMessageHandle message; // The displayed message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Event fired by the SDK when a message displayed notification
 * failed to be sent.
 */
struct NotifyMessageDisplayedFailureEvent
{
   SipStandaloneMessageHandle notification;  // The notification's message ID
   SipStandaloneMessageHandle message; // The displayed message
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 * Used to report general SDK error conditions, such as invalid handles, or cases
 * where the transfer is not in a valid state for the requested operation.
 */
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 *
 * Handles all SDK callbacks related to the standalone messaging feature.
 */
class SipStandaloneMessagingHandler
{
public:
   /**
    * Callback invoked by the SDK when a new message is received.
    *
    * @param account the account associated with the message.
    * @param args information about the new message.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewMessage(CPCAPI2::SipAccount::SipAccountHandle account, const NewMessageEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message was successful.
    *
    * @param account the account associated with the message.
    * @param args information about the success.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageSuccess(CPCAPI2::SipAccount::SipAccountHandle account, const SendMessageSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message failed.
    *
    * @param account the account associated with the message.
    * @param args information about the failure.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SendMessageFailureEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was successfully delivered
    * to the other participant's device(s).
    *
    * @param account the account associated with the message.
    * @param args information about the message delivered.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDelivered(CPCAPI2::SipAccount::SipAccountHandle account, const MessageDeliveredEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was successfully displayed
    * on the other pariticpant's device(s).
    *
    * @param account the account associated with the message.
    * @param args information about the message displayed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDisplayed(CPCAPI2::SipAccount::SipAccountHandle account, const MessageDisplayedEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message delivered notification was successfuly sent.
    *
    * @param account the account associated with the message.
    * @param args information about the message delivered notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDeliveredSuccess(CPCAPI2::SipAccount::SipAccountHandle account, const NotifyMessageDeliveredSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message delivered notification failed to be sent.
    *
    * @param account the account associated with the message.
    * @param args information about the message delivered notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDeliveredFailure(CPCAPI2::SipAccount::SipAccountHandle account, const NotifyMessageDeliveredFailureEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message displayed notification was successfuly sent.
    *
    * @param account the account associated with the message.
    * @param args information about the message displayed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDisplayedSuccess(CPCAPI2::SipAccount::SipAccountHandle account, const NotifyMessageDisplayedSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a message displayed notification failed to be sent.
    *
    * @param account the account associated with the message.
    * @param args information about the message displayed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNotifyMessageDisplayedFailure(CPCAPI2::SipAccount::SipAccountHandle account, const NotifyMessageDisplayedFailureEvent& args) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param account the account associated with the message.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& event) = 0;
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_HANDLER_H__
