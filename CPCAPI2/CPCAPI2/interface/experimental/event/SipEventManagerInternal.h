#pragma once

#if !defined(CPCAPI2_SIP_EVENT_MANAGER_INTERNAL_H)
#define CPCAPI2_SIP_EVENT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{

namespace SipEvent
{

class CPCAPI2_SHAREDLIBRARY_API SipEventManagerInternal
{
public:
  
   virtual int accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState, bool suppressNotify) = 0;


protected:

   virtual ~SipEventManagerInternal() {}
};

}
}

#endif // CPCAPI2_SIP_EVENT_MANAGER_H
