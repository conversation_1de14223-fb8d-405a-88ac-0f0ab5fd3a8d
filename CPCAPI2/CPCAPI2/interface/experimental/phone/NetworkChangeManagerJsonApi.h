#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_API_H)
#define CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
/**
*/
class CPCAPI2_SHAREDLIBRARY_API NetworkChangeManagerJsonApi
{
public:
   /**
   * Get a reference to the %NetworkChangeManagerJsonApi interface.
   */
   static NetworkChangeManagerJsonApi* getInterface(Phone* cpcPhone);

};

}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_API_H
