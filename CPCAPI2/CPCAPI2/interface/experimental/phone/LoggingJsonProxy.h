#pragma once

#if !defined(CPCAPI2_LOGGING_JSON_PROXY_H)
#define CPCAPI2_LOGGING_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "interface/public/phone/Phone.h"

namespace CPCAPI2
{
class Phone;

class PhoneLoggerJson
{
public:
   /**
   * Function object which will be invoked if callback diagnostic logging is enabled.
   */
   virtual bool log(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
                           int line, const char *message, const char *messageWithHeaders) = 0;
}; // class PhoneLoggerJson

/**
 */
class CPCAPI2_SHAREDLIBRARY_API LoggingJsonProxy
{
public:
   /**
   * Get a reference to the LoggingJsonProxy interface.
   */
   static LoggingJsonProxy* getInterface(Phone* cpcPhone);

   /**
   * Enables or disables the diagnostic logging to a file, for all SDK modules. Can be called before initialize(..) is called.
   * @param id A meaningful identifier that will become part of the log file name
   * @param enabled True to enable logging
   */
   virtual int setFileLoggingEnabled(const cpc::string& id, bool enabled) = 0;

   /**
    * Enables or disables the the diagnostic logging to a callback, for all SDK modules. Can be called before initialize(..) is called.
    * @param logger An instance of %PhoneLoggerJson, that will consume SDK diagnostic log callbacks.
    */
   virtual int setCallbackLoggingEnabled(PhoneLoggerJson *logger, bool enabled) = 0;

   /**
   * Sets logging level, for all SDK modules. S
   * Should be called after setLoggedEnabled is called. Otherwise the changes won't take effect
   * and will be overriden by default values when setLoggedEnabled is called.
   * @param level Desired level of log output.
   */
   virtual int setLogLevel(CPCAPI2::LogLevel level) = 0;

   virtual int logLibVersions() = 0;

protected:
   /*
    * The SDK will manage memory life of %LoggingJsonProxy.
    */
   virtual ~LoggingJsonProxy() {}
}; //class LoggingJsonProxy

} // namespace CPCAPI2
#endif // CPCAPI2_LOGGING_JSON_PROXY_H
