#pragma once

#if !defined(CPCAPI2_PHONE_HANDLER_INTERNAL_H)
#define CPCAPI2_PHONE_HANDLER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
/**
* Event passed in PhoneHandler::onEventQueueError().
*/
struct EventQueueErrorEvent
{
   enum EventQueueErrorReason
   {
      EventQueueErrorReason_InvalidQueueId      = 1, // Queue-Id in the request was invalid
      EventQueueErrorReason_Unknown             = 2  // Internal queue error
   };

   cpc::string errorText;
   EventQueueErrorReason errorReason;
};

/**
 * Handler for general internal errors in the SDK.
*/
class PhoneHandlerInternal
{
public:
   /**
    * Indicates that an error has occurred while handling the previous queue event.
   */
   virtual int onEventQueueError(const EventQueueErrorEvent& args) = 0;

};

}
#endif // CPCAPI2_PHONE_HANDLER_INTERNAL_H
