#pragma once

#if !defined(CPCAPI2_BLUETOOTH_HANDLER_H)
#define CPCAPI2_BLUETOOTH_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "BluetoothManager.h"

namespace CPCAPI2
{

/**
*/
struct BluetoothEvent
{
   cpc::string deviceAddress;
   cpc::string deviceName;
   bool unexpectedEvent;
};

/**
 */
class BluetoothHandler
{
public:
   /**
    * Notifies the application when bluetooth state has changed
    */
   virtual int onBluetoothHeadsetUnavailable(const BluetoothEvent& args) = 0;
   virtual int onBluetoothHeadsetAvailable(const BluetoothEvent& args) = 0;
   virtual int onBluetoothAudioDisconnected(const BluetoothEvent& args) = 0;
   virtual int onBluetoothAudioConnected(const BluetoothEvent& args) = 0;
};

}
#endif // CPCAPI2_BLUETOOTH_HANDLER_H
