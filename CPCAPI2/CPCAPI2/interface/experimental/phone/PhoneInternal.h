#pragma once

#if !defined(CPCAPI2_PHONE_INTERNAL_H)
#define CPCAPI2_PHONE_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "Permissions.h"
#include "PermissionsHandler.h"
#include "licensing/LicensingClientHandler.h"
#include "log/LocalLoggerHandler.h"
#include "phone/Phone.h"
#include "phone/SslCipherOptions.h"
#include "phone/NetworkChangeManager.h"

namespace CPCAPI2
{
   class SslCipherOptions;
   class LocalLogger;

   namespace Licensing
   {
      class LicensingClientManager;
   }

   struct ConnectionPreferences
   {
      bool useNetworkChangeManager;
      SslCipherOptions ciphers;

      enum ExternalResolverUsage
      {
         ExternalResolverUsage_Disable = 0,
         ExternalResolverUsage_Enable = 1,
         ExternalResolverUsage_Force = 2
      };
      ExternalResolverUsage externalResolver;

      enum NetworkChangeManagerType
      {
         NetworkChangeManagerType_PlatformDefault = 0,
         NetworkChangeManagerType_Mock = 1
      };
      NetworkChangeManagerType networkChangeManagerType;

      ConnectionPreferences()
      {
         useNetworkChangeManager = true;
#ifdef CPCAPI2_AUTO_TEST
         networkChangeManagerType = NetworkChangeManagerType_Mock;
#else
         networkChangeManagerType = NetworkChangeManagerType_PlatformDefault;
#endif
         externalResolver = ExternalResolverUsage_Disable;
      };
   };

   /**
    * The event type follows a format of <module-name>.<event-name> which can be
    * referenced in the associated .yml file
   */
   class SdkEvent
   {
   public:
      SdkEvent() : mType("None") {}
      SdkEvent(const std::string& eventName) : 
         mType(eventName)
      {}
      
      SdkEvent(const std::string& eventName, std::string&& json) :
         mType(eventName), mJson(std::move(json)) 
      {}
      
      // move constructor
      SdkEvent(SdkEvent&& other) :
         mType(std::move(other.mType)),
         mJson(std::move(other.mJson))
      {}
      
      // move assignment
      SdkEvent& operator=( SdkEvent&& other)
      {
         mType = std::move(other.mType);
         mJson = std::move(other.mJson);
         return *this;
      }

      SdkEvent& operator=(const SdkEvent& other) = default;
      SdkEvent(const SdkEvent& other) = default;

      virtual~ SdkEvent() {}

      const std::string& type() const { return mType; }
      const std::string& json() const { return mJson; }

   private:
      std::string mType;
      std::string mJson;
   };

   struct CPCAPI2_SHAREDLIBRARY_API PhoneLogEvent2
   {
      CPCAPI2::LogLevel level;
      const char* subsystem;
      const char* file;
      int line;
      const char* message;
      const char* sourceThreadId;
   };

   class CPCAPI2_SHAREDLIBRARY_API PhoneLogger2
   {
   public:
      virtual bool operator()(const PhoneLogEvent2& logEvent) = 0;
   };

   class CPCAPI2_SHAREDLIBRARY_API PhoneInternal : public Phone
   {
   public:
      static PhoneInternal* create(int threadPoolThreadIdx);
      static PhoneInternal* create(PhoneInternal* phone);

      virtual int initialize(const LicenseInfo& licenseInfo, PhoneErrorHandler* errorHandler, bool useNetworkChangeManager = true) = 0;
      virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, bool useNetworkChangeManager = true) = 0;
      virtual int initialize(CPCAPI2::Licensing::LicensingClientManager* licensingManager, PhoneHandler* handler, bool useNetworkChangeManager) = 0;
      virtual int initialize(const char* licenseKey, const cpc::string& licenseDocumentLocation, const cpc::string& appVersion) = 0;
      virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const SslCipherOptions& ciphers, bool useNetworkChangeManager = true) = 0;
      // initializes Phone with the preferred external resolver setting:
      virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const ConnectionPreferences& conPref, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportWiFi) = 0;

      virtual void externalLog(CPCAPI2::LogLevel level, const cpc::string& msg) = 0;

      // enables or disables the per Phone instance SDK file logging mechanism. The file name logged to will begin with the value of id.
      // resip based SDK logging will not show up in these logs.
      virtual int setLocalFileLoggingEnabled(const cpc::string& id, bool enabled) = 0;
      // sets per Phone instance SDK file logging level. defaults to warn -- TODO: rename or clone, since this applies to both file and callback logging
      virtual int setLocalFileLoggingLevel(CPCAPI2::LogLevel lvl) = 0;

      virtual int setLocalCallbackLoggingEnabled(LocalLoggerHandler* handler, bool enabled) = 0;

      virtual LocalLogger* localLogger() = 0;


      virtual int setLoggingEnabled2(PhoneLogger2* logger, bool enabled) = 0;

      virtual void runOnSdkModuleThread(void (*func)(void)) = 0;
      virtual void runOnSdkModuleThread(void(*funcToRun)(void*), void* context) = 0;
      virtual void blockUntilRanOnSdkModuleThread(void(*funcToRun)(void*), void* context) = 0;
      virtual void runOnAsioThread(void(*funcToRun)(void*), void* context) = 0;

      virtual void setPermissionsHandler(PermissionsHandler* handler) = 0;

      virtual void onRequestPermissionsResult(int requestCode, std::vector<CPCAPI2::Permission> permissions, std::vector<bool> result) = 0;

      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

      // set a function to be called when PhoneInterface destructs. useful for unit test purposes
      virtual void setCallOnDestructFn(void (*func)(void*), void* context) = 0;
      virtual void setCallOnAppReleaseFn(void (*func)(void*), void* context) = 0;

      virtual void setPhoneName(const cpc::string& name) = 0;

      // synchronously checks on the main SDK thread (PhoneInterface thread) whether 
      // any OpenSSL errors exist on that thread's OpenSSL error queue.
      // outLastOpenSslError will be set to 0 if no error exists, otherwise will contain
      // the value of the latest error.
      virtual void checkSdkThreadOpenSslErrors(unsigned long& outLastOpenSslError) = 0;

      /**
       * @brief Create a Event Queue object
       * 
       * Create a new event queue and return the queue’s handle. The queue is registered for the event 
       * types specified in events. If no event types are specified, the queue will return all events fired
       * by the SDK. Event types may be specific events (ex. Phone.ErrorEvent) or an asterisk may be added
       * to the end of the string to perform starts with matching (e.g. Phone.* to match all Phone events).
       * 
       * @param events List of events to register to
       * @return int Returns the queueId of the created queue
      */
      virtual int createEventQueue(const cpc::vector<cpc::string>& events) = 0;

      /**
       * @brief Destroy the specified queue
       * 
       * @param queueId Identifier of the queue to destroy
      */
      virtual void destroyEventQueue(int queueId) = 0;

      /**
       * @brief Retrieve the next event in the queue.
       * 
       * Returns the next event in the queue. If there are no pending events in the queue, the event poller
       * will wait up to timeout milliseconds (0 is infinite) for an event otherwise return an Event with
       * type = “None”. If timeout is negative, an Event with type = “None” is returned immediately.
       * 
       * @param queueId Identier of the queue from which to retrieve events
       * @param timeout Timeout duration to wait for the event in milliseconds (0 is infinite)
       * @return SdkEvent Returns the next event in the queue
      */
      virtual SdkEvent getEvent(int queueId, int timeout) = 0;

   };

   class PhoneLoggerInternal : public PhoneLogger
   {
   public:
      virtual void startLogger() = 0;

      virtual void cleanupLogger() = 0;
   };

}
#endif // CPCAPI2_PHONE_INTERNAL_H
