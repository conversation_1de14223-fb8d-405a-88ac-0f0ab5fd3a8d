#pragma once

#if !defined(CPCAPI2_PERMISSION_HANDLER_H)
#define CPCAPI2_PERMISSION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include <vector>

namespace CPCAPI2
{
/**
 * <PERSON><PERSON> for notifying the app that required permissions are missing
 */
class PermissionsHandler
{
public:
   /**
    * Indicates that a required permission hasn't been granted
    */
   virtual void requestPermissions(int requestCode, std::vector<cpc::string> permissions) = 0;
};
}
#endif // CPCAPI2_PERMISSION_HANDLER_H
