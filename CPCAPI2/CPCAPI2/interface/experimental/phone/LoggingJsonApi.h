#pragma once

#if !defined(CPCAPI2_LOGGING_JSON_API_H)
#define CPCAPI2_LOGGING_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API LoggingJsonApi
{
public:
   /**
   * Get a reference to the %LoggingJsonApi interface.
   */
   static LoggingJsonApi* getInterface(Phone* cpcPhone);
};

}

#endif // CPCAPI2_LOGGING_JSON_API_H
