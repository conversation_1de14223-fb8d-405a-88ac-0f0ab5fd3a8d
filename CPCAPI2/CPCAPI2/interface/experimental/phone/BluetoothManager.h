#pragma once

#ifndef CPCAPI2_BLUETOOTH_MANAGER_H
#define CPCAPI2_BLUETOOTH_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcapi2utils.h"

namespace CPCAPI2
{
// Forward declarations
class BluetoothHandler;
class Phone;

/**
 *
 * Interface for bluetooth related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API BluetoothManager
{
public:
   typedef int BluetoothManagerHandle;

   /**
    * Get a reference to the %BluetoothManager interface.
    *
    * @param cpcPhone the phone object to be associated with this manager.
    *
    * @return the %BluetoothManager interface.
    */
   static BluetoothManager* getInterface(Phone* cpcPhone);

   /**
    * Create a new handle needed to install and remove a %BluetoothHandle.
    */
   virtual BluetoothManagerHandle create() = 0;
    
   /**
    * The blocking modes for the process() function. See that function
    * for details.
    */
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
        
   /**
    * Allows the application code to receive callback notifications from the SDK.
    *
    * These callbacks will happen synchronously, in the same thread of execution as that in which
    * %process() is invoked.  Depending on the application threading model,
    * process() can be used in two different ways:
    * <ol>
    * <li>blocking mode ?Typically in this mode, %process() is called by the
    * application from a background (worker) thread.  The call to process()
    * blocks until a callback function needs to be invoked.
    * <li>non-blocking mode ?In this mode, %process() is called by the application
    * from the main (GUI) thread of the application, typically from the
    * main message/event loop.  In this mode, %process() returns immediately
    * and so must be called frequently enough that the application can receive
    * its callback notifications in a timely manner.
    *
    * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
    *                representing the time the call to process(..) will block waiting for a callback
    *                from the SDK
    * </ol>
    */
   virtual int process(unsigned int timeout = kBlockingModeNonBlocking) = 0;

   /**
    * Set the handler for network change events.
    *
    * @param handle the reference from %create() used to track this handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(BluetoothManagerHandle handle, BluetoothHandler* handler) = 0;
    
   /**
    * Remove a handler previously installed with %setHandler().
    *
    * @param handle the reference used to track the installed handler.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int removeHandler(BluetoothManagerHandle handle) = 0;
   
   /**
    * Returns bluetooth headset availability
    * @return bool.
    */
   virtual bool isBluetoothHeadsetAvailable() const = 0;
   /**
    * Returns bluetooth audio availability
    * @return bool.
    */
   virtual bool isBluetoothAudioConnected() const = 0;
   
protected:
   /*
    * The SDK will manage memory life of %BluetoothManager.
    */
   virtual ~BluetoothManager() {}
};
}

#endif // CPCAPI2_BLUETOOTH_MANAGER_H
