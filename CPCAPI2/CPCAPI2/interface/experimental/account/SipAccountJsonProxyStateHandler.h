#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H)
#define CPCAPI2_SIP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "../jsonapi/JsonApiClient.h"
#include "account/SipAccountState.h"

namespace CPCAPI2
{
namespace SipAccount
{
struct JsonProxyAccountStateEvent
{
   cpc::vector<SipAccountState> accountState;
};

class SipAccountJsonProxyStateHandler
{
public:
   virtual int onAccountState(CPCAPI2::JsonApi::JsonApiConnectionHandle conn, const JsonProxyAccountStateEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_ACCOUNT_JSON_PROXY_STATE_HANDLER_H
