#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_JSON_API_H)
#define CPCAPI2_SIP_ACCOUNT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;

namespace SipAccount
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API SipAccountJsonApi
{
public:
   /**
   * Get a reference to the %SipAccountJsonApi interface.
   */
   static SipAccountJsonApi* getInterface(Phone* cpcPhone);
};

}
}

#endif // CPCAPI2_SIP_ACCOUNT_JSON_API_H
