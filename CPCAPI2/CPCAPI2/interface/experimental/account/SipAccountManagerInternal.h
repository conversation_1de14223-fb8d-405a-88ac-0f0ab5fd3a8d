#pragma once

#if !defined(CPCAPI2_ACCOUNT_MANAGER_INTERNAL_H)
#define CPCAPI2_ACCOUNT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "account/SipAccount.h"

namespace CPCAPI2
{

namespace SipAccount
{

class SipNetworkProbeHandler;
class DialogDnsResultHandler;
class SipAccountMessageDecoratorHandler;

class CPCAPI2_SHAREDLIBRARY_API SipAccountManagerInternal
{

public:

   virtual CPCAPI2::SipAccount::SipAccountHandle create(const SipAccountSettings& accountSettings, CPCAPI2::SipAccount::SipAccountHandle handle) = 0;
   virtual CPCAPI2::SipAccount::SipAccountHandle create(CPCAPI2::SipAccount::SipAccountHandle handle) = 0;

   virtual int setSipAccountUseAlias(CPCAPI2::SipAccount::SipAccountHandle handle, bool useAlias) = 0;

   enum CertLoadStorageType
   {
      CertLoadStorageType_OS,
      CertLoadStorageType_FileSystem
   };

   virtual int setCertStorageLoadType(SipAccount::SipAccountHandle h, CertLoadStorageType type) = 0;
   virtual int setCertStorageFileSystemPath(SipAccount::SipAccountHandle h, const cpc::string& path) = 0;

   virtual int discardRegistration(CPCAPI2::SipAccount::SipAccountHandle handle) = 0;

   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   virtual int sendOptionsMessage(SipAccount::SipAccountHandle accountHandle, const cpc::string target) = 0;

   /**
    * Skip resetting transport on network change; only applies to useRegistar = false
    * Resetting UDP transports on network change with useRegistrar = false can cause UDP port in use exceptions
    * on macOS -- which is recovered by the SDK but disrupts normal account state flow. Potentially fixable by
    * marking our SIP UDP sockets as reusable?
    */
   virtual int setSkipResetTransportOnNetworkChange(SipAccount::SipAccountHandle accountHandle, bool skip) = 0;

#ifdef CPCAPI2_AUTO_TEST

   virtual int closeTransportConnections(CPCAPI2::SipAccount::SipAccountHandle handle) = 0;
   virtual int setFakeResponse(CPCAPI2::SipAccount::SipAccountHandle handle, bool enable, cpc::string method, int responseCode = 0, std::string responseReason = "", int warningCode = 0) = 0;

   /**
    * Set the handler for probe status events on the specified account. Set the handler
    * immediately after creating the account.  Must be called on the same thread as process(..)
    *
    * To un-register the handler, pass NULL for handler.
   */
   virtual int setProbeHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipNetworkProbeHandler* handler) = 0;

   /**
    * Set the delay before a probe is sent out. Maximum delay has to be less than the probing timeout of 2000 msecs. The
    * delay can be used during testing to provide some control over the order in which the probing responses are received.
   */
   virtual int setProbeMockDelay(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::IpVersion ipVersion, unsigned int delayMsecs) = 0;

   /**
    * Set the handler for dns reset events on the specified account. Set the handler
    * immediately after creating the account.  Must be called on the same thread as process(..)
    *
    * To un-register the handler, pass NULL for handler.
   */
   virtual int setDnsHandler(CPCAPI2::SipAccount::SipAccountHandle account, DialogDnsResultHandler* handler) = 0;
   /**
    * Set the domain lock string to test
    * Value should be same as would CPCAPI2_BRAND_LICENSE_DOMAIN_LOCK
   */
   virtual int setDomainLockString(const cpc::string& brandingString) = 0;

   virtual int setIgnoreNetworkChangeStarcodeFilter(CPCAPI2::SipAccount::SipAccountHandle account, bool enabled) = 0;

   /**
    * Set the handler for sip account message decorator events on the specified account. Set the handler
    * immediately after creating the account.  Must be called on the same thread as process(..)
    *
    * To un-register the handler, pass NULL for handler.
   */
   virtual int setDecoratorHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountMessageDecoratorHandler* handler) = 0;

#endif

};

}

}

#endif // CPCAPI2_ACCOUNT_MANAGER_INTERNAL_H


