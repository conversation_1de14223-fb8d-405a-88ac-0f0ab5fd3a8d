#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_JSON_PROXY_H)
#define CPCAPI2_SIP_ACCOUNT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "account/SipAccount.h"

namespace CPCAPI2
{
class Phone;

namespace SipAccount
{
class SipAccountJsonProxyStateHandler;
class SipAccountHandler;
typedef unsigned int SipAccountHandle;

/**
 */
class CPCAPI2_SHAREDLIBRARY_API SipAccountManagerJsonProxy : public CPCAPI2::SipAccount::SipAccountManager
{
public:
   /**
   * Get a reference to the SipAccountManagerJsonProxy interface.
   */
   static SipAccountManagerJsonProxy* getInterface(Phone* cpcPhone);

   virtual int setStateHandler(SipAccountJsonProxyStateHandler* handler) = 0;
   virtual int requestStateAllAccounts() = 0;


protected:
   /*
    * The SDK will manage memory life of %SipAccountManagerJsonProxy.
    */
   virtual ~SipAccountManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_SIP_ACCOUNT_JSON_PROXY_H
