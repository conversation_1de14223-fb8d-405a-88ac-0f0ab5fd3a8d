#pragma once

#if !defined(CPCAPI2_SIP_NETWORK_PROBE_HANDLER_H)
#define CPCAPI2_SIP_NETWORK_PROBE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "account/SipAccountSettings.h"
#include <iostream>

namespace CPCAPI2
{

namespace SipAccount
{

/**
* Event passed in SipNetworkProbeHandler::onNetworkProbeStatusChanged(); 
* <ul>
* <li>probeMessageId: Unique identifier for the probe status update message.
* <li>status: Probe status.
* <li>reason: Reason for the probe status change.
* <li>ipVersionPreferred: IP version configured in SipAccountSettings.
* <li>ipVersionSelected: IP version selected based on probing results.
* <li>probeIpVersion: IP version being probed.
* <li>tid: Transaction-id of the SIP message.
* <li>signallingStatusCode: The SIP code for the failure, for example, 404.
* <li>signallingResponseText: The SIP response text for the status code.
* <li>probeTargetAddress: Target address that the probe was sent to.
* <li>probeLocalAddress: Contact address used in the probe.
* </ul>
*/
struct SipNetworkProbeStatusChangedEvent
{
   enum Status
   {
      Status_Probing                     = 1,  /**< Probes have been sent, waiting for response. */
      Status_ResponseReceived            = 2,  /**< Response received from the non-preferred ip version, could be for either the V4 or V6 probe. */
      Status_Completed                   = 3   /**< IP version selected, either due to a successful response or probe timeout. */
   };
   
   enum Reason
   {
      Reason_None                        = 0,  /**< No explicit reason for probe status change. */
      Reason_ProbesSent                  = 1,  /**< Probe requests have been sent to both the V4 and V6 addresses. */
      Reason_ResponseReceived            = 2,  /**< Response was received from the non-preferred ip version, could be for either the V4 or V6 probe. */
      Reason_PreferredResponseReceived   = 3,  /**< Response was received from the preferred ip version, could be for either the V4 or V6 probe. */
      Reason_ProbeTimeout                = 4,  /**< Probe timeout, i.e. no response to either the V4 or V6 probe within 2 seconds. */
      Reason_StaleResponseReceived       = 5,  /**< Response was received after the probe timed-out, could be a response from wire or internal response such as transaction timeout (408). */
      Reason_RegisterSent                = 6,  /**< Register request has been sent on selected IP version. */
      Reason_NetworkChangeDelay          = 7   /**< Probing delay due to network change. */
   };
   
   unsigned int                          probeMessageId;
   Status                                status;
   Reason                                reason;
   IpVersion                             ipVersionPreferred;
   IpVersion                             ipVersionSelected;      /** Valid only after probing status is completed. */

   /** Following variables are only applicable to a particular probe transaction, i.e. V4 or V6. */
   IpVersion                             probeIpVersion;
   cpc::string                           tid;
   unsigned int                          signallingStatusCode;
   cpc::string                           signallingResponseText;
   cpc::string                           probeTargetAddress;
   cpc::string                           probeLocalAddress;

   SipNetworkProbeStatusChangedEvent() :
      probeMessageId(0),
      status(Status_Probing),
      reason(Reason_None),
      ipVersionPreferred(IpVersion_Auto),
      ipVersionSelected(IpVersion_V4),
      probeIpVersion(IpVersion_V4),
      tid(""),
      signallingStatusCode(0),
      signallingResponseText(""),
      probeTargetAddress(""),
      probeLocalAddress("") {}
};

/**
 * The handler for SIP network probing events. SipAccount::setProbingHandler().
*/
class SipNetworkProbeHandler
{

public:

   /**
   * Notifies the application when the probing state has changed to a specific state.
   */
   virtual int onNetworkProbeStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipNetworkProbeStatusChangedEvent& args) = 0;

};


inline std::ostream& operator<<(std::ostream& os, SipNetworkProbeStatusChangedEvent::Status& status)
{
   switch (status)
   {
      case SipNetworkProbeStatusChangedEvent::Status_Probing: os << "probing"; break;
      case SipNetworkProbeStatusChangedEvent::Status_ResponseReceived: os << "responseReceived"; break;
      case SipNetworkProbeStatusChangedEvent::Status_Completed: os << "completed"; break;
      default: os << "invalid"; break;
   }
      
   return os;
}

inline std::ostream& operator<<(std::ostream& os, SipNetworkProbeStatusChangedEvent::Reason& reason)
{
   switch (reason)
   {
      case SipNetworkProbeStatusChangedEvent::Reason_None: os << "none"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_ProbesSent: os << "probesSent"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_ResponseReceived: os << "responseReceived"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_PreferredResponseReceived: os << "preferredResponseReceived"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_ProbeTimeout: os << "probeTimeout"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_StaleResponseReceived: os << "staleResponseReceived"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_RegisterSent: os << "registerSent"; break;
      case SipNetworkProbeStatusChangedEvent::Reason_NetworkChangeDelay: os << "networkChangeDelay"; break;
      default: os << "invalid"; break;
   }
      
   return os;
}

inline std::ostream& operator<<(std::ostream& os, SipNetworkProbeStatusChangedEvent& event)
{
   os << "Probe Status Changed Event: [probeMessageId=" << event.probeMessageId << ", status=" << event.status << ", reason=" << event.reason << ", preferred-ip=" << event.ipVersionPreferred;
   
   if (event.status == SipNetworkProbeStatusChangedEvent::Status_Completed)
   {
      os << ", selected-ip=" << event.ipVersionSelected;
   }
   
   if (!event.tid.empty())
   {
      os << ", probed-ip=" << event.probeIpVersion << ", tid=" << event.tid << ", response-code=" << event.signallingStatusCode << ", response-desc="
         << event.signallingResponseText << ", target-address=" << event.probeTargetAddress << ", local-address=" << event.probeLocalAddress;
   }
   
   os << "]";
   return os;
}
   
}

}

#endif // CPCAPI2_SIP_NETWORK_PROBE_HANDLER_H
