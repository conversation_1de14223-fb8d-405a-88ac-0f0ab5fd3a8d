#pragma once

#ifndef __CPCAPI2_MESSAGESTORE_HANDLER_H__
#define __CPCAPI2_MESSAGESTORE_HANDLER_H__

#include <cpcstl/string.h>

#include "cpcapi2defs.h"
#include "MessageStoreTypes.h"

namespace CPCAPI2
{
   namespace MessageStore
   {
      /**
       * Events which are fired to the application. The application should
       * ignore the 'placeholder' integer as it will always be set to zero.
       * The handles for the message store are located inside of each event.
       */
      class MessageStoreHandler
      {
      public:
         virtual int onHandler( unsigned int placeholder, const HandlerEvent& evt ) = 0;
         virtual int onCreate( unsigned int placeholder, const KreateEvent& evt ) = 0;
         virtual int onDestroy( unsigned int placeholder, const DestroyEvent& evt ) = 0;
         virtual int onHistoryLoaded( unsigned int placeholder, const HistoryLoadedEvent& evt ) = 0;
         virtual int onQueryHistory( unsigned int placeholder, const HistoryEvent& evt ) = 0;
         virtual int onQueryMessageInfo( unsigned int placeholder, const MessageInfoEvent& evt ) = 0;
         virtual int onQueryLastMessageInfo( unsigned int placeholder, const LastMessageInfoEvent& evt ) = 0;
         virtual int onHistoryFlush( unsigned int placeholder, const FlushEvent& evt ) = 0;
      };

      class MessageStoreHandlerStub : public MessageStoreHandler
      {
      public:
         int onHandler( unsigned int placeholder, const HandlerEvent& evt ) OVERRIDE { return kSuccess; }
         int onCreate( unsigned int placeholder, const KreateEvent& evt ) OVERRIDE { return kSuccess; }
         int onDestroy( unsigned int placeholder, const DestroyEvent& evt ) OVERRIDE { return kSuccess; }
         int onHistoryLoaded( unsigned int placeholder, const HistoryLoadedEvent& evt ) OVERRIDE { return kSuccess; }
         int onQueryHistory( unsigned int placeholder, const HistoryEvent& evt ) OVERRIDE { return kSuccess; }
         int onQueryMessageInfo( unsigned int placeholder, const MessageInfoEvent& evt ) OVERRIDE { return kSuccess; }
         int onQueryLastMessageInfo( unsigned int placeholder, const LastMessageInfoEvent& evt ) OVERRIDE { return kSuccess; }
         int onHistoryFlush( unsigned int placeholder, const FlushEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}

#endif // __CPCAPI2_MESSAGESTORE_HANDLER_H__
