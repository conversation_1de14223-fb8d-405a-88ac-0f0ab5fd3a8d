#pragma once
#ifndef __CPCAPI2__MESSAGESTORE_TYPES_H__
#define __CPCAPI2__MESSAGESTORE_TYPES_H__

#include <stdint.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <xmpp/XmppAccount.h>

namespace CPCAPI2
{
   namespace MessageStore
   {
      typedef unsigned int MessageStoreHandle;
      typedef unsigned int MessageViewHandle;

      // Forward decl's
      class MessageStoreProviderInputs;

      /**
       * Possible providers for data to the message store (not all are
       * as of yet implemented)
       */
      typedef enum ProviderType
      {
         //ProviderType_XMPP,
         ProviderType_XMPP_MultiUser
         // ProviderType_RemoteSync // possible future
      } ProviderType;

      /**
       * Message store specific settings
       */
      typedef struct Settings
      {
         ProviderType provider;

         /**
          * Writable directory where the cache file will be stored. (full path)
          */
         cpc::string cacheDirectory;

         /**
          * ProviderType_XMPP_MultiUser chat specific setting: causes
          * non-persistent chatrooms (sometimes referred to as adhoc)
          * to be ignored and not cached.
          */
         bool ignoreNonPersistentMUC;

         // initializing ctor
         Settings( void )
            : provider( ProviderType_XMPP_MultiUser ),
              ignoreNonPersistentMUC( false ) {}
      } Settings;

      /**
       * An Entity is a uniquely identifiably object in the system which
       * contains a user visible alias. It is normally a user of the chat,
       * however it can be other things, such as a chat bot, or a room in the
       * case of a group chat, or even a server.
       *
       * Messages are sent to or from entities, but not all entities support
       * both sending and receiving messages
       */
      typedef struct EntityInfo
      {
         /**
          * A unique ID for the entity. No guarantees whether this is user
          * visible or not, but it possibly not very friendly to show to
          * the end user.
          */
         cpc::string uniqueID;

         /**
          * An optional alias for the user which should be shown instead of the
          * ID, when it is set. In the case of a room, the alias is the user
          * visible room name.
          */
         cpc::string alias;
      } EntityInfo;

      /**
       * This structure describes information about each message in the store.
       * It makes certain assumptions about the protocol, which any providers
       * will endeavour to adhere to in order to maintain the required
       * functionality.
       */
      typedef struct MessageInfo
      {
         /**
          * A non-user-visible unique identifier assigned to this message which
          * is unique (at least minimally within the chatroom but possibly at a
          * higher level of uniqueness).
          */
         cpc::string uniqueID;

         /**
          * A non-user-visible identifier which links the message back to a
          * chatroom or, in the peer-to-peer context, a thread ID (aka session
          * ID). This should match the ID provided in the settings.
          */
         cpc::string roomID;

         /**
          * Information about the user that sent the message.
          */
         EntityInfo sender;

         /**
          * The recipient of the message. Note that this may refer either to
          * a specific user, or possibly to the room itself in the case of
          * a persistent group chat.
          */
         EntityInfo recipient;

         /**
          * The message content in plaintext. Message content could be either
          * in plaintext only, or html only, or both.
          */
         cpc::string plainTextContent;

         /**
          * The (optional) message content in HTML. Message content could be either
          * in plaintext only, or html only, or both.
          */
         cpc::string htmlContent;

         /**
          * Timestamp of the message, in milliseconds since the unix epoch. This
          * is updated when the message is replaced or deleted.
          */
         uint64_t timestampMillis;

         /**
          * Has the message been deleted.
          */
         bool isDeleted;

         /**
          * Has the message changed from the original.
          */
         bool isEdited;

         /**
          * Has the message been delivered to the recipient.
          */
         bool isDelivered;

         /**
          * Has the message been read by the recipient.
          */
         bool isRead;
      
         /**
          * Reactions that have been applied to the message.
          */
         cpc::vector<cpc::string> reactions;
      } MessageInfo;


      //================= Event Types ==============================

      typedef struct AsyncResult
      {
         int result;             // kSuccess, kFail, etc
         cpc::string reasonText; // only if result is a failure
      } AsyncResult;

      typedef struct HandlerEvent
      {
         AsyncResult result;
      } HandlerEvent;

      /**
       * So-named because CreateEvent is a win32 method
       */
      typedef struct KreateEvent
      {
         MessageStoreHandle hMessageStore;
         cpc::string roomID;
      } KreateEvent;

      typedef struct DestroyEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;
      } DestroyEvent;

      typedef struct HistoryLoadedEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;

         /**
          * If HistoryLoadedEvent was fired due to discovery, the hAccount may not
          * yet be ready, in that case hAccount will be set to -1.
          */
         CPCAPI2::XmppAccount::XmppAccountHandle hAccount;
      } HistoryLoadedEvent;

      typedef struct HistoryEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;
         cpc::vector< MessageInfo > history; // sorted according to timestamp
      } HistoryEvent;

      typedef struct MessageInfoEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;
         MessageInfo info;
      } MessageInfoEvent;

      typedef struct LastMessageInfoEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;
         MessageInfo lastInfo;
      } LastMessageInfoEvent;

      typedef struct FlushEvent
      {
         MessageStoreHandle hMessageStore;
         AsyncResult result;
      } FlushEvent;
   }
}

#endif // __CPCAPI2__MESSAGESTORE_TYPES_H__
