#pragma once
#ifndef __CPCAPI2__MESSAGESTORE_MANAGER_H__
#define __CPCAPI2__MESSAGESTORE_MANAGER_H__

#include <cpcstl/string.h>

#include "MessageStoreTypes.h"
#include "MessageStoreHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace MessageStore
   {
      /**
       * The purpose of the message store manager is to provide an interface
       * for applications which abstracts and handles the issues around
       * persisting and paging through a large message history which is
       * accessible normally only through a protocol to a server back-end.
       *
       * It should obviate the need for the application to create its own
       * client-side caching systems for messages.
       *
       * NB: It is geared toward solving the problem of historical messages and
       * not intended to fully abstract messaging. Therefore it DOES NOT try to
       * hide all messaging protocol interactions. Instead this component will
       * update itself based on events coming from the network which impact the
       * messaging history (northward), but does not issue commands which
       * control/direct the messaging itself (southward).
       *
       * Unless otherwise noted all methods in this manager are asynchronous
       * and require an event signaled to the application for completion and/or
       * status.
       */
      class CPCAPI2_SHAREDLIBRARY_API MessageStoreManager
      {
      public:
         /**
          * Get a reference to the %MessageStoreManager interface. This method
          * is synchronous.
          */   
         static MessageStoreManager* getInterface( Phone* cpcPhone );

         /**
          * Sets the application handler for message store events. Note that
          * this method is also asynchronous. There is no guarantee that the
          * handler will actually be set (or removed) after the call is
          * returned. To that end, onHandler will be fired when the handler has
          * been set.
          *
          * In order to remove the handler, call setHandler with NULL as the
          * handler parameter. ***IMPORTANT: The SDK will delete the handler
          * when it is safe to do so***. It must be this way because the actual
          * deletion will also happen asynchronously. As such the ownership of
          * the handler is passed from the application to the SDK once it is
          * set as the handler.
          */
         virtual int setHandler( MessageStoreHandler *handler ) = 0;

         /**
          * Pushes the settings down to the message store manager, settings
          * will take effect until new settings are pushed. After applying
          * settings, events will start.
          */
         virtual int applySettings( const Settings& newSettings ) = 0;

         /**
          * Deletes/Releases all information related to a message store. The
          * cache itself will be removed from disk and will not show up in
          * future discoveries.
          */
         virtual int destroy( const MessageStoreHandle& hMessageStore ) = 0;

         /**
          * Returns a list of historical items for the given room, starting at
          * the the provided timestamp and until the latest timestamp cached.
          *
          * @param hMessageStore the message store in question
          * @param sinceMillis a timestamp, in milliseconds since the epoch,
          *        after which all cached history will be returned up until the
          *        most recent cached item.
          */
         virtual int queryCachedHistory( const MessageStoreHandle& hMessageStore,
            uint64_t sinceMillis ) = 0;

         /**
          * The same as the other queryCachedHistory method, however there
          * is a limit on the number of results which may be returned.
          *
          * @param hMessageStore the message store in question
          * @param sinceMillis a timestamp, in milliseconds since the epoch,
          *        after which all cached history will be returned up until the
          *        most recent cached item.
          * @param limit the number of results returned
          */
         virtual int queryCachedHistory( const MessageStoreHandle& hMessageStore,
            uint64_t sinceMillis, int limit ) = 0;

         /**
          * Functions similar to queryCachedHistory, however the timestamp
          * represents a point in time before which the history will be queried.
          */
         virtual int queryCachedHistoryBackward(
            const MessageStoreHandle& hMessageStore,
            uint64_t untilMillis, int limit ) = 0;

         /**
          * Given a (known) message ID, query the cached history of that specific
          * message.
          *
          * @param hMessageStore the message store to query
          */
         virtual int queryMessageInfo( const MessageStoreHandle& hMessageStore,
            const cpc::string& messageID ) = 0;

         /**
          * Returns (in an asynchronous event on the handler) the last known
          * timestamp which has been retained in the cache.
          *
          * @param hMessageStore the message store to query
          */
         virtual int queryLastMessageInfo( const MessageStoreHandle& hMessageStore ) = 0;

         /**
          * Releases all historical items associated with the room.  Use this
          * function with caution, as all history will be dropped permanently!
          * Note, this differs from destroy in the sense that the message store
          * is still available on disk, but with no history.
          *
          * @param hMessageStore the message store in question
          */
         virtual int flushHistory( const MessageStoreHandle& hMessageStore ) = 0;

         /**
          * The blocking modes for the process() function. See that function
          * for details.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         // constants
         static const int kAccountModuleDisabled = -1;

         /**
          * Allows the application code to receive callback notifications
          * from the SDK.
          *
          * These callbacks will happen synchronously, in the same thread of
          * execution as that in which %process() is invoked.  Depending on
          * the application threading model, process() can be used in two
          * different ways:
          * <ol>
          * <li>blocking mode ?Typically in this mode, %process() is called by the 
          * application from a background (worker) thread.  The call to process() 
          * blocks until a callback function needs to be invoked.
          * <li>non-blocking mode ?In this mode, %process() is called by the application 
          * from the main (GUI) thread of the application, typically from the 
          * main message/event loop.  In this mode, %process() returns immediately 
          * and so must be called frequently enough that the application can receive 
          * its callback notifications in a timely manner.
          *
          * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
          *                representing the time the call to process(..) will block waiting for a callback
          *                from the SDK
          * </ol>
          */
         virtual int process( unsigned int timeout ) = 0;
      };
   }
}

#endif // __CPCAPI2__MESSAGESTORE_MANAGER_H__
