#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_JSON_API_H)
#define CPCAPI2_CLOUD_RELAY_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace CloudRelay
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API CloudRelayJsonApi
{
public:
   /**
   * Get a reference to the %CloudRelayJsonApi interface.
   */
   static CloudRelayJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_CLOUD_RELAY_JSON_API_H
