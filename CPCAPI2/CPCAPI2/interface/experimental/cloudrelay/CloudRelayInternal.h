#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_MANAGER_INTERNAL_H)
#define CPCAPI2_CLOUD_RELAY_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "CloudRelayManager.h"

namespace CPCAPI2
{
namespace CloudRelay
{
   class CPCAPI2_SHAREDLIBRARY_API CloudRelayManagerInternal : public CloudRelayManager
   {
   public:
      static CloudRelayManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   };  
}
}
#endif // CPCAPI2_CLOUD_RELAY_MANAGER_INTERNAL_H
