#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_JSON_PROXY_H)
#define CPCAPI2_CLOUD_RELAY_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "cloudrelay/CloudRelayManager.h"

namespace CPCAPI2
{
class Phone;
namespace CloudRelay
{
class CPCAPI2_SHAREDLIBRARY_API CloudRelayManagerJsonProxy : public CPCAPI2::CloudRelay::CloudRelayManager
{
public:
   /**
   * Get a reference to the CloudRelayManagerJsonProxy interface.
   */
   static CloudRelayManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %CloudRelayManagerJsonProxy.
    */
   virtual ~CloudRelayManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_CLOUD_RELAY_JSON_PROXY_H
