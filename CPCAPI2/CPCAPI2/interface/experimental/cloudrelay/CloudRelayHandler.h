#pragma once

#if !defined(CPCAPI2_CLOUD_RELAY_HANDLER_H)
#define CPCAPI2_CLOUD_RELAY_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "CloudRelayTypes.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{
namespace CloudRelay
{
struct BroadcastEvent
{
   CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser;
   cpc::string msg;
};

struct MessageEvent
{
   CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser;
   cpc::string msg;
};

class CloudRelayHandler
{
public:
   virtual int onBroadcast(CloudRelayHandle relay, const BroadcastEvent& args) = 0;
   virtual int onMessage(CloudRelayHandle relay, const MessageEvent& args) = 0;
};

}
}
#endif // CPCAPI2_CLOUD_RELAY_HANDLER_H
