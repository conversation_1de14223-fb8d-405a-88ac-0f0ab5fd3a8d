#pragma once

#if !defined(CPCAPI2_CloudRelay_MANAGER_H)
#define CPCAPI2_CloudRelay_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "CloudRelayTypes.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{
class Phone;

namespace CloudRelay
{
class CloudRelayHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API CloudRelayManager
{
public:
   /**
   * Get a reference to the %CloudRelayManager interface.
   */   
   static CloudRelayManager* getInterface(Phone* cpcPhone);

   static const cpc::string& getServiceId();

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kCloudRelayModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling CloudRelayManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};

   virtual int start() = 0;
   virtual int shutdown() = 0;

   virtual CloudRelayHandle getRelay() = 0;

   virtual int setHandler(CloudRelayHandler* handler) = 0;
   virtual int setHandler(CloudRelayHandle relay, CloudRelayHandler* handler) = 0;

   virtual int broadcast(CloudRelayHandle relay, const cpc::string& msg) = 0;
   virtual int sendTo(CloudRelayHandle relay, CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::string& msg) = 0;
};

}
}

#endif // CPCAPI2_CloudRelay_MANAGER_H
