#pragma once

#if !defined(CPCAPI2_PROVISION_HANDLER_H)
#define CPCAPI2_PROVISION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "provision/RcsProvision.h"

#include <set>

namespace CPCAPI2
{
namespace RcsProvision
{

enum RcsProvisionStatus
{
   RcsProvisionStatus_Dormant                 = -3,
   RcsProvisionStatus_DisabledUntilUserAction = -2,
   RcsProvisionStatus_DisabledPermanently     = -1,
   RcsProvisionStatus_DisabledTemporarily     = 0,
   RcsProvisionStatus_Enabled                 = 1
};

struct RcsOnProvisionStatusChangedEvent
{
   RcsProvisionStatus                     status;
   SipAccount::SipAccountHandle           account;
};

struct RcsOnProvisionUserMessageEvent
{
   cpc::string                           title;
   cpc::string                           message;
   bool                                   showAcceptButton;
   bool                                   showRejectButton;
};

/**
 * Handler for events relating to provision option change notification.
 */
class RcsProvisionHandler
{
public:
   /**
   * Notifies that there is a change of the provision status.
   * @param provision The %Provision to use for delivery
   * @param args Contains details about the change of the provision status
   */   
   virtual int onProvisionStatusChanged(RcsProvisionHandle provision, const RcsOnProvisionStatusChangedEvent& args) = 0;

   /**
   * Notifies that there is a user message sent from the provision server.
   * @param provision The %Provision to use for delivery
   * @param args Contains details about the user message
   */   
   virtual int onProvisionUserMessage(RcsProvisionHandle provision, const RcsOnProvisionUserMessageEvent& args) = 0;
};

}
}
#endif // CPCAPI2_PROVISION_HANDLER_H
