#pragma once

#if !defined(CPCAPI2_PROVISION_H)
#define CPCAPI2_PROVISION_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "account/SipAccount.h"
#include "../capability/RcsCapabilityDiscovery.h"

namespace CPCAPI2
{
class Phone;

namespace RcsProvision
{
class RcsProvisionHandler;
typedef unsigned int RcsProvisionHandle;

/**
* Encapsulates functionality for RCS provision setting parameters
*/
struct RcsProvisionSettings
{
   cpc::string http_url;
   cpc::string https_url;
   cpc::string username;
   cpc::string password;
   cpc::string IMSI;
   cpc::string rcs_version;
   cpc::vector<cpc::string> rcs_profiles;
   cpc::string client_vendor;
   cpc::string client_version;
   cpc::string terminal_vendor;
   cpc::string terminal_model;
   cpc::string terminal_sw_version;
   cpc::string IMEI;
   cpc::string friendly_device_name;
};

/**
* Encapsulates functionality for RCS provision (*******);
* typically you create only one %RcsProvision; get a reference to the interface 
* using the static method getInterface(). 
*
* To initiate a provision process, use requestForProvision(); the ensuing RcsProvisionHandler 
* event will pass a %RcsProvisionHandle.
*/
class CPCAPI2_SHAREDLIBRARY_API RcsProvisionManager
{
public:
   /**
   * Get a reference to the %RcsProvisionManager interface.
   */   
   static RcsProvisionManager* getInterface(Phone* cpcPhone);

   /**
   * Create a provision session.
   * @param params The provision parameters.
   */
   virtual RcsProvisionHandle create(const RcsProvisionSettings& settings) = 0;

   /**
   * Set the handler for the specified provision session.
   * Set the handler immediately after creating the provision session.
   * 
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as 
   * process(..)
   */
   virtual int setHandler(
      RcsProvisionHandle provision,
      RcsProvisionHandler* handler) = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode - Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode - In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(
      unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling RcsProvisionManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

   /**
   * Request for provision from the server. This is an asynchronous call.
   * @param provision The %Provision to use for delivery
   */
   virtual int requestForProvision(
      RcsProvisionHandle provision, bool forcedUpdate) = 0;

   /**
   * Create an account with a given provision session.
   * @param provision The %Provision to use for delivery
   */
   virtual SipAccount::SipAccountHandle createAccount(
      RcsProvisionHandle provision,
      const SipAccount::SipAccountSettings& sipAccountSettings) = 0;

   /**
   * Populate a RcsCapabilityDiscoverySettings with a given provision session.
   * @param provision The %Provision to use for delivery
   * @param settings The %RcsCapabilityDiscoverySettings to be populated
   */
   virtual int populateSettings(
      RcsProvisionHandle provision,
      RcsCapabilityDiscovery::RcsCapabilityDiscoverySettings& settings) = 0;

   /**
   * Populate a SipAccountSettings with a given provision session.
   * @param provision The %Provision to use for delivery
   * @param settings The %SipAccountSettings to be populated
   */
   virtual int populateSettings(
      RcsProvisionHandle provision,
      SipAccount::SipAccountSettings& settings) = 0;

   /**
   * Populate a RcsCapabilitySet with a given provision session.
   * @param provision The %Provision to use for delivery
   * @param settings The %RcsCapabilitySet to be populated
   */
   virtual int populateCapabilitySet(
      RcsProvisionHandle provision,
      RcsCapabilityDiscovery::RcsCapabilitySet& caps) = 0;

   /**
   * Load the associated profile from the specified path.
   * @param provision The %Provision to use for delivery
   * @param path The path of the profile to be loaded from
   */
   virtual int loadProfile(
      RcsProvisionHandle provision,
      const cpc::string& path) = 0;

   /**
   * Save the associated profile to the specified path.
   * @param provision The %Provision to use for delivery
   * @param path The path of the profile to be save as
   */
   virtual int saveProfile(
      RcsProvisionHandle provision,
      const cpc::string& path) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %RcsProvisionManager.
    */
   virtual ~RcsProvisionManager() {}
};

}
}

#endif // CPCAPI2_PROVISION_H
