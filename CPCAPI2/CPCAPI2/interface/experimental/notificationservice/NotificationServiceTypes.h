#pragma once
#ifndef __CPCAPI2_NOTIFICATIONSERVICETYPES_H__
#define __CPCAPI2_NOTIFICATIONSERVICETYPES_H__

#include "cpcstl/string.h"
#include <websocket/WebSocketTypes.h>

namespace CPCAPI2
{
   namespace Notification
   {
      typedef unsigned int ChannelHandle;

      /**
       * Generic service authentication / authorization info
       */
      typedef struct AuthInfo
      {
         cpc::string groupName;
         cpc::string userName;
         cpc::string password;
      } AuthInfo;

      /**
       * Currently known product codes (others could be specified by the
       * application in the future). Similar to a SKU
       */
      typedef cpc::string ProductCode;
      const static ProductCode ProductCode_BriaX( "BRIX" );
      const static ProductCode ProductCode_BriaXPro( "BRXP" );
      const static ProductCode ProductCode_BriaXLite( "BRXL" );

      /**
       * OS version and client build number. Used when a service wants the
       * SNS to forward notifications to e.g. only mobile devices less than
       * build version XYZ.
       */
      typedef struct BuildInfo
      {
         /**
          * The category of product
          */
         ProductCode productCode;

         /**
          * The license key/string used for this product.
          */
         cpc::string licenseKey;

         /**
          * The language setting of the build/OS (like "en_US" for example).
          */
         cpc::string locale;

         /**
          * (Mainly for Mobile) Unique string used to identify application
          * in the appstore/google play
          */
         cpc::string applicationID;

         /**
          * Default ctor
          */
         BuildInfo() : productCode( ProductCode_BriaX )  {}
      } BuildInfo;

      /**
       * A list of predefined event types (others can be used by the application).
       */
      typedef cpc::string EventType;
      const static EventType EventType_Unknown( "UNK" );
      const static EventType EventType_ServiceMessage( "CSM" );
      const static EventType EventType_Provisioning( "PROV" );

      /**
       * Denotes the type of push notification required for this subscription
       */
      typedef enum PushType
      {
         PushType_None = 0,                   // Desktop devices (no push notification required)
         PushType_ApplePushKit = 1,           // iOS devices (possibly OSX in the future)
         PushType_GoogleCloudMessaging = 2    // Android devices
      } PushType;

      /**
       * Structure containing information push notification information. Push
       * notifications will be used by the SNS to "wake up" the application, in
       * the case where an urgent event is sent, but the application does not
       * have an active websocket.
       *
       * The push notification will contain (at least) two different pieces of
       * information: A token which identifies the push notification as coming
       * from the SNS, and a second token which identifies the service that posted
       * the event. The format of the push notification is outside the scope of
       * this header file and will be described in the SNS server design document.
       *
       * We reserve the right to extend the push notification with additional
       * information beyond the two pieces of information described above.
       */
      typedef struct PushInfo
      {
         /**
          * (Required) the type of push notification (can be set to PushType_None).
          */
         PushType    pushType;

         /**
          * (Optional) the pushToken field needs to be set in the event that pushType
          * is set to something other than "PushType_None".
          */
         cpc::string pushToken;

         /**
          * Default ctor
          */
         PushInfo() : pushType( PushType_None ) {}
      } PushInfo;

      /**
       * See NotificationService header for full state machine diagram.
       */
      typedef enum ChannelState
      {
         ChannelState_Disconnected = 0, // obvs.
         ChannelState_Connecting = 1,   // trying to connect
         ChannelState_Connected = 2,    // succeeded connecting
         ChannelState_Failed = 3        // connection failed (will either retry or disconnect)
      } ChannelState;

      /**
       * Structure containing all relevant settings for the channel.
       * (subdivided into "info" blocks).
       */
      typedef struct ChannelSettings
      {
         AuthInfo    authInfo;
         BuildInfo   buildInfo;
         PushInfo    pushInfo;

         /**
          * Universally unique Device ID (required). This field must be
          * the same as the ID used to login to the Stretto Provisioning
          * service (the "uuid=" field)
          */
         cpc::string deviceUUID;

         /**
          * Common web-socket settings including location of the server,
          * TLS mode, and etc.
          */
         WebSocket::WebSocketSettings wsSettings;
      } ChannelSettings;

   }
}

#endif //  __CPCAPI2_NOTIFICATIONSERVICETYPES_H__
