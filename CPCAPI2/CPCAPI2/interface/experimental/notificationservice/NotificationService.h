#pragma once

#ifndef CPCAPI2_NOTIFICATIONSERVICE_H
#define CPCAPI2_NOTIFICATIONSERVICE_H

#include "cpcapi2defs.h"
#include "NotificationServiceTypes.h"
#include "NotificationHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace Notification
   {
      /**
       * The NotificationService is used to create and manage notification
       * channels. The Notification channels are used to receive different
       * types of events. Each notification channel follows the same state
       * machine with regards to its connectivity:
       *
       *  +----------------------------------------------------------------------------------+
       *  |                                                                                  |
       *  |                           disconnect                                             |
       *  | disconnect +--------------------------------------------------------+            |
       *  |            v                                                        |            |
       *  |          +--------------+  connect      +------------+  success   +-----------+  |
       *  +--------> | Disconnected | ------------> |            | ---------> | Connected |  |
       *             +--------------+               |            |            +-----------+  |
       *               ^              disconnect    |            |              |            |
       *               +--------------------------- | Connecting |              | failure    |
       *                                            |            |              v            |
       *                                            |            |  failure   +-----------+  |
       *                                            |            | ---------> |  Failed   | -+
       *                                            +------------+            +-----------+
       *                                              ^            timer        |
       *                                              +-------------------------+
       *
       * Above diagram generated using Graph::Easy CPAN Perl module with the
       * following input:
       *
       * [ Disconnected ] -- connect --> [ Connecting ]
       * [ Connecting ] -- success --> [ Connected ]
       * [ Connecting ] -- failure --> [ Failed ]
       * [ Connecting ] -- disconnect --> [ Disconnected ]
       * [ Connected ] -- failure --> [ Failed ]
       * [ Connected ] -- disconnect --> [ Disconnected ]
       * [ Failed ] -- timer --> [ Connecting ]
       * [ Failed ] -- disconnect --> [ Disconnected ]
       */
      class CPCAPI2_SHAREDLIBRARY_API NotificationService
      {
      public:
         /**
          * Get a reference to the NotificationService interface.
          */
         static NotificationService* getInterface(Phone* cpcPhone);

         /**
          * Creates a new notification channel which will be used for
          * receiving notifications from the Stretto Notification Service
          */
         virtual ChannelHandle createChannel( void ) = 0;

         /**
          * Configure the default channel settings.
          * Call configureDefaultAccountSettings() and then applySettings().
          *
          * @param channel the channel handle.
          * @param settings the structure that holds 
          *        configuration information for the notification service.
          */
         virtual int configureChannelSettings(
            const ChannelHandle& channel,
            const ChannelSettings& settings ) = 0;

         /**
          * Apply the channel settings configured with
          * configureDefaultAccountSettings()
          *
          * @param channel the channel handle
          */
         virtual int applySettings( const ChannelHandle& channel ) = 0;

         /**
          * Connecting a channel will also result in a subscription to the
          * event type, assuming the connection is successful.
          */
         virtual int connect( const ChannelHandle& channel ) = 0;

         /**
          * Disconnecting the channel will also result in an unsubscribe to the
          * event, assuming it was previously connected.
          */
         virtual int disconnect( const ChannelHandle& channel ) = 0;

         /**
          * Deletes a channel, uses the handle obtained from create. The SDK
          * will not disconnect a channel automatically, so call destroy only
          * once the channel has been fully disconnected (transitioned to
          * the Disconnected state).
          */
         virtual int destroy( const ChannelHandle& channel ) = 0;

         /**
          * Set the handler for events on the specified channel. Set the
          * handler immediately after calling NotificationService::create()
          * and NotificationService::applySettings(..)
          *
          * To un-register the handler, pass NULL for handler. Must be
          * called on the same thread as process(..).
          *
          * NB: Calling setHandler with NULL as the handler will result in
          * the calling thread being blocked until the handler has been
          * removed from the system.
          */
         virtual int setHandler( const ChannelHandle& channel,
            NotificationHandler* handler) = 0;

         /**
          * The blocking modes for the process() function. See that function
          * for details.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Allows the application code to receive callback notifications
          * from the SDK.
          *
          * These callbacks will happen synchronously, in the same thread of
          * execution as that in which %process() is invoked.  Depending on the
          * application threading model, process() can be used in two different
          * ways:
          * <ol>
          * <li>blocking mode ?Typically in this mode, %process() is called by
          * the application from a background (worker) thread.  The call to
          * process() blocks until a callback function needs to be invoked.
          * <li>non-blocking mode ?In this mode, %process() is called by the
          * application from the main (GUI) thread of the application,
          * typically from the main message/event loop.  In this mode,
          * %process() returns immediately and so must be called frequently
          * enough that the application can receive its callback notifications
          * in a timely manner.
          *
          * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
          *                representing the time the call to process(..) will block waiting for a callback
          *                from the SDK
          * </ol>
          */
         virtual int process(unsigned int timeout) = 0;

         /**
          * Posts a functor to the SDK callback queue; this allows the
          * application to "unblock" the thread calling
          * NotificationService::process(..) if it is blocked waiting for an
          * SDK callback.  Unblocking the process() thread is useful at
          * SDK/application shutdown in certain applications.
          */
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %NotificationService.
          */
         virtual ~NotificationService() {}
      };
   }
}
#endif // CPCAPI2_NOTIFICATIONSERVICE_H
