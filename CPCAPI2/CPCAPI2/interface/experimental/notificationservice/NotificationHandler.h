#pragma once

#ifndef CPCAPI2_NOTIFICATIONHANDLER_H
#define CPCAPI2_NOTIFICATIONHANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "NotificationServiceTypes.h"
#include <stdint.h>

namespace CPCAPI2
{
   class Phone;

   namespace Notification
   {
      /**
       * Informs the application about connection state changes occurring in
       * the channel.
       */
      typedef struct ChannelStateChangedEvent
      {
         ChannelState oldState;
         ChannelState newState;
      } ChannelStateChangedEvent;

      /**
       * Event containing the notification document.
       */
      typedef struct NotificationEvent
      {
         EventType   eventType;
         cpc::string eventData;       // body of the event (content)
         int64_t     eventDateMillis; // millis since epoch
      } NotificationEvent;

      /**
       * Event passed in NotificationHandler::onError().
       */
      typedef struct ErrorEvent
      {
         int errNum; // as defined in errno.h
         cpc::string errorText;

         ErrorEvent() : errNum( 0 ) {} // default ctor
      } ErrorEvent;

      /**
       * The handler for events on the Notification Service
       */
      class NotificationHandler
      {
      public:
         virtual int onChannelStateChanged( const ChannelHandle& channel, const ChannelStateChangedEvent& event ) = 0;
         virtual int onNotification( const ChannelHandle& channel, const NotificationEvent& event ) = 0;
         virtual int onError( const ChannelHandle& channel, const ErrorEvent& event ) = 0;
      };
   }
}
#endif // CPCAPI2_NOTIFICATIONHANDLER_H
