#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_SERVER_H)
#define CPCAPI2_REMOTE_CONTROL_SERVER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace RemoteControl
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API RemoteControlServer
{
public:
   /**
   * Get a reference to the %RemoteControlServer interface.
   */   
   static RemoteControlServer* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kRemoteControlModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling RemoteControlServer::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual int start() = 0;
   virtual int shutdown() = 0;
   virtual int processRemoteRequest(const cpc::string& request) = 0;
};

}
}

#endif // CPCAPI2_REMOTE_CONTROL_SERVER_H
