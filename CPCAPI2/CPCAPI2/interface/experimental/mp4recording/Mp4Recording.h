#pragma once

#if !defined(CPCAPI2_MP4_RECORDING_H)
#define CPCAPI2_MP4_RECORDING_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace Mp4Recording
{
typedef unsigned int Mp4RecordingHandle;

struct Mp4RecordingSettings
{
   Mp4RecordingSettings() : videoReceiveChannel(-1), videoSendChannel(-1), audioReceiveChannel(-1), audioSendChannel(-1) {}
   int videoReceiveChannel;
   int videoSendChannel;
   int audioReceiveChannel;
   int audioSendChannel;
   cpc::string filenameUtf8;
};

class Mp4RecordingHandler
{
public:
   virtual ~Mp4RecordingHandler() {}

};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API Mp4RecordingManager
{
public:
   /**
   * Get a reference to the %Mp4RecordingManager interface.
   */   
   static Mp4RecordingManager* getInterface(Phone* cpcPhone);

   virtual Mp4RecordingHandle createRecordingSession() = 0;
   virtual int setMp4RecordingSettings(Mp4RecordingHandle videoStream, const Mp4RecordingSettings& settings) = 0;
   virtual int setMp4RecordingHandler(Mp4RecordingHandle videoStream, Mp4RecordingHandler* handler) = 0;
   virtual int startRecording(Mp4RecordingHandle videoStream) = 0;
   virtual int stopRecording(Mp4RecordingHandle videoStream) = 0;

};

}
}

#endif // CPCAPI2_MP4_RECORDING_H
