#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "../experimental/push_endpoint/PushNotificationEndpointTypes.h"

namespace CPCAPI2
{
class Phone;

namespace PushService
{
class PushNotificationServiceManager;
}

namespace PushEndpoint
{
class PushNotificationEndpointHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API PushNotificationEndpointManager
{
public:
   /**
   * Get a reference to the %PushNotificationEndpointManager interface.
   */   
   static PushNotificationEndpointManager* getInterface(Phone* cpcPhone);
   static const cpc::string& getServiceId();

   virtual int setHandler(PushNotificationEndpointHandle device, PushNotificationEndpointHandler* handler) = 0;

   virtual PushNotificationEndpointHandle createPushNotificationEndpoint() = 0;
   virtual int registerForPushNotifications(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo) = 0;
   virtual int unregisterForPushNotifications(PushNotificationEndpointHandle device) = 0;

   virtual int setPushNotificationService(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr) = 0;

   /**
    * Query response is received in the onPushRegistrationQueryListResult event.
    */
   virtual int queryPushRegistrationList() { return 0; };

   /**
    * Query response is received in the onPushRegistrationQueryInfoResult event.
   */
   virtual int queryPushRegistrationInfo(PushNotificationEndpointHandle device) { return 0; };

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kPushNotificationModuleDisabled = -1;

   /**
      * Allows the application code to receive callback notifications from
      * the SDK.
      *
      * These callbacks will happen synchronously, in the same thread of
      * execution as that in which %process() is invoked.  Depending on the
      * application threading model, process() can be used in two different
      * ways:
      * <ol>
      * <li>blocking mode ?Typically in this mode, %process() is called by the 
      * application from a background (worker) thread.  The call to process() 
      * blocks until a callback function needs to be invoked.
      * <li>non-blocking mode ?In this mode, %process() is called by the application 
      * from the main (GUI) thread of the application, typically from the 
      * main message/event loop.  In this mode, %process() returns immediately 
      * and so must be called frequently enough that the application can receive 
      * its callback notifications in a timely manner.
      *
      * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
      *                representing the time the call to process(..) will block waiting for a callback
      *                from the SDK
      *</ol>
      */
   virtual int process(unsigned int timeout) = 0;

   /**
      * Allows the application to "unblock" the thread calling
      * PushNotificationManager::process(..) if it is blocked waiting for
      * an SDK callback.  Unblocking the process() thread is useful at
      * SDK/application shutdown in certain applications.
      */
   virtual void interruptProcess() = 0;
};
}
}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_H
