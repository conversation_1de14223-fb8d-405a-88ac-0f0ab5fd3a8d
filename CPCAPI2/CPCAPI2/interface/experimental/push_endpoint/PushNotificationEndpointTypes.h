#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_TYPES_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_TYPES_H

#include "cpcapi2defs.h"

#include "../experimental/push_endpoint/PushNotificationCommonTypes.h"

#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace PushEndpoint
{
typedef unsigned int PushNotificationEndpointHandle;

}
}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_TYPES_H
