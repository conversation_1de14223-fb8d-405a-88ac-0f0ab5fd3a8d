#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_HANDLER_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "jsonapi/JsonApiTypes.h"
#include "push_endpoint/PushNotificationEndpointTypes.h"

namespace CPCAPI2
{
namespace PushEndpoint
{
/**
   * The format of the json document provided for the direct push
   * notifications will follow the format outlined for google FCM (I had to
   * pick a standard, so that one made sense).
   *
   * @see https://firebase.google.com/docs/cloud-messaging/http-server-ref
   */
struct PushNotificationEvent
{
   cpc::string jsonDocument;
};

struct PushRegistrationSuccessEvent
{
   PushNotificationEndpointId endpointId;
};

struct PushRegistrationFailureEvent
{
   cpc::string errorText;
};

struct PushRegistrationQueryListResult
{
   cpc::vector<CPCAPI2::PushEndpoint::PushNotificationEndpointHandle> registrationList;
};

struct PushRegistrationQueryInfoResult
{
   CPCAPI2::PushEndpoint::PushNotificationEndpointHandle pushEndpointHandle;
   CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle;
   CPCAPI2::PushEndpoint::PushNotificationEndpointId pushEndpointId;
   CPCAPI2::PushEndpoint::PushNetworkType pushNetworkType;
   cpc::string apnsTopic;
   cpc::string deviceToken;

   PushRegistrationQueryInfoResult() { reset(); }

   void reset()
   {
      pushEndpointHandle = 0;
      pushEndpointId = 0;
      pushNetworkType = PushNetworkType_Unknown;
      apnsTopic = "";
      deviceToken = "";
   }
};

/**
 * The "client" handler is used primarily for sending a push notification
 * directly to the client (via websocket or conceivably some other
 * mechanism in the future). If an application sets the client handler,
 * it will be notified directly via this mechanism over the jsonAPI
 * channel. This is meant primarily for platforms which cannot or will
 * not support the usual push networks, however in theory it is not
 * limited only to those platforms.
 *
 * IMPORTANT: This type of "push" notification will not wake up any
 * application which is suspended on mobile devices, unlike APN and FCM.
*/
class PushNotificationEndpointHandler
{

public:

   /**
    * A "push" notification is sent directly to the client from the agent.
    *
    * @param deviceToken the registered device token for the client. This
    *        should not be a surprise to the application as it is the same
    *        token provided by the client.
    * @param evt the PushNotificationEvent which contains the json document.
    *        This is provided as a json document in order to mirror the
    *        behavior of the other push network types.
   */
   virtual int onPushNotification(
      PushNotificationEndpointHandle h,
      const PushNotificationEvent& evt ) = 0;

   virtual int onPushRegistrationSuccess(
      PushNotificationEndpointHandle h,
      const PushRegistrationSuccessEvent& evt) = 0;

   virtual int onPushRegistrationFailure(
      PushNotificationEndpointHandle h,
      const PushRegistrationFailureEvent& evt) = 0;

   virtual int onPushRegistrationQueryListResult(
      const CPCAPI2::PushEndpoint::PushRegistrationQueryListResult& args) { return 0; };

   virtual int onPushRegistrationQueryInfoResult(
      CPCAPI2::PushEndpoint::PushNotificationEndpointHandle h,
      const CPCAPI2::PushEndpoint::PushRegistrationQueryInfoResult& args) { return 0; };

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_HANDLER_H
