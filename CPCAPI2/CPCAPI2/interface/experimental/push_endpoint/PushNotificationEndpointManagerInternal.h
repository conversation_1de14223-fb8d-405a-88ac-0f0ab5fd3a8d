#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_INTERNAL_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "../experimental/push_endpoint/PushNotificationEndpointManager.h"

namespace CPCAPI2
{

namespace PushEndpoint
{

   class CPCAPI2_SHAREDLIBRARY_API PushNotificationEndpointManagerInternal : public PushNotificationEndpointManager
   {

   public:

      static PushNotificationEndpointManagerInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setEventCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual void addEventObserver(PushNotificationEndpointHandler* observer) {};
      virtual void removeEventObserver(PushNotificationEndpointHandler* observer) {};

   };  

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_MANAGER_INTERNAL_H
