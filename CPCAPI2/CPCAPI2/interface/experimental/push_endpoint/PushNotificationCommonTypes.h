#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_COMMON_TYPES_H)
#define CPCAPI2_PUSH_NOTIFICATION_COMMON_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{

namespace PushEndpoint
{

typedef cpc::string PushNotificationEndpointId;

enum PushNetworkType
{
   PushNetworkType_Unknown = 0,
   PushNetworkType_Apple   = 10,
   PushNetworkType_FCM     = 20,  // Firebase
   PushNetworkType_WS      = 40   // Websocket
};

struct PushNotificationRegistrationInfo
{
   // Optional -- if provided, updates the push registration
   // info for an endpoint that is already registered with this ID
   PushNotificationEndpointId pushEndpointId;

   PushNetworkType pushNetworkType;

   // Unique identifier for a device (same concept exists on each push
   // network type presently).
   cpc::string deviceToken;

   // Apple (APN) specific info
   struct APNInfo
   {
      cpc::string apnsTopic;
      cpc::string apnsTopicPushKit;
      cpc::string pushKitToken;
      bool useSandbox;
   } apnInfo;

   // Firebase (FCM) specific info
   struct FCMInfo
   {
      // placeholder
   } fcmInfo;

   struct WSInfo
   {
      CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle;
   } websocketInfo;

   PushNotificationRegistrationInfo() :
   pushNetworkType(PushNetworkType_Unknown)
   {
   }
};

struct PushDatabaseSettings
{
   PushDatabaseSettings() : redisIp(""), redisPort(6379)
   {
   }

   cpc::string redisIp;
   int redisPort;
};

cpc::string get_debug_string(const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& args);
cpc::string get_debug_string(const CPCAPI2::PushEndpoint::PushDatabaseSettings& args);

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_COMMON_TYPES_H
