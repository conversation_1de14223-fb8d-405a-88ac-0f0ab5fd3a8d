#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_API_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace PushEndpoint
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API PushNotificationEndpointJsonApi
{
public:
   /**
   * Get a reference to the %PushNotificationEndpointJsonApi interface.
   */
   static PushNotificationEndpointJsonApi* getInterface(Phone* cpcPhone);
};
}
}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_API_H
