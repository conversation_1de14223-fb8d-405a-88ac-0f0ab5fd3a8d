#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "../experimental/push_endpoint/PushNotificationEndpointManager.h"

namespace CPCAPI2
{
class Phone;
namespace PushEndpoint
{
class CPCAPI2_SHAREDLIBRARY_API PushNotificationEndpointManagerJsonProxy : public CPCAPI2::PushEndpoint::PushNotificationEndpointManager
{
public:
   /**
   * Get a reference to the PushNotificationEndpointManagerJsonProxy interface.
   */
   static PushNotificationEndpointManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %PushNotificationEndpointManagerJsonProxy.
    */
   virtual ~PushNotificationEndpointManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_PROXY_H
