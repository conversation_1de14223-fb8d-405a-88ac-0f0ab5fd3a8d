#pragma once

#if !defined(CPCAPI2_VIDEO_JSON_API_H)
#define CPCAPI2_VIDEO_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace Media
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API VideoJsonApi
{
public:
   /**
   * Get a reference to the %VideoJsonApi interface.
   */
   static VideoJsonApi* getInterface(Phone* cpcPhone);

protected:
   /*
   * The SDK will manage memory life of %VideoJsonApi.
   */
   virtual ~VideoJsonApi() {}
};

}
}

#endif // CPCAPI2_VIDEO_JSON_API_H
