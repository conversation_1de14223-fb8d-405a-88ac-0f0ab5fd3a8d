#pragma once

#if !defined(CPCAPI2_MEDIA_MANAGER_INTERNAL_H)
#define CPCAPI2_MEDIA_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
class Phone;
namespace Media
{
   class CPCAPI2_SHAREDLIBRARY_API MediaTransportsReactorFactory
   {
   public:
      static MediaTransportsReactorFactory* create();

	   virtual ~MediaTransportsReactorFactory() {}

      virtual void initialize() = 0;
      virtual void initializeSingle() = 0;
      virtual void shutdown() = 0;
      virtual void addRef() = 0;
      virtual void releaseRef() = 0;
   };
   
   /**
   */
   class CPCAPI2_SHAREDLIBRARY_API MediaManagerInternal
   {
   public:
      static MediaManagerInternal* getInterface(Phone* cpcPhone, MediaTransportsReactorFactory* mediaReactorFactory);
	  
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

      virtual ~MediaManagerInternal() {}
   };

}
}

#endif // CPCAPI2_MEDIA_MANAGER_INTERNAL_H
