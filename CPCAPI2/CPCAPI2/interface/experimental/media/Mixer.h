#pragma once

#if !defined(CPCAPI2_MIXER_H)
#define CPCAPI2_MIXER_H

#include "cpcapi2defs.h"
#include "media/audio/AudioHandler.h"

namespace CPCAPI2
{
namespace Media
{

struct MixerSettings
{
   EchoCancellationMode perChannelAecMode = CPCAPI2::Media::EchoCancellationMode::EchoCancellationMode_None;
   NoiseSuppressionMode perChannelNsMode = CPCAPI2::Media::NoiseSuppressionMode::NoiseSuppressionMode_None;
};

} // namespace Media
} // namespace CPCAPI2
#endif // CPCAPI2_MIXER_H
