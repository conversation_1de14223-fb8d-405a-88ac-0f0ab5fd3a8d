#pragma once

#if !defined(CPCAPI2_AUDIO_JSON_API_H)
#define CPCAPI2_AUDIO_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace Media
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API AudioJsonApi
{
public:
   /**
   * Get a reference to the %AudioJsonApi interface.
   */
   static AudioJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_AUDIO_JSON_API_H
