#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_SERVICE_HANDLER_H)
#define CPCAPI2_PUSH_NOTIFICATION_SERVICE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "push_service/PushNotificationServiceTypes.h"

namespace CPCAPI2
{

namespace PushService
{

enum StatusCode
{
   StatusCode_Success,                        /** 200 - Success */
   StatusCode_BadRequest,                     /** 400 - Bad Request */
   StatusCode_AuthenticationError,            /** 403 - There was an error with the certificate or with the provider authentication token */
   StatusCode_BadMethod,                      /** 405 - The request used a bad :method value. Only POST requests are supported */
   StatusCode_ExpiredDeviceToken,             /** 410 - The device token is no longer active for the topic */
   StatusCode_OversizedPayload,               /** 413 - The notification payload was too large */
   StatusCode_TooManyRequests,                /** 429 - The server received too many requests for the same device token */
   StatusCode_InternalServerError,            /** 500 - Internal server error */
   StatusCode_ServerUnavailable,              /** 503 - The server is shutting down and unavailable */
   StatusCode_Unknown
};

enum ReasonCode
{
   ReasonCode_BadCollapseId,                    /** 400 - The collapse identifier exceeds the maximum allowed size */
   ReasonCode_BadDeviceToken,                   /** 400 - The specified device token was bad, verify that the request contains a valid token and that the token matches the environment */
   ReasonCode_BadExpirationDate,                /** 400 - The apns-expiration value is bad */
   ReasonCode_BadMessageId,                     /** 400 - The apns-id value is bad */
   ReasonCode_BadPriority,                      /** 400 - The apns-priority value is bad */
   ReasonCode_BadTopic,                         /** 400 - The apns-topic was invalid */
   ReasonCode_DeviceTokenNotForTopic,           /** 400 - The device token does not match the specified topic */
   ReasonCode_DuplicateHeaders,                 /** 400 - One or more headers were repeated */
   ReasonCode_IdleTimeout,                      /** 400 - Idle time out */
   ReasonCode_MissingDeviceToken,               /** 400 - The device token is not specified in the request :path, verify that the :path header contains the device token */
   ReasonCode_MissingTopic,                     /** 400 - The apns-topic header of the request was not specified, the header is mandatory when the client is connected using a certificate that supports multiple topics */
   ReasonCode_PayloadEmpty,                     /** 400 - The message payload was empty */
   ReasonCode_TopicDisallowed,                  /** 400 - Pushing to this topic is not allowed */
   ReasonCode_BadCertificate,                   /** 403 - The certificate was bad */
   ReasonCode_BadCertificateEnvironment,        /** 403 - The client certificate was for the wrong environment */
   ReasonCode_ExpiredProviderToken,             /** 403 - The provider token is stale and a new token should be generated */
   ReasonCode_Forbidden,                        /** 403 - The specified action is not allowed */
   ReasonCode_InvalidProviderToken,             /** 403 - The provider token is not valid or the token signature could not be verified */
   ReasonCode_MissingProviderToken,             /** 403 - No provider certificate was used to connect to the push server and the authorization header was missing or no provider token was specified */
   ReasonCode_BadPath,                          /** 404 - The request contained a bad :path value */
   ReasonCode_MethodNotAllowed,                 /** 405 - The specified :method was not POST */
   ReasonCode_Unregistered,                     /** 410 - The device token is inactive for the specified topic */
   ReasonCode_PayloadTooLarge,                  /** 413 - The message payload was too large */
   ReasonCode_TooManyProviderTokenUpdates,      /** 429 - The provider token is being updated too often */
   ReasonCode_TooManyRequests,                  /** 429 - Too many requests were made consecutively to the same device token */
   ReasonCode_InternalServerError,              /** 500 - An internal server error occurred */
   ReasonCode_ServiceUnavailable,               /** 503 - The service is unavailable */
   ReasonCode_Shutdown,                         /** 503 - The server is shutting down */
   ReasonCode_Unknown
};

struct NotificationSuccessEvent
{
   CPCAPI2::PushEndpoint::PushNotificationEndpointId device;
};

struct NotificationFailureEvent
{
   CPCAPI2::PushEndpoint::PushNotificationEndpointId device;
   CPCAPI2::PushService::StatusCode statusCode;
   CPCAPI2::PushService::ReasonCode reasonCode;
   cpc::string errorString;
};

struct NotificationServiceRegistrationEvent
{
   CPCAPI2::PushEndpoint::PushNotificationEndpointId device;
   CPCAPI2::PushService::PushNotificationServiceHandle service;
   bool success;
};

/**
*/
class PushNotificationServiceHandler
{

public:

   /**
    * Posted by the SDK when the notification was successfully sent
   */
   virtual int onNotificationSuccess(PushNotificationServiceHandle h, const NotificationSuccessEvent& evt) = 0;
   virtual int onNotificationFailure(PushNotificationServiceHandle h, const NotificationFailureEvent& evt) = 0;
   virtual int onNotificationServiceRegistration(PushNotificationServiceHandle h, const NotificationServiceRegistrationEvent& evt) = 0;

};

cpc::string get_debug_string(const CPCAPI2::PushService::StatusCode& status);
cpc::string get_debug_string(const CPCAPI2::PushService::ReasonCode& reason);
cpc::string get_debug_string(const CPCAPI2::PushService::NotificationSuccessEvent& event);
cpc::string get_debug_string(const CPCAPI2::PushService::NotificationFailureEvent& event);
cpc::string get_debug_string(const CPCAPI2::PushService::NotificationServiceRegistrationEvent& event);
int get_notification_status_code(const CPCAPI2::PushService::StatusCode status);
CPCAPI2::PushService::StatusCode get_notification_status(int status);
int get_notification_status_code(const CPCAPI2::PushService::ReasonCode reason);
CPCAPI2::PushService::ReasonCode get_notification_reason(cpc::string reason);

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_SERVICE_HANDLER_H
