#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_SERVICE_TYPES_H)
#define CPCAPI2_PUSH_NOTIFICATION_SERVICE_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

#include "push_endpoint/PushNotificationCommonTypes.h"

#include "cpcstl/vector.h"

namespace CPCAPI2
{
   namespace PushService
   {
      typedef unsigned int PushNotificationServiceHandle;

      struct PushProviderSettings
      {
         CPCAPI2::PushEndpoint::PushNetworkType pushNetworkType;

         typedef struct APNSettings
         {
            cpc::string apnUrl;
            cpc::string apnKey;
            cpc::string p8file;
            cpc::string authKeyId;
            cpc::string teamId;
            bool pushKitEnabled;

            APNSettings() : apnUrl(""), apn<PERSON>ey(""), p8file(""), authKeyId(""), teamId(""), pushKitEnabled(false) {}
         } APNSettings;

         // Apple (APN) specific settings
         // NB: This is generally the "production" APN.
         APNSettings apnSettings;

         // Apple (APN) specific settings
         // This is for the "sandbox" APN which has different URL, key, token,
         // etc. The second structure is needed because the server needs the
         // ability to switch between them in real-time.
         APNSettings apnSandboxSettings;

         // Firebase (FCM) specific settings
         struct FCMSettings
         {
            cpc::string fcmUrl;
            cpc::string fcmKey;
         } fcmSettings;
      };

      // This structure is used to represent custom data passed to either APN
      // or FCM.  Both of these services use a list of typed key-value pairs as
      // a section in their document.
      enum CustomDataType
      {
         CustomDataType_bool,
         CustomDataType_int,
         CustomDataType_double,
         CustomDataType_string,
         CustomDataType_uint
      };

      /**
       * Wrapper struct which could be one of several types (similar to union)
       */
      struct CustomDataField
      {
         /**
         * The "name" portion of the name/value pair which this custom data
         * field represents
         */
         cpc::string    name;

         /**
         * The "value" portion could be one of several types as indicated by
         * the dataType field and its selected value
         */
         CustomDataType dataType; // type of value
         bool           boolVal;
         int64_t        intVal;
         double         doubleVal;
         cpc::string    stringVal;

         // basic ctors to aid in initialization
         CustomDataField(void) {}
         CustomDataField(const char *name, const bool& val) : name(name), dataType(CustomDataType_bool), boolVal(val) {}
         CustomDataField(const char *name, const int64_t& val) : name(name), dataType(CustomDataType_int), intVal(val) {}
         CustomDataField(const char *name, const int32_t& val) : name(name), dataType(CustomDataType_int), intVal(val) {}
         CustomDataField(const char *name, const uint64_t& val) : name(name), dataType(CustomDataType_uint), intVal(static_cast<int64_t>(val)) {}
         CustomDataField(const char *name, const uint32_t& val) : name(name), dataType(CustomDataType_uint), intVal(static_cast<int64_t>(val)) {}
         CustomDataField(const char *name, const uint16_t& val) : name(name), dataType(CustomDataType_uint), intVal(static_cast<int64_t>(val)) {}
         CustomDataField(const char *name, const double& val) : name(name), dataType(CustomDataType_double), doubleVal(val) {}
         CustomDataField(const char *name, const cpc::string& val) : name(name), dataType(CustomDataType_string), stringVal(val) {}
         CustomDataField(const char *name, const char* val) : name(name), dataType(CustomDataType_string), stringVal(val) {}
      };

      /**
       * A list of custom data fields, constitutes custom data
       */
      typedef cpc::vector<CustomDataField> CustomData;

      enum PushNotificationOption
      {
         /**
          * If supported by the push network type, this push notification
          * requests ability to be responded to by the user with an in-line reply;
          * e.g. ability to reply from the lock screen
          */
         PushNotificationOption_InlineReply = 0,

         /**
          * To the extent that the push provider has such a configuration,
          * try to send the push request via a sandbox server (this option
          * is not supported by all providers)
          */
         PushNotificationOption_Sandbox
      };

      /**
      * Structure which is generic to all supported push network types.  Fields
      * will be added to this structure on an as-needed basis.
      */
      struct PushNotificationRequest
      {
         /**
          * Localization string/keys for the title of the notification.
          */
         cpc::string title_loc_key;

         /**
          * Localization substitutions for the title.
          */
         cpc::vector< cpc::string > title_loc_args;

         /**
          * The notification text.
          * For an empty (invisible) message, leave this field blank.
          */
         cpc::string body;

         /**
          * List of key/value pairs which should be included in the
          * push notification into an extended data section.
          */
         CustomData customData;

         /**
          * List of options to apply to this push notification. Options are
          * interpreted by the SDK into appropriate (if any) options in the
          * push network specific payload.
          *
          * Note cpc::unordered_map does not appear to support enums for keys,
          * or that would have been used here
          */
         cpc::vector<PushNotificationOption> options;

         struct APNInfo
         {
            bool usePushKit;

            APNInfo() : usePushKit(false) {}
         } apnInfo;

         // Firebase (FCM) specific info
         struct FCMInfo
         {
            // placeholder
         } fcmInfo;

         struct WSInfo
         {
            // placeholder
         } wsInfo;
      };
   }
}

#endif // CPCAPI2_PUSH_NOTIFICATION_SERVICE_TYPES_H
