#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_SERVICE_MANAGER_INTERNAL_H)
#define CPCAPI2_PUSH_NOTIFICATION_SERVICE_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"
#include "PushNotificationServiceManager.h"

namespace CPCAPI2
{

namespace PushService
{

class CPCAPI2_SHAREDLIBRARY_API PushNotificationServiceManagerInternal : public PushNotificationServiceManager
{

public:

   static PushNotificationServiceManagerInternal* getInternalInterface(CPCAPI2::Phone* cpcPhone);
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
   virtual void addEventObserver(PushNotificationServiceHandler* handler) = 0;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_SERVICE_MANAGER_INTERNAL_H
