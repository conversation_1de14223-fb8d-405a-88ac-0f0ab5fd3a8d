#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_INTERNAL_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_INTERNAL_H

#include "cpcapi2defs.h"
#include "ConferenceConnector.h"

namespace CPCAPI2
{
namespace ConferenceConnector
{
   class CPCAPI2_SHAREDLIBRARY_API ConferenceConnectorInternal : public ConferenceConnectorManager
   {
   public:
      static ConferenceConnectorInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;
      virtual int dropIncomingJsonMessages(CloudConferenceSessionHandle session, bool enable) = 0;
      virtual int queryMediaStatistics(CloudConferenceSessionHandle session) = 0;
      virtual int setMediaInactivityMonitor(CloudConferenceSessionHandle session, bool enableMediaInactivityMonitor, int mediaInactivityIntervalMsecs) = 0;
   };
}
}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_INTERNAL_H
