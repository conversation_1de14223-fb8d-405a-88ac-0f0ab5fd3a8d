#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_JSON_API_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace ConferenceConnector
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API ConferenceConnectorJsonApi
{
public:
   /**
   * Get a reference to the %ConferenceConnectorJsonApi interface.
   */
   static ConferenceConnectorJsonApi* getInterface(Phone* cpcPhone);
};
}
}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_JSON_API_H
