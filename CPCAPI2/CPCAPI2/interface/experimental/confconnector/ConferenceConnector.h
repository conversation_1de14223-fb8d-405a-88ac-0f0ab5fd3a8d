#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_H

#include "cpcapi2defs.h"
#include "ConferenceConnectorTypes.h"

namespace CPCAPI2
{
class Phone;
namespace ConferenceConnector
{
class CPCAPI2_SHAREDLIBRARY_API ConferenceConnectorManager
{
public:
   /**
   * Get a reference to the %ConferenceConnectorManager interface.
   */   
   static ConferenceConnectorManager* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kConferenceConnectorModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling ConferenceConnectorManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};
   
   /**
   * Allocates a handle to control a client's interactions with a set of server-hosted
   * back-end services that work together as a system (i.e. a "cloud").
   */
   virtual ConferenceConnectorHandle createConferenceConnector() = 0;

   virtual int setHandler(ConferenceConnectorHandle conn, ConferenceConnectorHandler* handler) = 0;

   virtual int destroyConferenceConnector(ConferenceConnectorHandle conn) = 0;

   /**
    * Sets connection settings; required before any cloud-based services can be requested/accessed.
    */
   virtual int setConnectionSettings(ConferenceConnectorHandle conn, const ConferenceConnectorSettings& settings) = 0;

   /**
    * Connect to the back-end services requested by prior calls to requestService(..).
    * The ConferenceConnectorManager follows this sequence:
    * 1) request an auth token from the authentication server (per username/password and URL provided in setConnectionSettings(..))
    * 2) use the auth token to request back-end service URLs from the orchestration server
    * 3) connect to the URLs provided by the orchestration server
    * 
    * Progress is reported via the ConferenceConnectorHandler::onServiceConnectionStatusChanged(..) callback function.
    */
   virtual int connectToConferenceService(ConferenceConnectorHandle conn) = 0;
   virtual int disconnectFromConferenceService(ConferenceConnectorHandle conn) = 0;

   virtual int queryConferenceList(ConferenceConnectorHandle conn) = 0;

   virtual int createNewConference(ConferenceConnectorHandle conn, const CloudConferenceSettings& settings) = 0;

   virtual int queryParticipantList(CloudConferenceHandle conference) = 0;

   virtual int destroyConference(CloudConferenceHandle conference) = 0;

   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference) = 0;
   virtual CloudConferenceSessionHandle createConferenceSession(CloudConferenceHandle conference, const CloudConferenceSessionSettings& settings) = 0;
   virtual int setSessionSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionSettings& settings) = 0;
   virtual int setSessionMediaSettings(CloudConferenceSessionHandle session, const CloudConferenceSessionMediaSettings& settings) = 0;
   virtual int startSession(CloudConferenceSessionHandle session) = 0;
   virtual int endSession(CloudConferenceSessionHandle session) = 0;
   virtual int updateSessionMedia(CloudConferenceSessionHandle session) = 0;
   virtual int setParticipantPermissions(CloudConferenceSessionHandle session, CloudConferenceParticipantHandle participant, const ConferenceSessionPermissions& permissions) = 0;
   virtual int pauseSessionPlayout(CloudConferenceSessionHandle session) = 0;
   virtual int resumeSessionPlayout(CloudConferenceSessionHandle session) = 0;

};

}

}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_H
