#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_TYPES_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "media/video/Video.h"

#define CONF_CONNECTOR_AUTH_TIMEOUT_SECONDS 5
#define CONF_CONNECTOR_ORCH_TIMEOUT_SECONDS 15

namespace CPCAPI2
{
namespace ConferenceConnector
{
typedef unsigned int ConferenceConnectorHandle;
typedef unsigned int CloudConferenceSessionHandle;
typedef unsigned int CloudConferenceHandle;
typedef unsigned int CloudConferenceParticipantHandle;

class ConferenceConnectorHandler;

enum AuthType
{
   AuthType_Service = 0,
   AuthType_Stretto = 1
};

struct ConferenceConnectorSettings
{
   cpc::string username;
   cpc::string password;
   cpc::string regionCode;
   cpc::string authServerUrl;
   cpc::string authServerApiKey;
   AuthType authType;
   cpc::string orchestrationServerUrl;
   cpc::string joinUrl;
   bool ignoreCertVerification;
   int authenticationTimeoutSeconds;
   int orchestrationTimeoutSeconds;

   ConferenceConnectorSettings() :
      authType(AuthType_Service),
      ignoreCertVerification(false),
      authenticationTimeoutSeconds(CONF_CONNECTOR_AUTH_TIMEOUT_SECONDS),
      orchestrationTimeoutSeconds(CONF_CONNECTOR_ORCH_TIMEOUT_SECONDS) {}
};

enum ServiceConnectionStatus
{
   ServiceConnectionStatus_Disconnecting = 0,
   ServiceConnectionStatus_Disconnected = 1,
   ServiceConnectionStatus_Connecting = 2,
   ServiceConnectionStatus_Authenticating = 3,
   ServiceConnectionStatus_Connected = 4,
   ServiceConnectionStatus_ConnFailure = 5,
   ServiceConnectionStatus_AuthFailure = 6,
   ServiceConnectionStatus_Timeout = 7
};

enum CloudConferenceType
{
   CloudConferenceType_Screenshare = 0,
   CloudConferenceType_AudioVideo_MCU = 1,
   CloudConferenceType_AudioVideo_SFU = 2
};

struct CloudConferenceSettings
{
   cpc::string conferenceId;
   cpc::string conferenceDescription;
   CloudConferenceType conferenceType;
   CloudConferenceHandle parentConference;
   bool persistent;
   cpc::vector<int> conferenceTags;

   CloudConferenceSettings()
   {
      conferenceType = CloudConferenceType_Screenshare;
      parentConference = (CloudConferenceHandle)0xffffffff;
      persistent = false;
   }
};

enum CloudConferenceRole
{
   CloudConferenceRole_Host = 0,
   CloudConferenceRole_Participant = 1
};

struct CloudConferenceSessionSettings
{
   CloudConferenceRole role = CloudConferenceRole_Host;
   cpc::string address;
   cpc::string displayName;
   cpc::vector<int> tags;
   bool addToFloor = false;
   int mediaDscp = 46; // EF
};

enum MediaDirection
{
   MediaDirection_None = 0,
   MediaDirection_SendRecv,
   MediaDirection_SendOnly,
   MediaDirection_RecvOnly,
   MediaDirection_Inactive
};

struct CloudConferenceSessionMediaSettings
{
   CloudConferenceSessionMediaSettings()
   {
      audioDirection = MediaDirection_None;
      videoDirection = MediaDirection_None;
      remoteVideoRenderSurface = NULL;
      remoteVideoRenderSurfaceType = Media::VideoSurfaceType_Default;
      screenCaptureMaxFrameRate = 30;
   }

   CPCAPI2::ConferenceConnector::MediaDirection audioDirection;
   CPCAPI2::ConferenceConnector::MediaDirection videoDirection;
   void* remoteVideoRenderSurface;
   Media::VideoSurfaceType remoteVideoRenderSurfaceType;
   unsigned int screenCaptureMaxFrameRate;
};

struct CloudConferenceInfo
{
   CloudConferenceHandle conference;
   CloudConferenceType conferenceType;
   cpc::string displayName;
   cpc::string joinUrl;
   cpc::string description;
   cpc::string conferenceId;
   cpc::vector<int> tags;
   bool persistent;

   CloudConferenceInfo()
   {
      conference = (CloudConferenceHandle)0;
      conferenceType = CloudConferenceType_AudioVideo_MCU;
      persistent = false;
   }
};

struct CloudConferenceParticipantInfo
{
   CloudConferenceParticipantHandle participant;
   cpc::string address;
   cpc::string displayName;
   bool hasFloor = false;
   cpc::vector<int> tags;
   unsigned int peerConnection;
   uint64_t joinTimestampMsecs;
};

enum SessionStatus
{
   SessionStatus_NotConnected,
   SessionStatus_Connecting,
   SessionStatus_Connected,
   SessionStatus_ConnectionFailed,
   SessionStatus_Reconnecting
};

enum SessionMediaType
{
   SessionMediaType_Audio,
   SessionMediaType_Video,
   SessionMediaType_Screenshare
};

enum SessionMediaStatus
{
   SessionMediaStatus_None,
   SessionMediaStatus_OfferSent,
   SessionMediaStatus_AnswerReceived
};

struct ConferenceSessionMediaInfo
{
   CPCAPI2::ConferenceConnector::MediaDirection mediaDirection;
   SessionMediaType mediaType;
   int mediaStreamId = -1;
};

struct ConferenceSessionPermissions
{
   bool canCreateConference = false;
};

}
}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_TYPES_H
