#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_H

#include "cpcapi2defs.h"
#include "confconnector/ConferenceConnector.h"

namespace CPCAPI2
{
class Phone;
namespace ConferenceConnector
{
class CPCAPI2_SHAREDLIBRARY_API ConferenceConnectorManagerJsonProxy : public CPCAPI2::ConferenceConnector::ConferenceConnectorManager
{
public:
   /**
   * Get a reference to the ConferenceConnectorManagerJsonProxy interface.
   */
   static ConferenceConnectorManagerJsonProxy* getInterface(Phone* cpcPhone);

protected:
   /*
    * The SDK will manage memory life of %ConferenceConnectorManagerJsonProxy.
    */
   virtual ~ConferenceConnectorManagerJsonProxy() {}
};

}
}
#endif // CPCAPI2_CONFERENCE_CONNECTOR_JSON_PROXY_H
