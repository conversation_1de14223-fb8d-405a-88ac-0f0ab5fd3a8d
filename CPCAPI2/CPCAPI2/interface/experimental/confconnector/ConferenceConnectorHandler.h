#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_HANDLER_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "ConferenceConnectorTypes.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{
namespace ConferenceConnector
{
struct ServiceConnectionStatusEvent
{
   cpc::string serverUri;
   ServiceConnectionStatus connectionStatus;
   cpc::string statusDesc;
   CPCAPI2::JsonApi::AuthToken authToken;
};

struct ConferenceCreatedEvent
{
   CloudConferenceHandle conference;
   cpc::string displayName;
};

struct ConferenceEndedEvent
{
   CloudConferenceHandle conference;
   cpc::string displayName;
};

struct ConferenceListUpdatedEvent
{
   cpc::vector<CloudConferenceInfo> conferenceList;
};

struct ConferenceParticipantListUpdatedEvent
{
   CloudConferenceHandle conference;
   cpc::vector<CloudConferenceParticipantInfo> participantList;
   cpc::vector<CloudConferenceParticipantInfo> addedParticipants;
   cpc::vector<CloudConferenceParticipantInfo> updatedParticipants;
   cpc::vector<CloudConferenceParticipantInfo> removedParticipants;
};

struct ConferenceSessionStatusChangedEvent
{
   CloudConferenceHandle conference = (CloudConferenceHandle)-1;
   CloudConferenceSessionHandle session = (CloudConferenceSessionHandle)-1;
   SessionStatus sessionStatus = SessionStatus_NotConnected;
   ConferenceSessionMediaInfo audio;
   ConferenceSessionMediaInfo video;
   ConferenceSessionMediaInfo screenshare;
   ConferenceSessionPermissions permissions;
   unsigned int peerConnection = -1;
   bool hasFloor = false;
};

struct ConferenceSessionMediaStatusChangedEvent
{
   CloudConferenceHandle conference = (CloudConferenceHandle)-1;
   CloudConferenceSessionHandle session = (CloudConferenceSessionHandle)-1;
   unsigned int peerConnection = -1;
   SessionMediaStatus mediaStatus = SessionMediaStatus_None;
};

struct ConferenceConnectorMediaStatistics
{
   unsigned int connectionId;
   unsigned int mediaStreamId;
   int rtpPacketReceiveCount;
   int rtcpPacketReceiveCount;
   int videoReceiveFrameWidth;
   int videoReceiveFrameHeight;
   int videoReceiveFps;
};

struct ConferenceConnectorMediaStatisticsEvent
{
   cpc::vector<ConferenceConnectorMediaStatistics> mediaStreamStats;
};


class ConferenceConnectorHandler
{
public:
   virtual ~ConferenceConnectorHandler() {}
   virtual int onServiceConnectionStatusChanged(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& args) = 0;
   virtual int onConferenceCreated(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& args) = 0;
   virtual int onConferenceEnded(ConferenceConnectorHandle conn, const ConferenceEndedEvent& args) = 0;
   virtual int onConferenceListUpdated(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& args) = 0;
   virtual int onConferenceParticipantListUpdated(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args) = 0;
   virtual int onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args) = 0;
   virtual int onConferenceSessionMediaStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionMediaStatusChangedEvent& args) = 0;
   virtual int onConferenceConnectorMediaStatistics(ConferenceConnectorHandle conn, const ConferenceConnectorMediaStatisticsEvent& args) { return 0; };
};

}

}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_HANDLER_H
