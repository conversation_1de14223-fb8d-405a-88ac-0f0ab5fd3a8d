#pragma once
#ifndef __CPCAPI2_STRETTOPROVISIONINGINTERNALTYPES_H__
#define __CPCAPI2_STRETTOPROVISIONINGINTERNALTYPES_H__

#include "cpcstl/string.h"
#include <strettoprovisioning/StrettoProvisioningTypes.h>

namespace CPCAPI2
{
   namespace StrettoProvisioning
   {
      struct StrettoProvisioningInternalSettings : public StrettoProvisioningSettings
      {
         cpc::string provisioningUrl;
         
         StrettoProvisioningInternalSettings(const StrettoProvisioningSettings& b) : StrettoProvisioningSettings(b) {}
         StrettoProvisioningInternalSettings() {}
         virtual ~StrettoProvisioningInternalSettings() {}
      };
   }
}

#endif //  __CPCAPI2_STRETTOPROVISIONINGTYPES_H__
