#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_H)
#define CPCAPI2_CLOUD_CONNECTOR_H

#include "cpcapi2defs.h"
#include "CloudConnectorTypes.h"

namespace CPCAPI2
{

class Phone;

namespace CloudConnector
{

class CPCAPI2_SHAREDLIBRARY_API CloudConnectorManager
{

public:

   /**
   * Get a reference to the %CloudConnectorManager interface.
   */   
   static CloudConnectorManager* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kCloudConnectorModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling CloudConnectorManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};
   
   /**
   * Allocates a handle to control a client's interactions with a set of server-hosted
   * back-end services that work together as a system (i.e. a "cloud").
   */
   virtual CloudConnectorHandle createCloudConnector() = 0;

   virtual int setHandler(CloudConnectorHandle conn, CloudConnectorHandler* handler) = 0;

   /**
    * Sets connection settings; required before any cloud-based services can be requested/accessed.
    */
   virtual int setConnectionSettings(CloudConnectorHandle conn, const CloudConnectorSettings& settings) = 0;

   /**
    * Requests usage of a particular service, by name and geographic region.
    * May be called multiple times prior to calling connectToServices(..) so that the CloudConnectorManager
    * can create/manage only as many connections to the servers as are required (e.g. a single connection
    * when multiple services are hosted at the same URL).
    */
   virtual int requestService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) = 0;

   /**
    * Connect to the back-end services requested by prior calls to requestService(..).
    * The CloudConnectorManager follows this sequence:
    * 1) request an auth token from the authentication server (per username/password and URL provided in setConnectionSettings(..))
    * 2) use the auth token to request back-end service URLs from the orchestration server
    * 3) connect to the URLs provided by the orchestration server
    * 
    * Progress is reported via the CloudConnectorHandler::onServiceConnectionStatusChanged(..) callback function.
    */
   virtual int connectToServices(CloudConnectorHandle conn) = 0;

   /**
    * Once the CloudConnectorManager has successfully connected to back-end services, the client application
    * uses this function to request the (internally allocated) instance of the SDK/Phone that represents the
    * remotely-executing service.
    *
    * For example, if services Mango and Papaya are running on the same server CloudServerX, the client application
    * would obtain the same instance of the SDK/Phone by calling this function and specifying those services.
    *
    * The client application should only use the returned SDK/Phone to access the requested services, and 
    * should NOT attempt to obtain references to other unrelated SDK modules.
    *
    * It is NOT necessary for the client application to release the returned Phone instance; it can be deallocated
    * using disconnectService(..) if it is no longer needed, and will otherwise be disposed of automatically
    * when the Phone instance associated with the CloudConnectorManager itself is released.
    */
   virtual CPCAPI2::Phone* getPhone(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) = 0;

   /**
    * May be called by the client application to release resources allocated for the specified service,
    * and to disconnect from the corresponding back-end server if appropriate (no other services in use on that server).
    * Note that calling this function is only necessary if a service is no longer required prior to app exit.
    */
   virtual int disconnectService(CloudConnectorHandle conn, const ServiceDescription& serviceDescriptor) = 0;

   /**
    * Sends an "add user" request to the specified authentication server.  If the user details provided
    * are accepted, the user is added to the authentication server database and can subsequently
    * be used for accessing cloud services.
    */
   virtual int addUser(CloudConnectorHandle conn, const AddUserRequest& req) = 0;

   /**
    * Logout initiates the following actions:
    *
    *    - initiates logout on the json client
    *    - disconnects the associated cloud services
    *    - disconnects the json client web socket connection
    *    - deletion of the associated phone instance on the xmpp agent
    *    - disconnection from the cloud
    *
    * Triggers the onLogout callback once the logout steps have been completed
   */
   virtual int logout(CloudConnectorHandle conn) = 0;

   /**
    * Deletes the cloud connector associated with the handle. Triggers the destroyed connection status callback
    * once the connection has been destroyed. The handle will no longer be valid after making this call
   */
   virtual int destroy(CloudConnectorHandle conn) = 0;

};

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_H
