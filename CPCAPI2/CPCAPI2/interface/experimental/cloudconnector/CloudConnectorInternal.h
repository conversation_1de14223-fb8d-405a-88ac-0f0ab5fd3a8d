#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_INTERNAL_H)
#define CPCAPI2_CLOUD_CONNECTOR_INTERNAL_H

#include "cpcapi2defs.h"
#include "CloudConnector.h"

namespace CPCAPI2
{

namespace CloudConnector
{

   class CPCAPI2_SHAREDLIBRARY_API CloudConnectorInternal : public CloudConnectorManager
   {

   public:

      static CloudConnectorInternal* getInternalInterface(Phone* cpcPhone);
      virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   };  

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_INTERNAL_H
