#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_TYPES_H)
#define CPCAPI2_CLOUD_CONNECTOR_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

#define CLOUD_CONNECTOR_AUTH_TIMEOUT_SECONDS 5
#define CLOUD_CONNECTOR_ORCH_TIMEOUT_SECONDS 15

namespace CPCAPI2
{

class Phone;

namespace CloudConnector
{

typedef unsigned int CloudConnectorHandle;
class CloudConnectorHandler;

struct CloudConnectorSettings
{
   cpc::string username;
   cpc::string password;
   cpc::string regionCode;
   cpc::string authServerUrl;
   cpc::string orchestrationServerUrl;
   bool ignoreCertVerification;
   int authenticationTimeoutSeconds;
   int orchestrationTimeoutSeconds;

   CloudConnectorSettings() :
      ignoreCertVerification(false),
      authenticationTimeoutSeconds(CLOUD_CONNECTOR_AUTH_TIMEOUT_SECONDS),
      orchestrationTimeoutSeconds(CLOUD_CONNECTOR_ORCH_TIMEOUT_SECONDS) {}
};

struct ServiceDescription
{
   cpc::string region;
   cpc::string service;
   cpc::string loopbackUrl; /** For debug use only, cloud connector will return this url for the server address */

   ServiceDescription() : region(""), service(""), loopbackUrl("") {}
   ServiceDescription(cpc::string region_, cpc::string service_) : region(region_), service(service_), loopbackUrl("") {}
   ServiceDescription(cpc::string region_, cpc::string service_, cpc::string loopbackUrl_) : region(region_), service(service_), loopbackUrl(loopbackUrl_) {}
};

enum ServiceConnectionStatus
{
   ServiceConnectionStatus_Disconnecting = 0,
   ServiceConnectionStatus_Disconnected = 1,
   ServiceConnectionStatus_Connecting = 2,
   ServiceConnectionStatus_Authenticating = 3,
   ServiceConnectionStatus_Connected = 4,
   ServiceConnectionStatus_ConnFailure = 5,
   ServiceConnectionStatus_AuthFailure = 6,
   ServiceConnectionStatus_Destroyed = 7
};

struct AddUserRequest
{
   cpc::string username;
   cpc::string password;
   cpc::string authServerUrl;
   cpc::string authServerApiKey;
};

cpc::string get_debug_string(const CPCAPI2::CloudConnector::ServiceConnectionStatus& status);

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_TYPES_H
