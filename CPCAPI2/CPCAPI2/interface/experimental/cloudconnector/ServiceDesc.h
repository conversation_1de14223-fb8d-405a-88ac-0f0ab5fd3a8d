#pragma once

#include <cpcstl/string.h>

namespace CPCAPI2
{
namespace CloudConnector
{
struct ServiceDesc
{
   cpc::string region;
   cpc::string service;
   
   ServiceDesc() : region(""), service("") {}
   ServiceDesc(cpc::string service_, cpc::string region_) : region(region_), service(service_) {}
   
   bool operator<(const ServiceDesc& rhs) const
   {
      return (service + region) < (rhs.service + rhs.region);
   }
};

struct ServiceDescUrl
{
   ServiceDesc serviceDesc;
   cpc::string url;
   
   ServiceDescUrl() : url("") {}
   ServiceDescUrl(cpc::string service, cpc::string region) : serviceDesc(ServiceDesc(service, region)), url("") {}
   ServiceDescUrl(ServiceDesc desc, cpc::string url_) : serviceDesc(desc), url(url_) {}
};


}
}
