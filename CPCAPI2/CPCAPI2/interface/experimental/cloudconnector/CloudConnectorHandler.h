#pragma once

#if !defined(CPCAPI2_CLOUD_CONNECTOR_HANDLER_H)
#define CPCAPI2_CLOUD_CONNECTOR_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "CloudConnectorTypes.h"
#include "jsonapi/JsonApiTypes.h"

namespace CPCAPI2
{

namespace CloudConnector
{

struct ServiceConnectionStatusEvent
{
   cpc::string serverUri;
   cpc::vector<ServiceDescription> services;
   ServiceConnectionStatus connectionStatus;
   cpc::string statusDesc;
   CPCAPI2::JsonApi::AuthToken authToken;

   ServiceConnectionStatusEvent() : serverUri(""), connectionStatus(ServiceConnectionStatus_Disconnected), statusDesc("") {}
};

struct AddUserResponse
{
   bool success;
   cpc::string errorDetails;
};

struct LogoutResult
{
   bool success;
   cpc::string errorDetails;
};

class CloudConnectorHandler
{

public:

   virtual ~CloudConnectorHandler() {}

   virtual int onServiceConnectionStatusChanged(CloudConnectorHandle conn, const ServiceConnectionStatusEvent& args) = 0;

   virtual int onAddUserResponse(CloudConnectorHandle conn, const AddUserResponse& args) = 0;

   virtual int onLogout(CloudConnectorHandle conn, const LogoutResult& args) = 0;

};

}

}

#endif // CPCAPI2_CLOUD_CONNECTOR_HANDLER_H
