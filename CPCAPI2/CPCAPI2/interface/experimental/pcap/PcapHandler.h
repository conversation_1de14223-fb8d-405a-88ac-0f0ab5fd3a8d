#pragma once

#if !defined(CPCAPI2_PCAP_HANDLER_H)
#define CPCAPI2_PCAP_HANDLER_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
namespace Pcap
{
/**
 * PCAP network interface.
 */
struct PcapInterface
{
   /**
    * Pcap interface name.
    */
   cpc::string name;

   /**
    * Textual description of interface.
    */
   cpc::string description;

   /**
    * IPv4 and IPv6 address.
    */
   cpc::string addr;

   /**
    * Set to true if PcapInterface is used for captuering.
    */
   bool preferred;

   PcapInterface()
   {
      preferred = false;
   }
};

/**
 * Event passed in PcapHandler::onCaptureStarted().
 */
struct CaptureStartedEvent
{
   PcapInterface pcapInterface;
};

/**
 * Event passed in PcapHandler::onCaptureCompleted().
 */
struct CaptureCompletedEvent
{
   cpc::string filePath;
};

/**
 * Event passed in PcapHandler::onQueryInterfacesSuccess().
 */
struct QueryInterfacesSuccessEvent
{
   cpc::vector<PcapInterface> interfaces;
};

/**
 * Event passed in PcapHandler::onError().
 */
struct ErrorEvent
{
   cpc::string errorText;
};

class PcapHandler
{
public:
   /**
    * Notifies the application about interfaces.
    */
   virtual int onQueryInterfacesSuccess(const QueryInterfacesSuccessEvent& evt) = 0;

   /**
    * Notifies the application when capture has started.
    */
   virtual int onCaptureStarted(const CaptureStartedEvent& evt) = 0;

   /**
    * Notifies the application of current total number of bytes captured
    */
   virtual int onCaptureProgress(unsigned long totalBytes) = 0;

   /**
    * Notifies the application when capture has ended.
    */
   virtual int onCaptureCompleted(const CaptureCompletedEvent& evt) = 0;

   /**
    * Used to report general PCAP error conditions, such as capture, or query interfaces.
    */
   virtual int onError(const ErrorEvent& evt) = 0;
};

} //namespace Pcap
} //namespace CPCAPI2
#endif // CPCAPI2_PCAP_HANDLER_H
