#pragma once

#if !defined(CPCAPI2_PCAP_MANAGER_H)
#define CPCAPI2_PCAP_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{

class Phone;

namespace Pcap
{

class PcapHandler;

/**
* Manager interface for Pcap service.
* get a reference to the interface using the static method getInterface().
* If packet capturing has been started without previously calling
* setCaptureInterface() default network interface will be used.
* Default interface is the one that is used to reach 8.8.8.8.
* If capture interface is not set with setCaptureInterface() and
* 8.8.8.8 cannot be reached packet capturing will fail.
*/

class CPCAPI2_SHAREDLIBRARY_API PcapManager
{
public:
   /**
   * Get a reference to the %PcapManager interface.
   */
   static PcapManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for Pcap events.
   *
   * @param handle the pcap handle which will be used
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
   */   
   virtual int setHandler(PcapHandler* handler) = 0;

   /**
   * Query for a list of the interfaces that packets can be captured on.
   * The response will be sent to the PcapHandler.
   */
   virtual int queryInterfaces() = 0;

   /**
   * Set the interface to capture packets on.
   *
   * @param interface the interface to capture when start() is called
   */
   virtual int setCaptureInterface(const cpc::string& interfaceName) = 0;

   /**
   * Start capturing packets.
   */
	virtual int start(const cpc::string& filePath) = 0;
   
   /**
   * Stop capturing packets.
   */	 
	virtual int stop()  = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

protected:
   /* 
    * The SDK will manage memory life of %PcapManager.
    */
   virtual ~PcapManager() {}

   
};

}//namespace Pcap
}//namespace CPCAPI2
#endif
