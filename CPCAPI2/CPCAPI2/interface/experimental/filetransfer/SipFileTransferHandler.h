#pragma once

#ifndef __CPCAPI2_SIP_FILE_TRANSFER_HANDLER_H__
#define __CPCAPI2_SIP_FILE_TRANSFER_HANDLER_H__

#include <stdint.h>

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipFileTransferManager.h"

namespace CPCAPI2
{
   namespace SipFileTransfer
   {
      enum FileTransferEndReason
      {
         FileTransferEndReason_Unknown                = 1100,
         FileTransferEndReason_UserTerminatedLocally  = 1110,
         FileTransferEndReason_UserTerminatedRemotely = 1120,
         FileTransferEndReason_ServerError            = 1130,
         FileTransferEndReason_ServerRejected         = 1140
      };

      enum FileTransferState
      {
         FileTransferState_None             = 0,
         FileTransferState_LocalOriginated  = 1000, 
         FileTransferState_RemoteOriginated = 1010, 
         FileTransferState_RemoteRinging    = 1020, 
         FileTransferState_LocalRinging     = 1030, 
         FileTransferState_Connected        = 1040, 
         FileTransferState_Early            = 1050,
         FileTransferState_Ended            = 1060
      };

      /**
       * Represents whether we are sending or receiving the INVITE
       * which creates the file transfer, not whether the file(s) are
       * incoming or outgoing (in theory this could be different)
       */
      enum FileTransferType
      {
         FileTransferType_Incoming = 1200,
         FileTransferType_Outgoing = 1210
      };

      enum FileTransferItemEndReason
      {
         FileTransferItemEndReason_Complete    = 1300,
         FileTransferItemEndReason_Interrupted = 1310
      };

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       * Event fired by the SDK to signal a new file transfer call. The items
       * are not yet known at this point and will be provided in a subsequent
       * event (FileTransferConfiguredEvent) provided the file transfer is
       * completed and not rejected.
       */
      struct NewFileTransferEvent
      {
         FileTransferState fileTransferState;
         FileTransferType fileTransferType;
         cpc::string remoteAddress;
         cpc::string remoteDisplayName;
         CPCAPI2::SipAccount::SipAccountHandle account;
      };

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       * The details (the file transfer items) are now known, or have changed.
       */
      struct FileTransferConfiguredEvent
      {
         FileTransferState fileTransferState;
         FileTransferType fileTransferType;
         SipFileTransferItems fileItems; // The items present in the transfer
      };

      /**
       * Fired by the SDK to signal the completion of the entire file transfer
       */
      struct FileTransferEndedEvent
      {
         FileTransferState fileTransferState;
         FileTransferEndReason endReason;
         unsigned int sipResponseCode;
         cpc::string signallingEndEvent;  // e.g. BYE, INVITE/487
         cpc::string signallingEndReason; // from SIP Reason header
      };

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       * Event fired by the SDK to signal completion of a file transfer item.
       * This event is only fired when the item is completed or interrupted.
       * In the event that the call is disconnected before the items are
       * finished, FileTransferItemEndedEvent(s) will be fired for each item in
       * progress.
       */
      struct FileTransferItemEndedEvent
      {
         SipFileTransferItemHandle fileTransferItem; // The item in question
         FileTransferItemEndReason endReason;
      };

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       * Event fired by the SDK to indicate the progress of an individual item
       */
      struct FileTransferItemProgressEvent
      {
         SipFileTransferItemHandle fileTransferItem; // The item in question
         unsigned short percent; // percentage [ 0% .. 100% ]
      };

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       * Used to report general SDK error conditions, such as invalid handles,
       * or cases where the transfer is not in a valid state for the requested
       * operation.
       */
      struct ErrorEvent
      {
         cpc::string errorText;
      };
	  
	  /**
	   * Do not use this interface; it covers alpha-grade  
      * functionality that is not officially supported and is subject to change.
      */
      class CPCAPI2_SHAREDLIBRARY_API SipFileTransferHandler
      {
      public:
         /**
          * Callback invoked by the SDK when a new file transfer is created,
          * either by sending or receiving.
          */
         virtual int onNewFileTransfer(
            const SipFileTransferHandle& fileTransfer,
            const NewFileTransferEvent& event ) = 0;

         /**
          * The details of the file transfer are now known, chiefly the
          * file transfer items. This will only happen if the file transfer
          * has been accepted by the remote side.
          */
         virtual int onFileTransferConfigured(
            const SipFileTransferHandle& fileTransfer,
            const FileTransferConfiguredEvent& event ) = 0;

         /**
          * Callback invoked by the SDK when a file transfer has completed
          * (including all of its file transfer items)
          */
         virtual int onFileTransferEnded(
            const SipFileTransferHandle& fileTransfer,
            const FileTransferEndedEvent& event ) = 0;

         /**
          * Progress indication (in percent) of an ongoing file transfer item
          */
         virtual int onFileTransferItemProgress(
            const SipFileTransferHandle& fileTransfer,
            const FileTransferItemProgressEvent& event ) = 0;

         /**
          * Invoked by the SDK when a file (item) has finished transfering.
          * i.e. when percentage completion reaches 100%, or the item is
          * cancelled either locally or remotely.
          */
         virtual int onFileTransferItemEnded(
            const SipFileTransferHandle& fileTransfer,
            const FileTransferItemEndedEvent& event ) = 0;

         /**
          * Invoked by the SDK for errors which do not fall into the categories
          * outlined above.
          */
         virtual int onError(
            const SipFileTransferHandle& fileTransfer,
            const ErrorEvent& event ) = 0;
      };
   }
}

#endif // __CPCAPI2_SIP_FILE_TRANSFER_HANDLER_H__
