#pragma once

#ifndef __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_H__
#define __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <stdint.h>
#include "cpcapi2defs.h"

namespace CPCAPI2
{
   // forward declarations
   class Phone;

   namespace SipFileTransfer
   {
      // forward declarations
      class SipFileTransferHandler;

      // type defs
      typedef unsigned int SipFileTransferHandle;
      typedef unsigned int SipFileTransferItemHandle;

      /**
       * Ternary state (accepted, rejected, or unprocessed). Each of the
       * file transfer items can be in one of these three states, depending
       * on the call flow.
       */
      typedef enum AcceptedState
      {
         ftitem_notprocessed = 1400, // default
         ftitem_accepted     = 1410, // user (or possibly remote user) has accepted the item
         ftitem_rejected     = 1420  // user (or possibly remote user) has denied the item
      } AcceptedState;

      /**
       * Struct which is used to pass information to and from the user, about
       * each file transfer item.
       */
      typedef struct SipFileTransferItemDetail
      {
         SipFileTransferItemHandle handle; /* (read-write) handle for the item */
         cpc::string   localfilePath;     /* (read-write) Local Path to the file (not including name) */
         cpc::string   localfileName;     /* (read-write) Local Name of the file */
         cpc::string   remotefileName;    /* (read-only)  Remote Name of the file */
         uint64_t       fileSizeBytes;     /* (read-only)  Size of the file in bytes */
         bool           isIncoming;        /* (read-only)  True if the item is being transferred from the remote side, false otherwise */
         AcceptedState  acceptedState;     /* (read-write) State of the acceptance (by the user) of the item */
         unsigned short percentComplete;   /* (read-only)  Percentage complete of the item transfer */
      } SipFileTransferItemDetail;

      typedef cpc::vector< SipFileTransferItemDetail > SipFileTransferItems;

      /**
	    * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
	    */
      class CPCAPI2_SHAREDLIBRARY_API SipFileTransferManager
      {
      public:
         /**
          * Get a reference to the %SipFileTransferManager interface.
          */   
         static SipFileTransferManager* getInterface( Phone* cpcPhone );

         /**
          * Set the handler for subscription events on the specified account.
          * Set the handler immediately after creating the account.
          *
          * To un-register the handler, pass NULL for handler. Must be called
          * on the same thread as SipAccount::process(..)
          */
         virtual int setHandler(
            SipAccount::SipAccountHandle account,
            SipFileTransferHandler* handler ) = 0;

         /**
          * Creates a new (empty) file transfer item handle, used for outbound
          * transfers. The handle will be unique across the SDK. After items
          * are created, they should be "configured" using the
          * configureFileTransferItems method, before start is called.
          */
         virtual SipFileTransferItemHandle createFileTransferItem(
            SipAccount::SipAccountHandle account ) = 0;

         /**
          * Allocates a new file transfer within the SDK, for outbound
          * transfer. After createFileTransfer is called,
          * configureFileTransferItems should be invoked in order to set the
          * items to be transferred.
          *
          * After this has been done, addParticipant(..) and start(..)
          * @return SipFileTransferHandle which is unique within the SDK
          */
         virtual SipFileTransferHandle createFileTransfer(
            SipAccount::SipAccountHandle account ) = 0;

         /**
          * Adds a participant to the file transfer session.  Call this
          * function after createFileTransfer(..) and before start(..).  Note:
          * The format of the targetAddress parameter is
          * sip:<EMAIL>.
          *
          * NOTE: only the first participant will be used, as file transfer is
          * only to one hop (i.e. not 1:N). Additional participants will be
          * ignored.
          */
         virtual int addParticipant( SipFileTransferHandle fileTransfer,
            const cpc::string& targetSIPAddress ) = 0;

         /**
          * Initiates an outgoing (client) session by sending an INVITE to the
          * remote participant.
          */
         virtual int start( SipFileTransferHandle fileTransfer ) = 0;

         /**
          * Ends an (already connected) session.  Sends an outgoing BYE. Does
          * nothing if the file transfer is not connected.
          */
         virtual int end( SipFileTransferHandle fileTransfer ) = 0;

         /**
          * Used to inform the remote end that an incoming file transfer
          * request is being processed.  Typically this means that the
          * application is prompting the user to accept/reject each file
          * transfer item.
          */
         virtual int provisionalAccept( SipFileTransferHandle fileTransfer ) = 0;

         /**
          * This method should be called before accepting/starting a file
          * transfer, it informs the SDK which file transfer items will be
          * used.
          *
          * In the case of an inbound call transfer, the application should set
          * the "isAccepted" flag for each item in the list.  If the user
          * accepted the item, then the local name and/or path may also be
          * modified.  The handles should not be changed.
          */
         virtual int configureFileTransferItems(
            SipFileTransferHandle fileTransfer,
            const SipFileTransferItems& fileItems
         ) = 0;

         /**
          * Accepts the file transfer (sending a 200 OK). Any file transfer
          * items which are marked as "isAccepted" using the
          * configureFileTransferItems method will be accepted, otherwise their
          * m= line will be set to 0.
          */
         virtual int accept( SipFileTransferHandle fileTransfer ) = 0;

         /**
          * The entire file transfer (and all of its items) will be rejected
          * with the reason sent to the Remote party. This should be done
          * BEFORE accept is called, otherwise an end() call will be required.
          *
          * @param fileTransfer The incoming session to reject.
          * @param rejectReason The SIP response code sent to the originating party.
          */
         virtual int reject( SipFileTransferHandle fileTransfer,
            unsigned int rejectReason ) = 0;

         /**
          * Cancels an ongoing file transfer (within the overall file
          * transfer call).
          */
         virtual int cancelItem( SipFileTransferHandle fileTransfer,
            SipFileTransferItemHandle fileTransferItem ) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %SipFileTransferManager.
          */
         virtual ~SipFileTransferManager() {}
      };
   }
}

#endif // __CPCAPI2_SIP_FILE_TRANSFER_MANAGER_H__

