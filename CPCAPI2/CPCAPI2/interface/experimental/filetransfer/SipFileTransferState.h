#pragma once

#if !defined(CPCAPI2_SIP_FILE_TRANSFER_STATE_H)
#define CPCAPI2_SIP_FILE_TRANSFER_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "SipFileTransferHandler.h"

#include <map>

namespace CPCAPI2
{ 
   namespace SipFileTransfer
   {
      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       */
      class SipFileTransferManager;

      /**
       * Do not use this interface; it covers alpha-grade  
       * functionality that is not officially supported and is subject to change.
       */
      struct SipFileTransferState
      {
         CPCAPI2::SipAccount::SipAccountHandle account;
         FileTransferState fileTransferState;
         FileTransferType fileTransferType;
         cpc::string remoteAddress;
         cpc::string remoteDisplayName;
         FileTransferEndReason endReason;
         SipFileTransferItems fileItems;

         SipFileTransferState()
         {
            account           = 0;
            fileTransferState = FileTransferState_None;
            fileTransferType  = FileTransferType_Incoming;
            remoteAddress     = "";
            remoteDisplayName = "";
            endReason         = FileTransferEndReason_Unknown;
         }
      };

      /**
       * File transfer state information. Accessed via SipFileTransferManager.
       */
      class CPCAPI2_SHAREDLIBRARY_API SipFileTransferStateManager
      {
      public:
         /**
         * Get a reference to the %SipFileTransferStateManager interface.
         */   
         static SipFileTransferStateManager* getInterface(SipFileTransferManager* cpcConvMan);

         virtual int getState(SipFileTransferHandle h, SipFileTransferState& conversationState) = 0;
         
         
      protected:
         /*
          * The SDK will manage memory life of %SipFileTransferStateManager.
          */
         virtual ~SipFileTransferStateManager() {}
      };
   }
}

#endif // CPCAPI2_SIP_FILE_TRANSFER_STATE_H
