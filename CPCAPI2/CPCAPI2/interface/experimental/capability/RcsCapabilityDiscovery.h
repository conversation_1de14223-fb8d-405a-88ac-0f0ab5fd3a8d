#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
class Phone;

namespace RcsCapabilityDiscovery
{

class RcsCapabilityDiscoveryHandler;

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Encapsulates functionality for RCS capability
* typically only the 16 pre-defined static %RcsCapability should be used.
*/
struct RcsCapability
{
   cpc::string tag;
   unsigned int id;

   /**
   * Get the RCS service name for this %RcsCapabilityDiscovery instance.
   */   
   const cpc::string& getDescription() const;
   bool operator <(const RcsCapability& other) const { return tag < other.tag; }
   bool operator ==(const RcsCapability& other) const { return tag == other.tag; }

   static CPCAPI2_SHAREDLIBRARY_API int getById(unsigned int id, RcsCapability& capability);

   static const RcsCapability ImageShare;
   static const RcsCapability VideoShare;

   static const RcsCapability Chat;
   static const RcsCapability FullStoreAndForwardGroupChat;
   static const RcsCapability FileTransfer;
   static const RcsCapability FileTransferThumbnail;
   static const RcsCapability FileTransferStoreAndForward;
   static const RcsCapability FileTransferViaHTTP;

   static const RcsCapability IPBasedStandaloneMessaging;

   static const RcsCapability VideoShareOutsideOfAVoiceCall;

   static const RcsCapability SocialPresenceInformation;

   static const RcsCapability IPVoiceCall;
   static const RcsCapability IPVideoCall;
   static const RcsCapability RCSIPVoiceCall;
   static const RcsCapability RCSIPVideoCall;
   static const RcsCapability RCSIPVideoCallOnly;

   static const RcsCapability GeolocationPUSH;
   static const RcsCapability GeolocationPULL;
   static const RcsCapability GeolocationPULLUsingFileTransfer;
};

typedef cpc::vector<RcsCapability> RcsCapabilitySet;

struct RcsCapabilityDiscoverySettings
{
   int pollingPeriodMs;
   int pollingRate;
   int pollingRatePeriodMs;
   int capInfoExpiryMs;
   int defaultDisc;
   int capDiscCommonStack;
   bool isIMCapAlwaysON;

   RcsCapabilityDiscoverySettings() :
      pollingPeriodMs(0),
      pollingRate(0),
      pollingRatePeriodMs(0),
      capInfoExpiryMs(0),
      defaultDisc(0),
      capDiscCommonStack(0),
      isIMCapAlwaysON(false)
   {}
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Encapsulates functionality for RCS capability status.
*/
struct RcsCapabilityStatus
{
   cpc::string targetAddress;
   RcsCapabilitySet caps;
   bool isRcsCapable;
   int64_t lastUpdated;

   RcsCapabilityStatus() : isRcsCapable(false), lastUpdated(0) {}
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Encapsulates functionality for RCS capability discovery with cache support;
* suppose to support both SIP OPTIONS message (*******) and Presence (*******)
* typically you create only one %RcsCapabilityDiscovery; get a reference to the interface 
* using the static method getInterface(). 
*/
class CPCAPI2_SHAREDLIBRARY_API RcsCapabilityDiscoveryManager
{
public:
   /**
   * Get a reference to the %RcsCapabilityDiscoveryManager interface.
   */   
   static RcsCapabilityDiscoveryManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for RCS capability discovery.
   */   
   virtual int setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      RcsCapabilityDiscoveryHandler* handler) = 0;

   /**
   * Import capability status to cache (possibly from other persisted sources).
   * @param account The %Account to use for delivery
   * @param cache The de-serialized contact capability status list from other sources
   */   
   virtual int importToCache(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<RcsCapabilityStatus>& cache) = 0;

   /**
   * Export capability status from cache (possibly to other persisted sources).
   * @param account The %Account to use for delivery
   * @param cache The output contact capability status list
   */   
   virtual int exportFromCache(
      CPCAPI2::SipAccount::SipAccountHandle account,
      cpc::vector<RcsCapabilityStatus>& cache) = 0;

   /**
   * Add a contact so its capability status will be notified when updated.
   * @param account The %Account to use for delivery
   * @param targetAddress The contact to be monitored
   */   
   virtual int addContact(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress) = 0;

   /**
   * Remove a contact so its capability status will not be monitored.
   * @param account The %Account to use for delivery
   * @param targetAddress The contact to be removed
   */   
   virtual int removeContact(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress) = 0;

   /**
   * Synchronize with a given list of contacts.
   * @param account The %Account to use for delivery
   * @param targetAddresses The list of contacts to be synchronized
   */   
   virtual int synchronizeAllContacts(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::vector<cpc::string>& targetAddresses) = 0;

   /**
   * Result code of getContactCapabilityStatus()
   * kError is still used for "general SDK error" (e.g. SIP stack is shut down)
   */   
   static const int kCompletedSynchronously;
   static const int kCompletedAsynchronously;

   /**
   * Get a contact's capability status.
   * @param account The %Account to use for delivery
   * @param targetAddress The contact to be queried
   * @param status The output capability status
   */   
   virtual int getContactCapabilityStatus(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      RcsCapabilityStatus& status) = 0;

   /**
   * Set self capability set.
   * @param account The %Account to use for delivery
   * @param caps The capability set to be set
   */   
   virtual int setMyCapabilities(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilitySet& caps) = 0;

   /**
   * Update capability discovery settings, eg. timeout value, period.
   * @param account The %Account to use for delivery
   * @param settings The capability discovery settings to be updated
   */   
   virtual int updateSettings(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const RcsCapabilityDiscoverySettings& settings) = 0;

protected:
   /*
    * The SDK will manage memory life of %RcsCapabilityDiscoveryManager.
    */
   virtual ~RcsCapabilityDiscoveryManager() {}
};

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_H
