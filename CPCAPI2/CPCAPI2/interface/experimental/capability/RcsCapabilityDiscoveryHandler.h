#pragma once

#if !defined(CPCAPI2_CAPABILITY_DISCOVERY_HANDLER_H)
#define CPCAPI2_CAPABILITY_DISCOVERY_HANDLER_H

#include "cpcapi2defs.h"
#include "RcsCapabilityDiscovery.h"

namespace CPCAPI2
{
namespace RcsCapabilityDiscovery
{

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
*
* The notification when a contact's capability status has been changed
*
* @param signalingStatusCode The standard SIP code for the success (e.g. 200) or failure (e.g. 404)
* @param status The updated capability status of the specified contact
*/   
struct RcsOnContactCapabilityStatusChangedEvent
{
   unsigned int                           signalingStatusCode;
   RcsCapabilityStatus                    status;
};

/**
* Do not use this interface; it covers alpha-grade  
* functionality that is not officially supported and is subject to change.
* Handler for events relating to capability status change notification.
*/
class RcsCapabilityDiscoveryHandler
{
public:
   /**
   * Notifies that there is a change of a contact's capability status.
   * @param account The %SipAccount that was used to deliver the capability discovery
   * @param args Contains details about the change of the contact's capability status
   */   
   virtual int onContactCapabilityStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const RcsOnContactCapabilityStatusChangedEvent& args) = 0;
};

}
}
#endif // CPCAPI2_CAPABILITY_DISCOVERY_HANDLER_H
