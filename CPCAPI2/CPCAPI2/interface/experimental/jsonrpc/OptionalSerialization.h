#include <nlohmann/json.hpp>
#include <jsonrpccxx/typemapper.hpp>
#include <optional>

#if !defined(NLOHMANN_OPTIONAL_SERIALIZATION_H)
#define NLOHMANN_OPTIONAL_SERIALIZATION_H

namespace jsonrpccxx
{
    template <typename T>
    constexpr json::value_t GetType(type<std::optional<T>>)
    {
        return GetType(type<typename std::decay<T>::type>());
    }
}

namespace nlohmann
{
template <typename T>
struct adl_serializer<std::optional<T>>
{
    static void to_json(json &j, const std::optional<T> &opt)
    {
        j = json::object();
        if (opt)
        {
            j = *opt;
        } else {
            j = json::value_t::null;
        }
    }

    static void from_json(const json &j, std::optional<T> &opt)
    {
        if (!j.is_null())
        {
            opt = j.get<T>();
        }
        else
        {
            opt = std::nullopt;
        }
    }
};
} // namespace nlohmann

#endif // NLOHMANN_OPTIONAL_SERIALIZATION_H
