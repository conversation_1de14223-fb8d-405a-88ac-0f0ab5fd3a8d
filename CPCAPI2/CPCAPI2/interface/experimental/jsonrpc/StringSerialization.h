#include <nlohmann/json.hpp>
#include <jsonrpccxx/typemapper.hpp>
#include "cpcstl/string.h"

#if !defined(NLOHMANN_STRING_SERIALIZATION_H)
#define NLOHMANN_STRING_SERIALIZATION_H

namespace jsonrpccxx
{
    constexpr json::value_t GetType(type<cpc::string>)
    {
        return json::value_t::string;
    }
}

namespace nlohmann
{
template <>
struct adl_serializer<cpc::string>
{
    static void to_json(json &j, const cpc::string &s)
    {
        j = s.c_str();
    }

    static void from_json(const json &j, cpc::string &s)
    {
        if (j.is_string())
        {
            s = j.get<std::string>().c_str();
        }
        else
        {
            s = "";
        }
    }
};
} // namespace nlohmann

#endif // NLOHMANN_STRING_SERIALIZATION_H
