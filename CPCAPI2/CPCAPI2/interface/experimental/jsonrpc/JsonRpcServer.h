#pragma once

#if !defined(CPCAPI2_JSON_RPC_SERVER_H)
#define CPCAPI2_JSON_RPC_SERVER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

#ifndef CPCAPI2_JSONRPC_API
#ifdef CPCAPI2_JSONRPC_EXPORT
#define CPCAPI2_JSONRPC_API CPCAPI2_SHAREDLIBRARY_EXPORT
#else
#define CPCAPI2_JSONRPC_API CPCAPI2_SHAREDLIBRARY_API
#endif
#endif

namespace CPCAPI2
{

namespace JsonRpc
{
typedef int64_t JsonRpcConnectionHandle;

/**
*/
class CPCAPI2_JSONRPC_API JsonRpcServer
{
public:
   static JsonRpcServer* create();

   virtual std::string processIncoming(const std::string& incoming) = 0;
};
} // namespace JsonRpc
} // CPCAPI2
#endif // CPCAPI2_JSON_RPC_SERVER_H
