#include <nlohmann/json.hpp>
#include <jsonrpccxx/typemapper.hpp>
#include "cpcstl/vector.h"

#if !defined(NLOHMANN_VECTOR_SERIALIZATION_H)
#define NLOHMANN_VECTOR_SERIALIZATION_H

namespace jsonrpccxx
{
    template <typename T>
    constexpr json::value_t GetType(type<cpc::vector<T>>)
    {
        return json::value_t::array;
    }
}
namespace nlohmann
{
template <typename T>
struct adl_serializer<cpc::vector<T>>
{
    static void to_json(json &j, const cpc::vector<T> &arr)
    {
        j = json::array();
        for(T e : arr)
        {
            j.push_back(e);
        }
    }

    static void from_json(const json &j, cpc::vector<T> &arr)
    {
        arr.clear();
        if (j.is_array())
        {
            for(T e: j)
            {
                arr.push_back(e);
            }
        }
    }
};
} // namespace nlohmann

#endif // NLOHMANN_VECTOR_SERIALIZATION_H
