#pragma once

#if !defined(CPCAPI2_AUDIOEXT_H)
#define CPCAPI2_AUDIOEXT_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include <media/MediaManager.h>

namespace CPCAPI2
{
namespace Media
{
/**
* Experimental.
*/
class CPCAPI2_SHAREDLIBRARY_API AudioExt
{
public:
   /**
   * Get a reference to the %Video interface.
   */   
   static AudioExt* getInterface(MediaManager* cpcPhone);

   /**
   * Experimental
   */
   virtual int setAudioDeviceFile(const cpc::string& inputAudiofileNameUtf8, const cpc::string& outputAudiofileNameUtf8) = 0;
   virtual int setMicAGCEnabled(bool enabled) = 0;
};

}
}
#endif // CPCAPI2_AUDIOEXT_H
