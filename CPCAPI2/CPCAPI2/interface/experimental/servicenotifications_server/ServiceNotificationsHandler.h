#pragma once

#if !defined(CPCAPI2_SERVICE_NOTIFICATIONS_HANDLER_H)
#define CPCAPI2_SERVICE_NOTIFICATIONS_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "servicenotifications_server/ServiceNotificationsTypes.h"

namespace CPCAPI2
{
   namespace ServiceNotifications
   {
      typedef struct NotificationSuccessEvent
      {
      } NotificationSuccessEvent;

      typedef struct NotificationFailureEvent
      {
      } NotificationFailureEvent;

      typedef struct ErrorEvent
      {
         cpc::string errorText;
      } ErrorEvent;

      /**
      */
      class ServiceNotificationsHandler
      {
      public:
         /**
          * Posted by the SDK when the notification was successfully sent
          */
         virtual int onNotificationSuccess( ServiceNotificationsHandle device, const NotificationSuccessEvent& evt ) = 0;
         virtual int onNotificationFailure( ServiceNotificationsHandle device, const NotificationFailureEvent& evt ) = 0;
         virtual int onError( ServiceNotificationsHandle hDevice, const ErrorEvent& evt ) = 0;
      };

      /**
       * "stub" class which implements each method in the interface. An
       * application may choose to extend this rather than implement the
       * interface directly, in order to avoid breakages when new methods are
       * added/removed.
       */
      class ServiceNotificationsHandlerStub : public ServiceNotificationsHandler
      {
      public:
         virtual int onNotificationSuccess(ServiceNotificationsHandle device, const NotificationSuccessEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onNotificationFailure(ServiceNotificationsHandle device, const NotificationFailureEvent& evt ) OVERRIDE { return kSuccess; }
         virtual int onError( ServiceNotificationsHandle hDevice, const ErrorEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}

#endif // CPCAPI2_SERVICE_NOTIFICATIONS_HANDLER_H
