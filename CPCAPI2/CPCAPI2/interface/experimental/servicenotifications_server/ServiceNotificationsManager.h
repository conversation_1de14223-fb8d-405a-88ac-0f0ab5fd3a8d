#pragma once

#ifndef CPCAPI2_SERVICE_NOTIFICATIONS_MANAGER_H
#define CPCAPI2_SERVICE_NOTIFICATIONS_MANAGER_H

#include "cpcapi2defs.h"
#include "servicenotifications_server/ServiceNotificationsTypes.h"
#include "push_endpoint/PushNotificationCommonTypes.h"
#include "push_service/PushNotificationServiceManager.h"

namespace CPCAPI2
{
   class Phone;

   namespace ServiceNotifications
   {
      class ServiceNotificationsHandler;

      /**
       */
      class CPCAPI2_SHAREDLIBRARY_API ServiceNotificationsManager
      {
      public:
         /**
          * Get a reference to the %ServiceNotificationsManager interface.
          */   
         static ServiceNotificationsManager* getInterface( Phone* cpcPhone );

         /**
          * Creates a handle/object to use for sending service notifications.
          */
         virtual ServiceNotificationsHandle create( void ) = 0;

         /**
          * Sets the handler on the instance of service notifications handle
          */
         virtual int setHandler( ServiceNotificationsHandle hService,
            ServiceNotificationsHandler* handler) = 0;

         /**
          * Sets the push notification server manager interface which will be
          * used by the ServiceNotificationsManager in order to send push
          * notifications to devices.
          */
         virtual int setPushNotificationServiceManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager) = 0;

         /**
          * Cleans up any resources associated with this instance of
          * service notifications.
          */
         virtual int destroy( ServiceNotificationsHandle hService ) = 0;

         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;
         static const int kServiceNotificationsModuleDisabled = -1;

         /**
          * Allows the application code to receive callback notifications from
          * the SDK.
          *
          * These callbacks will happen synchronously, in the same thread of
          * execution as that in which %process() is invoked.  Depending on the
          * application threading model, process() can be used in two different
          * ways:
          * <ol>
          * <li>blocking mode ?Typically in this mode, %process() is called by the 
          * application from a background (worker) thread.  The call to process() 
          * blocks until a callback function needs to be invoked.
          * <li>non-blocking mode ?In this mode, %process() is called by the application 
          * from the main (GUI) thread of the application, typically from the 
          * main message/event loop.  In this mode, %process() returns immediately 
          * and so must be called frequently enough that the application can receive 
          * its callback notifications in a timely manner.
          *
          * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
          *                representing the time the call to process(..) will block waiting for a callback
          *                from the SDK
          *</ol>
          */
         virtual int process( unsigned int timeout ) = 0;
      };
   }
}

#endif // CPCAPI2_SERVICE_NOTIFICATIONS_MANAGER_H
