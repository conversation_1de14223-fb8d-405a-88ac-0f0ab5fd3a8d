#pragma once

#if !defined(CPCAPI2_SERVICE_NOTIFICATIONS_TYPES_H)
#define CPCAPI2_SERVICE_NOTIFICATIONS_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
   namespace ServiceNotifications
   {
      typedef unsigned int ServiceNotificationsHandle;

      /**
       * The different stages of a service's lifecycle.
       */
      typedef enum ServiceState
      {
          ServiceState_Starting,
          ServiceState_Started,
          ServiceState_Stopping,
          ServiceState_Stopped
      } ServiceState;

      /**
       * Different 'reasons' for why the state of a service might change.
       */
      typedef enum ReasonCode
      {
          ReasonCode_ScheduledOutage,
          ReasonCode_HighLoad,
          ReasonCode_Unknown
      } ReasonCode;

      /**
       * Upon receipt of a ServiceEvent, the clients will need to consult the
       * orchestration server to obtain a new service. The orchestration server
       * MAY choose to return the same service to the client, optionally.
       */
      typedef struct ServiceEvent
      {
          /**
           * Current state of the service in question
           */
          ServiceState state;

          /**
           * The "reason code" which may be used to inform the user of the
           * cause of the outage
           */
          ReasonCode reason;

          /**
           * The name of the affected service.
           */
          cpc::string serviceName;
      } ServiceEvent;
   }
}

#endif // CPCAPI2_SERVICE_NOTIFICATIONS_TYPES_H
