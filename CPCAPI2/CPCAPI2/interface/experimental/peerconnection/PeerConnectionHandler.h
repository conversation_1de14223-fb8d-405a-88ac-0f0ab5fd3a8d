#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_HANDLER_H)
#define CPCAPI2_PEER_CONNECTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "PeerConnectionTypes.h"

namespace CPCAPI2
{
namespace PeerConnection
{
typedef unsigned int PeerConnectionHandle;

struct UserMediaSuccessEvent
{
};

struct LocalSessionDescriptionCreatedEvent
{
   SessionDescription sdp;
};

struct SignalingStateChangeEvent
{
   SignalingState newState;
};

struct SetLocalSessionDescriptionResult
{
   cpc::vector<MediaInfo> mediaInfo;
};

struct SetRemoteSessionDescriptionResult
{
   cpc::vector<MediaInfo> mediaInfo;
};

struct AddRemoteStreamEvent
{
};

struct RemoveRemoteStreamEvent
{
};

struct CreateOfferResult
{
   SessionDescription sdp;
};

struct CreateAnswerResult
{
   SessionDescription sdp;
};

struct WebVideoServerReadyEvent
{
};

struct MediaInactivityEvent
{
};

struct ErrorEvent
{
};

/**
*/
class PeerConnectionHandler
{
public:
   virtual int onSignalingStateChange(PeerConnectionHandle pc, const SignalingStateChangeEvent& args) = 0;

   virtual int onCreateOfferResult(PeerConnectionHandle pc, const CreateOfferResult& args) = 0;
   virtual int onCreateAnswerResult(PeerConnectionHandle pc, const CreateAnswerResult& args) = 0;
   virtual int onSetLocalSessionDescriptionResult(PeerConnectionHandle pc, const SetLocalSessionDescriptionResult& args) = 0;
   virtual int onSetRemoteSessionDescriptionResult(PeerConnectionHandle pc, const SetRemoteSessionDescriptionResult& args) = 0;
   virtual int onWebVideoServerReady(PeerConnectionHandle pc, const WebVideoServerReadyEvent& args) {
      return 0;
   }
   virtual int onMediaInactivity(PeerConnectionHandle pc, const MediaInactivityEvent& args) {
      return 0;
   }
   virtual int onError(PeerConnectionHandle pc, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_PEER_CONNECTION_HANDLER_H
