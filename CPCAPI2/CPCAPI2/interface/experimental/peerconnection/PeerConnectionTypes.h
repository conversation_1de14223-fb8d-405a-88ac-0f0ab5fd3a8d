#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_TYPES_H)
#define CPCAPI2_PEER_CONNECTION_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace PeerConnection
{
typedef unsigned int PeerConnectionHandle;
typedef unsigned int MediaStreamHandle;

enum MediaType
{
   MediaType_Audio = 0x1,
   MediaType_Video = 0x2
};

enum MediaDirection
{
   MediaDirection_None = 0,
   MediaDirection_SendRecv,
   MediaDirection_SendOnly,
   MediaDirection_RecvOnly,
   MediaDirection_Inactive
};

enum SignalingState
{
   SignalingState_Stable,
   SignalingState_HaveLocalOffer,
   SignalingState_HaveRemoteOffer,
   SignalingState_HaveLocalPranswer,
   SignalingState_HaveRemotePranswer,
   SignalingState_Closed
};

enum MediaEncryptionMode
{
   MediaEncryptionMode_Unencrypted = 0x1,
   MediaEncryptionMode_SRTP_SDES_Encrypted = 0x2,
   MediaEncryptionMode_SRTP_DTLS_Encrypted = 0x4
};

enum MediaCryptoSuite
{
   MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32 = 0,
   MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80 = 1,
   MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32 = 2,
   MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 = 3,
   MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32 = 4,
   MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80 = 5,
   MediaCryptoSuite_AEAD_AES_128_GCM        = 10,
   MediaCryptoSuite_AEAD_AES_256_GCM        = 11
};

struct MediaEncryptionOptions
{
   CPCAPI2::PeerConnection::MediaEncryptionMode     mediaEncryptionMode;
   bool                                             secureMediaRequired;
   CPCAPI2::PeerConnection::MediaCryptoSuite        mediaCryptoSuite;
};

struct MediaCodec
{
   cpc::string                                      codecPayloadName;
   int                                              codecFrequency;
};

struct MediaInfo
{
   CPCAPI2::PeerConnection::MediaType               mediaType;
   CPCAPI2::PeerConnection::MediaDirection          mediaDirection;
   CPCAPI2::PeerConnection::MediaEncryptionOptions  mediaEncryptionOptions;
   MediaStreamHandle                                mediaStream;
   int                                              mediaStreamId;
   /**
    * Specify to override default codec list
    */
   cpc::vector<MediaCodec>                          codecs;

   MediaInfo()
   {
      mediaType = MediaType_Audio;
      mediaDirection = CPCAPI2::PeerConnection::MediaDirection_SendRecv;
      mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
      mediaEncryptionOptions.secureMediaRequired = false;
      mediaEncryptionOptions.mediaCryptoSuite = CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80;
      mediaStream = 0;
      mediaStreamId = -1;
   }

   MediaInfo(MediaType type, MediaDirection direction)
   {
      mediaType = type;
      mediaDirection = direction;
      mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
      mediaEncryptionOptions.secureMediaRequired = false;
      mediaEncryptionOptions.mediaCryptoSuite = CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80;
      mediaStream = 0;
      mediaStreamId = -1;
   }

   MediaInfo(MediaType type, MediaDirection direction, int mediaStreamId)
   {
      mediaType = type;
      mediaDirection = direction;
      mediaEncryptionOptions.mediaEncryptionMode = CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted;
      mediaEncryptionOptions.secureMediaRequired = false;
      mediaEncryptionOptions.mediaCryptoSuite = CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80;
      mediaStream = 0;
      this->mediaStreamId = mediaStreamId;
   };
};

struct PeerConnectionSettings
{
   enum SecureMediaMode
   {
      SecureMediaMode_None = 0,
      SecureMediaMode_SDES = 1,
      SecureMediaMode_DTLS = 2
   };
   enum NatTraversalMode
   {
      NatTraversalMode_None = 0,
      NatTraversalMode_Auto = 1,  /* attempt ICE if a STUN server is configured and the remote end supports ICE */
      NatTraversalMode_STUN = 2,
      NatTraversalMode_TURN = 3,
      NatTraversalMode_ICE
   };
   enum NatTraversalServerType
   {
      NatTraversalServerType_StunAndTurn = 0,
      NatTraversalServerType_StunOnly = 1,
      NatTraversalServerType_TurnOnly = 2
   };

   cpc::string sessionName;
   cpc::string certAor;
   PeerConnectionSettings::NatTraversalMode natTraversalMode;
   PeerConnectionSettings::SecureMediaMode secureMediaMode;
   bool secureMediaRequired;
   cpc::string natTraversalServerHostname;
   cpc::string natTraversalServerUsername;
   cpc::string natTraversalServerPassword;
   int natTraversalServerPort;
   PeerConnectionSettings::NatTraversalServerType natTraversalServerType;
   cpc::string localInterface;
   cpc::string forcedHostCandidate;
   bool useUnixDomainSockets;
   int mixId;
   int mixContribution;
   bool startPaused;
   int videoCaptureDeviceId;
   bool adaptVideoCodecOnRemotePacketLoss;
   bool isScreenshare;
   void* defaultVideoRenderSurface;
   int videoMaxBitrateKbps;
   int videoTargetBitrateKbps;
   int videoStartBitrateKbps;
   int videoMinBitrateKbps;
   bool rtcpEnabled;
   bool rtcpMux;
   bool useRandomPortsInIceCandidates; // for testing only
   int mediaDscp;  // EF 46 by default

   PeerConnectionSettings()
      : natTraversalMode(PeerConnectionSettings::NatTraversalMode_Auto),
      secureMediaMode(SecureMediaMode_None),
      secureMediaRequired(false),
      natTraversalServerPort(3478),
      natTraversalServerType(PeerConnectionSettings::NatTraversalServerType_StunAndTurn),
      useUnixDomainSockets(false),
      mixId(-1),
      mixContribution(1),
      startPaused(false),
      videoCaptureDeviceId(-1),
      adaptVideoCodecOnRemotePacketLoss(false),
      isScreenshare(false),
      defaultVideoRenderSurface(NULL),
      videoMaxBitrateKbps(2000),
      videoTargetBitrateKbps(2000),
      videoStartBitrateKbps(2000),
      videoMinBitrateKbps(500),
      rtcpEnabled(true),
      rtcpMux(false),
      useRandomPortsInIceCandidates(false),
      mediaDscp(46)
   {
   }
};

struct SessionDescription
{
   cpc::string sdpString;
   unsigned short sdpLen;

   enum SessionDescriptionType
   {
      SessionDescriptionType_Offer = 0,
      SessionDescriptionType_Answer,
      SessionDescriptionType_Pranswer
   };
   SessionDescriptionType sdpType;
};

}
}
#endif // CPCAPI2_PEER_CONNECTION_TYPES_H
