#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_JSON_API_H)
#define CPCAPI2_PEER_CONNECTION_JSON_API_H

#include "cpcapi2defs.h"
#include "jsonapi/JsonApiServer.h"

namespace CPCAPI2
{
class Phone;
namespace PeerConnection
{
/**
*/
class CPCAPI2_SHAREDLIBRARY_API PeerConnectionJsonApi
{
public:
   /**
   * Get a reference to the %PeerConnectionJsonApi interface.
   */
   static PeerConnectionJsonApi* getInterface(Phone* cpcPhone);

};

}
}

#endif // CPCAPI2_PEER_CONNECTION_JSON_API_H
