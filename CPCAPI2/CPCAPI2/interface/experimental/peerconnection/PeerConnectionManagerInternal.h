#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_MANAGER_INTERNAL_H)
#define CPCAPI2_PEER_CONNECTION_MANAGER_INTERNAL_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{

namespace PeerConnection
{
   /**
   */
   class CPCAPI2_SHAREDLIBRARY_API PeerConnectionManagerInternal
   {
   public:

      virtual int setMediaInactivityTimeoutMsecs(PeerConnectionHandle pc, int msecs) { return 0; }
      virtual int setLoggingMediaStatisticsFrequencyMsecs(PeerConnectionHandle pc, int msecs) { return 0; }
      virtual int pauseTxMedia(PeerConnectionHandle pc, const SessionDescription& sdp) { return 0; }
      virtual int queryMediaStatistics(PeerConnectionHandle pc) { return 0; }
      virtual int logMediaStatistics(PeerConnectionHandle pc) { return 0; }

   };

}

}

#endif // CPCAPI2_PEER_CONNECTION_MANAGER_INTERNAL_H
