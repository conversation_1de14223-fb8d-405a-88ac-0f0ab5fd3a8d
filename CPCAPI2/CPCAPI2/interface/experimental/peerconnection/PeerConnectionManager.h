#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_MANAGER_H)
#define CPCAPI2_PEER_CONNECTION_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "PeerConnectionTypes.h"

namespace CPCAPI2
{
class Phone;

namespace PeerConnection
{
typedef unsigned int PeerConnectionHandle;
class PeerConnectionHandler;

/**
*/
class CPCAPI2_SHAREDLIBRARY_API PeerConnectionManager
{
public:
   /**
   * Get a reference to the %PeerConnectionManager interface.
   */   
   static PeerConnectionManager* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kPeerConnectionModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling PeerConnectionManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   virtual PeerConnectionHandle createPeerConnection() = 0;

   virtual int setHandler(PeerConnectionHandle pc, PeerConnectionHandler* handler) = 0;
   virtual int setDefaultSettings(PeerConnectionHandle pc, const PeerConnectionSettings& settings) = 0;
   virtual int createOffer(PeerConnectionHandle pc) = 0;
   virtual int createAnswer(PeerConnectionHandle pc) = 0;
   virtual int setLocalDescription(PeerConnectionHandle pc, const SessionDescription& sdp) = 0;
   virtual int setRemoteDescription(PeerConnectionHandle pc, const SessionDescription& sdp) = 0;
   virtual int close(PeerConnectionHandle pc) = 0;

   virtual MediaStreamHandle createMediaStream() = 0;

   /**
   * Changes the set of media offered by the SDK.  May be called prior to createOffer(..) or createAnswer(..).
   */
   virtual int configureMedia(PeerConnectionHandle pc, MediaStreamHandle mediaStream, const CPCAPI2::PeerConnection::MediaInfo& mediaDescriptor) = 0;

   virtual int startWebVideoServerForMediaStream(PeerConnectionHandle pc, MediaStreamHandle mediaStream) = 0;

   virtual int playSound(PeerConnectionHandle pc, const cpc::string& mediaUri, bool repeat) = 0;

   // Set for all connections
   virtual int setAudioDeviceCloseDelay(int audioDeviceCloseDelay) = 0;

   virtual int addAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp) = 0;
   virtual int removeAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp) = 0;
   virtual int startMediaInactivityMonitor(PeerConnectionHandle pc) = 0;
   virtual int stopMediaInactivityMonitor(PeerConnectionHandle pc) = 0;
   virtual int startLoggingMediaStatistics(PeerConnectionHandle pc) = 0;
   virtual int stopLoggingMediaStatistics(PeerConnectionHandle pc) = 0;

   virtual int pauseAudioPlayout(PeerConnectionHandle pc) = 0;
   virtual int resumeAudioPlayout(PeerConnectionHandle pc) = 0;
};

}
}

#endif // CPCAPI2_PEER_CONNECTION_MANAGER_H
