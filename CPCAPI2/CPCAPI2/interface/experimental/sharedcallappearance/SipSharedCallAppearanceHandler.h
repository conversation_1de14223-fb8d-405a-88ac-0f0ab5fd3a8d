#pragma once

#if !defined(CPCAPI2_SIP_SHARED_CALL_APPEARANCE_HANDLER_H)
#define CPCAPI2_SIP_SHARED_CALL_APPEARANCE_HANDLER_H

#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipSharedCallAppearanceTypes.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"
#include "dialogevent/SipDialogEventPublicationHandler.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceNewSubscription().
 *
 * A new subscription for a SCAP PUID has been created.
 */
struct SharedCallAppearanceNewSubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;             // SCAP PUID
};

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionStateChanged().
 *
 * The state of the subscription for a SCAP PUID has changed.
 */
struct SharedCallAppearanceSubscriptionStateChangedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;              // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;                          // SCAP PUID
   CPCAPI2::SipEvent::SipSubscriptionState subscriptionState;  // Subscription state
};

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionEnded().
 *
 * The subscription session for a SCAP PUID is terminated (for any reason).
 */
struct SharedCallAppearanceSubscriptionEndedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;         // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;                     // SCAP PUID
   CPCAPI2::SipEvent::SipSubscriptionEndReason endReason; // Reason for the subscription termination
};

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceStateChanged().
 *
 * The dialog state information of a SCAP PUID has changed.
 */
struct SharedCallAppearanceStateChangedEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;      // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;                  // SCAP PUID
   cpc::vector<SipSharedCallAppearanceCallInfo> calls; // Calls on the SCAP PUID
};

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceMakeExclusiveSuccess().
 *
 * The make exclusive operation on an appearance has succeeded.
 */
struct SharedCallAppearanceMakeExclusiveSuccessEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;             // SCAP PUID
};

/**
 * Event passed in SipSharedCallAppearanceHandler::onSharedCallAppearanceMakeExclusiveFailure().
 *
 * The make exclusive operation on an appearance has failed.
 */
struct SharedCallAppearanceMakeExclusiveFailureEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;             // SCAP PUID
};

/**
* Event passed in SipSharedCallAppearanceHandler::onError().
*
* Used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account; // Account associated with SCAP PUID set
   SipSharedCallAppearanceHandle sca;             // SCAP PUID
   cpc::string errorText;                         // Error message
};

/**
 * Handler for events related to SCAP PUID subscriptions and call features/operations; 
 * set in SipSharedCallAppearanceManager::setHandler().
 */
class SipSharedCallAppearanceHandler
{
public:
   /**
    * ALU17.
    *
    * Callback invoked by the SDK when a new subscription for a SCAP PUID has been created.
    *
    * @param scaSet the affected set.
    * @param args information about the new subscription state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceNewSubscription(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SharedCallAppearanceNewSubscriptionEvent& args) = 0;

   /**
    * ALU17.
    *
    * Callback invoked by the SDK when the state of subscription for a SCAP PUID has changed.
    *
    * @param scaSet the affected rset.
    * @param args information about the subscription state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceSubscriptionStateChanged(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SharedCallAppearanceSubscriptionStateChangedEvent& args) = 0;

   /**
    * ALU17.
    *
    * Callback invoked by the SDK when the subscription for a SCAP PUID has ended.
    *
    * @param scaSet the affected set.
    * @param args information about the terminated subscription.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceSubscriptionEnded(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SharedCallAppearanceSubscriptionEndedEvent& args) = 0;

   /**
    * ALU17.
    *
    * Callback invoked by the SDK when the state of a SCAP PUID has changed.
    *
    * @param scaSet the set that contains the affected SCAP PUID.
    * @param args information about the state change.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceStateChanged(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SharedCallAppearanceStateChangedEvent& args) = 0;

   /**
    * ALU24.
    *
    * Callback invoked by the SDK when the make exclusive operation on a SCAP PUID has succeeded.
    *
    * @param scaSet the set that contains the affected SCAP PUID.
    * @param args information about the success of the operation.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceMakeExclusiveSuccess(
      SipSharedCallAppearanceSetHandle scaSet, 
      const SharedCallAppearanceMakeExclusiveSuccessEvent& args) = 0;

   /**
    * ALU24.
    *
    * Callback invoked by the SDK when the make exclusive operation on a SCAP PUID has failed.
    *
    * @param scaSet the set that contains the affected SCAP PUID.
    * @param args information about the failure of the operation.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSharedCallAppearanceMakeExclusiveFailure(
      SipSharedCallAppearanceSetHandle scaSet, 
      const SharedCallAppearanceMakeExclusiveFailureEvent& args) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param scaSet the set the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(
         SipSharedCallAppearanceSetHandle scaSet, 
         const ErrorEvent& args) = 0;
};

}
}

#endif // CPCAPI2_SIP_SHARED_CALL_APPEARANCE_HANDLER_H
