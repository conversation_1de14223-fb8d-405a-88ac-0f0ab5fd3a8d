#pragma once

#if !defined(CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_H)
#define CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "SipSharedCallAppearanceTypes.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"

namespace CPCAPI2
{ 
namespace SipSharedCallAppearance 
{
class SipSharedCallAppearanceManager;

/**
 * State information of a SCAP PUID.
 */
struct SipSharedCallAppearanceState
{
   SipSharedCallAppearanceHandle sca;                         // SCAP PUID
   bool subscriptionStarted;                                  // True, subscription is started, false otherwise
   CPCAPI2::SipEvent::SipSubscriptionState subscriptionState; // State of the subscription
   cpc::vector<SipSharedCallAppearanceCallInfo> calls;        // Calls on the SCAP PUID
};

/**
 * State information of a set of SCAP PUIDs. 
 */
struct SipSharedCallAppearanceSetState
{
   CPCAPI2::SipAccount::SipAccountHandle account;       // Account associated with the set
   SipSharedCallAppearanceSetHandle scaSet;             // The set
   cpc::vector<SipSharedCallAppearanceState> scaStates; // The state information of the SCAP PUIDs part of the set
};

/**
 * Manager interface that provides a mechanism for obtaining up-to-date cached information
 * about a specific SCAP PUID or a set SCAP PUIDs; get a reference to the interface 
 * using the static method getInterface(), then call getState() or getCall() to obtain the 
 * information.
 */
class CPCAPI2_SHAREDLIBRARY_API SipSharedCallAppearanceStateManager
{
public:
   /**
    * Get a reference to the %SipSharedCallAppearanceStateManager interface.
    */
   static SipSharedCallAppearanceStateManager* getInterface(SipSharedCallAppearanceManager* scaManager);

   /**
    * Retrieve the state information of a SCAP PUID set.
    *
    * @param scaSet the set to query state information from.
    * @param scaSetState the state information of the set (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getState(
      SipSharedCallAppearanceSetHandle scaSet, 
      SipSharedCallAppearanceSetState& scaSetState) = 0;

   /**
    * Retrieve the state information of a SCAP PUID in a given set.
    *
    * @param scaSet the set that contains the SCAP PUID.
    * @param sca the SCAP PUID to query state information from.
    * @param scaState the state information of the SCAP PUID (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getState(
      SipSharedCallAppearanceSetHandle scaSet,
      SipSharedCallAppearanceHandle sca, 
      SipSharedCallAppearanceState& scaState) = 0;

   /**
    * Retrieve the call information of an appearance.
    *
    * @param scaSet the set that contains the appearance.
    * @param sca the SCAP PUID of the appearance that has the call.
    * @param appearance the appearance number to query call information from.
    * @param call the call information of the appearance (out param).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int getCall(
      SipSharedCallAppearanceSetHandle scaSet,
      SipSharedCallAppearanceHandle sca, 
      int appearance,
      SipSharedCallAppearanceCallInfo& call) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipSharedCallAppearanceStateManager.
    */
   virtual ~SipSharedCallAppearanceStateManager() {}
};

}
}

#endif // CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_H
