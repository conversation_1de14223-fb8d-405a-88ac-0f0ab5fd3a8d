#pragma once

#if !defined(CPCAPI2_SIP_SHARED_CALL_APPEARANCE_TYPES_H)
#define CPCAPI2_SIP_SHARED_CALL_APPEARANCE_TYPES_H

#include "cpcstl/string.h"
#include "dialogevent/SipDialogEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

// Typedefs
typedef unsigned int SipSharedCallAppearanceSetHandle;
typedef cpc::string SipSharedCallAppearanceHandle;

/**
 * ALU17.
 *
 * Settings that apply to a SCAP PUID.
 *
 * Used with SipSharedCallAppearanceManager::addSharedCallAppearance(..) to control the behaviour of the SDK
 * with respect to SCAP PUID related functionality.
 */
struct SipSharedCallAppearanceSettings
{
   bool allowScapHoldJoin;       // True to allow SCAP hold operation, false to disallow it
   bool allowScapBridgeIn;       // True to allow SCAP bridge-in operation, false to disallow it
   bool allowMakeExclusiveCalls; // true to allow make exclusive operation, false to disallow it

   SipSharedCallAppearanceSettings() 
   {
      allowScapHoldJoin = true;
      allowScapBridgeIn = true;
      allowMakeExclusiveCalls = true;
   }
};

/**
 * ALU17.
 *
 * Settings that apply to a set of SCAP PUIDs.
 *
 * Used with SipSharedCallAppearanceManager::createSharedCallAppearanceSet(..) to control the 
 * behaviour of the SDK with respect to SCAP related functionality.
 */
struct SipSharedCallAppearanceSetSettings
{
   int expires;                      // Expires value to use for subscriptions on SCAP PUIDs
   cpc::string scapHoldUri;          // ALU21: SIP URI of SCAP-Hold server
   cpc::string conferenceFactoryUri; // ALU18: SIP URI of conference-factory
   bool useSharedDialogParameter;    // Add shared parameter to dialog event package subscription

   SipSharedCallAppearanceSetSettings()
   {
      expires = 3600;
      useSharedDialogParameter = true; // required for ALU
   }
};

/**
 * Dialog/call information on a call appearance.
 * The appearance number is stored as an attribute of the dialog.
 */
struct SipSharedCallAppearanceCallInfo
{
   SipSharedCallAppearanceHandle sca;          // SCAP PUID
   CPCAPI2::SipDialogEvent::DialogInfo dialog; // Information on the call associated with the SCAP PUID
   bool isHeld;                                // True, the call is held, false otherwise
   bool isScapHeld;                            // True, the call is SCAP held, false otherwise
   bool isParked;                              // True, the call is parked, false otherwise
};

}
}

#endif // CPCAPI2_SIP_SHARED_CALL_APPEARANCE_TYPES_H
