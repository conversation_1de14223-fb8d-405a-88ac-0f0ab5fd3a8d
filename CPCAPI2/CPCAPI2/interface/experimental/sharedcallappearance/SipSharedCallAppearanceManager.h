#pragma once

#if !defined(CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_H)
#define CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipSharedCallAppearanceTypes.h"
#include "call/SipConversationTypes.h"

namespace CPCAPI2
{
class Phone;

namespace SipSharedCallAppearance
{
class SipSharedCallAppearanceHandler;

/**
 * Manages the subscriptions for all the sets of Shared Call Appearances (SCAP PUIDs) created 
 * with this entity. Each set contains a list of SCAP PUIDs to subscribe to. 
 *
 * A SCAP PUID can have zero to multiple appearances numbers. An appearance is characterized 
 * by its SCAP PUID and appearance number. Each appearance can have zero or one call.
 *
 * Call features can be performed on an appearance such as making an outbound call, receiving 
 * an inbound call, putting multiple appearances in a conference, bridging-in to a (remote) 
 * call on an appearance, putting a call on SCAP hold and off SCAP hold and making a call 
 * exclusive.
 *
 * The subscription mechanism allows this entity to monitor remote appearances. Notifications 
 * are generated as a result of call features being carried out on appearances, both for 
 * local and remote appearances.
 */
class CPCAPI2_SHAREDLIBRARY_API SipSharedCallAppearanceManager
{
public:
   /**
    * Get a reference to the %SipSharedCallAppearanceManager interface.
    */   
   static SipSharedCallAppearanceManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for shared call appearance events on the specified account. Set the handler
    * immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the same thread as 
    * SipAccountManager::process(..)
    *
    * @param account the user account to be associated with the handler.
    * @param handler the handler to register
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipSharedCallAppearanceHandler* handler) = 0;

   /**
    * ALU17.
    *
    * Create a set that contains a list of SCAP PUIDs to monitor.
    *
    * @param account the user account to be associated with the set.
    * @param settings the settings.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual SipSharedCallAppearanceSetHandle createSharedCallAppearanceSet(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const SipSharedCallAppearanceSetSettings& settings) = 0;

   /**
    * ALU17.
    *
    * Add a SCAP PUID to be monitored to a set of SCAP PUIDs.
    *
    * @param scaSet the set to add the line to.
    * @param sca the SCAP PUID to monitor.
    * @param scaSettings the settings for the SCAP PUID.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int addSharedCallAppearance(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SipSharedCallAppearanceHandle& sca, 
         const SipSharedCallAppearanceSettings& scaSettings) = 0;

   /**
    * ALU17.
    *
    * Start subscriptions on all the SCAP PUIDs included in the set.
    *
    * @param scaSet the set to use.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int start(SipSharedCallAppearanceSetHandle scaSet) = 0;

   /**
    * ALU17.
    *
    * End subscriptions on all the SCAP PUIDs included in the set.
    *
    * @param scaSet the set use.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int end(SipSharedCallAppearanceSetHandle scaSet) = 0;

   /**
    * ALU18.
    *
    * Make an outbound call to a remote party via an appearance.
    *
    * A new conversation is created for the outbound call and is started using 
    * SipConversationManager::start(). The caller of this method must be prepared to handle 
    * all SipConversationManager related events fired as a result. 
    *
    * A notification (SharedCallAppearanceStateChanged) is generated which contains
    * the allocated appearance number.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID to use to make the outbound call.
    * @param to the SIP address of the remote party.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle makeCall(
         SipSharedCallAppearanceSetHandle scaSet,
         const SipSharedCallAppearanceHandle& sca,
         const cpc::string& to,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) = 0;

   /**
    * ALU18.
    *
    * Make an outbound call to a remote party via an appearance.
    *
    * A new conversation is created for the outbound call and is started using
    * SipConversationManager::start(). The caller of this method must be prepared to handle
    * all SipConversationManager related events fired as a result.
    *
    * A notification (SharedCallAppearanceStateChanged) is generated which contains
    * the allocated appearance number.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID to use to make the outbound call.
    * @param to the SIP address of the remote party.
    * @param audioDescriptor audio options for the resulting call.
    * @param videoDescriptor video options for the resulting call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle makeCall(
         SipSharedCallAppearanceSetHandle scaSet,
         const SipSharedCallAppearanceHandle& sca,
         const cpc::string& to,
         const CPCAPI2::SipConversation::MediaInfo& audioDescriptor,
         const CPCAPI2::SipConversation::MediaInfo& videoDescriptor) = 0;

   /**
    * ALU18.
    *
    * Create a new conference by making an outbound call to the conference-factory 
    * via an appearance and transfer (local) calls on other appearances to the conference. 
    *
    * The address of the conference-factory is configured in SipSharedCallAppearanceSetSettings.
    *
    * A new conversation is created for the outbound call and is started using 
    * SipConversationManager::start(). Each local call is transferred using 
    * SipConversationManager::transfer(). The caller of this method must be prepared 
    * to handle all SipConversationManager related events fired as a result. 
    * 
    * A notification (SharedCallAppearanceStateChanged) is generated which contains
    * the allocated appearance number.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID to use to make the outbound conference call.
    * @param otherConversations the handles of the conversations to transfer to the conference.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle makeConferenceCall(
         SipSharedCallAppearanceSetHandle scaSet,
         const SipSharedCallAppearanceHandle& sca,
         const cpc::vector<CPCAPI2::SipConversation::SipConversationHandle>& otherConversations,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) = 0;

   /**
    * ALU21.
    *
    * Put an appearance that has a local call on SCAP hold. The conversation specified is 
    * transfered to the SCAP-Hold server. Any appearance can later pick up the SCAP held call by
    * using the SCAP join operation. The local call in question must be in the 'Confirmed' state.
    *
    * The address of the SCAP-Hold server is configured in the SipSharedCallAppearanceSetSettings.
    *
    * The conversation is transferred to the SCAP-Hold server using SipConversationManager::transfer().
    * The caller of this method must be prepared to handle all SipConversationManager related events 
    * fired as a result. 
    *
    * A notification (SharedCallAppearanceStateChanged) is generated which indicates that
    * the appearance in question is SCAP held.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID of the local call.
    * @param conversation the handle of the conversation (which represents the local call).
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int scapHold(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SipSharedCallAppearanceHandle& sca,
         CPCAPI2::SipConversation::SipConversationHandle conversation) = 0;

   /**
    * ALU22.
    *
    * Join an appearance that is SCAP held i.e. put a call off SCAP hold. 
    * This results into a local call being created.
    *
    * A new conversation is created and started using SipConversationManager::start().
    * The caller of this method must be prepared to handle all SipConversationManager 
    * related events fired as a result. 
    *
    * A notification (SharedCallAppearanceStateChanged) is generated which indicates that
    * the appearance in question is no longer SCAP held.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID of the appearance.
    * @param appearance the appearance number.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle scapJoin(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SipSharedCallAppearanceHandle& sca, 
         int appearance,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) = 0;
   virtual CPCAPI2::SipConversation::SipConversationHandle scapUnhold(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SipSharedCallAppearanceHandle& sca, 
         int appearance, 
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) { return scapJoin(scaSet, sca, appearance, mediaDescriptor); }

   /**
    * ALU23.
    *
    * Bridge into an appearance that has a remote call. The remote call in question must
    * be in the 'Confirmed' state. This results into a conference being setup and local call
    * being created.
    *
    * A new conversation is created and started using SipConversationManager::start().
    * The caller of this method must be prepared to handle all SipConversationManager related events 
    * fired as a result. 
    *
    * A notification (SharedCallAppearanceStateChanged) is generated which indicates that
    * the appearance in question now has a local call.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID of the appearance.
    * @param appearance the appearance number.
    * @param mediaDescriptor media options for the resulting call. Defaults to unsecured audio call.
    *
    * @return a handle to the new conversation or 0 if failed creating the conversation.
    */
   virtual CPCAPI2::SipConversation::SipConversationHandle scapBridgeIn(
         SipSharedCallAppearanceSetHandle scaSet, 
         const SipSharedCallAppearanceHandle& sca,
         int appearance,
         const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor = CPCAPI2::SipConversation::MediaInfo()) = 0;

   /**
    * ALU24.
    *
    * Change the exclusive state of a call on an appearance. The call in question must be 
    * in the 'Confirmed' state.
    *
    * A notification (SharedCallAppearanceMakeExclusiveSuccess or SharedCallAppearanceMakeExclusiveFailure) 
    * is generated which indicates whether the operation succeeded or not. Another notification 
    * (SharedCallAppearanceStateChanged) is generated to indicate the new state of the call on the appearance.
    *
    * @param scaSet the set that contains the SCAP PUID to use.
    * @param sca the SCAP PUID of the appearance.
    * @param appearance the appearance number.
    * @param exclusive true to make the call exclusive, false to make the call non-exclusive. Default to exclusive.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int makeExclusive(
         SipSharedCallAppearanceSetHandle scaSet,
         const SipSharedCallAppearanceHandle& sca,
         int appearance,
         bool exclusive = true) = 0;

   /**
    * Convenience method for retrieving the appearance number of a SCAP call by parsing the Alert-Info header
    * 
    * @param alertInfoHeader value of the Alert-Info header as provided by SipConversationHandler::NewConversationEvent
    * or SipConversationHandler::ConversationStateChangedEvent
    * @return the appearance number for the call, kError if not found
    */
   virtual int getAppearanceForScapCall(
      const cpc::string& alertInfoHeader) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipSharedCallAppearanceManager.
    */
   virtual ~SipSharedCallAppearanceManager() {}
};

}
}

#endif // CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_H
