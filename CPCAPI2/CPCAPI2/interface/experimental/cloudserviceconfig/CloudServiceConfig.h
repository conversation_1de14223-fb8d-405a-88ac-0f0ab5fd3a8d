#pragma once

#if !defined(CPCAPI2_ORCHESTRATION_SERVER_SERVICE_CONFIG_H)
#define CPCAPI2_ORCHESTRATION_SERVER_SERVICE_CONFIG_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "orchestration_server/OrchestrationServer.h"

namespace CPCAPI2
{

class Phone;

namespace CloudServiceConfig
{

struct ServiceConfigSettings
{
   cpc::string authServerUrl;
   cpc::string orchestrationServerUrl;
   cpc::string username;
   cpc::string password;
   cpc::string authToken; // local data
};

typedef unsigned int CloudServiceConfigHandle;

struct SetServerInfoResult
{
   bool success;
};

class CloudServiceConfigHandler
{

public:

   virtual ~CloudServiceConfigHandler() {}

   virtual int onSetServerInfoSuccess(CloudServiceConfigHandle configHandle, const SetServerInfoResult& args) = 0;
   virtual int onSetServerInfoFailure(CloudServiceConfigHandle configHandle, const SetServerInfoResult& args) = 0;

};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API CloudServiceConfigManager
{

public:

   /**
   * Get a reference to the %CloudServiceConfigManager interface.
   */   
   static CloudServiceConfigManager* getInterface(Phone* cpcPhone);

   virtual int setServerInfo(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) = 0;
   virtual int setServerTtl(const ServiceConfigSettings& serviceConfigSettings, const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) = 0;
   virtual int setHandler(CloudServiceConfigHandler* handler) = 0;

#ifdef CPCAPI2_AUTO_TEST
   virtual int stopTtlService() = 0;
   virtual int removeServerInfo(const CPCAPI2::OrchestrationServer::ServerInfo& serverInfo) = 0;
#endif

};

cpc::string get_debug_string(const CPCAPI2::CloudServiceConfig::ServiceConfigSettings& settings);

}

}

#endif // CPCAPI2_ORCHESTRATION_SERVER_SERVICE_CONFIG_H
