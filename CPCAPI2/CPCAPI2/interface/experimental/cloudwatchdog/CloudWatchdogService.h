#pragma once

#if !defined(CPCAPI2_CLOUD_WATCHDOG_SERVICE_H)
#define CPCAPI2_CLOUD_WATCHDOG_SERVICE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{

class Phone;

namespace CloudWatchdog
{

typedef unsigned int CloudWatchdogHandle;

class CloudWatchdogHandler;

struct ServerAndRegion
{
   cpc::string server;
   cpc::string region;

   ServerAndRegion() : server(""), region("") {}
   ServerAndRegion(cpc::string server_, cpc::string region_) : server(server_), region(region_) {}
};

struct CloudWatchdogConfig
{
   cpc::vector<ServerAndRegion> serversToMonitor;

   // Watchdog server monitoring frequency in seconds
   unsigned int monitorFrequency;

   // Triggers a callback for every server query response
   bool queryResponseEnabled;

   struct APNInfo
   {
      bool usePushKit;

      APNInfo() : usePushKit(false) {}
   } apnInfo;

   // Firebase (FCM) specific info
   struct FCMInfo
   {
      // placeholder
   } fcmInfo;

   struct WSInfo
   {
      // placeholder
   } wsInfo;

   CloudWatchdogConfig() : monitorFrequency(60), queryResponseEnabled(false) {}
   CloudWatchdogConfig(cpc::vector<ServerAndRegion>& serversToMonitor_, unsigned int monitorFrequency_, bool queryResponseEnabled_) : serversToMonitor(serversToMonitor_), monitorFrequency(monitorFrequency_), queryResponseEnabled(queryResponseEnabled_) {}
};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API CloudWatchdogService
{

public:

   /**
   * Get a reference to the %CloudWatchdog interface.
   */   
   static CloudWatchdogService* getInterface(Phone* cpcPhone);

   virtual int start(const CloudWatchdogConfig& serverConfig = CloudWatchdogConfig()) = 0;

   virtual int shutdown() = 0;

   virtual int setHandler(CloudWatchdogHandler* handler) = 0;

};

}

}

#endif // CPCAPI2_CLOUD_WATCHDOG_SERVICE_H
