#pragma once

#if !defined(CPCAPI2_CLOUD_WATCHDOG_SERVICE_HANDLER_H)
#define CPCAPI2_CLOUD_WATCHDOG_SERVICE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "interface/experimental/cloudwatchdog/CloudWatchdogService.h"

namespace CPCAPI2
{

namespace CloudWatchdog
{

struct MonitoredServerInfo
{
   ServerAndRegion server;
   cpc::vector<cpc::string> services;
   
   MonitoredServerInfo() : server("", "") {}
   MonitoredServerInfo(cpc::string server_, cpc::string region_, cpc::vector<cpc::string>& services_) : server(server_, region_), services(services_) {}
};

struct WatchdogStartEvent
{
   cpc::vector<MonitoredServerInfo> serverList;
};

struct ServerQueryResult
{
   cpc::vector<MonitoredServerInfo> serverList;
};

struct ServerUpEvent
{
   MonitoredServerInfo server;
};

struct ServerDownEvent
{
   MonitoredServerInfo server;
};

struct ServerShutdownEvent
{
   MonitoredServerInfo server;
};

struct SetServerInfoResult
{
   MonitoredServerInfo server;
   bool success;
};

struct NotificationSent
{
   enum ServerState
   {
      UP,
      DOWN
   };

   enum NotificationType
   {
      REGISTER
   };

   cpc::string server;
   ServerState state;
   NotificationType type;
   cpc::vector<cpc::string> endpointIds;

   NotificationSent() : server(""), state(UP), type(REGISTER) {}
   NotificationSent(cpc::string server_) : server(server_), state(UP), type(REGISTER) {}
   NotificationSent(cpc::string server_, const cpc::vector<cpc::string>& endpointIds_) : server(server_), state(UP), type(REGISTER), endpointIds(endpointIds_) {}
   NotificationSent(cpc::string server_, ServerState state_, NotificationType type_) : server(server_), state(state_), type(type_) {}
   NotificationSent(cpc::string server_, ServerState state_, NotificationType type_, const cpc::vector<cpc::string>& endpointIds_) : server(server_), state(state_), type(type_), endpointIds(endpointIds_) {}
};


/**
*/
class CloudWatchdogHandler
{

public:

   virtual ~CloudWatchdogHandler() {}

   /** Triggered when the watchdog service starts and receives its first query response. */
   virtual int onWatchdogStart(CloudWatchdogHandle handle, const WatchdogStartEvent& args) = 0;

   /** Triggered with every server query, only triggered if configured. */
   virtual int onServerQueryResult(CloudWatchdogHandle handle, const ServerQueryResult& args) = 0;

   /** Triggered when the watchdog detects a new server. */
   virtual int onServerUp(CloudWatchdogHandle handle, const ServerUpEvent& args) = 0;

   /** Triggered when the watchdog detects a down server. */
   virtual int onServerDown(CloudWatchdogHandle handle, const ServerDownEvent& args) = 0;

   /** Triggered when the watchdog detects a manually triggered server shutdown. */
   virtual int onServerShutdown(CloudWatchdogHandle handle, const ServerShutdownEvent& args) = 0;

   /** Triggered when the server info is reset by the watchdog. */
   virtual int onSetServerInfoResult(CloudWatchdogHandle handle, const SetServerInfoResult& args) = 0;

   /** Triggered when the push notification is sent to inform the endpoints of the server status changes. */
   virtual int onNotificationSent(CloudWatchdogHandle handle, const NotificationSent& args) = 0;

};

}

}

#endif // CPCAPI2_CLOUD_WATCHDOG_SERVICE_HANDLER_H
