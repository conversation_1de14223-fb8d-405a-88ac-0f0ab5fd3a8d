#pragma once

#if !defined(CPCAPI2_AUDIO_TRANS_H)
#define CPCAPI2_AUDIO_TRANS_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class Phone;

namespace AudioTrans
{
typedef unsigned int AudioTransHandle;

struct AudioTransSettings
{
   AudioTransSettings() : audioRecvChannel(-1) {}
   int audioRecvChannel;
};

struct AudioTranscriptionResult
{
   int resultSetId;
   int participantId;
   cpc::string result;
};

class AudioTransHandler
{
public:
   virtual ~AudioTransHandler() {}

   virtual int onTranscriptionResult(AudioTransHandle audioTransSession, const AudioTranscriptionResult& args) = 0;

};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API AudioTransManager
{
public:
   /**
   * Get a reference to the %AudioTransManager interface.
   */   
   static AudioTransManager* getInterface(Phone* cpcPhone);

   virtual int startAudioTransServer() = 0;
   virtual int stopAudioTransServer() = 0;
   virtual int setAudioTransServer(AudioTransManager* masterAudioTransManager) = 0;

   virtual AudioTransHandle createAudioTranscriptionSession() = 0;
   virtual int setAudioTransSettings(AudioTransHandle videoStream, const AudioTransSettings& settings) = 0;
   virtual int setAudioTransHandler(AudioTransHandle videoStream, AudioTransHandler* handler) = 0;
   virtual int startTranscription(AudioTransHandle videoStream) = 0;
   virtual int stopTranscription(AudioTransHandle videoStream) = 0;

};

}
}

#endif // CPCAPI2_AUDIO_TRANS_H
