#pragma once

#if !defined(CPCAPI2_ORCHESTRATION_SERVER_H)
#define CPCAPI2_ORCHESTRATION_SERVER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cloudconnector/ServiceDesc.h"

namespace CPCAPI2
{

class Phone;

namespace OrchestrationServer
{

class OrchestrationServerHandler;

struct OrchestrationServerConfig
{
   cpc::string redisIp;
   int redisPort;
   
   OrchestrationServerConfig() : redisIp(""), redisPort(6379) {}
};

struct ServerInfo
{
   cpc::string region;
   cpc::string uri;
   cpc::vector<cpc::string> services;
   bool watchdog;
   int64_t ttlSeconds;
   bool started; // Flag enabled when the server is starting up
   bool shutdown; // Flag enabled when the server is shutdown manually
   
   ServerInfo() : region(""), uri(""), watchdog(false), ttlSeconds(-1), started(false), shutdown(false) {}
};

/**
*/
class CPCAPI2_SHAREDLIBRARY_API OrchestrationServer
{

public:

   /**
   * Get a reference to the %OrchestrationServer interface.
   */   
   static OrchestrationServer* getInterface(Phone* cpcPhone);

   virtual int start(const OrchestrationServerConfig& serverConfig = OrchestrationServerConfig()) = 0;
   virtual int shutdown() = 0;

   virtual int setHandler(OrchestrationServerHandler* handler) = 0;

   virtual int setServerInfo(const ServerInfo& serverInfo) = 0;
   virtual int setServerTtl(const cpc::string& uri, uint64_t ttlMs) = 0;
   virtual int removeServer(const cpc::string& service, const cpc::string& region, const cpc::string& uri) = 0;

   // used by Watchdog service
   virtual int queryServers(const cpc::vector<CloudConnector::ServiceDesc>& services) = 0;
   virtual int queryServerTtl(const cpc::string& uri) = 0;
   virtual int queryServerUsers(const cpc::string& uri) = 0;

   // used by unit tests
   virtual int flushAll() = 0;

};

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::OrchestrationServerConfig& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::ServerInfo& args);

}

}

#endif // CPCAPI2_ORCHESTRATION_SERVER_H
