#pragma once

#if !defined(CPCAPI2_ORCHESTRATION_SERVER_HANDLER_H)
#define CPCAPI2_ORCHESTRATION_SERVER_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "OrchestrationServer.h"

namespace CPCAPI2
{

namespace OrchestrationServer
{

struct ServiceMapping
{
   cpc::string service;
   cpc::string uri;
};

struct SetServerInfoResult
{
   bool success;
   ServerInfo serverInfo;
};

struct RequestServiceResult
{
   cpc::vector<ServiceMapping> serviceMappings;
};

struct QueryServersResult
{
   cpc::vector<ServerInfo> servers;
};

struct QueryServerTtlResult
{
   cpc::string uri;
   int64_t expiresInMs;
};

struct QueryServerUsersResult
{
   cpc::string uri;
   cpc::vector<cpc::string> endpointIds;
};

/**
*/
class OrchestrationServerHandler
{

public:

   virtual ~OrchestrationServerHandler() {}

   virtual int onSetServerInfoResult(int requestHandle, const SetServerInfoResult& args) = 0;
   virtual int onRequestServiceResult(int requestHandle, const RequestServiceResult& args) = 0;
   virtual int onQueryServersResult(int requestHandle, const QueryServersResult& args) = 0;
   virtual int onQueryServerTtlResult(int requestHandle, const QueryServerTtlResult& args) = 0;
   virtual int onQueryServerUsersResult(int requestHandle, const QueryServerUsersResult& args) = 0;

};

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::ServiceMapping& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::SetServerInfoResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RequestServiceResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServersResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args);

}

}

#endif // CPCAPI2_ORCHESTRATION_SERVER_HANDLER_H
