#pragma once

#if !defined(CPCAPI2_WEB_CALL_HANDLER_H)
#define CPCAPI2_WEB_CALL_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include <call/SipConversationTypes.h>
#include "WebCallTypes.h"

namespace CPCAPI2
{ 
namespace WebCall
{
/**
 * Event passed in WebCallHandler::onNewConversation.
 * An outgoing INVITE has been submitted to the SIP stack for transmission, OR
 * an outgoing INVITE has forked, OR
 * an incoming INVITE has been received.
 */
struct NewConversationEvent
{
   ConversationState conversationState;
   ConversationType conversationType;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   cpc::vector<MediaInfo> localMediaInfo;
   cpc::vector<MediaInfo> remoteMediaInfo;
};

/**
 * Event passed in WebCallHandler::onConversationEnded().
 */
struct ConversationEndedEvent
{
   ConversationState conversationState;
   ConversationEndReason endReason;
   unsigned int sipResponseCode;
   cpc::string signallingEndEvent; // e.g. BYE, INVITE/487
   cpc::string signallingEndReason; // from SIP Reason header
};

/**
 * Event passed in WebCallHandler::onIncomingTransferRequest().
 *
 */
struct TransferRequestEvent
{
   cpc::string transferTargetAddress;
   cpc::string transferTargetDisplayName;
   WebCallHandle transferTargetConversation;
};

/**
 * Event passed in WebCallHandler::onTransferProgress.
 * Provides status updates once an attended transfer is initiated.
 * Taken from the NOTIFY w/Event: refer requests issued by the transferee.
 */
struct TransferProgressEvent
{
   TransferProgressEventType progressEventType;
   unsigned int sipResponseCode;
};

/**
 * Event passed in WebCallHandler::onIncomingRedirectRequest().
 * Incoming SIP 302 response received as a result of an outgoing INVITE.
 * Provides the SIP address of the party that the call is being redirected to. The SIP
 * address is in the format <protocol>:<AOR>, for example, sip:<EMAIL>.
 */
struct RedirectRequestEvent
{
   cpc::string targetAddress;
};

/**
 * Event passed in WebCallHandler::onIncomingTargetChangeRequest().
 */
struct TargetChangeRequestEvent
{
   cpc::string targetAddress;
};

/**
 * Event passed in WebCallHandler::onConversationStateChangeRequest(); 
 * this event indicates the remote end sent a request or response that involves
 * SIP functionality that the SDK does not support.
 */
struct ConversationStateChangeRequestEvent
{
};

/**
 * Event passed in WebCallHandler::onConversationStateChanged().
 * The conversation state changed as a result of a local or remote action.
 * See #ConversationState for a list of possible states.
 */
struct ConversationStateChangedEvent
{
   ConversationState conversationState;
};

/**
* Event passed in WebCallHandler::onConversationMediaChangeRequest().
* The remote party wants to change the set of media and/or directions of media,
* or requires the local party to send a media offer.
*/
struct ConversationMediaChangeRequestEvent
{
   cpc::vector<MediaInfo> remoteMediaInfo;
};

/**
* Event passed in WebCallHandler::onConversationMediaChanged().
* Gives the updated local media state after a successful or unsuccessful offer/answer exchange.
*/
struct ConversationMediaChangedEvent
{
   cpc::vector<MediaInfo> localMediaInfo;
   cpc::vector<MediaInfo> remoteMediaInfo;
   bool localHold;
   bool remoteHold;
};

/**
* Event passed in WebCallHandler::onError(), used to report general SDK error 
* conditions, such as invalid handles, or to report cases 
* where the call is not in a valid state for the requested peration.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Event passed in WebCallHandler::onConversationStatisticsUpdated().
 * Holds updated conversation statistics after refreshConversationStatistics() is called.
 */
struct ConversationStatisticsUpdatedEvent
{
   ConversationStatistics conversationStatistics;
};

/**
* Handler for events relating to incoming, outgoing, and established 
* phone calls; set in SipConversationManager::setHandler().
*/
class WebCallHandler
{
public:
   virtual int onNewConversation(WebCallHandle conversation, const NewConversationEvent& args) = 0;

   virtual int onConversationEnded(WebCallHandle conversation, const ConversationEndedEvent& args) = 0;

   virtual int onIncomingTransferRequest(WebCallHandle conversation, const TransferRequestEvent& args) = 0;
   virtual int onIncomingRedirectRequest(WebCallHandle conversation, const RedirectRequestEvent& args) = 0;
   virtual int onIncomingTargetChangeRequest(WebCallHandle conversation, const TargetChangeRequestEvent& args) = 0;
   virtual int onTransferProgress(WebCallHandle conversation, const TransferProgressEvent& args) = 0;

   virtual int onConversationStateChangeRequest(WebCallHandle conversation, const ConversationStateChangeRequestEvent& args) = 0;
   virtual int onConversationStateChanged(WebCallHandle conversation, const ConversationStateChangedEvent& args) = 0;

   virtual int onConversationMediaChangeRequest(WebCallHandle conversation, const ConversationMediaChangeRequestEvent& args) = 0;
   virtual int onConversationMediaChanged(WebCallHandle conversation, const ConversationMediaChangedEvent& args) = 0;

   virtual int onConversationStatisticsUpdated(WebCallHandle conversation, const ConversationStatisticsUpdatedEvent& args) = 0;

   virtual int onError(WebCallHandle subscription, const ErrorEvent& args) = 0;
};

}
}

#endif // CPCAPI2_WEB_CALL_HANDLER_H
