#pragma once

#if !defined(CPCAPI2_WEB_CALL_MANAGER_H)
#define CPCAPI2_WEB_CALL_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "WebCallTypes.h"

namespace CPCAPI2
{ 
class Phone;

namespace WebCall 
{
class WebCallHandler;

/**
* Manager interface to control incoming, outgoing and established phone calls; 
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API WebCallManager
{
public:
   /**
   * Get a reference to the %WebCallManager interface.
   */   
   static WebCallManager* getInterface(Phone* cpcPhone);

   virtual int process(unsigned int timeout) = 0;

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kWebCallModuleDisabled = -1;

   /**
   * Set the handler for call and navigation events.
   */
   virtual int setHandler(WebCallHandler* handler) = 0;

   virtual WebNavigationHandle navigate(const cpc::string& targetURL) = 0;
   virtual int closeWebSession(WebNavigationHandle webSession) = 0;

   /**
   * Allocates a new call within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing call.
   */
   virtual WebCallHandle createConversation(WebNavigationHandle context) = 0;

   /**
   * Adds a participant to the call.  Call this function after createConversation(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int addParticipant(WebCallHandle conversation, const cpc::string& targetAddress) = 0;

   /**
   * Changes the set of media offered by the SDK.  May be called:
   * <ul>
   * <li>on initial outgoing conversation, prior to calling start(), in order to set the media offer.
   * <li>on an incoming conversation, prior to calling accept(), in order 
   * to provide a counter-offer for the media.
   * <li> on an already established call, prior to calling sendMediaChangeRequest(),
   * in order to change the media.
   */
   virtual int configureMedia(WebCallHandle conversation, const MediaInfo& mediaDescriptor) = 0;

   /**
   * Enables or disables a particular media type for the conversation.
   * Note: This is a simple convenience function and has the same effect as manually specifying media
   * information using configureMedia(..)
   * May be invoked prior to starting the conversation, or after start(..) has already been invoked.
   * Used prior to calling sendMediaChangeRequest(..) to send a re-INVITE request with a new SDP offer reflecting the
   * enabled/disabled states of the media streams on the call.
   */
   virtual int setMediaEnabled(WebCallHandle conversation, MediaType mediaType, bool enabled) = 0;

   /**
   * Use this function to enable RFC 3323 privacy mechanisms for an outgoing call prior to calling start(..).
   */
   virtual int setAnonymousMode(WebCallHandle conversation, unsigned int anonymousMode) = 0;

   /**
   * Initiates an outgoing call by sending an INVITE to the remote participant (see addParticipant(..)).
   */
   virtual int start(WebCallHandle conversation) = 0;

   /**
   * Places the call on hold.  Results in an outgoing re-INVITE request with media attributes
   * set such that remote media is no longer received.  Regardless of the success/failure of the re-INVITE
   * transaction, the SDK will cease sending media from the mic/camera to the remote party.
   */
   virtual int hold(WebCallHandle conversation) = 0;

   /**
   * Takes the call off hold.  See hold(..).
   */
   virtual int unhold(WebCallHandle conversation) = 0;

   /**
   * Sends a re-INVITE requesting a change in the set of negotiated media streams.
   * For example, can be used to upgrade an audio-only call to audio-video.
   * Call this function after calling setMedia(..).
   */
   virtual int sendMediaChangeRequest(WebCallHandle conversation) = 0;

   /**
   * Ends a call.  Sends an outgoing BYE or CANCEL request depending on the current conversation state.
   */
   virtual int end(WebCallHandle conversation) = 0;

   /**
   * Redirects an incoming call to a new target address.  Sends a SIP 302 response.
   */
   virtual int redirect(WebCallHandle conversation, const cpc::string& targetAddress) = 0;

   /**
   * Used after receiving an incoming call to indicate to the remote end that the local user agent is 'ringing'.
   */
   virtual int sendRingingResponse(WebCallHandle conversation) = 0;

   /**
   * Used after receiving an incoming call to reject the INVITE session offered by the remote party.
   * @param conversation The incoming call to reject.
   * @param rejectReason The SIP response code sent to the caller. 0 will automatically select 486 for INVITE, 488 for RE-INVITE
   */
   virtual int reject(WebCallHandle conversation, unsigned int rejectReason=0) = 0;

   /**
   * Used to answer an incoming call (200 OK with SDP answer).
   */
   virtual int accept(WebCallHandle conversation) = 0;

   /**
   * Accept an incoming transfer request.  Results in a new outgoing call to the transfer target.
   * See TransferRequestEvent.
   */
   virtual int acceptIncomingTransferRequest(WebCallHandle transferTargetConversation) = 0;

   /**
   * Reject an incoming transfer request.
   */
   virtual int rejectIncomingTransferRequest(WebCallHandle transferTargetConversation) = 0;

   /**
   * Transfers the call specified as the transfereeConversation to the remote party of the transferTargetConversation.
   * This is a SIP attended transfer.
   * Typically the transferee would be "on hold", and the transferTargetConversation would be "established"
   * prior to calling this function.
   */
   virtual int transfer(WebCallHandle transferTargetConversation, WebCallHandle transfereeConversation) = 0;

   /**
   * Transfers the call specified as the transfereeConversation to the address specified by targetAddress.
   * This is a SIP basic transfer.
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int transfer(WebCallHandle transfereeConversation, const cpc::string& targetAddress) = 0;

   /**
    * Assigns a preference to the specified DtmfMode, where ordinal 0 is the DtmfMode that will
    * be used first (whenever possible).  Other modes (ordinal 1, 2, ...) will be attempted under
    * the following conditions:
    * RFC 2833:  failure to negotiate the telephone-event codec in SDP
    * In Band:  negotiation of a voice codec such as G.729 which distorts inband DTMF
    * SIP INFO:  reception of an error response after sending a DTMF INFO to the remote target
    *
    * Specify ordinal 0xFFFFFFFF to completely clear the preferred order.
    *
    * @param account SipAccount to apply the DTMF mode preference to.
    * @param ordinal Priority ordering of the DTMF mode relative to other DTMF modes.
    * @param dtmfMode One of the values from the DtmfMode enumeration.
    */
   virtual int setDtmfMode(unsigned int ordinal, DtmfMode dtmfMode) = 0;

   /**
    * Starts playing a DTMF tone, according to the DTMF mode selected for the SipAccount via setDtmfMode(..).
    * @param conversation Conversation to play the tone for.  May be 0 if only local playback is desired.
    * @param toneId See http://tools.ietf.org/html/rfc4733#section-3.2 for toneId semantics.
    * @param playLocally True to play the DTMF tone to the local Audio playout device.
    */
   virtual int startDtmfTone(WebCallHandle conversation, unsigned int toneId, bool playLocally) = 0;

   /**
    * Stops playing all DTMF tones.
    */
   virtual int stopDtmfTone() = 0;

   virtual int refreshConversationStatistics(WebCallHandle conversation) = 0;

};

}
}

#endif // CPCAPI2_WEB_CALL_MANAGER_H
