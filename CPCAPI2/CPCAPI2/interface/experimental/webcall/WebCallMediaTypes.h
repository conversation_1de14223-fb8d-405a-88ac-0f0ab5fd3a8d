#pragma once

#if !defined(CPCAPI2_WEB_CALL_MEDIA_TYPES_H)
#define CPCAPI2_WEB_CALL_MEDIA_TYPES_H

namespace CPCAPI2
{
namespace WebCall
{
/**
* A struct. 
*/ 
   struct AudioCodec
   {
      int   pltype;
      char  plname[32];
      int   plfreq;
      int   pacsize;
      int   channels;
      int   rate;

      AudioCodec()
      {
         pltype = 0;
         plname[0] = 0;
         plfreq = 0;
         pacsize = 0;
         channels = 0;
         rate = 0;
      }
   };

/**
* A struct. 
*/ 
   struct VideoCodec
   {
      char                    plName[32];
      unsigned char           plType;

      unsigned short          width;
      unsigned short          height;

      unsigned int            startBitrate;
      unsigned int            maxBitrate;
      unsigned int            minBitrate;
      unsigned char           maxFramerate;

      VideoCodec()
      {
         plName[0] = 0;
         plType = 0;
         width = 0;
         height = 0;
         startBitrate = 0;
         maxBitrate = 0;
         minBitrate = 0;
         maxFramerate = 0;
      }
   };

/**
* A struct. 
*/ 
   struct AudioStatistics
   {
      AudioCodec     codec;

      unsigned short fractionLost;
      unsigned int   cumulativeLost;
      unsigned int   extendedMax;
      unsigned int   jitterSamples;
      int            rttMs;
      int            bytesSent;
      int            packetsSent;
      int            bytesReceived;
      int            packetsReceived;

      unsigned int   maxJitterMs;
      unsigned int   averageJitterMs;
      unsigned int   discardedPackets;
   };

/**
* A struct. 
*/ 
   struct VideoStatistics
   {
      VideoCodec      codec;

      unsigned int    bytesSent;
      unsigned int    packetsSent;
      unsigned int    bytesReceived;
      unsigned int    packetsReceived;

      unsigned short  inFractionLost;
      unsigned int    inCumulativeLost;
      unsigned int    inExtendedMax;
      unsigned int    inJitter;
      int             inRttMms;
      unsigned short  outFractionLost;
      unsigned int    outCumulativeLost;
      unsigned int    outExtendedMax;
      unsigned int    outJitter;
      int             outRttMms;

      unsigned int    totalBitrateSent;
      unsigned int    videoBitrateSent;
      unsigned int    fecBitrateSent;
      unsigned int    nackBitrateSent;
   };
}
}
#endif // CPCAPI2_WEB_CALL_MEDIA_TYPES_H