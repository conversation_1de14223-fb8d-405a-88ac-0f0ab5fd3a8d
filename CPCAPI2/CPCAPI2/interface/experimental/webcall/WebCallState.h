#pragma once

#if !defined(CPCAPI2_WEB_CALL_STATE_H)
#define CPCAPI2_WEB_CALL_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "WebCallTypes.h"

namespace CPCAPI2
{ 
namespace WebCall 
{
class WebCallManager;

/**
* Struct that is read using WebCallStateManager::getState().
*/ 
struct WebCallState
{
   CPCAPI2::SipAccount::SipAccountHandle account;
   ConversationState conversationState;
   ConversationType conversationType;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   cpc::vector<MediaInfo> remoteMediaInfo;
   cpc::vector<MediaInfo> localMediaInfo;
   bool localHold;
   bool remoteHold;
   ConversationEndReason endReason;
   ConversationStatistics statistics;

   WebCallState()
   {
      account = 0;
      conversationState = ConversationState_None;
      conversationType = ConversationType_Incoming;
      remoteAddress = "";
      remoteDisplayName = "";
      localHold = false;
      remoteHold = false;
      endReason = ConversationEndReason_Unknown;
   }
};
/**
* Manager interface that provides a mechanism for obtaining the 
* WebCallState struct that provides information about a 
* specific conversation; get a reference to the interface using the 
* static method getInterface(), then call getState() to obtain the struct.
*/
class CPCAPI2_SHAREDLIBRARY_API WebCallStateManager
{
public:
   /**
   * Get a reference to the %WebCallStateManager interface.
   */   
   static WebCallStateManager* getInterface(WebCallManager* cpcConvMan);

   virtual int getState(WebCallHandle h, WebCallState& conversationState) = 0;
};

}
}

#endif // CPCAPI2_WEB_CALL_STATE_H
