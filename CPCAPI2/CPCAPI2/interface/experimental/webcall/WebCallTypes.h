#pragma once

#if !defined(CPCAPI2_WEB_CALL_TYPES_H)
#define CPCAPI2_WEB_CALL_TYPES_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include "WebCallMediaTypes.h"

namespace CPCAPI2
{
namespace WebCall
{
typedef unsigned int WebCallHandle;
typedef unsigned int WebNavigationHandle;

enum ConversationState
{
   ConversationState_None                 = 0,
	ConversationState_LocalOriginated		= 1000, 
	ConversationState_RemoteOriginated		= 1010, 
	ConversationState_RemoteRinging			= 1020, 
	ConversationState_LocalRinging			= 1030, 
	ConversationState_Connected				= 1040, 
	ConversationState_Early					   = 1050,
   ConversationState_Ended                = 1060
};

enum ConversationType
{
	ConversationType_Incoming					      = 1200,
	ConversationType_Outgoing					      = 1210,
	ConversationType_IncomingJoinRequest			= 1220,
	ConversationType_IncomingTransferRequest		= 1230
};

enum ConversationEndReason
{
	ConversationEndReason_Unknown					      = 1100,
	ConversationEndReason_UserTerminatedLocally		= 1110,
	ConversationEndReason_UserTerminatedRemotely		= 1120,
   ConversationEndReason_ServerError		         = 1130,
   ConversationEndReason_ServerRejected            = 1140
};

enum TransferProgressEventType
{
   TransferProgressEventType_Trying                = 1300,
   TransferProgressEventType_Ringing               = 1310,
   TransferProgressEventType_Connected             = 1320,
   TransferProgressEventType_Redirected            = 1330,
   TransferProgressEventType_Failed                = 1340
};

enum MediaType
{
    MediaType_Audio            = 0x1,
    MediaType_Video            = 0x2
};

enum MediaDirection
{
    MediaDirection_None           = 0,
    MediaDirection_SendReceive    = 1,
    MediaDirection_SendOnly       = 2,
    MediaDirection_ReceiveOnly    = 3,
    MediaDirection_Inactive       = 4
};

enum MediaEncryptionMode
{
    MediaEncryptionMode_Unencrypted          = 0x1,
    MediaEncryptionMode_SRTP_SDES_Encrypted  = 0x2,
    MediaEncryptionMode_SRTP_DTLS_Encrypted  = 0x4
};

/**
* A struct. 
*/ 
struct MediaEncryptionOptions
{
    MediaEncryptionMode     mediaEncryptionMode;
};

/**
* A struct. 
*/ 
struct MediaInfo
{
    MediaType               mediaType;
    MediaDirection          mediaDirection;
    MediaEncryptionOptions  mediaEncryptionOptions;
    AudioCodec              audioCodec;
    VideoCodec              videoCodec;

    MediaInfo()
    {
       mediaType = MediaType_Audio;
       mediaDirection = MediaDirection_None;
       mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
    }
};

enum DtmfMode
{
   DtmfMode_RFC2833          = 1,
   DtmfMode_InBand           = 2,
   DtmfMode_SIP_INFO         = 3,
   DtmfMode_RFC2833_InBand   = 4,
   DtmfMode_RFC2833_SIP_INFO = 5,
   DtmfMode_InBand_SIP_INFO  = 6,
   DtmfMode_Everything       = 7
};

enum NatTraversalMode
{
   NatTraversalMode_None   = 0,
   NatTraversalMode_Auto   = 1,  /* attempt ICE if a STUN server is configured and the remote end supports ICE */
   NatTraversalMode_STUN   = 2,
   NatTraversalMode_TURN   = 3
};

/**
* A struct. Holds conversation media information
*/
struct ConversationStatistics
{
   cpc::vector<AudioStatistics> audioChannels;
   cpc::vector<VideoStatistics> videoChannels;
};

}

/**
* A struct. Used with SipConversationManager::setDefaultSettings(..) to control the behaviour of the SDK
* with respect to INVITE-session-related functionality.
*/
struct WebCallSettings
{
   /**
   * Sets the session name that appears in SDP bodies sent by the SDK.
   */
   cpc::string                           sessionName;

   /**
   * Sets the NAT traversal mode used for media.
   */
   WebCall::NatTraversalMode      natTraversalMode;

   /**
   * Sets the address of the NAT traversal server (STUN or TURN).
   * By default the SDK will obtain this address from DNS SRV records, however the application can override
   * the value from DNS by specifying a value here, or "server.none.phone.config" to prevent client side media NAT traversal.
   */
   cpc::string                           natTraversalServer;

   WebCallSettings()
   {
      sessionName = " ";
      natTraversalMode = WebCall::NatTraversalMode_None;
      natTraversalServer = "";
   }
};

}

#endif // CPCAPI2_WEB_CALL_TYPES_H
