#pragma once

#if !defined(CPCAPI2_AUTH_PROVIDER_H)
#define CPCAPI2_AUTH_PROVIDER_H

#include "cpcapi2defs.h"


namespace CPCAPI2
{
class Phone;

namespace Auth
{

class CPCAPI2_SHAREDLIBRARY_API AuthProvider
{
public:
   /**
   * Get a reference to the %AuthProvider interface.
   */   
   static AuthProvider* getInterface(Phone* phone);


   /*
    * The SDK will manage memory life of %AuthProvider.
    */
protected:
   virtual ~AuthProvider() {}
};

}
}
#endif // CPCAPI2_MEDIA_MANAGER_H
