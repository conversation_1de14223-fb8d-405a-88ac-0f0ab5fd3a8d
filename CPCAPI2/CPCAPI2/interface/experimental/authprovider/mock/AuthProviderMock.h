#pragma once

#if !defined(CPCAPI2_AUTH_PROVIDER_MOCK_H)
#define CPCAPI2_AUTH_PROVIDER_MOCK_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
class AuthProvider;

namespace Auth
{

class CPCAPI2_SHAREDLIBRARY_API AuthProviderMock
{
public:
   /**
   * Get a reference to the %AuthProviderStretto interface.
   */   
   static AuthProviderMock* getInterface(AuthProvider* cpcPhone);

   virtual cpc::string providerName() = 0;
   virtual int login(const cpc::string& username) = 0;

   /*
    * The SDK will manage memory life of %AuthProviderMock.
    */
protected:
   virtual ~AuthProviderMock() {}
};

}
}
#endif // CPCAPI2_AUTH_PROVIDER_MOCK_H
