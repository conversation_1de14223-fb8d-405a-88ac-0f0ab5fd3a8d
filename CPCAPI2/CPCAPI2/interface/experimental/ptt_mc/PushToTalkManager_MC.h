#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MANAGER_MC_H
#define CPCAPI2_PUSHTOTALK_MANAGER_MC_H

#include "cpcapi2defs.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"

namespace CPCAPI2
{

class Phone;

namespace PushToTalk
{

class PushToTalkHandler;

/**
===================================================================================================
PTT SENDER
                                          +----------------+
                                          |                |
                                          |    Idle        +<---+
                                          |                |    |
                                          +-------+--------+    |
                                                  |             |
                                         start()  |             |
                                                  v             |
                                          +-------+--------+    |
                                          |                |    |
                                          |    Initiated   |    |
                                          |                |    |
                                          +-------+--------+    |
                       audio input device ready,  |             |
                       INVITE sessions connected  |             |
                                                  v             |
+----------------+    startTalkSpurt()    +-------+--------+    |
|                +<-----------------------+                |    |
|   Talking      |                        |    Active      |    |
|                +----------------------->+                |    |
+-------+--------+     endTalkSpurt()     +-------+--------+    |
        |                                         |             |
        |                                  end()  |             |
  end() |                                         v             |
        |                                 +-------+--------+    |  audio playout finished,
        |                                 |                |    |  BYEs sent
        +-------------------------------->+    Ending      +----+
                                          |                |
                                          +----------------+


==================================================================================================
PTT RECEIVER

                                                       +----------------+
                                                       |                |
                                                       |     Idle       +<---+
                                                       |                |    |
                                                       +-------+--------+    |
                                                               |             |
                                     incoming INVITE received  |             |
                                                               v             |
                                                       +-------+--------+    |
                                                       |                |    |
                                                       |   Initiated    |    |
                                                       |                |    |
                                                       +-------+--------+    |
                                    audio input device ready,  |             |
                                    INVITE sessions connected  |             | audio playout finished
                                                               v             |
             +----------------+    startTalkSpurt()    +-------+--------+    |
             |                +<-----------------------+                |    |
             |   Talking      |                        |    Active      |    |
             |                +----------------------->+                |    |
             +-------+--------+     endTalkSpurt()     +-------+--------+    |
                     |                                         |             |
                     |                   end() / received BYE  |             |
end() / received BYE |                                         v             |
                     |                                 +-------+--------+    |
                     |                                 |                |    |
                     +-------------------------------->+    Ending      +----+
                                                       |                |
                                                       +----------------+

*/
class CPCAPI2_SHAREDLIBRARY_API PushToTalkManagerMc : public PushToTalkManager
{
public:
   /**
    * Get a reference to the %PushToTalkManagerMc interface.
   */   
   static PushToTalkManagerMc* getInterface(Phone* cpcPhone);

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MANAGER_MC_H
