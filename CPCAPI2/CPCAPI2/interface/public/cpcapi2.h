#pragma once

#if !defined(CPCAPI2_H)
#define CPCAPI2_H

#include "cpcapi2defs.h"

#include "phone/Phone.h"
#include "phone/PhoneErrorHandler.h"
#include "phone/PhoneHandler.h"

// CPCAPI2_BRAND_ACCOUNT_MODULE
#include "account/SipAccount.h"
#include "account/SipAccountHandler.h"
#include "account/SipAccountSettings.h"

// CPCAPI2_BRAND_ACCOUNT_STATE_MODULE
#include "account/SipAccountState.h"

// CPCAPI2_BRAND_IM_MODULE
#include "im/SipInstantMessage.h"
#include "im/SipInstantMessageHandler.h"

// CPCAPI2_BRAND_CALL_MODULE
#include "call/SipConversationManager.h"
#include "call/SipConversationTypes.h"
#include "call/SipConversationHandler.h"

// CPCAPI2_BRAND_CALL_STATE_MODULE
#include "call/SipConversationState.h"

// CPCAPI2_BRAND_MEDIA_MODULE
#include "media/MediaManager.h"
#include "media/audio/AndroidAudio.h"
#include "media/audio/IOSAudio.h"
#include "media/audio/Audio.h"
#include "media/audio/AudioHandler.h"
#include "media/audio/CodecSettings.h"
#include "media/video/Video.h"
#include "media/video/VideoHandler.h"
#include "media/video/VideoCodecSettings.h"

// CPCAPI2_BRAND_SIP_EVENT_MODULE
#include "event/SipEventManager.h"
#include "event/SipPublicationManager.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"

#include "presence/SipPresenceManager.h"
#include "presence/SipPresenceSubscriptionHandler.h"
#include "presence/SipPresencePublicationHandler.h"

// CPCAPI2_BRAND_SIP_MWI_MODULE
#include "mwi/SipMessageWaitingIndicationManager.h"
#include "mwi/SipMessageWaitingIndicationHandler.h"

//CPCAPI2_NETWORK_CHANGE_MODULE
#include "phone/NetworkChangeManager.h"
#include "phone/NetworkChangeHandler.h"

// CPCAPI2_XMPP_ACCOUNT_MODULE
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppAccountHandler.h"
#include "xmpp/XmppAccountSettings.h"

// CPCAPI2_BRAND_XMPP_STATE_MODULE
#include "xmpp/XmppAccountState.h"

// CPCAPI2_XMPP_ROSTER_MODULE
#include "xmpp/XmppRoster.h"
#include "xmpp/XmppRosterHandler.h"
#include "xmpp/XmppRosterTypes.h"

// CPCAPI2_BRAND_XMPP_ROSTER_MODULE
#include "xmpp/XmppRosterState.h"

// CPCAPI2_BRAND_XMPP_CHAT_MODULE
#include "xmpp/XmppChatManager.h"
#include "xmpp/XmppChatHandler.h"

// CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE
#include "xmpp/XmppFileTransferManager.h"
#include "xmpp/XmppFileTransferHandler.h"
#include "xmpp/XmppFileTransferState.h"

// CPCAPI2_BRAND_XMPP_VCARD_MODULE
#include "xmpp/XmppVCardManager.h"
#include "xmpp/XmppVCardHandler.h"
#include "xmpp/XmppVCardState.h"
#include "xmpp/XmppVCardTypes.h"

// CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE
#include "xmpp/XmppMultiUserChatManager.h"
#include "xmpp/XmppMultiUserChatHandler.h"
#include "xmpp/XmppMultiUserChatState.h"
#include "xmpp/XmppMultiUserChatTypes.h"

// CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE
#include "xmpp/XmppIMCommandManager.h"
#include "xmpp/XmppIMCommandHandler.h"

// CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE
#include "strettoprovisioning/StrettoProvisioningTypes.h"
#include "strettoprovisioning/StrettoProvisioning.h"
#include "strettoprovisioning/StrettoProvisioningHandler.h"

// CPCAPI2_BRAND_STRETTO_UEM_MODULE
#include "strettouem/StrettoUem.h"
#include "strettouem/StrettoUemTypes.h"
#include "strettouem/StrettoUemHandler.h"

// CPCAPI2_BRAND_CALL_QUALITY_REPORTING_MODULE
#include "callquality/CallQualityReportTypes.h"
#include "callquality/CallQualityReportHandler.h"
#include "callquality/CallQualityReportManager.h"

#if defined(CPCAPI2_INCLUDE_UNRELEASED_HEADERS)

// CPCAPI2_BRAND_AUDIO_EXT_MODULE
#include "../experimental/audio_ext/AudioExt.h"

// CPCAPI2_BRAND_VIDEO_EXT_MODULE
#include "../experimental/video_ext/VideoExt.h"

// CPCAPI2_XCAP_MODULE
#include "../experimental/xcap/XcapSubscriptionHandler.h"
#include "../experimental/xcap/XcapPublicationHandler.h"
#include "../experimental/xcap/XcapResourceListTypes.h"
#include "../experimental/xcap/XcapResourceListManager.h"
#include "../experimental/xcap/XcapResourceListHandler.h"
#include "../experimental/xcap/XcapSettings.h"

// CPCAPI2_GENBAND_MODULE
#include "../experimental/genband/GenbandRestAPIInterface.h"
#include "../experimental/genband/GenbandRestAPIHandler.h"
#include "../experimental/genband/GenbandWatcherinfoExt.h"

// CPCAPI2_SIP_PRESENCE_CONFIGURATION_MODULE
#include "../experimental/sippresenceconfiguration/SipPresenceConfigurationManager.h"

// CPCAPI2_GENBAND_SOPI_MODULE
#include "../experimental/genband/GenbandSopiManager.h"
#include "../experimental/genband/GenbandSopiHandler.h"

// CPCAPI2_PEER_CONNECTION_MODULE
#include "../experimental/peerconnection/PeerConnectionManager.h"
#include "../experimental/peerconnection/PeerConnectionHandler.h"

// CPCAPI2_WEB_CALL_MODULE
#include "../experimental/webcall/WebCallState.h"
#include "../experimental/webcall/WebCallTypes.h"
#include "../experimental/webcall/WebCallManager.h"
#include "../experimental/webcall/WebCallHandler.h"

// CPCAPI2_CAPABILITY_DISCOVERY_MODULE
#include "../experimental/capability/RcsCapabilityDiscovery.h"
#include "../experimental/capability/RcsCapabilityDiscoveryHandler.h"

// CPCAPI2_PROVISION_MODULE
#include "../experimental/provision/RcsProvision.h"
#include "../experimental/provision/RcsProvisionHandler.h"

// CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
#include "../experimental/dialogevent/SipDialogEventSubscriptionManager.h"
#include "../experimental/dialogevent/SipDialogEventSubscriptionHandler.h"
#include "../experimental/dialogevent/SipDialogEventPublicationManager.h"
#include "../experimental/dialogevent/SipDialogEventPublicationHandler.h"

// CPCAPI2_BRAND_SIP_BUSY_LAMP_FIELD_MODULE
#include "../experimental/busylampfield/SipBusyLampFieldManager.h"
#include "../experimental/busylampfield/SipBusyLampFieldState.h"
#include "../experimental/busylampfield/SipBusyLampFieldHandler.h"
#include "../experimental/busylampfield/SipBusyLampFieldTypes.h"

// CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE
#include "../experimental/sharedcallappearance/SipSharedCallAppearanceManager.h"
#include "../experimental/sharedcallappearance/SipSharedCallAppearanceState.h"
#include "../experimental/sharedcallappearance/SipSharedCallAppearanceHandler.h"

// CPCAPI2_BRAND_SIP_CHAT_MODULE
#include "../experimental/chat/SipChatManager.h"
#include "../experimental/chat/SipChatHandler.h"

// CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE
#include "../experimental/standalonemessaging/SipStandaloneMessagingManager.h"
#include "../experimental/standalonemessaging/SipStandaloneMessagingHandler.h"

// CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE
#include "../experimental/filetransfer/SipFileTransferManager.h"
#include "../experimental/filetransfer/SipFileTransferHandler.h"
#include "../experimental/filetransfer/SipFileTransferState.h"

// CPCAPI2_BRAND_SIP_REG_EVENT_MODULE
#include "../experimental/regevent/SipRegEventManager.h"
#include "../experimental/regevent/SipRegEventHandler.h"

// CPCAPI2_TERADICI_AUDIO_MODULE
#include "../experimental/teradici/media/TeradiciAudio.h"
#include "../experimental/teradici/media/TeradiciAudioHandler.h"

// CPCAPI2_TERADICI_CALLSTATE_MODULE
#include "../experimental/teradici/callstate/TeradiciCallstateInterface.h"

// CPCAPI2_TERADICI_LOGGER_MODULE
#include "../experimental/teradici/logging/TeradiciLoggerInterface.h"
#include "../experimental/teradici/logging/TeradiciLoggerHandler.h"

// CPCAPI2_BRAND_WATCHER_INFO_MODULE
#include "../experimental/watcherinfo/WatcherInfoManager.h"
#include "../experimental/watcherinfo/WatcherInfoSubscriptionHandler.h"

// CPCAPI2_BRAND_CONFERENCE_MODULE
#include "../experimental/conference/SipConferenceHandler.h"
#include "../experimental/conference/SipConferenceManager.h"

// CPCAPI2_BRAND_LICENSING_MODULE
#include "../experimental/licensing/LicensingClientManager.h"
#include "../experimental/licensing/LicensingClientHandler.h"

// CPCAPI2_BRAND_RECORDING_MODULE
#include "recording/RecordingHandler.h"
#include "recording/RecordingManager.h"

// CPCAPI2_BRAND_REMOTE_SYNC_MODULE
#include "../experimental/remotesync/RemoteSyncManager.h"
#include "../experimental/remotesync/RemoteSyncHandler.h"

// CPCAPI2_BRAND_ANALYTICS_MODULE
#include "../experimental/analytics1/AnalyticsManager.h"
#include "../experimental/analytics1/AnalyticsHandler.h"

// CPCAPI2_BRAND_VCCS_MODULE
#include "../experimental/vccs/VccsAccountManager.h"
#include "../experimental/vccs/VccsAccountHandler.h"
#include "../experimental/vccs/VccsAccountSettings.h"
#include "../experimental/vccs/VccsAccountTypes.h"
#include "../experimental/vccs/VccsConferenceHandler.h"
#include "../experimental/vccs/VccsConferenceManager.h"
#include "../experimental/vccs/VccsConferenceTypes.h"

// CPCAPI2_BRAND_SNS_MODULE
#include "../experimental/notificationservice/NotificationServiceTypes.h"
#include "../experimental/notificationservice/NotificationService.h"
#include "../experimental/notificationservice/NotificationHandler.h"

// CPCAPI2_BRAND_BIEVENTS_MODULE
#include "../experimental/bievents/BIEventsTypes.h"
#include "../experimental/bievents/BIEventsManager.h"
#include "../experimental/bievents/BIEventsHelper.h"
#include "../experimental/bievents/BIEventsHandler.h"

// CPCAPI2_BRAND_REMOTE_CONTROL_MODULE
#include "../experimental/remotecontrol/RemoteControlServer.h"
#include "../experimental/remotecontrol/RemoteControlClient.h"

// CPCAPI2_BRAND_BROADSOFT_XSI_MODULE
#include "../experimental/broadsoftxsi/BroadsoftXsiTypes.h"
#include "../experimental/broadsoftxsi/BroadsoftXsi.h"
#include "../experimental/broadsoftxsi/BroadsoftXsiHandler.h"

//CPCAPI2_BRAND_WATCHDOG_MODULE
#include "../experimental/watchdog/WatchdogManager.h"

//CPCAPI2_BRAND_OPEN_LDAP_MODULE
#include "../experimental/ldap/LdapHandler.h"
#include "../experimental/ldap/LdapManager.h"
#include "../experimental/ldap/LdapTypes.h"

// CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE
#include "../experimental/push_service/PushNotificationServiceManager.h"
#include "../experimental/push_service/PushNotificationServiceHandler.h"

// CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE
#include "../experimental/push_endpoint/PushNotificationEndpointManager.h"
#include "../experimental/push_endpoint/PushNotificationEndpointHandler.h"

// CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE
#include "../experimental/pushnotification/PushNotificationClientManager.h"
#include "../experimental/pushnotification_server/PushNotificationServerManager.h"
#include "../experimental/pushnotification/PushNotificationHandler.h"

// CPCAPI2_BRAND_JSON_API_CLIENT
#include "../experimental/jsonapi/JsonApiClient.h"
#include "../experimental/jsonapi/JsonApiClientHandler.h"
#include "../experimental/account/SipAccountJsonProxy.h"
#include "../experimental/account/SipAccountJsonProxyStateHandler.h"
#include "../experimental/call/SipConversationJsonProxy.h"
#include "../experimental/call/SipConversationJsonProxyStateHandler.h"
#include "../experimental/xmpp/XmppAccountJsonProxy.h"
#include "../experimental/xmpp/XmppAccountJsonProxyStateHandler.h"
#include "../experimental/xmpp/XmppChatJsonProxy.h"
#include "../experimental/xmpp/XmppRosterJsonProxy.h"
#include "../experimental/xmpp/XmppRosterJsonProxyStateHandler.h"
#include "../experimental/xmpp/XmppVCardJsonProxy.h"
#include "../experimental/xmpp/XmppVCardJsonProxyStateHandler.h"
#include "../experimental/xmpp/XmppMultiUserChatJsonProxy.h"
#include "../experimental/xmpp/XmppMultiUserChatJsonProxyStateHandler.h"
#include "../experimental/xmpp_agent/XmppAgentJsonProxy.h"
#include "../experimental/xmpp_agent/XmppAgentHandler.h"

// CPCAPI2_BRAND_JSON_API_SERVER
#include "../experimental/jsonapi/JsonApiServer.h"
#include "../experimental/jsonapi/JsonApiServerHandler.h"
#include "../experimental/jsonapi/JsonApiServerSendTransport.h"
#include "../experimental/account/SipAccountJsonApi.h"
#include "../experimental/call/SipConversationJsonApi.h"

// CPCAPI2_BRAND_XMPP_AGENT_MODULE
#include "../experimental/xmpp_agent/XmppAgentManager.h"

// CPCPAI2_BRAND_AUTH_PROVIDER_MODULE
#include "../experimental/authprovider/AuthProvider.h"

// CPCAPI2_BRAND_AUTH_PROVIDER_MOCK_MODULE
#include "../experimental/authprovider/mock/AuthProviderMock.h"

// CPCAPI2_BRAND_CONFERENCE_BRIDGE_MODULE
#include "../experimental/confbridge/ConferenceBridgeManager.h"
#include "../experimental/confbridge/ConferenceBridgeHandler.h"

// CPCAPI2_BRAND_AUTH_SERVER_MODULE
#include "../experimental/auth_server/AuthServer.h"
#include "../experimental/auth_server/AuthServerHandler.h"

// CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE
#include "../experimental/orchestration_server/OrchestrationServer.h"

// CPCAPI2_BRAND_CLOUD_WATCHDOG_SERVER_MODULE
#include "../experimental/cloudwatchdog/CloudWatchdogService.h"
#include "../experimental/cloudwatchdog/CloudWatchdogServiceHandler.h"

// CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
#include "../experimental/cloudconnector/CloudConnector.h"
#include "../experimental/cloudconnector/CloudConnectorHandler.h"
#include "../experimental/cloudconnector/CloudConnectorTypes.h"

// CPCAPI2_BRAND_CLOUD_SERVICE_CONFIG_MODULE
#include "../experimental/cloudserviceconfig/CloudServiceConfig.h"

// CPCAPI2_BRAND_VIDEO_STREAMING_MODULE
#include "../experimental/videostreaming/VideoStreaming.h"

// CPCAPI2_BRAND_AUDIO_TRANS_MODULE
#include "../experimental/audiotranscription/AudioTrans.h"

// CPCAPI2_BRAND_CONFERENCE_CONNECTOR_MODULE
#include "../experimental/confconnector/ConferenceConnector.h"
#include "../experimental/confconnector/ConferenceConnectorHandler.h"
#include "../experimental/confconnector/ConferenceConnectorTypes.h"

// CPCAPI2_BRAND_MESSAGESTORE_MODULE
#include "../experimental/messagestore/MessageStoreTypes.h"
#include "../experimental/messagestore/MessageStoreManager.h"
#include "../experimental/messagestore/MessageStoreHandler.h"

// CPCAPI2_BRAND_OPEN_PCAP_MODULE
#include "../experimental/pcap/PcapHandler.h"
#include "../experimental/pcap/PcapManager.h"

// CPCAPI2_BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE
#include "../experimental/remotesync_xmpp_helper/RemoteSyncXmppHandler.h"
#include "../experimental/remotesync_xmpp_helper/RemoteSyncXmppHelper.h"

// CPCAPI2_BRAND_MP4_RECORDING_MODULE
#include "../experimental/mp4recording/Mp4Recording.h"

// CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE
#include "../experimental/logcatmonitor/LogcatMonitor.h"
#include "../experimental/logcatmonitor/LogcatHandler.h"

// CPCAPI2_BRAND_PTT_MODULE
#include "../experimental/ptt/PushToTalkHandler.h"
#include "../experimental/ptt/PushToTalkManager.h"

// CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE
#include "../experimental/websocket_server/WebSocketServerHandler.h"
#include "../experimental/websocket_server/WebSocketServerManager.h"

// CPCAPI2_BRAND_SIGNATURE_MODULE
#include "../experimental/signature/SignatureManager.h"

//CPCAPI2_BRAND_XMPP_PUSH_MODULE
#include "../experimental/xmpp/XmppPushHandler.h"
#include "../experimental/xmpp/XmppPushTypes.h"
#include "../experimental/xmpp/XmppPushManager.h"

#endif // CPCAPI2_INCLUDE_UNRELEASED_HEADERS

#endif // CPCAPI2_H
