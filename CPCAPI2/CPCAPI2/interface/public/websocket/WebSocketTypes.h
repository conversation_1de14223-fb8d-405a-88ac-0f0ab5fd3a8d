#pragma once
#ifndef __CPCAPI2_WEBSOCKET_WEBSOCKETTYPES_H__
#define __CPCAPI2_WEBSOCKET_WEBSOCKETTYPES_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <phone/SslCipherOptions.h>

/**
 * Common websocket types used in several different modules.
 */
namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * Different types of supported certificate verification modes. Similar
       * to the similarly-named openssl modes in ssl.h.
       *
       * Note that the modes "CertVerificationMode_Fail_If_No_Peer_Cert" and
       * "CertVerificationMode_Client_Once" also imply
       * "CertVerificationMode_Peer".
       */
      typedef enum CertVerificationMode
      {
         CertVerificationMode_None,
         CertVerificationMode_Peer,
         CertVerificationMode_Fail_If_No_Peer_Cert,
         CertVerificationMode_Client_Once
      } CertVerificationMode;

      /**
       * Allows the application to control whether TLS/SSL certificates
       * are verified against the OS store, or against a filesystem store
       *
       * Any files with ".pem" as an extension in the specified directory
       * will be loaded as a certificate.
       */
      typedef enum CertStorageLoadType
      {
         /**
          * Load (and test) certificates against the OS cert store
          */
         CertStorageLoadType_OS,

         /**
          * Load (and test) certificates against a file-based cert store
          */
         CertStorageLoadType_FileSystem,

         /**
          * Load (and test) certificates against both the OS cert store
          * and a file-based cert store
          */
         CertStorageLoadType_Both
      } CertStorageLoadType;

      /**
       * Structure containing all relevant settings for the websock.
       */
      typedef struct WebSocketSettings
      {
         /**
          * The (provisioned?) location of the SNS server. The URL may be
          * either secure (wss://) or insecure (ws://).
          */
         cpc::string webSocketURL;

         /**
          * Because remote sync
          */
         bool sendImmediatePing;

         /**
          * The "ping" interval (in seconds) which will be used to keep the
          * websocket connection open. This number is used after the account
          * is in "Registered" state.
          */
         int pingIntervalSeconds;

         /**
          * The SDK will wait a random amount between 0 and this
          * initialRetryInterval before trying to contact the server again.
          * It will then double the value each failure (backoff algorithm).
          */
         int initialRetryIntervalSeconds;

         /**
          * The maximum (ceiling) seconds that the retry mechanism will use,
          * in the event that the SDK is handling the connection retry
          * mechanism.
          */
         int maxRetryIntervalSeconds;

         /**
          * Controls the TLS certificate verification mode. If not
          * specified, the default will be "CertVerificationMode_Peer". The
          * mode will only be used in the event that the webSocketURL is
          * a secure (wss://) URL.
          */
         CertVerificationMode certMode;

         /**
          * If set to true (default) the protocol content will be
          * written to the logfile at "Info" level. Otherwise if the
          * application is not interested in the raw JSON content, set this
          * to false.
          */
         bool logPayload;

         /**
          * No longer supported on iOS due to Apple changes; enabling will result in no-op. 
          *
          * Some platforms (iOS) have requirements around backgrounding the
          * applications and socket use. By setting this flag to true, sockets
          * will have the kCFStreamNetworkServiceType set to
          * kCFStreamNetworkServiceTypeVoIP on iOS. If these words mean nothing
          * to you, it is probably best to leave this flag set to the default
          * (false).
          */
         bool backgroundSocketsIfPossible;

         /**
          * Some protocols require a login/logout procedure (most of them).
          * However in certain situations it may not be required, as in the case
          * of a guest VCCS for example. NB: if Login is not required, then
          * neither will a logout be performed.
          */
         bool isLoginRequired;

         /**
         * TLS protocol version to use.
         */
         TLSVersion tlsVersion;

         /**
         * OpenSSL cipher suite string. See SslCipherOptions.h for recommended cipher suites.
         */
         CipherSuite cipherSuite;

         /**
          * Base 64 encoded public keys, checked during certificate
          * verification.  The public keys specified in this list are checked
          * against the value in the server certificate. If specified, when an
          * accepted public key is found in this list the certificate will be
          * considered valid, overriding the result of the default certificate
          * validation.  Note that this even applies if
          * CertVerificationMode_None is set to false.  If nothing is present
          * in this list, default certificate verification will be used (and
          * possibly the requiredCertPublicKeys below).
          */
         cpc::vector< cpc::string > acceptedCertPublicKeys;

         /**
          * Base 64 encoded public keys, checked during certificate
          * verification. The public keys specified in this list are checked
          * against the value in the server certificate. If specified, an
          * additional check is performed to ensure that the public key in the
          * server certificate matches at least one of the values specified
          * here, overriding the result of the default certification
          * validation.  Note that even if CertVerificationMode_None is used, a
          * missing required cert will still result in the connection failing.
          *
          * If BOTH acceptedCertPublicKeys and requiredCertPublicKeys are
          * specified, requiredCertPublicKeys will take precedence.
          */
         cpc::vector< cpc::string > requiredCertPublicKeys;

         /**
          * Setting which controls how certificates are loaded for TLS/SSL
          * (Loads from the operating system by default).
          */
         CertStorageLoadType certStorageLoadType;

         /**
          * Sets the file system path where certificates are stored.  Note that
          * if this is not specified the cert store will be checked against the
          * OS cert store, regardless of the CertStorageLoadType setting.
          */
         cpc::string certStorageFileSystemPath;

         // Default ctor with reasonable values
         WebSocketSettings()
         {
            sendImmediatePing           = true;
            pingIntervalSeconds         = 55;    // less than 60 seconds default
            initialRetryIntervalSeconds = 5;
            maxRetryIntervalSeconds     = 900;   // 15 minutes upper limit
            certMode                    = CertVerificationMode_Peer;
            logPayload                  = true;
            backgroundSocketsIfPossible = false;
            isLoginRequired             = true;
            tlsVersion                  = TLS_DEFAULT;
            certStorageLoadType         = CertStorageLoadType_OS;
         }
      } WebSocketSettings;
      
      cpc::string get_debug_string(const CertVerificationMode& mode);
      cpc::string get_debug_string(const WebSocketSettings& settings);

   }
}

#endif // __CPCAPI2_WEBSOCKET_WEBSOCKETTYPES_H__
