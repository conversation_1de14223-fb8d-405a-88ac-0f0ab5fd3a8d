#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_MANAGER_H)
#define CPCAPI2_SIP_PRESENCE_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "event/SipEventManager.h"
#include "event/SipEventSubscriptionHandler.h"
#include "SipPresenceModel.h"

namespace CPCAPI2
{
class Phone;

namespace SipPresence
{
using CPCAPI2::SipEvent::SipEventSubscriptionHandle;
using CPCAPI2::SipEvent::SipEventPublicationHandle;
class SipPresenceSubscriptionHandler;
class SipPresencePublicationHandler;

/**
* A struct. 
*/ 
struct SipPresenceSubscriptionSettings
{
   unsigned int expiresSeconds;
   cpc::vector<int>  retryWithTheseResponseCodesOnly;
   unsigned int minTimeBeforeResubscribeInSeconds, maxTimeBeforeResubscribeInSeconds;
   SipPresenceSubscriptionSettings()
   {
      expiresSeconds = 3600;
      minTimeBeforeResubscribeInSeconds = 3;
      maxTimeBeforeResubscribeInSeconds = 3600;
      retryWithTheseResponseCodesOnly.push_back(404);
      retryWithTheseResponseCodesOnly.push_back(408);
      retryWithTheseResponseCodesOnly.push_back(477);
      retryWithTheseResponseCodesOnly.push_back(480);
      retryWithTheseResponseCodesOnly.push_back(481);
      retryWithTheseResponseCodesOnly.push_back(500);
      retryWithTheseResponseCodesOnly.push_back(503);
   }
};

/**
* A struct. 
*/ 
struct SipPresencePublicationSettings
{
   unsigned int expiresSeconds;

   SipPresencePublicationSettings()
   {
      expiresSeconds = 3600;
   }
};

/**
* A struct. 
*/ 
struct StatusUpdateParameters
{
   cpc::string note;
};

/**
* Manager interface to create and manage presence subscriptions 
* and to publish the local user's presence via SIP SIMPLE; get a reference to the 
* interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SipPresenceManager
{
public:
   /**
   * Get a reference to the %SipPresenceManager interface.
   */   
   static SipPresenceManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for presence events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same 
   * thread as SipAccount::process(..)
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipPresenceSubscriptionHandler* handler) = 0;

  /**
   * Set the handler for presence publication events on the specified account.
   * Set the handler immediately after creating the account.
   */   
   virtual int setPublicationHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipPresencePublicationHandler* handler) = 0;

  /**
   * Allocates a new publication within the SDK.  This function is used in concert with publish(..)
   * to send presence PUBLISH requests to a Presence UA server
   */
  virtual SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipPresencePublicationSettings& settings) = 0;

  /**
   * Used to send Presence state on to a subscription agent server
   * Sends a PUBLISH request with content as specified by the presenceState parameter.
   */
   virtual int publish(SipEventPublicationHandle publication, const Presence& presenceState) = 0;

  /**
   * Prepares and sends a pre-populated Presence document
   * including status information corresponding to the specified presenceStatus.
   * Sends a PUBLISH request immediately.  To adorn/modify the Presence document, use #preparePresence(..)
   */
   virtual int publish(SipEventPublicationHandle publication, CannedStatus presenceStatus) = 0;

   /**
   * Prepares and sends a pre-populated Presence document
   * including status information corresponding to the specified presenceStatus.
   * Sends a PUBLISH request immediately.  To adorn/modify the Presence document, use #preparePresence(..)
   */
   virtual int publish(SipEventPublicationHandle publication, CannedStatus presenceStatus, const StatusUpdateParameters& params) = 0;

   /**
   * Removes event state for the publication per section 4.5 of RFC 3903.
   */
   virtual int endPublish(SipEventPublicationHandle publication) = 0;

   /**
   * Refreshes a publication session. Sends an outgoing PUBLISH.
   */
   virtual int refreshPublish(SipEventPublicationHandle publication) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Use this function if the default values in SipPresenceSubscriptionSettings are not suitable for your application.
   */
   virtual int applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings) = 0;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Sets the address of the event server responsible for sending event notifications.  For example, this may be
   * the address of a resource list server.  If an event server is set on a subscription, the outgoing SUBSCRIBE
   * is sent to the targetAddress for the event server specified in the targetAddress parameter.
   * Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   */
   virtual int start(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Ends a outgoing subscription session by sending an outgoing SUBSCRIBE with Expires == 0.
   * Ends an incoming subscription session by sending a NOTIFY with Subscription-State: terminated
   */
   virtual int end(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Refreshes a subscription session. Sends an outgoing SUBSCRIBE.
   */
   virtual int refresh(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
   * @param subscription The incoming subscription session to reject.
   * @param rejectReason The SIP response code sent to the originating party.
   */
   virtual int reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason=486/*Busy here*/) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK).
   */
   virtual int accept(SipEventSubscriptionHandle subscription, const Presence& presenceState) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK) with a pre-populated presence document
   */
   virtual int accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK) with a pre-populated presence document
   */
   virtual int accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& params) = 0;

   /**
   * Used to put an incoming (server) subscription session to PENDING state (200 OK).
   */
   virtual int provisionalAccept(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Used to send outgoing event state on an incoming (server) subscription session.
   * Sends a NOTIFY request with content as specified by the presenceState parameter.
   */
   virtual int notify(SipEventSubscriptionHandle subscription, const Presence& presenceState) = 0;

   /**
   * Prepares and sends a pre-populated Presence document, customized for the specified subscription,
   * and including status information corresponding to the specified presenceStatus.
   * Sends a NOTIFY request immediately.  To adorn/modify the Presence document, use preparePresence(..)
   */
   virtual int notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) = 0;

   /**
   * Prepares and sends a pre-populated Presence document, customized for the specified subscription,
   * and including status information corresponding to the specified presenceStatus.
   * Sends a NOTIFY request immediately.  To adorn/modify the Presence document, use preparePresence(..)
   */
   virtual int notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& params) = 0;

   /**
   * Prepares a pre-populated Presence document, customized for the specified subscription,
   * and including status information corresponding to the specified presenceStatus.
   * This is a convenience function, which enables applications to quickly generate a Presence document
   * prior to submitting it to the remote party via the notify(..) function.
   * The prepared Presence document is returned via the SipPresenceSubscriptionHandler::onPresenceReadyToSend(..)
   * callback function.
   */
   virtual int preparePresence(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipPresenceManager.
    */
   virtual ~SipPresenceManager() {}
};

}
}
#endif // CPCAPI2_SIP_PRESENCE_MANAGER_H
