#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_MODEL_H)
#define CPCAPI2_SIP_PRESENCE_MODEL_H

#include <cpcstl/vector.h>
#include <cpcstl/string.h>

/**
  * This file defines data types to store information from SIP PRESENCE updates.
  * The schema types maintain as direct of a translation of data from XML to simple C++ types as possible
  */

namespace CPCAPI2
{

namespace SipPresence
{

enum CannedStatus
{
   CannedStatus_Available           = 1000,
   CannedStatus_Busy                = 1100,
   CannedStatus_Away                = 1200,
   CannedStatus_OnThePhone          = 1300,
   CannedStatus_NotAvailable        = 1400,
   CannedStatus_DND                 = 1500,
   CannedStatus_AppearOffline       = 1600,
   CannedStatus_Idle                = 1700,
   CannedStatus_Other               = 1800,
   CannedStatus_Lunch               = 2000,
   CannedStatus_Vacation            = 2100,
   CannedStatus_PendingAuth         = 2200
};

/*
REFERENCES:
   RFC 4479 - A Data Model for Presence: http://tools.ietf.org/html/rfc4479
   RFC 3863 - Presence Information Data Format (PIDF): http://tools.ietf.org/html/rfc3863
   RFC 4480 - RPID: Rich Presence Extensions to the 
              Presence Information Data Format (PIDF): http://tools.ietf.org/html/rfc4480
   
NOTES:
   ##any   => Any well-formed XML from any namespace (default)
   ##other => Any well-formed XML that is from a namespace other than the target 
              namespace of the type being defined (unqualified elements are not allowed)
   ref: http://www.w3.org/TR/xmlschema-0/#ref34
*/

   typedef cpc::string Id;
   typedef cpc::string URI;
   typedef cpc::string DateTime;
   typedef cpc::string Other;

   // usage Optional<int> myint;
   template <typename T>

/**
* A struct. 
*/ 
   struct Optional
   {
      Optional(): present(false){}
      bool present;
      T value;

      void operator=(const T& rhs) { value = rhs; present = true; }
      T* operator->() { return &value; }
   };




/* =============================================================================================================================================================
 *            BEGIN common-schema.xsd (rfc 4479)
 * ============================================================================================================================================================= */

/* <xs:simpleType name="Timestamp_t">
      <xs:annotation>
         <xs:documentation>Timestamp type</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:dateTime"/>
   </xs:simpleType> */

/* EXAMPLE:
   <pdm:timestamp>2010-07-01T09:37:47Z</pdm:timestamp> */

   /** Timestamp type
     */
   typedef DateTime Timestamp;


   
/* <xs:simpleType name="deviceID_t">
      <xs:annotation>
         <xs:documentation>Device ID, a URN</xs:documentation>
      </xs:annotation>
      <xs:restriction base="xs:anyURI"/>
   </xs:simpleType> */

/* EXAMPLE:
   <dm:deviceID>urn:x-mac:0003ba4811e3</dm:deviceID> */
   
   /** Device ID, a URN
     */
   typedef URI DeviceId;

/* <xs:attributeGroup name="fromUntil">
      <xs:attribute name="from" type="xs:dateTime"/>
      <xs:attribute name="until" type="xs:dateTime"/>
   </xs:attributeGroup> */

/* EXAMPLE:
   <rpid:activities from="2005-05-30T12:00:00+05:00" until="2005-05-30T17:00:00+05:00"> */

/**
* A struct. 
*/ 
   struct FromUntil
   {
      Optional<DateTime> from;
      Optional<DateTime> until;
   };

/* <xs:complexType name="Note_t">
      <xs:annotation>
         <xs:documentation>Note type</xs:documentation>
      </xs:annotation>
      <xs:simpleContent>
         <xs:extension base="xs:string">
            <xs:attribute ref="xml:lang"/>
         </xs:extension>
      </xs:simpleContent>
   </xs:complexType> */

/* EXAMPLE:
   <note xml:lang="en">Don't Disturb Please!</note> */

/**
* A struct. 
*/ 
   struct Note
   {
      cpc::string text;
      Optional<cpc::string> lang;

      Optional<cpc::string> ns;
   };



   typedef cpc::vector<Note> Notes;





/* =============================================================================================================================================================
 *            BEGIN rpid.xsd (rfc 4480)
 * ============================================================================================================================================================= */


/* <xs:element name="activities">
      <xs:annotation>
         <xs:documentation>
            Describes what the person is currently doing, expressed as an enumeration of 
            activity-describing elements. A person can be engaged in multiple activities 
            at the same time, e.g., traveling and having a meal.
         </xs:documentation>
      </xs:annotation>

      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
               <xs:element name="unknown" type="empty" minOccurs="0"/>
               <xs:sequence maxOccurs="unbounded">
                  <xs:choice>
                     <xs:element name="appointment" type="empty" />
                     <xs:element name="away" type="empty" />
                     <xs:element name="breakfast" type="empty" />
                     <xs:element name="busy" type="empty" />
                     <xs:element name="dinner" type="empty" />
                     <xs:element name="holiday" type="empty" />
                     <xs:element name="in-transit" type="empty" />
                     <xs:element name="looking-for-work" type="empty" />
                     <xs:element name="meal" type="empty" />
                     <xs:element name="meeting" type="empty" />
                     <xs:element name="on-the-phone" type="empty" />
                     <xs:element name="performance" type="empty" />
                     <xs:element name="permanent-absence" type="empty" />
                     <xs:element name="playing" type="empty" />
                     <xs:element name="presentation" type="empty" />
                     <xs:element name="shopping" type="empty" />
                     <xs:element name="sleeping" type="empty" />
                     <xs:element name="spectator" type="empty" />
                     <xs:element name="steering" type="empty" />
                     <xs:element name="travel" type="empty" />
                     <xs:element name="tv" type="empty" />
                     <xs:element name="vacation" type="empty" />
                     <xs:element name="working" type="empty" />
                     <xs:element name="worship" type="empty" />
                     <xs:element name="other" type="Note_t" />
                     <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
                  </xs:choice>
               </xs:sequence>
            </xs:choice>
         </xs:sequence>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */
   
/* EXAMPLE:
   <rpid:activities from="2005-05-30T12:00:00+05:00"
      until="2005-05-30T17:00:00+05:00">
      <rpid:note>Far away</rpid:note>
      <rpid:away/>
   </rpid:activities> */

   enum ActivityType
   {
      ActivityType_Appointment, 
      ActivityType_Away, 
      ActivityType_Breakfast, 
      ActivityType_Busy, 
      ActivityType_Dinner, 
      ActivityType_Holiday, 
      ActivityType_InTransit, 
      ActivityType_LookingForWork, 
      ActivityType_Meal, 
      ActivityType_Meeting, 
      ActivityType_OnThePhone, 
      ActivityType_Performance, 
      ActivityType_PermanentAbsence, 
      ActivityType_Playing, 
      ActivityType_Presentation, 
      ActivityType_Shopping, 
      ActivityType_Sleeping, 
      ActivityType_Spectator, 
      ActivityType_Steering, 
      ActivityType_Travel, 
      ActivityType_Tv, 
      ActivityType_Vacation, 
      ActivityType_Working, 
      ActivityType_Worship, 
      ActivityType_Unknown, 
      ActivityType_Other, 
      ActivityType_Lunch
   };

/**
* A struct.
*/
   struct Activity
   {
      ActivityType activity;
      // otherValue is only valid if activity==other
      Note otherValue;
   };
   
   /** A struct. Describes what the person is currently doing, expressed as an enumeration of 
     * activity-describing elements. A person can be engaged in multiple activities 
     * at the same time, e.g., traveling and having a meal.
     */
   struct Activities
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Notes notes;
      cpc::vector<Activity> activities;
   };


/* <xs:element name="class" type="xs:token">
      <xs:annotation>
         <xs:documentation>
            Describes the class of the service, device or person.
         </xs:documentation>
      </xs:annotation>
   </xs:element> */

/* EXAMPLE:
   <rpid:class>calendar</rpid:class> */

   /** Describes the class of the service, device or person.
     */
   typedef cpc::string Class;


/* <xs:element name="mood">
      <xs:annotation>
         <xs:documentation>
            Describes the mood of the presentity.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
               <xs:element name="unknown" type="empty"/>
               <xs:sequence maxOccurs="unbounded">
                  <xs:choice>
                     <xs:element name="afraid" type="empty"/>
                     <xs:element name="amazed" type="empty"/>
                     <xs:element name="angry" type="empty"/>
                     <xs:element name="annoyed" type="empty"/>
                     <xs:element name="anxious" type="empty" />
                     <xs:element name="ashamed" type="empty" />
                     <xs:element name="bored" type="empty" />
                     <xs:element name="brave" type="empty" />
                     <xs:element name="calm" type="empty" />
                     <xs:element name="cold" type="empty" />
                     <xs:element name="confused" type="empty" />
                     <xs:element name="contented" type="empty" />
                     <xs:element name="cranky" type="empty" />
                     <xs:element name="curious" type="empty" />
                     <xs:element name="depressed" type="empty" />
                     <xs:element name="disappointed" type="empty" />
                     <xs:element name="disgusted" type="empty" />
                     <xs:element name="distracted" type="empty" />
                     <xs:element name="embarrassed" type="empty" />
                     <xs:element name="excited" type="empty" />
                     <xs:element name="flirtatious" type="empty" />
                     <xs:element name="frustrated" type="empty" />
                     <xs:element name="grumpy" type="empty" />
                     <xs:element name="guilty" type="empty" />
                     <xs:element name="happy" type="empty" />
                     <xs:element name="hot" type="empty" />
                     <xs:element name="humbled" type="empty" />
                     <xs:element name="humiliated" type="empty" />
                     <xs:element name="hungry" type="empty" />
                     <xs:element name="hurt" type="empty" />
                     <xs:element name="impressed" type="empty" />
                     <xs:element name="in_awe" type="empty" />
                     <xs:element name="in_love" type="empty" />
                     <xs:element name="indignant" type="empty" />
                     <xs:element name="interested" type="empty" />
                     <xs:element name="invincible" type="empty" />
                     <xs:element name="jealous" type="empty" />
                     <xs:element name="lonely" type="empty" />
                     <xs:element name="mean" type="empty" />
                     <xs:element name="moody" type="empty" />
                     <xs:element name="nervous" type="empty" />
                     <xs:element name="neutral" type="empty" />
                     <xs:element name="offended" type="empty" />
                     <xs:element name="playful" type="empty" />
                     <xs:element name="proud" type="empty" />
                     <xs:element name="relieved" type="empty" />
                     <xs:element name="remorseful" type="empty" />
                     <xs:element name="restless" type="empty" />
                     <xs:element name="sad" type="empty" />
                     <xs:element name="sarcastic" type="empty" />
                     <xs:element name="serious" type="empty" />
                     <xs:element name="shocked" type="empty" />
                     <xs:element name="shy" type="empty" />
                     <xs:element name="sick" type="empty" />
                     <xs:element name="sleepy" type="empty" />
                     <xs:element name="stressed" type="empty" />
                     <xs:element name="surprised" type="empty" />
                     <xs:element name="thirsty" type="empty" />
                     <xs:element name="worried" type="empty" />
                     <xs:element name="other" type="Note_t" />
                     <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
                  </xs:choice>
               </xs:sequence>
            </xs:choice>
         </xs:sequence>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:mood>
      <rpid:angry/>
      <rpid:other>brooding</rpid:other>
   </rpid:mood> */

   enum MoodValue 
   {
      MoodValue_Afraid, MoodValue_Amazed, MoodValue_Angry, MoodValue_Annoyed, 
      MoodValue_Anxious, MoodValue_Ashamed, MoodValue_Bored, MoodValue_Brave, 
      MoodValue_Calm, MoodValue_Cold, MoodValue_Confused, MoodValue_Contented, 
      MoodValue_Cranky, MoodValue_Curious, MoodValue_Depressed, 
      MoodValue_Disappointed, MoodValue_Disgusted, MoodValue_Distracted, 
      MoodValue_Embarrassed, MoodValue_Excited, MoodValue_Flirtatious, 
      MoodValue_Frustrated, MoodValue_Grumpy, MoodValue_Guilty, 
      MoodValue_Happy, MoodValue_Hot, MoodValue_Humbled, MoodValue_Humiliated, 
      MoodValue_Hungry, MoodValue_Hurt, MoodValue_Impressed, MoodValue_InAwe, 
      MoodValue_InLove, MoodValue_Indignant, MoodValue_Interested, 
      MoodValue_Invincible, MoodValue_Jealous, MoodValue_Lonely, 
      MoodValue_Mean, MoodValue_Moody, MoodValue_Nervous, MoodValue_Neutral, 
      MoodValue_Offended, MoodValue_Playful, MoodValue_Proud, 
      MoodValue_Relieved, MoodValue_Remorseful, MoodValue_Restless,
      MoodValue_Sad, MoodValue_Sarcastic, MoodValue_Serious, MoodValue_Shocked, 
      MoodValue_Shy, MoodValue_Sick, MoodValue_Sleepy, MoodValue_Stressed,
      MoodValue_Surprised, MoodValue_Thirsty, MoodValue_Worried, MoodValue_Unknown, MoodValue_Other
   };

/**
* A struct. 
*/ 
   struct Mood
   {
      MoodValue mood;
      // otherValue is only valid if mood==other
      Note otherValue;
   };
   
   /** A struct. Describes the mood of the presentity.
     */
   struct Moods
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Notes notes;
      cpc::vector<Mood> moods;
   };

/* <xs:element name="place-is">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:element name="audio" minOccurs="0">
               <xs:complexType>
                  <xs:choice>
                     <xs:element name="noisy" type="empty" />
                     <xs:element name="ok" type="empty" />
                     <xs:element name="quiet" type="empty" />
                     <xs:element name="unknown" type="empty" />
                  </xs:choice>
               </xs:complexType>
            </xs:element>
            <xs:element name="video" minOccurs="0">
               <xs:complexType>
                  <xs:choice>
                     <xs:element name="toobright" type="empty" />
                     <xs:element name="ok" type="empty" />
                     <xs:element name="dark" type="empty" />
                     <xs:element name="unknown" type="empty" />
                  </xs:choice>
               </xs:complexType>
            </xs:element>
            <xs:element name="text" minOccurs="0">
               <xs:complexType>
                  <xs:choice>
                     <xs:element name="uncomfortable" type="empty" />
                     <xs:element name="inappropriate" type="empty" />
                     <xs:element name="ok" type="empty" />
                     <xs:element name="unknown" type="empty" />
                  </xs:choice>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:place-is>
      <rpid:audio>
         <rpid:noisy/>
      </rpid:audio>
   </rpid:place-is> */
   enum AudioIsType 
   {
      AudioIsType_Noisy, 
      AudioIsType_Ok, 
      AudioIsType_Quiet, 
      AudioIsType_Unknown
   };

   enum VideoIsType 
   {
      VideoIsType_TooBright, 
      VideoIsType_Ok, 
      VideoIsType_Dark, 
      VideoIsType_Unknown
   };

   enum TextIsType 
   {
      TextIsType_Uncomfortable, 
      TextIsType_Inappropriate, 
      TextIsType_Ok, 
      TextIsType_Unknown
   };

/**
* A struct. 
*/ 
   struct PlaceIs
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Notes notes;
      Optional<AudioIsType> audio;
      Optional<VideoIsType> video;
      Optional<TextIsType> text;
   };

/* <xs:element name="place-type">
      <xs:annotation>
         <xs:documentation>
            Describes the type of place the person is currently at.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
               <xs:element name="other" type="Note_t"/>
               <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
            </xs:choice>
         </xs:sequence>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:place-type><lt:residence/></rpid:place-type> */

/**
* A struct. 
*/ 
   struct PlaceTypeValue
   {
      // 'other' is the only type supported without more extensions
      Note otherValue;
   };

    /** A struct. Describes the type of place the person is currently at.
      */
   struct PlaceType
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Notes notes;
      PlaceTypeValue placeType;
   };


/* <xs:element name="privacy">
      <xs:annotation>
          <xs:documentation>
             Indicates which type of communication third parties in the vicinity of the presentity are 
             unlikely to be able to intercept accidentally or intentionally.
          </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
               <xs:element name="unknown" type="empty"/>
               <xs:sequence minOccurs="1">
                  <xs:element name="audio" type="empty" minOccurs="0"/>
                  <xs:element name="text" type="empty" minOccurs="0"/>
                  <xs:element name="video" type="empty" minOccurs="0"/>
                  <xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
               </xs:sequence>
            </xs:choice>
         </xs:sequence>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:privacy><rpid:unknown/></rpid:privacy> */

   enum PrivacyType 
   {
      PrivacyType_Audio, 
      PrivacyType_Text, 
      PrivacyType_Video,
      PrivacyType_Unknown
   };

   /** A struct. Indicates which type of communication third parties in the vicinity of the presentity are 
     * unlikely to be able to intercept accidentally or intentionally.
     */
   struct Privacy
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Notes notes;
      cpc::vector<PrivacyType> privacy;
   };

/* <xs:element name="relationship">
      <xs:annotation>
          <xs:documentation>
             Designates the type of relationship an alternate contact has with the presentity.
          </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
                <xs:element name="assistant" type="empty" />
                <xs:element name="associate" type="empty" />
                <xs:element name="family" type="empty" />
                <xs:element name="friend" type="empty" />
                <xs:element name="other" type="Note_t" minOccurs="0" />
                <xs:element name="self" type="empty" />
                <xs:element name="supervisor" type="empty" />
                <xs:element name="unknown" type="empty" />
                <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
            </xs:choice>
         </xs:sequence>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:relationship><rpid:self/></rpid:relationship> */

   enum RelationshipType 
   {
      RelationshipType_Assistant, 
      RelationshipType_Associate, 
      RelationshipType_Family, 
      RelationshipType_Friend, 
      RelationshipType_Self, 
      RelationshipType_Supervisor, 
      RelationshipType_Unknown, 
      RelationshipType_Other
   };

/**
* A struct. 
*/ 
   struct RelationshipValue
   {
      RelationshipType relationship;
      // otherValue is only valid if relationship==other
      Note otherValue;
   };

   /** A struct. Designates the type of relationship an alternate contact has with the presentity.
     */
   struct Relationship
   {
      Notes notes;
      RelationshipValue relationship;
   };


/* <xs:element name="service-class">
      <xs:annotation>
         <xs:documentation>
            Designates the type of service offered.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded" />
            <xs:choice>
               <xs:element name="courier" type="empty" />
               <xs:element name="electronic" type="empty" />
               <xs:element name="freight" type="empty" />
               <xs:element name="in-person" type="empty" />
               <xs:element name="postal" type="empty" />
               <xs:element name="unknown" type="empty" />
               <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
            </xs:choice>
         </xs:sequence>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:service-class><rpid:electronic/></rpid:service-class> */

   enum ServiceClassType 
   {
      ServiceClassType_Courier, 
      ServiceClassType_Electronic, 
      ServiceClassType_Freight, 
      ServiceClassType_InPerson, 
      ServiceClassType_Postal,
      ServiceClassType_Unknown
   };

   /** A struct. Designates the type of service offered.
     */
   struct ServiceClass
   {
      Notes notes;
      ServiceClassType serviceClass;
   };

/* <xs:element name="sphere">
      <xs:annotation>
         <xs:documentation>
            Designates the current state and role that the person plays.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:choice minOccurs="0">
            <xs:element name="home" type="empty" />
            <xs:element name="work" type="empty" />
            <xs:element name="unknown" type="empty" />
            <xs:any namespace="##other" maxOccurs="unbounded" processContents="lax"/>
         </xs:choice>
         <xs:attributeGroup ref="fromUntil"/>
         <xs:attribute name="id" type="xs:ID"/>
         <xs:anyAttribute namespace="##any" processContents="lax"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:sphere><work/></rpid:sphere> 
   Note: RFC example is wrong: //http://www.rfc-editor.org/errata_search.php?rfc=4480 - errata ID: 2961 */

   enum SphereType 
   {
      SphereType_Home,
      SphereType_Work,
      SphereType_Unknown
   };

   /** A struct. Designates the current state and role that the person plays.
     */
   struct Sphere
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Optional<SphereType> sphere;
   };

/* <xs:element name="status-icon">
      <xs:annotation>
         <xs:documentation>
            A URI pointing to an image (icon) representing the current status of the person or service.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:simpleContent>
            <xs:extension base="xs:anyURI">
               <xs:attributeGroup ref="fromUntil"/>
               <xs:attribute name="id" type="xs:ID"/>
               <xs:anyAttribute namespace="##any" processContents="lax"/>
            </xs:extension>
         </xs:simpleContent>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:status-icon>http://example.com/mail.png</rpid:status-icon> */

   /** A struct. A URI pointing to an image (icon) representing the current status of the person or service.
     */
   struct StatusIcon
   {
      Optional<Id> id;
      FromUntil fromUntil;
      URI uri;
   };

/* <xs:element name="time-offset">
      <xs:annotation>
         <xs:documentation>
            Describes the number of minutes of offset from UTC at the user's current location.
         </xs:documentation>
      </xs:annotation>

      <xs:complexType>
         <xs:simpleContent>
            <xs:extension base="xs:integer">
               <xs:attributeGroup ref="fromUntil"/>
               <xs:attribute name="description" type="xs:string"/>
               <xs:attribute name="id" type="xs:ID"/>
               <xs:anyAttribute namespace="##any" processContents="lax"/>
            </xs:extension>
         </xs:simpleContent>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:time-offset>-240</rpid:time-offset> */
   
   /** A struct. Describes the number of minutes of offset from UTC at the user's current location.
     */
   struct TimeOffset
   {
      Optional<Id> id;
      FromUntil fromUntil;
      Optional<cpc::string> description;
      int offset;
   };

/* <xs:element name="user-input">
      <xs:annotation>
         <xs:documentation>
            Records the user-input or usage state of the service or device.
         </xs:documentation>
      </xs:annotation>
      <xs:complexType>
            <xs:simpleContent>
               <xs:extension base="activeIdle">
                  <xs:attribute name="idle-threshold" type="xs:positiveInteger"/>
                  <xs:attribute name="last-input" type="xs:dateTime"/>
                  <xs:attribute name="id" type="xs:ID"/>
                  <xs:anyAttribute namespace="##any" processContents="lax"/>
               </xs:extension>
            </xs:simpleContent>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <rpid:user-input idle-threshold="600" last-input="2004-10-21T13:20:00-05:00">idle</rpid:user-input> */

   enum ActiveIdle
   {
      ActiveIdle_Active,
      ActiveIdle_Idle
   };

   /** A struct. Records the user-input or usage state of the service or device.
     */
   struct UserInput
   {
      Optional<Id> id;
      ActiveIdle activeIdle;
      Optional<unsigned int> idleThreshold;
      Optional<DateTime> lastInput;
   };




/* =============================================================================================================================================================
 *            BEGIN pidf-datamodel.xsd (rfc 4479)
 * ============================================================================================================================================================= */

/* <xs:simpleType name="basic">
      <xs:restriction base="xs:string">
         <xs:enumeration value="open"/>
         <xs:enumeration value="closed"/>
      </xs:restriction>
   </xs:simpleType> */

/* EXAMPLE:
   <basic>open</basic> */

   enum BasicStatus
   {
      BasicStatusType_Open,
      BasicStatusType_Closed
   };

/* <xs:complexType name="status">
      <xs:sequence>
         <xs:element name="basic" type="tns:basic" minOccurs="0"/>
         <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
   </xs:complexType> */

/* EXAMPLE:
   <status>
      <basic>open</basic>
   </status> */

/**
* A struct.
*/
   struct Status
   {
      Optional<BasicStatus> basic;
   };

/* <xs:element name="device">
      <xs:annotation>
         <xs:documentation>Contains information about the device</xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element ref="deviceID"/>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="timestamp" type="Timestamp_t" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="id" type="xs:ID" use="required"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <dm:device id="pc147">
      <rpid:user-input idle-threshold="600" last-input="2004-10-21T13:20:00-05:00">idle</rpid:user-input>
      <dm:deviceID>urn:device:0003ba4811e3</dm:deviceID>
      <dm:note>PC</dm:note>
   </dm:device> */
   
/**
* A struct. 
*/ 
   struct Device
   {
      Id id;
      DeviceId deviceID;
      Notes notes;
      Optional<Timestamp> timestamp;
      Optional<UserInput> userInput;
   };
   
/* <xs:element name="person">
      <xs:annotation>
         <xs:documentation>Contains information about the human user</xs:documentation>
      </xs:annotation>
      <xs:complexType>
         <xs:sequence>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
               <xs:annotation>
                  <xs:documentation>Characteristic and status information</xs:documentation>
               </xs:annotation>
            </xs:any>
            <xs:element name="note" type="Note_t" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="timestamp" type="Timestamp_t" minOccurs="0"/>
         </xs:sequence>
         <xs:attribute name="id" type="xs:ID" use="required"/>
      </xs:complexType>
   </xs:element> */

/* EXAMPLE:
   <dm:person id="p1">
      <rpid:activities from="2005-05-30T12:00:00+05:00"
         until="2005-05-30T17:00:00+05:00">
         <rpid:note>Far away</rpid:note>
         <rpid:away/>
      </rpid:activities>
      <rpid:class>calendar</rpid:class>
      <rpid:mood>
        <rpid:angry/>
        <rpid:other>brooding</rpid:other>
      </rpid:mood>
      <rpid:place-is>
         <rpid:audio>
            <rpid:noisy/>
         </rpid:audio>
      </rpid:place-is>
      <rpid:place-type><lt:residence/></rpid:place-type>
      <rpid:privacy><rpid:unknown/></rpid:privacy>
      <rpid:sphere>bowling league</rpid:sphere>
      <rpid:status-icon>http://example.com/play.gif</rpid:status-icon>
      <rpid:time-offset>-240</rpid:time-offset>
      <dm:note>Scoring 120</dm:note>
      <dm:timestamp>2005-05-30T16:09:44+05:00</dm:timestamp>
   </dm:person> */

   /** A struct. Contains information about the human user
     */
   struct Person
   {
      Id id;
      Notes notes;
      Optional<Timestamp> timestamp;
      Optional<Activities> activities;
      Optional<Class> classEnt; /* 'class' is reserved */
      Optional<Moods> mood;
      Optional<PlaceIs> placeIs;
      Optional<PlaceType> placeType;
      Optional<Privacy> privacy;
      Optional<Sphere> sphere;
      Optional<StatusIcon> statusIcon;
      Optional<TimeOffset> timeOffset;
      Optional<UserInput> userInput;   //RFC4480
      Optional<Status> status;
      Optional<Other> other; // free-form text
   };


/* =============================================================================================================================================================
 *            BEGIN pidf.xsd (rfc 4479)
 * ============================================================================================================================================================= */

/* <xs:simpleType name="qvalue">
      <xs:restriction base="xs:decimal">
         <xs:pattern value="0(.[0-9]{0,3})?"/>
         <xs:pattern value="1(.0{0,3})?"/>
      </xs:restriction>
   </xs:simpleType> */

/* EXAMPLE:
   priority="0.8" */

   typedef float QValue;

/* <xs:complexType name="contact">
      <xs:simpleContent>
         <xs:extension base="xs:anyURI">
            <xs:attribute name="priority" type="tns:qvalue"/>
         </xs:extension>
      </xs:simpleContent>
   </xs:complexType> */

/* EXAMPLE:
   <contact priority="0.8">im:<EMAIL></contact> */

/**
* A struct.
*/

   struct Contact
   {
      Optional<QValue> priority;
      URI contact;
   };

 

/* <xs:complexType name="tuple">
      <xs:sequence>
         <xs:element name="status" type="tns:status"/>
         <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="contact" type="tns:contact" minOccurs="0"/>
         <xs:element name="note" type="tns:note" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="timestamp" type="xs:dateTime" minOccurs="0"/>
      </xs:sequence>
      <xs:attribute name="id" type="xs:ID" use="required"/>
   </xs:complexType> */

/* EXAMPLE:
   <tuple id="bs35r9">
      <status>
         <basic>open</basic>
      </status>
      <dm:deviceID>urn:device:0003ba4811e3</dm:deviceID>
      <rpid:relationship><rpid:self/></rpid:relationship>
      <rpid:service-class><rpid:electronic/></rpid:service-class>
      <contact priority="0.8">im:<EMAIL></contact>
      <note xml:lang="en">Don't Disturb Please!</note>
      <note xml:lang="fr">Ne derangez pas, s'il vous plait</note>
      <timestamp>2005-10-27T16:49:29Z</timestamp>
   </tuple> */

/**
* A struct.
*/
   struct Tuple
   {
      Id id;
      Notes notes;
      Optional<Timestamp> timestamp;
      Status status;
      Optional<Contact> contact;
      Optional<DeviceId> deviceID;
      Optional<Relationship> relationship;
      Optional<ServiceClass> serviceClass;
      Optional<Class> classEnt;
      Optional<UserInput> userInput;   //RFC4480
   };


/* <xs:complexType name="presence">
      <xs:sequence>
         <xs:element name="tuple" type="tns:tuple" minOccurs="0" maxOccurs="unbounded"/>
         <xs:element name="note" type="tns:note" minOccurs="0" maxOccurs="unbounded"/>
         <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
      </xs:sequence>
      <xs:attribute name="entity" type="xs:anyURI" use="required"/>
   </xs:complexType> */

/* EXAMPLE:
   <presence xmlns="urn:ietf:params:xml:ns:pidf"
           xmlns:dm="urn:ietf:params:xml:ns:pidf:data-model"
           xmlns:lt="urn:ietf:params:xml:ns:location-type"
           xmlns:rpid="urn:ietf:params:xml:ns:pidf:rpid"
           entity="pres:<EMAIL>">
      <tuple id="ty4658">
         ...
      </tuple>
      <tuple id="eg92n8">
         ...
      </tuple>
      <note>I'll be in Tokyo next week</note>
      <dm:device id="pc147">
         ...
      </dm:device>
      <dm:person id="p1">
         ...
      </dm:person>
   </presence> */

/**
* A struct.
*/
   struct Presence
   {
      cpc::string entity;
      Notes notes;
      cpc::vector<Tuple> tuples;
      cpc::vector<Device> devices;
      cpc::vector<Person> persons;
   };
}
}
#endif // CPCAPI2_SIP_PRESENCE_MODEL_H