#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_PARSER_H)
#define CPCAPI2_SIP_PRESENCE_PARSER_H

#include <vector>
#include "SipPresenceModel.h"

namespace CPCAPI2
{
class Phone;

namespace SipPresence
{

class SipPresenceParser
{
public:
   /**
   * Get a reference to the %SipPresenceParser interface.
   */   
   static SipPresenceParser* getInterface(Phone* cpcPhone);

private:
};


}
}
#endif // CPCAPI2_SIP_PRESENCE_PARSER_H
