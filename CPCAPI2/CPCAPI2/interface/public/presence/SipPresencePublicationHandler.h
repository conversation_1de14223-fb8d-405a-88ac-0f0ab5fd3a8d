#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_PUBLICATION_HANDLER_H)
#define CPCAPI2_SIP_PRESENCE_PUBLICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "event/SipEventPublicationHandler.h"
#include "SipPresenceModel.h"

namespace CPCAPI2
{
namespace SipPresence
{

/**
 * Event passed in SipPresencePublicationHandler::onPublicationSuccess().
 */
struct PresencePublicationSuccessEvent
{
};

/**
 * Event passed in SipPresencePublicationHandler::onPublicationFailure().
 */
struct PresencePublicationFailureEvent
{
   CPCAPI2::SipEvent::SipPublicationFailureReason reason;
};

/**
 * Event passed in SipPresencePublicationHandler::onPublicationRemove().
 */
struct PresencePublicationRemoveEvent
{
};

/**
* Event passed in SipPresencePublicationHandler::onError(); 
* used to report general SDK error conditions, such as invalid handles, or cases
* where the subscription is not in a valid state for the requested
* operation.
*/
struct PresencePublicationErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to publishing the local user's presence; 
* set in SipPresenceManager::setPublicationHandler().
*/
class SipPresencePublicationHandler
{
public:
   /**
    * Notifies the application when a PUBLISH succeeds.
    */
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationSuccessEvent& args) = 0;
   
   /**
    * Notifies the application when a PUBLISH fails.
    */
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationFailureEvent& args) = 0;
   
   /**
    * Notifies the application when a publication was successfully removed.
    */
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationRemoveEvent & args) = 0;
   
   /**
    * Used to report general SDK error conditions, such as invalid handles, or cases where the subscription is not in a 
    * valid state for the requested operation.
    */
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PresencePublicationErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_PRESENCE_PUBLICATION_HANDLER_H
