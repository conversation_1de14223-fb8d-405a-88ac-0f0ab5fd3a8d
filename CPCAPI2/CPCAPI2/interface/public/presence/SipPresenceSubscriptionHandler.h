#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_SUBSCRIPTION_HANDLER_H)
#define CPCAPI2_SIP_PRESENCE_SUBSCRIPTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "event/SipEventSubscriptionHandler.h"
#include "SipPresenceModel.h"

namespace CPCAPI2
{
namespace SipPresence
{
using CPCAPI2::SipEvent::SipEventSubscriptionHandle;

/**
 * Event passed in SipPresenceSubscriptionHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
 * an outgoing SUBSCRIBE has forked, OR
 * an incoming SUBSCRIBE has been received.
 */
struct NewPresenceSubscriptionEvent
{
   CPCAPI2::SipEvent::SubscriptionType   subscriptionType;
   cpc::string                           remoteAddress;
   cpc::string                           remoteDisplayName;
   CPCAPI2::SipAccount::SipAccountHandle account;
};

/**
 * Event passed in SipPresenceSubscriptionHandler::onSubscriptionEnded().
 * The client or server subscription session terminated (for any reason).
 */
struct PresenceSubscriptionEndedEvent
{
   unsigned int                                     retryTime;
   CPCAPI2::SipEvent::SipSubscriptionEndReason      endReason;
   unsigned int                                     statusCode; // Error code
};

/**
 * Event passed in SipPresenceSubscriptionHandler::onIncomingPresenceStatus().
 * An incoming NOTIFY was received.
 */
struct IncomingPresenceStatusEvent
{
   Presence presence;
   CannedStatus status;
};

/**
 * Event passed in SipPresenceSubscriptionHandler::onSubscriptionStateChanged().
 * The state of the incoming or outgoing subscription has changed.
 * This might happen for any of the following reasons:
 * <ul>
 * <li>The remote party accepted the subscription request
 * <li>The remote party rejected or terminated the subscription
 * <li>The event server is transitioning to an out-of-service state
 * </ul>
 */
struct PresenceSubscriptionStateChangedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionState    subscriptionState;
};

/**
 * Event passed in SipPresenceSubscriptionHandler::onPresenceReadyToSend().
 * A pre-populated Presence document is ready. Invoked by the SDK
 * after the application calls SipPresenceManager::preparePresence(..)
 */
struct PresenceReadyToSendEvent
{
   Presence presence;
};

/**
* Event passed in SipPresenceSubscriptionHandler::onError(); 
* used to report general SDK error conditions, such as invalid handles, or cases
* where the subscription is not in a valid state for the requested
* operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to presence subscriptions; 
* set in SipPresenceManager::setHandler().
*/
class SipPresenceSubscriptionHandler
{
public:
   /**
    * Notifies the application when an outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
    * an outgoing SUBSCRIBE has forked, OR
    * an incoming SUBSCRIBE has been received.
    */
   virtual int onNewSubscription(SipEventSubscriptionHandle subscription, const NewPresenceSubscriptionEvent& args) = 0;

   /**
    * Notifies the application when the client or server subscription session terminated (for any reason).
    */
   virtual int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const PresenceSubscriptionEndedEvent& args) = 0;
   
   /**
    * Notifies the application when an incoming NOTIFY was received.
    */
   virtual int onIncomingPresenceStatus(SipEventSubscriptionHandle subscription, const IncomingPresenceStatusEvent& args) = 0;
   
   /**
    * Notifies the application when the state of the subscription has changed, due to either the remote party accepting
    * the subscription request, or the remote party rejecting or terminating the subscription.
    */
   virtual int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const PresenceSubscriptionStateChangedEvent& args) = 0;
   
   /**
    * Invoked by the SDK after the application calls SipPresenceManager::preparePresence(..)
    */
   virtual int onPresenceReadyToSend(SipEventSubscriptionHandle subscription, const PresenceReadyToSendEvent& args) = 0;
   
   /**
    * Used to report general SDK error conditions, such as invalid handles, or cases where the subscription is not in a 
    * valid state for the requested operation.
    */
   virtual int onError(SipEventSubscriptionHandle publication, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_PRESENCE_SUBSCRIPTION_HANDLER_H
