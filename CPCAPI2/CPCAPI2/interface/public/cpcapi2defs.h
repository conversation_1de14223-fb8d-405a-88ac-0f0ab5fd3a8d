#pragma once

#if !defined(CPCAPI2_CPCAPI2DEFS_H)
#define CPCAPI2_CPCAPI2DEFS_H

#ifndef CPCAPI2_SHAREDLIBRARY_EXPORT
#if _WIN32
  #define CPCAPI2_SHAREDLIBRARY_EXPORT __declspec(dllexport)
#else
  #define CPCAPI2_SHAREDLIBRARY_EXPORT __attribute__((__visibility__("default")))
#endif
#endif

#ifndef CPCAPI2_SHAREDLIBRARY_API
#if _WIN32
  #ifdef CPCAPI2_STATIC_LIB
    #define CPCAPI2_SHAREDLIBRARY_API
  #else
    #ifdef CPCAPI2_SHAREDLIBRARY_EXPORTS
      #define CPCAPI2_SHAREDLIBRARY_API CPCAPI2_SHAREDLIBRARY_EXPORT
    #else
      #define CPCAPI2_SHAREDLIBRARY_API __declspec(dllimport)
    #endif
  #endif
#else
  #define CPCAPI2_SHAREDLIBRARY_API CPCAPI2_SHAREDLIBRARY_EXPORT
#endif
#endif

// This macro will be used to mark methods as deprecated:
// (stolen from http://stackoverflow.com/questions/295120/c-mark-as-deprecated )
//
// In order to avoid warnings/errors from the DEPRECATED macro, just define
// CPCAPI2_NO_DEPRECATIONS
#ifndef CPCAPI2_NO_DEPRECATIONS
#ifdef __GNUC__
#define DEPRECATED __attribute__((deprecated))
#elif defined(_MSC_VER)
#define DEPRECATED __declspec(deprecated)
#else
#pragma message("WARNING: You need to implement DEPRECATED for this compiler")
#define DEPRECATED
#endif
#else
#define DEPRECATED
#endif

#ifdef WIN32
// Don't include winsock.h
#pragma push_macro("_WINSOCKAPI_")
#ifndef _WINSOCKAPI_
#define _WINSOCKAPI_
#endif
#ifdef _CONSOLE
#define STDMETHODCALLTYPE __stdcall
#else
#include <unknwn.h>
#endif
#pragma pop_macro("_WINSOCKAPI_")
#endif

#ifdef CPCAPI2_NO_CXX11
#define OVERRIDE
#else //CPCAPI2_NO_CXX11
#define OVERRIDE override
#endif //CPCAPI2_NO_CXX11

//TODO There are GCC extensions for fallthrough before C++17
#if __cplusplus >= 201703L
#define FALL_THROUGH [[fallthrough]]
#else
#define FALL_THROUGH
#endif

#if defined(WIN32)
#include <WinSock2.h>
#endif

#include <cstdint>

namespace CPCAPI2
{
const int kSuccess = 0;
const int kError = 0x80000001;

namespace SipAccount
{

typedef unsigned int SipAccountHandle;

} // namespace: SipAccount

static const int kBlockingModeNonBlocking = -1;
static const int kBlockingModeInfinite = 0;
static const int kModuleDisabled = -1;

} // namespace: CPCAPI2

#endif // CPCAPI2_CPCAPI2DEFS_H
