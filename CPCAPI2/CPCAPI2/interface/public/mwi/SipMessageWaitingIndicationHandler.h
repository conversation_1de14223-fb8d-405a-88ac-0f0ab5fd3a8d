#pragma once

#if !defined(CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_HANDLER_H)
#define CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "event/SipEventSubscriptionHandler.h"

namespace CPCAPI2
{
namespace SipMessageWaitingIndication
{
using CPCAPI2::SipEvent::SipEventSubscriptionHandle;

/**
 * Event passed in SipMessageWaitingIndicationHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission.
 */
struct NewMWISubscriptionEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;
};

/**
 * Event passed in SipMessageWaitingIndicationHandler::onSubscriptionEnded().
 * The client subscription session is terminated (for any reason).
 */
struct MWISubscriptionEndedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionEndReason      endReason;
};

/**
* A struct.
*/
struct MessageWaitingItem
{
   /**
    * The type of message that is waiting.
    */
   enum Type 
   {
      Voice,
      Fax,
      Pager,
      Multimedia,
      Text,
      None 
   };     
   
   /**
    *  Get the type of the message that is waiting.
    */
   Type type;

   /**
    *  Get the count of "new messages"; the meaning of "new message" 
    *  is defined by the MWI server.
    */
   uint64_t newMessageCount; 

   /**
    *  Get the count of "old messages"; the meaning of "old message" 
    *  is defined by the MWI server.
    */
   uint64_t oldMessageCount;

   /**
    *  Get the count of "new urgent messages"; the meaning of "urgent message" 
    *  is defined by the MWI server.
    */
   uint64_t newUrgentMessageCount;

   /**
    *  Get the count of "old urgent messages"; the meaning of "urgent message" 
    *  is defined by the MWI server.
    */
   uint64_t oldUrgentMessageCount;
};

/**
 * Event passed in SipMessageWaitingIndicationHandler::onIncomingMWIStatus().
 * An incoming NOTIFY was received.
 */
struct IncomingMWIStatusEvent
{
   bool hasMessages;
   cpc::vector<MessageWaitingItem> items;
   CPCAPI2::SipAccount::SipAccountHandle accountHandle;
};

/**
 * Event passed in SipMessageWaitingIndicationHandler::onSubscriptionStateChanged().
 * The state of the outgoing subscription has changed.
 */
struct MWISubscriptionStateChangedEvent
{
   CPCAPI2::SipEvent::SipSubscriptionState    subscriptionState;
};

/**
* Event passed in SipMessageWaitingIndicationHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the subscription is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events related to MWI functionality; 
* set in SipMessageWaitingIndication::setHandler().
*/
class SipMessageWaitingIndicationHandler
{
public:
   /**
    * Notifies the application when an outgoing SUBSCRIBE has been submitted to the SIP stack for transmission.
    */
   virtual int onNewSubscription(SipEventSubscriptionHandle subscription, const NewMWISubscriptionEvent& args) = 0;
   
   /**
    * Notifies the application when the client subscription session is terminated (for any reason).
    */
   virtual int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const MWISubscriptionEndedEvent& args) = 0;
   
   /**
    * Notifies the application when an incoming NOTIFY was received.
    */
   virtual int onIncomingMWIStatus(SipEventSubscriptionHandle subscription, const IncomingMWIStatusEvent& args) = 0;
   
   /**
    * Notifies the application when the state of the outgoing subscription has changed.
    */
   virtual int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const MWISubscriptionStateChangedEvent& args) = 0;
   
   /**
    * Used to report general SDK error conditions, such as invalid handles, or cases where the subscription is not in 
    * a valid state for the requested operation.
    */
   virtual int onError(SipEventSubscriptionHandle subscription, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_HANDLER_H
