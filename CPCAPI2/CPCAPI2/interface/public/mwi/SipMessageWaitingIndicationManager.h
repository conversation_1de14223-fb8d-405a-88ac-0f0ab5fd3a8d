#pragma once

#if !defined(CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_MANAGER_H)
#define CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_MANAGER_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
class Phone;

namespace SipMessageWaitingIndication
{
class SipMessageWaitingIndicationHandler;

typedef uint32_t SipMWISubscriptionHandle;

/**
* A struct. 
*/ 
struct SipMWISubscriptionSettings
{
   uint32_t                     expiresSeconds;

   SipMWISubscriptionSettings()
   {
      expiresSeconds = 3600;
   }
};

/**
* Manager interface to subscribe for message waiting indication (MWI) 
* information that is stored on a voicemail server; 
* the interface conforms with RFC 3842; get a reference to the 
* interface using the static method getInterface(). 
* Only outgoing message-summary subscriptions are supported.
*/
class CPCAPI2_SHAREDLIBRARY_API SipMessageWaitingIndicationManager
{
public:
   /**
   * Get a reference to the %SipMessageWaitingIndicationManager interface.
   */   
   static SipMessageWaitingIndicationManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for subscription events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same 
   * thread as SipAccount::process(..)
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipMessageWaitingIndicationHandler* handler) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with start(..)
   * to begin a new outgoing (client) subscription session.
   * Note that some legacy systems send unsolicited out-of-dialog NOTIFYs for message-summary a short time after initial
   * registration (i.e. after a successful REGISTER/200 OK).  In this case, a call to createSubscription(..) is not
   * required -- event information will nonetheless be reported via the SipMessageWaitingIndicationHandler.
   */
   virtual SipMWISubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Invoked prior to calling start(..) to customize the default subscription parameters @see SIPMWISubscriptionSettings.
   */
   virtual int applySubscriptionSettings(SipMWISubscriptionHandle subscription, const SipMWISubscriptionSettings& settings) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE.
   */
   virtual int start(SipMWISubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
   */
   virtual int end(SipMWISubscriptionHandle subscription) = 0;
   

protected:
   /*
    * The SDK will manage memory life of %~SipMessageWaitingIndicationManager.
    */
   virtual ~SipMessageWaitingIndicationManager() {}

};

}
}

#endif // CPCAPI2_SIP_MESSAGE_WAITING_INDICATION_MANAGER_H
