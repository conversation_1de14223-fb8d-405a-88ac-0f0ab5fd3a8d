#pragma once

#if !defined(CPCAPI2_VIDEO_CODEC_SETTINGS_H)
#define CPCAPI2_VIDEO_CODEC_SETTINGS_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Media
{

/**
 * Configuration options for the H.264 codec.
 */
struct H264Config
{
    bool enableNonInterleavedMode;
    bool preferNonInterleavedMode;

    H264Config()
    {
        enableNonInterleavedMode = true;
        preferNonInterleavedMode = false;
    }
};

}
}

#endif // CPCAPI2_VIDEO_CODEC_SETTINGS_H