#pragma once

#if !defined(CPCAPI2_VIDEO_H)
#define CPCAPI2_VIDEO_H

#include "cpcapi2defs.h"
#include "VideoHandler.h"
#include "VideoCodecSettings.h"

namespace CPCAPI2
{
class Window;
namespace Media
{
class MediaManager;

enum VideoCaptureResolution
{
   VideoCaptureResolution_Low             = 1,
   VideoCaptureResolution_Standard        = 2,
   VideoCaptureResolution_High            = 3,
   VideoCaptureResolution_HD_1280x720p    = 4,
   VideoCaptureResolution_HD_1920x1080p   = 5,
   VideoCaptureResolution_MaxSupported    = 6,
   VideoCaptureResolution_848x480p        = 7
};

enum VideoMixMode
{
   VideoMixMode_ClientMixing              = 0,
   VideoMixMode_NoMixing                  = 32
};

enum PacketLossStrategy
{
   PacketLossStrategy_None                               = 0,
   PacketLossStrategy_Retransmit                         = 1,
   PacketLossStrategy_Retransmit_RTX_SSRC_multiplexing   = 2
};

enum VideoSurfaceType
{
  VideoSurfaceType_Default = 0,
  VideoSurfaceType_WindowsHWND = 1,
  VideoSurfaceType_WindowsWpfControl = 2
};

struct PacketLossConfig
{
   PacketLossStrategy packetLossStrategy;
};

/**
* Used with Video::setCaptureDevice(..) to specify that the outgoing
* video stream should be captured from the content of the local display
* instead of from a camera.
*
* Supported on Windows and OS X only.
*/
const unsigned int kScreenCaptureDeviceId = 0x00008001;
const unsigned int kCustomVideoSourceDeviceId = 0x00008002;

/**
* Manager interface for controlling video and video devices; 
* get a reference to the interface using the static method getInterface().
* Provides video device enumeration, selection, and control-related features.
*/
class CPCAPI2_SHAREDLIBRARY_API Video
{
public:
   /**
   * Get a reference to the %Video interface.
   */   
   static Video* getInterface(MediaManager* cpcPhone);

   /**
   * Sets the callback handler for video related events.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as
   * MediaManager::process(..)
   */
   virtual int setHandler(VideoHandler* handler) = 0;

   /**
    * Queries the list of video capture devices available on the system.
    * The set of devices reported is dependant on OS support.
    * See VideoHandler::onVideoDeviceListUpdated(..) for the results of the query.
    */
   virtual int queryDeviceList() = 0;

   /**
    * Selects the video capture device used during a video call and for local video preview.
    * The deviceId corresponds to the VideoDeviceInfo::id returned by calling queryDeviceList().
    */
   virtual int setCaptureDevice(unsigned int deviceId) = 0;

   /**
    * Sets the orientation of the capture device to allow video to be displayed in the correct orientation
    * This is primarily useful on mobile devices
    */
   virtual int setCaptureDeviceOrientation(VideoOrientation orientation) = 0;

   /**
    * Sets the orientation of the image to allow video to be displayed in the correct orientation on remote device
    * This is primarily useful on mobile devices
    */
   virtual int setCaptureImageOrientation(VideoImageOrientation orientation) = 0;
   
   /**
    * Specifies the target GUI surface used for rendering the far-end video during a call.
    * For most platforms type = VideoSurfaceType_Default is recommended.
    * On Windows:
    *     The surface is the HANDLE to the window used by the Direct3D renderer and the type is VideoSurfaceType_WindowsHWND or
    *     The surface is the value returned by DX9VideoControl.Handle() and the type is VideoSurfaceType_WindowsWpfControl
    */
   virtual int setIncomingVideoRenderTarget(void* surface, VideoSurfaceType type = VideoSurfaceType_Default) = 0;

   /**
   * Specifies the target GUI surface used for rendering the far-end video during a call.
   * For most platforms type = VideoSurfaceType_Default is recommended.
   * On Windows:
   *     The surface is the HANDLE to the window used by the Direct3D renderer and the type is VideoSurfaceType_WindowsHWND or
   *     The surface is the value returned by DX9VideoControl.Handle() and the type is VideoSurfaceType_WindowsWpfControl
   */
   virtual int setIncomingVideoRenderTarget(int recvVideoStreamId, void* surface, VideoSurfaceType type = VideoSurfaceType_Default) = 0;

   /**
    * Specifies the target GUI surface used for rendering the local capture video during a call.
    * For most platforms type = VideoSurfaceType_Default is recommended.
    * On Windows:
    *     The surface is the HANDLE to the window used by the Direct3D renderer and the type is VideoSurfaceType_WindowsHWND or
    *     The surface is the value returned by DX9VideoControl.Handle() and the type is VideoSurfaceType_WindowsWpfControl
    */
   virtual int setLocalVideoRenderTarget(void* surface, VideoSurfaceType type = VideoSurfaceType_Default) = 0;

   /**
    * Specifies the video capture resolution used when local video is rendered, but no calls
    * are active.  Use setPreferredResolution(..) to set the resolution used during a call.
    */
   virtual int setLocalVideoPreviewResolution(VideoCaptureResolution resolution) = 0;

   /**
    * Starts capturing from the video capture device specified in setCaptureDevice(..) 
    */
   virtual int startCapture() = 0;
   virtual int stopCapture() = 0;

   /**
    * Specifies the preferred video resolution.  Selecting a higher resolution
    * impacts both CPU usage and bandwidth usage.
    */
   virtual int setPreferredResolution(unsigned int codecId, VideoCaptureResolution resolution) = 0;

   /**
    * Queries the list of video codecs available in this version of the SDK.
    * The set of codecs reported is dependant on OS support.
    * See VideoHandler::onVideoCodecListUpdated(..) for the results of the query.
    */
   virtual int queryCodecList() = 0;

   /**
    * Enables or disables a codec. Changes take effect for subsequent new incoming/outgoing
    * calls.
    * @param codecId The id of the codec returned by VideoHandler::onVideoCodecListUpdated(..)
    * @param enabled True to enable the codec, false to disable
    */
   virtual int setCodecEnabled(unsigned int codecId, bool enabled) = 0;

   /**
    * Sets the priority of a codec. Higher value indicates a higher priority codec which should be used if possible
    * @param codecId The id of the codec returned by #VideoHandler::onVideoCodecListUpdated(..)
    * @param priority The priority of the codec. Higher value = greater priority to be used
    */
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) = 0;
	
   /**
    * Sets the payload type of a codec to be used in the initial SDP offer for a call.
    * Care must be taken to ensure different codecs do not share the same payloadType value.
    *
    * Note that the H.264 codec in this SDK can use two payload types; one for interleaved mode and one for 
    * non-interleaved mode. Setting the payload type here for H.264 will result in interleaved mode offered
    * as payloadType, and non-interleaved offered as payloadType - 1
    *
    * @param codecId The id of the codec returned by #AudioHandler::onAudioCodecListUpdated(..)
    * @param payloadType The payload type to be used.
    */
   virtual int setCodecPayloadType(unsigned int codecId, unsigned int payloadType) = 0;
	
   /**
    * Enables or disables hardware acceleration for encoding a given codec.
    *
    * Current support for hardware accelerated encoding includes:
    *   - VP8 (Android only)
    *   - H.264 (Android only)
    *
    * The availability of hardware accelerated encoding for a given codec is device dependant.
    * The SDK will use hardware accelerated encoding whenever it detects that it is available on the
    * device, unless hardware accelerated encoding has been explicitly disabled for a codec using
    * this function.
    *
    * @param codecId The id of the codec returned by VideoHandler::onVideoCodecListUpdated(..)
    * @param enabled True to allow using hardware acceleration for encoding for the given codec, false to disallow using hardware acceleration for encoding
    */
   virtual int setCodecEncodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled) = 0;

   /**
    * Enables or disables hardware acceleration for decoding a given codec.
    *
    * Current support for hardware accelerated decoding includes:
    *   - VP8 (Android only)
    *   - H.264 (Android only)
    *
    * The availability of hardware accelerated decoding for a given codec is device dependant.
    * The SDK will use hardware accelerated decoding whenever it detects that it is available on the
    * device, unless hardware accelerated decoding has been explicitly disabled for a codec using
    * this function.
    *
    * @param codecId The id of the codec returned by VideoHandler::onVideoCodecListUpdated(..)
    * @param enabled True to allow using hardware acceleration for decoding for the given codec, false to disallow using hardware acceleration for decoding
    */
   virtual int setCodecDecodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled) = 0;

   /**
    * Configures packet loss handling, specifically RFC 4585 and RFC 4588 behaviour with respect to NACK and RTX.
    */
   virtual int setPacketLossConfig(unsigned int codecId, const PacketLossConfig& packetLossConfig) = 0;

   /**
    * Mutes or unmutes the active video capture device.
    * When video is muted, local preview will still be presented, but
    * the captured video will not be sent to remote participants.
    */
   virtual int setVideoMute(bool enabled) = 0;

   /**
    * Sets the video mixing mode.
    */
   virtual int setVideoMixMode(VideoMixMode mixMode) = 0;

   /**
    * Enables or disables a video engine. 
    * @param enabled True to enable video engine, false to disable
    */
   virtual int setVideoApiEnabled(bool enabled) = 0;

   /**
    * Sets QoS (DSCP) value for video RTP streams.
    */
   virtual int setVideoDscp(unsigned int mediaDscp) = 0;
   
   /**
   * Enable/Disable video QoS Fastlane support on iOS 10.0 or higher
   * @param videoUseQosFastlane Whether video RTP socket will be tagged with SO_NET_SERVICE_TYPE with value NET_SERVICE_TYPE_VO
   * @param useDscp Whether video RTP socket will be tagged with IP_TOS with value provided to setVideoDscp method. Note Apple does not recommend using this approach
   */
   virtual int setVideoUseQosFastlane(bool useQosFastlane, bool useWithDscp = false) = 0;

   /**
    * Windows only: Shows the capture-device-specific property page.
    */
   virtual int showPropertyPage(void* surface) = 0;

   /**
   * Set per-codec configuration.
   * Changes are applied to media streams created after this function is called.
   */
   virtual int setCodecConfig(const H264Config& config) = 0;

   /**
   * Connects the received video stream (recvVideoStreamId) to the specified send
   * video stream (sendVideoStreamId).  
   * Encoded video received on the receive stream will be passed (without processing)
   * to each of the send streams specified, up to a limit of X streams.
   * A send video stream can only be connected to a single receive stream.
   * The video stream IDs are obtained via SipConversationHandler::onConversationMediaChanged(..).
   * It is not necessary to explicitly disconnect streams when e.g. the SIP calls 
   * with the sender or receivers are ending.
   */
   virtual int connectVideoStreams(int recvVideoStreamId, int sendVideoStreamId) = 0;

   /**
   * Disconnects the received video stream (recvVideoStreamId) and the specified send
   * video stream (sendVideoStreamId).
   * The video stream IDs are obtained via SipConversationHandler::onConversationMediaChanged(..).
   */
   virtual int disconnectVideoStreams(int recvVideoStreamId, int sendVideoStreamId) = 0;

   /**
    * Requests a key frame from the remote video sender.
    * The video stream ID is obtained via SipConversationHandler::onConversationMediaChanged(..).
    */
   virtual int requestKeyFrame(int recvVideoStreamId) = 0;

protected:
   /*
    * The SDK will manage memory life of %Video.
    */
   virtual ~Video() {}
};

}
}
#endif // CPCAPI2_VIDEO_H
