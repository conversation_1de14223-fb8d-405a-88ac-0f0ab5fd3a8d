#pragma once

#if !defined(CPCAPI2_VIDEO_HANDLER_H)
#define CPCAPI2_VIDEO_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace Media
{

enum VideoOrientation
{
  VideoOrientation_0 =    0,
  VideoOrientation_90 =   90,
  VideoOrientation_180 =  180,
  VideoOrientation_270 =  270
};
   
enum VideoImageOrientation
{
   VideoImageOrientation_Default,
   VideoImageOrientation_Portrait,
   VideoImageOrientation_Landscape
};

enum VideoCaptureState
{
    VideoCaptureState_Started,  // the device has been asked to capture video
    VideoCaptureState_Stopped,  // the device has been asked to stop capturing video
    VideoCaptureState_Hung,     // the device is started or resumed but has not provided a video frame for at least 3 seconds
    VideoCaptureState_Resumed,  // the device has provided a video frame subsequent to being hung
    VideoCaptureState_Error,    // the device has returned an error and therefore is not started
};

/**
* A struct. 
*/ 
struct VideoDeviceInfo
{
   cpc::string      friendlyName;
   unsigned int      id;
   VideoOrientation  orientation;
   unsigned int      cameraIndex;
};

/**
* Event passed in VideoHandler::onVideoDeviceListUpdated().
*/
struct VideoDeviceListUpdatedEvent
{
   cpc::vector<VideoDeviceInfo> deviceInfo;
};

/**
* A struct. 
*/ 
struct VideoCodecInfo
{
   cpc::string      codecName;
   unsigned int      id;
   bool              enabled;
   unsigned int      minBandwidth;
   unsigned int      maxBandwidth;
   unsigned int      priority;
   unsigned int      payloadType;
};

/**
* Event passed in VideoHandler::onVideoCodecListUpdated().
*/
struct VideoCodecListUpdatedEvent
{
   cpc::vector<VideoCodecInfo> codecInfo;
};

/**
* Event passed in VideoHandler::onVideoCodecListUpdated().
*/
struct VideoCaptureStateChangedEvent
{
   VideoCaptureState state;
};

/**
* Handler for events relating to video and video devices; set in Video::setHandler().
*/
class VideoHandler
{
public:
   /**
    * Called when the list of media devices on the system changes, or as a result of
    * calling Video::queryDeviceList().
    */
   virtual int onVideoDeviceListUpdated(const VideoDeviceListUpdatedEvent& args) = 0;

   /**
    * Called as a result of calling Video::queryCodecList().
    */
   virtual int onVideoCodecListUpdated(const VideoCodecListUpdatedEvent& args) = 0;

    /**
     * Called as a result of local video capture pausing or resuming.
     */
    virtual int onVideoCaptureStateChanged(const VideoCaptureStateChangedEvent& args) { return 0; };

};

}
}
#endif // CPCAPI2_VIDEO_HANDLER_H
