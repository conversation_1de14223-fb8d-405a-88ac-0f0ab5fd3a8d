#pragma once

#if !defined(CPCAPI2_MEDIA_MANAGER_H)
#define CPCAPI2_MEDIA_MANAGER_H

#include "cpcapi2defs.h"
#include "MediaStackSettings.h"

namespace CPCAPI2
{
class Phone;

namespace Media
{
enum DevicePerformanceProfile
{
   DevicePerformanceProfile_SlowMobile    = 0,
   DevicePerformanceProfile_Mobile        = 1,
   DevicePerformanceProfile_Desktop       = 2
};

/**
* Manager interface with generic functionality for controlling both audio and video; 
* get a reference to the interface using the static method getInterface(). 
* The SDK currently supports Audio and Video as optional modules;
* The Audio and Video modules are both registered within the same MediaManager
* so that they can work together for features such as lip sync.
*/
class CPCAPI2_SHAREDLIBRARY_API MediaManager
{
public:
   /**
   * Get a reference to the %MediaManager interface.
   */   
   static MediaManager* getInterface(Phone* cpcPhone);

   /**
    * Ensures that the Media stack has been fully initialized.
    * Must be called from within the main application thread before a SipAccount is enabled.
    * Note that on some operating systems, this function may trigger a consent prompt requiring
    * the user to allow access to the microphone, speaker, and camera.
    */
   virtual int initializeMediaStack(const MediaStackSettings& settings) = 0;
   virtual int initializeMediaStack() = 0;

   /**
    * Changing the values in MediaStackSettings require the media stack to be re-initialized,
    * and thus cannot be performed if there are any calls in progress.
    */
   virtual int updateMediaSettings(const MediaStackSettings& settings) = 0;

   /**
    * Sets the RTP keep alive interval.  Keep-alives will be sent when no media is flowing
    * in order to keep NAT pin holes open.
    */
   virtual int setRtpKeepAliveIntervalSeconds(unsigned long rtpKeepAliveIntervalSeconds) = 0;

   /**
	 * Voice Quality Monitoring enabled SDKs only
    *
    * Enables reporting and processing of statistics summary report blocks,
    * as defined in RFC 3611 section 4.6.
    * @see struct XRStatisticsSummary in SipConversationMediaTypes.h
    */
   virtual int setRtcpXrStatisticsSummaryReportsEnabled(bool enabled) = 0;
   
   /**
	 * Voice Quality Monitoring enabled SDKs only
    *
    * Enables reporting and processing of VoIP metrics report blocks,
    * as defined in RFC 3611 section 4.7.
    * @see struct XRVoipMetrics in SipConversationMediaTypes.h
    */
   virtual int setRtcpXrVoIPMetricsReportsEnabled(bool enabled) = 0;

   /**
    * Enable or disable Music On Hold feature.
    * If your app wishes to play music on hold, call this function, passing a true argument
    * before placing the call on hold; your app can then use SipConversationManager::playSound(..)
    * to play back an audio clip to the remote party during a locally initiated hold.
    */
   virtual int setMoHEnabled(bool enabled) = 0;

   /**
    * Specifies a device performance profile, used to tune settings such as 
    * codec computational complexity and maximum resolution.
    */
   virtual int setDevicePerformanceProfile(DevicePerformanceProfile performanceProfile) = 0;
   
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kMediaModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling MediaManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   /*
    * The SDK will manage memory life of %MediaManager.
    */
protected:
   virtual ~MediaManager() {}
};

}
}
#endif // CPCAPI2_MEDIA_MANAGER_H
