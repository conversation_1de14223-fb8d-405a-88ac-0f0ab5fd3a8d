#pragma once

#if !defined(CPCAPI2_ANDROID_AUDIO_H)
#define CPCAPI2_ANDROID_AUDIO_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "AudioHandler.h"

namespace CPCAPI2
{
namespace Media
{
class MediaManager;
/**
* Provides access to Android-specific features and functionality.
*/
class CPCAPI2_SHAREDLIBRARY_API AndroidAudio
{
public:
   /**
   * Get a reference to the %AndroidAudio interface.
   */
   static AndroidAudio* getInterface(MediaManager* cpcMediaManager);

   /**
   * See Audio.h - playSound
   * On Android, an audio usage is also required. See the Android documentation for android.media.AudioAttributes for the list of possible audio usages.
   * Common usages include:
   *  USAGE_VOICE_COMMUNICATION_SIGNALLING: In call tones (ex. DTMF, ringback, call waiting)
   *  USAGE_NOTIFICATION_RINGTONE: Ringtone
   *  USAGE_NOTIFICATION_COMMUNICATION_INSTANT: New instant message received
   */
   virtual PlaySoundHandle playSound(AudioDeviceRole role, int32_t audioUsage, const cpc::string& resourceUri, bool repeat = false) = 0;

   /**
   * Enables the application to override the SDK's default hardware echo cancellation
   * settings. By default, the SDK will use hardware echo cancellation, if available on a device,
   * except for particular phones where testing has shown that it does not work well; in this
   * case the SDK will fall back to using the more processor-intensive software echo cancellation.
   * @param enabled true to allow use of trhe device's echo cancellation (if available) or false to prevent it's use and fallback to sofware implementation
   */
   virtual int setHardwareEchoCancellationEnabled(const bool enabled) = 0;

   /**
   * Enables the application to override the SDK's default hardware automatic gain control
   * settings. By default, the SDK will use hardware automatic gain control, if available on a device,
   * except for particular phones where testing has shown that it does not work well; in this
   * case the SDK will fall back to using the more processor-intensive software automatic gain control if it is enabled.
   * @param enabled true to allow use of trhe device's automatic gain contrrol (if available) or false to prevent it's use and fallback to sofware implementation
   */
   virtual int setHardwareAutomaticGainControlEnabled(const bool enabled) = 0;

   /**
   * Enables the application to override the SDK's default hardware noise suppression
   * settings. By default, the SDK will use hardware noise suppression, if available on a device,
   * except for particular phones where testing has shown that it does not work well; in this
   * case the SDK will fall back to using the more processor-intensive software noise suppression.
   * @param enabled true to allow use of trhe device's noise suppression (if available) or false to prevent it's use and fallback to sofware implementation
   */
   virtual int setHardwareNoiseSuppressionEnabled(const bool enabled) = 0;

   /**
   * Enables the application to override the SDK's default low latency (OpenSL) audio playout for the current device.
   * By default, on Android 8.1 and below the SDK will use low latency OpenSL playout, if available on a given device,
   * except for particular phones where testing has shown that it does not work well; in this
   * case the SDK will fall back to using the AudioTrack back-end.
   * @param enabled true to allow use of OpenSL (if available) or false to prevent use of OpenSL
   */
   virtual int setLowLatencyPlayoutEnabled(const bool enabled) = 0;

   /**
   * Enable usage of AudioTrack's low latency mode.
   * By default, low latency mode is enabled on Android 8.1 and disabled on Android 9 and above.
   * On some Android 9 devices enabling low latency mode will result in no audio conditions
   * @param enabled true to allow use of AudioTrack low latency mode or false to prevent use of low latency mode
   */
   virtual int setLowLatencyAudioTrackEnabled(const bool enabled) = 0;

  /**
   * See http://developer.android.com/reference/android/media/MediaRecorder.AudioSource.html
   * By default, the SDK will use VOICE_COMMUNICATION so that the application can leverage
   * hardware AEC and Noise Suppression.
   */
   virtual int setAudioSource(int audioSource) = 0;
};

}
}
#endif // CPCAPI2_ANDROID_AUDIO_H
