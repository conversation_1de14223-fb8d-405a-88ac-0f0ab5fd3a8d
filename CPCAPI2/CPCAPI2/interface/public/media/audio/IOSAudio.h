#pragma once

#if !defined(CPCAPI2_IOS_AUDIO_H)
#define CPCAPI2_IOS_AUDIO_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Media
{
class MediaManager;
/**
* Provides access to iOS-specific features and functionality.
*/
class CPCAPI2_SHAREDLIBRARY_API IOSAudio
{
public:
   /**
   * Get a reference to the %IOSAudio interface.
   */   
   static IOSAudio* getInterface(MediaManager* cpcMediaManager);
   
   /**
    *
    */
   virtual int activatePlayAndRecordMode(bool activate) = 0;
   
   /**
    * For iOS CallKit integration. Should be called from either provider:didActivateAudioSession:
    * or provider:didDeactivateAudioSession CXProviderDelegate method. For further details, see the
    * iOS developer guide bundled with this SDK.
    */
   virtual int setAudioSessionActivated(bool activated) = 0;
   
   /**
    * Enable/Disable audio QoS Fastlane support on iOS 10.0 or higher
    * @param useQosFastlane Whether audio RTP socket will be tagged with SO_NET_SERVICE_TYPE with value NET_SERVICE_TYPE_VI
    * @param useDscp Whether audio RTP socket will be tagged with IP_TOS with value provided to setAudioDscp method. Note Apple does not recommend using this approach
    */
   virtual int setUseQosFastlane(bool useQosFastlane, bool useWithDscp = false) = 0;
   
   /**
    * iOS only: Enable/Disable audio interruptions handling. The SDK by default has audio interruption handling enabled.
    * Can be disabled at startup to avoid interruption handling causing conflicts with other media stacks.
    * If interruption handling is disabled, CallKit must be used for calls with the CounterPath SDK.
    * If interruption handling is disabled, ECPCKeepBgAliveStrategyHard should not be used with CPCiOSBackgroundManager.
    * @param enabled Whether audio interruptions handling should be enabled
    */
   virtual int setAudioInterruptionsHandlingEnabled(bool enabled) = 0;
};

}
}
#endif // CPCAPI2_IOS_AUDIO_H
