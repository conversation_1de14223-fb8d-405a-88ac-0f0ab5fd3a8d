#pragma once

#if !defined(CPCAPI2_AUDIO_HANDLER_H)
#define CPCAPI2_AUDIO_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace Media
{
typedef unsigned int PlaySoundHandle;

enum AudioDeviceRole
{
   AudioDeviceRole_None          = 0,
   AudioDeviceRole_Headset       = 1,
   AudioDeviceRole_SpeakerPhone    = 2,
   AudioDeviceRole_Ringing          = 3,
   AudioDeviceRole_Bluetooth          = 4
};

enum AudioDeviceType
{
   MediaDeviceType_Capture       = 1,
   MediaDeviceType_Render        = 2
};

// DEPRECATED; do not use
enum GainMode
{
   GainMode_None                 = 0,
   GainMode_Adaptive             = 1,
    // Supported on some android devices
   GainMode_NativeAGC            = 2,
   GainMode_Fixed                = 3
};

// DEPRECATED; do not use
struct GainConfig
{
   GainMode mode;
   // Target dB level for audio. In range [-31, 0]
   int targetLeveldB;
   // Dynamic range compression which can be applied to achieve target level. In range [0, 90]
   int compressionGaindB;

   GainConfig()
   {
      mode = GainMode_Adaptive;
      targetLeveldB = -3;
      compressionGaindB = 9;
   }
};

// DEPRECATED; do not use
struct SpeakerGainConfig
{
	bool enabled;
	float scale; //valid range [0.0,10.0]
	SpeakerGainConfig()
	{
	   enabled = true;
	   scale = 1.0;
	}
};

// DEPRECATED; do not use
struct GainSettings
{
   GainConfig rxConfig;
   GainConfig txConfig;
   SpeakerGainConfig spkConfig;
};

enum EchoCancellationMode
{
   EchoCancellationMode_None                            = 0,
   EchoCancellationMode_AggressiveEchoCancellation      = 1,
   EchoCancellationMode_DefaultEchoCancellation         = 2,
   EchoCancellationMode_LoudSpeakerphoneEchoSuppression = 3,
   EchoCancellationMode_SpeakerphoneEchoSuppression     = 4,
   EchoCancellationMode_LoudEarpieceEchoSuppression     = 5,
   EchoCancellationMode_EarpieceEchoSuppression         = 6,

   // DEPRECATED; use EchoCancellationMode_DefaultEchoCancellation instead
   EchoCancellationMode_NativeAEC                       = 7
};

enum NoiseSuppressionMode
{
   NoiseSuppressionMode_None                             = 0,
   NoiseSuppressionMode_Low                              = 1,
   NoiseSuppressionMode_Moderate                         = 2,
   NoiseSuppressionMode_High                             = 3,
   NoiseSuppressionMode_VeryHigh                         = 4,
   NoiseSuppressionMode_NativeNS                         = 5, // Supported on some android devices
   /*
    * // NVIDIA RTX noise removal for Windows and Linux. Requires an RTX (GeForce 2000 or later, Quadro, or Tesla)
    * Windows: 64 bit builds only and the user must install the redistributable from NVIDIA (https://www.nvidia.com/en-us/geforce/broadcasting/broadcast-sdk/resources/)
    * Linux: Restributable isn't released yet
    */
   NoiseSuppressionMode_RtxNs                            = 6,
};

enum VadMode
{
   VadMode_None                                          = 0,
   VadMode_Conventional                                  = 1,     // lowest reduction
   VadMode_AggressiveLow                                 = 2,
   VadMode_AggressiveMid                                 = 3,
   VadMode_AggressiveHigh                                = 4      // highest reduction
};

enum SystemAudioServiceErrorLevel
{
   SystemAudioServiceErrorLevel_None                            = 0,
   SystemAudioServiceErrorLevel_Unrecoverable                   = 1, // There will probably be no audio after this error
   SystemAudioServiceErrorLevel_Error                           = 2, // A serious error has occurred and there is a chance that audio isn't working
   SystemAudioServiceErrorLevel_Warning                         = 3, // An error has occurred, but the system should have recovered
   SystemAudioServiceErrorLevel_Debug                           = 4,
   SystemAudioServiceErrorLevel_SilentRecording                 = 5, // Silence was received from the audio device. Could be hardware mute or a audio device failure
   SystemAudioServiceErrorLevel_InactiveRecording               = 6, // No audio sample from the audio device while recording
   SystemAudioServiceErrorLevel_ActiveRecording                 = 7  // New audio sample received from the previously inactive audio device
};

/**
 * Special device ID that can be passed to Audio::setCaptureDevice(..) or Audio::setRenderDevice(..)
 * such that the SDK uses the default system device configured in the operating system.
 *
 * To discover the name and other information of the current default system device as set in the operating system,
 * call queryDeviceList() and pay attention to the defaultSystemDevice parameter of AudioDeviceInfo items
 * returned in the AudioDeviceListUpdatedEvent.
 *
 * Supported on Windows and OS X only.
 */
static const int kAudioDefaultSystemDeviceId = 1;

/**
 * Special device ID that can be passed to Audio::setCaptureDevice(..) or Audio::setRenderDevice(..)
 * such that the SDK uses the default system communication device configured in the operating system.
 *
 * To discover the name and other information of the current default system communication  device as set in
 * the operating system, call queryDeviceList() and pay attention to the defaultSystemCommDevice parameter of
 * AudioDeviceInfo items returned in the AudioDeviceListUpdatedEvent.
 *
 * Supported on Windows only.
 */
static const int kAudioDefaultSystemCommDeviceId = 2;

/**
* Special device ID that can be passed to Audio::setCaptureDevice(..)
* such that the SDK captures any sounds currently playing on the system audio output.
*
* Supported on Windows only.
*/
static const int kAudioLoopbackDeviceId = 3;


/**
* A struct.
*/
struct AudioDeviceInfo
{
   cpc::string          friendlyName;
   cpc::string          hid; // hardware ID may contain both vendor ID and product ID
   unsigned int         id;
   AudioDeviceRole      role;
   AudioDeviceType      deviceType;

   /**
    * true if the SDK does not recommend this device to be used (e.g. a virtual
    * audio device), especially as a device automatically chosen by the application.
    */
   bool                 inadvisable;

   /**
    * Windows and OS X only - whether this device is the system default
    */
   bool                 defaultSystemDevice;

   /**
    * Windows only - whether this device is the default communication device
    */
   bool                 defaultSystemCommDevice;
};

/**
* Event passed in AudioHandler::onAudioDeviceListUpdated().
*/
struct AudioDeviceListUpdatedEvent
{
   cpc::vector<AudioDeviceInfo> deviceInfo;
};

/**
* A struct.
*/
struct AudioCodecInfo
{
   cpc::string      codecName;
   unsigned int      id;
   bool              enabled;
   unsigned int      samplingRate;
   unsigned int      minBandwidth;
   unsigned int      maxBandwidth;
   unsigned int      priority;
   unsigned int      payloadType;
};

/**
* Event passed in AudioHandler::onAudioCodecListUpdated().
*/
struct AudioCodecListUpdatedEvent
{
   cpc::vector<AudioCodecInfo> codecInfo;
};

/**
* Event passed in AudioHandler::onAudioDeviceVolume().
*/
struct AudioDeviceVolumeEvent
{
   bool              micMuted;
   bool              speakerMuted;
   unsigned int      micVolumeLevel;
   unsigned int      speakerVolumeLevel;
};

/**
* Event passed in AudioHandler::onAudioDeviceLevelChange().
*/
struct AudioDeviceLevelChangeEvent
{
   unsigned int      inputDeviceLevel;
   unsigned int      outputDeviceLevel;
};

/**
* Event passed in AudioHandler::onSystemAudioServiceError().
*/
struct SystemAudioServiceErrorEvent
{
   SystemAudioServiceErrorLevel errorLevel;
};

struct AudioStreamStartedEvent
{
   int streamId;
};

struct AudioStreamStoppedEvent
{
   int streamId;
};

/**
* Handler for events relating to audio and audio devices;
* set in Audio::setHandler().
*/
class AudioHandler
{
public:
   /**
    * Called when the list of media devices on the system changes, or as a result of
    * calling Audio::queryDeviceList().
    */
   virtual int onAudioDeviceListUpdated(const AudioDeviceListUpdatedEvent& args) = 0;

   /**
    * Called when the audio specified in the call to MediaManager::playSound(..) has
    * successfully finished playing.
    */
   virtual int onPlaySoundComplete(PlaySoundHandle soundClip) = 0;

   /**
    * Called when the audio specified in the call to MediaManager::playSound(..) could
    * not be played.  Possible reasons include:
    *  - resource specified by resourceUri not found
    */
   virtual int onPlaySoundFailure(PlaySoundHandle soundClip) = 0;

   /**
    * Called as a result of calling Audio::queryCodecList().
    */
   virtual int onAudioCodecListUpdated(const AudioCodecListUpdatedEvent& args) = 0;

   /**
    * Called as a result of calling Audio::queryDeviceVolume().
    */
   virtual int onAudioDeviceVolume(const AudioDeviceVolumeEvent& args) = 0;

   /**
    * Called when a new input and/or output stream is created for audio capture/playback.
    */
   virtual int onAudioStreamStarted(const AudioStreamStartedEvent& args) {
      return CPCAPI2::kSuccess;
   }

   /**
   * Called when an input and/or output stream is stopped.
   */
   virtual int onAudioStreamStopped(const AudioStreamStoppedEvent& args) {
      return CPCAPI2::kSuccess;
   }

   /**
    * Called as a result of change in input and/or output device levels.
    */
   virtual int onAudioDeviceLevelChange(const AudioDeviceLevelChangeEvent& args) { return CPCAPI2::kSuccess; }

   /**
    * Called when an error is returned by the system. Ex. On Android, when AudioFlinger dies
    * Also called when audio device switches between inactive (no audio sample during recording) and active state (new audio sample in inactive state)
    */
   virtual int onSystemAudioServiceError(const SystemAudioServiceErrorEvent& args) { return CPCAPI2::kSuccess; }
};

}
}
#endif // CPCAPI2_AUDIO_HANDLER_H
