#pragma once

#if !defined(CPCAPI2_AUDIO_H)
#define CPCAPI2_AUDIO_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "AudioHandler.h"
#include "CodecSettings.h"

namespace CPCAPI2
{
namespace Media
{
class MediaManager;
typedef unsigned int PlaySoundHandle;
   
/**
* Manager interface to control audio and audio devices; 
* get a reference to the interface using the static method getInterface().
* Provides audio device enumeration, selection, and control-related features.
*/
class CPCAPI2_SHAREDLIBRARY_API Audio
{
public:
   /**
   * Get a reference to the %Audio interface.
   */   
   static Audio* getInterface(MediaManager* cpcMediaManager);

   /**
   * Sets the callback handler for audio related events.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as 
   * MediaManager::process(..)
   */
   virtual int setHandler(AudioHandler* handler) = 0;

   /**
    * Queries the list of audio capture and playout devices available on the system.
    * The set of devices reported is dependent on OS support.
    * See AudioHandler::onAudioDeviceListUpdated(..)  for the results of the query.
    */
   virtual int queryDeviceList() = 0;

   /**
    * Selects the audio capture/render device used for the voice path during an audio call.
    * The deviceId corresponds to the AudioDeviceInfo::id returned by calling queryDeviceList(),
    * or one of kAudioDeviceDefaultSystemId, kAudioDeviceDefaultCommunicationId, if supported
    * on your platform.
    * AudioDeviceRole "role" is tied to device with device identification number "deviceID ".
    * So if role=AudioDeviceRole_Ringing for example, device represented with deviceID will be
    * used to play ringing sound.
    */
   virtual int setCaptureDevice(unsigned int deviceId, AudioDeviceRole role) = 0;
   virtual int setRenderDevice(unsigned int deviceId, AudioDeviceRole role) = 0;

   /**
    * Plays the specified sound on the specified audio render device.
    * The resourceUri specifies the location of the audio data to be played.
    * 
    * Supported URI formats:
    *
    * DTMF tones, e.g. tone:n;duration=ms where n is 0..15 (standard) and 16..38 (extended) and ms is the duration in milliseconds (optional).
    * Resource URIs, e.g. /home/<USER>
    *
    * Standard DTMF tones:
    *
    * 0-9: 0-9
    * 10: *
    * 11: #
    * 12: A
    * 13: B
    * 14: C
    * 15: D
    *
    * Extended DTMF tones:
    *
    * 16: Flash
    * 17-26: Bellcore various cadences
    * 27: Dialtone with forwarding
    * 28: Dialtone with message waiting
    * 29: Call ended
 
    *
    * 31: Busy
    * 32: Ringback
    * 33: Dialtone
    * 34: Reorder
    * 35: Call waiting
    * 36: North American ringback
    * 37: British ringback
    * 38: European ringback
    *
    * If multiple devices are present and tied to appropriate AudioDeviceRole with setRenderDevice(...), sound will be 
    * played using audio device tied to "role".
    * So if role=AudioDeviceRole_Ringing is previously tied to any audio device it will be used to play ringing sound.
    */
   virtual PlaySoundHandle playSound(AudioDeviceRole role, const cpc::string& resourceUri, bool repeat = false) = 0;

   /**
    * Stop playing the specified sound.  Note that it is only necessary to call this if you wish to prematurely stop
    * the playing sound, or if you wish to stop playback when playSound was called with 'repeat' set to 'true'.
    */
   virtual int stopPlaySound(PlaySoundHandle sound) = 0;

   /**
    * Queries the list of audio codecs available in this version of the SDK.
    * The set of codecs reported is dependant on OS support.
    * See AudioHandler::onAudioCodecListUpdated(..) for the results of the query.
    */
   virtual int queryCodecList() = 0;

   /**
    * Enables or disables a codec.  Changes take effect for subsequent new incoming/outgoing
    * calls.
    * @param codecId The id of the codec returned by AudioHandler::onAudioCodecListUpdated(..)
    * @param enabled True to enable the codec, false to disable
    */
   virtual int setCodecEnabled(unsigned int codecId, bool enabled) = 0;

   /**
    * Sets the priority of a codec. Higher value indicates a higher priority codec which should be used if possible
    * @param codecId The id of the codec returned by #AudioHandler::onAudioCodecListUpdated(..)
    * @param priority The priority of the codec. Higher value = greater priority to be used
    */
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) = 0;
	
   /**
    * Sets the payload type of a codec to be used in the initial SDP offer for a call.
    * Care must be taken to ensure different codecs do not share the same payloadType value.
    * @param codecId The id of the codec returned by #AudioHandler::onAudioCodecListUpdated(..)
    * @param payloadType The payload type to be used.
    */
   virtual int setCodecPayloadType(unsigned int codecId, unsigned int payloadType) = 0;
	
   /**
    * Sets the payload type of telephone-event to be used in the initial SDP offer for a call.
    * Care must be taken to ensure different codecs do not share the same payloadType value.
    * @param payloadType The payload type to be used.
    */
   virtual int setTelephoneEventPayloadType(unsigned int payloadType) = 0;

   /**
    * Mutes or unmutes the active microphone device.
    */
   virtual int setMicMute(bool enabled) = 0;

   /**
    * Mutes or unmutes the active speaker device.
    */
   virtual int setSpeakerMute(bool enabled) = 0;

   /**
    * Sets the active microphone device volume.
    * @param level The desired level in the range 0 ... 100
    */
   virtual int setMicVolume(unsigned int level) = 0;

   /**
    * Activates and/or deactivates the software microphone level feature of the SDK,
    * and allows the microphone levels to be controlled in software as opposed to
    * by the device driver. A level of 100 will result in a 40% increase in gain
    * on the original sample, with no change around level of 70. Below that results
    * in attenuation.
    *
    * Audio may become clipped at high gain levels, so caution is advised. Use
    * feedback to the user in order to obtain the correct level.
    *
    * @param enbled true if the microphone volume should be software controlled,
    *        and false otherwise.
    * @param level The desired level in the range 0 ... 100
    */
   virtual int setMicSoftwareVolume( bool enabled, unsigned int level ) = 0;

   /**
    * Sets the active speaker device volume.
    * @param level The desired level in the range 0 ... 100
    */
   virtual int setSpeakerVolume(unsigned int level) = 0;

   /**
    * Queries the current status of microphone and speaker mute and volume levels.
    * See AudioHandler::onAudioDeviceVolume(..) for the results of the query.
    */
   virtual int queryDeviceVolume() = 0;

   /**
    * Configures Acoustic Echo Cancellation / Echo Suppression.
    * NOTE: Not supported on iOS, since the built-in device AEC is used.
    */
   virtual int setEchoCancellationMode(AudioDeviceRole role, EchoCancellationMode mode) = 0;

   /**
    * Configures the Noise Suppression audio filter.
    */
   virtual int setNoiseSuppressionMode(AudioDeviceRole role, NoiseSuppressionMode mode) = 0;

   /**
    * Configures Voice Activity Detection.
    */
   virtual int setVadMode(AudioDeviceRole role, VadMode mode) = 0;

   /**
    * DEPRECATED; do not use
    * Sets the RX/TX gain configuration used for all active and future audio streams
    */
   DEPRECATED virtual int setGainSettings(const GainSettings& settings) = 0;

   /**
    * Sets QoS (DSCP) value for audio RTP streams.
    */
   virtual int setAudioDscp(unsigned int mediaDscp) = 0;

   /**
   * Starts sending input device levels to application layer.
   */
   virtual int startMonitoringCaptureDeviceLevels(unsigned int deviceId) = 0;

   /**
   * Stops sending input device levels to application layer.
   */
   virtual int stopMonitoringCaptureDeviceLevels() = 0;

   /**
   * Starts sending output device levels to application layer.
   */
   virtual int startMonitoringRenderDeviceLevels(unsigned int deviceId, const cpc::string& resourceUri) = 0;

   /**
   * Stops sending output device levels to application layer.
   */
   virtual int stopMonitoringRenderDeviceLevels() = 0;

   /**
   * Set per-codec configuration.
   * Changes are applied to media streams created after this function is called.
   */
   virtual int setCodecConfig(const G729Config& config) = 0;

   /**
   * Set per-codec configuration.
   * Changes are applied to media streams created after this function is called.
   */
   virtual int setCodecConfig(const OpusConfig& config) = 0;

   /**
    * Forward encoded audio from recvStream to sendStream.
    */
   virtual int connectAudioStreams(int recvStreamId, int sendStreamId) = 0;
   virtual int disconnectAudioStreams(int recvStreamId, int sendStreamId) = 0;

protected:
   /*
    * The SDK will manage memory life of %Audio.
    */
   virtual ~Audio() {}
};

}
}
#endif // CPCAPI2_AUDIO_H
