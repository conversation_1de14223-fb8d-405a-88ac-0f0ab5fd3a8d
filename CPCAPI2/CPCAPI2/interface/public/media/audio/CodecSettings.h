#pragma once

#if !defined(CPCAPI2_CODEC_SETTINGS_H)
#define CPCAPI2_CODEC_SETTINGS_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Media
{

/**
 * Configuration options for the G.729 codec.
 */
struct G729Config
{
    bool useAnnexB;
    bool useRFC7261; // involves adjusting the SDP answer to match negotiated annexb

    G729Config()
    {
        useAnnexB = true;
        useRFC7261 = false; // assume previous behavior
    }
};

/**
 * Configuration options for the Opus codec.
 */
struct OpusConfig
{
   int complexity;
   int application;
   bool enableDtx;
   int bitrate;

   OpusConfig()
   {
      complexity = 5;
      application = 0; // 0=Voip, 1=Audio, 2=LowDelay
      enableDtx = true;
      bitrate = 64000;
   }
};

}
}

#endif // CPCAPI2_CODEC_SETTINGS_H