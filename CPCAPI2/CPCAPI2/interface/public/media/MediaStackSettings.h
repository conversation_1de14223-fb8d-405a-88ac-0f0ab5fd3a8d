#pragma once

#if !defined(CPCAPI2_MEDIA_STACK_SETTINGS_H)
#define CPCAPI2_MEDIA_STACK_SETTINGS_H

#include "cpcapi2defs.h"

namespace CPCAPI2
{
namespace Media
{

   // Audio device layers
   enum AudioLayers
   {
       AudioLayers_PlatformDefault = 0,
#ifdef _WIN32
       AudioLayers_WindowsWave = 1,
       AudioLayers_WindowsCore = 2,
#endif
       AudioLayers_LinuxAlsa = 3,
       AudioLayers_LinuxPulse = 4,
       AudioLayers_Dummy = 5,
       AudioLayers_File = 6,
       AudioLayers_AAudio = 7
   };

   struct MediaStackSettings
   {
      AudioLayers audioLayer;

      // Applicable to Android only: indicates stream type, defaults to 0 (voice_call)
      int streamType;
      // Deprecated: Use %AudioAudio setAudioSource method instead
      // Applicable to Android only: indicates audio source, defaults to 1 (AudioSource.MIC)
      int audioSource;

      // Used for servers, where local audio output is not desired
      bool audioOutputDisabled;

      // Used for servers, where a thread pool is used for audio encoding
      int numAudioEncoderThreads;

      MediaStackSettings()
      {
         audioLayer = AudioLayers_PlatformDefault;
         streamType = 0;
         audioSource = 1;
         audioOutputDisabled = false;
         numAudioEncoderThreads = 0;
      }
   };

}
}
#endif // CPCAPI2_MEDIA_STACK_SETTINGS_H
