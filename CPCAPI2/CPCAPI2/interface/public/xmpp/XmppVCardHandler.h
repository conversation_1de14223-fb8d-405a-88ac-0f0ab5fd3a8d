#pragma once

#ifndef __CPCAPI2_XMPP_VCARD_HANDLER_H__
#define __CPCAPI2_XMPP_VCARD_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppVCardManager.h"

namespace CPCAPI2
{
namespace XmppVCard
{
struct VCardFetchedEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppVCardHandle handle;
   cpc::string jid;
   XmppVCardDetail detail;
};

struct VCardOperationResultEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppVCardHandle handle;
   cpc::string jid;
   int type; // fetch (0) or store (1)
   DEPRECATED int result; // no error (0) or any error (non-zero)

   // deprecated 'result' is now replaced with the combination of 'success' and 'resultCode'
   bool success;
   int resultCode;
   cpc::string resultStr; // string value for resultCode

   int xmppErrorCode;
   cpc::string xmppErrorStr; // string value for xmppErrorCode
};

/**
 * Used to report general SDK error conditions, such as invalid handles,
 * or cases where the account is not in a valid state for the requested
 * operation. Handles will be set to 0 for general errors.
 */
struct ErrorEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppVCardHandle handle;
   cpc::string errorText;
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppVCardHandler
{
public:
   /**
    * This function is called when a VCard has been successfully fetched.
    */
   virtual int onVCardFetched(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt) = 0;

   /**
    * This function is called to indicate the result of a VCard operation
    */
   virtual int onVCardOperationResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt) = 0;

   /**
    * Used to report general SDK error conditions, such as invalid handles,
    * or cases where the account is not in a valid state for the requested
    * operation.
    */
   virtual int onError(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt) = 0;
};

cpc::string get_debug_string(const CPCAPI2::XmppVCard::VCardOperationResultEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::ErrorEvent& event);
}
}

#endif // __CPCAPI2_XMPP_VCARD_HANDLER_H__
