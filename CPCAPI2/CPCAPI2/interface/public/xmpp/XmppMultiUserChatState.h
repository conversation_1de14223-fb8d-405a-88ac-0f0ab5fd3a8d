#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppAccount.h"
#include "XmppMultiUserChatHandler.h"

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class XmppMultiUserChatManager;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
struct XmppMultiUserChatState
{
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   cpc::vector<ServiceAvailabilityEvent> services;
   XmppMultiUserChatRoomState room;
   cpc::vector<ParticipantState> participants;
   ParticipantState self;
   XmppAccount::XmppDataForm dataform;
   XmppMultiUserChatConfigurations configurations; // DEPRECATED

   XmppMultiUserChatState() : account(0) {}
};

struct XmppMultiUserChatStateInfo
{
   XmppMultiUserChatHandle muc;
   XmppMultiUserChatState state;

   XmppMultiUserChatStateInfo() : muc(0) {}
};

/**
 * Multi user chat state information. Accessed via XmppMultiUserChatManager.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppMultiUserChatStateManager
{

public:

   /**
    * Get a reference to the %XmppMultiUserChatStateManager interface.
    * A reference should be obtained shortly after creating an XMPP account.
    */
   static XmppMultiUserChatStateManager* getInterface(XmppMultiUserChatManager* manager);

   /**
    * Gets the state for a particular multi user chat.
    */
   virtual int getState(XmppMultiUserChatHandle handle, XmppMultiUserChatState& state) = 0;

   /**
    * Gets the multi user chat states for all the rooms for a particular XMPP account handle.
    */
   virtual int getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppMultiUserChatStateInfo>& states) = 0;

   /**
    * Gets the multi user chat states for all the rooms for all the existing XMPP accounts.
    */
   virtual int getAllStates(cpc::vector<XmppMultiUserChatStateInfo>& states) = 0;

protected:

   /**
    * The SDK will manage memory life of %XmppMultiUserChatStateManager.
    */
   virtual ~XmppMultiUserChatStateManager() {}

};

}
}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_H
