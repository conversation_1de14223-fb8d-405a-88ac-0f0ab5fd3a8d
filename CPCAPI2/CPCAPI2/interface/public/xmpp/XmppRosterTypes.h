#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_TYPES_H)
#define CPCAPI2_XMPP_ROSTER_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{

namespace XmppRoster
{

typedef cpc::vector<cpc::string> StringArray;

enum SubscriptionState
{
   SubscriptionState_None,              /**< Contact and user are not subscribed to each other, and
                                          * neither has requested a subscription from the other. */
   SubscriptionState_None_OutPending,   /**< Contact and user are not subscribed to each other, and
                                          * user has sent contact a subscription request but contact
                                          * has not replied yet. */
   SubscriptionState_None_InPending,    /**< Contact and user are not subscribed to each other, and
                                          * contact has sent user a subscription request but user has
                                          * not replied yet (note: contact's server SHOULD NOT push or
                                          * deliver roster items in this state, but instead SHOULD wait
                                          * until contact has approved subscription request from user). */
   SubscriptionState_None_InOutPending, /**< Contact and user are not subscribed to each other, contact
                                          * has sent user a subscription request but user has not replied
                                          * yet, and user has sent contact a subscription request but
                                          * contact has not replied yet. */
   SubscriptionState_Out,               /**< User is subscribed to contact (one-way). */
   SubscriptionState_Out_InPending,     /**< User is subscribed to contact, and contact has sent user a
                                          * subscription request but user has not replied yet. */
   SubscriptionState_In,                /**< Contact is subscribed to user (one-way). */
   SubscriptionState_In_OutPending,     /**< Contact is subscribed to user, and user has sent contact a
                                          * subscription request but contact has not replied yet. */
   SubscriptionState_InOut              /**< User and contact are subscribed to each other (two-way). */
};

enum PresenceType
{
   PresenceType_Available,                  /**< The entity is online. */
   PresenceType_Chat,                       /**< The entity is 'available for chat'. */
   PresenceType_Away,                       /**< The entity is away. */
   PresenceType_DND,                        /**< The entity is DND (Do Not Disturb). */
   PresenceType_XA,                         /**< The entity is XA (eXtended Away). */
   PresenceType_Unavailable,                /**< The entity is offline. */
   PresenceType_Probe,                      /**< This is a presence probe. */
   PresenceType_Error,                      /**< This is a presence error. */
   PresenceType_Invalid,                    /**< The stanza is invalid. */
   PresenceType_Unknown                     /**< Presence has not been set yet. */
};

enum UserActivityGeneralType
{
   ActivityDoingChores,
   ActivityDrinking,
   ActivityEating,
   ActivityExercising,
   ActivityGrooming,
   ActivityHavingAppointment,
   ActivityInactive,
   ActivityRelaxing,
   ActivityTalking,
   ActivityTraveling,
   ActivityUndefinedGeneralType,
   ActivityWorking,
   ActivityInvalidGeneralType
};

enum UserActivitySpecificType
{
   ActivityAtTheSpa,
   ActivityBrushingTeeth,
   ActivityBuyingGroceries,
   ActivityCleaning,
   ActivityCoding,
   ActivityCommuting,
   ActivityCooking,
   ActivityCycling,
   ActivityDancing,
   ActivityDayOff,
   ActivityDoingMaintenance,
   ActivityDoingTheDishes,
   ActivityDoingTheLaundry,
   ActivityDriving,
   ActivityFishing,
   ActivityGaming,
   ActivityGardening,
   ActivityGettingAHaircut,
   ActivityGoingOut,
   ActivityHangingOut,
   ActivityHavingABeer,
   ActivityHavingASnack,
   ActivityHavingBreakfast,
   ActivityHavingCoffee,
   ActivityHavingDinner,
   ActivityHavingLunch,
   ActivityHavingTea,
   ActivityHiding,
   ActivityHiking,
   ActivityInACar,
   ActivityInAMeeting,
   ActivityInRealLife,
   ActivityJogging,
   ActivityOnABus,
   ActivityOnAPlane,
   ActivityOnATrain,
   ActivityOnATrip,
   ActivityOnThePhone,
   ActivityOnVacation,
   ActivityOnVideoPhone,
   ActivityOther,
   ActivityPartying,
   ActivityPlayingSports,
   ActivityPraying,
   ActivityReading,
   ActivityRehearsing,
   ActivityRunning,
   ActivityRunningAnErrand,
   ActivityScheduledHoliday,
   ActivityShaving,
   ActivityShopping,
   ActivitySkiing,
   ActivitySleeping,
   ActivitySmoking,
   ActivitySocializing,
   ActivityStudying,
   ActivitySunbathing,
   ActivitySwimming,
   ActivityTakingABath,
   ActivityTakingAShower,
   ActivityThinking,
   ActivityWalking,
   ActivityWalkingTheDog,
   ActivityWatchingAMovie,
   ActivityWatchingTv,
   ActivityWorkingOut,
   ActivityWriting,
   ActivityInvalidSpecificType
};

struct ResourceItem
{
   cpc::string resource;
   int priority;
   PresenceType presenceType;
   cpc::string presenceStatusText;

   // XEP-0108
   UserActivityGeneralType userActivityGeneralType;
   UserActivitySpecificType userActivitySpecificType;
   cpc::string userActivityText;

   bool isCiscoRichPresence;
   bool isCiscoCustomStatus;

   ResourceItem() :
      resource(""),
      priority(0),
      presenceType(PresenceType_Unknown),
      presenceStatusText(""),
      userActivityGeneralType(ActivityInvalidGeneralType),
      userActivitySpecificType(ActivityInvalidSpecificType),
      isCiscoRichPresence(false),
      isCiscoCustomStatus(false)
   {}
};

struct RosterItem
{
   cpc::string address;
   cpc::string displayName;
   cpc::vector<cpc::string> groups;
   SubscriptionState subscription;
   cpc::vector<ResourceItem> resources;

   RosterItem() : subscription(SubscriptionState_None) {}
};

cpc::string get_debug_string(const CPCAPI2::XmppRoster::SubscriptionState& state);
cpc::string get_debug_string(const CPCAPI2::XmppRoster::PresenceType& type);
cpc::string get_debug_string(const CPCAPI2::XmppRoster::UserActivityGeneralType& type);
cpc::string get_debug_string(const CPCAPI2::XmppRoster::UserActivitySpecificType& type);
cpc::string get_debug_string(const CPCAPI2::XmppRoster::ResourceItem& item);
cpc::string get_debug_string(const CPCAPI2::XmppRoster::RosterItem& item);

} // namespace XmppRoster

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_TYPES_H
