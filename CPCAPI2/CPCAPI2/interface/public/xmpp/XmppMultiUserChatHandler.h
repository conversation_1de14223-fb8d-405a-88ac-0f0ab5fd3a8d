#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppMultiUserChatTypes.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppChatTypes.h"
#include "xmpp/XmppRosterTypes.h"

namespace CPCAPI2
{
namespace XmppMultiUserChat
{

struct ServiceAvailabilityEvent
{
   bool available;
   cpc::string service;
};

struct RoomListItem
{
   cpc::string jid;
   cpc::string name;
   cpc::string node;
};

struct RoomListRetrievedEvent
{
   cpc::vector<RoomListItem> rooms;
};

struct ParticipantState
{
   ParticipantState()
      : presence(XmppRoster::PresenceType_Unknown)
      , affiliation(AffiliationNone)
      , role(RoleNone)
      , isBanned(false)
      , isKicked(false)
      , isRemoved(false)
      , isRoomDestroyed(false)
   {}

   cpc::string nickname;
   cpc::string jid;
   XmppRoster::PresenceType presence;
   cpc::string message; // for presence
   XmppMultiUserChatAffiliation affiliation;
   XmppMultiUserChatRole role;
   bool isBanned;
   bool isKicked;
   bool isRemoved;
   bool isRoomDestroyed; // bliu: not all servers support broadcasting <destroy/> when removing all participants
};

struct ParticipantAddedEvent
{
   cpc::string nickname;
   ParticipantState state;
};

struct ParticipantRemovedEvent
{
   cpc::string nickname;
   cpc::string jid;
   cpc::string reason;
};

struct ParticipantUpdatedEvent
{
   cpc::string nickname;
   cpc::string reason;
   ParticipantState state;
};

struct ParticipantSelfUpdatedEvent
{
   cpc::string jid; // maker
   cpc::string reason;
   ParticipantState state;
};

struct ParticipantChatStateEvent
{
   cpc::string nickname;
   cpc::string jid; // maker
   XmppChat::IsComposingMessageState state; // The state of the 'isComposing' indicator
};

struct LocalUserLeftEvent
{
   cpc::string reason;
};

struct MultiUserChatReadyEvent
{
   int features;
   DEPRECATED cpc::string room; // only partial room ID without service portion
   cpc::string roomjid; // full room ID
   bool isNewRoom;
};

struct MultiUserChatConfigurationRequestedEvent
{
   XmppAccount::XmppDataForm dataform;
   DEPRECATED XmppMultiUserChatConfigurations configurations;
};

struct MultiUserChatListRequestedEvent
{
   XmppMultiUserChatListType type;
   cpc::vector<XmppMultiUserChatConfigurationsListItem> items;
};

struct MultiUserChatSubjectChangedEvent
{
   cpc::string nickname; // from
   cpc::string subject; // new subject
};

struct MultiUserChatNewMessageEvent
{
   XmppMultiUserChatMessageHandle message;
   cpc::string messageId; // message ID
   cpc::string stanzaId; // The message stanza ID (XEP-0359) if provided
   cpc::string originId; // The message origin ID (XEP-0359) if provided
   cpc::string nickname; // from
   cpc::string jid; // from
   cpc::string plain;
   cpc::string html; // the app layer needs to handle the unescaping
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond; // millisecond part of the timestamp
   bool isPrivate; // is a private message
   bool isDelayedDelivery; // did message include a delayed time stamp
   cpc::string replaces; // The message ID this message replaces in case of message correction
};

struct MultiUserChatNewReactionEvent
{
   XmppMultiUserChatMessageHandle message;
   cpc::string messageId; // message ID
   cpc::string nickname; // from
   cpc::string jid; // from
   cpc::string target;
   cpc::vector<cpc::string> reactions;
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond; // millisecond part of the timestamp
   bool isPrivate; // is a private message
   bool isDelayedDelivery; // did message include a delayed time stamp
};

struct MultiUserChatNewMessageRetractionEvent
{
   XmppMultiUserChatMessageHandle message;
   cpc::string messageId; // message ID
   cpc::string nickname; // from
   cpc::string jid; // from
   cpc::string target;
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond; // millisecond part of the timestamp
   bool isPrivate; // is a private message
   bool isDelayedDelivery; // did message include a delayed time stamp
};

struct SendMessageSuccessEvent
{
   XmppMultiUserChatMessageHandle message; // the new message
   cpc::string messageId; // the message ID
   cpc::string replaces; // The message ID this message replaces in case of message correction
};

/**
 * Event fired by the SDK when a new message failed to send.
 */
struct SendMessageFailureEvent
{
   XmppMultiUserChatMessageHandle message; // the new message
};

struct MultiUserChatInvitationReceivedEvent
{
   DEPRECATED cpc::string room; // only partial room ID without service portion
   cpc::string roomjid; // full room ID
   cpc::string jid; // from
   cpc::string reason;
   cpc::string password;
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond; // millisecond part of the timestamp
   bool isDelayedDelivery; // did message include a delayed time stamp
};

struct MultiUserChatInvitationDeclinedEvent
{
   cpc::string jid; // invitee
   cpc::string reason;
};

enum MultiUserChatErrorType
{
   NoError,
   PasswordRequired,
   UserBanned,
   RoomNotFound,
   CreationRestricted,
   ReservedNicknameRequired,
   NotAMember,
   NicknameConflict,
   MaximumUsersReached,
   OtherError
};

struct MultiUserChatErrorEvent
{
   MultiUserChatErrorType type;
   cpc::string error;
};

struct MultiUserChatRoomStateChangedEvent
{
   XmppMultiUserChatRoomState state;
};

struct RoomBookmarksReceivedEvent
{
   cpc::vector<RoomBookmark> bookmarks;
};

struct NewRoomEvent
{
   CPCAPI2::XmppAccount::XmppAccountHandle hAccount;
   cpc::string roomjid;
};

struct MessageDeliveredEvent
{
   cpc::string messageId;                        // The message ID
   cpc::string from;                             // The originator of the message
   uint64_t timestamp;                           // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;                         // The millisecond part of the timestamp
   bool isDelayedDelivery;                       // Did message include a delayed time stamp
};

/**
 * Event fired by the SDK when a message was read by the other participant's device(s).
 */
struct MessageReadEvent
{
   cpc::string messageId;                        // The message ID
   cpc::string from;                             // The originator of the message
   uint64_t timestamp;                           // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;                         // The millisecond part of the timestamp
   bool isDelayedDelivery;                       // Did message include a delayed time stamp
};

/**
 * Handler for evts relating creating, destroying and updating multi user chat.
 * Set in XmppMultiUserChatManager::setHandler().
 */
class XmppMultiUserChatHandler
{
public:

   /**
    * Notifies that the service is available on the server.
    * Query the evt for information.
    * A handler to this event is optional.
    */
   virtual void onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& evt) {}

   /**
    * Notifies that a list of rooms has been retrieved from the server.
    */
   virtual void onRoomListRetrieved(XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& evt) = 0;

   /**
    * Notifies that a participant has joined the multi user chat. Doesn't include self joining a room.
    * Expect for onParticipantSelfUpdated as a self event instead.
    * Query the evt for the participant.
    */
   virtual void onParticipantAdded(XmppMultiUserChatHandle handle, const ParticipantAddedEvent& evt) = 0;

   /**
    * Notifies that a participant has left the multi user chat or has been kicked out.
    * Query the evt for the participant.
    */
   virtual void onParticipantRemoved(XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& evt) = 0;

   /**
    * Notifies that a participant's information has changed. Doesn't include self state changes.
    * Query the evt for the change.
    */
   virtual void onParticipantUpdated(XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& evt) = 0;

   /**
    * Notifies that the local user's information has changed.
    * Query the evt for information.
    */
   virtual void onParticipantSelfUpdated(XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& evt) = 0;

   /**
    * Notifies that the multi user chat is ready to be used; called in response to
    * XmppMultiUserChat::create() (if supportsConfigurationRequest() is false)
    * or in response to XmppMultiUserChat::submitConfigurationDataForm() or
    * MultiUserChatConfigurationRequest::submit() (if %supportsConfigurationRequest()
    * is true).
    */
   virtual void onMultiUserChatReady(XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& evt) = 0;

   /**
    * Notifies that the subject of the multi user chat has changed.
    * Query the evt for information.
    */
   virtual void onMultiUserChatSubjectChanged(XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& evt) = 0;

   /**
    * Notifies that a new message has been received.
    * Query the evt for information.
    */
   virtual void onMultiUserChatNewMessage(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& evt) = 0;

   /**
    * Notifies that a new reaction has been received.
    * Query the evt for information.
    */
   virtual void onMultiUserChatNewReaction(XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& evt) = 0;

   /**
    * Notifies that a new message retraction has been received.
    * Query the evt for information.
    */
   virtual void onMultiUserChatNewMessageRetraction(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageRetractionEvent& evt) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message was successful.
    * Query the evt for information.
    * A handler to this event is optional.
    */
   virtual void onSendMessageSuccess(XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& args) {}

   /**
    * Callback invoked by the SDK when the sending of a message failed.
    * Query the evt for information.
    * A handler to this event is optional.
    */
   virtual void onSendMessageFailure(XmppMultiUserChatHandle handle, const SendMessageFailureEvent& args) {}

   /**
    * Notifies that the local user has received an chat state message ("typing..") as part of the
    * multi user chat; this callback is received only on a multi user chat that is in progress
    * and for which the local user is a participant.
    * Notice that local user self chat state changes will also be recognized as participant chat state change.
    * Query the evt for information.
    */
   virtual void onParticipantChatStateReceived(XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& evt) = 0;

   /**
    * Notifies that an invitation to the local user has been received.
    * Query the evt for information.
    */
   virtual void onMultiUserChatInvitationReceived(XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& evt) = 0;

   /**
    * Notifies that an invitation that the local user sent has been declined by the recipient.
    * Query the evt for information.
    */
   virtual void onMultiUserChatInvitationDeclined(XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& evt) = 0;

   /**
    * Notifies that an error has occurred for this multi user chat.
    */
   virtual void onMultiUserChatError(XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& evt) = 0;

   /**
    * Notifies that the local user has left the multi user chat.
    * (If another participant leaves, onParticipantRemoved() is called.)
    * The evt is informational (contains no content).
    */
   virtual void onLocalUserLeft(XmppMultiUserChatHandle handle, const LocalUserLeftEvent& evt) = 0;

   /**
    * Called on the multi user chat owner when the multi user chat has been created and
    * passes information about the multi user chat; called only if
    * MultiUserChatHandler::supportsConfigurationRequest() is true. Query the evt
    * for the configuration form.
    */
   virtual void onMultiUserChatConfigurationRequested(XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& evt) = 0;

   /**
    * Notifies that configuration information about the multi user chat has changed.
    * Query the evt for information.
    */
   virtual void onMultiUserChatRoomStateChanged(XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& evt) = 0;

   virtual void onMultiUserChatListRequested(XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& evt) = 0;

   virtual void onRoomBookmarksReceived(XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& evt) = 0;

   virtual void onNewRoomHandle( XmppMultiUserChatHandle handle, const NewRoomEvent& evt ) = 0;

      /**
    * Callback invoked by the SDK when the message was successfully delivered
    * to the other participant's device(s).
    *
    * @param chat the chat session that the new message was sent against.
    * @param args information about the message delivered.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDelivered(XmppMultiUserChatHandle handle, const MessageDeliveredEvent& evt) { return kSuccess; }

   /**
 * Callback invoked by the SDK when the message was read
 * to the other participant's device(s).
 *
 * @param chat the chat session that the new message was read.
 * @param args information about the message read.
 *
 * return kSuccess if the operation was successful, kError otherwise.
 */
   virtual int onMessageRead(XmppMultiUserChatHandle handle, const MessageReadEvent& evt) { return kSuccess; }

};

} // XmppMultiUserChat
} // CPCAPI2

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_H
