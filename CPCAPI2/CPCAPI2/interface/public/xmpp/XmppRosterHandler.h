#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_HANDLER_H)
#define CPCAPI2_XMPP_ROSTER_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "XmppRosterTypes.h"
#include "XmppCannedPresence.h"

namespace CPCAPI2
{
namespace XmppRoster
{
typedef unsigned int XmppRosterHandle;

struct XmppRosterUpdateEvent
{
   struct ChangeItemAdd
   {
      RosterItem item;
   };
   struct ChangeItemUpdate
   {
      RosterItem item;
   };
   struct ChangeItemRemove
   {
      cpc::string address;
   };
   bool fullUpdate;
   cpc::vector<ChangeItemAdd> added;
   cpc::vector<ChangeItemUpdate> updated;
   cpc::vector<ChangeItemRemove> removed;
};

struct XmppRosterUpdateFailedEvent
{
   unsigned int errorCode;
   cpc::string errorText;
};

struct XmppRosterPresenceEvent
{
   RosterItem rosterItem;
   cpc::string resource;
   XmppCannedPresence compositeCannedPresence;
};

struct XmppRosterSubscriptionRequestEvent
{
   cpc::string address;
   cpc::string msg;
};

struct XmppRosterUnsubscriptionRequestEvent
{
   cpc::string address;
   cpc::string msg;
};

/**
 * Event passed in XmppAccountHandler::onError().
 */
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * The handler for events on XmppAccount; passed in XmppAccount::setHandler().
 */
class XmppRosterHandler
{
public:
   /**
    * Notifies the application when the %XmppRoster has been updated on the server.
    * May result from a local operation, or a change initiated by a remote client logged in with the same credentials.
    */
   virtual int onRosterUpdate(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUpdateEvent& args) = 0;

   /**
    * Notifies the application when a subscription request was received
    */
   virtual int onSubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterSubscriptionRequestEvent& args) = 0;

   /**
    * Notifies the application when an unsubscription request was received
    */
   virtual int onUnsubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUnsubscriptionRequestEvent& args) = 0;

   /**
    * Notifies the application when one or more items in the XmppRoster have updated presence status information available.
    */
   virtual int onRosterPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args) = 0;

   /**
    * Notifies the application when self presence status information was available.
    */
   virtual int onSelfPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args) { return kSuccess; } // no override required

   /**
    * Notifies the application when an account error has occurred
    */
   virtual int onError(CPCAPI2::XmppRoster::XmppRosterHandle roster, const ErrorEvent& args) = 0;
};

} // namespace XmppRoster
} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_HANDLER_H
