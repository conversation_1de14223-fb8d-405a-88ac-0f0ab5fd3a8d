#pragma once

#if !defined(CPCAPI2_XMPP_CANNED_PRESENCE_H)
#define CPCAPI2_XMPP_CANNED_PRESENCE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "xmpp/XmppRosterTypes.h"

namespace CPCAPI2
{

namespace XmppRoster
{

enum XmppCannedStatus
{
   XmppCannedStatus_Available,                  // precedence 100
   XmppCannedStatus_Chat,                       // precedence 110
   XmppCannedStatus_Away,                       // precedence 50 Away presence with lower precedence than Available
   XmppCannedStatus_AppearAway,                 // precedence 200 Away presence with higher precedence than Available
   XmppCannedStatus_Busy,                       // precedence 500
   XmppCannedStatus_HavingLunch,                // precedence 210
   XmppCannedStatus_OnVacation,                 // precedence 220
   XmppCannedStatus_ScheduledHoliday,           // precedence 230
   XmppCannedStatus_InactiveOther,              // precedence 299
   XmppCannedStatus_OnThePhone,                 // precedence 700
   XmppCannedStatus_DoNotDisturb,               // precedence 900
   XmppCannedStatus_NotAvailableForCalls,       // precedence 800
   XmppCannedStatus_AppearOffline,              // precedence 1000 Offline presence with highest precedence which should only be used among Bria clients
   XmppCannedStatus_Invisible,                  // precedence 0 Offline presence with the ability to receive incoming presences and messages
   XmppCannedStatus_Offline,                    // precedence 0 Offline presence without the ability to receive incoming presences and messages
   XmppCannedStatus_Other,                      // precedence 1
   XmppCannedStatus_Invalid                     // precedence 1
};

struct XmppCannedPresence
{
   cpc::string resource;
   XmppCannedStatus status;
   cpc::string note;
   int priority;

   XmppCannedPresence()
   {
      resource = "";
      status = XmppCannedStatus_Invalid;
      note = "";
      priority = -1;
   };

   XmppCannedPresence(const CPCAPI2::XmppRoster::RosterItem &rosterItem);

   // Converts canned presence into XMPP presence and user activity for publishing presence
   static void cannedStatusToPresence(XmppCannedStatus status,
         CPCAPI2::XmppRoster::PresenceType &presenceType,
         CPCAPI2::XmppRoster::UserActivityGeneralType &userActivityGeneralType,
         CPCAPI2::XmppRoster::UserActivitySpecificType &userActivitySpecificType,
         cpc::string &userActivityText);
};

} // namespace XmppRoster

} // namespace CPCAPI2

#endif
