#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_STATE_H)
#define CPCAPI2_XMPP_VCARD_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "XmppVCardHandler.h"

namespace CPCAPI2
{
namespace XmppVCard
{

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
*/
class XmppVCardManager;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
*/
struct XmppVCardState
{
   XmppVCardDetail detail;
   int fetchResult; // result code for fetch operation
   int storeResult; // result code for store operation

   XmppVCardState() : fetchResult(0), storeResult(0) {}
};

struct XmppVCardStateInfo
{
   XmppVCardHandle vcard;
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   cpc::string jid;
   XmppVCardState state;

   XmppVCardStateInfo() : vcard(0), account(0), jid("") {}
};

/**
   * VCard state information. Accessed via XmppVCardManager.
*/
class CPCAPI2_SHAREDLIBRARY_API XmppVCardStateManager
{

public:

   /**
    * Get a reference to the %XmppVCardStateManager interface.
    * A reference should be obtained shortly after creating an XMPP account.
    */
   static XmppVCardStateManager* getInterface(XmppVCardManager* manager);

   /**
    * Gets the vcard state for a particular XMPP jid.
    */
   virtual int getState(XmppVCardHandle handle, const cpc::string& jid, XmppVCardState& state) = 0;

   /**
    * Gets the vcard state for all the jids for a particular XMPP vcard handle.
    */
   virtual int getAllStates(XmppVCardHandle handle, cpc::vector<XmppVCardStateInfo>& states) = 0;

   /**
    * Gets the vcard state for all the jids for a particular XMPP account handle.
    */
   virtual int getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppVCardStateInfo>& states) = 0;

   /**
    * Gets the vcard state for all the existing XMPP accounts.
    */
   virtual int getAllStateInfo(cpc::vector<XmppVCardStateInfo>& states) = 0;

protected:

   /**
    * The SDK will manage memory life of %XmppVCardStateManager.
    */
   virtual ~XmppVCardStateManager() {}

};

cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardState& state);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardStateInfo& state);

}
}

#endif // CPCAPI2_XMPP_VCARD_STATE_H
