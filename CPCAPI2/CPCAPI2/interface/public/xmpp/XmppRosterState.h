#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_STATE_H)
#define CPCAPI2_XMPP_ROSTER_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppRoster.h"
#include "xmpp/XmppRosterTypes.h"

namespace CPCAPI2
{

namespace XmppRoster
{

class XmppRosterManager;

struct XmppRosterState
{
   XmppAccount::XmppAccountHandle account;
   XmppRosterHandle roster;
   cpc::vector<RosterItem> rosterItems;

   XmppRosterState()
   {
      account = (-1);
      roster = (-1);
   }
};

class CPCAPI2_SHAREDLIBRARY_API XmppRosterStateManager
{

public:

   /**
    * Get a reference to the XmppRosterStateManager interface.
   */
   static XmppRosterStateManager* getInterface(XmppRosterManager* cpcRosterManager);

   virtual int getRosterState(XmppRosterHandle roster, XmppRosterState& state) = 0;
   virtual int getRosterStateForAccount(XmppAccount::XmppAccountHandle account, XmppRosterState& state) = 0;
   virtual int getAllRosterState(cpc::vector<XmppRosterState>& states) = 0;

protected:

   /**
    * The SDK will manage memory life of %XmppRosterStateManager.
   */
   virtual~ XmppRosterStateManager() {};

};

cpc::string get_debug_string(const CPCAPI2::XmppRoster::XmppRosterState& state);

}

}

#endif // CPCAPI2_XMPP_ROSTER_STATE_H
