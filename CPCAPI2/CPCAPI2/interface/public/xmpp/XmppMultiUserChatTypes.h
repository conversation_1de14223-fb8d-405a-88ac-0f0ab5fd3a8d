#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_TYPES_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace XmppMultiUserChat
{

typedef unsigned int XmppMultiUserChatHandle;
typedef unsigned int XmppMultiUserChatMessageHandle;

/**
 * MutliUserChat room configuration
 */
struct RoomConfig
{
   bool isInstant;                  // An instant room is created with default configurations vesus a reserved room requires setConfigurations().
   //bool isPublic;                 // A public room can be discovered by getRoomList() vesus a hidden room requires the knowledge of the room JID.
   //bool isPersistent;             // A persistent room remains open even without any participant vesus a temporary room does not.
   bool createIfNotExisting;        // Whether the room should be recreated if not existing

   RoomConfig()
      : isInstant(false)
      , createIfNotExisting(true)
   {}
};

/**
 * MultiUserChat room state.
 */
struct XmppMultiUserChatRoomState
{

   /**
    * Returns true if the multi user chat was created by the local user
    * (rather than by another user inviting the local user).
    */
   bool isCreatedBySelf; // bliu: TODO

   /**
    * True means the multi user chat is ready to use;
    * MultiUserChatHandler::onMultiUserChatReady() serves the same purpose.
    */
   bool isReady;

   /**
    * Get the subject of the multi user chat
    */
   cpc::string subject;

   cpc::string name;
   cpc::string description;
   cpc::string creation; // the format is uncertain. don't use as a timestamp.

   bool isPublic;
   bool isPasswordProtected;
   bool isOpen;
   bool isModerated;
   bool isPersistent;
   bool isRecorded;

   /**
    * 0: NotAnonymous Anyone can obtain information (using call Participant::getAddress()).
    * 1: SemiAnonymous Only the moderator can obtain information.
    * 2: FullyAnonymous No one can obtain information.
    */
   int anonymousMode;

   int numOfParticipants;

   /**
    * List of room owners. Alternate to retrieving owner list via
    * XmppMultiUserChatManager::requestList(..), but retrieving the
    * list here in XmppMultiUserChatRoomState may not be supported
    * by all XMPP servers.
    */
   cpc::vector<cpc::string> owners;

   int maxHistoryFetch;

   XmppMultiUserChatRoomState()
      : isCreatedBySelf(false)
      , isReady(false)
      , isPublic(false)
      , isPasswordProtected(false)
      , isOpen(false)
      , isModerated(false)
      , isPersistent(false)
      , isRecorded(false)
      , anonymousMode(0)
      , numOfParticipants(0)
      , maxHistoryFetch(0)
   {}
};

/**
 * Structure that contains one item of history from the multi user chat
 * that the local user will join; an empty vector of these structs is
 * used in the JoinData struct that is passed in
 * MultiUserChat::join(). Once the join request is accepted
 * by the server, the vector will be filled with content.
 */
struct XmppMultiUserChatHistoryItem
{
   cpc::string from; // jid
   cpc::string plain;
   cpc::string html;
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;
};

/**
 * Holds possible affiliations for a participant in a multi user chat;
 * The enumerations are as per XEP-0045.
 */
enum XmppMultiUserChatAffiliation
{
   AffiliationNone,
   AffiliationOutcast,
   AffiliationMember,
   AffiliationOwner,
   AffiliationAdmin,
   AffiliationInvalid
};

/**
 * Holds possible roles for a participant in a multi user chat;
 * The enumerations are as per XEP-0045.
 */
enum XmppMultiUserChatRole
{
   RoleNone,
   RoleVisitor,
   RoleParticipant,
   RoleModerator,
   RoleInvalid
};

enum XmppMultiUserChatListType
{
   VoiceList,
   BanList,
   MemberList,
   ModeratorList,
   OwnerList,
   AdminList
};

// DEPRECATED
struct XmppMultiUserChatConfigurationItem
{
   int type;
   cpc::string name;
   bool required;
   cpc::string label;
   cpc::vector<cpc::string> values;
};

// DEPRECATED
struct XmppMultiUserChatConfigurations
{
   int type;
   cpc::vector<cpc::string> instructions;
   cpc::string title;
   cpc::vector<XmppMultiUserChatConfigurationItem> items;
};

// when calling setList(), populate only one of affiliation or role but not both otherwise setList() may fail on some server
struct XmppMultiUserChatConfigurationsListItem
{
   cpc::string jid;
   cpc::string nick;
   XmppMultiUserChatAffiliation affiliation;
   XmppMultiUserChatRole role;
   cpc::string reason;

   XmppMultiUserChatConfigurationsListItem()
      : affiliation(AffiliationInvalid)
      , role(RoleInvalid)
   {
   }
};

struct RoomBookmark
{
   cpc::string name;       // the friendly name for the bookmark, must present for the bookmark to be valid
   cpc::string jid;        // the JID for the MUC room, must present for the bookmark to be valid
   cpc::string nickname;   // the nickname of the user in the MUC room
   cpc::string password;
   bool autojoin;
};

} // XmppMultiUserChat
} // CPCAPI2

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_TYPES_H
