#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_H)
#define CPCAPI2_XMPP_ACCOUNT_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "phone/NetworkChangeManager.h"
#include "xmpp/XmppAccountSettings.h"
#include "xmpp/XmppRosterTypes.h"
#include "xmpp/XmppCannedPresence.h"

namespace CPCAPI2
{
class Phone;

namespace XmppAccount
{
class XmppAccountHandler;
typedef unsigned int XmppAccountHandle;

struct XmppDataFormField
{
   int type;
   cpc::string name;
   bool required;
   cpc::string label;
   cpc::vector<cpc::string> values;

   XmppDataFormField() : type(-1), name(""), required(false), label("") {};
};

// bliu: doesn't support Xmpp DataForm <reported> type yet
struct XmppDataForm
{
   int type;
   cpc::vector<cpc::string> instructions;
   cpc::string title;
   cpc::vector<XmppDataFormField> fields;

   XmppDataForm() : type(-1), title("") {};
};

struct XmppStorageData
{
   cpc::string name; // need to be escaped as non-XML
   cpc::string value; // need to be escaped as non-XML
};

/**
 * Main entry point for XMPP-related functionality.
 *
 * Usage:
 * <ol>
 *    <li> Get a reference to the XmppAccount module using XmppAccountManager::getInterface(phone)
 *    <li> Create the XmppAccount using XmppAccountManager::create(settings)
 *    <li> Set an account handler using XmppAccountManager::setHandler(..)
 *    <li> Enable the account using XmppAccountManager::enable(..)
 * </ol>
 */
class CPCAPI2_SHAREDLIBRARY_API XmppAccountManager
{
public:
   /**
    * Get a reference to the XmppAccountManager interface.
    */
   static XmppAccountManager* getInterface(Phone* cpcPhone);

   static const cpc::string& getServiceId();

   /**
    * Create an account.
    */
   virtual XmppAccountHandle create() = 0;

   /**
    * Create an account.
    * @param XmppAccountSettings The XmppAccountSettings that holds configuration information for this account.
    */
   virtual XmppAccountHandle create(const XmppAccountSettings& settings) = 0;

   /**
    * Configure the default account settings.
    * Call configureDefaultAccountSettings(), and then applySettings().
    *
    * @param account the account handle.
    * @param settings the XmppAccountSettings that holds configuration information for this account.
    */
   virtual int configureDefaultAccountSettings(XmppAccountHandle account, const XmppAccountSettings& settings) = 0;

   /**
    * Apply the account settings configured with configureDefaultAccountSettings()
    *
    * @param account the account handle
    */
   virtual int applySettings(XmppAccountHandle account) = 0;

   /**
    * Set the handler for events on the specified account. Set the handler
    * immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
    */
   virtual int setHandler(XmppAccountHandle account, XmppAccountHandler* handler) = 0;

   /**
    * Send out presence status
    */
   virtual int publishPresence(XmppAccountHandle account,
         XmppRoster::PresenceType presence,
         const cpc::string& note = "",
         const XmppRoster::UserActivityGeneralType& userActivityGeneralType = XmppRoster::ActivityInvalidGeneralType,
         const XmppRoster::UserActivitySpecificType& userActivitySpecificType = XmppRoster::ActivityInvalidSpecificType,
         const cpc::string& userActivityText = "") = 0;

   /**
    * Send out presence status using canned status
    */
   virtual int publishCannedPresence(XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note = "") = 0;

   /**
    * Enable the specified account so that it can be used to communicate with the XMPP server.
    *
    * You should use XmppAccountHandler::onAccountStatusChanged()
    * to be notified of progress after invoking %enable().
    */
   virtual int enable(XmppAccountHandle account) = 0;

   /**
    * Disable the specified account: if account will be discconnected if it connected.
    * You should use XmppAccountHandler::onAccountStatusChanged()
    * to be notified of progress after invoking %disable().
    */
   virtual int disable(XmppAccountHandle account) = 0;

   /**
    * Deletes an account, uses the handle obtained from create.
    * This call would have no effect at all and raise Error_NotDisconnected if the account is enabled.
    */
   virtual int destroy(XmppAccountHandle handle) = 0;

   /**
    * Request a refresh of the current registration.
    *
    * @param deadlineSecondsFromNow Set to 0 to request an unconditional refresh. Otherwise specify a value
    * to be compared against the number of seconds remaining before the next
    * scheduled refresh by the SDK, wherein a refresh will only be performed now
    * if (timeUntilNextRefresh < deadlineSecondsFromNow).
    */
   //virtual int requestRegistrationRefresh(XmppAccountHandle account, unsigned int deadlineSecondsFromNow) = 0;

   /**
    * Set or clear a restriction on use of the specified network type.
    */
   virtual int setNetworkRestriction(XmppAccountHandle account, NetworkTransport transport, bool restricted) = 0;

   /**
    * Block incoming presence
    * Deprecated, use setInactive() instead
    */
   DEPRECATED virtual int blockIncomingPresence(XmppAccountHandle account, bool block) = 0;

   /**
    * Enable or disable XEP-0352 Client State Indication.
    * This feature allows the client to inform the server when the user is not actively using the client,
    * allowing the server to optimise traffic to the client accordingly.
    * (It is up to the server configurations to determine how to optimise the traffic)
    * This can save bandwidth and resources on both the client and server.
    * E.g. the app layer may call setInactive(account, true) before the client is sent into background or is idle for too long,
    * and setInactive(account, true) after the client is brought back to foreground or gains some input from the user.
    * @param account the account handle.
    * @param inactive true for enabling, false for disabling
    */
   virtual int setInactive(XmppAccountHandle account, bool inactive) = 0;

   /**
    * Enable or disable hibernation which informs the server to queue up messages sent to a mobile client
    * @param account the account handle.
    * @param active true for enabling, false for disabling
    */
   virtual int setHibernationState(XmppAccountHandle account, bool active) = 0;

   /**
    * Get entity time. A result will be notified via EntityTimeEvent.
    * @param account the account handle.
    * @param jid the entity's JID. If empty, the server time will be queried by default.
    */
   virtual int getEntityTime(XmppAccountHandle account, const cpc::string& jid = "") = 0;

   /**
    * Enable notification.
    * @param account the account handle.
    * @param node the notification node.
    * @param dataform the OPTIONAL notification configurations.
    */
   virtual int enableNotification(XmppAccountHandle account, const cpc::string& node, const XmppDataForm& dataform = XmppDataForm()) = 0;

   /**
    * Disable notification.
    * @param account the account handle.
    * @param node the notification node. Disable all notifications if empty node is provided.
    */
   virtual int disableNotification(XmppAccountHandle account, const cpc::string& node = "") = 0;

   /**
    * Set/get private storage data.
    */
   virtual int getPrivateStorageData(XmppAccountHandle account) = 0;
   virtual int setPrivateStorageData(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data) = 0;

   /**
    * The blocking modes for the process() function. See that function
    * for details.
    */
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;

   // constants
   static const int kAccountModuleDisabled = -1;

   /**
    * Allows the application code to receive callback notifications from the SDK.
    *
    * These callbacks will happen synchronously, in the same thread of execution as that in which
    * %process() is invoked.  Depending on the application threading model,
    * process() can be used in two different ways:
    * <ol>
    * <li>blocking mode ?Typically in this mode, %process() is called by the
    * application from a background (worker) thread.  The call to process()
    * blocks until a callback function needs to be invoked.
    * <li>non-blocking mode ?In this mode, %process() is called by the application
    * from the main (GUI) thread of the application, typically from the
    * main message/event loop.  In this mode, %process() returns immediately
    * and so must be called frequently enough that the application can receive
    * its callback notifications in a timely manner.
    *
    * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
    *                representing the time the call to process(..) will block waiting for a callback
    *                from the SDK
    * </ol>
    */
   virtual int process(unsigned int timeout) = 0;

   // The following (synchronous) methods are used in conjunction with the RemoteSync Module
   virtual cpc::string getRemoteSyncAccountID(XmppAccountHandle account) = 0;
   virtual XmppAccountHandle getAccountHandleFromRemoteSyncID(cpc::string accountID) = 0;

   virtual int send(XmppAccountHandle account, const cpc::string& xml) = 0;

   /**
    * Decodes a provisioning response received from the Provisioning interface.
    * @param provisioningResponse The complete provisioning response, as returned by the Provisioning interface.
    * @param outXmppAccountSettings A vector of XmppAccountSettings; the number of items in the vector will be equivalent to the number of sets of Xmpp account settings in the provisioning response. If a particular setting was not provisioned down, the SDK will use a default value.
    */
   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<XmppAccountSettings>& outXmppAccountSettings) = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppAccountManager.
    */
   virtual ~XmppAccountManager() {}
};

}
}
#endif // CPCAPI2_XMPP_ACCOUNT_H
