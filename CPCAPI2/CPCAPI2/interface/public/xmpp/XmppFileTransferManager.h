#pragma once

#ifndef __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_H__
#define __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_H__

#include <cstdint>

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppAccount.h"

namespace CPCAPI2
{
// forward declarations
class Phone;

namespace XmppFileTransfer
{
// forward declarations
class XmppFileTransferHandler;

// type defs
typedef unsigned int XmppFileTransferHandle;
typedef unsigned int XmppFileTransferItemHandle;

/**
 * Ternary state (accepted, rejected, or unprocessed). Each of the
 * file transfer items can be in one of these three states.
 */
enum AcceptedState
{
   ftitem_notprocessed = 1400, // default
   ftitem_accepted     = 1410, // user (or possibly remote user) has accepted the item
   ftitem_rejected     = 1420  // user (or possibly remote user) has denied the item
};

/**
 * File transfer type (Socks5, Inband, etc).
 * Each of the file transfer items can be in one or multiple of the types. Use bitwise OR for multiple types.
 */
enum FileTransferStreamType
{
   FileTransferStreamType_Socks5    = 1,     // Not valid if remote target is not a JID
   FileTransferStreamType_InBand    = 2,     // Not valid if remote target is not a JID
   FileTransferStreamType_OutOfBand = 4,     // Not yet supported, do NOT use
   FileTransferStreamType_XEP0363   = 8,     // Not valid if remote target is not blank
   FileTransferStreamType_All       = 255,   // DEPRECATED replaced with FileTransferStreamType_Auto
   FileTransferStreamType_Auto      = 255,   // the SDK will automatically attempt the best option according to connectivity:
                                             // if the remote target is a JID, the option shall be Socks5 and InBand
                                             // if the remote target is XEP-0363 service, the option shall be XEP0363
};

/**
 * Struct which is used to pass information to and from the user, about
 * each file transfer item.
 */
struct XmppFileTransferItemDetail
{
   XmppFileTransferItemHandle handle;     /* (read-write) handle for the item */
   cpc::string localfilePath;             /* (read-write) Local Path to the file (not including name) */
   cpc::string localfileName;             /* (read-write) Local Name of the file */
   cpc::string remotefileName;            /* (read-only)  DEPRECATED Remote Name of the file. This was inherited from the SIP file transfer and not used in XMPP */
   cpc::string transferType;              /* (read-only)  string representing/flagging special file transfers i.e. screen capture*/
   uint64_t fileSizeBytes;                /* (read-write) Size of the file in bytes */
   bool isIncoming;                       /* (read-only)  True if the item is being transferred from the remote side, false otherwise */
   AcceptedState acceptedState;           /* (read-write) State of the acceptance (by the user) of the item */
   unsigned short percentComplete;        /* (read-only)  Percentage complete of the item transfer */
   int streamTypes;                       /* (read-write) Stream types of the item transfer. Can be bitwise OR of multiple types */

   XmppFileTransferItemDetail()
      : handle(0)
      , fileSizeBytes(0)
      , isIncoming(false)
      , acceptedState(ftitem_notprocessed)
      , percentComplete(0)
      , streamTypes(FileTransferStreamType_Auto)
   {}
};

typedef cpc::vector< XmppFileTransferItemDetail > XmppFileTransferItems;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppFileTransferManager
{
public:
   /**
    * Get a reference to the %XmppFileTransferManager interface.
    */
   static XmppFileTransferManager* getInterface( Phone* cpcPhone );

   /**
    * Set the handler for subscription events on the specified account.
    * Set the handler immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called
    * on the same thread as XmppAccountManager::process(..)
    */
   virtual int setHandler(
      XmppAccount::XmppAccountHandle account,
      XmppFileTransferHandler* handler
   ) = 0;

   /**
    * Allocates a new file transfer within the SDK, for outbound
    * transfer. After createFileTransfer() is called,
    * configureFileTransferItems should be invoked in order to set the
    * items to be transferred.
    *
    * After this has been done, addParticipant(..) and start(..)
    * @return XmppFileTransferHandle which is unique within the SDK
    */
   virtual XmppFileTransferHandle createFileTransfer(
      XmppAccount::XmppAccountHandle account
   ) = 0;

   /**
    * Creates a new (empty) file transfer item handle, used for outbound
    * transfers. The handle will be unique across the SDK. After items
    * are created, they should be "configured" using the
    * configureFileTransferItems method, before start() is called.
    */
   virtual XmppFileTransferItemHandle createFileTransferItem(
      XmppAccount::XmppAccountHandle account
   ) = 0;

   /**
    * Adds a participant to the file transfer session.  Call this
    * function after createFileTransfer(..) and before start(..).
    * Note: The targetAddress parameter could be:
    * 1. a full jid, e.g. <EMAIL>/resource.
    * 2. or bare jid, e.g. <EMAIL>, in which case the file transfer request will be
    * broadcast to all resources of the target.
    * 3. or XEP0363_service from ServiceAvailabilityEvent, XEP-0363 will be used for uploading a file to the server.
    *
    * NOTE: only the first participant will be used, as file transfer is
    * only to one hop (i.e. not 1:N). Additional participants will be
    * ignored.
    */
   virtual int addParticipant(
      XmppFileTransferHandle fileTransfer,
      const cpc::string& targetAddress
   ) = 0;

   /**
    * Initiates an outgoing (client) session to the remote participant.
    */
   virtual int start( XmppFileTransferHandle fileTransfer ) = 0;

   /**
    * Ends an (already connected) session.
    * Does nothing if the file transfer is not connected.
    * Notice that this call will terminate file transfer regardless file transfer status at remote side.
    * E.g. local status is competed while remote status may be still in progress
    */
   virtual int end( XmppFileTransferHandle fileTransfer ) = 0;

   /**
    * This method should be called before accepting/starting a file
    * transfer, it informs the SDK which file transfer items will be
    * used.
    *
    * In the case of an inbound transfer, the application should set
    * the "isAccepted" flag for each item in the list.  If the user
    * accepted the item, then the local name and/or path may also be
    * modified.  The handles should not be changed.
    */
   virtual int configureFileTransferItems(
      XmppFileTransferHandle fileTransfer,
      const XmppFileTransferItems& fileItems
   ) = 0;

   /**
    * Accepts the file transfer. Any file transfer
    * items which are marked as "isAccepted" using the
    * configureFileTransferItems method will be accepted
    */
   virtual int accept( XmppFileTransferHandle fileTransfer ) = 0;

   /**
    * The entire file transfer (and all of its items) will be rejected
    * with the reason sent to the remote party. This should be done
    * BEFORE accept() is called, otherwise an end() call will be required.
    *
    * @param fileTransfer The incoming session to reject.
    * @param reason The reason message sent to the originating party.
    */
   virtual int reject(
      XmppFileTransferHandle fileTransfer,
      const cpc::string& reason = ""
   ) = 0;

   /**
    * Cancels an ongoing file transfer (within the overall file transfer).
    * Notice that this call will terminate file transfer item regardless file transfer status at remote side.
    * E.g. local status is competed while remote status may be still in progress
    */
   virtual int cancelItem(
      XmppFileTransferHandle fileTransfer,
      XmppFileTransferItemHandle fileTransferItem
   ) = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppFileTransferManager.
    */
   virtual ~XmppFileTransferManager() {}
};

}
}

#endif // __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_H__
