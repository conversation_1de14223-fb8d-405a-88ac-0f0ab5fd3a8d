#pragma once

#if !defined(CPCAPI2_XMPP_FILE_TRANSFER_STATE_H)
#define CPCAPI2_XMPP_FILE_TRANSFER_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppFileTransferHandler.h"

namespace CPCAPI2
{
namespace XmppFileTransfer
{
/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class XmppFileTransferManager;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
struct XmppFileTransferState
{
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   FileTransferState fileTransferState;
   FileTransferType fileTransferType;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   FileTransferEndReason endReason;
   XmppFileTransferItems fileItems;

   XmppFileTransferState()
   {
      account           = 0;
      fileTransferState = FileTransferState_None;
      fileTransferType  = FileTransferType_Incoming;
      remoteAddress     = "";
      remoteDisplayName = "";
      endReason         = FileTransferEndReason_Unknown;
   }
};

/**
 * File transfer state information. Accessed via XmppFileTransferManager.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppFileTransferStateManager
{
public:
   /**
    * Get a reference to the %XmppFileTransferStateManager interface.
    * A reference should be obtained shortly after creating an XMPP account.
    */
   static XmppFileTransferStateManager* getInterface(XmppFileTransferManager* manager);

   /*
    * Gets the state for a particular XMPP file transfer.
    */
   virtual int getState(XmppFileTransferHandle handle, XmppFileTransferState& state) = 0;
};

}
}

#endif // CPCAPI2_XMPP_FILE_TRANSFER_STATE_H
