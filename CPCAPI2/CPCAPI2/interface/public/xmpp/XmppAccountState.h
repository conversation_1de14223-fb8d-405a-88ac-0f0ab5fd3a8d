#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_STATE_H)
#define CPCAPI2_XMPP_ACCOUNT_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppAccountHandler.h"
#include "xmpp/XmppAccountSettings.h"

namespace CPCAPI2
{
namespace XmppAccount
{
class XmppAccountManager;
struct XmppAccountState
{
   XmppAccountHandle account;
   XmppAccountStatusChangedEvent::Status accountStatus;
   XmppAccountSettings settings;

   XmppAccountState()
   {
      account = 0;
      accountStatus = XmppAccountStatusChangedEvent::Status_Disconnected;
   }
};
/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppAccountStateManager
{
public:
   /**
   * Get a reference to the XmppAccountStateManager interface.
   */
   static XmppAccountStateManager* getInterface(XmppAccountManager* cpcAcctMan);

   virtual int getStateAllAccounts(cpc::vector<XmppAccountState>& accountState) = 0;

protected:

   /*
    * The SDK will manage memory life of %XmppAccountStateManager.
    */
   virtual ~XmppAccountStateManager() {}

};

}
}
#endif // CPCAPI2_XMPP_ACCOUNT_STATE_H
