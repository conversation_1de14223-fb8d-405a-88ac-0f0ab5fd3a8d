#pragma once

#ifndef __CPCAPI2_XMPP_VCARD_TYPES_H__
#define __CPCAPI2_XMPP_VCARD_TYPES_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace XmppVCard
{
/**
 * Struct which is used to represent a vcard.
 */
struct XmppVCardDetail
{
   /**
    * Addressing type indicators.
    * @note @c AddrTypeDom and @c AddrTypeIntl are mutually exclusive. If both are present,
    * @c AddrTypeDom takes precendence.
    * @note Also note that not all adress types are applicable everywhere. For example,
    * @c AddrTypeIsdn does not make sense for a postal address. Check XEP-0054
    * for details.
    */
   enum AddressType
   {
      AddrTypeHome   =      1,    /**< Home address. */
      AddrTypeWork   =      2,    /**< Work address. */
      AddrTypePref   =      4,    /**< Preferred address. */
      AddrTypeX400   =      8,    /**< X.400 address. */
      AddrTypeInet   =     16,    /**< Internet address. */
      AddrTypeParcel =     32,    /**< Parcel address. */
      AddrTypePostal =     64,    /**< Postal address. */
      AddrTypeDom    =    128,    /**< Domestic(?) address. */
      AddrTypeIntl   =    256,    /**< International(?) address. */
      AddrTypeVoice  =    512,    /**< Voice number. */
      AddrTypeFax    =   1024,    /**< Fax number. */
      AddrTypePager  =   2048,    /**< Pager. */
      AddrTypeMsg    =   4096,    /**< MSG(?) */
      AddrTypeCell   =   8192,    /**< Cell phone number. */
      AddrTypeVideo  =  16384,    /**< Video chat(?). */
      AddrTypeBbs    =  32768,    /**< BBS. */
      AddrTypeModem  =  65536,    /**< Modem. */
      AddrTypeIsdn   = 131072,    /**< ISDN. */
      AddrTypePcs    = 262144     /**< PCS. */
   };

   /**
    * A person's full name.
    */
   struct Name
   {
      cpc::string family;         /**< Family name. */
      cpc::string given;          /**< Given name. */
      cpc::string middle;         /**< Middle name. */
      cpc::string prefix;         /**< Name prefix. */
      cpc::string suffix;         /**< Name suffix. */
   };

   /**
    * Classifies the VCard.
    */
   enum VCardClassification
   {
      ClassNone         = 0,      /**< Not classified. */
      ClassPublic       = 1,      /**< Public. */
      ClassPrivate      = 2,      /**< Private. */
      ClassConfidential = 4       /**< Confidential. */
   };

   /**
    * Describes an email field.
    */
   struct Email
   {
      cpc::string userid;         /**< Email address. */
      bool home;                  /**< Whether this is a personal address. */
      bool work;                  /**< Whether this is a work address. */
      bool internet;              /**< Whether this is an internet address(?). */
      bool pref;                  /**< Whether this is the preferred address. */
      bool x400;                  /**< Whether this is an X.400 address. */
   };

   /**
    * Describes a telephone number entry.
    */
   struct Telephone
   {
      cpc::string number;         /**< The phone number. */
      bool home;                  /**< Whether this is a personal number. */
      bool work;                  /**< Whether this is a work number. */
      bool voice;                 /**< Whether this is a voice number. */
      bool fax;                   /**< Whether this is a fax number. */
      bool pager;                 /**< Whether this is a pager. */
      bool msg;                   /**< MSG(?) */
      bool cell;                  /**< Whether this is a cell phone. */
      bool video;                 /**< Whether this is a video chat(?). */
      bool bbs;                   /**< Whether this is a BBS. */
      bool modem;                 /**< Whether this is a modem. */
      bool isdn;                  /**< Whether this is a ISDN line(?) */
      bool pcs;                   /**< PCS(?) */
      bool pref;                  /**< Whether this is the preferred number. */
   };

   /**
    * Describes an address entry.
    */
   struct Address
   {
      cpc::string pobox;          /**< Pobox. */
      cpc::string extadd;         /**< Extended address. */
      cpc::string street;         /**< Street. */
      cpc::string locality;       /**< Locality. */
      cpc::string region;         /**< Region. */
      cpc::string pcode;          /**< Postal code. */
      cpc::string ctry;           /**< Country. */
      bool home;                  /**< Whether this is a personal address. */
      bool work;                  /**< Whether this is a work address. */
      bool postal;                /**< Whether this is a postal address(?). */
      bool parcel;                /**< Whether this is a arcel address(?). */
      bool pref;                  /**< Whether this is the preferred address. */
      bool dom;                   /**< Whether this is a domestic(?) address. */
      bool intl;                  /**< Whether this is an international(?) address. */
   };

   /**
    * Describes an address label.
    */
   struct Label
   {
      cpc::vector<cpc::string> lines;           /**< A list of lines. */
      bool home;                  /**< Whether this is a personal address. */
      bool work;                  /**< Whether this is a work address. */
      bool postal;                /**< Whether this is a postal address(?). */
      bool parcel;                /**< Whether this is a arcel address(?). */
      bool pref;                  /**< Whether this is the preferred address. */
      bool dom;                   /**< Whether this is a domestic(?) address. */
      bool intl;                  /**< Whether this is an international(?) address. */
   };

   /**
    * Describes geo information.
    */
   struct Geo
   {
      cpc::string latitude;       /**< Longitude. */
      cpc::string longitude;      /**< Latitude. */
   };

   /**
    * Describes organization information.
    */
   struct Organization
   {
      cpc::string name;                 /**< The organizations name. */
      cpc::vector<cpc::string> units;   /**< A list of units in the organization
                                          * (the VCard's owner belongs to?). */
   };

   /**
    * Describes photo/logo information.
    */
   struct Photo
   {
      cpc::string extval;         /**< The photo is not stored inside the VCard. This is a hint (URL?)
                                    * where to look for it. */
      cpc::vector<char> binval;   /**< This is the photo (binary). */
      cpc::string type;           /**< This is a hint at the mime-type. May be forged! */
      cpc::string hash;           /**< Hex hash value of binval if exists */

      Photo()
      {
      }

      Photo(const Photo& other)
      {
         *this = other;
      }

      Photo& operator =(const Photo& other)
      {
         extval = other.extval;
         type = other.type;

         if (hash != other.hash)
         {
            hash = other.hash;
            binval = other.binval;
         }

         return *this;
      }
   };

   /**
    * A list of email fields.
    */
   typedef cpc::vector<Email> EmailList;

   /**
    * A list of telephone entries.
    */
   typedef cpc::vector<Telephone> TelephoneList;

   /**
    * A list of address entries.
    */
   typedef cpc::vector<Address> AddressList;

   /**
    * A list of address labels.
    */
   typedef cpc::vector<Label> LabelList;

   EmailList emailList;
   TelephoneList telephoneList;
   AddressList addressList;
   LabelList labelList;

   Name name;
   Geo geo; // geo location
   Organization organization;
   Photo photo;
   Photo logo;

   VCardClassification classification;

   cpc::string formattedname;
   cpc::string nickname;
   cpc::string url;
   cpc::string birthday;
   cpc::string jid;
   cpc::string title;
   cpc::string role;
   cpc::string note;
   cpc::string desc; // description
   cpc::string mailer;
   cpc::string timezone;
   cpc::string product; // the production identifier
   cpc::string revision; // the date of the last revision
   cpc::string sortstring;
   cpc::string phonetic; // pronunciation with white space deliminator
   cpc::string cpcollab; // Counterpath collaboration
   cpc::string cpsoftphone; // Counterpath extension: softphone URI
   bool cpsoftphone_pref; // Counterpath extension: softphone pref
   cpc::string uid; // the unique identifier
};

cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::AddressType& type);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Name& name);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::VCardClassification& classification);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Email& email);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Telephone& telephone);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Address& address);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Label& label);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Geo& geo);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Organization& organization);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::Photo& photo);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::EmailList& list);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::TelephoneList& list);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::AddressList& list);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail::LabelList& list);
cpc::string get_debug_string(const CPCAPI2::XmppVCard::XmppVCardDetail& detail);

}
}

#endif // __CPCAPI2_XMPP_VCARD_TYPES_H__

