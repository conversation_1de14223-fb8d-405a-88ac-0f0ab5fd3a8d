#pragma once

#ifndef __CPCAPI2_XMPP_VCARD_MANAGER_H__
#define __CPCAPI2_XMPP_VCARD_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "xmpp/XmppAccount.h"
#include "XmppVCardTypes.h"

namespace CPCAPI2
{
// forward declarations
class Phone;

namespace XmppVCard
{
// forward declarations
class XmppVCardHandler;

// type defs
typedef unsigned int XmppVCardHandle;

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppVCardManager
{
public:
   /**
    * Get a reference to the %XmppVCardManager interface.
    */
   static XmppVCardManager* getInterface( Phone* cpcPhone );

   /**
    * Set the handler for subscription events on the specified account.
    * Set the handler immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the same thread as XmppAccountManager::process(..)
    */
   virtual int setHandler(
      XmppAccount::XmppAccountHandle account,
      XmppVCardHandler* handler
   ) = 0;

   /**
    * Allocates a new vcard within the SDK.
    *
    * @return XmppVCardHandle which is unique within the SDK
    */
   virtual XmppVCardHandle create(XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Retrieve the vcard of a given JID
    */
   virtual int fetchVCard(XmppVCardHandle handle, const cpc::string& jid) = 0;

   /**
    * Update own vcard
    */
   virtual int storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard) = 0;

   /**
    * Cancel all pending operations (fetch or store)
    */
   virtual int cancelVCardOperations(XmppVCardHandle handle) = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppVCardManager.
    */
   virtual ~XmppVCardManager() {}
};

}
}

#endif // __CPCAPI2_XMPP_VCARD_MANAGER_H__
