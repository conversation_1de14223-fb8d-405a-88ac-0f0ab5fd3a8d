#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_H)
#define CPCAPI2_XMPP_ROSTER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "XmppRosterTypes.h"

namespace CPCAPI2
{
class Phone;

namespace XmppAccount
{
// sentinel/initialization value for XmppAccountHandle is -1 cast back to unsigned int
typedef unsigned int XmppAccountHandle;
}

namespace XmppRoster
{
typedef unsigned int XmppRosterUpdateRequestHandle;
typedef unsigned int XmppRosterHandle;
typedef unsigned int XmppRosterIterator;
class XmppRosterHandler;

/**
 */
class CPCAPI2_SHAREDLIBRARY_API XmppRosterManager
{
public:
   /**
    * Get a reference to the XmppAccountManager interface.
    */
   static XmppRosterManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for events on the specified account. Set the handler
    * immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the same thread as XmppAccountManager::process(..)
    */
   virtual int setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler) = 0;

   /**
    * Create a roster for the specified account and start querying the roster as soon as possible
    */
   virtual XmppRosterHandle createRoster(CPCAPI2::XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Accept a subscription received through onRosterSubscriptionRequest
    */
   virtual int acceptSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) = 0;

   /**
    * Reject a subscription received through onRosterSubscriptionRequest
    */
   virtual int rejectSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) = 0;

   /**
    * Remove contact's subscription for user's presence
    */
   virtual int cancelAcceptedSubscription(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message=cpc::string()) = 0;

   /**
    * Add an entry to roster
    */
   virtual int addRosterItem(XmppRosterHandle roster,
                  const cpc::string& address,
                  const cpc::string& displayName = cpc::string(),
                  const cpc::vector<cpc::string>& groups = StringArray()) = 0;

   /**
    * Update an entry in roster
    */
   virtual int updateRosterItem(XmppRosterHandle roster,
                  const cpc::string& address,
                  const cpc::string& displayName,
                  const cpc::vector<cpc::string>& groups = StringArray()) = 0;

   /**
    * Remote an entry from roster
    */
   virtual int removeRosterItem(XmppRosterHandle roster, const cpc::string& address) = 0;

   /**
    * Send request for contact's presence. Compliant servers will auto add contact to roster
    * if contact is not already in roster
    */
   virtual int subscribePresence(XmppRosterHandle roster,
                  const cpc::string& address,
                  const cpc::string& displayName,
                  const cpc::vector<cpc::string>& groups = StringArray(),
                  const cpc::string& message = cpc::string()) = 0;

   /**
    * Removes presence subscription
    */
   virtual int unsubscribePresence(XmppRosterHandle roster, const cpc::string& address) = 0;

   /**
    * Query for the entire roster state
    * This is a synchronous call
    */
   virtual int getRosterState(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems) = 0;

   /**
    * Disabled batch operations for now due to complex network responses
    * (e.g. a subscribe can trigger an auto-add operation which would trigger seperate onAdded notification).
    */

protected:
   /*
    * The SDK will manage memory life of %XmppRosterManager.
    */
   virtual ~XmppRosterManager() {}
};

} // namespace XmppRoster
} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_H
