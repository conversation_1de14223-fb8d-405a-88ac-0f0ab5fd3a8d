#pragma once

#if !defined(__CPCAPI2_XMPP_IM_COMMAND_MANAGER_H__)
#define __CPCAPI2_XMPP_IM_COMMAND_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppIMCommandHandler.h"
#include "XmppAccount.h"
#include "XmppChatTypes.h"
#include "XmppMultiUserChatTypes.h"

namespace CPCAPI2
{

namespace XmppIMCommand
{

/**
 * Interface for XMPP IM command related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppIMCommandManager
{
public:
   /**
    * Get a reference to the %XmppIMCommandManager interface.
    *
    * @param cpcPhone the phone object to be associated with this manager.
    *
    * @return the %XmppIMCommandManager interface.
    */
   static XmppIMCommandManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for IM Command related events on the specified account.
    * Set the handler immediately after getting a reference to this manager.
    *
    * To un-register the handler, pass NULL for handler. Must be called on
    * the same thread as XmppAccountManager::process(..)
    *
    * @param handle the account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(XmppAccount::XmppAccountHandle handle, XmppChatIMCommandHandler* handler) = 0;

   /**
    * Set the handler for IM Command related events on the specified account.
    * Set the handler immediately after getting a reference to this manager.
    *
    * To un-register the handler, pass NULL for handler. Must be called on
    * the same thread as XmppAccountManager::process(..)
    *
    * @param handle the account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(XmppAccount::XmppAccountHandle handle, XmppMultiUserChatIMCommandHandler* handler) = 0;

   /**
    * Send IM command through chat session.
    * @param handle the chat session associated with the command to send.
    * @param type the command type.
    * @param payload the command content.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppChat::XmppChatMessageHandle sendChatIMCommand(XmppChat::XmppChatHandle handle, int type, const cpc::string& payload, const cpc::string& htmlPayload = "") = 0;

   /**
    * Send IM command through multi user chat session.
    * @param handle the multi user chat session associated with the command to send.
    * @param type the command type.
    * @param payload the command content.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppMultiUserChat::XmppMultiUserChatMessageHandle sendMultiUserChatIMCommand(XmppMultiUserChat::XmppMultiUserChatHandle handle, int type, const cpc::string& payload) = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppChatManager.
    */
   virtual ~XmppIMCommandManager() {}
};

}
}

#endif // __CPCAPI2_XMPP_IM_COMMAND_MANAGER_H__
