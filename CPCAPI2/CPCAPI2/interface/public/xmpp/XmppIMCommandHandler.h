#pragma once

#if !defined(__CPCAPI2_XMPP_IM_COMMAND_HANDLER_H__)
#define __CPCAPI2_XMPP_IM_COMMAND_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppChatTypes.h"
#include "XmppMultiUserChatTypes.h"

namespace CPCAPI2
{

namespace XmppIMCommand
{

struct ChatIMCommandReceivedEvent
{
   cpc::string remote;              // The bare jid of the sender
   int type;                        // The command type
   cpc::string payload;             // The command content
   uint64_t timestamp;              // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;            // The millisecond part of the timestamp
   bool isDelayedDelivery;          // Did message include a delayed time stamp
};

struct MultiUserChatIMCommandReceivedEvent
{
   cpc::string remote;              // The bare jid of the sender
   cpc::string nickname;            // The nickname of the sender
   int type;                        // The command type
   cpc::string payload;             // The command content
   uint64_t timestamp;              // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;            // The millisecond part of the timestamp
   bool isDelayedDelivery;          // Did message include a delayed time stamp
};

struct ChatIMCommandSentEvent
{
   XmppChat::XmppChatMessageHandle message;
};

struct MultiUserChatIMCommandSentEvent
{
   XmppMultiUserChat::XmppMultiUserChatMessageHandle message;
};

#if 0 // bliu: TODO
/**
 * Used to report general SDK error conditions, such as invalid handles, or cases
 * where the transfer is not in a valid state for the requested operation.
 */
struct ErrorEvent
{
   cpc::string errorText;
};
#endif

/**
 * Handles all SDK callbacks related to the XMPP IM command feature.
 */
class XmppChatIMCommandHandler
{
public:

   /**
    * Callback invoked by the SDK when an IM command was received.
    *
    * @param handle the chat session that the command was received.
    * @param evt information about the received command.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIMCommandReceived(XmppChat::XmppChatHandle handle, const ChatIMCommandReceivedEvent& evt) = 0;

   /**
    * Callback invoked by the SDK when an IM command was sent.
    *
    * @param message the chat session that the command was sent.
    * @param evt information about the sent command.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIMCommandSent(XmppChat::XmppChatHandle handle, const ChatIMCommandSentEvent& evt) = 0;

#if 0 // bliu: merge to XmppChat::onError?
   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param chat the chat session the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(XmppChatHandle chat, const ErrorEvent& event) = 0;
#endif

};

/**
 * Handles all SDK callbacks related to the XMPP IM command feature.
 */
class XmppMultiUserChatIMCommandHandler
{
public:

   /**
    * Callback invoked by the SDK when an IM command was received.
    *
    * @param handle the multi user chat session that the command was received.
    * @param evt information about the received command.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIMCommandReceived(XmppMultiUserChat::XmppMultiUserChatHandle handle, const MultiUserChatIMCommandReceivedEvent& evt) = 0;

   /**
    * Callback invoked by the SDK when an IM command was sent.
    *
    * @param message the multi user chat session that the command was sent.
    * @param evt information about the sent command.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIMCommandSent(XmppMultiUserChat::XmppMultiUserChatHandle handle, const MultiUserChatIMCommandSentEvent& evt) = 0;

#if 0 // bliu: merge to XmppMultiUserChat::onError?
   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param handle the multi user chat session the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(XmppMultiUserChat::XmppMultiUserChatHandle handle, const ErrorEvent& event) = 0;
#endif

};

}
}

#endif // __CPCAPI2_XMPP_IM_COMMAND_HANDLER_H__
