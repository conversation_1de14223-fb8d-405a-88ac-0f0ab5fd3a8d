#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_TYPES_H__)
#define __CPCAPI2_XMPP_CHAT_TYPES_H__

namespace CPCAPI2
{
namespace XmppChat
{
// Type definitions
typedef unsigned int XmppChatHandle;
typedef unsigned int XmppChatMessageHandle;

/**
 * Possible message delivery statuses.
 */
enum MessageDeliveryStatus
{
   MessageDeliveryStatus_Delivered = 1,
   MessageDeliveryStatus_Error = 2
};

/**
 * Possible message display statuses.
 */
enum MessageDisplayStatus
{
   MessageDisplayStatus_Displayed = 1
};

/**
 * Possible states for the 'IsComposing' indicator.
 */
enum IsComposingMessageState
{
   IsComposingMessageState_Unknown = 0,
   IsComposingMessageState_Idle    = 1,
   IsComposingMessageState_Active  = 2
};

// DEPRECATED: never used
enum MessageType
{
   MessageType_MessageDeliveredNotification,
   MessageType_MessageDisplayedNotification,
   MessageType_IsComposingNotification,
   MessageType_Message
};

}
}

#endif // __CPCAPI2_XMPP_CHAT_TYPES_H__