#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_HANDLER_H__)
#define __CPCAPI2_XMPP_CHAT_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppChatTypes.h"
#include "XmppAccount.h"

namespace CPCAPI2
{

namespace XmppChat
{
/**
 * Type of chat session.
 */
enum ChatType
{
   ChatType_Incoming = 1000,
   ChatType_Outgoing = 1010,
};

/**
 * Possible reasons for a terminated chat session.
 */
enum ChatEndReason
{
   ChatEndReason_Unknown                = 1100,
   ChatEndReason_UserTerminatedLocally  = 1110,
   ChatEndReason_UserTerminatedRemotely = 1120,
   ChatEndReason_Rejected               = 1130, // DEPRECATED: this value is never used in the SDK
};

/**
 * Possible reasons for peer discovery failure.
 */
enum PeerDiscoErrorReason
{
   PeerDiscoErrorReason_Timeout    = 1200,
   PeerDiscoErrorReason_InvalidJid = 1210,
};

/**
 * Event fired by the SDK to signal a new chat session.
 */
struct NewChatEvent
{
   XmppAccount::XmppAccountHandle account;
   ChatType chatType; // Type of chat session
   cpc::string remote;
};

/**
 * Event fired by the SDK when the other participant of the
 * chat session is composing a new message.
 */
struct IsComposingMessageEvent
{
   IsComposingMessageState state; // The state of the 'isComposing' indicator
};

/*
 * Event fired by the SDK to signal a new message within a chat session.
 */
struct NewMessageEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppChatMessageHandle message;   // The message
   cpc::string messageId;           // The message ID
   cpc::string originId;            // The message origin ID (XEP-0359) if provided
   cpc::string threadId;            // The thread ID
   cpc::string from;                // The originator of the message
   cpc::string to;                  // The destination of the message
   cpc::string messageContent;      // The content of the message in plain text, represented as a byte array
   cpc::string htmlText;            // The content of the message in html text, represented as a byte array. The app layer needs to handle the unescaping.
   cpc::string subject;             // The subject of the message
   uint64_t timestamp;              // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;            // The millisecond part of the timestamp
   bool isDelayedDelivery;          // Did message include a delayed time stamp
   bool isOutbound;                 // Is an outbound message (by default, false)
   cpc::string replaces;            // The message ID this message replaces in case of message correction

   NewMessageEvent() : account( -1 ), timestamp( 0 ), millisecond( 0 ), isDelayedDelivery( false ), isOutbound( false ), replaces( "" ) {}
};

/*
 * Event fired by the SDK to signal a reaction within a chat session.
 */
struct NewReactionEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppChatMessageHandle message;   // The message
   cpc::string messageId;           // The message ID
   cpc::string threadId;            // The thread ID
   cpc::string from;                // The originator of the message
   cpc::string reactionTarget;      // The message ID this reaction applies to
   cpc::vector<cpc::string> reactions; // The content, each reaction represented as a byte array
   uint64_t timestamp;              // The timestamp of the reaction, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;            // The millisecond part of the timestamp
   bool isDelayedDelivery;          // Did message include a delayed time stamp
   bool isOutbound;                 // Is an outbound message (by default, false)

   NewReactionEvent() : account( -1 ), timestamp( 0 ), millisecond( 0 ), isDelayedDelivery( false ), isOutbound( false ) {}
};

/*
 * Event fired by the SDK to signal a message retraction within a chat session.
 */
struct NewMessageRetractionEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppChatMessageHandle message;   // The message
   cpc::string messageId;           // The message ID
   cpc::string threadId;            // The thread ID
   cpc::string from;                // The originator of the message
   cpc::string retractTarget;       // The message ID this retraction applies to
   uint64_t timestamp;              // The timestamp of the retraction, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;            // The millisecond part of the timestamp
   bool isDelayedDelivery;          // Did message include a delayed time stamp
   bool isOutbound;                 // Is an outbound message (by default, false)

   NewMessageRetractionEvent() : account( -1 ), timestamp( 0 ), millisecond( 0 ), isDelayedDelivery( false ), isOutbound( false ) {}
};

/**
 * Event fired by the SDK when a message was successfully
 * delivered to the other participant's device(s).
 */
struct MessageDeliveredEvent
{
   DEPRECATED XmppChatMessageHandle message;                // The delivered message. This could be 0, and messageId and threadId shall be used instead
   DEPRECATED MessageDeliveryStatus messageDeliveryStatus;  // The status of the message delivery
   cpc::string messageId;                        // The message ID
   cpc::string threadId;                         // The thread ID
   cpc::string from;                             // The originator of the message
   uint64_t timestamp;                           // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;                         // The millisecond part of the timestamp
   bool isDelayedDelivery;                       // Did message include a delayed time stamp
};

/**
 * Event fired by the SDK when message delivery
 * failed to the other participant's device(s).
 */
struct MessageDeliveryErrorEvent
{
   XmppChatMessageHandle message;                // The failed message. May be empty if the original chat session has already ended
   DEPRECATED MessageDeliveryStatus messageDeliveryStatus;  // The status of the message delivery
   cpc::string from;                             // The originator of the message
};

/**
 * Event fired by the SDK when a message was successfully
 * displayed on the other participant's device(s).
 */
struct MessageDisplayedEvent
{
   XmppChatMessageHandle message;                // The displayed message
   MessageDisplayStatus messageDisplayStatus;    // The status of the message display
   cpc::string from;                             // The originator of the message
};

/**
 * Event fired by the SDK when a message was read by the other participant's device(s).
 */
struct MessageReadEvent
{
   cpc::string messageId;                        // The message ID
   cpc::string threadId;                         // The thread ID
   cpc::string from;                             // The originator of the message
   uint64_t timestamp;                           // The timestamp of the message, represented as number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond;                         // The millisecond part of the timestamp
   bool isDelayedDelivery;                       // Did message include a delayed time stamp
};

/**
 * Event fired by the SDK when a new message was successfully
 * sent.
 */
struct SendMessageSuccessEvent
{
   XmppChatMessageHandle message; // The new message
   cpc::string messageId;         // The message ID
   cpc::string threadId;          // The thread ID
   cpc::string replaces;          // The message ID this message replaces in case of message correction
};

/**
 * Event fired by the SDK when a new message failed to send.
 */
struct SendMessageFailureEvent
{
   XmppChatMessageHandle message; // The new message
};

#if 1 // bliu: not used
/**
 * Event fired by the SDK when a message delivered notification
 * was successfully sent.
 */
struct NotifyMessageDeliveredSuccessEvent
{
   XmppChatMessageHandle notification; // The notification
   XmppChatMessageHandle message;      // The delivered message
};

/**
 * Event fired by the SDK when a message displayed notification
 * was successfully sent.
 */
struct NotifyMessageDisplayedSuccessEvent
{
   XmppChatMessageHandle notification; // The notification
   XmppChatMessageHandle message;      // The displayed message
};
#endif

/**
 * Event fired by the SDK to signal the termination of a chat session.
 */
struct ChatEndedEvent
{
   XmppAccount::XmppAccountHandle account;
   XmppChatHandle chat;      // The chat session that ended
   ChatEndReason endReason; // Reason for the termination of a chat session
};

/**
 * Event fired by the SDK when peer-to-peer discovery is finished.
 */
struct ChatDiscoEvent
{
   cpc::string remoteJID;           // Remote JID
   bool replaceMessageSupported;    // Replacing message is supported
   bool messageRetractionSupported; // Message retraction is supported
   bool messageReactionsSupported;  // Message reactions are supported

   ChatDiscoEvent() : replaceMessageSupported(false),  messageRetractionSupported(false), messageReactionsSupported(false) {}
};

/**
 * Event fired by the SDK to signal error in peer discovery.
 */
struct ChatDiscoErrorEvent
{
   cpc::string remoteJID;       // Remote JID
   PeerDiscoErrorReason reason; // Reason for peer discovery error
};

/**
 * Used to report general SDK error conditions, such as invalid handles, or cases
 * where the transfer is not in a valid state for the requested operation.
 */
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Used to indicate the result of a chat handle validation request.
 */
struct ValidateChatHandleEvent
{
   XmppAccount::XmppAccountHandle account;
   bool chatHandleValid; // Whether the chat handle is still valid
};

/**
 * Handles all SDK callbacks related to the XMPP chat feature.
 */
class XmppChatHandler
{
public:
   /**
    * Callback invoked by the SDK when a new chat session is established.
    *
    * @param chat the chat session that was established.
    * @param args information about the new chat session.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewChat(XmppChatHandle chat, const NewChatEvent& args) = 0;
   /**
    * Callback invoked by the SDK when the other participant is composing a new message.
    *
    * @param chat the chat session associated with the message being composed.
    * @param args information about the message being composed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIsComposingMessage(XmppChatHandle chat, const IsComposingMessageEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a new message is received within a chat session.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the new message.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewMessage(XmppChatHandle chat, const NewMessageEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a new reaction is received within a chat session.
    *
    * @param chat the chat session with which the reaction is associated.
    * @param args information about the reaction.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewReaction(XmppChatHandle chat, const NewReactionEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a new message retraction is received within a chat session.
    *
    * @param chat the chat session with which the retraction is associated.
    * @param args information about the retraction.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a new OUTBOUND message is sent by the application.
    * Note that this callback will be invoked regardless of success or failure of the
    * sending operation.
    * 
    * @param chat handle to the specific chat instance involved in this event
    * @param args an event containing the information about the message content
    * @return kSuccess if the operation was successful, kError otherwise.
    */
    virtual int onNewOutboundMessage(XmppChatHandle chat, const NewMessageEvent& args ) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message was successful.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the success.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the sending of a message failed.
    *
    * @param chat the chat session with which the message is associated.
    * @param args information about the failure.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was successfully delivered
    * to the other participant's device(s).
    *
    * @param chat the chat session that the new message was sent against.
    * @param args information about the message delivered.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageDelivered(XmppChatHandle chat, const MessageDeliveredEvent& args) = 0;

   /**
    * Callback invoked by the SDK when the message was read
    * to the other participant's device(s).
    *
    * @param chat the chat session that the new message was read.
    * @param args information about the message read.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onMessageRead(XmppChatHandle chat, const MessageReadEvent& args) { return kSuccess; }

   /**
   * Callback invoked by the SDK when the message delivery failed
   * to the other participant's device(s).
   *
   * @param chat the chat session that the new message was sent against.
   * @param args information about the message delivered.
   *
   * return kSuccess if the operation was successful, kError otherwise.
   */
   virtual int onMessageDeliveryError(XmppChatHandle chat, const MessageDeliveryErrorEvent& args) = 0;

   /**
    * DEPRECATED: This is not used at all
    * Callback invoked by the SDK when the message was successfully displayed
    * on the other pariticpant's device(s).
    *
    * @param chat the chat session that the new message was sent against.
    * @param args information about the message displayed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int onMessageDisplayed(XmppChatHandle chat, const MessageDisplayedEvent& args) = 0;

   /**
    * Callback invoked by the SDK when a chat session was terminated.
    *
    * @param chat the chat session that was terminated.
    * @param args information about the terminated chat session.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onChatEnded(XmppChatHandle chat, const ChatEndedEvent& args) = 0;

   /**
    * Callback invoked by the SDK when peer-to-peer discovery is completed.
    *
    * @param chat the chat session for which discovery is completed.
    * @param args details of the discovery.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args) = 0;

   /**
    * Callback invoked by the SDK when peer-to-peer discovery failed.
    *
    * @param chat the chat session for which discovery failed.
    * @param args details of the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args) = 0;

   /**
    * Invoked by the SDK in response to XmppChatManager::validateChatHandle(..)
    */
   virtual int onValidateChatHandleResult(XmppChatHandle chat, const ValidateChatHandleEvent& args) = 0;

   // /**
   //  * Callback invoked by the SDK when an existing message was modified.
   //  *
   //  * @param chat the chat session.
   //  * @param args information about the modified chat message.
   //  *
   //  * return kSuccess if the operation was successful, kError otherwise.
   //  */
   // virtual int onMessageEdited(XmppChatHandle chat, const MessageCorrectedEvent& args) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param chat the chat session the error occured against.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(XmppChatHandle chat, const ErrorEvent& args) = 0;
};

}
}

#endif // __CPCAPI2_XMPP_CHAT_HANDLER_H__
