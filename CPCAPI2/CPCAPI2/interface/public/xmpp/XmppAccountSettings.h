#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_SETTINGS_H)
#define CPCAPI2_XMPP_ACCOUNT_SETTINGS_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <phone/SslCipherOptions.h>

namespace CPCAPI2
{
namespace XmppAccount
{

enum IpVersion
{
   IpVersion_V4,
   IpVersion_V6,
   IpVersion_Auto_PreferV4,
   IpVersion_Auto_PreferV6
};

enum SSLVersion
{
   TLS_DEFAULT        = -1,   /** Use the value set in SslCipherOptions */
   SSL_NONE           = 0,
   SSL_V2             = 1,    /** Not supported as it is no longer deemed secure. */
   SSL_V3             = 2,    /** Not supported as it is no longer deemed secure. */
   TLS_V1_0           = 3,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_1           = 4,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_2           = 5,
   TLS_V1_3           = 6,
   SSL_HIGHEST        = 1000, /** <PERSON><PERSON> will negotiated highest supported version between client and server TLS 1.0 and above */
   TLS_NON_DEPRECATED = 1001  /** SDK will negotiated highest supported version between client and server TLS 1.2 and above */
};

/**
 * A struct. Contains data used by the account; passed in XmppAccount::create();
 * the data includes information that allows the application to connect to the network.
 */
struct XmppAccountSettings
{
   /**
    * The user portion of the XMPP identity (JID) for this account,
    * for example, the "kperera" portion of "<EMAIL>"
    */
   cpc::string                           username;

   /**
    * The host portion of the XMPP identity (JID) for this account,
    * for example, the "zippy-phone.com" portion of "<EMAIL>"
    */
   cpc::string                           domain;

   /**
    * The password for this account.
    */
   cpc::string                           password;

   /**
    * The fully qualified host name or IP address of the outbound proxy.
    */
   cpc::string                           proxy;

   /**
    * The listening port of the server or proxy.
    * Specifying a non-zero port would turn off DNS SRV lookup.
    */
   unsigned int                          port;

   /**
    * The resource portion of the full XMPP identity for this account,
    * leave empty to have server generate one for the account.
    */
   cpc::string                           resource;

   /**
    * The priority for this account's XMPP resource.
    */
   int                                   priority;

   /**
    * Part of entity capability discovery
    */
   cpc::string                           softwareName;
   cpc::string                           softwareVersion;
   cpc::string                           softwareOS;
   cpc::string                           identityCategory;
   cpc::string                           identityType;

   /**
    * Time (in seconds) to wait for TCP to connect to a host before giving up.
    */
   unsigned int                          connectTimeOut;

   /**
    * A random interval (in seconds) to wait for TCP to connect to a host.
    */
   unsigned int                          connectRandomInterval;

   /**
    * Periodic connection keep alive paramters.
    * Specifying zero keepAliveTime (in seconds) will disable keep alive.
    * Specifying true for usePingKeepAlive will use XEP instead of
    * sending white space keep alive.
    */
   unsigned int                          keepAliveTime;
   bool                                  usePingKeepAlive;

   /**
    * List of external file transfer proxies.
    * Format: [<username>[:<password>]@]<server>[:<port>];jid=<jid>
    * E.g.: username:<EMAIL>:7777;jid=proxy.jabber.org
    */
   cpc::vector<cpc::string>              fileTransfileProxies;

   /**
    * Specifying whether to host local Socks5 proxy or not.
    */
   bool                                  enableLocalSocks5Proxy;

   /**
    * Specifying whether to discovery remote stream host or not.
    */
   bool                                  enableRemoteStreamHostDiscovery;

   /**
    * Specifying XEP-0363 HTTP file upload rate (in bytes/sec, 0 means unlimited).
    */
   unsigned int                          httpFileUploadTransferRate;

   /**
    * SSL protocol version to use.
    */
   SSLVersion                            sslVersion;

   /**
   * OpenSSL cipher suite string. See SslCipherOptions.h for recommended cipher suites.
   */
   CipherSuite                           cipherSuite;

   /**
    * Ignore certificate verification.
    */
   bool                                  ignoreCertVerification;

   /**
    * Additional DNS names checked during certificate verification.
    * Useful in situations where server certificates do not conform to
    * RFC-specified constraints.
    * The DNS names specified in this list are checked in
    * addition to the domain.
    * DNS names specified here when ignoreCertVerification is set to 'true'
    * are ignored.
    */
   cpc::vector<cpc::string>              additionalCertPeerNames;

   /**
    * Base 64 encoded public keys, checked during certificate verification.
    * The public keys specified in this list are checked against the
    * value in the server certificate.  If any of the keys match, the
    * certificate will be considered valid, regardless of other certificate
    * validation errors (even if ignoreCertVerification is set to 'false').
    * If the server certificate does not match any of the keys specified
    * in this list, the certificate may still be considered valid subject to default
    * validation procedures.
    */
   cpc::vector<cpc::string>              acceptedCertPublicKeys;

   /**
    * Base 64 encoded public keys, checked during certificate verification.
    * If specified, certificate verification is performed as normal,
    * with an additional check to ensure that the public key in the server
    * certificate matches at least one of the values specified here.
    * Public keys specified here when ignoreCertVerification is set to 'true'
    * are still checked and may still cause certificate validation to fail.
    */
   cpc::vector<cpc::string>              requiredCertPublicKeys;

   /**
    * Output all sent and received XMPP stanzas to debug log.
    */
   bool                                  logXmppStanzas;

   /**
    * XMPP transport IP version (IPv4, IPv6. or auto).
    */
   IpVersion                             ipVersion;

   /**
    * Optional list of name servers to be used by the XMPP stack.  Used in place of any default/system nameservers
    * returned by the OS.  Specified in the format "*******" (for IPv4) or "2001:4860:4860::8888" (for IPv6).
    */
   cpc::vector<cpc::string>              nameServers;

   /**
    * Optional list of name servers to be used by the XMPP stack.  Used in addition to any default/system nameservers
    * returned by the OS.  The server specified here will be used as fall-backs in the event the OS/network does not
    * provide any DNS servers or if those servers are not reachable.
    * Specified in the format "*******" (for IPv4) or "2001:4860:4860::8888" (for IPv6).
    */
   cpc::vector<cpc::string>              additionalNameServers;

   /**
    * Stream Management settings (XEP-0198).
    */
   bool                                  enableStreamManagement;
   bool                                  enableStreamResumption; // relies on enableStreamManagement to be true
   int                                   maxStreamResumptionTimeout; // to negotiate a max duration (seconds) for a stream session to live after interruption

   /**
    * DEPRECATED: the SDK handles stream resumption internally if possible. No more app layer intervene is required.
    * Used for stream resumption. Ignored if enableStreamResumption is false.
    */
   cpc::string                           streamManagementId;
   int                                   streamManagementSequence;

   /**
    * If this parameter is true, the SDK will automatically publish initial presence as Available after the account is connected but before the account status is changed to Status_Connected.
    * If this paremeter is false, the SDK will not publish any initial presence and it is up to the app layer to publish a desired initial presence upon the account status is changed to Status_Connected.
    * Default as true.
    * Refer to https://xmpp.org/rfcs/rfc3921.html#presence for more details.
    */
   bool                                  publishInitialPresenceAsAvailable;

   /**
    * A non-empty XmppAccountSettings.resource renders this parameter as false.
    * If this parameter is true, the SDK will request for server resource bind for reconnection instead.
    * If this parameter is false, the SDK will reuse the previous successful bound resource for reconnection.
    * Default as false. It's better to deal with resource conflict instead of inconsistent presence issue.
    */
   bool                                  fallbackOnResourceConflict;

   /**
    * If this parameter is true, the SDK will automatically negotiate compression as stream feature.
    * If this paremeter is false, the SDK will not negotiate compression as stream feature..
    * Default as true.
    */
   bool                                  enableCompression;

   /**
    * If this parameter is true, the SDK will register for XMPP presence events. Debug use only. Default as true.
    */
   bool                                  enableXmppPresence;

   /**
    * If this parameter is true, the SDK will register for XMPP stanza events. Debug use only. Default as true.
    */
   bool                                  enableXmppStanza;
   
   /**
    * If session management is enabled and this value is > 0, check on this interval and send an ack if the stanza 
    * count has changed. Default is 60.
    */
   unsigned int                          unrequestedAckSendIntervalSec;

   /**
    * If this parameter is true, the SDK will support <see-other-host/> stream error.
    * However the format of <see-other-host/> is not well standardized, therefore th support is experimental only.
    */
   bool                                  enableSeeOtherHost;

   /**
    * If this parameter is true, the SDK will log TLS encryption keys.
    */
   bool                                  logTlsEncryptionKey;

   /**
    * Support for legacy XEP-0022: Message Events:
    * use legacy XEP-0022 for chat delivery and read receipts
    * if enabled enableChatDeliveryReceipts and enableChatReadReceipts have no effect
    * read receipts and delivery receipts supported by default via Message Events
    */
   bool                                  legacyChatXepSupport;

   /**
    * XEP-0184 Message Delivery Receipts support for chat
    * valid only if legacyChatXepSupport is off
    */
   bool                                  enableChatDeliveryReceipts;
   
   /**
    * XEP-0333: Displayed Markers support for chat
    * valid only if legacyChatXepSupport is off 
    */
   bool                                  enableChatReadReceipts;

   /**
    * XEP-0393: Message Styling support for chat
    */
   bool                                  supportChatMessageStyling;

   /**
    * XEP-0184: Message Delivery Receipts support for groupchat
    */
   bool                                  enableGroupChatDeliveryReceipts;

   /**
    * XEP-0333: Displayed Markers support for groupchat
    */
   bool                                  enableGroupChatReadReceipts;

   XmppAccountSettings()
   {
      port = 0;
      priority = 0;
      softwareName = "CPCAPI2-based Client";
      softwareVersion = "1.0";
      identityCategory = "client";
      identityType = "phone";
      connectTimeOut = 10;
      connectRandomInterval = 30;
      keepAliveTime = 30;
      usePingKeepAlive = false;
      enableLocalSocks5Proxy = true;
      enableRemoteStreamHostDiscovery = true;
      httpFileUploadTransferRate = 0;
      sslVersion = TLS_DEFAULT;
      ignoreCertVerification = false;
      logXmppStanzas = true;
      ipVersion = IpVersion_V4;
      enableStreamManagement = true;
      enableStreamResumption = false; // bliu: turn off for now and need more field result
      maxStreamResumptionTimeout = 600; // 10 mins
      streamManagementSequence = 0;
      publishInitialPresenceAsAvailable = true;
      fallbackOnResourceConflict = false;
      enableCompression = true;
      enableXmppPresence = true;
      enableXmppStanza = true;
      unrequestedAckSendIntervalSec = 60;
      enableSeeOtherHost = false;
      logTlsEncryptionKey = false;
      legacyChatXepSupport = true;
      enableChatDeliveryReceipts = true;
      enableChatReadReceipts = true;
      supportChatMessageStyling = true;
      enableGroupChatDeliveryReceipts = false;
      enableGroupChatReadReceipts = false;
   }
};

}
}
#endif // CPCAPI2_XMPP_ACCOUNT_SETTINGS_H
