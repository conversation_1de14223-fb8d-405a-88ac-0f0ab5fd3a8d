#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_MANAGER_H__)
#define __CPCAPI2_XMPP_CHAT_MANAGER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "XmppChatTypes.h"

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace XmppAccount
{
typedef unsigned int XmppAccountHandle;
}

namespace XmppChat
{
// Forward declarations
class XmppChatHandler;

/**
 * Interface for XMPP chat related functionality in the SDK.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppChatManager
{
public:
   /**
    * Get a reference to the %XmppChatManager interface.
    *
    * @param cpcPhone the phone object to be associated with this manager.
    *
    * @return the %XmppChatManager interface.
    */
   static XmppChatManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for chat related events on the specified account.
    * Set the handler immediately after getting a reference to this manager.
    *
    * To un-register the handler, pass NULL for handler. Must be called on
    * the same thread as XmppAccountManager::process(..)
    *
    * @param account the user account to be associated with the handler.
    * @param handler the handler to register.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppChatHandler* handler) = 0;

   /**
    * Allocates a chat session within the SDK. This function is
    * used in concert with addParticipant(..) and start(..) to begin a
    * new chat session. Use end..() to later terminate the chat session.
    *
    * @param account the user account of the creator of the
    *                new chat session.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual XmppChatHandle createChat(XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Adds a participant to the chat session. Call this
    * function after createChat(..) and before start(..).
    *
    * NOTE: Only the first participant will be used, as only 1-1 chat is
    * supported at the moment. Additional participants will be
    * ignored.
    *
    * @param chat the chat session to add a participant to.
    * @param participantAddress the JID of the participant. The
    *                              format of the targetAddress parameter is
    *                              <EMAIL>.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int addParticipant(XmppChatHandle chat, const cpc::string& participantAddress) = 0;

   /**
    * Initiates a chat session.
    *
    * @param chat The chat session to initiate.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int start(XmppChatHandle chat) = 0;

   /**
    * Ends a chat session.
    *
    * @param chat The chat session to terminate.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int end(XmppChatHandle chat) = 0;

   /**
    * Sends a message to the other party that is part of the chat session.
    *
    * @param chat The chat session to send the message against.
    * @param messageContent the content of message in plain text to send.
    * @param htmlText the content of message in html to send. The content should be properly escasped in order to be embedded in Xmpp stanza.
    * @param subject the subject of message to send.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppChatMessageHandle sendMessage(XmppChatHandle chat, const cpc::string& messageContent, const cpc::string& htmlText = "", const cpc::string& subject = "") = 0;

   /**
    * Sends a message reaction to the other party that is part of the chat session.
    *
    * @param chat The chat session to send the message against.
    * @param target The chat message to send the reaction against.
    * @param reactions The reactions to send, as a series of emojis encoded as Unicode codepoints.
    *
    * @return the handle of the reaction being sent.
    */
   virtual XmppChatMessageHandle sendReaction(XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string>& reactions) = 0;

   /**
    * Sends a message retraction to the other party that is part of the chat session.
    *
    * @param chat The chat session to send the retraction against.
    * @param target The chat message to be retracted.
    *
    * @return the handle of the retraction being sent.
    */
   virtual XmppChatMessageHandle sendMessageRetraction(XmppChatHandle chat, const cpc::string& target) = 0;

   /**
    * DEPRECATED: there is no real way to accept/reject incoming message session in Xmpp.
    * Used after receiving an incoming chat session to accept the
    * session offered by the remote party.
    *
    * @param chat The incoming chat session to accept.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int accept(XmppChatHandle chat) = 0;

   /**
    * DEPRECATED: there is no real way to accept/reject incoming message session in Xmpp.
    * Used after receiving an incoming chat session to reject the
    * session offered by the remote party.
    *
    * @param chat The incoming chat session to reject.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int reject(XmppChatHandle chat) = 0;

   /**
    * DEPRECATED: new and simpler version is available with same function name and less parameters
    * Notifies the other participant that the message has been successfully
    * delivered.
    *
    * @param chat the chat session associated with the message delivered.
    * @param message the delivered message.
    * @param messageDeliveryStatus the status of the message delivery.
    *
    * @return the handle of the notification being sent.
    */
   DEPRECATED virtual XmppChatMessageHandle notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus) = 0;

   /**
    * DEPRECATED: new and simpler version is available with same function name and less parameters
    * Notifies the other participant that the message has been successfully
    * displayed.
    *
    * @param chat the chat session associated with the message being composed.
    * @param message the displayed message.
    * @param messageDisplayStatus the status of the message display
    *
    * @return the handle of the notification being sent.
    */
   DEPRECATED virtual XmppChatMessageHandle notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus) = 0;

   /**
    * DEPRECATED: The SDK makes this call internally and automatically upon receiving an incoming message
    * Notifies the other participant that the message has been successfully
    * delivered.
    *
    * @param chat the chat session associated with the message delivered.
    * @param message the delivered message.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message) = 0;

   /**
    * DEPRECATED: This is not used at all
    * Notifies the other participant that the message has been successfully
    * displayed.
    *
    * @param chat the chat session associated with the message being composed.
    * @param message the displayed message.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message) = 0;

   /**
    * Notifies the other participant that the message has been read.
    *
    * @param chat the chat session associated with the message being composed.
    * @param message the message being read.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   DEPRECATED virtual int notifyMessageRead(XmppChatHandle chat, XmppChatMessageHandle message) { return kSuccess; }

   /**
    * Notifies the other participant that the message has been read.
    *
    * @param chat the chat session associated with the message being composed.
    * @param threadId the thread ID.
    * @param messageId the message ID.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int notifyMessageRead(XmppChatHandle chat, const cpc::string& threadId, const cpc::string& messageId) { return kSuccess; }

   /**
    * Notifies the other participant that the message has been read.
    *
    * @param account the account associated with the message being read.
    * @param peerJid the JID of peer end. The SDK will use bare JID internally.
    * @param threadId the thread ID.
    * @param messageId the message ID.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int notifyMessageRead(XmppAccount::XmppAccountHandle account, const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId) { return kSuccess; }

   /**
    * Indicates that a new message is being typed/composed.
    *
    * Notes:
    *
    * 1) It does not necessarily mean that a notification will be sent to the other
    *    participant of the chat session. It depends on the internal state and timers.
    *
    * 2) However, this method must be called often while the participant is actively
    *    typing/composing the message in order to maintain an accurate internal state.
    *
    * @param chat the chat session associated with the message being composed.
    * @param refreshInterval the refresh interval to use (in seconds), defaults to 90 seconds.
    * @param idleInterval the idle interval to use (in seconds), defaults to 15 seconds.
    *        Switch to idle state immediately without switching to active state if idleInterval = 0.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setIsComposingMessage(XmppChatHandle chat, int refreshInterval = 90, int idleInterval = 15) = 0;

   // The following (synchronous) methods are related to the use of XMPP with the RemoteSync module.
   virtual cpc::string getRemoteSyncFromID(XmppChatHandle chat, XmppChatMessageHandle message) = 0;
   virtual cpc::string getRemoteSyncToID(XmppChatHandle chat, XmppChatMessageHandle message) = 0;
   virtual cpc::string getRemoteSyncConversationID(XmppChatHandle chat) = 0;
   DEPRECATED virtual cpc::string getRemoteSyncUniqueID(XmppChatHandle chat, const cpc::string& stanzaID) = 0;
   virtual cpc::string getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID = "")  = 0;

   /**
    * Returns a text/xml document with both of the messageContent (plaintext)
    * and htmlText (HTML) encoded into the string. Note that it is required to
    * have *at least* a plaintext string. The Content-Type of the resulting
    * RemoteSync string will be text/xml.
    */
   virtual cpc::string getRemoteSyncEncodedContent(const cpc::string& plainText, const cpc::string& htmlText = "") = 0;

   /**
    * Method parses the RemoteSync text/xml body and returns in two outparams,
    * the plaintext and HTML fragment embedded inside of it.
    */
   virtual int getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML) = 0;

   /**
    * Method to check whether an XmppChatHandle is valid. A handle is valid if the handle was returned via
    * createChat(..), the associated chat session has not yet ended.
    * Result is returned via XmppChatHandler::onValidateChatHandleResult(..)
    */
   virtual int validateChatHandle(XmppAccount::XmppAccountHandle account, XmppChatHandle chat) = 0;

   /**
    * Last message correction.
    *
    * @param chat The chat session to send the message against.
    * @param replaces The messageID of the message this message replaces.
    * @param messageContent the content of message in plain text to send.
    * @param htmlText the content of message in html to send. The content should be properly escasped in order to be embedded in Xmpp stanza.
    * @param subject the subject of message to send.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppChatMessageHandle replaceMessage(XmppChatHandle chat, const cpc::string& replaces, const cpc::string& messageContent, const cpc::string& htmlText = "", const cpc::string& subject = "") = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppChatManager.
    */
   virtual ~XmppChatManager() {}
};

}
}

#endif // __CPCAPI2_XMPP_CHAT_MANAGER_H__
