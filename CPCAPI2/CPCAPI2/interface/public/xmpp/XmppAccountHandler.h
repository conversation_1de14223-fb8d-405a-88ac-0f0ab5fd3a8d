#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_HANDLER_H)
#define CPCAPI2_XMPP_ACCOUNT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{

namespace XmppAccount
{

struct XmppStorageData;
typedef unsigned int XmppAccountHandle;

enum Error
{
   Error_None                 = 1,
   Error_NoHandlerSet         = 2,
   Error_IoError              = 3,
   Error_DnsError             = 4,
   Error_HostNotFound         = 5,
   Error_ConnectionRefused    = 6,
   Error_AlreadyEnabled       = 7,
   Error_NotEnabled           = 8,
   Error_AlreadyConnected     = 9,
   Error_NotConnected         = 10,
   Error_ParseError           = 11,
   Error_StreamError          = 12,
   Error_TlsFailed            = 13,
   Error_CompressionFailed    = 14,
   Error_UnsupportedAuthMech  = 15,
   Error_AuthenticationFailed = 16,
   Error_NotDisconnected      = 17
};

/**
 * Details about the server certificate.
 * Used for diagnosing connection failures due to certificate
 * validation errors.
 */
struct XmppTLSConnectionInfo
{
   XmppTLSConnectionInfo() : certificateStatus(0) {}

   enum CertificateStatus
   {
      CertificateStatus_Ok = 0,               /**< The certificate is valid and trusted. */
      CertificateStatus_Invalid = 1,          /**< The certificate is not trusted. */
      CertificateStatus_SignerUnknown = 2,    /**< The certificate hasn't got a known issuer. */
      CertificateStatus_Revoked = 4,          /**< The certificate has been revoked. */
      CertificateStatus_Expired = 8,          /**< The certificate has expired. */
      CertificateStatus_NotActive = 16,       /**< The certificate is not yet active. */
      CertificateStatus_WrongPeer = 32,       /**< The certificate has not been issued for the peer we're connected to. */
      CertificateStatus_CertSignerNotCa = 64, /**< The signer is not a CA. */
      CertificateStatus_WrongPubKey = 128     /**< The public key does not match any of the keys specified in XmppAccountSettings::pinnedCertPublicKeys */
   };

   int certificateStatus;          /**< Bitwise or'ed CertificateStatus or CertificateStatus_Ok. */
   cpc::string issuer;             /**< The name of the issuing entity.*/
   cpc::string server;             /**< The server the certificate has been issued for. */
   cpc::vector<cpc::string> peerNames;           /**< From the subjectAltName in the certificate. */
   cpc::string protocol;           /**< The encryption protocol used for the connection. */
   cpc::string cipher;             /**< The cipher used for the connection. */
   cpc::string mac;                /**< The MAC used for the connection. */
   cpc::string compression;        /**< The compression used for the connection. */
   cpc::string publicKey;          /**< Base 64 encoded version of the public key. */
};

/**
 * Event passed in XmppAccountHandler::onAccountStatusChanged();
 * notifies that the status of the account has changed. It contains:
 * <ul>
 * <li>accountStatus: The current status of the account is one of the values in Status.
 * <li>errorStatusCode: The error code for the failure.
 * <li>errorText: The text description of the error code, for example,
 * "An I/O error occured".
 * </ul>
 */
struct XmppAccountStatusChangedEvent
{
   enum Status
   {
      Status_Connected           = 1,
      Status_Failure             = 2,
      Status_Disconnected        = 3,
      Status_Connecting          = 4,
      Status_Disconnecting       = 5,
      Status_Destroyed           = 6,
      Status_Resuming            = 7,
      Status_Resumed             = 8
   };

   Status                        accountStatus;
   Error                         errorCode;
   cpc::string                   errorText;
   XmppTLSConnectionInfo         tlsInfo;
   cpc::string                   remote; /** Identifier string for the remote entity. The value is not guaranteed and should be treated as an optional. */
};

/**
 * Event passed in XmppAccountHandler::onError().
 */
struct ErrorEvent
{
   cpc::string errorText;
   Error errorCode;
};

/**
 * Event passed in XmppAccountHandler::onLicensingError().
 */
struct LicensingErrorEvent
{
   cpc::string errorText;
};

struct EntityTimeEvent
{
   int errorCode;
   cpc::string from;
   uint64_t timestamp; // number of seconds since 1970-01-01 00:00:00, in UTC only
   uint16_t millisecond; // millisecond part of the timestamp
};

struct EntityFeatureEvent
{
   enum Feature
   {
      EntityTime,
      Ping,
      Privacy,
      Notification,
      AdhocCommands
   };

   cpc::string entity; // the JID of the entity possessing the features. empty JID represents for the server.
   cpc::vector<Feature> features;
};

// DEPRECATED: this event won't be fired to the app layer any more
struct StreamManagementStateEvent
{
   cpc::string id;
   int sequence;
};

struct PrivateStorageDataEvent
{
   cpc::vector<XmppStorageData> data;
};

/**
 * The handler for events on XmppAccount; passed in XmppAccount::setHandler().
 */
class XmppAccountHandler
{

public:

   /**
    * Notifies the application when the %XmppAccount has changed to a specific state.
    */
   virtual int onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args) = 0;

   /**
    * Notifies the application when an account error has occurred.
    */
   virtual int onError(XmppAccountHandle account, const ErrorEvent& args) = 0;

   /**
    * DEPRECATED; no longer used. Licensing errors are now indicated with a PhoneErrorHandler::onLicensingErrorEvent
    */
   DEPRECATED virtual int onLicensingError(XmppAccountHandle account, const LicensingErrorEvent& args) { return kSuccess; }

   /**
    * Notifies the application about entity time as requested.
    */
   virtual int onEntityTime(XmppAccountHandle account, const EntityTimeEvent& args) = 0;

   /**
    * Notifies what features are supported by an entity.
    * Query the args for information.
    */
   virtual int onEntityFeature(XmppAccountHandle account, const EntityFeatureEvent& args) = 0;

   /**
    * DEPRECATED: the SDK handles stream resumption internally if possible. This event won't be fired to the app layer any more.
    * Notifies stream management state.
    * Query the args for information.
    * Otional.
    */
   DEPRECATED virtual int onStreamManagementState(XmppAccountHandle account, const StreamManagementStateEvent& args) { return kSuccess; }

   virtual int onPrivateStorageData(XmppAccountHandle account, const PrivateStorageDataEvent& args) { return kSuccess; }
};

cpc::string get_debug_string(const CPCAPI2::XmppAccount::Error& error);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus& status);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppTLSConnectionInfo& info);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status& status);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::ErrorEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::LicensingErrorEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityTimeEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityFeatureEvent::Feature& feature);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityFeatureEvent& event);
cpc::string get_debug_string(const CPCAPI2::XmppAccount::StreamManagementStateEvent& event);

}

}

#endif // CPCAPI2_XMPP_ACCOUNT_HANDLER_H
