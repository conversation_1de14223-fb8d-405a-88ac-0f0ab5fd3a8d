#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppMultiUserChatTypes.h"
#include "xmpp/XmppMultiUserChatHandler.h"

namespace CPCAPI2
{
namespace XmppMultiUserChat
{

/**
 * Manager interface to control incoming, outgoing and established multi user chat calls;
 * get a reference to the interface using the static method getInterface().
 */
class CPCAPI2_SHAREDLIBRARY_API XmppMultiUserChatManager
{
public:
   /**
    * Get a reference to the %XmppMultiUserChatManager interface.
    */
   static XmppMultiUserChatManager* getInterface(Phone* cpcPhone);

   /**
    * Set the handler for call events on the specified account. Set the handler
    * immediately after creating the account.
    *
    * To un-register the handler, pass NULL for handler. Must be called on the
    * same thread as XmppAccountManager::process(..)
    */
   virtual int setHandler(
      XmppAccount::XmppAccountHandle account,
      XmppMultiUserChatHandler* handler) = 0;

   /**
    * Get a list of all multi user chat rooms accessible to this account.
    */
   virtual int getRoomList(XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Set room bookmarks
    */
   DEPRECATED virtual int setRoomBookmarks(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks) = 0;

   /**
    * Get room bookmarks
    */
   DEPRECATED virtual int getRoomBookmarks(XmppAccount::XmppAccountHandle account) = 0;

   /**
    * Allocates a new multi user chat within the SDK.  This function is used in concert with other operations e.g. join(...).
    * @param room A unique room identifier will be created if left as blank. Should follow node identifier definition specified in XEP-0029.
    * @param instant An instant room is created with default configurations vesus a reserved room requires setConfigurations().
    */
   virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, const cpc::string& room) = 0;
   DEPRECATED virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, bool instantRoom = true) = 0;

   /**
    * Destory a multi user chat room.
    * @param alternate An alternate room to join for the current participant in the room.
    * @param password The password for joining the alternate room.
    */
   virtual int destroyRoom(XmppMultiUserChatHandle handle, const cpc::string& reason = "", const cpc::string& alternate = "", const cpc::string& password = "") = 0;

   /**
    * Get the details about a multi user chat room accessible to this account.
    * Results will be notified through onMultiUserChatRoomStateChanged event.
    */
   virtual int getRoomInfo(XmppMultiUserChatHandle handle) = 0;

   /**
    * Get the details about multi user chat rooms accessible to this account.
    * Results will be notified through onMultiUserChatRoomStateChanged event.
    */
   virtual int getRoomsInfo(const cpc::vector<XmppMultiUserChatHandle>& handles) = 0;

   /**
    * Accept an invitation to a multi user chat.
    * @param nickname A unique participant identifier. Should follow node identifier definition specified in XEP-0029.
    * @param historyRequester Four possibilities:
    * 1. since:<number>, number of seconds since 1970-01-01 00:00:00, in UTC only
    * 2. message:<number>, e.g. "message:100" the most recent 100 messages
    * 3. char:<number>, e.g. "char:1000" the max characters of *complete* Xmpp messages
    * 4. seconds<number>, e.g. "seconds:3600" all message within the past 3600 seconds
    */
   virtual int accept(
      XmppMultiUserChatHandle handle,
      const cpc::string& nickname,
      const cpc::string& historyRequester = "",
      const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd = cpc::vector<XmppMultiUserChatHistoryItem>()) = 0;

   /**
    * Reject an invitation to a multi user chat.
    */
   virtual int decline(XmppMultiUserChatHandle handle, const cpc::string& reason = "") = 0;

   /**
    * Join the multi user chat.
    * @param config A configuration to create a room if joining a non-existing room.
    * @param room A unique room identifier will be created if left as blank. Should follow node identifier definition specified in XEP-0029.
    * @param nickname A unique participant identifier. Should follow node identifier definition specified in XEP-0029.
    * @param historyRequester Four possibilities:
    * 1. since:<number>, number of seconds since 1970-01-01 00:00:00, in UTC only
    * 2. message:<number>, e.g. "message:100" the most recent 100 messages
    * 3. char:<number>, e.g. "char:1000" the max characters of *complete* Xmpp messages
    * 4. seconds<number>, e.g. "seconds:3600" all message within the past 3600 seconds
    *
    * Note: the SDK will automatically accept default room configuration up creation and it's up to the app layer to change the configuration upon onMultiUserChatConfigurationRequested event.
    */
   virtual int join(
      XmppMultiUserChatHandle handle,
      RoomConfig config,
      const cpc::string& nickname,
      const cpc::string& password = "",
      const cpc::string& historyRequester = "",
      const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd = cpc::vector<XmppMultiUserChatHistoryItem>()) = 0;

   DEPRECATED virtual int join(
      XmppMultiUserChatHandle handle,
      const cpc::string& room,
      const cpc::string& nickname,
      const cpc::string& password = "",
      const cpc::string& historyRequester = "",
      const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd = cpc::vector<XmppMultiUserChatHistoryItem>()) = 0;

   /**
    * Leave the multi user chat; the local user will no longer be a participant.
    */
   virtual int leave(XmppMultiUserChatHandle handle, const cpc::string& reason = "") = 0;

   /**
    * Send a plain-text or html message to everyone currently in the multi user chat.
    * @param handle The chat session to send the message against.
    * @param plain the content of message in plain text to send.
    * @param html the content of message in html to send. The content should be properly escasped in order to be embedded in Xmpp stanza.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppMultiUserChatMessageHandle sendMessage(XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html) = 0;

   /**
    * Send a reaction to a specific MUC message.
    * @param handle The chat session to send the reaction against.
    * @param target The chat message to send the reaction against.
    * @param reactions The reactions to send, as a series of emojis encoded as Unicode codepoints.
    *
    * @return the handle of the reaction being sent.
    */
   virtual XmppMultiUserChatMessageHandle sendReaction(XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions) = 0;

   /**
    * Send a message retraction to a specific MUC message.
    * @param handle The chat session to send the retraction against.
    * @param target The chat message to be retracted.
    *
    * @return the handle of the retraction being sent.
    */
   virtual XmppMultiUserChatMessageHandle sendMessageRetraction(XmppMultiUserChatHandle handle, const cpc::string& target) = 0;

   /**
    * Indicates that a new message is being typed/composed.
    *
    * Notes:
    *
    * 1) It does not necessarily mean that a notification will be sent to the other
    *    participant of the chat session. It depends on the internal state and timers.
    *
    * 2) However, this method must be called often while the participant is actively
    *    typing/composing the message in order to maintain an accurate internal state.
    *
    * @param handle the multi user chat session associated with the message being composed.
    * @param refreshInterval the refresh interval to use (in seconds), defaults to 90 seconds.
    * @param idleInterval the idle interval to use (in seconds), defaults to 15 seconds.
    *        Switch to idle state immediately without switching to active state if idleInterval = 0.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setIsComposingMessage(XmppMultiUserChatHandle handle, int refreshInterval = 90, int idleInterval = 15) = 0;

   /**
    * Send out presence status
    */
   virtual int publishPresence(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note = "") = 0;

   /**
    * Change the nickname.
    * @param nickname A unique participant identifier. Should follow node identifier definition specified in XEP-0029.
    */
   virtual int changeNickname(XmppMultiUserChatHandle handle, const cpc::string& nickname) = 0;

   /**
    * Change the subject of the multi user chat.
    */
   virtual int changeSubject(XmppMultiUserChatHandle handle, const cpc::string& subject) = 0;

   /**
    * Send an invitation to another person, passing the person's JID and an optional reason.
    */
   virtual int invite(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason = "") = 0;

   /**
    * Kick a participant, passing the person's nickname and an optional reason.
    */
   virtual int kick(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason = "") = 0;

   /**
    * Ban a participant, passing the person's nickname and an optional reason.
    */
   virtual int ban(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason = "") = 0;

   /**
    * Change the affiliation of a participant, passing the person's nickname and an optional reason.
    * Deprecated: use changeJidAffiliation(..) instead.
    */
   DEPRECATED virtual int changeAffiliation(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason = "") = 0;

   /**
    * Change the affiliation of a participant, passing the person's bare JID and an optional reason.
    */
   virtual int changeJidAffiliation(XmppMultiUserChatHandle handle, const cpc::string& bareJid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason = "") = 0;

   /**
    * Change the role of a participant, passing the person's nickname and an optional reason.
    */
   virtual int changeRole(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason = "") = 0;

   /**
    * Request for room configuration. Should be only called after successfully joining a room.
    */
   virtual int requestConfigurations(XmppMultiUserChatHandle handle) = 0;

   /**
    * Change room configuration. It is preferably to change configurations upon onMultiUserChatConfigurationRequested event.
    */
   virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform) = 0;
   DEPRECATED virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations) = 0;

   /**
    * Request for list.
    */
   virtual int requestList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type) = 0;

   /**
    * Modify list. Only changed items should be present.
    */
   virtual int setList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items) = 0;

   /**
    * @brief notify multiuser chat room that the message is read
    * 
    * @param handle the multi user chat session associated with the read message
    * @param messageId id of the read message
    * @return int kSuccess if the operation was successful, kError otherwise.
    */
   virtual int notifyMessageRead(XmppMultiUserChatHandle handle, const cpc::string& messageId) = 0;

   /**
    * Send a plain-text or html message that replaces a previously sent message.
    * @param handle The chat session to send the message against.
    * @param replaces The messageID of the message this message replaces.
    * @param plain the content of message in plain text to send.
    * @param html the content of message in html to send. The content should be properly escasped in order to be embedded in Xmpp stanza.
    *
    * @return the handle of the message being sent.
    */
   virtual XmppMultiUserChatMessageHandle replaceMessage(XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html) = 0;

protected:
   /*
    * The SDK will manage memory life of %XmppMultiUserChatManager.
    */
   virtual ~XmppMultiUserChatManager() {}
};

}
}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_H
