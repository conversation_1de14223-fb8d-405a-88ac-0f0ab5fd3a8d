#pragma once

#ifndef __CPCAPI2_XMPP_FILE_TRANSFER_HANDLER_H__
#define __CPCAPI2_XMPP_FILE_TRANSFER_HANDLER_H__

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "XmppFileTransferManager.h"

namespace CPCAPI2
{
namespace XmppFileTransfer
{

// DEPRECATED: never used
enum FileTransferEndReason
{
   FileTransferEndReason_Unknown                = 1100,
   FileTransferEndReason_UserTerminatedLocally  = 1110,
   FileTransferEndReason_UserTerminatedRemotely = 1120,
   FileTransferEndReason_ServerError            = 1130,
   FileTransferEndReason_ServerRejected         = 1140,
};

// DEPRECATED: never used
enum FileTransferState
{
   FileTransferState_None             = 0,
   FileTransferState_LocalOriginated  = 1000,
   FileTransferState_RemoteOriginated = 1010,
   FileTransferState_Connected        = 1040,
   FileTransferState_Early            = 1050,
   FileTransferState_Ended            = 1060,
};

/**
   * Represents whether we are sending or receiving the file transfer.
   */
enum FileTransferType
{
   FileTransferType_Incoming = 1200,
   FileTransferType_Outgoing = 1210,
};

enum FileTransferItemEndReason
{
   FileTransferItemEndReason_Complete              = 1300,
   FileTransferItemEndReason_Interrupted           = 1310, // DEPRECATED
   FileTransferItemEndReason_Failed                = 1320, // DEPRECATED
   FileTransferItemEndReason_LocalCancel           = 1330,
   FileTransferItemEndReason_RemoteCancel          = 1340,
   FileTransferItemEndReason_BadFile               = 1350,
   FileTransferItemEndReason_BadConnection         = 1360,
   FileTransferItemEndReason_FileTooLarge          = 1370,
   FileTransferItemEndReason_Unknown               = 1399,
};

struct ServiceAvailabilityEvent
{
   bool XEP0363_available;
   cpc::string XEP0363_service;
   uint64_t XEP0363_maxFileSize; // only valid if XEP0363_available is true and 0 means the server doesn't provide a default value
};

/**
   * Do not use this interface; it covers alpha-grade
   * functionality that is not officially supported and is subject to change.
   * Event fired by the SDK to signal a new file transfer call. The items
   * are not yet known at this point and will be provided in a subsequent
   * event (FileTransferConfiguredEvent) provided the file transfer is
   * completed and not rejected.
   */
struct NewFileTransferEvent
{
   DEPRECATED FileTransferState fileTransferState;
   FileTransferType fileTransferType;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   XmppFileTransferItems fileItems; // The items present in the transfer
};

/**
   * Fired by the SDK to signal the completion of the entire file transfer
   */
struct FileTransferEndedEvent
{
   DEPRECATED FileTransferState fileTransferState;
   DEPRECATED FileTransferEndReason endReason;
   DEPRECATED unsigned int xmppResponseCode;
   DEPRECATED cpc::string signallingEndEvent;
   DEPRECATED cpc::string signallingEndReason;
};

/**
   * Do not use this interface; it covers alpha-grade
   * functionality that is not officially supported and is subject to change.
   * Event fired by the SDK to signal completion of a file transfer item.
   * This event is only fired when the item is completed or interrupted.
   * In the event that the call is disconnected before the items are
   * finished, FileTransferItemEndedEvent(s) will be fired for each item in
   * progress.
   */
struct FileTransferItemEndedEvent
{
   XmppFileTransferItemHandle fileTransferItem; // The item in question
   FileTransferItemEndReason endReason; // in case of FileTransferItemEndReason_Failed, check for other possible stream type
   DEPRECATED int streamTypeAttempted; // stream type attempted for this transfer. the value may be different from the original value of XmppFileTransferItemDetail.streamType
   cpc::string remoteFileURI; // URI of the remote file, currently only available as if the file is successfully uploaded to the server via XEP-0363
};

/**
   * Do not use this interface; it covers alpha-grade
   * functionality that is not officially supported and is subject to change.
   * Event fired by the SDK to indicate the progress of an individual item
   */
struct FileTransferItemProgressEvent
{
   XmppFileTransferItemHandle fileTransferItem; // The item in question
   unsigned short percent; // percentage [ 0% .. 100% ]
};

/**
   * Do not use this interface; it covers alpha-grade
   * functionality that is not officially supported and is subject to change.
   * Used to report general SDK error conditions, such as invalid handles,
   * or cases where the transfer is not in a valid state for the requested
   * operation.
   */
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Do not use this interface; it covers alpha-grade
 * functionality that is not officially supported and is subject to change.
 */
class CPCAPI2_SHAREDLIBRARY_API XmppFileTransferHandler
{
public:

   /**
   * Notifies that the service is available on the server.
   * Query the evt for information.
   * A handler to this event is optional.
   */
   virtual void onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& evt) {}

   /**
    * Callback invoked by the SDK when a new file transfer is created,
    * either by sending or receiving.
    */
   virtual int onNewFileTransfer(
      const XmppFileTransferHandle& fileTransfer,
      const NewFileTransferEvent& args ) = 0;

   /**
    * Callback invoked by the SDK when a file transfer has completed
    * (including all of its file transfer items)
    */
   virtual int onFileTransferEnded(
      const XmppFileTransferHandle& fileTransfer,
      const FileTransferEndedEvent& args ) = 0;

   /**
    * Progress indication (in percent) of an ongoing file transfer item
    */
   virtual int onFileTransferItemProgress(
      const XmppFileTransferHandle& fileTransfer,
      const FileTransferItemProgressEvent& args ) = 0;

   /**
    * Invoked by the SDK when a file (item) has finished transfering.
    * i.e. when percentage completion reaches 100%, or the item is
    * cancelled either locally or remotely.
    */
   virtual int onFileTransferItemEnded(
      const XmppFileTransferHandle& fileTransfer,
      const FileTransferItemEndedEvent& args ) = 0;

   /**
    * Invoked by the SDK for errors which do not fall into the categories
    * outlined above.
    */
   virtual int onError(
      const XmppFileTransferHandle& fileTransfer,
      const ErrorEvent& args ) = 0;
};

}
}

#endif // __CPCAPI2_XMPP_FILE_TRANSFER_HANDLER_H__
