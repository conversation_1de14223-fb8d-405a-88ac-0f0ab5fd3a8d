#pragma once

#if !defined(__CPCAPI2_CPM_TYPES_H__)
#define __CPCAPI2_CPM_TYPES_H__

namespace CPCAPI2
{
namespace CPM
{

/**
 * Possible message delivery statuses (IMDN).
 */
enum MessageDeliveryStatus 
{
   MessageDeliveryStatus_Delivered = 1,
   MessageDeliveryStatus_Failed    = 2,
   MessageDeliveryStatus_Forbidden = 3,
   MessageDeliveryStatus_Error     = 4
};

/**
 * Possible message display statuses (IMDN).
 */
enum MessageDisplayStatus 
{
   MessageDisplayStatus_Displayed = 1,
   MessageDisplayStatus_Forbidden = 2,
   MessageDisplayStatus_Error     = 3
};

/**
 * Types of disposition notifications supported (IMDN).
 */
enum DispositionNotificationType
{
   DispositionNotificationType_PositiveDelivery = 1,
   DispositionNotificationType_NegativeDelivery = 2,
   DispositionNotificationType_Display          = 3
};

/**
 * MIME types supported.
 */
enum MimeType
{
   MimeType_TextPlain = 1,
   MimeType_TextXml   = 2,
   MimeType_TextHtml  = 3,
   MimeType_ImageJpeg = 4,
   MimeType_ImagePng  = 5
};

}
}

#endif // __CPCAPI2_CPM_TYPES_H__
