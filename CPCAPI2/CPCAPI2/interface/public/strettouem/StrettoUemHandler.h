#pragma once
#ifndef __CPCAPI2_STRETTOUEMHANDLER_H__
#define __CPCAPI2_STRETTOUEMHANDLER_H__

#include "cpcstl/string.h"
#include "StrettoUemTypes.h"

namespace CPCAPI2
{
   namespace StrettoUem
   {
      class StrettoUemHandler
      {
      public:

         /**
          * Event fired to the application in order to indicate the failure of
          * connecting to the Stretto server.
          */
         virtual int onConnectionFailed( const StrettoUemHandle& handle, const OnConnectionFailedEvent& evt ) = 0;

         /**
          * Event fired to application to indicate success or failure of the report.
          * The response code is a number returned from the server.
          */
         virtual int onReportResponse( const StrettoUemHandle& handle, const OnReportResponseEvent& evt ) = 0;
      };
   }
}

#endif // __CPCAPI2_ANALYTICSHANDLER_H__
