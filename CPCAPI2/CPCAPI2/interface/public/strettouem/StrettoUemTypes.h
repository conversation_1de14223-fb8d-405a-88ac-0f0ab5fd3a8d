#pragma once
#ifndef __CPCAPI2_STRETTOUSEREXPERIENCEMETRICSTYPES_H__
#define __CPCAPI2_STRETTOUSEREXPERIENCEMETRICSTYPES_H__

#include <cpcstl/string.h>

namespace CPCAPI2
{
   namespace StrettoUem
   {
      typedef unsigned int StrettoUemHandle;
      
      struct OnConnectionFailedEvent
      {
         long errNo; // The system-dependent error number, set when the connection failed
         cpc::string errorValue;
      };

      struct OnReportResponseEvent
      {
         long responseCode;   // HTTP response code
         cpc::string errorValue;
      };

      /**
       * Settings necessary to open a connection to the Stretto UEM server.
       * These can be obtained via Stretto provisioning in the StrettoUemManager::decodeProvisioningResponse method.
       */
      struct StrettoUemSettings
      {
         /**
          * URL of the Stretto server.
          */
         cpc::string serverUrl;
      
         /**
          * The username (if any) used for HTTP-Auth
          */
         cpc::string httpUserName;
         /**
          * The password (if any) used for HTTP-Auth
          */
         cpc::string httpPassword;
         /**
          * The user currently logged in
          */
         cpc::string strettoUserName;
      };
      
      struct GeneralStats
      {
         /**
          * Client version (free form)
          */
         cpc::string  clientVersion;
         
         /**
          * Date of installation of the client (seconds since UNIX epoch) 
          */
         int64_t installationDate;

         /**
          * UTC timezone offset in minutes. e.g. UTC-5:00 would be represented as
          * utcTimzoneOffsetMinutes = -300
          */
         int utcTimezoneOffsetMinutes;

         GeneralStats()
         {
            installationDate = 0;
            utcTimezoneOffsetMinutes = 0;
         }
      };
   }
}

#endif //  __CPCAPI2_STRETTOUSEREXPERIENCEMETRICSTYPES_H__
