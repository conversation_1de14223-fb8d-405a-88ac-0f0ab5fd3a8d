#pragma once

#ifndef CPCAPI2_STRETTOUSEREXPERIENCEMETRICS_H
#define CPCAPI2_STRETTOUSEREXPERIENCEMETRICS_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "StrettoUemTypes.h"
#include "StrettoUemHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace StrettoUem
   {
      /**
       * Provides functionality for Stretto User Experience Metrics (UEM). This module allow for gatherig of metrics such as
       * call quality reports; this module also provides support for seamlessly sending these metrics to Stretto.
       */
      class CPCAPI2_SHAREDLIBRARY_API StrettoUemManager
      {
      public:
         /**
          * Get a reference to the StrettoUemManager interface.
          */
         static StrettoUemManager* getInterface(Phone* cpcPhone);

         /**
          * Method to add a notification listener to receive
          * responses, errors, and updates.
          * 
          * @param notificationListener
          */
         virtual void setHandler(StrettoUemHandler * notificationHandler) = 0;
         
         /**
          * Decodes a provisioning response received from the Provisioning interface.
          * @param provisioningResponse The complete provisioning response, as returned by the Provisioning interface.
          * @param outStrettoUemSettings An StrettoUemSettings object which will be populated by this method based on the provisioning
          * response if this method returns a non-failure response.
          */
         virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, StrettoUemSettings& outStrettoUemSettings) = 0;
         
         /**
          * Opens a Stretto UEM session. Once this method is called, recording of metrics will begin. Call sendReport(..) to send a UEM report
          * to Stretto.
          */
         virtual StrettoUemHandle open(const StrettoUemSettings& settings, const GeneralStats& general) = 0;
         
         /**
          * Closes an open UEM session.
          */
         virtual int close(StrettoUemHandle serverHandle) = 0;

         /**
          * Sends a UEM report to the server
          */
         virtual int sendReport(StrettoUemHandle serverHandle) = 0;
         
         /**
          * The blocking modes for the process() function.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Method which must be called by the application in order to
          * 'service' events coming from the remote sync handler.
          */
         virtual int process(unsigned int timeout) = 0;

      protected:
         /*
          * The SDK will manage memory life of %StrettoUemManager.
          */
         virtual ~StrettoUemManager() {}
      };
   }
}
#endif // CPCAPI2_STRETTOUSEREXPERIENCEMETRICS_H
