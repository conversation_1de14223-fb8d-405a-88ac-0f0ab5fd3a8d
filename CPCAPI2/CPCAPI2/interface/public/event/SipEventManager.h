#pragma once

#if !defined(CPCAPI2_SIP_EVENT_MANAGER_H)
#define CPCAPI2_SIP_EVENT_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipEventState.h"

namespace CPCAPI2
{
class Phone;

namespace SipEvent
{
class SipEventSubscriptionHandler;

typedef unsigned int SipEventSubscriptionHandle;
typedef unsigned int SipEventPublicationHandle;

enum SipSubscriptionTerminateReason
{
   SipSubscriptionTerminateReason_NoResource,
   SipSubscriptionTerminateReason_Deactivate,
   SipSubscriptionTerminateReason_Probation,
   SipSubscriptionTerminateReason_Rejected,
   SipSubscriptionTerminateReason_Timeout,
   SipSubscriptionTerminateReason_Giveup,
   SipSubscriptionTerminateReason_NoReason,
};

/**
* A struct.
*/
struct SipEventSubscriptionSettings
{
   cpc::string            eventPackage;
   cpc::vector<Parameter> eventPackageParams;
   unsigned int           expiresSeconds;
   cpc::vector<CPCAPI2::MimeType> supportedMimeTypes;

   SipEventSubscriptionSettings()
   {
      expiresSeconds = 3600;
   }
};

/**
* Manager interface that provides SUBSCRIBE-related functionality (RFC 3265); 
* get a reference to the interface using the static method getInterface(); 
* see also SipEvent::SipPresenceManager, which is a more specific interface 
* for handling presence events. 
* This generic interface supports subscriptions to any event package: 
* handling of the content in the event state notifications is left to the 
* application code; we recommend that you not use this 
* manager for “presence” events; instead, use SipEvent::SipPresenceManager.
*/
class CPCAPI2_SHAREDLIBRARY_API SipEventManager
{
public:
   /**
   * Get a reference to the %SipEventManager interface.
   */   
   static SipEventManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for subscription events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same 
   * thread as SipAccount::process(..)
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& eventType,
         SipEventSubscriptionHandler* handler) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Must be invoked prior to calling start(..) so that the event package and other subscription parameters are configured.
   */
   virtual int applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings) = 0;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Sets the address of the event server responsible for sending event notifications.  For example, this may be
   * the address of a resource list server.  If an event server is set on a subscription, the outgoing SUBSCRIBE
   * is sent to the targetAddress for the event server specified in the targetAddress parameter..
   * Call this function after createSubscription(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   */
   virtual int start(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0. 
   * The retryAfter parameter if not -1 will be used for the value of the "retry-after" parameter of the "Subscription-State" header.
   */
   virtual int end(SipEventSubscriptionHandle subscription, SipSubscriptionTerminateReason reason = SipSubscriptionTerminateReason_NoResource, int retryAfter = -1) = 0;

   /**
   * Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
   * @param subscription The incoming subscription session to reject.
   * @param rejectReason The SIP response code sent to the originating party.
   */
   virtual int reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK).
   */
   virtual int accept(SipEventSubscriptionHandle subscription, const SipEventState& eventState) = 0;

   /**
   * Used to put an incoming (server) subscription session to PENDING state (200 OK).
   */
   virtual int provisionalAccept(SipEventSubscriptionHandle subscription, const SipEventState& eventState) = 0;

   /**
   * Used to send outgoing event state on an incoming (server) subscription session.
   * Sends a NOTIFY request with content as specified by the eventState parameter.
   */
   virtual int notify(SipEventSubscriptionHandle subscription, const SipEventState& eventState) = 0;
   
   /**
   * Used to refresh an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant or
   * to the event/resource-list server.
   * @param subscription The outgoing subscription to refresh.
   */
   virtual int refresh(SipEventSubscriptionHandle subscription) = 0;
   

protected:
   /*
    * The SDK will manage memory life of %SipEventManager.
    */
   virtual ~SipEventManager() {}
};

}
}

#endif // CPCAPI2_SIP_EVENT_MANAGER_H
