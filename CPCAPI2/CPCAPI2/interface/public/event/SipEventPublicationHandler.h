#pragma once

#if !defined(CPCAPI2_SIP_EVENT_PUBLICATION_HANDLER_H)
#define CPCAPI2_SIP_EVENT_PUBLICATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace SipEvent
{
typedef unsigned int SipEventPublicationHandle;

enum SipPublicationFailureReason
{
   SipPublicationFailureReason_Unknown                   = 1100,
   SipPublicationFailureReason_ServerError               = 1110,
   SipPublicationFailureReason_ConditionalRequestFailed  = 1120
};

/**
* Event passed in SipEventPublicationHandler::onPublicationSuccess().
 */
struct PublicationSuccessEvent
{
};

/**
* Event passed in SipEventPublicationHandler::onPublicationFailure().
 */
struct PublicationFailureEvent
{
   SipPublicationFailureReason      reason;
};

/**
* Event passed in SipEventPublicationHandler::onPublicationRemove().
 */
struct PublicationRemoveEvent
{
};

/**
* Event passed in SipEventPublicationHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct PublicationErrorEvent
{
   cpc::string errorText;
};

struct PublicationManagerReadyEvent
{
};

/**
* Handler for events relating to PUBLISH-related functionality (RFC 3903); 
* set in SipEventPublicationManager::setHandler();
* if you are implementing SipPresencePublicationHandler, 
* you should not use this SipEventPublicationHandler to handle "presence" events.
*/
class SipEventPublicationHandler
{
public:
   virtual int onPublicationSuccess(SipEventPublicationHandle publication, const PublicationSuccessEvent& args) = 0;
   virtual int onPublicationFailure(SipEventPublicationHandle publication, const PublicationFailureEvent& args) = 0;
   virtual int onPublicationRemove(SipEventPublicationHandle publication, const PublicationRemoveEvent & args) = 0;
   virtual int onError(SipEventPublicationHandle publication, const PublicationErrorEvent& args) = 0;
   virtual int onReady(CPCAPI2::SipAccount::SipAccountHandle sipAccount, const PublicationManagerReadyEvent& args) {
      return kSuccess;
   }
};

}
}
#endif // CPCAPI2_SIP_EVENT_PUBLICATION_HANDLER_H
