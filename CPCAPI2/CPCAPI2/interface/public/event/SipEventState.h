#pragma once

#if !defined(CPCAPI2_SIP_EVENT_STATE_H)
#define CPCAPI2_SIP_EVENT_STATE_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

namespace CPCAPI2
{
namespace SipEvent
{

enum SipSubscriptionEndReason
{
   SipSubscriptionEndReason_Unknown      = 1200,
   SipSubscriptionEndReason_ServerError  = 1210,
   SipSubscriptionEndReason_ServerEnded  = 1220
};

enum SubscriptionType
{
   SipSubscriptionType_Incoming  = 1300,
   SipSubscriptionType_Outgoing  = 1310
};

enum SipSubscriptionState
{
   SipSubscriptionState_NotStarted           = 1400,
   SipSubscriptionState_Pending              = 1410,
   SipSubscriptionState_Active               = 1420,
   SipSubscriptionState_Terminated           = 1430,
   SipSubscriptionState_WaitingToSubscribe   = 1440
};

/**
* A struct. Used with SipEventManager and SipPublicationManager.
*/
struct SipEventState
{
   cpc::string                           mimeType;
   cpc::string                           mimeSubType;
   cpc::string                           eventPackage;
   cpc::string                           contentUTF8;
   unsigned int                          contentLength;
   unsigned int                          expiresTimeMs;

   SipEventState()
   {
      mimeType = "";
      mimeSubType = "";
      eventPackage = "";
      contentUTF8 = "";
      contentLength = 0;
      expiresTimeMs = 0;
   }
};

/**
* A struct. Used with SipEventManager for resource list processing.
*/
struct SipEventResourceInstance
{
   cpc::string id;
   SipSubscriptionState subscriptionState;
   cpc::string mimeType;
   cpc::string mimeSubType;
   cpc::string contentUtf8;
};

/**
* A struct. Used with SipEventManager for resource list processing.
*/
struct SipEventResource
{
   cpc::string uri;
   cpc::vector<cpc::string> names;
   cpc::vector<SipEventResourceInstance> instances;
};

}
}
#endif // CPCAPI2_SIP_EVENT_STATE_H
