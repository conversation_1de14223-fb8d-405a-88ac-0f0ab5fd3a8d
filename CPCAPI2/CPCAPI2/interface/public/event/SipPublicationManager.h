#pragma once

#if !defined(CPCAPI2_SIP_PUBLICATION_MANAGER_H)
#define CPCAPI2_SIP_PUBLICATION_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipEventState.h"

namespace CPCAPI2
{
class Phone;

namespace SipEvent
{
class SipEventPublicationHandler;

typedef unsigned int SipEventPublicationHandle;

struct SipEventPublicationSettings
{
   cpc::string                     eventPackage;
   cpc::vector<Parameter>          eventPackageParams;
   unsigned int                     expiresSeconds;
   cpc::vector<CPCAPI2::MimeType>   supportedMimeTypes;
   bool                             ignoreFailures;

   SipEventPublicationSettings()
   {
      expiresSeconds = 3600;
      ignoreFailures = false;
   }
};

/**
* Manager interface that provides PUBLISH-related functionality (RFC 3903) ; 
* get a reference to the interface using the static method getInterface(); 
* see also SipEvent::SipPresenceManager, which is a more 
* specific interface for handling presence events. 
* This generic interface supports publication of any event package: 
* handling of the content in the event state notifications is left to the application code. 
* We recommend that you not use this manager for “presence” events; instead, use 
* SipEvent::SipPresenceManager.
*/
class CPCAPI2_SHAREDLIBRARY_API SipEventPublicationManager
{
public:
   /**
   * Get a reference to the %SipPublicationManager interface.
   */   
   static SipEventPublicationManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as SipAccountManager::process(..)
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& eventType,
         SipEventPublicationHandler* handler) = 0;

   /**
   * Allocates a new publication context within the SDK.  This function is used in concert with setTarget(..)
   * to set up a publication prior to calling publish(..).
   */
   virtual SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings) = 0;

   /**
   * Sets the address identifying the party for which event state will be published.
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int setTarget(SipEventPublicationHandle publication, const cpc::string& targetAddress) = 0;

   /**
   * Sends an outgoing PUBLISH request to publish initial event state or to refresh event state
   * for the publication.
   */
   virtual int publish(SipEventPublicationHandle publication, const SipEventState& eventState) = 0;

   /**
   * Removes event state for the publication per section 4.5 of RFC 3903.
   */
   virtual int end(SipEventPublicationHandle publication) = 0;

   /**
   * Used to refresh an outgoing (client) publication session by sending a PUBLISH to the remote participant or
   * to the event/resource-list server.
   * @param publication The outgoing publication to refresh.
   */
   virtual int refresh(SipEventPublicationHandle publication) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %SipEventPublicationManager.
    */
   virtual ~SipEventPublicationManager() {}
};

}
}
#endif // CPCAPI2_SIP_PUBLICATION_MANAGER_H
