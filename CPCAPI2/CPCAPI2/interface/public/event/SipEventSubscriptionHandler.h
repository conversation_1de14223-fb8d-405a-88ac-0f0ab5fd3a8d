#pragma once

#if !defined(CPCAPI2_SIP_EVENT_SUBSCRIPTION_HANDLER_H)
#define CPCAPI2_SIP_EVENT_SUBSCRIPTION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipEventState.h"

namespace CPCAPI2
{
namespace SipEvent
{
typedef unsigned int SipEventSubscriptionHandle;

/**
 * Event passed in SipEventSubscriptionHandler::onNewSubscription().
 * An outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
 * an outgoing SUBSCRIBE has forked, OR
 * an incoming SUBSCRIBE has been received.
 */
struct NewSubscriptionEvent
{
   SubscriptionType                      subscriptionType;
   cpc::string                           remoteAddress;
   cpc::string                           remoteDisplayName;
   cpc::string                           eventPackage;
   cpc::vector<Parameter>                eventPackageParams;
   cpc::vector<CPCAPI2::MimeType>        supportedMimeTypes;
   CPCAPI2::SipAccount::Sip<PERSON><PERSON>untHandle account;
};

/**
 * Event passed in SipEventSubscriptionHandler::onSubscriptionEnded(). 
 * Indicates the client or server subscription session has terminated.
 */
struct SubscriptionEndedEvent
{
   SipSubscriptionEndReason      endReason;
   SubscriptionType              subscriptionType;
   int                           statusCode; // Error code
   int                           retryAfter; // value of Retry-Header if present
   bool                          initialSubscribe; // Initial Subscribe failed
   bool                          isNotifyTerminated; // Terminated because of NOTIFY
   cpc::string                   remoteAddress; // Buddy address
   cpc::string                   reason; // subscription end reason
};

/**
 * Event passed in SipEventSubscriptionHandler::onIncomingEventState().
 * Indicates an incoming NOTIFY was received.
 */
struct IncomingEventStateEvent
{
   SipEventState          eventState;
   cpc::vector<Parameter> eventPackageParams;
};

/**
* Event passed in SipEventSubscriptionHandler::onIncomingResourceList().
* Indicates an incoming eventlist from a resource list server subscription(RFC 4662).
*/
struct IncomingResourceListEvent
{
   cpc::string uri;
   int version;
   bool fullState;
   cpc::vector<SipEventResource> resources;
};

/**
 * Event passed in SipEventSubscriptionHandler::onSubscriptionStateChanged().
 * The state of the incoming or outgoing subscription has changed.
 * This might happen for any of the following reasons:
 * <ul>
 * <li>The remote party accepted the subscription request
 * <li>The remote party rejected or terminated the subscription
 * <li>The event server is transitioning to an out-of-service state
 * </ul>
 */
struct SubscriptionStateChangedEvent
{
   SipSubscriptionState    subscriptionState;
};

/**
 * Event passed in SipEventSubscriptionHandler::onNotifySuccess().
 * An outgoing NOTIFY request was accepted (200 OK) by the remote party.
 */
struct NotifySuccessEvent
{
};

/**
 * Event passed in SipEventSubscriptionHandler::onNotifyFailure().
 * An outgoing NOTIFY request was rejected by the remote party.
 */
struct NotifyFailureEvent
{
   int sipResponseCode;
};

/**
* Event passed in SipEventSubscriptionHandler::onError(); 
* used to report general SDK error conditions, such as invalid handles, or cases
* where the subscription is not in a valid state for the requested
* operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* Handler for events relating to SUBSCRIBE-related functionality (RFC 3265); 
* set in SipEventManager::setHandler(). If you are implementing 
* SipPresenceSubscriptionHandler, you should not use this SipEventSubscriptionHandler 
* to handle "presence" events.
*/
class SipEventSubscriptionHandler
{
public:
   /**
    * Notifies the application when an outgoing SUBSCRIBE has been submitted to the SIP stack for transmission, OR
    * an outgoing SUBSCRIBE has forked, OR
    * an incoming SUBSCRIBE has been received.
    */
   virtual int onNewSubscription(SipEventSubscriptionHandle subscription, const NewSubscriptionEvent& args) = 0;
   
   /**
    * Notifies the application when the client or server subscription session has terminated.
    */
   virtual int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const SubscriptionEndedEvent& args) = 0;
   
   /**
    * Notifies the application that an incoming NOTIFY was received.
    */
   virtual int onIncomingEventState(SipEventSubscriptionHandle subscription, const IncomingEventStateEvent& args) = 0;
   
   /**
    * Notifies the application that an incoming eventlist from a resource list server subscription(RFC 4662) has been received.
    */
   virtual int onIncomingResourceList(SipEventSubscriptionHandle subscription, const IncomingResourceListEvent& args) = 0;
   
   /**
    * Notifies the application when the subscription state changes, either from the remote party accepting the subscription request,
    * or the remote party rejecting or terminating the subscription.
    */
   virtual int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const SubscriptionStateChangedEvent& args) = 0;

   /**
    * Notifies the application when an outgoing NOTIFY request was accepted (200 OK) by the remote party.
    */
   virtual int onNotifySuccess(SipEventSubscriptionHandle subscription, const NotifySuccessEvent& args) = 0;
   
   /**
    * Notifies the application when an outgoing NOTIFY request was rejected by the remote party.
    */
   virtual int onNotifyFailure(SipEventSubscriptionHandle subscription, const NotifyFailureEvent& args) = 0;

   /**
    * Used to report general SDK error conditions, such as invalid handles, or to report cases where the subscription is not in a valid state
    * for the requested operation
    */
   virtual int onError(SipEventSubscriptionHandle subscription, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_SIP_EVENT_SUBSCRIPTION_HANDLER_H
