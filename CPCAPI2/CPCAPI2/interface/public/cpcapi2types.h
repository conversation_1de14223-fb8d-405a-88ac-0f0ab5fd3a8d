#pragma once
#if !defined(CPCAPI2_COMMON_TYPES_H)
#define CPCAPI2_COMMON_TYPES_H

#include "cpcstl/string.h"

namespace CPCAPI2
{
/**
* A struct.
*/
struct NameAddress
{
   cpc::string displayName;
   cpc::string address;
};

/**
* A struct.
*/
struct MimeType
{
   cpc::string mimeType;
   cpc::string mimeSubType;

   MimeType(const cpc::string& type, const cpc::string& subType) : mimeType( type ), mimeSubType( subType ) {}
};

/**
* Parameter name and value.
* Typically used for extracting parameters from SIP headers or
* settings parameters in SIP headers.
*/
struct Parameter
{
   cpc::string name;
   cpc::string value;

   Parameter(const cpc::string& name, const cpc::string& value) : name(name), value(value) {}
};

/**
* Header name and value.
* Typically used for extracting parameters from SIP headers or
* settings parameters in SIP headers.
*/
struct SipHeader
{
   cpc::string header;
   cpc::string value;
};

struct DialogId
{
   cpc::string callId;
   cpc::string localTag;
   cpc::string remoteTag;
};

} // namespace CPCAPI2

#endif // CPCAPI2_COMMON_TYPES_H
