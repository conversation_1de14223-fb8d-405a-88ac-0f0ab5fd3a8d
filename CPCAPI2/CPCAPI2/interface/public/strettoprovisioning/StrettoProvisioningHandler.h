#pragma once

#ifndef CPCAPI2_STRETTOPROVISIONINGHANDLER_H
#define CPCAPI2_STRETTOPROVISIONINGHANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "StrettoProvisioningTypes.h"
#include <stdint.h>

namespace CPCAPI2
{
   class Phone;

   namespace StrettoProvisioning
   {
      /**
       * Event passed in StrettoProvisioningHandler::onError().
       */
      typedef struct ErrorEvent
      {
         int errNo; // as defined in errno.h
         cpc::string errorText;

         ErrorEvent() : errNo( 0 ) {} // default ctor
      } ErrorEvent;

      /**
       * Event which contains the StrettoProvisioning document.
       */
      typedef struct StrettoProvisioningEvent
      {
         cpc::string document;
      } StrettoProvisioningEvent;

      /**
       * A bit more detail about what kind of error has happened
       */
      typedef enum StrettoProvisioningError
      {
         StrettoProvisioningError_None,      // No error (default case)
         StrettoProvisioningError_Resolve,   // DNS resolution failure
         StrettoProvisioningError_Connect,   // Server unreachable
         StrettoProvisioningError_Cert,      // Cert verification failed
         StrettoProvisioningError_Auth,      // User authentication failed on server
         StrettoProvisioningError_HTTP,      // HTTP Error
         StrettoProvisioningError_Malformed  // Bad/missing document
      } StrettoProvisioningError;

      /**
       * Event which is fired when there is an unrecoverable error while
       * trying to retrieve the StrettoProvisioning document from the server.
       */
      typedef struct StrettoProvisioningErrorEvent
      {
         StrettoProvisioningError errorType;

         /**
          * If errorType is StrettoProvisioningerror_HTTP, the matching response code.
          * This code will be zero if errorType is anything else.
          */
         unsigned int httpResponseCode;

         StrettoProvisioningErrorEvent() : errorType( StrettoProvisioningError_None ), httpResponseCode( 0 ) {} // ctor
      } StrettoProvisioningErrorEvent;

      /**
       * The handler for events on the StrettoProvisioning Service
       */
      class StrettoProvisioningHandler
      {
      public:
         /**
          * Called when the provisioning request succeeds.
          */
         virtual int onProvisioningSuccess( const StrettoProvisioningHandle& handle, const StrettoProvisioningEvent& event ) = 0;
         /**
          * Event which is fired when there is an unrecoverable error while trying to retrieve the StrettoProvisioning document
          * from the server.; e.g. invalid credentials, could not connect to server.
          */
         virtual int onProvisioningError( const StrettoProvisioningHandle& handle, const StrettoProvisioningErrorEvent& event ) = 0;
         /**
          * Called when a general error has occured.
          */
         virtual int onError( const StrettoProvisioningHandle& handle, const ErrorEvent& event ) = 0;
      };
   }
}
#endif // CPCAPI2_STRETTOPROVISIONINGHANDLER_H
