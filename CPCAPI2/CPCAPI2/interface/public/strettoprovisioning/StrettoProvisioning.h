#pragma once

#ifndef CPCAPI2_STRETTOPROVISIONING_H
#define CPCAPI2_STRETTOPROVISIONING_H

#include "cpcapi2defs.h"
#include "StrettoProvisioningTypes.h"
#include "StrettoProvisioningHandler.h"

namespace CPCAPI2
{
   class Phone;

   namespace StrettoProvisioning
   {
      /**
       * Provides Stretto Provisioning related functionality.
       * In other words, can be used to retrieve settings for a particular user
       * (e.g. SIP account settings) from Stretto.
       */
      class CPCAPI2_SHAREDLIBRARY_API StrettoProvisioning
      {
      public:
         /**
          * Get a reference to the StrettoProvisioning interface.
          */
         static StrettoProvisioning* getInterface(Phone* cpcPhone);

         /**
          * Creates a new handle that will be used for provisioning operations.
          */
         virtual StrettoProvisioningHandle create( void ) = 0;

         /**
          * Configure the settings for a particular handle.
          * Call configureSettings() and then applySettings().
          *
          * @param handle the provisioning handle.
          * @param settings the structure that holds 
          *        configuration information for the notification service.
          */
         virtual int configureSettings(
            const StrettoProvisioningHandle& handle,
            const StrettoProvisioningSettings& settings ) = 0;

         /**
          * Apply the handle settings configured with
          * configureDefaultAccountSettings()
          *
          * @param handle the provisioning handle
          */
         virtual int applySettings( const StrettoProvisioningHandle& handle ) = 0;

         /**
          * Request the StrettoProvisioning document from the server.
          */
         virtual int request( const StrettoProvisioningHandle& handle ) = 0;

         /**
          * Deletes a StrettoProvisioning instance, uses the handle obtained from create.
          */
         virtual int destroy( const StrettoProvisioningHandle& handle ) = 0;

         /**
          * Set the handler for events on the specified handle. Set the
          * handler immediately after calling StrettoProvisioning::create()
          * and StrettoProvisioning::applySettings(..)
          *
          * To un-register the handler, pass NULL for handler. Must be
          * called on the same thread as process(..).
          *
          * NB: Calling setHandler with NULL as the handler will result in
          * the calling thread being blocked until the handler has been
          * removed from the system.
          */
         virtual int setHandler( const StrettoProvisioningHandle& handle,
            StrettoProvisioningHandler* handler) = 0;

         /**
          * The blocking modes for the process() function. See that function
          * for details.
          */
         static const int kBlockingModeNonBlocking = -1;
         static const int kBlockingModeInfinite = 0;

         /**
          * Allows the application code to receive callback notifications
          * from the SDK.
          *
          * These callbacks will happen synchronously, in the same thread of
          * execution as that in which %process() is invoked.  Depending on the
          * application threading model, process() can be used in two different
          * ways:
          * <ol>
          * <li>blocking mode ?Typically in this mode, %process() is called by
          * the application from a background (worker) thread.  The call to
          * process() blocks until a callback function needs to be invoked.
          * <li>non-blocking mode ?In this mode, %process() is called by the
          * application from the main (GUI) thread of the application,
          * typically from the main message/event loop.  In this mode,
          * %process() returns immediately and so must be called frequently
          * enough that the application can receive its callback notifications
          * in a timely manner.
          *
          * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
          *                representing the time the call to process(..) will block waiting for a callback
          *                from the SDK
          * </ol>
          */
         virtual int process(unsigned int timeout) = 0;

         /**
          * Posts a functor to the SDK callback queue; this allows the
          * application to "unblock" the thread calling
          * StrettoProvisioning::process(..) if it is blocked waiting for an
          * SDK callback.  Unblocking the process() thread is useful at
          * SDK/application shutdown in certain applications.
          */
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) = 0;
         
      protected:
         /*
          * The SDK will manage memory life of %StrettoProvisioning.
          */
         virtual ~StrettoProvisioning() {}
      };
   }
}
#endif // CPCAPI2_STRETTOPROVISIONING_H
