#pragma once
#ifndef __CPCAPI2_STRETTOPROVISIONINGTYPES_H__
#define __CPCAPI2_STRETTOPROVISIONINGTYPES_H__

#include <cpcstl/string.h>

namespace CPCAPI2
{
   namespace StrettoProvisioning
   {
      typedef unsigned int StrettoProvisioningHandle;

      /**
       * Generic service authentication / authorization info
       */
      typedef struct AuthInfo
      {
         /*
          * SPID (Service Provider ID). Can be left blank if users will login via username@group
          * Can be filled in with value of group if users will instead login via just username
          */
         cpc::string spid;

         /*
          * Login username that <PERSON><PERSON><PERSON> will authenticate against and pull relevent provisioning data for          
          */
         cpc::string userName;

         /*
          * Login password that <PERSON><PERSON><PERSON> with authenticate against.
          */
         cpc::string password;
      } AuthInfo;

      typedef struct BuildInfo
      {
         /**
          * A version of the application, again usually in dotted decimal notation.
          * Also includes an optional build number on the end. for example
          * "1.2.3.12345"
          */
         cpc::string versionString;
      } BuildInfo;

      /**
       * Structure containing all relevant settings for the channel.
       * (subdivided into "info" blocks).
       */
      typedef struct StrettoProvisioningSettings
      {
         AuthInfo    authInfo;
         BuildInfo   buildInfo;

         /**
          * If set to true (default) the JSON protocol content will be
          * written to the logfile at "Info" level. Otherwise if the
          * application is not interested in the raw JSON content, set this
          * to false.
          */
         bool logJSONPayload;

         // Default ctor with reasonable values
         StrettoProvisioningSettings()
         {
            logJSONPayload = true;
         }
         
         virtual ~StrettoProvisioningSettings() {}
         
      } StrettoProvisioningSettings;

   }
}

#endif //  __CPCAPI2_STRETTOPROVISIONINGTYPES_H__
