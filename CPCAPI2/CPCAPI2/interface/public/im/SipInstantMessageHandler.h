#pragma once

#if !defined(CPCAPI2_INSTANT_MESSAGE_HANDLER_H)
#define CPCAPI2_INSTANT_MESSAGE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "../iscomposing/IsComposingTypes.h"
#include "SipInstantMessage.h"

#include <ctime>

namespace CPCAPI2
{
namespace SipInstantMessage
{

/**
* Event passed in SipInstantMessageHandler::onIncomingInstantMessage().
* Contains information about an incoming IM. The next step must
* be to call SipInstantMessage::acceptIncoming() or rejectIncoming(),
* in both cases passing in the handle that is passed in this event.
*
* @param im The handle for this particular IM transaction
* @param to The recipient of the message
* @param from The sender of the message
* @param mimeType The mime type
* @param content UTF-8 encoded copy of the contents; NOTE: applications must memcpy the contents
* @param contentLength the length in bytes of the content of the MESSAGE, taken from the Content-Length header
* @param messageType type of message received e.g "IM", "SMS".
*/
struct SipIncomingInstantMessageEvent
{
   SipInstantMessageHandle               im;
   NameAddress                           to;
   NameAddress                           from;
   cpc::string                           mimeType;
   cpc::string                           content;
   CPCAPI2::SipAccount::SipAccountHandle account;
   cpc::string                           messageType;
   cpc::vector<CPCAPI2::SipHeader>       nonStandardHeaders;
};

/**
* Contains information about the success or failure of an outgoing IM.
* <li>accountStatus: The handle for the specific IM.
* <li>signalingStatusCode: The standard SIP code for the success (e.g. 200) or
* failure (e.g. 404)
* <li>signalingResponseText: The text description of the SIP code, for example,
* "OK".
*/

struct SipOutgoingInstantMessageHeaders
{
   cpc::vector<cpc::string> acceptableMimeTypes;
};

/**
* Event passed in SipInstantMessageHandler::onOutgoingInstantMessageFailure().
*/

struct SipOutgoingInstantMessageEvent
{
   SipInstantMessageHandle im;
   unsigned int            signalingStatusCode;
   cpc::string            signalingResponseText;
   SipOutgoingInstantMessageHeaders headers;
};

/**
 * Event fired by the SDK when the other participant of the
 * chat session is composing a new message.
 *
 * @param state The state of the 'isComposing' indicator
 * @param to The recipient of the message
 * @param from The sender of the message
 * @param mimeType MIME type of the message being composed
 * @param lastActive date/time the 'isComposing' indicator was last active
 */
struct IsComposingMessageEvent
{
   IsComposing::IsComposingMessageState state;
   NameAddress to;
   NameAddress from;
   cpc::string mimeType;
   struct std::tm lastActive;
};

/**
 * Event fired by the SDK when a 'message being composed' notification
 * was successfully sent.
 *
 * @param state The state of the 'isComposing' indicator
 */
struct SetIsComposingMessageSuccessEvent
{
   IsComposing::IsComposingMessageState state;
};

/**
 * Event fired by the SDK when a 'message being composed' notification
 * failed sending.
 *
 * @param state The state of the 'isComposing' indicator
 */
struct SetIsComposingMessageFailureEvent
{
};

/**
* Event passed in SipInstantMessageHandler::onError();
* used to report general SDK error conditions, such as invalid handles, or cases
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};


/**
 * Handler for events relating to incoming and outgoing
 * SIP instant messages; set in SipInstantMessageManager::setHandler().
 */
class SipInstantMessageHandler
{
public:
   /**
   * Notifies that an IM has been received on the specified account.
   */
   virtual int onIncomingInstantMessage(CPCAPI2::SipAccount::SipAccountHandle account, const SipIncomingInstantMessageEvent& args) = 0;

   /**
   * Event passed in SipInstantMessageHandler::onOutgoingInstantMessageSuccess().
   * Notifies that an outgoing IM has been successfully delivered to the recipient.
   * @param account The %SipAccount that was used to deliver the IM
   * @param args Contains details about the success of the event
   * which should always be 200 OK or 202 Accepted).
   */
   virtual int onOutgoingInstantMessageSuccess(CPCAPI2::SipAccount::SipAccountHandle account, const SipOutgoingInstantMessageEvent& args) { return kSuccess; }

   /**
   * Notifies that delivery of an outgoing IM has failed.
   * @param account The %SipAccount that was used to deliver the IM
   * @param args Contains details about the failure of the event.
   */
   virtual int onOutgoingInstantMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SipOutgoingInstantMessageEvent& args) { return kSuccess; }

   /**
    * Callback invoked by the SDK when the other participant is composing a new message.
    *
    * @param account The %SipAccount that was used to receive the 'isComposing' notification.
    * @param args information about the message being composed.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onIsComposingMessage(CPCAPI2::SipAccount::SipAccountHandle account, const IsComposingMessageEvent& args) { return kSuccess; }

   /**
    * Callback invoked by the SDK when a 'message being composed' notification was successfuly sent.
    *
    * @param account The %SipAccount used.
    * @param args information about the message being composed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSetIsComposingMessageSuccess(CPCAPI2::SipAccount::SipAccountHandle account, const SetIsComposingMessageSuccessEvent& args) { return kSuccess; }

   /**
    * Callback invoked by the SDK when a 'message being composed' notification failed sending.
    *
    * @param account The %SipAccount used.
    * @param args information about the message being composed notification.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onSetIsComposingMessageFailure(CPCAPI2::SipAccount::SipAccountHandle account, const SetIsComposingMessageFailureEvent& args) { return kSuccess; }

   /**
    * Invoked by the SDK for errors which do not fall into the categories outlined above.
    *
    * @param account The %SipAccount used.
    * @param args information about the error.
    *
    * return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& args) = 0;
};

}
}
#endif // CPCAPI2_INSTANT_MESSAGE_HANDLER_H
