#pragma once

#if !defined(CPCAPI2_INSTANT_MESSAGE_H)
#define CPCAPI2_INSTANT_MESSAGE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

#include <ctime>

namespace CPCAPI2
{
class Phone;

namespace SipInstantMessage
{
class SipInstantMessageHandler;
typedef unsigned int SipInstantMessageHandle;

/**
* Manager interface to control incoming and outgoing
* SIP SIMPLE instant messages; get a reference to the interface
* using the static method getInterface(); typically
* you create only one %SipInstantMessage.
*
* To create an outgoing IM, use sendMessage(); the ensuing SipInstantMessageHandler
* event will pass a %SipInstantMessageHandle.
*
* To handle all incoming IMs, obtain the SipInstantMessageHandle from the
* SipInstantMessageHandler incoming event, then call acceptIncoming()
* and rejectIncoming(), passing in the handle.
*/
class CPCAPI2_SHAREDLIBRARY_API SipInstantMessageManager
{
public:
   /**
   * Get a reference to the %SipInstantMessage interface.
   */
   static SipInstantMessageManager* getInterface(Phone* cpcPhone);

   /**
   * Holds values for the type of an outgoing IM; used in sendMessage().
   */
   enum MimeType
   {
      MimeType_TextPlain   = 1,
      MimeType_TextHtml    = 2,
      MimeType_Custom      = 3
   };

   /**
   * Set the handler for incoming and outgoing instant messages.
   *
   * To un-register the handler, pass NULL for handler. Must be
   * called on the same thread as SipAccount::process(..)
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipInstantMessageHandler* handler) = 0;

   /**
   * Indicates that only a specific set of mime types is accepted and adds an accepted mime type
   * This function can be called multiple times to add mime types.
   * The default behavior if this is never called is to process all mime types.
   * Mime types can be rejected at the instant message handler by calling
   * rejectIncomingMimeType(...) inside onIncomingInstantMessage
   * @param mimeType the mime type (such as text/xml) which is acceptable
   */
   virtual int acceptMimeType(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& mimeType) = 0;
   virtual int acceptMimeType(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const MimeType mimeType) = 0;

   /**
   * Accept the incoming message identified by the %SipInstantMessageHandle.
   *
   * @param im The handle that was passed in
   * SipInstantMessageHandler::onIncomingInstantMessage()
   * @param statusCode The applicable  code, typically 200
   */
   virtual int acceptIncoming(
         SipInstantMessageHandle im,
         unsigned int statusCode = 200) = 0;

   /**
   * Reject the incoming message identified by the %SipInstantMessageHandle.
   *
   * @param im The handle that was passed in
   * SipInstantMessageHandler::onIncomingInstantMessage()
   * @param statusCode The applicable  code, for example, azj - all the reject codes look like they would apply to the proxy, not to the client. can you think of any example to put in here
   * @param reasonText The text description of the  code, for example, clb
   */
   virtual int rejectIncoming(
         SipInstantMessageHandle im,
         unsigned int statusCode,
         const cpc::string& reasonText) = 0;

  /**
   * Reject the incoming message with an unrecognized mime type.
   * A 415 Unsupported media type response will be sent with the provided list of acceptable mime-types
   *
   * @param im The handle that was passed in
   * SipInstantMessageHandler::onIncomingInstantMessage()
   * @param acceptMimeTypes The list of mime types which are acceptable
   */
   virtual int rejectIncomingMimeType(
         SipInstantMessageHandle im,
         const cpc::vector<cpc::string>& acceptMimeTypes) = 0;

   /**
   * Send an instant message to the specified URI, using the specified account.
   * @param account The %Account to use for delivery
   * @param targetAddress The fully formatted address of the recipient,
   * <NAME_EMAIL> or <EMAIL>
   * @param content The body of the IM; UTF-8 encoded.
   * @param contentLength The length of the content of the MESSAGE, in bytes; will
   * be placed in the Content-Length header
   * @param mimeType A value from this enum
   * @param messageType the type of message to send e.g "IM", "SMS", defaults to none.
   */
   virtual SipInstantMessageHandle sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         MimeType mimeType,
         const cpc::string& messageType = "") = 0;

   /**
   * Send an instant message to the specified URI, using the specified account.
   * @param account The %Account to use for delivery
   * @param targetAddress The fully formatted address of the recipient,
   * <NAME_EMAIL> or <EMAIL>
   * @param content The body of the IM; UTF-8 encoded.
   * @param contentLength The length of the content of the MESSAGE, in bytes; will
   * be placed in the Content-Length header
   * @param customMimeType A custom mime-type value to be used
   * @param messageType the type of message to send e.g "IM", "SMS", defaults to none.
   */
   virtual SipInstantMessageHandle sendMessage(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const cpc::string& targetAddress,
         const char* content,
         unsigned int contentLength,
         const cpc::string& customMimeType,
         const cpc::string& messageType = "") = 0;

   /**
    * Indicates that a new message is being typed/composed.
    *
    * Notes:
    *
    * 1) It does not necessarily mean that a notification will be sent to the other
    *    participant of the IM session. It depends on the internal state and timers.
    *
    * 2) However, this method must be called often while the participant is actively
    *    typing/composing the message in order to maintain an accurate internal state.
    *
    * @param account The %Account to use.
    * @param targetAddress The fully formatted address of the recipient.
    * @param mimeType the MIME type of the message being composed.
    * @param datetime the date/time at which the message is being composed, defaults to the current date/time if not specified.
    * @param refreshInterval the refresh interval to use (in seconds), defaults to 90 seconds.
    * @param idleInterval the idle interval to use (in seconds), defaults to 15 seconds.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setIsComposingMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      MimeType mimeType,
      struct std::tm* datetime = 0,
      int refreshInterval = 90,
      int idleInterval = 15) = 0;

   /**
    * Indicates that a new message is being typed/composed.
    *
    * Notes:
    *
    * 1) It does not necessarily mean that a notification will be sent to the other
    *    participant of the IM session. It depends on the internal state and timers.
    *
    * 2) However, this method must be called often while the participant is actively
    *    typing/composing the message in order to maintain an accurate internal state.
    *
    * @param account The %Account to use.
    * @param targetAddress The fully formatted address of the recipient.
    * @param customMimeType the custom MIME type of the message being composed.
    * @param datetime the date/time at which the message is being composed, defaults to the current date/time if not specified.
    * @param refreshInterval the refresh interval to use (in seconds), defaults to 90 seconds.
    * @param idleInterval the idle interval to use (in seconds), defaults to 15 seconds.
    *
    * @return kSuccess if the operation was successful, kError otherwise.
    */
   virtual int setIsComposingMessage(
      CPCAPI2::SipAccount::SipAccountHandle account,
      const cpc::string& targetAddress,
      const cpc::string& customMimeType,
      struct std::tm* datetime = 0,
      int refreshInterval = 90,
      int idleInterval = 15) = 0;

protected:
   /*
    * The SDK will manage memory life of %SipInstantMessageManager.
    */
   virtual ~SipInstantMessageManager() {}
};

}
}
#endif // CPCAPI2_INSTANT_MESSAGE_H
