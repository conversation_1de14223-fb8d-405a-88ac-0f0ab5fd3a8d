#pragma once

#if !defined(CPCAPI2_ACCOUNT_H)
#define CPCAPI2_ACCOUNT_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipAccountSettings.h"

namespace CPCAPI2
{

class Phone;

namespace SipAccount
{

class SipAccountHandler;
class SipAccountAdornmentHandler;

typedef unsigned int SipAccountHandle;


/**
 * Main entry point for SIP-related functionality; each %SipAccount 
 * represents one instance of a SIP user agent; create as many accounts 
 * as you need by getting a reference to the interface 
 * using the static method getInterface() then
 * by calling the static method %SipAccount::create().
 *
 * This class allows for creation and management of 
 * SIP transports and registration sessions:
 * use this interface to REGISTER with your SIP proxy/registrar.
 * 
 * Other SDK modules depend on the SipAccount module and cannot function unless
 * it has been created and successfully enabled. 
 *
 * Usage:
 * <ol>
 *    <li> Get a reference to the SipAccount module using SipAccountManager::getInterface(phone)
 *    <li> Create the SipAccount using SipAccountManager::create(accountSettings)
 *    <li> Set an account handler using SipAccountManager::setHandler(..)
 *    <li> Enable the account using SipAccountManager::enable(..)
 * </ol>
 */
class CPCAPI2_SHAREDLIBRARY_API SipAccountManager
{

public:

   /**
   * Get a reference to the SipAccount interface.
   */
   static SipAccountManager* getInterface(Phone* cpcPhone);

   /**
   * Create an account.
   * @param SipAccountSettings The SipAccountSettings that holds 
   * configuration information for this account.
   */
   virtual CPCAPI2::SipAccount::SipAccountHandle create() = 0;

   /**
   * DEPRECATED - do not use
   * Instead, use create() and then call applySettings(..)
   */
   virtual CPCAPI2::SipAccount::SipAccountHandle create(const SipAccountSettings& sipAccountSettings) = 0;

   /**
   * Configure the default account settings.  These settings are used unless overridden by network-transport-specific settings.
   * Call configureDefaultAccountSettings(), configureTransportAccountSettings() (optional), and then applySettings().
   *
   * @param account the account handle.
   * @param sipAccountSettings the SipAccountSettings that holds 
   *                           configuration information for this account.
   */
   virtual int configureDefaultAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings) = 0;

   /**
    * Configure transport-specific account settings.  These settings override the default account settings, and take effect when the NetworkChangeManager detects that
    * network connectivity has changed to the specified transport type.
    * Call configureDefaultAccountSettings(), configureTransportAccountSettings() (optional), and then applySettings().
    *
    * @param account the account handle.
    * @param sipAccountSettings the SipAccountSettings that holds
    *                           configuration information for this account.
    * @param transport the network transport to associate configuration with.
    */
   virtual int configureTransportAccountSettings(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountSettings& sipAccountSettings, CPCAPI2::NetworkTransport transport) = 0;

   /**
    * Apply the account settings configured with configureDefaultAccountSettings(), configureTransportAccountSettings()
    *
    * @param account the account handle
    */
   virtual int applySettings(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Deletes an account, uses the handle obtained from create.
   */
   virtual int destroy(CPCAPI2::SipAccount::SipAccountHandle handle) = 0;

   /**
   * Set the handler for events on the specified account. Set the handler
   * immediately after calling SipAccountManager::create() and SipAccountManager::applySettings(..)
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
   */
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipAccount::SipAccountHandler* handler) = 0;

   /**
   * Enable the specified account so that it can be used to send/receive SIP messages.
   * If the SipAccountSettings.useRegistrar is true, then this function also sends
   * the initial SIP REGISTER message to the proxy/registrar configured in SipAccountSettings.
   *
   * You should use CPCAPI2::SipAccount::SipAccountHandler::onAccountStatusChanged()
   * to be notified of progress after invoking %enable().
   */
   virtual int enable(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Disable the specified account: if SipAccountSettings.useRegistrar is true, unregister it with the SIP registrar.
   * You should use CPCAPI2::SipAccount::SipAccountHandler::onAccountStatusChanged()
   * to be notified of progress after invoking %disable().
   *
   * @param account the account handle
   * @param force if false, and calls are still active, account disable operation will be deferred until all calls end. If true, account will disable
   * immediately regardless of whether any call is active.
   */
   virtual int disable(CPCAPI2::SipAccount::SipAccountHandle account, bool force = false) = 0;

   /**
   * Request a refresh of the current registration (sends a re-REGISTER request).
   * Applications should not normally need to call this, as the SDK will automatically refresh registrations before
   * they expire.
   *
   * @param deadlineSecondsFromNow Set to 0 to request an unconditional refresh. Otherwise specify a value 
   *                               to be compared against the number of seconds remaining before the next
   *                               scheduled refresh by the SDK, wherein a refresh will only be performed now
   *                               if (timeUntilNextRefresh < deadlineSecondsFromNow).
   */
   virtual int requestRegistrationRefresh(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int deadlineSecondsFromNow) = 0;

   /**
    * Sets restriction on specified network.
    *
    * @param account the account handle.
    * @param transport the network transport.
    * @param restricted restriction value
    */
   virtual int setNetworkRestriction(CPCAPI2::SipAccount::SipAccountHandle account, NetworkTransport transport, bool restricted) = 0;

   /**
    * The folowing setters will set custom values for certain SIP timers (see RFC 3261 for a full table of SIP timers.)
    * It is not necessary to set these timers; if they are not set then the default values will be used.
    * These setters must be called before any SIP account is enabled. The values set here will apply for all SIP
    * accounts.
    */

   /**
    * Sets the SIP T1 timer value (see RFC 3261)
    *
    * @param t1ValueMs the value (in milliseconds) for the T1 timer
    */
   virtual void setT1TimerValueMs(unsigned long t1ValueMs) = 0;

   /**
   * Sets the SIP T2 timer value (see RFC 3261)
   *
   * @param t2ValueMs the value (in milliseconds) for the T2 timer
   */
   virtual void setT2TimerValueMs(unsigned long t2ValueMs) = 0;

   /**
   * Sets the SIP T4 timer value (see RFC 3261)
   *
   * @param t4ValueMs the value (in milliseconds) for the T4 timer
   */
   virtual void setT4TimerValueMs(unsigned long t4ValueMs) = 0;

   /**
   * Sets the SIP TD timer value (see RFC 3261)
   *
   * @param tDValueMs the value (in milliseconds) for the TD timer
   */
   virtual void setTDTimerValueMs(unsigned long tDValueMs) = 0;

   /**
   * Sets the SIP TF timer value (see RFC 3261)
   *
   * @param tDValueMs the value (in milliseconds) for the TF timer
   */
   virtual void setTFTimerValueMs(unsigned long tFValueMs) = 0;

   /**
    * Sets the SIP total transaction timeout value.
    *
    * @param sipTotalTransactionTimeoutMs
    */
   virtual void setSipTotalTransactionTimeoutMs(unsigned long sipTotalTransactionTimeoutMs) = 0;

   /**
    * Gets the SIP T1 timer value (see RFC 3261)
    */
   virtual unsigned long getT1TimerValueMs() = 0;

   /**
   * Gets the SIP T2 timer value (see RFC 3261)
   */
   virtual unsigned long getT2TimerValueMs() = 0;

   /**
   * Gets the SIP T4 timer value (see RFC 3261)
   */
   virtual unsigned long getT4TimerValueMs() = 0;

   /**
   * Gets the SIP TD timer value (see RFC 3261)
   */
   virtual unsigned long getTDTimerValueMs() = 0;

   /**
   * Gets the SIP TF timer value (see RFC 3261)
   */
   virtual unsigned long getTFTimerValueMs() = 0;

   // timeout values

   /**
   * The blocking modes for the process() function. See that function
   * for details.
   */
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;

   // constants
   static const int kAccountModuleDisabled = -1;

   /**
    * Functionality is replaced by Phone::process()
    */
   DEPRECATED virtual int process(unsigned int timeout) = 0;

   /**
   * Posts a functor to the SDK callback queue; this allows the application to "unblock" the
   * thread calling SipAccount::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

   /**
   * DEPRECATED: Use interruptProcess() instead
   */
   DEPRECATED virtual void postToProcessThread(void (*pfun)(void*), void* obj) = 0;

   /**
   * Set the handler for call events on the specified account. Set the handler
   * immediately after creating the account.  Must be called on the same thread as process(..)
   *
   * To un-register the handler, pass NULL for handler.
   */
   virtual int setAdornmentHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipAccountAdornmentHandler* handler) = 0;

   /**
   * To be called inside your adornment handler.
   * @param account The account handle; should be the same as the account handle passed to your implementation of %SipAccount::SipAccountAdornmentHandler::onAccountAdornment
   * @param adornmentMessageId The adornment message id; should be the same as the adornment message id in %SipAccount::SipAccountAdornmentEvent as passed to adornment your callback handler 
   * @param customHeaders Header name value pairs that will be set for the SIP message being adorned
   */
   virtual int adornMessage(SipAccountHandle account, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders) = 0;

   /**
    * Decodes a provisioning response received from the Provisioning interface.
    * @param provisioningResponse The complete provisioning response, as returned by the Provisioning interface.
    * @param outSipAccountSettings A vector of SipAccountSettings; the number of items in the vector will be equivalent to the number of sets of SIP account settings in the provisioning response. If a particular setting was not provisioned down, the SDK will use a default value.
    */
   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipAccountSettings>& outSipAccountSettings) = 0;

protected:

   /* 
    * The SDK will manage memory life of %SipAccountManager.
    */
   virtual ~SipAccountManager() {}

};

}

}

#endif // CPCAPI2_ACCOUNT_H
