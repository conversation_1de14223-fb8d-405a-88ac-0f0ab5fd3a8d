#pragma once

#if !defined(CPCAPI2__ACCOUNT_SETTINGS_H)
#define CPCAPI2__ACCOUNT_SETTINGS_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <phone/NetworkChangeManager.h>
#include <phone/SslCipherOptions.h>

namespace CPCAPI2
{

struct SipResponseType
{
   cpc::string  method;
   unsigned int responseCode;
   SipResponseType() : responseCode( 0 ) {}
   SipResponseType(const cpc::string& m, unsigned int code) : method( m ), responseCode( code ) {}
};

struct SipParameterType
{
    cpc::string name;
    cpc::string value;
    SipParameterType() {}
    SipParameterType(const cpc::string& n, const cpc::string& v = "") : name( n ), value( v ) {}
};

namespace SipAccount
{
   
/**
* Holds possible values for the SipAccountTransportType parameter:
* <ul>
* <li>1: the application sets up the transport based on the 
* capabilities of the network and the the application computer;
* the preferred method will be obtained via DNS (NAPTR/srv) queries.  
* <li>2: This transport provides no signalling security.  
* <li>3: This transport provides no signalling security.  
* <li>4: Choose this option if you want to request signalling encryption.
* </ul>
*/
enum SipAccountTransportType
{
   SipAccountTransport_Auto         = 1,
   SipAccountTransport_UDP          = 2,
   SipAccountTransport_TCP          = 3,
   SipAccountTransport_TLS          = 4,
   SipAccountTransport_Unknown      = 5
};

/**
* Holds possible values for the SipAccountSessionTimerMode parameter
*/
enum SipAccountSessionTimerMode
{
   /**
   * When this flag is specified, Session Timers will not be used in any
   * session, except it is explicitly required in the remote request.
   */
   SipAccountSessionTimerMode_Inactive    = 1,

   /**
   * When this flag is specified, Session Timers will be used in all
   * sessions whenever remote supports and uses it.
   */
   SipAccountSessionTimerMode_Optional    = 2,

   /**
   * When this flag is specified, Session Timers support will be
   * a requirement for the remote to be able to establish a session.
   */
   SipAccountSessionTimerMode_Required    = 3,

   /**
   * When this flag is specified, Session Timers will always be used
   * in all sessions, regardless whether remote supports/uses it or not.
   */
   SipAccountSessionTimerMode_Always      = 4
};

/**
* Holds possible values for the StunServerSourceType parameter.
*/
enum StunServerSourceType
{
   /**
   * When this flag is specified, no STUN server will be used.
   */
   StunServerSource_None    = 0,

   /**
   * When this flag is specified, the STUN server found as a result
   * of a DNS SRV lookup on the configured domain will be used.
   */
   StunServerSource_SRV     = 1,

   /**
   * When this flag is specified, the configured STUN server will be used.
   */
   StunServerSource_Custom  = 2
};

enum IpVersion
{
   /* use only IPv4 */
   IpVersion_V4,

   /* use only IPv6 */
   IpVersion_V6,

   /* dual stack support, prefer IPv4 when available */
   IpVersion_Auto,

   /* dual stack support, prefer IPv6 when available */
   IpVersion_Auto_PreferV6
};

enum SSLVersion
{
   TLS_DEFAULT        = -1,   /** Use the value set in SslCipherOptions */
   SSL_NONE           = 0,
   SSL_V2             = 1,    /** Not supported as it is no longer deemed secure. */
   SSL_V3             = 2,    /** Not supported as it is no longer deemed secure. */
   TLS_V1_0           = 3,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_1           = 4,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_2           = 5,
   TLS_V1_3           = 6,
   SSL_HIGHEST        = 1000, /** SDK will negotiated highest supported version between client and server TLS 1.0 and above */
   TLS_NON_DEPRECATED = 1001  /** SDK will negotiated highest supported version between client and server TLS 1.2 and above */
};
   
enum KeepAliveMode
{
   KeepAliveMode_Default,
   KeepAliveMode_NoKeepAlives,
   KeepAliveMode_CRLFCRLF
};

enum TunnelType
{
   TunnelType_TSCF,
   TunnelType_StrettoTunnel,
   TunnelType_Unknown
};

enum TunnelTransportType
{
   TunnelTransport_Auto    = 0,
   TunnelTransport_UDP     = 1,
   TunnelTransport_TCP     = 2,
   TunnelTransport_TLS     = 3,
   TunnelTransport_DTLS    = 4
};

enum TunnelMediaTransportType
{
   TunnelMediaTransport_Default           = 0,
   TunnelMediaTransport_DatagramPreferred = 1,
   TunnelMediaTransport_DatagramOnly      = 2,
   TunnelMediaTransport_StreamPreferred   = 3,
   TunnelMediaTransport_StreamOnly        = 4
};

enum TransportHoldover
{
   TransportHoldover_None  = 0,
   TransportHoldover_All   = 1,
   TransportHoldover_V4    = 2,
   TransportHoldover_V6    = 3
};

enum NetworkChangeFilter
{
   NetworkChangeFilter_None  = 0,
   NetworkChangeFilter_IpVer = 1
};

struct TunnelConfig
{
   /**
   * True to use a tunnel for transporting signaling and media.
   */
   bool         useTunnel;

   /**
    * The method of tunnelling to use.
    */
   TunnelType tunnelType;

   /**
    * The address of the server to use for the tunnel.
    */
   cpc::string  server;

   /**
   * The signaling transport type to use for the tunnel.
   */
   TunnelTransportType transportType;

   /**
   * The media transport type to use for the tunnel.
   */
   TunnelMediaTransportType mediaTransportType;

   /**
   * The redundancy factor for the tunnel. Determines how many 
   * redundant tunnels should be created for single conversation. 
   * Increases conversation quality at the cost of bandwidth. Can
   * take values 0, 1 and 2. 
   */
   unsigned int redundancyFactor;

   /**
   * True to do load balancing for the tunnel.
   */
   bool         doLoadBalancing;

   /**
   * True to bypass TLS certificate verification for the tunnel
   */
   bool         ignoreCertVerification;

   /**
   * True to disable the Nagle algorithm. It is suggested to improve latency for TLS transports
   */
   bool         disableNagleAlgorithm;

   /**
   * The WebSocket URL where tunnel should be established
   */
   cpc::string  strettoTunnelURL;

   /**
   * The push notification token associcated with device
   */
   cpc::string  strettoTunnelToken;

   /**
   * The ID specified in the push notification payload
   */
   cpc::string  strettoTunnelSessionID;

   /**
   * True to setup the tunnel for connectivity test
   */
   bool         strettoTunnelTestConnection;

   /**
   * True to enable logging of transport traces
   */
   bool         logStrettoTunnelTransportTraces;
   
   
   /* 
   * True to skip the Stretto tunnel handshake;
   * should not be used under normal circumstances. */
   bool         strettoTunnelSkipHandshake;

   TunnelConfig()
   {
      useTunnel        = false;
      tunnelType       = TunnelType_Unknown;
      transportType    = TunnelTransport_Auto;
      mediaTransportType = TunnelMediaTransport_Default;
      redundancyFactor = 0;
      doLoadBalancing  = false;
      ignoreCertVerification = false;
      disableNagleAlgorithm = false;
      strettoTunnelTestConnection = false;
      logStrettoTunnelTransportTraces = true;
      strettoTunnelSkipHandshake = false;
   }

   bool operator!=(const TunnelConfig &other) const {
      return useTunnel != other.useTunnel ||
         tunnelType != other.tunnelType ||
         server != other.server ||
         transportType != other.transportType ||
         mediaTransportType != other.mediaTransportType ||
         redundancyFactor != other.redundancyFactor ||
         doLoadBalancing != other.doLoadBalancing ||
         ignoreCertVerification != other.ignoreCertVerification ||
         disableNagleAlgorithm != other.disableNagleAlgorithm ||
         strettoTunnelURL != other.strettoTunnelURL ||
         strettoTunnelToken != other.strettoTunnelToken ||
         strettoTunnelSessionID != other.strettoTunnelSessionID ||
         strettoTunnelTestConnection != other.strettoTunnelTestConnection ||
         logStrettoTunnelTransportTraces != other.logStrettoTunnelTransportTraces;
   }
};

/**
* A struct. Contains data used by the account; passed in SipAccount::create(); 
* the data includes information that allows the application to connect to the network.
*/
struct SipAccountSettings
{
   /**
   * The user portion of the SIP identity for this account, 
   * for example, the "kperera" portion of "<EMAIL>"
   */
   cpc::string                           username;

   /**
   * The  host portion of the SIP identity for this account, 
   * for example, the "zippy-phone.com" portion of "<EMAIL>"
   */
   cpc::string                           domain;

   /**
   * The password for this account at the SIP registrar.
   */
   cpc::string                           password;

   /**
   * Any display name for this account; used in SIP From headers.
   */
   cpc::string                           displayName;

   /**
   * The authorization name, if required for this account by the SIP registrar.
   */
   cpc::string                           auth_username;

   /**
   * The authorization realm, if required for this account by the SIP registrar.
   * If not specified, SipAccountSettings::domain is used.
   */
   cpc::string                           auth_realm;

   /**
   * True to send a SIP REGISTER request to the proxy/registrar.
   */
   bool                                  useRegistrar;

   /**
   * The fully qualified host name or IP
   * address of the outbound proxy. These formats are allowed:
   * <ul>
   * <li> abc.com - uses DNS SRV to resolve the _sip services at abc.com
   * <li> sip.abc.com (fully qualified) ?uses DNS A/AAAA to resolve the 
   * address of the proxy/registrar
   * <li> *************:5060 ?DNS is not used to resolve the address of 
   * the proxy/registrar
   * </ul>
   */
   cpc::string                           outboundProxy;

   /**
   * True to send all outgoing requests/reponses via the outbound proxy
   * specified in SipAccountSettings::outboundProxy.
   * Note that this should only be used for interoperability with broken
   * or incorrectly configured implementations, since it will break
   * compliance with RFC-3261 mid-dialog/target refresh request handling.
   */
   bool                                  alwaysRouteViaOutboundProxy;

   /**
   * The time interval between the application�s 
   * attempts to reregister in order to refresh the account registration. A value 
   * of 0 means not to reregister after the initial registration. This value 
   * is placed in the �Expires?header field of the register message.
   */
   unsigned int                          registrationIntervalSeconds;

   /**
   * If the registration fails, the application will atleast wait this amount of time,
   * before attempting to reregister.
   */
   unsigned int                          minimumRegistrationIntervalSeconds;
   
   /**
   * If the reregistration fails, the application keeps on increasing the registration
   * retry duration, until the duration reaches this amount of time.
   */
   unsigned int                          maximumRegistrationIntervalSeconds;

   /**
   * Set to true if your proxy is able to support rport (RFC 3581). A value of true instructs 
   * the application to include the rport parameter in the REGISTER request, 
   * with the expectation that your proxy will provide the appropriate rport 
   * value in the 200 OK. When rport is true, the local SDK will attempt SIP NAT 
   * traversal by leveraging the rport and received parameters; if the 
   * rport/received parameters do not match the values in the 
   * Contact header, the account will un-REGISTER and re-REGISTER with 
   * an updated Contact header, reflecting the discovered values.
   * Set to false if your proxy cannot support rport.
   * If enabled, STUN will be disabled.
   */
   bool                                  useRport;

   /**
   * The transport to use for signaling; enter one of the values from SipAccountTransportType.
   */
   unsigned int                          sipTransportType;

   /**
   * Exclude encrypted transports when sipTransportType == SipAccountTransport_Auto.
   */
   bool                                  excludeEncryptedTransports;

   /**
   * The name to put in the user agent in the SIP User-Agent header of outgoing traffic.
   */
   cpc::string                           userAgent;

   /**
   * Time between CR/LF keepalive messages in seconds.  Set to 0 to disable.
   */
   unsigned int                          udpKeepAliveTime;
   unsigned int                          tcpKeepAliveTime;

   /**
   * True to enable SIP Outbound support (RFC 5626)
   * If enabled, STUN will be disabled
   */
   bool                                  useOutbound;

   /**
   * True to enable GRUU support.
   */
   bool                                  useGruu;

   /**
   * Allow to add other non-escaped chars to the existing set. Applicable to Uri only.
   * Current non-escaped char set is:
   *  "abcdefghijklmnopqrstuvwxyz"
   *  "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
   *  "**********"
   *  "-_.!~*\\()&=+$,;?/"
   */
   cpc::string                           otherNonEscapedCharsInUri;

   /**
   * Optional list of name servers to be used by the SIP stack.  Used in place of any default/system nameservers
   * returned by the OS.  Specified in the format "*******" (for IPv4) or "2001:4860:4860::8888" (for IPv6).
   */
   cpc::vector<cpc::string>              nameServers;

   /**
   * Optional list of name servers to be used by the SIP stack.  Used in addition to any default/system nameservers
   * returned by the OS.  The server specified here will be used as fall-backs in the event the OS/network does not
   * provide any DNS servers or if those servers are not reachable.
   * Specified in the format "*******" (for IPv4) or "2001:4860:4860::8888" (for IPv6).
   */
   cpc::vector<cpc::string>              additionalNameServers;

   /**
   * Session timer (RFC 4028) mode; enter one of the values from SipAccountSessionTimerMode.
   */
   SipAccountSessionTimerMode            sessionTimerMode;

   /**
   * Session timer value in seconds. Must be at least 90 seconds, or session timers will not be used.
   */
   unsigned int                          sessionTimeSeconds;

   /**
   * No longer supported; use rport instead.
   * The source of the STUN server to use.
   * If useRport or useOutbound are enabled, STUN is disabled
   */
   enum StunServerSourceType             stunServerSource;

   /**
   * No longer supported; use rport instead.
   * STUN server host address[:port]. Only used when stunServerSource == StunServerSource_Custom.
   */
   cpc::string                           stunServer;
   
   /**
    * Ignore certificate verification.
    */
   bool                                  ignoreCertVerification;

   /**
   * Additional DNS names checked during certificate verification.
   * Useful in situations where server certificates do not conform to
   * RFC-specified constraints.
   * The DNS names specified in this list are checked in
   * addition to the domain.
   * DNS names specified here when ignoreCertVerification is set to 'true'
   * are ignored.
   */
   cpc::vector<cpc::string>              additionalCertPeerNames;

   /**
   * Base 64 encoded public keys, checked during certificate verification.
   * The public keys specified in this list are checked against the
   * value in the server certificate.  If any of the keys match, the
   * certificate will be considered valid, regardless of other certificate
   * validation errors (even if ignoreCertVerification is set to 'false').
   * If the server certificate does not match any of the keys specified
   * in this list, the certificate may still be considered valid subject to default
   * validation procedures.
   */
   cpc::vector<cpc::string>              acceptedCertPublicKeys;

   /**
   * Base 64 encoded public keys, checked during certificate verification.
   * If specified, certificate verification is performed as normal,
   * with an additional check to ensure that the public key in the server
   * certificate matches at least one of the values specified here.
   * Public keys specified here when ignoreCertVerification is set to 'true'
   * are still checked and may still cause certificate validation to fail.
   */
   cpc::vector<cpc::string>              requiredCertPublicKeys;

   /**
    * QoS for SIP traffic.
    */
   unsigned int                          sipQosSettings;

   /**
   * True to include an Authorization header in initial outgoing requests.
   */
   bool                                  useImsAuthHeader;

   /**
   * Minimal value for SIP port. For TCP, the SDK will only open a listening
   * port for SIP if a SIP port range is specified (here) and either useRport or
   * useRegistrar is false.
   */
   int                                   minSipPort;

   /**
   * Maximal value for SIP port. For TCP, the SDK will only open a listening
   * port for SIP if a SIP port range is specified (here) and either useRport or
   * useRegistrar is false.
   */
   int                                   maxSipPort;

   /**
   * When no SIP port in domain or outboundProxy setting is specified, this destination 
   * SIP port will be used if no DNS SRV records are discovered, for UDP and TCP.
   * Applications can specify a non-default SIP port in the domain or outboundProxy setting instead
   * of here to skip DNS SRV lookups.
   * This setting defaults to port 5060.
   */
   int                                   defaultSipPort;

   /**
   * When no SIP port in domain or outboundProxy setting is specified, this destination 
   * SIP port will be used if no DNS SRV records are discovered, for TLS.
   * Applications can specify a non-default SIP port in the domain or outboundProxy setting instead
   * of here to skip DNS SRV lookups.
   * This setting defaults to port 5061.
   */
   int                                   defaultSipsPort;

   /**
    * Controls whether or not the method parameter is enabled in the Refer-To header.
    * Parameter use specified in RFC 3515 and RFC 4579.
   */
   bool                                  useMethodParamInReferTo;

   /**
   * Indicator wether to use "sip.instance" media feature tag which appears as a 
   * "+sip.instance" Contact header field parameter.  This is a Uniform Resource 
   * Name (URN) that uniquely identifies this specific UA instance.
   */
   bool                                  useInstanceId;

   /**
    * Indicates whether to advertise the support of the Answer-Mode extension defined in RFC 5373,
    * results in the inclusion of the "extensions=answermode" parameter in the Contact header,
    * and also includes the "answermode" option tag in the Supported header.
   */
   bool                                  answerModeSupported;

   /**
   * SIP transport IP version (IPv4, IPv6, or auto).
   */
   IpVersion                             ipVersion;

   /**
   * SSL protocol version to use.
   */
   SSLVersion                            sslVersion;

   /**
   * OpenSSL cipher suite string. See SslCipherOptions.h for recommended cipher suites.
   */
   CipherSuite                           cipherSuite;

   /**
   * Flag to control if SSL_OP_LEGACY_SERVER_CONNECT
   * is to be used when setting up an openSSL connection.
   */
   bool enableLegacyServerConnect;

   /**
   * Re-register when any of the response types specified here are
   * received.
   */
   cpc::vector<SipResponseType>          reRegisterOnResponseTypes;

   /**
   * True to automatically handle network de-registration requests using
   * the "reg" SUBSCRIBE/NOTIFY event package
   */
   bool                                  enableRegeventDeregistration;

   /**
    * If DNS failover is in use, enable to have the SDK account periodically attempt to revert back to the
    * primary SIP server, if it had previously failed over to the secondary (non-preferred) SIP server.
    * If not enabled, the SDK account will not proactively attempt to revert to the primary SIP server in the above case.
    *
    * When this setting is enabled, the following pre-conditions are required before a DNS reset (and revert to primary
    * SIP server) are triggered:
    *   - Account is registered
    *   - Current registration does not match preferred target (based on DNS results)
    *   - No active calls
    *   - Preferred target is active (based on response to SIP OPTIONS request the SDK account sends out)
    *   - History of triggered DNS resets is less than threshold (10 resets in 600 seconds)
   */
   bool                                  enableDNSResetOnRegistrationRefresh;

   /**
    * Enabling will reset the authentication everytime the DNS is reset, only applicable
    * if DNS reset has been enabled.
   */
   bool                                  enableAuthResetUponDNSReset;

   /**
   * xcap root location inside domain. Part of the path that goes between domain and AUID
   */
   cpc::string                           XCAPRoot;

   /**
   * Configuration to use for for transporting signaling and media via tunnel.
   */
   TunnelConfig                          tunnelConfig;

   /**
   * Additional parameters to be added to SIP Contact headers.
   */
   cpc::vector<SipParameterType>         capabilities;

   /**
   * Additional parameters to be added to SIP From header on outgoing requests
   */
   cpc::vector<SipParameterType>         additionalFromParameters;

   /**
   * Limit networking usage to this address if specified.  Address
   * can be IPv4 or IPv6 depending on setting of ipVersion.
   */
   cpc::string                           sourceAddress;

   /**
   * Provide the value of the P-Asserted-Identity header instead of From/To 
   * as the identity of the sender for received messages, if present.(RFC 3325)
   */
   bool                                  preferPAssertedIdentity;

   /**
   * Automatically re-open connections for registration when closed.
   * This setting is for platforms that make preferable solutions
   * difficult or impossible to deploy.  Applies to TCP/TLS only.
   */
   bool                                  autoRetryOnTransportDisconnect;

   /**
   * Configures the keep-alive strategy used by the SDK for SIP.
   * KeepAliveMode_CRLFCRLF - Use CRLFCRLF keep-alives for both SIP/UDP and SIP/TCP.
   * KeepAliveMode_Default - Use transport-specific default keep-alives; currently this implies CRLFCRLF for both SIP/UDP and SIP/TCP.
   * KeepAliveMode_NoKeepAlives - No keep-alives sent by the SDK client.
   * NOTE: Setting this to 'KeepAliveMode_NoKeepAlives' is not practical in deployments 
   * where an SDK client may be behind a NAT/firewall, since the SIP proxy will have
   * no means of forwarding requests to the client if/when the firewall pinhole closes (for UDP)
   * or the TCP connection is closed.
   */
   KeepAliveMode                         keepAliveMode;

   /**
   * Configures the SDK's preferred mechanism for identifying its registration binding.
   * Rinstance is enabled by default.  It permits rport re-registration (and thus SIP NAT traversal)
   * to succeed in a wide variety of networks.
   * Rinstance should only be disabled if specific interop issues with it are encountered.
   */
   bool                                  useRinstance;

   /**
   * Support the use of IPv4-only SIP services/infrastructure from an IPv6-only client.
   * Uses DNS64 to translate IPv4 addresses into IPv6 addresses.
   * Uses fake IPv4 addresses in SIP Via and Contact headers, as well as SDP (media) c= lines.
   * Requires a Session Border Controller (SBC) with latching support, or a STUN server.
   * 
   * Enabled by default, but only used when IPv6 is used to contact an IPv4-only SIP proxy/registrar.
   * On platforms/networks where alternative IPv4-to-IPv6 transition mechanisms are used
   * (e.g. 464XLAT, Android CLAT) this feature is typically not necessary.
   * NOTE: Requires that the configured name servers (e.g. from the OS or using SipAccountSettings)
   * support DNS64.
   */
   bool                                  enableNat64Support;

   /**
   * True to prevent modifying the From/Contact headers and only include the Privacy header for anonymous requests (RFC 3323).
   */
   bool                                  usePrivacyHeaderOnlyForAnonymous;
   
   /**
    * If any SIP calls are active, the transport IP verson the call started on matches that
    * of this setting, and the device has transitioned from WWAN to WiFi, the SDK will attempt
    * to maintain the existing connection to the SIP server over the WWAN transport until all
    * calls have ended.
    * This strategy is an alternate to the SDK's default behaviour for call continuity where it
    * will attempt a re-INVITE with updated c-line when transitioning to a new network.
    *
    * Currently only supported on iOS
    */
   TransportHoldover                     transportHoldover;

   /**
    * Enable pinging the server using OPTIONS messages to check that it is alive.
    * See Packetizer's POCS-1 (https://hive1.hive.packetizer.com/users/packetizer/pocs/POCS-1.pdf)
    * On a failed ping, the account will attempt to reregister and the account status will change to Status_Refreshing.
    */
   bool                                  useOptionsPing;

   /**
    * The interval in milliseconds between OPTIONS pings.
    */
   unsigned int                          optionsPingInterval;

   /**
    * PEM client certificate used in mutual authentication. 
    * Can be a path to a *.pem file or a string containing the certificate.
    */
   cpc::string userCertificatePEM;
   /**
    * PEM client private key used in mutual authentication.
    * Can be a path to a *.pem file or a string containing the private key.
    */
   cpc::string userPrivateKeyPEM;
   
   /**
    * Forces the SDK to open a listen socket for SIP.
    *
    * In some cases the SDK will automatically not open a listen socket for TCP or TLS:
    *    - on iOS, both TCP and TLS listen socket will not be opened: to work around iOS defects (O-4002)
    *    - on any platform, for TLS if no client certificate is provided via userCertificatePEM
    *
    * The application should be aware of repercussions of forcing a listen socket.
    *
    */
   bool                                  forceListenSocket;

   /**
   * The local group ID used to support SIP Publish reports as defined in RFC 6035
   */
   cpc::string                           localGroup;
   
   /**
    * Enable TimerF override for registration messages by setting the desired TimerF timeout in msecs.
    * Default value is 0, indicating that TimerF override is disabled.
   */
   unsigned int overrideMsecsTimerF;

   /**
    * Min interval in which the SDK will not re-transmit 200 OK response to an invite request
    * for systems that can't handle retransmits in short intervals
    */
   unsigned int minInvite200RetransmitIntervalSec;

   /**
    * Enable SDK handling of unpadded Base64 encoded crypto keys
    */
   bool addCryptoKeyPadding;

   /**
    * Criteria for ignoring irrelevant network changes
    * NetworkChangeFilter_None:
    *    NetworkChangeFilter is off
    * NetworkChangeFilter_IpVer:
    *    Ignore network change if it does not affect the ipVersion used
    *    Applies to IpVersion_V4 and IpVersion_V6 only, ignored if dual stack support used
    */
   NetworkChangeFilter networkChangeFilter;

   SipAccountSettings()
   {
      domain                              = "";
      useRegistrar                        = true;
      alwaysRouteViaOutboundProxy         = false;
      registrationIntervalSeconds         = 3600;
      minimumRegistrationIntervalSeconds  = 5;
      maximumRegistrationIntervalSeconds  = 3600;
      useRport                            = true;
      sipTransportType                    = SipAccountTransport_Auto;
      excludeEncryptedTransports          = false;
      userAgent                           = "CPCAPI2-based UA";
      udpKeepAliveTime                    = 30;
      tcpKeepAliveTime                    = 120;
      useOutbound                         = false;
      useGruu                             = false;
      sessionTimerMode                    = SipAccountSessionTimerMode_Inactive;
      sessionTimeSeconds                  = 0;
      stunServerSource                    = StunServerSource_None;
      ignoreCertVerification              = false;
      sipQosSettings                      = 40;
      useImsAuthHeader                    = false;
      minSipPort                          = 0;
      maxSipPort                          = 0;
      defaultSipPort                      = 5060;
      defaultSipsPort                     = 5061;
      useMethodParamInReferTo             = false;
      useInstanceId                       = false;
      answerModeSupported                 = false;
      ipVersion                           = IpVersion_V4;
      sslVersion                          = TLS_DEFAULT;
      enableLegacyServerConnect           = false;
      sourceAddress                       = "";
      enableRegeventDeregistration        = false;
      enableDNSResetOnRegistrationRefresh = false;
      enableAuthResetUponDNSReset         = false;
      preferPAssertedIdentity             = true;
      autoRetryOnTransportDisconnect      = false;
      keepAliveMode                       = KeepAliveMode_Default;
      reRegisterOnResponseTypes.push_back(SipResponseType("REGISTER", 500));
      reRegisterOnResponseTypes.push_back(SipResponseType("REGISTER", 503));
      reRegisterOnResponseTypes.push_back(SipResponseType("REGISTER", 504));
      reRegisterOnResponseTypes.push_back(SipResponseType("REGISTER", 407));
      reRegisterOnResponseTypes.push_back(SipResponseType("REGISTER", 408));
      useRinstance                        = true;
      enableNat64Support                  = true;
      usePrivacyHeaderOnlyForAnonymous    = false;
      transportHoldover                   = TransportHoldover_None;
      useOptionsPing                      = false;
      optionsPingInterval                 = 0;
      userCertificatePEM                  = "none";
      userPrivateKeyPEM                   = "";
      forceListenSocket                   = false;
      localGroup                          = "";
      overrideMsecsTimerF                 = 0;
      minInvite200RetransmitIntervalSec   = 0;
      addCryptoKeyPadding                 = false;
      networkChangeFilter                 = NetworkChangeFilter_IpVer;
   }
};
   
cpc::string get_debug_string(const SipAccount::SipAccountTransportType& transport);
cpc::string get_debug_string(const SipAccount::IpVersion& ip);
cpc::string get_debug_string(const SipAccount::SSLVersion& ssl);
   
}
   
}

#endif // CPCAPI2_SIP_ACCOUNT_SETTINGS_H
