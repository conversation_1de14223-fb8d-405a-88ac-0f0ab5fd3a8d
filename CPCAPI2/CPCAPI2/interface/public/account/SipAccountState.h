#pragma once

#if !defined(CPCAPI2_SIP_ACCOUNT_STATE_H)
#define CPCAPI2_SIP_ACCOUNT_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "account/SipAccountHandler.h"
#include "account/SipAccountSettings.h"

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountManager;
struct SipAccountState
{
   SipAccountHandle account;
   SipAccountStatusChangedEvent::Status accountStatus;
   SipAccountSettings settings;

   SipAccountState()
   {
      account = 0;
      accountStatus = SipAccountStatusChangedEvent::Status_Unregistered;
   }
};
/**
 */
class CPCAPI2_SHAREDLIBRARY_API SipAccountStateManager
{
public:
   /**
   * Get a reference to the SipAccountStateManager interface.
   */
   static SipAccountStateManager* getInterface(SipAccountManager* cpcAcctMan);

   virtual int getStateAllAccounts(cpc::vector<SipAccountState>& accountState) = 0;

protected:

   /*
    * The SDK will manage memory life of %SipAccountStateManager.
    */
   virtual ~SipAccountStateManager() {}

};

}
}
#endif // CPCAPI2_SIP_ACCOUNT_STATE_H
