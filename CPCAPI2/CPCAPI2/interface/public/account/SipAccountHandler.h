#pragma once

#if !defined(CPCAPI2_ACCOUNT_HANDLER_H)
#define CPCAPI2_ACCOUNT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "account/SipAccountSettings.h"

namespace CPCAPI2
{
namespace SipAccount
{
/**
* Details about the server certificate.
* Used for diagnosing connection failures due to certificate
* validation errors.
*/
struct SipTLSConnectionInfo
{
   SipTLSConnectionInfo() : certificateStatus(0), sslVersion(TLS_V1_0) {}

   enum CertificateStatus
   {
      CertificateStatus_Ok                = 0,   /**< The certificate is valid and trusted. */
      CertificateStatus_Invalid           = 1,   /**< The certificate is not trusted. */
      CertificateStatus_SignerUnknown     = 2,   /**< The certificate hasn't got a known issuer. */
      CertificateStatus_Revoked           = 4,   /**< The certificate has been revoked. */
      CertificateStatus_Expired           = 8,   /**< The certificate has expired. */
      CertificateStatus_NotActive         = 16,  /**< The certificate is not yet active. */
      CertificateStatus_WrongPeer         = 32,  /**< The certificate has not been issued for the peer we're connected to. */
      CertificateStatus_CertSignerNotCa   = 64,  /**< The signer is not a CA. */
      CertificateStatus_WrongPubKey       = 128, /**< The public key does not match any of the keys specified in SipAccountSettings::pinnedCertPublicKeys. */
   };

   int certificateStatus;                        /**< Bitwise or'ed CertificateStatus or CertificateStatus_Ok. */
   cpc::string issuer;                           /**< The name of the issuing entity.*/
   cpc::string server;                           /**< The server the certificate has been issued for. */
   cpc::vector<cpc::string> peerNames;           /**< From the subjectAltName in the certificate. */
   cpc::string protocol;                         /**< The encryption protocol used for the connection. */
   cpc::string cipher;                           /**< The cipher used for the connection. */
   cpc::string compression;                      /**< The compression used for the connection. */
   cpc::string publicKey;                        /**< Base 64 encoded version of the public key. */
   SSLVersion sslVersion;                        /**< SSL protocol version negotiated. */
};

/**
* Event passed in SipaccountHandler::onAccountStatusChanged();
* notifies that the status of the account has changed. It contains:
* <ul>
* <li>accountStatus: The current status of the account is one of the values in Status.
* <li>reason: Reason for the account status change.
* <li>signalingStatusCode: The SIP code for the failure, for example, 404.
* <li>failureRetryAfterSecs: Duration before the next register message will be sent.
* Default value is 0 and indicates that there are no pending failure retries scheduled.
* This field is only applicable when the status is WaitingToRegister.
* <li>signalingResponseText: The text description of the SIP code, for example,
* "Not Found (User not found)".
* <li>transportType: Transport used by the client.
* <li>tlsInfo: Details about the server certificate.
* </ul>
*
* Refreshing account status is specific for handling re-registrations triggered from:
* - app triggered requestRegistrationRefresh
* - queued network-change handling, e.g. end of calls where dns cache had been frozen
* - upon dns reset triggered when the dns reset feature has been enabled
* - upon failure handling when the options ping feature has been enabled
* - handling network changes
* - retries from registrations triggered due to handling network changes (refresh status is sent with each retry)
*/
struct SipAccountStatusChangedEvent
{
   /*
                            refresh error
    +----------------------------------------------------------------------+
    |                                                                      |
    |                                                                      |
    |                         +-------------------+                        |
    |        any response     | Unregistering     |     disable            |
    |      +------------------+                   <-----------------+      |
    |      |                  +-------------------+                 |      |
    |      |                                                        |      |
    |      |                                                        |      |
    |      |                                                        |      |
+---v------v---+              +-------------------+              +--+------+--+
| Unregistered |  enable      | Registering       |    200       | Registered |
|              +-------------->                   +-------------->            |
+-----^----^---+              +--+------^------+--+              +--^------+--+
      |    |    disable / error  |      |      |                    |      |
      |    +---------------------+    retry 408/503                200   refresh
      |                                 |      |                    |      |
      |                       +---------+------v--+              +--+------v--+
      |     disable           | WaitingToRegister |    408/503   | Refreshing |
      +-----------------------+                   <--------------+            |
                              +-------------------+              +------------+

   */

   enum Status
   {
      Status_Registered          = 1,
      Status_Failure             = 2, // deprecated; this state will never be reported (Unregistered will be reported instead)
      Status_Unregistered        = 3,
      Status_Registering         = 4,
      Status_Unregistering       = 5,
      Status_WaitingToRegister   = 6,
      Status_Refreshing          = 7
   };
   Status                        accountStatus;

   enum Reason
   {
      Reason_None                        = 0,  /**< No explicit reason for account status change. */
      Reason_No_Network                  = 1,  /**< Account status changed because network is not available. */
      Reason_Restricted_Network          = 2,  /**< Account status changed because network has limited connectivity. */
      Reason_New_Network                 = 3,  /**< Account status changed because client connected to a new network. */
      Reason_Server_Response             = 4,  /**< Account status changed because client received response from server. */
      Reason_Local_Timeout               = 5,  /**< Account status changed because local time out expired. Didn't receive keep alive in time for example. */
      Reason_NetworkDeregistered         = 6,  /**< Account status changed due to network deregistration. */
      Reason_Tunnel_Failure              = 7,  /**< Account status changed due to network tunneling failure. */
      Reason_Dns_Lookup                  = 8,  /**< Account status changed due to DNS lookup failure */
      Reason_Transport_Protocol_Mismatch = 9,  /**< Account status changed because of transport protocol mismatch between local and remote party. */
      Reason_No_Route_To_Host            = 10,  /**< Account status changed because route to host can't be established. */
      Reason_TLS_Cipher_Mismatch         = 11,  /**< Account status changed because there is no shared TLS cipher between local and remote party. */
      Reason_Domain_Locked               = 12,  /**< Account status changed due to domain not allowed. */
   };

   Reason                        reason;
   unsigned int                  signalingStatusCode;
   unsigned int                  failureRetryAfterSecs;
   cpc::string                   signalingResponseText;
   cpc::string                   accountBindingIpAddress; /** Local contact address that shall be used for signaling. */
   cpc::string                   rinstance; /** rinstance param (if used) */
   SipAccountTransportType       transportType;
   SipTLSConnectionInfo          tlsInfo;
   IpVersion                     ipVersionInUse; /** Valid only after successful registration */
   cpc::string                   serverIpAddress; /** IP address (V4 or V6) of connected server; valid only after successful registration */
   unsigned int                  serverPort; /** Port of connected server; valid only after successful registration */
   cpc::string                   localIpAddress; /** Local IP address (V4 or V6); valid only after successful registration */
   unsigned int                  localPort; /** Local port; valid only after successful registration */
   cpc::string                   localContactBinding; /** Only relevant for registered status */
   unsigned int                  responseTimeMs;
   cpc::string                   callId;

   SipAccountStatusChangedEvent() :
      accountStatus(Status_Unregistered),
      reason(Reason_None),
      signalingStatusCode(0),
      failureRetryAfterSecs(0),
      signalingResponseText(""),
      accountBindingIpAddress(""),
      rinstance(""),
      transportType(SipAccountTransport_Unknown),
      ipVersionInUse(IpVersion_V4),
      serverIpAddress(""),
      serverPort(0),
      localIpAddress(""),
      localPort(0),
      localContactBinding(""),
      responseTimeMs(0),
      callId("") {}
};

/**
* Event passed in SipAccountHandler::onError().
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
* DEPRECATED - no longer used.
* Event passed in SipaccountHandler::onLicensingError().
*/
struct LicensingErrorEvent
{
   cpc::string errorText;
};

/**
 * The handler for events on SipAccount; passed in SipAccount::setHandler().
 */
class SipAccountHandler
{
public:
   /**
   * Notifies the application when the %SipAccount has changed to a specific state.
   */
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountStatusChangedEvent& args) = 0;

   /**
   * Notifies the application when an account error has occurred
   */
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const ErrorEvent& args) = 0;

   /**
   * DEPRECATED; no longer used. Licensing errors are now indicated with a PhoneErrorHandler::onLicensingErrorEvent
   */
   DEPRECATED virtual int onLicensingError(CPCAPI2::SipAccount::SipAccountHandle account, const LicensingErrorEvent& args) { return kSuccess; };
};

struct SipAccountAdornmentEvent
{
   unsigned int adornmentMessageId;

   // request-specific
   cpc::string target;
   cpc::string method;

   // response-specific
   unsigned int responseCode;

   // general
   cpc::string message;
};

class SipAccountAdornmentHandler
{
public:
   /**
    * Notifies the application when an outgoing account related SIP message is ready to be adorned.
    */
   virtual int onAccountAdornment(CPCAPI2::SipAccount::SipAccountHandle account, const SipAccountAdornmentEvent& args) = 0;
};

}
}
#endif // CPCAPI2_ACCOUNT_HANDLER_H
