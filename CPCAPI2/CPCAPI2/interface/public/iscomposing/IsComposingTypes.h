#pragma once

#if !defined(__CPCAPI2_IS_COMPOSING_TYPES_H__)
#define __CPCAPI2_IS_COMPOSING_TYPES_H__

namespace CPCAPI2
{
namespace IsComposing
{

/**
 * Possible states for the 'IsComposing' indicator.
 */
enum IsComposingMessageState 
{
   IsComposingMessageState_Unknown = 0,
   IsComposingMessageState_Idle    = 1,
   IsComposingMessageState_Active  = 2
};

}
}

#endif // __CPCAPI2_IS_COMPOSING_TYPES_H__