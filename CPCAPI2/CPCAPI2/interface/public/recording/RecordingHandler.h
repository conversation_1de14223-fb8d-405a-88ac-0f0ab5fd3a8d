#pragma once

#if !defined(CPCAPI2_RECORDING_HANDLER_H)
#define CPCAPI2_RECORDING_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
namespace Recording
{

typedef unsigned int RecorderHandle;

enum RecordingStopReason
{
   RecordingStopReason_Unknown          = 0,
   RecordingStopReason_NoSpace          = 1,
   RecordingStopReason_BadFile          = 2,
};

/**
 * Event passed in RecordingHandler::onRecordingStopped().
 * Contains information about stop reason
 *
 * @param recorder The handle for this particular recorder
 * @param reason The stop reason
 */
struct RecordingStoppedEvent
{
   DEPRECATED RecorderHandle recorder;
   enum RecordingStopReason reason;
};

enum RecordingStreams
{
   RecordingStreams_None = 0,   // uninitialized state
   RecordingStreams_Local = 1,  // recorded local microphone
   RecordingStreams_Remote = 2, // recorded audio from remote part(y/ies)
   RecordingStreams_Both = RecordingStreams_Local | RecordingStreams_Remote
};

enum RecordingStreamError
{
   RecordingStreamError_None = 0,         // no errors
   RecordingStreamError_Unknown = 1,      // unknown error
   RecordingStreamError_Permissions = 2,  // user does not have access to the provided path
   RecordingStreamError_InvalidPath = 3,  // the path provided was not found
};

struct RecorderFilenameInfo
{
   bool completed;                     // TRUE when the recording has completed for this file
   RecordingStreams includedStreams;   // what is the file containing
   cpc::string filename;               // the full path to the file
   enum RecordingStreamError error;

   RecorderFilenameInfo() : completed(false), includedStreams(RecordingStreams_None) {}
};

/**
 * Event passed to RecordingHandler::onRecorderFilenames() when requested via getRecorderFilenames()
 * and also when recording has ended.
 *
 * Contains information about the files that are being recorded for the recorder
 *
 * @param recorder The handle for this particular recorder
 * @param event Information about the file(s) currently being recorded by this recorder
 */
struct RecorderFilenamesEvent
{
   // info about each file recorded - when splitMedia is TRUE there would be more than one file
   cpc::vector<RecorderFilenameInfo> files;
};

/**
 * Handler for events relating to recording;
 * set in RecordingManager::setHandler().
 */
class RecordingHandler
{
public:
   /**
    * Called when recorder was stopped during recording (i.e. only fired in an error scenario).
    */
   DEPRECATED virtual int onRecordingStopped(const RecordingStoppedEvent& args) { return 0; };
   virtual int onRecordingStoppedEx(RecorderHandle recorder, const RecordingStoppedEvent& args) { return 0; };

   /**
    * Called when a request is made for the recorder filenames and also when the recording session is destroyed by the app.
    */
   virtual int onRecorderFilenames(RecorderHandle recorder, const RecorderFilenamesEvent& args) = 0;
};

}
}
#endif // CPCAPI2_RECORDING_HANDLER_H
