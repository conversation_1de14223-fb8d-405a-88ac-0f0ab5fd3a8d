#pragma once

#if !defined(CPCAPI2_RECORDING_MANAGER_H)
#define CPCAPI2_RECORDING_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "RecordingHandler.h"
#include "call/SipConversationTypes.h"

#include <vector>

namespace CPCAPI2
{
class Phone;

namespace Recording
{
/**
* Manager controlling recording
*/
class CPCAPI2_SHAREDLIBRARY_API RecordingManager
{
public:
   /**
    * Get a reference to the %RecordingManager interface.
    */
   static RecordingManager* getInterface(Phone* cpcPhone);
   
   /**
    * Set the handler for events on the specified recorder.
	*
	* To un-register the handler, pass NULL for handler. Must be called on the same thread as process(..)
    */
   virtual int setHandler(RecorderHandle recorder, RecordingHandler* handler) = 0;
   
   /**
    * Creates new recorder and returns recorder handle.
    * @param filePath path to destination file
    * @param splitMedia whether or not local and remote participant media is saved to separate files. Use getRecorderFilenames method to obtain relevant filenames.
    */
   virtual RecorderHandle audioRecorderCreate(const cpc::string& filePath, bool splitMedia = false) = 0;
   
   /**
    * Adds conversation to recorder. The conversation must be in state ConversationState_Connected.
    * @param recorder recorder handle
    * @param conversation conversation handle
    */
   virtual int recorderAddConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation) = 0;
   
   /**
    * Removes conversation from recorder.
    * @param recorder recorder handle
    * @param conversation conversation handle
    */
   virtual int recorderRemoveConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation) = 0;
   
   /**
    * Starts recording.
    * @param recorder recorder handle
    */
   virtual int recorderStart(RecorderHandle recorder) = 0;
   
   /**
    * Pauses recording.
    * @param recorder recorder handle
    */
   virtual int recorderPause(RecorderHandle recorder) = 0;
   
   /**
    * Stops recording and destroys recorder.
    * @param recorder recorder handle
    */
   virtual int recorderDestroy(RecorderHandle recorder) = 0;
   
   /**
    * Initiates an OnRecorderFilenames event with a list of the files being recorded.
    * @param recorder recorder handle
    */
   virtual int getRecorderFilenames(RecorderHandle recorder) = 0;
   
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kRecordingModuleDisabled = -1;
   
   /**
    * Allows the application code to receive callback notifications from the SDK.
    *
    * These callbacks will happen synchronously, in the same thread of execution as that in which
    * %process() is invoked.  Depending on the application threading model,
    * process() can be used in two different ways:
    * <ol>
    * <li>blocking mode ?Typically in this mode, %process() is called by the
    * application from a background (worker) thread.  The call to process()
    * blocks until a callback function needs to be invoked.
    * <li>non-blocking mode ?In this mode, %process() is called by the application
    * from the main (GUI) thread of the application, typically from the
    * main message/event loop.  In this mode, %process() returns immediately
    * and so must be called frequently enough that the application can receive
    * its callback notifications in a timely manner.
    *
    * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
    *                representing the time the call to process(..) will block waiting for a callback
    *                from the SDK
    *</ol>
    */
   virtual int process(unsigned int timeout) = 0;
   
   /**
    * Allows the application to "unblock" the
    * thread calling MediaManager::process(..) if it is blocked waiting for an SDK callback.
    * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
    */
   virtual void interruptProcess() = 0;
   
protected:
   /*
    * The SDK will manage memory life of %RecordingManager.
    */
   virtual ~RecordingManager() {}
};

}
}
#endif // CPCAPI2_RECORDING_MANAGER_H
