/*-
 * Copyright 2012 <PERSON>
 * All rights reserved
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted providing that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON>TITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON>VE<PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef TINYSTL_STRING_H
#define TINYSTL_STRING_H

#include "cpcapi2defs.h"

#include "allocator.h"
#include "stddef.h"
#include "hash.h"

#include <iostream>


#ifndef CPCAPI2_CPCSTL_API
#ifdef CPCAPI2_CPCSTL_EXPORT
#define CPCAPI2_CPCSTL_API CPCAPI2_SHAREDLIBRARY_EXPORT
#else
#define CPCAPI2_CPCSTL_API CPCAPI2_SHAREDLIBRARY_API
#endif
#endif

namespace cpc {
   class CPCAPI2_CPCSTL_API string {
	public:

      static const size_t npos = -1;

		string();
		string(const string& other);
		string(const char* sz);
		string(const char* sz, size_t len);
		~string();

		string& operator=(const string& other);
      string substr(size_t start, size_t len=npos) const;

		const char* c_str() const;
		size_t size() const;
      bool empty() const;

      void assign(const char* sz, size_t len=npos);

		void reserve(size_t size);
		void resize(size_t size);

		void append(const char* first, const char* last);
      void append(const string& other);

      size_t find(const string& str) const;

		void swap(string& other);

      operator std::wstring() const;
      operator const char*() const;

      string& operator+=(const string& rhs);
      bool operator<(const string& rhs) const;

   private:
      void* toWstring(const char* in, const wchar_t** out, size_t* outSize) const;
      void freeWstring(void* h) const;

	private:
		typedef char* pointer;
		pointer m_first;
		pointer m_last;
		pointer m_capacity;

		static const size_t c_nbuffer = 12;
		char m_buffer[12];
	};

   string to_string(unsigned int val);
   string to_string(int val);
   string to_string(float val);
   string to_string(long val);
   string to_string(int64_t val);
   float to_float(const string& str);
   int to_int(const string& str);

   inline size_t strlen(const char* sz) {
      const char* end = sz;
      for(;*end != '\0'; end++){}
      return end-sz;
   }

   inline string::operator std::wstring() const {
      const wchar_t* buffer = NULL;
      size_t bufferSize = 0;
      void* wstrHandle = toWstring(c_str(), &buffer, &bufferSize);
      std::wstring result(buffer, bufferSize);
      freeWstring(wstrHandle);
      return result;
   }

	inline string::string()
		: m_first(m_buffer)
		, m_last(m_buffer)
		, m_capacity(m_buffer + c_nbuffer)
	{
		resize(0);
	}

	inline string::string(const string& other)
		: m_first(m_buffer)
		, m_last(m_buffer)
		, m_capacity(m_buffer + c_nbuffer)
	{
		reserve(other.size());
		append(other.m_first, other.m_last);
	}

	inline string::string(const char* sz)
		: m_first(m_buffer)
		, m_last(m_buffer)
		, m_capacity(m_buffer + c_nbuffer)
	{
		size_t len = 0;
		for (const char* it = sz; it && *it; ++it)
			++len;

		reserve(len);
		append(sz, sz + len);
	}

	inline string::string(const char* sz, size_t len)
		: m_first(m_buffer)
		, m_last(m_buffer)
		, m_capacity(m_buffer + c_nbuffer)
	{
		reserve(len);
		append(sz, sz + len);
	}

	inline string& string::operator=(const string& other) {
		string(other).swap(*this);
		return *this;
	}

	inline const char* string::c_str() const {
		return m_first;
	}

	inline size_t string::size() const
	{
		return (size_t)(m_last - m_first);
	}

   inline bool string::empty() const
   {
      return (size() == 0);
   }

	inline void string::resize(size_t size) {
		reserve(size);
		for (pointer it = m_last, end = m_first + size + 1; it < end; ++it)
			*it = 0;

		m_last += size;
	}

   inline string& string::operator+=(const string& rhs) {
      this->append(rhs);
      return *this;
   }

   inline string operator+(string lhs, const string& rhs) {
      string result = lhs;
      result += rhs;
      return result;
   }

   inline void string::assign(const char* sz, size_t len) {
      if (len == npos) {
         len = cpc::strlen(sz);
      }
      *this = cpc::string(sz, len);
   }

   inline size_t string::find(const string& str) const {
      size_t strSize = str.size();
      if (0 == strSize) {
         return 0;
      }
      size_t thisSize = size();
      for(size_t idx=0; strSize+idx <= thisSize; idx++) {
         bool match = true;
         for(size_t strIdx=0; strIdx < strSize; strIdx++) {
            char myCh = this->c_str()[idx+strIdx];
            char otherCh = str.c_str()[strIdx];
            if(myCh != otherCh) {
               match = false;
               break;
            }
         }
         if (match) {
            return idx;
         }
      }
      return npos;
   }

   inline void string::append(const string& other) {
      append(other.m_first, other.m_last);
	}

	inline void string::append(const char* first, const char* last) {

		const size_t newsize = (size_t)((m_last - m_first) + (last - first) + 1);
		if (m_first + newsize > m_capacity)
			reserve((newsize * 3) / 2);

		for (; first != last; ++m_last, ++first)
			*m_last = *first;
		*m_last = 0;
	}

	inline void string::swap(string& other) {
		const pointer tfirst = m_first, tlast = m_last, tcapacity = m_capacity;
		m_first = other.m_first, m_last = other.m_last, m_capacity = other.m_capacity;
		other.m_first = tfirst, other.m_last = tlast, other.m_capacity = tcapacity;

		char tbuffer[c_nbuffer];

		if (m_first == other.m_buffer)
			for  (pointer it = other.m_buffer, end = m_last, out = tbuffer; it != end; ++it, ++out)
				*out = *it;

		if (other.m_first == m_buffer) {
			other.m_last = other.m_last - other.m_first + other.m_buffer;
			other.m_first = other.m_buffer;
			other.m_capacity = other.m_buffer + c_nbuffer;

			for (pointer it = other.m_first, end = other.m_last, in = m_buffer; it != end; ++it, ++in)
				*it = *in;
			*other.m_last = 0;
		}

		if (m_first == other.m_buffer) {
			m_last = m_last - m_first + m_buffer;
			m_first = m_buffer;
			m_capacity = m_buffer + c_nbuffer;

			for (pointer it = m_first, end = m_last, in = tbuffer; it != end; ++it, ++in)
				*it = *in;
			*m_last = 0;
		}
	}

   inline bool operator==(const string& lhs, const char* rhs) {
      typedef const char* pointer;
		const size_t lsize = lhs.size(), rsize = cpc::strlen(rhs);
		if (lsize != rsize)
			return false;

		pointer lit = lhs.c_str(), rit = rhs;
		pointer lend = lit + lsize;
		while (lit != lend)
			if (*lit++ != *rit++)
				return false;

		return true;
	}


   inline bool operator==(const char* lhs, const string& rhs) {
      return rhs == lhs;
	}

	inline bool operator==(const string& lhs, const string& rhs) {
		typedef const char* pointer;

		const size_t lsize = lhs.size(), rsize = rhs.size();
		if (lsize != rsize)
			return false;

		pointer lit = lhs.c_str(), rit = rhs.c_str();
		pointer lend = lit + lsize;
		while (lit != lend)
			if (*lit++ != *rit++)
				return false;

		return true;
	}

   inline bool operator!=(const string& lhs, const string& rhs) {
		return !(lhs == rhs);
	}

   inline bool string::operator<(const string& rhs) const {
   	typedef const char* pointer;
		const size_t lsize = size(), rsize = rhs.size();
		size_t minSize = lsize < rsize ? lsize : rsize;

      size_t idx = 0;
      for(; idx < minSize; idx++) {
         char left = c_str()[idx];
         char right = rhs.c_str()[idx];
         if (left < right) {
            return true;
         } else if (left > right) {
            return false;
         }
      }
      if (lsize < rsize) {
         return true;
      } else if (lsize > rsize) {
         return false;
      }
      return false;
   }

	static inline size_t hash(const string& value) {
		return hash_string(value.c_str(), value.size());
	}
   
   inline std::ostream& operator<<(std::ostream& strm, const cpc::string& cpstr)
   {
      return strm.write(cpstr.c_str(), cpstr.size());
   }

}

#endif
