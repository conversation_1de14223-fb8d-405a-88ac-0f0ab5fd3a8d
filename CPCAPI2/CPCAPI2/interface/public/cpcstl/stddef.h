/*-
 * Copyright 2012 <PERSON>
 * All rights reserved
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted providing that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON>TITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef TINYSTL_STDDEF_H
#define TINYSTL_STDDEF_H



#ifndef CPCAPI2_SHAREDLIBRARY_API
#if _WIN32
  #ifdef CPCAPI2_STATIC_LIB
    #define CPCAPI2_SHAREDLIBRARY_API
  #else
    #ifdef CPCAPI2_SHAREDLIBRARY_EXPORTS
      #define CPCAPI2_SHAREDLIBRARY_API __declspec(dllexport)
    #else
      #define CPCAPI2_SHAREDLIBRARY_API __declspec(dllimport)
    #endif
  #endif
#else
  #define CPCAPI2_SHAREDLIBRARY_API __attribute__((__visibility__("default")))
#endif
#endif



#if defined(_WIN64)
	typedef long long unsigned int size_t;
#elif defined(_WIN32)
	typedef unsigned int size_t;
#elif defined (__linux__) && defined(__SIZE_TYPE__)
	typedef __SIZE_TYPE__ size_t;
#else
#	include <stddef.h>
#endif

#endif
