/*-
 * Copyright 2012 <PERSON>
 * All rights reserved
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted providing that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED.  IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
 * DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF <PERSON><PERSON><PERSON>TITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef TINYSTL_ALLOCATOR_H
#define TINYSTL_ALLOCATOR_H

#include "cpcapi2defs.h"

#include "stddef.h"

#ifndef CPCAPI2_CPCSTL_API
#ifdef CPCAPI2_CPCSTL_EXPORT
#define CPCAPI2_CPCSTL_API CPCAPI2_SHAREDLIBRARY_EXPORT
#else
#define CPCAPI2_CPCSTL_API CPCAPI2_SHAREDLIBRARY_API
#endif
#endif

namespace cpc {

	struct CPCAPI2_CPCSTL_API allocator {
		static void* static_allocate(size_t bytes);
		static void static_deallocate(void* ptr, size_t /*bytes*/);
	};
}

#ifndef TINYSTL_ALLOCATOR
#	define TINYSTL_ALLOCATOR ::cpc::allocator
#endif

#endif
