#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_HANDLER_H)
#define CPCAPI2_SIP_CONVERSATION_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipConversationTypes.h"

namespace CPCAPI2
{
namespace SipConversation
{
/**
 * Event passed in SipConversationHandler::onNewConversation.
 * An outgoing INVITE has been submitted to the SIP stack for transmission, OR
 * an outgoing INVITE has forked, OR
 * an incoming INVITE has been received.
 */
struct NewConversationEvent
{
   ConversationState                             conversationState;
   ConversationType                              conversationType;
   cpc::string                                   localAddress;
   cpc::string                                   localDisplayName;
   cpc::string                                   remoteAddress;
   cpc::string                                   remoteDisplayName;
   cpc::vector<MediaInfo>                        localMediaInfo;
   cpc::vector<MediaInfo>                        remoteMediaInfo;
   SipConversationHandle                         relatedConversation;       // forked conversation original handle
   SipConversationHandle                         conversationToReplace;
   SipConversationHandle                         conversationToJoin;
   CPCAPI2::SipAccount::SipAccountHandle         account;
   bool                                          autoAnswer;
   bool                                          isCodecsMismatched;
   bool                                          isAudioCodecsMismatched;
   bool                                          isVideoCodecsMismatched;
   cpc::string                                   alertInfoHeader;
   CPCAPI2::SipConversation::AnswerModeSettings  answerMode;
   cpc::string                                   sipMessage;                // the raw SIP message, for incoming calls
   SessionDescription                            sessionDescription;        // the SDP body, for incoming calls
   cpc::vector<cpc::string>                      resourcePriority;          // RFC 4412
   cpc::vector<HistoryInfo>                      historyInfo;               // RFC 7044
   cpc::string                                   pCalledPartyIdAddress;     // RFC 7315: value of P-Called-Party-ID header address
   cpc::string                                   pCalledPartyIdDisplayname; // RFC 7315: value of P-Called-Party-ID header display name
   cpc::string                                   sessionId;                 // RFC 7329: value of Session-ID header
   cpc::string                                   referredByAddress;         // RFC 3892: value of Referred-By header address
   cpc::string                                   referredByDisplayname;     // RFC 3892: value of Referred-By header display name
   cpc::string                                   diversionAddress;          // RFC 5806: value of Diversion header address
   cpc::string                                   diversionDisplayname;      // RFC 5806: value of Diversion header display name
   cpc::string                                   diversionReason;           // RFC 5806: value of Diversion header reason

   NewConversationEvent()
   {
      conversationState       = ConversationState_None;
      conversationType        = ConversationType_Incoming;
      relatedConversation     = 0;
      conversationToReplace   = 0;
      conversationToJoin      = 0;
      account                 = 0;
      autoAnswer              = false;
      isCodecsMismatched      = false;
      isAudioCodecsMismatched = false;
      isVideoCodecsMismatched = false;
      answerMode.mode         = CPCAPI2::SipConversation::AnswerMode_Disabled;
   }
};

/**
 * Event passed in SipConversationHandler::onConversationEnded().
 * callQualityReport is available for Voice Quality Monitoring enabled SDKs only.
 * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
 * representative for pricing.
 */
struct ConversationEndedEvent
{
   ConversationState conversationState;
   ConversationEndReason endReason;
   unsigned int sipResponseCode;
   unsigned int responseTimeMs;

   /**
    * Method from Request-Line for requests, reason from Status-Line for responses
    */
   cpc::string signallingEndEvent;

   /**
    * from SIP Reason header if available, otherwise same as signallingEndEvent
   */
   cpc::string signallingEndReason;

   /**
    * from SIP Warning header if available, otherwise same as signallingEndEvent
   */
   unsigned int signallingEndWarningCode;
   cpc::string signallingEndWarning;

   /**
    * an RFC6035 SIP Voice Quality Session Report. Only available if Voice Quality Monitoring is enabled in your SDK.
    * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
    * representative for pricing.
    */ 
   cpc::string callQualityReport;

   /*
    * RFC 7329: value of Session-ID header
   */
   cpc::string sessionId;

   ConversationEndedEvent() :
      conversationState(ConversationState_None),
      endReason(ConversationEndReason_Unknown),
      sipResponseCode(0),
      responseTimeMs(0),
      signallingEndEvent(""),
      signallingEndReason(""),
      signallingEndWarningCode(0),
      signallingEndWarning(""),
      callQualityReport("") {}
   virtual~ ConversationEndedEvent() {}
};

/**
 * Event passed in SipConversationHandler::onIncomingTransferRequest().
 *
 */
struct TransferRequestEvent
{
   cpc::string transferTargetAddress;
   cpc::string transferTargetDisplayName;
   SipConversationHandle transferTargetConversation;
};

/**
 * Event passed in SipConversationHandler::onTransferProgress.
 * Provides status updates once an attended transfer is initiated.
 * Taken from the NOTIFY w/Event: refer requests issued by the transferee.
 */
struct TransferProgressEvent
{
   TransferProgressEventType progressEventType;
   unsigned int sipResponseCode;
};

/*
 * Event passed in SipConversationHandler::onTransferResponse.
 */
struct TransferResponseEvent
{
   unsigned int sipResponseCode;
   cpc::string warningHeader;
};

/**
 * Event passed in SipConversationHandler::onIncomingRedirectRequest().
 * Incoming SIP 302 response received as a result of an outgoing INVITE.
 * Provides the SIP address of the party that the call is being redirected to. The SIP
 * address is in the format <protocol>:<AOR>, for example, sip:<EMAIL>.
 */
struct RedirectRequestEvent
{
   cpc::string targetAddress;
   cpc::string reason;
};

/**
 * Event passed in SipConversationHandler::onIncomingTargetChangeRequest().
 */
struct TargetChangeRequestEvent
{
   cpc::string targetAddress;
};

/**
 * Event passed in SipConversationHandler::onIncomingHangupRequest().
 * Incoming REFER (Refer-To: BYE).  Used in 3PCC (third-party call control) 
 * call flows when
 * the controlling endpoint requests the controlled endpoint to hang up.
 */
struct HangupRequestEvent
{
};

/**
 * Incoming Broadsoft 'talk' event
 */
struct BroadsoftTalkEvent
{
};

/**
 * Incoming Broadsoft 'hold' event
 */
struct BroadsoftHoldEvent
{
};

/**
 * Event passed in SipConversationHandler::onConversationStateChangeRequest(); 
 * this event indicates the remote end sent a request or response that involves
 * SIP functionality that the SDK does not support.
 */
struct ConversationStateChangeRequestEvent
{
};

/**
 * Event passed in SipConversationHandler::onConversationStateChanged().
 * The conversation state changed as a result of a local or remote action.
 * See #ConversationState for a list of possible states.
 */
struct ConversationStateChangedEvent
{
   ConversationState conversationState;
   DialogId dialogId;
   cpc::string contactHeaderField;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   cpc::string alertInfoHeader;
   CPCAPI2::SipConversation::AnswerModeSettings answerMode;
   int responseCode;
   cpc::string statusText;
   EntendedStateInfo extendedStateInfo;
   cpc::vector<HistoryInfo> historyInfo;
   cpc::string sessionId; // RFC 7329: value of Session-ID header
   unsigned int responseTimeMs;

   ConversationStateChangedEvent()
   : conversationState(ConversationState_None),
     responseCode(0),
     responseTimeMs(0)
   {}
};

/**
* Event passed in SipConversationHandler::onConversationMediaChangeRequest().
* The remote party wants to change the set of media and/or directions of media,
* or requires the local party to send a media offer.
*/
struct ConversationMediaChangeRequestEvent
{
   cpc::vector<MediaInfo> remoteMediaInfo;
};

/**
* Event passed in SipConversationHandler::onConversationMediaChanged().
* Gives the updated local media state after a successful or unsuccessful offer/answer exchange.
*/
struct ConversationMediaChangedEvent
{
   cpc::vector<MediaInfo> localMediaInfo;
   cpc::vector<MediaInfo> remoteMediaInfo;
   bool localHold;
   bool remoteHold;
   cpc::string assertedIdentity;
};

/**
* Event passed in SipConversationHandler::onError(), used to report general SDK error 
* conditions, such as invalid handles, or to report cases 
* where the call is not in a valid state for the requested operation.
*/
struct ErrorEvent
{
   cpc::string errorText;
};

/**
 * Event passed in SipConversationHandler::onConversationStatisticsUpdated().
 * Holds updated conversation statistics after refreshConversationStatistics() is called.
 */
struct ConversationStatisticsUpdatedEvent
{
   ConversationStatistics conversationStatistics;
   JitterBufferStatistics jitterBufferStatistics;
};

/**
* Event passed in AudioHandler::onAudioDeviceLevelChange().
*/
struct ConversationAudioDeviceLevelChangeEvent
{
   unsigned int      inputDeviceLevel;
   unsigned int      outputDeviceLevel;
};

/**
* Handler for events relating to incoming, outgoing, and established 
* phone calls; set in SipConversationManager::setHandler().
*/
class SipConversationHandler
{
public:
   /**
    * Notifies the application when a new conversation has been created.
    * An outgoing INVITE has been submitted to the SIP stack for transmission, OR
    * an outgoing INVITE has forked, OR
    * an incoming INVITE has been received.
    */
   virtual int onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args) = 0;

   /**
    * Notifies the application when a conversation has ended.
    */
   virtual int onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args) = 0;

   /**
    * Notifies the application when an incoming transfer request has been received.
    */
   virtual int onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args) = 0;

   /**
    * Notifies the applicaiton when an incoming SIP 302 response received as a result of an outgoing INVITE.
    */
   virtual int onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args) = 0;

   virtual int onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args) = 0;

   /**
    * Notifies the application when an incoming REFER (Refer-To: BYE) is received.  Used in 3PCC (third-party call control)
    * call flows when
    * the controlling endpoint requests the controlled endpoint to hang up.
    */
   virtual int onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args) = 0;

   /**
    * Notifies the application when an incoming Broadsoft 'talk' event has been received.
    */
   virtual int onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args) = 0;

   /**
    * Notifies the application when an incoming Broadsoft 'hold' event has been received.
    */
   virtual int onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args) = 0;

   /**
    * Notifies the application on status updates once an attended transfer is initiated.
    * Taken from the NOTIFY w/Event: refer requests issued by the transferee.
    */
   virtual int onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args) = 0;
   
   /**
    * Notifies the application when SIP response to transfer REFER is received.
    * Applications should not normally need to handle this event or react to it;
    * use onTransferProgress(..) instead.
    */
   virtual int onTransferResponse(SipConversationHandle conversation, const TransferResponseEvent& args) { return CPCAPI2::kSuccess; };

   /**
    * Notifies the application when the remote end sent a request or response that involves SIP functionality that the SDK does not support.
    */
   virtual int onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args) = 0;

   /**
    * Notifies the application when the conversation state changed as a result of a local or remote action.
    */
   virtual int onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args) = 0;

   /**
    * Notifies the application when the remote party wants to change the set of media and/or directions of media, or requires the local 
    * party to send a media offer.
    */
   virtual int onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args) = 0;

   /**
    * Notifies the application on the updated local media state after a successful or unsuccessful offer/answer exchange.
    */
   virtual int onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args) = 0;

   /**
    * Notifies the application on updated conversation statistics after refreshConversationStatistics() is called.
    */
   virtual int onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args) = 0;

   /**
    * Notifies the application on updated audio device levels if %SipConversationManager::startMonitoringAudioDeviceLevels was enabled
    */
   virtual int onAudioDeviceLevelChange(SipConversationHandle conversation, const ConversationAudioDeviceLevelChangeEvent& args) { return CPCAPI2::kSuccess; }

   /**
    * Used to report general SDK error conditions, such as invalid handles, or to report cases where the call is not in a valid state 
    * for the requested operation
    */
   virtual int onError(SipConversationHandle conversation, const ErrorEvent& args) = 0;

};

struct ConversationAdornmentEvent
{
   CPCAPI2::SipAccount::SipAccountHandle account;

   unsigned int adornmentMessageId;

   // request-specific
   cpc::string target;
   cpc::string method;

   // response-specific
   unsigned int responseCode;

   // general
   cpc::string message;
   cpc::string cseqMethod;
};

/**
* Handler for events relating to adornment; set in SipConversationManager::setAdornmentHandler().
*/
class SipConversationAdornmentHandler
{

public:

   /**
    * Notifies the application when an outgoing SIP message related to conversation is ready to be adorned;
    * e.g. outgoing INVITE request, outgoing 180 Ringing response, outgoing 200 OK response, outgoing BYE request.
    */
   virtual int onConversationAdornment(SipConversationHandle conversation, const ConversationAdornmentEvent& args) = 0;

};

}

}

#endif // CPCAPI2_SIP_CONVERSATION_HANDLER_H
