#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_TYPES_H)
#define CPCAPI2_SIP_CONVERSATION_TYPES_H

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include "SipConversationMediaTypes.h"
#include "phone/NetworkChangeManager.h"
#include "cpcapi2defs.h"

namespace CPCAPI2
{
struct HistoryInfo
{
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   cpc::string reason;
};

namespace SipConversation
{
typedef unsigned int SipConversationHandle;

/**
* State of the conversation from the clients point of view. 
* Holds possible values for the ConversationState parameter:
* <ul>
* <li>1: ConversationState_None - No state. Conversation doesn't exist.
* <li>2: ConversationState_LocalOriginated - Local end initiated conversation.
* <li>3: ConversationState_RemoteOriginated - Remote end initiated conversation.
* <li>4: ConversationState_RemoteRinging - Ringing signal is played on remote end.
* <li>5: ConversationState_LocalRinging - Ringing signal is played on local end.
* <li>6: ConversationState_Connected - Conversation is established.
* <li>7: ConversationState_Early - Conversation is in the Early state - typically after getting 18x.
* <li>8: ConversationState_Ended - Conversation is finished.
* </ul>
*/
enum ConversationState
{
   ConversationState_None                 = 0,
   ConversationState_LocalOriginated      = 1000, 
   ConversationState_RemoteOriginated     = 1010, 
   ConversationState_RemoteRinging        = 1020, 
   ConversationState_LocalRinging         = 1030, 
   ConversationState_Connected            = 1040, 
   ConversationState_Early                = 1050,
   ConversationState_Ended                = 1060
};

/**
* Type of the conversation from the clients point of view.
* Holds possible values for the ConversationType parameter:
* <ul>
* <li>1: ConversationType_Incoming - Regular conversation initiated by remote end.
* <li>2: ConversationType_Outgoing - Regular conversation initiated by local end.
* <li>3: ConversationType_IncomingJoinRequest - Join request initiated by remote end. 
*        UAC can be joined to conference or two party conversation (which would then become conference).
* <li>4: ConversationType_IncomingTransferRequest - Transfer conversation request initiated by remote end.
* <li>5: ConversationType_OutgoingNetworkChangeHandover - Handover session using a configured star-code required for a specific network-change handling mode
* </ul>
*/
enum ConversationType
{
   ConversationType_Incoming                      = 1200,
   ConversationType_Outgoing                      = 1210,
   ConversationType_IncomingJoinRequest           = 1220,
   ConversationType_IncomingTransferRequest       = 1230,
   ConversationType_OutgoingNetworkChangeHandover = 1240
};

/**
* Conversation end reason from the clients point of view.
* Holds possible values for the ConversationEndReason parameter:
* <ul>
* <li>1: ConversationEndReason_Unknown - Unknown reason for conversation end.
* <li>2: ConversationEndReason_UserTerminatedLocally - Local party ended conversation.
* <li>3: ConversationEndReason_UserTerminatedRemotely - Remote party ended conversation
* <li>4: ConversationEndReason_ServerError - Conversation ended due to server error.
* <li>5: ConversationEndReason_ServerRejected - Conversation ended because server rejected conversation for some reason.
* <li>6: ConversationEndReason_Redirected - Call is redirected to other user agent.
* <li>7: ConversationEndReason_CallAnsweredElsewhere - Call is already answered on other user agent.
* </ul>
*/
enum ConversationEndReason
{
   ConversationEndReason_Unknown                   = 1100,
   ConversationEndReason_UserTerminatedLocally     = 1110,
   ConversationEndReason_UserTerminatedRemotely    = 1120,
   ConversationEndReason_ServerError               = 1130,
   ConversationEndReason_ServerRejected            = 1140,
   ConversationEndReason_Redirected                = 1150,
   ConversationEndReason_CallAnsweredElsewhere     = 1160
};

/**
* Call quality descriptor.
* Holds possible values for the ConversationCallQuality parameter:
* <ul>
* <li>1: ConversationCallQuality_Unknown - Unknown call quality.
* <li>2: ConversationCallQuality_Good - Good call quality.
* <li>3: ConversationCallQuality_Fair - Fair call quality.
* <li>4: ConversationCallQuality_Poor - Poor call quality.
* </ul>
*/
enum ConversationCallQuality
{
   ConversationCallQuality_Unknown = 0,
   ConversationCallQuality_Good    = 1,
   ConversationCallQuality_Fair    = 2,
   ConversationCallQuality_Poor    = 3
};

/**
* Call transfer progress descriptor.
* Holds possible values for the TransferProgressEventType parameter:
* <ul>
* <li>1: TransferProgressEventType_Trying - Client is attempting to perform transfer.
*        Transfer request is sent and provisional answer is received.
* <li>2: TransferProgressEventType_Ringing - Transfer request is received and remote side
         is being alerted.
* <li>3: TransferProgressEventType_Connected - Transfer is performed successfully.
* <li>4: TransferProgressEventType_Redirected - Transfer is redirected to other location.
* <li>5: TransferProgressEventType_Failed - Transfer failed.
* </ul>
*/
enum TransferProgressEventType
{
   TransferProgressEventType_Trying                = 1300,
   TransferProgressEventType_Ringing               = 1310,
   TransferProgressEventType_Connected             = 1320,
   TransferProgressEventType_Redirected            = 1330,
   TransferProgressEventType_Failed                = 1340
};

/**
* Describes type of the media used in call.
* Holds possible values for the MediaType parameter:
* <ul>
* <li>1: MediaType_Audio - Audio media type.
* <li>2: MediaType_Video - Video media type.
* </ul>
*/
enum MediaType
{
    MediaType_Audio            = 0x1,
    MediaType_Video            = 0x2
};


/**
* Describes media flow direction used in call from the clients point of view.
* Holds possible values for the MediaDirection parameter:
* <ul>
* <li>1: MediaDirection_None - No media flow in either direction. Although it might be present.
         This might be case when both parties put another party on hold.
* <li>2: MediaDirection_SendReceive - Media flows in both directions. From local
         end to remote end and from remote end to local end. 
* <li>3: MediaDirection_SendOnly - Media flows only from local end to remote end.
* <li>4: MediaDirection_ReceiveOnly - Media flows only from remote end to local end.
* <li>5: MediaDirection_Inactive - No media flow in either direction. Media flow is not present.
* </ul>
*/
enum MediaDirection
{
    MediaDirection_None           = 0,
    MediaDirection_SendReceive    = 1,
    MediaDirection_SendOnly       = 2,
    MediaDirection_ReceiveOnly    = 3,
    MediaDirection_Inactive       = 4
};

/**
* Describes media encryption type used in call.
* Holds possible values for the MediaEncryptionMode parameter:
* <ul>
* <li>1: MediaEncryptionMode_Unencrypted - No encryption applied.
* <li>2: MediaEncryptionMode_SRTP_SDES_Encrypted - Media is encrypted using
         Secure Real-time Transport Protocol and 
         Session Description Protocol Security Descriptions.
* <li>3: MediaEncryptionMode_SRTP_DTLS_Encrypted - Media is encrypted using
         Secure Real-time Transport Protocol and 
         Datagram Transport Layer Security
* </ul>
*/
enum MediaEncryptionMode
{
    MediaEncryptionMode_Unencrypted          = 0x1,
    MediaEncryptionMode_SRTP_SDES_Encrypted  = 0x2,
    MediaEncryptionMode_SRTP_DTLS_Encrypted  = 0x4
};

/**
 * Describes cipher suite used for encryption in SRTP.
 * Holds possible values for the MediaCryptoSuite parameter:
 * <ul>
 * <li>1: MediaCryptoSuite_None - No suite supported
 * <li>2: MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32 - 128-bit AES with 32-bit SHA-1 HMAC
 * <li>3: MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80 - 128-bit AES with 80-bit SHA-1 HMAC
 * <li>4: MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32 - 256-bit AES with 32-bit SHA-1 HMAC
 * <li>5: MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 - 256-bit AES with 80-bit SHA-1 HMAC
 * <li>2: MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32 - 192-bit AES with 32-bit SHA-1 HMAC
 * <li>3: MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80 - 192-bit AES with 80-bit SHA-1 HMAC
 * <li>4: MediaCryptoSuite_AEAD_AES_128_GCM        - 128-bit AES-GCM with 16-bit auth
 * <li>5: MediaCryptoSuite_AEAD_AES_256_GCM        - 256-bit AES-GCM with 16-bit auth
 * </ul>
*/
enum MediaCryptoSuite
{
   MediaCryptoSuite_None                    = 0,
   MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32 = 1,
   MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80 = 2,
   MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32 = 3,
   MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80 = 4,
   MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32 = 5,
   MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80 = 6,
   MediaCryptoSuite_AEAD_AES_128_GCM        = 10,
   MediaCryptoSuite_AEAD_AES_256_GCM        = 11
};

/**
* Struct holding media encryption requirements in a call.
* <ul>
* <li>1: mediaEncryptionMode - Media encryption mode used in call.
* <li>2: secureMediaRequired - Is it required to use media security in call.
* <li>3: mediaCryptoSuite - Media crypto suite used for SRTP encryption; lower priority suites should be at the start
* </ul>
*/
struct MediaEncryptionOptions
{
   MediaEncryptionMode             mediaEncryptionMode;
   bool                            secureMediaRequired;
   cpc::vector<MediaCryptoSuite>   mediaCryptoSuites;

   MediaEncryptionOptions()
   {
      mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
      secureMediaRequired = false;

      // All cryptos are enabled by default, but can be customized by the application
      mediaCryptoSuites.push_back(MediaCryptoSuite_AEAD_AES_128_GCM);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AEAD_AES_256_GCM);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32);
      mediaCryptoSuites.push_back(MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32);
   }
};

/**
* A struct describing media used in a call.
* <ul>
* <li>1: mediaType - type of media used in a call.
* <li>2: mediaDirection - media flow direction in a call.
* <li>3: mediaCrypto - media crypto selected in a call.
* <li>4: mediaEncryptionOptions - media encryption options applied to a call.
* <li>5: audioCodec - audio codec used in a call.
* <li>6: videoCodec - video codec used in a call.
* <li>7: conferenceMixContribution
* <li>8: mediaStreamId
* </ul>
*/ 
struct MediaInfo
{
    MediaType               mediaType;
    MediaDirection          mediaDirection;
    MediaCryptoSuite        mediaCrypto;
    MediaEncryptionOptions  mediaEncryptionOptions;
    AudioCodec              audioCodec;
    VideoCodec              videoCodec;
    unsigned int            conferenceMixContribution;
    bool                    isLocallyDisabled;
    /**
    * The conference mix that the conversation will be part of.
    * All conversations that share the same conference mix ID will
    * have their audio/video mixed locally.
    * Note that this setting currently applies to MCU mix mode only.
    * By default, each SIP account has its own mix id.
    */
    int                     conferenceMixId;
    int                     mediaStreamId;
    unsigned int            videoCaptureDeviceId;

    MediaInfo(MediaType type = MediaType_Audio, MediaDirection direction = MediaDirection_SendReceive)
    {
       mediaType = type;
       mediaDirection = direction;
       mediaCrypto = MediaCryptoSuite_None;

       conferenceMixContribution = 100;
       conferenceMixId = -1;
       mediaStreamId = -1;
       videoCaptureDeviceId = -1;
       isLocallyDisabled = false;
    }
};

/**
* Describes DTMF standard(s) used by client.
* <ul>
* <li>1: DtmfMode_RFC2833 - send DTMF tones as recommended in RFC 2833.
* <li>2: DtmfMode_InBand - send DTMF tones within the same band or channel used for voice.
* <li>3: DtmfMode_SIP_INFO - use SIP network elements to transmit DTMF tones out-of-band 
*        as telephone-events independent of the media stream.
* <li>4: DtmfMode_RFC2833_InBand - combination of DtmfMode_RFC2833 and DtmfMode_InBand
* <li>5: DtmfMode_RFC2833_SIP_INFO - combination of DtmfMode_RFC2833 and DtmfMode_SIP_INFO
* <li>6: DtmfMode_InBand_SIP_INFO - combination of DtmfMode_InBand and DtmfMode_SIP_INFO
* <li>7: DtmfMode_Everything - combination of all three types.
* </ul>
*/
enum DtmfMode
{
   DtmfMode_RFC2833          = 1,
   DtmfMode_InBand           = 2,
   DtmfMode_SIP_INFO         = 3,
   DtmfMode_RFC2833_InBand   = 4,
   DtmfMode_RFC2833_SIP_INFO = 5,
   DtmfMode_InBand_SIP_INFO  = 6,
   DtmfMode_Everything       = 7
};

/**
* Describes available options for NAT traversal.
* <ul>
* <li>1: NatTraversalMode_None - No NAT traversal.
* <li>2: NatTraversalMode_Auto - Attempt ICE if a NAT traversal server is discovered via SRV lookup or specified explicitly; candidates used depends on natTraversalServerType
* <li>3: NatTraversalMode_STUN - Use public IP address discovered via STUN for SDP c-line
* <li>4: NatTraversalMode_TURN - Use ICE with only TURN candidates
* <li>5: NatTraversalMode_ICE - Use ICE; candidates used depends on natTraversalServerType
* </ul>
*/
enum NatTraversalMode
{
   NatTraversalMode_None   = 0,  /* No NAT traversal */
   NatTraversalMode_Auto   = 1,  /* attempt ICE if a NAT traversal server is discovered via SRV lookup or specified explicitly; candidates used depends on natTraversalServerType */
   NatTraversalMode_STUN   = 2,  /* use public IP address discovered via STUN for SDP c-line */
   NatTraversalMode_TURN   = 3,  /* use ICE with only TURN candidates */
   NatTraversalMode_ICE    = 4   /* use ICE; candidates used depends on natTraversalServerType */
};

/**
* Describes standard used when call is put on hold.
* <ul>
* <li>1: HoldMode_RFC3264 - Use RFC 3264 standard.
* <li>2: HoldMode_RFC2543 - Use RFC 2543 standard.
* </ul>
*/
enum HoldMode
{
   HoldMode_RFC3264        = 0x1,
   HoldMode_RFC2543        = 0x2
};

/**
* Describes provisional acceptance (PRACK) regime used by client.
* <ul>
* <li>1: PrackMode_Disabled - PRACK use is not supported.
* <li>2: PrackMode_Supported - PRACK use is supported.     
* <li>3: PrackMode_Required - PRACK use is required.
* <li>4: PrackMode_SupportUasAndUac - PRACK use is supported both as server and client.
* </ul>
*/
enum PrackMode
{
   PrackMode_Disabled            = 0,
   PrackMode_Supported           = 1,
   PrackMode_Required            = 2,
   PrackMode_SupportUasAndUac    = 3
};

/**
* Statistics from RTCP sender and receiver reports.
*/
struct ConversationStatistics
{
   cpc::vector<AudioStatistics> audioChannels;
   cpc::vector<RemoteAudioStatistics> remoteAudioChannels;
   cpc::vector<VideoStatistics> videoChannels;
   cpc::vector<RemoteVideoStatistics> remoteVideoChannels;
   ConversationCallQuality callQuality;
   /**
    * Network MOS - experimental: do not use
    */
   short networkMos;

   ConversationStatistics()
   {
      callQuality = ConversationCallQuality_Unknown;
      networkMos = -1;
   }
};

/**
* Jitter buffer statistics.
*/
struct JitterBufferStatistics
{
   cpc::vector<AudioJitterBufferStatistics> audioChannels;
   cpc::vector<VideoJitterBufferStatistics> videoChannels;
};

/**
* Holds possible values for the NatTraversalServerSourceType parameter.
*/
enum NatTraversalServerSourceType
{
   /**
   * When this flag is specified, no NAT traversal server will be used.
   */
   NatTraversalServerSource_None    = 0,

   /**
   * When this flag is specified, the NAT traversal server found as a result
   * of a DNS SRV lookup on the configured domain will be used.
   */
   NatTraversalServerSource_SRV     = 1,

   /**
   * When this flag is specified, the configured NAT traversal server will be used.
   */
   NatTraversalServerSource_Custom  = 2
};

/**
* Specifies the type of NAT traversal server specified by 
* SipConversationSettings::natTraversalServerSource
* when NatTraversalServerSource_Custom or
* NatTraversalServerSource_SRV is set.
*/
enum NatTraversalServerType
{
   NatTraversalServerType_StunAndTurn    = 0,
   NatTraversalServerType_StunOnly       = 1,
   NatTraversalServerType_TurnOnly       = 2
};

/**
* Describes the current answer-mode options as per RFC 5373.
* <ul>
* <li>1: AnswerMode_Disabled - answer mode use is disabled for outgoing calls
* <li>2: AnswerMode_Manual - enable answer mode and suggest manual mode to remote entity
* <li>3: AnswerMode_Auto- enable answer mode and suggest auto mode to remote entity
* </ul>
*/
enum AnswerMode
{
   AnswerMode_Disabled              = 0,
   AnswerMode_Manual                = 1,
   AnswerMode_Auto                  = 2
};

struct AnswerModeSettings
{
   // Outgoing Answer-Mode attributes
   AnswerMode mode;
   bool privileged;
   bool required;
   // bool isExplicit;

   // Attributes for handling incoming Answer-Mode requests
   bool challenge;
   bool allowManual;
   bool allowAuto;
   bool allowPrivileged;

   AnswerModeSettings() : mode(AnswerMode_Disabled), privileged(false), required(false), challenge(true), allowManual(true), allowAuto(true), allowPrivileged(true) {}
   AnswerModeSettings(AnswerMode mode_, bool privileged_, bool required_) : mode(mode_), privileged(privileged_), required(required_), challenge(true), allowManual(true), allowAuto(true), allowPrivileged(true) {}
   AnswerModeSettings(AnswerMode mode_, bool privileged_, bool required_, bool challenge_, bool allowManual_, bool allowAuto_, bool allowPrivileged_) : mode(mode_), privileged(privileged_), required(required_), challenge(challenge_), allowManual(allowManual_), allowAuto(allowAuto_), allowPrivileged(allowPrivileged_) {}
   virtual ~AnswerModeSettings() {}
};

struct SessionDescription
{
   cpc::string sdpString;
   unsigned short sdpLen;

   enum SessionDescriptionType
   {
      SessionDescriptionType_Offer = 0,
      SessionDescriptionType_Answer,
      SessionDescriptionType_Pranswer
   };
   SessionDescriptionType sdpType;

   SessionDescription() : sdpLen(0), sdpType(SessionDescriptionType_Offer)
   {
   }

   virtual ~SessionDescription() {}
};

enum EntendedStateInfo
{
   ExtendedStateInfo_None = 0,
   EntendedStateInfo_RemoteRinging_Ringing = 1,
   EntendedStateInfo_RemoteRinging_CallIsBeingForwarded = 2,
   EntendedStateInfo_RemoteRinging_Queued = 3
};

/**
* Describes the network change handover mode
* <ul>
* <li>1: NetworkChangeHandoverMode_Reinvite - default reinvite behaviour
* <li>2: NetworkChangeHandoverMode_Starcode - for handling network change handover with star-code sessions
* </ul>
*/
enum NetworkChangeHandoverMode
{
   NetworkChangeHandoverMode_Reinvite              = 0,
   NetworkChangeHandoverMode_Starcode              = 1
};

}

/**
* A struct. Used with SipConversationManager::setDefaultSettings(..) to control the behaviour of the SDK
* with respect to INVITE-session-related functionality.
*/
struct SipConversationSettings
{
   /**
   * Sets the session name that appears in SDP bodies sent by the SDK.
   */
   cpc::string                           sessionName;

   /**
   * Sets the NAT traversal mode used for media.
   */
   SipConversation::NatTraversalMode      natTraversalMode;

   /**
   * The source of the NAT traversal server to use.
   */
   SipConversation::NatTraversalServerSourceType natTraversalServerSource;

   /**
   * Sets the address of the NAT traversal server (STUN or TURN). 
   * Only used when natTraversalServerSource == NatTraversalServerSource_Custom.
   */
   cpc::string                            natTraversalServer;

   /**
   * Specifies the type of NAT traversal server specified in natTraversalServer.
   */
   SipConversation::NatTraversalServerType natTraversalServerType;

   /**
   * Used to specify the RFC support level for call 'hold' functionality.
   */
   SipConversation::HoldMode              holdMode;

   /**
   * Used to specify the PRACK support. Can only be set before account is enabled. If the account has been enabled, it must be disabled and re-enabled to change the PRACK mode.
   */
   SipConversation::PrackMode             prackMode;

   /**
    * Used to specify the attributes of the Answer-Mode functionality as per RFC 5373. Advertising the support for Answer-Mode functionality in the register request is controlled from the useAnswerMode account setting..
   */
   SipConversation::AnswerModeSettings    answerMode;

   /**
    * Used to specify how network changes should be handled for active calls, i.e. using default reinvite behaviour or using star-code sessions
   */
   SipConversation::NetworkChangeHandoverMode networkChangeHandoverMode;

   /**
    * Used to specify the star-code required when handling network changes using star-code handover sessions
   */
   cpc::string networkChangeHandoverStarcode;

   /**
   * Used to specify the RTP port range
   */
   unsigned int                           minRtpPort;
   unsigned int                           maxRtpPort;

   /**
   * Used to specify the audio RTP port range
   */
   unsigned int                           minRtpPortAudio;
   unsigned int                           maxRtpPortAudio;

   /**
   * Used to specify the video RTP port range
   */
   unsigned int                           minRtpPortVideo;
   unsigned int                           maxRtpPortVideo;

   /**
   * Used to specify the TURN username and password
   */
   cpc::string                            turnUsername;
   cpc::string                            turnPassword;

   /**
   * If true, the P-Preferred-Identity header will be included in outgoing INVITE requests
   */
   bool                                   includePPreferredIdentity;

   /**
   * If true, the P-Asserted-Identity header will be included in 200 OK responses
   */
   bool                                   includePAssertedIdentity;

   /**
   * If true, these attribute lines will be included in the SDP:
   *
   * a=rtpmap:9 G722/8000
	* a=rtpmap:8 PCMA/8000
	* a=rtpmap:0 PCMU/8000
   */
   bool                                   includeAttribsForStaticPLs;

   /*
    * Whether or not to include call transfer related SIP messages in adornment events,
    * if conversation adornment is enabled; e.g. NOTIFY, REFER request/responses.
    * To avoid breaking backwards compatability with older SDK versions that did not
    * fire adornment events for all these messages, this is disabled by default.
   */
   bool                                   adornTransferMessages;

   SipConversationSettings()
   {
      sessionName = " ";
      natTraversalMode = SipConversation::NatTraversalMode_None;
      natTraversalServerSource = SipConversation::NatTraversalServerSource_Custom;
      natTraversalServer = "";
      natTraversalServerType = SipConversation::NatTraversalServerType_StunAndTurn;
      holdMode = SipConversation::HoldMode_RFC3264;
      prackMode = SipConversation::PrackMode_Disabled;
      answerMode.mode = SipConversation::AnswerMode_Disabled;
      networkChangeHandoverMode = SipConversation::NetworkChangeHandoverMode_Reinvite;
      networkChangeHandoverStarcode = "";
      minRtpPort = 0;
      maxRtpPort = 0;
      minRtpPortAudio = 0;
      maxRtpPortAudio = 0;
      minRtpPortVideo = 0;
      maxRtpPortVideo = 0;
      turnUsername = "";
      turnPassword = "";
      includePPreferredIdentity = false;
      includePAssertedIdentity = false;
      includeAttribsForStaticPLs = false;
      adornTransferMessages = false;
   }
};

class PlaySoundStream
{
public:
   virtual ~PlaySoundStream() {}
   // Reads |length| bytes from file to |buf|. Returns the number of bytes read
   // or -1 on error.
   virtual int Read(void *buf, size_t len) = 0;
   virtual int Rewind() = 0;

protected:
   PlaySoundStream() {}
};

}

#endif // CPCAPI2_SIP_CONVERSATION_TYPES_H
