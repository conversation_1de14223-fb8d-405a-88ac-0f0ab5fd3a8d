#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_MANAGER_H)
#define CPCAPI2_SIP_CONVERSATION_MANAGER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "cpcapi2types.h"
#include "SipConversationTypes.h"
#include "media/video/Video.h"

namespace CPCAPI2
{ 
class Phone;

namespace SipConversation 
{
class SipConversationHandler;
class SipConversationAdornmentHandler;

/**
* Manager interface to control incoming, outgoing and established phone calls; 
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API SipConversationManager
{
public:
   /**
   * Get a reference to the %SipConversationManager interface.
   */   
   static SipConversationManager* getInterface(Phone* cpcPhone);

   /**
   * Set the handler for call events on the specified account. Set the handler
   * immediately after creating the account.
   *
   * To un-register the handler, pass NULL for handler. Must be called on the same thread as SipAccount::process(..)
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConversationHandler* handler) = 0;

   /**
   * Set default settings which apply to all incoming and outgoing calls on the specified account.
   * Invoked immediately after creating the account.
   */
   virtual int setDefaultSettings(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const CPCAPI2::SipConversationSettings& settings) = 0;

   /**
   * Set default settings which apply to all incoming and outgoing calls on the specified account, 
   * for specified transport type. Invoked immediately after creating the account.
   */
   virtual int setDefaultSettings(
         CPCAPI2::SipAccount::SipAccountHandle account,
         const CPCAPI2::SipConversationSettings& settings,
         CPCAPI2::NetworkTransport transport) = 0;

   /**
    * DEPRECATED: No longer necessary to call.
    *
    * This function should be called when an application initiates a push-call. It ensures that the network
    * interfaces are reset in case there were any network changes when the application was in the background.
    * It also allows the SDK to perform any other preliminary tasks required for handling push-calls.
    */
   DEPRECATED virtual int setCallInitiatedFromPush(SipConversationHandle conversation) = 0;
   
   /**
   * Allocates a new call within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing call.
   */
   virtual SipConversationHandle createConversation(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Adds a participant to the call.  Call this function after createConversation(..) and before start(..).
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int addParticipant(SipConversationHandle conversation, const cpc::string& targetAddress) = 0;

   /**
   * Changes the set of media offered by the SDK.  May be called:
   * <ul>
   * <li>on initial outgoing conversation, prior to calling start(), in order to set the media offer.
   * <li>on an incoming conversation, prior to calling accept(), in order 
   * to provide a counter-offer for the media.
   * <li> on an already established call, prior to calling sendMediaChangeRequest(),
   * in order to change the media.
   */
   virtual int configureMedia(SipConversationHandle conversation, const MediaInfo& mediaDescriptor) = 0;

   /**
   * Enables or disables a particular media type for the conversation.
   * Note: This is a simple convenience function and has the same effect as manually specifying media
   * information using configureMedia(..)
   * For the 'disable' case, results in the SDP m= line for the media getting disabled (port == 0).
   * May be invoked prior to starting the conversation, or after start(..) has already been invoked.
   * Used prior to calling sendMediaChangeRequest(..) to send a re-INVITE request with a new SDP offer reflecting the
   * enabled/disabled states of the media streams on the call.
   */
   virtual int setMediaEnabled(SipConversationHandle conversation, MediaType mediaType, bool enabled) = 0;

   /**
   * Enables or disables a particular media type for the conversation.
   * Note: This is a simple convenience function and has the same effect as manually specifying media
   * information using configureMedia(..)
   * For the 'disable' case, results in the SDP m= line direction getting set to 'inactive'.
   * May be invoked prior to starting the conversation, or after start(..) has already been invoked.
   * Used prior to calling sendMediaChangeRequest(..) to send a re-INVITE request with a new SDP offer reflecting the
   * enabled/disabled states of the media streams on the call.
   */
   virtual int setMediaEnabledByDirection(SipConversationHandle conversation, MediaType mediaType, bool enabled) = 0;

   /**
   * Use this function to enable RFC 3323 privacy mechanisms for an outgoing call prior to calling start(..).
   */
   virtual int setAnonymousMode(SipConversationHandle conversation, unsigned int anonymousMode) = 0;

   /**
   * When this feature is enabled, it tries to setup a call using RTP/SAVP, if it fails it then tries
   * to setup a call using RTP/AVP. Note that there is no need to configure the media encryption related settings
   * for the call in this case as it is handled automatically.
   */
   virtual int setBestEffortMediaEncryption(SipConversationHandle conversation, bool enabled) = 0;
   
   /**
    * Used to specify the preferred crypto suites when media encryption has been enabled. Lower priority suites should be at the start.
   */
   DEPRECATED virtual int setMediaCryptoSuites(SipConversationHandle conversation, cpc::vector<MediaCryptoSuite> cryptoSuites) = 0;
   
   /**
    * Used to specify the preferred crypto suites for a particular media-type, when media encryption has been enabled. Lower priority suites should be at the start.
   */
   virtual int setCryptoSuitesForMedia(SipConversationHandle conversation, CPCAPI2::SipConversation::MediaType mediaType, cpc::vector<MediaCryptoSuite> cryptoSuites) = 0;

   /**
   * Initiates an outgoing call by sending an INVITE to the remote participant (see addParticipant(..)).
   */
   virtual int start(SipConversationHandle conversation) = 0;

   /**
   * Places the call on hold.  Results in an outgoing re-INVITE request with media attributes
   * set such that remote media is no longer received.  Regardless of the success/failure of the re-INVITE
   * transaction, the SDK will cease sending media from the mic/camera to the remote party.
   */
   virtual int hold(SipConversationHandle conversation) = 0;

   /**
   * Takes the call off hold.  See hold(..).
   */
   virtual int unhold(SipConversationHandle conversation) = 0;

   /**
   * Sends a re-INVITE requesting a change in the set of negotiated media streams.
   * For example, can be used to upgrade an audio-only call to audio-video.
   * Call this function after calling setMedia(..).
   */
   virtual int sendMediaChangeRequest(SipConversationHandle conversation) = 0;

   /**
   * Ends a call.  Sends an outgoing BYE or CANCEL request depending on the current conversation state.
   */
   virtual int end(SipConversationHandle conversation) = 0;

   /**
   * Redirects an incoming call to a new target address.  Sends a SIP 302 response.
   */
   virtual int redirect(SipConversationHandle conversation, const cpc::string& targetAddress, const cpc::string& reason) = 0;

   /**
   * Used after receiving an incoming call to indicate to the remote end that the local user agent is 'ringing'.
   */
   virtual int sendRingingResponse(SipConversationHandle conversation) = 0;

   /**
   * Used after receiving an incoming call to reject the INVITE session offered by the remote party.
   * @param conversation The incoming call to reject.
   * @param rejectReason The SIP response code sent to the caller. 0 will automatically select 486 for INVITE, 488 for RE-INVITE
   */
   virtual int reject(SipConversationHandle conversation, unsigned int rejectReason=0) = 0;

   /**
   * Used to answer an incoming call (200 OK with SDP answer).
   */
   virtual int accept(SipConversationHandle conversation) = 0;

   /**
   * Accept an incoming transfer request.  Results in a new outgoing call to the transfer target.
   * See TransferRequestEvent.
   */
   virtual int acceptIncomingTransferRequest(SipConversationHandle transferTargetConversation) = 0;

   /**
   * Reject an incoming transfer request.
   */
   virtual int rejectIncomingTransferRequest(SipConversationHandle transferTargetConversation) = 0;

   /**
   * Transfers the call specified as the transfereeConversation to the remote party of the transferTargetConversation.
   * This is a SIP attended transfer.
   * Typically the transferee would be "on hold", and the transferTargetConversation would be "established"
   * prior to calling this function.
   */
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation) = 0;

   /**
   * Transfers the call specified as the transfereeConversation to the remote party of the transferTargetConversation.
   * This is a SIP attended transfer.
   * Typically the transferee would be "on hold", and the transferTargetConversation would be "established"
   * prior to calling this function.
   */
   virtual int transfer(SipConversationHandle transferTargetConversation, SipConversationHandle transfereeConversation, bool endTargetConversationOnSuccess) = 0;

   /**
   * Transfers the call specified as the transfereeConversation to the address specified by targetAddress.
   * This is a SIP basic transfer.
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int transfer(SipConversationHandle transfereeConversation, const cpc::string& targetAddress) = 0;

   /**
    * Assigns a preference to the specified DtmfMode, where ordinal 0 is the DtmfMode that will
    * be used first (whenever possible).  Other modes (ordinal 1, 2, ...) will be attempted under
    * the following conditions:
    * RFC 2833:  failure to negotiate the telephone-event codec in SDP
    * In Band:  negotiation of a voice codec such as G.729 which distorts inband DTMF
    * SIP INFO:  reception of an error response after sending a DTMF INFO to the remote target
    *
    * Specify ordinal 0xFFFFFFFF to completely clear the preferred order.
    *
    * @param account SipAccount to apply the DTMF mode preference to.
    * @param ordinal Priority ordering of the DTMF mode relative to other DTMF modes.
    * @param dtmfMode One of the values from the DtmfMode enumeration.
    */
   virtual int setDtmfMode(CPCAPI2::SipAccount::SipAccountHandle account, unsigned int ordinal, DtmfMode dtmfMode) = 0;

   /**
    * Starts playing a DTMF tone, according to the DTMF mode selected for the SipAccount via setDtmfMode(..).
    * @param conversation Conversation to play the tone for.  May be 0 if only local playback is desired.
    * @param toneId See http://tools.ietf.org/html/rfc4733#section-3.2 for toneId semantics.
    * @param playLocally True to play the DTMF tone to the local Audio playout device.
    */
   virtual int startDtmfTone(SipConversationHandle conversation, unsigned int toneId, bool playLocally) = 0;

   /**
    * Stops playing all DTMF tones.
    */
   virtual int stopDtmfTone() = 0;

   /**
    * Assigns a preference for answer-mode as per RFC 5373. It allows the call originator to specify the
    * call-handling preference to the call-target, i.e. auto-answer or manual and if the answer mode is mandatory
    * Selecting mandatory will result in the UAS rejecting the call if the answer mode is not supported
   */
   virtual int setAnswerMode(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::SipConversation::AnswerModeSettings answerMode, CPCAPI2::NetworkTransport transport) = 0;

   /**
    * Requests the latest statistics for the conversation. Use while the call is established,
    * or once the call has been terminated.  See SipConversationHandler::onConversationStatisticsUpdated(..)
    * DEPRECATED: Use the version of refreshConversationStatistics that takes the 'includeJitterStatistics' parameter.
    * NOTE: Detailed jitter buffer statistics will NOT be included when using this deprecated version of the function.
    */
   virtual int refreshConversationStatistics(SipConversationHandle conversation) = 0;

   /**
    * Requests the latest statistics for the conversation. Use while the call is established,
    * or once the call has been terminated.  See SipConversationHandler::onConversationStatisticsUpdated(..)
    * @param conversation Conversation to get statistics for.
    * @param includeNetworkStatistics True to include network-related statistics (packets received, lost, etc.)
    * @param includeJitterStatistics True to include jitter buffer statistics.
    * @param includeRemoteStatistics True to include the RemoteAudioStatistics and RemoteVideoStatistics data.
    *
    * WARNING: Retrieving conversation statistics is a relatively expensive operation.  It is recommended
    *          that applications only call this function when absolutely necessary.  Also, if the
    *          application is not concerned with detailed jitter buffer statistics, it is recommended
    *          to set the includeJitterStatistics to 'false'.
    */
   virtual int refreshConversationStatistics(SipConversationHandle conversation, bool includeNetworkStatistics, bool includeJitterStatistics, bool includeRemoteStatistics) = 0;

   /**
   * Set the handler for call events on the specified account. Set the handler
   * immediately after creating the account. Must be called on the same thread as SipAccount::process(..)
   *
   * To un-register the handler, pass NULL for handler.
   */
   virtual int setAdornmentHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConversationAdornmentHandler* handler) = 0;

   virtual int adornMessage(SipConversationHandle conversation, unsigned int adornmentMessageId, const cpc::vector<SipHeader>& customHeaders) = 0;
	
   /**
    * Plays the specified sound remotely.
    *
    * Supported URI formats:
    *
    * DTMF tones, e.g. tone:n;duration=ms where n is 0..15 (standard) and 16..38 (extended) and ms is the duration in milliseconds (optional).
    * Resource URIs, e.g. file:/home/<USER>
    *
    * Standard DTMF tones:
    *
    * 0-9: 0-9
    * 10: *
    * 11: #
    * 12: A
    * 13: B
    * 14: C
    * 15: D
    *
    * Extended DTMF tones:
    *
    * 16: Flash
    * 17-26: Bellcore various cadences
    * 27: Dialtone with forwarding
    * 28: Dialtone with message waiting
    *
    * 31: Busy
    * 32: Ringback
    * 33: Dialtone
    * 34: Reorder
    * 35: Call waiting
    * 36: North American ringback
    * 37: British ringback
    * 38: European ringback
    *
    * @param resourceUri specifies the location of the audio data to be played.
    * @param repeat specifies necessary to play sound in loop 
    */
   virtual int playSound(SipConversationHandle conversation,const cpc::string& resourceUri, bool repeat) = 0;

   /**
   * Plays the specified sound stream remotely.
   * @param playSoundStream the audio data to be played.
   * @param repeat specifies necessary to play sound in loop
   */
   virtual int playSound(SipConversationHandle conversation, PlaySoundStream* playSoundStream, bool repeat) = 0;

   /**
    * Stop playing the specified sound.  Note that it is only necessary to call this if you wish to prematurely stop
    * the playing sound, or if you wish to stop playback when playSound was called with 'repeat' set to 'true'.
    */
   virtual int stopPlaySound(SipConversationHandle conversation) = 0;

   /**
    * Start monitoring audio device levels (input and output) for the specified conversation.
    * See SipConversationHandler::onAudioDeviceLevelChange(..)
    */
   virtual int startMonitoringAudioDeviceLevels(SipConversationHandle conversation) = 0;

   /**
    * Stop monitoring audio device levels (input and output) for the specified conversation.
    */
   virtual int stopMonitoringAudioDeviceLevels(SipConversationHandle conversation) = 0;

   /**
    * Decodes a provisioning response received from the Provisioning interface.
    * @param provisioningResponse The complete provisioning response, as returned by the Provisioning interface.
    * @param outConversationSettings A vector of SipConversationSettings; the number of items in the vector will be equivalent to the number of sets
    * of conversation settings in the provisioning response. If a particular setting was not provisioned down, the SDK will use a default value.
    */
   virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<SipConversationSettings>& outConversationSettings) = 0;

   /**
    * Get the current call count.
    */
   virtual int getCallCount() = 0;

   /**
    * iOS specific. Enables CallKit mode for conversation. To be called from within CallKit's
    * reportNewIncomingCallWithUUID:update:completion: handler or provider:performStartCallAction:
    * handler. Failure to call may result in audio problems with the call. For further details, see the
    * iOS developer guide bundled with this SDK.
    */
   virtual int setCallKitMode(SipConversationHandle conversation) = 0;

   /**
    * Android specific. Turns on Telecom framework mode for this call.
    * The Telecom framework will be responsible for audio routing; Audio::setCaptureDevice/Audio::setRenderDevice will have no effect.
    * Must be called before start() for outgoing calls or sendRingingResponse() for incoming calls.
    */
   virtual int setTelecomFrameworkMode(SipConversationHandle conversation) = 0;

   /**
    * Sets a video render surface to be used for the incoming video stream of the specified conversation.
	*/
   virtual int setIncomingVideoRenderTarget(SipConversationHandle conversation, void* surface, Media::VideoSurfaceType type = Media::VideoSurfaceType_Default) = 0;

   /**
    * Sets the delay after the end of a call before the audio device is closed.
    * audioDeviceCloseDelay: delay in milliseconds
    */
   virtual int setAudioDeviceCloseDelay(SipConversationHandle conversation, int audioDeviceCloseDelay) = 0;

protected:
   /*
    * The SDK will manage memory life of %SipConversationManager.
    */
   virtual ~SipConversationManager() {}
};

}

}

#endif // CPCAPI2_SIP_CONVERSATION_MANAGER_H
