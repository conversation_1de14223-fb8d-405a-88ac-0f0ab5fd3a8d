#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_MEDIA_TYPES_H)
#define CPCAPI2_SIP_CONVERSATION_MEDIA_TYPES_H

#include <stdint.h>
#include <cpcstl/string.h>

namespace CPCAPI2
{
namespace SipConversation
{
/**
* A struct. 
*/ 
   struct AudioCodec
   {
      int   pltype;
      char  plname[32];
      int   plfreq;
      int   pacsize;
      int   channels;
      int   rate;
      unsigned int priority;
      cpc::string displayName;

      AudioCodec()
      {
         pltype = 0;
         plname[0] = 0;
         plfreq = 0;
         pacsize = 0;
         channels = 0;
         rate = 0;
         priority = 0;
      }
   };

/**
* A struct. 
*/ 
   struct VideoCodec
   {
      char                    plName[32];
      unsigned char           plType;

      unsigned short          width;
      unsigned short          height;

      unsigned int            startBitrate;
      unsigned int            maxBitrate;
      unsigned int            minBitrate;
      unsigned char           maxFramerate;

      bool                    hadwareAccelerated;
      unsigned int priority;
      
      cpc::string             displayName;

      VideoCodec()
      {
         plName[0] = 0;
         plType = 0;
         width = 0;
         height = 0;
         startBitrate = 0;
         maxBitrate = 0;
         minBitrate = 0;
         maxFramerate = 0;
         hadwareAccelerated = false;
         priority = 0;
      }
   };

   struct IPEndpoint
   {
      cpc::string    ipAddress;
      int            port;
   };

   struct StreamStatistics
   {
      /**
      * RFC 3550: "The fraction of RTP data packets from source SSRC_n lost since the
      * previous SR or RR packet was sent, expressed as a fixed point
      * number with the binary point at the left edge of the field."
      */
      unsigned short fractionLost;
      /**
      * RFC 3550: "The total number of RTP data packets from source SSRC_n that have
      * been lost since the beginning of reception.  This number is
      * defined to be the number of packets expected less the number of
      * packets actually received, where the number of packets received
      * includes any which are late or duplicates.  Thus, packets that
      * arrive late are not counted as lost, and the loss may be negative
      * if there are duplicates.  The number of packets expected is
      * defined to be the extended last sequence number received, as
      * defined next, less the initial sequence number received."
      */
      unsigned int   cumulativeLost;
      /**
      * RFC 3550: "The total number of RTP data packets from source SSRC_n that have
      * been lost since the beginning of reception.  This number is
      * defined to be the number of packets expected less the number of
      * packets actually received, where the number of packets received
      * includes any which are late or duplicates.  Thus, packets that
      * arrive late are not counted as lost, and the loss may be negative
      * if there are duplicates.  The number of packets expected is
      * defined to be the extended last sequence number received, as
      * defined next, less the initial sequence number received."
      */
      unsigned int   extendedMax;
      /**
      * RFC 3550: See description of "interarrival jitter"
      *
      */
      unsigned int   jitterSamples;
      /**
       * Round trip time in ms; snapsnot, not a running average
       */
      int64_t        rttMs;
   };

   /**
   * All values are cumulative over the duration of the call
   */
   struct StreamDataCounters
   {
      unsigned int bytesSent;
      unsigned int packetsSent;
      unsigned int bytesReceived;
      unsigned int packetsReceived;
      
      StreamDataCounters() : bytesSent(0), packetsSent(0), bytesReceived(0), packetsReceived(0) {}
   };
   
   /**
    * Voice Quality Monitoring enabled SDKs only.
    * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
    * representative for pricing.
    */
   struct XRVoipMetrics {
      // RFC 3611 4.7
      unsigned short lossRate;
      unsigned short discardRate;
      unsigned short burstDensity;
      unsigned short gapDensity;
      unsigned short burstDuration;
      unsigned short gapDuration;
      unsigned short roundTripDelay;
      unsigned short endSystemDelay;
      unsigned short signalLevel;
      unsigned short noiseLevel;
      unsigned short RERL;
      unsigned short Gmin;
      unsigned short Rfactor;
      unsigned short extRfactor;
      unsigned short MOSLQ;
      unsigned short MOSCQ;
      unsigned short RXconfig;
      unsigned short JBnominal;
      unsigned short JBmax;
      unsigned short JBabsMax;
   };
   
   /**
    * Voice Quality Monitoring enabled SDKs only.
    * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
    * representative for pricing.
    */
   struct XRStatisticsSummary {
      // RFC 3611 4.6
      unsigned short begin_seq;
      unsigned short end_seq;
      unsigned int lost_packets;
      unsigned int dup_packets;
      unsigned int min_jitter;
      unsigned int max_jitter;
      unsigned int mean_jitter;
      unsigned int dev_jitter;
      unsigned short min_ttl_or_hl;
      unsigned short max_ttl_or_hl;
      unsigned short mean_ttl_or_hl;
      unsigned short dev_ttl_or_hl;
   };
   
   /**
   * Locally-calculated statistics for the received RTP stream.
   */ 
   struct AudioStatistics
   {
      AudioCodec          encoder;
      AudioCodec          decoder;
      StreamStatistics    streamStatistics;
      StreamDataCounters  streamDataCounters;
      /**
       * Max jitter over whole call duration
       */
      unsigned int        maxJitterMs;
      /**
       * Average jitter over whole call duration
       */
      unsigned int        averageJitterMs;
      /**
       * Number of discarded packets over whole call duration
       */
      unsigned int        discardedPackets;
     /**
      * Voice Quality Monitoring enabled SDKs only.
      * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
      * representative for pricing.
      */
      XRVoipMetrics       XRvoipMetrics;
     /**
      * Voice Quality Monitoring enabled SDKs only.
      * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
      * representative for pricing.
      */
      XRStatisticsSummary XRstatisticsSummary;
      /**
      * Voice Quality Monitoring enabled SDKs only.
      * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
      * representative for pricing.
      */
      cpc::string         intervalCallQualityReport;
      int64_t             callStartTimeNTP;
      IPEndpoint          endpoint;
   };

   struct RemoteAudioStatistics
   {
      unsigned int        sender_SSRC;
      unsigned int        source_SSRC;
      StreamStatistics    streamStatistics;

     /**
      * Voice Quality Monitoring enabled SDKs only.
      * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
      * representative for pricing.
      */
      XRVoipMetrics       XRvoipMetrics;

     /**
      * Voice Quality Monitoring enabled SDKs only.
      * Voice Quality Monitoring is an optional feature. Contact your CounterPath sales
      * representative for pricing.
      */
      XRStatisticsSummary XRstatisticsSummary;
      IPEndpoint          endpoint;

      /**
       * The last time ANY RTCP packet was received from the remote party. The
       * units are NTP timestamps.
       */
      uint64_t            lastRtcpReceived;

      /**
       * The last time specifically an XR report was received from the remote
       * party. The units are NTP timestamps.
       */
      uint64_t            lastRtcpXrReceived;

      /**
       * The last time a sender report was received from the remote party. The
       * units are NTP timestamps.
       */
      uint64_t            lastSenderReportReceived;
   };

   /**
   * Locally-calculated statistics for the received RTP stream.
   */ 
   struct VideoStatistics
   {
      VideoCodec         encoder;
      VideoCodec         decoder;

      StreamStatistics   streamStatistics;
      StreamDataCounters streamDataCounters;

      unsigned int    totalBitrateSent;
      unsigned int    videoBitrateSent;
      unsigned int    fecBitrateSent;
      unsigned int    nackBitrateSent;
      unsigned int    discardedPackets;
      unsigned int    currentTargetBitrate;
      IPEndpoint      endpoint;
   };

   struct RemoteVideoStatistics
   {
      StreamStatistics streamStatistics;
      IPEndpoint       endpoint;

      /**
       * The last time ANY RTCP packet was received from the remote party. The
       * units are NTP timestamps.
       */
      uint64_t            lastRtcpReceived;

      /**
       * The last time a sender report was received from the remote party. The
       * units are NTP timestamps.
       */
      uint64_t            lastSenderReportReceived;
   };

   /**
   * Audio jitter buffer statistics.
   */ 
   struct AudioJitterBufferStatistics
   {
      // current jitter buffer size in milliseconds
      unsigned short currentBufferSizeMs;
      // optimal jitter buffer size in milliseconds
      unsigned short preferredBufferSizeMs;
      // true if the jitter buffer has expanded due to bursts of jitter
      bool jitterBurstsFound;
      // packet loss rate, taking into account packets lost in the 
      // network and packets discarded by the jitter buffer because they 
      // are too late (in percent) (in Q14)
      unsigned short currentEffectivePacketLossRate;
      // packet loss rate, taking into account late packets only (in percent) (in Q14)
      unsigned short currentDiscardRate;
      // amount of synthesized audio inserted, expressed as a fraction of the original stream (in Q14)
      unsigned short currentSynthesizedAudioInsertRate;
      // amount of synthesized audio inserted pre-emptively, expressed as a fraction of the original stream (in Q14)
      unsigned short currentSynthesizedAudioPreemptiveInsertRate;
      // amount of audio removed by compressing it in time, expressed as a fraction of the original stream (in Q14)
      unsigned short currentAccelerateRate;
      // clock-drift in parts-per-million (may be negative or positive)
      int clockDriftPPM;
      // average packet waiting time in the jitter buffer in milliseconds
      int meanWaitingTimeMs;
      // median packet waiting time in the jitter buffer in milliseconds
      int medianWaitingTimeMs;
      // minimum packet waiting time in the jitter buffer in milliseconds
      int minWaitingTimeMs;
      // maximum packet waiting time in the jitter buffer in milliseconds
      int maxWaitingTimeMs;
      // number of zeroe'd-out samples added as a result of the jitter buffer's built-in packet loss concealment
      int addedSamples;
   };

   /**
   * Video jitter buffer statistics.
   */
   struct VideoJitterBufferStatistics
   {
      // number of decoded key frames
      unsigned int numDecodedKeyFrames;
      // number of decoded delta frames
      unsigned int numDecodedDeltaFrames;
      // minimum buffer time required for smooth playback in milliseconds
      int currentBufferSizeMs;
      // packet loss rate, taking into account late packets only (in percent) (in Q14)
      unsigned short currentDiscardRate;
   };
}
}
#endif // CPCAPI2_SIP_CONVERSATION_MEDIA_TYPES_H
