#pragma once

#if !defined(CPCAPI2_SIP_CONVERSATION_STATE_H)
#define CPCAPI2_SIP_CONVERSATION_STATE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "SipConversationTypes.h"

namespace CPCAPI2
{
namespace SipConversation 
{
class SipConversationManager;

/**
* Struct that is read using SipConversationStateManager::getState().
*/
struct SipConversationState
{
   CPCAPI2::SipAccount::SipAccountHandle account;
   SipConversationHandle conversation;
   ConversationState conversationState;
   ConversationType conversationType;
   cpc::string localAddress;
   cpc::string localDisplayName;
   cpc::string remoteAddress;
   cpc::string remoteDisplayName;
   cpc::vector<MediaInfo> remoteMediaInfo;
   cpc::vector<MediaInfo> localMediaInfo;
   bool localHold;
   bool remoteHold;
   ConversationEndReason endReason;
   ConversationStatistics statistics;
   JitterBufferStatistics jitterBufferStatistics;
   CPCAPI2::SipConversation::AnswerModeSettings answerMode;

   SipConversationState()
   {
      account = 0;
      conversation = 0;
      conversationState = ConversationState_None;
      conversationType = ConversationType_Incoming;
      localAddress = "";
      localDisplayName = "";
      remoteAddress = "";
      remoteDisplayName = "";
      localHold = false;
      remoteHold = false;
      endReason = ConversationEndReason_Unknown;
      answerMode.mode = CPCAPI2::SipConversation::AnswerMode_Disabled;
   }
};
/**
* Manager interface that provides a mechanism for obtaining the 
* SipConversationState struct that provides information about a 
* specific conversation; get a reference to the interface using the 
* static method getInterface(), then call getState() to obtain the struct.
*/
class CPCAPI2_SHAREDLIBRARY_API SipConversationStateManager
{

public:

   /**
   * Get a reference to the %SipConversationStateManager interface.
   * A reference should be obtained before any calls are active.
   */
   static SipConversationStateManager* getInterface(SipConversationManager* cpcConvMan);

   /**
    * Gets the state for a particular conversation.
    */
   virtual int getState(SipConversationHandle h, SipConversationState& conversationState) = 0;

   virtual int getStateAllConversations(cpc::vector<SipConversationState>& conversationState) = 0;

   virtual int getStateAllActiveConversations(cpc::vector<SipConversationState>& conversationState)  = 0;

protected:
   /*
    * The SDK will manage memory life of %SipConversationStateManager.
    */
   virtual ~SipConversationStateManager() {}

};

}

}

#endif // CPCAPI2_SIP_CONVERSATION_STATE_H
