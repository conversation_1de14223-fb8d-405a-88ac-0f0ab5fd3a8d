#pragma once

#if !defined(CPCAPI2_CALL_QUALITY_REPORT_HANDLER_H)
#define CPCAPI2_CALL_QUALITY_REPORT_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "CallQualityReportTypes.h"

namespace CPCAPI2
{
namespace CallQuality
{
struct CallQualityReportGeneratedEvent
{
   /**
    * Call quality interval report in RFC 6035 format.
    */
   cpc::string callQualityReport;
};

struct CallQualityReportSuccessEvent
{
};

struct CallQualityReportFailureEvent
{
   int errorCode;
   int sipResponseCode;
   cpc::string sipErrorDetails;
};

class CallQualityReportHandler
{
public:
   virtual ~CallQualityReportHandler() {}

   /**
    * Notifies the application when a call quality interval report has been generated by the SDK.
    */
   virtual int onCallQualityReportGenerated(CallQualityReporterHandle cqr, const CallQualityReportGeneratedEvent& evt) = 0;

   /**
    * Notifies the application when a call quality interval report has been successfully delivered via SIP PUBLISH.
    */
   virtual int onCallQualityReportSuccess(CallQualityReporterHandle cqr, const CallQualityReportSuccessEvent& evt) = 0;

   /**
   * Notifies the application when an attempt to deliver a call quality interval report via SIP PUBLISH has failed.
   */
   virtual int onCallQualityReportFailure(CallQualityReporterHandle cqr, const CallQualityReportFailureEvent& evt) = 0;
};
   
}
   
}

#endif // CPCAPI2_CALL_QUALITY_REPORT_HANDLER_H
