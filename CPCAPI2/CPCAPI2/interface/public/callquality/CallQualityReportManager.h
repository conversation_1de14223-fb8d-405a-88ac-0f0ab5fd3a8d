#pragma once

#if !defined(CPCAPI2_CALL_QUALITY_REPORT_MANAGER_H)
#define CPCAPI2_CALL_QUALITY_REPORT_MANAGER_H

#include "cpcapi2defs.h"
#include "CallQualityReportTypes.h"
#include "CallQualityReportHandler.h"

namespace CPCAPI2
{
class Phone;
namespace CallQuality
{
/**
* Manager interface to control call quality reporting for phone calls;
* get a reference to the interface using the static method getInterface().
*/
class CPCAPI2_SHAREDLIBRARY_API CallQualityReportManager
{
public:
   /**
   * Get a reference to the %CallQualityReportManager interface.
   */   
   static CallQualityReportManager* getInterface(Phone* cpcPhone);

   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   static const int kCallQualityModuleDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   *</ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling CallQualityReportManager::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;
   
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) {};
   
   /**
    * Allocates a new call quality reporter within the SDK.
    * A call quality reporter will report call quality via callbacks
    * and/or via SIP PUBLISH (if configured) for all calls that take place
    * after startCallQualityReporter(..) is invoked.
    * Only a single call quality reporter instance is necessary to report call quality 
    * for multiple/subsequent calls.
    */
   virtual CallQualityReporterHandle createCallQualityReporter() = 0;

   /**
    * Set the handler for call quality events.
    */
   virtual int setHandler(CallQualityReporterHandle cqr, CallQualityReportHandler* handler) = 0;

   /**
    * Configures the call quality reporter, including the reporting interval, and the URI of the SIP endpoint
    * that will receive call quality reports via SIP PUBLISH and the RFC 6035 vq-rtcpxr event package.
    * Must be invoked prior to calling startCallQualityReporter(..).
    */
   virtual int configureCallQualityReporter(CallQualityReporterHandle cqr, const CallQualityReporterConfig& config) = 0;

   /**
    * Start reporting call quality for any calls that begin after startCallQualityReporter(..) is invoked.
    * The Call quality reporter must be started before the SIP account is enabled.
    */
   virtual int startCallQualityReporter(CallQualityReporterHandle cqr) = 0;

   /**
    * Stop reporting call quality.  Should be invoked just prior to application shutdown, or 
    * earlier if call quality reporting is no longer desired by the application.
    */
   virtual int stopCallQualityReporter(CallQualityReporterHandle cqr) = 0;

};

}

}

#endif // CPCAPI2_CALL_QUALITY_REPORT_MANAGER_H
