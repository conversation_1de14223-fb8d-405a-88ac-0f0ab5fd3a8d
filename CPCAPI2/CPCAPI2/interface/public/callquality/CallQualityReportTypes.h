#pragma once

#if !defined(CPCAPI2_CALL_QUALITY_REPORT_TYPES_H)
#define CPCAPI2_CALL_QUALITY_REPORT_TYPES_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
namespace CallQuality
{
typedef unsigned int CallQualityReporterHandle;

struct CallQualityReporterConfig
{
   /**
    * The SIP account used for sending the outgoing SIP PUBLISH call quality reports.
    */
   CPCAPI2::SipAccount::SipAccountHandle sipAccount;

   /**
    * The SIP URI of the service that will receive call quality reports via SIP PUBLISH.
    * Optional.  If unspecified, the application may (manually) handle call quality reports
    * via CallQualityReportHandler::onCallQualityReportGenerated(..)
    */
   cpc::string reportingServiceSipUri;

   /**
    * The interval at which to generate and send call quality reports.
    * Note that the minimum value is 10 seconds.
    */
   unsigned int reportingIntervalSeconds;

   /**
    * If an outgoing SIP PUBLISH request fails, ignore the error
    * and continue sending additional call quality reports.
    */
   bool ignoreFailures;

   CallQualityReporterConfig()
   {
      reportingIntervalSeconds = 20;
      ignoreFailures = false;
   }
};
}
}

#endif // CPCAPI2_CALL_QUALITY_REPORT_TYPES_H
