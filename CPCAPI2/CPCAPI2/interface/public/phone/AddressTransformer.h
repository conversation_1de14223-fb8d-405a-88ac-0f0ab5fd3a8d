#pragma once

#if !defined(CPCAPI2_ADDRESS_TRANSFORMER_H)
#define CPCAPI2_ADDRESS_TRANSFORMER_H

#include "cpcapi2utils.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{

enum AddressUsageType
{ 
   AddressUsageType_SipAccount = 0,
   AddressUsageType_SipConversation = 1,
   AddressUsageType_SipPresence = 2, 
   AddressUsageType_SipFileTransfer = 3,
   AddressUsageType_SipChat = 4
};

struct AddressTransformationContext {
   AddressUsageType addressUsageType;
   cpc::string registrationDomain;
   AddressTransformationContext()
   {
      addressUsageType = AddressUsageType_SipConversation;
      registrationDomain = "";
   }
   AddressTransformationContext(AddressUsageType contextType, cpc::string contextDomain)
   {
      addressUsageType = contextType;
      registrationDomain = contextDomain;
   }
};

class AddressTransformer
{ 
public: 
   virtual int applyTransformation(const cpc::string& targetAddress, AddressTransformationContext context, cpc::string& transformedAddress) = 0;
   virtual ~AddressTransformer() {}
};

}

#endif // CPCAPI2_ADDRESS_TRANSFORMER_H
