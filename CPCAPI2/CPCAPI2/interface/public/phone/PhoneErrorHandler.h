#pragma once

#if !defined(CPCAPI2_PHONE_ERROR_HANDLER_H)
#define CPCAPI2_PHONE_ERROR_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{
/**
* Event passed in PhoneHandler::onError().
*/
struct PhoneErrorEvent
{
   cpc::string errorText;
};

/**
* Event passed in PhoneHandler::onLicensingError().
*/
struct LicensingErrorEvent
{
   enum LicensingErrorReason
   {
      LicensingErrorReason_Invalid      = 1, // License is not valid for this product
      LicensingErrorReason_Expired      = 2, // License validity period has expired
      LicensingErrorReason_Rejected     = 3, // License was rejected by the validation authority
      LicensingErrorReason_ServerError  = 4, // Server connection error
      LicensingErrorReason_Unknown      = 5  // Internal licensing error, check errorText.
   };

   cpc::string errorText;
   LicensingErrorReason errorReason;
};

/**
 * Handler for general errors in the SDK that are not handled 
 * by other handler interfaces; set in Phone::setErrorHandler()
 *
 */
class PhoneErrorHandler
{
public:
   /**
    * Indicates that a general error on the phone has occurred but the error did not trigger a more specific onError event
    */
   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args) = 0;

   /**
   * Indicates that an error has occurred while validating the license data. The SDK instance is no longer usable and should be destroyed.
   */
   virtual int onLicensingError(const LicensingErrorEvent& args) = 0;

   virtual ~PhoneErrorHandler() {}
};

}
#endif // CPCAPI2_PHONE_ERROR_HANDLER_H
