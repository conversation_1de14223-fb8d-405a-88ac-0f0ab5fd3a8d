#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_HANDLER_H)
#define CPCAPI2_NETWORK_CHANGE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"
#include "cpcstl/string.h"
#include "NetworkChangeManager.h"

namespace CPCAPI2
{

/**
*/
struct NetworkChangeEvent
{
   NetworkTransport                         networkTransport;
   cpc::vector<cpc::string>                 interfacesUp;
   cpc::vector<cpc::string>                 interfacesDown;
};

/**
 */
class NetworkChangeHandler
{
public:
   /**
    * Notifies the application when a transport change has occured; e.g. WiFi to WWAN
    */
   DEPRECATED virtual int onNetworkChange(const NetworkChangeEvent& args) { return kSuccess; }

   /**
    * Notifies the application when a transport change has occured; e.g. WiFi to WWAN
    */
   virtual int onNetworkChangeEx(NetworkChangeManager::NetworkChangeManagerHandle handle, const NetworkChangeEvent& args) { return kSuccess; };
};

}
#endif // CPCAPI2_NETWORK_CHANGE_HANDLER_H
