#pragma once

#if !defined(CPCAPI2_SSL_CIPHER_OPTIONS_H)
#define CPCAPI2_SSL_CIPHER_OPTIONS_H

#include "cpcstl/string.h"

namespace CPCAPI2
{

/**
 * A OpenSSL cipher list. Use one of the recommended suites or see the documentation for openssl ciphers for more information on generating a string (https://www.openssl.org/docs/man1.1.1/man1/ciphers.html)
 */
typedef cpc::string CipherSuite;

/** These are the cipher suites recommended by OWASP. More information can be found at https://cheatsheetseries.owasp.org/cheatsheets/TLS_Cipher_String_Cheat_Sheet.html
 * ECDH is perferred over DH due to emphermeal DH key length limitations in Java 1.7 which affects servers such as Openfire
 * ECDSA support is added the cipher lists for compatibility
 * CipherSuiteAdvanced - The strongest Perfect Forwared Secrecy ciphers. Requires TLS 1.2 or above
 * CipherSuiteBroadCompatibility - Requires TLS 1.2 or above
 * CipherSuiteWidestCompatibility
 * CipherSuiteLegacy
 */
const CipherSuite CipherSuiteAdvanced("TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256");
const CipherSuite CipherSuiteBroadCompatibility("TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256");
const CipherSuite CipherSuiteWidestCompatibility("TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:DHE-RSA-AES256-SHADHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA");
const CipherSuite CipherSuiteLegacy("TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA");
const CipherSuite CipherSuitePerformance("CHACHA20:AESGCM");

enum SslCipherUsage
{
   SslCipherUsageHttp,
   SslCipherUsageSip,
   SslCipherUsageXmpp,
   SslCipherUsageWebSockets,
   SslCipherUsageDtlsSrtp
};

enum TLSVersion
{
   TLS_DEFAULT        = -1,
   TLS_NONE           = 0,
   TLS_V1_0           = 3,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_1           = 4,    /** Deprecated by the IETF. Will be removed in the future. */
   TLS_V1_2           = 5,
   TLS_V1_3           = 6,
   TLS_HIGHEST        = 1000, /** SDK will negotiated highest supported version between client and server; TLS 1.0 and above */
   TLS_NON_DEPRECATED = 1001  /** SDK will negotiated highest supported version between client and server; TLS 1.2 and above */
};

/**
* SSL cipher suite configuration for use in TLS connections
* Contains cipher list strings for HTTP, SIP and XMPP
* More details on cipher list format at https://wiki.openssl.org/index.php/Manual:Ciphers(1)
*/
class CPCAPI2_SHAREDLIBRARY_API SslCipherOptions
{
public:
   SslCipherOptions();
   ~SslCipherOptions();

   TLSVersion getTLSVersion(SslCipherUsage type) const;
   void setTLSVersion(SslCipherUsage type, TLSVersion version);

   CipherSuite getCiphers(SslCipherUsage type) const;
   void setCiphers(SslCipherUsage type, const CipherSuite& ciphers);

private:
   CipherSuite mHttpCiphers;
   CipherSuite mSipCiphers;
   CipherSuite mXmppCiphers;
   CipherSuite mWebSocketsCiphers;
   CipherSuite mDtlsSrtpCiphers;

   TLSVersion mHttpTLSVersion;
   TLSVersion mSipTLSVersion;
   TLSVersion mXmppTLSVersion;
   TLSVersion mWebSocketsTLSVersion;
   TLSVersion mDtlsSrtpTLSVersion;
};

}

#endif // CPCAPI2_SSL_CIPHER_OPTIONS_H
