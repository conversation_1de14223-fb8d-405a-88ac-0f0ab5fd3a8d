#pragma once

#if !defined(CPCAPI2_PHONE_H)
#define CPCAPI2_PHONE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcapi2utils.h"

#include "phone/PhoneErrorHandler.h"
#include "phone/AddressTransformer.h"
#include "phone/PhoneHandler.h"
#include "phone/SslCipherOptions.h"

#ifndef CPCAPI2_PHONE_API
#ifdef CPCAPI2_PHONE_EXPORT
#define CPCAPI2_PHONE_API CPCAPI2_SHAREDLIBRARY_EXPORT
#else
#define CPCAPI2_PHONE_API CPCAPI2_SHAREDLIBRARY_API
#endif
#endif

namespace CPCAPI2
{
class PhoneErrorHandler;
class AddressTransformer;
class PhoneHandler;
class SslCipherOptions;

class PhoneLogger
{
public:
   /**
   * Function object which will be invoked if callback diagnostic logging is enabled.
   * Note: The instance of %PhoneLogger should be prepared to receive calls on a thread the SDK
   * creates and dedicates to logging, i.e. not a main or UI thread.
   */
   virtual bool operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
                           int line, const char *message, const char *messageWithHeaders) = 0;
};

/**
 * SDK licensing data passed in Phone::initialize()
 */
struct LicenseInfo 
{
   cpc::string licenseKey;                // The license key
   cpc::string licenseDocumentLocation;   // Location for the license file (mandatory if license key is provided).
   cpc::string licenseAor;                // Licensing identifier (optional).
};

/**
 * Manager interface that is the main entry point to the SDK; 
 * get a reference to the interface using the static method create();
 * typically you create only one %Phone.
 *
 * You must call %Phone::create() before attempting to use any other
 * modules in the SDK.
 *
 * You are responsible for keeping a reference to the %Phone for
 * the lifetime of the SDK usage.
 */
class CPCAPI2_PHONE_API Phone
{
public:
   /**
   * Get the SDK version.
   */
   static const cpc::string& getVersion();

   /**
   * Create the %Phone object.
   */
   static Phone* create();

   /**
   * Deallocate the %Phone and all sub-modules.
   */
   static void release(Phone* phone);

   /**
   * DEPRECATED: Use the version of initialize that takes the PhoneHandler parameter.
   * Initialize SDK-wide functionality, such as logging.
   * This method should be called immediately after creating an instance of %Phone (though setLoggingEnabled may be called before initialize)
   * Otherwise <module>::getInterface() may return NULL if called prior to initialize().
   * @param licenseInfo contains data used to validate licensing of the SDK
   * @param errorHandler a global error handler used anywhere that an error occurs in the phone without having a known context
   * or to report license validation errors.
   */
   DEPRECATED virtual int initialize(const LicenseInfo& licenseInfo, PhoneErrorHandler* errorHandler, bool useNetworkChangeManager = true) = 0;

   /**
   * Initialize SDK-wide functionality, such as logging.
   * This method should be called immediately after creating an instance of %Phone (though setLoggingEnabled may be called before initialize)
   * Otherwise <module>::getInterface() may return NULL if called prior to initialize().
   * @param licenseInfo contains data used to validate licensing of the SDK
   * @param handler a global handler used anywhere that notifies licensing success, licensing errors and other errors that occur in the phone
   */
   virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, bool useNetworkChangeManager = true) = 0;

   /**
   * DEPRECATED: Use the version of initialize that takes the LicenseInfo parameter.
   */
   DEPRECATED virtual int initialize(const char* licenseKey, const cpc::string& licenseDocumentLocation, const cpc::string& licenseAor = "") = 0;

   /**
   * Initialize SDK-wide functionality, such as logging.
   * This method should be called immediately after creating an instance of %Phone (though setLoggingEnabled may be called before initialize)
   * Otherwise <module>::getInterface() may return NULL if called prior to initialize().
   * @param licenseInfo contains data used to validate licensing of the SDK
   * @param handler a global handler used anywhere that notifies licensing success, licensing errors and other errors that occur in the phone
   * @param ciphers contains cipher options for http, sip and xmpp used when initializing ssl
   */
   virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const SslCipherOptions& ciphers, bool useNetworkChangeManager = true) = 0;

   /**
   * Sets the string conversion functions used internally by all SDK modules.
   * The default string conversion functions provided by the SDK map to those available in the C++ standard library
   * and are generally adequate; it is not neccessary to set custom string converters unless your platform uses
   * an old version of the C++ standard library.
   */
   virtual int setStringConverter(cpc::string (*wstrToUtf8)(const cpc::string&),
                                  cpc::string (*utf8ToWstr)(const char*)) { return kSuccess; };

   /**
   * Enables or disables the diagnostic logging to a file, for all SDK modules. Can be called before initialize(..) is called.
   * @param id A meaningful identifier that will become part of the log file name
   * @param enabled True to enable logging
   */
   virtual int setLoggingEnabled(const cpc::string& id, bool enabled) = 0;

   /**
    * Enables or disables the the diagnostic logging to a callback, for all SDK modules. Can be called before initialize(..) is called.
    * @param logger An instance of %PhoneLogger, that will consume SDK diagnostic log callbacks.
    */   
   virtual int setLoggingEnabled(PhoneLogger *logger, bool enabled) = 0;

   /**
   * Sets the folder into which logs and audio dumps are written
   * @param dir The directory in which to log
   */
   virtual int setLogDirectory(const cpc::string& dir) = 0;

   /**
   * Sets logging level, for all SDK modules. S
   * Should be called after setLoggedEnabled is called. Otherwise the changes won't take effect
   * and will be overriden by default values when setLoggedEnabled is called.
   * @param level Desired level of log output.
   */
   virtual int setLogLevel(CPCAPI2::LogLevel level) = 0;

   /**
    * DEPRECATED: The error handler should be provided as argument to initialize() instead.
    * Sets the global error handler used anywhere that an error occurs in the phone without having a known context
    * For example, if an FpCommand returns kError but information of which account is in error is not available
    */
   DEPRECATED virtual void setErrorHandler(PhoneErrorHandler* handler) = 0;

   /**
    * DEPRECATED: Fires an onError event to the PhoneErrorHandler set by setErrorHandler
    * This is used internally by module interfaces
    */
   DEPRECATED virtual PhoneErrorHandler* getErrorHandler() = 0;

   virtual void setAddressTransformer(AddressTransformer* transformer) = 0;
   virtual AddressTransformer* getAddressTransformer() = 0;

   /**
    * Returns the InstanceId used for registration 
    */
   virtual const cpc::string& getInstanceId() const = 0;

   /**
   * The blocking modes for the process() function. See that function
   * for details.
   */
   static const int kBlockingModeNonBlocking = -1;
   static const int kBlockingModeInfinite = 0;
   
   // constants
   static const int kPhoneDisabled = -1;

   /**
   * Allows the application code to receive callback notifications from the SDK.
   *
   * These callbacks will happen synchronously, in the same thread of execution as that in which 
   * %process() is invoked.  Depending on the application threading model, 
   * process() can be used in two different ways:
   * <ol>
   * <li>blocking mode ?Typically in this mode, %process() is called by the 
   * application from a background (worker) thread.  The call to process() 
   * blocks until a callback function needs to be invoked.
   * <li>non-blocking mode ?In this mode, %process() is called by the application 
   * from the main (GUI) thread of the application, typically from the 
   * main message/event loop.  In this mode, %process() returns immediately 
   * and so must be called frequently enough that the application can receive 
   * its callback notifications in a timely manner.
   *
   * @param timeout kBlockingModeNonBlocking, kBlockingModeInfinite, or a value in milliseconds
   *                representing the time the call to process(..) will block waiting for a callback
   *                from the SDK
   * </ol>
   */
   virtual int process(unsigned int timeout) = 0;

   /**
   * Allows the application to "unblock" the
   * thread calling Phone::process(..) if it is blocked waiting for an SDK callback.
   * Unblocking the process() thread is useful at SDK/application shutdown in certain applications.
   */
   virtual void interruptProcess() = 0;

protected:
   virtual ~Phone() {}
};

}

#endif // CPCAPI2_PHONE_H
