#pragma once

#if !defined(CPCAPI2_PHONE_HANDLER_H)
#define CPCAPI2_PHONE_HANDLER_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "phone/PhoneErrorHandler.h"

namespace CPCAPI2
{

	/**
	* <PERSON><PERSON> for notifying the consumer that the SDK has validated the license; set in Phone::setPhoneHandler()
	*
	*/
	class PhoneHandler
	{
	public:

		/**
		* Indicates that a general error on the phone has occurred but the error did not trigger a more specific onError event
		*/
		virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args) = 0;

		/**
		* Indicates that an error has occurred while validating the license data. The SDK instance is no longer usable and should be destroyed.
		*/
		virtual int onLicensingError(const LicensingErrorEvent& args) = 0;

		/**
		* Indicates that the phone instance possesses a valid license.
		*/
		virtual int onLicensingSuccess() = 0;

		virtual ~PhoneHandler() {}
	};

   class AppRunner
   {
   public:
      AppRunner() {}
      virtual~ AppRunner() {}

      /**
       * Interface to allow the SDK to trigger consumer final shutdown. Useful to ensure that the SDK is
       * cleaned up before the consumer is destroyed.
       */
      virtual int shutdown() { return kSuccess; }
   };
}

#endif
