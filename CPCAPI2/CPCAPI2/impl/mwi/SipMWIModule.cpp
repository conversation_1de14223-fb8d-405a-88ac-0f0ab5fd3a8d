#include "brand_branded.h"

#include "interface/public/mwi/SipMessageWaitingIndicationManager.h"

#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1)
#include "SipMWIManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipMessageWaitingIndication
{
SipMessageWaitingIndicationManager* SipMessageWaitingIndicationManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipMWIManagerInterface>(phone, "SipMWIManagerInterface");
#else
   return NULL;
#endif
}
}
}
