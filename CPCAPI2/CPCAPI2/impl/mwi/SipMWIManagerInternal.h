#pragma once

#if !defined(CPCAPI2_MWI_MANAGER_INTERNAL_H)
#define CPCAPI2_MWI_MANAGER_INTERNAL_H

#include <mwi/SipMessageWaitingIndicationManager.h>

namespace CPCAPI2
{

   namespace SipMessageWaitingIndication
   {

      class CPCAPI2_SHAREDLIBRARY_API SipMWIManagerInternal
      {
      public:
         virtual SipMWISubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipMWISubscriptionHandle h) = 0;
      };

   }
}
#endif // CPCAPI2_MWI_MANAGER_INTERNAL_H