#pragma once

#if !defined(CPCAPI2_SIP_MWI_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_MWI_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "mwi/SipMessageWaitingIndicationManager.h"
#include "mwi/SipMessageWaitingIndicationHandler.h"
#include "SipMWIManagerInternal.h"
#include "../phone/PhoneInterface.h"

#include <map>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipEvent
{
class SipEventManagerInterface;
}

namespace SipMessageWaitingIndication
{
class SipMWIInternalEventHandler;

class SipMWIManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipMessageWaitingIndicationHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                               public SipMessageWaitingIndicationManager,
                               public SipMWIManagerInternal,
                               public PhoneModule
{
public:
   SipMWIManagerInterface(Phone* phone);
   virtual ~SipMWIManagerInterface();

   virtual void Release() OVERRIDE;

   /**
   * Set the handler.
   */   
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipMessageWaitingIndicationHandler* handler) OVERRIDE;


   virtual int registerForEventQueue(CPCAPI2::SipAccount::SipAccountHandle account);
   virtual int unRegisterForEventQueue(CPCAPI2::SipAccount::SipAccountHandle account);

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipMWISubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual SipMWISubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipMWISubscriptionHandle h) OVERRIDE;

   virtual int applySubscriptionSettings(SipMWISubscriptionHandle subscription, const SipMWISubscriptionSettings& settings) OVERRIDE;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant
   * (see addParticipant(..)) or to the event/resource-list server.
   */
   virtual int start(SipMWISubscriptionHandle subscription) OVERRIDE;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
   */
   virtual int end(SipMWISubscriptionHandle subscription) OVERRIDE;

private:
   int setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipMessageWaitingIndicationHandler* handler);
   int startImpl(SipMWISubscriptionHandle subscription);


   virtual int registerAccountEventsImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   virtual int unregisterAccountEventsImpl(CPCAPI2::SipAccount::SipAccountHandle account);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipMWIInternalEventHandler*> AccountMap;
   AccountMap mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipMessageWaitingIndicationHandler*> mHandlers;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
};

std::ostream& operator<<(std::ostream& os, const NewMWISubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const MWISubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const MessageWaitingItem& evt);
std::ostream& operator<<(std::ostream& os, const IncomingMWIStatusEvent& evt);
std::ostream& operator<<(std::ostream& os, const MWISubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipMessageWaitingIndication::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipMWISubscriptionSettings& evt);

}
}
#endif // CPCAPI2_SIP_MWI_MANAGER_INTERFACE_H
