#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipMWIManagerInterface.h"
#include "SipMWIInternalEventHandler.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../util/cpc_logger.h"

#include <resip/stack/PlainContents.hxx>
#include <resip/stack/Mime.hxx>
#include <resip/dum/ClientSubscription.hxx>
#include <resip/dum/ServerSubscription.hxx>

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_MWI

namespace CPCAPI2
{
namespace SipMessageWaitingIndication
{

SipMWIManagerInterface::SipMWIManagerInterface(Phone* phone)
   : EventSource2<EventHandler<SipMessageWaitingIndicationHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mPhone(dynamic_cast<PhoneInterface*>(phone)), mAccountIf(NULL)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
}

SipMWIManagerInterface::~SipMWIManagerInterface()
{
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      mSipEventIf->removeSdkObserver(it->second, it->first, "message-summary");
      delete it->second;
   }
   mAccountMap.clear();
}
   
void SipMWIManagerInterface::Release()
{
   delete this;
}

int SipMWIManagerInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipMessageWaitingIndicationHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipMWIManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
   
   return kSuccess;
}

int SipMWIManagerInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipMessageWaitingIndicationHandler* handler)
{

   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
      removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }

   if (handler)
   {
      registerAccountEventsImpl(account);
   }
   else
   {
      unregisterAccountEventsImpl(account);
   }

   return kSuccess;
}


int SipMWIManagerInterface::registerForEventQueue(CPCAPI2::SipAccount::SipAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&SipMWIManagerInterface::registerAccountEventsImpl, this, account));
   return kSuccess;
}

int SipMWIManagerInterface::unRegisterForEventQueue(CPCAPI2::SipAccount::SipAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&SipMWIManagerInterface::unregisterAccountEventsImpl, this, account));
   return kSuccess;
}

int SipMWIManagerInterface::registerAccountEventsImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipMWIInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   if (evtMan == NULL)
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipMWIManager::setHandler");
         return kError;
      }

      evtMan = new SipMWIInternalEventHandler(this, *acct, *mSipEventIf);
      mAccountMap[account] = evtMan;
      acct->setMWIhandler(std::bind(&SipMWIInternalEventHandler::onOutOfDialogMWI, evtMan, std::placeholders::_1, std::placeholders::_2));
   }

   mSipEventIf->addSdkObserver(evtMan, account, "message-summary");
   
   return kSuccess;
}

int SipMWIManagerInterface::unregisterAccountEventsImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   // todo: clean up SipMWIInternalEventHandler instance created
   AccountMap::iterator it = mAccountMap.find(account);
   if (SipMWIInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second))
   {
      mSipEventIf->removeSdkObserver(evtMan, account, "message-summary");
   }

   return kSuccess;
}

SipEventSubscriptionHandle SipMWIManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);
   return mSipEventIf->createSubscription(account);
}

SipEventSubscriptionHandle SipMWIManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipMWISubscriptionHandle h)
{
   return mSipEventIf->createSubscription(account, h);
}

int SipMWIManagerInterface::start(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);
   postToSdkThread(resip::resip_bind(&SipMWIManagerInterface::startImpl, this, subscription));
   return kSuccess;
}

int SipMWIManagerInterface::startImpl(SipMWISubscriptionHandle subscription)
{
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipMWIManager::start");
         return kError;
      }

      cpc::string localUri = "sip:" + acct->getSettings().username + "@" + acct->getSettings().domain;
      mSipEventIf->addParticipant(subscription, localUri);
      mSipEventIf->start(subscription);
   }
   return kSuccess;
}

int SipMWIManagerInterface::end(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);
   return mSipEventIf->end(subscription);
}

int SipMWIManagerInterface::applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipMWISubscriptionSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << settings);
   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = "message-summary";
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(MimeType("application","simple-message-summary"));
   return mSipEventIf->applySubscriptionSettings(subscription, eventSettings);
}

std::ostream& operator<<(std::ostream& os, const NewMWISubscriptionEvent& evt)
{
  return os << "NewMWISubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const MWISubscriptionEndedEvent& evt)
{
  return os << "MWISubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const MessageWaitingItem& evt)
{
  return os << "MessageWaitingItem";
}

std::ostream& operator<<(std::ostream& os, const IncomingMWIStatusEvent& evt)
{
  return os << "IncomingMWIStatusEvent";
}

std::ostream& operator<<(std::ostream& os, const MWISubscriptionStateChangedEvent& evt)
{
  return os << "MWISubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const SipMessageWaitingIndication::ErrorEvent& evt)
{
  return os << "SipMessageWaitingIndication::ErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipMWISubscriptionSettings& evt)
{
   return os << "SipMWISubscriptionSettings";
}

}
}
#endif
