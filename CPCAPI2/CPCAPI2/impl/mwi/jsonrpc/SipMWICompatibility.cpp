#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "SipMWICompatibility.h"
#include "interface/public/mwi/SipMessageWaitingIndicationManager.h"
#include "mwi/SipMWIManagerInterface.h"


namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipMwi
{

SipMwiCompatibility::SipMwiCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances> &phones)
{
   mPhones = phones;
}

::CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager* SipMwiCompatibility::getInstance(const int64_t phoneHandle)
{
   return ::CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager::getInterface(mPhones->get(phoneHandle));
}

void SipMwiCompatibility::registerForEventQueue(const int64_t phoneHandle, const int64_t accountHandle)
{
   static_cast<::CPCAPI2::SipMessageWaitingIndication::SipMWIManagerInterface*>(getInstance(phoneHandle))->registerForEventQueue(accountHandle);
}

void SipMwiCompatibility::unRegisterForEventQueue(const int64_t phoneHandle, const int64_t accountHandle)
{
   static_cast<::CPCAPI2::SipMessageWaitingIndication::SipMWIManagerInterface*>(getInstance(phoneHandle))->unRegisterForEventQueue(accountHandle);
}

int64_t SipMwiCompatibility::createSubscription(const int64_t phoneHandle, const int64_t accountHandle)
{
   return getInstance(phoneHandle)->createSubscription(accountHandle);
}

void SipMwiCompatibility::start(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle)
{
   getInstance(phoneHandle)->start(mwiSubscriptionHandle);
}

void SipMwiCompatibility::end(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle)
{
   getInstance(phoneHandle)->end(mwiSubscriptionHandle);
}


} // namepace SipConversation
}    // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE