#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"
#include "gen/SipMwi/server/ISipMwi.h"

namespace CPCAPI2
{
namespace SipMessageWaitingIndication
{
class SipMessageWaitingIndicationManager;
}
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace SipMwi
{
class SipMwiCompatibility : public jsonrpc::CPCAPI2::SipMwi::ISipMwi
{
public:
	SipMwiCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);

   void registerForEventQueue(const int64_t phoneHandle, const int64_t accountHandle);

	void unRegisterForEventQueue(const int64_t phoneHandle, const int64_t accountHandle);

    int64_t createSubscription(const int64_t phoneHandle, const int64_t accountHandle);

	void start(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle);

	void end(const int64_t phoneHandle, const int64_t mwiSubscriptionHandle);

private:
	::CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationManager* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
	


}; // class SipMwiCompatibility
} // namepace SipMwi
} // namepace CPCAPI2
} // namepace jsonrpc
