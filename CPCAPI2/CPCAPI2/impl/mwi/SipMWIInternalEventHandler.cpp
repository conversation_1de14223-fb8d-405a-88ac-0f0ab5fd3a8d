#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1)
#include <cpcapi2utils.h>
#include "SipMWIInternalEventHandler.h"
#include "mwi/SipMessageWaitingIndicationHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../util/DumFpCommand.h"

#include <rutil/Random.hxx>
#include <resip/dum/ClientOutOfDialogReq.hxx>
#include <resip/dum/ServerOutOfDialogReq.hxx>
#include <resip/stack/MessageWaitingContents.hxx>
#include <rutil/Data.hxx>
#include <resip/stack/Mime.hxx>

#include "gen/SipMwi/datatypes/SipMwiEvents.h"
#include "gen/SipMwi/datatypes/SipMwiIncomingMwiStatusEvent.h"
#include "gen/SipMwi/datatypes/MwiType.h"
#include "phone/EventQueue.h"

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_MWI

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{

namespace SipMessageWaitingIndication
{

SipMWIInternalEventHandler::SipMWIInternalEventHandler(SipMWIManagerInterface* iff, CPCAPI2::SipAccount::SipAccountImpl& acct, CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf) :
mInterface(iff),
mAccount(acct),
mSipEventIf(sipEventIf)
{
}

SipMWIInternalEventHandler::~SipMWIInternalEventHandler()
{
}

int SipMWIInternalEventHandler::onNewSubscription(SipEventSubscriptionHandle subscription, const NewSubscriptionEvent& args)
{
   DebugLog(<< "SipMWIInternalEventHandler::onNewSubscription(): subscription: " << subscription << " account: " << args.account
      << " subscription-type: " << args.subscriptionType << " event-package: " << args.eventPackage << " remote-address: " << args.remoteAddress);
   if (SipSubscriptionType_Outgoing == args.subscriptionType)
   {
      NewMWISubscriptionEvent mwiArgs;
      mwiArgs.account = mAccount.getHandle();
      mInterface->fireEvent(cpcEvent(SipMessageWaitingIndicationHandler, onNewSubscription), mAccount.getHandle(), subscription, mwiArgs);
   }
   else
   {
      mSipEventIf.reject(subscription, 489);
   }
   return kSuccess;
}

int SipMWIInternalEventHandler::onSubscriptionEnded(SipEventSubscriptionHandle subscription, const SubscriptionEndedEvent& args)
{
   StackLog(<< "SipMWIInternalEventHandler::onSubscriptionEnded(): subscription: " << subscription << " reason-code: " << args.endReason
      << " reason: " << args.reason << " status-code: " << args.statusCode << " subscription-type: " << args.subscriptionType << " remote-address: " << args.remoteAddress);
   if (SipSubscriptionType_Outgoing == args.subscriptionType)
   {
      // incoming MWI subscribe is automatically rejected (obelisk-110)
      // only fire events for end of outgoing subscribe
      MWISubscriptionEndedEvent mwiArgs;
      mwiArgs.endReason = args.endReason;
      mInterface->fireEvent(cpcEvent(SipMessageWaitingIndicationHandler, onSubscriptionEnded), mAccount.getHandle(), subscription, mwiArgs);
   }
   return kSuccess;
}

int SipMWIInternalEventHandler::onIncomingEventState(SipEventSubscriptionHandle subscription, const IncomingEventStateEvent& args)
{
   using namespace resip;

   IncomingMWIStatusEvent mwiArgs;
   //resip::MessageWaitingContents messageWaitingContents(
   //   resip::Data(args.eventState.contentUTF8.c_str(), args.eventState.contentLength),
   //   resip::Mime(StringConv::wstringToUtf8(args.eventState.mimeType).c_str(), StringConv::wstringToUtf8(args.eventState.mimeSubType).c_str()));
   resip::MessageWaitingContents messageWaitingContents;
   resip::Data contentsData(args.eventState.contentUTF8.c_str(), args.eventState.contentLength);
   resip::ParseBuffer pb(contentsData);
   messageWaitingContents.parse(pb);

   mwiArgs.hasMessages = messageWaitingContents.hasMessages();

   for (int h = mw_voice; h < MW_MAX; ++h)
   {
      HeaderType eHeaderType = static_cast<resip::HeaderType>(h);

      if (messageWaitingContents.exists(eHeaderType))
      {
         MessageWaitingContents::Header& tHeader = messageWaitingContents.header(eHeaderType);

         MessageWaitingItem::Type type = MessageWaitingItem::None;

         switch (eHeaderType)
         {
            case mw_voice:
               type = MessageWaitingItem::Voice;
               break;
#if 0 // bliu: missing from SipMessageWaitingIndicationHandler.h however we cannot simply insert Video between Voice and Fax
            case mw_video:
               type = MessageWaitingItem::Video;
               break;
#endif
            case mw_fax:
               type = MessageWaitingItem::Fax;
               break;
            case mw_pager:
               type = MessageWaitingItem::Pager;
               break;
            case mw_multimedia:
               type = MessageWaitingItem::Multimedia;
               break;
            case mw_text:
               type = MessageWaitingItem::Text;
               break;
            case mw_none:
               type = MessageWaitingItem::None;
               break;
            case MW_MAX:
               assert(0);
            default:
               break;
         }

         MessageWaitingItem mw_item = { type, tHeader.newCount(), tHeader.oldCount(), tHeader.urgentNewCount(), tHeader.urgentOldCount() };
         mwiArgs.items.push_back(mw_item);
      }
   }

   // for convenience handling of OOD MWI notifications
   mwiArgs.accountHandle = mAccount.getHandle();
   fireOnIncomingMWIStatus(mAccount.getHandle(), subscription, mwiArgs);

   return kSuccess;
}

jsonrpc::CPCAPI2::SipMwi::MwiType convert(MessageWaitingItem::Type type)
{
   switch (type)
   {
      case MessageWaitingItem::Voice:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::Voice;
      case MessageWaitingItem::Fax:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::Fax;
      case MessageWaitingItem::Pager:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::Pager;
      case MessageWaitingItem::Multimedia:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::Multimedia;
      case MessageWaitingItem::Text:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::Text;
      case MessageWaitingItem::None:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::None;
      default:
         return jsonrpc::CPCAPI2::SipMwi::MwiType::None;
   }
}

static jsonrpc::CPCAPI2::SipMwi::SipMwiMessageWaitingItem convert(const MessageWaitingItem& item)
{
   jsonrpc::CPCAPI2::SipMwi::SipMwiMessageWaitingItem ret;
   // todo: uncomment when code gen for mwiType is fixed
   // ret.mwiType = convert(item.type);
   ret.newMessageCount = item.newMessageCount;
   ret.oldMessageCount = item.oldMessageCount;
   ret.newUrgentMessageCount = item.newUrgentMessageCount;
   ret.oldUrgentMessageCount = item.oldUrgentMessageCount;

   return ret;
}

static cpc::vector<jsonrpc::CPCAPI2::SipMwi::SipMwiMessageWaitingItem> convert(const cpc::vector<MessageWaitingItem>& items)
{
   cpc::vector<jsonrpc::CPCAPI2::SipMwi::SipMwiMessageWaitingItem> ret;

   for (cpc::vector<MessageWaitingItem>::const_iterator it = items.begin(); it != items.end(); ++it)
   {
      ret.push_back(convert(*it));
   }
   
   return ret; 
}

void SipMWIInternalEventHandler::fireOnIncomingMWIStatus(SipAccount::SipAccountHandle accountHandle, CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const IncomingMWIStatusEvent& evt)
{
   mInterface->fireEvent(cpcEvent(SipMessageWaitingIndicationHandler, onIncomingMWIStatus), accountHandle, subscription, evt);

   jsonrpc::CPCAPI2::SipMwi::SipMwiIncomingMwiStatusEvent jrpcEvt;
   jrpcEvt.mwiSubscriptionHandle = subscription;
   jrpcEvt.hasMessages = evt.hasMessages;
   jrpcEvt.sipAccountHandle = accountHandle;
   jrpcEvt.items = convert(evt.items);
   mAccount.getPhone()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::SipMwi::SipMwiEvents::SipMwiDotOnIncomingMwiStatus), std::move(jrpcEvt.marshal())));
}

int SipMWIInternalEventHandler::onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const SubscriptionStateChangedEvent& args)
{
   StackLog(<< "SipMWIInternalEventHandler::onSubscriptionStateChanged(): subscription: " << subscription << " state: " << args.subscriptionState);
   MWISubscriptionStateChangedEvent mwiArgs;
   mwiArgs.subscriptionState = args.subscriptionState;
   mInterface->fireEvent(cpcEvent(SipMessageWaitingIndicationHandler, onSubscriptionStateChanged), mAccount.getHandle(), subscription, mwiArgs);

   return kSuccess;
}

int SipMWIInternalEventHandler::onNotifySuccess(SipEventSubscriptionHandle subscription, const NotifySuccessEvent& args)
{
   return kSuccess;
}

int SipMWIInternalEventHandler::onNotifyFailure(SipEventSubscriptionHandle subscription, const NotifyFailureEvent& args)
{
   return kSuccess;
}

int SipMWIInternalEventHandler::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   return kSuccess;
}

bool SipMWIInternalEventHandler::onOutOfDialogMWI(resip::ServerOutOfDialogReqHandle h, const resip::SipMessage& request)
{
   using namespace resip;
   resip::SharedPtr<resip::SipMessage> msg = h->accept();
   h->send(msg);

   IncomingEventStateEvent fakeEventStateEvt;
   fakeEventStateEvt.eventState.contentLength = request.header(h_ContentLength).value();
   Contents *contents = request.getContents();
   if(contents != NULL)
   {
      fakeEventStateEvt.eventState.contentUTF8 = contents->getBodyData().c_str();
   }
   fakeEventStateEvt.eventState.eventPackage = "message-summary";
   fakeEventStateEvt.eventState.expiresTimeMs = 3600;
   fakeEventStateEvt.eventState.mimeSubType = "simple-message-summary";
   fakeEventStateEvt.eventState.mimeType = "application";

   onIncomingEventState(0, fakeEventStateEvt);

   return true;
}

}

}

#endif
