#pragma once

#if !defined(CPCAPI2_SIP_MWI_JSON_INTERFACE_H)
#define CPCAPI2_SIP_MWI_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/EventSyncHandler.h"
#include "phone/PhoneModule.h"
#include "interface/public/mwi/SipMessageWaitingIndicationHandler.h"
#include "interface/experimental/mwi/SipMWIJsonApi.h"
#include "mwi/SipMWIManagerInterface.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace SipMessageWaitingIndication
{
class SipMWIJsonServerInterface : public CPCAPI2::EventSyncHandler<CPCAPI2::SipMessageWaitingIndication::SipMessageWaitingIndicationHandler>,
                                  public CPCAPI2::SipMessageWaitingIndication::SipMWIJson<PERSON>pi,
                                  public CPCAPI2::JsonApi::JsonApiServerModule,
                                  public CPCAPI2::PhoneModule
{
public:
   SipMWIJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~SipMWIJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // SipMessageWaitingIndicationHandler
   virtual int onNewSubscription(SipEventSubscriptionHandle subscription, const NewMWISubscriptionEvent& args) OVERRIDE;
   virtual int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const MWISubscriptionEndedEvent& args) OVERRIDE;
   virtual int onIncomingMWIStatus(SipEventSubscriptionHandle subscription, const IncomingMWIStatusEvent& args) OVERRIDE;
   virtual int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const MWISubscriptionStateChangedEvent& args) OVERRIDE;
   virtual int onError(SipEventSubscriptionHandle subscription, const ErrorEvent& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleCreateSubscription(const rapidjson::Value &functionObjectVal);
   int handleApplySubscriptionSettings(const rapidjson::Value& functionObjectVal);
   int handleSetHandler(const rapidjson::Value & functionObjectVal);
   int handleStart(const rapidjson::Value& functionObjectVal);
   int handleEnd(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::SipMessageWaitingIndication::SipMWIManagerInterface* mSipMwiIf;
};
}
}
#endif // CPCAPI2_SIP_MWI_JSON_INTERFACE_H
