#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipMWIJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

#include "interface/experimental/mwi/SipMWIJsonApi.h"

namespace CPCAPI2
{
   namespace SipMessageWaitingIndication
   {
      SipMWIJsonApi* SipMWIJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipMWIJsonServerInterface>(phone, "SipMWIJsonApi");
#else
         return NULL;
#endif
      }
   }
}
