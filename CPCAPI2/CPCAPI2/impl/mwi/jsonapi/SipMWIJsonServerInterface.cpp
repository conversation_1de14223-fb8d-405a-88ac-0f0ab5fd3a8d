#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_MWI_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "SipMWIJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_MWI
#define JSON_MODULE "SipMWIManagerJsonProxy"

namespace CPCAPI2
{

namespace SipMessageWaitingIndication
{

SipMWIJsonServerInterface::SipMWIJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mSipMwiIf = dynamic_cast<SipMWIManagerInterface*>(SipMessageWaitingIndicationManager::getInterface(phone));
   mSipMwiIf->addSdkObserver(this);

   mFunctionMap["createSubscription"] = std::bind(&SipMWIJsonServerInterface::handleCreateSubscription, this, std::placeholders::_1);
   mFunctionMap["applySubscriptionSettings"] = std::bind(&SipMWIJsonServerInterface::handleApplySubscriptionSettings, this, std::placeholders::_1);
   mFunctionMap["setHandler"] = std::bind(&SipMWIJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["start"] = std::bind(&SipMWIJsonServerInterface::handleStart, this, std::placeholders::_1);
   mFunctionMap["end"] = std::bind(&SipMWIJsonServerInterface::handleEnd, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

SipMWIJsonServerInterface::~SipMWIJsonServerInterface()
{
}

void SipMWIJsonServerInterface::Release()
{
}

void SipMWIJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void SipMWIJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int SipMWIJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&SipMWIJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void SipMWIJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int SipMWIJsonServerInterface::handleCreateSubscription(const rapidjson::Value& functionObjectVal)
{
   SipAccount::SipAccountHandle account = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   SipMWISubscriptionHandle handle = mSipMwiIf->createSubscription(account);

   JsonFunctionCall(mTransport, "createSubscriptionResult", JSON_VALUE(handle));
   return kSuccess;
}

int SipMWIJsonServerInterface::handleApplySubscriptionSettings(const rapidjson::Value &functionObjectVal)
{
   SipMWISubscriptionHandle subscription = 0;
   SipMWISubscriptionSettings settings;
   JsonDeserialize(functionObjectVal, JSON_VALUE(subscription), JSON_VALUE(settings));

   mSipMwiIf->applySubscriptionSettings(subscription, settings);
   return kSuccess;
}

int SipMWIJsonServerInterface::handleSetHandler(const rapidjson::Value & functionObjectVal)
{
   SipMessageWaitingIndicationManager* sipMWIManager = SipMessageWaitingIndicationManager::getInterface(mPhone);
   SipAccount::SipAccountHandle acctHandle = 0;
   JsonDeserialize(functionObjectVal, "account", acctHandle);
   sipMWIManager->setHandler(acctHandle, this);
   return kSuccess;
}

int SipMWIJsonServerInterface::handleStart(const rapidjson::Value & functionObjectVal)
{
   SipMWISubscriptionHandle subscription = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(subscription));

   mSipMwiIf->start(subscription);
   return kSuccess;
}

int SipMWIJsonServerInterface::handleEnd(const rapidjson::Value & functionObjectVal)
{
   SipMWISubscriptionHandle subscription = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(subscription));

   mSipMwiIf->end(subscription);
   return kSuccess;
}

int SipMWIJsonServerInterface::onNewSubscription(SipEventSubscriptionHandle subscription, const NewMWISubscriptionEvent &args)
{
   JsonFunctionCall(mTransport, "onNewSubscription", JSON_VALUE(subscription), JSON_VALUE(args));
   return kSuccess;
}

int SipMWIJsonServerInterface::onSubscriptionEnded(SipEventSubscriptionHandle subscription, const MWISubscriptionEndedEvent& args)
{
   JsonFunctionCall(mTransport, "onSubscriptionEnded", JSON_VALUE(subscription), JSON_VALUE(args));
   return kSuccess;
}

int SipMWIJsonServerInterface::onIncomingMWIStatus(SipEventSubscriptionHandle subscription, const IncomingMWIStatusEvent &args)
{
   JsonFunctionCall(mTransport, "onIncomingMWIStatus", JSON_VALUE(subscription), JSON_VALUE(args));
   return kSuccess;
}

int SipMWIJsonServerInterface::onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const MWISubscriptionStateChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onSubscriptionStateChanged", JSON_VALUE(subscription), JSON_VALUE(args));
   return kSuccess;
}

int SipMWIJsonServerInterface::onError(SipEventSubscriptionHandle subscription, const ErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(subscription), JSON_VALUE(args));
   return kSuccess;
}

} // namespace SipMessageWaitingIndication

} // namespace CPCAPI2

#endif
