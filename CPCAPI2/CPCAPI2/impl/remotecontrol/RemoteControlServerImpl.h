#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_SERVER_IMPL_H)
#define CPCAPI2_REMOTE_CONTROL_SERVER_IMPL_H

#include "cpcapi2defs.h"

#include "RemoteControlProtoParser.h"
#include "RemoteControlImplTypes.h"

#include <rutil/MultiReactor.hxx>

#include <memory>

namespace CPCAPI2
{
namespace RemoteControl
{
class RemoteControlServerImpl
{
public:
   RemoteControlServerImpl();
   virtual ~RemoteControlServerImpl();

   int processRemoteRequest(const cpc::string& request);

private:
   int dragMouse(int x, int y, CPCAPI2::RemoteControl::MouseButton button);
   int moveMouse(int x, int y);
   int moveMouseSmooth(int x, int y);
   int mouseClick(CPCAPI2::RemoteControl::Mouse<PERSON><PERSON><PERSON> button, bool doubleClick);
   int mouseToggle(CPCAPI2::RemoteControl::MouseButton button, CPCAPI2::RemoteControl::ButtonDirection direction);
   int setMouseDelay(int delayMs);
   int scrollMouse(int scrollMagnitude, CPCAPI2::RemoteControl::ButtonDirection direction);

   int keyTap(const cpc::string& key, const cpc::string& keyFlags);
   int keyToggle(const cpc::string& key, CPCAPI2::RemoteControl::ButtonDirection direction, const cpc::string& keyFlags);
   int keyToggle(int key, CPCAPI2::RemoteControl::ButtonDirection direction, const cpc::string& keyFlags);
   int typeString(const cpc::string& str);
   int typeStringDelayed(const cpc::string& str, int delayMs);
   int setKeyboardDelay(int delayMs);

   int handleSendFile(int fileId, int fileSize, const char* fileName, const uint8_t* fileBytes);

private:
   std::unique_ptr<RemoteControlProtoParser> mParser;
};

}
}

#endif // CPCAPI2_REMOTE_CONTROL_SERVER_IMPL_H

