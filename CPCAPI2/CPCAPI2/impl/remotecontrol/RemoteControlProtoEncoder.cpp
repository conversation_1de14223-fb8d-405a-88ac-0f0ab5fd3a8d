#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "RemoteControlProtoEncoder.h"

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlProtoEncoder::RemoteControlProtoEncoder()
{
}

RemoteControlProtoEncoder::~RemoteControlProtoEncoder()
{
}

int RemoteControlProtoEncoder::encode(const RemoteControlCommand& command, std::string& encoded)
{
   std::stringstream ss;
   ss.write((const char*)&command.commandType, sizeof(int));
   switch (command.commandType)
   {
   case RCCT_dragMouse:
      encodeDragMouse(command.commandDetails.dragMouse, ss);
      break;
   case RCCT_moveMouse:
      encodeMoveMouse(command.commandDetails.moveMouse, ss);
      break;
   case RCCT_mouseClick:
      encodeMouseClick(command.commandDetails.mouseClick, ss);
      break;
   case RCCT_mouseToggle:
      encodeMouseToggle(command.commandDetails.mouseToggle, ss);
      break;
   case RCCT_keyPressed:
      encodeKeyPressed(command.commandDetails.keyPressed, ss);
      break;
   case RCCT_keyReleased:
      encodeKeyReleased(command.commandDetails.keyReleased, ss);
      break;
   case RCCT_sendFile:
      encodeSendFile(command.commandDetails.sendFile, ss);
      break;
   default:
      break;
   }
   encoded = ss.str();
   return 0;
}

int RemoteControlProtoEncoder::encodeDragMouse(const RCC_DragMouse& dragMouse, std::stringstream& ss)
{
   ss.write((const char*)&dragMouse.x, sizeof(int));
   ss.write((const char*)&dragMouse.y, sizeof(int));
   ss.write((const char*)&dragMouse.button, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeMoveMouse(const RCC_MoveMouse& moveMouse, std::stringstream& ss)
{
   ss.write((const char*)&moveMouse.x, sizeof(int));
   ss.write((const char*)&moveMouse.y, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeMouseClick(const RCC_MouseClick& mouseClick, std::stringstream& ss)
{
   ss.write((const char*)&mouseClick.button, sizeof(int));
   ss.write((const char*)&mouseClick.clicks, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeMouseToggle(const RCC_MouseToggle& mouseToggle, std::stringstream& ss)
{
   ss.write((const char*)&mouseToggle.button, sizeof(int));
   ss.write((const char*)&mouseToggle.direction, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeKeyPressed(const RCC_KeyPressed& keyPressed, std::stringstream& ss)
{
   ss.write((const char*)&keyPressed.keycode, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeKeyReleased(const RCC_KeyReleased& keyReleased, std::stringstream& ss)
{
   ss.write((const char*)&keyReleased.keycode, sizeof(int));
   return 0;
}

int RemoteControlProtoEncoder::encodeSendFile(const RCC_SendFile& sendFile, std::stringstream& ss)
{
   ss.write((const char*)&sendFile.fileId, sizeof(int));
   ss.write((const char*)&sendFile.fileSize, sizeof(int));
   ss.write(sendFile.fileName, sizeof(sendFile.fileName));
   ss.write((const char*)sendFile.fileBytes, sendFile.fileSize);
   return 0;
}

}
}
#endif
