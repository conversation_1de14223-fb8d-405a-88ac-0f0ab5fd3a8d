#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "RemoteControlProtoParser.h"

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlProtoParser::RemoteControlProtoParser()
{
}

RemoteControlProtoParser::~RemoteControlProtoParser()
{
}

RemoteControlCommandType findCommandType(const char* buff)
{
   const int* buffInt = (const int*)buff;
   if (*buffInt < 0 || *buffInt > RCCT_maxVal)
   {
      return RCCT_noOp;
   }
   return (RemoteControlCommandType)*buffInt;
}

RemoteControlCommand RemoteControlProtoParser::parse(const char* buff, size_t buffsize)
{
   static RemoteControlCommand NoOpCommand = { RCCT_noOp, RCC_NoOp() };
   RemoteControlCommand retCmd;
   retCmd.commandType = findCommandType(buff);
   buff += sizeof(int);
   buffsize -= sizeof(int);
   switch (retCmd.commandType)
   {
   case RCCT_dragMouse:
      if (parseDragMouse(retCmd.commandDetails.dragMouse, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_moveMouse:
      if (parseMoveMouse(retCmd.commandDetails.moveMouse, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_mouseClick:
      if (parseMouseClick(retCmd.commandDetails.mouseClick, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_mouseToggle:
      if (parseMouseToggle(retCmd.commandDetails.mouseToggle, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_keyPressed:
      if (parseKeyPressed(retCmd.commandDetails.keyPressed, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_keyReleased:
      if (parseKeyReleased(retCmd.commandDetails.keyReleased, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   case RCCT_sendFile:
      if (parseSendFile(retCmd.commandDetails.sendFile, buff, buffsize) != 0)
      {
         return NoOpCommand;
      }
      break;
   default:
      break;
   }
   return retCmd;
}

int RemoteControlProtoParser::parseDragMouse(RCC_DragMouse& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < (3 * sizeof(int)))
   {
      return -1;
   }
   cmd.x = *(int*)buff;
   buff += sizeof(int);
   cmd.y = *(int*)buff;
   buff += sizeof(int);
   cmd.button = *(int*)buff;
   buff += sizeof(int);
   buffsize -= 3*sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseMoveMouse(RCC_MoveMouse& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < (2 * sizeof(int)))
   {
      return -1;
   }
   cmd.x = *(int*)buff;
   buff += sizeof(int);
   cmd.y = *(int*)buff;
   buff += sizeof(int);
   buffsize -= 2 * sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseMouseClick(RCC_MouseClick& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < (2 * sizeof(int)))
   {
      return -1;
   }
   cmd.button = *(int*)buff;
   buff += sizeof(int);
   cmd.clicks = *(int*)buff;
   buff += sizeof(int);
   buffsize -= 2 * sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseMouseToggle(RCC_MouseToggle& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < (2 * sizeof(int)))
   {
      return -1;
   }
   cmd.button = *(int*)buff;
   buff += sizeof(int);
   cmd.direction = *(int*)buff;
   buff += sizeof(int);
   buffsize -= 2 * sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseKeyPressed(RCC_KeyPressed& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < sizeof(int))
   {
      return -1;
   }
   cmd.keycode = *(int*)buff;
   buff += sizeof(int);
   buffsize -= sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseKeyReleased(RCC_KeyReleased& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < sizeof(int))
   {
      return -1;
   }
   cmd.keycode = *(int*)buff;
   buff += sizeof(int);
   buffsize -= sizeof(int);
   return 0;
}

int RemoteControlProtoParser::parseSendFile(RCC_SendFile& cmd, const char* buff, size_t buffsize)
{
   if (buffsize < (2 * sizeof(int)))
   {
      return -1;
   }
   cmd.fileId = *(int*)buff;
   buff += sizeof(int);
   cmd.fileSize = *(int*)buff;
   buff += sizeof(int);
   buffsize -= 2 * sizeof(int);
   memcpy(cmd.fileName, buff, sizeof(cmd.fileName));
   buff += sizeof(cmd.fileName);
   buffsize -= sizeof(cmd.fileName);
   cmd.fileBytes = (const uint8_t*)buff;
   buffsize -= cmd.fileSize;
   return 0;
}

}
}
#endif
