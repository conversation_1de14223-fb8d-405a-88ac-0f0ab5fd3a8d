#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_SERVER_INTERFACE_H)
#define CPCAPI2_REMOTE_CONTROL_SERVER_INTERFACE_H

#include "cpcapi2defs.h"
#include "remotecontrol/RemoteControlServer.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>

#include <map>

namespace CPCAPI2
{
class PhoneInterface;

namespace RemoteControl
{
class RemoteControlServerImpl;

class RemoteControlServerInterface : public RemoteControlServer,
                                       public PhoneModule
                                       #ifdef CPCAPI2_AUTO_TEST
                                          , public AutoTestProcessor
                                       #endif
{
public:
   RemoteControlServerInterface(Phone* phone);
   virtual ~RemoteControlServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   // RemoteControlServer
   virtual int start() OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int processRemoteRequest(const cpc::string& request) OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

private:
   int startImpl();
   int shutdownImpl();
   int processRemoteRequestImpl(const cpc::string& request);

private:
   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
   RemoteControlServerImpl* mImpl;
};

}
}

#endif // CPCAPI2_REMOTE_CONTROL_SERVER_INTERFACE_H
