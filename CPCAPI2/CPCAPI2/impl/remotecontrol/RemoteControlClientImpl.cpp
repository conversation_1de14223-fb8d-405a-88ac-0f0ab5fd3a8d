#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "RemoteControlClientImpl.h"
#include "RemoteControlImplTypes.h"

#include "../phone/PhoneInterface.h"
#include "../media/VideoInterface.h"
#include "../media/MediaManagerInterface.h"
#include "../media/VideoImpl.h"
#include "../util/cpc_logger.h"

#include <rutil/Random.hxx>

#include <MediaStackImpl.hxx>
#include <RtpStreamImpl.hxx>
#include <MixerImpl.hxx>

#include <packager/base/at_exit.h>

#include <thread>
#include <future>
#include <stdio.h>
#include <fstream>

#include <Shlwapi.h>

using websocketpp::lib::placeholders::_1;
using websocketpp::lib::placeholders::_2;
using websocketpp::lib::bind;

using namespace CPCAPI2::Media;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlClientImpl::RemoteControlClientImpl(CPCAPI2::PhoneInterface* phone)
   : mProtoEnc(new RemoteControlProtoEncoder()),
     mReactor(phone->getSdkModuleThread()),
     mPhone(phone),
     mScreenShareSurface(NULL)
{
   initWebsocketClient();
}

RemoteControlClientImpl::~RemoteControlClientImpl()
{
   shutdownWebsocketClient();
}

void RemoteControlClientImpl::inputThread()
{
   base::AtExitManager atExit;
   mMessageLoop.reset(new base::MessageLoopForUI());
   mUserInputMonitor = media::UserInputMonitor::Create(scoped_refptr<base::SingleThreadTaskRunner>(), mMessageLoop->task_runner());
   mUserInputMonitor->AddMouseListener(this);
   mUserInputMonitor->AddKeyboardListener(this);
   mMessageLoop->Run();
   mMessageLoop.reset();
}

void RemoteControlClientImpl::stopInputMonitor()
{
   mUserInputMonitor->RemoveMouseListener(this);
   mUserInputMonitor->RemoveKeyboardListener(this);
   mMessageLoop->QuitWhenIdle();
   mScreenShareSurface = NULL;
   mUserInputMonitor.reset();
}

int RemoteControlClientImpl::getVideoStreamDims(unsigned int& width, unsigned int& height)
{
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   std::shared_ptr<webrtc_recon::MediaStackImpl> ms = dynamic_cast<MediaManagerInterface*>(mm)->media_stack_ptr();
   std::shared_ptr<webrtc_recon::MixerImpl> mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(ms->mixer());
   if (mixerImpl.get() != NULL)
   {
      return mixerImpl->getVideoFrameDimsForRenderSurface(mScreenShareSurface, width, height);
   }
   return -1;
}

int RemoteControlClientImpl::getWindowDimsImpl(int& left, int& top, int& right, int& bottom)
{
   left = 0;
   top = 0;
   right = 0;
   bottom = 0;

#if WIN32
   RECT rect;
   if (::GetClientRect((HWND)mScreenShareSurface, &rect) != FALSE)
   {
      if (::MapWindowPoints((HWND)mScreenShareSurface, NULL, (LPPOINT)&rect, 2) != FALSE)
      {
         left = rect.left;
         top = rect.top;
         right = rect.right;
         bottom = rect.bottom;
      }
   }
   return 0;
#else
   return -1;
#endif
}

inline int32_t fixed_div_16(int32_t x, int32_t y)
{
   return ((int64_t)x * (1 << 16)) / y;
}

inline int32_t fixed_mul_16(int32_t x, int32_t y)
{
   return ((int64_t)x * (int64_t)y) >> 16;
}

// inputs as regular integers, outputs as Q16
void calculateTargetDimensions(int windowWidth, int windowHeight, int frameWidth, int frameHeight, int& targetWidth, int& targetHeight)
{
   targetWidth = windowWidth << 16;
   targetHeight = windowHeight << 16;

   if (windowWidth == 0 || windowHeight == 0 || frameWidth == 0 || frameHeight == 0)
   {
      return;
   }

   // find best fit for frame into window
   int widthScale = fixed_div_16(windowWidth << 16, frameWidth << 16);
   int heightScale = fixed_div_16(windowHeight << 16, frameHeight << 16);
   int scale = std::min(widthScale, heightScale);

   targetWidth = fixed_mul_16(frameWidth << 16, scale);
   targetHeight = fixed_mul_16(frameHeight << 16, scale);
}

void RemoteControlClientImpl::OnMouseMoved(const media::SkIPoint& position)
{
   if (mScreenShareSurface != NULL)
   {
      mLastMousePos = position;

      int left, top, right, bottom = 0;
      if (getWindowDimsImpl(left, top, right, bottom) == 0)
      {
         POINT mouseCoOrdsAsPoint;
         mouseCoOrdsAsPoint.x = position.x();
         mouseCoOrdsAsPoint.y = position.y();
         if (::WindowFromPhysicalPoint(mouseCoOrdsAsPoint) != mScreenShareSurface)
         {
            return;
         }

         unsigned int vidWidth, vidHeight = 0;
         if (getVideoStreamDims(vidWidth, vidHeight) == 0)
         {
            if (vidWidth == 0 || vidHeight == 0)
            {
               return;
            }

            int winWidth = right - left;
            int winHeight = bottom - top;

            int targetWidthQ16, targetHeightQ16 = 0; // Q16
            calculateTargetDimensions(winWidth, winHeight, vidWidth, vidHeight, targetWidthQ16, targetHeightQ16);

            int effectiveLeftQ16 = (left << 16) + (((winWidth << 16) - targetWidthQ16) >> 1);
            int effectiveTopQ16 = (top << 16) + (((winHeight << 16) - targetHeightQ16) >> 1);
            int effectiveRightQ16 = (right << 16) - (((winWidth << 16) - targetWidthQ16) >> 1);
            int effectiveBottomQ16 = (bottom << 16) - (((winHeight << 16) - targetHeightQ16) >> 1);

            int xQ16 = position.x() << 16;
            int yQ16 = position.y() << 16;

            if (xQ16 >= effectiveLeftQ16 && xQ16 <= effectiveRightQ16 &&
                yQ16 >= effectiveTopQ16 && yQ16 <= effectiveBottomQ16)
            {
               int mouseRelativeXQ16 = xQ16 - effectiveLeftQ16;
               int mouseRelativeYQ16 = yQ16 - effectiveTopQ16;
               RemoteControlCommand moveMouse;
               moveMouse.commandType = RCCT_moveMouse;
               moveMouse.commandDetails.moveMouse.x = fixed_div_16(mouseRelativeXQ16, targetWidthQ16);
               moveMouse.commandDetails.moveMouse.y = fixed_div_16(mouseRelativeYQ16, targetHeightQ16);
               std::string encoded;
               if (mProtoEnc->encode(moveMouse, encoded) == 0)
               {
                  sendData(encoded);
               }
            }
         }
      }
   }
}

void RemoteControlClientImpl::OnMouseButton(unsigned short dwFlags, const media::SkIPoint& position)
{
   if (mScreenShareSurface != NULL)
   {
      POINT mouseCoOrdsAsPoint;
      mouseCoOrdsAsPoint.x = position.x();
      mouseCoOrdsAsPoint.y = position.y();
      if (::WindowFromPhysicalPoint(mouseCoOrdsAsPoint) != mScreenShareSurface)
      {
         return;
      }

      RemoteControlCommand mouseToggle;
      mouseToggle.commandType = RCCT_mouseToggle;
      if (dwFlags & RI_MOUSE_LEFT_BUTTON_DOWN)
      {
         mouseToggle.commandDetails.mouseToggle.button = MouseButton_Left;
         mouseToggle.commandDetails.mouseToggle.direction = ButtonDirection_Down;
      }
      else if (dwFlags & RI_MOUSE_LEFT_BUTTON_UP)
      {
         mouseToggle.commandDetails.mouseToggle.button = MouseButton_Left;
         mouseToggle.commandDetails.mouseToggle.direction = ButtonDirection_Up;
      }
      else
      {
         return;
      }
      std::string encoded;
      if (mProtoEnc->encode(mouseToggle, encoded) == 0)
      {
         sendData(encoded);
      }
   }
}

void RemoteControlClientImpl::OnKeyPressed(uint8_t keycode)
{
   if (mScreenShareSurface != NULL)
   {
      POINT mouseCoOrdsAsPoint;
      mouseCoOrdsAsPoint.x = mLastMousePos.x();
      mouseCoOrdsAsPoint.y = mLastMousePos.y();
      if (::WindowFromPhysicalPoint(mouseCoOrdsAsPoint) != mScreenShareSurface)
      {
         return;
      }

      RemoteControlCommand keyPressed;
      keyPressed.commandType = RCCT_keyPressed;
      keyPressed.commandDetails.keyPressed.keycode = keycode;

      std::string encoded;
      if (mProtoEnc->encode(keyPressed, encoded) == 0)
      {
         sendData(encoded);
      }
   }
}

void RemoteControlClientImpl::OnKeyReleased(uint8_t keycode)
{
   if (mScreenShareSurface != NULL)
   {
      POINT mouseCoOrdsAsPoint;
      mouseCoOrdsAsPoint.x = mLastMousePos.x();
      mouseCoOrdsAsPoint.y = mLastMousePos.y();
      if (::WindowFromPhysicalPoint(mouseCoOrdsAsPoint) != mScreenShareSurface)
      {
         return;
      }

      RemoteControlCommand keyReleased;
      keyReleased.commandType = RCCT_keyReleased;
      keyReleased.commandDetails.keyReleased.keycode = keycode;

      std::string encoded;
      if (mProtoEnc->encode(keyReleased, encoded) == 0)
      {
         sendData(encoded);
      }
   }
}

void RemoteControlClientImpl::initWebsocketClient()
{
   // clear all error/access channels
   m_endpoint.clear_access_channels(websocketpp::log::alevel::all);
   m_endpoint.clear_error_channels(websocketpp::log::elevel::all);

   // Initialize the endpoint
   m_endpoint.init_asio();

   // Mark this endpoint as perpetual. Perpetual endpoints will not exit
   // even if there are no connections.
   m_endpoint.start_perpetual();

   // Start a background thread and run the endpoint in that thread
   m_thread.reset(new thread_type(&client::run, &m_endpoint));
}

void RemoteControlClientImpl::shutdownWebsocketClient()
{
   m_endpoint.get_io_service().post([this]() {
      try {
         websocketpp::lib::error_code ec;
         client::connection_ptr con = m_endpoint.get_con_from_hdl(m_con, ec);

         if (con.get() != NULL)
         {
            // for each connection call close
            con->set_close_handler([](websocketpp::connection_hdl) {
            });
            con->close(0, "");
         }

         // Unflag the endpoint as perpetual. This will instruct it to stop once
         // all connections are finished.
         m_endpoint.stop_perpetual();
      }
      catch (const websocketpp::exception& /*exc*/) {
      }
   });

   // Block until everything is done
   m_thread->join();
}

int RemoteControlClientImpl::setIncomingVideoRenderTarget(void* surface)
{
   if (surface != NULL)
   {
      mScreenShareSurface = surface;
      mInputThread.reset(new CPCAPI2::thread(std::bind(&RemoteControlClientImpl::inputThread, this)));
   }
   else
   {
      if (mMessageLoop.get() != NULL && mInputThread.get() != NULL)
      {
         mMessageLoop->task_runner()->PostTask(
            FROM_HERE,
            base::Bind(&RemoteControlClientImpl::stopInputMonitor, base::Unretained(this)));
         mInputThread->join();
      }
   }
   return kSuccess;
}

int RemoteControlClientImpl::connectWebsocketClient(const std::string& uri)
{
   m_endpoint.get_io_service().post([this, uri]() {
      InfoLog(<< "RemoteControlClientImpl::connectWebsocketClient " << uri.c_str());

      websocketpp::lib::error_code ec;

      // connect to this address
      client::connection_ptr con = m_endpoint.get_connection(uri, ec);
      if (ec) {
         return ec.value();
      }
      m_con = con->get_handle();

      con->set_open_handler(bind(&RemoteControlClientImpl::on_open, this, std::placeholders::_1));
      con->set_fail_handler(bind(&RemoteControlClientImpl::on_fail, this, std::placeholders::_1));
      con->set_message_handler(bind(&RemoteControlClientImpl::on_message, this, std::placeholders::_1, std::placeholders::_2));
      con->set_close_handler(bind(&RemoteControlClientImpl::on_close, this, std::placeholders::_1));

      InfoLog(<< "RemoteControlClientImpl::connectWebsocketClient connecting...");
      m_endpoint.connect(con);
      return 0;
   });

   return 0;
}

int RemoteControlClientImpl::disconnectWebsocketClient()
{
   m_endpoint.get_io_service().post([this]() {
      InfoLog(<< "RemoteControlClientImpl::disconnectWebsocketClient");
      websocketpp::lib::error_code ec;
      client::connection_ptr con = m_endpoint.get_con_from_hdl(m_con, ec);
      if (con.get() != NULL)
      {
         // for each connection call close
         con->close(0, "", ec);
      }
   });
   return 0;
}

int RemoteControlClientImpl::sendFile(const std::string& fileNameUtf8)
{
   RemoteControlCommand sendFile;
   sendFile.commandType = RCCT_sendFile;
   sendFile.commandDetails.sendFile.fileId = resip::Random::getCryptoRandom();

   std::string fileNameOnly(PathFindFileName(fileNameUtf8.c_str()));
   memset(sendFile.commandDetails.sendFile.fileName, 0, sizeof(sendFile.commandDetails.sendFile.fileName));
   memcpy(sendFile.commandDetails.sendFile.fileName, fileNameOnly.c_str(), fileNameOnly.size());

   std::ifstream fileStream(fileNameUtf8.c_str(), std::ios::in | std::ios::binary);
   if (!fileStream.is_open())
   {
      return -1;
   }
   const size_t fileChunkSize = 4096;
   char fileChunk[fileChunkSize];

   do
   {
      fileStream.read(fileChunk, fileChunkSize);
      sendFile.commandDetails.sendFile.fileSize = (int)fileStream.gcount();
      sendFile.commandDetails.sendFile.fileBytes = (uint8_t*)fileChunk;
      std::string encoded;
      if (mProtoEnc->encode(sendFile, encoded) == 0)
      {
         sendData(encoded);
      }
   } while (!fileStream.eof());

   fileStream.close();
   return 0;
}

int RemoteControlClientImpl::sendData(const std::string& data)
{
   m_endpoint.get_io_service().post([this, data]() {
      try {
         websocketpp::lib::error_code ec;
         client::connection_ptr con = m_endpoint.get_con_from_hdl(m_con, ec);
         if (con.get() != NULL)
         {
            //DebugLog(<< "RemoteControlClientImpl::sendWebsocketClient data=" << data.c_str());
            ec = con->send(data, websocketpp::frame::opcode::binary);
            if (ec)
            {
               WarningLog(<< "RemoteControlClientImpl::sendWebsocketClient failed ec=" << ec.value());
            }
            else
            {
               //DebugLog(<< "RemoteControlClientImpl::sendWebsocketClient success");
            }
         }
      }
      catch (const websocketpp::exception& /*exc*/) {
      }
   });

   return 0;
}

void RemoteControlClientImpl::on_message(websocketpp::connection_hdl h, message_ptr msg)
{
   DebugLog(<< "RemoteControlClientImpl::on_message");
   mReactor.post(resip::resip_bind(&RemoteControlClientImpl::handle_on_message, this, h, msg));
}

void RemoteControlClientImpl::handle_on_message(websocketpp::connection_hdl h, message_ptr msg)
{
}

void RemoteControlClientImpl::on_fail(websocketpp::connection_hdl h)
{
   std::error_code ec = m_endpoint.get_con_from_hdl(h)->get_ec();
   WarningLog(<< "RemoteControlClientImpl::on_fail");
}

void RemoteControlClientImpl::handle_on_fail(websocketpp::connection_hdl, std::error_code ec)
{
}

void RemoteControlClientImpl::on_open(websocketpp::connection_hdl h)
{
   InfoLog(<< "RemoteControlClientImpl::on_open");
   mReactor.post(resip::resip_bind(&RemoteControlClientImpl::handle_on_open, this, h));
}

void RemoteControlClientImpl::handle_on_open(websocketpp::connection_hdl)
{
}

void RemoteControlClientImpl::on_close(websocketpp::connection_hdl h)
{
   InfoLog(<< "RemoteControlClientImpl::on_close");
}

void RemoteControlClientImpl::handle_on_close(websocketpp::connection_hdl)
{

}
}
}
#endif // CPCAPI2_BRAND_REMOTE_CONTROL_MODULE
