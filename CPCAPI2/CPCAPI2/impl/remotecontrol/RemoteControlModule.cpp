#include "brand_branded.h"

#include "interface/experimental/remotecontrol/RemoteControlClient.h"
#include "interface/experimental/remotecontrol/RemoteControlServer.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "RemoteControlServerInterface.h"
#include "RemoteControlClientInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlServer* RemoteControlServer::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   RemoteControlServer* convManager = dynamic_cast<RemoteControlServer*>(pi->getInterfaceByName("RemoteControlServerInterface"));
   if (convManager == NULL)
   {
      RemoteControlServerInterface* m_if = new RemoteControlServerInterface(cpcPhone);
      pi->registerInterface("RemoteControlServerInterface", m_if);
      convManager = m_if;
   }
   return convManager;
#else
   return NULL;
#endif
}

RemoteControlClient* RemoteControlClient::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   RemoteControlClient* convManager = dynamic_cast<RemoteControlClient*>(pi->getInterfaceByName("RemoteControlClientInterface"));
   if (convManager == NULL)
   {
      RemoteControlClientInterface* m_if = new RemoteControlClientInterface(cpcPhone);
      pi->registerInterface("RemoteControlClientInterface", m_if);
      convManager = m_if;
   }
   return convManager;
#else
   return NULL;
#endif
}

}
}
