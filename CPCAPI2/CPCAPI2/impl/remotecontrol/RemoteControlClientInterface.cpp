#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "cpcapi2utils.h"
#include "RemoteControlClientInterface.h"
#include "RemoteControlClientImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlClientInterface::RemoteControlClientInterface(Phone* phone)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mImpl(NULL)
{
}

RemoteControlClientInterface::~RemoteControlClientInterface()
{
   mShutdown = true;
   interruptProcess();
}

void RemoteControlClientInterface::Release()
{
   delete this;
}

int RemoteControlClientInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kRemoteControlModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kRemoteControlModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* RemoteControlClientInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void RemoteControlClientInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void RemoteControlClientInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void RemoteControlClientInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
}

int RemoteControlClientInterface::setIncomingVideoRenderTarget(void* surface)
{
   post(resip::resip_bind(&RemoteControlClientInterface::setIncomingVideoRenderTargetImpl, this, surface));
   return kSuccess;
}

int RemoteControlClientInterface::setIncomingVideoRenderTargetImpl(void* surface)
{
   if (mImpl == NULL)
   {
      mImpl = new RemoteControlClientImpl(mPhone);
   }
   mImpl->setIncomingVideoRenderTarget(surface);
   return kSuccess;
}

int RemoteControlClientInterface::connect(const cpc::string& serverWsUrl)
{
   post(resip::resip_bind(&RemoteControlClientInterface::connectImpl, this, serverWsUrl));
   return kSuccess;
}

int RemoteControlClientInterface::connectImpl(const cpc::string& serverWsUrl)
{
   if (mImpl == NULL)
   {
      return kError;
   }
   mImpl->connectWebsocketClient(std::string(serverWsUrl.c_str(), serverWsUrl.size()));
   return kSuccess;
}

int RemoteControlClientInterface::disconnect()
{
   post(resip::resip_bind(&RemoteControlClientInterface::disconnectImpl, this));
   return kSuccess;
}

int RemoteControlClientInterface::disconnectImpl()
{
   if (mImpl == NULL)
   {
      return kError;
   }
   mImpl->disconnectWebsocketClient();
   return kSuccess;
}

int RemoteControlClientInterface::sendFile(const cpc::string& filePathFull)
{
   post(resip::resip_bind(&RemoteControlClientInterface::sendFileImpl, this, filePathFull));
   return kSuccess;
}

int RemoteControlClientInterface::sendFileImpl(const cpc::string& filePathFull)
{
   if (mImpl == NULL)
   {
      return kError;
   }
   mImpl->sendFile(filePathFull.c_str());
   return kSuccess;
}


}
}

#endif
