#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_PROTO_PARSER_H)
#define CPCAPI2_REMOTE_CONTROL_PROTO_PARSER_H

#include "RemoteControlProtoTypes.h"

namespace CPCAPI2
{
namespace RemoteControl
{
class RemoteControlProtoParser
{
public:
   RemoteControlProtoParser();
   virtual ~RemoteControlProtoParser();

   RemoteControlCommand parse(const char* buff, size_t buffsize);

private:
   int parseDragMouse(RCC_DragMouse& cmd, const char* buff, size_t buffsize);
   int parseMoveMouse(RCC_MoveMouse& cmd, const char* buff, size_t buffsize);
   int parseMouseClick(RCC_MouseClick& cmd, const char* buff, size_t buffsize);
   int parseMouseToggle(RCC_MouseToggle& cmd, const char* buff, size_t buffsize);
   int parseKeyPressed(RCC_KeyPressed& cmd, const char* buff, size_t buffsize);
   int parseKeyReleased(RCC_KeyReleased& cmd, const char* buff, size_t buffsize);
   int parseSendFile(RCC_SendFile& cmd, const char* buff, size_t buffsize);

};
}
}

#endif // CPCAPI2_REMOTE_CONTROL_PROTO_PARSER_H
