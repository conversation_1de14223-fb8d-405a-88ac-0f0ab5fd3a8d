#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_PROTO_TYPES_H)
#define CPCAPI2_REMOTE_CONTROL_PROTO_TYPES_H

#include <stdint.h>

namespace CPCAPI2
{
namespace RemoteControl
{
enum RemoteControlCommandType
{
   RCCT_noOp = 0,
   RCCT_dragMouse = 1,
   RCCT_moveMouse = 2,
   RCCT_moveMouseSmooth = 3,
   RCCT_mouseClick = 4,
   RCCT_mouseToggle = 5,
   RCCT_setMouseDelay = 6,
   RCCT_scrollMouse = 7,
   RCCT_keyPressed = 8,
   RCCT_keyReleased = 9,
   RCCT_sendFile = 10,
   RCCT_maxVal = RCCT_sendFile
};

struct RCC_NoOp
{
};

struct RCC_DragMouse
{
   int x;
   int y;
   int button;
};

struct RCC_MoveMouse
{
   int x;
   int y;
};

struct RCC_MoveMouseSmooth
{
   int x;
   int y;
};

struct RCC_MouseClick
{
   int button;
   int clicks;
};

struct RCC_MouseToggle
{
   int direction;
   int button;
};

struct RCC_SetMouseDelay
{
   int delayMs;
};

struct RCC_ScrollMouse
{
   int scrollMagnitude;
   int direction;
};

struct RCC_KeyPressed
{
   int keycode;
};

struct RCC_KeyReleased
{
   int keycode;
};

struct RCC_SendFile
{
   int fileId;
   int fileSize;
   char fileName[256];
   const uint8_t* fileBytes;
};

struct RemoteControlCommand
{
   RemoteControlCommandType commandType;
   union {
      RCC_NoOp noOp;
      RCC_DragMouse dragMouse;
      RCC_MoveMouse moveMouse;
      RCC_MoveMouseSmooth moveMouseSmooth;
      RCC_MouseClick mouseClick;
      RCC_MouseToggle mouseToggle;
      RCC_SetMouseDelay setMouseDelay;
      RCC_ScrollMouse scrollMouse;
      RCC_KeyPressed keyPressed;
      RCC_KeyReleased keyReleased;
      RCC_SendFile sendFile;
   } commandDetails;
};
}
}

#endif // CPCAPI2_REMOTE_CONTROL_PROTO_TYPES_H