#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_PROTO_ENCODER_H)
#define CPCAPI2_REMOTE_CONTROL_PROTO_ENCODER_H

#include "RemoteControlProtoTypes.h"

#include <string>
#include <sstream>

namespace CPCAPI2
{
namespace RemoteControl
{
class RemoteControlProtoEncoder
{
public:
   RemoteControlProtoEncoder();
   virtual ~RemoteControlProtoEncoder();

   int encode(const RemoteControlCommand& command, std::string& encoded);

private:
   int encodeDragMouse(const RCC_DragMouse& dragMouse, std::stringstream& ss);
   int encodeMoveMouse(const RCC_MoveMouse& moveMouse, std::stringstream& ss);
   int encodeMouseClick(const RCC_MouseClick& mouseClick, std::stringstream& ss);
   int encodeMouseToggle(const RCC_MouseToggle& mouseToggle, std::stringstream& ss);
   int encodeKeyPressed(const RCC_KeyPressed& keyPressed, std::stringstream& ss);
   int encodeKeyReleased(const RCC_KeyReleased& keyReleased, std::stringstream& ss);
   int encodeSendFile(const RCC_SendFile& sendFile, std::stringstream& ss);
};
}
}

#endif // CPCAPI2_REMOTE_CONTROL_PROTO_ENCODER_H
