#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "RemoteControlServerImpl.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"

#include <robotjs/src/mouse.h>
#include <robotjs/src/keycode.h>
#include <robotjs/src/keypress.h>
#include <robotjs/src/microsleep.h>
#include <robotjs/src/deadbeef_rand.h>

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
#include <codecvt>

#include <ShlObj.h>

using namespace resip;

namespace CPCAPI2
{
namespace RemoteControl
{
//Global delays.
static int mouseDelay = 0;
static int keyboardDelay = 10;

RemoteControlServerImpl::RemoteControlServerImpl()
   : mParser(new RemoteControlProtoParser())
{
   deadbeef_srand_time();
}

RemoteControlServerImpl::~RemoteControlServerImpl()
{
}

int RemoteControlServerImpl::processRemoteRequest(const cpc::string& request)
{
   RemoteControlCommand cmd = mParser->parse(request.c_str(), request.size());
   switch (cmd.commandType)
   {
   case RCCT_moveMouse:
      moveMouse(cmd.commandDetails.moveMouse.x, cmd.commandDetails.moveMouse.y);
      break;
   case RCCT_mouseClick:
      mouseClick((MouseButton)cmd.commandDetails.mouseClick.button, cmd.commandDetails.mouseClick.clicks > 1);
      break;
   case RCCT_mouseToggle:
      mouseToggle((MouseButton)cmd.commandDetails.mouseToggle.button, (ButtonDirection)cmd.commandDetails.mouseToggle.direction);
      break;
   case RCCT_keyPressed:
      keyToggle(cmd.commandDetails.keyPressed.keycode, RemoteControl::ButtonDirection_Down, "");
      break;
   case RCCT_keyReleased:
      keyToggle(cmd.commandDetails.keyReleased.keycode, RemoteControl::ButtonDirection_Up, "");
      break;
   case RCCT_sendFile:
      handleSendFile(cmd.commandDetails.sendFile.fileId, cmd.commandDetails.sendFile.fileSize, 
         cmd.commandDetails.sendFile.fileName,
         cmd.commandDetails.sendFile.fileBytes);
      break;
   default:
      break;
   }
   return 0;
}

int CheckMouseButton(CPCAPI2::RemoteControl::MouseButton b, MMMouseButton* const button)
{
   if (!button) return -1;

   if (b == MouseButton_Left)
   {
      *button = LEFT_BUTTON;
   }
   else if (b == MouseButton_Right)
   {
      *button = RIGHT_BUTTON;
   }
   else if (b == MouseButton_Centre)
   {
      *button = CENTER_BUTTON;
   }
   else
   {
      return -2;
   }

   return 0;
}

int RemoteControlServerImpl::dragMouse(int x, int y, CPCAPI2::RemoteControl::MouseButton button)
{
   MMMouseButton mmbtn = LEFT_BUTTON;
   CheckMouseButton(button, &mmbtn);
   MMPoint point;
   point = MMPointMake(x, y);
   ::dragMouse(point, mmbtn);
   ::microsleep(mouseDelay);
   return kSuccess;
}

// Get the horizontal and vertical screen sizes in pixel
void GetDesktopResolution(int& horizontal, int& vertical)
{
   RECT desktop;
   // Get a handle to the desktop window
   const HWND hDesktop = GetDesktopWindow();
   // Get the size of screen to the variable desktop
   GetWindowRect(hDesktop, &desktop);
   // The top left corner will have coordinates (0,0)
   // and the bottom right corner will have coordinates
   // (horizontal, vertical)
   horizontal = desktop.right;
   vertical = desktop.bottom;
}

inline int32_t fixed_mul_16(int32_t x, int32_t y)
{
   return ((int64_t)x * (int64_t)y) >> 16;
}

int RemoteControlServerImpl::moveMouse(int x, int y)
{
   int screenWidth, screenHeight = 0;
   GetDesktopResolution(screenWidth, screenHeight);
   //std::cout << "screen: " << screenWidth << ", " << screenHeight << std::endl;
   //std::cout << "x: " << x << ", y: " << y << std::endl;
   int relativeX = fixed_mul_16(screenWidth << 16, x) >> 16;
   int relativeY = fixed_mul_16(screenHeight << 16, y) >> 16;
   //std::cout << "moveMouse: " << relativeX << ", " << relativeY << std::endl;
   MMPoint point;
   point = MMPointMake(relativeX, relativeY);
   ::moveMouse(point);
   ::microsleep(mouseDelay);
   return kSuccess;
}

int RemoteControlServerImpl::moveMouseSmooth(int x, int y)
{
   MMPoint point;
   point = MMPointMake(x, y);
   ::smoothlyMoveMouse(point);
   ::microsleep(mouseDelay);
   return kSuccess;
}

int RemoteControlServerImpl::mouseClick(CPCAPI2::RemoteControl::MouseButton button, bool doubleClick)
{
   MMMouseButton mmbtn = LEFT_BUTTON;
   CheckMouseButton(button, &mmbtn);
   if (!doubleClick)
   {
      ::clickMouse(mmbtn);
   }
   else
   {
      ::doubleClick(mmbtn);
   }

   microsleep(mouseDelay);
   return kSuccess;
}

int RemoteControlServerImpl::mouseToggle(CPCAPI2::RemoteControl::MouseButton button, CPCAPI2::RemoteControl::ButtonDirection direction)
{
   MMMouseButton mmbtn = LEFT_BUTTON;
   bool down = (direction == CPCAPI2::RemoteControl::ButtonDirection_Down);
   CheckMouseButton(button, &mmbtn);
   ::toggleMouse(down, mmbtn); // NB: order of params switched
   ::microsleep(mouseDelay);
   return kSuccess;
}

int RemoteControlServerImpl::setMouseDelay(int delayMs)
{
   mouseDelay = delayMs;
   return kSuccess;
}

int RemoteControlServerImpl::scrollMouse(int scrollMagnitude, CPCAPI2::RemoteControl::ButtonDirection direction)
{
   MMMouseWheelDirection scrollDirection = (direction == CPCAPI2::RemoteControl::ButtonDirection_Down ? DIRECTION_DOWN : DIRECTION_UP);
   ::scrollMouse(scrollMagnitude, scrollDirection);
   ::microsleep(mouseDelay);
   return kSuccess;
}

struct KeyNames
{
   const char* name;
   MMKeyCode   key;
};

static KeyNames key_names[] =
{
   { "backspace", K_BACKSPACE },
   { "delete", K_DELETE },
   { "enter", K_RETURN },
   { "tab", K_TAB },
   { "escape", K_ESCAPE },
   { "up", K_UP },
   { "down", K_DOWN },
   { "right", K_RIGHT },
   { "left", K_LEFT },
   { "home", K_HOME },
   { "end", K_END },
   { "pageup", K_PAGEUP },
   { "pagedown", K_PAGEDOWN },
   { "f1", K_F1 },
   { "f2", K_F2 },
   { "f3", K_F3 },
   { "f4", K_F4 },
   { "f5", K_F5 },
   { "f6", K_F6 },
   { "f7", K_F7 },
   { "f8", K_F8 },
   { "f9", K_F9 },
   { "f10", K_F10 },
   { "f11", K_F11 },
   { "f12", K_F12 },
   { "f13", K_F13 },
   { "f14", K_F14 },
   { "f15", K_F15 },
   { "f16", K_F16 },
   { "f17", K_F17 },
   { "f18", K_F18 },
   { "f19", K_F19 },
   { "f20", K_F20 },
   { "f21", K_F21 },
   { "f22", K_F22 },
   { "f23", K_F23 },
   { "f24", K_F24 },
   { "command", K_META },
   { "alt", K_ALT },
   { "control", K_CONTROL },
   { "shift", K_SHIFT },
   { "right_shift", K_RIGHTSHIFT },
   { "space", K_SPACE },
   { "printscreen", K_PRINTSCREEN },
   { "insert", K_INSERT },

   { "audio_mute", K_AUDIO_VOLUME_MUTE },
   { "audio_vol_down", K_AUDIO_VOLUME_DOWN },
   { "audio_vol_up", K_AUDIO_VOLUME_UP },
   { "audio_play", K_AUDIO_PLAY },
   { "audio_stop", K_AUDIO_STOP },
   { "audio_pause", K_AUDIO_PAUSE },
   { "audio_prev", K_AUDIO_PREV },
   { "audio_next", K_AUDIO_NEXT },
   { "audio_rewind", K_AUDIO_REWIND },
   { "audio_forward", K_AUDIO_FORWARD },
   { "audio_repeat", K_AUDIO_REPEAT },
   { "audio_random", K_AUDIO_RANDOM },

   { "numpad_0", K_NUMPAD_0 },
   { "numpad_1", K_NUMPAD_1 },
   { "numpad_2", K_NUMPAD_2 },
   { "numpad_3", K_NUMPAD_3 },
   { "numpad_4", K_NUMPAD_4 },
   { "numpad_5", K_NUMPAD_5 },
   { "numpad_6", K_NUMPAD_6 },
   { "numpad_7", K_NUMPAD_7 },
   { "numpad_8", K_NUMPAD_8 },
   { "numpad_9", K_NUMPAD_9 },

   { "lights_mon_up", K_LIGHTS_MON_UP },
   { "lights_mon_down", K_LIGHTS_MON_DOWN },
   { "lights_kbd_toggle", K_LIGHTS_KBD_TOGGLE },
   { "lights_kbd_up", K_LIGHTS_KBD_UP },
   { "lights_kbd_down", K_LIGHTS_KBD_DOWN },

   { NULL, K_NOT_A_KEY } /* end marker */
};

int CheckKeyCodes(const char* k, MMKeyCode *key)
{
   if (!key) return -1;

   if (strlen(k) == 1)
   {
      *key = keyCodeForChar(*k);
      return 0;
   }

   *key = K_NOT_A_KEY;

   KeyNames* kn = key_names;
   while (kn->name)
   {
      if (strcmp(k, kn->name) == 0)
      {
         *key = kn->key;
         break;
      }
      kn++;
   }

   if (*key == K_NOT_A_KEY)
   {
      return -2;
   }

   return 0;
}

int CheckKeyFlags(const char* f, MMKeyFlags* flags)
{
   if (!flags) return -1;

   if (strcmp(f, "alt") == 0)
   {
      *flags = MOD_ALT;
   }
   else if (strcmp(f, "command") == 0)
   {
      *flags = MOD_META;
   }
   else if (strcmp(f, "control") == 0)
   {
      *flags = MOD_CONTROL;
   }
   else if (strcmp(f, "shift") == 0)
   {
      *flags = MOD_SHIFT;
   }
   else if (strcmp(f, "none") == 0)
   {
      *flags = MOD_NONE;
   }
   else
   {
      return -2;
   }

   return 0;
}

int GetFlagsFromValue(const cpc::string& value, MMKeyFlags* flags)
{
   if (!flags) return -1;

   std::string s = value.c_str();
   size_t pos = 0;
   std::string token;
   MMKeyFlags f = MOD_NONE;
   while ((pos = s.find("|")) != std::string::npos) {
      token = s.substr(0, pos);
      CheckKeyFlags(token.c_str(), &f);
      *flags = (MMKeyFlags)(*flags | f);
      s.erase(0, pos + 1);
   }
   CheckKeyFlags(s.c_str(), &f);
   *flags = (MMKeyFlags)(*flags | f);
   return 0;
}

int RemoteControlServerImpl::keyTap(const cpc::string& key, const cpc::string& keyFlags)
{
   MMKeyFlags flags = MOD_NONE;
   MMKeyCode keyCode;
   const char* k = key.c_str();
   GetFlagsFromValue(keyFlags, &flags);
   CheckKeyCodes(k, &keyCode);
   ::tapKeyCode(keyCode, flags);
   ::microsleep(keyboardDelay);
   return kSuccess;
}

int RemoteControlServerImpl::keyToggle(const cpc::string& key, CPCAPI2::RemoteControl::ButtonDirection direction, const cpc::string& keyFlags)
{
   bool down = (direction == ButtonDirection_Down);
   MMKeyFlags flags = MOD_NONE;
   MMKeyCode keyCode;
   const char* k = key.c_str();
   GetFlagsFromValue(keyFlags, &flags);
   CheckKeyCodes(k, &keyCode);
   ::toggleKeyCode(keyCode, down, flags);
   ::microsleep(keyboardDelay);
   return kSuccess;
}

int RemoteControlServerImpl::keyToggle(int key, CPCAPI2::RemoteControl::ButtonDirection direction, const cpc::string& keyFlags)
{
   bool down = (direction == ButtonDirection_Down);
   MMKeyFlags flags = MOD_NONE;
   MMKeyCode keyCode = (MMKeyCode)key;
   GetFlagsFromValue(keyFlags, &flags);
   ::toggleKeyCode(keyCode, down, flags);
   ::microsleep(keyboardDelay);
   return kSuccess;
}

int RemoteControlServerImpl::typeString(const cpc::string& str)
{
   ::typeString(str.c_str());
   return kSuccess;
}

int RemoteControlServerImpl::typeStringDelayed(const cpc::string& str, int delayMs)
{
   ::typeStringDelayed(str.c_str(), delayMs);
   return kSuccess;
}

int RemoteControlServerImpl::setKeyboardDelay(int delayMs)
{
   keyboardDelay = delayMs;
   return kSuccess;
}

int RemoteControlServerImpl::handleSendFile(int fileId, int fileSize, const char* fileName, const uint8_t* fileBytes)
{
   using convert_type = std::codecvt_utf8<wchar_t>;
   std::wstring_convert<convert_type, wchar_t> converter;

   PWSTR desktopPath = NULL;
   std::wstring fileNameFull;
   fileNameFull.reserve(MAX_PATH);
   SHGetKnownFolderPath(FOLDERID_Desktop, 0, NULL, &desktopPath);
   fileNameFull.append(desktopPath);
   fileNameFull.append(L"\\Drop\\");
   CPCAPI2::FileUtils::CreateDir(converter.to_bytes(fileNameFull).c_str(), false);
   fileNameFull.append(cpc::string(fileName));

#if _DEBUG
   std::cout << "saving file: " << converter.to_bytes(fileNameFull) << std::endl;
#endif

   std::ofstream fileStream(fileNameFull, std::ios::binary | std::ios::app);
   if (!fileStream)
   {
      return kError;
   }
   fileStream.write((const char*)fileBytes, fileSize);
   fileStream.close();
   return kSuccess;
}


}
}

#endif
