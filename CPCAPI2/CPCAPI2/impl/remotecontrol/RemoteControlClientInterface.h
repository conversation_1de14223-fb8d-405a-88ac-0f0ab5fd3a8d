#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_CLIENT_INTERFACE_H)
#define CPCAPI2_REMOTE_CONTROL_CLIENT_INTERFACE_H

#include "cpcapi2defs.h"
#include "remotecontrol/RemoteControlClient.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>

#include <map>

namespace CPCAPI2
{
class PhoneInterface;

namespace RemoteControl
{
class RemoteControlClientImpl;

class RemoteControlClientInterface : public RemoteControlClient,
                                       public PhoneModule
                                       #ifdef CPCAPI2_AUTO_TEST
                                          , public AutoTestProcessor
                                       #endif
{
public:
   RemoteControlClientInterface(Phone* phone);
   virtual ~RemoteControlClientInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   // RemoteControlClient
   virtual int setIncomingVideoRenderTarget(void* surface) OVERRIDE;
   virtual int connect(const cpc::string& serverWsUrl) OVERRIDE;
   virtual int disconnect() OVERRIDE;
   virtual int sendFile(const cpc::string& filePathFull) OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

private:
   int setIncomingVideoRenderTargetImpl(void* surface);
   int connectImpl(const cpc::string& serverWsUrl);
   int disconnectImpl();
   int sendFileImpl(const cpc::string& filePathFull);

private:
   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
   RemoteControlClientImpl* mImpl;
};

}
}

#endif // CPCAPI2_REMOTE_CONTROL_CLIENT_INTERFACE_H
