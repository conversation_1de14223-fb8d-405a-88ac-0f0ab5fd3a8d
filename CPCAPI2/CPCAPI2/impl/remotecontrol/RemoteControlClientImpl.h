#pragma once

#if !defined(CPCAPI2_REMOTE_CONTROL_CLIENT_IMPL_H)
#define CPCAPI2_REMOTE_CONTROL_CLIENT_IMPL_H

#include "RemoteControlProtoEncoder.h"
#include "../util/cpc_thread.h"

#include <rutil/MultiReactor.hxx>

#include <packager/base/logging.h>
#include <packager/base/atomicops.h>
#include <packager/base/message_loop/message_loop.h>
#include <packager/media/base/user_input_monitor.h>

#include <websocketpp/common/thread.hpp>
#include <websocketpp/config/asio_no_tls_client.hpp>
#include <websocketpp/client.hpp>

namespace CPCAPI2
{
class PhoneInterface;
namespace RemoteControl
{
class RemoteControlClientImpl : public media::UserInputMonitor::MouseEventListener,
                                public media::UserInputMonitor::KeyboardEventListener
{
public:
   RemoteControlClientImpl(CPCAPI2::PhoneInterface* phone);
   virtual ~RemoteControlClientImpl();

   int setIncomingVideoRenderTarget(void* surface);
   int connectWebsocketClient(const std::string& uri);
   int disconnectWebsocketClient();
   int sendFile(const std::string& fileNameUtf8);

   // media::UserInputMonitor::MouseEventListener
   virtual void OnMouseMoved(const media::SkIPoint& position);
   virtual void OnMouseButton(unsigned short dwFlags, const media::SkIPoint& position);

   // media::UserInputMonitor::KeyboardEventListener
   virtual void OnKeyPressed(uint8_t keycode);
   virtual void OnKeyReleased(uint8_t keycode);

private:
   typedef websocketpp::lib::thread thread_type;
   typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;
   typedef websocketpp::client<websocketpp::config::asio_client> client;
   typedef websocketpp::config::asio_client::message_type::ptr message_ptr;

   void initWebsocketClient();
   void shutdownWebsocketClient();
   void stopInputMonitor();
   int sendData(const std::string& data);
   int getWindowDimsImpl(int& left, int& top, int& right, int& bottom);
   int getVideoStreamDims(unsigned int& width, unsigned int& height);

   void on_message(websocketpp::connection_hdl, message_ptr msg);
   void handle_on_message(websocketpp::connection_hdl, message_ptr msg);
   void on_fail(websocketpp::connection_hdl);
   void handle_on_fail(websocketpp::connection_hdl, std::error_code ec);
   void on_open(websocketpp::connection_hdl);
   void handle_on_open(websocketpp::connection_hdl);
   void on_close(websocketpp::connection_hdl);
   void handle_on_close(websocketpp::connection_hdl);

   void inputThread();

private:
   std::unique_ptr<base::MessageLoopForUI> mMessageLoop;
   std::unique_ptr<media::UserInputMonitor> mUserInputMonitor;
   std::unique_ptr<RemoteControlProtoEncoder> mProtoEnc;
   client m_endpoint;
   thread_ptr m_thread;
   websocketpp::connection_hdl m_con;
   resip::MultiReactor& mReactor;
   CPCAPI2::PhoneInterface* mPhone;
   void* mScreenShareSurface;
   std::unique_ptr<CPCAPI2::thread> mInputThread;
   media::SkIPoint mLastMousePos;
};
}
}

#endif // CPCAPI2_REMOTE_CONTROL_CLIENT_IMPL_H
