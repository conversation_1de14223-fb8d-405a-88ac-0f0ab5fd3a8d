#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_CONTROL_MODULE == 1)
#include "cpcapi2utils.h"
#include "RemoteControlServerInterface.h"
#include "RemoteControlServerImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace RemoteControl
{
RemoteControlServerInterface::RemoteControlServerInterface(Phone* phone)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mImpl(NULL)
{
}

RemoteControlServerInterface::~RemoteControlServerInterface()
{
   mShutdown = true;
   interruptProcess();
}

void RemoteControlServerInterface::Release()
{
   delete this;
}

int RemoteControlServerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kRemoteControlModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kRemoteControlModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* RemoteControlServerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void RemoteControlServerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void RemoteControlServerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void RemoteControlServerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
}

int RemoteControlServerInterface::start()
{
   post(resip::resip_bind(&RemoteControlServerInterface::startImpl, this));
   return kSuccess;
}

int RemoteControlServerInterface::startImpl()
{
   if (mImpl == NULL)
   {
      mImpl = new RemoteControlServerImpl();
   }
   return kSuccess;
}

int RemoteControlServerInterface::shutdown()
{
   post(resip::resip_bind(&RemoteControlServerInterface::shutdownImpl, this));
   return kSuccess;
}

int RemoteControlServerInterface::shutdownImpl()
{
   delete mImpl;
   mImpl = NULL;
   return kSuccess;
}


int RemoteControlServerInterface::processRemoteRequest(const cpc::string& request)
{
   post(resip::resip_bind(&RemoteControlServerInterface::processRemoteRequestImpl, this, request));
   return kSuccess;
}

int RemoteControlServerInterface::processRemoteRequestImpl(const cpc::string& request)
{
   return mImpl->processRemoteRequest(request);
}

}
}

#endif
