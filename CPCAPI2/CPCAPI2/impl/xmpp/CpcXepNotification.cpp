//
//  CpcXepNotification.cpp
//  BriaVoip
//
//  Created by <PERSON> on Januray 19, 2017.
//  Copyright 2017 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepNotification.h"

const std::string CpcXepNotification::XMLNS_NOTIFICATION = "urn:xmpp:push:0";

CpcXepNotification::CpcXepNotification(bool enable, const gloox::JID& jid, const std::string& node, const gloox::DataForm& form)
   : StanzaExtension(EXepNotification)
   , mEnable(enable)
   , mJid(jid)
   , mNode(node)
   , mForm(form)
{
}

CpcXepNotification::~CpcXepNotification()
{
}

const std::string& CpcXepNotification::filterString() const
{
   static const std::string filter =
      "/iq/enable[@xmlns='" + XMLNS_NOTIFICATION + "']"
      "|/iq/disable[@xmlns='" + XMLNS_NOTIFICATION + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepNotification::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepNotification(*this);
}

gloox::Tag* CpcXepNotification::tag() const
{
   gloox::Tag* _tag = new gloox::Tag(mEnable ? "enable" : "disable", "xmlns", XMLNS_NOTIFICATION);

   if (mEnable)
   {
      _tag->addAttribute("jid", mJid.bare()); // bare jid according to https://xmpp.org/extensions/xep-0357.html#enabling
      _tag->addAttribute("node", mNode);
      if (!mForm.fields().empty()) _tag->addChild(mForm.tag()); // bliu: data form <reported> is not used in XEP-0357
   }
   else
   {
      _tag->addAttribute("jid", mJid.bare()); // bare jid according to https://xmpp.org/extensions/xep-0357.html#disabling
      if (!mNode.empty()) _tag->addAttribute("node", mNode);
   }

   return _tag;
}

gloox::StanzaExtension* CpcXepNotification::clone() const
{
   return new CpcXepNotification(*this);
}
