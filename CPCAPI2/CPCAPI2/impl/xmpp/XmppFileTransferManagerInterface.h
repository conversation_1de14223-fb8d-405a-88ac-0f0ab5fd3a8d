#pragma once

#ifndef __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_INTERFACE_H__
#define __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"

#include "phone/PhoneModule.h"
#include "../experimental/phone/Permissions.h"
#include "xmpp/XmppFileTransferManager.h"
#include "XmppAccountInterface.h"
#include "XmppFileTransferInfo.h"
#include "XmppCommon.h"

namespace CPCAPI2
{
// Forward declarations
class Phone;

namespace XmppFileTransfer
{
// Forward declarations
class XmppFileTransferManagerImpl;

// Main file transfer interface
class XmppFileTransferManagerInterface
   : public XmppFileTransferManager
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppFileTransferManagerImpl>
{
public:
   XmppFileTransferManagerInterface( Phone* phone );
   virtual ~XmppFileTransferManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // XmppFileTransferManager
   virtual int setHandler( XmppAccount::XmppAccountHandle account, XmppFileTransferHandler* handler ) OVERRIDE;
   virtual XmppFileTransferItemHandle createFileTransferItem( XmppAccount::XmppAccountHandle account ) OVERRIDE;
   virtual XmppFileTransferHandle createFileTransfer( XmppAccount::XmppAccountHandle account ) OVERRIDE;
   virtual int addParticipant( XmppFileTransferHandle fileTransfer, const cpc::string& targetAddress ) OVERRIDE;
   virtual int start( XmppFileTransferHandle fileTransfer ) OVERRIDE;
   virtual int end( XmppFileTransferHandle fileTransfer ) OVERRIDE;
   virtual int configureFileTransferItems( XmppFileTransferHandle fileTransfer, const XmppFileTransferItems& fileItems ) OVERRIDE;
   virtual int accept( XmppFileTransferHandle fileTransfer ) OVERRIDE;
   virtual int reject( XmppFileTransferHandle fileTransfer, const cpc::string& reason ) OVERRIDE;
   virtual int cancelItem( XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem ) OVERRIDE;

   PhoneInterface* phoneInterface();
   void addSdkObserver(XmppFileTransferHandler* observer);
   void removeSdkObserver(XmppFileTransferHandler* observer);

   void onPermissionGranted(int requestCode, CPCAPI2::Permission permission);

private: // methods invoked by DumFp thread asynchronously
   void setHandlerImpl( XmppAccount::XmppAccountHandle account, XmppFileTransferHandler* handler );
   void createFileTransferItemImpl( XmppAccount::XmppAccountHandle account, XmppFileTransferItemHandle itemHandle );
   void createFileTransferImpl( XmppAccount::XmppAccountHandle account, XmppFileTransferHandle sessionHandle );
   void addParticipantImpl( XmppFileTransferHandle fileTransfer, const cpc::string& targetAddress );
   void startImpl( XmppFileTransferHandle fileTransfer );
   void endImpl( XmppFileTransferHandle fileTransfer );
   void configureFileTransferItemsImpl( XmppFileTransferHandle fileTransfer, const XmppFileTransferItems& fileItems );
   void acceptImpl( XmppFileTransferHandle fileTransfer );
   void rejectImpl( XmppFileTransferHandle fileTransfer, const cpc::string& reason);
   void cancelItemImpl( XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem );

private: // helpers
   XmppFileTransferInfo* getFileTransferInfo( XmppFileTransferHandle h );

private: // data
   XmppAccount::XmppAccountInterface* mAccountIf;
   PhoneInterface* mPhone;
   std::list<XmppFileTransferHandler*> mSdkObservers;

   typedef std::set<XmppFileTransferHandle> PendingTransfersList;
   PendingTransfersList mIncomingPendingPermission;
   PendingTransfersList mOutgoingPendingPermission;
};

}
}

#endif // __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_INTERFACE_H__
