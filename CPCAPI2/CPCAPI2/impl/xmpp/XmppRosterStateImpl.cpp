#include "XmppRosterStateImpl.h"
#include "XmppRosterInterface.h"

#include "brand_branded.h"

#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ROSTER

#if (CPCAPI2_BRAND_XMPP_ROSTER_STATE_MODULE == 1)

namespace CPCAPI2
{

namespace XmppRoster
{

XmppRosterStateImpl::XmppRosterStateImpl(XmppRosterInterface* rosterIf) :
mRosterIf(rosterIf)
{
   StackLog(<< "XmppRosterStateImpl::XmppRosterStateImpl(): " << this);
   XmppAccount::XmppAccountManager* xmppAccount = XmppAccount::XmppAccountManager::getInterface(rosterIf->phoneInterface());
   XmppAccount::XmppAccountInterface* xmppAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(xmppAccount);
   xmppAccountIf->addSdkObserver(this);
}

XmppRosterStateImpl::~XmppRosterStateImpl()
{
   mAccountMap.clear();
   for (RosterStateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); i++)
   {
      i->second.clear();
   }
   mStateMap.clear();
   StackLog(<< "XmppRosterStateImpl::~XmppRosterStateImpl(): " << this);
}

void XmppRosterStateImpl::Release()
{
   StackLog(<< "XmppRosterStateImpl::Release(): " << this);
   delete this;
}

int XmppRosterStateImpl::getRosterState(CPCAPI2::XmppRoster::XmppRosterHandle roster, XmppRosterState& state)
{
   DebugLog(<< "XmppRosterStateImpl::getRosterState(): " << this << " roster: " << roster << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size() <<
            " roster item list size: " << getItemCount());
   RosterStateMap::iterator i = mStateMap.find(roster);
   if (i == mStateMap.end())
   {
      InfoLog(<< "XmppRosterStateImpl::getRosterState(): " << this << " invalid roster handle: " << roster);
      return kError;
   }
   
   state.roster = i->first;
   state.account = getAccountHandle(i->first);
   
   for (RosterStateMap::mapped_type::iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      state.rosterItems.push_back(j->second);
   }
   
   return kSuccess;
}

   
int XmppRosterStateImpl::getRosterStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppRosterState& state)
{
   XmppRosterHandle roster = getRosterHandle(account);
   StackLog(<< "XmppRosterStateImpl::getRosterStateByAccount(): " << this << " account: " << account << " roster: " << roster << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size() <<
            " roster item list size: " << getItemCount());
   getRosterState(roster, state);
   if (account != state.account)
   {
      DebugLog(<< "XmppRosterStateImpl::getRosterStateForAccount(): " << this << " handle mismatch between account: " << account << " and state account: " << state.account << " for roster: " << roster);
      state.account = account;
      return kError;
   }
   
   return kSuccess;
}
   
int XmppRosterStateImpl::getAllRosterState(cpc::vector<XmppRosterState>& states)
{
   StackLog(<< "XmppRosterStateImpl::getAllRosterState(): " << this << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size());
   for (RosterStateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); i++)
   {
      XmppRosterState state;
      state.roster = i->first;
      state.account = getAccountHandle(i->first);
      for (RosterStateMap::mapped_type::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         state.rosterItems.push_back(j->second);
      }
      states.push_back(state);
   }
   return kSuccess;
}
   
CPCAPI2::XmppAccount::XmppAccountHandle XmppRosterStateImpl::getAccountHandle(XmppRosterHandle roster)
{
   RosterAccountMap::iterator i = mAccountMap.find(roster);
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppRosterStateImpl::getAccountHandle(): " << this << " no account mapping found for roster: " << roster);
      return 0;
   }
   
   CPCAPI2::XmppAccount::XmppAccountHandle account = i->second;
   StackLog(<< "XmppRosterStateImpl::getAccountHandle(): " << this << " roster: " << roster << " is mapped to account: " << account);
   return account;
}
  
XmppRosterHandle XmppRosterStateImpl::getRosterHandle(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   for (RosterAccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); i++)
   {
      if (i->second == account)
      {
         StackLog(<< "XmppRosterStateImpl::getRosterHandle(): " << this << " account: " << account << " is mapped to roster: " << i->first);
         return (i->first);
      }
   }
   
   DebugLog(<< "XmppRosterStateImpl::getRosterHandle(): " << this << " no roster mapping found for account: " << account);
   return 0;
}
  
// XmppRosterHandlerInternal

int XmppRosterStateImpl::onCreateRosterResult(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterCreatedResultEvent& args)
{
   StackLog(<< "XmppRosterStateImpl::onCreateRosterResult(): " << this << " roster: " << roster << " account: " << args.account << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size() <<
            " roster item list size: " << getItemCount());
   if ((mAccountMap.count(roster) != 0) && (mAccountMap[roster] != roster))
   {
      DebugLog(<< "XmppRosterStateImpl::onCreateRosterResult(): " << this << " mismatch in roster account map, roster: " << roster << " account: " << args.account << " mapped roster: " << mAccountMap[args.account]);
   }
      
   mAccountMap[roster] = args.account;
      
   if (mStateMap.count(roster) != 0)
   {
      DebugLog(<< "XmppRosterStateImpl::onCreateRosterResult(): " << this << " mismatch in roster state map, roster: " << roster << " already has existing roster items");
   }
      
   mStateMap.erase(roster);
      
   return kSuccess;
}
   
// XmppRosterHandler

int XmppRosterStateImpl::onRosterUpdate(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUpdateEvent& args)
{
   // StackLog(<< "XmppRosterStateImpl::onRosterUpdate(): " << this << " roster: " << roster << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size() << " roster item list size: " << getItemCount());
   RosterAccountMap::iterator i = mAccountMap.find(roster);
   
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppRosterStateImpl::onRosterUpdate(): " << this << " no account mapping found for roster: " << roster);
      return kError;
   }

   if (args.fullUpdate)
   {
      mStateMap.erase(roster);
   }
   
   RosterStateMap::iterator j = mStateMap.find(roster);
   if (j == mStateMap.end())
   {
      DebugLog(<< "XmppRosterStateImpl::onRosterUpdate(): " << this << " no existing state found for roster: " << roster);
   }

   if (args.added.size() > 0)
   {
      if (j == mStateMap.end())
      {
         RosterStateMap::mapped_type items;
         for (cpc::vector<XmppRosterUpdateEvent::ChangeItemAdd>::const_iterator k = args.added.begin(); k != args.added.end(); k++)
         {
            items.insert(std::make_pair((*k).item.address, (*k).item));
         }
      
         mStateMap[roster] = items;
      }
      else
      {
         RosterStateMap::mapped_type& items = mStateMap[roster];
         
         for (cpc::vector<XmppRosterUpdateEvent::ChangeItemAdd>::const_iterator k = args.added.begin(); k != args.added.end(); k++)
         {
            items.insert(std::make_pair((*k).item.address, (*k).item));
         }
      }
   }
   
   if (args.updated.size() > 0)
   {
      if (j == mStateMap.end())
      {
         RosterStateMap::mapped_type items;
         for (cpc::vector<XmppRosterUpdateEvent::ChangeItemUpdate>::const_iterator k = args.updated.begin(); k != args.updated.end(); k++)
         {
            items.insert(std::make_pair((*k).item.address, (*k).item));
         }
         
         mStateMap[roster] = items;
      }
      else
      {
         RosterStateMap::mapped_type& items = mStateMap[roster];
         for (cpc::vector<XmppRosterUpdateEvent::ChangeItemUpdate>::const_iterator k = args.updated.begin(); k != args.updated.end(); k++)
         {
            items[(*k).item.address] = (*k).item;
         }
      }
   }
   
   if (args.removed.size() > 0)
   {
      if (j != mStateMap.end())
      {
         RosterStateMap::mapped_type& items = mStateMap[roster];
         for (cpc::vector<XmppRosterUpdateEvent::ChangeItemRemove>::const_iterator k = args.removed.begin(); k != args.removed.end(); k++)
         {
            items.erase((*k).address);
         }
      }
   }

   printRosterStatus();
   return kSuccess;
}
   
int XmppRosterStateImpl::onSubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterSubscriptionRequestEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onUnsubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUnsubscriptionRequestEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onRosterPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args)
{
   // StackLog(<< "XmppRosterStateImpl::onRosterPresence(): " << this << " roster: " << roster << " account map size: " << mAccountMap.size() << " roster map size: " << mStateMap.size() << " roster item list size: " << getItemCount());
   RosterAccountMap::iterator i = mAccountMap.find(roster);
   
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppRosterStateImpl::onRosterPresence(): " << this << " no account mapping found for roster: " << roster);
      return kError;
   }
   
   RosterStateMap::iterator j = mStateMap.find(roster);
   if (j == mStateMap.end())
   {
      DebugLog(<< "XmppRosterStateImpl::onRosterPresence(): " << this << " no existing state found for roster: " << roster);
      return kError;
   }
         
   RosterStateMap::mapped_type& items = mStateMap[roster];
   RosterStateMap::mapped_type::iterator k = items.find(args.rosterItem.address);
   
   if (k == items.end())
   {
      DebugLog(<< "XmppRosterStateImpl::onRosterPresence(): " << this << " no existing roster item found for jid: " << args.rosterItem.address << " for roster handle: " << roster);
      return kSuccess;
   }

   k->second = args.rosterItem;
   return kSuccess;
}
   
int XmppRosterStateImpl::onSelfPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onError(CPCAPI2::XmppRoster::XmppRosterHandle roster, const ErrorEvent& args)
{
   return kSuccess;
}

int XmppRosterStateImpl::onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
   // StackLog(<< "XmppRosterStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " account-list: " << mAccountMap.size());
   if (args.accountStatus == XmppAccount::XmppAccountStatusChangedEvent::Status_Destroyed)
   {
      for (RosterAccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); ++i)
      {
         if ((i->second) == account)
         {
            DebugLog(<< "XmppRosterStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " destroyed with roster: " << i->first << " before deletion account-list: " << mStateMap.size());
            mStateMap.erase(i->first);
            mAccountMap.erase(i);
            break;
         }
      }
   }
   
   return kSuccess;
}
   
int XmppRosterStateImpl::onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args)
{
   return kSuccess;
}
   
int XmppRosterStateImpl::getItemCount()
{
   int itemCount(0);
   for (RosterStateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      itemCount += i->second.size();
   }
   return itemCount;
}
                                                          
void XmppRosterStateImpl::printRosterStatus()
{
   std::stringstream os;
   for (RosterStateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      XmppRosterState state;
      state.roster = i->first;
      state.account = getAccountHandle(i->first);
      os << std::endl << "Roster: " << i->first << " Account: " << getAccountHandle(i->first) << " Item Count: " << i->second.size() << " { ";
      for (RosterStateMap::mapped_type::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         os << "{ address: " << (*j).first << " item: " << (*j).second << " }";
      }
      os << " }";
   }
   
   StackLog(<< "XmppRosterStateImpl::printRosterStatus(): " << os.str().c_str());
}

}

}

#endif // CPCAPI2_BRAND_XMPP_ROSTER_STATE_MODULE
