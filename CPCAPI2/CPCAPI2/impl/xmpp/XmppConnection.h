#pragma once

#if !defined(CPCAPI2_XMPP_CONNECTION_H)
#define CPCAPI2_XMPP_CONNECTION_H

#include "cpcapi2defs.h"
#include "brand_branded.h"

#include <atomic>

#include <resip/stack/Tuple.hxx>
#include <rutil/dns/QueryTypes.hxx>
#include <rutil/dns/DnsStub.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/Socket.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <client.h>
#include <connectiontcpclient.h>
#include <connectiontcpserver.h>
#include <connectionhandler.h>

#include "xmpp/XmppAccountSettings.h"

#define XMPP_CLIENT_SERVICE_NAME "xmpp-client"
#define XMPP_CLIENT_SERVICE_PORT 5222
#define XMPP_CLIENT_CONNECT_TIMEOUT 10

namespace CPCAPI2
{

// We are using the DNS functions from RESIP
class XmppConnectionTcpClient
   : public gloox::ConnectionTCPClient
   , public resip::DnsResultSink
   , public resip::ReactorEventHandler
   , public resip::DeadlineTimerHandler
   , public resip::ReactorBinded
{
public:
   struct SrvRecord
   {
      std::string target;
      unsigned int port;
      unsigned int priority;
      unsigned int weight;
      double weightRandomValue; // used for weighted random shuffle
   };

   struct DnsSettings
   {
      resip::DnsStub::NameserverList nameServers;
      bool includeSystemDnsServers;

      DnsSettings() : nameServers(resip::DnsStub::EmptyNameserverList), includeSystemDnsServers(true) {}
   };

public:
   XmppConnectionTcpClient(
      resip::MultiReactor& reactor,
      gloox::ConnectionDataHandler* cdh,
      const gloox::LogSink& logInstance,
      const std::string& server,
      int port = -1,
      XmppAccount::IpVersion ipVersion = XmppAccount::IpVersion_V4,
      unsigned int connectTimeOut = XMPP_CLIENT_CONNECT_TIMEOUT,
      const std::string& serviceName = XMPP_CLIENT_SERVICE_NAME,
      resip::TransportType = resip::TCP,
      DnsSettings dnsSettings = DnsSettings());

   virtual ~XmppConnectionTcpClient();

   const resip::Tuple& target() const { return mTarget; }

   bool isWritable(); // for XmppFileSender::transfer()

   void setReactorWorker(resip::MultiReactor* reactor) { mReactorWorker = reactor; }
   resip::MultiReactor* getReactorWorker() { return mReactorWorker; }
   resip::MultiReactor& getReactor() { return mReactor; }

public: // resip::ReactorEventHandler interface
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual unsigned int getTimeTillNextProcessMS() OVERRIDE;
   virtual const char* getEventHandlerDesc() const OVERRIDE { return "XmppConnectionTcpClient"; }

public: // resip::DnsResultSink interface
   virtual void onDnsResult(const resip::DNSResult<resip::DnsHostRecord>& res) OVERRIDE;
   virtual void onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>& res) OVERRIDE;
   virtual void onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>& res) OVERRIDE;
   virtual void onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>& res) OVERRIDE;
   virtual void onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>& res) OVERRIDE;

public: // resip::DeadlineTimerHandler interface
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

public: // gloox::ConnectionTCPClient
   virtual void _release() OVERRIDE;
   virtual void cleanup() OVERRIDE;
   virtual gloox::ConnectionError recv(int timeout = -1) OVERRIDE;
   virtual gloox::ConnectionError connect() OVERRIDE;
   virtual bool send(const std::string& data) OVERRIDE;
   virtual gloox::ConnectionBase* newInstance() const OVERRIDE;

protected: // gloox::ConnectionTCPClient
   virtual bool dataAvailable(int timeout) OVERRIDE { return true; } // not used with overrided recv() but kept to align with XmppConnectionTcpServer

public: // resip::ReactorBinded
   virtual void release() OVERRIDE { delete this; }

private:
   XmppConnectionTcpClient(const XmppConnectionTcpClient& other);
   void lookup(const resip::Data& name);
   void startAsyncLookup();
   void startAsyncConnect();
   void currentHostFailed(gloox::ConnectionError error);
   void arrangeSrvRecords();
   void selectNextSrvRecord();
   void buildFdSetForResolveTask(resip::ReactorEventHandler::FdSetType& fdset);
   void buildFdSetForConnectTask(resip::ReactorEventHandler::FdSetType& fdset);
   void buildFdSetForDataTask(resip::ReactorEventHandler::FdSetType& fdset);
   void processResolveTask(resip::ReactorEventHandler::FdSetType& fdset);
   void processConnectTask(resip::ReactorEventHandler::FdSetType& fdset);
   void processDataTask(resip::ReactorEventHandler::FdSetType& fdset);
   void connectCompleted();
   void connectFailed(gloox::ConnectionError error);

   void sendBuffer();

   // run in the main SDK thread
   void handleDisconnect(gloox::ConnectionError reason);
   void handleReceivedData(const std::string& data);

private:
   resip::MultiReactor& mReactor;
   std::string mServiceName;
   resip::TransportType mTransportType;
   XmppAccount::IpVersion mIpVersion;
   gloox::ConnectionError mError;
   resip::DnsStub* mDnsStub;
   std::list<resip::Tuple> mRecords;
   std::list<SrvRecord> mSrvRecords;
   UInt64 mTimeOut;
   unsigned int mSrvResultPort;
   unsigned int mConnectTimeOut;
   unsigned int mConnectAttempts;

   int mIndex;
   std::set<int> mDnsLookupSet;

   resip::Tuple mTarget;

   std::string mBuffer;

   resip::DeadlineTimer<resip::MultiReactor>* mTimer;

   resip::MultiReactor* mReactorWorker;
};

class XmppConnectionTcpServer
   : public gloox::ConnectionTCPServer
   , public resip::ReactorEventHandler
   , public gloox::ConnectionHandler
{
public:
   XmppConnectionTcpServer(resip::MultiReactor& reactor, gloox::ConnectionHandler* ch, const gloox::LogSink& logInstance, const std::string& ip, int port);
   virtual ~XmppConnectionTcpServer();

public: // resip::ReactorEventHandler interface
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual unsigned int getTimeTillNextProcessMS() OVERRIDE { return 300000; /* 5 mins */ }
   virtual const char* getEventHandlerDesc() const OVERRIDE { return "XmppConnectionTcpServer"; }

protected: // gloox::ConnectionTCPServer
   virtual bool dataAvailable(int timeout) OVERRIDE { return true; }
   virtual gloox::ConnectionTCPBase* newConnectionTCPClient(const std::string& server, int port) OVERRIDE;

protected: // gloox::ConnectionHandler
   virtual void handleIncomingConnection(ConnectionBase* server, ConnectionBase* connection) OVERRIDE;

private:
   resip::MultiReactor& mReactor;
   gloox::ConnectionHandler* mConnectionHandler;
};

} // CPCAPI2
#endif // CPCAPI2_ASYNC_TCP_RESOLVER_H
