//
//  CpcXepUserActivity.cpp
//  BriaVoip
//
//  Created by <PERSON> on 2/8/11.
//  Copyright 2011 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepUserActivity.h"

#include "tag.h"
#include "util.h"

static const char* generalTypeValues[] = {
   "doing_chores",
   "drinking",
   "eating",
   "exercising",
   "grooming",
   "having_appointment",
   "inactive",
   "relaxing",
   "talking",
   "traveling",
   "undefined",
   "working"
};

static const char* specificTypeValues[] = {
   "at_the_spa",
   "brushing_teeth",
   "buying_groceries",
   "cleaning",
   "coding",
   "commuting",
   "cooking",
   "cycling",
   "dancing",
   "day_off",
   "doing_maintenance",
   "doing_the_dishes",
   "doing_the_laundry",
   "driving",
   "fishing",
   "gaming",
   "gardening",
   "getting_a_haircut",
   "going_out",
   "hanging_out",
   "having_a_beer",
   "having_a_snack",
   "having_breakfast",
   "having_coffee",
   "having_dinner",
   "having_lunch",
   "having_tea",
   "hiding",
   "hiking",
   "in_a_car",
   "in_a_meeting",
   "in_real_life",
   "jogging",
   "on_a_bus",
   "on_a_plane",
   "on_a_train",
   "on_a_trip",
   "on_the_phone",
   "on_vacation",
   "on_video_phone",
   "other",
   "partying",
   "playing_sports",
   "praying",
   "reading",
   "rehearsing",
   "running",
   "running_an_errand",
   "scheduled_holiday",
   "shaving",
   "shopping",
   "skiing",
   "sleeping",
   "smoking",
   "socializing",
   "studying",
   "sunbathing",
   "swimming",
   "taking_a_bath",
   "taking_a_shower",
   "thinking",
   "walking",
   "walking_the_dog",
   "watching_a_movie",
   "watching_tv",
   "working_out",
   "writing"
};

static inline CpcXepUserActivity::GeneralType activityGeneralType(const std::string& generalType)
{
   return (CpcXepUserActivity::GeneralType)gloox::util::deflookup(generalType, generalTypeValues, CpcXepUserActivity::ActivityInvalidGeneralType);
}

static inline CpcXepUserActivity::SpecificType activitySpecificType(const std::string& specificType)
{
   return (CpcXepUserActivity::SpecificType)gloox::util::deflookup(specificType, specificTypeValues, CpcXepUserActivity::ActivityInvalidSpecificType);
}

const std::string CpcXepUserActivity::XMLNS_USER_ACTIVITY = "http://jabber.org/protocol/activity";

CpcXepUserActivity::CpcXepUserActivity(const CpcXepUserActivity& activity)
   : StanzaExtension(EXepUserActivity)
   , _generalType(activity._generalType)
   , _specificType(activity._specificType)
   , _text(activity._text)
{
}

CpcXepUserActivity::CpcXepUserActivity(const gloox::Tag* tag)
   : StanzaExtension(EXepUserActivity)
   , _generalType(ActivityInvalidGeneralType)
   , _specificType(ActivityInvalidSpecificType)
{
   if (tag)
   {
      const gloox::TagList& childTags = tag->children();
      for (gloox::TagList::const_iterator it=childTags.begin(); it!=childTags.end(); ++it)
      {
         if ( (*it)->name() == "text" )
         {
            _text[(*it)->findAttribute("xml:lang")] = (*it)->cdata();
         }
      }
      for (gloox::TagList::const_iterator it=childTags.begin(); it!=childTags.end(); ++it)
      {
         if ( (*it)->name() != "text" )
         {
            _generalType = activityGeneralType( (*it)->name() );
            if (_generalType != ActivityInvalidGeneralType)
            {
               // Found a general type we understand, now find the specific type
               const gloox::TagList& specificTags = (*it)->children();
               for (gloox::TagList::const_iterator jt=specificTags.begin(); jt!=specificTags.end(); ++jt)
               {
                  _specificType = activitySpecificType( (*jt)->name() );
                  if ( _specificType != ActivityInvalidSpecificType )
                     break;
               }
               break;
            }
         }
      }
   }
}

CpcXepUserActivity::~CpcXepUserActivity()
{
}

CpcXepUserActivity::GeneralType CpcXepUserActivity::generalType() const
{
   return _generalType;
}

void CpcXepUserActivity::setGeneralType(CpcXepUserActivity::GeneralType generalType)
{
   _generalType = generalType;
}

CpcXepUserActivity::SpecificType CpcXepUserActivity::specificType() const
{
   return _specificType;
}

void CpcXepUserActivity::setSpecificType(CpcXepUserActivity::SpecificType specificType)
{
   _specificType = specificType;
}

const std::string& CpcXepUserActivity::text(const std::string& lang) const
{
   gloox::StringMap::const_iterator it = _text.find( lang );
   return it != _text.end() ? (*it).second : gloox::EmptyString;
}

void CpcXepUserActivity::setText(const std::string& text, const std::string& lang)
{
   _text[lang] = text;
}

const std::string& CpcXepUserActivity::filterString() const
{
   // TODO: make sure it works with pubsub and PEP to interoperate with third party XMPP servers/clients
   // DCM: "/presence/activity" is deprecated but needed to IOT with previous Bria clients
   static const std::string filter =
      "/presence/activity[@xmlns='" + XMLNS_USER_ACTIVITY + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepUserActivity::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepUserActivity(tag);
}

gloox::Tag* CpcXepUserActivity::tag() const
{
   if (_generalType == ActivityInvalidGeneralType)
      return 0;

   gloox::Tag* a = new gloox::Tag( "activity", "xmlns", XMLNS_USER_ACTIVITY );
   gloox::Tag* g = new gloox::Tag( gloox::util::lookup((unsigned)_generalType, generalTypeValues) );
   a->addChild(g);
   
   if (_specificType != ActivityInvalidSpecificType)
   {
      gloox::Tag* s = new gloox::Tag( gloox::util::lookup((unsigned)_specificType, specificTypeValues) );
      g->addChild(s);
   }
   for (gloox::StringMap::const_iterator it=_text.begin(); it!=_text.end(); ++it)
   {
      gloox::Tag* txt = new gloox::Tag( a, "text" );
      txt->addAttribute( "xml:lang", it->first );
      txt->setCData( it->second );
   }

   return a;
}

gloox::StanzaExtension* CpcXepUserActivity::clone() const
{
   return new CpcXepUserActivity( *this );
}
