#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)

#include "XmppChatInfo.h"

#include <jid.h>
#include <message.h>
#include <messagesession.h>
#include <messageeventfilter.h>
#include <chatstatefilter.h>

namespace CPCAPI2
{
namespace XmppChat
{

XmppChatInfo::XmppChatInfo()
   : accountHandle(0)
   , handle(0)
   , mGlooxMessageSession(NULL)
   , mGlooxMessageEventFilter(NULL)
   , mGlooxChatStateFilter(NULL)
   , mMessageFilter(NULL)
   , mGlooxSessionEventHandler(NULL)
   , mInactiveTimer(NULL)
   , mIsActive(false)
   , mReplaceMessageSupported(false)
{
}

XmppChatInfo::~XmppChatInfo()
{
   delete mInactiveTimer;
   for (auto& discoTimer : mPeerDiscoveryTimers) {
      delete discoTimer.second;
   }

   // Destroy all the message info objects
   for (XmppChatMessageInfoMap::const_iterator iter = messageInfoMap.begin(); iter != messageInfoMap.end(); iter++)
   {
      XmppChatMessageInfo* messageInfo = iter->second;
      assert(messageInfo);
      delete messageInfo;
   }
}

XmppChatMessageInfo* XmppChatInfo::getMessageInfo(const XmppChatMessageHandle& handle) const
{
   XmppChatMessageInfoMap::const_iterator it = messageInfoMap.find(handle);
   return (it != messageInfoMap.end()) ? it->second : NULL;
}

void XmppChatInfo::addMessageInfo(const XmppChatMessageHandle& handle, XmppChatMessageInfo* messageInfo)
{
   assert(!messageInfoMap[handle] || messageInfoMap[handle] == messageInfo);

   // Keep the mapping message handle -> message information
   messageInfoMap[handle] = messageInfo;
}

bool XmppChatInfo::hasMessageId(const XmppChatMessageHandle& handle)
{
   return messageInfoMap.find(handle) != messageInfoMap.end();
}

}
}

#endif
