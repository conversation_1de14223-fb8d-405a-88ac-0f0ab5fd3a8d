#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)

#include "XmppPushManagerImpl.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "util/ReactorHelpers.h"
#include "log/LocalLogger.h"

#include <iq.h>
#include <disco.h>
#include <error.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_PUSH

namespace CPCAPI2
{
namespace XmppPush
{

bool operator ==(const GroupChatFilter& lhs, const GroupChatFilter& rhs)
{
   if (lhs.rule != rhs.rule) return false;
   if (lhs.jid != rhs.jid) return false;
   if (lhs.nick != rhs.nick) return false;
   return true;
}

bool operator !=(const GroupChatFilter& lhs, const GroupChatFilter& rhs)
{
   return !(lhs == rhs);
}

template<typename T>
bool operator ==(const cpc::vector<T>& lhs, const cpc::vector<T>& rhs)
{
   if (lhs.size() != rhs.size()) return false;

   for (size_t i = 0; i < lhs.size(); ++i)
   {
      if (lhs[i] != rhs[i]) return false;
   }

   return true;
}

template<typename T>
bool operator !=(const cpc::vector<T>& lhs, const cpc::vector<T>& rhs)
{
   return !(lhs == rhs);
}

const std::string XmppPushManagerImpl::PUSH_REGISTER_COMMAND   = "register-device";
const std::string XmppPushManagerImpl::PUSH_UNREGISTER_COMMAND = "unregister-device";
const std::string XmppPushManagerImpl::PUSH_PROVIDER_FIELD     = "provider";
const std::string XmppPushManagerImpl::PUSH_DEVICE_FIELD       = "device-token";
#ifdef ANDROID
const std::string XmppPushManagerImpl::PUSH_PROVIDER = "fcm-xmpp-api";              //imap.mobilevoiplive.com & tigase.im
#elif defined(CPCAPI2_AUTO_TEST)
const std::string XmppPushManagerImpl::PUSH_PROVIDER = "tigase:messenger:apns:1";   //tigase.im
#else
const std::string XmppPushManagerImpl::PUSH_PROVIDER = "apns-binary-api";           //imap.mobilevoiplive.com
#endif

XmppPushManagerImpl::XmppPushManagerImpl(PhoneInterface* phone, XmppAccount::XmppAccountImpl& account, XmppPushManagerInterface& intf)
   : mAppHandler(NULL)
   , mAccount(account)
   , mInterface(intf)
   , mPhone(phone)
   , mDisconnecting(false)
   , mAdhoc(NULL)
   , mNotificationsComponent("")
   , mPendingServer("")
   , mNode("")
   , mDeviceToken("")
   , mInited(false)
{
   mAccount.registerAccountObserver(this);
   mAccount.registerDiscoObserver(this);
}

XmppPushManagerImpl::~XmppPushManagerImpl()
{
   mAccount.unregisterDiscoObserver(this);
   mAccount.unregisterAccountObserver(this);

   cleanup();
}

void XmppPushManagerImpl::setHandler(XmppPushHandler* handler)
{
   StackLog(<< "XmppPushManagerImpl::setHandler(): handler: " << handler);
   mAppHandler = handler;
}

void XmppPushManagerImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppPushManagerImpl::onWillConnect()");
}

void XmppPushManagerImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppPushManagerImpl::onDidConnect()");
   if (NULL != mAdhoc)
   {
      ErrLog(<< "XmppPushManagerImpl::onDidConnect: gloox::Adhoc not cleaned up!");
      return;
   }
   mAdhoc = new gloox::Adhoc(mAccount.getGlooxClient());
}

void XmppPushManagerImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppPushManagerImpl::onWillDisconnect()");
   mDisconnecting = true;
}
void XmppPushManagerImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppPushManagerImpl::onDidDisconnect()");
   cleanup();
   mDisconnecting = false;
}
void XmppPushManagerImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppPushManagerImpl::onDestroy()");
   cleanup();
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppPushManagerImpl::onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info)
{
   if (mDisconnecting || !mAccount.isConnected()) return;

   if (info.hasFeature(getProvider()))
   {
      DebugLog(<< "Found Push Notifications component: " << from.bare());
      mPendingServer = from.bare();
   }
   if (info.hasFeature(CpcXepNotification::XMLNS_NOTIFICATION))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotification::XMLNS_NOTIFICATION);
      mNotificationsSupport.insert(from.bare());
   }

   // TODO: check if from XMPP server or push notification component
   //if (from != mAccount.getGlooxClient()->server()) return;

   if (info.hasFeature(CpcXepNotificationTigase::XMLNS_IGNORE_UNKNOWN))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotificationTigase::XMLNS_IGNORE_UNKNOWN);
      mFeatures.insert(TigaseFeature::FilterIgnoreUnknown);
   }
   if (info.hasFeature(CpcXepNotificationTigase::XMLNS_FILTER_MUTED))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotificationTigase::XMLNS_FILTER_MUTED);
      mFeatures.insert(TigaseFeature::FilterMuted);
   }
   if (info.hasFeature(CpcXepNotificationTigase::XMLNS_FILTER_GROUPCHAT))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotificationTigase::XMLNS_FILTER_GROUPCHAT);
      mFeatures.insert(TigaseFeature::FilterGroupchat);
   }
   if (info.hasFeature(CpcXepNotificationTigase::XMLNS_AWAY))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotificationTigase::XMLNS_AWAY);
      mFeatures.insert(TigaseFeature::Away);
   }
   if (info.hasFeature(CpcXepNotificationTigase::XMLNS_PRIORITY))
   {
      DebugLog(<< from.bare() << " supports " << CpcXepNotificationTigase::XMLNS_PRIORITY);
      mFeatures.insert(TigaseFeature::Priority);
   }
}

void XmppPushManagerImpl::onXmppDiscoCompleted()
{
   DebugLog(<< "XmppPushManagerImpl::onXmppDiscoCompleted(): mPendingServer=" << mPendingServer);
   if (mDisconnecting || !mAccount.isConnected()) return;

   if (!mPendingServer.empty())
   {
      init();
   }
}

void XmppPushManagerImpl::init()
{
   if (!checkConnection()) return;

   if (mPendingServer.empty())
   {
      ErrLog(<< "XmppPushManagerImpl::init(): init called with undefined pending server!");
      return;
   }

   if (mNotificationsComponent != mPendingServer)
   {
      DebugLog(<< "XmppPushManagerImpl::init(): Checking Adhoc support on " << mPendingServer);

      mCommands.clear();
      mInited = false;
      mNotificationsComponent = mPendingServer;
      if (NULL == mAdhoc)
      {
         WarningLog(<< "XmppPushManagerImpl::init(): gloox::Adhoc is NULL!");
         mAdhoc = new gloox::Adhoc(mAccount.getGlooxClient());
      }
      mAdhoc->checkSupport(mNotificationsComponent, this, Context::AdHocSupport);
   }
   else
   {
      DebugLog(<< "XmppPushManagerImpl::init(): notification component unchanged, nothing to do " << mPendingServer);
   }
}

void XmppPushManagerImpl::configurePushSettings(const XmppPushSettings& settings)
{
   mPendingSettings = settings;
}

void XmppPushManagerImpl::applyPushSettings()
{
   if (!mFeatures.count(TigaseFeature::FilterIgnoreUnknown)) WarningLog(<< "XmppPushManagerImpl::applyPushSettings: ignoreUnknownSender not supported");
   if (!mFeatures.count(TigaseFeature::FilterMuted)) WarningLog(<< "XmppPushManagerImpl::applyPushSettings: mutedContacts not supported");
   if (!mFeatures.count(TigaseFeature::FilterGroupchat)) WarningLog(<< "XmppPushManagerImpl::applyPushSettings: groupChatFilter not supported");
   if (!mFeatures.count(TigaseFeature::Away)) WarningLog(<< "XmppPushManagerImpl::applyPushSettings: sendNotificationsWhileAway not supported");

   bool ignoreChanged = (mPendingSettings.ignoreUnknownSender != mSettings.ignoreUnknownSender);
   bool awayChanged = (mPendingSettings.sendNotificationsWhileAway != mSettings.sendNotificationsWhileAway);
   bool mutedChanged = (mPendingSettings.mutedContacts != mSettings.mutedContacts);
   bool filterChanged = (mPendingSettings.groupChatFilter != mSettings.groupChatFilter);
   bool providerChanged = (mPendingSettings.provider != mSettings.provider);

   if (providerChanged)
   {
      // Discovery needs to be re-done to determine "provider" support on the server
      WarningLog(<< "To apply provider change, xmpp push needs to be re-started (reconnect the xmpp account)!");
   }

   if (ignoreChanged || awayChanged || mutedChanged || filterChanged || providerChanged)
   {
      mSettings = mPendingSettings;
   }
}

void XmppPushManagerImpl::pushRegister(const cpc::string&  deviceToken)
{
   std::string error;
   if (mNotificationsComponent.empty())
   {
      WarningLog(<< "XmppPushManagerImpl::pushRegister: push notification component unknown!");
      error = "Push notification component not provided";
   }
   if (!mInited)
   {
      WarningLog(<< "XmppPushManagerImpl::pushRegister: Tigase push not initialized (ad-hoc support check didn't finish?)");
      error = "Push not initialized";
   }
   if (!mCommands.count(AdhocCommand::RegisterDevice))
   {
      WarningLog(<< "XmppPushManagerImpl::pushRegister: Tigase push register not supported (ad-hoc support check didn't finish?)");
      error = "Push registration not supported";
   }

   if (!error.empty() || !checkConnection())
   {
      fireError(Error_AdhocDiscovery, error.c_str());
      return;
   }

   mDeviceToken = deviceToken.c_str();
   DebugLog(<< "XmppPushManagerImpl::pushRegister: registering device " << mDeviceToken << ", provider: " << getProvider());
   gloox::Adhoc::Command* cmd = new gloox::Adhoc::Command(PUSH_REGISTER_COMMAND, gloox::Adhoc::Command::Action::Execute);

   if (NULL == mAdhoc)
   {
      WarningLog(<< "gloox::AdHoc not yet inited");
      fireError(Error_PushRegister, "gloox::AdHoc not yet inited");
      return;
   }

   mAdhoc->execute(mNotificationsComponent, cmd, this, Context::AdHocExecute0);
}

void XmppPushManagerImpl::pushUnregister(const cpc::string& deviceToken)
{
   if (mNotificationsComponent.empty())
   {
      WarningLog(<< "XmppPushManagerImpl::pushUnregister: push notification component unknown!");
      return;
   }
   if (mNode.empty())
   {
      WarningLog(<< "XmppPushManagerImpl::pushUnregister: node not configured (push not registered?)");
   }
   if (!mInited)
   {
      WarningLog(<< "XmppPushManagerImpl::pushUnregister: Tigase push not initialized (ad-hoc support check didn't finish?)");
   }

   if (deviceToken.c_str() != mDeviceToken) 
   {
      if (mDeviceToken.empty())
      {
         DebugLog(<< "XmppPushManagerImpl::pushUnregister: updating device token");
         mDeviceToken = deviceToken.c_str();
      }
      else
      {
         WarningLog(<< "XmppPushManagerImpl::pushUnregister: deviceTokens don't match");
      }
   }

   if (!mCommands.count(AdhocCommand::UnregisterDevice))
   {
      WarningLog(<< "XmppPushManagerImpl::pushUnregister: Tigase push unregister not supported (ad-hoc support check didn't finish?)");
   }

   DebugLog(<< "XmppPushManagerImpl::pushUnregister: unegistering device " << mDeviceToken << ", provider: " << getProvider());
   gloox::Adhoc::Command* cmd = new gloox::Adhoc::Command(PUSH_UNREGISTER_COMMAND, gloox::Adhoc::Command::Action::Execute);

   if (!checkConnection()) return;
   if (NULL == mAdhoc)
   {
      WarningLog(<< "gloox::AdHoc already destroyed");
      fireError(Error_PushUnregister, "gloox::AdHoc already destroyed");
   }
   mAdhoc->execute(mNotificationsComponent, cmd, this, Context::AdHocExecute0);
}

void XmppPushManagerImpl::setIgnoreUnknownSender(bool ignoreUnknown)
{
   if (!mFeatures.count(TigaseFeature::FilterIgnoreUnknown))
   {
      WarningLog(<< "XmppPushManagerImpl::setIgnoreUnknownSender: ignoreUnknownSender not supported!");
      //return;
   }

   if (mSettings.ignoreUnknownSender != ignoreUnknown)
   {
      DebugLog(<< "XmppPushManagerImpl::setIgnoreUnknownSender: ignoreUnknown:" << ignoreUnknown);
      mSettings.ignoreUnknownSender = ignoreUnknown;
   }
}

void XmppPushManagerImpl::setSendNotificationsWhileAway(bool sendWhileAway)
{
   if (!mFeatures.count(TigaseFeature::Away))
   {
      WarningLog(<< "XmppPushManagerImpl::setSendNotificationsWhileAway: sendNotificationsWhileAway not supported!");
      //return;
   }

   if (mSettings.sendNotificationsWhileAway != sendWhileAway)
   {
      DebugLog(<< "XmppPushManagerImpl::setSendNotificationsWhileAway: sendWhileAway:" << sendWhileAway);
      mSettings.sendNotificationsWhileAway = sendWhileAway;
   }
}

void XmppPushManagerImpl::setContactMuted(const cpc::string& contactJid, bool muted)
{
   if (!mFeatures.count(TigaseFeature::FilterMuted))
   {
      WarningLog(<< "XmppPushManagerImpl::setContactMuted: filter muted not supported!");
      //return;
   }

   DebugLog(<< "XmppPushManagerImpl::setContactMuted: " << contactJid.c_str() << " muted:" << muted);
   
   gloox::JID cJid = gloox::JID(contactJid.c_str());
   cpc::vector<cpc::string>::iterator it = mSettings.mutedContacts.begin();
   //clear all in case of duplicates
   while (it != mSettings.mutedContacts.end())
   {
      if (*it == cJid.bare().c_str()) it = mSettings.mutedContacts.erase(it);
      else ++it;
   }
   if (muted)
   {
      mSettings.mutedContacts.push_back(cJid.bare().c_str());
   }

}

void XmppPushManagerImpl::setGroupChatFilter(const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick)
{
   if (!mFeatures.count(TigaseFeature::FilterGroupchat))
   {
      WarningLog(<< "XmppPushManagerImpl::setGroupChatFilter: groupchat filter not supported!");
      //return;
   }

   DebugLog(<< "XmppPushManagerImpl::setGroupChatFilter: " << roomJid.c_str() << " rule:" << rule << " (nick: " << nick << ")");

   gloox::JID rJid = gloox::JID(roomJid.c_str());
   cpc::vector<GroupChatFilter>::iterator it = mSettings.groupChatFilter.begin();
   //clear all in case of duplicates
   while (it != mSettings.groupChatFilter.end())
   {
      if (it->jid == rJid.bare().c_str()) it = mSettings.groupChatFilter.erase(it);
      else ++it;
   }

   GroupChatFilter gcf;
   gcf.rule = rule;
   gcf.jid = rJid.bare().c_str();
   if (rule == GroupChatFilterRule::Mentioned) gcf.nick = nick;

   mSettings.groupChatFilter.push_back(gcf);
}

void XmppPushManagerImpl::enableNotifications(bool enable)
{
   if (mNode.empty()) return;
   if (mNotificationsSupport.empty())
   {
      fireError(Error_PushSetup, (CpcXepNotification::XMLNS_NOTIFICATION + " not supported").c_str());
      return;
   }
   if (!checkConnection()) return;

   DebugLog(<< "XmppPushManagerImpl::enableNotifications: enable:" << enable);

   gloox::JID jid = (mNotificationsComponent.empty()) ? gloox::JID(mAccount.getGlooxClient()->server()) : gloox::JID(mNotificationsComponent);
   if (!mNotificationsSupport.count(jid.bare()))
   {
      WarningLog(<< "XmppPushManagerImpl::enableNotifications " << jid.bare() << " does not support " << CpcXepNotification::XMLNS_NOTIFICATION << ", using " << *mNotificationsSupport.begin());
      jid.setJID(*mNotificationsSupport.begin());
   }

   gloox::IQ iq (gloox::IQ::Set, jid);
   CpcXepNotificationTigase* nt = new CpcXepNotificationTigase(enable, mNotificationsComponent, mNode, mSettings.ignoreUnknownSender, mSettings.sendNotificationsWhileAway);
   if (enable)
   {
      nt->setMutedContacts(mSettings.mutedContacts);
      nt->setGroupChatFilter(mSettings.groupChatFilter);
   }

   iq.addExtension(nt);
   mAccount.getGlooxClient()->send(iq);
}

void XmppPushManagerImpl::fireError(Error code, const cpc::string& errorText)
{
   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppPush: " + errorText);
      return;
   }

   PushErrorEvent evt;
   evt.errorCode = code;
   evt.errorText = errorText;
   fireEvent(cpcFunc(XmppPushHandler::onPushConfigError), evt);
}

//gloox::AdhocHandler
void XmppPushManagerImpl::handleAdhocSupport(const gloox::JID& remote, bool support, int context)
{
    if (context == Context::AdHocSupport)
   {
      if (remote == mNotificationsComponent)
      {
         if (support)
         {
            if (!checkConnection()) return;
            DebugLog(<< "XmppPushManagerImpl::handleAdhocSupport: getCommands: " << mNotificationsComponent);
            if (NULL == mAdhoc)
            {
               WarningLog(<< "gloox::AdHoc is null");
               fireError(Error_PushSetup, "gloox::AdHoc is null");
               return;
            }
            mAdhoc->getCommands(mNotificationsComponent, this, Context::AdHocCommands);
         }
         else
         {
            std::stringstream ss;
            ss << remote << " does not support adhoc commands!";
            WarningLog(<< "XmppPushManagerImpl::handleAdhocSupport: " << ss.str());
            fireError(Error_AdhocDiscovery, ss.str().c_str());
         }
      }
      else
      {
         DebugLog(<< "XmppPushManagerImpl::handleAdhocSupport: response from unexpected jid: " << remote);
      }
   }
   else
   {
      DebugLog(<< "XmppPushManagerImpl::handleAdhocSupport: unexcpected context: " << context);
   }
}
void XmppPushManagerImpl::handleAdhocCommands(const gloox::JID& remote, const gloox::StringMap& commands, int context)
{
    if (context == Context::AdHocCommands)
    {
      if (remote == mNotificationsComponent)
      {
         std::stringstream ss;
         if (commands.count(PUSH_REGISTER_COMMAND))
         {
            DebugLog(<< "XmppPushManagerImpl::handleAdhocCommands: " << PUSH_REGISTER_COMMAND << " supported");
            mCommands.insert(AdhocCommand::RegisterDevice);
         }
         else
         {
            ss << PUSH_REGISTER_COMMAND << " not supported";
         }

         if (commands.count(PUSH_UNREGISTER_COMMAND))
         {
            DebugLog(<< "XmppPushManagerImpl::handleAdhocCommands: " << PUSH_UNREGISTER_COMMAND << " supported");
            mCommands.insert(AdhocCommand::UnregisterDevice);
         }
         else
         {
            ss << " " << PUSH_UNREGISTER_COMMAND << " not supported";
         }

         mInited = true;
         
         if (mCommands.count(AdhocCommand::RegisterDevice) && mCommands.count(AdhocCommand::UnregisterDevice))
         {
            DebugLog(<< "Supported commands: " << PUSH_REGISTER_COMMAND << ", " << PUSH_UNREGISTER_COMMAND);

            PushConfigEvent evt;
            evt.pushNotificationsComponent = mNotificationsComponent.c_str();
            fireEvent(cpcFunc(XmppPushHandler::onPushConfigured), evt);
         }
         else
         {
            WarningLog(<< "XmppPushManagerImpl::handleAdhocCommands: " << ss.str());
            fireError(Error_AdhocDiscovery, ss.str().c_str());
         }
      }
      else
      {
         DebugLog(<< "XmppPushManagerImpl::handleAdhocCommands: response from unexpected jid: " << remote);
      }
   }
   else
   {
      DebugLog(<< "XmppPushManagerImpl::handleAdhocCommands: unexpected context: " << context);
   }
}
void XmppPushManagerImpl::handleAdhocError(const gloox::JID& remote, const gloox::Error* error, int context)
{
   if (remote == mNotificationsComponent)
   {
      std::stringstream ss;
      ss << error->text() << " In context " << context;
      WarningLog(<< "XmppPushManagerImpl::handleAdhocError: " << ss.str());
      fireError(Error_AdhocDiscovery, ss.str().c_str());
   }
   else
   {
      DebugLog(<< "XmppPushManagerImpl::handleAdhocError: response from unexpected jid: " << remote);
   }
}
void XmppPushManagerImpl::handleAdhocExecutionResult(const gloox::JID& remote, const gloox::Adhoc::Command& command, int context)
{
   if (remote == mNotificationsComponent)
   {
      if (context == Context::AdHocExecute0)
      {
         gloox::DataForm* form = new gloox::DataForm(*command.form());
         if (command.node() == PUSH_REGISTER_COMMAND || command.node() == PUSH_UNREGISTER_COMMAND)
         {
            if (!form->hasField(PUSH_PROVIDER_FIELD) || !form->hasField(PUSH_DEVICE_FIELD))
            {
               fireError(Error_AdhocExecute, "Unexpected form fields!");
               return;
            }

            if (!checkConnection()) return;

            form->setType(gloox::FormType::TypeSubmit);
            form->field(PUSH_PROVIDER_FIELD)->setValue(getProvider());
            form->field(PUSH_DEVICE_FIELD)->setValue(mDeviceToken);
            gloox::Adhoc::Command* cmd = new gloox::Adhoc::Command(command.node(), command.sessionID(), gloox::Adhoc::Command::Action::Next, form);
            if (NULL == mAdhoc)
            {
               WarningLog(<< "gloox::AdHoc is null");
               fireError(Error_PushSetup, "gloox::AdHoc is null");
               return;
            }
            mAdhoc->execute(mNotificationsComponent, cmd, this, Context::AdHocExecute1);
         }
         else
         {
            DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: unexcpected command: " << command.node());
         }
      }
      else if (context == Context::AdHocExecute1)
      {
         if (command.node() == PUSH_REGISTER_COMMAND)
         {
            if (command.form()->type() == gloox::FormType::TypeResult && command.form()->hasField("node"))
            {
               mNode = command.form()->field("node")->value();
               DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: " << command.node() << ": " << command.form()->title());

               PushEvent evt;
               evt.deviceToken = mDeviceToken.c_str();
               fireEvent(cpcFunc(XmppPushHandler::onPushRegistered), evt);
            }
            else
            {
               ErrLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: invalid state (status: " << command.status() <<
                  ", hasNodeField: " << command.form()->hasField("node") << ")");
               fireError(Error_AdhocExecute, "Invalid execution state");
            }
         }
         else if (command.node() == PUSH_UNREGISTER_COMMAND)
         {
            if (command.form()->type() == gloox::FormType::TypeResult)
            {
               DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: " << command.node() << ": " << command.form()->title());

               PushEvent evt;
               evt.deviceToken = mDeviceToken.c_str();
               fireEvent(cpcFunc(XmppPushHandler::onPushUnregistered), evt);

               mNode = "";
            }
            else
            {
               ErrLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: invalid state (status: " << command.status() <<
                  ", type: " << command.form()->type() << ")");
            }
         }
         else
         {
            DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: unexcpected command: " << command.node());
         }
      }
      else
      {
         DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: unexcpected context: " << context);
      }
   }
   else
   {
      DebugLog(<< "XmppPushManagerImpl::handleAdhocExecutionResult: response from unexpected jid: " << remote);
   }
}

std::string XmppPushManagerImpl::getProvider()
{
   if (mSettings.provider.empty()) return PUSH_PROVIDER;
   return mSettings.provider.c_str();
}

bool XmppPushManagerImpl::checkConnection()
{
   if (!mDisconnecting && mAccount.isConnected())
   {
      return true;
   }
   else
   {
      WarningLog(<< "XMPP account not connected!");
      fireError(Error_PushSetup, "XMPP account not connected");
   }
   return false;
}

MucRegistration* XmppPushManagerImpl::getRegistration(const std::string& roomJid)
{
   std::set<MucRegistration*>::iterator it = mMucRegistrations.begin();
   for (; it != mMucRegistrations.end(); ++it)
   {
      MucRegistration* reg = *it;
      if (reg->mJid == roomJid)
      {
         return reg;
      }
   }
   return NULL;
}

void XmppPushManagerImpl::registerOfflineMessageDelivery(const cpc::string& roomJid, const cpc::string& nick)
{
   DebugLog(<< "XmppPushManagerImpl::registerOfflineMessageDelivery");
   if (!checkConnection()) return;

   if (roomJid.empty() || nick.empty())
   {
      ErrLog(<< "XmppPushManagerImpl::registerOfflineMessageDelivery: no jid/nick defined");
      return;
   }
   gloox::JID rJid = gloox::JID(roomJid.c_str());
   MucRegistration* offlineReg = getRegistration(rJid.bare());
   
   if (NULL == offlineReg)
   {
      DebugLog(<< "XmppPushManagerImpl::registerOfflineMessageDelivery: creating registration for " << rJid.bare());
      offlineReg = new MucRegistration();
      offlineReg->mJid = rJid.bare();
      offlineReg->mRegistration = new gloox::Registration(mAccount.getGlooxClient(), rJid.bare());
      offlineReg->mRegistration->registerRegistrationHandler(this);
      mMucRegistrations.insert(offlineReg);
   }

   offlineReg->mNick = nick.c_str();
   offlineReg->mRegistration->fetchRegistrationFields();
}

//gloox::RegistrationHandler
void XmppPushManagerImpl::handleRegistrationFields( const gloox::JID& from, int fields, std::string instructions )
{
   DebugLog(<< "XmppPushManagerImpl::handleRegistrationFields: from=" << from.bare());
}
void XmppPushManagerImpl::handleAlreadyRegistered( const gloox::JID& from )
{
   DebugLog(<< "XmppPushManagerImpl::handleAlreadyRegistered: from=" << from.bare());
}
void XmppPushManagerImpl::handleRegistrationResult( const gloox::JID& from, gloox::RegistrationResult regResult )
{
   DebugLog(<< "XmppPushManagerImpl::handleRegistrationResult: from=" << from.bare());
   PushMucRegistrationEvent evt;
   evt.roomJid = from.bare().c_str();
   
   if (regResult == gloox::RegistrationResult::RegistrationSuccess)
   {
      DebugLog(<< "XmppPushManagerImpl::handleRegistrationResult: registration success");
      evt.success = true;
   }
   else
   {
      evt.success = false;
      getMucRegistrationErrorText(regResult, evt.errorText);
      WarningLog(<< "XmppPushManagerImpl::handleRegistrationResult: registration failed with " << evt.errorText.c_str());
   }

   fireEvent(cpcFunc(XmppPushHandler::onMucRegistrationResult), evt);
}
void XmppPushManagerImpl::handleDataForm( const gloox::JID& from, const gloox::DataForm& form )
{
   DebugLog(<< "XmppPushManagerImpl::handleDataForm: from=" << from.bare());

   const std::string typeVal = "http://jabber.org/protocol/muc#register";
   const std::string nickVar = "muc#register_roomnick";
   const std::string offlineVar = "{http://tigase.org/protocol/muc}offline";

   gloox::DataFormField* formType = form.field("FORM_TYPE");
   if (NULL == formType || formType->value() != typeVal)
   {
      ErrLog(<< "XmppPushManagerImpl::handleDataForm: invalid form");
      return;
   }
   if (!form.hasField(nickVar) || !form.hasField(offlineVar))
   {
      ErrLog(<< "XmppPushManagerImpl::handleDataForm: form missing expected fields");
      return;
   }

   MucRegistration* offlineReg = getRegistration(from.bare());
   if (NULL == offlineReg)
   {
      ErrLog(<< "XmppPushManagerImpl::handleDataForm: No registration found for " << from.bare());
      return;
   }

   gloox::DataForm* submitForm = new gloox::DataForm(form);

   submitForm->setType(gloox::FormType::TypeSubmit);
   submitForm->field(nickVar)->setValue(offlineReg->mNick);
   submitForm->field(offlineVar)->setValue("true");

   offlineReg->mRegistration->createAccount(submitForm);
}
void XmppPushManagerImpl::handleOOB( const gloox::JID& from, const gloox::OOB& oob )
{
   DebugLog(<< "XmppPushManagerImpl::handleOOB: from=" << from.bare());
}

void XmppPushManagerImpl::cleanup()
{
   mAccount.getAccountInterface().getReactor().postMS(resip::resip_static_bind(resip::default_delete<gloox::Adhoc>, mAdhoc), 1000);
   mAdhoc = NULL;

   std::set<MucRegistration*>::iterator it = mMucRegistrations.begin();
   while (it != mMucRegistrations.end())
   {
      MucRegistration* reg = (*it);
      DebugLog(<< "XmppPushManagerImpl::cleanup(): Removing registration for " << reg->mJid);
      reg->mRegistration->removeRegistrationHandler();
      delete reg->mRegistration;
      it = mMucRegistrations.erase(it);
      delete reg;
   }
   DebugLog(<< "XmppPushManagerImpl::cleanup(): registration count: " << mMucRegistrations.size());
}

void XmppPushManagerImpl::getMucRegistrationErrorText(gloox::RegistrationResult regResult, cpc::string& errorText)
{
   switch(regResult)
   {
   case gloox::RegistrationSuccess:
      errorText = "Registration Success";
      break;
   case gloox::RegistrationNotAcceptable:
      errorText = "Registration Not Acceptable";
      break;
   case gloox::RegistrationConflict:
      errorText = "Registration Conflict";
      break;
   case gloox::RegistrationNotAuthorized:
      errorText = "Not Authorized";
      break;
   case gloox::RegistrationBadRequest:
      errorText = "Bad Request";
      break;
   case gloox::RegistrationForbidden:
      errorText = "Forbidden";
      break;
   case gloox::RegistrationRequired:
      errorText = "Registration Required";
      break;
   case gloox::RegistrationUnexpectedRequest:
      errorText = "Unexpected Request";
      break;
   case gloox::RegistrationNotAllowed:
      errorText = "Not Allowed";
      break;
   //case gloox::RegistrationUnknownError: 
   default:
      errorText = "Unknown error";
      break;
   }
}

}
}

#endif //#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)
