//
//  CpcXepMessageProcessingHint.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 05, 2021.
//  Copyright 2021 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageProcessingHint.h"

const std::string CpcXepMessageProcessingHint::XMLNS_MESSAGE_PROCESSING_HINT = "urn:xmpp:hints";

CpcXepMessageProcessingHint::CpcXepMessageProcessingHint(MessageProcessingHintType type)
   : StanzaExtension(EXepMessageProcessingHint)
   , mType(type)
   , mValid(true)
{
}

CpcXepMessageProcessingHint::CpcXepMessageProcessingHint(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageProcessingHint)
   , mType(MessageProcessingHintType_Invalid)
   , mValid(false)
{
   if (tag == NULL) return;

   if (tag->name() == "store-offline") mType = MessageProcessingHintType_StoreOffline;
   else if (tag->name() == "store") mType = MessageProcessingHintType_Store;
   else return;

   mValid = true;
}

const std::string& CpcXepMessageProcessingHint::filterString() const
{
   static std::string filter;
   switch (mType)
   {
      case MessageProcessingHintType_Store:
         filter = "/message/store[@xmlns='" + XMLNS_MESSAGE_PROCESSING_HINT + "']";
         break;
      case MessageProcessingHintType_StoreOffline:
         filter = "/message/store-offline[@xmlns='" + XMLNS_MESSAGE_PROCESSING_HINT + "']";
         break;
      default:
         assert(false);
   }

   return filter;
}

gloox::StanzaExtension* CpcXepMessageProcessingHint::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageProcessingHint(tag);
}

gloox::Tag* CpcXepMessageProcessingHint::tag() const
{
   std::string type;

   switch (mType)
   {
   case MessageProcessingHintType_StoreOffline:
      type = "store-offline";
      break;
   case MessageProcessingHintType_Store:
      type = "store";
      break;
   default:
      assert(false);
      return NULL;
   }

   gloox::Tag* t = new gloox::Tag(type, "xmlns", XMLNS_MESSAGE_PROCESSING_HINT);

   if (!mValid) return NULL;

   return t;
}

gloox::StanzaExtension* CpcXepMessageProcessingHint::clone() const
{
   return new CpcXepMessageProcessingHint(*this);
}
