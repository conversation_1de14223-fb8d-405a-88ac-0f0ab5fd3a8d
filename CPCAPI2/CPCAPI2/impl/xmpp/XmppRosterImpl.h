#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_IMPL_H)
#define CPCAPI2_XMPP_ROSTER_IMPL_H

#include "cpcapi2defs.h"
#include <rosterlistener.h>
#include "XmppAccountImpl.h"
#include "XmppRosterInterface.h"
#include "xmpp/XmppRosterHandler.h"
#include "xmpp/XmppRosterHandlerInternal.h"
#include "xmpp/XmppRosterSyncHandler.h"

namespace CPCAPI2
{

namespace XmppRoster
{

class XmppRosterHandler;
class XmppRosterHandlerInternal;

class XmppRosterImpl
   : public XmppAccount::XmppAccountObserver
   , public gloox::RosterListener
   , public std::enable_shared_from_this<XmppRosterImpl>
{

public:

   XmppRosterImpl(XmppAccount::XmppAccountImpl& account, XmppRosterInterface& intf);
   virtual ~XmppRosterImpl();

   void addSdkObserver(XmppRosterHandlerInternal* sdkObserver);
   void removeSdkObserver(XmppRosterHandlerInternal* sdkObserver);
   void setHandler(XmppRosterHandler* handler);

   void acceptSubscriptionRequest(const cpc::string& address);
   void rejectSubscriptionRequest(const cpc::string& address);
   void cancelAcceptedSubscription(const cpc::string& address, const cpc::string& message);

   void addRosterItem(const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void updateRosterItem(const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void removeRosterItem(const cpc::string& address);

   void subscribePresence(const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message);
   void unsubscribePresence(const cpc::string& address);

   static std::atomic<XmppRosterHandle> sNextRosterHandle;

private:

   // XmppAccount::XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;

   // gloox::RosterListener
   virtual void handleItemAdded(const gloox::JID& jid) OVERRIDE;
   virtual void handleItemSubscribed(const gloox::JID& jid) OVERRIDE;
   virtual void handleItemRemoved(const gloox::JID& jid) OVERRIDE;
   virtual void handleItemUpdated(const gloox::JID& jid) OVERRIDE;
   virtual void handleItemUnsubscribed(const gloox::JID& jid) OVERRIDE;
   virtual void handleRoster(const gloox::Roster& roster) OVERRIDE;
   virtual void handleRosterPresence(const gloox::RosterItem& item, const std::string& resource, const gloox::Presence& presence) OVERRIDE;
   virtual void handleSelfPresence(const gloox::RosterItem& item, const std::string& resource, const gloox::Presence& presence) OVERRIDE;
   virtual bool handleSubscriptionRequest(const gloox::JID& jid, const std::string& msg) OVERRIDE;
   virtual bool handleUnsubscriptionRequest(const gloox::JID& jid, const std::string& msg) OVERRIDE;
   virtual void handleNonrosterPresence(const gloox::Presence& presence) OVERRIDE;
   virtual void handleRosterError(const gloox::IQ& iq) OVERRIDE;

private:

   friend class XmppRosterInterface;

   int setHandle(XmppRosterHandle h);
   XmppRosterHandle getHandle() { return mHandle; }
   void cleanup();

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, XmppRosterHandle handle, const TEvt& evt)
   {
      for (std::list<XmppRosterHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, handle, evt);
         if (dynamic_cast<XmppRosterSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }

      if (mAppHandler != reinterpret_cast<XmppRosterHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, handle, evt);
         mAccount.postCallback(cb);
      }
   }

   // specialization for XmppRosterHandlerInternal which cannot be handled by mAppHandler
   void fireEvent(const char* funcName, int (XmppRosterHandlerInternal::*func)(XmppRosterHandle, const XmppRosterCreatedResultEvent&), XmppRosterCreatedResultEvent& args)
   {
      for (std::list<XmppRosterHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, args);
         if (dynamic_cast<XmppRosterSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }
   }

   void fireError(const cpc::string& errorText);
   void fireAdded(const gloox::JID& jid);
   void fireUpdated(const gloox::JID& jid);
   void fireRemoved(const gloox::JID& jid);

   void toResourceItem(const std::string& resName, const gloox::Resource& resItem, XmppRoster::ResourceItem& item);
   void toRosterItem(const gloox::RosterItem& rosItem, XmppRoster::RosterItem& item);
   void toRosterItem(const gloox::JID& jid, XmppRoster::RosterItem& item);

private:

   XmppAccount::XmppAccountImpl& mAccount;
   XmppRosterInterface& mInterface;
   XmppRosterHandler* mAppHandler;
   std::list<XmppRosterHandlerInternal*> mSdkObservers;
   XmppRosterHandle mHandle;

};

} // namespace CPCAPI2

} // namespace XmppRoster

#endif // CPCAPI2_XMPP_ROSTER_IMPL_H
