#pragma once

#ifndef __CPCAPI2_XMPPFILETRANSFERINFO_H__
#define __CPCAPI2_XMPPFILETRANSFERINFO_H__

#include "cpcapi2defs.h"

#include "xmpp/XmppFileTransferManager.h"
#include "xmpp/XmppFileTransferHandler.h"
#include "xmpp/XmppAccount.h"

#include <list>
#include <map>
#include <future>

#include <jid.h>

#include "util/CurlPPProgress.h"

namespace CPCAPI2
{
namespace XmppFileTransfer
{

struct XmppFileTransferItemInfo : XmppFileTransferItemDetail
{
   XmppFileTransferItemInfo( XmppFileTransferItemHandle newHandle );
   ~XmppFileTransferItemInfo();

   std::string fileFullPath; // path + name
   std::list<std::string> sids; // gloox stream id
   bool isDone; // for tracing different status of items in one transfer session

   CurlPPProgress* XEP0363Progress;
   std::future<void> XEP0363Uploader;
};

typedef std::map<XmppFileTransferItemHandle, XmppFileTransferItemInfo*> XmppFileTransferItemInfoMap;

struct XmppFileTransferInfo
{
   XmppFileTransferInfo( XmppFileTransferHandle newHandle, XmppAccount::XmppAccountHandle account);

   // file transfer items "contained" within this transfer
   std::list<XmppFileTransferItemHandle>  transferItems;

   XmppAccount::XmppAccountHandle         accountHandle;
   XmppFileTransferHandle                 handle;

   gloox::JID                             remoteAddress;
};

typedef std::map<XmppFileTransferHandle, XmppFileTransferInfo*> XmppFileTransferInfoMap;

}
}

#endif // __CPCAPI2_XMPPFILETRANSFERINFO_H__
