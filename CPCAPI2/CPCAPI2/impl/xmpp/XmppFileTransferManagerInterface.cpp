#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)

#include "cpcapi2defs.h"

#include <algorithm>
#include <sstream>

#include "XmppFileTransferManagerInterface.h"
#include "XmppFileTransferInfo.h"
#include "XmppFileTransferManagerImpl.h"
#include "XmppAccountInterface.h"
#include "phone/PhoneInterface.h"
#include "filetransfer/SipFileTransferFileMap.h"
#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_FILETRANSFER

using namespace CPCAPI2::XmppFileTransfer;
using CPCAPI2::XmppAccount::XmppAccountInterface;
using CPCAPI2::XmppAccount::XmppAccountManager;
using CPCAPI2::XmppAccount::XmppAccountHandle;
using CPCAPI2::XmppAccount::XmppAccountImpl;
using CPCAPI2::SipFileTransfer::FileMap;

XmppFileTransferManagerInterface::XmppFileTransferManagerInterface( CPCAPI2::Phone *phone )
   : mAccountIf(NULL)
   , mPhone(dynamic_cast<PhoneInterface*>( phone ))
{
   mAccountIf = dynamic_cast< XmppAccountInterface* >( XmppAccountManager::getInterface( phone ));
}

XmppFileTransferManagerInterface::~XmppFileTransferManagerInterface()
{
}

void XmppFileTransferManagerInterface::addSdkObserver(XmppFileTransferHandler* observer)
{
   mSdkObservers.push_back(observer);
}

void XmppFileTransferManagerInterface::removeSdkObserver(XmppFileTransferHandler* observer)
{
   mSdkObservers.erase(std::remove(mSdkObservers.begin(), mSdkObservers.end(), observer), mSdkObservers.end());
}

CPCAPI2::PhoneInterface* XmppFileTransferManagerInterface::phoneInterface()
{
   return mPhone;
}

void XmppFileTransferManagerInterface::Release()
{
   delete this; // suicide
}

int XmppFileTransferManagerInterface::setHandler(
   XmppAccountHandle account,
   XmppFileTransferHandler* handler )
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppFileTransferManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppFileTransferManagerInterface::setHandlerImpl( XmppAccountHandle account, XmppFileTransferHandler* handler )
{
   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(account);

   if (pImpl == NULL)
   {
      XmppAccountImpl* acct = mAccountIf->getImpl(account).get();

      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for XmppFileTransferManager::setHandler");
         return;
      }

      pImpl = std::make_shared<XmppFileTransferManagerImpl>(*acct, *this);
      mImplMap[account] = pImpl;

      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppFileTransferHandler*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         pImpl->addSdkObserver(*it);
      }
   }

   pImpl->setHandler( handler );
}

XmppFileTransferItemHandle XmppFileTransferManagerInterface::createFileTransferItem( XmppAccountHandle account )
{
   XmppFileTransferItemHandle itemHandle = XmppFileTransferManagerImpl::sNextFileTransferItemHandle++;

   // run a Dum command to create the file transfer structure in our collection
   mAccountIf->post( resip::resip_bind(&XmppFileTransferManagerInterface::createFileTransferItemImpl, this, account, itemHandle ));
   return itemHandle;
}

void XmppFileTransferManagerInterface::createFileTransferItemImpl( XmppAccountHandle account, XmppFileTransferItemHandle itemHandle )
{
   XmppAccountImpl* acct = mAccountIf->getImpl( account ).get();
   if( !acct )
   {
      cpc::string msg = cpc::string("XmppFileTransferManagerInterface::createFileTransferItem called with invalid account handle: " + cpc::to_string(account)) +
                         cpc::string(" XmppFileTransferItemHandle invalid: " + cpc::to_string(itemHandle));
      mAccountIf->fireError(msg);
      return;
   }

   if( !acct->isConnected() )
   {
      cpc::string msg = cpc::string("XmppFileTransferManagerInterface::createFileTransferItem called before account is connected: " + cpc::to_string(account)) +
                         cpc::string(", XmppFileTransferItemHandle invalid: " + cpc::to_string(itemHandle));
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppFileTransferManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      mAccountIf->fireError("XmppFileTransferManager::createFileTransferItem before XmppFileTransferManager::setHandler");
      return;
   }

   // By default it is outgoing
   XmppFileTransferItemInfo* pInfo = new XmppFileTransferItemInfo(itemHandle);
   impl->setFileTransferItemInfo( itemHandle, pInfo );
}

// create a new, outbound file transfer
XmppFileTransferHandle XmppFileTransferManagerInterface::createFileTransfer( XmppAccountHandle account )
{
   // Create a new XmppFileTransferInfo and store it in our collection. Return the handle
   XmppFileTransferHandle sessionHandle = XmppFileTransferManagerImpl::sNextFileTransferHandle++;

   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::createFileTransferImpl, this, account, sessionHandle ));
   return sessionHandle;
}

void XmppFileTransferManagerInterface::createFileTransferImpl( XmppAccountHandle account, XmppFileTransferHandle sessionHandle )
{
   XmppAccountImpl* acct = mAccountIf->getImpl( account ).get();
   if( !acct )
   {
      cpc::string msg = cpc::string("XmppFileTransferManagerInterface::createFileTransfer called with invalid account handle: " + cpc::to_string(account)) +
                         cpc::string(" XmppFileTransferHandle invalid: " + cpc::to_string(sessionHandle));
      mAccountIf->fireError(msg);
      return;
   }

   if( !acct->isConnected() )
   {
      cpc::string msg = cpc::string("XmppFileTransferManagerInterface::createFileTransfer called before account is connected: " + cpc::to_string(account)) +
                         cpc::string(", XmppFileTransferHandle invalid: " + cpc::to_string(sessionHandle));
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppFileTransferManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      mAccountIf->fireError("XmppFileTransferManager::createFileTransfer before XmppFileTransferManager::setHandler");
      return;
   }

   XmppFileTransferInfo * pInfo = new XmppFileTransferInfo( sessionHandle, account );
   impl->setFileTransferInfo( sessionHandle, pInfo );
}

int XmppFileTransferManagerInterface::addParticipant( XmppFileTransferHandle fileTransfer, const cpc::string& targetAddress )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::addParticipantImpl, this, fileTransfer, targetAddress ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::addParticipantImpl( XmppFileTransferHandle fileTransfer, const cpc::string& targetAddress )
{
   XmppFileTransferInfo * pInfo = getFileTransferInfo( fileTransfer );
   if ( pInfo == NULL )
   {
      cpc::string msg = cpc::string("XmppFileTransferManager::addParticipant called with invalid fileTransfer handle: ") + cpc::to_string(fileTransfer);
      mAccountIf->fireError(msg);
      return;
   }

   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if ( pImpl == NULL )
   {
      cpc::string msg = cpc::string("XmppFileTransferManager::addParticipant called with invalid fileTransfer handle: ") + cpc::to_string(fileTransfer);
      mAccountIf->fireError(msg);
      return;
   }

   gloox::JID jid;

   if (!jid.setJID(targetAddress.c_str()))
   {
      cpc::string msg = "Failed to parse participant URI '" + targetAddress + "'";
      pImpl->fireError( fileTransfer, msg );
      return;
   }

#if 0 // bliu: disable this check for broadcast
   if (jid.resource().empty())
   {
      cpc::string msg = "Must specify a resource when sending a file '" + targetAddress + "'";
      pImpl->fireError( fileTransfer, msg );
      return;
   }
#endif

   pInfo->remoteAddress = jid;
}

int XmppFileTransferManagerInterface::start( XmppFileTransferHandle fileTransfer )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::startImpl, this, fileTransfer ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::startImpl( XmppFileTransferHandle fileTransfer )
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if ( pImpl == NULL ) return;

   if( !pInfo->remoteAddress )
   {
      pImpl->fireError( fileTransfer, "Cannot start file transfer. Invalid remote address" );
      return;
   }

   if( pInfo->transferItems.empty() )
   {
      pImpl->fireError( fileTransfer, "Cannot start file transfer. No files have been added" );
      return;
   }

   for( std::list<XmppFileTransferItemHandle>::iterator iter = pInfo->transferItems.begin() ; iter != pInfo->transferItems.end() ; ++iter )
   {
      XmppFileTransferItemInfo* pItem = pImpl->getFileTransferItemInfo(*iter);
      if( pItem == NULL )
         continue;

      if (!phoneInterface()->hasFilePermission(Permission_ReadFiles, cpc::string(pItem->fileFullPath.c_str())))
      {
         phoneInterface()->requestPermission(0, Permission_ReadFiles);
         mOutgoingPendingPermission.insert(fileTransfer);
         InfoLog(<< "Cannot read file due to missing permission");
         return;
      }
   }

   pImpl->start(fileTransfer);
}

int XmppFileTransferManagerInterface::end( XmppFileTransferHandle fileTransfer )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::endImpl, this, fileTransfer ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::endImpl( XmppFileTransferHandle fileTransfer )
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if( pImpl == NULL ) return;

   pImpl->end( fileTransfer );

   mIncomingPendingPermission.erase( fileTransfer );
   mOutgoingPendingPermission.erase( fileTransfer );
}

// This method serves two purposes:
// 1) in the case of outbound file item, adds the items to the file transfer.
// 2) in the case of inbound file item, updates the file transfer item data.
int XmppFileTransferManagerInterface::configureFileTransferItems( XmppFileTransferHandle fileTransfer, const XmppFileTransferItems& fileItems )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::configureFileTransferItemsImpl, this, fileTransfer, fileItems ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::configureFileTransferItemsImpl( XmppFileTransferHandle fileTransfer, const XmppFileTransferItems& fileItems )
{
   // Update the info we have with the read-write members from the structure
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   // Fetch the account and therefore the XmppFileTransferManagerImpl
   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if( pImpl == NULL ) return;

   // Loop over the items and update the known info
   for( XmppFileTransferItems::const_iterator iter = fileItems.begin() ; iter != fileItems.end() ; ++iter )
   {
      // Item should exist (create... should be called first)
      XmppFileTransferItemInfo* pItem = pImpl->getFileTransferItemInfo( iter->handle );
      if( pItem == NULL )
         continue;

      // Set the stream type
      pItem->streamTypes = iter->streamTypes;

      if( !iter->localfileName.empty() )
         pItem->localfileName = iter->localfileName;

      if( !iter->localfilePath.empty() )
         pItem->localfilePath = iter->localfilePath;

      if (!iter->transferType.empty())
         pItem->transferType = iter->transferType;

      pItem->fileFullPath = iter->localfilePath;

      std::string pathDelimeter = "/";
#ifdef _WIN32
      pathDelimeter = "\\";
#endif

      if (!iter->localfileName.empty())
      {
         pItem->fileFullPath += pathDelimeter;
         pItem->fileFullPath += iter->localfileName;
      }

#ifdef ANDROID
      if (std::string::npos != pItem->fileFullPath.find(":"))
      {
         // for content resolver mode, we don't want to append the filename to the path, since
         // this would make the path invalid -- so we ensure the full file path is exactly equal
         // to the local file path, which should be the content URI
         pItem->fileFullPath = iter->localfilePath;
      }
#endif

      if (pItem->isIncoming) // Inbound items: update the accepted state
      {
         // check for items not in the original request
         if (std::find(pInfo->transferItems.begin(), pInfo->transferItems.end(), pItem->handle) == pInfo->transferItems.end()) continue;

         pItem->acceptedState = iter->acceptedState;
      }
      else // Outbound item handling
      {
         // Set the remote filename if not already set
         if( pItem->remotefileName.empty() )
         {
            pItem->remotefileName = iter->remotefileName.empty() ? iter->localfileName : iter->remotefileName;
         }

         // Set the file size
         pItem->fileSizeBytes = iter->fileSizeBytes;

         // Make sure the specified file is good
         if (FileMap::FileExists(iter->localfileName, iter->localfilePath) && FileMap::IsRegularFile(iter->localfileName, iter->localfilePath))
         {
            pItem->fileSizeBytes = FileMap::GetFileSize(iter->localfileName, iter->localfilePath);
         }

         // Add the item to the containing transfer if previous checks passed
         pInfo->transferItems.push_back( iter->handle );
      }
   }
}

// method called to accept an incoming file transfer
int XmppFileTransferManagerInterface::accept( XmppFileTransferHandle fileTransfer )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::acceptImpl, this, fileTransfer ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::acceptImpl( XmppFileTransferHandle fileTransfer )
{
   // Accept an incoming XMPP file transfer
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   // Check that the account is valid
   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if ( pImpl == NULL ) return;

   std::list<XmppFileTransferItemHandle>::iterator iter;
   for( iter = pInfo->transferItems.begin() ; iter != pInfo->transferItems.end() ; ++iter )
   {
      XmppFileTransferItemInfo* pItem = pImpl->getFileTransferItemInfo(*iter);
      if( pItem == NULL )
         continue;

      // Don't accept file transfers which are not incoming
      if( !pItem->isIncoming )
         continue;

      if (!phoneInterface()->hasFilePermission(Permission_WriteFiles, cpc::string(pItem->fileFullPath.c_str())))
      {
        phoneInterface()->requestPermission(0, Permission_WriteFiles);
        mIncomingPendingPermission.insert(fileTransfer);
        InfoLog(<< "Cannot write file due to missing permission");
        return;
      }

      // Don't accept file transfers which are not marked as such
      if( pItem->acceptedState != ftitem_accepted )
         continue;
   }

   pImpl->accept(fileTransfer);
}

int XmppFileTransferManagerInterface::reject( XmppFileTransferHandle fileTransfer, const cpc::string& reason)
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::rejectImpl, this, fileTransfer, reason));
   return kSuccess;
}

void XmppFileTransferManagerInterface::rejectImpl( XmppFileTransferHandle fileTransfer, const cpc::string& reason)
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if ( pImpl == NULL ) return;

   pImpl->reject(fileTransfer, reason);
}

int XmppFileTransferManagerInterface::cancelItem( XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem )
{
   mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::cancelItemImpl, this, fileTransfer, fileTransferItem ));
   return kSuccess;
}

void XmppFileTransferManagerInterface::cancelItemImpl( XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem )
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if( pInfo == NULL ) return;

   std::shared_ptr<XmppFileTransferManagerImpl> pImpl = getImpl(pInfo->accountHandle);
   if ( pImpl == NULL ) return;

   std::list<XmppFileTransferItemHandle>::iterator itItem = std::find(pInfo->transferItems.begin(), pInfo->transferItems.end(), fileTransferItem);
   if (itItem == pInfo->transferItems.end()) return;

   XmppFileTransferItemInfo* pItem = pImpl->getFileTransferItemInfo(*itItem);
   if( pItem == NULL ) return;

   pItem->acceptedState = ftitem_rejected;

   pImpl->cancelItem(fileTransfer, fileTransferItem);
}

XmppFileTransferInfo* XmppFileTransferManagerInterface::getFileTransferInfo( XmppFileTransferHandle h )
{
   for (ImplMap::iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      // Just return the first one we find.
      XmppFileTransferInfo * pInfo = it->second->getFileTransferInfo( h );
      if( pInfo != NULL ) return pInfo;
   }
   return NULL;
}

void XmppFileTransferManagerInterface::onPermissionGranted(int requestCode, CPCAPI2::Permission permission)
{
  if (Permission_ReadFiles == permission)
  {
    InfoLog(<< "Granted read file permission. Sending pending outgoing files");
    for (PendingTransfersList::iterator it = mOutgoingPendingPermission.begin(); it != mOutgoingPendingPermission.end(); ++it)
    {
      mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::startImpl, this, *it ));
    }
    mOutgoingPendingPermission.clear();
  }
  else if (Permission_WriteFiles == permission)
  {
    InfoLog(<< "Granted write file permission. Accepting pending incoming files");
    for (PendingTransfersList::iterator it = mIncomingPendingPermission.begin(); it != mIncomingPendingPermission.end(); ++it)
    {
      mAccountIf->post( resip::resip_bind( &XmppFileTransferManagerInterface::acceptImpl, this, *it ));
    }
    mIncomingPendingPermission.clear();
  }
}

#endif
