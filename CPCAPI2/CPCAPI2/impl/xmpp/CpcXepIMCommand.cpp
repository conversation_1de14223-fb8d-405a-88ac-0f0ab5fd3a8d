//
//  CpcXepIMCommand.cpp
//  BriaVoip
//
//  Created by <PERSON> on November 16, 2016.
//  Copyright 2016 CounterPath Corporation. All rights reserved.
//

#include "brand_branded.h"

#include "CpcXep.h"
#include "CpcXepIMCommand.h"
#include "base64.h"

#include <stdlib.h>

const std::string CpcXepIMCommand::XMLNS_IM_COMMAND = "http://webex.com/connect/imcmd";

CpcXepIMCommand::CpcXepIMCommand(int type, const std::string& payload)
   : StanzaExtension(EXepIMCommand)
   , mType(type)
   , mPayload(payload)
   , mValid(true)
{
}

CpcXepIMCommand::CpcXepIMCommand(const gloox::Tag* tag)
   : StanzaExtension(EXepIMCommand)
   , mType(0)
   , mValid(false)
{
   if (tag == NULL || tag->name() != "x" || tag->xmlns() != XMLNS_IM_COMMAND) return;

   if (tag->hasAttribute("type"))
   {
      mType = atoi(tag->findAttribute("type").c_str());
   }

   mPayload = gloox::Base64::decode64(tag->cdata());

   mValid = true;
}

CpcXepIMCommand::~CpcXepIMCommand()
{
}

const int CpcXepIMCommand::type() const
{
   return mType;
}

const std::string& CpcXepIMCommand::payload() const
{
   return mPayload;
}

const std::string& CpcXepIMCommand::filterString() const
{
   static const std::string& filter = "/message/x[@xmlns='" + XMLNS_IM_COMMAND + "']";
   return filter;
}

gloox::StanzaExtension* CpcXepIMCommand::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepIMCommand(tag);
}

gloox::Tag* CpcXepIMCommand::tag() const
{
   gloox::Tag* tag = new gloox::Tag("x", "xmlns", XMLNS_IM_COMMAND);
   tag->addAttribute("type", mType);
   tag->addCData(gloox::Base64::encode64(mPayload));
   return tag;
}

gloox::StanzaExtension* CpcXepIMCommand::clone() const
{
   return new CpcXepIMCommand(*this);
}
