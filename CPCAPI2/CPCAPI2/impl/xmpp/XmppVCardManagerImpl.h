#pragma once

#ifndef __CPCAPI2_XMPP_VCARD_MANAGER_IMPL_H__
#define __CPCAPI2_XMPP_VCARD_MANAGER_IMPL_H__

#include <list>
#include <atomic>

#include "cpcapi2defs.h"

#include "XmppAccountImpl.h"
#include "XmppVCardManagerInterface.h"
#include "XmppVCardHandlerInternal.h"
#include "XmppVCardSyncHandler.h"

#include <vcardmanager.h>
#include <vcardhandler.h>
#include <presencehandler.h>

namespace CPCAPI2
{

namespace XmppVCard
{

class XmppVCardManagerImpl
   : public XmppAccount::XmppAccountObserver
   , public gloox::VCardHandler
   , public gloox::PresenceHandler
   , public std::enable_shared_from_this<XmppVCardManagerImpl>
{

   friend class XmppVCardManagerInterface;

public:

   XmppVCardManagerImpl(XmppAccount::XmppAccountImpl& account, XmppVCardManagerInterface& intf);
   virtual ~XmppVCardManagerImpl();

   void setHandler(XmppVCardHandler* handler);

   void fetchVCard(XmppVCardHandle handle, const cpc::string& jid);
   void storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard);
   void cancelVCardOperations(XmppVCardHandle handle);

   void fireVCardFetched(XmppVCardHandle handle, const VCardFetchedEvent& evt);
   void fireVCardOperationResult(XmppVCardHandle handle, const VCardOperationResultEvent& evt);
   void fireError(XmppVCardHandle handle, const cpc::string& errorText);

   void addSdkObserver(XmppVCardHandlerInternal* sdkObserver);
   void removeSdkObserver(XmppVCardHandlerInternal* sdkObserver);

public:

   static std::atomic<XmppVCardHandle> sNextVCardHandle;

   void setHandle(XmppVCardHandle h);
   XmppVCardHandle getHandle() const { return mHandle; }

private:

   // XmppAccount::XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;

   // gloox::VCardHandler
   virtual void handleVCard(const gloox::JID& jid, const gloox::VCard* vcard) OVERRIDE;
   virtual void handleVCardResult(gloox::VCardHandler::VCardContext context, const gloox::JID& jid, gloox::StanzaError se = gloox::StanzaErrorUndefined, gloox::StanzaErrorType setype = gloox::StanzaErrorTypeUndefined, std::string errorStr = "", int errorCode = 0) OVERRIDE;

   // gloox::PresenceHandler
   virtual void handlePresence(const gloox::Presence& presence) OVERRIDE;

private:

   void cleanup();

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, XmppVCardHandle handle, const TEvt& evt)
   {
      for (std::list<XmppVCardHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, handle, evt);
         if (dynamic_cast<XmppVCardSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }

      if (mAppHandler != reinterpret_cast<XmppVCardHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, handle, evt);
         mAccount.postCallback(cb);
      }
   }

   // specialization for XmppVCardHandlerInternal which cannot be handled by mAppHandler
   void fireEvent(const char* funcName, int (XmppVCardHandlerInternal::*func)(XmppVCardHandle, const XmppVCardCreatedResultEvent&), XmppVCardCreatedResultEvent& args)
   {
      for (std::list<XmppVCardHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, args);
         if (dynamic_cast<XmppVCardSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }
   }

private:

   XmppAccount::XmppAccountImpl& mAccount;
   XmppVCardManagerInterface& mInterface;

   XmppVCardHandler* mAppHandler;
   std::list<XmppVCardHandlerInternal*> mSdkObservers;

   gloox::VCardManager* mGlooxVCardManager;

   XmppVCardHandle mHandle;

   typedef std::map<std::string, std::string> PhotoHashMap;
   PhotoHashMap mPhotoHashMap; // jid to photo hash

};

}

}

#endif // __CPCAPI2_XMPP_VCARD_MANAGER_IMPL_H__
