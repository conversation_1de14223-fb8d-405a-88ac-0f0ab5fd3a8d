#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)

#include "XmppVCardManagerImpl.h"
#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "cpcapi2utils.h"
#include "XmppAccountImpl.h"

#include <error.h>
#include <disco.h>
#include <vcardupdate.h>
#include <sha.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD

#define FIRE_ERROR(handle, msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   fireError(handle, ss.str().c_str());\
}

namespace CPCAPI2
{

namespace XmppVCard
{

#ifdef _WIN32
std::atomic<XmppVCardHandle> XmppVCardManagerImpl::sNextVCardHandle = 1;
#else
std::atomic<XmppVCardHandle> XmppVCardManagerImpl::sNextVCardHandle = ATOMIC_VAR_INIT(1);
#endif

static void _set_photo(const gloox::VCard::Photo& src, XmppVCardDetail::Photo& dst)
{
   if (!src.binval.empty() && !src.type.empty())
   {
      dst.type = src.type.c_str();

      gloox::SHA sha;
      sha.feed(src.binval);
      const std::string hash = sha.hex();

      if (hash == dst.hash.c_str()) return;

      dst.hash = hash.c_str();

      dst.binval.clear();

      for (std::string::const_iterator it = src.binval.begin(); it != src.binval.end(); ++it)
      {
         dst.binval.push_back(*it);
      }
   }
   else
   {
      dst.type = "";
      dst.hash = "";
      dst.binval.clear();
      dst.extval = src.extval.c_str();
   }
}

XmppVCardManagerImpl::XmppVCardManagerImpl(XmppAccount::XmppAccountImpl& account, XmppVCardManagerInterface& intf)
   : mAccount(account)
   , mInterface(intf)
   , mAppHandler(NULL)
   , mGlooxVCardManager(NULL)
   , mHandle(0)
{
   mAccount.registerAccountObserver(this);
}

XmppVCardManagerImpl::~XmppVCardManagerImpl()
{
   cleanup();

   mAccount.unregisterAccountObserver(this);
}

void XmppVCardManagerImpl::cleanup()
{
   if (mAccount.isConnected()) mAccount.getGlooxClient()->removePresenceHandler(this);

   delete mGlooxVCardManager;
   mGlooxVCardManager = NULL;

   mPhotoHashMap.clear();

   //mHandle = 0; // bliu: TODO need to reconsider the design of handle assignment
}

void XmppVCardManagerImpl::setHandler(XmppVCardHandler* handler)
{
   // With auto-test cases or json proxies, the handler may always be the same, e.g. 0xDEADBEFF
   if (mAppHandler != NULL && handler != NULL && handler != mAppHandler)
   {
      FIRE_ERROR(mHandle, "XmppVCardManager::setHandler() has been called multiple time with the same account handle: " << mAccount.getHandle());
   }

   mAppHandler = handler;
}

void XmppVCardManagerImpl::setHandle(XmppVCardHandle handle)
{
   if ((mHandle != 0) && (mHandle != handle))
   {
      FIRE_ERROR(mHandle, "XmppVCard can't apply multiple vcard managers to the same xmpp account");
      return;
   }

   mHandle = handle;
}

void XmppVCardManagerImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_VCARD_TEMP);
   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_X_VCARD_UPDATE);

   if (account.getSettings().enableXmppStanza)
      account.getGlooxClient()->registerStanzaExtension(new gloox::VCardUpdate());

   if (account.getSettings().enableXmppPresence)
      account.getGlooxClient()->registerPresenceHandler(this);

   mGlooxVCardManager = new gloox::VCardManager(account.getGlooxClient());
}

void XmppVCardManagerImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
   fetchVCard(mHandle, account.getGlooxClient()->jid().bare().c_str());
}

void XmppVCardManagerImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppVCardManagerImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppVCardManagerImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppVCardManagerImpl::handleVCard(const gloox::JID& jid, const gloox::VCard* vcard)
{
   assert(vcard != NULL);

   StackLog(<< "XmppVCardManagerImpl::handleVCard(): " << this << " handle: " << mHandle << " account jid: " << jid.bare().c_str() << " vcard jid: " << vcard->jabberid().c_str());

   VCardFetchedEvent evt;
   evt.account = mAccount.getHandle();
   evt.handle = mHandle;
   evt.jid = jid.bare().c_str();

   XmppVCardDetail& _vcard = evt.detail;

   if (vcard->isEmpty())
   {
      _vcard.jid = evt.jid;
      fireVCardFetched(mHandle, evt);
      return;
   }

   _vcard.formattedname = vcard->formattedname().c_str();
   _vcard.name.family = vcard->name().family.c_str();
   _vcard.name.given = vcard->name().given.c_str();
   _vcard.name.middle = vcard->name().middle.c_str();
   _vcard.name.prefix = vcard->name().prefix.c_str();
   _vcard.name.suffix = vcard->name().suffix.c_str();
   _vcard.nickname = vcard->nickname().c_str();
   _vcard.url = vcard->url().c_str();
   _vcard.birthday = vcard->bday().c_str();
   _vcard.jid = vcard->jabberid().c_str();
   _vcard.title = vcard->title().c_str();
   _vcard.role = vcard->role().c_str();
   _vcard.note = vcard->note().c_str();
   _vcard.desc = vcard->desc().c_str();
   _vcard.mailer = vcard->mailer().c_str();
   _vcard.revision = vcard->rev().c_str();
   _vcard.uid = vcard->uid().c_str();
   _vcard.timezone = vcard->tz().c_str();
   _vcard.product = vcard->prodid().c_str();
   _vcard.sortstring = vcard->sortstring().c_str();
   _vcard.phonetic = vcard->phonetic().c_str();
   _vcard.cpcollab = vcard->cpcollab().c_str();
   _vcard.cpsoftphone = vcard->cpsoftphone().c_str();
   _vcard.cpsoftphone_pref = vcard->cpsoftphone_pref();
   _set_photo(vcard->photo(), _vcard.photo);
   _set_photo(vcard->logo(), _vcard.logo);

   for (gloox::VCard::EmailList::const_iterator it = vcard->emailAddresses().begin(); it != vcard->emailAddresses().end(); ++it)
   {
      XmppVCardDetail::Email _item;
      _item.userid = it->userid.c_str();
      _item.home = it->home;
      _item.work = it->work;
      _item.internet = it->internet;
      _item.pref = it->pref;
      _item.x400 = it->x400;

      _vcard.emailList.push_back(_item);
   }

   for (gloox::VCard::AddressList::const_iterator it = vcard->addresses().begin(); it != vcard->addresses().end(); ++it)
   {
      XmppVCardDetail::Address _item;
      _item.pobox = it->pobox.c_str();
      _item.extadd = it->extadd.c_str();
      _item.street = it->street.c_str();
      _item.locality = it->locality.c_str();
      _item.region = it->region.c_str();
      _item.pcode = it->pcode.c_str();
      _item.ctry = it->ctry.c_str();
      _item.home = it->home;
      _item.work = it->work;
      _item.postal = it->postal;
      _item.parcel = it->parcel;
      _item.pref = it->pref;
      _item.dom = it->dom;
      _item.intl = it->intl;

      _vcard.addressList.push_back(_item);
   }

   for (gloox::VCard::LabelList::const_iterator it = vcard->labels().begin(); it != vcard->labels().end(); ++it)
   {
      XmppVCardDetail::Label _item;

      for (gloox::StringList::const_iterator itLine = it->lines.begin(); itLine != it->lines.end(); ++itLine)
      {
         _item.lines.push_back(itLine->c_str());
      }

      _item.home = it->home;
      _item.work = it->work;
      _item.postal = it->postal;
      _item.parcel = it->parcel;
      _item.pref = it->pref;
      _item.dom = it->dom;
      _item.intl = it->intl;

      _vcard.labelList.push_back(_item);
   }

   for (gloox::VCard::TelephoneList::const_iterator it = vcard->telephone().begin(); it != vcard->telephone().end(); ++it)
   {
      XmppVCardDetail::Telephone _item;

      _item.number = it->number.c_str();
      _item.home = it->home;
      _item.work = it->work;
      _item.voice = it->voice;
      _item.fax = it->fax;
      _item.pager = it->pager;
      _item.msg = it->msg;
      _item.cell = it->cell;
      _item.video = it->video;
      _item.bbs = it->bbs;
      _item.modem = it->modem;
      _item.isdn = it->isdn;
      _item.pcs = it->pcs;
      _item.pref = it->pref;

      _vcard.telephoneList.push_back(_item);
   }

   _vcard.geo.latitude = vcard->geo().latitude.c_str();
   _vcard.geo.longitude = vcard->geo().longitude.c_str();

   for (gloox::StringList::const_iterator it = vcard->org().units.begin(); it != vcard->org().units.end(); ++it)
   {
      _vcard.organization.units.push_back(it->c_str());
   }

   _vcard.organization.name = vcard->org().name.c_str();
   _vcard.classification = (XmppVCardDetail::VCardClassification)vcard->classification();

   fireVCardFetched(mHandle, evt);
}

void XmppVCardManagerImpl::handleVCardResult(
   gloox::VCardHandler::VCardContext context,
   const gloox::JID& jid,
   gloox::StanzaError se,
   gloox::StanzaErrorType setype,
   std::string errorStr,
   int errorCode)
{
   VCardOperationResultEvent evt;
   evt.account = mAccount.getHandle();
   evt.handle = mHandle;
   evt.jid = jid.bare().c_str();
   evt.type = context;
   evt.result = se == gloox::StanzaErrorUndefined ? 0 : se + 1; // bliu: as commented in VCardHandler::handleVCardResult() that StanzaErrorUndefined means no error occured
   evt.success = se == gloox::StanzaErrorUndefined ? true : false;
   evt.resultCode = se;
   evt.resultStr = gloox::Error::stanzaErrorString(se).c_str();
   evt.xmppErrorCode = errorCode;
   evt.xmppErrorStr = errorStr.c_str();

   StackLog(<< "XmppVCardManagerImpl::handleVCardResult(): " << this << " vcard result: " << get_debug_string(evt) << " errorType: " << setype << " errorTypeStr: " << gloox::Error::stanzaErrorTypeString(setype).c_str());

   fireVCardOperationResult(mHandle, evt);
}

void XmppVCardManagerImpl::handlePresence(const gloox::Presence& presence)
{
   // bliu: TODO check offline presence to remove item from mPhotoHashMap?

   if (const gloox::VCardUpdate* vcardUpdate = presence.findExtension<gloox::VCardUpdate>(gloox::ExtVCardUpdate))
   {
      PhotoHashMap::iterator it = mPhotoHashMap.find(presence.from().bare());

      if (it != mPhotoHashMap.end() && it->second == vcardUpdate->hash())
      {
         DebugLog(<< "XmppVCardManagerImpl::handlePresence(): " << this << " duplicate VcardUpdate hash (" << vcardUpdate->hash() << ") for jid: " << presence.from().full());
         return;
      }

      // hash changed, retrieve vCard with updated photo element
      mPhotoHashMap[presence.from().bare()] = vcardUpdate->hash();

      StackLog(<< "XmppVCardManagerImpl::handlePresence(): " << this << " auto fetching for jid: " << presence.from().full() << " vcard handle: " << mHandle);
      fetchVCard(mHandle, presence.from().bare().c_str());
   }
}

void XmppVCardManagerImpl::addSdkObserver(XmppVCardHandlerInternal* sdkObserver)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), sdkObserver) != mSdkObservers.end())
   {
      StackLog(<< "XmppVCardManagerImpl::addSdkObserver(): " << this << " observer: " << sdkObserver << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.push_back(sdkObserver);
   StackLog(<< "XmppVCardManagerImpl::addSdkObserver(): " << this << " observer: " << sdkObserver << " added to handler list of size: " << mSdkObservers.size());
}

void XmppVCardManagerImpl::removeSdkObserver(XmppVCardHandlerInternal* handler)
{
   std::list<XmppVCardHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), handler);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppVCardManagerImpl::removeSdkObserver(): XmppVCardHandlerInternal handler: " << handler << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppVCardManagerImpl::fetchVCard(XmppVCardHandle handle, const cpc::string& jid)
{
   DebugLog(<< "XmppVCardManagerImpl::fetchVCard(): " << this << " handle: " << handle << " vcard jid: " << jid);
   if (mGlooxVCardManager != NULL)
   {
      mGlooxVCardManager->fetchVCard(gloox::JID(jid.c_str()), this);
   }
   else
   {
      handleVCardResult(gloox::VCardHandler::FetchVCard, gloox::JID(jid.c_str()));
   }
}

void XmppVCardManagerImpl::storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard)
{
   StackLog(<< "XmppVCardManagerImpl::storeVCard(): " << this << " handle: " << handle << " vcard jid: " << vcard.jid);
   if (mGlooxVCardManager == NULL)
   {
      handleVCardResult(gloox::VCardHandler::StoreVCard, gloox::JID(vcard.jid.c_str()));
      return;
   }

   gloox::VCard* _vcard = new gloox::VCard;
   _vcard->setFormattedname(vcard.formattedname.c_str());
   _vcard->setName(vcard.name.family.c_str(), vcard.name.given.c_str(), vcard.name.middle.c_str(), vcard.name.prefix.c_str(), vcard.name.suffix.c_str());
   _vcard->setNickname(vcard.nickname.c_str());
   _vcard->setUrl(vcard.url.c_str());
   _vcard->setBday(vcard.birthday.c_str());
   _vcard->setJabberid(vcard.jid.c_str());
   _vcard->setTitle(vcard.title.c_str());
   _vcard->setRole(vcard.role.c_str());
   _vcard->setNote(vcard.note.c_str());
   _vcard->setDesc(vcard.desc.c_str());
   _vcard->setMailer(vcard.mailer.c_str());
   _vcard->setRev(vcard.revision.c_str());
   _vcard->setUid(vcard.uid.c_str());
   _vcard->setTz(vcard.timezone.c_str());
   _vcard->setProdid(vcard.product.c_str());
   _vcard->setSortstring(vcard.sortstring.c_str());
   _vcard->setPhonetic(vcard.phonetic.c_str());
   _vcard->setCPCollab(vcard.cpcollab.c_str());
   _vcard->setCPSoftphone(vcard.cpsoftphone.c_str());
   _vcard->setCPSoftphonePref(vcard.cpsoftphone_pref);

   if ((vcard.photo.binval.size() > 0) && !vcard.photo.type.empty())
   {
      std::string photoBinary (vcard.photo.binval.data(), vcard.photo.binval.size());
      _vcard->setPhoto(vcard.photo.type.c_str(), photoBinary);
   }
   else
   {
      _vcard->setPhotoUri(vcard.photo.extval.c_str());
   }
   if ((vcard.logo.binval.size() > 0) && !vcard.logo.type.empty())
   {
      std::string logoBinary (vcard.logo.binval.data(), vcard.logo.binval.size());
      _vcard->setLogo(vcard.logo.type.c_str(), logoBinary);
   }
   else
   {
      _vcard->setLogoUri(vcard.logo.extval.c_str());
   }

   for (unsigned int i = 0; i < vcard.emailList.size(); ++i)
   {
      int type = 0;

      if (vcard.emailList[i].home) type |= gloox::VCard::AddrTypeHome;
      if (vcard.emailList[i].work) type |= gloox::VCard::AddrTypeWork;
      if (vcard.emailList[i].pref) type |= gloox::VCard::AddrTypePref;
      if (vcard.emailList[i].x400) type |= gloox::VCard::AddrTypeX400;
      if (vcard.emailList[i].internet) type |= gloox::VCard::AddrTypeInet;

      _vcard->addEmail(vcard.emailList[i].userid.c_str(), type);
   }

   for (unsigned int i = 0; i < vcard.addressList.size(); ++i)
   {
      int type = 0;

      if (vcard.addressList[i].home)   type |= gloox::VCard::AddrTypeHome;
      if (vcard.addressList[i].work)   type |= gloox::VCard::AddrTypeWork;
      if (vcard.addressList[i].pref)   type |= gloox::VCard::AddrTypePref;
      if (vcard.addressList[i].parcel) type |= gloox::VCard::AddrTypeParcel;
      if (vcard.addressList[i].postal) type |= gloox::VCard::AddrTypePostal;
      if (vcard.addressList[i].dom)    type |= gloox::VCard::AddrTypeDom;
      if (vcard.addressList[i].intl)   type |= gloox::VCard::AddrTypeIntl;

      _vcard->addAddress(
         vcard.addressList[i].pobox.c_str(),
         vcard.addressList[i].extadd.c_str(),
         vcard.addressList[i].street.c_str(),
         vcard.addressList[i].locality.c_str(),
         vcard.addressList[i].region.c_str(),
         vcard.addressList[i].pcode.c_str(),
         vcard.addressList[i].ctry.c_str(),
         type
      );
   }

   for (unsigned int i = 0; i < vcard.labelList.size(); ++i)
   {
      int type = 0;

      if (vcard.labelList[i].home)     type |= gloox::VCard::AddrTypeHome;
      if (vcard.labelList[i].work)     type |= gloox::VCard::AddrTypeWork;
      if (vcard.labelList[i].pref)     type |= gloox::VCard::AddrTypePref;
      if (vcard.labelList[i].parcel)   type |= gloox::VCard::AddrTypeParcel;
      if (vcard.labelList[i].postal)   type |= gloox::VCard::AddrTypePostal;
      if (vcard.labelList[i].dom)      type |= gloox::VCard::AddrTypeDom;
      if (vcard.labelList[i].intl)     type |= gloox::VCard::AddrTypeIntl;

      gloox::StringList lines;
      cpc::vector<cpc::string>::const_iterator git = vcard.labelList[i].lines.begin();
      for(; git != vcard.labelList[i].lines.end(); ++git)
      {
         lines.push_back((*git).c_str());
      }

      _vcard->addLabel(lines, type);
   }

   for (unsigned int i = 0; i < vcard.telephoneList.size(); ++i)
   {
      int type = 0;

      if (vcard.telephoneList[i].home)    type |= gloox::VCard::AddrTypeHome;
      if (vcard.telephoneList[i].work)    type |= gloox::VCard::AddrTypeWork;
      if (vcard.telephoneList[i].pref)    type |= gloox::VCard::AddrTypePref;
      if (vcard.telephoneList[i].voice)   type |= gloox::VCard::AddrTypeVoice;
      if (vcard.telephoneList[i].fax)     type |= gloox::VCard::AddrTypeFax;
      if (vcard.telephoneList[i].pager)   type |= gloox::VCard::AddrTypePager;
      if (vcard.telephoneList[i].msg)     type |= gloox::VCard::AddrTypeMsg;
      if (vcard.telephoneList[i].cell)    type |= gloox::VCard::AddrTypeCell;
      if (vcard.telephoneList[i].video)   type |= gloox::VCard::AddrTypeVideo;
      if (vcard.telephoneList[i].bbs)     type |= gloox::VCard::AddrTypeBbs;
      if (vcard.telephoneList[i].modem)   type |= gloox::VCard::AddrTypeModem;
      if (vcard.telephoneList[i].isdn)    type |= gloox::VCard::AddrTypeIsdn;
      if (vcard.telephoneList[i].pcs)     type |= gloox::VCard::AddrTypePcs;

      _vcard->addTelephone(vcard.telephoneList[i].number.c_str(), type);
   }

   _vcard->setGeo(vcard.geo.latitude.c_str(), vcard.geo.longitude.c_str());
   gloox::StringList orgunits;
   cpc::vector<cpc::string>::const_iterator git = vcard.organization.units.begin();
   for(; git != vcard.organization.units.end(); ++git)
   {
      orgunits.push_back((*git).c_str());
   }

   _vcard->setOrganization(vcard.organization.name.c_str(), orgunits);
   _vcard->setClass((gloox::VCard::VCardClassification)vcard.classification);
   mGlooxVCardManager->storeVCard(_vcard, this);

   gloox::VCardUpdate* vcardUpdate = vcard.photo.hash.empty() ? new gloox::VCardUpdate() : new gloox::VCardUpdate(vcard.photo.hash.c_str());
   mAccount.getGlooxClient()->presence().addExtension(vcardUpdate);
   mAccount.getGlooxClient()->setPresence();
   mAccount.getGlooxClient()->presence().removeExtension(gloox::ExtVCardUpdate);
}

void XmppVCardManagerImpl::cancelVCardOperations(XmppVCardHandle handle)
{
   if (mGlooxVCardManager != NULL)
   {
      mGlooxVCardManager->cancelVCardOperations(this);
   }
}

void XmppVCardManagerImpl::fireError(XmppVCardHandle handle, const cpc::string& errorText)
{
   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppVCard: " + errorText);
      return;
   }

   ErrorEvent evt;
   evt.account = mAccount.getHandle();
   evt.handle = handle;
   evt.errorText = errorText;
   fireEvent(cpcFunc(XmppVCardHandler::onError), handle, evt);
}

void XmppVCardManagerImpl::fireVCardFetched(XmppVCardHandle handle, const VCardFetchedEvent& evt)
{
   fireEvent(cpcFunc(XmppVCardHandler::onVCardFetched), handle, evt);
}

void XmppVCardManagerImpl::fireVCardOperationResult(XmppVCardHandle handle, const VCardOperationResultEvent& evt)
{
   fireEvent(cpcFunc(XmppVCardHandler::onVCardOperationResult), handle, evt);
}

}
}
#endif
