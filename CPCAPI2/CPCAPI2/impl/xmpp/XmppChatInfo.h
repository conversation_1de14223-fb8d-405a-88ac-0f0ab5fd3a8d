#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_INFO_H__)
#define __CPCAPI2_XMPP_CHAT_INFO_H__

#include "cpcapi2defs.h"
#include "xmpp/XmppChatTypes.h"
#include "XmppChatMessageInfo.h"
#include "iscomposing/IsComposingInfo.h"

#include <rutil/DeadlineTimer.hxx>

#include <atomic>
#include <list>

namespace gloox
{
class MessageSession;
class MessageEventFilter;
class MessageEventHandler;
class ChatStateFilter;
class ChatStateHandler;
class Message;
class JID;
}

namespace CPCAPI2
{
namespace XmppAccount
{
typedef unsigned int XmppAccountHandle;
}

namespace XmppChat
{

class XmppMessageFilter;
class GlooxSessionEventHandler;

struct XmppChatInfo : public CPCAPI2::IsComposing::IsComposingInfo
{
   XmppChatInfo();
   ~XmppChatInfo();

   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle;
   XmppChatHandle handle;
   XmppChatMessageInfoMap messageInfoMap;
   std::vector<gloox::JID> targetAddresses;

   gloox::MessageSession* mGlooxMessageSession;
   gloox::MessageEventFilter* mGlooxMessageEventFilter;
   gloox::ChatStateFilter* mGlooxChatStateFilter;
   XmppMessageFilter* mMessageFilter;
   GlooxSessionEventHandler* mGlooxSessionEventHandler;

   class MessageMappings
   {
   public:
      // add() pushes out the old mappings
      void add(XmppChatMessageHandle message, const std::string& id)
      {
         while (mQueue.size() >= MaxMessageMappings) mQueue.pop_front();
         mQueue.push_back(std::make_pair(message, id));
      }

      std::string find(XmppChatMessageHandle message) const
      {
         for (auto& v : mQueue)
         {
            if (v.first == message) return v.second;
         }

         return std::string();
      }

      XmppChatMessageHandle find(const std::string& id) const
      {
         for (auto& v : mQueue)
         {
            if (v.second == id) return v.first;
         }

         return 0;
      }

   private:
      std::list<std::pair<XmppChatMessageHandle, std::string> > mQueue;
      const static int MaxMessageMappings = 100;
   };

   MessageMappings mIncomingMessages;
   MessageMappings mOutgoingMessages;

   std::unique_ptr<gloox::Message> mLastIncomingGlooxMessage;

   // bliu: keep track of local active state
   resip::DeadlineTimer<resip::MultiReactor>* mInactiveTimer;
   bool mIsActive;

   // keep track of local peer discovery requests
   std::map<gloox::JID, resip::DeadlineTimer<resip::MultiReactor>*> mPeerDiscoveryTimers;

   // remote capabilities
   bool mReplaceMessageSupported;

   bool hasMessageId(const XmppChatMessageHandle& message);
   XmppChatMessageInfo* getMessageInfo(const XmppChatMessageHandle& message) const;
   void addMessageInfo(const XmppChatMessageHandle& handle, XmppChatMessageInfo* messageInfo);
};

typedef std::map<XmppChatHandle, XmppChatInfo*> XmppChatInfoMap;

}
}

#endif // __CPCAPI2_XMPP_CHAT_INFO_H__
