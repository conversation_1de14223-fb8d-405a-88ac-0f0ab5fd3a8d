#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE == 1)

#include "XmppIMCommandManagerInterface.h"
#include "XmppChatManagerInterface.h"
#include "XmppMultiUserChatManagerInterface.h"
#include "XmppChatManagerImpl.h"
#include "XmppMultiUserChatManagerImpl.h"

namespace CPCAPI2
{
namespace XmppIMCommand
{

XmppIMCommandManagerInterface::XmppIMCommandManagerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mAccountIf(dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone)))
{
}

XmppIMCommandManagerInterface::~XmppIMCommandManagerInterface()
{
}

void XmppIMCommandManagerInterface::Release()
{
   delete this;
}

int XmppIMCommandManagerInterface::setHandler(XmppAccount::XmppAccountHandle handle, XmppChatIMCommandHandler* handler)
{
   if (XmppChat::XmppChatManagerInterface* intf = dynamic_cast<XmppChat::XmppChatManagerInterface*>(XmppChat::XmppChatManager::getInterface(mPhone)))
   {
      return intf->setIMCommandHandler(handle, handler);
   }

   return kError;
}

int XmppIMCommandManagerInterface::setHandler(XmppAccount::XmppAccountHandle handle, XmppMultiUserChatIMCommandHandler* handler)
{
   if (XmppMultiUserChat::XmppMultiUserChatManagerInterface* intf = dynamic_cast<XmppMultiUserChat::XmppMultiUserChatManagerInterface*>(XmppMultiUserChat::XmppMultiUserChatManager::getInterface(mPhone)))
   {
      return intf->setIMCommandHandler(handle, handler);
   }

   return kError;
}

XmppChat::XmppChatMessageHandle XmppIMCommandManagerInterface::sendChatIMCommand(XmppChat::XmppChatHandle handle, int type, const cpc::string& payload, const cpc::string& htmlPayload)
{
   if (XmppChat::XmppChatManagerInterface* intf = dynamic_cast<XmppChat::XmppChatManagerInterface*>(XmppChat::XmppChatManager::getInterface(mPhone)))
   {
      return intf->sendIMCommand(handle, type, payload, htmlPayload);
   }

   return kError;
}

XmppMultiUserChat::XmppMultiUserChatMessageHandle XmppIMCommandManagerInterface::sendMultiUserChatIMCommand(XmppMultiUserChat::XmppMultiUserChatHandle handle, int type, const cpc::string& payload)
{
   if (XmppMultiUserChat::XmppMultiUserChatManagerInterface* intf = dynamic_cast<XmppMultiUserChat::XmppMultiUserChatManagerInterface*>(XmppMultiUserChat::XmppMultiUserChatManager::getInterface(mPhone)))
   {
      return intf->sendIMCommand(handle, type, payload);
   }

   return kError;
}

}
}

#endif
