#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)

#include "XmppFileTransferStateImpl.h"
#include "XmppFileTransferManagerInterface.h"
#include "phone/PhoneInterface.h"

using namespace CPCAPI2::XmppFileTransfer;

XmppFileTransferStateImpl::XmppFileTransferStateImpl(XmppFileTransferManagerInterface* intf)
   : mInterface(intf)
{
}

XmppFileTransferStateImpl::~XmppFileTransferStateImpl()
{
}

void XmppFileTransferStateImpl::Release()
{
   delete this;
}

int XmppFileTransferStateImpl::getState(XmppFileTransferHandle handle, XmppFileTransferState& state)
{
   std::map<XmppFileTransferHandle,XmppFileTransferState>::iterator it = mStateMap.find(handle);
   if (it != mStateMap.end())
   {
      state = it->second;
      return kSuccess;
   }
   return kError;
}

int XmppFileTransferStateImpl::onNewFileTransfer( const XmppFileTransferHandle& handle, const NewFileTransferEvent& args )
{
   XmppFileTransferState state;
   state.account           = args.account;
   state.fileTransferState = args.fileTransferState;
   state.fileTransferType  = args.fileTransferType;
   state.endReason         = FileTransferEndReason_Unknown;
   state.remoteAddress     = args.remoteAddress;
   state.remoteDisplayName = args.remoteDisplayName;

   mStateMap[ handle ] = state; // default copy ctor (primitive types)
   return kSuccess;
}

int XmppFileTransferStateImpl::onFileTransferEnded( const XmppFileTransferHandle& handle, const FileTransferEndedEvent& args )
{
   std::map<XmppFileTransferHandle,XmppFileTransferState>::iterator it = mStateMap.find(handle);
   if (it != mStateMap.end())
   {
      //it->second.account = args.account;
      it->second.fileTransferState = args.fileTransferState;
      //it->second.fileTransferType = args.fileTransferType;
      it->second.endReason = args.endReason;
      //it->second.remoteAddress = args.remoteAddress;
      //it->second.remoteDisplayName = args.remoteDisplayName;
   }
   return kSuccess;
}

/**
 * Progress indication (in percent) of an ongoing file transfer item
 */
int XmppFileTransferStateImpl::onFileTransferItemProgress( const XmppFileTransferHandle& handle, const FileTransferItemProgressEvent& args )
{
   std::map<XmppFileTransferHandle,XmppFileTransferState>::iterator it = mStateMap.find(handle);
   if (it != mStateMap.end())
   {
      XmppFileTransferItems::iterator itItem = it->second.fileItems.begin();
      for (; itItem != it->second.fileItems.end(); ++itItem)
      {
         if( itItem->handle != args.fileTransferItem )
            continue;

         itItem->percentComplete = args.percent;
      }
   }
   return kSuccess;
}

/**
 * Invoked by the SDK when a file (item) has finished transfering
 */
int XmppFileTransferStateImpl::onFileTransferItemEnded( const XmppFileTransferHandle& handle, const FileTransferItemEndedEvent& args )
{
   std::map<XmppFileTransferHandle,XmppFileTransferState>::iterator it = mStateMap.find(handle);
   if (it != mStateMap.end())
   {
      XmppFileTransferItems::iterator itItem = it->second.fileItems.begin();
      for (; itItem != it->second.fileItems.end(); ++itItem)
      {
         if( itItem->handle != args.fileTransferItem )
            continue;

         // TODO: what to set here? Do we need file transfer item state as part of details?
      }
   }
   return kSuccess;
}

int XmppFileTransferStateImpl::onError(const XmppFileTransferHandle& handle, const ErrorEvent& args)
{
   return kSuccess;
}

#endif