#pragma once

#if !defined(CPCAPI2_XMPP_PRIVACY_IMPL_H)
#define CPCAPI2_XMPP_PRIVACY_IMPL_H

#include "cpcapi2defs.h"
#include "brand_branded.h"

#include "XmppAccountImpl.h"

#include <privacymanager.h>
#include <privacylisthandler.h>

namespace CPCAPI2
{

namespace XmppPrivacy
{

class XmppPrivacyImpl
   : public XmppAccount::XmppAccountObserver
   , public XmppAccount::XmppDiscoObserver
   , public gloox::PrivacyListHandler
{
public:
   XmppPrivacyImpl(XmppAccount::XmppAccountImpl& account);
   virtual ~XmppPrivacyImpl();

   void setInvisible(bool invisible);
   void setMyPresence(const XmppRoster::PresenceType& presence, const cpc::string& msg, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText);
   void setPresenceInDeny(bool deny);

   void addPrivacyRule(const gloox::PrivacyItem& rule);
   void removePrivacyRule(const gloox::PrivacyItem& rule);

public: // XmppAccount::XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;

public: // XmppAccount::XmppDiscoObserver
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) OVERRIDE;
   virtual void onXmppDiscoCompleted() OVERRIDE;

public: // gloox::PrivacyListHandler
   virtual void handlePrivacyListNames(const std::string&, const std::string&, const gloox::StringList&) OVERRIDE;
   virtual void handlePrivacyList(const std::string&, const gloox::PrivacyListHandler::PrivacyList&) OVERRIDE;
   virtual void handlePrivacyListChanged(const std::string&) OVERRIDE;
   virtual void handlePrivacyListResult(const std::string&, gloox::PrivacyListResult) OVERRIDE;

private:
   void cleanup();

   const gloox::PrivacyListHandler::PrivacyList createPrivacyList() const;
   const gloox::PrivacyListHandler::PrivacyList createInvisibileList() const;
   const gloox::PrivacyListHandler::PrivacyList createPresenceInDenyList() const;

private:
   XmppAccount::XmppAccountImpl& mAccount;
   gloox::PrivacyManager* mGlooxPrivacyManager;
   std::string mDefaultPrivacyListName;
   std::string mActivePrivacyListName;
   gloox::StringList mAllPrivacyListNames;
   XmppRoster::PresenceType mMyPresence;
   cpc::string mMyMsg;
   XmppRoster::UserActivityGeneralType mMyUserActivityGeneralType;
   XmppRoster::UserActivitySpecificType mMyUserActivitySpecificType;
   cpc::string mMyUserActivityText;
   bool mPresenceInDenied;
   gloox::PrivacyListHandler::PrivacyList mPendingPrivacyItems;
};

} // namespace XmppPrivacy
} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_PRIVACY_IMPL_H
