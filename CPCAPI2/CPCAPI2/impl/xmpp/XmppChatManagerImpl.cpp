// bliu: TODO list
// raiseMessageEvent(gloox::MessageEventCancel)

#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)

#include "XmppChatManagerImpl.h"
#include "XmppTimeHelper.h"
#include "CpcXep.h"
#include "CpcXepIMCommand.h"
#include "CpcXepChatMarker.h"
#include "CpcXepMessageCorrection.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "util/ReactorHelpers.h"
#include "log/LocalLogger.h"
#include "iscomposing/IsComposingInfo.h"
#include "iscomposing/IsComposingHelper.h"
#include "analytics1/AnalyticsManagerInterface.h"

#include <message.h>
#include <messageevent.h>
#include <messageeventfilter.h>
#include <chatstate.h>
#include <chatstatefilter.h>
#include <messagefilter.h>
#include <xhtmlim.h>
#include <error.h>
#include <util.h>
#include <receipt.h>

#include "rutil/Random.hxx"
#include <boost/algorithm/string.hpp> // boost::replace

#include "CpcXepChatMarker.cpp"
#include "CpcXepMessageProcessingHint.cpp"
#include "CpcXepMessageOrigin.cpp"
#include "CpcXepMessageReaction.cpp"
#include "CpcXepMessageRetract.cpp"
#include "CpcXepMessageStyling.cpp"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_CHAT
#define CP_LOCAL_LOGGER_VAR mLocalLogger

enum DiscoContext
{
   InitialInfo,
   InitialItems,
   CustomItemsInfo
};

#define FIRE_ERROR(handle, msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   fireError(handle, ss.str().c_str());\
}

template<typename Event>
static void setEventTimeStamp(Event& evt, const gloox::Message& msg)
{
   evt.timestamp = 0;
   evt.millisecond = 0;

   if (const gloox::DelayedDelivery* dd = msg.when())
   {
      CPCAPI2::TimeHelper::to_time(dd->stamp(), evt.timestamp, evt.millisecond);
   }

   if (evt.timestamp == 0)
   {
      uint64_t ms = CPCAPI2::TimeHelper::millisSinceEpoch();
      evt.timestamp = (time_t)(ms / 1000);
      evt.millisecond = ms % 1000;

      evt.isDelayedDelivery = false;
   }
   else
   {
      evt.isDelayedDelivery = true;
   }
}

// this code is duplicated in XmppMultiUserChatManagerImpl.cpp
static const std::string _generate_guid()
{
   // previous code used std::rand to generate a 32 character length string.
   // we've switched to crypto random to avoid collisions when running auto tests
   // in parallel in multiple docker containers on linux.

   const unsigned int charsToReturn = 32;
   // hex() turns every byte in a resip::Data into 2 characters
   const unsigned int randomBytes = charsToReturn / 2;
   return resip::Random::getCryptoRandom(randomBytes).hex().c_str();
}

namespace CPCAPI2
{
namespace XmppChat
{

class GlooxXHtmlParser : private gloox::TagHandler
{
public:
   GlooxXHtmlParser(const std::string& bodyContent)
      : mHtmlTag(NULL)
   {
      mParser = new gloox::Parser(this);
      std::string htmlContent = "<html xmlns='" + gloox::XMLNS_XHTML_IM + "'><body xmlns='http://www.w3.org/1999/xhtml'>";
      htmlContent += bodyContent;
      htmlContent += "</body></html>";
      boost::ireplace_all(htmlContent, "<br>", "<br/>"); // TODO: properly convert HTML to XHTML
      boost::replace_all(htmlContent, "&nbsp;", "&#160;"); // DCM: gloox does not support HTML named entities

      if (mParser->feed(htmlContent) != -1)
      {
         delete mHtmlTag;
         mHtmlTag = NULL;
      }
   }

   virtual ~GlooxXHtmlParser()
   {
      delete mHtmlTag;
      delete mParser;
   }

   virtual void handleTag(gloox::Tag* tag)
   {
      delete mHtmlTag; // it might be a clone from a previous tag

      mHtmlTag = tag == NULL ? NULL : tag->clone();
   }

   gloox::Tag* htmlTag()
   {
      return mHtmlTag;
   }

private:
   gloox::Parser* mParser;
   gloox::Tag* mHtmlTag;
};

class GlooxXHtmlReader
{
public:
   GlooxXHtmlReader(const gloox::Stanza& stanza)
      : mWasFound(false)
   {
      const gloox::XHtmlIM* xi = stanza.findExtension<gloox::XHtmlIM>(gloox::ExtXHtmlIM);
      if (xi)
      {
         gloox::Tag* bodyTag = xi->xhtml()->findChild("body", gloox::XMLNS, "http://www.w3.org/1999/xhtml");
         if (bodyTag)
         {
            std::string bodyText = bodyTag->xml();
            std::string::size_type b = bodyText.find_first_of('>');
            std::string::size_type e = bodyText.find_last_of('<');
            if (b != std::string::npos && e != std::string::npos && b < e)
            {
               // take out the 'body' start and end tags
               mBodyContent = bodyText.substr(b+1, e-b-1);
               mWasFound = true;
            }
         }
      }
   }

   bool found() const
   {
      return mWasFound;
   }

   const std::string& bodyContent() const
   {
      return mBodyContent;
   }

private:
   bool mWasFound;
   std::string mBodyContent;
};

class XmppMessageFilter : public gloox::MessageFilter
{
public:
   XmppMessageFilter(gloox::MessageSession* session, XmppChatManagerImpl& impl, XmppChatHandle handle)
      : gloox::MessageFilter(session)
      , mImpl(impl)
      , mHandle(handle)
      , mReplacesMessageID("")
   {}

   // gloox::MessageFilter
   virtual void decorate(gloox::Message& msg) OVERRIDE
   {
      if (mIMCommand)
      {
         msg.removeExtension(gloox::ExtMessageEvent); // assume MessageEvent extension comes prior to IMCommand
         msg.addExtension(mIMCommand.release());
      }

      if (!mOriginID.empty())
      {
          msg.addExtension(new CpcXepMessageOrigin(mOriginID));
          mOriginID.clear();
      }

      if (!mReplacesMessageID.empty())
      {
          msg.addExtension(new CpcXepMessageCorrection(mReplacesMessageID));
          mReplacesMessageID.clear();
      }

      if (!mXHtmlContentToSend.empty())
      {
         GlooxXHtmlParser xhtmlParser (mXHtmlContentToSend);
         if (gloox::Tag* tag = xhtmlParser.htmlTag())
         {
            gloox::XHtmlIM* xhtmlIm = new gloox::XHtmlIM(tag);
            msg.addExtension(xhtmlIm);
         }
         else
         {
            DebugLog(<< "XmppMessageFilter::decorate(): malformed XHTML: " << mXHtmlContentToSend);

            mImpl.fireError(mHandle, ("invalid xhtml content: " + mXHtmlContentToSend).c_str());
         }

         mXHtmlContentToSend.clear();
      }

      if (!mReactionTarget.empty())
      {
         msg.addExtension(new CpcXepMessageReaction(mReactionTarget, mReactionsToSend));
         msg.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
         mReactionTarget.clear();
      }

      if (!mRetractTarget.empty())
      {
         msg.addExtension(new CpcXepMessageRetract(mRetractTarget));
         msg.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
         mRetractTarget.clear();
      }
   }

   virtual void filter(gloox::Message& msg) OVERRIDE {}

private:
   friend class XmppChatManagerImpl;

   XmppChatManagerImpl& mImpl;
   XmppChatHandle mHandle;
   std::string mReplacesMessageID;
   // message
   std::string mOriginID;
   std::string mXHtmlContentToSend;
   // reaction
   std::string mReactionTarget;
   std::vector<std::string> mReactionsToSend;
   // message retraction
   std::string mRetractTarget;

   std::unique_ptr<CpcXepIMCommand> mIMCommand;
};

class XmppMessageEventFilter : public gloox::MessageEventFilter
{
public:
   XmppMessageEventFilter(gloox::MessageSession* session)
      : gloox::MessageEventFilter(session)
   {
   }

   // bliu: copied over from gloox::MessageEventFilter::raiseMessageEvent()
   // in order to add store-offline in delivery confirmation
   void raiseMessageEvent(gloox::MessageEventType event, const std::string& id, const std::string& thread)
   {
      if (m_disable || (!(m_requestedEvents & event) && (event != gloox::MessageEventCancel)))
         return;

      switch (event)
      {
      case gloox::MessageEventOffline:
      case gloox::MessageEventDelivered:
      case gloox::MessageEventDisplayed:
         m_requestedEvents &= ~event;
         break;
      case gloox::MessageEventComposing:
         if (m_lastSent == gloox::MessageEventComposing)
            return;
         break;
      case gloox::MessageEventCancel:
      default:
         break;
      }

      m_lastSent = event;

      gloox::Message m(gloox::Message::Chat, m_parent->target());
      m.setThread(thread);
      m.addExtension(new gloox::MessageEvent(event, id.empty() ? m_lastID : id));
      if (event == gloox::MessageEventDelivered)
      {
         // bliu: not used for now
         //m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Received, id.empty() ? m_lastID : id));
         m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
      }

      send(m);
   }
};

#ifdef _WIN32
std::atomic<XmppChatHandle> XmppChatManagerImpl::sNextXmppChatHandle = 1;
std::atomic<XmppChatMessageHandle> XmppChatManagerImpl::sNextXmppChatMessageHandle = 1;
#else
std::atomic<XmppChatHandle> XmppChatManagerImpl::sNextXmppChatHandle = ATOMIC_VAR_INIT(1);
std::atomic<XmppChatMessageHandle> XmppChatManagerImpl::sNextXmppChatMessageHandle = ATOMIC_VAR_INIT(1);
#endif

XmppChatManagerImpl::XmppChatManagerImpl(PhoneInterface* cpcPhone, XmppAccount::XmppAccountImpl& account, XmppChatManagerInterface& intf)
   : IsComposingManager(cpcPhone, &account.getAccountInterface().getReactor())
   , mAppHandler(NULL)
   , mIMCommandHandler(NULL)
   , mAccount(account)
   , mInterface(intf)
   , mInactiveTimerHandler(*this)
   , mPeerDiscoveryTimerHandler(*this)
   , mPhone(cpcPhone)
   , mLocalLogger(cpcPhone->localLogger())
   , mSupportChatMessageStyling(true)
   , mLegacyReceiptsEnabled(true)
   , mDeliveryReceiptsEnabled(false)
   , mReadReceiptsEnabled(false)
{
   mAccount.registerAccountObserver(this);
   StackLog(<< "XmppChatManagerImpl::XmppChatManagerImpl(): constructor this: " << this);
}

XmppChatManagerImpl::~XmppChatManagerImpl()
{
   StackLog(<< "XmppChatManagerImpl::XmppChatManagerImpl(): destructor this: " << this);

   cleanup();

   mAccount.unregisterAccountObserver(this);

}

void XmppChatManagerImpl::addSdkObserver(XmppChatHandler* sdkObserver)
{
   mSdkObservers.push_back(sdkObserver);
}

void XmppChatManagerImpl::removeSdkObserver(XmppChatHandler* sdkObserver)
{
   mSdkObservers.remove(sdkObserver);
}

void XmppChatManagerImpl::setHandler(XmppChatHandler* handler)
{
   StackLog(<< "XmppChatManagerImpl::setHandler(): chat handler: " << handler);
   mAppHandler = handler;
}

void XmppChatManagerImpl::setHandler(XmppIMCommand::XmppChatIMCommandHandler* handler)
{
   DebugLog(<< "XmppChatManagerImpl::setHandler(): im command handler: " << handler);
   mIMCommandHandler = handler;
}

XmppChatInfo* XmppChatManagerImpl::getChatInfo(XmppChatHandle handle) const
{
   ChatInfoMap::const_iterator it = mChatInfoMap.begin();
   for (; it != mChatInfoMap.end(); ++it)
   {
      if ((*it)->handle == handle)
      {
         return *it;
      }
   }
   return NULL;
}

void XmppChatManagerImpl::cleanup()
{
   LocalDebugLog("XmppChatManagerImpl::cleanup");

   while (!mChatInfoMap.empty())
   {
      ChatInfoMap::iterator it = mChatInfoMap.begin();
      endChat((*it)->handle, ChatEndReason_UserTerminatedLocally);
   }

   for (auto& i : mMessageAckMap)
   {
      SendMessageFailureEvent evt;
      evt.message = i.second.second.message;
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageFailure), i.second.first, evt);
   }

   mMessageAckMap.clear();
}

void XmppChatManagerImpl::addChatInfo(XmppChatInfo* chatInfo)
{
   mChatInfoMap.insert(chatInfo);
}

XmppChatInfo* XmppChatManagerImpl::getChatInfoForGlooxMessageSession(gloox::MessageSession* session) const
{
   ChatInfoMap::const_iterator it = mChatInfoMap.begin();
   for (; it != mChatInfoMap.end(); ++it)
   {
      if ((*it)->mGlooxMessageSession == session)
      {
         return *it;
      }
   }
   return NULL;
}

void XmppChatManagerImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{

   getChatSettings(mAccount.getSettings());

   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_CHAT_STATES);
   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_DELAY);
   if (mLegacyReceiptsEnabled)
   {
      account.getGlooxClient()->disco()->addFeature(CpcXepChatMarker::XMLNS_CHAT_MARKER);
      InfoLog(<< "LegacyReceipts is enabled");
   }
   else
   {
      if (mReadReceiptsEnabled)
      {
         account.getGlooxClient()->disco()->addFeature(CpcXepChatMarker::XMLNS_CHAT_MARKER);
         InfoLog(<< "ReadReceipts is enabled");
      }
      if (mDeliveryReceiptsEnabled)
      {
         account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_RECEIPTS);
         InfoLog(<< "DeliveryReceipts is enabled");
      }
   }
   // enable desktop sharing with Cisco Jabber client
   account.getGlooxClient()->disco()->addFeature("cisco.com/p2p-desktop-share");
   // enable picture sharing with Cisco Jabber client
   account.getGlooxClient()->disco()->addFeature("http://webex.com/connect/customcaps/picture-share");
   account.getGlooxClient()->disco()->addFeature("http://webex.com/connect/customcaps/picture-share-mix");
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageOrigin::XMLNS_MESSAGE_ORIGIN);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageCorrection::XMLNS_MESSAGE_CORRECTION);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageReaction::XMLNS_MESSAGE_REACTION);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageRetract::XMLNS_MESSAGE_RETRACT);
   if (mSupportChatMessageStyling)
      account.getGlooxClient()->disco()->addFeature(CpcXepMessageStyling::XMLNS_MESSAGE_STYLING);

   account.getGlooxClient()->registerStanzaExtension(new gloox::XHtmlIM());
   account.getGlooxClient()->registerStanzaExtension(new gloox::ChatState(0));
   account.getGlooxClient()->registerStanzaExtension(new gloox::MessageEvent(0));
   account.getGlooxClient()->registerStanzaExtension(new gloox::DelayedDelivery());
   account.getGlooxClient()->registerStanzaExtension(new CpcXepIMCommand());
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageOrigin(""));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageCorrection(0));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageReaction("",std::vector<std::string>()));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageRetract(""));
   if (mSupportChatMessageStyling)
      account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageStyling(true));
   if (mLegacyReceiptsEnabled)
   {
      account.getGlooxClient()->registerStanzaExtension(new CpcXepChatMarker());
   }
   else
   {
      if (mReadReceiptsEnabled)
         account.getGlooxClient()->registerStanzaExtension(new CpcXepChatMarker());
      if (mDeliveryReceiptsEnabled)
         account.getGlooxClient()->registerStanzaExtension(new gloox::Receipt(0));
   }

   StackLog(<< "XmppChatManagerImpl::onWillConnect(): registering XmppChatManagerImpl: " << this << " as message session handler in gloox client: " << account.getGlooxClient());
   account.getGlooxClient()->registerMessageSessionHandler(this, gloox::Message::Chat | gloox::Message::Headline | gloox::Message::Normal);
}

void XmppChatManagerImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppChatManagerImpl::onDidConnect()");
}

void XmppChatManagerImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppChatManagerImpl::onWillDisconnect()");
   cleanup();
}

void XmppChatManagerImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppChatManagerImpl::onDidDisconnect()");
   cleanup();
}

void XmppChatManagerImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppChatManagerImpl::onDestroy()");
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppChatManagerImpl::onStreamManagementAck(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppChatManagerImpl::onStreamManagementAck()");
   gloox::TagList tags = mAccount.getGlooxClient()->sendQueue();

   auto copy = mMessageAckMap;
   for (auto& i : copy)
   {
      bool acked = true;

      for (auto& t : tags)
      {
         if (t == NULL) continue;
         if (t->name() != "message") continue;
         if (t->findAttribute("id") == i.first)
         {
            acked = false;
            break;
         }
      }

      if (acked)
      {
         InfoLog(<< "onSendMessageSuccess triggered by onStreamManagementAck: message=" << i.second.second.message << ", id=" << i.first);
         fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), i.second.first, i.second.second);
         mMessageAckMap.erase(i.first);
      }
   }

   gloox::util::clearList(tags);
}

bool XmppChatManagerImpl::handleMessageSession(gloox::MessageSession* session)
{
   StackLog(<< "XmppChatManagerImpl::handleMessageSession()");
   XmppChatInfo* ci = new XmppChatInfo();
   ci->accountHandle = mAccount.getHandle();
   ci->handle = sNextXmppChatHandle++;
   ci->mGlooxMessageSession = session;
   ci->mGlooxSessionEventHandler = new GlooxSessionEventHandler(*this, session);
   ci->mGlooxMessageEventFilter = new XmppMessageEventFilter(session);
   ci->mGlooxMessageEventFilter->registerMessageEventHandler(ci->mGlooxSessionEventHandler);
   ci->mGlooxChatStateFilter = new gloox::ChatStateFilter(session);
   ci->mGlooxChatStateFilter->registerChatStateHandler(ci->mGlooxSessionEventHandler);
   ci->mMessageFilter = new XmppMessageFilter(session, *this, ci->handle);
   addChatInfo(ci);

   IsComposingManager::initialize(ci);

   session->registerMessageHandler(this);

   // Fire the new chat session evt
   NewChatEvent evt;
   evt.chatType = ChatType_Incoming;
   evt.account = mAccount.getHandle();
   evt.remote = session->target().bare().c_str();
   fireEvent(cpcFunc(XmppChatHandler::onNewChat), ci->handle, evt);

   return true;
}

// gloox::MessageHandler
void XmppChatManagerImpl::handleMessage(const gloox::Message& msg, gloox::MessageSession* session)
{
   InfoLog(<< "XmppChatManagerImpl::handleMessage(): threadId=" << msg.thread() << ", messageId=" << msg.id());

   XmppChatInfo* ci = getChatInfoForGlooxMessageSession(session);
   if (ci == NULL)
   {
      FIRE_ERROR(0, "MessageSession not found");
      return;
   }

   if (msg.subtype() == gloox::Message::MessageType::Error)
   {
      InfoLog(<< "XmppChatManagerImpl::handleMessage() error: " << std::unique_ptr<gloox::Tag>(msg.tag())->xml());

      //ignore if warning or not an actual error
      // bliu: StanzaErrorTypeContinue is never actually referenced in gloox
      if (const gloox::Error* error = msg.error())
      {
         if (error->type() == gloox::StanzaErrorTypeContinue || error->type() == gloox::StanzaErrorTypeUndefined)
            return;
      }

      if (msg.id().empty()) return;

      MessageDeliveryErrorEvent evt;
      evt.from = msg.from().full().c_str();
      evt.messageDeliveryStatus = MessageDeliveryStatus_Error;
      evt.message = ci->mOutgoingMessages.find(msg.id());
      fireEvent(cpcFunc(XmppChatHandler::onMessageDeliveryError), ci->handle, evt);
      return;
   }

   if (const CpcXepIMCommand* imcmd = msg.findExtension<CpcXepIMCommand>(EXepIMCommand))
   {
      XmppIMCommand::ChatIMCommandReceivedEvent evt;
      evt.remote = msg.from().bare().c_str();
      evt.type = imcmd->type();
      evt.payload = imcmd->payload().c_str();

      if (evt.payload.empty())
      {
         GlooxXHtmlReader xHtmlReader(msg);
         std::string htmlContent;

         if (xHtmlReader.found())
         {
            htmlContent = xHtmlReader.bodyContent();
         }

         if (!htmlContent.empty())
         {
            evt.payload = htmlContent.c_str();
         }
      }

      setEventTimeStamp(evt, msg);

      // bliu: TODO add sdk observer
      mAccount.postCallback(makeFpCommand(XmppIMCommand::XmppChatIMCommandHandler::onIMCommandReceived, mIMCommandHandler, ci->handle, evt));
      return;
   }

   if (mLegacyReceiptsEnabled)
   {
      if (const CpcXepChatMarker* c = msg.findExtension<CpcXepChatMarker>(EXepChatMarker))
      {
         if (!c->isValid())
         {
            WarningLog(<< "invalid Chat Marker");
            goto end_of_chat_marker;
         }

         // bliu: not yet sent by message receiver
         switch (c->type())
         {

   #if 0
         case CpcXepChatMarker::ChatMarkerType_Received:
            {
               MessageDeliveredEvent evt;
               evt.message = ci->mOutgoingMessages.find(c->id());
               evt.messageDeliveryStatus = MessageDeliveryStatus_Delivered;
               evt.messageId = c->id().c_str();
               evt.threadId = msg.thread().c_str();
               evt.from = msg.from().full().c_str();

               setEventTimeStamp(evt, msg);

               fireEvent(cpcFunc(XmppChatHandler::onMessageDelivered), ci->handle, evt);
               break;
            }
   #endif

         case CpcXepChatMarker::ChatMarkerType_Acknowledged:
            {
               MessageReadEvent evt;
               evt.messageId = c->id().c_str();
               evt.threadId = msg.thread().c_str();
               evt.from = msg.from().full().c_str();

               setEventTimeStamp(evt, msg);

               fireEvent(cpcFunc(XmppChatHandler::onMessageRead), ci->handle, evt);
               break;
            }

         default:
            {
               WarningLog(<< "non-supported Chat Marker type=" << c->type());
               break;
            }
         }

      end_of_chat_marker: ;
      }
   }
   else
   {
      if (mDeliveryReceiptsEnabled)
      {
         if (const gloox::Receipt* r = msg.findExtension<gloox::Receipt>(gloox::ExtReceipt))
         {
            gloox::Receipt::ReceiptType type = r->rcpt();
            if (type == gloox::Receipt::Request)
            {
               notifyMessageDelivered(ci, msg.thread(), msg.id());
            }
            else if (type == gloox::Receipt::Received)
            {
               MessageDeliveredEvent evt;
               evt.message = ci->mOutgoingMessages.find(r->id());
               evt.messageDeliveryStatus = MessageDeliveryStatus_Delivered;
               evt.messageId = r->id().c_str();
               evt.threadId = msg.thread().c_str();
               evt.from = msg.from().full().c_str();
               setEventTimeStamp(evt, msg);

               fireEvent(cpcFunc(XmppChatHandler::onMessageDelivered), ci->handle, evt);
            }
         }
      }

      if (mReadReceiptsEnabled)
      {
         if (const CpcXepChatMarker* c = msg.findExtension<CpcXepChatMarker>(EXepChatMarker))
         {
            if (!c->isValid())
            {
               WarningLog(<< "invalid Chat Marker");
            }
            else
            {
               if (c->type() == CpcXepChatMarker::ChatMarkerType_Displayed)
               {
                  MessageReadEvent evt;
                  evt.messageId = c->id().c_str();
                  evt.threadId = msg.thread().c_str();
                  evt.from = msg.from().full().c_str();

                  setEventTimeStamp(evt, msg);

                  fireEvent(cpcFunc(XmppChatHandler::onMessageRead), ci->handle, evt);
               }
               else if (c->type() == CpcXepChatMarker::ChatMarkerType_Markable)
               {
                  InfoLog(<< "XmppChatManagerImpl::handleMessage(): marker request received for message id " << msg.id());
               }
            }
         }
      }
   }

   GlooxXHtmlReader xHtmlReader(msg);
   std::string htmlContent;

   if (xHtmlReader.found())
   {
      htmlContent = xHtmlReader.bodyContent();
   }

   cpc::string originId;
   if (const CpcXepMessageOrigin* mr = msg.findExtension<CpcXepMessageOrigin>(EXepMessageOrigin))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid origin tag");
      }
      else
      {
         originId = mr->originID().c_str();
      }
   }

   cpc::string reactionTarget;
   cpc::vector<cpc::string> reactions;
   if (const CpcXepMessageReaction* mr = msg.findExtension<CpcXepMessageReaction>(EXepMessageReaction))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid reaction tag");
      }
      else
      {
         reactionTarget = mr->targetId().c_str();
         std::vector<std::string> temp = mr->reactions();   // this avoids a dangling pointer below
         for (auto it = temp.begin(); it != temp.end(); ++it)
            reactions.push_back(it->c_str());
      }
   }

   cpc::string retractTarget;
   if (const CpcXepMessageRetract* mr = msg.findExtension<CpcXepMessageRetract>(EXepMessageRetract))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid message retraction tag");
      }
      else
      {
         retractTarget = mr->targetId().c_str();
      }
   }

   if (msg.body().empty() && htmlContent.empty() && reactionTarget.empty() && retractTarget.empty())  
      return;

   cpc::string messageId = msg.id().c_str();
   if (messageId.empty())
   {
      if (originId.empty())
      {
         ErrLog(<< "XmppChatManagerImpl::handleMessage(): from=" << msg.from() << " got empty message ID");
      }
      else
      {
         InfoLog(<< "XmppChatManagerImpl::handleMessage(): from=" << msg.from() << " got empty message ID so using origin ID: " << originId);
         messageId = originId;
      }
      
   }
   // DRL NOTE: We have code in XmppMultiUserChatManagerImpl for which adds to the ID
   // if it is too short, so we may want to have that code here too?

   //if(mAccount->isAcceptableInstantMessageMimeType(mimeType, mimeSubType) == false)
   //{
   //   return;
   //}

   ci->mLastIncomingGlooxMessage.reset(new gloox::Message(msg));

   XmppChatMessageHandle message = sNextXmppChatMessageHandle++;
   addMessageInfo(ci->handle, message, MessageType_Message, false, 0, IsComposingMessageState_Unknown);

   LocalMaxLog("XmppChatManagerImpl::handleMessage(): handle: {} threadId: {} messageId: {}", ci->handle, msg.thread(), messageId);

   ci->mIncomingMessages.add(message, messageId.c_str());

   IsComposingManager::setMessageReceived(ci);

   cpc::string replaces;
   if (const CpcXepMessageCorrection* mc = msg.findExtension<CpcXepMessageCorrection>(EXepMessageCorrection))
   {
      if (!mc->isValid())
      {
         WarningLog(<< "No message id in replace tag");
      }
      else
      {
         replaces = mc->id().c_str();
      }
   }

   if (!reactionTarget.empty())
   {
      NewReactionEvent evt;
      evt.account = mAccount.getHandle();
      evt.message = message;
      evt.messageId = messageId;
      evt.threadId = msg.thread().c_str();
      evt.from = msg.from().bare().c_str();

      setEventTimeStamp(evt, msg);

      // the only difference with the below
      evt.reactionTarget = reactionTarget.c_str();
      evt.reactions = reactions;

      fireEvent(cpcFunc(XmppChatHandler::onNewReaction), ci->handle, evt);
   }
   else if (!retractTarget.empty())
   {
      NewMessageRetractionEvent evt;
      evt.account = mAccount.getHandle();
      evt.message = message;
      evt.messageId = messageId;
      evt.threadId = msg.thread().c_str();
      evt.from = msg.from().bare().c_str();

      setEventTimeStamp(evt, msg);

      // the only difference with the above
      evt.retractTarget = retractTarget.c_str();

      fireEvent(cpcFunc(XmppChatHandler::onNewMessageRetraction), ci->handle, evt);
   }
   else
   {
      NewMessageEvent evt;
      evt.account = mAccount.getHandle();
      evt.message = message;
      evt.messageId = messageId;
      evt.originId = originId;
      evt.threadId = msg.thread().c_str();
      evt.from = msg.from().bare().c_str();
      evt.to = msg.to().bare().c_str();
      evt.replaces = replaces;

      setEventTimeStamp(evt, msg);

      // the only difference with the above
      evt.messageContent = msg.body().c_str();
      evt.htmlText = htmlContent.c_str();
      evt.subject = msg.subject().c_str();

      fireEvent(cpcFunc(XmppChatHandler::onNewMessage), ci->handle, evt);
   }

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   //Send data to Analytics (UEM) module as well
   static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->instantMessageInfoFired(mAccount.getHandle(), true, false);
#endif

   if (mLegacyReceiptsEnabled)
   {
      // bliu: temporarily allow automatic acknowledge for now
      notifyMessageDelivered(ci, msg.thread().c_str(), messageId.c_str());
   }
}

// gloox::PresenceHandler
void XmppChatManagerImpl::handlePresence(const gloox::Presence& presence)
{
   StackLog(<< "XmppChatManagerImpl::handlePresence(): " << presence.from().full());

   for (auto& i : mChatInfoMap)
   {
      if (i->targetAddresses.empty()) continue;

      auto& jid = i->targetAddresses.front();

      if (jid.bareJID() == presence.from().bareJID())
      {
         i->mGlooxMessageSession->resetResource();
      }
   }
}

void XmppChatManagerImpl::handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context)
{
   StackLog(<< "XmppChatManagerImpl::handleDiscoInfo(): " << from.full());
/* It does not seem necessary to save this.
   mDiscoSet.erase(context);

   // 0 for initial getDiscoInfo() upon connected
   if (context == DiscoContext::InitialInfo)
   {
      if (info.hasFeature(gloox::XMLNS_DISCO_ITEMS))
      {
         mAccount.getGlooxClient()->disco()->getDiscoItems(mAccount.getGlooxClient()->server(), gloox::EmptyString, this, DiscoContext::InitialItems);
         mDiscoSet.insert(InitialItems);
      }
   }
*/
   if (mLegacyReceiptsEnabled)
   {
      if (!info.hasFeature(CpcXepChatMarker::XMLNS_CHAT_MARKER))
         WarningLog(<< "LegacyReceipts is enabled, but remote " << from.full() << " doesn't support feature " << CpcXepChatMarker::XMLNS_CHAT_MARKER);
   }
   else
   {
      if (mReadReceiptsEnabled)
      {
         if (!info.hasFeature(CpcXepChatMarker::XMLNS_CHAT_MARKER))
            WarningLog(<< "ReadReceipts is enabled, but remote " << from.full() << " doesn't support feature " << CpcXepChatMarker::XMLNS_CHAT_MARKER);
      }
      if (mDeliveryReceiptsEnabled)
      {
         if (!info.hasFeature(gloox::XMLNS_RECEIPTS))
            WarningLog(<< "DeliveryReceipts is enabled, but remote " << from.full() << " doesn't support feature " << gloox::XMLNS_RECEIPTS);
      }
   }

   // if JID have resource check if partner supports XEP-0308
   if (!from.resource().empty()) {
      DebugLog(<< "Searching capabilities of: " << from.full());
      bool foundJID = false;
      for (const auto &chatInfo : mChatInfoMap) {
         if (chatInfo) {
            const auto &targetAddresses = chatInfo->targetAddresses;
            if (targetAddresses.end() != find(targetAddresses.begin(), targetAddresses.end(), from)) {
               chatInfo->mReplaceMessageSupported = info.hasFeature(CpcXepMessageCorrection::XMLNS_MESSAGE_CORRECTION);
               foundJID = true;

               // find, cancel and remove peer discovery timer
               const auto itPeerDiscoTimerIt = chatInfo->mPeerDiscoveryTimers.find(from);
               if (chatInfo->mPeerDiscoveryTimers.end() != itPeerDiscoTimerIt) {
                  auto peerDiscoTimer = itPeerDiscoTimerIt->second;
                  if (peerDiscoTimer != NULL) {
                     peerDiscoTimer->cancel();
                  }
                  chatInfo->mPeerDiscoveryTimers.erase(itPeerDiscoTimerIt);
               }

               ChatDiscoEvent evt;
               evt.remoteJID = from.full().c_str();
               evt.replaceMessageSupported = chatInfo->mReplaceMessageSupported;
               evt.messageRetractionSupported = false;
               evt.messageReactionsSupported = false;

               fireEvent(cpcFunc(XmppChatHandler::onChatDiscoCompleted), chatInfo->handle, evt);
               break;
            }
         }
      }

      if (!foundJID) {
         WarningLog(<< "JID: " << from.full() << " not found in list of available chats");
      }
   }

}

void XmppChatManagerImpl::handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context)
{
   StackLog(<< "XmppChatManagerImpl::handleDiscoItems(): " << from.full());
/*
   mDiscoSet.erase(context);

   if (context != DiscoContext::InitialItems) return;

   int i = DiscoContext::CustomItemsInfo; // to distinguish from initial getDiscoInfo() and getDiscoItems()

   for (gloox::Disco::ItemList::const_iterator it = items.items().begin(), end = items.items().end(); it != end; ++it)
   {
      mClient->disco()->getDiscoInfo((*it)->jid(), gloox::EmptyString, this, i);

      mDiscoSet.insert(i++);
   }
*/
}

void XmppChatManagerImpl::handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context)
{
   StackLog(<< "XmppChatManagerImpl::handleDiscoError(): " << from.full());
/*
   InfoLog(<< "Disco error: handle=" << mHandle << " from=" << from.full() << " gloox error=" << (error == NULL ? "" : error->text()));

   mDiscoSet.erase(context);

   if (!isDiscoCompleted()) return;

   for (DiscoObservers::iterator it = mDiscoObservers.begin(), end = mDiscoObservers.end(); it != end; ++it)
   {
      (*it)->onXmppDiscoCompleted();
   }
*/
}

bool XmppChatManagerImpl::handleDiscoSet(const gloox::IQ& iq)
{
   StackLog(<< "XmppChatManagerImpl::handleDiscoSet()");
   
   return gloox::DiscoHandler::handleDiscoSet(iq);
}

void XmppChatManagerImpl::InactiveTimerHandler::onTimer(unsigned short timerId, void* appState)
{
   StackLog(<< "XmppChatManagerImpl::onTimer()");
   if (timerId != 1) return; // not inactive timer

   XmppChatInfo* chatInfo = reinterpret_cast<XmppChatInfo*>(appState);
   assert(chatInfo != NULL);

   if (!mImpl.validateChatStatus(chatInfo)) return;

   if (!chatInfo->mIsActive) return;

   chatInfo->mGlooxChatStateFilter->setChatState(gloox::ChatStateInactive);
   chatInfo->mIsActive = false;
}

void XmppChatManagerImpl::PeerDiscoveryTimerHandler::onTimer(unsigned short timerId, void* waitParams)
{
   StackLog(<< "XmppChatManagerImpl::PeerDiscoveryTimerHandler::onTimer()");

   std::shared_ptr<PeerDiscoveryWaitParams> params(reinterpret_cast<PeerDiscoveryWaitParams*>(waitParams));
   if (!params) {
      WarningLog(<<"XmppChatManagerImpl::PeerDiscoveryTimerHandler::onTimer() - waitParams is NULL");
      return;
   }

   if (timerId != 1) return; // not inactive timer

   XmppChatInfo* chatInfo = params->chatInfo;
   assert(chatInfo != NULL);

   if (!mImpl.validateChatStatus(chatInfo)) return;

   const auto peerDiscoTimerIt = chatInfo->mPeerDiscoveryTimers.find(params->jid);
   if (peerDiscoTimerIt == chatInfo->mPeerDiscoveryTimers.end())  return; // request not found

   ChatDiscoErrorEvent evt;
   evt.remoteJID = params->jid.bare().c_str();
   evt.reason = PeerDiscoErrorReason::PeerDiscoErrorReason_Timeout;
   mImpl.fireEvent(cpcFunc(XmppChatHandler::onChatDiscoError), chatInfo->handle, evt);

   delete peerDiscoTimerIt->second;
   chatInfo->mPeerDiscoveryTimers.erase(peerDiscoTimerIt);
}

void XmppChatManagerImpl::handleChatState(gloox::MessageSession* session, const gloox::JID& from, gloox::ChatStateType state)
{
   StackLog(<< "XmppChatManagerImpl::handleChatState()");
   XmppChatInfo* ci = getChatInfoForGlooxMessageSession(session);
   if (ci == NULL)
   {
      FIRE_ERROR(0, "MessageSession not found");
      return;
   }

   switch (state)
   {
   case gloox::ChatStateGone:
      // must be done async since gloox still needs access to the MessageSession

      LocalDebugLog("XmppChatManagerImpl::handleChatState ChatState gone for chat: {}", ci->handle);
      mAccount.post(resip::resip_bind(&XmppChatManagerImpl::endChat, shared_from_this(), ci->handle, ChatEndReason_UserTerminatedRemotely));
      break;

   case gloox::ChatStateComposing:
      {
         IsComposing::IsComposingDocument isComposingDoc;
         isComposingDoc.setState(IsComposing::IsComposingMessageState_Active);
         isComposingDoc.setRefresh(120); // bliu: TODO timeout value
         IsComposingManager::processIsComposingMessageNotification(ci, isComposingDoc);
         break;
      }

   case gloox::ChatStatePaused:
   case gloox::ChatStateInactive:
      {
         IsComposing::IsComposingDocument isComposingDoc;
         isComposingDoc.setState(IsComposing::IsComposingMessageState_Idle);
         isComposingDoc.setRefresh(0); // bliu: TODO timeout value
         IsComposingManager::processIsComposingMessageNotification(ci, isComposingDoc);
         break;
      }

   case gloox::ChatStateActive:
   default:
      break;
   }
}

void XmppChatManagerImpl::handleMessageEvent(gloox::MessageSession* session, const gloox::Message& msg, const gloox::MessageEvent& me)
{
   StackLog(<< "XmppChatManagerImpl::handleMessageEvent()");
   XmppChatInfo* ci = getChatInfoForGlooxMessageSession(session);
   if (ci == NULL)
   {
      FIRE_ERROR(0, "MessageSession not found");
      return;
   }

   if (me.event() & gloox::MessageEventDelivered)
   {
      InfoLog(<< "message delivery confirmation from " << msg.from().full() << ", id=" << me.id());

      MessageAckMap::iterator it = mMessageAckMap.find(me.id());

      if (it != mMessageAckMap.end())
      {
         InfoLog(<< "onSendMessageSuccess triggered by message delivery: message=" << it->second.second.message << ", id=" << it->first);
         fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), it->second.first, it->second.second);
         mMessageAckMap.erase(it);
      }

      MessageDeliveredEvent evt;
      evt.message = ci->mOutgoingMessages.find(me.id());
      evt.messageDeliveryStatus = MessageDeliveryStatus_Delivered;
      evt.messageId = me.id().c_str();
      evt.threadId = msg.thread().c_str();
      evt.from = msg.from().full().c_str();

      setEventTimeStamp(evt, msg);

      fireEvent(cpcFunc(XmppChatHandler::onMessageDelivered), ci->handle, evt);
   }

   if (me.event() & gloox::MessageEventDisplayed)
   {
      InfoLog(<< "message displayed confirmation from " << msg.from().full() << ", id=" << me.id());

      MessageDisplayedEvent evt;
      evt.message = ci->mOutgoingMessages.find(me.id());
      evt.messageDisplayStatus = MessageDisplayStatus_Displayed;
      evt.from = msg.from().full().c_str();
      fireEvent(cpcFunc(XmppChatHandler::onMessageDisplayed), ci->handle, evt);
   }
}

void XmppChatManagerImpl::fireError(XmppChatHandle chat, const cpc::string& errorText)
{
   LocalErrLog("XmppChatManagerImpl::fireError with errorText: {}", errorText);

   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppChat: " + errorText);
      return;
   }

   ErrorEvent evt;
   evt.errorText = errorText;
   fireEvent(cpcFunc(XmppChatHandler::onError), chat, evt);
}

void XmppChatManagerImpl::startChat(XmppChatInfo* chatInfo)
{
   StackLog(<< "XmppChatManagerImpl::startChat()");
   LocalDebugLog("XmppChatManagerImpl::startChat for chat handle: {}", chatInfo->handle);
   if (chatInfo->targetAddresses.empty())
   {
      FIRE_ERROR(chatInfo->handle, "Empty participant list");
      return;
   }

   chatInfo->mGlooxMessageSession = new gloox::MessageSession(mAccount.getGlooxClient(), chatInfo->targetAddresses.front(), true, 0 /*gloox::MessageTypeChat*/);
   chatInfo->mGlooxMessageSession->registerMessageHandler(this);
   chatInfo->mGlooxSessionEventHandler = new GlooxSessionEventHandler(*this, chatInfo->mGlooxMessageSession);
   chatInfo->mGlooxMessageEventFilter = new gloox::MessageEventFilter(chatInfo->mGlooxMessageSession);
   chatInfo->mGlooxMessageEventFilter->registerMessageEventHandler(chatInfo->mGlooxSessionEventHandler);
   chatInfo->mGlooxChatStateFilter = new gloox::ChatStateFilter(chatInfo->mGlooxMessageSession);
   chatInfo->mGlooxChatStateFilter->registerChatStateHandler(chatInfo->mGlooxSessionEventHandler);
   chatInfo->mMessageFilter = new XmppMessageFilter(chatInfo->mGlooxMessageSession, *this, chatInfo->handle);

   IsComposingManager::initialize(chatInfo);

   transitionToActiveState(chatInfo);

   // let's see what the other end supports, but only if JID have resource set
   const auto& jid = chatInfo->targetAddresses.front();
   if (jid.resource().empty()) {
      WarningLog(<< "XmppChatManagerImpl::startChat - target address is not full JID, resource part is missing: " << jid.full());
   } else {
      peerDiscovery(chatInfo, jid);
   }

   NewChatEvent evt;
   evt.account = mAccount.getHandle();
   evt.chatType = ChatType_Outgoing;
   evt.remote = chatInfo->targetAddresses.front().bare().c_str();
   fireEvent(cpcFunc(XmppChatHandler::onNewChat), chatInfo->handle, evt);
}

void XmppChatManagerImpl::endChat(XmppChatHandle handle, ChatEndReason reason)
{
   StackLog(<< "XmppChatManagerImpl::endChat()");
   LocalDebugLog("XmppChatManagerImpl::endChat for chat handle: {}, reason: {}", handle, reason);

   XmppChatInfo* chatInfo = getChatInfo(handle);

   if (chatInfo == NULL) return;

   if (validateChatStatus(chatInfo))
   {
      if (reason != ChatEndReason_UserTerminatedRemotely) chatInfo->mGlooxChatStateFilter->setChatState(gloox::ChatStateGone);

      chatInfo->mGlooxMessageSession->removeMessageHandler();
      chatInfo->mGlooxMessageEventFilter->removeMessageEventHandler();
      chatInfo->mGlooxChatStateFilter->removeChatStateHandler();

      mAccount.getGlooxClient()->disposeMessageSession(chatInfo->mGlooxMessageSession);
   }

   //delete chatInfo->mGlooxMessageSession; // deleted in disposeMessageSession()
   //delete chatInfo->mGlooxMessageEventFilter; // deleted in disposeMessageSession()
   //delete chatInfo->mGlooxChatStateFilter; // deleted in disposeMessageSession()
   //delete chatInfo->mMessageFilter; // deleted in disposeMessageSession()
   delete chatInfo->mGlooxSessionEventHandler;

   ChatEndedEvent evt;
   evt.account = chatInfo->accountHandle;
   evt.chat = chatInfo->handle;
   evt.endReason = reason;
   fireEvent(cpcFunc(XmppChatHandler::onChatEnded), chatInfo->handle, evt);

   mChatInfoMap.erase(chatInfo);
   delete chatInfo;
}

void XmppChatManagerImpl::sendMessage(XmppChatInfo* chatInfo, XmppChatMessageHandle message, const std::string& messageContent, const std::string& htmlText, const std::string& subject)
{
   StackLog(<< "XmppChatManagerImpl::sendMessage(): handle=" << message);
   assert(chatInfo);

   if (!validateChatStatus(chatInfo)) return;

   // Make sure there is a message to send
   if (messageContent.empty() && htmlText.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageFailure), chatInfo->handle, evt);
      return;
   }

   gloox::StanzaExtensionList extList;
   if (!mLegacyReceiptsEnabled)
   {
      if (mDeliveryReceiptsEnabled)
      {
         extList.push_back(new gloox::Receipt(gloox::Receipt::Request));
      }
      if (mReadReceiptsEnabled)
      {
         extList.push_back(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Markable));
      }
   }
   extList.push_back(new CpcXepMessageOrigin());

   
   cpc::string replaces = chatInfo->mMessageFilter->mReplacesMessageID.c_str();

   // Rely on content decoration to send the ID as an Origin-ID
   std::string messageId = _generate_guid();
   chatInfo->mMessageFilter->mOriginID = messageId;

   // Send the message
   chatInfo->mMessageFilter->mXHtmlContentToSend = htmlText;

   // no message ID is generated here, we use the Origin-ID
   chatInfo->mGlooxMessageSession->send(messageContent, subject, extList, messageId);
   std::string threadId = chatInfo->mGlooxMessageSession->threadID();

   InfoLog(<< "XmppChatManagerImpl::sendMessage(): handle=" << message << ", threadId=" << threadId << ", messageId=" << messageId);

   // Store information about the outgoing message
   addMessageInfo(chatInfo->handle, message, MessageType_Message, true, 0, IsComposingMessageState_Unknown);

   // Used for the 'Delivered' and 'Displayed' notifications
   chatInfo->mOutgoingMessages.add(message, messageId);

   // Update IsComposing state - IM sent
   IsComposingManager::setMessageSent(chatInfo);

   transitionToActiveState(chatInfo);

   // Fire an event at this point to indicate that we will be sending an outgoing message
   NewMessageEvent nme;
   nme.account = mAccount.getHandle();
   nme.message = message;
   nme.messageId = messageId.c_str();
   nme.threadId = threadId.c_str();
   nme.from = mAccount.getGlooxClient()->jid().bare().c_str();
   nme.to = chatInfo->mGlooxMessageSession->target().bare().c_str();
   nme.messageContent = messageContent.c_str();
   nme.htmlText = htmlText.c_str();
   nme.subject = subject.c_str();
   nme.isOutbound = true;
   nme.replaces = replaces;
   
   uint64_t ms = TimeHelper::millisSinceEpoch();
   nme.timestamp = (time_t)(ms / 1000);
   nme.millisecond = ms % 1000;
   nme.isDelayedDelivery = false;
   fireEvent(cpcFunc(XmppChatHandler::onNewOutboundMessage), chatInfo->handle, nme);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();
   evt.threadId = threadId.c_str();
   evt.replaces = replaces;

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(chatInfo->handle, evt)));
   }
   else
   {
      InfoLog(<< "onSendMessageSuccess without Stream Management support: message=" << evt.message << ", id=" << evt.messageId);
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), chatInfo->handle, evt);
   }

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   //Send data to Analytics (UEM) module as well
   static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->instantMessageInfoFired(mAccount.getHandle(), false, false);
#endif

}

void XmppChatManagerImpl::sendReaction(XmppChatInfo* chatInfo, XmppChatMessageHandle message, const std::string& target, const std::vector<std::string>& reactions)
{
   StackLog(<< "XmppChatManagerImpl::sendReaction(): handle=" << message << " target=" << target);
   assert(chatInfo);

   if (!validateChatStatus(chatInfo)) return;

   // Make sure there is a target to send to
   if (target.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageFailure), chatInfo->handle, evt);
      return;
   }

   gloox::StanzaExtensionList extList;
   if (!mLegacyReceiptsEnabled)
   {
      if (mDeliveryReceiptsEnabled)
      {
         extList.push_back(new gloox::Receipt(gloox::Receipt::Request));
      }
      if (mReadReceiptsEnabled)
      {
         extList.push_back(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Markable));
      }
   }
   
   // Send the reaction
   chatInfo->mMessageFilter->mReactionTarget = target;
   chatInfo->mMessageFilter->mReactionsToSend = reactions;
   std::string messageId = chatInfo->mGlooxMessageSession->send(std::string(), std::string(), extList);
   std::string threadId = chatInfo->mGlooxMessageSession->threadID();

   InfoLog(<< "XmppChatManagerImpl::sendReaction(): handle=" << message << ", threadId=" << threadId << ", messageId=" << messageId);

   // Store information about the outgoing message
   addMessageInfo(chatInfo->handle, message, MessageType_Message, true, 0, IsComposingMessageState_Unknown);

   // Used for the 'Delivered' and 'Displayed' notifications
   chatInfo->mOutgoingMessages.add(message, messageId);

   transitionToActiveState(chatInfo);

   // Fire an event at this point to indicate that we will be sending an outgoing message
   NewMessageEvent nme;
   nme.account = mAccount.getHandle();
   nme.message = message;
   nme.messageId = messageId.c_str();
   nme.threadId = threadId.c_str();
   nme.from = mAccount.getGlooxClient()->jid().bare().c_str();
   nme.to = chatInfo->mGlooxMessageSession->target().bare().c_str();
   nme.isOutbound = true;
   
   uint64_t ms = TimeHelper::millisSinceEpoch();
   nme.timestamp = (time_t)(ms / 1000);
   nme.millisecond = ms % 1000;
   nme.isDelayedDelivery = false;
   fireEvent(cpcFunc(XmppChatHandler::onNewOutboundMessage), chatInfo->handle, nme);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();
   evt.threadId = threadId.c_str();

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(chatInfo->handle, evt)));
   }
   else
   {
      InfoLog(<< "onSendMessageSuccess without Stream Management support: message=" << evt.message << ", id=" << evt.messageId);
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), chatInfo->handle, evt);
   }

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   //Send data to Analytics (UEM) module as well
   static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->instantMessageInfoFired(mAccount.getHandle(), false, false);
#endif

}

void XmppChatManagerImpl::sendMessageRetraction(XmppChatInfo* chatInfo, XmppChatMessageHandle message, const std::string& target)
{
   StackLog(<< "XmppChatManagerImpl::sendMessageRetraction(): handle=" << message << " target=" << target);
   assert(chatInfo);

   if (!validateChatStatus(chatInfo)) return;

   // Make sure there is a target to send to
   if (target.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageFailure), chatInfo->handle, evt);
      return;
   }

   gloox::StanzaExtensionList extList;
   if (!mLegacyReceiptsEnabled)
   {
      if (mDeliveryReceiptsEnabled)
      {
         extList.push_back(new gloox::Receipt(gloox::Receipt::Request));
      }
      if (mReadReceiptsEnabled)
      {
         extList.push_back(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Markable));
      }
   }
   
   // Send the retraction
   chatInfo->mMessageFilter->mRetractTarget = target;
   std::string messageId = chatInfo->mGlooxMessageSession->send(std::string(), std::string(), extList);
   std::string threadId = chatInfo->mGlooxMessageSession->threadID();

   InfoLog(<< "XmppChatManagerImpl::sendMessageRetraction(): handle=" << message << ", threadId=" << threadId << ", messageId=" << messageId);

   // Store information about the outgoing message
   addMessageInfo(chatInfo->handle, message, MessageType_Message, true, 0, IsComposingMessageState_Unknown);

// let's only provide delivery receipts for messages that contain content (a retraction doesn't include content)
//   // Used for the 'Delivered' and 'Displayed' notifications
//   chatInfo->mOutgoingMessages.add(message, messageId);

   transitionToActiveState(chatInfo);

   // Fire an event at this point to indicate that we will be sending an outgoing message
   NewMessageEvent nme;
   nme.account = mAccount.getHandle();
   nme.message = message;
   nme.messageId = messageId.c_str();
   nme.threadId = threadId.c_str();
   nme.from = mAccount.getGlooxClient()->jid().bare().c_str();
   nme.to = chatInfo->mGlooxMessageSession->target().bare().c_str();
   nme.isOutbound = true;
   
   uint64_t ms = TimeHelper::millisSinceEpoch();
   nme.timestamp = (time_t)(ms / 1000);
   nme.millisecond = ms % 1000;
   nme.isDelayedDelivery = false;
   fireEvent(cpcFunc(XmppChatHandler::onNewOutboundMessage), chatInfo->handle, nme);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();
   evt.threadId = threadId.c_str();

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(chatInfo->handle, evt)));
   }
   else
   {
      InfoLog(<< "onSendMessageSuccess without Stream Management support: message=" << evt.message << ", id=" << evt.messageId);
      fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), chatInfo->handle, evt);
   }

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   //Send data to Analytics (UEM) module as well
   static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->instantMessageInfoFired(mAccount.getHandle(), false, false);
#endif

}

void XmppChatManagerImpl::acceptChat(XmppChatInfo* chatInfo)
{
   StackLog(<< "XmppChatManagerImpl::acceptChat()");
   if (!validateChatStatus(chatInfo)) return;

   transitionToActiveState(chatInfo);
}

void XmppChatManagerImpl::rejectChat(XmppChatInfo* chatInfo, unsigned int reasonCode)
{
   StackLog(<< "XmppChatManagerImpl::rejectChat()");
   if (!validateChatStatus(chatInfo)) return;

   if (!chatInfo->mLastIncomingGlooxMessage) return;

   gloox::Error* error = new gloox::Error(gloox::StanzaErrorTypeCancel, gloox::StanzaErrorServiceUnavailable);
   chatInfo->mGlooxMessageSession->replyWithError(*chatInfo->mLastIncomingGlooxMessage, error);
}
//public
void XmppChatManagerImpl::notifyMessageDelivered(XmppChatInfo* chatInfo, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageDelivered()");
   if (!validateChatStatus(chatInfo)) return;

   if (!mLegacyReceiptsEnabled)
   {
      WarningLog(<< "XmppChatManagerImpl::notifyMessageDelivered: legacyChatXepSupport disabled in account settings!");
      return;
   }
   //notifyMessageStatus(chatInfo, message, origMessage, MessageType_MessageDeliveredNotification, messageDeliveryStatus);

   static_cast<XmppMessageEventFilter*>(chatInfo->mGlooxMessageEventFilter)->raiseMessageEvent(gloox::MessageEventDelivered, chatInfo->mIncomingMessages.find(message), chatInfo->mGlooxMessageSession->threadID());

// bliu: not used for now
#if 0
   auto session = chatInfo->mGlooxMessageSession;
   gloox::Message m(gloox::Message::Chat, session->target());
   m.setThread(session->threadID());
   m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Received, chatInfo->mIncomingMessages.find(message)));
   m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
   mAccount.getGlooxClient()->send(m);
#endif

}
//private
void XmppChatManagerImpl::notifyMessageDelivered(XmppChatInfo* chatInfo, const std::string& threadId, const std::string& messageId)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageDelivered() threadId=" << threadId << " messageId=" << messageId);
   if (!validateChatStatus(chatInfo)) return;

   if (mLegacyReceiptsEnabled)
   {
      //notifyMessageStatus(chatInfo, message, origMessage, MessageType_MessageDeliveredNotification, messageDeliveryStatus);

      static_cast<XmppMessageEventFilter*>(chatInfo->mGlooxMessageEventFilter)->raiseMessageEvent(gloox::MessageEventDelivered, messageId, threadId);

      // bliu: not used for now
#if 0
      auto session = chatInfo->mGlooxMessageSession;
      gloox::Message m(gloox::Message::Chat, session->target());
      m.setThread(session->threadID());
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Received, chatInfo->mIncomingMessages.find(message)));
      m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
      mAccount.getGlooxClient()->send(m);
#endif
   }
   else
   {
      if (!mDeliveryReceiptsEnabled)
      {
         WarningLog(<< "XmppChatManagerImpl::notifyMessageDelivered: enableChatDeliveryReceipts disabled in account settings");
      }
      auto session = chatInfo->mGlooxMessageSession;
      gloox::Message m(gloox::Message::Chat, session->target());
      m.setThread(threadId);
      m.addExtension(new gloox::Receipt(gloox::Receipt::Received, messageId));
      //m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
      mAccount.getGlooxClient()->send(m);
   }
}

void XmppChatManagerImpl::notifyMessageDisplayed(XmppChatInfo* chatInfo, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageDisplayed()");
   if (!validateChatStatus(chatInfo)) return;

   //notifyMessageStatus(chatInfo, message, origMessage, MessageType_MessageDisplayedNotification, messageDisplayStatus);
   chatInfo->mGlooxMessageEventFilter->raiseMessageEvent(gloox::MessageEventDisplayed, chatInfo->mIncomingMessages.find(message));
}

void XmppChatManagerImpl::notifyMessageRead(XmppChatInfo* chatInfo, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageRead(): message=" << message);
   if (!validateChatStatus(chatInfo)) return;

   if (mLegacyReceiptsEnabled)
   {
      auto session = chatInfo->mGlooxMessageSession;
      gloox::Message m(gloox::Message::Chat, session->target());
      m.setThread(session->threadID());
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Acknowledged, chatInfo->mIncomingMessages.find(message)));
      m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
      mAccount.getGlooxClient()->send(m);
   }
   else
      WarningLog(<< "XmppChatManagerImpl::notifyMessageRead: legacyChatXepSupport disabled in account settings!");
}

void XmppChatManagerImpl::notifyMessageRead(XmppChatInfo* chatInfo, const cpc::string& threadId, const cpc::string& messageId)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageRead(): threadId=" << threadId << " messageId=" << messageId);
   if (!validateChatStatus(chatInfo)) return;

   auto session = chatInfo->mGlooxMessageSession;
   gloox::Message m(gloox::Message::Chat, session->target());
   m.setThread(threadId.c_str());

   if (mLegacyReceiptsEnabled)
   {
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Acknowledged, messageId.c_str()));
      m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
   }
   else
   {
      if (!mReadReceiptsEnabled)
      {
         WarningLog(<< "XmppChatManagerImpl::notifyMessageRead: enableChatReadReceipts disabled in account settings");
      }
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Displayed, messageId.c_str()));
   }

   mAccount.getGlooxClient()->send(m);
}

void XmppChatManagerImpl::notifyMessageRead(const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageRead(): peerJid=" << peerJid << " threadId=" << threadId << " messageId=" << messageId);

   if (!mAccount.isConnected())
   {
      FIRE_ERROR(0, "XmppChatManager::notifyMessageRead() called with account=" << mAccount.getHandle() << " is not connected");
      return;
   }

   gloox::JID jid(peerJid.c_str());
   if (!jid)
   {
      FIRE_ERROR(0, "XmppChatManager::notifyMessageRead() called with invalid peerJid: " << peerJid);
      return;
   }

   gloox::Message m(gloox::Message::Chat, jid.bareJID());
   m.setThread(threadId.c_str());
   
   if (mLegacyReceiptsEnabled)
   {
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Acknowledged, messageId.c_str()));
      m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
   }
   else
   {
      if (!mReadReceiptsEnabled)
      {
         WarningLog(<< "XmppChatManagerImpl::notifyMessageRead: enableChatReadReceipts disabled in account settings");
      }
      m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Displayed, messageId.c_str()));
   }

   mAccount.getGlooxClient()->send(m);
}

void XmppChatManagerImpl::notifyMessageStatus(XmppChatInfo* chatInfo, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageType messageType, int messageStatus)
{
   StackLog(<< "XmppChatManagerImpl::notifyMessageStatus()");
   assert(chatInfo);

   if (!validateChatStatus(chatInfo)) return;

   // Get the original message info
   XmppChatMessageInfo* origMessageInfo = chatInfo->getMessageInfo(origMessage);
   assert(origMessageInfo);

   // Send a notification
   //CpimMessage cpimMessage = CpmHelper::createCpimMessage(message, fromUri, toUri, origMessage, origMessageInfo->datetimeString, messageType, messageStatus);
   //SipMsrpMessagingManager::sendMessage(chatInfo, message, cpimMessage, messageType);

   // Store information about the sent message
   addMessageInfo(chatInfo->handle, message, messageType, true, origMessage, IsComposingMessageState_Unknown);
}

void XmppChatManagerImpl::addMessageInfo(XmppChatHandle chat, XmppChatMessageHandle message, MessageType messageType, bool outgoing, XmppChatMessageHandle origMessage, IsComposingMessageState isComposingState)
{
   StackLog(<< "XmppChatManagerImpl::addMessageInfo()");
   XmppChatInfo* chatInfo = getChatInfo(chat);
   assert(chatInfo);
   XmppChatMessageInfo* messageInfo = new XmppChatMessageInfo(); // Destroyed in XmppChatInfo's destructor
   messageInfo->handle = message;
   messageInfo->type = messageType;
   messageInfo->outgoing = outgoing;
   messageInfo->origHandle = origMessage;
   messageInfo->isComposingState = isComposingState;
   chatInfo->addMessageInfo(messageInfo->handle, messageInfo);
}

void XmppChatManagerImpl::setIsComposingMessage(XmppChatInfo* chatInfo, int refreshInterval, int idleInterval)
{
   StackLog(<< "XmppChatManagerImpl::setIsComposingMessage()");
   if (!validateChatStatus(chatInfo)) return;

   tm niltime = IsComposing::IsComposingHelper::getCurrentDateTime();
   IsComposingManager::setIsComposingMessage(chatInfo, resip::Mime(), niltime, refreshInterval, idleInterval);
}

void XmppChatManagerImpl::sendIsComposingMessageNotification(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive)
{
   StackLog(<< "XmppChatManagerImpl::sendIsComposingMessageNotification()");
   XmppChatInfo* chatInfo = dynamic_cast<XmppChatInfo*>(info);

   if (!validateChatStatus(chatInfo)) return;

   if (state == IsComposing::IsComposingMessageState_Active)
   {
      if (!chatInfo->mIsActive) transitionToActiveState(chatInfo);

      chatInfo->mGlooxMessageEventFilter->raiseMessageEvent(gloox::MessageEventComposing);
      chatInfo->mGlooxChatStateFilter->setChatState(gloox::ChatStateComposing);

      if (chatInfo->mInactiveTimer != NULL) chatInfo->mInactiveTimer->cancel();
   }
   else if (state == IsComposing::IsComposingMessageState::IsComposingMessageState_Idle)
   {
      chatInfo->mGlooxMessageEventFilter->raiseMessageEvent(gloox::MessageEventCancel);
      chatInfo->mGlooxChatStateFilter->setChatState(gloox::ChatStatePaused);

      startInactiveTimer(chatInfo);
   }
}

void XmppChatManagerImpl::sendIMCommand(XmppChatInfo* chatInfo, XmppChatMessageHandle message, int type, const cpc::string& payload, const cpc::string& htmlPayload)
{
   StackLog(<< "XmppChatManagerImpl::sendIMCommand()");
   if (!validateChatStatus(chatInfo)) return;

   chatInfo->mMessageFilter->mIMCommand.reset(new CpcXepIMCommand(type, payload.c_str()));
   chatInfo->mMessageFilter->mXHtmlContentToSend = htmlPayload.c_str();

   chatInfo->mGlooxMessageSession->send("", "");

   assert(!chatInfo->mMessageFilter->mIMCommand);

   XmppIMCommand::ChatIMCommandSentEvent evt;
   evt.message = message;
   // bliu: TODO add sdk observer
   mAccount.postCallback(makeFpCommand(XmppIMCommand::XmppChatIMCommandHandler::onIMCommandSent, mIMCommandHandler, chatInfo->handle, evt));
}

void XmppChatManagerImpl::onIsComposingMessage(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive)
{
   StackLog(<< "XmppChatManagerImpl::onIsComposingMessage()");
   XmppChatInfo* chatInfo = dynamic_cast<XmppChatInfo*>(info);
   IsComposingMessageEvent evt;
   evt.state = static_cast<IsComposingMessageState>(state);
   fireEvent(cpcFunc(XmppChatHandler::onIsComposingMessage), chatInfo->handle, evt);
}

bool XmppChatManagerImpl::validateChatStatus(XmppChatInfo* chatInfo)
{
   StackLog(<< "XmppChatManagerImpl::validateChatStatus()");
   if (chatInfo->mGlooxMessageSession == NULL)
   {
      FIRE_ERROR(chatInfo->handle, "chat " << chatInfo->handle << " is either ended or not yet started");

      return false;
   }

   if (!mAccount.isConnected())
   {
      FIRE_ERROR(chatInfo->handle, "account " << mAccount.getHandle() << " is not connected for chat " << chatInfo->handle);

      return false;
   }

   return true;
}

void XmppChatManagerImpl::getChatSettings(const XmppAccount::XmppAccountSettings& settings)
{
   mSupportChatMessageStyling = settings.supportChatMessageStyling;
   mLegacyReceiptsEnabled = settings.legacyChatXepSupport;
   mDeliveryReceiptsEnabled = settings.enableChatDeliveryReceipts;
   mReadReceiptsEnabled = settings.enableChatReadReceipts;
}

void XmppChatManagerImpl::peerDiscovery(XmppChatInfo* chatInfo, const gloox::JID& peerJid)
{
   if (peerJid.resource().empty()) {
      ChatDiscoErrorEvent evt;
      evt.remoteJID = peerJid.bare().c_str();
      evt.reason = PeerDiscoErrorReason::PeerDiscoErrorReason_InvalidJid;
      fireEvent(cpcFunc(XmppChatHandler::onChatDiscoError), chatInfo->handle, evt);
   } else {
      startPeerDiscoveryTimer(chatInfo, peerJid);
      mAccount.getGlooxClient()->disco()->getDiscoInfo(peerJid, "", this, 0);
   }
}

#ifdef CPCAPI2_AUTO_TEST
void XmppChatManagerImpl::peerDiscoverySetTimeout(XmppChatInfo* chatInfo, const gloox::JID& peerJid, uint32_t timeout)
{
   DebugLog(<< "XmppChatManagerImpl::peerDiscoverySetTimeout() started");

   if (!chatInfo) return;

   auto& timers = chatInfo->mPeerDiscoveryTimers;
   const auto timerIt = timers.find(peerJid);
   if (timerIt == timers.end()) return;

   resip::DeadlineTimer<resip::MultiReactor>* timer = timerIt->second;
   if (!timer) return;

   timer->cancel();

   auto chatDiscoWaitParam = new PeerDiscoveryWaitParams({chatInfo, peerJid});
   timer->expires_from_now(timeout); // set new timeout
   timer->async_wait(&mPeerDiscoveryTimerHandler, 1, reinterpret_cast<void*>(chatDiscoWaitParam));

   DebugLog(<< "XmppChatManagerImpl::peerDiscoverySetTimeout() finished");
}
#endif

void XmppChatManagerImpl::replaceMessage(XmppChatInfo *chatInfo, XmppChatMessageHandle message, const std::string& replaces, const std::string& messageContent, const std::string& htmlText, const std::string& subject)
{
   StackLog(<< "XmppChatManagerImpl::replaceMessage(): replaces=" << replaces);

   //ror: need to revisit this, mReplaceMessageSupported should be stored on chatInfo level, not module wide:
   /*if (!mReplaceMessageSupported)
   {
      auto& jid = chatInfo->targetAddresses.front();
      WarningLog(<< "Sending replace message, but remote " << jid.full() << " doesn't support feature " << CpcXepMessageCorrection::XMLNS_MESSAGE_CORRECTION);
   }*/
   
   chatInfo->mMessageFilter->mReplacesMessageID = replaces;
   sendMessage(chatInfo, message, messageContent, htmlText, subject);
}

void XmppChatManagerImpl::transitionToActiveState(XmppChatInfo* chatInfo)
{
   StackLog(<< "XmppChatManagerImpl::transitionToActiveState()");
   chatInfo->mGlooxChatStateFilter->setChatState(gloox::ChatStateActive);
   chatInfo->mIsActive = true;
   startInactiveTimer(chatInfo);
}

void XmppChatManagerImpl::startInactiveTimer(XmppChatInfo* chatInfo)
{
   StackLog(<< "XmppChatManagerImpl::startInactiveTimer()");
   if (chatInfo->mInactiveTimer == NULL)
   {
      chatInfo->mInactiveTimer = new resip::DeadlineTimer<resip::MultiReactor>(mAccount.getAccountInterface().getReactor());
   }
   else
   {
      chatInfo->mInactiveTimer->cancel();
   }

   chatInfo->mInactiveTimer->expires_from_now(60 * 1000); // inactive state timeout
   chatInfo->mInactiveTimer->async_wait(&mInactiveTimerHandler, 1, chatInfo);
}

void XmppChatManagerImpl::startPeerDiscoveryTimer(XmppChatInfo* chatInfo, const gloox::JID& jid)
{
   StackLog(<< "XmppChatManagerImpl::startPeerDiscoveryTimer() for " << jid.full());

   const auto peerDiscoTimerIt = chatInfo->mPeerDiscoveryTimers.find(jid);
   resip::DeadlineTimer<resip::MultiReactor>* peerDiscoTimer = NULL;

   if (peerDiscoTimerIt == chatInfo->mPeerDiscoveryTimers.end())
   {
      peerDiscoTimer = new resip::DeadlineTimer<resip::MultiReactor>(mAccount.getAccountInterface().getReactor());
      chatInfo->mPeerDiscoveryTimers[jid] = peerDiscoTimer;
   }
   else
   {
      peerDiscoTimer = peerDiscoTimerIt->second;
      peerDiscoTimer->cancel();
   }

   auto chatDiscoWaitParam = new PeerDiscoveryWaitParams({chatInfo, jid});
   peerDiscoTimer->expires_from_now(30 * 1000); // peer discovery timeout
   peerDiscoTimer->async_wait(&mPeerDiscoveryTimerHandler, 1, reinterpret_cast<void*>(chatDiscoWaitParam));
}

void XmppChatManagerImpl::validateChatHandle(XmppChatHandle chat)
{
   bool valid = false;

   if (getChatInfo(chat))
   {
      valid = true;
   }

   ValidateChatHandleEvent evt;
   evt.chatHandleValid = valid;
   evt.account = mAccount.getHandle();
   fireEvent(cpcFunc(XmppChatHandler::onValidateChatHandleResult), chat, evt);
}

GlooxSessionEventHandler::GlooxSessionEventHandler(XmppChatManagerImpl& chatManager, gloox::MessageSession* session)
   : mChatManager(chatManager), mGlooxSession(session)
{
}

GlooxSessionEventHandler::~GlooxSessionEventHandler()
{
}

void GlooxSessionEventHandler::handleMessageEvent(const gloox::Message& msg, const gloox::MessageEvent& me)
{
   mChatManager.handleMessageEvent(mGlooxSession, msg, me);
}

void GlooxSessionEventHandler::handleChatState(const gloox::JID& from, gloox::ChatStateType state)
{
   mChatManager.handleChatState(mGlooxSession, from, state);
}

}
}

#endif
