#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_INTERNAL_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_INTERNAL_H

#include "cpcapi2defs.h"
#include <xmpp/XmppMultiUserChatHandler.h>

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

struct XmppMultiUserChatCreatedResultEvent
{
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc;
   CPCAPI2::XmppAccount::XmppAccountHandle account;
   cpc::string room;
};
   
/**
 * The handler for internal events on Xmpp Multi User Chat; passed in XmppAccountInterface::setInternalHandler().
*/
class XmppMultiUserChatHandlerInternal : public XmppMultiUserChatHandler
{
   
public:

   /**
    * Notifies the application when the create multi user chat request has been completed
   */
   virtual int onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc, const XmppMultiUserChatCreatedResultEvent& args) = 0;

};

} // namespace XmppMultiUserChat

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_HANDLER_INTERNAL_H
