//
//  CpcXepMessageOrigin
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_ORIGIN_H__
#define CPC_XEP_MESSAGE_ORIGIN_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0359} (unique and stable IDs for messages).
*/
class CpcXepMessageOrigin : public gloox::StanzaExtension
{
public:
   CpcXepMessageOrigin(std::string originID);
   CpcXepMessageOrigin(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   std::string originID() const { return mOriginID; }

   static const std::string XMLNS_MESSAGE_ORIGIN;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mOriginID;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_ORIGIN_H__
