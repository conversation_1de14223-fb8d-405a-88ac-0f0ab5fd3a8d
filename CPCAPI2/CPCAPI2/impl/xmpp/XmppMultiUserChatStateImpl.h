#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_IMPL_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "xmpp/XmppMultiUserChatManager.h"
#include "xmpp/XmppMultiUserChatState.h"
#include "xmpp/XmppMultiUserChatHandlerInternal.h"
#include "xmpp/XmppAccountHandlerInternal.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{

class LocalLogger;

namespace XmppMultiUserChat
{

class XmppMultiUserChatManagerInterface;

class XmppMultiUserChatStateImpl
   : public XmppMultiUserChatStateManager
   , public PhoneModule
   , public XmppMultiUserChatHandlerInternal
   , public XmppAccount::XmppAccountHandlerInternal
{

public:

   typedef std::map<XmppMultiUserChatHandle, XmppMultiUserChatState> StateMap;
   typedef std::map<XmppMultiUserChatHandle, CPCAPI2::XmppAccount::XmppAccountHandle> AccountMap;
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, cpc::vector<ServiceAvailabilityEvent>> ServiceMap;

   XmppMultiUserChatStateImpl(XmppMultiUserChatManagerInterface* intf);
   virtual ~XmppMultiUserChatStateImpl();

   // Inherited via XmppMultiUserChatStateManager
   virtual int getState(XmppMultiUserChatHandle handle, XmppMultiUserChatState& state) OVERRIDE;
   virtual int getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppMultiUserChatStateInfo>& states) OVERRIDE;
   virtual int getAllStates(cpc::vector<XmppMultiUserChatStateInfo>& states) OVERRIDE;
   virtual void Release() OVERRIDE;

   // Inherited via XmppMultiUserChatHandler
   virtual void onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& evt) OVERRIDE;
   virtual void onRoomListRetrieved(XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& evt) OVERRIDE;
   virtual void onParticipantAdded(XmppMultiUserChatHandle handle, const ParticipantAddedEvent& evt) OVERRIDE;
   virtual void onParticipantRemoved(XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& evt) OVERRIDE;
   virtual void onParticipantUpdated(XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& evt) OVERRIDE;
   virtual void onParticipantSelfUpdated(XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatReady(XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& evt) OVERRIDE;
   virtual void onMultiUserChatSubjectChanged(XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatNewMessage(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& evt) OVERRIDE;
   virtual void onMultiUserChatNewReaction(XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& evt) OVERRIDE;
   virtual void onMultiUserChatNewMessageRetraction(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageRetractionEvent& evt) OVERRIDE;
   virtual void onSendMessageSuccess(XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& evt) OVERRIDE;
   virtual void onSendMessageFailure(XmppMultiUserChatHandle handle, const SendMessageFailureEvent& evt) OVERRIDE;
   virtual void onParticipantChatStateReceived(XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& evt) OVERRIDE;
   virtual void onMultiUserChatInvitationReceived(XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatInvitationDeclined(XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatError(XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& evt) OVERRIDE;
   virtual void onLocalUserLeft(XmppMultiUserChatHandle handle, const LocalUserLeftEvent& evt) OVERRIDE;
   virtual void onMultiUserChatConfigurationRequested(XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatRoomStateChanged(XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& evt) OVERRIDE;
   virtual void onMultiUserChatListRequested(XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& evt) OVERRIDE;
   virtual void onRoomBookmarksReceived(XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& args) OVERRIDE;
   virtual void onNewRoomHandle(XmppMultiUserChatHandle handle, const NewRoomEvent& evt) OVERRIDE;

   // Inherited via XmppMultiUserChatHandlerInternal
   virtual int onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc, const XmppMultiUserChatCreatedResultEvent& args) OVERRIDE;

   // Inherited via XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;

   // Inherited via XmppAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE;
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityFeatureEvent& args) OVERRIDE;

private:

   CPCAPI2::XmppAccount::XmppAccountHandle getAccountHandle(XmppMultiUserChatHandle vcard);
   cpc::vector<XmppMultiUserChatHandle> getMultiUserChatHandles(CPCAPI2::XmppAccount::XmppAccountHandle account);
   int getServiceMappingForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<ServiceAvailabilityEvent>& services);
   int getMucCount(CPCAPI2::XmppAccount::XmppAccountHandle account);
   int getStateCount();
   int getServiceCount();

   StateMap mStateMap;
   AccountMap mAccountMap;
   ServiceMap mServiceMap;
   XmppMultiUserChatManagerInterface* mInterface;
   LocalLogger* mLocalLogger;

};

}

}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_STATE_IMPL_H
