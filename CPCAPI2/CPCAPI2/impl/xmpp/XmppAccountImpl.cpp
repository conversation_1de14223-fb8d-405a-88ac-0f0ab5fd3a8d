// bliu: note
// connect() will operate immediately, i.e. no post()
// disconnect() will operate asynchronously, i.e. post()
// connect() will cancel mConnectTimer
// reconnection timeout will be either determined amount for XmppConnection failure or a various amount for other failures
// if connected or connecting, connect() has no effect
// if disconnected, disable() still fires disconnecting and disconnected events
// multiple connect() will fire multiple connecting events but only one connected event
// multiple disconnect() will fire multiple disconnecting and disconnected events
// there is one minor potential timing issue that onDisconnect() and connect() happen at the same time but startPostDisconnectSequence() concludes the end result
// mReconnectionCount and mResource will not be reset in cleanup()
// cleanup other modules in case resumption fails or times out
// prevent the app layer from rejoining muc after resumed with new Resuming and Resumed account status

#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include "XmppAccountImpl.h"
#include "XmppTimeHelper.h"
#include "XmppPrivacyImpl.h"
#include "CpcXep.h"
#include "CpcXepEntityTime.h"
#include "CpcXepNotification.h"
#include "XmppCommon.h"
#include <cpcapi2utils.h>
#include <disco.h>
#include <error.h>
#include <delayeddelivery.h>
#include <vcardupdate.h>
#include <xmpp/XmppAccountHandler.h>
#include "util/DumFpCommand.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"
#include "util/ReactorHelpers.h"
#include <rutil/Logger.hxx>
#include <rutil/Socket.hxx>
#include <rutil/Random.hxx>
#include <resip/stack/Uri.hxx>
#include "XmppAccountHandlerInternal.h"
#include "analytics1/AnalyticsManagerInterface.h"
#include "json/JsonHelper.h"
#include <regex>

#include "CpcXepUserActivity.h"

#ifdef USE_SSL
#include "util/GlooxTlsClient.h"
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT

template<typename T>
bool operator ==(const cpc::vector<T>& lhs, const cpc::vector<T>& rhs)
{
   if (lhs.size() != rhs.size()) return false;

   for (size_t i = 0; i < lhs.size(); ++i)
   {
      if (lhs[i] != rhs[i]) return false;
   }

   return true;
}

template<typename T>
bool operator !=(const cpc::vector<T>& lhs, const cpc::vector<T>& rhs)
{
   return !(lhs == rhs);
}

static resip::SecurityTypes::SSLType getSSLType(CPCAPI2::XmppAccount::SSLVersion sslVersion)
{
   switch (sslVersion)
   {
   case CPCAPI2::XmppAccount::TLS_DEFAULT:          return resip::SecurityTypes::TLS_HIGHEST;
   case CPCAPI2::XmppAccount::SSL_NONE:             return resip::SecurityTypes::NoSSL;
   case CPCAPI2::XmppAccount::SSL_V2:               return resip::SecurityTypes::SSLv2;
   case CPCAPI2::XmppAccount::SSL_V3:               return resip::SecurityTypes::SSLv3;
   case CPCAPI2::XmppAccount::TLS_V1_0:             return resip::SecurityTypes::TLSv1_0;
   case CPCAPI2::XmppAccount::TLS_V1_1:             return resip::SecurityTypes::TLSv1_1;
   case CPCAPI2::XmppAccount::TLS_V1_2:             return resip::SecurityTypes::TLSv1_2;
   case CPCAPI2::XmppAccount::TLS_V1_3:             return resip::SecurityTypes::TLSv1_3;
   case CPCAPI2::XmppAccount::SSL_HIGHEST:          return resip::SecurityTypes::TLS_HIGHEST;
   case CPCAPI2::XmppAccount::TLS_NON_DEPRECATED:   return resip::SecurityTypes::TLS_NON_DEPRECATED;
   }
   return resip::SecurityTypes::NoSSL;
}

#ifdef CPCAPI2_AUTO_TEST
// this is used by unit tests to validate the ACK handling
extern "C" 
{
   int xmppUnrequestedAckCount = 0;
}
#endif

namespace CPCAPI2
{

namespace XmppAccount
{

enum DiscoContext
{
   InitialInfo,
   InitialItems,
   CustomItemsInfo
};

enum TimerType
{
   Ping,
   Connect,
   Disco,
   Resume,
   UnrequestedAck
};

const std::string XMLNS_CP_PRIVATEDATA = "urn:counterpath:private-data";
const std::string XMLNS_XEP_0352 = "urn:xmpp:csi:0";

#ifdef _WIN32
std::atomic<XmppAccountHandle> XmppAccountImpl::sNextAccountHandle = 1;
#else
std::atomic<XmppAccountHandle> XmppAccountImpl::sNextAccountHandle = ATOMIC_VAR_INIT(1);
#endif

static XmppAccount::Error fromGlooxConnectionErrorCode(gloox::ConnectionError e);
static cpc::string toErrorText(XmppAccount::Error e);
static const char* toRestrictionString(XmppAccount::Restriction restriction);

// specialization for XmppAccountHandlerInternal which cannot be handled by mAppHandler
template<>
void XmppAccountImpl::fireEvent<int (XmppAccountHandlerInternal::*)(XmppAccountHandle, const XmppAccountConfiguredEvent&)>(const char* funcName, int (XmppAccountHandlerInternal::*func)(XmppAccountHandle, const XmppAccountConfiguredEvent&), const XmppAccountConfiguredEvent& args)
{
   for (std::list<XmppAccountHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
   {
      resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, args);
      if (dynamic_cast<XmppAccountSyncHandler*>(*it) != NULL)
      {
         (*cb)();
         delete cb;
      }
      else
      {
         postCallback(cb);
      }
   }
}

template<>
void XmppAccountImpl::fireEvent<int (XmppAccountHandlerInternal::*)(XmppAccountHandle, const XmppAccountEnabledEvent&)>(const char* funcName, int (XmppAccountHandlerInternal::*func)(XmppAccountHandle, const XmppAccountEnabledEvent&), const XmppAccountEnabledEvent& args)
{
   for (std::list<XmppAccountHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
   {
      resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, args);
      if (dynamic_cast<XmppAccountSyncHandler*>(*it) != NULL)
      {
         (*cb)();
         delete cb;
      }
      else
      {
         postCallback(cb);
      }
   }
}

template<>
void XmppAccountImpl::fireEvent<int (XmppAccountHandlerInternal::*)(XmppAccountHandle, const XmppAccountDisabledEvent&)>(const char* funcName, int (XmppAccountHandlerInternal::*func)(XmppAccountHandle, const XmppAccountDisabledEvent&), const XmppAccountDisabledEvent& args)
{
   for (std::list<XmppAccountHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
   {
      resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, args);
      if (dynamic_cast<XmppAccountSyncHandler*>(*it) != NULL)
      {
         (*cb)();
         delete cb;
      }
      else
      {
         postCallback(cb);
      }
   }
}

XmppAccountImpl::XmppAccountImpl(XmppAccountHandle h, const XmppAccountSettings& settings, resip::Fifo<resip::ReadCallbackBase>* callbackFifo, const std::function<void(void)>& cbHook, PhoneInterface* phone, XmppAccountInterface& accountIf, const std::list<XmppAccountHandlerInternal*>& initialObservers)
   : mHandle(h)
   , mAppHandler(NULL)
   , mSettings(settings)
   , mPendingSettings(settings)
   , mCallbackFifo(callbackFifo)
   , mCbHook(cbHook)
   , mPhone(phone)
   , mAccountIf(accountIf)
   , mClient(NULL)
   , mPrivateStorage(NULL)
   , mPrivacy(NULL)
   , mPingTimer(accountIf.getReactor())
   , mConnectTimer(accountIf.getReactor())
   , mDiscoTimer(accountIf.getReactor())
   , mUnrequestedAckTimer(accountIf.getReactor())
   , mTotalStanzasReceived(0)
   , mReconnectCount(0)
   , mSdkObservers(initialObservers)
   , mStatus(XmppAccountStatusChangedEvent::Status_Disconnected)
   , mResume(false)
   , mStreamManagementInfo(NULL)
   , mResumeTimer(accountIf.getReactor())
{
   registerDiscoObserver(this);

   mRestrictions.insert(UserDisabledRestriction);

   StackLog(<< "XmppAccountImpl(): " << this << " user: " << settings.username << " domain: " << settings.domain << " initial observers size: " << initialObservers.size());

   if (settings.username.empty() && settings.domain.empty())
   {
      StackLog(<< "XmppAccountImpl(): " << this << " jid not configured yet");
   }
   else
   {
      XmppAccountConfiguredEvent evt;
      evt.settings = settings;
      fireEvent(cpcFunc(XmppAccountHandlerInternal::onAccountConfigured), evt);
   }
}

XmppAccountImpl::~XmppAccountImpl()
{
   StackLog(<< "XmppAccountImpl::~XmppAccountImpl()");
   unregisterDiscoObserver(this);

   cleanup();
}

void XmppAccountImpl::post(resip::ReadCallbackBase* cmd)
{
   mAccountIf.getReactor().post(cmd);
}

void XmppAccountImpl::postCallback(resip::ReadCallbackBase* fp)
{
   StackLog(<< "XmppAccountImpl postCallback mAppHandler: " << mAppHandler << " fp: " << fp);
   mCallbackFifo->add(fp);
   if (mCbHook) { mCbHook(); }
}

void XmppAccountImpl::setHandler(XmppAccountHandler* handler)
{
   StackLog(<< "XmppAccountImpl::setHandler(): handler: " << handler);
   mAppHandler = handler;
}

void XmppAccountImpl::fireError(const cpc::string& errorText)
{
   ErrLog(<< "Firing XmppAccountImpl error: " << errorText);

   if (mAppHandler == NULL)
   {
      PhoneErrorEvent evt;
      evt.errorText = errorText;
      mPhone->fireEvent(cpcEvent(PhoneHandler, onError), "XmppAccountInterface", evt);
      mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), "XmppAccountInterface", evt);
   }
   else
   {
      ErrorEvent evt;
      evt.errorText = errorText;
      fireEvent(cpcFunc(XmppAccountHandler::onError), evt);
   }
}

void XmppAccountImpl::fireXmppError(const XmppAccount::Error errorCode)
{
   ErrorEvent evt;
   evt.errorText = toErrorText(errorCode);
   fireEvent(cpcFunc(XmppAccountHandler::onError), evt);
}

void XmppAccountImpl::registerAccountObserver(XmppAccountObserver* observer)
{
   assert(std::find(mAccountObservers.begin(), mAccountObservers.end(), observer) == mAccountObservers.end());
   StackLog(<< "XmppAccountImpl::registerAccountObserver(): add XmppAccountObserver observer: " << observer << " to list of size: " << mAccountObservers.size());
   mAccountObservers.push_back(observer);
}

void XmppAccountImpl::unregisterAccountObserver(XmppAccountObserver* observer)
{
   // http://en.wikipedia.org/wiki/Erase-remove_idiom
   StackLog(<< "XmppAccountImpl::unregisterAccountObserver(): remove XmppAccountObserver observer: " << observer << " from list of size: " << mAccountObservers.size());
   mAccountObservers.erase(std::remove(mAccountObservers.begin(), mAccountObservers.end(), observer), mAccountObservers.end());
}

void XmppAccountImpl::addSdkObserver(XmppAccountHandlerInternal* handler)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), handler) != mSdkObservers.end())
   {
      StackLog(<< "XmppAccountImpl::addSdkObserver(): XmppAccountHandlerInternal handler: " << handler << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppAccountImpl::addSdkObserver(): XmppAccountHandlerInternal handler: " << handler);
   mSdkObservers.push_back(handler);
}

void XmppAccountImpl::removeSdkObserver(XmppAccountHandlerInternal* handler)
{
   std::list<XmppAccountHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), handler);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppAccountImpl::removeSdkObserver(): XmppAccountHandlerInternal handler: " << handler << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppAccountImpl::applySettings()
{
   DebugLog(<< "applySettings");

#define IS_SETTING_CHANGED(field) (mSettings.field != mPendingSettings.field)

   bool isChanged =
      IS_SETTING_CHANGED(username)
      || IS_SETTING_CHANGED(domain)
      || IS_SETTING_CHANGED(password)
      || IS_SETTING_CHANGED(proxy)
      || IS_SETTING_CHANGED(port)
      || IS_SETTING_CHANGED(resource)
      || IS_SETTING_CHANGED(priority)
      || IS_SETTING_CHANGED(softwareName)
      || IS_SETTING_CHANGED(softwareVersion)
      || IS_SETTING_CHANGED(softwareOS)
      || IS_SETTING_CHANGED(identityCategory)
      || IS_SETTING_CHANGED(identityType)
      || IS_SETTING_CHANGED(connectTimeOut)
      || IS_SETTING_CHANGED(connectRandomInterval)
      || IS_SETTING_CHANGED(keepAliveTime)
      || IS_SETTING_CHANGED(usePingKeepAlive)
      || IS_SETTING_CHANGED(fileTransfileProxies)
      || IS_SETTING_CHANGED(enableLocalSocks5Proxy)
      || IS_SETTING_CHANGED(enableRemoteStreamHostDiscovery)
      || IS_SETTING_CHANGED(httpFileUploadTransferRate)
      || IS_SETTING_CHANGED(sslVersion)
      || IS_SETTING_CHANGED(cipherSuite)
      || IS_SETTING_CHANGED(ignoreCertVerification)
      || IS_SETTING_CHANGED(additionalCertPeerNames)
      || IS_SETTING_CHANGED(acceptedCertPublicKeys)
      || IS_SETTING_CHANGED(requiredCertPublicKeys)
      || IS_SETTING_CHANGED(logXmppStanzas)
      || IS_SETTING_CHANGED(ipVersion)
      || IS_SETTING_CHANGED(nameServers)
      || IS_SETTING_CHANGED(additionalNameServers)
      || IS_SETTING_CHANGED(enableStreamManagement)
      || IS_SETTING_CHANGED(enableStreamResumption)
      || IS_SETTING_CHANGED(maxStreamResumptionTimeout)
      || IS_SETTING_CHANGED(publishInitialPresenceAsAvailable)
      || IS_SETTING_CHANGED(fallbackOnResourceConflict)
      || IS_SETTING_CHANGED(enableCompression)
      || IS_SETTING_CHANGED(enableXmppPresence)
      || IS_SETTING_CHANGED(enableXmppStanza)
      || IS_SETTING_CHANGED(unrequestedAckSendIntervalSec)
      || IS_SETTING_CHANGED(enableSeeOtherHost)
      || IS_SETTING_CHANGED(logTlsEncryptionKey);

   if (isChanged)
   {
      mSettings = mPendingSettings;

      InfoLog(<< "reset session data due to applySettings");
      mReconnectCount = 0;
      mResource.clear();
      mSeeOtherHost.clear();

      checkUnrequestedAck();  // (re)start timer as needed

      // only re-enable if the account is enabled
      if (isEnabled()) disconnect(true);
   }

   // fire onAccountConfigured event? Yes!
   XmppAccountConfiguredEvent evt;
   evt.settings = mSettings;
   fireEvent(cpcFunc(XmppAccountHandlerInternal::onAccountConfigured), evt);

   // bliu: this is the only event cannot be fired with makeFpCommand() and postCallback() idiom
}

void XmppAccountImpl::destroy(bool force)
{
   InfoLog(<< "XmppAccountImpl::destroy(): " << this << " handle=" << mHandle << " username=" << mSettings.username);

   if (isEnabled() && (force == false))
   {
      ErrLog(<< "destroy(): cannot delete account as it is still enabled");
      fireError(toErrorText(XmppAccount::Error_NotDisconnected));
      return;
   }

   if (force)
   {
      // Avoid contacting observers during forced shutdown
      mSdkObservers.clear();
   }

   XmppAccountObservers copy = mAccountObservers;
   for (XmppAccountObservers::reverse_iterator it = copy.rbegin(), rend = copy.rend(); it != rend; ++it)
   {
      (*it)->onDestroy(*this);
   }

   // Destroy requires that disable has already been called for the account
   //
   // 1) Disable triggers addRestriction that triggers disconnect, i.e. startDisconnectSequence
   //
   // 2) Disconnect sequence includes calls to performWillDisconnectTask, removeConnectionListener and disconnect, disconnect also triggers cleanup
   //
   // 3) Cleanup cancels the timers (ping, connect, disco), clears privacy and private storeage, gloox client, server-features, resets tls info
   //
   // 4) Destructor calls unregisterDiscoObserver(), cleanup() and deletes the stack log
   //
   fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Destroyed);

   // delete this; // Not required as default deletion expected when the xmpp interface releases this impl, due to reference count.
}

void XmppAccountImpl::setPresence(XmppRoster::PresenceType presence, const cpc::string& msg, int priority)
{
   updatePresence(presence, msg, priority);

   mClient->setPresence();
}

void XmppAccountImpl::updatePresence(XmppRoster::PresenceType presence, const cpc::string& msg, int priority)
{
   // bliu: do NOT create a new presence instance and assign it to mClient->presence() because the assignment operator is not overloaded
   mClient->presence().setPresence(static_cast<gloox::Presence::PresenceType>(presence));
   mClient->presence().setPriority(priority);
   mClient->presence().resetStatus();
   mClient->presence().addStatus(msg.c_str());

   // clean up previous activity
   mClient->presence().removeExtension(EXepUserActivity);
}

void XmppAccountImpl::publishPresence(XmppRoster::PresenceType presence, const cpc::string& msg, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   if (!isConnected()) return;

   mPrivacy->setMyPresence(presence, msg, userActivityGeneralType, userActivitySpecificType, userActivityText);

   updatePresence(presence, msg, mSettings.priority);

   // DCM: This feature is deprecated since XEP-0108 specifies activity SHOULD NOT be included inside presence
   // https://xmpp.org/extensions/xep-0108.html#proto-pubsub
   if (userActivityGeneralType != XmppRoster::ActivityInvalidGeneralType || userActivitySpecificType != XmppRoster::ActivityInvalidSpecificType)
   {
      CpcXepUserActivity* userActivity = new CpcXepUserActivity();
      if (userActivityGeneralType != XmppRoster::ActivityInvalidGeneralType) userActivity->setGeneralType(static_cast<CpcXepUserActivity::GeneralType>(userActivityGeneralType));
      if (userActivitySpecificType != XmppRoster::ActivityInvalidSpecificType) userActivity->setSpecificType(static_cast<CpcXepUserActivity::SpecificType>(userActivitySpecificType));
      if (!userActivityText.empty()) userActivity->setText(userActivityText.c_str());

      mClient->presence().addExtension(userActivity);
   }

   mClient->setPresence();
}

void XmppAccountImpl::publishCannedPresence(XmppRoster::XmppCannedStatus status, const cpc::string& note)
{
   assert(status != XmppRoster::XmppCannedStatus_Invalid);

   if (!isConnected()) return;

   if (status == XmppRoster::XmppCannedStatus_Invisible)
   {
      if (!mServerFeatures.count(EntityFeatureEvent::Privacy))
      {
         fireError("server doesn't support privacy when publishing XmppCannedStatus_Invisible");
         return;
      }

      mPrivacy->setInvisible(true);
      return;
   }

   mPrivacy->setInvisible(false);

   XmppRoster::PresenceType presence;
   XmppRoster::UserActivityGeneralType userActivityGeneralType;
   XmppRoster::UserActivitySpecificType userActivitySpecificType;
   cpc::string userActivityText;

   XmppRoster::XmppCannedPresence::cannedStatusToPresence(status, presence, userActivityGeneralType, userActivitySpecificType, userActivityText);

   publishPresence(presence, note, userActivityGeneralType, userActivitySpecificType, userActivityText);
}

void XmppAccountImpl::blockIncomingPresence(bool block)
{
   if (!isConnected()) return;

   mPrivacy->setPresenceInDeny(block);
}

void XmppAccountImpl::setHibernationState(bool active)
{
   if (!isConnected()) return;

   if (!mClient->getOptionalStreamFeature("mobile", "http://tigase.org/protocol/mobile#v3"))
   {
      InfoLog(<< "server doesn't support Tigase Mobile Optimization");
      return;
   }

   std::stringstream ss;
   ss << "<iq type=\"set\" id=\""
      << mClient->getID() << "\">"
      << "<mobile xmlns=\"http://tigase.org/protocol/mobile#v3\" enable=\""
      << (active ? "true" : "false")
      << "\"/>"
      << "</iq>";

   mClient->send(ss.str());
}

void XmppAccountImpl::getEntityTime(const cpc::string& jid)
{
   if (!isConnected()) return;

   if (!isDiscoCompleted())
   {
      mAccountIf.getReactor().postMS(resip::resip_bind(&XmppAccountImpl::getEntityTime, shared_from_this(), jid), 200);
      return;
   }

   std::string _jid = jid.c_str();

   if (_jid.empty())
   {
      if (!mServerFeatures.count(EntityFeatureEvent::EntityTime))
      {
         fireError("Server does not supported entity time");
         return;
      }

      _jid = mClient->server();
   }

   StackLog(<< "XmppAccountImpl::getEntityTime(): jid: " << jid);
   gloox::IQ iq (gloox::IQ::Get, _jid, mClient->getID());

   iq.addExtension(new CpcXepEntityTime());

   mClient->send(iq);
}

void XmppAccountImpl::enableNotification(const cpc::string& node, const XmppDataForm& dataform)
{
   if (!isConnected()) return;

   if (!isDiscoCompleted())
   {
      mAccountIf.getReactor().postMS(resip::resip_bind(&XmppAccountImpl::enableNotification, shared_from_this(), node, dataform), 200);
      return;
   }

   if (!mServerFeatures.count(EntityFeatureEvent::Notification))
   {
      fireError("Server does not supported push notification");
      return;
   }

   if (node.empty())
   {
      fireError("Node is required to enable push notification");
      return;
   }

   StackLog(<< "XmppAccountImpl::enableNotification(): node: " << node);
   gloox::DataForm form (gloox::FormType::TypeSubmit);

   if (!dataform.fields.empty()) // bliu: data form <reported> is not used in XEP-0357
   {
      XmppCommon::convert(dataform, form);

      if (!form.hasField("FORM_TYPE"))
      {
         form.addField(gloox::DataFormField::TypeNone, "FORM_TYPE", "http://jabber.org/protocol/pubsub#publish-options");
      }
   }

   gloox::IQ iq (gloox::IQ::Set, gloox::JID());
   iq.addExtension(new CpcXepNotification(true, mClient->jid(), node.c_str(), form));
   mClient->send(iq);
}

void XmppAccountImpl::disableNotification(const cpc::string& node)
{
   if (!isConnected()) return;

   if (!isDiscoCompleted())
   {
      mAccountIf.getReactor().postMS(resip::resip_bind(&XmppAccountImpl::disableNotification, shared_from_this(), node), 200);
      return;
   }

   if (!mServerFeatures.count(EntityFeatureEvent::Notification))
   {
      fireError("Server does not supported push notification");
      return;
   }

   StackLog(<< "XmppAccountImpl::disableNotification(): node: " << node);
   gloox::IQ iq (gloox::IQ::Set, gloox::JID());
   iq.addExtension(new CpcXepNotification(false, mClient->jid(), node.c_str()));
   mClient->send(iq);
}

void XmppAccountImpl::getPrivateStorageData()
{
   if (!isConnected()) return;

   if (!isDiscoCompleted())
   {
      mAccountIf.getReactor().postMS(resip::resip_bind(&XmppAccountImpl::getPrivateStorageData, shared_from_this()), 200);
      return;
   }

   mPrivateStorage->requestXML("storage", XMLNS_CP_PRIVATEDATA, this);
}

void XmppAccountImpl::setPrivateStorageData(const cpc::vector<XmppStorageData>& data)
{
   if (!isConnected()) return;

   if (!isDiscoCompleted())
   {
      mAccountIf.getReactor().postMS(resip::resip_bind(&XmppAccountImpl::setPrivateStorageData, shared_from_this(), data), 200);
      return;
   }

   gloox::Tag* tag = new gloox::Tag("storage");
   tag->setXmlns(XMLNS_CP_PRIVATEDATA);

   {
      gloox::Tag* revision = new gloox::Tag("revision");

      uint64_t t = TimeHelper::millisSinceEpoch() / 1000;
      tm* tt = TimeHelper::gmtime(&t);
      char buffer[64];
      memset(buffer, 0, sizeof(buffer));

      /*size_t size = */std::strftime(buffer, sizeof(buffer) - 1, "%Y%m%dT%H:%M:%SZ", tt);

      revision->addAttribute("timestamp", buffer);
      revision->addAttribute("resource", mResource);
      revision->addAttribute("address", mCurrentInterfaceIPAddress.c_str());

      tag->addChild(revision);
   }

   for (auto& i : data)
   {
      gloox::Tag* item = new gloox::Tag("data");
      item->addAttribute("name", i.name.c_str());
      item->addCData(i.value.c_str());
      tag->addChild(item);
   }

   mPrivateStorage->storeXML(tag, this);
}

void XmppAccountImpl::send(const cpc::string& xml)
{
   if (!isConnected()) return;

   mClient->send(xml.c_str());
}

void XmppAccountImpl::startConnectSequence()
{
   // stop mResumeTimer so it won't be fired during connecting stage to avoid force disconnection
   mResumeTimer.cancel();

   mResume ? resumeGlooxClient() : startGlooxClient();
}

void XmppAccountImpl::startDisconnectSequence(bool reconnect)
{
   // bliu: disconnecting and disconnected events to be fired unconditionally even with !isConnected()
   fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Disconnecting);

   if (isConnected())
   {
      performWillDisconnectTask();

      if (mSettings.enableStreamManagement)
      {
         mClient->ackStreamManagement();
      }

      outputStats();

      mClient->removeConnectionListener(this);
      mClient->disconnect();

      performDidDisconnectTask();

      InfoLog(<< "Disconnected locally: handle=" << mHandle);

      cleanup();
   }

   fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Disconnected);

   if (reconnect) this->reconnect(2000); // bliu: actual reconnection depends on restrictions
}

// this is mimic to resumeGlooxClient, make sure to update both if necessary
void XmppAccountImpl::startGlooxClient()
{
   assert(!isConnected());

   {
      std::string str;
      Json::StdStringBuffer buffer(str);
      Json::StdStringWriter writer(buffer);
      writer.StartObject();
      Json::Write(writer, "XmppAccountSettings", mSettings);
      writer.EndObject();
      DebugLog(<< "XmppAccountSettings mHandle=" << mHandle << ": " << std::regex_replace(str, std::regex("(\"password\"):(\"[^\"]*\")"), "$1:XXXXX"));
   }

   std::string jidStr = (mSettings.username + "@" + mSettings.domain).c_str();
   DebugLog(<< "startGlooxClient: jid: " << jidStr);

   if (!mSettings.resource.empty())
   {
      jidStr += ("/" + mSettings.resource).c_str();
      InfoLog(<< "Use resource in settings: " << mSettings.resource);
   }
   else if (!mResource.empty())
   {
      jidStr += "/" + mResource;
      InfoLog(<< "Use resource from previous successful login session: " << mResource);
   }
   else
   {
      InfoLog(<< "No resource specified");
   }

   gloox::JID jid(jidStr);
   std::string password = mSettings.password.c_str();
   mClient = new gloox::Client(jid, password);
   mStackLog = new XmppStackLog(mSettings.logXmppStanzas, mPhone->localLogger());
   mClient->logInstance().registerLogHandler(gloox::LogLevelDebug, gloox::LogAreaAll, mStackLog);
   mClient->registerConnectionListener(this);
   mClient->registerIqHandler(this, EXepEntityTime);

   mClient->setServer(mSettings.domain.c_str());

   XmppConnectionTcpClient::DnsSettings dnsSettings;
   populateNameServer(dnsSettings);

   std::string server = mSettings.proxy.empty() ? mSettings.domain.c_str() : mSettings.proxy.c_str();
   unsigned short port = mSettings.port;

   if (!mSeeOtherHost.empty())
   {
      auto semicolon = mSeeOtherHost.find_first_of(';');
      resip::Uri seeOtherHost(("see-other-host:" + (semicolon == std::string::npos ? mSeeOtherHost : mSeeOtherHost.substr(0, semicolon))).c_str());
      mSeeOtherHost = mSeeOtherHost.erase(0, semicolon == std::string::npos ? mSeeOtherHost.size() : semicolon + 1);
      if (!seeOtherHost.host().empty()) server = seeOtherHost.host().c_str();
      if (seeOtherHost.port() > 0) port = seeOtherHost.port();
   }

   XmppConnectionTcpClient* connection = new XmppConnectionTcpClient(mAccountIf.getReactor(), mClient, mClient->logInstance(), server, port, mSettings.ipVersion, mSettings.connectTimeOut, XMPP_CLIENT_SERVICE_NAME, resip::TCP, dnsSettings);
   mClient->setConnectionImpl(connection);

   mClient->setCompression(mSettings.enableCompression);

   mClient->setStreamManagement(mSettings.enableStreamManagement, mSettings.enableStreamResumption, mSettings.maxStreamResumptionTimeout);

   // bliu: set initial presence
   if (!mSettings.publishInitialPresenceAsAvailable) setPresence(XmppRoster::PresenceType_Unknown, "", -1);

#ifdef USE_SSL
   SslCipherOptions sslOptions = mPhone->getSslCipherOptions();
   resip::SecurityTypes::SSLType sslType = (TLS_DEFAULT == mSettings.sslVersion) ? getSSLType((SSLVersion)sslOptions.getTLSVersion(SslCipherUsageXmpp)) : getSSLType(mSettings.sslVersion);
   CipherSuite cipherSuite = (mSettings.cipherSuite.empty()) ? sslOptions.getCiphers(SslCipherUsageXmpp) : mSettings.cipherSuite;

   if (sslType == resip::SecurityTypes::NoSSL)
   {
      ErrLog(<< "SSL/TLS is disabled");
   }
   else
   {
      gloox::TLSBase* tlsClientImpl = new GlooxTlsClient(mClient, mClient->server(), sslType, cipherSuite.c_str());
      tlsClientImpl->setInitLib(false);
      if (tlsClientImpl->init(EmptyString, EmptyString, StringList(), cipherSuite.c_str(), mSettings.logTlsEncryptionKey))
      {
         mClient->setEncryptionImpl(tlsClientImpl);
      }
      else
      {
         ErrLog(<< "Error initializing TLS handler for XMPP");
         delete tlsClientImpl;
      }
   }
#else
   DebugLog(<< "Use default TLS");
#endif

   if (mSettings.port > 0)
   {
      // NOTE: gloox disables DNS SRV lookup if port is set
      mClient->setPort(mSettings.port);
   }

   mClient->addOptionalStreamFeature("mobile", "http://tigase.org/protocol/mobile#v3");
   mClient->addOptionalStreamFeature("csi", XMLNS_XEP_0352);

   mPrivateStorage = new gloox::PrivateXML(mClient);

   mPrivacy = new XmppPrivacy::XmppPrivacyImpl(*this);

   DebugLog(<< "XmppAccountImpl::startGlooxClient(): trigger performWillConnectTask");
   performWillConnectTask();

   if (!mClient->connect(false))
   {
      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Failure, XmppAccount::Error_ConnectionRefused);
   }
}

// this is mimic from startGlooxClient, make sure to update both if necessary
void XmppAccountImpl::resumeGlooxClient()
{
   assert(isConnected());

   std::string jidStr = (mSettings.username + "@" + mSettings.domain).c_str();
   DebugLog(<< "resumetGlooxClient: jid: " << jidStr);

   XmppConnectionTcpClient::DnsSettings dnsSettings;
   populateNameServer(dnsSettings);

   auto oldConnection = mClient->connectionImpl();
   assert(oldConnection != NULL);

   XmppConnectionTcpClient* connection = new XmppConnectionTcpClient(mAccountIf.getReactor(), mClient, mClient->logInstance(), oldConnection->server(), oldConnection->port(), mSettings.ipVersion, mSettings.connectTimeOut, XMPP_CLIENT_SERVICE_NAME, resip::TCP, dnsSettings);
   mClient->setConnectionImpl(connection);

#ifdef USE_SSL
   SslCipherOptions sslOptions = mPhone->getSslCipherOptions();
   resip::SecurityTypes::SSLType sslType = (TLS_DEFAULT == mSettings.sslVersion) ? getSSLType((SSLVersion)sslOptions.getTLSVersion(SslCipherUsageXmpp)) : getSSLType(mSettings.sslVersion);
   CipherSuite cipherSuite = (mSettings.cipherSuite.empty()) ? sslOptions.getCiphers(SslCipherUsageXmpp) : mSettings.cipherSuite;

   if (sslType == resip::SecurityTypes::NoSSL)
   {
      ErrLog(<< "SSL/TLS is disabled");
   }
   else
   {
      gloox::TLSBase* tlsClientImpl = new GlooxTlsClient(mClient, mClient->server(), sslType, cipherSuite.c_str());
      tlsClientImpl->setInitLib(false);
      if (tlsClientImpl->init(EmptyString, EmptyString, StringList(), cipherSuite.c_str(), mSettings.logTlsEncryptionKey))
      {
         mClient->setEncryptionImpl(tlsClientImpl);
      }
      else
      {
         ErrLog(<< "Error initializing TLS handler for XMPP");
         delete tlsClientImpl;
      }
   }
#else
   DebugLog(<< "Use default TLS");
#endif

   // do NOT fire failure event in case of failure
   mClient->connect(false);
}

void XmppAccountImpl::startPostConnectSequence()
{
   ping();
   checkUnrequestedAck();

   mConnectTimer.cancel();
   mReconnectCount = 0; // reset if connected
   mTotalStanzasReceived = 0;

   const resip::Tuple& target = static_cast<XmppConnectionTcpClient*>(mClient->connectionImpl())->target(); // bliu: always present if connected
   IpHelpers::getPreferredLocalIpAddress(target, mCurrentInterfaceIPAddress);
   InfoLog(<< "Connected to " << target << " via network interface " << mCurrentInterfaceIPAddress);

   if (!mResume)
   {
      // service discovery
      mClient->disco()->getDiscoInfo(mClient->server(), gloox::EmptyString, this, DiscoContext::InitialInfo);
      mDiscoSet.insert(DiscoContext::InitialInfo);

      // TODO: what if the resumption starts before the disco procedure completes? should the timer be adjusted accordingly?
      // this is unlikely and the timer is actually optional anyways
      mDiscoTimer.cancel();
      mDiscoTimer.expires_from_now(10 * 1000); // 10 seconds max however disco result still counts afterwards
      mDiscoTimer.async_wait(this, TimerType::Disco, NULL);

      performDidConnectTask();

      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Connected);
   }
   else
   {
      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Resumed);
   }

   mResumeTimer.cancel();
   mResume = false;
}

void XmppAccountImpl::startPostDisconnectSequence(gloox::ConnectionError e, gloox::StreamError se, const std::string& streamErrorData)
{
   // bliu: avoid multiple onDisconnect() caused by resource conflict or send() error
   if (mStatus == XmppAccountStatusChangedEvent::Status_Failure)
   {
      InfoLog(<< "Account is already disconnected: mHandle=" << mHandle);
      return;
   }
   else if (mStatus == XmppAccountStatusChangedEvent::Status_Disconnecting)
   {
      // we are probably already executing XmppAccountImpl::startDisconnectSequence
      // further up the call stack, so just let that method take care of cleanup;
      // else below we'll destroy mClient and potentially queue up reconnects, which
      // can conflict with what XmppAccountImpl::startDisconnectSequence is already
      // trying to do

      DebugLog(<< "Account is already disconnecting: mHandle=" << mHandle);
      return;
   }

   XmppAccount::Error error = fromGlooxConnectionErrorCode(e);

   mResume = (error == XmppAccount::Error_IoError) && isStreamManagementEnabled() && mStreamManagementInfo->resume;

   DebugLog(<< "XmppAccountImpl::startPostDisconnectSequence: mHandle=" << mHandle << " mResume="<< mResume << " gloox::error= " << e << " error=" << error);

   if (mResume)
   {
      mReconnectCount = 0;

      // if resume timer is faster than reconnect, the reconnection will be interrupted as expected
      mResumeTimer.expires_from_now(std::min<int>(std::max<int>(0, mStreamManagementInfo->max), 600) * 1000); // max at 600 seconds
      mResumeTimer.async_wait(this, TimerType::Resume, NULL);

      reconnect((mReconnectCount + 1) * 5000 + resip::Random::getRandom() % (mSettings.connectRandomInterval * 1000));
      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Resuming, error);

      return;
   }

   if (isConnected())
   {
      performDidDisconnectTask();

      outputStats();
   }

   // no more access to mClient afterwards
   cleanup();

   switch (error)
   {
   // bliu: retry for recoverable failures
   case XmppAccount::Error_StreamError:
      InfoLog(<< "Gloox stream error: handle=" << mHandle << " error=" << se);

      if (se == gloox::StreamErrorSeeOtherHost && mSettings.enableSeeOtherHost)
      {
         InfoLog(<< "Reconnect immediately with see-other-host: " << streamErrorData);

#if 0 // bliu: avoid circular and infinite see-other-host
         if (mSeeOtherHost.empty()) mSeeOtherHost = streamErrorData;
         else mSeeOtherHost += ';' + streamErrorData;
#else
         mSeeOtherHost = streamErrorData;
#endif

         if (!mSeeOtherHost.empty())
         {
            // re-try immediately
            reconnect(0);

            break;
         }
      }

      if (se == gloox::StreamErrorConflict)
      {
         InfoLog(<< "Resource conflict while connected");

         if (!mSettings.resource.empty() || !mSettings.fallbackOnResourceConflict)
         {
            InfoLog(<< "Reconnection will not be attempted to avoid circular kicking - resource in settings: " << mSettings.resource << ", fallback: " << mSettings.fallbackOnResourceConflict);
            break;
         }

         mResource.clear();
      }

   case XmppAccount::Error_IoError:
   case XmppAccount::Error_DnsError:
   //case XmppAccount::Error_HostNotFound: // XmppConnection specific error
   case XmppAccount::Error_ConnectionRefused:
   case XmppAccount::Error_NotConnected:
   case XmppAccount::Error_ParseError:
      InfoLog(<< "Reconnection shall proceed after disconnection with error=" << error);

      // reconnect immediately for see-other-host
      if (!mSeeOtherHost.empty())
      {
         reconnect(0);
         break;
      }

      reconnect((mReconnectCount >= 12 ? 12 : (mReconnectCount + 1)) * 5000 + resip::Random::getRandom() % (mSettings.connectRandomInterval * 1000));
      break;

   case XmppAccount::Error_None:
   case XmppAccount::Error_NoHandlerSet:
   case XmppAccount::Error_AlreadyEnabled:
   case XmppAccount::Error_NotEnabled:
   case XmppAccount::Error_AlreadyConnected: // not used
   case XmppAccount::Error_TlsFailed:
   case XmppAccount::Error_CompressionFailed:
   case XmppAccount::Error_UnsupportedAuthMech:
   case XmppAccount::Error_AuthenticationFailed:
   default:
      InfoLog(<< "Reconnection shall not proceed after disconnection with error=" << error);
      break;
   }

   fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Failure, error);
}

void XmppAccountImpl::performWillConnectTask()
{
#if 0
   extern int FIPS_mode(void); // from #include <openssl/crypto.h>

   if (FIPS_mode())
   {
      gloox::SaslMechanism saslMech = (gloox::SaslMechanism)(gloox::SaslMechAll ^ gloox::SaslMechDigestMd5);
      mClient->setSASLMechanisms(saslMech);
   }
#endif

   // setup discovery and capability
   mClient->disco()->setVersion(mSettings.softwareName.c_str(), mSettings.softwareVersion.c_str(), mSettings.softwareOS.c_str());
   mClient->disco()->setIdentity(mSettings.identityCategory.c_str(), mSettings.identityType.c_str());

   mClient->registerStanzaExtension(new CpcXepUserActivity());
   mClient->registerStanzaExtension(new CpcXepEntityTime());

   StackLog(<< "XmppAccountImpl::performWillConnectTask(): XmppAccountObserver observer list size: " << mAccountObservers.size());
   for (XmppAccountObservers::iterator it = mAccountObservers.begin(), end = mAccountObservers.end(); it != end; ++it)
   {
      StackLog(<< "XmppAccountImpl::performWillConnectTask(): trigger onWillConnect on XmppAccountObserver: " << (*it));
      (*it)->onWillConnect(*this);
   }
}

void XmppAccountImpl::performDidConnectTask()
{
   for (XmppAccountObservers::iterator it = mAccountObservers.begin(), end = mAccountObservers.end(); it != end; ++it)
   {
      (*it)->onDidConnect(*this);
   }
}

void XmppAccountImpl::performWillDisconnectTask()
{
   for (XmppAccountObservers::reverse_iterator it = mAccountObservers.rbegin(), rend = mAccountObservers.rend(); it != rend; ++it)
   {
      (*it)->onWillDisconnect(*this);
   }
}

void XmppAccountImpl::performDidDisconnectTask()
{
   for (XmppAccountObservers::reverse_iterator it = mAccountObservers.rbegin(), rend = mAccountObservers.rend(); it != rend; ++it)
   {
      (*it)->onDidDisconnect(*this);
   }
}

void XmppAccountImpl::fireAccountStatusChange(XmppAccountStatusChangedEvent::Status accountStatus, XmppAccount::Error errorCode)
{
   mStatus = accountStatus;

   XmppAccountStatusChangedEvent evt;
   evt.accountStatus = accountStatus;
   evt.errorCode = errorCode;
   evt.errorText = toErrorText(errorCode);

   if (accountStatus == XmppAccountStatusChangedEvent::Status_Connected ||
       accountStatus == XmppAccountStatusChangedEvent::Status_Failure)
   {
      evt.tlsInfo = mTlsConnInfo;
   }

   if (isConnected() && mClient->connectionImpl() != NULL)
   {
      auto remote = static_cast<XmppConnectionTcpClient*>(mClient->connectionImpl())->target();

      std::ostringstream oss;
      oss << remote.presentationFormat() << ":" << remote.getPort();
      evt.remote = oss.str().c_str();
   }

   fireEvent(cpcFunc(XmppAccountHandler::onAccountStatusChanged), evt);

#if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
   //Send data to Analytics (UEM) module as well
   if (!mPhone->isReleasing()) static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->xmppAccountStatusChangeFired(mHandle, evt);
#endif

}

void XmppAccountImpl::outputStats()
{
   assert(isConnected());

   gloox::StatisticsStruct stats = mClient->getStatistics();
   DebugLog(
      << "I/O stats: in=" << stats.totalBytesReceived
      << ", out=" << stats.totalBytesSent
      << ", encryption=" << (stats.encryption?"true":"false")
      << ", compression=" << (stats.compression?"true":"false")
      << ", retries=" << mReconnectCount);
}

void XmppAccountImpl::cleanup()
{
   mPingTimer.cancel();
   mConnectTimer.cancel();
   mDiscoTimer.cancel();
   mUnrequestedAckTimer.cancel();

   delete mPrivacy;
   mPrivacy = NULL;

   delete mPrivateStorage;
   mPrivateStorage = NULL;

   if (isConnected())
   {
      mClient->logInstance().removeLogHandler(mStackLog);

      // bliu: need this for all pending calls referring mClient/XmppConnection in the reactor
      mClient->setConnectionImpl(NULL);
      mAccountIf.getReactor().postMS(resip::resip_static_bind(resip::default_delete<gloox::Client>, mClient), 3000);
      mClient = NULL;

      // bliu: mStackLog is not much necessary in deletion of mClient
      delete mStackLog;
      mStackLog = NULL;
   }

   mDiscoSet.clear();

   mServerFeatures.clear();

   mTlsConnInfo = XmppTLSConnectionInfo();

   // only reset due to user intervention or change of settings
   //mReconnectCount = 0;
   //mResource.clear();
   //mSeeOtherHost.clear();

   mCurrentInterfaceIPAddress = resip::Data::Empty;

   mResume = false;

   delete mStreamManagementInfo;
   mStreamManagementInfo = NULL;

   mResumeTimer.cancel();
}

void XmppAccountImpl::ping()
{
   if (!isConnected())
   {
      InfoLog(<< "XmppAccount is not connected hence stop pinging");
      return; // no need for ping if not connected
   }

   DebugLog(<< "Settings:usePingKeepAlive=" << mSettings.usePingKeepAlive << ", keepAliveTime=" << mSettings.keepAliveTime);
   mSettings.usePingKeepAlive && mServerFeatures.count(EntityFeatureEvent::Ping) ? mClient->xmppPing(gloox::JID(""), NULL) : mClient->whitespacePing();
   mClient->reqStreamManagement();

   mPingTimer.cancel();
   mPingTimer.expires_from_now(mSettings.keepAliveTime > 0 ? mSettings.keepAliveTime * 1000 : 3000);
   mPingTimer.async_wait(this, TimerType::Ping, NULL);
}

void XmppAccountImpl::checkUnrequestedAck()
{
   if (!isConnected())
   {
      InfoLog(<< "XmppAccount is not connected hence not checking for unrequested ACK");
      return;
   }

   DebugLog(<< "Settings:unrequestedAckSendIntervalSec=" << mSettings.unrequestedAckSendIntervalSec);

   mUnrequestedAckTimer.cancel();
   if (mSettings.unrequestedAckSendIntervalSec == 0 || !mSettings.enableStreamManagement)
   {
      DebugLog(<< "No unrequested ACK handling configured");
      return;
   }

   gloox::StatisticsStruct stats = mClient->getStatistics();
   DebugLog(<< "Stanza's received: " << stats.totalStanzasReceived);
   if (stats.totalStanzasReceived && stats.totalStanzasReceived != mTotalStanzasReceived)
   {
#ifdef CPCAPI2_AUTO_TEST
      xmppUnrequestedAckCount++;
#endif
      DebugLog(<< "Sending unrequested ACK");
      mClient->ackStreamManagement();
   }
   mTotalStanzasReceived = stats.totalStanzasReceived;
      
   mUnrequestedAckTimer.expires_from_now(mSettings.unrequestedAckSendIntervalSec * 1000);
   mUnrequestedAckTimer.async_wait(this, TimerType::UnrequestedAck, NULL);
}

void XmppAccountImpl::connect()
{
   if (mAppHandler == NULL)
   {
      ErrLog(<< "connect(): mAppHandler not set");
      fireError(toErrorText(XmppAccount::Error_NoHandlerSet));
      return;
   }

   if (!mRestrictions.empty())
   {
      InfoLog(<< "connect(): some restriction is in effect");

      if (mRestrictions.find(NetworkRestriction) != mRestrictions.end())
      {
         fireError("Restricted network");
      }

      if (mRestrictions.find(UserDisabledRestriction) != mRestrictions.end())
      {
         fireError("Disabled by user");
      }

      return;
   }

   if (!mResume)
   {
      if (isConnected())
      {
         ErrLog(<< "connect(): already connected");
         fireError(toErrorText(XmppAccount::Error_AlreadyConnected));
         return;
      }

      cleanup();

      // resume will fire resuming and resumed instead of connecting and connected
      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Connecting);
   }

   startConnectSequence();
}

void XmppAccountImpl::disconnect(bool reconnect)
{
   DebugLog(<< "XmppAccount disconnect with reconnect=" << reconnect);

   post(resip::resip_bind(&XmppAccountImpl::startDisconnectSequence, shared_from_this(), reconnect));
}

void XmppAccountImpl::reconnect(int milliseconds)
{
   DebugLog(<< "Reconnect: handle=" << mHandle << " timeout=" << milliseconds << "ms");

   mConnectTimer.cancel();
   mConnectTimer.expires_from_now(milliseconds);
   mConnectTimer.async_wait(this, TimerType::Connect, NULL);

   ++mReconnectCount;
}

void XmppAccountImpl::onConnect()
{
   InfoLog(<< "Connected: handle=" << mHandle);

   startPostConnectSequence();
}

void XmppAccountImpl::onDisconnect(gloox::ConnectionError e)
{
   InfoLog(<< "Disconnected: handle=" << mHandle << " gloox error=" << e);

   if (!isConnected()) return;

   post(resip::resip_bind(&XmppAccountImpl::startPostDisconnectSequence, shared_from_this(), e, mClient->streamError(), mClient->streamErrorCData()));
}

void XmppAccountImpl::onResourceBind(const std::string& resource)
{
   // bliu: record the binded resource and this value will be used during next connect()
   InfoLog(<< "Resource binding: " << resource);

   mResource = resource;
}

void XmppAccountImpl::onResourceBindError(const gloox::Error* error)
{
   InfoLog(<< "Resource binding error: handle=" << mHandle << " gloox error=" << (error == NULL ? "" : error->text()));

   mResource.clear();

   disconnect(mSettings.fallbackOnResourceConflict); // bliu: won't reconnect to avoid infinite resource bind error
}

void XmppAccountImpl::onSessionCreateError(const gloox::Error* error)
{
   InfoLog(<< "Session creation error: handle=" << mHandle << " gloox error=" << (error == NULL ? "" : error->text()));

   disconnect(true);
}

bool XmppAccountImpl::onTLSConnect(const gloox::CertInfo& info)
{
   bool certVerified = (gloox::CertOk == info.status);
   bool retVal = (certVerified || mSettings.ignoreCertVerification);

   DebugLog(<< "TLS info - version: " << info.protocol);

   mTlsConnInfo.certificateStatus = info.status;

   // by default Gloox is only matching against mSettings.domain, but
   // the customer might configure additional DNS names which are valid, so
   // we need to check those as well
   if (info.status == gloox::CertWrongPeer)
   {
      std::list<std::string>::const_iterator it = info.peerNames.begin();
      for (; it != info.peerNames.end(); ++it)
      {
         cpc::vector<cpc::string>::const_iterator itAddtlNames = mSettings.additionalCertPeerNames.begin();
         for (; itAddtlNames != mSettings.additionalCertPeerNames.end(); ++itAddtlNames)
         {
            if (it->compare(itAddtlNames->c_str()) == 0)
            {
               //certVerified = true;
               retVal = true;
               mTlsConnInfo.certificateStatus = gloox::CertOk;
               break;
            }
         }
      }
   }

   if (info.status != gloox::CertOk)
   {
      cpc::vector<cpc::string>::const_iterator itAccKeys = mSettings.acceptedCertPublicKeys.begin();
      for (; itAccKeys != mSettings.acceptedCertPublicKeys.end(); ++itAccKeys)
      {
         if (info.pubKey.compare(itAccKeys->c_str()) == 0)
         {
            //certVerified = true;
            retVal = true;
            mTlsConnInfo.certificateStatus = gloox::CertOk;
            break;
         }
      }
   }

   bool passedPinnedCertCheck = (mSettings.requiredCertPublicKeys.size() == 0);
   if (mSettings.requiredCertPublicKeys.size() > 0)
   {
      cpc::vector<cpc::string>::const_iterator itPubKeys = mSettings.requiredCertPublicKeys.begin();
      for (; itPubKeys != mSettings.requiredCertPublicKeys.end(); ++itPubKeys)
      {
         if (info.pubKey.compare(itPubKeys->c_str()) == 0)
         {
            passedPinnedCertCheck = true;
            break;
         }
      }
   }

   if (!passedPinnedCertCheck)
   {
      //certVerified = false;
      retVal = false;
      mTlsConnInfo.certificateStatus |= XmppTLSConnectionInfo::CertificateStatus_WrongPubKey;
   }

   mTlsConnInfo.cipher = info.cipher.c_str();
   mTlsConnInfo.compression = info.compression.c_str();
   mTlsConnInfo.issuer = info.issuer.c_str();
   mTlsConnInfo.mac = info.mac.c_str();
   std::list<std::string>::const_iterator itPeers = info.peerNames.begin();
   for (; itPeers != info.peerNames.end(); ++itPeers)
   {
      mTlsConnInfo.peerNames.push_back(itPeers->c_str());
   }
   mTlsConnInfo.protocol = info.protocol.c_str();
   mTlsConnInfo.publicKey = info.pubKey.c_str();
   mTlsConnInfo.server = info.server.c_str();

   if (retVal)
   {
      if (mSettings.ignoreCertVerification)
      {
         DebugLog(<< "TLS certificate validation passed via ignoreCertVerification setting; continuing with XMPP connection");
      }
      else
      {
         DebugLog(<< "TLS certificate validation passed; continuing with XMPP connection");
      }
   }
   else
   {
      ErrLog(<< "TLS certificate validation failed - status: " << info.status
             << ", passedPinnedCertCheck: " << passedPinnedCertCheck 
             << ". Gloox will now abort the connection.");
   }

   return retVal;
}

void XmppAccountImpl::onStreamEvent(gloox::StreamEvent event)
{
   StackLog(<< "onStreamEvent: " << event);

   switch (event)
   {
   case StreamEventSMEnabled:
      InfoLog(<< "stream management enable succeeded: handle=" << mHandle);

      if (mStreamManagementInfo != NULL)
      {
         delete mStreamManagementInfo;
      }

      mStreamManagementInfo = new gloox::Client::StreamManagementInfo(mClient->getStreamManagementInfo());

      break;

   case StreamEventSMEnableFailed:
      InfoLog(<< "stream management enable failed: handle=" << mHandle);
      delete mStreamManagementInfo;
      mStreamManagementInfo = NULL;
      break;

   case StreamEventSMResumed:
      InfoLog(<< "stream resumed: handle=" << mHandle);
      break;

   case StreamEventSMResumeFailed:
      InfoLog(<< "stream resumption failed: handle=" << mHandle);
      break;

   case StreamEventSMAck:
      for (XmppAccountObservers::iterator it = mAccountObservers.begin(), end = mAccountObservers.end(); it != end; ++it)
      {
         (*it)->onStreamManagementAck(*this);
      }

      break;

   default:
      break;
   }
}

// bliu: three possibilities for announcing onDiscoCompleted:
// 1. initial getDiscoInfo() fails
// 2. no disco items
// 3. all disco items have completed their getDiscoInfo() with either success or failure
void XmppAccountImpl::handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context)
{
   mDiscoSet.erase(context);

   // 0 for initial getDiscoInfo() upon connected
   if (context == DiscoContext::InitialInfo)
   {
      if (info.hasFeature(gloox::XMLNS_DISCO_ITEMS))
      {
         mClient->disco()->getDiscoItems(mClient->server(), gloox::EmptyString, this, DiscoContext::InitialItems);
         mDiscoSet.insert(InitialItems);
      }
   }

   bool completed = isDiscoCompleted();

   for (DiscoObservers::iterator it = mDiscoObservers.begin(), end = mDiscoObservers.end(); it != end; ++it)
   {
      (*it)->onXmppDiscoInfo(from, info);

      if (completed) (*it)->onXmppDiscoCompleted();
   }

   if (completed) mDiscoTimer.cancel();
}

void XmppAccountImpl::handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context)
{
   mDiscoSet.erase(context);

   if (context != DiscoContext::InitialItems) return;

   int i = DiscoContext::CustomItemsInfo; // to distinguish from initial getDiscoInfo() and getDiscoItems()

   for (gloox::Disco::ItemList::const_iterator it = items.items().begin(), end = items.items().end(); it != end; ++it)
   {
      mClient->disco()->getDiscoInfo((*it)->jid(), gloox::EmptyString, this, i);

      mDiscoSet.insert(i++);
   }

   if (!isDiscoCompleted()) return;

   for (DiscoObservers::iterator it = mDiscoObservers.begin(), end = mDiscoObservers.end(); it != end; ++it)
   {
      (*it)->onXmppDiscoCompleted();
   }

   mDiscoTimer.cancel();
}

void XmppAccountImpl::handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context)
{
   InfoLog(<< "Disco error: handle=" << mHandle << " from=" << from.full() << " gloox error=" << (error == NULL ? "" : error->text()));

   mDiscoSet.erase(context);

   if (!isDiscoCompleted()) return;

   for (DiscoObservers::iterator it = mDiscoObservers.begin(), end = mDiscoObservers.end(); it != end; ++it)
   {
      (*it)->onXmppDiscoCompleted();
   }
}

bool XmppAccountImpl::handleDiscoSet(const gloox::IQ& iq)
{
   return gloox::DiscoHandler::handleDiscoSet(iq);
}

void XmppAccountImpl::handlePrivateXML(const gloox::Tag* xml)
{
   if (xml == NULL) return;

   PrivateStorageDataEvent evt;

   for (auto& i : xml->children())
   {
      if (i->name() == "revision")
      {
         InfoLog(<< "revision: timestamp=" << i->findAttribute("timestamp") << ", resource=" << i->findAttribute("resource") << ", address=" << i->findAttribute("address"));
         continue;
      }

      if (i->name() != "data") continue;

      XmppStorageData item;
      item.name = i->findAttribute("name").c_str();

      if (i->hasAttribute("value")) item.value = i->findAttribute("value").c_str(); // bliu: for backward compatibility
      else item.value = i->cdata().c_str();

      if (!item.name.empty()) evt.data.push_back(item);
   }

   fireEvent(cpcFunc(XmppAccountHandler::onPrivateStorageData), evt);
}

void XmppAccountImpl::handlePrivateXMLResult(const std::string& uid, gloox::PrivateXMLHandler::PrivateXMLResult pxResult)
{
   // bliu: TODO
}

bool XmppAccountImpl::handleIq(const gloox::IQ& iq)
{
   const CpcXepEntityTime* entityTime = iq.findExtension<CpcXepEntityTime>(EXepEntityTime);
   if (entityTime == NULL) return false;

   switch (iq.subtype())
   {
   case gloox::IQ::Result:
   {
      EntityTimeEvent evt;
      evt.errorCode = 0;
      evt.from = iq.from().full().c_str();
      evt.timestamp = 0;
      evt.millisecond = 0;

      if (TimeHelper::to_time(entityTime->timestamp(), evt.timestamp, evt.millisecond))
      {
         fireEvent(cpcFunc(XmppAccountHandler::onEntityTime), evt);
      }
      else
      {
         fireError("invalid entity time");
      }

      return true;
   }

   case gloox::IQ::Get:
   {
      CpcXepEntityTime* entityTime = new CpcXepEntityTime();
      entityTime->setTime();

      gloox::IQ newIq (gloox::IQ::Result, iq.from(), iq.id());
      newIq.addExtension(entityTime);

      mClient->send(newIq);

      return true;
   }

   case gloox::IQ::Error:
   {
      EntityTimeEvent evt;
      evt.timestamp = 0;
      evt.millisecond = 0;
      evt.errorCode = iq.error()->error();
      evt.from = iq.from().full().c_str();

      fireEvent(cpcFunc(XmppAccountHandler::onEntityTime), evt);

      return true;
   }

   default: return false;
   }
}

void XmppAccountImpl::handleIqID(const gloox::IQ& iq, int context)
{
   // bliu: not used yet
}

void XmppAccountImpl::registerDiscoObserver(XmppDiscoObserver* observer)
{
   if (observer == NULL)
   {
      assert(false);
      return;
   }

   if (std::find(mDiscoObservers.begin(), mDiscoObservers.end(), observer) != mDiscoObservers.end()) return;

   mDiscoObservers.push_back(observer);
}

void XmppAccountImpl::unregisterDiscoObserver(XmppDiscoObserver* observer)
{
   mDiscoObservers.erase(std::remove(mDiscoObservers.begin(), mDiscoObservers.end(), observer), mDiscoObservers.end());
}

bool XmppAccountImpl::isEnabled() const
{
   return mRestrictions.find(UserDisabledRestriction) == mRestrictions.end();
}

bool XmppAccountImpl::isDiscoCompleted() const
{
   return mDiscoSet.empty();
}

bool XmppAccountImpl::isStreamManagementEnabled() const
{
   return mStreamManagementInfo != NULL;
}

void XmppAccountImpl::onTimer(unsigned short timerId, void* appState)
{
   switch (timerId)
   {
   case TimerType::Ping:
      ping(); // setup for next ping
      break;

   case TimerType::Connect:
      connect();
      break;

   case TimerType::Disco:
      if (!isConnected()) break;
      if (isDiscoCompleted()) break;

      mDiscoSet.clear();

      for (DiscoObservers::iterator it = mDiscoObservers.begin(), end = mDiscoObservers.end(); it != end; ++it)
      {
         (*it)->onXmppDiscoCompleted();
      }

      break;

   case TimerType::Resume:
      // this part is ignored in startPostDisconnectSequence() when mResume = true
      if (isConnected())
      {
         performDidDisconnectTask();

         outputStats();
      }

      cleanup();

      fireAccountStatusChange(XmppAccountStatusChangedEvent::Status_Failure, Error_NotConnected);

      break;

   case TimerType::UnrequestedAck:
      checkUnrequestedAck();
      break;

   default:
      assert(false);
   }
}

void XmppAccountImpl::populateNameServer(XmppConnectionTcpClient::DnsSettings& dnsSettings) const
{
   const XmppAccountSettings acctSettings = getSettings();
   dnsSettings.includeSystemDnsServers = false;

   if (acctSettings.nameServers.size() > 0)
   {
      for (cpc::vector<cpc::string>::const_iterator it = acctSettings.nameServers.begin(); it != acctSettings.nameServers.end(); ++it)
      {
         resip::Tuple t(it->c_str(), 53, resip::UDP);
         dnsSettings.nameServers.push_back(t.toGenericIPAddress());
      }
   }
   else if (acctSettings.additionalNameServers.size() > 0)
   {
      dnsSettings.includeSystemDnsServers = true;
      for (cpc::vector<cpc::string>::const_iterator it = acctSettings.additionalNameServers.begin(); it != acctSettings.additionalNameServers.end(); ++it)
      {
         resip::Tuple t(it->c_str(), 53, resip::UDP);
         dnsSettings.nameServers.push_back(t.toGenericIPAddress());
      }
   }
}

const bool XmppAccountImpl::isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const
{
   return mRestrictedNetworks.find(currentNetworkTransport) != mRestrictedNetworks.end();
}

void XmppAccountImpl::addRestriction(Restriction restriction)
{
   DebugLog(<< "XmppAccountImpl::addRestriction: handle=" << mHandle << " restriction=" << toRestrictionString(restriction));

   bool restricted = !mRestrictions.empty();

   mRestrictions.insert(restriction);

   bool disconnected = false;
   // bliu: only fire disconnecting event if the account is not restricted or the new restriction is issued by user
   if (restricted)
   {
      if (restriction == UserDisabledRestriction)
      {
         disconnect(false);
         disconnected = true;
      }
   }
   else
   {
      if (restriction == NetworkRestriction && mResume)
      {
         // cause Failure account status
         simulateNetworkLoss();
         return;
      }

      disconnect(false);
      disconnected = true;
   }

   if (disconnected)
   {
      XmppAccountDisabledEvent evt;
      fireEvent(cpcFunc(XmppAccountHandlerInternal::onAccountDisabled), evt);
   }
}

void XmppAccountImpl::removeRestriction(Restriction restriction)
{
   DebugLog(<< "XmppAccountImpl::removeRestriction: handle=" << mHandle << " restriction=" << toRestrictionString(restriction));

   mRestrictions.erase(restriction);

   // reset due to user intervention
   mReconnectCount = 0;

   if (restriction == NetworkRestriction)
   {
      // if mResume is true, reconnect timer is already running
      // and we don't want to disconnect explicitly to keep the gloox client alive
      if (mResume)
      {
         InfoLog(<< "Trying to resume stream after network restriction removed");
         return;
      }
      // mCurrentInterfaceIPAddress can only be assigned in startPostConnectSequence() so mConnection is always valid.
      // an empty mCurrentInterfaceIPAddress means the account might be still connecting using old network interface.
      // it wouldn't hurt too much to force reconnection even with the preferred network interface been chosen.
      if (mCurrentInterfaceIPAddress.empty())
      {
         InfoLog(<< "Not yet connected hence reconnection is enforced");
         disconnect(true);
         return;
      }

      // rr: not sure if this is the right check, do we really have a gloox client instance at this point?
      assert(isConnected());

      const resip::Tuple& target = static_cast<XmppConnectionTcpClient*>(mClient->connectionImpl())->target(); // bliu: always present if connected

      resip::Data newInterfaceAddress;
      IpHelpers::getPreferredLocalIpAddress(target, newInterfaceAddress);
      DebugLog(<< "target: " << target << ", preferred interface: " << newInterfaceAddress << ", old interface: " << mCurrentInterfaceIPAddress);

      if (newInterfaceAddress != mCurrentInterfaceIPAddress)
      {
         InfoLog(<< "Network interface switch to " << newInterfaceAddress << " from " << mCurrentInterfaceIPAddress);
         disconnect(true); // disconnect() will lead to reconnect automatically
         return;
      }

      // leave with the only case that the account is connected with the same valid mCurrentInterfaceIPAddress
      InfoLog(<< "Network interface switch is not required");
   }
   else if (restriction == UserDisabledRestriction)
   {
      InfoLog(<< "reset session data due to user intervention");

      XmppAccountEnabledEvent evt;
      evt.settings = mSettings;
      fireEvent(cpcFunc(XmppAccountHandlerInternal::onAccountEnabled), evt);

      mResource.clear();
   }

   reconnect(2000);

   ping(); // immediate ping to detect possible connection loss
}

void XmppAccountImpl::simulateNetworkLoss()
{
   if (!isConnected()) return;

   auto connection = static_cast<XmppConnectionTcpClient*>(mClient->connectionImpl());

#ifdef CPCAPI2_AUTO_TEST

#ifdef _WIN32
   ::shutdown(connection->socket(), SD_BOTH);
#else
   ::shutdown(connection->socket(), SHUT_WR);
#endif

   ping();
#endif

}

void XmppAccountImpl::setInactive(bool inactive)
{
   if (!isConnected()) return;

   if (!mClient->getOptionalStreamFeature("csi", XMLNS_XEP_0352))
   {
      InfoLog(<< "server doesn't support XEP-0352 Client State Indication");
      return;
   }

   std::ostringstream oss;
   oss << "<" << (inactive ? "inactive" : "active") << " xmlns=\"urn:xmpp:csi:0\" />";
   mClient->send(oss.str());
}

void XmppAccountImpl::onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info)
{
   // bliu: TODO support entities other than server

   if (from != mClient->server()) return;

   if (info.hasFeature(CpcXepEntityTime::XMLNS_ENTITY_TIME))
   {
      mServerFeatures.insert(EntityFeatureEvent::EntityTime);
   }

   if (info.hasFeature(gloox::XMLNS_XMPP_PING))
   {
      mServerFeatures.insert(EntityFeatureEvent::Ping);
   }

   if (info.hasFeature(gloox::XMLNS_PRIVACY))
   {
      mServerFeatures.insert(EntityFeatureEvent::Privacy);
   }

   if (info.hasFeature(CpcXepNotification::XMLNS_NOTIFICATION))
   {
      mServerFeatures.insert(EntityFeatureEvent::Notification);
   }
   
   if (info.hasFeature(gloox::XMLNS_ADHOC_COMMANDS))
   {
      mServerFeatures.insert(EntityFeatureEvent::AdhocCommands);
   }
}

void XmppAccountImpl::onXmppDiscoCompleted()
{
   EntityFeatureEvent evt;

   // bliu: do NOT assign evt.entity to indicate a server entity

   std::copy(mServerFeatures.begin(), mServerFeatures.end(), std::back_inserter(evt.features));

   fireEvent(cpcFunc(XmppAccountHandler::onEntityFeature), evt);
}

static const char* toRestrictionString(XmppAccount::Restriction restriction)
{
   switch (restriction)
   {
      case UserDisabledRestriction:
         return "UserDisabledRestriction";
      case NetworkRestriction:
         return "NetworkRestriction";
      default:
         return "Unknown!";
   }
}

static XmppAccount::Error fromGlooxConnectionErrorCode(gloox::ConnectionError e)
{
   switch (e)
   {
   case gloox::ConnStreamError:
      return XmppAccount::Error_StreamError;
   case gloox::ConnStreamVersionError:
      return XmppAccount::Error_StreamError;
   case gloox::ConnStreamClosed:
      return XmppAccount::Error_StreamError;
   case gloox::ConnProxyAuthRequired:
      return XmppAccount::Error_AuthenticationFailed;
   case gloox::ConnProxyAuthFailed:
      return XmppAccount::Error_AuthenticationFailed;
   case gloox::ConnProxyNoSupportedAuth:
      return XmppAccount::Error_AuthenticationFailed;
   case gloox::ConnIoError:
      return XmppAccount::Error_IoError;
   case gloox::ConnParseError:
      return XmppAccount::Error_ParseError;
   case gloox::ConnConnectionRefused:
      return XmppAccount::Error_ConnectionRefused;
   case gloox::ConnDnsError:
      return XmppAccount::Error_DnsError;
   case gloox::ConnOutOfMemory:
      return XmppAccount::Error_IoError; // gloox never return this error code!
   case gloox::ConnNoSupportedAuth:
      return XmppAccount::Error_UnsupportedAuthMech;
   case gloox::ConnTlsFailed:
      return XmppAccount::Error_TlsFailed;
   case gloox::ConnTlsNotAvailable:
      return XmppAccount::Error_TlsFailed;
   case gloox::ConnCompressionFailed:
      return XmppAccount::Error_CompressionFailed;
   case gloox::ConnAuthenticationFailed:
      return XmppAccount::Error_AuthenticationFailed;
   case gloox::ConnUserDisconnected:
      return XmppAccount::Error_None;
   case gloox::ConnNotConnected:
      return XmppAccount::Error_NotConnected;
   case gloox::ConnNoError:
   default:
      return XmppAccount::Error_None;
   }
}

static cpc::string toErrorText(XmppAccount::Error e)
{
   switch (e)
   {
   case XmppAccount::Error_NoHandlerSet:
      return "XMPP account cannot be enabled before handler is set";
   case XmppAccount::Error_IoError:
      return "Send/receive error";
   case XmppAccount::Error_DnsError:
      return "DNS lookup error";
   case XmppAccount::Error_HostNotFound:
      return "Host not found";
   case XmppAccount::Error_ConnectionRefused:
      return "Connection refused";
   case XmppAccount::Error_AlreadyEnabled:
      return "XMPP account already enabled";
   case XmppAccount::Error_NotEnabled:
      return "XMPP account is not enabled";
   case XmppAccount::Error_AlreadyConnected:
      return "Already connected";
   case XmppAccount::Error_NotConnected:
      return "Not connected";
   case XmppAccount::Error_ParseError:
      return "XMPP parse error";
   case XmppAccount::Error_StreamError:
      return "XMPP stream error";
   case XmppAccount::Error_TlsFailed:
      return "TLS failed";
   case XmppAccount::Error_CompressionFailed:
      return "Compression failed";
   case XmppAccount::Error_UnsupportedAuthMech:
      return "No supported authentication mechanism";
   case XmppAccount::Error_AuthenticationFailed:
      return "Authentication failed";
   case XmppAccount::Error_NotDisconnected:
      return "Not disconnected";
   case XmppAccount::Error_None:
   default:
      return "";
   }
}

} // namespace XmppAccount

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ACCOUNT_MODULE
