//
//  CpcXepNotification.h
//  BriaVoip
//
//  Created by <PERSON> on January 19, 2017.
//  Copyright 2017 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_NOTIFICATION_H__
#define CPC_XEP_NOTIFICATION_H__

#include "stanzaextension.h"
#include "tag.h"
#include "jid.h"
#include "dataform.h"

/**
* @brief This is an implementation of @xep{0357} (Push Notification).
*/
class CpcXepNotification : public gloox::StanzaExtension
{
public:
   CpcXepNotification(bool enable, const gloox::JID& jid, const std::string& node, const gloox::DataForm& form = gloox::DataForm(NULL));
   virtual ~CpcXepNotification();

   static const std::string XMLNS_NOTIFICATION;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

private:
   bool mEnable;
   gloox::JID mJid;
   std::string mNode;
   gloox::DataForm mForm;
};

#endif // CPC_XEP_NOTIFICATION_H__
