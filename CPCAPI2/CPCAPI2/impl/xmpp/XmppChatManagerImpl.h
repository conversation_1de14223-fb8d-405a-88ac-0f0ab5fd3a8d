#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_MANAGER_IMPL_H__)
#define __CPCAPI2_XMPP_CHAT_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "XmppChatInfo.h"
#include "XmppAccountImpl.h"
#include "XmppChatManagerInterface.h"
#include "xmpp/XmppChatTypes.h"
#include "xmpp/XmppChatManager.h"
#include "xmpp/XmppChatHandler.h"
#include "xmpp/XmppIMCommandHandler.h"
#include "xmpp/XmppAccountSettings.h"
#include "XmppChatSyncHandler.h"
#include "iscomposing/IsComposingManager.h"
#include "phone/PhoneInterface.h"

#include <messagesessionhandler.h>
#include <messagehandler.h>
#include <messageeventhandler.h>
#include <chatstatehandler.h>
#include <presencehandler.h>

#include <set>

namespace CPCAPI2
{

namespace XmppChat
{

class XmppChatManagerImpl
   : public XmppAccount::XmppAccountObserver
   , public IsComposing::IsComposingManager
   , public gloox::MessageSessionHandler
   , public gloox::MessageHandler
   , public gloox::PresenceHandler
   , public gloox::DiscoHandler
   , public std::enable_shared_from_this<XmppChatManagerImpl>
{

public:

   XmppChatManagerImpl(PhoneInterface* cpcPhone, XmppAccount::XmppAccountImpl& account, XmppChatManagerInterface& intf);
   virtual ~XmppChatManagerImpl();

   // Invoked by the interface
   XmppChatInfo* getChatInfo(XmppChatHandle handle) const;
   void addChatInfo(XmppChatInfo* chatInfo);
   void addSdkObserver(XmppChatHandler* sdkObserver);
   void removeSdkObserver(XmppChatHandler* sdkObserver);
   void setHandler(XmppChatHandler* handler);
   void setHandler(XmppIMCommand::XmppChatIMCommandHandler* handler);
   void startChat(XmppChatInfo* chatInfo);
   // endChat() may be called asynchronously while another endChat() is in progress, therefore use XmppChatHandle instead of XmppChatInfo
   void endChat(XmppChatHandle handle, ChatEndReason reason);
   void sendMessage(XmppChatInfo *chatInfo, XmppChatMessageHandle message, const std::string& messageContent, const std::string& htmlText, const std::string& subject);
   void sendReaction(XmppChatInfo *chatInfo, XmppChatMessageHandle message, const std::string& target, const std::vector<std::string>& reactions);
   void sendMessageRetraction(XmppChatInfo *chatInfo, XmppChatMessageHandle message, const std::string& target);
   void acceptChat(XmppChatInfo* chatInfo);
   void rejectChat(XmppChatInfo* chatInfo, unsigned int reasonCode);

   DEPRECATED void notifyMessageDelivered(XmppChatInfo* chatInfo, XmppChatMessageHandle message);
   DEPRECATED void notifyMessageDisplayed(XmppChatInfo* chatInfo, XmppChatMessageHandle message);

   DEPRECATED void notifyMessageRead(XmppChatInfo* chatInfo, XmppChatMessageHandle message);
   void notifyMessageRead(XmppChatInfo* chatInfo, const cpc::string& threadId, const cpc::string& messageId);
   void notifyMessageRead(const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId);

   void setIsComposingMessage(XmppChatInfo* chatInfo, int refreshInterval, int idleInterval);
   void sendIMCommand(XmppChatInfo* chatInfo, XmppChatMessageHandle message, int type, const cpc::string& payload, const cpc::string& htmlPayload);
   void validateChatHandle(XmppChatHandle chat);
   void replaceMessage(XmppChatInfo *chatInfo, XmppChatMessageHandle message, const std::string& replaces, const std::string& messageContent, const std::string& htmlText, const std::string& subject);

   void fireError(XmppChatHandle chat, const cpc::string& errorText);

   // (indirectly via GlooxMessageEventHandler) gloox::ChatStateHandler
   void handleChatState(gloox::MessageSession* session, const gloox::JID& from, gloox::ChatStateType state);

   // (indirectly via GlooxMessageEventHandler) gloox::MessageEventHandler
   void handleMessageEvent(gloox::MessageSession* session, const gloox::Message& msg, const gloox::MessageEvent& me);

   void peerDiscovery(XmppChatInfo* chatInfo, const gloox::JID& peerJid);
#ifdef CPCAPI2_AUTO_TEST
   void peerDiscoverySetTimeout(XmppChatInfo* chatInfo, const gloox::JID& peerJid, uint32_t timeout);
#endif

private:

   // IsComposingManager
   virtual void sendIsComposingMessageNotification(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive) OVERRIDE;
   virtual void onIsComposingMessage(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive) OVERRIDE;

   // XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onStreamManagementAck(XmppAccount::XmppAccountImpl& account) OVERRIDE;

   // gloox::MessageSessionHandler
   virtual bool handleMessageSession(gloox::MessageSession* session) OVERRIDE;

   // gloox::MessageHandler
   virtual void handleMessage(const gloox::Message& msg, gloox::MessageSession* session) OVERRIDE;

   // gloox::PresenceHandler
   virtual void handlePresence(const gloox::Presence& presence) OVERRIDE;

   // gloox::DiscoHandler
   virtual void handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context) OVERRIDE;
   virtual void handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context) OVERRIDE;
   virtual void handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context) OVERRIDE;
   virtual bool handleDiscoSet(const gloox::IQ& iq) OVERRIDE;

private:

   XmppChatInfo* getChatInfoForGlooxMessageSession(gloox::MessageSession* session) const;
   void cleanup();

   // Implementation methods
   void addMessageInfo(XmppChatHandle chat, XmppChatMessageHandle message, MessageType messageType, bool outgoing, XmppChatMessageHandle origMessage, IsComposingMessageState isComposingState);
   void notifyMessageStatus(XmppChatInfo* chatInfo, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageType messageType, int messageStatus);
   bool validateChatStatus(XmppChatInfo* chatInfo);
   void transitionToActiveState(XmppChatInfo* chatInfo);
   void startInactiveTimer(XmppChatInfo* chatInfo);
   void startPeerDiscoveryTimer(XmppChatInfo* chatInfo, const gloox::JID& jid);
   void notifyMessageDelivered(XmppChatInfo* chatInfo, const std::string& threadId, const std::string& messageId);
   void getChatSettings(const XmppAccount::XmppAccountSettings& settings);

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, XmppChatHandle handle, const TEvt& evt)
   {
      for (std::list<XmppChatHandler*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, handle, evt);
         if (dynamic_cast<XmppChatSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }

      if (mAppHandler != reinterpret_cast<XmppChatHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, handle, evt);
         mAccount.postCallback(cb);
      }
   }

public:

   static std::atomic<XmppChatHandle> sNextXmppChatHandle;
   static std::atomic<XmppChatMessageHandle> sNextXmppChatMessageHandle;

private:

   XmppChatHandler* mAppHandler;
   std::list<XmppChatHandler*> mSdkObservers;
   XmppIMCommand::XmppChatIMCommandHandler* mIMCommandHandler;
   LocalLogger* mLocalLogger;

   PhoneInterface* mPhone;

   XmppAccount::XmppAccountImpl& mAccount;
   XmppChatManagerInterface& mInterface;
   typedef std::set<XmppChatInfo*> ChatInfoMap;
   ChatInfoMap mChatInfoMap;
//   typedef std::set<int> DiscoSet;
//   DiscoSet mDiscoSet;

   // use message ID as the key
   typedef std::map<std::string, std::pair<XmppChatHandle, SendMessageSuccessEvent> > MessageAckMap;
   MessageAckMap mMessageAckMap;

   // bliu: this class eliminates multiple inheritence in XmppChatManagerImpl
   struct InactiveTimerHandler : public resip::DeadlineTimerHandler
   {
      InactiveTimerHandler(XmppChatManagerImpl& impl) : mImpl(impl) {}

      // resip::DeadlineTimerHandler
      virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

      XmppChatManagerImpl& mImpl;
   };

   struct PeerDiscoveryWaitParams
   {
      XmppChatInfo* chatInfo;
      gloox::JID jid;
   };

   struct PeerDiscoveryTimerHandler : public resip::DeadlineTimerHandler
   {
      PeerDiscoveryTimerHandler(XmppChatManagerImpl& impl) : mImpl(impl) {}

      // resip::DeadlineTimerHandler
      virtual void onTimer(unsigned short timerId, void* waitParams) OVERRIDE;

      XmppChatManagerImpl& mImpl;
   };

   friend struct InactiveTimerHandler;
   friend struct PeerDiscoveryTimerHandler;

   InactiveTimerHandler mInactiveTimerHandler;
   PeerDiscoveryTimerHandler mPeerDiscoveryTimerHandler;

   // our settings
   bool mSupportChatMessageStyling;
   bool mLegacyReceiptsEnabled;
   bool mDeliveryReceiptsEnabled;
   bool mReadReceiptsEnabled;
};

class GlooxSessionEventHandler
   : public gloox::MessageEventHandler
   , public gloox::ChatStateHandler
{

public:

   GlooxSessionEventHandler(XmppChatManagerImpl& chatManager, gloox::MessageSession* session);
   virtual ~GlooxSessionEventHandler();

   // gloox::MessageEventHandler
   virtual void handleMessageEvent(const gloox::Message& msg, const gloox::MessageEvent& me);

   // gloox::ChatStateHandler
   virtual void handleChatState(const gloox::JID& from, gloox::ChatStateType state);

private:

   XmppChatManagerImpl& mChatManager;
   gloox::MessageSession* mGlooxSession;

};

}

}

#endif // __CPCAPI2_XMPP_CHAT_MANAGER_IMPL_H__
