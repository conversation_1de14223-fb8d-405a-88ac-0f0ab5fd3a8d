//
//  CpcXepEntityTime.h
//  BriaVoip
//
//  Created by <PERSON> on July 14, 2016.
//  Copyright 2016 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_ENTITY_TIME_H__
#define CPC_XEP_ENTITY_TIME_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0202} (Entity Time).
*
* The class also implements the deprecated @xep{0090} (Legacy Entity Time) in a read-only fashion.
* It understands both XEP formats for input, but any output will conform to @xep{0202}.
*/
class CpcXepEntityTime : public gloox::StanzaExtension
{
public:
   CpcXepEntityTime(const gloox::Tag* tag = NULL);
   virtual ~CpcXepEntityTime();

   static const std::string XMLNS_ENTITY_TIME;
   static const std::string XMLNS_ENTITY_TIME_LEGACY;

   /**
   * The format MUST adhere to the dateTime format specified in @xep{0082} and MUST be expressed in UTC.
   */
   const std::string& timestamp() const;
   const std::string& timezone() const;

   void setTime();

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mTimestamp;
   std::string mTimezone;
   bool mValid;
};

#endif // CPC_XEP_ENTITY_TIME_H__
