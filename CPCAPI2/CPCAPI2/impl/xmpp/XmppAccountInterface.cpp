#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include <cpcapi2utils.h>
#include "util/cpc_logger.h"
#include "util/DeviceInfo.h"
#include "util/ReactorHelpers.h"
#include "XmppAccountInterface.h"
#include "phone/PhoneInterface.h"
#include "xmpp/XmppAccountHandler.h"
#include "phone/Phone.h"
#include "XmppAccountImpl.h"
#include "json/JsonHelper.h"

#include <functional>
#include <algorithm>
#include <sstream>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT

namespace CPCAPI2
{

namespace XmppAccount
{

XmppAccountInterface::XmppAccountInterface(Phone* phone)
   : mShutdown(false)
   , mPreRelease(false)
   , mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mReactor(mPhone->getSdkModuleThread())
   , mRemoteSyncHelperIf(NULL)
{
   mNetworkChangeManagerIf = (NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone);
   mNetworkChangeManagerIf->addSdkObserver(this);

   mPhone->addRefImpl();

   // ensure the remote sync helper is initialized, so it can add itself as an observer for events.
   // must be called via post here, since the helper module itself attempts to get a reference to XmppAccountInterface
   post(resip::resip_bind(&XmppAccountInterface::getRemoteSyncHelper, this));
}

XmppAccountInterface::~XmppAccountInterface()
{
   // mReactor.stop(); // bliu: called in ~MultiReactor()

   if (!mPreRelease)
      mNetworkChangeManagerIf->removeSdkObserver(this);

   mShutdown = true;
   mCallbacks.add(new resip::ReadCallbackNoOp());
   mCallbacks.clear();

   mPhone->releaseImpl();
}

void XmppAccountInterface::PreRelease()
{
   InfoLog(<< "XmppAccountInterface::PreRelease()");
   mNetworkChangeManagerIf->removeSdkObserver(this);

   mPreRelease = true;

   cpc::vector<XmppAccountHandle> accounts = getAccountHandles();
   for (cpc::vector<XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppAccountImpl> account = getImpl(h))
      {
         if (account->isEnabled())
         {
            InfoLog(<< "XmppAccountInterface::PreRelease(): xmpp account: " << h << " is still enabled, proceed to disable");
            disable(h);
         }
         else
         {
            InfoLog(<< "XmppAccountInterface::PreRelease(): xmpp account: " << h << " is disabled, proceed to destroy");
            destroy(h);
         }
      }
   }
}

bool XmppAccountInterface::PreReleaseCompleted()
{
   // If there are no more accounts, we're done.
   return (mImplMap.size() == 0);
}

void XmppAccountInterface::Release()
{
   cpc::vector<XmppAccountHandle> accounts = getAccountHandles();
   for (cpc::vector<XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppAccountImpl> account = getImpl(h))
      {
         // Cleanup the sdk observer handlers from the xmpp account, to remove possibility of dangling handlers being triggered
         for (std::list<XmppAccountHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            account->removeSdkObserver(*j);

         // Forcefully destroy the XmppAccountImpl, as there was no luck with a graceful exit
         InfoLog(<< "XmppAccountInterface::PreRelease(): initiating a force-clean on xmpp account: " << h);
         account->destroy(true);

         mImplMap.erase(h); // This should trigger the deletion of the account due to reference count
      }
   }

   resip::default_delete<XmppAccountInterface>(this);
}

void XmppAccountInterface::fireError(const cpc::string& errorText)
{
   ErrLog(<< "Firing XmppAccountInterface error: " << errorText);

   PhoneErrorEvent evt;
   evt.errorText = errorText;
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), cpc::string("XmppAccountInterface"), evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), cpc::string("XmppAccountInterface"), evt);
}

int XmppAccountInterface::setHandler(XmppAccountHandle account, XmppAccountHandler* handler)
{
   DebugLog(<< "XmppAccountInterface setHandler " << account << " -> " << handler);

   resip::ReadCallbackBase* f = resip::resip_bind(&XmppAccountInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      process(-1);
      mCallbacks.clear();
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

void XmppAccountInterface::setHandlerImpl(XmppAccountHandle account, XmppAccountHandler* handler)
{
   DebugLog(<< "XmppAccountInterface setHandlerImpl " << account << " -> " << handler);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->setHandler(handler);

      if (handler != NULL)
      {
         // .jjg. order is important here -- the SDK observers need to be first
         // since some of them need to do their thing before the app gets called back
         for (std::list< XmppAccountHandlerInternal* >::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
            acct->addSdkObserver(*it);
      }
   }
   else
   {
      fireError("Invalid account handle for setHandler " + cpc::to_string(account));
   }
}

void XmppAccountInterface::addSdkObserver(XmppAccountHandlerInternal* observer)
{
   // NB: observers should be added before the handler is set, or they won't get pushed into the account.
   mSdkObservers.push_back( observer );
}

void XmppAccountInterface::removeSdkObserver(XmppAccountHandlerInternal* observer)
{
   mSdkObservers.remove( observer );
}

XmppAccountHandle XmppAccountInterface::create()
{
   return create(XmppAccountSettings());
}

XmppAccountHandle XmppAccountInterface::create(const XmppAccountSettings& settings)
{
   XmppAccountHandle account = 0x100 + XmppAccountImpl::sNextAccountHandle++;
   DebugLog(<< "XmppAccountInterface::create(): " << this << " account: " << account << " uid: " << settings.username << " domain: " << settings.domain << " ignoreCert: " << (settings.ignoreCertVerification ? "true" : "false"));

   post(resip::resip_bind(&XmppAccountInterface::createImpl, this, account, settings));
   return account;
}

void XmppAccountInterface::create(XmppAccountHandle account, const XmppAccountSettings& settings)
{
   DebugLog(<< "XmppAccountInterface::create(): " << this << " account: " << account << " uid: " << settings.username << " domain: " << settings.domain << " ignoreCert: " << (settings.ignoreCertVerification ? "true" : "false"));
   post(resip::resip_bind(&XmppAccountInterface::createImpl, this, account, settings));
}

void XmppAccountInterface::createImpl(XmppAccountHandle account, const XmppAccountSettings& settings)
{
   StackLog(<< "XmppAccountInterface::createImpl(): " << this << " account: " << account << " uid: " << settings.username << " domain: " << settings.domain << " ignoreCert: " << (settings.ignoreCertVerification ? "true" : "false"));

   if (!mPhone->isInitialized())
   {
      fireError("Phone was not initialized. XMPP account " + cpc::to_string(account) + " was not created.");
      return;
   }

   if (!isLicenseOK())
   {
      fireError("License not valid. XMPP account " + cpc::to_string(account) + " was not created.");
      return;
   }

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      StackLog(<< "XmppAccountInterface::createImpl(): " << this << " updating existing account: " << account << " uid: " << settings.username << " domain: " << settings.domain);

      acct->pendingSettings() = settings;
      acct->applySettings();
   }
   else
   {
      StackLog(<< "XmppAccountInterface::createImpl(): " << this << " creating new account: " << account << " uid: " << settings.username << " domain: " << settings.domain);
      std::shared_ptr<XmppAccountImpl> newAcct = std::make_shared<XmppAccountImpl>(account, settings, &mCallbacks, mCbHook, mPhone, *this, mSdkObservers);

      NetworkTransport transport = mNetworkChangeManagerIf->networkTransport();
      if (newAcct->isRestrictedNetwork(transport) || transport == TransportNone)
      {
         newAcct->addRestriction(NetworkRestriction);
      }

      mImplMap[account] = newAcct;
   }
}

void XmppAccountInterface::simulateNetworkLoss(XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppAccountInterface::simulateNetworkLossImpl, this, account));
}

void XmppAccountInterface::simulateNetworkLossImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountInterface::simulateNetworkLossImpl(): xmpp account handle: " << account);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->simulateNetworkLoss();
   }
   else
   {
      fireError("Invalid account handle for simulateNetworkLoss " + cpc::to_string(account));
   }
}

void XmppAccountInterface::simulateNetworkRestriction(XmppAccountHandle account, bool restricted)
{
   post(resip::resip_bind(&XmppAccountInterface::simulateNetworkRestrictionImpl, this, account, restricted));
}

void XmppAccountInterface::simulateNetworkRestrictionImpl(XmppAccountHandle account, bool restricted)
{
   StackLog(<< "XmppAccountInterface::simulateNetworkRestrictionImpl(): xmpp account handle: " << account);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      restricted ? acct->addRestriction(NetworkRestriction) : acct->removeRestriction(NetworkRestriction);
   }
   else
   {
      fireError("Invalid account handle for simulateNetworkRestriction " + cpc::to_string(account));
   }
}

int XmppAccountInterface::setInactive(XmppAccountHandle account, bool inactive)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::setInactiveImpl, this, account, inactive));
   return kSuccess;
}

void XmppAccountInterface::setInactiveImpl(XmppAccountHandle account, bool inactive)
{
   StackLog(<< "XmppAccountInterface::setInactiveImpl(): xmpp account handle: " << account << " inactive=" << inactive);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->setInactive(inactive);
   }
   else
   {
      fireError("Invalid account handle for setInactive " + cpc::to_string(account));
   }
}

int XmppAccountInterface::configureDefaultAccountSettings(XmppAccountHandle account, const XmppAccountSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::configureDefaultAccountSettingsImpl, this, account, settings));
   return kSuccess;
}

void XmppAccountInterface::configureDefaultAccountSettingsImpl(XmppAccountHandle account, const XmppAccountSettings& settings)
{
   StackLog(<< "XmppAccountInterface::configureDefaultAccountSettingsImpl(): xmpp account handle: " << account);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->pendingSettings() = settings;
   }
   else
   {
      fireError("Invalid account handle for configureDefaultAccountSettings " + cpc::to_string(account));
   }
}

void XmppAccountInterface::setAccountSettingsInternal(XmppAccountHandle account, const XmppAccountSettingsInternal& settings)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::setAccountSettingsInternalImpl, this, account, settings));
}

void XmppAccountInterface::setAccountSettingsInternalImpl(XmppAccountHandle account, const XmppAccountSettingsInternal& settings)
{
   StackLog(<< __FUNCTION__ << " xmpp account handle : " << account);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->getSettingsInternal() = settings;
   }
   else
   {
      fireError(__FUNCTION__ + cpc::string(" invalid account handle=") + cpc::to_string(account));
   }
}

int XmppAccountInterface::applySettings(XmppAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::applySettingsImpl, this, account));
   return kSuccess;
}

void XmppAccountInterface::applySettingsImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountInterface::applySettingsImpl(): xmpp account handle: " << account);

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->applySettings();
   }
   else
   {
      fireError("Invalid account handle for applySettings " + cpc::to_string(account));
   }
}

int XmppAccountInterface::enable(XmppAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::enableImpl, this, account));
   return kSuccess;
}

void XmppAccountInterface::enableImpl(XmppAccountHandle account)
{
   DebugLog(<< "XmppAccountInterface::enableImpl(): " << this << " xmpp account handle: " << account);

   if (!mPhone->isInitialized())
   {
      fireError("Phone was not initialized. XMPP account " + cpc::to_string(account) + " cannot be enabled.");
      return;
   }

   if (!isLicenseOK())
   {
      fireError("License not valid. XMPP account " + cpc::to_string(account) + " cannot be enabled.");
      return;
   }

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->removeRestriction(UserDisabledRestriction);
   }
   else
   {
      fireError("Invalid account handle for enable " + cpc::to_string(account));
   }
}

int XmppAccountInterface::disable(XmppAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::disableImpl, this, account));
   return kSuccess;
}

void XmppAccountInterface::disableImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountInterface::disableImpl(): xmpp account handle: " << account << " account list: " << mImplMap.size());

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->addRestriction(UserDisabledRestriction);
   }
   else
   {
      fireError("Invalid account handle for disable " + cpc::to_string(account));
   }
}

int XmppAccountInterface::destroy(XmppAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   post(resip::resip_bind(&XmppAccountInterface::destroyImpl, this, account));
   return kSuccess;
}

void XmppAccountInterface::destroyImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountInterface::destroyImpl(): xmpp account handle: " << account << " account list: " << mImplMap.size());

   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      if (acct->isEnabled())
      {
         acct->fireXmppError(XmppAccount::Error_NotDisconnected);
         return;
      }

      InfoLog(<< "XmppAccountInterface::destroyImpl(): destroying xmpp account: " << account);
      acct->destroy();

      mImplMap.erase(account); // This should trigger the deletion of the account due to reference count
   }
   else
   {
      fireError("Invalid account handle for destroy " + cpc::to_string(account));
   }
}

int XmppAccountInterface::publishPresence(XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   post(resip::resip_bind(&XmppAccountInterface::publishPresenceImpl, this, account, presence, note, userActivityGeneralType, userActivitySpecificType, userActivityText));
   return kSuccess;
}

void XmppAccountInterface::publishPresenceImpl(XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   StackLog(<< "XmppAccountInterface::publishPresenceImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->publishPresence(presence, note, userActivityGeneralType, userActivitySpecificType, userActivityText);
   }
   else
   {
      fireError("Invalid account handle for publishPresence " + cpc::to_string(account));
   }
}

int XmppAccountInterface::publishCannedPresence(XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note)
{
   post(resip::resip_bind(&XmppAccountInterface::publishCannedPresenceImpl, this, account, status, note));
   return kSuccess;
}

void XmppAccountInterface::publishCannedPresenceImpl(XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note)
{
   StackLog(<< "XmppAccountInterface::publishCannedPresenceImpl(): xmpp account handle=" << account << " status=" << status);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->publishCannedPresence(status, note);
   }
   else
   {
      fireError("Invalid account handle for publishCannedPresence " + cpc::to_string(account));
   }
}

int XmppAccountInterface::blockIncomingPresence(XmppAccountHandle account, bool block)
{
   post(resip::resip_bind(&XmppAccountInterface::blockIncomingPresenceImpl, this, account, block));
   return kSuccess;
}

void XmppAccountInterface::blockIncomingPresenceImpl(XmppAccountHandle account, bool block)
{
   StackLog(<< "XmppAccountInterface::blockIncomingPresenceImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->blockIncomingPresence(block);
   }
   else
   {
      fireError("Invalid account handle for blockIncomingPresence " + cpc::to_string(account));
   }
}

int XmppAccountInterface::setHibernationState(XmppAccountHandle account, bool active)
{
   post(resip::resip_bind(&XmppAccountInterface::setHibernationStateImpl, this, account, active));
   return kSuccess;
}

void XmppAccountInterface::setHibernationStateImpl(XmppAccountHandle account, bool active)
{
   StackLog(<< "XmppAccountInterface::setHibernationStateImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->setHibernationState(active);
   }
   else
   {
      fireError("Invalid account handle for setHibernationState " + cpc::to_string(account));
   }
}

int XmppAccountInterface::getEntityTime(XmppAccountHandle account, const cpc::string& jid)
{
   post(resip::resip_bind(&XmppAccountInterface::getEntityTimeImpl, this, account, jid));
   return kSuccess;
}

void XmppAccountInterface::getEntityTimeImpl(XmppAccountHandle account, const cpc::string& jid)
{
   StackLog(<< "XmppAccountInterface::getEntityTimeImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->getEntityTime(jid);
   }
   else
   {
      fireError("Invalid account handle for getEntityTime " + cpc::to_string(account));
   }
}

int XmppAccountInterface::enableNotification(XmppAccountHandle account, const cpc::string& node, const XmppDataForm& dataform)
{
   post(resip::resip_bind(&XmppAccountInterface::enableNotificationImpl, this, account, node, dataform));
   return kSuccess;
}

void XmppAccountInterface::enableNotificationImpl(XmppAccountHandle account, const cpc::string& node, const XmppDataForm& dataform)
{
   StackLog(<< "XmppAccountInterface::enableNotificationImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->enableNotification(node, dataform);
   }
   else
   {
      fireError("Invalid account handle for enableNotification " + cpc::to_string(account));
   }
}

int XmppAccountInterface::disableNotification(XmppAccountHandle account, const cpc::string& node)
{
   post(resip::resip_bind(&XmppAccountInterface::disableNotificationImpl, this, account, node));
   return kSuccess;
}

void XmppAccountInterface::disableNotificationImpl(XmppAccountHandle account, const cpc::string& node)
{
   StackLog(<< "XmppAccountInterface::disableNotificationImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->disableNotification(node);
   }
   else
   {
      fireError("Invalid account handle for disableNotification " + cpc::to_string(account));
   }
}

int XmppAccountInterface::getPrivateStorageData(XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppAccountInterface::getPrivateStorageDataImpl, this, account));
   return kSuccess;
}

void XmppAccountInterface::getPrivateStorageDataImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountInterface::getPrivateStorageDataImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->getPrivateStorageData();
   }
   else
   {
      fireError("Invalid account handle for getPrivateStorageData " + cpc::to_string(account));
   }
}

int XmppAccountInterface::setPrivateStorageData(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data)
{
   post(resip::resip_bind(&XmppAccountInterface::setPrivateStorageDataImpl, this, account, data));
   return kSuccess;
}

void XmppAccountInterface::setPrivateStorageDataImpl(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data)
{
   StackLog(<< "XmppAccountInterface::setPrivateStorageDataImpl(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->setPrivateStorageData(data);
   }
   else
   {
      fireError("Invalid account handle for setPrivateStorageData " + cpc::to_string(account));
   }
}

int XmppAccountInterface::process(unsigned int timeout)
{
   if (mShutdown)
   {
      return kAccountModuleDisabled;
   }

   resip::ReadCallbackBase* fp = mCallbacks.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kAccountModuleDisabled;
      }
      fp = mCallbacks.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* XmppAccountInterface::process_test(int timeout)
{
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbacks.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

XmppAccountManagerInternal* XmppAccountManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<XmppAccountManagerInternal*>(XmppAccountManager::getInterface(cpcPhone));
}

void XmppAccountInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void XmppAccountInterface::post(resip::ReadCallbackBase* f)
{
   mReactor.post(f);
}

void XmppAccountInterface::execute(resip::ReadCallbackBase* f)
{
   mReactor.execute(f);
}

int XmppAccountInterface::onNetworkChange(const NetworkChangeEvent& params)
{
   post(resip::resip_bind(&XmppAccountInterface::handleNetworkChangeEvent, this, params));
   return 0;
}

int XmppAccountInterface::handleNetworkChangeEvent(const NetworkChangeEvent& params)
{
   NetworkTransport transport = params.networkTransport;
   DebugLog(<< "handleNetworkChangeEvent transport = " << transport);

   for (ImplMap::iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      if (transport == TransportNone)
      {
         DebugLog(<< "handleNetworkChangeEvent - no network");
         it->second->addRestriction(NetworkRestriction);
      }
      else if (it->second->isRestrictedNetwork(transport))
      {
         DebugLog(<< "handleNetworkChangeEvent - restricted network, transport=" << transport);
         it->second->addRestriction(NetworkRestriction);
      }
      else
      {
         DebugLog(<< "handleNetworkChangeEvent - unrestricted network");
         it->second->removeRestriction(NetworkRestriction);
      }
   }

   return kSuccess;
}

cpc::vector<XmppAccountHandle> XmppAccountInterface::getAccountHandles()
{
   cpc::vector<XmppAccountHandle> accountHandles;

   for (ImplMap::const_iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      accountHandles.push_back(it->first);
   }

   return accountHandles;
}

void XmppAccountInterface::onLicensingError()
{
   for (ImplMap::const_iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      if (it->second->isEnabled()) it->second->addRestriction(UserDisabledRestriction);
   }
}

RemoteSyncXmppHelper::RemoteSyncXmppHelper* XmppAccountInterface::getRemoteSyncHelper()
{
   if (mRemoteSyncHelperIf == NULL)
   {
      mRemoteSyncHelperIf = RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(mPhone);
   }
   return mRemoteSyncHelperIf;
}

cpc::string XmppAccountInterface::getRemoteSyncAccountID(XmppAccountHandle account)
{
   return getRemoteSyncHelper()->getRemoteSyncAccountID(account);
}

XmppAccountHandle XmppAccountInterface::getAccountHandleFromRemoteSyncID(cpc::string accountID)
{
   return getRemoteSyncHelper()->getAccountHandleFromRemoteSyncID(accountID);
}

int XmppAccountInterface::send(XmppAccountHandle account, const cpc::string& xml)
{
   post(resip::resip_bind(&XmppAccountInterface::sendImpl, this, account, xml));
   return kSuccess;
}

void XmppAccountInterface::sendImpl(XmppAccountHandle account, const cpc::string& xml)
{
   StackLog(<< "XmppAccountInterface::send(): xmpp account handle: " << account);
   if (std::shared_ptr<XmppAccountImpl> acct = getImpl(account))
   {
      acct->send(xml);
   }
   else
   {
      fireError("Invalid account handle for send " + cpc::to_string(account));
   }
}

int XmppAccountInterface::setNetworkRestriction(XmppAccountHandle account, NetworkTransport transport, bool restricted)
{
   if (transport == TransportNone)
      return kError;

   post(resip::resip_bind(&XmppAccountInterface::setNetworkRestrictionImpl, this, account, transport, restricted));
   return kSuccess;
}

void XmppAccountInterface::setNetworkRestrictionImpl(XmppAccountHandle account, NetworkTransport transport, bool restricted)
{
   std::shared_ptr<XmppAccountImpl> acct = getImpl(account);

   if (acct == NULL)
   {
      fireError("Invalid account handle for setNetworkRestriction " + cpc::to_string(account));
      return;
   }

   if (restricted != acct->isRestrictedNetwork(transport))
   {
      std::set<NetworkTransport>& networks = acct->restrictedNetworks();

      if (restricted)
      {
         networks.insert(transport);
      }
      else
      {
         networks.erase(transport);
      }

      NetworkTransport currentTransport = mNetworkChangeManagerIf->networkTransport();

      if (currentTransport == transport)
      {
         NetworkChangeEvent params;
         params.networkTransport = transport;
         handleNetworkChangeEvent(params);
      }
   }
}

int XmppAccountInterface::onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
   if (mPreRelease)
   {
      if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Disconnected)
      {
         InfoLog(<< "XmppAccountInterface::onAccountStatusChanged(): xmpp account: " << account << " is disabled, proceed to destroy as pre-release has been initiated");
         destroy(account);
      }
   }

   return kSuccess;
}

int XmppAccountInterface::onError(XmppAccountHandle account, const ErrorEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::onLicensingError(XmppAccountHandle account, const LicensingErrorEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::onAccountConfigured(XmppAccountHandle account, const XmppAccountConfiguredEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::onEntityTime(XmppAccountHandle account, const EntityTimeEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::onEntityFeature(XmppAccountHandle account, const EntityFeatureEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::onStreamManagementState(XmppAccountHandle account, const StreamManagementStateEvent& args)
{
   return kSuccess;
}

int XmppAccountInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<XmppAccountSettings>& outXmppAccountSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("xmppAccount"))
   {
      WarningLog(<< "Invalid provisioning format, xmppAccount node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& account = provisionedJSON["xmppAccount"];
   if (!account.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, xmppAccount node not an array. Aborting decode.");
      return kError;
   }

   for (rapidjson::Value::ConstValueIterator itr = account.Begin(); itr != account.End(); ++itr)
   {
      if (!itr->HasMember("xmppAccountSettings"))
      {
         WarningLog(<< "Invalid provisioning format, xmppAccountSettings node missing.");
         continue;
      }

      const rapidjson::Value& accountSettings = (*itr)["xmppAccountSettings"];
      if (!accountSettings.IsObject())
      {
         WarningLog(<< "Invalid provisioning format, xmppAccountSettings not an object.");
         continue;
      }

      XmppAccountSettings settings;
      JsonDeserialize(*itr, "xmppAccountSettings", settings);
      outXmppAccountSettings.push_back(settings);
   }
   return kSuccess;
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::Error& error)
{
   switch (error)
   {
      case XmppAccount::Error_None: return "None";
      case XmppAccount::Error_NoHandlerSet: return "NoHandlerSet";
      case XmppAccount::Error_IoError: return "IoError";
      case XmppAccount::Error_DnsError: return "DnsError";
      case XmppAccount::Error_HostNotFound: return "HostNotFound";
      case XmppAccount::Error_ConnectionRefused: return "ConnectionRefused";
      case XmppAccount::Error_AlreadyEnabled: return "AlreadyEnabled";
      case XmppAccount::Error_NotEnabled: return "NotEnabled";
      case XmppAccount::Error_AlreadyConnected: return "AlreadyConnected";
      case XmppAccount::Error_NotConnected: return "NotConnected";
      case XmppAccount::Error_ParseError: return "ParseError";
      case XmppAccount::Error_StreamError: return "StreamError";
      case XmppAccount::Error_TlsFailed: return "TlsFailed";
      case XmppAccount::Error_CompressionFailed: return "CompressionFailed";
      case XmppAccount::Error_UnsupportedAuthMech: return "UnsupportedAuthMech";
      case XmppAccount::Error_AuthenticationFailed: return "AuthenticationFailed";
      case XmppAccount::Error_NotDisconnected: return "NotDisconnected";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus& status)
{
   switch (status)
   {
      case XmppTLSConnectionInfo::CertificateStatus_Ok: return "Ok";
      case XmppTLSConnectionInfo::CertificateStatus_Invalid: return "Invalid";
      case XmppTLSConnectionInfo::CertificateStatus_SignerUnknown: return "SignerUnknown";
      case XmppTLSConnectionInfo::CertificateStatus_Revoked: return "Revoked";
      case XmppTLSConnectionInfo::CertificateStatus_Expired: return "Expired";
      case XmppTLSConnectionInfo::CertificateStatus_NotActive: return "NotActive";
      case XmppTLSConnectionInfo::CertificateStatus_WrongPeer: return "WrongPeer";
      case XmppTLSConnectionInfo::CertificateStatus_CertSignerNotCa: return "CertSignerNotCa";
      case XmppTLSConnectionInfo::CertificateStatus_WrongPubKey: return "WrongPublicKey";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppTLSConnectionInfo& info)
{
   std::stringstream ss;
   ss << "certificateStatus: " << info.certificateStatus << " issuer: " << info.issuer << " server: " << info.server
      << " protocol: " << info.protocol << " cipher: " << info.cipher << " mac: " << info.mac << " compression: "
      << info.compression << " publicKey: " << info.publicKey << " peerName count: " << info.peerNames.size();
   for (cpc::vector<cpc::string>::const_iterator i = info.peerNames.begin(); i != info.peerNames.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status& status)
{
   switch (status)
   {
      case XmppAccountStatusChangedEvent::Status_Connected: return "Connected";
      case XmppAccountStatusChangedEvent::Status_Failure: return "Failure";
      case XmppAccountStatusChangedEvent::Status_Disconnected: return "Disconnected";
      case XmppAccountStatusChangedEvent::Status_Connecting: return "Connecting";
      case XmppAccountStatusChangedEvent::Status_Disconnecting: return "Disconnecting";
      case XmppAccountStatusChangedEvent::Status_Destroyed: return "Destroyed";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event)
{
   std::stringstream ss;
   ss << "status: " << event.accountStatus << " errorCode: " << event.errorCode << " errorText: " << event.errorText << " tlsInfo: " << event.tlsInfo;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::ErrorEvent& event)
{
   std::stringstream ss;
   ss << "errorText: " << event.errorText;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::LicensingErrorEvent& event)
{
   std::stringstream ss;
   ss << "errorText: " << event.errorText;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityTimeEvent& event)
{
   std::stringstream ss;
   ss << "errorCode: " << event.errorCode << " from: " << event.from.c_str() << " timestamp: " << event.timestamp << " millisecond: " << event.millisecond;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityFeatureEvent::Feature& feature)
{
   switch (feature)
   {
      case EntityFeatureEvent::EntityTime: return "EntityTime";
      case EntityFeatureEvent::Ping: return "Ping";
      case EntityFeatureEvent::Privacy: return "Privacy";
      case EntityFeatureEvent::Notification: return "Notification";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::EntityFeatureEvent& event)
{
   std::stringstream ss;
   ss << "entity: " << event.entity << " feature count: " << event.features.size();
   for (cpc::vector<EntityFeatureEvent::Feature>::const_iterator i = event.features.begin(); i != event.features.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAccount::StreamManagementStateEvent& event)
{
   std::stringstream ss;
   ss << "id: " << event.id << " sequence: " << event.sequence;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::Error& error)
{
   os << CPCAPI2::XmppAccount::get_debug_string(error);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus& status)
{
   os << CPCAPI2::XmppAccount::get_debug_string(status);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppTLSConnectionInfo& info)
{
   os << CPCAPI2::XmppAccount::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status& status)
{
   os << CPCAPI2::XmppAccount::get_debug_string(status);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::ErrorEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::LicensingErrorEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityTimeEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityFeatureEvent::Feature& feature)
{
   os << CPCAPI2::XmppAccount::get_debug_string(feature);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityFeatureEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::StreamManagementStateEvent& event)
{
   os << CPCAPI2::XmppAccount::get_debug_string(event);
   return os;
}

}

}

#endif // CPCAPI2_XMPP_ACCOUNT_MODULE
