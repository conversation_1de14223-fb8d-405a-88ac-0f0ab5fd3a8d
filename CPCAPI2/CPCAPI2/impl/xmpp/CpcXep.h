#ifndef CPC_XEP_H__
#define CPC_XEP_H__

#include "stanzaextension.h"

enum CpcXepType
{
   EXepNone = gloox::ExtUser + 1000,
   EXepUserActivity,
   EXepEntityTime,
   EXepIMCommand,
   EXepNotification,
   EXepCiscoCustomStatus,
   EXepHTTPFileUpload,
   EXepChatMarker,
   EXepMessageProcessingHint,
   EXepMessageCorrection,
   EXepMessageOrigin,
   EXepMessageReaction,
   EXepMessageRetract,
   EXepMessageStyling,
   EXepMessageStanza
};

#endif // CPC_XEP_H__
