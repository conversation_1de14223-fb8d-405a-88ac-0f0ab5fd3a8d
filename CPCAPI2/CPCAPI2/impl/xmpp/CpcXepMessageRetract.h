//
//  CpcXepMessageRetract
//  BriaVoip
//
//  Created by <PERSON> on July 12, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_RETRACT_H__
#define CPC_XEP_MESSAGE_RETRACT_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0424} (message retraction).
*/
class CpcXepMessageRetract : public gloox::StanzaExtension
{
public:
   CpcXepMessageRetract(std::string targetId);
   CpcXepMessageRetract(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   const std::string targetId() const { return mTargetId; }

   static const std::string XMLNS_MESSAGE_RETRACT;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mTargetId;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_RETRACT_H__
