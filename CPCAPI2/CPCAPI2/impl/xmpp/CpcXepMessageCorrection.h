#ifndef CPC_XEP_MESSAGE_CORRECTION_H__
#define CPC_XEP_MESSAGE_CORRECTION_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0308} (Last Message Correction).
*/
class CpcXepMessageCorrection : public gloox::StanzaExtension
{
public:
   CpcXepMessageCorrection(const std::string& id);
   CpcXepMessageCorrection(const gloox::Tag* tag);

   bool isValid() const { return !mId.empty(); }
   const std::string id() const { return mId; }

   static const std::string XMLNS_MESSAGE_CORRECTION;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mId;

};

#endif // CPC_XEP_MESSAGE_CORRECTION_H__
