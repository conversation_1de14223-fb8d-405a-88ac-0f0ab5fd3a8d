#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_INTERFACE_H)
#define CPCAPI2_XMPP_ROSTER_INTERFACE_H

#include "cpcapi2defs.h"
#include "XmppAccountInterface.h"
#include "XmppCommon.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppRosterInternal.h"
#include "xmpp/XmppRosterHandlerInternal.h"
#include "xmpp/XmppRosterState.h"
#include "phone/PhoneModule.h"

#include <rutil/RecursiveMutex.hxx>

namespace CPCAPI2
{

class PhoneInterface;
class LocalLogger;

namespace XmppRoster
{

class XmppRosterImpl;

class XmppRosterInterface
   : public XmppRosterManagerInternal
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppRosterImpl>
{
public:

   XmppRosterInterface(Phone* phone);
   virtual ~XmppRosterInterface();

   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual XmppRosterHandle createRoster(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler) OVERRIDE;

   virtual int acceptSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;
   virtual int rejectSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;
   virtual int cancelAcceptedSubscription(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message) OVERRIDE;

   virtual int addRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups) OVERRIDE;
   virtual int updateRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups) OVERRIDE;
   virtual int removeRosterItem(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;

   virtual int subscribePresence(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message) OVERRIDE;
   virtual int unsubscribePresence(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;

   virtual int getRosterState(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems) OVERRIDE;

   // XmppRosterManagerInternal
   // static XmppRosterManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
   virtual void create(XmppAccount::XmppAccountHandle account) OVERRIDE;
   void create(XmppRosterHandle roster, XmppAccount::XmppAccountHandle account);

   virtual PhoneInterface* phoneInterface() { return mPhone; }

   void addSdkObserver(XmppRosterHandlerInternal* observer);
   void removeSdkObserver(XmppRosterHandlerInternal* observer);
   void getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts);

   struct RosterItemList
   {
      XmppRosterHandle roster;
      cpc::vector<RosterItem> items;
   } ;

   int getRosterState(XmppAccount::XmppAccountHandle account, RosterItemList& rosterItems);
   int getAllRosterState(std::map<XmppAccount::XmppAccountHandle, RosterItemList>& rosterItems);

private:

   friend class XmppRosterImpl; // to access cache. it's better to implement state manager instead

   XmppRosterImpl* getRoster(XmppRosterHandle rosterHandle);

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler);

   void createImpl(XmppAccount::XmppAccountHandle account);
   void createRosterImpl(XmppAccount::XmppAccountHandle account, XmppRosterHandle h);

   void acceptSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address);
   void rejectSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address);
   void cancelAcceptedSubscriptionImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message);

   void addRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void updateRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void removeRosterItemImpl(XmppRosterHandle roster, const cpc::string& address);

   void subscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message);
   void unsubscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address);

   PhoneInterface* mPhone;
   LocalLogger* mLocalLogger;
   bool mPreRelease;
   XmppAccount::XmppAccountInterface* mAccountIf;
   std::function<void(void)> mCbHook;
   std::list<XmppRosterHandlerInternal*> mSdkObservers;

   typedef std::map<XmppRosterHandle, std::map<cpc::string, RosterItem> > CacheMap;
   CacheMap mCache;
   resip::RecursiveMutex mCacheLock;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::SubscriptionState& state);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::PresenceType& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::UserActivityGeneralType& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::UserActivitySpecificType& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::ResourceItem& item);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::RosterItem& item);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::XmppRosterState& state);

}

}

#endif // CPCAPI2_XMPP_ROSTER_INTERFACE_H
