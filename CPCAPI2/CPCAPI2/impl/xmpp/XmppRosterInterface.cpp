#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)
#include "XmppRosterInterface.h"
#include <cpcapi2utils.h>
#include "util/DumFpCommand.h"
#include "XmppAccountImpl.h"
#include "XmppRosterImpl.h"

#include <sstream>

#include "util/cpc_logger.h"
#include "log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT
#define CP_LOCAL_LOGGER_VAR mLocalLogger

#define FIRE_ERROR(msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   mAccountIf->fireError(ss.str().c_str());\
}

namespace CPCAPI2
{

namespace XmppRoster
{

#define __verify(condition, handler) if (!(condition)) { /* assert(false); */ handler; }

XmppRosterInterface::XmppRosterInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mLocalLogger(mPhone->localLogger())
   , mPreRelease(false)
{
   mAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone));
   StackLog(<< "XmppRosterInterface::XmppRosterInterface(): " << this << " phone: " << mPhone);
}

XmppRosterInterface::~XmppRosterInterface()
{
   StackLog(<< "XmppRosterInterface::~XmppRosterInterface(): " << this << " phone: " << mPhone);

   // this is a special hack for XmppRoster due to the fact that XmppRosterImpl's destructor still accesses XmppRosterInterface.
   // the following line should be automatically called inside ImplManager's destructor,
   // however this also means XmppRosterImpl lives longer than XmppRosterInterface which is not desirable in this case.
   mImplMap.clear();
}

void XmppRosterInterface::addSdkObserver(XmppRosterHandlerInternal* observer)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), observer) != mSdkObservers.end())
   {
      StackLog(<< "XmppRosterInterface::addSdkObserver(): XmppRosterInterface observer: " << observer << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppRosterInterface::addSdkObserver(): XmppRosterInterface observer: " << observer);
   mSdkObservers.push_back(observer);
}

void XmppRosterInterface::removeSdkObserver(XmppRosterHandlerInternal* observer)
{
   std::list<XmppRosterHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), observer);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppRosterInterface::removeSdkObserver(): XmppRosterInterface observer: " << observer << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppRosterInterface::getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts)
{
   for (ImplMap::const_iterator i = mImplMap.begin(); i != mImplMap.end(); ++i)
   {
      accounts.push_back(i->first);
   }
}

XmppRosterManagerInternal* XmppRosterManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<XmppRosterManagerInternal*>(XmppRosterManager::getInterface(cpcPhone));
}

void XmppRosterInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   StackLog(<< "XmppRosterInterface::setCallbackHook(): " << this << " phone: " << mPhone);
   mCbHook = std::bind(cbHook, context);
}

void XmppRosterInterface::PreRelease()
{
   LocalDebugLog("XmppRosterInterface::PreRelease");

   mPreRelease = true;

   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppRosterImpl> roster = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppRosterHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            roster->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }
}

bool XmppRosterInterface::PreReleaseCompleted()
{
   // If there are no more chats, we're done.
   return (mImplMap.size() == 0);
}

void XmppRosterInterface::Release()
{
   LocalDebugLog("XmppRosterInterface::Release");
   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);

   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppRosterImpl> roster = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppRosterHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            roster->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }

   mSdkObservers.clear();
   delete this;
}

XmppRosterHandle XmppRosterInterface::createRoster(XmppAccount::XmppAccountHandle account)
{
   XmppRosterHandle handle = XmppRosterImpl::sNextRosterHandle++;
   StackLog(<< "XmppRosterInterface::createRoster(): " << this << " phone: " << mPhone << " account: " << account << " handle: " << handle);
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::createRosterImpl, this, account, handle));
   return handle;
}

void XmppRosterInterface::createRosterImpl(XmppAccount::XmppAccountHandle account, XmppRosterHandle h)
{
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      FIRE_ERROR("XmppRosterManager::createRoster called with invalid account handle: " << account);
      return;
   }

   std::shared_ptr<XmppRosterImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      cpc::string msg = cpc::string("XmppRosterInterface::create before setHandler() is called: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   impl->setHandle(h);

   DebugLog(<< "XmppRosterInterface::createRosterImpl(): " << this << " phone: " << mPhone << " account: " << account << " handle: " << h);
   XmppRosterCreatedResultEvent evt;
   evt.account = account;
   impl->fireEvent(cpcFunc(XmppRosterHandlerInternal::onCreateRosterResult), evt);
}

void XmppRosterInterface::create(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterInterface::create(): " << this << " phone: " << mPhone << " account: " << account);
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::createImpl, this, account));
}

void XmppRosterInterface::createImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterInterface::createVCardImpl(): " << this << " account: " << account);
   XmppRosterHandle handle = XmppRosterImpl::sNextRosterHandle++;
   createRosterImpl(account, handle);
}

void XmppRosterInterface::create(XmppRosterHandle roster, XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterInterface::create(): " << this << " phone: " << mPhone << " account: " << account);
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::createRosterImpl, this, account, roster));
}

int XmppRosterInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler)
{
   StackLog(<< "XmppRosterInterface::setHandler(): " << this << " phone: " << mPhone << " account: " << account);
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XmppRosterInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(cmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(cmd);
   }
   return kSuccess;
}

void XmppRosterInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler)
{
   StackLog(<< "XmppRosterInterface::setHandlerImpl(): xmpp account: " << account << " handler: " << handler);

   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      cpc::string msg = cpc::string("XmppRosterInterface::setHandler called with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppRosterImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      StackLog(<< "XmppRosterInterface::setHandlerImpl(): xmpp account: " << account << " creating new XmppRosterImpl");
      // Create a new manager impl
      impl = std::make_shared<XmppRosterImpl>(*acct, *this); // Destroyed in the destructor of this class

      // Keep mapping account -> manager impl
      mImplMap[account] = impl;

      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppRosterHandlerInternal*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
   }

   // Register the handler
   StackLog(<< "XmppRosterInterface::setHandlerImpl(): xmpp account: " << account << " registering the handler: " << handler);
   impl->setHandler(handler);
}

int XmppRosterInterface::acceptSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::acceptSubscriptionRequestImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterInterface::acceptSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterInterface::acceptSubscriptionRequestImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::acceptSubscriptionRequest called with invalid roster handle: " << roster);
      return;
   }

   r->acceptSubscriptionRequest(address);
}

int XmppRosterInterface::rejectSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::rejectSubscriptionRequestImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterInterface::rejectSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterInterface::rejectSubscriptionRequestImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::rejectSubscriptionRequest called with invalid roster handle: " << roster);
      return;
   }

   r->rejectSubscriptionRequest(address);
}

int XmppRosterInterface::cancelAcceptedSubscription(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::cancelAcceptedSubscriptionImpl, this, roster, address, message));
   return kSuccess;
}

void XmppRosterInterface::cancelAcceptedSubscriptionImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message)
{
   StackLog(<< "XmppRosterInterface::cancelAcceptedSubscriptionImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::cancelAcceptedSubscription called with invalid roster handle: " << roster);
      return;
   }

   r->cancelAcceptedSubscription(address, message);
}

int XmppRosterInterface::addRosterItem(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::addRosterItemImpl, this, roster, address, displayName, groups));
   return kSuccess;
}

void XmppRosterInterface::addRosterItemImpl(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups)
{
   StackLog(<< "XmppRosterInterface::addRosterItemImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::addRosterItem called with invalid roster handle: " << roster);
      return;
   }

   r->addRosterItem(address, displayName, groups);
}

int XmppRosterInterface::updateRosterItem(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::updateRosterItemImpl, this, roster, address, displayName, groups));
   return kSuccess;
}

void XmppRosterInterface::updateRosterItemImpl(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups)
{
   StackLog(<< "XmppRosterInterface::updateRosterItemImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::updateRosterItem called with invalid roster handle: " << roster);
      return;
   }

   r->updateRosterItem(address, displayName, groups);
}

int XmppRosterInterface::removeRosterItem(XmppRosterHandle roster, const cpc::string& address)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::removeRosterItemImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterInterface::removeRosterItemImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterInterface::removeRosterItemImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::removeRosterItem called with invalid roster handle: " << roster);
      return;
   }

   r->removeRosterItem(address);
}

int XmppRosterInterface::subscribePresence(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups,
   const cpc::string& message)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::subscribePresenceImpl, this, roster, address, displayName, groups, message));
   return kSuccess;
}

void XmppRosterInterface::subscribePresenceImpl(
   XmppRosterHandle roster,
   const cpc::string& address,
   const cpc::string& displayName,
   const cpc::vector<cpc::string>& groups,
   const cpc::string& message)
{
   StackLog(<< "XmppRosterInterface::subscribePresenceImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::subscribePresence called with invalid roster handle: " << roster);
      return;
   }

   r->subscribePresence(address, displayName, groups, message);
}

int XmppRosterInterface::unsubscribePresence(XmppRosterHandle roster, const cpc::string& address)
{
   mAccountIf->post(resip::resip_bind(&XmppRosterInterface::unsubscribePresenceImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterInterface::unsubscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterInterface::unsubscribePresenceImpl(): address: " << address << " handle: " << roster);
   XmppRosterImpl* r = getRoster(roster);
   if (r == NULL)
   {
      FIRE_ERROR("XmppRosterManager::unsubscribePresence called with invalid roster handle: " << roster);
      return;
   }

   r->unsubscribePresence(address);
}

int XmppRosterInterface::getRosterState(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems)
{
   __verify(rosterItems.empty(), rosterItems.clear());

   resip::Lock locker (mCacheLock);

   CacheMap::iterator it = mCache.find(roster);

   if (it == mCache.end()) return kError;

   for (CacheMap::mapped_type::iterator mit = it->second.begin(); mit != it->second.end(); ++mit)
   {
      rosterItems.push_back(mit->second);
   }

   return kSuccess;
}

int XmppRosterInterface::getRosterState(XmppAccount::XmppAccountHandle account, RosterItemList& rosterItems)
{
   rosterItems.roster = (-1);
   rosterItems.items.clear();
   std::shared_ptr<XmppRosterImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      DebugLog(<< "XmppRosterInterface::getRosterState(): no account found for handle: " << account);
      return kError;
   }

   rosterItems.roster = impl->getHandle();

   return getRosterState(rosterItems.roster, rosterItems.items);
}

int XmppRosterInterface::getAllRosterState(std::map<XmppAccount::XmppAccountHandle, RosterItemList>& rosterItems)
{
   rosterItems.clear();
   for (ImplMap::iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      RosterItemList items;
      getRosterState(it->first, items);
      rosterItems[it->first] = items;
   }

   return kSuccess;
}

XmppRosterImpl* XmppRosterInterface::getRoster(XmppRosterHandle rosterHandle)
{
   for (ImplMap::iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      std::shared_ptr<XmppRosterImpl> impl = it->second;
      if (impl->getHandle() == rosterHandle) return impl.get();
   }

   return NULL;
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::SubscriptionState& state)
{
   switch (state)
   {
      case SubscriptionState_None: return "None";
      case SubscriptionState_None_OutPending: return "OutPending";
      case SubscriptionState_None_InPending: return "InPending";
      case SubscriptionState_None_InOutPending: return "InOutPending";
      case SubscriptionState_Out: return "Out";
      case SubscriptionState_Out_InPending: return "Out_InPending";
      case SubscriptionState_In: return "In";
      case SubscriptionState_In_OutPending: return "In_OutPending";
      case SubscriptionState_InOut: return "InOut";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::PresenceType& type)
{
   switch (type)
   {
      case PresenceType_Available: return "available";
      case PresenceType_Chat: return "chat";
      case PresenceType_Away: return "away";
      case PresenceType_DND: return "dnd";
      case PresenceType_XA: return "xa";
      case PresenceType_Unavailable: return "unavailable";
      case PresenceType_Probe: return "probe";
      case PresenceType_Error: return "error";
      case PresenceType_Invalid: return "invalid";
      case PresenceType_Unknown: return "unknown";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::UserActivityGeneralType& type)
{
   switch (type)
   {
      case ActivityDoingChores: return "ActivityDoingChores";
      case ActivityDrinking: return "ActivityDrinking";
      case ActivityEating: return "ActivityEating";
      case ActivityExercising: return "ActivityExercising";
      case ActivityGrooming: return "ActivityGrooming";
      case ActivityHavingAppointment: return "ActivityHavingAppointment";
      case ActivityInactive: return "ActivityInactive";
      case ActivityRelaxing: return "ActivityRelaxing";
      case ActivityTalking: return "ActivityTalking";
      case ActivityTraveling: return "ActivityTraveling";
      case ActivityUndefinedGeneralType: return "ActivityUndefinedGeneralType";
      case ActivityWorking: return "ActivityWorking";
      case ActivityInvalidGeneralType: return "ActivityInvalidGeneralType";
      default: return "invalid";
   };
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::UserActivitySpecificType& type)
{
   switch (type)
   {
      case ActivityAtTheSpa: return "ActivityAtTheSpa";
      case ActivityBrushingTeeth: return "ActivityBrushingTeeth";
      case ActivityBuyingGroceries: return "ActivityBuyingGroceries";
      case ActivityCleaning: return "ActivityCleaning";
      case ActivityCoding: return "ActivityCoding";
      case ActivityCommuting: return "ActivityCommuting";
      case ActivityCooking: return "ActivityCooking";
      case ActivityCycling: return "ActivityCycling";
      case ActivityDancing: return "ActivityDancing";
      case ActivityDayOff: return "ActivityDayOff";
      case ActivityDoingMaintenance: return "ActivityDoingMaintenance";
      case ActivityDoingTheDishes: return "ActivityDoingTheDishes";
      case ActivityDoingTheLaundry: return "ActivityDoingTheLaundry";
      case ActivityDriving: return "ActivityDriving";
      case ActivityFishing: return "ActivityFishing";
      case ActivityGaming: return "ActivityGaming";
      case ActivityGardening: return "ActivityGardening";
      case ActivityGettingAHaircut: return "ActivityGettingAHaircut";
      case ActivityGoingOut: return "ActivityGoingOut";
      case ActivityHangingOut: return "ActivityHangingOut";
      case ActivityHavingABeer: return "ActivityHavingABeer";
      case ActivityHavingASnack: return "ActivityHavingASnack";
      case ActivityHavingBreakfast: return "ActivityHavingBreakfast";
      case ActivityHavingCoffee: return "ActivityHavingCoffee";
      case ActivityHavingDinner: return "ActivityHavingDinner";
      case ActivityHavingLunch: return "ActivityHavingLunch";
      case ActivityHavingTea: return "ActivityHavingTea";
      case ActivityHiding: return "ActivityHiding";
      case ActivityHiking: return "ActivityHiking";
      case ActivityInACar: return "ActivityInACar";
      case ActivityInAMeeting: return "ActivityInAMeeting";
      case ActivityInRealLife: return "ActivityInRealLife";
      case ActivityJogging: return "ActivityJogging";
      case ActivityOnABus: return "ActivityOnABus";
      case ActivityOnAPlane: return "ActivityOnAPlane";
      case ActivityOnATrain: return "ActivityOnATrain";
      case ActivityOnATrip: return "ActivityOnATrip";
      case ActivityOnThePhone: return "ActivityOnThePhone";
      case ActivityOnVacation: return "ActivityOnVacation";
      case ActivityOnVideoPhone: return "ActivityOnVideoPhone";
      case ActivityOther: return "ActivityOther";
      case ActivityPartying: return "ActivityPartying";
      case ActivityPlayingSports: return "ActivityPlayingSports";
      case ActivityPraying: return "ActivityPraying";
      case ActivityReading: return "ActivityReading";
      case ActivityRehearsing: return "ActivityRehearsing";
      case ActivityRunning: return "ActivityRunning";
      case ActivityRunningAnErrand: return "ActivityRunningAnErrand";
      case ActivityScheduledHoliday: return "ActivityScheduledHoliday";
      case ActivityShaving: return "ActivityShaving";
      case ActivityShopping: return "ActivityShopping";
      case ActivitySkiing: return "ActivitySkiing";
      case ActivitySleeping: return "ActivitySleeping";
      case ActivitySmoking: return "ActivitySmoking";
      case ActivitySocializing: return "ActivitySocializing";
      case ActivityStudying: return "ActivityStudying";
      case ActivitySunbathing: return "ActivitySunbathing";
      case ActivitySwimming: return "ActivitySwimming";
      case ActivityTakingABath: return "ActivityTakingABath";
      case ActivityTakingAShower: return "ActivityTakingAShower";
      case ActivityThinking: return "ActivityThinking";
      case ActivityWalking: return "ActivityWalking";
      case ActivityWalkingTheDog: return "ActivityWalkingTheDog";
      case ActivityWatchingAMovie: return "ActivityWatchingAMovie";
      case ActivityWatchingTv: return "ActivityWatchingTv";
      case ActivityWorkingOut: return "ActivityWorkingOut";
      case ActivityWriting: return "ActivityWriting";
      case ActivityInvalidSpecificType: return "ActivityInvalidSpecificType";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::ResourceItem& item)
{
   std::stringstream ss;
   ss << "resource: " << item.resource << " priority: " << item.priority << " presenceType: " << item.presenceType
      << " presenceStatusText: " << item.presenceStatusText << " userActivityGeneralType: " << item.userActivityGeneralType
      << " userActivitySpecificType: " << item.userActivitySpecificType << " userActivityText: " << item.userActivityText
      << " isCiscoRichPresence: " << item.isCiscoRichPresence << " isCiscoCustomStatus: " << item.isCiscoCustomStatus;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::RosterItem& item)
{
   std::stringstream ss;
   ss << "address: " << item.address << " displayName: " << item.displayName << " subscription: " << item.subscription
      << " groups-count: " << item.groups.size();
   for (cpc::vector<cpc::string>::const_iterator i = item.groups.begin(); i != item.groups.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   ss << " resources-count: " << item.resources.size();
   for (cpc::vector<ResourceItem>::const_iterator j = item.resources.begin(); j != item.resources.end(); j++)
   {
      ss << " {" << (*j) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppRoster::XmppRosterState& state)
{
   std::stringstream ss;
   ss << "roster: " << state.roster << " account: " << state.account << " items-count: " << state.rosterItems.size();
   for (cpc::vector<CPCAPI2::XmppRoster::RosterItem>::const_iterator i = state.rosterItems.begin(); i != state.rosterItems.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::SubscriptionState& state)
{
   os << CPCAPI2::XmppRoster::get_debug_string(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::PresenceType& type)
{
   os << CPCAPI2::XmppRoster::get_debug_string(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::UserActivityGeneralType& type)
{
   os << CPCAPI2::XmppRoster::get_debug_string(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::UserActivitySpecificType& type)
{
   os << CPCAPI2::XmppRoster::get_debug_string(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::ResourceItem& item)
{
   os << CPCAPI2::XmppRoster::get_debug_string(item);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::RosterItem& item)
{
   os << CPCAPI2::XmppRoster::get_debug_string(item);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppRoster::XmppRosterState& state)
{
   os << CPCAPI2::XmppRoster::get_debug_string(state);
   return os;
}

} // namespace XmppRoster

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_MODULE
