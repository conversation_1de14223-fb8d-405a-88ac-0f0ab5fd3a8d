#include "brand_branded.h"

#include "interface/public/xmpp/XmppFileTransferManager.h"
#include "interface/public/xmpp/XmppFileTransferState.h"

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)
#include "XmppFileTransferManagerInterface.h"
#include "XmppFileTransferStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace XmppFileTransfer
{

XmppFileTransferManager* XmppFileTransferManager::getInterface( CPCAPI2::Phone* cpcPhone )
{
#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppFileTransferManagerInterface>(phone, "XmppFileTransferManagerInterface");
#else
   return NULL;
#endif
}

XmppFileTransferStateManager* XmppFileTransferStateManager::getInterface(XmppFileTransferManager* manager)
{
#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)
   XmppFileTransferManagerInterface* parent = dynamic_cast<XmppFileTransferManagerInterface*>(manager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<XmppFileTransferStateImpl>(phone, "XmppFileTransferStateManager", parent);
#else
   return NULL;
#endif
}

}
}
