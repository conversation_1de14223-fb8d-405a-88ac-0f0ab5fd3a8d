#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_HANDLER_INTERNAL_H)
#define CPCAPI2_XMPP_VCARD_HANDLER_INTERNAL_H

#include "cpcapi2defs.h"
#include <xmpp/XmppVCardHandler.h>

namespace CPCAPI2
{

namespace XmppVCard
{

struct XmppVCardCreatedResultEvent
{
   CPCAPI2::XmppAccount::XmppAccountHandle account;
};
   
/**
 * The handler for internal events on Xmpp VCard; passed in XmppAccountInterface::setInternalHandler().
*/
class XmppVCardHandlerInternal : public XmppVCardHandler
{
   
public:

   /**
    * Notifies the application when the create vcard request has been completed
   */
   virtual int onCreateVCardResult(CPCAPI2::XmppVCard::XmppVCardHandle vcard, const XmppVCardCreatedResultEvent& args) = 0;

};

} // namespace XmppVCard

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_VCARD_HANDLER_INTERNAL_H
