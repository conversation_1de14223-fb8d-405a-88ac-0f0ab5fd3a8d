#include "CpcXep.h"
#include "CpcXepMessageCorrection.h"

const std::string CpcXepMessageCorrection::XMLNS_MESSAGE_CORRECTION = "urn:xmpp:message-correct:0";

CpcXepMessageCorrection::CpcXepMessageCorrection(const std::string& id)
   : StanzaExtension(EXepMessageCorrection)
   , mId(id)
{
}

CpcXepMessageCorrection::CpcXepMessageCorrection(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageCorrection)
{
   if (tag == NULL) return;
   mId = tag->findAttribute("id");

}

const std::string& CpcXepMessageCorrection::filterString() const
{
   static const std::string filter =
      "/message/replace[@xmlns='" + XMLNS_MESSAGE_CORRECTION + "']";
   return filter;
}

gloox::StanzaExtension* CpcXepMessageCorrection::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageCorrection(tag);
}

gloox::Tag* CpcXepMessageCorrection::tag() const
{
   if (mId.empty()) return NULL;

   gloox::Tag* t = new gloox::Tag("replace", "xmlns", XMLNS_MESSAGE_CORRECTION);
   t->addAttribute("id", mId);

   return t;
}

gloox::StanzaExtension* CpcXepMessageCorrection::clone() const
{
   return new CpcXepMessageCorrection(*this);
}
