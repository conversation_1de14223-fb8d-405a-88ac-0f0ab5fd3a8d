//
//  CpcXepMessageReaction.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageReaction.h"

const std::string CpcXepMessageReaction::XMLNS_MESSAGE_REACTION = "urn:xmpp:reactions:0";

CpcXepMessageReaction::CpcXepMessageReaction(std::string targetId, std::vector<std::string> reactions)
   : StanzaExtension(EXepMessageReaction)
   , mTargetId(targetId)
   , mReactions(reactions)
   , mValid(true)
{
}

CpcXepMessageReaction::CpcXepMessageReaction(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageReaction)
   , mValid(false)
{
   if (tag == NULL) return;

   if (auto reactions = tag->findTag("/message/reactions"))
   {
      mTargetId = reactions->findAttribute( "id" );

      mReactions.clear();
      const gloox::TagList& childTags = reactions->children();
      for (gloox::TagList::const_iterator it=childTags.begin(); it!=childTags.end(); ++it)
      {
         if ( (*it)->name() == "reaction" )
         {
            mReactions.push_back((*it)->cdata());
         }
      }
   }

   mValid = true;
}

const std::string& CpcXepMessageReaction::filterString() const
{
   static const std::string filter =
      "/message/reactions[@xmlns='" + XMLNS_MESSAGE_REACTION + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageReaction::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageReaction(tag);
}

gloox::Tag* CpcXepMessageReaction::tag() const
{
   gloox::Tag* t = new gloox::Tag("reactions", "xmlns", XMLNS_MESSAGE_REACTION);

   if (!mValid) return NULL;

   t->addAttribute("id", mTargetId);

   for(const std::string& reaction : mReactions) 
      new gloox::Tag( t, "reaction", reaction );

   return t;
}

gloox::StanzaExtension* CpcXepMessageReaction::clone() const
{
   return new CpcXepMessageReaction(*this);
}
