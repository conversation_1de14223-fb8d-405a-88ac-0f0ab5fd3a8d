// XmppConnection follows reactor pattern hence no need for m_sendMutex

#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include <random>
#include <fcntl.h>
#include <thread>

#include "XmppConnection.h"
#include <cpcapi2utils.h>
#include "util/LogSubsystems.h"
#include <resip/stack/InternalTransport.hxx>
#include "rutil/DnsUtil.hxx"
#include "rutil/Logger.hxx"
#include "rutil/Socket.hxx"
#include "rutil/Time.hxx"
#include "util/ReactorHelpers.h"

// MSG_NOSIGNAL does not exists on OS X
#if defined(__APPLE__) || defined(__MACH__)
# ifndef MSG_NOSIGNAL
#   define MSG_NOSIGNAL SO_NOSIGPIPE
# endif
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_CONNECTION

using namespace resip;

namespace CPCAPI2
{

static const int kProtocol = 5;
static const int MaxBufferSize = 4 * 1024 * 1024;

static auto sRecvReactorPool = []() {
   std::vector<resip::MultiReactor*> pool(
#if CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1 && CPCAPI2_AUTO_TEST == 0
      std::thread::hardware_concurrency()
#else
      2 // bliu: the multithreaded configuration should be more aligned with the non-auto test setup.
#endif
      );

   for (size_t i = 0; i < pool.size(); ++i)
   {
      std::ostringstream oss;
      oss << "RecvReactorPool" << i;
      pool[i] = new resip::MultiReactor(oss.str().c_str());
      pool[i]->start();
   }

   return pool;
}();

static void addToRecvReactorPool(XmppConnectionTcpClient* client)
{
   assert(client->state() == gloox::StateConnected);

#ifdef _WIN32
   static std::atomic<size_t> seq = 0;
#else
   static std::atomic<size_t> seq = ATOMIC_VAR_INIT(0);
#endif

   resip::MultiReactor* r = sRecvReactorPool[seq++ % sRecvReactorPool.size()];
   r->registerEventHandler(client);
   client->setReactorWorker(r);
}

static void removeFromRecvReactorPool(XmppConnectionTcpClient* client)
{
   // bliu: client->getReactorWorker() may have race condition, thus make a copy
   auto worker = client->getReactorWorker();

   DebugLog(<< "removeFromRecvReactorPool(): client=" << client << " (" << (client->getReactor().isCurrentThread() ? "SDK" : "worker") << " thread) worker=" << (worker == NULL ? 0 : worker->getThreadId()) << " socket=" << client->socket());

   if (worker == NULL) return;

   // to avoid deadlock, do not call mReactor.execute() from worker reactor
   worker->execute(resip::resip_bind(&resip::MultiReactor::unregisterEventHandler, worker, client));
   client->setReactorWorker(NULL);
}

XmppConnectionTcpClient::XmppConnectionTcpClient(
   resip::MultiReactor& reactor,
   gloox::ConnectionDataHandler* cdh,
   const gloox::LogSink& logInstance,
   const std::string& server,
   int port,
   XmppAccount::IpVersion ipVersion,
   unsigned int connectTimeOut,
   const std::string& serviceName,
   resip::TransportType transportType,
   DnsSettings dnsSettings)

   : gloox::ConnectionTCPClient(cdh, logInstance, server, port)
   , mReactor(reactor)
   , mServiceName(serviceName)
   , mTransportType(transportType)
   , mSrvResultPort(0)
   , mConnectTimeOut(connectTimeOut)
   , mConnectAttempts(0)
   , mTimeOut(0)
   , mDnsStub(new resip::DnsStub(dnsSettings.nameServers, dnsSettings.includeSystemDnsServers))
   , mError(gloox::ConnNoError)
   , mIpVersion(ipVersion)
   , mIndex(1)
   , mTimer(new resip::DeadlineTimer<resip::MultiReactor>(reactor))
   , mReactorWorker(NULL)
{
   mReactor.registerEventHandler(this);
   DebugLog(<< "XmppConnectionTcpClient(): " << this);
}

XmppConnectionTcpClient::~XmppConnectionTcpClient()
{
   DebugLog(<< "~XmppConnectionTcpClient(): " << this << " (" << (mReactor.isCurrentThread() ? "SDK" : "worker") << " thread) socket=" << m_socket);

   // bliu: although removeFromRecvReactorPool() is called in cleanup()
   // subclass' cleanup() won't be called in base class' destructor
   cleanup();

   registerConnectionDataHandler(NULL); // Reset the m_handler to NULL, as the gloox::client is destroyed

   // cleanup() may re-register 'this' to mReactor
   mReactor.unregisterEventHandler(this);

   mReactor.safeDelete(mTimer); // timer is associated with main SDK reactor
   mReactor.safeDelete(mDnsStub); //DnsStub is associated with main SDK reactor

   // bliu: OBELISK-5687 wait until safeDelete() finishes
   // need to confirm there is no deadlock
   mReactor.execute(new resip::ReadCallbackNoOp());
}

void XmppConnectionTcpClient::_release()
{
   assert(mReactor.isCurrentThread());

   reactorSafeReleaseAfter(&mReactor);
}

bool XmppConnectionTcpClient::isWritable()
{
   return mBuffer.empty();
}

void XmppConnectionTcpClient::connectCompleted()
{
   // bliu: keep m_socket as nonblocking or send() may block
   //if (!makeSocketBlocking(m_socket))
   //{
   //   connectFailed(gloox::ConnIoError);
   //   return;
   //}

   InfoLog(<< "Connected socket=" << m_socket << " " << localInterface() << ":" << localPort());

   m_state = gloox::StateConnected;
   m_cancel = false;

   mReactor.unregisterEventHandler(this);
   addToRecvReactorPool(this);

   m_handler->handleConnect(this);
}

void XmppConnectionTcpClient::connectFailed(gloox::ConnectionError error)
{
   m_state = gloox::StateDisconnected;

   m_handler->handleDisconnect(this, error);
}

void XmppConnectionTcpClient::process(ReactorEventHandler::FdSetType& fdset)
{
   switch (m_state)
   {
   case gloox::StateConnecting:
      assert(mReactor.isCurrentThread());
      processResolveTask(fdset);
      processConnectTask(fdset);
      break;

   case gloox::StateConnected:
      assert(!mReactor.isCurrentThread());
      processDataTask(fdset);
      break;

   default:
      break;
   }
}

void XmppConnectionTcpClient::buildFdSet(ReactorEventHandler::FdSetType& fdset)
{
   switch (m_state)
   {
   case gloox::StateConnecting:
      assert(mReactor.isCurrentThread());
      buildFdSetForResolveTask(fdset);
      buildFdSetForConnectTask(fdset);
      break;

   case gloox::StateConnected:
      assert(!mReactor.isCurrentThread());
      buildFdSetForDataTask(fdset);
      break;

   default:
      break;
   }
}

unsigned int XmppConnectionTcpClient::getTimeTillNextProcessMS()
{
   int nextMs = 5 * 60 * 1000;

   switch (m_state)
   {
   case gloox::StateConnecting:
      // if we're connecting, to adhere to the app's requested connectTimeOut
      // settings value, make sure we get processing time even if there is no socket activity.
      nextMs = 1000;
      break;

   default:
      break;
   }

   return nextMs;
}

void XmppConnectionTcpClient::processResolveTask(ReactorEventHandler::FdSetType& fdset)
{
   if (m_state != gloox::StateConnecting) return;

   mDnsStub->process(fdset);
}

void XmppConnectionTcpClient::processConnectTask(ReactorEventHandler::FdSetType& fdset)
{
   if (m_socket == INVALID_SOCKET || m_state != gloox::StateConnecting) return;

   if (mConnectTimeOut > 0 && resip::ResipClock::getTimeSecs() >= mTimeOut)
   {
      currentHostFailed(gloox::ConnIoError);
   }
   else if (fdset.hasException(m_socket))
   {
      currentHostFailed(gloox::ConnIoError);
   }
   else if (fdset.readyToRead(m_socket) || fdset.readyToWrite(m_socket))
   {
      int optval;
      socklen_t optlen = sizeof(optval);
      if (getsockopt(m_socket, SOL_SOCKET, SO_ERROR, reinterpret_cast<char*>(&optval), &optlen) != 0)
      {
         connectFailed(gloox::ConnIoError);
      }

      if (optval == 0)
      {
         connectCompleted();
      }
      else
      {
         DebugLog (<<"Connect failed: err=" << optval);
         currentHostFailed(gloox::ConnConnectionRefused);
      }
   }
}

void XmppConnectionTcpClient::processDataTask(ReactorEventHandler::FdSetType& fdset)
{
   if (m_socket == INVALID_SOCKET || m_state != gloox::StateConnected) return;

   if (fdset.readyToRead(m_socket))
   {
      recv(-1);
   }
}

void XmppConnectionTcpClient::buildFdSetForResolveTask(ReactorEventHandler::FdSetType& fdset)
{
   mDnsStub->buildFdSet(fdset);
}

void XmppConnectionTcpClient::buildFdSetForConnectTask(ReactorEventHandler::FdSetType& fdset)
{
   if (m_socket == INVALID_SOCKET || m_state != gloox::StateConnecting) return;

   fdset.setRead(m_socket);
   fdset.setWrite(m_socket);
   fdset.setExcept(m_socket);
}

void XmppConnectionTcpClient::buildFdSetForDataTask(ReactorEventHandler::FdSetType& fdset)
{
   if (m_socket == INVALID_SOCKET || m_state != gloox::StateConnected) return;

   fdset.setRead(m_socket);
}

void XmppConnectionTcpClient::lookup(const resip::Data& name)
{
   if (mDnsLookupSet.insert(mIndex).second)
   {
      switch (mIpVersion)
      {
      case XmppAccount::IpVersion_V6:
#ifdef IPPROTO_IPV6
         mDnsStub->lookup<RR_AAAA>(name, kProtocol, this, reinterpret_cast<void*>(mIndex));
#else
         connectFailed(XmppConnectionTcpClient::HostNotFound);
#endif
         break;

      case XmppAccount::IpVersion_Auto_PreferV6:
#ifdef IPPROTO_IPV6
         mDnsStub->lookup<RR_AAAA>(name, kProtocol, this, reinterpret_cast<void*>(mIndex));
#else
         mDnsStub->lookup<RR_A>(name, kProtocol, this, reinterpret_cast<void*>(mIndex));
#endif
         break;

      case XmppAccount::IpVersion_V4:
      case XmppAccount::IpVersion_Auto_PreferV4:
      default:
         mDnsStub->lookup<RR_A>(name, kProtocol, this, reinterpret_cast<void*>(mIndex));
         break;
      }
   }
   else
   {
      ErrLog(<< "duplicate DNS lookup index " << mIndex);
   }

   ++mIndex;
}

void XmppConnectionTcpClient::startAsyncLookup()
{
   if (resip::DnsUtil::isIpAddress(m_server.c_str()))
   {
      resip::Tuple dest (m_server.c_str(), m_port > 0 ? m_port : XMPP_CLIENT_SERVICE_PORT, mTransportType);
      mRecords.push_back(dest);
      startAsyncConnect();
      return;
   }

   if (m_port > 0)
   {
      lookup(m_server.c_str());
      return;
   }

   std::string srv = "_" + mServiceName + "._" + std::string(resip::toDataLower(mTransportType).c_str()) + "." + m_server;
   // Try to resolve SRV records first
   mDnsStub->lookup<RR_SRV>(srv.c_str(), kProtocol, this);
}

void XmppConnectionTcpClient::startAsyncConnect()
{
   if (m_socket != INVALID_SOCKET || m_state != gloox::StateConnecting) return;

   if (mRecords.empty())
   {
      if (!mDnsLookupSet.empty()) return;

      if (!mSrvRecords.empty())
      {
         selectNextSrvRecord();
         return;
      }

      if (mConnectAttempts == 0)
      {
         connectFailed(gloox::ConnDnsError);
         return;
      }

      if (mError == gloox::ConnNoError) mError = gloox::ConnIoError;

      connectFailed(mError);
      return;
   }

   if (m_socket != INVALID_SOCKET) return;

   ++mConnectAttempts;
   mTimeOut = resip::ResipClock::getTimeSecs() + mConnectTimeOut;

   Tuple dest = mRecords.front();
   mRecords.pop_front();
   mTarget = dest;

   m_socket = InternalTransport::socket(mTransportType, dest.toGenericIPAddress().isVersion4()? resip::V4 : resip::V6);

   InfoLog(<< "Connecting to socket=" << m_socket << " " << dest);

   if (m_socket == INVALID_SOCKET)
   {
      connectFailed(gloox::ConnIoError);
      return;
   }

   if (!makeSocketNonBlocking(m_socket))
   {
      connectFailed(gloox::ConnIoError);
      return;
   }

   if (resip::setSocketRcvBufLen(m_socket, 1024 * 1024) < 16 * 1024)
   {
      InfoLog(<< "cannot adjust receive buffer size socket=" << m_socket);
   }

   if (resip::setSocketSndBufLen(m_socket, 1024 * 1024) < 16 * 1024)
   {
      InfoLog(<< "cannot adjust send buffer size socket=" << m_socket);
   }

#ifdef WIN32
  #define PORTABLE_CONNECT_EINTR WSAEINTR
  #define PORTABLE_CONNECT_EINPROGRESS WSAEWOULDBLOCK
  #define PORTABLE_CONNECT_ECONNREFUSED WSAECONNREFUSED
  #define PORTABLE_CONNECT_ENETUNREACH WSAENETUNREACH
  #define PORTABLE_CONNECT_EHOSTUNREACH WSAEHOSTUNREACH
#else // WIN32
  #define PORTABLE_CONNECT_EINTR EINTR
  #define PORTABLE_CONNECT_EINPROGRESS EINPROGRESS
  #define PORTABLE_CONNECT_ECONNREFUSED ECONNREFUSED
  #define PORTABLE_CONNECT_ENETUNREACH ENETUNREACH
  #define PORTABLE_CONNECT_EHOSTUNREACH EHOSTUNREACH
#endif // WIN32

   int ret;
   int err;
   do {
      const sockaddr& serverAddress = dest.getSockaddr();
      ret = ::connect(m_socket, &serverAddress, dest.length());
      if (ret != SOCKET_ERROR)
      {
         connectCompleted();
         return;
      }

#ifdef __THREADX
      err = tfGetSocketError(m_socket);
#else
      err = getErrno();
#endif

      if (err == PORTABLE_CONNECT_EINPROGRESS)
      {
         return;
      }
   } while (err == PORTABLE_CONNECT_EINTR);

   DebugLog (<<"Connect failed: err=" << err << " socket=" << m_socket);
   resip::closeSocket(m_socket);
   m_socket = INVALID_SOCKET;

   switch (err)
   {
   case PORTABLE_CONNECT_ECONNREFUSED:
      mError = gloox::ConnConnectionRefused;
      break;

   case PORTABLE_CONNECT_ENETUNREACH:
   case PORTABLE_CONNECT_EHOSTUNREACH:
      mError = gloox::ConnDnsError;
      break;

   default:
      mError = gloox::ConnIoError;
   }

   startAsyncConnect();
}

void XmppConnectionTcpClient::currentHostFailed(gloox::ConnectionError error)
{
   mError = error;
   resip::closeSocket(m_socket);
   m_socket = INVALID_SOCKET;
   startAsyncConnect();
}

void XmppConnectionTcpClient::selectNextSrvRecord()
{
   if (mSrvRecords.empty())
   {
      connectFailed(gloox::ConnDnsError);
      return;
   }

   SrvRecord srv = mSrvRecords.front();
   mSrvRecords.pop_front();

   mSrvResultPort = srv.port;

   lookup(srv.target.c_str());
}

void XmppConnectionTcpClient::arrangeSrvRecords()
{
   // get sum of all weights
   double totalWeight = 0;
   for (std::list<SrvRecord>::iterator it = mSrvRecords.begin(); it != mSrvRecords.end(); ++it)
   {
      totalWeight += it->weight;
   }

   if (totalWeight==0)
   {
      totalWeight = 1;
   }

   // assign weighted random value
   std::random_device rd;
   std::default_random_engine dre(rd());
   std::uniform_real_distribution<double> di(0,totalWeight);
   for (std::list<SrvRecord>::iterator it = mSrvRecords.begin(); it != mSrvRecords.end(); ++it)
   {
      double randValue = di(dre);
      it->weightRandomValue = it->weight * randValue;
   }

   // now sort items first by priority then by weighted random value
   // TODO: lambda expression
   mSrvRecords.sort(
      [](const SrvRecord& a, const SrvRecord& b) -> bool
      {
         if (a.priority < b.priority)
            return true;
         if (a.priority > b.priority)
            return false;

         return a.weightRandomValue > b.weightRandomValue;
      }
   );
}

void XmppConnectionTcpClient::onDnsResult(const resip::DNSResult<resip::DnsHostRecord>& res)
{
   if (res.status == 0)
   {
      for (std::vector<resip::DnsHostRecord>::const_iterator it = res.records.begin(); it != res.records.end(); it++)
      {
         Tuple dest(it->host(), mSrvResultPort > 0 ? mSrvResultPort : m_port > 0 ? m_port : XMPP_CLIENT_SERVICE_PORT, mTransportType, it->name());
         mRecords.push_back(dest);
      }
   }

#ifdef IPPROTO_IPV6
   if (mIpVersion == XmppAccount::IpVersion_Auto_PreferV4)
   {
      if (mDnsLookupSet.insert(mIndex).second)
      {
         mDnsStub->lookup<RR_AAAA>(res.domain, kProtocol, this, reinterpret_cast<void*>(mIndex));
      }
      else
      {
         ErrLog(<< "duplicate DNS lookup index " << mIndex);
      }

      ++mIndex;
   }
#endif

   mDnsLookupSet.erase(static_cast<int>(reinterpret_cast<intptr_t>(res.userData)));

   startAsyncConnect();
}

void XmppConnectionTcpClient::onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>& res)
{
   if (res.status == 0)
   {
      for (std::vector<resip::DnsAAAARecord>::const_iterator it = res.records.begin(); it != res.records.end(); it++)
      {
         const int port = mSrvResultPort > 0 ? mSrvResultPort : m_port > 0 ? m_port : XMPP_CLIENT_SERVICE_PORT;

#ifdef IPPROTO_IPV6
         Tuple dest(it->v6Address(), port, mTransportType, it->name());
         mRecords.push_back(dest);
#endif
      }
   }

   if (mIpVersion == XmppAccount::IpVersion_Auto_PreferV6)
   {
      if (mDnsLookupSet.insert(mIndex).second)
      {
         mDnsStub->lookup<RR_A>(res.domain, kProtocol, this, reinterpret_cast<void*>(mIndex));
      }
      else
      {
         ErrLog(<< "duplicate DNS lookup index " << mIndex);
      }

      ++mIndex;
   }

   mDnsLookupSet.erase(static_cast<int>(reinterpret_cast<intptr_t>(res.userData)));

   startAsyncConnect();
}

void XmppConnectionTcpClient::onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>& res)
{
   DebugLog(<< "XmppConnectionTcpClient::onDnsResult (Srv) status:" << res.status << ", records:" << res.records.size());
   if (res.status == 0 && !res.records.empty())
   {
      for (std::vector<resip::DnsSrvRecord>::const_iterator it = res.records.begin(); it != res.records.end(); it++)
      {
         SrvRecord srvRecord;
         srvRecord.target = it->target().c_str();
         srvRecord.port = it->port();
         srvRecord.priority = it->priority();
         srvRecord.weight = it->weight();

         mSrvRecords.push_back(srvRecord);
      }

      if (!mSrvRecords.empty())
      {
         arrangeSrvRecords();
         selectNextSrvRecord();
      }
   }
   else
   {
      // If SRV lookup fails, try host lookup
      mSrvResultPort = 0;

      lookup(m_server.c_str());
   }
}

void XmppConnectionTcpClient::onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>& res)
{
   connectFailed(gloox::ConnDnsError);
}

void XmppConnectionTcpClient::onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>& res)
{
   if (res.status == 0)
   {
      for (std::vector<resip::DnsCnameRecord>::const_iterator it = res.records.begin(); it != res.records.end(); it++)
      {
         lookup(it->name());
      }
   }
   else
   {
      connectFailed(gloox::ConnDnsError);
   }
}

void XmppConnectionTcpClient::onTimer(unsigned short timerId, void* appState)
{
   sendBuffer();
}

void XmppConnectionTcpClient::cleanup()
{
   assert(mReactor.isCurrentThread());

   // Note that this connection cleanup is initiated as a result of the disconnect request on the xmpp account, but is actually
   // triggered from the base gloox::ClientBase::disconnect. This is executed on the master phone thread, but the receive buffer
   // may still be populated that is running from the reactor pool
   DebugLog(<< "XmppConnectionTcpClient::cleanup(): " << this << " (" << (mReactor.isCurrentThread() ? "SDK" : "worker") << " thread) socket=" << m_socket);

   mTimer->cancel();

   // two cases:
   // 1. cleanup() is followed by ~XmppConnectionTcpClient()
   // 2. cleanup() is not followed by ~XmppConnectionTcpClient(), e.g. SOCKS5Bytestream::connect()
   // #2 may require 'this' to be re-registered to mReactor
   removeFromRecvReactorPool(this);
   mReactor.registerEventHandler(this);

   gloox::ConnectionTCPClient::cleanup();
}

gloox::ConnectionError XmppConnectionTcpClient::recv(int timeout)
{
   assert(!mReactor.isCurrentThread());

   m_recvMutex.lock();

   if (m_cancel || m_socket < 0)
   {
      m_recvMutex.unlock();
      return gloox::ConnNotConnected;
   }

   if (!dataAvailable(timeout))
   {
      m_recvMutex.unlock();
      return gloox::ConnNoError;
   }

   // DCM: old code: int size = static_cast<int>( ::recv( m_socket, m_buf, m_bufsize, 0 ) );
   // DCM begin
   int size;

#if defined( _WIN32 ) && !defined( __SYMBIAN32__ )
   size = static_cast<int>(::recv(m_socket, m_buf, m_bufsize, 0));
#else
   do {
      size = static_cast<int>(::recv(m_socket, m_buf, m_bufsize, 0));
   } while (size == -1 && errno == EINTR);
#endif

   if (size < 0)
   {
#if defined( _WIN32 ) && !defined( __SYMBIAN32__ )
      WarningLog(<< "recv() failed socket=" << m_socket << " WSAGetLastError: " << ::WSAGetLastError());
#else
      WarningLog(<< "recv() failed socket=" << m_socket << " errno: " << errno);
#endif
   }
   else if (size == 0)
   {
      DebugLog(<<"recv() returned zero socket=" << m_socket); // DCM
   }
   // DCM end
   if (size > 0)
      m_totalBytesIn += size;

   m_recvMutex.unlock();

   if (size <= 0)
   {
      if (size == -1)
      {
         // recv() failed for an unexpected reason
#if defined( _WIN32 ) && !defined( __SYMBIAN32__ )
         WarningLog(<< "recv() failed socket=" << m_socket << " WSAGetLastError: " << ::WSAGetLastError());
#else
         WarningLog(<< "recv() failed socket=" << m_socket << " errno: " << errno);
#endif
      }

// OBELISK-6136
#if CPCAPI2_AUTO_TEST
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
#endif

      gloox::ConnectionError error = (size ? gloox::ConnIoError : gloox::ConnStreamClosed);
      mReactor.post(resip::resip_safe_bind(&XmppConnectionTcpClient::handleDisconnect, this, error));

      // bliu: make sure this is no long running in reactor pool after disconnection
      // notice that a closed socket will always be readable and recv() returns 0
      // OBELISK-6136: removeFromRecvReactorPool() should only be called right before returning
      removeFromRecvReactorPool(this);

      return error;
   }

   m_buf[size] = '\0';

   mReactor.post(resip::resip_safe_bind(&XmppConnectionTcpClient::handleReceivedData, this, std::string(m_buf, size)));

   return gloox::ConnNoError;
}

gloox::ConnectionError XmppConnectionTcpClient::connect()
{
   // FIXME CHECKME
   if (!m_handler)
   {
      return gloox::ConnNotConnected;
   }

   if (m_socket >= 0 && m_state > gloox::StateDisconnected)
   {
      return gloox::ConnNoError;
   }

   m_state = gloox::StateConnecting;

   startAsyncLookup();

   return gloox::ConnNoError;
}

bool XmppConnectionTcpClient::send(const std::string& data)
{
   mBuffer.append(data.data(), data.size());

   if (mBuffer.empty() || m_socket < 0)
   {
      WarningLog(<< "cannot send buffer=" << mBuffer.size() << " socket=" << m_socket);
      return false;
   }

   if (!mTimer->isActive()) sendBuffer();

   return true;
}

void XmppConnectionTcpClient::sendBuffer()
{
   assert(mReactor.isCurrentThread());

   int sent = 0;
   size_t num = 0;
   const size_t len = mBuffer.size();

   for (; num < len; num += sent)
   {

#ifdef WIN32
      int ctbsendflg = 0;
#else
      int ctbsendflg = MSG_NOSIGNAL;
#endif

      sent = static_cast<int>(::send(m_socket, (mBuffer.data() + num), len - num, ctbsendflg));

      if (sent == -1)
      {
         int socket_err = resip::getErrno();
#ifdef WIN32
#ifdef RESIP_USE_POLL_FDSET
         if (socket_err == WSAEWOULDBLOCK || socket_err == WSAENOTCONN)
#else
         if (socket_err == WSAEWOULDBLOCK)
#endif
#else
         if (socket_err == EAGAIN || socket_err == EWOULDBLOCK || socket_err == EINTR)
#endif
         {
            // recoverable error (do not call handleDisconnect())
            sent = 0;
         }
         else
         {
#if defined( _WIN32 ) && !defined( __SYMBIAN32__ )
            WarningLog(<< "send() failed socket=" << m_socket << " WSAGetLastError: " << ::WSAGetLastError());
#else
            WarningLog(<< "send() failed socket=" << m_socket << " errno: " << errno);
#endif
         }

         break;
      }
   }

   m_totalBytesOut += num;

   mBuffer = mBuffer.substr(num, len - num);

   if (sent < 0)
   {
      if (mBuffer.size() > MaxBufferSize) WarningLog(<< "sending buffer overflow socket=" << m_socket);

      if (m_handler) m_handler->handleDisconnect(this, gloox::ConnIoError);

      return;
   }

   mTimer->cancel();

   if (!mBuffer.empty())
   {
      mTimer->expires_from_now(10);
      mTimer->async_wait(this, 0, NULL);
   }
}

// bliu: need the check of m_cancel for both handleDisconnect() and handleReceivedData()
// because it's possible m_handler has been released
// XmppFileTransferTest.OBELISK5056_receiver is able to reproduce the issue
void XmppConnectionTcpClient::handleDisconnect(gloox::ConnectionError reason)
{
   if (m_handler && !m_cancel) m_handler->handleDisconnect(this, reason);
}

void XmppConnectionTcpClient::handleReceivedData(const std::string& data)
{
   if (m_handler && !m_cancel) m_handler->handleReceivedData(this, data);
}

gloox::ConnectionBase* XmppConnectionTcpClient::newInstance() const
{
   XmppConnectionTcpClient* newConn = new XmppConnectionTcpClient(mReactor, m_handler, m_logInstance, m_server, m_port);
   return newConn;
}

XmppConnectionTcpServer::XmppConnectionTcpServer(resip::MultiReactor& reactor, gloox::ConnectionHandler* ch, const gloox::LogSink& logInstance, const std::string& ip, int port)
   : gloox::ConnectionTCPServer(this, logInstance, ip, port)
   , mReactor(reactor)
   , mConnectionHandler(ch)
{
   mReactor.registerEventHandler(this);
}

XmppConnectionTcpServer::~XmppConnectionTcpServer()
{
   mReactor.unregisterEventHandler(this);
}

void XmppConnectionTcpServer::process(ReactorEventHandler::FdSetType& fdset)
{
   if (fdset.readyToRead(m_socket))
   {
      recv(-1);
   }
}

void XmppConnectionTcpServer::buildFdSet(ReactorEventHandler::FdSetType& fdset)
{
   fdset.setRead(m_socket);
}

gloox::ConnectionTCPBase* XmppConnectionTcpServer::newConnectionTCPClient(const std::string& server, int port)
{
   return new XmppConnectionTcpClient(mReactor, NULL, m_logInstance, server, port);
}

void XmppConnectionTcpServer::handleIncomingConnection(ConnectionBase* server, ConnectionBase* connection)
{
   XmppConnectionTcpClient* client = static_cast<XmppConnectionTcpClient*>(connection);

   if (!makeSocketNonBlocking(client->socket()))
   {
      ErrLog(<< "cannot make client connection as non-blocking socket=" << client->socket());
   }

   mReactor.unregisterEventHandler(client);
   addToRecvReactorPool(client);

   mConnectionHandler->handleIncomingConnection(server, connection);
}

} // namespace CPCAPI2
#endif // CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE
