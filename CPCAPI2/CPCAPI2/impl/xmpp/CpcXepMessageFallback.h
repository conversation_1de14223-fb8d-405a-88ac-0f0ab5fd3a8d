//
//  CpcXepMessageFallback
//  BriaVoip
//
//  Created by <PERSON> on July 12, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_FALLBACK_H__
#define CPC_XEP_MESSAGE_FALLBACK_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0428} (fallback indication).
*/
class CpcXepMessageFallback : public gloox::StanzaExtension
{
public:
   CpcXepMessageFallback(std::string targetId);
   CpcXepMessageFallback(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   const std::string isFor() const { return mIsFor; }

   static const std::string XMLNS_MESSAGE_FALLBACK;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mIsFor;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_FALLBACK_H__
