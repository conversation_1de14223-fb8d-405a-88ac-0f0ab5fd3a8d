#include "brand_branded.h"

#include "interface/public/xmpp/XmppAccount.h"
#include "interface/public/xmpp/XmppAccountState.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include "XmppAccountInterface.h"
#include "XmppAccountStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
template<>
void GetInterfaceImpl<XmppAccount::XmppAccountInterface, PhoneInterface, PhoneInterface*>(XmppAccount::XmppAccountInterface*& module, PhoneInterface* phone, const cpc::string& name, PhoneInterface* p0)
{
   module = dynamic_cast<XmppAccount::XmppAccountInterface*>(phone->getInterfaceByName(name));
   if (module == NULL)
   {
      module = new XmppAccount::XmppAccountInterface(phone);
      phone->registerInterface(name, module);
      module->addSdkObserver(module);
   }
}
#endif

namespace XmppAccount
{

XmppAccountManager* XmppAccountManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppAccountInterface>(phone, "XmppAccountInterface");
#else
   return NULL;
#endif
}

const cpc::string& XmppAccountManager::getServiceId()
{
   static cpc::string serviceId = "xmppaccount";
   return serviceId;
}

XmppAccountStateManager* XmppAccountStateManager::getInterface(XmppAccountManager* cpcAcctMan)
{
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_XMPP_ACCOUNT_STATE_MODULE == 1)
   XmppAccountInterface* parent = dynamic_cast<XmppAccountInterface*>(cpcAcctMan);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<XmppAccountStateImpl>(phone, "XmppAccountStateManager", parent);
#else
   return NULL;
#endif
}

}
}
