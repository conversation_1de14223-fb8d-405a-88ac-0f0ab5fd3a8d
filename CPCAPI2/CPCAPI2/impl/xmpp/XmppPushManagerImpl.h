#pragma once

#if !defined(__CPCAPI2_XMPP_PUSH_MANAGER_IMPL_H__)
#define __CPCAPI2_XMPP_PUSH_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "xmpp/XmppPushTypes.h"
#include "xmpp/XmppPushManager.h"
#include "xmpp/XmppPushHandler.h"
#include "phone/PhoneInterface.h"
#include "XmppAccountImpl.h"
#include "XmppPushManagerInterface.h"

#include "CpcXepNotificationTigase.h"

#include <adhoc.h>
#include <adhochandler.h>
#include <registration.h>
#include <registrationhandler.h>

namespace CPCAPI2
{

namespace XmppPush
{

enum Context
{
    AdHocSupport = 100,
    AdHocCommands,
    AdHocExecute0,
    AdHocExecute1,
    AdHocExecute2
};

enum AdhocCommand
{
    RegisterDevice,
    UnregisterDevice
};

enum TigaseFeature
{
    FilterIgnoreUnknown,
    FilterMuted,
    FilterGroupchat,
    Away,
    Priority
};

struct MucRegistration
{
   MucRegistration() : mRegistration(NULL) {}
   std::string mJid;
   std::string mNick;
   gloox::Registration* mRegistration;
};

class XmppPushManagerImpl
   : public XmppAccount::XmppAccountObserver
   , public XmppAccount::XmppDiscoObserver
   , public gloox::AdhocHandler
   , public gloox::RegistrationHandler
   , public std::enable_shared_from_this<XmppPushManagerImpl>
{

public:
   XmppPushManagerImpl(PhoneInterface* phone, XmppAccount::XmppAccountImpl& account, XmppPushManagerInterface& intf);
   virtual ~XmppPushManagerImpl();

   static const std::string PUSH_REGISTER_COMMAND;
   static const std::string PUSH_UNREGISTER_COMMAND;
   static const std::string PUSH_PROVIDER_FIELD;
   static const std::string PUSH_DEVICE_FIELD;
   static const std::string PUSH_PROVIDER;

   void setHandler(XmppPushHandler* handler);
   void configurePushSettings(const XmppPushSettings& settings);
   void applyPushSettings();
   void pushRegister(const cpc::string& deviceToken);
   void pushUnregister(const cpc::string& deviceToken);
   void setIgnoreUnknownSender(bool ignoreUnknown);
   void setSendNotificationsWhileAway(bool sendWhileAway);
   void setContactMuted(const cpc::string& contactJid, bool muted = true);
   void setGroupChatFilter(const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick = "");
   void enableNotifications(bool enable);
   void registerOfflineMessageDelivery(const cpc::string& roomJid, const cpc::string& nick);

private:

   // XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onStreamManagementAck(XmppAccount::XmppAccountImpl& account) OVERRIDE {};

   // XmppDiscoObserver
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) OVERRIDE;
   virtual void onXmppDiscoCompleted() OVERRIDE;

    //gloox::AdhocHandler
   virtual void handleAdhocSupport(const gloox::JID& remote, bool support, int context) OVERRIDE;
   virtual void handleAdhocCommands(const gloox::JID& remote, const gloox::StringMap& commands, int context) OVERRIDE;
   virtual void handleAdhocError(const gloox::JID& remote, const gloox::Error* error, int context) OVERRIDE;
   virtual void handleAdhocExecutionResult(const gloox::JID& remote, const gloox::Adhoc::Command& command, int context) OVERRIDE;

   //gloox::RegistrationHandler
   virtual void handleRegistrationFields( const gloox::JID& from, int fields, std::string instructions ) OVERRIDE;
   virtual void handleAlreadyRegistered( const gloox::JID& from ) OVERRIDE;
   virtual void handleRegistrationResult( const gloox::JID& from, gloox::RegistrationResult regResult ) OVERRIDE;
   virtual void handleDataForm( const gloox::JID& from, const gloox::DataForm& form ) OVERRIDE;
   virtual void handleOOB( const gloox::JID& from, const gloox::OOB& oob ) OVERRIDE;

   void init();
   void fireError(Error code, const cpc::string& errorText);
   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, const TEvt& evt)
   {
      if (mAppHandler != reinterpret_cast<XmppPushHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, mAccount.getHandle(), evt);
         mAccount.postCallback(cb);
      }
   }

   std::string getProvider();
   bool checkConnection();
   MucRegistration* getRegistration(const std::string& roomJid);
   void getMucRegistrationErrorText(gloox::RegistrationResult regResult, cpc::string& errorText);

   void cleanup();

private:

   XmppPushHandler* mAppHandler;
   PhoneInterface* mPhone;
   XmppAccount::XmppAccountImpl& mAccount;
   XmppPushManagerInterface& mInterface;
   bool mDisconnecting;

   XmppPushSettings mSettings;
   XmppPushSettings mPendingSettings;

   gloox::Adhoc* mAdhoc;

   bool mInited;
   std::string mNotificationsComponent;
   std::string mPendingServer;
   std::string mDeviceToken;
   std::set<std::string> mNotificationsSupport;
   std::set<AdhocCommand> mCommands;
   std::set<TigaseFeature> mFeatures;
   std::string mNode;

   std::set<MucRegistration*> mMucRegistrations;
};

}
}

#endif // __CPCAPI2_XMPP_PUSH_MANAGER_IMPL_H__
