#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_STATE_IMPL_H)
#define CPCAPI2_XMPP_ROSTER_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "xmpp/XmppAccountHandler.h"
#include "XmppRosterHandlerInternal.h"
#include "XmppAccountHandlerInternal.h"
#include "xmpp/XmppRosterState.h"
#include "../phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{

namespace XmppRoster
{

class XmppRosterInterface;

class XmppRosterStateImpl : public XmppRosterStateManager,
                            public XmppRosterHandlerInternal,
                            public XmppAccount::XmppAccountHandlerInternal,
                            public PhoneModule
{

public:

   XmppRosterStateImpl(XmppRosterInterface* acctIf);
   virtual~ XmppRosterStateImpl();

   // XmppRosterStateManager
   virtual int getRosterState(CPCAPI2::XmppRoster::XmppRosterHandle roster, XmppRosterState& state) OVERRIDE;
   virtual int getRosterStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppRosterState& state) OVERRIDE;
   virtual int getAllRosterState(cpc::vector<XmppRosterState>& states) OVERRIDE;

   // Inherited via XmppRosterHandlerInternal
   virtual int onCreateRosterResult(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterCreatedResultEvent& args) OVERRIDE;
   
   // Inherited via XmppRosterHandler
   virtual int onRosterUpdate(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUpdateEvent& args) OVERRIDE;
   virtual int onSubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterSubscriptionRequestEvent& args) OVERRIDE;
   virtual int onUnsubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterUnsubscriptionRequestEvent& args) OVERRIDE;
   virtual int onRosterPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args) OVERRIDE;
   virtual int onSelfPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppRoster::XmppRosterHandle roster, const ErrorEvent& args) OVERRIDE;
   
   // Inherited via XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;
   
   // Inherited via XmppAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE;
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityFeatureEvent& args) OVERRIDE;

   // PhoneModule
   virtual void Release() OVERRIDE;

private:

   CPCAPI2::XmppAccount::XmppAccountHandle getAccountHandle(XmppRosterHandle roster);
   XmppRosterHandle getRosterHandle(CPCAPI2::XmppAccount::XmppAccountHandle account);
   int getItemCount();
   void printRosterStatus();

   XmppRosterInterface* mRosterIf;
   typedef std::map<XmppRosterHandle, std::map<cpc::string, RosterItem>> RosterStateMap;
   typedef std::map<XmppRosterHandle, CPCAPI2::XmppAccount::XmppAccountHandle> RosterAccountMap;
   RosterStateMap mStateMap;
   RosterAccountMap mAccountMap;

};

}

}

#endif // CPCAPI2_XMPP_ROSTER_STATE_IMPL_H
