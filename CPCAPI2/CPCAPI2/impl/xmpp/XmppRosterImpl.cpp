#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)
#include "XmppRosterImpl.h"
#include <cpcapi2utils.h>
#include "util/DumFpCommand.h"
#include <xmpp/XmppRosterHandler.h>
#include <xmpp/XmppRosterHandlerInternal.h>
#include "XmppAccountImpl.h"
#include "XmppVCardManagerImpl.h"
#include <rostermanager.h>

#include "CpcXep.h"
#include "CpcXepUserActivity.h"

#include <iq.h>
#include <error.h>
#include <vcardupdate.h>
#include <capabilities.h>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ROSTER

using namespace gloox;

namespace CPCAPI2
{

namespace XmppRoster
{

class CpcXepCiscoCustomStatus : public gloox::StanzaExtension
{
public:
   CpcXepCiscoCustomStatus(const gloox::Tag* tag = 0)
      : StanzaExtension(EXepCiscoCustomStatus)
   {}

   virtual ~CpcXepCiscoCustomStatus() {}

   static const std::string XMLNS_CISCO_CUSTOM_STATUS;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const
   {
      static const std::string filter =
         "/presence/x[@xmlns='" + XMLNS_CISCO_CUSTOM_STATUS + "']";

      return filter;
   }

   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const
   {
   return new CpcXepCiscoCustomStatus(*this);
   }

   virtual gloox::Tag* tag() const { return NULL; }

   virtual gloox::StanzaExtension* clone() const { return new CpcXepCiscoCustomStatus(*this); }
};

const std::string CpcXepCiscoCustomStatus::XMLNS_CISCO_CUSTOM_STATUS = "http://webex.com/connect/customstatus";

#ifdef _WIN32
std::atomic<XmppRosterHandle> XmppRosterImpl::sNextRosterHandle = 1;
#else
std::atomic<XmppRosterHandle> XmppRosterImpl::sNextRosterHandle = ATOMIC_VAR_INIT(1);
#endif

static gloox::StringList toGlooxStringList(const cpc::vector<cpc::string>& strs);

XmppRosterImpl::XmppRosterImpl(XmppAccount::XmppAccountImpl& account, XmppRosterInterface& intf)
   : mAccount(account)
   , mInterface(intf)
   , mHandle(0)
{
   mAccount.registerAccountObserver(this);
}

XmppRosterImpl::~XmppRosterImpl()
{
   cleanup();

   mAccount.unregisterAccountObserver(this);

   resip::Lock locker(mInterface.mCacheLock);

   mInterface.mCache.erase(mHandle);
}

void XmppRosterImpl::cleanup()
{
   //mHandle = 0; // bliu: TODO need to reconsider the design of handle assignment
   if (mAccount.isConnected()) mAccount.getGlooxClient()->rosterManager()->removeRosterListener();
}

void XmppRosterImpl::addSdkObserver(XmppRosterHandlerInternal* handler)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), handler) != mSdkObservers.end())
   {
      StackLog(<< "XmppRosterImpl::addSdkObserver(): " << this << " handler: " << handler << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.push_back(handler);
   StackLog(<< "XmppRosterImpl::addSdkObserver(): " << this << " handler: " << handler << " added to handler list of size: " << mSdkObservers.size());
}

void XmppRosterImpl::removeSdkObserver(XmppRosterHandlerInternal* handler)
{
   std::list<XmppRosterHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), handler);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppRosterImpl::removeSdkObserver(): XmppRosterHandlerInternal handler: " << handler << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppRosterImpl::setHandler(XmppRosterHandler* handler)
{
   StackLog(<< "XmppRosterImpl::setHandler(): " << this << " updating app handler: " << mAppHandler << " to: " << handler);
   mAppHandler = handler;
}

int XmppRosterImpl::setHandle(XmppRosterHandle h)
{
   if (mHandle != 0)
   {
      fireError("XmppRoster can't apply multiple rosters to the same xmpp account");
      return kError;
   }
   mHandle = h;
   return kSuccess;
}

void XmppRosterImpl::fireError(const cpc::string& errorText)
{
   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppRoster: " + errorText);
      return;
   }

   XmppRoster::ErrorEvent evt;
   evt.errorText = errorText;
   fireEvent(cpcFunc(XmppRosterHandler::onError), mHandle, evt);
}

void XmppRosterImpl::toResourceItem(const std::string& resName, const gloox::Resource& resItem, XmppRoster::ResourceItem& item)
{
   // TODO: support extended presence (e.g. OnThePhone)
   item.resource = resName.c_str();
   item.priority = resItem.priority();
   item.presenceType = (XmppRoster::PresenceType)resItem.presence();
   item.presenceStatusText = resItem.message().c_str();

   for (gloox::StanzaExtensionList::const_iterator it = resItem.extensions().begin(), end = resItem.extensions().end(); it != end; ++it)
   {
      if (*it == NULL) continue;

      if ((*it)->extensionType() == EXepUserActivity)
      {
         if (const CpcXepUserActivity* extUserActivity = dynamic_cast<const CpcXepUserActivity*>(*it))
         {
            item.userActivityGeneralType = static_cast<UserActivityGeneralType>(extUserActivity->generalType());
            item.userActivitySpecificType = static_cast<UserActivitySpecificType>(extUserActivity->specificType());
            item.userActivityText = extUserActivity->text().c_str();
         }
      }
      else if ((*it)->extensionType() == gloox::ExtCaps)
      {
         if (const gloox::Capabilities* cap = dynamic_cast<const gloox::Capabilities*>(*it))
         {
            if (cap->node() == "http://cisco.com/cup/caps" && item.priority == 127)
            {
               item.isCiscoRichPresence = true;

               if (item.presenceType == PresenceType_Away && item.presenceStatusText.empty()) item.presenceStatusText = "Away";
            }
         }
      }
      else if ((*it)->extensionType() == EXepCiscoCustomStatus)
      {
         item.isCiscoCustomStatus = true;
      }
   }
}

void XmppRosterImpl::toRosterItem( const gloox::RosterItem& rosItem, RosterItem& item )
{
   item.address = rosItem.jidJID().bare().c_str();
   item.displayName = rosItem.name().c_str();
   const gloox::RosterItem::ResourceMap& resources = rosItem.resources();
   gloox::RosterItem::ResourceMap::const_iterator rit = resources.begin();
   for(; rit != resources.end(); ++rit)
   {
      ResourceItem resource;
      toResourceItem(rit->first, (*rit->second), resource);
      if (resource.isCiscoCustomStatus) continue;
      item.resources.push_back(resource);
   }
   const gloox::StringList& groups = rosItem.groups();
   gloox::StringList::const_iterator git = groups.begin();
   for(; git != groups.end(); ++git)
   {
      item.groups.push_back((*git).c_str());
   }
   item.subscription = (SubscriptionState) rosItem.subscription();
}

void XmppRosterImpl::toRosterItem( const gloox::JID& jid, RosterItem& item )
{
   gloox::RosterItem* rosItem = mAccount.getGlooxClient()->rosterManager()->getRosterItem(jid);
   if (rosItem != NULL)
   {
      toRosterItem(*rosItem, item);
   }
}

void XmppRosterImpl::acceptSubscriptionRequest(const cpc::string& address)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      client->rosterManager()->ackSubscriptionRequest( jid, true );
   }
}

void XmppRosterImpl::rejectSubscriptionRequest(const cpc::string& address)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      client->rosterManager()->ackSubscriptionRequest( jid, false );
   }
}

void XmppRosterImpl::cancelAcceptedSubscription( const cpc::string& address, const cpc::string& message )
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      std::string msg = message.c_str();
      client->rosterManager()->cancel(jid, msg);
   }
}

void XmppRosterImpl::addRosterItem(
      const cpc::string& address,
      const cpc::string& displayName,
      const cpc::vector<cpc::string>& groups)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      std::string name = displayName.c_str();
      gloox::StringList glooxGroups = toGlooxStringList(groups);
      client->rosterManager()->add(jid, name, glooxGroups);
   }
}

void XmppRosterImpl::updateRosterItem(
      const cpc::string& address,
      const cpc::string& displayName,
      const cpc::vector<cpc::string>& groups)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      gloox::Roster* glooxRoster = mAccount.getGlooxClient()->rosterManager()->roster();
      gloox::Roster::iterator it = glooxRoster->find(jid);
      if (it != glooxRoster->end())
      {
         std::string name = displayName.c_str();
         gloox::StringList glooxGroups = toGlooxStringList(groups);
         gloox::RosterItem* rosterItem = it->second;
         rosterItem->setName(name);
         rosterItem->setGroups(glooxGroups);
         client->rosterManager()->synchronize();
      }
   }
}

void XmppRosterImpl::removeRosterItem(const cpc::string& address)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      client->rosterManager()->remove(jid);
   }
}

void XmppRosterImpl::subscribePresence(
      const cpc::string& address,
      const cpc::string& displayName,
      const cpc::vector<cpc::string>& groups,
      const cpc::string& message)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      std::string name = displayName.c_str();
      std::string msg = message.c_str();
      gloox::StringList glooxGroups = toGlooxStringList(groups);
      client->rosterManager()->subscribe(jid, name, glooxGroups, msg);
   }
}

void XmppRosterImpl::unsubscribePresence(const cpc::string& address)
{
   gloox::Client* client = mAccount.getGlooxClient();
   if (client != NULL)
   {
      std::string jid = address.c_str();
      client->rosterManager()->unsubscribe(jid);
   }
}

void XmppRosterImpl::fireAdded( const gloox::JID& jid )
{
   XmppRosterUpdateEvent evt;
   evt.fullUpdate = false;
   XmppRosterUpdateEvent::ChangeItemAdd change;
   toRosterItem(jid, change.item);
   evt.added.push_back(change);
   fireEvent(cpcFunc(XmppRosterHandler::onRosterUpdate), mHandle, evt);

   resip::Lock locker (mInterface.mCacheLock);
   XmppRosterInterface::CacheMap::mapped_type& items = mInterface.mCache[mHandle];
   items.insert(std::make_pair(change.item.address, change.item));
}

void XmppRosterImpl::fireUpdated( const gloox::JID& jid )
{
   XmppRosterUpdateEvent evt;
   evt.fullUpdate = false;
   XmppRosterUpdateEvent::ChangeItemUpdate change;
   toRosterItem(jid, change.item);
   evt.updated.push_back(change);
   fireEvent(cpcFunc(XmppRosterHandler::onRosterUpdate), mHandle, evt);

   resip::Lock locker (mInterface.mCacheLock);
   XmppRosterInterface::CacheMap::mapped_type& items = mInterface.mCache[mHandle];
   items[change.item.address] = change.item;
}

void XmppRosterImpl::fireRemoved( const gloox::JID& jid )
{
   XmppRosterUpdateEvent evt;
   evt.fullUpdate = false;
   XmppRosterUpdateEvent::ChangeItemRemove change;
   change.address = (jid.bare()).c_str();
   evt.removed.push_back(change);
   fireEvent(cpcFunc(XmppRosterHandler::onRosterUpdate), mHandle, evt);

   resip::Lock locker (mInterface.mCacheLock);
   XmppRosterInterface::CacheMap::mapped_type& items = mInterface.mCache[mHandle];
   items.erase(change.address);
}

void XmppRosterImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
   gloox::Client* glooxClient = account.getGlooxClient();
   glooxClient->rosterManager()->registerRosterListener(this, false);
   glooxClient->registerStanzaExtension(new CpcXepCiscoCustomStatus(NULL));
}

void XmppRosterImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
}

void XmppRosterImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppRosterImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   // notify handler roster items are stale
   // notify handler presence subscriptions are stale

   cleanup();
}

void XmppRosterImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppRosterImpl::handleItemAdded( const gloox::JID& jid )
{
   fireAdded(jid);
}

void XmppRosterImpl::handleItemSubscribed( const gloox::JID& jid )
{
   fireUpdated(jid);
}

void XmppRosterImpl::handleItemRemoved( const gloox::JID& jid )
{
   fireRemoved(jid);
}

void XmppRosterImpl::handleItemUpdated( const gloox::JID& jid )
{
   fireUpdated(jid);
}

void XmppRosterImpl::handleItemUnsubscribed( const gloox::JID& jid )
{
   fireUpdated(jid);
}

void XmppRosterImpl::handleRoster( const gloox::Roster& roster )
{
   XmppRosterInterface::CacheMap::mapped_type items;

   XmppRosterUpdateEvent evt;
   evt.fullUpdate = true;
   gloox::Roster::const_iterator it = roster.begin();
   for(; it != roster.end(); it++)
   {
      const gloox::JID& jid = it->first;
      XmppRosterUpdateEvent::ChangeItemAdd change;
      toRosterItem(jid, change.item);
      evt.added.push_back(change);

      items.insert(std::make_pair(change.item.address, change.item));
   }
   fireEvent(cpcFunc(XmppRosterHandler::onRosterUpdate), mHandle, evt);

   resip::Lock locker (mInterface.mCacheLock);
   mInterface.mCache[mHandle].swap(items);
}

void XmppRosterImpl::handleRosterPresence( const gloox::RosterItem& item, const std::string& resource, const gloox::Presence& presence )
{
   XmppRosterPresenceEvent evt;
   toRosterItem(item, evt.rosterItem);

   evt.resource = resource.c_str();
   evt.compositeCannedPresence = XmppCannedPresence(evt.rosterItem);

   DebugLog(<< "CPCAPI2 >> handleRosterPresence status:" << evt.compositeCannedPresence.status << " Note: " << evt.compositeCannedPresence.note);

   fireEvent(cpcFunc(XmppRosterHandler::onRosterPresence), mHandle, evt);

   resip::Lock locker (mInterface.mCacheLock);

   XmppRosterInterface::CacheMap::mapped_type& items = mInterface.mCache[mHandle];
   XmppRosterInterface::CacheMap::mapped_type::iterator it = items.find(evt.rosterItem.address);

   if (it == items.end()) return;

   it->second = evt.rosterItem;
}

void XmppRosterImpl::handleSelfPresence( const gloox::RosterItem& item, const std::string& resource, const gloox::Presence& presence )
{
   XmppRosterPresenceEvent evt;
   toRosterItem(item, evt.rosterItem);

   evt.resource = resource.c_str();
   evt.compositeCannedPresence = XmppCannedPresence(evt.rosterItem);

   DebugLog(<< "CPCAPI2 >> handleSelfPresence status:" << evt.compositeCannedPresence.status << " Note: " << evt.compositeCannedPresence.note);

   fireEvent(cpcFunc(XmppRosterHandler::onSelfPresence), mHandle, evt);

   // bliu: state manager is unnecessary
   //resip::Lock locker(mInterface.mCacheLock);

   //XmppRosterInterface::CacheMap::mapped_type& items = mInterface.mCache[mHandle];
   //XmppRosterInterface::CacheMap::mapped_type::iterator it = items.find(evt.rosterItem.address);

   //if (it == items.end()) return;

   //it->second = evt.rosterItem;
}

bool XmppRosterImpl::handleSubscriptionRequest( const gloox::JID& jid, const std::string& msg )
{
   XmppRosterSubscriptionRequestEvent evt;
   evt.address = jid.bare().c_str();
   evt.msg = msg.c_str();
   fireEvent(cpcFunc(XmppRosterHandler::onSubscriptionRequest), mHandle, evt);
   return false;
}

bool XmppRosterImpl::handleUnsubscriptionRequest( const gloox::JID& jid, const std::string& msg )
{
   return false;
}

void XmppRosterImpl::handleNonrosterPresence( const gloox::Presence& presence )
{
   // Ignore?
}

void XmppRosterImpl::handleRosterError( const gloox::IQ& iq )
{
   if (iq.error() != NULL)
   {
      fireError((iq.error()->text()).c_str());
   }
   else
   {
      fireError("Roster Error");
   }
}

static gloox::StringList toGlooxStringList(const cpc::vector<cpc::string>& strs)
{
   gloox::StringList result;
   cpc::vector<cpc::string>::const_iterator it = strs.begin();
   for(; it != strs.end(); it++)
   {
      result.push_back((*it).c_str());
   }
   return result;
}

} // namespace XmppRoster
} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_MODULE
