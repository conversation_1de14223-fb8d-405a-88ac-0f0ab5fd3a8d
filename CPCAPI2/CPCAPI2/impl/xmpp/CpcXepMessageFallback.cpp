//
//  CpcXepMessageFallback.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageFallback.h"

const std::string CpcXepMessageFallback::XMLNS_MESSAGE_FALLBACK = "urn:xmpp:fallback:0";

CpcXepMessageFallback::CpcXepMessageFallback(std::string isFor)
   : StanzaExtension(EXepMessageFallback)
   , mIsFor(isFor)
   , mValid(true)
{
}

CpcXepMessageFallback::CpcXepMessageFallback(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageFallback)
   , mValid(false)
{
   if (tag == NULL) return;

   if (auto fallback = tag->findTag("/message/fallback"))
   {
      mIsFor = fallback->findAttribute( "for" );
   }

   mValid = true;
}

const std::string& CpcXepMessageFallback::filterString() const
{
   static const std::string filter =
      "/message/fallback[@xmlns='" + XMLNS_MESSAGE_FALLBACK + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageFallback::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageFallback(tag);
}

gloox::Tag* CpcXepMessageFallback::tag() const
{
   gloox::Tag* t = new gloox::Tag("fallback", "xmlns", XMLNS_MESSAGE_FALLBACK);

   if (!mValid) return NULL;

   t->addAttribute("for", mIsFor);

   return t;
}

gloox::StanzaExtension* CpcXepMessageFallback::clone() const
{
   return new CpcXepMessageFallback(*this);
}
