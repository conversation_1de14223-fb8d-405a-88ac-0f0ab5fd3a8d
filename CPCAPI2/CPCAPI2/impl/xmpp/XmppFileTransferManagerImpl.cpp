// bliu: TODO
// receive accepts with a stream type which isn't listed by sender
// support of file transfer in MUC xep-0065 section 7

// bliu: note
// redundant bytesteam (in case of IBB fallback) will be terminated by dispose() instead of cancel()
// sender calls dispose() and receiver calls cancel()
// sender relies on cancel iq to terminate a file transfer item
// receiver relies on tcp shutdown to terminate a file transfer item
// all dispose/cancle related calls inside an event handler (including notifyFTRequestError() and setBytestream()) should be issued as async
// all disposeReceiver/Sender should be proceeded with setEndReason even with FileTransferItemEndReason_Unknown to change state
// SOCKS5Bytestream's m_connection is automatically instantiated from XmppConnectionTcpClient for the following scenarios:
// 1. outgoing socks5 bytestream through self hosted proxy (XmppConnectionTcpServer.getConnection())
// 2. outgoing socks5 bytestream through other hosted proxy (m_parent->connectionImpl()->newInstance())
// 3. incoming socks5 bytestream (m_parent->connectionImpl()->newInstance())
// file transfer session can be negotiated as Peer-to-Peer S5B, Peer-Server-Peer S5B, direct IBB, fallback IBB

#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)

#include <sys/types.h>
#include <sys/stat.h>

#include <string>
#include <cstdio>
#include <cstdlib>

#include "XmppFileTransferManagerImpl.h"
#include "XmppFileTransferInfo.h"
#include "XmppAccountImpl.h"
#include "XmppConnection.h"
#include "CpcXep.h"
#include "CpcXepHTTPFileUpload.h"

#include "curlpp/Easy.hpp"
#include "curlpp/Options.hpp"
#include "util/IpHelpers.h"
#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "util/ReactorHelpers.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPHelper.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

#include <rutil/DnsUtil.hxx>
#include <resip/stack/Uri.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include "cpcapi2utils.h"

#include <client_https.hpp>
#include "websocketpp/uri.hpp"

#include <error.h>
#include <disco.h>
#include <util.h>
#include <socks5bytestream.h>
#include <connectionsocks5proxy.h>

#include <boost/lexical_cast.hpp>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_FILETRANSFER
#define MY_FT_PORT 6666

#define FIRE_ERROR(handle, msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   fireError(handle, ss.str().c_str());\
}

namespace CPCAPI2
{
namespace XmppFileTransfer
{

class SIProfileFT : public gloox::SIProfileFT
{
public:
   SIProfileFT(gloox::ClientBase* parent, gloox::SIProfileFTHandler* sipfth, gloox::SIManager* manager, gloox::SOCKS5BytestreamManager* s5Manager)
      : gloox::SIProfileFT(parent, sipfth, manager, s5Manager) {}

   virtual ~SIProfileFT() {}

   virtual void handleSIRequestResult(const gloox::JID& from, const gloox::JID& to, const std::string& sid, const gloox::SIManager::SI& si) OVERRIDE
   {
      pendingOutgoings.push_back(OutgoingSIInfo(from, to, sid, static_cast<gloox::SIManager::SI*>(si.clone())));
      checkOutgoingQueue();
   }

   virtual void handleIncomingBytestreamRequest(const std::string& sid, const gloox::JID& from) OVERRIDE
   {
      pendingIncomings.push_back(IncomingSIInfo(from, sid));
      checkIncomingQueue();
   }

   void checkout(const std::string& sid)
   {
      if (activeOutgoings.erase(sid)) checkOutgoingQueue();
      if (activeIncomings.erase(sid)) checkIncomingQueue();

      // bliu: TODO remove sid from m_trackMap
   }

private:
   void checkOutgoingQueue()
   {
      while (!pendingOutgoings.empty() && activeOutgoings.size() < 2)
      {
         const OutgoingSIInfo& info = pendingOutgoings.front();
         activeOutgoings.insert(info.sid);
         gloox::SIProfileFT::handleSIRequestResult(info.from, info.to, info.sid, *info.si);
         pendingOutgoings.pop_front();
      }
   }

   void checkIncomingQueue()
   {
      while (!pendingIncomings.empty() && activeIncomings.size() < 2)
      {
         const IncomingSIInfo& info = pendingIncomings.front();
         activeIncomings.insert(info.sid);
         gloox::SIProfileFT::handleIncomingBytestreamRequest(info.sid, info.from);
         pendingIncomings.pop_front();
      }
   }

   struct OutgoingSIInfo
   {
      OutgoingSIInfo(const gloox::JID& from, const gloox::JID& to, const std::string& sid, const gloox::SIManager::SI* si)
         : from(from), to(to), sid(sid), si(si) {}

      const gloox::JID from;
      const gloox::JID to;
      const std::string sid;
      std::unique_ptr<const gloox::SIManager::SI> si;
   };

   struct IncomingSIInfo
   {
      IncomingSIInfo(const gloox::JID& from, const std::string& sid)
         : from(from), sid(sid) {}

      const gloox::JID from;
      const std::string sid;
   };

   std::set<std::string> activeOutgoings;
   std::set<std::string> activeIncomings;
   std::list<OutgoingSIInfo> pendingOutgoings;
   std::list<IncomingSIInfo> pendingIncomings;
};

class XEP0363Progress : public CurlPPProgress
{
public:
   XEP0363Progress(XmppAccount::XmppAccountImpl& account, XmppFileTransferManagerImpl& impl, XmppFileTransferHandle transfer, XmppFileTransferItemHandle item, unsigned int timeoutSeconds = 30) : CurlPPProgress(timeoutSeconds), account(account), impl(impl), transfer(transfer), item(item), progress(0) {}
   virtual ~XEP0363Progress() {}
   virtual int operator()(double dltotal, double dlnow, double ultotal, double ulnow) OVERRIDE
   {
      if (ultotal > 0)
      {
         unsigned short current = ulnow * 100 / ultotal;
         if (current != progress)
         {
            progress = current;
            account.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireProgress, impl.shared_from_this(), transfer, item, progress));
         }
      }

      return CurlPPProgress::operator()(dltotal, dlnow, ultotal, ulnow);
   }

private:
   XmppAccount::XmppAccountImpl& account;
   XmppFileTransferManagerImpl& impl;
   XmppFileTransferHandle transfer;
   XmppFileTransferItemHandle item;
   unsigned short progress;
};

#ifdef _WIN32
std::atomic<XmppFileTransferHandle> XmppFileTransferManagerImpl::sNextFileTransferHandle = 1;
std::atomic<XmppFileTransferItemHandle> XmppFileTransferManagerImpl::sNextFileTransferItemHandle = 1;
#else
std::atomic<XmppFileTransferHandle> XmppFileTransferManagerImpl::sNextFileTransferHandle = ATOMIC_VAR_INIT(1);
std::atomic<XmppFileTransferItemHandle> XmppFileTransferManagerImpl::sNextFileTransferItemHandle = ATOMIC_VAR_INIT(1);
#endif

XmppFileTransferManagerImpl::XmppFileTransferManagerImpl(XmppAccount::XmppAccountImpl& account, XmppFileTransferManagerInterface& intf)
   : gloox::SOCKS5BytestreamManager(NULL, NULL)
   , mAccount(account)
   , mInterface(intf)
   , mSIProfileFT(NULL)
   , mSOCKS5BytestreamServer(NULL)
   , mListeningPort(0)
   , mAppHandler(NULL)
   , mXEP0363MaxFileSize(0)
{
   mAccount.registerAccountObserver(this);
   mAccount.registerDiscoObserver(this);
}

XmppFileTransferManagerImpl::~XmppFileTransferManagerImpl()
{
   mFileTransferDebug.clear();
   mAccount.unregisterAccountObserver(this);
   mAccount.unregisterDiscoObserver(this);

   cleanup();

   for (XmppFileTransferInfoMap::iterator it = mFileTransferInfoMap.begin(); it != mFileTransferInfoMap.end(); ++it)
   {
      delete it->second;
   }

   for (XmppFileTransferItemInfoMap::iterator it = mFileTransferItemInfoMap.begin(); it != mFileTransferItemInfoMap.end(); ++it)
   {
      delete it->second;
   }
}

void XmppFileTransferManagerImpl::disposeReceiver(const std::string& sid)
{
   const std::string _sid = sid; // make a copy so it can be accessed after deletion if sid is from a mStreamId

   for (FileReceiverMap::iterator it = mFileReceiverMap.begin(); it != mFileReceiverMap.end(); ++it)
   {
      if (sid == it->first)
      {
         delete it->second;
         mFileReceiverMap.erase(it);
         break;
      }
   }

   if (mSIProfileFT != NULL) std::static_pointer_cast<SIProfileFT>(mSIProfileFT)->checkout(_sid);
}

void XmppFileTransferManagerImpl::disposeSender(const std::string& sid)
{
   const std::string _sid = sid; // make a copy so it can be accessed after deletion if sid is from a mStreamId

   for (FileSenderMap::iterator it = mFileSenderMap.begin(); it != mFileSenderMap.end(); ++it)
   {
      if (sid == it->first)
      {
         delete it->second;
         mFileSenderMap.erase(it);
         break;
      }
   }

   if (mSIProfileFT != NULL) std::static_pointer_cast<SIProfileFT>(mSIProfileFT)->checkout(_sid);
}

void XmppFileTransferManagerImpl::disposeXEP0363(XmppFileTransferHandle transfer, XmppFileTransferItemHandle item)
{
   DebugLog(<< "disposeXEP0363: transfer=" << transfer << " item=" << item);

   for (auto& value : mXEP0363Active)
   {
      if (value.second.first != transfer || value.second.second != item) continue;

      mXEP0363Active.erase(value.first);

      if (auto i = getFileTransferItemInfo(item))
      {
         //i->curlRequest.reset();
         if (i->XEP0363Progress != NULL)
         {
            i->XEP0363Progress->abort();
            assert(i->XEP0363Uploader.valid());
            i->XEP0363Uploader.wait();
         }
         else
         {
            InfoLog(<< "no XEP0363Progress exists transfer=" << transfer << " item=" << item);
            fireItemEnded(transfer, item, FileTransferItemEndReason_LocalCancel, FileTransferStreamType_XEP0363, "");
         }
      }
      else WarningLog(<< "disposeXEP0363: cannot find item=" << item);

      checkXEP0363Queue();

      return;
   }

   WarningLog(<< "disposeXEP0363: cannot find transfer=" << transfer << " item=" << item);
}

void XmppFileTransferManagerImpl::checkXEP0363Queue()
{
   DebugLog(<< "checkXEP0363Queue: mXEP0363Pending=" << mXEP0363Pending.size() << " mXEP0363Active=" << mXEP0363Active.size());

   while (!mXEP0363Pending.empty() && mXEP0363Active.size() < 2) // max 2 concurrent session
   {
      const auto& value = mXEP0363Pending.front();

      if (auto transfer = getFileTransferInfo(value.first))
      {
         if (auto item = getFileTransferItemInfo(value.second))
         {
            InfoLog(<< "CpcXepHTTPFileUpload: transfer=" << value.first << " item=" << value.second << " remote=" << transfer->remoteAddress.full() << " file=" << item->fileFullPath << " size=" << item->fileSizeBytes);

            gloox::IQ iq(gloox::IQ::Get, transfer->remoteAddress, m_parent->getID());
            iq.addExtension(new CpcXepHTTPFileUpload(item->localfileName.c_str(), item->fileSizeBytes));
            m_parent->send(iq);

            mXEP0363Active.insert(std::make_pair(iq.id(), value));
         }
         else WarningLog(<< "checkXEP0363Queue: cannot find item=" << value.second);
      }
      else WarningLog(<< "checkXEP0363Queue: cannot find transfer=" << value.first);

      mXEP0363Pending.pop_front();
   }
}

void XmppFileTransferManagerImpl::cleanup()
{
   for (TimerMap::iterator it = mTimers.begin(); it != mTimers.end(); ++it)
   {
      it->second->cancel();
      delete it->second;
   }

   mTimers.clear();

   // Remove operations in progress
   FileReceiverMap receivers = mFileReceiverMap;

   for (FileReceiverMap::reverse_iterator it = receivers.rbegin(); it != receivers.rend(); ++it)
   {
      it->second->cancel();
   }

   assert(mFileReceiverMap.empty());

   FileSenderMap senders = mFileSenderMap;

   for (FileSenderMap::reverse_iterator it = senders.rbegin(); it != senders.rend(); ++it)
   {
      it->second->cancel();
   }

   assert(mFileSenderMap.empty());

   if (mSOCKS5BytestreamServer)
   {
      mSOCKS5BytestreamServer->stop();
      mSOCKS5BytestreamServer.reset();
   }

   mPresenceResourceMap.clear();

   if (mSIProfileFT != NULL)
   {
      mSIProfileFT->removeSOCKS5BytestreamServer();
      mSIProfileFT.reset();
   }

   mListeningPort = 0;

#if 1 // bliu: clean up SOCKS5BytestreamManager. TODO: not inherit from SOCKS5BytestreamManager?
   gloox::util::clearMap(m_s5bMap);
   m_asyncTrackMap.clear();
   m_hosts.clear();

   if (m_parent != NULL)
   {
      m_parent->removePresenceHandler(this);

      // because gloox::SOCKS5BytestreamManager() was created with m_parent as NULL, we need to compensate here
      m_parent = NULL;
   }
#endif

   mDiscoService.setJID(gloox::EmptyString);

   mTokens.clear();

   mXEP0363Service.clear();
   mXEP0363MaxFileSize = 0;

   mXEP0363Pending.clear(); // clear pending prior to active so pending won't become active

   while (!mXEP0363Active.empty())
   {
      auto i = mXEP0363Active.begin();
      disposeXEP0363(i->second.first, i->second.second);
   }

   assert(mXEP0363Active.empty());

   //mFileTransferInfoMap // bliu: TODO move from ~XmppFileTransferManagerImpl()??
   //mFileTransferItemInfoMap // bliu: TODO move from ~XmppFileTransferManagerImpl()??
}

void XmppFileTransferManagerImpl::discoverRemoteStreamHosts()
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppFileTransferManagerImpl::discoverRemoteStreamHosts, shared_from_this()), 200);
      return;
   }

   if (!mDiscoService)
   {
      InfoLog(<< "Skip remote stream host query since proxy service is not available");
      return;
   }

   const std::string id = m_parent->getID();
   mTokens.push_back(id);

   gloox::IQ iq(gloox::IQ::Get, mDiscoService, id);
   iq.setFrom(m_parent->jid());

   std::string emptySid;
   gloox::StreamHostList emptyList;
   iq.addExtension( new gloox::SOCKS5BytestreamManager::Query(emptySid, gloox::SOCKS5BytestreamManager::S5BTCP, emptyList) );

   InfoLog(<< "Sending remote stream host query to " << mDiscoService.full());

   m_parent->send(iq);
}

void XmppFileTransferManagerImpl::discoverStreamHosts()
{
   // 1. Set up local stream hosts
   if (mListeningPort != 0 && mSOCKS5BytestreamServer != NULL)
   {
      std::list<std::pair<resip::Data, resip::Data> > ifs = resip::DnsUtil::getInterfaces();

      gloox::StringList myIpAddresses;

      for (std::list<std::pair<resip::Data, resip::Data> >::const_iterator it = ifs.begin(); it != ifs.end(); ++it)
      {
         resip::Tuple t(it->second, 0, resip::UNKNOWN_TRANSPORT);

#ifndef CPCAPI2_USE_IPV6
         if (!t.isV4()) continue;
#endif

         if (t.isLoopback()) continue;

         myIpAddresses.push_back(it->second.c_str());
      }

      for (gloox::StringList::const_iterator it = myIpAddresses.begin(); it != myIpAddresses.end(); ++it)
      {
         InfoLog(<< "Stream host (local) - jid: " << m_parent->jid().full() << ", host: " << *it << ", port: " << mListeningPort);

         addStreamHost(m_parent->jid(), *it, mListeningPort);
      }
   }

   // 2. From account settings
   for (cpc::vector<cpc::string>::const_iterator it = mAccount.getSettings().fileTransfileProxies.begin(), end = mAccount.getSettings().fileTransfileProxies.end(); it != end; ++it)
   {
      resip::Uri u(("proxy:" + *it).c_str());
      resip::ExtensionParameter jidExt ("jid");
      resip::Data jid = u.exists(jidExt) ? u.param(jidExt) : "";

      InfoLog(<< "Stream host (setting) - jid: " << jid.c_str() << ", host: " << u.host().c_str() << ", port: " << u.port());

      addStreamHost(gloox::JID(jid.c_str()), u.host().c_str(), u.port()/*, u.user().c_str(), u.password().c_str()*/);
   }

   // 3. Query remote stream host
   // called in onXmppDiscoCompleted()
}

void XmppFileTransferManagerImpl::setHandler( XmppFileTransferHandler* handler)
{
   if (mAppHandler != NULL && handler != NULL)
   {
      FIRE_ERROR(0, "XmppFileTransferManager::setHandler() has been called multiple time with the same account handle: " << mAccount.getHandle());
   }

   mAppHandler = handler;
}

void XmppFileTransferManagerImpl::onTimer(unsigned short timerId, void* appState)
{
   XmppFileTransferHandle handle = (XmppFileTransferHandle)(uintptr_t)appState;

   XmppFileTransferInfo* pInfo = getFileTransferInfo(handle);

   if (pInfo != NULL)
   {
      NewFileTransferEvent evt;
      evt.account = pInfo->accountHandle;

      for (std::list<XmppFileTransferItemHandle>::iterator it = pInfo->transferItems.begin(); it != pInfo->transferItems.end(); ++it)
      {
         if (XmppFileTransferItemInfo* info = getFileTransferItemInfo(*it))
         {
            evt.fileItems.push_back(*info);
         }
      }

      evt.remoteAddress = pInfo->remoteAddress.full().c_str();
      evt.remoteDisplayName = pInfo->remoteAddress.bare().c_str();
      evt.fileTransferState = FileTransferState_RemoteOriginated;
      evt.fileTransferType = FileTransferType_Incoming;

      fireNewTransfer(handle, evt);
   }

   TimerMap::iterator it = mTimers.find(handle);
   if (it == mTimers.end()) return;

   delete it->second;
   mTimers.erase(it);
}

void XmppFileTransferManagerImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
   // because gloox::SOCKS5BytestreamManager() was created with m_parent as NULL, we need to compensate here
   m_parent = account.getGlooxClient();

   m_parent->disco()->addFeature(gloox::XMLNS_BYTESTREAMS);
   //m_parent->disco()->addFeature(gloox::XMLNS_SI); // did in SIManager
   //m_parent->disco()->addFeature(gloox::XMLNS_SI_FT); // did in SIManager

   m_parent->registerStanzaExtension(new gloox::SOCKS5BytestreamManager::Query());
   m_parent->registerIqHandler( this, gloox::StanzaExtensionType::ExtS5BQuery );

   m_parent->registerStanzaExtension(new CpcXepHTTPFileUpload());
   m_parent->registerIqHandler(this, EXepHTTPFileUpload);
   m_parent->registerIqHandler(this, gloox::ExtError); // bliu: Openfire plugin doesn't include XEP-0363 extension for errors

   m_parent->registerPresenceHandler(this);
}

void XmppFileTransferManagerImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
   mSIProfileFT = std::static_pointer_cast<gloox::SIProfileFT>(std::make_shared<SIProfileFT>(m_parent, this, static_cast<gloox::SIManager*>(NULL), this));

   registerBytestreamHandler(mSIProfileFT.get());
   int listeningPortRange = 10;
#if defined(__linux__)
   const char* sListeningPortRange = getenv("CPCAPI2_FT_PORT_RANGE");
   if (sListeningPortRange)
   {
      std::stringstream ss;
      ss << sListeningPortRange;
      ss >> listeningPortRange;
   }
#endif

   if (mAccount.getSettings().enableLocalSocks5Proxy)
   {
      DebugLog(<< "XmppFileTransferManagerImpl::onDidConnect(): listening port: " << MY_FT_PORT << " range: " << listeningPortRange);

      for (unsigned short listeningPort = MY_FT_PORT; listeningPort < (MY_FT_PORT + listeningPortRange); listeningPort++)
      {
         mSOCKS5BytestreamServer = std::make_shared<gloox::SOCKS5BytestreamServer>(m_parent->logInstance(), listeningPort);

         XmppConnectionTcpServer* server = new XmppConnectionTcpServer(mAccount.getAccountInterface().getReactor(), mSOCKS5BytestreamServer.get(), m_parent->logInstance(), "", listeningPort);
         mSOCKS5BytestreamServer->setConnectionImpl(server);

         gloox::ConnectionError listenError = mSOCKS5BytestreamServer->listen();

         if (listenError == gloox::ConnNoError)
         {
            mListeningPort = listeningPort;
            DebugLog(<< "XmppFileTransferManagerImpl::onDidConnect(): successfully established connection on listening port: " << mListeningPort);
            break;
         }
         else
         {
            mSOCKS5BytestreamServer.reset();
         }
      }

      mSIProfileFT->registerSOCKS5BytestreamServer(mSOCKS5BytestreamServer.get());
   }

   discoverStreamHosts();
}

void XmppFileTransferManagerImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppFileTransferManagerImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppFileTransferManagerImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppFileTransferManagerImpl::onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info)
{
   if (info.hasFeature(gloox::XMLNS_BYTESTREAMS))
   {
      for (gloox::Disco::IdentityList::const_iterator it = info.identities().begin(), end = info.identities().end(); it != end; ++it)
      {
         if ((*it)->category() == "proxy" && (*it)->type() == "bytestreams")
         {
            mDiscoService = from;

            InfoLog(<< "Discovered proxy service " << mDiscoService.full());

            return;
         }
      }
   }

   if (info.hasFeature(CpcXepHTTPFileUpload::XMLNS_HTTP_FILE_UPLOAD))
   {
      for (gloox::Disco::IdentityList::const_iterator it = info.identities().begin(), end = info.identities().end(); it != end; ++it)
      {
         if ((*it)->category() == "store" && (*it)->type() == "file")
         {
            mXEP0363Service = from.bare();

            if (auto tag = std::shared_ptr<gloox::Tag>(info.tag()))
            {
               if (auto value = tag->findTag("query/x[@xmlns='jabber:x:data']/field[@var='max-file-size']/value"))
               {
                  mXEP0363MaxFileSize = boost::lexical_cast<decltype(mXEP0363MaxFileSize)>(value->cdata());
               }
            }

            InfoLog(<< "Discovered XEP-0363 HTTP File Upload service: service= " << mXEP0363Service << " max-file-size=" << mXEP0363MaxFileSize);

            return;
         }
      }
   }
}

void XmppFileTransferManagerImpl::onXmppDiscoCompleted()
{
   ServiceAvailabilityEvent evt;
   evt.XEP0363_available = !mXEP0363Service.empty();
   evt.XEP0363_service = mXEP0363Service.c_str();
   evt.XEP0363_maxFileSize = mXEP0363MaxFileSize;
   fireEvent(cpcFunc(XmppFileTransferHandler::onServiceAvailability), mAccount.getHandle(), evt);

   if (!mDiscoService)
   {
      InfoLog(<< "No proxy service has been discovered");
      return;
   }

   if (mAccount.getSettings().enableRemoteStreamHostDiscovery) discoverRemoteStreamHosts();
}

XmppFileTransferInfo *XmppFileTransferManagerImpl::getFileTransferInfo(XmppFileTransferHandle h) const
{
   XmppFileTransferInfoMap::const_iterator it = mFileTransferInfoMap.find(h);
   if (it != mFileTransferInfoMap.end())
      return it->second;

   return NULL;
}

void XmppFileTransferManagerImpl::setFileTransferInfo(XmppFileTransferHandle h, XmppFileTransferInfo * pInfo)
{
   assert(mFileTransferInfoMap[h] == NULL || mFileTransferInfoMap[h] == pInfo);
   mFileTransferInfoMap[h] = pInfo;
}

void XmppFileTransferManagerImpl::removeFileTransferInfo( XmppFileTransferHandle h )
{
   if (XmppFileTransferInfo* info = getFileTransferInfo(h))
   {
      auto copy = info->transferItems;
      for (auto item : copy)
      {
         removeFileTransferItemInfo(item);
      }

      delete info;
      mFileTransferInfoMap.erase(h);
   }
}

XmppFileTransferItemInfo* XmppFileTransferManagerImpl::getFileTransferItemInfo(XmppFileTransferItemHandle ih) const
{
   XmppFileTransferItemInfoMap::const_iterator it = mFileTransferItemInfoMap.find(ih);
   return it != mFileTransferItemInfoMap.end() ? it->second : NULL;
}

void XmppFileTransferManagerImpl::setFileTransferItemInfo(XmppFileTransferItemHandle ih, XmppFileTransferItemInfo* item)
{
   assert(mFileTransferItemInfoMap[ih] == NULL || mFileTransferItemInfoMap[ih] == item);
   mFileTransferItemInfoMap[ih] = item;
}

void XmppFileTransferManagerImpl::removeFileTransferItemInfo(XmppFileTransferItemHandle ih)
{
   if (XmppFileTransferItemInfo* info = getFileTransferItemInfo(ih))
   {
      delete info;
      mFileTransferItemInfoMap.erase(ih);
   }
}

void XmppFileTransferManagerImpl::addSdkObserver(XmppFileTransferHandler* sdkObserver)
{
   mSdkObservers.push_back(sdkObserver);
}

void XmppFileTransferManagerImpl::handleFTRequest(
   const gloox::JID& from,
   const gloox::JID& to,
   const std::string& sid,
   const std::string& name,
   long size,
   const std::string& hash,
   const std::string& date,
   const std::string& mimetype,
   const std::string& desc,
   const std::string& transferType,
   int stypes)
{
   DebugLog(<< "XMPP FT (receiver): handleFTRequest() [" << sid << ", " << name << "]");

   XmppFileTransferItemInfo* pItemInfo = new XmppFileTransferItemInfo(sNextFileTransferItemHandle++);
   pItemInfo->sids.push_back(sid);
   pItemInfo->fileSizeBytes = size;
   pItemInfo->isIncoming = true;
   pItemInfo->remotefileName = name.c_str();
   pItemInfo->localfileName  = name.c_str(); // Initial suggestion
   pItemInfo->localfilePath  = ".";          // Initial suggestion
   pItemInfo->acceptedState  = ftitem_notprocessed;
   pItemInfo->streamTypes = static_cast<FileTransferStreamType>(stypes);
   pItemInfo->transferType = !transferType.empty() ? transferType.c_str() : "";

   setFileTransferItemInfo(pItemInfo->handle, pItemInfo);

   XmppFileTransferInfo* pInfo = NULL;

   for (TimerMap::iterator it = mTimers.begin(); it != mTimers.end(); ++it)
   {
      XmppFileTransferInfo* info = getFileTransferInfo(it->first);

      if (info == NULL) continue;

      if (info->remoteAddress == from)
      {
         pInfo = info;
         break;
      }
   }

   if (pInfo == NULL)
   {
      pInfo = new XmppFileTransferInfo(sNextFileTransferHandle++, mAccount.getHandle());
      pInfo->remoteAddress = from;

      TimerMap::mapped_type timer = new resip::DeadlineTimer<resip::MultiReactor>(mAccount.getAccountInterface().getReactor());
      mTimers[pInfo->handle] = timer;
      setFileTransferInfo(pInfo->handle, pInfo);
   }

   TimerMap::mapped_type& timer = mTimers[pInfo->handle];
   timer->cancel();
   timer->expires_from_now(2000);
   timer->async_wait(this, 0, reinterpret_cast<void*>(pInfo->handle));

   pInfo->transferItems.push_back(pItemInfo->handle);

   XmppFileReceiver* fileReceive =
      new XmppFileReceiver(
         *this,
         pInfo->handle,
         pItemInfo->handle,
         sid,
         from,
         name,
         desc,
         size,
         mimetype,
         hash
      );

   mFileReceiverMap[sid] = fileReceive;
}

void XmppFileTransferManagerImpl::handleFTRequestError(const gloox::IQ& iq, const std::string& sid)
{
   ErrLog(<< "XMPP FT: handleFTRequestError(): " << std::shared_ptr<gloox::Tag>(iq.tag())->xml());

   FileReceiverMap::iterator itReceiver = mFileReceiverMap.find(sid);
   if (itReceiver != mFileReceiverMap.end()) itReceiver->second->notifyFTRequestError(iq);

   FileSenderMap::iterator itSender = mFileSenderMap.find(sid);
   if (itSender != mFileSenderMap.end()) itSender->second->notifyFTRequestError(iq);
}

void XmppFileTransferManagerImpl::handleFTBytestream(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT: handleFTBytestream()");

   if (!mAccount.isConnected()) return;

   if (bytestream == NULL) return;

   if (bytestream->initiator().bare() != m_parent->jid().bare())
   {
      FileReceiverMap::const_iterator iterFind = mFileReceiverMap.find(bytestream->sid());
      if (iterFind == mFileReceiverMap.end())
      {
         InfoLog(<< "XMPP FT (receiver): close orphan stream [" << bytestream->sid() << "]");

         // must call SIProfileFT::cancel() to send out reject message instead of SIProfileFT::dispose()
         mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mSIProfileFT, bytestream));
         mAccount.post(resip::resip_bind(&SIProfileFT::checkout, std::static_pointer_cast<SIProfileFT>(mSIProfileFT), bytestream->sid()));
         return;
      }

      XmppFileReceiver* fileReceive = iterFind->second;
      fileReceive->setBytestream(bytestream);
   }
   else
   {
      FileSenderMap::const_iterator iterFind = mFileSenderMap.find(bytestream->sid());
      if (iterFind == mFileSenderMap.end())
      {
         InfoLog(<< "XMPP FT (sender): close orphan stream [" << bytestream->sid() << "]");

         mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mSIProfileFT, bytestream));
         mAccount.post(resip::resip_bind(&SIProfileFT::checkout, std::static_pointer_cast<SIProfileFT>(mSIProfileFT), bytestream->sid()));
         return;
      }

      XmppFileSender* fileSend = iterFind->second;
      fileSend->setBytestream(bytestream);
   }
}

const std::string XmppFileTransferManagerImpl::handleOOBRequestResult(const gloox::JID& from, const gloox::JID& to, const std::string& sid)
{
   // TODO: add OOB support
   FileSenderMap::const_iterator iterFind = mFileSenderMap.find(sid);
   if (iterFind == mFileSenderMap.end())
   {
      InfoLog(<< "XMPP FT (sender): close orphan stream [" << sid << "]");
      mAccount.post(resip::resip_bind(&SIProfileFT::checkout, std::static_pointer_cast<SIProfileFT>(mSIProfileFT), sid));
      return ""; // bliu: TODO close the stream
   }

   XmppFileSender* fileSend = iterFind->second;
   return fileSend->getLocalFilePath();
}

void XmppFileTransferManagerImpl::handleStreamTypeAttempted(const gloox::JID& initiator, const std::string& sid, gloox::SIProfileFT::StreamType streamType)
{
   if (initiator.bare() != m_parent->jid().bare())
   {
      FileReceiverMap::const_iterator it = mFileReceiverMap.find(sid);
      if (it == mFileReceiverMap.end())
      {
         InfoLog(<< "XMPP FT (receiver): close orphan stream [" << sid << "]");
         mAccount.post(resip::resip_bind(&SIProfileFT::checkout, std::static_pointer_cast<SIProfileFT>(mSIProfileFT), sid));
         return; // bliu: TODO close the stream
      }

      it->second->handleStreamTypeAttempted(streamType);
   }
   else
   {
      FileSenderMap::const_iterator it = mFileSenderMap.find(sid);
      if (it == mFileSenderMap.end())
      {
         InfoLog(<< "XMPP FT (sender): close orphan stream [" << sid << "]");
         mAccount.post(resip::resip_bind(&SIProfileFT::checkout, std::static_pointer_cast<SIProfileFT>(mSIProfileFT), sid));
         return; // bliu: TODO close the stream
      }

      it->second->handleStreamTypeAttempted(streamType);
   }
}

XmppFileTransferManagerImpl::XmppFileTransferDebug::XmppFileTransferDebug(std::shared_ptr<XmppFileTransferManagerImpl> manager_, XmppAccount::XmppAccountImpl& account_, XmppFileTransferHandle transfer_, XmppFileTransferItemHandle item_) :
manager(manager_),
account(account_),
transfer(transfer_),
item(item_)
{
}

XmppFileTransferManagerImpl::XmppFileTransferDebug::~XmppFileTransferDebug()
{
}

int XmppFileTransferManagerImpl::XmppFileTransferDebug::debug(curl_infotype infoType, char* debugData, size_t dataSize)
{
   std::string data;
   data.assign(debugData, dataSize);
   account.post(resip::resip_bind(&XmppFileTransferDebug::debugImpl, this, infoType, data));
   return kSuccess;
}

int XmppFileTransferManagerImpl::XmppFileTransferDebug::debugImpl(curl_infotype infoType, const std::string& debugData)
{
   std::stringstream ss;
   switch (infoType)
   {
      case CURLINFO_TEXT:
         ss << " == Info: " << debugData; break;
      case CURLINFO_HEADER_OUT:
         ss << " => Send header: " << debugData; break;
      case CURLINFO_DATA_OUT:
         ss << " => Send data: " << debugData; break;
      case CURLINFO_SSL_DATA_OUT:
         ss << " => Send SSL data: " << debugData; break;
      case CURLINFO_HEADER_IN:
         ss << " <= Recv header: " << debugData; break;
      case CURLINFO_DATA_IN:
         ss << " <= Recv data: " << debugData; break;
      case CURLINFO_SSL_DATA_IN:
         ss << " <= Recv SSL data: " << debugData; break;
      default:
         ss << " == Data: " << debugData; break;
   }
   DebugLog(<< "XmppFileTransferDebug::debugImpl(): transfer: " << transfer << " item: " << item << ss.str());
   return kSuccess;
}

bool XmppFileTransferManagerImpl::handleIq(const gloox::IQ& iq)
{
   auto itActive = mXEP0363Active.find(iq.id());

   if (itActive != mXEP0363Active.end())
   {
      auto itemInfo = getFileTransferItemInfo(itActive->second.second);
      auto transfer = itActive->second.first;
      auto item = itActive->second.second;

      switch (iq.subtype())
      {
      case gloox::IQ::Error:
      {
         if (!std::shared_ptr<gloox::Tag>(iq.tag())->findTag("/iq/error/file-too-large[@xmlns='" + CpcXepHTTPFileUpload::XMLNS_HTTP_FILE_UPLOAD + "']")) return false;

         InfoLog(<< "XmppFileTransferManagerImpl::handleIq(): file-too-large error for upload: transfer=" << transfer << " item=" << item);
         fireItemEnded(transfer, item, FileTransferItemEndReason_FileTooLarge, FileTransferStreamType_XEP0363);
         break;
      }
      case gloox::IQ::Result:
      {
         if (auto extension = iq.findExtension<CpcXepHTTPFileUpload>(EXepHTTPFileUpload))
         {
            InfoLog(<< "Put: " << extension->put() << " Authorization: " << extension->authorization() << " Cookie: " << extension->cookie() << " Get: " << extension->get());

            std::string file = itemInfo->fileFullPath;
            uint64_t size = itemInfo->fileSizeBytes;
            std::string put = extension->put();
            std::string authorization = extension->authorization();
            std::string cookie = extension->cookie();
            std::string get = extension->get();
            itemInfo->XEP0363Progress = new XEP0363Progress(mAccount, *this, transfer, item);

            itemInfo->XEP0363Uploader = std::async(std::launch::async, [=]()
            {

#ifdef ANDROID
               AndroidFileAccess fileBuffer;
#else
               std::filebuf fileBuffer;
#endif
               fileBuffer.open(file, std::ios::in | std::ios::binary);
               std::istream ifs(&fileBuffer);
               int response(0);
               std::string responseBodyStr("");

               {
                  CurlPPHelper helper;
                  curlpp::Easy request;

                  // *** Uncomment if detailed protocol logging is required ***
                  // std::shared_ptr<XmppFileTransferDebug> transferDebug(new XmppFileTransferDebug(shared_from_this(), mAccount, transfer, item));
                  // mFileTransferDebug[iq.id()] = transferDebug;
                  // request.setOpt(new curlpp::Options::DebugFunction(std::bind(&XmppFileTransferDebug::debug, transferDebug, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3)));
                  DebugLog(<< "XmppFileTransferManagerImpl::handleIq(): libcurl version: " << curlpp::libcurlVersion());

                  helper.setDefaultOptions(request, put, "PUT", size);
                  request.setOpt(new curlpp::Options::Verbose(true));

                  std::list<std::string> headers;
                  headers.push_back("Authorization: " + authorization);
                  headers.push_back("Cookie: " + cookie);
                  request.setOpt(new curlpp::options::HttpHeader(headers));

                  request.setOpt(new curlpp::Options::ReadStream(&ifs));
                  request.setOpt(new curlpp::Options::InfileSizeLarge(size));

                  auto& settings = mAccount.getSettingsInternal();

                  if (settings.httpFileUploadCertVerification == XmppAccount::XmppAccountSettingsInternal::CertVerification_Unspecified
                     ? mAccount.getSettings().ignoreCertVerification
                     : settings.httpFileUploadCertVerification == XmppAccount::XmppAccountSettingsInternal::CertVerification_Ignore)
                  {
                     request.setOpt(new curlpp::options::SslVerifyPeer(0));
                     request.setOpt(new curlpp::options::SslVerifyHost(0));
                  }
                  else
                  {
                     CurlPPSSL cssl(SslCipherOptions(), settings.acceptableFailures, settings.certFolder, settings.useCertFolderOnly);
                     request.setOpt(new curlpp::options::SslCtxFunction(cssl));
                  }

                  // Prepare the ability to abort the request and set the timer
                  request.setOpt(new curlpp::options::ProgressFunction(static_cast<XEP0363Progress&>(*itemInfo->XEP0363Progress)));

                  request.setOpt(new curlpp::options::ConnectTimeout(30));
                  request.setOpt(new curlpp::options::LowSpeedLimit(5));
                  request.setOpt(new curlpp::options::LowSpeedTime(30));

                  // request.setOpt(new curlpp::options::MaxConnects(100));
                  // request.setOpt(new curlpp::options::FreshConnect(1));
                  // request.setOpt(new curlpp::options::ForbidReuse(1));

                  {
                     auto result = curl_easy_setopt(request.getHandle(), CURLOPT_LOCALPORTRANGE, 1000);

                     if (result != CURLE_OK)
                     {
                        WarningLog(<< "XmppFileTransferManagerImpl::handleIq(): curl_easy_setopt CURLOPT_LOCALPORTRANGE failed result=" << result);
                     }
                  }

                  auto rate = mAccount.getSettings().httpFileUploadTransferRate;
                  if (rate > 0)
                  {
                     InfoLog(<< "curl_easy_setopt CURLOPT_MAX_SEND_SPEED_LARGE rate=" << rate);
                     auto result = curl_easy_setopt(request.getHandle(), CURLOPT_MAX_SEND_SPEED_LARGE, static_cast<curl_off_t>(rate));

                     if (result != CURLE_OK)
                     {
                        WarningLog(<< "curl_easy_setopt CURLOPT_MAX_SEND_SPEED_LARGE failed result=" << result);
                     }
                  }

                  try
                  {
                     request.perform();
                     mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_Complete, FileTransferStreamType_XEP0363, get));
                  }
                  catch (curlpp::LibcurlRuntimeError& e)
                  {
                     InfoLog(<< "CurlPP runtime error during XEP-0363 upload: account: " << mAccount.getSettings().username << " handle: " << mAccount.getHandle() << " transfer=" << transfer << " item=" << item << " error code=" << e.whatCode() << " error=" << e.what());

                     if (e.whatCode() == CURLcode::CURLE_ABORTED_BY_CALLBACK)
                     {
                        mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_LocalCancel, FileTransferStreamType_XEP0363, ""));
                     }
                     else
                     {
                        mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_BadConnection, FileTransferStreamType_XEP0363, ""));
                     }
                  }
                  catch (curlpp::LogicError& e)
                  {
                     InfoLog(<< "CurlPP logic error during XEP-0363 upload: transfer=" << transfer << " item=" << item << " error=" << e.what());
                     mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_BadConnection, FileTransferStreamType_XEP0363, ""));
                  }
                  catch (std::runtime_error& e)
                  {
                     InfoLog(<< "Runtime error during XEP-0363 upload: transfer=" << transfer << " item=" << item << " error=" << e.what());
                     mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_BadConnection, FileTransferStreamType_XEP0363, ""));
                  }
                  catch (std::exception& e)
                  {
                     InfoLog(<< "Exception error during XEP-0363 upload: transfer=" << transfer << " item=" << item << " error=" << e.what());
                     mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::fireItemEnded, shared_from_this(), transfer, item, FileTransferItemEndReason_BadConnection, FileTransferStreamType_XEP0363, ""));
                  }
               }

               mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeXEP0363, shared_from_this(), transfer, item));
            });
         }

         break;
      }

      default:
         WarningLog(<< "invalid IQ type=" << iq.subtype());
         break;
      }

      return true;
   }

   gloox::StringList::iterator it = std::find(mTokens.begin(), mTokens.end(), iq.id());
   if (it == mTokens.end()) return gloox::SOCKS5BytestreamManager::handleIq(iq);

   mTokens.erase(it);

   gloox::StreamHostList hosts;

   if (iq.subtype() == gloox::IQ::Result)
   {
      if (const gloox::SOCKS5BytestreamManager::Query* s5bQuery = iq.findExtension<gloox::SOCKS5BytestreamManager::Query>( gloox::ExtS5BQuery ))
      {
         for (gloox::StreamHostList::const_iterator it = s5bQuery->hosts().begin(), end = s5bQuery->hosts().end(); it != end; it++)
         {
            InfoLog(<< "Stream host (disco) - jid: " << it->jid.full() << ", host: " << it->host << ", port: " << it->port);

            if (it->host == "127.0.0.1")
            {
               ErrLog(<< "XMPP FT: IP of " + it->jid.full() + " has been returned as 127.0.0.1. May need to set xmpp.proxy.externalip property on your XMPP server");
            }

            if (!it->jid.full().empty() && !it->host.empty() && it->port > 0) addStreamHost(it->jid, it->host, it->port);
         }
      }
   }

   // return true as handled
   return true;
}

void XmppFileTransferManagerImpl::handlePresence(const gloox::Presence& presence)
{
   if (presence.presence() != gloox::Presence::Unavailable)
   {
      mPresenceResourceMap[presence.from().bareJID()].insert(presence.from());
      return;
   }

   mPresenceResourceMap[presence.from().bareJID()].erase(presence.from());
   if (mPresenceResourceMap[presence.from().bareJID()].empty()) mPresenceResourceMap.erase(presence.from().bareJID());

   for (FileReceiverMap::iterator it = mFileReceiverMap.begin(), end = mFileReceiverMap.end(); it != end; ++it)
   {
      if (it->second->getPeerJID() != presence.from()) continue;

      it->second->setEndReason(FileTransferItemEndReason_BadConnection);
      mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, shared_from_this(), it->first));
   }

   for (FileSenderMap::iterator it = mFileSenderMap.begin(), end = mFileSenderMap.end(); it != end; ++it)
   {
      if (it->second->getPeerJID() != presence.from()) continue;

      it->second->setEndReason(FileTransferItemEndReason_BadConnection);
      mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, shared_from_this(), it->first));
   }
}

void XmppFileTransferManagerImpl::start( XmppFileTransferHandle fileTransfer )
{
   if (mSIProfileFT == NULL)
   {
      FIRE_ERROR(fileTransfer, "XmppFileTransferManager is invalid for start()");
      return;
   }

   XmppFileTransferInfoMap::iterator it = mFileTransferInfoMap.find(fileTransfer);

   if (it == mFileTransferInfoMap.end()) return;

   bool useXEP0363 = it->second->remoteAddress == mXEP0363Service;

   InfoLog(<< "use XEP-0363 for file transfer: " << fileTransfer << " " << useXEP0363);

   NewFileTransferEvent evt;
   evt.account = it->second->accountHandle;

   for (std::list<XmppFileTransferItemHandle>::iterator itItem = it->second->transferItems.begin(); itItem != it->second->transferItems.end(); ++itItem)
   {

      XmppFileTransferItemInfo* item = getFileTransferItemInfo(*itItem);
      if (item == NULL) continue;

#ifdef ANDROID
       int64_t size = -1;
       AndroidFileAccess fileBuffer;
       if (nullptr != fileBuffer.open(item->fileFullPath, std::ios::in))
       {
          size = fileBuffer.size();
          fileBuffer.close();
       }
#else
#ifdef _WIN32
      struct _stat f_stat;
      cpc::string cs = item->fileFullPath.c_str();
      std::wstring ws = cs;
      if (_wstat(ws.c_str(), &f_stat) != 0)
#else
      struct stat f_stat;
      if (stat(item->fileFullPath.c_str(), &f_stat) != 0)
#endif
      {
         FIRE_ERROR(fileTransfer, "Cannot open file " << item->fileFullPath << " to send");
         fireItemEnded(fileTransfer, item->handle, FileTransferItemEndReason_BadFile, 0);
         continue;
      }

      int size = f_stat.st_size;
#endif
      if (size <= 0)
      {
        FIRE_ERROR(fileTransfer, "Sending file " << item->fileFullPath << " with invalid size " << size);
        fireItemEnded(fileTransfer, item->handle, FileTransferItemEndReason_BadFile, 0);
        continue;
      }

      if (item->fileSizeBytes != size)
      {
         WarningLog(<< "mismatched file size provided in XmppFileTransferItemDetail: transfer=" << fileTransfer << " item=" << item->handle << " fileSizeBytes=" << item->fileSizeBytes << " actual size=" << size);

         item->fileSizeBytes = size;
      }

      if (useXEP0363)
      {
         mXEP0363Pending.push_back(std::make_pair(fileTransfer, item->handle));
         checkXEP0363Queue();
      }
      else
      {
         PresenceResourceMap::mapped_type resources;

         if (!it->second->remoteAddress.resource().empty())
         {
            resources.insert(it->second->remoteAddress);
         }
         else if (mPresenceResourceMap.find(it->second->remoteAddress.bareJID()) != mPresenceResourceMap.end())
         {
            resources = mPresenceResourceMap[it->second->remoteAddress];
         }
         else
         {
            // Bare JID and no valid resource
         }

         for (PresenceResourceMap::mapped_type::const_iterator itResource = resources.begin(), endResource = resources.end(); itResource != endResource; ++itResource)
         {
            const char* filename;
            if (0 < item->remotefileName.size())
            {
              filename = item->remotefileName.c_str();
            }
            else
            {
              filename = item->localfileName.c_str();
            }

            std::string streamId = mSIProfileFT->requestFT(
               *itResource,
               filename,
               size,
               "", // hash
               "", // description
               "", // date
               "", // mimetype
               item->transferType.c_str(),
               static_cast<gloox::SIProfileFT::StreamType>(item->streamTypes) // streamTypes
            );

            if (streamId.empty())
            {
               FIRE_ERROR(fileTransfer, "Cannot initiate SI request with file " << item->fileFullPath);
               fireItemEnded(fileTransfer, item->handle, FileTransferItemEndReason_BadFile, 0); // TODO: very large file??
               continue;
            }

            item->sids.push_back(streamId);

            XmppFileSender* fileSend =
               new XmppFileSender(
                  *this,
                  it->first,
                  item->handle,
                  streamId,
                  it->second->remoteAddress,
                  filename,
                  item->fileFullPath.c_str(),
                  "", // description
                  size,
                  "" //mimetype
               );

            mFileSenderMap[streamId] = fileSend;
         }

         if (item->sids.empty())
         {
            FIRE_ERROR(fileTransfer, "No available resource from the target JID " << it->second->remoteAddress.bare());
            fireItemEnded(fileTransfer, item->handle, FileTransferItemEndReason_BadConnection, 0);
            continue;
         }
      }

      evt.fileItems.push_back(*item);
   }

   evt.remoteAddress = it->second->remoteAddress.full().c_str();
   evt.remoteDisplayName = it->second->remoteAddress.bare().c_str();
   evt.fileTransferState = FileTransferState_LocalOriginated;
   evt.fileTransferType = FileTransferType_Outgoing;

   fireNewTransfer(it->first, evt);
}

void XmppFileTransferManagerImpl::end( XmppFileTransferHandle fileTransfer )
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if ( pInfo == NULL ) return;

   for (std::list<XmppFileTransferItemHandle>::iterator it = pInfo->transferItems.begin(); it != pInfo->transferItems.end(); ++it)
   {
      cancelItem(pInfo->handle, *it);
   }

   removeFileTransferInfo(fileTransfer);
}

void XmppFileTransferManagerImpl::accept(XmppFileTransferHandle fileTransfer)
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if ( pInfo == NULL ) return;

   for (std::list<XmppFileTransferItemHandle>::iterator it = pInfo->transferItems.begin(); it != pInfo->transferItems.end(); ++it)
   {
      XmppFileTransferItemInfo* item = getFileTransferItemInfo(*it);
      if (item == NULL) continue;

      // Don't accept file transfers which are not incoming
      if (!item->isIncoming) continue;

      if (item->sids.empty()) continue;

      FileReceiverMap::iterator itReceiver = mFileReceiverMap.find(item->sids.front());

      if (itReceiver == mFileReceiverMap.end()) continue;

      if (item->acceptedState == ftitem_accepted)
      {
         itReceiver->second->accept(item->fileFullPath.c_str(), item->streamTypes);
      }
      else
      {
         itReceiver->second->reject(gloox::EmptyString);
      }
   }
}

void XmppFileTransferManagerImpl::reject(XmppFileTransferHandle fileTransfer, const cpc::string& reason)
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if ( pInfo == NULL ) return;

   for (std::list<XmppFileTransferItemHandle>::iterator it = pInfo->transferItems.begin(); it != pInfo->transferItems.end(); ++it)
   {
      XmppFileTransferItemInfo* item = getFileTransferItemInfo(*it);
      if (item == NULL) continue;

      if (item->sids.empty()) continue;

      FileReceiverMap::iterator itReceiver = mFileReceiverMap.find(item->sids.front());

      if (itReceiver != mFileReceiverMap.end())
      {
         itReceiver->second->reject(reason.c_str());
      }
   }
}

void XmppFileTransferManagerImpl::cancelItem(XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem)
{
   XmppFileTransferInfo *pInfo = getFileTransferInfo( fileTransfer );
   if ( pInfo == NULL ) return;

   // bliu: in reversed order so no pending item gets put into active queue
   for (std::list<XmppFileTransferItemHandle>::reverse_iterator it = pInfo->transferItems.rbegin(); it != pInfo->transferItems.rend(); ++it)
   {
      XmppFileTransferItemInfo* item = getFileTransferItemInfo(*it);
      if (item == NULL) continue;

      if (item->handle != fileTransferItem) continue;

      std::list<std::string> sids = item->sids;

      for (std::list<std::string>::iterator itStreamId = sids.begin(), endStreamId = sids.end(); itStreamId != endStreamId; ++itStreamId)
      {
         FileReceiverMap::iterator itReceiver = mFileReceiverMap.find(*itStreamId);

         if (itReceiver != mFileReceiverMap.end())
         {
            itReceiver->second->cancel();
         }

         FileSenderMap::iterator itSender = mFileSenderMap.find(*itStreamId);

         if (itSender != mFileSenderMap.end())
         {
            itSender->second->cancel();
         }
      }

      disposeXEP0363(fileTransfer, fileTransferItem);

      removeFileTransferItemInfo(fileTransferItem);
   }
}

void XmppFileTransferManagerImpl::fireError( XmppFileTransferHandle h, const cpc::string& errorText )
{
   InfoLog(<< "File transfer " << h << " error: " << errorText);

   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppFileTransfer: " + errorText);
      return;
   }

   ErrorEvent evt;
   evt.errorText = errorText;
   fireEvent(cpcFunc(XmppFileTransferHandler::onError), h, evt);
}

void XmppFileTransferManagerImpl::fireProgress( XmppFileTransferHandle h, XmppFileTransferItemHandle ih, unsigned short percent )
{
   FileTransferItemProgressEvent evt;
   evt.fileTransferItem = ih;
   evt.percent = percent;
   fireEvent(cpcFunc(XmppFileTransferHandler::onFileTransferItemProgress), h, evt);
}

void XmppFileTransferManagerImpl::fireEnded( XmppFileTransferHandle h, const FileTransferEndedEvent& evt )
{
   fireEvent(cpcFunc(XmppFileTransferHandler::onFileTransferEnded), h, evt);
}

void XmppFileTransferManagerImpl::fireItemEnded( XmppFileTransferHandle h, XmppFileTransferItemHandle ih, FileTransferItemEndReason endReason, int streamTypeAttempted, const std::string& remoteFileURI )
{
   FileTransferItemEndedEvent evt;
   evt.fileTransferItem = ih;
   evt.endReason = endReason;
   evt.streamTypeAttempted = streamTypeAttempted;
   evt.remoteFileURI = remoteFileURI.c_str();

   fireEvent(cpcFunc(XmppFileTransferHandler::onFileTransferItemEnded), h, evt);

   XmppFileTransferInfo* pInfo = getFileTransferInfo(h);
   if (pInfo != NULL)
   {
      bool done = true;

      for (std::list<XmppFileTransferItemHandle>::iterator it = pInfo->transferItems.begin(); it != pInfo->transferItems.end(); ++it)
      {
         XmppFileTransferItemInfo* item = getFileTransferItemInfo(*it);
         if (item == NULL) continue;

         if (item->handle == ih) item->isDone = true;
         if (!item->isDone) done = false; // something not completed yet, don't fire the event
      }

      if (!done) return;
   }

   FileTransferEndedEvent evtAll;
   evtAll.endReason = FileTransferEndReason_Unknown;
   evtAll.fileTransferState = FileTransferState_Ended;
   fireEnded(h, evtAll);
}

void XmppFileTransferManagerImpl::fireNewTransfer( XmppFileTransferHandle h, const NewFileTransferEvent& evt )
{
   fireEvent(cpcFunc(XmppFileTransferHandler::onNewFileTransfer), h, evt);
}

XmppFileReceiver::XmppFileReceiver(
   XmppFileTransferManagerImpl& xmppFileTransferManager,
   XmppFileTransferHandle transfer,
   XmppFileTransferItemHandle handle,
   const std::string& streamId,
   const gloox::JID& sender,
   const std::string& filename,
   const std::string& description,
   uint64_t filesize,
   const std::string& mimetype,
   const std::string& hash
)
   : mXmppFileTransferManager(xmppFileTransferManager)
   , mTransfer(transfer)
   , mHandle(handle)
   , mStreamId(streamId)
   , mSender(sender)
   , mFileName(filename)
   , mDescription(description)
   , mFileSize(filesize)
   , mFileSizeProcessed(0)
   , mLastProgress(0)
   , mMimeType(mimetype)
   , mHash(hash)
   , mBytestream(NULL)
   , mTransferState(TransferState_None)
   , mStreamTypesAttempted(0)
   , mTimer(mXmppFileTransferManager.mAccount.getAccountInterface().getReactor())
   , mEndReason(FileTransferItemEndReason_Unknown)
{
}

XmppFileReceiver::~XmppFileReceiver()
{
   if (mReceivedFile.is_open())
   {
      mReceivedFile.close();
   }

   if (mBytestream != NULL)
   {
      mBytestream->removeBytestreamDataHandler();

      if (mBytestream->type() == gloox::Bytestream::S5B && static_cast<gloox::SOCKS5Bytestream*>(mBytestream)->connectionImpl() != nullptr)
      {
         static_cast<gloox::SOCKS5Bytestream*>(mBytestream)->connectionImpl()->cleanup();
      }

      if (mEndReason == FileTransferItemEndReason_Complete) mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, mBytestream));
      else mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mXmppFileTransferManager.mSIProfileFT, mBytestream));
   }

   // bliu: TODO change to switch statement after enumeration value conflict is solved
   if (mEndReason == FileTransferItemEndReason_Complete)
   {
      InfoLog(<< "XMPP FT (receiver): finished COMPLETE [" << mStreamId << ", " << mFileName << "]");

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, FileTransferItemEndReason_Complete, mStreamTypesAttempted);
   }
   else if (mEndReason == FileTransferItemEndReason_BadFile || mEndReason == FileTransferItemEndReason_BadConnection)
   {
      InfoLog(<< "XMPP FT (receiver): finished FAILED with attempted stream type=" << mStreamTypesAttempted << " reason=" << mEndReason << " [" << mStreamId << ", " << mFileName << "]");

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, mEndReason, mStreamTypesAttempted);
   }
   else
   {
      InfoLog(<< "XMPP FT (receiver): finished NOT complete reason=" << mEndReason << " [" << mStreamId << ", " << mFileName << "]");

      std::remove(mLocalFilePath.c_str());

      if (mFileSizeProcessed == 0 && mBytestream != NULL) // don't access mBytestream, it has been deleted
      {
         // DCM: something went wrong with the file transfer

         const cpc::string msg = cpc::string("XMPP (receiver): finished NOT complete ") + mFileName.c_str();
         mXmppFileTransferManager.fireError(mTransfer, msg);
      }

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, mEndReason, mStreamTypesAttempted);
   }
}

void XmppFileReceiver::setBytestream(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (receiver): setBytestream() [" << mStreamId << ", " << mFileName << "]");

   assert(bytestream != NULL);

   if (mBytestream != NULL && mBytestream != bytestream)
   {
      InfoLog(<< "XMPP FT (receiver): set multiple setBytestream() [" << mStreamId << ", " << mFileName << "]");

      mBytestream->removeBytestreamDataHandler();

      // bliu: only possible case would be fallback to IBB, so call dispose() instead of cancel() in order not to close the SI
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, mBytestream));
   }

   mBytestream = bytestream;
   assert(mBytestream != NULL);

   if (mTransferState == TransferState_Ended) return;

   mBytestream->registerBytestreamDataHandler(this);

   // direct IBB will be hurt by this timeout if the sender queues up the session
   if (mBytestream->type() == gloox::Bytestream::S5B)
   {
      mTimer.cancel(); //
      mTimer.expires_from_now(60000);
      mTimer.async_wait(this, 1, NULL);
   }

   if (mBytestream->connect()) return;

   WarningLog(<< "XMPP FT (receiver): stream failed to connect [" << mStreamId << ", " << mFileName << "]");

   // bliu: error will be handled in notifyFTRequestError(), don't change to fail state here
}

void XmppFileReceiver::accept(const std::string& localFilePath, int streamType)
{
   DebugLog(<< "XMPP FT (receiver): accept file [" << mStreamId << ", " << mFileName << "]");

   if (mXmppFileTransferManager.mSIProfileFT == NULL)
   {
      mXmppFileTransferManager.fireError(mTransfer, "XmppFileTransferManager is invalid for accept()");
      return;
   }

   if (mTransferState != TransferState_None) return;

   mTransferState = TransferState_Started;

   mLocalFilePath = localFilePath;

   if (!mXmppFileTransferManager.mSIProfileFT->acceptFT(mSender, mStreamId, static_cast<gloox::SIProfileFT::StreamType>(streamType)))
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      mXmppFileTransferManager.disposeReceiver(mStreamId);
   }
}

void XmppFileReceiver::reject(const std::string& reason)
{
   DebugLog(<< "XMPP FT (receiver): reject file [" << mStreamId << ", " << mFileName << "]");

   if (mXmppFileTransferManager.mSIProfileFT == NULL)
   {
      mXmppFileTransferManager.fireError(mTransfer, "XmppFileTransferManager is invalid for reject()");
      return;
   }

   if (mTransferState != TransferState_None) return;

   mXmppFileTransferManager.mSIProfileFT->declineFT(mSender, mStreamId, gloox::SIManager::RequestRejected, reason);

   setEndReason(FileTransferItemEndReason_LocalCancel);
   mXmppFileTransferManager.disposeReceiver(mStreamId);
}

void XmppFileReceiver::cancel()
{
   DebugLog(<< "XMPP FT (receiver): cancel file [" << mStreamId << ", " << mFileName << "]");

   setEndReason(FileTransferItemEndReason_LocalCancel);
   mXmppFileTransferManager.disposeReceiver(mStreamId);
}

void XmppFileReceiver::setEndReason(FileTransferItemEndReason reason)
{
   mTransferState = TransferState_Ended;
   if (mEndReason == FileTransferItemEndReason_Unknown) mEndReason = reason;
}

void XmppFileReceiver::notifyFTRequestError(const gloox::IQ& iq)
{
   setEndReason(FileTransferItemEndReason_BadConnection);

   if (iq.error() != NULL)
   {
      mXmppFileTransferManager.fireError(mTransfer, iq.error()->text().c_str());

      if (iq.error()->type() == gloox::StanzaErrorTypeCancel)
      {
         switch (iq.error()->error())
         {
         case gloox::StanzaErrorForbidden:
         case gloox::StanzaErrorServiceUnavailable:
            mEndReason = FileTransferItemEndReason_RemoteCancel; // force to override mEndReason
         default:
            break;
         }
      }
   }

   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileReceiver::handleBytestreamData(gloox::Bytestream* bytestream, const std::string& data)
{
   std::streamsize written = 0;
   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   if (mBytestream == NULL || !mBytestream->isOpen())
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      goto end;
   }

   if (!mReceivedFile.is_open())
   {
      setEndReason(FileTransferItemEndReason_BadFile);
      goto end;
   }

   mTransferState = TransferState_Transferring;

   written = mReceivedFile.sputn(data.c_str(), static_cast<std::streamsize>(data.size()));

   if (written != data.size())
   {
      ErrLog(<< "XMPP FT (receiver): failed to write to file [" << mStreamId << ", " << mFileName << "]");
      setEndReason(FileTransferItemEndReason_BadFile);
      goto end;
   }

   mFileSizeProcessed += static_cast<uint64_t>(data.size());

   if (mFileSize != 0)
   {
      unsigned int progress = mFileSizeProcessed * 100 / mFileSize;
      if (mLastProgress != progress)
      {
         mLastProgress = progress;
         mXmppFileTransferManager.fireProgress(mTransfer, mHandle, mLastProgress);
      }
   }

   //DebugLog(<< "XMPP FT (receiver): received size " << mFileSizeProcessed << ", file size " << mFileSize << " [" << mStreamId << ", " << mFileName << "]");

   // bliu: receiver detects a completed file transfer if the size matches
   if (mFileSizeProcessed != mFileSize) return;

   InfoLog(<< "XMPP FT (receiver): reach the expect file size [" << mStreamId << ", " << mFileName << "]");

   setEndReason(FileTransferItemEndReason_Complete);

end:
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileReceiver::handleBytestreamError(gloox::Bytestream* bytestream, const gloox::IQ& iq)
{
   ErrLog(<< "XMPP FT (receiver): handleBytestreamError(): " << std::shared_ptr<gloox::Tag>(iq.tag())->xml());

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   if (iq.error() != NULL) mXmppFileTransferManager.fireError(mTransfer, iq.error()->text().c_str());

   setEndReason(FileTransferItemEndReason_BadConnection);
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileReceiver::handleBytestreamOpen(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (receiver): handleBytestreamOpen() [" << mStreamId << ", " << mFileName << "]");

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   if (mBytestream == NULL || !mBytestream->isOpen())
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      goto end;
   }

   if (mReceivedFile.is_open())
   {
      WarningLog(<< "XMPP FT (receiver): receiving file is already opened [" << mStreamId << ", " << mFileName << "]");
      return; // bliu: handleBytestreamOpen() may be called multiple times, don't goto end
   }

#ifdef _WIN32
   {
      cpc::string cs = mLocalFilePath.c_str();
      std::wstring ws = cs;
      mReceivedFile.open(ws.c_str(), std::ios::binary | std::ios_base::out);
   }
#else
   mReceivedFile.open(mLocalFilePath.c_str(), std::ios::binary | std::ios_base::out);
#endif

   if (!mReceivedFile.is_open())
   {
      ErrLog(<< "XMPP FT (receiver): handleBytestreamOpen() " << "failed to open to write: " << mLocalFilePath << " [" << mStreamId << ", " << mFileName << "]");

      const cpc::string msg = cpc::string("failed to open to write: ") + mLocalFilePath.c_str();
      mXmppFileTransferManager.fireError(mTransfer, msg);

      setEndReason(FileTransferItemEndReason_BadFile);
      goto end;
   }

   InfoLog(<< "XMPP FT (receiver): FileTransfer open for writing " << mLocalFilePath << " [" << mStreamId << ", " << mFileName << "]");

   return;

end:
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileReceiver::handleStreamTypeAttempted(gloox::SIProfileFT::StreamType streamType)
{
   DebugLog(<< "XMPP FT (receiver): handleStreamTypeAttempted() " << streamType << " [" << mStreamId << ", " << mFileName << "]");

   mStreamTypesAttempted |= static_cast<FileTransferStreamType>(streamType);

   mTransferState = TransferState_Negotiating;

   // set a 30-second timer for IBB fallback
   if (mStreamTypesAttempted != FileTransferStreamType_InBand && streamType == gloox::SIProfileFT::FTTypeIBB)
   {
      mTimer.cancel();
      mTimer.expires_from_now(30000);
      mTimer.async_wait(this, 0, NULL);
   }
}

void XmppFileReceiver::handleBytestreamClose(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (receiver): handleBytestreamClose() [" << mStreamId << ", " << mFileName << "]");

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::cancel, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   setEndReason(FileTransferItemEndReason_RemoteCancel);
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeReceiver, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileReceiver::onTimer(unsigned short timerId, void* appState)
{
   switch (timerId)
   {
   case 0: // fallback
      if (mTransferState != TransferState_Negotiating) return;

      WarningLog(<< "XMPP FT (receiver): cancel due to fallback timeout [" << mStreamId << ", " << mFileName << "]");
      cancel();
      break;

   case 1: // connect
      if (mTransferState != TransferState_Negotiating) return;

      WarningLog(<< "XMPP FT (receiver): cancel due to connect timeout [" << mStreamId << ", " << mFileName << "]");
      cancel();
      break;

   default:
      assert(false);
   }
}

XmppFileSender::XmppFileSender(
   XmppFileTransferManagerImpl& xmppFileTransferManager,
   XmppFileTransferHandle transfer,
   XmppFileTransferItemHandle handle,
   const std::string& streamId,
   const gloox::JID& target,
   const std::string& filename,
   const std::string& localFilePath,
   const std::string& description,
   uint64_t filesize,
   const std::string& mimetype
)
   : mXmppFileTransferManager(xmppFileTransferManager)
   , mTransfer(transfer)
   , mHandle(handle)
   , mStreamId(streamId)
   , mTarget(target)
   , mFileName(filename)
   , mLocalFilePath(localFilePath)
   , mDescription(description)
   , mFileSize(filesize)
   , mFileSizeProcessed(0)
   , mLastProgress(0)
   , mMimeType(mimetype)
   , mBytestream(NULL)
   , mTransferState(TransferState_Started)
   , mStreamTypesAttempted(0)
   , mTimer(mXmppFileTransferManager.mAccount.getAccountInterface().getReactor())
   , mEndReason(FileTransferItemEndReason_Unknown)
{
}

XmppFileSender::~XmppFileSender()
{
   if (mSendFile.is_open())
   {
      mSendFile.close();
   }

   if (mBytestream != NULL)
   {
      mBytestream->removeBytestreamDataHandler();

      if (mBytestream->type() == gloox::Bytestream::S5B && static_cast<gloox::SOCKS5Bytestream*>(mBytestream)->connectionImpl() != nullptr)
      {
         static_cast<gloox::SOCKS5Bytestream*>(mBytestream)->connectionImpl()->cleanup();
      }

      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, mBytestream));
   }

   XmppFileTransferItemInfo* info = mXmppFileTransferManager.getFileTransferItemInfo(mHandle);
   info->sids.remove(mStreamId);

   if (!info->sids.empty()) return;

   // bliu: TODO change to switch statement after enumeration value conflict is solved
   if (mEndReason == FileTransferItemEndReason_Complete)
   {
      InfoLog(<< "XMPP FT (sender): finished COMPLETE [" << mStreamId << ", " << mFileName << "]");

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, FileTransferItemEndReason_Complete, mStreamTypesAttempted);
   }
   else if (mEndReason == FileTransferItemEndReason_BadFile || mEndReason == FileTransferItemEndReason_BadConnection)
   {
      InfoLog(<< "XMPP FT (sender): finished FAILED with attempted stream type=" << mStreamTypesAttempted << " reason=" << mEndReason << " [" << mStreamId << ", " << mFileName << "]");

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, mEndReason, mStreamTypesAttempted);
   }
   else
   {
      InfoLog(<< "XMPP FT (sender): finished NOT complete reason=" << mEndReason << " [" << mStreamId << ", " << mFileName << "]");

      mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, mEndReason, mStreamTypesAttempted);
   }
}

void XmppFileSender::transfer()
{
   XmppConnectionTcpClient* connection = NULL;

   // bliu: somewhere else changed the status and it's unnecessary to disposeSender() here
   if (mTransferState != TransferState_Transferring) return;

   if (mBytestream == NULL || !mBytestream->isOpen())
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      goto end;
   }

   if (!mSendFile.is_open())
   {
      setEndReason(FileTransferItemEndReason_BadFile);
      goto end;
   }

   if (!mXmppFileTransferManager.mAccount.isConnected())
   {
      setEndReason(FileTransferItemEndReason_Unknown);
      goto end;
   }

   connection = static_cast<XmppConnectionTcpClient*>(mBytestream->type() == gloox::Bytestream::S5B ? static_cast<gloox::SOCKS5Bytestream*>(mBytestream)->connectionImpl() : mXmppFileTransferManager.m_parent->connectionImpl());

   if (connection == NULL)
   {
      WarningLog(<< "XMPP FT (sender): bad connection, maybe the receiver closed the connection [" << mStreamId << ", " << mFileName << "]");

      setEndReason(FileTransferItemEndReason_BadConnection);
      goto end;
   }

   if (connection->isWritable())
   {
      std::streamsize chunkSize = mSendFile.sgetn(mReadBuffer.data(), mReadBuffer.capacity());
      assert(chunkSize <= mReadBuffer.capacity());

      mTransferState = TransferState_Transferring;

      std::string dataToSend(mReadBuffer.data(), chunkSize);

      if (!mBytestream->send(dataToSend))
      {
         WarningLog(<< "XMPP FT (sender): transfer error [" << mStreamId << ", " << mFileName << "]");

         setEndReason(FileTransferItemEndReason_BadConnection);
         goto end;
      }

      mFileSizeProcessed += chunkSize;

      if (mFileSize != 0)
      {
         unsigned int progress = (mFileSizeProcessed * 100) / mFileSize;
         if (mLastProgress != progress)
         {
            mLastProgress = progress;
            mXmppFileTransferManager.fireProgress(mTransfer, mHandle, mLastProgress);
         }
      }

      if (mFileSizeProcessed == mFileSize)
      {
         InfoLog(<< "XMPP FT (sender): reach the end of file [" << mStreamId << ", " << mFileName << "]");

         setEndReason(FileTransferItemEndReason_Complete);

#if 0
         // bliu: delay the completion at the sender end for 2 second so the receiver end can complete its transfer first
         mXmppFileTransferManager.mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId), 2000);
#else // disable automatic file transfer termination (implemented above) at the sender end which may cause incompletion at the receiver end
         mXmppFileTransferManager.fireItemEnded(mTransfer, mHandle, mEndReason, mStreamTypesAttempted);
#endif

         return;
      }
   }
   else
   {
      DebugLog(<< "XMPP FT (sender): connection is not writable [" << mStreamId << ", " << mFileName << "]");
   }

   mTimer.cancel();
   mTimer.expires_from_now(mBytestream->type() == gloox::Bytestream::IBB ? 100 : 10);
   mTimer.async_wait(this, 1, NULL);
   return;

end:
   mXmppFileTransferManager.mAccount.getAccountInterface().getReactor().post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileSender::setBytestream(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (sender): setBytestream() [" << mStreamId << ", " << mFileName << "]");

   assert(bytestream != NULL);

   if (mBytestream != NULL && mBytestream != bytestream)
   {
      InfoLog(<< "XMPP FT (sender): set multiple setBytestream() [" << mStreamId << ", " << mFileName << "]");

      mBytestream->removeBytestreamDataHandler();

      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, mBytestream));
   }

   mBytestream = bytestream;
   assert(mBytestream != NULL);

   mBytestream->registerBytestreamDataHandler(this);

   if (mBytestream->connect()) return;

   ErrLog(<< "XMPP FT (sender): stream failed to connect [" << mStreamId << ", " << mFileName << "]");

   setEndReason(FileTransferItemEndReason_BadConnection);
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileSender::cancel()
{
   DebugLog(<< "XMPP FT (sender): cancel file [" << mStreamId << ", " << mFileName << "]");

   setEndReason(FileTransferItemEndReason_LocalCancel);
   mXmppFileTransferManager.disposeSender(mStreamId);
}

void XmppFileSender::setEndReason(FileTransferItemEndReason reason)
{
   mTransferState = TransferState_Ended;
   if (mEndReason == FileTransferItemEndReason_Unknown) mEndReason = reason;
}

void XmppFileSender::notifyFTRequestError(const gloox::IQ& iq)
{
   setEndReason(FileTransferItemEndReason_BadConnection);

   if (iq.error() != NULL)
   {
      mXmppFileTransferManager.fireError(mTransfer, iq.error()->text().c_str());

      if (iq.error()->type() == gloox::StanzaErrorTypeCancel)
      {
         switch (iq.error()->error())
         {
         case gloox::StanzaErrorForbidden:
         case gloox::StanzaErrorServiceUnavailable:
            mEndReason = FileTransferItemEndReason_RemoteCancel; // force to override mEndReason
            break;

         default:
            break;
         }
      }
   }

   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

const std::string& XmppFileSender::getLocalFilePath() const
{
   return mLocalFilePath;
}

void XmppFileSender::handleStreamTypeAttempted(gloox::SIProfileFT::StreamType streamType)
{
   DebugLog(<< "XMPP FT (sender): handleStreamTypeAttempted() " << streamType << " [" << mStreamId << ", " << mFileName << "]");

   // bliu: terminate the requests to all other resource first, then terminate the request to the current resource if FTTypeNone is responded
   for (XmppFileTransferManagerImpl::FileSenderMap::const_iterator it = mXmppFileTransferManager.mFileSenderMap.begin(), end = mXmppFileTransferManager.mFileSenderMap.end(); it != end; ++it)
   {
      if (it->second->mTransfer != mTransfer) continue;
      if (it->second->mHandle != mHandle) continue;
      if (it->second->mStreamId == mStreamId) continue;

      it->second->setEndReason(FileTransferItemEndReason_Unknown);
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), it->first));
   }

   if (streamType == gloox::SIProfileFT::FTTypeNone)
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
      return;
   }

   mStreamTypesAttempted |= static_cast<FileTransferStreamType>(streamType);

   mTransferState = TransferState_Negotiating;
}

void XmppFileSender::handleBytestreamData(gloox::Bytestream* bytestream, const std::string& data)
{
   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }
}

void XmppFileSender::handleBytestreamError(gloox::Bytestream* bytestream, const gloox::IQ& iq)
{
   ErrLog(<< "XMPP FT (sender): handleBytestreamError(): " << std::shared_ptr<gloox::Tag>(iq.tag())->xml());

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   if (iq.error() != NULL) mXmppFileTransferManager.fireError(mTransfer, iq.error()->text().c_str());

   setEndReason(FileTransferItemEndReason_BadConnection);
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileSender::handleBytestreamOpen(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (sender): handleBytestreamOpen() [" << mStreamId << ", " << mFileName << "]");

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   if (mBytestream == NULL || !mBytestream->isOpen())
   {
      setEndReason(FileTransferItemEndReason_BadConnection);
      goto end;
   }

   if (mSendFile.is_open())
   {
      // this may happen if external SOCKS5 server is used (one from incoming SOCKS5 connection, the other from IQ stanza)
      WarningLog(<< "XMPP FT (sender): sending file is already opened [" << mStreamId << ", " << mFileName << "]");
      return;
   }

#ifdef _WIN32
   {
      cpc::string cs = mLocalFilePath.c_str();
      std::wstring ws = cs;
      mSendFile.open(ws.c_str(), std::ios::binary | std::ios_base::in);
   }
#else
   mSendFile.open(mLocalFilePath.c_str(), std::ios::binary | std::ios_base::in);
#endif

   if (!mSendFile.is_open())
   {
      ErrLog(<< "XMPP FT (sender): handleBytestreamOpen(): " << "failed to open to read: " << mLocalFilePath << " [" << mStreamId << ", " << mFileName << "]");

      const cpc::string msg = cpc::string("failed to open to read: ") + mLocalFilePath.c_str();
      mXmppFileTransferManager.fireError(mTransfer, msg);

      setEndReason(FileTransferItemEndReason_BadFile);
      goto end;
   }

   InfoLog(<< "XMPP FT (sender): open for reading " << mLocalFilePath << " [" << mStreamId << ", " << mFileName << "]");

   mTransferState = TransferState_Transferring;

   mReadBuffer.reserve(mBytestream->type() == gloox::Bytestream::IBB ? 4 * 1024 : 16 * 1024); // bliu: block is fixed and adjust interval for transfer rate

   transfer();

   return;

end:
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileSender::handleBytestreamClose(gloox::Bytestream* bytestream)
{
   DebugLog(<< "XMPP FT (sender): handleBytestreamClose() [" << mStreamId << ", " << mFileName << "]");

   if (bytestream != mBytestream)
   {
      mXmppFileTransferManager.mAccount.post(resip::resip_bind(&gloox::SIProfileFT::dispose, mXmppFileTransferManager.mSIProfileFT, bytestream));
      return;
   }

   setEndReason(FileTransferItemEndReason_BadConnection); // sender can detect network loss
   mXmppFileTransferManager.mAccount.post(resip::resip_bind(&XmppFileTransferManagerImpl::disposeSender, mXmppFileTransferManager.shared_from_this(), mStreamId));
}

void XmppFileSender::onTimer(unsigned short timerId, void* appState)
{
   switch (timerId)
   {
   case 0: // negotiation
#if 0 // bliu: disable negotation timer
      if (mTransferState != TransferState_Negotiating) return;

      WarningLog(<< "XMPP FT (sender): cancel due to negotiation timeout [" << mStreamId << ", " << mFileName << "]");

      cancel();
#endif
      break;

   case 1: // sending
      transfer();
      break;

   default:
     assert(false);
   }
}

} // namespace XmppFileTransfer
} // namespace CPCAPI2

#endif
