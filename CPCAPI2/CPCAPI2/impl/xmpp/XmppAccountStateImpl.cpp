#include "XmppAccountStateImpl.h"
#include "XmppAccountInterface.h"

#include "brand_branded.h"

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_STATE_MODULE == 1)

namespace CPCAPI2
{

namespace XmppAccount
{

XmppAccountStateImpl::XmppAccountStateImpl(XmppAccountInterface* acctIf)
   : mAcctIf(acctIf)
{
   // StackLog(<< "XmppAccountStateImpl::XmppAccountStateImpl(): " << this);
}

XmppAccountStateImpl::~XmppAccountStateImpl()
{
   // StackLog(<< "XmppAccountStateImpl::~XmppAccountStateImpl(): " << this);
}

void XmppAccountStateImpl::Release()
{
   StackLog(<< "XmppAccountStateImpl::Release(): " << this);
   delete this;
}

int XmppAccountStateImpl::getStateAllAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountState>& accountState)
{
   StackLog(<< "XmppAccountStateImpl::getStateAllAccounts(): " << this << " account-list: " << mStateMap.size());
   AccountStateMap::const_iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      accountState.push_back(it->second);
   }
   return 0;
}

int XmppAccountStateImpl::onAccountConfigured(XmppAccountHandle account, const XmppAccountConfiguredEvent& args)
{
   StackLog(<< "XmppAccountStateImpl::onAccountConfigured(): " << this << " account: " << account << " account-list: " << mStateMap.size());
   if (mStateMap.count(account) == 0)
   {
      mStateMap[account] = XmppAccountState();
   }
   AccountStateMap::iterator it = mStateMap.find(account);
   if (it != mStateMap.end())
   {
      it->second.account = account;
      it->second.settings = args.settings;
   }
   return 0;
}

int XmppAccountStateImpl::onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
   // StackLog(<< "XmppAccountStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " account-list: " << mStateMap.size());
   if (mStateMap.count(account) == 0)
   {
      mStateMap[account] = XmppAccountState();
   }

   AccountStateMap::iterator it = mStateMap.find(account);
   if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Destroyed)
   {
      StackLog(<< "XmppAccountStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " destroyed, account-list: " << mStateMap.size());
      mStateMap.erase(account);
   }
   else
   {
      if (it != mStateMap.end())
      {
         it->second.account = account;
         it->second.accountStatus = args.accountStatus;
      }
   }
   return 0;
}

int XmppAccountStateImpl::onError(XmppAccountHandle account, const ErrorEvent& args)
{
   StackLog(<< "XmppAccountStateImpl::onError(): " << this << " account: " << account << " account-list: " << mStateMap.size());
   return 0;
}

int XmppAccountStateImpl::onEntityTime(XmppAccountHandle account, const EntityTimeEvent& args)
{
   return kSuccess;
}

int XmppAccountStateImpl::onEntityFeature(XmppAccountHandle account, const EntityFeatureEvent& args)
{
   return kSuccess;
}

}

}

#endif // CPCAPI2_BRAND_XMPP_ACCOUNT_STATE_MODULE
