#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_HANDLER_INTERNAL_H)
#define CPCAPI2_XMPP_ROSTER_HANDLER_INTERNAL_H

#include "cpcapi2defs.h"
#include <xmpp/XmppAccount.h>
#include <xmpp/XmppRosterHandler.h>

namespace CPCAPI2
{

namespace XmppRoster
{

struct XmppRosterCreatedResultEvent
{
   CPCAPI2::XmppAccount::XmppAccountHandle account;
};

/**
 * The handler for internal events on Xmpp Roster; passed in XmppAccountInterface::setInternalHandler().
*/
class XmppRosterHandlerInternal : public XmppRosterHandler
{

public:

   /**
    * Notifies the application when the create roster request has been completed
   */
   virtual int onCreateRosterResult(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterCreatedResultEvent& args) = 0;

};

} // namespace XmppRoster

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ROSTER_HANDLER_INTERNAL_H
