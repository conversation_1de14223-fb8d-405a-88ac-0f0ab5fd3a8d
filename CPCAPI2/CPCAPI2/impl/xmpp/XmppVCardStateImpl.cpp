#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)

#include "XmppVCardStateImpl.h"
#include "XmppVCardManagerInterface.h"
#include "xmpp/XmppVCardState.h"
#include "XmppVCardStateImpl.h"
#include "phone/PhoneInterface.h"

#include <map>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD

namespace CPCAPI2
{

namespace XmppVCard
{

XmppVCardStateImpl::XmppVCardStateImpl(XmppVCardManagerInterface* intf)
   : mInterface(intf)
{
   StackLog(<< "XmppVCardStateImpl::XmppVCardStateImpl(): " << this);
   XmppAccount::XmppAccountManager* xmppAccount = XmppAccount::XmppAccountManager::getInterface(intf->phoneInterface());
   XmppAccount::XmppAccountInterface* xmppAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(xmppAccount);
   xmppAccountIf->addSdkObserver(this);
}

XmppVCardStateImpl::~XmppVCardStateImpl()
{
   mStateMap.clear();
   mAccountMap.clear();
}

void XmppVCardStateImpl::Release()
{
   delete this;
}

int XmppVCardStateImpl::getState(XmppVCardHandle handle, const cpc::string& jid, XmppVCardState& state)
{
   StackLog(<< "XmppVCardStateImpl::getState(): " << this << " vcard: " << handle << " jid: " << jid << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::getState(): " << this << " invalid vcard handle: " << handle << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
      return kError;
   }

   VCardMap::iterator itVCard = it->second.find(jid);

   if (itVCard == it->second.end())
   {
      DebugLog(<< "XmppVCardStateImpl::getState(): " << this << " invalid jid: " << jid << " for vcard handle: " << handle << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
      return kError;
   }

   state = itVCard->second;
   return kSuccess;
}

int XmppVCardStateImpl::getAllStates(XmppVCardHandle handle, cpc::vector<XmppVCardStateInfo>& states)
{
   StackLog(<< "XmppVCardStateImpl::getAllStates(): " << this << " vcard: " << handle << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   StateMap::iterator i = mStateMap.find(handle);
   if (i == mStateMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::getAllStates(): " << this << " invalid vcard handle: " << handle << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
      return kError;
   }

   XmppVCardStateInfo state;
   state.vcard = handle;
   state.account = getAccountHandle(handle);
   for (VCardMap::iterator j = i->second.begin(); j != i->second.end(); ++j)
   {
      state.jid = j->first;
      state.state = j->second;
      states.push_back(state);
   }

   return kSuccess;
}

int XmppVCardStateImpl::getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppVCardStateInfo>& states)
{
   XmppVCardHandle vcard = getVCardHandle(account);
   StackLog(<< "XmppVCardStateImpl::getAllStatesForAccount(): " << this << " account: " << account << " vcard: " << vcard << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   return (getAllStates(vcard, states));
}

int XmppVCardStateImpl::getAllStateInfo(cpc::vector<XmppVCardStateInfo>& states)
{
   StackLog(<< "XmppVCardStateImpl::getAllStateInfo(): " << this << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   XmppVCardStateInfo state;
   for (StateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      state.vcard = i->first;
      state.account = getAccountHandle(state.vcard);

      for (VCardMap::iterator j = i->second.begin(); j != i->second.end(); ++j)
      {
         state.jid = j->first;
         state.state = j->second;
         states.push_back(state);
      }
   }

   return kSuccess;
}

// XmppVCardHandlerInternal

int XmppVCardStateImpl::onCreateVCardResult(CPCAPI2::XmppVCard::XmppVCardHandle vcard, const XmppVCardCreatedResultEvent& args)
{
   StackLog(<< "XmppVCardStateImpl::onCreateVCardResult(): " << this << " vcard: " << vcard << " account: " << args.account << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   if ((mAccountMap.count(vcard) != 0) && (mAccountMap[vcard] != args.account))
   {
      DebugLog(<< "XmppVCardStateImpl::onCreateVCardResult(): " << this << " mismatch in vcard account map, vcard: " << vcard << " account: " << args.account << " mapped vcard: " << mAccountMap[vcard]);
      return kError;
   }

   mAccountMap[vcard] = args.account;

   if (mStateMap.count(vcard) != 0)
   {
      DebugLog(<< "XmppVCardStateImpl::onCreateVCardResult(): " << this << " mismatch in vcard state map, vcard: " << vcard << " already has existing states");
   }

   mStateMap.erase(vcard);
   return kSuccess;
}

int XmppVCardStateImpl::onVCardFetched(XmppVCardHandle handle, const VCardFetchedEvent& evt)
{
   StackLog(<< "XmppVCardStateImpl::onVCardFetched(): " << this << " vcard: " << handle << " account: " << evt.account << " jid: " << evt.jid << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   AccountMap::iterator i = mAccountMap.find(handle);
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::onVCardFetched(): " << this << " no account mapping found for vcard: " << handle);
      return kError;
   }

   XmppVCardState state;
   state.detail = evt.detail;
   StateMap::iterator j = mStateMap.find(handle);
   if (j == mStateMap.end())
   {
      // First vcard
      StackLog(<< "XmppVCardStateImpl::onVCardFetched(): " << this << " no vcard mapping found for handle: " << handle);
      StateMap::mapped_type states;
      states.insert(std::make_pair(evt.jid, state));
      mStateMap[handle] = states;
   }
   else
   {
      StackLog(<< "XmppVCardStateImpl::onVCardFetched(): " << this << " vcard mapping found for handle: " << handle);
      j->second.erase(evt.jid);
      StateMap::mapped_type& states = mStateMap[handle];
      states.erase(evt.jid);
      states.insert(std::make_pair(evt.jid, state));
   }

   return kSuccess;
}

int XmppVCardStateImpl::onVCardOperationResult(XmppVCardHandle handle, const VCardOperationResultEvent& evt)
{
   StackLog(<< "XmppVCardStateImpl::onVCardOperationResult(): " << this << " vcard: " << handle << " account: " << evt.account << " jid: " << evt.jid << " account map size: " << mAccountMap.size() << " vcard map size: " << mStateMap.size() << " state count: " << getStateCount());
   AccountMap::iterator i = mAccountMap.find(handle);
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::onVCardFetched(): " << this << " no account mapping found for vcard: " << handle);
      return kError;
   }

   StateMap::iterator j = mStateMap.find(handle);
   if (j == mStateMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::onVCardOperationResult(): " << this << " no vcard mapping found for handle: " << handle);
      return kError;
   }

   VCardMap::iterator k = j->second.find(evt.jid);
   if (k == j->second.end())
   {
      DebugLog(<< "XmppVCardStateImpl::onVCardOperationResult(): " << this << " no state mapping found for handle: " << handle << " jid: " << evt.jid);
      return kError;
   }

   VCardMap::mapped_type& state = j->second[evt.jid];
   evt.type == 0 ? (state.fetchResult = evt.result) : (state.storeResult = evt.result);
   return kSuccess;
}

/**
 * Progress indication (in percent) of an ongoing file transfer item
 */
int XmppVCardStateImpl::onError(XmppVCardHandle handle, const ErrorEvent& evt)
{
   WarningLog(<< "XmppVCardStateImpl::onError(): " << this << " vcard: " << handle << " vcard handle in error event: " << evt.handle << " account: " << evt.account << " error text: " << evt.errorText);
   return kSuccess;
}

int XmppVCardStateImpl::onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args)
{
   return kSuccess;
}

int XmppVCardStateImpl::onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
   // StackLog(<< "XmppVCardStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " account-list: " << mAccountMap.size());
   if (args.accountStatus == XmppAccount::XmppAccountStatusChangedEvent::Status_Destroyed)
   {
      for (AccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); ++i)
      {
         if ((i->second) == account)
         {
            DebugLog(<< "XmppVCardStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " destroyed with vcard: " << i->first << " before deletion account-list: " << mStateMap.size());
            mStateMap.erase(i->first);
            mAccountMap.erase(i);
            break;
         }
      }
   }

   return kSuccess;
}

int XmppVCardStateImpl::onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args)
{
   return kSuccess;
}

int XmppVCardStateImpl::onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args)
{
   return kSuccess;
}

int XmppVCardStateImpl::onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args)
{
   return kSuccess;
}

CPCAPI2::XmppAccount::XmppAccountHandle XmppVCardStateImpl::getAccountHandle(XmppVCardHandle vcard)
{
   AccountMap::iterator i = mAccountMap.find(vcard);
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppVCardStateImpl::getAccountHandle(): " << this << " no account mapping found for vcard: " << vcard);
      return 0;
   }

   CPCAPI2::XmppAccount::XmppAccountHandle account = i->second;
   StackLog(<< "XmppVCardStateImpl::getAccountHandle(): " << this << " vcard: " << vcard << " is mapped to account: " << account);
   return account;
}

XmppVCardHandle XmppVCardStateImpl::getVCardHandle(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   for (AccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); i++)
   {
      if (i->second == account)
      {
         StackLog(<< "XmppVCardStateImpl::getVCardHandle(): " << this << " account: " << account << " is mapped to vcard: " << i->first);
         return (i->first);
      }
   }

   DebugLog(<< "XmppVCardStateImpl::getVCardHandle(): " << this << " no vcard mapping found for account: " << account);
   return 0;
}

int XmppVCardStateImpl::getStateCount()
{
   int stateCount(0);
   for (StateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      stateCount += i->second.size();
   }
   return stateCount;
}

cpc::string get_debug_string(const XmppVCardState& state)
{
   std::stringstream ss;
   ss << "fetchResult: " << state.fetchResult << " storeResult: " << state.storeResult << " {" << get_debug_string(state.detail) << "}";
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardStateInfo& state)
{
   std::stringstream ss;
   ss << "vcard: " << state.vcard << " account: " << state.account << " jid: " << state.jid << " vcard state: " << get_debug_string(state.state) << "}";
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const XmppVCardState& state)
{
   os << CPCAPI2::XmppVCard::get_debug_string(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardStateInfo& state)
{
   os << CPCAPI2::XmppVCard::get_debug_string(state);
   return os;
}

}

}

#endif
