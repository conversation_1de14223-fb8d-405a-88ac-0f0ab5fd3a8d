#include "brand_branded.h"

#include "interface/public/xmpp/XmppRoster.h"
#include "interface/public/xmpp/XmppRosterState.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)
#include "XmppRosterInterface.h"
#include "XmppRosterStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

namespace XmppRoster
{

XmppRosterManager* XmppRosterManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppRosterInterface>(phone, "XmppRosterInterface");
#else
   return NULL;
#endif
}

XmppRosterStateManager* XmppRosterStateManager::getInterface(XmppRosterManager* cpcRosterManager)
{
#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_XMPP_ROSTER_STATE_MODULE == 1)
   XmppRosterInterface* parent = dynamic_cast<XmppRosterInterface*>(cpcRosterManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<XmppRosterStateImpl>(phone, "XmppRosterStateManager", parent);
#else
   return NULL;
#endif
}

}

}
