//
//  CpcXepMessageOrigin.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageOrigin.h"

const std::string CpcXepMessageOrigin::XMLNS_MESSAGE_ORIGIN = "urn:xmpp:sid:0";

CpcXepMessageOrigin::CpcXepMessageOrigin(std::string originID)
   : StanzaExtension(EXepMessageOrigin)
   , mOriginID(originID)
   , mValid(true)
{
}

CpcXepMessageOrigin::CpcXepMessageOrigin(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageOrigin)
   , mValid(false)
{
   if (tag == NULL) return;

   mOriginID = tag->findAttribute("id");

   if (!mOriginID.empty())
      mValid = true;
}

const std::string& CpcXepMessageOrigin::filterString() const
{
   static const std::string filter =
      "/message/origin-id[@xmlns='" + XMLNS_MESSAGE_ORIGIN + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageOrigin::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageOrigin(tag);
}

gloox::Tag* CpcXepMessageOrigin::tag() const
{
   gloox::Tag* t = new gloox::Tag("origin-id", "xmlns", XMLNS_MESSAGE_ORIGIN);

   if (!mValid) return NULL;

   t->addAttribute("id", mOriginID);

   return t;
}

gloox::StanzaExtension* CpcXepMessageOrigin::clone() const
{
   return new CpcXepMessageOrigin(*this);
}
