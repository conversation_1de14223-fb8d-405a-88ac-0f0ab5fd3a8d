#pragma once

#if !defined(CPCAPI2_XMPP_XMPP_MULTI_USER_CHAT_MANAGER_IMPL_H)
#define CPCAPI2_XMPP_XMPP_MULTI_USER_CHAT_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "XmppAccountImpl.h"
#include "XmppMultiUserChatManagerInterface.h"
#include "XmppMultiUserChatSyncHandler.h"
#include "XmppMultiUserChatHandlerInternal.h"
#include "iscomposing/IsComposingManager.h"
#include "iscomposing/IsComposingInfo.h"

#include <mucroom.h>
#include <mucroomhandler.h>
#include <mucinvitationhandler.h>
#include <bookmarkstorage.h>
#include <bookmarkhandler.h>

#include <atomic>

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

class XmppMultiUserChatManagerImpl
   : public XmppAccount::XmppAccountObserver
   , public XmppAccount::XmppDiscoObserver
   , public IsComposing::Is<PERSON>omposing<PERSON>anager
   , public gloox::M<PERSON><PERSON><PERSON>Hand<PERSON>
   , public gloox::MUCRoomConfigHandler
   , public gloox::MUCInvitationHandler
   , public gloox::DiscoHandler
   , public gloox::BookmarkHandler
   , public std::enable_shared_from_this<XmppMultiUserChatManagerImpl>
{

public:

   XmppMultiUserChatManagerImpl(Phone* phone, XmppAccount::XmppAccountImpl& account, XmppMultiUserChatManagerInterface& intf);
   virtual ~XmppMultiUserChatManagerImpl();

   // Invoked by the interface
   void addSdkObserver(XmppMultiUserChatHandlerInternal* sdkObserver);
   void removeSdkObserver(XmppMultiUserChatHandlerInternal* sdkObserver);
   XmppMultiUserChatInfo* getMultiUserChatInfo(XmppMultiUserChatHandle handle) const;

   void fireError(XmppMultiUserChatHandle handle, const cpc::string& errorText);

   void setHandler(XmppMultiUserChatHandler* handler);
   void setHandler(XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler);
   void getRoomList();
   void setRoomBookmarks(const cpc::vector<RoomBookmark>& bookmarks);
   void getRoomBookmarks();
   void create(XmppMultiUserChatHandle handle, const cpc::string& room);
   void DEPRECATE_create(XmppMultiUserChatHandle handle, bool instantRoom);
   void destroyRoom(XmppMultiUserChatInfo* info, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password);
   void getRoomInfo(XmppMultiUserChatInfo* info);
   void accept(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void decline(XmppMultiUserChatInfo* info, const cpc::string& reason);
   void join(XmppMultiUserChatInfo* info, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void DEPRECATE_join(XmppMultiUserChatInfo* info, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void leave(XmppMultiUserChatInfo* info, const cpc::string& reason);
   void sendMessage(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& plain, const cpc::string& html);
   void sendReaction(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   void sendMessageRetraction(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& target);
   void setIsComposingMessage(XmppMultiUserChatInfo* info, int refreshInterval, int idleInterval);
   void publishPresence(XmppMultiUserChatInfo* info, XmppRoster::PresenceType presence, const cpc::string& note);
   void changeNickname(XmppMultiUserChatInfo* info, const cpc::string& nickname);
   void changeSubject(XmppMultiUserChatInfo* info, const cpc::string& subject);
   void invite(XmppMultiUserChatInfo* info, const cpc::string& jid, const cpc::string& reason);
   void kick(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& reason);
   void ban(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& reason);
   void changeAffiliation(XmppMultiUserChatInfo* info, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeJidAffiliation(XmppMultiUserChatInfo* info, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeRole(XmppMultiUserChatInfo* info, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason);
   void requestConfigurations(XmppMultiUserChatInfo* info);
   void setConfigurations(XmppMultiUserChatInfo* info, const XmppAccount::XmppDataForm& dataform);
   void setConfigurations(XmppMultiUserChatInfo* info, const XmppMultiUserChatConfigurations& configurations);
   void requestList(XmppMultiUserChatInfo *info, XmppMultiUserChatListType type);
   void setList(XmppMultiUserChatInfo* info, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items);
   void sendIMCommand(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, int type, const cpc::string& payload);
   bool deliveryReceiptsEnabled() { return mDeliveryReceiptsEnabled; }
   bool readReceiptsEnabled() { return mReadReceiptsEnabled; }
   void notifyMessageRead(XmppMultiUserChatInfo* info, const cpc::string& messageId);
   void replaceMessage(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html);

private:
   friend class XmppMultiUserChatInfo;
   // should only be called by XmppMultiUserChatInfo
   void onChatState(IsComposing::IsComposingInfo* info, const gloox::JID& from, gloox::ChatStateType state);

private:

   // IsComposingManager
   virtual void sendIsComposingMessageNotification(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive) OVERRIDE;
   virtual void onIsComposingMessage(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive) OVERRIDE;

   // XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onStreamManagementAck(XmppAccount::XmppAccountImpl& account) OVERRIDE;

   // XmppDiscoObserver
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) OVERRIDE;
   virtual void onXmppDiscoCompleted() OVERRIDE;

   // gloox::MUCRoomHandler
   virtual void handleMUCParticipantPresence(gloox::MUCRoom* room, const gloox::MUCRoomParticipant& participant, const gloox::Presence& presenceStanza) OVERRIDE;
   virtual void handleMUCMessage(gloox::MUCRoom* room, const gloox::Message& msg, bool priv) OVERRIDE;
   virtual bool handleMUCRoomCreation(gloox::MUCRoom* room) OVERRIDE;
   virtual void handleMUCSubject(gloox::MUCRoom* room, const std::string& nick, const std::string& subject) OVERRIDE;
   virtual void handleMUCInviteDecline(gloox::MUCRoom* room, const gloox::JID& invitee, const std::string& reason) OVERRIDE;
   virtual void handleMUCError(gloox::MUCRoom* room, const gloox::Error* error) OVERRIDE;
   virtual void handleMUCInfo(gloox::MUCRoom* room, int features, const std::string& name, const gloox::DataForm* form) OVERRIDE;
   virtual void handleMUCInfoError(gloox::MUCRoom* room, const gloox::Error* error) OVERRIDE;
   virtual void handleMUCItems(gloox::MUCRoom* room, const gloox::Disco::ItemList& items) OVERRIDE;
   virtual void handleMUCItemsError(gloox::MUCRoom* room, const gloox::Error* error) OVERRIDE;

   // gloox::MUCRoomConfigHandler
   virtual void handleMUCConfigList(gloox::MUCRoom* room, const gloox::MUCListItemList& items, gloox::MUCOperation operation) OVERRIDE;
   virtual void handleMUCConfigForm(gloox::MUCRoom* room, const gloox::DataForm& form) OVERRIDE;
   virtual void handleMUCConfigResult(gloox::MUCRoom* room, bool success, gloox::MUCOperation operation) OVERRIDE;
   virtual void handleMUCRequest(gloox::MUCRoom* room, const gloox::DataForm& form) OVERRIDE;

   // gloox::MUCInvitationHandler
   virtual void handleMUCInvitation(const gloox::JID& room, const gloox::JID& from, const std::string& reason, const gloox::Message& msg, const std::string& password, bool cont, const std::string& thread) OVERRIDE;

   // gloox::DiscoHandler
   virtual void handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context) OVERRIDE;
   virtual void handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context) OVERRIDE;
   virtual void handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context) OVERRIDE;
   virtual bool handleDiscoSet(const gloox::IQ& iq) OVERRIDE;

   // gloox::BookmarkHandler
   virtual void handleBookmarks(const gloox::BookmarkList& bList, const gloox::ConferenceList& cList) OVERRIDE;

private:

   void cleanup();
   // use underscore to disable overload which may cause issue in resip::bind
   void join_(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void DEPRECATE_join_(XmppMultiUserChatInfo* info, const gloox::JID& nick, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void processAddHistory(XmppMultiUserChatInfo* info);
   bool validateChatStatus(XmppMultiUserChatInfo* info);
   void transitionToActiveState(XmppMultiUserChatInfo* info);
   void startInactiveTimer(XmppMultiUserChatInfo* info);
   void updateParticipantState(XmppMultiUserChatInfo* info, const gloox::MUCRoomParticipant& glooxParticipant, const gloox::Presence& presenceStanza, ParticipantState& state);
   void updateRoomState(XmppMultiUserChatInfo* info, int flags, const std::string& room, const gloox::DataForm* form);
   void checkForRoomReady(XmppMultiUserChatInfo* info);
   void notifyMessageDelivered(XmppMultiUserChatInfo* info, const std::string& threadId, const std::string& messageId);
   void getMUCSettings(const XmppAccount::XmppAccountSettings& settings);
   bool isMyStanza(const gloox::JID& from, gloox::MUCRoom* room);

   XmppMultiUserChatInfo* getMultiUserChatInfoForGlooxRoom(gloox::MUCRoom* room) const;

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, XmppMultiUserChatHandle handle, const TEvt& evt)
   {
      for (std::list<XmppMultiUserChatHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, handle, evt);
         if (dynamic_cast<XmppMultiUserChatSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }

      if (mAppHandler != reinterpret_cast<XmppMultiUserChatHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, handle, evt);
         mAccount.postCallback(cb);
      }
   }

   // specialization for XmppMultiUserChatHandlerInternal which cannot be handled by mAppHandler
   void fireEvent(const char* funcName, int (XmppMultiUserChatHandlerInternal::*func)(XmppMultiUserChatHandle, const XmppMultiUserChatCreatedResultEvent&), XmppMultiUserChatCreatedResultEvent& args)
   {
      for (std::list<XmppMultiUserChatHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, args.muc, args);
         if (dynamic_cast<XmppMultiUserChatSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            mAccount.postCallback(cb);
         }
      }
   }

public:

   static std::atomic<XmppMultiUserChatHandle> sNextXmppMultiUserChatHandle;
   static std::atomic<XmppMultiUserChatMessageHandle> sNextXmppMultiUserChatMessageHandle;

private:

   XmppAccount::XmppAccountImpl& mAccount;
   XmppMultiUserChatManagerInterface& mInterface;

   typedef std::map<XmppMultiUserChatHandle, XmppMultiUserChatInfo*> InfoMap;
   InfoMap mInfoMap;

   XmppMultiUserChatHandler* mAppHandler;
   std::list<XmppMultiUserChatHandlerInternal*> mSdkObservers;
   XmppIMCommand::XmppMultiUserChatIMCommandHandler* mIMCommandHandler;

   gloox::JID mDiscoService;

   // use message ID as the key
   typedef std::map<std::string, std::pair<XmppMultiUserChatHandle, SendMessageSuccessEvent> > MessageAckMap;
   MessageAckMap mMessageAckMap;

   // bliu: this class eliminates multiple inheritence in XmppMultiUserChatManagerImpl
   struct InactiveTimerHandler : public resip::DeadlineTimerHandler
   {
      InactiveTimerHandler(XmppMultiUserChatManagerImpl& impl) : mImpl(impl) {}

      // resip::DeadlineTimerHandler
      virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

      XmppMultiUserChatManagerImpl& mImpl;
   };

   friend struct InactiveTimerHandler;

   InactiveTimerHandler mInactiveTimerHandler;

   gloox::BookmarkStorage* mBookmarkStorage;

   bool mDeliveryReceiptsEnabled;
   bool mReadReceiptsEnabled;
};

}

}

#endif // CPCAPI2_XMPP_XMPP_MULTI_USER_CHAT_MANAGER_IMPL_H
