#pragma once

#ifndef __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_IMPL_H__
#define __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_IMPL_H__

#include <atomic>
#include <list>
#include <fstream>

#ifdef ANDROID
#include "AndroidFileAccess.h"
#endif

#include "cpcapi2defs.h"
#include "curlpp/Easy.hpp"
#include "curlpp/Options.hpp"

#include "XmppAccountImpl.h"
#include "XmppFileTransferManagerInterface.h"
#include "XmppFileTransferInfo.h"

#include <bytestreamdatahandler.h>
#include <socks5bytestreamserver.h>
#include <siprofileft.h>
#include <siprofilefthandler.h>
#include <presencehandler.h>

#include <rutil/DeadlineTimer.hxx>

namespace CPCAPI2
{
namespace XmppFileTransfer
{

class XmppFileReceiver;
class XmppFileSender;

enum TransferState
{
   TransferState_None,
   TransferState_Started,
   TransferState_Negotiating,
   TransferState_Transferring,
   TransferState_Ended
};

class XmppFileTransferManagerImpl
   : public XmppAccount::XmppAccountObserver
   , public XmppAccount::XmppDiscoObserver
   , public gloox::SOCKS5BytestreamManager
   , public gloox::SIProfileFTHandler
   , public gloox::PresenceHandler
   , public resip::DeadlineTimerHandler
   , public std::enable_shared_from_this<XmppFileTransferManagerImpl>
{
public:
   XmppFileTransferManagerImpl(XmppAccount::XmppAccountImpl& account, XmppFileTransferManagerInterface& intf);
   virtual ~XmppFileTransferManagerImpl();

   void setHandler( XmppFileTransferHandler* handler );

   void start(XmppFileTransferHandle fileTransfer);
   void end(XmppFileTransferHandle fileTransfer);
   void accept(XmppFileTransferHandle fileTransfer);
   void reject(XmppFileTransferHandle fileTransfer, const cpc::string& reason);
   void cancelItem(XmppFileTransferHandle fileTransfer, XmppFileTransferItemHandle fileTransferItem);

   XmppFileTransferInfo *getFileTransferInfo( XmppFileTransferHandle h ) const;
   void setFileTransferInfo( XmppFileTransferHandle h, XmppFileTransferInfo * pInfo );
   void removeFileTransferInfo( XmppFileTransferHandle h );

   XmppFileTransferItemInfo* getFileTransferItemInfo( XmppFileTransferItemHandle ih ) const;
   void setFileTransferItemInfo( XmppFileTransferItemHandle ih, XmppFileTransferItemInfo* pItem );
   void removeFileTransferItemInfo(XmppFileTransferItemHandle ih);

   void fireError( XmppFileTransferHandle h, const cpc::string& errorText );
   void fireProgress( XmppFileTransferHandle h, XmppFileTransferItemHandle ih, unsigned short percent );
   void fireEnded( XmppFileTransferHandle h, const FileTransferEndedEvent& evt );
   void fireItemEnded( XmppFileTransferHandle h, XmppFileTransferItemHandle ih, FileTransferItemEndReason endReason, int streamTypeAttempted, const std::string& remoteFileURI = "" );
   void fireNewTransfer( XmppFileTransferHandle h, const NewFileTransferEvent& evt );

   void addSdkObserver(XmppFileTransferHandler* sdkObserver);

public:
   static std::atomic<XmppFileTransfer::XmppFileTransferHandle> sNextFileTransferHandle;
   static std::atomic<XmppFileTransfer::XmppFileTransferItemHandle> sNextFileTransferItemHandle;

private: // resip::DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:
   // XmppAccount::XmppAccountObserver
   virtual void onWillConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidConnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onWillDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDidDisconnect(XmppAccount::XmppAccountImpl& account) OVERRIDE;
   virtual void onDestroy(XmppAccount::XmppAccountImpl& account) OVERRIDE;

   // XmppDiscoObserver
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) OVERRIDE;
   virtual void onXmppDiscoCompleted() OVERRIDE;

   // gloox::SIProfileFTHandler
   virtual void handleFTRequest(const gloox::JID& from, const gloox::JID& to, const std::string& sid, const std::string& name, long size, const std::string& hash, const std::string& date, const std::string& mimetype, const std::string& desc, const std::string& transferType, int stypes) OVERRIDE;
   virtual void handleFTRequestError(const gloox::IQ& iq, const std::string& sid) OVERRIDE;
   virtual void handleFTBytestream(gloox::Bytestream* bytestream) OVERRIDE;
   virtual const std::string handleOOBRequestResult(const gloox::JID& from, const gloox::JID& to, const std::string& sid) OVERRIDE;
   virtual void handleStreamTypeAttempted(const gloox::JID& initiator, const std::string& sid, gloox::SIProfileFT::StreamType streamType) OVERRIDE;

   // gloox:IqHandler inherited from gloox::SOCKS5BytestreamManager
   virtual bool handleIq(const gloox::IQ& iq) OVERRIDE;

   // gloox::PresenceHandler
   virtual void handlePresence(const gloox::Presence& presence) OVERRIDE;

   void discoverRemoteStreamHosts();
   void discoverStreamHosts();
   void cleanup();

   void disposeReceiver(const std::string& sid);
   void disposeSender(const std::string& sid);
   void disposeXEP0363(XmppFileTransferHandle transfer, XmppFileTransferItemHandle item);

   void checkXEP0363Queue();

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, XmppFileTransferHandle handle, const TEvt& evt)
   {
      for (size_t i = 0; i < mSdkObservers.size(); ++i)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, mSdkObservers[i], handle, evt);
         mAccount.postCallback(cb);
      }

      if (mAppHandler != reinterpret_cast<XmppFileTransferHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, mAppHandler, handle, evt);
         mAccount.postCallback(cb);
      }
   }

private:

   class XmppFileTransferDebug // : public std::enable_shared_from_this<XmppFileTransferDebug>
   {
   public:
      XmppFileTransferDebug(std::shared_ptr<XmppFileTransferManagerImpl> manager_, XmppAccount::XmppAccountImpl& account_, XmppFileTransferHandle transfer_, XmppFileTransferItemHandle item_);
      virtual~ XmppFileTransferDebug();
      int debug(curl_infotype infoType, char* debugData, size_t dataSize);

      XmppFileTransferHandle transfer;
      XmppFileTransferItemHandle item;
      XmppAccount::XmppAccountImpl& account;
      std::shared_ptr<XmppFileTransferManagerImpl> manager;
   private:
      XmppFileTransferDebug();
      int debugImpl(curl_infotype infoType, const std::string& debugData);
   };

   XmppAccount::XmppAccountImpl& mAccount;
   XmppFileTransferManagerInterface& mInterface;

   XmppFileTransferHandler* mAppHandler;
   std::vector<XmppFileTransferHandler*> mSdkObservers;

   XmppFileTransferInfoMap mFileTransferInfoMap;
   XmppFileTransferItemInfoMap mFileTransferItemInfoMap;

   gloox::StringList mTokens;

   std::shared_ptr<gloox::SIProfileFT> mSIProfileFT;
   std::shared_ptr<gloox::SOCKS5BytestreamServer> mSOCKS5BytestreamServer;
   unsigned short mListeningPort;

   typedef std::map<std::string, XmppFileReceiver*> FileReceiverMap;
   typedef std::map<std::string, XmppFileSender*> FileSenderMap;

   FileReceiverMap mFileReceiverMap;
   FileSenderMap mFileSenderMap;

   friend class XmppFileReceiver;
   friend class XmppFileSender;

   typedef std::map<XmppFileTransferHandle, resip::DeadlineTimer<resip::MultiReactor>*> TimerMap;
   TimerMap mTimers;

   gloox::JID mDiscoService;

   typedef std::map<gloox::JID, std::set<gloox::JID> > PresenceResourceMap;
   PresenceResourceMap mPresenceResourceMap;

   std::string mXEP0363Service;
   uint64_t mXEP0363MaxFileSize;
   std::map<std::string, std::pair<XmppFileTransferHandle, XmppFileTransferItemHandle> > mXEP0363Active;
   std::list<std::pair<XmppFileTransferHandle, XmppFileTransferItemHandle> > mXEP0363Pending;
   std::map<std::string, std::shared_ptr<XmppFileTransferDebug>> mFileTransferDebug;
};

class XmppFileReceiver
   : public gloox::BytestreamDataHandler
   , public resip::DeadlineTimerHandler
{
public:
   XmppFileReceiver(
      XmppFileTransferManagerImpl& xmppFileTransferManager,
      XmppFileTransferHandle transfer,
      XmppFileTransferItemHandle handle,
      const std::string& streamId,
      const gloox::JID& sender,
      const std::string& filename,
      const std::string& description,
      uint64_t filesize,
      const std::string& mimetype,
      const std::string& hash
   );

   ~XmppFileReceiver();

   void setBytestream(gloox::Bytestream* bytestream);

   void accept(const std::string& localFilePath, int streamType);
   void reject(const std::string& reason);
   void cancel();

   void notifyFTRequestError(const gloox::IQ& iq);

   void handleStreamTypeAttempted(gloox::SIProfileFT::StreamType streamType);

   void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   const gloox::JID& getPeerJID() const { return mSender; }

   void setEndReason(FileTransferItemEndReason reason);

private:
   // gloox::BytestreamDataHandler
   virtual void handleBytestreamData(gloox::Bytestream* bytestream, const std::string& data) OVERRIDE;
   virtual void handleBytestreamError(gloox::Bytestream* bytestream, const gloox::IQ& iq) OVERRIDE;
   virtual void handleBytestreamOpen(gloox::Bytestream* bytestream) OVERRIDE;
   virtual void handleBytestreamClose(gloox::Bytestream* bytestream) OVERRIDE;

private:
   XmppFileTransferManagerImpl& mXmppFileTransferManager;
   const XmppFileTransferHandle mTransfer;
   const XmppFileTransferItemHandle mHandle;
   const std::string mStreamId;
   const gloox::JID mSender;
   const std::string mFileName;
   const std::string mDescription;
   std::string mLocalFilePath;
   const uint64_t mFileSize;
   uint64_t mFileSizeProcessed;
   unsigned int mLastProgress;
   const std::string mMimeType;
   const std::string mHash;

#ifdef ANDROID
   AndroidFileAccess mReceivedFile;
#else
  std::filebuf mReceivedFile;
#endif
   gloox::Bytestream* mBytestream;

   TransferState mTransferState;

   int mStreamTypesAttempted;

   resip::DeadlineTimer<resip::MultiReactor> mTimer;

   FileTransferItemEndReason mEndReason;
};

class XmppFileSender
   : public gloox::BytestreamDataHandler
   , public resip::DeadlineTimerHandler
{
public:
   XmppFileSender(
      XmppFileTransferManagerImpl& xmppFileTransferManager,
      XmppFileTransferHandle transfer,
      XmppFileTransferItemHandle handle,
      const std::string& streamId,
      const gloox::JID& target,
      const std::string& filename,
      const std::string& localFilePath,
      const std::string& description,
      uint64_t filesize,
      const std::string& mimetype
   );

   virtual ~XmppFileSender();

   void transfer();

   void setBytestream(gloox::Bytestream* bytestream);

   void cancel();

   void notifyFTRequestError(const gloox::IQ& iq);

   const std::string& getLocalFilePath() const;

   void handleStreamTypeAttempted(gloox::SIProfileFT::StreamType streamType);

   void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   const gloox::JID& getPeerJID() const { return mTarget; }

   void setEndReason(FileTransferItemEndReason reason);

private:
   // gloox::BytestreamDataHandler
   virtual void handleBytestreamData(gloox::Bytestream* bytestream, const std::string& data) OVERRIDE;
   virtual void handleBytestreamError(gloox::Bytestream* bytestream, const gloox::IQ& iq) OVERRIDE;
   virtual void handleBytestreamOpen(gloox::Bytestream* bytestream) OVERRIDE;
   virtual void handleBytestreamClose(gloox::Bytestream* bytestream) OVERRIDE;

private:
   const XmppFileTransferHandle mTransfer;
   const XmppFileTransferItemHandle mHandle;

   XmppFileTransferManagerImpl& mXmppFileTransferManager;
   const std::string mStreamId;
   const gloox::JID mTarget;
   const std::string mFileName;
   const std::string mDescription;
   const std::string mLocalFilePath;
   const uint64_t mFileSize;
   uint64_t mFileSizeProcessed;
   unsigned int mLastProgress;
   const std::string mMimeType;
   const std::string mHash;

#ifdef ANDROID
   AndroidFileAccess mSendFile;
#else
   std::filebuf mSendFile;
#endif

   gloox::Bytestream* mBytestream;

   TransferState mTransferState;

   int mStreamTypesAttempted;

   std::vector<char> mReadBuffer;

   resip::DeadlineTimer<resip::MultiReactor> mTimer;

   FileTransferItemEndReason mEndReason;
};

}
}

#endif // __CPCAPI2_XMPP_FILE_TRANSFER_MANAGER_IMPL_H__
