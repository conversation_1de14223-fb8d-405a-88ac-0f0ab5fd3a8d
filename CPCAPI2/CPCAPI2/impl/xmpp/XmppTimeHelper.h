#pragma once

#ifndef __XMPP_TIME_HELPER_H__
#define __XMPP_TIME_HELPER_H__

#include <ctime>

#ifdef ANDROID
# if defined(__LP64__)
#  include <time.h>
# else
#  include <time64.h>
# endif
#endif

namespace CPCAPI2
{
namespace TimeHelper
{
   /** bliu: copied over from SyncManagerImpl.cpp
   * Return the number of milliseconds since the UNIX epoch (Jan 1st, 1970).
   * NB: Win32 epoch is not the same.
   */
   static uint64_t millisSinceEpoch()
   {
      uint64_t result = 0;
#ifdef _WIN32
      FILETIME ft;
      GetSystemTimeAsFileTime(&ft);

      LARGE_INTEGER date, adjust;
      date.HighPart = ft.dwHighDateTime;
      date.LowPart = ft.dwLowDateTime;

      // 100-nanoseconds = milliseconds * 10000
      adjust.QuadPart = 11644473600000 * 10000;

      // removes the diff between 1970 and 1601
      date.QuadPart -= adjust.QuadPart;

      // result in millis, not nano-intervals
      result = date.QuadPart / 10000;
#else
      struct timeval now;
      gettimeofday(&now, NULL);
      result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
#endif
      return result;
   }

   static tm* gmtime(const uint64_t* timestamp)
   {
      #ifdef _WIN32
         __time64_t t = *timestamp;
         return _gmtime64(&t);
      #elif (defined(ANDROID) && !defined(__LP64__))
         int64_t t = *timestamp;
         return gmtime64(&t);
      #else
         time_t t = *timestamp;
         return ::gmtime(&t);
      #endif
   }

   // bliu: TODO thread-safty and portability
   static uint64_t timegm(tm *tm, bool& err)
   {
   #ifdef _WIN32
      return _mkgmtime64(tm);
   #elif (defined(ANDROID) && !defined(__LP64__))
      // time_t is signed on Android.
      static const time_t kTimeMax = ~(1L << (sizeof(time_t) * CHAR_BIT - 1));
      static const time_t kTimeMin = (1L << (sizeof(time_t) * CHAR_BIT - 1));
      time64_t result = timegm64(tm);
      if (result < kTimeMin || result > kTimeMax)
         err = true;
      return result;
   #else
      return timegm(tm);
   #endif
   }

   static bool to_time(const std::string& timestamp, uint64_t& t, uint16_t& millisecond)
   {
      std::tm tt = { 0 };
      bool err = false;

      if (sscanf(timestamp.c_str(), "%4d%2d%2dT%2d:%2d:%2d", &tt.tm_year, &tt.tm_mon, &tt.tm_mday, &tt.tm_hour, &tt.tm_min, &tt.tm_sec) == 6)
      {
         tt.tm_year -= 1900;
         tt.tm_mon -= 1;
         t = TimeHelper::timegm(&tt, err);
         return t != -1 && !err;
      }

      if (sscanf(timestamp.c_str(), "%4d-%2d-%2dT%2d:%2d:%2dZ", &tt.tm_year, &tt.tm_mon, &tt.tm_mday, &tt.tm_hour, &tt.tm_min, &tt.tm_sec) == 6)
      {
         tt.tm_year -= 1900;
         tt.tm_mon -= 1;
         t = TimeHelper::timegm(&tt, err);
         return t != -1 && !err;
      }

      if (sscanf(timestamp.c_str(), "%4d-%2d-%2dT%2d:%2d:%2d.%3huZ", &tt.tm_year, &tt.tm_mon, &tt.tm_mday, &tt.tm_hour, &tt.tm_min, &tt.tm_sec, &millisecond) == 7)
      {
         tt.tm_year -= 1900;
         tt.tm_mon -= 1;
         t = TimeHelper::timegm(&tt, err);
         if (t == -1) millisecond = 0;
         return t != -1 && !err;
      }

      return false;
   }

} // namespace TimeHelper
} // namespace CPCAPI2

#endif // __XMPP_TIME_HELPER_H__
