//
//  CpcXepMessageProcessingHint
//  BriaVoip
//
//  Created by <PERSON> on July 05, 2021.
//  Copyright 2021 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_PROCESS_HINT_H__
#define CPC_XEP_MESSAGE_PROCESS_HINT_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0334} (Message Processing Hints).
*/
class CpcXepMessageProcessingHint : public gloox::StanzaExtension
{
public:
   enum MessageProcessingHintType
   {
      MessageProcessingHintType_Invalid,
      MessageProcessingHintType_StoreOffline,
      MessageProcessingHintType_Store
   };

   CpcXepMessageProcessingHint(MessageProcessingHintType type);
   CpcXepMessageProcessingHint(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   MessageProcessingHintType type() const { return mType; }

   static const std::string XMLNS_MESSAGE_PROCESSING_HINT;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   MessageProcessingHintType mType;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_PROCESS_HINT_H__
