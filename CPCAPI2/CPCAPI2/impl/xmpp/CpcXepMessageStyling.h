//
//  CpcXepMessageStyling
//  BriaVoip
//
//  Created by <PERSON> on March 19, 2025.
//  Copyright 2025 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_STYLING_H__
#define CPC_XEP_MESSAGE_STYLING_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0393} (message retraction).
*/
class CpcXepMessageStyling : public gloox::StanzaExtension
{
public:
   CpcXepMessageStyling(bool styled);
   CpcXepMessageStyling(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   const bool styled() const { return mStyled; }

   static const std::string XMLNS_MESSAGE_STYLING;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   bool mStyled;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_STYLING_H__
