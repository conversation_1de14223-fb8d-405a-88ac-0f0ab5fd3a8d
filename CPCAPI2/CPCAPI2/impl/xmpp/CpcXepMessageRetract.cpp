//
//  CpcXepMessageRetract.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageRetract.h"

const std::string CpcXepMessageRetract::XMLNS_MESSAGE_RETRACT = "urn:xmpp:message-retract:1";

CpcXepMessageRetract::CpcXepMessageRetract(std::string targetId)
   : StanzaExtension(EXepMessageRetract)
   , mTargetId(targetId)
   , mValid(true)
{
}

CpcXepMessageRetract::CpcXepMessageRetract(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageRetract)
   , mValid(false)
{
   if (tag == NULL) return;

   if (auto retract = tag->findTag("/message/retract"))
   {
      mTargetId = retract->findAttribute( "id" );
   }

   mValid = true;
}

const std::string& CpcXepMessageRetract::filterString() const
{
   static const std::string filter =
      "/message/retract[@xmlns='" + XMLNS_MESSAGE_RETRACT + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageRetract::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageRetract(tag);
}

gloox::Tag* CpcXepMessageRetract::tag() const
{
   gloox::Tag* t = new gloox::Tag("retract", "xmlns", XMLNS_MESSAGE_RETRACT);

   if (!mValid) return NULL;

   t->addAttribute("id", mTargetId);

   return t;
}

gloox::StanzaExtension* CpcXepMessageRetract::clone() const
{
   return new CpcXepMessageRetract(*this);
}
