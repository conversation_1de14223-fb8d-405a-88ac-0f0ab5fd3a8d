#pragma once

#if !defined(CPCAPI2_XMPP_STACK_LOG_H)
#define CPCAPI2_XMPP_STACK_LOG_H

#include <rutil/Subsystem.hxx>
#include <client.h>

namespace CPCAPI2
{
class LocalLogger;

class XmppStackLog : public gloox::LogHandler
{
public:
   XmppStackLog(bool logXmppStanzas, LocalLogger* localLogger);
   virtual ~XmppStackLog();

   // gloox::LogHandler interface
   virtual void handleLog(gloox::LogLevel level, gloox::LogArea area, const std::string& message);

private:
   bool mLogXmppStanzas;
   LocalLogger* mLocalLogger;
};
} // CPCAPI2

#endif
