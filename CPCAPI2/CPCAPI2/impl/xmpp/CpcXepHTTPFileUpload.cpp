//
//  CpcXepHTTPFileUpload.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 14, 2016.
//  Copyright 2016 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepHTTPFileUpload.h"

#include <ctime>
#include <cstring>
#include <sstream>

#include "curl/curl.h"

const std::string CpcXepHTTPFileUpload::XMLNS_HTTP_FILE_UPLOAD = "urn:xmpp:http:upload:0";

CpcXepHTTPFileUpload::CpcXepHTTPFileUpload(const std::string& file, uint64_t size)
   : StanzaExtension(EXepHTTPFileUpload)
   , mFile(file)
   , mSize(size)
   , mValid(true)
{
}

CpcXepHTTPFileUpload::CpcXepHTTPFileUpload(const gloox::Tag* tag)
   : StanzaExtension(EXepHTTPFileUpload)
   , mSize(0)
   , mValid(false)
{
   if (tag == NULL) return;

   if (auto _put = tag->findTag("/iq/slot/put"))
   {
      mPut = _put->findAttribute("url");

      if (auto _authorization = _put->findChild("header", "name", "Authorization"))
      {
         mAuthorization = _authorization->cdata();
      }

      if (auto _cookie = _put->findChild("header", "name", "Cookie"))
      {
         mCookie = _cookie->cdata();
      }
   }

   if (auto _get = tag->findTag("/iq/slot/get"))
   {
      mGet = _get->findAttribute("url");
   }

   mValid = true;
}

CpcXepHTTPFileUpload::~CpcXepHTTPFileUpload()
{
}

const std::string& CpcXepHTTPFileUpload::put() const
{
   return mPut;
}

const std::string& CpcXepHTTPFileUpload::authorization() const
{
   return mAuthorization;
}

const std::string& CpcXepHTTPFileUpload::cookie() const
{
   return mCookie;
}

const std::string& CpcXepHTTPFileUpload::get() const
{
   return mGet;
}

const std::string& CpcXepHTTPFileUpload::filterString() const
{
   static const std::string filter =
      "/iq/slot[@xmlns='" + XMLNS_HTTP_FILE_UPLOAD + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepHTTPFileUpload::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepHTTPFileUpload(tag);
}

gloox::Tag* CpcXepHTTPFileUpload::tag() const
{
   gloox::Tag* _request = new gloox::Tag("request", "xmlns", XMLNS_HTTP_FILE_UPLOAD);

   if (!mValid) return _request;

   char* escaped = curl_easy_escape(NULL, mFile.c_str(), 0);
   _request->addAttribute("filename", escaped);
   curl_free(escaped);
   std::stringstream ss;
   ss << mSize;
   _request->addAttribute("size", ss.str());

   return _request;
}

gloox::StanzaExtension* CpcXepHTTPFileUpload::clone() const
{
   return new CpcXepHTTPFileUpload(*this);
}
