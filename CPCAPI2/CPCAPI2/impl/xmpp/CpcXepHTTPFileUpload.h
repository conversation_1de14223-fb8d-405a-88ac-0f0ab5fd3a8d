//
//  CpcXepHTTPFileUpload.h
//  BriaVoip
//
//  Created by <PERSON> on August 14, 2019.
//  Copyright 2019 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_HTTP_FILE_UPLOAD_H__
#define CPC_XEP_HTTP_FILE_UPLOAD_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0363} (HTTP File Upload).
*/
class CpcXepHTTPFileUpload : public gloox::StanzaExtension
{
public:
   CpcXepHTTPFileUpload(const std::string& file, uint64_t size);
   CpcXepHTTPFileUpload(const gloox::Tag* tag = NULL);
   virtual ~CpcXepHTTPFileUpload();

   static const std::string XMLNS_HTTP_FILE_UPLOAD;

   const std::string& put() const;
   const std::string& authorization() const;
   const std::string& cookie() const;
   const std::string& get() const;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   // request
   const std::string mFile;
   const uint64_t mSize;

   // slot
   std::string mPut;
   std::string mAuthorization;
   std::string mCookie;
   std::string mGet;

   bool mValid;
};

#endif // CPC_XEP_HTTP_FILE_UPLOAD_H__
