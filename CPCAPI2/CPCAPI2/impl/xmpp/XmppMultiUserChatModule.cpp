#include "brand_branded.h"

#include "interface/public/xmpp/XmppMultiUserChatManager.h"
#include "interface/public/xmpp/XmppMultiUserChatState.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "XmppMultiUserChatManagerInterface.h"
#include "XmppMultiUserChatStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace XmppMultiUserChat
{
XmppMultiUserChatManager* XmppMultiUserChatManager::getInterface( CPCAPI2::Phone* cpcPhone )
{
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppMultiUserChatManagerInterface>(phone, "XmppMultiUserChatManagerInterface");
#else
   return NULL;
#endif
}

XmppMultiUserChatStateManager* XmppMultiUserChatStateManager::getInterface(XmppMultiUserChatManager* manager)
{
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
   XmppMultiUserChatManagerInterface* parent = dynamic_cast<XmppMultiUserChatManagerInterface*>(manager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<XmppMultiUserChatStateImpl>(phone, "XmppMultiUserChatStateManager", parent);
#else
   return NULL;
#endif
}

}
}
