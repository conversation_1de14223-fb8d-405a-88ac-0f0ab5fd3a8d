#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
#include "XmppPrivacyImpl.h"

using namespace gloox;

namespace CPCAPI2
{
namespace XmppPrivacy
{

static const std::string sInvisiblePrivacyListName = "invisible";  // recommended by XEP-0126
static const std::string sPresenceInDenyPrivacyListName = "deny-presence-in";
static const PrivacyItem sInvisibleRule (PrivacyItem::TypeUndefined, PrivacyItem::ActionDeny, PrivacyItem::PacketPresenceOut, "");
static const PrivacyItem sPresenceInDenyRule(PrivacyItem::TypeUndefined, PrivacyItem::ActionDeny, PrivacyItem::PacketPresenceIn, "");
static const PrivacyItem sDefaultAllowRule (PrivacyItem::TypeUndefined, PrivacyItem::ActionAllow, PrivacyItem::PacketAll, "");

XmppPrivacyImpl::XmppPrivacyImpl(XmppAccount::XmppAccountImpl& account)
   : mAccount(account)
   , mGlooxPrivacyManager(NULL)
   , mMyPresence(XmppRoster::PresenceType_Unknown)
   , mMyUserActivityGeneralType(XmppRoster::ActivityInvalidGeneralType)
   , mMyUserActivitySpecificType(XmppRoster::ActivityInvalidSpecificType)
   , mPresenceInDenied(false)
{
   mAccount.registerAccountObserver(this);
   mAccount.registerDiscoObserver(this);
}

XmppPrivacyImpl::~XmppPrivacyImpl()
{
   mAccount.unregisterAccountObserver(this);
   mAccount.unregisterDiscoObserver(this);

   cleanup();
}

void XmppPrivacyImpl::cleanup()
{
   if (mGlooxPrivacyManager != NULL)
   {
      mGlooxPrivacyManager->removePrivacyListHandler();
      delete mGlooxPrivacyManager; // must come before deletion of mClient
      mGlooxPrivacyManager = NULL;
   }

   mDefaultPrivacyListName.clear();
   mActivePrivacyListName.clear();
   mAllPrivacyListNames.clear();

   mMyPresence = XmppRoster::PresenceType_Unknown;
   mMyMsg = "";
   mMyUserActivityGeneralType = XmppRoster::ActivityInvalidGeneralType;
   mMyUserActivitySpecificType = XmppRoster::ActivityInvalidSpecificType;
   mMyUserActivityText = "";
   mPresenceInDenied = false;
   mPendingPrivacyItems.clear();
}

void XmppPrivacyImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
}

void XmppPrivacyImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
}

void XmppPrivacyImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppPrivacyImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppPrivacyImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   // don't need any handling here because XmppPrivacyImpl will be deleted along with XmppAccountImpl
}

void XmppPrivacyImpl::onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info)
{
   if (!info.hasFeature(XMLNS_PRIVACY)) return;
   if (mGlooxPrivacyManager != NULL) return;

   mGlooxPrivacyManager = new PrivacyManager(mAccount.getGlooxClient());
   mGlooxPrivacyManager->registerPrivacyListHandler(this);
   mGlooxPrivacyManager->requestListNames();
}

void XmppPrivacyImpl::onXmppDiscoCompleted()
{
}

// begin: gloox::PrivacyListHandler
void XmppPrivacyImpl::handlePrivacyListNames(const std::string& activeListName, const std::string& defaultListName, const gloox::StringList& allListsNames)
{
   if(mGlooxPrivacyManager == NULL) return;

   if (allListsNames.size() > 0)
      mAllPrivacyListNames = allListsNames;

   if(activeListName.size() > 0)
      mActivePrivacyListName = activeListName;

   if(defaultListName.size() > 0)
      mDefaultPrivacyListName = defaultListName;

   if(activeListName.size() == 0 && defaultListName.size() > 0)
      mGlooxPrivacyManager->setActive(defaultListName);

   if(defaultListName.size() == 0 && activeListName.size() > 0)
      mGlooxPrivacyManager->setDefault(activeListName);

   if(activeListName.size() == 0 && defaultListName.size() == 0)
   {
      for(gloox::StringList::const_iterator iter = allListsNames.begin(); iter != allListsNames.end(); iter++)
      {
         std::string name = *iter;
         if (name.size() == 0 || name == sInvisiblePrivacyListName || name == sPresenceInDenyPrivacyListName)
            continue;

         mDefaultPrivacyListName = name;
         mActivePrivacyListName = name;

         mGlooxPrivacyManager->setDefault(name);
         mGlooxPrivacyManager->setActive(name);
      }
   }

   //if(allListsNames.size () == 0)
   //   mPrivacyListXMPP->NotifyPrivacyListLoaded(true);

   if( mActivePrivacyListName.size () == 0 )
      return;

   //SUA_TRACES_DEBUG("XMPP: Active Privacy List is: \n" << mActivePrivacyListName);

   mGlooxPrivacyManager->requestList(mActivePrivacyListName);
}

void XmppPrivacyImpl::handlePrivacyList(const std::string& listName, const gloox::PrivacyListHandler::PrivacyList& items)
{
   //CXsAutoLock cLock(*mLock);

   //FIRST
   //{
   //   GOTOFINALLY_IF(mPrivacyListXMPP == NULL)

   //   PrivacyStoreManagerPtr privacyStoreManager = PrivacyStoreManager::create(mPrivacyListXMPP);
   //   GOTOFINALLY_IF(privacyStoreManager == NULL)

   //   typedef std::map<std::string, cpsi::PrivacyRuleModifierPtr> RULES_MAP;
   //   RULES_MAP rulesMap;

   //   for(PrivacyListHandler::PrivacyList::const_iterator it = items.begin(); it != items.end(); it++)
   //   {
   //      const PrivacyItem& item = *it;

   //      PrivacyItem::ItemType type = item.type();
   //      if(type != PrivacyItem::TypeJid) // TO DO, store other
   //         continue;

   //      std::string uri = item.value();

   //      if(uri.size() == 0)
   //         continue;

   //      int packetType = item.packetType();
   //      PrivacyItem::ItemAction action = item.action();

   //      PrivacyRuleModifierPtr rule;
   //      RULES_MAP::iterator iterFind = rulesMap.find(uri);
   //      if(iterFind != rulesMap.end())
   //      {
   //         rule = iterFind->second;
   //      }
   //      else
   //      {
   //         wstring subject = toWString(uri);
   //         wstring::size_type at = subject.find('@');

   //         if(at == std::string::npos)
   //         {
   //            rule = mPrivacyListXMPP->createPrivacyRule(PrivacyRule::Domain, subject);
   //         }
   //         else
   //         {
   //            rule = mPrivacyListXMPP->createPrivacyRule(PrivacyRule::Uri, subject);
   //         }

   //         rulesMap[uri] = rule;
   //      }

   //      PrivacyRule::Action cpsiAction = PrivacyRule::Undefined;
   //      if(action == PrivacyItem::ActionAllow)
   //         cpsiAction = PrivacyRule::Allow;
   //      else if(action == PrivacyItem::ActionDeny)
   //         cpsiAction = PrivacyRule::Deny;

   //      if(cpsiAction == PrivacyRule::Undefined)
   //         continue;

   //      if(packetType & PrivacyItem::PacketMessage)
   //         rule->setAction(PrivacyRule::InstantMessage, cpsiAction);
   //      if(packetType & PrivacyItem::PacketPresenceOut)
   //         rule->setAction(PrivacyRule::Presence, cpsiAction);
   //   }

   //   PrivacyRuleVector loadedRules;
   //   for(RULES_MAP::const_iterator iter = rulesMap.begin(); iter != rulesMap.end(); iter++)
   //   {
   //      PrivacyRuleModifierPtr rule = iter->second;

   //      loadedRules.push_back(rule);
   //   }

   //   privacyStoreManager->addUpdateDeletePrivacyRules(
   //      loadedRules,
   //      PrivacyStorage::XMPP,
   //      true
   //   );

   //   mPrivacyListXMPP->NotifyPrivacyListLoaded(true);
   //
   if (listName != sInvisiblePrivacyListName && listName == mActivePrivacyListName)
   {
      mAccount.publishPresence(mMyPresence, mMyMsg, mMyUserActivityGeneralType, mMyUserActivitySpecificType, mMyUserActivityText);
   }

   //   SUA_TRACES_INFO("XMPP: downloaded Privacy List: \n" + mPrivacyListXMPP->toString());
   //}
   //FINALLY
   //{
   //}
}

void XmppPrivacyImpl::handlePrivacyListChanged(const std::string& listName)
{
   //CXsAutoLock cLock(*mLock);

   //FIRST
   //{
   //   GOTOFINALLY_IF(mGlooxPrivacyManager == NULL)

      if (std::find(mAllPrivacyListNames.begin(), mAllPrivacyListNames.end(), listName) == mAllPrivacyListNames.end())
      {
         // new list was added
         mAllPrivacyListNames.push_back(listName);
      }
   //
   //   GOTOFINALLY_IF(mActivePrivacyListName != listName)
      if (mActivePrivacyListName != listName) return;

      mGlooxPrivacyManager->requestList(mActivePrivacyListName);
   //}
   //FINALLY
   //{
   //}
}

void XmppPrivacyImpl::handlePrivacyListResult(const std::string& token, PrivacyListResult result)
{
   // bliu: TODO add token match and error handling
}
// end: gloox::PrivacyListHandler

void XmppPrivacyImpl::setInvisible(bool invisible)
{
   if (!mAccount.isConnected()) return;
   if (mGlooxPrivacyManager == NULL) return;

   if (invisible)
   {
      // 1. Publish Unavailable so the local user is offline
      mAccount.setPresence(XmppRoster::PresenceType_Unavailable, "", 0);

      // 2. Enable invisiblity privacy list
      if (std::find(mAllPrivacyListNames.begin(), mAllPrivacyListNames.end(), sInvisiblePrivacyListName) == mAllPrivacyListNames.end())
      {
         mAllPrivacyListNames.push_back(sInvisiblePrivacyListName);
      }

      // if it exists, we'll overwrite it
      gloox::PrivacyListHandler::PrivacyList privacyList = createInvisibileList();

      std::string token = mGlooxPrivacyManager->store(sInvisiblePrivacyListName, privacyList);

      mGlooxPrivacyManager->setActive(sInvisiblePrivacyListName);

      mActivePrivacyListName = sInvisiblePrivacyListName;

      // 3. Create a fake available presence to let server know we can receive messages
      mAccount.setPresence(XmppRoster::PresenceType_Available, "", 0);

      //resetPing();
   }
   else
   {
      // were we invisible before?
      if (mActivePrivacyListName == sInvisiblePrivacyListName)
      {
         mGlooxPrivacyManager->unsetActive();

         mActivePrivacyListName = "";
      }

      if (mActivePrivacyListName.size() == 0 && mDefaultPrivacyListName.size() > 0)
      {
         // fallback to default list
         gloox::PrivacyListHandler::PrivacyList privacyList = createPrivacyList();

         if(privacyList.size() == 0)
         {
            privacyList.push_back(sDefaultAllowRule);
         }

         mGlooxPrivacyManager->store(mDefaultPrivacyListName, privacyList);

         mGlooxPrivacyManager->setActive(mDefaultPrivacyListName);
         mGlooxPrivacyManager->setDefault(mDefaultPrivacyListName);

         mActivePrivacyListName = mDefaultPrivacyListName;

         //SUA_TRACES_DEBUG("XMPP: Active/Default privacy list was changed to: \n" << mActivePrivacyListName);
      }
   }
}

void XmppPrivacyImpl::setPresenceInDeny(bool deny)
{
   if (!mAccount.isConnected()) return;
   if (mGlooxPrivacyManager == NULL) return;

   mPresenceInDenied = deny;

   if (mActivePrivacyListName == sInvisiblePrivacyListName)
   {
      //sPresenceInDenyRule will be added via setInvisible() and mPresenceInDenied
      setInvisible(true);
   }
   else
   {
      if (deny)
      {
         if (std::find(mAllPrivacyListNames.begin(), mAllPrivacyListNames.end(), sPresenceInDenyPrivacyListName) == mAllPrivacyListNames.end())
         {
            mAllPrivacyListNames.push_back(sPresenceInDenyPrivacyListName);
         }

         gloox::PrivacyListHandler::PrivacyList privacyList = createPresenceInDenyList();
         std::string token = mGlooxPrivacyManager->store(sPresenceInDenyPrivacyListName, privacyList);
         mGlooxPrivacyManager->setActive(sPresenceInDenyPrivacyListName);
         mActivePrivacyListName = sPresenceInDenyPrivacyListName;
      }
      else
      {
         if (mActivePrivacyListName == sPresenceInDenyPrivacyListName)
         {
            mGlooxPrivacyManager->unsetActive();
            mActivePrivacyListName = "";
         }

         if (mActivePrivacyListName.size() == 0 && mDefaultPrivacyListName.size() > 0)
         {
            gloox::PrivacyListHandler::PrivacyList privacyList = createPrivacyList();

            if (privacyList.size() == 0)
            {
               privacyList.push_back(sDefaultAllowRule);
            }

            mGlooxPrivacyManager->store(mDefaultPrivacyListName, privacyList);

            mGlooxPrivacyManager->setActive(mDefaultPrivacyListName);
            mGlooxPrivacyManager->setDefault(mDefaultPrivacyListName);

            mActivePrivacyListName = mDefaultPrivacyListName;
         }
      }
   }
}

void XmppPrivacyImpl::setMyPresence(const XmppRoster::PresenceType& presence, const cpc::string& msg, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   mMyPresence = presence;
   mMyMsg = msg;
   mMyUserActivityGeneralType = userActivityGeneralType;
   mMyUserActivitySpecificType = userActivitySpecificType;
   mMyUserActivityText = userActivityText;
}

void XmppPrivacyImpl::addPrivacyRule(const PrivacyItem& rule)
{
   gloox::PrivacyListHandler::PrivacyList::iterator it = std::find(mPendingPrivacyItems.begin(), mPendingPrivacyItems.end(), rule);

   if (it != mPendingPrivacyItems.end()) return;

   mPendingPrivacyItems.push_back(rule);
}

void XmppPrivacyImpl::removePrivacyRule(const PrivacyItem& rule)
{
   mPendingPrivacyItems.remove(rule);
}

const gloox::PrivacyListHandler::PrivacyList XmppPrivacyImpl::createPrivacyList() const
{
   PrivacyListHandler::PrivacyList privacyList = mPendingPrivacyItems;

   if (mPresenceInDenied)
      privacyList.push_back(sPresenceInDenyRule);

   //FIRST
   //{
   //   GOTOFINALLY_IF(mPrivacyListXMPP == NULL)

   //   PrivacyRuleVector privacyRules = mPrivacyListXMPP->getAllRules();

   //   SUA_TRACES_INFO("XMPP: uploaded Privacy List: \n" + mPrivacyListXMPP->toString());

   //   for(PrivacyRuleVector::const_iterator iter = privacyRules.begin(); iter != privacyRules.end(); iter++)
   //   {
   //      PrivacyRulePtr privacyRule = *iter;
   //      if(privacyRule == NULL)
   //         continue;

   //      wstring subject = privacyRule->getSubject();
   //      PrivacyRule::Type type = privacyRule->getType();

   //      // Do only URIs and domains
   //      // Domains are of jid type in XMPP too
   //      if(type != PrivacyRule::Uri && type != PrivacyRule::Domain)
   //         continue;

   //      PrivacyRule::Action actionIM = privacyRule->getAction(PrivacyRule::InstantMessage);
   //      if(actionIM != PrivacyRule::Undefined)
   //      {
   //         PrivacyItem ruleIM(
   //            PrivacyItem::TypeJid,
   //            actionIM == PrivacyRule::Allow ? PrivacyItem::ActionAllow : PrivacyItem::ActionDeny,
   //            PrivacyItem::PacketMessage,
   //            toString(subject)
   //         );

   //         privacyList.push_back(ruleIM);
   //      }

   //      PrivacyRule::Action actionPresence = privacyRule->getAction(PrivacyRule::Presence);
   //      if(actionPresence != PrivacyRule::Undefined)
   //      {
   //         PrivacyItem rulePresence(
   //            PrivacyItem::TypeJid,
   //            actionPresence == PrivacyRule::Allow ? PrivacyItem::ActionAllow : PrivacyItem::ActionDeny,
   //            PrivacyItem::PacketPresenceOut,
   //            toString(subject)
   //         );

   //         privacyList.push_back(rulePresence);
   //      }
   //   }
   //}
   //FINALLY
   //{
      return privacyList;
   //}
}

const gloox::PrivacyListHandler::PrivacyList XmppPrivacyImpl::createInvisibileList() const
{
   PrivacyListHandler::PrivacyList privacyList;

   //SUA_TRACES_DEBUG("XMPP: Creating invisible Privacy List...");

   // start with the normal "visible" list
   PrivacyListHandler::PrivacyList defaultPrivacyList = createPrivacyList();

   for(PrivacyListHandler::PrivacyList::const_iterator iter = defaultPrivacyList.begin(); iter != defaultPrivacyList.end(); iter++)
   {

#if 0 // bliu: allow pending rules to be added to the privacy list
      // !gkwei! Remove whitelisted presence rules as people on your whitelist
      // will end up seeing you as Available, which is not what we want.
      // invisibleRule does NOT have higher priority than other rules
      if ((*iter).action() == PrivacyItem::ActionAllow && (*iter).packetType() & PrivacyItem::PacketPresenceOut)
      {
         continue;
      }
#endif

      privacyList.push_back(*iter);
   }

   if (mPresenceInDenied)
      privacyList.push_back(sPresenceInDenyRule);

   privacyList.push_back(sInvisibleRule);

   return privacyList;
}

const gloox::PrivacyListHandler::PrivacyList XmppPrivacyImpl::createPresenceInDenyList() const
{
   PrivacyListHandler::PrivacyList privacyList;

   // start with the normal "visible" list
   PrivacyListHandler::PrivacyList defaultPrivacyList = createPrivacyList();

   if (defaultPrivacyList.size() == 0)
      privacyList.push_back(sPresenceInDenyRule);

   for (PrivacyListHandler::PrivacyList::const_iterator iter = defaultPrivacyList.begin(); iter != defaultPrivacyList.end(); iter++)
   {
       if ((*iter).action() == PrivacyItem::ActionAllow && (*iter).packetType() & PrivacyItem::PacketPresenceIn)
      {
         continue;
      }

      privacyList.push_back(*iter);
   }

   return privacyList;
}

} // namespace XmppPrivacy
} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ACCOUNT_MODULE
