#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)

#include <algorithm>
#include <string>
#include <sstream>

#include "XmppChatManagerInterface.h"
#include "XmppChatManagerImpl.h"
#include "XmppChatInfo.h"

#include "phone/Phone.h"
#include "phone/PhoneInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountImpl.h"

#include <message.h>
#include <jid.h>
#include <gloox.h>
#include <messagesession.h>
#include <libxml/xpath.h>
#include <libxml/xpathInternals.h>
#include "util/LibxmlSharedUsage.h"
#include "util/ResipConv.h"
#include "util/cpc_logger.h"
#include "log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CP_LOCAL_LOGGER_VAR mLocalLogger

#define XMPP_UNIQUE_ID_MIN_SIGNIFICANT_LENGTH 8

#define FIRE_ERROR(msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   mAccountIf->fireError(ss.str().c_str());\
   LocalErrLog("XmppChatManagerInterface::FIRE_ERROR {}", ss.str());\
}

namespace CPCAPI2
{

namespace XmppChat
{

XmppChatManagerInterface::XmppChatManagerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mPreRelease(false)
   , mAccountIf(dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone)))
   , mLocalLogger(mPhone->localLogger())
   , mRemoteSyncHelperIf(NULL)
{
}

XmppChatManagerInterface::~XmppChatManagerInterface()
{
}

void XmppChatManagerInterface::addSdkObserver(XmppChatHandler* observer)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), observer) != mSdkObservers.end())
   {
      StackLog(<< "XmppChatManagerInterface::addSdkObserver(): XmppChatManagerInterface observer: " << observer << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppChatManagerInterface::addSdkObserver(): XmppChatManagerInterface observer: " << observer);
   mSdkObservers.push_back(observer);
}

void XmppChatManagerInterface::removeSdkObserver(XmppChatHandler* observer)
{
   std::list<XmppChatHandler*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), observer);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppChatManagerInterface::removeSdkObserver(): XmppChatManagerInterface observer: " << observer << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppChatManagerInterface::getChatHandles(cpc::vector<XmppChatHandle>& chats)
{
   for (ImplMap::const_iterator i = mImplMap.begin(); i != mImplMap.end(); ++i)
   {
      chats.push_back(i->first);
   }
}

void XmppChatManagerInterface::PreRelease()
{
   LocalDebugLog("XmppChatManagerInterface::PreRelease");

   mPreRelease = true;

   cpc::vector<XmppChatHandle> chats;
   getChatHandles(chats);
   for (cpc::vector<XmppChatHandle>::iterator i = chats.begin(); i != chats.end(); ++i)
   {
      XmppChatHandle h = (*i);
      if (std::shared_ptr<XmppChatManagerImpl> chat = getImpl(h))
      {
         // Cleanup the sdk observer handlers from the xmpp account, to remove possibility of dangling handlers being triggered
         for (std::list<XmppChatHandler*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            chat->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }
}

bool XmppChatManagerInterface::PreReleaseCompleted()
{
   // If there are no more chats, we're done.
   return (mImplMap.size() == 0);
}

void XmppChatManagerInterface::Release()
{
   LocalDebugLog("XmppChatManagerInterface::Release");
   cpc::vector<XmppChatHandle> chats;
   getChatHandles(chats);

   for (cpc::vector<XmppChatHandle>::iterator i = chats.begin(); i != chats.end(); ++i)
   {
      XmppChatHandle h = (*i);
      if (std::shared_ptr<XmppChatManagerImpl> chat = getImpl(h))
      {
         // Cleanup the sdk observer handlers from the xmpp account, to remove possibility of dangling handlers being triggered
         for (std::list<XmppChatHandler*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            chat->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }

   delete this;
}

int XmppChatManagerInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppChatHandler* handler)
{
   StackLog(<< "XmppChatManagerInterface::setHandler(): xmpp account: " << account << " handler: " << handler);
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppChatManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      StackLog(<< "XmppChatManagerInterface::setHandler(): calling post for xmpp account: " << account << " handler: " << handler);
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppChatManagerInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppChatHandler* handler)
{
   StackLog(<< "XmppChatManagerInterface::setHandlerImpl(): xmpp account: " << account << " handler: " << handler);

   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      cpc::string msg = cpc::string("XmppChatManagerInterface::setHandler called with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      StackLog(<< "XmppChatManagerInterface::setHandlerImpl(): xmpp account: " << account << " creating new XmppChatManagerImpl");
      // Create a new manager impl
      impl = std::make_shared<XmppChatManagerImpl>(mPhone, *acct, *this); // Destroyed in the destructor of this class

      // Keep mapping account -> manager impl
      mImplMap[account] = impl;

      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppChatHandler*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
   }

   // Register the handler
   StackLog(<< "XmppChatManagerInterface::setHandlerImpl(): xmpp account: " << account << " registering the handler: " << handler);
   impl->setHandler(handler);
}

int XmppChatManagerInterface::setIMCommandHandler(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppChatIMCommandHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppChatManagerInterface::setIMCommandHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppChatManagerInterface::setIMCommandHandlerImpl(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppChatIMCommandHandler* handler)
{
   // Retrieve the chat manager associated with the account specified
   if (std::shared_ptr<XmppChatManagerImpl> impl = getImpl(account))
   {

#if 0 // bliu: TODO
      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppChatHandler*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
#endif

      impl->setHandler(handler);
      return;
   }

   cpc::string msg = cpc::string("XmppChatManager::setHandler with invalid account handle: ") +  cpc::to_string(account);
   mAccountIf->fireError(msg);
}

XmppChatHandle XmppChatManagerInterface::createChat(XmppAccount::XmppAccountHandle account)
{
   XmppChatHandle sessionHandle = XmppChatManagerImpl::sNextXmppChatHandle++;
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::createChatImpl, this, account, sessionHandle));
   return sessionHandle;
}

void XmppChatManagerInterface::createChat(XmppChatHandle chat, XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::createChatImpl, this, account, chat));
}

void XmppChatManagerInterface::createChatImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle sessionHandle)
{
   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Creating chat session with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      cpc::string msg = cpc::string("Creating chat session before account is connected: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppChatManagerImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      cpc::string msg = cpc::string("Creating chat session before setHandler() is called: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Create the chat information associated with the chat session and store it in the chat manager
   XmppChatInfo* chatInfo = new XmppChatInfo(); // Destroyed in XmppChatManagerImpl::endChat()
   chatInfo->accountHandle = account;
   chatInfo->handle = sessionHandle;
   impl->addChatInfo(chatInfo);
}

int XmppChatManagerInterface::addParticipant(XmppChatHandle chat, const cpc::string& participantAddress)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::addParticipantImpl, this, chat, participantAddress));
   return kSuccess;
}

void XmppChatManagerInterface::addParticipantImpl(XmppChatHandle chat, const cpc::string& participantAddress)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::addParticipant called with invalid handle: " << chat);
      return;
   }

   context.info->targetAddresses.push_back(gloox::JID(participantAddress.c_str()));
}

int XmppChatManagerInterface::start(XmppChatHandle chat)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::startImpl, this, chat));
   return kSuccess;
}

void XmppChatManagerInterface::startImpl(XmppChatHandle chat)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::start called with invalid handle: " << chat);
      return;
   }

   // Make sure there is one participant added to the session
   if (context.info->targetAddresses.empty())
   {
      // No participant specified. Send an error
      context.impl->fireError(context.info->handle, "Cannot start chat session. No participants have been added");
      return;
   }

   // Start the chat session
   context.impl->startChat(context.info);
}

int XmppChatManagerInterface::end(XmppChatHandle chat)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::endImpl, this, chat));
   return kSuccess;
}

void XmppChatManagerInterface::endImpl(XmppChatHandle chat)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::end called with invalid handle: " << chat);
      return;
   }

   LocalDebugLog("XmppChatManagerInterface::endImpl for handle {}", chat);

   // Terminate the chat session
   context.impl->endChat(context.info->handle, ChatEndReason_UserTerminatedLocally);
}

XmppChatMessageHandle XmppChatManagerInterface::sendMessage(XmppChatHandle chat, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendMessageImpl, this, chat, messageHandle, messageContent, htmlText, subject));
   return messageHandle;
}

void XmppChatManagerInterface::sendMessage(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendMessageImpl, this, chat, message, messageContent, htmlText, subject));
}

void XmppChatManagerInterface::sendMessageImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::sendMessage called with invalid handle: " << chat);
      return;
   }

   // Send the message
   context.impl->sendMessage(context.info, message, std::string(messageContent.c_str(), messageContent.size()), std::string(htmlText.c_str(), htmlText.size()), std::string(subject.c_str(), subject.size()));
}

XmppChatMessageHandle XmppChatManagerInterface::sendReaction(XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendReactionImpl, this, chat, messageHandle, target, reactions));
   return messageHandle;
}

void XmppChatManagerInterface::sendReaction(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendReactionImpl, this, chat, message, target, reactions));
}

void XmppChatManagerInterface::sendReactionImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::sendReaction called with invalid handle: " << chat);
      return;
   }

   std::vector<std::string> _reactions;
   cpc::vector<cpc::string> temp = reactions;   // this avoids a dangling pointer below
   for (auto it = temp.begin(); it != temp.end(); ++it)
      _reactions.push_back(it->c_str());

   // Send the message
   context.impl->sendReaction(context.info, message, target.c_str(), _reactions);
}

XmppChatMessageHandle XmppChatManagerInterface::sendMessageRetraction(XmppChatHandle chat, const cpc::string& target)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendMessageRetractionImpl, this, chat, messageHandle, target));
   return messageHandle;
}

void XmppChatManagerInterface::sendMessageRetraction(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendMessageRetractionImpl, this, chat, message, target));
}

void XmppChatManagerInterface::sendMessageRetractionImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& target)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::sendMessageRetraction called with invalid handle: " << chat);
      return;
   }

   // Send the message
   context.impl->sendMessageRetraction(context.info, message, target.c_str());
}

XmppChatMessageHandle XmppChatManagerInterface::replaceMessage(XmppChatHandle chat, const cpc::string& messageId, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::replaceMessageImpl, this, chat, messageHandle, messageId, messageContent, htmlText, subject));
   return messageHandle;
}

void XmppChatManagerInterface::replaceMessageImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& messageId, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::replaceMessage called with invalid handle: " << chat);
      return;
   }

   context.impl->replaceMessage(context.info, message, messageId.c_str(), std::string(messageContent.c_str(), messageContent.size()), std::string(htmlText.c_str(), htmlText.size()), std::string(subject.c_str(), subject.size()));
}

int XmppChatManagerInterface::accept(XmppChatHandle chat)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::acceptImpl, this, chat));
   return kSuccess;
}

void XmppChatManagerInterface::acceptImpl(XmppChatHandle chat)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::accept called with invalid handle: " << chat);
      return;
   }

   // Accept the chat session
   context.impl->acceptChat(context.info);
}

int XmppChatManagerInterface::reject(XmppChatHandle chat)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::rejectImpl, this, chat));
   return kSuccess;
}

void XmppChatManagerInterface::rejectImpl(XmppChatHandle chat)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::reject called with invalid handle: " << chat);
      return;
   }

   // Reject the chat session
   context.impl->rejectChat(context.info, 0);
}

int XmppChatManagerInterface::setIsComposingMessage(XmppChatHandle chat, int refreshInterval, int idleInterval)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::setIsComposingMessageImpl, this, chat, refreshInterval, idleInterval));
   return kSuccess;
}

void XmppChatManagerInterface::setIsComposingMessageImpl(XmppChatHandle chat, int refreshInterval, int idleInterval)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::setIsComposingMessage called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->setIsComposingMessage(context.info, refreshInterval, idleInterval);
}

XmppChatMessageHandle XmppChatManagerInterface::sendIMCommand(XmppChatHandle chat, int type, const cpc::string& payload, const cpc::string& htmlPayload)
{
   XmppChatMessageHandle message = XmppChatManagerImpl::sNextXmppChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::sendIMCommandImpl, this, chat, message, type, payload, htmlPayload));
   return message;
}

void XmppChatManagerInterface::sendIMCommandImpl(XmppChatHandle chat, XmppChatMessageHandle message, int type, const cpc::string& payload, const cpc::string& htmlPayload)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::sendIMCommand called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->sendIMCommand(context.info, message, type, payload, htmlPayload);
}

RemoteSyncXmppHelper::RemoteSyncXmppHelper* XmppChatManagerInterface::getRemoteSyncHelper()
{
   if (mRemoteSyncHelperIf == NULL)
   {
      mRemoteSyncHelperIf = RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(mPhone);
   }
   return mRemoteSyncHelperIf;
}

cpc::string XmppChatManagerInterface::getRemoteSyncFromID(XmppChatHandle chat, XmppChatMessageHandle message)
{
   return getRemoteSyncHelper()->getRemoteSyncFromID(chat, message);
}

cpc::string XmppChatManagerInterface::getRemoteSyncToID(XmppChatHandle chat, XmppChatMessageHandle message)
{
   return getRemoteSyncHelper()->getRemoteSyncToID(chat, message);
}

cpc::string XmppChatManagerInterface::getRemoteSyncConversationID( XmppChatHandle chat )
{
   return getRemoteSyncHelper()->getRemoteSyncConversationID(chat);
}

cpc::string XmppChatManagerInterface::getRemoteSyncUniqueID( XmppChatHandle chat, const cpc::string& stanzaID )
{
   return getRemoteSyncHelper()->getRemoteSyncUniqueID(chat, stanzaID);
}

cpc::string XmppChatManagerInterface::getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID)
{
   return getRemoteSyncHelper()->getRemoteSyncUniqueID2(stanzaID, threadID);
}

cpc::string XmppChatManagerInterface::getRemoteSyncEncodedContent( const cpc::string& plainText, const cpc::string& htmlText )
{
   return getRemoteSyncHelper()->getRemoteSyncEncodedContent(plainText, htmlText);
}

int XmppChatManagerInterface::getRemoteSyncDecodedContent( const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML )
{
   return getRemoteSyncHelper()->getRemoteSyncDecodedContent(remoteSyncEncodedContent, outPlainText, outHTML);
}

int XmppChatManagerInterface::validateChatHandle(XmppAccount::XmppAccountHandle account, XmppChatHandle chat)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::validateChatHandleImpl, this, account, chat));

   return 0;
}

void XmppChatManagerInterface::validateChatHandleImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle chat)
{
   if (std::shared_ptr<XmppChatManagerImpl> impl = getImpl(account))
   {
      impl->validateChatHandle(chat);
   }
   else
   {
      FIRE_ERROR("XmppChatManager::validateChatHandleImpl couldn't get impl from account handle");
   }
}

XmppChatMessageHandle XmppChatManagerInterface::notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus)
{
   // Generate a new message ID
   XmppChatMessageHandle handle = createMessage(chat);

   // Send the delivery notification
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDeliveredImpl_DEPRECATED, this, chat, handle, message, messageDeliveryStatus));
   return handle;
}

int XmppChatManagerInterface::notifyMessageDelivered(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus)
{
   // Send the delivery notification
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDeliveredImpl_DEPRECATED, this, chat, chatMessage, message, messageDeliveryStatus));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageDeliveredImpl_DEPRECATED(XmppChatHandle chat, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageDeliveryStatus messageDeliveryStatus)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageDelivered called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->notifyMessageDelivered(context.info, origMessage);
}

XmppChatMessageHandle XmppChatManagerInterface::notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus)
{
   // Generate a new message ID
   XmppChatMessageHandle handle = createMessage(chat);

   // Send the display notification
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDisplayedImpl_DEPRECATED, this, chat, handle, message, messageDisplayStatus));
   return handle;
}

int XmppChatManagerInterface::notifyMessageDisplayed(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus)
{
   // Send the display notification
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDisplayedImpl_DEPRECATED, this, chat, chatMessage, message, messageDisplayStatus));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageDisplayedImpl_DEPRECATED(XmppChatHandle chat, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageDisplayStatus messageDisplayStatus)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageDisplayed called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->notifyMessageDisplayed(context.info, origMessage);
}

int XmppChatManagerInterface::notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDeliveredImpl, this, chat, message));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageDeliveredImpl(XmppChatHandle chat, XmppChatMessageHandle message)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageDelivered called with invalid handle: " << chat);
      return;
   }

   context.impl->notifyMessageDelivered(context.info, message);
}

int XmppChatManagerInterface::notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message)
{
   mAccountIf->post(resip::resip_bind(&XmppChatManagerInterface::notifyMessageDisplayedImpl, this, chat, message));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageDisplayedImpl(XmppChatHandle chat, XmppChatMessageHandle message)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageDisplayed called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->notifyMessageDisplayed(context.info, message);
}

int XmppChatManagerInterface::notifyMessageRead(XmppChatHandle chat, XmppChatMessageHandle message)
{
   mAccountIf->post(resip::resip_bind(static_cast<void(XmppChatManagerInterface::*)(XmppChatHandle, XmppChatMessageHandle)>(&XmppChatManagerInterface::notifyMessageReadImpl), this, chat, message));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageReadImpl(XmppChatHandle chat, XmppChatMessageHandle message)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageRead called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->notifyMessageRead(context.info, message);
}

int XmppChatManagerInterface::notifyMessageRead(XmppChatHandle chat, const cpc::string& threadId, const cpc::string& messageId)
{
   mAccountIf->post(resip::resip_bind(static_cast<void(XmppChatManagerInterface::*)(XmppChatHandle, const cpc::string&, const cpc::string&)>(&XmppChatManagerInterface::notifyMessageReadImpl), this, chat, threadId, messageId));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageReadImpl(XmppChatHandle chat, const cpc::string& threadId, const cpc::string& messageId)
{
   InfoContext context;

   if (!getChatInfoContext(chat, context))
   {
      FIRE_ERROR("XmppChatManager::notifyMessageRead called with invalid handle: " << chat);
      return;
   }

   // Send the notification
   context.impl->notifyMessageRead(context.info, threadId, messageId);
}

int XmppChatManagerInterface::notifyMessageRead(XmppAccount::XmppAccountHandle account, const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId)
{
   mAccountIf->post(resip::resip_bind(static_cast<void(XmppChatManagerInterface::*)(XmppAccount::XmppAccountHandle, const cpc::string&, const cpc::string&, const cpc::string&)>(&XmppChatManagerInterface::notifyMessageReadImpl), this, account, peerJid, threadId, messageId));
   return kSuccess;
}

void XmppChatManagerInterface::notifyMessageReadImpl(XmppAccount::XmppAccountHandle account, const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId)
{
   std::shared_ptr<XmppChatManagerImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      FIRE_ERROR("XmppChatManager::notifyMessageRead called with invalid account handle: " << account);
      return;
   }

   // Send the notification
   impl->notifyMessageRead(peerJid, threadId, messageId);
}

XmppChat::XmppChatMessageHandle XmppChatManagerInterface::createMessage(XmppChatHandle chat)
{
   // Generate a new message ID
   XmppChatMessageHandle handle = XmppChatManagerImpl::sNextXmppChatMessageHandle++;
   return handle;
}

int XmppChatManagerInterface::createMessage(XmppChat::XmppChatMessageHandle chatMessage, XmppChatHandle chat)
{
   return kSuccess;
}

bool XmppChatManagerInterface::getChatInfoContext(XmppChatHandle handle, InfoContext& context) const
{
   for (ImplMap::const_iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      if (XmppChatInfo* info = it->second->getChatInfo(handle))
      {
         context.impl = it->second.get();
         context.info = info;
         return true;
      }
   }

   return false;
}

}

}

#endif
