#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_IMPL_H)
#define CPCAPI2_XMPP_ACCOUNT_IMPL_H

#include "cpcapi2defs.h"
#include "xmpp/XmppRosterTypes.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppAccountSettings.h"
#include "xmpp/XmppAccountHandler.h"
#include "XmppAccountInterface.h"
#include "XmppConnection.h"
#include "XmppStackLog.h"
#include "XmppAccountSyncHandler.h"

#include "phone/PhoneInterface.h"

#include <rutil/Fifo.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <client.h>
#include <connectiontcpclient.h>
#include <connectionlistener.h>
#include <disco.h>
#include <discohandler.h>
#include <privatexml.h>
#include <privatexmlhandler.h>

namespace CPCAPI2
{

namespace XmppPrivacy
{
   class XmppPrivacyImpl;
}

namespace XmppAccount
{

enum Restriction
{
   UserDisabledRestriction,
   NetworkRestriction,
};

class XmppAccountImpl;
class XmppAccountHandlerInternal;

struct XmppAccountObserver
{
   virtual void onWillConnect(XmppAccountImpl& account) = 0;
   virtual void onDidConnect(XmppAccountImpl& account) = 0;
   virtual void onWillDisconnect(XmppAccountImpl& account) = 0;
   virtual void onDidDisconnect(XmppAccountImpl& account) = 0;
   virtual void onDestroy(XmppAccountImpl& account) = 0;
   virtual void onStreamManagementAck(XmppAccountImpl& account) {}
};

struct XmppDiscoObserver
{
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) = 0;
   virtual void onXmppDiscoCompleted() = 0;
};

class XmppAccountImpl
   : public gloox::ConnectionListener
   , public gloox::DiscoHandler
   , public gloox::IqHandler
   , public gloox::PrivateXMLHandler
   , public resip::DeadlineTimerHandler
   , public XmppDiscoObserver
   , public std::enable_shared_from_this<XmppAccountImpl>
{

public:

   XmppAccountImpl(
      XmppAccountHandle h,
      const XmppAccountSettings& accountSettings,
      resip::Fifo<resip::ReadCallbackBase>* callbackFifo,
      const std::function<void(void)>& cbHook,
      PhoneInterface* phone,
      XmppAccountInterface& accountIf,
      const std::list<XmppAccountHandlerInternal*>& initialObservers);

   virtual ~XmppAccountImpl();

   void post(resip::ReadCallbackBase* cmd);
   void postCallback(resip::ReadCallbackBase* fp);

   void setHandler(XmppAccountHandler* handler);
   XmppAccountHandle getHandle() const { return mHandle; }
   const XmppAccountSettings& getSettings() const { return mSettings; }
   XmppAccountSettings& pendingSettings() { return mPendingSettings; }
   XmppAccountSettingsInternal& getSettingsInternal() { return mSettingsInternal; }

   void applySettings();
   void destroy(bool force = false);

   gloox::Client* getGlooxClient() { return mClient; }

   bool isEnabled() const;
   bool isConnected() const { return mClient != NULL; } // bliu: not necessarily means connected, more like active (could be connecting or connected)

   // use setPresence() instead of gloox::Client::setPresence() so extensions embedded with gloox::Presence can be taken care of
   void setPresence(XmppRoster::PresenceType presence, const cpc::string& msg, int priority);
   void updatePresence(XmppRoster::PresenceType presence, const cpc::string& msg, int priority);

   void publishPresence(XmppRoster::PresenceType presence, const cpc::string& msg, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText);
   void publishCannedPresence(XmppRoster::XmppCannedStatus status, const cpc::string& note);
   void blockIncomingPresence(bool block);

   void setHibernationState(bool active);

   void getEntityTime(const cpc::string& jid);

   void enableNotification(const cpc::string& node, const XmppDataForm& dataform);
   void disableNotification(const cpc::string& node);

   void getPrivateStorageData();
   void setPrivateStorageData(const cpc::vector<XmppStorageData>& data);

   void send(const cpc::string& xml);

   void fireError(const cpc::string& errorText);
   void fireXmppError(const XmppAccount::Error errorCode);

   void registerAccountObserver(XmppAccountObserver* observer);
   void unregisterAccountObserver(XmppAccountObserver* observer);

   void registerDiscoObserver(XmppDiscoObserver* observer);
   void unregisterDiscoObserver(XmppDiscoObserver* observer);

   bool isDiscoCompleted() const;

   bool isStreamManagementEnabled() const;

   XmppPrivacy::XmppPrivacyImpl* getXmppPrivacy() { return mPrivacy; }

   void addRestriction(Restriction restriction);
   void removeRestriction(Restriction restriction);
   std::set<NetworkTransport>& restrictedNetworks() { return mRestrictedNetworks; }
   const bool isRestrictedNetwork(const NetworkTransport currentNetworkTransport) const;
   const bool isRestricted() const { return !mRestrictions.empty(); }

   XmppAccountInterface& getAccountInterface() { return mAccountIf; }
   void addSdkObserver(XmppAccountHandlerInternal* handler);
   void removeSdkObserver(XmppAccountHandlerInternal* handler);

   void populateNameServer(XmppConnectionTcpClient::DnsSettings& dnsSettings) const;

   void simulateNetworkLoss();
   void setInactive(bool inactive);

   static std::atomic<XmppAccountHandle> sNextAccountHandle;

private:

   // gloox::ConnectionListener
   virtual void onConnect() OVERRIDE;
   virtual void onDisconnect( gloox::ConnectionError e ) OVERRIDE;
   virtual void onResourceBind( const std::string& resource ) OVERRIDE;
   virtual void onResourceBindError( const gloox::Error* error ) OVERRIDE;
   virtual void onSessionCreateError( const gloox::Error* error ) OVERRIDE;
   virtual bool onTLSConnect( const gloox::CertInfo& info ) OVERRIDE;
   virtual void onStreamEvent(gloox::StreamEvent event) OVERRIDE;

   // gloox::DiscoHandler
   virtual void handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context) OVERRIDE;
   virtual void handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context) OVERRIDE;
   virtual void handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context) OVERRIDE;
   virtual bool handleDiscoSet(const gloox::IQ& iq) OVERRIDE;

   // gloox::PrivateXMLHandler
   virtual void handlePrivateXML(const gloox::Tag* xml) OVERRIDE;
   virtual void handlePrivateXMLResult(const std::string& uid, gloox::PrivateXMLHandler::PrivateXMLResult pxResult) OVERRIDE;

   // gloox::IqHandler
   virtual bool handleIq(const gloox::IQ& iq) OVERRIDE;
   virtual void handleIqID(const gloox::IQ& iq, int context) OVERRIDE;

   // resip::DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   // XmppDiscoObserver
   virtual void onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info) OVERRIDE;
   virtual void onXmppDiscoCompleted() OVERRIDE;

private:

   void startConnectSequence();
   void startPostConnectSequence();
   void startDisconnectSequence(bool reconnect);
   void startPostDisconnectSequence(gloox::ConnectionError e, gloox::StreamError se, const std::string& streamErrorData);

   void performWillConnectTask();
   void performDidConnectTask();
   void performWillDisconnectTask();
   void performDidDisconnectTask();

   void fireAccountStatusChange(XmppAccountStatusChangedEvent::Status accountStatus, XmppAccount::Error errorCode = XmppAccount::Error_None);

   void startGlooxClient();
   void resumeGlooxClient();
   void cleanup();
   void connect();
   void disconnect(bool reconnect); // reconnection will be attempted after disconnection
   void reconnect(int milliseconds);
   void ping();
   void checkUnrequestedAck();

   void outputStats();

   template<typename TFn, typename TEvt>
   void fireEvent(const char* funcName, TFn func, const TEvt& evt)
   {
      for (std::list<XmppAccountHandlerInternal*>::const_iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *it, mHandle, evt);
         if (dynamic_cast<XmppAccountSyncHandler*>(*it) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      if (mAppHandler != reinterpret_cast<XmppAccountHandler*>(0xDEADBEFF))
      {
         resip::ReadCallbackBase* callback = makeFpCommandNew(funcName, func, mAppHandler, mHandle, evt);
         postCallback(callback);
      }
   }

private:

   gloox::Client* mClient;
   gloox::PrivateXML* mPrivateStorage;
   PhoneInterface* mPhone;
   XmppAccountInterface& mAccountIf;
   XmppAccountHandle mHandle;
   XmppAccountSettings mSettings;
   XmppAccountSettings mPendingSettings;
   XmppAccountSettingsInternal mSettingsInternal;
   XmppAccountHandler* mAppHandler;
   XmppStackLog* mStackLog;
   typedef std::vector<XmppAccountObserver*> XmppAccountObservers;
   XmppAccountObservers mAccountObservers;
   resip::Fifo<resip::ReadCallbackBase>* mCallbackFifo;
   std::function<void(void)> mCbHook;
   XmppPrivacy::XmppPrivacyImpl* mPrivacy;

   resip::DeadlineTimer<resip::MultiReactor> mUnrequestedAckTimer;
   resip::DeadlineTimer<resip::MultiReactor> mConnectTimer;
   resip::DeadlineTimer<resip::MultiReactor> mDiscoTimer;
   resip::DeadlineTimer<resip::MultiReactor> mPingTimer;

   long int mTotalStanzasReceived;
   typedef std::set<int> DiscoSet;
   DiscoSet mDiscoSet;

   typedef std::vector<XmppDiscoObserver*> DiscoObservers;
   DiscoObservers mDiscoObservers;

   std::set<Restriction> mRestrictions;
   std::set<NetworkTransport> mRestrictedNetworks;
   std::list<XmppAccountHandlerInternal*> mSdkObservers;
   XmppTLSConnectionInfo mTlsConnInfo;

   size_t mReconnectCount;

   std::set<EntityFeatureEvent::Feature> mServerFeatures;

   resip::Data mCurrentInterfaceIPAddress;

   std::string mResource;
   std::string mSeeOtherHost;

   XmppAccountStatusChangedEvent::Status mStatus;

   bool mResume;
   gloox::Client::StreamManagementInfo* mStreamManagementInfo;
   resip::DeadlineTimer<resip::MultiReactor> mResumeTimer; // to cancel pending resumption

};

} // XmppAccount

} // CPCAPI2

#endif // CPCAPI2_XMPP_ACCOUNT_IMPL_H
