#include "CpcXep.h"
#include "CpcXepNotificationTigase.h"

const std::string CpcXepNotificationTigase::XMLNS_IGNORE_UNKNOWN   = "tigase:push:filter:ignore-unknown:0";
const std::string CpcXepNotificationTigase::XMLNS_FILTER_MUTED     = "tigase:push:filter:muted:0";
const std::string CpcXepNotificationTigase::XMLNS_FILTER_GROUPCHAT = "tigase:push:filter:groupchat:0";
const std::string CpcXepNotificationTigase::XMLNS_AWAY             = "tigase:push:away:0";
const std::string CpcXepNotificationTigase::XMLNS_PRIORITY         = "tigase:push:priority:0";

CpcXepNotificationTigase::CpcXepNotificationTigase(bool enable, const gloox::JID& jid, const std::string& node, bool ignoreUnknownSender, bool sendNotificationsWhileAway)
   : CpcXepNotification(enable, jid, node, gloox::DataForm(NULL))
   , mEnable(enable)
   , mIgnoreUnknownSender(ignoreUnknownSender)
   , mSendNotificationsWhileAway(sendNotificationsWhileAway)
{
}

CpcXepNotificationTigase::~CpcXepNotificationTigase()
{
}

const std::string& CpcXepNotificationTigase::filterString() const
{
   return CpcXepNotification::filterString();
}

gloox::StanzaExtension* CpcXepNotificationTigase::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepNotificationTigase(*this);
}

gloox::Tag* CpcXepNotificationTigase::tag() const
{
   gloox::Tag* _tag = CpcXepNotification::tag();
   
   if (mEnable)
   {
      if (mIgnoreUnknownSender)
      {
         gloox::Tag* u = new gloox::Tag("ignore-unknown", "xmlns", XMLNS_IGNORE_UNKNOWN);
         _tag->addChild(u);
      }

      if (mSendNotificationsWhileAway)
      {
         _tag->addAttribute("away", "true");
      }

      if (mMutedContacts.size() > 0)
      {
         gloox::Tag* m = new gloox::Tag("muted", "xmlns", XMLNS_FILTER_MUTED);
         
         for (cpc::vector<cpc::string>::const_iterator csi = mMutedContacts.begin(); csi != mMutedContacts.end(); ++csi)
         {
            gloox::Tag* mi = new gloox::Tag("item", "jid", csi->c_str());
            m->addChild(mi);
         }
         _tag->addChild(m);
      }

      if (mGroupChatFilter.size() > 0)
      {
         gloox::Tag* g = new gloox::Tag("groupchat", "xmlns", XMLNS_FILTER_GROUPCHAT);
         for (cpc::vector<CPCAPI2::XmppPush::GroupChatFilter>::const_iterator gfi = mGroupChatFilter.begin(); gfi != mGroupChatFilter.end(); ++gfi)
         {
            gloox::Tag* gr = new gloox::Tag("room", "jid", gfi->jid.c_str());
            if (gfi->rule == CPCAPI2::XmppPush::GroupChatFilterRule::Mentioned)
            {
               gr->addAttribute("allow", "mentioned");
               gr->addAttribute("nick", gfi->nick.c_str());
            }
            else if (gfi->rule == CPCAPI2::XmppPush::GroupChatFilterRule::Always)
            {
               gr->addAttribute("allow", "always");
            }
            else if (gfi->rule == CPCAPI2::XmppPush::GroupChatFilterRule::Never)
            {
               gr->addAttribute("allow", "never");
            }
            g->addChild(gr);
         }
         _tag->addChild(g);
      }
   }

   return _tag;
}

gloox::StanzaExtension* CpcXepNotificationTigase::clone() const
{
   return new CpcXepNotificationTigase(*this);
}
