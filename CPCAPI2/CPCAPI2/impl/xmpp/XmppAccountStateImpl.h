#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_STATE_IMPL_H)
#define CPCAPI2_XMPP_ACCOUNT_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "XmppAccountHandlerInternal.h"
#include "xmpp/XmppAccountState.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{
namespace XmppAccount
{
class XmppAccountInterface;

class XmppAccountStateImpl
   : public XmppAccountStateManager
   , public XmppAccountHandlerInternal
   , public PhoneModule
{
public:
   XmppAccountStateImpl(XmppAccountInterface* acctIf);
   virtual ~XmppAccountStateImpl();

   // XmppAccountStateManager
   virtual int getStateAllAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountState>& accountState) OVERRIDE;

   // PhoneModule
   virtual void Release() OVERRIDE;

   // Inherited via XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;
   //virtual int onAccountEnabled(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountEnabledEvent& args) OVERRIDE;

   // Inherited via XmppAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE;
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const EntityFeatureEvent& args) OVERRIDE;

private:
   XmppAccountInterface* mAcctIf;
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, XmppAccountState> AccountStateMap;
   AccountStateMap mStateMap;
};

}
}

#endif // CPCAPI2_XMPP_ACCOUNT_STATE_IMPL_H
