#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_MESSAGE_INFO_H__)
#define __CPCAPI2_XMPP_CHAT_MESSAGE_INFO_H__

#include "cpcapi2defs.h"
#include "xmpp/XmppChatHandler.h"

#include <map>

namespace CPCAPI2
{
namespace XmppChat
{

struct XmppChatMessageInfo
{
   XmppChatMessageHandle handle;
   MessageType type;
   bool outgoing; // true, if it's an outgoing message, false if it's incoming
   cpc::string datetimeString;
   XmppChatMessageHandle origHandle; // For IMDN notifications only: handle of the message being reported upon
   IsComposingMessageState isComposingState; // For isComposing notifications only
};

typedef std::map<XmppChatMessageHandle, XmppChatMessageInfo*> XmppChatMessageInfoMap;

}
}

#endif // __CPCAPI2_XMPP_CHAT_MESSAGE_INFO_H__
