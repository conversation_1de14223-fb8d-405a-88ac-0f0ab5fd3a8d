#ifndef CPC_XEP_NOTIFICATIONTIGASE_H__
#define CPC_XEP_NOTIFICATIONTIGASE_H__

#include "CpcXepNotification.h"
#include "xmpp/XmppPushTypes.h"

#include "stanzaextension.h"
#include "tag.h"
#include "jid.h"
#include "dataform.h"

/**
* @brief This is an implementation of @xep{0357} (Push Notification) with Tigase custom extensions.
*/
class CpcXepNotificationTigase : public CpcXepNotification
{
public:
   CpcXepNotificationTigase(bool enable, const gloox::JID& jid, const std::string& node, bool ignoreUnknownSender, bool sendNotificationsWhileAway);
   virtual ~CpcXepNotificationTigase();
   void setMutedContacts(const cpc::vector<cpc::string>& muted) { mMutedContacts = muted; }
   void setGroupChatFilter(const cpc::vector<CPCAPI2::XmppPush::GroupChatFilter>& filter) { mGroupChatFilter = filter; }

   static const std::string XMLNS_IGNORE_UNKNOWN;
   static const std::string XMLNS_FILTER_MUTED;
   static const std::string XMLNS_FILTER_GROUPCHAT;
   static const std::string XMLNS_AWAY;
   static const std::string XMLNS_PRIORITY;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

private:
   bool mEnable;
   bool mIgnoreUnknownSender;
   bool mSendNotificationsWhileAway;
   cpc::vector<cpc::string> mMutedContacts;
   cpc::vector<CPCAPI2::XmppPush::GroupChatFilter> mGroupChatFilter;
};

#endif // CPC_XEP_NOTIFICATIONTIGASE_H__
