#include "XmppCommon.h"

namespace CPCAPI2
{
namespace XmppCommon
{

void convert(const XmppAccount::XmppDataForm& dataform, gloox::DataForm& gloox_dataform)
{
   for (cpc::vector<XmppAccount::XmppDataFormField>::const_iterator it = dataform.fields.begin(), end = dataform.fields.end(); it != end; ++it)
   {
      const XmppAccount::XmppDataFormField& _field = *it;

      gloox::DataFormField* field = new gloox::DataFormField(static_cast<gloox::DataFormField::FieldType>(_field.type));
      field->setName(it->name.c_str());

      for (cpc::vector<cpc::string>::const_iterator vit = _field.values.begin(), vend = _field.values.end(); vit != vend; ++vit)
      {
         field->addValue(vit->c_str());
      }

      gloox_dataform.addField(field);
   }
}

void convert(const gloox::DataForm& gloox_dataform, XmppAccount::XmppDataForm& dataform)
{
   for (gloox::DataFormFieldContainer::FieldList::const_iterator it = gloox_dataform.fields().begin(), end = gloox_dataform.fields().end(); it != end; ++it)
   {
      const gloox::DataFormField* _field = *it;

      if (_field == NULL) continue;

      XmppAccount::XmppDataFormField field;
      field.type = _field->type();
      field.name = _field->name().c_str();
      field.required = _field->required();
      field.label = _field->label().c_str();

      for (gloox::StringList::const_iterator vit = _field->values().begin(), vend = _field->values().end(); vit != vend; ++vit)
      {
         field.values.push_back(vit->c_str());
      }

      dataform.fields.push_back(field);
   }

   dataform.type = gloox_dataform.type();
   dataform.title = gloox_dataform.title().c_str();

   for (gloox::StringList::const_iterator it = gloox_dataform.instructions().begin(), end = gloox_dataform.instructions().end(); it != end; ++it)
   {
      dataform.instructions.push_back(it->c_str());
   }
}

}
}
