// bliu: TODO list
// raiseMessageEvent()

// bliu: notes
// handleMUCRoomCreation returns true instead of using gloox::InstantMUCRoom

#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

#include "XmppMultiUserChatManagerImpl.h"
#include "XmppPrivacyImpl.h"
#include "XmppTimeHelper.h"
#include "XmppCommon.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "util/ReactorHelpers.h"
#include "cpcapi2utils.h"

#include "iscomposing/IsComposingHelper.h"

#include <message.h>
#include <mucmessagesession.h>
#include <messageeventfilter.h>
#include <messageeventhandler.h>
#include <chatstate.h>
#include <chatstatefilter.h>
#include <chatstatehandler.h>
#include <messagefilter.h>
#include <xhtmlim.h>
#include <error.h>
#include <util.h>
#include <receipt.h>

#include <boost/algorithm/string.hpp> // boost::replace

#include "rutil/Random.hxx"

#include <sstream>
#include <ctime>

#include "CpcXep.h"
#include "CpcXepIMCommand.h"
#include "CpcXepChatMarker.h"
#include "CpcXepMessageCorrection.h"
#include "CpcXepMessageOrigin.h"
#include "CpcXepMessageReaction.h"
#include "CpcXepMessageRetract.h"
#include "CpcXepMessageStyling.h"
#include "CpcXepMessageStanza.cpp"
#include "CpcXepMessageProcessingHint.h"

#ifdef ANDROID
# if defined(__LP64__)
#  include <time.h>
# else
#  include <time64.h>
# endif
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT

#define FIRE_ERROR(handle, msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   fireError(handle, ss.str().c_str());\
}

// this code is duplicated in XmppChatManagerImpl.cpp
static const std::string _generate_guid()
{
   // previous code used std::rand to generate a 32 character length string.
   // we've switched to crypto random to avoid collisions when running auto tests
   // in parallel in multiple docker containers on linux.

   const unsigned int charsToReturn = 32;
   // hex() turns every byte in a resip::Data into 2 characters
   const unsigned int randomBytes = charsToReturn / 2;
   return resip::Random::getCryptoRandom(randomBytes).hex().c_str();
}

template<typename Event>
static void setEventTimeStamp(Event& evt, const gloox::Message& msg)
{
   evt.timestamp = 0;
   evt.millisecond = 0;

   if (const gloox::DelayedDelivery* dd = msg.when())
   {
      CPCAPI2::TimeHelper::to_time(dd->stamp(), evt.timestamp, evt.millisecond);
   }

   if (evt.timestamp == 0)
   {
      uint64_t ms = CPCAPI2::TimeHelper::millisSinceEpoch();
      evt.timestamp = (time_t)(ms / 1000);
      evt.millisecond = ms % 1000;

      evt.isDelayedDelivery = false;
   }
   else
   {
      evt.isDelayedDelivery = true;
   }
}

namespace CPCAPI2
{
namespace XmppMultiUserChat
{

// begin: taken from XmppChatManagerImpl.cpp
class GlooxXHtmlParser : private gloox::TagHandler
{
public:
   GlooxXHtmlParser(const std::string& bodyContent)
      : mHtmlTag(NULL)
   {
      mParser = new gloox::Parser(this);
      std::string htmlContent = "<html xmlns='" + gloox::XMLNS_XHTML_IM + "'><body xmlns='http://www.w3.org/1999/xhtml'>";
      htmlContent += bodyContent;
      htmlContent += "</body></html>";
      boost::ireplace_all(htmlContent, "<br>", "<br/>"); // TODO: properly convert HTML to XHTML
      boost::replace_all(htmlContent, "&nbsp;", "&#160;"); // DCM: gloox does not support HTML named entities

      if (mParser->feed(htmlContent) != -1)
      {
         delete mHtmlTag;
         mHtmlTag = NULL;
      }
   }

   virtual ~GlooxXHtmlParser()
   {
      delete mHtmlTag;
      delete mParser;
   }

   virtual void handleTag(gloox::Tag* tag)
   {
      delete mHtmlTag; // it might be a clone from a previous tag

      mHtmlTag = tag == NULL ? NULL : tag->clone();
   }

   gloox::Tag* htmlTag()
   {
      return mHtmlTag;
   }

private:
   gloox::Parser* mParser;
   gloox::Tag* mHtmlTag;
};

class GlooxXHtmlReader
{
public:
   GlooxXHtmlReader(const gloox::Stanza& stanza)
      : mWasFound(false)
   {
      const gloox::XHtmlIM* xi = stanza.findExtension<gloox::XHtmlIM>(gloox::ExtXHtmlIM);
      if (xi)
      {
         gloox::Tag* bodyTag = xi->xhtml()->findChild("body", gloox::XMLNS, "http://www.w3.org/1999/xhtml");
         if (bodyTag)
         {
            std::string bodyText = bodyTag->xml();
            std::string::size_type b = bodyText.find_first_of('>');
            std::string::size_type e = bodyText.find_last_of('<');
            if (b != std::string::npos && e != std::string::npos && b < e)
            {
               // take out the 'body' start and end tags
               mBodyContent = bodyText.substr(b+1, e-b-1);
               mWasFound = true;
            }
         }
      }
   }

   bool found() const
   {
      return mWasFound;
   }

   const std::string& bodyContent() const
   {
      return mBodyContent;
   }

private:
   bool mWasFound;
   std::string mBodyContent;
};

class XmppMessageFilter : public gloox::MessageFilter
{
public:
   XmppMessageFilter(gloox::MessageSession* session, XmppMultiUserChatManagerImpl& impl, XmppMultiUserChatHandle handle)
      : gloox::MessageFilter(session)
      , mImpl(impl)
      , mHandle(handle)
      , mOriginID("")
      , mReplacesMessageID("")
   {}

   // gloox::MessageFilter
   virtual void decorate(gloox::Message& msg) OVERRIDE
   {
      if (mIMCommand)
      {
         msg.removeExtension(gloox::ExtMessageEvent); // assume MessageEvent extension comes prior to IMCommand
         msg.addExtension(mIMCommand.release());
         return;
      }

      if (msg.hasBody())
      {
         if (mImpl.deliveryReceiptsEnabled())
         {
            msg.addExtension(new gloox::Receipt(gloox::Receipt::Request));
         }
         if (mImpl.readReceiptsEnabled())
         {
            msg.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Markable));
         }
         if (!mReplacesMessageID.empty())
         {
            msg.addExtension(new CpcXepMessageCorrection(mReplacesMessageID));
            mReplacesMessageID.clear();
         }
      }

      if (!mOriginID.empty())
      {
         msg.addExtension(new CpcXepMessageOrigin(mOriginID));
         mOriginID.clear();
      }

      if (!mXHtmlContentToSend.empty())
      {
         GlooxXHtmlParser xhtmlParser (mXHtmlContentToSend);
         if (gloox::Tag* tag = xhtmlParser.htmlTag())
         {
            gloox::XHtmlIM* xhtmlIm = new gloox::XHtmlIM(tag);
            msg.addExtension(xhtmlIm);
         }
         else
         {
            DebugLog(<< "XmppMessageFilter::decorate(): malformed XHTML: " << mXHtmlContentToSend);

            mImpl.fireError(mHandle, ("invalid xhtml content: " + mXHtmlContentToSend).c_str());
         }

         mXHtmlContentToSend.clear();
      }

      if (!mReactionTarget.empty())
      {
         msg.addExtension(new CpcXepMessageReaction(mReactionTarget, mReactionsToSend));
         msg.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
         mReactionTarget.clear();
      }

      if (!mRetractTarget.empty())
      {
         msg.addExtension(new CpcXepMessageRetract(mRetractTarget));
         msg.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
         mRetractTarget.clear();
      }
   }

   virtual void filter(gloox::Message& msg) OVERRIDE {}

private:
   friend class XmppMultiUserChatManagerImpl;

   XmppMultiUserChatManagerImpl& mImpl;
   XmppMultiUserChatHandle mHandle;
   std::string mReplacesMessageID;
   // message
   std::string mOriginID;
   std::string mXHtmlContentToSend;
   // reaction
   std::string mReactionTarget;
   std::vector<std::string> mReactionsToSend;
   // message retraction
   std::string mRetractTarget;

   std::unique_ptr<CpcXepIMCommand> mIMCommand;
};
// end: taken from XmppChatManagerImpl.cpp

class XmppMultiUserChatStateFilter : public gloox::ChatStateFilter
{
public:
   XmppMultiUserChatStateFilter(gloox::MUCMessageSession* parent) : gloox::ChatStateFilter(parent) {}

   void setChatState(gloox::ChatStateType state)
   {
      if ((!m_enableChatStates || state == m_lastSent || state == gloox::ChatStateInvalid) && state != gloox::ChatStateComposing) //.JC. fogbugz 37159 "is typing" is not shown on IM window for XMPP account.
         return;

      gloox::Message m(gloox::Message::Groupchat, m_parent->target()); // bliu: override for type=Groupchat
      m.addExtension(new gloox::ChatState(state));

      m_lastSent = state;

      send(m);
   }
};

struct XmppMultiUserChatInfo
   : public IsComposing::IsComposingInfo
   , public gloox::ChatStateHandler
{
   XmppMultiUserChatInfo(XmppMultiUserChatManagerImpl& mgr, XmppMultiUserChatHandle handle)
      : mMgr(mgr)
      , mHandle(handle)
      , mInstantRoom(false)
      , mCreateIfNotExisting(true)
      //, mGlooxMessageEventFilter(NULL)
      , mGlooxChatStateFilter(NULL)
      , mMessageFilter(NULL)
      , mRoom(NULL)
      , mParticipantsReady(false)
      , mReadyNotified(false)
      //, mIsCreatedBySelf(false)
      , mIsNewRoom(false)
      , mFeatures(0)
      , mInvitationInfo(NULL)
      , mInactiveTimer(NULL)
      , mIsActive(false)
      , mIsInConfiguration(false)
      , mForceRoomStateChangeCallback(false)
   {}

   virtual ~XmppMultiUserChatInfo()
   {
      delete mInactiveTimer;

      //delete mGlooxMessageEventFilter;
      delete mGlooxChatStateFilter;
      delete mMessageFilter;
      delete mRoom;
      delete mInvitationInfo;

      for (auto item : mChatStates)
      {
         delete item.second;
      }
   }

   // gloox::ChatStateHandler
   virtual void handleChatState(const gloox::JID& from, gloox::ChatStateType state) OVERRIDE
   {
      auto it = mChatStates.find(from);

      if (it == mChatStates.end())
      {
         auto insertor = mChatStates.insert(std::make_pair(from, new ChatStatInfo(from, mHandle)));
         assert(insertor.second);

         it = insertor.first;
         mMgr.initialize(it->second);
      }

      mMgr.onChatState(it->second, from, state);
   }

   XmppMultiUserChatManagerImpl& mMgr;
   const XmppMultiUserChatHandle mHandle;
   bool mInstantRoom;
   bool mCreateIfNotExisting;

   typedef std::map<std::string, ParticipantState> ParticipantMap;
   ParticipantMap mParticipants;
   ParticipantState mSelfState;

   //gloox::MessageEventFilter* mGlooxMessageEventFilter;
   //gloox::MessageEventHandler* mGlooxMessageEventHandler;
   XmppMultiUserChatStateFilter* mGlooxChatStateFilter;
   XmppMessageFilter* mMessageFilter;
   gloox::MUCRoom* mRoom;
   std::string mNickname;
   cpc::vector<XmppMultiUserChatHistoryItem> mHistoryToAdd;
   bool mParticipantsReady;
   bool mReadyNotified;
   //bool mIsCreatedBySelf; // bliu: TODO
   bool mIsNewRoom;
   std::string mSubject;
   int mFeatures;

   XmppMultiUserChatRoomState mRoomState;
   // this owner list is so we can more easily perform equality checks
   // when updateRoomState(..) is called -- since the cpc::vector owner
   // list in XmppMultiUserChatRoomState does not have an equality operator
   // available.
   gloox::StringList mOwners;
   bool mForceRoomStateChangeCallback;

   struct InvitationInfo
   {
      gloox::JID room;
      gloox::JID invitor;
      std::string password;
   };

   InvitationInfo* mInvitationInfo;

   struct ChatStatInfo : IsComposing::IsComposingInfo
   {
      ChatStatInfo(const gloox::JID& jid, XmppMultiUserChatHandle handle) : jid(jid), handle(handle) {}
      gloox::JID jid;
      XmppMultiUserChatHandle handle;
   };

   std::map<gloox::JID, ChatStatInfo*> mChatStates;

   // bliu: keep track of local active state
   resip::DeadlineTimer<resip::MultiReactor>* mInactiveTimer;
   bool mIsActive;

   bool mIsInConfiguration;
};

static void _dataform_to_configurations(const gloox::DataForm& form, XmppMultiUserChatConfigurations& configurations)
{
   for (gloox::DataFormFieldContainer::FieldList::const_iterator it = form.fields().begin(), end = form.fields().end(); it != end; ++it)
   {
      const gloox::DataFormField* field = *it;

      XmppMultiUserChatConfigurationItem item;
      item.type = field->type();
      item.name = field->name().c_str();
      item.required = field->required();
      item.label = field->label().c_str();

      for (gloox::StringList::const_iterator vit = field->values().begin(), vend = field->values().end(); vit != vend; ++vit)
      {
         item.values.push_back(vit->c_str());
      }

      configurations.items.push_back(item);
   }

   configurations.type = form.type();
   configurations.title = form.title().c_str();

   for (gloox::StringList::const_iterator it = form.instructions().begin(), end = form.instructions().end(); it != end; ++it)
   {
      configurations.instructions.push_back(it->c_str());
   }
}

static void _configurations_to_dataform(const XmppMultiUserChatConfigurations& configurations, gloox::DataForm& form)
{
   for (cpc::vector<XmppMultiUserChatConfigurationItem>::const_iterator it = configurations.items.begin(), end = configurations.items.end(); it != end; ++it)
   {
      gloox::DataFormField* field = new gloox::DataFormField(static_cast<gloox::DataFormField::FieldType>(it->type));
      field->setName(it->name.c_str());

      for (cpc::vector<cpc::string>::const_iterator vit = it->values.begin(), vend = it->values.end(); vit != vend; ++vit)
      {
         field->addValue(vit->c_str());
      }

      form.addField(field);
   }
}

#ifdef _WIN32
std::atomic<XmppMultiUserChatHandle> XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatHandle = 1;
std::atomic<XmppMultiUserChatMessageHandle> XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle = 1;
#else
std::atomic<XmppMultiUserChatHandle> XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatHandle = ATOMIC_VAR_INIT(1);
std::atomic<XmppMultiUserChatMessageHandle> XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle = ATOMIC_VAR_INIT(1);
#endif

XmppMultiUserChatManagerImpl::XmppMultiUserChatManagerImpl(Phone* phone, XmppAccount::XmppAccountImpl& account, XmppMultiUserChatManagerInterface& intf)
   : IsComposingManager(dynamic_cast<PhoneInterface*>(phone), &account.getAccountInterface().getReactor())
   , gloox::MUCInvitationHandler(NULL)
   , mAccount(account)
   , mInterface(intf)
   , mAppHandler(NULL)
   , mIMCommandHandler(NULL)
   , mInactiveTimerHandler(*this)
   , mBookmarkStorage(NULL)
   , mDeliveryReceiptsEnabled(false)
   , mReadReceiptsEnabled(false)
{
   mAccount.registerAccountObserver(this);
   mAccount.registerDiscoObserver(this);
}

XmppMultiUserChatManagerImpl::~XmppMultiUserChatManagerImpl()
{
   cleanup();

   mAccount.unregisterAccountObserver(this);
   mAccount.unregisterDiscoObserver(this);
}

void XmppMultiUserChatManagerImpl::cleanup()
{
   //mAccount.getGlooxClient()->removeMUCInvitationHandler(); // bliu: TODO

   while (!mInfoMap.empty())
   {
      InfoMap::iterator it = mInfoMap.begin();
      leave(it->second, "");
   }

   mDiscoService.setJID(gloox::EmptyString);

   if (mBookmarkStorage != NULL)
   {
      mBookmarkStorage->removeBookmarkHandler();
      delete mBookmarkStorage;
      mBookmarkStorage = NULL;
   }

   for (auto& i : mMessageAckMap)
   {
      SendMessageFailureEvent evt;
      evt.message = i.second.second.message;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageFailure), i.second.first, evt);
   }

   mMessageAckMap.clear();
}

void XmppMultiUserChatManagerImpl::fireError(XmppMultiUserChatHandle handle, const cpc::string& errorText)
{
   if (mAppHandler == NULL)
   {
      mAccount.fireError("XmppMultiUserChat: " + errorText);
      return;
   }

   MultiUserChatErrorEvent evt;
   evt.type = OtherError;
   evt.error = errorText;
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatError), handle, evt);
}

XmppMultiUserChatInfo* XmppMultiUserChatManagerImpl::getMultiUserChatInfo(XmppMultiUserChatHandle handle) const
{
   if (handle == 0) return NULL;

   for (InfoMap::const_iterator it = mInfoMap.begin(), end = mInfoMap.end(); it != end; ++it)
   {
      if (it->second->mHandle == handle) return it->second;
   }

   return NULL;
}

XmppMultiUserChatInfo* XmppMultiUserChatManagerImpl::getMultiUserChatInfoForGlooxRoom(gloox::MUCRoom* room) const
{
   if (room == NULL)
   {
      DebugLog(<< "gloox::MUCRoom is NULL");
      return NULL;
   }

   for (InfoMap::const_iterator it = mInfoMap.begin(), end = mInfoMap.end(); it != end; ++it)
   {
      if (it->second->mRoom == room) return it->second;
   }

   DebugLog(<< "invalid gloox::MUCRoom:" << std::hex << room);
   return NULL;
}

void XmppMultiUserChatManagerImpl::addSdkObserver(XmppMultiUserChatHandlerInternal* handler)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), handler) != mSdkObservers.end())
   {
      StackLog(<< "XmppMultiUserChatManagerImpl::addSdkObserver(): XmppMultiUserChatHandlerInternal handler: " << handler << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppMultiUserChatManagerImpl::addSdkObserver(): XmppMultiUserChatHandlerInternal handler: " << handler);
   mSdkObservers.push_back(handler);
}

void XmppMultiUserChatManagerImpl::removeSdkObserver(XmppMultiUserChatHandlerInternal* handler)
{
   std::list<XmppMultiUserChatHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), handler);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppMultiUserChatManagerImpl::removeSdkObserver(): XmppMultiUserChatHandlerInternal handler: " << handler << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppMultiUserChatManagerImpl::setHandler(XmppMultiUserChatHandler* handler)
{
   mAppHandler = handler;
}

void XmppMultiUserChatManagerImpl::setHandler(XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler)
{
   mIMCommandHandler = handler;
}

void XmppMultiUserChatManagerImpl::getRoomList()
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppMultiUserChatManagerImpl::getRoomList, shared_from_this()), 200);
      return;
   }

   if (!mDiscoService)
   {
      FIRE_ERROR(0, "Skip getRoomList() since conference service is not available (perhaps wait for onServiceAvailability)");
      return;
   }

   if (mAccount.getGlooxClient())
      mAccount.getGlooxClient()->disco()->getDiscoItems(mDiscoService, "", this, 0);
}

void XmppMultiUserChatManagerImpl::setRoomBookmarks(const cpc::vector<RoomBookmark>& bookmarks)
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppMultiUserChatManagerImpl::setRoomBookmarks, shared_from_this(), bookmarks), 200);
      return;
   }

   if (!mDiscoService)
   {
      FIRE_ERROR(0, "Skip setRoomBookmarks() since conference service is not available (perhaps wait for onServiceAvailability)");
      return;
   }

   if (mAccount.isConnected())
   {
      gloox::ConferenceList list;

      for (cpc::vector<RoomBookmark>::const_iterator it = bookmarks.begin(), end = bookmarks.end(); it != end; ++it)
      {
         gloox::ConferenceListItem item;
         item.name = it->name.c_str();
         item.jid = it->jid.c_str();
         item.nick = it->nickname.c_str();
         item.password = it->password.c_str();
         item.autojoin = it->autojoin;
         list.push_back(item);
      }

      mBookmarkStorage->storeBookmarks(gloox::BookmarkList(), list);
   }
}

void XmppMultiUserChatManagerImpl::getRoomBookmarks()
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppMultiUserChatManagerImpl::getRoomBookmarks, shared_from_this()), 200);
      return;
   }

   if (!mDiscoService)
   {
      FIRE_ERROR(0, "Skip getRoomBookmarks() since conference service is not available (perhaps wait for onServiceAvailability)");
      return;
   }

   if (mAccount.isConnected())
   {
      mBookmarkStorage->requestBookmarks();
   }
}

void XmppMultiUserChatManagerImpl::create(XmppMultiUserChatHandle handle, const cpc::string& room)
{
   InfoLog(<< "XmppMultiUserChatManager: create: " << handle << ", room: " << room);

   XmppMultiUserChatInfo* info = new XmppMultiUserChatInfo(*this, handle);

   if (!mInfoMap.insert(std::make_pair(handle, info)).second)
   {
      FIRE_ERROR(handle, "XmppMultiUserChatManager::create with an existing handle: " << handle);
      return;
   }

   gloox::JID roomJid = mDiscoService;

   std::string _room = room.empty() ? _generate_guid() : room.c_str();

   if (_room.find('@') == std::string::npos)
   {
      roomJid.setUsername(_room);
   }
   else
   {
      roomJid.setJID(_room);
   }

   if (!roomJid)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::create with invalid room name: " << room);
      return;
   }

   info->mRoom = new gloox::MUCRoom(mAccount.getGlooxClient(), roomJid, this, this);

   XmppMultiUserChatCreatedResultEvent createEvt;
   createEvt.muc = handle;
   createEvt.account = mAccount.getHandle();
   createEvt.room = room;
   fireEvent(cpcFunc(XmppMultiUserChatHandlerInternal::onCreateMultiUserChatResult), createEvt);

   struct NewRoomEvent evt;
   evt.roomjid = roomJid.bare().c_str();
   evt.hAccount = mAccount.getHandle();
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onNewRoomHandle), info->mHandle, evt );
}

void XmppMultiUserChatManagerImpl::DEPRECATE_create(XmppMultiUserChatHandle handle, bool instantRoom)
{
   InfoLog(<< "XmppMultiUserChatManager::DEPRECATE_create(): muc: " << handle);

   XmppMultiUserChatInfo* info = new XmppMultiUserChatInfo(*this, handle);

   if (!mInfoMap.insert(std::make_pair(handle, info)).second)
   {
      FIRE_ERROR(handle, "XmppMultiUserChatManager::create with an existing handle: " << handle);
      return;
   }

   info->mInstantRoom = instantRoom;

   XmppMultiUserChatCreatedResultEvent createEvt;
   createEvt.muc = handle;
   createEvt.account = mAccount.getHandle();
   createEvt.room = "";
   fireEvent(cpcFunc(XmppMultiUserChatHandlerInternal::onCreateMultiUserChatResult), createEvt);
}

void XmppMultiUserChatManagerImpl::destroyRoom(XmppMultiUserChatInfo* info, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password)
{
   InfoLog(<< "XmppMultiUserChatManager: destroyRoom: " << info->mHandle);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::destroyRoom with an invalid handle: " << info->mHandle);
      return;
   }

   gloox::JID alternateJid;

   if (!alternate.empty())
   {
      std::string _alternate = alternate.c_str();

      if (_alternate.find('@') == std::string::npos)
      {
         alternateJid = mDiscoService;
         alternateJid.setUsername(_alternate);
      }
      else
      {
         alternateJid.setJID(_alternate);
      }
   }

   info->mRoom->destroy(reason.c_str(), alternateJid, password.c_str());
}

void XmppMultiUserChatManagerImpl::getRoomInfo(XmppMultiUserChatInfo* info)
{
   InfoLog(<< "XmppMultiUserChatManager: getRoomInfo: " << info->mHandle);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::getRoomInfo with an invalid handle: " << info->mHandle);
      return;
   }

   info->mForceRoomStateChangeCallback = true;

   info->mRoom->getRoomInfo();
}

void XmppMultiUserChatManagerImpl::accept(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   InfoLog(<< "XmppMultiUserChatManager: accept: " << info->mHandle);

   if (info->mInvitationInfo == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::accept with a non-invited handle: " << info->mHandle);
      return;
   }

   if (info->mRoom == NULL)
   {
      gloox::JID nick = info->mInvitationInfo->room;

      if (!nick.setResource(nickname.c_str()))
      {
         FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::accept (handle=" << info->mHandle << ") with an invalid nickname: " << nick);
         return;
      }

      DEPRECATE_join_(info, nick, info->mInvitationInfo->password.c_str(), historyRequester, historyToAdd);
   }
   else
   {
      join_(info, nickname, info->mInvitationInfo->password.c_str(), historyRequester, historyToAdd);
   }

   struct NewRoomEvent evt2;
   evt2.roomjid =  info->mInvitationInfo->room.bare().c_str();
   evt2.hAccount = mAccount.getHandle();
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onNewRoomHandle), info->mHandle, evt2 );

   // bliu: TODO
   delete info->mInvitationInfo;
   info->mInvitationInfo = NULL;
}

void XmppMultiUserChatManagerImpl::decline(XmppMultiUserChatInfo* info, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: decline: " << info->mHandle << " reason " << reason);

   if (info->mRoom != NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::decline with a joined handle: " << info->mHandle);
      return;
   }

   if (info->mInvitationInfo == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::decline with a non-invited handle: " << info->mHandle);
      return;
   }

   gloox::Message* msg = gloox::MUCRoom::declineInvitation(info->mInvitationInfo->room, info->mInvitationInfo->invitor, reason.c_str());
   if (msg != NULL && mAccount.getGlooxClient() != NULL) mAccount.getGlooxClient()->send(*msg);
   delete msg;

   delete info->mInvitationInfo;
   info->mInvitationInfo = NULL;
}

void XmppMultiUserChatManagerImpl::join_(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "the room is not created yet");
      return;
   }

   if (!info->mNickname.empty())
   {
      FIRE_ERROR(info->mHandle, "the current user is already in room: " << info->mNickname);
      mAccount.post(resip::resip_bind(&XmppMultiUserChatManagerImpl::leave, shared_from_this(), info, ""));
      return;
   }

   IsComposingManager::initialize(info);

   info->mMessageFilter = new XmppMessageFilter(NULL, *this, info->mHandle); // bliu: TODO MessageSession is passed as NULL, need to fix afterwards?
   info->mRoom->registerMessageFilter(info->mMessageFilter); // must be called before join() to be added to the filter list

   info->mNickname = nickname.c_str();

   info->mRoom->setNick(nickname.c_str());
   info->mRoom->setPassword(password.c_str());

   size_t colon = historyRequester.find(":");

   if (colon != cpc::string::npos)
   {
      cpc::string type = historyRequester.substr(0, colon);
      cpc::string value = historyRequester.size() > colon ? historyRequester.substr(colon + 1, historyRequester.size() - colon) : "";

      if (type == "since")
      {
         time_t t = cpc::to_int(value);

         // bliu: TODO thread-safety
         tm* tt = std::gmtime(&t);
         char buffer[64];
         memset(buffer, 0, sizeof(buffer));
         /*size_t size = */std::strftime(buffer, sizeof(buffer) - 1, "%Y-%m-%dT%H:%M:%SZ", tt);

         info->mRoom->setRequestHistory(buffer);
      }
      else if (type == "message")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistoryMaxStanzas);
      }
      else if (type == "char")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistoryMaxChars);
      }
      else if (type == "seconds")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistorySeconds);
      }
   }

   info->mHistoryToAdd = historyToAdd;

   info->mRoom->join();

   //info->mGlooxMessageEventFilter = new gloox::MessageEventFilter(info->mRoom->getMUCMessageSession());
   info->mGlooxChatStateFilter = new XmppMultiUserChatStateFilter(info->mRoom->getMUCMessageSession());  // must be called after join() for a valid message session
   info->mGlooxChatStateFilter->registerChatStateHandler(info);
}

void XmppMultiUserChatManagerImpl::DEPRECATE_join_(XmppMultiUserChatInfo* info, const gloox::JID& nick, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   assert(nick);

   for (InfoMap::iterator it = mInfoMap.begin(), end = mInfoMap.end(); it != end; ++it)
   {
      XmppMultiUserChatInfo* _info = it->second;

      if (_info->mRoom != NULL && _info->mRoom->name() == nick.username() && _info->mRoom->service() == nick.server())
      {
         FIRE_ERROR(info->mHandle, "the current user is already in room: " << nick.bare());
         mAccount.post(resip::resip_bind(&XmppMultiUserChatManagerImpl::leave, shared_from_this(), info, ""));
         return;
      }
   }

   info->mRoom = new gloox::MUCRoom(mAccount.getGlooxClient(), nick, this, this);

   IsComposingManager::initialize(info);

   info->mMessageFilter = new XmppMessageFilter(NULL, *this, info->mHandle); // bliu: TODO MessageSession is passed as NULL, need to fix afterwards?
   info->mRoom->registerMessageFilter(info->mMessageFilter); // must be called before join() to be added to the filter list

   info->mNickname = nick.resource().c_str();

   info->mRoom->setPassword(password.c_str());

   size_t colon = historyRequester.find(":");

   if (colon != cpc::string::npos)
   {
      cpc::string type = historyRequester.substr(0, colon);
      cpc::string value = historyRequester.size() > colon ? historyRequester.substr(colon + 1, historyRequester.size() - colon) : "";

      if (type == "since")
      {
         time_t t = cpc::to_int(value);

         // bliu: TODO thread-safety
         tm* tt = std::gmtime(&t);
         char buffer[64];
         memset(buffer, 0, sizeof(buffer));
         /*size_t size = */std::strftime(buffer, sizeof(buffer) - 1, "%Y-%m-%dT%H:%M:%SZ", tt);

         info->mRoom->setRequestHistory(buffer);
      }
      else if (type == "message")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistoryMaxStanzas);
      }
      else if (type == "char")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistoryMaxChars);
      }
      else if (type == "seconds")
      {
         info->mRoom->setRequestHistory(atoi(value.c_str()), gloox::MUCRoom::HistorySeconds);
      }
   }

   info->mHistoryToAdd = historyToAdd;

   info->mRoom->join();

   //info->mGlooxMessageEventFilter = new gloox::MessageEventFilter(info->mRoom->getMUCMessageSession());
   info->mGlooxChatStateFilter = new XmppMultiUserChatStateFilter(info->mRoom->getMUCMessageSession());  // must be called after join() for a valid message session
   info->mGlooxChatStateFilter->registerChatStateHandler(info);
}

void XmppMultiUserChatManagerImpl::join(XmppMultiUserChatInfo* info, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppMultiUserChatManagerImpl::join, shared_from_this(), info, config, nickname, password, historyRequester, historyToAdd), 200);
      return;
   }

   if (!mDiscoService)
   {
      FIRE_ERROR(info->mHandle, "Skip join() since conference service is not available (perhaps wait for onServiceAvailability)");
      return;
   }

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::join with an invalid handle: " << info->mHandle);
      return;
   }

   info->mInstantRoom = config.isInstant;
   info->mCreateIfNotExisting = config.createIfNotExisting;

   join_(info, nickname, password, historyRequester, historyToAdd);
}

void XmppMultiUserChatManagerImpl::DEPRECATE_join(XmppMultiUserChatInfo* info, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   if (!mAccount.isDiscoCompleted())
   {
      // re-issue the call until disco is completed
      mAccount.getAccountInterface().getReactor().postMS(resip::resip_bind(&XmppMultiUserChatManagerImpl::DEPRECATE_join, shared_from_this(), info, room, nickname, password, historyRequester, historyToAdd), 200);
      return;
   }

   if (!mDiscoService)
   {
      FIRE_ERROR(info->mHandle, "Skip join() since conference service is not available (perhaps wait for onServiceAvailability)");
      return;
   }

   if (info->mRoom != NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::join with a joined handle: " << info->mHandle);
      return;
   }

   gloox::JID roomJid = mDiscoService;

   std::string _room = room.empty() ? _generate_guid() : room.c_str();

   if (_room.find('@') == std::string::npos)
   {
      roomJid.setUsername(_room);
   }
   else
   {
      roomJid.setJID(_room);
   }

   InfoLog(<< "XmppMultiUserChatManager: join: " << _room);

   if (!roomJid)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::join with invalid room name: " << room << "/" << nickname);
      return;
   }

   gloox::JID nick = roomJid;

   if (!nick.setResource(nickname.c_str()))
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::join (handle=" << info->mHandle << ") with an invalid nickname: " << nick);
      return;
   }

   DEPRECATE_join_(info, nick, password, historyRequester, historyToAdd);
}

void XmppMultiUserChatManagerImpl::leave(XmppMultiUserChatInfo* info, const cpc::string& reason)
{
   if (info->mRoom != NULL)
   {
      InfoLog(<< "XmppMultiUserChatManager: leave room: " <<info->mRoom->name() << " reason: "  << reason);
      
      // must remove chat state filter before leave() otherwise it will be deleted inside leave() which causes double deallocation
      if (gloox::MUCMessageSession* session = info->mRoom->getMUCMessageSession())
      {
         session->removeMessageFilter(info->mGlooxChatStateFilter);
      }
      else
      {
         // if the session has gone away then the deallocation of this item has already taken place, so don't delete it a second time
         info->mGlooxChatStateFilter = NULL;
      }

      info->mRoom->removeMUCRoomHandler();
      info->mRoom->leave(reason.c_str());

      LocalUserLeftEvent evt;
      evt.reason = reason;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onLocalUserLeft), info->mHandle, evt);
   } else
   {
      InfoLog(<< "XmppMultiUserChatManager: leave room reason: " << reason);
   }

   mInfoMap.erase(info->mHandle);
   delete info;
}

void XmppMultiUserChatManagerImpl::sendMessage(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& plain, const cpc::string& html)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::sendMessage(): handle=" << message);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendMessage not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendMessage room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   if (plain.empty() && html.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageFailure), info->mHandle, evt);
      return;
   }

   // Rely on content decoration to send the ID as an Origin-ID
   std::string messageId = _generate_guid();
   info->mMessageFilter->mOriginID = messageId;

   if (!html.empty())
   {
      // Rely on content decoration
      info->mMessageFilter->mXHtmlContentToSend = html.c_str();
   }

   IsComposingManager::setMessageSent(info);
   cpc::string replaces = info->mMessageFilter->mReplacesMessageID.c_str();

   // no message ID is generated here, we use the Origin-ID
   info->mRoom->send(plain.c_str(), messageId);

   InfoLog(<< "XmppMultiUserChatManagerImpl::sendMessage(): handle=" << message << ", messageId=" << messageId);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();
   evt.replaces = replaces;

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(info->mHandle, evt)));
   }
   else
   {
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageSuccess), info->mHandle, evt);
   }
}

void XmppMultiUserChatManagerImpl::sendReaction(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::sendReaction(): handle=" << message);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendReaction not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendReaction room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   if (target.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageFailure), info->mHandle, evt);
      return;
   }

   std::vector<std::string> _reactions;
   cpc::vector<cpc::string> temp = reactions;   // this avoids a dangling pointer below
   for (auto it = temp.begin(); it != temp.end(); ++it)
      _reactions.push_back(it->c_str());

   // Send the reaction
   info->mMessageFilter->mReactionTarget = target;
   info->mMessageFilter->mReactionsToSend = _reactions;

   IsComposingManager::setMessageSent(info);

   std::string messageId = info->mRoom->send("", _generate_guid());

   InfoLog(<< "XmppMultiUserChatManagerImpl::sendReaction(): handle=" << message << ", messageId=" << messageId);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(info->mHandle, evt)));
   }
   else
   {
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageSuccess), info->mHandle, evt);
   }
}

void XmppMultiUserChatManagerImpl::sendMessageRetraction(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& target)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::sendMessageRetraction(): handle=" << message);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendMessageRetraction not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendMessageRetraction room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   if (target.empty())
   {
      SendMessageFailureEvent evt;
      evt.message = message;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageFailure), info->mHandle, evt);
      return;
   }

   // Send the retraction
   info->mMessageFilter->mRetractTarget = target;

   IsComposingManager::setMessageSent(info);

   std::string messageId = info->mRoom->send("", _generate_guid());

   InfoLog(<< "XmppMultiUserChatManagerImpl::sendMessageRetraction(): handle=" << message << ", messageId=" << messageId);

   SendMessageSuccessEvent evt;
   evt.message = message;
   evt.messageId = messageId.c_str();\

   if (mAccount.isStreamManagementEnabled())
   {
      mAccount.getGlooxClient()->reqStreamManagement();

      mMessageAckMap.insert(std::make_pair(messageId, std::make_pair(info->mHandle, evt)));
   }
   else
   {
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageSuccess), info->mHandle, evt);
   }
}

void XmppMultiUserChatManagerImpl::setIsComposingMessage(XmppMultiUserChatInfo* info, int refreshInterval, int idleInterval)
{
   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setIsComposingMessage not yet joined a room handle=" << info->mHandle);
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setIsComposingMessage room is not yet ready (perhaps wait for onMultiUserChatReady) handle=" << info->mHandle);
      return;
   }

   InfoLog(<< "XmppMultiUserChatManager: setIsComposingMessage handle=" << info->mHandle << " refresh=" << refreshInterval << " idle=" << idleInterval << " state=" << typeid(*info->composerState).name());

   tm niltime = IsComposing::IsComposingHelper::getCurrentDateTime();
   IsComposingManager::setIsComposingMessage(info, resip::Mime(), niltime, refreshInterval, idleInterval);
}

void XmppMultiUserChatManagerImpl::publishPresence(XmppMultiUserChatInfo* info, XmppRoster::PresenceType presence, const cpc::string& note)
{
   InfoLog(<< "XmppMultiUserChatManager: publishPresence: " << presence << " with note " << note);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::publishPresence not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::publishPresence room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->setPresence(static_cast<gloox::Presence::PresenceType>(presence), note.c_str());
}

void XmppMultiUserChatManagerImpl::changeNickname(XmppMultiUserChatInfo* info, const cpc::string& nickname)
{
   InfoLog(<< "XmppMultiUserChatManager: changeNickname: " << nickname);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeNickname not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeNickname room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->setNick(nickname.c_str());
}

void XmppMultiUserChatManagerImpl::changeSubject(XmppMultiUserChatInfo* info, const cpc::string& subject)
{
   InfoLog(<< "XmppMultiUserChatManager: changeSubject: " << subject);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeSubject not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeSubject room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->setSubject(subject.c_str());
}

void XmppMultiUserChatManagerImpl::invite(XmppMultiUserChatInfo* info, const cpc::string& jid, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: invite: " << jid);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::invite not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::invite room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   gloox::JID _jid;

   if (!_jid.setJID(jid.c_str()))
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::invite with invalid jid: " << jid);
      return;
   }

   info->mRoom->invite(_jid, reason.c_str());
}

void XmppMultiUserChatManagerImpl::kick(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: kick: " << nickname << " for " << reason);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::kick not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::kick room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->kick(nickname.c_str(), reason.c_str());
}

void XmppMultiUserChatManagerImpl::ban(XmppMultiUserChatInfo* info, const cpc::string& nickname, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: ban: " << nickname << " for " << reason);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::ban not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::ban room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->ban(nickname.c_str(), reason.c_str());
}

void XmppMultiUserChatManagerImpl::changeAffiliation(XmppMultiUserChatInfo* info, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: changeAffiliation: " << nickname << " to " << affiliation << " for " << reason);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeAffiliation not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeAffiliation room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->setAffiliation(nickname.c_str(), static_cast<gloox::MUCRoomAffiliation>(affiliation), reason.c_str());
}

void XmppMultiUserChatManagerImpl::changeJidAffiliation(XmppMultiUserChatInfo* info, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: changeJidAffiliation: " << jid << " to " << affiliation << " for " << reason);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeAffiliation not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeAffiliation room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   // per XEP-0045, "Affiliations are granted, revoked, and maintained based on the user's bare JID, not the nick as with roles."
   info->mRoom->setAffiliation(gloox::JID(jid.c_str()), static_cast<gloox::MUCRoomAffiliation>(affiliation), reason.c_str());
}

void XmppMultiUserChatManagerImpl::changeRole(XmppMultiUserChatInfo* info, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason)
{
   InfoLog(<< "XmppMultiUserChatManager: changeRole: " << nickname << " to " << role << " for " << reason);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeRole not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::changeRole room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mRoom->setRole(nickname.c_str(), static_cast<gloox::MUCRoomRole>(role), reason.c_str());
}

void XmppMultiUserChatManagerImpl::requestConfigurations(XmppMultiUserChatInfo* info)
{
   InfoLog(<< "XmppMultiUserChatManager: setConfigurations");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::requestConfigurations not yet joined a room");
      return;
   }

   info->mRoom->requestRoomConfig();
}

void XmppMultiUserChatManagerImpl::setConfigurations(XmppMultiUserChatInfo* info, const XmppAccount::XmppDataForm& dataform)
{
   InfoLog(<< "XmppMultiUserChatManager: setConfigurations");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setConfigurations not yet joined a room");
      return;
   }

   gloox::DataForm* form = new gloox::DataForm(gloox::TypeSubmit);

   XmppCommon::convert(dataform, *form);

   info->mRoom->setRoomConfig(form);

   // bliu: getRoomInfo() will be called automatically upon 104 status from server
   //info->mRoom->getRoomInfo();
}

void XmppMultiUserChatManagerImpl::setConfigurations(XmppMultiUserChatInfo* info, const XmppMultiUserChatConfigurations& configurations)
{
   InfoLog(<< "XmppMultiUserChatManager: setConfigurations");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setConfigurations not yet joined a room");
      return;
   }

   gloox::DataForm* form = new gloox::DataForm(gloox::TypeSubmit);

   _configurations_to_dataform(configurations, *form);

   info->mRoom->setRoomConfig(form);

   // bliu: getRoomInfo() will be called automatically upon 104 status from server
   //info->mRoom->getRoomInfo();
}

void XmppMultiUserChatManagerImpl::requestList(XmppMultiUserChatInfo* info, XmppMultiUserChatListType type)
{
   InfoLog(<< "XmppMultiUserChatManager: requestList");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::requestList not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::requestList room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   gloox::MUCOperation op = gloox::InvalidOperation;

   switch(type)
   {
      case VoiceList:
         op = gloox::RequestVoiceList;
         break;
      case BanList:
         op = gloox::RequestBanList;
         break;
      case MemberList:
         op = gloox::RequestMemberList;
         break;
      case ModeratorList:
         op = gloox::RequestModeratorList;
         break;
      case OwnerList:
         op = gloox::RequestOwnerList;
         break;
      case AdminList:
         op = gloox::RequestAdminList;
         break;
      default:
         return;
   }

   info->mRoom->requestList(op);
}

void XmppMultiUserChatManagerImpl::setList(XmppMultiUserChatInfo* info, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items)
{
   InfoLog(<< "XmppMultiUserChatManager: setList");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setList not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::setList room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   gloox::MUCOperation op = gloox::InvalidOperation;

   switch(type)
   {
      case VoiceList:
         op = gloox::StoreVoiceList;
         break;
      case BanList:
         op = gloox::StoreBanList;
         break;
      case MemberList:
         op = gloox::StoreMemberList;
         break;
      case ModeratorList:
         op = gloox::StoreModeratorList;
         break;
      case OwnerList:
         op = gloox::StoreOwnerList;
         break;
      case AdminList:
         op = gloox::StoreAdminList;
         break;
      default:
         return;
   }

   gloox::MUCListItemList list;

   for(cpc::vector<XmppMultiUserChatConfigurationsListItem>::const_iterator it = items.begin(); it != items.end(); it++)
   {
      gloox::MUCListItem item = gloox::MUCListItem(gloox::JID(it->jid.c_str()), static_cast<gloox::MUCRoomRole>(it->role), static_cast<gloox::MUCRoomAffiliation>(it->affiliation), it->nick.c_str());
      list.push_back(item);
   }

   info->mRoom->storeList(list, op);
}

// IsComposingManager
void XmppMultiUserChatManagerImpl::sendIsComposingMessageNotification(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, int refreshInterval, const tm& lastActive)
{
   XmppMultiUserChatInfo* _info = static_cast<XmppMultiUserChatInfo*>(info);

   if (!validateChatStatus(_info)) return;

   if (state == IsComposing::IsComposingMessageState_Active)
   {
      if (!_info->mIsActive) transitionToActiveState(_info);

      //_info->mGlooxMessageEventFilter->raiseMessageEvent(gloox::MessageEventComposing);
      _info->mGlooxChatStateFilter->setChatState(gloox::ChatStateComposing);

      if (_info->mInactiveTimer != NULL) _info->mInactiveTimer->cancel();
   }
   else if (state == IsComposing::IsComposingMessageState_Idle)
   {
      _info->mGlooxChatStateFilter->setChatState(gloox::ChatStatePaused);

      startInactiveTimer(_info);
   }
}

void XmppMultiUserChatManagerImpl::sendIMCommand(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, int type, const cpc::string& payload)
{
   InfoLog(<< "XmppMultiUserChatManager: sendIMCommand");

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendIMCommand not yet joined a room");
      return;
   }

   if (!info->mReadyNotified)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::sendIMCommand room is not yet ready (perhaps wait for onMultiUserChatReady)");
      return;
   }

   info->mMessageFilter->mIMCommand.reset(new CpcXepIMCommand(type, payload.c_str()));
   info->mRoom->send("");

   assert(!info->mMessageFilter->mIMCommand);

   XmppIMCommand::MultiUserChatIMCommandSentEvent evt;
   evt.message = message;
   // bliu: TODO add sdk observer
   mAccount.postCallback(makeFpCommand(XmppIMCommand::XmppMultiUserChatIMCommandHandler::onIMCommandSent, mIMCommandHandler, info->mHandle, evt));
}

void XmppMultiUserChatManagerImpl::onIsComposingMessage(IsComposing::IsComposingInfo* info, IsComposing::IsComposingMessageState state, const resip::Mime& contentType, const tm& lastActive)
{
   XmppMultiUserChatInfo::ChatStatInfo* _info = static_cast<XmppMultiUserChatInfo::ChatStatInfo*>(info);

   InfoLog(<< "XmppMultiUserChatManager: onIsComposingMessage handle=" << _info->handle << " nick=" << _info->jid.resource() << " typing=" << state << " (" << (state == IsComposing::IsComposingMessageState_Active ? "active" : "idle") << ") state=" << typeid(*info->receiverState).name());

   ParticipantChatStateEvent evt;
   evt.nickname = _info->jid.resource().c_str();
   evt.jid = _info->jid.full().c_str();
   evt.state = static_cast<XmppChat::IsComposingMessageState>(state);
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onParticipantChatStateReceived), _info->handle, evt);
}

// XmppAccountObserver
void XmppMultiUserChatManagerImpl::onWillConnect(XmppAccount::XmppAccountImpl& account)
{
   getMUCSettings(mAccount.getSettings());

   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_CHAT_STATES);
   account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_DELAY);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageOrigin::XMLNS_MESSAGE_ORIGIN);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageCorrection::XMLNS_MESSAGE_CORRECTION);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageReaction::XMLNS_MESSAGE_REACTION);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageRetract::XMLNS_MESSAGE_RETRACT);
   account.getGlooxClient()->disco()->addFeature(CpcXepMessageStanza::XMLNS_MESSAGE_STANZA);
   if (mReadReceiptsEnabled)
   {
      account.getGlooxClient()->disco()->addFeature(CpcXepChatMarker::XMLNS_CHAT_MARKER);
   }
   if (mDeliveryReceiptsEnabled)
   {
      account.getGlooxClient()->disco()->addFeature(gloox::XMLNS_RECEIPTS);
   }

   account.getGlooxClient()->registerStanzaExtension(new gloox::XHtmlIM());
   account.getGlooxClient()->registerStanzaExtension(new gloox::ChatState(0));
   account.getGlooxClient()->registerStanzaExtension(new gloox::DelayedDelivery());
   account.getGlooxClient()->registerStanzaExtension(new gloox::MUCRoom::MUC());
   account.getGlooxClient()->registerStanzaExtension(new gloox::MUCRoom::MUCUser());
   account.getGlooxClient()->registerStanzaExtension(new CpcXepIMCommand());
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageOrigin(""));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageCorrection(0));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageReaction("",std::vector<std::string>()));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageRetract(""));
   account.getGlooxClient()->registerStanzaExtension(new CpcXepMessageStanza(""));
   if (mReadReceiptsEnabled)
   {
      account.getGlooxClient()->registerStanzaExtension(new CpcXepChatMarker());
   }
   if (mDeliveryReceiptsEnabled)
   {
      account.getGlooxClient()->registerStanzaExtension(new gloox::Receipt(0));
   }
   account.getGlooxClient()->registerMUCInvitationHandler(this);
}

void XmppMultiUserChatManagerImpl::onDidConnect(XmppAccount::XmppAccountImpl& account)
{
   mBookmarkStorage = new gloox::BookmarkStorage(mAccount.getGlooxClient());
   mBookmarkStorage->registerBookmarkHandler(this);
}

void XmppMultiUserChatManagerImpl::onWillDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppMultiUserChatManagerImpl::onDidDisconnect(XmppAccount::XmppAccountImpl& account)
{
   cleanup();
}

void XmppMultiUserChatManagerImpl::onDestroy(XmppAccount::XmppAccountImpl& account)
{
   mInterface.destroyImpl(mAccount.getHandle());
}

void XmppMultiUserChatManagerImpl::onStreamManagementAck(XmppAccount::XmppAccountImpl& account)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::onStreamManagementAck()");
   gloox::TagList tags = mAccount.getGlooxClient()->sendQueue();

   auto copy = mMessageAckMap;
   for (auto& i : copy)
   {
      bool acked = true;

      for (auto& t : tags)
      {
         if (t == NULL) continue;
         if (t->name() != "message") continue;
         if (t->findAttribute("id") == i.first)
         {
            acked = false;
            break;
         }
      }

      if (acked)
      {
         fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageSuccess), i.second.first, i.second.second);
         mMessageAckMap.erase(i.first);
      }
   }

   gloox::util::clearList(tags);
}

// XmppDiscoObserver
void XmppMultiUserChatManagerImpl::onXmppDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info)
{
   if (!info.hasFeature(gloox::XMLNS_MUC)) return;

   if (mDiscoService) return; // take the first result only

   for (gloox::Disco::IdentityList::const_iterator it = info.identities().begin(), end = info.identities().end(); it != end; ++it)
   {
      if ((*it)->category() == "conference" && (*it)->type() == "text")
      {
         mDiscoService = from;

         InfoLog(<< "Discovered conference service " << mDiscoService.full());

         ServiceAvailabilityEvent evt;
         evt.available = true;
         evt.service = from.bare().c_str();
         fireEvent(cpcFunc(XmppMultiUserChatHandler::onServiceAvailability), mAccount.getHandle(), evt);

         // query for allowable traffic according to http://xmpp.org/extensions/xep-0045.html#impl-service-traffic
         //mAccount.getGlooxClient()->disco()->getDiscoInfo(mDiscoService, "http://jabber.org/protocol/muc#traffic", this, 0); // bliu: TODO

         gloox::PrivacyItem rule (gloox::PrivacyItem::TypeJid, gloox::PrivacyItem::ActionAllow, gloox::PrivacyItem::PacketAll, from.bare());
         mAccount.getXmppPrivacy()->addPrivacyRule(rule);

         return;
      }
   }
}

void XmppMultiUserChatManagerImpl::onXmppDiscoCompleted()
{
   if (!mDiscoService)
   {
      InfoLog(<< "No conference service has been discovered");

      ServiceAvailabilityEvent evt;
      evt.available = false;
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onServiceAvailability), mAccount.getHandle(), evt);
   }
}

// gloox::MUCRoomHandler
void XmppMultiUserChatManagerImpl::handleMUCParticipantPresence(gloox::MUCRoom *room, const gloox::MUCRoomParticipant& glooxParticipant, const gloox::Presence& presenceStanza)
{
   //InfoLog(<< "XMPP: handleMUCParticipantPresence: " << sua::GlooxScopedTag(presenceStanza.tag()).xml());

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   if (glooxParticipant.nick == NULL) return;

   std::string nickname = glooxParticipant.nick->resource();

   // Flag UserSelf is optional for XMPP, so need to check against nickname as well
   bool self = (glooxParticipant.flags & gloox::UserSelf) || info->mNickname == nickname;

   if (self) // self is not included in info->mParticipants
   {
      info->mParticipantsReady = true;

      updateParticipantState(info, glooxParticipant, presenceStanza, info->mSelfState);

      if (info->mSelfState.isBanned || info->mSelfState.isKicked || info->mSelfState.isRemoved || (info->mSelfState.presence == XmppRoster::PresenceType_Unavailable && !(glooxParticipant.flags & gloox::UserNickChanged)))
      {
         leave(info, glooxParticipant.reason.c_str()); // info is no longer valid after leave()
         return;
      }

      checkForRoomReady(info);

      if (!info->mIsInConfiguration) info->mRoom->getRoomInfo();

      return;
   }

   if (info->mParticipants.empty())
   {
      // Good time to ask room for the room info
      info->mRoom->getRoomInfo();
   }

   XmppMultiUserChatInfo::ParticipantMap::iterator it = info->mParticipants.find(nickname);

   if (it == info->mParticipants.end()) // create participant
   {
      ParticipantState state;
      state.nickname = nickname.c_str();
      // DRL: In a semi-anonymous room the JID may not be provided.
      if (glooxParticipant.jid != NULL) state.jid = glooxParticipant.jid->bare().c_str();
      state.presence = static_cast<XmppRoster::PresenceType>(presenceStanza.presence());
      state.affiliation = static_cast<XmppMultiUserChatAffiliation>(glooxParticipant.affiliation);
      state.role = static_cast<XmppMultiUserChatRole>(glooxParticipant.role);
      state.isBanned = glooxParticipant.flags & gloox::UserBanned;
      state.isKicked = glooxParticipant.flags & gloox::UserKicked;
      state.isRemoved = glooxParticipant.flags & gloox::UserAffiliationChanged;

      // bliu: don't add invalid participant upon presence update
      if (state.isBanned || state.isKicked || state.isRemoved || info->mSelfState.presence == XmppRoster::PresenceType_Unavailable) return;

      info->mParticipants.insert(std::make_pair(nickname, state));

      ParticipantAddedEvent evt;
      evt.nickname = nickname.c_str();
      evt.state = state;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onParticipantAdded), info->mHandle, evt);
   }
   else
   {
      updateParticipantState(info, glooxParticipant, presenceStanza, it->second);

      if (presenceStanza.subtype() == gloox::Presence::Unavailable)
      {
         // Offline means that the user left the room or was forced to
         ParticipantRemovedEvent evt;

         evt.nickname = nickname.c_str();
         if (evt.nickname.empty()) evt.nickname = it->first.c_str();

         if (glooxParticipant.jid != NULL) evt.jid = glooxParticipant.jid->bare().c_str();
         if (presenceStanza.error() != NULL) evt.reason = presenceStanza.error()->text().c_str(); // bliu: TODO

         fireEvent(cpcFunc(XmppMultiUserChatHandler::onParticipantRemoved), info->mHandle, evt);

         info->mParticipants.erase(nickname);
      }
   }
}

void XmppMultiUserChatManagerImpl::handleMUCMessage(gloox::MUCRoom* room, const gloox::Message& messageStanza, bool priv)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage(): from=" << messageStanza.from() << " messageId=" << messageStanza.id());

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   info->mGlooxChatStateFilter->filter(const_cast<gloox::Message&>(messageStanza));

   if (const CpcXepIMCommand* imcmd = messageStanza.findExtension<CpcXepIMCommand>(EXepIMCommand))
   {
      XmppIMCommand::MultiUserChatIMCommandReceivedEvent evt;
      evt.remote = messageStanza.from().bare().c_str(); // bliu: bare jid might not present in case of anonymous room
      evt.nickname = messageStanza.from().resource().c_str();
      evt.type = imcmd->type();
      evt.payload = imcmd->payload().c_str();
      evt.timestamp = 0;
      evt.millisecond = 0;

      if (const gloox::DelayedDelivery* dd = messageStanza.when())
      {
         TimeHelper::to_time(dd->stamp(), evt.timestamp, evt.millisecond);
      }

      if (evt.timestamp == 0)
      {
         uint64_t ms = TimeHelper::millisSinceEpoch();
         evt.timestamp = (time_t)(ms / 1000);
         evt.millisecond = ms % 1000;

         evt.isDelayedDelivery = false;
      }
      else
      {
         evt.isDelayedDelivery = true;
      }

      // bliu: TODO add sdk observer
      mAccount.postCallback(makeFpCommand(XmppIMCommand::XmppMultiUserChatIMCommandHandler::onIMCommandReceived, mIMCommandHandler, info->mHandle, evt));
      return;
   }

   MessageAckMap::iterator itAck = mMessageAckMap.find(messageStanza.id());

   if (itAck != mMessageAckMap.end())
   {
      InfoLog(<< "onSendMessageSuccess triggered by incoming message: message=" << itAck->second.second.message << ", id=" << itAck->first);
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onSendMessageSuccess), itAck->second.first, itAck->second.second);
      mMessageAckMap.erase(itAck);
   }

   if (mDeliveryReceiptsEnabled)
   {
      //InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: mDeliveryReceiptsEnabled");
      if (const gloox::Receipt* r = messageStanza.findExtension<gloox::Receipt>(gloox::ExtReceipt))
      {
         gloox::Receipt::ReceiptType type = r->rcpt();
         if (type == gloox::Receipt::Request)
         {
            InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: delivery request received for message id " << messageStanza.id());
            // do NOT send delivery receipts for own messages sent
            if (isMyStanza(messageStanza.from(), info->mRoom))
            {
               InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: skip sending delivery receipt to " << messageStanza.from().full());
            }
            else
            {
               notifyMessageDelivered(info, messageStanza.thread(), messageStanza.id());
            }
         }
         else if (type == gloox::Receipt::Received)
         {
            if (isMyStanza(messageStanza.from(), info->mRoom))
            {
               InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: skip onMessageDelivered event received from " << messageStanza.from().full());
            }
            else
            {
               InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: delivery confirmation received for message id " << r->id());
               MessageDeliveredEvent evt;
               //evt.message = info->mOutgoingMessages.find(r->id());
               //evt.messageDeliveryStatus = MessageDeliveryStatus_Delivered;
               evt.messageId = r->id().c_str();
               //evt.threadId = messageStanza.thread().c_str();
               evt.from = messageStanza.from().full().c_str();

               setEventTimeStamp(evt, messageStanza);
               fireEvent(cpcFunc(XmppMultiUserChatHandler::onMessageDelivered), info->mHandle, evt);
            }
         }
      }
   }

   if (mReadReceiptsEnabled)
   {
      //InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: mReadReceiptsEnabled");
      if (const CpcXepChatMarker* c = messageStanza.findExtension<CpcXepChatMarker>(EXepChatMarker))
      {
         if (!c->isValid())
         {
            WarningLog(<< "invalid Chat Marker");
         }
         else
         {
            if (c->type() == CpcXepChatMarker::ChatMarkerType_Displayed)
            {
               if (isMyStanza(messageStanza.from(), info->mRoom))
               {
                  InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: skip onMessageRead event received from " << messageStanza.from().full());
               }
               else
               {
                  MessageReadEvent evt;
                  evt.messageId = c->id().c_str();
                  //evt.threadId = messageStanza.thread().c_str();
                  evt.from = messageStanza.from().full().c_str();

                  setEventTimeStamp(evt, messageStanza);

                  fireEvent(cpcFunc(XmppMultiUserChatHandler::onMessageRead), info->mHandle, evt);
               }
            }
            else if (c->type() == CpcXepChatMarker::ChatMarkerType_Markable)
            {
               InfoLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: marker request received for message id " << messageStanza.id());
            }
         }
      }
   }

   cpc::string originId;
   if (const CpcXepMessageOrigin* mr = messageStanza.findExtension<CpcXepMessageOrigin>(EXepMessageOrigin))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid origin tag");
      }
      else
      {
         originId = mr->originID().c_str();
      }
   }

   cpc::string stanzaId;
   if (const CpcXepMessageStanza* mr = messageStanza.findExtension<CpcXepMessageStanza>(EXepMessageStanza))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid stanza tag");
      }
      else
      {
         stanzaId = mr->stanzaID().c_str();
      }
   }

   cpc::string reactionTarget;
   cpc::vector<cpc::string> reactions;
   if (const CpcXepMessageReaction* mr = messageStanza.findExtension<CpcXepMessageReaction>(EXepMessageReaction))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid reaction tag");
      }
      else
      {
         reactionTarget = mr->targetId().c_str();
         std::vector<std::string> temp = mr->reactions();   // this avoids a dangling pointer below
         for (auto it = temp.begin(); it != temp.end(); ++it)
            reactions.push_back(it->c_str());
      }
   }

   cpc::string retractTarget;
   if (const CpcXepMessageRetract* mr = messageStanza.findExtension<CpcXepMessageRetract>(EXepMessageRetract))
   {
      if (!mr->isValid())
      {
         WarningLog(<< "Invalid message retraction tag");
      }
      else
      {
         retractTarget = mr->targetId().c_str();
      }
   }

   cpc::string replaces;
   if (const CpcXepMessageCorrection* mc = messageStanza.findExtension<CpcXepMessageCorrection>(EXepMessageCorrection))
   {
      if (mc->isValid())
      {
         replaces = mc->id().c_str();
      }
      else
      {
         WarningLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage: no id in message correction tag");
      }
   }

   cpc::string jid;
   XmppMultiUserChatInfo::ParticipantMap::iterator it = info->mParticipants.find(messageStanza.from().resource().c_str());
   if (it != info->mParticipants.end())
   {
      jid = it->second.jid;
   }

   cpc::string plain = messageStanza.body().c_str();
   cpc::string html;
   GlooxXHtmlReader xHtmlReader (messageStanza);
   if (xHtmlReader.found())
   {
      html = xHtmlReader.bodyContent().c_str();
   }

   if (plain.empty() && html.empty() && reactionTarget.empty() && retractTarget.empty()) 
      return; // bliu: moved this check from MUCRoom::handleMessage to here

   uint64_t timestamp = 0;
   uint16_t millisecond = 0;
   if (const gloox::DelayedDelivery* dd = messageStanza.when())
   {
      TimeHelper::to_time(dd->stamp(), timestamp, millisecond);

      if (jid.empty())
      {
         // jid can be a room's jid, if room is semi anonymous
         if(dd->from().username() == room->name() && dd->from().server() == room->service())
         {
            XmppMultiUserChatInfo::ParticipantMap::iterator it = info->mParticipants.find(dd->from().resource().c_str());

            if (it != info->mParticipants.end())
            {
               jid = it->second.jid.c_str();
            }
         }
         else
         {
            jid = dd->from().bare().c_str();
         }
      }
   }

   bool isDelayedDelivery = false;
   if (timestamp == 0)
   {
      uint64_t ms = TimeHelper::millisSinceEpoch();
      timestamp = (time_t)(ms / 1000);
      millisecond = ms % 1000;

      isDelayedDelivery = false;
   }
   else
   {
      isDelayedDelivery = true;
   }

   cpc::string messageId = messageStanza.id().c_str();   // this is actually the message ID
   if (messageId.empty())
   {
      ErrLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage(): from=" << messageStanza.from() << " got empty message ID");
   }
   else
   {
      // The following code to "enhance" the message ID was added for OBELISK-4296 by Bill Liu:
      // Bill: I think the original issue is that 3rd party client might not send a global unique message ID  
      // which would mess up with our message history. Probably at that time, we didn't have any plan to react 
      // to the message, which you do now. If this causes issue, I think you need to remove this logic and combine 
      // another field to create a unique message ID.
      // Dominique: I think the best course of action is to leave it as-is and in this case we may not support
      // reacting to a message that comes from one of these clients, since the current requirement is only to 
      // support the reacting feature between our own clients.
      if (messageId.size() < 20)
      {
         DebugLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage(): from=" << messageStanza.from() << " got message ID " << 
            messageId << " which is too short so appending " << ("@" + messageStanza.from().full()));
         messageId += ("@" + messageStanza.from().full()).c_str();
      }
   }

   {
      auto it = info->mChatStates.find(messageStanza.from());
      if (it != info->mChatStates.end()) IsComposingManager::setMessageReceived(it->second);
      else DebugLog(<< "XmppMultiUserChatManagerImpl::handleMUCMessage(): from=" << messageStanza.from() << " cannot find associated chat state");
   }

   if (!reactionTarget.empty())
   {
      MultiUserChatNewReactionEvent evt;
      evt.message = sNextXmppMultiUserChatMessageHandle++;
      evt.messageId = messageId;
      evt.nickname = messageStanza.from().resource().c_str();
      evt.isPrivate = priv;
      evt.jid = jid;
      evt.timestamp = timestamp;
      evt.millisecond = millisecond;
      evt.isDelayedDelivery = isDelayedDelivery;

      // the only difference with the below
      evt.target = reactionTarget;
      evt.reactions = reactions;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatNewReaction), info->mHandle, evt);
   }
   else if (!retractTarget.empty())
   {
      MultiUserChatNewMessageRetractionEvent evt;
      evt.message = sNextXmppMultiUserChatMessageHandle++;
      evt.messageId = messageId;
      evt.nickname = messageStanza.from().resource().c_str();
      evt.isPrivate = priv;
      evt.jid = jid;
      evt.timestamp = timestamp;
      evt.millisecond = millisecond;
      evt.isDelayedDelivery = isDelayedDelivery;

      // the only difference with the above
      evt.target = retractTarget;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatNewMessageRetraction), info->mHandle, evt);
   }
   else
   {
      MultiUserChatNewMessageEvent evt;
      evt.message = sNextXmppMultiUserChatMessageHandle++;
      evt.messageId = messageId;
      evt.stanzaId = stanzaId;
      evt.originId = originId;
      evt.nickname = messageStanza.from().resource().c_str();
      evt.isPrivate = priv;
      evt.replaces = replaces;
      evt.jid = jid;
      evt.timestamp = timestamp;
      evt.millisecond = millisecond;
      evt.isDelayedDelivery = isDelayedDelivery;

      // the only difference with the above
      evt.plain = plain;
      evt.html = html;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatNewMessage), info->mHandle, evt);
   }
}

bool XmppMultiUserChatManagerImpl::handleMUCRoomCreation(gloox::MUCRoom* room)
{
   InfoLog(<< "XMPP: handleMUCRoomCreation: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return false; // bliu: old SDK returns true here

   // bliu: TODO mIsCreatedBySelf -> room->affiliation() == gloox::MUCRoomAffiliation::AffiliationOwner

   info->mIsNewRoom = true;

   if (!info->mCreateIfNotExisting)
   {
      info->mRoom->cancelRoomCreation();
      return false;
   }

   if (info->mInstantRoom) return true;

   info->mRoom->requestRoomConfig();

   info->mIsInConfiguration = true;

   return false;
}

void XmppMultiUserChatManagerImpl::handleMUCSubject(gloox::MUCRoom* room, const std::string& nick, const std::string& subject)
{
   InfoLog(<< "XMPP: handleMUCSubject: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   // bliu: make sure onMultiUserChatSubjectChanged always fired
   //if (info->mSubject != subject)
   {
      info->mSubject = subject;

      MultiUserChatSubjectChangedEvent evt;
      evt.nickname = nick.c_str();
      evt.subject = subject.c_str();
      fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatSubjectChanged), info->mHandle, evt);
   }
}

void XmppMultiUserChatManagerImpl::handleMUCInviteDecline(gloox::MUCRoom* room, const gloox::JID& invitee, const std::string& reason)
{
   InfoLog(<< "XMPP: handleMUCInviteDecline: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   MultiUserChatInvitationDeclinedEvent evt;
   evt.jid = invitee.full().c_str();
   evt.reason = reason.c_str();

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatInvitationDeclined), info->mHandle, evt);
}

void XmppMultiUserChatManagerImpl::handleMUCError(gloox::MUCRoom *room, const gloox::Error* error)
{
   InfoLog(<< "XMPP: handleMUCError: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   if (error == NULL) return;

   MultiUserChatErrorEvent evt;

   evt.error = error->text().c_str();

   switch (error->error())
   {
   case gloox::StanzaErrorNotAuthorized:
      evt.type = PasswordRequired;
      break;
   case gloox::StanzaErrorForbidden:
      evt.type = UserBanned;
      break;
   case gloox::StanzaErrorItemNotFound:
      evt.type = RoomNotFound;
      break;
   case gloox::StanzaErrorNotAllowed:
      evt.type = CreationRestricted;
      break;
   case gloox::StanzaErrorNotAcceptable:
      evt.type = ReservedNicknameRequired;
      break;
   case gloox::StanzaErrorRegistrationRequired:
      evt.type = NotAMember;
      break;
   case gloox::StanzaErrorConflict:
      evt.type = NicknameConflict;
      break;
   case gloox::StanzaErrorServiceUnavailable:
      evt.type = MaximumUsersReached;
      break;
   default:
      evt.type = OtherError;
   }

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatError), info->mHandle, evt);

   leave(info, "");
}

void XmppMultiUserChatManagerImpl::handleMUCInfo(gloox::MUCRoom *room, int features, const std::string& name, const gloox::DataForm* form)
{
   InfoLog(<< "XMPP: handleMUCInfo: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   updateRoomState(info, features, name, form);

   info->mRoomState.isReady = true;
   info->mFeatures = features;
   info->mIsInConfiguration = false;

   checkForRoomReady(info);
}

void XmppMultiUserChatManagerImpl::handleMUCInfoError(gloox::MUCRoom *room, const gloox::Error* error)
{
   InfoLog(<< "XMPP: handleMUCInfoError: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   MultiUserChatErrorEvent evt;
   evt.error = error->text().c_str();

   switch (error->error())
   {
   case gloox::StanzaErrorItemNotFound:
   case gloox::StanzaErrorServiceUnavailable:
      evt.type = RoomNotFound;
      break;

   default:
      evt.type = OtherError;
      break;
   }

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatError), info->mHandle, evt);
}

void XmppMultiUserChatManagerImpl::handleMUCItems(gloox::MUCRoom* room, const gloox::Disco::ItemList& items)
{
   InfoLog(<< "XMPP: handleMUCItems: ");

   // Not needed.
}

void XmppMultiUserChatManagerImpl::handleMUCItemsError(gloox::MUCRoom *room, const gloox::Error* error)
{
   InfoLog(<< "XMPP: handleMUCItemsError: ");

   // Not needed.
}

// gloox::MUCRoomConfigHandler
void XmppMultiUserChatManagerImpl::handleMUCConfigList(gloox::MUCRoom* room, const gloox::MUCListItemList& items, gloox::MUCOperation operation)
{
   InfoLog(<< "XMPP: handleMUCConfigList: ");

   XmppMultiUserChatListType type;

   switch(operation)
   {
      case gloox::RequestVoiceList:
         type = VoiceList;
         break;
      case gloox::RequestBanList:
         type = BanList;
         break;
      case gloox::RequestMemberList:
         type = MemberList;
         break;
      case gloox::RequestModeratorList:
         type = ModeratorList;
         break;
      case gloox::RequestOwnerList:
         type = OwnerList;
         break;
      case gloox::RequestAdminList:
         type = AdminList;
         break;
      default:
         return;
   }

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   MultiUserChatListRequestedEvent evt;
   evt.type = type;

   for(gloox::MUCListItemList::const_iterator it = items.begin(); it != items.end(); it++)
   {
      XmppMultiUserChatConfigurationsListItem item;
      item.jid = it->jid().full().c_str();
      item.nick = it->nick().c_str();
      item.affiliation = static_cast<XmppMultiUserChatAffiliation>(it->affiliation());
      item.role = static_cast<XmppMultiUserChatRole>(it->role());
      item.reason = it->reason().c_str();
      evt.items.push_back(item);
   }

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatListRequested), info->mHandle, evt);
}

void XmppMultiUserChatManagerImpl::handleMUCConfigForm(gloox::MUCRoom* room, const gloox::DataForm& form)
{
   InfoLog(<< "XMPP: handleMUCConfigForm: ");

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   MultiUserChatConfigurationRequestedEvent evt;

   XmppCommon::convert(form, evt.dataform);
   _dataform_to_configurations(form, evt.configurations);

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested), info->mHandle, evt);
}

void XmppMultiUserChatManagerImpl::handleMUCConfigResult(gloox::MUCRoom* room, bool success, gloox::MUCOperation operation)
{
   InfoLog(<< "XMPP: handleMUCConfigResult: " << success);

   XmppMultiUserChatInfo* info = getMultiUserChatInfoForGlooxRoom(room);
   if (info == NULL) return;

   if (success)
   {
      info->mRoom->getRoomInfo();
      return;
   }

   MultiUserChatErrorEvent evt;
   evt.type = OtherError;
   evt.error = "Operation failed";
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatError), info->mHandle, evt);
}

void XmppMultiUserChatManagerImpl::handleMUCRequest(gloox::MUCRoom* room, const gloox::DataForm& form)
{
   InfoLog(<< "XMPP: handleMUCRequest: " << std::shared_ptr<gloox::Tag>(form.tag())->xml());

   // Not currently needed.
}

// gloox::MUCInvitationHandler
void XmppMultiUserChatManagerImpl::handleMUCInvitation(const gloox::JID& room, const gloox::JID& from, const std::string& reason, const gloox::Message& msg, const std::string& password, bool cont, const std::string& thread)
{
   InfoLog(<< "XMPP: handleMUCInvitation:  room=" << room << " from=" << from << " reason=" << reason << " body=" << msg.body());

   //if (mAnonymous) return; // bliu: TODO
   if (!mAccount.isConnected()) return;

   MultiUserChatInvitationReceivedEvent evt;
   evt.room = room.username().c_str();
   evt.roomjid = room.bare().c_str();
   evt.jid = from.full().c_str();
   evt.reason = reason.c_str();
   evt.password = password.c_str();

   evt.timestamp = 0;
   evt.millisecond = 0;

   if (const gloox::DelayedDelivery* dd = msg.when())
   {
      TimeHelper::to_time(dd->stamp(), evt.timestamp, evt.millisecond);
   }

   if (evt.timestamp == 0)
   {
      uint64_t ms = TimeHelper::millisSinceEpoch();
      evt.timestamp = (time_t)(ms / 1000);
      evt.millisecond = ms % 1000;

      evt.isDelayedDelivery = false;
   }
   else
   {
      evt.isDelayedDelivery = true;
   }

   for (InfoMap::iterator it = mInfoMap.begin(), end = mInfoMap.end(); it != end; ++it)
   {
      XmppMultiUserChatInfo* _info = it->second;

      if (_info->mRoom != NULL && _info->mRoom->name() == room.username() && _info->mRoom->service() == room.server())
      {
         DebugLog(<< "the current user is already in room: " << room.bare());
         fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatInvitationReceived), _info->mHandle, evt);
         return;
      }
   }

   XmppMultiUserChatInfo* info = new XmppMultiUserChatInfo(*this, sNextXmppMultiUserChatHandle++);
   info->mInvitationInfo = new XmppMultiUserChatInfo::InvitationInfo;
   info->mInvitationInfo->invitor = from;
   info->mInvitationInfo->room = room;
   info->mInvitationInfo->password = password;
   info->mRoom = new gloox::MUCRoom(mAccount.getGlooxClient(), room, this, this);
   mInfoMap[info->mHandle] = info;

   XmppMultiUserChatCreatedResultEvent createEvt;
   createEvt.muc = info->mHandle;
   createEvt.account = mAccount.getHandle();
   createEvt.room = room.bare().c_str();
   fireEvent(cpcFunc(XmppMultiUserChatHandlerInternal::onCreateMultiUserChatResult), createEvt);

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatInvitationReceived), info->mHandle, evt);

   //resetPing(); // bliu: TODO
}

void XmppMultiUserChatManagerImpl::handleDiscoInfo(const gloox::JID& from, const gloox::Disco::Info& info, int context)
{
   // bliu: TODO
}

void XmppMultiUserChatManagerImpl::handleDiscoItems(const gloox::JID& from, const gloox::Disco::Items& items, int context)
{
   RoomListRetrievedEvent evt;

   for (gloox::Disco::ItemList::const_iterator it = items.items().begin(), end = items.items().end(); it != end; ++it)
   {
      RoomListItem _item;
      _item.jid = (*it)->jid().full().c_str();
      _item.name = (*it)->name().c_str();
      _item.node = (*it)->node().c_str();

      evt.rooms.push_back(_item);
   }

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onRoomListRetrieved), mAccount.getHandle(), evt);
}

void XmppMultiUserChatManagerImpl::handleDiscoError(const gloox::JID& from, const gloox::Error* error, int context)
{
   // bliu: TODO
}

bool XmppMultiUserChatManagerImpl::handleDiscoSet(const gloox::IQ& iq)
{
   return gloox::DiscoHandler::handleDiscoSet(iq);
}

void XmppMultiUserChatManagerImpl::handleBookmarks(const gloox::BookmarkList& bList, const gloox::ConferenceList& cList)
{
   RoomBookmarksReceivedEvent evt;

   for (gloox::ConferenceList::const_iterator it = cList.begin(), end = cList.end(); it != end; ++it)
   {
      RoomBookmark bookmark;
      bookmark.name = it->name.c_str();
      bookmark.jid = it->jid.c_str();
      bookmark.nickname = it->nick.c_str();
      bookmark.password = it->password.c_str();
      bookmark.autojoin = it->autojoin;
      evt.bookmarks.push_back(bookmark);
   }

   fireEvent(cpcFunc(XmppMultiUserChatHandler::onRoomBookmarksReceived), mAccount.getHandle(), evt);

}

void XmppMultiUserChatManagerImpl::InactiveTimerHandler::onTimer(unsigned short timerId, void* appState)
{
   if (timerId != 1) return; // not inactive timer

   XmppMultiUserChatInfo* info = reinterpret_cast<XmppMultiUserChatInfo*>(appState);
   assert(info != NULL);

   if (!mImpl.validateChatStatus(info)) return;

   if (!info->mIsActive) return;

   info->mGlooxChatStateFilter->setChatState(gloox::ChatStateInactive);
   info->mIsActive = false;
}

void XmppMultiUserChatManagerImpl::onChatState(IsComposing::IsComposingInfo* info, const gloox::JID& from, gloox::ChatStateType state)
{
   switch (state)
   {
   case gloox::ChatStateGone:
      // bliu: XEP-0085 A client SHOULD ignore <gone/> notifications received from other room occupants.
      break;

   case gloox::ChatStateComposing:
      {
         IsComposing::IsComposingDocument isComposingDoc;
         isComposingDoc.setState(IsComposing::IsComposingMessageState_Active);
         isComposingDoc.setRefresh(120); // bliu: TODO timeout value
         IsComposingManager::processIsComposingMessageNotification(info, isComposingDoc);
         break;
      }

   case gloox::ChatStatePaused:
   case gloox::ChatStateInactive:
      {
         IsComposing::IsComposingDocument isComposingDoc;
         isComposingDoc.setState(IsComposing::IsComposingMessageState_Idle);
         isComposingDoc.setRefresh(0); // bliu: TODO timeout value
         IsComposingManager::processIsComposingMessageNotification(info, isComposingDoc);
         break;
      }

   case gloox::ChatStateActive:
   default:
      break;
   }
}

bool XmppMultiUserChatManagerImpl::validateChatStatus(XmppMultiUserChatInfo* info)
{
   if (!mAccount.isConnected())
   {
      FIRE_ERROR(info->mHandle, "account " << mAccount.getHandle() << " is not connected for multi user chat " << info->mHandle);
      return false;
   }

   //if (info->mGlooxMessageSession == NULL)
   //{
   //   FIRE_ERROR(info->mHandle, "chat " << info->mHandle << " is either ended or not yet started");
   //   return false;
   //}

   return true;
}

void XmppMultiUserChatManagerImpl::transitionToActiveState(XmppMultiUserChatInfo* info)
{
   info->mGlooxChatStateFilter->setChatState(gloox::ChatStateActive);
   info->mIsActive = true;
   startInactiveTimer(info);
}

void XmppMultiUserChatManagerImpl::startInactiveTimer(XmppMultiUserChatInfo* info)
{
   if (info->mInactiveTimer == NULL)
   {
      info->mInactiveTimer = new resip::DeadlineTimer<resip::MultiReactor>(mAccount.getAccountInterface().getReactor());
   }
   else
   {
      info->mInactiveTimer->cancel();
   }

   info->mInactiveTimer->expires_from_now(60 * 1000); // inactive state timeout
   info->mInactiveTimer->async_wait(&mInactiveTimerHandler, 1, info);
}

void XmppMultiUserChatManagerImpl::updateParticipantState(XmppMultiUserChatInfo* info, const gloox::MUCRoomParticipant& glooxParticipant, const gloox::Presence& presenceStanza, ParticipantState& state)
{
   bool isChanged = false;

   cpc::string newNickname = glooxParticipant.newNick.c_str();
   if (!newNickname.empty())
   {
      state.nickname = newNickname;
      isChanged = true;
   }

   XmppRoster::PresenceType newPresence = static_cast<XmppRoster::PresenceType>(presenceStanza.presence());
   if (state.presence != newPresence)
   {
      state.presence = newPresence;
      isChanged = true;
   }

   cpc::string newMessage = presenceStanza.status().c_str();
   if (state.message != newMessage)
   {
      state.message = newMessage;
      isChanged = true;
   }

   XmppMultiUserChatAffiliation newAffiliation = static_cast<XmppMultiUserChatAffiliation>(glooxParticipant.affiliation);
   if (state.affiliation != newAffiliation)
   {
      state.affiliation = newAffiliation;
      isChanged = true;
   }

   XmppMultiUserChatRole newRole = static_cast<XmppMultiUserChatRole>(glooxParticipant.role);
   if (state.role != newRole)
   {
      state.role = newRole;
      isChanged = true;
   }

   bool newIsBanned = glooxParticipant.flags & gloox::UserBanned;
   if (state.isBanned != newIsBanned)
   {
      state.isBanned = newIsBanned;
      isChanged = true;
   }

   bool newIsKicked = glooxParticipant.flags & gloox::UserKicked;
   if (state.isKicked != newIsKicked)
   {
      state.isKicked = newIsKicked;
      isChanged = true;
   }

   bool newIsRemoved = glooxParticipant.flags & gloox::UserAffiliationChanged;
   if (state.isRemoved != newIsRemoved)
   {
      state.isRemoved = newIsRemoved;
      isChanged = true;
   }

   bool newIsRoomDestroyed = glooxParticipant.flags & gloox::UserRoomDestroyed;
   if (state.isRoomDestroyed != newIsRoomDestroyed)
   {
      state.isRoomDestroyed = newIsRoomDestroyed;
      isChanged = true;
   }

   if (!isChanged) return; // nothing changed yet

   if (&state == &info->mSelfState) // self
   {
      ParticipantSelfUpdatedEvent evt;
      if (glooxParticipant.actor != NULL) evt.jid = glooxParticipant.actor->full().c_str();
      evt.reason = glooxParticipant.reason.c_str();
      evt.state = info->mSelfState;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onParticipantSelfUpdated), info->mHandle, evt);
   }
   else
   {
      ParticipantUpdatedEvent evt;
      if (glooxParticipant.nick != NULL) evt.nickname = glooxParticipant.nick->resource().c_str();
      if (glooxParticipant.jid != NULL) state.jid = glooxParticipant.jid->bare().c_str();
      evt.reason = glooxParticipant.reason.c_str();
      evt.state = state;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onParticipantUpdated), info->mHandle, evt);
   }
}

void XmppMultiUserChatManagerImpl::updateRoomState(XmppMultiUserChatInfo* info, int flags, const std::string& room, const gloox::DataForm* form)
{
   bool isChanged = false;

   cpc::string newName = room.c_str();
   if (info->mRoomState.name != newName)
   {
      info->mRoomState.name = newName;
      isChanged = true;
   }

   bool newPublic = flags & gloox::FlagPublic;
   if (info->mRoomState.isPublic != newPublic)
   {
      info->mRoomState.isPublic = newPublic;
      isChanged = true;
   }

   bool newPasswordProtected = flags & gloox::FlagPasswordProtected;
   if (info->mRoomState.isPasswordProtected != newPasswordProtected)
   {
      info->mRoomState.isPasswordProtected = newPasswordProtected;
      isChanged = true;
   }

   bool newOpen = flags & gloox::FlagOpen;
   if (info->mRoomState.isOpen != newOpen)
   {
      info->mRoomState.isOpen = newOpen;
      isChanged = true;
   }

   bool newModerated = flags & gloox::FlagModerated;
   if (info->mRoomState.isModerated != newModerated)
   {
      info->mRoomState.isModerated = newModerated;
      isChanged = true;
   }

   bool newPersistent = flags & gloox::FlagPersistent;
   if (info->mRoomState.isPersistent != newPersistent)
   {
      info->mRoomState.isPersistent = newPersistent;
      isChanged = true;
   }

   bool newRecorded = flags & gloox::FlagPublicLogging;
   if (info->mRoomState.isRecorded != newRecorded)
   {
      info->mRoomState.isRecorded = newRecorded;
      isChanged = true;
   }

   int newAnnonyousMode = 0;
   if (flags & gloox::FlagNonAnonymous) newAnnonyousMode = 0;
   else if (flags & gloox::FlagSemiAnonymous) newAnnonyousMode = 1;
   else if (flags & gloox::FlagFullyAnonymous) newAnnonyousMode = 2;

   if (info->mRoomState.anonymousMode != newAnnonyousMode)
   {
      info->mRoomState.anonymousMode = newAnnonyousMode;
      isChanged = true;
   }

   if (form)
   {
      for (gloox::DataFormFieldContainer::FieldList::const_iterator it = form->fields().begin(), end = form->fields().end(); it != end; ++it)
      {
         const gloox::DataFormField* field = *it;
         if (field == NULL) continue;

         if (field->name() == "muc#roominfo_description" ||
             field->name() == "muc#roomconfig_roomdesc") // per XEP-0045 example 10, disco#info with FORM_TYPE of muc#roominfo
                                                         // also has muc#roomconfig_ fields
         {
            // update conference description
            cpc::string newDescription = field->value().c_str();
            if (info->mRoomState.description != newDescription)
            {
               info->mRoomState.description = newDescription;
               isChanged = true;
            }
         }
         else if (field->name() == "muc#roominfo_subject")
         {
            // update conference subject
            cpc::string newSubject = field->value().c_str();
            if (info->mRoomState.subject != newSubject)
            {
               info->mRoomState.subject = newSubject;
               isChanged = true;
            }
         }
         else if (field->name() == "muc#roominfo_occupants")
         {
            // update conference occupants
            int newNumOfParticipants = std::atoi(field->value().c_str());
            if (info->mRoomState.numOfParticipants != newNumOfParticipants)
            {
               info->mRoomState.numOfParticipants = newNumOfParticipants;
               isChanged = true;
            }
         }
         else if (field->name() == "x-muc#roominfo_creationdate" ||
                  field->name() == "muc#roominfo_creationdate") // seen against tigase
         {
            // update conference creation date
            cpc::string newCreation = field->value().c_str();
            if (info->mRoomState.creation != newCreation)
            {
               info->mRoomState.creation = newCreation;
               isChanged = true;
            }
         }
         else if (field->name() == "muc#roomconfig_roomowners")
         {
            if (info->mOwners != field->values())
            {
               isChanged = true;
               info->mOwners = field->values();
               info->mRoomState.owners.clear();
               for (gloox::StringList::const_iterator it = field->values().begin(); it != field->values().end(); ++it)
               {
                  const std::string& s = *it;
                  info->mRoomState.owners.push_back(s.c_str());
               }
            }
         }
         else if (field->name() == "muc#maxhistoryfetch")
         {
            cpc::string newHistoryFetch = field->value().c_str();
            if( newHistoryFetch.size() > 0 )
            {
               int newValue( atoi( newHistoryFetch.c_str() ));
               if( info->mRoomState.maxHistoryFetch != newValue )
               {
                  info->mRoomState.maxHistoryFetch = newValue;
                  isChanged = true;
               }
            }
         }
      }

      // bliu: TODO submit data form as the original implementation
   }

   if (isChanged || info->mForceRoomStateChangeCallback)
   {
      info->mForceRoomStateChangeCallback = false;

      MultiUserChatRoomStateChangedEvent evt;
      evt.state = info->mRoomState;

      fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged), info->mHandle, evt);
   }
   else
   {
      DebugLog(<< "room state hasn't been changed: " << info->mHandle);
   }
}

void XmppMultiUserChatManagerImpl::processAddHistory(XmppMultiUserChatInfo* info)
{
   if (info->mRoom == NULL) return;
   if (info->mHistoryToAdd.empty()) return;

   for (cpc::vector<XmppMultiUserChatHistoryItem>::const_iterator it = info->mHistoryToAdd.begin(), end = info->mHistoryToAdd.end(); it != end; it++)
   {
      const XmppMultiUserChatHistoryItem& item = *it;

      gloox::JID jid;
      if (!jid.setJID(item.from.c_str())) continue;

      if (item.plain.empty() && item.html.empty()) continue;

      if (!item.html.empty())
      {
         // Rely on content decoration
         info->mMessageFilter->mXHtmlContentToSend = item.html.c_str();
      }

      // bliu: TODO thread-safety
      tm* tt = TimeHelper::gmtime(&item.timestamp);
      char buffer[64];
      memset(buffer, 0, sizeof(buffer));

      // bliu: MUCRoom::addHistory() doesn't take millisecond
      /*size_t size = */std::strftime(buffer, sizeof(buffer) - 1, "%Y%m%dT%H:%M:%SZ", tt);

      info->mRoom->addHistory(item.plain.c_str(), jid, buffer);
   }

   info->mHistoryToAdd.clear();
}

void XmppMultiUserChatManagerImpl::checkForRoomReady(XmppMultiUserChatInfo* info)
{
   if (!info->mParticipantsReady) return;
   if (!info->mRoomState.isReady) return;
   if (info->mReadyNotified) return;

   // Self is notified as the last one
   info->mReadyNotified = true;

   MultiUserChatReadyEvent evt;
   evt.room = info->mRoom->name().c_str();
   evt.roomjid = (info->mRoom->name() + '@' + info->mRoom->service()).c_str();
   evt.features = info->mFeatures;
   evt.isNewRoom = info->mIsNewRoom;
   fireEvent(cpcFunc(XmppMultiUserChatHandler::onMultiUserChatReady), info->mHandle, evt);

   processAddHistory(info);
}

void XmppMultiUserChatManagerImpl::notifyMessageDelivered(XmppMultiUserChatInfo* info, const std::string& threadId, const std::string& messageId)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::notifyMessageDelivered() threadId=" << threadId << " messageId=" << messageId);
   if (!validateChatStatus(info)) return;
   if (!mDeliveryReceiptsEnabled)
   {
      WarningLog(<< "XmppMultiUserChatManagerImpl::notifyMessageDelivered: enableGroupChatDeliveryReceipts disabled in account settings");
   }
   gloox::Message m(gloox::Message::Groupchat, (info->mRoom->name() + '@' + info->mRoom->service()));
   m.addExtension(new gloox::Receipt(gloox::Receipt::Received, messageId));
   //m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
   mAccount.getGlooxClient()->send(m);
}

void XmppMultiUserChatManagerImpl::notifyMessageRead(XmppMultiUserChatInfo* info, const cpc::string& messageId)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::notifyMessageRead(): messageId=" << messageId);
   if (!validateChatStatus(info)) return;

   gloox::Message m(gloox::Message::Groupchat, (info->mRoom->name() + '@' + info->mRoom->service()));

   if (!mReadReceiptsEnabled)
   {
      WarningLog(<< "XmppMultiUserChatManagerImpl::notifyMessageRead: enableGroupChatReadReceipts disabled in account settings");
   }
   m.addExtension(new CpcXepChatMarker(CpcXepChatMarker::ChatMarkerType_Displayed, messageId.c_str()));
   //m.addExtension(new CpcXepMessageProcessingHint(CpcXepMessageProcessingHint::MessageProcessingHintType_StoreOffline));
   mAccount.getGlooxClient()->send(m);
}

void XmppMultiUserChatManagerImpl::getMUCSettings(const XmppAccount::XmppAccountSettings& settings)
{
   mDeliveryReceiptsEnabled = settings.enableGroupChatDeliveryReceipts;
   mReadReceiptsEnabled = settings.enableGroupChatReadReceipts;
}

bool XmppMultiUserChatManagerImpl::isMyStanza(const gloox::JID& from, gloox::MUCRoom* room)
{
   return (from.username() == room->name() && from.server() == room->service() && from.resource() == room->nick());
}

void XmppMultiUserChatManagerImpl::replaceMessage(XmppMultiUserChatInfo* info, XmppMultiUserChatMessageHandle message, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html)
{
   StackLog(<< "XmppMultiUserChatManagerImpl::replaceMessage(): handle=" << message << " replaces=" << replaces);

   if (info->mRoom == NULL)
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::replaceMessage not yet joined a room");
      return;
   }
   if (replaces.empty())
   {
      FIRE_ERROR(info->mHandle, "XmppMultiUserChatManager::replaceMessage message ID to replace empty");
      return;
   }

   // Rely on content decoration
   info->mMessageFilter->mReplacesMessageID = replaces.c_str();
   sendMessage(info, message, plain, html);
}

} // XmppMultiUserChat
} // CPCAPI2
#endif // CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE
