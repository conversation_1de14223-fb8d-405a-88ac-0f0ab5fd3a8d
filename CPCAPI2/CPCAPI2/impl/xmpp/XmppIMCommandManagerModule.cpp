#include "brand_branded.h"

#include "interface/public/xmpp/XmppIMCommandManager.h"

#if (CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "XmppIMCommandManagerInterface.h"
#endif

namespace CPCAPI2
{
namespace XmppIMCommand
{
   XmppIMCommandManager* XmppIMCommandManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_XMPP_IM_COMMAND_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<XmppIMCommandManagerInterface>(phone, "XmppIMCommandManagerInterface");
#else
      return NULL;
#endif
   }

}
}
