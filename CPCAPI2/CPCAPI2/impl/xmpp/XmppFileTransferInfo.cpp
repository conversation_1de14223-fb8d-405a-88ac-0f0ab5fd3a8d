#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)

#include "XmppFileTransferInfo.h"

using namespace CPCAPI2::XmppFileTransfer;
using namespace CPCAPI2::XmppAccount;

XmppFileTransferItemInfo::XmppFileTransferItemInfo( XmppFileTransferItemHandle newHandle )
   : isDone(false)
   , XEP0363Progress(NULL)
{
   // Initialize the basic type members of details
   handle = newHandle;
}

XmppFileTransferItemInfo::~XmppFileTransferItemInfo()
{
   if (XEP0363Progress != NULL) XEP0363Progress->abort();

   if (XEP0363Uploader.valid()) XEP0363Uploader.wait();

   delete XEP0363Progress; // XEP0363Uploader requires XEP0363Progress
}

XmppFileTransferInfo::XmppFileTransferInfo( XmppFileTransferHandle newHandle, XmppAccountHandle account )
   : accountHandle( account )
   , handle( newHandle )
{
}

#endif
