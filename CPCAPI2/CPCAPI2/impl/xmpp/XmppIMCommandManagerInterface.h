#pragma once

#if !defined(__CPCAPI2_XMPP_IM_COMMAND_MANAGER_INTERFACE_H__)
#define __CPCAPI2_XMPP_IM_COMMAND_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include "XmppAccountInterface.h"
#include "xmpp/XmppIMCommandManager.h"
#include "xmpp/XmppIMCommandHandler.h"

namespace CPCAPI2
{

namespace XmppIMCommand
{

class XmppIMCommandManagerInterface
   : public XmppIMCommandManager
   , public PhoneModule
{
public:
   XmppIMCommandManagerInterface(Phone* phone);
   virtual ~XmppIMCommandManagerInterface();

   // PhoneModule Interface
   virtual void Release() OVERRIDE;

   // XmppIMCommandManager Interface
   virtual int setHandler(XmppAccount::XmppAccountHandle handle, XmppChatIMCommandHandler* handler) OVERRIDE;
   virtual int setHandler(XmppAccount::XmppAccountHandle handle, XmppMultiUserChatIMCommandHandler* handler) OVERRIDE;
   virtual XmppChat::XmppChatMessageHandle sendChatIMCommand(XmppChat::XmppChatHandle handle, int type, const cpc::string& payloacd, const cpc::string& htmlPayload) OVERRIDE;
   virtual XmppMultiUserChat::XmppMultiUserChatMessageHandle sendMultiUserChatIMCommand(XmppMultiUserChat::XmppMultiUserChatHandle handle, int type, const cpc::string& payload) OVERRIDE;

private:
   PhoneInterface* mPhone;
   XmppAccount::XmppAccountInterface* mAccountIf;
};

}
}

#endif // __CPCAPI2_XMPP_IM_COMMAND_MANAGER_INTERFACE_H__
