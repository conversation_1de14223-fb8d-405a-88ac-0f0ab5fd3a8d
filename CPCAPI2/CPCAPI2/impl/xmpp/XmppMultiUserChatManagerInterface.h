#pragma once

#if !defined(__CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERFACE_H__)
#define __CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppMultiUserChatInternal.h"
#include "xmpp/XmppIMCommandHandler.h"
#include "XmppMultiUserChatHandlerInternal.h"
#include "XmppCommon.h"

namespace CPCAPI2
{

class PhoneInterface;
class LocalLogger;

namespace XmppMultiUserChat
{

class XmppMultiUserChatManagerImpl;
struct XmppMultiUserChatInfo;

class XmppMultiUserChatManagerInterface
   : public XmppMultiUserChatManagerInternal
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppMultiUserChatManagerImpl>
{

public:

   XmppMultiUserChatManagerInterface(Phone* phone);
   virtual ~XmppMultiUserChatManagerInterface();

   void addSdkObserver(XmppMultiUserChatHandlerInternal* observer);
   void removeSdkObserver(XmppMultiUserChatHandlerInternal* observer);
   void getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts);
   PhoneInterface* phoneInterface();

   // PhoneModule Interface
   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   // XmppMultiUserChatManager Interface
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler) OVERRIDE;
   virtual int getRoomList(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int setRoomBookmarks(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks) OVERRIDE;
   virtual int getRoomBookmarks(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, const cpc::string& room) OVERRIDE;
   void create(XmppMultiUserChatHandle handle, XmppAccount::XmppAccountHandle account, const cpc::string& room);
   virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, bool instantRoom) OVERRIDE;
   void create(XmppMultiUserChatHandle handle, XmppAccount::XmppAccountHandle account, bool instantRoom);
   virtual int destroyRoom(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password) OVERRIDE;
   virtual int getRoomInfo(XmppMultiUserChatMessageHandle handle) OVERRIDE;
   virtual int getRoomsInfo(const cpc::vector<XmppMultiUserChatHandle>& handles) OVERRIDE;
   virtual int accept(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int decline(XmppMultiUserChatHandle handle, const cpc::string& reason) OVERRIDE;
   virtual int join(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int join(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int leave(XmppMultiUserChatHandle handle, const cpc::string& reason) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle sendMessage(XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html) OVERRIDE;
   void sendMessage(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html);
   virtual XmppMultiUserChatMessageHandle sendReaction(XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions) OVERRIDE;
   void sendReaction(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   virtual XmppMultiUserChatMessageHandle sendMessageRetraction(XmppMultiUserChatHandle handle, const cpc::string& target) OVERRIDE;
   void sendMessageRetraction(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target);
   virtual int setIsComposingMessage(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval) OVERRIDE;
   virtual int publishPresence(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note) OVERRIDE;
   virtual int changeNickname(XmppMultiUserChatHandle handle, const cpc::string& nickname) OVERRIDE;
   virtual int changeSubject(XmppMultiUserChatHandle handle, const cpc::string& subject) OVERRIDE;
   virtual int invite(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason) OVERRIDE;
   virtual int kick(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason) OVERRIDE;
   virtual int ban(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason) OVERRIDE;
   virtual int changeAffiliation(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason) OVERRIDE;
   virtual int changeJidAffiliation(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason) OVERRIDE;
   virtual int changeRole(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason) OVERRIDE;
   virtual int requestConfigurations(XmppMultiUserChatHandle handle) OVERRIDE;
   virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform) OVERRIDE;
   virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations) OVERRIDE;
   virtual int requestList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type) OVERRIDE;
   virtual int setList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items) OVERRIDE;
   virtual int notifyMessageRead(XmppMultiUserChatHandle handle, const cpc::string& messageId) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle replaceMessage(XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html) OVERRIDE;

   // XmppMultiUserChatManagerInternal
   // static XmppMultiUserChatManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
   virtual void createMultiUserChat(XmppAccount::XmppAccountHandle account, const cpc::string& room) OVERRIDE;

   int setIMCommandHandler(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler);
   XmppMultiUserChatMessageHandle sendIMCommand(XmppMultiUserChatHandle handle, int type, const cpc::string& payload);

private:

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler);
   void setIMCommandHandlerImpl(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler);
   void getRoomListImpl(XmppAccount::XmppAccountHandle account);
   void setRoomBookmarksImpl(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks);
   void getRoomBookmarksImpl(XmppAccount::XmppAccountHandle account);
   void createImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandle handle, const cpc::string& room);
   void createMultiUserChatImpl(XmppAccount::XmppAccountHandle account, const cpc::string& room);
   void DEPRECATE_createImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandle handle, bool instantRoom);
   void destroyRoomImpl(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password);
   void getRoomInfoImpl(XmppMultiUserChatHandle handle);
   void getRoomsInfoImpl(const cpc::vector<XmppMultiUserChatHandle>& handles);
   void acceptImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void declineImpl(XmppMultiUserChatHandle handle, const cpc::string& reason);
   void joinImpl(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void DEPRECATE_joinImpl(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void leaveImpl(XmppMultiUserChatHandle handle, const cpc::string& reason);
   void sendMessageImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& plain, const cpc::string& html);
   void sendReactionImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   void sendMessageRetractionImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& target);
   void setIsComposingMessageImpl(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval);
   void publishPresenceImpl(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note);
   void changeNicknameImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname);
   void changeSubjectImpl(XmppMultiUserChatHandle handle, const cpc::string& subject);
   void inviteImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason);
   void kickImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason);
   void banImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason);
   void changeAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeJidAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeRoleImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason);
   void requestConfigurationsImpl(XmppMultiUserChatHandle handle);
   void setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform);
   void setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations);
   void requestListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type);
   void setListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items);
   void sendIMCommandImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, int type, const cpc::string& payload);
   void notifyMessageReadImpl(XmppMultiUserChatHandle handle, const cpc::string& messageId);
   void replaceMessageImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html);

   struct InfoContext
   {
      InfoContext() : impl(NULL), info(NULL) {}
      XmppMultiUserChatManagerImpl* impl;
      XmppMultiUserChatInfo* info;
   };

   bool getMultiUserChatInfoContext(XmppMultiUserChatHandle handle, InfoContext& context) const;

private:

   PhoneInterface* mPhone;
   LocalLogger* mLocalLogger;
   bool mPreRelease;
   XmppAccount::XmppAccountInterface* mAccountIf;
   std::list<XmppMultiUserChatHandlerInternal*> mSdkObservers;
   std::function<void(void)> mCbHook;

};

}

}

#endif // __CPCAPI2_XMPP_MULTI_USER_CHAT_MANAGER_INTERFACE_H__
