#pragma once

#ifndef __CPCAPI2_XMPP_VCARD_MANAGER_INTERFACE_H__
#define __CPCAPI2_XMPP_VCARD_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"

#include "phone/PhoneModule.h"
#include "xmpp/XmppVCardInternal.h"
#include "xmpp/XmppVCardHandlerInternal.h"
#include "XmppAccountInterface.h"
#include "XmppCommon.h"

namespace CPCAPI2
{

// Forward declarations
class Phone;
class PhoneInterface;
class LocalLogger;

namespace XmppVCard
{

// Forward declarations
class XmppVCardManagerImpl;

// Main file transfer interface
class XmppVCardManagerInterface
   : public XmppVCardManagerInternal
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppVCardManagerImpl>
{

public:

   XmppVCardManagerInterface(Phone* phone);
   virtual ~XmppVCardManagerInterface();

   // PhoneModule
   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   // XmppVCardManager
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler) OVERRIDE;
   virtual XmppVCardHandle create(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int fetchVCard(XmppVCardHandle handle, const cpc::string& jid) OVERRIDE;
   virtual int storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard) OVERRIDE;
   virtual int cancelVCardOperations(XmppVCardHandle handle) OVERRIDE;

   // XmppVCardManagerInternal
   // static XmppVCardManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
   virtual void createVCard(XmppAccount::XmppAccountHandle account) OVERRIDE;

   virtual PhoneInterface* phoneInterface() { return mPhone; }

   void addSdkObserver(XmppVCardHandlerInternal* observer);
   void removeSdkObserver(XmppVCardHandlerInternal* observer);
   void getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts);

private: // methods invoked by DumFp thread asynchronously

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler);
   void createImpl(XmppAccount::XmppAccountHandle account, XmppVCardHandle handle);
   void createVCardImpl(XmppAccount::XmppAccountHandle account);
   void fetchVCardImpl(XmppVCardHandle handle, const cpc::string& jid);
   void storeVCardImpl(XmppVCardHandle handle, const XmppVCardDetail& vcard);
   void cancelVCardOperationsImpl(XmppVCardHandle handle);

private: // helpers

   XmppVCardManagerImpl* getVCardManager(XmppVCardHandle handle);

private: // data

   XmppAccount::XmppAccountInterface* mAccountIf;
   PhoneInterface* mPhone;
   LocalLogger* mLocalLogger;
   bool mPreRelease;
   std::list<XmppVCardHandlerInternal*> mSdkObservers;
   std::function<void(void)> mCbHook;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppVCard::VCardOperationResultEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppVCard::ErrorEvent& event);

}

}

#endif // __CPCAPI2_XMPP_VCARD_MANAGER_INTERFACE_H__
