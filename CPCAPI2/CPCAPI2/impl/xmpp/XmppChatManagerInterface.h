#pragma once

#if !defined(__CPCAPI2_XMPP_CHAT_MANAGER_INTERFACE_H__)
#define __CPCAPI2_XMPP_CHAT_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include "xmpp/XmppChatManager.h"
#include "xmpp/XmppChatHandler.h"
#include "xmpp/XmppIMCommandHandler.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"
#include "XmppChatInfo.h"
#include "XmppCommon.h"

namespace CPCAPI2
{

class Phone;

namespace XmppAccount
{
class XmppAccountInterface;
}

namespace XmppChat
{

class XmppChatManagerImpl;

class XmppChatManagerInterface
   : public XmppChatManager
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppChatManagerImpl>
{

public:

   XmppChatManagerInterface(Phone* phone);
   virtual ~XmppChatManagerInterface();

   void addSdkObserver(XmppChatHandler* observer);
   void removeSdkObserver(XmppChatHandler* observer);
   void getChatHandles(cpc::vector<XmppChatHandle>& chats);

   // PhoneModule Interface
   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   // XmppChatManager Interface
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppChatHandler* handler) OVERRIDE;
   virtual XmppChatHandle createChat(XmppAccount::XmppAccountHandle account) OVERRIDE;
   void createChat(XmppChatHandle chat, XmppAccount::XmppAccountHandle account);
   virtual int addParticipant(XmppChatHandle chat, const cpc::string& participantAddress) OVERRIDE;
   virtual int start(XmppChatHandle chat) OVERRIDE;
   virtual int end(XmppChatHandle chat) OVERRIDE;
   virtual XmppChatMessageHandle sendMessage(XmppChatHandle chat, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject) OVERRIDE;
   void sendMessage(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject);
   virtual XmppChatMessageHandle sendReaction(XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string>& reactions) OVERRIDE;
   void sendReaction(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   virtual XmppChatMessageHandle sendMessageRetraction(XmppChatHandle chat, const cpc::string& target) OVERRIDE;
   void sendMessageRetraction(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target);
   virtual int accept(XmppChatHandle chat) OVERRIDE;
   virtual int reject(XmppChatHandle chat) OVERRIDE;

   DEPRECATED virtual XmppChatMessageHandle notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus) OVERRIDE;
   DEPRECATED int notifyMessageDelivered(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus);

   DEPRECATED virtual XmppChatMessageHandle notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus) OVERRIDE;
   DEPRECATED int notifyMessageDisplayed(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus);

   DEPRECATED virtual int notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   DEPRECATED virtual int notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;

   DEPRECATED virtual int notifyMessageRead(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual int notifyMessageRead(XmppChatHandle chat, const cpc::string& threadId, const cpc::string& messageId) OVERRIDE;
   virtual int notifyMessageRead(XmppAccount::XmppAccountHandle account, const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId) OVERRIDE;

   virtual int setIsComposingMessage(XmppChatHandle chat, int refreshInterval, int idleInterval) OVERRIDE;
   virtual int validateChatHandle(XmppAccount::XmppAccountHandle account, XmppChatHandle chat) OVERRIDE;
   virtual XmppChatMessageHandle replaceMessage(XmppChatHandle chat, const cpc::string& replaces, const cpc::string& messageContent, const cpc::string& htmlText = "", const cpc::string& subject = "") OVERRIDE;

   virtual cpc::string getRemoteSyncFromID(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncToID(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncConversationID(XmppChatHandle chat) OVERRIDE;
   virtual cpc::string getRemoteSyncUniqueID(XmppChatHandle chat, const cpc::string& stanzaID) OVERRIDE;
   virtual cpc::string getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID = "") OVERRIDE;
   virtual cpc::string getRemoteSyncEncodedContent(const cpc::string& plainText, const cpc::string& htmlText = "") OVERRIDE;
   virtual int getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML) OVERRIDE;

   XmppChatMessageHandle createMessage(XmppChatHandle chat);
   int createMessage(XmppChatMessageHandle chatMessage, XmppChatHandle chat);

   int setIMCommandHandler(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppChatIMCommandHandler* handler);
   XmppChatMessageHandle sendIMCommand(XmppChatHandle chat, int type, const cpc::string& payload, const cpc::string& htmlPayload = "");

private:

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppChatHandler* handler);
   void setIMCommandHandlerImpl(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppChatIMCommandHandler* handler);
   void createChatImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle sessionHandle);
   void addParticipantImpl(XmppChatHandle chat, const cpc::string& participantAddress);
   void startImpl(XmppChatHandle chat);
   void endImpl(XmppChatHandle chat);
   void sendMessageImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject);
   void sendReactionImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   void sendMessageRetractionImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& target);
   void acceptImpl(XmppChatHandle chat);
   void rejectImpl(XmppChatHandle chat);
   DEPRECATED void notifyMessageDeliveredImpl_DEPRECATED(XmppChatHandle chat, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageDeliveryStatus messageDeliveryStatus);
   DEPRECATED void notifyMessageDisplayedImpl_DEPRECATED(XmppChatHandle chat, XmppChatMessageHandle message, XmppChatMessageHandle origMessage, MessageDisplayStatus messageDeliveryStatus);
   DEPRECATED void notifyMessageDeliveredImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   DEPRECATED void notifyMessageDisplayedImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   DEPRECATED void notifyMessageReadImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   void notifyMessageReadImpl(XmppChatHandle chat, const cpc::string& threadId, const cpc::string& messageId);
   void notifyMessageReadImpl(XmppAccount::XmppAccountHandle account, const cpc::string& peerJid, const cpc::string& threadId, const cpc::string& messageId);

   void setIsComposingMessageImpl(XmppChatHandle chat, int refreshInterval, int idleInterval);
   void sendIMCommandImpl(XmppChatHandle chat, XmppChatMessageHandle message, int type, const cpc::string& payload, const cpc::string& htmlPayload);
   void validateChatHandleImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle chat);
   void replaceMessageImpl(XmppChatHandle chat, XmppChatMessageHandle message, const cpc::string& messageId, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject);

   struct InfoContext
   {
      InfoContext() : impl(NULL), info(NULL) {}
      XmppChatManagerImpl* impl;
      XmppChatInfo* info;
   };

   bool getChatInfoContext(XmppChatHandle handle, InfoContext& context) const;
   RemoteSyncXmppHelper::RemoteSyncXmppHelper* getRemoteSyncHelper();

private:

   PhoneInterface* mPhone;
   XmppAccount::XmppAccountInterface* mAccountIf;
   bool mPreRelease;
   std::list<XmppChatHandler*> mSdkObservers;
   LocalLogger* mLocalLogger;
   RemoteSyncXmppHelper::RemoteSyncXmppHelper* mRemoteSyncHelperIf;

};

}

}

#endif // __CPCAPI2_XMPP_CHAT_MANAGER_INTERFACE_H__
