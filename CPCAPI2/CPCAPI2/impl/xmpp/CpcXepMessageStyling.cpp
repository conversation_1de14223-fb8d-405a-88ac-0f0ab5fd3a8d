//
//  CpcXepMessageStyling.cpp
//  BriaVoip
//
//  Created by <PERSON> on March 19, 2025.
//  Copyright 2025 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageStyling.h"

const std::string CpcXepMessageStyling::XMLNS_MESSAGE_STYLING = "urn:xmpp:styling:0";

CpcXepMessageStyling::CpcXepMessageStyling(bool styled)
   : StanzaExtension(EXepMessageStyling)
   , mStyled(styled)
   , mValid(true)
{
}

CpcXepMessageStyling::CpcXepMessageStyling(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageStyling)
   , mStyled(true)
   , mValid(false)
{
   if (tag == NULL) return;

   if (auto styled = tag->findTag("/message/unstyled"))
   {
      mStyled = false;
   }

   mValid = true;
}

const std::string& CpcXepMessageStyling::filterString() const
{
   static const std::string filter = "/message/unstyled[@xmlns='" + XMLNS_MESSAGE_STYLING + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageStyling::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageStyling(tag);
}

gloox::Tag* CpcXepMessageStyling::tag() const
{
   if (mStyled) return NULL;

   gloox::Tag* t = new gloox::Tag("unstyled", "xmlns", XMLNS_MESSAGE_STYLING);

   if (!mValid) return NULL;

   return t;
}

gloox::StanzaExtension* CpcXepMessageStyling::clone() const
{
   return new CpcXepMessageStyling(*this);
}
