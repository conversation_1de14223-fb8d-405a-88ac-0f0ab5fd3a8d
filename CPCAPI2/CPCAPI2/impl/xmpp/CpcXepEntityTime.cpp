//
//  CpcXepEntityTime.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 14, 2016.
//  Copyright 2016 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepEntityTime.h"

#include <ctime>
#include <cstring>

const std::string CpcXepEntityTime::XMLNS_ENTITY_TIME = "urn:xmpp:time";
const std::string CpcXepEntityTime::XMLNS_ENTITY_TIME_LEGACY = "jabber:iq:time";

CpcXepEntityTime::CpcXepEntityTime(const gloox::Tag* tag)
   : StanzaExtension(EXepEntityTime)
   , mValid(false)
{
   if (tag == NULL) return;

   if (tag->name() == "time" && tag->xmlns() == XMLNS_ENTITY_TIME)
   {
      if (const gloox::Tag* _utc = tag->findChild("utc"))
      {
         mTimestamp = _utc->cdata();
      }

      if (const gloox::Tag* _tz = tag->findChild("tzo"))
      {
         mTimezone = _tz->cdata();
      }

      mValid = true;

      return;
   }

   if (tag->name() == "query" && tag->xmlns() == XMLNS_ENTITY_TIME_LEGACY)
   {
      if (const gloox::Tag* _utc = tag->findChild("utc"))
      {
         mTimestamp = _utc->cdata();
      }

      if (const gloox::Tag* _tz = tag->findChild("tz"))
      {
         mTimezone = _tz->cdata();
      }

      mValid = true;

      return;
   }
}

CpcXepEntityTime::~CpcXepEntityTime()
{
}

const std::string& CpcXepEntityTime::timestamp() const
{
   return mTimestamp;
}

const std::string& CpcXepEntityTime::timezone() const
{
   return mTimezone;
}

// bliu: TODO set mTimezone according to XEP-0082
void CpcXepEntityTime::setTime()
{
   time_t t = time(NULL);
   tm* utc = std::gmtime(&t);
   char buffer [64];
   memset(buffer, 0, sizeof(buffer));
   /*size_t size = */std::strftime(buffer, sizeof(buffer) - 1, "%Y-%m-%dT%H:%M:%SZ", utc);
   mTimestamp = buffer;

   mValid = true;
}

const std::string& CpcXepEntityTime::filterString() const
{
   static const std::string filter =
      "/iq/time[@xmlns='" + XMLNS_ENTITY_TIME + "']"
      "|/iq/query[@xmlns='" + XMLNS_ENTITY_TIME_LEGACY + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepEntityTime::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepEntityTime(tag);
}

gloox::Tag* CpcXepEntityTime::tag() const
{
   gloox::Tag* _tag = new gloox::Tag("time", "xmlns", XMLNS_ENTITY_TIME);

   if (!mValid) return _tag;

   gloox::Tag* _utc = new gloox::Tag("utc", mTimestamp);
   gloox::Tag* _tzo = new gloox::Tag("tzo", mTimezone);

   _tag->addChild(_utc);
   _tag->addChild(_tzo);

   return _tag;
}

gloox::StanzaExtension* CpcXepEntityTime::clone() const
{
   return new CpcXepEntityTime(*this);
}
