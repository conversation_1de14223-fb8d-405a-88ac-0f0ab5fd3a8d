//
//  CpcXepMessageStanza
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_STANZA_H__
#define CPC_XEP_MESSAGE_STANZA_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0359} (unique and stable IDs for messages).
*/
class CpcXepMessageStanza : public gloox::StanzaExtension
{
public:
   CpcXepMessageStanza(std::string stanzaID);
   CpcXepMessageStanza(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   std::string stanzaID() const { return mStanzaID; }

   static const std::string XMLNS_MESSAGE_STANZA;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mStanzaID;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_STANZA_H__
