//
//  CpcXepIMCommand.h
//  BriaVoip
//
//  Created by <PERSON> on November 16, 2016.
//  Copyright 2016 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_IM_COMMAND_H__
#define CPC_XEP_IM_COMMAND_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of IM Command.
*/
class CpcXepIMCommand : public gloox::StanzaExtension
{
public:
   CpcXepIMCommand(int type, const std::string& payload);
   CpcXepIMCommand(const gloox::Tag* tag = NULL);
   virtual ~CpcXepIMCommand();

   static const std::string XMLNS_IM_COMMAND;

   const int type() const;
   const std::string& payload() const;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   int mType;
   std::string mPayload;
   bool mValid;
};

#endif // CPC_XEP_IM_COMMAND_H__
