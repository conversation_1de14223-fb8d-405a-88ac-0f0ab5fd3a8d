#pragma once

#if !defined(__CPCAPI2_XMPP_COMMON_H__)
#define __CPCAPI2_XMPP_COMMON_H__

#include "dataform.h"

#include "xmpp/XmppAccount.h"
#include <map>
#include <memory>

namespace CPCAPI2
{
namespace XmppCommon
{

void convert(const XmppAccount::XmppDataForm& dataform, gloox::DataForm& gloox_dataform);
void convert(const gloox::DataForm& gloox_dataform, XmppAccount::XmppDataForm& dataform);

// Special notice:
// any shared_ptr inside mImplMap will be deleted *after* the destruction of the subclass of ImplManager
// check for XmppRosterImpl's destructor to see why this matters
template<typename T>
class ImplManager
{
public:
   std::shared_ptr<T> getImpl(XmppAccount::XmppAccountHandle account)
   {
      for (auto i : mImplMap)
      {
         if (i.first == account) return i.second;
      }

      return NULL;
   }

   void destroyImpl(XmppAccount::XmppAccountHandle account) // should be accessed by impl class T only
   {
      mImplMap.erase(account);
   }

protected:
   typedef std::map<XmppAccount::XmppAccountHandle, std::shared_ptr<T> > ImplMap;
   ImplMap mImplMap;
};

}
}

#endif // __CPCAPI2_XMPP_COMMON_H__
