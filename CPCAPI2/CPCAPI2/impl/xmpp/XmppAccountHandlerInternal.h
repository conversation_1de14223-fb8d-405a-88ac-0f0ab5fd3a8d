#pragma once
#ifndef __CPCAPI2_XMPPACOUNTHANDLER_INTERNAL_H__
#define __CPCAPI2_XMPPACOUNTHANDLER_INTERNAL_H__

#include <xmpp/XmppAccountHandler.h>
#include <xmpp/XmppAccountSettings.h>

namespace CPCAPI2
{
   namespace XmppAccount
   {
      struct XmppAccountConfiguredEvent
      {
         XmppAccountSettings settings;
      };

      struct XmppAccountEnabledEvent
      {
         XmppAccountSettings settings;
      };

      struct XmppAccountDisabledEvent
      {
      };

      class XmppAccountHandlerInternal : public XmppAccountHandler
      {
      public:
         // Fired after an event has been configured with all settings
         virtual int onAccountConfigured( XmppAccountHandle account, const XmppAccountConfiguredEvent& args ) = 0;

         // Fired when an account has been enabled
         virtual int onAccountEnabled( XmppAccountHandle account, const XmppAccountEnabledEvent& args ) { return kSuccess; }

         // Fired when an account has been disabled
         virtual int onAccountDisabled( XmppAccountHandle account, const XmppAccountDisabledEvent& args ) { return kSuccess; }
      };
   }
}


#endif // __CPCAPI2_XMPPACOUNTHANDLER_INTERNAL_H__
