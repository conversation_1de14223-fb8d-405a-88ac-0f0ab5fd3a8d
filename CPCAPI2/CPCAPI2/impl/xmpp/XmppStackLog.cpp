#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)

#include "XmppStackLog.h"
#include "log/LocalLogger.h"
#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_STACK

namespace CPCAPI2
{

XmppStackLog::XmppStackLog(bool logXmppStanzas, LocalLogger* localLogger)
   : mLogXmppStanzas(logXmppStanzas)
   , mLocalLogger(localLogger)
{
}

XmppStackLog::~XmppStackLog()
{
}

void XmppStackLog::handleLog(gloox::LogLevel level, gloox::LogArea area, const std::string& message)
{
   if (gloox::LogAreaXmlIncoming == area)
   {
      if (mLogXmppStanzas)
      {
         mLocalLogger->logMaxMessage(" IN: {}", message);
         DebugLog(<< " IN: " << message);
      }
      return;
   }
   else if (gloox::LogAreaXmlOutgoing == area)
   {
      if (mLogXmppStanzas)
      {
         if (message.size() == 1 && message.c_str()[0] == ' ')
         {
            // avoid logging ping messages
         }
         else
         {
            mLocalLogger->logMaxMessage(" OUT: {}", message);
            DebugLog(<< "OUT: " << message);
         }
      }
      return;
   }

   switch (level)
   {
   case gloox::LogLevelWarning:
      WarningLog(<< message);
      break;
   case gloox::LogLevelError:
      ErrLog(<< message);
      break;
   case gloox::LogLevelDebug:
   default:
      DebugLog(<< message);
      break;
   }
}

} // namespace CPCAPI2

#endif // CPCAPI2_XMPP_ACCOUNT_MODULE
