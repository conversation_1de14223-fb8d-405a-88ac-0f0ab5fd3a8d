//
//  CpcXepUserActivity.h
//  BriaVoip
//
//  Created by <PERSON> on 2/8/11.
//  Copyright 2011 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_USER_ACTIVITY_H__
#define CPC_XEP_USER_ACTIVITY_H__

#ifdef __ANDROID__
#include "stanzaextension.h"
#include "tag.h"
#else
#include <stanzaextension.h>
#include <tag.h>
#endif

// TODO: implement XEP-0108 fully with pubsub and PEP

class CpcXepUserActivity : public gloox::StanzaExtension
{
public:
   enum GeneralType
   {
      ActivityDoingChores,
      ActivityDrinking,
      ActivityEating,
      ActivityExercising,
      ActivityGrooming,
      ActivityHavingAppointment,
      ActivityInactive,
      ActivityRelaxing,
      ActivityTalking,
      ActivityTraveling,
      ActivityUndefinedGeneralType,
      ActivityWorking,
      ActivityInvalidGeneralType
   };

   enum SpecificType
   {
      ActivityAtTheSpa,
      ActivityBrushingTeeth,
      ActivityBuyingGroceries,
      ActivityCleaning,
      ActivityCoding,
      ActivityCommuting,
      ActivityCooking,
      ActivityCycling,
      ActivityDancing,
      ActivityDayOff,
      ActivityDoingMaintenance,
      ActivityDoingTheDishes,
      ActivityDoingTheLaundry,
      ActivityDriving,
      ActivityFishing,
      ActivityGaming,
      ActivityGardening,
      ActivityGettingAHaircut,
      ActivityGoingOut,
      ActivityHangingOut,
      ActivityHavingABeer,
      ActivityHavingASnack,
      ActivityHavingBreakfast,
      ActivityHavingCoffee,
      ActivityHavingDinner,
      ActivityHavingLunch,
      ActivityHavingTea,
      ActivityHiding,
      ActivityHiking,
      ActivityInACar,
      ActivityInAMeeting,
      ActivityInRealLife,
      ActivityJogging,
      ActivityOnABus,
      ActivityOnAPlane,
      ActivityOnATrain,
      ActivityOnATrip,
      ActivityOnThePhone,
      ActivityOnVacation,
      ActivityOnVideoPhone,
      ActivityOther,
      ActivityPartying,
      ActivityPlayingSports,
      ActivityPraying,
      ActivityReading,
      ActivityRehearsing,
      ActivityRunning,
      ActivityRunningAnErrand,
      ActivityScheduledHoliday,
      ActivityShaving,
      ActivityShopping,
      ActivitySkiing,
      ActivitySleeping,
      ActivitySmoking,
      ActivitySocializing,
      ActivityStudying,
      ActivitySunbathing,
      ActivitySwimming,
      ActivityTakingABath,
      ActivityTakingAShower,
      ActivityThinking,
      ActivityWalking,
      ActivityWalkingTheDog,
      ActivityWatchingAMovie,
      ActivityWatchingTv,
      ActivityWorkingOut,
      ActivityWriting,
      ActivityInvalidSpecificType
   };

public:
   CpcXepUserActivity(const CpcXepUserActivity& activity);
   CpcXepUserActivity(const gloox::Tag* tag=0);
   virtual ~CpcXepUserActivity();

   static const std::string XMLNS_USER_ACTIVITY;

   GeneralType generalType() const;
   void setGeneralType(GeneralType generalType);
   SpecificType specificType() const;
   void setSpecificType(SpecificType specificType);
   const std::string& text(const std::string& lang = gloox::EmptyString) const;
   void setText(const std::string& text, const std::string& lang = gloox::EmptyString);

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   GeneralType _generalType;
   SpecificType _specificType;
   gloox::StringMap _text;
};

#endif // CPC_XEP_USER_ACTIVITY_H__
