#pragma once
#ifndef __CPCAPI2_XMPPMULTIUSERCHATSYNCHANDLER_H__
#define __CPCAPI2_XMPPMULTIUSERCHATSYNCHANDLER_H__

namespace CPCAPI2
{
   namespace XmppMultiUserChat
   {
      /**
       * Marker interface to determine whether or not handler
       * should be called synchronously (used only for internal
       * handlers).
       */
      class XmppMultiUserChatSyncHandler
      {
      };
   }
}

#endif // __CPCAPI2_XMPPMULTIUSERCHATSYNCHANDLER_H__
