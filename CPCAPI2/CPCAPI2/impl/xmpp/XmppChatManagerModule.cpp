#include "brand_branded.h"

#include "interface/public/xmpp/XmppChatManager.h"

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)
#include "XmppChatManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)
template<>
void GetInterfaceImpl<XmppChat::XmppChatManagerInterface, PhoneInterface, PhoneInterface*>(XmppChat::XmppChatManagerInterface*& module, PhoneInterface* phone, const cpc::string& name, PhoneInterface* p0)
{
   module = dynamic_cast<XmppChat::XmppChatManagerInterface*>(phone->getInterfaceByName(name));
   if (module == NULL)
   {
      module = new XmppChat::XmppChatManagerInterface(phone);
      phone->registerInterface(name, module);
   }
}
#endif

namespace XmppChat
{

XmppChatManager* XmppChatManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppChatManagerInterface>(phone, "XmppChatManagerInterface");
#else
   return NULL;
#endif
}

}
}
