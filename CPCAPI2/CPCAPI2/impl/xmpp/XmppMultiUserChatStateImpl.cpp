#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

#include "XmppMultiUserChatStateImpl.h"
#include "XmppMultiUserChatManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "log/LocalLogger.h"

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT
#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

XmppMultiUserChatStateImpl::XmppMultiUserChatStateImpl(XmppMultiUserChatManagerInterface* intf)
   : mInterface(intf)
   , mLocalLogger(intf->phoneInterface()->localLogger())
{
   LocalDebugLog("XmppMultiUserChatStateImpl ctor");
   XmppAccount::XmppAccountManager* xmppAccount = XmppAccount::XmppAccountManager::getInterface(intf->phoneInterface());
   XmppAccount::XmppAccountInterface* xmppAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(xmppAccount);
   xmppAccountIf->addSdkObserver(this);
}

XmppMultiUserChatStateImpl::~XmppMultiUserChatStateImpl()
{
   mStateMap.clear();
   mAccountMap.clear();
   mServiceMap.clear();
}

void XmppMultiUserChatStateImpl::Release()
{
   delete this;
}

int XmppMultiUserChatStateImpl::getState(XmppMultiUserChatHandle handle, XmppMultiUserChatState& state)
{
   StackLog(<< "XmppMultiUserChatStateImpl::getState(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " state map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::getState(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return kError;
   }

   state = it->second;
   getServiceMappingForAccount(state.account, state.services);
   return kSuccess;
}

int XmppMultiUserChatStateImpl::getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppMultiUserChatStateInfo>& states)
{
   cpc::vector<XmppMultiUserChatHandle> mucs = getMultiUserChatHandles(account);
   StackLog(<< "XmppMultiUserChatStateImpl::getAllStatesForAccount(): " << this << " account: " << account << " mucs list size: " << mucs.size() << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());

   for (cpc::vector<XmppMultiUserChatHandle>::iterator it = mucs.begin(); it != mucs.end(); ++it)
   {
      XmppMultiUserChatStateInfo state;
      state.muc = *it;
      getState(*it, state.state);
      states.push_back(state);
   }

   return kSuccess;
}

int XmppMultiUserChatStateImpl::getAllStates(cpc::vector<XmppMultiUserChatStateInfo>& states)
{
   StackLog(<< "XmppMultiUserChatStateImpl::getAllStates(): " << this << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());

   for (StateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      XmppMultiUserChatStateInfo state;
      state.muc = i->first;
      state.state = i->second;
      getServiceMappingForAccount(state.state.account, state.state.services);
      states.push_back(state);
   }

   return kSuccess;
}

// XmppMultiUserChatHandlerInternal

int XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc, const XmppMultiUserChatCreatedResultEvent& args)
{
   LocalDebugLog("XmppMultiUserChatStateImpl::onCreateMultiUserChatResult() muc: {} account: {} account map size: {} muc map size: {}", muc, args.account, mAccountMap.size(), mStateMap.size());

   StackLog(<< "XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(): " << this << " muc: " << muc << " account: " << args.account << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   if ((mAccountMap.count(muc) != 0) && (mAccountMap[muc] != args.account))
   {
      LocalErrLog("XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(): mismatch in muc account map, muc: {} account: {} mapped muc: {}", muc, args.account, mAccountMap[muc]);
      DebugLog(<< "XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(): " << this << " mismatch in muc account map, muc: " << muc << " account: " << args.account << " mapped muc: " << mAccountMap[muc]);
      return kError;
   }

   mAccountMap[muc] = args.account;

   if (mStateMap.count(muc) != 0)
   {
      LocalErrLog("XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(): mismatch in muc state map, muc: {} already has existing states", muc);
      DebugLog(<< "XmppMultiUserChatStateImpl::onCreateMultiUserChatResult(): " << this << " mismatch in muc state map, muc: " << muc << " already has existing states");
   }

   mStateMap.erase(muc);
   XmppMultiUserChatState state;
   state.account = args.account;
   mStateMap.insert(std::make_pair(muc, state));

   return kSuccess;
}

void XmppMultiUserChatStateImpl::onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& evt)
{
   LocalDebugLog("XmppMultiUserChatStateImpl::onServiceAvailability for account {}", account);

   StackLog(<< "XmppMultiUserChatStateImpl::onServiceAvailability(): " << this << " account: " << account << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size() << " service: " << evt.service);
   ServiceMap::iterator i = mServiceMap.find(account);
   if (i == mServiceMap.end())
   {
      LocalDebugLog("XmppMultiUserChatStateImpl::onServiceAvailability for account {} new service mapping for: {}", account, evt.service);
      StackLog(<< "XmppMultiUserChatStateImpl::onServiceAvailability(): " << this << " account: " << account << " new service mapping for: " << evt.service);
      cpc::vector<ServiceAvailabilityEvent> services;
      services.push_back(evt);
      mServiceMap.insert(std::make_pair(account, services));
   }
   else
   {
      for (cpc::vector<ServiceAvailabilityEvent>::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         if ((*j).service == evt.service)
         {
            LocalDebugLog("XmppMultiUserChatStateImpl::onServiceAvailability for account {} update existing service mapping: {}", account, evt.service);
            StackLog(<< "XmppMultiUserChatStateImpl::onServiceAvailability(): " << this << " account: " << account << " update existing service mapping: " << evt.service);
            i->second.erase(j);
            break;
         }
      }
      i->second.push_back(evt);
   }

   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onRoomListRetrieved(XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onRoomListRetrieved(): " << this << " account: " << account << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onParticipantAdded(XmppMultiUserChatHandle handle, const ParticipantAddedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onParticipantAdded(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onParticipantAdded(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   mStateMap[handle].participants.push_back(evt.state);
}

void XmppMultiUserChatStateImpl::onParticipantRemoved(XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onParticipantRemoved(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onParticipantRemoved(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   for (cpc::vector<ParticipantState>::iterator p = it->second.participants.begin(), end = it->second.participants.end(); p != end; ++p)
   {
      if (p->nickname == evt.nickname)
      {
         it->second.participants.erase(p);
         return;
      }
   }
}

void XmppMultiUserChatStateImpl::onParticipantUpdated(XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onParticipantUpdated(): " << this << " muc: " << handle  << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onParticipantUpdated(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   for (cpc::vector<ParticipantState>::iterator p = it->second.participants.begin(), end = it->second.participants.end(); p != end; ++p)
   {
      if (p->nickname == evt.nickname)
      {
         *p = evt.state;
         return;
      }
   }
}

void XmppMultiUserChatStateImpl::onParticipantSelfUpdated(XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& evt)
{
   StackLog(<< "XmppMultiUserChatStateImpl::onParticipantSelfUpdated(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size() << " jid: " << evt.jid);
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onParticipantSelfUpdated(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   mStateMap[handle].self = evt.state;
}

void XmppMultiUserChatStateImpl::onMultiUserChatReady(XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& evt)
{
   StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatReady(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatReady(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   it->second.room.name = evt.room;
   it->second.room.isReady = true;
}

void XmppMultiUserChatStateImpl::onMultiUserChatSubjectChanged(XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatSubjectChanged(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatSubjectChanged(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   it->second.room.subject = evt.subject;
}

void XmppMultiUserChatStateImpl::onMultiUserChatNewMessage(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewMessage(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewMessage(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onMultiUserChatNewReaction(XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewReaction(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewReaction(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onMultiUserChatNewMessageRetraction(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageRetractionEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewMessageRetraction(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatNewMessageRetraction(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onSendMessageSuccess(XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onSendMessageSuccess(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onSendMessageSuccess(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onSendMessageFailure(XmppMultiUserChatHandle handle, const SendMessageFailureEvent& evt)
{
   StackLog(<< "XmppMultiUserChatStateImpl::onSendMessageFailure(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size() << " error on message: " << evt.message);
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onSendMessageFailure(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onParticipantChatStateReceived(XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onParticipantChatStateReceived(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onParticipantChatStateReceived(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onMultiUserChatInvitationReceived(XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatInvitationReceived(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatInvitationReceived(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onMultiUserChatInvitationDeclined(XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatInvitationDeclined(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatInvitationDeclined(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onMultiUserChatError(XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& evt)
{
   DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatError(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size() << " error type: " << evt.type << " error: " << evt.error);
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatError(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // bliu: TODO
}

void XmppMultiUserChatStateImpl::onLocalUserLeft(XmppMultiUserChatHandle handle, const LocalUserLeftEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onLocalUserLeft(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onLocalUserLeft(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   mStateMap.erase(handle);
   mAccountMap.erase(handle);
}

void XmppMultiUserChatStateImpl::onMultiUserChatConfigurationRequested(XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatConfigurationRequested(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatConfigurationRequested(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size() );
      return;
   }

   it->second.dataform = evt.dataform;
   it->second.configurations = evt.configurations; // DEPRECATED
}

void XmppMultiUserChatStateImpl::onMultiUserChatRoomStateChanged(XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatRoomStateChanged(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatRoomStateChanged(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }

   it->second.room = evt.state;
}

void XmppMultiUserChatStateImpl::onMultiUserChatListRequested(XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatListRequested(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onMultiUserChatListRequested(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // TODO
}

void XmppMultiUserChatStateImpl::onRoomBookmarksReceived(XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onRoomBookmarksReceived(): " << this << " account: " << account << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   if (getMucCount(account) == 0)
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onRoomBookmarksReceived(): " << this << " no muc mapping found for account: " << account);
      return;
   }
   // TODO
}

void XmppMultiUserChatStateImpl::onNewRoomHandle(XmppMultiUserChatHandle handle, const NewRoomEvent& evt)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onNewRoomHandle(): " << this << " muc: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
   StateMap::iterator it = mStateMap.find(handle);
   if (it == mStateMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::onNewRoomHandle(): " << this << " invalid muc handle: " << handle << " account map size: " << mAccountMap.size() << " muc map size: " << mStateMap.size());
      return;
   }
   // TODO
}

int XmppMultiUserChatStateImpl::onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args)
{
   return kSuccess;
}

int XmppMultiUserChatStateImpl::onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
   // StackLog(<< "XmppMultiUserChatStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " account-list: " << mAccountMap.size());
   if (args.accountStatus == XmppAccount::XmppAccountStatusChangedEvent::Status_Destroyed)
   {
      for (AccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); ++i)
      {
         if ((i->second) == account)
         {
            DebugLog(<< "XmppMultiUserChatStateImpl::onAccountStatusChanged(): " << this << " account: " << account << " destroyed with muc: " << i->first << " before deletion account-list: " << mStateMap.size());
            mServiceMap.erase(account);
            mStateMap.erase(i->first);
            mAccountMap.erase(i);
            break;
         }
      }
   }

   return kSuccess;
}

int XmppMultiUserChatStateImpl::onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args)
{
   return kSuccess;
}

int XmppMultiUserChatStateImpl::onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args)
{
   return kSuccess;
}

int XmppMultiUserChatStateImpl::onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args)
{
   return kSuccess;
}

CPCAPI2::XmppAccount::XmppAccountHandle XmppMultiUserChatStateImpl::getAccountHandle(XmppMultiUserChatHandle muc)
{
   AccountMap::iterator i = mAccountMap.find(muc);
   if (i == mAccountMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::getAccountHandle(): " << this << " no account mapping found for muc: " << muc);
      return 0;
   }

   CPCAPI2::XmppAccount::XmppAccountHandle account = i->second;
   return account;
}

cpc::vector<XmppMultiUserChatHandle> XmppMultiUserChatStateImpl::getMultiUserChatHandles(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   cpc::vector<XmppMultiUserChatHandle> mucs;
   for (AccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); i++)
   {
      if (i->second == account)
      {
         // StackLog(<< "XmppMultiUserChatStateImpl::getMultiUserChatHandles(): " << this << " account: " << account << " is mapped to muc: " << i->first);
         mucs.push_back(i->first);
      }
   }

   if (mucs.size() == 0)
   {
      LocalDebugLog("XmppMultiUserChatStateImpl::getMultiUserChatHandles() no muc mapping found for account: {}", account);
      DebugLog(<< "XmppMultiUserChatStateImpl::getMultiUserChatHandles(): " << this << " no muc mapping found for account: " << account);
   }

   return mucs;
}

int XmppMultiUserChatStateImpl::getServiceMappingForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<ServiceAvailabilityEvent>& services)
{
   ServiceMap::iterator i = mServiceMap.find(account);
   if (i == mServiceMap.end())
   {
      DebugLog(<< "XmppMultiUserChatStateImpl::getServiceMappingForAccount(): " << this << " no service mapping found for account: " << account);
   }
   else
   {
      services = i->second;
   }
   return services.size();
}

int XmppMultiUserChatStateImpl::getMucCount(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   int mucCount = 0;
   for (AccountMap::iterator i = mAccountMap.begin(); i != mAccountMap.end(); i++)
   {
      if (i->second == account)
      {
         mucCount++;
      }
   }

   return mucCount;
}

int XmppMultiUserChatStateImpl::getStateCount()
{
   return mStateMap.size();
}

int XmppMultiUserChatStateImpl::getServiceCount()
{
   return mServiceMap.size();
}

}

}

#endif
