#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)

#include "XmppVCardTypesInternal.h"
#include "cpcapi2utils.h"

#include <sstream>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD

using namespace CPCAPI2;
using namespace CPCAPI2::XmppVCard;

cpc::string BoolStr(bool status)
{
   return (status  ? "true" : "false");
}

namespace CPCAPI2
{

namespace XmppVCard
{

cpc::string get_debug_string(const XmppVCardDetail::AddressType& type)
{
   switch (type)
   {
      case XmppVCardDetail::AddrTypeHome: return "AddrTypeHome";
      case XmppVCardDetail::AddrTypeWork: return "AddrTypeWork";
      case XmppVCardDetail::AddrTypePref: return "AddrTypePref";
      case XmppVCardDetail::AddrTypeX400: return "AddrTypeX400";
      case XmppVCardDetail::AddrTypeInet: return "AddrTypeInet";
      case XmppVCardDetail::AddrTypeParcel: return "AddrTypeParcel";
      case XmppVCardDetail::AddrTypePostal: return "AddrTypePostal";
      case XmppVCardDetail::AddrTypeDom: return "AddrTypeDom";
      case XmppVCardDetail::AddrTypeIntl: return "AddrTypeIntl";
      case XmppVCardDetail::AddrTypeVoice: return "AddrTypeVoice";
      case XmppVCardDetail::AddrTypeFax: return "AddrTypeFax";
      case XmppVCardDetail::AddrTypePager: return "AddrTypePager";
      case XmppVCardDetail::AddrTypeMsg: return "AddrTypeMsg";
      case XmppVCardDetail::AddrTypeCell: return "AddrTypeCell";
      case XmppVCardDetail::AddrTypeVideo: return "AddrTypeVideo";
      case XmppVCardDetail::AddrTypeBbs: return "AddrTypeBbs";
      case XmppVCardDetail::AddrTypeModem: return "AddrTypeModem";
      case XmppVCardDetail::AddrTypeIsdn: return "AddrTypeIsdn";
      case XmppVCardDetail::AddrTypePcs: return "AddrTypePcs";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const XmppVCardDetail::Name& name)
{
   std::stringstream ss;
   ss << "family: " << name.family << " given: " << name.given << " middle: " << name.middle << " prefix: " << name.prefix << " suffix: " << name.suffix;
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::VCardClassification& classification)
{
   switch (classification)
   {
      case XmppVCardDetail::ClassNone: return "ClassNone";
      case XmppVCardDetail::ClassPublic: return "ClassPublic";
      case XmppVCardDetail::ClassPrivate: return "ClassPrivate";
      case XmppVCardDetail::ClassConfidential: return "ClassConfidential";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const XmppVCardDetail::Email& email)
{
   std::stringstream ss;
   ss << "userid: " << email.userid << " home: " << BoolStr(email.home) << " work: " << BoolStr(email.work) << " internet: " << BoolStr(email.internet) << " pref: " << BoolStr(email.pref) << " x400: " << BoolStr(email.x400);
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Telephone& telephone)
{
   std::stringstream ss;
   ss << "number: " << telephone.number << " home: " << BoolStr(telephone.home) << " work: " << BoolStr(telephone.work) << " voice: " << BoolStr(telephone.voice) << " fax: " << BoolStr(telephone.fax) << " pager: " << BoolStr(telephone.pager) << " msg: " << BoolStr(telephone.msg) << " cell: " << BoolStr(telephone.cell) << " video: " << BoolStr(telephone.video) << " bbs: " << BoolStr(telephone.bbs) << " modem: " << BoolStr(telephone.modem) << " isdn: " << BoolStr(telephone.isdn) << " pcs: " << BoolStr(telephone.pcs) << " pref: " << BoolStr(telephone.pref);
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Address& address)
{
   std::stringstream ss;
   ss << "pobox: " << address.pobox << " extadd: " << address.extadd << " street: " << address.street << " locality: " << address.locality << " region: " << address.region << " pcode: " << address.pcode << " ctry: " << address.ctry << " home: " << BoolStr(address.home) << " work: " << BoolStr(address.work) << " postal: " << BoolStr(address.postal) << " parcel: " << BoolStr(address.parcel) << " pref: " << BoolStr(address.pref) << " dom: " << BoolStr(address.dom) << " intl: " << BoolStr(address.intl);
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Label& label)
{
   std::stringstream ss;
   ss << " home: " << BoolStr(label.home) << " work: " << BoolStr(label.work) << " postal: " << BoolStr(label.postal) << " parcel: " << BoolStr(label.parcel) << " pref: " << BoolStr(label.pref) << " dom: " << BoolStr(label.dom) << " intl: " << BoolStr(label.intl) << " line-count: " << label.lines.size();
   for (cpc::vector<cpc::string>::const_iterator i = label.lines.begin(); i != label.lines.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Geo& geo)
{
   std::stringstream ss;
   ss << "latitude: " << geo.latitude << " longitude: " << geo.longitude;
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Organization& organization)
{
   std::stringstream ss;
   ss << "name: " << organization.name << " unit-count: " << organization.units.size();
   for (cpc::vector<cpc::string>::const_iterator i = organization.units.begin(); i != organization.units.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::Photo& photo)
{
   std::stringstream ss;
   ss << "extval: " << photo.extval << " type: " << photo.type << " binval-size: " << photo.binval.size() << " bytes";
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::EmailList& list)
{
   std::stringstream ss;
   ss << "email-count: " << list.size();
   for (XmppVCardDetail::EmailList::const_iterator i = list.begin(); i != list.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::TelephoneList& list)
{
   std::stringstream ss;
   ss << "telephone-count: " << list.size();
   for (XmppVCardDetail::TelephoneList::const_iterator i = list.begin(); i != list.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::AddressList& list)
{
   std::stringstream ss;
   ss << "address-count: " << list.size();
   for (XmppVCardDetail::AddressList::const_iterator i = list.begin(); i != list.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail::LabelList& list)
{
   std::stringstream ss;
   ss << "label-count: " << list.size();
   for (XmppVCardDetail::LabelList::const_iterator i = list.begin(); i != list.end(); i++)
   {
      ss << " {" << get_debug_string(*i) << "}";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const XmppVCardDetail& detail)
{
   std::stringstream ss;
   ss << "emailList: " << get_debug_string(detail.emailList)
      << " telephoneList: " << get_debug_string(detail.telephoneList)
      << " addressList: " << get_debug_string(detail.addressList)
      << " labelList: " << get_debug_string(detail.labelList)
      << " name: " << get_debug_string(detail.name)
      << " geo: " << get_debug_string(detail.geo)
      << " organization: " << get_debug_string(detail.organization)
      << " photo: " << get_debug_string(detail.photo)
      << " logo: " << get_debug_string(detail.logo)
      << " classification: " << get_debug_string(detail.classification)
      << " formattedname: " << detail.formattedname
      << " nickname: " << detail.nickname
      << " url: " << detail.url
      << " birthday: " << detail.birthday
      << " jid: " << detail.jid
      << " title: " << detail.title
      << " role: " << detail.role
      << " note: " << detail.note
      << " desc: " << detail.desc
      << " mailer: " << detail.mailer
      << " timezone: " << detail.timezone
      << " product: " << detail.product
      << " revision: " << detail.revision
      << " sortstring: " << detail.sortstring
      << " phonetic: " << detail.phonetic
      << " cpcollab: " << detail.cpcollab
      << " cpsoftphone: " << detail.cpsoftphone
      << " cpsoftphone_pref: " << detail.cpsoftphone_pref
      << " uid: " << detail.uid;

   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::AddressType& type)
{
   os << get_debug_string(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Name& name)
{
   os << get_debug_string(name);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::VCardClassification& classification)
{
   os << get_debug_string(classification);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Email& email)
{
   os << get_debug_string(email);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Telephone& telephone)
{
   os << get_debug_string(telephone);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Address& address)
{
   os << get_debug_string(address);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Label& label)
{
   os << get_debug_string(label);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Geo& geo)
{
   os << get_debug_string(geo);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Organization& organization)
{
   os << get_debug_string(organization);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Photo& photo)
{
   os << get_debug_string(photo);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::EmailList& list)
{
   os << get_debug_string(list);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::TelephoneList& list)
{
   os << get_debug_string(list);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::AddressList& list)
{
   os << get_debug_string(list);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::LabelList& list)
{
   os << get_debug_string(list);
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail& detail)
{
   os << get_debug_string(detail);
   return os;
}

}

}

#endif
