#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppPushManager.h"

#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)
#include "XmppPushManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

namespace XmppPush
{

XmppPushManager* XmppPushManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppPushManagerInterface>(phone, "XmppPushManagerInterface");
#else
   return NULL;
#endif
}

}
}
