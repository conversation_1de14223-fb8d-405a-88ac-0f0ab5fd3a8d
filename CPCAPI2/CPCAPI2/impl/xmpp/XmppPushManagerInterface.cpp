#include "brand_branded.h"
#if (CPCAPI2_BRAND_XMPP_PUSH_MODULE == 1)

#include <algorithm>
#include <string>
#include <sstream>

#include "XmppPushManagerInterface.h"
#include "XmppPushManagerImpl.h"

#include "phone/Phone.h"
#include "phone/PhoneInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountImpl.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_PUSH

namespace CPCAPI2
{

namespace XmppPush
{

XmppPushManagerInterface::XmppPushManagerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mAccountIf(dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone)))
{
}

XmppPushManagerInterface::~XmppPushManagerInterface()
{
}

std::shared_ptr<XmppPushManagerImpl> XmppPushManagerInterface::getXmppPushManagerImpl(XmppAccount::XmppAccountHandle account)
{
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return NULL;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppPushManagerImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      cpc::string msg = cpc::string("SetHandler() not called: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
   }

   return impl;
}

void XmppPushManagerInterface::getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts)
{
   for (ImplMap::const_iterator i = mImplMap.begin(); i != mImplMap.end(); ++i)
   {
      accounts.push_back(i->first);
   }
}

void XmppPushManagerInterface::PreRelease()
{
   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppPushManagerImpl> push = getImpl(h))
      {
         mImplMap.erase(h);
      }
   }
}

bool XmppPushManagerInterface::PreReleaseCompleted()
{
   return (mImplMap.size() == 0);
}

void XmppPushManagerInterface::Release()
{
   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppPushManagerImpl> push = getImpl(h))
      {
         mImplMap.erase(h);
      }
   }

   delete this;
}

int XmppPushManagerInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppPushHandler* handler)
{
   StackLog(<< "XmppPushManagerInterface::setHandler(): xmpp account: " << account << " handler: " << handler);
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppPushManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      StackLog(<< "XmppPushManagerInterface::setHandler(): calling post for xmpp account: " << account << " handler: " << handler);
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppPushManagerInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppPushHandler* handler)
{
   StackLog(<< "XmppPushManagerInterface::setHandlerImpl(): xmpp account: " << account << " handler: " << handler);

   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      cpc::string msg = cpc::string("XmppPushManagerInterface::setHandler called with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppPushManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      StackLog(<< "XmppPushManagerInterface::setHandlerImpl(): xmpp account: " << account << " creating new XmppChatManagerImpl");
      // Create a new manager impl
      impl = std::make_shared<XmppPushManagerImpl>(mPhone, *acct, *this); // Destroyed in the destructor of this class

      // Keep mapping account -> manager impl
      mImplMap[account] = impl;
   }

   // Register the handler
   StackLog(<< "XmppPushManagerInterface::setHandlerImpl(): xmpp account: " << account << " registering the handler: " << handler);
   impl->setHandler(handler);
}

int XmppPushManagerInterface::configurePushSettings(XmppAccount::XmppAccountHandle account, const XmppPushSettings& settings)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::configurePushSettingsImpl, this, account, settings));
   return kSuccess;
}

void XmppPushManagerInterface::configurePushSettingsImpl(XmppAccount::XmppAccountHandle account, const XmppPushSettings& settings)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->configurePushSettings(settings);
   }
}

int XmppPushManagerInterface::applyPushSettings(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::applyPushSettingsImpl, this, account));
   return kSuccess;
}

void XmppPushManagerInterface::applyPushSettingsImpl(XmppAccount::XmppAccountHandle account)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->applyPushSettings();
   }
}

int XmppPushManagerInterface::pushRegister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::pushRegisterImpl, this, account, deviceToken));
   return kSuccess;
}

void XmppPushManagerInterface::pushRegisterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->pushRegister(deviceToken);
   }
}

int XmppPushManagerInterface::pushUnregister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::pushUnregisterImpl, this, account, deviceToken));
   return kSuccess;
}

void XmppPushManagerInterface::pushUnregisterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->pushUnregister(deviceToken);
   }
}

int XmppPushManagerInterface::setIgnoreUnknownSender(XmppAccount::XmppAccountHandle account, bool ignoreUnknown)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::setIgnoreUnknownSenderImpl, this, account, ignoreUnknown));
   return kSuccess;
}

void XmppPushManagerInterface::setIgnoreUnknownSenderImpl(XmppAccount::XmppAccountHandle account, bool ignoreUnknown)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->setIgnoreUnknownSender(ignoreUnknown);
   }
}

int XmppPushManagerInterface::setSendNotificationsWhileAway(XmppAccount::XmppAccountHandle account, bool sendWhileAway)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::setSendNotificationsWhileAwayImpl, this, account, sendWhileAway));
   return kSuccess;
}

void XmppPushManagerInterface::setSendNotificationsWhileAwayImpl(XmppAccount::XmppAccountHandle account, bool sendWhileAway)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->setSendNotificationsWhileAway(sendWhileAway);
   }
}

int XmppPushManagerInterface::setContactMuted(XmppAccount::XmppAccountHandle account, const cpc::string& contactJid, bool muted)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::setContactMutedImpl, this, account, contactJid, muted));
   return kSuccess;
}

void XmppPushManagerInterface::setContactMutedImpl(XmppAccount::XmppAccountHandle account, const cpc::string& contactJid, bool muted)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->setContactMuted(contactJid, muted);
   }
}

int XmppPushManagerInterface::setGroupChatFilter(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::setGroupChatFilterImpl, this, account, roomJid, rule, nick));
   return kSuccess;
}

void XmppPushManagerInterface::setGroupChatFilterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->setGroupChatFilter(roomJid, rule, nick);
   }
}

int XmppPushManagerInterface::enableNotifications(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::enableNotificationsImpl, this, account));
   return kSuccess;
}

void XmppPushManagerInterface::enableNotificationsImpl(XmppAccount::XmppAccountHandle account)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->enableNotifications(true);
   }
}

int XmppPushManagerInterface::disableNotifications(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::disableNotificationsImpl, this, account));
   return kSuccess;
}

void XmppPushManagerInterface::disableNotificationsImpl(XmppAccount::XmppAccountHandle account)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->enableNotifications(false);
   }
}

int XmppPushManagerInterface::registerMucOfflineMessageDelivery(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, const cpc::string& nick)
{
   mAccountIf->post(resip::resip_bind(&XmppPushManagerInterface::registerOfflineMessageDeliveryImpl, this, account, roomJid, nick));
   return kSuccess;
}

void XmppPushManagerInterface::registerOfflineMessageDeliveryImpl(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, const cpc::string& nick)
{
   if (std::shared_ptr<XmppPushManagerImpl> impl= getXmppPushManagerImpl(account))
   {
      impl->registerOfflineMessageDelivery(roomJid, nick);
   }
}

}
}

#endif //CPCAPI2_BRAND_XMPP_PUSH_MODULE
