#include "brand_branded.h"

#include "xmpp/XmppCannedPresence.h"

namespace CPCAPI2
{
namespace XmppRoster
{

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1)
// file scope declaration
static void fromResourceItem(const CPCAPI2::XmppRoster::ResourceItem &resourceItem, XmppCannedPresence &briaPresence);
static CPCAPI2::XmppRoster::PresenceType toPresenceType(XmppCannedStatus status);
static void toUserActivity(XmppCannedStatus status,
         CPCAPI2::XmppRoster::UserActivityGeneralType &userActivityGeneralType,
         CPCAPI2::XmppRoster::UserActivitySpecificType &userActivitySpecificType,
         cpc::string &userActivityText);
static int toXmppCannedStatusPrecedence(const CPCAPI2::XmppRoster::ResourceItem &resourceItem);
static int compareResourcePrecendence(const CPCAPI2::XmppRoster::ResourceItem& lhs, const CPCAPI2::XmppRoster::ResourceItem &rhs);
static cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator findHighestPrecedenceResource(const cpc::vector<CPCAPI2::XmppRoster::ResourceItem> &resources);

//
XmppCannedPresence::XmppCannedPresence(const CPCAPI2::XmppRoster::RosterItem &rosterItem)
{
   cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator it = findHighestPrecedenceResource(rosterItem.resources);
   if (it != rosterItem.resources.end())
   {
      fromResourceItem(*it, *this);
   }
   else
   {
      status = XmppCannedStatus_Offline;
      priority = -1;
   }
}

void XmppCannedPresence::cannedStatusToPresence(
   XmppCannedStatus status,
   CPCAPI2::XmppRoster::PresenceType &presenceType,
   CPCAPI2::XmppRoster::UserActivityGeneralType &userActivityGeneralType,
   CPCAPI2::XmppRoster::UserActivitySpecificType &userActivitySpecificType,
   cpc::string &userActivityText)
{
   presenceType = toPresenceType(status);
   toUserActivity(status, userActivityGeneralType, userActivitySpecificType, userActivityText);
}

// file scope definition
static void fromResourceItem(const CPCAPI2::XmppRoster::ResourceItem &resourceItem, XmppCannedPresence &briaPresence)
{
   briaPresence.resource = resourceItem.resource;
   briaPresence.priority = resourceItem.priority;
   briaPresence.note = resourceItem.presenceStatusText;

   switch (resourceItem.presenceType)
   {
      case CPCAPI2::XmppRoster::PresenceType_Available:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Available;
         break;
      case CPCAPI2::XmppRoster::PresenceType_Chat:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Chat;
         break;
      case CPCAPI2::XmppRoster::PresenceType_Away:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Away;
         break;
      case CPCAPI2::XmppRoster::PresenceType_DND:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_DoNotDisturb;
         break;
      case CPCAPI2::XmppRoster::PresenceType_XA:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Away;
         break;
      case CPCAPI2::XmppRoster::PresenceType_Unavailable:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Offline;
         break;
      default:
         briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Invalid;
         break;
   }

   if (resourceItem.userActivityGeneralType == CPCAPI2::XmppRoster::ActivityInactive && resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityHiding)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_AppearAway;
   else if (resourceItem.userActivityGeneralType == CPCAPI2::XmppRoster::ActivityWorking && resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityOther)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_Busy;
   else if (resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityHavingLunch)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch;
   else if (resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityOnVacation)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_OnVacation;
   else if (resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityScheduledHoliday)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_ScheduledHoliday;
   else if (resourceItem.userActivityGeneralType == CPCAPI2::XmppRoster::ActivityInactive && resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityOther)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_InactiveOther;
   else if (resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityOnThePhone)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone;
   else if (resourceItem.userActivityGeneralType == CPCAPI2::XmppRoster::ActivityUndefinedGeneralType && resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityOther)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_NotAvailableForCalls;
   else if (resourceItem.presenceType == CPCAPI2::XmppRoster::PresenceType_XA && resourceItem.userActivityGeneralType == CPCAPI2::XmppRoster::ActivityWorking && resourceItem.userActivitySpecificType == CPCAPI2::XmppRoster::ActivityHiding)
      briaPresence.status = CPCAPI2::XmppRoster::XmppCannedStatus_AppearOffline;
}

static CPCAPI2::XmppRoster::PresenceType toPresenceType(XmppCannedStatus status)
{
   switch(status)
   {
      case CPCAPI2::XmppRoster::XmppCannedStatus_Available:
         return CPCAPI2::XmppRoster::PresenceType_Available;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Chat:
         return CPCAPI2::XmppRoster::PresenceType_Chat;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Away:
      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearAway:
         return CPCAPI2::XmppRoster::PresenceType_Away;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Busy:
         return CPCAPI2::XmppRoster::PresenceType_DND;

      case CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch:
         return CPCAPI2::XmppRoster::PresenceType_Away;

      case CPCAPI2::XmppRoster::XmppCannedStatus_OnVacation:
         return CPCAPI2::XmppRoster::PresenceType_Away;

      case CPCAPI2::XmppRoster::XmppCannedStatus_ScheduledHoliday:
         return CPCAPI2::XmppRoster::PresenceType_Away;

      case CPCAPI2::XmppRoster::XmppCannedStatus_InactiveOther:
         return CPCAPI2::XmppRoster::PresenceType_Away;

      case CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone:
         return CPCAPI2::XmppRoster::PresenceType_DND;

      case CPCAPI2::XmppRoster::XmppCannedStatus_DoNotDisturb:
         return CPCAPI2::XmppRoster::PresenceType_DND;

      case CPCAPI2::XmppRoster::XmppCannedStatus_NotAvailableForCalls:
         return CPCAPI2::XmppRoster::PresenceType_DND;

      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearOffline:
         return CPCAPI2::XmppRoster::PresenceType_XA;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Offline:
         return CPCAPI2::XmppRoster::PresenceType_Unavailable;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Invalid:
      default:
         return CPCAPI2::XmppRoster::PresenceType_Unavailable;
    }
}

static void toUserActivity(
   XmppCannedStatus status,
   CPCAPI2::XmppRoster::UserActivityGeneralType &userActivityGeneralType,
   CPCAPI2::XmppRoster::UserActivitySpecificType &userActivitySpecificType,
   cpc::string &userActivityText)
{
   userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInvalidGeneralType;
   userActivitySpecificType = CPCAPI2::XmppRoster::ActivityInvalidSpecificType;
   userActivityText = "";

   switch(status)
   {
      case CPCAPI2::XmppRoster::XmppCannedStatus_Available:
         // Does not have user activity
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Chat:
         // Does not have user activity
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Away:
         // Does not have user activity
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearAway:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInactive;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityHiding;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Busy:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityWorking;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityOther;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInactive;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityHavingLunch;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_OnVacation:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInactive;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityOnVacation;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_ScheduledHoliday:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInactive;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityScheduledHoliday;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_InactiveOther:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInactive;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityOther;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityTalking;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityOnThePhone;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_DoNotDisturb:
         // Does not have user activity
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_NotAvailableForCalls:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityUndefinedGeneralType;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityOther;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearOffline:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityWorking;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityHiding;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Offline:
         userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInvalidGeneralType;
         userActivitySpecificType = CPCAPI2::XmppRoster::ActivityInvalidSpecificType;
         break;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Invalid:
      default:
         break;
   }
}

static int toXmppCannedStatusPrecedence(const CPCAPI2::XmppRoster::ResourceItem &resourceItem)
{
   CPCAPI2::XmppRoster::XmppCannedPresence presence;
   fromResourceItem(resourceItem, presence);

    switch(presence.status)
    {
      case CPCAPI2::XmppRoster::XmppCannedStatus_Available:
        return resourceItem.isCiscoRichPresence ? 99 : 100;
      case CPCAPI2::XmppRoster::XmppCannedStatus_Chat:
        return 110;
      case CPCAPI2::XmppRoster::XmppCannedStatus_Away:
        return resourceItem.isCiscoRichPresence ? 49 : 50;
      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearAway:
        return 200;
      case CPCAPI2::XmppRoster::XmppCannedStatus_Busy:
        return 500;
      case CPCAPI2::XmppRoster::XmppCannedStatus_HavingLunch:
        return 210;
      case CPCAPI2::XmppRoster::XmppCannedStatus_OnVacation:
        return 220;
      case CPCAPI2::XmppRoster::XmppCannedStatus_ScheduledHoliday:
        return 230;
      case CPCAPI2::XmppRoster::XmppCannedStatus_InactiveOther:
        return 299;
      case CPCAPI2::XmppRoster::XmppCannedStatus_OnThePhone:
        return 700;
      case CPCAPI2::XmppRoster::XmppCannedStatus_DoNotDisturb:
        return resourceItem.isCiscoRichPresence ? 499 : 900;
      case CPCAPI2::XmppRoster::XmppCannedStatus_NotAvailableForCalls:
        return 800;
      case CPCAPI2::XmppRoster::XmppCannedStatus_AppearOffline:
        return 1000;
      case CPCAPI2::XmppRoster::XmppCannedStatus_Offline:
        return 0;

      case CPCAPI2::XmppRoster::XmppCannedStatus_Invalid:
      default:
        return 1;
    }
}

static int compareResourcePrecendence(const CPCAPI2::XmppRoster::ResourceItem& lhs, const CPCAPI2::XmppRoster::ResourceItem &rhs)
{
   // First compare XMPP resource priority
   if (!lhs.isCiscoRichPresence && !rhs.isCiscoRichPresence)
   {
      if (lhs.priority > rhs.priority)
         return -1;
      else if (rhs.priority > lhs.priority)
         return 1;
   }

   // Next compare Canned Presence Model precedence
   int lhsp = toXmppCannedStatusPrecedence(lhs);
   int rhsp = toXmppCannedStatusPrecedence(rhs);

   if (lhsp > rhsp)
      return -1;
   else if (rhsp > lhsp)
      return 1;

   if (lhs.presenceStatusText < rhs.presenceStatusText)
      return 1;
   else if (rhs.presenceStatusText < lhs.presenceStatusText)
      return -1;

   return 0;
}

static cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator findHighestPrecedenceResource(const cpc::vector<CPCAPI2::XmppRoster::ResourceItem> &resources)
{
   if (resources.empty()) return resources.end();

   cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator it = resources.begin();
   cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator itHighest = resources.end();
   cpc::vector<CPCAPI2::XmppRoster::ResourceItem>::const_iterator itCiscoRichPresence = resources.end();
   for (; it != resources.end(); ++it)
   {
      // take out Cisco Rich Presence for final precedence comparison
      if ((*it).isCiscoRichPresence)
      {
         itCiscoRichPresence = it;
         continue;
      }

      if (itHighest == resources.end())
      {
         itHighest = it;
         continue;
      }

      if (compareResourcePrecendence(*itHighest, *it) > 0)
         itHighest = it;
   }

   if (itCiscoRichPresence != resources.end())
   {
      if (itHighest == resources.end() || compareResourcePrecendence(*itHighest, *itCiscoRichPresence) > 0)
         itHighest = itCiscoRichPresence;
   }

   return itHighest;
}
#else // CPCAPI2_BRAND_XMPP_ROSTER_MODULE
// Stub out logic if Canned Presence Module is not enabled
XmppCannedPresence::XmppCannedPresence(const CPCAPI2::XmppRoster::RosterItem &rosterItem)
{
}
void XmppCannedPresence::cannedStatusToPresence(
   XmppCannedStatus status,
   CPCAPI2::XmppRoster::PresenceType &presenceType,
   CPCAPI2::XmppRoster::UserActivityGeneralType &userActivityGeneralType,
   CPCAPI2::XmppRoster::UserActivitySpecificType &userActivitySpecificType,
   cpc::string &userActivityText)
{
   presenceType = CPCAPI2::XmppRoster::PresenceType_Unavailable;
   userActivityGeneralType = CPCAPI2::XmppRoster::ActivityInvalidGeneralType;
   userActivitySpecificType = CPCAPI2::XmppRoster::ActivityInvalidSpecificType;
   userActivityText = "";
}
#endif // CPCAPI2_BRAND_XMPP_ROSTER_MODULE

} // namespace XmppRoster
} // namespace CPCAPI2
