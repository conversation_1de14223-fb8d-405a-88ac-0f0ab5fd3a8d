#include "brand_branded.h"

#include "interface/public/xmpp/XmppVCardManager.h"
#include "interface/public/xmpp/XmppVCardState.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)
#include "XmppVCardManagerInterface.h"
#include "XmppVCardStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace XmppVCard
{

XmppVCardManager* XmppVCardManager::getInterface( CPCAPI2::Phone* cpcPhone )
{
#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppVCardManagerInterface>(phone, "XmppVCardManagerInterface");
#else
   return NULL;
#endif
}

XmppVCardStateManager* XmppVCardStateManager::getInterface(XmppVCardManager* manager)
{
#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)
   XmppVCardManagerInterface* parent = dynamic_cast<XmppVCardManagerInterface*>(manager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<XmppVCardStateImpl>(phone, "XmppVCardStateManager", parent);
#else
   return NULL;
#endif
}

}
}
