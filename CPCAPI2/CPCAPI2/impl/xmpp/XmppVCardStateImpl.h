#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_STATE_IMPL_H)
#define CPCAPI2_XMPP_VCARD_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "xmpp/XmppVCardManager.h"
#include "xmpp/XmppVCardState.h"
#include "XmppVCardHandlerInternal.h"
#include "XmppAccountHandlerInternal.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{

namespace XmppVCard
{

class XmppVCardManagerInterface;

class XmppVCardStateImpl
   : public XmppVCardStateManager
   , public XmppVCardHandlerInternal
   , public XmppAccount::XmppAccountHandlerInternal
   , public PhoneModule
{

public:

   typedef std::map<cpc::string, XmppVCardState> VCardMap;
   typedef std::map<XmppVCardHandle, VCardMap> StateMap;
   typedef std::map<XmppVCardHandle, CPCAPI2::XmppAccount::XmppAccountHandle> AccountMap;

   XmppVCardStateImpl(XmppVCardManagerInterface* intf);
   virtual ~XmppVCardStateImpl();

   // Inherited via XmppVCardHandler
   virtual int getState(XmppVCardHandle handle, const cpc::string& jid, XmppVCardState& state) OVERRIDE;
   virtual int getAllStates(XmppVCardHandle handle, cpc::vector<XmppVCardStateInfo>& states) OVERRIDE;
   virtual int getAllStatesForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account, cpc::vector<XmppVCardStateInfo>& states) OVERRIDE;
   virtual int getAllStateInfo(cpc::vector<XmppVCardStateInfo>& states) OVERRIDE;
   virtual void Release() OVERRIDE;

   // Inherited via XmppVCardHandlerInternal
   virtual int onCreateVCardResult(CPCAPI2::XmppVCard::XmppVCardHandle vcard, const XmppVCardCreatedResultEvent& args) OVERRIDE;

   // Inherited via XmppVCardHandler
   virtual int onVCardFetched(XmppVCardHandle handle, const VCardFetchedEvent& evt) OVERRIDE;
   virtual int onVCardOperationResult(XmppVCardHandle handle, const VCardOperationResultEvent& evt) OVERRIDE;
   virtual int onError(XmppVCardHandle handle, const ErrorEvent& evt) OVERRIDE;

   // Inherited via XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;

   // Inherited via XmppAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE;
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const XmppAccount::EntityFeatureEvent& args) OVERRIDE;

private:

   CPCAPI2::XmppAccount::XmppAccountHandle getAccountHandle(XmppVCardHandle vcard);
   XmppVCardHandle getVCardHandle(CPCAPI2::XmppAccount::XmppAccountHandle account);
   int getStateCount();

   StateMap mStateMap;
   AccountMap mAccountMap;
   XmppVCardManagerInterface* mInterface;

};

std::ostream& operator<<(std::ostream& os, const XmppVCardState& state);
std::ostream& operator<<(std::ostream& os, const XmppVCardStateInfo& state);

}

}

#endif // CPCAPI2_XMPP_VCARD_STATE_IMPL_H
