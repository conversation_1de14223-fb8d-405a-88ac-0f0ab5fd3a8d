#pragma once
#ifndef __CPCAPI2_XMPPCHATSYNCHANDLER_H__
#define __CPCAPI2_XMPPCHATSYNCHANDLER_H__

namespace CPCAPI2
{
   namespace XmppChat
   {
      /**
       * Marker interface to determine whether or not handler
       * should be called synchronously (used only for internal
       * handlers).
       */
      class XmppChatSyncHandler
      {
      };
   }
}

#endif // __CPCAPI2_XMPPCHATSYNCHANDLER_H__