#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppVCardJsonProxyInterface.h"
#include "xmpp/XmppVCardTypesInternal.h"
#include "XmppAccountJsonProxyInterface.h"
#include "xmpp/XmppVCardManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"

#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD
#define JSON_MODULE "XmppVCardJsonApi"

namespace CPCAPI2
{

namespace XmppVCard
{

XmppVCardJsonProxyInterface::XmppVCardJsonProxyInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mTransport(NULL),
mStateHandler(NULL),
mHandlePrefix(0)
{
   mFunctionMap["onCreateResult"] = std::bind(&XmppVCardJsonProxyInterface::handleCreateResult, this, std::placeholders::_1);
   mFunctionMap["onVCardFetched"] = std::bind(&XmppVCardJsonProxyInterface::handleVCardFetched, this, std::placeholders::_1);
   mFunctionMap["onVCardOperationResult"] = std::bind(&XmppVCardJsonProxyInterface::handleVCardOperationResult, this, std::placeholders::_1);
   mFunctionMap["onError"] = std::bind(&XmppVCardJsonProxyInterface::handleError, this, std::placeholders::_1);
   mFunctionMap["onVCardState"] = std::bind(&XmppVCardJsonProxyInterface::handleVCardState, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mHandlePrefix = resip::Random::getCryptoRandom();
   while ((mHandlePrefix & 0xFFFF0000) == 0)
   {
      mHandlePrefix = (mHandlePrefix + 1) << 1;
   }
   mHandlePrefix = (mHandlePrefix & 0xFFFF0000);
   
   mPhone->addRefImpl();
   StackLog(<< "XmppVCardJsonProxyInterface::XmppVCardJsonProxyInterface(): " << this << " phone: " << mPhone);
}

XmppVCardJsonProxyInterface::~XmppVCardJsonProxyInterface()
{
   StackLog(<< "XmppVCardJsonProxyInterface::~XmppVCardJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppVCardJsonProxyInterface::Release()
{
   StackLog(<< "XmppVCardJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

void XmppVCardJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppVCardJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   dynamic_cast<CPCAPI2::XmppAccount::XmppAccountJsonProxyInterface*>(CPCAPI2::XmppAccount::XmppAccountJsonProxyInterface::getInterface(mPhone))->postCallbackPub(fp);
}

void XmppVCardJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

// JsonApiClientModule
void XmppVCardJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppVCardJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppVCardJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppVCardJsonProxyInterface::processIncomingImpl()");
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppVCardJsonProxyInterface::setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler)
{
   StackLog(<< "XmppVCardJsonProxyInterface::setHandler(): " << account << " -> " << handler);
   resip::ReadCallbackBase* f = resip::resip_bind(&XmppVCardJsonProxyInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      XmppAccount::XmppAccountManagerJsonProxy::getInterface(mPhone)->process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

int XmppVCardJsonProxyInterface::setHandlerImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler)
{
   StackLog(<< "XmppVCardJsonProxyInterfac::setHandlerImpl(): " << account << " -> " << handler);
   mAppHandlers[account] = handler;
   bool release = (handler ? false : true);

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account), JSON_VALUE(release));

   return kSuccess;
}

XmppVCardHandle XmppVCardJsonProxyInterface::create(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppVCardJsonProxyInterface::create(): account: " << account);
   auto hF = mServerCreatedHandle.get_future();
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::createImpl, this, account));
   
   XmppVCardHandle h = 0;
   
   if (hF.wait_for(std::chrono::milliseconds(5000)) == std::future_status::ready)
   {
      h = hF.get();
      StackLog(<< "XmppVCardJsonProxyInterface::create(): account: " << account << " vcard handle: " << h);
   }
   else
   {
      WarningLog(<< "XmppVCardJsonProxyInterface::create(): create vcard request for account: " << account << " timed out, no response received from server");
   }
   
   mServerCreatedHandle = std::promise<XmppVCardHandle>(); // reset for next use
   return h;
}

int XmppVCardJsonProxyInterface::createImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppVCardJsonProxyInterface::createImpl(): account: " << account);

   JsonFunctionCall(mTransport, "create", JSON_VALUE(account));

   return kSuccess;
}

int XmppVCardJsonProxyInterface::fetchVCard(XmppVCardHandle handle, const cpc::string& jid)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::fetchVCardImpl, this, handle, jid));
   return kSuccess;
}

int XmppVCardJsonProxyInterface::fetchVCardImpl(XmppVCardHandle handle, const cpc::string& jid)
{
   StackLog(<< "XmppVCardJsonProxyInterface::fetchVCardImpl(): handle: " << handle << " jid: :" << jid);

   JsonFunctionCall(mTransport, "fetchVCard", JSON_VALUE(handle), JSON_VALUE(jid));

   return kSuccess;
}

int XmppVCardJsonProxyInterface::storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::storeVCardImpl, this, handle, vcard));
   return kSuccess;
}

int XmppVCardJsonProxyInterface::storeVCardImpl(XmppVCardHandle handle, const XmppVCardDetail& detail)
{
   StackLog(<< "XmppVCardJsonProxyInterface::storeVCardImpl(): " << this << " phone: " << mPhone << " handle: " << handle << " vcard (" << &detail << "): " << detail << " photo image size: " << detail.photo.binval.size());
   JsonFunctionCall(mTransport, "storeVCard", JSON_VALUE(handle), JSON_VALUE(detail));
   return kSuccess;
}

int XmppVCardJsonProxyInterface::cancelVCardOperations(XmppVCardHandle handle)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::cancelVCardOperationsImpl, this, handle));
   return kSuccess;
}

int XmppVCardJsonProxyInterface::cancelVCardOperationsImpl(XmppVCardHandle handle)
{
   StackLog(<< "XmppVCardJsonProxyInterface::cancelVCardOperationsImpl(): handle: " << handle);

   JsonFunctionCall(mTransport, "cancelVCardOperations", JSON_VALUE(handle));

   return kSuccess;
}

int XmppVCardJsonProxyInterface::handleCreateResult(const rapidjson::Value& functionObjectVal)
{
   XmppAccount::XmppAccountHandle account = 0;
   XmppVCard::XmppVCardHandle handle = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(handle));

   mServerCreatedHandle.set_value(handle);
   StackLog(<< "XmppVCardJsonProxyInterface::handleCreateResult(): account: " << account << " handle: " << handle);
   return kSuccess;
}

int XmppVCardJsonProxyInterface::handleVCardFetched(const rapidjson::Value& functionObjectVal)
{
   XmppVCard::VCardFetchedEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));

   std::map<XmppAccount::XmppAccountHandle, XmppVCardHandler*>::iterator it = mAppHandlers.find(args.account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppVCardHandler::onVCardFetched, it->second, args.handle, args));
   }

   StackLog(<< "XmppVCardJsonProxyInterface::handleVCardFetched(): account: " << args.account << " handle: " << args.handle << " jid: " << args.jid << " vcard: " << args.detail);
   return kSuccess;
}

int XmppVCardJsonProxyInterface::handleVCardOperationResult(const rapidjson::Value& functionObjectVal)
{
   XmppVCard::VCardOperationResultEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));

   std::map<XmppAccount::XmppAccountHandle, XmppVCardHandler*>::iterator it = mAppHandlers.find(args.account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppVCardHandler::onVCardOperationResult, it->second, args.handle, args));
   }

   StackLog(<< "XmppVCardJsonProxyInterface::handleVCardOperationResult(): account: " << args.account << " handle: " << args.handle << " jid: " << args.jid << " type: " << args.type << " result: " << args.result);
   return kSuccess;
}

int XmppVCardJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
   XmppVCard::ErrorEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));

   std::map<XmppAccount::XmppAccountHandle, XmppVCardHandler*>::iterator it = mAppHandlers.find(args.account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppVCardHandler::onError, it->second, args.handle, args));
   }

   StackLog(<< "XmppVCardJsonProxyInterface::handleError() account: " << args.account << " handle: " << args.handle << " error: " << args.errorText);
   return kSuccess;
}

int XmppVCardJsonProxyInterface::handleVCardState(const rapidjson::Value& functionObjectVal)
{
   XmppVCard::JsonProxyVCardStateEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));
      
   StackLog(<< "XmppVCardJsonProxyInterface::handleVCardState(): mStateHandler: " << mStateHandler << " state list: " << args.states.size());
   if (mStateHandler != NULL)
   {
      // StackLog(<< "XmppVCardJsonProxyInterface::handleVCardState(): mStateHandler: " << mStateHandler << " initiating postCallback");
      postCallback(makeFpCommand1(XmppVCardJsonProxyStateHandler::onVCardState, mStateHandler, args));
   }
      
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::setStateHandler(XmppVCardJsonProxyStateHandler* handler)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::setStateHandlerImpl, this, handler));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::setStateHandlerImpl(XmppVCardJsonProxyStateHandler* handler)
{
   StackLog(<< "XmppVCardJsonProxyInterface::setStateHandlerImpl(): handler: " << handler);
   mStateHandler = handler;
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestVCardState(XmppVCardHandle handle)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::requestVCardStateImpl, this, handle));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestVCardStateImpl(XmppVCardHandle vcard)
{
   StackLog(<< "XmppVCardJsonProxyInterface::requestVCardStateImpl(): vcard: " << vcard);
   JsonFunctionCall(mTransport, "requestVCardState", JSON_VALUE(vcard));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestVCardStateForAccount(XmppAccount::XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::requestVCardStateForAccountImpl, this, account));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestVCardStateForAccountImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppVCardJsonProxyInterface::requestVCardStateForAccountImpl(): account: " << account);
   JsonFunctionCall(mTransport, "requestVCardStateForAccount", JSON_VALUE(account));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestAllVCardState()
{
   post(resip::resip_bind(&XmppVCardJsonProxyInterface::requestAllVCardStateImpl, this));
   return kSuccess;
}
   
int XmppVCardJsonProxyInterface::requestAllVCardStateImpl()
{
   StackLog(<< "XmppVCardJsonProxyInterface::requestAllVCardStateImpl()");
   JsonFunctionCall(mTransport, "requestAllVCardState");
   return kSuccess;
}
   
}

}

#endif
