#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppVCardJsonProxy.h"
#include "interface/experimental/xmpp/XmppVCardJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppVCardJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppVCardJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace XmppVCard
   {
      XmppVCardJsonApi* XmppVCardJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppVCardJsonServerInterface>(phone, "XmppVCardJsonApi");
#else
         return NULL;
#endif
      }
      
      XmppVCardManagerJsonProxy* XmppVCardManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppVCardJsonProxyInterface>(phone, "XmppVCardManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
