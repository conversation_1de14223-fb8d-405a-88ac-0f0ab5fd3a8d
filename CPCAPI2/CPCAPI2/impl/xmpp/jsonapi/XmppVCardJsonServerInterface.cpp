#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppVCardJsonServerInterface.h"
#include "xmpp/XmppVCardManagerInterface.h"
#include "xmpp/XmppVCardTypesInternal.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD
#define JSON_MODULE "XmppVCardManagerJsonProxy"

namespace CPCAPI2
{

namespace XmppVCard
{

XmppVCardJsonServerInterface::XmppVCardJsonServerInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   XmppVCardManager* xmppVCard = XmppVCardManager::getInterface(phone);
   
   // the state needs to be setup early
   XmppVCardStateManager::getInterface(xmppVCard);
   XmppVCardManagerInterface* xmppVCardIf = dynamic_cast<XmppVCardManagerInterface*>(xmppVCard);
   xmppVCardIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&XmppVCardJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["create"] = std::bind(&XmppVCardJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["fetchVCard"] = std::bind(&XmppVCardJsonServerInterface::handleFetchVCard, this, std::placeholders::_1);
   mFunctionMap["storeVCard"] = std::bind(&XmppVCardJsonServerInterface::handleStoreVCard, this, std::placeholders::_1);
   mFunctionMap["cancelVCardOperations"] = std::bind(&XmppVCardJsonServerInterface::handleCancelVCardOperations, this, std::placeholders::_1);
   mFunctionMap["requestVCardState"] = std::bind(&XmppVCardJsonServerInterface::handleRequestVCardState, this, std::placeholders::_1);
   mFunctionMap["requestVCardStateForAccount"] = std::bind(&XmppVCardJsonServerInterface::handleRequestVCardStateForAccount, this, std::placeholders::_1);
   mFunctionMap["requestAllVCardState"] = std::bind(&XmppVCardJsonServerInterface::handleRequestAllVCardState, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppVCardJsonServerInterface::~XmppVCardJsonServerInterface()
{
}

void XmppVCardJsonServerInterface::Release()
{
}

void XmppVCardJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppVCardJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppVCardJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppVCardJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppVCardJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppVCardJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* xmppVCardManager = XmppVCardManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   bool release = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(release));

   StackLog(<< "XmppVCardJsonServerInterface::handleSetHandler(): " << (release ? "releasing" : "creating") << " vcard handler for account: " << account);
   xmppVCardManager->setHandler(account, (release ? NULL : (XmppVCardHandler*)0xDEADBEFF)); // note: this ends in FF, meaning that we don't want callbacks at all
   return kSuccess;
}

int XmppVCardJsonServerInterface::handleCreate(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* xmppVCardManager = XmppVCardManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
   XmppVCardHandle handle = xmppVCardManager->create(account);

   if (handle == 0)
   {
      InfoLog(<< "XmppVCardJsonServerInterface::handleCreate(): failure");
   }

   StackLog(<< "XmppVCardJsonServerInterface::handleCreate(): account: " << account << " handle: " << handle);

   JsonFunctionCall(mTransport, "onCreateResult", JSON_VALUE(account), JSON_VALUE(handle));
   return kSuccess;
}

int XmppVCardJsonServerInterface::handleFetchVCard(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* xmppVCardManager = XmppVCardManager::getInterface(mPhone);
   XmppVCardHandle handle = 0;
   cpc::string jid("");
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(jid));

   StackLog(<< "XmppVCardJsonServerInterface::handleFetchVCard(): handle: " << handle << " jid: " << jid);
   xmppVCardManager->fetchVCard(handle, jid);
   return kSuccess;
}

int XmppVCardJsonServerInterface::handleStoreVCard(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* xmppVCardManager = XmppVCardManager::getInterface(mPhone);
   XmppVCardHandle handle = 0;
   XmppVCard::XmppVCardDetail detail;
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(detail));

   StackLog(<< "XmppVCardJsonServerInterface::handleStoreVCard(): handle: " << handle << " vcard: " << detail);
   xmppVCardManager->storeVCard(handle, detail);
   return kSuccess;
}

int XmppVCardJsonServerInterface::handleCancelVCardOperations(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* xmppVCardManager = XmppVCardManager::getInterface(mPhone);
   XmppVCardHandle handle = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle));

   StackLog(<< "XmppVCardJsonServerInterface::handleCancelVCardOperations(): handle: " << handle);
   xmppVCardManager->cancelVCardOperations(handle);
   return kSuccess;
}

int XmppVCardJsonServerInterface::handleRequestVCardState(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* vcardMgr = XmppVCardManager::getInterface(mPhone);
   XmppVCardStateManager* vcardStateMgr = XmppVCardStateManager::getInterface(vcardMgr);
      
   XmppVCardHandle vcard = (-1);
   JsonDeserialize(functionObjectVal, JSON_VALUE(vcard));
   
   JsonProxyVCardStateEvent args;
   vcardStateMgr->getAllStates(vcard, args.states);
      
   StackLog(<< "XmppVCardJsonServerInterface::handleRequestVCardState(): " << this << " mPhone: " << mPhone << " vcardMgr: " << vcardMgr << " vcardStateMgr: " << vcardStateMgr << " vcard: " << vcard << " vcard list: " << args.states.size());
   
   JsonFunctionCallNoLog(mTransport, "onVCardState", JSON_VALUE(args));
   return kSuccess;
}
   
int XmppVCardJsonServerInterface::handleRequestVCardStateForAccount(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* vcardMgr = XmppVCardManager::getInterface(mPhone);
   XmppVCardStateManager* vcardStateMgr = XmppVCardStateManager::getInterface(vcardMgr);
      
   XmppAccount::XmppAccountHandle account = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
      
   JsonProxyVCardStateEvent args;
   vcardStateMgr->getAllStatesForAccount(account, args.states);
      
   StackLog(<< "XmppVCardJsonServerInterface::handleRequestVCardStateForAccount(): " << this << " mPhone: " << mPhone << " vcardMgr: " << vcardMgr << " vcardStateMgr: " << vcardStateMgr << " account: " << account << " vcard list: " << args.states.size());
      
   JsonFunctionCallNoLog(mTransport, "onVCardState", JSON_VALUE(args));
   return kSuccess;
}
   
int XmppVCardJsonServerInterface::handleRequestAllVCardState(const rapidjson::Value& functionObjectVal)
{
   XmppVCardManager* vcardMgr = XmppVCardManager::getInterface(mPhone);
   XmppVCardStateManager* vcardStateMgr = XmppVCardStateManager::getInterface(vcardMgr);
   
   JsonProxyVCardStateEvent args;
   vcardStateMgr->getAllStateInfo(args.states);
   
   StackLog(<< "XmppVCardJsonServerInterface::handleRequestAllVCardState(): " << this << " mPhone: " << mPhone << " vcardMgr: " << vcardMgr << " vcardStateMgr: " << vcardStateMgr << " vcard stats info list: " << args.states.size());
   
   JsonFunctionCallNoLog(mTransport, "onVCardState", JSON_VALUE(args));
   return kSuccess;
}
   
int XmppVCardJsonServerInterface::onVCardFetched(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& args)
{
   StackLog(<< "XmppVCardJsonServerInterface::onVCardFetched(): photo binary-data size: " << args.detail.photo.binval.size() << " logo binary-data size: " << args.detail.logo.binval.size());
   StackLog(<< "XmppVCardJsonServerInterface::onVCardFetched(): account: " << args.account << " handle: " << args.handle << " jid: " << args.jid << " vcard: " << args.detail);
   JsonFunctionCallNoLog(mTransport, "onVCardFetched", JSON_VALUE(handle), JSON_VALUE(args));
   return kSuccess;
}

int XmppVCardJsonServerInterface::onVCardOperationResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& args)
{
   StackLog(<< "XmppVCardJsonServerInterface::onVCardOperationResult(): account: " << args.account << " handle: " << args.handle << " jid: " << args.jid << " type: " << args.type << " result: " << args.result);
   JsonFunctionCall(mTransport, "onVCardOperationResult", JSON_VALUE(handle), JSON_VALUE(args));
   return kSuccess;
}

int XmppVCardJsonServerInterface::onError(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& args)
{
   StackLog(<< "XmppVCardJsonServerInterface::onError(): account: " << args.account << " handle: " << args.handle << " errorText: " << args.errorText);
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(handle), JSON_VALUE(args));
   return kSuccess;
}

int XmppVCardJsonServerInterface::onCreateVCardResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::XmppVCardCreatedResultEvent& args)
{
   JsonFunctionCall(mTransport, "onCreateVCardResult", "account", args.account, "handle", handle);
   return kSuccess;
}
   
}

}

#endif
