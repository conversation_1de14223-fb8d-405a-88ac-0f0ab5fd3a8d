#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_JSON_INTERFACE_H)
#define CPCAPI2_XMPP_ACCOUNT_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/xmpp/XmppAccountJsonApi.h"
#include "jsonapi/JsonApiServerModule.h"
#include "xmpp/XmppAccountHandlerInternal.h"
#include "xmpp/XmppAccountSyncHandler.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{

class PhoneInterface;
namespace XmppAccount
{

class XmppAccountJsonServerInterface : public CPCAPI2::XmppAccount::XmppAccountHandlerInternal,
                                       public CPCAPI2::XmppAccount::XmppAccountJsonApi,
                                       public CPCAPI2::XmppAccount::XmppAccountSyncHand<PERSON>,
                                       public CPCAPI2::JsonApi::JsonApiServerModule,
                                       public CPCAPI2::PhoneModule
{

public:

   XmppAccountJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~XmppAccountJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE;
   virtual int onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent& args) OVERRIDE;
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args) OVERRIDE;
   virtual int onStreamManagementState(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::StreamManagementStateEvent& args) OVERRIDE;
   virtual int onPrivateStorageData(XmppAccountHandle account, const PrivateStorageDataEvent& args) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleCreateWithSettings(const rapidjson::Value& functionObjectVal);
   int handleConfigureDefaultAccountSettings(const rapidjson::Value& functionObjectVal);
   int handleApplySettings(const rapidjson::Value& functionObjectVal);
   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handlePublishPresence(const rapidjson::Value& functionObjectVal);
   int handlePublishCannedPresence(const rapidjson::Value& functionObjectVal);
   int handleEnable(const rapidjson::Value& functionObjectVal);
   int handleDisable(const rapidjson::Value& functionObjectVal);
   int handleDestroy(const rapidjson::Value& functionObjectVal);
   int handleSetNetworkRestriction(const rapidjson::Value& functionObjectVal);
   int handleBlockIncomingPresence(const rapidjson::Value& functionObjectVal);
   int handleSetHibernationState(const rapidjson::Value& functionObjectVal);
   int handleGetEntityTime(const rapidjson::Value& functionObjectVal);
   int handleEnableNotification(const rapidjson::Value& functionObjectVal);
   int handleDisableNotification(const rapidjson::Value& functionObjectVal);
   int handleGetPrivateStorageData(const rapidjson::Value& functionObjectVal);
   int handleSetPrivateStorageData(const rapidjson::Value& functionObjectVal);
   int handleRequestStateAllAccounts(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_XMPP_ACCOUNT_JSON_INTERFACE_H
