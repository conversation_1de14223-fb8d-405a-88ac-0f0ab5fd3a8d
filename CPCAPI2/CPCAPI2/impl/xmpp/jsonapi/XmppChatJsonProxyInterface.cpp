#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppChatJsonProxyInterface.h"
#include "XmppAccountJsonProxyInterface.h"
#include "xmpp/XmppChatManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"

#include <rutil/Random.hxx>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_CHAT

#define JSON_MODULE "XmppChatJsonApi"

namespace CPCAPI2
{

namespace XmppChat
{

XmppChatJsonProxyInterface::XmppChatJsonProxyInterface(Phone* phone) :
   CPCAPI2::EventSource<CPCAPI2::XmppAccount::Xmpp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, XmppC<PERSON><PERSON><PERSON><PERSON>, XmppChat<PERSON>son<PERSON><PERSON><PERSON><PERSON><PERSON>>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread(),
         static_cast<XmppAccount::XmppAccountJsonProxyInterface*>(XmppAccount::XmppAccountJsonProxyInterface::getInterface(phone))->callbackFifoHolder()),
      mPhone(dynamic_cast<PhoneInterface*>(phone)),
      mTransport(NULL),
      mRemoteSyncHelperIf(NULL)
{
   mFunctionMap["onNewChat"] = std::bind(&XmppChatJsonProxyInterface::handleNewChat, this, std::placeholders::_1);
   mFunctionMap["onIsComposingMessage"] = std::bind(&XmppChatJsonProxyInterface::handleIsComposingMessage, this, std::placeholders::_1);
   mFunctionMap["onNewMessage"] = std::bind(&XmppChatJsonProxyInterface::handleNewMessage, this, std::placeholders::_1);
   mFunctionMap["onNewReaction"] = std::bind(&XmppChatJsonProxyInterface::handleNewReaction, this, std::placeholders::_1);
   mFunctionMap["onNewOutboundMessage"] = std::bind(&XmppChatJsonProxyInterface::handleNewOutboundMessage, this, std::placeholders::_1);
   mFunctionMap["onSendMessageSuccess"] = std::bind(&XmppChatJsonProxyInterface::handleSendMessageSuccess, this, std::placeholders::_1);
   mFunctionMap["onSendMessageFailure"] = std::bind(&XmppChatJsonProxyInterface::handleSendMessageFailure, this, std::placeholders::_1);
   mFunctionMap["onMessageDelivered"] = std::bind(&XmppChatJsonProxyInterface::handleMessageDelivered, this, std::placeholders::_1);
   mFunctionMap["onMessageDeliveryError"] = std::bind(&XmppChatJsonProxyInterface::handleMessageDeliveryError, this, std::placeholders::_1);
   mFunctionMap["onMessageDisplayed"] = std::bind(&XmppChatJsonProxyInterface::handleMessageDisplayed, this, std::placeholders::_1);
   mFunctionMap["onChatEnded"] = std::bind(&XmppChatJsonProxyInterface::handleChatEnded, this, std::placeholders::_1);
   mFunctionMap["onValidateChatHandleResult"] = std::bind(&XmppChatJsonProxyInterface::handleValidateChatHandleResult, this, std::placeholders::_1);
   mFunctionMap["onCreateMessageResult"] = std::bind(&XmppChatJsonProxyInterface::handleCreateMessageResult, this, std::placeholders::_1);
   mFunctionMap["onNotifyMessageDeliveredResult"] = std::bind(&XmppChatJsonProxyInterface::handleNotifyMessageDeliveredResult, this, std::placeholders::_1);
   mFunctionMap["onNotifyMessageDisplayedResult"] = std::bind(&XmppChatJsonProxyInterface::handleNotifyMessageDisplayedResult, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mPhone->addRefImpl();
   StackLog(<< "XmppChatJsonProxyInterface::XmppChatJsonProxyInterface(): " << this << " phone: " << mPhone);
}

XmppChatJsonProxyInterface::~XmppChatJsonProxyInterface()
{
   StackLog(<< "XmppChatJsonProxyInterface::~XmppChatJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppChatJsonProxyInterface::Release()
{
   StackLog(<< "XmppChatJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

// JsonApiClientModule
void XmppChatJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppChatJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   if (std::string(funcName) == "onNewMessage")
   {
      DebugLog(<< "XmppChatJsonProxyInterface::processIncoming for onNewMessage; message id: " << functionObjectVal["args"]["messageId"].GetString());
   }
   
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppChatJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppChatJsonProxyInterface::processIncomingImpl()");
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   if (std::string(funcName) == "onNewMessage")
   {
      DebugLog(<< "XmppChatJsonProxyInterface::processIncomingImpl for onNewMessage; message id: " << functionObjectVal["args"]["messageId"].GetString());
   }

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppChatJsonProxyInterface::handleNewChat(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleNewChat()");
   XmppChatHandle chat = -1;
   XmppChat::NewChatEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   mMapChatHandleToAccountHandle[chat] = args.account;

   fireEvent(cpcFunc(XmppChatHandler::onNewChat), args.account, chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleIsComposingMessage(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleIsComposingMessage()");
   XmppChatHandle chat = -1;
   XmppChat::IsComposingMessageEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onIsComposingMessage), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNewMessage(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleNewMessage()");
   XmppChatHandle chat = -1;
   XmppChat::NewMessageEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.find(chat) == mMapChatHandleToAccountHandle.end())
   {
      mMapChatHandleToAccountHandle[chat] = args.account;
   }

   DebugLog(<< "XmppChatJsonProxyInterface::handleNewMessage firing onNewMessage callback for message id: " << args.messageId);
   fireEvent(cpcFunc(XmppChatHandler::onNewMessage), args.account, chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNewReaction(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleNewReaction()");
   XmppChatHandle chat = -1;
   XmppChat::NewReactionEvent args;

// DRL FIXIT! Need a converter here!
//   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onNewReaction), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNewMessageRetraction(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleNewMessageRetraction()");
   XmppChatHandle chat = -1;
   XmppChat::NewMessageRetractionEvent args;

// DRL FIXIT! Need a converter here!
//   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onNewMessageRetraction), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNewOutboundMessage(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleNewOutboundMessage()");
   XmppChatHandle chat = -1;
   XmppChat::NewMessageEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.find(chat) == mMapChatHandleToAccountHandle.end())
   {
      mMapChatHandleToAccountHandle[chat] = args.account;
   }

   DebugLog(<< "XmppChatJsonProxyInterface::handleNewOutboundMessage firing onNewMessage callback for message id: " << args.messageId);
   fireEvent(cpcFunc(XmppChatHandler::onNewOutboundMessage), args.account, chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleSendMessageSuccess(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleSendMessageSuccess()");
   XmppChatHandle chat = -1;
   XmppChat::SendMessageSuccessEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onSendMessageSuccess), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleSendMessageFailure(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleSendMessageFailure()");
   XmppChatHandle chat = -1;
   XmppChat::SendMessageFailureEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onSendMessageFailure), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleMessageDelivered(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleMessageDelivered()");
   XmppChatHandle chat = -1;
   XmppChat::MessageDeliveredEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onMessageDelivered), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleMessageDeliveryError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleMessageDeliveryError()");
   XmppChatHandle chat = -1;
   XmppChat::MessageDeliveryErrorEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onMessageDeliveryError), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleMessageDisplayed(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleMessageDisplayed()");
   XmppChatHandle chat = -1;
   XmppChat::MessageDisplayedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onMessageDisplayed), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleChatEnded(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleChatEnded()");
   XmppChatHandle chat = -1;
   XmppChat::ChatEndedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onChatEnded), mMapChatHandleToAccountHandle[chat], chat, args);
   
   mMapChatHandleToAccountHandle.erase(chat);
   
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppChatJsonProxyInterface::handleError()");
   XmppChatHandle chat = -1;
   XmppChat::ErrorEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));

   if (mMapChatHandleToAccountHandle.count(chat) == 0)
   {
      return kError;
   }

   fireEvent(cpcFunc(XmppChatHandler::onError), mMapChatHandleToAccountHandle[chat], chat, args);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleCreateMessageResult(const rapidjson::Value& functionObjectVal)
{
   XmppChat::XmppChatMessageHandle handle = -1;
   XmppChat::XmppChatHandle chat = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(chat));
   StackLog(<< "XmppChatJsonProxyInterface::handleCreateMessageResult(): chat: " << chat << " handle: " << handle);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleValidateChatHandleResult(const rapidjson::Value& functionObjectVal)
{
   XmppChat::XmppChatHandle chat = -1;
   ValidateChatHandleEvent args;
   args.account = -1;
   args.chatHandleValid = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(args));
   StackLog(<< "XmppChatJsonProxyInterface::handleValidateChatHandleResult(): chat:" << chat);

   // mMapChatHandleToAccountHandle may not contain this chat handle -- so don't check it

   fireEvent(cpcFunc(XmppChatHandler::onValidateChatHandleResult), args.account, chat, args);
  
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNotifyMessageDeliveredResult(const rapidjson::Value& functionObjectVal)
{
   XmppChat::XmppChatMessageHandle message = -1;
   XmppChat::XmppChatHandle chat = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(message), JSON_VALUE(chat));
   StackLog(<< "XmppChatJsonProxyInterface::handleNotifyMessageDeliveredResult(): chat: " << chat << " message: " << message);
   return kSuccess;
}

int XmppChatJsonProxyInterface::handleNotifyMessageDisplayedResult(const rapidjson::Value& functionObjectVal)
{
   XmppChat::XmppChatMessageHandle message = -1;
   XmppChat::XmppChatHandle chat = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(message), JSON_VALUE(chat));
   StackLog(<< "XmppChatJsonProxyInterface::handleNotifyMessageDisplayedResult(): chat: " << chat << " message: " << message);
   return kSuccess;
}

int XmppChatJsonProxyInterface::setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppChatHandler* handler)
{
   setAppHandler(account, handler);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::setHandlerImpl, this, account, handler));
   return kSuccess;
}

void XmppChatJsonProxyInterface::setHandlerImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppChatHandler* handler)
{
   bool release = (handler ? false : true);
   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account), JSON_VALUE(release));
}

XmppChatHandle XmppChatJsonProxyInterface::createChat(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppChatJsonProxyInterface::createChat: account: " << account);
   XmppChatHandle h = (XmppChatHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::createChatImpl, this, h, account));
   return h;
}

void XmppChatJsonProxyInterface::createChatImpl(XmppChatHandle chat, XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppChatJsonProxyInterface::createChatImpl()");
   JsonFunctionCall(mTransport, "createChat", JSON_VALUE(chat), JSON_VALUE(account));
}

int XmppChatJsonProxyInterface::addParticipant(XmppChatHandle chat, const cpc::string & participantAddress)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::addParticipantImpl, this, chat, participantAddress));
   return kSuccess;
}

void XmppChatJsonProxyInterface::addParticipantImpl(XmppChatHandle chat, const cpc::string & participantAddress)
{
   StackLog(<< "XmppChatJsonProxyInterface::addParticipantImpl()");
   JsonFunctionCall(mTransport, "addParticipant", JSON_VALUE(chat), JSON_VALUE(participantAddress));
}

int XmppChatJsonProxyInterface::start(XmppChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::startImpl, this, chat));
   return kSuccess;
}

void XmppChatJsonProxyInterface::startImpl(XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::startImpl()");
   JsonFunctionCall(mTransport, "start", JSON_VALUE(chat));
}

int XmppChatJsonProxyInterface::end(XmppChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::endImpl, this, chat));
   return kSuccess;
}

void XmppChatJsonProxyInterface::endImpl(XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::endImpl()");
   JsonFunctionCall(mTransport, "end", JSON_VALUE(chat));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::sendMessage(XmppChatHandle chat, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::sendMessageImpl, this, messageHandle, chat, messageContent, htmlText, subject));
   return messageHandle;
}

void XmppChatJsonProxyInterface::sendMessageImpl(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject)
{
   StackLog(<< "XmppChatJsonProxyInterface::sendMessageImpl()");
   JsonFunctionCall(mTransport, "sendMessage", JSON_VALUE(message), JSON_VALUE(chat), JSON_VALUE(messageContent), JSON_VALUE(htmlText), JSON_VALUE(subject));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::sendReaction(XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string> & reactions)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::sendReactionImpl, this, messageHandle, chat, target, reactions));
   return messageHandle;
}

void XmppChatJsonProxyInterface::sendReactionImpl(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string> & reactions)
{
   StackLog(<< "XmppChatJsonProxyInterface::sendReactionImpl()");
   JsonFunctionCall(mTransport, "sendReaction", JSON_VALUE(message), JSON_VALUE(chat), JSON_VALUE(target), JSON_VALUE(reactions));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::sendMessageRetraction(XmppChatHandle chat, const cpc::string& target)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::sendMessageRetractionImpl, this, messageHandle, chat, target));
   return messageHandle;
}

void XmppChatJsonProxyInterface::sendMessageRetractionImpl(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string& target)
{
   StackLog(<< "XmppChatJsonProxyInterface::sendMessageRetractionImpl()");
   JsonFunctionCall(mTransport, "sendMessageRetraction", JSON_VALUE(message), JSON_VALUE(chat), JSON_VALUE(target));
}

int XmppChatJsonProxyInterface::accept(XmppChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::acceptImpl, this, chat));
   return kSuccess;
}

void XmppChatJsonProxyInterface::acceptImpl(XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::acceptImpl()");
   JsonFunctionCall(mTransport, "accept", JSON_VALUE(chat));
}

int XmppChatJsonProxyInterface::reject(XmppChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::rejectImpl, this, chat));
   return kSuccess;
}

void XmppChatJsonProxyInterface::rejectImpl(XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::rejectImpl()");
   JsonFunctionCall(mTransport, "reject", JSON_VALUE(chat));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDelivered: chat: " << chat << " message: " << message);
   XmppChatMessageHandle h = (XmppChatMessageHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::notifyMessageDeliveredImpl_DEPRECATED, this, h, chat, message, messageDeliveryStatus));
   return h;
}

void XmppChatJsonProxyInterface::notifyMessageDeliveredImpl_DEPRECATED(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDeliveredImpl(): chat: " << chat << " message: " << message);
   JsonFunctionCall(mTransport, "notifyMessageDelivered", JSON_VALUE(chatMessage), JSON_VALUE(chat), JSON_VALUE(message), JSON_VALUE(messageDeliveryStatus));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDisplayed: chat: " << chat << " message: " << message);
   XmppChatMessageHandle h = (XmppChatMessageHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::notifyMessageDisplayedImpl_DEPRECATED, this, h, chat, message, messageDisplayStatus));
   return h;
}

void XmppChatJsonProxyInterface::notifyMessageDisplayedImpl_DEPRECATED(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDisplayedImpl(): chat: " << chat << " message: " << message);
   JsonFunctionCall(mTransport, "notifyMessageDelivered", JSON_VALUE(chatMessage), JSON_VALUE(chat), JSON_VALUE(message), JSON_VALUE(messageDisplayStatus));
}

int XmppChatJsonProxyInterface::notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDelivered: chat: " << chat << " message: " << message);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::notifyMessageDeliveredImpl, this, chat, message));
   return kSuccess;
}

void XmppChatJsonProxyInterface::notifyMessageDeliveredImpl(XmppChatHandle chat, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDeliveredImpl(): chat: " << chat << " message: " << message);
   JsonFunctionCall(mTransport, "notifyMessageDelivered", JSON_VALUE(chat), JSON_VALUE(message));
}

int XmppChatJsonProxyInterface::notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDisplayed: chat: " << chat << " message: " << message);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::notifyMessageDisplayedImpl, this, chat, message));
   return kSuccess;
}

void XmppChatJsonProxyInterface::notifyMessageDisplayedImpl(XmppChatHandle chat, XmppChatMessageHandle message)
{
   StackLog(<< "XmppChatJsonProxyInterface::notifyMessageDisplayedImpl(): chat: " << chat << " message: " << message);
   JsonFunctionCall(mTransport, "notifyMessageDelivered", JSON_VALUE(chat), JSON_VALUE(message));
}

int XmppChatJsonProxyInterface::setIsComposingMessage(XmppChatHandle chat, int refreshInterval, int idleInterval)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::setIsComposingMessageImpl, this, chat, refreshInterval, idleInterval));
   return kSuccess;
}

void XmppChatJsonProxyInterface::setIsComposingMessageImpl(XmppChatHandle chat, int refreshInterval, int idleInterval)
{
   StackLog(<< "XmppChatJsonProxyInterface::setIsComposingMessageImpl()");
   JsonFunctionCall(mTransport, "setIsComposingMessage", JSON_VALUE(chat), JSON_VALUE(refreshInterval), JSON_VALUE(idleInterval));
}

RemoteSyncXmppHelper::RemoteSyncXmppHelper* XmppChatJsonProxyInterface::getRemoteSyncHelper()
{
   if (mRemoteSyncHelperIf == NULL)
   {
      mRemoteSyncHelperIf = RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(mPhone);
   }
   return mRemoteSyncHelperIf;
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncFromID(XmppChatHandle chat, XmppChatMessageHandle message)
{
   return getRemoteSyncHelper()->getRemoteSyncFromID(chat, message);
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncToID(XmppChatHandle chat, XmppChatMessageHandle message)
{
   return getRemoteSyncHelper()->getRemoteSyncToID(chat, message);
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncConversationID(XmppChatHandle chat)
{
   return getRemoteSyncHelper()->getRemoteSyncConversationID(chat);
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncUniqueID(XmppChatHandle chat, const cpc::string & stanzaID)
{
   return getRemoteSyncHelper()->getRemoteSyncUniqueID(chat, stanzaID);
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncUniqueID2(const cpc::string & stanzaID, const cpc::string & threadID)
{
   return getRemoteSyncHelper()->getRemoteSyncUniqueID2(stanzaID, threadID);
}

cpc::string XmppChatJsonProxyInterface::getRemoteSyncEncodedContent(const cpc::string & plainText, const cpc::string & htmlText)
{
   return getRemoteSyncHelper()->getRemoteSyncEncodedContent(plainText, htmlText);
}

int XmppChatJsonProxyInterface::getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML)
{
   return getRemoteSyncHelper()->getRemoteSyncDecodedContent(remoteSyncEncodedContent, outPlainText, outHTML);
}

int XmppChatJsonProxyInterface::validateChatHandle(XmppAccount::XmppAccountHandle account, XmppChatHandle chat)
{
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::validateChatHandleImpl, this, account, chat));
   return kSuccess;
}

void XmppChatJsonProxyInterface::validateChatHandleImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::validateChatHandleImpl()");
   JsonFunctionCall(mTransport, "validateChatHandle", JSON_VALUE(account), JSON_VALUE(chat));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::createMessage(XmppChat::XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::createMessage: chat: " << chat);
   XmppChatMessageHandle h = (XmppChatMessageHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::createMessageImpl, this, h, chat));
   return h;
}

void XmppChatJsonProxyInterface::createMessageImpl(XmppChatMessageHandle chatMessage, XmppChat::XmppChatHandle chat)
{
   StackLog(<< "XmppChatJsonProxyInterface::createMessageImpl()");
   JsonFunctionCall(mTransport, "createMessage", JSON_VALUE(chatMessage), JSON_VALUE(chat));
}

XmppChatMessageHandle XmppChatJsonProxyInterface::replaceMessage(XmppChatHandle chat, const cpc::string& messageId, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject)
{
   XmppChatMessageHandle messageHandle = createMessage(chat);
   postToSdkThread(resip::resip_bind(&XmppChatJsonProxyInterface::replaceMessageImpl, this, messageHandle, chat, messageId, messageContent, htmlText, subject));
   return messageHandle;
}

void XmppChatJsonProxyInterface::replaceMessageImpl(XmppChatMessageHandle message, XmppChatHandle chat, const cpc::string & messageId, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject)
{
   StackLog(<< "XmppChatJsonProxyInterface::sendMessageImpl() STUB");
   JsonFunctionCall(mTransport, "replaceMessage", JSON_VALUE(message), JSON_VALUE(chat), JSON_VALUE(messageId), JSON_VALUE(messageContent), JSON_VALUE(htmlText), JSON_VALUE(subject));
}

}

}

#endif
