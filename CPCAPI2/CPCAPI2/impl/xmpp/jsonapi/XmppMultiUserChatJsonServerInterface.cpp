#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppMultiUserChatJsonServerInterface.h"
#include "xmpp/XmppMultiUserChatManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT
#define JSON_MODULE "XmppMultiUserChatManagerJsonProxy"

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

XmppMultiUserChatJsonServerInterface::XmppMultiUserChatJsonServerInterface(Phone* phone) :
   mPhone(static_cast<PhoneInterface*>(phone))
{
   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(phone);

   // the state needs to be setup early
   XmppMultiUserChatStateManager::getInterface(mgr);
   XmppMultiUserChatManagerInterface* xmppMucIf = dynamic_cast<XmppMultiUserChatManagerInterface*>(mgr);
   xmppMucIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["getRoomList"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleGetRoomList, this, std::placeholders::_1);
   mFunctionMap["setRoomBookmarks"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetRoomBookmarks, this, std::placeholders::_1);
   mFunctionMap["getRoomBookmarks"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleGetRoomBookmarks, this, std::placeholders::_1);
   mFunctionMap["create"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["create2"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleCreate2, this, std::placeholders::_1);
   mFunctionMap["destroyRoom"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleDestroyRoom, this, std::placeholders::_1);
   mFunctionMap["getRoomInfo"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleGetRoomInfo, this, std::placeholders::_1);
   mFunctionMap["accept"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleAccept, this, std::placeholders::_1);
   mFunctionMap["decline"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleDecline, this, std::placeholders::_1);
   mFunctionMap["join"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleJoin, this, std::placeholders::_1);
   mFunctionMap["join2"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleJoin2, this, std::placeholders::_1);
   mFunctionMap["leave"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleLeave, this, std::placeholders::_1);
   mFunctionMap["sendMessage"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSendMessage, this, std::placeholders::_1);
   mFunctionMap["setIsComposingMessage"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetIsComposingMessage, this, std::placeholders::_1);
   mFunctionMap["publishPresence"] = std::bind(&XmppMultiUserChatJsonServerInterface::handlePublishPresence, this, std::placeholders::_1);
   mFunctionMap["changeNickname"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleChangeNickname, this, std::placeholders::_1);
   mFunctionMap["changeSubject"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleChangeSubject, this, std::placeholders::_1);
   mFunctionMap["invite"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleInvite, this, std::placeholders::_1);
   mFunctionMap["kick"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleKick, this, std::placeholders::_1);
   mFunctionMap["ban"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleBan, this, std::placeholders::_1);
   mFunctionMap["changeAffiliation"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleChangeAffiliation, this, std::placeholders::_1);
   mFunctionMap["changeJidAffiliation"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleChangeJidAffiliation, this, std::placeholders::_1);
   mFunctionMap["changeRole"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleChangeRole, this, std::placeholders::_1);
   mFunctionMap["requestConfigurations"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleRequestConfigurations, this, std::placeholders::_1);
   mFunctionMap["setConfigurations"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetConfigurations, this, std::placeholders::_1);
   mFunctionMap["setConfigurationsDeprecated"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetConfigurationsDeprecated, this, std::placeholders::_1);
   mFunctionMap["requestList"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleRequestList, this, std::placeholders::_1);
   mFunctionMap["setList"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleSetList, this, std::placeholders::_1);
   mFunctionMap["requestMultiUserChatState"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatState, this, std::placeholders::_1);
   mFunctionMap["requestMultiUserChatStateForAccount"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatStateForAccount, this, std::placeholders::_1);
   mFunctionMap["requestAllMultiUserChatState"] = std::bind(&XmppMultiUserChatJsonServerInterface::handleRequestAllMultiUserChatState, this, std::placeholders::_1);

   mTransport = JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppMultiUserChatJsonServerInterface::~XmppMultiUserChatJsonServerInterface()
{
}

void XmppMultiUserChatJsonServerInterface::Release()
{
}

void XmppMultiUserChatJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppMultiUserChatJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppMultiUserChatJsonServerInterface::processIncoming(const JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppMultiUserChatJsonServerInterface::processIncomingImpl(JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

void XmppMultiUserChatJsonServerInterface::onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& args)
{
   JsonFunctionCall(mTransport, "onServiceAvailability", JSON_VALUE(account), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onRoomListRetrieved(XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& args)
{
   JsonFunctionCall(mTransport, "onRoomListRetrieved", JSON_VALUE(account), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onParticipantAdded(XmppMultiUserChatHandle handle, const ParticipantAddedEvent& args)
{
   JsonFunctionCall(mTransport, "onParticipantAdded", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onParticipantRemoved(XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& args)
{
   JsonFunctionCall(mTransport, "onParticipantRemoved", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onParticipantUpdated(XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onParticipantUpdated", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onParticipantSelfUpdated(XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onParticipantSelfUpdated", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatReady(XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatReady", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatSubjectChanged(XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatSubjectChanged", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatNewMessage(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatNewMessage", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatNewReaction(XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatNewReaction", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatNewMessageRetraction(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageRetractionEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatNewReaction", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onSendMessageSuccess(XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& args)
{
   JsonFunctionCall(mTransport, "onSendMessageSuccess", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onSendMessageFailure(XmppMultiUserChatHandle handle, const SendMessageFailureEvent& args)
{
   JsonFunctionCall(mTransport, "onSendMessageFailure", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onParticipantChatStateReceived(XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& args)
{
   JsonFunctionCall(mTransport, "onParticipantChatStateReceived", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatInvitationReceived(XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatInvitationReceived", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatInvitationDeclined(XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatInvitationDeclined", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatError(XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatError", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onLocalUserLeft(XmppMultiUserChatHandle handle, const LocalUserLeftEvent& args)
{
   JsonFunctionCall(mTransport, "onLocalUserLeft", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatConfigurationRequested(XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatConfigurationRequested", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatRoomStateChanged(XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatRoomStateChanged", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onMultiUserChatListRequested(XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& args)
{
   JsonFunctionCall(mTransport, "onMultiUserChatListRequested", JSON_VALUE(handle), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onRoomBookmarksReceived(XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& args)
{
   JsonFunctionCall(mTransport, "onRoomBookmarksReceived", JSON_VALUE(account), JSON_VALUE(args));
}

void XmppMultiUserChatJsonServerInterface::onNewRoomHandle(XmppMultiUserChatHandle handle, const NewRoomEvent& args)
{
   StackLog(<< __func__);
   
   JsonFunctionCall(mTransport, "onNewRoomHandle", JSON_VALUE(handle), JSON_VALUE(args));
}

int XmppMultiUserChatJsonServerInterface::onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::XmppMultiUserChatCreatedResultEvent& args)
{
   StackLog(<< __func__);
   
   JsonFunctionCall(mTransport, "onCreateMultiUserChatResult", JSON_VALUE(handle), JSON_VALUE(args));
   return kSuccess;
}
   
int XmppMultiUserChatJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   bool release = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(release));

   StackLog(<< "XmppMultiUserChatJsonServerInterface::handleSetHandler(): " << (release ? "releasing" : "creating") << " multi-user chat handler for account: " << account);
   mgr->setHandler(account, (release ? NULL : (XmppMultiUserChatHandler*)0xDEADBEFF)); // note: this ends in FF, meaning that we don't want callbacks at all
   return kSuccess;
}

int XmppMultiUserChatJsonServerInterface::handleGetRoomList(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   return mgr->getRoomList(account);
}

int XmppMultiUserChatJsonServerInterface::handleSetRoomBookmarks(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   cpc::vector<RoomBookmark> bookmarks;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(bookmarks));

   return mgr->setRoomBookmarks(account, bookmarks);
}

int XmppMultiUserChatJsonServerInterface::handleGetRoomBookmarks(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   return mgr->getRoomBookmarks(account);
}

int XmppMultiUserChatJsonServerInterface::handleCreate(const rapidjson::Value& functionObjectVal)
{
   return kSuccess;
}

int XmppMultiUserChatJsonServerInterface::handleCreate2(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   cpc::string room = "";
   bool instantRoom = true;
   XmppMultiUserChatHandle muc = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(muc), JSON_VALUE(account), JSON_VALUE(room));

   static_cast<XmppMultiUserChatManagerInterface*>(mgr)->create(muc, account, room);
   return kSuccess;
}

int XmppMultiUserChatJsonServerInterface::handleDestroyRoom(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string reason = "";
   cpc::string alternate = "";
   cpc::string password = "";

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(reason), JSON_VALUE(alternate), JSON_VALUE(password));

   return mgr->destroyRoom(handle, reason, alternate, password);
}

int XmppMultiUserChatJsonServerInterface::handleGetRoomInfo(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle));

   return mgr->getRoomInfo(handle);
}

int XmppMultiUserChatJsonServerInterface::handleGetRoomsInfo(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   cpc::vector<XmppMultiUserChatHandle> handles = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handles));

   return mgr->getRoomsInfo(handles);
}

int XmppMultiUserChatJsonServerInterface::handleAccept(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;
   cpc::string historyRequester;
   cpc::vector<XmppMultiUserChatHistoryItem> historyToAdd;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));

   return mgr->accept(handle, nickname, historyRequester, historyToAdd);
}

int XmppMultiUserChatJsonServerInterface::handleDecline(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(reason));

   return mgr->decline(handle, reason);
}

int XmppMultiUserChatJsonServerInterface::handleJoin(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string room;
   cpc::string nickname;
   cpc::string password;
   cpc::string historyRequester;
   cpc::vector<XmppMultiUserChatHistoryItem> historyToAdd;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(room), JSON_VALUE(nickname), JSON_VALUE(password), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));

   return mgr->join(handle, room, nickname, password, historyRequester, historyToAdd);
}

int XmppMultiUserChatJsonServerInterface::handleJoin2(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   RoomConfig config;
   cpc::string nickname;
   cpc::string password;
   cpc::string historyRequester;
   cpc::vector<XmppMultiUserChatHistoryItem> historyToAdd;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(config), JSON_VALUE(nickname), JSON_VALUE(password), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));

   return mgr->join(handle, config, nickname, password, historyRequester, historyToAdd);
}

int XmppMultiUserChatJsonServerInterface::handleLeave(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(reason));

   return mgr->leave(handle, reason);
}

int XmppMultiUserChatJsonServerInterface::handleSendMessage(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatMessageHandle message = -1;
   XmppMultiUserChatHandle handle = -1;
   cpc::string plain;
   cpc::string html;

   JsonDeserialize(functionObjectVal, JSON_VALUE(message), JSON_VALUE(handle), JSON_VALUE(plain), JSON_VALUE(html));

   static_cast<XmppMultiUserChatManagerInterface*>(mgr)->sendMessage(message, handle, plain, html);

   return kSuccess;
}

int XmppMultiUserChatJsonServerInterface::handleSetIsComposingMessage(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   int refreshInterval = 90;
   int idleInterval = 15;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(refreshInterval), JSON_VALUE(idleInterval));

   return mgr->setIsComposingMessage(handle, refreshInterval, idleInterval);
}

int XmppMultiUserChatJsonServerInterface::handlePublishPresence(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   XmppRoster::PresenceType presence = XmppRoster::PresenceType_Unknown;
   cpc::string note;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(presence), JSON_VALUE(note));

   return mgr->publishPresence(handle, presence, note);
}

int XmppMultiUserChatJsonServerInterface::handleChangeNickname(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname));

   return mgr->changeNickname(handle, nickname);
}

int XmppMultiUserChatJsonServerInterface::handleChangeSubject(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string subject;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(subject));

   return mgr->changeSubject(handle, subject);
}

int XmppMultiUserChatJsonServerInterface::handleInvite(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string jid;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(jid), JSON_VALUE(reason));

   return mgr->invite(handle, jid, reason);
}

int XmppMultiUserChatJsonServerInterface::handleKick(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(reason));

   return mgr->kick(handle, nickname, reason);
}

int XmppMultiUserChatJsonServerInterface::handleBan(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(reason));

   return mgr->ban(handle, nickname, reason);
}

int XmppMultiUserChatJsonServerInterface::handleChangeAffiliation(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;
   XmppMultiUserChatAffiliation affiliation = XmppMultiUserChatAffiliation::AffiliationNone;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(affiliation), JSON_VALUE(reason));

   return mgr->changeAffiliation(handle, nickname, affiliation, reason);
}

int XmppMultiUserChatJsonServerInterface::handleChangeJidAffiliation(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string jid;
   XmppMultiUserChatAffiliation affiliation = XmppMultiUserChatAffiliation::AffiliationNone;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(jid), JSON_VALUE(affiliation), JSON_VALUE(reason));

   return mgr->changeJidAffiliation(handle, jid, affiliation, reason);
}

int XmppMultiUserChatJsonServerInterface::handleChangeRole(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   cpc::string nickname;
   XmppMultiUserChatRole role = XmppMultiUserChatRole::RoleNone;
   cpc::string reason;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(role), JSON_VALUE(reason));

   return mgr->changeRole(handle, nickname, role, reason);
}

int XmppMultiUserChatJsonServerInterface::handleRequestConfigurations(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle));

   return mgr->requestConfigurations(handle);
}

int XmppMultiUserChatJsonServerInterface::handleSetConfigurations(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   XmppAccount::XmppDataForm dataform;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(dataform));

   return mgr->setConfigurations(handle, dataform);
}

int XmppMultiUserChatJsonServerInterface::handleSetConfigurationsDeprecated(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);
      
   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   XmppMultiUserChatConfigurations configurations;
      
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(configurations));
      
   return mgr->setConfigurations(handle, configurations);
}
   
int XmppMultiUserChatJsonServerInterface::handleRequestList(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   XmppMultiUserChatListType type = VoiceList;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(type));

   return mgr->requestList(handle, type);
}

int XmppMultiUserChatJsonServerInterface::handleSetList(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatManager* mgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatHandle handle = -1;
   XmppMultiUserChatListType type = VoiceList;
   cpc::vector<XmppMultiUserChatConfigurationsListItem> items;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(type), JSON_VALUE(items));

   return mgr->setList(handle, type, items);
}
   
int XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatState(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChatManager* mucMgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatStateManager* mucStateMgr = XmppMultiUserChatStateManager::getInterface(mucMgr);
      
   XmppMultiUserChatHandle muc = (-1);
   JsonDeserialize(functionObjectVal, JSON_VALUE(muc));
      
   JsonProxyMultiUserChatStateEvent args;
   XmppMultiUserChatStateInfo state;
   state.muc = muc;
   mucStateMgr->getState(muc, state.state);
   args.states.push_back(state);
      
   StackLog(<< "XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatState(): " << this << " mPhone: " << mPhone << " mucMgr: " << mucMgr << " mucStateMgr: " << mucStateMgr << " muc: " << muc << " muc list: " << args.states.size());
      
   JsonFunctionCall(mTransport, "onMultiUserChatState", JSON_VALUE(args));
   return kSuccess;
}
   
int XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatStateForAccount(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChatManager* mucMgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatStateManager* mucStateMgr = XmppMultiUserChatStateManager::getInterface(mucMgr);
      
   XmppAccount::XmppAccountHandle account = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
      
   JsonProxyMultiUserChatStateEvent args;
   mucStateMgr->getAllStatesForAccount(account, args.states);
      
   StackLog(<< "XmppMultiUserChatJsonServerInterface::handleRequestMultiUserChatStateForAccount(): " << this << " mPhone: " << mPhone << " mucMgr: " << mucMgr << " mucStateMgr: " << mucStateMgr << " account: " << account << " muc list: " << args.states.size());
      
   JsonFunctionCall(mTransport, "onMultiUserChatState", JSON_VALUE(args));
   return kSuccess;
}
   
int XmppMultiUserChatJsonServerInterface::handleRequestAllMultiUserChatState(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChatManager* mucMgr = XmppMultiUserChatManager::getInterface(mPhone);
   XmppMultiUserChatStateManager* mucStateMgr = XmppMultiUserChatStateManager::getInterface(mucMgr);
      
   JsonProxyMultiUserChatStateEvent args;
   mucStateMgr->getAllStates(args.states);
      
   StackLog(<< "XmppMultiUserChatJsonServerInterface::handleRequestAllMultiUserChatState(): " << this << " mPhone: " << mPhone << " mucMgr: " << mucMgr << " mucStateMgr: " << mucStateMgr << " muc stats info list: " << args.states.size());
      
   JsonFunctionCall(mTransport, "onMultiUserChatState", JSON_VALUE(args));
   return kSuccess;
}

}

}

#endif
