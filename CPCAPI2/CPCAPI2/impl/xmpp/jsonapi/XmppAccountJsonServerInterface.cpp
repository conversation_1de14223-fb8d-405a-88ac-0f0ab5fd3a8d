#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppAccountJsonServerInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountState.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT
#define JSON_MODULE "XmppAccountManagerJsonProxy"

namespace CPCAPI2
{

namespace XmppAccount
{

const std::size_t XmppAccountManagerCreate = std::hash<std::string>{}("create");

XmppAccountJsonServerInterface::XmppAccountJsonServerInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   XmppAccountManager* xmppAccount = XmppAccountManager::getInterface(phone);

   // the state needs to be setup early
   XmppAccountStateManager::getInterface(xmppAccount);
   XmppAccountInterface* xmppAccountIf = dynamic_cast<XmppAccountInterface*>(xmppAccount);
   xmppAccountIf->addSdkObserver(this);

   mFunctionMap["create"] = std::bind(&XmppAccountJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["createWithSettings"] = std::bind(&XmppAccountJsonServerInterface::handleCreateWithSettings, this, std::placeholders::_1);
   mFunctionMap["configureDefaultAccountSettings"] = std::bind(&XmppAccountJsonServerInterface::handleConfigureDefaultAccountSettings, this, std::placeholders::_1);
   mFunctionMap["applySettings"] = std::bind(&XmppAccountJsonServerInterface::handleApplySettings, this, std::placeholders::_1);
   mFunctionMap["setHandler"] = std::bind(&XmppAccountJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["publishPresence"] = std::bind(&XmppAccountJsonServerInterface::handlePublishPresence, this, std::placeholders::_1);
   mFunctionMap["publishCannedPresence"] = std::bind(&XmppAccountJsonServerInterface::handlePublishCannedPresence, this, std::placeholders::_1);
   mFunctionMap["enable"] = std::bind(&XmppAccountJsonServerInterface::handleEnable, this, std::placeholders::_1);
   mFunctionMap["disable"] = std::bind(&XmppAccountJsonServerInterface::handleDisable, this, std::placeholders::_1);
   mFunctionMap["destroy"] = std::bind(&XmppAccountJsonServerInterface::handleDestroy, this, std::placeholders::_1);
   mFunctionMap["setNetworkRestriction"] = std::bind(&XmppAccountJsonServerInterface::handleSetNetworkRestriction, this, std::placeholders::_1);
   mFunctionMap["blockIncomingPresence"] = std::bind(&XmppAccountJsonServerInterface::handleBlockIncomingPresence, this, std::placeholders::_1);
   mFunctionMap["setHibernationState"] = std::bind(&XmppAccountJsonServerInterface::handleSetHibernationState, this, std::placeholders::_1);
   mFunctionMap["getEntityTime"] = std::bind(&XmppAccountJsonServerInterface::handleGetEntityTime, this, std::placeholders::_1);
   mFunctionMap["enableNotification"] = std::bind(&XmppAccountJsonServerInterface::handleEnableNotification, this, std::placeholders::_1);
   mFunctionMap["disableNotification"] = std::bind(&XmppAccountJsonServerInterface::handleDisableNotification, this, std::placeholders::_1);
   mFunctionMap["getPrivateStorageData"] = std::bind(&XmppAccountJsonServerInterface::handleGetPrivateStorageData, this, std::placeholders::_1);
   mFunctionMap["setPrivateStorageData"] = std::bind(&XmppAccountJsonServerInterface::handleSetPrivateStorageData, this, std::placeholders::_1);
   mFunctionMap["requestStateAllAccounts"] = std::bind(&XmppAccountJsonServerInterface::handleRequestStateAllAccounts, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppAccountJsonServerInterface::~XmppAccountJsonServerInterface()
{
}

void XmppAccountJsonServerInterface::Release()
{
}

void XmppAccountJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppAccountJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppAccountJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppAccountJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppAccountJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppAccountJsonServerInterface::handleCreate(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->create(account, XmppAccountSettings());

   StackLog(<< "XmppAccountJsonServerInterface::handleCreate(): account: " << account);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleCreateWithSettings(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   XmppAccountSettings settings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(settings));

   DebugLog(<< "XmppAccountJsonServerInterface::handleCreateWithSettings(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   
   resip::Data username(settings.username.c_str(), settings.username.size());
   std::string password(resip::Data(settings.password.c_str()).base64decode().c_str());
   if (password.size() >= username.base64encode().size())
      settings.password.assign(resip::Data(password.substr(username.base64encode().size(), std::string::npos)).base64decode().c_str());

   // TODO: Note that currently the account handle is generated on the client application. This creates the possibility
   // that the same XMPP account, in context or user and password, could have two different handles. As such, it is
   // necessary to block account creation of an XMPP account that is already mapped to another user handle.
   dynamic_cast<XmppAccountManagerInternal*>(xmppAccountManager)->create(account, settings);
   xmppAccountManager->setHandler(account, (XmppAccountHandler*)0xDEADBEFF); // note: this ends in FF, meaning that we don't want callbacks at all

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleConfigureDefaultAccountSettings(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   DebugLog(<< "XmppAccountJsonServerInterface::handleConfigureDefaultAccountSettings(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone);
   XmppAccountHandle account = -1;
   XmppAccountSettings settings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(settings));

   resip::Data username(settings.username.c_str(), settings.username.size());
   std::string password(resip::Data(settings.password.c_str()).base64decode().c_str());
   if (password.size() >= username.base64encode().size())
      settings.password.assign(resip::Data(password.substr(username.base64encode().size(), std::string::npos)).base64decode().c_str());

   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->configureDefaultAccountSettings(account, settings);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleApplySettings(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   DebugLog(<< "XmppAccountJsonServerInterface::handleApplySettings(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->applySettings(account);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   bool release = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(release));

   StackLog(<< "XmppAccountJsonServerInterface::handleSetHandler(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << (release ? " releasing" : " creating") << " account handler for account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->setHandler(account, (release ? NULL : (XmppAccountHandler*)0xDEADBEFF)); // note: this ends in FF, meaning that we don't want callbacks at all

   return kSuccess;
}

int XmppAccountJsonServerInterface::handlePublishPresence(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   XmppRoster::ResourceItem resourceItem;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(resourceItem));

   DebugLog(<< "XmppAccountJsonServerInterface::handlePublishPresence(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->publishPresence(account, resourceItem.presenceType, resourceItem.presenceStatusText, resourceItem.userActivityGeneralType, resourceItem.userActivitySpecificType, resourceItem.userActivityText);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handlePublishCannedPresence(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   XmppRoster::XmppCannedPresence cannedPresence;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(cannedPresence));

   DebugLog(<< "XmppAccountJsonServerInterface::handlePublishCannedPresence(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->publishCannedPresence(account, cannedPresence.status, cannedPresence.note);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleEnable(const rapidjson::Value& functionObjectVal)
{
   DebugLog(<< "XmppAccountJsonServerInterface::handleEnable()");
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   xmppAccountManager->enable(account);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleDisable(const rapidjson::Value& functionObjectVal)
{
   DebugLog(<< "XmppAccountJsonServerInterface::handleDisable()");
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   xmppAccountManager->disable(account);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleDestroy(const rapidjson::Value& functionObjectVal)
{
   DebugLog(<< "XmppAccountJsonServerInterface::handleDestroy()");
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);
      
   XmppAccountHandle account = -1;
      
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));
      
   xmppAccountManager->destroy(account);
      
   return kSuccess;
}
   
int XmppAccountJsonServerInterface::handleSetNetworkRestriction(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   NetworkTransport transport = CPCAPI2::TransportNone;
   bool restricted = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(transport), JSON_VALUE(restricted));

   DebugLog(<< "XmppAccountJsonServerInterface::handleSetNetworkRestriction(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->setNetworkRestriction(account, transport, restricted);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleBlockIncomingPresence(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   bool block = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(block));

   DebugLog(<< "XmppAccountJsonServerInterface::handleBlockIncomingPresence(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->blockIncomingPresence(account, block);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleSetHibernationState(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   bool active = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(active));

   DebugLog(<< "XmppAccountJsonServerInterface::handleSetHibernationState(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->setHibernationState(account, active);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleGetEntityTime(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   cpc::string jid = "";

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(jid));

   DebugLog(<< "XmppAccountJsonServerInterface::handleGetEntityTime(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->getEntityTime(account, jid);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleEnableNotification(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   cpc::string node = "";
   XmppAccount::XmppDataForm dataform;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(node), JSON_VALUE(dataform));

   DebugLog(<< "XmppAccountJsonServerInterface::handleEnableNotification(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->enableNotification(account, node, dataform);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleDisableNotification(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   cpc::string node = "";

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(node));

   DebugLog(<< "XmppAccountJsonServerInterface::handleDisableNotification(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->disableNotification(account, node);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleGetPrivateStorageData(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   cpc::string node = "";

   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   DebugLog(<< "XmppAccountJsonServerInterface::handleGetPrivateStorageData(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->getPrivateStorageData(account);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleSetPrivateStorageData(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAccountManager = XmppAccountManager::getInterface(mPhone);

   XmppAccountHandle account = -1;
   cpc::vector<XmppStorageData> data;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(data));

   DebugLog(<< "XmppAccountJsonServerInterface::handleSetPrivateStorageData(): " << this << " XmppAccountManager: " << xmppAccountManager << " mPhone: " << mPhone << " account: " << account);
   dynamic_cast<XmppAccountInterface*>(xmppAccountManager)->setPrivateStorageData(account, data);

   return kSuccess;
}

int XmppAccountJsonServerInterface::handleRequestStateAllAccounts(const rapidjson::Value& functionObjectVal)
{
   XmppAccountManager* xmppAcctManager = XmppAccountManager::getInterface(mPhone);
   XmppAccountStateManager* acctStateMgr = XmppAccountStateManager::getInterface(xmppAcctManager);

   DebugLog(<< "XmppAccountJsonServerInterface::handleRequestStateAllAccounts(): " << this << " XmppAccountManager: " << xmppAcctManager << " XmppAccountStateManager: " << acctStateMgr << " mPhone: " << mPhone);
   JsonProxyAccountStateEvent args;
   acctStateMgr->getStateAllAccounts(args.accountState);
   
   for (cpc::vector<XmppAccountState>::iterator i = args.accountState.begin(); i != args.accountState.end(); i++)
   {
      std::string password1(resip::Data((*i).settings.password.c_str(), (*i).settings.password.size()).base64encode().c_str());
      std::string password2(resip::Data((*i).settings.username.c_str(), (*i).settings.username.size()).base64encode().append(password1.c_str(), password1.size()).c_str());
      (*i).settings.password.assign(resip::Data(password2.c_str(), password2.size()).base64encode().c_str());
   }

   JsonFunctionCall(mTransport, "onAccountState", JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args)
{
   // Ignore internal handler callback
   //
   // JsonFunctionCall(mTransport, "onAccountConfigured", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args)
{
   JsonFunctionCall(mTransport, "onAccountStatusChanged", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}
   
int XmppAccountJsonServerInterface::onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onLicensingError", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args)
{
   JsonFunctionCall(mTransport, "onEntityTime", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args)
{
   JsonFunctionCall(mTransport, "onEntityFeature", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onStreamManagementState(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::StreamManagementStateEvent& args)
{
   JsonFunctionCall(mTransport, "onStreamManagementState", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

int XmppAccountJsonServerInterface::onPrivateStorageData(XmppAccountHandle account, const PrivateStorageDataEvent& args)
{
   JsonFunctionCall(mTransport, "onPrivateStorageData", JSON_VALUE(account), JSON_VALUE(args));
   return kSuccess;
}

}

}

#endif
