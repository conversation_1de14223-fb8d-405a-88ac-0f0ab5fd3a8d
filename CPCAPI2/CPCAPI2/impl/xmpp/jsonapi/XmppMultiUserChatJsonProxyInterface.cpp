#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppMultiUserChatJsonProxyInterface.h"
#include "XmppAccountJsonProxyInterface.h"
#include "xmpp/XmppMultiUserChatManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"

#include <rutil/Random.hxx>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT

#define JSON_MODULE "XmppMultiUserChatJsonApi"

namespace CPCAPI2
{

namespace XmppMultiUserChat
{

XmppMultiUserChatJsonProxyInterface::XmppMultiUserChatJsonProxyInterface(Phone* phone) :
mPhone(static_cast<PhoneInterface*>(phone)),
mTransport(NULL),
mStateHandler(NULL),
mNextMessageHandle(1),
mHandlePrefix(0)
{
   mFunctionMap["onCreateMultiUserChatResult"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleCreateMultiUserChatResult, this, std::placeholders::_1);
   mFunctionMap["onServiceAvailability"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleServiceAvailability, this, std::placeholders::_1);
   mFunctionMap["onRoomListRetrieved"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleRoomListRetrieved, this, std::placeholders::_1);
   mFunctionMap["onParticipantAdded"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleParticipantAdded, this, std::placeholders::_1);
   mFunctionMap["onParticipantRemoved"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleParticipantRemoved, this, std::placeholders::_1);
   mFunctionMap["onParticipantUpdated"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleParticipantUpdated, this, std::placeholders::_1);
   mFunctionMap["onParticipantSelfUpdated"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleParticipantSelfUpdated, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatReady"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatReady, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatSubjectChanged"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatSubjectChanged, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatNewMessage"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatNewMessage, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatNewReaction"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatNewReaction, this, std::placeholders::_1);
   mFunctionMap["onSendMessageSuccess"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleSendMessageSuccess, this, std::placeholders::_1);
   mFunctionMap["onSendMessageFailure"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleSendMessageFailure, this, std::placeholders::_1);
   mFunctionMap["onParticipantChatStateReceived"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleParticipantChatStateReceived, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatInvitationReceived"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatInvitationReceived, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatInvitationDeclined"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatInvitationDeclined, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatError"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatError, this, std::placeholders::_1);
   mFunctionMap["onLocalUserLeft"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleLocalUserLeft, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatConfigurationRequested"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatConfigurationRequested, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatRoomStateChanged"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatRoomStateChanged, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatListRequested"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatListRequested, this, std::placeholders::_1);
   mFunctionMap["onRoomBookmarksReceived"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleRoomBookmarksReceived, this, std::placeholders::_1);
   mFunctionMap["onNewRoomHandle"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleNewRoomHandle, this, std::placeholders::_1);
   mFunctionMap["onMultiUserChatState"] = std::bind(&XmppMultiUserChatJsonProxyInterface::handleMultiUserChatState, this, std::placeholders::_1);
   
   JsonApi::JsonApiClientInterface* jsonApiClientIf = static_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mHandlePrefix = resip::Random::getCryptoRandom();
   while ((mHandlePrefix & 0xFFFF0000) == 0)
   {
      mHandlePrefix = (mHandlePrefix + 1) << 1;
   }
   mHandlePrefix = (mHandlePrefix & 0xFFFF0000);
   
   mPhone->addRefImpl();
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::XmppMultiUserChatJsonProxyInterface(): " << this << " phone: " << mPhone);
}

XmppMultiUserChatJsonProxyInterface::~XmppMultiUserChatJsonProxyInterface()
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::~XmppMultiUserChatJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppMultiUserChatJsonProxyInterface::Release()
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

void XmppMultiUserChatJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppMultiUserChatJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

void XmppMultiUserChatJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   static_cast<XmppAccount::XmppAccountJsonProxyInterface*>(XmppAccount::XmppAccountJsonProxyInterface::getInterface(mPhone))->postCallbackPub(fp);
}

// JsonApiClientModule
void XmppMultiUserChatJsonProxyInterface::setTransport(JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppMultiUserChatJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::processIncomingImpl()");
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppMultiUserChatJsonProxyInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler)
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface setHandler " << account << " -> " << handler);
   resip::ReadCallbackBase* f = resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      XmppAccount::XmppAccountManagerJsonProxy::getInterface(mPhone)->process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler)
{
   StackLog(<< __func__);

   mAppHandlers[account] = handler;
   bool release = (handler ? false : true);

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account), JSON_VALUE(release));
}

int XmppMultiUserChatJsonProxyInterface::getRoomList(XmppAccount::XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::getRoomListImpl, this, account));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::getRoomListImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "getRoomList", JSON_VALUE(account));
}

int XmppMultiUserChatJsonProxyInterface::setRoomBookmarks(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setRoomBookmarksImpl, this, account, bookmarks));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::setRoomBookmarksImpl(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "setRoomBookmarks", JSON_VALUE(account), JSON_VALUE(bookmarks));
}

int XmppMultiUserChatJsonProxyInterface::getRoomBookmarks(XmppAccount::XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::getRoomBookmarksImpl, this, account));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::getRoomBookmarksImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "getRoomBookmarks", JSON_VALUE(account));
}

XmppMultiUserChatHandle XmppMultiUserChatJsonProxyInterface::create(XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   InfoLog(<< "XmppMultiUserChatJsonProxyInterface::create(): account: " << account << " room: " << room);
   XmppMultiUserChatHandle h = (XmppMultiUserChatHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::createImpl, this, h, account, room));
   return h;
}

void XmppMultiUserChatJsonProxyInterface::createImpl(XmppMultiUserChatHandle muc, XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   StackLog(<< __func__);
   mMapChatHandleToAccountHandle[muc] = account;
   JsonFunctionCall(mTransport, "create2", JSON_VALUE(muc), JSON_VALUE(account), JSON_VALUE(room));
}

XmppMultiUserChatHandle XmppMultiUserChatJsonProxyInterface::create(XmppAccount::XmppAccountHandle account, bool instantRoom)
{
   InfoLog(<< "XmppMultiUserChatJsonProxyInterface::create(): *** deprecated");
   assert(false);
   return 0;
}

int XmppMultiUserChatJsonProxyInterface::destroyRoom(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::destroyRoomImpl, this, handle, reason, alternate, password));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::destroyRoomImpl(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "destroyRoom", JSON_VALUE(handle), JSON_VALUE(reason), JSON_VALUE(alternate), JSON_VALUE(password));
}

int XmppMultiUserChatJsonProxyInterface::getRoomInfo(XmppMultiUserChatHandle handle)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::getRoomInfoImpl, this, handle));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::getRoomInfoImpl(XmppMultiUserChatHandle handle)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "getRoomInfo", JSON_VALUE(handle));
}

int XmppMultiUserChatJsonProxyInterface::getRoomsInfo(const cpc::vector<XmppMultiUserChatHandle>& handles)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::getRoomsInfoImpl, this, handles));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::getRoomsInfoImpl(const cpc::vector<XmppMultiUserChatHandle>& handles)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "getRoomsInfo", JSON_VALUE(handles));
}

int XmppMultiUserChatJsonProxyInterface::accept(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::acceptImpl, this, handle, nickname, historyRequester, historyToAdd));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::acceptImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "accept", JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));
}

int XmppMultiUserChatJsonProxyInterface::decline(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::declineImpl, this, handle, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::declineImpl(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "decline", JSON_VALUE(handle), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::join(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::joinImpl2, this, handle, config, nickname, password, historyRequester, historyToAdd));

   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::joinImpl2(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   JsonFunctionCall(mTransport, "join2", JSON_VALUE(handle), JSON_VALUE(config), JSON_VALUE(nickname), JSON_VALUE(password), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));
}

int XmppMultiUserChatJsonProxyInterface::join(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   InfoLog(<< __func__ << " deprecated");
   assert(0);

   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::joinImpl, this, handle, room, nickname, password, historyRequester, historyToAdd));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::joinImpl(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   JsonFunctionCall(mTransport, "join", JSON_VALUE(handle), JSON_VALUE(room), JSON_VALUE(nickname), JSON_VALUE(password), JSON_VALUE(historyRequester), JSON_VALUE(historyToAdd));
}

int XmppMultiUserChatJsonProxyInterface::leave(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::leaveImpl, this, handle, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::leaveImpl(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "leave", JSON_VALUE(handle), JSON_VALUE(reason));
}

XmppMultiUserChatMessageHandle XmppMultiUserChatJsonProxyInterface::sendMessage(XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html)
{
   XmppMultiUserChatMessageHandle message = mHandlePrefix | mNextMessageHandle++;
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::sendMessageImpl, this, message, handle, plain, html));
   return message;
}

void XmppMultiUserChatJsonProxyInterface::sendMessageImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "sendMessage", JSON_VALUE(message), JSON_VALUE(handle), JSON_VALUE(plain), JSON_VALUE(html));
}

XmppMultiUserChatMessageHandle XmppMultiUserChatJsonProxyInterface::sendReaction(XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   XmppMultiUserChatMessageHandle message = mHandlePrefix | mNextMessageHandle++;
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::sendReactionImpl, this, message, handle, target, reactions));
   return message;
}

void XmppMultiUserChatJsonProxyInterface::sendReactionImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "sendReaction", JSON_VALUE(message), JSON_VALUE(handle), JSON_VALUE(target), JSON_VALUE(reactions));
}

XmppMultiUserChatMessageHandle XmppMultiUserChatJsonProxyInterface::sendMessageRetraction(XmppMultiUserChatHandle handle, const cpc::string& target)
{
   XmppMultiUserChatMessageHandle message = mHandlePrefix | mNextMessageHandle++;
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::sendMessageRetractionImpl, this, message, handle, target));
   return message;
}

void XmppMultiUserChatJsonProxyInterface::sendMessageRetractionImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "sendMessageRetraction", JSON_VALUE(message), JSON_VALUE(handle), JSON_VALUE(target));
}

XmppMultiUserChatMessageHandle XmppMultiUserChatJsonProxyInterface::replaceMessage(XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html)
{
   XmppMultiUserChatMessageHandle message = mHandlePrefix | mNextMessageHandle++;
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::replaceMessageImpl, this, message, handle, replaces, plain, html));
   return message;
}

void XmppMultiUserChatJsonProxyInterface::replaceMessageImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "replaceMessage", JSON_VALUE(message), JSON_VALUE(handle), JSON_VALUE(replaces), JSON_VALUE(plain), JSON_VALUE(html));
}

int XmppMultiUserChatJsonProxyInterface::setIsComposingMessage(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setIsComposingMessageImpl, this, handle, refreshInterval, idleInterval));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::setIsComposingMessageImpl(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "setIsComposingMessage", JSON_VALUE(handle), JSON_VALUE(refreshInterval), JSON_VALUE(idleInterval));
}

int XmppMultiUserChatJsonProxyInterface::publishPresence(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::publishPresenceImpl, this, handle, presence, note));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::publishPresenceImpl(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "publishPresence", JSON_VALUE(handle), JSON_VALUE(presence), JSON_VALUE(note));
}

int XmppMultiUserChatJsonProxyInterface::changeNickname(XmppMultiUserChatHandle handle, const cpc::string& nickname)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::changeNicknameImpl, this, handle, nickname));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::changeNicknameImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "changeNickname", JSON_VALUE(handle), JSON_VALUE(nickname));
}

int XmppMultiUserChatJsonProxyInterface::changeSubject(XmppMultiUserChatHandle handle, const cpc::string& subject)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::changeSubjectImpl, this, handle, subject));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::changeSubjectImpl(XmppMultiUserChatHandle handle, const cpc::string& subject)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "changeSubject", JSON_VALUE(handle), JSON_VALUE(subject));
}

int XmppMultiUserChatJsonProxyInterface::invite(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::inviteImpl, this, handle, jid, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::inviteImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "invite", JSON_VALUE(handle), JSON_VALUE(jid), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::kick(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::kickImpl, this, handle, nickname, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::kickImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "kick", JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::ban(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::banImpl, this, handle, nickname, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::banImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "ban", JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::changeAffiliation(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::changeAffiliationImpl, this, handle, nickname, affiliation, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::changeAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "changeAffiliation", JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(affiliation), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::changeJidAffiliation(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::changeJidAffiliationImpl, this, handle, jid, affiliation, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::changeJidAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   StackLog(<< __func__);

   JsonFunctionCall(mTransport, "changeJidAffiliation", JSON_VALUE(handle), JSON_VALUE(jid), JSON_VALUE(affiliation), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::changeRole(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::changeRoleImpl, this, handle, nickname, role, reason));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::changeRoleImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "changeRole", JSON_VALUE(handle), JSON_VALUE(nickname), JSON_VALUE(role), JSON_VALUE(reason));
}

int XmppMultiUserChatJsonProxyInterface::requestConfigurations(XmppMultiUserChatHandle handle)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::requestConfigurationsImpl, this, handle));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::requestConfigurationsImpl(XmppMultiUserChatHandle handle)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "requestConfigurations", JSON_VALUE(handle));
}

int XmppMultiUserChatJsonProxyInterface::setConfigurations(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setConfigurationsImpl, this, handle, dataform));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "setConfigurations", JSON_VALUE(handle), JSON_VALUE(dataform));
}

int XmppMultiUserChatJsonProxyInterface::setConfigurations(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setConfigurationsDeprecatedImpl, this, handle, configurations));
   return kSuccess;
}
   
void XmppMultiUserChatJsonProxyInterface::setConfigurationsDeprecatedImpl(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "setConfigurationsDeprecated", JSON_VALUE(handle), JSON_VALUE(configurations));
}

int XmppMultiUserChatJsonProxyInterface::requestList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::requestListImpl, this, handle, type));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::requestListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "requestList", JSON_VALUE(handle), JSON_VALUE(type));
}

int XmppMultiUserChatJsonProxyInterface::setList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setListImpl, this, handle, type, items));
   return kSuccess;
}

void XmppMultiUserChatJsonProxyInterface::setListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items)
{
   StackLog(<< __func__);
   JsonFunctionCall(mTransport, "setList", JSON_VALUE(handle), JSON_VALUE(type), JSON_VALUE(items));
}

int XmppMultiUserChatJsonProxyInterface::handleCreateMultiUserChatResult(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChat::XmppMultiUserChatHandle handle = -1;
   XmppMultiUserChatCreatedResultEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   // we do this here for the case of an invitation
   mMapChatHandleToAccountHandle[handle] = args.account;

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleServiceAvailability(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppAccount::XmppAccountHandle account = -1;
   ServiceAvailabilityEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));

   std::map<XmppAccount::XmppAccountHandle, XmppMultiUserChatHandler*>::const_iterator itAccount = mAppHandlers.find(account);
   if (itAccount == mAppHandlers.end()) return kError;

   postCallback(makeFpCommand(XmppMultiUserChatHandler::onServiceAvailability, itAccount->second, account, args));

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleRoomListRetrieved(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppAccount::XmppAccountHandle account = -1;
   RoomListRetrievedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));

   std::map<XmppAccount::XmppAccountHandle, XmppMultiUserChatHandler*>::const_iterator itAccount = mAppHandlers.find(account);
   if (itAccount == mAppHandlers.end()) return kError;

   postCallback(makeFpCommand(XmppMultiUserChatHandler::onRoomListRetrieved, itAccount->second, account, args));

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleParticipantAdded(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   ParticipantAddedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onParticipantAdded, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleParticipantRemoved(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   ParticipantRemovedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onParticipantRemoved, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleParticipantUpdated(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   ParticipantUpdatedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onParticipantUpdated, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleParticipantSelfUpdated(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   ParticipantSelfUpdatedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onParticipantSelfUpdated, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatReady(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatReadyEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatReady, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatSubjectChanged(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatSubjectChangedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatSubjectChanged, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatNewMessage(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatNewMessageEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatNewMessage, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatNewReaction(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatNewReactionEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatNewReaction, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatNewMessageRetraction(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatNewMessageRetractionEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatNewMessageRetraction, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleSendMessageSuccess(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   SendMessageSuccessEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onSendMessageSuccess, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleSendMessageFailure(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   SendMessageFailureEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onSendMessageFailure, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleParticipantChatStateReceived(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   ParticipantChatStateEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onParticipantChatStateReceived, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatInvitationReceived(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChatHandle handle = -1;
   MultiUserChatInvitationReceivedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));
   StackLog(<< __func__ << ": muc handle: " << handle << " roomjid: " << args.roomjid << " jid: " << args.jid);

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatInvitationReceived, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatInvitationDeclined(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatInvitationDeclinedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatInvitationDeclined, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatErrorEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatError, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleLocalUserLeft(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   LocalUserLeftEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onLocalUserLeft, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatConfigurationRequested(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatConfigurationRequestedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatConfigurationRequested, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatRoomStateChanged(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatRoomStateChangedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatRoomStateChanged, handler, handle, args));
   }

   return kSuccess;
}

int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatListRequested(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);

   XmppMultiUserChatHandle handle = -1;
   MultiUserChatListRequestedEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));

   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onMultiUserChatListRequested, handler, handle, args));
   }

   return kSuccess;
}
   
int XmppMultiUserChatJsonProxyInterface::handleRoomBookmarksReceived(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);
   
   XmppAccount::XmppAccountHandle account = -1;
   RoomBookmarksReceivedEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   std::map<XmppAccount::XmppAccountHandle, XmppMultiUserChatHandler*>::const_iterator itAccount = mAppHandlers.find(account);
   if (itAccount == mAppHandlers.end()) return kError;
   
   postCallback(makeFpCommand(XmppMultiUserChatHandler::onRoomBookmarksReceived, itAccount->second, account, args));
   
   return kSuccess;
   
}
   
int XmppMultiUserChatJsonProxyInterface::handleNewRoomHandle(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< __func__);
      
   XmppMultiUserChatHandle handle = -1;
   NewRoomEvent args;
      
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle), JSON_VALUE(args));
   
   // Note: Mapping should not need to be updated for this muc handle, as create result should have updated it already
   // mMapChatHandleToAccountHandle[handle] = args.account;
   
   if (XmppMultiUserChatHandler* handler = findAppHandler(handle))
   {
      postCallback(makeFpCommand(XmppMultiUserChatHandler::onNewRoomHandle, handler, handle, args));
   }
      
   return kSuccess;
}   
   
int XmppMultiUserChatJsonProxyInterface::handleMultiUserChatState(const rapidjson::Value& functionObjectVal)
{
   XmppMultiUserChat::JsonProxyMultiUserChatStateEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));
   
   // Note: Account mapping needs to be updated, as create results will not be received for existing accounts
   for (cpc::vector<XmppMultiUserChatStateInfo>::iterator i = args.states.begin(); i != args.states.end(); ++i)
   {
      mMapChatHandleToAccountHandle[(*i).muc] = (*i).state.account;
   }
      
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::handleMultiUserChatState(): mStateHandler: " << mStateHandler << " state list: " << args.states.size());
   if (mStateHandler != NULL)
   {
      // StackLog(<< "XmppMultiUserChatJsonProxyInterface::handleMultiUserChatState(): mStateHandler: " << mStateHandler << " initiating postCallback");
      postCallback(makeFpCommand1(XmppMultiUserChatJsonProxyStateHandler::onMultiUserChatState, mStateHandler, args));
   }
      
   return kSuccess;
}
   
int XmppMultiUserChatJsonProxyInterface::setStateHandler(XmppMultiUserChatJsonProxyStateHandler* handler)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::setStateHandlerImpl, this, handler));
   return kSuccess;
}
   
void XmppMultiUserChatJsonProxyInterface::setStateHandlerImpl(XmppMultiUserChatJsonProxyStateHandler* handler)
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::setStateHandlerImpl(): handler: " << handler);
   mStateHandler = handler;
}
   
int XmppMultiUserChatJsonProxyInterface::requestMultiUserChatState(XmppMultiUserChatHandle handle)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateImpl, this, handle));
   return kSuccess;
}
   
void XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateImpl(XmppMultiUserChatHandle muc)
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateImpl(): muc: " << muc);
   JsonFunctionCall(mTransport, "requestMultiUserChatState", JSON_VALUE(muc));
}
   
int XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateForAccount(XmppAccount::XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateForAccountImpl, this, account));
   return kSuccess;
}
   
void XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateForAccountImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::requestMultiUserChatStateForAccountImpl(): account: " << account);
   JsonFunctionCall(mTransport, "requestMultiUserChatStateForAccount", JSON_VALUE(account));
}
   
int XmppMultiUserChatJsonProxyInterface::requestAllMultiUserChatState()
{
   post(resip::resip_bind(&XmppMultiUserChatJsonProxyInterface::requestAllMultiUserChatStateImpl, this));
   return kSuccess;
}
   
void XmppMultiUserChatJsonProxyInterface::requestAllMultiUserChatStateImpl()
{
   StackLog(<< "XmppMultiUserChatJsonProxyInterface::requestAllMultiUserChatStateImpl()");
   JsonFunctionCall(mTransport, "requestAllMultiUserChatState");
}

XmppMultiUserChatHandler* XmppMultiUserChatJsonProxyInterface::findAppHandler(XmppMultiUserChatHandle handle) const
{
   std::map<XmppMultiUserChatHandle, XmppAccount::XmppAccountHandle>::const_iterator itChat = mMapChatHandleToAccountHandle.find(handle);
   if (itChat == mMapChatHandleToAccountHandle.end())
   {
      DebugLog(<< __func__ << ": no account handle found for muc handle: " << handle);
      return NULL;
   }

   std::map<XmppAccount::XmppAccountHandle, XmppMultiUserChatHandler*>::const_iterator itAccount = mAppHandlers.find(itChat->second);
   if (itAccount == mAppHandlers.end())
   {
      DebugLog(<< __func__ << ": no muc handler found for account: " << itChat->second << " muc: " << handle);
      return NULL;
   }

   return itAccount->second;
}

int XmppMultiUserChatJsonProxyInterface::notifyMessageRead(XmppMultiUserChatHandle handle, const cpc::string& messageId)
{
   return kSuccess;
}

}

}

#endif
