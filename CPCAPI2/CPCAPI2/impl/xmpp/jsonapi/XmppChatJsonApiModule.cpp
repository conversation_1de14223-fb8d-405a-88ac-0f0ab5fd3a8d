#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppChatJsonProxy.h"
#include "interface/experimental/xmpp/XmppChatJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppChatJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppChatJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace XmppChat
   {
      XmppChatJsonApi* XmppChatJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppChatJsonServerInterface>(phone, "XmppChatJsonApi");
#else
         return NULL;
#endif
      }

      XmppChatManagerJsonProxy* XmppChatManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppChatJsonProxyInterface>(phone, "XmppChatManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
