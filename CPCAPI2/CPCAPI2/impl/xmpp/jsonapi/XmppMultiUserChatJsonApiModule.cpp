#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppMultiUserChatJsonProxy.h"
#include "interface/experimental/xmpp/XmppMultiUserChatJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppMultiUserChatJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppMultiUserChatJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace XmppMultiUserChat
   {
      XmppMultiUserChatJsonApi* XmppMultiUserChatJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppMultiUserChatJsonServerInterface>(phone, "XmppMultiUserChatJsonApi");
#else
         return NULL;
#endif
      }

      XmppMultiUserChatManagerJsonProxy* XmppMultiUserChatManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppMultiUserChatJsonProxyInterface>(phone, "XmppMultiUserChatManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
