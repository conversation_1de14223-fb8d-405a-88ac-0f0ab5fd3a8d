#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_JSON_INTERFACE_H)
#define CPCAPI2_XMPP_ROSTER_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/xmpp/XmppRosterJsonApi.h"
#include "xmpp/XmppRosterSyncHandler.h"
#include "xmpp/XmppRosterHandlerInternal.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppRoster
{

class XmppRosterJsonServerInterface : public CPCAPI2::XmppRoster::XmppRosterHandlerInternal,
                                      public CPCAPI2::XmppRoster::XmppRosterJsonApi,
                                      public CPCAPI2::XmppRoster::XmppRosterSyncHand<PERSON>,
                                      public CPCAPI2::JsonApi::JsonApiServerModule,
                                      public CPCAPI2::PhoneModule
{

public:

   XmppRosterJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~XmppRosterJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppRosterHandlerInternal
   virtual int onRosterUpdate(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& args) OVERRIDE;
   virtual int onSubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& args) OVERRIDE;
   virtual int onUnsubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterUnsubscriptionRequestEvent& args) OVERRIDE;
   virtual int onRosterPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterPresenceEvent& args) OVERRIDE;
   virtual int onSelfPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::ErrorEvent& args) OVERRIDE;
   virtual int onCreateRosterResult(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterCreatedResultEvent& args) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleAcceptSubscriptionRequest(const rapidjson::Value& functionObjectVal);
   int handleRejectSubscriptionRequest(const rapidjson::Value& functionObjectVal);
   int handleCancelAcceptedSubscription(const rapidjson::Value& functionObjectVal);
   int handleAddRosterItem(const rapidjson::Value& functionObjectVal);
   int handleUpdateRosterItem(const rapidjson::Value& functionObjectVal);
   int handleRemoveRosterItem(const rapidjson::Value& functionObjectVal);
   int handleSubscribePresence(const rapidjson::Value& functionObjectVal);
   int handleUnsubscribePresence(const rapidjson::Value& functionObjectVal);
   int handleGetRosterState(const rapidjson::Value& functionObjectVal);
   int handleRequestRosterState(const rapidjson::Value& functionObjectVal);
   int handleRequestRosterStateForAccount(const rapidjson::Value& functionObjectVal);
   int handleRequestAllRosterState(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_XMPP_ROSTER_JSON_INTERFACE_H
