#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppAccountJsonProxyInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppAccountJsonProxyStateHandler.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/cpc_logger.h"

#include <rutil/Random.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ACCOUNT
#define JSON_MODULE "XmppAccountJsonApi"

namespace CPCAPI2
{

namespace XmppAccount
{

XmppAccountJsonProxyInterface::XmppAccountJsonProxyInterface(Phone* phone) :
   CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppAccountHandler, XmppAccountJsonSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mPhone(dynamic_cast<PhoneInterface*>(phone)), mTransport(NULL),
   mStateHandler(NULL),
   mRemoteSyncHelperIf(NULL)
{
   mFunctionMap["onAccountConfigured"] = std::bind(&XmppAccountJsonProxyInterface::handleAccountConfigured, this, std::placeholders::_1);
   mFunctionMap["onAccountStatusChanged"] = std::bind(&XmppAccountJsonProxyInterface::handleAccountStatusChanged, this, std::placeholders::_1);
   mFunctionMap["onError"] = std::bind(&XmppAccountJsonProxyInterface::handleError, this, std::placeholders::_1);
   mFunctionMap["onLicensingError"] = std::bind(&XmppAccountJsonProxyInterface::handleLicensingError, this, std::placeholders::_1);
   mFunctionMap["onEntityTime"] = std::bind(&XmppAccountJsonProxyInterface::handleEntityTime, this, std::placeholders::_1);
   mFunctionMap["onEntityFeature"] = std::bind(&XmppAccountJsonProxyInterface::handleEntityFeature, this, std::placeholders::_1);
   mFunctionMap["onStreamManagementState"] = std::bind(&XmppAccountJsonProxyInterface::handleStreamManagementState, this, std::placeholders::_1);
   mFunctionMap["onPrivateStorageData"] = std::bind(&XmppAccountJsonProxyInterface::handlePrivateStorageData, this, std::placeholders::_1);
   mFunctionMap["onAccountState"] = std::bind(&XmppAccountJsonProxyInterface::handleAccountState, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mPhone->addRefImpl();
   StackLog(<< "XmppAccountJsonProxyInterface::XmppAccountJsonProxyInterface(): " << this << " phone: " << mPhone);
}

XmppAccountJsonProxyInterface::~XmppAccountJsonProxyInterface()
{
   StackLog(<< "XmppAccountJsonProxyInterface::~XmppAccountJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppAccountJsonProxyInterface::Release()
{
   StackLog(<< "XmppAccountJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

// JsonApiClientModule
void XmppAccountJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppAccountJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppAccountJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppAccountJsonProxyInterface::processIncomingImpl(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      DebugLog(<< "XmppAccountJsonProxyInterface::processIncomingImpl(): triggering " << funcName << " callback");
      it->second(functionObjectVal);
   }
}
   
int XmppAccountJsonProxyInterface::handleAccountConfigured(const rapidjson::Value& functionObjectVal)
{
   InfoLog(<< __func__ << "*** internal handler callback");
   assert(false);

   // Ignore internal handler callback
   //
   // StackLog(<< "XmppAccountJsonProxyInterface::handleAccountConfigured(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   // XmppAccountHandle account = -1;
   // XmppAccountConfiguredEvent args;
   //
   // JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   //
   // fireEvent(cpcFunc(XmppAccountHandlerInternal::onAccountConfigured), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleAccountStatusChanged(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleAccountStatusChanged(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   XmppAccountStatusChangedEvent args;
      
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onAccountStatusChanged), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleError(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   ErrorEvent args;
      
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onError), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleLicensingError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleLicensingError(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   LicensingErrorEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onLicensingError), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleEntityTime(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleEntityTime(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   EntityTimeEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onEntityTime), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleEntityFeature(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleEntityFeature(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   EntityFeatureEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onEntityFeature), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleStreamManagementState(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handleStreamManagementState(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   StreamManagementStateEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onStreamManagementState), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handlePrivateStorageData(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAccountJsonProxyInterface::handlePrivateStorageData(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   XmppAccountHandle account = -1;
   PrivateStorageDataEvent args;
   
   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(args));
   
   fireEvent(cpcFunc(XmppAccountHandler::onPrivateStorageData), account, args);
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::handleAccountState(const rapidjson::Value& functionObjectVal)
{
   XmppAccount::JsonProxyAccountStateEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(args));
   
   for (cpc::vector<XmppAccountState>::iterator i = args.accountState.begin(); i != args.accountState.end(); i++)
   {
      resip::Data username((*i).settings.username.c_str(), (*i).settings.username.size());
      std::string password(resip::Data((*i).settings.password.c_str()).base64decode().c_str());
      if (password.size() >= username.base64encode().size())
         (*i).settings.password.assign(resip::Data(password.substr(username.base64encode().size(), std::string::npos)).base64decode().c_str());
   }
   
   if (mStateHandler != NULL)
   {
      postCallback(resip::resip_bind(&XmppAccountJsonProxyStateHandler::onAccountState, mStateHandler, 0, args));
   }
   return kSuccess;
}

// XmppAccountManager
XmppAccountHandle XmppAccountJsonProxyInterface::create()
{
   StackLog(<< "XmppAccountJsonProxyInterface::create()");
   XmppAccountHandle h = (XmppAccountHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::createImpl, this, h));
   return h;
 }

int XmppAccountJsonProxyInterface::createImpl(XmppAccountHandle account)
{
   JsonFunctionCall(mTransport, "create", JSON_VALUE(account));
   return kSuccess;
}

XmppAccountHandle XmppAccountJsonProxyInterface::create(const XmppAccountSettings& settings)
{
   XmppAccountHandle h = (XmppAccountHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::createWithSettingsImpl, this, h, settings));
   return h;
}

int XmppAccountJsonProxyInterface::createWithSettingsImpl(XmppAccountHandle account, const XmppAccountSettings& settings_)
{
   StackLog(<< "XmppAccountJsonProxyInterface::createWithSettingsImpl(): account: " << account << " with settings");

   XmppAccountSettings settings(settings_);
   std::string password1(resip::Data(settings_.password.c_str(), settings_.password.size()).base64encode().c_str());
   std::string password2(resip::Data(settings_.username.c_str(), settings_.username.size()).base64encode().append(password1.c_str(), password1.size()).c_str());
   settings.password.assign(resip::Data(password2.c_str(), password2.size()).base64encode().c_str());

   JsonFunctionCall(mTransport, "createWithSettings", JSON_VALUE(account), JSON_VALUE(settings));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::configureDefaultAccountSettings(XmppAccountHandle account, const XmppAccountSettings& settings)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::configureDefaultAccountSettingsImpl, this, account, settings));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::configureDefaultAccountSettingsImpl(XmppAccountHandle account, const XmppAccountSettings& settings_)
{
   StackLog(<< "XmppAccountJsonProxyInterface::configureDefaultAccountSettingsImpl(): account: " << account);

   XmppAccountSettings settings(settings_);
   std::string password1(resip::Data(settings_.password.c_str(), settings_.password.size()).base64encode().c_str());
   std::string password2(resip::Data(settings_.username.c_str(), settings_.username.size()).base64encode().append(password1.c_str(), password1.size()).c_str());
   settings.password.assign(resip::Data(password2.c_str(), password2.size()).base64encode().c_str());

   JsonFunctionCall(mTransport, "configureDefaultAccountSettings", JSON_VALUE(account), JSON_VALUE(settings));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::applySettings(XmppAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::applySettingsImpl, this, account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::applySettingsImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountJsonProxyInterface::applySettingsImpl(): account: " << account);
   JsonFunctionCall(mTransport, "applySettings", JSON_VALUE(account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppAccount::XmppAccountHandler* handler)
{
   StackLog(<< "XmppAccountJsonProxyInterface setHandler " << account << " -> " << handler);
   setAppHandler(account, handler);

   resip::ReadCallbackBase* f = resip::resip_bind(&XmppAccountJsonProxyInterface::setHandlerImpl, this, account, handler);
   postToSdkThread(f);

   return kSuccess;
}

int XmppAccountJsonProxyInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppAccount::XmppAccountHandler* handler)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setHandlerImpl(): handler list: " << mAppHandlers.size() << " adding handler: " << handler << " for account: " << account);

   bool release = (handler ? false : true);
   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account), JSON_VALUE(release));

   return kSuccess;
}

int XmppAccountJsonProxyInterface::publishPresence(XmppAccount::XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::publishPresenceImpl, this, account, presence, note, userActivityGeneralType, userActivitySpecificType, userActivityText));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::publishPresenceImpl(XmppAccount::XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText)
{
   StackLog(<< "XmppAccountJsonProxyInterface::publishPresenceImpl(): account: " << account);

   XmppRoster::ResourceItem resourceItem;
   resourceItem.presenceType = presence;
   resourceItem.presenceStatusText = note;
   resourceItem.userActivityGeneralType = userActivityGeneralType;
   resourceItem.userActivitySpecificType = userActivitySpecificType;
   resourceItem.userActivityText = userActivityText;

   JsonFunctionCall(mTransport, "publishPresence", JSON_VALUE(account), JSON_VALUE(resourceItem));

   return kSuccess;
}

int XmppAccountJsonProxyInterface::publishCannedPresence(XmppAccount::XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::publishCannedPresenceImpl, this, account, status, note));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::publishCannedPresenceImpl(XmppAccount::XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note)
{
   StackLog(<< "XmppAccountJsonProxyInterface::publishCannedPresenceImpl(): account: " << account);

   XmppRoster::XmppCannedPresence cannedPresence;
   cannedPresence.status = status;
   cannedPresence.note = note;

   JsonFunctionCall(mTransport, "publishCannedPresence", JSON_VALUE(account), JSON_VALUE(cannedPresence));

   return kSuccess;
}

int XmppAccountJsonProxyInterface::enable(XmppAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::enableImpl, this, account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::enableImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountJsonProxyInterface::enableImpl(): account: " << account);
   JsonFunctionCall(mTransport, "enable", JSON_VALUE(account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::disable(XmppAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::disableImpl, this, account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::disableImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountJsonProxyInterface::disableImpl(): account: " << account);
   JsonFunctionCall(mTransport, "disable", JSON_VALUE(account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::destroy(XmppAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::destroyImpl, this, account));
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::destroyImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountJsonProxyInterface::destroyImpl(): account: " << account);
   JsonFunctionCall(mTransport, "destroy", JSON_VALUE(account));
   return kSuccess;
}
   
int XmppAccountJsonProxyInterface::setNetworkRestriction(XmppAccountHandle account, NetworkTransport transport, bool restricted)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::setNetworkRestrictionImpl, this, account, transport, restricted));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setNetworkRestrictionImpl(XmppAccountHandle account, NetworkTransport transport, bool restricted)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setNetworkRestrictionImpl(): account: " << account);
   JsonFunctionCall(mTransport, "setNetworkRestriction", JSON_VALUE(account), JSON_VALUE(transport), JSON_VALUE(restricted));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::blockIncomingPresence(XmppAccountHandle account, bool block)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::blockIncomingPresenceImpl, this, account, block));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::blockIncomingPresenceImpl(XmppAccountHandle account, bool block)
{
   StackLog(<< "XmppAccountJsonProxyInterface::blockIncomingPresenceImpl(): account: " << account);
   JsonFunctionCall(mTransport, "blockIncomingPresence", JSON_VALUE(account), JSON_VALUE(block));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setInactive(XmppAccountHandle account, bool inactive)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::setInactiveImpl, this, account, inactive));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setInactiveImpl(XmppAccountHandle account, bool inactive)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setInactiveImpl(): account: " << account);
   JsonFunctionCall(mTransport, "setInactive", JSON_VALUE(account), JSON_VALUE(inactive));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setHibernationState(XmppAccountHandle account, bool active)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::setHibernationStateImpl, this, account, active));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setHibernationStateImpl(XmppAccountHandle account, bool active)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setHibernationStateImpl(): account: " << account);
   JsonFunctionCall(mTransport, "setHibernationState", JSON_VALUE(account), JSON_VALUE(active));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::getEntityTime(XmppAccountHandle account, const cpc::string& jid)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::getEntityTimeImpl, this, account, jid));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::getEntityTimeImpl(XmppAccountHandle account, const cpc::string& jid)
{
   StackLog(<< "XmppAccountJsonProxyInterface::getEntityTimeImpl(): account: " << account);
   JsonFunctionCall(mTransport, "getEntityTime", JSON_VALUE(account), JSON_VALUE(jid));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::enableNotification(XmppAccountHandle account, const cpc::string& node, const XmppAccount::XmppDataForm& dataform)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::enableNotificationImpl, this, account, node, dataform));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::enableNotificationImpl(XmppAccountHandle account, const cpc::string& node, const XmppAccount::XmppDataForm& dataform)
{
   StackLog(<< "XmppAccountJsonProxyInterface::enableNotificationImpl(): account: " << account);
   JsonFunctionCall(mTransport, "enableNotification", JSON_VALUE(account), JSON_VALUE(node), JSON_VALUE(dataform));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::disableNotification(XmppAccountHandle account, const cpc::string& node)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::disableNotificationImpl, this, account, node));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::disableNotificationImpl(XmppAccountHandle account, const cpc::string& node)
{
   StackLog(<< "XmppAccountJsonProxyInterface::disableNotificationImpl(): account: " << account);
   JsonFunctionCall(mTransport, "disableNotification", JSON_VALUE(account), JSON_VALUE(node));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::getPrivateStorageData(XmppAccountHandle account)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::getPrivateStorageDataImpl, this, account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::getPrivateStorageDataImpl(XmppAccountHandle account)
{
   StackLog(<< "XmppAccountJsonProxyInterface::getPrivateStorageDataImpl(): account: " << account);
   JsonFunctionCall(mTransport, "getPrivateStorageData", JSON_VALUE(account));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setPrivateStorageData(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::setPrivateStorageDataImpl, this, account, data));
   return kSuccess;
}

int XmppAccountJsonProxyInterface::setPrivateStorageDataImpl(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setPrivateStorageDataImpl(): account: " << account);
   JsonFunctionCall(mTransport, "setPrivateStorageData", JSON_VALUE(account), JSON_VALUE(data));
   return kSuccess;
}

RemoteSyncXmppHelper::RemoteSyncXmppHelper* XmppAccountJsonProxyInterface::getRemoteSyncHelper()
{
   if (mRemoteSyncHelperIf == NULL)
   {
      mRemoteSyncHelperIf = RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(mPhone);
   }
   return mRemoteSyncHelperIf;
}

cpc::string XmppAccountJsonProxyInterface::getRemoteSyncAccountID(XmppAccountHandle account)
{
   return getRemoteSyncHelper()->getRemoteSyncAccountID(account);
}

XmppAccountHandle XmppAccountJsonProxyInterface::getAccountHandleFromRemoteSyncID(cpc::string accountID)
{
   return getRemoteSyncHelper()->getAccountHandleFromRemoteSyncID(accountID);
}

int XmppAccountJsonProxyInterface::send(XmppAccountHandle account, const cpc::string& xml)
{
   // TODO:
   return kError;
}

int XmppAccountJsonProxyInterface::setStateHandler(XmppAccountJsonProxyStateHandler* handler)
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::setStateHandlerImpl, this, handler));
   return 0;
}

void XmppAccountJsonProxyInterface::setStateHandlerImpl(XmppAccountJsonProxyStateHandler* handler)
{
   StackLog(<< "XmppAccountJsonProxyInterface::setStateHandlerImpl(): handler: " << handler);
   mStateHandler = handler;
}

int XmppAccountJsonProxyInterface::requestStateAllAccounts()
{
   postToSdkThread(resip::resip_bind(&XmppAccountJsonProxyInterface::requestStateAllAccountsImpl, this));
   return 0;
}

int XmppAccountJsonProxyInterface::requestStateAllAccountsImpl()
{
   StackLog(<< "XmppAccountJsonProxyInterface::requestStateAllAccountsImpl()");
   JsonFunctionCall(mTransport, "requestStateAllAccounts");
   return kSuccess;
}
int XmppAccountJsonProxyInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<XmppAccountSettings>& outXmppAccountSettings)
{
   return kSuccess;
}


}

}

#endif
