#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_CHAT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppChatJsonServerInterface.h"
#include "xmpp/XmppChatManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

#include "util/cpc_logger.h"
#include "log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_CHAT
#define JSON_MODULE "XmppChatManagerJsonProxy"
#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{

namespace XmppChat
{

XmppChatJsonServerInterface::XmppChatJsonServerInterface(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mLocalLogger(mPhone->localLogger())
{
   XmppChatManager* xmppChat = XmppChatManager::getInterface(phone);
   XmppChatManagerInterface* xmppChatIf = dynamic_cast<XmppChatManagerInterface*>(xmppChat);
   xmppChatIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&XmppChatJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["createChat"] = std::bind(&XmppChatJsonServerInterface::handleCreateChat, this, std::placeholders::_1);
   mFunctionMap["addParticipant"] = std::bind(&XmppChatJsonServerInterface::handleAddParticipant, this, std::placeholders::_1);
   mFunctionMap["start"] = std::bind(&XmppChatJsonServerInterface::handleStart, this, std::placeholders::_1);
   mFunctionMap["end"] = std::bind(&XmppChatJsonServerInterface::handleEnd, this, std::placeholders::_1);
   mFunctionMap["sendMessage"] = std::bind(&XmppChatJsonServerInterface::handleSendMessage, this, std::placeholders::_1);
   mFunctionMap["accept"] = std::bind(&XmppChatJsonServerInterface::handleAccept, this, std::placeholders::_1);
   mFunctionMap["reject"] = std::bind(&XmppChatJsonServerInterface::handleReject, this, std::placeholders::_1);
   mFunctionMap["notifyMessageDelivered"] = std::bind(&XmppChatJsonServerInterface::handleNotifyMessageDelivered, this, std::placeholders::_1);
   mFunctionMap["notifyMessageDisplayed"] = std::bind(&XmppChatJsonServerInterface::handleNotifyMessageDisplayed, this, std::placeholders::_1);
   mFunctionMap["setIsComposingMessage"] = std::bind(&XmppChatJsonServerInterface::handleSetIsComposingMessage, this, std::placeholders::_1);
   mFunctionMap["validateChatHandle"] = std::bind(&XmppChatJsonServerInterface::handleValidateChatHandle, this, std::placeholders::_1);
   mFunctionMap["createMessage"] = std::bind(&XmppChatJsonServerInterface::handleCreateMessage, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppChatJsonServerInterface::~XmppChatJsonServerInterface()
{
}

void XmppChatJsonServerInterface::Release()
{
}

void XmppChatJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppChatJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppChatJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppChatJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppChatJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppChatJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   bool release = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(release));

   StackLog(<< "XmppChatJsonServerInterface::handleSetHandler(): " << (release ? "releasing" : "creating") << " chat handler for account: " << account);
   xmppChatManager->setHandler(account, (release ? NULL : (XmppChatHandler*)0xDEADBEFF)); // note: this ends in FF, meaning that we don't want callbacks at all
   return kSuccess;
}

int XmppChatJsonServerInterface::handleValidateChatHandle(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   XmppChat::XmppChatHandle chat = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(chat));

   StackLog(<< "XmppChatJsonServerInterface::handleValidateChatHandle(): chat: " << chat << " account: " << account);
   xmppChatManager->validateChatHandle(account, chat);

   return kSuccess;
}

int XmppChatJsonServerInterface::handleCreateChat(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   XmppChatHandle chat = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(account));

   dynamic_cast<XmppChatManagerInterface*>(xmppChatManager)->createChat(chat, account);
   StackLog(<< "XmppChatJsonServerInterface::handleCreateChat(): chat: " << chat << " account: " << account);

   return kSuccess;
}

int XmppChatJsonServerInterface::handleAddParticipant(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;
   cpc::string participantAddress;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(participantAddress));

   StackLog(<< "XmppChatJsonServerInterface::handleAddParticipant(): chat: " << chat << " participantAddress: " << participantAddress);
   xmppChatManager->addParticipant(chat, participantAddress);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleStart(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat));

   StackLog(<< "XmppChatJsonServerInterface::handleStart(): chat: " << chat);
   xmppChatManager->start(chat);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleEnd(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat));

   StackLog(<< "XmppChatJsonServerInterface::handleEnd(): chat: " << chat);
   xmppChatManager->end(chat);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleSendMessage(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatMessageHandle message = -1;
   XmppChatHandle chat = -1;
   cpc::string messageContent, htmlText, subject;

   JsonDeserialize(functionObjectVal, JSON_VALUE(message), JSON_VALUE(chat), JSON_VALUE(messageContent), JSON_VALUE(htmlText), JSON_VALUE(subject));

   StackLog(<< "XmppChatJsonServerInterface::handleSendMessage(): chat: " << chat << " message: " << message);
   dynamic_cast<XmppChatManagerInterface*>(xmppChatManager)->sendMessage(message, chat, messageContent, htmlText, subject);

   return kSuccess;
}

int XmppChatJsonServerInterface::handleAccept(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat));

   StackLog(<< "XmppChatJsonServerInterface::handleAccept(): chat: " << chat);
   xmppChatManager->accept(chat);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleReject(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat));

   StackLog(<< "XmppChatJsonServerInterface::handleReject(): chat: " << chat);
   xmppChatManager->reject(chat);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleNotifyMessageDelivered(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;
   XmppChatMessageHandle message = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(message));

   StackLog(<< "XmppChatJsonServerInterface::handleNotifyMessageDelivered(): chat: " << chat << " message: " << message);
   xmppChatManager->notifyMessageDelivered(chat, message);

   return kSuccess;
}

int XmppChatJsonServerInterface::handleNotifyMessageDisplayed(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;
   XmppChatMessageHandle message = -1;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(message));

   StackLog(<< "XmppChatJsonServerInterface::handleNotifyMessageDisplayed(): chat: " << chat << " message: " << message);
   xmppChatManager->notifyMessageDisplayed(chat, message);

   return kSuccess;
}

int XmppChatJsonServerInterface::handleSetIsComposingMessage(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = -1;
   int refreshInterval = 90, idleInterval = 15;

   JsonDeserialize(functionObjectVal, JSON_VALUE(chat), JSON_VALUE(refreshInterval), JSON_VALUE(idleInterval));

   StackLog(<< "XmppChatJsonServerInterface::handleSetIsComposingMessage(): chat: " << chat);
   xmppChatManager->setIsComposingMessage(chat, refreshInterval, idleInterval);
   return kSuccess;
}

int XmppChatJsonServerInterface::handleCreateMessage(const rapidjson::Value& functionObjectVal)
{
   XmppChatManager* xmppChatManager = XmppChatManager::getInterface(mPhone);
   XmppChatHandle chat = 0;
   XmppChatMessageHandle chatMessage = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(chatMessage), JSON_VALUE(chat));

   dynamic_cast<XmppChatManagerInterface*>(xmppChatManager)->createMessage(chatMessage, chat);

   StackLog(<< "XmppChatJsonServerInterface::handleCreateMessage(): message: " << chatMessage << " chat: " << chat);
   return kSuccess;
}

int XmppChatJsonServerInterface::onNewChat(XmppChatHandle chat, const NewChatEvent& args)
{
   JsonFunctionCall(mTransport, "onNewChat", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onIsComposingMessage(XmppChatHandle chat, const IsComposingMessageEvent& args)
{
   JsonFunctionCall(mTransport, "onIsComposingMessage", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onNewMessage(XmppChatHandle chat, const NewMessageEvent& args)
{
   LocalMaxLog("XmppChatJsonServerInterface::onNewMessage for message id: {}, XmppChatJsonServerInterface: {}", args.messageId, (void*)this);

   JsonFunctionCall(mTransport, "onNewMessage", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onNewReaction(XmppChatHandle chat, const NewReactionEvent& args)
{
// DRL FIXIT! We need to convert the structure below!
//   JsonFunctionCall(mTransport, "onNewReaction", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent& args)
{
// DRL FIXIT! We need to convert the structure below!
//   JsonFunctionCall(mTransport, "onNewMessageRetraction", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onNewOutboundMessage(XmppChatHandle chat, const NewMessageEvent& args)
{
   LocalMaxLog("XmppChatJsonServerInterface::onNewOutboundMessage for message id: {}, XmppChatJsonServerInterface: {}", args.messageId, (void*)this);

   JsonFunctionCall(mTransport, "onNewOutboundMessage", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent& args)
{
   JsonFunctionCall(mTransport, "onSendMessageSuccess", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent& args)
{
   JsonFunctionCall(mTransport, "onSendMessageFailure", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onMessageDelivered(XmppChatHandle chat, const MessageDeliveredEvent& args)
{
   JsonFunctionCall(mTransport, "onMessageDelivered", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onMessageDeliveryError(XmppChatHandle chat, const MessageDeliveryErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onMessageDeliveryError", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onMessageDisplayed(XmppChatHandle chat, const MessageDisplayedEvent& args)
{
   JsonFunctionCall(mTransport, "onMessageDisplayed", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onChatEnded(XmppChatHandle chat, const ChatEndedEvent& args)
{
   JsonFunctionCall(mTransport, "onChatEnded", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args)
{
   // JsonFunctionCall(mTransport, "onChatDiscoCompleted", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args)
{
   // JsonFunctionCall(mTransport, "onChatDiscoError", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onValidateChatHandleResult(XmppChatHandle chat, const ValidateChatHandleEvent& args)
{
   JsonFunctionCall(mTransport, "onValidateChatHandleResult", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

int XmppChatJsonServerInterface::onError(XmppChatHandle chat, const ErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(chat), JSON_VALUE(args));
   return kSuccess;
}

}

}

#endif
