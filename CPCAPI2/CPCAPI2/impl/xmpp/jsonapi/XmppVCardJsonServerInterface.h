#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_JSON_SERVER_INTERFACE_H)
#define CPCAPI2_XMPP_VCARD_JSON_SERVER_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/xmpp/XmppVCardJsonApi.h"
#include "xmpp/XmppVCardSyncHandler.h"
#include "xmpp/XmppVCardHandlerInternal.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppVCard
{
   
class XmppVCardJsonServerInterface : public CPCAPI2::XmppVCard::XmppVCardHandlerInternal,
                                     public CPCAPI2::XmppVCard::XmppVCardJsonApi,
                                     public CPCAPI2::XmppVCard::XmppVCardSyncHand<PERSON>,
                                     public CPCAPI2::JsonApi::JsonApiServerModule,
                                     public CPCAPI2::PhoneModule
{

public:

   XmppVCardJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~XmppVCardJsonServerInterface();
         
   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppVCardHandler
   virtual int onVCardFetched(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt) OVERRIDE;
   virtual int onVCardOperationResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt) OVERRIDE;
   virtual int onError(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt) OVERRIDE;
   
   // XmppVCardHandlerInternal
   virtual int onCreateVCardResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const XmppVCardCreatedResultEvent& evt) OVERRIDE;
         
private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
         
   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
         
   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleFetchVCard(const rapidjson::Value& functionObjectVal);
   int handleStoreVCard(const rapidjson::Value& functionObjectVal);
   int handleCancelVCardOperations(const rapidjson::Value& functionObjectVal);
   int handleRequestVCardState(const rapidjson::Value& functionObjectVal);
   int handleRequestVCardStateForAccount(const rapidjson::Value& functionObjectVal);
   int handleRequestAllVCardState(const rapidjson::Value& functionObjectVal);

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)>> FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_XMPP_VCARD_JSON_SERVER_INTERFACE_H
