#pragma once

#if !defined(CPCAPI2_XMPP_ROSTER_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_XMPP_ROSTER_JSON_PROXY_INTERFACE_H

#include "xmpp/XmppRosterJsonProxy.h"
#include "xmpp/XmppRosterJsonProxyStateHandler.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <future>
#include <condition_variable>


namespace CPCAPI2
{

class PhoneInterface;

namespace XmppRoster
{

class XmppRosterImpl;

class XmppRosterJsonProxyInterface : public CPCAPI2::XmppRoster::XmppRosterManagerJsonProxy,
                                     public CPCAPI2::JsonApi::JsonApiClientModule,
                                     public CPCAPI2::PhoneModule
{

public:

   XmppRosterJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~XmppRosterJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // Inherited via XmppRosterManager
   virtual XmppRosterHandle createRoster(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler) OVERRIDE;

   virtual int acceptSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;
   virtual int rejectSubscriptionRequest(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;
   virtual int cancelAcceptedSubscription(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message) OVERRIDE;

   virtual int addRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups) OVERRIDE;
   virtual int updateRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups) OVERRIDE;
   virtual int removeRosterItem(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;

   virtual int subscribePresence(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message) OVERRIDE;
   virtual int unsubscribePresence(XmppRosterHandle roster, const cpc::string& address) OVERRIDE;

   virtual int getRosterState(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems) OVERRIDE; // Blocking
   
   // XmppRosterManagerJsonProxy
   virtual int setStateHandler(XmppRosterJsonProxyStateHandler* handler) OVERRIDE;
   virtual int requestRosterState(XmppRosterHandle roster) OVERRIDE;
   virtual int requestRosterStateForAccount(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int requestAllRosterState() OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);

private:

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   int handleRosterUpdate(const rapidjson::Value& functionObjectVal);
   int handleSubscriptionRequest(const rapidjson::Value& functionObjectVal);
   int handleUnsubscriptionRequest(const rapidjson::Value& functionObjectVal);
   int handleRosterPresence(const rapidjson::Value& functionObjectVal);
   int handleSelfPresence(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);
   int handleRosterState(const rapidjson::Value& functionObjectVal);
   int handleRosterItems(const rapidjson::Value& functionObjectVal);

   void createRosterImpl(XmppRosterHandle roster, XmppAccount::XmppAccountHandle account);
   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppRosterHandler* handler);

   void acceptSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address);
   void rejectSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string& address);
   void cancelAcceptedSubscriptionImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& message);

   void addRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void updateRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups);
   void removeRosterItemImpl(XmppRosterHandle roster, const cpc::string& address);

   void subscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message);
   void unsubscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address);

   void getRosterStateImpl(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems);
   
   void setStateHandlerImpl(XmppRosterJsonProxyStateHandler* handler);;
   void requestRosterStateImpl(XmppRosterHandle roster);
   void requestRosterStateForAccountImpl(XmppAccount::XmppAccountHandle account);
   void requestAllRosterStateImpl();

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;

   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<XmppAccount::XmppAccountHandle, XmppRoster::XmppRosterHandler*> mAppHandlers;
   std::map<XmppRoster::XmppRosterHandle, XmppAccount::XmppAccountHandle> mMapRosterHandleToAccountHandle;
   std::promise<XmppRoster::XmppRosterHandle> mServerCreatedHandle;

   std::promise<JsonProxyRosterItemsEvent> mRosterEvent;
   XmppRosterJsonProxyStateHandler* mStateHandler;
   int mHandlePrefix;

};

}
   
}

#endif // CPCAPI2_XMPP_ROSTER_JSON_PROXY_INTERFACE_H
