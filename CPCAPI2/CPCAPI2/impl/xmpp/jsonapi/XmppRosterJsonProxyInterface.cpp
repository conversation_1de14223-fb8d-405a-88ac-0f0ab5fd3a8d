#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppRosterJsonProxyInterface.h"
#include "XmppAccountJsonProxyInterface.h"
#include "xmpp/XmppRosterJsonProxyStateHandler.h"
#include "xmpp/XmppRosterInterface.h"
#include "xmpp/XmppRosterState.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"

#include <rutil/Random.hxx>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ROSTER
#define JSON_MODULE "XmppRosterJsonApi"

namespace CPCAPI2
{

namespace XmppRoster
{

XmppRosterJsonProxyInterface::XmppRosterJsonProxyInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mTransport(NULL),
mStateHandler(NULL),
mHandlePrefix(0)
{
   mFunctionMap["onRosterUpdate"] = std::bind(&XmppRosterJsonProxyInterface::handleRosterUpdate, this, std::placeholders::_1);
   mFunctionMap["onSubscriptionRequest"] = std::bind(&XmppRosterJsonProxyInterface::handleSubscriptionRequest, this, std::placeholders::_1);
   mFunctionMap["onUnsubscriptionRequest"] = std::bind(&XmppRosterJsonProxyInterface::handleUnsubscriptionRequest, this, std::placeholders::_1);
   mFunctionMap["onRosterPresence"] = std::bind(&XmppRosterJsonProxyInterface::handleRosterPresence, this, std::placeholders::_1);
   mFunctionMap["onSelfPresence"] = std::bind(&XmppRosterJsonProxyInterface::handleSelfPresence, this, std::placeholders::_1);
   mFunctionMap["onError"] = std::bind(&XmppRosterJsonProxyInterface::handleError, this, std::placeholders::_1);
   mFunctionMap["onRosterState"] = std::bind(&XmppRosterJsonProxyInterface::handleRosterState, this, std::placeholders::_1);
   mFunctionMap["onRosterItems"] = std::bind(&XmppRosterJsonProxyInterface::handleRosterItems, this, std::placeholders::_1);
   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mHandlePrefix = resip::Random::getCryptoRandom();
   while ((mHandlePrefix & 0xFFFF0000) == 0)
   {
      mHandlePrefix = (mHandlePrefix + 1) << 1;
   }
   mHandlePrefix = (mHandlePrefix & 0xFFFF0000);

   mPhone->addRefImpl();

   StackLog(<< "XmppRosterJsonProxyInterface::XmppRosterJsonProxyInterface(): " << this << " phone: " << mPhone);
}

XmppRosterJsonProxyInterface::~XmppRosterJsonProxyInterface()
{
   StackLog(<< "XmppRosterJsonProxyInterface::~XmppRosterJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppRosterJsonProxyInterface::Release()
{
   StackLog(<< "XmppRosterJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

void XmppRosterJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppRosterJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

void XmppRosterJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   // StackLog(<< "XmppRosterJsonProxyInterface::postCallback(): fp: " << fp);
   dynamic_cast<XmppAccount::XmppAccountJsonProxyInterface*>(XmppAccount::XmppAccountJsonProxyInterface::getInterface(mPhone))->postCallbackPub(fp);
}

// JsonApiClientModule
void XmppRosterJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppRosterJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppRosterJsonProxyInterface::processIncomingImpl(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      DebugLog(<< "XmppRosterJsonProxyInterface::processIncomingImpl(): triggering " << funcName << " callback");
      it->second(functionObjectVal);
   }
}

int XmppRosterJsonProxyInterface::handleRosterUpdate(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleRosterUpdate()");
   XmppRosterHandle roster = -1;
   XmppRoster::XmppRosterUpdateEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onRosterUpdate, it->second, roster, args));
   }

   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleSubscriptionRequest(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleSubscriptionRequest()");
   XmppRosterHandle roster = -1;
   XmppRoster::XmppRosterSubscriptionRequestEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   if (mMapRosterHandleToAccountHandle.count(roster) == 0)
   {
      return kError;
   }

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onSubscriptionRequest, it->second, roster, args));
   }

   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleUnsubscriptionRequest(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleUnsubscriptionRequest()");
   XmppRosterHandle roster = -1;
   XmppRoster::XmppRosterUnsubscriptionRequestEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   if (mMapRosterHandleToAccountHandle.count(roster) == 0)
   {
      return kError;
   }

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onUnsubscriptionRequest, it->second, roster, args));
   }

   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleRosterPresence(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleRosterPresence()");
   XmppRosterHandle roster = -1;
   XmppRoster::XmppRosterPresenceEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   if (mMapRosterHandleToAccountHandle.count(roster) == 0)
   {
      return kError;
   }

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onRosterPresence, it->second, roster, args));
   }
   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleSelfPresence(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleSelfPresence()");
   XmppRosterHandle roster = -1;
   XmppRoster::XmppRosterPresenceEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   if (mMapRosterHandleToAccountHandle.count(roster) == 0)
   {
      return kError;
   }

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onSelfPresence, it->second, roster, args));
   }
   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleError()");
   XmppRosterHandle roster = -1;
   XmppRoster::ErrorEvent args;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(args));

   if (mMapRosterHandleToAccountHandle.count(roster) == 0)
   {
      return kError;
   }

   XmppAccount::XmppAccountHandle account = mMapRosterHandleToAccountHandle[roster];
   std::map<XmppAccount::XmppAccountHandle, XmppRosterHandler*>::iterator it = mAppHandlers.find(account);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppRosterHandler::onError, it->second, roster, args));
   }
   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleRosterItems(const rapidjson::Value& functionObjectVal)
{
   XmppRoster::JsonProxyRosterItemsEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));

   mRosterEvent.set_value(args);
   StackLog(<< "XmppRosterJsonProxyInterface::handleRosterItems(): received " << args.rosterItems.size() << " roster items for roster: " << args.roster);

   if (mStateHandler != NULL)
   {
      postCallback(makeFpCommand1(XmppRosterJsonProxyStateHandler::onRosterItems, mStateHandler, args));
   }

   return kSuccess;
}

int XmppRosterJsonProxyInterface::handleRosterState(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppRosterJsonProxyInterface::handleRosterState()");

   XmppRoster::JsonProxyRosterStateEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(args));

   // Ensure all the handles are up-to-date
   cpc::vector<XmppRosterState>::const_iterator iter = args.rosterState.begin();
   for( ; iter != args.rosterState.end() ; ++iter )
      mMapRosterHandleToAccountHandle[ iter->roster ] = iter->account;

   // StackLog(<< "XmppRosterJsonProxyInterface::handleRosterState(): mStateHandler: " << mStateHandler);
   if (mStateHandler != NULL)
   {
      // StackLog(<< "XmppRosterJsonProxyInterface::handleRosterState(): mStateHandler: " << mStateHandler << " initiating postCallback");
      postCallback(makeFpCommand1(XmppRosterJsonProxyStateHandler::onRosterState, mStateHandler, args));
   }

   return kSuccess;
}

int XmppRosterJsonProxyInterface::setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppRosterHandler * handler)
{
   StackLog(<< "XmppRosterJsonProxyInterface setHandler " << account << " -> " << handler);
   resip::ReadCallbackBase* f = resip::resip_bind(&XmppRosterJsonProxyInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      execute(f);
      XmppAccount::XmppAccountManagerJsonProxy::getInterface(mPhone)->process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

void XmppRosterJsonProxyInterface::setHandlerImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppRosterHandler * handler)
{
   StackLog(<< "XmppRosterJsonProxyInterface setHandlerImpl " << account << " -> " << handler);
   mAppHandlers[account] = handler;
   bool release = (handler ? false : true);

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(account), JSON_VALUE(release));
}

XmppRosterHandle XmppRosterJsonProxyInterface::createRoster(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterJsonProxyInterface::createRoster: account: " << account);
   XmppRosterHandle h = (XmppRosterHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::createRosterImpl, this, h, account));
   mMapRosterHandleToAccountHandle[h] = account;
   return h;
}

void XmppRosterJsonProxyInterface::createRosterImpl(XmppRosterHandle roster, XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterJsonProxyInterface::createRosterImpl: account: " << account);
   JsonFunctionCall(mTransport, "create", JSON_VALUE(roster), JSON_VALUE(account));
}

int XmppRosterJsonProxyInterface::acceptSubscriptionRequest(XmppRosterHandle roster, const cpc::string & address)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::acceptSubscriptionRequestImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::acceptSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string & address)
{
   StackLog(<< "XmppRosterJsonProxyInterface::acceptSubscriptionRequestImpl()");
   JsonFunctionCall(mTransport, "acceptSubscriptionRequest", JSON_VALUE(roster), JSON_VALUE(address));
}

int XmppRosterJsonProxyInterface::rejectSubscriptionRequest(XmppRosterHandle roster, const cpc::string & address)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::rejectSubscriptionRequestImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::rejectSubscriptionRequestImpl(XmppRosterHandle roster, const cpc::string & address)
{
   StackLog(<< "XmppRosterJsonProxyInterface::rejectSubscriptionRequestImpl()");
   JsonFunctionCall(mTransport, "rejectSubscriptionRequest", JSON_VALUE(roster), JSON_VALUE(address));
}

int XmppRosterJsonProxyInterface::cancelAcceptedSubscription(XmppRosterHandle roster, const cpc::string & address, const cpc::string & message)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::cancelAcceptedSubscriptionImpl, this, roster, address, message));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::cancelAcceptedSubscriptionImpl(XmppRosterHandle roster, const cpc::string & address, const cpc::string & message)
{
   StackLog(<< "XmppRosterJsonProxyInterface::cancelAcceptedSubscriptionImpl()");
   JsonFunctionCall(mTransport, "cancelAcceptedSubscription", JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(message));
}

int XmppRosterJsonProxyInterface::addRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::addRosterItemImpl, this, roster, address, displayName, groups));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::addRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups)
{
   StackLog(<< "XmppRosterJsonProxyInterface::addRosterItemImpl()");
   JsonFunctionCall(mTransport, "addRosterItem", JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups));
}

int XmppRosterJsonProxyInterface::updateRosterItem(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::updateRosterItemImpl, this, roster, address, displayName, groups));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::updateRosterItemImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups)
{
   StackLog(<< "XmppRosterJsonProxyInterface::updateRosterItemImpl()");
   JsonFunctionCall(mTransport, "updateRosterItem", JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups));
}

int XmppRosterJsonProxyInterface::removeRosterItem(XmppRosterHandle roster, const cpc::string& address)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::removeRosterItemImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::removeRosterItemImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterJsonProxyInterface::removeRosterItemImpl()");
   JsonFunctionCall(mTransport, "removeRosterItem", JSON_VALUE(roster), JSON_VALUE(address));
}

int XmppRosterJsonProxyInterface::subscribePresence(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::subscribePresenceImpl, this, roster, address, displayName, groups, message));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::subscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address, const cpc::string& displayName, const cpc::vector<cpc::string>& groups, const cpc::string& message)
{
   StackLog(<< "XmppRosterJsonProxyInterface::subscribePresenceImpl()");
   JsonFunctionCall(mTransport, "subscribePresence", JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups), JSON_VALUE(message));
}

int XmppRosterJsonProxyInterface::unsubscribePresence(XmppRosterHandle roster, const cpc::string& address)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::unsubscribePresenceImpl, this, roster, address));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::unsubscribePresenceImpl(XmppRosterHandle roster, const cpc::string& address)
{
   StackLog(<< "XmppRosterJsonProxyInterface::unsubscribePresenceImpl()");
   JsonFunctionCall(mTransport, "unsubscribePresence", JSON_VALUE(roster), JSON_VALUE(address));
}

int XmppRosterJsonProxyInterface::getRosterState(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems)
{
   StackLog(<< "XmppRosterJsonProxyInterface::getRosterState: roster: " << roster);
   auto hF = mRosterEvent.get_future();
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::getRosterStateImpl, this, roster, std::ref(rosterItems)));
   
   JsonProxyRosterItemsEvent h;
   
   if (hF.wait_for(std::chrono::milliseconds(5000)) == std::future_status::ready)
   {
      h = hF.get();
      roster = h.roster;
      rosterItems = h.rosterItems;
      StackLog(<< "XmppRosterJsonProxyInterface::getRosterState(): roster handle: " << roster << " rosterItems: " << rosterItems.size());
   }
   else
   {
      WarningLog(<< "XmppRosterJsonProxyInterface::getRosterState(): state request for roster: " << roster << " timed out, no response received from server");
   }
   
   mRosterEvent = std::promise<JsonProxyRosterItemsEvent>(); // reset for next use
   return kSuccess;
}

void XmppRosterJsonProxyInterface::getRosterStateImpl(XmppRosterHandle roster, cpc::vector<RosterItem>& rosterItems)
{
   StackLog(<< "XmppRosterJsonProxyInterface::getRosterStateImpl: roster: " << roster);
   JsonFunctionCall(mTransport, "getRosterState", JSON_VALUE(roster));
}

int XmppRosterJsonProxyInterface::setStateHandler(XmppRosterJsonProxyStateHandler* handler)
{
   StackLog(<< "XmppRosterJsonProxyInterface::setStateHandler(): handler: " << handler);
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::setStateHandlerImpl, this, handler));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::setStateHandlerImpl(XmppRosterJsonProxyStateHandler* handler)
{
   StackLog(<< "XmppRosterJsonProxyInterface::setStateHandlerImpl(): handler: " << handler);
   mStateHandler = handler;
}

int XmppRosterJsonProxyInterface::requestRosterState(XmppRosterHandle roster)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::requestRosterStateImpl, this, roster));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::requestRosterStateImpl(XmppRosterHandle roster)
{
   StackLog(<< "XmppRosterJsonProxyInterface::requestRosterStateImpl(): roster: " << roster);
   JsonFunctionCall(mTransport, "requestRosterState", JSON_VALUE(roster));
}

int XmppRosterJsonProxyInterface::requestRosterStateForAccount(XmppAccount::XmppAccountHandle account)
{
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::requestRosterStateForAccountImpl, this, account));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::requestRosterStateForAccountImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppRosterJsonProxyInterface::requestRosterStateForAccountImpl(): account: " << account);
   JsonFunctionCall(mTransport, "requestRosterStateForAccount", JSON_VALUE(account));
}

int XmppRosterJsonProxyInterface::requestAllRosterState()
{
   StackLog(<< "XmppRosterJsonProxyInterface::requestAllRosterState()");
   post(resip::resip_bind(&XmppRosterJsonProxyInterface::requestAllRosterStateImpl, this));
   return kSuccess;
}

void XmppRosterJsonProxyInterface::requestAllRosterStateImpl()
{
   StackLog(<< "XmppRosterJsonProxyInterface::requestAllRosterStateImpl()");
   JsonFunctionCall(mTransport, "requestAllRosterState");
}

}

}

#endif
