#pragma once

#if !defined(CPCAPI2_XMPP_CHAT_JSON_INTERFACE_H)
#define CPCAPI2_XMPP_CHAT_JSON_INTERFACE_H

#include "interface/public/xmpp/XmppChatHandler.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/xmpp/XmppChatJsonApi.h"
#include "xmpp/XmppChatSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{

class PhoneInterface;
class LocalLogger;

namespace XmppChat
{

class XmppChatJsonServerInterface : public CPCAPI2::XmppChat::XmppChatHandler,
                                    public CPCAPI2::XmppChat::XmppChatJsonApi,
                                    public CPCAPI2::XmppChat::XmppChatSyncHand<PERSON>,
                                    public CPCAPI2::JsonApi::JsonApiServerModule,
                                    public CPCAPI2::PhoneModule
{

public:

   XmppChatJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~XmppChatJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppChatHandler
   // Inherited via XmppChatHandler
   virtual int onNewChat(XmppChatHandle chat, const NewChatEvent & args) OVERRIDE;
   virtual int onIsComposingMessage(XmppChatHandle chat, const IsComposingMessageEvent & args) OVERRIDE;
   virtual int onNewMessage(XmppChatHandle chat, const NewMessageEvent & args) OVERRIDE;
   virtual int onNewReaction(XmppChatHandle chat, const NewReactionEvent & args) OVERRIDE;
   virtual int onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent & args) OVERRIDE;
   virtual int onNewOutboundMessage(XmppChatHandle chat, const NewMessageEvent & args) OVERRIDE;
   virtual int onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent & args) OVERRIDE;
   virtual int onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent & args) OVERRIDE;
   virtual int onMessageDelivered(XmppChatHandle chat, const MessageDeliveredEvent & args) OVERRIDE;
   virtual int onMessageDeliveryError(XmppChatHandle chat, const MessageDeliveryErrorEvent & args) OVERRIDE;
   virtual int onMessageDisplayed(XmppChatHandle chat, const MessageDisplayedEvent & args) OVERRIDE;
   virtual int onChatEnded(XmppChatHandle chat, const ChatEndedEvent & event) OVERRIDE;
   virtual int onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args) OVERRIDE;
   virtual int onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args) OVERRIDE;
   virtual int onValidateChatHandleResult(XmppChatHandle chat, const ValidateChatHandleEvent& event) OVERRIDE;
   virtual int onError(XmppChatHandle chat, const ErrorEvent & event) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handleCreateChat(const rapidjson::Value& functionObjectVal);
   int handleAddParticipant(const rapidjson::Value& functionObjectVal);
   int handleStart(const rapidjson::Value& functionObjectVal);
   int handleEnd(const rapidjson::Value& functionObjectVal);
   int handleSendMessage(const rapidjson::Value& functionObjectVal);
   int handleAccept(const rapidjson::Value& functionObjectVal);
   int handleReject(const rapidjson::Value& functionObjectVal);
   int handleNotifyMessageDelivered(const rapidjson::Value& functionObjectVal);
   int handleNotifyMessageDisplayed(const rapidjson::Value& functionObjectVal);
   int handleSetIsComposingMessage(const rapidjson::Value& functionObjectVal);
   int handleValidateChatHandle(const rapidjson::Value& functionObjectVal);
   int handleCreateMessage(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   LocalLogger* mLocalLogger;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_XMPP_CHAT_JSON_INTERFACE_H
