#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppRosterJsonServerInterface.h"
#include "xmpp/XmppRosterInterface.h"
#include "xmpp/XmppRosterJsonProxyStateHandler.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ROSTER
#define JSON_MODULE "XmppRosterManagerJsonProxy"

namespace CPCAPI2
{

namespace XmppRoster
{

XmppRosterJsonServerInterface::XmppRosterJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   XmppRosterManager* xmppRoster = XmppRosterManager::getInterface(phone);

   // the state needs to be setup early
   XmppRosterStateManager::getInterface(xmppRoster);
   XmppRosterInterface* xmppRosterIf = dynamic_cast<XmppRosterInterface*>(xmppRoster);
   xmppRosterIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&XmppRosterJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["create"] = std::bind(&XmppRosterJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["acceptSubscriptionRequest"] = std::bind(&XmppRosterJsonServerInterface::handleAcceptSubscriptionRequest, this, std::placeholders::_1);
   mFunctionMap["rejectSubscriptionRequest"] = std::bind(&XmppRosterJsonServerInterface::handleRejectSubscriptionRequest, this, std::placeholders::_1);
   mFunctionMap["cancelAcceptedSubscription"] = std::bind(&XmppRosterJsonServerInterface::handleCancelAcceptedSubscription, this, std::placeholders::_1);
   mFunctionMap["addRosterItem"] = std::bind(&XmppRosterJsonServerInterface::handleAddRosterItem, this, std::placeholders::_1);
   mFunctionMap["updateRosterItem"] = std::bind(&XmppRosterJsonServerInterface::handleUpdateRosterItem, this, std::placeholders::_1);
   mFunctionMap["removeRosterItem"] = std::bind(&XmppRosterJsonServerInterface::handleRemoveRosterItem, this, std::placeholders::_1);
   mFunctionMap["subscribePresence"] = std::bind(&XmppRosterJsonServerInterface::handleSubscribePresence, this, std::placeholders::_1);
   mFunctionMap["unsubscribePresence"] = std::bind(&XmppRosterJsonServerInterface::handleUnsubscribePresence, this, std::placeholders::_1);
   mFunctionMap["getRosterState"] = std::bind(&XmppRosterJsonServerInterface::handleGetRosterState, this, std::placeholders::_1);
   mFunctionMap["requestRosterState"] = std::bind(&XmppRosterJsonServerInterface::handleRequestRosterState, this, std::placeholders::_1);
   mFunctionMap["requestRosterStateForAccount"] = std::bind(&XmppRosterJsonServerInterface::handleRequestRosterStateForAccount, this, std::placeholders::_1);
   mFunctionMap["requestAllRosterState"] = std::bind(&XmppRosterJsonServerInterface::handleRequestAllRosterState, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppRosterJsonServerInterface::~XmppRosterJsonServerInterface()
{
}

void XmppRosterJsonServerInterface::Release()
{
}

void XmppRosterJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppRosterJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppRosterJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppRosterJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppRosterJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppRosterJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   bool release = false;

   JsonDeserialize(functionObjectVal, JSON_VALUE(account), JSON_VALUE(release));

   StackLog(<< "XmppRosterJsonServerInterface::handleSetHandler(): " << (release ? "releasing" : "creating") << " roster handler for account: " << account);
   xmppRosterManager->setHandler(account, (release ? NULL : (XmppRosterHandler*)0xDEADBEFF)); // note: this ends in FF, meaning that we don't want callbacks at all
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleCreate(const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppRosterJsonServerInterface::handleCreate()");
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppAccount::XmppAccountHandle account = -1;
   XmppRosterHandle roster = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(account));

   dynamic_cast<XmppRosterInterface*>(xmppRosterManager)->create(roster, account);

   StackLog(<< "XmppRosterJsonServerInterface::handleCreate(): account: " << account);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleAcceptSubscriptionRequest(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address));

   xmppRosterManager->acceptSubscriptionRequest(roster, address);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleRejectSubscriptionRequest(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address));

   xmppRosterManager->rejectSubscriptionRequest(roster, address);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleCancelAcceptedSubscription(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;
   cpc::string message;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(message));

   xmppRosterManager->cancelAcceptedSubscription(roster, address, message);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleAddRosterItem(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;
   cpc::string displayName;
   cpc::vector<cpc::string> groups;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups));

   dynamic_cast<XmppRosterInterface*>(xmppRosterManager)->addRosterItem(roster, address, displayName, groups);

   return kSuccess;
}

int XmppRosterJsonServerInterface::handleUpdateRosterItem(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;
   cpc::string displayName;
   cpc::vector<cpc::string> groups;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups));

   xmppRosterManager->updateRosterItem(roster, address, displayName, groups);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleRemoveRosterItem(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address));

   xmppRosterManager->removeRosterItem(roster, address);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleSubscribePresence(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;
   cpc::string displayName;
   cpc::vector<cpc::string> groups;
   cpc::string message;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address), JSON_VALUE(displayName), JSON_VALUE(groups), JSON_VALUE(message));

   xmppRosterManager->subscribePresence(roster, address, displayName, groups, message);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleUnsubscribePresence(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* xmppRosterManager = XmppRosterManager::getInterface(mPhone);
   XmppRosterHandle roster = -1;
   cpc::string address;

   JsonDeserialize(functionObjectVal, JSON_VALUE(roster), JSON_VALUE(address));

   xmppRosterManager->unsubscribePresence(roster, address);
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleGetRosterState(const rapidjson::Value & functionObjectVal)
{
   XmppRosterManager* rosterMgr = XmppRosterManager::getInterface(mPhone);
   JsonProxyRosterItemsEvent args;

   JsonDeserialize(functionObjectVal,"roster", args.roster);

   rosterMgr->getRosterState(args.roster, args.rosterItems);

   StackLog(<< "XmppRosterJsonServerInterface::handleGetRosterState(): " << this << " mPhone: " << mPhone << " rosterMgr: " << rosterMgr << " roster: " << args.roster << " roster items: " << args.rosterItems.size());

   JsonFunctionCall(mTransport, "onRosterItems", JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleRequestRosterState(const rapidjson::Value& functionObjectVal)
{
   XmppRosterManager* rosterMgr = XmppRosterManager::getInterface(mPhone);
   XmppRosterStateManager* rosterStateMgr = XmppRosterStateManager::getInterface(rosterMgr);

   XmppRosterHandle roster = (-1);
   JsonDeserialize(functionObjectVal, JSON_VALUE(roster));

   XmppRosterState state;
   rosterStateMgr->getRosterState(roster, state);

   StackLog(<< "XmppRosterJsonServerInterface::handleRequestRosterState(): " << this << " mPhone: " << mPhone << " rosterMgr: " << rosterMgr << " rosterStateMgr: " << rosterStateMgr << " account: " << state.account << " roster: " << roster << " roster items: " << state.rosterItems.size());
   JsonProxyRosterStateEvent args;
   args.rosterState.push_back(state);

   JsonFunctionCall(mTransport, "onRosterState", JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleRequestRosterStateForAccount(const rapidjson::Value& functionObjectVal)
{
   XmppRosterManager* rosterMgr = XmppRosterManager::getInterface(mPhone);
   XmppRosterStateManager* rosterStateMgr = XmppRosterStateManager::getInterface(rosterMgr);

   XmppAccount::XmppAccountHandle account = -1;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   XmppRosterState state;
   rosterStateMgr->getRosterStateForAccount(account, state);

   StackLog(<< "XmppRosterJsonServerInterface::handleRequestRosterStateForAccount(): " << this << " mPhone: " << mPhone << " rosterMgr: " << rosterMgr << " rosterStateMgr: " << rosterStateMgr << " account: " << account << " roster: " << state.roster << " roster items: " << state.rosterItems.size());
   JsonProxyRosterStateEvent args;
   args.rosterState.push_back(state);

   StackLog(<< "XmppRosterJsonServerInterface::handleRequestRosterStateForAccount(): " << this << " mPhone: " << mPhone << " rosterMgr: " << rosterMgr << " rosterStateMgr: " << rosterStateMgr << " state: " << state);

   JsonFunctionCall(mTransport, "onRosterState", JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::handleRequestAllRosterState(const rapidjson::Value& functionObjectVal)
{
   XmppRosterManager* rosterMgr = XmppRosterManager::getInterface(mPhone);
   XmppRosterStateManager* rosterStateMgr = XmppRosterStateManager::getInterface(rosterMgr);

   JsonProxyRosterStateEvent args;
   rosterStateMgr->getAllRosterState(args.rosterState);

   StackLog(<< "XmppRosterJsonServerInterface::handleRequestAllRosterState(): " << this << " mPhone: " << mPhone << " rosterMgr: " << rosterMgr << " rosterStateMgr: " << rosterStateMgr << " roster states: " << args.rosterState.size());

   JsonFunctionCall(mTransport, "onRosterState", JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onRosterUpdate(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterUpdateEvent& args)
{
   JsonFunctionCall(mTransport, "onRosterUpdate", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onSubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterSubscriptionRequestEvent& args)
{
   JsonFunctionCall(mTransport, "onSubscriptionRequest", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onUnsubscriptionRequest(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterUnsubscriptionRequestEvent& args)
{
   JsonFunctionCall(mTransport, "onUnsubscriptionRequest", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onRosterPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterPresenceEvent& args)
{
   JsonFunctionCall(mTransport, "onRosterPresence", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onSelfPresence(CPCAPI2::XmppRoster::XmppRosterHandle roster, const XmppRosterPresenceEvent& args)
{
   JsonFunctionCall(mTransport, "onSelfPresence", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onError(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::ErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(roster), JSON_VALUE(args));
   return kSuccess;
}

int XmppRosterJsonServerInterface::onCreateRosterResult(CPCAPI2::XmppRoster::XmppRosterHandle roster, const CPCAPI2::XmppRoster::XmppRosterCreatedResultEvent& args)
{
   JsonFunctionCall(mTransport, "onCreateRosterResult", "account", args.account, "handle", roster);
   return kSuccess;
}

}

}

#endif
