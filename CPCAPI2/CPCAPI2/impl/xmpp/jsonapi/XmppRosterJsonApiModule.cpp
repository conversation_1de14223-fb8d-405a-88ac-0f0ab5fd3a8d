#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppRosterJsonProxy.h"
#include "interface/experimental/xmpp/XmppRosterJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppRosterJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppRosterJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace XmppRoster
   {
      XmppRosterJsonApi* XmppRosterJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppRosterJsonServerInterface>(phone, "XmppRosterJsonApi");
#else
         return NULL;
#endif
      }

      XmppRosterManagerJsonProxy* XmppRosterManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_ROSTER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppRosterJsonProxyInterface>(phone, "XmppRosterManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
