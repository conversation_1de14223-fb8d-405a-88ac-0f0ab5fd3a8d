#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_INTERFACE_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "xmpp/XmppMultiUserChatJsonApi.h"
#include "xmpp/XmppMultiUserChatSyncHandler.h"
#include "xmpp/XmppMultiUserChatHandlerInternal.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"
#include "util/DumFpCommand.h"

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppMultiUserChat
{

class XmppMultiUserChatJsonServerInterface : public XmppMultiUserChat::XmppMultiUserChatHandlerInternal,
                                             public XmppMultiUserChat::XmppMultiUserChatJsonApi,
                                             public XmppMultiUserChat::XmppMultiUserChatSyncHandler,
                                             public JsonApi::JsonApiServerModule,
                                             public PhoneModule
{

public:

   XmppMultiUserChatJsonServerInterface(Phone* phone);
   virtual ~XmppMultiUserChatJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppMultiUserChatHandler
   virtual void onServiceAvailability(XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& args) OVERRIDE;
   virtual void onRoomListRetrieved(XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& args) OVERRIDE;
   virtual void onParticipantAdded(XmppMultiUserChatHandle handle, const ParticipantAddedEvent& args) OVERRIDE;
   virtual void onParticipantRemoved(XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& args) OVERRIDE;
   virtual void onParticipantUpdated(XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& args) OVERRIDE;
   virtual void onParticipantSelfUpdated(XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& args) OVERRIDE;
   virtual void onMultiUserChatReady(XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& args) OVERRIDE;
   virtual void onMultiUserChatSubjectChanged(XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& args) OVERRIDE;
   virtual void onMultiUserChatNewMessage(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& args) OVERRIDE;
   virtual void onMultiUserChatNewReaction(XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& args) OVERRIDE;
   virtual void onMultiUserChatNewMessageRetraction(XmppMultiUserChatHandle handle, const MultiUserChatNewMessageRetractionEvent& args) OVERRIDE;
   virtual void onSendMessageSuccess(XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& args) OVERRIDE;
   virtual void onSendMessageFailure(XmppMultiUserChatHandle handle, const SendMessageFailureEvent& args) OVERRIDE;
   virtual void onParticipantChatStateReceived(XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& args) OVERRIDE;
   virtual void onMultiUserChatInvitationReceived(XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& args) OVERRIDE;
   virtual void onMultiUserChatInvitationDeclined(XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& args) OVERRIDE;
   virtual void onMultiUserChatError(XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& args) OVERRIDE;
   virtual void onLocalUserLeft(XmppMultiUserChatHandle handle, const LocalUserLeftEvent& args) OVERRIDE;
   virtual void onMultiUserChatConfigurationRequested(XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& args) OVERRIDE;
   virtual void onMultiUserChatRoomStateChanged(XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& args) OVERRIDE;
   virtual void onMultiUserChatListRequested(XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& args) OVERRIDE;
   virtual void onRoomBookmarksReceived(XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& args) OVERRIDE;
   virtual void onNewRoomHandle(XmppMultiUserChatHandle handle, const NewRoomEvent& evt) OVERRIDE;

   // XmppMultiUserChatHandlerInternal
   virtual int onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChatCreatedResultEvent& evt) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handleGetRoomList(const rapidjson::Value& functionObjectVal);
   int handleSetRoomBookmarks(const rapidjson::Value& functionObjectVal);
   int handleGetRoomBookmarks(const rapidjson::Value& functionObjectVal);
   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleCreate2(const rapidjson::Value& functionObjectVal);
   int handleDestroyRoom(const rapidjson::Value& functionObjectVal);
   int handleGetRoomInfo(const rapidjson::Value& functionObjectVal);
   int handleGetRoomsInfo(const rapidjson::Value& functionObjectVal);
   int handleAccept(const rapidjson::Value& functionObjectVal);
   int handleDecline(const rapidjson::Value& functionObjectVal);
   int handleJoin(const rapidjson::Value& functionObjectVal);
   int handleJoin2(const rapidjson::Value& functionObjectVal);
   int handleLeave(const rapidjson::Value& functionObjectVal);
   int handleSendMessage(const rapidjson::Value& functionObjectVal);
   int handleSetIsComposingMessage(const rapidjson::Value& functionObjectVal);
   int handlePublishPresence(const rapidjson::Value& functionObjectVal);
   int handleChangeNickname(const rapidjson::Value& functionObjectVal);
   int handleChangeSubject(const rapidjson::Value& functionObjectVal);
   int handleInvite(const rapidjson::Value& functionObjectVal);
   int handleKick(const rapidjson::Value& functionObjectVal);
   int handleBan(const rapidjson::Value& functionObjectVal);
   int handleChangeAffiliation(const rapidjson::Value& functionObjectVal);
   int handleChangeJidAffiliation(const rapidjson::Value& functionObjectVal);
   int handleChangeRole(const rapidjson::Value& functionObjectVal);
   int handleRequestConfigurations(const rapidjson::Value& functionObjectVal);
   int handleSetConfigurations(const rapidjson::Value& functionObjectVal);
   int handleSetConfigurationsDeprecated(const rapidjson::Value& functionObjectVal);
   int handleRequestList(const rapidjson::Value& functionObjectVal);
   int handleSetList(const rapidjson::Value& functionObjectVal);
   int handleRequestMultiUserChatState(const rapidjson::Value& functionObjectVal);
   int handleRequestMultiUserChatStateForAccount(const rapidjson::Value& functionObjectVal);
   int handleRequestAllMultiUserChatState(const rapidjson::Value& functionObjectVal);

private:

   PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_INTERFACE_H
