#pragma once

#if !defined(CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_INTERFACE_H

#include "xmpp/XmppMultiUserChatJsonProxy.h"
#include "xmpp/XmppMultiUserChatJsonProxyStateHandler.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>
#include <future>

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppMultiUserChat
{

class XmppMultiUserChatJsonProxyInterface : public XmppMultiUserChatManagerJsonProxy,
                                            public JsonApi::JsonApiClientModule,
                                            public PhoneModule
{

public:

   XmppMultiUserChatJsonProxyInterface(Phone* phone);
   virtual ~XmppMultiUserChatJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppMultiUserChatManagerJsonProxy
   virtual int setStateHandler(XmppMultiUserChatJsonProxyStateHandler* handler) OVERRIDE;
   virtual int requestMultiUserChatState(XmppMultiUserChatHandle handle) OVERRIDE;
   virtual int requestMultiUserChatStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int requestAllMultiUserChatState() OVERRIDE;
   
   // XmppMultiUserChatManager
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler) OVERRIDE;
   virtual int getRoomList(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int setRoomBookmarks(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks) OVERRIDE;
   virtual int getRoomBookmarks(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, const cpc::string& room) OVERRIDE;
   virtual XmppMultiUserChatHandle create(XmppAccount::XmppAccountHandle account, bool instant) OVERRIDE;
   virtual int destroyRoom(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password) OVERRIDE;
   virtual int getRoomInfo(XmppMultiUserChatHandle handle) OVERRIDE;
   virtual int getRoomsInfo(const cpc::vector<XmppMultiUserChatHandle>& handles) OVERRIDE;
   virtual int accept(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int decline(XmppMultiUserChatHandle handle, const cpc::string& reason) OVERRIDE;
   virtual int join(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int join(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd) OVERRIDE;
   virtual int leave(XmppMultiUserChatHandle handle, const cpc::string& reason) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle sendMessage(XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle sendReaction(XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle sendMessageRetraction(XmppMultiUserChatHandle handle, const cpc::string& target) OVERRIDE;
   virtual int setIsComposingMessage(XmppMultiUserChatHandle chat, int refreshInterval, int idleInterval) OVERRIDE;
   virtual int publishPresence(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note) OVERRIDE;
   virtual int changeNickname(XmppMultiUserChatHandle handle, const cpc::string& nickname) OVERRIDE;
   virtual int changeSubject(XmppMultiUserChatHandle handle, const cpc::string& subject) OVERRIDE;
   virtual int invite(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason) OVERRIDE;
   virtual int kick(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason) OVERRIDE;
   virtual int ban(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason) OVERRIDE;
   virtual int changeAffiliation(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason) OVERRIDE;
   virtual int changeJidAffiliation(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason = "") OVERRIDE;
   virtual int changeRole(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason) OVERRIDE;
   virtual int requestConfigurations(XmppMultiUserChatHandle handle) OVERRIDE;
   virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform) OVERRIDE;
   DEPRECATED virtual int setConfigurations(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations) OVERRIDE;
   virtual int requestList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type) OVERRIDE;
   virtual int setList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items) OVERRIDE;
   virtual int notifyMessageRead(XmppMultiUserChatHandle handle, const cpc::string& messageId) OVERRIDE;
   virtual XmppMultiUserChatMessageHandle replaceMessage(XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   int handleCreateMultiUserChatResult(const rapidjson::Value& functionObjectVal);
   int handleServiceAvailability(const rapidjson::Value& functionObjectVal);
   int handleRoomListRetrieved(const rapidjson::Value& functionObjectVal);
   int handleParticipantAdded(const rapidjson::Value& functionObjectVal);
   int handleParticipantRemoved(const rapidjson::Value& functionObjectVal);
   int handleParticipantUpdated(const rapidjson::Value& functionObjectVal);
   int handleParticipantSelfUpdated(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatReady(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatSubjectChanged(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatNewMessage(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatNewReaction(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatNewMessageRetraction(const rapidjson::Value& functionObjectVal);
   int handleSendMessageSuccess(const rapidjson::Value& functionObjectVal);
   int handleSendMessageFailure(const rapidjson::Value& functionObjectVal);
   int handleParticipantChatStateReceived(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatInvitationReceived(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatInvitationDeclined(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatError(const rapidjson::Value& functionObjectVal);
   int handleLocalUserLeft(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatConfigurationRequested(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatRoomStateChanged(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatListRequested(const rapidjson::Value& functionObjectVal);
   int handleRoomBookmarksReceived(const rapidjson::Value& functionObjectVal);
   int handleNewRoomHandle(const rapidjson::Value& functionObjectVal);
   int handleMultiUserChatState(const rapidjson::Value& functionObjectVal);

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler);
   void getRoomListImpl(XmppAccount::XmppAccountHandle account);
   void setRoomBookmarksImpl(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks);
   void getRoomBookmarksImpl(XmppAccount::XmppAccountHandle account);
   void createImpl(XmppMultiUserChatHandle muc, XmppAccount::XmppAccountHandle account, const cpc::string& room);
   void destroyRoomImpl(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password);
   void getRoomInfoImpl(XmppMultiUserChatHandle handle);
   void getRoomsInfoImpl(const cpc::vector<XmppMultiUserChatHandle>& handles);
   void acceptImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void declineImpl(XmppMultiUserChatHandle handle, const cpc::string& reason);
   void joinImpl(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void joinImpl2(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd);
   void leaveImpl(XmppMultiUserChatHandle handle, const cpc::string& reason);
   void sendMessageImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html);
   void sendReactionImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions);
   void sendMessageRetractionImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target);
   void setIsComposingMessageImpl(XmppMultiUserChatHandle chat, int refreshInterval, int idleInterval);
   void publishPresenceImpl(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note);
   void changeNicknameImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname);
   void changeSubjectImpl(XmppMultiUserChatHandle handle, const cpc::string& subject);
   void inviteImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason);
   void kickImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason);
   void banImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason);
   void changeAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeJidAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason);
   void changeRoleImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason);
   void requestConfigurationsImpl(XmppMultiUserChatHandle handle);
   void setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform);
   void setConfigurationsDeprecatedImpl(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations);
   void requestListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type);
   void setListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items);
   void setStateHandlerImpl(XmppMultiUserChatJsonProxyStateHandler* handler);
   void requestMultiUserChatStateImpl(XmppMultiUserChatHandle handle);
   void requestMultiUserChatStateForAccountImpl(CPCAPI2::XmppAccount::XmppAccountHandle account);
   void requestAllMultiUserChatStateImpl();
   void replaceMessageImpl(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html);

   XmppMultiUserChatHandler* findAppHandler(XmppMultiUserChatHandle handle) const;

private:

   PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   JsonApi::JsonApiTransport* mTransport;
   std::map<XmppAccount::XmppAccountHandle, XmppMultiUserChatHandler*> mAppHandlers;
   std::map<XmppMultiUserChatHandle, XmppAccount::XmppAccountHandle> mMapChatHandleToAccountHandle;

   std::promise<XmppMultiUserChat::XmppMultiUserChatHandle> mServerCreatedHandle;
   XmppMultiUserChatJsonProxyStateHandler* mStateHandler;
   XmppMultiUserChatMessageHandle mNextMessageHandle;
   int mHandlePrefix;

};

}

}

#endif // CPCAPI2_XMPP_MULTI_USER_CHAT_JSON_PROXY_INTERFACE_H
