#pragma once
   
#if !defined(CPCAPI2_XMPP_VCARD_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_XMPP_VCARD_JSON_PROXY_INTERFACE_H

#include "xmpp/XmppVCardJsonProxy.h"
#include "xmpp/XmppVCardJsonProxyStateHandler.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <future>
#include <condition_variable>
   
namespace CPCAPI2
{

class PhoneInterface;

namespace XmppVCard
{

class XmppVCardJsonProxyInterface : public CPCAPI2::XmppVCard::XmppVCardManagerJsonProxy,
                                    public CPCAPI2::JsonApi::JsonApiClientModule,
                                    public CPCAPI2::PhoneModule
{

public:

   XmppVCardJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~XmppVCardJsonProxyInterface();
            
   // PhoneModule
   virtual void Release() OVERRIDE;
            
   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppVCardManagerJsonProxy
   virtual int setStateHandler(XmppVCardJsonProxyStateHandler* handler) OVERRIDE;
   virtual int requestVCardState(XmppVCardHandle handle) OVERRIDE;
   virtual int requestVCardStateForAccount(CPCAPI2::XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int requestAllVCardState() OVERRIDE;
   
   // XmppVCardManager
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler) OVERRIDE;
   virtual XmppVCardHandle create(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int fetchVCard(XmppVCardHandle handle, const cpc::string& jid) OVERRIDE;
   virtual int storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard) OVERRIDE;
   virtual int cancelVCardOperations(XmppVCardHandle handle) OVERRIDE;

   // virtual int process(unsigned int timeout) OVERRIDE;
   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);
   void execute(resip::ReadCallbackBase* f);
            
private:

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);
   int setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler);
   int createImpl(XmppAccount::XmppAccountHandle account);
   int fetchVCardImpl(XmppVCardHandle handle, const cpc::string& jid);
   int storeVCardImpl(XmppVCardHandle handle, const XmppVCardDetail& vcard);
   int cancelVCardOperationsImpl(XmppVCardHandle handle);
   
   int setStateHandlerImpl(XmppVCardJsonProxyStateHandler* handler);
   int requestVCardStateImpl(XmppVCardHandle handle);
   int requestVCardStateForAccountImpl(CPCAPI2::XmppAccount::XmppAccountHandle account);
   int requestAllVCardStateImpl();
   
   int handleCreateResult(const rapidjson::Value& functionObjectVal);
   int handleVCardFetched(const rapidjson::Value& functionObjectVal);
   int handleVCardOperationResult(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);
   int handleVCardState(const rapidjson::Value& functionObjectVal);

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<XmppVCard::XmppVCardHandle, XmppVCard::XmppVCardHandler*> mAppHandlers;
   std::promise<XmppVCardHandle> mServerCreatedHandle;
   XmppVCardJsonProxyStateHandler* mStateHandler;
   std::mutex mMutex;
   int mHandlePrefix;

};

}

}
   
#endif // CPCAPI2_XMPP_VCARD_JSON_PROXY_INTERFACE_H
