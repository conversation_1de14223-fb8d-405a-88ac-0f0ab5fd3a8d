#pragma once

#if !defined(CPCAPI2_XMPP_CHAT_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_XMPP_CHAT_JSON_PROXY_INTERFACE_H

#include "xmpp/XmppChatJsonProxy.h"
#include "xmpp/XmppAccount.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>
#include <future>

namespace CPCAPI2
{
class PhoneInterface;
namespace XmppChat
{
class XmppChatJsonSyncHandler {};
class XmppChatJsonProxyInterface :
   public CPCAPI2::EventSource<CPCAPI2::XmppAccount::Xmpp<PERSON>ccount<PERSON><PERSON><PERSON>, XmppChatHandler, XmppChatJsonSyncHandler>,
   public CPCAPI2::XmppChat::XmppChatManagerJsonProxy,
   public CPCAPI2::JsonApi::JsonApiClientModule,
   public CPCAPI2::PhoneModule
{
public:
   XmppChatJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~XmppChatJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppChatHandler, XmppChatJsonSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppChatHandler, XmppChatJsonSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppChatHandler, XmppChatJsonSyncHandler>::setCallbackHook(cbHook, context);
   }

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // Inherited via XmppChatManager
   virtual int setHandler(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppChatHandler * handler) OVERRIDE;
   virtual XmppChatHandle createChat(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int addParticipant(XmppChatHandle chat, const cpc::string & participantAddress) OVERRIDE;
   virtual int start(XmppChatHandle chat) OVERRIDE;
   virtual int end(XmppChatHandle chat) OVERRIDE;
   virtual XmppChatMessageHandle sendMessage(XmppChatHandle chat, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject) OVERRIDE;
   virtual XmppChatMessageHandle sendReaction(XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string> & reactions) OVERRIDE;
   virtual XmppChatMessageHandle sendMessageRetraction(XmppChatHandle chat, const cpc::string& target) OVERRIDE;
   DEPRECATED virtual int accept(XmppChatHandle chat) OVERRIDE;
   DEPRECATED virtual int reject(XmppChatHandle chat) OVERRIDE;
   DEPRECATED virtual XmppChatMessageHandle notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus) OVERRIDE;
   DEPRECATED virtual XmppChatMessageHandle notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus) OVERRIDE;
   virtual int notifyMessageDelivered(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual int notifyMessageDisplayed(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual int setIsComposingMessage(XmppChatHandle chat, int refreshInterval, int idleInterval) OVERRIDE;
   virtual cpc::string getRemoteSyncFromID(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncToID(XmppChatHandle chat, XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncConversationID(XmppChatHandle chat) OVERRIDE;
   DEPRECATED virtual cpc::string getRemoteSyncUniqueID(XmppChatHandle chat, const cpc::string & stanzaID) OVERRIDE;
   virtual cpc::string getRemoteSyncUniqueID2(const cpc::string & stanzaID, const cpc::string & threadID) OVERRIDE;
   virtual cpc::string getRemoteSyncEncodedContent(const cpc::string & plainText, const cpc::string & htmlText) OVERRIDE;
   virtual int getRemoteSyncDecodedContent(const cpc::string & remoteSyncEncodedContent, cpc::string & outPlainText, cpc::string & outHTML) OVERRIDE;
   virtual int validateChatHandle(XmppAccount::XmppAccountHandle account, XmppChatHandle chat) OVERRIDE;
   virtual XmppChatMessageHandle replaceMessage(XmppChatHandle chat, const cpc::string& messageId, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject) OVERRIDE;

   XmppChatMessageHandle createMessage(XmppChat::XmppChatHandle chat);

private:
   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   int handleNewChat(const rapidjson::Value& functionObjectVal);
   int handleIsComposingMessage(const rapidjson::Value& functionObjectVal);
   int handleNewMessage(const rapidjson::Value& functionObjectVal);
   int handleNewReaction(const rapidjson::Value& functionObjectVal);
   int handleNewMessageRetraction(const rapidjson::Value& functionObjectVal);
   int handleNewOutboundMessage(const rapidjson::Value& functionObjectVal);
   int handleSendMessageSuccess(const rapidjson::Value& functionObjectVal);
   int handleSendMessageFailure(const rapidjson::Value& functionObjectVal);
   int handleMessageDelivered(const rapidjson::Value& functionObjectVal);
   int handleMessageDeliveryError(const rapidjson::Value& functionObjectVal);
   int handleMessageDisplayed(const rapidjson::Value& functionObjectVal);
   int handleChatEnded(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);
   int handleValidateChatHandleResult(const rapidjson::Value& functionObjectVal);
   int handleCreateMessageResult(const rapidjson::Value& functionObjectVal);
   int handleNotifyMessageDeliveredResult(const rapidjson::Value& functionObjectVal);
   int handleNotifyMessageDisplayedResult(const rapidjson::Value& functionObjectVal);

   void createChatImpl(XmppChatHandle chat, XmppAccount::XmppAccountHandle account);
   void addParticipantImpl(XmppChatHandle chat, const cpc::string & participantAddress);
   void startImpl(XmppChatHandle chat);
   void endImpl(XmppChatHandle chat);
   void sendMessageImpl(XmppChatMessageHandle messageHandle, XmppChatHandle chat, const cpc::string & messageContent, const cpc::string & htmlText, const cpc::string & subject);
   void sendReactionImpl(XmppChatMessageHandle messageHandle, XmppChatHandle chat, const cpc::string& target, const cpc::vector<cpc::string> & reactions);
   void sendMessageRetractionImpl(XmppChatMessageHandle messageHandle, XmppChatHandle chat, const cpc::string& target);
   void acceptImpl(XmppChatHandle chat);
   void rejectImpl(XmppChatHandle chat);
   void notifyMessageDeliveredImpl_DEPRECATED(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDeliveryStatus messageDeliveryStatus);
   void notifyMessageDisplayedImpl_DEPRECATED(XmppChatMessageHandle chatMessage, XmppChatHandle chat, XmppChatMessageHandle message, MessageDisplayStatus messageDisplayStatus);
   void notifyMessageDeliveredImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   void notifyMessageDisplayedImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   void setIsComposingMessageImpl(XmppChatHandle chat, int refreshInterval, int idleInterval);
   void getRemoteSyncFromIDImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   void getRemoteSyncToIDImpl(XmppChatHandle chat, XmppChatMessageHandle message);
   void getRemoteSyncConversationIDImpl(XmppChatHandle chat);
   void getRemoteSyncUniqueIDImpl(XmppChatHandle chat, const cpc::string& stanzaID);
   void getRemoteSyncUniqueID2Impl(const cpc::string& stanzaID, const cpc::string& threadID);
   void getRemoteSyncEncodedContentImpl(const cpc::string& plainText, const cpc::string& htmlText);
   void getRemoteSyncDecodedContentImpl(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML);
   void validateChatHandleImpl(XmppAccount::XmppAccountHandle account, XmppChatHandle chat);
   void createMessageImpl(XmppChatMessageHandle chatMessage, XmppChatHandle chatHandle);
   void setHandlerImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, XmppChatHandler * handler);
   void replaceMessageImpl(XmppChatMessageHandle messageHandle, XmppChatHandle chat, const cpc::string& messageId, const cpc::string& messageContent, const cpc::string& htmlText, const cpc::string& subject);

   RemoteSyncXmppHelper::RemoteSyncXmppHelper* getRemoteSyncHelper();

private:
   struct DecodedContent
   {
      int result;
      cpc::string outPlainText;
      cpc::string outHTML;
      DecodedContent(): result(-1), outPlainText(""), outHTML("") {}
   };

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<XmppChat::XmppChatHandle, XmppAccount::XmppAccountHandle> mMapChatHandleToAccountHandle;

   RemoteSyncXmppHelper::RemoteSyncXmppHelper* mRemoteSyncHelperIf;

};
}
}
#endif // CPCAPI2_XMPP_CHAT_JSON_PROXY_INTERFACE_H
