#include "brand_branded.h"

#include "interface/experimental/xmpp/XmppAccountJsonProxy.h"
#include "interface/experimental/xmpp/XmppAccountJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppAccountJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppAccountJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace XmppAccount
   {
      XmppAccountJsonApi* XmppAccountJsonApi::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppAccountJsonServerInterface>(phone, "XmppAccountJsonApi");
#else
         return NULL;
#endif
      }

      XmppAccountManagerJsonProxy* XmppAccountManagerJsonProxy::getInterface(Phone* cpcPhone)
      {
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<XmppAccountJsonProxyInterface>(phone, "XmppAccountManagerJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
