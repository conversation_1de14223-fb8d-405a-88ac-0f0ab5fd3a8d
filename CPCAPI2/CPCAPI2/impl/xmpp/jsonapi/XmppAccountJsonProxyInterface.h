#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_INTERFACE_H

#include "xmpp/XmppAccountJsonProxy.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "phone/Cpcapi2EventSource.h"
#include "util/AutoTestProcessor.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <mutex>
#include <condition_variable>
#include <future>

namespace CPCAPI2
{

class PhoneInterface;
namespace XmppAccount
{
class XmppAccountJsonSyncHandler {};

class XmppAccountJsonProxyInterface : public CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, Xmpp<PERSON><PERSON>unt<PERSON>and<PERSON>, XmppAccountJsonSyncHandler>, 
                                      public CPCAPI2::XmppAccount::XmppAccountManagerJsonProxy,
                                      public CPCAPI2::JsonApi::JsonApiClientModule,
                                      public CPCAPI2::PhoneModule
{

public:

   XmppAccountJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~XmppAccountJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // XmppAccountManager
   virtual XmppAccountHandle create() OVERRIDE;
   virtual XmppAccountHandle create(const XmppAccountSettings& settings) OVERRIDE;
   virtual int configureDefaultAccountSettings(XmppAccountHandle account, const XmppAccountSettings& settings) OVERRIDE;
   virtual int applySettings(XmppAccountHandle account) OVERRIDE;
   virtual int setHandler(XmppAccountHandle account, XmppAccountHandler* handler) OVERRIDE;
   virtual int publishPresence(XmppAccount::XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText) OVERRIDE;
   virtual int publishCannedPresence(XmppAccount::XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note) OVERRIDE;
   virtual int enable(XmppAccountHandle account) OVERRIDE;
   virtual int disable(XmppAccountHandle account) OVERRIDE;
   virtual int destroy(XmppAccountHandle account) OVERRIDE;
   virtual int setNetworkRestriction(XmppAccountHandle account, NetworkTransport transport, bool restricted) OVERRIDE;
   virtual int blockIncomingPresence(XmppAccountHandle account, bool block) OVERRIDE;
   virtual int setInactive(XmppAccountHandle account, bool inactive) OVERRIDE;
   virtual int setHibernationState(XmppAccountHandle account, bool active) OVERRIDE;
   virtual int getEntityTime(XmppAccountHandle account, const cpc::string& jid) OVERRIDE;
   virtual int enableNotification(XmppAccountHandle account, const cpc::string& node, const XmppAccount::XmppDataForm& dataform) OVERRIDE;
   virtual int disableNotification(XmppAccountHandle account, const cpc::string& node) OVERRIDE;
   virtual int getPrivateStorageData(XmppAccountHandle account) OVERRIDE;
   virtual int setPrivateStorageData(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data) OVERRIDE;
   virtual cpc::string getRemoteSyncAccountID(XmppAccountHandle account) OVERRIDE;
   virtual XmppAccountHandle getAccountHandleFromRemoteSyncID(cpc::string remoteSyncId) OVERRIDE;
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppAccountHandler, XmppAccountJsonSyncHandler>::process(timeout);
   }
   virtual int send(XmppAccountHandle account, const cpc::string& xml) OVERRIDE;
   virtual int decodeProvisioningResponse(const cpc::string & provisioningResponse, cpc::vector<XmppAccountSettings>& outXmppAccountSettings) OVERRIDE;

   // XmppAccountManagerJsonProxy
   virtual int setStateHandler(XmppAccountJsonProxyStateHandler* handler) OVERRIDE;
   virtual int requestStateAllAccounts() OVERRIDE;

   void postCallbackPub(resip::ReadCallbackBase* fp)
   {
      CPCAPI2::EventSource<CPCAPI2::XmppAccount::XmppAccountHandle, XmppAccountHandler, XmppAccountJsonSyncHandler>::postCallback(fp);
   }

private:

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);
   RemoteSyncXmppHelper::RemoteSyncXmppHelper* getRemoteSyncHelper();

   // XmppAccountManager implementation
   int createImpl(XmppAccountHandle account);
   int createWithSettingsImpl(XmppAccountHandle acctHandle, const XmppAccountSettings& settings);
   int configureDefaultAccountSettingsImpl(XmppAccountHandle account, const XmppAccountSettings& settings);
   int applySettingsImpl(XmppAccountHandle account);
   int setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppAccount::XmppAccountHandler* handler);
   int publishPresenceImpl(XmppAccount::XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText);
   int publishCannedPresenceImpl(XmppAccount::XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note);
   int enableImpl(XmppAccountHandle account);
   int disableImpl(XmppAccountHandle account);
   int destroyImpl(XmppAccountHandle account);
   int setNetworkRestrictionImpl(XmppAccountHandle account, NetworkTransport transport, bool restricted);
   int blockIncomingPresenceImpl(XmppAccountHandle account, bool block);
   int setInactiveImpl(XmppAccountHandle account, bool inactive);
   int setHibernationStateImpl(XmppAccountHandle account, bool active);
   int getEntityTimeImpl(XmppAccountHandle account, const cpc::string& jid);
   int enableNotificationImpl(XmppAccountHandle account, const cpc::string& node, const XmppAccount::XmppDataForm& dataform);
   int disableNotificationImpl(XmppAccountHandle account, const cpc::string& node);
   int getPrivateStorageDataImpl(XmppAccountHandle account);
   int setPrivateStorageDataImpl(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data);

   // XmppAccountManagerJsonProxy implementation
   void setStateHandlerImpl(XmppAccountJsonProxyStateHandler* handler);
   int requestStateAllAccountsImpl();

   int handleAccountConfigured(const rapidjson::Value& functionObjectVal);
   int handleAccountStatusChanged(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);
   int handleLicensingError(const rapidjson::Value& functionObjectVal);
   int handleEntityTime(const rapidjson::Value& functionObjectVal);
   int handleEntityFeature(const rapidjson::Value& functionObjectVal);
   int handleStreamManagementState(const rapidjson::Value& functionObjectVal);
   int handlePrivateStorageData(const rapidjson::Value& functionObjectVal);
   int handleAccountState(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   XmppAccountJsonProxyStateHandler* mStateHandler;
   RemoteSyncXmppHelper::RemoteSyncXmppHelper* mRemoteSyncHelperIf;

};

}

}

#endif // CPCAPI2_XMPP_ACCOUNT_JSON_PROXY_INTERFACE_H
