#pragma once

#if !defined(CPCAPI2_XMPP_ACCOUNT_INTERFACE_H)
#define CPCAPI2_XMPP_ACCOUNT_INTERFACE_H

#include "cpcapi2defs.h"
#include "XmppCommon.h"
#include "xmpp/XmppAccount.h"
#include "xmpp/XmppAccountInternal.h"
#include "util/DumFpCommand.h"
#include "util/AutoTestProcessor.h"
#include "phone/NetworkChangeManagerImpl.h"
#include "phone/PhoneModule.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"

#include <map>
#include <list>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <resip/dum/DumCommand.hxx>

#include "XmppAccountHandlerInternal.h"

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppAccount
{

class XmppAccountImpl;

class XmppAccountInterface
   : public XmppAccountManagerInternal
   , public PhoneModule
   , public CPCAPI2::EventSyncHandler<NetworkChangeHandler>
   , public XmppAccountHandlerInternal
#ifdef CPCAPI2_AUTO_TEST
   , public AutoTestProcessor
#endif
   , public XmppCommon::ImplManager<XmppAccountImpl>
{

public:

   XmppAccountInterface(Phone* phone);
   virtual ~XmppAccountInterface();

   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual XmppAccountHandle create() OVERRIDE;

   virtual XmppAccountHandle create(const XmppAccountSettings& settings) OVERRIDE;

   virtual int configureDefaultAccountSettings(XmppAccountHandle account, const XmppAccountSettings& settings) OVERRIDE;
   virtual void setAccountSettingsInternal(XmppAccountHandle account, const XmppAccountSettingsInternal& settings) OVERRIDE;

   virtual int applySettings(XmppAccountHandle account) OVERRIDE;

   virtual int setHandler(XmppAccountHandle account, XmppAccountHandler* handler) OVERRIDE;

   virtual int publishPresence(XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText) OVERRIDE;

   virtual int publishCannedPresence(XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note) OVERRIDE;

   virtual int enable(XmppAccountHandle account) OVERRIDE;

   virtual int disable(XmppAccountHandle account) OVERRIDE;

   virtual int destroy(XmppAccountHandle account) OVERRIDE;

   virtual int setNetworkRestriction(XmppAccountHandle account, NetworkTransport transport, bool restricted) OVERRIDE;

   virtual int blockIncomingPresence(XmppAccountHandle account, bool block) OVERRIDE;

   virtual int setInactive(XmppAccountHandle account, bool inactive) OVERRIDE;

   virtual int setHibernationState(XmppAccountHandle account, bool active) OVERRIDE;

   virtual int getEntityTime(XmppAccountHandle account, const cpc::string& jid) OVERRIDE;

   virtual int enableNotification(XmppAccountHandle account, const cpc::string& node, const XmppDataForm& dataform) OVERRIDE;

   virtual int disableNotification(XmppAccountHandle account, const cpc::string& node) OVERRIDE;

   virtual int getPrivateStorageData(XmppAccountHandle account) OVERRIDE;

   virtual int setPrivateStorageData(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data) OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;

   virtual cpc::string getRemoteSyncAccountID(XmppAccountHandle account) OVERRIDE;

   virtual XmppAccountHandle getAccountHandleFromRemoteSyncID(cpc::string accountID) OVERRIDE;

   virtual int send(XmppAccountHandle account, const cpc::string& xml) OVERRIDE;

#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual PhoneInterface* phoneInterface() { return mPhone; }

   // XmppAccountManagerInternal
   // static XmppAccountManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
   virtual void create(XmppAccountHandle account, const XmppAccountSettings& settings) OVERRIDE;
   virtual void simulateNetworkLoss(XmppAccountHandle account) OVERRIDE;
   virtual void simulateNetworkRestriction(XmppAccountHandle account, bool restricted) OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void addSdkObserver(XmppAccountHandlerInternal* observer);
   void removeSdkObserver(XmppAccountHandlerInternal* observer);
   cpc::vector<XmppAccountHandle> getAccountHandles();

   void fireError(const cpc::string& errorText);

   resip::MultiReactor& getReactor() { return mReactor; }

   virtual int decodeProvisioningResponse(const cpc::string & provisioningResponse, cpc::vector<XmppAccountSettings>& outSipAccountSettings) OVERRIDE;

#ifdef CPCAPI2_AUTO_TEST
public:
#else
private:
#endif

   // NetworkChangeHandler
   virtual int onNetworkChange(const NetworkChangeEvent& event) OVERRIDE;
   int handleNetworkChangeEvent(const NetworkChangeEvent& event);

   // PhoneModule
   virtual void onLicensingError() OVERRIDE;

   // XmppAccountHandlerInternal
   virtual int onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args) OVERRIDE;
   virtual int onError(XmppAccountHandle account, const ErrorEvent& args) OVERRIDE;
   virtual int onLicensingError(XmppAccountHandle account, const LicensingErrorEvent& args) OVERRIDE;
   virtual int onAccountConfigured(XmppAccountHandle account, const XmppAccountConfiguredEvent& args) OVERRIDE;
   virtual int onEntityTime(XmppAccountHandle account, const EntityTimeEvent& args) OVERRIDE;
   virtual int onEntityFeature(XmppAccountHandle account, const EntityFeatureEvent& args) OVERRIDE;
   virtual int onStreamManagementState(XmppAccountHandle account, const StreamManagementStateEvent& args) OVERRIDE;

private:

   void createImpl(XmppAccountHandle account, const XmppAccountSettings& settings);
   void configureDefaultAccountSettingsImpl(XmppAccountHandle account, const XmppAccountSettings& settings);
   void setAccountSettingsInternalImpl(XmppAccountHandle account, const XmppAccountSettingsInternal& settings);
   void applySettingsImpl(XmppAccountHandle account);
   void setHandlerImpl(XmppAccountHandle account, XmppAccountHandler* handler);
   void publishPresenceImpl(XmppAccountHandle account, XmppRoster::PresenceType presence, const cpc::string& note, const XmppRoster::UserActivityGeneralType& userActivityGeneralType, const XmppRoster::UserActivitySpecificType& userActivitySpecificType, const cpc::string& userActivityText);
   void publishCannedPresenceImpl(XmppAccountHandle account, XmppRoster::XmppCannedStatus status, const cpc::string& note);
   void enableImpl(XmppAccountHandle account);
   void disableImpl(XmppAccountHandle account);
   void destroyImpl(XmppAccountHandle account); // override ImplManager::destroyImpl()
   void setNetworkRestrictionImpl(XmppAccountHandle account, NetworkTransport transport, bool restricted);
   void blockIncomingPresenceImpl(XmppAccountHandle account, bool block);
   void setInactiveImpl(XmppAccountHandle account, bool inactive);
   void setHibernationStateImpl(XmppAccountHandle account, bool active);

   void getEntityTimeImpl(XmppAccountHandle account, const cpc::string& jid);

   void enableNotificationImpl(XmppAccountHandle account, const cpc::string& node, const XmppDataForm& dataform);
   void disableNotificationImpl(XmppAccountHandle account, const cpc::string& node);

   void getPrivateStorageDataImpl(XmppAccountHandle account);
   void setPrivateStorageDataImpl(XmppAccountHandle account, const cpc::vector<XmppStorageData>& data);

   void sendImpl(XmppAccountHandle account, const cpc::string& xml);

   void simulateNetworkLossImpl(XmppAccountHandle account);
   void simulateNetworkRestrictionImpl(XmppAccountHandle account, bool restricted);

   RemoteSyncXmppHelper::RemoteSyncXmppHelper* getRemoteSyncHelper();

private:

   resip::Fifo<resip::ReadCallbackBase> mCallbacks;
   bool mShutdown;
   bool mPreRelease;
   PhoneInterface* mPhone;
   std::function<void(void)> mCbHook;
   std::list<XmppAccountHandlerInternal*> mSdkObservers;
   resip::MultiReactor& mReactor;
   RemoteSyncXmppHelper::RemoteSyncXmppHelper* mRemoteSyncHelperIf;
   NetworkChangeManagerInterface* mNetworkChangeManagerIf;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::Error& error);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppTLSConnectionInfo::CertificateStatus& status);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppTLSConnectionInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status& status);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::ErrorEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::LicensingErrorEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityTimeEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityFeatureEvent::Feature& feature);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::EntityFeatureEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAccount::StreamManagementStateEvent& event);

}

}

#endif // CPCAPI2_XMPP_ACCOUNT_INTERFACE_H
