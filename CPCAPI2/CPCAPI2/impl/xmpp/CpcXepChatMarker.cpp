//
//  CpcXepChatMarker.cpp
//  BriaVoip
//
//  Created by <PERSON> on June 17, 2021.
//  Copyright 2021 CounterPath Corporation. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepChatMarker.h"

const std::string CpcXepChatMarker::XMLNS_CHAT_MARKER = "urn:xmpp:chat-markers:0";

CpcXepChatMarker::CpcXepChatMarker(ChatMarkerType type, const std::string& id)
   : StanzaExtension(EXepChatMarker)
   , mType(type)
   , mId(id)
   , mValid(true)
{
}

CpcXepChatMarker::CpcXepChatMarker(const gloox::Tag* tag)
   : StanzaExtension(EXepChatMarker)
   , mType(ChatMarkerType_Invalid)
   , mValid(false)
{
   if (tag == NULL) return;

   if (tag->name() == "markable") mType = ChatMarkerType_Markable;
   else if (tag->name() == "received") mType = ChatMarkerType_Received;
   else if (tag->name() == "displayed") mType = ChatMarkerType_Displayed;
   else if (tag->name() == "acknowledged") mType = ChatMarkerType_Acknowledged;
   else return;

   mId = tag->findAttribute("id");

   mValid = true;
}

const std::string& CpcXepChatMarker::filterString() const
{
   static const std::string filter =
      "/message/markable[@xmlns='" + XMLNS_CHAT_MARKER + "']"
      "|/message/received[@xmlns='" + XMLNS_CHAT_MARKER + "']"
      "|/message/displayed[@xmlns='" + XMLNS_CHAT_MARKER + "']"
      "|/message/acknowledged[@xmlns='" + XMLNS_CHAT_MARKER + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepChatMarker::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepChatMarker(tag);
}

gloox::Tag* CpcXepChatMarker::tag() const
{
   std::string type;

   switch (mType)
   {
   case ChatMarkerType_Markable:
      type = "markable";
      break;

   case ChatMarkerType_Received:
      type = "received";
      break;

   case ChatMarkerType_Displayed:
      type = "displayed";
      break;

   case ChatMarkerType_Acknowledged:
      type = "acknowledged";
      break;

   default:
      assert(false);
      return NULL;
   }

   gloox::Tag* t = new gloox::Tag(type, "xmlns", XMLNS_CHAT_MARKER);

   if (!mValid) return NULL;

   if ( !mId.empty() )
      t->addAttribute("id", mId);

   return t;
}

gloox::StanzaExtension* CpcXepChatMarker::clone() const
{
   return new CpcXepChatMarker(*this);
}
