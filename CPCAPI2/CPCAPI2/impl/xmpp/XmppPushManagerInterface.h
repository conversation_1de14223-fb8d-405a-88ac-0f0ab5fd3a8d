#pragma once

#if !defined(__CPCAPI2_XMPP_PUSH_MANAGER_INTERFACE_H__)
#define __CPCAPI2_XMPP_PUSH_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "phone/PhoneModule.h"
#include "phone/PhoneInterface.h"
#include "xmpp/XmppPushManager.h"
#include "xmpp/XmppPushHandler.h"
#include "xmpp/XmppPushTypes.h"
#include "XmppCommon.h"

namespace CPCAPI2
{

class Phone;

namespace XmppAccount
{
class XmppAccountInterface;
}

namespace XmppPush
{

class XmppPushManagerImpl;

class XmppPushManagerInterface
   : public XmppPushManager
   , public PhoneModule
   , public XmppCommon::ImplManager<XmppPushManagerImpl>
{

public:

   XmppPushManagerInterface(Phone* phone);
   virtual ~XmppPushManagerInterface();

   // PhoneModule Interface
   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   // XmppPushManager Interface
   virtual int setHandler(XmppAccount::XmppAccountHandle account, XmppPushHandler* handler) OVERRIDE;
   virtual int configurePushSettings(XmppAccount::XmppAccountHandle account, const XmppPushSettings& settings) OVERRIDE;
   virtual int applyPushSettings(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int pushRegister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken) OVERRIDE;
   virtual int pushUnregister(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken) OVERRIDE;
   virtual int setIgnoreUnknownSender(XmppAccount::XmppAccountHandle account, bool ignoreUnknown) OVERRIDE;
   virtual int setSendNotificationsWhileAway(XmppAccount::XmppAccountHandle account, bool sendWhileAway) OVERRIDE;
   virtual int setContactMuted(XmppAccount::XmppAccountHandle account, const cpc::string& contactJid, bool muted = true) OVERRIDE;
   virtual int setGroupChatFilter(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick = "") OVERRIDE;

   virtual int enableNotifications(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int disableNotifications(XmppAccount::XmppAccountHandle account) OVERRIDE;

   virtual int registerMucOfflineMessageDelivery(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, const cpc::string& nick) OVERRIDE;

private:

   std::shared_ptr<XmppPushManagerImpl> getXmppPushManagerImpl(XmppAccount::XmppAccountHandle account);
   void getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts);

   void setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppPushHandler* handler);
   void configurePushSettingsImpl(XmppAccount::XmppAccountHandle account, const XmppPushSettings& settings);
   void applyPushSettingsImpl(XmppAccount::XmppAccountHandle account);
   void pushRegisterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken);
   void pushUnregisterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& deviceToken);
   void setIgnoreUnknownSenderImpl(XmppAccount::XmppAccountHandle account, bool ignoreUnknown);
   void setSendNotificationsWhileAwayImpl(XmppAccount::XmppAccountHandle account, bool sendWhileAway);
   void setContactMutedImpl(XmppAccount::XmppAccountHandle account, const cpc::string& contactJid, bool muted = true);
   void setGroupChatFilterImpl(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, GroupChatFilterRule rule, const cpc::string& nick = "");
   void enableNotificationsImpl(XmppAccount::XmppAccountHandle account);
   void disableNotificationsImpl(XmppAccount::XmppAccountHandle account);
   void registerOfflineMessageDeliveryImpl(XmppAccount::XmppAccountHandle account, const cpc::string& roomJid, const cpc::string& nick);

private:

   PhoneInterface* mPhone;
   XmppAccount::XmppAccountInterface* mAccountIf;
};

}

}

#endif // __CPCAPI2_XMPP_PUSH_MANAGER_INTERFACE_H__
