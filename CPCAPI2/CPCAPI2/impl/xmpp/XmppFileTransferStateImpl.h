#pragma once

#if !defined(CPCAPI2_XMPP_FILE_TRANSFER_STATE_IMPL_H)
#define CPCAPI2_XMPP_FILE_TRANSFER_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "xmpp/XmppFileTransferManager.h"
#include "xmpp/XmppFileTransferState.h"
#include "xmpp/XmppFileTransferHandler.h"
#include "XmppFileTransferInfo.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{
namespace XmppFileTransfer
{

class XmppFileTransferManagerInterface;

class XmppFileTransferStateImpl
   : public XmppFileTransferStateManager
   , public PhoneModule
   , public XmppFileTransferHandler
{
public:
   XmppFileTransferStateImpl(XmppFileTransferManagerInterface* intf);
   virtual ~XmppFileTransferStateImpl();

   virtual int getState(XmppFileTransferHandle handle, XmppFileTransferState& state) OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual int onNewFileTransfer( const XmppFileTransferHandle& handle, const NewFileTransferEvent& args ) OVERRIDE;
   virtual int onFileTransferEnded( const XmppFileTransferHandle& handle, const FileTransferEndedEvent& args ) OVERRIDE;
   virtual int onFileTransferItemProgress( const XmppFileTransferHandle& handle, const FileTransferItemProgressEvent& args ) OVERRIDE;
   virtual int onFileTransferItemEnded( const XmppFileTransferHandle& handle, const FileTransferItemEndedEvent& args ) OVERRIDE;
   virtual int onError( const XmppFileTransferHandle& handle, const ErrorEvent& args ) OVERRIDE;

private:
   std::map<XmppFileTransferHandle, XmppFileTransferState> mStateMap;
   XmppFileTransferManagerInterface* mInterface;
};

}
}

#endif // CPCAPI2_XMPP_FILE_TRANSFER_STATE_IMPL_H
