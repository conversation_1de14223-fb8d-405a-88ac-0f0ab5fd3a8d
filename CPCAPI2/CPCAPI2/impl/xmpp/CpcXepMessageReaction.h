//
//  CpcXepMessageReaction
//  BriaVoip
//
//  Created by <PERSON> on July 12, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#ifndef CPC_XEP_MESSAGE_REACTION_H__
#define CPC_XEP_MESSAGE_REACTION_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0444} (message reactions).
*/
class CpcXepMessageReaction : public gloox::StanzaExtension
{
public:
   CpcXepMessageReaction(std::string targetId, std::vector<std::string> reactions);
   CpcXepMessageReaction(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   const std::string targetId() const { return mTargetId; }
   std::vector<std::string> reactions() const { return mReactions; }

   static const std::string XMLNS_MESSAGE_REACTION;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   std::string mTargetId;
   std::vector<std::string> mReactions;

   bool mValid;
};

#endif // CPC_XEP_MESSAGE_REACTION_H__
