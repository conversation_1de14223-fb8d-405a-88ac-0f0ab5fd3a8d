#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_VCARD_MODULE == 1)

#include "cpcapi2defs.h"

#include <algorithm>
#include <sstream>

#include "XmppVCardManagerInterface.h"
#include "XmppVCardManagerImpl.h"
#include "XmppAccountInterface.h"
#include "phone/PhoneInterface.h"

#include "util/cpc_logger.h"
#include "log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_VCARD
#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{

namespace XmppVCard
{

XmppVCardManagerInterface::XmppVCardManagerInterface(CPCAPI2::Phone* phone)
   : mAccountIf(NULL)
   , mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mLocalLogger(mPhone->localLogger())
   , mPreRelease(false)
{
   mAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone));
}

XmppVCardManagerInterface::~XmppVCardManagerInterface()
{
}

void XmppVCardManagerInterface::addSdkObserver(XmppVCardHandlerInternal* observer)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), observer) != mSdkObservers.end())
   {
      StackLog(<< "XmppVCardManagerInterface::addSdkObserver(): XmppVCardManagerInterface observer: " << observer << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppVCardManagerInterface::addSdkObserver(): XmppVCardManagerInterface observer: " << observer);
   mSdkObservers.push_back(observer);
}

void XmppVCardManagerInterface::removeSdkObserver(XmppVCardHandlerInternal* observer)
{
   std::list<XmppVCardHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), observer);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppVCardManagerInterface::removeSdkObserver(): XmppVCardManagerInterface observer: " << observer << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppVCardManagerInterface::getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts)
{
   for (ImplMap::const_iterator i = mImplMap.begin(); i != mImplMap.end(); ++i)
   {
      accounts.push_back(i->first);
   }
}

XmppVCardManagerInternal* XmppVCardManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<XmppVCardManagerInternal*>(XmppVCardManager::getInterface(cpcPhone));
}

void XmppVCardManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   StackLog(<< "XmppVCardManagerInterface::setCallbackHook(): " << this << " phone: " << mPhone);
   mCbHook = std::bind(cbHook, context);
}

void XmppVCardManagerInterface::PreRelease()
{
   LocalDebugLog("XmppVCardManagerInterface::PreRelease");

   mPreRelease = true;

   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppVCardManagerImpl> vcard = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppVCardHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            vcard->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }
}

bool XmppVCardManagerInterface::PreReleaseCompleted()
{
   // If there are no more vcards, we're done.
   return (mImplMap.size() == 0);
}

void XmppVCardManagerInterface::Release()
{
   LocalDebugLog("XmppVCardManagerInterface::Release");
   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppVCardManagerImpl> vcard = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppVCardHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            vcard->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }

   mSdkObservers.clear();
   delete this;
}

int XmppVCardManagerInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppVCardManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(setHandlerCmd);
   }
   return kSuccess;
}

void XmppVCardManagerInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppVCardHandler* handler)
{
   StackLog(<< "XmppVCardManagerInterface::setHandlerImpl(): xmpp account: " << account << " handler: " << handler);

   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      cpc::string msg = cpc::string("XmppVCardManagerInterface::setHandler called with invalid account handle: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppVCardManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      StackLog(<< "XmppVCardManagerInterface::setHandlerImpl(): xmpp account: " << account << " creating new XmppVCardManagerImpl");
      // Create a new manager impl
      impl = std::make_shared<XmppVCardManagerImpl>(*acct, *this); // Destroyed in the destructor of this class

      // Keep mapping account -> manager impl
      mImplMap[account] = impl;

      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppVCardHandlerInternal*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
   }

   // Register the handler
   StackLog(<< "XmppVCardManagerInterface::setHandlerImpl(): xmpp account: " << account << " registering the handler: " << handler);
   impl->setHandler(handler);
}

XmppVCardHandle XmppVCardManagerInterface::create(XmppAccount::XmppAccountHandle account)
{
   // Create a new XmppVCardInfo and store it in our collection. Return the handle
   XmppVCardHandle handle = XmppVCardManagerImpl::sNextVCardHandle++;

   mAccountIf->post(resip::resip_bind( &XmppVCardManagerInterface::createImpl, this, account, handle));
   return handle;
}

void XmppVCardManagerInterface::createImpl(XmppAccount::XmppAccountHandle account, XmppVCardHandle handle)
{
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();

   if (acct == NULL)
   {
      WarningLog(<< "XmppVCardManagerInterface::createImpl(): " << this << " phone: " << mPhone << " account handle: " << account << " is invalid");
      cpc::string msg = cpc::string("XmppVCardManagerInterface::create called with invalid account handle: " + cpc::to_string(account));
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager impl associated with the account specified
   std::shared_ptr<XmppVCardManagerImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      cpc::string msg = cpc::string("XmppVCardManagerInterface::create before setHandler() is called: ") + cpc::to_string(account);
      mAccountIf->fireError(msg);
      return;
   }

   impl->setHandle(handle);

   DebugLog(<< "XmppVCardManagerInterface::createImpl(): " << this << " phone: " << mPhone << " account: " << account << " handle: " << handle);
   XmppVCardCreatedResultEvent evt;
   evt.account = account;
   impl->fireEvent(cpcFunc(XmppVCardHandlerInternal::onCreateVCardResult), evt);
}

void XmppVCardManagerInterface::createVCard(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppVCardManagerInterface::createVCardImpl, this, account));
}

void XmppVCardManagerInterface::createVCardImpl(XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppVCardManagerInterface::createVCardImpl(): " << this << " phone: " << mPhone << " account: " << account);
   XmppVCardHandle handle = XmppVCardManagerImpl::sNextVCardHandle++;
   createImpl(account, handle);
}

int XmppVCardManagerInterface::fetchVCard(XmppVCardHandle handle, const cpc::string& jid)
{
   mAccountIf->post( resip::resip_bind( &XmppVCardManagerInterface::fetchVCardImpl, this, handle, jid ));
   return kSuccess;
}

void XmppVCardManagerInterface::fetchVCardImpl(XmppVCardHandle handle, const cpc::string& jid)
{
   StackLog(<< "XmppVCardManagerInterface::fetchVCardImpl(): " << this << " phone: " << mPhone << " handle: " << handle);
   XmppVCardManagerImpl* pImpl = getVCardManager(handle);
   if (pImpl == NULL)
   {
      InfoLog(<< "XmppVCardManagerInterface::fetchVCardImpl(): " << this << " no vcard manager instance found for vcard handle: " << handle);
      return;
   }

   gloox::JID _jid;

   if( !_jid.setJID(jid.c_str()) || _jid.full().empty() )
   {
      pImpl->fireError(handle, "Cannot fetch vcard with empty jid.");
      return;
   }

   pImpl->fetchVCard(handle, jid);
}

int XmppVCardManagerInterface::storeVCard(XmppVCardHandle handle, const XmppVCardDetail& vcard)
{
   mAccountIf->post(resip::resip_bind(&XmppVCardManagerInterface::storeVCardImpl, this, handle, vcard));
   return kSuccess;
}

void XmppVCardManagerInterface::storeVCardImpl(XmppVCardHandle handle, const XmppVCardDetail& vcard)
{
   StackLog(<< "XmppVCardManagerInterface::storeVCardImpl(): " << this << " phone: " << mPhone << " handle: " << handle << " vcard (" << &vcard << "): " << CPCAPI2::XmppVCard::get_debug_string(vcard) << " photo image size: " << vcard.photo.binval.size());
   XmppVCardManagerImpl* pImpl = getVCardManager(handle);
   if (pImpl == NULL)
   {
      InfoLog(<< "XmppVCardManagerInterface::storeVCardImpl(): " << this << " no vcard manager instance found for vcard handle: " << handle);
      return;
   }

   pImpl->storeVCard(handle, vcard);
}

int XmppVCardManagerInterface::cancelVCardOperations(XmppVCardHandle handle)
{
   mAccountIf->post(resip::resip_bind(&XmppVCardManagerInterface::cancelVCardOperationsImpl, this, handle));
   return kSuccess;
}

void XmppVCardManagerInterface::cancelVCardOperationsImpl(XmppVCardHandle handle)
{
   XmppVCardManagerImpl* pImpl = getVCardManager(handle);
   if (pImpl == NULL)
   {
      InfoLog(<< "XmppVCardManagerInterface::cancelVCardOperationImpl(): " << this << " no vcard manager instance found for vcard handle: " << handle);
      return;
   }

   pImpl->cancelVCardOperations(handle);
}

XmppVCardManagerImpl* XmppVCardManagerInterface::getVCardManager(XmppVCardHandle handle)
{
   for(ImplMap::const_iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      std::shared_ptr<XmppVCardManagerImpl> impl = it->second;
      if (impl->getHandle() == handle) return impl.get();
   }

   InfoLog(<< "XmppVCardManagerInterface::getVCardManager(): " << this << " no vcard manager instance found for vcard handle: " << handle);
   return NULL;
}

cpc::string get_debug_string(const CPCAPI2::XmppVCard::VCardOperationResultEvent& event)
{
   std::stringstream ss;
   ss << "account: " << event.account << " handle: " << event.handle << " jid: " << event.jid << " type: " << event.type << " result: " << event.result << " success: " << event.success << " resultCode: " << event.resultCode << " resultStr: \"" << event.resultStr << "\" xmppErrorCode: " << event.xmppErrorCode << " xmppErrorStr: \"" << event.xmppErrorStr << "\"";
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppVCard::ErrorEvent& event)
{
   std::stringstream ss;
   ss << "account: " << event.account << " handle: " << event.handle << " errorText: " << event.errorText;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppVCard::VCardOperationResultEvent& event)
{
   os << CPCAPI2::XmppVCard::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppVCard::ErrorEvent& event)
{
   os << CPCAPI2::XmppVCard::get_debug_string(event);
   return os;
}

}

}

#endif
