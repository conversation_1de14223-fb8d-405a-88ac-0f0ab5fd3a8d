//
//  CpcXepChatMarker.h
//  BriaVoip
//
//  Created by <PERSON> on June 17, 2021.
//  Copyright 2021 CounterPath Corporation. All rights reserved.
//

#ifndef CPC_XEP_CHAT_MARKER_H__
#define CPC_XEP_CHAT_MARKER_H__

#include "stanzaextension.h"
#include "tag.h"

/**
* @brief This is an implementation of @xep{0333} (Chat Markers).
*/
class CpcXepChatMarker : public gloox::StanzaExtension
{
public:
   enum ChatMarkerType
   {
      ChatMarkerType_Invalid,
      ChatMarkerType_Markable,
      ChatMarkerType_Received,
      ChatMarkerType_Displayed,
      ChatMarkerType_Acknowledged
   };

   CpcXepChatMarker(ChatMarkerType type, const std::string& id = gloox::EmptyString);
   CpcXepChatMarker(const gloox::Tag* tag = NULL);

   bool isValid() const { return mValid; }
   ChatMarkerType type() const { return mType; }
   const std::string id() const { return mId; }

   static const std::string XMLNS_CHAT_MARKER;

   // virtual methods from StanzaExtension parent class
   virtual const std::string& filterString() const;
   virtual gloox::StanzaExtension* newInstance(const gloox::Tag* tag) const;
   virtual gloox::Tag* tag() const;
   virtual gloox::StanzaExtension* clone() const;

protected:
   ChatMarkerType mType;
   std::string mId;

   bool mValid;
};

#endif // CPC_XEP_CHAT_MARKER_H__
