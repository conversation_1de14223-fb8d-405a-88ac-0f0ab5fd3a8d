//
//  CpcXepMessageStanza.cpp
//  BriaVoip
//
//  Created by <PERSON> on July 09, 2024.
//  Copyright 2024 Alianza Inc. All rights reserved.
//

#include "CpcXep.h"
#include "CpcXepMessageStanza.h"

const std::string CpcXepMessageStanza::XMLNS_MESSAGE_STANZA = "urn:xmpp:sid:0";

CpcXepMessageStanza::CpcXepMessageStanza(std::string stanzaID)
   : StanzaExtension(EXepMessageStanza)
   , mStanzaID(stanzaID)
   , mValid(true)
{
}

CpcXepMessageStanza::CpcXepMessageStanza(const gloox::Tag* tag)
   : StanzaExtension(EXepMessageStanza)
   , mValid(false)
{
   if (tag == NULL) return;

   mStanzaID = tag->findAttribute("id");

   if (!mStanzaID.empty())
      mValid = true;
}

const std::string& CpcXepMessageStanza::filterString() const
{
   static const std::string filter =
      "/message/stanza-id[@xmlns='" + XMLNS_MESSAGE_STANZA + "']";

   return filter;
}

gloox::StanzaExtension* CpcXepMessageStanza::newInstance(const gloox::Tag* tag) const
{
   return new CpcXepMessageStanza(tag);
}

gloox::Tag* CpcXepMessageStanza::tag() const
{
   gloox::Tag* t = new gloox::Tag("stanza-id", "xmlns", XMLNS_MESSAGE_STANZA);

   if (!mValid) return NULL;

   t->addAttribute("id", mStanzaID);

   return t;
}

gloox::StanzaExtension* CpcXepMessageStanza::clone() const
{
   return new CpcXepMessageStanza(*this);
}
