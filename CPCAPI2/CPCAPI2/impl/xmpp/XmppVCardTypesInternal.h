#pragma once

#if !defined(CPCAPI2_XMPP_VCARD_TYPES_INTERNAL_H)
#define CPCAPI2_XMPP_VCARD_TYPES_INTERNAL_H

#include "xmpp/XmppVCardTypes.h"
#include "xmpp/XmppVCardHandler.h"
#include <document.h> // rapid<PERSON>son's DOM-style API

#include <iostream>

namespace CPCAPI2
{

namespace XmppVCard
{

std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::AddressType& type);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Name& name);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::VCardClassification& classification);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Email& email);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Telephone& telephone);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Address& address);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Label& label);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Geo& geo);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Organization& organization);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::Photo& photo);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::EmailList& list);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::TelephoneList& list);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::AddressList& list);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail::LabelList& list);
std::ostream& operator<<(std::ostream& os, const XmppVCardDetail& detail);

}

} // CPCAPI2

#endif
