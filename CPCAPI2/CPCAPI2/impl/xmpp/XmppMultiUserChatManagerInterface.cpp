#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

#include "XmppMultiUserChatManagerInterface.h"
#include "XmppMultiUserChatManagerImpl.h"

#include "util/ResipConv.h"
#include "xmpp/XmppAccountImpl.h"
#include "xmpp/XmppMultiUserChatHandlerInternal.h"

#include <sstream>

#include "util/cpc_logger.h"
#include "log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT
#define CP_LOCAL_LOGGER_VAR mLocalLogger

#define FIRE_ERROR(msg)\
{\
   std::ostringstream ss;\
   ss << msg;\
   mAccountIf->fireError(ss.str().c_str());\
}

namespace CPCAPI2
{
namespace XmppMultiUserChat
{

XmppMultiUserChatManagerInterface::XmppMultiUserChatManagerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
   , mLocalLogger(mPhone->localLogger())
   , mPreRelease(false)
   , mAccountIf(NULL)
{
   mAccountIf = dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone));
}

XmppMultiUserChatManagerInterface::~XmppMultiUserChatManagerInterface()
{
}

void XmppMultiUserChatManagerInterface::addSdkObserver(XmppMultiUserChatHandlerInternal* observer)
{
   // Make sure the handler isn't already present.
   if (std::find(mSdkObservers.begin(), mSdkObservers.end(), observer) != mSdkObservers.end())
   {
      StackLog(<< "XmppMultiUserChatManagerInterface::addSdkObserver(): XmppMultiUserChatManagerInterface observer: " << observer << " already present in list of size: " << mSdkObservers.size());
      return;
   }

   StackLog(<< "XmppMultiUserChatManagerInterface::addSdkObserver(): XmppMultiUserChatManagerInterface observer: " << observer);
   mSdkObservers.push_back(observer);
}

void XmppMultiUserChatManagerInterface::removeSdkObserver(XmppMultiUserChatHandlerInternal* observer)
{
   std::list<XmppMultiUserChatHandlerInternal*>::iterator i = std::find(mSdkObservers.begin(), mSdkObservers.end(), observer);

   if (i == mSdkObservers.end())
   {
      StackLog(<< "XmppMultiUserChatManagerInterface::removeSdkObserver(): XmppMultiUserChatManagerInterface observer: " << observer << " not found in list of size: " << mSdkObservers.size());
      return;
   }

   mSdkObservers.erase(i);
}

void XmppMultiUserChatManagerInterface::getAccountHandles(cpc::vector<XmppAccount::XmppAccountHandle>& accounts)
{
   for (ImplMap::const_iterator i = mImplMap.begin(); i != mImplMap.end(); ++i)
   {
      accounts.push_back(i->first);
   }
}

XmppMultiUserChatManagerInternal* XmppMultiUserChatManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<XmppMultiUserChatManagerInternal*>(XmppMultiUserChatManager::getInterface(cpcPhone));
}

void XmppMultiUserChatManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   StackLog(<< "XmppMultiUserChatManagerInterface::setCallbackHook(): " << this << " phone: " << mPhone);
   mCbHook = std::bind(cbHook, context);
}

PhoneInterface* XmppMultiUserChatManagerInterface::phoneInterface()
{
   return mPhone;
}

void XmppMultiUserChatManagerInterface::PreRelease()
{
   LocalDebugLog("XmppMultiUserChatManagerInterface::PreRelease");

   mPreRelease = true;

   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppMultiUserChatManagerImpl> chat = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppMultiUserChatHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            chat->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }

   // mSdkObservers.clear();
}

bool XmppMultiUserChatManagerInterface::PreReleaseCompleted()
{
   // If there are no more muc chats, we're done.
   return (mImplMap.size() == 0);
}

void XmppMultiUserChatManagerInterface::Release()
{
   LocalDebugLog("XmppMultiUserChatManagerInterface::Release");
   cpc::vector<XmppAccount::XmppAccountHandle> accounts;
   getAccountHandles(accounts);
   for (cpc::vector<XmppAccount::XmppAccountHandle>::iterator i = accounts.begin(); i != accounts.end(); ++i)
   {
      XmppAccount::XmppAccountHandle h = (*i);
      if (std::shared_ptr<XmppMultiUserChatManagerImpl> chat = getImpl(h))
      {
         // Cleanup the sdk observer handlers, to remove possibility of dangling handlers being triggered
         for (std::list<XmppMultiUserChatHandlerInternal*>::iterator j = mSdkObservers.begin(); j != mSdkObservers.end(); ++j)
            chat->removeSdkObserver(*j);

         mImplMap.erase(h);
      }
   }

   mSdkObservers.clear();
   delete this;
}

int XmppMultiUserChatManagerInterface::setHandler(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppMultiUserChatManagerInterface::setHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setHandlerImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandler* handler)
{
   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // No chat manager associated with the account

      // Get the associated account object
      XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();

      if (acct == NULL)
      {
         FIRE_ERROR("XmppMultiUserChatManager::setHandler with invalid account handle: " << account);
         return;
      }

      // Create a new chat manager
      impl = std::make_shared<XmppMultiUserChatManagerImpl>(mPhone, *acct, *this); // Destroyed in the destructor of this class

      // Register the event handler
      //mAccountIf->getReactor().registerEventHandler(impl);

      // Keep mapping account -> chat manager
      mImplMap[account] = impl;

      StackLog(<< "XmppMultiUserChatManagerInterface::setHandlerImpl(): " << this << " phone: " << mPhone << " account: " << account << " adding " << mSdkObservers.size() << " sdk observers to new muc interface");
      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::list<XmppMultiUserChatHandlerInternal*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
   }

   impl->setHandler(handler);
}

int XmppMultiUserChatManagerInterface::setIMCommandHandler(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&XmppMultiUserChatManagerInterface::setIMCommandHandlerImpl, this, account, handler);
   if (handler == NULL)
   {
      mAccountIf->execute(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->post(setHandlerCmd);
   }

   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setIMCommandHandlerImpl(XmppAccount::XmppAccountHandle account, XmppIMCommand::XmppMultiUserChatIMCommandHandler* handler)
{
   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);

   if (impl == NULL)
   {
      FIRE_ERROR("XmppMultiUserChatManager::setHandler with invalid account handle: " << account);
      return;
   }

#if 0 // bliu: TODO
      // .jjg. order is important here -- the SDK observers need to be first
      // since some of them (like the XmppFileTransferState module) need to do
      // their thing before the app gets called back
      for (std::set<XmppMultiUserChatHandlerInternal*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
      {
         impl->addSdkObserver(*it);
      }
#endif

   impl->setHandler(handler);
}

int XmppMultiUserChatManagerInterface::getRoomList(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::getRoomListImpl, this, account));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::getRoomListImpl(XmppAccount::XmppAccountHandle account)
{
   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomList with invalid account handle: " << account);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomList before account is connected: " << account);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomList before XmppMultiUserChatManager::setHandler");
      return;
   }

   impl->getRoomList();
}

int XmppMultiUserChatManagerInterface::setRoomBookmarks(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::setRoomBookmarksImpl, this, account, bookmarks));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setRoomBookmarksImpl(XmppAccount::XmppAccountHandle account, const cpc::vector<RoomBookmark>& bookmarks)
{
   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::setRoomBookmarks with invalid account handle: " << account);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::setRoomBookmarks before account is connected: " << account);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::setRoomBookmarks before XmppMultiUserChatManager::setHandler");
      return;
   }

   impl->setRoomBookmarks(bookmarks);
}

int XmppMultiUserChatManagerInterface::getRoomBookmarks(XmppAccount::XmppAccountHandle account)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::getRoomBookmarksImpl, this, account));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::getRoomBookmarksImpl(XmppAccount::XmppAccountHandle account)
{
   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomBookmarks with invalid account handle: " << account);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomBookmarks before account is connected: " << account);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      FIRE_ERROR("XmppMultiUserChatManager::getRoomBookmarks before XmppMultiUserChatManager::setHandler");
      return;
   }

   impl->getRoomBookmarks();
}

XmppMultiUserChatHandle XmppMultiUserChatManagerInterface::create(XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   // Create a new XmppVCardInfo and store it in our collection. Return the handle
   XmppMultiUserChatHandle handle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatHandle++;

   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::createImpl, this, account, handle, room));
   return handle;
}

void XmppMultiUserChatManagerInterface::create(XmppMultiUserChatHandle handle, XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::createImpl, this, account, handle, room));
}

void XmppMultiUserChatManagerInterface::createMultiUserChat(CPCAPI2::XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::createMultiUserChatImpl, this, account, room));
}

void XmppMultiUserChatManagerInterface::createMultiUserChatImpl(CPCAPI2::XmppAccount::XmppAccountHandle account, const cpc::string& room)
{
   XmppMultiUserChatHandle handle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatHandle++;
   DebugLog(<< "XmppMultiUserChatManagerInterface::createMultiUserChatImpl(): " << this << " phone: " << mPhone << " account: " << account << " room: " << room << " muc: " << handle);
   createImpl(account, handle, room);
}

void XmppMultiUserChatManagerInterface::createImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandle handle, const cpc::string& room)
{
   DebugLog(<< "XmppMultiUserChatManagerInterface::createImpl(): " << this << " phone: " << mPhone << " account: " << account << " room: " << room << " muc: " << handle);

   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      FIRE_ERROR("Creating multi user chat session with invalid account handle: " << account);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      FIRE_ERROR("Creating multi user chat session before account is connected: " << account);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      FIRE_ERROR("Creating multi user chat session before XmppMultiUserChatManager::setHandler");
      return;
   }

   impl->create(handle, room);
}

XmppMultiUserChatHandle XmppMultiUserChatManagerInterface::create(XmppAccount::XmppAccountHandle account, bool instantRoom)
{
   // Create a new XmppMultiUserChatInfo and store it in our collection. Return the handle
   XmppMultiUserChatHandle handle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatHandle++;

   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::DEPRECATE_createImpl, this, account, handle, instantRoom));
   return handle;
}

void XmppMultiUserChatManagerInterface::create(XmppMultiUserChatHandle handle, XmppAccount::XmppAccountHandle account, bool instantRoom)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::DEPRECATE_createImpl, this, account, handle, instantRoom));
}

void XmppMultiUserChatManagerInterface::DEPRECATE_createImpl(XmppAccount::XmppAccountHandle account, XmppMultiUserChatHandle handle, bool instantRoom)
{
   DebugLog(<< "XmppMultiUserChatManagerInterface::DEPRECATE_createImpl(): " << this << " phone: " << mPhone << " account: " << account << " instantRoom: " << instantRoom << " muc: " << handle);

   // Retrieve the account
   XmppAccount::XmppAccountImpl* acct = mAccountIf->getImpl(account).get();
   if (acct == NULL)
   {
      // No account found. Send an error
      FIRE_ERROR("Creating multi user chat session with invalid account handle: " << account);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isConnected())
   {
      // Account not enabled. Send an error
      FIRE_ERROR("Creating multi user chat session before account is connected: " << account);
      return;
   }

   // Retrieve the chat manager associated with the account specified
   std::shared_ptr<XmppMultiUserChatManagerImpl> impl = getImpl(account);
   if (impl == NULL)
   {
      // Account not enabled. Send an error
      FIRE_ERROR("Creating multi user chat session before XmppMultiUserChatManager::setHandler");
      return;
   }

   impl->DEPRECATE_create(handle, instantRoom);
}

int XmppMultiUserChatManagerInterface::destroyRoom(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::destroyRoomImpl, this, handle, reason, alternate, password));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::destroyRoomImpl(XmppMultiUserChatHandle handle, const cpc::string& reason, const cpc::string& alternate, const cpc::string& password)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::destroy called with invalid handle: " << handle);
      return;
   }

   context.impl->destroyRoom(context.info, reason, alternate, password);
}

int XmppMultiUserChatManagerInterface::getRoomInfo(XmppMultiUserChatHandle handle)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::getRoomInfoImpl, this, handle));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::getRoomInfoImpl(XmppMultiUserChatHandle handle)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::getRoomInfo called with invalid handle: " << handle);
      return;
   }

   context.impl->getRoomInfo(context.info);
}

int XmppMultiUserChatManagerInterface::getRoomsInfo(const cpc::vector<XmppMultiUserChatHandle>& handles)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::getRoomsInfoImpl, this, handles));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::getRoomsInfoImpl(const cpc::vector<XmppMultiUserChatHandle>& handles)
{
   for (cpc::vector<XmppMultiUserChatHandle>::const_iterator it = handles.begin(); it != handles.end(); ++it)
   {
      getRoomInfoImpl(*it);
   }
}

int XmppMultiUserChatManagerInterface::accept(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::acceptImpl, this, handle, nickname, historyRequester, historyToAdd));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::acceptImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::accept called with invalid handle: " << handle);
      return;
   }

   context.impl->accept(context.info, nickname, historyRequester, historyToAdd);
}

int XmppMultiUserChatManagerInterface::decline(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::declineImpl, this, handle, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::declineImpl(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::decline called with invalid handle: " << handle);
      return;
   }

   context.impl->decline(context.info, reason);
}

int XmppMultiUserChatManagerInterface::join(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::joinImpl, this, handle, config, nickname, password, historyRequester, historyToAdd));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::joinImpl(XmppMultiUserChatHandle handle, RoomConfig config, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::join called with invalid handle: " << handle);
      return;
   }

   context.impl->join(context.info, config, nickname, password, historyRequester, historyToAdd);
}

int XmppMultiUserChatManagerInterface::join(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::DEPRECATE_joinImpl, this, handle, room, nickname, password, historyRequester, historyToAdd));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::DEPRECATE_joinImpl(XmppMultiUserChatHandle handle, const cpc::string& room, const cpc::string& nickname, const cpc::string& password, const cpc::string& historyRequester, const cpc::vector<XmppMultiUserChatHistoryItem>& historyToAdd)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::join called with invalid handle: " << handle);
      return;
   }

   context.impl->DEPRECATE_join(context.info, room, nickname, password, historyRequester, historyToAdd);
}

int XmppMultiUserChatManagerInterface::leave(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::leaveImpl, this, handle, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::leaveImpl(XmppMultiUserChatHandle handle, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::leave called with invalid handle: " << handle);
      return;
   }

   context.impl->leave(context.info, reason);
}

XmppMultiUserChatMessageHandle XmppMultiUserChatManagerInterface::sendMessage(XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html)
{
   XmppMultiUserChatMessageHandle messageHandle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendMessageImpl, this, handle, messageHandle, plain, html));
   return messageHandle;
}

void XmppMultiUserChatManagerInterface::sendMessage(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& plain, const cpc::string& html)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendMessageImpl, this, handle, message, plain, html));
}

void XmppMultiUserChatManagerInterface::sendMessageImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& plain, const cpc::string& html)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::sendMessage called with invalid handle: " << handle);
      return;
   }

   context.impl->sendMessage(context.info, message, plain, html);
}

XmppMultiUserChatMessageHandle XmppMultiUserChatManagerInterface::sendReaction(XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   XmppMultiUserChatMessageHandle messageHandle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendReactionImpl, this, handle, messageHandle, target, reactions));
   return messageHandle;
}

void XmppMultiUserChatManagerInterface::sendReaction(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendReactionImpl, this, handle, message, target, reactions));
}

void XmppMultiUserChatManagerInterface::sendReactionImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& target, const cpc::vector<cpc::string>& reactions)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::sendReaction called with invalid handle: " << handle);
      return;
   }

   context.impl->sendReaction(context.info, message, target, reactions);
}

XmppMultiUserChatMessageHandle XmppMultiUserChatManagerInterface::sendMessageRetraction(XmppMultiUserChatHandle handle, const cpc::string& target)
{
   XmppMultiUserChatMessageHandle messageHandle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendMessageRetractionImpl, this, handle, messageHandle, target));
   return messageHandle;
}

void XmppMultiUserChatManagerInterface::sendMessageRetraction(XmppMultiUserChatMessageHandle message, XmppMultiUserChatHandle handle, const cpc::string& target)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendMessageRetractionImpl, this, handle, message, target));
}

void XmppMultiUserChatManagerInterface::sendMessageRetractionImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& target)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::sendMessageRetraction called with invalid handle: " << handle);
      return;
   }

   context.impl->sendMessageRetraction(context.info, message, target);
}

XmppMultiUserChatMessageHandle XmppMultiUserChatManagerInterface::replaceMessage(XmppMultiUserChatHandle handle, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html)
{
   XmppMultiUserChatMessageHandle messageHandle = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::replaceMessageImpl, this, handle, messageHandle, replaces, plain, html));
   return messageHandle;
}

void XmppMultiUserChatManagerInterface::replaceMessageImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, const cpc::string& replaces, const cpc::string& plain, const cpc::string& html)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::sendMessage called with invalid handle: " << handle);
      return;
   }

   context.impl->replaceMessage(context.info, message, replaces, plain, html);
}

int XmppMultiUserChatManagerInterface::setIsComposingMessage(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::setIsComposingMessageImpl, this, handle, refreshInterval, idleInterval));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setIsComposingMessageImpl(XmppMultiUserChatHandle handle, int refreshInterval, int idleInterval)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::setIsComposingMessage called with invalid handle: " << handle);
      return;
   }

   context.impl->setIsComposingMessage(context.info, refreshInterval, idleInterval);
}

int XmppMultiUserChatManagerInterface::publishPresence(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::publishPresenceImpl, this, handle, presence, note));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::publishPresenceImpl(XmppMultiUserChatHandle handle, XmppRoster::PresenceType presence, const cpc::string& note)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::publishPresence called with invalid handle: " << handle);
      return;
   }

   context.impl->publishPresence(context.info, presence, note);
}

int XmppMultiUserChatManagerInterface::changeNickname(XmppMultiUserChatHandle handle, const cpc::string& nickname)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::changeNicknameImpl, this, handle, nickname));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::changeNicknameImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::changeNickname called with invalid handle: " << handle);
      return;
   }

   context.impl->changeNickname(context.info, nickname);
}

int XmppMultiUserChatManagerInterface::changeSubject(XmppMultiUserChatHandle handle, const cpc::string& subject)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::changeSubjectImpl, this, handle, subject));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::changeSubjectImpl(XmppMultiUserChatHandle handle, const cpc::string& subject)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::changeSubject called with invalid handle: " << handle);
      return;
   }

   context.impl->changeSubject(context.info, subject);
}

int XmppMultiUserChatManagerInterface::invite(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::inviteImpl, this, handle, jid, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::inviteImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::invite called with invalid handle: " << handle);
      return;
   }

   context.impl->invite(context.info, jid, reason);
}

int XmppMultiUserChatManagerInterface::kick(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::kickImpl, this, handle, nickname, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::kickImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::kick called with invalid handle: " << handle);
      return;
   }

   context.impl->kick(context.info, nickname, reason);
}

int XmppMultiUserChatManagerInterface::ban(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::banImpl, this, handle, nickname, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::banImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::ban called with invalid handle: " << handle);
      return;
   }

   context.impl->ban(context.info, nickname, reason);
}

int XmppMultiUserChatManagerInterface::changeAffiliation(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::changeAffiliationImpl, this, handle, nickname, affiliation, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::changeAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::changeAffiliation called with invalid handle: " << handle);
      return;
   }

   context.impl->changeAffiliation(context.info, nickname, affiliation, reason);
}

int XmppMultiUserChatManagerInterface::changeJidAffiliation(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::changeJidAffiliationImpl, this, handle, jid, affiliation, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::changeJidAffiliationImpl(XmppMultiUserChatHandle handle, const cpc::string& jid, const XmppMultiUserChatAffiliation& affiliation, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::changeAffiliation called with invalid handle: " << handle);
      return;
   }

   context.impl->changeJidAffiliation(context.info, jid, affiliation, reason);
}

int XmppMultiUserChatManagerInterface::changeRole(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::changeRoleImpl, this, handle, nickname, role, reason));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::changeRoleImpl(XmppMultiUserChatHandle handle, const cpc::string& nickname, const XmppMultiUserChatRole& role, const cpc::string& reason)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::changeRole with invalid handle: " << handle);
      return;
   }

   context.impl->changeRole(context.info, nickname, role, reason);
}

int XmppMultiUserChatManagerInterface::requestConfigurations(XmppMultiUserChatHandle handle)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::requestConfigurationsImpl, this, handle));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::requestConfigurationsImpl(XmppMultiUserChatHandle handle)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::requestConfigurations with invalid handle: " << handle);
      return;
   }

   context.impl->requestConfigurations(context.info);
}

int XmppMultiUserChatManagerInterface::setConfigurations(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform)
{
   mAccountIf->post(resip::resip_bind(static_cast<void(XmppMultiUserChatManagerInterface::*)(XmppMultiUserChatHandle, const XmppAccount::XmppDataForm&)>(&XmppMultiUserChatManagerInterface::setConfigurationsImpl), this, handle, dataform));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppAccount::XmppDataForm& dataform)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::setConfiguration with invalid handle: " << handle);
      return;
   }

   context.impl->setConfigurations(context.info, dataform);
}

int XmppMultiUserChatManagerInterface::setConfigurations(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations)
{
   mAccountIf->post(resip::resip_bind(static_cast<void(XmppMultiUserChatManagerInterface::*)(XmppMultiUserChatHandle, const XmppMultiUserChatConfigurations&)>(&XmppMultiUserChatManagerInterface::setConfigurationsImpl), this, handle, configurations));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setConfigurationsImpl(XmppMultiUserChatHandle handle, const XmppMultiUserChatConfigurations& configurations)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::setConfiguration with invalid handle: " << handle);
      return;
   }

   context.impl->setConfigurations(context.info, configurations);
}

int XmppMultiUserChatManagerInterface::requestList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::requestListImpl, this, handle, type));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::requestListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::requestOwnerList with invalid handle: " << handle);
      return;
   }

   context.impl->requestList(context.info, type);
}

int XmppMultiUserChatManagerInterface::setList(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::setListImpl, this, handle, type, items));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::setListImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatListType type, const cpc::vector<XmppMultiUserChatConfigurationsListItem>& items)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::setOwnerList with invalid handle: " << handle);
      return;
   }

   context.impl->setList(context.info, type, items);
}

XmppMultiUserChatMessageHandle XmppMultiUserChatManagerInterface::sendIMCommand(XmppMultiUserChatHandle handle, int type, const cpc::string& payload)
{
   XmppMultiUserChatMessageHandle message = XmppMultiUserChatManagerImpl::sNextXmppMultiUserChatMessageHandle++;
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::sendIMCommandImpl, this, handle, message, type, payload));
   return message;
}

void XmppMultiUserChatManagerInterface::sendIMCommandImpl(XmppMultiUserChatHandle handle, XmppMultiUserChatMessageHandle message, int type, const cpc::string& payload)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::sendIMCommand with invalid handle: " << handle);
      return;
   }

   context.impl->sendIMCommand(context.info, message, type, payload);
}

int XmppMultiUserChatManagerInterface::notifyMessageRead(XmppMultiUserChatHandle handle, const cpc::string& messageId)
{
   mAccountIf->post(resip::resip_bind(&XmppMultiUserChatManagerInterface::notifyMessageReadImpl, this, handle, messageId));
   return kSuccess;
}

void XmppMultiUserChatManagerInterface::notifyMessageReadImpl(XmppMultiUserChatHandle handle, const cpc::string& messageId)
{
   InfoContext context;

   if (!getMultiUserChatInfoContext(handle, context))
   {
      FIRE_ERROR("XmppMultiUserChatManager::notifyMessageRead with invalid handle: " << handle);
      return;
   }

   context.impl->notifyMessageRead(context.info, messageId);
}

bool XmppMultiUserChatManagerInterface::getMultiUserChatInfoContext(XmppMultiUserChatHandle handle, InfoContext& context) const
{
   for (ImplMap::const_iterator it = mImplMap.begin(), end = mImplMap.end(); it != end; ++it)
   {
      if (XmppMultiUserChatInfo* info = it->second->getMultiUserChatInfo(handle))
      {
         context.impl = it->second.get();
         context.info = info;
         return true;
      }
   }

   return false;
}

}

}

#endif
