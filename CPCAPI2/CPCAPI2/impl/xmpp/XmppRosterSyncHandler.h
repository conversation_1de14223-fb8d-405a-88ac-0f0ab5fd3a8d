#pragma once
#ifndef __CPCAPI2_XMPPROSTERSYNCHA<PERSON>LER_H__
#define __CPCAPI2_XMPPROSTERSYNCHA<PERSON>LER_H__

namespace CPCAPI2
{
   namespace XmppRoster
   {
      /**
       * Marker interface to determine whether or not handler
       * should be called synchronously (used only for internal
       * handlers).
       */
      class XmppRosterSyncHandler
      {
      };
   }
}

#endif // __CPCAPI2_XMPPROSTERSYNCHANDLER_H__