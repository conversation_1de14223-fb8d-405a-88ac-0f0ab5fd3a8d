#pragma once
#ifndef __PROVISIONING_STRETTOPROVISIONINGTYPESINTERNAL_H__
#define __PROVISIONING_STRETTOPROVISIONINGTYPESINTERNAL_H__

#include <strettoprovisioning/StrettoProvisioningTypes.h>
#include <string>

namespace CPCAPI2
{
   namespace StrettoProvisioning
   {
      class StrettoProvisioningImpl;

      typedef struct StrettoProvisioningInfo
      {
         AuthInfo authInfo;
         BuildInfo buildInfo;

         StrettoProvisioningImpl *pImpl;
      } StrettoProvisioningInfo;
   }
}

#endif /* __PROVISIONING_STRETTOPROVISIONINGTYPESINTERNAL_H__ */
