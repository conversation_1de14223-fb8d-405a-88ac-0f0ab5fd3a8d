#pragma once

#ifndef CPCAPI2_STRETTOPROVISIONINGIMPL_H
#define CPCAPI2_STRETTOPROVISIONINGIMPL_H

#include <atomic>
#include <map>
#include <thread>
#include <vector>

#include <boost/asio.hpp>

#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#include "../util/HttpClient.h"

#include "cpcapi2defs.h"

#include <strettoprovisioning/StrettoProvisioning.h>
#include <strettoprovisioning/StrettoProvisioningTypes.h>
#include <strettoprovisioning/StrettoProvisioningInternalTypes.h>
#include <strettoprovisioning/StrettoProvisioningHandler.h>
#include "StrettoProvisioningTypesInternal.h"
#include "StrettoProvisioningSyncHandler.h"

#include "../util/DumFpCommand.h"

namespace CPCAPI2
{
   class WebSocketCommand;

   namespace StrettoProvisioning
   {
      class StrettoProvisioningInterface;
      class StrettoProvisioningImpl
      {
      public:
         StrettoProvisioningImpl(
            StrettoProvisioningInterface* iface,
            StrettoProvisioningHandle hStrettoProvisioning);
         virtual ~StrettoProvisioningImpl();

         // StrettoProvisioning Implementation
         int configureChannelSettings( StrettoProvisioningInfo* info, const StrettoProvisioningInternalSettings& settings );
         int applySettings();
         int request();
      
         const StrettoProvisioningHandle getStrettoProvisioningHandle() { return m_Handle; }

      private: // methods

         // Returns a new string with escaped/encoded special characters.
         void processServerResponse(int errorCode, int responseStatus, std::string responseBody );

      private: // data

         StrettoProvisioningInterface* m_If;

         // HTTP Client used to communicate to the StrettoProvisioning server
         HTTPClient* m_HttpClient;

         // The channel for which this service applies.
         StrettoProvisioningHandle m_Handle;

         StrettoProvisioningInternalSettings m_Settings;
      };
   }
}
#endif // CPCAPI2_STRETTOPROVISIONINGIMPL_H
