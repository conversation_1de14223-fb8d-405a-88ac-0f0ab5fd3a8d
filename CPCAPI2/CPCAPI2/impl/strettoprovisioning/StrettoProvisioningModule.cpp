#include "brand_branded.h"

#include <strettoprovisioning/StrettoProvisioning.h>

#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
#include "StrettoProvisioningInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace StrettoProvisioning
   {
      StrettoProvisioning* StrettoProvisioning::getInterface( CPCAPI2::Phone* cpcPhone )
      {
#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<StrettoProvisioningInterface>(phone, "StrettoProvisioning");
#else
         return NULL;
#endif
      }
   }
}
