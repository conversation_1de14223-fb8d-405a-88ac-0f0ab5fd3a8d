#pragma once

#ifndef CPCAPI2_STRETTOPROVISIONINGINTERFACE_H
#define CPCAPI2_STRETTOPROVISIONINGINTERFACE_H

#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include "cpcapi2defs.h"
#include <phone/PhoneModule.h>
#include "phone/Cpcapi2EventSource.h"
#include <strettoprovisioning/StrettoProvisioning.h>
#include <strettoprovisioning/StrettoProvisioningTypes.h>
#include <strettoprovisioning/StrettoProvisioningHandler.h>
#include "strettoprovisioning/StrettoProvisioningSyncHandler.h"
#include "StrettoProvisioningTypesInternal.h"
#include "../util/AutoTestProcessor.h"

namespace CPCAPI2
{
   class Phone;

   namespace StrettoProvisioning
   {
      class StrettoProvisioningInternalSettings;
   
      class StrettoProvisioningInterface :
         public CPCAPI2::EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>,
         public StrettoProvisioning,
         public CPCAPI2::PhoneModule
      {
      public:
         StrettoProvisioningInterface(Phone* phone);
         virtual ~StrettoProvisioningInterface();

         // PhoneModule implementation
         void Release() OVERRIDE;

         // Provisioning Implementation
         virtual StrettoProvisioningHandle create( void ) OVERRIDE;
         virtual int configureSettings( const StrettoProvisioningHandle& hProvisioning, const StrettoProvisioningSettings& settings ) OVERRIDE;
         virtual int applySettings( const StrettoProvisioningHandle& hProvisioning ) OVERRIDE;
         virtual int request( const StrettoProvisioningHandle& hProvisioning ) OVERRIDE;
         virtual int destroy( const StrettoProvisioningHandle& hProvisioning ) OVERRIDE;
         virtual int setHandler( const StrettoProvisioningHandle& hProvisioning, StrettoProvisioningHandler* handler) OVERRIDE;
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE {
            CPCAPI2::EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>::postToSdkThread(resip::resip_bind(&StrettoProvisioningInterface::execFunction2, this, pfun, obj));
         }

         virtual int process(unsigned int timeout) OVERRIDE {
            return CPCAPI2::EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>::process(timeout);
         }
         virtual void interruptProcess() OVERRIDE {
            CPCAPI2::EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>::interruptProcess();
         }
         virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
            CPCAPI2::EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>::setCallbackHook(cbHook, context);
         }

         int createImpl( const StrettoProvisioningHandle& hProvisioning );
         int configureSettingsImpl(const StrettoProvisioningHandle& hProvisioning, const StrettoProvisioningInternalSettings& settings);
         int applySettingsImpl( const StrettoProvisioningHandle& hProvisioning );
         int requestImpl( const StrettoProvisioningHandle& hProvisioning );
         int destroyImpl( const StrettoProvisioningHandle& hProvisioning );

      private: // methods

         // This method will set the out param to the channel info (internal) if it
         // exists, and return true. Otherwise, an error will be thrown to the application
         // and false will be returned.
         bool getStrettoProvisioningInfo(const StrettoProvisioningHandle& hProvisioning, StrettoProvisioningInfo*& pOutInfo);

         void execFunction2(void(*func)(void*), void* context)
         {
            (*func)(context);
         }

      private: // data

         friend class StrettoProvisioningImpl;

         // Static handle counter
         static std::atomic< StrettoProvisioningHandle > s_CurrentHandle;

         // Map of AccountHandle(s) to AccountInfo(s)
         std::map< StrettoProvisioningHandle, StrettoProvisioningInfo* > m_InfoMap;

         // used for dispatching
         resip::MultiReactor m_IOService;
            
         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;
      };
   }
}
#endif // CPCAPI2_STRETTOPROVISIONINGINTERFACE_H
