#include <atomic>
#include <map>
#include <thread>
#include <algorithm>
#include <string>
#include <regex>

#include <stdlib.h>

#include <brand_branded.h>

#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PROVISIONING

#include <sstream>

#include <boost/asio.hpp>

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "cpcapi2defs.h"
#include <phone/PhoneModule.h>
#include "../util/DumFpCommand.h"

#include <writer.h>
#include <stringbuffer.h>

#if defined(_WIN32)
#include <licensing/licensekey/HardwareHash.h>
#elif defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !(TARGET_OS_IPHONE))
#include <licensing/licensekey/HardwareHash.h>
#endif

#include <strettoprovisioning/StrettoProvisioning.h>
#include <strettoprovisioning/StrettoProvisioningTypes.h>
#include <strettoprovisioning/StrettoProvisioningInternalTypes.h>
#include <strettoprovisioning/StrettoProvisioningHandler.h>
#include "StrettoProvisioningTypesInternal.h"
#include "StrettoProvisioningImpl.h"
#include "StrettoProvisioningInterface.h"
#include "util/PlatformUtils.h"

#include <util/DeviceInfo.h>
#include <document.h>

// This is here just for the percent encoding functions of the URL
#include <utils/msrp_string.h>
#include <utils/msrp_mem.h>

#include <curlpp/cURLpp.hpp>

/* delims and sub-delims according to rfc 3986. NOTE: I've added SPACE
   to this list to reflect common usage. */
#define RESERVED_CHARS ":/?#[]@!$&'()*+,;= "

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   ErrorEvent evt; \
   evt.errorText = ( ERRTEXT ); \
   evt.errNo = 0; \
   fireEvent( cpcFunc( StrettoProvisioningHandler::onError ),evt ); \
   return kError; \
}
#define INVALID_HANDLER_STR "Provisioning Handler Already Set"

using namespace CPCAPI2::StrettoProvisioning;
using CPCAPI2::WebSocketCommand;

StrettoProvisioningImpl::StrettoProvisioningImpl(
   StrettoProvisioningInterface* iface, StrettoProvisioningHandle hProvisioning)
   : m_If(iface), m_HttpClient( new HTTPClient ),
     m_Handle(hProvisioning)
{
}

StrettoProvisioningImpl::~StrettoProvisioningImpl()
{
   delete m_HttpClient;
}

// Provisioning Implementation
int StrettoProvisioningImpl::configureChannelSettings( StrettoProvisioningInfo* info, const StrettoProvisioningInternalSettings& settings )
{
   // Always update the settings
   m_Settings = settings;

   return kSuccess;
}

int StrettoProvisioningImpl::applySettings()
{
   return kSuccess;
}

int StrettoProvisioningImpl::request()
{
   // Create the XML document which will be used for the POST to the provisioning server
   std::string postURL;
   {
      // Build the URL by appending all of the items as query parameters.
      std::stringstream ss;
      ss << m_Settings.provisioningUrl;

      ss << "?"; // start the query params.. woo

      if( m_Settings.authInfo.userName.size() > 0 )
      {
         ss << "username=" << curlpp::escape(m_Settings.authInfo.userName.c_str());
      }

      if (m_Settings.authInfo.password.size() > 0)
      {
         ss << "&password=" << curlpp::escape(m_Settings.authInfo.password.c_str());
      }

      if (m_Settings.authInfo.spid.size() > 0)
      {
         ss << "&spid=" << curlpp::escape(m_Settings.authInfo.spid.c_str());
      }

      PlatformUtils::OSInfo osInfo;
      PlatformUtils::PlatformUtils::getOSInfo( osInfo );

      if( osInfo.osVersion.size() > 0 )
      {
         ss << "&os=" << curlpp::escape(osInfo.osVersion.c_str());
      }

      std::string type = "sdk."; // not really used; kind of redundant AFAIK
      switch( osInfo.osType )
      {
      case PlatformUtils::OSType_Windows:
         type += "windows";
         break;
      case PlatformUtils::OSType_OSX:
         type += "apple";
         break;
      case PlatformUtils::OSType_Linux:
         type += "linux";
         break;
      case PlatformUtils::OSType_Android:
         type += "android";
         break;
      case PlatformUtils::OSType_iOS:
         type += "ios";
         break;
      case PlatformUtils::OSType_Other:
         type += "other";
         break;
      }

      PlatformUtils::DeviceInfo deviceInfo;
      PlatformUtils::PlatformUtils::getDeviceInfo( deviceInfo );

      // Add a "dot" plus the form factor.
      switch( deviceInfo.deviceFormFactor )
      {
      case PlatformUtils::DeviceFormFactor_Phone:
         type += ".phone";
         break;
      case PlatformUtils::DeviceFormFactor_Tablet:
         type += ".tablet";
         break;
      case PlatformUtils::DeviceFormFactor_Computer:
      default:
         type += ".desktop";
         break;
      }

      ss << "&type=" << curlpp::escape(type.c_str());

      if( deviceInfo.deviceModel.size() > 0 )
      {
         ss << "&device=" << curlpp::escape(deviceInfo.deviceModel.c_str());
      }

      // Use getInstanceId() for the uuid. This implementation is
      // available on all platforms.
      cpc::string instanceID;
      if( CPCAPI2::DeviceInfo::getInstanceId( instanceID ) == kSuccess )
      {
         ss << "&uuid=" << curlpp::escape(instanceID.c_str());
      }

      // "duui" (based on HardwareHash) is used on SOME platforms.
#if defined(_WIN32)
      Licensing::HardwareHash hh;
      std::string hash( hh.GenerateHardwareHash() );
      ss << "&duui=" << curlpp::escape(hash);
#elif defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !(TARGET_OS_IPHONE))
      Licensing::HardwareHash hh;
      std::string hash( hh.GenerateHardwareHash() );
      ss << "duui=" << curlpp::escape(hash);
#endif

      if( m_Settings.buildInfo.versionString.size() > 0 )
      {
         ss << "&build=" << curlpp::escape(m_Settings.buildInfo.versionString.c_str());
      }

      // Add the requested responseformat (json)
      ss << "&responseformat=application%2Fjson";
      postURL = ss.str();
   }

   int errorCode;
   int responseStatus;
   cpc::string result;
   cpc::string contentType;
   HTTPClient::RedirectInfo redirectInfo;
   cpc::vector< HTTPClient::StringPair > customHeaders;
   m_HttpClient->HTTPSendMessage(
      HTTPClient::EHTTPVerbGET,
      postURL.c_str(),
      NULL, // mime-type
      m_Settings.authInfo.userName.c_str(),
      m_Settings.authInfo.password.c_str(),
      NULL, // client certificate
      NULL, // client certificate password
      NULL, // everything is in the parameter
      0,
      0,
      false,
      false,
      false, // true,  //ignore all the certificate validation error- only for licensing - FB #52301
      false,  //no cookie support for licensing yet; m_ipSettings->GetBool("system", "http:enable_cookies",false),
      "",     //no cookie support for licensing yet; m_ipSettings->GetString("system", "http:cookie_file",""),
      customHeaders,
      false,
      false,
      errorCode,
      responseStatus,
      contentType,
      result,
      redirectInfo,
      NULL,
      "" // no proxy support for provisioning (may receive a redirect however)
   );

   processServerResponse(errorCode, responseStatus, std::string(result.c_str()));
   return kSuccess;
}

void StrettoProvisioningImpl::processServerResponse( int errorCode, int responseStatus, std::string responseBody )
{
   // Did we get a response?
   if (errorCode != 0)
   {
      StrettoProvisioningErrorEvent event;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse(): Error:" << errorCode << " Reason:" << responseStatus );

      if (responseStatus == CPCAPI2::HTTPClient::EHTTPAuth)
         event.errorType = StrettoProvisioningError_Auth;
      else if(responseStatus == CPCAPI2::HTTPClient::EHTTPEmbedded)
         event.errorType = StrettoProvisioningError_Cert;
      else
         event.errorType = StrettoProvisioningError_Connect;

      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, event);
      return;
   }

   // Is there any data in the response?
   if (responseBody.size() == 0)
   {
      StrettoProvisioningErrorEvent event;
      event.errorType = StrettoProvisioningError_Malformed;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse() - No data in response!");
      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, event);
      return;
   }

   // Check for various response statuses, only 2xx is an OK response
   if( responseStatus >= 500 )
   {
      StrettoProvisioningErrorEvent evt;
      evt.errorType = StrettoProvisioningError_HTTP;
      evt.httpResponseCode = responseStatus;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse() - Got 5xx class response (" << responseStatus << ")");
      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, evt);
      return;
   }
   else if( responseStatus >= 400 )
   {
      StrettoProvisioningErrorEvent evt;
      evt.errorType = StrettoProvisioningError_HTTP;
      evt.httpResponseCode = responseStatus;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse() - Got 4xx class response (" << responseStatus << ")");
      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, evt);
      return;
   }
   else if( responseStatus >= 300 )
   {
      // A 301 or 302 response indicates we are being redirected to another server. But according to
      // Line 892 in CurlHttp.cxx, the lib should automatically follow redirections (which is great,
      // otherwise we have to write code to check for redirection cycles). So just throw an error
      // here since we shouldn't get those results in any case.
      StrettoProvisioningErrorEvent evt;
      evt.errorType = StrettoProvisioningError_HTTP;
      evt.httpResponseCode = responseStatus;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse() - Got 3xx class response (" << responseStatus << ")");
      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, evt);
      return;
   }
   else if( responseStatus >= 200 )
   {
      std::regex e ("\"password\".*"); // for simplicity, doesn't attempt to handle case where other JSON elements appear on same line after the password
      std::string passwordMaskedResponseBody = std::regex_replace (responseBody, e, "/* password line masked out by SDK */");
      DebugLog(<< "StrettoProvisioningImpl::processServerResponse() - Got 2xx class response; body: " << passwordMaskedResponseBody);

      // Parse the body in order to scan for an error message.
      // TODO: ***NB!!*** the format here will change to JSON eventually
      if( responseBody.find( "[DATA]\nSuccess=0" ) != std::string::npos ||
          responseBody.find( "[DATA]\r\nSuccess=0" ) != std::string::npos )
      {
         StrettoProvisioningErrorEvent evt;
         evt.errorType = StrettoProvisioningError_Auth;
         m_If->fireEvent( cpcFunc( StrettoProvisioningHandler::onProvisioningError ), m_Handle, evt );
      }
      else
      {
         StrettoProvisioningEvent evt;
         evt.document = responseBody.c_str();
         m_If->fireEvent( cpcFunc( StrettoProvisioningHandler::onProvisioningSuccess ), m_Handle, evt );
      }
   }
   else // if( responseStatus >= 100 )
   {
      StrettoProvisioningErrorEvent evt;
      evt.errorType = StrettoProvisioningError_HTTP;
      evt.httpResponseCode = responseStatus;
      ErrLog(<< "StrettoProvisioningImpl::processServerResponse() - Got 1xx class response (" << responseStatus << ")");
      m_If->fireEvent(cpcFunc(StrettoProvisioningHandler::onProvisioningError), m_Handle, evt);
      return;
   }
}

#endif // #if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)
