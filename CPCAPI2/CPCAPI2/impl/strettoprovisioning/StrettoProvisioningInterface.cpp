#include <brand_branded.h>

#if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1)

#include <mutex>
#include <condition_variable>

#include <assert.h>

#include "StrettoProvisioningInterface.h"
#include "StrettoProvisioningImpl.h"
#include "phone/PhoneInterface.h"

using namespace CPCAPI2::StrettoProvisioning;

#define INVALID_HANDLE_STR  "Invalid Handle"
std::atomic< StrettoProvisioningHandle > StrettoProvisioningInterface::s_CurrentHandle( 1 );

StrettoProvisioningInterface::StrettoProvisioningInterface(CPCAPI2::Phone* phone) : EventSource<StrettoProvisioningHandle, StrettoProvisioningHandler, StrettoProvisioningSyncHandler>(m_IOService), m_Shutdown( false )
{
   m_IOService.start();
}

StrettoProvisioningInterface::~StrettoProvisioningInterface()
{
   m_Shutdown = true;
}

// PhoneModule implementation
void StrettoProvisioningInterface::Release()
{
   // Release is actually called in the resip reactor thread already. So no need to
   // delegate into the IO service, do everything inline.
   m_Shutdown = true;
   std::map< StrettoProvisioningHandle, StrettoProvisioningInfo* >::iterator iter = m_InfoMap.begin();
   for( ; iter != m_InfoMap.end() ; ++iter )
   {
      StrettoProvisioningInfo *info( iter->second );
      if( info != NULL )
      {
         delete info->pImpl; info->pImpl = NULL;
         delete info;
      }
   }
   m_InfoMap.clear();
   m_IOService.stop();

   delete this; // suicide
}

bool StrettoProvisioningInterface::getStrettoProvisioningInfo( const StrettoProvisioningHandle& hStrettoProvisioning, StrettoProvisioningInfo*& pOutInfo )
{
   bool result = false;

   std::map< StrettoProvisioningHandle, StrettoProvisioningInfo* >::iterator iter = m_InfoMap.find( hStrettoProvisioning );
   if( iter == m_InfoMap.end() )
   {
      ErrorEvent evt;
      evt.errNo     = 0;
      evt.errorText = INVALID_HANDLE_STR;
      // TODO
      // info->acctImpl->fireEvent( cpcFunc( StrettoProvisioningHandler::onError ), evt ); // how ?
   }
   else
   {
      pOutInfo = iter->second;
      result = true;
   }

   return result;
}

// StrettoProvisioning Implementation
StrettoProvisioningHandle StrettoProvisioningInterface::create( void )
{
   StrettoProvisioningHandle hStrettoProvisioning = s_CurrentHandle.fetch_add( 1 );
   m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::createImpl, this, hStrettoProvisioning));
   return hStrettoProvisioning;
}

int StrettoProvisioningInterface::createImpl( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   StrettoProvisioningInfo *info = new StrettoProvisioningInfo;
   info->pImpl = new StrettoProvisioningImpl(this, hStrettoProvisioning);
   m_InfoMap[ hStrettoProvisioning ] = info;
   return kSuccess;
}

int StrettoProvisioningInterface::configureSettings( const StrettoProvisioningHandle& hStrettoProvisioning, const StrettoProvisioningSettings& settings )
{
#if defined(CPCAPI2_AUTO_TEST)
   if (const StrettoProvisioningInternalSettings* castedSettings = dynamic_cast<const StrettoProvisioningInternalSettings*>(&settings))
   {
      m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::configureSettingsImpl, this, hStrettoProvisioning, *castedSettings));
   }
   else
#endif
   {
      StrettoProvisioningInternalSettings copiedSettings(settings);
      copiedSettings.provisioningUrl = CPCAPI2_BRAND_PROVISIONING_URL;
      m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::configureSettingsImpl, this, hStrettoProvisioning, copiedSettings));
   }

   return kSuccess;
}

int StrettoProvisioningInterface::configureSettingsImpl( const StrettoProvisioningHandle& hStrettoProvisioning, const StrettoProvisioningInternalSettings& settings )
{
   StrettoProvisioningInfo* info = NULL;
   if( !getStrettoProvisioningInfo( hStrettoProvisioning, info ))
      return kError;

   if( info->pImpl == NULL )
      return kError;

   info->authInfo   = settings.authInfo;
   info->buildInfo  = settings.buildInfo;

   return info->pImpl->configureChannelSettings( info, settings );
}

int StrettoProvisioningInterface::applySettings( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::applySettingsImpl, this, hStrettoProvisioning));
   return kSuccess;
}

int StrettoProvisioningInterface::applySettingsImpl( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   return kSuccess;
}

int StrettoProvisioningInterface::request( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::requestImpl, this, hStrettoProvisioning));
   return kSuccess;
}

int StrettoProvisioningInterface::requestImpl( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   StrettoProvisioningInfo* info = NULL;
   if( !getStrettoProvisioningInfo( hStrettoProvisioning, info ))
      return kError;

   return info->pImpl->request();
}

int StrettoProvisioningInterface::destroy( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   m_IOService.post(resip::resip_bind(&StrettoProvisioningInterface::destroyImpl, this, hStrettoProvisioning));
   return kSuccess;
}

int StrettoProvisioningInterface::destroyImpl( const StrettoProvisioningHandle& hStrettoProvisioning )
{
   StrettoProvisioningInfo* info = NULL;
   if( !getStrettoProvisioningInfo( hStrettoProvisioning, info ))
      return kError;

   delete info->pImpl;
   delete info;

   m_InfoMap.erase( hStrettoProvisioning );
   return kSuccess;
}

int StrettoProvisioningInterface::setHandler( const StrettoProvisioningHandle& hStrettoProvisioning, StrettoProvisioningHandler* handler)
{
   setAppHandler(hStrettoProvisioning, handler);
   return kSuccess;
}


#endif // CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE
