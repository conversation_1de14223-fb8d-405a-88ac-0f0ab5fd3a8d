#pragma once

#include <resip/stack/Tuple.hxx>
#include <resip/stack/TransportFailure.hxx>


namespace resip
{

class StrettoTunnelEventHandler
{
   public:
      enum ReconnectingReason
      {
         ReconnectingReason_Timeout,
         ReconnectingReason_NewNetwork,
         ReconnectingReason_Unknown
      };

   public:
      virtual ~StrettoTunnelEventHandler() {}

      virtual void onTunnelStarted(const Tuple& transportTuple) = 0;
      virtual void onTunnelStopped(const Tuple& transportTuple, TransportFailure::FailureReason failureReason, int signalingCode, const std::string& signalingText) = 0;
      virtual void onTunnelReconnecting(const Tuple& transportTuple, ReconnectingReason reason) = 0;
};

}
