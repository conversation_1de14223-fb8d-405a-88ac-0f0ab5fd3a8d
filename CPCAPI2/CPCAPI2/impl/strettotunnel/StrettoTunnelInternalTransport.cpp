#include "brand_branded.h"

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)

#include <sstream>
#include <string>

#include <boost/algorithm/string/predicate.hpp>

#include "StrettoTunnelInternalTransport.h"
#include "json/JsonHelper.h"
#include "rutil/Logger.hxx"

#include "util/BoostTlsHelper.h"

// Used for loading system cert stores:
#if defined(WinRT)
#include <resip/stack/ssl/counterpath/XWinRTSecurity.hxx>
#elif _WIN32
#include <resip/stack/ssl/counterpath/XWinSecurity.hxx>
#elif ANDROID
#include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
#elif BB10
#include <resip/stack/ssl/counterpath/BlackberrySecurity.hxx>
#elif __APPLE__
#include "resip/stack/ssl/IOSSecurity.hxx"
#elif __THREADX
#include <resip/stack/ssl/counterpath/ThreadXSecurity.hxx>
#elif __linux__
#include <resip/stack/ssl/counterpath/LinuxSecurity.hxx>
#endif


#define RESIPROCATE_SUBSYSTEM Subsystem::TRANSPORT


static void milliSleep(int milliSec)
{
   struct timeval sleepTv;
   sleepTv.tv_sec = 0;
   sleepTv.tv_usec = milliSec * 1000;
   select(0, NULL, NULL, NULL, &sleepTv);
}

static bool isWebsocketCloseErrorFatal(websocketpp::close::status::value local_close_code, websocketpp::close::status::value remote_close_code, websocketpp::lib::error_code ec)
{
   if (websocketpp::close::status::tls_handshake == local_close_code || websocketpp::close::status::message_too_big == local_close_code)
   {
      return true;
   }

   if (websocketpp::close::status::message_too_big == remote_close_code)
   {
      return true;
   }

   return false;
}

static bool getPublicKeyFromCertificate(X509 *cert, cpc::string &publicKey)
{
   int result = false;

   // Refer to https://www.owasp.org/index.php/Certificate_and_Public_Key_Pinning#OpenSSL

   int len1 = 0, len2 = 0;
   unsigned char *buff1 = NULL;

   do
   {
      /* Begin Gyrations to get the subjectPublicKeyInfo       */
      /* Thanks to Viktor Dukhovni on the OpenSSL mailing list */

      /* http://groups.google.com/group/mailing.openssl.users/browse_thread/thread/d61858dae102c6c7 */
      len1 = i2d_X509_PUBKEY(X509_get_X509_PUBKEY(cert), NULL);
      if(!(len1 > 0))
         break; /* failed */

      /* scratch */
      unsigned char* temp = NULL;

      /* http://www.openssl.org/docs/crypto/buffer.html */
      buff1 = temp = (unsigned char*)OPENSSL_malloc(len1);
      if(!(buff1 != NULL))
          break; /* failed */

      /* http://www.openssl.org/docs/crypto/d2i_X509.html */
      len2 = i2d_X509_PUBKEY(X509_get_X509_PUBKEY(cert), &temp);

      /* These checks are verifying we got back the same values as when we sized the buffer.      */
      /* Its pretty weak since they should always be the same. But it gives us something to test. */
      if(!((len1 == len2) && (temp != NULL) && ((temp - buff1) == len1)))
          break; /* failed */

      /* End Gyrations */

      resip::Data pubKey = resip::Data(buff1, len1);
      publicKey = pubKey.base64encode().c_str();

      result = true;
   } while (0);

   /* http://www.openssl.org/docs/crypto/buffer.html */
   if(NULL != buff1)
      OPENSSL_free(buff1);

   return result;
}

namespace resip
{
   StrettoTunnelInternalTransport::StrettoTunnelOstreamBuffer::StrettoTunnelOstreamBuffer(bool isErrorLog)
      : mIsErrorLog(isErrorLog)
   {
   }

   int StrettoTunnelInternalTransport::StrettoTunnelOstreamBuffer::sync()
   {
      std::string trace = this->str();
      this->str("");

      // Erase timestamp
      size_t index = trace.find(']');
      if (index != std::string::npos)
         trace.erase(0, index + 1);
      // Erase trailing newline
      index = trace.find_last_not_of("\r\n");
      if (index != std::string::npos)
         trace.erase(index + 1);

      if (mIsErrorLog)
      {
         ErrLog(<< "[WSPP] " << trace);
      }
      else
      {
         DebugLog(<< "[WSPP] " << trace);
      }

      return 0;
   }

   const int StrettoTunnelInternalTransport::sPollingIntervalMs = 100;

   StrettoTunnelInternalTransport::StrettoTunnelInternalTransport(Fifo<TransactionMessage>& fifo,
         int portNum,
         IpVersion version,
         const Data& interfaceObj,
         const std::string& tunnelURL,
         const std::string& pushToken,
         const std::string& sessionId,
         bool testConnection,
         bool logTransportTraces,
         bool ignoreCertVerification,
         const cpc::vector<cpc::string>& requiredCertPublicKeys,
         bool skipTunnelHandshake)
      : InternalTransport(fifo, portNum, version, interfaceObj, 0 /*afterSockCreationFunc*/, 0 /*beforeSockCloseFunc*/, Compression::Disabled, RESIP_TRANSPORT_FLAG_NOBIND),
        mTunnelURL(tunnelURL),
        mPushToken(pushToken),
        mSessionId(sessionId),
        mTestConnection(testConnection),
        mLogTransportTraces(logTransportTraces),
        mIgnoreCertVerification(ignoreCertVerification),
        mRequiredCertPublicKeys(requiredCertPublicKeys),
        mJustStartedRelay(false),
        mTunnelState(ETunnelStateInit),
        mStopThreadReason(EStopThreadNoReason),
        mStopThreadRequested(false),
        mTunnelSocketFd(INVALID_SOCKET),
        mWebSocketTCPClient(NULL),
        mWebSocketTLSClient(NULL),
        mWebSocketAccessBuffer(false),
        mWebSocketErrorBuffer(true),
        mWebSocketAccessOstream(&mWebSocketAccessBuffer),
        mWebSocketErrorOstream(&mWebSocketErrorBuffer),
        mSkipTunnelHandshake(skipTunnelHandshake)
   {
      mUseTLSClient = boost::starts_with(mTunnelURL, "wss:");
   }

   StrettoTunnelInternalTransport::~StrettoTunnelInternalTransport()
   {
      if (mPollItemHandle)
         mPollGrp->delPollItem(mPollItemHandle);
      if (mInterruptorHandle)
         mPollGrp->delPollItem(mInterruptorHandle);

      if(!mTxFifo.empty())
      {
         WarningLog(<< "TX Fifo non-empty in ~StrettoTunnelInternalTransport! Has " << mTxFifo.size() << " messages.");
      }
   }

   void
   StrettoTunnelInternalTransport::onOpen(websocketpp::connection_hdl hdl)
   {
      std::string tunnelOp;
      {
         resip::Lock lock(mMutex);
         if (mTestConnection)
         {
            tunnelOp = "tunnelTest";
         }
         else if (mFirstIncomingMessage.length()>0)
         {
            tunnelOp = "tunnelResume";
         }
         else
         {
            tunnelOp = "tunnelInvite";
         }
      }

      this->setTunnelState(ETunnelStateHandshake);

      if (mPushToken.empty())
      {
         WarningLog(<< "Push token empty; tunnel handshake may fail");
      }
      if (mSessionId.empty())
      {
         WarningLog(<< "Push session id empty; tunnel handshake may fail");
      }

      std::string payload;
      CPCAPI2::Json::StdStringBuffer buffer(payload);
      CPCAPI2::Json::StdStringWriter writer(buffer);
      writer.StartObject();

      writer.Key("handshake");
      writer.StartObject();
      CPCAPI2::Json::Write(writer, "token", mPushToken);
      CPCAPI2::Json::Write(writer, "id", mSessionId);
      CPCAPI2::Json::Write(writer, "op", tunnelOp);
      writer.EndObject(); // End handshake

      writer.EndObject();


      InfoLog(<<"Sending handshake (length: " << payload.length() << "): " << std::endl << std::endl << payload);

      websocketpp::lib::error_code ec;

      if (mSkipTunnelHandshake)
      {
         this->setTunnelState(ETunnelStateRelay);
      }
      else
      {
         if(mUseTLSClient)
         {
            mWebSocketTLSClient->send(hdl, payload, websocketpp::frame::opcode::text, ec);
         }
         else
         {
            mWebSocketTCPClient->send(hdl, payload, websocketpp::frame::opcode::text, ec);
         }

         if(ec)
         {
            this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure));
         }
      }
      
   }

   void
   StrettoTunnelInternalTransport::onClose(websocketpp::connection_hdl hdl)
   {
      InfoLog(<< "StrettoTunnelInternalTransport::onClose");
      this->setTunnelState(ETunnelStateDisconnected, TunnelStateInfo(TransportFailure::TransportShutdown));
   }

   void
   StrettoTunnelInternalTransport::onFail(websocketpp::connection_hdl hdl)
   {
      websocketpp::close::status::value local_close_code;
      websocketpp::close::status::value remote_close_code;
      websocketpp::lib::error_code ec;

      if(mUseTLSClient)
      {
         ErrLog(<< "WebSocket failed (state: " << mWebSocketTLSConnection->get_state()
                << ", local code: " << mWebSocketTLSConnection->get_local_close_code()
                << ", local reason: " << mWebSocketTLSConnection->get_local_close_reason()
                << ", remote code: " << mWebSocketTLSConnection->get_remote_close_code()
                << ", remote reason: " << mWebSocketTLSConnection->get_remote_close_reason()
                << ") : " << mWebSocketTLSConnection->get_ec() << " - " << mWebSocketTLSConnection->get_ec().message() );
         local_close_code = mWebSocketTLSConnection->get_local_close_code();
         remote_close_code = mWebSocketTLSConnection->get_remote_close_code();
         ec = mWebSocketTLSConnection->get_ec();
      }
      else
      {
         ErrLog(<< "WebSocket failed (state: " << mWebSocketTCPConnection->get_state()
                << ", local code: " << mWebSocketTCPConnection->get_local_close_code()
                << ", local reason: " << mWebSocketTCPConnection->get_local_close_reason()
                << ", remote code: " << mWebSocketTCPConnection->get_remote_close_code()
                << ", remote reason: " << mWebSocketTCPConnection->get_remote_close_reason()
                << ") : " << mWebSocketTCPConnection->get_ec() << " - " << mWebSocketTCPConnection->get_ec().message() );
         local_close_code = mWebSocketTCPConnection->get_local_close_code();
         remote_close_code = mWebSocketTCPConnection->get_remote_close_code();
         ec = mWebSocketTCPConnection->get_ec();
      }

      if (isWebsocketCloseErrorFatal(local_close_code, remote_close_code, ec))
      {
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure, ec));
      }
      else
      {
         // If not fatal, may trigger a re-connect attempt
         this->setTunnelState(ETunnelStateDisconnected, TunnelStateInfo(TransportFailure::Failure, ec));
      }
   }

   void
   StrettoTunnelInternalTransport::onTCPMessage(websocketpp::connection_hdl hdl, websocketpp::config::asio_client::message_type::ptr msg)
   {
      processIncomingMessage(msg);
   }

   void
   StrettoTunnelInternalTransport::onTCPSocketInit(websocketpp::connection_hdl hdl, boost::asio::ip::tcp::socket& stream)
   {
      mTunnelSocketFd = stream.native_handle();
      DebugLog (<< "Websocket TCP socket fd=" << mTunnelSocketFd);
   }

   websocketpp::lib::shared_ptr<boost::asio::ssl::context>
   StrettoTunnelInternalTransport::onTLSInit(websocketpp::connection_hdl hdl)
   {
      // Copied from websocket/WebSocketStateDispatcher.cpp
      //

      // When TLS is initialized, hook into the existing system's certificate store
      websocketpp::lib::shared_ptr<boost::asio::ssl::context> ctx = CPCAPI2::initializeBoostTlsContext(CPCAPI2::TLS_NON_DEPRECATED, CPCAPI2::CipherSuiteLegacy.c_str(), resip::SecurityTypes::TLSMode_TLS_Client);

      if(!mIgnoreCertVerification)
      {
         X509_STORE *rootSSLCerts = 0;
         resip::Data pathToCerts = "";
         unsigned short usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
#if defined(WinRT)
         resip::Security *sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType);
#elif _WIN32
         resip::Security *sec = new resip::XWinSecurity(pathToCerts, usCertStorageType);
#elif __APPLE__
         resip::Security *sec = new resip::IOSSecurity(pathToCerts, usCertStorageType);
#elif __linux__
#if ANDROID
         resip::Security *sec = new resip::AndroidSecurity(pathToCerts);
#else
         resip::Security *sec = new resip::LinuxSecurity(pathToCerts);
#endif
#elif BB10
         resip::Security *sec = new resip::BlackberrySecurity(pathToCerts);
#else
         assert(0);
#endif

         //load all the certificates
         sec->preload();
         SSL_CTX *sslCtx = sec->getSslCtx();
         if(sslCtx)
         {
            rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
            // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
            sslCtx->cert_store = 0;
#else
            X509_STORE_up_ref(rootSSLCerts);
#endif
            if( rootSSLCerts )
               SSL_CTX_set_cert_store(ctx->native_handle(), rootSSLCerts);
         }
         delete sec;
      }

      if(mIgnoreCertVerification)
      {
         ctx->set_verify_mode( boost::asio::ssl::verify_none );
      }
      else
      {
         // NOTE: verify_fail_if_no_peer_cert is ignored by client according to OpenSSL doc
         ctx->set_verify_mode( boost::asio::ssl::verify_peer | boost::asio::ssl::verify_fail_if_no_peer_cert);
      }

      if(mRequiredCertPublicKeys.size()>0)
      {
         ctx->set_verify_callback(std::bind(&StrettoTunnelInternalTransport::verifyPeerCallback,this,std::placeholders::_1,std::placeholders::_2));
      }

      return ctx;
   }

   bool
   StrettoTunnelInternalTransport::verifyPeerCallback(
      bool preverified,    // True if the certificate passed pre-verification.
      boost::asio::ssl::verify_context& ctx) // The peer certificate and other context.
   {
      cpc::string publicKey;
      X509 *cert = NULL;

      STACK_OF(X509) *chain = X509_STORE_CTX_get_chain(ctx.native_handle());
      if (chain)
      {
         int n = sk_X509_num(chain);
         if (n > 0)
         {
            cert = sk_X509_value(chain, 0);
         }
      }

      if (cert)
      {
         char subject_name[256];
         X509_NAME_oneline(X509_get_subject_name(cert), subject_name, sizeof(subject_name));
         InfoLog(<< "Verifying: " << subject_name);

         if (getPublicKeyFromCertificate(cert, publicKey) == false)
         {
            InfoLog(<< "No public key");
            return false;
         }
      }
      else
      {
         InfoLog(<< "No server certificate");
         return false;
      }

      if (mRequiredCertPublicKeys.size()>0)
      {
         bool matchedRequiredPublicKey = false;

         cpc::vector<cpc::string>::const_iterator it = mRequiredCertPublicKeys.begin();
         for (; it!=mRequiredCertPublicKeys.end(); ++it)
         {
            InfoLog(<< "Matching " << publicKey << " against " << *it);
            if (publicKey == *it)
            {
               DebugLog(<< "Public key matched");
               matchedRequiredPublicKey = true;
               break;
            }
         }

         if (matchedRequiredPublicKey == false)
         {
            InfoLog(<< "Public key not found: " << publicKey);
         }
         return matchedRequiredPublicKey;
      }

      bool verified = preverified;

      // TODO: verify cert here if not using pinning

      return verified;
   }

   void
   StrettoTunnelInternalTransport::onTLSSocketInit(websocketpp::connection_hdl hdl, boost::asio::ssl::stream<boost::asio::ip::tcp::socket>& stream)
   {
      mTunnelSocketFd = stream.lowest_layer().native_handle();
      DebugLog (<< "Websocket TLS socket fd=" << mTunnelSocketFd);
   }

   void
   StrettoTunnelInternalTransport::onTLSMessage(websocketpp::connection_hdl hdl, websocketpp::config::asio_tls_client::message_type::ptr msg)
   {
      processIncomingMessage(msg);
   }

   void
   StrettoTunnelInternalTransport::setupTLSSocket()
   {
      mWebSocketTLSClient = new WebSocketTLSClient();
      mWebSocketTLSClient->clear_access_channels(websocketpp::log::alevel::all);
      mWebSocketTLSClient->clear_error_channels(websocketpp::log::elevel::all);
      if (mLogTransportTraces)
      {
         mWebSocketTLSClient->get_alog().set_ostream(&mWebSocketAccessOstream);
         mWebSocketTLSClient->get_elog().set_ostream(&mWebSocketErrorOstream);
      }
      mWebSocketTLSClient->init_asio();

      mWebSocketTLSClient->set_socket_init_handler(std::bind(&StrettoTunnelInternalTransport::onTLSSocketInit,this,std::placeholders::_1,std::placeholders::_2));
      mWebSocketTLSClient->set_tls_init_handler(std::bind(&StrettoTunnelInternalTransport::onTLSInit,this,std::placeholders::_1));
      mWebSocketTLSClient->set_open_handler(std::bind(&StrettoTunnelInternalTransport::onOpen,this,std::placeholders::_1));
      mWebSocketTLSClient->set_close_handler(std::bind(&StrettoTunnelInternalTransport::onClose,this,std::placeholders::_1));
      mWebSocketTLSClient->set_fail_handler(std::bind(&StrettoTunnelInternalTransport::onFail,this,std::placeholders::_1));
      mWebSocketTLSClient->set_message_handler(std::bind(&StrettoTunnelInternalTransport::onTLSMessage,this,std::placeholders::_1,std::placeholders::_2));

      mWebSocketTLSClient->set_access_channels(websocketpp::log::alevel::connect | websocketpp::log::alevel::disconnect | websocketpp::log::alevel::devel);
      mWebSocketTLSClient->set_error_channels(websocketpp::log::elevel::fatal | websocketpp::log::elevel::rerror | websocketpp::log::elevel::warn);
      // mWebSocketTLSClient->set_access_channels(websocketpp::log::alevel::all);
      // mWebSocketTLSClient->set_error_channels(websocketpp::log::elevel::all);

      websocketpp::lib::error_code ec;
      mWebSocketTLSConnection = mWebSocketTLSClient->get_connection(mTunnelURL, ec);
      if (ec)
      {
         ErrLog(<< "WebSocket error: " << ec.message() );
         mWebSocketTLSConnection.reset();
         delete mWebSocketTLSClient;
         mWebSocketTLSClient = NULL;
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure, ec));
      }
      else
      {
         mWebSocketTLSClient->connect(mWebSocketTLSConnection);
      }
   }

   void
   StrettoTunnelInternalTransport::teardownTLSSocket(bool waitForCompletion)
   {
      if (mWebSocketTLSClient)
      {
         websocketpp::connection_hdl hdl = mWebSocketTLSConnection->get_handle();
         websocketpp::lib::error_code ec;
         mWebSocketTLSClient->close(hdl, websocketpp::close::status::going_away, "", ec);
         if (!ec)
         {
            if (waitForCompletion)
            {
               mWebSocketTLSClient->run();
            }
            else
            {
               while (this->pollOne() > 0)
               {
                  ; // keep polling until blocked
               }
            }
         }

         mWebSocketTLSConnection.reset();
         delete mWebSocketTLSClient;
         mWebSocketTLSClient = NULL;
      }
   }

   void
   StrettoTunnelInternalTransport::setupTCPSocket()
   {
      mWebSocketTCPClient = new WebSocketTCPClient();
      mWebSocketTCPClient->clear_access_channels(websocketpp::log::alevel::all);
      mWebSocketTCPClient->clear_error_channels(websocketpp::log::elevel::all);
      if (mLogTransportTraces)
      {
         mWebSocketTCPClient->get_alog().set_ostream(&mWebSocketAccessOstream);
         mWebSocketTCPClient->get_elog().set_ostream(&mWebSocketErrorOstream);
      }
      mWebSocketTCPClient->init_asio();

      mWebSocketTCPClient->set_socket_init_handler(std::bind(&StrettoTunnelInternalTransport::onTCPSocketInit,this,std::placeholders::_1,std::placeholders::_2));
      mWebSocketTCPClient->set_open_handler(std::bind(&StrettoTunnelInternalTransport::onOpen,this,std::placeholders::_1));
      mWebSocketTCPClient->set_close_handler(std::bind(&StrettoTunnelInternalTransport::onClose,this,std::placeholders::_1));
      mWebSocketTCPClient->set_fail_handler(std::bind(&StrettoTunnelInternalTransport::onFail,this,std::placeholders::_1));
      mWebSocketTCPClient->set_message_handler(std::bind(&StrettoTunnelInternalTransport::onTCPMessage,this,std::placeholders::_1,std::placeholders::_2));

      mWebSocketTCPClient->set_access_channels(websocketpp::log::alevel::connect | websocketpp::log::alevel::disconnect | websocketpp::log::alevel::devel);
      mWebSocketTCPClient->set_error_channels(websocketpp::log::elevel::fatal | websocketpp::log::elevel::rerror | websocketpp::log::elevel::warn);
      // mWebSocketTCPClient->set_access_channels(websocketpp::log::alevel::all);
      // mWebSocketTCPClient->set_error_channels(websocketpp::log::elevel::all);

      websocketpp::lib::error_code ec;
      mWebSocketTCPConnection = mWebSocketTCPClient->get_connection(mTunnelURL, ec);
      if (ec)
      {
         ErrLog(<< "WebSocket error: " << ec.message() );
         mWebSocketTCPConnection.reset();
         delete mWebSocketTCPClient;
         mWebSocketTCPClient = NULL;
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure, ec));
      }
      else
      {
         mWebSocketTCPClient->connect(mWebSocketTCPConnection);
      }
   }

   void
   StrettoTunnelInternalTransport::teardownTCPSocket(bool waitForCompletion)
   {
      if (mWebSocketTCPClient)
      {
         websocketpp::connection_hdl hdl = mWebSocketTCPConnection->get_handle();
         websocketpp::lib::error_code ec;
         mWebSocketTCPClient->close(hdl, websocketpp::close::status::going_away, "", ec);
         if (!ec)
         {
            if (waitForCompletion)
            {
               mWebSocketTCPClient->run();
            }
            else
            {
               while (this->pollOne() > 0)
               {
                  ; // keep polling until blocked
               }
            }
         }

         mWebSocketTCPConnection.reset();
         delete mWebSocketTCPClient;
         mWebSocketTCPClient = NULL;
      }
   }

   void
   StrettoTunnelInternalTransport::setTunnelState(TunnelState tunnelState, const TunnelStateInfo &tunnelStateInfo)
   {
      {
         resip::Lock lock(mMutex);

         if (mTunnelState == tunnelState)
         {
            return;
         }

         mTunnelState = tunnelState;
         mTunnelStateInfo = tunnelStateInfo;

         if (mTunnelState == ETunnelStateRelay)
         {
            mJustStartedRelay = true;
         }
      }

      if (tunnelState == ETunnelStateShutdown || tunnelState == ETunnelStateRelay || tunnelState == ETunnelStateDisconnected)
      {
         mReadSignalingSocket.send(); // notify upper layer of failure/relay/disconnect
      }
   }

   void
   StrettoTunnelInternalTransport::sendKeepAlive()
   {
      static const std::string sEmptyString;
      websocketpp::lib::error_code ec;

      if (mUseTLSClient)
      {
         websocketpp::connection_hdl hdl = mWebSocketTLSConnection->get_handle();
         mWebSocketTLSClient->ping(hdl, sEmptyString, ec);
      }
      else
      {
         websocketpp::connection_hdl hdl = mWebSocketTCPConnection->get_handle();
         mWebSocketTCPClient->ping(hdl, sEmptyString, ec);
      }

      if (ec)
      {
         InfoLog(<< "Failed to send ping");
      }
   }

   bool
   StrettoTunnelInternalTransport::sendPayload(SendDataPtr sendData)
   {
      // InfoLog(<< "Sending message (length: " << sendData->data.size() << "): " << std::endl << std::endl << sendData->data);
      InfoLog(<< "Sending message (length: " << sendData->data.size() << ")");

      if (mTestConnection)
      {
         DebugLog (<< "Outgoing message ignored (testConnection==true)");
         return true;
      }

      // The sendData parameter must be either a full SIP message or keep-alive
      if (sendData->data.size() <= 4)
      {
         this->sendKeepAlive();
         return true;
      }

      websocketpp::lib::error_code ec;

      if (mUseTLSClient)
      {
         websocketpp::connection_hdl hdl = mWebSocketTLSConnection->get_handle();
         mWebSocketTLSClient->send(hdl, sendData->data.data(), sendData->data.size(), websocketpp::frame::opcode::binary, ec);
      }
      else
      {
         websocketpp::connection_hdl hdl = mWebSocketTCPConnection->get_handle();
         mWebSocketTCPClient->send(hdl, sendData->data.data(), sendData->data.size(), websocketpp::frame::opcode::binary, ec);
      }

      if (ec)
      {
         return false;
      }

      return true;
   }

   void
   StrettoTunnelInternalTransport::processHandshakeResponse(websocketpp::config::asio_client::message_type::ptr msg)
   {
      rapidjson::Document inDoc;
      const char *cstr = msg->get_payload().c_str();
      inDoc.Parse<rapidjson::kParseDefaultFlags>(cstr);
      if (inDoc.HasParseError())
      {
         InfoLog (<< "Invalid handshake response: " << inDoc.GetParseError());
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure));
         return;
      }

      if (!inDoc.HasMember("handshakeResponse"))
      {
         InfoLog (<< "Missing handshake response");
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure));
         return;
      }
      const rapidjson::Value &handshakeResponseObj = inDoc["handshakeResponse"];

      if (!handshakeResponseObj.HasMember("code"))
      {
         InfoLog (<< "Missing handshake response code");
         this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::Failure));
         return;
      }
      int code = handshakeResponseObj["code"].GetInt();
      if (code < 200 || code >= 300)
      {
         TunnelStateInfo tunnelStateInfo = TunnelStateInfo(TransportFailure::Failure);
         tunnelStateInfo.handshakeResponseCode = code;
         if (handshakeResponseObj.HasMember("reason"))
         {
            tunnelStateInfo.handshakeResponseReason = handshakeResponseObj["reason"].GetString();
            InfoLog (<< "Handshake response failure: code=" << code << ", reason=" << tunnelStateInfo.handshakeResponseReason);
         }
         else
         {
            InfoLog (<< "Handshake response failure: code=" << code);
         }

         this->setTunnelState(ETunnelStateShutdown, tunnelStateInfo);
         return;
      }

      // Now the tunnel is in SIP message relay mode
      this->setTunnelState(ETunnelStateRelay);
      this->processTxQueue();
   }

   void
   StrettoTunnelInternalTransport::relayIncomingMessage(websocketpp::config::asio_client::message_type::ptr msg)
   {
      if (mJustStartedRelay)
      {
         mJustStartedRelay = false;

         if (mFirstIncomingMessage.length() == 0)
         {
            mFirstIncomingMessage = msg->get_payload();
         }
         else if (mFirstIncomingMessage == msg->get_payload())
         {
            InfoLog(<< "Ignoring dup first incoming message");
            return;
         }
      }

      resip::DataPtr dataPtr(new resip::Data(msg->get_payload().c_str(), msg->get_payload().length()));

      {
         resip::Lock lock(mMutex);
         mRxQueue.push(dataPtr);
         mReadSignalingSocket.send(); // "notify" upper layer of new message
      }
   }

   void
   StrettoTunnelInternalTransport::processIncomingMessage(websocketpp::config::asio_client::message_type::ptr msg)
   {
      resip::DataPtr dataPtr(new resip::Data(msg->get_payload().c_str(), msg->get_payload().length()));
      InfoLog(<< "Received message (length: " << msg->get_payload().length() << "): " << std::endl << std::endl << *dataPtr);

      switch(mTunnelState)
      {
      case ETunnelStateHandshake:
         {
            processHandshakeResponse(msg);
            break;
         }

      case ETunnelStateRelay:
         {
            if (mTestConnection)
            {
               DebugLog (<< "Incoming message ignored (testConnection==true)");
            }
            else
            {
               relayIncomingMessage(msg);
            }
            break;
         }

      default:
         InfoLog (<< "Incoming message discarded (state=" << mTunnelState << ")");
         break;
      }
   }

   size_t
   StrettoTunnelInternalTransport::pollOne()
   {
      size_t numberOfHandlersExecuted = 0;

      try
      {
         if (mUseTLSClient)
         {
            numberOfHandlersExecuted = mWebSocketTLSClient->poll_one();
         }
         else
         {
            numberOfHandlersExecuted = mWebSocketTCPClient->poll_one();
         }
      }
      catch (websocketpp::exception const & e)
      {
         ErrLog(<< "Web socket exception: " << e.what());
      }

      return numberOfHandlersExecuted;
   }

   size_t
   StrettoTunnelInternalTransport::runOne()
   {
      size_t numberOfHandlersExecuted = 0;

      try
      {
         if (mUseTLSClient)
         {
            numberOfHandlersExecuted = mWebSocketTLSClient->run_one();
         }
         else
         {
            numberOfHandlersExecuted = mWebSocketTCPClient->run_one();
         }
      }
      catch (websocketpp::exception const & e)
      {
         ErrLog(<< "Web socket exception: " << e.what());
      }

      return numberOfHandlersExecuted;
   }

   void
   StrettoTunnelInternalTransport::resetProcessing()
   {
      resip::Lock lock(mMutex);
      mTunnelState = ETunnelStateInit;
      mStopThreadReason = EStopThreadNoReason;
      mStopThreadRequested = false;
      std::queue<DataPtr> emptyRxQueue;
      std::queue<SendDataPtr> emptyTxQueue;
      std::swap(mRxQueue, emptyRxQueue);
      std::swap(mTxQueue, emptyTxQueue);
   }

   void
   StrettoTunnelInternalTransport::stopProcessing(StopThreadReason stopReason)
   {
      resip::Lock lock(mMutex);
      mStopThreadRequested = true;
      mStopThreadReason = stopReason;
   }

   bool
   StrettoTunnelInternalTransport::isShutdownRequested()
   {
      resip::Lock lock(mMutex);
      return mStopThreadRequested;
   }

   void
   StrettoTunnelInternalTransport::processTxQueue()
   {
      std::queue<SendDataPtr> txQueue;

      {
         resip::Lock lock(mMutex);
         std::swap(mTxQueue, txQueue);
      }

      while (!txQueue.empty())
      {
         SendDataPtr data = txQueue.front();
         txQueue.pop();
         this->sendPayload(data);
      }
   }

   void
   StrettoTunnelInternalTransport::processingThread(int startDelayMs)
   {
      InfoLog(<< "Processing thread for Stretto Tunnel started: sessionId=" << mSessionId << ", startDelayMs=" << startDelayMs);

      while (startDelayMs > 0)
      {
         startDelayMs -= sPollingIntervalMs;
         milliSleep(sPollingIntervalMs);
         if (this->isShutdownRequested())
         {
            this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::None));
            break;
         }
      }

      mWebSocketTCPClient = NULL;

      if (mTunnelState == ETunnelStateInit)
      {
         if (mUseTLSClient)
         {
            setupTLSSocket();
         }
         else
         {
            setupTCPSocket();
         }
      }

      while (mTunnelState == ETunnelStateInit)
      {
         while (this->pollOne() > 0)
         {
           ; // keep polling
         }

         if (mTunnelState == ETunnelStateInit)
         {
            if (this->isShutdownRequested())
            {
               this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::None));
               break;
            }

            milliSleep(sPollingIntervalMs);
         }
      }

      while (mTunnelSocketFd != INVALID_SOCKET && (mTunnelState == ETunnelStateHandshake || mTunnelState == ETunnelStateRelay))
      {
         int num = 0;

         Socket maxFd;
         fd_set readfds;
         FD_ZERO(&readfds);
         FD_SET(mTunnelSocketFd, &readfds);
         maxFd = mTunnelSocketFd;
         if (mTunnelState == ETunnelStateRelay)
         {
            FD_SET(mWriteSignalingSocket.getSocketDescriptor(), &readfds);
            if (maxFd < mWriteSignalingSocket.getSocketDescriptor())
            {
               maxFd = mWriteSignalingSocket.getSocketDescriptor();
            }
         }

         fd_set writefds;
         FD_ZERO(&writefds);
         FD_SET(mTunnelSocketFd, &writefds);

         struct timeval tv;
         tv.tv_sec = 0;
         tv.tv_usec = sPollingIntervalMs * 1000;
         int n = select(maxFd + 1, &readfds, &writefds, NULL, &tv);
         if (n < 0)
         {
            ErrLog(<< "Select failed: errno=" << errno);
         }
         else if (n > 0)
         {
            if (FD_ISSET(mTunnelSocketFd, &readfds) || FD_ISSET(mTunnelSocketFd, &writefds))
            {
               int n;
               while ((n=this->pollOne()) > 0)
               {
                  num += n;
               }
            }
            if (mTunnelState == ETunnelStateRelay && FD_ISSET(mWriteSignalingSocket.getSocketDescriptor(), &readfds))
            {
               this->processTxQueue();
               mWriteSignalingSocket.receive(); // "clear" signaling
            }
         }

         if (this->isShutdownRequested())
         {
            this->setTunnelState(ETunnelStateShutdown, TunnelStateInfo(TransportFailure::None));
            break;
         }

         if (num == 0)
         {
            milliSleep(sPollingIntervalMs);
         }
      }

      bool waitForCompletion = false;
      if (mUseTLSClient)
      {
         this->teardownTLSSocket(waitForCompletion);
      }
      else
      {
         this->teardownTCPSocket(waitForCompletion);
      }
      mTunnelSocketFd = INVALID_SOCKET;

      InfoLog(<< "Processing thread for Stretto Tunnel finished: sessionId=" << mSessionId);
   }

   Socket
   StrettoTunnelInternalTransport::internal_socket(TransportType type, IpVersion ipVer)
   {
      return mReadSignalingSocket.getSocketDescriptor();
   }

   void
   StrettoTunnelInternalTransport::bind()
   {
      // No need to bind for websocket tunnel
      // Client always connect and never listens for incoming connections for incoming connections
   }
}

#endif // CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE
