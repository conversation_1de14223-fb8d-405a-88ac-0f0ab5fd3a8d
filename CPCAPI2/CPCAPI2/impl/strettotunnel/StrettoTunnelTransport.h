#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)

#include <memory>
#include "StrettoTunnelInternalTransport.h"
#include "StrettoTunnelEventHandler.h"

#include <thread>
#include <rutil/Mutex.hxx>
#include "phone/NetworkChangeHandler.h"
#include "resip/stack/MsgHeaderScanner.hxx"

namespace resip
{
   class StrettoTunnelTransport : public StrettoTunnelInternalTransport, public FdPollItemIf
   {
   public:
      static bool hasOnlyIPv6Interfaces();

      static Tuple getNextTuple(
            IpVersion ipVersion,
            TransportType transportType,
            const std::string& preferedSrcIp,
            const std::string& tunnelURL);

      StrettoTunnelTransport(
            Fifo<TransactionMessage>& fifo,
            const Tuple& tuple,
            const std::string& tunnelURL,
            const std::string& pushToken,
            const std::string& sessionId,
            bool testConnection,
            bool logTransportTraces,
            resip::IpVersion ipVersionRequested,
            resip::IpVersion ipVersionInUse,
            resip::TransportType transportTypeRequested,
            resip::TransportType transportTypeInUse,
            bool ignoreCertVerification,
            const cpc::vector<cpc::string>& requiredCertPublicKeys,
            bool skipTunnelHandshake);
      ~StrettoTunnelTransport();

      bool isStub() const;

      void setHandler(StrettoTunnelEventHandler* handler);
      void reconnectAfterNetworkChange(CPCAPI2::NetworkTransport activeNetworkTransport);

      void process(FdSet& fdset) OVERRIDE;
      void process() OVERRIDE;
      bool hasDataToSend() const OVERRIDE;
      void buildFdSet( FdSet& fdset) OVERRIDE;
      void setPollGrp(FdPollGrp *grp) OVERRIDE;
      bool isReliable() const OVERRIDE;
      bool isDatagram() const OVERRIDE;

      // FdPollItemIf
      // Socket getPollSocket() const OVERRIDE;
      void processPollEvent(FdPollEventMask mask) OVERRIDE;

   private:
      void processRxAll();
      void processTxAll();
      resip::SipMessage* makeSipMessage(const char *srcBuffer, size_t len, Tuple &sender);

      // statistics
      unsigned mPollEventCnt;
      unsigned mTxTryCnt;
      unsigned mTxMsgCnt;
      unsigned mTxFailCnt;
      unsigned mRxTryCnt;
      unsigned mRxMsgCnt;
      unsigned mRxKeepaliveCnt;
      unsigned mRxTransactionCnt;
      int mReconnectCounter;
      bool mReportedTunnelStarted;
      bool mReportedTunnelStopped;
      bool mIsStub;

      resip::TransportType mTransportTypeInUse;
      std::shared_ptr<std::thread> mThread;
      StrettoTunnelEventHandler* mHandler;
   };
}

#endif // CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE
