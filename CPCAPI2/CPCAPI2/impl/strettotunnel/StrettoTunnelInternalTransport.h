#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)

#include <iostream>

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>

#include "websocket/WebSocketTypes.h"

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <rutil/Data.hxx>
#include <rutil/SharedPtr.hxx>
#include <resip/stack/InternalTransport.hxx>
#include <reflow/FakeSelectSocketDescriptor.hxx>


namespace resip
{
   typedef resip::SharedPtr<resip::Data> DataPtr;
   typedef resip::SharedPtr<resip::SendData> SendDataPtr;

   class StrettoTunnelInternalTransport : public InternalTransport
   {
   public:
      typedef enum {
         ETunnelStateInit,
         ETunnelStateHandshake,
         ETunnelStateRelay,
         ETunnelStateDisconnected,
         ETunnelStateShutdown
      } TunnelState;

      typedef enum {
        EStopThreadNoReason,
        EStopThreadReconnect,
        EStopThreadShutdown
      } StopThreadReason;

      struct TunnelStateInfo {
        TransportFailure::FailureReason transportFailure;
        websocketpp::lib::error_code    webSocketErrorCode;
        int                             handshakeResponseCode;
        std::string                     handshakeResponseReason;

        TunnelStateInfo() {
          transportFailure = TransportFailure::None;
          webSocketErrorCode = websocketpp::transport::error::general;
          handshakeResponseCode = 0;
        }

        TunnelStateInfo(TransportFailure::FailureReason transportFailure) {
          this->transportFailure = transportFailure;
          webSocketErrorCode = websocketpp::transport::error::general;
          handshakeResponseCode = 0;
        }

        TunnelStateInfo(TransportFailure::FailureReason transportFailure, websocketpp::lib::error_code webSocketErrorCode) {
          this->transportFailure = transportFailure;
          this->webSocketErrorCode = webSocketErrorCode;
          handshakeResponseCode = 0;
        }

        TunnelStateInfo(TransportFailure::FailureReason transportFailure, int handshakeResponseCode, const std::string &handshakeResponseReason) {
          this->transportFailure = transportFailure;
          webSocketErrorCode = websocketpp::transport::error::general;
          this->handshakeResponseCode = handshakeResponseCode;
          this->handshakeResponseReason = handshakeResponseReason;
        }
      };


      struct debuggable_asio_client : public websocketpp::config::asio_client {
        static const websocketpp::log::level alog_level = websocketpp::log::alevel::all;
        static const websocketpp::log::level elog_level = websocketpp::log::elevel::all;
      };

      struct debuggable_asio_tls_client : public websocketpp::config::asio_tls_client {
        static const websocketpp::log::level alog_level = websocketpp::log::alevel::all;
        static const websocketpp::log::level elog_level = websocketpp::log::elevel::all;
      };

      typedef websocketpp::client<debuggable_asio_client> WebSocketTCPClient;
      typedef websocketpp::client<debuggable_asio_tls_client> WebSocketTLSClient;

      class StrettoTunnelOstreamBuffer : public std::stringbuf
      {
      protected:
         bool mIsErrorLog;

      public:
         StrettoTunnelOstreamBuffer(bool isErrorLog=false);
         virtual int sync();
      };

   public:
      StrettoTunnelInternalTransport(
            Fifo<TransactionMessage>& fifo,
            int portNum,
            IpVersion version,
            const Data& interfaceObj,
            const std::string& tunnelURL,
            const std::string& pushToken,
            const std::string& sessionId,
            bool testConnection,
            bool logTransportTraces,
            bool ignoreCertVerification,
            const cpc::vector<cpc::string>& requiredCertPublicKeys,
            bool skipTunnelHandshake);
      ~StrettoTunnelInternalTransport();

      Socket internal_socket(TransportType type, IpVersion ipVer);
      void bind();

   protected:
      void resetProcessing();
      void stopProcessing(StopThreadReason stopReason);
      bool isShutdownRequested();
      void processingThread(int startDelayMs);

   private:
      void onOpen(websocketpp::connection_hdl hdl);
      void onClose(websocketpp::connection_hdl hdl);
      void onFail(websocketpp::connection_hdl hdl);
      void onTCPMessage(websocketpp::connection_hdl hdl, websocketpp::config::asio_client::message_type::ptr msg);
      void onTCPSocketInit(websocketpp::connection_hdl hdl, boost::asio::ip::tcp::socket& stream);
      websocketpp::lib::shared_ptr<boost::asio::ssl::context> onTLSInit( websocketpp::connection_hdl hdl);
      void onTLSSocketInit(websocketpp::connection_hdl hdl, boost::asio::ssl::stream<boost::asio::ip::tcp::socket>& stream);
      void onTLSMessage(websocketpp::connection_hdl hdl, websocketpp::config::asio_tls_client::message_type::ptr msg);
      void setupTCPSocket();
      void teardownTCPSocket(bool waitForCompletion=true);
      void setupTLSSocket();
      void teardownTLSSocket(bool waitForCompletion=true);
      size_t pollOne();
      size_t runOne();
      void setTunnelState(TunnelState tunnelState, const TunnelStateInfo &tunnelStateInfo = TunnelStateInfo());
      bool sendPayload(SendDataPtr data);
      void sendKeepAlive();
      void processHandshakeResponse(websocketpp::config::asio_client::message_type::ptr msg);
      void relayIncomingMessage(websocketpp::config::asio_client::message_type::ptr msg);
      void processIncomingMessage(websocketpp::config::asio_client::message_type::ptr msg);
      void processTxQueue();
      bool verifyPeerCallback(bool preverified, boost::asio::ssl::verify_context& ctx);

   protected:
      static const int sPollingIntervalMs;

      std::string mTunnelURL;
      std::string mPushToken;
      std::string mSessionId;
      bool mTestConnection;
      bool mLogTransportTraces;
      bool mIgnoreCertVerification;
      cpc::vector<cpc::string> mRequiredCertPublicKeys;

      resip::Mutex mMutex;
      bool mStopThreadRequested;
      bool mJustStartedRelay;
      std::string mFirstIncomingMessage;
      TunnelState mTunnelState;
      TunnelStateInfo mTunnelStateInfo;
      StopThreadReason mStopThreadReason;
      Socket mTunnelSocketFd;
      std::queue<DataPtr> mRxQueue;
      std::queue<SendDataPtr> mTxQueue;

      // Only one of the client will be in use at a time
      bool mUseTLSClient;
      WebSocketTCPClient *mWebSocketTCPClient;
      WebSocketTCPClient::connection_ptr mWebSocketTCPConnection;
      WebSocketTLSClient *mWebSocketTLSClient;
      WebSocketTLSClient::connection_ptr mWebSocketTLSConnection;

      flowmanager::FakeSelectSocketDescriptor mReadSignalingSocket;
      flowmanager::FakeSelectSocketDescriptor mWriteSignalingSocket;

      StrettoTunnelOstreamBuffer mWebSocketAccessBuffer;
      StrettoTunnelOstreamBuffer mWebSocketErrorBuffer;
      std::ostream mWebSocketAccessOstream;
      std::ostream mWebSocketErrorOstream;
      
      bool mSkipTunnelHandshake;
   };
}

#endif // CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE
