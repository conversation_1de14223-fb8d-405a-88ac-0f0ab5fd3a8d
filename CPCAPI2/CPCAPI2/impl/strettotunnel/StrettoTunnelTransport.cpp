#include "brand_branded.h"

#if (CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE == 1)

#include <iostream>
#include <memory>
#include <string>
#include <vector>

#include <boost/algorithm/string/predicate.hpp>

#include "StrettoTunnelTransport.h"
#include "resip/stack/Helper.hxx"
#include "resip/stack/SendData.hxx"
#include "resip/stack/SipMessage.hxx"
#include "rutil/Data.hxx"
#include "rutil/DnsUtil.hxx"
#include "rutil/Logger.hxx"
#include "rutil/Socket.hxx"
#include "rutil/WinLeakCheck.hxx"
#include "rutil/compat.hxx"

#include "../util/IpHelpers.h"

#define MAX_RECONNECT_ATTEMPTS 5
#define RESIPROCATE_SUBSYSTEM Subsystem::TRANSPORT

using namespace resip;

static resip::Mutex sMutex;
static int sIPv4Port = 0;
static int sIPv6Port = 0;
static int sPreferedSrcIpPort = 0;

static resip::Data getLocalIpAddress(IpVersion ipVersion)
{
   resip::Data srcIp = resip::Data::Empty;

   if (ipVersion == resip::V4)
   {
      std::list<std::pair<resip::Data, resip::Data> > v4ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, true, false, false, false);
      if (v4ifs.size() > 0)
      {
         resip::Tuple googDns("*******", 53, resip::UDP);
         CPCAPI2::IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
         DebugLog(<< "getLocalIpAddress (using *******) - " << srcIp);
      }
   }
   else if (ipVersion == resip::V6)
   {
      std::list<std::pair<resip::Data, resip::Data> > v6ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, false, true, false, false);
      if (v6ifs.size() > 0)
      {
         resip::Tuple googDns("2001:4860:4860::8888", 53, resip::UDP);
         CPCAPI2::IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
         DebugLog(<< "getLocalIpAddress (using 2001:4860:4860::8888) - " << srcIp);
      }
   }

   return srcIp;
}


static int getNextIPv4Port()
{
   resip::Lock lock(sMutex);
   if ((++sIPv4Port % 4000) == 0)
      sIPv4Port = 1;
   return sIPv4Port;
}

static int getNextIPv6Port()
{
   resip::Lock lock(sMutex);
   if ((++sIPv6Port % 4000) == 0)
      sIPv6Port = 1;
   return sIPv6Port;
}

static int getNextPreferedSrcIpPort()
{
   resip::Lock lock(sMutex);
   if ((++sPreferedSrcIpPort % 4000) == 0)
      sPreferedSrcIpPort = 1;
   return sPreferedSrcIpPort;
}

bool
StrettoTunnelTransport::hasOnlyIPv6Interfaces()
{
   std::list<std::pair<resip::Data, resip::Data> > v4ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, true, false, false, false);
   std::list<std::pair<resip::Data, resip::Data> > v6ifs = resip::DnsUtil::getInterfacesEx(resip::Data::Empty, false, true, false, false);
   return v4ifs.size() == 0 && v6ifs.size() > 0;
}

Tuple
StrettoTunnelTransport::getNextTuple(
      IpVersion ipVersion,
      TransportType transportType,
      const std::string& preferedSrcIp,
      const std::string& tunnelURL)
{
   bool useTLS = boost::starts_with(tunnelURL, "wss:");
   // TransportType tunnelTransportType = transportType;
   TransportType tunnelTransportType = useTLS? resip::WSS : resip::WS;

   if (preferedSrcIp.length() > 0)
   {
      return Tuple(
            resip::Data(preferedSrcIp.c_str()),
            getNextPreferedSrcIpPort(),
            ipVersion,
            tunnelTransportType);
   }
   else if (ipVersion==resip::V4)
   {
      return Tuple(
            getLocalIpAddress(ipVersion),
            getNextIPv4Port(),
            ipVersion,
            tunnelTransportType);
   }
   else
   {
      return Tuple(
            getLocalIpAddress(ipVersion),
            getNextIPv6Port(),
            ipVersion,
            tunnelTransportType);
   }
}

StrettoTunnelTransport::StrettoTunnelTransport(
      Fifo<TransactionMessage>& fifo,
      const Tuple& tuple,
      const std::string& tunnelURL,
      const std::string& pushToken,
      const std::string& sessionId,
      bool testConnection,
      bool logTransportTraces,
      IpVersion ipVersionRequested,
      IpVersion ipVersionInUse,
      TransportType transportTypeRequested,
      TransportType transportTypeInUse,
      bool ignoreCertVerification,
      const cpc::vector<cpc::string>& requiredCertPublicKeys,
      bool skipTunnelHandshake)
: StrettoTunnelInternalTransport(fifo, tuple.getPort(), tuple.ipVersion(), Tuple::inet_ntop(tuple), tunnelURL, pushToken, sessionId,
                               testConnection, logTransportTraces, ignoreCertVerification, requiredCertPublicKeys, skipTunnelHandshake),
  mReconnectCounter(0),
  mReportedTunnelStarted(false),
  mReportedTunnelStopped(false),
  mTransportTypeInUse(transportTypeInUse),
  mHandler(0)
{
   mPollEventCnt = 0;
   mTxTryCnt = mTxMsgCnt = mTxFailCnt = 0;
   mRxTryCnt = mRxMsgCnt = mRxKeepaliveCnt = mRxTransactionCnt = 0;
   mTuple = tuple;

   InfoLog (<< "Creating Stretto tunnel transport host=" << mInterface
            << " tunnelURL=" << tunnelURL
            << " pushToken=" << pushToken
            << " sessionId=" << sessionId );

   if (ipVersionRequested != ipVersionInUse)
   {
      InfoLog (<< "Requested IP version " << ipVersionRequested << " is different from version in use " << ipVersionInUse << ". Marking as stub");
      mFd = INVALID_SOCKET;
      mIsStub = true;
      return;
   }

   if (transportTypeRequested != transportTypeInUse)
   {
      InfoLog (<< "Requested transport type " << transportTypeRequested << " is different from transport in use " << transportTypeInUse << ". Marking as stub");
      mFd = INVALID_SOCKET;
      mIsStub = true;
      return;
   }

   mFd = mReadSignalingSocket.getSocketDescriptor();
   mTuple.mFlowKey=(FlowKey)mFd;
   mIsStub = false;

   if (mAfterSocketCreationFunc)
   {
      mAfterSocketCreationFunc(mFd, transport(), __FILE__, __LINE__);
   }

   InfoLog (<< "StrettoTunnelTransport FlowKey=" << mTuple.mFlowKey);

   mTxFifo.setDescription("StrettoTunnelTransport::mTxFifo");

   mThread = std::make_shared<std::thread>(std::bind(&StrettoTunnelTransport::processingThread, this, 0));
}

StrettoTunnelTransport::~StrettoTunnelTransport()
{
   InfoLog(<< "Shutting down " << mTuple
           <<" tf="<<mTransportFlags<<" evt="<<(mPollGrp?1:0)
           <<" stats:"
           <<" poll="<<mPollEventCnt
           <<" txtry="<<mTxTryCnt
           <<" txmsg="<<mTxMsgCnt
           <<" txfail="<<mTxFailCnt
           <<" rxtry="<<mRxTryCnt
           <<" rxmsg="<<mRxMsgCnt
           <<" rxka="<<mRxKeepaliveCnt
           <<" rxtr="<<mRxTransactionCnt
           );

   if (mFd != INVALID_SOCKET)
   {
      callBeforeCloseSocketFunc(mFd);
   }

   if (mThread)
   {
      this->stopProcessing(EStopThreadShutdown);
      mThread->join();
      mThread.reset();
      mFd = INVALID_SOCKET;
   }

   setPollGrp(0);
}

bool
StrettoTunnelTransport::isStub() const
{
   return mIsStub;
}

void
StrettoTunnelTransport::setHandler(StrettoTunnelEventHandler* handler)
{
   resip::Lock lock(mMutex);

   mHandler = handler;
}

void
StrettoTunnelTransport::reconnectAfterNetworkChange(CPCAPI2::NetworkTransport activeNetworkTransport)
{
   InfoLog(<< "reconnectAfterNetworkChange: " << activeNetworkTransport);

   StrettoTunnelEventHandler* handler = 0;
   TunnelState tunnelState;
   {
      resip::Lock lock(mMutex);
      tunnelState = mTunnelState;
      handler = mHandler;
   }
   if (tunnelState == ETunnelStateShutdown)
   {
      InfoLog(<< "reconnectAfterNetworkChange: tunnel is already shutdown");
      return;
   }

   if (mThread)
   {
      this->stopProcessing(EStopThreadReconnect);
      mThread->join();
      mThread.reset();
   }

   {
      resip::Lock lock(mMutex);
      mTunnelState = ETunnelStateDisconnected;
   }
   mReportedTunnelStarted = false;
   mReportedTunnelStopped = false;
   this->resetProcessing();

   if (handler != 0)
   {
      handler->onTunnelReconnecting(this->getTuple(), StrettoTunnelEventHandler::ReconnectingReason_NewNetwork);
   }

   if (activeNetworkTransport != CPCAPI2::TransportNone)
   {
      ++mReconnectCounter;
      mThread = std::make_shared<std::thread>(std::bind(&StrettoTunnelTransport::processingThread, this, 200));
   }
}

bool
StrettoTunnelTransport::isReliable() const
{
   return true;

#if 0
   switch(mTransportTypeInUse)
   {
      case TLS:
         return true;
      case TCP:
         return true;
      case UDP:
         return false;
      case SCTP:
         return true;
      case DCCP:
         return true;
      case DTLS:
         return false;
      case WS:
         return true;
      case WSS:
         return true;
      default:
         return false;
   }
#endif
}

bool
StrettoTunnelTransport::isDatagram() const
{
   return true;
}

void
StrettoTunnelTransport::setPollGrp(FdPollGrp *grp)
{
   if(mPollGrp)
   {
      mPollGrp->delPollItem(mPollItemHandle);
      mPollItemHandle=0;
   }

   if(grp)
   {
      mPollItemHandle = grp->addPollItem(mReadSignalingSocket.getSocketDescriptor(), FPEM_Read, this);
      // above released by InternalTransport destructor
      // ?bwc? Is this really a good idea? If the InternalTransport d'tor is
      // freeing this, shouldn't InternalTransport::setPollGrp() handle
      // creating it?
   }

   InternalTransport::setPollGrp(grp);
}

void
StrettoTunnelTransport::process()
{
   mStateMachineFifo.flush();
   if ( (mTransportFlags & RESIP_TRANSPORT_FLAG_TXNOW)!= 0 )
   {
      processTxAll();
      // FALLTHRU to code below in case queue not-empty
      // shouldn't ever happen (with current code)
      // but in future we may throttle transmits
   }
   if ( mPollGrp )
   {
      processTxAll();
   }
}

void
StrettoTunnelTransport::processPollEvent(FdPollEventMask mask)
{
   ++mPollEventCnt;

   if (this->isShutdownRequested())
   {
      InfoLog(<< "In shutdown state:");
      return;
   }

   if ( mask & FPEM_Error )
   {
      assert(0);
   }
   if ( mask & FPEM_Write )
   {
      processTxAll();
   }
   if ( mask & FPEM_Read )
   {
      processRxAll();
   }
}

bool
StrettoTunnelTransport::hasDataToSend() const
{
   return false;
}

void
StrettoTunnelTransport::buildFdSet( FdSet& fdset )
{
   fdset.setRead(mReadSignalingSocket.getSocketDescriptor());

   if (mTxFifoOutBuffer.messageAvailable())
   {
      fdset.setWrite(mWriteSignalingSocket.getSocketDescriptor());
   }
}

void
StrettoTunnelTransport::process(FdSet& fdset)
{
   // pull buffers to send out of TxFifo
   // receive datagrams from fd
   // preparse and stuff into RxFifo

   if (mTxFifoOutBuffer.messageAvailable())
   {
      processTxAll();
   }

   fd_set readfds;
   FD_ZERO(&readfds);
   FD_SET(mReadSignalingSocket.getSocketDescriptor(), &readfds);
   timeval tv = { 0, 0 };

   if (select(mReadSignalingSocket.getSocketDescriptor() + 1, &readfds, NULL, NULL, &tv))
   {
      processRxAll();
   }

   mStateMachineFifo.flush();
}

void
StrettoTunnelTransport::processTxAll()
{
   ++mTxTryCnt;

   SendDataPtr failedSendData;
   TransportFailure::FailureReason failCode = TransportFailure::None;
   int subCode = 0;

   size_t n = 0;

   {
      resip::Lock lock(mMutex);

      SendData *msg;
      while ( (msg=mTxFifoOutBuffer.getNext(RESIP_FIFO_NOWAIT)) != NULL )
      {
         SendDataPtr data(msg);

         if (mTunnelStateInfo.transportFailure != TransportFailure::None)
         {
            failedSendData = data;
            failCode = mTunnelStateInfo.transportFailure;
            break;
         }

         mTxQueue.push(data);
         ++n;
      }
   }

   if (failedSendData)
   {
      this->fail(failedSendData->transactionId, failCode);
   }

   if (n > 0)
   {
      mWriteSignalingSocket.send(); // "notify" lower layer
   }
}

void
StrettoTunnelTransport::processRxAll()
{
   std::queue<DataPtr> rxQueue;
   TunnelState tunnelState;
   TunnelStateInfo tunnelStateInfo;
   StopThreadReason stopReason;
   StrettoTunnelEventHandler* handler = 0;

   {
      resip::Lock lock(mMutex);
      std::swap(mRxQueue, rxQueue);
      handler = mHandler;
      tunnelState = mTunnelState;
      tunnelStateInfo = mTunnelStateInfo;
      stopReason = mStopThreadReason;
   }

   while (!rxQueue.empty())
   {
      DataPtr data = rxQueue.front();
      rxQueue.pop();

      SipMessage *msg = this->makeSipMessage(data->data(), data->size(), mTuple);
      if (msg)
      {
         mStateMachineFifo.add(msg);
      }
      else
      {
         InfoLog(<< "Discarding invalid message:" << std::endl << data);
      }
   }

   mReadSignalingSocket.receive(); // "clear" signaling

   if (tunnelState == ETunnelStateRelay)
   {
      mReconnectCounter = 0;

      if (mReportedTunnelStarted==false)
      {
         mReportedTunnelStarted = true;
         InfoLog(<< "Stretto tunnel started (relay mode)");
         if (handler != 0)
         {
            handler->onTunnelStarted(this->getTuple());
         }
      }
   }
   else if (tunnelState == ETunnelStateShutdown)
   {
      if (stopReason != EStopThreadReconnect && mReportedTunnelStopped==false)
      {
         mReportedTunnelStopped = true;
         InfoLog(<< "Stretto tunnel stopped: stopReason=" << stopReason << ", failureReason=" << tunnelStateInfo.transportFailure);

         this->flowTerminated(mTuple);

         if (handler != 0)
         {
            handler->onTunnelStopped(this->getTuple(), tunnelStateInfo.transportFailure, tunnelStateInfo.handshakeResponseCode, tunnelStateInfo.handshakeResponseReason);
         }
      }
   }
   else if (tunnelState == ETunnelStateDisconnected)
   {
      if (mReconnectCounter < MAX_RECONNECT_ATTEMPTS)
      {
         if (mThread)
         {
            this->stopProcessing(EStopThreadReconnect);
            mThread->join();
            mThread.reset();
         }

         mReportedTunnelStarted = false;
         mReportedTunnelStopped = false;
         this->resetProcessing();

         if (handler != 0)
         {
            StrettoTunnelEventHandler::ReconnectingReason reconnectingReason;
            if (tunnelStateInfo.webSocketErrorCode == websocketpp::transport::error::timeout)
               reconnectingReason = StrettoTunnelEventHandler::ReconnectingReason_Timeout;
            else
               reconnectingReason = StrettoTunnelEventHandler::ReconnectingReason_Unknown;
            handler->onTunnelReconnecting(this->getTuple(), reconnectingReason);
         }

         ++mReconnectCounter;
         InfoLog(<< "Attempting to reconnect: " << mReconnectCounter);
         mThread = std::make_shared<std::thread>(std::bind(&StrettoTunnelTransport::processingThread, this, 500));
      }
   }
}

resip::SipMessage*
StrettoTunnelTransport::makeSipMessage(const char *srcBuffer, size_t len, Tuple &sender)
{
   char *buffer = new char[len + 5];
   memcpy(buffer, srcBuffer, len);

   resip::SipMessage *msg = new resip::SipMessage(this);
   sender.transport = this;
   sender.transportKey = getKey();
   sender.mFlowKey=mTuple.mFlowKey;
   msg->setSource(sender);
   msg->addBuffer(buffer);

   resip::MsgHeaderScanner msgHeaderScanner;
   msgHeaderScanner.prepareForMessage(msg);
   char *unprocessedCharPtr;
   if (msgHeaderScanner.scanChunk(buffer, (unsigned int)len, &unprocessedCharPtr) != MsgHeaderScanner::scrEnd)
   {
      InfoLog(<<"Scanner rejecting buffer as unparsable / fragmented.");
      delete msg;
      return 0;
   }

   // no pp error
   unsigned int used = (unsigned int)(unprocessedCharPtr - buffer);

   if (used < len)
   {
      // body is present .. add it up.
      // NB. The Sip Message uses an overlay (again)
      // for the body. It ALSO expects that the body
      // will be contiguous (of course).
      // it doesn't need a new buffer in UDP b/c there
      // will only be one datagram per buffer. (1:1 strict)

      msg->setBody(buffer+used,UInt32(len-used));
      //DebugLog(<<"added " << len-used << " byte body");
   }

   return msg;
}

#endif // CPCAPI2_BRAND_STRETTO_TUNNEL_MODULE
