#include "brand_branded.h"

#include "interface/experimental/signature/SignatureManager.h"

#include "impl/phone/PhoneInterface.h"
#include "impl/signature/SignatureManagerInterface.h"

namespace CPCAPI2
{
namespace Signature
{
   SignatureManager* SignatureManager::getInterface(Phone* cpcPhone)
   {
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<SignatureManagerInterface>(phone, "SignatureCheckManager");
   }

}
}
