#ifdef WIN32
#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)

#include "ProcSigCheck.h"

#undef WINVER
#define WINVER _WIN32_WINNT_WIN8
#undef _WIN32_WINNT
#define _WIN32_WINNT _WIN32_WINNT_WIN8


#include <tchar.h>
#include <stdio.h>
#include <stdlib.h>
#include <windows.h>
#include <Softpub.h>
#include <wincrypt.h>
#include <wintrust.h>
#include <assert.h>
#include <mscat.h>
#include <sstream>
#include <filesystem>

#include <rutil/Data.hxx>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE


#pragma comment (lib, "wintrust")

using namespace CPCAPI2::Signature;

static bool sigCheckLoggingEnabled()
{
   // as of writing early SDK logging with the electron app doesn't seem to work;
   // licensing happens before electron app enables logging.
   // so for now we'll use on stderr logging

   char* e = getenv("CPCAPI2_SIGCHECK_LOGGING");

   if (e && strlen(e) > 0)
   {
      return true;
   }
   return false;
}

#define DbgLog(args_)                                                    \
   do                                                                   \
   {                                                                    \
      {                                                                 \
         std::stringstream ss;                                          \
         ss args_;                                                      \
         OutputDebugStringA(ss.str().c_str());                          \
      }                                                                 \
   } while (false)


bool certMatches(HANDLE stateInfo, const std::string& certFingerprint)
{
   CRYPT_PROVIDER_DATA* providerData = WTHelperProvDataFromStateData(stateInfo);
   CRYPT_PROVIDER_SGNR* providerSgnr = WTHelperGetProvSignerFromChain(providerData, 0, false, 0);
   CRYPT_PROVIDER_CERT* providerCert = WTHelperGetProvCertFromChain(providerSgnr, 0);

   BYTE thumbPrint[512];
   DWORD thumbPrintSize = sizeof(thumbPrint);

   if (!CryptHashCertificate2(L"SHA1", 0, NULL, providerCert->pCert->pbCertEncoded, providerCert->pCert->cbCertEncoded, thumbPrint, &thumbPrintSize))
   {
      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: CryptHashCertificate2 returned false " << __LINE__);
      return false;
   }

   const resip::Data certToMatch(certFingerprint.c_str());

   resip::Data procCert(thumbPrint, thumbPrintSize);
   procCert = procCert.hex();

   if (certToMatch == procCert)
   {
      return true;
   }

   if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: Fingerprint did not match; fingerprint from file is " << procCert << " " << __LINE__ << std::endl);

   return false;
}

// lifted from https://docs.microsoft.com/en-us/windows/win32/seccrypto/example-c-program--verifying-the-signature-of-a-pe-file
bool ProcSigCheck::checkCurrentAppSignatureValidAndMatches(const std::string& certFingerprintToMatch)
{
   wchar_t pwszSourceFile[1024];
   if (GetModuleFileNameW(NULL, pwszSourceFile, sizeof(pwszSourceFile)) == 0)
   {
      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: GetModuleFileNameW failed " << __LINE__ << std::endl);
      return false;
   }


   LONG lStatus;
   DWORD dwLastError;

   // Initialize the WINTRUST_FILE_INFO structure.

   WINTRUST_FILE_INFO FileData;
   memset(&FileData, 0, sizeof(FileData));
   FileData.cbStruct = sizeof(WINTRUST_FILE_INFO);
   FileData.pcwszFilePath = pwszSourceFile;
   FileData.hFile = NULL;
   FileData.pgKnownSubject = NULL;

   /*
   WVTPolicyGUID specifies the policy to apply on the file
   WINTRUST_ACTION_GENERIC_VERIFY_V2 policy checks:

   1) The certificate used to sign the file chains up to a root
   certificate located in the trusted root certificate store. This
   implies that the identity of the publisher has been verified by
   a certification authority.

   2) In cases where user interface is displayed (which this example
   does not do), WinVerifyTrust will check for whether the
   end entity certificate is stored in the trusted publisher store,
   implying that the user trusts content from this publisher.

   3) The end entity certificate has sufficient permission to sign
   code, as indicated by the presence of a code signing EKU or no
   EKU.
   */

   GUID WVTPolicyGUID = WINTRUST_ACTION_GENERIC_VERIFY_V2;
   WINTRUST_DATA WinTrustData;

   // Initialize the WinVerifyTrust input data structure.

   // Default all fields to 0.
   memset(&WinTrustData, 0, sizeof(WinTrustData));

   WinTrustData.cbStruct = sizeof(WinTrustData);

   // Use default code signing EKU.
   WinTrustData.pPolicyCallbackData = NULL;

   // No data to pass to SIP.
   WinTrustData.pSIPClientData = NULL;

   // Disable WVT UI.
   WinTrustData.dwUIChoice = WTD_UI_NONE;

   // No revocation checking.
   WinTrustData.fdwRevocationChecks = WTD_REVOKE_NONE;

   // Verify an embedded signature on a file.
   WinTrustData.dwUnionChoice = WTD_CHOICE_FILE;

   // Verify action.
   WinTrustData.dwStateAction = WTD_STATEACTION_VERIFY;

   // Verification sets this value.
   WinTrustData.hWVTStateData = NULL;

   // Not used.
   WinTrustData.pwszURLReference = NULL;

   // This is not applicable if there is no UI because it changes
   // the UI to accommodate running applications instead of
   // installing applications.
   WinTrustData.dwUIContext = 0;

   // Set pFile.
   WinTrustData.pFile = &FileData;

   WINTRUST_SIGNATURE_SETTINGS WinTrustSignatureSettings;
   memset(&WinTrustSignatureSettings, 0, sizeof(WinTrustSignatureSettings));

   WinTrustSignatureSettings.dwFlags = WSS_VERIFY_SPECIFIC;
   WinTrustSignatureSettings.cbStruct = sizeof(WinTrustData.pSignatureSettings);
   WinTrustSignatureSettings.dwIndex = 0;


   // WinVerifyTrust verifies signatures as specified by the GUID
   // and Wintrust_Data.
   lStatus = WinVerifyTrust(
      NULL,
      &WVTPolicyGUID,
      &WinTrustData);


   bool signatureValid = false;

   switch (lStatus)
   {
   case ERROR_SUCCESS:
      /*
      Signed file:
          - Hash that represents the subject is trusted.

          - Trusted publisher without any verification errors.

          - UI was disabled in dwUIChoice. No publisher or
              time stamp chain errors.

          - UI was enabled in dwUIChoice and the user clicked
              "Yes" when asked to install and run the signed
              subject.
      */

      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: Signature valid and trusted; next step does it match " << certFingerprintToMatch << __LINE__ << std::endl);
      if (certMatches(WinTrustData.hWVTStateData, certFingerprintToMatch))
      {
         signatureValid = true;
      }

      break;

   case TRUST_E_NOSIGNATURE:
      // The file was not signed or had a signature
      // that was not valid.

      // Get the reason for no signature.
      dwLastError = GetLastError();
      if (TRUST_E_NOSIGNATURE == dwLastError ||
         TRUST_E_SUBJECT_FORM_UNKNOWN == dwLastError ||
         TRUST_E_PROVIDER_UNKNOWN == dwLastError)
      {
         // The file was not signed.

         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: File not signed " << __LINE__ << std::endl);
      }
      else
      {
         // The signature was not valid or there was an error
         // opening the file.

         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: Signature not valid or error openeing file " << __LINE__ << std::endl);
      }

      break;

   case TRUST_E_EXPLICIT_DISTRUST:
      // The hash that represents the subject or the publisher
      // is not allowed by the admin or user.

      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_EXPLICIT_DISTRUST " << __LINE__ << std::endl);

      break;

   case TRUST_E_SUBJECT_NOT_TRUSTED:
      // The user clicked "No" when asked to install and run.

      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_SUBJECT_NOT_TRUSTED " << __LINE__ << std::endl);

      break;

   case CRYPT_E_SECURITY_SETTINGS:
      /*
      The hash that represents the subject or the publisher
      was not explicitly trusted by the admin and the
      admin policy has disabled user trust. No signature,
      publisher or time stamp errors.
      */

      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: CRYPT_E_SECURITY_SETTINGS " << __LINE__ << std::endl);

      break;

   default:
      // The UI was disabled in dwUIChoice or the admin policy
      // has disabled user trust. lStatus contains the
      // publisher or time stamp chain error.

      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: Error is " << lStatus << " " << __LINE__ << std::endl);

      break;
   }

   // Any hWVTStateData must be released by a call with close.
   WinTrustData.dwStateAction = WTD_STATEACTION_CLOSE;

   lStatus = WinVerifyTrust(
      NULL,
      &WVTPolicyGUID,
      &WinTrustData);

   return signatureValid;
}

bool getFilenameAttribValue(HANDLE catalogHandle, CRYPTCATMEMBER* member, std::wstring& outFilename)
{
   WCHAR key[] = L"Filename";
   CRYPTCATATTRIBUTE* attrib = CryptCATGetAttrInfo(catalogHandle, member, key);
   if (attrib)
   {
      const wchar_t* val = reinterpret_cast<wchar_t*>(attrib->pbValue);
      outFilename = val;
      return true;
   }

   return false;
}

bool ProcSigCheck::checkParentAppSignatureValidAndMatches(const std::string& certFingerprint)
{
   // not supported at the moment on windows
   return false;
}

bool ProcSigCheck::checkCatalogSignatureValidAndMatches(const std::string& catalogPathRelativeToAppPath, const std::string& certFingerprint)
{
   HANDLE CatHandle;

   wchar_t exePath[1042];
   if (GetModuleFileNameW(NULL, exePath, sizeof(exePath)) == 0)
   {
      if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: GetModuleFileNameW failed " << __LINE__ << std::endl);
      return false;
   }

   std::filesystem::path catalogPath(exePath);
   catalogPath.remove_filename();
   catalogPath /= catalogPathRelativeToAppPath;

   const std::wstring catalogPathWstr = catalogPath.c_str();
   CatHandle = CryptCATOpen(const_cast<LPWSTR>(catalogPathWstr.c_str()), CRYPTCAT_OPEN_EXISTING, 0, CRYPTCAT_VERSION_2, 0);
   if (CatHandle == INVALID_HANDLE_VALUE)
   {
      return false;
   }

   CRYPTCATMEMBER* pMember = NULL;

   int catMemberCount = 0;
   int verifiedCatMemberCount = 0;

   for (pMember = CryptCATEnumerateMember(CatHandle, pMember);
      NULL != pMember;
      pMember = CryptCATEnumerateMember(CatHandle, pMember))
   {
      ++catMemberCount;

      WINTRUST_FILE_INFO FileData;
      memset(&FileData, 0, sizeof(FileData));
      FileData.cbStruct = sizeof(WINTRUST_FILE_INFO);
      FileData.hFile = NULL;
      FileData.pgKnownSubject = NULL;


      GUID WVTPolicyGUID = WINTRUST_ACTION_GENERIC_VERIFY_V2;
      WINTRUST_DATA WinTrustData;

      // Initialize the WinVerifyTrust input data structure.

      // Default all fields to 0.
      memset(&WinTrustData, 0, sizeof(WinTrustData));

      WinTrustData.cbStruct = sizeof(WinTrustData);

      // Use default code signing EKU.
      WinTrustData.pPolicyCallbackData = NULL;

      // No data to pass to SIP.
      WinTrustData.pSIPClientData = NULL;

      // Disable WVT UI.
      WinTrustData.dwUIChoice = WTD_UI_NONE;

      // No revocation checking.
      WinTrustData.fdwRevocationChecks = WTD_REVOKE_NONE;

      // Verify an embedded signature on a catalog.
      WinTrustData.dwUnionChoice = WTD_CHOICE_CATALOG;

      // Verify action.
      WinTrustData.dwStateAction = WTD_STATEACTION_VERIFY;

      // Verification sets this value.
      WinTrustData.hWVTStateData = NULL;

      // Not used.
      WinTrustData.pwszURLReference = NULL;

      // This is not applicable if there is no UI because it changes
      // the UI to accommodate running applications instead of
      // installing applications.
      WinTrustData.dwUIContext = 0;


      std::wstring memberFilename;
      if (!getFilenameAttribValue(CatHandle, pMember, memberFilename))
      {
         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: Couldn't get filename attribute " << __LINE__ << std::endl);
         break;
      }

      // the path should be relative to the .cat file
      std::filesystem::path memberFilePath(catalogPath);
      memberFilePath.remove_filename();
      memberFilePath /= memberFilename;


      WINTRUST_CATALOG_INFO catInfo;
      memset(&catInfo, 0, sizeof(catInfo));
      catInfo.cbStruct = sizeof(catInfo);
      catInfo.pcwszCatalogFilePath = catalogPathWstr.c_str();
      catInfo.pcwszMemberFilePath = memberFilePath.c_str();
      catInfo.pcwszMemberTag = pMember->pwszReferenceTag;

      WinTrustData.pCatalog = &catInfo;


      WINTRUST_SIGNATURE_SETTINGS WinTrustSignatureSettings;
      memset(&WinTrustSignatureSettings, 0, sizeof(WinTrustSignatureSettings));

      WinTrustSignatureSettings.dwFlags = WSS_VERIFY_SPECIFIC;
      WinTrustSignatureSettings.cbStruct = sizeof(WinTrustData.pSignatureSettings);
      WinTrustSignatureSettings.dwIndex = 0;

      // WinVerifyTrust verifies signatures as specified by the GUID
      // and Wintrust_Data.
      DWORD dwLastError;
      LONG lStatus = WinVerifyTrust(
         NULL,
         &WVTPolicyGUID,
         &WinTrustData);

      BOOL ourCert = false;
      switch (lStatus)
      {
      case ERROR_SUCCESS:
         /*
         Signed file:
             - Hash that represents the subject is trusted.

             - Trusted publisher without any verification errors.

             - UI was disabled in dwUIChoice. No publisher or
                 time stamp chain errors.

             - UI was enabled in dwUIChoice and the user clicked
                 "Yes" when asked to install and run the signed
                 subject.
         */

         if (certMatches(WinTrustData.hWVTStateData, certFingerprint))
         {
            ++verifiedCatMemberCount;
         }
         break;

      case TRUST_E_NOSIGNATURE:
         // The file was not signed or had a signature
         // that was not valid.

         // Get the reason for no signature.
         dwLastError = GetLastError();
         if (TRUST_E_NOSIGNATURE == dwLastError ||
            TRUST_E_SUBJECT_FORM_UNKNOWN == dwLastError ||
            TRUST_E_PROVIDER_UNKNOWN == dwLastError)
         {
            // The file was not signed.

            if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_NOSIGNATURE " << __LINE__ << std::endl);
         }
         else
         {
            // The signature was not valid or there was an error
            // opening the file.

            if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_NOSIGNATURE " << __LINE__ << std::endl);
         }

         break;

      case TRUST_E_EXPLICIT_DISTRUST:
         // The hash that represents the subject or the publisher
         // is not allowed by the admin or user.

         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_EXPLICIT_DISTRUST " << __LINE__ << std::endl);
         break;

      case TRUST_E_SUBJECT_NOT_TRUSTED:
         // The user clicked "No" when asked to install and run.

         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: TRUST_E_SUBJECT_NOT_TRUSTED " << __LINE__ << std::endl);
         break;

      case CRYPT_E_SECURITY_SETTINGS:
         /*
         The hash that represents the subject or the publisher
         was not explicitly trusted by the admin and the
         admin policy has disabled user trust. No signature,
         publisher or time stamp errors.
         */

         if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: CRYPT_E_SECURITY_SETTINGS " << __LINE__ << std::endl);

         break;

      default:
         // The UI was disabled in dwUIChoice or the admin policy
         // has disabled user trust. lStatus contains the
         // publisher or time stamp chain error.
         break;
      }

      // Any hWVTStateData must be released by a call with close.
      WinTrustData.dwStateAction = WTD_STATEACTION_CLOSE;

      lStatus = WinVerifyTrust(
         NULL,
         &WVTPolicyGUID,
         &WinTrustData);
   }

   CryptCATClose(CatHandle);

   if (verifiedCatMemberCount == catMemberCount)
   {
      return true;
   }

   if (sigCheckLoggingEnabled()) DbgLog(<< "Sigcheck: verifiedCatMemberCount: " << verifiedCatMemberCount << " != catMemberCount: " << catMemberCount << " " << __LINE__ << std::endl);

   return false;
}

#endif // #if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)
#endif // #if WIN32
