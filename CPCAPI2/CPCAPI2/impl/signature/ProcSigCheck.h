#pragma once

#include <cpcstl/string.h>

namespace CPCAPI2
{
namespace Signature
{
   class ProcSigCheck
   {
   public:
      // checks the app hosting the SDK
      static bool checkCurrentAppSignatureValidAndMatches(const std::string& certFingerprint);
      static bool checkCatalogSignatureValidAndMatches(const std::string& catalogPathRelativeToAppPath, const std::string& certFingerprint);
      // checks the parent app to the current app hosting the SDK
      static bool checkParentAppSignatureValidAndMatches(const std::string& certFingerprint);

   };
}
}