#pragma once

#if !defined(CPCAPI2_SIGNATURE_MANAGER_INTERFACE_H)
#define CPCAPI2_SIGNATURE_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "signature/SignatureManager.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"

namespace CPCAPI2
{
class PhoneInterface;
namespace Signature
{
class SignatureManagerInterface : public SignatureManager, 
                                  public PhoneModule                                  
{
public:
   SignatureManagerInterface(Phone* phone);
   virtual ~SignatureManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // SignatureManager
   virtual bool brandedSignatureCheck() override;


private:
   PhoneInterface* mPhone;
};


}
}

#endif // CPCAPI2_SIGNATURE_MANAGER_INTERFACE_H

