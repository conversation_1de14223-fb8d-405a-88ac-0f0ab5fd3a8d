#include "brand_branded.h"
#if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)

#include "ProcSigCheck.h"
#import <Foundation/Foundation.h>
#import <Security/Security.h>
#import <AppKit/AppKit.h>
#import <libproc.h>

using namespace CPCAPI2::Signature;

static bool sigCheckLoggingEnabled()
{
   // as of writing early SDK logging with the electron app doesn't seem to work;
   // licensing happens before electron app enables logging.
   // so for now we'll use on stderr logging

   char* e = getenv("CPCAPI2_SIGCHECK_LOGGING");

   if (e && strlen(e) > 0)
   {
      return true;
   }
   return false;
}

bool checkSignatureValidAndMatches(SecCodeRef code, const std::string& certFingerprint)
{
   SecRequirementRef requirement = 0;
   
   // anchor apple generic indicates code signed using a signing certificated issued by Apple to developers.
   // verifying this is not important; what is important is verifying <PERSON><PERSON><PERSON> performed the signature --
   // which we verify by checking our certificate was the one used to validate the signature.
   
   // Hash is the SHA-1 fingerprint taken from our signing certificate
   
   NSString* fingerprint = [NSString stringWithUTF8String:certFingerprint.c_str()];
   NSString* secReqString = [NSString stringWithFormat:@"anchor trusted and anchor apple generic and certificate leaf = H\"%@\"", fingerprint];
   OSStatus status = SecRequirementCreateWithString((__bridge CFStringRef)secReqString, kSecCSDefaultFlags, &requirement);

   CFErrorRef errorRef;
   if (errSecSuccess != SecCodeCheckValidityWithErrors(code, kSecCSDefaultFlags, requirement, &errorRef))
   {
      NSString* err = [(__bridge NSError *)errorRef localizedDescription];
      if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: Not validated (code check)! " << [err UTF8String] << ", " << __LINE__ << std::endl;
      return false;
   }
   
   // modification to bundle was not caught in some cases with just call to SecCodeCheckValidityWithErrors
   if (errSecSuccess != SecStaticCodeCheckValidityWithErrors(code, kSecCSDefaultFlags, requirement, &errorRef))
   {
      NSString* err = [(__bridge NSError *)errorRef localizedDescription];
      if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: Not validated (code check)! " << [err UTF8String] << ", " << __LINE__ << std::endl;
      return false;
   }

   if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: Validated " << __LINE__ << std::endl;
   return true;
}

bool ProcSigCheck::checkCurrentAppSignatureValidAndMatches(const std::string& certFingerprint)
{
   SecCodeRef code;
   SecCodeCopySelf( kSecCSDefaultFlags, &code );
   
   return checkSignatureValidAndMatches(code, certFingerprint);
}

bool ProcSigCheck::checkCatalogSignatureValidAndMatches(const std::string& catalogPathRelativeToAppPath, const std::string& certFingerprint)
{
   // not supported on mac
   return false;
}

bool ProcSigCheck::checkParentAppSignatureValidAndMatches(const std::string& certFingerprint)
{
   pid_t parentPid = getppid();

   CFNumberRef value = NULL;
   CFDictionaryRef attributes = NULL;
   SecCodeRef code = NULL;
   CFURLRef path = NULL;
   CFStringRef posixPath = NULL;
   OSStatus status;
   char* ret = NULL;

   value = CFNumberCreate(kCFAllocatorDefault, kCFNumberSInt32Type, &parentPid);
   if (value == NULL)
   {
      if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: parent fail " << __LINE__ << std::endl;
      return false;
   }

   attributes = CFDictionaryCreate(kCFAllocatorDefault, (const void **)&kSecGuestAttributePid, (const void **)&value, 1, NULL, NULL);
   if (attributes == NULL)
   {
      if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: parent fail " << __LINE__ << std::endl;
      return false;
   }

   status = SecCodeCopyGuestWithAttributes(NULL, attributes, kSecCSDefaultFlags, &code);
   if (status)
   {
      if (sigCheckLoggingEnabled()) std::cerr << "Sigcheck: parent fail " << __LINE__ << std::endl;
      return false;
   }
   
   return checkSignatureValidAndMatches(code, certFingerprint);
}

#endif // CPCAPI2_BRAND_SIGNATURE_MODULE
