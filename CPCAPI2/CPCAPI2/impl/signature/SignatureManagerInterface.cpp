#include "brand_branded.h"

#include "SignatureManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "ProcSigCheck.h"

#include <boost/algorithm/string.hpp>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{
namespace Signature
{

SignatureManagerInterface::SignatureManagerInterface(Phone* phone) :
   mPhone(static_cast<PhoneInterface*>(phone))
{
}

void SignatureManagerInterface::Release()
{
   delete this;
}

SignatureManagerInterface::~SignatureManagerInterface()
{
}

bool SignatureManagerInterface::brandedSignatureCheck()
{
#if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)
   const std::string requiredFingerprint(CPCAPI2_BRAND_SIGNATURE_MODULE_CERT_FINGERPRINT);
   if (requiredFingerprint.empty())
   {
      return true;
   }

   const std::string testBrandedValue(CPCAPI2_BRAND_SIGNATURE_MODULE_CHECKS);
   std::vector<std::string> verify;

   boost::algorithm::split(verify, testBrandedValue, boost::is_any_of(":"));

   if (verify.size() == 0 && testBrandedValue.length() != 0)
   {
      ErrLog(<< "Failed parsing branding");
      return false;
   }

   int verifiedCount = 0;
   for (std::vector<std::string>::const_iterator it = verify.begin(); it != verify.end(); ++it)
   {
      if (*it == "{APP}")
      {
         if (ProcSigCheck::checkCurrentAppSignatureValidAndMatches(requiredFingerprint))
         {
            ++verifiedCount;
         }
      }
      else if (boost::algorithm::ends_with(*it, ".cat"))
      {
         if (ProcSigCheck::checkCatalogSignatureValidAndMatches(it->c_str(), requiredFingerprint))
         {
            ++verifiedCount;
         }
      }
      else if (boost::algorithm::ends_with(*it, "{PARENTAPP}"))
      {
         if (ProcSigCheck::checkParentAppSignatureValidAndMatches(requiredFingerprint))
         {
            ++verifiedCount;
         }
      }
   }

   return verifiedCount == verify.size();
#endif // CPCAPI2_BRAND_SIGNATURE_MODULE
   return false;
}

}
}
