#pragma once
#ifndef __CPCAPI2_DISPATCHERSHIM_H__
#define __CPCAPI2_DISPATCHERSHIM_H__

#include "WebSocketStateDispatcher.h"

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * The purpose of this class is to dispatch the websocket callbacks
       * into the correct states from the state machine. It maintains the
       * lifetime of certain objects that will be needed when async callbacks
       * fire after the WebSocketStateMachine has been deleted. This class
       * also handles the transfer of logging produced by the Websocketpp
       * library into our logging subsystem
       */
      class DispatcherShim :
         public WebSocketHandler,
         public TCPWebSocketHandler,
         public TLSWebSocketHandler
      {
         // this class is used to transfer logging produced by the Websocketpp library into our logging subsystem
         class MyLogger : public std::stringbuf
         {
            CPCAPI2_Subsystem mLoggingSubsystem;
            std::string mWebSocketName;

         public:
            MyLogger(CPCAPI2_Subsystem loggingSubsystem, const std::string& webSocketName) :
               mLoggingSubsystem(loggingSubsystem),
               mWebSocketName(webSocketName)
            {
            }

            virtual~ MyLogger()
            {
            }

            virtual int sync()
            {
               // map WebSocketpp error levels to CounterPath logging levels
               static const std::map<std::string, resip::Log::Level> wsLevelMap =
               {
                  {std::string("devel"), resip::Log::Debug},
                  {std::string("library"), resip::Log::Debug},
                  {std::string("info"), resip::Log::Info},
                  {std::string("warning"), resip::Log::Warning},
                  {std::string("error"), resip::Log::Err},
                  {std::string("fatal"), resip::Log::Err},
                  {std::string("unknown"), resip::Log::Err}
               };

               // the current content to be logged, should be one log line
               std::string temp = this->str();

               // lets strip the time as it'll be added again anyway
               std::size_t start = temp.find(']');
               if (start != std::string::npos)
               {
                  start += 2; // skip the bracket and the following space
                  temp = temp.substr(start);
               }

               // lets strip the level as we'll map it to one of ours
               std::string wsLevel = "";
               start = temp.find(']');
               if (start != std::string::npos)
               {
                  wsLevel = temp.substr(1, start - 1);
                  start += 2; // skip the bracket and the following space
                  temp = temp.substr(start);
               }

               // add the websocket name so we can identify the source of the logging
               temp = "WebSocketStateMachine(" + mWebSocketName + "): " + temp;

               // convert the WebSocketpp log level to a CounterPath log level
               resip::Log::Level level = resip::Log::Err;
               auto it = wsLevelMap.find(wsLevel);
               if (it != wsLevelMap.end())
                  level = it->second;
               else
                  assert(0);

               GenericLog(mLoggingSubsystem, level, << temp);

               // clear the current content so we don't log it again
               this->str("");

               return 0;
            }
         };

      public:
         DispatcherShim( WebSocketStateDispatcherPtr psm, CPCAPI2_Subsystem loggingSubsystem, const std::string& webSocketName )
         {
            m_WeakPtr = psm;

            // connect MyLogger to our logging`
            m_pStringBuf = new MyLogger(loggingSubsystem, webSocketName);
            m_pOStream = new std::ostream(m_pStringBuf);

            // connect the Websocketpp logging to MyLogger
            m_TCPEndpoint.get_alog().set_ostream(m_pOStream);
            m_TCPEndpoint.get_elog().set_ostream(m_pOStream);
            m_TLSEndpoint.get_alog().set_ostream(m_pOStream);
            m_TLSEndpoint.get_elog().set_ostream(m_pOStream);
         }
         virtual ~DispatcherShim()
         {
            m_WeakPtr.reset();

            // reset the Websocketpp logging back to default
            m_TCPEndpoint.get_alog().set_ostream(&std::cout);
            m_TCPEndpoint.get_elog().set_ostream(&std::cout);
            m_TLSEndpoint.get_alog().set_ostream(&std::cout);
            m_TLSEndpoint.get_elog().set_ostream(&std::cout);

            delete m_pStringBuf;
            delete m_pOStream;
            m_pStringBuf = NULL;
            m_pOStream = NULL;
         }

         tcpClient& TCPEndpoint() { return m_TCPEndpoint; }
         tcpConnectionPtr& TCPCon() { return m_TCPCon; }
         tlsClient& TLSEndpoint() { return m_TLSEndpoint; }
         tlsConnectionPtr& TLSCon() { return m_TLSCon; }

         void onOpen( websocketpp::connection_hdl hWebSock ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onOpen( hWebSock );
         }

         void onClose( websocketpp::connection_hdl hWebSock ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onClose( hWebSock );
         }

         void onFail( websocketpp::connection_hdl hWebSock ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onFail( hWebSock );
         }

         void onTCPMessage( websocketpp::connection_hdl hWebSock, tcpMessagePtr msg ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onTCPMessage( hWebSock, msg );
         }

         void onTCPSocketInit( websocketpp::connection_hdl hWebSock, tcpStream& stream ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onTCPSocketInit( hWebSock, stream );
         }

         tlsContext onTLSInit( websocketpp::connection_hdl hWebSock ) OVERRIDE
         {
            websocketpp::lib::shared_ptr<boost::asio::ssl::context> ctx;
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               return ptr->onTLSInit( hWebSock );
            else
               return ctx;
         }

         void onTLSSocketInit( websocketpp::connection_hdl hWebSock, tlsStream& stream ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onTLSSocketInit( hWebSock, stream );
         }

         void onTLSMessage( websocketpp::connection_hdl hWebSock, tlsMessagePtr msg ) OVERRIDE
         {
            WebSocketStateDispatcherPtr ptr( m_WeakPtr.lock() );
            if( ptr )
               ptr->onTLSMessage( hWebSock, msg );
         }

         void onPongTimeout(websocketpp::connection_hdl hWebSock)
         {
            WebSocketStateDispatcherPtr ptr(m_WeakPtr.lock());
            if (ptr)
               ptr->onPongTimeout(hWebSock);
         }

         bool onPing(websocketpp::connection_hdl hWebSock)
         {
            WebSocketStateDispatcherPtr ptr(m_WeakPtr.lock());
            if (ptr)
               return ptr->onPing(hWebSock);

            return true;
         }

      private:
         WebSocketStateDispatcherWPtr m_WeakPtr; // not owned, obvs.

         // the following are stored here because we need them to remain "alive" until the callbacks have been called

         tcpClient m_TCPEndpoint;
         tcpConnectionPtr m_TCPCon;
         tlsClient m_TLSEndpoint;
         tlsConnectionPtr m_TLSCon;

         // used to catch Websocketpp logging
         std::stringbuf* m_pStringBuf;
         std::ostream* m_pOStream;
      };
   }
}

#endif // __CPCAPI2_DISPATCHERSHIM_H__
