#pragma once
#ifndef __CPCAPI2_NOTIFICATIONSTATEDISPATCHER_H__
#define __CPCAPI2_NOTIFICATIONSTATEDISPATCHER_H__

#include <memory>

#include <cpcapi2defs.h>

#include <websocket/WebSocketTypes.h>
#include <websocket/WebSocketHandler.h>

#include "WebSocketState.h"

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

#if TARGET_OS_IPHONE
#include <CFNetwork/CFNetwork.h>
#endif

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;
      class DispatcherShim;

      /**
       * The purpose of this class is to dispatch the websocket callbacks
       * into the correct states from the state machine.
       */
      class WebSocketStateDispatcher :
         public WebSocketHandler,
         public TCPWebSocketHandler,
         public TLSWebSocketHandler
      {
      public:
         WebSocketStateDispatcher( WebSocketStateMachine *psm, const WebSocketSettings& settings );
         virtual ~WebSocketStateDispatcher();

         void onOpen( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         void onClose( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         void onFail( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         void onTCPMessage( websocketpp::connection_hdl hWebSock, tcpMessagePtr msg ) OVERRIDE;
         void onTCPSocketInit( websocketpp::connection_hdl hWebSock, tcpStream& stream ) OVERRIDE;
         tlsContext onTLSInit( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         void onTLSSocketInit( websocketpp::connection_hdl hWebSock, tlsStream& stream ) OVERRIDE;
         void onTLSMessage( websocketpp::connection_hdl hWebSock, tlsMessagePtr msg ) OVERRIDE;
         bool onVerifyPeerCallback( bool preverified, boost::asio::ssl::verify_context & ctx );
         void onPongTimeout(websocketpp::connection_hdl hWebSock);
         bool onPing(websocketpp::connection_hdl hWebSock);

      private:
         WebSocketStateMachine *m_StateMachine; // not owned
         const WebSocketSettings& m_Settings;
         std::string m_WebsocketName;
      };

      typedef std::shared_ptr< WebSocketStateDispatcher > WebSocketStateDispatcherPtr;
      typedef std::weak_ptr< WebSocketStateDispatcher > WebSocketStateDispatcherWPtr;
   }
}

#endif // __CPCAPI2_WEBSOCKETSTATEDISPATCHER_H__
