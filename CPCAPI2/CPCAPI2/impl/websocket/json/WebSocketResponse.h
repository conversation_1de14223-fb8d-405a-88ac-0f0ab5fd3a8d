#pragma once
#ifndef __CPCAPI2_WEBSOCKETRESPONSE_H__
#define __CPCAPI2_WEBSOCKETRESPONSE_H__

#include <string>

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * Interface for WebSocketResponse(s) which are sent from the server to
       * the client.
       */
      class WebSocketResponse
      {
      public:
         virtual ~WebSocketResponse() {}

         /**
          * Returns a constant char which is the command name used by the
          * server to represent this response (NB: this may be called out of
          * context of parsing for lookups).
          */
         virtual const char *getCommandName() const = 0;

         /**
          * Returns the request handle which was parsed from the JSON document.
          * Note that this will return CPCAPI2_JSON_NO_REQUEST_ID if the
          * document was not parsed properly.
          */
         virtual const RequestHandle getRequestHandle() const = 0;

         /**
          * Parses the document, and populates the subclass' information
          * (including the request handle)
          */
         virtual bool fromString( const std::string &inDocument ) = 0;
      };
   }
}

#endif // __CPCAPI2_VCCS_WEBSOCKETRESPONSE_H__
