#pragma once
#ifndef __CPCAPI2_JSON_DATA_WEBSOCKET_COMMAND_H__
#define __CPCAPI2_JSON_DATA_WEBSOCKET_COMMAND_H__

#include "WebSocketCommand.h"
#include "json/JsonData.h"
#include "websocket/WebSocketCommonTypes.h"

#include <ostream>

namespace CPCAPI2
{
namespace JsonApi
{

class JsonDataWebSocketCommand : public CPCAPI2::WebSocket::WebSocketCommand
{
public:

   JsonDataWebSocketCommand(CPCAPI2::Json::JsonDataPointer jsonData) : mJsonData(jsonData) {}

   const char *getCommandName() const
   {
      return "";
   }

   const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const
   {
      return 0;
   }

   virtual bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string& outstring )
   {
      outstring.reserve(mJsonData->getMessageSize() + mJsonData->getBinarySize());
      outstring.append(mJsonData->getMessageData(), mJsonData->getMessageSize());
      outstring.append(mJsonData->getBinaryData(), mJsonData->getBinarySize());
      return true;
   }

   virtual std::string getLogString( const CPCAPI2::WebSocket::RequestHandle& hRequest = 0 ) const
   {
      std::string logString(mJsonData->getMessageData(), mJsonData->getMessageSize());
      if (mJsonData->isBinary())
      {
         logString += " [binary data]";
      }
      return logString;
   }

   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::JsonApi::JsonDataWebSocketCommand& val);

protected:
   virtual size_t getMessageSizeEstimate(const CPCAPI2::WebSocket::RequestHandle& hRequest)
   {
      return mJsonData->getMessageSize() + mJsonData->getBinarySize();
   }

   virtual websocketpp::frame::opcode::value getMode()
   {
      if (mJsonData->isBinary())
      {
         return websocketpp::frame::opcode::binary;
      }
      else
      {
         return websocketpp::frame::opcode::text;
      }
   }

private:
   const CPCAPI2::Json::JsonDataPointer mJsonData;
};

inline std::ostream& operator<<(std::ostream& os, const CPCAPI2::JsonApi::JsonDataWebSocketCommand& val)
{
   return os << val.getLogString(0);
}

} // namespace JsonApi
} // namespace CPCAPI2

#endif // __CPCAPI2_JSON_DATA_WEBSOCKET_COMMAND_H__
