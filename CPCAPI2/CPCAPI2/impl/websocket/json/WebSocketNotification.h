#pragma once
#ifndef __CPCAPI2_WEBSOCKETNOTIFICATION_H__
#define __CPCAPI2_WEBSOCKETNOTIFICATION_H__

#include <string>

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * Abstract class for WebSocketNotification(s) which are sent from the client to
       * the server. All commands have a request and a name, and also support
       * converstion to a JSON format.
       */
      class WebSocketNotification
      {
      public:
         virtual ~WebSocketNotification() {}

         /**
          * Returns the string which is used by the server for this notification.
          */
         virtual const char *getNotificationName() const = 0;

         /**
          * Parses the document and populates the subclass' information.
          */
         virtual bool fromString( const std::string &inDocument ) = 0;
      };
   }
}

#endif // __CPCAPI2_WEBSOCKETNOTIFICATION_H__