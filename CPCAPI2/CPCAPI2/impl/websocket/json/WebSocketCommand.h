#pragma once
#ifndef __CPCAPI2_WEBSOCKETCOMMAND_H__
#define __CPCAPI2_WEBSOCKETCOMMAND_H__

#include <json/JsonHelper.h>
#include "../WebSocketCommonTypes.h"
#include <websocket/WebSocketTypes.h>

#include <websocketpp/transport/stub/base.hpp>

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * Interface for WebSocketCommand(s) which are sent from the client to
       * the server. All commands have a request and a name, and also support
       * converstion to a JSON format.
       */
      class WebSocketCommand
      {
      public:
         WebSocketCommand() {}

         virtual ~WebSocketCommand() {}

         /**
          * Returns a constant char which is the command name used by the
          * server to represent this command (NB: this may be called out of
          * context of parsing for lookups).
          */
         virtual const char *getCommandName() const = 0;

         /**
          * Returns the request handle which was placed into the JSON document.
          * Note that this will return CPCAPI2_JSON_NO_REQUEST_ID if the
          * document was not constructed, yet. This depends on the toString
          * method being called (since the object is re-used).
          */
         virtual const RequestHandle getRequestHandle() const = 0;

         /**
          * Builds a document (in string form) using the given request handle.
          * this method is important to keep the state machine generic and to
          * abstract the specific handling of JSON.
          */
         virtual bool toString( const RequestHandle& hRequest, std::string& outstring ) = 0;

         /**
          * Returns a string to use for identifying the command in logging statement
          * Ex. for remote sync SyncCmd `SYNC sms`
          */
         virtual const std::string getLogIdentifier()
         {
            return getCommandName();
         }

         /**
          * Returns a string to use for logging i.e. binary data censored
          */
         virtual std::string getLogString( const RequestHandle& hRequest )
         {
            std::string outstring;
            if (toString(hRequest, outstring))
            {
               return outstring;
            }
            else
            {
               return "Error generating command text";
            }
         };

      protected:
         virtual size_t getMessageSizeEstimate(const RequestHandle& hRequest) const
         {
            return 1024; // Most messages should be under 1kb
         }

         virtual websocketpp::frame::opcode::value getMode() const
         {
            return websocketpp::frame::opcode::text;
         }

      public:
         /**
          * Sends the message to the wire. A generic implementation is provided here,
          * but subclasses can use thier own implementation to prevent string copies.
          */
         template <typename connection, typename config>
         bool sendMessage(const RequestHandle& hRequest, websocketpp::connection_hdl hdl, websocketpp::endpoint<connection, config>& endpoint, websocketpp::lib::error_code& err)
         {
            auto conn = endpoint.get_con_from_hdl(hdl, err);
            if ( err )
            {
               return false;
            }

            typename connection::message_ptr msg = conn->get_message(this->getMode(), this->getMessageSizeEstimate(hRequest));
            std::string& payload = msg->get_raw_payload();

            this->toString(hRequest, payload);

            endpoint.send(hdl, msg, err);

            return err.value() == 0;
         }
      };
   }
}

#endif // __CPCAPI2_WEBSOCKETCOMMAND_H__
