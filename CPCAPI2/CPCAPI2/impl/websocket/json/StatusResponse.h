#pragma once
#ifndef __CPCAPI2_STATUSRESPONSE_H__
#define __CPCAPI2_STATUSRESPONSE_H__

#include <string>
#include <map>

#include <cpcapi2defs.h>
#include "WebSocketResponse.h"
#include "json/JsonHelper.h"

namespace CPCAPI2
{
   namespace WebSocket
   {
      static const char *RESPONSE_NAME_STATUS = "STATUS_RESPONSE";
      static const char *CLIENT_COMMAND = "cmd";
      static const char *CLIENT_REQUEST_ID = "requestID";

      class StatusResponse : public WebSocketResponse
      {
      public:
         StatusResponse() : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~StatusResponse() {}

         virtual const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_STATUS; }

         /**
          * Returns the command name which resulted in this status response
          * (as the status response applies to multiple possible commands).
          */
         const char *getOriginalCommandName() const
         {
            return m_OriginalCommandName.c_str();
         }

         const char *getErrorCode() const
         {
            return m_ErrorCode.c_str();
         }

         bool isErrorCondition() const
         {
            return ( m_ErrorCode != "OK" );
         }

         bool getExtensionAttribute( const char *attrName, int& outValue ) const
         {
            std::map< std::string, int >::const_iterator iter = m_ExtensionAttributes.find( attrName );
            if( iter != m_ExtensionAttributes.end() )
            {
               outValue = iter->second;
               return true;
            }
            return false;
         }

         bool getExtensionAttribute( const char *attrName, std::string& outValue ) const
         {
            std::map< std::string, std::string >::const_iterator iter = m_ExtensionAttributesStr.find( attrName );
            if( iter != m_ExtensionAttributesStr.end() )
            {
               outValue = iter->second;
               return true;
            }
            return false;
         }

         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            const std::string cmdName( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmdName != getCommandName() )
               return false;

            // {"requestID":1,"errorCode":"OK","cmd":"STATUS_RESPONSE","command":"LOGIN"}

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // error code ( error is assumed if not present )
            if( inDocument.HasMember( "errorCode" ))
               m_ErrorCode = inDocument[ "errorCode" ].GetString();

            // Parse the original command name, in derived classes this is optional
            if( inDocument.HasMember( "command" ))
               m_OriginalCommandName = inDocument[ "command" ].GetString();

            // Parse all values into a map indexed by key (treated as extension attributes)
            // This will include things specifically parsed above, but just ignore them :-P
            for( rapidjson::Value::ConstMemberIterator itr = inDocument.MemberBegin();
                 itr != inDocument.MemberEnd(); ++itr )
            {
               if( itr->value.GetType() == rapidjson::kNumberType )
                  m_ExtensionAttributes[ itr->name.GetString() ] = itr->value.GetInt();
               else if( itr->value.GetType() == rapidjson::kStringType )
                  m_ExtensionAttributesStr[ itr->name.GetString() ] = itr->value.GetString();
            }

            return true;
         }

      protected:
         RequestHandle m_hRequest;
         std::string m_OriginalCommandName;
         std::string m_ErrorCode;
         std::map< std::string, int > m_ExtensionAttributes;
         std::map< std::string, std::string > m_ExtensionAttributesStr;
      };
   }
}

#endif // __CPCAPI2_STATUSRESPONSE_H__
