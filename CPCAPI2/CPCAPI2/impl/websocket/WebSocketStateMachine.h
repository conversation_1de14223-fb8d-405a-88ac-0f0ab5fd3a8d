#pragma once
#ifndef __CPCAPI2_NOTIFICATION_CONNECTIONSTATEMACHINE_H__
#define __CPCAPI2_NOTIFICATION_CONNECTIONSTATEMACHINE_H__

#include <string>
#include <map>
#include <list>
#include <boost/asio.hpp>

#include <util/statemachine/AbstractStateMachineListener.h>
#include <util/statemachine/AbstractStateMachine.h>

#include <websocket/WebSocketTypes.h>
#include <websocket/WebSocketStateDispatcher.h>

#include "WebSocketCommonTypes.h"
#include "util/LogSubsystems.h"

namespace CPCAPI2
{
   namespace WebSocket
   {
      // Forward decl's
      class WebSocketState;
      class WebSocketStateDispatcher;
      class WebSocketCommand;

      /**
       * Listener(s) to the state machine. All callbacks are invoked
       * directly inside of the state machine thread.
       */
      class WebSocketStateMachineListener : public AbstractStateMachineListener
      {
      public:
         /**
          * Called by the state machine when a login message needs to be sent
          * (optional, depends on the protocol). This callback happens while
          * the state machine is in the "Connecting" state.
          */
         virtual void onLogin( void ) = 0;

         /**
          * Called by the state machine when a login message response is
          * received (optional, depends on the protocol). The state listener
          * may decide upon processing of this response, which state to advance
          * the state machine, so long as it is along the diagram below. This
          * callback happens while the state machine is in the "Connecting"
          * state.
          */
         virtual void onLoginResponse( const std::string& inDocument ) = 0;

         /**
          * Called by the state machine when a logout message needs to be send
          * (again, optional). A response to a logout message will be ignored
          * and not delivered to onMessage.
          */
         virtual void onLogout( void ) = 0;

         /**
          * Called by the state machine when a message (either a response or
          * notification) is received while in connected state.
          */
         virtual void onMessage( const std::string& inDocument ) = 0;

         /**
          * Callback from the websocket state machine when a ping message
          * is required to be sent. The application can choose to invoke
          * the ping method on the state machine, in which case a websocketpp
          * 'ping' message will be sent (this is the recommended course of
          * action). However some modules have special ping functionality
          * that needs to override this behavior.
          */
         virtual void onPing( websocketpp::connection_hdl hWebSock ) = 0;

         /**
          * Returns true if the websocket state machine should reconnect
          * after a failure. The websocket close/fail code is passed as
          * a parameter.
          */
         virtual bool onReconnect( uint16_t code ) = 0;

         virtual void onClose() {}
      };

      /**
       * This class implements the state machine. There is no actual
       * implementation in this file except for the state transitions,
       * which delegate to the actual state objects.
       *
       * The responsibility of this file is to implement the state
       * transitions and ensure that the states move in the correct
       * way.
       *
       * The state machine follows this diagram:
       *
       * +----------------------------------------------------------------------------------------------------+
       * |                                                                                                    |
       * |                                                          timer                                     |
       * |                                             +------------------------------------------------------+------------+
       * |                                             |                                                      |            |
       * | disconnect                  disconnect      |                                                      |            |
       * |              +------------------------------+--------------------------+                           |            |
       * |              v                              v                          |                           |            |
       * |            +--------------+  connect      +------------+  success    +-----------+  inactivity   +-----------+  |
       * +----------> |              | ------------> |            | ----------> | Connected | ------------> | Suspended |  |
       *              |              |               |            |             +-----------+               +-----------+  |
       *              |              |  disconnect   |            |               |                           |            |
       *              | Disconnected | <------------ | Connecting |               | failure                   |            |
       *              |              |               |            |               v                           |            |
       *              |              |               |            |  failure    +-----------+                 |            |
       *              |              |               |            | ----------> |  Failed   | ----------------+------------+
       *              +--------------+               +------------+             +-----------+                 |
       *                ^                              ^            activity      |                           |
       *                | disconnect                   +--------------------------+---------------------------+
       *                |                                                         |
       *                |                                                         |
       *                +---------------------------------------------------------+
       *
       * Functions which are needed by more than one state are also
       * included here, for convenience.
       */
      class WebSocketStateMachine : public AbstractStateMachine
      {
      public:
         /**
          * Warning: settings is stored as a reference by WebSocketStateMachine. Ensure
          * the settings instance you pass exists for the lifetime of this WebSocketStateMachine
          *
          * webSocketName param is only used for debugging/logging purposes.
          */
         WebSocketStateMachine(
            CPCAPI2_Subsystem loggingSubsystem,
            boost::asio::io_service& io_service,
            const WebSocketSettings& settings,
            const std::string& webSocketName );
         virtual ~WebSocketStateMachine();

         /**
          * Sets a flag which allows suspending of the connection. If set to
          * true, the flag will allow the state machine to transition into the
          * suspend state at the next opportunity. The suspended state will
          * drop the connection to the server and shut down the connection
          * (allowing valuable server resources to be reclaimed). If the flag
          * is unset (false) the state machine will avoid going into the
          * suspend state and instead try to keep the connection alive. If the
          * state machine is currently suspended and the flag is set to true
          * (or if activity is required on the connection by virtue of a
          * command being issued), the state will automatically transition back
          * to "connecting".
          */
         void setSuspendable( bool isSuspendable );

         /**
          * Converts a specific WebSocketCommand to a JSON Document, along with
          * the request handle (which is included in the document). Checks
          * the current state to ensure we are in a connected state, and
          * if so, the command will be sent to the server and true will be
          * returned.
          *
          * If a command is sent while the state machine is in "suspended"
          * state, the command will fail, however the state machine will
          * automatically initiate a state transition into "connecting" state,
          * at which point the application may reissue the command in question.
          *
          * If anything else failed, false will be returned and the
          * outErrMessage will be set appropriately.
          */
         bool sendCommand(
            const RequestHandle& hRequest,
            WebSocketCommand& command,
            std::string& outErrMessage,
            bool isLogCensored = false);

         /**
          * Sends a websocketpp-level ping message
          */
         void sendPing( websocketpp::connection_hdl hWebSock );

         /**
          * Sends a websocketpp-level ping message
          */
         void sendPing();

      private:

         // Settings used by the state machine for behavioural options
         WebSocketSettings m_Settings;
         
         // Websocketpp connection-related information

         // websocketpp listener/dispatcher (owned)
         WebSocketStateDispatcherPtr m_Dispatcher;

         // wrapper for the above
         std::shared_ptr< DispatcherShim > m_pShim;

         // suspendable flag
         bool m_IsSuspendable;
         
      private: // Methods only callable by the states

         friend class StateConnected;
         friend class StateConnecting;
         friend class StateDisconnected;
         friend class StateFailed;
         friend class StateSuspended;
         friend class WebSocketStateDispatcher;

         const websocketpp::connection_hdl getActiveConnectionHandle();
         bool openConnection( const std::string& wsURL, websocketpp::lib::error_code& ec );
         void closeConnections( const std::string& reason );
         bool isSuspendable( void ) { return m_IsSuspendable; }

         // Allow the states to trigger these callbacks
         void fireLogin( void );
         void fireLoginResponse( const std::string& inDocument );
         void fireLogout( void );
         void fireMessage( const std::string& inDocument );
         void firePing( websocketpp::connection_hdl hWebSock );
         bool fireOnReconnect( uint16_t code );
         void fireClose();
      };
      
      std::ostream& operator<<(std::ostream& os, const CertVerificationMode& mode);
      std::ostream& operator<<(std::ostream& os, const WebSocketSettings& settings);
   }
}

#endif // __CPCAPI2_NOTIFICATION_CONNECTIONSTATEMACHINE_H__
