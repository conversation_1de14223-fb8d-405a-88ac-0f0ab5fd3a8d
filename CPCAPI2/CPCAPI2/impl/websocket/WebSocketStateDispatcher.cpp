#include <rutil/Logger.hxx>

#include <assert.h>
#include <document.h>

#include "WebSocketStateDispatcher.h"
#include "WebSocketStateMachine.h"
#include "DispatcherShim.h"

// Used for loading system cert stores:
#if defined(WinRT)
#include <resip/stack/ssl/counterpath/XWinRTSecurity.hxx>
#elif _WIN32
#include <resip/stack/ssl/counterpath/XWinSecurity.hxx>
#elif __linux__
#if ANDROID
#include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
#else
#include <resip/stack/ssl/counterpath/LinuxSecurity.hxx>
#endif // __linux__
#elif BB10
#include <resip/stack/ssl/counterpath/BlackberrySecurity.hxx>
#elif __APPLE__
#include "resip/stack/ssl/IOSSecurity.hxx"
#elif __THREADX
#include <resip/stack/ssl/counterpath/ThreadXSecurity.hxx>
#endif

#include "util/LogSubsystems.h"
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#include "util/BoostTlsHelper.h"

using namespace CPCAPI2::WebSocket;

WebSocketStateDispatcher::WebSocketStateDispatcher( WebSocketStateMachine *psm, const WebSocketSettings& settings )
   : m_StateMachine( psm ),
     m_Settings( settings )
{
   if (m_StateMachine)
   {
      m_WebsocketName = m_StateMachine->m_MachineName;
   }

   assert( m_StateMachine != NULL );
}

WebSocketStateDispatcher::~WebSocketStateDispatcher()
{
}

void WebSocketStateDispatcher::onOpen( websocketpp::connection_hdl hWebSock )
{
   WebSocketStatePtr state = std::dynamic_pointer_cast< WebSocketState >( m_StateMachine->getCurrentState() );
   if( state != NULL )
      state->onOpen();
}

void WebSocketStateDispatcher::onClose( websocketpp::connection_hdl hWebSock )
{
   uint16_t remoteCode( 0 ), localCode( 0 );
   std::string remoteReason, localReason;

   if( m_StateMachine->m_pShim->TCPCon() )
   {
      if (m_StateMachine->m_pShim->TCPCon() != m_StateMachine->m_pShim->TCPEndpoint().get_con_from_hdl(hWebSock))
      {
         return;
      }
      remoteCode = m_StateMachine->m_pShim->TCPCon()->get_remote_close_code();
      remoteReason = m_StateMachine->m_pShim->TCPCon()->get_remote_close_reason();
      localCode = m_StateMachine->m_pShim->TCPCon()->get_local_close_code();
      localReason = m_StateMachine->m_pShim->TCPCon()->get_local_close_reason();
   }
   else if( m_StateMachine->m_pShim->TLSCon() )
   {
      if (m_StateMachine->m_pShim->TLSCon() != m_StateMachine->m_pShim->TLSEndpoint().get_con_from_hdl(hWebSock))
      {
         return;
      }
      remoteCode = m_StateMachine->m_pShim->TLSCon()->get_remote_close_code();
      remoteReason = m_StateMachine->m_pShim->TLSCon()->get_remote_close_reason();
      localCode = m_StateMachine->m_pShim->TLSCon()->get_local_close_code();
      localReason = m_StateMachine->m_pShim->TLSCon()->get_local_close_reason();
   }

   GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, <<
      "onClose called from websocket (" << m_WebsocketName << ")," <<
      " remoteCode: "   << remoteCode <<
      " remoteReason: " << remoteReason <<
      " localCode: "    << localCode <<
      " localReason: "  << localReason );

   WebSocketStatePtr state = std::dynamic_pointer_cast< WebSocketState >( m_StateMachine->getCurrentState() );
   if( state != NULL )
      state->onClose( remoteCode, remoteReason );
}

void WebSocketStateDispatcher::onFail( websocketpp::connection_hdl hWebSock )
{
   uint16_t remoteCode( 0 ), localCode( 0 );
   std::string remoteReason, localReason;

   if( m_StateMachine->m_pShim->TCPCon() )
   {
      if (m_StateMachine->m_pShim->TCPCon() != m_StateMachine->m_pShim->TCPEndpoint().get_con_from_hdl(hWebSock))
      {
         return;
      }
      remoteCode = m_StateMachine->m_pShim->TCPCon()->get_remote_close_code();
      remoteReason = m_StateMachine->m_pShim->TCPCon()->get_remote_close_reason();
      localCode = m_StateMachine->m_pShim->TCPCon()->get_local_close_code();
      localReason = m_StateMachine->m_pShim->TCPCon()->get_local_close_reason();
   }
   else if( m_StateMachine->m_pShim->TLSCon() )
   {
      if (m_StateMachine->m_pShim->TLSCon() != m_StateMachine->m_pShim->TLSEndpoint().get_con_from_hdl(hWebSock))
      {
         return;
      }
      remoteCode = m_StateMachine->m_pShim->TLSCon()->get_remote_close_code();
      remoteReason = m_StateMachine->m_pShim->TLSCon()->get_remote_close_reason();
      localCode = m_StateMachine->m_pShim->TLSCon()->get_local_close_code();
      localReason = m_StateMachine->m_pShim->TLSCon()->get_local_close_reason();
   }

   GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, <<
      "onFail called from websocket(" << m_WebsocketName << ")," <<
      " remoteCode: "   << remoteCode <<
      " remoteReason: " << remoteReason <<
      " localCode: "    << localCode <<
      " localReason: "  << localReason );

   WebSocketStatePtr state = std::dynamic_pointer_cast< WebSocketState >( m_StateMachine->getCurrentState() );
   if( state != NULL )
      state->onFail( remoteCode, remoteReason );
}

void WebSocketStateDispatcher::onTCPMessage( websocketpp::connection_hdl hWebSock, tcpMessagePtr msg )
{
   if (m_StateMachine->m_pShim->TCPCon() != m_StateMachine->m_pShim->TCPEndpoint().get_con_from_hdl(hWebSock))
   {
      return;
   }

   // Log all incoming messages here. Outbound messages are logged
   // in the JSONUtils class.
   std::string message = msg->get_payload();
   if( m_Settings.logPayload )
   {
      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info,
         << "\n========= Received from websocket (" << m_WebsocketName << ") ========\n" <<
         message.c_str() << "\n" <<
         "===========================================" );
   }

   // Scan for (and log) the skey token. This requires one additional parsing step
   // however the advantage is that we can do it in a single spot.
   {
      rapidjson::Document inDocument;
      inDocument.Parse<0>( message.c_str() );

      if( inDocument.HasMember( "skey" ))
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info,
            << "Received (" << m_WebsocketName << ") skey of " << inDocument[ "skey" ].GetString() );
      }
   }

   WebSocketStatePtr state = std::dynamic_pointer_cast< WebSocketState >( m_StateMachine->getCurrentState() );
   if( state != NULL )
      state->onMessage( message );
}

void WebSocketStateDispatcher::onTCPSocketInit( websocketpp::connection_hdl hWebSock, tcpStream& stream )
{
}

void WebSocketStateDispatcher::onTLSMessage( websocketpp::connection_hdl hWebSock, tlsMessagePtr msg )
{
   if (m_StateMachine->m_pShim->TLSCon() != m_StateMachine->m_pShim->TLSEndpoint().get_con_from_hdl(hWebSock))
   {
      return;
   }

   // Log all incoming messages here. Outbound messages are logged
   // in the JSONUtils class.
   std::string message = msg->get_payload();
   if( m_Settings.logPayload )
   {
      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info,
         << "\n========= Received from websocket (" << m_WebsocketName << ") ========\n" <<
         message.c_str() << "\n" <<
         "===========================================" );
   }

   // Scan for (and log) the skey token. This requires one additional parsing step
   // however the advantage is that we can do it in a single spot.
   {
      rapidjson::Document inDocument;
      inDocument.Parse<0>( message.c_str() );

      if( inDocument.HasMember( "skey" ))
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info,
            << "Received (" << m_WebsocketName << ") skey of " << inDocument[ "skey" ].GetString() );
      }
   }

   WebSocketStatePtr state = std::dynamic_pointer_cast< WebSocketState >( m_StateMachine->getCurrentState() );
   if( state != NULL )
      state->onMessage( message );
}

void WebSocketStateDispatcher::onPongTimeout(websocketpp::connection_hdl hWebSock)
{
   GenericLog(m_StateMachine->m_LoggingSubsystem, resip::Log::Info, <<
      "onPongTimeout called from websocket (" << m_WebsocketName << "), connection failed.");

   WebSocketStatePtr state = std::dynamic_pointer_cast<WebSocketState>(m_StateMachine->getCurrentState());
   if (state != NULL)
      state->onFail(0, "Pong timeout");
}

// server sent a ping request
bool WebSocketStateDispatcher::onPing(websocketpp::connection_hdl hWebSock)
{
   GenericLog(m_StateMachine->m_LoggingSubsystem, resip::Log::Debug, <<
      "received server ping from websocket (" << m_WebsocketName << "). Responding with pong");

   return true;
}

// Similar code in StrettoTunnelInternalTransport.cpp (opportunity for consolidation?)
static bool getPublicKeyFromCertificate(X509 *cert, cpc::string &publicKey)
{
   bool result = false;

   // Refer to https://www.owasp.org/index.php/Certificate_and_Public_Key_Pinning#OpenSSL

   int len1 = 0, len2 = 0;
   unsigned char *buff1 = NULL;

   do
   {
      /* Begin Gyrations to get the subjectPublicKeyInfo       */
      /* Thanks to Viktor Dukhovni on the OpenSSL mailing list */

      /* http://groups.google.com/group/mailing.openssl.users/browse_thread/thread/d61858dae102c6c7 */
      len1 = i2d_X509_PUBKEY(X509_get_X509_PUBKEY(cert), NULL);
      if(!(len1 > 0))
         break; /* failed */

      /* scratch */
      unsigned char* temp = NULL;

      /* http://www.openssl.org/docs/crypto/buffer.html */
      buff1 = temp = (unsigned char*)OPENSSL_malloc(len1);
      if(!(buff1 != NULL))
          break; /* failed */

      /* http://www.openssl.org/docs/crypto/d2i_X509.html */
      len2 = i2d_X509_PUBKEY(X509_get_X509_PUBKEY(cert), &temp);

      /* These checks are verifying we got back the same values as when we sized the buffer.      */
      /* Its pretty weak since they should always be the same. But it gives us something to test. */
      if(!((len1 == len2) && (temp != NULL) && ((temp - buff1) == len1)))
          break; /* failed */

      /* End Gyrations */

      resip::Data pubKey = resip::Data(buff1, len1);
      publicKey = pubKey.base64encode().c_str();

      result = true;
   } while (0);

   /* http://www.openssl.org/docs/crypto/buffer.html */
   if(NULL != buff1)
      OPENSSL_free(buff1);

   return result;
}

bool WebSocketStateDispatcher::onVerifyPeerCallback(
   bool preverified,    // True if the certificate passed pre-verification.
   boost::asio::ssl::verify_context& ctx) // The peer certificate and other context.
{
   bool retVal = ( preverified || m_Settings.certMode == CertVerificationMode_None );
   cpc::string publicKey;
   X509 *cert = NULL;

   STACK_OF(X509) *chain = X509_STORE_CTX_get_chain(ctx.native_handle());
   if (chain)
   {
      int n = sk_X509_num(chain);
      if (n > 0)
      {
         cert = sk_X509_value(chain, 0);
      }
   }

   if (cert)
   {
      char subject_name[256];
      X509_NAME_oneline(X509_get_subject_name(cert), subject_name, sizeof(subject_name));
      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Verifying: " << subject_name);

      if (getPublicKeyFromCertificate(cert, publicKey) == false)
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") No public key");
         return retVal;
      }
   }
   else
   {
      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") No server certificate");
      return retVal;
   }

   if( m_Settings.acceptedCertPublicKeys.size() > 0 )
   {
      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Checking Accepted Cert List" );
      cpc::vector< cpc::string >::const_iterator it = m_Settings.acceptedCertPublicKeys.begin();
      for( ; it!= m_Settings.acceptedCertPublicKeys.end() ; ++it)
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Matching " << publicKey << " against " << *it);
         if (publicKey == *it)
         {
            GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Public key matched");
            retVal = true;
            break;
         }
      }
   }

   if( m_Settings.requiredCertPublicKeys.size() > 0 )
   {
      bool matchedRequiredPublicKey = false;

      GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Checking Required Cert List" );
      cpc::vector< cpc::string >::const_iterator it = m_Settings.requiredCertPublicKeys.begin();
      for( ; it!= m_Settings.requiredCertPublicKeys.end() ; ++it)
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Matching " << publicKey << " against " << *it);
         if (publicKey == *it)
         {
            GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Public key matched");
            matchedRequiredPublicKey = true;
            break;
         }
      }

      if( !matchedRequiredPublicKey )
      {
         GenericLog( m_StateMachine->m_LoggingSubsystem, resip::Log::Info, << "WebSocketStateDispatcher(" << m_WebsocketName << ") Public key not found: " << publicKey);
         retVal = false;
      }
   }

   return retVal;
}

tlsContext WebSocketStateDispatcher::onTLSInit( websocketpp::connection_hdl hWebSock )
{
   // When TLS is initialized, hook into the existing system's certificate store
   websocketpp::lib::shared_ptr<boost::asio::ssl::context> ctx = initializeBoostTlsContext(m_Settings.tlsVersion, m_Settings.cipherSuite.c_str(), resip::SecurityTypes::TLSMode_TLS_Client);

   if( m_Settings.certMode != CertVerificationMode_None )
   {
      X509_STORE* rootSSLCerts = 0;
      resip::Data pathToCerts = ( m_Settings.certStorageFileSystemPath.c_str() );
      unsigned short usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;

      switch( m_Settings.certStorageLoadType )
      {
      case CertStorageLoadType_Both:
         usCertStorageType = ( resip::CERT_OS_SPECIFIC_STORAGE | resip::CERT_FILESYSTEM_STORAGE );
         break;
      case CertStorageLoadType_FileSystem:
         usCertStorageType = resip::CERT_FILESYSTEM_STORAGE;
         break;
      case CertStorageLoadType_OS:
      default:
         usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
         break;
      }

      // Override setting if there's no path
      if( pathToCerts.empty() )
         usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;

#if defined(WinRT)
      resip::Security* sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType);
#elif _WIN32
      resip::Security* sec = new resip::XWinSecurity(pathToCerts, usCertStorageType);
#elif __APPLE__
      resip::Security* sec = new resip::IOSSecurity(pathToCerts, usCertStorageType);
#elif __linux__
#if ANDROID
      resip::Security* sec = new resip::AndroidSecurity(pathToCerts);
#else
      resip::Security* sec = new resip::LinuxSecurity(pathToCerts);
#endif
#elif BB10
      resip::Security* sec = new resip::BlackberrySecurity(pathToCerts);
#else
      assert(0);
#endif

      //load all the certificates
      sec->preload();
      SSL_CTX *sslCtx = sec->getSslCtx();
      if(sslCtx)
      {
         rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
         // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
         sslCtx->cert_store = 0;
#else
         X509_STORE_up_ref(rootSSLCerts);
#endif
         if( rootSSLCerts )
            SSL_CTX_set_cert_store(ctx->native_handle(), rootSSLCerts);
      }
      delete sec;
   }

   switch( m_Settings.certMode )
   {
   case CertVerificationMode_Peer:
      ctx->set_verify_mode( boost::asio::ssl::verify_peer );
      break;
   case CertVerificationMode_Fail_If_No_Peer_Cert:
      ctx->set_verify_mode( boost::asio::ssl::verify_peer | boost::asio::ssl::verify_fail_if_no_peer_cert );
      break;
   case CertVerificationMode_Client_Once:
      ctx->set_verify_mode( boost::asio::ssl::verify_peer | boost::asio::ssl::verify_client_once );
      break;
   case CertVerificationMode_None:
   default:
      ctx->set_verify_mode( boost::asio::ssl::verify_none );
      break;
   }

   if( m_Settings.acceptedCertPublicKeys.size() > 0 || m_Settings.requiredCertPublicKeys.size() > 0 )
      ctx->set_verify_callback( std::bind( &WebSocketStateDispatcher::onVerifyPeerCallback, this, std::placeholders::_1, std::placeholders::_2 ));

   return ctx;
}

void WebSocketStateDispatcher::onTLSSocketInit( websocketpp::connection_hdl hWebSock, tlsStream& stream )
{
}
