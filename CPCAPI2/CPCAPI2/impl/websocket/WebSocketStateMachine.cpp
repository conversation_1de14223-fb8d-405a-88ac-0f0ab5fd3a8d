
#include <assert.h>
#include <algorithm>
#include <memory>

#include <rutil/Logger.hxx>

#include <websocket/WebSocketTypes.h>
#include "json/JsonDataWebSocketCommand.h"
#include "states/StateConnected.h"
#include "states/StateConnecting.h"
#include "states/StateDisconnected.h"
#include "states/StateFailed.h"
#include "states/StateSuspended.h"

#include "WebSocketState.h"
#include "WebSocketStateMachine.h"
#include "WebSocketStateDispatcher.h"
#include "DispatcherShim.h"

#include <writer.h>
#include <stringbuffer.h>

#include <iosfwd>
#include <iostream>

using namespace CPCAPI2::WebSocket;
using CPCAPI2::CPCAPI2_Subsystem;

WebSocketStateMachine::WebSocketStateMachine(CPCAPI2_Subsystem loggingSubsystem, boost::asio::io_service& io_service, const WebSocketSettings& settings, const std::string& webSocketName) :
   AbstractStateMachine( loggingSubsystem, webSocketName ),
   m_Settings(settings),
   m_Dispatcher(NULL),
   m_IsSuspendable(false)
{
   websocketpp::lib::error_code ec;

   m_Dispatcher.reset( new WebSocketStateDispatcher( this, settings ));

   // Create the dispatcher shim to protect us from the nasty websocketpp lib.
   // The shim itself has to be a shared pointer since it needs to live for the
   // entire time it's in the io_service.
   m_pShim = std::make_shared< DispatcherShim >(m_Dispatcher, loggingSubsystem, webSocketName);

   // Create/Replace all of the states to the state machine
   addState( WebSocketStatePtr( new StateDisconnected( this )));
   addState( WebSocketStatePtr( new StateConnecting( this )));
   addState( WebSocketStatePtr( new StateConnected( io_service, this )));
   addState( WebSocketStatePtr( new StateFailed( io_service, this )));
   addState( WebSocketStatePtr( new StateSuspended( this )));

   // Set the initial state on the state machine (disconnected)
   setCurrentState( STATE_DISCONNECTED_ID );

   // Initialize the TCP endpoint
   m_pShim->TCPEndpoint().clear_access_channels( websocketpp::log::alevel::all );
   m_pShim->TCPEndpoint().set_error_channels( websocketpp::log::elevel::all );
   m_pShim->TCPEndpoint().init_asio( &io_service, ec ); // NB: share the I/O service (swallow any non-errors, log all errors)

   // Install TCPEndpoint listeners
   m_pShim->TCPEndpoint().set_open_handler(websocketpp::lib::bind(
      &DispatcherShim::onOpen,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TCPEndpoint().set_close_handler(websocketpp::lib::bind(
      &DispatcherShim::onClose,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TCPEndpoint().set_fail_handler(websocketpp::lib::bind(
      &DispatcherShim::onFail,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TCPEndpoint().set_message_handler(websocketpp::lib::bind(
      &DispatcherShim::onTCPMessage,
      m_pShim,
      websocketpp::lib::placeholders::_1,
      websocketpp::lib::placeholders::_2
      ));

   m_pShim->TCPEndpoint().set_socket_init_handler(websocketpp::lib::bind(
      &DispatcherShim::onTCPSocketInit,
      m_pShim,
      websocketpp::lib::placeholders::_1,
      websocketpp::lib::placeholders::_2
      ));

   m_pShim->TCPEndpoint().set_pong_timeout_handler(websocketpp::lib::bind(
      &DispatcherShim::onPongTimeout,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   // Initialize the TLS endpoint
   m_pShim->TLSEndpoint().clear_access_channels(websocketpp::log::alevel::all);
   m_pShim->TLSEndpoint().set_error_channels(websocketpp::log::elevel::all);
   m_pShim->TLSEndpoint().init_asio( &io_service, ec ); // NB: share the I/O service (swallow any non-errors, log all errors)

   // Install TLSEndpoint listeners
   m_pShim->TLSEndpoint().set_open_handler(websocketpp::lib::bind(
      &DispatcherShim::onOpen,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TLSEndpoint().set_close_handler(websocketpp::lib::bind(
      &DispatcherShim::onClose,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TLSEndpoint().set_fail_handler(websocketpp::lib::bind(
      &DispatcherShim::onFail,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TLSEndpoint().set_message_handler(websocketpp::lib::bind(
      &DispatcherShim::onTLSMessage,
      m_pShim,
      websocketpp::lib::placeholders::_1,
      websocketpp::lib::placeholders::_2
      ));

   m_pShim->TLSEndpoint().set_tls_init_handler(websocketpp::lib::bind(
      &DispatcherShim::onTLSInit,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));

   m_pShim->TLSEndpoint().set_socket_init_handler(websocketpp::lib::bind(
      &DispatcherShim::onTLSSocketInit,
      m_pShim,
      websocketpp::lib::placeholders::_1,
      websocketpp::lib::placeholders::_2
      ));

   m_pShim->TLSEndpoint().set_pong_timeout_handler(websocketpp::lib::bind(
      &DispatcherShim::onPongTimeout,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));
      
   m_pShim->TLSEndpoint().set_ping_handler(websocketpp::lib::bind(
      &DispatcherShim::onPing,
      m_pShim,
      websocketpp::lib::placeholders::_1
      ));
}

WebSocketStateMachine::~WebSocketStateMachine()
{
   closeConnections( "WebSocketStateMachine Destroyed" );

   // Clear all the handlers from the TCP endpoint.
   // the "nice" way to do this is to use the C++11 feature "nullptr", which we can't use.
   m_pShim->TCPEndpoint().set_open_handler( websocketpp::open_handler() );
   m_pShim->TCPEndpoint().set_open_handshake_timeout( 0 );
   m_pShim->TCPEndpoint().set_close_handler( websocketpp::close_handler() );
   m_pShim->TCPEndpoint().set_close_handshake_timeout( 0 );
   m_pShim->TCPEndpoint().set_fail_handler( websocketpp::fail_handler() );
   m_pShim->TCPEndpoint().set_message_handler( tcpClient::message_handler() );
   m_pShim->TCPEndpoint().set_socket_init_handler( websocketpp::transport::asio::basic_socket::socket_init_handler() );
   m_pShim->TCPEndpoint().set_pong_timeout_handler( websocketpp::pong_timeout_handler() );

   // Clear all the handlers from the TLS endpoint.
   // the "nice" way to do this is to use the C++11 feature "nullptr", which we can't use.
   m_pShim->TLSEndpoint().set_open_handler( websocketpp::open_handler() );
   m_pShim->TLSEndpoint().set_open_handshake_timeout( 0 );
   m_pShim->TLSEndpoint().set_close_handler( websocketpp::close_handler() );
   m_pShim->TLSEndpoint().set_close_handshake_timeout( 0 );
   m_pShim->TLSEndpoint().set_fail_handler( websocketpp::fail_handler() );
   m_pShim->TLSEndpoint().set_message_handler( tlsClient::message_handler() );
   m_pShim->TLSEndpoint().set_tls_init_handler( websocketpp::transport::asio::tls_socket::tls_init_handler() );
   m_pShim->TLSEndpoint().set_socket_init_handler( websocketpp::transport::asio::tls_socket::socket_init_handler() );
   m_pShim->TLSEndpoint().set_pong_timeout_handler( websocketpp::pong_timeout_handler() );
   m_pShim->TLSEndpoint().set_ping_handler( websocketpp::ping_handler() );

   m_Dispatcher.reset();
}

void WebSocketStateMachine::fireLogin( void )
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for( iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter )
   {
      if( *iter == NULL )
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
         temp->onLogin();
   }
}

void WebSocketStateMachine::fireLoginResponse( const std::string& inDocument )
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for (iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter)
   {
      if (*iter == NULL)
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
         temp->onLoginResponse( inDocument );
   }
}

void WebSocketStateMachine::fireLogout( void )
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for( iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter )
   {
      if( *iter == NULL )
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
         temp->onLogout();
   }
}

void WebSocketStateMachine::fireMessage( const std::string& inDocument )
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for( iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter )
   {
      if( *iter == NULL )
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
         temp->onMessage( inDocument );
   }
}

void WebSocketStateMachine::firePing( websocketpp::connection_hdl hWebSock )
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for( iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter )
   {
      if( *iter == NULL )
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
         temp->onPing( hWebSock );
   }
}

// OK, I know having a return code ona fireXYZ method is not great, but the rule for this
// method is that if any of the listeners request a retry, we will do a retry (err in favor
// of retries). In reality I think there probably is usually only one listener here.
bool WebSocketStateMachine::fireOnReconnect( uint16_t code )
{
   bool result = false;
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for( iter = m_Listeners.begin() ; iter != m_Listeners.end() ; ++iter )
   {
      if( *iter == NULL )
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if( temp != NULL )
      {
         if( temp->onReconnect( code ))
         {
            result = true;
            break;
         }
      }
   }
   return result;
}

void WebSocketStateMachine::fireClose()
{
   std::list< AbstractStateMachineListener* >::const_iterator iter;
   for (iter = m_Listeners.begin(); iter != m_Listeners.end(); ++iter)
   {
      if (*iter == NULL)
         continue;

      WebSocketStateMachineListener *temp = dynamic_cast< WebSocketStateMachineListener* >( *iter );
      if (temp != NULL)
         temp->onClose();
   }
}

void WebSocketStateMachine::setSuspendable( bool isSuspendable )
{
   m_IsSuspendable = isSuspendable;

   // If we were previously suspended, restart the connection
   if( !m_IsSuspendable && strcmp( getCurrentStateID(), STATE_SUSPENDED_ID ) == 0 )
      setCurrentState( STATE_CONNECTING_ID );
}

// friend functions for the states

const websocketpp::connection_hdl WebSocketStateMachine::getActiveConnectionHandle()
{
   websocketpp::connection_hdl activeHandle;

   // Give TLS priority
   if( m_pShim->TLSCon() )
      activeHandle = m_pShim->TLSCon()->get_handle();
   else if( m_pShim->TCPCon() )
      activeHandle = m_pShim->TCPCon()->get_handle();

   return activeHandle;
}

bool WebSocketStateMachine::openConnection( const std::string& wsURL, websocketpp::lib::error_code& ec )
{
   // Open the right kind of connection depending on whether the URL is secure, or not.
   websocketpp::uri_ptr location = websocketpp::lib::make_shared< websocketpp::uri >( wsURL );
   if (!location->get_valid())
   {
      GenericLog(m_LoggingSubsystem, resip::Log::Err, << "WebSocketStateMachine(" << m_MachineName << ")::openConnection() Invalid URL: " << wsURL);
      return false; // invalid uri
   }

   if( location->get_secure() )
   {
      // Create the connection if needed.
      if( !m_pShim->TLSCon() )
      {
         m_pShim->TLSCon() = m_pShim->TLSEndpoint().get_connection( location, ec );
         if (ec)
         {
            GenericLog(m_LoggingSubsystem, resip::Log::Err, << "WebSocketStateMachine(" << m_MachineName << ")::openConnection() Error creating TLS connection: " << ec.message());
            return false;
         }
      }

      // Initiate a TLS connection
      m_pShim->TLSEndpoint().connect( m_pShim->TLSCon() );
   }
   else
   {
      // Create the connection if needed.
      if( !m_pShim->TCPCon() )
      {
         m_pShim->TCPCon() = m_pShim->TCPEndpoint().get_connection( location, ec );
         if( ec )
         {
            GenericLog(m_LoggingSubsystem, resip::Log::Err, << "WebSocketStateMachine(" << m_MachineName << ")::openConnection() Error creating TCP connection: " << ec.message());
            return false;
         }
      }

      // Initiate a TCP connection
      m_pShim->TCPEndpoint().connect( m_pShim->TCPCon() );
   }

   return true;
}

bool WebSocketStateMachine::sendCommand(
   const RequestHandle& hRequest,
   WebSocketCommand& command,
   std::string& outErrMessage,
   bool isLogCensored)
{
   std::string logIdent = m_MachineName + " request " + std::to_string(hRequest);
   const std::string commandLogIdent = command.getLogIdentifier();
   if (!commandLogIdent.empty())
   {
      logIdent += " " + commandLogIdent;
   }
   // DebugLog(<< "WebSocketStateMachine(" << logIdent << ")::sendCommand(): request: " << hRequest);

   // If we are in the suspended state fail the command attempt,
   // but start the state transition into connecting again. Treat
   // this as a request to reconnect.
   if ( strcmp( getCurrentStateID(), STATE_SUSPENDED_ID ) == 0 )
   {
      outErrMessage = "Suspended/Reconnecting";
      setCurrentState( STATE_CONNECTING_ID );
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "WebSocketStateMachine(" << logIdent << ")::sendCommand(): Problem while sending command with requestHandle: " << hRequest << " as connection is suspended or reconnecting");
      return false;
   }

   if( strcmp( getCurrentStateID(), STATE_CONNECTED_ID ) != 0 &&
       strcmp( getCurrentStateID(), STATE_CONNECTING_ID ) != 0 )
   {
      outErrMessage = "Invalid State";
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "WebSocketStateMachine(" << logIdent << ")::sendCommand(): Problem while sending command with requestHandle: " << hRequest << " due to invalid state");
      return false;
   }

   if ( m_Settings.logPayload )
   {
      // Log all outbound commands here. Inbound messages are logged in
      // the WebSocketStateDispatcher class.
      if( isLogCensored )
      {
         GenericLog( m_LoggingSubsystem, resip::Log::Info, << "\n"
            "========= Sending to websocket (" << logIdent << ") ======\n" <<
            "[ message omitted ]\n" <<
            "=====================================" );
      }
      else
      {
         GenericLog( m_LoggingSubsystem, resip::Log::Info, << "\n"
            "========= Sending to websocket (" << logIdent << ") ======\n" <<
            command.getLogString(hRequest) << "\n" <<
            "=====================================" );
      }
   }

   // Give priority to TLS
   websocketpp::lib::error_code err;
   if (m_pShim->TLSCon() && !command.sendMessage(hRequest, m_pShim->TLSCon()->get_handle(), m_pShim->TLSEndpoint(), err))
   {
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "WebSocketCommand(" << logIdent << ")::sendMessage(): Problem while sending over TLS: " << err.message().c_str());
      return false;
   }
   else if (m_pShim->TCPCon() && !command.sendMessage(hRequest, m_pShim->TCPCon()->get_handle(), m_pShim->TCPEndpoint(), err))
   {
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "WebSocketCommand(" << logIdent << ")::sendMessage(): Problem while sending over TCP: " << err.message().c_str());
      return false;
   }
   else if (!m_pShim->TLSCon() && !m_pShim->TCPCon())
   {
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "WebSocketCommand(" << logIdent << ")::sendMessage(): No open connction.");
      return false;
   }

   // Restart the ping timer here if we're in connected state
   std::shared_ptr< StateConnected > pConnectedState;
   pConnectedState = std::dynamic_pointer_cast< StateConnected >( m_CurrentState.lock() );
   if( pConnectedState.get() != NULL )
      pConnectedState->restartTimer();

   return true;
}

void WebSocketStateMachine::closeConnections( const std::string& reason )
{
   if( m_pShim->TCPCon() )
   {
      m_pShim->TCPCon()->set_close_handshake_timeout( 0 );

      websocketpp::lib::error_code ec;
      if( m_pShim->TCPCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TCPCon()->close( websocketpp::close::status::normal, reason, ec );

      m_pShim->TCPCon().reset();
   }

   if( m_pShim->TLSCon() )
   {
      m_pShim->TLSCon()->set_close_handshake_timeout( 0 );

      websocketpp::lib::error_code ec;
      if( m_pShim->TLSCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TLSCon()->close( websocketpp::close::status::normal, reason, ec );

      m_pShim->TLSCon().reset();
   }
}

void WebSocketStateMachine::sendPing( websocketpp::connection_hdl hWebSock )
{
   // Give priority to TLS
   if( m_pShim->TLSCon() )
   {
      websocketpp::lib::error_code ec;
      if( m_pShim->TLSCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TLSEndpoint().ping( hWebSock, "", ec );
   }
   else if( m_pShim->TCPCon() )
   {
      websocketpp::lib::error_code ec;
      if( m_pShim->TCPCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TCPEndpoint().ping( hWebSock, "", ec );
   }

   // Restart the ping timer here if we're in connected state
   std::shared_ptr< StateConnected > pConnectedState;
   pConnectedState = std::dynamic_pointer_cast< StateConnected >( m_CurrentState.lock() );
   if( pConnectedState.get() != NULL )
      pConnectedState->restartTimer();
}

void WebSocketStateMachine::sendPing()
{
   // Give priority to TLS
   if( m_pShim->TLSCon() )
   {
      websocketpp::lib::error_code ec;
      if( m_pShim->TLSCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TLSEndpoint().ping( m_pShim->TLSCon()->get_handle(), "", ec );
   }
   else if( m_pShim->TCPCon() )
   {
      websocketpp::lib::error_code ec;
      if( m_pShim->TCPCon()->get_state() == websocketpp::session::state::open )
         m_pShim->TCPEndpoint().ping( m_pShim->TCPCon()->get_handle(), "", ec );
   }

   // Restart the ping timer here if we're in connected state
   std::shared_ptr< StateConnected > pConnectedState;
   pConnectedState = std::dynamic_pointer_cast< StateConnected >( m_CurrentState.lock() );
   if( pConnectedState.get() != NULL )
      pConnectedState->restartTimer();
}

namespace CPCAPI2
{

namespace WebSocket
{

cpc::string get_debug_string(const CertVerificationMode& mode)
{
   switch (mode)
   {
      case WebSocket::CertVerificationMode_None: return "none";
      case WebSocket::CertVerificationMode_Peer: return "peer";
      case WebSocket::CertVerificationMode_Fail_If_No_Peer_Cert: return "fail_if_no_peer_cert";
      case WebSocket::CertVerificationMode_Client_Once: return "client_once";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const WebSocketSettings& settings)
{
   std::stringstream ss;
   ss << "webSocketURL: " << settings.webSocketURL << " pingIntervalSeconds: " << settings.pingIntervalSeconds << " initialRetryIntervalSeconds: " << settings.initialRetryIntervalSeconds << " maxRetryIntervalSeconds: " << settings.maxRetryIntervalSeconds << " certMode: " << settings.certMode << " logPayload: " << settings.logPayload << " backgroundSocketsIfPossible: " << settings.backgroundSocketsIfPossible << " isLoginRequired: " << settings.isLoginRequired;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CertVerificationMode& mode)
{
   os << get_debug_string(mode);
   return os;
}

std::ostream& operator<<(std::ostream& os, const WebSocketSettings& settings)
{
   os << get_debug_string(settings);
   return os;
}

}

}
