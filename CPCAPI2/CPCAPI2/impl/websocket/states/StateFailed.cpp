#include "StateFailed.h"

#include <algorithm>
#include <random>
#include <assert.h>

#include <websocket/WebSocketTypes.h>

#include "StateDisconnected.h"
#include "StateConnecting.h"
#include "StateConnected.h"
#include "TimerShim.h"

#include "../WebSocketStateMachine.h"

using namespace CPCAPI2::WebSocket;

static const char *s_nextIDs[] = {
   STATE_DISCONNECTED_ID,
   STATE_CONNECTING_ID,
   NULL
};

StateFailed::StateFailed( boost::asio::io_service& io_service, WebSocketStateMachine* pSM )
   : m_pStateMachine( pSM ), m_Timer( io_service ), m_ExpiryTime( 2 )
{
   resetExpiryTime();
}

void StateFailed::getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs )
{
   if( outNextIDs == NULL || outNumIDs == NULL )
      return;

   *outNextIDs = s_nextIDs;
   *outNumIDs  = ( sizeof( s_nextIDs ) / sizeof( const char * )) - 1;
}

void StateFailed::enter( const char *prevStateID )
{
   assert( m_pStateMachine != NULL );
   if( m_pStateMachine == NULL )
      return;

   // Close the websocket connection and prepare to reconnect at a later time.
   m_pStateMachine->closeConnections( "Failed" );

   if( strcmp( prevStateID, STATE_CONNECTED_ID ) == 0 )
      resetExpiryTime();
   else
      doubleExpiryTime();

   mWakeLockHandle = BackgroundManager::Instance()->acquireWakeLock();

   m_Timer.expires_from_now( m_ExpiryTime );
   TimerShim *shim = new TimerShim( shared_from_this() ); // deleted after firing
   m_Timer.async_wait( std::bind( &TimerShim::onTimer, shim, m_pStateMachine->getActiveConnectionHandle(), std::placeholders::_1 ));
}

void StateFailed::leave( const char *nextStateID )
{
   // Cancel any active timers upon leaving the state
   m_Timer.cancel();

   // If we are going to StateDisconnected, reset the expiry time.
   if( strcmp( nextStateID, STATE_DISCONNECTED_ID ) == 0 )
      resetExpiryTime();
}

void StateFailed::onTimer( websocketpp::connection_hdl hWebSock, const boost::system::error_code& e )
{
   if( m_pStateMachine == NULL )
      return;

   BackgroundManager::Instance()->releaseWakeLock(mWakeLockHandle);
   mWakeLockHandle = 0;

   // When this timer fires, attempt another connection by transitioning into
   // the connecting state (but only if the timer wasn't already cancelled)
   if( !e )
      m_pStateMachine->setCurrentState( STATE_CONNECTING_ID );
}

void StateFailed::resetExpiryTime( void )
{
   // Set expiry time to initial (random) value
   std::default_random_engine generator;
   std::uniform_int_distribution<int> distribution( 1, m_pStateMachine->m_Settings.initialRetryIntervalSeconds );
   m_ExpiryTime = boost::posix_time::seconds( distribution( generator ));
}

void StateFailed::doubleExpiryTime( void )
{
   // Double the current value of the timer (with a maximum of maxRetryIntervalSeconds ).
   m_ExpiryTime *= 2;
   m_ExpiryTime = std::min< boost::posix_time::seconds >( m_ExpiryTime,
      boost::posix_time::seconds( m_pStateMachine->m_Settings.maxRetryIntervalSeconds ));
}
