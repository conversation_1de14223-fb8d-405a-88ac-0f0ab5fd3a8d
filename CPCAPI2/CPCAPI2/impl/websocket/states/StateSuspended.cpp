#include <assert.h>

#include "StateSuspended.h"
#include "StateDisconnected.h"
#include "StateConnecting.h"

#include "../WebSocketStateMachine.h"

using namespace CPCAPI2::WebSocket;

static const char *s_nextIDs[] = {
   STATE_DISCONNECTED_ID,
   STATE_CONNECTING_ID,
   NULL
};

void StateSuspended::getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs )
{
   if( outNextIDs == NULL || outNumIDs == NULL )
      return;

   *outNextIDs = s_nextIDs;
   *outNumIDs  = ( sizeof( s_nextIDs ) / sizeof( const char * )) - 1;
}

void StateSuspended::enter( const char *prevStateID )
{
   // Upon arriving into the Unregistered state, ensure that any open
   // websocket connections are closed
   assert( m_pStateMachine != NULL  );
   if( m_pStateMachine == NULL )
      return;

   m_pStateMachine->closeConnections( "Suspended" );
}
