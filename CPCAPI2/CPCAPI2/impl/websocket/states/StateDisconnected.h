#pragma once
#ifndef __STATEDISCONNECTED_H__
#define __STATEDISCONNECTED_H__

#include "../WebSocketState.h"

#define STATE_DISCONNECTED_ID "{D88C2825-0450-4B8C-A554-E358204F0843}"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;

      class StateDisconnected : public WebSocket::WebSocketStateStub
      {
      public:
         StateDisconnected( WebSocketStateMachine* pSM )
            : m_pStateMachine( pSM ), m_bCloseCalled( false ) {}
         const char *getName() const OVERRIDE { return "StateDisconnected"; }
         const char *getUniqueID() const OVERRIDE { return STATE_DISCONNECTED_ID; }
         void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) OVERRIDE;
         void enter( const char * prevStateID ) OVERRIDE;
         bool closeCalled() { return m_bCloseCalled; }

         void onClose(uint16_t code, const std::string& reason) OVERRIDE;

      private:
         WebSocketStateMachine *m_pStateMachine; // not owned
         bool m_bCloseCalled;
      };
   }
}

#endif /* __STATEDISCONNECTED_H__ */
