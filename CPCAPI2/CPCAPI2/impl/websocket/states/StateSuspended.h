#pragma once
#ifndef __STATESUSPENDED_H__
#define __STATESUSPENDED_H__

#include "../WebSocketState.h"

#define STATE_SUSPENDED_ID "{F54789AD-1178-476E-B332-DDB26751DCB5}"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;

      class StateSuspended : public WebSocket::WebSocketStateStub
      {
      public:
         StateSuspended( WebSocketStateMachine* pSM )
            : m_pStateMachine( pSM ) {}
         const char *getName() const OVERRIDE { return "StateSuspended"; }
         const char *getUniqueID() const OVERRIDE { return STATE_SUSPENDED_ID; }
         void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) OVERRIDE;
         void enter( const char * prevStateID ) OVERRIDE;

      private:
         WebSocketStateMachine *m_pStateMachine; // not owned
      };
   }
}

#endif /* __STATESUSPENDED_H__ */