#include <assert.h>

#include "StateDisconnected.h"
#include "StateConnecting.h"

#include "../WebSocketStateMachine.h"

using namespace CPCAPI2::WebSocket;

static const char *s_nextIDs[] = {
   STATE_CONNECTING_ID,
   NULL
};

void StateDisconnected::getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs )
{
   if( outNextIDs == NULL || outNumIDs == NULL )
      return;

   *outNextIDs = s_nextIDs;
   *outNumIDs  = ( sizeof( s_nextIDs ) / sizeof( const char * )) - 1;
}

void StateDisconnected::enter( const char *prevStateID )
{
   // Upon arriving into the Disconnected state, ensure that any open
   // websocket connections are closed
   assert( m_pStateMachine != NULL  );
   if( m_pStateMachine == NULL )
      return;

   m_bCloseCalled = false;

   m_pStateMachine->closeConnections( "Disconnected" );
}

void StateDisconnected::onClose(uint16_t code, const std::string& reason)
{
   m_bCloseCalled = true;
   m_pStateMachine->fireClose();
}
