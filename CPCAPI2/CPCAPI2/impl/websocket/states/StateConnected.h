#pragma once
#ifndef __STATECONNECTED_H__
#define __STATECONNECTED_H__

#include <string>
#include <memory>
#include <boost/asio.hpp>

#include <util/statemachine/AbstractState.h>
#include <websocketpp/client.hpp>

#include "../WebSocketState.h"

#define STATE_CONNECTED_ID "{7AD452B3-62C5-47E4-B409-7BE23877D4A5}"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;

      class StateConnected :
         public WebSocket::WebSocketStateStub,
         public std::enable_shared_from_this< StateConnected >
      {
      public:
         StateConnected( boost::asio::io_service& io_service, WebSocketStateMachine* pSM )
            : m_pStateMachine( pSM ), m_Timer( io_service ), m_ExpiryTime( 0 ) {}
         virtual ~StateConnected() { m_Timer.cancel(); }

         const char *getName() const OVERRIDE { return "StateConnected"; }
         const char *getUniqueID() const OVERRIDE { return STATE_CONNECTED_ID; }
         void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) OVERRIDE;
         void enter( const char *prevStateID ) OVERRIDE;
         void leave( const char *nextStateID ) OVERRIDE;

         void onClose( uint16_t code, const std::string& reason ) OVERRIDE;
         void onFail( uint16_t code, const std::string& reason ) OVERRIDE;
         void onMessage( const std::string& message ) OVERRIDE;

         void restartTimer();
         void onTimer( websocketpp::connection_hdl hWebSock, const boost::system::error_code& e ) OVERRIDE;

      private:
         WebSocketStateMachine *m_pStateMachine;
         boost::asio::deadline_timer m_Timer;
         boost::posix_time::seconds m_ExpiryTime;
      };
   }
}

#endif /* __STATECONNECTED_H__ */