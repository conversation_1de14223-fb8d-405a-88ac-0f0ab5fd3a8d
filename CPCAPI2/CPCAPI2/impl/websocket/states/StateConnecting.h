#pragma once
#ifndef __STATECONNECTING_H__
#define __STATECONNECTING_H__

#include "../WebSocketState.h"

#define STATE_CONNECTING_ID "{4950DD63-8BB0-4A7D-BC66-7013FE453563}"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;

      class StateConnecting : public WebSocket::WebSocketStateStub
      {
      public:
         StateConnecting( WebSocketStateMachine* pSM )
            : m_pStateMachine( pSM ) {}

         const char *getName() const OVERRIDE { return "StateConnecting"; }
         const char *getUniqueID() const OVERRIDE { return STATE_CONNECTING_ID; }
         void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) OVERRIDE;
         void enter( const char *prevStateID ) OVERRIDE;

         void onOpen( void ) OVERRIDE;
         void onClose( uint16_t code, const std::string& reason ) OVERRIDE;
         void onFail( uint16_t code, const std::string& reason ) OVERRIDE;
         void onMessage( const std::string& message ) OVERRIDE;

      private:
         WebSocketStateMachine *m_pStateMachine; // not owned
      };
   }
}

#endif /* __STATECONNECTING_H__ */