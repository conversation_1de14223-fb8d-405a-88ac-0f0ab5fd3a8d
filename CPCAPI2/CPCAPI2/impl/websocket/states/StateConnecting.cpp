#include "StateConnecting.h"

#include <assert.h>
#include <cpcapi2defs.h>

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "StateConnected.h"
#include "StateFailed.h"
#include "StateDisconnected.h"
#include "StateSuspended.h"

#include "../WebSocketStateMachine.h"

using namespace CPCAPI2::WebSocket;

static const char *s_nextIDs[] = {
   STATE_CONNECTED_ID,
   STATE_FAILED_ID,
   STATE_DISCONNECTED_ID,
   NULL
};

void StateConnecting::getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs )
{
   if( outNextIDs == NULL || outNumIDs == NULL )
      return;

   *outNextIDs = s_nextIDs;
   *outNumIDs  = ( sizeof( s_nextIDs ) / sizeof( const char * )) - 1;
}

void StateConnecting::enter( const char* prevStateID )
{
   assert( m_pStateMachine != NULL );
   if (m_pStateMachine == NULL)
   {
      GenericLog(m_pStateMachine->m_LoggingSubsystem, resip::Log::Err, << "StateConnecting(" << m_pStateMachine->m_MachineName << ")::enter() State machine is NULL");
      return;
   }

   if( strcmp( prevStateID, STATE_DISCONNECTED_ID ) == 0 ||
       strcmp( prevStateID, STATE_FAILED_ID ) == 0 ||
       strcmp( prevStateID, STATE_SUSPENDED_ID ) == 0 )
   {
      // Account has been enabled by the user, or else the connection is in
      // process of being retried automatically. Initiate a new connection.
      websocketpp::lib::error_code ec;
      if( !m_pStateMachine->openConnection( m_pStateMachine->m_Settings.webSocketURL.c_str(), ec ))
      {
         GenericLog( m_pStateMachine->m_LoggingSubsystem, resip::Log::Warning,
            << "StateConnecting(" << m_pStateMachine->m_MachineName << ")::enter() misconfiguration, connection attempt failed (no retry)");
      }
   }
}

void StateConnecting::onOpen( void )
{
   assert( m_pStateMachine != NULL );
   if( m_pStateMachine == NULL )
      return;

   if( m_pStateMachine->m_Settings.isLoginRequired )
   {
      // Connection succeeded, perform a login
      GenericLog( m_pStateMachine->m_LoggingSubsystem, resip::Log::Debug,
         << "StateConnecting(" << m_pStateMachine->m_MachineName << ")::onOpen() connected to service, starting LOGIN" );
      m_pStateMachine->fireLogin();
   }
   else
   {
      // No login required, proceed to connected state.
      m_pStateMachine->setCurrentState( STATE_CONNECTED_ID );
   }
}

void StateConnecting::onClose( uint16_t code, const std::string& reason )
{
   // If the connection fails to be established, check the settings.
   GenericLog( m_pStateMachine->m_LoggingSubsystem, resip::Log::Debug,
      << "StateConnecting(" << m_pStateMachine->m_MachineName << ")::onClose() Socket was closed" );

   // Treat closure the same as a failure
   onFail( code, reason );
}

void StateConnecting::onFail( uint16_t code, const std::string& reason )
{
   assert( m_pStateMachine != NULL );

   // Consult the various modules as to what they want to do with this code.
   // the choices are, reconnect (state failed) or disconnect
   const char *NEXT_STATE = m_pStateMachine->fireOnReconnect( code ) ?
      STATE_FAILED_ID : STATE_DISCONNECTED_ID;

   m_pStateMachine->setCurrentState( NEXT_STATE, code );
}

void StateConnecting::onMessage( const std::string& message )
{
   assert( m_pStateMachine != NULL );
   m_pStateMachine->fireLoginResponse( message );
}
