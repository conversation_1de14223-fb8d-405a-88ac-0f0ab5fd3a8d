#pragma once
#ifndef __STATEFAILED_H__
#define __STATEFAILED_H__

#include <boost/asio.hpp>

#include <memory>
#include "../WebSocketState.h"
#include "../../phone/BackgroundManager.h"

#define STATE_FAILED_ID "{8163C3FD-7043-4BCB-B224-8F77589F3E5E}"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketStateMachine;

      class StateFailed :
         public WebSocketStateStub,
         public std::enable_shared_from_this< StateFailed >
      {
      public:
         StateFailed( boost::asio::io_service& io_service, WebSocketStateMachine* pSM );
         virtual ~StateFailed() { m_Timer.cancel(); }

         const char *getName() const OVERRIDE { return "StateFailed"; }
         const char *getUniqueID() const OVERRIDE { return STATE_FAILED_ID; }
         void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) OVERRIDE;
         void enter( const char* prevStateID ) OVERRIDE;
         void leave( const char* nextStateID ) OVERRIDE;
         void onTimer( websocketpp::connection_hdl hWebSock, const boost::system::error_code& e ) OVERRIDE;

         void resetExpiryTime( void );
         void doubleExpiryTime( void );

      private:
         WebSocketStateMachine       *m_pStateMachine; // not owned
         boost::asio::deadline_timer  m_Timer;
         boost::posix_time::seconds   m_ExpiryTime;
         WakeLockHandle               mWakeLockHandle = 0;
      };
   }
}

#endif /* __STATEWAITING_H__ */
