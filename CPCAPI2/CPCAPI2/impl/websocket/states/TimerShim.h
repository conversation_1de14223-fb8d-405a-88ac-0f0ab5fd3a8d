#pragma once
#ifndef __TIMERSHIM_H__
#define __TIMERSHIM_H__

#include <boost/asio.hpp>
#include <memory>

#include <websocketpp/client.hpp>
#include "../WebSocketState.h"

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * The purpose of this class is to act as a "glue" between the asio
       * io_service and the class which is processing timer events.
       * Unfortunately due to the nature of asio io_service, we cannot
       * guarantee that timers will not fire after their containing classes are
       * deleted (even when the timer is cancelled, in fact that will result in
       * a callback).
       *
       * The solution is to insert a shim which holds a weak reference to the
       * instance of the class in question which needs to be called.  IF the
       * timer is fired after the parent class is deleted, it will harmlessly
       * do nothing and then be removed from the io_service afterwards.
       */
      class TimerShim
      {
      public:
         TimerShim( WebSocketStatePtr pOwningClass )
         {
            m_OwningClass = pOwningClass;
         }

         virtual ~TimerShim() {}

         void onTimer( websocketpp::connection_hdl hWebSock, const boost::system::error_code& e )
         {
            WebSocketStatePtr pOwningClass( m_OwningClass.lock() );
            if( pOwningClass.get() )
            {
               pOwningClass->onTimer( hWebSock, e );
            }

            // shim deletes itself after firing. Notice that this happens even when
            // the timer is cancelled.
            delete this;
         }

      private:
         WebSocketStateWPtr m_OwningClass;
      };
   }
}

#endif // __TIMERSHIM_H__
