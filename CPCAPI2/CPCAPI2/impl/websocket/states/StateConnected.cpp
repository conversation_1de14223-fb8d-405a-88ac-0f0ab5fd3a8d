#include "StateConnected.h"

#include <assert.h>

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "StateFailed.h"
#include "StateDisconnected.h"
#include "StateSuspended.h"
#include "TimerShim.h"

#include "../WebSocketStateMachine.h"

using namespace CPCAPI2::WebSocket;

static const char *s_nextIDs[] = {
   STATE_FAILED_ID,
   STATE_DISCONNECTED_ID,
   STATE_SUSPENDED_ID,
   NULL
};

void StateConnected::getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs )
{
   if( outNextIDs == NULL || outNumIDs == NULL )
      return;

   *outNextIDs = s_nextIDs;
   *outNumIDs  = ( sizeof( s_nextIDs ) / sizeof( const char * )) - 1;
}

void StateConnected::enter( const char* prevStateID )
{
   if (m_pStateMachine->m_Settings.sendImmediatePing)
   {
      // Force a ping to be sent immediately upon connecting, because some applications
      // require the delta timestamp (remotesync) immediately. This is what happens when
      // you overload two purposes onto a single protocol message :-)
      const websocketpp::connection_hdl hWebSock(m_pStateMachine->getActiveConnectionHandle());
      m_pStateMachine->firePing(hWebSock);
   }

   // Reset the ping timer (which probably is already done by firePing, but, just to
   // be safe we'll restart it again)
   restartTimer();
}

void StateConnected::leave( const char* nextStateID )
{
   // Send an unsubscribe on our way out
   assert( m_pStateMachine != NULL );
   if( m_pStateMachine == NULL )
      return;

   if( m_pStateMachine->m_Settings.isLoginRequired )
      m_pStateMachine->fireLogout();

   // Cancel any active timers upon leaving the state
   m_Timer.cancel();
}

void StateConnected::onClose( uint16_t code, const std::string& reason )
{
   // Treat closure the same as failure
   GenericLog( m_pStateMachine->m_LoggingSubsystem, resip::Log::Debug,
      << "StateConnected(" << m_pStateMachine->m_MachineName << ")::onClose() Socket Closed (calling onFail)" );
   onFail( code, reason );
}

void StateConnected::onMessage( const std::string& message )
{
   assert( m_pStateMachine != NULL );
   // Parse any incoming notifications
   m_pStateMachine->fireMessage( message );
}

void StateConnected::onFail( uint16_t code, const std::string& reason )
{
   GenericLog( m_pStateMachine->m_LoggingSubsystem, resip::Log::Debug,
      << "StateConnected(" << m_pStateMachine->m_MachineName << ")::onFail() Connection Failure" );

   if( m_pStateMachine == NULL )
      return;

   // If there is a connection failure while in registered state,
   // transition to failed state
   m_pStateMachine->setCurrentState( STATE_FAILED_ID, code);
}

void StateConnected::onTimer( 
   websocketpp::connection_hdl hWebSock, const boost::system::error_code& e )
{
   if (e != boost::asio::error::operation_aborted)
   {
      // Timer was not cancelled, take necessary action.
      if( m_pStateMachine == NULL )
         return;

      // When the timer fires, check the preferences around suspending
      if( m_pStateMachine->isSuspendable() )
      {
         // Move into the suspended state
         m_pStateMachine->setCurrentState( STATE_SUSPENDED_ID );
      }
      else
      {
         // Send a ping message to the server in order to
         // prevent the socket(s) from closing (this method causes
         // restartTimer to be invoked again).
         m_pStateMachine->firePing( hWebSock );
      }
   }
}

void StateConnected::restartTimer()
{
   if( m_pStateMachine == NULL )
      return;

   const int& interval( m_pStateMachine->m_Settings.pingIntervalSeconds );
   if( interval > 0 )
   {
      TimerShim *shim = new TimerShim( shared_from_this() ); // deleted by dtor

      // restart the timer for the next ping
      m_ExpiryTime = boost::posix_time::seconds( interval );
      m_Timer.expires_from_now( m_ExpiryTime );
      m_Timer.async_wait( std::bind( &TimerShim::onTimer, shim, m_pStateMachine->getActiveConnectionHandle(), std::placeholders::_1 ));
   }
}
