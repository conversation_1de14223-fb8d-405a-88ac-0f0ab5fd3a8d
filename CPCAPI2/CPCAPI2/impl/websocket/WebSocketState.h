#pragma once
#ifndef __CPCAPI2_WEBSOCKET_WEBSOCKETSTATE_H__
#define __CPCAPI2_WEBSOCKET_WEBSOCKETSTATE_H__

#include <memory>

#include <cpcapi2defs.h>

#include <boost/asio.hpp>

#include <websocketpp/client.hpp>
#include <cpcstl/string.h>
#include <util/statemachine/AbstractState.h>

namespace CPCAPI2
{
   namespace WebSocket
   {
      /**
       * The purpose of this interface is to describe what each state in the
       * state machine is able to do.
       */
      class WebSocketState : public AbstractState
      {
      public:
         
         WebSocketState() : AbstractState() {}
         virtual ~WebSocketState() {}
         
         /**
          * Called when the websocket connection is established (opened)
          */
         virtual void onOpen( void ) = 0;

         /**
          * Called when the websocket connection is explicitly closed, either
          * locally or remotely.
          *
          * @param code the "close code" as specified by websocketpp
          * @param reason the "reason" as passed by websocketpp for the close
          */
         virtual void onClose( uint16_t code, const std::string& reason ) = 0;

         /**
          * Called when the websocket connection is broken, or otherwise when
          * the connection was attempted and failed to be established.
          *
          * @param code the "close code" as specified by websocketpp
          * @param reason the "reason" as passed by websocketpp for the close
          */
         virtual void onFail( uint16_t code, const std::string& reason ) = 0;

         /**
          * Called when a message is received from the remote side over the
          * websocket.
          */
         virtual void onMessage( const std::string& message ) = 0;

         /**
          * Some states have timers which need to be processed.
          */
         virtual void onTimer(
            websocketpp::connection_hdl hWebSock,
            const boost::system::error_code& e ) = 0;
      };
      typedef std::shared_ptr< WebSocketState > WebSocketStatePtr;
      typedef std::weak_ptr< WebSocketState > WebSocketStateWPtr;

      /**
       * Stub which overrides all optional methods, except for the name and ID
       */
      class WebSocketStateStub : public WebSocketState
      {
      public:
         virtual ~WebSocketStateStub() {}
         void enter( const char* prevStateID ) OVERRIDE {}
         void leave( const char* nextStateID ) OVERRIDE {}
         void onOpen( void ) OVERRIDE {}
         void onClose( uint16_t code, const std::string& reason ) OVERRIDE {}
         void onFail( uint16_t code, const std::string& reason )  OVERRIDE {}
         void onMessage( const std::string& message ) OVERRIDE {}
         void onTimer( websocketpp::connection_hdl hWebSock,
            const boost::system::error_code& e ) OVERRIDE {}
      };
   }
}

#endif // __CPCAPI2_WEBSOCKET_WEBSOCKETSTATE_H__
