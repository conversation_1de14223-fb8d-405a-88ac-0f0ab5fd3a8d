#pragma once
#ifndef __CPCAPI2_WEBSOCKETCOMMONTYPES_H__
#define __CPCAPI2_WEBSOCKETCOMMONTYPES_H__

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>

namespace CPCAPI2
{
   namespace WebSocket
   {
      typedef int64_t RequestHandle;

      // Websocket typedefs (tcp versions)
      typedef websocketpp::client<websocketpp::config::asio_client> tcpClient;
      typedef websocketpp::config::asio_client::message_type::ptr tcpMessagePtr;
      typedef boost::asio::ip::tcp::socket tcpStream;
      typedef tcpClient::connection_ptr tcpConnectionPtr;

      // Websocket typedefs (tls versions)
      typedef websocketpp::client<websocketpp::config::asio_tls_client> tlsClient;
      typedef websocketpp::config::asio_tls_client::message_type::ptr tlsMessagePtr;
      typedef boost::asio::ssl::stream< boost::asio::ip::tcp::socket > tlsStream;
      typedef websocketpp::lib::shared_ptr<boost::asio::ssl::context> tlsContext;
      typedef tlsClient::connection_ptr tlsConnectionPtr;
   }
}

#endif //  __CPCAPI2_WEBSOCKETCOMMONTYPES_H__