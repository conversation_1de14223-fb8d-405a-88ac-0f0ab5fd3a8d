#pragma once
#ifndef __CPCAPI2_WEBSOCKETHANDLER_H__
#define __CPCAPI2_WEBSOCKETHANDLER_H__

#include <cpcapi2defs.h>
#include "WebSocketCommonTypes.h"

namespace CPCAPI2
{
   namespace WebSocket
   {
      // Forward decl's
      class TCPWebSocketHandler;
      class TLSWebSocketHandler;

      /**
       * Base interface containing common TCP and TLS handler functions.
       */
      class WebSocketHandler
      {
      public:
         virtual ~WebSocketHandler() {}
         
         /**
          * Called by websocketpp when the connection is established to the
          * server
          */
         virtual void onOpen( websocketpp::connection_hdl hWebSock ) = 0;

         /**
          * Called by websocketpp when the connection is closed to the server
          */
         virtual void onClose( websocketpp::connection_hdl hWebSock ) = 0;

         /**
          * Called by websocketpp to indicate that a previous connection has
          * failed
          */
         virtual void onFail( websocketpp::connection_hdl hWebSock ) = 0;
      };

      class WebSocketHandlerStub : public WebSocketHandler
      {
      public:
         virtual void onOpen( websocketpp::connection_hdl hWebSock ) OVERRIDE {};
         virtual void onClose( websocketpp::connection_hdl hWebSock ) OVERRIDE {};
         virtual void onFail( websocketpp::connection_hdl hWebSock ) OVERRIDE {};
      };

      /**
       * TCP-specific mix-in interface for handling web socket callbacks.
       */
      class TCPWebSocketHandler
      {
      public:
         /**
          * Called by websocketpp in order to process incoming messages
          */
         virtual void onTCPMessage( websocketpp::connection_hdl hWebSock, tcpMessagePtr msg ) = 0;

         /**
          * Called by websocketpp when the socket is being created. Some
          * platforms need additional handling (iOS) to the sockets in order to
          * prevent backgrounding problems.
          */
         virtual void onTCPSocketInit( websocketpp::connection_hdl hWebSock, tcpStream& stream ) = 0;
      };

      class TCPWebSocketHandlerStub : public TCPWebSocketHandler
      {
      public:
         virtual void onTCPMessage( websocketpp::connection_hdl hWebSock, tcpMessagePtr msg ) OVERRIDE {};
         virtual void onTCPSocketInit( websocketpp::connection_hdl hWebSock, tcpStream& stream ) OVERRIDE {};
      };

      /**
       * TLS-specific mix-in interface for handling web socket callbacks.
       */
      class TLSWebSocketHandler
      {
      public:
         /**
          * Called by websocketpp in order to process incoming messages
          */
         virtual void onTLSMessage( websocketpp::connection_hdl hWebSock, tlsMessagePtr msg ) = 0;

         /**
          * Called by websocketpp before the connection is established, while
          * setting up TLS. This should be used for reading certs from the cert
          * store.
          */
         virtual tlsContext onTLSInit( websocketpp::connection_hdl hWebSock ) = 0;

         /**
          * Called by websocketpp when the socket is being created. Some
          * platforms need additional handling (iOS) to the sockets in order to
          * prevent backgrounding problems.
          */
         virtual void onTLSSocketInit( websocketpp::connection_hdl hWebSock, tlsStream& stream ) = 0;
      };

      class TLSWebSocketHandlerStub : public TLSWebSocketHandler
      {
      public:
         virtual void onTLSMessage( websocketpp::connection_hdl hWebSock, tlsMessagePtr msg ) OVERRIDE {};
         virtual tlsContext onTLSInit( websocketpp::connection_hdl hWebSock ) OVERRIDE { tlsContext ctx; return ctx; };
         virtual void onTLSSocketInit( websocketpp::connection_hdl hWebSock, tlsStream& stream ) OVERRIDE {};
      };
   }
}

#endif // __CPCAPI2_WEBSOCKETHANDLERSTUB_H__