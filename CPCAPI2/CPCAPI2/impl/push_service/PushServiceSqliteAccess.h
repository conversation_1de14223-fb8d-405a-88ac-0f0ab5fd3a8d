#pragma once

#if !defined(CPCAPI2_PUSH_SERVICE_SQLITE_ACCESS)
#define CPCAPI2_PUSH_SERVICE_SQLITE_ACCESS

#include "PushServiceRedisAccessIf.h"

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <rutil/MultiReactor.hxx>

#include <functional>
#include <thread>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace soci
{
   class session;
}

namespace CPCAPI2
{
namespace PushService
{
class SqliteAccess : public RedisAccessBase
{
public:
   SqliteAccess();
   virtual ~SqliteAccess();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) OVERRIDE;
   virtual void shutdown() OVERRIDE;

   virtual int addPushRegistration(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb) OVERRIDE;
   virtual int queryPushRegistration(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb) OVERRIDE;

   virtual int flushAll() OVERRIDE;
   virtual int setHandler(RedisConnectionHandler* handler) OVERRIDE;

private:

   void initializeImpl(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb);
   void addPushRegistrationImpl(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb);
   void queryPushRegistrationImpl(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb);
   void flushAllImpl();

   void createTables();
   void dump();

private:

   resip::MultiReactor mReactor;
   std::unique_ptr<soci::session> mSOCI;
   int64_t mPushRegTimeoutIntervalMs;
   RedisConnectionHandler* mHandler;

};

}

}

#endif // CPCAPI2_PUSH_SERVICE_SQLITE_ACCESS
