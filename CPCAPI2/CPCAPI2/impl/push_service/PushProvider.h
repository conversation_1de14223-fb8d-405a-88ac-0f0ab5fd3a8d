#pragma once

#if !defined(CPCAPI2_PUSH_PROVIDER_H)
#define CPCAPI2_PUSH_PROVIDER_H

#include <cpcapi2defs.h>

#include "push_endpoint/PushNotificationCommonTypes.h"
#include "push_service/PushNotificationServiceTypes.h"

#include <memory>

namespace CPCAPI2
{

namespace PushService
{

   /**
    * Public interface which is implemented by different push network
    * implementations (APN, FCM, etc).
   */
   class PushProvider
   {

   public:

      PushProvider() {}
      virtual ~PushProvider() {}

      virtual CPCAPI2::PushEndpoint::PushNetworkType getNetworkType() const = 0;
      virtual void init(const PushProviderSettings& settings) = 0;

      /**
       * Sends the document to the push server (which will in turn send it
       * to the devices. Note that the doc argument may be modified before
       * sending (to add important fields).
      */
      virtual void sendPushNotification(
         CPCAPI2::PushEndpoint::PushNotificationEndpointId device,
         CPCAPI2::PushService::PushNotificationServiceHandle service,
         const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info,
         const PushNotificationRequest& request ) = 0;

   };

   typedef std::shared_ptr<PushProvider> PushProviderPtr;

}

}

#endif // CPCAPI2_PUSH_PROVIDER_H
