#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "impl/jsonapi/JsonApiServerSendTransportInternal.h"
#include "json/JsonSerialize.h"
#include "push_service/PushNotificationServiceInterface.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "PushNetworkManager_WS.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "ApplePushUtils.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <fstream>
#include <sstream>

#include <time.h>

#include <sys/types.h>
#include <sys/stat.h>
#ifndef WIN32
#include <unistd.h>
#endif

#ifdef WIN32
#define stat _stat
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
//using CPCAPI2::HTTPClient;

PushNetworkManager_WS::PushNetworkManager_WS(CPCAPI2::Phone *phone, PushNotificationServiceInterface* pushMgrIf) :
PushProvider(),
mPushMgrIf(pushMgrIf)
{
   m_Transport = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone));
}

PushNetworkManager_WS::~PushNetworkManager_WS()
{
}

PushNetworkType PushNetworkManager_WS::getNetworkType() const
{
   return PushNetworkType::PushNetworkType_WS;
}

void PushNetworkManager_WS::init(const PushProviderSettings& settings)
{
}

bool PushNetworkManager_WS::buildJSON(const cpc::string& deviceToken, const PushNotificationRequest & request, std::string & outJson)
{
   if (deviceToken.empty())
   {
      return false;
   }

   rapidjson::StringBuffer buffer;
   rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
   writer.StartObject();

   // Add the target destination
   Json::Write(writer, "to", deviceToken);

   // Add the notification specific keys (only if body is present)
   if (request.body.size() > 0 )
   {
      Json::WriteObject(writer, "notification",  "body", request.body, "title-loc-key", request.title_loc_key, "title-loc-args", request.title_loc_args);
   }
   else
   {
      // APN requires the following key to indicate an invisible (background) update
      bool contentAvailable = true;
      Json::Write(writer, "content_available", contentAvailable);
   }

   // FCM spec says custom data must be under "data"
   // TODO: keynames should be enforced to be different from reserved keynames
   writer.Key("data");

   // data object
   writer.StartObject();
   for (CustomData::const_iterator iter = request.customData.begin(); iter != request.customData.end(); ++iter)
   {
      switch (iter->dataType)
      {
      case CustomDataType_bool:
         Json::Write(writer, iter->name, iter->boolVal);
         break;
      case CustomDataType_int:
         Json::Write(writer, iter->name, iter->intVal);
         break;
     case CustomDataType_uint:
        Json::Write(writer, iter->name, static_cast<uint64_t>(iter->intVal));
        break;
      case CustomDataType_double:
         Json::Write(writer, iter->name, iter->doubleVal);
         break;
      case CustomDataType_string:
         Json::Write(writer, iter->name, iter->stringVal);
         break;
      }
   }
   writer.EndObject();
   // end data object

   writer.EndObject();
   outJson = buffer.GetString();
   return true;
}

void PushNetworkManager_WS::sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const CPCAPI2::PushService::PushNotificationRequest& request)
{
   mPushMgrIf->postPushFuncToSdkThread(resip::resip_bind(&PushNetworkManager_WS::sendPushNotificationImpl, this, device, service, info, request));
}

void PushNetworkManager_WS::sendPushNotificationImpl(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const CPCAPI2::PushService::PushNotificationRequest& request)
{
   std::string body;
   if (!buildJSON(info.deviceToken, request, body))
   {
      InfoLog(<< "PushNetworkManager_WS::sendPushNotification(): failed to build JSON document");
      NotificationFailureEvent evt;
      evt.device = device;
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationFailure), service, evt);
      return;
   }

   InfoLog(<< "PushNetworkManager_WS::sendPushNotification(device=" << info.deviceToken.c_str() << ", body=" << body << ")");

   // Use the json connection id to send the message directly using the
   // internal transport.  It is necessary to do this because we need to send the
   // message on a DIFFERENT Json connection.

   rapidjson::Document notificationDoc;
   notificationDoc.SetObject();
   notificationDoc.AddMember("moduleId", "PushNotificationEndpointManagerJsonProxy", notificationDoc.GetAllocator());

   rapidjson::Value functionObject(rapidjson::kObjectType);
   functionObject.AddMember("functionName", "onPushNotification", notificationDoc.GetAllocator());
   rapidjson::Value devVal(device.c_str(), notificationDoc.GetAllocator());
   functionObject.AddMember("device", devVal, notificationDoc.GetAllocator());

   rapidjson::Value eventVal( body.c_str(), notificationDoc.GetAllocator());
   functionObject.AddMember("jsonDocument", eventVal, notificationDoc.GetAllocator());
   notificationDoc.AddMember("functionObject", functionObject, notificationDoc.GetAllocator());

   m_Transport->send(info.websocketInfo.jsonUserHandle, notificationDoc);

   NotificationSuccessEvent nse;
   nse.device = device;
   mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationSuccess), service, nse);
}

#endif
