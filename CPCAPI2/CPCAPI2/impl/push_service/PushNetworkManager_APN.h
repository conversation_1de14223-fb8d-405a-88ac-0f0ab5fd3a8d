#pragma once

#if !defined(CPCAPI2_PUSH_NETWORK_MANAGER_APN_H)
#define CPCAPI2_PUSH_NETWORK_MANAGER_APN_H

#include "util/HttpClient.h"

#include "rutil/Data.hxx"
#include "PushProvider.h"

#include <string>
#include <vector>

namespace CPCAPI2
{

class LocalLogger;

namespace PushService
{

class PushNotificationServiceInterface;

class PushNetworkManager_APN : public PushProvider
{

public:

   PushNetworkManager_APN(CPCAPI2::PushService::PushNotificationServiceInterface* pushMgrIf);
   virtual ~PushNetworkManager_APN();

   CPCAPI2::PushEndpoint::PushNetworkType getNetworkType(void) const OVERRIDE;
   void init(const PushProviderSettings& settings) OVERRIDE;
   void sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const PushNotificationRequest& request) OVERRIDE;

private:

   bool buildJSON(const PushNotificationRequest& request, std::string& outJson);
   resip::Data encodeAuthToken( const resip::Data& p8file, const resip::Data& authKeyId, const resip::Data& teamId );

private:

   PushProviderSettings m_Settings;
   CPCAPI2::PushService::PushNotificationServiceInterface* mPushMgrIf;
   CPCAPI2::HTTPClient mProdHttpClient;
   CPCAPI2::HTTPClient mSandboxHttpClient;
   time_t mProviderAuthTokenValiditySeconds;
   bool mHaveProdHttpSession;
   bool mHaveSandboxHttpSession;
   LocalLogger* mLocalLogger;

};

}

}


#endif // CPCAPI2_PUSH_NETWORK_MANAGER_APN_H
