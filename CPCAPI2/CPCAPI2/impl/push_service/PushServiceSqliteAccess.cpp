#include "brand_branded.h"

#if ((CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1) || (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1))

#include "PushServiceSqliteAccess.h"

#include <webrtc/system_wrappers/interface/clock.h>

#include <soci/soci.h>
#include <soci/sqlite3/soci-sqlite3.h>

#include <future>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace soci;

namespace CPCAPI2
{

namespace PushService
{

SqliteAccess::SqliteAccess() :
mReactor("PushServiceSqliteAccess"),
mPushRegTimeoutIntervalMs(3628800000), // 6 weeks
mHandler(NULL)
{
   mReactor.start();
}

SqliteAccess::~SqliteAccess()
{
}

void SqliteAccess::createTables()
{
   (*mSOCI) <<
      "CREATE TABLE IF NOT EXISTS PushRegistration ( "
      " id INTEGER PRIMARY KEY, "
      " userDeviceId TEXT UNIQUE, "
      " lastUpdateNtpTimeMs INT, "
      " pushNetworkType INT, "
      " deviceToken TEXT, "
      " apnsTopic TEXT, "
      " useSandbox INT, "
      " jsonUserHandle INT, "
      " apnsTopicPushKit TEXT, "
      " pushKitToken TEXT"
      " )";
}

int SqliteAccess::initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   mReactor.post(resip::resip_bind(&SqliteAccess::initializeImpl, this, serverConfig, initCb));
   return 0;
}

void SqliteAccess::initializeImpl(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   bool success = true;
   cpc::string statusDesc = "OK";
   RedisConnectionStatus connectionStatus = RedisConnectionStatus_Connected;

   try
   {
      mPushRegTimeoutIntervalMs = serverConfig.pushRegTimeoutIntervalMs;

      mSOCI.reset(new soci::session());
      mSOCI->open(soci::sqlite3, (std::string)serverConfig.ip);

      // std::ostream* os = new std::ostream(NULL);
      // os->rdbuf(std::cout.rdbuf()); // uses cout's buffer
      // mSOCI->set_log_stream(os);

      mSOCI->begin();

      if (serverConfig.port == 9999)
      {
         flushAllImpl();
      }
      createTables();

      mSOCI->commit();
   }
   catch (soci::soci_error&)
   {
      success = false;
      statusDesc = "Error";
      connectionStatus = RedisConnectionStatus_ConnFailure;
      mSOCI->close();
   }

   if (mHandler)
   {
      RedisConnectionStatusEvent args;
      args.connectionStatus = connectionStatus;
      args.statusDesc = statusDesc;
      mHandler->onRedisConnectionStatusChanged(args);
   }
}

void SqliteAccess::shutdown()
{
   mReactor.stop();
   mHandler = NULL;
}

int SqliteAccess::addPushRegistration(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb)
{
   mReactor.post(resip::resip_bind(&SqliteAccess::addPushRegistrationImpl, this, regInfo, regAddedCb));
   return 0;
}

void SqliteAccess::addPushRegistrationImpl(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb)
{
   int64_t lastUpdateTimeMs = webrtc::Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();
   if (regInfo.userPlusDeviceIdentity.size() > 1)
   {
      {
         const std::string userPlusDeviceIdentity(regInfo.userPlusDeviceIdentity.c_str());
         const std::string deviceToken(regInfo.deviceToken.c_str());
         const std::string apnsTopic(regInfo.apnsTopic.c_str());
         const std::string apnsTopicPushKit(regInfo.apnsTopicPushKit.c_str());
         const std::string pushKitToken(regInfo.pushKitToken.c_str());

         // warning: don't use temporary variables for inserting.
         // from http://soci.sourceforge.net/doc/3.2/exchange.html
         // Object lifetime and immutability:
         //
         // SOCI assumes that local variables provided as use elements live at least as long at it takes to execute the whole statement.
         // In short statement forms like above, the statement is executed sometime at the end of the full expression and the whole process
         // is driven by the invisible temporary object handled by the library. If the data provided by user comes from another temporary
         // variable, it might be possible for the compiler to arrange them in a way that the user data will be destroyed before the statement
         // will have its chance to execute, referencing objects that no longer exist:

         (*mSOCI) << "INSERT OR REPLACE INTO PushRegistration(userDeviceId, lastUpdateNtpTimeMs, pushNetworkType, deviceToken, apnsTopic, useSandbox, jsonUserHandle, apnsTopicPushKit, pushKitToken) VALUES(:userDeviceId, :lastUpdateNtpTimeMs, :pushNetworkType, :deviceToken, :apnsTopic, :useSandbox, :jsonUserHandle, :apnsTopicPushKit, :pushKitToken)",
            use(userPlusDeviceIdentity),
            use(lastUpdateTimeMs),
            use(regInfo.pushNetworkType),
            use(deviceToken),
            use(apnsTopic),
            use((unsigned int)regInfo.useSandbox),
            use(regInfo.jsonUserHandle),
            use(apnsTopicPushKit),
            use(pushKitToken);

         AddPushRegistrationResult res;
         res.endpointId = regInfo.userPlusDeviceIdentity;
         res.result = 0;
         regAddedCb(true, res);
      }
      return;
   }

   StackLog(<< "SqliteAccess::addPushRegistrationImpl(): error inserting data: " << regInfo);

   AddPushRegistrationResult res;
   res.result = -1;
   regAddedCb(true, res);
}

int SqliteAccess::queryPushRegistration(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb)
{
   mReactor.post(resip::resip_bind(&SqliteAccess::queryPushRegistrationImpl, this, serviceRequest, queryPushRegsCb));
   return 0;
}

void SqliteAccess::queryPushRegistrationImpl(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb)
{
   QueryPushRegistrationResult res;
   res.result = -1;

   soci::indicator isqlid;
   soci::indicator iluntm;
   soci::indicator ipnt;
   soci::indicator idevtoken;
   soci::indicator iapnstopic;
   soci::indicator iusesandbox;
   soci::indicator ijsonuserhandle;
   soci::indicator iapnstopicpushkit;
   soci::indicator ipushkittoken;
   int64_t sqlid = -1;
   int64_t lastUpdateTimeMs = -1;
   int pushNetworkTypeRes = -1;
   std::string deviceTokenRes("");
   std::string apnsTopicRes("");
   int64_t useSandbox( 0 );
   std::string userDeviceId(serviceRequest.endpointId.c_str());
   unsigned int jsonUserHandleRes = 0;
   std::string apnsTopicPushKitRes("");
   std::string pushKitTokenRes("");

   (*mSOCI) << "SELECT id, lastUpdateNtpTimeMs, pushNetworkType, deviceToken, apnsTopic, useSandbox, jsonUserHandle, apnsTopicPushKit, pushKitToken FROM PushRegistration WHERE userDeviceId = :userDeviceId",
      into(sqlid, isqlid),
      into(lastUpdateTimeMs, iluntm),
      into(pushNetworkTypeRes, ipnt),
      into(deviceTokenRes, idevtoken),
      into(apnsTopicRes, iapnstopic),
      into(useSandbox, iusesandbox), // ?
      into(jsonUserHandleRes, ijsonuserhandle),
      into(apnsTopicPushKitRes, iapnstopicpushkit),
      into(pushKitTokenRes, ipushkittoken),
      use(userDeviceId, "userDeviceId");

   if (mSOCI->got_data())
   {
      if (isqlid == soci::i_ok && iluntm == soci::i_ok && ipnt == soci::i_ok && idevtoken == soci::i_ok)
      {
         int64_t nowNtpMs = webrtc::Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();
         if ((nowNtpMs - lastUpdateTimeMs) < mPushRegTimeoutIntervalMs)
         {
            res.result = 0;
            res.pushNetworkType = pushNetworkTypeRes;
            res.deviceToken = deviceTokenRes.c_str();
            res.apnsTopic = apnsTopicRes.c_str();
            res.useSandbox = useSandbox;
            res.jsonUserHandle = jsonUserHandleRes;
            res.apnsTopicPushKit = apnsTopicPushKitRes.c_str();
            res.pushKitToken = pushKitTokenRes.c_str();

            DebugLog(<< "SqliteAccess::queryPushRegistrationImpl(): " << res);
         }
         else
         {
            (*mSOCI) << "DELETE FROM PushRegistration WHERE id = :id",
               use(sqlid, "id");
         }
      }
   }
   else
   {
      DebugLog(<< "SqliteAccess::queryPushRegistrationImpl(): query failed: " << mSOCI->get_last_query() << " for attributes: " << serviceRequest);
   }

   queryPushRegsCb(true, res);

}

int SqliteAccess::flushAll()
{
   mReactor.post(resip::resip_bind(&SqliteAccess::flushAllImpl, this));
   return 0;
}

void SqliteAccess::flushAllImpl()
{
   (*mSOCI) << "DROP TABLE IF EXISTS PushRegistration";
}

int SqliteAccess::setHandler(RedisConnectionHandler* handler)
{
   DebugLog(<< "PushServiceSqliteAccess::setHandler(): mHandler: " << mHandler << " will be updated to: handler: " << handler);
   mHandler = handler;
   return kSuccess;
}

void SqliteAccess::dump()
{
   // int count;
   // (*mSOCI) << "SELECT count(*) FROM PushRegistration", into(count);

   soci::rowset<soci::row> rows = ((*mSOCI).prepare << "SELECT * FROM PushRegistration");

   int count(0);
   std::stringstream dump;
   dump << "<PushRegistration>" << std::endl;
   for (soci::rowset<soci::row>::const_iterator it = rows.begin(); it != rows.end(); ++it, ++count)
   {
      dump << "<row>" << std::endl;
      soci::row const& row = (*it);
      for (std::size_t i = 0; i != row.size(); ++i)
      {
         const column_properties& props = row.get_properties(i);

         dump << '<' << props.get_name() << '>';

         switch(props.get_data_type())
         {
            case dt_string: dump << row.get<std::string>(i); break;
            case dt_double: dump << row.get<double>(i); break;
            case dt_integer: dump << row.get<int>(i); break;
            case dt_long_long: dump << row.get<long long>(i); break;
            case dt_unsigned_long_long: dump << row.get<unsigned long long>(i); break;
            case dt_date: { std::tm when = row.get<std::tm>(i); dump << asctime(&when); break; }
            default: break;
         }

         dump << "</" << props.get_name() << '>' << std::endl;
      }
      dump << "</row>" << std::endl;
   }
   dump << "</PushRegistration>";

   StackLog(<< "SqliteAccess::dump(): table rows: " << count << std::endl << dump.str() << std::endl);
}

}

}

#endif
