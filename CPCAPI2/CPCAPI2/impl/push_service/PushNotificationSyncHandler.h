#pragma once
#ifndef __CPCAPI2_PUSHNOTIFICATIONSYNCHANDLER_H__
#define __CPCAPI2_PUSHNOTIFICATIONSYNCHANDLER_H__

namespace resip
{
   struct ReadCallbackBase;
}

namespace CPCAPI2
{
namespace PushNotification
{
// "marker" interface for executing callbacks on same thread
class PushNotificationClientSyncHandler
{
};

// "marker" interface for executing callbacks on a different thread
class PushNotificationClientAsyncHandler
{
public:
 virtual ~PushNotificationClientAsyncHandler() {}
 virtual void onEvent(resip::ReadCallbackBase* rcb) = 0;
};

// "marker" interface for executing callbacks on same thread
class PushNotificationSyncHandler
{
};
}
}

#endif // __CPCAPI2_PUSHNOTIFICATIONSYNCHANDLER_H__