#include "brand_branded.h"

#include "interface/experimental/push_service/PushNotificationServiceManager.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
#include "PushNotificationServiceInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

namespace PushService
{

   PushNotificationServiceManager* PushNotificationServiceManager::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<PushNotificationServiceInterface>(phone, "PushNotificationServiceInterface");
#else
      return NULL;
#endif
   }

}

}
