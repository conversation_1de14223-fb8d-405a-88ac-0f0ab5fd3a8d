#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)

#include "json/JsonSerialize.h"
#include "push_service/PushNotificationServiceInterface.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "PushNetworkManager_FCM.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "ApplePushUtils.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <fstream>
#include <sstream>

#include <time.h>

#include <sys/types.h>
#include <sys/stat.h>
#ifndef WIN32
#include <unistd.h>
#endif

#ifdef WIN32
#define stat _stat
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2::PushService;
using namespace CPCAPI2::PushEndpoint;
using CPCAPI2::HTTPClient;

PushNetworkManager_FCM::PushNetworkManager_FCM(PushNotificationServiceInterface* pushMgrIf) :
PushProvider(),
mPushMgrIf(pushMgrIf),
mHaveHttpSession(false)
{
   DebugLog(<< "PushNetworkManager_FCM()");
}

PushNetworkManager_FCM::~PushNetworkManager_FCM()
{
}

PushNetworkType PushNetworkManager_FCM::getNetworkType() const
{
   return PushNetworkType::PushNetworkType_FCM;
}

void PushNetworkManager_FCM::init(const PushProviderSettings& settings)
{
   StackLog(<< "PushNetworkManager_FCM::init(): fcmUrl: " << settings.fcmSettings.fcmUrl.c_str() << " fcmKey: " << settings.fcmSettings.fcmKey.c_str());
   m_Settings = settings;
}

bool PushNetworkManager_FCM::buildJSON(const cpc::string& deviceToken, const PushNotificationRequest & request, std::string & outJson)
{
   if (deviceToken.empty())
   {
      return false;
   }

   rapidjson::StringBuffer buffer;
   rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
   writer.StartObject();

   // Add the target destination
   Json::Write(writer, "to", deviceToken);

   // Add the notification specific keys (only if body is present)
   if (request.body.size() > 0)
   {
      Json::WriteObject(writer, "notification",  "body", request.body, "title-loc-key", request.title_loc_key, "title-loc-args", request.title_loc_args);
      // Including the collapse_key member, ends up making the android app treat it like data, and it get's processed by the app
      // notificationObject.AddMember("collapse_key", deviceToken.c_str(), doc.GetAllocator());
   }
   else
   {
      // APN requires the following key to indicate an invisible (background) update
      bool contentAvailable = true;
      Json::Write(writer, "content_available", contentAvailable);

      // It seems if the "notification" attribute is not present, it also wakes up the app from background
      // Adding an empty notification body.
      // rapidjson::Value notificationObject( rapidjson::kObjectType );
      // doc.AddMember("notification", notificationObject, doc.GetAllocator());
   }

   // FCM spec says custom data must be under "data"
   // TODO: keynames should be enforced to be different from reserved keynames
   writer.Key("data");
   writer.StartObject(); // Start data object
   for (CustomData::const_iterator iter = request.customData.begin(); iter != request.customData.end(); ++iter)
   {
      switch (iter->dataType)
      {
      case CustomDataType_bool:
         Json::Write(writer, iter->name.c_str(), iter->boolVal);
         break;
      case CustomDataType_int:
         Json::Write(writer, iter->name, iter->intVal);
         break;
      case CustomDataType_uint:
         Json::Write(writer, iter->name, static_cast<uint64_t>(iter->intVal));
         break;
      case CustomDataType_double:
         Json::Write(writer, iter->name, iter->doubleVal);
         break;
      case CustomDataType_string:
         Json::Write(writer, iter->name, iter->stringVal);
         break;
      }
   }
   writer.EndObject(); // End data object

   writer.EndObject();
   outJson = buffer.GetString();
   return true;
}

void PushNetworkManager_FCM::sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const PushNotificationRegistrationInfo& info, const PushNotificationRequest& request)
{
   std::string body;
   if(!buildJSON(info.deviceToken, request, body))
   {
      InfoLog(<< "PushNetworkManager_FCM::sendPushNotification(): failed to build JSON document");
      NotificationFailureEvent evt;
      evt.device = device;
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationFailure), service, evt);
      return;
   }

   InfoLog(<< "PushNetworkManager_FCM::sendPushNotification(device=" << info.deviceToken.c_str() << ", body=" << body << ")");

   // Build config using a combination of static information plus what is in the settings
   HTTPClient::RequestConfig config;
   config.verb                    = CPCAPI2::HTTPClient::EHTTPVerbPOST;
   config.mimeType                = "application/json";
   config.username                = "";
   config.password                = "";
   config.clientCertificate       = "";
   config.clientCertificatePasswd = "";
   config.useEmbeddedCert1        = false;
   config.useEmbeddedCert2        = false;
   config.ignoreCertErrors        = false;
   config.enableCookies           = false;
   config.cookieFile              = "";
   config.verboseLogging          = true;
   config.userAgent               = "CounterPath-HTTP";
   config.useHttp2                = false;

   CPCAPI2::HTTPClient::ResponseResult result;

   // prepend "key=" on the front
   std::string apiKey = m_Settings.fcmSettings.fcmKey.c_str();
   apiKey = "key=" + apiKey;

   // Fetch the URL from settings
   std::string serverUrl = m_Settings.fcmSettings.fcmUrl.c_str();
   if( serverUrl.size() == 0 )
      return;

   config.messageBody = body.c_str();
   config.messageLengthInBytes = body.size();
   config.customHeaders.push_back({ "Authorization", apiKey.c_str() });
      
   StackLog(<< "PushNetworkManager_FCM::sendPushNotification(): http data: " << config);
   if (mHaveHttpSession)
   {
      InfoLog(<< "PushNetworkManager_FCM::sendPushNotification(): sending session push request to " << serverUrl);
      mHttpClient.DoSessionRequest(serverUrl.c_str(), config, result);
   }
   else
   {
      InfoLog(<< "PushNetworkManager_FCM::sendPushNotification(): sending initial push request to " << serverUrl);
      mHaveHttpSession = true;
      mHttpClient.StartHTTPSession(serverUrl.c_str(), config, result);
   }

   InfoLog(<< "PushNetworkManager_FCM::sendPushNotification(): push request result: notification service handle: " << service << " status=" << result.status << ", errorCode=" << result.errorCode << ", messageBody=" << result.messageBody);
   NotificationSuccessEvent evt;
   evt.device = device;
   mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationSuccess), service, evt);
}

#endif
