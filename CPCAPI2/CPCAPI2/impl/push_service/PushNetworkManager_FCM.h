#pragma once

#if !defined(CPCAPI2_PUSH_NETWORK_MANAGER_FCM_H)
#define CPCAPI2_PUSH_NETWORK_MANAGER_FCM_H

#include "util/HttpClient.h"

#include "rutil/Data.hxx"
#include "PushProvider.h"

#include <string>
#include <vector>

namespace CPCAPI2
{

namespace PushService
{

class PushNotificationServiceInterface;

class PushNetworkManager_FCM : public PushProvider
{

public:

   PushNetworkManager_FCM(PushNotificationServiceInterface* pushMgrIf);
   virtual ~PushNetworkManager_FCM();

   CPCAPI2::PushEndpoint::PushNetworkType getNetworkType() const OVERRIDE;
   void init(const PushProviderSettings& settings) OVERRIDE;
   void sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const PushNotificationRequest& request) OVERRIDE;

private:

   bool buildJSON(const cpc::string& deviceToken, const PushNotificationRequest& request, std::string& outJson);

private:

   PushProviderSettings m_Settings;
   PushNotificationServiceInterface* mPushMgrIf;
   CPCAPI2::HTTPClient mHttpClient;
   bool mHaveHttpSession;

};

}

}

#endif // CPCAPI2_PUSH_NETWORK_MANAGER_FCM_H
