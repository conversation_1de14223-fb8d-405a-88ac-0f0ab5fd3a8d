#include "brand_branded.h"

#if ((CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1) || (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1))

#include "PushServiceRedisAccess.h"

#include <rutil/Data.hxx>

#include <future>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WATCHDOG

#define CPCAPI2_PushService_RedisPrefix(_str) std::string("cpcapi2::pushservice::") + _str

namespace CPCAPI2
{

namespace PushService
{

RedisAccess::RedisAccess() :
mRedisClient(new redisclient::RedisAsyncClient(mIOS)),
mPushRegTimeoutIntervalMs(3628800000), // 6 weeks
mHandler(NULL)
{
   // StackLog(<< "PushServiceRedisAccess(): " << this);
}

RedisAccess::~RedisAccess()
{
   // StackLog(<< "~PushServiceRedisAccess(): " << this);
}

int RedisAccess::initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   DebugLog(<< "PushServiceRedisAccess::initialize(): serverConfig: " << serverConfig);
   if (mClientThread.get() != NULL)
   {
      return 0;
   }

   mPushRegTimeoutIntervalMs = serverConfig.pushRegTimeoutIntervalMs;

   mClientThread.reset(new std::thread([this, serverConfig, initCb]()
   {
      boost::asio::ip::address address = boost::asio::ip::address::from_string(serverConfig.ip.c_str());
      const unsigned short port = serverConfig.port;
      boost::asio::ip::tcp::endpoint endpoint(address, port);

      try
      {
         mRedisClient->connect(endpoint, std::bind(&RedisAccess::handleConnected, this, initCb, std::placeholders::_1, std::placeholders::_2));
         mIOS.run();
         DebugLog(<< "PushServiceRedisAccess::initialize(): run completed");
      }
      catch (std::exception& e)
      {
         WarningLog(<< "PushServiceRedisAccess::initialize(): exception: " << e.what());
         if (mHandler)
         {
            RedisConnectionStatusEvent args;
            args.connectionStatus = RedisConnectionStatus_ConnFailure;
            args.statusDesc = e.what();
            mHandler->onRedisConnectionStatusChanged(args);
         }
      }
   }));
      
   return 0;
}

void RedisAccess::shutdown()
{
   InfoLog(<< "PushServiceRedisAccess::shutdown()");
   
   if (mClientThread.get() == NULL)
   {
      return;
   }

   mIOS.post([this]() {
      mRedisClient->disconnect();
   });

   mClientThread->join();
   mRedisClient.reset();
   mIOS.stop();
   mClientThread.reset();
   
   mHandler = NULL;
}

int RedisAccess::addPushRegistration(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "PushServiceRedisAccess::addPushRegistration(): redis client not connected");
      AddPushRegistrationResult rerr;
      rerr.result = -1;
      regAddedCb(true, rerr);
      return 0;
   }
   
   int64_t pushRegTimeoutMs = mPushRegTimeoutIntervalMs;
   mRedisClient->command("HMSET", { CPCAPI2_PushService_RedisPrefix(regInfo.userPlusDeviceIdentity.c_str()),
      "pushNetworkType", resip::Data::from(regInfo.pushNetworkType).c_str(), 
      "deviceToken", regInfo.deviceToken.c_str(), 
      "apnsTopic", regInfo.apnsTopic.c_str(),
      "useSandbox", resip::Data::from(regInfo.useSandbox).c_str(),
      "jsonUserHandle", resip::Data::from(regInfo.jsonUserHandle).c_str(),
      "apnsTopicPushKit", regInfo.apnsTopicPushKit.c_str(),
      "pushKitToken", regInfo.pushKitToken.c_str() }, [&, regInfo, regAddedCb, pushRegTimeoutMs](const redisclient::RedisValue& vadd)
   {
      if (vadd.isOk())
      {
         AddPushRegistrationResult res;
         res.endpointId = regInfo.userPlusDeviceIdentity;
         res.result = 0;
         regAddedCb(true, res);
         mRedisClient->command("PEXPIRE", { CPCAPI2_PushService_RedisPrefix(regInfo.userPlusDeviceIdentity.c_str()), resip::Data::from(pushRegTimeoutMs).c_str() }, [](const redisclient::RedisValue&) {
         });
      }
   });
   return 0;
}

int RedisAccess::queryPushRegistration(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "PushServiceRedisAccess::queryPushRegistration(): redis client not connected");
      QueryPushRegistrationResult rerr;
      rerr.result = -1;
      queryPushRegsCb(true, rerr);
      return 0;
   }

   mRedisClient->command("HMGET", { CPCAPI2_PushService_RedisPrefix(serviceRequest.endpointId.c_str()), "pushNetworkType", "deviceToken", "apnsTopic", "useSandbox", "jsonUserHandle", "apnsTopicPushKit", "pushKitToken" }, [queryPushRegsCb](const redisclient::RedisValue& v)
   {
      if (v.isOk() && v.isArray())
      {
         std::vector<redisclient::RedisValue> resultArray = v.toArray();
         QueryPushRegistrationResult res;
         res.result = 0;
         res.pushNetworkType = resip::Data(resultArray[0].inspect()).convertInt();
         res.deviceToken = resultArray[1].toString().c_str();
         res.apnsTopic = resultArray[2].toString().c_str();
         res.useSandbox = (resip::Data(resultArray[3].inspect()).convertInt()) != 0;
         res.jsonUserHandle = (unsigned int)resip::Data(resultArray[4].toString().c_str()).convertInt();
         res.apnsTopicPushKit = resultArray[5].toString().c_str();
         res.pushKitToken = resultArray[6].toString().c_str();
         queryPushRegsCb(true, res);
      }
   });
   return 0;
}

int RedisAccess::flushAll()
{
   DebugLog(<< "PushServiceRedisAccess::flushAll()");
   std::promise<void> p1;
   std::future<void> f1 = p1.get_future();
   mRedisClient->command("FLUSHALL", { }, [&p1](const redisclient::RedisValue& v) {
      p1.set_value();
   });
   f1.wait();
   return 0;
}

int RedisAccess::setHandler(RedisConnectionHandler* handler)
{
   DebugLog(<< "PushServiceRedisAccess::setHandler(): mHandler: " << mHandler << " will be updated to: handler: " << handler);
   mHandler = handler;
   return 0;
}
   
void RedisAccess::handleConnected(const std::function<void(bool, const cpc::string&)>& initCb, bool success, const std::string& msg)
{
   InfoLog(<< "PushServiceRedisAccess::handleConnected(): success: " << success << " status: " << msg);
   
   if (mHandler)
   {
      RedisConnectionStatusEvent args;
      (success ? args.connectionStatus = RedisConnectionStatus_Connected : args.connectionStatus = RedisConnectionStatus_ConnFailure);
      args.statusDesc = msg.c_str();
      mHandler->onRedisConnectionStatusChanged(args);
   }
}
   
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessConfig& args)
{
   std::stringstream ss;
   ss << " RedisAccessConfig: ip: " << args.ip.c_str() << " port: " << args.port << " pushRegTimeoutIntervalMs: " << args.pushRegTimeoutIntervalMs;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationArgs& args)
{
   std::stringstream ss;
   ss << " AddPushRegistrationArgs: pushNetworkType: " << args.pushNetworkType << " deviceToken: " << args.deviceToken
      << " apnsTopic: " << args.apnsTopic << " apnsTopicPushKit: " << args.apnsTopicPushKit << " pushKitToken: " << args.pushKitToken
      << " useSandbox: " << args.useSandbox << " jsonUserHandle: " << args.jsonUserHandle
      << " userPlusDeviceIdentity: " << args.userPlusDeviceIdentity;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationResult& args)
{
   std::stringstream ss;
   ss << " AddPushRegistrationResult: result: " << args.result << " endpointId: " << args.endpointId;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationArgs& args)
{
   std::stringstream ss;
   ss << " QueryPushRegistrationArgs: endpointId: " << args.endpointId;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationResult& args)
{
   std::stringstream ss;
   ss << " QueryPushRegistrationResult: result: " << args.result << " pushNetworkType: " << args.pushNetworkType << " deviceToken: " << args.deviceToken
      << " apnsTopic: " << args.apnsTopic << " apnsTopicPushKit: " << args.apnsTopicPushKit << " pushKitToken: " << args.pushKitToken
      << " useSandbox: " << args.useSandbox << " jsonUserHandle: " << args.jsonUserHandle;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushService::RedisConnectionStatus& args)
{
   switch (args)
   {
      case RedisConnectionStatus_Connected: return "connected";
      case RedisConnectionStatus_ConnFailure: return "failure";
      default: return "disconnected";
   }
   
   return "disconnected";
}
   
cpc::string get_debug_string(const CPCAPI2::PushService::RedisConnectionStatusEvent& args)
{
   std::stringstream ss;
   ss << " RedisConnectionStatusEvent: status: " << args.connectionStatus << " description: " << args.statusDesc;
   return ss.str().c_str();
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessConfig& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationArgs& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationResult& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationArgs& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationResult& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisConnectionStatus& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisConnectionStatusEvent& args)
{
   os << CPCAPI2::PushService::get_debug_string(args);
   return os;
}
   
}

}

#endif
