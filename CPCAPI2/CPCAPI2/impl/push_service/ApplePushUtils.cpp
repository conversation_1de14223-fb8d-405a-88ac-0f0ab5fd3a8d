#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)

#include "ApplePushUtils.h"

#include <rutil/DataStream.hxx>

#include <fstream>
#include <sstream>
#include <openssl/ec.h>
#include <openssl/pem.h>

namespace CPCAPI2
{
namespace PushService
{
// adapted from http://eclipsesource.com/blogs/2016/09/07/tutorial-code-signing-and-verification-with-openssl/
EC_KEY* createPrivateECkey(const resip::Data& key)
{
   EC_KEY *eckey = NULL;
   const char* c_string = key.c_str();
   BIO * keybio = BIO_new_mem_buf((void*)c_string, -1);
   if (keybio == NULL) {
      return NULL;
   }
   eckey = PEM_read_bio_ECPrivateKey(keybio, &eckey, NULL, NULL);
   return eckey;
}

#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
void EVP_MD_CTX_free(EVP_MD_CTX *ctx)
{
   EVP_MD_CTX_cleanup(ctx);
   OPENSSL_free(ctx);
}

void ECDSA_SIG_get0(const ECDSA_SIG *sig, const BIGNUM **pr, const BIGNUM **ps)
{
   if (pr != NULL)
      *pr = sig->r;
   if (ps != NULL)
      *ps = sig->s;
}
#endif

// adapted from http://eclipsesource.com/blogs/2016/09/07/tutorial-code-signing-and-verification-with-openssl/
bool ECSign(EC_KEY* eckey,
   const resip::Data& msg,
   resip::Data& signedMsg) 
{
   EVP_MD_CTX* m_SignCtx = EVP_MD_CTX_create();
   EVP_PKEY* priKey = EVP_PKEY_new();
   EVP_PKEY_assign_EC_KEY(priKey, eckey);
   if (EVP_DigestSignInit(m_SignCtx, NULL, EVP_sha256(), NULL, priKey) <= 0) {
      return false;
   }
   if (EVP_DigestSignUpdate(m_SignCtx, msg.data(), msg.size()) <= 0) {
      return false;
   }
   size_t MsgLenEnc;
   if (EVP_DigestSignFinal(m_SignCtx, NULL, &MsgLenEnc) <= 0) {
      return false;
   }
   if (EVP_DigestSignFinal(m_SignCtx, (unsigned char*)signedMsg.getBuf(MsgLenEnc), &MsgLenEnc) <= 0) {
      return false;
   }

   // ----
   ECDSA_SIG *ec_sig = NULL;
   const BIGNUM *ec_sig_r = NULL;
   const BIGNUM *ec_sig_s = NULL;
   unsigned int degree, bn_len, r_len, s_len, buf_len;
   unsigned char *raw_buf;
   EC_KEY *ec_key;

   /* For EC we need to convert to a raw format of R/S. */

   /* Get the actual ec_key */
   ec_key = EVP_PKEY_get1_EC_KEY(priKey);
   if (ec_key == NULL)
      return false;

   degree = EC_GROUP_get_degree(EC_KEY_get0_group(ec_key));

   EC_KEY_free(ec_key);

   /* Get the sig from the DER encoded version. */
   char* dataBuf = signedMsg.getBuf(signedMsg.size());
   ec_sig = d2i_ECDSA_SIG(NULL, (const unsigned char **)&dataBuf, MsgLenEnc);
   if (ec_sig == NULL)
      return false;

   ECDSA_SIG_get0(ec_sig, &ec_sig_r, &ec_sig_s);
   r_len = BN_num_bytes(ec_sig_r);
   s_len = BN_num_bytes(ec_sig_s);
   bn_len = (degree + 7) / 8;
   if ((r_len > bn_len) || (s_len > bn_len))
      return false;

   buf_len = 2 * bn_len;
   raw_buf = (unsigned char*)alloca(buf_len);
   if (raw_buf == NULL)
      return false;

   /* Pad the bignums with leading zeroes. */
   memset(raw_buf, 0, buf_len);
   BN_bn2bin(ec_sig_r, raw_buf + bn_len - r_len);
   BN_bn2bin(ec_sig_s, raw_buf + buf_len - s_len);

   memcpy(signedMsg.getBuf(buf_len), raw_buf, buf_len);
   // ----

   EVP_MD_CTX_free(m_SignCtx);
   return true;
}

// adapted from http://doctrina.org/Base64-With-OpenSSL-C-API.html
// encodes a binary safe base 64 string
resip::Data Base64Encode(const resip::Data& toencode) 
{ 
   BIO *bio, *b64;
   BUF_MEM *bufferPtr;

   b64 = BIO_new(BIO_f_base64());
   bio = BIO_new(BIO_s_mem());
   bio = BIO_push(b64, bio);

   BIO_set_flags(bio, BIO_FLAGS_BASE64_NO_NL); //Ignore newlines - write everything in one line
   BIO_write(bio, toencode.data(), toencode.size());
   BIO_flush(bio);
   BIO_get_mem_ptr(bio, &bufferPtr);
   BIO_set_close(bio, BIO_NOCLOSE);
   BIO_free_all(bio);

   resip::Data ret((*bufferPtr).data, (*bufferPtr).length);
   ret.replace("+", "-");
   ret.replace("/", "_");
   return ret;
}

resip::Data ApplePushUtils::loadCertFromFile(const resip::Data& fileName)
{
   std::ifstream in(fileName.c_str());
   if (in.is_open())
   {
      std::ostringstream oss;
      oss << in.rdbuf() << std::flush;

      resip::Data doc(oss.str().c_str(), oss.str().size());
      return doc;
   }
   return resip::Data::Empty;
}

int ApplePushUtils::GenerateJWT(const resip::Data& p8file, const resip::Data& authKeyId, const resip::Data& teamId, resip::Data& outJwt)
{
   // from http://thrysoee.dk/apns/ or http://stackoverflow.com/questions/39943701/how-to-send-apns-push-messages-using-apns-auth-key-and-standard-cli-tools
   time_t genTime = time(NULL);
   resip::Data header;
   {
      resip::DataStream headerDs(header);
      headerDs << "{ \"alg\": \"ES256\", \"kid\": \"" << authKeyId << "\" }";
   }
   resip::Data claims;
   {
      resip::DataStream claimsDs(claims);
      claimsDs << "{ \"iss\": \"" << teamId << "\", \"iat\": " << resip::Data::from(genTime) << " }";
   }
   resip::Data headerAndClaimsBase64;
   {
      resip::DataStream headerAndClaimsDs(headerAndClaimsBase64);
      headerAndClaimsDs << Base64Encode(header) << "." << Base64Encode(claims);
   }

   resip::Data cert = loadCertFromFile(p8file);
   EC_KEY* eckeyobj = createPrivateECkey(cert);
   if (eckeyobj == NULL)
   {
      return -1;
   }

   resip::Data signedHeaderAndClaims;
   if (!ECSign(eckeyobj, headerAndClaimsBase64, signedHeaderAndClaims))
   {
      return -1;
   }

   outJwt.clear();
   {
      resip::DataStream jwtDs(outJwt);
      jwtDs << headerAndClaimsBase64 << "." << Base64Encode(signedHeaderAndClaims);
   }
   return 0;
}
}
}

#endif
