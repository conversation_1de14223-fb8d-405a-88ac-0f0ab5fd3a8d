#pragma once

#if !defined(CPCAPI2_PUSH_SERVICE_REDIS_ACCESS)
#define CPCAPI2_PUSH_SERVICE_REDIS_ACCESS

#include "PushServiceRedisAccessIf.h"

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <boost/asio.hpp>
#include <redisclient.h>

#include <functional>
#include <thread>

namespace CPCAPI2
{

namespace PushService
{

class RedisAccess : public RedisAccessBase
{
public:

   RedisAccess();
   virtual ~RedisAccess();

   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) OVERRIDE;
   virtual void shutdown() OVERRIDE;

   virtual int addPushRegistration(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb) OVERRIDE;
   virtual int queryPushRegistration(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb) OVERRIDE;

   virtual int flushAll() OVERRIDE;
   virtual int setHandler(RedisConnectionHandler* handler) OVERRIDE;

private:

   void handleConnected(const std::function<void(bool, const cpc::string&)>& initCb, bool success, const std::string& msg);

private:

   std::unique_ptr<std::thread> mClientThread;
   boost::asio::io_service mIOS;
   std::unique_ptr<redisclient::RedisAsyncClient> mRedisClient;
   int64_t mPushRegTimeoutIntervalMs;
   
   // Handler to allow database connectivity owner to be informed about connection status
   RedisConnectionHandler* mHandler;

};

}

}

#endif // CPCAPI2_PUSH_SERVICE_REDIS_ACCESS
