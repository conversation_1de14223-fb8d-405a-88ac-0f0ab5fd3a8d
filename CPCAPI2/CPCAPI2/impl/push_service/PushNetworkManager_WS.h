#pragma once

#if !defined(CPCAPI2_PUSH_NETWORK_MANAGER_WS_H)
#define CPCAPI2_PUSH_NETWORK_MANAGER_WS_H

#include <string>
#include <vector>

#include "push_service/PushNotificationServiceTypes.h"

#include "rutil/Data.hxx"
#include "PushProvider.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"

namespace CPCAPI2
{

namespace PushService
{

class PushNotificationServiceInterface;

class PushNetworkManager_WS : public PushProvider
{

public:

   PushNetworkManager_WS(CPCAPI2::Phone *phone, PushNotificationServiceInterface* pushMgrIf);
   virtual ~PushNetworkManager_WS();

   CPCAPI2::PushEndpoint::PushNetworkType getNetworkType() const OVERRIDE;
   void init(const PushProviderSettings& settings) OVERRIDE;
   void sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const PushNotificationRequest& request) OVERRIDE;

private:

   bool buildJSON(const cpc::string& deviceToken, const PushNotificationRequest& request, std::string& outJson);
   void sendPushNotificationImpl(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, CPCAPI2::PushService::PushNotificationServiceHandle service, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info, const PushNotificationRequest& request);

private:

   PushNotificationServiceInterface* mPushMgrIf;
   JsonApi::JsonApiUserHandle m_hUser;
   JsonApi::JsonApiServerSendTransportInternal* m_Transport;

};

}

}


#endif // CPCAPI2_PUSH_NETWORK_MANAGER_WS_H
