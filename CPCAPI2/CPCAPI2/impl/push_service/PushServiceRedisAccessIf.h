#pragma once

#if !defined(CPCAPI2_PUSH_SERVICE_REDIS_ACCESS_IF)
#define CPCAPI2_PUSH_SERVICE_REDIS_ACCESS_IF

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <functional>

namespace CPCAPI2
{

namespace PushService
{

enum RedisConnectionStatus
{
   RedisConnectionStatus_Connected = 0,
   RedisConnectionStatus_Disconnected = 1,
   RedisConnectionStatus_ConnFailure = 2
};

struct RedisConnectionStatusEvent
{
   RedisConnectionStatus connectionStatus;
   cpc::string statusDesc;

   RedisConnectionStatusEvent() : connectionStatus(RedisConnectionStatus_Disconnected), statusDesc("") {}
};

class RedisConnectionHandler
{

public:

   virtual~ RedisConnectionHandler() {}

   virtual int onRedisConnectionStatusChanged(const RedisConnectionStatusEvent& args) = 0;

};

struct RedisAccessConfig
{
   cpc::string ip;
   int port;
   int64_t pushRegTimeoutIntervalMs;

   RedisAccessConfig() : ip(""), port(-1), pushRegTimeoutIntervalMs(3628800000) {} // Timeout interval 6 weeks
};

class RedisAccessBase
{

public:

   struct AddPushRegistrationArgs
   {
      int pushNetworkType;
      cpc::string deviceToken;
      cpc::string apnsTopic;
      cpc::string apnsTopicPushKit;
      cpc::string pushKitToken;
      bool useSandbox;
      unsigned int jsonUserHandle;
      cpc::string userPlusDeviceIdentity;

      AddPushRegistrationArgs() : pushNetworkType(-1), deviceToken(""), apnsTopic(""), apnsTopicPushKit(""), pushKitToken(""), useSandbox(false), jsonUserHandle(0), userPlusDeviceIdentity("") {}
   };

   struct AddPushRegistrationResult
   {
      int result;
      cpc::string endpointId;

      AddPushRegistrationResult() : result(-1), endpointId("") {}
   };

   struct QueryPushRegistrationArgs
   {
      cpc::string endpointId;

      QueryPushRegistrationArgs() : endpointId("") {}
   };

   struct QueryPushRegistrationResult
   {
      int result;
      int pushNetworkType;
      cpc::string deviceToken;
      cpc::string apnsTopic;
      cpc::string apnsTopicPushKit;
      cpc::string pushKitToken;
      bool useSandbox;
      unsigned int jsonUserHandle;

      QueryPushRegistrationResult() : result(-1), pushNetworkType(-1), deviceToken(""), apnsTopic(""), apnsTopicPushKit(""), pushKitToken(""), useSandbox(false), jsonUserHandle(0) {}
   };

   virtual ~RedisAccessBase() {}

   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) = 0;

   virtual void shutdown() = 0;

   virtual int addPushRegistration(const AddPushRegistrationArgs& regInfo, const std::function<void(bool, const AddPushRegistrationResult&)>& regAddedCb) = 0;

   virtual int queryPushRegistration(const QueryPushRegistrationArgs& serviceRequest, const std::function<void(bool, const QueryPushRegistrationResult&)>& queryPushRegsCb) = 0;

   virtual int flushAll() = 0;

   virtual int setHandler(RedisConnectionHandler* handler) = 0;

};

cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessConfig& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationArgs& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationResult& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationArgs& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationResult& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisConnectionStatus& args);
cpc::string get_debug_string(const CPCAPI2::PushService::RedisConnectionStatusEvent& args);


std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessConfig& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::AddPushRegistrationResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisAccessBase::QueryPushRegistrationResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisConnectionStatus& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::RedisConnectionStatusEvent& args);

}

}

#endif // CPCAPI2_PUSH_SERVICE_REDIS_ACCESS_IF
