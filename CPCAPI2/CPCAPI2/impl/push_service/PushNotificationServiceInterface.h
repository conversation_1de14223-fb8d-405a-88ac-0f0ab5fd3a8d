#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_SERVICE_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_SERVICE_INTERFACE_H

#include "cpcapi2defs.h"
#include "push_service/PushNotificationServiceManager.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "push_service/PushNotificationServiceManagerInternal.h"
#include "push_service/PushServiceRedisAccessIf.h"
#include "PushProvider.h"

#include "phone/Cpcapi2EventSource.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <map>

namespace CPCAPI2
{

class PhoneInterface;

namespace PushService
{

class RedisAccessBase;
class PushNotificationServiceSyncHandler {};

class PushNotificationServiceInterface : public CPCAPI2::EventSource<PushNotificationServiceHandle, PushNotificationServiceHandler, PushNotificationServiceSyncHandler>,
                                         public PushNotificationServiceManagerInternal,
                                         public RedisConnectionHandler,
                                         public resip::DeadlineTimerHandler,
                                         public PhoneModule
{

public:

   PushNotificationServiceInterface(Phone* phone);
   virtual ~PushNotificationServiceInterface();

   template<typename TFn, typename TEvt> void fireServerHandlerEvent(const char* funcName, TFn func, PushNotificationServiceHandle h, const TEvt& args)
   {
      fireEvent(funcName, func, h, args);
   }

   void postPushFuncToSdkThread(resip::ReadCallbackBase* command)
   {
      postToSdkThread(command);
   }

   RedisAccessBase* getPushDatabaseAccess() const
   {
      return mPushDb.get();
   }
   
   PhoneInterface* phoneInterface() const
   {
      return mPhone;
   }

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE
   {
      return CPCAPI2::EventSource<PushNotificationServiceHandle, PushNotificationServiceHandler, PushNotificationServiceSyncHandler>::process(timeout);
   }

   virtual void interruptProcess() OVERRIDE
   {
      CPCAPI2::EventSource<PushNotificationServiceHandle, PushNotificationServiceHandler, PushNotificationServiceSyncHandler>::interruptProcess();
   }

   // override both EventSource and PushNotificationServiceManagerInternal
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE
   {
      CPCAPI2::EventSource<PushNotificationServiceHandle, PushNotificationServiceHandler, PushNotificationServiceSyncHandler>::setCallbackHook(cbHook, context);
   }

   // PushNotificationServiceManager
   virtual PushNotificationServiceHandle createPushNotificationHandle() OVERRIDE;
   virtual int registerPushNotificationService(PushNotificationServiceHandle h, CPCAPI2::PushEndpoint::PushNotificationEndpointId device) OVERRIDE;
   virtual int unregisterPushNotificationService(PushNotificationServiceHandle h) OVERRIDE;
   virtual int setHandler(PushNotificationServiceHandle h, PushNotificationServiceHandler* handler) OVERRIDE;
   virtual int configurePushProvider(const PushProviderSettings& pushProviderSettings) OVERRIDE;
   virtual int configureDatabaseAccess(const CPCAPI2::PushEndpoint::PushDatabaseSettings& pushDbSettings) OVERRIDE;
   virtual int sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, const PushNotificationRequest& pushRequest) OVERRIDE;

   // PushNotificationServiceManagerInternal
   virtual void addEventObserver(PushNotificationServiceHandler* handler) OVERRIDE;

   // RedisConnectionHandler
   virtual int onRedisConnectionStatusChanged(const CPCAPI2::PushService::RedisConnectionStatusEvent& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   int createPushNotificationHandleImpl(PushNotificationServiceHandle h);
   int registerPushNotificationServiceImpl(PushNotificationServiceHandle h, CPCAPI2::PushEndpoint::PushNotificationEndpointId device);
   int unregisterPushNotificationServiceImpl(PushNotificationServiceHandle h);
   int configurePushProviderImpl(const PushProviderSettings& pushProviderSettings);
   int configureDatabaseAccessImpl(const CPCAPI2::PushEndpoint::PushDatabaseSettings& pushDbSettings);
   int sendPushNotificationImpl(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, const PushNotificationRequest& pushRequest);
   int onRedisConnectionStatusChangedImpl(const CPCAPI2::PushService::RedisConnectionStatusEvent& args);

private:

   PhoneInterface* mPhone;
   std::map<CPCAPI2::PushEndpoint::PushNetworkType, PushProviderPtr> mPushProviders;
   std::map<CPCAPI2::PushService::PushNotificationServiceHandle, CPCAPI2::PushEndpoint::PushNotificationEndpointId> mPushRegistrationHandles;
   std::map<CPCAPI2::PushEndpoint::PushNotificationEndpointId, CPCAPI2::PushService::PushNotificationServiceHandle> mPushRegistrationEndpoints;
   std::unique_ptr<RedisAccessBase> mPushDb;
   resip::DeadlineTimer<resip::MultiReactor> mRedisConnectionTimer;
   CPCAPI2::PushEndpoint::PushDatabaseSettings mDbSettings;
   unsigned int mRedisConnectionTimeoutMsecs;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::StatusCode& status);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::ReasonCode& reason);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationSuccessEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationFailureEvent& event);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& event);
   
class PushNotificationServiceHandleFactory
{

public:

   static PushNotificationServiceHandle getNext() { return sNextHandle++; }

private:

   static std::atomic<PushNotificationServiceHandle> sNextHandle;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_SERVICE_INTERFACE_H
