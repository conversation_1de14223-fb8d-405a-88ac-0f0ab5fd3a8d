#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)

#include "json/JsonSerialize.h"
#include "PushNotificationServiceInterface.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "PushNetworkManager_APN.h"

#include "util/cpc_logger.h"
#include "util/DumFpCommand.h"
#include "ApplePushUtils.h"
#include "phone/PhoneInterface.h"
#include "log/LocalLogger.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <fstream>
#include <sstream>

#include <time.h>

#include <sys/types.h>
#include <sys/stat.h>
#ifndef WIN32
#include <unistd.h>
#endif

#ifdef WIN32
#define stat _stat
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{
namespace PushService
{
PushNetworkManager_APN::PushNetworkManager_APN(PushNotificationServiceInterface* pushMgrIf) :
PushProvider(),
mPushMgrIf(pushMgrIf),
mProviderAuthTokenValiditySeconds(55*60),
mHaveProdHttpSession(false),
mHaveSandboxHttpSession(false),
mLocalLogger(pushMgrIf->phoneInterface()->localLogger())
{
   DebugLog(<< "PushNetworkManager_APN()");
}

PushNetworkManager_APN::~PushNetworkManager_APN()
{
}

CPCAPI2::PushEndpoint::PushNetworkType PushNetworkManager_APN::getNetworkType() const
{
   return CPCAPI2::PushEndpoint::PushNetworkType::PushNetworkType_Apple;
}

void PushNetworkManager_APN::init(const PushProviderSettings& settings)
{
   StackLog(
      << "PushNetworkManager_APN::init():"
      << " (PROD) p8file: "    << settings.apnSettings.p8file.c_str()
      << " (PROD) authKeyId: " << settings.apnSettings.authKeyId
      << " (PROD) teamId: "    << settings.apnSettings.teamId
      << " (PROD) apnUrl: "    << settings.apnSettings.apnUrl
      << " (SBOX) p8file: "    << settings.apnSandboxSettings.p8file.c_str()
      << " (SBOX) authKeyId: " << settings.apnSandboxSettings.authKeyId
      << " (SBOX) teamId: "    << settings.apnSandboxSettings.teamId
      << " (SBOX) apnUrl: "    << settings.apnSandboxSettings.apnUrl);

   m_Settings = settings;
}

bool PushNetworkManager_APN::buildJSON(const PushNotificationRequest& request, std::string& outJson)
{
   rapidjson::StringBuffer buffer;
   rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
   writer.StartObject();

   // BRICE currently looks for a "cpc" object
   // cpc object
   writer.Key("cpc");
   writer.StartObject();
   for( CustomData::const_iterator iter = request.customData.begin() ; iter != request.customData.end() ; ++iter )
   {
      switch( iter->dataType )
      {
      case CustomDataType_bool:
         Json::Write(writer, iter->name, iter->boolVal);
         break;
      case CustomDataType_int:
         Json::Write(writer, iter->name, iter->intVal);
         break;
      case CustomDataType_uint:
         Json::Write(writer, iter->name, static_cast<uint64_t>(iter->intVal));
         break;
      case CustomDataType_double:
         Json::Write(writer, iter->name, iter->doubleVal);
         break;
      case CustomDataType_string:
         Json::Write(writer, iter->name, iter->stringVal);
         break;
      }
   }
   writer.EndObject();
   // end cpc object

   writer.Key("aps");
   writer.StartObject(); // Start aps object
   // TODO: invisible notifications.
   if (request.body.size() > 0)
   {
      // alert object
      Json::WriteObject(writer, "alert",  "body", request.body, "title-loc-key", request.title_loc_key, "title-loc-args", request.title_loc_args);

      for (cpc::vector < PushNotificationOption >::const_iterator it = request.options.begin(); it != request.options.end(); ++it)
      {
         if (*it == PushNotificationOption_InlineReply)
         {
            Json::Write(writer, "category", "NEW_MESSAGE_CATEGORY");
         }
      }
   }
   else
   {
      // APN requires the following key to indicate an invisible (background) update
      int contentAvailable = 1;
      Json::Write(writer, "content-available", contentAvailable);
   }
   writer.EndObject(); // End aps object

   writer.EndObject();
   outJson = buffer.GetString();
   return true;
}

// static int sPushCount = 0; // For testing expired tokens

void PushNetworkManager_APN::sendPushNotification(
   CPCAPI2::PushEndpoint::PushNotificationEndpointId device,
   CPCAPI2::PushService::PushNotificationServiceHandle service,
   const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& info,
   const PushNotificationRequest& request)
{
   const cpc::string& deviceToken(info.deviceToken);
   const cpc::string& apnsTopic(info.apnInfo.apnsTopic);
   const cpc::string& pushKitToken(info.apnInfo.pushKitToken);
   const cpc::string& apnsTopicPushKit(info.apnInfo.apnsTopicPushKit);

   std::string body;
   if (!buildJSON(request, body))
   {
      LocalInfoLog("PushNetworkManager_APN::sendPushNotification(): failed to build JSON document, jsonUser={}", info.websocketInfo.jsonUserHandle);
      NotificationFailureEvent evt;
      evt.device = device;
      evt.statusCode = PushService::StatusCode_Unknown;
      evt.reasonCode = PushService::ReasonCode_Unknown;
      evt.errorString = cpc::string(get_debug_string(evt.reasonCode));
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationFailure), service, evt);
      return;
   }

   LocalInfoLog("PushNetworkManager_APN::sendPushNotification(device={}, pushKitToken={} jsonUser={}, notification service={}, apnsTopic={}, apnsTopicPushKit={} body={}",
           deviceToken.c_str(), pushKitToken.c_str(), info.websocketInfo.jsonUserHandle, service, apnsTopic.c_str(), apnsTopicPushKit.c_str(), body);

   HTTPClient::RequestConfig config;
   config.verb                    = CPCAPI2::HTTPClient::EHTTPVerbPOST;
   config.mimeType                = "text/plain";
   config.username                = "";
   config.password                = "";
   config.clientCertificate       = "";
   config.clientCertificatePasswd = "";
   config.useEmbeddedCert1        = false;
   config.useEmbeddedCert2        = false;
   config.ignoreCertErrors        = false;
   config.enableCookies           = false;
   config.cookieFile              = "";
   config.verboseLogging          = true;
   config.userAgent               = "CounterPath-HTTP";
   config.useHttp2                = true;

   CPCAPI2::HTTPClient::ResponseResult result;

   // Get the right URL depending on the request options
   bool isSandbox = false;
   cpc::vector< PushNotificationOption >::const_iterator iter = request.options.begin();
   for( ; iter != request.options.end() ; ++iter )
   {
      if( *iter == PushNotificationOption_Sandbox )
      {
         LocalDebugLog("Will deliver for sandbox APNS");
         isSandbox = true;
      }
   }

   std::string pushApiUrl = isSandbox ?
      m_Settings.apnSandboxSettings.apnUrl.c_str() :
      m_Settings.apnSettings.apnUrl.c_str();
   bool pushKitEnabled = (isSandbox ? m_Settings.apnSandboxSettings.pushKitEnabled : m_Settings.apnSettings.pushKitEnabled);

   // Append the device token to the URL
   if (pushApiUrl.size() > 0 && (pushApiUrl[ pushApiUrl.size() - 1 ] != '/'))
      pushApiUrl += "/";
   if (request.apnInfo.usePushKit && pushKitEnabled)
   {
      pushApiUrl += pushKitToken.c_str();
   }
   else
   {
      pushApiUrl += deviceToken.c_str();
   }

   resip::Data p8file( isSandbox ? m_Settings.apnSandboxSettings.p8file : m_Settings.apnSettings.p8file );
   resip::Data authKeyId( isSandbox ? m_Settings.apnSandboxSettings.authKeyId :m_Settings.apnSettings.authKeyId);
   resip::Data teamId( isSandbox ? m_Settings.apnSandboxSettings.teamId :m_Settings.apnSettings.teamId);

   LocalInfoLog("Using following configuration for APN: p8file: {} authKeyId: {} teamId: {} pushApiUrl: {}", p8file.c_str(), authKeyId.c_str(), teamId.c_str(), pushApiUrl.c_str());

   std::string bearerAuthToken = std::string("bearer ") + encodeAuthToken(p8file, authKeyId, teamId).c_str();
   config.messageBody = body.c_str();
   config.messageLengthInBytes = body.size();
   config.customHeaders.push_back({ "authorization", bearerAuthToken.c_str() });
   if (request.apnInfo.usePushKit && pushKitEnabled)
   {
      config.customHeaders.push_back({ "apns-topic", apnsTopicPushKit.c_str() });
   }
   else
   {
      config.customHeaders.push_back({ "apns-topic", apnsTopic.c_str() });
   }
   config.customHeaders.push_back({ "apns-priority", "10" });

   config.verboseLogging = true;

   CPCAPI2::HTTPClient& httpClient = isSandbox ? mSandboxHttpClient : mProdHttpClient;
   bool haveHttpSession = isSandbox ? mHaveSandboxHttpSession : mHaveProdHttpSession;

   // This had to be commented out for now, the reason being:
   // The collapse ID cannot exceed 64 bytes, and the device token is often longer.
   // So the problem is that the APN is rejecting our HTTP message.
   // config.customHeaders.push_back({ "apns-collapse-id", deviceToken.c_str() });

   // StackLog(<< "PushNetworkManager_APN::sendPushNotification(): http data: " << config);
   if (haveHttpSession)
   {
      LocalInfoLog("PushNetworkManager_APN::sendPushNotification(): sending session push request to {} for jsonUser={}", pushApiUrl, info.websocketInfo.jsonUserHandle);
      httpClient.DoSessionRequest(pushApiUrl.c_str(), config, result);
   }
   else
   {
      LocalInfoLog("PushNetworkManager_APN::sendPushNotification(): sending initial push request to {} for jsonUser={}", pushApiUrl, info.websocketInfo.jsonUserHandle);
      if (isSandbox)
      {
         mHaveSandboxHttpSession = true;
      }
      else
      {
         mHaveProdHttpSession = true;
      }
      httpClient.StartHTTPSession(pushApiUrl.c_str(), config, result);
   }

   // sPushCount++; // For testing expired tokens

   LocalInfoLog("PushNetworkManager_APN::sendPushNotification(): push request result: jsonUser={}, status={}, errorCode={}, messageBody={}", info.websocketInfo.jsonUserHandle, result.status, result.errorCode, result.messageBody.c_str());

   if (result.status == 403 && result.messageBody.find("ExpiredProviderToken") != cpc::string::npos)
   {
      LocalDebugLog("PushNetworkManager_APN::sendPushNotification(): expired authentication token, will attempt to resend notification for jsonUser={} with updated token", info.websocketInfo.jsonUserHandle);
      std::remove("auth_token.txt");
      sendPushNotification(device, service, info, request);
   }
   /* For testing expired tokens
   else if ((sPushCount > 0) && (sPushCount % 5 == 0))
   {
      NotificationFailureEvent evt;
      evt.device = device;
      evt.statusCode = PushService::StatusCode_ExpiredDeviceToken;
      evt.reasonCode = PushService::ReasonCode_Unregistered;
      evt.errorString = cpc::string(get_debug_string(evt.reasonCode));
      InfoLog(<< "PushNetworkManager_APN::sendPushNotification(): notification failure event for testing: " << evt);
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationFailure), service, evt);
   }
   */
   else if (result.status < 200 || result.status >= 300)
   {
      NotificationFailureEvent evt;
      evt.device = device;
      evt.statusCode = get_notification_status(result.status);
      evt.reasonCode = PushService::ReasonCode_Unknown;
      evt.errorString = cpc::string(get_debug_string(evt.reasonCode));

      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(result.messageBody.c_str());

      if (jsonRequest->HasParseError())
      {
         LocalWarningLog("Invalid message body format for jsonUser={}, parse error occured: {}", info.websocketInfo.jsonUserHandle, jsonRequest->GetParseError());
      }
      else if (!jsonRequest->HasMember("reason"))
      {
         LocalWarningLog("Invalid message body for jsonUser={}, missing \"reason\" key", info.websocketInfo.jsonUserHandle);
      }
      else
      {
         const rapidjson::Value& reasonVal = (*jsonRequest)["reason"];
         if (!reasonVal.IsString())
         {
            LocalWarningLog(, "Invalid message body for jsonUser={}, \"reason\" is not a string", info.websocketInfo.jsonUserHandle);
         }
         else
         {
            evt.errorString = reasonVal.GetString();
            evt.reasonCode = get_notification_reason(evt.errorString);
         }
      }

      LocalInfoLog("PushNetworkManager_APN::sendPushNotification(): notification failure  for jsonUser={}, event {}: ", info.websocketInfo.jsonUserHandle, evt);
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationFailure), service, evt);
   }
   else // 200 level response
   {
      NotificationSuccessEvent evt;
      evt.device = device;
      mPushMgrIf->fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationSuccess), service, evt);
   }
}

resip::Data PushNetworkManager_APN::encodeAuthToken(const resip::Data& p8file, const resip::Data& authKeyId, const resip::Data& teamId)
{
   LocalDebugLog("PushNetworkManager_APN::encodeAuthToken()");
   struct stat result;
   if (stat("auth_token.txt", &result) == 0)
   {
      time_t mod_time = result.st_mtime;
      if (time(NULL) - mod_time > mProviderAuthTokenValiditySeconds)
      {
         std::remove("auth_token.txt");
      }
   }

   std::ifstream in("auth_token.txt");
   std::string storedAuthToken;
   if (in.is_open())
   {
      std::ostringstream oss;
      oss << in.rdbuf() << std::flush;

      storedAuthToken = std::string(oss.str().c_str(), oss.str().size());
      if (storedAuthToken.empty())
      {
         LocalErrLog("PushNetworkManager_APN::encodeAuthToken(): existing authentication file seems to be empty, will have to be generated");
         in.close();
         std::remove("auth_token.txt");
      }
      else
      {
         return resip::Data(storedAuthToken.c_str(), storedAuthToken.size());
      }
   }
   else
   {
      LocalInfoLog("PushNetworkManager_APN::encodeAuthToken(): could not find stored authentication token, will have to be generated");
   }

   if (p8file.empty())
   {
      LocalErrLog("PushNetworkManager_APN::encodeAuthToken(): p8 file is not populated");
   }
   else
   {
      std::ifstream p8fileStr( p8file.c_str() );
      if (!p8fileStr.is_open())
      {
         LocalErrLog("PushNetworkManager_APN::encodeAuthToken(): error opening p8 file: {}", p8file.c_str());
      }
      p8fileStr.close();
   }

   resip::Data jwt;
   ApplePushUtils::GenerateJWT(p8file, authKeyId /* usually the second half of the .p8 file name */, teamId /*"UQC9N9AMZM"*/, jwt);

   std::ofstream outstr("auth_token.txt", std::ios_base::out | std::ios_base::trunc);
   if (outstr.is_open())
   {
      if (jwt.empty())
      {
         LocalErrLog("PushNetworkManager_APN::encodeAuthToken(): error generating jwt token");
      }
      outstr << jwt << std::flush;
      outstr.close();
   }
   else
   {
      LocalErrLog("PushNetworkManager_APN::encodeAuthToken(): could not generate jwt token as the authentication file could not be opened");
   }

   return jwt;
}

}

}

#endif
