#pragma once

#if !defined(CPCAPI2_APPLE_PUSH_UTILS)
#define CPCAPI2_APPLE_PUSH_UTILS

#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace PushService
{
class ApplePushUtils
{
public:
   static int GenerateJWT(const resip::Data& p8file, const resip::Data& authKeyId, const resip::Data& teamId, resip::Data& outJwt);

private:
   static resip::Data loadCertFromFile(const resip::Data& fileName);
};
}
}

#endif // CPCAPI2_APPLE_PUSH_UTILS
