#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "********************************.h"
#include "PushNetworkManager_APN.h"
#include "PushNetworkManager_FCM.h"
#include "PushNetworkManager_WS.h"
#include "PushServiceRedisAccess.h"
#include "PushServiceSqliteAccess.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/Random.hxx>

#include <future>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define REDIS_CONNECTION_RETRY_INTERVAL_MSECS                                 7500
#define REDIS_CONNECTION_RETRY_INTERVAL_MAXIMUM_MSECS                         120000
#define REDIS_CONNECTION_TIMER_ID                                             1

using namespace CPCAPI2::PushEndpoint;
using CPCAPI2::Phone;

namespace CPCAPI2
{

namespace PushService
{

std::atomic<PushNotificationServiceHandle> PushNotificationServiceHandleFactory::sNextHandle(1);

********************************::********************************(Phone* phone) :
CPCAPI2::EventSource<PushNotificationServiceHandle, PushNotificationServiceHandler, PushNotificationServiceSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mRedisConnectionTimer(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mRedisConnectionTimeoutMsecs(REDIS_CONNECTION_RETRY_INTERVAL_MSECS)
{
   mPhone->addRefImpl();
}

********************************::~********************************()
{
   if (mPushDb)
   {
      mPushDb->shutdown();
   }
   mPhone->releaseImpl();
   mPushRegistrationEndpoints.clear();
   mPushRegistrationHandles.clear();
}

void ********************************::Release()
{
   delete this;
}

PushService::PushNotificationServiceHandle ********************************::createPushNotificationHandle()
{
   PushNotificationServiceHandle h = PushNotificationServiceHandleFactory::getNext();
   DebugLog(<< "********************************::createPushNotificationHandle(): handle: " << h);
   postToSdkThread(resip::resip_bind(&********************************::createPushNotificationHandleImpl, this, h));
   return h;
}

int ********************************::createPushNotificationHandleImpl(PushNotificationServiceHandle h)
{
   return kSuccess;
}

int ********************************::registerPushNotificationService(PushNotificationServiceHandle h, CPCAPI2::PushEndpoint::PushNotificationEndpointId device)
{
   DebugLog(<< "********************************::registerPushNotificationService(): handle: " << h << " device: " << device);
   postToSdkThread(resip::resip_bind(&********************************::registerPushNotificationServiceImpl, this, h, device));
   return kSuccess;
}

int ********************************::registerPushNotificationServiceImpl(PushNotificationServiceHandle h, CPCAPI2::PushEndpoint::PushNotificationEndpointId device)
{
   NotificationServiceRegistrationEvent evt;
   evt.device = device;
   evt.service = h;

   std::map<CPCAPI2::PushService::PushNotificationServiceHandle, CPCAPI2::PushEndpoint::PushNotificationEndpointId>::iterator i = mPushRegistrationHandles.find(h);
   if (i == mPushRegistrationHandles.end())
   {
      std::map<CPCAPI2::PushEndpoint::PushNotificationEndpointId, CPCAPI2::PushService::PushNotificationServiceHandle>::iterator j = mPushRegistrationEndpoints.find(device);
      if (j == mPushRegistrationEndpoints.end())
      {
         DebugLog(<< "********************************::registerPushNotificationServiceImpl(): registering notification handle: " << h << " with device: " << device);
         evt.success = true;
         mPushRegistrationHandles[h] = device;
         mPushRegistrationEndpoints[device] = h;
      }
      else
      {
         DebugLog(<< "********************************::registerPushNotificationServiceImpl(): invalid registeration request, notification device: " << i->second << " already registered under handle: " << i->first);
         evt.success = false;
      }
   }
   else
   {
      if ((i->first == h) && (i->second == device))
      {
         DebugLog(<< "********************************::registerPushNotificationServiceImpl(): notification handle: " << h << " already registered with device: " << i->second);
         evt.success = true;
      }
      else
      {
         DebugLog(<< "********************************::registerPushNotificationServiceImpl(): invalid registeration request, notification handle: " << h << " already registered with another device: " << i->second);
         evt.success = false;
      }
   }

   fireServerHandlerEvent(cpcFunc(PushNotificationServiceHandler::onNotificationServiceRegistration), h, evt);
   return kSuccess;
}

int ********************************::unregisterPushNotificationService(PushNotificationServiceHandle h)
{
   DebugLog(<< "********************************::unregisterPushNotificationService(): handle: " << h);
   postToSdkThread(resip::resip_bind(&********************************::unregisterPushNotificationServiceImpl, this, h));
   return kSuccess;
}

int ********************************::unregisterPushNotificationServiceImpl(PushNotificationServiceHandle h)
{
   std::map<CPCAPI2::PushService::PushNotificationServiceHandle, CPCAPI2::PushEndpoint::PushNotificationEndpointId>::iterator i = mPushRegistrationHandles.find(h);
   if (i == mPushRegistrationHandles.end())
   {
      DebugLog(<< "********************************::unregisterPushNotificationServiceImpl(): invalid unregisteration request, notification handle: " << h << " is invalid");
   }
   else
   {
      DebugLog(<< "********************************::unregisterPushNotificationServiceImpl(): notification handle: " << h << " found mapped with device: " << i->second);
      mPushRegistrationEndpoints.erase(i->second);
      mPushRegistrationHandles.erase(i);
   }

   return kSuccess;
}

int ********************************::setHandler(PushNotificationServiceHandle h, PushNotificationServiceHandler* pHandler)
{
   DebugLog(<< "********************************::setHandler()");
   setAppHandler(h, pHandler);
   return kSuccess;
}

int ********************************::configurePushProvider(const PushProviderSettings& pushProviderSettings)
{
   DebugLog(<< "********************************::configurePushProvider()");
   postToSdkThread(resip::resip_bind(&********************************::configurePushProviderImpl, this, pushProviderSettings));
   return kSuccess;
}

// This method is a kind of factory for the push provider
int ********************************::configurePushProviderImpl(const PushProviderSettings& pushProviderSettings)
{
   DebugLog(<< "********************************::configurePushProviderImpl()");
   if( mPushProviders.find( pushProviderSettings.pushNetworkType ) != mPushProviders.end() )
      return kSuccess; // currently no way to "reconfigure"

   PushProviderPtr pushProvider;
   switch (pushProviderSettings.pushNetworkType)
   {
   case PushNetworkType_Apple:
      pushProvider.reset( new PushNetworkManager_APN(this));
      break;
   case PushNetworkType_FCM:
      pushProvider.reset( new PushNetworkManager_FCM(this));
      break;
   case PushNetworkType_WS:
      pushProvider.reset( new PushNetworkManager_WS(mPhone, this));
      break;
   case PushNetworkType_Unknown:
      assert(0 && "Unknown push provider type");
      break;
   }

   if (pushProvider)
   {
      pushProvider->init(pushProviderSettings);
      mPushProviders[pushProviderSettings.pushNetworkType] = pushProvider;
   }

   return kSuccess;
}

int ********************************::configureDatabaseAccess(const CPCAPI2::PushEndpoint::PushDatabaseSettings& pushDbSettings)
{
   // StackLog(<< "********************************::configureDatabaseAccess()");
   postToSdkThread(resip::resip_bind(&********************************::configureDatabaseAccessImpl, this, pushDbSettings));
   return kSuccess;
}

int ********************************::configureDatabaseAccessImpl(const CPCAPI2::PushEndpoint::PushDatabaseSettings& pushDbSettings)
{
   StackLog(<< "********************************::configureDatabaseAccessImpl(): initiating connection with database server: " << pushDbSettings.redisIp << ":" << pushDbSettings.redisPort);
   if (resip::isEqualNoCase(pushDbSettings.redisIp.c_str(), "pushdbsqlite"))
   {
      mPushDb.reset(new SqliteAccess());
   }
   else
   {
      mPushDb.reset(new RedisAccess());
   }

   mPushDb->setHandler(this);
   mDbSettings = pushDbSettings;
   mRedisConnectionTimer.cancel();
   
   RedisAccessConfig redisAccessConfig;
   redisAccessConfig.ip = pushDbSettings.redisIp;
   redisAccessConfig.port = pushDbSettings.redisPort;
   mPushDb->initialize(redisAccessConfig, NULL);

   return kSuccess;
}

int ********************************::sendPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, const PushNotificationRequest& pushRequest)
{
   postToSdkThread(resip::resip_bind(&********************************::sendPushNotificationImpl, this, device, pushRequest));
   return kSuccess;
}

int ********************************::sendPushNotificationImpl(CPCAPI2::PushEndpoint::PushNotificationEndpointId device, const PushNotificationRequest& pushRequest)
{
   StackLog(<< "********************************::sendPushNotificationImpl(): endpoint-id: " << device);
   RedisAccessBase::QueryPushRegistrationArgs queryArgs;
   queryArgs.endpointId = device;
   auto p1 = std::make_shared<std::promise<RedisAccessBase::QueryPushRegistrationResult> >();
   std::future<RedisAccessBase::QueryPushRegistrationResult> f1 = p1->get_future();
   mPushDb->queryPushRegistration(queryArgs, [p1](bool, const RedisAccessBase::QueryPushRegistrationResult& res)
   {
      p1->set_value(res);
   });

   if (f1.wait_for(std::chrono::milliseconds(200000)) == std::future_status::ready)
   {
      const RedisAccessBase::QueryPushRegistrationResult& qr = f1.get();
      if (qr.result == 0)
      {
         std::map<PushNetworkType, PushProviderPtr>::const_iterator itPushProv = mPushProviders.find((PushNetworkType)qr.pushNetworkType);
         if (itPushProv != mPushProviders.end())
         {
            if (itPushProv->second)
            {
               PushNotificationRegistrationInfo pushRegInfo;
               pushRegInfo.deviceToken = qr.deviceToken;
               pushRegInfo.pushNetworkType = (PushNetworkType)qr.pushNetworkType;
               pushRegInfo.apnInfo.apnsTopic = qr.apnsTopic;
               pushRegInfo.apnInfo.useSandbox = qr.useSandbox;
               pushRegInfo.apnInfo.apnsTopicPushKit = qr.apnsTopicPushKit;
               pushRegInfo.apnInfo.pushKitToken = qr.pushKitToken;
               pushRegInfo.websocketInfo.jsonUserHandle = qr.jsonUserHandle;

               // all the members support '=' as far as I can tell: d.s.
               PushNotificationRequest temp = pushRequest;

               DebugLog(<< "********************************::sendPushNotificationImpl qr.useSandbox: " << qr.useSandbox << ", temp.options:");
               for (cpc::vector<PushNotificationOption>::const_iterator it = temp.options.begin(); it != temp.options.end(); ++it)
               {
                  DebugLog(<< "temp.option: " << *it);
               }

               // what the endpoint registered overrides the settings for the sandbox
               if( qr.useSandbox )
                  temp.options.push_back( PushNotificationOption_Sandbox );

               DebugLog(<< "********************************::sendPushNotificationImpl(): " << pushRegInfo.pushNetworkType << " deviceToken: " << pushRegInfo.deviceToken << " pushKitToken: " << pushRegInfo.apnInfo.pushKitToken);
               PushNotificationServiceHandle service = 0;
               std::map<CPCAPI2::PushEndpoint::PushNotificationEndpointId, CPCAPI2::PushService::PushNotificationServiceHandle>::iterator i = mPushRegistrationEndpoints.find(device);
               if (i != mPushRegistrationEndpoints.end())
               {
                  service = i->second;
               }
               DebugLog(<< "********************************::sendPushNotificationImpl(): notification handle: " << service << " device: " << device << " push network-type: " << pushRegInfo.pushNetworkType << " deviceToken: " << pushRegInfo.deviceToken << " pushKitToken: " << pushRegInfo.apnInfo.pushKitToken);
               itPushProv->second->sendPushNotification(device, service, pushRegInfo, temp);
            }
         }
      }
   }
   else
   {
      ErrLog(<< "********************************::sendPushNotificationImpl() failed to send push to device " << device << "; reason=dbtimeout");
   }
   return kSuccess;
}

void ********************************::addEventObserver(PushNotificationServiceHandler* handler)
{
   addSdkObserver(handler);
}

int ********************************::onRedisConnectionStatusChanged(const CPCAPI2::PushService::RedisConnectionStatusEvent& args)
{
   // StackLog(<< "********************************::onRedisConnectionStatusChanged(): server: " << mDbSettings.redisIp << ":" << mDbSettings.redisPort << " status: " << args.connectionStatus  << " thread-id: " << std::this_thread::get_id());
   postToSdkThread(resip::resip_bind(&********************************::onRedisConnectionStatusChangedImpl, this, args));
   return kSuccess;
}

int ********************************::onRedisConnectionStatusChangedImpl(const CPCAPI2::PushService::RedisConnectionStatusEvent& args)
{
   StackLog(<< "********************************::onRedisConnectionStatusChangedImpl(): server: " << mDbSettings.redisIp << ":" << mDbSettings.redisPort << " status: " << args.connectionStatus);
   mRedisConnectionTimer.cancel();

   if (args.connectionStatus == RedisConnectionStatus_Connected)
   {
      // Nothing to do
      StackLog(<< "********************************::onRedisConnectionStatusChangedImpl(): server is connected, stop connection retry timer");
      mRedisConnectionTimeoutMsecs = REDIS_CONNECTION_RETRY_INTERVAL_MSECS;
   }
   else if (args.connectionStatus == RedisConnectionStatus_Disconnected)
   {
      // Nothing to do
      StackLog(<< "********************************::onRedisConnectionStatusChangedImpl(): server is disconnected, stop connection retry timer");
      mRedisConnectionTimeoutMsecs = REDIS_CONNECTION_RETRY_INTERVAL_MSECS;
   }
   else if (args.connectionStatus == RedisConnectionStatus_ConnFailure)
   {
      StackLog(<< "********************************::onRedisConnectionStatusChangedImpl(): connection failure to server: " << mDbSettings.redisIp << ":" << mDbSettings.redisPort);

      // Restart timer
      mRedisConnectionTimer.expires_from_now(mRedisConnectionTimeoutMsecs);
      mRedisConnectionTimer.async_wait(this, REDIS_CONNECTION_TIMER_ID, NULL);

      // Update duration
      if (mRedisConnectionTimeoutMsecs < REDIS_CONNECTION_RETRY_INTERVAL_MAXIMUM_MSECS)
         mRedisConnectionTimeoutMsecs = 2 * mRedisConnectionTimeoutMsecs;
   }
   else
   {
      InfoLog(<< "********************************::onRedisConnectionStatusChangedImpl(): invalid redis connection state: " << args.connectionStatus);
      assert(0);
   }

   return kSuccess;
}

void ********************************::onTimer(unsigned short timerId, void* appState)
{
   // StackLog(<< "********************************::onTimer(): timerId: " << timerId);

   if (timerId == REDIS_CONNECTION_TIMER_ID)
   {
      // StackLog(<< "********************************::onTimer(): redis connection timerId: " << timerId);
      if (mPushDb)
      {
         mPushDb->shutdown();
      }

      // Retry connection
      DebugLog(<< "********************************::onTimer(): timerId: " << timerId << " database server: " << mDbSettings.redisIp << ":" << mDbSettings.redisPort);
      configureDatabaseAccessImpl(mDbSettings);
   }
   else
   {
      InfoLog(<< "********************************::onTimer(): timerId: " << timerId << " is invalid");
      assert(0);
   }
}

PushNotificationServiceManagerInternal* PushNotificationServiceManagerInternal::getInternalInterface(CPCAPI2::Phone* cpcPhone)
{
   return static_cast<PushNotificationServiceManagerInternal*>(PushNotificationServiceManager::getInterface(cpcPhone));
}

cpc::string get_debug_string(const CPCAPI2::PushService::StatusCode& status)
{
   switch (status)
   {
      case StatusCode::StatusCode_Success: return "Success";
      case StatusCode::StatusCode_BadRequest: return "BadRequest";
      case StatusCode::StatusCode_AuthenticationError: return "AuthenticationError";
      case StatusCode::StatusCode_BadMethod: return "BadMethod";
      case StatusCode::StatusCode_ExpiredDeviceToken: return "ExpiredDeviceToken";
      case StatusCode::StatusCode_OversizedPayload: return "OversizedPayload";
      case StatusCode::StatusCode_TooManyRequests: return "TooManyRequests";
      case StatusCode::StatusCode_InternalServerError: return "InternalServerError";
      case StatusCode::StatusCode_ServerUnavailable: return "ServerUnavailable";
      default: return "Unknown";
   }

   return "Unknown";
}

int get_notification_status_code(const CPCAPI2::PushService::StatusCode status)
{
   switch (status)
   {
      case StatusCode::StatusCode_Success: return 200;
      case StatusCode::StatusCode_BadRequest: return 400;
      case StatusCode::StatusCode_AuthenticationError: return 403;
      case StatusCode::StatusCode_BadMethod: return 405;
      case StatusCode::StatusCode_ExpiredDeviceToken: return 410;
      case StatusCode::StatusCode_OversizedPayload: return 413;
      case StatusCode::StatusCode_TooManyRequests: return 429;
      case StatusCode::StatusCode_InternalServerError: return 500;
      case StatusCode::StatusCode_ServerUnavailable: return 503;
      default: return (-1);
   }

   return (-1);
}

CPCAPI2::PushService::StatusCode get_notification_status(int status)
{
   if (status == 200) return StatusCode::StatusCode_Success;
   if (status == 400) return StatusCode::StatusCode_BadRequest;
   if (status == 403) return StatusCode::StatusCode_AuthenticationError;
   if (status == 405) return StatusCode::StatusCode_BadMethod;
   if (status == 410) return StatusCode::StatusCode_ExpiredDeviceToken;
   if (status == 413) return StatusCode::StatusCode_OversizedPayload;
   if (status == 429) return StatusCode::StatusCode_TooManyRequests;
   if (status == 500) return StatusCode::StatusCode_InternalServerError;
   if (status == 503) return StatusCode::StatusCode_ServerUnavailable;

   return StatusCode::StatusCode_Unknown;
}

cpc::string get_debug_string(const CPCAPI2::PushService::ReasonCode& reason)
{
   switch (reason)
   {
      case ReasonCode::ReasonCode_BadCollapseId: return "BadCollapseId";
      case ReasonCode::ReasonCode_BadDeviceToken: return "BadDeviceToken";
      case ReasonCode::ReasonCode_BadExpirationDate: return "BadExpirationDate";
      case ReasonCode::ReasonCode_BadMessageId: return "BadMessageId";
      case ReasonCode::ReasonCode_BadPriority: return "BadPriority";
      case ReasonCode::ReasonCode_BadTopic: return "BadTopic";
      case ReasonCode::ReasonCode_DeviceTokenNotForTopic: return "DeviceTokenNotForTopic";
      case ReasonCode::ReasonCode_DuplicateHeaders: return "DuplicateHeaders";
      case ReasonCode::ReasonCode_IdleTimeout: return "IdleTimeout";
      case ReasonCode::ReasonCode_MissingDeviceToken: return "MissingDeviceToken";
      case ReasonCode::ReasonCode_MissingTopic: return "MissingTopic";
      case ReasonCode::ReasonCode_PayloadEmpty: return "PayloadEmpty";
      case ReasonCode::ReasonCode_TopicDisallowed: return "TopicDisallowed";
      case ReasonCode::ReasonCode_BadCertificate: return "BadCertificate";
      case ReasonCode::ReasonCode_BadCertificateEnvironment: return "BadCertificateEnvironment";
      case ReasonCode::ReasonCode_ExpiredProviderToken: return "ExpiredProviderToke";
      case ReasonCode::ReasonCode_Forbidden: return "Forbidden";
      case ReasonCode::ReasonCode_InvalidProviderToken: return "InvalidProviderToken";
      case ReasonCode::ReasonCode_MissingProviderToken: return "MissingProviderToken";
      case ReasonCode::ReasonCode_BadPath: return "BadPath";
      case ReasonCode::ReasonCode_MethodNotAllowed: return "MethodNotAllowed";
      case ReasonCode::ReasonCode_Unregistered: return "Unregistered";
      case ReasonCode::ReasonCode_PayloadTooLarge: return "PayloadTooLarge";
      case ReasonCode::ReasonCode_TooManyProviderTokenUpdates: return "TooManyProviderTokenUpdates";
      case ReasonCode::ReasonCode_TooManyRequests: return "TooManyRequests";
      case ReasonCode::ReasonCode_InternalServerError: return "InternalServerError";
      case ReasonCode::ReasonCode_ServiceUnavailable: return "ServiceUnavailable";
      case ReasonCode::ReasonCode_Shutdown: return "Shutdown";
      default: return "Unknown";
   }

   return "Unknown";
}

int get_notification_status_code(const CPCAPI2::PushService::ReasonCode reason)
{
   switch (reason)
   {
      case ReasonCode::ReasonCode_BadCollapseId: return 400;
      case ReasonCode::ReasonCode_BadDeviceToken: return 400;
      case ReasonCode::ReasonCode_BadExpirationDate: return 400;
      case ReasonCode::ReasonCode_BadMessageId: return 400;
      case ReasonCode::ReasonCode_BadPriority: return 400;
      case ReasonCode::ReasonCode_BadTopic: return 400;
      case ReasonCode::ReasonCode_DeviceTokenNotForTopic: return 400;
      case ReasonCode::ReasonCode_DuplicateHeaders: return 400;
      case ReasonCode::ReasonCode_IdleTimeout: return 400;
      case ReasonCode::ReasonCode_MissingDeviceToken: return 400;
      case ReasonCode::ReasonCode_MissingTopic: return 400;
      case ReasonCode::ReasonCode_PayloadEmpty: return 400;
      case ReasonCode::ReasonCode_TopicDisallowed: return 400;
      case ReasonCode::ReasonCode_BadCertificate: return 403;
      case ReasonCode::ReasonCode_BadCertificateEnvironment: return 403;
      case ReasonCode::ReasonCode_ExpiredProviderToken: return 403;
      case ReasonCode::ReasonCode_Forbidden: return 403;
      case ReasonCode::ReasonCode_InvalidProviderToken: return 403;
      case ReasonCode::ReasonCode_MissingProviderToken: return 403;
      case ReasonCode::ReasonCode_BadPath: return 404;
      case ReasonCode::ReasonCode_MethodNotAllowed: return 405;
      case ReasonCode::ReasonCode_Unregistered: return 410;
      case ReasonCode::ReasonCode_PayloadTooLarge: return 413;
      case ReasonCode::ReasonCode_TooManyProviderTokenUpdates: return 429;
      case ReasonCode::ReasonCode_TooManyRequests: return 429;
      case ReasonCode::ReasonCode_InternalServerError: return 500;
      case ReasonCode::ReasonCode_ServiceUnavailable: return 503;
      case ReasonCode::ReasonCode_Shutdown: return 503;
      default: return (-1);
   }

   return (-1);
}

std::map<cpc::string, CPCAPI2::PushService::ReasonCode> g_notificationReasonMapping
{
   {"BadCollapseId", ReasonCode::ReasonCode_BadCollapseId},
   {"BadDeviceToken", ReasonCode::ReasonCode_BadDeviceToken},
   {"BadExpirationDate", ReasonCode::ReasonCode_BadExpirationDate},
   {"BadMessageId", ReasonCode::ReasonCode_BadMessageId},
   {"BadPriority", ReasonCode::ReasonCode_BadPriority},
   {"BadTopic", ReasonCode::ReasonCode_BadTopic},
   {"DeviceTokenNotForTopic", ReasonCode::ReasonCode_DeviceTokenNotForTopic},
   {"DuplicateHeaders", ReasonCode::ReasonCode_DuplicateHeaders},
   {"IdleTimeout", ReasonCode::ReasonCode_IdleTimeout},
   {"MissingDeviceToken", ReasonCode::ReasonCode_MissingDeviceToken},
   {"MissingTopic", ReasonCode::ReasonCode_MissingTopic},
   {"PayloadEmpty", ReasonCode::ReasonCode_PayloadEmpty},
   {"TopicDisallowed", ReasonCode::ReasonCode_TopicDisallowed},
   {"BadCertificate", ReasonCode::ReasonCode_BadCertificate},
   {"BadCertificateEnvironment", ReasonCode::ReasonCode_BadCertificateEnvironment},
   {"ExpiredProviderToken", ReasonCode::ReasonCode_ExpiredProviderToken},
   {"Forbidden", ReasonCode::ReasonCode_Forbidden},
   {"InvalidProviderToken", ReasonCode::ReasonCode_InvalidProviderToken},
   {"MissingProviderToken", ReasonCode::ReasonCode_MissingProviderToken},
   {"BadPath", ReasonCode::ReasonCode_BadPath},
   {"MethodNotAllowed", ReasonCode::ReasonCode_MethodNotAllowed},
   {"Unregistered", ReasonCode::ReasonCode_Unregistered},
   {"PayloadTooLarge", ReasonCode::ReasonCode_PayloadTooLarge},
   {"TooManyProviderTokenUpdates", ReasonCode::ReasonCode_TooManyProviderTokenUpdates},
   {"TooManyRequests", ReasonCode::ReasonCode_TooManyRequests},
   {"InternalServerError", ReasonCode::ReasonCode_InternalServerError},
   {"ServiceUnavailable", ReasonCode::ReasonCode_ServiceUnavailable},
   {"Shutdown", ReasonCode::ReasonCode_Shutdown}
};

CPCAPI2::PushService::ReasonCode get_notification_reason(cpc::string reason)
{
   std::map<cpc::string, CPCAPI2::PushService::ReasonCode>::iterator i = g_notificationReasonMapping.find(reason);
   if (i == g_notificationReasonMapping.end()) return ReasonCode_Unknown;
   return (i->second);
}

cpc::string get_debug_string(const CPCAPI2::PushService::NotificationSuccessEvent& event)
{
   std::stringstream ss;
   ss << "device: " << event.device;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushService::NotificationFailureEvent& event)
{
   std::stringstream ss;
   ss << "device: " << event.device << " status: " << event.statusCode << " reason: " << event.reasonCode << " errorString: " << event.errorString;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushService::NotificationServiceRegistrationEvent& event)
{
   std::stringstream ss;
   ss << "device: " << event.device << " notification handle: " << event.service << " success: " << event.success;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::StatusCode& status)
{
   os << CPCAPI2::PushService::get_debug_string(status);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::ReasonCode& reason)
{
   os << CPCAPI2::PushService::get_debug_string(reason);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationSuccessEvent& event)
{
   os << CPCAPI2::PushService::get_debug_string(event);
   return os;
}
 
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationFailureEvent& event)
{
   os << CPCAPI2::PushService::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& event)
{
   os << CPCAPI2::PushService::get_debug_string(event);
   return os;
}

}

}

#endif
