#include "brand_branded.h"

#include <servicenotifications_server/ServiceNotificationsManager.h>

#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)
#include "ServiceNotificationsManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace ServiceNotifications
   {
      ServiceNotificationsManager* ServiceNotificationsManager::getInterface( CPCAPI2::Phone* cpcPhone )
      {
#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface< ServiceNotificationsManagerInterface >( phone, "ServiceNotificationsManager" );
#else
         return NULL;
#endif
      }
   }
}
