#include "ServiceNotificationsManagerInterface.h"

#include <brand_branded.h>

#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)

#include <mutex>
#include <condition_variable>

#include <sstream>

#include <xmpp/XmppAccountInterface.h>

#include <servicenotifications_server/ServiceNotificationsHandler.h>
#include <servicenotifications_server/ServiceNotificationsSettings.h>
#include <servicenotifications_server/ServiceNotificationsInternalTypes.h>
#include <pushnotification_server/PushNotificationServerManager.h>

#include <phone/PhoneInterface.h>
#include "util/cpc_logger.h"

#include "ServiceNotificationsManagerImpl.h"

using namespace CPCAPI2::ServiceNotifications;
using CPCAPI2::PushNotification::PushNotificationDeviceHandle;
using CPCAPI2::XmppAccount::XmppAccountManager;
using CPCAPI2::XmppAccount::XmppAccountInterface;

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   if( info->pImpl != NULL ) \
   { \
      ErrorEvent evt; \
      evt.errorText = ( ERRTEXT ); \
      info->pImpl->fireEvent( cpcFunc( ServiceNotificationsHandler::onError ), evt ); \
   } \
   return kError; \
}
#define INVALID_ACCOUNT_HANDLE_STR  "Invalid Account Handle"

std::atomic< ServiceNotificationsHandle > ServiceNotificationsManagerInterface::s_CurrentHandle( 1 );

ServiceNotificationsManagerInterface::ServiceNotificationsManagerInterface(CPCAPI2::Phone* phone) :
   m_Shutdown( false ),
   m_Phone(phone),
   m_PhoneInterface( dynamic_cast<PhoneInterface*>( phone )),
   m_Reactor( m_PhoneInterface->getSdkModuleThread() )
{
}

ServiceNotificationsManagerInterface::~ServiceNotificationsManagerInterface()
{
   m_Shutdown = true;

   // Free everything in the info map
   std::map< ServiceNotificationsHandle, ServiceNotificationsInfo* >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL )
         delete iter->second->pImpl;

      delete iter->second;
      ++iter;
   }
   m_InfoMap.clear();
}

ServiceNotificationsInfo *ServiceNotificationsManagerInterface::getAccountInfo( ServiceNotificationsHandle hService )
{
   std::map< ServiceNotificationsHandle, ServiceNotificationsInfo* >::const_iterator iter( m_InfoMap.find( hService ));
   if( iter == m_InfoMap.end() )
      return NULL;

   return iter->second;
}

ServiceNotificationsHandle ServiceNotificationsManagerInterface::create()
{
   ServiceNotificationsHandle hService = s_CurrentHandle.fetch_add( 1 );
   m_Reactor.post( resip::resip_bind( &ServiceNotificationsManagerInterface::createImpl, this, hService ));
   return hService;
}

int ServiceNotificationsManagerInterface::createImpl( const ServiceNotificationsHandle& hService )
{
   ServiceNotificationsInfo *info = getAccountInfo( hService );
   if( info != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   XmppAccountInterface* xmppAcctIf = NULL;
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   xmppAcctIf = dynamic_cast<XmppAccountInterface*>(XmppAccountManager::getInterface( m_Phone ));
#endif

   info         = new ServiceNotificationsInfo;
   info->handle = hService;
   info->pImpl  = new ServiceNotificationsManagerImpl(m_Phone, hService, xmppAcctIf, mPushServiceMgr, m_Reactor, m_CallbackFifo);
   m_InfoMap[ hService ] = info;
   return kSuccess;
}

void ServiceNotificationsManagerInterface::Release()
{
   delete this; // suicide
}

int ServiceNotificationsManagerInterface::destroy(ServiceNotificationsHandle hService)
{
   m_Reactor.post( resip::resip_bind( &ServiceNotificationsManagerInterface::destroyImpl, this, hService ));
   return kSuccess;
}

int ServiceNotificationsManagerInterface::destroyImpl(ServiceNotificationsHandle hService)
{
   ServiceNotificationsInfo *info = getAccountInfo( hService );
   if( info == NULL )
      return kError;

   delete info->pImpl;
   delete info;

   m_InfoMap.erase( hService );
   return kSuccess;
}

int ServiceNotificationsManagerInterface::setHandler(ServiceNotificationsHandle hService, ServiceNotificationsHandler* handler)
{
   int result = kError;
   if( handler != NULL )
   {
      // Just do the normal thing.
      m_Reactor.post( resip::resip_bind( &ServiceNotificationsManagerInterface::setHandlerImpl, this, hService, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( ServiceNotificationsManagerInterface *parent, ServiceNotificationsHandle& hService, ServiceNotificationsHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhAccount( hService ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhAccount, mHandler );
            mCVar.notify_all();
         }

         ServiceNotificationsManagerInterface *mParent;
         ServiceNotificationsHandle& mhAccount;
         ServiceNotificationsHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hService, handler, mutex, cvar, result );
         m_Reactor.post( resip::resip_bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int ServiceNotificationsManagerInterface::setPushNotificationServiceManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager)
{
   m_Reactor.post( resip::resip_bind( &ServiceNotificationsManagerInterface::setPushNotificationServiceManagerImpl, this, pushManager ));
   return kSuccess;
}

int ServiceNotificationsManagerInterface::setPushNotificationServiceManagerImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushManager)
{
   mPushServiceMgr = pushManager;
   return kSuccess;
}

int ServiceNotificationsManagerInterface::setHandlerImpl(ServiceNotificationsHandle hService, ServiceNotificationsHandler* handler)
{
   ServiceNotificationsInfo *info = getAccountInfo( hService );
   if( info == NULL )
      return kError;

   return info->pImpl->setHandler( handler );
}

int ServiceNotificationsManagerInterface::notify( ServiceNotificationsHandle hService, CPCAPI2::PushEndpoint::PushNotificationEndpointId hDevice, const ServiceEvent& evt )
{
   m_Reactor.post( resip::resip_bind( &ServiceNotificationsManagerInterface::notifyImpl, this, hService, hDevice, evt ));
   return kSuccess;
}

int ServiceNotificationsManagerInterface::notifyImpl( ServiceNotificationsHandle hService, CPCAPI2::PushEndpoint::PushNotificationEndpointId hDevice, const ServiceEvent& evt )
{
   ServiceNotificationsInfo *info = getAccountInfo( hService );
   if( info == NULL )
      return kError;

   return info->pImpl->notify( hDevice, evt );
}

int ServiceNotificationsManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* ServiceNotificationsManagerInterface::process_test(int timeout)
{
   if( m_Shutdown )
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}
#endif // CPCAPI2_AUTO_TEST

#endif // CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE
