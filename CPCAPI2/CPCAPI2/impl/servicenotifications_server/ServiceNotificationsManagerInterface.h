#pragma once
#ifndef __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERINTERFACE_H__
#define __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERINTERFACE_H__

#include <atomic>
#include <thread>
#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <phone/PhoneModule.h>
#include <servicenotifications_server/ServiceNotificationsManager.h>
#include <servicenotifications_server/ServiceNotificationsHandler.h>
#include <push_endpoint/PushNotificationCommonTypes.h>
#include "../util/AutoTestProcessor.h"
#include <phone/NetworkChangeManagerImpl.h>
#include <xmpp/XmppAccountHandlerInternal.h>

namespace CPCAPI2
{
   struct NetworkChangeEvent;

   namespace ServiceNotifications
   {
      // Forward decl's
      struct ServiceNotificationsInfo;
      typedef struct ServiceNotificationsInfo ServiceNotificationsInfo;         

      class ServiceNotificationsManagerInterface :
         public ServiceNotificationsManager,
         public CPCAPI2::PhoneModule
#ifdef CPCAPI2_AUTO_TEST
         , public AutoTestProcessor
#endif
      {
      public:
         ServiceNotificationsManagerInterface(Phone* phone);
         virtual ~ServiceNotificationsManagerInterface();

         ServiceNotificationsHandle create( void ) OVERRIDE;
         int setHandler( ServiceNotificationsHandle hService, ServiceNotificationsHandler* handler ) OVERRIDE;
         int setPushNotificationServiceManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager) OVERRIDE;
         int notify( ServiceNotificationsHandle hService, CPCAPI2::PushEndpoint::PushNotificationEndpointId hDevice, const ServiceEvent& evt );
         int destroy( ServiceNotificationsHandle hService ) OVERRIDE;
         int process( unsigned int timeout ) OVERRIDE;

#ifdef CPCAPI2_AUTO_TEST
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

         // PhoneModule implementation
         void Release() OVERRIDE;

         // Methods which are dispatched to the io_service
         int createImpl( const ServiceNotificationsHandle& hService );
         int setHandlerImpl(ServiceNotificationsHandle hService, ServiceNotificationsHandler* handler);
         int setPushNotificationServiceManagerImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushManager);
         int notifyImpl( ServiceNotificationsHandle hService, CPCAPI2::PushEndpoint::PushNotificationEndpointId hDevice, const ServiceEvent& evt );
         int destroyImpl(ServiceNotificationsHandle hService);
         int onAccountStatusChangedImpl(XmppAccount::XmppAccountHandle account, const XmppAccount::XmppAccountStatusChangedEvent& args);

         // returns a pointer (not owned) into the ServiceNotificationsInfo structure
         // for the corresponding handle
         ServiceNotificationsInfo *getAccountInfo( ServiceNotificationsHandle hService );

      private: // data

         // Static handle counter
         static std::atomic< ServiceNotificationsHandle > s_CurrentHandle;

         // Map of ServiceNotificationsHandle(s) to ServiceNotificationsInfo(s)
         std::map< ServiceNotificationsHandle, ServiceNotificationsInfo* > m_InfoMap;

         // Callback Fifo which should be used to marshal the events (process method should be called)
         resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;

         CPCAPI2::Phone* m_Phone;
         CPCAPI2::PhoneInterface *m_PhoneInterface;
         CPCAPI2::PushService::PushNotificationServiceManager* mPushServiceMgr;

         // synonymous with the asio::io_service, but homebrewed.
         // Initialized from the PhoneInterface's reactor
         resip::MultiReactor& m_Reactor;
      };
   }
}

#endif // __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERINTERFACE_H__
