#include "ServiceNotificationsManagerImpl.h"

#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)

#include <xmpp/XmppAccountInterface.h>

#include <servicenotifications_server/ServiceNotificationsHandler.h>
#include <pushnotification_server/PushNotificationServerManager.h>

#include "ServiceNotificationsInternalTypes.h"

using namespace CPCAPI2::ServiceNotifications;
using CPCAPI2::PushEndpoint::PushNotificationEndpointId;
using CPCAPI2::XmppAccount::XmppAccountHandle;
using CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent;

// Timeout for service down events (service must be down for this long before
// the actual timeout is fired).
#define SERVICE_TIMEOUT_MILLIS ( 2 * 60 * 1000 )
#define SERVICE_DOWN_ID 1
#define SERVICE_UP_ID 2

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   ErrorEvent evt; \
   evt.errorText = ( ERRTEXT ); \
   fireEvent( cpcFunc( ServiceNotificationsHandler::onError ),evt ); \
   return kError; \
}
#define INVALID_ACCOUNT_STATE_STR   "Invalid Account State"
#define INVALID_ACCOUNT_HANDLER_STR "Account Handler Already Set"

ServiceNotificationsManagerImpl::ServiceNotificationsManagerImpl(
   CPCAPI2::Phone *pPhone,
   ServiceNotificationsHandle hService,
   CPCAPI2::XmppAccount::XmppAccountInterface *xmppAcctIf,
   CPCAPI2::PushService::PushNotificationServiceManager* pushMgr,
   resip::MultiReactor& reactor,
   resip::Fifo< resip::ReadCallbackBase >& callbackFifo ) :
     m_RequestHandle( 1 ),
     m_CallbackFifo( callbackFifo ),
     m_hService( hService ),
     m_Reactor( reactor ),
     m_Handler( NULL ),
     m_Phone( pPhone ),
     m_XmppAccountInterface( xmppAcctIf ),
     m_PushNotificationServerManager( pushMgr )
{
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   if( m_XmppAccountInterface != NULL )
      m_XmppAccountInterface->addSdkObserver( this );
#endif
}

ServiceNotificationsManagerImpl::~ServiceNotificationsManagerImpl()
{
#if (CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1)
   if( m_XmppAccountInterface != NULL )
      m_XmppAccountInterface->removeSdkObserver( this );
#endif

   m_Handler = NULL;
}

int ServiceNotificationsManagerImpl::configureSettings(const ServiceNotificationsSettings& settings)
{
   // Always update the settings
   m_Settings = settings;
   return kSuccess;
}

int ServiceNotificationsManagerImpl::setHandler(ServiceNotificationsHandler* handler)
{
   // Ensure handler isn't already set.
   if( m_Handler != NULL && handler != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLER_STR );

   m_Handler = handler;
   return kSuccess;
}

static bool ServiceStateToString( const ServiceState& state, std::string& outVal )
{
   switch( state )
   {
   case ServiceState_Starting:
      outVal = "Starting";
      break;
   case ServiceState_Started:
      outVal = "Started";
      break;
   case ServiceState_Stopping:
      outVal = "Stopping";
      break;
   case ServiceState_Stopped:
      outVal = "Stopped";
      break;
   default:
      outVal = "Unknown";
      break;
   }
   return true;
}

int ServiceNotificationsManagerImpl::notify(PushNotificationEndpointId hDevice, const ServiceEvent & evt)
{
   using namespace CPCAPI2::PushService;

   if( m_PushNotificationServerManager != NULL )
   {
      // Build a document from the event contents.
      PushNotificationRequest pushRequest;

      std::string stateVal;
      ServiceStateToString( evt.state, stateVal );

      pushRequest.customData.push_back( CustomDataField( "event", "service" ));
      pushRequest.customData.push_back( CustomDataField( "state", stateVal.c_str() ));
      pushRequest.customData.push_back( CustomDataField( "reasonCode", ( int64_t ) evt.reason ));
      pushRequest.customData.push_back( CustomDataField( "serviceName", evt.serviceName.c_str() ));

      // Send the notification
      m_PushNotificationServerManager->sendPushNotification( hDevice, pushRequest );
   }
   return kSuccess;
}

int ServiceNotificationsManagerImpl::onAccountStatusChanged( XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
   XmppStateMap::iterator iter;

   if( args.accountStatus == XmppAccountStatusChangedEvent::Status_Disconnected ||
       args.accountStatus == XmppAccountStatusChangedEvent::Status_Failure )
   {
      // Try to determine whether the event happened because of an
      // error which will impact the xmpp service as a whole
      switch( args.errorCode )
      {
      //case XmppAccount::Error_None:
      //case XmppAccount::Error_NoHandlerSet:
      case XmppAccount::Error_IoError:
      case XmppAccount::Error_DnsError:
      case XmppAccount::Error_HostNotFound:
      case XmppAccount::Error_ConnectionRefused:
      //case XmppAccount::Error_AlreadyEnabled:
      //case XmppAccount::Error_NotEnabled:
      //case XmppAccount::Error_AlreadyConnected:
      //case XmppAccount::Error_NotConnected: // ?
      //case XmppAccount::Error_ParseError: // ?
      //case XmppAccount::Error_StreamError: // ?
      case XmppAccount::Error_TlsFailed:
      case XmppAccount::Error_CompressionFailed:
      case XmppAccount::Error_UnsupportedAuthMech:
      //case XmppAccount::Error_AuthenticationFailed: // ?
         iter = m_XmppAccountStates.find( account );
         if( iter == m_XmppAccountStates.end() )
         {
            // No entry, create one, and start a timer.
            XmppAccountStateInfo info;
            info.isServiceDown = true;
            info.hAccount = new XmppAccountHandle( account );
            info.mServiceTimer = new resip::DeadlineTimer< resip::MultiReactor >( m_Reactor );
            info.mServiceTimer->expires_from_now( SERVICE_TIMEOUT_MILLIS );
            info.mServiceTimer->async_wait( this, SERVICE_DOWN_ID, info.hAccount ); // NB: remember to delete this
            m_XmppAccountStates[ account ] = info;
         }
         break;
      default:
         // Some error which is considered recoverable
         break;
      }
   }
   else if( args.accountStatus == XmppAccountStatusChangedEvent::Status_Connected )
   {
      iter = m_XmppAccountStates.find( account );
      if( iter != m_XmppAccountStates.end() )
      {
         // Cancel any existing timer. Remove the entry from the map
         XmppAccountStateInfo& info( iter->second );

         if( info.mServiceTimer )
            info.mServiceTimer->cancel();

         delete info.mServiceTimer;
         info.mServiceTimer = NULL;

         delete info.hAccount;
         info.hAccount = NULL;

         m_XmppAccountStates.erase( iter );
      }
   }

   return kSuccess;
}

void ServiceNotificationsManagerImpl::onTimer( unsigned short timerId, void* appState )
{
   XmppAccountHandle *pHandle = ( XmppAccountHandle* ) appState;
   if( pHandle == NULL )
      return;

   XmppStateMap::iterator iter = m_XmppAccountStates.find( *pHandle );
   if( iter == m_XmppAccountStates.end() )
      return; // This shouldn't be possible because the timer itself is stored here.

   XmppAccountStateInfo& info( iter->second );

   if( timerId == SERVICE_DOWN_ID )
   {
      // mark it down and fire an event
      info.isServiceDown = true;

      // This is kind of dangerous (broadcast)
      ServiceEvent evt;
      evt.serviceName = "XMPP_ACCOUNT";
      evt.state = ServiceState_Stopped;
      evt.reason = ReasonCode_Unknown;
      //notifyAll( evt );
   }
   else if( timerId == SERVICE_UP_ID )
   {
      // mark it up. For the time being we don't fire anything here.
      info.isServiceDown = false;
   }

   // Make sure the timer is cleaned up.
   if( info.mServiceTimer )
      info.mServiceTimer->cancel();

   delete info.mServiceTimer;
   info.mServiceTimer = NULL;

   delete info.hAccount;
   info.hAccount = NULL;
}

#endif // CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE
