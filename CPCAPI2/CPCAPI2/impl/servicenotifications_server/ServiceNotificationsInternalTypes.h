#pragma once
#ifndef __CPCAPI2_SERVICE_NOTIFICATIONS_INTERNAL_TYPES_H__
#define __CPCAPI2_SERVICE_NOTIFICATIONS_INTERNAL_TYPES_H__

#include <brand_branded.h>
#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)

#include <servicenotifications_server/ServiceNotificationsTypes.h>

namespace CPCAPI2
{
   namespace ServiceNotifications
   {
      class ServiceNotificationsManagerImpl;

      // Information about the current account, keyed by handle
      typedef struct ServiceNotificationsInfo
      {
         ServiceNotificationsInfo() :
            handle(( ServiceNotificationsHandle ) -1 ),
            pImpl( NULL )
            {}

         // Handle related to this account
         ServiceNotificationsHandle handle;

         // Pointer to the implementation of the interface
         ServiceNotificationsManagerImpl *pImpl;
      } ServiceNotificationsInfo;
   }
}

#endif // CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE
#endif // __CPCAPI2_SERVICE_NOTIFICATIONS_INTERNAL_TYPES_H__
