#pragma once
#ifndef __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERIMPL_H__
#define __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERIMPL_H__

#include <brand_branded.h>
#if (CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE == 1)

#include <map>
#include <atomic>
#include <boost/asio.hpp>

#include <websocket/WebSocketHandler.h>
#include <websocket/json/WebSocketCommand.h>
#include <websocket/WebSocketStateMachine.h>
#include <websocket/WebSocketStateDispatcher.h>

#include <phone/PhoneModule.h>
#include <phone/NetworkChangeHandler.h>
#include <push_endpoint/PushNotificationCommonTypes.h>

#include <xmpp/XmppAccountHandlerInternal.h>
#include <xmpp/XmppAccountSyncHandler.h>

#include <servicenotifications_server/ServiceNotificationsManager.h>
#include <servicenotifications_server/ServiceNotificationsSettings.h>
#include <servicenotifications_server/ServiceNotificationsSyncHandler.h>
#include <servicenotifications_server/ServiceNotificationsHandler.h>

#include <rutil/DeadlineTimer.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#include "../util/DumFpCommand.h"

#if defined(TARGET_OS_IPHONE) && (TARGET_OS_IPHONE==1)
#include <CFNetwork/CFNetwork.h>
#endif

namespace CPCAPI2
{
   namespace XmppAccount
   {
      class XmppAccountInterface;
   }

   namespace ServiceNotifications
   {
      class AccountState;
      struct ServiceNotificationsInfo;
      typedef struct ServiceNotificationsInfo ServiceNotificationsInfo;

      class ServiceNotificationsManagerImpl :
          public CPCAPI2::XmppAccount::XmppAccountHandlerInternal,
          public CPCAPI2::XmppAccount::XmppAccountSyncHandler,
          public resip::DeadlineTimerHandler
      {
      public:

         ServiceNotificationsManagerImpl(
            CPCAPI2::Phone *pPhone,
            ServiceNotificationsHandle hService,
            CPCAPI2::XmppAccount::XmppAccountInterface *xmppAcctIf,
            CPCAPI2::PushService::PushNotificationServiceManager* pushMgr,
            resip::MultiReactor& reactor,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo );
         virtual ~ServiceNotificationsManagerImpl();

         // dispatched methods implementation
         int configureSettings(const ServiceNotificationsSettings& settings);
         int setHandler(ServiceNotificationsHandler* handler);
         int notify(CPCAPI2::PushEndpoint::PushNotificationEndpointId hDevice, const ServiceEvent& evt);

         // XmppAccountHandler(Internal)
         int onAccountStatusChanged(XmppAccount::XmppAccountHandle account, const XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
         int onError(XmppAccount::XmppAccountHandle account, const XmppAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }
         int onEntityTime(XmppAccount::XmppAccountHandle account, const XmppAccount::EntityTimeEvent& args) OVERRIDE { return kSuccess; }
         int onEntityFeature(XmppAccount::XmppAccountHandle account, const XmppAccount::EntityFeatureEvent& args) OVERRIDE { return kSuccess; }
         int onAccountConfigured( XmppAccount::XmppAccountHandle account, const XmppAccount::XmppAccountConfiguredEvent& args ) OVERRIDE { return kSuccess; }

         // DeadlineTimerHandler
         void onTimer( unsigned short timerId, void* appState ) OVERRIDE;

         // Accessor methods
         const ServiceNotificationsSettings& getSettings() { return m_Settings; }
         const ServiceNotificationsHandle getHandle() { return m_hService; }
         WebSocket::RequestHandle nextRequestHandle() { return m_RequestHandle.fetch_add( 1 ); }

         // Funky macro for firing events
         template<typename TFn, typename TEvt> void fireEvent(
            const char* funcName,
            TFn func,
            const TEvt& args)
         {
            if( m_Handler == NULL )
               return;

            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_hService, args);
            if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<ServiceNotificationsSyncHandler*>( m_Handler ) != NULL)
            {
               // Invoke the callback synchronously in these cases
               ( *cb )();
               delete cb;
            }
            else
            {
               // Object "cb" should be deleted by whomever is processing this FIFO
               m_CallbackFifo.add( cb );
            }
         }

      private: // data

         std::atomic< WebSocket::RequestHandle > m_RequestHandle;

         resip::MultiReactor& m_Reactor; // passed into websocketpp lib
         resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo; // shared between Interface and Impl

         const ServiceNotificationsHandle m_hService;

         // Pointer to the settings for this account (owned)
         ServiceNotificationsSettings m_Settings;

         // Application handler for account related activity
         ServiceNotificationsHandler *m_Handler;

         // Handle to the Phone Interface
         CPCAPI2::Phone *m_Phone;

         // Handle to the XMPP Account Interface
         CPCAPI2::XmppAccount::XmppAccountInterface *m_XmppAccountInterface;

         // Handle to the correct push notification server manager
         CPCAPI2::PushService::PushNotificationServiceManager *m_PushNotificationServerManager;

         // Map to track states of the XMPP accounts to fire service events
         typedef struct XmppAccountStateInfo
         {
            XmppAccountStateInfo( void ) : mServiceTimer( NULL ), hAccount( NULL ) {}

            resip::DeadlineTimer< resip::MultiReactor > *mServiceTimer;
            bool isServiceDown;
            XmppAccount::XmppAccountHandle *hAccount;
         } XmppAccountStateInfo;

         typedef std::map< XmppAccount::XmppAccountHandle, XmppAccountStateInfo > XmppStateMap;
         XmppStateMap m_XmppAccountStates;
      };
   }
}

#endif // CPCAPI2_BRAND_SERVICE_NOTIFICATIONS_MODULE
#endif // __CPCAPI2_SERVICE_NOTIFICATIONS_MANAGERIMPL_H__
