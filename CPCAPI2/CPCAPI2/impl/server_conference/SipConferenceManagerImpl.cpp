#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#include "SipConferenceManagerImpl.h"
#include "conference/SipConferenceHandler.h"
#include "SipConferenceManagerInterface.h"
#include "cpcapi2utils.h"
#include "../util/buffer.h"
#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

using namespace resip;
using namespace CPCAPI2::SipAccount;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_EVENT

namespace CPCAPI2
{
namespace SipConference
{
SipConferenceManagerImpl::SipConferenceManagerImpl(std::shared_ptr<AccountMap> parentMap, SipAccountImpl& account, CPCAPI2::SipConference::SipConferenceManagerInterface* sipEventIf)
   : mParentMap(parentMap), mAccount(account), mConfIf(sipEventIf)
{
   mAccount.registerAccountAwareFeature(this);
}

SipConferenceManagerImpl::~SipConferenceManagerImpl()
{
   mAccount.unregisterAccountAwareFeature(this);
}

void SipConferenceManagerImpl::release()
{
   if (std::shared_ptr<AccountMap> parentMap = mParentMap.lock())
   {
      parentMap->erase(mAccount.getHandle());
   }
   delete this;
}

void SipConferenceManagerImpl::setHandler(SipConferenceHandler* handler)
{
    if (handler == NULL) 
    {
    }
    else
    {
    }
}

//
// IAccountAware
//
int SipConferenceManagerImpl::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   return kSuccess;
}

int SipConferenceManagerImpl::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   InfoLog(<< "SipConferenceManagerImpl::addHandlers(..)");
   // the recon ConversationManager will get its copy of DUM from the UserAgent
   mDum = dum;
   
   //HandlerMap::const_iterator it = mAppHandlers.begin();
   //for (; it != mAppHandlers.end(); ++it)
   //{
   //   mDum->addClientSubscriptionHandler(it->first, this);
   //   mDum->addServerSubscriptionHandler(it->first, this);
   //}

   return kSuccess;
}

int SipConferenceManagerImpl::onDumBeingDestroyed()
{
   InfoLog(<< "SipConferenceManagerImpl::onDumBeingDestroyed()");
   if (mDum.get() != NULL) // if account was created but never enabled
   {
   }
   mDum.reset();
   return kSuccess;
}

class SipConferenceManagerAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
{
public:
   SipConferenceManagerAppDialogFactoryDelegate(SipConferenceManagerImpl* pParent)
      : mImpl(pParent) {};

   // accept anything (NOTE: implies a priority/order)
   virtual bool isMyMessage(const resip::SipMessage& msg) { return msg.method() == resip::SUBSCRIBE; };
   virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
   {
      //return new SipEventSubscriptionCreationInfo(dum, mImpl);
      return NULL;
   }

private:
   SipConferenceManagerImpl* mImpl;
};

int SipConferenceManagerImpl::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{
   // this part runs before addHandlers
   //std::shared_ptr<SipEventManagerAppDialogFactoryDelegate> pTemp(new SipEventManagerAppDialogFactoryDelegate(this));
   //factory.addDelegate(pTemp, true);
   return kSuccess;
}


}
}
#endif // CPCAPI2_BRAND_CALL_MODULE
