#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_CONFERENCE_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "conference/SipConferenceManager.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"

#include <map>
#include <string>
#include <vector>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipConversation
{
class SipAVConversationManagerInterface;
}

namespace SipConference
{
class SipConferenceManagerImpl;

class SipConferenceManagerInterface : public SipConferenceManager, public PhoneModule
{
public:
   SipConferenceManagerInterface(Phone* phone);
   virtual ~SipConferenceManagerInterface();

   virtual void Release() OVERRIDE;

   /**
   * Set the handler.
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConferenceHandler* handler) OVERRIDE;

   virtual CPCAPI2::SipConversation::SipConversationHandle createServerConference(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int setConferenceFactoryAddress(CPCAPI2::SipConversation::SipConversationHandle conference, const cpc::string& confFactoryAddress) OVERRIDE;
   virtual int start(CPCAPI2::SipConversation::SipConversationHandle conference) OVERRIDE;
   virtual int addToServerConference(CPCAPI2::SipConversation::SipConversationHandle conference,
                                        CPCAPI2::SipConversation::SipConversationHandle conversation) OVERRIDE;



private:
   int setHandlerImpl(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipConferenceHandler* handler);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mConv;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipConferenceManagerImpl*> AccountMap;
   std::shared_ptr<AccountMap> mAccountMap;
};
}
}
#endif // CPCAPI2_SIP_CONFERENCE_MANAGER_INTERFACE_H
