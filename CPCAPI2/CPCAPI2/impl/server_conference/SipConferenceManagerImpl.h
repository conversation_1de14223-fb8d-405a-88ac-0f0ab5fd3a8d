#pragma once

#if !defined(CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H)
#define CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "conference/SipConferenceManager.h"
#include "conference/SipConferenceHandler.h"
#include "../account/SipAccountImpl.h"
#include "../account/SipAccountAwareFeature.h"
//#include "SipEventSubscriptionCreationInfo.h"

#include <resip/dum/SubscriptionHandler.hxx>

namespace CPCAPI2
{
namespace SipConference
{
class SipConferenceHandler;
class SipConferenceManagerInterface;

class SipConferenceManagerImpl : public CPCAPI2::SipAccount::SipAccountAwareFeature
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipConferenceManagerImpl*> AccountMap;
   SipConferenceManagerImpl(std::shared_ptr<AccountMap> parentMap, CPCAPI2::SipAccount::SipAccountImpl& account, CPCAPI2::SipConference::SipConferenceManagerInterface* sipEventIf);
   virtual ~SipConferenceManagerImpl();

   void setHandler(SipConferenceHandler* handler);

   //
   // IAccountAware
   //
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE;
   virtual int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory) OVERRIDE;

   resip::SharedPtr<resip::DialogUsageManager> getDum() { return mDum; }

private:

private:
   std::weak_ptr<AccountMap> mParentMap;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   typedef std::map<resip::Data, SipConferenceHandler*> HandlerMap;
   HandlerMap mAppHandlers;
   resip::SharedPtr<resip::DialogUsageManager> mDum;
   CPCAPI2::SipConference::SipConferenceManagerInterface* mConfIf;
};
}
}
#endif // CPCAPI2_SIP_CONFERENCE_MANAGER_IMPL_H
