#include "brand_branded.h"

#include "interface/experimental/conference/SipConferenceManager.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#include "SipConferenceManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipConference
{

SipConferenceManager* SipConferenceManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipConferenceManagerInterface>(phone, "SipConferenceManagerInterface");
#else
   return NULL;
#endif
}

}
}
