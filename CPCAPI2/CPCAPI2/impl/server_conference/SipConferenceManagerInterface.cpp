#include "brand_branded.h"

#if (CPCAPI2_BRAND_CONFERENCE_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipConferenceManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../call/SipAVConversationManagerInterface.h"
#include "SipConferenceManagerImpl.h"
#include "../util/ResipConv.h"

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using resip::ReadCallbackBase;

namespace CPCAPI2
{
namespace SipConference
{
SipConferenceManagerInterface::SipConferenceManagerInterface(Phone* phone)
   : mAccountIf(NULL),
     mAccountMap(new AccountMap)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mConv = dynamic_cast<SipAVConversationManagerInterface*>(SipConversationManager::getInterface(phone));
   mPhone = dynamic_cast<PhoneInterface*>(phone);
}

SipConferenceManagerInterface::~SipConferenceManagerInterface()
{
   mAccountMap->clear();
}
   
void SipConferenceManagerInterface::Release()
{
   delete this;
}

int SipConferenceManagerInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConferenceHandler* handler)
{
   ReadCallbackBase* setHandlerCmd = resip::resip_bind(&SipConferenceManagerInterface::setHandlerImpl, this, account, handler);
   
   if (handler == NULL)
   {
      mAccountIf->executeOnSdkThread(setHandlerCmd);
      mAccountIf->process(-1);
   }
   else
   {
      mAccountIf->postToSdkThread(setHandlerCmd);
   }
   
   return kSuccess;
}

int SipConferenceManagerInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipConferenceHandler* handler)
{
   AccountMap::iterator it = mAccountMap->find(account);
   SipConferenceManagerImpl* evtMan = (it == mAccountMap->end() ? NULL : it->second);
   if (evtMan == NULL && handler != NULL)
   {
      if (SipAccountImpl* acct = mAccountIf->getAccountImpl(account))
      {
         evtMan = new SipConferenceManagerImpl(mAccountMap, *acct, this);
         (*mAccountMap)[account] = evtMan;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SipEventManager::setHandler");
         return kError;
      }
   }
   if (evtMan != NULL)
   {
      evtMan->setHandler(handler);
   }
   return kSuccess;
}

CPCAPI2::SipConversation::SipConversationHandle SipConferenceManagerInterface::createServerConference(CPCAPI2::SipAccount::SipAccountHandle account)
{
   return mConv->createConversation(account);
}

int SipConferenceManagerInterface::setConferenceFactoryAddress(CPCAPI2::SipConversation::SipConversationHandle conference, const cpc::string& confFactoryAddress)
{
   return mConv->addParticipant(conference, confFactoryAddress);
}

int SipConferenceManagerInterface::start(CPCAPI2::SipConversation::SipConversationHandle conference)
{
   return mConv->start(conference);
}

int SipConferenceManagerInterface::addToServerConference(CPCAPI2::SipConversation::SipConversationHandle conference, 
                                       CPCAPI2::SipConversation::SipConversationHandle conversation)
{
   return mConv->transfer(conversation, conference, false);
}




}
}
#endif
