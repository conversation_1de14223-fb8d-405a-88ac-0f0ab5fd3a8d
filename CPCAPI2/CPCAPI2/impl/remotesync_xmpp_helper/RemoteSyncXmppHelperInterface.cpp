#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE == 1)
#include "RemoteSyncXmppHelperInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "xmpp/XmppChatManagerInterface.h"
#include "xmpp/jsonapi/XmppChatJsonProxyInterface.h"
#include "xmpp/jsonapi/XmppAccountJsonProxyInterface.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "log/LocalLogger.h"

#include <libxml/xpath.h>
#include <libxml/xpathInternals.h>
#include "../util/LibxmlSharedUsage.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_SYNC
#define XMPP_UNIQUE_ID_MIN_SIGNIFICANT_LENGTH 8

#define CP_LOCAL_LOGGER_VAR mLocalLogger

using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppChat;

namespace CPCAPI2
{
namespace RemoteSyncXmppHelper
{
RemoteSyncXmppHelperInterface::RemoteSyncXmppHelperInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mLocalLogger(dynamic_cast<PhoneInterface*>(phone)->localLogger())
{
   mPhone->addRefImpl();

   XmppChatManager* xmppChatMgr = XmppChatManager::getInterface(phone);
   if (xmppChatMgr != NULL)
   {
      dynamic_cast<XmppChatManagerInterface*>(xmppChatMgr)->addSdkObserver(this);
   }

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   XmppChatManagerJsonProxy* xmppChatJson = XmppChatManagerJsonProxy::getInterface(phone);
   if (xmppChatJson != NULL)
   {
      dynamic_cast<XmppChatJsonProxyInterface*>(xmppChatJson)->addSdkObserver(this);
   }
#endif

   XmppAccount::XmppAccountInterface* xmppAcctMgr = dynamic_cast<XmppAccount::XmppAccountInterface*>(XmppAccount::XmppAccountManager::getInterface(phone));
   if (xmppAcctMgr != NULL)
   {
      xmppAcctMgr->addSdkObserver(this);
   }

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   XmppAccount::XmppAccountManagerJsonProxy* xmppAcctJson = dynamic_cast<XmppAccount::XmppAccountManagerJsonProxy*>(XmppAccount::XmppAccountManagerJsonProxy::getInterface(phone));
   if (xmppAcctJson != NULL)
   {
      dynamic_cast<XmppAccountJsonProxyInterface*>(xmppAcctJson)->addSdkObserver(this);
   }
#endif
}

RemoteSyncXmppHelperInterface::~RemoteSyncXmppHelperInterface()
{
   mPhone->releaseImpl();
}

void RemoteSyncXmppHelperInterface::Release()
{
   //XmppChatManager* xmppChatMgr = XmppChatManager::getInterface(mPhone);
   //dynamic_cast<XmppChatManagerInterface*>(xmppChatMgr)->removeSdkObserver(this);

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
   XmppChatManagerJsonProxy* xmppChatJson = XmppChatManagerJsonProxy::getInterface(mPhone);
   if (xmppChatJson != NULL)
   {
      dynamic_cast<XmppChatJsonProxyInterface*>(xmppChatJson)->removeSdkObserver(this);
   }

   XmppAccount::XmppAccountManagerJsonProxy* xmppAcctJson = dynamic_cast<XmppAccount::XmppAccountManagerJsonProxy*>(XmppAccount::XmppAccountManagerJsonProxy::getInterface(mPhone));
   if (xmppAcctJson != NULL)
   {
      dynamic_cast<XmppAccountJsonProxyInterface*>(xmppAcctJson)->removeSdkObserver(this);
   }
#endif

   delete this;
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncFromID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message)
{
   ChatStateMap::iterator cit = mChatStateMap.find(chat);
   if (cit == mChatStateMap.end()) return "";

   ChatState::MessageStateMap::iterator mit = cit->second.messages.find(message);
   if (mit == cit->second.messages.end()) return "";

   if (mit->second.outgoing)
   {
      auto configIt = mAcctConfigMap.find(cit->second.account);
      if (configIt != mAcctConfigMap.end())
      {
         return configIt->second.username + "@" + configIt->second.domain;
      }
   }
   return cit->second.remote;
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncToID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message)
{
   ChatStateMap::iterator cit = mChatStateMap.find(chat);
   if (cit == mChatStateMap.end()) return "";

   ChatState::MessageStateMap::iterator mit = cit->second.messages.find(message);
   if (mit == cit->second.messages.end()) return "";

   if (!mit->second.outgoing)
   {
      auto configIt = mAcctConfigMap.find(cit->second.account);
      if (configIt != mAcctConfigMap.end())
      {
         return configIt->second.username + "@" + configIt->second.domain;
      }
   }
   return cit->second.remote;
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncConversationID(CPCAPI2::XmppChat::XmppChatHandle chat)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it == mChatStateMap.end()) return "";

   auto configIt = mAcctConfigMap.find(it->second.account);
   if (configIt != mAcctConfigMap.end())
   {
      std::stringstream id;
      id << "xmpp:" << configIt->second.username << "@" << configIt->second.domain;
      id << ":" << it->second.remote;
      id.flush();
      return id.str().c_str();
   }
   return "";
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncUniqueID(CPCAPI2::XmppChat::XmppChatHandle chat, const cpc::string& stanzaID)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);

   return getRemoteSyncUniqueID2(stanzaID, it == mChatStateMap.end() ? "" : it->second.thread);
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID)
{
   // For one-to-one messages, the unique ID is the concatenation of message
   // thread ID and XMPP message stanza ID. If the stanza ID is empty, do not
   // use thread ID alone. When stanza ID and thread ID are present, then the
   // format will be:
   //
   // <stanzaID>:<threadID>
   //
   // If only the stanza ID is available then the format will be
   // (notice that trailing colon):
   //
   // <stanzaID>:
   //
   // Otherwise the uniqueID field must be left as an empty string and sync
   // server will perform conflict resolution based on
   // from/to/content/timestamp.

   cpc::string result;

   if (stanzaID.empty())
      return "";

   result = stanzaID;
   result += ":";

   if (!threadID.empty())
      result += threadID;

   // return empty string if length is not long enough to
   // be considered universally unique
   if (result.size() < XMPP_UNIQUE_ID_MIN_SIGNIFICANT_LENGTH)
      return "";

   return result;
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncAccountID(XmppAccountHandle account)
{
   SettingsMap::iterator it = mAcctConfigMap.find(account);

   if (it == mAcctConfigMap.end())
   {
      LocalMaxLog("Couldn't find entry in mAcctConfigMap for getRemoteSyncAccountID {}. mAcctConfigMap size: {}", account, mAcctConfigMap.size());
      return "";
   }

   std::stringstream id;
   id << "xmpp:" << it->second.username << "@" << it->second.domain;
   return id.str().c_str();
}

XmppAccountHandle RemoteSyncXmppHelperInterface::getAccountHandleFromRemoteSyncID(const cpc::string& accountID)
{
   for (SettingsMap::iterator it = mAcctConfigMap.begin(); it != mAcctConfigMap.end(); it++)
   {
      cpc::string account = getRemoteSyncAccountID(it->first);
      if (!account.empty() && (account == accountID))
      {
         return (it->first);
      }
   }
   return 0;
}

// shamelessly stolen from http://stackoverflow.com/questions/3418231/replace-part-of-a-string-with-another-string
static void replaceAll(std::string& str, const std::string& from, const std::string& to)
{
   if (from.empty())
      return;

   size_t start_pos = 0;
   while ((start_pos = str.find(from, start_pos)) != std::string::npos)
   {
      str.replace(start_pos, from.length(), to);
      start_pos += to.length(); // In case 'to' contains 'from', like replacing 'x' with 'yx'
   }
}

cpc::string RemoteSyncXmppHelperInterface::getRemoteSyncEncodedContent(const cpc::string& plainText, const cpc::string& htmlText)
{
   std::string escapedPlainText = plainText.c_str();
   std::string escapedHtmlText = htmlText.c_str();
   cpc::string result;

   // Duplicate the HTML from the plaintext if it's not present.
   if (escapedHtmlText.empty())
      escapedHtmlText = escapedPlainText;

   // Replace these chars with their entities to prevent XML problems.
   // "   &quot;
   // '   &apos;
   // <   &lt;
   // >   &gt;
   // &   &amp;
   //
   // NB: ampersand must be done first because entity codes contain ampersands.
   //
   replaceAll(escapedPlainText, "&", "&amp;");
   replaceAll(escapedPlainText, "\"", "&quot;");
   replaceAll(escapedPlainText, "'", "&apos;");
   replaceAll(escapedPlainText, "<", "&lt;");
   replaceAll(escapedPlainText, ">", "&gt;");

   // Do the same for the HTML.
   replaceAll(escapedHtmlText, "&", "&amp;");
   replaceAll(escapedHtmlText, "\"", "&quot;");
   replaceAll(escapedHtmlText, "'", "&apos;");
   replaceAll(escapedHtmlText, "<", "&lt;");
   replaceAll(escapedHtmlText, ">", "&gt;");

   // Build the document
   // format = "<message><body>%s</body><html><body>%s</body></html></message>";
   result = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>";
   result += "<message><body>";
   result += escapedPlainText.c_str();
   result += "</body><html><body>";
   result += escapedHtmlText.c_str();
   result += "</body></html></message>";
   return result;
}

int RemoteSyncXmppHelperInterface::getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML)
{
   const xmlChar *plainTextPath = (const xmlChar*) "/message/body[1]";
   const xmlChar *htmlTextPath = (const xmlChar*) "/message/html/body[1]";
   std::string stdPlain;
   std::string stdHtml;

   // Use libxml + XPath to parse the document (hopefully the shortest way)
   LibxmlSharedUsage::addRef();

   xmlDocPtr doc = xmlParseMemory(remoteSyncEncodedContent.c_str(), remoteSyncEncodedContent.size());
   xmlXPathContextPtr xpathCtx = NULL;
   xmlXPathObjectPtr xpathObjPlain = NULL;
   xmlXPathObjectPtr xpathObjHtml = NULL;
   xmlChar *data = NULL;

   // Create xpath evaluation context
   xpathCtx = xmlXPathNewContext(doc);
   if (xpathCtx == NULL)
      goto done;

   // Evaluate xpath expression to get plaintext
   xpathObjPlain = xmlXPathEvalExpression(plainTextPath, xpathCtx);
   if (xpathObjPlain == NULL || xmlXPathNodeSetIsEmpty(xpathObjPlain->nodesetval))
      goto done;

   data = xmlNodeListGetString(doc, xpathObjPlain->nodesetval->nodeTab[0]->children, 1);
   stdPlain = (const char *)data; // copy the plaintext
   xmlFree(data);

   // unescape anything in the data which is escaped.
   // NB: ampersand should be last.
   replaceAll(stdPlain, "&quot;", "\"");
   replaceAll(stdPlain, "&apos;", "'");
   replaceAll(stdPlain, "&lt;", "<");
   replaceAll(stdPlain, "&gt;", ">");
   replaceAll(stdPlain, "&amp;", "&");

   // Evaluate xpath expression to get html content
   xpathObjHtml = xmlXPathEvalExpression(htmlTextPath, xpathCtx);
   if (xpathObjHtml == NULL || xmlXPathNodeSetIsEmpty(xpathObjHtml->nodesetval))
      goto done;

   data = xmlNodeListGetString(doc, xpathObjHtml->nodesetval->nodeTab[0]->children, 1);
   stdHtml = (const char *)data; // copy the html
   xmlFree(data);

   // unescape anything in the data which is escaped.
   // NB: ampersand should be last.
   replaceAll(stdHtml, "&quot;", "\"");
   replaceAll(stdHtml, "&apos;", "'");
   replaceAll(stdHtml, "&lt;", "<");
   replaceAll(stdHtml, "&gt;", ">");
   replaceAll(stdHtml, "&amp;", "&");

   outPlainText = stdPlain.c_str();
   outHTML = stdHtml.c_str();

done:

   if (xpathObjHtml != NULL)
   {
      xmlXPathFreeObject(xpathObjHtml);
      xpathObjHtml = NULL;
   }
   if (xpathObjPlain != NULL)
   {
      xmlXPathFreeObject(xpathObjPlain);
      xpathObjPlain = NULL;
   }
   if (xpathCtx != NULL)
   {
      xmlXPathFreeContext(xpathCtx);
      xpathCtx = NULL;
   }
   if (doc != NULL)
   {
      xmlFreeDoc(doc);
      doc = NULL;
   }

   LibxmlSharedUsage::release();
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onNewChat(XmppChatHandle chat, const NewChatEvent& args)
{
   // bliu: this is the only time where ChatState is created
   // neither create() or addParticipant() gives a meaningful remote sync information yet

   ChatState& cs = mChatStateMap[chat];

   cs.account = args.account;
   cs.remote = args.remote;

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onIsComposingMessage(XmppChatHandle chat, const IsComposingMessageEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onNewMessage(XmppChatHandle chat, const NewMessageEvent& args)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it != mChatStateMap.end())
   {
      it->second.thread = args.threadId;
      it->second.messages[args.message].outgoing = false;
   }

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onNewReaction(XmppChatHandle chat, const NewReactionEvent& args)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it != mChatStateMap.end())
   {
      it->second.thread = args.threadId;
      it->second.messages[args.message].outgoing = false;
   }

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent& args)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it != mChatStateMap.end())
   {
      it->second.thread = args.threadId;
      it->second.messages[args.message].outgoing = false;
   }

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onNewOutboundMessage(XmppChatHandle chat, const NewMessageEvent& args)
{
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it != mChatStateMap.end())
   {
      it->second.thread = args.threadId;
      it->second.messages[args.message].outgoing = true;
   }

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent& args)
{
   // Possibly redundant given the above method. Still, shouldn't hurt?
   ChatStateMap::iterator it = mChatStateMap.find(chat);
   if (it != mChatStateMap.end())
   {
      it->second.thread = args.threadId;
      it->second.messages[args.message].outgoing = true;
   }

   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onMessageDelivered(XmppChatHandle chat, const MessageDeliveredEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onMessageDeliveryError(XmppChatHandle chat, const MessageDeliveryErrorEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onMessageDisplayed(XmppChatHandle chat, const MessageDisplayedEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onChatEnded(XmppChatHandle chat, const ChatEndedEvent& event)
{
   //mChatStateMap.erase(chat); // bliu: TODO check for validity of chat handle in all other occurrances
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args) 
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onValidateChatHandleResult(XmppChatHandle chat, const ValidateChatHandleEvent& event)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onError(XmppChatHandle chat, const XmppChat::ErrorEvent& event)
{
   return kSuccess;
}

int RemoteSyncXmppHelperInterface::onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args)
{
   LocalMaxLog("RemoteSyncXmppHelperInterface::onAccountConfigured for account {}", account);
   mAcctConfigMap[account] = args.settings;
   return kSuccess;
}

}
}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
