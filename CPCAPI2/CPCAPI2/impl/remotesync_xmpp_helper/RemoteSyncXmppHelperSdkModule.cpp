#include "brand_branded.h"

#include "interface/experimental/remotesync_xmpp_helper/RemoteSyncXmppHelper.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE == 1)
#include "RemoteSyncXmppHelperInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace RemoteSyncXmppHelper
{
   RemoteSyncXmppHelper* RemoteSyncXmppHelper::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_REMOTE_SYNC_XMPP_HELPER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<RemoteSyncXmppHelperInterface>(phone, "RemoteSyncXmppHelper");
#else
      return NULL;
#endif
   }

}
}
