#pragma once

#if !defined(CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H)
#define CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H

#include "cpcapi2defs.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"
#include "xmpp/XmppChatHandler.h"
#include "xmpp/XmppAccountHandlerInternal.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{
namespace XmppAccount
{
   class XmppAccountInterface;
}

class PhoneInterface;
class LocalLogger;
namespace RemoteSyncXmppHelper
{
class RemoteSyncXmppHelperInterface : public PhoneModule,
                                      public CPCAPI2::RemoteSyncXmppHelper::RemoteSyncXmppHelper,
                                      public CPCAPI2::XmppChat::XmppChatHandler,
                                      public CPCAPI2::XmppAccount::XmppAccountHandlerInternal
{
public:
   RemoteSyncXmppHelperInterface(Phone* phone);
   virtual ~RemoteSyncXmppHelperInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // RemoteSyncXmppHelper
   virtual cpc::string getRemoteSyncFromID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncToID(CPCAPI2::XmppChat::XmppChatHandle chat, CPCAPI2::XmppChat::XmppChatMessageHandle message) OVERRIDE;
   virtual cpc::string getRemoteSyncConversationID(CPCAPI2::XmppChat::XmppChatHandle chat) OVERRIDE;
   DEPRECATED virtual cpc::string getRemoteSyncUniqueID(CPCAPI2::XmppChat::XmppChatHandle chat, const cpc::string& stanzaID) OVERRIDE;
   virtual cpc::string getRemoteSyncUniqueID2(const cpc::string& stanzaID, const cpc::string& threadID = "") OVERRIDE;
   virtual cpc::string getRemoteSyncAccountID(CPCAPI2::XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual CPCAPI2::XmppAccount::XmppAccountHandle getAccountHandleFromRemoteSyncID(const cpc::string& accountID) OVERRIDE;
   virtual cpc::string getRemoteSyncEncodedContent(const cpc::string& plainText, const cpc::string& htmlText = "") OVERRIDE;
   virtual int getRemoteSyncDecodedContent(const cpc::string& remoteSyncEncodedContent, cpc::string& outPlainText, cpc::string& outHTML) OVERRIDE;

   PhoneInterface* getThisSdkPhone() const {
      return mPhone;
   }

   // XmppChatHandler
   virtual int onNewChat(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent& args) OVERRIDE;
   virtual int onIsComposingMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::IsComposingMessageEvent& args) OVERRIDE;
   virtual int onNewMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args) OVERRIDE;
   virtual int onNewReaction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewReactionEvent& args) OVERRIDE;
   virtual int onNewMessageRetraction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageRetractionEvent& args) OVERRIDE;
   virtual int onNewOutboundMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args) OVERRIDE;
   virtual int onSendMessageSuccess(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageSuccessEvent& args) OVERRIDE;
   virtual int onSendMessageFailure(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageFailureEvent& args) OVERRIDE;
   virtual int onMessageDelivered(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveredEvent& args) OVERRIDE;
   virtual int onMessageDeliveryError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveryErrorEvent& args) OVERRIDE;
   virtual int onMessageDisplayed(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDisplayedEvent& args) OVERRIDE;
   virtual int onChatDiscoCompleted(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatDiscoEvent& args) OVERRIDE;
   virtual int onChatDiscoError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatDiscoErrorEvent& args) OVERRIDE;
   virtual int onChatEnded(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent& event) OVERRIDE;
   virtual int onValidateChatHandleResult(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ValidateChatHandleEvent& event) OVERRIDE;
   virtual int onError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ErrorEvent& event) OVERRIDE;

   // XmppAccountHandlerInternal
   virtual int onAccountConfigured(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE;
   virtual int onAccountStatusChanged(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE { return kSuccess; }
   virtual int onError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onLicensingError(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::LicensingErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onEntityTime(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityTimeEvent& args) OVERRIDE { return kSuccess; }
   virtual int onEntityFeature(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::EntityFeatureEvent& args) OVERRIDE { return kSuccess; }
   virtual int onStreamManagementState(CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppAccount::StreamManagementStateEvent& args) OVERRIDE { return kSuccess; }

private:
   PhoneInterface* mPhone;
   LocalLogger* mLocalLogger;
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, CPCAPI2::XmppAccount::XmppAccountSettings> SettingsMap;
   SettingsMap mAcctConfigMap;

   struct ChatState
   {
      CPCAPI2::XmppAccount::XmppAccountHandle account;
      cpc::string remote;
      cpc::string thread;

      struct MessageState
      {
         bool outgoing;
      };

      typedef std::map<CPCAPI2::XmppChat::XmppChatMessageHandle, MessageState> MessageStateMap;
      MessageStateMap messages;
   };

   typedef std::map<CPCAPI2::XmppChat::XmppChatHandle, ChatState> ChatStateMap;
   ChatStateMap mChatStateMap;
};


}
}

#endif // CPCAPI2_CONFERENCE_CONNECTOR_INTERFACE_H

