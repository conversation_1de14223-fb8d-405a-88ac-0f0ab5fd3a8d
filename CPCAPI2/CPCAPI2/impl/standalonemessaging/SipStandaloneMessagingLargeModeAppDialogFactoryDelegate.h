#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_LARGE_MODE_MESSAGING_APP_DIALOG_FACTORY_DELEGATE_H__)
#define __CPCAPI2_SIP_STANDALONE_LARGE_MODE_MESSAGING_APP_DIALOG_FACTORY_DELEGATE_H__

#include "../account/AppDialogFactoryDelegate.h"

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{
   class SipStandaloneMessagingLargeMode;

   class SipStandaloneMessagingLargeModeAppDialogFactoryDelegate : public CPCAPI2::SipAccount::AppDialogFactoryDelegate
   {
   public:
      SipStandaloneMessagingLargeModeAppDialogFactoryDelegate(SipStandaloneMessagingLargeMode* parent) : parent(parent) {}
      virtual ~SipStandaloneMessagingLargeModeAppDialogFactoryDelegate() {}

      virtual bool isMyMessage(const resip::SipMessage& msg);
      virtual resip::AppDialogSet* createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg);

   private:
      SipStandaloneMessagingLargeMode *parent;
   };

}
}

#endif // __CPCAPI2_SIP_STANDALONE_LARGE_MODE_MESSAGING_APP_DIALOG_FACTORY_DELEGATE_H__