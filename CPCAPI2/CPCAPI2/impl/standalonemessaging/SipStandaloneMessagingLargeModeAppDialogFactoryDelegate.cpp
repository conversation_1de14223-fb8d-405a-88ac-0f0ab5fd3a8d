#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE==1)
#include "SipStandaloneMessagingLargeModeAppDialogFactoryDelegate.h"
#include "SipStandaloneMessagingLargeMode.h"

#include "../cpm/CpmHelper.h"

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{

bool SipStandaloneMessagingLargeModeAppDialogFactoryDelegate::isMyMessage(const resip::SipMessage& msg)
{
   // Make sure the message has the expected feature tag and MIME type
   return CpmHelper::contains3gppFeatureTag(msg, SipStandaloneMessagingLargeMode::CPM_FEATURE_TAG) && CpmHelper::acceptsCpimMimeType(msg);
}

resip::AppDialogSet* SipStandaloneMessagingLargeModeAppDialogFactoryDelegate::createAppDialogSet(resip::DialogUsageManager& dum, const resip::SipMessage& msg)
{
   int sessionHandle = SipStandaloneMessagingLargeModeSessionInfo::nextSessionHandle++;
   return new SipStandaloneMessagingLargeModeSessionInfo(sessionHandle, parent->getDum(), parent);
}

}
}
#endif
