#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE==1)
#include "SipStandaloneMessagingManagerInterface.h"
#include "SipStandaloneMessagingManagerImpl.h"

#include "../cpm/CpmHelper.h"
#include "../util/ResipConv.h"
#include "../phone/PhoneInterface.h"

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{

SipStandaloneMessagingManager* SipStandaloneMessagingManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipStandaloneMessagingManagerInterface>(phone, "SipStandaloneMessagingManagerInterface");
}

SipStandaloneMessagingManagerInterface::SipStandaloneMessagingManagerInterface(Phone* phone)
   : CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipStandaloneMessagingHandler, CPCAPI2::SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(cpcPhone)),
     mAccountIf(NULL),
     cpcPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<CPCAPI2::SipAccount::SipAccountInterface*>(CPCAPI2::SipAccount::SipAccountManager::getInterface(phone));
}

SipStandaloneMessagingManagerInterface::~SipStandaloneMessagingManagerInterface()
{
   // Delete all the SM manager instances and clear the map
   for (AccountMap::iterator it = accountMap.begin(); it != accountMap.end(); it++)
   {
      SipStandaloneMessagingManagerImpl* smManager = it->second;
      delete smManager;
   }   
   accountMap.clear();
}

void SipStandaloneMessagingManagerInterface::Release()
{
   delete this;
}

int SipStandaloneMessagingManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessagingHandler* handler)
{
   postToSdkThread(resip::resip_bind(&SipStandaloneMessagingManagerInterface::setHandlerImpl, this, account, handler));
   return kSuccess;
}

void SipStandaloneMessagingManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessagingHandler* handler)
{
   // Retrieve the SM manager associated with the account specified
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   if (!smManager)
   {
      // No SM manager associate with the account

      // Get the associated account object
      CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipStandaloneMessagingManager::setHandler");
         return;
      }

      // Create a new SM manager
      smManager = new SipStandaloneMessagingManagerImpl(cpcPhone, *acct); // Destroyed in the destructor of this class

      // Keep mapping account -> SM manager
      accountMap[account] = smManager;
   }

   // Register the handler
   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
     removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }
}

SipStandaloneMessageHandle SipStandaloneMessagingManagerInterface::sendMessage(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& targetAddress, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications, bool forceLargeMode)
{
   // Validate the account
   if (!isValidAccount(account, false))
   {
      return "";
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Generate a new message ID
   SipStandaloneMessageHandle handle = smManager->getNextMessageId();

   // Send the message
   postToSdkThread(resip::resip_bind(&SipStandaloneMessagingManagerInterface::sendMessageImpl, this, account, handle, targetAddress, messageContent, mimeType, datetime, dispositionNotifications, forceLargeMode));
   return handle;
}

void SipStandaloneMessagingManagerInterface::sendMessageImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle handle, const cpc::string& targetAddress, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications, bool forceLargeMode)
{
   // Validate the account
   if (!isValidAccount(account))
   {
      return;
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Validate the target address
   resip::NameAddr targetNameAddr;
   if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
   {
      smManager->fireError("Failed to parse target URI '" + targetAddress + "'");
      return;
   }

   // Make sure the target is not the same as the originator of the message
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipStandaloneMessagingManager::sendMessage");
      return;
   }

   resip::Uri accountUri = CpmHelper::getUri(*acct);
   if (accountUri == targetNameAddr.uri())
   {
      // Target same as originator. Send an error
      smManager->fireError("Cannot send message. Originator and sender are the same");
      return;
   }

   // Set the date/time if needed
   tm datetimeVal = datetime ? *datetime : CPM::CpmHelper::getCurrentDateTime();

   // Send the message
   smManager->sendMessage(handle, targetNameAddr, resip::Data(messageContent.c_str(), messageContent.size()), CpmHelper::mimeTypeToContentType(mimeType), datetimeVal, dispositionNotifications, forceLargeMode);
}

SipStandaloneMessageHandle SipStandaloneMessagingManagerInterface::notifyMessageDelivered(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, MessageDeliveryStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string& datetimeString)
{
   // Validate the account
   if (!isValidAccount(account, false))
   {
      return "";
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Generate a new message ID
   SipStandaloneMessageHandle handle = smManager->getNextMessageId();

   // Send the delivery notification
   postToSdkThread(resip::resip_bind(&SipStandaloneMessagingManagerInterface::notifyMessageDeliveredImpl, this, account, handle, message, messageDeliveryStatus, targetAddress, datetimeString));
   return handle;
}

void SipStandaloneMessagingManagerInterface::notifyMessageDeliveredImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, SipStandaloneMessageHandle origMessage, MessageDeliveryStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string& datetimeString)
{
   // Validate the account
   if (!isValidAccount(account))
   {
      return;
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Validate the target address
   resip::NameAddr targetNameAddr;
   if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
   {
      smManager->fireError("Failed to parse target URI '" + targetAddress + "'");
      return;
   }

   // Send the delivery notification
   smManager->notifyMessageDelivered(message, origMessage, messageDeliveryStatus, targetNameAddr, datetimeString);
}

SipStandaloneMessageHandle SipStandaloneMessagingManagerInterface::notifyMessageDisplayed(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, MessageDisplayStatus messageDisplayStatus, const cpc::string& targetAddress, const cpc::string& datetimeString)
{
   // Validate the account
   if (!isValidAccount(account, false))
   {
      return "";
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Generate a new message ID
   SipStandaloneMessageHandle handle = smManager->getNextMessageId();

   // Send the display notification
   postToSdkThread(resip::resip_bind(&SipStandaloneMessagingManagerInterface::notifyMessageDisplayedImpl, this, account, handle, message, messageDisplayStatus, targetAddress, datetimeString));
   return handle;
}

void SipStandaloneMessagingManagerInterface::notifyMessageDisplayedImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, SipStandaloneMessageHandle origMessage, MessageDisplayStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string& datetimeString)
{
   // Validate the account
   if (!isValidAccount(account))
   {
      return;
   }

   // Get the SM manager associated with the account
   SipStandaloneMessagingManagerImpl* smManager = getStandaloneMessagingManager(account);
   assert(smManager);

   // Validate the target address
   resip::NameAddr targetNameAddr;
   if (!ResipConv::stringToAddr(targetAddress, targetNameAddr))
   {
      smManager->fireError("Failed to parse target URI '" + targetAddress + "'");
      return;
   }

   // Send the display notification
   smManager->notifyMessageDisplayed(message, origMessage, messageDeliveryStatus, targetNameAddr, datetimeString);
}

SipStandaloneMessagingManagerImpl* SipStandaloneMessagingManagerInterface::getStandaloneMessagingManager(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = accountMap.find(account);
   return (it != accountMap.end()) ? it->second : NULL;
}

bool SipStandaloneMessagingManagerInterface::isValidAccount(CPCAPI2::SipAccount::SipAccountHandle account, bool checkEnabled)
{
   // Validate the account
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Invalid account handle specified: " + cpc::to_string(account));
      mAccountIf->fireError(msg);
      return false;
   }

   // Make sure the account is enabled
   if (checkEnabled && !acct->isEnabled())
   {
      // Account not enabled. Send an error
      cpc::string msg = cpc::string("Account not enabled: " + cpc::to_string(account));
      mAccountIf->fireError(msg);
      return false;
   }

   // Valid account specified
   return true;
}

std::ostream& operator<<(std::ostream& os, const NewMessageEvent& evt)
{
   return os << "NewMessageEvent";
}

std::ostream& operator<<(std::ostream& os, const SendMessageSuccessEvent& evt)
{
   return os << "SendMessageSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const SendMessageFailureEvent& evt)
{
   return os << "SendMessageFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const MessageDeliveredEvent& evt)
{
   return os << "MessageDeliveredEvent";
}

std::ostream& operator<<(std::ostream& os, const MessageDisplayedEvent& evt)
{
   return os << "MessageDisplayedEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredSuccessEvent& evt)
{
   return os << "NotifyMessageDeliveredSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredFailureEvent& evt)
{
   return os << "NotifyMessageDeliveredFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedSuccessEvent& evt)
{
   return os << "NotifyMessageDisplayedSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedFailureEvent& evt)
{
   return os << "NotifyMessageDisplayedFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const SipStandaloneMessaging::ErrorEvent& evt)
{
   return os << "SipStandaloneMessaging::ErrorEvent";
}

}
}
#endif
