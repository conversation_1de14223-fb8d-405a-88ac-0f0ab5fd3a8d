#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "../account/SipAccountInterface.h"
#include "standalonemessaging/SipStandaloneMessagingManager.h"
#include "standalonemessaging/SipStandaloneMessagingHandler.h"

#include <ctime>

namespace CPCAPI2
{
class Phone;

namespace SipStandaloneMessaging
{
class SipStandaloneMessagingManagerImpl;

typedef std::map<SipAccount::SipAccountHandle, SipStandaloneMessagingManagerImpl*> AccountMap;

class SipStandaloneMessagingManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipStandaloneMessagingHand<PERSON>, CPCAPI2::SipAccount::SipAccountHandle> >,,
                                               public SipStandaloneMessagingManager,
                                               public PhoneModule
{
public:
   SipStandaloneMessagingManagerInterface(Phone* phone);
   virtual ~SipStandaloneMessagingManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipStandaloneMessagingManagerInterface);

   // PhoneModule Interface
   virtual void Release() OVERRIDE;

   // SipStandaloneMessagingManager Interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessagingHandler* handler);
   virtual SipStandaloneMessageHandle sendMessage(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& targetAddress, const cpc::string& messageContent, CPM::MimeType mimeType = MimeType_TextPlain, struct std::tm* datetime = 0, const cpc::vector<DispositionNotificationType>& dispositionNotifications = cpc::vector<DispositionNotificationType>(), bool forceLargeMode = false);
   virtual SipStandaloneMessageHandle notifyMessageDelivered(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, MessageDeliveryStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string& datetimeString);
   virtual SipStandaloneMessageHandle notifyMessageDisplayed(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, MessageDisplayStatus messageDisplayStatus, const cpc::string& targetAddress, const cpc::string& datetimeString);

private:
   SipAccount::SipAccountInterface* mAccountIf;
   PhoneInterface* cpcPhone;
   AccountMap accountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipStandaloneMessagingHandler*> mHandlers;
   SipStandaloneMessageHandle nextHandle;

   bool isValidAccount(CPCAPI2::SipAccount::SipAccountHandle account, bool checkEnabled = true);
   SipStandaloneMessagingManagerImpl*getStandaloneMessagingManager(CPCAPI2::SipAccount::SipAccountHandle account);
   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessagingHandler* handler);
   void sendMessageImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, const cpc::string& targetAddress, const cpc::string& messageContent, CPM::MimeType mimeType, struct std::tm* datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications, bool forceLargeMode);
   void notifyMessageDeliveredImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, SipStandaloneMessageHandle origMessage, MessageDeliveryStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string &datetimeString);
   void notifyMessageDisplayedImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipStandaloneMessageHandle message, SipStandaloneMessageHandle origMessage, MessageDisplayStatus messageDeliveryStatus, const cpc::string& targetAddress, const cpc::string& datetimeString);
};

std::ostream& operator<<(std::ostream& os, const NewMessageEvent& evt);
std::ostream& operator<<(std::ostream& os, const SendMessageSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const SendMessageFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const MessageDeliveredEvent& evt);
std::ostream& operator<<(std::ostream& os, const MessageDisplayedEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDeliveredFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyMessageDisplayedFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipStandaloneMessaging::ErrorEvent& evt);
}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_INTERFACE_H__
