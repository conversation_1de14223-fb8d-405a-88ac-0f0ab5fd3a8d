#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE==1)
#include "SipStandaloneMessagingLargeMode.h"
#include "SipStandaloneMessagingManagerImpl.h"
#include "SipStandaloneMessagingLargeModeAppDialogFactoryDelegate.h"

#include <resip/dum/ClientInviteSession.hxx>

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{

const cpc::string SipStandaloneMessagingLargeMode::CPM_FEATURE_TAG = "3gpp-service.ims.icsi.oma.cpm.largemsg";

int SipStandaloneMessagingLargeModeSessionInfo::nextSessionHandle = 1;

SipStandaloneMessagingLargeMode::SipStandaloneMessagingLargeMode(SipStandaloneMessagingManagerImpl *manager) 
   : SipMsrpMessagingManager(manager->getAccount()), 
     manager(manager) 
{
}

int SipStandaloneMessagingLargeMode::registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory)
{   
   std::shared_ptr<SipStandaloneMessagingLargeModeAppDialogFactoryDelegate> appDialogFactoryDelegate(new SipStandaloneMessagingLargeModeAppDialogFactoryDelegate(this)); // Shared pointer
   factory.addDelegate(appDialogFactoryDelegate);

   return kSuccess;
}

void SipStandaloneMessagingLargeMode::sendMessage(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, const resip::NameAddr& targetNameAddr, const CpimMessage& cpimMessage, MessageType messageType)
{
   // Create the session info
   int sessionHandle = SipStandaloneMessagingLargeModeSessionInfo::nextSessionHandle++;
   SipStandaloneMessagingLargeModeSessionInfo* sessionInfo = new SipStandaloneMessagingLargeModeSessionInfo(sessionHandle, getDum(), this); // Destroyed in onTerminated()
   addSessionInfo(sessionHandle, sessionInfo);

   // Keep information about the message to send in the session
   sessionInfo->messageHandle = message;
   sessionInfo->messageContent = cpimMessage;

   // Keep the remote party and set active endpoint for MSRP
   sessionInfo->remotePartyAddress = targetNameAddr;
   sessionInfo->isActive = true;

   // Start the session
   SipMsrpMessagingManager::startSession(sessionInfo, CPM_FEATURE_TAG);
}

void SipStandaloneMessagingLargeMode::onOffer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents)
{
   // Get the session information associated with the SIP session received
   SipStandaloneMessagingLargeModeSessionInfo* sessionInfo = dynamic_cast<SipStandaloneMessagingLargeModeSessionInfo*>(ish->getAppDialogSet().get());
   assert(sessionInfo);

   // Process the offer
   SipMsrpMessagingManager::onOffer(ish, msg, contents);

   // Accept the session offer
   SipMsrpMessagingManager::acceptSession(sessionInfo);
}

void SipStandaloneMessagingLargeMode::onConnected(ClientInviteSessionHandle cish, const SipMessage& msg)
{
   // Get the session information associated with the SIP session received
   SipStandaloneMessagingLargeModeSessionInfo* sessionInfo = dynamic_cast<SipStandaloneMessagingLargeModeSessionInfo*>(cish->getAppDialogSet().get());
   assert(sessionInfo);

   // Get the information on the message to send
   SipStandaloneMessageHandle message = sessionInfo->messageHandle;
   CpimMessage messageContent = sessionInfo->messageContent;

   // Now that the session is started, it is now time to send the message
   SipMsrpMessagingManager::sendMessage(sessionInfo, message, messageContent, MessageType_Message);
}

void SipStandaloneMessagingLargeMode::onMsrpMessageSendComplete(msrp_message_t* msrpMessage, int session, const SipStandaloneMessageHandle& message, MessageType messageType)
{  
   // Get the session information
   SipStandaloneMessagingLargeModeSessionInfo* sessionInfo = dynamic_cast<SipStandaloneMessagingLargeModeSessionInfo*>(getSessionInfo(session));
   assert(sessionInfo);

   // Fire event
   SendMessageSuccessEvent event;
   event.message = message;
   manager->fireSendMessageSuccess(event);

   // Terminate the session
   // Note: Run in resip thread to ensure that MSRP message destruction does not interfere with the tear down of the MSRP session
   manager->getAccount().post(resip::resip_bind(&SipStandaloneMessagingLargeMode::terminateSession, this, sessionInfo));
}

void SipStandaloneMessagingLargeMode::onMsrpMessageRecvComplete(msrp_message_t* msrpMessage, int session, uint8_t* data, uint64_t dataLength)
{ 
   // Get the session information
   SipStandaloneMessagingLargeModeSessionInfo* sessionInfo = dynamic_cast<SipStandaloneMessagingLargeModeSessionInfo*>(getSessionInfo(session));
   assert(sessionInfo);

   // Extract the content from the MSRP message (CPIM)
   resip::Data messageContentBytes = resip::Data(data, (long) dataLength);

   // Parse the CPIM message and check the type of message
   CpimMessage cpimMessage = CpimMessage::parse(messageContentBytes);
   SipStandaloneMessageHandle message = manager->processStandaloneMessage(cpimMessage);
}

}
}
#endif
