#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_MODE_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_MODE_H__

#include "standalonemessaging/SipStandaloneMessagingTypes.h"
#include "../cpm/CpmHelper.h"

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{
class SipStandaloneMessagingMode
{
public:
   virtual void sendMessage(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, const resip::NameAddr& targetNameAddr, const CpimMessage& cpimMessage, MessageType messageType) = 0;

protected:
   SipStandaloneMessagingMode() {}
   virtual ~SipStandaloneMessagingMode() {}
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_MODE_H__