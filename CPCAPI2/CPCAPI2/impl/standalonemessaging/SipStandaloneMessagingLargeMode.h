#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_LARGE_MODE_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_LARGE_MODE_H__

#include "SipStandaloneMessagingMode.h"
#include "standalonemessaging/SipStandaloneMessagingTypes.h"
#include "../cpm/CpimMessage.h"
#include "../cpm/CpmHelper.h"
#include "../cpm/SipMsrpMessagingManager.h"

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{
// Forward declarations
class SipStandaloneMessagingManagerImpl;

struct SipStandaloneMessagingLargeModeSessionInfo : public SipMsrpMessagingSessionInfo
{
   SipStandaloneMessagingLargeModeSessionInfo(int handle, resip::SharedPtr<resip::DialogUsageManager> dum, resip::InviteSessionHandler* handler) : SipMsrpMessagingSessionInfo(handle, dum, handler) {}
   ~SipStandaloneMessagingLargeModeSessionInfo() {}

   SipStandaloneMessageHandle messageHandle;
   CpimMessage messageContent;

   static int nextSessionHandle;
};

class SipStandaloneMessagingLargeMode : public SipStandaloneMessagingMode, public SipMsrpMessagingManager
{
public:
   SipStandaloneMessagingLargeMode(SipStandaloneMessagingManagerImpl *manager);
   ~SipStandaloneMessagingLargeMode() {}

   // SipStandaloneMessagingMode interface
   void sendMessage(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, const resip::NameAddr& targetNameAddr, const CpimMessage& cpimMessage, MessageType messageType);

   static const cpc::string CPM_FEATURE_TAG;

private:
   SipStandaloneMessagingManagerImpl* manager;

   // SipStandaloneMessagingMode
   int registerSdkDialogSetFactory(CPCAPI2::SipAccount::AppDialogSetFactory& factory);
   virtual void onOffer(InviteSessionHandle ish, const SipMessage& msg, const SdpContents& contents);
   virtual void onConnected(ClientInviteSessionHandle, const SipMessage& msg) OVERRIDE;

   // SipMsrpMessagingManager   
   void onMsrpMessageSendComplete(msrp_message_t* msrpMessage, int session, const cpc::string& message, MessageType messageType);
   void onMsrpMessageRecvComplete(msrp_message_t* msrpMessage, int session, uint8_t* data, uint64_t dataLength);
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_LARGE_MODE_H__