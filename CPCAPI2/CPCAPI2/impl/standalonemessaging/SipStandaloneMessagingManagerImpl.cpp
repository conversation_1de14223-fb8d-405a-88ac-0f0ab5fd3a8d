#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE==1)
#include "SipStandaloneMessagingManagerImpl.h"

#include "../cpm/CpmHelper.h"
#include "../cpm/CpimMessage.h"

#include <utils/msrp_string.h>
#include <utils/msrp_mem.h>

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{

SipStandaloneMessagingManagerImpl::SipStandaloneMessagingManagerImpl(SipStandaloneMessagingManagerInterface* iff, PhoneInterface* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl& account)
   : mInterface(iff),
     mPhone(cpcPhone),
     account(account),
     pagerMode(this),
     largeMode(this)
{
   account.registerAccountAwareFeature(&pagerMode);
   account.registerAccountAwareFeature(&largeMode);

   // Register the event handler
   mPhone->getSdkModuleThread().registerEventHandler(&largeMode);
}

SipStandaloneMessagingManagerImpl::~SipStandaloneMessagingManagerImpl()
{
   // De-register the event handler
   mPhone->getSdkModuleThread().unregisterEventHandler(&largeMode);

   account.unregisterAccountAwareFeature(&pagerMode);
   account.unregisterAccountAwareFeature(&largeMode);
}

void SipStandaloneMessagingManagerImpl::sendMessage(const SipStandaloneMessageHandle& message, const resip::NameAddr& targetNameAddr, const resip::Data& messageContent, const resip::Mime& contentType, const struct std::tm& datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications, bool forceLargeMode)
{
   // Make sure there is a message to send
   if (messageContent.empty())
   {
      SendMessageFailureEvent event;
      event.message = message;
      fireSendMessageFailure(event);
      return;
   }

   // Create the content of the CPIM message
   resip::Uri fromUri = CpmHelper::getUri(account);  
   resip::Uri toUri = targetNameAddr.uri();
   CpimMessage cpimMessage = CpmHelper::createCpimMessage(message, fromUri, toUri, datetime, contentType, messageContent, dispositionNotifications);   

   // Determine the mode to use for this message, pager or large
   SipStandaloneMessagingMode* mode;
   if (messageContent.size() > 1300 || forceLargeMode)
      mode = &largeMode;
   else 
      mode = &pagerMode;

   // Send the message
   mode->sendMessage(message, "", targetNameAddr, cpimMessage, MessageType_Message);
}

void SipStandaloneMessagingManagerImpl::notifyMessageDelivered(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus, const resip::NameAddr& targetNameAddr, const cpc::string& datetimeString)
{
   notifyMessageStatus(message, origMessage, MessageType_MessageDeliveredNotification, messageDeliveryStatus, targetNameAddr, datetimeString);
}

void SipStandaloneMessagingManagerImpl::notifyMessageDisplayed(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageDisplayStatus messageDisplayStatus, const resip::NameAddr& targetNameAddr, const cpc::string& datetimeString)
{
   notifyMessageStatus(message, origMessage, MessageType_MessageDisplayedNotification, messageDisplayStatus, targetNameAddr, datetimeString);
}

void SipStandaloneMessagingManagerImpl::notifyMessageStatus(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageType messageType, int messageStatus, const resip::NameAddr& targetNameAddr, const cpc::string& datetimeString)
{
   // Create the content of the CPIM message
   resip::Uri fromUri = CpmHelper::getUri(account);  
   resip::Uri toUri = targetNameAddr.uri();
   CpimMessage cpimMessage = CpmHelper::createCpimMessage(message, toUri, fromUri, origMessage, datetimeString, messageType, messageStatus);

   // Send the notification
   pagerMode.sendMessage(message, origMessage, targetNameAddr, cpimMessage, messageType);
}

void SipStandaloneMessagingManagerImpl::fireNewMessage(const NewMessageEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onNewMessage), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireSendMessageSuccess(const SendMessageSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onSendMessageSuccess), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireSendMessageFailure(const SendMessageFailureEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onSendMessageFailure), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireMessageDelivered(const MessageDeliveredEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onMessageDelivered), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireMessageDisplayed(const MessageDisplayedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onMessageDisplayed), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireNotifyMessageDeliveredSuccess(const NotifyMessageDeliveredSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onNotifyMessageDeliveredSuccess), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireNotifyMessageDeliveredFailure(const NotifyMessageDeliveredFailureEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onNotifyMessageDeliveredFailure), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireNotifyMessageDisplayedSuccess(const NotifyMessageDisplayedSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onNotifyMessageDisplayedSuccess), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireNotifyMessageDisplayedFailure(const NotifyMessageDisplayedFailureEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onNotifyMessageDisplayedFailure), account.getHandle(), event);
}

void SipStandaloneMessagingManagerImpl::fireError(const cpc::string& msg)
{
   ErrorEvent event;
   event.errorText = msg;
   fireError(event);
}

void SipStandaloneMessagingManagerImpl::fireError(const ErrorEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipStandaloneMessagingHandler, onError), account.getHandle(), event);
}

SipStandaloneMessageHandle SipStandaloneMessagingManagerImpl::getNextMessageId()
{
   // Generate a string based ID with 64 bit of randomness
   char* randomIdStr = msrp_string_new_random(64);
   cpc::string messageId = (randomIdStr);
   msrp_safe_free((void**) &randomIdStr);

   return messageId;
}

void SipStandaloneMessagingManagerImpl::processImdnNotification(const CpimMessage& cpimMessage)
{
   // Extract the content of the IMDN notification
   SipStandaloneMessageHandle message;
   SipStandaloneMessageHandle origMessage;
   cpc::string datetimeString;
   int notificationStatus;
   MessageType notificationType;
   CpmHelper::extractCpimMessage(cpimMessage, message, origMessage, datetimeString, notificationType, notificationStatus);

   // Check the type of notification
   if (notificationType == MessageType_MessageDeliveredNotification)
   {
      // IMDN Delivery Notification

      // Fire the event
      MessageDeliveredEvent event;
      event.notification = message;
      event.message = origMessage;
      event.messageDeliveryStatus = (MessageDeliveryStatus) notificationStatus;
      event.datetime = CpmHelper::extractDateTime(datetimeString);
      fireMessageDelivered(event);
   }
   else if (notificationType == MessageType_MessageDisplayedNotification)
   {
      // IMDN Display Notification
      MessageDisplayedEvent event;
      event.notification = message;
      event.message = origMessage;
      event.messageDisplayStatus = (MessageDisplayStatus) notificationStatus;
      event.datetime = CpmHelper::extractDateTime(datetimeString);
      fireMessageDisplayed(event);
   }
   else
   {
      // Unsupported notification
      assert(false);
   }
}

SipStandaloneMessageHandle SipStandaloneMessagingManagerImpl::processStandaloneMessage(const CpimMessage& cpimMessage)
{
   // Extract the content of the message
   SipStandaloneMessageHandle message;
   cpc::string fromUriStr;
   cpc::string toUriStr;
   tm datetime;
   cpc::string datetimeString;
   resip::Mime contentType;
   resip::Data messageContent;
   cpc::vector<DispositionNotificationType> dispositionNotifications;
   CpmHelper::extractCpimMessage(cpimMessage, message, fromUriStr, toUriStr, datetimeString, datetime, contentType, messageContent, dispositionNotifications);

   // Notify of the incoming message
   NewMessageEvent event;
   event.message = message;
   event.messageContent = cpc::string(messageContent.c_str(), messageContent.size());
   event.from = fromUriStr;
   event.to = toUriStr;
   event.mimeType = CpmHelper::contentTypeToMimeType(contentType);
   event.datetime = datetime;
   event.datetimeString = datetimeString;
   event.dispositionNotifications = dispositionNotifications;
   fireNewMessage(event);

   return message;
}

}
}
#endif
