#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_IMPL_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "SipStandaloneMessagingManagerInterface.h"
#include "SipStandaloneMessagingPagerMode.h"
#include "SipStandaloneMessagingLargeMode.h"
#include "standalonemessaging/SipStandaloneMessagingManager.h"
#include "standalonemessaging/SipStandaloneMessagingHandler.h"
#include "../cpm/CpimMessage.h"
#include "../cpm/CpmHelper.h"
#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
class Phone;

namespace SipStandaloneMessaging
{

class SipStandaloneMessagingManagerImpl
{
public:
   SipStandaloneMessagingManagerImpl(SipStandaloneMessagingManagerInterface* iff, PhoneInterface* cpcPhone, CPCAPI2::SipAccount::SipAccountImpl& account);
   virtual ~SipStandaloneMessagingManagerImpl();

   // Invoked by the interface
   SipStandaloneMessageHandle getNextMessageId();
   void sendMessage(const SipStandaloneMessageHandle& message, const resip::NameAddr& targetNameAddr, const resip::Data& messageContent, const resip::Mime& contentType, const struct std::tm& datetime, const cpc::vector<DispositionNotificationType>& dispositionNotifications, bool forceLargeMode);
   void notifyMessageDelivered(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageDeliveryStatus messageDeliveryStatus, const resip::NameAddr& targetNameAddr, const cpc::string& datetimeString);
   void notifyMessageDisplayed(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageDisplayStatus messageDisplayStatus, const resip::NameAddr& targetNameAddr, const cpc::string& datetimeString);
   void fireError(const cpc::string& msg);

   // Invoked by the modes
   SipAccount::SipAccountImpl& getAccount() { return account;  }
   void processImdnNotification(const CpimMessage& cpimMessage);
   SipStandaloneMessageHandle processStandaloneMessage(const CpimMessage& cpimMessage);
   void fireNotifyMessageDeliveredSuccess(const NotifyMessageDeliveredSuccessEvent& event);
   void fireNotifyMessageDeliveredFailure(const NotifyMessageDeliveredFailureEvent& event);
   void fireNotifyMessageDisplayedSuccess(const NotifyMessageDisplayedSuccessEvent& event);
   void fireNotifyMessageDisplayedFailure(const NotifyMessageDisplayedFailureEvent& event);
   void fireNewMessage(const NewMessageEvent& event);
   void fireSendMessageSuccess(const SendMessageSuccessEvent& event);
   void fireSendMessageFailure(const SendMessageFailureEvent& event);
   void fireMessageDelivered(const MessageDeliveredEvent& event);
   void fireMessageDisplayed(const MessageDisplayedEvent& event);

private:
   SipStandaloneMessagingManagerInterface* mInterface;
   PhoneInterface* cpcPhone;
   SipStandaloneMessagingHandler* appHandler;
   CPCAPI2::SipAccount::SipAccountImpl& account;
   SipStandaloneMessagingPagerMode pagerMode;
   SipStandaloneMessagingLargeMode largeMode;

   void notifyMessageStatus(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, MessageType messageType, int messageStatus, const resip::NameAddr& participantAddress, const cpc::string& datetimeString);
   void fireError(const ErrorEvent& event);
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_MANAGER_IMPL_H__
