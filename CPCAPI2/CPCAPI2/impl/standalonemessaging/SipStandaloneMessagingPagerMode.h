#pragma once

#if !defined(__CPCAPI2_SIP_STANDALONE_MESSAGING_PAGER_MODE_H__)
#define __CPCAPI2_SIP_STANDALONE_MESSAGING_PAGER_MODE_H__

#include "SipStandaloneMessagingMode.h"
#include "../cpm/CpmHelper.h"
#include "standalonemessaging/SipStandaloneMessagingTypes.h"
#include "../account/SipAccountAwareFeature.h"
#include "../account/CPPagerMessageHandlerDelegate.h"

using resip::SipMessage;
using resip::Contents;

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{
// Forward declarations
class SipStandaloneMessagingManagerImpl;

struct SipStandaloneMessagingPagerModeMessageInfo
{
   SipStandaloneMessageHandle message;
   MessageType messageType;
   SipStandaloneMessageHandle origMessage; // For IMDN notifications only: handle of the message being reported upon
};

class SipStandaloneMessagingPagerMode : public SipStandaloneMessagingMode,
                                        public CPCAPI2::SipAccount::SipAccountAwareFeature,
                                        public CPCAPI2::SipAccount::CPPagerMessageHandlerDelegate
{
public:
   SipStandaloneMessagingPagerMode(SipStandaloneMessagingManagerImpl *manager);

   // SipStandaloneMessagingMode interface
   void sendMessage(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, const resip::NameAddr& targetNameAddr, const CpimMessage& cpimMessage, MessageType messageType);

private:
   SipStandaloneMessagingManagerImpl* manager;
   resip::SharedPtr<resip::DialogUsageManager> dum;
   std::map<resip::ClientPagerMessageHandle, SipStandaloneMessagingPagerModeMessageInfo> clientHandleMap;

   static const cpc::string CPM_FEATURE_TAG;

   // SipAccountAwareFeature interface
   virtual int registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler) OVERRIDE;
   virtual int adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile) OVERRIDE;
   virtual int addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourseIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig) OVERRIDE;
   virtual int onDumBeingDestroyed() OVERRIDE;
   virtual void release() OVERRIDE {}

   // CPPagerMessageHandlerDelegate interface
   virtual void onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& message) OVERRIDE;
   virtual void onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status) OVERRIDE;
   virtual void onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents) OVERRIDE;
   virtual bool isMyMessage(const resip::SipMessage& msg);
   virtual bool isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg);

   resip::ClientPagerMessageHandle sendMessage(const SipStandaloneMessageHandle& message, const resip::NameAddr& targetNameAddr, const resip::Data& messageContentBytes, MessageType messageType);
   void acceptMessage(const SipStandaloneMessageHandle& message);
};

}
}

#endif // __CPCAPI2_SIP_STANDALONE_MESSAGING_PAGER_MODE_H__
