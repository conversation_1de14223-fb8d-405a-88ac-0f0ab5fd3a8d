#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_STANDALONE_MESSAGING_MODULE==1)
#include "SipStandaloneMessagingPagerMode.h"
#include "SipStandaloneMessagingManagerImpl.h"
#include "../cpm/CpmHelper.h"
#include "../cpm/CpimMessage.h"

#include <resip/dum/ClientPagerMessage.hxx>
#include <resip/dum/ServerPagerMessage.hxx>
#include <resip/stack/Mime.hxx>
#include <resip/stack/GenericContents.hxx>

using namespace CPCAPI2::CPM;

namespace CPCAPI2
{
namespace SipStandaloneMessaging
{

const cpc::string SipStandaloneMessagingPagerMode::CPM_FEATURE_TAG = "3gpp-service.ims.icsi.oma.cpm.msg";

SipStandaloneMessagingPagerMode::SipStandaloneMessagingPagerMode(SipStandaloneMessagingManagerImpl *manager)
   : manager(manager)
{
}

int SipStandaloneMessagingPagerMode::adornMasterProfile(resip::SharedPtr<resip::MasterProfile>& profile)
{
   profile->addSupportedMethod(resip::MESSAGE);
   profile->addSupportedMimeType(resip::MESSAGE, CPM::CpmHelper::CPIM_CONTENT_TYPE);

   return kSuccess;
}

int SipStandaloneMessagingPagerMode::addHandlers(resip::SharedPtr<resip::DialogUsageManager>& dum, const std::string& overrideSourceIpSignalling, const std::string& overrideSourceIpTransport, void* tscconfig)
{
   // Keep a reference to the DUM
   this->dum = dum;

   return kSuccess;
}

int SipStandaloneMessagingPagerMode::onDumBeingDestroyed()
{
   this->dum.reset();
   return kSuccess;
}

int SipStandaloneMessagingPagerMode::registerSdkPagerMessageHandler(CPCAPI2::SipAccount::CPPagerMessageHandler& pagerMessageHandler)
{
   pagerMessageHandler.addDelegate(this);

   return kSuccess;
}

void SipStandaloneMessagingPagerMode::onMessageArrived(resip::ServerPagerMessageHandle h, const resip::SipMessage& sipMessage)
{
   // Extract the content from the SIP message (CPIM)
   resip::Data messageContentBytes = sipMessage.getContents()->getBodyData();

   // Parse the CPIM message and check the type of message
   CpimMessage cpimMessage = CpimMessage::parse(messageContentBytes);
   if (CpmHelper::isImdnNotification(cpimMessage))
   {
      // IMDN notification
      manager->processImdnNotification(cpimMessage);
   }
   else
   {
      // Standalone message
      SipStandaloneMessageHandle message = manager->processStandaloneMessage(cpimMessage);
   }

   // Send a 200/OK back
   resip::SharedPtr<SipMessage> msg = h->accept();
   h->send(msg);
}

void SipStandaloneMessagingPagerMode::onSuccess(resip::ClientPagerMessageHandle h, const resip::SipMessage& status)
{
   // Get the handle or the SIP message in question
   std::map<resip::ClientPagerMessageHandle, SipStandaloneMessagingPagerModeMessageInfo>::iterator it = clientHandleMap.find(h);
   if (it == clientHandleMap.end())
   {
      assert(false);
   }
   SipStandaloneMessagingPagerModeMessageInfo messageInfo = it->second;
   SipStandaloneMessageHandle message = messageInfo.message;
   MessageType messageType = messageInfo.messageType;
   SipStandaloneMessageHandle origMessage = messageInfo.origMessage;

   // Check the type of message received
   if (messageType == MessageType_MessageDeliveredNotification)
   {
      // Message Delivered notification
      NotifyMessageDeliveredSuccessEvent event;
      event.notification = message;
      event.message = origMessage;
      manager->fireNotifyMessageDeliveredSuccess(event);
   }
   else if (messageType == MessageType_MessageDisplayedNotification)
   {
      // Message Displayed notification
      NotifyMessageDisplayedSuccessEvent event;
      event.notification = message;
      event.message = origMessage;
      manager->fireNotifyMessageDisplayedSuccess(event);
   }
   else if (messageType == MessageType_Message)
   {
      // Standalone message

      // Fire event
      SendMessageSuccessEvent event;
      event.message = message;
      manager->fireSendMessageSuccess(event);
   }
   else
   {
      // Unsupported message type
      assert(false);
   }

   // Remove the handle
   clientHandleMap.erase(it);
}

void SipStandaloneMessagingPagerMode::onFailure(resip::ClientPagerMessageHandle h, const resip::SipMessage& status, std::unique_ptr<resip::Contents> contents)
{
   // Get the handle or the SIP message in question
   std::map<resip::ClientPagerMessageHandle, SipStandaloneMessagingPagerModeMessageInfo>::iterator it = clientHandleMap.find(h);
   if (it == clientHandleMap.end())
   {
      assert(false);
   }
   SipStandaloneMessagingPagerModeMessageInfo messageInfo = it->second;
   SipStandaloneMessageHandle message = messageInfo.message;
   MessageType messageType = messageInfo.messageType;
   SipStandaloneMessageHandle origMessage = messageInfo.origMessage;

   // Check the type of message received
   if (messageType == MessageType_MessageDeliveredNotification)
   {
      // Message Delivered notification
      NotifyMessageDeliveredFailureEvent event;
      event.notification = message;
      event.message = origMessage;
      manager->fireNotifyMessageDeliveredFailure(event);
   }
   else if (messageType == MessageType_MessageDisplayedNotification)
   {
      // Message Displayed notification
      NotifyMessageDisplayedFailureEvent event;
      event.notification = message;
      event.message = origMessage;
      manager->fireNotifyMessageDisplayedFailure(event);
   }
   else if (messageType == MessageType_Message)
   {
      // Standalone message

      // Notify of the failure sending the message
      SendMessageFailureEvent event;
      event.message = message;
      resip::StatusLine statusLine = status.header(resip::h_StatusLine);
      event.errorCode = statusLine.responseCode();
      manager->fireSendMessageFailure(event);
   }
   else
   {
      // Unsupported message type
      assert(false);
   }

   // Remove the handle
   clientHandleMap.erase(it);
}

bool SipStandaloneMessagingPagerMode::isMyMessage(const resip::SipMessage& msg)
{
   // Make sure the message has the expected feature tag and MIME type
   return CpmHelper::contains3gppFeatureTag(msg, CPM_FEATURE_TAG) &&
          msg.header(resip::h_ContentType) == CPM::CpmHelper::CPIM_CONTENT_TYPE;
}

bool SipStandaloneMessagingPagerMode::isMyResponse(resip::ClientPagerMessageHandle h, const resip::SipMessage& msg)
{
   // Check that this service has seen the handle specified
   return clientHandleMap.find(h) != clientHandleMap.end();
}

void SipStandaloneMessagingPagerMode::sendMessage(const SipStandaloneMessageHandle& message, const SipStandaloneMessageHandle& origMessage, const resip::NameAddr& targetNameAddr, const CpimMessage& cpimMessage, MessageType messageType)
{
   // Convert the CPIM message to a byte array
   resip::Data messageContentBytes = cpimMessage.toBytes();

   // Send message
   resip::ClientPagerMessageHandle cpmh = sendMessage(message, targetNameAddr, messageContentBytes, messageType);

   // Keep the relationship message handle->message info in the map
   SipStandaloneMessagingPagerModeMessageInfo messageInfo;
   messageInfo.message = message;
   messageInfo.messageType = messageType;
   messageInfo.origMessage = origMessage;
   clientHandleMap[cpmh] = messageInfo;
}

resip::ClientPagerMessageHandle SipStandaloneMessagingPagerMode::sendMessage(const SipStandaloneMessageHandle& message, const resip::NameAddr& targetNameAddr, const resip::Data& messageContentBytes, MessageType messageType)
{
   // Send message
   std::unique_ptr<resip::Contents> customContents(new resip::GenericContents(messageContentBytes, CPM::CpmHelper::CPIM_CONTENT_TYPE));
   resip::ClientPagerMessageHandle cpmh = dum->makePagerMessage(targetNameAddr);
   CpmHelper::set3gppFeatureTag(cpmh->getMessageRequest(), CPM_FEATURE_TAG);
   cpmh->page(std::move(customContents));

   return cpmh;
}

}
}
#endif
