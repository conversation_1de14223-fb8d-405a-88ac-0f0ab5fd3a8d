#pragma once

#include "cpcapi2defs.h"
#include "webcall/WebCallHandler.h"
#include "webcall/WebCallManager.h"

#include <resip/stack/NameAddr.hxx>

#include <map>

namespace recon
{
class AVOfferAnswerSession;
}

namespace webrtc_recon
{
class RtpStreamImpl;
}

namespace CPCAPI2
{
namespace WebCall
{
   struct WebCallCreationInfo
   {
      WebNavigationHandle                        context;
      std::vector<cpc::string>                  targetAddresses;
      std::vector<MediaInfo>                     localMediaInfo;
      std::vector<MediaInfo>                     remoteMediaInfo;
      unsigned int                               anonymousMode;
      bool                                       hasPendingRequest;
      std::vector<MediaInfo>                     remoteMediaChangeRequest;
      std::vector<std::weak_ptr<webrtc_recon::RtpStreamImpl> > rtpStreams;

      ConversationState conversationState;
      ConversationType conversationType;

      recon::AVOfferAnswerSession* avoaSession;

      WebCallCreationInfo()
      {
         conversationState = ConversationState_None;
         conversationType = ConversationType_Outgoing;
         MediaInfo defaultMedia;
         localMediaInfo.push_back(defaultMedia);
         anonymousMode = 0;
         hasPendingRequest = false;
         avoaSession = NULL;
      }
   };

   typedef std::map<WebCallHandle, WebCallCreationInfo*> WebCallCreationInfoMap;

}
}
