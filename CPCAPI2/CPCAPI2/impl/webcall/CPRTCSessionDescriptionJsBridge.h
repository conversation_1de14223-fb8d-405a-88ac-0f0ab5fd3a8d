#pragma once

#include <resip/recon/AVOfferAnswerSession.hxx>

#include <v8.h>

namespace CPCAPI2
{
class PhoneInterface;

namespace WebCall
{
class CPRTCSessionDescription
{
public:
   CPRTCSessionDescription(v8::Isolate* isolate, const v8::Persistent<v8::Context>& context, CPCAPI2::PhoneInterface* pi);
   virtual ~CPRTCSessionDescription();

   static void CPRTCSessionDescriptionCtor(const v8::FunctionCallbackInfo<v8::Value>& args);
   static CPRTCSessionDescription* Unwrap(v8::Handle<v8::Object> obj);
   v8::Handle<v8::Object> Wrap();

   std::string sdp;
   recon::AVOfferAnswerSession::SdpDisposition disposition;

private:
   v8::Isolate* getIsolate() { return mIsolate; }

   static v8::Handle<v8::ObjectTemplate> MakeTemplate(v8::Isolate* isolate);
   static void getSdp(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void getType(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);

private:
   v8::Isolate* mIsolate;
   v8::Persistent<v8::Context> mContext;
   CPCAPI2::PhoneInterface* mPhone;
   v8::Persistent<v8::Object> jsObj; // the instance of CPRTCSessionDescription in Javascript-land
   v8::Persistent<v8::ObjectTemplate> mRtcSessionDescTemplate;
};

}
}

