#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)

#include "WebSocketJsBridge.h"
#include "WebCallManagerImpl.h"
#include "../phone/PhoneInterface.h"

#include <iostream>
#include <map>
#include <queue>
#include <string>

#include "../util/PlatformLog.h"

using websocketpp::lib::placeholders::_1;
using websocketpp::lib::placeholders::_2;
using websocketpp::lib::bind;

// pull out the type of messages sent by our config

using namespace v8;

namespace CPCAPI2
{
namespace WebCall
{
WebSocketJsBridge::WebSocketJsBridge(v8::Isolate* isolate, const v8::Persistent<v8::Context>& context, CPCAPI2::PhoneInterface* pi) : mIsolate(isolate), mPhone(pi)
{
   mContext.Reset(isolate, context);
   initWebsocketClient();
}

WebSocketJsBridge::~WebSocketJsBridge()
{
   shutdownWebsocketClient();
   mContext.Reset();
   jsObj.Reset();
   jsOnCloseFun.Reset();
   jsOnErrorFun.Reset();
   jsOnMessageFun.Reset();
   jsOnOpenFun.Reset();
}

void WebSocketJsBridge::Dispose()
{
   delete this;
}

void WebSocketJsBridge::WebSocketCtor(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   //Locker lock;
   v8::HandleScope scope(args.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Handle<ObjectTemplate> t = v8::ObjectTemplate::New(args.GetIsolate());

   Local<External> dataExt = Local<External>::Cast(args.Data());
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(dataExt->Value());
   PhoneInterface* pi = wcm->getPhoneInterface();

   //The JavaScript point object only has 1 C++ object
   t->SetInternalFieldCount(1);

   t->Set(String::NewFromUtf8(args.GetIsolate(), "send"), FunctionTemplate::New(args.GetIsolate(), sendWrapper));
   t->SetAccessor(
      String::NewFromUtf8(args.GetIsolate(), "onerror", String::kInternalizedString),
      getOnError, setOnError);
   t->SetAccessor(
      String::NewFromUtf8(args.GetIsolate(), "onopen", String::kInternalizedString),
      getOnOpen, setOnOpen);
   t->SetAccessor(
      String::NewFromUtf8(args.GetIsolate(), "onmessage", String::kInternalizedString),
      getOnMessage, setOnMessage);
   t->SetAccessor(
      String::NewFromUtf8(args.GetIsolate(), "onclose", String::kInternalizedString),
      getOnClose, setOnClose);

   Local<Object> obj;

   if(!args[0].IsEmpty() && args[0]->IsString()) 
   {
      WebSocketJsBridge* websocket = new WebSocketJsBridge(args.GetIsolate(), wcm->getContext(), pi);
      wcm->addDisposable(websocket);
      Handle<Value> uriJsStr = args[0];
      String::Utf8Value uriJsStrVal(uriJsStr);
      if (websocket->connectWebsocketClient(*uriJsStrVal) == 0)
      {
         obj = t->NewInstance();
         obj->SetInternalField(0, External::New(args.GetIsolate(), websocket));                        
         // !jjg! this line might be completely wrong
         websocket->jsObj.Reset(args.GetIsolate(), obj);

         // Return this newly created object
         args.GetReturnValue().Set(obj);
      }
   }
}

void WebSocketJsBridge::sendWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   WebSocketJsBridge* websocket = static_cast<WebSocketJsBridge*>(external->Value());

   if(!args[0].IsEmpty() && args[0]->IsString())
   {
      Handle<Value> dataJsStr = args[0];
      String::Utf8Value dataJsStrVal(dataJsStr);
      websocket->sendWebsocketClient(*dataJsStrVal);
   }
}

WebSocketJsBridge* WebSocketJsBridge::UnwrapWebSocket(Handle<Object> obj) 
{
  Handle<External> field = Handle<External>::Cast(obj->GetInternalField(0));
  void* ptr = field->Value();
  return static_cast<WebSocketJsBridge*>(ptr);
}

void WebSocketJsBridge::getOnError(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info)
{
  // Extract the C++ request object from the JavaScript wrapper.
  WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(websocket->jsOnErrorFun);
}

void WebSocketJsBridge::setOnError(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info)
{
   // Extract the C++ request object from the JavaScript wrapper.
   WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());
   v8::Handle<v8::Function> fun = v8::Handle<v8::Function>::Cast(value);
   websocket->jsOnErrorFun.Reset(info.GetIsolate(), fun);
}

void WebSocketJsBridge::getOnOpen(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info)
{
  // Extract the C++ request object from the JavaScript wrapper.
  WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(websocket->jsOnOpenFun);
}

void WebSocketJsBridge::setOnOpen(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info)
{
   // Extract the C++ request object from the JavaScript wrapper.
   WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());
   v8::Handle<v8::Function> fun = v8::Handle<v8::Function>::Cast(value);
   websocket->jsOnOpenFun.Reset(info.GetIsolate(), fun);
}

void WebSocketJsBridge::getOnMessage(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info)
{
  // Extract the C++ request object from the JavaScript wrapper.
  WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(websocket->jsOnMessageFun);
}

void WebSocketJsBridge::setOnMessage(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info)
{
   // Extract the C++ request object from the JavaScript wrapper.
   WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());
   v8::Handle<v8::Function> fun = v8::Handle<v8::Function>::Cast(value);
   websocket->jsOnMessageFun.Reset(info.GetIsolate(), fun);
}

void WebSocketJsBridge::getOnClose(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info)
{
  // Extract the C++ request object from the JavaScript wrapper.
  WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(websocket->jsOnCloseFun);
}

void WebSocketJsBridge::setOnClose(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info)
{
   // Extract the C++ request object from the JavaScript wrapper.
   WebSocketJsBridge* websocket = UnwrapWebSocket(info.Holder());
   v8::Handle<v8::Function> fun = v8::Handle<v8::Function>::Cast(value);
   websocket->jsOnCloseFun.Reset(info.GetIsolate(), fun);
}

void WebSocketJsBridge::initWebsocketClient()
{
   // clear all error/access channels
   m_endpoint.clear_access_channels(websocketpp::log::alevel::all);
   m_endpoint.clear_error_channels(websocketpp::log::elevel::all);
        
   // Initialize the endpoint
   m_endpoint.init_asio();

   // Mark this endpoint as perpetual. Perpetual endpoints will not exit
   // even if there are no connections.
   m_endpoint.start_perpetual();

   // Start a background thread and run the endpoint in that thread
   m_thread.reset(new thread_type(&client::run, &m_endpoint));
}

int WebSocketJsBridge::connectWebsocketClient(const std::string& uri)
{
   m_endpoint.get_io_service().post([this, uri] () {
      stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::connectWebsocketClient %s", uri.c_str());

      websocketpp::lib::error_code ec;

      // connect to this address
      m_con = m_endpoint.get_connection(uri,ec);
      if (ec) {
         m_con.reset();
         return ec.value();
      }
       
      m_con->set_open_handler(bind(&WebSocketJsBridge::on_open,this,std::placeholders::_1));
      m_con->set_fail_handler(bind(&WebSocketJsBridge::on_fail,this,std::placeholders::_1));
      m_con->set_message_handler(bind(&WebSocketJsBridge::on_message,this,std::placeholders::_1,std::placeholders::_2));
      m_con->set_close_handler(bind(&WebSocketJsBridge::on_close,this,std::placeholders::_1));

      stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::connectWebsocketClient connecting...");
      m_endpoint.connect(m_con);
      return 0;
   });

   return 0;
}

int WebSocketJsBridge::sendWebsocketClient(const std::string& data)
{
   m_endpoint.get_io_service().post([this, data] () {
      stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::sendWebsocketClient data=%s", data.c_str());
      websocketpp::lib::error_code ec;
      ec = m_con->send(data, websocketpp::frame::opcode::text);
	   if (ec)
	   {
		  stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::sendWebsocketClient failed ec=%d", ec.value());
	   }
      else
      {
          stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::sendWebsocketClient success");
      }
   });

   return 0;
}

void WebSocketJsBridge::shutdownWebsocketClient()
{
   m_endpoint.get_io_service().post([this] () {
      if (m_con.get() != NULL)
      {
         // for each connection call close
         m_con->set_close_handler([] (websocketpp::connection_hdl) {
         });
         m_con->close(0, "");
      }

      // Unflag the endpoint as perpetual. This will instruct it to stop once
      // all connections are finished.
      m_endpoint.stop_perpetual();
   });

   // Block until everything is done
   m_thread->join();
}

void WebSocketJsBridge::on_message(websocketpp::connection_hdl h, message_ptr msg)
{
   stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::on_message");
   mPhone->getSdkModuleThread().post(resip::resip_bind(&WebSocketJsBridge::handle_on_message, this, h, msg));
}

void WebSocketJsBridge::handle_on_message(websocketpp::connection_hdl h, message_ptr msg)
{
   HandleScope handle_scope(getIsolate());
   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   MessageEvent* messageEvt = new MessageEvent(getIsolate());
   messageEvt->data = msg->get_payload();
   messageEvt->origin = m_endpoint.get_con_from_hdl(h)->get_uri()->str();

   // Set up an exception handler before calling the callback function
   TryCatch try_catch;
   
   const int argc=1;
   v8::Handle<v8::Value> argv[] = { messageEvt->WrapMessageEvent() };
   v8::Local<v8::Function> onmessage_fun = v8::Local<v8::Function>::New(getIsolate(), jsOnMessageFun);
   Handle<Value> result = onmessage_fun->Call(context->Global(), argc, argv);

   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
   }   
}

void WebSocketJsBridge::on_fail(websocketpp::connection_hdl h)
{
   std::error_code ec = m_endpoint.get_con_from_hdl(h)->get_ec();
   stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::on_fail");
   mPhone->getSdkModuleThread().post(resip::resip_bind(&WebSocketJsBridge::handle_on_fail, this, h, ec));
}

void WebSocketJsBridge::handle_on_fail(websocketpp::connection_hdl, std::error_code ec)
{
   HandleScope handle_scope(getIsolate());
   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Set up an exception handler before calling the callback function
   TryCatch try_catch;
   
   const int argc=0;
   v8::Handle<v8::Value>* argv = NULL;
   v8::Local<v8::Function> onerror_fun = v8::Local<v8::Function>::New(getIsolate(), jsOnErrorFun);
   Handle<Value> result = onerror_fun->Call(context->Global(), argc, argv);

   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
   }
}

void WebSocketJsBridge::on_open(websocketpp::connection_hdl h)
{
   stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::on_open");
   mPhone->getSdkModuleThread().post(resip::resip_bind(&WebSocketJsBridge::handle_on_open, this, h));
}

void WebSocketJsBridge::handle_on_open(websocketpp::connection_hdl)
{
   HandleScope handle_scope(getIsolate());
   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Set up an exception handler before calling the callback function
   TryCatch try_catch;
   
   const int argc=0;
   v8::Handle<v8::Value>* argv = NULL;
   v8::Local<v8::Function> onopen_fun = v8::Local<v8::Function>::New(getIsolate(), jsOnOpenFun);
   Handle<Value> result = onopen_fun->Call(context->Global(), argc, argv);

   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
   }
}

void WebSocketJsBridge::on_close(websocketpp::connection_hdl h)
{
   stdLog(LogLevel::LogLevel_Info, "WebSocketJsBridge::on_close");
   mPhone->getSdkModuleThread().post(resip::resip_bind(&WebSocketJsBridge::handle_on_close, this, h));
}

void WebSocketJsBridge::handle_on_close(websocketpp::connection_hdl)
{
   HandleScope handle_scope(getIsolate());
   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Set up an exception handler before calling the callback function
   TryCatch try_catch;
   
   const int argc=0;
   v8::Handle<v8::Value>* argv = NULL;
   v8::Local<v8::Function> onclose_fun = v8::Local<v8::Function>::New(getIsolate(), jsOnCloseFun);
   Handle<Value> result = onclose_fun->Call(context->Global(), argc, argv);

   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
   }
}


///////////////////////////////////////////////////////////////////////////////
void MessageEvent::getData(Local<String> name, const PropertyCallbackInfo<Value>& info) 
{
  // Extract the C++ request object from the JavaScript wrapper.
  MessageEvent* msgEvt = UnwrapMessageEvent(info.Holder());

  // Fetch the path.
  const std::string& dataStr = msgEvt->data;

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(String::NewFromUtf8(
      info.GetIsolate(), dataStr.c_str(), String::kNormalString,
      static_cast<int>(dataStr.length())));
}

void MessageEvent::getOrigin(Local<String> name, const PropertyCallbackInfo<Value>& info) 
{
  // Extract the C++ request object from the JavaScript wrapper.
  MessageEvent* msgEvt = UnwrapMessageEvent(info.Holder());

  // Fetch the path.
  const std::string& dataStr = msgEvt->origin;

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(String::NewFromUtf8(
      info.GetIsolate(), dataStr.c_str(), String::kNormalString,
      static_cast<int>(dataStr.length())));
}

void MessageEvent::getLastEventId(Local<String> name, const PropertyCallbackInfo<Value>& info) 
{
  // Extract the C++ request object from the JavaScript wrapper.
  MessageEvent* msgEvt = UnwrapMessageEvent(info.Holder());

  // Fetch the path.
  const std::string& dataStr = msgEvt->lastEventId;

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(String::NewFromUtf8(
      info.GetIsolate(), dataStr.c_str(), String::kNormalString,
      static_cast<int>(dataStr.length())));
}


Handle<ObjectTemplate> MessageEvent::MakeMessageEventTemplate(Isolate* isolate) 
{
  HandleScope handle_scope(isolate);

  Handle<ObjectTemplate> result = ObjectTemplate::New();
  result->SetInternalFieldCount(1);

  // Add accessors for each of the fields of the request.
  result->SetAccessor(
     String::NewFromUtf8(isolate, "data", String::kInternalizedString),
     MessageEvent::getData);
  result->SetAccessor(
     String::NewFromUtf8(isolate, "origin", String::kInternalizedString),
     MessageEvent::getOrigin);
  result->SetAccessor(
     String::NewFromUtf8(isolate, "lastEventId", String::kInternalizedString),
     MessageEvent::getLastEventId);

  // Again, return the result through the current handle scope.
  return handle_scope.Close(result);
}

Handle<Object> MessageEvent::WrapMessageEvent() 
{
  // Handle scope for temporary handles.
  HandleScope handle_scope(mIsolate);

  // Fetch the template for creating JavaScript http request wrappers.
  // It only has to be created once, which we do on demand.
  if (mTemplate.IsEmpty()) {
    Handle<ObjectTemplate> raw_template = MakeMessageEventTemplate(mIsolate);
    mTemplate.Reset(mIsolate, raw_template);
  }
  Handle<ObjectTemplate> templ =
      Local<ObjectTemplate>::New(mIsolate, mTemplate);

  // Create an empty http request wrapper.
  Handle<Object> result = templ->NewInstance();

  // Wrap the raw C++ pointer in an External so it can be referenced
  // from within JavaScript.
  Handle<External> request_ptr = External::New(mIsolate, this);

  // Store the request pointer in the JavaScript wrapper.
  result->SetInternalField(0, request_ptr);

  // Return the result through the current handle scope.  Since each
  // of these handles will go away when the handle scope is deleted
  // we need to call Close to let one, the result, escape into the
  // outer handle scope.
  return handle_scope.Close(result);
}

MessageEvent* MessageEvent::UnwrapMessageEvent(Handle<Object> obj) 
{
  Handle<External> field = Handle<External>::Cast(obj->GetInternalField(0));
  void* ptr = field->Value();
  return static_cast<MessageEvent*>(ptr);
}
}
}

#endif // CPCAPI2_WEB_CALL_MODULE

