#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)

#include "CPRTCSessionDescriptionJsBridge.h"
#include "WebCallManagerImpl.h"
#include "../phone/PhoneInterface.h"

#include <iostream>
#include <map>
#include <queue>
#include <string>

using namespace v8;

namespace CPCAPI2
{
namespace WebCall
{
CPRTCSessionDescription::CPRTCSessionDescription(v8::Isolate* isolate, const v8::Persistent<v8::Context>& context, CPCAPI2::PhoneInterface* pi) : mIsolate(isolate), mPhone(pi)
{
   mContext.Reset(isolate, context);
}

CPRTCSessionDescription::~CPRTCSessionDescription()
{
   jsObj.Reset();
   mRtcSessionDescTemplate.Reset();
}

void CPRTCSessionDescription::CPRTCSessionDescriptionCtor(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   //Locker lock;
   v8::HandleScope scope(args.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Handle<ObjectTemplate> t = MakeTemplate(args.GetIsolate());

   Local<External> dataExt = Local<External>::Cast(args.Data());
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(dataExt->Value());
   PhoneInterface* pi = wcm->getPhoneInterface();

   //The JavaScript point object only has 1 C++ object
   t->SetInternalFieldCount(1);

   Local<Object> obj;
   CPRTCSessionDescription* sessDesc = NULL;

   if(!args[0].IsEmpty() && args[0]->IsObject()) 
   {
      v8::Handle<v8::Object> sessDescObj = v8::Handle<v8::Object>::Cast(args[0]);
      v8::Local<v8::Value> typeJsVal = sessDescObj->GetRealNamedProperty(String::NewFromUtf8(args.GetIsolate(),"type"));
      String::Utf8Value typeJsStr(typeJsVal);
      v8::Local<v8::Value> sdpJsVal = sessDescObj->GetRealNamedProperty(String::NewFromUtf8(args.GetIsolate(),"sdp"));
      String::Utf8Value sdpJsStr(sdpJsVal);
      sessDesc = new CPRTCSessionDescription(args.GetIsolate(), wcm->getContext(), pi);
      if (resip::isEqualNoCase(*typeJsStr, "offer"))
      {
         sessDesc->disposition = recon::AVOfferAnswerSession::SdpDisposition_Offer;
      }
      else if (resip::isEqualNoCase(*typeJsStr, "answer"))
      {
         sessDesc->disposition = recon::AVOfferAnswerSession::SdpDisposition_Answer;
      }
      else if (resip::isEqualNoCase(*typeJsStr, "pranswer"))
      {
         sessDesc->disposition = recon::AVOfferAnswerSession::SdpDisposition_Pranswer;
      }
      sessDesc->sdp = *sdpJsStr;

      obj = t->NewInstance();
      obj->SetInternalField(0, External::New(args.GetIsolate(), sessDesc));                        
      // !jjg! this line might be completely wrong
      sessDesc->jsObj.Reset(args.GetIsolate(), obj);

      // Return this newly created object
      args.GetReturnValue().Set(obj);
   }
}

void CPRTCSessionDescription::getSdp(Local<String> name, const PropertyCallbackInfo<Value>& info) 
{
  // Extract the C++ request object from the JavaScript wrapper.
  CPRTCSessionDescription* rtcSessDes = Unwrap(info.Holder());

  // Fetch the path.
  const std::string& sdp = rtcSessDes->sdp;

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(String::NewFromUtf8(
      info.GetIsolate(), sdp.c_str(), String::kNormalString,
      static_cast<int>(sdp.length())));
}

void CPRTCSessionDescription::getType(Local<String> name, const PropertyCallbackInfo<Value>& info) 
{
  // Extract the C++ request object from the JavaScript wrapper.
  CPRTCSessionDescription* rtcSessDes = Unwrap(info.Holder());

  // Fetch the path.
  recon::AVOfferAnswerSession::SdpDisposition sdpType = rtcSessDes->disposition;
  std::string sdpTypeStr;
  switch (sdpType)
  {
  case recon::AVOfferAnswerSession::SdpDisposition_Offer:
     sdpTypeStr = "offer";
     break;
  case recon::AVOfferAnswerSession::SdpDisposition_Answer:
     sdpTypeStr = "answer";
     break;
  case recon::AVOfferAnswerSession::SdpDisposition_Pranswer:
     sdpTypeStr = "pranswer";
     break;
  default:
     break;
  }

  // Wrap the result in a JavaScript string and return it.
  info.GetReturnValue().Set(String::NewFromUtf8(
      info.GetIsolate(), sdpTypeStr.c_str(), String::kNormalString,
      static_cast<int>(sdpTypeStr.length())));
}

Handle<ObjectTemplate> CPRTCSessionDescription::MakeTemplate(Isolate* isolate) 
{
  HandleScope handle_scope(isolate);

  Handle<ObjectTemplate> result = ObjectTemplate::New();
  result->SetInternalFieldCount(1);

  // Add accessors for each of the fields of the request.
  result->SetAccessor(
     String::NewFromUtf8(isolate, "sdp", String::kInternalizedString),
     getSdp);
  result->SetAccessor(
     String::NewFromUtf8(isolate, "type", String::kInternalizedString),
     getType);

  // Again, return the result through the current handle scope.
  return handle_scope.Close(result);
}

Handle<Object> CPRTCSessionDescription::Wrap() 
{
  // Handle scope for temporary handles.
  HandleScope handle_scope(getIsolate());

  // Fetch the template for creating JavaScript http request wrappers.
  // It only has to be created once, which we do on demand.
  if (mRtcSessionDescTemplate.IsEmpty()) {
    Handle<ObjectTemplate> raw_template = MakeTemplate(getIsolate());
    mRtcSessionDescTemplate.Reset(getIsolate(), raw_template);
  }
  Handle<ObjectTemplate> templ =
      Local<ObjectTemplate>::New(getIsolate(), mRtcSessionDescTemplate);

  // Create an empty http request wrapper.
  Handle<Object> result = templ->NewInstance();

  // Wrap the raw C++ pointer in an External so it can be referenced
  // from within JavaScript.
  Handle<External> request_ptr = External::New(getIsolate(), this);

  // Store the request pointer in the JavaScript wrapper.
  result->SetInternalField(0, request_ptr);

  // Return the result through the current handle scope.  Since each
  // of these handles will go away when the handle scope is deleted
  // we need to call Close to let one, the result, escape into the
  // outer handle scope.
  return handle_scope.Close(result);
}

CPRTCSessionDescription* CPRTCSessionDescription::Unwrap(Handle<Object> obj) 
{
  Handle<External> field = Handle<External>::Cast(obj->GetInternalField(0));
  void* ptr = field->Value();
  return static_cast<CPRTCSessionDescription*>(ptr);
}


}
}

#endif // CPCAPI2_WEB_CALL_MODULE

