#pragma once

#if !defined(CPCAPI2_WEB_CALL_MANAGER_INTERFACE_H)
#define CPCAPI2_WEB_CALL_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "webcall/WebCallManager.h"
#include "../phone/PhoneModule.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>

#include <map>
#include <string>
#include <vector>

#include <rutil/MultiReactor.hxx>
#include <rutil/SelectInterruptor.hxx>
#include <resip/dum/DumCommand.hxx>

namespace CPCAPI2
{
class PhoneInterface;

namespace Media
{
class MediaManagerInterface;
}

namespace WebCall
{
class WebCallManagerImpl;
struct WebCallCreationInfo;

class WebCallManagerInterface : public WebCallManager, 
                                public PhoneModule
#ifdef CPCAPI2_AUTO_TEST
                                , public AutoTestProcessor
#endif
{
public:
   WebCallManagerInterface(Phone* phone);
   virtual ~WebCallManagerInterface();

   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif
   virtual void interruptProcess() OVERRIDE;

   virtual int setHandler(WebCallHandler* handler) OVERRIDE;

   virtual WebNavigationHandle navigate(const cpc::string& targetURL) OVERRIDE;
   virtual int closeWebSession(WebNavigationHandle webSession) OVERRIDE;

   virtual WebCallHandle createConversation(WebNavigationHandle context) OVERRIDE;
   virtual int addParticipant(WebCallHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int setMediaEnabled(WebCallHandle conversation, CPCAPI2::WebCall::MediaType mediaType, bool enabled) OVERRIDE;
   virtual int configureMedia(WebCallHandle conversation, const CPCAPI2::WebCall::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int setAnonymousMode(WebCallHandle conversation, unsigned int anonymousMode) OVERRIDE;
   virtual int start(WebCallHandle conversation) OVERRIDE;

   virtual int hold(WebCallHandle conversation) OVERRIDE;
   virtual int unhold(WebCallHandle conversation) OVERRIDE;
   virtual int sendMediaChangeRequest(WebCallHandle conversation) OVERRIDE;

   virtual int end(WebCallHandle conversation) OVERRIDE;

   // incoming
   virtual int redirect(WebCallHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int sendRingingResponse(WebCallHandle conversation) OVERRIDE;
   virtual int reject(WebCallHandle conversation, unsigned int rejectReason = 0) OVERRIDE;
   virtual int accept(WebCallHandle conversation) OVERRIDE;

   // transfer
   virtual int transfer(WebCallHandle transferTargetConversation, WebCallHandle transfereeConversation) OVERRIDE;
   virtual int transfer(WebCallHandle conversation, const cpc::string& targetAddress) OVERRIDE;
   virtual int acceptIncomingTransferRequest(WebCallHandle transferTargetConversation) OVERRIDE;
   virtual int rejectIncomingTransferRequest(WebCallHandle transferTargetConversation) OVERRIDE;

   // DTMF
   virtual int setDtmfMode(unsigned int ordinal, CPCAPI2::WebCall::DtmfMode dtmfMode) OVERRIDE;
   virtual int startDtmfTone(WebCallHandle conversation, unsigned int toneId, bool playLocally) OVERRIDE;
   virtual int stopDtmfTone();

   virtual int refreshConversationStatistics(WebCallHandle conversation) OVERRIDE;

   PhoneInterface* phoneInterface();

   void addSdkObserver(WebCallHandler* observer);
   void removeSdkObserver(WebCallHandler* observer);

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

private:
   int setHandlerImpl(
         WebCallHandler* handler);

   int navigateImpl(WebNavigationHandle h, const cpc::string& targetURL);
   int closeWebSessionImpl(WebNavigationHandle webSession);

   WebCallManagerImpl* initImpl(WebNavigationHandle h);

   int createConversationImpl(WebNavigationHandle context, WebCallHandle h);
   int addParticipantImpl(WebCallHandle conversation, const cpc::string& targetAddress);
   int setMediaEnabledImpl(WebCallHandle conversation, MediaType mediaType, bool enabled);
   int configureMediaImpl(WebCallHandle conversation, const MediaInfo& mediaDescriptor);
   int setAnonymousModeImpl(WebCallHandle conversation, unsigned int anonymousMode);
   int startImpl(WebCallHandle conversation);
   int holdImpl(WebCallHandle conversation);
   int unholdImpl(WebCallHandle conversation);
   int sendMediaChangeRequestImpl(WebCallHandle conversation);
   int endImpl(WebCallHandle conversation);
   int redirectImpl(WebCallHandle conversation, const cpc::string& targetAddress);
   int sendRingingResponseImpl(WebCallHandle conversation);
   int rejectImpl(WebCallHandle conversation, unsigned int rejectReason);
   int acceptImpl(WebCallHandle conversation);
   int transferImpl(WebCallHandle transferTargetConversation, WebCallHandle transfereeConversation);
   int transferImpl(WebCallHandle conversation, const cpc::string& targetAddress);
   int acceptIncomingTransferRequestImpl(WebCallHandle transferTargetConversation);
   int setDtmfModeImpl(unsigned int ordinal, CPCAPI2::WebCall::DtmfMode dtmfMode);
   int startDtmfToneImpl(WebCallHandle conversation, unsigned int toneId, bool playLocally);
   int refreshConversationStatisticsImpl(WebCallHandle conversation);
   int dtmfToneChannelCleanup(int channel_for_tone);

   WebCallCreationInfo* getCreationInfo(WebCallHandle h);
   WebCallManagerImpl* getConvImpl(WebCallHandle h);

   void fireError(const cpc::string& msg);

private:
   bool mShutdown;
   typedef std::map<WebNavigationHandle, WebCallManagerImpl*> ContextMap;
   ContextMap mContextMap;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;

   CPCAPI2::Media::MediaManagerInterface* mMediaIf;
   PhoneInterface* mPhone;
   std::set<WebCallHandler*> mSdkObservers;
   WebCallHandler* mAppHandler;
};

class WebNavigationHandleFactory
{
public:
   static WebNavigationHandle getNext() { return sNextHandle++; }
private:
   static WebNavigationHandle sNextHandle;
};

class WebCallHandleFactory
{
public:
   static WebCallHandle getNext() { return sNextHandle++; }
private:
   static WebCallHandle sNextHandle;
};
}
}

#endif // CPCAPI2_WEB_CALL_MANAGER_INTERFACE_H
