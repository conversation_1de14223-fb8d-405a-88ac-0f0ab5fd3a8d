#pragma once

#if !defined(CPCAPI2_WEB_CALL_MANAGER_IMPL_H)
#define CPCAPI2_WEB_CALL_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "webcall/WebCallTypes.h"
#include "webcall/WebCallManager.h"
#include "webcall/WebCallHandler.h"
#include "WebCallCreationInfo.h"

#include <resip/recon/AVOfferAnswerSession.hxx>

#include <vector>
#include <v8.h>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
class PhoneInterface;

namespace WebCall
{
class WebCallHandler;
class WebCallManagerInterface;
class WebCallDisposable;

class WebCallManagerImpl
{
public:
   WebCallManagerImpl(WebCallManagerInterface* iface, webrtc_recon::MediaStackImpl* ms, WebCallHandler* handler, WebNavigationHandle contextHandle);
   virtual ~WebCallManagerImpl();

   void addAppHandler(WebCallHandler* handler);
   void navigate(const cpc::string& targetURL);
   void closeWebSession();

   void fireError(const WebCallHandle& h, const cpc::string& errorText);
   void fireStatisticsUpdated(const WebCallHandle& h, const ConversationStatistics& stats);

   WebCallCreationInfo* getCreationInfo(const WebCallHandle& h) const;
   void addCreationInfo(const WebCallHandle& h, WebCallCreationInfo* ci);
   void removeCreationInfo(const WebCallHandle& h);

   std::vector<DtmfMode>& dtmfPreferences() { return mDtmfModePreferentialOrder; }

   int initScript(const std::string& scriptUtf8);
   int executeScript(v8::Handle<v8::String> script);
   v8::Isolate* getIsolate() { return mIsolate; }
   const v8::Persistent<v8::Context>& getContext() const { return mContext; }
   void addDisposable(WebCallDisposable* disposable);

   // interface to call control functions implemented in javascript
   int javascriptMakeCall(WebCallHandle h);
   int javascriptHangup(WebCallHandle h);
   int javascriptAnswer(WebCallHandle h);

   CPCAPI2::PhoneInterface* getPhoneInterface() const;

   void handleSdpReady(const std::string& sdp, recon::AVOfferAnswerSession::SdpDisposition sdpDisposition, v8::Persistent<v8::Function>& jsSdpReadyFun);

private:
   int parseXML(const std::string& xmlUtf8, std::string& scriptSrc);

   static void CPRTCPeerConnectionCtor(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void setLocalDescriptionWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void setRemoteDescriptionWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void createOfferWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void createAnswerWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void closeWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);

   static void console_log_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void consoleGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo);

   static void windowGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo);
   static void window_location_wrapper(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo);
   static void location_host_wrapper(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo);

   static void cprtcGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo);
   static void cprtc_fireNewConversation_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   void fireNewConversation(WebCallHandle h, v8::Handle<v8::Object> pc);
   static void cprtc_fireConversationEnded_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   void fireConversationEnded(WebCallHandle h);
   static void cprtc_fireConversationStateChanged_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   void fireConversationStateChanged(WebCallHandle h);
   void fireConversationMediaChanged(WebCallHandle h);
   static void cprtc_fireNewIncomingConversation_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   WebCallHandle fireNewIncomingConversation(v8::Handle<v8::Object> pc, const std::string& address, const std::string& displayName);

   WebCallHandle getHandleForAVOA(recon::AVOfferAnswerSession* avoa);

private:
   WebCallHandler* mAppHandler;
   std::vector<DtmfMode> mDtmfModePreferentialOrder;
   WebCallManagerInterface* m_if;
   WebCallCreationInfoMap mCallCreationInfo;
   v8::Isolate* mIsolate;
   v8::Persistent<v8::Context> mContext;
   v8::Persistent<v8::Function> mMakeCall;
   v8::Persistent<v8::Function> mHangup;
   v8::Persistent<v8::Function> mAnswer;
   WebNavigationHandle mContextHandle;
   std::string mHost;
   std::vector<WebCallDisposable*> mDisposables;
};

}
}

#endif // CPCAPI2_WEB_CALL_MANAGER_IMPL_H
