#include "brand_branded.h"

#include "interface/experimental/webcall/WebCallManager.h"

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
#include "WebCallManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace WebCall
{
WebCallManager* WebCallManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<WebCallManagerInterface>(phone, "WebCallManagerInterface");
#else
   return NULL;
#endif
}

}
}
