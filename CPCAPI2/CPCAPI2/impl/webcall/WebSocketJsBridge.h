#pragma once

#include "WebCallDisposable.h"

#include <v8.h>
#include <websocketpp/common/thread.hpp>
#include <websocketpp/config/asio_no_tls_client.hpp>
#include <websocketpp/client.hpp>

namespace CPCAPI2
{
class PhoneInterface;

namespace WebCall
{
struct MessageEvent
{
   MessageEvent(v8::Isolate* isolate) : mIsolate(isolate) {}
   ~MessageEvent() { mTemplate.Reset(); }

   std::string data;
   std::string origin;
   std::string lastEventId;

   static void getData(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void getOrigin(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void getLastEventId(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);

   static v8::Handle<v8::ObjectTemplate> MakeMessageEventTemplate(v8::Isolate* isolate);
   v8::Handle<v8::Object> WrapMessageEvent();
   static MessageEvent* UnwrapMessageEvent(v8::Handle<v8::Object> obj);

private:
   v8::Isolate* mIsolate;
   v8::Persistent<v8::ObjectTemplate> mTemplate;
};

class WebSocketJsBridge : public WebCallDisposable
{
public:
   WebSocketJsBridge(v8::Isolate* isolate, const v8::Persistent<v8::Context>& context, CPCAPI2::PhoneInterface* pi);
   virtual ~WebSocketJsBridge();

   virtual void Dispose();

   static void WebSocketCtor(const v8::FunctionCallbackInfo<v8::Value>& args);

   int connectWebsocketClient(const std::string& uri);
   int sendWebsocketClient(const std::string& data);

private:
   v8::Isolate* getIsolate() { return mIsolate; }

   static WebSocketJsBridge* UnwrapWebSocket(v8::Handle<v8::Object> obj);
   static void sendWrapper(const v8::FunctionCallbackInfo<v8::Value>& args);
   static void getOnError(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void setOnError(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info);
   static void getOnOpen(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void setOnOpen(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info);
   static void getOnMessage(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void setOnMessage(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info);
   static void getOnClose(v8::Local<v8::String> name, const v8::PropertyCallbackInfo<v8::Value>& info);
   static void setOnClose(v8::Local<v8::String> prop, v8::Local<v8::Value> value, const v8::PropertyCallbackInfo<void>& info);

   // websocket++ handlers
   typedef websocketpp::config::asio_client::message_type::ptr message_ptr;

   void initWebsocketClient();
   void shutdownWebsocketClient();
   void on_message(websocketpp::connection_hdl, message_ptr msg);
   void handle_on_message(websocketpp::connection_hdl, message_ptr msg);
   void on_fail(websocketpp::connection_hdl);
   void handle_on_fail(websocketpp::connection_hdl, std::error_code ec);
   void on_open(websocketpp::connection_hdl);
   void handle_on_open(websocketpp::connection_hdl);
   void on_close(websocketpp::connection_hdl);
   void handle_on_close(websocketpp::connection_hdl);

private:
   v8::Isolate* mIsolate;
   v8::Persistent<v8::Context> mContext;
   CPCAPI2::PhoneInterface* mPhone;
   v8::Persistent<v8::Object> jsObj; // the instance of WebSocket in Javascript-land

   v8::Persistent<v8::Function> jsOnErrorFun;
   v8::Persistent<v8::Function> jsOnOpenFun;
   v8::Persistent<v8::Function> jsOnMessageFun;
   v8::Persistent<v8::Function> jsOnCloseFun;

   typedef websocketpp::lib::thread thread_type;
   typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;
   typedef websocketpp::client<websocketpp::config::asio_client> client;
    
   client m_endpoint;
   thread_ptr m_thread;
   client::connection_ptr m_con;

};

}
}

