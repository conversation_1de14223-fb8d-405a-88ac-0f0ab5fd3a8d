#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
#include "cpcapi2utils.h"
#include "WebCallManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "WebCallManagerImpl.h"
#include "../util/dtmf_tone_helper.h"
#include "../util/ResipConv.h"

#include "../media/MediaManagerInterface.h"
#include "media/audio/Audio.h"
#include "../media/PlaySoundHelper.h"
#include <MediaStackImpl.hxx>
#include <MediaStackSettingsImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <RtpStreamImpl.hxx>

// video support
#include "media/video/Video.h"
#include "../media/VideoInterface.h"

#include <resip/stack/ExtensionParameter.hxx>

#pragma comment(lib, "v8.lib")
#pragma comment(lib, "icui18n.lib")
#pragma comment(lib, "icuuc.lib")

using namespace CPCAPI2::Media;
using resip::ReadCallbackBase;

namespace CPCAPI2
{
namespace WebCall
{
WebCallHandle WebCallHandleFactory::sNextHandle = 1;
WebNavigationHandle WebNavigationHandleFactory::sNextHandle = 1;

WebCallManagerInterface::WebCallManagerInterface(Phone* phone)
	: mShutdown(false),
     mMediaIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mAppHandler(NULL)
{
   mMediaIf = dynamic_cast<MediaManagerInterface*>(MediaManager::getInterface(phone));
}

WebCallManagerInterface::~WebCallManagerInterface()
{
}
   
void WebCallManagerInterface::Release()
{
   delete this;
}

int WebCallManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kWebCallModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kWebCallModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* WebCallManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void WebCallManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void WebCallManagerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}
   
void WebCallManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   //if (mCbHook) { mCbHook(); }
}

PhoneInterface* WebCallManagerInterface::phoneInterface()
{
   return mPhone;
}

void WebCallManagerInterface::addSdkObserver(WebCallHandler* observer)
{
   mSdkObservers.insert(observer);
}

void WebCallManagerInterface::removeSdkObserver(WebCallHandler* observer)
{
   mSdkObservers.erase(observer);
}

int WebCallManagerInterface::setHandler(
      WebCallHandler* handler)
{
   resip::ReadCallbackBase* configureCmd = resip::resip_bind(&WebCallManagerInterface::setHandlerImpl, this, handler);
   post(configureCmd);
   return kSuccess;
}

int WebCallManagerInterface::setHandlerImpl(
      WebCallHandler* handler)
{
   mAppHandler = handler;
   return kSuccess;
}

WebNavigationHandle WebCallManagerInterface::navigate(const cpc::string& targetURL)
{
   WebNavigationHandle h = WebNavigationHandleFactory::getNext();
   resip::ReadCallbackBase* cmd = resip::resip_bind(&WebCallManagerInterface::navigateImpl, this, h, targetURL);
   post(cmd);
   return h;
}

int WebCallManagerInterface::navigateImpl(WebNavigationHandle h, const cpc::string& targetURL)
{
   WebCallManagerImpl* convMan = initImpl(h);
   convMan->navigate(targetURL);
   //convMan->setDefaultSettings(settings);
   return kSuccess;
}

int WebCallManagerInterface::closeWebSession(WebNavigationHandle webSession)
{
   post(resip::resip_bind(&WebCallManagerInterface::closeWebSessionImpl, this, webSession));
   return kSuccess;
}

int WebCallManagerInterface::closeWebSessionImpl(WebNavigationHandle webSession)
{
   ContextMap::iterator it = mContextMap.find(webSession);
   if (it != mContextMap.end())
   {
      it->second->closeWebSession();
      delete it->second;
      mContextMap.erase(it);
   }
   return kSuccess;
}

WebCallManagerImpl* WebCallManagerInterface::initImpl(WebNavigationHandle context)
{
   webrtc_recon::MediaStackImpl* ms = mMediaIf->media_stack();
   ContextMap::iterator it = mContextMap.find(context);
   WebCallManagerImpl* convMan = (it == mContextMap.end() ? NULL : it->second);
   if (convMan == NULL)
   {
      convMan = new WebCallManagerImpl(this, ms, mAppHandler, context);
      mContextMap[context] = convMan;
   }
   return convMan;
}

WebCallHandle WebCallManagerInterface::createConversation(WebNavigationHandle context)
{
   WebCallHandle h = WebCallHandleFactory::getNext();
   resip::ReadCallbackBase* cmd = resip::resip_bind(&WebCallManagerInterface::createConversationImpl, this, context, h);
   post(cmd);
   return h;
}

int WebCallManagerInterface::createConversationImpl(WebNavigationHandle context, WebCallHandle h)
{
   ContextMap::iterator it = mContextMap.find(context);
   if (it != mContextMap.end())
   {
      WebCallCreationInfo* ci = new WebCallCreationInfo();
      ci->context = context;
      ci->conversationState = ConversationState_LocalOriginated;
      ci->conversationType = ConversationType_Outgoing;
      it->second->addCreationInfo(h, ci);
      return kSuccess;
   }
   return kError;
}

WebCallCreationInfo* WebCallManagerInterface::getCreationInfo(WebCallHandle h)
{
   ContextMap::iterator itAcct = mContextMap.begin();
   for (; itAcct != mContextMap.end(); ++itAcct)
   {
      WebCallCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return ci;
      }
   }
   return NULL;
}

WebCallManagerImpl* WebCallManagerInterface::getConvImpl(WebCallHandle h)
{
   ContextMap::iterator itAcct = mContextMap.begin();
   for (; itAcct != mContextMap.end(); ++itAcct)
   {
      WebCallCreationInfo* ci = itAcct->second->getCreationInfo(h);
      if (ci != NULL)
      {
         return itAcct->second;
      }
   }
   return NULL;
}

int WebCallManagerInterface::addParticipant(WebCallHandle conversation, const cpc::string& targetAddress)
{
   post(resip::resip_bind(&WebCallManagerInterface::addParticipantImpl, 
      this, conversation, targetAddress));
   return kSuccess;
}

int WebCallManagerInterface::addParticipantImpl(WebCallHandle conversation, const cpc::string& targetAddress)
{
   WebCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ci->targetAddresses.push_back(targetAddress);
   }
   else
   {
      cpc::string msg = "WebCallManagerInterface::addParticipant called with invalid conversation handle: " + cpc::to_string(conversation);
      fireError(msg);
   }
   return kSuccess;
}

int WebCallManagerInterface::setMediaEnabled(WebCallHandle conversation, MediaType mediaType, bool enabled)
{
   post(resip::resip_bind(&WebCallManagerInterface::setMediaEnabledImpl, 
      this, conversation, mediaType, enabled));
   return kSuccess;
}

int WebCallManagerInterface::setMediaEnabledImpl(WebCallHandle conversation, MediaType mediaType, bool enabled)
{
	WebCallCreationInfo* ci = getCreationInfo(conversation);
	if (ci != NULL)
	{
		std::vector<MediaInfo>::iterator itMi = ci->localMediaInfo.begin();
		bool found = false;
		for (; itMi != ci->localMediaInfo.end(); ++itMi)
		{
			if (itMi->mediaType == mediaType)
			{
				found = true;
            if (enabled)
            {
               itMi->mediaDirection = MediaDirection_SendReceive;
            }
            else
            {
               itMi->mediaDirection = MediaDirection_None;
            }
			}
		}
		if (!found && enabled)
		{
         MediaInfo mi;
         mi.mediaType = mediaType;
         mi.mediaDirection = MediaDirection_SendReceive;
         mi.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_Unencrypted;
			ci->localMediaInfo.push_back(mi);
		}
	}
	return kSuccess;
}

int WebCallManagerInterface::configureMedia(WebCallHandle conversation, const MediaInfo& mediaDescriptor)
{
   post(resip::resip_bind(&WebCallManagerInterface::configureMediaImpl, 
      this, conversation, mediaDescriptor));
   return kSuccess;
}

int WebCallManagerInterface::configureMediaImpl(WebCallHandle conversation, const MediaInfo& mediaDescriptor)
{
	WebCallCreationInfo* ci = getCreationInfo(conversation);
	if (ci != NULL)
	{
		std::vector<MediaInfo>::iterator itMi = ci->localMediaInfo.begin();
		bool found = false;
		for (; itMi != ci->localMediaInfo.end(); ++itMi)
		{
			if (itMi->mediaType == mediaDescriptor.mediaType)
			{
				found = true;
            (*itMi) = mediaDescriptor;
			}
		}
		if (!found)
		{
			ci->localMediaInfo.push_back(mediaDescriptor);
		}
	}
	return kSuccess;
}

int WebCallManagerInterface::setAnonymousMode(WebCallHandle conversation, unsigned int anonymousMode)
{
   post(resip::resip_bind(&WebCallManagerInterface::setAnonymousModeImpl, 
      this, conversation, anonymousMode));
   return kSuccess;
}

int WebCallManagerInterface::setAnonymousModeImpl(WebCallHandle conversation, unsigned int anonymousMode)
{
	return kSuccess;
}

int WebCallManagerInterface::start(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::startImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::startImpl(WebCallHandle conversation)
{
   WebCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ContextMap::iterator itAcct = mContextMap.find(ci->context);
      if (itAcct != mContextMap.end())
      {
         WebCallManagerImpl* acct = itAcct->second;

         if (ci->targetAddresses.empty())
         {
            acct->fireError(conversation, "Cannot start conversation. No participants have been added");
            return kSuccess;
         }

         acct->javascriptMakeCall(conversation);
      }
      return kSuccess;
   }
   else
   {
      return kError;
   }
      
   return kSuccess;
}

int WebCallManagerInterface::hold(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::holdImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::holdImpl(WebCallHandle conversation)
{
   return kSuccess;
}

int WebCallManagerInterface::unhold(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::unholdImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::unholdImpl(WebCallHandle conversation)
{
   return kSuccess;
}

int WebCallManagerInterface::sendMediaChangeRequest(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::sendMediaChangeRequestImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::sendMediaChangeRequestImpl(WebCallHandle conversation)
{
   return kSuccess;
}

int WebCallManagerInterface::end(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::endImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::endImpl(WebCallHandle conversation)
{
   WebCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ContextMap::iterator itAcct = mContextMap.find(ci->context);
      if (itAcct != mContextMap.end())
      {
         WebCallManagerImpl* acct = itAcct->second;
         acct->javascriptHangup(conversation);
      }
   }
   return kSuccess;
}

int WebCallManagerInterface::redirect(WebCallHandle conversation, const cpc::string& targetAddress)
{
   post(resip::resip_bind(&WebCallManagerInterface::redirectImpl, 
      this, conversation, targetAddress));
   return kSuccess;
}

int WebCallManagerInterface::redirectImpl(WebCallHandle conversation, const cpc::string& targetAddress)
{
   return kSuccess;
}

int WebCallManagerInterface::sendRingingResponse(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::sendRingingResponseImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::sendRingingResponseImpl(WebCallHandle conversation)
{
   return kSuccess;
}

int WebCallManagerInterface::reject(WebCallHandle conversation, unsigned int rejectReason)
{
   post(resip::resip_bind(&WebCallManagerInterface::rejectImpl, 
      this, conversation, rejectReason));
   return kSuccess;
}

int WebCallManagerInterface::rejectImpl(WebCallHandle conversation, unsigned int rejectReason)
{
   WebCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ContextMap::iterator itAcct = mContextMap.find(ci->context);
      if (itAcct != mContextMap.end())
      {
			WebCallManagerImpl* acct = itAcct->second;

      }
   }
   return kSuccess;
}

int WebCallManagerInterface::accept(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::acceptImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::acceptImpl(WebCallHandle conversation)
{
   WebCallCreationInfo* ci = getCreationInfo(conversation);
   if (ci != NULL)
   {
      ContextMap::iterator itAcct = mContextMap.find(ci->context);
      if (itAcct != mContextMap.end())
      {
			WebCallManagerImpl* acct = itAcct->second;
         if (acct != NULL)
         {
            acct->javascriptAnswer(conversation);
         }
      }
   }
   return kSuccess;
}

int WebCallManagerInterface::transfer(WebCallHandle transferTargetConversation, WebCallHandle transfereeConversation)
{
   post(resip::resip_bind(static_cast<int (WebCallManagerInterface::*)(WebCallHandle,WebCallHandle)>(&WebCallManagerInterface::transferImpl), 
      this, transferTargetConversation, transfereeConversation));
   return kSuccess;
}

int WebCallManagerInterface::transferImpl(WebCallHandle transferTargetConversation, WebCallHandle transfereeConversation)
{
   return kSuccess;
}

int WebCallManagerInterface::transfer(WebCallHandle conversation, const cpc::string& targetAddress)
{
   post(resip::resip_bind(static_cast<int (WebCallManagerInterface::*)(WebCallHandle,const cpc::string&)>(&WebCallManagerInterface::transferImpl), 
      this, conversation, targetAddress));
   return kSuccess;
}

int WebCallManagerInterface::transferImpl(WebCallHandle conversation, const cpc::string& targetAddress)
{
   return kSuccess;
}

int WebCallManagerInterface::acceptIncomingTransferRequest(WebCallHandle transferTargetConversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::acceptIncomingTransferRequestImpl, 
      this, transferTargetConversation));
   return kSuccess;
}

int WebCallManagerInterface::acceptIncomingTransferRequestImpl(WebCallHandle transferTargetConversation)
{
   return kSuccess;
}

int WebCallManagerInterface::rejectIncomingTransferRequest(WebCallHandle transferTargetConversation)
{
   return kSuccess;
}

int WebCallManagerInterface::setDtmfMode(unsigned int ordinal, CPCAPI2::WebCall::DtmfMode dtmfMode)
{
   post(resip::resip_bind(&WebCallManagerInterface::setDtmfModeImpl, 
      this, ordinal, dtmfMode));
   return kSuccess;
}

int WebCallManagerInterface::setDtmfModeImpl(unsigned int ordinal, CPCAPI2::WebCall::DtmfMode dtmfMode)
{
   return kSuccess;
}

int WebCallManagerInterface::startDtmfTone(WebCallHandle conversation, unsigned int toneId, bool playLocally)
{
   post(resip::resip_bind(&WebCallManagerInterface::startDtmfToneImpl, 
      this, conversation, toneId, playLocally));
   return kSuccess;
}

int WebCallManagerInterface::startDtmfToneImpl(WebCallHandle conversation, unsigned int toneId, bool playLocally)
{
   return kError;
}

int WebCallManagerInterface::dtmfToneChannelCleanup(int channel_for_tone)
{
   mMediaIf->media_stack()->voe_base()->StopPlayout(channel_for_tone);
   mMediaIf->media_stack()->voe_base()->DeleteChannel(channel_for_tone);
   return kSuccess;
}

int WebCallManagerInterface::stopDtmfTone()
{
	return kSuccess;
}

int WebCallManagerInterface::refreshConversationStatistics(WebCallHandle conversation)
{
   post(resip::resip_bind(&WebCallManagerInterface::refreshConversationStatisticsImpl, 
      this, conversation));
   return kSuccess;
}

int WebCallManagerInterface::refreshConversationStatisticsImpl(WebCallHandle conversation)
{
   return kSuccess;
}

void WebCallManagerInterface::fireError(const cpc::string& msg)
{
}

}
}

#endif
