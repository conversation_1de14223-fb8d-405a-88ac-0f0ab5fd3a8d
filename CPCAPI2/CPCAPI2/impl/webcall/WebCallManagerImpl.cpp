#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEB_CALL_MODULE == 1)
#include "WebCallManagerImpl.h"
#include "WebCallManagerInterface.h"
#include "WebSocketJsBridge.h"
#include "WebCallDisposable.h"
#include "CPRTCSessionDescriptionJsBridge.h"
#include "webcall/WebCallHandler.h"
#include "../util/DumFpCommand.h"
#include "../util/HttpClient.h"
#include "../util/IpHelpers.h"
#include "../util/CurlURI.h"
#include "cpcapi2utils.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "media/MediaManager.h"
#include "../media/MediaManagerInterface.h"

#include <MediaStackImpl.hxx>

#include <libxml/xmlreader.h>
#include "../util/libXmlHelper.h"

#include <rutil/Data.hxx>
#include <rutil/DataStream.hxx>

#include "../util/PlatformLog.h"

using namespace CPCAPI2::PeerConnection;
using namespace v8;
using resip::ReadCallbackBase;

namespace CPCAPI2
{
namespace WebCall
{
class LibXmlParserInitHelper
{
public:
   LibXmlParserInitHelper() { }
   virtual ~LibXmlParserInitHelper() 
   {
      // do not call here; can cause crashes. see LibxmlSharedUsage.h
      //::xmlCleanupParser();
   }
};

void libxmlErrorHandler(void* arg, const char* msg, xmlParserSeverities severity, xmlTextReaderLocatorPtr locator)
{
}

WebCallManagerImpl::WebCallManagerImpl(WebCallManagerInterface* iface, webrtc_recon::MediaStackImpl* ms, WebCallHandler* handler, WebNavigationHandle contextHandle)
   : m_if(iface), mAppHandler(handler), mContextHandle(contextHandle), mDtmfModePreferentialOrder(( int ) DtmfMode_Everything )
{
   mDtmfModePreferentialOrder[ 0 ] = DtmfMode_RFC2833_Inband;
}

WebCallManagerImpl::~WebCallManagerImpl()
{
   mContext.Reset();
   mMakeCall.Reset();
   mHangup.Reset();
   mAnswer.Reset();
}

void WebCallManagerImpl::addDisposable(WebCallDisposable* disposable)
{
   mDisposables.push_back(disposable);
}

void WebCallManagerImpl::addAppHandler(WebCallHandler* handler)
{
   mAppHandler = handler;
}

void WebCallManagerImpl::navigate(const cpc::string& targetURL)
{
   stdLog(LogLevel::LogLevel_Info, "Navigate to %s", StringConv::wstringToUtf8(targetURL).c_str());

   int resultErrCode = -1;
   int responseStatus = -1;
   std::string resultBlob;
   std::string resultContentType;

   HTTPClient httpClient;
   httpClient.StartHTTPSession(
      0,
      HTTPClient::EHTTPVerbGET,
      StringConv::wstringToUtf8(targetURL.c_str()).c_str(),
      NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, false, false, false, false, "", 
      &resultErrCode, &responseStatus, resultContentType, resultBlob);

   std::string scriptSrc;
   parseXML(resultBlob, scriptSrc);

   xten::CurlURI webSiteUri(StringConv::wstringToUtf8(targetURL));
   resip::Data hostAsData;
   {
      resip::DataStream ds(hostAsData);
      ds << webSiteUri.host();
      ds << ":";
      ds << webSiteUri.port();
   }
   mHost = hostAsData.c_str();

   if (!scriptSrc.empty())
   {
      httpClient.DoSessionRequest(
         0,
         HTTPClient::EHTTPVerbGET,
         scriptSrc.c_str(),
         NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, false, false, false, false, "", 
         &resultErrCode, &responseStatus, resultContentType, resultBlob);

      if (!resultBlob.empty())
      {
         if (initScript(resultBlob) == kSuccess)
         {
            
         }
      }
   }
}

int WebCallManagerImpl::parseXML(const std::string& xmlUtf8, std::string& scriptSrc)
{
   int ret = 0;
   LibXmlParserInitHelper parserHelper; // nice way of making sure that we init/uninit the parser for this scope

   xmlTextReaderPtr reader = xmlReaderForMemory(
      xmlUtf8.c_str(),
      (int)xmlUtf8.size(),
      NULL,
      "UTF-8",
      128 // XML_PARSE_PEDANTIC
   );

   if (reader != NULL)
   {
      xmlTextReaderSetErrorHandler(reader, &libxmlErrorHandler, this);
      ret = xmlTextReaderRead(reader);
      while (ret == 1)
      {
			std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "script" && xmlTextReaderNodeType(reader) == 1)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
					scriptSrc = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"src"));
               ret = 0;
               break;
            }
         }
         ret = xmlTextReaderRead(reader);
      }
      xmlFreeTextReader(reader);
   }
   return ret;
}

WebCallCreationInfo* WebCallManagerImpl::getCreationInfo(const WebCallHandle& h) const
{
   WebCallCreationInfoMap::const_iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      return it->second;
   }
   return NULL;
}

void WebCallManagerImpl::addCreationInfo(const WebCallHandle& h, WebCallCreationInfo* ci)
{
   mCallCreationInfo[h] = ci;
}

void WebCallManagerImpl::removeCreationInfo(const WebCallHandle& h)
{
   mCallCreationInfo.erase(h);
}

void WebCallManagerImpl::closeWebSession()
{
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (it->second->avoaSession != NULL)
      {
         it->second->avoaSession->close();
         delete it->second->avoaSession;
      }
      fireConversationEnded(it->first);
      delete it->second;
      it->second = NULL;
   }
   mCallCreationInfo.clear();

   std::vector<WebCallDisposable*>::iterator itDisp = mDisposables.begin();
   for (; itDisp != mDisposables.end(); ++itDisp)
   {
      (*itDisp)->Dispose();
   }
   mDisposables.clear();
}

void WebCallManagerImpl::fireError(const WebCallHandle& h, const cpc::string& errorText)
{
   ErrorEvent event;
   event.errorText = errorText;

   ReadCallbackBase* cb = makeFpCommand(
      WebCallHandler::onError,
      mAppHandler,
      h,
      event);
   m_if->postCallback(cb);
}

void WebCallManagerImpl::fireStatisticsUpdated(const WebCallHandle& h, const ConversationStatistics& stats)
{
   ConversationStatisticsUpdatedEvent args;
   args.conversationStatistics = stats;

   ReadCallbackBase* cb = makeFpCommand(
      WebCallHandler::onConversationStatisticsUpdated,
      mAppHandler,
      h,
      args);
   m_if->postCallback(cb);
}

CPCAPI2::PhoneInterface* WebCallManagerImpl::getPhoneInterface() const
{
   return m_if->phoneInterface();
}

struct CPRTCPeerConnectionState : public recon::AVOfferAnswerSession::SdpReady
{
public:
   CPRTCPeerConnectionState(WebCallManagerImpl* cm) : wcm(cm)
   {}
   virtual ~CPRTCPeerConnectionState()
   {}

   virtual void onSdpReady()
   {
      PhoneInterface* phoneInterface = wcm->getPhoneInterface();
      phoneInterface->getSdkModuleThread().post(resip::resip_bind(&CPRTCPeerConnectionState::handleSdpReady, this));
   }

   void handleSdpReady()
   {
      if (!jsSdpReadyFun.IsEmpty())
      {
         wcm->handleSdpReady(resip::Data::from(*this->sdp).c_str(), this->sdpDisposition, jsSdpReadyFun);
      }
   }

   WebCallManagerImpl* wcm;
   recon::AVOfferAnswerSession* avoaSession;
   Persistent<Object> jsObj; // the instance of CPRTCPeerConnection in Javascript-land
   Persistent<Function> jsSdpReadyFun;
};

void WebCallManagerImpl::handleSdpReady(const std::string& sdp, recon::AVOfferAnswerSession::SdpDisposition sdpDisposition, Persistent<Function>& jsSdpReadyFun)
{
   HandleScope handle_scope(getIsolate());
   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   CPRTCSessionDescription* cppSessDesc = new CPRTCSessionDescription(getIsolate(), mContext, getPhoneInterface());
   cppSessDesc->sdp = sdp;
   cppSessDesc->disposition = sdpDisposition;
   Handle<Object> sessDescObj = cppSessDesc->Wrap();

   // Set up an exception handler before calling the callback function
   TryCatch try_catch;

   const int argc=1;
   v8::Handle<v8::Value> argv[argc] = { sessDescObj };
   v8::Local<v8::Function> sdpready_fun = v8::Local<v8::Function>::New(getIsolate(), jsSdpReadyFun);
   Handle<Value> result = sdpready_fun->Call(context->Global(), argc, argv);

   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
   }
}

void WebCallManagerImpl::setLocalDescriptionWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   stdLog(LogLevel::LogLevel_Info, "WebCallManagerImpl::setLocalDescriptionWrapper");
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());

   if(!args[0].IsEmpty() && args[0]->IsObject())
   {
      v8::Handle<v8::Object> sessDescObj = v8::Handle<v8::Object>::Cast(args[0]);
      CPRTCSessionDescription* sessDesc = CPRTCSessionDescription::Unwrap(sessDescObj);
      resip::ParseBuffer pb(sessDesc->sdp.data(), sessDesc->sdp.size());
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      if (pcState->avoaSession->setLocalDescription(resipSdp, sessDesc->disposition) == 0)
      {
         if (pcState->avoaSession->signalingState() == recon::AVOfferAnswerSession::SignalingState_stable)
         {
            pcState->wcm->fireConversationMediaChanged(pcState->wcm->getHandleForAVOA(pcState->avoaSession));
         }
      }
   }
}

void WebCallManagerImpl::setRemoteDescriptionWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   stdLog(LogLevel::LogLevel_Info, "WebCallManagerImpl::setRemoteDescriptionWrapper");
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());

   if(!args[0].IsEmpty() && args[0]->IsObject())
   {
      v8::Handle<v8::Object> sessDescObj = v8::Handle<v8::Object>::Cast(args[0]);
      CPRTCSessionDescription* sessDesc = CPRTCSessionDescription::Unwrap(sessDescObj);
      resip::ParseBuffer pb(sessDesc->sdp.data(), sessDesc->sdp.size());
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      if (pcState->avoaSession->setRemoteDescription(resipSdp, sessDesc->disposition) == 0)
      {
         if (pcState->avoaSession->signalingState() == recon::AVOfferAnswerSession::SignalingState_stable)
         {
            pcState->wcm->fireConversationMediaChanged(pcState->wcm->getHandleForAVOA(pcState->avoaSession));
         }
      }
   }
}

void addStreamWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());
   pcState->avoaSession->configureAudioStream("primaryAudio", recon::AVOfferAnswerSession::MediaDirection_SendRecv);
   pcState->avoaSession->configureVideoStream("primaryVideo", recon::AVOfferAnswerSession::MediaDirection_SendRecv);
}

void WebCallManagerImpl::createOfferWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   stdLog(LogLevel::LogLevel_Info, "WebCallManagerImpl::createOfferWrapper");
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());

   if(!args[0].IsEmpty() && args[0]->IsFunction())
   {
      v8::Handle<v8::Function> sdpready_fun = v8::Handle<v8::Function>::Cast(args[0]);
      pcState->jsSdpReadyFun.Reset(args.GetIsolate(), sdpready_fun);
      pcState->avoaSession->createOffer(false);
   }
}


void WebCallManagerImpl::createAnswerWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   stdLog(LogLevel::LogLevel_Info, "WebCallManagerImpl::createAnswerWrapper");
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());

   if(!args[0].IsEmpty() && args[0]->IsFunction())
   {
      v8::Handle<v8::Function> sdpready_fun = v8::Handle<v8::Function>::Cast(args[0]);
      pcState->jsSdpReadyFun.Reset(args.GetIsolate(), sdpready_fun);
      pcState->avoaSession->createAnswer();
   }
}

void WebCallManagerImpl::closeWrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(external->Value());
   pcState->avoaSession->close();
}

// Defines a CPRTCPeerConnection() JS Object
void WebCallManagerImpl::CPRTCPeerConnectionCtor(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   //Locker lock;
   v8::HandleScope scope(args.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Handle<ObjectTemplate> t = v8::ObjectTemplate::New(args.GetIsolate());

   //The JavaScript point object only has 1 C++ object
   t->SetInternalFieldCount(1);

   t->Set(String::NewFromUtf8(args.GetIsolate(), "createOffer"), FunctionTemplate::New(args.GetIsolate(), createOfferWrapper));
   t->Set(String::NewFromUtf8(args.GetIsolate(), "createAnswer"), FunctionTemplate::New(args.GetIsolate(), createAnswerWrapper));
   t->Set(String::NewFromUtf8(args.GetIsolate(), "addStream"), FunctionTemplate::New(args.GetIsolate(), addStreamWrapper));
   t->Set(String::NewFromUtf8(args.GetIsolate(), "setLocalDescription"), FunctionTemplate::New(args.GetIsolate(), setLocalDescriptionWrapper));
   t->Set(String::NewFromUtf8(args.GetIsolate(), "setRemoteDescription"), FunctionTemplate::New(args.GetIsolate(), setRemoteDescriptionWrapper));
   t->Set(String::NewFromUtf8(args.GetIsolate(), "close"), FunctionTemplate::New(args.GetIsolate(), closeWrapper));

   Local<External> dataExt = Local<External>::Cast(args.Data());
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(dataExt->Value());
   PhoneInterface* pi = wcm->getPhoneInterface();
   webrtc_recon::MediaStackImpl* mediaStack = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(pi))->media_stack();
   CPRTCPeerConnectionState* pcState = new CPRTCPeerConnectionState(wcm);
   pcState->avoaSession = new recon::AVOfferAnswerSession(mediaStack, pi->getSdkModuleThread());

   Local<Object> obj;

   recon::AVSessionConfig avconfig;
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), avconfig.ipAddress);
   avconfig.sessionName = "CPRTC";
   avconfig.secureMediaRequired = true;
   avconfig.secureMediaMode = 2;
   avconfig.certAor = "<EMAIL>";
   avconfig.natTraversalMode = 3;
   avconfig.natTraversalServerHostname = "stun.counterpath.com";
   avconfig.natTraversalServerPort = 3478;

   // If Point(x, y) ctor was passed in values assign them
   if(!args[0].IsEmpty() && args[0]->IsObject()) {
      //t->Set(String::New("x"), args[0]);
      //t->Set(String::New("y"), args[1]);
      //p = new Point(args[0]->Int32Value(), args[1]->Int32Value());

      /* args[0] is supposed to be of type RTCConfiguration where
         dictionary RTCConfiguration {
             sequence<RTCIceServer> iceServers;
             RTCIceTransports       iceTransports = "all";
             RTCIdentityOption      requestIdentity = "ifconfigured";
         };
      */

      obj = t->NewInstance();
      obj->SetInternalField(0, External::New(args.GetIsolate(), pcState));                        
   } else {
      /**
      * Wrap a point object
      */
      //p = new Point(0, 0);
      obj = t->NewInstance();
      obj->SetInternalField(0, External::New(args.GetIsolate(), pcState));
   }

   pcState->avoaSession->setHandler(pcState);
   pcState->avoaSession->applyConfiguration(avconfig);

   // !jjg! this line might be completely wrong
   pcState->jsObj.Reset(args.GetIsolate(), obj);

   // Return this newly created object
   args.GetReturnValue().Set(obj);
}

void WebCallManagerImpl::console_log_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   if(!args[0].IsEmpty() && args[0]->IsString())
   {
      Handle<Value> arg = args[0];
      String::Utf8Value value(arg);
      stdLog(LogLevel::LogLevel_Info, "JsConsole => %s", *value);
   }
}

void WebCallManagerImpl::consoleGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo)
{
   v8::HandleScope scope(propInfo.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct

   Handle<ObjectTemplate> consoleTemplate = v8::ObjectTemplate::New(propInfo.GetIsolate());
   consoleTemplate->SetInternalFieldCount(0);
   consoleTemplate->Set(String::NewFromUtf8(propInfo.GetIsolate(), "log"), FunctionTemplate::New(propInfo.GetIsolate(), console_log_wrapper));
   Local<Object> consoleObj = consoleTemplate->NewInstance();
   propInfo.GetReturnValue().Set(consoleObj);
}

void WebCallManagerImpl::location_host_wrapper(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo)
{
   v8::HandleScope scope(propInfo.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Local<External> dataExt = Local<External>::Cast(propInfo.Data());
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(dataExt->Value());
   propInfo.GetReturnValue().Set(String::NewFromUtf8(propInfo.GetIsolate(), wcm->mHost.c_str()));
}

void WebCallManagerImpl::window_location_wrapper(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo)
{
   v8::HandleScope scope(propInfo.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Local<External> dataExt = Local<External>::Cast(propInfo.Data());

   Handle<ObjectTemplate> locTemplate = v8::ObjectTemplate::New(propInfo.GetIsolate());
   locTemplate->SetAccessor(String::NewFromUtf8(propInfo.GetIsolate(), "host"), &WebCallManagerImpl::location_host_wrapper, 0, dataExt);
   Local<Object> locObj = locTemplate->NewInstance();
   propInfo.GetReturnValue().Set(locObj);
}

void WebCallManagerImpl::windowGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo)
{
   v8::HandleScope scope(propInfo.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct
   Local<External> dataExt = Local<External>::Cast(propInfo.Data());

   Handle<ObjectTemplate> windowTemplate = v8::ObjectTemplate::New(propInfo.GetIsolate());
   windowTemplate->SetAccessor(String::NewFromUtf8(propInfo.GetIsolate(), "location"), &WebCallManagerImpl::window_location_wrapper, 0, dataExt);
   Local<Object> windowObj = windowTemplate->NewInstance();
   propInfo.GetReturnValue().Set(windowObj);
}

std::string argToString(Handle<Value> hv)
{
   String::Utf8Value value(hv);
   return *value;
}

//void WebCallManagerImpl::cprtc_addConversation_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
//{
//   Local<v8::Object> self = args.Holder();
//   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
//   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(external->Value());
//
//   // params:
//   //   - callId
//   //   - CPRTCPeerConnection
//   //   - isIncoming
//   //   - remoteAddress
//   //   - remoteDisplayName
//
//   if(!args[0].IsEmpty() && args[0]->IsInt32() &&
//      !args[1].IsEmpty() && args[1]->IsObject() &&
//      !args[2].IsEmpty() && args[2]->IsBoolean() &&
//      !args[3].IsEmpty() && args[3]->IsString() &&
//      !args[4].IsEmpty() && args[4]->IsString())
//   {
//      WebCallHandle h = args[0]->Int32Value();
//      Local<v8::Object> pcJsObj = args[0];
//      Local<External> pcJsObjExt = Local<External>::Cast(pcJsObj->GetInternalField(0));
//      CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(pcJsObjExt->Value());
//
//      WebCallInfo evt;
//      evt.conversationState = args[2]->BooleanValue() ? ConversationState_RemoteOriginated : ConversationState_LocalOriginated;
//      evt.conversationType = args[2]->BooleanValue() ? ConversationType_Incoming : ConversationType_Outgoing;
//      evt.remoteAddress = StringConv::utf8ToWstring(argToString(args[3]));
//      evt.remoteDisplayName = StringConv::utf8ToWstring(argToString(args[4]));
//      evt.pcState = pcState;
//      wcm->addConversation(h, evt);
//   }
//}

void WebCallManagerImpl::cprtc_fireNewConversation_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(external->Value());

   // params:
   //   - callId

   if(!args[0].IsEmpty() && args[0]->IsInt32() && !args[1].IsEmpty())
   {
      WebCallHandle h = args[0]->Int32Value();
      v8::Handle<v8::Object> pcJsObj = v8::Handle<v8::Object>::Cast(args[1]);
      wcm->fireNewConversation(h, pcJsObj);
   }
}

void WebCallManagerImpl::fireNewConversation(WebCallHandle h, v8::Handle<v8::Object> pc)
{
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      Local<External> pcJsObjExt = Local<External>::Cast(pc->GetInternalField(0));
      CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(pcJsObjExt->Value());
      it->second->avoaSession = pcState->avoaSession;

      CPCAPI2::WebCall::NewConversationEvent evt;
      CPCAPI2::WebCall::MediaInfo miAudio;
      miAudio.mediaDirection = MediaDirection_SendReceive;
      miAudio.mediaType = MediaType_Audio;
      miAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      miAudio.audioCodec.channels = 1;
      miAudio.audioCodec.pacsize = 20;
      miAudio.audioCodec.plfreq = 8000;
      memcpy(miAudio.audioCodec.plname,"PCMU\0",5);
      miAudio.audioCodec.pltype = 0;
      miAudio.audioCodec.rate = 8000;
      evt.localMediaInfo.push_back(miAudio);
      evt.remoteMediaInfo.push_back(miAudio);
      CPCAPI2::WebCall::MediaInfo miVideo;
      miVideo.mediaDirection = MediaDirection_SendReceive;
      miVideo.mediaType = MediaType_Video;
      miVideo.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      miVideo.videoCodec.height = 480;
      miVideo.videoCodec.width = 640;
      miVideo.videoCodec.maxBitrate = 0;
      miVideo.videoCodec.maxFramerate = 15;
      miVideo.videoCodec.minBitrate = 0;
      memcpy(miVideo.videoCodec.plName,"VP8\0",4);
      miVideo.videoCodec.plType = 120;
      miVideo.videoCodec.startBitrate = 0;
      evt.localMediaInfo.push_back(miVideo);
      evt.remoteMediaInfo.push_back(miVideo);
      evt.conversationState = it->second->conversationState;
      evt.conversationType = it->second->conversationType;
      evt.remoteAddress = it->second->targetAddresses.front();
      evt.remoteDisplayName = it->second->targetAddresses.front();
      ReadCallbackBase* cb = makeFpCommand(
         WebCallHandler::onNewConversation,
         mAppHandler,
         h,
         evt);
      m_if->postCallback(cb);
   }
}

void WebCallManagerImpl::cprtc_fireNewIncomingConversation_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(external->Value());

   // params:
   //   - peerConn
   //   - address
   //   - displayName

   if(!args[0].IsEmpty() && !args[1].IsEmpty() && !args[2].IsEmpty())
   {
      v8::Handle<v8::Object> pcJsObj = v8::Handle<v8::Object>::Cast(args[0]);

      v8::String::Utf8Value addressJs(args[1]);
      std::string address(*addressJs);
      v8::String::Utf8Value displayNameJs(args[2]);
      std::string displayName(*displayNameJs);
      WebCallHandle h = wcm->fireNewIncomingConversation(pcJsObj, address, displayName);
      Handle<v8::Integer> handleJs = v8::Uint32::NewFromUnsigned(h);
      args.GetReturnValue().Set(handleJs);
   }
}

WebCallHandle WebCallManagerImpl::fireNewIncomingConversation(v8::Handle<v8::Object> pc, const std::string& address, const std::string& displayName)
{
   stdLog(LogLevel::LogLevel_Info, "WebCallManagerImpl::fireNewIncomingConversation");

   WebCallHandle h = WebCallHandleFactory::getNext();
   WebCallCreationInfo* ci = new WebCallCreationInfo();
   ci->context = this->mContextHandle;
   ci->conversationState = ConversationState_RemoteOriginated;
   ci->conversationType = ConversationType_Incoming;
   this->addCreationInfo(h, ci);

   Local<External> pcJsObjExt = Local<External>::Cast(pc->GetInternalField(0));
   CPRTCPeerConnectionState* pcState = static_cast<CPRTCPeerConnectionState*>(pcJsObjExt->Value());
   ci->avoaSession = pcState->avoaSession;

   CPCAPI2::WebCall::NewConversationEvent evt;
   CPCAPI2::WebCall::MediaInfo miAudio;
   miAudio.mediaDirection = MediaDirection_SendReceive;
   miAudio.mediaType = MediaType_Audio;
   miAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
   miAudio.audioCodec.channels = 1;
   miAudio.audioCodec.pacsize = 20;
   miAudio.audioCodec.plfreq = 8000;
   memcpy(miAudio.audioCodec.plname,"PCMU\0",5);
   miAudio.audioCodec.pltype = 0;
   miAudio.audioCodec.rate = 8000;
   evt.localMediaInfo.push_back(miAudio);
   evt.remoteMediaInfo.push_back(miAudio);
   CPCAPI2::WebCall::MediaInfo miVideo;
   miVideo.mediaDirection = MediaDirection_SendReceive;
   miVideo.mediaType = MediaType_Video;
   miVideo.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
   miVideo.videoCodec.height = 480;
   miVideo.videoCodec.width = 640;
   miVideo.videoCodec.maxBitrate = 0;
   miVideo.videoCodec.maxFramerate = 15;
   miVideo.videoCodec.minBitrate = 0;
   memcpy(miVideo.videoCodec.plName,"VP8\0",4);
   miVideo.videoCodec.plType = 120;
   miVideo.videoCodec.startBitrate = 0;
   evt.localMediaInfo.push_back(miVideo);
   evt.remoteMediaInfo.push_back(miVideo);
   evt.conversationState = ci->conversationState;
   evt.conversationType = ci->conversationType;
   evt.remoteAddress = StringConv::utf8ToWstring(address.c_str());
   evt.remoteDisplayName = StringConv::utf8ToWstring(displayName.c_str());

   ReadCallbackBase* cb = makeFpCommand(
      WebCallHandler::onNewConversation,
      mAppHandler,
      h,
      evt);
   m_if->postCallback(cb);
   return h;
}

void WebCallManagerImpl::cprtc_fireConversationEnded_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(external->Value());

   // params:
   //   - callId

   if(!args[0].IsEmpty() && args[0]->IsInt32())
   {
      WebCallHandle h = args[0]->Int32Value();
      wcm->fireConversationEnded(h);
      WebCallCreationInfo* ci = wcm->getCreationInfo(h);
      if (ci != NULL)
      {
         if (ci->avoaSession != NULL)
         {
            ci->avoaSession->close();
            delete ci->avoaSession;
         }
         delete ci;
         wcm->removeCreationInfo(h);
      }
   }
}

void WebCallManagerImpl::fireConversationEnded(WebCallHandle h)
{
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      it->second->conversationState = ConversationState_Ended;
      CPCAPI2::WebCall::ConversationEndedEvent evt;
      evt.conversationState = it->second->conversationState;
      evt.sipResponseCode = 0;
      evt.endReason = ConversationEndReason_UserTerminatedRemotely;
      ReadCallbackBase* cb = makeFpCommand(
         WebCallHandler::onConversationEnded,
         mAppHandler,
         h,
         evt);
      m_if->postCallback(cb);
   }
}

void WebCallManagerImpl::cprtc_fireConversationStateChanged_wrapper(const v8::FunctionCallbackInfo<v8::Value>& args)
{
   Local<v8::Object> self = args.Holder();
   Local<External> external = Local<External>::Cast(self->GetInternalField(0));
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(external->Value());

   // params:
   //   - callId

   if(!args[0].IsEmpty() && args[0]->IsInt32())
   {
      WebCallHandle h = args[0]->Int32Value();
      wcm->fireConversationStateChanged(h);
   }
}

void WebCallManagerImpl::fireConversationStateChanged(WebCallHandle h)
{
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      it->second->conversationState = ConversationState_Connected;

      CPCAPI2::WebCall::ConversationStateChangedEvent evt;
      evt.conversationState = it->second->conversationState;
      ReadCallbackBase* cb = makeFpCommand(
         WebCallHandler::onConversationStateChanged,
         mAppHandler,
         h,
         evt);
      m_if->postCallback(cb);
   }
}

void WebCallManagerImpl::fireConversationMediaChanged(WebCallHandle h)
{
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.find(h);
   if (it != mCallCreationInfo.end())
   {
      CPCAPI2::WebCall::ConversationMediaChangedEvent evt;
      CPCAPI2::WebCall::MediaInfo miAudio;
      miAudio.mediaDirection = MediaDirection_SendReceive;
      miAudio.mediaType = MediaType_Audio;
      miAudio.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      miAudio.audioCodec.channels = 1;
      miAudio.audioCodec.pacsize = 20;
      miAudio.audioCodec.plfreq = 8000;
      memcpy(miAudio.audioCodec.plname,"PCMU\0",5);
      miAudio.audioCodec.pltype = 0;
      miAudio.audioCodec.rate = 8000;
      evt.localMediaInfo.push_back(miAudio);
      evt.remoteMediaInfo.push_back(miAudio);
      CPCAPI2::WebCall::MediaInfo miVideo;
      miVideo.mediaDirection = MediaDirection_SendReceive;
      miVideo.mediaType = MediaType_Video;
      miVideo.mediaEncryptionOptions.mediaEncryptionMode = MediaEncryptionMode_SRTP_DTLS_Encrypted;
      miVideo.videoCodec.height = 480;
      miVideo.videoCodec.width = 640;
      miVideo.videoCodec.maxBitrate = 0;
      miVideo.videoCodec.maxFramerate = 15;
      miVideo.videoCodec.minBitrate = 0;
      memcpy(miVideo.videoCodec.plName,"VP8\0",4);
      miVideo.videoCodec.plType = 120;
      miVideo.videoCodec.startBitrate = 0;
      evt.localMediaInfo.push_back(miVideo);
      evt.remoteMediaInfo.push_back(miVideo);
      evt.localHold = false;
      evt.remoteHold = false;
      ReadCallbackBase* cb = makeFpCommand(
         WebCallHandler::onConversationMediaChanged,
         mAppHandler,
         h,
         evt);
      m_if->postCallback(cb);
   }
}

void WebCallManagerImpl::cprtcGetter(v8::Local<v8::String> prop, const v8::PropertyCallbackInfo<v8::Value>& propInfo)
{
   v8::HandleScope scope(propInfo.GetIsolate()); // !jjg! not sure if args.GetIsolate() is correct

   Handle<ObjectTemplate> templ = v8::ObjectTemplate::New(propInfo.GetIsolate());
   templ->SetInternalFieldCount(1);
   templ->Set(String::NewFromUtf8(propInfo.GetIsolate(), "fireNewConversation"), FunctionTemplate::New(propInfo.GetIsolate(), cprtc_fireNewConversation_wrapper));
   templ->Set(String::NewFromUtf8(propInfo.GetIsolate(), "fireConversationEnded"), FunctionTemplate::New(propInfo.GetIsolate(), cprtc_fireConversationEnded_wrapper));
   templ->Set(String::NewFromUtf8(propInfo.GetIsolate(), "fireConversationStateChanged"), FunctionTemplate::New(propInfo.GetIsolate(), cprtc_fireConversationStateChanged_wrapper));
   templ->Set(String::NewFromUtf8(propInfo.GetIsolate(), "fireNewIncomingConversation"), FunctionTemplate::New(propInfo.GetIsolate(), cprtc_fireNewIncomingConversation_wrapper));

   Local<External> dataExt = Local<External>::Cast(propInfo.Data());
   WebCallManagerImpl* wcm = static_cast<WebCallManagerImpl*>(dataExt->Value());

   Local<Object> consoleObj = templ->NewInstance();
   consoleObj->SetInternalField(0, External::New(propInfo.GetIsolate(), wcm));
   propInfo.GetReturnValue().Set(consoleObj);
}

int WebCallManagerImpl::initScript(const std::string& scriptUtf8)
{
   v8::V8::InitializeICU();

   mIsolate = Isolate::New();
   mIsolate->Enter();

   // Create a handle scope to hold the temporary references.
   HandleScope handle_scope(getIsolate());

   // Create a template for the global object where we set the
   // built-in global functions.
   Handle<ObjectTemplate> global = ObjectTemplate::New();

   // Expose the CPRTCPeerConnection Javascript class
   Handle<FunctionTemplate> obj = FunctionTemplate::New();
   obj->Set(String::NewFromUtf8(getIsolate(), "CPRTCPeerConnection"), FunctionTemplate::New(getIsolate(), CPRTCPeerConnectionCtor, v8::External::New(getIsolate(), this)));
   global->Set(String::NewFromUtf8(getIsolate(), "CPRTCPeerConnection"), FunctionTemplate::New(getIsolate(), CPRTCPeerConnectionCtor, v8::External::New(getIsolate(), this)));

   // Expose the CPRTCSessionDescription Javascript class
   Handle<FunctionTemplate> sessDescObj = FunctionTemplate::New();
   sessDescObj->Set(String::NewFromUtf8(getIsolate(), "CPRTCSessionDescription"), FunctionTemplate::New(getIsolate(), CPRTCSessionDescription::CPRTCSessionDescriptionCtor, v8::External::New(getIsolate(), this)));
   global->Set(String::NewFromUtf8(getIsolate(), "CPRTCSessionDescription"), FunctionTemplate::New(getIsolate(), CPRTCSessionDescription::CPRTCSessionDescriptionCtor, v8::External::New(getIsolate(), this)));

   // Expose the console class
   global->SetAccessor(String::NewFromUtf8(getIsolate(), "console"), &WebCallManagerImpl::consoleGetter);

   // Expose the window class
   global->SetAccessor(String::NewFromUtf8(getIsolate(), "window"), &WebCallManagerImpl::windowGetter, NULL, v8::External::New(getIsolate(), this));

   // Expose the cprtc class
   global->SetAccessor(String::NewFromUtf8(getIsolate(), "cprtc"), &WebCallManagerImpl::cprtcGetter, NULL, v8::External::New(getIsolate(), this));

   // Expose the WebSocket class
   Handle<FunctionTemplate> websocketObj = FunctionTemplate::New();
   websocketObj->Set(String::NewFromUtf8(getIsolate(), "WebSocket"), FunctionTemplate::New(getIsolate(), WebSocketJsBridge::WebSocketCtor, v8::External::New(getIsolate(), this)));
   global->Set(String::NewFromUtf8(getIsolate(), "WebSocket"), FunctionTemplate::New(getIsolate(), WebSocketJsBridge::WebSocketCtor, v8::External::New(getIsolate(), this)));

   // Each processor gets its own context so different processors don't
   // affect each other. Context::New returns a persistent handle which
   // is what we need for the reference to remain after we return from
   // this method. That persistent handle has to be disposed in the
   // destructor.
   v8::Handle<v8::Context> context = Context::New(getIsolate(), NULL, global);
   mContext.Reset(getIsolate(), context);

   // Enter the new context so all the following operations take place
   // within it.
   Context::Scope context_scope(context);

   Handle<String> v8script = String::NewFromUtf8(getIsolate(), scriptUtf8.data(), String::kNormalString, scriptUtf8.size());

   // Compile and run the script
   if (executeScript(v8script) == kError)
      return kError;

   // The script compiled and ran correctly.  Now we fetch out the
   // MakeCall and Hangup functions from the global object.
   Handle<Value> makeCall_val = context->Global()->Get(String::NewFromUtf8(getIsolate(), "MakeCall"));
   Handle<Value> hangup_val = context->Global()->Get(String::NewFromUtf8(getIsolate(), "Hangup"));
   Handle<Value> answer_val = context->Global()->Get(String::NewFromUtf8(getIsolate(), "Answer"));

   // If there is no Process function, or if it is not a function,
   // bail out
   if (!makeCall_val->IsFunction() || !hangup_val->IsFunction() || !answer_val->IsFunction())
   {
      return kError;
   }

   // It is a function; cast it to a Function
   Handle<Function> makeCall_fun = Handle<Function>::Cast(makeCall_val);
   Handle<Function> hangup_fun = Handle<Function>::Cast(hangup_val);
   Handle<Function> answer_fun = Handle<Function>::Cast(answer_val);

   // Store the function in a Persistent handle, since we also want
   // that to remain after this call returns
   mMakeCall.Reset(getIsolate(), makeCall_fun);
   mHangup.Reset(getIsolate(), hangup_fun);
   mAnswer.Reset(getIsolate(), answer_fun);

   // All done; all went well
   return kSuccess;
}

int WebCallManagerImpl::executeScript(Handle<String> script) {
  HandleScope handle_scope(getIsolate());

  // We're just about to compile the script; set up an error handler to
  // catch any exceptions the script might throw.
  TryCatch try_catch;

  // Compile the script and check for errors.
  Handle<Script> compiled_script = Script::Compile(script);
  if (compiled_script.IsEmpty()) {
    //String::Utf8Value error(try_catch.Exception());
    //Log(*error);
    // The script failed to compile; bail out.
    return kError;
  }

  // Run the script!
  Handle<Value> result = compiled_script->Run();
  if (result.IsEmpty()) {
    // The TryCatch above is still in effect and will have caught the error.
    //String::Utf8Value error(try_catch.Exception());
    //Log(*error);
    // Running the script failed; bail out.
    return kError;
  }
  return kSuccess;
}

int WebCallManagerImpl::javascriptMakeCall(WebCallHandle h)
{
   if (mMakeCall.IsEmpty())
   {
      return kError;
   }

   WebCallCreationInfo* ci = getCreationInfo(h);

   // Create a handle scope to hold the temporary references.
   HandleScope handle_scope(getIsolate());

   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Wrap the C++ request object in a JavaScript wrapper
   std::string targetUtf8 = StringConv::wstringToUtf8(ci->targetAddresses.front());
   Handle<String> targetAddressJs = String::NewFromUtf8(getIsolate(), targetUtf8.data(), String::kNormalString, targetUtf8.size());
   Handle<v8::Integer> handleJs = v8::Uint32::NewFromUnsigned(h);

   // Set up an exception handler before calling the Process function
   TryCatch try_catch;

   // Invoke the process function, giving the global object as 'this'
   // and one argument, the request.
   const int argc = 2;
   Handle<Value> argv[argc] = { handleJs, targetAddressJs };
   v8::Local<v8::Function> process =
      v8::Local<v8::Function>::New(getIsolate(), mMakeCall);
   Handle<Value> result = process->Call(context->Global(), argc, argv);
   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
      return kError;
   }

   return kSuccess;
}

int WebCallManagerImpl::javascriptHangup(WebCallHandle h)
{
   WebCallCreationInfo* ci = getCreationInfo(h);

   // Create a handle scope to hold the temporary references.
   HandleScope handle_scope(getIsolate());

   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Wrap the C++ request object in a JavaScript wrapper
   Handle<v8::Integer> handleJs = v8::Uint32::NewFromUnsigned(h);

   // Set up an exception handler before calling the Process function
   TryCatch try_catch;

   // Invoke the process function, giving the global object as 'this'
   // and one argument, the request.
   const int argc = 1;
   Handle<Value> argv[argc] = { handleJs };
   v8::Local<v8::Function> process =
      v8::Local<v8::Function>::New(getIsolate(), mHangup);
   Handle<Value> result = process->Call(context->Global(), argc, argv);
   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
      return kError;
   }

   return kSuccess;
}

int WebCallManagerImpl::javascriptAnswer(WebCallHandle h)
{
   WebCallCreationInfo* ci = getCreationInfo(h);

   // Create a handle scope to hold the temporary references.
   HandleScope handle_scope(getIsolate());

   v8::Local<v8::Context> context =
      v8::Local<v8::Context>::New(getIsolate(), mContext);

   // Enter this processor's context so all the remaining operations
   // take place there
   Context::Scope context_scope(context);

   // Wrap the C++ request object in a JavaScript wrapper
   Handle<v8::Integer> handleJs = v8::Uint32::NewFromUnsigned(h);

   // Set up an exception handler before calling the Process function
   TryCatch try_catch;

   // Invoke the process function, giving the global object as 'this'
   // and one argument, the request.
   const int argc = 1;
   Handle<Value> argv[argc] = { handleJs };
   v8::Local<v8::Function> process =
      v8::Local<v8::Function>::New(getIsolate(), mAnswer);
   Handle<Value> result = process->Call(context->Global(), argc, argv);
   if (result.IsEmpty()) {
      String::Utf8Value error(try_catch.Exception());
      std::cout << "exception: " << *error << std::endl;
      return kError;
   }

   return kSuccess;
}

WebCallHandle WebCallManagerImpl::getHandleForAVOA(recon::AVOfferAnswerSession* avoa)
{
   WebCallHandle h = 0xFFFFFFFF;
   WebCallCreationInfoMap::iterator it = mCallCreationInfo.begin();
   for (; it != mCallCreationInfo.end(); ++it)
   {
      if (it->second->avoaSession == avoa)
      {
         return it->first;
      }
   }
   return h;
}

}
}

#endif // 
