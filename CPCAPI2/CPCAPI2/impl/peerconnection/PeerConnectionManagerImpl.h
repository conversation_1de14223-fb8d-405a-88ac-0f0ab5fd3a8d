#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_MANAGER_IMPL_H)
#define CPCAPI2_PEER_CONNECTION_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandlerInternal.h"
#include "video_ext/VideoExt.h"
#include "PeerConnectionSyncHandler.h"
#include "util/DumFpCommand.h"

#include <AVOfferAnswerSession.hxx>
#include <MediaStackImpl.hxx>
#include <RtpStreamImpl.hxx>

#include <rutil/DeadlineTimer.hxx>

namespace flowmanager
{
class FlowManager;
}

namespace CPCAPI2
{
namespace PeerConnection
{
class PeerConnectionManagerInterface;

class PeerConnectionManagerImpl : public recon::AVOfferAnswerSession::SdpReady,
                                  public recon::AVOfferAnswerSession::MediaStreamReady,
                                  public recon::AVOfferAnswerSession::MediaStreamUsable,
                                  public CPCAPI2::Media::VideoWebsocketServerHand<PERSON>,
                                  private resip::DeadlineTimerHandler
{
public:
   PeerConnectionManagerImpl(PeerConnectionManagerInterface* pcmif, CPCAPI2::Media::VideoExt* videoExtIf, resip::MultiReactor& reactor, webrtc_recon::MediaStackImpl* mediaStack, flowmanager::FlowManager* flowManager, PeerConnectionHandle handle);
   virtual ~PeerConnectionManagerImpl();

   recon::AVOfferAnswerSession* getOA() const;
   void configureMediaStream(const CPCAPI2::PeerConnection::MediaInfo& mediaDesc);
   void startWebVideoServerForMediaStream(MediaStreamHandle mediaStream);
   void playSound(const cpc::string& mediaUri, bool repeat);
   void startIncomingMediaInactivityMonitor();
   void stopIncomingMediaInactivityMonitor();
   void startLoggingMediaStatistics();
   void stopLoggingMediaStatistics();
   void setMediaInactivityTimeoutMsecs(int msecs);
   void setLoggingMediaStatisticsFrequencyMsecs(int msecs);
   void queryMediaStatistics();
   void logMediaStatistics();
   void setAudioDeviceCloseDelay(int msec);
   void pauseAudioPlayout();
   void resumeAudioPlayout();

   // SdpReady
   virtual void onSdpReady() OVERRIDE;

   // MediaStreamReady
   virtual void onMediaStreamReady(bool localSdpSet, bool remoteSdpSet) OVERRIDE;

   // MediaStreamUsable
   virtual void onMediaStreamUsable(const resip::Data& msid) OVERRIDE;

   // VideoWebsocketServerHandler
   virtual void onVideoWebsocketServerStarted(int mediaStreamId, const CPCAPI2::Media::VideoWebsocketServerStartedEvent& args) OVERRIDE;

private:
   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   PeerConnectionConversationCallQuality calculateCallQuality(
      unsigned short localFractionLost,
      unsigned short jitterPacketLossRate,
      unsigned int totalReceivedPackets,
      unsigned int jitterMsecs,
      int64_t latency,
      int64_t timeSinceLastRtpReceivedMs);

   std::shared_ptr<recon::AVOfferAnswerSession> mOA;
   PeerConnectionManagerInterface* mPCM_if;
   CPCAPI2::Media::VideoExt* mVideoExt;
   PeerConnectionHandle mHandle;
   int mMediaInactivityTimeoutMsecs;
   int mLoggingMediaStatisticsFrequencyMsecs;
   int mAudioDeviceCloseDelayMsecs;

   struct MediaStreamState
   {
      CPCAPI2::PeerConnection::MediaInfo mediaDescriptor;
      std::weak_ptr<webrtc_recon::RtpStreamImpl> rtpStream;
      int lastNumPacketsRecvd = 0;
      std::chrono::time_point<std::chrono::system_clock> updateTimestamp;
   };
   typedef std::map<MediaStreamHandle, MediaStreamState> MediaStreamStateMap;
   MediaStreamStateMap mMediaStreams;

   resip::MultiReactor& mReactor;
   std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mInactivityTimer;
   std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mLoggingTimer;
};

}
}

#endif // CPCAPI2_PEER_CONNECTION_MANAGER_IMPL_H
