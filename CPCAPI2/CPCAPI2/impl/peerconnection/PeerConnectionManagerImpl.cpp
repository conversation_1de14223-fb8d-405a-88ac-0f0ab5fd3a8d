#include "brand_branded.h"

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
#include "PeerConnectionManagerImpl.h"
#include "PeerConnectionManagerInterface.h"
#include "PeerConnectionHandlerInternal.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "video_ext/VideoExt.h"
#include "cpcapi2utils.h"
#include "../util/cpc_logger.h"
#include "../util/TimeUtils.h"
#include "json/JsonFunctionSerialize.h"
#include "websocket/json/JsonDataWebSocketCommand.h"

#include <media/MediaStack.hxx>

#include "../media/AudioInterface.h"
#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <chrono>
#include <ctime>
#include <cmath>
#include <time.h>
#include <iomanip>
#include <voe_codec.h>
#include <vie_codec.h>
#include <voe_rtp_rtcp.h>
#include <voe_neteq_stats.h>
#include <vie_rtp_rtcp.h>
#include <voe_base.h>
#include <voe_file.h>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <webrtc/system_wrappers/interface/clock.h>
#include <../webrtc_recon/codecs/CpsiCodec.hxx>
#include <webrtc/common_types.h>
#include <codecs/OpusCodecImpl.hxx>
#include <string>

#ifdef __ANDROID__
#include "phone/PhoneInterface.h"
#endif

using namespace resip;
using namespace recon;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PEERCONNECTION

#define PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_TIMER_ID 0
#define PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_TIMER_ID 1
#define PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_INTERVAL 1000
#define PEERCONNECTIONMANAGERIMPL_INACTIVITY_TIMEOUT_MSECS 2000
#define PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_INTERVAL_MSECS 2000
#define PEERCONNECTIONMANAGERIMPL_QOS_INACTIVITY_MSECS 1000

namespace CPCAPI2
{

namespace PeerConnection
{

PeerConnectionManagerImpl::PeerConnectionManagerImpl(PeerConnectionManagerInterface* pcmif, CPCAPI2::Media::VideoExt* videoExtIf, resip::MultiReactor& reactor, webrtc_recon::MediaStackImpl* mediaStack, flowmanager::FlowManager* flowManager, PeerConnectionHandle handle) :
mOA(new AVOfferAnswerSession(mediaStack, flowManager, reactor)),
mPCM_if(pcmif),
mVideoExt(videoExtIf),
mHandle(handle),
mMediaInactivityTimeoutMsecs(PEERCONNECTIONMANAGERIMPL_INACTIVITY_TIMEOUT_MSECS),
mLoggingMediaStatisticsFrequencyMsecs(PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_INTERVAL_MSECS),
mAudioDeviceCloseDelayMsecs(0),
mReactor(reactor)
{
}

PeerConnectionManagerImpl::~PeerConnectionManagerImpl()
{
   if (mInactivityTimer.get() != NULL)
   {
      assert(mReactor.isCurrentThread());
      mInactivityTimer->cancel();
   }
   if (mLoggingTimer.get() != NULL)
   {
      assert(mReactor.isCurrentThread());
      mLoggingTimer->cancel();
   }
}

recon::AVOfferAnswerSession* PeerConnectionManagerImpl::getOA() const
{
   return mOA.get();
}

void PeerConnectionManagerImpl::configureMediaStream(const CPCAPI2::PeerConnection::MediaInfo& mediaDesc)
{
   mMediaStreams[mediaDesc.mediaStream].mediaDescriptor = mediaDesc;
}

void PeerConnectionManagerImpl::startWebVideoServerForMediaStream(MediaStreamHandle mediaStream)
{
   MediaStreamStateMap::iterator it = mMediaStreams.find(mediaStream);
   if (it != mMediaStreams.end())
   {
      mVideoExt->startWebsocketServer(this);
      mVideoExt->startWebsocketServerForReceiveStream(it->second.mediaDescriptor.mediaStreamId);
   }
}

void PeerConnectionManagerImpl::setMediaInactivityTimeoutMsecs(int msecs)
{
   mMediaInactivityTimeoutMsecs = ((msecs >= PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_INTERVAL) ? msecs : PEERCONNECTIONMANAGERIMPL_INACTIVITY_TIMEOUT_MSECS);
}

void PeerConnectionManagerImpl::setLoggingMediaStatisticsFrequencyMsecs(int msecs)
{
   mLoggingMediaStatisticsFrequencyMsecs = ((msecs >= PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_INTERVAL_MSECS) ? msecs : PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_INTERVAL_MSECS);
}

void PeerConnectionManagerImpl::playSound(const cpc::string& resourceUri, bool repeat)
{
   MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin();
   for (; itMediaState != mMediaStreams.end(); ++itMediaState)
   {
      if (itMediaState->second.mediaDescriptor.mediaType == PeerConnection::MediaType_Audio)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
         {
            if (resourceUri.find("tone:") == 0)
            {
               InfoLog(<< "play test tone");
               rtpStreamImpl->playTone(1000, 1000);
            }
            else if (resourceUri.find("silence:") == 0)
            {
               InfoLog(<< "play silence");
               rtpStreamImpl->playTone(0, 0);
            }
            else if (resourceUri.find("file:") == 0)
            {
               cpc::string filename(resourceUri.substr(5, resourceUri.size()));
               InfoLog(<< "playFile path: " << filename.c_str());

               //if (!mPhone->hasFilePermission(Permission_ReadFiles, filename))
               //{
               //   InfoLog(<< "playFile - Cannot read file due to missing permission");
               //   mPhone->requestPermission(0, Permission_ReadFiles);
               //   return kError;
               //}

               resip::Data file_name(filename);
               rtpStreamImpl->playFile(file_name, repeat);
            }
            else if (resourceUri.find("seq:") == 0)
            {
               cpc::string seq(resourceUri.substr(4, resourceUri.size()));
               InfoLog(<< "seq is: " << seq.c_str());
               resip::Data sequence(seq);
               rtpStreamImpl->playSeq(sequence, repeat);
            }
         }
         break;
      }
   }
}

void PeerConnectionManagerImpl::pauseAudioPlayout()
{
   MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin();
   for (; itMediaState != mMediaStreams.end(); ++itMediaState)
   {
      if (itMediaState->second.mediaDescriptor.mediaType == PeerConnection::MediaType_Audio)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
         {
            rtpStreamImpl->pauseRtpReceive();
         }
      }
   }
}

void PeerConnectionManagerImpl::resumeAudioPlayout()
{
   MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin();
   for (; itMediaState != mMediaStreams.end(); ++itMediaState)
   {
      if (itMediaState->second.mediaDescriptor.mediaType == PeerConnection::MediaType_Audio)
      {
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
         {
            rtpStreamImpl->resumeRtpReceive();
         }
      }
   }
}

void PeerConnectionManagerImpl::startIncomingMediaInactivityMonitor()
{
   DebugLog(<< "PeerConnectionManagerImpl::startIncomingMediaInactivityMonitor(): peer connection: " << mHandle << " enabling media inactivity timeout: " << mMediaInactivityTimeoutMsecs << " msecs");
   mInactivityTimer.reset(new resip::DeadlineTimer<resip::MultiReactor>(mReactor));
   mInactivityTimer->expires_from_now(PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_INTERVAL);
   mInactivityTimer->async_wait(this, PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_TIMER_ID, NULL);
}

void PeerConnectionManagerImpl::stopIncomingMediaInactivityMonitor()
{
   DebugLog(<< "PeerConnectionManagerImpl::stopIncomingMediaInactivityMonitor(): peer connection: " << mHandle << " disabling media inactivity timeout: " << mMediaInactivityTimeoutMsecs << " msecs");
   if (mInactivityTimer.get() != NULL)
   {
      mInactivityTimer->cancel();
   }
}

void PeerConnectionManagerImpl::startLoggingMediaStatistics()
{
   DebugLog(<< "PeerConnectionManagerImpl::startLoggingMediaStatistics(): peer connection: " << mHandle << " enabling logging media statistics, frequency: " << mLoggingMediaStatisticsFrequencyMsecs << " msecs");
   mLoggingTimer.reset(new resip::DeadlineTimer<resip::MultiReactor>(mReactor));
   mLoggingTimer->expires_from_now(mLoggingMediaStatisticsFrequencyMsecs);
   mLoggingTimer->async_wait(this, PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_TIMER_ID, NULL);
}

void PeerConnectionManagerImpl::stopLoggingMediaStatistics()
{
   DebugLog(<< "PeerConnectionManagerImpl::stopLoggingMediaStatistics(): peer connection: " << mHandle << " disabling logging media statistics, frequency: " << mLoggingMediaStatisticsFrequencyMsecs << " msecs");
   if (mLoggingTimer.get() != NULL)
   {
      mLoggingTimer->cancel();
   }
}

void PeerConnectionManagerImpl::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_TIMER_ID)
   {
      mInactivityTimer->async_wait(this, PEERCONNECTIONMANAGERIMPL_INACTIVITY_CHECK_TIMER_ID, NULL);
      size_t streamsWithInactivity = 0;

      MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin();
      for (; itMediaState != mMediaStreams.end(); ++itMediaState)
      {
         // StackLog(<< "PeerConnectionManagerImpl::onTimer(): peer connection: " << mHandle << " media-stream: " << itMediaState->first << " id: " << itMediaState->second.mediaDescriptor.mediaStreamId << " type: " << itMediaState->second.mediaDescriptor.mediaType << " direction: " << itMediaState->second.mediaDescriptor.mediaDirection);
         if (itMediaState->second.mediaDescriptor.mediaDirection == CPCAPI2::PeerConnection::MediaDirection_SendRecv ||
            itMediaState->second.mediaDescriptor.mediaDirection == CPCAPI2::PeerConnection::MediaDirection_RecvOnly)
         {
            if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
            {
               int rtpPackets = 0;
               int rtcpPackets = 0;
               if (rtpStreamImpl->getNumPacketsReceivedFromReflowTransport(rtpPackets, rtcpPackets) == 0)
               {
                  StackLog(<< "PeerConnectionManagerImpl::onTimer(): peer connection: " << mHandle << " rtp channel: " << rtpStreamImpl->channel() << " media-stream: " << itMediaState->first << " stream-id: " << itMediaState->second.mediaDescriptor.mediaStreamId << " RTP packets received: " << rtpPackets << ", RTCP packets received: " << rtcpPackets);
                  int prevNumPacketsRecvd = itMediaState->second.lastNumPacketsRecvd;
                  itMediaState->second.lastNumPacketsRecvd = rtpPackets;
                  std::chrono::time_point<std::chrono::system_clock> currentTimestamp = std::chrono::system_clock::now();
                  if (prevNumPacketsRecvd == itMediaState->second.lastNumPacketsRecvd)
                  {
                     std::chrono::milliseconds delta = std::chrono::duration_cast<std::chrono::milliseconds>(currentTimestamp - itMediaState->second.updateTimestamp);
                     if (delta.count() >= mMediaInactivityTimeoutMsecs)
                     {
                        streamsWithInactivity++;
                     }
                  }
                  else
                  {
                     itMediaState->second.updateTimestamp = currentTimestamp;
                  }
               }
            }
         }
      }

      if (streamsWithInactivity > 0 && streamsWithInactivity == mMediaStreams.size())
      {
         InfoLog(<< "PeerConnectionManagerImpl::onTimer(): peer connection: " << mHandle << " media-stream count: " << mMediaStreams.size() << " streams with inactivity: " << streamsWithInactivity);
         MediaInactivityEvent args;
         mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onMediaInactivity), mHandle, args);
      }
   }
   else if (timerId == PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_TIMER_ID)
   {
      mLoggingTimer->async_wait(this, PEERCONNECTIONMANAGERIMPL_MEDIA_STATISTICS_TIMER_ID, NULL);
      logMediaStatistics();
   }
}

void PeerConnectionManagerImpl::queryMediaStatistics()
{
#ifdef CPCAPI2_AUTO_TEST
   PeerConnectionMediaStatisticsEvent args;
   for (MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin(); itMediaState != mMediaStreams.end(); ++itMediaState)
   {
      // StackLog(<< "PeerConnectionManagerImpl::queryMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection);
      if (itMediaState->second.mediaDescriptor.mediaDirection == CPCAPI2::PeerConnection::MediaDirection_SendRecv ||
         itMediaState->second.mediaDescriptor.mediaDirection == CPCAPI2::PeerConnection::MediaDirection_RecvOnly)
      {
         // StackLog(<< "PeerConnectionManagerImpl::queryMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " media-direction is send-recv or recv-only");
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
         {
            int rtpReceivePackets = 0;
            int rtcpReceivePackets = 0;
            if (rtpStreamImpl->getNumPacketsReceivedFromReflowTransport(rtpReceivePackets, rtcpReceivePackets) == 0)
            {
               PeerConnectionMediaStreamStatistics stats;
               stats.mediaStream = itMediaState->first;
               stats.rtpPacketCount = rtpReceivePackets;
               stats.rtcpPacketCount = rtcpReceivePackets;
               stats.videoReceiveFrameWidth = rtpStreamImpl->getVideoWidth();
               stats.videoReceiveFrameHeight = rtpStreamImpl->getVideoHeight();
               stats.videoReceiveFps = rtpStreamImpl->getVideoFramerate();
               StackLog(<< "PeerConnectionManagerImpl::queryMediaStatistics(): " << this << " pc: " << mHandle << " rtp channel: " << rtpStreamImpl->channel() << " media-stream: " << itMediaState->first << " stream-id: " << itMediaState->second.mediaDescriptor.mediaStreamId << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection << " RTP packets received: " << rtpReceivePackets << ", RTCP packets received: " << rtcpReceivePackets << " video frame received: " << stats.videoReceiveFrameWidth << "x" << stats.videoReceiveFrameHeight);
               args.mediaStreamStats.push_back(stats);
            }
         }
      }
   }

   DebugLog(<< "PeerConnectionManagerImpl::queryMediaStatistics(): " << this << " pc: " << mHandle << " media-streams: " << mMediaStreams.size() << " media stat count: " << args.mediaStreamStats.size());

   mPCM_if->firePcInternalEvent(cpcFunc(PeerConnectionHandlerInternal::onPeerConnectionMediaStatistics), mHandle, args);
#endif
}

resip::Data callQualityString(PeerConnectionConversationCallQuality cq)
{
   switch (cq)
   {
   case PeerConnectionConversationCallQuality_Fair:
      return "Fair";
   case PeerConnectionConversationCallQuality_Good:
      return "Good";
   case PeerConnectionConversationCallQuality_Poor:
      return "Poor";
   case PeerConnectionConversationCallQuality_Unknown:
      return "Unknown";
   default: break;
   }
   return "Invalid Value";
}

resip::Data mediaDirectionString(PeerConnection::MediaDirection md)
{
   switch (md)
   {
   case PeerConnection::MediaDirection_None:
      return "None";
   case PeerConnection::MediaDirection_SendRecv:
      return "SendRecv";
   case PeerConnection::MediaDirection_SendOnly:
      return "SendOnly";
   case PeerConnection::MediaDirection_RecvOnly:
      return "RecvOnly";
   case PeerConnection::MediaDirection_Inactive:
      return "Inactive";
   default: break;
   }
   return "Invalid Value";
}

resip::Data mediaTypeString(PeerConnection::MediaType mt)
{
   switch (mt)
   {
   case PeerConnection::MediaType_Audio:
      return "Audio";
   case PeerConnection::MediaType_Video:
      return "Video";
   default: break;
   }
   return "Invalid Value";
}

std::string getTimeString(int64_t ntpTime)
{
   std::chrono::time_point<std::chrono::system_clock> ntpTimestamp((std::chrono::milliseconds(ntpTime)));
   std::time_t timeT = std::chrono::system_clock::to_time_t(ntpTimestamp);
   struct std::tm* timeTm = gmtime(&timeT);
   char buf[40];
   strftime(buf, 40, "%Y-%m-%d %X", timeTm);
   return buf;
}

void PeerConnectionManagerImpl::logMediaStatistics()
{
   for (MediaStreamStateMap::iterator itMediaState = mMediaStreams.begin(); itMediaState != mMediaStreams.end(); ++itMediaState)
   {
      {
         // StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection);
         if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
         {
            int rtpPackets = 0;
            int rtcpPackets = 0;
            if (rtpStreamImpl->getNumPacketsReceivedFromReflowTransport(rtpPackets, rtcpPackets) == 0)
            {
               InfoLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): " << this << " pc: " << mHandle << " rtp channel: " << rtpStreamImpl->channel()
                  << " media-stream: " << itMediaState->first << " stream-id: " << itMediaState->second.mediaDescriptor.mediaStreamId
                  << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection
                  << " RTP packets received: " << rtpPackets << ", RTCP packets received: " << rtcpPackets);
               std::shared_ptr<webrtc_recon::MediaStackImpl> ms = std::dynamic_pointer_cast<webrtc_recon::MediaStackImpl>(rtpStreamImpl->getMediaStack());
               if (ms)
               {
                  if (rtpStreamImpl->getCallStartTime() == 0)
                  {
                     // Ignore media statistics when the rtp stream stats have not been initialized
                     continue;
                  }
                  if (rtpStreamImpl->mediaType() == recon::MediaStack::MediaType_Audio)
                  {
                     std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(ms->codecFactory());
                     webrtc::CallStatistics callStatistics;
                     PeerConnectionAudioStatistics audioStatistics;
                     webrtc::CodecInst audioCodec;
                     PeerConnectionAudioJitterBufferStatistics audioJitterStats = {};
                     int rtpRxReflowPackets = 0;
                     int rtcpRxReflowPackets = 0;
                     /*
                     int encoderPacketSize = 0;
                     int encoderSamplingRate = 0;
                     int encoderBitRate = 0;
                     int decoderPacketSize = 0;
                     int decoderSamplingRate = 0;
                     int decoderBitRate = 0;
                     */
                     if (rtpStreamImpl->channelSet() && (ms->audioCodec()->GetSendCodec(rtpStreamImpl->channel(), audioCodec) == 0))
                     {
                        // StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection);
                        if (rtpStreamImpl->getNumPacketsReceivedFromReflowTransport(rtpRxReflowPackets, rtcpRxReflowPackets) != 0)
                        {
                           StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " error extracting reflow packets");
                        }

                        callStatistics.fractionLost = 0;
                        callStatistics.cumulativeLost = 0;
                        callStatistics.extendedMax = 0;
                        callStatistics.jitterSamples = 0;
                        callStatistics.rttMs = 0;
                        callStatistics.bytesSent = 0;
                        callStatistics.packetsSent = 0;
                        callStatistics.bytesReceived = 0;
                        callStatistics.packetsReceived = 0;
                        callStatistics.capture_start_ntp_time_ms_ = 0;
                        if (ms->voe_rtp_rtcp())
                        {
                           ms->voe_rtp_rtcp()->GetRTCPStatistics(rtpStreamImpl->channel(), callStatistics);
                        }

                        audioStatistics.callStartTimeNTP = rtpStreamImpl->getCallStartTime();
                        audioStatistics.localEndpoint.ipAddress = rtpStreamImpl->getLocalTuple().presentationFormat().c_str();
                        audioStatistics.localEndpoint.port = rtpStreamImpl->getLocalTuple().getPort();
                        audioStatistics.remoteEndpoint.ipAddress = rtpStreamImpl->getRemoteTuple().presentationFormat().c_str();
                        audioStatistics.remoteEndpoint.port = rtpStreamImpl->getRemoteTuple().getPort();
                        audioStatistics.encoder.pltype = audioCodec.pltype;
                        strncpy(audioStatistics.encoder.plname, audioCodec.plname, 32);
                        audioStatistics.encoder.plfreq = audioCodec.plfreq;
                        audioStatistics.encoder.pacsize = audioCodec.pacsize;
                        audioStatistics.encoder.channels = audioCodec.channels;
                        audioStatistics.encoder.rate = audioCodec.rate;

                        if (codecFactory)
                        {
                           if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getAudioCodec(audioCodec.plname, audioCodec.plfreq))
                           {
                              audioStatistics.encoder.displayName = cpsiCodec->display_name.c_str();
                           }

                           /*
                           std::shared_ptr<webrtc_recon::OpusCodecImpl> opusCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", audioCodec.plfreq));
                           if (opusCodec)
                           {
                              encoderBitRate = opusCodec->settings().webrtcCodecInfo.audio.rate;
                              encoderPacketSize = opusCodec->settings().webrtcCodecInfo.audio.pacsize;
                              encoderSamplingRate = opusCodec->settings().webrtcCodecInfo.audio.plfreq;
                              // StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " encoder: bit-rate: " << encoderBitRate << " packet-size: " << encoderPacketSize << " sampling-rate: " << encoderSamplingRate);
                           }
                           */
                        }

                        webrtc::CodecInst remoteAudioCodec;
                        if (rtpStreamImpl->channelSet() && ms->audioCodec()->GetRecCodec(rtpStreamImpl->channel(), remoteAudioCodec) == 0)
                        {
                           audioStatistics.decoder.pltype = remoteAudioCodec.pltype;
                           strncpy(audioStatistics.decoder.plname, remoteAudioCodec.plname, 32);
                           audioStatistics.decoder.plfreq = remoteAudioCodec.plfreq;
                           audioStatistics.decoder.pacsize = remoteAudioCodec.pacsize;
                           audioStatistics.decoder.channels = remoteAudioCodec.channels;
                           audioStatistics.decoder.rate = remoteAudioCodec.rate;

                           if (codecFactory)
                           {
                              if (std::shared_ptr<webrtc_recon::CpsiCodec> cpsiCodec = codecFactory->getAudioCodec(remoteAudioCodec.plname, remoteAudioCodec.plfreq))
                              {
                                 audioStatistics.decoder.displayName = cpsiCodec->display_name.c_str();
                              }

                              /*
                              std::shared_ptr<webrtc_recon::OpusCodecImpl> opusCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", remoteAudioCodec.plfreq));
                              if (opusCodec)
                              {
                                 decoderBitRate = opusCodec->settings().webrtcCodecInfo.audio.rate;
                                 decoderPacketSize = opusCodec->settings().webrtcCodecInfo.audio.pacsize;
                                 decoderSamplingRate = opusCodec->settings().webrtcCodecInfo.audio.plfreq;
                                 // StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " decoder: bit-rate: " << decoderBitRate << " packet-size: " << decoderPacketSize << " sampling-rate: " << decoderSamplingRate);
                              }
                              */
                           }
                        }

                        audioStatistics.streamStatistics.fractionLost = callStatistics.fractionLost;
                        audioStatistics.streamStatistics.cumulativeLost = callStatistics.cumulativeLost;
                        audioStatistics.streamStatistics.extendedMax = callStatistics.extendedMax;
                        audioStatistics.streamStatistics.jitterSamples = callStatistics.jitterSamples;
                        audioStatistics.streamStatistics.rttMs = callStatistics.rttMs;
                        audioStatistics.streamDataCounters.bytesSent = callStatistics.bytesSent;
                        audioStatistics.streamDataCounters.packetsSent = callStatistics.packetsSent;
                        audioStatistics.streamDataCounters.bytesReceived = callStatistics.bytesReceived;
                        audioStatistics.streamDataCounters.packetsReceived = callStatistics.packetsReceived;
                        if (ms->voe_rtp_rtcp())
                        {
                           ms->voe_rtp_rtcp()->GetRTPStatistics(rtpStreamImpl->channel(), audioStatistics.averageJitterMs, audioStatistics.maxJitterMs, audioStatistics.discardedPackets);
                           // StackLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " mediaStreamId: " << itMediaState->first << " media-direction: " << itMediaState->second.mediaDescriptor.mediaDirection << " average-jitter-ms: " << audioStatistics.averageJitterMs << " max-jitter-ms: " << audioStatistics.maxJitterMs << " discarded-packets: " << audioStatistics.discardedPackets);
                        }

                        webrtc::RTCPVoIPMetric localVoipMetric;
                        if (rtpStreamImpl->getLocalRTCPVoIPMetric(&localVoipMetric) == 0)
                        {
                           audioStatistics.xRVoipMetrics.burstDensity = localVoipMetric.burstDensity;
                           audioStatistics.xRVoipMetrics.burstDuration = localVoipMetric.burstDuration;
                           audioStatistics.xRVoipMetrics.discardRate = localVoipMetric.discardRate;
                           audioStatistics.xRVoipMetrics.endSystemDelay = localVoipMetric.endSystemDelay;
                           audioStatistics.xRVoipMetrics.extRfactor = localVoipMetric.extRfactor;
                           audioStatistics.xRVoipMetrics.gapDensity = localVoipMetric.gapDensity;
                           audioStatistics.xRVoipMetrics.gapDuration = localVoipMetric.gapDuration;
                           audioStatistics.xRVoipMetrics.Gmin = localVoipMetric.Gmin;
                           audioStatistics.xRVoipMetrics.JBabsMax = localVoipMetric.JBabsMax;
                           audioStatistics.xRVoipMetrics.JBmax = localVoipMetric.JBmax;
                           audioStatistics.xRVoipMetrics.JBnominal = localVoipMetric.JBnominal;
                           audioStatistics.xRVoipMetrics.lossRate = localVoipMetric.lossRate;
                           audioStatistics.xRVoipMetrics.MOSCQ = localVoipMetric.MOSCQ;
                           audioStatistics.xRVoipMetrics.MOSLQ = localVoipMetric.MOSLQ;
                           audioStatistics.xRVoipMetrics.noiseLevel = localVoipMetric.noiseLevel;
                           audioStatistics.xRVoipMetrics.RERL = localVoipMetric.RERL;
                           audioStatistics.xRVoipMetrics.Rfactor = localVoipMetric.Rfactor;
                           audioStatistics.xRVoipMetrics.roundTripDelay = localVoipMetric.roundTripDelay;
                           audioStatistics.xRVoipMetrics.RXconfig = localVoipMetric.RXconfig;
                           audioStatistics.xRVoipMetrics.signalLevel = localVoipMetric.signalLevel;
                        }

                        webrtc::RTCPXRStatsSummary localStatsSummary;
                        if (rtpStreamImpl->getLocalRTCPXRStatsSummary(&localStatsSummary) == 0)
                        {
                           audioStatistics.xRStatisticsSummary.begin_seq = localStatsSummary.begin_seq;
                           audioStatistics.xRStatisticsSummary.dev_jitter = localStatsSummary.dev_jitter;
                           audioStatistics.xRStatisticsSummary.dev_ttl_or_hl = localStatsSummary.dev_ttl_or_hl;
                           audioStatistics.xRStatisticsSummary.dup_packets = localStatsSummary.dup_packets;
                           audioStatistics.xRStatisticsSummary.end_seq = localStatsSummary.end_seq;
                           audioStatistics.xRStatisticsSummary.lost_packets = localStatsSummary.lost_packets;
                           audioStatistics.xRStatisticsSummary.max_jitter = localStatsSummary.max_jitter;
                           audioStatistics.xRStatisticsSummary.max_ttl_or_hl = localStatsSummary.max_ttl_or_hl;
                           audioStatistics.xRStatisticsSummary.mean_jitter = localStatsSummary.mean_jitter;
                           audioStatistics.xRStatisticsSummary.mean_ttl_or_hl = localStatsSummary.mean_ttl_or_hl;
                           audioStatistics.xRStatisticsSummary.min_jitter = localStatsSummary.min_jitter;
                           audioStatistics.xRStatisticsSummary.min_ttl_or_hl = localStatsSummary.min_ttl_or_hl;
                        }

                        audioStatistics.intervalCallQualityReport = cpc::string(rtpStreamImpl->getIntervalCallQualityReport().c_str(), rtpStreamImpl->getIntervalCallQualityReport().size());

                        memset(&audioJitterStats, 0, sizeof(audioJitterStats));
                        webrtc::NetworkStatistics networkStats;
                        memset(&networkStats, 0, sizeof(networkStats));
                        if (ms->voe_neteq_stats()->GetNetworkStatistics(rtpStreamImpl->channel(), networkStats) == 0)
                        {
                           audioJitterStats.addedSamples = networkStats.addedSamples;
                           audioJitterStats.clockDriftPPM = networkStats.clockDriftPPM;
                           audioJitterStats.currentAccelerateRate = networkStats.currentAccelerateRate;
                           audioJitterStats.currentBufferSizeMs = networkStats.currentBufferSize;
                           audioJitterStats.currentDiscardRate = networkStats.currentDiscardRate;
                           audioJitterStats.currentEffectivePacketLossRate = networkStats.currentPacketLossRate;
                           audioJitterStats.currentSynthesizedAudioInsertRate = networkStats.currentExpandRate;
                           audioJitterStats.currentSynthesizedAudioPreemptiveInsertRate = networkStats.currentPreemptiveRate;
                           audioJitterStats.jitterBurstsFound = networkStats.jitterPeaksFound;
                           audioJitterStats.maxWaitingTimeMs = networkStats.maxWaitingTimeMs;
                           audioJitterStats.meanWaitingTimeMs = networkStats.meanWaitingTimeMs;
                           audioJitterStats.medianWaitingTimeMs = networkStats.medianWaitingTimeMs;
                           audioJitterStats.minWaitingTimeMs = networkStats.minWaitingTimeMs;
                           audioJitterStats.preferredBufferSizeMs = networkStats.preferredBufferSize;
                        }
                     }

                     int64_t startNTPtime = rtpStreamImpl->getCallStartTime();
                     int64_t currentNTPtime = webrtc::Clock::GetRealTimeClock()->CurrentNtpInMilliseconds();
                     uint64_t diffTimeMsecs = (uint64_t)(currentNTPtime - startNTPtime);
                     uint64_t currentEpochTime = TimeUtils::millisSinceUnixEpoch();
                     uint64_t deltaTimeMsecs = (uint64_t)(currentNTPtime - currentEpochTime);

                     double statsDurationSeconds = (double)(diffTimeMsecs / 1000);
                     double rx_bitrate = ((statsDurationSeconds > 0) ? ((double)(audioStatistics.streamDataCounters.bytesReceived * 8) / statsDurationSeconds) : 0);
                     double tx_bitrate = ((statsDurationSeconds > 0) ? ((double)(audioStatistics.streamDataCounters.bytesSent * 8) / statsDurationSeconds) : 0);

                     DebugLog(<< "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " startNTPtime: " << startNTPtime << " currentNTPtime: " << currentNTPtime << " diffTimeMsecs: " << diffTimeMsecs << " currentEpochTime: " << currentEpochTime << " deltaTimeMsecs: " << deltaTimeMsecs);

                     std::stringstream localEndpoint;
                     std::stringstream remoteEndpoint;
                     localEndpoint << audioStatistics.localEndpoint.ipAddress << ":" << audioStatistics.localEndpoint.port;
                     remoteEndpoint << audioStatistics.remoteEndpoint.ipAddress << ":" << audioStatistics.remoteEndpoint.port;

#ifdef __ANDROID__
                     int wifiRssi = 0xFFFFF, wifiSignalLevel = -1, wifiMaxSignalLevel = -1, wifiFreqMhz = -1, wifiChannel = -1, wifiLinkSpeedMbps = -1;
                     NetworkChangeManagerInterface* ncm = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPCM_if->phoneInterface()));
                     if (ncm)
                     {
                         wifiRssi = ncm->currentWifiRssi();
                         wifiSignalLevel = ncm->currentWifiSignalLevel();
                         wifiMaxSignalLevel = ncm->maxWifiSignalLevel();
                         wifiFreqMhz = ncm->currentWifiFreqMhz();
                         wifiChannel = ncm->currentWifiChannel();
                         wifiLinkSpeedMbps = ncm->currentWifiLinkSpeedMbps();
                     }
#endif

                     int64_t lastRtpReceivedMs = 0;
                     rtpStreamImpl->getLastReceivedRtpTimeMs(&lastRtpReceivedMs);
                     int64_t timeSinceLastRtpReceivedMs = webrtc::Clock::GetRealTimeClock()->TimeInMilliseconds() - lastRtpReceivedMs;
                     unsigned int packetsReceived(audioStatistics.streamDataCounters.packetsReceived);
                     unsigned int jitterMs = ((audioStatistics.streamStatistics.jitterSamples * 1000) / audioStatistics.decoder.plfreq);

                     // packetloss in percent since last RTCP report went out. jitter buffer does not seem to be involved in this calculation;
                     // severe jitter resulting in dropped packets is not always indicated here
                     double currentPacketLoss = (audioStatistics.streamStatistics.fractionLost * 100.0 / 255.0);

                     // packetloss in percent since last time ms->voe_neteq_stats()->GetNetworkStatistics was invoked. good indication of
                     // dropped packets due to jitter or network loss
                     double currentJbPacketLossRate = round(audioJitterStats.currentEffectivePacketLossRate * std::pow(2, -14) * 100);

                     // packetloss in percent taking into account only late packets discarded since last time ms->voe_neteq_stats()->GetNetworkStatistics was invoked
                     // note: seems to always be 0, even with very high jitter.
                     double currentDiscardRate = round(audioJitterStats.currentDiscardRate * std::pow(2, -14) * 100);

                     // audioStatistics.streamStatistics.rttMs always seems to be 0, likely due to disabled rtcp
                     PeerConnectionConversationCallQuality callQuality = calculateCallQuality(
                        audioStatistics.streamStatistics.fractionLost,
                        audioJitterStats.currentEffectivePacketLossRate,
                        jitterMs,
                        packetsReceived,
                        audioStatistics.streamStatistics.rttMs,
                        timeSinceLastRtpReceivedMs);

                     CPCAPI2::Json::JsonDataPointer json = Json::MakeJsonDataPointer();
                     bool supressLog(false);
                     bool noOverhead(true);
                     CPCAPI2::Json::JsonFunctionSerialize serializer(json, supressLog, "PeerConnectionManager", "logMediaStatistics", noOverhead);
                     serializer.addValue("peerConnection", mHandle);
                     serializer.addValue("stream", itMediaState->first);
                     serializer.addValue("mediaType", mediaTypeString(itMediaState->second.mediaDescriptor.mediaType).c_str());
                     serializer.addValue("mediaDirection", mediaDirectionString(itMediaState->second.mediaDescriptor.mediaDirection).c_str());
                     serializer.addValue("callQuality", callQualityString(callQuality).c_str());
                     serializer.addValue("callDurationMs", diffTimeMsecs);
                     serializer.addValue("callStartTime", getTimeString(startNTPtime - deltaTimeMsecs));
                     serializer.addValue("currentTime", getTimeString(currentNTPtime - deltaTimeMsecs));
                     serializer.addValue("noRxPacketUpdateMs", timeSinceLastRtpReceivedMs);
                     serializer.addValue("cumulativeLost", audioStatistics.streamStatistics.cumulativeLost);
                     serializer.addValue("currentPacketLoss", currentPacketLoss);
                     serializer.addValue("currentJbPacketLossRate", currentJbPacketLossRate);
                     serializer.addValue("currentDiscardRate", currentDiscardRate);
                     serializer.addValue("currentJbSizeMs", audioJitterStats.currentBufferSizeMs);
                     serializer.addValue("discarded", audioStatistics.discardedPackets);

                     // seems to always be 0 -- StatisticsUpdated(..) in channel.cc never seems to get past if (stats_.ssrc != ssrc) check
                     // serializer.addValue("averageJitterMs", audioStatistics.averageJitterMs);

                     // doesn't seem to accurately reflect actual jitter; in order check in StreamStatisticianImpl::UpdateCounters
                     // is questionable given RFC 3550 sec 6.4.1 specifies order of arrival should be used, not necessarily in sequence)
                     serializer.addValue("jitterMs", jitterMs);
                     serializer.addValue("rttMs", audioStatistics.streamStatistics.rttMs);
                     serializer.addValue("packetsRx", audioStatistics.streamDataCounters.packetsReceived);
                     serializer.addValue("packetsTx", audioStatistics.streamDataCounters.packetsSent);
                     serializer.addValue("reflowRtpPacketsRx", rtpRxReflowPackets);
                     serializer.addValue("reflowRtcpPacketsRx", rtcpRxReflowPackets);
                     serializer.addValue("bytesRx", audioStatistics.streamDataCounters.bytesReceived);
                     serializer.addValue("bpsRx", rx_bitrate);
                     serializer.addValue("bytesTx", audioStatistics.streamDataCounters.bytesSent);
                     serializer.addValue("bpsTx", tx_bitrate);
                     serializer.addValue("decoderPlName", audioStatistics.decoder.plname);
                     serializer.addValue("decoderFrequency", audioStatistics.decoder.plfreq);
                     serializer.addValue("decoderPacketSize", audioStatistics.decoder.pacsize);
                     serializer.addValue("decoderRate", audioStatistics.decoder.rate);
                     serializer.addValue("encoderPlName", audioStatistics.encoder.plname);
                     serializer.addValue("encoderFrequency", audioStatistics.encoder.plfreq);
                     serializer.addValue("encoderPacketSize", audioStatistics.encoder.pacsize);
                     serializer.addValue("encoderRate", audioStatistics.encoder.rate);
                     serializer.addValue("localEndpoint", localEndpoint.str().c_str());
                     serializer.addValue("remoteEndpoint", remoteEndpoint.str().c_str());
#ifdef __ANDROID__
                     // only android specific so far because we've only implented rssi/signal level retreival on android
                     if (ncm && ncm->networkTransport() == TransportWiFi)
                     {
                        serializer.addValue("wifiRssi", wifiRssi);
                        serializer.addValue("wifiSignalLevel", std::to_string(wifiSignalLevel) + "/" + std::to_string(wifiMaxSignalLevel));
                        serializer.addValue("wifiFreqMhz", wifiFreqMhz);
                        serializer.addValue("wifiChannel", wifiChannel);
                        serializer.addValue("wifiLinkSpeedMbps", wifiLinkSpeedMbps);
                     }
#endif
                     serializer.finalize();
                     CPCAPI2::JsonApi::JsonDataWebSocketCommand cmd(json);
                     PcStatLog(PC_LOGID_MEDIA_QOS, << "PeerConnectionManagerImpl::logMediaStatistics(): pc: " << mHandle << " stream: " << itMediaState->first << " version: " << PC_STAT_VERSION << " data: " << cmd.getLogString(0));
                  }
               }
            }
         }
      }
   }
}

/**
 * With 1-way streams and without RTCP, we do not have the statistics to take into account latency-rtt factor. As such the
 * latency (< 150 ms deemed acceptable, should remain < 300 ms) which can be caused by various factors:
 * - processing or compression or algorithmic - codec and dsp dependant (> 5ms for opus, typical 26.5 ms for 20 ms frame, dependant on bitrate)
 * - packetization or serialization (dependant on clock rate, frame or payload size)
 * - queuing or buffering (bandwidth or traffic-load dependant)
 * - network switching or nodal
 * - de-jitter
 *
 * Rather than ignoring the rtt value altogether, presuming an acceptable rtt of 150 msecs and below, and apply an adjustment factor.
 * Some qos thresholds based on industrial standards:
 * - packet loss < 1%
 * - 1-way latency < 150 ms (mouth to ear)
 * - average 1-way jitter < 30 ms
 *
 * MOS: Excellent (5), Good (4), Fair (3), Poor (2), Bad (1) => 1 + (0.035 * R) + ((.000007) * R * (R - 60) * (100 - R));  // > 4.34 Best, > 4.04 High, > 3.6 Medium, > 3.1 Low, < 3.1 Poor
 * Reference: https://www.ciscopress.com/articles/article.asp?p=357102, https://opus-codec.org/comparison/, https://www.diva-portal.org/smash/get/diva2:422202/FULLTEXT01.pdf
*/
PeerConnectionConversationCallQuality PeerConnectionManagerImpl::calculateCallQuality(
   unsigned short localFractionLost,
   unsigned short jitterPacketLossRate,
   unsigned int totalReceivedPackets,
   unsigned int jitterMsecs,
   int64_t latency,
   int64_t timeSinceLastRtpReceivedMs)
{
   // TODO: Should also take packet burst into account, could track number of packets in each stat query
   double currentPacketLoss = 0;
   if (localFractionLost > 0)
   {
      currentPacketLoss = localFractionLost * 100.0 / 255.0;
   }
   double currentJbPacketLossRate = round(jitterPacketLossRate * std::pow(2, -14) * 100);
   double jitterFactor = (100.0 * (jitterMsecs / 30.0)); // based on acceptable jitter of 30 ms

   // As average jitter and latency do not seem to be populated for PTT calls with RTCP disabled,
   // using an X factor based on the packet loss, jitter packet loss and jitter values, before
   // applying the mos calculator logic. Giving lower weightage to jitter.
   double X = 1.0 + (((std::min(100.0,currentPacketLoss)) + (std::min(100.0,currentJbPacketLossRate)) + std::min(100.0,jitterFactor))/(3.0 * 100.0));
   int64_t rttMs = latency;
   if (rttMs <= 0)
   {
      rttMs = X * 50; // use X-factor if latency not populated, using 50 ms as base (deterioration occurs beyond 150 ms)
   }

   // Logic from the mos calculator in the sdk call module
   double R = 0.0;
   double effectiveLatencyMs = rttMs + (jitterMsecs * 2) + 10; // TODO: should be using averageJitterMs
   if (effectiveLatencyMs < 160)
   {
      R = 93.2 - (effectiveLatencyMs / 40);
   }
   else
   {
      R = 93.2 - (effectiveLatencyMs - 120) / 10;
   }

   R = R - (currentPacketLoss * 4.0);

   double mosFloat;
   if (R < 0)
   {
      mosFloat = 1;
   }
   else if (R > 100)
   {
      mosFloat = 4.5;
   }
   else
   {
      mosFloat = 1 + (0.035 * R) + ((.000007) * R * (R - 60) * (100 - R));
   }

   //short mosShort = round(mosFloat * 10);

   PeerConnectionConversationCallQuality qualityMos = PeerConnectionConversationCallQuality_Unknown;
   PeerConnectionConversationCallQuality qualityPacketLoss = PeerConnectionConversationCallQuality_Unknown;

   if (totalReceivedPackets == 0)
   {
      qualityMos = PeerConnectionConversationCallQuality_Unknown;
   }
   else if (timeSinceLastRtpReceivedMs >= PEERCONNECTIONMANAGERIMPL_QOS_INACTIVITY_MSECS)
   {
      qualityMos = PeerConnectionConversationCallQuality_Unknown;
   }
   else if (mosFloat <= 2.0)
   {
      qualityMos = PeerConnectionConversationCallQuality_Poor;
   }
   else if (mosFloat >= 4.0)
   {
      qualityMos = PeerConnectionConversationCallQuality_Good;
   }
   else
   {
      qualityMos = PeerConnectionConversationCallQuality_Fair;
   }

   // DebugLog(<< "PeerConnectionManagerImpl::calculateCallQuality(): MOS: " << mosShort << " mosFloat: " << mosFloat << " localFractionLost: " << localFractionLost << " currentPacketLoss: " << currentPacketLoss << " jitterPacketLossRate: " << jitterPacketLossRate << " currentJbPacketLossRate: " << currentJbPacketLossRate << " jitterMsecs: " << jitterMsecs << " jitterFactor: " << jitterFactor << " X-Factor: " << X << " rttMs: " << rttMs << " quality-mos: " << qualityMos << " quality-packet-loss: " << qualityPacketLoss);
   return qualityMos;
}

void PeerConnectionManagerImpl::onSdpReady()
{
   DebugLog(<< "onSdpReady: " << this->sdpDisposition);
   resip::Data sdpData(resip::Data::from(*this->sdp));
   if (this->sdpDisposition == AVOfferAnswerSession::SdpDisposition_Offer)
   {
      CreateOfferResult args;
      args.sdp.sdpLen = sdpData.size();
      args.sdp.sdpString = sdpData.c_str();
      args.sdp.sdpType = (SessionDescription::SessionDescriptionType)this->sdpDisposition;
      mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onCreateOfferResult), mHandle, args);
   }
   else if (this->sdpDisposition == AVOfferAnswerSession::SdpDisposition_Answer)
   {
      CreateAnswerResult args;
      args.sdp.sdpLen = sdpData.size();
      args.sdp.sdpString = sdpData.c_str();
      args.sdp.sdpType = (SessionDescription::SessionDescriptionType)this->sdpDisposition;
      mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onCreateAnswerResult), mHandle, args);
   }
}

void PeerConnectionManagerImpl::setAudioDeviceCloseDelay(int audioDeviceCloseDelay)
{
   mAudioDeviceCloseDelayMsecs = audioDeviceCloseDelay;

   for (auto it = mMediaStreams.begin(); it != mMediaStreams.end(); ++it)
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStream = it->second.rtpStream.lock())
      {
         rtpStream->setAudioDeviceCloseDelay(mAudioDeviceCloseDelayMsecs);
      }
   }
}

void PeerConnectionManagerImpl::onMediaStreamReady(bool localSdpSet, bool remoteSdpSet)
{
   SetLocalSessionDescriptionResult argsLocal;
   SetRemoteSessionDescriptionResult argsRemote;
   std::vector<AVOfferAnswerSession::MediaStreamInfo>::const_iterator itMsi = MediaStreamReady::mediaStreamInfo.begin();
   for (; itMsi != MediaStreamReady::mediaStreamInfo.end(); ++itMsi)
   {
      std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = std::dynamic_pointer_cast<webrtc_recon::RtpStreamImpl>(itMsi->rtpStream);

      rtpStreamImpl->setAudioDeviceCloseDelay(mAudioDeviceCloseDelayMsecs);

      int msidInt = itMsi->msid.convertInt();
      MediaStreamStateMap::iterator itMediaState = mMediaStreams.find(msidInt);
      if (itMediaState != mMediaStreams.end())
      {
         itMediaState->second.mediaDescriptor.mediaStreamId = rtpStreamImpl->channel();
         itMediaState->second.rtpStream = rtpStreamImpl;
         itMediaState->second.updateTimestamp = std::chrono::system_clock::now();

         CPCAPI2::PeerConnection::MediaInfo mi = itMediaState->second.mediaDescriptor;
         argsRemote.mediaInfo.push_back(mi);
         argsLocal.mediaInfo.push_back(mi);

         //if (itTrans->second.initParams.sendEncodings.size() > 0)
         //{
         //   assert(itTrans->second.initParams.sendEncodings.size() == 1); // multiple encodings not yet supported
         //   RtpEncodingParameters encodingParams = itTrans->second.initParams.sendEncodings[0];
         //   if (rtpStreamImpl->mediaType() == recon::MediaStack::MediaType_Video)
         //   {
         //      rtpStreamImpl->setVideoEncodingParams(encodingParams.maxBitrate, encodingParams.maxFramerate);
         //   }
         //}
      }
   }

   if (localSdpSet)
   {
      mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onSetLocalSessionDescriptionResult), mHandle, argsLocal);
   }
   if (remoteSdpSet)
   {
      mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onSetRemoteSessionDescriptionResult), mHandle, argsRemote);
   }
}

void PeerConnectionManagerImpl::onMediaStreamUsable(const resip::Data& msid)
{
   InfoLog(<< "onMediaStreamUsable: msid: " << msid);

   int msidInt = msid.convertInt();
   MediaStreamStateMap::iterator itMediaState = mMediaStreams.find((MediaStreamHandle)msidInt);
   if (itMediaState != mMediaStreams.end())
   {
      if (std::shared_ptr<webrtc_recon::RtpStreamImpl> rtpStreamImpl = itMediaState->second.rtpStream.lock())
      {
         if (rtpStreamImpl->mediaType() == recon::MediaStack::MediaType_Video &&
             rtpStreamImpl->isReceivingRtp())
         {
            InfoLog(<< "onMediaStreamUsable - requesting key frame for channel " << rtpStreamImpl->channel());
            std::shared_ptr<webrtc_recon::MediaStackImpl> ms = std::dynamic_pointer_cast<webrtc_recon::MediaStackImpl>(rtpStreamImpl->getMediaStack());
            ms->vie_rtp_rtcp()->RequestKeyFrame(rtpStreamImpl->channel());
         }
         else
         {
            InfoLog(<< "onMediaStreamUsable - audio");
         }
      }
   }
}

void PeerConnectionManagerImpl::onVideoWebsocketServerStarted(int mediaStreamId, const CPCAPI2::Media::VideoWebsocketServerStartedEvent& args)
{
   WebVideoServerReadyEvent wvsReadyArgs;
   mPCM_if->firePcEvent(cpcFunc(PeerConnectionHandler::onWebVideoServerReady), mHandle, wvsReadyArgs);
}

}

}

#endif
