#pragma once

#ifndef CPCAPI2_PEER_CONNECTION_HANDLER_INTERNAL_H
#define CPCAPI2_PEER_CONNECTION_HANDLER_INTERNAL_H

#include <peerconnection/PeerConnectionTypes.h>
#include <peerconnection/PeerConnectionHandler.h>
#include <cassert>

#define PcStatLog(x,y) InfoLog(y << " log-id: " << x)
#define PC_STAT_VERSION                                                   "1.0.0"
#define PC_LOGID_MEDIA_QOS                                                1000


namespace CPCAPI2
{

namespace PeerConnection
{
   struct PeerConnectionEndpoint
   {
      cpc::string ipAddress;
      int port;
   };

   enum PeerConnectionConversationCallQuality
   {
      PeerConnectionConversationCallQuality_Unknown,
      PeerConnectionConversationCallQuality_Good,
      PeerConnectionConversationCallQuality_Fair,
      PeerConnectionConversationCallQuality_Poor
   };

   struct PeerConnectionAudioJitterBufferStatistics
   {
      unsigned short currentBufferSizeMs; // current jitter buffer size in milliseconds
      unsigned short preferredBufferSizeMs; // optimal jitter buffer size in milliseconds
      bool jitterBurstsFound; // true if the jitter buffer has expanded due to bursts of jitter
      unsigned short currentEffectivePacketLossRate; // packet loss rate, taking into account packets lost in the network and packets discarded by the jitter buffer because they are too late (in percent) (in Q14)
      unsigned short currentDiscardRate; // packet loss rate, taking into account late packets only (in percent) (in Q14)
      unsigned short currentSynthesizedAudioInsertRate; // amount of synthesized audio inserted, expressed as a fraction of the original stream (in Q14)
      unsigned short currentSynthesizedAudioPreemptiveInsertRate; // amount of synthesized audio inserted pre-emptively, expressed as a fraction of the original stream (in Q14)
      unsigned short currentAccelerateRate; // amount of audio removed by compressing it in time, expressed as a fraction of the original stream (in Q14)
      int clockDriftPPM;  // clock-drift in parts-per-million (may be negative or positive)
      int meanWaitingTimeMs; // average packet waiting time in the jitter buffer in milliseconds
      int medianWaitingTimeMs; // median packet waiting time in the jitter buffer in milliseconds
      int minWaitingTimeMs; // minimum packet waiting time in the jitter buffer in milliseconds
      int maxWaitingTimeMs; // maximum packet waiting time in the jitter buffer in milliseconds
      int addedSamples; // number of zeroe'd-out samples added as a result of the jitter buffer's built-in packet loss concealment
   };

   struct PeerConnectionStreamStatistics
   {
      unsigned short fractionLost;
      unsigned int cumulativeLost;
      unsigned int extendedMax;
      unsigned int jitterSamples;
      int64_t rttMs;
   };

   struct PeerConnectionStreamDataCounters
   {
      unsigned int bytesSent;
      unsigned int packetsSent;
      unsigned int bytesReceived;
      unsigned int packetsReceived;
   };

   struct PeerConnectionAudioCodec
   {
      int pltype = 0;
      char plname[32];
      int plfreq = 0;
      int pacsize = 0;
      int channels = 0;
      int rate = 0;
      unsigned int priority = 0;
      cpc::string displayName;

      PeerConnectionAudioCodec()
      {
         plname[0] = 0;
      }
   };

   /**
    * Voice Quality Monitoring enabled SDKs only.
   */
   struct PeerConnectionXRVoipMetrics
   {
      // RFC 3611 4.7
      unsigned short lossRate;
      unsigned short discardRate;
      unsigned short burstDensity;
      unsigned short gapDensity;
      unsigned short burstDuration;
      unsigned short gapDuration;
      unsigned short roundTripDelay;
      unsigned short endSystemDelay;
      unsigned short signalLevel;
      unsigned short noiseLevel;
      unsigned short RERL;
      unsigned short Gmin;
      unsigned short Rfactor;
      unsigned short extRfactor;
      unsigned short MOSLQ;
      unsigned short MOSCQ;
      unsigned short RXconfig;
      unsigned short JBnominal;
      unsigned short JBmax;
      unsigned short JBabsMax;
   };

   /**
    * Voice Quality Monitoring enabled SDKs only.
   */
   struct PeerConnectionXRStatisticsSummary
   {
      // RFC 3611 4.6
      unsigned short begin_seq;
      unsigned short end_seq;
      unsigned int lost_packets;
      unsigned int dup_packets;
      unsigned int min_jitter;
      unsigned int max_jitter;
      unsigned int mean_jitter;
      unsigned int dev_jitter;
      unsigned short min_ttl_or_hl;
      unsigned short max_ttl_or_hl;
      unsigned short mean_ttl_or_hl;
      unsigned short dev_ttl_or_hl;
   };

   struct PeerConnectionAudioStatistics
   {
      PeerConnectionAudioCodec encoder;
      PeerConnectionAudioCodec decoder;
      PeerConnectionStreamStatistics streamStatistics;
      PeerConnectionStreamDataCounters streamDataCounters;
      unsigned int maxJitterMs; // Max jitter over whole call duration
      unsigned int averageJitterMs; // Average jitter over whole call duration
      unsigned int discardedPackets; // Number of discarded packets over whole call duration
      PeerConnectionXRVoipMetrics xRVoipMetrics; // VQM enabled SDKs only
      PeerConnectionXRStatisticsSummary xRStatisticsSummary; // VQM enabled SDKs only
      cpc::string intervalCallQualityReport;
      int64_t callStartTimeNTP;
      PeerConnectionEndpoint localEndpoint;
      PeerConnectionEndpoint remoteEndpoint;
   };

   struct PeerConnectionMediaStreamStatistics
   {
      MediaStreamHandle mediaStream = 0;
      int rtpPacketCount = 0;
      int rtcpPacketCount = 0;
      int videoReceiveFrameWidth = 0;
      int videoReceiveFrameHeight = 0;
      int videoReceiveFps = 0;
   };

   struct PeerConnectionMediaStatisticsEvent
   {
      cpc::vector<PeerConnectionMediaStreamStatistics> mediaStreamStats;
   };

   /**
    * Private interface for internal methods on the SDK Observers.
   */
   class PeerConnectionHandlerInternal : public PeerConnectionHandler
   {

   public:

      virtual int onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle peerConnection, const PeerConnectionMediaStatisticsEvent& args) { assert(0); return 0; };

   };
}

}

#endif /* CPCAPI2_PEER_CONNECTION_HANDLER_INTERNAL_H */
