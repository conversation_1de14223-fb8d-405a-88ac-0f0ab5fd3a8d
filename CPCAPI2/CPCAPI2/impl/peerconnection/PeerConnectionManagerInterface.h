#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_MANAGER_INTERFACE_H)
#define CPCAPI2_PEER_CONNECTION_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionManagerInternal.h"
#include "peerconnection/PeerConnectionHandlerInternal.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "../phone/Cpcapi2EventSource.h"
#include "../phone/NetworkChangeManagerImpl.h"

#include <rutil/Fifo.hxx>

#include <map>

namespace resip
{
class ResolverHandler;
}

namespace flowmanager
{
class FlowManager;
}

namespace CPCAPI2
{
class PhoneInterface;

namespace PeerConnection
{
class PeerConnectionManagerImpl;

class PeerConnectionManagerInterface : public PeerConnectionManager,
                                       public PeerConnectionManagerInternal,
                                       public CPCAPI2::EventSource<PeerConnectionHandle, PeerConnectionHandler, PeerConnectionSyncHandler>,
                                       public PhoneModule
{
public:
   PeerConnectionManagerInterface(Phone* phone);
   virtual ~PeerConnectionManagerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int setHandler(PeerConnectionHandle pc, PeerConnectionHandler* handler) OVERRIDE;
   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<PeerConnectionHandle, PeerConnectionHandler, PeerConnectionSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<PeerConnectionHandle, PeerConnectionHandler, PeerConnectionSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<PeerConnectionHandle, PeerConnectionHandler, PeerConnectionSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual PeerConnectionHandle createPeerConnection() OVERRIDE;

   virtual int setDefaultSettings(PeerConnectionHandle pc, const PeerConnectionSettings& settings) OVERRIDE;
   virtual int createOffer(PeerConnectionHandle pc) OVERRIDE;
   virtual int createAnswer(PeerConnectionHandle pc) OVERRIDE;
   virtual int setLocalDescription(PeerConnectionHandle pc, const SessionDescription& sdp) OVERRIDE;
   virtual int setRemoteDescription(PeerConnectionHandle pc, const SessionDescription& sdp) OVERRIDE;
   virtual int close(PeerConnectionHandle pc) OVERRIDE;
   virtual MediaStreamHandle createMediaStream() OVERRIDE;
   virtual int configureMedia(PeerConnectionHandle pc, MediaStreamHandle mediaStream, const CPCAPI2::PeerConnection::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int startWebVideoServerForMediaStream(PeerConnectionHandle pc, MediaStreamHandle mediaStream) OVERRIDE;
   virtual int playSound(PeerConnectionHandle pc, const cpc::string& mediaUri, bool repeat) OVERRIDE;
   virtual int addAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp) OVERRIDE;
   virtual int removeAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp) OVERRIDE;
   virtual int startMediaInactivityMonitor(PeerConnectionHandle pc) OVERRIDE;
   virtual int stopMediaInactivityMonitor(PeerConnectionHandle pc) OVERRIDE;
   virtual int startLoggingMediaStatistics(PeerConnectionHandle pc) OVERRIDE;
   virtual int stopLoggingMediaStatistics(PeerConnectionHandle pc) OVERRIDE;
   virtual int setAudioDeviceCloseDelay(int msecs) OVERRIDE;
   virtual int pauseAudioPlayout(PeerConnectionHandle pc) OVERRIDE;
   virtual int resumeAudioPlayout(PeerConnectionHandle pc) OVERRIDE;

   // PeerConnectionManagerInternal
   virtual int setMediaInactivityTimeoutMsecs(PeerConnectionHandle pc, int msecs) OVERRIDE;
   virtual int setLoggingMediaStatisticsFrequencyMsecs(PeerConnectionHandle pc, int msecs) OVERRIDE;
   virtual int pauseTxMedia(PeerConnectionHandle pc, const SessionDescription& sdp) OVERRIDE;
   virtual int queryMediaStatistics(PeerConnectionHandle pc) OVERRIDE;
   virtual int logMediaStatistics(PeerConnectionHandle pc) OVERRIDE;

   void createPeerConnection(PeerConnectionHandle pc);
   int preinitDns(const cpc::string& natTraversalServerHostname, int natTraversalServerPort);

   PhoneInterface* phoneInterface() const { return mPhone; }

   template<typename TFn, typename TEvt> void firePcEvent(const char* funcName, TFn func, PeerConnectionHandle h, const TEvt& args)
   {
      fireEvent(funcName, func, h, args);
   }

   template<typename TFn, typename TEvt> void firePcInternalEvent(const char* funcName, TFn func, PeerConnectionHandle h, const TEvt& args)
   {
#if defined(CPCAPI2_AUTO_TEST)
      {
         for (thandlersafe_iterator itObs = mSdkObserversSafe.begin(); itObs != mSdkObserversSafe.end(); ++itObs)
         {
            if (std::shared_ptr<PeerConnectionHandler> handler = (*itObs).lock())
            {
               PeerConnectionHandlerInternal* internalHandler = dynamic_cast<PeerConnectionHandlerInternal*>(handler.get());
               if (internalHandler)
               {
                  resip::ReadCallbackBase* cb = resip::resip_bind(func, internalHandler, h, args);
                  if (dynamic_cast<PeerConnectionSyncHandler*>(handler.get()))
                  {
                     (*cb)();
                     delete cb;
                  }
                  else
                  {
                     postCallback(cb);
                  }
               }
            }
         }

         observerhandlermap_iterator itObserver = mSdkObserversScoped.find(h);
         if (itObserver != mSdkObserversScoped.end())
         {
            for (thandler_iterator itObs = itObserver->second.begin(); itObs != itObserver->second.end(); ++itObs)
            {
               PeerConnectionHandlerInternal* internalHandler = dynamic_cast<PeerConnectionHandlerInternal*>(*itObs);
               if (internalHandler)
               {
                  resip::ReadCallbackBase* cb = resip::resip_bind(func, internalHandler, h, args);
                  if (dynamic_cast<PeerConnectionSyncHandler*>(*itObs))
                  {
                     (*cb)();
                     delete cb;
                  }
                  else
                  {
                     postCallback(cb);
                  }
               }
            }
         }
      }
#endif
   }

private:
   int createPeerConnectionImpl(PeerConnectionHandle pc);
   int setDefaultSettingsImpl(PeerConnectionHandle pc, const PeerConnectionSettings& settings);
   int createOfferImpl(PeerConnectionHandle pc);
   int createAnswerImpl(PeerConnectionHandle pc);
   int setLocalDescriptionImpl(PeerConnectionHandle pc, const SessionDescription& sdp);
   int setRemoteDescriptionImpl(PeerConnectionHandle pc, const SessionDescription& sdp);
   int configureMediaImpl(PeerConnectionHandle pc, MediaStreamHandle mediaStream, const CPCAPI2::PeerConnection::MediaInfo& mediaDescriptor);
   int closeImpl(PeerConnectionHandle pc);
   int startWebVideoServerForMediaStreamImpl(PeerConnectionHandle pc, MediaStreamHandle mediaStream);
   int playSoundImpl(PeerConnectionHandle pc, const cpc::string& mediaUri, bool repeat);
   int addAdditionalDestinationImpl(PeerConnectionHandle pc, const SessionDescription& sdp);
   int removeAdditionalDestinationImpl(PeerConnectionHandle pc, const SessionDescription& sdp);
   int startMediaInactivityMonitorImpl(PeerConnectionHandle pc);
   int stopMediaInactivityMonitorImpl(PeerConnectionHandle pc);
   int startLoggingMediaStatisticsImpl(PeerConnectionHandle pc);
   int stopLoggingMediaStatisticsImpl(PeerConnectionHandle pc);
   int setMediaInactivityTimeoutMsecsImpl(PeerConnectionHandle pc, int msecs);
   int setLoggingMediaStatisticsFrequencyMsecsImpl(PeerConnectionHandle pc, int msecs);
   int queryMediaStatisticsImpl(PeerConnectionHandle pc);
   int pauseTxMediaImpl(PeerConnectionHandle pc, const SessionDescription& sdp);
   int logMediaStatisticsImpl(PeerConnectionHandle pc);
   int setAudioDeviceCloseDelayImpl(int msecs);
   int pauseAudioPlayoutImpl(PeerConnectionHandle pc);
   int resumeAudioPlayoutImpl(PeerConnectionHandle pc);
   int getImpl(PeerConnectionHandle pc, PeerConnectionManagerImpl*& pimpl);
   int preinitDtls();
   int guessLocalIpAddress(resip::Data& outLocalIpAddr);

private:
   bool mShutdown;
   PhoneInterface* mPhone;
   typedef std::map<PeerConnectionHandle, PeerConnectionManagerImpl*> PeerConnMap;
   PeerConnMap mPeerConnMap;
   flowmanager::FlowManager* mFlowManager;
   int mAudioDeviceCloseDelayMsecs;
   resip::ResolverHandler* mNatTraversalServerResolveHelper;
};

class PeerConnectionHandleFactory
{
public:
   static PeerConnectionHandle getNext() { return sNextHandle++; }
private:
   static PeerConnectionHandle sNextHandle;
};

class MediaStreamHandleFactory
{
public:
   static MediaStreamHandle getNext() { return sNextHandle++; }
private:
   static MediaStreamHandle sNextHandle;
};

}
}

#endif // CPCAPI2_PEER_CONNECTION_MANAGER_INTERFACE_H
