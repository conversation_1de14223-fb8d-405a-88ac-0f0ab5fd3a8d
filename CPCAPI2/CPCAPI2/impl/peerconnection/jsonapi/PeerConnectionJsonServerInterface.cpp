#include "brand_branded.h"

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "PeerConnectionJsonServerInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "media/VideoJsonApi.h"
#include "media/jsonapi/VideoJsonServerInterface.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_CALL
#define JSON_MODULE "PeerConnectionManagerJsonProxy"

namespace CPCAPI2
{
namespace PeerConnection
{
PeerConnectionJsonServerInterface::PeerConnectionJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mPeerConnMgr(CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(phone))
{
   PeerConnectionManagerInterface* confBridgeMgrIf = dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr);
   confBridgeMgrIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&PeerConnectionJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["createPeerConnection"] = std::bind(&PeerConnectionJsonServerInterface::handleCreatePeerConnection, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setDefaultSettings"] = std::bind(&PeerConnectionJsonServerInterface::handleSetDefaultSettings, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["configureMedia"] = std::bind(&PeerConnectionJsonServerInterface::handleConfigureMedia, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["createOffer"] = std::bind(&PeerConnectionJsonServerInterface::handleCreateOffer, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setLocalDescription"] = std::bind(&PeerConnectionJsonServerInterface::handleSetLocalDescription, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setRemoteDescription"] = std::bind(&PeerConnectionJsonServerInterface::handleSetRemoteDescription, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["close"] = std::bind(&PeerConnectionJsonServerInterface::handleClose, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startWebVideoServerForMediaStream"] = std::bind(&PeerConnectionJsonServerInterface::handleStartWebVideoServerForMediaStream, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

PeerConnectionJsonServerInterface::~PeerConnectionJsonServerInterface()
{
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->removeSdkObserver(this);
}

void PeerConnectionJsonServerInterface::Release()
{
}

void PeerConnectionJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void PeerConnectionJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int PeerConnectionJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&PeerConnectionJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void PeerConnectionJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   DebugLog(<< "PeerConnectionJsonServerInterface handling " << funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int PeerConnectionJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   post(resip::resip_bind(&PeerConnectionJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void PeerConnectionJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
   MapConnToPeerConns::iterator it = mMapConnToPeerConns.find(conn);
   if (it != mMapConnToPeerConns.end())
   {
      for (std::vector<PeerConnectionHandle>::iterator itPc = it->second.begin(); itPc != it->second.end(); ++itPc)
      {
         mPeerConnMgr->close(*itPc);
      }
      mMapConnToPeerConns.erase(it);

      // forward the connection closed message to the Video interface now, in case it already handled 
      // the connection closed event for this connection and missed its chance to clean up video capture
      // devices, since the PeerConnection RTP streams weren't torn down yet
      CPCAPI2::Media::VideoJsonApi* videoJsonApi = CPCAPI2::Media::VideoJsonApi::getInterface(mPhone);
      dynamic_cast<CPCAPI2::Media::VideoJsonServerInterface*>(videoJsonApi)->handleConnectionClosed(conn);
   }
}

int PeerConnectionJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   JsonDeserialize(functionObjectVal, "pc", pcHandle);
   mPeerConnMgr->setHandler(pcHandle, (PeerConnectionHandler*)0xDEADBEFF);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleCreatePeerConnection(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   JsonDeserialize(functionObjectVal, "pc", pcHandle);
   dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr)->createPeerConnection(pcHandle);
   mMapConnToPeerConns[connId].push_back(pcHandle);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleSetDefaultSettings(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   PeerConnectionSettings pcSettings;

   JsonDeserialize(functionObjectVal, "pc", pcHandle, "settings", pcSettings);

   mPeerConnMgr->setDefaultSettings(pcHandle, pcSettings);

   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleConfigureMedia(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   MediaStreamHandle msHandle = 0;
   CPCAPI2::PeerConnection::MediaInfo mi;

   JsonDeserialize(functionObjectVal, "pc", pcHandle, "mediaStream", msHandle, "mediaDescriptor", mi);

   if (functionObjectVal.HasMember("mediaDescriptor"))
   {
      mPeerConnMgr->configureMedia(pcHandle, msHandle, mi);
   }
   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleCreateOffer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   JsonDeserialize(functionObjectVal, "pc", pcHandle);
   mPeerConnMgr->createOffer(pcHandle);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleSetLocalDescription(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   CPCAPI2::PeerConnection::SessionDescription sdp;

   JsonDeserialize(functionObjectVal, "pc", pcHandle, "sdp", sdp.sdpString, "sdpType", sdp.sdpType);
   sdp.sdpLen = sdp.sdpString.size();

   mPeerConnMgr->setLocalDescription(pcHandle, sdp);

   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleSetRemoteDescription(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   CPCAPI2::PeerConnection::SessionDescription sdp;

   JsonDeserialize(functionObjectVal, "pc", pcHandle, "sdp", sdp.sdpString, "sdpType", sdp.sdpType);
   sdp.sdpLen = sdp.sdpString.size();

   mPeerConnMgr->setRemoteDescription(pcHandle, sdp);

   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleClose(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   JsonDeserialize(functionObjectVal, "pc", pcHandle);
   mPeerConnMgr->close(pcHandle);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::handleStartWebVideoServerForMediaStream(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   PeerConnectionHandle pcHandle = 0;
   MediaStreamHandle msHandle = 0;

   JsonDeserialize(functionObjectVal, "pc", pcHandle, "ms", msHandle);

   mPeerConnMgr->startWebVideoServerForMediaStream(pcHandle, msHandle);

   return kSuccess;
}

int PeerConnectionJsonServerInterface::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   JsonFunctionCall(mTransport, "onCreateOfferResult", JSON_VALUE(pc), "sdpOffer", args.sdp.sdpString);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   JsonFunctionCall(mTransport, "onSetRemoteSessionDescriptionResult", JSON_VALUE(pc), "mediaInfo", args.mediaInfo);
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onWebVideoServerReady(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::WebVideoServerReadyEvent& args)
{
   JsonFunctionCall(mTransport, "onWebVideoServerReady", JSON_VALUE(pc));
   return kSuccess;
}

int PeerConnectionJsonServerInterface::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   return kSuccess;
}


}
}
#endif
