#pragma once

#if !defined(CPCAPI2_PEER_CONNECTION_JSON_INTERFACE_H)
#define CPCAPI2_PEER_CONNECTION_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/peerconnection/PeerConnectionJsonApi.h"
#include "interface/experimental/peerconnection/PeerConnectionHandler.h"
#include "interface/experimental/peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace PeerConnection
{
class PeerConnectionJsonServerInterface : public CPCAPI2::PeerConnection::PeerConnectionHandler,
                                           public CPCAPI2::PeerConnection::PeerConnection<PERSON><PERSON><PERSON><PERSON>,
                                           public CPCAPI2::PeerConnection::Peer<PERSON>on<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   PeerConnectionJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~PeerConnectionJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args) OVERRIDE;
   virtual int onWebVideoServerReady(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::WebVideoServerReadyEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleCreatePeerConnection(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetDefaultSettings(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleConfigureMedia(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleCreateOffer(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetLocalDescription(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetRemoteDescription(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleClose(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartWebVideoServerForMediaStream(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   typedef std::map<CPCAPI2::JsonApi::JsonApiRequestInfo, std::vector<CPCAPI2::PeerConnection::PeerConnectionHandle> > MapConnToPeerConns;
   MapConnToPeerConns mMapConnToPeerConns;
};
}
}
#endif // CPCAPI2_PEER_CONNECTION_JSON_INTERFACE_H
