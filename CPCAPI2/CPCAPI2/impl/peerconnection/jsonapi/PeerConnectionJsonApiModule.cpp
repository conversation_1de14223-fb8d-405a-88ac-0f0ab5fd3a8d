#include "brand_branded.h"

#include <interface/experimental/peerconnection/PeerConnectionJsonApi.h>

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "PeerConnectionJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace PeerConnection
   {
      PeerConnectionJsonApi* PeerConnectionJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<PeerConnectionJsonServerInterface>(phone, "PeerConnectionJsonA<PERSON>");
#else
         return NULL;
#endif
      }

   }
}
