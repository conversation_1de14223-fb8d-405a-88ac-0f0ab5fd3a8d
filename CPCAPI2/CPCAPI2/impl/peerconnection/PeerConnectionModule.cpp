#include "brand_branded.h"

#include "interface/experimental/peerconnection/PeerConnectionManager.h"

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
#include "PeerConnectionManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace PeerConnection
{
PeerConnectionManager* PeerConnectionManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<PeerConnectionManagerInterface>(phone, "PeerConnectionManagerInterface");
#else
   return NULL;
#endif
}

}
}
