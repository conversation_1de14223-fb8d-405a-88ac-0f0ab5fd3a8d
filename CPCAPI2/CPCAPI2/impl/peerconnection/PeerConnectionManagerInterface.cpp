#include "brand_branded.h"

#if (CPCAPI2_BRAND_PEER_CONNECTION_MODULE == 1)
#include "cpcapi2utils.h"
#include "PeerConnectionManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../media/MediaManagerInterface.h"
#include "video_ext/VideoExt.h"
#include "../util/IpHelpers.h"
#include "../util/cpc_logger.h"
#include "PeerConnectionManagerImpl.h"

#include <rutil/ParseBuffer.hxx>
#include <rutil/Random.hxx>

#include <AVOfferAnswerSession.hxx>
#include <reflow/FlowManager.hxx>

using namespace CPCAPI2::Media;
using namespace resip;
using namespace recon;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PEERCONNECTION

namespace CPCAPI2
{
namespace PeerConnection
{
PeerConnectionHandle PeerConnectionHandleFactory::sNextHandle = 1;
MediaStreamHandle MediaStreamHandleFactory::sNextHandle = 1;

recon::AVOfferAnswerSession::SdpDisposition toReconSdpDisp(CPCAPI2::PeerConnection::SessionDescription::SessionDescriptionType sdpType)
{
   switch (sdpType)
   {
   case SessionDescription::SessionDescriptionType_Offer:
      return recon::AVOfferAnswerSession::SdpDisposition_Offer;
   case SessionDescription::SessionDescriptionType_Answer:
      return recon::AVOfferAnswerSession::SdpDisposition_Answer;
   case SessionDescription::SessionDescriptionType_Pranswer:
      return recon::AVOfferAnswerSession::SdpDisposition_Pranswer;
   }
   return recon::AVOfferAnswerSession::SdpDisposition_Offer;
}

class ResolveHelper : public resip::ResolverHandler
{
public:
   ResolveHelper(PhoneInterface* phone, flowmanager::FlowManager* flowManager) : mPhone(phone), mFlowManager(flowManager) {}
   virtual ~ResolveHelper() {}

   void resolve(const resip::Data& hostname, int natTraversalServerPort, bool v6) {
      if (hostname == mHostname)
         return;

      mHostname = hostname;
      if (mResolver.get() == NULL) {
         resip::DnsStub::DnsSettings dnsSettings;
         dnsSettings.includeIpv6SystemDnsServers = v6;
         // Add Google DNS as fallback
         dnsSettings.includeSystemDnsServers = true;
         dnsSettings.nameServers.push_back(resip::Tuple("*******", 53, resip::UDP).toGenericIPAddress());
         dnsSettings.nameServers.push_back(resip::Tuple("*******", 53, resip::UDP).toGenericIPAddress());
         mResolver.reset(resip::Resolver<resip::MultiReactor>::createResolver(&mPhone->getSdkModuleThread(), dnsSettings));
      }
      mResolver->async_resolve(hostname, resip::Data::from(natTraversalServerPort), this, 0, NULL);
   }

   virtual void onResult(unsigned short resolveId, const std::vector<resip::Tuple>& results,
      void* appState, ErrorType errorType) {
      PeerConnectionManagerInterface* peerConnMgr = dynamic_cast<PeerConnectionManagerInterface*>(PeerConnectionManager::getInterface(mPhone));
      if (peerConnMgr != NULL)
      {
         if (!results.empty())
         {
            for (const resip::Tuple& r : results)
            {
               InfoLog(<< "Update DNS static cache with entry: " << mHostname << " --> " << r);
            }
            mFlowManager->preinitStandaloneDns(mHostname, results);
         }
      }
   }

   void dispose() {
      mPhone->getSdkModuleThread().post(resip::resip_static_bind(&ResolveHelper::disposeImpl, this));
   }

private:
   static void disposeImpl(ResolveHelper* resolveHelper) {
      delete resolveHelper;
   }

private:
   PhoneInterface* mPhone;
   flowmanager::FlowManager* mFlowManager;
   std::unique_ptr<resip::Resolver<resip::MultiReactor> > mResolver;
   resip::Data mHostname;
};

PeerConnectionManagerInterface::PeerConnectionManagerInterface(Phone* phone)
   : EventSource<PeerConnectionHandle, PeerConnectionHandler, PeerConnectionSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mFlowManager(NULL),
     mAudioDeviceCloseDelayMsecs(0),
     mNatTraversalServerResolveHelper(NULL)
{
   CPCAPI2::Media::MediaManager* mm = MediaManager::getInterface(mPhone);
   webrtc_recon::MediaStackImpl* mediaStack = dynamic_cast<MediaManagerInterface*>(mm)->media_stack();
   mFlowManager = new flowmanager::FlowManager(mediaStack->getMediaTransportsReactorFactory());
   if (mPhone->getSdkModuleThread().isCurrentThread())
   {
      preinitDtls();
   }
   else
   {
      postToSdkThread(resip::resip_bind(&PeerConnectionManagerInterface::preinitDtls, this));
   }
}

PeerConnectionManagerInterface::~PeerConnectionManagerInterface()
{
   mShutdown = true;
   delete mFlowManager;
   if (mNatTraversalServerResolveHelper != NULL)
   {
      dynamic_cast<ResolveHelper*>(mNatTraversalServerResolveHelper)->dispose(); // delete self on SDK thread
   }
}

void PeerConnectionManagerInterface::Release()
{
   delete this;
}

int PeerConnectionManagerInterface::getImpl(PeerConnectionHandle pc, PeerConnectionManagerImpl*& pimpl)
{
   PeerConnMap::iterator it = mPeerConnMap.find(pc);
   if (it != mPeerConnMap.end())
   {
      pimpl = it->second;
      return kSuccess;
   }
   WarningLog(<< "could not find PeerConnectionHandle " << pc << " in map of size " << mPeerConnMap.size());
   return kError;
}

int PeerConnectionManagerInterface::setHandler(PeerConnectionHandle pc, PeerConnectionHandler* handler)
{
   setAppHandler(pc, handler);
   return kSuccess;
}

int PeerConnectionManagerInterface::preinitDtls()
{
#if defined(USE_DTLS)
   if (mFlowManager->getDtlsFactory() == NULL)
   {
      AVMediaConfig mediaConfig;
      resip::SecurityTypes::SSLType tlsVersion = (resip::SecurityTypes::SSLType)mPhone->getSslCipherOptions().getTLSVersion(SslCipherUsageSip);
      resip::BaseSecurity::CipherList tlsCiphers(mPhone->getSslCipherOptions().getCiphers(SslCipherUsageSip).c_str());

      resip::Data certAor = resip::Random::getCryptoRandomBase64(6) + "@local";
      mFlowManager->initializeDtlsFactory(certAor.c_str(), tlsVersion, tlsCiphers.cipherList().c_str(), "");
   }
#endif
   return kSuccess;
}

int PeerConnectionManagerInterface::preinitDns(const cpc::string& natTraversalServerHostname, int natTraversalServerPort)
{
   // use Google DNS as a token "on the public Internet" server which we expect the OS route table to 
   // map to whatever local interface is used for all Internet traffic
   resip::Data ipAddress;
   bool v6 = false;
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), ipAddress);

   if (ipAddress.empty() || resip::isEqualNoCase(ipAddress, "*******"))
   {
      CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("2001:4860:4860::8888", 53, resip::V6), ipAddress);
      v6 = true;
   }

   ResolveHelper* resolveHelper = dynamic_cast<ResolveHelper*>(mNatTraversalServerResolveHelper);
   if (mNatTraversalServerResolveHelper == NULL)
   {
      resolveHelper = new ResolveHelper(mPhone, mFlowManager);
      mNatTraversalServerResolveHelper = resolveHelper;
   }
   resolveHelper->resolve(natTraversalServerHostname.c_str(), natTraversalServerPort, v6);
   return kSuccess;
}

PeerConnectionHandle PeerConnectionManagerInterface::createPeerConnection()
{
   PeerConnectionHandle h = PeerConnectionHandleFactory::getNext();
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::createPeerConnectionImpl, this, h);
   postToSdkThread(rcb);
   return h;
}

void PeerConnectionManagerInterface::createPeerConnection(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::createPeerConnectionImpl, this, pc);
   postToSdkThread(rcb);
}

int PeerConnectionManagerInterface::createPeerConnectionImpl(PeerConnectionHandle pc)
{
   CPCAPI2::Media::MediaManager* mm = MediaManager::getInterface(mPhone);
   CPCAPI2::Media::VideoExt* videoExtIf = CPCAPI2::Media::VideoExt::getInterface(mm);

   MediaManagerInterface* mi = dynamic_cast<MediaManagerInterface*>(mm);
   if (mi)
   {
      webrtc_recon::MediaStackImpl* mediaStack = mi->media_stack();
      if (mediaStack)
      {
         PeerConnectionManagerImpl* peerConnImpl = new PeerConnectionManagerImpl(this, videoExtIf, mPhone->getSdkModuleThread(), mediaStack, mFlowManager, pc);
         mPeerConnMap[pc] = peerConnImpl;
         peerConnImpl->setAudioDeviceCloseDelay(mAudioDeviceCloseDelayMsecs);
         if (mediaStack->mixer())
         {
            mediaStack->mixer()->addRefLocalDevices();
            peerConnImpl->getOA()->setHandler((AVOfferAnswerSession::SdpReady*)peerConnImpl);
            peerConnImpl->getOA()->setHandler((AVOfferAnswerSession::MediaStreamReady*)peerConnImpl);
            peerConnImpl->getOA()->setHandler((AVOfferAnswerSession::MediaStreamUsable*)peerConnImpl);
            DebugLog(<< "PeerConnectionManagerInterface::createPeerConnectionImpl(): successfully added PeerConnectionHandle " << pc << " to map");
         }
      }
      else
      {
         InfoLog(<< "PeerConnectionManagerInterface::createPeerConnectionImpl(): invalid media-stack for PeerConnectionHandle " << pc);
      }
   }
   else
   {
      InfoLog(<< "PeerConnectionManagerInterface::createPeerConnectionImpl(): invalid media-manager-interface for PeerConnectionHandle " << pc);
   }

   return kSuccess;
}

int PeerConnectionManagerInterface::guessLocalIpAddress(resip::Data& outLocalIpAddr)
{
   // use Google DNS as a token "on the public Internet" server which we expect the OS route table to
   // map to whatever local interface is used for all Internet traffic
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), outLocalIpAddr);
   DebugLog(<< "getPreferredLocalIpAddress for IPv4 returns " << outLocalIpAddr);

   if (outLocalIpAddr.empty() || resip::isEqualNoCase(outLocalIpAddr, "*******"))
   {
      CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("2001:4860:4860::8888", 53, resip::V6), outLocalIpAddr);
      DebugLog(<< "getPreferredLocalIpAddress for IPv6 returns " << outLocalIpAddr);
   }

   if (outLocalIpAddr.empty())
   {
      outLocalIpAddr = CPCAPI2::IpHelpers::getAnyLocalIpAddress(resip::V4);

      if (outLocalIpAddr.empty())
      {
         outLocalIpAddr = CPCAPI2::IpHelpers::getAnyLocalIpAddress(resip::V6);
      }
      
      if (outLocalIpAddr.empty())
      {
         WarningLog(<< "Failed to find any local IP address; using localhost");
         outLocalIpAddr = "127.0.0.1";
      }
      else
      {
         WarningLog(<< "Failed to use OS routing to guess local IP address; falling back to " << outLocalIpAddr);
      }
   }
   
   return kSuccess;
}

int PeerConnectionManagerInterface::setDefaultSettings(PeerConnectionHandle pc, const PeerConnectionSettings& settings)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setDefaultSettingsImpl, this, pc, settings);
   postToSdkThread(rcb);
   return kSuccess;
}


int PeerConnectionManagerInterface::setDefaultSettingsImpl(PeerConnectionHandle pc, const PeerConnectionSettings& settings)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      recon::AVSessionConfig avconfig;
      if (settings.localInterface.empty())
      {
         guessLocalIpAddress(avconfig.ipAddress);
      }
      else
      {
         avconfig.ipAddress = settings.localInterface.c_str();
      }
      avconfig.sessionName = (settings.sessionName).c_str();
      avconfig.certAor = (settings.certAor).c_str();
      avconfig.natTraversalMode = settings.natTraversalMode;
      avconfig.natTraversalServerHostname = (settings.natTraversalServerHostname).c_str();
      avconfig.natTraversalServerPort = settings.natTraversalServerPort;
      avconfig.natTraversalServerSupportsTurn = (settings.natTraversalServerType == PeerConnectionSettings::NatTraversalServerType_StunAndTurn || settings.natTraversalServerType == PeerConnectionSettings::NatTraversalServerType_TurnOnly);
      avconfig.natTraversalServerUsername = (settings.natTraversalServerUsername).c_str();
      avconfig.natTraversalServerPassword = (settings.natTraversalServerPassword).c_str();
      avconfig.useUnixDomainSockets = settings.useUnixDomainSockets;
      avconfig.mixId = settings.mixId;
      avconfig.startPaused = settings.startPaused;
      avconfig.mixContribution = settings.mixContribution;
      avconfig.videoCaptureDeviceId = settings.videoCaptureDeviceId;
      avconfig.adaptVideoCodecOnRemotePacketLoss = settings.adaptVideoCodecOnRemotePacketLoss;
      avconfig.isScreenshare = settings.isScreenshare;
      avconfig.videoRenderSurface = settings.defaultVideoRenderSurface;
      avconfig.videoMaxBitrateKbps = settings.videoMaxBitrateKbps;
      avconfig.videoTargetBitrateKbps = settings.videoTargetBitrateKbps;
      avconfig.videoStartBitrateKbps = settings.videoStartBitrateKbps;
      avconfig.videoMinBitrateKbps = settings.videoMinBitrateKbps;
      avconfig.rtpKeepAliveMethod = 1;
      avconfig.rtcpEnabled = settings.rtcpEnabled;
      avconfig.rtcpMux = settings.rtcpMux;
      avconfig.useRandomPortsInIceCandidates = settings.useRandomPortsInIceCandidates;
      avconfig.forcedHostCandidate = settings.forcedHostCandidate.c_str();
      avconfig.dscpAudio = settings.mediaDscp;
      DebugLog(<< "PeerConnectionManagerInterface::setDefaultSettingsImpl(): pc: " << pc << " ip: " << avconfig.ipAddress.c_str() << " dscpAudio: " << avconfig.dscpAudio);
      return pimpl->getOA()->applyConfiguration(avconfig);
   }
   return kError;
}

int PeerConnectionManagerInterface::createOffer(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::createOfferImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::createOfferImpl(PeerConnectionHandle pc)
{
   DebugLog(<< "PeerConnectionManagerInterface::createOfferImpl(): pc " << pc);
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      return pimpl->getOA()->createOffer(false);
   }
   return kError;
}

int PeerConnectionManagerInterface::createAnswer(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::createAnswerImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::createAnswerImpl(PeerConnectionHandle pc)
{
   DebugLog(<< "PeerConnectionManagerInterface::createAnswerImpl(): pc " << pc);
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      return pimpl->getOA()->createAnswer();
   }
   return kError;
}

int PeerConnectionManagerInterface::setLocalDescription(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setLocalDescriptionImpl, this, pc, sdp);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::setLocalDescriptionImpl(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      resip::ParseBuffer pb(sdp.sdpString, sdp.sdpLen);
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      return pimpl->getOA()->setLocalDescription(resipSdp, toReconSdpDisp(sdp.sdpType));
   }
   else
   {
      WarningLog(<< "Couldn't find PeerConnectionManagerImpl for PeerConnectionHandle " << pc);
   }
   return kError;
}

int PeerConnectionManagerInterface::setRemoteDescription(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setRemoteDescriptionImpl, this, pc, sdp);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::setRemoteDescriptionImpl(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      InfoLog(<< "PeerConnectionManagerInterface::setRemoteDescription(" << pc << ")");
      InfoLog(<< resip::Data(sdp.sdpString, sdp.sdpLen));
      resip::ParseBuffer pb(sdp.sdpString, sdp.sdpLen);
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      return pimpl->getOA()->setRemoteDescription(resipSdp, toReconSdpDisp(sdp.sdpType));
   }
   return kError;
}

AVOfferAnswerSession::MediaDirection toMediaDir(CPCAPI2::PeerConnection::MediaDirection mediaDir)
{
   if (mediaDir == MediaDirection_SendRecv)
      return AVOfferAnswerSession::MediaDirection_SendRecv;
   else if (mediaDir == MediaDirection_SendOnly)
      return AVOfferAnswerSession::MediaDirection_SendOnly;
   else if (mediaDir == MediaDirection_RecvOnly)
      return AVOfferAnswerSession::MediaDirection_RecvOnly;
   else if (mediaDir == MediaDirection_Inactive)
      return AVOfferAnswerSession::MediaDirection_Inactive;
   return AVOfferAnswerSession::MediaDirection_None;
}

int PeerConnectionManagerInterface::close(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::closeImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::closeImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->stopLoggingMediaStatistics();
      pimpl->getOA()->close();

      CPCAPI2::Media::MediaManager* mm = MediaManager::getInterface(mPhone);
      webrtc_recon::MediaStackImpl* mediaStack = dynamic_cast<MediaManagerInterface*>(mm)->media_stack();
      mediaStack->mixer()->removeRefLocalDevices();

      mPeerConnMap.erase(pc);
      delete pimpl;

      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::addAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::addAdditionalDestinationImpl, this, pc, sdp);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::addAdditionalDestinationImpl(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      resip::ParseBuffer pb(sdp.sdpString, sdp.sdpLen);
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      pimpl->getOA()->addAdditionalDestination(resipSdp);
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::removeAdditionalDestination(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::removeAdditionalDestinationImpl, this, pc, sdp);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::removeAdditionalDestinationImpl(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      resip::ParseBuffer pb(sdp.sdpString, sdp.sdpLen);
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      pimpl->getOA()->removeAdditionalDestination(resipSdp);
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::startMediaInactivityMonitor(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::startMediaInactivityMonitorImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::startMediaInactivityMonitorImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->startIncomingMediaInactivityMonitor();
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::stopMediaInactivityMonitor(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::stopMediaInactivityMonitorImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::stopMediaInactivityMonitorImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->stopIncomingMediaInactivityMonitor();
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::pauseTxMedia(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::pauseTxMediaImpl, this, pc, sdp);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::pauseTxMediaImpl(PeerConnectionHandle pc, const SessionDescription& sdp)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      resip::ParseBuffer pb(sdp.sdpString, sdp.sdpLen);
      resip::SdpContents resipSdp;
      resipSdp.parse(pb);
      pimpl->getOA()->pauseTxMedia(resipSdp);
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::startLoggingMediaStatistics(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::startLoggingMediaStatisticsImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::startLoggingMediaStatisticsImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->startLoggingMediaStatistics();
      return kSuccess;
   }
   return kError;
}

int PeerConnectionManagerInterface::stopLoggingMediaStatistics(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::stopLoggingMediaStatisticsImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::stopLoggingMediaStatisticsImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->stopLoggingMediaStatistics();
      return kSuccess;
   }
   return kError;
}

MediaStreamHandle PeerConnectionManagerInterface::createMediaStream()
{
   MediaStreamHandle ms = MediaStreamHandleFactory::getNext();
   return ms;
}

int PeerConnectionManagerInterface::configureMedia(PeerConnectionHandle pc, MediaStreamHandle mediaStream, const CPCAPI2::PeerConnection::MediaInfo& mediaDescriptor)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::configureMediaImpl, this, pc, mediaStream, mediaDescriptor);
   postToSdkThread(rcb);
   return kSuccess;
}

recon::AVSecureMediaMode convertMediaEncryptionMode(CPCAPI2::PeerConnection::MediaEncryptionMode mediaEncryptionMode)
{
   switch (mediaEncryptionMode)
   {
   case CPCAPI2::PeerConnection::MediaEncryptionMode_SRTP_SDES_Encrypted:
      return recon::AVSecureMediaMode_Srtp;
   case CPCAPI2::PeerConnection::MediaEncryptionMode_SRTP_DTLS_Encrypted:
      return recon::AVSecureMediaMode_SrtpDtls;
   case CPCAPI2::PeerConnection::MediaEncryptionMode_Unencrypted:
      FALL_THROUGH;
   default:
      return recon::AVSecureMediaMode_NoSecureMedia;
   }
}

recon::AVSecureMediaCryptoSuite convertMediaCryptoSuite(CPCAPI2::PeerConnection::MediaCryptoSuite mediaCryptoSuite)
{
   switch (mediaCryptoSuite)
   {
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_128_HMAC_SHA1_32:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_128_HMAC_SHA1_32;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_128_HMAC_SHA1_80:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_128_HMAC_SHA1_80;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_256_HMAC_SHA1_32:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_256_HMAC_SHA1_32;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_256_HMAC_SHA1_80:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_256_HMAC_SHA1_80;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_192_HMAC_SHA1_32:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_192_HMAC_SHA1_32;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AES_CM_192_HMAC_SHA1_80:
      return recon::AVSecureMediaCryptoSuite_SRTP_AES_CM_192_HMAC_SHA1_80;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AEAD_AES_128_GCM:
      return recon::AVSecureMediaCryptoSuite_SRTP_AEAD_AES_128_GCM;
   case CPCAPI2::PeerConnection::MediaCryptoSuite_AEAD_AES_256_GCM:
      return recon::AVSecureMediaCryptoSuite_SRTP_AEAD_AES_256_GCM;
   default:
      return AVSecureMediaCryptoSuite_SRTP_CRYPTO_SUITE_INVALID;
   }
}

int PeerConnectionManagerInterface::configureMediaImpl(PeerConnectionHandle pc, MediaStreamHandle mediaStream, const CPCAPI2::PeerConnection::MediaInfo& mediaDescriptor)
{
   DebugLog(<< "configureMediaImpl: pc: " << pc << " media: " << mediaDescriptor.mediaType << ", " << mediaStream);
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      resip::Data label = resip::Data::from(mediaStream);
      AVMediaConfig mediaConfig;
      mediaConfig.secureMediaMode = convertMediaEncryptionMode(mediaDescriptor.mediaEncryptionOptions.mediaEncryptionMode);
      mediaConfig.secureMediaRequired = mediaDescriptor.mediaEncryptionOptions.secureMediaRequired;
      mediaConfig.sdesCryptoSuite = convertMediaCryptoSuite(mediaDescriptor.mediaEncryptionOptions.mediaCryptoSuite);
      mediaConfig.tlsVersion = (resip::SecurityTypes::SSLType)mPhone->getSslCipherOptions().getTLSVersion(SslCipherUsageSip);
      mediaConfig.tlsCiphers = resip::BaseSecurity::CipherList(mPhone->getSslCipherOptions().getCiphers(SslCipherUsageSip).c_str());
      mediaConfig.tlsDHParamsFile = "";

      for (cpc::vector<MediaCodec>::const_iterator it = mediaDescriptor.codecs.begin(); it != mediaDescriptor.codecs.end(); ++it)
      {
         recon::AVCodec codec;
         codec.plname = it->codecPayloadName.c_str();
         codec.plfreq = it->codecFrequency;
         mediaConfig.codecs.push_back(codec);
      }

      if (mediaDescriptor.mediaType == PeerConnection::MediaType_Audio)
      {
         if (pimpl->getOA()->configureAudioStream(label, toMediaDir(mediaDescriptor.mediaDirection), mediaConfig) != 0)
         {
            ErrLog(<< "failed to configureMedia (audio)");
            return kError;
         }
      }
      else if (mediaDescriptor.mediaType == PeerConnection::MediaType_Video)
      {
         if (pimpl->getOA()->configureVideoStream(label, toMediaDir(mediaDescriptor.mediaDirection), mediaConfig) != 0)
         {
            ErrLog(<< "failed to configureMedia (video)");
            return kError;
         }
      }
      else
      {
         assert(0); // not yet supported
         return kError;
      }
      CPCAPI2::PeerConnection::MediaInfo mi(mediaDescriptor);
      mi.mediaStream = mediaStream;
      pimpl->configureMediaStream(mi);
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::startWebVideoServerForMediaStream(PeerConnectionHandle pc, MediaStreamHandle mediaStream)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::startWebVideoServerForMediaStreamImpl, this, pc, mediaStream);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::startWebVideoServerForMediaStreamImpl(PeerConnectionHandle pc, MediaStreamHandle mediaStream)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->startWebVideoServerForMediaStream(mediaStream);
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::setMediaInactivityTimeoutMsecs(PeerConnectionHandle pc, int msecs)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setMediaInactivityTimeoutMsecsImpl, this, pc, msecs);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::setMediaInactivityTimeoutMsecsImpl(PeerConnectionHandle pc, int msecs)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (pc == 0)
   {
      for (auto it = mPeerConnMap.begin(); it != mPeerConnMap.end(); ++it)
      {
         it->second->setAudioDeviceCloseDelay(msecs);
      }
   }
   else
   {
      if (getImpl(pc, pimpl) == 0)
      {
         pimpl->setMediaInactivityTimeoutMsecs(msecs);
      }
   }

   return kSuccess;
}

int PeerConnectionManagerInterface::setLoggingMediaStatisticsFrequencyMsecs(PeerConnectionHandle pc, int msecs)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setLoggingMediaStatisticsFrequencyMsecsImpl, this, pc, msecs);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::setLoggingMediaStatisticsFrequencyMsecsImpl(PeerConnectionHandle pc, int msecs)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->setLoggingMediaStatisticsFrequencyMsecs(msecs);
   }

   return kSuccess;
}

int PeerConnectionManagerInterface::queryMediaStatistics(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::queryMediaStatisticsImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::queryMediaStatisticsImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->queryMediaStatistics();
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::logMediaStatistics(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::logMediaStatisticsImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::logMediaStatisticsImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->logMediaStatistics();
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::setAudioDeviceCloseDelay(int msecs)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::setAudioDeviceCloseDelayImpl, this, msecs);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::setAudioDeviceCloseDelayImpl(int msecs)
{
   mAudioDeviceCloseDelayMsecs = msecs;
   for (auto it = mPeerConnMap.begin(); it != mPeerConnMap.end(); ++it)
   {
      it->second->setAudioDeviceCloseDelay(msecs);
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::pauseAudioPlayout(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::pauseAudioPlayoutImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::pauseAudioPlayoutImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->pauseAudioPlayout();
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::resumeAudioPlayout(PeerConnectionHandle pc)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::resumeAudioPlayoutImpl, this, pc);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::resumeAudioPlayoutImpl(PeerConnectionHandle pc)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->resumeAudioPlayout();
   }
   return kSuccess;
}

int PeerConnectionManagerInterface::playSound(PeerConnectionHandle pc, const cpc::string& mediaUri, bool repeat)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PeerConnectionManagerInterface::playSoundImpl, this, pc, mediaUri, repeat);
   postToSdkThread(rcb);
   return kSuccess;
}

int PeerConnectionManagerInterface::playSoundImpl(PeerConnectionHandle pc, const cpc::string& mediaUri, bool repeat)
{
   PeerConnectionManagerImpl* pimpl = NULL;
   if (getImpl(pc, pimpl) == 0)
   {
      pimpl->playSound(mediaUri, repeat);
   }
   return kSuccess;
}

}
}

#endif
