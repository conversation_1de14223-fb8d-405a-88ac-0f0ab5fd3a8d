#include "brand_branded.h"

#include "interface/public/cpcstl/string.h"
#include "interface/experimental/xmpp_agent/XmppAgentManager.h"

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1)
#include "XmppAgentManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

namespace XmppAgent
{

XmppAgentManager* XmppAgentManager::getInterface(Phone* cpcPhone)
{
   if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XmppAgentManagerInterface>(phone, "XmppAgentManagerInterface");
#else
   return NULL;
#endif
}

const cpc::string& XmppAgentManager::getServiceId()
{
   static cpc::string serviceId = "xmppagent";
   return serviceId;
}

}

}
