#pragma once

#if !defined(CPCAPI2_XMPP_AGENT_MANAGER_INTERFACE_H)
#define CPCAPI2_XMPP_AGENT_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "xmpp_agent/XmppAgentManager.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentInternal.h"
#include "xmpp/XmppChatHandler.h"
#include "xmpp/XmppVCardHandler.h"
#include "xmpp/XmppAccountHandlerInternal.h"
#include "xmpp/XmppAccountSyncHandler.h"
#include "push_service/PushNotificationServiceManager.h"
#include "push_service/PushNotificationServiceManagerInternal.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "push_endpoint/PushNotificationEndpointTypes.h"
#include "jsonapi/JsonApiServerHandler.h"
#include "remotesync/RemoteSyncManager.h"
#include "remotesync/RemoteSyncHandler.h"
#include "remotesync/RemoteSyncInternalHandlerTypes.h"
#include "XmppAgentSyncHandler.h"
#include "xmpp/XmppChatSyncHandler.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "../util/STAAssertion.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <set>
#include <map>
#include <atomic>

namespace CPCAPI2
{
class PhoneInterface;
class LocalLogger;

namespace JsonApi
{
   class JsonApiServerSendTransportInternal;
}

namespace XmppAgent
{
class XmppAgentManagerInterface : public XmppAgentManagerInternal,
                                  public PhoneModule,
                                  public CPCAPI2::XmppChat::XmppChatHandler,
                                  public CPCAPI2::XmppChat::XmppChatSyncHandler,
                                  public CPCAPI2::XmppVCard::XmppVCardHandler,
                                  public CPCAPI2::RemoteSync::RemoteSyncHandler,
                                  public CPCAPI2::RemoteSync::RemoteSyncAsyncHandler,
                                  public CPCAPI2::XmppAccount::XmppAccountHandlerInternal,
                                  public CPCAPI2::XmppAccount::XmppAccountSyncHandler,
                                  public CPCAPI2::PushService::PushNotificationServiceHandler,
                                  public CPCAPI2::JsonApi::JsonApiServerHandler,
                                  public resip::DeadlineTimerHandler
#ifdef CPCAPI2_AUTO_TEST
                                , public AutoTestProcessor
#endif
{
public:

   struct XmppAgentState
   {
      XmppPushRegistrationInfo registrationInfo;
      CPCAPI2::JsonApi::AuthToken authToken;
      bool isRegisteredForPush;
      bool isLoggedOut;
      XmppAgentHandler* appHandler;
      cpc::vector<CPCAPI2::XmppAgent::XmppChatEvent> events;
      uint64_t serviceDownAtMs; // wall clock time

      XmppAgentState()
      {
         registrationInfo.xmppAccountHandle = -1;
         isRegisteredForPush = false;
         isLoggedOut = false;
         appHandler = NULL;
         serviceDownAtMs = 0;
      }
   };

   enum RemoteSyncConnectionState
   {
      RemoteSyncConnectionState_Disconnected,
      RemoteSyncConnectionState_Connecting,
      RemoteSyncConnectionState_Connected,
      RemoteSyncConnectionState_Disconnecting
   };

   enum RemoteSyncHandlingState
   {
      RemoteSyncHandlingState_Init,
      RemoteSyncHandlingState_Started,
      RemoteSyncHandlingState_GetMessageCounts,
      RemoteSyncHandlingState_FetchRange,
      RemoteSyncHandlingState_FetchConversations,
      RemoteSyncHandlingState_Completed
   };

   friend std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::XmppAgentState& state);
   friend std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::RemoteSyncConnectionState& state);
   friend std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::RemoteSyncHandlingState& state);

   XmppAgentManagerInterface(Phone* phone);
   virtual ~XmppAgentManagerInterface();

   // PhoneModule
   virtual void PreRelease() OVERRIDE;
   virtual bool PreReleaseCompleted() OVERRIDE;
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void interruptProcess() OVERRIDE;

   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

   // XmppAgentManagerInternal
   // static XmppAgentManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void addSdkObserver(XmppAgentHandler* observer) OVERRIDE;
   virtual void removeSdkObserver(XmppAgentHandler* observer) OVERRIDE;
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;

   // XmppAgentManager
   virtual int setPushNotificationManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager) OVERRIDE;
   virtual int setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonServer) OVERRIDE;
   virtual int setHandler(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler) OVERRIDE;
   virtual XmppPushRegistrationHandle createXmppPushRegistration() OVERRIDE;
   virtual XmppPushRegistrationHandle createXmppPushRegistration(const CPCAPI2::JsonApi::AuthToken& authToken) OVERRIDE;
   virtual int registerForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo) OVERRIDE;
   virtual int unregisterForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int requestEventHistory(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int registerForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings) OVERRIDE;
   virtual int unregisterForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int logout(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int logoutAccount(XmppAccount::XmppAccountHandle account) OVERRIDE;
   virtual int queryXmppRegistrationList() OVERRIDE;
   virtual int queryXmppRegistrationInfo(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;

   int createXmppPushRegistration(XmppPushRegistrationHandle xmppPushRegistration);
   int createXmppPushRegistration(XmppPushRegistrationHandle xmppPushRegistration, const CPCAPI2::JsonApi::AuthToken& authToken);

   // XmppAccountHandler(Internal)
   int onAccountStatusChanged(XmppAccount::XmppAccountHandle account, const XmppAccount::XmppAccountStatusChangedEvent& args) OVERRIDE;
   int onError(XmppAccount::XmppAccountHandle account, const XmppAccount::ErrorEvent& args) OVERRIDE { return kSuccess; }
   int onEntityTime(XmppAccount::XmppAccountHandle account, const XmppAccount::EntityTimeEvent& args) OVERRIDE { return kSuccess; }
   int onEntityFeature(XmppAccount::XmppAccountHandle account, const XmppAccount::EntityFeatureEvent& args) OVERRIDE { return kSuccess; }
   int onAccountConfigured(XmppAccount::XmppAccountHandle account, const XmppAccount::XmppAccountConfiguredEvent& args) OVERRIDE { return kSuccess; }

   // XmppChatHandler
   virtual int onNewChat(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent& args) OVERRIDE;
   virtual int onNewMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args) OVERRIDE;
   virtual int onNewReaction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewReactionEvent& args) OVERRIDE { return kSuccess; }
   virtual int onNewMessageRetraction(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageRetractionEvent& args) OVERRIDE { return kSuccess; }
   virtual int onNewOutboundMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args) OVERRIDE;
   virtual int onChatEnded(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent& args) OVERRIDE;
   virtual int onIsComposingMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::IsComposingMessageEvent& args) OVERRIDE { return kSuccess; }
   virtual int onSendMessageSuccess(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageSuccessEvent& args) OVERRIDE { return kSuccess; }
   virtual int onSendMessageFailure(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::SendMessageFailureEvent& args) OVERRIDE { return kSuccess; }
   virtual int onMessageDelivered(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveredEvent& args) OVERRIDE { return kSuccess; }
   virtual int onMessageDeliveryError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDeliveryErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onMessageDisplayed(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::MessageDisplayedEvent& args) OVERRIDE { return kSuccess; }
   virtual int onValidateChatHandleResult(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ValidateChatHandleEvent& event) OVERRIDE { return kSuccess; }
   virtual int onChatDiscoCompleted(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatDiscoEvent& args) OVERRIDE { return kSuccess; }
   virtual int onChatDiscoError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatDiscoErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onError(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ErrorEvent& event) OVERRIDE { return kSuccess; }

   // XmppVCardHandler
   virtual int onVCardFetched(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt) OVERRIDE;
   virtual int onVCardOperationResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt) OVERRIDE;
   virtual int onError(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt) OVERRIDE;

   // RemoteSyncHandler
   virtual int onSetAccounts(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SetAccountsEvent& evt) OVERRIDE;
   virtual int onNotificationUpdate(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::NotificationUpdateEvent& evt) OVERRIDE;
   virtual int onMessageReactions(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageReactionsEvent& evt) OVERRIDE;
   virtual int onSyncItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& evt) OVERRIDE;
   virtual int onUpdateItemComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& evt) OVERRIDE;
   virtual int onFetchRangeComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& evt) OVERRIDE;
   virtual int onFetchConversationsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& evt) OVERRIDE;
   virtual int onConversationUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& evt) OVERRIDE;
   virtual int onMessageCount(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageCountEvent& evt) OVERRIDE;
   virtual int onError(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnErrorEvent& evt) OVERRIDE;
   virtual int onConnectionState(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnConnectionStateEvent& evt) OVERRIDE;
   virtual int onTimestampDelta(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& evt) OVERRIDE;
   virtual int onUpdateItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemsCompleteEvent& evt) OVERRIDE;
   virtual int onItemsUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ItemsUpdatedEvent& evt) OVERRIDE;

   // RemoteSyncAsyncHandler
   virtual void onEvent(resip::ReadCallbackBase* rcb) OVERRIDE;

   // PushNotificationServiceHandler
   virtual int onNotificationSuccess(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationSuccessEvent& evt) OVERRIDE;
   virtual int onNotificationFailure(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationFailureEvent& evt) OVERRIDE;
   virtual int onNotificationServiceRegistration(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& evt) OVERRIDE;

   // JsonServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args) OVERRIDE;
   //virtual int onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args) OVERRIDE;
   //virtual int onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args) OVERRIDE;
   virtual int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) OVERRIDE;
   //virtual int onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::SessionStateEvent& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   typedef struct RemoteSyncItemInfo
   {
      RemoteSyncItemInfo(CPCAPI2::RemoteSync::RemoteSyncItem* syncItem_, bool pushSent_) : syncItem(syncItem_), pushSent(pushSent_) {}
      CPCAPI2::RemoteSync::RemoteSyncItem* syncItem;
      bool pushSent;
   } RemoteSyncItemInfo;

   typedef std::map<cpc::string, RemoteSyncItemInfo*> RemoteSyncItemMap;

   // XmppChatHandler Implementation
   int onNewChatImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent& args);
   int onNewMessageImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args);
   int onNewOutboundMessageImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args);
   int onChatEndedImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent& args);

   // XmppVCardHandler Implementation
   int onVCardFetchedImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt);
   int onVCardOperationResultImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt);
   int onErrorImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt);

   void createXmppPushRegistrationImpl2(XmppPushRegistrationHandle xmppPushRegistration);
   void createXmppPushRegistrationImpl(XmppPushRegistrationHandle xmppPushRegistration, const CPCAPI2::JsonApi::AuthToken& authToken);
   void setPushNotificationManagerImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushManager);
   void setJsonApiServerImpl(CPCAPI2::JsonApi::JsonApiServer* jsonServer);
   void setHandlerImpl(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler);
   void registerForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo);
   void unregisterForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void requestEventHistoryImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void registerForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings);
   void unregisterForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void logoutImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void logoutAccountImpl(XmppAccount::XmppAccountHandle account);
   void queryXmppRegistrationListImpl();
   void queryXmppRegistrationInfoImpl(XmppPushRegistrationHandle xmppPushRegistration);

   // PushNotificationServiceHandler Implementation
   void onNotificationSuccessImpl(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationSuccessEvent& evt);
   void onNotificationFailureImpl(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationFailureEvent& evt);
   void onNotificationServiceRegistrationImpl(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& evt);

   // JsonServerHandler Implementation
   void onNewLoginImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args);
   //void onReLoginImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args);
   //void onPreLogoutImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args);
   void onLogoutImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args);
   //void onSessionStateImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::SessionStateEvent& args);

   bool findXmppPushRegHandleImpl(const XmppPushRegistrationInfo& registrationInfo, XmppPushRegistrationHandle& xmppPushRegistration);
   bool getXmppPushRegHandle(
      const XmppPushRegistrationInfo& xmppPushRegistrationInfo,
      CPCAPI2::PushEndpoint::PushNotificationEndpointId& pushNotificationDeviceHandle,
      CPCAPI2::XmppAgent::XmppPushRegistrationHandle& xmppPushRegistration) const;
   void addToXmppPushRegHandleList(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentState* agentState);
   void removeFromXmppPushRegHandleList(const XmppPushRegistrationInfo& xmppPushRegistrationInfo);

   bool sendPushNotification(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args);
   bool clearPushNotification(XmppAccount::XmppAccountHandle account, const CPCAPI2::PushService::CustomData& customData);
   bool addXmppRegistrationToAccountMapping(XmppAccount::XmppAccountHandle xmppAccountHandle, XmppPushRegistrationHandle xmppPushRegistration);
   bool addXmppRegistrationToJsonUserMapping(JsonApi::JsonApiUserHandle jsonUserHandle, XmppPushRegistrationHandle xmppPushRegistration);
   bool addXmppRegistrationToPushEndpointMapping(PushEndpoint::PushNotificationEndpointId pushEndpointId, XmppPushRegistrationHandle xmppPushRegistration);
   bool removeXmppRegistrationFromAccountMapping(XmppAccount::XmppAccountHandle xmppAccountHandle, XmppPushRegistrationHandle xmppPushRegistration);
   bool removeXmppRegistrationFromJsonUserMapping(JsonApi::JsonApiUserHandle jsonUserHandle, XmppPushRegistrationHandle xmppPushRegistration);
   bool removeXmppRegistrationFromPushEndpointMapping(PushEndpoint::PushNotificationEndpointId pushEndpointId, XmppPushRegistrationHandle xmppPushRegistration);
   bool deleteXmppAgentState(XmppPushRegistrationHandle xmppPushRegistration);
   bool sendRemoteSyncItem(const CPCAPI2::XmppChat::NewMessageEvent& args);
   void startSyncTimer(uint32_t timeoutMs);
   void stopSyncTimer();
   void startLogoutCleanupTimer(uint32_t timeoutMs);
   void stopLogoutCleanupTimer();
   void addToSyncDelayList(const CPCAPI2::XmppChat::NewMessageEvent& args);
   void removeFromSyncDelayList(const cpc::vector<RemoteSync::RemoteSyncItem>& syncItems);
   bool isInSyncDelayList(const RemoteSync::RemoteSyncItem& syncItem);
   cpc::string getUniqueIdFromSyncItemDelayList(const RemoteSync::RemoteSyncItem& item);
   bool isSyncItemSame(const CPCAPI2::XmppChat::NewMessageEvent& newMessage, const RemoteSync::RemoteSyncItem& item);
   bool isSyncItemSame(const RemoteSync::RemoteSyncItem& item1, const RemoteSync::RemoteSyncItem& item2);

   // Utility functions for Remote-Sync
   cpc::string getConversationID(const CPCAPI2::XmppChat::NewMessageEvent& args);
   cpc::string getConversationThreadID(const CPCAPI2::XmppChat::NewMessageEvent& args);
   bool getRemoteSyncItemByUniqueID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& uniqueID, RemoteSync::RemoteSyncItem*& item) const;
   bool getRemoteSyncItemByServerID(XmppAccount::XmppAccountHandle accountHandle, RemoteSync::ServerID serverID, RemoteSync::RemoteSyncItem*& item) const;
   bool getRemoteSyncItemByClientID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& clientID, RemoteSync::RemoteSyncItem*& item) const;
   bool getRemoteSyncItemInfoByUniqueID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& uniqueID, RemoteSyncItemInfo*& item) const;
   bool getRemoteSyncItemInfoByServerID(XmppAccount::XmppAccountHandle accountHandle, RemoteSync::ServerID serverID, RemoteSyncItemInfo*& item) const;
   bool getRemoteSyncItemInfoByClientID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& clientID, RemoteSyncItemInfo*& item) const;
   XmppAccount::XmppAccountHandle getAccountHandleFromRequestHandle(RemoteSync::RequestHandle requestHandle);
   XmppAccount::XmppAccountHandle getAccountHandleFromConversationId(cpc::string conversationID);
   cpc::string getNewUniqueID(const std::string messageID, const std::string oldUniqueID);
   void getChatMessageIdThreadId(const cpc::string syncUniqueId, cpc::string& chatMessageId, cpc::string& chatThreadId);
   void addRemoteSyncItem(const RemoteSync::RemoteSyncItem& item, bool pushSent = false);
   void printRemoteSyncItems();

   template<typename TFn, typename TEvt> void fireEvent(XmppAgentHandler* appHandler, const char* funcName, TFn func, XmppPushRegistrationHandle h, const TEvt& args, bool internalOnly = false, bool logging = true)
   {
      for (std::set<XmppAgentHandler*>::iterator itHandler = mSdkObservers.begin(); itHandler != mSdkObservers.end(); ++itHandler)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itHandler, h, args);
         if (dynamic_cast<XmppAgentSyncHandler*>(*itHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      // if (logging)
      //   logEvent(funcName, h);

      if (!internalOnly)
      {
         resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, appHandler, h, args);
         if (appHandler != (void*)0xDEADBEEF && dynamic_cast<XmppAgentSyncHandler*>(appHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
   }

   template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, const TEvt& args)
   {
      for (std::set<XmppAgentHandler*>::iterator itHandler = mSdkObservers.begin(); itHandler != mSdkObservers.end(); ++itHandler)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itHandler, args);
         if (dynamic_cast<XmppAgentSyncHandler*>(*itHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
   }

private:

   bool mShutdown;
   bool mPreRelease;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;

   // NOTE: this NOT from this SDK instance! i.e. it has a different Phone* because a single PushNotificationManager is shared among multiple SDK instances
   CPCAPI2::PushService::PushNotificationServiceManager* mPushManager;

   // NOTE: this NOT from this SDK instance! i.e. it has a different Phone* because a single JsonApiServer is shared among multiple SDK instances
   CPCAPI2::JsonApi::JsonApiServer* mJsonServer;
   CPCAPI2::JsonApi::JsonApiServerSendTransportInternal* mJsonApiServerSendTransport;

   typedef std::map<XmppPushRegistrationHandle, XmppAgentState*> XmppAgentStateMap;
   XmppAgentStateMap mStateMap;

   resip::DeadlineTimer<resip::MultiReactor> mAccountServiceNotificationTimer;

   // Each xmpp-account handle can have multiple xmpp-push-registration handles, as the same account can register from different devices.
   // Each push-device handle can have multiple xmpp-push-registration handles, as the same device can register for different accounts.
   // Each push-device handle will be mapped to a single device token.
   // Each xmpp-push-registration handle will be mapped to a single combination of an xmpp-account handle and a push-device handle.
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, cpc::vector<XmppPushRegistrationHandle>> AccountPushRegMap;
   AccountPushRegMap mAccountPushRegMap;

   typedef std::map<CPCAPI2::JsonApi::JsonApiUserHandle, cpc::vector<XmppPushRegistrationHandle>> JsonUserPushRegMap;
   JsonUserPushRegMap mJsonUserPushRegMap;

   typedef std::map<CPCAPI2::PushEndpoint::PushNotificationEndpointId, cpc::vector<XmppPushRegistrationHandle>> PushEndpointRegMap;
   PushEndpointRegMap mPushEndpointRegMap;

   typedef std::set<CPCAPI2::JsonApi::JsonApiUserHandle> JsonUserList;
   JsonUserList mLoggedOutJsonUsers;
   resip::DeadlineTimer<resip::MultiReactor> mLogoutCleanupTimer;

   // A single remote-sync session exists for each json login context, i.e. per SDK instance
   CPCAPI2::RemoteSync::SessionHandle mSyncSessionHandle;
   CPCAPI2::RemoteSync::RemoteSyncSettings mSyncSettings;
   CPCAPI2::RemoteSync::RequestHandle mSyncRequestHandle;
   CPCAPI2::RemoteSync::Revision mSyncRevision;
   RemoteSyncConnectionState mSyncConnectionState;
   RemoteSyncHandlingState mSyncHandlingState;

   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, RemoteSyncItemMap> RemoteSyncAccountItemMap;
   RemoteSyncAccountItemMap mAccountSyncItems;

   /**
    * Per-account state information including the current 'handling state'
    * as well as information pertaining to message count etc.
    */
   struct RemoteSyncContext
   {
      RemoteSyncHandlingState syncHandlingState;
      int messageCount;
      int unreadCount;
      int unreadConversations;
      int totalConversations;
      int fetchOffset;
      int64_t highestClientCreatedTime;
      std::set< CPCAPI2::RemoteSync::RequestHandle > requestHandles;
   };
   
   /**
    * The purpose of this map is to track the progress of the xmpp account through the
    * process of completing synchronization with the remotesync server.
    */
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, RemoteSyncContext> RemoteSyncAccountStateMap;
   RemoteSyncAccountStateMap mAccountSyncStates;

   struct SyncDelayInfo
   {
      SyncDelayInfo(uint64_t rxTimeMs, const CPCAPI2::XmppChat::NewMessageEvent& args) : receivedTimestampMsecs(rxTimeMs), newMessageEvent(args) {}
      uint64_t receivedTimestampMsecs;
      const CPCAPI2::XmppChat::NewMessageEvent newMessageEvent;
   };
   typedef std::vector<SyncDelayInfo*> SyncDelayList;
   SyncDelayList mSyncDelayList;

   resip::DeadlineTimer<resip::MultiReactor> mSyncDelayTimer;

   std::set<XmppAgentHandler*> mSdkObservers;
   std::function<void(void)> mCbHook;

   LocalLogger* mLocalLogger;
   STAAssertion mThreadCheck;

};

class XmppPushRegistrationHandleFactory
{

public:

   static XmppPushRegistrationHandle getNext() { return sNextHandle++; }

private:

   static std::atomic<XmppPushRegistrationHandle> sNextHandle;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& info);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppChatEventType& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppChatEvent& event);
std::ostream& operator<<(std::ostream& os, const cpc::vector<CPCAPI2::XmppAgent::XmppChatEvent>& events);

}

}

#endif // CPCAPI2_XMPP_AGENT_MANAGER_INTERFACE_H
