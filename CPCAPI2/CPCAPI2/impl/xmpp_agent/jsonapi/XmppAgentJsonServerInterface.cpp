#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppAgentJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include "xmpp_agent/XmppAgentManagerInterface.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define JSON_MODULE "XmppAgentManagerJsonProxy"

using namespace CPCAPI2::RemoteSync;

namespace CPCAPI2
{

namespace XmppAgent
{

XmppAgentJsonServerInterface::XmppAgentJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mFunctionMap["createXmppPushRegistration"] = std::bind(&XmppAgentJsonServerInterface::handleCreateXmppPushRegistration, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["registerForXmppPushNotifications"] = std::bind(&XmppAgentJsonServerInterface::handleRegisterForXmppPushNotifications, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["unregisterForXmppPushNotifications"] = std::bind(&XmppAgentJsonServerInterface::handleUnregisterForXmppPushNotifications, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["requestEventHistory"] = std::bind(&XmppAgentJsonServerInterface::handleRequestEventHistory, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["registerForRemoteSync"] = std::bind(&XmppAgentJsonServerInterface::handleRegisterForRemoteSync, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["unregisterForRemoteSync"] = std::bind(&XmppAgentJsonServerInterface::handleUnregisterForRemoteSync, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["logout"] = std::bind(&XmppAgentJsonServerInterface::handleLogout, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["logoutAccount"] = std::bind(&XmppAgentJsonServerInterface::handleLogoutAccount, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

XmppAgentJsonServerInterface::~XmppAgentJsonServerInterface()
{
   if (XmppAgentManager* xmppAgentManager = XmppAgentManager::getInterface(mPhone))
   {
      for (MapConnToPushRegHandle::const_iterator it = mMapConnToPushRegHandle.begin(); it != mMapConnToPushRegHandle.end(); ++it)
      {
         xmppAgentManager->setHandler(it->second, NULL);
      }
   }
   mMapConnToPushRegHandle.clear();
   mMapAccountToPushRegHandle.clear();
}

void XmppAgentJsonServerInterface::Release()
{
}

void XmppAgentJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppAgentJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int XmppAgentJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppAgentJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void XmppAgentJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "XmppAgentJsonServerInterface::processIncomingImpl()");
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int XmppAgentJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   StackLog(<< "XmppAgentJsonServerInterface::handleConnectionClosed()");
   post(resip::resip_bind(&XmppAgentJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void XmppAgentJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
#if 0
   MapConnToPushRegHandle::const_iterator it = mMapConnToPushRegHandle.find(conn);
   if (it != mMapConnToPushRegHandle.end())
   {
      XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
      XmppPushRegistrationInfo xmppPushRegistrationInfo;
      xmppAgentManager->registerForXmppPushNotifications(it->second, xmppPushRegistrationInfo);
   }
#endif
}

int XmppAgentJsonServerInterface::handleCreateXmppPushRegistration(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppAgentJsonServerInterface::handleCreateXmppPushRegistration()");

   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));

   XmppPushRegistrationHandle h = 0;
   if (functionObjectVal.HasMember("xmppPushRegistration"))
   {
      JsonDeserialize(functionObjectVal, "xmppPushRegistration", h);
      if (functionObjectVal.HasMember("authToken"))
      {
         CPCAPI2::JsonApi::AuthToken authToken;
         JsonDeserialize(functionObjectVal, "authToken", authToken.authToken);
         xmppAgentManager->createXmppPushRegistration(h, authToken);
      }
      else
      {
         xmppAgentManager->createXmppPushRegistration(h);
      }
   }
   return kSuccess;
}

int XmppAgentJsonServerInterface::handleRegisterForXmppPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppAgentJsonServerInterface::handleRegisterForXmppPushNotifications()");

   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));

   uint64_t xmppPushRegistration = 0;
   XmppPushRegistrationInfo xmppPushRegistrationInfo;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), JSON_VALUE(xmppPushRegistrationInfo));

   if (!conn.authToken.cp_user.empty() && !conn.authToken.device_uuid.empty())
   {
      xmppPushRegistrationInfo.pushNotificationDev = conn.authToken.cp_user + ":" + conn.authToken.device_uuid;
   }
   xmppPushRegistrationInfo.jsonUserHandle = conn.jsonUserHandle;

   xmppAgentManager->registerForXmppPushNotifications(xmppPushRegistration, xmppPushRegistrationInfo);

   xmppAgentManager->addSdkObserver(this);
   mMapConnToPushRegHandle[conn] = xmppPushRegistration;
   mMapAccountToPushRegHandle[xmppPushRegistrationInfo.xmppAccountHandle] = xmppPushRegistration;

   DebugLog(<< "XmppAgentJsonServerInterface::handleRegisterForXmppPushNotifications(): adding xmpp push registration handle: " << xmppPushRegistration << " for xmpp account: " << xmppPushRegistrationInfo.xmppAccountHandle << " total registered handlers: " << mMapConnToPushRegHandle.size());

   return kSuccess;
}

int XmppAgentJsonServerInterface::handleUnregisterForXmppPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppAgentJsonServerInterface::handleUnregisterForXmppPushNotifications()");

   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
   uint64_t xmppPushRegistration = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration));

   xmppAgentManager->removeSdkObserver(this);

   return xmppAgentManager->unregisterForXmppPushNotifications(xmppPushRegistration);
}

int XmppAgentJsonServerInterface::handleRegisterForRemoteSync(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
   DebugLog(<< "XmppAgentJsonServerInterface::handleRegisterForRemoteSync(): " << this);

   uint64_t xmppPushRegistration = 0;
   RemoteSyncSettings settings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), JSON_VALUE(settings));

   if (settings.accounts.empty())
   {
      WarningLog(<< "XmppAgentJsonServerInterface::handleRegisterForRemoteSync(): invalid remote-sync settings for xmppPushRegistration: " << xmppPushRegistration << " associated account not specified");
      XmppAgentRemoteSyncRegisterResult args;
      args.sessionHandle = (-1);
      JsonFunctionCall(mTransport, "remoteSyncRegisterResult", JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));
      return kError;
   }

   resip::Data username(settings.accounts[0].c_str(), settings.accounts[0].size());
   std::string password(resip::Data(settings.password.c_str()).base64decode().c_str());
   if (password.size() >= username.base64encode().size())
      settings.password.assign(resip::Data(password.substr(username.base64encode().size(), std::string::npos)).base64decode().c_str());

   xmppAgentManager->registerForRemoteSync(xmppPushRegistration, settings);

   return kSuccess;
}

int XmppAgentJsonServerInterface::handleUnregisterForRemoteSync(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
   DebugLog(<< "XmppAgentJsonServerInterface::handleUnregisterForRemoteSync(): " << this);
   uint64_t xmppPushRegistration = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration));
   return (xmppAgentManager->unregisterForRemoteSync(xmppPushRegistration));
}

int XmppAgentJsonServerInterface::handleLogout(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
   uint64_t xmppPushRegistration = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration));

   DebugLog(<< "XmppAgentJsonServerInterface::handleLogout(): " << this << " xmpp push registration: " << xmppPushRegistration);
   xmppAgentManager->logout(xmppPushRegistration);
   bool found = false;
   for (MapAccountToPushRegHandle::iterator i = mMapAccountToPushRegHandle.begin(); i != mMapAccountToPushRegHandle.end(); ++i)
   {
      if (i->second == xmppPushRegistration)
      {
         mMapConnToPushRegHandle.erase(conn);
         mMapAccountToPushRegHandle.erase(i->first);
         found = true;
         break;
      }
   }

   if (!found)
   {
      DebugLog(<< "XmppAgentJsonServerInterface::handleLogout(): " << this << " no connection mapping for xmpp push registration: " << xmppPushRegistration);
   }
   return kSuccess;
}

int XmppAgentJsonServerInterface::handleLogoutAccount(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   XmppAgentManagerInterface* xmppAgentManager = static_cast<XmppAgentManagerInterface*>(XmppAgentManager::getInterface(mPhone));
   unsigned int account = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(account));

   DebugLog(<< "XmppAgentJsonServerInterface::handleLogoutAccount(): " << this << " account: " << account);
   xmppAgentManager->logoutAccount(account);
   MapAccountToPushRegHandle::iterator i = mMapAccountToPushRegHandle.find(account);
   if (i != mMapAccountToPushRegHandle.end())
   {
      mMapConnToPushRegHandle.erase(conn);
      mMapAccountToPushRegHandle.erase(account);
   }
   else
   {
      DebugLog(<< "XmppAgentJsonServerInterface::handleLogoutAccount(): " << this << " no connection mapping for account: " << account);
   }
   return kSuccess;
}

int XmppAgentJsonServerInterface::handleRequestEventHistory(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "XmppAgentJsonServerInterface::handleRequestEventHistory()");
   XmppAgentManager* xmppAgentManager = XmppAgentManager::getInterface(mPhone);
   uint64_t xmppPushRegistration = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration));
   return xmppAgentManager->requestEventHistory(xmppPushRegistration);
}

int XmppAgentJsonServerInterface::onPushRegistrationSuccess(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationSuccessEvent& args)
{
   StackLog(<< "XmppAgentJsonServerInterface::onPushRegistrationSuccess()");
   JsonFunctionCall(mTransport, "onPushRegistrationSuccess", "xmppPushRegistration", pushRegistration, "pushEndpointId", args.pushEndpointId, "xmppAccountHandle", args.xmppAccountHandle);
   return kSuccess;
}

int XmppAgentJsonServerInterface::onPushRegistrationFailure(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationFailureEvent& args)
{
   StackLog(<< "XmppAgentJsonServerInterface::onPushRegistrationFailure()");
   JsonFunctionCall(mTransport, "onPushRegistrationFailure", "xmppPushRegistration", pushRegistration, "errorText", args.errorText, "pushEndpointId", args.pushEndpointId, "xmppAccountHandle", args.xmppAccountHandle);
   return kSuccess;
}

int XmppAgentJsonServerInterface::onEventHistory(XmppPushRegistrationHandle xmppPushRegistration, const XmppEventHistory& args)
{
   StackLog(<< "XmppAgentJsonServerInterface::onEventHistory()");
   JsonFunctionCall(mTransport, "onEventHistory", JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));
   return kSuccess;
}

int XmppAgentJsonServerInterface::onRemoteSyncRegisterResult(XmppPushRegistrationHandle xmppPushRegistration, const XmppAgentRemoteSyncRegisterResult& args)
{
   JsonFunctionCall(mTransport, "remoteSyncRegisterResult", JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));
   return kSuccess;
}

int XmppAgentJsonServerInterface::onLogout(XmppPushRegistrationHandle xmppPushRegistration, const LogoutResult& args)
{
   JsonFunctionCall(mTransport, "onLogout", JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));
   return kSuccess;
}

}

}

#endif
