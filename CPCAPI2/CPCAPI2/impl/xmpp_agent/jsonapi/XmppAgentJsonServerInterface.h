#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_JSON_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "xmpp_agent/XmppAgentJsonApi.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
   
class PhoneInterface;
namespace XmppAgent
{
   
class XmppAgentJsonServerInterface : public CPCAPI2::XmppAgent::XmppAgentJsonApi,
                                     public CPCAPI2::XmppAgent::XmppAgentHandler,
                                     public CPCAPI2::XmppAgent::XmppAgent<PERSON>ync<PERSON><PERSON><PERSON>,
                                     public CPCAPI2::JsonApi::JsonApiServerModule,
                                     public CPCAPI2::PhoneModule
{
   
public:
   
   XmppAgentJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~XmppAgentJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // XmppAgentHandler
   virtual int onPushRegistrationSuccess(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationSuccessEvent& args) OVERRIDE;
   virtual int onPushRegistrationFailure(XmppPushRegistrationHandle pushRegistration, const XmppPushRegistrationFailureEvent& args) OVERRIDE;
   virtual int onEventHistory(XmppPushRegistrationHandle xmppPushRegistration, const XmppEventHistory& args) OVERRIDE;
   virtual int onRemoteSyncRegisterResult(XmppPushRegistrationHandle xmppPushRegistration, const XmppAgentRemoteSyncRegisterResult& args) OVERRIDE;
   virtual int onLogout(XmppPushRegistrationHandle xmppPushRegistration, const LogoutResult& args) OVERRIDE;

private:
   
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleCreateXmppPushRegistration(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleRegisterForXmppPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleUnregisterForXmppPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleRequestEventHistory(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleRegisterForRemoteSync(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleUnregisterForRemoteSync(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleLogout(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);
   int handleLogoutAccount(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal);

private:
   
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo, const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

   typedef std::map<CPCAPI2::JsonApi::JsonApiRequestInfo, XmppPushRegistrationHandle> MapConnToPushRegHandle;
   MapConnToPushRegHandle mMapConnToPushRegHandle;
   
   typedef std::map<CPCAPI2::XmppAccount::XmppAccountHandle, XmppPushRegistrationHandle> MapAccountToPushRegHandle;
   MapAccountToPushRegHandle mMapAccountToPushRegHandle;

};
   
}
   
}

#endif // CPCAPI2_PUSH_NOTIFICATION_JSON_INTERFACE_H
