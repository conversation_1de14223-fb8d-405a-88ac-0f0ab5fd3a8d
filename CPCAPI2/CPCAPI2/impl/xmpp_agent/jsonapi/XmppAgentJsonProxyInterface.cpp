#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppAgentJsonProxyInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define JSON_MODULE "XmppAgentJsonApi"

namespace CPCAPI2
{

namespace XmppAgent
{

XmppAgentJsonProxyInterface::XmppAgentJsonProxyInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL)
{
   mFunctionMap["onEventHistory"] = std::bind(&XmppAgentJsonProxyInterface::handleEventHistory, this, std::placeholders::_1);
   mFunctionMap["onPushRegistrationSuccess"] = std::bind(&XmppAgentJsonProxyInterface::handleXmppPushRegistrationSuccess, this, std::placeholders::_1);
   mFunctionMap["onPushRegistrationFailure"] = std::bind(&XmppAgentJsonProxyInterface::handleXmppPushRegistrationFailure, this, std::placeholders::_1);
   mFunctionMap["remoteSyncRegisterResult"] = std::bind(&XmppAgentJsonProxyInterface::handleRemoteSyncRegisterResult, this, std::placeholders::_1);
   mFunctionMap["onLogout"] = std::bind(&XmppAgentJsonProxyInterface::handleLogoutResult, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());
   
   mPhone->addRefImpl();
}

XmppAgentJsonProxyInterface::~XmppAgentJsonProxyInterface()
{
   StackLog(<< "XmppAgentJsonProxyInterface::~XmppAgentJsonProxyInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
}

void XmppAgentJsonProxyInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void XmppAgentJsonProxyInterface::Release()
{
   StackLog(<< "XmppAgentJsonProxyInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

void XmppAgentJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   if (!mPhone->isReleasing() || !mPhone->isShutdown())
   {
      mPhone->getSdkModuleThread().post(f);
   }
   else 
   {
      delete f;
   }
}

void XmppAgentJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

void XmppAgentJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   mCallbacks.add(fp);
   if (mCbHook) { mCbHook(); }
}

// JsonApiClientModule
void XmppAgentJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int XmppAgentJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   StackLog(<< "XmppAgentJsonProxyInterface::processIncomingImpl(): trigger: " << funcName);
   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int XmppAgentJsonProxyInterface::process(unsigned int timeout)
{
   resip::ReadCallbackBase* fp = mCallbacks.getNext(timeout);
   while (fp)
   {
      (*fp)();
      delete fp;
      fp = mCallbacks.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* XmppAgentJsonProxyInterface::process_test(int timeout)
{
   //if (mShutdown)
   //{
   //   return NULL;
   //}
   resip::ReadCallbackBase* rcb = mCallbacks.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0, 0));
   }
   return NULL;
}
#endif

void XmppAgentJsonProxyInterface::addSdkObserver(XmppAgentHandler* observer)
{
   // NB: observers should be added before the handler is set, or they won't get pushed into the account.
   mSdkObservers.push_back(observer);
}

void XmppAgentJsonProxyInterface::removeSdkObserver(XmppAgentHandler* observer)
{
   mSdkObservers.remove(observer);
}

int XmppAgentJsonProxyInterface::setHandler(XmppPushRegistrationHandle pushRegistration, XmppAgentHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&XmppAgentJsonProxyInterface::setHandlerImpl, this, pushRegistration, handler);
   if (handler == NULL)
   {
      execute(f);
      process(-1);
   }
   else
   {
      post(f);
   }
   return kSuccess;
}

void XmppAgentJsonProxyInterface::setHandlerImpl(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler)
{
   StackLog(<< "XmppAgentJsonProxyInterface::setHandlerImpl(): xmpp push registration handle: " << xmppPushRegistration << " being added to handler list: " << mAppHandlers.size());
   mAppHandlers[xmppPushRegistration] = handler;

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(xmppPushRegistration));
}

XmppPushRegistrationHandle XmppAgentJsonProxyInterface::createXmppPushRegistration()
{
   XmppPushRegistrationHandle h = (XmppPushRegistrationHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::createXmppPushRegistrationImpl, this, h, ""));
   return h;
}

XmppPushRegistrationHandle XmppAgentJsonProxyInterface::createXmppPushRegistration(const CPCAPI2::JsonApi::AuthToken& authToken)
{
   XmppPushRegistrationHandle h = (XmppPushRegistrationHandle)CPCAPI2::JsonApi::JsonApiClient::getInterface(mPhone)->generateHandle();
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::createXmppPushRegistrationImpl, this, h, authToken.authToken));
   return h;
}

void XmppAgentJsonProxyInterface::createXmppPushRegistrationImpl(XmppPushRegistrationHandle xmppPushRegistration, const cpc::string& authToken)
{
   if (!authToken.empty())
   {
      JsonFunctionCall(mTransport, "createXmppPushRegistration", JSON_VALUE(xmppPushRegistration), JSON_VALUE(authToken));
   }
   else
   {
      JsonFunctionCall(mTransport, "createXmppPushRegistration", JSON_VALUE(xmppPushRegistration));
   }
}

int XmppAgentJsonProxyInterface::registerForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo)
{
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::registerForXmppPushNotificationsImpl, this, xmppPushRegistration, xmppPushRegistrationInfo));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::registerForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo)
{
   JsonFunctionCall(mTransport, "registerForXmppPushNotifications", JSON_VALUE(xmppPushRegistration), JSON_VALUE(xmppPushRegistrationInfo));
}

int XmppAgentJsonProxyInterface::unregisterForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration)
{
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::unregisterForXmppPushNotificationsImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::unregisterForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   JsonFunctionCall(mTransport, "unregisterForXmppPushNotifications", JSON_VALUE(xmppPushRegistration));
}

int XmppAgentJsonProxyInterface::requestEventHistory(XmppPushRegistrationHandle xmppPushRegistration)
{
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::requestEventHistoryImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::requestEventHistoryImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   JsonFunctionCall(mTransport, "requestEventHistory", JSON_VALUE(xmppPushRegistration));
}

int XmppAgentJsonProxyInterface::registerForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings)
{
   StackLog(<< "XmppAgentJsonProxyInterface::registerForRemoteSync(): webSocketURL: " << settings.wsSettings.webSocketURL);
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::registerForRemoteSyncImpl, this, xmppPushRegistration, settings));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::registerForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings_)
{
   StackLog(<< "XmppAgentJsonProxyInterface::registerForRemoteSyncImpl(): webSocketURL: " << settings_.wsSettings.webSocketURL);

   if (settings_.accounts.empty())
   {
      WarningLog(<< "XmppAgentJsonProxyInterface::registerForRemoteSyncImpl(): invalid remote-sync settings for webSocketURL: " << settings_.wsSettings.webSocketURL << " associated account not specified");
      return;
   }

   RemoteSync::RemoteSyncSettings settings(settings_);
   std::string password1(resip::Data(settings.password.c_str(), settings.password.size()).base64encode().c_str());
   std::string password2(resip::Data(settings.accounts[0].c_str(), settings.accounts[0].size()).base64encode().append(password1.c_str(), password1.size()).c_str());
   settings.password.assign(resip::Data(password2.c_str(), password2.size()).base64encode().c_str());

   JsonFunctionCall(mTransport, "registerForRemoteSync", JSON_VALUE(xmppPushRegistration), JSON_VALUE(settings));
}

int XmppAgentJsonProxyInterface::unregisterForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration)
{
   StackLog(<< "XmppAgentJsonProxyInterface::unregisterForRemoteSync(): xmpp push handle: " << xmppPushRegistration);
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::unregisterForRemoteSyncImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::unregisterForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   StackLog(<< "XmppAgentJsonProxyInterface::unregisterForRemoteSyncImpl(): xmpp push handle: " << xmppPushRegistration);
   JsonFunctionCall(mTransport, "unregisterForRemoteSync", JSON_VALUE(xmppPushRegistration));
}

int XmppAgentJsonProxyInterface::logout(XmppPushRegistrationHandle xmppPushRegistration)
{
   StackLog(<< "XmppAgentJsonProxyInterface::logout(): xmpp push handle: " << xmppPushRegistration);
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::logoutImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::logoutImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   StackLog(<< "XmppAgentJsonProxyInterface::logoutImpl(): xmpp push handle: " << xmppPushRegistration);
   JsonFunctionCall(mTransport, "logout", JSON_VALUE(xmppPushRegistration));
}

int XmppAgentJsonProxyInterface::logoutAccount(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppAgentJsonProxyInterface::logoutAccount(): xmpp account handle: " << account);
   post(resip::resip_bind(&XmppAgentJsonProxyInterface::logoutAccountImpl, this, account));
   return kSuccess;
}

void XmppAgentJsonProxyInterface::logoutAccountImpl(CPCAPI2::XmppAccount::XmppAccountHandle account)
{
   StackLog(<< "XmppAgentJsonProxyInterface::logoutAccountImpl(): xmpp account handle: " << account);
   JsonFunctionCall(mTransport, "logoutAccount", JSON_VALUE(account));
}

int XmppAgentJsonProxyInterface::handleXmppPushRegistrationSuccess(const rapidjson::Value& functionObjectVal)
{
   XmppPushRegistrationHandle xmppPushRegistration;
   XmppPushRegistrationSuccessEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), "pushEndpointId", args.pushEndpointId, "xmppAccountHandle", args.xmppAccountHandle);
   StackLog(<< "XmppAgentJsonProxyInterface::handleXmppPushRegistrationSuccess(): xmppPushRegistration: " << xmppPushRegistration << " pushEndpointId: " << args.pushEndpointId << " xmppAccountHandle: " << args.xmppAccountHandle);

   std::map<XmppPushRegistrationHandle, XmppAgentHandler*>::iterator it = mAppHandlers.find(xmppPushRegistration);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppAgentHandler::onPushRegistrationSuccess, it->second, xmppPushRegistration, args));
   }

   return kSuccess;
}

int XmppAgentJsonProxyInterface::handleXmppPushRegistrationFailure(const rapidjson::Value& functionObjectVal)
{
   XmppPushRegistrationHandle xmppPushRegistration;
   XmppPushRegistrationFailureEvent args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), "errorText", args.errorText, "pushEndpointId", args.pushEndpointId, "xmppAccountHandle", args.xmppAccountHandle);
   StackLog(<< "XmppAgentJsonProxyInterface::handleXmppPushRegistrationFailure(): xmppPushRegistration: " << xmppPushRegistration << " pushEndpointId: " << args.pushEndpointId << " xmppAccountHandle: " << args.xmppAccountHandle << " errorText: " << args.errorText);

   std::map<XmppPushRegistrationHandle, XmppAgentHandler*>::iterator it = mAppHandlers.find(xmppPushRegistration);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppAgentHandler::onPushRegistrationFailure, it->second, xmppPushRegistration, args));
   }

   return kSuccess;
}

int XmppAgentJsonProxyInterface::handleRemoteSyncRegisterResult(const rapidjson::Value& functionObjectVal)
{
   XmppPushRegistrationHandle xmppPushRegistration;
   XmppAgentRemoteSyncRegisterResult args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));

   StackLog(<< "XmppAgentJsonProxyInterface::handleRemoteSyncRegisterResult(): sessionHandle: " << args.sessionHandle << " xmppPushRegistration: " << xmppPushRegistration);

   std::map<XmppPushRegistrationHandle, XmppAgentHandler*>::iterator it = mAppHandlers.find(xmppPushRegistration);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppAgentHandler::onRemoteSyncRegisterResult, it->second, xmppPushRegistration, args));
   }

   return kSuccess;
}

int XmppAgentJsonProxyInterface::handleEventHistory(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "XmppAgentJsonProxyInterface::handleEventHistory(): handler list: " << mAppHandlers.size());
   XmppPushRegistrationHandle xmppPushRegistration = -1;
   XmppAgent::XmppEventHistory args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));

   std::map<XmppPushRegistrationHandle, XmppAgentHandler*>::iterator it = mAppHandlers.find(xmppPushRegistration);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppAgentHandler::onEventHistory, it->second, xmppPushRegistration, args));
   }
   return kSuccess;
}

int XmppAgentJsonProxyInterface::handleLogoutResult(const rapidjson::Value& functionObjectVal)
{
   XmppPushRegistrationHandle xmppPushRegistration;
   LogoutResult args;
   JsonDeserialize(functionObjectVal, JSON_VALUE(xmppPushRegistration), JSON_VALUE(args));

   StackLog(<< "XmppAgentJsonProxyInterface::handleLogoutResult(): xmppPushRegistration: " << xmppPushRegistration << " success: " << args.success);

   std::map<XmppPushRegistrationHandle, XmppAgentHandler*>::iterator it = mAppHandlers.find(xmppPushRegistration);
   if (it != mAppHandlers.end())
   {
      postCallback(makeFpCommand(XmppAgentHandler::onLogout, it->second, xmppPushRegistration, args));
   }

   return kSuccess;
}

}

}

#endif
