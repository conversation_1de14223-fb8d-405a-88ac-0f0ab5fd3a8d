#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_JSON_PROXY_INTERFACE_H

#include "xmpp_agent/XmppAgentJsonProxy.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp_agent/XmppAgentTypes.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <future>
#include <list>

namespace CPCAPI2
{

class PhoneInterface;

namespace XmppAgent
{

class XmppAgentJsonProxyInterface : public CPCAPI2::XmppAgent::XmppAgentManagerJsonProxy,
                                    public CPCAPI2::JsonApi::JsonApiClientModule,
#ifdef CPCAPI2_AUTO_TEST
                                    public AutoTestProcessor,
#endif
                                    public CPCAPI2::PhoneModule
{

public:

   XmppAgentJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~XmppAgentJsonProxyInterface();

   virtual void setCallbackHook(void (*cbHook)(void*), void* context);

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // AutoTestProcessor
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   // XmppAgentManagerJsonProxy

   // Inherited via XmppAgentManager
   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE {}
   virtual int setHandler(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler) OVERRIDE;
   virtual int setPushNotificationManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager) OVERRIDE { return kError; }
   virtual int setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonServer) OVERRIDE { return kError; }
   virtual XmppPushRegistrationHandle createXmppPushRegistration() OVERRIDE;
   virtual XmppPushRegistrationHandle createXmppPushRegistration(const CPCAPI2::JsonApi::AuthToken& authToken) OVERRIDE;
   virtual int registerForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo) OVERRIDE;
   virtual int unregisterForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int requestEventHistory(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int registerForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings) OVERRIDE;
   virtual int unregisterForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int logout(XmppPushRegistrationHandle xmppPushRegistration) OVERRIDE;
   virtual int logoutAccount(CPCAPI2::XmppAccount::XmppAccountHandle account) OVERRIDE;

   void addSdkObserver(XmppAgentHandler* observer);
   void removeSdkObserver(XmppAgentHandler* observer);

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   void setHandlerImpl(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler);
   void createXmppPushRegistrationImpl(XmppPushRegistrationHandle xmppPushRegistration, const cpc::string& authToken);
   void registerForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo);
   void unregisterForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void requestEventHistoryImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void registerForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings);
   void unregisterForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void logoutImpl(XmppPushRegistrationHandle xmppPushRegistration);
   void logoutAccountImpl(CPCAPI2::XmppAccount::XmppAccountHandle account);

   int handleXmppPushRegistrationSuccess(const rapidjson::Value& functionObjectVal);
   int handleXmppPushRegistrationFailure(const rapidjson::Value& functionObjectVal);
   int handleEventHistory(const rapidjson::Value& functionObjectVal);
   int handleRemoteSyncRegisterResult(const rapidjson::Value& functionObjectVal);
   int handleLogoutResult(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::map<XmppPushRegistrationHandle, XmppAgentHandler*> mAppHandlers;
   std::list<XmppAgentHandler*> mSdkObservers;
   resip::Fifo<resip::ReadCallbackBase> mCallbacks;
   std::function<void(void)> mCbHook;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_JSON_PROXY_INTERFACE_H
