#include "brand_branded.h"

#include "interface/experimental/xmpp_agent/XmppAgentJsonProxy.h"
#include "interface/experimental/xmpp_agent/XmppAgentJsonApi.h"

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "XmppAgentJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "XmppAgentJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace XmppAgent
{
   XmppAgentJsonApi* XmppAgentJsonApi::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<XmppAgentJsonServerInterface>(phone, "XmppAgentJsonApi");
#else
      return NULL;
#endif
   }

   XmppAgentManagerJsonProxy* XmppAgentManagerJsonProxy::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<XmppAgentJsonProxyInterface>(phone, "XmppAgentManagerJsonProxy");
#else
      return NULL;
#endif
   }
}
}
