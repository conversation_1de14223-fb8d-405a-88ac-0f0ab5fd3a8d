#include "brand_branded.h"

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "xmpp/XmppAccountState.h"
#include "xmpp/XmppChatManager.h"
#include "xmpp_agent/XmppAgentHandler.h"
#include "xmpp/XmppChatManagerInterface.h"
#include "xmpp/XmppAccountInterface.h"
#include "XmppAgentManagerInterface.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "jsonapi/JsonApiServerSendTransportInterface.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "push_service/PushNotificationServiceHandler.h"
#include "push_service/PushNotificationServiceInterface.h"
#include "remotesync/RemoteSyncManager.h"
#include "remotesync/RemoteSyncInternalHandlerTypes.h"
#include "remotesync/SyncManagerInterface.h"
#include "remotesync_xmpp_helper/RemoteSyncXmppHelper.h"
#include "log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "util/STAAssertion.h"
#include <boost/tokenizer.hpp>

#include <rutil/Time.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <time.h>

#include <sstream>
#include <functional>

using namespace resip;
using namespace CPCAPI2::XmppChat;
using namespace CPCAPI2::XmppVCard;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::RemoteSync;
using namespace CPCAPI2::JsonApi;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT 10
#define XMPP_AGENT_MAXIMUM_FETCH_MESSAGE_COUNT 10
#define XMPP_AGENT_REMOTE_SYNC_DELAY_TIMER_ID 1
#define XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMER_ID 2
#define XMPP_AGENT_LOGOUT_CLEANUP_TIMER_ID 3
#define XMPP_AGENT_REMOTE_SYNC_DELAY_MSECS 3000
#define XMPP_AGENT_REMOTE_SYNC_DELTA_MSECS 200
#define XMPP_AGENT_REMOTE_SYNC_EXPIRY_BUFFER_MSECS 100
#define XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMEOUT_MSECS 120*1000
#define XMPP_AGENT_LOGOUT_CLEANUP_TIMEOUT_MSECS 60000

#define CP_LOCAL_LOGGER_VAR mLocalLogger

namespace CPCAPI2
{

namespace XmppAgent
{

std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::XmppAgentState& state)
{
   os << "xmpp-registration: " << state.registrationInfo << " isRegisteredForPush: " << (state.isRegisteredForPush ? "yes" : "no") << " isLoggedOut: " << (state.isLoggedOut ? "yes" : "no")
   << " serviceDownAtMs: " << state.serviceDownAtMs << " handler: " << state.appHandler << " auth token: " << state.authToken.authToken << " auth user: " << state.authToken.cp_user << " auth device: " << state.authToken.device_uuid << " event-list: " << state.events.size();
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::RemoteSyncConnectionState& state)
{
   switch (state)
   {
      case XmppAgentManagerInterface::RemoteSyncConnectionState_Disconnected: os << "disconnected"; break;
      case XmppAgentManagerInterface::RemoteSyncConnectionState_Connecting: os << "connecting"; break;
      case XmppAgentManagerInterface::RemoteSyncConnectionState_Connected: os << "connected"; break;
      case XmppAgentManagerInterface::RemoteSyncConnectionState_Disconnecting: os << "disconnecting"; break;
      default: os << "invalid"; break;
   }
   return os;
}

std::ostream& operator<<(std::ostream& os, const XmppAgentManagerInterface::RemoteSyncHandlingState& state)
{
   switch (state)
   {
      case XmppAgentManagerInterface::RemoteSyncHandlingState_Init: os << "init"; break;
      case XmppAgentManagerInterface::RemoteSyncHandlingState_Started: os << "started"; break;
      case XmppAgentManagerInterface::RemoteSyncHandlingState_FetchRange: os << "fetch_range"; break;
      case XmppAgentManagerInterface::RemoteSyncHandlingState_GetMessageCounts: os << "get_message_counts"; break;
      case XmppAgentManagerInterface::RemoteSyncHandlingState_FetchConversations: os << "fetch_conversations"; break;
      case XmppAgentManagerInterface::RemoteSyncHandlingState_Completed: os << "completed"; break;
      default: os << "invalid"; break;
   }
   return os;
}

std::atomic<XmppPushRegistrationHandle> XmppPushRegistrationHandleFactory::sNextHandle(1);

XmppAgentManagerInterface::XmppAgentManagerInterface(Phone* phone) :
   mShutdown(false),
   mPreRelease(false),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mPushManager(NULL),
   mJsonServer(NULL),
   mJsonApiServerSendTransport(NULL),
   mAccountServiceNotificationTimer(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mLogoutCleanupTimer(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mSyncSessionHandle(-1),
   mSyncRequestHandle(-1),
   mSyncRevision(-1),
   mSyncConnectionState(RemoteSyncConnectionState_Disconnected),
   mSyncHandlingState(RemoteSyncHandlingState_Init),
   mSyncDelayTimer(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mLocalLogger(dynamic_cast<PhoneInterface*>(phone)->localLogger())
{
   mThreadCheck.test();

   XmppChatManager* xmppChat = XmppChatManager::getInterface(phone);
   XmppChatManagerInterface* xmppChatIf = dynamic_cast<XmppChatManagerInterface*>(xmppChat);
   xmppChatIf->addSdkObserver(this);

   XmppAccountManager* xmppAccount = XmppAccountManager::getInterface(phone);
   XmppAccountInterface* xmppAcctIf = dynamic_cast<XmppAccountInterface*>(xmppAccount);
   xmppAcctIf->addSdkObserver(this);

   RemoteSyncManagerInternal* syncMgr = RemoteSyncManagerInternal::getInternalInterface(mPhone);
   syncMgr->addEventObserver(this);
   
   mJsonApiServerSendTransport = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone));

   mAccountServiceNotificationTimer.expires_from_now(XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMEOUT_MSECS);
   mAccountServiceNotificationTimer.async_wait(this, XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMER_ID, NULL);
   
   LocalDebugLog("XmppAgentManagerInterface::XmppAgentManagerInterface(): {} phone: {} sdk reactor: {}",
                 log_ptr(this), log_ptr(phone), log_ptr(&dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()));
}

XmppAgentManagerInterface::~XmppAgentManagerInterface()
{
   mSyncDelayTimer.cancel();
   mAccountServiceNotificationTimer.cancel();
   mLogoutCleanupTimer.cancel();
   mShutdown = true;
   interruptProcess();
}

void XmppAgentManagerInterface::PreRelease()
{
   InfoLog(<< "XmppAgentManagerInterface::PreRelease()");
   mPreRelease = true;

   // Shutdown the timers
   mSyncDelayTimer.cancel();
   mAccountServiceNotificationTimer.cancel();
   mLogoutCleanupTimer.cancel();

   // Unsubscribe all observers
   JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
   if (jsonServerIf)
   {
      for (JsonUserPushRegMap::iterator i = mJsonUserPushRegMap.begin(); i != mJsonUserPushRegMap.end(); ++i)
      {
         jsonServerIf->removeSdkObserver(i->first, this);
      }
   }

   PushNotificationServiceInterface* pushMgrIf = dynamic_cast<PushNotificationServiceInterface*>(mPushManager);
   if (pushMgrIf)
   {
      for (XmppAgentStateMap::iterator j = mStateMap.begin(); j != mStateMap.end(); ++j)
      {
         pushMgrIf->removeSdkObserver(j->second->registrationInfo.pushNotificationServiceHandle, this);
      }
   }

   XmppChatManagerInterface* xmppChatIf = dynamic_cast<XmppChatManagerInterface*>(XmppChatManager::getInterface(mPhone));
   if (xmppChatIf)
   {
      xmppChatIf->removeSdkObserver(this);
   }

   XmppAccountInterface* xmppAcctIf = dynamic_cast<XmppAccountInterface*>(XmppAccountManager::getInterface(mPhone));
   if (xmppAcctIf)
   {
      xmppAcctIf->removeSdkObserver(this);
   }

   RemoteSyncManagerInternal* syncMgrIf = RemoteSyncManagerInternal::getInternalInterface(mPhone);
   // syncMgr->setHandler(sessionHandle, NULL);
   if (syncMgrIf)
   {
      syncMgrIf->removeEventObserver(this);
      if (mSyncSessionHandle != (-1))
      {
         syncMgrIf->disconnect(mSyncSessionHandle);
      }
   }

   mSyncSessionHandle = (-1);
   mSyncRequestHandle = (-1);
   mSyncRevision = (-1);
   mSyncConnectionState = RemoteSyncConnectionState_Disconnecting;
   mSyncHandlingState = RemoteSyncHandlingState_Init;

   mSdkObservers.clear();
   for (SyncDelayList::iterator k = mSyncDelayList.begin(); k != mSyncDelayList.end(); ++k)
   {
      delete (*k);
   }
   mSyncDelayList.clear();
   mAccountSyncStates.clear();
   mAccountSyncItems.clear();
   mLoggedOutJsonUsers.clear();
   mPushEndpointRegMap.clear();
   mJsonUserPushRegMap.clear();
   mAccountPushRegMap.clear();
   for (XmppAgentStateMap::iterator l = mStateMap.begin(); l != mStateMap.end(); ++l)
   {
      delete (l->second);
   }
   mStateMap.clear();
}

bool XmppAgentManagerInterface::PreReleaseCompleted()
{
   // If there are no more registrations, we're done.
   return (mStateMap.size() == 0);
}

void XmppAgentManagerInterface::Release()
{
   delete this;
}

int XmppAgentManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kXmppAgentModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kXmppAgentModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

XmppAgentManagerInternal* XmppAgentManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<XmppAgentManagerInternal*>(XmppAgentManager::getInterface(cpcPhone));
}

void XmppAgentManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

#ifdef CPCAPI2_AUTO_TEST
AutoTestReadCallback* XmppAgentManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void XmppAgentManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void XmppAgentManagerInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}

void XmppAgentManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook)
   {
      mCbHook();
   }
}

void XmppAgentManagerInterface::addSdkObserver(XmppAgentHandler* observer)
{
   mSdkObservers.insert(observer);
}

void XmppAgentManagerInterface::removeSdkObserver(XmppAgentHandler* observer)
{
   mSdkObservers.erase(observer);
}

int XmppAgentManagerInterface::setPushNotificationManager(CPCAPI2::PushService::PushNotificationServiceManager* pushManager)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::setPushNotificationManagerImpl, this, pushManager));
   return kSuccess;
}

void XmppAgentManagerInterface::setPushNotificationManagerImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushManager)
{
   mPushManager = pushManager;
}

int XmppAgentManagerInterface::setJsonApiServer(CPCAPI2::JsonApi::JsonApiServer* jsonServer)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::setJsonApiServerImpl, this, jsonServer));
   return kSuccess;
}

void XmppAgentManagerInterface::setJsonApiServerImpl(CPCAPI2::JsonApi::JsonApiServer* jsonServer)
{
   mJsonServer = jsonServer;
}

int XmppAgentManagerInterface::setHandler(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler)
{
   LocalMaxLog("XmppAgentManagerInterface::setHandler(): {} -> {}", xmppPushRegistration, log_ptr(handler));
   post(resip::resip_bind(&XmppAgentManagerInterface::setHandlerImpl, this, xmppPushRegistration, handler));
   return kSuccess;
}

void XmppAgentManagerInterface::setHandlerImpl(XmppPushRegistrationHandle xmppPushRegistration, XmppAgentHandler* handler)
{
   LocalMaxLog("XmppAgentManagerInterface::setHandlerImpl(): {} -> {}", xmppPushRegistration, log_ptr(handler));
   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it != mStateMap.end())
   {
      it->second->appHandler = handler;
   }
}

XmppPushRegistrationHandle XmppAgentManagerInterface::createXmppPushRegistration()
{
   LocalMaxLog("XmppAgentManagerInterface::createXmppPushRegistration (no auth token provided)");
   XmppPushRegistrationHandle xmppPushRegistration = XmppPushRegistrationHandleFactory::getNext();
   post(resip::resip_bind(&XmppAgentManagerInterface::createXmppPushRegistrationImpl2, this, xmppPushRegistration));
   return xmppPushRegistration;
}

void XmppAgentManagerInterface::createXmppPushRegistrationImpl2(XmppPushRegistrationHandle xmppPushRegistration)
{
   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it == mStateMap.end())
   {
      LocalMaxLog("creating state for handle: {}", xmppPushRegistration);
      mStateMap[xmppPushRegistration] = new XmppAgentState();
   }
}

XmppPushRegistrationHandle XmppAgentManagerInterface::createXmppPushRegistration(const CPCAPI2::JsonApi::AuthToken& authToken)
{
   LocalMaxLog("XmppAgentManagerInterface::createXmppPushRegistration (auth token provided but not used)");
   XmppPushRegistrationHandle xmppPushRegistration = XmppPushRegistrationHandleFactory::getNext();
   post(resip::resip_bind(&XmppAgentManagerInterface::createXmppPushRegistrationImpl2, this, xmppPushRegistration));
   return xmppPushRegistration;
}

int XmppAgentManagerInterface::createXmppPushRegistration(XmppPushRegistrationHandle xmppPushRegistration)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::createXmppPushRegistrationImpl2, this, xmppPushRegistration));
   return kSuccess;
}

int XmppAgentManagerInterface::createXmppPushRegistration(XmppPushRegistrationHandle xmppPushRegistration, const CPCAPI2::JsonApi::AuthToken& authToken)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::createXmppPushRegistrationImpl2, this, xmppPushRegistration));
   return kSuccess;
}

int XmppAgentManagerInterface::registerForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo)
{
   LocalDebugLog("XmppAgentManagerInterface::registerForXmppPushNotifications() xmppPushRegistration: {} xmppPushRegistrationInfo: {}", xmppPushRegistration, xmppPushRegistrationInfo);

   post(resip::resip_bind(&XmppAgentManagerInterface::registerForXmppPushNotificationsImpl, this, xmppPushRegistration, xmppPushRegistrationInfo));
   return kSuccess;
}

void XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration, const XmppPushRegistrationInfo& xmppPushRegistrationInfo)
{
   LocalMaxLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl() xmppPushRegistration: {} xmppPushRegistrationInfo: {}", xmppPushRegistration, xmppPushRegistrationInfo);

   if (xmppPushRegistration == (XmppPushRegistrationHandle)UINT_MAX)
   {
      LocalErrLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl() -- invalid xmppPushRegistration handle");
      return;
   }

   XmppAgentState* agentState = NULL;
   {
      XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
      if (it == mStateMap.end())
      {
         LocalErrLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl() -- failed to locate handle: {}", xmppPushRegistration);
         return;
      }

      LocalMaxLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): updating existing XmppAgentState with existing handle: {}", xmppPushRegistration);
      agentState = it->second;

      StackLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): existing state: " << *agentState);

      addXmppRegistrationToAccountMapping(xmppPushRegistrationInfo.xmppAccountHandle, xmppPushRegistration);
      addXmppRegistrationToJsonUserMapping(xmppPushRegistrationInfo.jsonUserHandle, xmppPushRegistration);
      addXmppRegistrationToPushEndpointMapping(xmppPushRegistrationInfo.pushNotificationDev, xmppPushRegistration);
   }

   {
      for (XmppAgentStateMap::iterator itExisting = mStateMap.begin(); itExisting != mStateMap.end(); ++itExisting)
      {
         if (itExisting->first != xmppPushRegistration &&
            itExisting->second->registrationInfo.pushNotificationDev == xmppPushRegistrationInfo.pushNotificationDev &&
            itExisting->second->registrationInfo.xmppAccountHandle == xmppPushRegistrationInfo.xmppAccountHandle)
         {
            LocalDebugLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): removing dangling XmppAgentState with xmpp account handle: {} \
             xmpp push handle: {} active xmpp push handle: {}",
             itExisting->second->registrationInfo.xmppAccountHandle, itExisting->first, xmppPushRegistration);

            StackLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): new agent state: " << *(mStateMap[xmppPushRegistration]));
            StackLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): existing agent state: " << *(itExisting->second));

            // Delete the registration state pointer associated to the new xmpp push registration handle
            CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle = itExisting->second->registrationInfo.jsonUserHandle;
            PushService::PushNotificationServiceHandle notificationServiceHandle = itExisting->second->registrationInfo.pushNotificationServiceHandle;
            delete mStateMap[xmppPushRegistration];

            // Populate the existing state associated to the stale xmpp push registration handle, and map it to the new xmpp push registration handle
            mStateMap[xmppPushRegistration] = itExisting->second;
            agentState = itExisting->second;

            // Remove the stale push registration handle from the account handle mapping
            removeXmppRegistrationFromAccountMapping(agentState->registrationInfo.xmppAccountHandle, itExisting->first);

            // Remove the stale push registration handle from the json user handle mapping
            removeXmppRegistrationFromJsonUserMapping(agentState->registrationInfo.jsonUserHandle, itExisting->first);

            // Remove the stale push registration handle from the push endpoint mapping
            removeXmppRegistrationFromPushEndpointMapping(agentState->registrationInfo.pushNotificationDev, itExisting->first);

            // Remove the push notification service registration
            mPushManager->unregisterPushNotificationService(notificationServiceHandle);

            // Unsubscribe from json server events if the json user handle is stale
            JsonUserPushRegMap::iterator jsonIter = mJsonUserPushRegMap.find(agentState->registrationInfo.jsonUserHandle);
            if (jsonIter == mJsonUserPushRegMap.end())
            {
               // No more push registration mappings exist for this json user
               JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
               jsonServerIf->removeSdkObserver(agentState->registrationInfo.jsonUserHandle, this);
            }

            // Delete the stale xmpp push registration mapping
            mStateMap.erase(itExisting);
            break;
         }
      }
   }

   if (!xmppPushRegistrationInfo.pushNotificationDev.empty() && xmppPushRegistrationInfo.xmppAccountHandle != 0)
   {
      LocalMaxLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): update xmpp push registration info for device handle: {} xmpp account handle: {} xmpp push handle: {}",
                  xmppPushRegistrationInfo.pushNotificationDev.c_str(), xmppPushRegistrationInfo.xmppAccountHandle, xmppPushRegistration);
      // only update the reg info if something valid was passed in;
      // this way the caller has a mechanism for toggling push notifications on/off
      // and relying on the existing (previous) registration info that was passed in
      agentState->registrationInfo = xmppPushRegistrationInfo;
   }

   agentState->isRegisteredForPush = true;

   // !jjg! WRONG not invalid -- we permit this because the end effect is that an existing push registration is simply updated
   // with isRegisteredForPush = true -- exactly what we want when e.g. the websocket connection from the client closes
   //if (xmppPushRegistrationInfo.xmppAccountHandle == 0)
   //{
   //   StackLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl() -- invalid xmpp account handle");
   //   return;
   //}

   // .jza. currently will set off an assert in PushNotificationServiceInterface's EventSource::addEventObserver
   // since that EventSource expects to receive calls in its own reactor.
   // when trying to fix this by adjusting EventSource::addEventObserver to post,
   // we started hitting shutdown crashes with this post executing later against a destroyed PushNotificationServiceInterface/EventSource
   // for now, comment out addEventObserver.
   //
   // possible fix might involve EventSource posting using a weak pointer, but this would involve adjusting creation of
   // PushNotificationServiceInterface to be held by a shared_ptr, which is a huge cascade of PhoneInterfaces changes :(

   PushService::PushNotificationServiceHandle notificationServiceHandle = 0;
   if (mPushManager)
   {
      notificationServiceHandle = mPushManager->createPushNotificationHandle();
      agentState->registrationInfo.pushNotificationServiceHandle = notificationServiceHandle;
      PushNotificationServiceInterface* pushMgrIf = dynamic_cast<PushNotificationServiceInterface*>(mPushManager);
      pushMgrIf->addSdkObserver(notificationServiceHandle, this);
      mPushManager->registerPushNotificationService(notificationServiceHandle, xmppPushRegistrationInfo.pushNotificationDev);
   }
   else
   {
      ErrLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): push notification service manager has not been initialized");
   }

   if (mJsonServer)
   {
      JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
      jsonServerIf->addSdkObserver(agentState->registrationInfo.jsonUserHandle, this);
   }
   else
   {
      ErrLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): json server has not been initialized");
   }

   StackLog(<< "XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): final state at registration: " << *agentState);

   XmppPushRegistrationSuccessEvent regSuccessEvt;
   regSuccessEvt.pushEndpointId = agentState->registrationInfo.pushNotificationDev;
   regSuccessEvt.xmppAccountHandle = agentState->registrationInfo.xmppAccountHandle;
   fireEvent(agentState->appHandler, cpcFunc(XmppAgentHandler::onPushRegistrationSuccess), xmppPushRegistration, regSuccessEvt);

   RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.find(xmppPushRegistrationInfo.xmppAccountHandle);
   if (i == mAccountSyncItems.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::registerForXmppPushNotificationsImpl(): adding account: {} to remote-sync account mapping", xmppPushRegistrationInfo.xmppAccountHandle);
      RemoteSyncItemMap emptyItemList;
      mAccountSyncItems[xmppPushRegistrationInfo.xmppAccountHandle] = emptyItemList;

      RemoteSyncContext syncContext;
      syncContext.syncHandlingState = RemoteSyncHandlingState_Init;
      syncContext.messageCount = (-1);
      syncContext.unreadCount = (-1);
      syncContext.unreadConversations = (-1);
      syncContext.totalConversations = (-1);
      syncContext.fetchOffset = (-1);
      syncContext.highestClientCreatedTime = (-1);
      mAccountSyncStates[xmppPushRegistrationInfo.xmppAccountHandle] = syncContext;
   }
}

int XmppAgentManagerInterface::unregisterForXmppPushNotifications(XmppPushRegistrationHandle xmppPushRegistration)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::unregisterForXmppPushNotificationsImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentManagerInterface::unregisterForXmppPushNotificationsImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it != mStateMap.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::unregisterForXmppPushNotificationsImpl(): agent state exists for xmpp push reg handle: {}", xmppPushRegistration);
      it->second->isRegisteredForPush = false;
      PushNotificationServiceInterface* pushMgrIf = dynamic_cast<PushNotificationServiceInterface*>(mPushManager);
      pushMgrIf->removeSdkObserver(it->second->registrationInfo.pushNotificationServiceHandle, this);
      mPushManager->unregisterPushNotificationService(it->second->registrationInfo.pushNotificationServiceHandle);

      // Unsubscribe from json server events if the json user handle is stale
      JsonUserPushRegMap::iterator jsonIter = mJsonUserPushRegMap.find(it->second->registrationInfo.jsonUserHandle);
      if (jsonIter == mJsonUserPushRegMap.end())
      {
         // No more push registration mappings exist for this json user
         JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
         jsonServerIf->removeSdkObserver(it->second->registrationInfo.jsonUserHandle, this);
      }
   }
}

int XmppAgentManagerInterface::registerForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings)
{
   LocalDebugLog("XmppAgentManagerInterface::registerForRemoteSync(): xmpp push reg handle: {} webSocketURL: {}", xmppPushRegistration, settings.wsSettings.webSocketURL.c_str());
   post(resip::resip_bind(&XmppAgentManagerInterface::registerForRemoteSyncImpl, this, xmppPushRegistration, settings));
   return kSuccess;
}
   
void XmppAgentManagerInterface::registerForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration, const struct CPCAPI2::RemoteSync::RemoteSyncSettings& settings)
{
   LocalMaxLog("XmppAgentManagerInterface::registerForRemoteSyncImpl: xmpp push reg handle: {}", xmppPushRegistration);
   
   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it == mStateMap.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::registerForRemoteSyncImpl(): ignoring remote-sync registration as push notification handle is invalid: {}", xmppPushRegistration);
      XmppAgentRemoteSyncRegisterResult args;
      args.sessionHandle = (-1);

      // No app handler can be accessed as cannot get the state info
      // fireEvent(it->second->appHandler, cpcFunc(XmppAgentHandler::onRemoteSyncRegisterResult), xmppPushRegistration, args);
      return;
   }
   
   if (settings.wsSettings.webSocketURL.empty() || settings.password.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::registerForRemoteSyncImpl(): ignoring remote-sync registration, as the webSocketURL or password have not been populated");
      XmppAgentRemoteSyncRegisterResult args;
      args.sessionHandle = (-1);
      fireEvent(it->second->appHandler, cpcFunc(XmppAgentHandler::onRemoteSyncRegisterResult), xmppPushRegistration, args);
      return;
   }
   
   // TODO: should we ignore the settings or update them, when multiple clients connect, as we may receive multiple remote-sync registrations.
   // Currently, will accept the first registration, and ignore the rest, as only one remote-sync session is required for a login context.
   if (mSyncSessionHandle != (-1))
   {
      LocalDebugLog("XmppAgentManagerInterface::registerForRemoteSyncImpl(): ignoring remote-sync registration, as a remote-sync session: {} has already been established", mSyncSessionHandle);
      XmppAgentRemoteSyncRegisterResult args;
      args.sessionHandle = mSyncSessionHandle;
      fireEvent(it->second->appHandler, cpcFunc(XmppAgentHandler::onRemoteSyncRegisterResult), xmppPushRegistration, args);
      return;
   }

   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   mSyncSessionHandle = syncMgr->create();

   CPCAPI2::RemoteSync::RemoteSyncSettings settingsCopy(settings);
   settingsCopy.wsSettings.certStorageFileSystemPath = ".";

   syncMgr->configureSettings(mSyncSessionHandle, settingsCopy);

   LocalMaxLog("XmppAgentManagerInterface::registerForRemoteSync(): push registration handle: {} session handle {}", xmppPushRegistration, mSyncSessionHandle);
   XmppAgentRemoteSyncRegisterResult args;
   args.sessionHandle = mSyncSessionHandle;
   fireEvent(it->second->appHandler, cpcFunc(XmppAgentHandler::onRemoteSyncRegisterResult), xmppPushRegistration, args);

   syncMgr->connect(mSyncSessionHandle);
}

int XmppAgentManagerInterface::unregisterForRemoteSync(XmppPushRegistrationHandle xmppPushRegistration)
{
   LocalDebugLog("XmppAgentManagerInterface::unregisterForRemoteSync(): xmpp push reg handle: {}", xmppPushRegistration);
   post(resip::resip_bind(&XmppAgentManagerInterface::unregisterForRemoteSyncImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentManagerInterface::unregisterForRemoteSyncImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   LocalMaxLog("XmppAgentManagerInterface::unregisterForRemoteSyncImpl: xmpp push reg handle: {} remote sync session handle: {}", xmppPushRegistration, mSyncSessionHandle);

   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it == mStateMap.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::unregisterForRemoteSyncImpl(): ignoring remote-sync unregistration as push registration handle is invalid: {}", xmppPushRegistration);
      return;
   }

   if (mSyncSessionHandle == (-1))
   {
      LocalDebugLog("XmppAgentManagerInterface::unregisterForRemoteSyncImpl(): ignoring remote-sync unregistration, as a remote-sync session: {} has not been established", mSyncSessionHandle);
      return;
   }

   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   syncMgr->disconnect(mSyncSessionHandle);

   mSyncSessionHandle = (-1);
   mSyncRequestHandle = (-1);
   mSyncRevision = (-1);
   mSyncConnectionState = RemoteSyncConnectionState_Disconnecting;
   mSyncHandlingState = RemoteSyncHandlingState_Init;
   mSyncDelayTimer.cancel();
}

int XmppAgentManagerInterface::logout(XmppPushRegistrationHandle xmppPushRegistration)
{
   LocalDebugLog("XmppAgentManagerInterface::logout(): xmpp push reg handle: {}", xmppPushRegistration);
   post(resip::resip_bind(&XmppAgentManagerInterface::logoutImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentManagerInterface::logoutImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   LocalMaxLog("XmppAgentManagerInterface::logoutImpl: xmpp push reg handle: {} remote sync session handle: {}", xmppPushRegistration, mSyncSessionHandle);

   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it == mStateMap.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::logoutImpl(): ignoring logout as push registration handle is invalid: {}", xmppPushRegistration);
      return;
   }

   XmppAgentState* state = it->second;

   if (state)
   {
      // TODO: handling post in following calls ?
      CPCAPI2::XmppAccount::XmppAccountHandle xmppAccountHandle = state->registrationInfo.xmppAccountHandle;
      CPCAPI2::JsonApi::JsonApiUserHandle jsonUserHandle = state->registrationInfo.jsonUserHandle;

      unregisterForRemoteSyncImpl(xmppPushRegistration);
      unregisterForXmppPushNotificationsImpl(xmppPushRegistration);
      // mPushManager->unregisterPushNotificationService(state->registrationInfo.pushNotificationServiceHandle);

      state->isLoggedOut = true;

      XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));

      LocalDebugLog("XmppAgentManagerInterface::logoutImpl(): {} phone: {} account: {} state: {}",
                    static_cast<void*>(this), static_cast<void*>(mPhone), xmppAccountHandle, *state);
      xmppIf->disable(xmppAccountHandle);
      xmppIf->destroy(xmppAccountHandle);
   }
}

int XmppAgentManagerInterface::logoutAccount(XmppAccount::XmppAccountHandle account)
{
   LocalDebugLog("XmppAgentManagerInterface::logoutAccount(): xmpp account handle: {}", account);
   post(resip::resip_bind(&XmppAgentManagerInterface::logoutAccountImpl, this, account));
   return kSuccess;
}

void XmppAgentManagerInterface::logoutAccountImpl(XmppAccount::XmppAccountHandle account)
{
   LocalMaxLog("XmppAgentManagerInterface::logoutAccountImpl: xmpp account handle: {} remote sync session handle: {}", account, mSyncSessionHandle);
   AccountPushRegMap::iterator i = mAccountPushRegMap.find(account);
   
   if (i == mAccountPushRegMap.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::logoutAccountImpl(): ignoring account logout as the xmpp account handle is invalid: {}", account);
      return;
   }
   
   for (cpc::vector<XmppPushRegistrationHandle>::iterator j = i->second.begin(); j != i->second.end(); ++j)
   {
      LocalDebugLog("XmppAgentManagerInterface::logoutAccountImpl(): initiating logout on xmpp push registration handle: {} for account: {}", *j, account);
      logoutImpl(*j);
   }
}

int XmppAgentManagerInterface::requestEventHistory(XmppPushRegistrationHandle xmppPushRegistration)
{
   // TODO: maintaining chat event may be deprecated, as depending on the remote-sync feature to sync messages
   post(resip::resip_bind(&XmppAgentManagerInterface::requestEventHistoryImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentManagerInterface::requestEventHistoryImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   // TODO: maintaining chat event may be deprecated, as depending on the remote-sync feature to sync messages
   LocalMaxLog("XmppAgentManagerInterface::requestEventHistoryImpl(): handle: {} agent state list size: {}", xmppPushRegistration, mStateMap.size());
   XmppAgentStateMap::iterator it = mStateMap.find(xmppPushRegistration);
   if (it != mStateMap.end())
   {
      XmppEventHistory args;
      args.chatEventHistory = it->second->events;
      LocalMaxLog("XmppAgentManagerInterface::requestEventHistoryImpl(): handle: {} has {} events", xmppPushRegistration, args.chatEventHistory.size());
      fireEvent(it->second->appHandler, cpcFunc(XmppAgentHandler::onEventHistory), xmppPushRegistration, args);
      it->second->events.clear();
   }
}

int XmppAgentManagerInterface::queryXmppRegistrationList()
{
   post(resip::resip_bind(&XmppAgentManagerInterface::queryXmppRegistrationListImpl, this));
   return kSuccess;
}

void XmppAgentManagerInterface::queryXmppRegistrationListImpl()
{
   LocalMaxLog("XmppAgentManagerInterface::queryXmppRegistrationListImpl(): agent state list size: {}", mStateMap.size());
   XmppAgentQueryListResult result;

   for (XmppAgentStateMap::iterator i = mStateMap.begin(); i != mStateMap.end(); ++i)
   {
      result.registrationList.push_back(i->first);
   }

   fireEvent(cpcFunc(XmppAgentHandler::onXmppAgentQueryListResult), result);
}

int XmppAgentManagerInterface::queryXmppRegistrationInfo(XmppPushRegistrationHandle xmppPushRegistration)
{
   post(resip::resip_bind(&XmppAgentManagerInterface::queryXmppRegistrationInfoImpl, this, xmppPushRegistration));
   return kSuccess;
}

void XmppAgentManagerInterface::queryXmppRegistrationInfoImpl(XmppPushRegistrationHandle xmppPushRegistration)
{
   LocalMaxLog("XmppAgentManagerInterface::queryXmppRegistrationInfoImpl(): handle: {} agent state list size: {}", xmppPushRegistration, mStateMap.size());
   XmppAgentQueryInfoResult result;
   XmppAgentStateMap::iterator i = mStateMap.find(xmppPushRegistration);
   if (i != mStateMap.end())
   {
      XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));

      result.xmppAccount = xmppIf->getRemoteSyncAccountID(i->second->registrationInfo.xmppAccountHandle);;
      result.xmppPushRegistration = xmppPushRegistration;
      result.xmppAccountHandle = i->second->registrationInfo.xmppAccountHandle;
      result.pushNotificationDev = i->second->registrationInfo.pushNotificationDev;
      result.pushNotificationServiceHandle = i->second->registrationInfo.pushNotificationServiceHandle;
      result.jsonUserHandle = i->second->registrationInfo.jsonUserHandle;
      result.syncSessionHandle = mSyncSessionHandle;
      result.isRegisteredForPush = i->second->isRegisteredForPush;
      result.isLoggedOut = i->second->isLoggedOut;
      result.serviceDownAtMs = i->second->serviceDownAtMs;
   }

   fireEvent(NULL, cpcFunc(XmppAgentHandler::onXmppAgentQueryInfoResult), xmppPushRegistration, result, true);
}

// XmppAccountHandler
int XmppAgentManagerInterface::onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
   mThreadCheck.test();

   // StackLog(<< "XmppAgentManagerInterface::onAccountStatusChanged(): account handle: " << account << " state: " << args.accountStatus << " args: " << args);
   LocalMaxLog("XmppAgentManagerInterface::onAccountStatusChanged(): account handle: {} state: {}", account, args.accountStatus);
   XmppAgentStateMap::iterator iter;

   if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Disconnected ||
      args.accountStatus == XmppAccountStatusChangedEvent::Status_Failure)
   {
      // Try to determine whether the event happened because of an
      // error which will impact the xmpp service as a whole
      switch (args.errorCode)
      {
      //case XmppAccount::Error_None:
      //case XmppAccount::Error_NoHandlerSet:
      case XmppAccount::Error_IoError:
      case XmppAccount::Error_DnsError:
      case XmppAccount::Error_HostNotFound:
      case XmppAccount::Error_ConnectionRefused:
      //case XmppAccount::Error_AlreadyEnabled:
      //case XmppAccount::Error_NotEnabled:
      //case XmppAccount::Error_AlreadyConnected:
      //case XmppAccount::Error_NotConnected: // ?
      //case XmppAccount::Error_ParseError: // ?
      //case XmppAccount::Error_StreamError: // ?
      case XmppAccount::Error_TlsFailed:
      case XmppAccount::Error_CompressionFailed:
      case XmppAccount::Error_UnsupportedAuthMech:
      //case XmppAccount::Error_AuthenticationFailed: // ?
      {
         // DebugLog(<< "XmppAgentManagerInterface::onAccountStatusChanged(): account handle: " << account << " non-recoverable error: " << args.errorCode);
         LocalDebugLog("XmppAgentManagerInterface::onAccountStatusChanged(): account handle: {} non-recoverable error: {}", account, args.errorCode);

         iter = mStateMap.begin();
         for (; iter != mStateMap.end(); ++iter)
         {
            if (iter->second->registrationInfo.xmppAccountHandle == account)
            {
               // StackLog(<< "XmppAgentManagerInterface::onAccountStatusChanged(): account handle: " << account << " marking service down");
               LocalMaxLog("XmppAgentManagerInterface::onAccountStatusChanged(): marking service down for account handle: {}", account);

               iter->second->serviceDownAtMs = resip::ResipClock::getTimeMs();
               break;
            }
         }
         break;
      }
      default:
         // Some error which is considered recoverable
         break;
      }
   }
   else if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Connected)
   {
      iter = mStateMap.begin();
      for (; iter != mStateMap.end(); ++iter)
      {
         if (iter->second->registrationInfo.xmppAccountHandle == account)
         {
            iter->second->serviceDownAtMs = 0;
            break;
         }
      }
   }
   else if (args.accountStatus == XmppAccountStatusChangedEvent::Status_Destroyed)
   {
      StackLog(<< "XmppAgentManagerInterface::onAccountStatusChanged(): account handle: " << account << " destroyed");
      cpc::vector<XmppPushRegistrationHandle> pushRegs;
      AccountPushRegMap::iterator k = mAccountPushRegMap.find(account);
      if (k != mAccountPushRegMap.end())
      {
         JsonApi::JsonApiUserHandle jsonUser = (-1);
         cpc::vector<XmppPushRegistrationHandle> pushRegs = k->second;

         // There would really be only one push registration for an account, because in the current implementation,
         // a new cpcapi runner is created for each web socket connection, i.e. the agent state for the same
         // account but from another device will be maintained in a seperate agent interface
         for (cpc::vector<XmppPushRegistrationHandle>::iterator m = pushRegs.begin(); m != pushRegs.end(); ++m)
         {
            iter = mStateMap.find(*m);
            if (iter != mStateMap.end())
            {
               XmppAgentState* state = iter->second;
               if (state && (state->isLoggedOut))
               {
                  jsonUser = state->registrationInfo.jsonUserHandle;
                  removeXmppRegistrationFromAccountMapping(state->registrationInfo.xmppAccountHandle, *m);
                  removeXmppRegistrationFromJsonUserMapping(state->registrationInfo.jsonUserHandle, *m);
                  removeXmppRegistrationFromPushEndpointMapping(state->registrationInfo.pushNotificationDev, *m);
                  LogoutResult args;
                  args.success = true;
                  fireEvent(state->appHandler, cpcFunc(XmppAgentHandler::onLogout), *m, args);
                  deleteXmppAgentState(*m);
               }
            }
         }

         if (jsonUser != (-1))
         {
            JsonUserPushRegMap::iterator n = mJsonUserPushRegMap.find(jsonUser);
            if (n == mJsonUserPushRegMap.end())
            {
               // No more push registration mappings exist for this json user
               JsonUserList::iterator o = mLoggedOutJsonUsers.find(jsonUser);
               if (o != mLoggedOutJsonUsers.end())
               {
                  stopLogoutCleanupTimer();
                  // Json user found in logout list
                  LocalDebugLog("XmppAgentManagerInterface::onAccountStatusChanged(): proceeding with json server logout for json user: {}", jsonUser);
                  mLoggedOutJsonUsers.erase(jsonUser);
                  LogoutProceedEvent proceedEvent;
                  proceedEvent.observer = this;
                  mJsonServer->proceedWithLogout(jsonUser, proceedEvent);
               }
            }
         }
      }
   }

   return kSuccess;
}

// XmppChatHandler
int XmppAgentManagerInterface::onNewChat(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent& args)
{
   // TODO: Triggering the post due to owner thread assert in DeadlineTimer
   LocalMaxLog("XmppAgentManagerInterface::onNewChat(): chat handle: {} account: {} remote: {} agent state list size: {}",
                 chat, args.account, args.remote, mStateMap.size());
   post(resip::resip_bind(&XmppAgentManagerInterface::onNewChatImpl, this, chat, args));
   return kSuccess;
}

int XmppAgentManagerInterface::onNewChatImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewChatEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onNewChatImpl(): chat handle: {} account: {} agent state list size: {}",
               chat, args.account, mStateMap.size());
   // TODO: maintaining chat event may be deprecated, as depending on the remote-sync feature to sync messages

   XmppChatEvent chatEvent;
   chatEvent.account = args.account;
   chatEvent.chat = chat;
   chatEvent.eventType = XmppChatEventType_NewChat;
   chatEvent.eventId = resip::Data::from(::time(NULL)).c_str();
   chatEvent.newChatEvent = args;

   StackLog(<< "XmppAgentManagerInterface::onNewChatImpl(): agent state list size: " << mStateMap.size() << " chatEvent: " << chatEvent);

   XmppAgentStateMap::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      StackLog(<< "XmppAgentManagerInterface::onNewChatImpl(): xmpp push reg handle: " << it->first << " agent-state: " << *(it->second) << " chat-event count: " << it->second->events.size());
      if (it->second->registrationInfo.xmppAccountHandle == args.account)
      {
         StackLog(<< "XmppAgentManagerInterface::onNewChatImpl(): found xmpp-account: " << args.account << " adding chat: " << chat);
         // OBELISK-3770: Commenting out as redundant if remote-sync is used: it->second->events.push_back(chatEvent);
      }
   }

   return kSuccess;
}

int XmppAgentManagerInterface::onNewMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   // TODO: Triggering the post due to owner thread assert in DeadlineTimer
   StackLog(<< "XmppAgentManagerInterface::onNewMessage(): chat handle: " << chat << " account: " << args.account << " message-id: " << args.messageId << " thread-id: " << args.threadId << " agent state list size: " << mStateMap.size());
   post(resip::resip_bind(&XmppAgentManagerInterface::onNewMessageImpl, this, chat, args));
   return kSuccess;
}
   
int XmppAgentManagerInterface::onNewMessageImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onNewMessageImpl(): chat handle: {} account: {} message-id: {} thread-id: {} agent state list size: {}",
               chat, args.account, args.messageId, args.threadId, mStateMap.size());
      
   XmppChatEvent chatEvent;
   chatEvent.account = args.account;
   chatEvent.chat = chat;
   chatEvent.eventType = XmppChatEventType_NewMessage;
   chatEvent.eventId = resip::Data::from(::time(NULL)).c_str();
   chatEvent.newMessageEvent = args;
   
   addToSyncDelayList(args);
      
   LocalMaxLog("XmppAgentManagerInterface::onNewMessageImpl(): agent state list size: {} chatEvent: {}",
               mStateMap.size(), chatEvent);
   
   sendPushNotification(chat, args);
   
   XmppChatManager* xmppChat = XmppChatManager::getInterface(mPhone);
   xmppChat->notifyMessageDelivered(chat, args.message, XmppChat::MessageDeliveryStatus_Delivered);
      
   return kSuccess;
}
  
int XmppAgentManagerInterface::onNewOutboundMessage(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   // TODO: Triggering the post due to owner thread assert in DeadlineTimer
   StackLog(<< "XmppAgentManagerInterface::onNewOutboundMessage(): chat handle: " << chat << " account: " << args.account << " message-id: " << args.messageId << " thread-id: " << args.threadId << " agent state list size: " << mStateMap.size());
   post(resip::resip_bind(&XmppAgentManagerInterface::onNewOutboundMessageImpl, this, chat, args));
   return kSuccess;
}
   
int XmppAgentManagerInterface::onNewOutboundMessageImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onNewOutboundMessageImpl(): chat handle: {} account: {} message-id: {} thread-id: {} agent state list size: {}",
               chat, args.account, args.messageId, args.threadId, mStateMap.size());
      
   XmppChatEvent chatEvent;
   chatEvent.account = args.account;
   chatEvent.chat = chat;
   chatEvent.eventType = XmppChatEventType_NewMessage;
   chatEvent.eventId = resip::Data::from(::time(NULL)).c_str();
   chatEvent.newMessageEvent = args;
   
   addToSyncDelayList(args);
      
   LocalMaxLog("XmppAgentManagerInterface::onNewOutboundMessageImpl(): agent state list size: {} chatEvent: {}",
               mStateMap.size(), chatEvent);
   
   return kSuccess;
}
  
int XmppAgentManagerInterface::onChatEnded(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent& args)
{
   // TODO: Triggering the post due to owner thread assert in DeadlineTimer
   StackLog(<< "XmppAgentManagerInterface::onChatEnded(): chat handle: " << chat << " account: " << args.account << " agent state list size: " << mStateMap.size());
   post(resip::resip_bind(&XmppAgentManagerInterface::onChatEndedImpl, this, chat, args));
   return kSuccess;
}
   
int XmppAgentManagerInterface::onChatEndedImpl(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::ChatEndedEvent& args)
{
   StackLog(<< "XmppAgentManagerInterface::onChatEndedImpl(): chat handle: " << chat << " account: " << args.account << " agent state list size: " << mStateMap.size());
   // TODO: maintaining chat event may be deprecated, as depending on the remote-sync feature to sync messages
   XmppChatEvent chatEvent;
   chatEvent.account = args.account;
   chatEvent.chat = chat;
   chatEvent.eventType = XmppChatEventType_ChatEnded;
   chatEvent.eventId = resip::Data::from(::time(NULL)).c_str();
   chatEvent.chatEndedEvent = args;

   StackLog(<< "XmppAgentManagerInterface::onChatEndedImpl(): agent state list size: " << mStateMap.size() << " chatEvent: " << chatEvent);
   
   XmppAgentStateMap::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      StackLog(<< "XmppAgentManagerInterface::onChatEndedImpl(): xmpp push reg handle: " << it->first << " agent-state: " << *(it->second) << " chat-event count: " << it->second->events.size());
      if (it->second->registrationInfo.xmppAccountHandle == args.account)
      {
         StackLog(<< "XmppAgentManagerInterface::onChatEndedImpl(): found xmpp-account: " << args.account  << " adding chat: " << chat);
         // OBELISK-3770: Commenting out as redundant if remote-sync is used: it->second->events.push_back(chatEvent);
      }
   }
   return kSuccess;
}

// XmppVCardHandler

int XmppAgentManagerInterface::onVCardFetched(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onVCardFetched(): handle: " << handle);
   post(resip::resip_bind(&XmppAgentManagerInterface::onVCardFetchedImpl, this, handle, evt));
   return kSuccess;
}

int XmppAgentManagerInterface::onVCardFetchedImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardFetchedEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onVCardFetchedImpl(): handle: " << handle);
   return kSuccess;
}

int XmppAgentManagerInterface::onVCardOperationResult(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onVCardOperationResult(): handle: " << handle);
   post(resip::resip_bind(&XmppAgentManagerInterface::onVCardOperationResultImpl, this, handle, evt));
   return kSuccess;
}

int XmppAgentManagerInterface::onVCardOperationResultImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::VCardOperationResultEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onVCardOperationResultImpl(): handle: " << handle);
   return kSuccess;
}

int XmppAgentManagerInterface::onError(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onError(): handle: " << handle);
   post(resip::resip_bind(&XmppAgentManagerInterface::onErrorImpl, this, handle, evt));
   return kSuccess;
}

int XmppAgentManagerInterface::onErrorImpl(CPCAPI2::XmppVCard::XmppVCardHandle handle, const CPCAPI2::XmppVCard::ErrorEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onErrorImpl(): handle: " << handle);
   return kSuccess;
}

bool XmppAgentManagerInterface::sendPushNotification(CPCAPI2::XmppChat::XmppChatHandle chat, const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   mThreadCheck.test();

   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   bool pushRequired = true;
   cpc::string accountId = xmppIf->getRemoteSyncAccountID(args.account);
   if (accountId.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::sendPushNotification(): could not get the remote-sync account-id for: {}", args.account);
   }
   else
   {
      cpc::string remoteId = ((accountId.substr(0,5) == "xmpp:") ? accountId.substr(5, std::string::npos): "");

      if (remoteId.empty())
      {
         LocalDebugLog("XmppAgentManagerInterface::sendPushNotification(): could not get the remote-id from the remote-sync account-id: {}");
      }
      else
      {
         LocalMaxLog("XmppAgentManagerInterface::sendPushNotification(): from: {} account remote-id: {}",
                       args.from, remoteId);

         if (args.from == remoteId)
         {
            LocalMaxLog("XmppAgentManagerInterface::sendPushNotification(): push notification not required as message was itself originated from account: {}", remoteId);
            return true;
         }
      }
   }

   XmppAgentStateMap::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      LocalMaxLog("XmppAgentManagerInterface::sendPushNotification(): xmpp push reg handle: {} agent-state: {}",
                  it->first, *(it->second));
      if (it->second->registrationInfo.xmppAccountHandle == args.account)
      {
         // OBELISK-3770: Commenting out as redundant if remote-sync is used: it->second->events.push_back(chatEvent);

         PushNotificationRequest pushRequest;

         pushRequest.body = args.messageContent;
         // pushRequest.title_loc_key = "tIncomingIm"; // "Incoming message from %@";
         pushRequest.title_loc_key = "Incoming PUSH message from %@";
         pushRequest.title_loc_args.push_back(args.from);

         pushRequest.customData.push_back(CustomDataField("event", "xmppIm"));
         pushRequest.customData.push_back(CustomDataField("messageId", args.messageId));
         pushRequest.customData.push_back(CustomDataField("messageContent", args.messageContent));
         pushRequest.customData.push_back(CustomDataField("chatHandle", (int64_t)chat));
         pushRequest.customData.push_back(CustomDataField("messageHandle", (int64_t)args.message));
         pushRequest.customData.push_back(CustomDataField("threadId", args.threadId));
         pushRequest.customData.push_back(CustomDataField("accountHandle", (int64_t)args.account));

         // Note that "from" and "to" are reserved keywords in the FCM push framework, and push notifications get rejected if included in custom data.
         pushRequest.customData.push_back(CustomDataField("fromUser", args.from));
         pushRequest.customData.push_back(CustomDataField("toUser", args.to));

         pushRequest.customData.push_back(CustomDataField("subject", args.subject));
         pushRequest.customData.push_back(CustomDataField("isDelayedDelivery", args.isDelayedDelivery));

         pushRequest.customData.push_back(CustomDataField("timestamp", args.timestamp));
         pushRequest.customData.push_back(CustomDataField("millisecond", args.millisecond));

         pushRequest.options.push_back(PushNotificationOption_InlineReply);         

         LocalMaxLog("XmppAgentManagerInterface::sendPushNotification(): sending push for account: {} jsonUser: {} pushService: {} chat: {}. {} customData items",
                     args.account, it->second->registrationInfo.jsonUserHandle, it->second->registrationInfo.pushNotificationServiceHandle, chat, pushRequest.customData.size());
         mPushManager->sendPushNotification(it->second->registrationInfo.pushNotificationDev, pushRequest);
      }
   }

   return true;
}

bool XmppAgentManagerInterface::clearPushNotification(XmppAccountHandle account, const CPCAPI2::PushService::CustomData& customData)
{
   LocalDebugLog("XmppAgentManagerInterface::clearPushNotification(): send clear push notification for account: {}", account);
   XmppAgentStateMap::iterator it = mStateMap.begin();
   for (; it != mStateMap.end(); ++it)
   {
      if (it->second->registrationInfo.xmppAccountHandle == account)
      {
         PushNotificationRequest pushRequest;
         
         pushRequest.body = "";
         pushRequest.title_loc_key = "";
         pushRequest.title_loc_args.clear();
         pushRequest.customData = customData;

         LocalDebugLog("XmppAgentManagerInterface::clearPushNotification(): send clear push notification for account: {} jsonUser: {} pushService: {}", account, it->second->registrationInfo.jsonUserHandle, it->second->registrationInfo.pushNotificationServiceHandle);
         mPushManager->sendPushNotification(it->second->registrationInfo.pushNotificationDev, pushRequest);
      }
   }
      
   return true;;
}

void XmppAgentManagerInterface::onEvent(resip::ReadCallbackBase* rcb)
{
   post(rcb);
}
   
bool XmppAgentManagerInterface::sendRemoteSyncItem(const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   mThreadCheck.test();

   LocalDebugLog("XmppAgentManagerInterface::sendRemoteSyncItem(): account: {} updating sync-server with item", args.account);
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalDebugLog("XmppAgentManagerInterface::sendRemoteSyncItem(): sessionHandle: {} ignoring sync request due to invalid session connection state: {}", mSyncSessionHandle, mSyncConnectionState);
      return false;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   
   cpc::string conversationID = getConversationID(args);
   cpc::string threadID = getConversationThreadID(args);
   if (conversationID.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::sendRemoteSyncItem(): sessionHandle: {} ignoring due to invalid conversation-id", mSyncSessionHandle);
      return false;
   }
   
   RemoteSyncItem syncItem;
   syncItem.serverID = 0;
   syncItem.clientID = threadID;
   syncItem.account = xmppIf->getRemoteSyncAccountID(args.account);
   syncItem.source = CPCAPI2::RemoteSync::RemoteSyncItem::unknown;
   syncItem.itemType = CPCAPI2::RemoteSync::RemoteSyncItem::im;
   syncItem.itemRead = false;
   syncItem.itemDeleted = false;
   syncItem.state = args.isOutbound ? 0 : CPCAPI2::RemoteSync::RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
   syncItem.clientTimestamp = (args.timestamp * 1000) + args.millisecond;
   syncItem.from = args.from;
   syncItem.to = args.to;
   syncItem.conversationID = conversationID; // Concatenation of xmpp:<xmpp account>:<remote-id>
   syncItem.contentType = "text/utf8";
   syncItem.content = args.messageContent;
   syncItem.uniqueID = threadID; // Concatenation of <message-id>:<thread-id>
   
   addRemoteSyncItem(syncItem);
         
   printRemoteSyncItems();
         
   LocalMaxLog("XmppAgentManagerInterface::sendRemoteSyncItem(): sessionHandle: {} RemoteSyncItem: {}", mSyncSessionHandle, syncItem);
   cpc::vector<RemoteSyncItem> syncItems;
   syncItems.push_back(syncItem);

   // sendRemoteSyncItem could happen at any moment and therefore is
   // not included as part of the state machine.
   RemoteSyncContext syncContext = mAccountSyncStates[args.account];
   syncContext.requestHandles.insert( syncMgr->syncItems( mSyncSessionHandle, syncItems ));
   mAccountSyncStates[args.account] = syncContext;
   
   return true;
}
   
// RemoteSyncHandler
int XmppAgentManagerInterface::onSetAccounts(const SessionHandle& sessionHandle, const SetAccountsEvent& evt)
{
   LocalDebugLog("XmppAgentManagerInterface::onSetAccounts(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} SetAccountsEvent: {}", sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalDebugLog("XmppAgentManagerInterface::onSetAccounts(): sessionHandle: {} ignoring event due to invalid connection state: ", sessionHandle, mSyncConnectionState);
      return kSuccess;
   }
   
   // TODO: verify that this callback will not be applicable to the xmpp-agent, as the agent is unlikely to modify the list of accounts maintained by the sync session.
  
   return kSuccess;
}
   
int XmppAgentManagerInterface::onNotificationUpdate(const SessionHandle& sessionHandle, const NotificationUpdateEvent& evt)
{
   LocalMaxLog("XmppAgentManagerInterface::onNotificationUpdate(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} NotificationUpdateEvent: {}", sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): sessionHandle: {} ignoring event due to invalid connection state: {}", sessionHandle, mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncRevision < evt.rev)
   {
      StackLog(<< "XmppAgentManagerInterface::onNotificationUpdate(): updating current revision from: " << mSyncRevision << " to: " << evt.rev);
      mSyncRevision = evt.rev;
   }
   else
   {
      LocalMaxLog("XmppAgentManagerInterface::onNotificationUpdate(): ignoring sync notification update as the notification revision: {} is older than our current revision: ", evt.rev, mSyncRevision);
      return kSuccess;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   cpc::vector<RemoteSyncItem>::const_iterator i;
   for (i = evt.items.begin(); i != evt.items.end(); i++)
   {
      RemoteSyncItem item = (*i);
      // Sometimes the notification update does not include the unique-id
      if ((*i).uniqueID.empty())
      {
         LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): remote-sync item with serverID: {} and clentID: {} has an empty uniqueID", (*i).serverID, (*i).clientID);
         cpc::string uid = getUniqueIdFromSyncItemDelayList(*i);
         if (uid.empty())
         {
            LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): ignoring remote-sync item as uid is empty with serverID: {}, account: {}, clientID: {}, item: {}", (*i).serverID, (*i).account, (*i).clientID, item);
            continue;
         }
         
         item.uniqueID = uid;
      }
      
      XmppAccountHandle account = xmppIf->getAccountHandleFromRemoteSyncID((*i).account); // Other options available if we want to try and avoid the synchronous call
      if (account == 0)
      {
         LocalWarningLog("Warning: getAccountHandleFromRemoteSyncID returned 0 for {}", (*i).account);
      }

      cpc::string msgId("");
      cpc::string threadId("");
      
      addRemoteSyncItem(item);
      getChatMessageIdThreadId((*i).uniqueID, msgId, threadId);
      
      const cpc::string fromJid = "xmpp:" + (*i).from;
      if ((*i).account == fromJid) // is there a better way to check? state seems to always be 0
      {
         // don't send pushes for messages sent from the same user on another device
      }
      else if (/*(*i).itemRead ||*/ (*i).itemDeleted ) // OBELISK-4706 -- for now, always send push notifications for IMs, even if marked as read
      {
         // OBELISK-4724 -- disabling attempted clearing of push notifications until we review / target this feature for a particular release
   
         //LocalMaxLog("XmppAgentManagerInterface::onNotificationUpdate(): sending push notification to clear a previous push notification for accountHandle: {}, clientID: {}, item: {}", account, (*i).clientID, item);
         //
         //PushService::CustomData customData;
         //customData.push_back(CustomDataField("event", "remoteSync"));
         //customData.push_back(CustomDataField("syncEvent", "notificationUpdate"));
         //customData.push_back(CustomDataField("serverID", (int64_t)(*i).serverID));
         //customData.push_back(CustomDataField("clientID", (*i).clientID));
         //customData.push_back(CustomDataField("account", (*i).account));
         //customData.push_back(CustomDataField("source", (int32_t)(*i).source));
         //customData.push_back(CustomDataField("itemType", (int32_t)(*i).itemType));
         //customData.push_back(CustomDataField("itemRead", (*i).itemRead));
         //customData.push_back(CustomDataField("itemDeleted", (*i).itemDeleted));
         //customData.push_back(CustomDataField("state", (int32_t)(*i).state));
         //customData.push_back(CustomDataField("clientTimestamp", (int64_t)(*i).clientTimestamp));
         //
         //// Note that "from" and "to" are reserved keywords in the FCM push framework, and push notifications get rejected if included in custom data.
         //customData.push_back(CustomDataField("fromUser", (*i).from));
         //customData.push_back(CustomDataField("toUser", (*i).to));
         //
         //customData.push_back(CustomDataField("conversationID", (*i).conversationID));
         //customData.push_back(CustomDataField("contentType", (*i).contentType));
         //customData.push_back(CustomDataField("content", (*i).content));
         //customData.push_back(CustomDataField("uniqueID", (*i).uniqueID));
         
         //clearPushNotification(account, customData);
      }
      else
      {
         // Possiblity of duplicate push-notifications if this notification update is triggered by a message that was delivered to the client via the xmpp-agent
         if (isInSyncDelayList(item))
         {
            LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): sync-item found in sync delay list for accountHandle: {}, clientID: {}, item: {}", account, (*i).clientID, item);
         }
         else
         {
            // Retrieve the item we just fetched to update the push sent flag
            RemoteSyncItemInfo* existingItemInfo = NULL;
            if (!getRemoteSyncItemInfoByUniqueID(account, (*i).uniqueID, existingItemInfo))
            {
               LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): no sync-item info found for accountHandle: {}, uniqueID: {}, item: {}", account, (*i).uniqueID, item);
               continue;
            }
            
            if (existingItemInfo->pushSent)
            {
               LocalWarningLog("XmppAgentManagerInterface::onNotificationUpdate(): push already sent for sync-item with accountHandle: {}, clientID: {}, item: {}", account, (*i).clientID, item);
               continue;
            }
            
            existingItemInfo->pushSent = true;
            CPCAPI2::XmppChat::NewMessageEvent newMessage;
            newMessage.account = account; // Other options available if we want to try and avoid the synchronous call
            newMessage.message = ::time(NULL); // TODO: Need to get the chat message handle, what if this is a new chat message. We do not necessarily have the chat handle, if this is hybrid deployment (local/remote SDK). Using timestamp to get unique identifier for now.
            newMessage.messageId = msgId;
            newMessage.threadId = ""; // Leaving empty to avoid duplicates, sync server uses the message-id to match other sync-items when the threadId is left empty
            newMessage.from = (*i).from;
            newMessage.to = (*i).to;
            newMessage.messageContent = (*i).content;
            newMessage.htmlText = "";
            newMessage.subject = "";
            newMessage.timestamp = (*i).clientTimestamp / 1000; // Convert to seconds
            newMessage.millisecond = (*i).clientTimestamp % 1000; // Get the milli-seconds
            newMessage.isDelayedDelivery = false;
         
            LocalMaxLog("XmppAgentManagerInterface::onNotificationUpdate(): send push notification and update the sync-item pushSent flag for accountHandle: {}, clientID: {}, messageId: {}", account, (*i).clientID, msgId);
            getChatMessageIdThreadId((*i).uniqueID, newMessage.messageId, newMessage.threadId);
         
            sendPushNotification(0, newMessage);
         }
      }
   }
   
   removeFromSyncDelayList(evt.items);

   printRemoteSyncItems();
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onMessageReactions(const SessionHandle& sessionHandle, const MessageReactionsEvent& evt)
{
   LocalMaxLog("XmppAgentManagerInterface::onMessageReactions(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} NotificationUpdateEvent: {}", sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalWarningLog("XmppAgentManagerInterface::onMessageReactions(): sessionHandle: {} ignoring event due to invalid connection state: {}", sessionHandle, mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncRevision < evt.rev)
   {
      StackLog(<< "XmppAgentManagerInterface::onMessageReactions(): updating current revision from: " << mSyncRevision << " to: " << evt.rev);
      mSyncRevision = evt.rev;
   }
   else
   {
      LocalMaxLog("XmppAgentManagerInterface::onMessageReactions(): ignoring sync notification update as the notification revision: {} is older than our current revision: ", evt.rev, mSyncRevision);
      return kSuccess;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   cpc::vector<RemoteSyncItem>::const_iterator i;
   // TODO: The MessageReactionsEvent (evt) contains a single reaction change (evt.address and evt.value).
   // The current implementation incorrectly tries to iterate over evt.items, which does not exist.
   // To correctly handle this, we need to:
   // 1. Get the message ID from evt.address.
   // 2. Retrieve the current list of reactions for that message ID from the MessageStore.
   // 3. Apply the delta (add or remove evt.value) to the list of reactions.
   // 4. Update the MessageStore with the modified list of reactions using MessageStoreImpl::updateReactions.
   // This will require access to the MessageStoreManager and MessageStoreImpl.

   // For now, commenting out the problematic loop to avoid compilation/runtime errors.
   // The logic below is incorrect for MessageReactionsEvent.
   /*
   for (i = evt.items.begin(); i != evt.items.end(); i++)
   {
       // ... (original code) ...
   }
   removeFromSyncDelayList(evt.items); // This line also needs to be removed/modified
   printRemoteSyncItems(); // This line also needs to be removed/modified
   */

   // Example (conceptual, requires actual MessageStore access and logic for add/remove):
   // XmppAccountHandle account = xmppIf->getAccountHandleFromRemoteSyncID(evt.account); // Assuming evt.account exists or can be derived
   // MessageStoreImpl* messageStore = getMessageStoreForAccountAndConversation(account, evt.address); // Need a way to get this
   // MessageInfo messageInfo;
   // if (messageStore->getMessageInfo(evt.address, messageInfo))
   // {
   //     cpc::vector<cpc::string> currentReactions = messageInfo.reactions;
   //     // Logic to add or remove evt.value from currentReactions
   //     // For example, if evt.value is "X" and it's an add: currentReactions.push_back(evt.value);
   //     // If it's a remove: find and erase evt.value from currentReactions;
   //     messageStore->updateReactions(evt.address, currentReactions);
   // }
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onSyncItemsComplete(const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& evt)
{
   LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} SyncItemsCompleteEvent: {}",
               sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);

   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalWarningLog("XmppAgentManagerInterface::onSyncItemsComplete(): sessionHandle: {} ignoring event due to invalid connection state: {}", sessionHandle, mSyncConnectionState);
      return kSuccess;
   }

   if ((mSyncHandlingState != RemoteSyncHandlingState_Started) && (mSyncHandlingState != RemoteSyncHandlingState_Completed))
   {
      LocalWarningLog("XmppAgentManagerInterface::onSyncItemsComplete(): sessionHandle: {} ignoring event due to invalid handling state: {}", sessionHandle, mSyncHandlingState);
      return kSuccess;
   }

   if (mAccountSyncItems.size() == 0)
   {
      LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): no account mapping exists in sync-item list");
      mSyncHandlingState = RemoteSyncHandlingState_Completed;
      return kSuccess;
   }

   if (mSyncRevision < evt.rev)
   {
      LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): updating current revision from: {} to {}", mSyncRevision, evt.rev);
      mSyncRevision = evt.rev;

      // TODO: for the first sync response that comes due to the empty sync request, it provides the current server sync revision.
      // With db persistence, we will have to manage our current revision, and decide on the fetching accordingly
   }
   else
   {
      LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): ignoring sync update as the revision: {} is older than our current revision: {}", evt.rev, mSyncRevision);

      if (mSyncHandlingState == RemoteSyncHandlingState_Started)
      {
         // Let's set all sync contexts to completed
         for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
         {
            RemoteSyncContext syncContext = mAccountSyncStates[i->first];
            syncContext.syncHandlingState = RemoteSyncHandlingState_Completed;
            syncContext.requestHandles.erase( evt.requestID );
            mAccountSyncStates[i->first] = syncContext;
         }

         mSyncRequestHandle = 0;
         mSyncHandlingState = RemoteSyncHandlingState_Completed;
      }

      return kSuccess;
   }

   if ((mSyncHandlingState == RemoteSyncHandlingState_Started) && (evt.requestID == mSyncRequestHandle) && evt.items.empty())
   {
      // We need to poll the item count for all the accounts
      mSyncRequestHandle = 0;
      mSyncHandlingState = RemoteSyncHandlingState_GetMessageCounts;

      XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
      RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

      cpc::vector<RemoteSyncItem::ItemType> types;
      types.push_back(RemoteSyncItem::im);

      for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
      {
         cpc::string accountID = xmppIf->getRemoteSyncAccountID(i->first);

         RemoteSyncContext syncContext = i->second;
         syncContext.syncHandlingState = RemoteSyncHandlingState_GetMessageCounts;
         syncContext.messageCount = (-1);
         syncContext.unreadCount = (-1);
         syncContext.unreadConversations = (-1);
         syncContext.totalConversations = (-1);
         syncContext.fetchOffset = (-1);
         syncContext.highestClientCreatedTime = (-1);
         syncContext.requestHandles.insert( syncMgr->getMessageCount(sessionHandle, accountID, types ));
         mAccountSyncStates[i->first] = syncContext;
      }
   }
   else
   {
      // Looks like this is a response to an actual sync item request
      XmppAccount::XmppAccountHandle accountHandle = getAccountHandleFromRequestHandle(evt.requestID);
      if (accountHandle == 0)
      {
         LocalWarningLog("XmppAgentManagerInterface::onSyncItemsComplete(): no account mapping exists for this sync-item response, with requestID: {}", evt.requestID);
         return kSuccess;
      }

      RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
      syncContext.requestHandles.erase( evt.requestID );
      syncContext.syncHandlingState = RemoteSyncHandlingState_Completed;
      mAccountSyncStates[accountHandle] = syncContext;

      for (cpc::vector<RemoteSyncItemUpdate>::const_iterator i = evt.items.begin(); i != evt.items.end(); i++)
      {
         if (i->preexists)
         {
            LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): sync-item with clientID: {} already exists on server",  i->clientID);
         }

         RemoteSyncItem* item = NULL;
         if (getRemoteSyncItemByClientID(accountHandle, i->clientID, item))
         {
            // Sync response provides the unique message id user by the server for this message, i.e. serverID, as well as the message status information
            item->serverID = i->serverID;
            item->itemRead = i->itemRead;
            item->itemDeleted = i->itemDeleted;
            item->state = i->itemState;
            item->clientTimestamp = i->clientCreatedTime;
         }
         else
         {
            LocalMaxLog("XmppAgentManagerInterface::onSyncItemsComplete(): no sync-item found for accountHandle: {}, clientID: {}", accountHandle, i->clientID);
            continue;
         }
      }
   }

   return kSuccess;
}
   
int XmppAgentManagerInterface::onUpdateItemComplete(const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onUpdateItemComplete(): sessionHandle: " << sessionHandle << " requestHandle: " << mSyncRequestHandle << " current-revision: " << mSyncRevision << " connection state: " << mSyncConnectionState << " sync handling state: " << mSyncHandlingState << " UpdateItemCompleteEvent: " << evt);
   
   // Not sure about this test. !ds!
   if (mSyncRequestHandle != evt.requestID)
   {
      DebugLog(<< "XmppAgentManagerInterface::onUpdateItemComplete(): sessionHandle: " << sessionHandle << " ignoring event due to invalid request handle: " << evt.requestID);
      return kSuccess;
   }
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      DebugLog(<< "XmppAgentManagerInterface::onUpdateItemComplete(): sessionHandle: " << sessionHandle << " ignoring event due to invalid connection state: " << mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncHandlingState != RemoteSyncHandlingState_Completed)
   {
      DebugLog(<< "XmppAgentManagerInterface::onUpdateItemComplete(): sessionHandle: " << sessionHandle << " ignoring event due to invalid handling state: " << mSyncHandlingState);
      return kSuccess;
   }
   
   // TODO: can we get item updates in states other than completed, and do they need to be handled
   // TODO: verify that this callback will not be applicable to the xmpp-agent, as the agent is unlikely to modify an item maintained by the sync session
   if (mSyncRevision < evt.rev)
   {
      StackLog(<< "XmppAgentManagerInterface::onUpdateItemComplete(): updating current revision from: " << mSyncRevision << " to: " << evt.rev);
      mSyncRevision = evt.rev;
   }
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onFetchRangeComplete(const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& evt)
{
   LocalMaxLog("XmppAgentManagerInterface::onFetchRangeComplete(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} FetchRangeCompleteEvent: {}",
               sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      LocalWarningLog("XmppAgentManagerInterface::onFetchRangeComplete(): sessionHandle: {} ignoring event due to invalid connection state: {}", sessionHandle, mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncHandlingState != RemoteSyncHandlingState_FetchRange)
   {
      LocalWarningLog("XmppAgentManagerInterface::onFetchRangeComplete(): sessionHandle: {} ignoring event due to invalid handling state: {}", sessionHandle, mSyncHandlingState);
      return kSuccess;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   
   XmppAccount::XmppAccountHandle accountHandle = getAccountHandleFromRequestHandle(evt.requestID);
   if (accountHandle == 0)
   {
      LocalWarningLog("XmppAgentManagerInterface::onFetchRangeComplete(): no account mapping exists for this fetch-range response, with requestID: {}", evt.requestID);
      return kSuccess;
   }
   
   RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
   
   for (cpc::vector<RemoteSyncItem>::const_iterator i = evt.items.begin(); i != evt.items.end(); i++)
   {
      addRemoteSyncItem(*i);
   }
   
   printRemoteSyncItems();
   
   if (evt.items.size() == XMPP_AGENT_MAXIMUM_FETCH_MESSAGE_COUNT)
   {
      cpc::vector<RemoteSyncItem::ItemType> types;
      types.push_back(RemoteSyncItem::im);
      
      int64_t lowestClientCreatedTime = 0;
      int64_t highestClientCreatedTime = syncContext.highestClientCreatedTime;
      cpc::string conversationID = ""; // Query for all conversations
      cpc::string accountID = xmppIf->getRemoteSyncAccountID(accountHandle);
      bool includeDeleted = false;
      int messageCount = XMPP_AGENT_MAXIMUM_FETCH_MESSAGE_COUNT;
      int messageOffset = syncContext.fetchOffset + XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT;;
      bool ascending = true;
      
      RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
      syncContext.fetchOffset = messageOffset;
      syncContext.requestHandles.insert( syncMgr->fetchRangeCreatedTime(sessionHandle, lowestClientCreatedTime, highestClientCreatedTime, types, conversationID, accountID, includeDeleted, messageCount, messageOffset, ascending ));
      
      mAccountSyncStates[accountHandle] = syncContext;
      
      LocalMaxLog("XmppAgentManagerInterface::onFetchRangeComplete(): account: {} fetching messsages with lowestTime: {} highestTime: {} messageCount: {} messageOffset: {}", accountHandle, lowestClientCreatedTime, syncContext.highestClientCreatedTime, messageCount, messageOffset);
      
      return kSuccess;
   }
   
   mAccountSyncStates[accountHandle].syncHandlingState = RemoteSyncHandlingState_Completed;
   bool fetchCompleted = true;
   for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
   {
      if (i->second.syncHandlingState != RemoteSyncHandlingState_Completed)
      {
         fetchCompleted = false;
         LocalDebugLog("XmppAgentManagerInterface::onFetchRangeComplete(): account: {} has not yet completed fetch-range", i->first);
         break;
      }
      
      LocalDebugLog("XmppAgentManagerInterface::onFetchRangeComplete(): account: {} has already completed fetch-range", i->first);
   }
   
   if (fetchCompleted)
   {
      LocalMaxLog("XmppAgentManagerInterface::onFetchRangeComplete(): remote-sync initialization completed");
      mSyncHandlingState = RemoteSyncHandlingState_Completed;
   }
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onFetchConversationsComplete(const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): sessionHandle: " << sessionHandle << " requestHandle: " << mSyncRequestHandle << " current-revision: " << mSyncRevision << " connection state: " << mSyncConnectionState << " sync handling state: " << mSyncHandlingState << " FetchConversationsCompleteEvent: " << evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): sessionHandle: " << sessionHandle << " ignoring event due to invalid connection state: " << mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncHandlingState != RemoteSyncHandlingState_FetchConversations)
   {
      DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): sessionHandle: " << sessionHandle << " ignoring event due to invalid handling state: " << mSyncHandlingState);
      return kSuccess;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   
   XmppAccount::XmppAccountHandle accountHandle = getAccountHandleFromRequestHandle(evt.requestID);
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): no account mapping exists for this fetch-conversation response, with requestID: " << evt.requestID);
      return kSuccess;
   }
   
   RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
   
   for (cpc::vector<RemoteSyncConversationThreadItem>::const_iterator i = evt.items.begin(); i != evt.items.end(); i++)
   {
      addRemoteSyncItem((*i).latestMessage);
   }
   
   printRemoteSyncItems();
   
   if (evt.items.size() == XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT)
   {
      // We need to query for more conversations
      int64_t lowestClientCreatedTime = 0;
      int conversationCount = XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT;
      int conversationOffset = syncContext.fetchOffset + XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT;
      
      RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
      syncContext.fetchOffset = conversationOffset;
      syncContext.requestHandles.insert( syncMgr->fetchConversations(sessionHandle, lowestClientCreatedTime, syncContext.highestClientCreatedTime, conversationCount, conversationOffset ));
      
      mAccountSyncStates[accountHandle] = syncContext;
      
      DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): account: " << accountHandle << " fetching conversations with lowestTime: " << lowestClientCreatedTime << " highestTime: " << syncContext.highestClientCreatedTime << " conversationCount: " << conversationCount << " conversationOffset: " << conversationOffset);
      return kSuccess;
   }
      
   mAccountSyncStates[accountHandle].syncHandlingState = RemoteSyncHandlingState_FetchRange;
   bool conversationCountCompleted = true;
   for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
   {
      if (i->second.syncHandlingState != RemoteSyncHandlingState_FetchRange)
      {
         conversationCountCompleted = false;
         DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): account: " << i->first << " has not yet completed fetch-conversations");
         break;
      }
      
      DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): account: " << i->first << " has already completed fetch-conversations");
   }
   
   if (conversationCountCompleted)
   {
      // We need to fetch all the conversation ranges
      mSyncRequestHandle = 0;
      mSyncHandlingState = RemoteSyncHandlingState_FetchRange;
      
      cpc::vector<RemoteSyncItem::ItemType> types;
      types.push_back(RemoteSyncItem::im);
      for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
      {
         int64_t lowestClientCreatedTime = 0;
         int64_t highestClientCreatedTime = syncContext.highestClientCreatedTime;
         cpc::string conversationID = ""; // Query for all conversations
         cpc::string accountID = xmppIf->getRemoteSyncAccountID(i->first);
         bool includeDeleted = false;
         int messageCount = XMPP_AGENT_MAXIMUM_FETCH_MESSAGE_COUNT;
         int messageOffset = 0;
         bool ascending = true;
         
         RemoteSyncContext syncContext = i->second;
         syncContext.syncHandlingState = RemoteSyncHandlingState_FetchRange;
         syncContext.fetchOffset = messageOffset;
         syncContext.requestHandles.insert( syncMgr->fetchRangeCreatedTime(sessionHandle, lowestClientCreatedTime, highestClientCreatedTime, types, conversationID, accountID, includeDeleted, messageCount, messageOffset, ascending ));
         
         mAccountSyncStates[i->first] = syncContext;
         
         DebugLog(<< "XmppAgentManagerInterface::onFetchConversationsComplete(): account: " << i->first << " fetching messages with lowestTime: " << lowestClientCreatedTime << " highestTime: " << highestClientCreatedTime << " messageCount: " << messageCount << " messageOffset: " << messageOffset);
      }
   }
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onConversationUpdated(const SessionHandle& sessionHandle, const ConversationUpdatedEvent& evt)
{
   DebugLog(<< "XmppAgentManagerInterface::onConversationUpdated(): sessionHandle: " << sessionHandle << " requestHandle: " << mSyncRequestHandle << " current-revision: " << mSyncRevision << " connection state: " << mSyncConnectionState << " sync handling state: " << mSyncHandlingState << " ConversationUpdatedEvent: " << evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      DebugLog(<< "XmppAgentManagerInterface::onConversationUpdated(): sessionHandle: " << sessionHandle << " ignoring event due to invalid connection state: " << mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncHandlingState != RemoteSyncHandlingState_Completed)
   {
      DebugLog(<< "XmppAgentManagerInterface::onConversationUpdated(): sessionHandle: " << sessionHandle << " ignoring event due to invalid handling state: " << mSyncHandlingState);
      return kSuccess;
   }
   
   if (mSyncRevision < evt.rev)
   {
      StackLog(<< "XmppAgentManagerInterface::onConversationUpdated(): updating current revision from: " << mSyncRevision << " to: " << evt.rev);
      mSyncRevision = evt.rev;
   }
   else
   {
      InfoLog(<< "XmppAgentManagerInterface::onConversationUpdated(): ignoring sync conversation updated as the notification revision: " << evt.rev << " is older than our current revision: " << mSyncRevision);
      return kSuccess;
   }
   
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   bool conversationFound = false;
   for (RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.begin(); i != mAccountSyncItems.end(); i++)
   {
      StackLog(<< "XmppAgentManagerInterface::onConversationUpdated(): items found for account handle: " << i->first);
      for (RemoteSyncItemMap::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         // TODO: this filter will go through previous conversations as well, as the conversationID will be the same
         if ((j->second->syncItem->conversationID == evt.conversationID) && (j->second->syncItem->clientTimestamp <= evt.highestClientCreatedTime))
         {
            if (conversationFound)
            {
               LocalWarningLog("XmppAgentManagerInterface::onConversationUpdated(): conversation found with conversationID: {}, account: {}, item: {}", j->second->syncItem->conversationID, i->first, *(j->second->syncItem));
            }
            else
            {
               // OBELISK-4724 -- disabling attempted clearing of push notifications until we review / target this feature for a particular release
               //
               //// The push to clear will be sent even if a push had not been sent originally, else we will need to maintain a list of push notifications sent
               //InfoLog(<< "XmppAgentManagerInterface::onConversationUpdated(): sending push notification to clear a previous push notification");
               //
               //PushService::CustomData customData;
               //customData.push_back(CustomDataField("event", "remoteSync"));
               //customData.push_back(CustomDataField("syncEvent", "conversationUpdate"));
               //customData.push_back(CustomDataField("requestID", (int64_t)evt.requestID));
               //customData.push_back(CustomDataField("rev", (int64_t)evt.rev));
               //customData.push_back(CustomDataField("conversationID", evt.conversationID));
               //customData.push_back(CustomDataField("highestClientCreatedTime", (int64_t)evt.highestClientCreatedTime));
               //customData.push_back(CustomDataField("setItemsRead", evt.setItemsRead));
               //customData.push_back(CustomDataField("setItemsDeleted", evt.setItemsDeleted));
               
               //clearPushNotification(xmppIf->getAccountHandleFromRemoteSyncID(j->second->syncItem->account), customData);
            }
            
            conversationFound = true;
            j->second->syncItem->itemRead = evt.setItemsRead;
            j->second->syncItem->itemDeleted = evt.setItemsDeleted;
         }
         else
         {
            //LocalWarningLog("XmppAgentManagerInterface::onConversationUpdated(): conversation-id or timestamp mismatch for sync-item conversationID: {}, event conversationID: {}, sync-item timestamp: {}, event timestamp: {}, account: {}, item: {}", j->second->syncItem->conversationID, evt.conversationID, j->second->syncItem->clientTimestamp, evt.highestClientCreatedTime, i->first, *(j->second->syncItem));
         }
      }
      
      // If the conversation is found for one account, no need to check any other accounts
      if (conversationFound)
      {
         InfoLog(<< "XmppAgentManagerInterface::onConversationUpdated(): conversation updated for account handle: " << i->first);
         break;
      }
   }
   
   return kSuccess;
}

int XmppAgentManagerInterface::onMessageCount(const SessionHandle& sessionHandle, const MessageCountEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onMessageCount(): sessionHandle: " << sessionHandle << " requestHandle: " << mSyncRequestHandle << " current-revision: " << mSyncRevision << " connection state: " << mSyncConnectionState << " sync handling state: " << mSyncHandlingState << " MessageCountEvent: " << evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): sessionHandle: " << sessionHandle << " ignoring event due to invalid connection state: " << mSyncConnectionState);
      return kSuccess;
   }
   
   if (mSyncHandlingState != RemoteSyncHandlingState_GetMessageCounts)
   {
      DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): sessionHandle: " << sessionHandle << " ignoring event due to invalid handling state: " << mSyncHandlingState);
      return kSuccess;
   }
   
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   
   XmppAccount::XmppAccountHandle accountHandle = getAccountHandleFromRequestHandle(evt.requestID);
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): no account mapping exists for this message-count response, with requestID: " << evt.requestID);
      return kSuccess;
   }
   
   RemoteSyncContext syncContext = mAccountSyncStates[accountHandle];
   
   syncContext.messageCount = evt.total;
   syncContext.unreadCount = evt.unread;
   syncContext.unreadConversations = evt.unreadConversations;
   syncContext.totalConversations = evt.totalConversations;
   syncContext.syncHandlingState = RemoteSyncHandlingState_FetchConversations;
   mAccountSyncStates[accountHandle] = syncContext;
   
   bool messageCountCompleted = true;
   for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
   {
      if (i->second.syncHandlingState != RemoteSyncHandlingState_FetchConversations)
      {
         messageCountCompleted = false;
         DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): account: " << i->first << " has not yet received the response to the message-count");
         break;
      }
      
      DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): account: " << i->first << " has already received response to the message-count: " << i->second.messageCount);
   }
   
   if (messageCountCompleted)
   {
      // We need to fetch all the conversations
      mSyncRequestHandle = 0;
      mSyncHandlingState = RemoteSyncHandlingState_FetchConversations;
      
      for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
      {
         int64_t lowestClientCreatedTime = 0;
         int64_t highestClientCreatedTime = mSyncRevision;
         int conversationCount = XMPP_AGENT_MAXIMUM_FETCH_CONVERSATION_COUNT;
         int conversationOffset = 0;
         
         RemoteSyncContext syncContext = i->second;
         syncContext.highestClientCreatedTime = highestClientCreatedTime;
         syncContext.fetchOffset = conversationOffset;
         syncContext.requestHandles.insert( syncMgr->fetchConversations(sessionHandle, lowestClientCreatedTime, highestClientCreatedTime, conversationCount, conversationOffset ));
         
         mAccountSyncStates[i->first] = syncContext;
         
         DebugLog(<< "XmppAgentManagerInterface::onMessageCount(): account: " << i->first << " fetching conversations with lowestTime: " << lowestClientCreatedTime << " highestTime: " << highestClientCreatedTime << " conversationCount: " << conversationCount << " conversationOffset: " << conversationOffset);
      }
   }
   
   return kSuccess;
}

int XmppAgentManagerInterface::onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& evt)
{
   return kSuccess;
}
  
int XmppAgentManagerInterface::onItemsUpdated(const SessionHandle& sessionHandle, const ItemsUpdatedEvent& evt)
{
   return kSuccess;
}

int XmppAgentManagerInterface::onError(const SessionHandle& sessionHandle, const OnErrorEvent& evt)
{
   LocalErrLog("XmppAgentManagerInterface::onError(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} OnErrorEvent: {}", sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onConnectionState(const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt)
{
   mThreadCheck.test();

   LocalDebugLog("XmppAgentManagerInterface::onConnectionState(): sessionHandle: {} requestHandle: {} current-revision: {} connection state: {} sync handling state: {} OnConnectionStateEvent: {}", sessionHandle, mSyncRequestHandle, mSyncRevision, mSyncConnectionState, mSyncHandlingState, evt);
   
   RemoteSyncConnectionState currentState = mSyncConnectionState;
   switch (evt.currentState)
   {
      case RemoteSync::ConnectionState_Connecting: mSyncConnectionState = RemoteSyncConnectionState_Connecting; break;
      case RemoteSync::ConnectionState_Connected: mSyncConnectionState = RemoteSyncConnectionState_Connected; break;
      default: mSyncConnectionState = RemoteSyncConnectionState_Disconnected; break;
   }
   
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   
   if ((currentState != RemoteSyncConnectionState_Connected) && (mSyncConnectionState == RemoteSyncConnectionState_Connected))
   {
      LocalDebugLog("XmppAgentManagerInterface::onConnectionState(): sessionHandle: {} send sync item request as the remote-sync connection has been established", sessionHandle);
      
      mSyncHandlingState = RemoteSyncHandlingState_Started;
      cpc::vector<RemoteSyncItem> syncItems;
      mSyncRequestHandle = syncMgr->syncItems(sessionHandle, syncItems);
   }
   else if ((evt.currentState == RemoteSync::ConnectionState_Disconnected) && (currentState == RemoteSyncConnectionState_Disconnecting))
   {
      LocalDebugLog("XmppAgentManagerInterface::onConnectionState(): sessionHandle: {} remote-sync disconnection has been successful", sessionHandle);
      
      syncMgr->setHandler(sessionHandle, NULL);
      mSyncConnectionState = RemoteSyncConnectionState_Disconnected;
   }
   
   return kSuccess;
}
   
int XmppAgentManagerInterface::onTimestampDelta(const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onTimestampDelta(): sessionHandle: " << sessionHandle << " requestHandle: " << mSyncRequestHandle << " current-revision: " << mSyncRevision << " connection state: " << mSyncConnectionState << " sync handling state: " << mSyncHandlingState << " OnTimestampDeltaEvent: " << evt);
   
   if (mSyncConnectionState != RemoteSyncConnectionState_Connected)
   {
      DebugLog(<< "XmppAgentManagerInterface::onTimestampDelta(): sessionHandle: " << sessionHandle << " ignoring event due to invalid connection state: " << mSyncConnectionState);
      return kSuccess;
   }
   
   return kSuccess;
}

// PushNotificationServiceHandler
int XmppAgentManagerInterface::onNotificationSuccess(PushNotificationServiceHandle h, const NotificationSuccessEvent& evt)
{
   // StackLog(<< "XmppAgentManagerInterface::onNotificationSuccess(): push service handle: " << h << " success event: " << evt);
   post(resip::resip_bind(&XmppAgentManagerInterface::onNotificationSuccessImpl, this, h, evt));
   return kSuccess;
}

void XmppAgentManagerInterface::onNotificationSuccessImpl(PushNotificationServiceHandle h, const NotificationSuccessEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onNotificationSuccess(): push service handle: " << h << " success event: " << evt);
}

int XmppAgentManagerInterface::onNotificationFailure(PushNotificationServiceHandle h, const NotificationFailureEvent& evt)
{
   // StackLog(<< "XmppAgentManagerInterface::onNotificationFailure(): push service handle: " << h << " failure event: status: " << evt.statusCode << " reason: " << evt.reasonCode);
   post(resip::resip_bind(&XmppAgentManagerInterface::onNotificationFailureImpl, this, h, evt));
   return kSuccess;
}

void XmppAgentManagerInterface::onNotificationFailureImpl(PushNotificationServiceHandle h, const NotificationFailureEvent& evt)
{
   LocalMaxLog("XmppAgentManagerInterface::onNotificationFailureImpl(): push service handle: {} failure event: status: {} reason: {}", h, evt.statusCode, evt.reasonCode);

   if ((evt.statusCode == StatusCode_ExpiredDeviceToken) && (evt.reasonCode == ReasonCode_Unregistered))
   {
      PushEndpointRegMap::iterator i = mPushEndpointRegMap.find(evt.device);
      if (i == mPushEndpointRegMap.end())
      {
         LocalDebugLog("XmppAgentManagerInterface::onNotificationFailureImpl(): push service handle: {} device: {} is unregistered with push server, device not found in agent endpoint list",
                       h, evt.device);
      }
      else
      {
         LocalDebugLog("XmppAgentManagerInterface::onNotificationFailureImpl(): push service handle: {} device: {} is unregistered with push server, device found with {}"\
                       "push registration handles", h, evt.device, i->second.size());

         // Remove the states and registrations associated to this endpoint, along with destroying the xmpp accounts
         for (cpc::vector<XmppPushRegistrationHandle>::iterator j = i->second.begin(); j != i->second.end(); ++j)
         {
            logout(*j);
         }
      }
   }
}

int XmppAgentManagerInterface::onNotificationServiceRegistration(PushNotificationServiceHandle h, const NotificationServiceRegistrationEvent& evt)
{
   // StackLog(<< "XmppAgentManagerInterface::onNotificationServiceRegistration(): push service handle: " << h << " notification service registration event: " << evt);
   post(resip::resip_bind(&XmppAgentManagerInterface::onNotificationServiceRegistrationImpl, this, h, evt));
   return kSuccess;
}

void XmppAgentManagerInterface::onNotificationServiceRegistrationImpl(PushNotificationServiceHandle h, const NotificationServiceRegistrationEvent& evt)
{
   StackLog(<< "XmppAgentManagerInterface::onNotificationServiceRegistrationImpl(): push service handle: " << h << " notification service registration event: " << evt);
   if (!evt.success)
   {
      LocalInfoLog("XmppAgentManagerInterface::onNotificationServiceRegistrationImpl(): error registering using push service handle: {} for device: {}", h, evt.device);
   }
}

// JsonServerHandler
int XmppAgentManagerInterface::onNewLogin(JsonApiUserHandle jsonApiUser, const NewLoginEvent& args)
{
   // StackLog(<< "XmppAgentManagerInterface::onNewLogin(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity);
   post(resip::resip_bind(&XmppAgentManagerInterface::onNewLoginImpl, this, jsonApiUser, args));
   return kSuccess;
}

void XmppAgentManagerInterface::onNewLoginImpl(JsonApiUserHandle jsonApiUser, const NewLoginEvent& args)
{
   StackLog(<< "XmppAgentManagerInterface::onNewLoginImpl(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity);
}

/*
int XmppAgentManagerInterface::onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args)
{
   // StackLog(<< "XmppAgentManagerInterface::onReLogin(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity);
   post(resip::resip_bind(&XmppAgentManagerInterface::onReLoginImpl, this, jsonApiUser, args));
   return kSuccess;
}

void XmppAgentManagerInterface::onReLoginImpl(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args)
{
   StackLog(<< "XmppAgentManagerInterface::onReLoginImpl(): jsonApiUser: " << jsonApiUser << " newJsonApiUser: " << args.newJsonApiUser);

   if (args.newJsonApiUser <= 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): jsonApiUser: " << jsonApiUser << " invalid update to jsonApiUser: " << args.newJsonApiUser);
      return;
   }

   JsonUserPushRegMap::iterator i = mJsonUserPushRegMap.find(jsonApiUser);
   if (i == mJsonUserPushRegMap.end())
   {
      // Should not have received this event unless we have a dangling observer
      DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): jsonApiUser: " << jsonApiUser << " not found in json user registration mapping");
   }
   else
   {
      DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity << " xmpp push registration " << i->second.size() << " handles found");
      bool found = false;
      for (cpc::vector<XmppPushRegistrationHandle>::iterator j = i->second.begin(); j != i->second.end(); ++j)
      {
         XmppAgentStateMap::iterator k = mStateMap.find(*j);
         if (k == mStateMap.end())
         {
            DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): xmpp registration: " << *j << " not found in registration state mapping");
         }
         else
         {
            XmppPushRegistrationInfo regInfo = k->second->registrationInfo;

            DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): updating jsonApiUser: " << jsonApiUser << " to new jsonApiUser: " << args.newJsonApiUser << " in xmpp registration state for: " << *j);

            if (mJsonServer)
            {
               // Unsubscribe from json server events for the stale json user handle
               JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
               jsonServerIf->removeSdkObserver(k->second->registrationInfo.jsonUserHandle, this);
               jsonServerIf->addSdkObserver(args.newJsonApiUser, this);
            }
            else
            {
               ErrLog(<< "XmppAgentManagerInterface::onReLoginImpl(): json server has not been initialized");
            }

            k->second->registrationInfo.jsonUserHandle = args.newJsonApiUser;
            found = true;
         }
      }

      if (found)
      {
         mJsonUserPushRegMap[args.newJsonApiUser] = i->second;
         mJsonUserPushRegMap.erase(jsonApiUser);

         JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
         jsonServerIf->removeSdkObserver(jsonApiUser, this);
         jsonServerIf->addSdkObserver(args.newJsonApiUser, this);

         mLoggedOutJsonUsers.erase(jsonApiUser);
         stopLogoutCleanupTimer();
         startLogoutCleanupTimer(XMPP_AGENT_LOGOUT_CLEANUP_TIMEOUT_MSECS);
      }
      else
      {
         DebugLog(<< "XmppAgentManagerInterface::onReLoginImpl(): no xmpp registrations impacted by the update to the new json user: " << args.newJsonApiUser);
      }
   }
}

int XmppAgentManagerInterface::onPreLogout(JsonApiUserHandle jsonApiUser, const PreLogoutEvent& args)
{
   // StackLog(<< "XmppAgentManagerInterface::onPreLogout(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity);
   post(resip::resip_bind(&XmppAgentManagerInterface::onPreLogoutImpl, this, jsonApiUser, args));
   return kSuccess;
}

void XmppAgentManagerInterface::onPreLogoutImpl(JsonApiUserHandle jsonApiUser, const PreLogoutEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onPreLogoutImpl(): jsonApiUser: {} userIdentity: {}", jsonApiUser, args.userIdentity);

   JsonUserPushRegMap::iterator i = mJsonUserPushRegMap.find(jsonApiUser);
   if (i == mJsonUserPushRegMap.end())
   {
      LogoutProceedEvent proceedEvent;
      proceedEvent.observer = this;
      mJsonServer->proceedWithLogout(jsonApiUser, proceedEvent);

      // Should not have received this event unless we have a dangling observer
      LocalDebugLog("XmppAgentManagerInterface::onPreLogoutImpl(): jsonApiUser: {} not found in json user registration mapping", jsonApiUser);
      JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
      jsonServerIf->removeSdkObserver(jsonApiUser, this);
   }
   else
   {
      LocalDebugLog("XmppAgentManagerInterface::onPreLogoutImpl(): jsonApiUser: {} userIdentity: {} xmpp push registration {} handles found", jsonApiUser, args.userIdentity, i->second.size());
      
      for (cpc::vector<XmppPushRegistrationHandle>::iterator j = i->second.begin(); j != i->second.end(); ++j)
      {
         logout(*j);
      }

      mLoggedOutJsonUsers.insert(jsonApiUser);
      stopLogoutCleanupTimer();
      startLogoutCleanupTimer(XMPP_AGENT_LOGOUT_CLEANUP_TIMEOUT_MSECS);
   }
}
*/

int XmppAgentManagerInterface::onLogout(JsonApiUserHandle jsonApiUser, const LogoutEvent& args)
{
   // StackLog(<< "XmppAgentManagerInterface::onLogin(): jsonApiUser: " << jsonApiUser << " userIdentity: " << args.userIdentity);
   post(resip::resip_bind(&XmppAgentManagerInterface::onLogoutImpl, this, jsonApiUser, args));
   return kSuccess;
}

void XmppAgentManagerInterface::onLogoutImpl(JsonApiUserHandle jsonApiUser, const LogoutEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onLogoutImpl(): jsonApiUser: {} userIdentity: {}", jsonApiUser, args.userIdentity);
   JsonApiServerInterface* jsonServerIf = dynamic_cast<JsonApiServerInterface*>(mJsonServer);
   jsonServerIf->removeSdkObserver(jsonApiUser, this);
}

/*
int XmppAgentManagerInterface::onSessionState(JsonApiUserHandle jsonApiUser, const SessionStateEvent& args)
{
   // StackLog(<< "XmppAgentManagerInterface::onLogout(): jsonApiUser: " << jsonApiUser << " active: " << args.isActive);
   post(resip::resip_bind(&XmppAgentManagerInterface::onSessionStateImpl, this, jsonApiUser, args));
   return kSuccess;
}

void XmppAgentManagerInterface::onSessionStateImpl(JsonApiUserHandle jsonApiUser, const SessionStateEvent& args)
{
   LocalMaxLog("XmppAgentManagerInterface::onSessionStateImpl(): jsonApiUser: {} active: {}", jsonApiUser, args.isActive);
}
*/

// DeadlineTimerHandler
void XmppAgentManagerInterface::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == XMPP_AGENT_REMOTE_SYNC_DELAY_TIMER_ID)
   {
      StackLog(<< "XmppAgentManagerInterface::onTimer(): timer-id: " << timerId << " sync-delay list: " << mSyncDelayList.size());

      LocalMaxLog("XmppAgentManagerInterface::onTimer() for XMPP_AGENT_REMOTE_SYNC_DELAY_TIMER_ID; sync delay list size: {}", mSyncDelayList.size());

      if (!mSyncDelayList.empty())
      {
         sendRemoteSyncItem(mSyncDelayList[0]->newMessageEvent);
         delete mSyncDelayList[0];
         mSyncDelayList.erase(mSyncDelayList.begin());
      }

      if (!mSyncDelayList.empty())
      {
         startSyncTimer(resip::ResipClock::getTimeMs() - mSyncDelayList[0]->receivedTimestampMsecs);
      }
   }
   else if (timerId == XMPP_AGENT_LOGOUT_CLEANUP_TIMER_ID)
   {
      // Really should only be one json user per xmpp agent interface
      for (JsonUserList::iterator i = mLoggedOutJsonUsers.begin(); i != mLoggedOutJsonUsers.end(); ++i)
      {
         LocalDebugLog("XmppAgentManagerInterface::onAccountStatusChanged(): proceeding with json server logout for json user due to timeout: {}", *i);
         LogoutProceedEvent proceedEvent;
         proceedEvent.observer = this;
         mJsonServer->proceedWithLogout(*i, proceedEvent);
      }
      mLoggedOutJsonUsers.clear();
   }
   else if (timerId == XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMER_ID)
   {
      // StackLog(<< "XmppAgentManagerInterface::onTimer(): service notification timer-id: " << timerId << " state map size: " << mStateMap.size());
      // LocalMaxLog("XmppAgentManagerInterface::onTimer(): service notification timer-id: {} state map size: {}", timerId, mStateMap.size());

      uint64_t now = resip::ResipClock::getTimeMs();
      auto iter = mStateMap.begin();
      for (; iter != mStateMap.end(); ++iter)
      {
         if (iter->second->serviceDownAtMs > 0 && (now >= iter->second->serviceDownAtMs)
            && (now - iter->second->serviceDownAtMs) >= XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMEOUT_MSECS)
         {
            // If serviceDownAtMs hasn't been reset in 60 minutes, we'll send another notification
            iter->second->serviceDownAtMs += 60 * 60 * 1000;

            // Build a document from the event contents.
            PushNotificationRequest pushRequest;

            CPCAPI2::XmppAccount::XmppAccountManager* xmppAccount = XmppAccountManager::getInterface(mPhone);
            CPCAPI2::XmppAccount::XmppAccountStateManager* xmppAcctState = XmppAccountStateManager::getInterface(xmppAccount);
            cpc::vector<XmppAccountState> accountStateVec;
            xmppAcctState->getStateAllAccounts(accountStateVec);

            // StackLog(<< "XmppAgentManagerInterface::onTimer(): service down timestamp: " << iter->second->serviceDownAtMs << " current timestamp: " << now << " state map size: " << accountStateVec.size());
            LocalInfoLog("XmppAgentManagerInterface::onTimer(): service down timestamp: {} current timestamp: {} state map size: {}", iter->second->serviceDownAtMs, now, accountStateVec.size());

            auto itAcct = accountStateVec.begin();
            for (; itAcct != accountStateVec.end(); ++itAcct)
            {
               if (itAcct->account == iter->second->registrationInfo.xmppAccountHandle)
               {
                  // StackLog(<< "XmppAgentManagerInterface::onTimer(): sending push for service down to account handle: " << itAcct->account);
                  LocalInfoLog("XmppAgentManagerInterface::onTimer(): sending push for service down to account: {} jsonUser: {} pushService: {}", itAcct->account, iter->second->registrationInfo.jsonUserHandle, iter->second->registrationInfo.pushNotificationServiceHandle);


                  pushRequest.customData.push_back(CustomDataField("event", "serviceDown"));
                  pushRequest.customData.push_back(CustomDataField("state", itAcct->accountStatus));
                  pushRequest.customData.push_back(CustomDataField("reasonCode", 0));
                  pushRequest.customData.push_back(CustomDataField("serviceName", getServiceId()));

                  // Send the notification
                  mPushManager->sendPushNotification(iter->second->registrationInfo.pushNotificationDev, pushRequest);

                  break;
               }
            }
         }
         /**
         // Test code to simulate a service down event
         else
         {
            XmppAccountStatusChangedEvent args;
            args.accountStatus = XmppAccount::XmppAccountStatusChangedEvent::Status_Failure;
            args.errorCode = Error_DnsError;
            args.errorText = "DnsError";
            onAccountStatusChanged(iter->second->registrationInfo.xmppAccountHandle, args);

            mAccountServiceNotificationTimer.expires_from_now(XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMEOUT_MSECS);
            mAccountServiceNotificationTimer.async_wait(this, XMPP_AGENT_SERVICE_NOTIFICATIONS_TIMER_ID, NULL);
         }
         */
      }
   }
}

void XmppAgentManagerInterface::startSyncTimer(uint32_t timeoutMs)
{
   LocalMaxLog("XmppAgentManagerInterface::startSyncTimer() for timeout: {} ms", timeoutMs);
   mSyncDelayTimer.expires_from_now(timeoutMs);
   mSyncDelayTimer.async_wait(this, XMPP_AGENT_REMOTE_SYNC_DELAY_TIMER_ID, NULL);
}

void XmppAgentManagerInterface::stopSyncTimer()
{
   mSyncDelayTimer.cancel();
}

void XmppAgentManagerInterface::startLogoutCleanupTimer(uint32_t timeoutMs)
{
   LocalMaxLog("XmppAgentManagerInterface::startLogoutCleanupTimer() for timeout: {} ms", timeoutMs);
   mLogoutCleanupTimer.expires_from_now(timeoutMs);
   mLogoutCleanupTimer.async_wait(this, XMPP_AGENT_LOGOUT_CLEANUP_TIMER_ID, NULL);
}

void XmppAgentManagerInterface::stopLogoutCleanupTimer()
{
   mLogoutCleanupTimer.cancel();
}

void XmppAgentManagerInterface::addToSyncDelayList(const CPCAPI2::XmppChat::NewMessageEvent& newMessageEvent)
{
   SyncDelayInfo* delayInfo = new SyncDelayInfo(resip::ResipClock::getTimeMs(), newMessageEvent);
   mSyncDelayList.push_back(delayInfo);

   LocalMaxLog("XmppAgentManagerInterface::addToSyncDelayList() pushed new item into sync list; new size: {}", mSyncDelayList.size());

   if (mSyncDelayList.size() == 1)
   {
      startSyncTimer(XMPP_AGENT_REMOTE_SYNC_DELAY_MSECS);
   }

   StackLog(<< "XmppAgentManagerInterface::addToSyncDelayList(): sync-delay list: " << mSyncDelayList.size());
}

void XmppAgentManagerInterface::removeFromSyncDelayList(const cpc::vector<RemoteSyncItem>& syncItems)
{
   bool timerStopped = false;
   for (cpc::vector<RemoteSyncItem>::const_iterator i = syncItems.begin(); i != syncItems.end(); i++)
   {
      for (SyncDelayList::iterator j = mSyncDelayList.begin(); j != mSyncDelayList.end(); j++)
      {
         if (isSyncItemSame((*j)->newMessageEvent, (*i)))
         {
            LocalMaxLog("XmppAgentManagerInterface::removeFromSyncDelayList(): removing duplicate sync-item: conversation id {} unique id {}", i->conversationID, i->uniqueID);
            StackLog(<< "XmppAgentManagerInterface::removeFromSyncDelayList(): removing duplicate sync-item: " << (*i));
            if (!timerStopped && (j == mSyncDelayList.begin()))
            {
               LocalMaxLog("XmppAgentManagerInterface::removeFromSyncDelayList(): deciding to call stopSyncTimer()");

               stopSyncTimer();
               timerStopped = true;
            }

            delete (*j);
            mSyncDelayList.erase(j);
            break;
         }
      }
   }
   
   if (timerStopped && (!mSyncDelayList.empty()))
   {
      startSyncTimer(resip::ResipClock::getTimeMs() - mSyncDelayList[0]->receivedTimestampMsecs);
   }
   
   StackLog(<< "XmppAgentManagerInterface::removeFromSyncDelayList(): sync-delay list: " << mSyncDelayList.size());
}
 
bool XmppAgentManagerInterface::isInSyncDelayList(const RemoteSync::RemoteSyncItem& syncItem)
{
   for (SyncDelayList::iterator j = mSyncDelayList.begin(); j != mSyncDelayList.end(); j++)
   {
      if (isSyncItemSame((*j)->newMessageEvent, syncItem))
      {
         StackLog(<< "XmppAgentManagerInterface::isInSyncDelayList(): found sync-item with unique-id: " << syncItem.uniqueID << " in sync delay list");
         return true;
      }
   }
      
   StackLog(<< "XmppAgentManagerInterface::isInSyncDelayList(): sync-item with unique-id: " << syncItem.uniqueID << " not found in sync delay list");
   return false;
}
   
cpc::string XmppAgentManagerInterface::getUniqueIdFromSyncItemDelayList(const RemoteSync::RemoteSyncItem& item)
{
   cpc::string uid = "";
   for (SyncDelayList::iterator j = mSyncDelayList.begin(); j != mSyncDelayList.end(); j++)
   {
      if (isSyncItemSame((*j)->newMessageEvent, item))
      {
         StackLog(<< "XmppAgentManagerInterface::getUniqueIdFromSyncItemDelayList(): Found matching sync-item: " << (*j));
         if ((*j)->newMessageEvent.messageId.empty()) break;
         uid = (*j)->newMessageEvent.messageId + ":" + (*j)->newMessageEvent.threadId;
         break;
      }
   }
      
   return uid;
}
   
bool XmppAgentManagerInterface::isSyncItemSame(const CPCAPI2::XmppChat::NewMessageEvent& newMessage, const RemoteSync::RemoteSyncItem& item)
{
   if (RemoteSyncXmppHelper::RemoteSyncXmppHelper* rsh = RemoteSyncXmppHelper::RemoteSyncXmppHelper::getInterface(mPhone))
   {
      cpc::string uniqueId = rsh->getRemoteSyncUniqueID2(newMessage.messageId, newMessage.threadId);
      if (!uniqueId.empty())
      {
         if (uniqueId == item.uniqueID)
         {
            // .jza. do we still need the old logic below?
            return true;
         }
      }
   }

   // TODO: Improve on this hack comparison

   if ((newMessage.from != item.from) || (newMessage.to != item.to) || (newMessage.messageContent != item.content))
   {
      return false;
   }

   uint64_t newMessageTimestamp = (newMessage.timestamp * 1000) + newMessage.millisecond;
   if ((newMessageTimestamp != item.clientTimestamp)
      && (((newMessageTimestamp > item.clientTimestamp) && ((newMessageTimestamp - item.clientTimestamp) > XMPP_AGENT_REMOTE_SYNC_DELTA_MSECS))
      || ((newMessageTimestamp < item.clientTimestamp) && ((item.clientTimestamp - newMessageTimestamp) > XMPP_AGENT_REMOTE_SYNC_DELTA_MSECS))))
   {
      return false;
   }

   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   cpc::string account = xmppIf->getRemoteSyncAccountID(newMessage.account);
   if (!account.empty() && (account != item.account))
   {
      return false;
   }

   cpc::string chatMessageId("");
   cpc::string chatThreadId("");
   getChatMessageIdThreadId(item.uniqueID, chatMessageId, chatThreadId);
   if (!chatMessageId.empty() && (chatMessageId != newMessage.messageId))
   {
      return false;
   }

   return true;
}

bool XmppAgentManagerInterface::isSyncItemSame(const RemoteSync::RemoteSyncItem& item1, const RemoteSync::RemoteSyncItem& item2)
{
   // TODO: Improve on this hack comparison
   
   if (!item1.clientID.empty() && (item1.clientID == item2.clientID))
   {
      return true;
   }
   
   if (!item1.uniqueID.empty() && (item1.uniqueID == item2.uniqueID))
   {
      return true;
   }
   
   if ((item1.from != item2.from) || (item1.to != item2.to) || (item1.content != item2.content) || (item1.account != item2.account) || (item1.conversationID != item2.conversationID))
   {
      return false;
   }
   
   cpc::string msgId1("");
   cpc::string msgId2("");
   cpc::string tid1("");
   cpc::string tid2("");
   getChatMessageIdThreadId(item1.uniqueID, msgId1, tid1);
   getChatMessageIdThreadId(item2.uniqueID, msgId2, tid2);
   if (!msgId1.empty() && (msgId1 != msgId2))
   {
      return false;
   }

   return true;
}

// Utility Functions

bool XmppAgentManagerInterface::addXmppRegistrationToAccountMapping(XmppAccountHandle xmppAccountHandle, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   AccountPushRegMap::iterator j = mAccountPushRegMap.find(xmppAccountHandle);
   if (j != mAccountPushRegMap.end())
   {
      pushRegs = j->second;
   }
   pushRegs.push_back(xmppPushRegistration);
   mAccountPushRegMap[xmppAccountHandle] = pushRegs;

   return true;
}

bool XmppAgentManagerInterface::addXmppRegistrationToJsonUserMapping(JsonApiUserHandle jsonUserHandle, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   JsonUserPushRegMap::iterator k = mJsonUserPushRegMap.find(jsonUserHandle);
   if (k != mJsonUserPushRegMap.end())
   {
      pushRegs = k->second;
   }
   pushRegs.push_back(xmppPushRegistration);
   mJsonUserPushRegMap[jsonUserHandle] = pushRegs;

   return true;
}

bool XmppAgentManagerInterface::addXmppRegistrationToPushEndpointMapping(PushEndpoint::PushNotificationEndpointId pushEndpointId, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   PushEndpointRegMap::iterator k = mPushEndpointRegMap.find(pushEndpointId);
   if (k != mPushEndpointRegMap.end())
   {
      pushRegs = k->second;
   }
   pushRegs.push_back(xmppPushRegistration);
   mPushEndpointRegMap[pushEndpointId] = pushRegs;
      
   return true;
}

bool XmppAgentManagerInterface::removeXmppRegistrationFromAccountMapping(XmppAccountHandle xmppAccountHandle, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   AccountPushRegMap::iterator k = mAccountPushRegMap.find(xmppAccountHandle);
   if (k != mAccountPushRegMap.end())
   {
      cpc::vector<XmppPushRegistrationHandle> existingPushRegs = k->second;
      // Update the account to registration mapping
      for (cpc::vector<XmppPushRegistrationHandle>::iterator m = existingPushRegs.begin(); m != existingPushRegs.end(); ++m)
      {
         if (xmppPushRegistration != (*m))
            pushRegs.push_back(*m);
      }
   }
   if (pushRegs.size() == 0)
   {
      mAccountPushRegMap.erase(xmppAccountHandle);
   }
   else
   {
      mAccountPushRegMap[xmppAccountHandle] = pushRegs;
   }

   return true;
}

bool XmppAgentManagerInterface::removeXmppRegistrationFromJsonUserMapping(JsonApiUserHandle jsonUserHandle, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   JsonUserPushRegMap::iterator n = mJsonUserPushRegMap.find(jsonUserHandle);
   if (n != mJsonUserPushRegMap.end())
   {
      cpc::vector<XmppPushRegistrationHandle> existingPushRegs = n->second;
      // Update the json user to registration mapping
      for (cpc::vector<XmppPushRegistrationHandle>::iterator o = existingPushRegs.begin(); o != existingPushRegs.end(); ++o)
      {
         if (xmppPushRegistration != (*o))
            pushRegs.push_back(*o);
      }
   }
   if (pushRegs.size() == 0)
   {
      mJsonUserPushRegMap.erase(jsonUserHandle);
   }
   else
   {
      mJsonUserPushRegMap[jsonUserHandle] = pushRegs;
   }

   return true;
}

bool XmppAgentManagerInterface::removeXmppRegistrationFromPushEndpointMapping(PushEndpoint::PushNotificationEndpointId pushEndpointId, XmppPushRegistrationHandle xmppPushRegistration)
{
   cpc::vector<XmppPushRegistrationHandle> pushRegs;
   PushEndpointRegMap::iterator n = mPushEndpointRegMap.find(pushEndpointId);
   if (n != mPushEndpointRegMap.end())
   {
      cpc::vector<XmppPushRegistrationHandle> existingPushRegs = n->second;
      // Update the push endpoint to registration mapping
      for (cpc::vector<XmppPushRegistrationHandle>::iterator o = existingPushRegs.begin(); o != existingPushRegs.end(); ++o)
      {
         if (xmppPushRegistration != (*o))
            pushRegs.push_back(*o);
      }
   }
   if (pushRegs.size() == 0)
   {
      mPushEndpointRegMap.erase(pushEndpointId);
   }
   else
   {
      mPushEndpointRegMap[pushEndpointId] = pushRegs;
   }

   return true;
}

bool XmppAgentManagerInterface::deleteXmppAgentState(XmppPushRegistrationHandle xmppPushRegistration)
{
   XmppAgentStateMap::iterator i = mStateMap.find(xmppPushRegistration);
   if (i != mStateMap.end())
   {
      delete (i->second);
      i->second = NULL;
      mStateMap.erase(xmppPushRegistration);
   }

   return true;
}

cpc::string XmppAgentManagerInterface::getConversationID(const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.find(args.account);
   if (i == mAccountSyncItems.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationID(): could not find conversationID as the xmpp account: {} was not found in sync item list", args.account);
      return "";
   }

   for (RemoteSyncItemMap::iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (((j->second->syncItem->to == args.to) && (j->second->syncItem->from == args.from)) || ((j->second->syncItem->to == args.from) && (j->second->syncItem->from == args.to)))
      {
         return (j->second->syncItem->conversationID);
      }
   }

   // No existing conversation found, could be a new conversation initiated by the remote party
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   cpc::string account = xmppIf->getRemoteSyncAccountID(args.account);
   if (account.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationID(): could not compose conversationID without the xmpp account for message account: {}", args.account);
      return "";
   }

   cpc::string accountId = ((account.substr(0,5) == "xmpp:") ? account.substr(5, std::string::npos): account);
   cpc::string toId = ((args.to.substr(0,5) == "xmpp:") ? args.to.substr(5, std::string::npos): args.to);
   cpc::string fromId = ((args.from.substr(0,5) == "xmpp:") ? args.from.substr(5, std::string::npos): args.from);

   if (accountId.empty() || toId.empty() || fromId.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationID(): could not compose conversationID for account: {} to: {} from: {}", accountId, toId, fromId);
      return "";
   }

   cpc::string remoteId = "";
   if (accountId == toId)
   {
      remoteId = fromId;
   }
   else if (remoteId == fromId)
   {
      remoteId = toId;
   }
   else
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationID(): could not compose conversationID as could not identify the remote-jid for account: {} to: {} from: {}", accountId, toId, fromId);
      return "";
   }

   cpc::string conversationID = account + ":" + remoteId;
   return conversationID;
}

cpc::string XmppAgentManagerInterface::getConversationThreadID(const CPCAPI2::XmppChat::NewMessageEvent& args)
{
   cpc::string threadID = "";
   
   if (args.messageId.empty())
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationThreadID(): message-id not available from new message event for account: {}", args.account);
      return threadID;
   }
   
   threadID = args.messageId + ":";
      
   RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.find(args.account);
   if (i == mAccountSyncItems.end())
   {
      LocalDebugLog("XmppAgentManagerInterface::getConversationThreadID(): could not find conversationID as the xmpp account: {} was not found in sync item list", args.account);
      return threadID;
   }
      
   if (!args.threadId.empty())
   {
      threadID = threadID + args.threadId;
      return threadID;
   }
      
   for (RemoteSyncItemMap::iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (((j->second->syncItem->to == args.to) && (j->second->syncItem->from == args.from)) || ((j->second->syncItem->to == args.from) && (j->second->syncItem->from == args.to)))
      {
         LocalDebugLog("XmppAgentManagerInterface::getConversationThreadID(): found conversation that matches to: {} from: {} with uniqueID: {}", args.to, args.from, j->second->syncItem->uniqueID);
         threadID = getNewUniqueID(threadID.c_str(), j->second->syncItem->uniqueID.c_str());
         return threadID;
      }
   }
      
   // No existing conversation found, could be a new conversation initiated by the remote party, just return the "messageId:"
   return threadID;
}
   
cpc::string XmppAgentManagerInterface::getNewUniqueID(const std::string messageID, const std::string oldUniqueID)
{
   // Unique ID is expected to be of a format <message-id>:<thread-id>
   std::string uniqueID = messageID;
   std::string threadID = "";
      
   boost::tokenizer<boost::char_separator<char>> tokens (oldUniqueID, boost::char_separator<char>(":"));
   int count = 0;
   for (boost::tokenizer<boost::char_separator<char>>::iterator i = tokens.begin(); i != tokens.end(); ++i, count++)
   {
      if (count == 1)
      {
         threadID = (*i);
         break;
      }
   }
      
   if (!threadID.empty())
   {
      uniqueID = uniqueID + threadID.c_str();
   }
      
   return uniqueID.c_str();
}

void XmppAgentManagerInterface::getChatMessageIdThreadId(const cpc::string syncUniqueId, cpc::string& chatMessageId, cpc::string& chatThreadId)
{
   // Remote-Sync Unique ID is expected to be of a format "<message-id>:" or "<message-id>:<thread-id>"
   std::string uniqueId = syncUniqueId.c_str();
   boost::tokenizer<boost::char_separator<char>> tokens (uniqueId, boost::char_separator<char>(":"));
   int count = 0;
   for (boost::tokenizer<boost::char_separator<char>>::iterator i = tokens.begin(); i != tokens.end(); ++i, count++)
   {
      if (count == 0)
      {
         chatMessageId = (*i).c_str();
      }
      else if (count == 1)
      {
         chatThreadId = (*i).c_str();
         break;
      }
   }
}
   
void XmppAgentManagerInterface::addRemoteSyncItem(const RemoteSyncItem& item, bool pushSent)
{
   XmppAccountInterface* xmppIf = dynamic_cast<XmppAccountInterface*>(CPCAPI2::XmppAccount::XmppAccountManager::getInterface(mPhone));
   XmppAccountHandle accountHandle = xmppIf->getAccountHandleFromRemoteSyncID(item.account);
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::addRemoteSyncItem(): no account handle found for sync-item account-id: " << item.account);
      return;
   }
   
   RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::addRemoteSyncItem(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return;
   }
   
   if (item.uniqueID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::addRemoteSyncItem(): ignoring sync-item due to invalid uniqueID, account handle: " << accountHandle);
      return;
   }
   
   RemoteSyncItemMap::iterator j = i->second.find(item.uniqueID);
   if (j == i->second.end())
   {
      i->second[item.uniqueID] = new RemoteSyncItemInfo(new RemoteSyncItem(item), pushSent);
      DebugLog(<< "XmppAgentManagerInterface::addRemoteSyncItem(): adding item with uniqueID: " << item.uniqueID << " to mapping for account handle: " << accountHandle << " item count: " << i->second.size() << " item: " << item);
      return;
   }
   
   // Check the revision, in case our sync item is newer than this update
   *(i->second[item.uniqueID]->syncItem) = item;
   DebugLog(<< "XmppAgentManagerInterface::addRemoteSyncItem(): updating item with uniqueID: " << item.uniqueID << " in mapping for account handle: " << accountHandle << " item count: " << i->second.size() << " item: " << item);
}
   
bool XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& uniqueID, RemoteSyncItem*& item) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(): invalid account handle: 0");
      return false;
   }
   
   if (uniqueID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(): invalid uniqueID");
      return false;
   }
   
   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }
      
   RemoteSyncItemMap::const_iterator j = i->second.find(uniqueID);
   if (j == i->second.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(): no item found with uniqueID: " << uniqueID << " for account handle: " << accountHandle);
      return false;
   }

   StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByUniqueID(): found item with uniqueID: " << uniqueID << " for account handle: " << accountHandle);
   item = j->second->syncItem;
   return true;
}
 
bool XmppAgentManagerInterface::getRemoteSyncItemByServerID(XmppAccount::XmppAccountHandle accountHandle, RemoteSync::ServerID serverID, RemoteSyncItem*& item) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByServerID(): invalid account handle: 0");
      return false;
   }
   
   if (serverID == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByServerID(): invalid serverID: 0");
      return false;
   }
   
   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByServerID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }
   
   for (RemoteSyncItemMap::const_iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (j->second->syncItem->serverID == serverID)
      {
         StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByServerID(): found item with serverID: " << serverID << " for account handle: " << accountHandle);
         item = j->second->syncItem;
         return true;
      }
   }

   DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByServerID(): no item found with serverID: " << serverID << " for account handle: " << accountHandle);
   return false;
}

bool XmppAgentManagerInterface::getRemoteSyncItemByClientID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& clientID, RemoteSync::RemoteSyncItem*& item) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByClientID(): invalid account handle: 0");
      return false;
   }
      
   if (clientID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByClientID(): invalid clientID: " << clientID);
      return false;
   }
      
   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByClientID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }
      
   for (RemoteSyncItemMap::const_iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (j->second->syncItem->clientID == clientID)
      {
         StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByClientID(): found item with clientID: " << clientID << " for account handle: " << accountHandle);
         item = j->second->syncItem;
         return true;
      }
   }
      
   DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemByClientID(): no item found with clientID: " << clientID << " for account handle: " << accountHandle);
   return false;
}
   
bool XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& uniqueID, RemoteSyncItemInfo*& itemInfo) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(): invalid account handle: 0");
      return false;
   }
      
   if (uniqueID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(): invalid uniqueID: " << uniqueID);
      return false;
   }
      
   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }
      
   for (RemoteSyncItemMap::const_iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (j->second->syncItem->uniqueID == uniqueID)
      {
         StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(): found item with uniqueID: " << uniqueID << " for account handle: " << accountHandle);
         itemInfo = j->second;
         return true;
      }
   }
      
   DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByUniqueID(): no item found with uniqueID: " << uniqueID << " for account handle: " << accountHandle);
   return false;
}
   
bool XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(XmppAccount::XmppAccountHandle accountHandle, RemoteSync::ServerID serverID, RemoteSyncItemInfo*& itemInfo) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(): invalid account handle: 0");
      return false;
   }
      
   if (serverID == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(): invalid serverID: " << serverID);
      return false;
   }
      
   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }
      
   for (RemoteSyncItemMap::const_iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (j->second->syncItem->serverID == serverID)
      {
         StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(): found item with serverID: " << serverID << " for account handle: " << accountHandle);
         itemInfo = j->second;
         return true;
      }
   }
      
   DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByServerID(): no item found with serverID: " << serverID << " for account handle: " << accountHandle);
   return false;
}
   
bool XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(XmppAccount::XmppAccountHandle accountHandle, const cpc::string& clientID, RemoteSyncItemInfo*& itemInfo) const
{
   if (accountHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(): invalid account handle: 0");
      return false;
   }

   if (clientID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(): invalid clientID: " << clientID);
      return false;
   }

   RemoteSyncAccountItemMap::const_iterator i = mAccountSyncItems.find(accountHandle);
   if (i == mAccountSyncItems.end())
   {
      DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(): no mapping found for account handle: " << accountHandle << " in sync-item list");
      return false;
   }

   for (RemoteSyncItemMap::const_iterator j = i->second.begin(); j != i->second.end(); j++)
   {
      if (j->second->syncItem->clientID == clientID)
      {
         StackLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(): found item with clientID: " << clientID << " for account handle: " << accountHandle);
         itemInfo = j->second;
         return true;
      }
   }

   DebugLog(<< "XmppAgentManagerInterface::getRemoteSyncItemInfoByClientID(): no item found with clientID: " << clientID << " for account handle: " << accountHandle);
   return false;
}
   
XmppAccount::XmppAccountHandle XmppAgentManagerInterface::getAccountHandleFromRequestHandle(RemoteSync::RequestHandle requestHandle)
{
   if (requestHandle == 0)
   {
      DebugLog(<< "XmppAgentManagerInterface::getAccountHandleFromRequestHandle(): invalid request handle: 0");
      return 0;
   }

   for (RemoteSyncAccountStateMap::iterator i = mAccountSyncStates.begin(); i != mAccountSyncStates.end(); i++)
   {
      std::set< CPCAPI2::RemoteSync::RequestHandle >& handles(( i->second ).requestHandles );
      if( handles.find( requestHandle ) != handles.end() )
      {
         return i->first;
      }
   }

   DebugLog(<< "XmppAgentManagerInterface::getAccountHandleFromRequestHandle(): no accountHandle found for requestHandle: " << requestHandle);
   return 0;
}

XmppAccount::XmppAccountHandle XmppAgentManagerInterface::getAccountHandleFromConversationId(cpc::string conversationID)
{
   if (conversationID.empty())
   {
      DebugLog(<< "XmppAgentManagerInterface::getAccountHandleFromConversationId(): invalid conversation-id");
      return 0;
   }

   for (RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.begin(); i != mAccountSyncItems.end(); i++)
   {
      for (RemoteSyncItemMap::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         if (j->second->syncItem->conversationID == conversationID)
         {
            return (i->first);
         }
      }
   }

   DebugLog(<< "XmppAgentManagerInterface::getAccountHandleFromConversationId(): no accountHandle found for conversation-id: " << conversationID);
   return 0;
}

void XmppAgentManagerInterface::printRemoteSyncItems()
{
   std::stringstream os;
   for (RemoteSyncAccountItemMap::iterator i = mAccountSyncItems.begin(); i != mAccountSyncItems.end(); i++)
   {
      os << std::endl << "\tAccount: " << i->first << " RemoteSyncItems: " << std::endl;
      for (RemoteSyncItemMap::iterator j = i->second.begin(); j != i->second.end(); j++)
      {
         os << "\t\t\t" << j->first << ": " << *(j->second->syncItem) << std::endl;
      }
   }

   StackLog(<< "XmppAgentManagerInterface::printRemoteSyncItems(): " << std::endl << os.str());
}

cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& info)
{
   std::stringstream ss;
   ss << "xmpp-account: " << info.xmppAccountHandle << " device-handle: " << info.pushNotificationDev << " service-handle: " << info.pushNotificationServiceHandle << " json-user-handle: " << info.jsonUserHandle << " server-url: " << info.pushServerUrl;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppChatEventType& type)
{
   switch (type)
   {
      case XmppChatEventType_NewChat: return "newChat";
      case XmppChatEventType_NewMessage: return "newMessage";
      case XmppChatEventType_ChatEnded: return "chatEnded";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::XmppAgent::XmppChatEvent& event)
{
   std::stringstream ss;
   ss << "event-id: " << event.eventId << " chat: " << event.chat << " xmpp-account: " << event.account << " event-type: " << event.eventType;
   return ss.str().c_str();
}

cpc::string get_debug_string(const cpc::vector<CPCAPI2::XmppAgent::XmppChatEvent>& events)
{
   std::stringstream ss;
   ss << "Chat event count: " << events.size();
   for (cpc::vector<XmppChatEvent>::const_iterator i = events.begin(); i != events.end(); i++)
   {
      ss << " {" << (*i) << "}";
   }
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& info)
{
   os << CPCAPI2::XmppAgent::get_debug_string(info);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppChatEventType& type)
{
   os << CPCAPI2::XmppAgent::get_debug_string(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::XmppAgent::XmppChatEvent& event)
{
   os << CPCAPI2::XmppAgent::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const cpc::vector<CPCAPI2::XmppAgent::XmppChatEvent>& events)
{
   os << CPCAPI2::XmppAgent::get_debug_string(events);
   return os;
}

}

}

#endif
