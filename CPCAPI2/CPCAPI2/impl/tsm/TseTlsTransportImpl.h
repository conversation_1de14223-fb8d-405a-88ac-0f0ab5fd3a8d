#pragma once

#ifdef USE_SSL

#include "TseTlsTransport.h"

namespace resip
{
   class TseTlsTransportImpl : public TseTlsTransport
   {
   public:
      TseTlsTransportImpl(Fifo<TransactionMessage>& fifo,
                   int portNum,
                   IpVersion version,
                   const Data& interfaceObj,
                   Security& security,
                   const Data& sipDomain, 
                   SecurityTypes::SSLType sslType,
                   AfterSocketCreationFuncPtr afterSockCreationFunc,
                   BeforeSocketCloseFuncPtr beforeSockCloseFunc,
                   Compression &compression,
		             unsigned transportFlags);
      virtual ~TseTlsTransportImpl();

      virtual bool hasSpecificContact() const { return true; }
   };
}

#endif /* USE_SSL */