#pragma once

#include <resip/stack/InternalTransport.hxx>

namespace resip
{
   class TseInternalTransport : public InternalTransport
   {
   public:
      TseInternalTransport(Fifo<TransactionMessage>& fifo,
                      int portNum,
                      IpVersion version,
                      const Data& interfaceObj,
                      void* tunnelHandle);
      ~TseInternalTransport();

      Socket internal_socket(TransportType type, IpVersion ipVer);
      void bind();
   protected:
      void* mTunnelHandle;
   };
}
