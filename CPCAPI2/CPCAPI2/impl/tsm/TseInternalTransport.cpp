#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseInternalTransport.h"
#include "rutil/Logger.hxx"

#include <tsc_control_api.h>
#include <tsc_socket_api.h>
#include <tsc_version.h>

#define RESIPROCATE_SUBSYSTEM Subsystem::TRANSPORT

namespace resip
{
   TseInternalTransport::TseInternalTransport(Fifo<TransactionMessage>& fifo,
      int portNum,
      IpVersion version,
      const Data& interfaceObj,
      void* tunnelHandle)
      : InternalTransport(fifo, portNum, version, interfaceObj),
        mTunnelHandle(tunnelHandle)
   {
   }

   TseInternalTransport::~TseInternalTransport()
   {
      if (mPollItemHandle)
         mPollGrp->delPollItem(mPollItemHandle);
      if (mInterruptorHandle)
         mPollGrp->delPollItem(mInterruptorHandle);

      if  (mFd != INVALID_SOCKET)
      {
         //DebugLog (<< "Closing " << mFd);
         tsc_close(mFd);
      }
      mFd = -2;
      if(!mTxFifo.empty())
      {
         WarningLog(<< "TX Fifo non-empty in ~InternalTransport! Has " << mTxFifo.size() << " messages.");
      }
   }

   Socket
   TseInternalTransport::internal_socket(TransportType type, IpVersion ipVer)
   {
      Socket fd;
      switch (type)
      {
         case UDP:
#ifdef USE_IPV6
            fd = tsc_socket((tsc_handle)mTunnelHandle, ipVer == V4 ? PF_INET : PF_INET6, SOCK_DGRAM, IPPROTO_UDP);
#else
            fd = tsc_socket((tsc_handle)mTunnelHandle, PF_INET, SOCK_DGRAM, IPPROTO_UDP);
#endif
            break;
         case TCP:
         case TLS:
#ifdef USE_IPV6
            fd = tsc_socket((tsc_handle)mTunnelHandle, ipVer == V4 ? PF_INET : PF_INET6, SOCK_STREAM, 0);
#else
            fd = tsc_socket((tsc_handle)mTunnelHandle, PF_INET, SOCK_STREAM, 0);
#endif
            break;
         default:
            InfoLog (<< "Try to create an unsupported socket type: " << Tuple::toData(type));
            assert(0);
            throw Transport::Exception("Unsupported transport", __FILE__,__LINE__);
      }

      if ( fd == INVALID_SOCKET )
      {
         int e = tsc_get_errno();
         ErrLog (<< "Failed to create socket: " << get_errno_string(e));
         throw Transport::Exception("Can't create tunnel transport", __FILE__,__LINE__);
      }

#ifdef USE_IPV6
#ifdef __linux__
      int on = 1;
      if (ipVer == V6)
      {
         if ( tsc_setsockopt(fd, IPPROTO_IPV6, IPV6_V6ONLY, (char*)&on, sizeof(on)) )
         {
            int e = tsc_get_errno();
            InfoLog (<< "Couldn't set sockoptions IPV6_V6ONLY: " << get_errno_string(e));
            //error(e);
            throw Exception("Failed setsockopt", __FILE__,__LINE__);
         }
      }
#endif
#endif

      DebugLog (<< "Creating fd=" << fd << (ipVer == V4 ? " V4/" : " V6/") << (type == UDP ? "UDP" : "TCP"));

      return fd;
   }

   void
   TseInternalTransport::bind()
   {
      DebugLog (<< "Binding to " << Tuple::inet_ntop(mTuple));

      if ( tsc_bind( mFd, &mTuple.getMutableSockaddr(), mTuple.length()) == SOCKET_ERROR )
      {
         int e = tsc_get_errno();
         ErrLog(<< get_errno_string(e));
         if ( e == EADDRINUSE )
         {
            //error(e);
            ErrLog (<< mTuple << " already in use ");
            throw Transport::Exception("port already in use", __FILE__,__LINE__);
         }
         else
         {
            //error(e);
            ErrLog (<< "Could not bind to " << mTuple);
            throw Transport::Exception("Could not use port", __FILE__,__LINE__);
         }
      }
      
      // If we bound to port 0, then query OS for assigned port number
      if(mTuple.getPort() == 0)
      {
#ifdef __THREADX
         int len = sizeof(mTuple.getMutableSockaddr());
#else
         socklen_t len = sizeof(mTuple.getMutableSockaddr());
#endif
         if(tsc_getsockname(mFd, &mTuple.getMutableSockaddr(), &len) == SOCKET_ERROR)
         {
            int e = tsc_get_errno();
            ErrLog (<<"getsockname failed, error=" << e);
            throw Transport::Exception("Could not query port", __FILE__,__LINE__);
         }
      }
      
      int flags = tsc_fcntl(mFd, F_GETFL, 0);
      flags = tsc_fcntl(mFd, F_SETFL, flags | O_NONBLOCK);
      if (flags != 0)
      {
         ErrLog (<< "Could not make socket non-blocking " << get_errno_string(tsc_get_errno()));
         throw Transport::Exception("Failed making socket non-blocking", __FILE__,__LINE__);
      }
      
      if (mAfterSocketCreationFunc)
      {
         mAfterSocketCreationFunc(mFd, transport(), __FILE__, __LINE__);
      }
   }
}

#endif