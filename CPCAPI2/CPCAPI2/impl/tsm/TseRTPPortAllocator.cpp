#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseRTPPortAllocator.h"
#include "TseTunnelManager.h"
#include <rutil/Logger.hxx>
#include <resip/stack/Tuple.hxx>

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::TRANSPORT

namespace CPCAPI2
{

TseRTPPortAllocator::TseRTPPortAllocator(recon::UserAgent* ua, void* tscconfig)
   : RTPPortAllocator(ua),
   mHandle(((TseTunnelConfig*)tscconfig)->handle)
{
}

bool TseRTPPortAllocator::udpPortAvailable(int port, const resip::Data& targetInterfaceIp)
{
   // there is no guarantee the port will still be available after moment after we check
 
   resip::Tuple t(targetInterfaceIp, port, resip::UDP);
   int fd = tsc_socket(mHandle, t.ipVersion() == resip::V6 ? AF_INET6 : AF_INET, SOCK_DGRAM, IPPROTO_UDP);
   if (fd == -1)
   {
      int e = tsc_get_errno();
      ErrLog(<< "Failed to create socket: " << get_errno_string(e));
      return false;
   }
   if (tsc_bind(fd, &t.getMutableSockaddr(), t.length()) == -1)
   {
      int e = tsc_get_errno();
      WarningLog(<< "Could not bind to " << t << ". " << get_errno_string(e));
      tsc_close(fd);
      return false;
   }
   tsc_close(fd);

   return true;
}
}
#endif