#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseTransportHelper.h"
#include "resip/stack/InternalTransport.hxx"
#include "resip/stack/SipMessage.hxx"
#include "rutil/Logger.hxx"

#define RESIPROCATE_SUBSYSTEM Subsystem::TRANSPORT

namespace resip
{
   Socket tsc_internal_socket(unsigned& transportFlags, TransportType type, IpVersion ipVer)
   {
      Socket fd;
      switch (type)
      {
         case UDP:
#ifdef USE_IPV6
            fd = tsc_socket((tsc_handle)transportFlags, ipVer == V4 ? PF_INET : PF_INET6, SOCK_DGRAM, IPPROTO_UDP);
#else
            fd = tsc_socket((tsc_handle)transportFlags, PF_INET, SOCK_DGRAM, IPPROTO_UDP);
#endif
            break;
         case TCP:
         case TLS:
#ifdef USE_IPV6
            fd = tsc_socket((tsc_handle)transportFlags, ipVer == V4 ? PF_INET : PF_INET6, SOCK_STREAM, 0);
#else
            fd = tsc_socket((tsc_handle)transportFlags, PF_INET, SOCK_STREAM, 0);
#endif
            break;
         default:
            InfoLog (<< "Try to create an unsupported socket type: " << Tuple::toData(type));
            assert(0);
            throw Transport::Exception("Unsupported transport", __FILE__,__LINE__);
      }

      if ( fd == INVALID_SOCKET )
      {
         int e = tsc_get_errno();
         ErrLog (<< "Failed to create socket: " << get_errno_string(e));
         throw Transport::Exception("Can't create TcpBaseTransport", __FILE__,__LINE__);
      }
	
	   // !jza! fix for writing to tcp socket (broken pipe) that results in sigpipe killing process
      // need to investigate further why this was not an issue before
#ifdef SO_NOSIGPIPE
      int on = 1;
      if ( tsc_setsockopt ( fd, SOL_SOCKET, SO_NOSIGPIPE, (char*)&on, sizeof(on)) )
      {
	      int e = tsc_get_errno();
         InfoLog (<< "Couldn't set sockoptions SO_NOSIGPIPE: " << get_errno_string(e));
         Transport::error(e);
         throw Transport::Exception("Failed setsockopt", __FILE__,__LINE__);
      }
#endif

      DebugLog (<< "Creating fd=" << fd << (ipVer == V4 ? " V4/" : " V6/") << (type == UDP ? "UDP" : "TCP"));
      transportFlags = 0;
      return fd;
   }

   int tsc_internal_connect (int s, const struct sockaddr *name, int namelen)
   {
      struct sockaddr name_copy = sockaddr(*name);
      return tsc_connect(s, &name_copy, namelen);
   }

   int tsc_internal_sendto (int s, const char *buf, int len, int flags, const struct sockaddr *to, int tolen)
   {
      char *buf_copy = strdup(buf);
      struct sockaddr to_copy = sockaddr(*to);
      int bytesSent = tsc_sendto(s, buf_copy, len, flags, &to_copy, tolen);
      free(buf_copy);
      return bytesSent;
   }

   bool TseMakeSocketNonBlocking(int fd)
   {
      int flags = tsc_fcntl(fd, F_GETFL, 0);
      flags = tsc_fcntl(fd, F_SETFL, flags | O_NONBLOCK);
	   if ( flags != 0 )
	   {
		   return false;
	   }
      return true;
   }

   void tsc_internal_bind(Socket &mFd, resip::Tuple &mTuple)
   {
      DebugLog (<< "Binding to " << Tuple::inet_ntop(mTuple));

      if ( tsc_bind( mFd, &mTuple.getMutableSockaddr(), mTuple.length()) == SOCKET_ERROR )
      {
         int e = tsc_get_errno();
         if ( e == EADDRINUSE )
         {
            Transport::error(e);
            ErrLog (<< mTuple << " already in use ");
            throw Transport::Exception("port already in use", __FILE__,__LINE__);
         }
         else
         {
            Transport::error(e);
            ErrLog (<< "Could not bind to " << mTuple);
            throw Transport::Exception("Could not use port", __FILE__,__LINE__);
         }
      }

      // If we bound to port 0, then query OS for assigned port number
      if(mTuple.getPort() == 0)
      {
         socklen_t len = sizeof(mTuple.getMutableSockaddr());
         if(tsc_getsockname(mFd, &mTuple.getMutableSockaddr(), &len) == SOCKET_ERROR)
         {
            int e = tsc_get_errno();
            ErrLog (<<"getsockname failed, error=" << e);
            throw Transport::Exception("Could not query port", __FILE__,__LINE__);
         }
      }

      bool ok = TseMakeSocketNonBlocking(mFd);
      if ( !ok )
      {
         ErrLog (<< "Could not make socket non-blocking ");
         throw Transport::Exception("Failed making socket non-blocking", __FILE__,__LINE__);
      }

      // Tunnelling doesn't use this function
      /*if (mAfterSocketCreationFunc)
      {
         mAfterSocketCreationFunc(mFd, transport(), __FILE__, __LINE__);
      }*/
   }

   // called from constructor of TcpTransport
   void tsc_tcp_base_init(Socket &mFd, unsigned& mTransportFlags, resip::Tuple &mTuple)
   {
      if ( (mTransportFlags & RESIP_TRANSPORT_FLAG_NOBIND)!=0 )
      {
         return;
      }

      DebugLog (<< "Opening TCP " << mFd);

      // tsc_setsockopt doesn't support SO_REUSEADDR
      /*int on = 1;
#if !defined(WIN32)
      if ( tsc_setsockopt ( mFd, SOL_SOCKET, SO_REUSEADDR, (char*)&on, sizeof(on)) )
#else
      if ( tsc_setsockopt ( mFd, SOL_SOCKET, SO_REUSEADDR, (char*)&on, sizeof(on)) )
#endif
      {
	      int e = tsc_get_errno();
         InfoLog (<< "Couldn't set sockoptions SO_REUSEPORT | SO_REUSEADDR: " << get_errno_string(e));
         Transport::error(e);
         throw Transport::Exception("Failed setsockopt", __FILE__,__LINE__);
      }*/

      tsc_internal_bind(mFd, mTuple);
      TseMakeSocketNonBlocking(mFd);

      // do the listen, seting the maximum queue size for compeletly established
      // sockets -- on linux, tcp_max_syn_backlog should be used for the incomplete
      // queue size(see man listen)
      int e = tsc_listen(mFd,64 );

      if (e != 0 )
      {
         int e = tsc_get_errno();
         InfoLog (<< "Failed listen " << get_errno_string(e));
         Transport::error(e);
         // !cj! deal with errors
	      throw Transport::Exception("Address already in use", __FILE__,__LINE__);
      }
   }
}
#endif