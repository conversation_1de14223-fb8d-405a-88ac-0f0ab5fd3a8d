#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "cpcapi2defs.h"

#include "rutil/TransportType.hxx"
#include <rutil/dns/DnsStub.hxx>
#include "account/SipAccountSettings.h"

#include <tsc_socket_api.h>
#include <tsc_version.h>

#include <map>
#ifdef ANDROID
#include <jni.h>
#endif

namespace resip
{
class Transport;
}

namespace CPCAPI2
{

struct TseTunnelConfig
{
   tsc_handle handle;
   unsigned int redundancy_factor;
   bool do_load_balancing;
   SipAccount::TunnelMediaTransportType media_transport;
};
   
class TseTunnelManager : public resip::DnsResultSink
{
public:
   TseTunnelManager();
   virtual ~TseTunnelManager();

   static TseTunnelManager* instance();

   struct TseTunnelInfo
   {
      tsc_handle tunnel_handle;
      tsc_notification_handle notification_handle;
      char tunnel_internal_ip[TSC_ADDR_STR_LEN];
      char sip_server[TSC_ADDR_STR_LEN];
   };
   int createTunnelHandle(const char* server,
      uint16_t port,
      resip::TransportType transportType,
      bool serverKeepalive,
      bool ignoreCertErrors,
      TseTunnelInfo& tunnel_info,
      resip::DnsStub& resipDns,
      bool disableNagleAlgorithm,
      cpc::string& errorText);
   tsc_handle getTunnelHandleForTransport(const resip::Transport* transport);

   void wakeUpTunnels(tsc_handle handle);
   void destroyTunnel(tsc_handle handle);
   void setHibernationState(tsc_handle handle, bool hibernation);
   
private:
   void initialize();
   static void tscLogger(tsc_log_level level, const char* format, va_list args);
   static int tscTlsVerifyCallbackIgnoreErrors(int ok, void* cert_X509_store_ctx);
   static int tscTlsVerifyCallback(int ok, void* cert_X509_store_ctx);

   // DnsResultSink
   virtual void onDnsResult(const resip::DNSResult<resip::DnsHostRecord>&);
   virtual void onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>&) {}
   virtual void onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>&) {}
   virtual void onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>&) {}
   virtual void onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>&) {}

private:
   static TseTunnelManager* s_instance;

   std::map<const resip::Transport*, TseTunnelInfo> mTunnelMap;

   bool mDnsLookupDone;
   std::vector<resip::DnsHostRecord> mDnsHostRecords;
};
}
#endif