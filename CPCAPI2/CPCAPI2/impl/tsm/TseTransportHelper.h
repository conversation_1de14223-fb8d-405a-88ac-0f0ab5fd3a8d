#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
#include "rutil/Socket.hxx"

#include <tsc_control_api.h>
#include <tsc_socket_api.h>
#include <tsc_version.h>

//#define accept tsc_accept // Using tsc_accept results in the Bria UI not displaying
//#define select tsc_select
//#define send tsc_send
//#define recv tsc_recv
//#define recvfrom tsc_recvfrom
//#if defined(WIN32)
//#define closesocket tsc_close
//#else
//#define close tsc_close
//#endif

namespace resip
{
   class Tuple;

   Socket tsc_internal_socket(unsigned& transportFlags, TransportType type, IpVersion ipVer);
   int tsc_internal_connect (int s, const struct sockaddr *name, int namelen);
   int tsc_internal_sendto (int s, const char *buf, int len, int flags, const struct sockaddr *to, int tolen);
   bool TseMakeSocketNonBlocking(int fd);
   void tsc_internal_bind(Socket &mFd, resip::Tuple &mTuple);
   void tsc_tcp_base_init(Socket &mFd, unsigned& mTransportFlags, resip::Tuple &mTuple);
}
#endif