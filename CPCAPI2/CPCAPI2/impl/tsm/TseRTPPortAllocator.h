#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include <resip/recon/RTPPortAllocator.hxx>
#include <rutil/Data.hxx>

#include <tsc_socket_api.h>

namespace CPCAPI2
{

class TseRTPPortAllocator : public recon::RTPPortAllocator
{
public:
   TseRTPPortAllocator(recon::UserAgent* ua, void* tscconfig);

private:
   bool udpPortAvailable(int port, const resip::Data& targetInterfaceIp);

   tsc_handle mHandle;
};
}
#endif