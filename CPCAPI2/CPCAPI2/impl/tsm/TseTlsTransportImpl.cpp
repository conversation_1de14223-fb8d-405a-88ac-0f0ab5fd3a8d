#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)
#ifdef USE_SSL

#include "TseTransportHelper.h"
#include "TseTlsTransportImpl.h"

#define TlsTransport TseTlsTransport
#define TcpTransport TseTcpTransport
#define TcpBaseTransport TseTcpBaseTransport
#define InternalTransport TseInternalTransport

//#include <resip/stack/TcpTransport.hxx>

//#define init() tsc_tcp_base_init(mFd, transportFlags, mTuple)

namespace resip
{
   TseTlsTransportImpl::TseTlsTransportImpl(Fifo<TransactionMessage>& fifo,
                   int portNum,
                   IpVersion version,
                   const Data& interfaceObj,
                   Security& security,
                   const Data& sipDomain, 
                   SecurityTypes::SSLType sslType,
                   AfterSocketCreationFuncPtr afterSockCreationFunc = 0,
                   BeforeSocketCloseFuncPtr beforeSockCloseFunc = 0,
                   Compression &compression = Compression::Disabled,
		             unsigned transportFlags = 0)
                   : TseTlsTransport(fifo, portNum, version, interfaceObj, security, sipDomain, sslType, afterSockCreationFunc, beforeSockCloseFunc, compression, transportFlags)
   {
   }

   TseTlsTransportImpl::~TseTlsTransportImpl()
   {
   }
}

#endif /* USE_SSL */
#endif