#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "reflow/FakeSelectSocketDescriptor.hxx"
#include "resip/recon/media/MediaStack.hxx"
#include "reTurn/client/TurnAsyncUdpSocket_no_asio.hxx"
#include "account/SipAccountSettings.h"

#include <thread>
#include <rutil/Mutex.hxx>

#include <tsc_control_api.h>
#include <tsc_socket_api.h>
#include <tsc_version.h>

namespace CPCAPI2
{
class TurnAsyncTseUdpSocket : public reTurn::TurnAsyncUdpSocket
{
public:
   TurnAsyncTseUdpSocket(tsc_handle tschandle,
                         resip::HighPerfReactor& reactor,
                         resip::Resolver <resip::HighPerfReactor>& resolver,
                         reTurn::TurnAsyncSocketHandler* turnAsyncSocketHandler,
                         const resip::Data& address, 
                         unsigned short port,
                         unsigned int redundancyFactor,
                         bool doLoadBalancing,
                         SipAccount::TunnelMediaTransportType mediaTransportType,
                         recon::MediaStack::MediaType mediaType);
   virtual ~TurnAsyncTseUdpSocket();

   // AsyncSocketBase legacy
   virtual reTurn::asio_error_code bind(const resip::Data& address, unsigned short port) OVERRIDE;
   virtual void transportSend(const reTurn::StunTuple& destination, reTurn::DataBuffer* buffer);
   virtual void transportClose() OVERRIDE;

   // ReactorEventHandler
   virtual void process(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual void buildFdSet(resip::ReactorEventHandler::FdSetType& fdset) OVERRIDE;
   virtual const char* getEventHandlerDesc() const OVERRIDE { return "TurnAsyncTseUdpSocket"; }

private:
   void transport_thread();
   void doReadOperations();

private:
   tsc_handle mTscHandle;
   unsigned int mRedundancyFactor;
   bool mDoLoadBalancing;
   SipAccount::TunnelMediaTransportType mMediaTransportType;
   recon::MediaStack::MediaType mMediaType;

   std::shared_ptr<std::thread> mThread;
   resip::Mutex mShutdownMutex;
   bool mShutdown;
   flowmanager::FakeSelectSocketDescriptor mFakeSocket;
};
}
#endif
