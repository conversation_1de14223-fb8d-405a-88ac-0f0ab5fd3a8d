#pragma once

#include <memory>
#include "resip/stack/MsgHeaderScanner.hxx"
#include "reflow/FakeSelectSocketDescriptor.hxx"
#include "TseInternalTransport.h"

#include <thread>
#include <rutil/Mutex.hxx>

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

namespace osc { class Stack; }

namespace resip
{
   class TseUdpTransport : public TseInternalTransport, public FdPollItemIf
   {
   public:
      TseUdpTransport(Fifo<TransactionMessage>& fifo,
                      int portNum,
                      IpVersion version,
                      const Data& interfaceObj,
                      void* tunnelHandle);
      ~TseUdpTransport();

      virtual bool isReliable() const { return false; }
      virtual bool isDatagram() const { return true; }

      virtual void process(FdSet& fdset);
      virtual void process();
      virtual bool hasDataToSend() const;
      virtual void buildFdSet( FdSet& fdset);
      virtual void setPollGrp(FdPollGrp *grp);

      // FdPollItemIf
      // virtual Socket getPollSocket() const;
      virtual void processPollEvent(FdPollEventMask mask);

      static const int MaxBufferSize = 8192;

   private:
      void processRxAll();
      int processRxRecv(char*& buffer, Tuple& sender);
      bool processRxParse(char *buffer, int len, Tuple& sender);
      void processTxAll();
      void processTxOne(SendData *data);
      void updateEvents();
      void transport_thread();

#if TARGET_OS_IPHONE
      void replaceSock();
#endif

      osc::Stack *mSigcompStack;

      // statistics
      unsigned mPollEventCnt;
      unsigned mTxTryCnt;
      unsigned mTxMsgCnt;
      unsigned mTxFailCnt;
      unsigned mRxTryCnt;
      unsigned mRxMsgCnt;
      unsigned mRxKeepaliveCnt;
      unsigned mRxTransactionCnt;

      char* mRxBuffer;
      MsgHeaderScanner mMsgHeaderScanner;
      bool mInWritable;
      bool mInActiveWrite;

      std::shared_ptr<std::thread> mThread;
      resip::Mutex mShutdownMutex;
      bool mShutdown;
      flowmanager::FakeSelectSocketDescriptor mFakeSocket;
   };
}