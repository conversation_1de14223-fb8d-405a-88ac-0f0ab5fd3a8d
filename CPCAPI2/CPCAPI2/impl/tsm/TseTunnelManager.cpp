#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseTunnelManager.h"

#include <tsc_control_api.h>

#ifdef ANDROID
#include "common/Java/JniHelper.h"
#endif

#include <rutil/DnsUtil.hxx>
#include <rutil/dns/QueryTypes.hxx>
#include <rutil/Logger.hxx>

#ifdef USE_SSL
#if ANDROID
#include <resip/stack/ssl/counterpath/AndroidSecurity.hxx>
#elif __APPLE__
#if TARGET_OS_IPHONE
#include "resip/stack/ssl/IOSSecurity.hxx"
#endif
#endif
#endif

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::TRANSPORT

namespace CPCAPI2
{
TseTunnelManager* TseTunnelManager::s_instance = NULL;
tsc_notification_handle socket_info_notification_handle = 0;

TseTunnelManager::TseTunnelManager() :
 mDnsLookupDone(false)
{
}

TseTunnelManager::~TseTunnelManager()
{
}

TseTunnelManager* TseTunnelManager::instance()
{
   if (s_instance == NULL)
   {
      s_instance = new TseTunnelManager();
      s_instance->initialize();
   }
   return s_instance;
}

void TseTunnelManager::tscLogger(tsc_log_level level, const char *format, va_list args)
{
   static char buffer[1028];

   if (level == tsc_log_level_disabled)
      return;

   vsnprintf(buffer, sizeof(buffer), format, args);

   switch (level) {
   case tsc_log_level_critical:
      CritLog(<< buffer);
      break;
   case tsc_log_level_error:
      ErrLog(<< buffer);
      break;
   case tsc_log_level_warning:
   case tsc_log_level_notice:
      WarningLog(<< buffer);
      break;
   case tsc_log_level_info:
      InfoLog(<< buffer);
      break;
   case tsc_log_level_debug:
   case tsc_log_level_trace:
   default:
      //StackLog(<< buffer);
      break;
   }
}

void TseTunnelManager::initialize()
{
   tsc_ctrl_init();
#ifdef ANDROID
   tsc_set_android_jvm(CPCAPI2::Jni::GetJVM());
#endif

   // setup logging
   tsc_set_log_output(NULL);
   tsc_set_log_handler(tscLogger);
   tsc_set_use_callback_only(tsc_bool_true);
   tsc_set_log_level(tsc_log_level_debug);

   tsc_app_info app_info = {
               "tsc_sip_client", /* App name*/
               "99", /*App version*/
               "CentOS", /*OS Name*/
               "1.6", /*OS Version*/
               "I am a test application" /* Extra info */
   };
}

void
tunnel_socket_info_notification_cb(tsc_notification_data* data)
{
   if (!data) {
      WarningLog(<< __FUNCTION__ << ": no notification data");
      return;
   }

   tsc_tunnel_socket_info* socket_info = (tsc_tunnel_socket_info*)data->data;
   if (!socket_info) {
      WarningLog(<< __FUNCTION__ << ": no socket info data");
      return;
   }
   char local_addr[TSC_ADDR_STR_LEN];
   char remote_addr[TSC_ADDR_STR_LEN];
   char nat_ipport[TSC_ADDR_STR_LEN];
   if (!tsc_ip_port_address_to_str(&(socket_info->local_address), local_addr, TSC_ADDR_STR_LEN)) {
      WarningLog(<< "Failed to convert tunnel local address");
   }
   if (!tsc_ip_port_address_to_str(&(socket_info->remote_address), remote_addr, TSC_ADDR_STR_LEN)) {
      WarningLog(<< "Failed to convert tunnel remote address");
   }
   if (!tsc_ip_port_address_to_str (&(socket_info->nat_ipport), nat_ipport, TSC_ADDR_STR_LEN)) {
      WarningLog(<< "Failed to convert tunnel NAT address");
   }
   InfoLog(<< "Got tunnel socket info notification: local address " << local_addr << ", remote address " << remote_addr << ", NAT address " << nat_ipport);
}

void
tunnel_termination_info_notification_cb(tsc_notification_data* data)
{
   if (!data) {
      WarningLog(<< __FUNCTION__ << ": no notification data");

      return;
   }

   tsc_config *config = &(((tsc_notification_termination_info_data *)data->data)->config);

   InfoLog(<< "Got tunnel termination info notification: tunnel " << config->tunnel_id.hi << config->tunnel_id.lo << " terminated");
}

void tunnel_redundancy_notification_cb(tsc_notification_data *notification)
{
    tsc_notification_redundancy_info_data *red_data = (tsc_notification_redundancy_info_data *)notification->data;

    if (red_data && red_data->available == tsc_bool_true) {
        if (red_data->enabled == tsc_bool_true) {
            InfoLog(<< "redundancy enabled on socket " << red_data->socket);
        } else {
            InfoLog(<< "redundancy disabled on socket " << red_data->socket);
        }
    } else {
        InfoLog(<< "redundancy not allowed on socket " << red_data->socket);
    }
}

int TseTunnelManager::createTunnelHandle(const char* server,
                                         uint16_t port,
                                         resip::TransportType transportType,
                                         bool serverKeepalive,
                                         bool ignoreCertErrors,
                                         TseTunnelInfo& tunnel_info,
                                         resip::DnsStub &resipDns,
                                         bool disableNagleAlgorithm,
                                         cpc::string& errorText)
{
   /*
   * Tunnel connection parameters
   */
   tsc_tunnel_params tunnel_params;
   memset (&tunnel_params, 0, sizeof (tsc_tunnel_params));

   tunnel_params.connection_params[0].server_address.port = port;

   // DNS lookup
   mDnsLookupDone = false;
   mDnsHostRecords.clear();
   resip::Data serverAddr(server);
   if (!resip::DnsUtil::isIpAddress(serverAddr))
   {
      resipDns.lookup<resip::RR_A>(serverAddr, this);
      // Wait a maximum of 5 seconds for the DNS 'A' response
      int loopc = 0;
      while (!mDnsLookupDone && loopc++ < 50)
      {
         resip::FdSet fdset;
         resipDns.buildFdSet(fdset);
         fdset.selectMilliSeconds(100);
         resipDns.process(fdset);
      }

      if (mDnsHostRecords.size() > 0)
      {
         resip::DnsHostRecord record = mDnsHostRecords.front();
         server = record.host().c_str();
      }
      else
      {
         WarningLog(<< "DNS lookup failed for tunnel server.");
         errorText = "DNS lookup failed for tunnel server";
         return -1;
      }
   }

   uint32_t address;
   tsc_inet_pton (AF_INET, server, &address);
   tunnel_params.connection_params[0].server_address.address =
      ntohl (address);

   /*
   * Set transport type to TLS or DTLS or UDP. Default is TCP
   */
   if (transportType == resip::UDP) {
      tunnel_params.connection_params[0].transport = tsc_transport_udp;
   }
   else if (transportType == resip::TCP) {
      tunnel_params.connection_params[0].transport = tsc_transport_tcp;
   }
#ifdef USE_SSL
   else if (transportType == resip::TLS) {
      tunnel_params.connection_params[0].transport = tsc_transport_tls;
   }
   else if (transportType == resip::DTLS) {
      tunnel_params.connection_params[0].transport = tsc_transport_dtls;
   }
#endif
   else {
      return -1;
   }

   if (disableNagleAlgorithm) {
      tunnel_params.connection_params[0].nagle_option = tsc_nagle_disabled;
   }

   tunnel_params.max_connections = 1;
   /*change this value to enable or disable keepalive_interval
   after enable, the range is from 10 to 30 */
   tunnel_params.tsc_fast_keepalive_interval = 0;

   /*
   * Enable wireshark tracing
   */
#ifndef TSC_ANDROID
   if (0) {
      tunnel_params.pcap_capture.enabled = tsc_bool_true;
      strcpy (tunnel_params.pcap_capture.filename, "tsc_sip_client.pcap");
   }
#endif

   /*
   * Set TLS parameters
   */
#ifdef TSC_TLS_CONFIG_FROM_FILE
   tunnel_params.sec_config[0].read_from_file =
      tsc_bool_true;
   tunnel_params.connection_params[0].sec_config_index = 0;
   /*
   * Setting this option will configure SDK to expect the following files
   * to be found in the current run directory:
   * 1. Certificate file "tsccert.pem"
   * 2. Private key file "tsckey.pem"
   * 3. CA file "tscca.pem"
   */
#else
   tunnel_params.sec_config[0].read_from_file =
         tsc_bool_false;
   tunnel_params.connection_params[0].sec_config_index = 0;

   if (ignoreCertErrors)
      tunnel_params.sec_config[0].tls_verify_callback = tscTlsVerifyCallbackIgnoreErrors;
#if ANDROID || TARGET_OS_IPHONE
   else
      tunnel_params.sec_config[0].tls_verify_callback = tscTlsVerifyCallback;
#endif

   // TODO: Add support for certificate validation
#if 0
#ifdef USE_SSL
   /*
   * If the transport is not UDP or TCP set the certificate parameters
   */
   if (transportType == resip::TLS
    || transportType == resip::DTLS
      ) {
      tunnel_params.sec_config[0].ca_count = 1;
      /*
      * more than one if more than one CA possible for server
      * certificate validation
      */
      tunnel_params.sec_config[0].config_ca.ca_len = sizeof(test_ca);
      memcpy (tunnel_params.sec_config[0].config_ca.ca, test_ca, sizeof(test_ca));

      /*
      * more than one for client certificate chain
      */

      tunnel_params.sec_config[0].cert_count = 1;
      tunnel_params.sec_config[0].config_cert.cert_len = sizeof(test_cert);
      memcpy (tunnel_params.sec_config[0].config_cert.cert, test_cert, sizeof(test_cert));

      tunnel_params.sec_config[0].private_key_len = sizeof(test_private_key);
      memcpy(tunnel_params.sec_config[0].private_key, test_private_key, sizeof(test_private_key));
      }
#endif //USE_SSL
#endif
#endif //TSC_TLS_CONFIG_FROM_FILE

   if (serverKeepalive) {
      tunnel_params.keepalive_refresher = tsc_keepalive_refresher_server;
   } else {
      tunnel_params.keepalive_refresher = tsc_keepalive_refresher_client;
   }

   /*
   *    Create a tunnel this includes
   *    TCP connect, (if transport == tsc_transport_tcp)
   *    UDP connect, (if transport == tsc_transport_udp)
   *    TLS handshake  (if transport == tsc_transport_tls)
   *    DTLS handshake  (if transport == tsc_transport_dtls)
   *    Tunnel configuration setup, including receiving inner IP configuration
   *    information.
   */

   InfoLog(<< "Attemping to establish a tunnel to TSCF server listening on " << server << ":" << port);

   tunnel_info.tunnel_handle = tsc_ctrl_new_tunnel (&tunnel_params, NULL);

   if (!tunnel_info.tunnel_handle) {
      ErrLog(<< "Failed to create tunnel handle");
      errorText = "TSCF Tunnel Creation Failed";
      return -1;
   }
   else {
      InfoLog(<< "Obtained a tunnel handle = " << tunnel_info.tunnel_handle);
   }

   tsc_notification_params params;
   params.opaque = this;
   if (!(tunnel_info.notification_handle = tsc_notification_enable(tunnel_info.tunnel_handle, tsc_notification_tunnel_socket_info, tunnel_socket_info_notification_cb, &params))) {
      WarningLog(<< "Failed to register tunnel socket info notification callback");
      return -1;
   } else {
      InfoLog(<< "Tunnel socket info notification callback registered: handle " << tunnel_info.notification_handle);
   }

   if (!tsc_notification_enable(tunnel_info.tunnel_handle, tsc_notification_tunnel_termination_info, tunnel_termination_info_notification_cb, NULL)) {
         WarningLog(<< "Failed to register tunnel termination info notification callback");
         return -1;
   } else {
      InfoLog(<< "Tunnel termination info notification callback registered: handle " << tunnel_info.notification_handle);
   }

   if (!(tsc_notification_enable(tunnel_info.tunnel_handle, tsc_notification_redundancy, tunnel_redundancy_notification_cb, NULL))) {
      WarningLog(<< "Failed to register tunnel redundancy notification callback");
   } else {
      InfoLog(<< "Tunnel redundancy notification callback registered: handle " << tunnel_info.notification_handle);
   }

   tsc_config config;
   tsc_get_config (tunnel_info.tunnel_handle, &config);   /* This is the configuration received
                                                          * from server
                                                          */

   /*
   * IP address assigned by server
   */
   char local[TSC_ADDR_STR_LEN];
   tsc_ip_address_to_str (&(config.internal_address), local,
      TSC_ADDR_STR_LEN);
   InfoLog(<< "Tunnel internal ip address (assigned by TSCF server) is " << local);
   memcpy(tunnel_info.tunnel_internal_ip, local, TSC_ADDR_STR_LEN);

   tsc_tunnel_socket_info tunnel_sock_info;
   char nat_ipport[TSC_ADDR_STR_LEN];
   if (tsc_get_tunnel_socket_info (tunnel_info.tunnel_handle, &tunnel_sock_info) == tsc_error_code_error) {
      WarningLog(<< "Failed to get tunnel socket info");
   } else if (!tsc_ip_port_address_to_str (&tunnel_sock_info.nat_ipport, nat_ipport, TSC_ADDR_STR_LEN)) {
      WarningLog(<< "Failed to convert tunnel NAT address");
   } else {
      InfoLog(<< "Tunnel NAT address (server reflexive) is " << nat_ipport);
   }

   /*
   * IP address of SIP interface on server (P-CSCF)
   */
   if (true) {
      char remote[TSC_ADDR_STR_LEN];
      tsc_ip_port_address_to_str (&(config.sip_server), remote,
         TSC_ADDR_STR_LEN);
      InfoLog(<< "SIP server (assigned by TSCF server) ip:port is " << remote);
      memcpy(tunnel_info.sip_server, remote, TSC_ADDR_STR_LEN);
   }
   else {
      //tsc_inet_pton (AF_INET, sipserver, &sipaddress);
      // std::cout << "    SIP server (assigned via CLI) i:port is %s:%d\n", sipserver, sipport) << std::endl;
   }

   return 0;
}

tsc_handle TseTunnelManager::getTunnelHandleForTransport(const resip::Transport* transport)
{
   std::map<const resip::Transport*, TseTunnelInfo>::iterator it = mTunnelMap.find(transport);
   if (it != mTunnelMap.end())
   {
      return it->second.tunnel_handle;
   }
   WarningLog(<< "Tunnel handle not found for Transport " << transport);
   return 0;
}

void TseTunnelManager::wakeUpTunnels(tsc_handle handle)
{
   if (tsc_wakeup(handle, 5000) == tsc_wakeup_keepalive_timeout)
   {
      // Force tsc_reconnect to occur
#ifdef ANDROID
      tsc_app_event(handle, tsc_app_andr_has_no_network);
      tsc_app_event(handle, tsc_app_andr_has_network);
#endif
   }
}

void TseTunnelManager::destroyTunnel(tsc_handle handle)
{
   tsc_delete_tunnel(handle);
}

void TseTunnelManager::setHibernationState(tsc_handle handle, bool hibernation)
{
   if (hibernation)
   {
      tsc_app_event(handle, tsc_app_ios_did_enter_background);
   }
   else
   {
      tsc_app_event(handle, tsc_app_ios_will_enter_foreground);
   }
}

int TseTunnelManager::tscTlsVerifyCallbackIgnoreErrors(int ok, void* cert_X509_store_ctx)
{
   // bypass TLS certificate verification
   return 1;
}

int TseTunnelManager::tscTlsVerifyCallback(int ok, void* cert_X509_store_ctx)
{
#if ANDROID
   return resip::AndroidSecurity::verifyCallback(ok, (X509_STORE_CTX *)cert_X509_store_ctx);
#elif TARGET_OS_IPHONE
   return resip::IOSSecurity::verifyCallback(ok, (X509_STORE_CTX *)cert_X509_store_ctx);
#endif
   return -1;
}

void TseTunnelManager::onDnsResult(const resip::DNSResult<resip::DnsHostRecord>& result)
{
   if (result.status == 0)
   {
      mDnsHostRecords = result.records;
   }

   mDnsLookupDone = true;
}

}
#endif
