#pragma once

#ifdef USE_SSL

#include <resip/stack/ssl/TlsTransport.hxx>

namespace resip
{
   class TseTlsTransport : public TlsTransport
   {
   public:
      TseTlsTransport(Fifo<TransactionMessage>& fifo,
                   int portNum,
                   IpVersion version,
                   const Data& interfaceObj,
                   Security& security,
                   const Data& sipDomain, 
                   SecurityTypes::SSLType sslType,
                   AfterSocketCreationFuncPtr afterSockCreationFunc = 0,
                   BeforeSocketCloseFuncPtr beforeSockCloseFunc = 0,
                   Compression &compression = Compression::Disabled,
		             unsigned transportFlags = 0)
                   : TlsTransport(fifo, portNum, version, interfaceObj, security, sipDomain, sslType, afterSockCreationFunc, beforeSockCloseFunc, compression, transportFlags)
      {
      }
   };
}

#endif /* USE_SSL */