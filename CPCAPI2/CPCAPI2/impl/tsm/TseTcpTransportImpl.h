#pragma once

//#define TcpTransport TseTcpTransport
//#define InternalTransport TseInternalTransport

#include "TseTcpTransport.h"

namespace resip
{
   class TseTcpTransportImpl : public TseTcpTransport
   {
   public:
      TseTcpTransportImpl(Fifo<TransactionMessage>& fifo,
                   int portNum,
                   IpVersion version,
                   const Data& interfaceObj,
                   AfterSocketCreationFuncPtr afterSockCreationFunc = 0,
                   BeforeSocketCloseFuncPtr beforeSockCloseFunc = 0,
                   Compression &compression = Compression::Disabled,
		             unsigned transportFlags = 0);
      virtual ~TseTcpTransportImpl();

      virtual bool hasSpecificContact() const { return true; }
   };
}