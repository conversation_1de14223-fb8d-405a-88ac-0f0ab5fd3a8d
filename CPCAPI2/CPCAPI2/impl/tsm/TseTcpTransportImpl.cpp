#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseTransportHelper.h"
#include "TseTcpTransportImpl.h"

//#define TcpTransport TseTcpTransport
//#define TcpBaseTransport TseTcpBaseTransport

//#include <resip/stack/TcpBaseTransport.hxx>

//#define init() tsc_tcp_base_init(mFd, transportFlags, mTuple)

namespace resip
{
   TseTcpTransportImpl::TseTcpTransportImpl(Fifo<TransactionMessage>& fifo,
                      int portNum,
                      IpVersion version,
                      const Data& interfaceObj,
                      AfterSocketCreationFuncPtr afterSockCreationFunc,
                      BeforeSocketCloseFuncPtr beforeSockCloseFunc,
                      Compression &compression,
		                unsigned transportFlags)
                      : TseTcpTransport(fifo, portNum, version, interfaceObj, afterSockCreationFunc, beforeSockCloseFunc, compression, transportFlags)
   {
   }

   TseTcpTransportImpl::~TseTcpTransportImpl()
   {
   }
}
#endif