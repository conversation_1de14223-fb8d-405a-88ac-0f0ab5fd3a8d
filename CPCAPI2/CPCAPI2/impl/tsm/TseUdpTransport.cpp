#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include <memory>

#include "resip/stack/Helper.hxx"
#include "resip/stack/SendData.hxx"
#include "resip/stack/SipMessage.hxx"
#include "rutil/Data.hxx"
#include "rutil/DnsUtil.hxx"
#include "rutil/Logger.hxx"
#include "rutil/Socket.hxx"
#include "rutil/WinLeakCheck.hxx"
#include "rutil/compat.hxx"
#include "TseUdpTransport.h"

#include <tsc_control_api.h>
#include <tsc_socket_api.h>
#include <tsc_version.h>

#ifdef USE_SIGCOMP
#include <osc/Stack.h>
#include <osc/StateChanges.h>
#include <osc/SigcompMessage.h>
#endif

#define RESIPROCATE_SUBSYSTEM Subsystem::TRANSPORT

using namespace resip;

TseUdpTransport::TseUdpTransport(Fifo<TransactionMessage>& fifo,
                           int portNum,
                           IpVersion version,
                           const Data& pinterface,
                           void* tunnelHandle)
: TseInternalTransport(fifo, portNum, version, pinterface, tunnelHandle),
mSigcompStack(0),
mRxBuffer(0),
mInWritable(false),
mThread(NULL),
mShutdown(false)
{
   mPollEventCnt = 0;
   mTxTryCnt = mTxMsgCnt = mTxFailCnt = 0;
   mRxTryCnt = mRxMsgCnt = mRxKeepaliveCnt = mRxTransactionCnt = 0;
   mTuple.setType(UDP);
   mFd = internal_socket(transport(), version);
   mTuple.mFlowKey=(FlowKey)mFd;
   bind();      // also makes it non-blocking

   InfoLog (<< "Creating UDP transport host=" << pinterface
            << " port=" << mTuple.getPort()
            << " ipv4=" << bool(version==V4) );

#ifdef USE_SIGCOMP
   if (mCompression.isEnabled())
   {
      DebugLog (<< "Compression enabled for transport: " << *this);
      mSigcompStack = new osc::Stack(mCompression.getStateHandler());
      mCompression.addCompressorsToStack(mSigcompStack);
   }
   else
   {
      DebugLog (<< "Compression disabled for transport: " << *this);
   }
#else
   DebugLog (<< "No compression library available: " << *this);
#endif
   mTxFifo.setDescription("TseUdpTransport::mTxFifo");

   mThread = std::make_shared<std::thread>(std::bind(&TseUdpTransport::transport_thread, this));
}

TseUdpTransport::~TseUdpTransport()
{
   InfoLog(<< "Shutting down " << mTuple
           <<" tf="<<mTransportFlags<<" evt="<<(mPollGrp?1:0)
           <<" stats:"
           <<" poll="<<mPollEventCnt
           <<" txtry="<<mTxTryCnt
           <<" txmsg="<<mTxMsgCnt
           <<" txfail="<<mTxFailCnt
           <<" rxtry="<<mRxTryCnt
           <<" rxmsg="<<mRxMsgCnt
           <<" rxka="<<mRxKeepaliveCnt
           <<" rxtr="<<mRxTransactionCnt
           );

   if (mFd != -1)
   {
      callBeforeCloseSocketFunc(mFd);
   }

   {
      resip::Lock lock(mShutdownMutex);
      mShutdown = true;
   }
   mThread->join();

#ifdef USE_SIGCOMP
   delete mSigcompStack;
#endif
   if ( mRxBuffer )
   {
      delete[] mRxBuffer;
   }
   setPollGrp(0);
}

void
TseUdpTransport::setPollGrp(FdPollGrp *grp)
{
   if(mPollGrp)
   {
      mPollGrp->delPollItem(mPollItemHandle);
      mPollItemHandle=0;
   }

   if(grp)
   {
      mPollItemHandle = grp->addPollItem(mFakeSocket.getSocketDescriptor(), FPEM_Read, this);
      // above released by InternalTransport destructor
      // ?bwc? Is this really a good idea? If the InternalTransport d'tor is
      // freeing this, shouldn't InternalTransport::setPollGrp() handle
      // creating it?
   }

   InternalTransport::setPollGrp(grp);
}


/**
 * Called after a message is added. Could try writing it now.
 */
void
TseUdpTransport::process() {
   mStateMachineFifo.flush();
   if ( (mTransportFlags & RESIP_TRANSPORT_FLAG_TXNOW)!= 0 )
   {
      processTxAll();
      // FALLTHRU to code below in case queue not-empty
      // shouldn't ever happen (with current code)
      // but in future we may throttle transmits
   }
   if ( mPollGrp )
      updateEvents();
}

void
TseUdpTransport::updateEvents()
{
   //assert( mPollGrp );
   bool haveMsg = mTxFifoOutBuffer.messageAvailable();
   if ( !mInWritable && haveMsg )
   {
      mPollGrp->modPollItem(mPollItemHandle, FPEM_Read|FPEM_Write);
      mInWritable = true;
   }
   else if ( mInWritable && !haveMsg )
   {
      mPollGrp->modPollItem(mPollItemHandle, FPEM_Read);
      mInWritable = false;
   }
}

void TseUdpTransport::transport_thread()
{
   const int sleepDurationMs = 100;
   while (1)
   {
      fd_set readfds;
      FD_ZERO(&readfds);
      FD_SET(mFakeSocket.getSocketDescriptor(), &readfds);
      timeval tv = { 0, 0 };
      if (!select(mFakeSocket.getSocketDescriptor() + 1, &readfds, NULL, NULL, &tv))
      {
         int size = mFd + 1;
         tsc_fd_set read;
         TSC_FD_ZERO(&read);
         TSC_FD_SET(mFd, &read);
         tsc_fd_set write;
         TSC_FD_ZERO(&write);
         tsc_fd_set except;
         TSC_FD_ZERO(&except);
         struct tsc_timeval tv;
         tv.tv_sec = 0;
         tv.tv_usec = sleepDurationMs * 1000;

         int numReady = tsc_select(size, &read, &write, &except, &tv);

         if (numReady > 0)
         {
            mFakeSocket.send();
         }
         else
         {
            // Possibly failed to get tunnel lock due to other media transports doing the same thing, wait a bit and try again
            tsc_sleep(sleepDurationMs);
         }
      }
      else
      {
         tsc_sleep(sleepDurationMs);
      }

      {
         resip::Lock lock(mShutdownMutex);
         if (mShutdown)
         {
            if (mFd != -1)
            {
               tsc_close(mFd);
               mFd = -1;
            }
            break;
         }
      }
   }
}


void
TseUdpTransport::processPollEvent(FdPollEventMask mask)
{
   ++mPollEventCnt;
   if ( mask & FPEM_Error )
   {
      assert(0);
   }
   if ( mask & FPEM_Write )
   {
      processTxAll();
      updateEvents();   // turn-off writability
   }
   if ( mask & FPEM_Read )
   {
      processRxAll();
      mFakeSocket.receive();
   }
}

/**
 If we return true, the TransactionController will set the timeout
 to zero so that process() is called immediately. We don't want this;
 instead, we depend upon the writable-socket callback (fdset or poll).
 **/
bool
TseUdpTransport::hasDataToSend() const
{
   return false;
}

void
TseUdpTransport::buildFdSet( FdSet& fdset )
{
   fdset.setRead(mFakeSocket.getSocketDescriptor());

   if (mTxFifoOutBuffer.messageAvailable())
   {
      fdset.setWrite(mFakeSocket.getSocketDescriptor());
   }
}

void
TseUdpTransport::process(FdSet& fdset)
{
   // pull buffers to send out of TxFifo
   // receive datagrams from fd
   // preparse and stuff into RxFifo

   if (mTxFifoOutBuffer.messageAvailable())
   {
      processTxAll();
   }

   fd_set readfds;
   FD_ZERO(&readfds);
   FD_SET(mFakeSocket.getSocketDescriptor(), &readfds);
   timeval tv = { 0, 0 };

   if (select(mFakeSocket.getSocketDescriptor() + 1, &readfds, NULL, NULL, &tv))
   {
      processRxAll();
      mFakeSocket.receive();
   }

   mStateMachineFifo.flush();
}

/**
 * Added support for TXNOW and TXALL. Generally only makes sense
 * to specify one of these. Limited testing shows limited performance
 * gain from either of these: the socket-event overhead appears tiny.
 */
void
TseUdpTransport::processTxAll()
{
   SendData *msg;
   ++mTxTryCnt;
   while ( (msg=mTxFifoOutBuffer.getNext(RESIP_FIFO_NOWAIT)) != NULL )
   {
      processTxOne(msg);
      // With UDP we don't need to worry about write blocking (I hope)
      if ( (mTransportFlags & RESIP_TRANSPORT_FLAG_TXALL)==0 )
         break;
   }
}

void
TseUdpTransport::processTxOne(SendData *data)
{
   ++mTxMsgCnt;
   assert(data);
   std::unique_ptr<SendData> sendData(data);
   //DebugLog (<< "Sent: " <<  sendData->data);
   //DebugLog (<< "Sending message on udp.");
   assert( sendData->destination.getPort() != 0 );

   const sockaddr& addr = sendData->destination.getSockaddr();
   int expected;
   int count;
   int tryCnt = 0;

   while (tryCnt < 5)
   {
   #ifdef USE_SIGCOMP
      // If message needs to be compressed, compress it here.
      if (mSigcompStack &&
          sendData->sigcompId.size() > 0 &&
          !sendData->isAlreadyCompressed )
      {
         osc::SigcompMessage *sm = mSigcompStack->compressMessage
         (sendData->data.data(), sendData->data.size(),
          sendData->sigcompId.data(), sendData->sigcompId.size(),
          isReliable());

         DebugLog (<< "Compressed message from "
                   << sendData->data.size() << " bytes to "
                   << sm->getDatagramLength() << " bytes");

         expected = sm->getDatagramLength();

         char* buf_copy = (char*) calloc(expected, sizeof(char));
         strncpy(buf_copy, sm->getDatagramMessage(), expected);
         struct sockaddr to_copy = sockaddr(addr);
         count = tsc_sendto(mFd,
                        buf_copy,
                        sm->getDatagramLength(),
                        0, // flags
                        &to_copy, sendData->destination.length());
         free(buf_copy);
         delete sm;
      }
      else
   #endif
      {
         expected = (int)sendData->data.size();
         char* buf_copy = (char*) calloc(expected, sizeof(char));
         strncpy(buf_copy, sendData->data.data(), expected);
         struct sockaddr to_copy = sockaddr(addr);
         count = tsc_sendto(mFd,
                        buf_copy, (int)sendData->data.size(),
                        0, // flags
                        &to_copy, (int)sendData->destination.length());
         free(buf_copy);
      }

      if ( count == SOCKET_ERROR )
      {
         int e = tsc_get_errno();
#if TARGET_OS_IPHONE
         if (tryCnt < 5 && (e == EADDRNOTAVAIL || e == EPIPE || e == ENOTCONN))
         {
            replaceSock();
            tryCnt++;
            continue;
         }
#endif
         ErrLog (<< "Failed (" << get_errno_string(e) << ") sending to " << sendData->destination);
         fail(sendData->transactionId);
         ++mTxFailCnt;
      }
      else
      {
         if (count != expected)
         {
            ErrLog (<< "UDPTransport - send buffer full" );
            fail(sendData->transactionId);
         }
         else
         {
            if (count != 4 || strncmp(sendData->data.data(), Symbols::CRLFCRLF, 4) != 0)
            {
               SpecialLog(<< "SIP (out):" << endl << sendData->data);
            }
         }
      }
      break;
   }
}

/**
 * Add options RXALL (to try receive all readable data) and KEEP_BUFFER.
 * While each can be specified independently, generally should do both
 * or neither. This is because with RXALL, every read cycle will have
 * end with an EAGAIN read followed by buffer free (if no KEEP_BUFFER flag).
 * Testing in very limited cases shows marginal (5%) performance improvements.
 * Probably "real" traffic (that is bursty) would more impact.
 */
void
TseUdpTransport::processRxAll()
{
   char *buffer = mRxBuffer;
   mRxBuffer = NULL;
   ++mRxTryCnt;
   for (;;)
   {
      // TBD: check StateMac capacity
      Tuple sender(mTuple);
      int len = processRxRecv(buffer, sender);
      if ( len <= 0 )
      {
         break;
      }
      ++mRxMsgCnt;
      if ( processRxParse(buffer, len, sender) )
      {
         buffer = NULL;
      }
      if ( (mTransportFlags & RESIP_TRANSPORT_FLAG_RXALL) == 0 )
      {
         break;
      }
   }
   if ( buffer && (mTransportFlags & RESIP_TRANSPORT_FLAG_KEEP_BUFFER)!=0 )
   {
      assert(mRxBuffer==NULL);
      mRxBuffer = buffer;
      buffer = NULL;
   }
   if ( buffer )
   {
      delete[] buffer;
   }
}

/*
 * Receive from socket and store results into {buffer}. Updates
 * {buffer} with actual buffer (in case allocation required),
 * {len} with length of receive data, and {sender} with who
 * sent the packet.
 * Return length of data read:
 *  0 if no data read and no more data to read (EAGAIN)
 *  >0 if data read and may be more data to read
 **/
int
TseUdpTransport::processRxRecv(char*& buffer, Tuple& sender)
{
   // !jf! this may have to change - when we read a message that is too big
   //should this buffer be allocated on the stack and then copied out, as it
   //needs to be deleted every time EWOULDBLOCK is encountered
   // .dlb. can we determine the size of the buffer before we allocate?
   // something about MSG_PEEK|MSG_TRUNC in Stevens..
   // .dlb. RFC3261 18.1.1 MUST accept 65K datagrams. would have to attempt to
   // adjust the UDP buffer as well...
   if (buffer==NULL)
   {
      buffer = MsgHeaderScanner::allocateBuffer(MaxBufferSize);
   }

   int tryCnt = 0;

   while (tryCnt < 5) {
      // !jf! how do we tell if it discarded bytes
      // !ah! we use the len-1 trick :-(
#ifdef __THREADX
      int slen = sender.length();
#else
      socklen_t slen = sender.length();
#endif
      int len = tsc_recvfrom( mFd,
                         buffer,
                         MaxBufferSize,
                         0 /*flags */,
                         &sender.getMutableSockaddr(),
                         &slen);
      if ( len == SOCKET_ERROR )
      {
         int err = tsc_get_errno();
         if ( err != EWOULDBLOCK  )
         {
            ErrLog(<< get_errno_string( err ));

#if TARGET_OS_IPHONE
            if (tryCnt < 5 && (err == EADDRNOTAVAIL || err == EPIPE || err == ENOTCONN))
            {
               replaceSock();
               tryCnt++;
               continue;
            }
#endif
         }
         len = 0;
      }
      if (len+1 >= MaxBufferSize)
      {
         InfoLog(<<"Datagram exceeded max length "<<MaxBufferSize);
         continue;
      }
      if (len > 0)
      {
         Data monkey(Data::Borrow, buffer, len);
         //InfoLog(<< "received datagram: " << std::endl << std::endl << monkey);
         SpecialLog(<< "SIP (in):" << std::endl << monkey);
      }
      return len;
   }
   return 0;
}


/**
 * Parse the contents of {buffer} and do something with it.
 * Return true iff {buffer} was consumed (absorbed into SipMessage
 * to be free'd later). Note return code doesn't indicate
 * "success" in parsing the message; rather, it just indicates
 * who owns buffer.
 **/
bool
TseUdpTransport::processRxParse(char *buffer, int len, Tuple& sender)
{
   bool origBufferConsumed = true;

   //handle incoming CRLFCRLF keep-alive packets
   if (len == 4 &&
       strncmp(buffer, Symbols::CRLFCRLF, len) == 0)
   {
      StackLog(<<"Throwing away incoming firewall keep-alive");
      ++mRxKeepaliveCnt;
      return false;
   }

   // this must be a STUN response (or garbage)
   if (buffer[0] == 1 && buffer[1] == 1 && ipVersion() == V4)
   {
      return false;
   }

   // this must be a STUN request (or garbage)
   if (buffer[0] == 0 && buffer[1] == 1 && ipVersion() == V4)
   {
      return false;
   }

#ifdef USE_SIGCOMP
   osc::StateChanges *sc = 0;
#endif

   // Attempt to decode SigComp message, if appropriate.
   if ((buffer[0] & 0xf8) == 0xf8)
   {
      if (!mCompression.isEnabled())
      {
         InfoLog(<< "Discarding unexpected SigComp Message");
         return false;
      }
#ifdef USE_SIGCOMP
      char* newBuffer = MsgHeaderScanner::allocateBuffer(MaxBufferSize);

      size_t uncompressedLength = mSigcompStack->uncompressMessage(buffer, len, newBuffer, MaxBufferSize, sc);

      DebugLog (<< "Uncompressed message from "
               << len << " bytes to "
               << uncompressedLength << " bytes");

      osc::SigcompMessage *nack = mSigcompStack->getNack();

      if (nack)
      {
         mTxFifo.add(new SendData(tuple,
                                  Data(nack->getDatagramMessage(),
                                       nack->getDatagramLength()),
                                  Data::Empty,
                                  Data::Empty,
                                  true));
         delete nack;
      }

      // delete[] buffer; NO: let caller do this if needed
      origBufferConsumed = false;
      buffer = newBuffer;
      len = uncompressedLength;
#endif
   }

   buffer[len]=0; // null terminate the buffer string just to make debug easier and reduce errors

   //DebugLog ( << "UDP Rcv : " << len << " b" );
   //DebugLog ( << Data(buffer, len).escaped().c_str());

   SipMessage* message = new SipMessage(this);

   // set the received from information into the received= parameter in the
   // via

   // It is presumed that UDP Datagrams are arriving atomically and that
   // each one is a unique SIP message


   // Save all the info where this message came from
   sender.transport = this;
   sender.transportKey = getKey();
   sender.mFlowKey=mTuple.mFlowKey;
   message->setSource(sender);
   //DebugLog (<< "Received from: " << sender);

   // Tell the SipMessage about this datagram buffer.
   // WATCHOUT: below here buffer is consumed by message
   message->addBuffer(buffer);

   mMsgHeaderScanner.prepareForMessage(message);

   char *unprocessedCharPtr;
   if (mMsgHeaderScanner.scanChunk(buffer,
                                   len,
                                   &unprocessedCharPtr) !=
       MsgHeaderScanner::scrEnd)
   {
      StackLog(<< Data(Data::Borrow, buffer, len));

      // Idea: consider backing buffer out of message and letting caller reuse it
      delete message;
      message=0;
      return origBufferConsumed;
   }

   // no pp error
   int used = int(unprocessedCharPtr - buffer);

   if (used < len)
   {
      // body is present .. add it up.
      // NB. The Sip Message uses an overlay (again)
      // for the body. It ALSO expects that the body
      // will be contiguous (of course).
      // it doesn't need a new buffer in UDP b/c there
      // will only be one datagram per buffer. (1:1 strict)

      message->setBody(buffer+used,len-used);
      //DebugLog(<<"added " << len-used << " byte body");
   }

   // .bwc. basicCheck takes up substantial CPU. Don't bother doing it
   // if we're overloaded.
   CongestionManager::RejectionBehavior behavior=getRejectionBehaviorForIncoming();
   if (behavior==CongestionManager::REJECTING_NON_ESSENTIAL
       || (behavior==CongestionManager::REJECTING_NEW_WORK
           && message->isRequest()))
   {
      // .bwc. If this fifo is REJECTING_NEW_WORK, we will drop
      // requests but not responses ( ?bwc? is this right for ACK?).
      // If we are REJECTING_NON_ESSENTIAL,
      // we reject all incoming work, since losing something from the
      // wire will not cause instability or leaks (see
      // CongestionManager.hxx)

      // .bwc. This handles all appropriate checking for whether
      // this is a response or an ACK.
      std::shared_ptr<SendData> tryLater(make503(*message, getExpectedWaitForIncoming()/1000));
      if(tryLater.get())
      {
         send(tryLater);
      }
      delete message; // dropping message due to congestion
      message = 0;
      return origBufferConsumed;
   }

   if (!basicCheck(*message))
   {
      delete message; // cannot use it, so, punt on it...
      // basicCheck queued any response required
      message = 0;
      return origBufferConsumed;
   }

   stampReceived(message);

#ifdef USE_SIGCOMP
   if (mCompression.isEnabled() && sc)
   {
      const Via &via = message->header(h_Vias).front();
      if (message->isRequest())
      {
         // For requests, the compartment ID is read out of the
         // top via header field; if not present, we use the
         // TCP connection for identification purposes.
         if (via.exists(p_sigcompId))
         {
            Data compId = via.param(p_sigcompId);
            if(!compId.empty())
            {
               // .bwc. Crash was happening here. Why was there an empty sigcomp id?
               mSigcompStack->provideCompartmentId(sc, compId.data(), compId.size());
            }
         }
         else
         {
            mSigcompStack->provideCompartmentId(sc, this, sizeof(this));
         }
      }
      else
      {
         // For responses, the compartment ID is supposed to be
         // the same as the compartment ID of the request. We
         // *could* dig down into the transaction layer to try to
         // figure this out, but that's a royal pain, and a rather
         // severe layer violation. In practice, we're going to ferret
         // the ID out of the the Via header field, which is where we
         // squirreled it away when we sent this request in the first place.
         // !bwc! This probably shouldn't be going out over the wire.
         Data compId = via.param(p_branch).getSigcompCompartment();
         if(!compId.empty())
         {
            mSigcompStack->provideCompartmentId(sc, compId.data(), compId.size());
         }
      }
   }
#endif

   mStateMachineFifo.add(message);
   ++mRxTransactionCnt;
   return origBufferConsumed;
}

#if TARGET_OS_IPHONE
void
TseUdpTransport::replaceSock()
{
   if (mFd == -1)
      return;

   Socket oldSock = mFd;

   tsc_close(mFd);

   mFd = internal_socket(transport(), ipVersion());
   mTuple.mFlowKey=(FlowKey)mFd;
   bind();     // also makes it non-blocking

   if (mPollGrp)
   {
      setPollGrp(mPollGrp);
   }

   InfoLog (<< "UDP socket fd=" << oldSock << " has been replaced with " << mFd);
}
#endif

#endif

/* ====================================================================
 * The Vovida Software License, Version 1.0
 *
 * Copyright (c) 2000 Vovida Networks, Inc.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in
 *    the documentation and/or other materials provided with the
 *    distribution.
 *
 * 3. The names "VOCAL", "Vovida Open Communication Application Library",
 *    and "Vovida Open Communication Application Library (VOCAL)" must
 *    not be used to endorse or promote products derived from this
 *    software without prior written permission. For written
 *    permission, <NAME_EMAIL>.
 *
 * 4. Products derived from this software may not be called "VOCAL", nor
 *    may "VOCAL" appear in their name, without prior written
 *    permission of Vovida Networks, Inc.
 *
 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, TITLE AND
 * NON-INFRINGEMENT ARE DISCLAIMED.  IN NO EVENT SHALL VOVIDA
 * NETWORKS, INC. OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT DAMAGES
 * IN EXCESS OF $1,000, NOR FOR ANY INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
 * USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
 * DAMAGE.
 *
 * ====================================================================
 *
 * This software consists of voluntary contributions made by Vovida
 * Networks, Inc. and many individuals on behalf of Vovida Networks,
 * Inc.  For more information on Vovida Networks, Inc., please see
 * <http://www.vovida.org/>.
 *
 * vi: set shiftwidth=3 expandtab:
 */
