#pragma once

#include <resip/stack/TcpTransport.hxx>

namespace resip
{
   class TseTcpTransport : public TcpTransport
   {
   public:
      TseTcpTransport(Fifo<TransactionMessage>& fifo,
                   int portNum,
                   IpVersion version,
                   const Data& interfaceObj,
                   AfterSocketCreationFuncPtr afterSockCreationFunc = 0,
                   BeforeSocketCloseFuncPtr beforeSockCloseFunc = 0,
                   Compression &compression = Compression::Disabled,
		             unsigned transportFlags = 0)
                   : TcpTransport(fifo, portNum, version, interfaceObj, afterSockCreationFunc, beforeSockCloseFunc, compression, transportFlags)
      {
      }
   };
}