#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include <reflow/TurnSocketFactory.hxx>
#include <account/SipAccountSettings.h>

#include <tsc_control_api.h>
#include <tsc_socket_api.h>
#include <tsc_version.h>

namespace CPCAPI2
{
   class TseTurnSocketFactory : public flowmanager::TurnSocketFactory
   {
   public:
      TseTurnSocketFactory(void* tscconfig);
      virtual ~TseTurnSocketFactory();

      virtual std::shared_ptr<reTurn::TurnAsyncSocket> createSocket(
         resip::HighPerfReactor& reactor,
         resip::Resolver <resip::HighPerfReactor>& resolver,
         reTurn::TurnAsyncSocketHandler *handler,
         reTurn::StunTuple localBinding,
         recon::MediaStack::MediaType mediaType
      );
   private:
      tsc_handle mTscHandle;
      unsigned int mRedundancyFactor;
      bool mDoLoadBalancing;
      SipAccount::TunnelMediaTransportType mMediaTransportType;
   };
}

#endif