#pragma once

#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

namespace resip
{
class TscFdSet
{
public:
   TscFdSet() : size(0), numReady(0)
   {
      TSC_FD_ZERO(&read);
      TSC_FD_ZERO(&write);
      TSC_FD_ZERO(&except);
   }

   int select(struct tsc_timeval& tv)
   {
      return numReady = ::tsc_select(size, &read, &write, &except, &tv);
   }

   int selectMilliSeconds(unsigned long ms)
   {
      struct tsc_timeval tv;
      tv.tv_sec = (ms/1000);
      tv.tv_usec = (ms%1000)*1000;
      return select(tv);
   }

   bool readyToRead(int fd)
   {
      return (TSC_FD_ISSET(fd, &read) != 0);
   }

   bool readyToWrite(int fd)
   {
      return (TSC_FD_ISSET(fd, &write) != 0);
   }

   bool hasException(int fd)
   {
      return (TSC_FD_ISSET(fd,&except) != 0);
   }

   void setRead(int fd)
   {
      assert( TSC_FD_SETSIZE >= 8 );
#ifndef WIN32 // windows fd are not int's and don't start at 0 - this won't work in windows
      assert( fd < (int)TSC_FD_SETSIZE ); // redefineing FD_SETSIZE will not work 
#else
      assert(read.fd_count < TSC_FD_SETSIZE); // Ensure there is room to add new FD
#endif
      TSC_FD_SET(fd, &read);
      size = ( int(fd+1) > size) ? int(fd+1) : size;
   }

   void setWrite(int fd)
   {
#ifndef WIN32 // windows fd are not int's and don't start at 0 - this won't work in windows
      assert( fd < (int)TSC_FD_SETSIZE ); // redefinitn FD_SETSIZE will not work 
#else
      assert(write.fd_count < TSC_FD_SETSIZE); // Ensure there is room to add new FD
#endif
      TSC_FD_SET(fd, &write);
      size = ( int(fd+1) > size) ? int(fd+1) : size;
   }

   void setExcept(int fd)
   {
#ifndef WIN32 // windows fd are not int's and don't start at 0 - this won't work in windows
      assert( fd < (int)TSC_FD_SETSIZE ); // redefinitn FD_SETSIZE will not work 
#else
      assert(except.fd_count < TSC_FD_SETSIZE); // Ensure there is room to add new FD
#endif
      TSC_FD_SET(fd,&except);
      size = ( int(fd+1) > size) ? int(fd+1) : size;
   }


   void clear(int fd)
   {
      TSC_FD_CLR(fd, &read);
      TSC_FD_CLR(fd, &write);
      TSC_FD_CLR(fd, &except);
   }

   void reset()
   {
      size = 0;
      numReady = 0;
      TSC_FD_ZERO(&read);
      TSC_FD_ZERO(&write);
      TSC_FD_ZERO(&except);
   }

   // Make this stuff public for async dns/ares to use
   tsc_fd_set read;
   tsc_fd_set write;
   tsc_fd_set except;
   int size;
   int numReady;  // set after each select call
};
}
#endif