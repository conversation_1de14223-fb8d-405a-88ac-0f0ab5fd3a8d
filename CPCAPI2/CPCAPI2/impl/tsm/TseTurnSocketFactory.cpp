#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TseTurnSocketFactory.h"
#include "TurnAsyncTseUdpSocket.h"

#include "TseTunnelManager.h"

#include <rutil/Logger.hxx>

#define RESIPROCATE_SUBSYSTEM resip::Subsystem::TRANSPORT

using namespace CPCAPI2;

TseTurnSocketFactory::TseTurnSocketFactory(void* tscconfig)
   : mTscHandle(((TseTunnelConfig*)tscconfig)->handle),
   mRedundancyFactor(((TseTunnelConfig*)tscconfig)->redundancy_factor),
   mDoLoadBalancing(((TseTunnelConfig*)tscconfig)->do_load_balancing),
   mMediaTransportType(((TseTunnelConfig*)tscconfig)->media_transport)
{
}

TseTurnSocketFactory::~TseTurnSocketFactory()
{
}

std::shared_ptr<reTurn::TurnAsyncSocket> 
TseTurnSocketFactory::createSocket(
   resip::HighPerfReactor& reactor,
   resip::Resolver <resip::HighPerfReactor>& resolver,
   reTurn::TurnAsyncSocketHandler *handler,
   reTurn::StunTuple localBinding,
   recon::MediaStack::MediaType mediaType
)
{
   std::shared_ptr<reTurn::TurnAsyncSocket> result;

   switch (localBinding.getTransportType())
   {
   case reTurn::StunTuple::UDP:
      result.reset(new TurnAsyncTseUdpSocket(mTscHandle, reactor, resolver, handler, resip::Tuple::inet_ntop(localBinding.getAddress()), localBinding.getPort(), mRedundancyFactor, mDoLoadBalancing, mMediaTransportType, mediaType));
      break;
   case reTurn::StunTuple::TCP:
      ErrLog(<< "TCP not supported as a SIP media transport for TSCF. Please use UDP.");
      //result.reset(new TurnAsyncTseTcpSocket(mIOService, handler, mLocalBinding.getAddress(), mLocalBinding.getPort()));
      break;
#ifdef USE_SSL
   case reTurn::StunTuple::TLS:
      ErrLog(<< "TLS not supported as a SIP media transport for TSCF. Please use UDP.");
      //result.reset(new TurnAsyncTseTlsSocket(mIOService, 
      //                                         mSslContext, 
      //                                         false, // validateServerCertificateHostname - TODO - make this configurable
      //                                         this, 
      //                                         mLocalBinding.getAddress(), 
      //                                         mLocalBinding.getPort()));
#endif
      break;
   default:
      // Bad Transport type!
      assert(false);
   }
   return result;
}
#endif