#include "brand_branded.h"

#if (CPCAPI2_BRAND_ACME_TSCF_MODULE == 1)

#include "TurnAsyncTseUdpSocket.h"

#include <rutil/Logger.hxx>

using namespace CPCAPI2;

#define NO_CHANNEL ((unsigned short)-1)
#define RECEIVE_BUFFER_SIZE 4096
#define RESIPROCATE_SUBSYSTEM resip::Subsystem::TRANSPORT

TurnAsyncTseUdpSocket::TurnAsyncTseUdpSocket(
   tsc_handle tschandle,
   resip::HighPerfReactor& reactor,
   resip::Resolver <resip::HighPerfReactor>& resolver,
   reTurn::TurnAsyncSocketHandler* turnAsyncSocketHandler,
   const resip::Data& address, 
   unsigned short port,
   unsigned int redundancyFactor,
   bool doLoadBalancing,
   SipAccount::TunnelMediaTransportType mediaTransportType,
   recon::MediaStack::MediaType mediaType)
   : TurnAsyncUdpSocket(reactor, resolver, turnAsyncSocketHandler, address, port),
     mTs<PERSON><PERSON><PERSON><PERSON>(tschandle),
     mRedundancyFactor(redundancyFactor),
     mDoLoadBalancing(doLoadBalancing),
     mMediaTransportType(mediaTransportType),
     mMediaType(mediaType),
     mThread(NULL),
     mShutdown(false)
{
   if (mFd != -1)
   {
      resip::closeSocket(mFd);
      mFd = -1;
   }
   bind(address, port);

   mThread = std::make_shared<std::thread>(std::bind(&TurnAsyncTseUdpSocket::transport_thread, this));
}

TurnAsyncTseUdpSocket::~TurnAsyncTseUdpSocket()
{
   {
      resip::Lock lock(mShutdownMutex);
      mShutdown = true;
   }
   mThread->join();
}

// AsyncSocketBase
reTurn::asio_error_code TurnAsyncTseUdpSocket::bind(const resip::Data& address, unsigned short port)
{
   reTurn::asio_error_code ec;
   mFd = tsc_socket(mTscHandle, AF_INET, SOCK_DGRAM, IPPROTO_UDP);
   if (mFd == SOCKET_ERROR)
   {
      ErrLog(<< "Failed to create async TSCF socket, " << get_errno_string(tsc_get_errno()));
      ec = reTurn::asio_error_code(reTurn::GeneralError, tsc_get_errno());
      return ec;
   }
   int flags = tsc_fcntl(mFd, F_GETFL, 0);
   flags = tsc_fcntl(mFd, F_SETFL, flags | O_NONBLOCK);
   if (flags != 0)
   {
      ErrLog(<< "Failed to make async TSCF socket non-blocking, " << get_errno_string(tsc_get_errno()));
      ec = reTurn::asio_error_code(reTurn::GeneralError, tsc_get_errno());
      return ec;
   }

   tsc_handle tunnel = tsc_get_tunnel(mFd);
   tsc_tunnel_socket_info tunnel_info;
   tsc_get_tunnel_socket_info(tunnel, &tunnel_info);

   if (mMediaType == recon::MediaStack::MediaType_Audio && mRedundancyFactor > 0) {
      tsc_so_redundancy redundancy;
      memset(&redundancy, 0, sizeof(tsc_so_redundancy));
      redundancy.redundancy_factor = (uint8_t)mRedundancyFactor;      

      if (tunnel_info.transport == tsc_transport_udp || tunnel_info.transport == tsc_transport_dtls) {
         redundancy.redundancy_method = tsc_so_redundancy_method_udp_dtls;
      } else {
         if (mDoLoadBalancing) {
            redundancy.redundancy_method = tsc_so_redundancy_method_tcp_tls_load_balance;
         } else {
            redundancy.redundancy_method = tsc_so_redundancy_method_tcp_tls_fan_out;
         }
      }

      if (tsc_setsockopt(mFd, SOL_SOCKET, SO_TSC_REDUNDANCY, (char *)&redundancy, sizeof(tsc_so_redundancy)) == -1) {
         WarningLog(<< "Failed to set redundancy");
      }
   }

   if (mMediaTransportType != SipAccount::TunnelMediaTransport_Default && tunnel_info.transport == tsc_transport_tls)
   {
      tsc_so_tunnel_transport tt;
      switch (mMediaTransportType)
      {
      case SipAccount::TunnelMediaTransport_DatagramPreferred:
         tt = tsc_so_tunnel_transport_datagram_preferred;
         break;
      case SipAccount::TunnelMediaTransport_DatagramOnly:
         tt = tsc_so_tunnel_transport_datagram_only;
         break;
      case SipAccount::TunnelMediaTransport_StreamPreferred:
         tt = tsc_so_tunnel_transport_stream_preferred;
         break;
      case SipAccount::TunnelMediaTransport_StreamOnly:
         tt = tsc_so_tunnel_transport_stream_only;
         break;
      case SipAccount::TunnelMediaTransport_Default:
      default:
         tt = tsc_so_tunnel_transport_default;
         break;
      }

      if (tsc_setsockopt(mFd, SOL_SOCKET, SO_TSC_TUNNEL_TRANSPORT, (char*)&tt, sizeof(tt)) != 0)
      {
         WarningLog(<< "Failed to set media transport type.");
      }
   }

   resip::Tuple t(address, port, resip::UDP);
   if (tsc_bind(mFd, &t.getMutableSockaddr(), t.length()) == SOCKET_ERROR)
   {
      ErrLog(<< "Failed to bind async TSCF socket, " << get_errno_string(tsc_get_errno()));
      ec = reTurn::asio_error_code(reTurn::GeneralError, tsc_get_errno());
      return ec;
   }
   return ec;
}

void TurnAsyncTseUdpSocket::transportSend(const reTurn::StunTuple& destination, reTurn::DataBuffer* buffer)
{
   resip::Tuple t(destination.getSockaddr(), resip::UDP);
   t.setPort(destination.getPort());
   if (tsc_sendto(mFd, buffer->mutableData(), buffer->size(), 0, &t.getMutableSockaddr(), t.length()) == SOCKET_ERROR)
   {
      ErrLog(<< "Failed to send data on async TSCF socket, " << get_errno_string(tsc_get_errno()));
   }
}

void TurnAsyncTseUdpSocket::transportClose()
{
   mReactor.unregisterEventHandler(this);
   {
      resip::Lock lock(mShutdownMutex);
      mShutdown = true;
   }
}

void TurnAsyncTseUdpSocket::process(ReactorEventHandler::FdSetType& fdset)
{
   fd_set readfds;
   FD_ZERO(&readfds);
   FD_SET(mFakeSocket.getSocketDescriptor(), &readfds);
   timeval tv = { 0, 0 };

   if (select(mFakeSocket.getSocketDescriptor() + 1, &readfds, NULL, NULL, &tv))
   {
      doReadOperations();
      mFakeSocket.receive();
   }
}

void TurnAsyncTseUdpSocket::buildFdSet(ReactorEventHandler::FdSetType& fdset)
{
   fdset.setRead(mFakeSocket.getSocketDescriptor());

   /*if (mTxFifoOutBuffer.messageAvailable())
   {
      fdset.setWrite(mFakeSocket.getSocketDescriptor());
   }*/
}

void TurnAsyncTseUdpSocket::transport_thread()
{
   const int sleepDurationMs = 50;
   while (1)
   {
      fd_set readfds;
      FD_ZERO(&readfds);
      FD_SET(mFakeSocket.getSocketDescriptor(), &readfds);
      timeval tv = { 0, 0 };
      if (!select(mFakeSocket.getSocketDescriptor() + 1, &readfds, NULL, NULL, &tv))
      {
         int size = mFd + 1;
         tsc_fd_set read;
         TSC_FD_ZERO(&read);
         TSC_FD_SET(mFd, &read);
         tsc_fd_set write;
         TSC_FD_ZERO(&write);
         tsc_fd_set except;
         TSC_FD_ZERO(&except);
         struct tsc_timeval tv;
         tv.tv_sec = 0;
         tv.tv_usec = sleepDurationMs * 1000;

         int numReady = tsc_select(size, &read, &write, &except, &tv);

         if (numReady > 0)
         {
            mFakeSocket.send();
         }
         else
         {
            // Possibly failed to get tunnel lock due to other media transports doing the same thing, wait a bit and try again
            tsc_sleep(sleepDurationMs);
         }
      }
      else
      {
         tsc_sleep(10);
      }

      {
         resip::Lock lock(mShutdownMutex);
         if (mShutdown)
         {
            if (mFd != -1)
            {
               tsc_close(mFd);
               mFd = -1;
            }
            break;
         }
      }
   }
}

void TurnAsyncTseUdpSocket::doReadOperations()
{
   resip::Tuple sender;
#ifdef __THREADX
   int slen = sender.length();
#else
   socklen_t slen = sender.length();
#endif
   mReceiveBuffer->mutableSize() = RECEIVE_BUFFER_SIZE;
   int num_bytes = tsc_recvfrom(mFd, mReceiveBuffer->mutableData(), mReceiveBuffer->size(), 0, &sender.getMutableSockaddr(), &slen);
   if (num_bytes == -1)
   {
      ErrLog(<< "Failed to receive data on async TSCF socket, " << get_errno_string(tsc_get_errno()));
   }
   else
   {
      //mSenderEndpoint = asio::ip::udp::endpoint(resip::Tuple_v4::from_string(resip::Tuple::inet_ntop(sender).c_str()), sender.getPort());
   }
   
   mReceiveBuffer->mutableSize() = num_bytes;
   if (num_bytes >= 0) handleReceivedData(sender, mReceiveBuffer);
}

#endif
