#include "brand_branded.h"

#if (CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE == 1)

#include "LogcatMonitorInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/ParseBuffer.hxx>
#include <rutil/Log.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/Random.hxx>

#ifdef ANDROID
#include <android/log.h>
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::Logcat;


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

LogcatMonitorInterface::LogcatMonitorInterface(resip::MultiReactor* reactor) :
   EventSource<int, LogcatHandler, LogcatSyncHandler> (*reactor),
   mMonitoring(false)
{
}

LogcatMonitorInterface::~LogcatMonitorInterface()
{
}

// https://stackoverflow.com/questions/1583353/how-to-read-exactly-one-line
bool ReadLine (int fd, std::string* line) {
  // We read-ahead, so we store in static buffer
  // what we already read, but not yet returned by ReadLine.
  static std::string buffer;

  // Do the real reading from fd until buffer has '\n'.
  std::string::iterator pos;
  while ((pos = find (buffer.begin(), buffer.end(), '\n')) == buffer.end ()) {
    char buf [1025];
    int n = read (fd, buf, 1024);
    if (n == -1) {    // handle errors
      *line = buffer;
      buffer = "";
      return false;
    }
    buf [n] = 0;
    buffer += buf;
  }

  // Split the buffer around '\n' found and return first part.
  *line = std::string (buffer.begin(), pos);
  buffer = std::string (pos + 1, buffer.end());
  return true;
}

int LogcatMonitorInterface::startMonitoring()
{
   if (!mMonitoring)
   {
      mMonitoring = true;
      mReactor.post(resip::resip_bind(&LogcatMonitorInterface::startMonitoringImpl, this));
   }
   
   return kSuccess;
}

int LogcatMonitorInterface::startMonitoringImpl()
{
   int numbytes;
   char buf[2048];
   struct hostent *he;
   struct sockaddr_in their_addr;

   if ((mSocketFd = socket(AF_INET, SOCK_STREAM, 0)) == -1)
   {
      ErrLog(<< "LogcatMonitor failed to create fd");
      return kSuccess;
   }

   their_addr.sin_family = AF_INET;
   their_addr.sin_port = htons(mSettings.port);
   inet_pton(AF_INET, mSettings.ip.c_str(), &(their_addr.sin_addr));

   bzero(&(their_addr.sin_zero), 8);

   InfoLog(<< "LogcatMonitor attempting to connect to server");

   if (connect(mSocketFd, (struct sockaddr *)&their_addr,
              sizeof(struct sockaddr)) == -1)
   {
      ErrLog(<< "LogcatMonitor failed to connect");
      return kSuccess;
   }

   InfoLog(<< "LogcatMonitor successfully connected to server");
   
   // netcat (if used as server we're connecting to) will buffer old logat
   // messages it has received while there has been no client connected.
   // flush those here, so we're only dealing with real time logcat messages.
   for (;;)
   {
      if (recv(mSocketFd, buf, 1024, MSG_DONTWAIT) < 0)
      {
         if (errno == EAGAIN || errno == EWOULDBLOCK)
         {
            break;
         }
         else
         {
            ErrLog(<< "Some other error flushing: " << errno);
         }
      }
   }

   DebugLog(<< "LogcatMonitor finished flushing; monitoring");
      
   LogcatConnectedEvent evt;
   fireEvent( cpcFunc( LogcatHandler::onLogcatConnected ), 0, evt );
   
   std::string line;
   while (1)
   {
      if (ReadLine(mSocketFd, &line))
      {
         processLogcatLine(line.c_str());
      }
      else
      {
         ErrLog(<< "LogcatMonitor failed while reading lines");
         return kSuccess;
      }
   }

   close(mSocketFd);
   
   return kSuccess;
}

int LogcatMonitorInterface::configureSettings(const LogcatMonitorSettings& settings)
{
   mReactor.post(resip::resip_bind(&LogcatMonitorInterface::configureSettingsImpl, this, settings));
   return kSuccess;
}

int LogcatMonitorInterface::configureSettingsImpl(const LogcatMonitorSettings& settings)
{
   mSettings = settings;
   return kSuccess;
}

int LogcatMonitorInterface::logcatTestMonitor()
{
   mActiveTestString = resip::Random::getCryptoRandomBase64(8);

#ifdef ANDROID
    __android_log_print(ANDROID_LOG_INFO, "CPCAPI2_LOGCATMONITOR", "LogcatMonitorInterface>>TEST>>%s", mActiveTestString.c_str());
#endif

   return kSuccess;
}

int LogcatMonitorInterface::processLogcatLine(const char* logcatLine)
{
   // 2019-08-18 17:52:32.309 3225-3561/? I AudioService: setMode(mode = 3, callingPackage = com.counterpath.sdkdemo.advancedaudiocall, Process ID: 4561

   try
   {
      resip::ParseBuffer pb(logcatLine);
      
      {
         // note this does not appear to be logged on all devices; is logged on Zebra devices but not Pixel devices
         // seen on TC52
         const char* audioModePrefix = "AudioService: setMode(mode = ";
         pb.skipToChars(audioModePrefix);
         if (!pb.eof())
         {
            DebugLog(<< "Found onAudioModeChange");
         
            LogcatAudioModeChangeEvent evt;
            
            pb.skipN(strlen(audioModePrefix));
            evt.newMode = pb.integer();
            
            fireEvent( cpcFunc( LogcatHandler::onAudioModeChange ), 0, evt );
            return kSuccess;
         }
         pb.reset(logcatLine);
      }
      
      {
         // seen on TC51 Android 8.1
         const char* audioModePrefix = "AudioService: setMode(mode, binder) AUDIO_MODE_CHANGED Mode = ";
         pb.skipToChars(audioModePrefix);
         if (!pb.eof())
         {
            DebugLog(<< "Found onAudioModeChange");
         
            LogcatAudioModeChangeEvent evt;
            
            pb.skipN(strlen(audioModePrefix));
            evt.newMode = pb.integer();
            
            fireEvent( cpcFunc( LogcatHandler::onAudioModeChange ), 0, evt );
            return kSuccess;
         }
         pb.reset(logcatLine);
      }

      {
         const char* logcatMonitorTestPrefix = "LogcatMonitorInterface>>TEST>>";
         pb.skipToChars(logcatMonitorTestPrefix);
         if (!pb.eof())
         {
            DebugLog(<< "Found onLocatTestMonitorSuccess");
         
            LogcatTestMonitorSuccessEvent evt;
            
            pb.skipN(strlen(logcatMonitorTestPrefix));
            if (resip::Data(pb.position().operator const char*()) == mActiveTestString)
            {
               fireEvent( cpcFunc( LogcatHandler::onLogcatTestMonitorSuccess ), 0, evt );
            }
            else
            {
               ErrLog(<< "Found but doesn't match");
            }
            
            return kSuccess;
         }
         pb.reset(logcatLine);
      }

      
   }
   catch(resip::BaseException& ex)
   {
      ErrLog(<< "Process logcat exception: " << logcatLine << " exception: " << ex);
   }

   return kSuccess;
}

int LogcatMonitorInterface::setHandler(LogcatHandler* handler)
{
   setAppHandler(0, handler);

   return kSuccess;
}

#endif // #if (CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE == 1)
