#ifndef LogcatMonitorInterface_h
#define LogcatMonitorInterface_h

#include <logcatmonitor/LogcatMonitor.h>
#include <logcatmonitor/LogcatHandler.h>
#include <phone/PhoneModule.h>
#include <phone/Cpcapi2EventSource.h>

using namespace CPCAPI2::Logcat;


class LogcatMonitorInterface : public LogcatMonitor, public CPCAPI2::EventSource<int, LogcatHandler, LogcatSyncHandler>
{
public:
   LogcatMonitorInterface(resip::MultiReactor* reactor);
   virtual ~LogcatMonitorInterface();
   int configureSettings(const LogcatMonitorSettings& settings) OVERRIDE;
   int startMonitoring() OVERRIDE;
   int logcatTestMonitor() OVERRIDE;

private:
   int configureSettingsImpl(const LogcatMonitorSettings& settings);
   int startMonitoringImpl();

   int processLogcatLine(const char* logcatLine);
   int setHandler(LogcatHandler* handler) OVERRIDE;
   
   
   // PhoneModule
   void PreRelease() {}
   void Release() {}
   
private:
   int mSocketFd;
   resip::Data mActiveTestString;
   LogcatMonitorSettings mSettings;
   std::atomic<bool> mMonitoring;
   
};



#endif /* LogcatMonitorInterface_h */
