#include "brand_branded.h"

#include <logcatmonitor/LogcatMonitor.h>

#if (CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE == 1)
#include <logcat/LogcatMonitorInterface.h>
#endif

#include "impl/phone/PhoneInterface.h"


namespace CPCAPI2
{
namespace Logcat
{


#if (CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE == 1)
static LogcatMonitor* gInstance;
static std::once_flag gInstanceFlag;

static void initInstance()
{
   resip::MultiReactor* reactor = new resip::MultiReactor("LogcatMonitor");
   gInstance = new LogcatMonitorInterface(reactor);
   reactor->start();
}
#endif

LogcatMonitor* LogcatMonitor::getInterface()
{
#if (CPCAPI2_BRAND_LOGCAT_MONITOR_MODULE == 1)
   std::call_once(gInstanceFlag, &initInstance);
   return gInstance;
#else
   return NULL;
#endif
}


}
}
