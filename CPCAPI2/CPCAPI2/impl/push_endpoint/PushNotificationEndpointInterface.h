#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_INTERFACE_H

#include "cpcapi2defs.h"
#include "push_endpoint/PushNotificationEndpointManagerInternal.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "push_service/PushNotificationServiceInterface.h"

#include "phone/Cpcapi2EventSource.h"
#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>

namespace CPCAPI2
{

class PhoneInterface;

namespace PushService
{
class RedisAccessBase;
}

namespace PushEndpoint
{

class PushNotificationEndpointSyncHandler {};

class PushNotificationEndpointInterface : public CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointSyncHandler>, 
                                          public PushNotificationEndpointManagerInternal,
                                          public PhoneModule
{

public:

   PushNotificationEndpointInterface(Phone* phone);
   virtual ~PushNotificationEndpointInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointSyncHandler>::setCallbackHook(cbHook, context);
   }

   // PushNotificationEndpointManagerInternal
   // static PushNotificationEndpointManagerInternal* getInternalInterface(Phone* cpcPhone);
   virtual void addEventObserver(PushNotificationEndpointHandler* observer) OVERRIDE;
   virtual void removeEventObserver(PushNotificationEndpointHandler* observer) OVERRIDE;
   virtual void setEventCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;

   // PushNotificationEndpointManager
   virtual int setPushNotificationService(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr) OVERRIDE;
   virtual int setHandler(PushNotificationEndpointHandle device, PushNotificationEndpointHandler* handler) OVERRIDE;
   virtual PushNotificationEndpointHandle createPushNotificationEndpoint() OVERRIDE;
   virtual int registerForPushNotifications(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo) OVERRIDE;
   virtual int unregisterForPushNotifications(PushNotificationEndpointHandle device) OVERRIDE;
   virtual int queryPushRegistrationList() OVERRIDE;
   virtual int queryPushRegistrationInfo(PushNotificationEndpointHandle device) OVERRIDE;

private:

   void addEventObserverImpl(PushNotificationEndpointHandler* observer);
   void removeEventObserverImpl(PushNotificationEndpointHandler* observer);
   void createPushNotificationEndpointImpl(PushNotificationEndpointHandle h);
   void registerForPushNotificationsImpl(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo);
   void unregisterForPushNotificationsImpl(PushNotificationEndpointHandle device);
   void setPushNotificationServiceImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr);
   void queryPushRegistrationListImpl();
   void queryPushRegistrationInfoImpl(PushNotificationEndpointHandle device);

private:

   PhoneInterface* mPhone;
   CPCAPI2::PushService::PushNotificationServiceInterface* mPushServiceIf;
   CPCAPI2::PushService::RedisAccessBase* mPushDb;

   struct PushNotificationEndpointImpl
   {
   };
   std::map<PushNotificationEndpointHandle, PushNotificationRegistrationInfo> mEndpoints;

};

class PushNotificationEndpointHandleFactory
{

public:

   static PushNotificationEndpointHandle getNext() { return sNextHandle++; }

private:

   static std::atomic<PushNotificationEndpointHandle> sNextHandle;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushEndpoint::PushDatabaseSettings& args);
   
}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_INTERFACE_H
