#include "brand_branded.h"

#include "interface/experimental/push_endpoint/PushNotificationEndpointJsonProxy.h"
#include "interface/experimental/push_endpoint/PushNotificationEndpointJsonApi.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "PushNotificationEndpointJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "PushNotificationEndpointJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace PushEndpoint
{
   PushNotificationEndpointJsonApi* PushNotificationEndpointJsonApi::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<PushNotificationEndpointJsonServerInterface>(phone, "PushNotificationEndpointJsonApi");
#else
      return NULL;
#endif
   }

   PushNotificationEndpointManagerJsonProxy* PushNotificationEndpointManagerJsonProxy::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<PushNotificationEndpointJsonProxyInterface>(phone, "PushNotificationEndpointManagerJsonProxy");
#else
      return NULL;
#endif
   }
}
}
