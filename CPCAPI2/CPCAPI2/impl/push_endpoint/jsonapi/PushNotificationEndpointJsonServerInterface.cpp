#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "PushNotificationEndpointJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define JSON_MODULE "PushNotificationEndpointManagerJsonProxy"

using namespace CPCAPI2::PushEndpoint;

PushNotificationEndpointJsonServerInterface::PushNotificationEndpointJsonServerInterface(CPCAPI2::Phone* phone) :
   CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonServerSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mPushNotificationEndpointManager(CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(phone))
{
   mFunctionMap["createPushNotificationEndpoint"] = std::bind(&PushNotificationEndpointJsonServerInterface::handleCreatePushNotificationEndpoint, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["registerForPushNotifications"] = std::bind(&PushNotificationEndpointJsonServerInterface::handleRegisterForPushNotifications, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["unregisterForPushNotifications"] = std::bind(&PushNotificationEndpointJsonServerInterface::handleUnregisterForPushNotifications, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
   dynamic_cast<PushNotificationEndpointInterface*>(mPushNotificationEndpointManager)->addSdkObserver(this);
}

PushNotificationEndpointJsonServerInterface::~PushNotificationEndpointJsonServerInterface()
{
   dynamic_cast<PushNotificationEndpointInterface*>(mPushNotificationEndpointManager)->removeSdkObserver(this);
}

void PushNotificationEndpointJsonServerInterface::Release()
{
   delete this;
}

int PushNotificationEndpointJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void PushNotificationEndpointJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int PushNotificationEndpointJsonServerInterface::handleCreatePushNotificationEndpoint(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "PushNotificationEndpointJsonServerInterface::handleCreatePushNotificationEndpoint()");

   PushNotificationEndpointHandle h = mPushNotificationEndpointManager->createPushNotificationEndpoint();

   if (h == 0)
   {
      InfoLog(<< "PushNotificationEndpointJsonServerInterface::handleCreatePushNotificationEndpoint(): failure");
   }

   JsonFunctionCall(mTransport, "onCreatePushNotificationEndpoint", "device", h);

   return kSuccess;
}

int PushNotificationEndpointJsonServerInterface::handleRegisterForPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "PushNotificationEndpointJsonServerInterface::handleRegisterForPushNotifications()");

   PushNotificationRegistrationInfo regInfo;
   uint64_t h = 0;
   JsonDeserialize(functionObjectVal, "registrationInfo", regInfo, "device", h, "useSandbox", regInfo.apnInfo.useSandbox);

   CPCAPI2::JsonApi::AuthToken authToken;
   if (functionObjectVal.HasMember("device"))
   {
      regInfo.websocketInfo.jsonUserHandle = conn.jsonUserHandle;
      if (!conn.authToken.cp_user.empty() && !conn.authToken.device_uuid.empty())
      {
         regInfo.pushEndpointId = conn.authToken.cp_user + ":" + conn.authToken.device_uuid;
      }
      mPushNotificationEndpointManager->registerForPushNotifications(h, regInfo);
   }

   return kSuccess;
}

int PushNotificationEndpointJsonServerInterface::handleUnregisterForPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value & functionObjectVal)
{
   StackLog(<< "PushNotificationEndpointJsonServerInterface::handleUnregisterForPushNotifications()");

   if (functionObjectVal.HasMember("device"))
   {
      uint64_t h = 0;
      JsonDeserialize(functionObjectVal, "device", h);
      mPushNotificationEndpointManager->unregisterForPushNotifications(h);
   }
   return kSuccess;
}

int PushNotificationEndpointJsonServerInterface::onPushNotification(PushNotificationEndpointHandle device, const PushNotificationEvent& event )
{
   JsonFunctionCall(mTransport, "onPushNotification", JSON_VALUE(device), JSON_VALUE(event));
   return kSuccess;
}

int PushNotificationEndpointJsonServerInterface::onPushRegistrationSuccess(
   PushNotificationEndpointHandle device,
   const PushRegistrationSuccessEvent& event)
{
   JsonFunctionCall(mTransport, "onPushRegistrationSuccess", JSON_VALUE(device), JSON_VALUE(event));
   return kSuccess;
}

int PushNotificationEndpointJsonServerInterface::onPushRegistrationFailure(
   PushNotificationEndpointHandle device,
   const PushRegistrationFailureEvent& event)
{
   JsonFunctionCall(mTransport, "onPushRegistrationFailure",JSON_VALUE(device), JSON_VALUE(event));
   return kSuccess;
}


#endif
