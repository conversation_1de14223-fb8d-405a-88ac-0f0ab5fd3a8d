#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "PushNotificationEndpointJsonProxyInterface.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"
#include "util/IpHelpers.h"

#include <rutil/Logger.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define JSON_MODULE "PushNotificationEndpointJsonApi"

namespace CPCAPI2
{

namespace PushEndpoint
{

PushNotificationEndpointJsonProxyInterface::PushNotificationEndpointJsonProxyInterface(Phone* phone)
   : CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL)
{
   StackLog(<< "PushNotificationEndpointJsonProxyInterface::PushNotificationEndpointJsonProxyInterface(): phone: " << mPhone);
   mFunctionMap["onCreatePushNotificationEndpoint"] = std::bind(&PushNotificationEndpointJsonProxyInterface::handleCreatePushNotificationEndpoint, this, std::placeholders::_1 );
   mFunctionMap["onPushNotification"] = std::bind(&PushNotificationEndpointJsonProxyInterface::handlePushNotification, this, std::placeholders::_1);
   mFunctionMap["onPushRegistrationSuccess"] = std::bind(&PushNotificationEndpointJsonProxyInterface::handlePushRegistrationSuccess, this, std::placeholders::_1);
   mFunctionMap["onPushRegistrationFailure"] = std::bind(&PushNotificationEndpointJsonProxyInterface::handlePushRegistrationFailure, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());

   mPhone->addRefImpl();
}

PushNotificationEndpointJsonProxyInterface::~PushNotificationEndpointJsonProxyInterface()
{
   StackLog(<< "PushNotificationEndpointJsonProxyInterface::~PushNotificationEndpointJsonProxyInterface(): phone: " << mPhone);
   mPhone->releaseImpl();
}

void PushNotificationEndpointJsonProxyInterface::Release()
{
   StackLog(<< "PushNotificationEndpointJsonProxyInterface::Release(): phone: " << mPhone);
   delete this;
}

// JsonApiClientModule
void PushNotificationEndpointJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int PushNotificationEndpointJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void PushNotificationEndpointJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int PushNotificationEndpointJsonProxyInterface::setHandler(PushNotificationEndpointHandle h, PushNotificationEndpointHandler* handler)
{
   setAppHandler(h, handler);
   return kSuccess;
}

PushNotificationEndpointHandle PushNotificationEndpointJsonProxyInterface::createPushNotificationEndpoint()
{
   auto hF = mServerCreatedHandle.get_future();
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointJsonProxyInterface::createPushNotificationEndpointImpl, this));
   PushNotificationEndpointHandle h = hF.get();
   mServerCreatedHandle = std::promise<PushNotificationEndpointHandle>(); // reset for next use
   return h;
}

void PushNotificationEndpointJsonProxyInterface::createPushNotificationEndpointImpl()
{
   StackLog(<< "PushNotificationEndpointJsonProxyInterface::createPushNotificationEndpointImpl(): phone: " << mPhone);
   JsonFunctionCall(mTransport, "createPushNotificationEndpoint");
}

int PushNotificationEndpointJsonProxyInterface::registerForPushNotifications(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointJsonProxyInterface::registerForPushNotificationsImpl, this, device, registrationInfo));
   return kSuccess;
}

void PushNotificationEndpointJsonProxyInterface::registerForPushNotificationsImpl(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo)
{
   JsonFunctionCall(mTransport, "registerForPushNotifications", JSON_VALUE(device), JSON_VALUE(registrationInfo));
}

int PushNotificationEndpointJsonProxyInterface::unregisterForPushNotifications(PushNotificationEndpointHandle device)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointJsonProxyInterface::unregisterForPushNotificationsImpl, this, device));
   return kSuccess;
}

void PushNotificationEndpointJsonProxyInterface::unregisterForPushNotificationsImpl(PushNotificationEndpointHandle device)
{
   JsonFunctionCall(mTransport, "unregisterForPushNotifications", JSON_VALUE(device));
}

int PushNotificationEndpointJsonProxyInterface::handleCreatePushNotificationEndpoint(const rapidjson::Value& functionObjectVal)
{
   PushNotificationEndpointHandle device;
   JsonDeserialize(functionObjectVal, JSON_VALUE(device));
   mServerCreatedHandle.set_value(device);
   return kSuccess;
}

int PushNotificationEndpointJsonProxyInterface::handlePushNotification(const rapidjson::Value& functionObjectVal)
{
   PushNotificationEndpointHandle device = 0;
   PushNotificationEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(device), JSON_VALUE(event));

   fireEvent(cpcFunc(PushNotificationEndpointHandler::onPushNotification), device, event);
   return kSuccess;
}

int PushNotificationEndpointJsonProxyInterface::handlePushRegistrationSuccess(const rapidjson::Value& functionObjectVal)
{
   PushNotificationEndpointHandle device = 0;
   PushRegistrationSuccessEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(device), JSON_VALUE(event));

   StackLog(<< "PushNotificationEndpointJsonProxyInterface::handlePushRegistrationSuccess(): device handle: " << device);

   fireEvent(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationSuccess), device, event);
   return kSuccess;
}

int PushNotificationEndpointJsonProxyInterface::handlePushRegistrationFailure(const rapidjson::Value& functionObjectVal)
{
   PushNotificationEndpointHandle device = 0;
   PushRegistrationFailureEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(device), JSON_VALUE(event));

   StackLog(<< "PushNotificationEndpointJsonProxyInterface::handlePushRegistrationFailure(): device handle: " << device);
   fireEvent(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationFailure), device, event);
   return kSuccess;
}

}

}

#endif
