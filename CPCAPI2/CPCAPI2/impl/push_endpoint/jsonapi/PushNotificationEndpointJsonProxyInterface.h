#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_PROXY_INTERFACE_H

#include "push_endpoint/PushNotificationEndpointJsonProxy.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/Cpcapi2EventSource.h"
#include "phone/PhoneModule.h"
#include "util/AutoTestProcessor.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

#include <future>

namespace CPCAPI2
{
class PhoneInterface;
namespace PushEndpoint
{
class PushNotificationEndpointJsonSyncHandler {};
class PushNotificationEndpointJsonProxyInterface : public CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonSyncHandler>,
                                           public CPCAPI2::PushEndpoint::PushNotificationEndpointManagerJsonProxy,
                                           public CPCAPI2::JsonApi::JsonApiClientModule,
                                           public CPCAPI2::PhoneModule
{
   
public:
   
   PushNotificationEndpointJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~PushNotificationEndpointJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE {
      return CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonSyncHandler>::process(timeout);
   }
   virtual void interruptProcess() OVERRIDE {
      CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE {
      CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHandler, PushNotificationEndpointJsonSyncHandler>::setCallbackHook(cbHook, context);
   }

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // PushNotificationEndpointManagerJsonProxy

   // Inherited via PushNotificationEndpointManager
   virtual int setHandler(PushNotificationEndpointHandle device, PushNotificationEndpointHandler* handler) OVERRIDE;
   virtual PushNotificationEndpointHandle createPushNotificationEndpoint() OVERRIDE;
   virtual int registerForPushNotifications(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo) OVERRIDE;
   virtual int unregisterForPushNotifications(PushNotificationEndpointHandle device) OVERRIDE;
   virtual int setPushNotificationService(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr) OVERRIDE {
      return kSuccess;
   }

private:
   void createPushNotificationEndpointImpl();
   void registerForPushNotificationsImpl(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo);
   void unregisterForPushNotificationsImpl(PushNotificationEndpointHandle device);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);
   int handleCreatePushNotificationEndpoint(const rapidjson::Value& functionObjectVal);
   int handlePushNotification(const rapidjson::Value& functionObjectVal);
   int handlePushRegistrationSuccess(const rapidjson::Value& functionObjectVal);
   int handlePushRegistrationFailure(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::promise<PushNotificationEndpointHandle> mServerCreatedHandle;
};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_ENDPOINT_JSON_PROXY_INTERFACE_H
