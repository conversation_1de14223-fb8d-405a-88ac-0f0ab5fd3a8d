#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H)
#define CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "push_endpoint/PushNotificationEndpointJsonApi.h"
#include "push_endpoint/PushNotificationEndpointHandler.h"
#include "push_endpoint/PushNotificationEndpointInterface.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace PushEndpoint
{
class PushNotificationEndpointJsonServerSyncHandler {};
class PushNotificationEndpointJsonServerInterface : public CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpoint<PERSON><PERSON><PERSON>, PushNotificationEndpointJsonServerSyncHandler>,
                                            public CPCAPI2::PushEndpoint::PushNotificationEndpoint<PERSON>son<PERSON>pi,
                                            public CPCAPI2::PushEndpoint::PushNotificationEndpointHandler,
                                            public CPCAPI2::PushEndpoint::PushNotificationEndpointSyncHandler,
                                            public CPCAPI2::JsonApi::JsonApiServerModule,
                                            public CPCAPI2::PhoneModule
{
public:
   PushNotificationEndpointJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~PushNotificationEndpointJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // PushNotificationEndpointHandler
   virtual int onPushNotification(PushNotificationEndpointHandle device, const PushNotificationEvent& evt) OVERRIDE;
   virtual int onPushRegistrationSuccess(
      PushNotificationEndpointHandle h,
      const PushRegistrationSuccessEvent& evt) OVERRIDE;
   virtual int onPushRegistrationFailure(
      PushNotificationEndpointHandle h,
      const PushRegistrationFailureEvent& evt) OVERRIDE;

private:
   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   int handleCreatePushNotificationEndpoint(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleRegisterForPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleUnregisterForPushNotifications(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::PushEndpoint::PushNotificationEndpointManager* mPushNotificationEndpointManager;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo, const rapidjson::Value&)>> FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_PUSH_NOTIFICATION_CLIENT_JSON_SERVER_INTERFACE_H
