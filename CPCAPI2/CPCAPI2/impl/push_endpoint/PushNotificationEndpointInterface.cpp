#include "brand_branded.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "PushNotificationEndpointInterface.h"
#include "push_service/PushServiceRedisAccess.h"

#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <rutil/Random.hxx>

#include <future>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2::PushService;
using CPCAPI2::Phone;

namespace CPCAPI2
{
   
namespace PushEndpoint
{

std::atomic<PushNotificationEndpointHandle> PushNotificationEndpointHandleFactory::sNextHandle(1);

PushNotificationEndpointInterface::PushNotificationEndpointInterface(Phone* phone)
   : CPCAPI2::EventSource<PushNotificationEndpointHandle, PushNotificationEndpointHand<PERSON>, PushNotificationEndpointSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mPushServiceIf(NULL),
     mPushDb(NULL)
{
   StackLog(<< "PushNotificationEndpointInterface::PushNotificationEndpointInterface(): " << this << " phone: " << mPhone);
   mPhone->addRefImpl();
}

PushNotificationEndpointInterface::~PushNotificationEndpointInterface()
{
   StackLog(<< "PushNotificationEndpointInterface::~PushNotificationEndpointInterface(): " << this << " phone: " << mPhone);
   mPhone->releaseImpl();
   mEndpoints.clear();
}

void PushNotificationEndpointInterface::Release()
{
   StackLog(<< "PushNotificationEndpointInterface::Release(): " << this << " phone: " << mPhone);
   delete this;
}

PushNotificationEndpointManagerInternal* PushNotificationEndpointManagerInternal::getInternalInterface(Phone* cpcPhone)
{
   return static_cast<PushNotificationEndpointManagerInternal*>(PushNotificationEndpointManager::getInterface(cpcPhone));
}

void PushNotificationEndpointInterface::setEventCallbackHook(void(*cbHook)(void*), void* context)
{
   setCallbackHook(cbHook, context);
}

void PushNotificationEndpointInterface::addEventObserver(PushNotificationEndpointHandler* observer)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::addEventObserverImpl, this, observer));
}

void PushNotificationEndpointInterface::addEventObserverImpl(PushNotificationEndpointHandler* observer)
{
   addSdkObserver(observer);
}

void PushNotificationEndpointInterface::removeEventObserver(PushNotificationEndpointHandler* observer)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::removeEventObserverImpl, this, observer));
}

void PushNotificationEndpointInterface::removeEventObserverImpl(PushNotificationEndpointHandler* observer)
{
   removeSdkObserver(observer);
}

int PushNotificationEndpointInterface::setPushNotificationService(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::setPushNotificationServiceImpl, this, pushServiceMgr));
   return kSuccess;
}

void PushNotificationEndpointInterface::setPushNotificationServiceImpl(CPCAPI2::PushService::PushNotificationServiceManager* pushServiceMgr)
{
   mPushServiceIf = dynamic_cast<PushNotificationServiceInterface*>(pushServiceMgr);
   mPushDb = mPushServiceIf->getPushDatabaseAccess();
}

int PushNotificationEndpointInterface::setHandler(PushNotificationEndpointHandle device, PushNotificationEndpointHandler* pHandler)
{
   setAppHandler(device, pHandler);
   return kSuccess;
}

PushNotificationEndpointHandle PushNotificationEndpointInterface::createPushNotificationEndpoint()
{
   PushNotificationEndpointHandle h = PushNotificationEndpointHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::createPushNotificationEndpointImpl, this, h));
   return h;
}

void PushNotificationEndpointInterface::createPushNotificationEndpointImpl(PushNotificationEndpointHandle h)
{
}

int PushNotificationEndpointInterface::registerForPushNotifications(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::registerForPushNotificationsImpl, this, device, registrationInfo));
   return kSuccess;
}

void PushNotificationEndpointInterface::registerForPushNotificationsImpl(PushNotificationEndpointHandle device, const PushNotificationRegistrationInfo& registrationInfo)
{
   DebugLog(<< "PushNotificationEndpointInterface::registerForPushNotificationsImpl(): " << this << " phone: " << mPhone << " endpoint handle: " << device << registrationInfo);
   RedisAccessBase::AddPushRegistrationArgs addRegArgs;
   addRegArgs.deviceToken = registrationInfo.deviceToken;
   addRegArgs.pushNetworkType = registrationInfo.pushNetworkType;
   addRegArgs.apnsTopic = registrationInfo.apnInfo.apnsTopic;
   addRegArgs.apnsTopicPushKit = registrationInfo.apnInfo.apnsTopicPushKit;
   addRegArgs.pushKitToken = registrationInfo.apnInfo.pushKitToken;
   addRegArgs.useSandbox = registrationInfo.apnInfo.useSandbox;
   addRegArgs.jsonUserHandle = registrationInfo.websocketInfo.jsonUserHandle;
   addRegArgs.userPlusDeviceIdentity = registrationInfo.pushEndpointId;

   auto pr1 = std::make_shared<std::promise<RedisAccessBase::AddPushRegistrationResult> >();
   std::future<RedisAccessBase::AddPushRegistrationResult> f1 = pr1->get_future();
   mPushDb->addPushRegistration(addRegArgs, [pr1](bool, const RedisAccessBase::AddPushRegistrationResult& arr)
   {
      pr1->set_value(arr);
   });

   bool success = false;
   PushNotificationEndpointId endpointId;
   if (f1.wait_for(std::chrono::milliseconds(200000)) == std::future_status::ready)
   {
      RedisAccessBase::AddPushRegistrationResult pushResResult = f1.get();
      if (pushResResult.result == 0)
      {
         success = true;
         endpointId = pushResResult.endpointId;
      }
   }
   else
   {
      InfoLog(<< "PushNotificationEndpointInterface::registerForPushNotificationsImpl(): redis request timeout when attempting addPushRegistration");
   }

   if (success)
   {
      PushRegistrationSuccessEvent regSuccessArgs;
      regSuccessArgs.endpointId = endpointId;

      mEndpoints[device] = registrationInfo;
      mEndpoints[device].pushEndpointId = endpointId;
      fireEvent(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationSuccess), device, regSuccessArgs);
      return;
   }

   PushRegistrationFailureEvent regFailArgs;
   regFailArgs.errorText = "unknown error";
   fireEvent(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationFailure), device, regFailArgs);
}

int PushNotificationEndpointInterface::unregisterForPushNotifications(PushNotificationEndpointHandle device)
{
   postToSdkThread(resip::resip_bind(&PushNotificationEndpointInterface::unregisterForPushNotificationsImpl, this, device));
   return kSuccess;
}

void PushNotificationEndpointInterface::unregisterForPushNotificationsImpl(PushNotificationEndpointHandle device)
{
   mEndpoints.erase(device);
}

int PushNotificationEndpointInterface::queryPushRegistrationList()
{
   resip::ReadCallbackBase* cb = resip::resip_bind(&PushNotificationEndpointInterface::queryPushRegistrationListImpl, this);
   postToSdkThread(cb);
   return kSuccess;
}

void PushNotificationEndpointInterface::queryPushRegistrationListImpl()
{
   DebugLog(<< "PushNotificationEndpointInterface::queryPushRegistrationListImpl(): endpoint list size: " << mEndpoints.size());
   PushRegistrationQueryListResult result;

   for (std::map<PushNotificationEndpointHandle, PushNotificationRegistrationInfo>::iterator i = mEndpoints.begin(); i != mEndpoints.end(); ++i)
   {
      result.registrationList.push_back(i->first);
   }

   fireEventObservers(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationQueryListResult), result);
}

int PushNotificationEndpointInterface::queryPushRegistrationInfo(PushNotificationEndpointHandle device)
{
   resip::ReadCallbackBase* cb = resip::resip_bind(&PushNotificationEndpointInterface::queryPushRegistrationInfoImpl, this, device);
   postToSdkThread(cb);
   return kSuccess;
}

void PushNotificationEndpointInterface::queryPushRegistrationInfoImpl(PushNotificationEndpointHandle device)
{
   DebugLog(<< "PushNotificationEndpointInterface::queryPushRegistrationInfoImpl(): device: " << device << " endpoint list size: " << mEndpoints.size());

   PushRegistrationQueryInfoResult result;
   std::map<PushNotificationEndpointHandle, PushNotificationRegistrationInfo>::iterator i = mEndpoints.find(device);
   if (i != mEndpoints.end())
   {
      result.pushEndpointHandle = i->first;
      result.jsonUserHandle = i->second.websocketInfo.jsonUserHandle;
      result.pushEndpointId = i->second.pushEndpointId;
      result.pushNetworkType = i->second.pushNetworkType;
      result.apnsTopic = i->second.apnInfo.apnsTopic;
      result.deviceToken = i->second.deviceToken;
   }

   fireEventObservers(cpcFunc(PushNotificationEndpointHandler::onPushRegistrationQueryInfoResult), device, result);
}

cpc::string get_debug_string(const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& args)
{
   std::stringstream ss;
   ss << " PushNotificationRegistrationInfo: pushEndpointId: " << args.pushEndpointId << " jsonUserHandle: " << args.websocketInfo.jsonUserHandle
      << " pushNetworkType: " << args.pushNetworkType << " deviceToken: " << args.deviceToken
      << " apnsTopic: " << args.apnInfo.apnsTopic << " apnsTopicPushKit: " << args.apnInfo.apnsTopicPushKit
      << " pushKitToken: " << args.apnInfo.pushKitToken << " useSandbox: " << args.apnInfo.useSandbox;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::PushEndpoint::PushDatabaseSettings& args)
{
   std::stringstream ss;
   ss << " PushDatabaseSettings: redisIp: " << args.redisIp << " redisPort: " << args.redisPort;
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushEndpoint::PushNotificationRegistrationInfo& args)
{
   os << CPCAPI2::PushEndpoint::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushEndpoint::PushDatabaseSettings& args)
{
   os << CPCAPI2::PushEndpoint::get_debug_string(args);
   return os;
}
   
}
   
}

#endif
