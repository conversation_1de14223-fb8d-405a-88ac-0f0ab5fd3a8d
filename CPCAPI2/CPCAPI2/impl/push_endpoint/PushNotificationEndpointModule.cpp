#include "brand_branded.h"

#include "interface/public/cpcstl/string.h"
#include "interface/experimental/push_endpoint/PushNotificationEndpointManager.h"

#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
#include "PushNotificationEndpointInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace PushEndpoint
{
   PushNotificationEndpointManager* PushNotificationEndpointManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_PUSH_NOTIFICATION_CLIENT_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<PushNotificationEndpointInterface>(phone, "PushNotificationEndpointInterface");
#else
      return NULL;
#endif
   }

   const cpc::string& PushNotificationEndpointManager::getServiceId()
   {
      static cpc::string serviceId = "pushnotificationendpoint";
      return serviceId;
   }
}
}
