#if !defined(CPCAPI2_AUTO_TEST)

#if defined(BB10)

#include "NetworkChangeManager_BB10.h"
#include "../util/cpc_logger.h"

#include <rutil/DnsUtil.hxx>
#include <list>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{
NetworkChangeManager_BB10::NetworkChangeManager_BB10(Phone* phone) :
   NetworkChangeManagerImpl(phone),
   mNotifyCnt(0),
   mPollingThread(NULL)
{
}

NetworkChangeManager_BB10::~NetworkChangeManager_BB10()
{
   if (mPollingThread)
   {
      stop();
   }
}

void NetworkChangeManager_BB10::pollInterfacesThread()
{
   bool shouldNotify = false;
   int notifyWaitCnt = 0;
   const int sleepInterval = 1000;
   std::set<resip::Data> lastRead = NetworkChangeManagerImpl::getLocalIPAddresses();
   while (!mShutdown)
   {
      std::set<resip::Data> ifaces;

      bool bWifiAvailable = false;
      bool bMobileAvailable = false;
      bool bVpnAvailable = false;
      string newWifiIpAddress;
      string newMobileIpAddress;
      string newVpnIpAddress;

      netstatus_interface_list_t interfaceList;
      if ((netstatus_get_interfaces(&interfaceList)) != BPS_SUCCESS)
      {
        continue;
      }

      for (int i = 0; i < interfaceList.num_interfaces; i++)
      {
        const char *intrface = interfaceList.interfaces[i];

        netstatus_interface_details_t *details = NULL;
        if (netstatus_get_interface_details(intrface, &details) != BPS_SUCCESS)
        {
          continue;
        }
        netstatus_interface_type_t type = netstatus_interface_get_type(details);
        bool connected = netstatus_interface_is_connected(details);
        bool up = netstatus_interface_is_up(details);
        netstatus_ip_status_t status = netstatus_interface_get_ip_status(details);

        if (type == NETSTATUS_INTERFACE_TYPE_WIFI && connected)
        {
          for (int i = 0; i < netstatus_interface_get_num_ip_addresses(details); i++)
          {
            string address = netstatus_interface_get_ip_address(details, i);

            if (isValidIp(address) && !bWifiAvailable)
            {
                bWifiAvailable = true;
                newWifiIpAddress = address;
            }
          }
        }
        else if (type == NETSTATUS_INTERFACE_TYPE_CELLULAR && connected)
        {
          for (int i = 0; i < netstatus_interface_get_num_ip_addresses(details); i++)
          {
            string address = netstatus_interface_get_ip_address(details, i);

            if (isValidIp(address) && !bMobileAvailable)
            {
              bMobileAvailable = true;
              newMobileIpAddress = address;
            }
          }
        }
        else if (type == NETSTATUS_INTERFACE_TYPE_VPN && connected)
        {
          for (int i = 0; i < netstatus_interface_get_num_ip_addresses(details); i++)
          {
            string address = netstatus_interface_get_ip_address(details, i);

            if (isValidIp(address) && !bVpnAvailable)
            {
                bVpnAvailable = true;
                newVpnIpAddress = address;
            }
          }
        }

        netstatus_free_interface_details(&details);
      }

      if (bWifiAvailable)
      {
        ifaces.insert(newWifiIpAddress);
      }

      if (bMobileAvailable)
      {
        ifaces.insert(bMobileAvailable);
      }

      if (bVpnAvailable)
      {
        ifaces.insert(bVpnAvailable);
      }

      if (ifaces != lastRead)
      {
          InfoLog(<< "NetworkChangeManager_BB10 - sendNetworkChangeEvent");
          NetworkChangeEvent params;

          // @todo VPN transport isn't supported byt the sdk yet
          if (bWifiAvailable)
          {
            params.networkTransport = TransportWiFi;
          }
          else if (bMobileAvailable)
          {
            params.networkTransport = TransportWWAN;
          }
          else
          {
            params.networkTransport = TransportNone;
          }

          sendNetworkChangeEvent(params);
      }
      this_thread::sleep_for(chrono::milliseconds(1000));
   }
}

int NetworkChangeManager_BB10::start()
{
    if (mPollingThread)
    {
       return -1;
    }
    mPollingThread = new thread(std::bind(&NetworkChangeManager_BB10::pollInterfacesThread, this));
    return 0;
}

int NetworkChangeManager_BB10::stop()
{
    mShutdown = true;
    if (mPollingThread)
    {
       mPollingThread->join();
    }
    delete mPollingThread;
    mPollingThread = NULL;
    return 0;
}
}

#endif // BB10
#endif
