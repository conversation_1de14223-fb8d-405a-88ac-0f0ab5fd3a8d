#pragma once

#ifndef EVENT_QUEUE_POLLER_H
#define EVENT_QUEUE_POLLER_H

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <cpcapi2types.h>

#include "impl/phone/Cpcapi2EventSource2.h"
#include "impl/phone/EventSyncHandler.h"
#include "impl/phone/PhoneInterface.h"
#include "../experimental/phone/PhoneInternal.h"
#include "impl/util/APILogger.h"
#include "impl/util/AutoTestProcessor.h"
#include "impl/util/DumFpCommand.h"
#include "impl/util/LogSubsystems.h"

#include <rutil/Fifo.hxx>
#include <rutil/Reactor.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <functional>
#include <memory>
#include <set>
#include <deque>
#include <string>
#include <future>
#include <type_traits>
#include <regex>


namespace CPCAPI2
{

class EventQueueFactory
{
public:
   EventQueueFactory() {}
   virtual~ EventQueueFactory() {}
   static int getNext();
protected:
   static int sNextQueueId;
};

class EventQueue : public std::enable_shared_from_this<EventQueue>
{
public:

   static std::shared_ptr<EventQueue> create(resip::MultiReactor& reactor);

   virtual~ EventQueue();

   // Associated to interfaces exposed in PhoneInternal
   int createEventQueue(const std::vector<std::string>& events);
   void destroyEventQueue(int queueId);
   SdkEvent getEvent(int queueId, int timeout);

   // Used by sdk to add events to the registered queues
   // EventSourceQueueInterface
   virtual void addEvent(const SdkEvent& evt);

private:

   EventQueue(resip::MultiReactor& reactor);

   void getQueuesForEventImpl(const SdkEvent& evt, std::vector<int>& queues);

   int createEventQueueImpl(const std::vector<std::string>& events);
   void destroyEventQueueImpl(int queueId);
   void addEventImpl(const SdkEvent& evt);
   void addEventImpl(int queueId, SdkEvent&& evt);
   std::vector<std::regex> toRegex(const std::vector<std::string>& filter) const;

   resip::MultiReactor& getSdkReactor() const { return mReactor; }

   // Map the queue-id with the associated events
   std::map<int, std::vector<std::regex> > mEventSubscriptions;
   std::map<int, std::deque<SdkEvent> > mEventQueue;
   std::mutex mMutex;
   std::condition_variable mCondition;
   resip::MultiReactor& mReactor;
   std::promise<int> mDestroyQueueFuture;

};

}

#endif // EVENT_QUEUE_POLLER_H
