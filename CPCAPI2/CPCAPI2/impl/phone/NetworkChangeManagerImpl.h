#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_MANAGER_IMPL_H)
#define CPCAPI2_NETWORK_CHANGE_MANAGER_IMPL_H

#include "phone/NetworkChangeHandler.h"
#include "phone/NetworkChangeManager.h"
#include "NetworkChangeHandlerInternal.h"
#include "NetworkChangePollingManager.h"
#include "NetworkChangeManagerInterface.h"
#include "PhoneInterface.h"

#include <map>
#include <set>

#include <rutil/Data.hxx>
#include <rutil/Fifo.hxx>

namespace CPCAPI2
{
class NetworkChangeManagerImpl : public resip::ReactorBinded,
                                 public NetworkChangePollingManager,
                                 public NetworkChangePollingHandler
{
public:
   typedef int NetworkChangeMangerImplHandle;
   typedef std::set<resip::Data> NetworkChangeManagerInterfaceSet;

   static std::set<resip::Data> getLocalIPAddresses(bool includeLocalAddress = false);
   static void compareInterfaceLists(const NetworkChangeManagerInterfaceSet& oldIfs, const NetworkChangeManagerInterfaceSet& newIfs, 
                                     cpc::vector<cpc::string>& ifsUp, cpc::vector<cpc::string>& ifsDown);

   NetworkChangeManagerImpl(Phone* phone);

   virtual int start() = 0;
   virtual int stop() = 0;

   virtual NetworkTransport networkTransport() const { return mLastKnownNetworkTransport; }
   virtual cpc::string wwanType() const { return cpc::string(); };

   // current RSSI signal quality rating, if wifi is in use by the device. returns 0xFFFFF if not supported on platform
   virtual int currentWifiRssi() { return -1; }
   // current signal strength, if wifi is in use by the device. returns -1 if not supported on platform
   virtual int currentWifiSignalLevel() { return -1; }
   // maximum possible signal strength (*not* maximum historial signal strength), if wifi is in use by the device.
   // returns -1 if not supported on platform
   virtual int maxWifiSignalLevel() { return -1; }
   virtual int currentWifiFreqMhz() { return -1; }
   virtual int currentWifiChannel() { return -1; }
   virtual int currentWifiLinkSpeedMbps() { return -1; }

   virtual void sendNetworkChangeEvent(const NetworkChangeEvent& args);

   // Add handlers for the network change manager internal network events
   virtual int addObserver(NetworkChangeHandlerInternal* observer) { return -1; }
   virtual int removeObserver(NetworkChangeHandlerInternal* observer) { return -1; }
   // Trigger query to get current network transport rather than use last updated value. Currently only implemented on Android
   virtual NetworkTransport queryTransport() const { return mLastKnownNetworkTransport; }
   // Trigger query to get current network interfaces rather than use last updated list. Currently only implemented on Android
   virtual void queryInterfaces(NetworkChangeManagerInterfaceSet& interfaces) { interfaces = mLastKnownInterfaces; }
   virtual bool isStarted() const { return mStarted; }
   virtual bool isShutdown() const { return mShutdown; }

   // NetworkChangePollingManager
   virtual int addPollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE { return -1; }
   virtual int removePollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE { return -1; }
   virtual int setPollingConfig(const NetworkChangePollingConfig& config) OVERRIDE { return -1; }

   // NetworkChangePollingHandler
   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) OVERRIDE {}
   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) OVERRIDE {}

protected:
   virtual ~NetworkChangeManagerImpl() {}
   virtual void release() OVERRIDE {
      delete this;
   }

   virtual void sendNetworkChangeEventUnconditional(const NetworkChangeEvent& args);
   virtual void sendNetworkChangeEventUnconditionalImpl(const NetworkChangeEvent& args);
   virtual int wifiFreqToChannel(int wifiFreqMhz) const;
   virtual std::set<resip::Data> getLocalIPAddresses_NonStatic(bool includeLocalAddress = false);

   PhoneInterface* mPhone;
   NetworkChangeManagerInterface* mInterface;
   NetworkChangeManagerInterfaceSet mLastKnownInterfaces;
   NetworkTransport mLastKnownNetworkTransport;
   std::atomic<bool> mStarted;
   std::atomic<bool> mShutdown;

private:
   NetworkChangeManagerImpl();
};
}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_IMPL_H
