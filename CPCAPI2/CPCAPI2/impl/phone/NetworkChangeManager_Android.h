#pragma once

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_ANDROID_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_ANDROID_H

#ifdef ANDROID

#include "NetworkChangeManagerImpl.h"
#include "NetworkChangeHandlerInternal.h"
#include "NetworkChangePollingHandler.h"
#include "NetworkChangePollingImpl.h"

#include "../util/cpc_thread.h"
#include "../util/AutoResetEvent.h"

#include <set>
#include <atomic>
#include <future>
#include <mutex>
#include <rutil/Data.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/RecursiveMutex.hxx>
#include <rutil/DeadlineTimer.hxx>
#include "JniHelper.h"

#include <jni.h>


namespace CPCAPI2
{

class NetworkChangeManager_Android : public NetworkChangeManagerImpl
{

public:

   NetworkChangeManager_Android(Phone* phone);
   virtual ~NetworkChangeManager_Android();

   // NetworkChangeManagerImpl
   virtual int start() OVERRIDE;
   virtual int stop() OVERRIDE;
   virtual int addObserver(NetworkChangeHandlerInternal* observer) OVERRIDE;
   virtual int removeObserver(NetworkChangeHandlerInternal* observer) OVERRIDE;
   virtual NetworkTransport networkTransport() const OVERRIDE;
   virtual int currentWifiRssi() OVERRIDE;
   virtual int currentWifiSignalLevel() OVERRIDE;
   virtual int maxWifiSignalLevel() OVERRIDE;
   virtual int currentWifiFreqMhz() OVERRIDE;
   virtual int currentWifiChannel() OVERRIDE;
   virtual int currentWifiLinkSpeedMbps() OVERRIDE;
   virtual NetworkTransport queryTransport() const OVERRIDE;
   virtual void queryInterfaces(NetworkChangeManagerInterfaceSet& interfaces) OVERRIDE;
   virtual int addPollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE;
   virtual int removePollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE;
   virtual int setPollingConfig(const NetworkChangePollingConfig& config) OVERRIDE;
   virtual bool isStarted() const OVERRIDE;
   virtual bool isShutdown() const OVERRIDE;

   // NetworkChangePollingHandler
   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) OVERRIDE;
   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) OVERRIDE;

   // Jni Interface
   static bool isSecureDnsActive();
   static void notifyNetworkChanged(int nktType);
   static void notifyPrivateDnsChanged(bool secureDns);
   void onNetworkChange(int ntkType);
   int enableAndroidJavaNetworkChangeManager();
   int disableAndroidJavaNetworkChangeManager();

private:

   void startJniThread();
   void stopJniThread();
   void _onNetworkChange(int ntkType);

   // Utility functions to send network events to internal handlers
   void sendNetworkInitializeEvent();
   void sendNetworkShutdownEvent();
   void sendNetworkStatusEvent(CPCAPI2::NetworkChangeEvent& evt);

private:

   static std::vector<NetworkChangeManager_Android*> sNetworkChangeManagers;
   static std::mutex smNetworkChangeManagers;

   NetworkChangeManager_Android();

   static bool mLastDnsSecure;

   std::shared_ptr<NetworkChangePollingImpl> mPollingImpl;
   std::unique_ptr<CPCAPI2::Jni::JniThread> mJniThread;
   CPCAPI2::Jni::ScopedGlobalRef<jobject> mJniObject;

   typedef std::set<NetworkChangeHandlerInternal*> HandlersSet;
   HandlersSet mInternalHandlers;

};

}

#endif // ANDROID
#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_ANDROID_H
