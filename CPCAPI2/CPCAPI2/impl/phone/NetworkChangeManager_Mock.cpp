#if defined(CPCAPI2_AUTO_TEST)

#include "NetworkChangeManager_Mock.h"
#include "NetworkChangePollingHandler.h"
#include "NetworkChangePollingManager.h"
#include "../util/cpc_logger.h"
#include "phone/PhoneInterface.h"

#include <rutil/DnsUtil.hxx>
#include <rutil/dns/DnsStub.hxx>
#include <list>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{

bool NetworkChangeManager_Mock::mLastDnsSecure = false;

NetworkChangeManager_Mock::NetworkChangeManager_Mock(Phone* phone, NetworkTransport transport) :
NetworkChangeManagerImpl(phone),
mMockTransport(TransportNone)
{
   std::set<resip::Data> ifaces;
   ifaces.insert("127.0.0.1");
   setMockInterfaces(ifaces);
   setNetworkTransport(transport);
   // NetworkChangeManager_Polling sets mLastKnownNetworkTransport in its constructor
   mLastKnownNetworkTransport = transport;
 
   switch (PhoneInterface::externalResolver())
   {
   case ConnectionPreferences::ExternalResolverUsage_Disable:
   case ConnectionPreferences::ExternalResolverUsage_Enable:
      DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = false;
      break;
   case ConnectionPreferences::ExternalResolverUsage_Force:
      DnsStub::enableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = true;
      break;
   default:
      DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = false;
   }
}

NetworkChangeManager_Mock::~NetworkChangeManager_Mock()
{
   // Do not call delete directly, use release() instead
   if (mPollingImpl)
   {
      ErrLog(<< "NetworkChangeManager_Mock deleted without release()");
      stop();
   }
}

void NetworkChangeManager_Mock::release()
{
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Mock::release() shutting down");
      stop();
   }

   delete this;
}

int NetworkChangeManager_Mock::start()
{
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Mock::start(): network polling has already been started");
      return kError;
   }

   mShutdown = false;

   DebugLog(<< "NetworkChangeManager_Mock::start(): start network polling");
   mPollingImpl = std::make_shared<NetworkChangePollingImpl>(mPhone, this, this);
   NetworkChangePollingConfig pollingConfig(200, 800, 0);
   mPollingImpl->setPollingConfig(pollingConfig);
   NetworkInitializeEvent evt;
   for (HandlersSet::iterator i = mInternalHandlers.begin(); i != mInternalHandlers.end(); i++)
   {
      NetworkChangeHandlerInternal* handler = (*i);
      if (handler)
      {
         handler->onNetworkInitializeEvent(evt);
      }
   }
   mStarted = true;
   return kSuccess;
}

int NetworkChangeManager_Mock::stop()
{
   DebugLog(<< "NetworkChangeManager_Mock::stop()");
   mShutdown = true;

   if (mPollingImpl)
   {
      NetworkShutdownEvent evt;
      for (HandlersSet::iterator i = mInternalHandlers.begin(); i != mInternalHandlers.end(); i++)
      {
         NetworkChangeHandlerInternal* handler = (*i);
         if (handler)
         {
            handler->onNetworkShutdownEvent(evt);
         }
      }
      mPollingImpl.reset();
      DebugLog(<< "NetworkChangeManager_Mock::stop(): network polling shutdown completed");
   }
   return kSuccess;
}

NetworkTransport NetworkChangeManager_Mock::networkTransport() const
{
   StackLog(<< "NetworkChangeManager_Mock::networkTransport(): current transport: " << mMockTransport);
   return mMockTransport;
}

int NetworkChangeManager_Mock::addObserver(NetworkChangeHandlerInternal* observer)
{
   StackLog(<< "NetworkChangeManager_Mock::addObserver(): current transport: " << mMockTransport);
   mInternalHandlers.insert(observer);
   return kSuccess;
}

int NetworkChangeManager_Mock::removeObserver(NetworkChangeHandlerInternal* observer)
{
   StackLog(<< "NetworkChangeManager_Mock::removeObserver(): current transport: " << mMockTransport);
   mInternalHandlers.erase(observer);
   return kSuccess;
}

int NetworkChangeManager_Mock::addPollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   StackLog(<< "NetworkChangeManager_Mock::addPollingHandler(): current transport: " << mMockTransport);
   if (handler)
   {
      if (mPollingImpl)
      {
         DebugLog(<< "NetworkChangeManager_Mock::addPollingHandler(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
         mPollingImpl->addPollingHandler(handler);
      }
   }
   return kSuccess;
}

int NetworkChangeManager_Mock::removePollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   StackLog(<< "NetworkChangeManager_Mock::removePollingHandler(): current transport: " << mMockTransport);
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Mock::removePollingHandler(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
      mPollingImpl->removePollingHandler(handler);
   }
   return kSuccess;
}

int NetworkChangeManager_Mock::setPollingConfig(const NetworkChangePollingConfig& config)
{
   StackLog(<< "NetworkChangeManager_Mock::setPollingConfig(): current transport: " << mMockTransport);
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Mock::setPollingConfig(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
      mPollingImpl->setPollingConfig(config);
   }
   return kSuccess;
}

void NetworkChangeManager_Mock::setNetworkTransport(NetworkTransport t)
{
   DebugLog(<< "NetworkChangeManager_Mock::setNetworkTransport(): update current transport: " << mMockTransport << " to: " << t);
   mMockTransport = t;
}

// NetworkChangePollingHandler
void NetworkChangeManager_Mock::onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt)
{
   resip::Lock lck(mMockInterfacesMutex);
   evt.transport = mMockTransport;
   for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator i = mMockInterfaces.begin(); i != mMockInterfaces.end(); i++)
      evt.interfaces.insert((*i).c_str());
}

void NetworkChangeManager_Mock::onPublishNetworkChange(const NetworkChangePublishEvent& evt)
{
   NetworkChangeEvent networkEvt;
   networkEvt.networkTransport = evt.transport;
   if (mPrevMockInterfaces.size() > 0)
   {
      compareInterfaceLists(mPrevMockInterfaces, mMockInterfaces, networkEvt.interfacesUp, networkEvt.interfacesDown);
   }
   sendNetworkChangeEvent(networkEvt);
}

void NetworkChangeManager_Mock::setMockInterfaces(const std::set<resip::Data>& mockInterfaces, std::set<resip::Data>* prevMockInterfaces)
{
   DebugLog(<< "NetworkChangeManager_Mock::setMockInterfaces()");
   resip::Lock lck(mMockInterfacesMutex);
   mMockInterfaces = mockInterfaces;
   if (NULL != prevMockInterfaces) mPrevMockInterfaces = *prevMockInterfaces;
}

NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet NetworkChangeManager_Mock::getLocalIPAddresses_NonStatic(bool includeLoopback)
{
   NetworkChangeManagerInterfaceSet result;

   int i = 0;
   for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator it = mMockInterfaces.begin(); it != mMockInterfaces.end(); it++)
   {
      if (includeLoopback || ((!resip::isEqualNoCase(*it, "127.0.0.1") && !resip::isEqualNoCase(*it, "::1"))))
      {
         result.insert(*it);
      }
   }

   return result;
}

void NetworkChangeManager_Mock::sendNetworkChangeEvent(const NetworkChangeEvent& event)
{
   DebugLog(<< "NetworkChangeManager_Mock::sendNetworkChangeEvent(): transport: " << event.networkTransport);

   // for autotest hooking
   if (mSendNetworkChangeEventImminentHook)
   {
      mSendNetworkChangeEventImminentHook();
   }

   // mimick NetworkChangeManager_Polling invoking NetworkChangeManagerImpl::sendNetworkChangeEvent
   NetworkChangeManagerImpl::sendNetworkChangeEvent(event);
}

void NetworkChangeManager_Mock::notifyPrivateDnsChanged(bool secureDns)
{
   if (PhoneInterface::externalResolver() == ConnectionPreferences::ExternalResolverUsage_Enable)
   {
      if (secureDns != mLastDnsSecure)
      {
         mLastDnsSecure = secureDns;
         if (secureDns)
            DnsStub::enableDnsFeatures(ExternalDns::Features::UseExternalResolver);
         else
            DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      }
   }
}

void NetworkChangeManager_Mock::setSendNetworkChangeEventImminentHook(std::function<void()> fn)
{
   mSendNetworkChangeEventImminentHook = fn;
}

bool NetworkChangeManager_Mock::isSecureDnsActive()
{
   return mLastDnsSecure;
}

}

#endif
