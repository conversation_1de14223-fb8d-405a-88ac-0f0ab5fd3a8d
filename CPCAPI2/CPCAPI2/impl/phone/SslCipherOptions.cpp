
#include "phone/SslCipherOptions.h"
#include "../util/cpc_logger.h"
#include <openssl/ssl.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{

SslCipherOptions::SslCipherOptions():
   mHttpCiphers(CipherSuiteLegacy),
   mSipCiphers(CipherSuiteLegacy),
   mXmppCiphers(CipherSuiteLegacy),
   mWebSocketsCiphers(CipherSuiteAdvanced),
   mDtlsSrtpCiphers(CipherSuitePerformance),
   mHttpTLSVersion(TLS_HIGHEST),
   mSipTLSVersion(TLS_HIGHEST),
   mXmppTLSVersion(TLS_HIGHEST),
   mWebSocketsTLSVersion(TLS_NON_DEPRECATED),
   mDtlsSrtpTLSVersion(TLS_V1_2)
   {}

SslCipherOptions::~SslCipherOptions() { };

cpc::string SslCipherOptions::getCiphers(SslCipherUsage type) const
{
   switch (type)
   {
   case SslCipherUsageHttp:
      return mHttpCiphers;
   case SslCipherUsageSip:
      return mSipCiphers;
   case SslCipherUsageXmpp:
      return mXmppCiphers;
   case SslCipherUsageWebSockets:
      return mWebSocketsCiphers;
   case SslCipherUsageDtlsSrtp:
      return mDtlsSrtpCiphers;
   default:
      ErrLog(<< "Unsupported SslCipherUsage type " << type);
      return "";
   }
}

void SslCipherOptions::setCiphers(SslCipherUsage type, const cpc::string& ciphers)
{
   switch (type)
   {
   case SslCipherUsageHttp:
      mHttpCiphers = ciphers;
      break;
   case SslCipherUsageSip:
      mSipCiphers = ciphers;
      break;
   case SslCipherUsageXmpp:
      mXmppCiphers = ciphers;
      break;
   case SslCipherUsageWebSockets:
      mWebSocketsCiphers = ciphers;
      break;
   case SslCipherUsageDtlsSrtp:
      mDtlsSrtpCiphers = ciphers;
      break;
   default:
      ErrLog(<< "Unsupported SslCipherUsage type " << type);
   }
}

TLSVersion SslCipherOptions::getTLSVersion(SslCipherUsage type) const
{
   switch (type)
   {
   case SslCipherUsageHttp:
      return mHttpTLSVersion;
   case SslCipherUsageSip:
      return mSipTLSVersion;
   case SslCipherUsageXmpp:
      return mXmppTLSVersion;
   case SslCipherUsageWebSockets:
      return mWebSocketsTLSVersion;
   case SslCipherUsageDtlsSrtp:
      return mDtlsSrtpTLSVersion;
   default:
      ErrLog(<< "Unsupported SslCipherUsage type " << type);
      return TLS_HIGHEST;
   }
}

void SslCipherOptions::setTLSVersion(SslCipherUsage type, TLSVersion version)
{
   if (TLS_DEFAULT == version)
   {
      version = TLS_HIGHEST;
   }
   switch (type)
   {
   case SslCipherUsageHttp:
      mHttpTLSVersion = version;
      break;
   case SslCipherUsageSip:
      mSipTLSVersion = version;
      break;
   case SslCipherUsageXmpp:
      mXmppTLSVersion = version;
      break;
   case SslCipherUsageWebSockets:
      mWebSocketsTLSVersion = version;
      break;
   case SslCipherUsageDtlsSrtp:
      mDtlsSrtpTLSVersion = version;
      break;
   default:
      mDtlsSrtpTLSVersion = TLS_HIGHEST;
      ErrLog(<< "Unsupported SslCipherUsage type " << type);
   }
}

} //namespace CPCAPI2
