#include "PhoneInterface.h"
#include "PhoneModule.h"
#include "cpcapi2utils.h"
#include "NetworkChangeManager_Polling.h"
#include "EventQueue.h"
#include "brand_branded.h"
#include "../util/LibxmlSharedUsage.h"
#include "phone/PhoneInternal.h"
#include "phone/PhoneHandlerInternal.h"
#include "../util/DeviceInfo.h"
#include "../util/PlatformUtils.h"
#include "../util/MediaStackLog.h"
#include "util/FileUtils.h"
#include "../licensing/LicensingClientManagerInterface.h"
#include "../account/SipAccountInterface.h"
#include "../filetransfer/SipFileTransferManagerInterface.h"
#include "../media/MediaManagerInterface.h"
#include "../media/VideoInterface.h"
#include "../recording/RecordingManagerInterface.h"
#include "../xmpp/XmppFileTransferManagerInterface.h"
#include "../log/LocalLogger.h"
#include "../log/FileLog.h"
#include "BackgroundManager.h"
#include "MediaStackImpl.hxx"
  

#include "nlohmann/json.hpp"
#include "gen/Phone/datatypes/PhoneEvents.h"
#include "gen/Phone/datatypes/PhoneLogEvent.h"
#include "gen/Phone/datatypes/PhoneOnLicensingErrorEvent.h"
#include "gen/Phone/datatypes/PhoneOnLicensingSuccessEvent.h"
#include "gen/Phone/datatypes/LicensingErrorReason.h"


#include <webrtc/system_wrappers/interface/trace.h>
#include <webrtc/base/platform_thread.h>

#include <curl/curl.h>

#ifdef __APPLE__
#include "TargetConditionals.h"
#endif

#ifdef CPCAPI2_AUTO_TEST
#include "NetworkChangeManager_Mock.h"
#endif

#if _WIN32
// WINAPI_FAMILY_ONE_PARTITION isn't defined for WP8
#ifdef WP8
#include "NetworkChangeManager_Metro.h"
// WinRT specific
#elif WINAPI_FAMILY_ONE_PARTITION( WINAPI_FAMILY_DESKTOP_APP, WINAPI_PARTITION_APP )
#include "NetworkChangeManager_Metro.h"
#else
#include "NetworkChangeManager_Polling.h"
#endif
#elif TARGET_OS_IPHONE
#include "NetworkChangeManager_iOS.h"
#elif ANDROID
#include "NetworkChangeManager_Android.h"
#elif __THREADX
#include "NetworkChangeManager_ThreadX.h"
#elif BB10
#include "NetworkChangeManager_BB10.h"
#else
#include "NetworkChangeManager_Polling.h"
#endif

#ifdef ANDROID
#include "BluetoothManager_Android.h"
#else
#include "BluetoothManagerNoOp.h"
#endif

#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

#include <sstream>
#include <typeinfo>

#if defined(USE_SSL) && defined(__THREADX)
#include <rutil/ssl/OpenSSLInit.hxx>
#endif


#if defined(USE_SSL)
#if (CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECK != 1)
#include <openssl/opensslv.h>
#include <openssl/crypto.h>
#if OPENSSL_VERSION_NUMBER != 0x300000D0L // 3.0.13
#error Mismatchd OpenSSL headers!
#endif // CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECK
#endif // OPENSSL_VERSION_NUMBER
#endif // USE_SSL


#include <boost/version.hpp>
#if BOOST_VERSION != 108200 // 1.82.0
#error Mismatched Boost headers!
#endif // BOOST_VERSION


#include <openssl/err.h>

#if defined(ANDROID)
#include "JniHelper.h"
#endif

// OBELISK-4319: Ensure that the resip::ContentsFactoryBase::FactoryMap is populated with the required content
// types by including the associated header files. This implicitly triggers the static content init functions,
// thus populating the content factory map upon startup.
#include "resip/stack/SdpContents.hxx"
#include "resip/stack/PlainContents.hxx"
#include "resip/stack/OctetContents.hxx"
#include "resip/stack/SipFrag.hxx"
#include "resip/stack/MultipartMixedContents.hxx"
#include "resip/stack/MultipartRelatedContents.hxx"
#include "resip/stack/MessageWaitingContents.hxx"
#include "resip/stack/MultipartAlternativeContents.hxx"
#include "resip/stack/MultipartSignedContents.hxx"
#include "resip/stack/Pkcs7Contents.hxx"                 // Also includes Pkcs7SignedContents
#include "../regevent/RegInfoContents.h"                 // Not part of default resip stack, created in CPCAPI2
#include "../event/RLMIContents.h"                       // Not part of default resip stack, created in CPCAPI2
#include "resip/stack/Pidf.hxx"                          // Did not get included by default
// #include "resip/stack/Rlmi.hxx"                       // Did not get included by default
// #include "resip/stack/CpimContents.hxx"               // Did not get included by default
// #include "resip/stack/DtmfPayloadContents.hxx"        // Did not get included by default
// #include "resip/stack/InvalidContents.hxx"            // Did not get included by default
// #include "resip/stack/X509Contents.hxx"               // Did not get included by default
// #include "resip/stack/Pkcs8Contents.hxx"              // Did not get included by default

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

#define CPCAPI2_PRERELEASE_TIMER_ID 1

namespace CPCAPI2
{
std::vector<resip::MultiReactor*> PhoneInterface::sSdkThreadPool;
std::vector<resip::MultiReactor*> PhoneInterface::sLoggerThreadPool;
std::vector< std::shared_ptr< AsioHolder > > PhoneInterface::sAsioIOServicePool;
std::atomic_int PhoneInterface::sLoggerThreadPoolIdx(0);
std::atomic_bool PhoneInterface::sCurlInitiated(false);
std::mutex PhoneInterface::sLoggerThreadPoolMutex;

ConnectionPreferences::ExternalResolverUsage PhoneInterface::mExternalResolver = ConnectionPreferences::ExternalResolverUsage_Disable;

class AsioHolderImpl : public AsioHolder
{
public:
   AsioHolderImpl() : mAsioIoService()
   {
      mWork.reset(new boost::asio::io_service::work(mAsioIoService));
   }

   virtual ~AsioHolderImpl()
   {
      stopAsio();
   }

   void initAsio() override
   {
      // Pump the io service with a thread that calls run()
      mAsioServiceThread.reset(new std::thread([&]() { mAsioIoService.run(); }));

      mAsioIoService.post(std::bind(&AsioHolderImpl::onIoServiceThreadStart, this));
   }

   void stopAsio() override
   {
      if (!mAsioIoService.stopped())
      {
         mAsioIoService.post(std::bind(&AsioHolderImpl::onIoServiceThreadEnd, this));

         mWork.reset();
         mAsioIoService.stop();
         mAsioServiceThread->join();
      }
   }

   boost::asio::io_service& get() override
   {
      return mAsioIoService;
   }
private:
   void onIoServiceThreadStart()
   {
#ifdef ANDROID
      // attach so that ANR traces (adb pull /data/anr/traces.txt) show a call stack of this thread
      mJniThread.reset(new CPCAPI2::Jni::JniThread());
#endif
   }

   void onIoServiceThreadEnd()
   {
#ifdef ANDROID
      mJniThread.reset();
#endif
   }

private:
   boost::asio::io_service mAsioIoService;
   std::unique_ptr<boost::asio::io_service::work> mWork;
   std::unique_ptr<std::thread> mAsioServiceThread;
#ifdef ANDROID
   std::unique_ptr<CPCAPI2::Jni::JniThread> mJniThread;
#endif
};

const cpc::string& Phone::getVersion()
{
   static const cpc::string& version = CPCAPI2_BRAND_VERSION_NUMBER;
   return version;
}

Phone* Phone::create()
{
   PhoneInterface *result = new PhoneInterface;
   try
   {
      result->initializeAfterConstuction();
   }
   catch (std::exception&)
   {
      delete result;
      result = nullptr;
      throw;
   }
   DebugLog(<< "Phone::create(): phone-interface: " << result);
   return result;
}

PhoneInternal* PhoneInternal::create(int threadPoolThreadIdx)
{
   while (threadPoolThreadIdx >= PhoneInterface::sdkThreadPool().size())
   {
      std::ostringstream s;
      s << "SDK Pool " << PhoneInterface::sdkThreadPool().size();
      resip::MultiReactor* r = new resip::MultiReactor( resip::Data( s.str() ));
      r->start();
      PhoneInterface::sdkThreadPool().push_back(r);

      // Also create an equivalent number of asio io_services
      std::shared_ptr< AsioHolder > holder;
      holder.reset( new AsioHolderImpl );
      holder->initAsio();
      PhoneInterface::asioIOServicePool().push_back( holder );
   }

   PhoneInterface* result = new PhoneInterface(PhoneInterface::sdkThreadPool()[threadPoolThreadIdx]);
   try
   {
      result->initializeAfterConstuction();
   }
   catch (std::exception&)
   {
      delete result;
      result = nullptr;
      throw;
   }
   PhoneInterface::sdkThreadPool()[threadPoolThreadIdx]->addRef();
   result->mAsio = PhoneInterface::asioIOServicePool()[threadPoolThreadIdx];

   // Make sure that the reactor does not get destroyed when the phone instance is destroyed as it is part of the thread pool
   result->mOwnSdkModuleThread = false;

   DebugLog(<< "Phone::create(): phone-interface: " << result << " threadPoolThreadIdx: " << threadPoolThreadIdx);
   return result;
}

PhoneInternal* PhoneInternal::create(PhoneInternal* phone)
{
   PhoneInterface *masterPhoneInterface = dynamic_cast< PhoneInterface* >( phone );
   if( masterPhoneInterface == NULL )
      return NULL;

   PhoneInterface* result = new PhoneInterface( &masterPhoneInterface->getSdkModuleThread() );
   try
   {
      result->initializeAfterConstuction();
   }
   catch (std::exception&)
   {
      delete result;
      result = nullptr;
      throw;
   }
   result->mAsio = masterPhoneInterface->mAsio;

   DebugLog(<< "Phone::create(): phone-internal: " << phone << " phone-interface: " << result);
   return result;
}

PhoneInternal* PhoneInterface::create(PhoneInternal* phone, resip::MultiReactor* externalLogger)
{
   PhoneInterface *masterPhoneInterface = dynamic_cast< PhoneInterface* >( phone );
   if( masterPhoneInterface == NULL )
      return NULL;

   PhoneInterface* result = new PhoneInterface(&masterPhoneInterface->getSdkModuleThread(), externalLogger);
   try
   {
      result->initializeAfterConstuction();
   }
   catch (std::exception&)
   {
      delete result;
      result = nullptr;
      throw;
   }
   result->mAsio = masterPhoneInterface->mAsio;

   DebugLog(<< "PhoneInterface::create(): phone-internal: " << phone << " phone-interface: " << result << " external-logger: " << externalLogger);
   return result;
}

void Phone::release(Phone* phone)
{
   DebugLog(<< "Phone::release(): phone: " << phone);
   if (phone)
   {
      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(phone);

      resip::Mutex mutex;
      resip::Condition condition;
      mutex.lock();
      pi->getSdkModuleThread().post(resip::resip_bind(&PhoneInterface::setReleaseCondition, pi, &mutex, &condition));
      pi->getSdkModuleThread().post(resip::resip_bind(&PhoneInterface::appPreReleaseImpl, pi));
      condition.wait(mutex);
      mutex.unlock();
   }
}

void PhoneInterface::addRefImpl()
{
   mRefCnt++;
}

void PhoneInterface::appPreReleaseImpl()
{
   // Start the releasing process by giving all the modules a signal and starting a timer
   // after which we will check that everything is ready.

   std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
   std::vector<PhoneModuleInfo>::reverse_iterator it = ifacesCopy.rbegin();
   for (; it != ifacesCopy.rend(); ++it)
   {
      PhoneModule* pm = it->module;
      if (pm && pm != mBorrowedLicensingClientManager)
      {
         pm->PreRelease();
      }
   }

   // Check again in 500 ms
   mPreReleaseTimer.expires_from_now( 500 );
   mPreReleaseTimer.async_wait( this, CPCAPI2_PRERELEASE_TIMER_ID, NULL );
}

void PhoneInterface::appReleaseImpl()
{
   {
      mInterfacesMutex.lock();
      if (mIsReleasing)
      {
         // DebugLog(<< "App release already in progress, this: " << this);
         mInterfacesMutex.unlock();
         return; // Looks like release is already in progress
      }
      mIsReleasing = true;

      for (std::vector<std::function<void(void)>>::iterator i = mCallOnAppReleaseFn.begin(); i != mCallOnAppReleaseFn.end(); i++)
      {
         (*i)();
      }

      std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
      mInterfacesMutex.unlock();

      // ensure the modules calling releaseImpl can't cause 'this' to get deleted
      mRefCnt++;

      std::vector<PhoneModuleInfo>::reverse_iterator it = ifacesCopy.rbegin();
      for (; it != ifacesCopy.rend(); ++it)
      {
         PhoneModule* pm = it->module;
         if (pm && pm != mBorrowedLicensingClientManager)
         {
            //DebugLog(<< "About to release module " << typeid(*pm).name() << " " << pm << " this: " << this);
            pm->Release();
         }
      }

      //DebugLog(<< "Done releasing " << ifacesCopy.size() << " modules");

      mInterfacesMutex.lock();
      mInterfaces.clear();
      mInterfacesMutex.unlock();

      // this will execute any commands currently queued for execution on the SDK thread,
      // which will almost certainly include various SDK modules' invocation of PhoneInterface::releaseImpl(..)
      mSdkModuleThread->detach();

      DebugLog(<< "About to reset shared asio service pointer");
      if (mOwnSdkModuleThread)
      {
         mAsio.reset();
      }
      DebugLog(<< "Done reset of shared asio service pointer");

      mRefCnt--;
   }

   if (mRefCnt == 1) // check again, in case one of the modules needs to hang on to the Phone for a bit (releaseImpl() should be called later)
   {
      //DebugLog(<< "appReleaseImpl ref count is 0; continuing cleanup");

      delete mWebRtcLogger;
      mWebRtcLogger = NULL;

      BluetoothManagerImpl* btManager = mBluetoothManager;
      mBluetoothManager = NULL;
      if (btManager != NULL)
      {
         btManager->release();
      }

      delete mDefaultErrorHandler;
      mDefaultErrorHandler = NULL;

	   delete mDefaultPhoneHandler;
	   mDefaultPhoneHandler = NULL;

      delete mAddressTransformer;
      mAddressTransformer = NULL;

      if (mOwnSdkModuleThread)
      {
         mSdkModuleThread->stop();
         BackgroundManager::Instance()->removeResipReactor(mSdkModuleThread);
      }

      delete mFileLog;
      mFileLog = NULL;

      if (mOwnSdkLoggerThread)
      {
         resip::MultiReactor* loggerThread = mLoggerThread;
         mLoggerThread = NULL;
         if (loggerThread)
         {
            resip::Log::setLevel(resip::Log::None, 0);
            //DebugLog(<< "About to execute cleanupLogger");
            PhoneLogger* pl = mLogger;
            mLoggerDisposed.store(true);
            loggerThread->post(resip::resip_static_bind(&PhoneInterface::cleanupLogger, pl));
            loggerThread->stop();
            loggerThread->join();
            delete loggerThread;
         }
      }
      else
      {
         mLoggerThread = NULL;
      }

      resip::Mutex* mutex = mReleaseMutex;
      resip::Condition* condition = mReleaseCondition;

      if (mutex != NULL) mutex->lock();

      delete this;
      // !! WARNING !! no member access now!

      if (condition != NULL) condition->broadcast();
      if (mutex != NULL) mutex->unlock();

      return;
   }

   mRefCnt--;
}

void PhoneInterface::releaseImpl()
{
   {
      resip::Lock lock (mInterfacesMutex);
      if (mInterfaces.size() > 0 || mRefCnt > 1)
      {
         mRefCnt--; // appReleaseImpl() must happen first
         return;
      }
   }

   if (mRefCnt == 1) // check again, in case one of the modules needs to hang on to the Phone for a bit
   {
      delete mWebRtcLogger;
      mWebRtcLogger = NULL;

      BluetoothManagerImpl* btManager = mBluetoothManager;
      mBluetoothManager = NULL;
      if (btManager != NULL)
      {
         btManager->release();
      }

      removeSdkObserver(mDefaultErrorHandler);
      delete mDefaultErrorHandler;
      mDefaultErrorHandler = NULL;

      removeSdkObserver(mDefaultPhoneHandler);
      delete mDefaultPhoneHandler;
      mDefaultPhoneHandler = NULL;

      delete mAddressTransformer;
      mAddressTransformer = NULL;

      if (mOwnSdkModuleThread)
      {
         mSdkModuleThread->stop();
      }

      if (mOwnSdkLoggerThread)
      {
         resip::MultiReactor* loggerThread = mLoggerThread;
         mLoggerThread = NULL;
         if (loggerThread)
         {
            resip::Log::setLevel(resip::Log::None, 0);
            PhoneLogger* pl = mLogger;
            mLoggerDisposed.store(true);
            loggerThread->post(resip::resip_static_bind(&PhoneInterface::cleanupLogger, pl));
            loggerThread->stop();
            loggerThread->join();
            delete loggerThread;
         }
      }
      else
      {
         mLoggerThread = NULL;
      }

      if (mOwnSdkModuleThread)
      {
         mAsio.reset();
      }

      resip::Mutex* mutex = mReleaseMutex;
      resip::Condition* condition = mReleaseCondition;

      if (mutex != NULL) mutex->lock();

      delete this;
      // !! WARNING !! no member access now!

      if (condition != NULL) condition->broadcast();
      if (mutex != NULL) mutex->unlock();

      return;
   }
   mRefCnt--;
}

class DefaultErrorHandler : public EventSyncHandler<PhoneErrorHandler>
{
   virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
   {
      ErrLog(<< "An unhandled error occurred in " << sourceModule << ": " << args.errorText);
      return kSuccess;
   }

   virtual int onLicensingError(const LicensingErrorEvent& args)
   {
      ErrLog(<< "A licensing error occurred: " << args.errorText);
      return kSuccess;
   }
};

class DefaultPhoneHandler : public EventSyncHandler<PhoneHandler>
{
	virtual int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args)
	{
		ErrLog(<< "An unhandled error occurred in " << sourceModule << ": " << args.errorText);
		return kSuccess;
	}

	virtual int onLicensingError(const LicensingErrorEvent& args)
	{
		ErrLog(<< "A licensing error occurred: " << args.errorText);
		return kSuccess;
	}

	virtual int onLicensingSuccess()
	{
		return kSuccess;
	}
};

PhoneInterface::PhoneInterface() :
   mWebRtcLogger(NULL),
   mErrorHandler(NULL),
   mPhoneHandler(NULL),
   mDefaultErrorHandler(new DefaultErrorHandler),
   mDefaultPhoneHandler(new DefaultPhoneHandler),
   mLogger(NULL),
   mLoggerThread(NULL),
   mLoggerDisposed(false),
   mLocalLogger(NULL),
   mFileLog(NULL),
   mOwnSdkLoggerThread(true),
   mOwnSdkModuleThread(true),
   mAddressTransformer(new DefaultAddressTransformer),
   mRefCnt(0),
   mInitialized(false),
   mLicenseOK(true),
   mShutdown(false),
//    mRelease(false),
   mPermissionsHandler(NULL),
   mReleaseMutex(NULL),
   mReleaseCondition(NULL),
   mIsReleasing(false),
   mBluetoothManager(NULL),
   mPreReleaseTimer( *mSdkModuleThread ),
   mPreReleaseTimerFires(0),
   EventSource2< EventHandler<PhoneErrorHandler>, EventHandler<PhoneHandler> >( *((mSdkModuleThread = new resip::MultiReactor("PhoneInterface-Master"))), nullptr, std::make_shared<CPCAPI2::CallbackFifoHolder2>(this), false) // mSdkModuleThread initializes after base classes
{
   mEventQueue = CPCAPI2::EventQueue::create(*mSdkModuleThread);

   addSdkObserver(mDefaultPhoneHandler);
   addSdkObserver(mDefaultErrorHandler);

   // This version of the ctor needs to create its own asio holder and thread
   mAsio.reset( new AsioHolderImpl );
   mAsio->initAsio();

   mSdkModuleThread->addRef();
   addRefImpl();
   webrtc::Trace::set_level_filter(webrtc::kTraceNone);
   resip::Log::setLevel(resip::Log::None, 0);
   LibxmlSharedUsage::addRef();
   XmlSecSharedUsage::addRef();

#ifdef ANDROID
   mLogDirectory = "/sdcard/";
#elif TARGET_OS_IPHONE
   mLogDirectory = std::string("/Documents/") + getenv("HOME");
#endif

   DebugLog(<< "PhoneInterface::PhoneInterface(): " << this << " default");
#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)
   // OBELISK-3116 - disable App Nap on MacOS as it has shown to cause problems with incoming calls.
   // namely, the process containing the SDK can be starved of CPU time (for 5-10 seconds), causing
   // missed incoming calls, as SDK threads are not able to react in time
   PlatformUtils::PlatformUtils::disableMacOsAppNap();
#endif
}

PhoneInterface::PhoneInterface(resip::MultiReactor* reactor) :
   mWebRtcLogger(NULL),
   mErrorHandler(NULL),
   mPhoneHandler(NULL),
   mDefaultErrorHandler(new DefaultErrorHandler),
   mDefaultPhoneHandler(new DefaultPhoneHandler),
   mLogger(NULL),
   mLoggerThread(NULL),
   mLoggerDisposed(false),
   mLocalLogger(NULL),
   mFileLog(NULL),
   mSdkModuleThread(reactor),
   mOwnSdkLoggerThread(true),
   mOwnSdkModuleThread(false),
   mAddressTransformer(new DefaultAddressTransformer),
   mRefCnt(0),
   mInitialized(false),
   mLicenseOK(true),
   mShutdown(false),
//   mRelease(false),
   mPermissionsHandler(NULL),
   mReleaseMutex(NULL),
   mReleaseCondition(NULL),
   mIsReleasing(false),
   mBluetoothManager(NULL),
   mPreReleaseTimer( *mSdkModuleThread ),
   mPreReleaseTimerFires(0),
   EventSource2< EventHandler<PhoneErrorHandler>, EventHandler<PhoneHandler> >(*reactor, nullptr, std::make_shared<CPCAPI2::CallbackFifoHolder2>(this), false)
{
   mEventQueue = CPCAPI2::EventQueue::create(*mSdkModuleThread);

   addSdkObserver(mDefaultPhoneHandler);
   addSdkObserver(mDefaultErrorHandler);

   mSdkModuleThread->addRef();
   addRefImpl();

   // there is probably already another phone instance active,
   // so don't touch log levels, as they can be global.

   //webrtc::Trace::set_level_filter(webrtc::kTraceNone);
   //resip::Log::setLevel(resip::Log::None, 0);

   LibxmlSharedUsage::addRef();
   XmlSecSharedUsage::addRef();

#ifdef ANDROID
   mLogDirectory = "/sdcard/";
#elif TARGET_OS_IPHONE
   mLogDirectory = std::string("/Documents/") + getenv("HOME");
#endif

   DebugLog(<< "PhoneInterface::PhoneInterface(): " << this << " reactor");
}

PhoneInterface::PhoneInterface(resip::MultiReactor* reactor, resip::MultiReactor* externalLogger) :
   mWebRtcLogger(NULL),
   mErrorHandler(NULL),
   mPhoneHandler(NULL),
   mDefaultErrorHandler(new DefaultErrorHandler),
   mDefaultPhoneHandler(new DefaultPhoneHandler),
   mLogger(NULL),
   mLoggerThread(externalLogger),
   mLoggerDisposed(false),
   mLocalLogger(NULL),
   mFileLog(NULL),
   mSdkModuleThread(reactor),
   mOwnSdkLoggerThread(false),
   mOwnSdkModuleThread(false),
   mAddressTransformer(new DefaultAddressTransformer),
   mRefCnt(0),
   mInitialized(false),
   mLicenseOK(true),
   mShutdown(false),
   // mRelease(false),
   mPermissionsHandler(NULL),
   mReleaseMutex(NULL),
   mReleaseCondition(NULL),
   mIsReleasing(false),
   mBluetoothManager(NULL),
   mPreReleaseTimer( *mSdkModuleThread ),
   mPreReleaseTimerFires(0),
   EventSource2< EventHandler<PhoneErrorHandler>, EventHandler<PhoneHandler> >(*reactor, nullptr, std::make_shared<CPCAPI2::CallbackFifoHolder2>(this), false)
{
   mEventQueue = CPCAPI2::EventQueue::create(*mSdkModuleThread);

   addSdkObserver(mDefaultPhoneHandler);
   addSdkObserver(mDefaultErrorHandler);

   mSdkModuleThread->addRef();
   addRefImpl();

   // there is probably already another phone instance active,
   // so don't touch log levels, as they can be global.

   //webrtc::Trace::set_level_filter(webrtc::kTraceNone);
   //resip::Log::setLevel(resip::Log::None, 0);

   LibxmlSharedUsage::addRef();
   XmlSecSharedUsage::addRef();

#ifdef ANDROID
   mLogDirectory = "/sdcard/";
#elif TARGET_OS_IPHONE
   mLogDirectory = std::string("/Documents/") + getenv("HOME");
#endif

   DebugLog(<< "PhoneInterface::PhoneInterface(): " << this << " reactor with logger: " << externalLogger);
}


void PhoneInterface::initializeAfterConstuction()
{
#if defined(USE_SSL)
#if (CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECK != 1)
   if (SSLeay() != OPENSSL_VERSION_NUMBER)
   {
      // if you are hitting this, you may be hitting OBELISK-2718. To work around this issue, either curl or tsm needs to be rebuilt,
      // or the link order needs to be such that cpcapi2's OpenSSL is used instead of curl or tsm's OpenSSL.
      ErrLog(<< "OpenSSL runtime does not match headers from compilation, " << SSLeay() << " vs " << OPENSSL_VERSION_NUMBER);
      assert(0);
   }
#endif // CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECKS
#endif // USE_SSL

   if (!sCurlInitiated)
   {
      curl_global_init(CURL_GLOBAL_DEFAULT);
      sCurlInitiated = true;
   }

   if (mOwnSdkModuleThread)
   {
      mSdkModuleThread->start();
      BackgroundManager::Instance()->addResipReactor(mSdkModuleThread);
   }

   if (mOwnSdkLoggerThread && mWebRtcLogger == NULL)
   {
      mWebRtcLogger = new MediaStackLog();
      mWebRtcLogger->reset();
   }

   // Initialize NetworkChangeManagerInterface before modules which depend on it try to use it
   NetworkChangeManager::getInterface(this);
}

PhoneInterface::~PhoneInterface()
{
   DebugLog(<< "PhoneInterface::~PhoneInterface(): " << this);
   mShutdown = true;
   interruptProcess();
   mPreReleaseTimer.cancel();
   XmlSecSharedUsage::releaseRef();
   LibxmlSharedUsage::release(); // must be no more libxml use after this
   delete mLocalLogger;

   for (std::vector<std::function<void(void)>>::iterator i = mCallOnDestructFn.begin(); i != mCallOnDestructFn.end(); i++)
   {
      (*i)();
   }

   if (mSdkModuleThread)
   {
      mSdkModuleThread->releaseRef();
   }
}

int PhoneInterface::initialize(const LicenseInfo& licenseInfo, PhoneErrorHandler* errorHandler, bool useNetworkChangeManager)
{
	setErrorHandlerImpl(errorHandler);

   ConnectionPreferences::NetworkChangeManagerType netChgMgrType;
#ifdef CPCAPI2_AUTO_TEST
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_Mock;
#else
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
#endif

	initializeInternal(licenseInfo, NULL, useNetworkChangeManager, netChgMgrType);

	return kSuccess;
}

int PhoneInterface::initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, bool useNetworkChangeManager)
{
	setPhoneHandlerImpl(handler);

    ConnectionPreferences::NetworkChangeManagerType netChgMgrType;
 #ifdef CPCAPI2_AUTO_TEST
    netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_Mock;
 #else
    netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
 #endif

	initializeInternal(licenseInfo, NULL, useNetworkChangeManager, netChgMgrType);

	return kSuccess;
}

int PhoneInterface::initialize(CPCAPI2::Licensing::LicensingClientManager* licensingManager, PhoneHandler* handler, bool useNetworkChangeManager)
{
   setPhoneHandlerImpl(handler);

   ConnectionPreferences::NetworkChangeManagerType netChgMgrType;
#ifdef CPCAPI2_AUTO_TEST
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_Mock;
#else
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
#endif

   LicenseInfo licenseInfo;
   initializeInternal(licenseInfo, licensingManager, useNetworkChangeManager, netChgMgrType);

   return kSuccess;
}

void PhoneInterface::initializeInternal(const LicenseInfo& licenseInfo, CPCAPI2::Licensing::LicensingClientManager* licensingMgr, bool useNetworkChangeManager, const ConnectionPreferences::NetworkChangeManagerType& networkChangeManagerType, NetworkTransport transport)
{
   mInitialized = true;
   mLicenseInfo = licenseInfo;

   NetworkChangeManagerInterface* networkChangeIf = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(this));
   if (nullptr != networkChangeIf)
   {
     networkChangeIf->initImpl(useNetworkChangeManager, networkChangeManagerType, transport);
   }

#ifdef ANDROID
   mBluetoothManager = new BluetoothManager_Android(this);
#else
   mBluetoothManager = new BluetoothManagerNoOp(this);
#endif

   mBluetoothManager->start();

   DeviceInfo::getInstanceId(mInstanceId);
#if (CPCAPI2_BRAND_SDK_LICENSING == 1)
   bool needsCheck = true;
   Licensing::LicensingClientManagerInterface* licensingClientManager = NULL;
   if (licensingMgr != NULL)
   {
      licensingClientManager = dynamic_cast<Licensing::LicensingClientManagerInterface*>(licensingMgr);
      needsCheck = !licensingClientManager->checkValidateLicensesAttempted();
      DebugLog(<< "needsCheck " << (needsCheck ? "TRUE" : "FALSE"));
      mBorrowedLicensingClientManager = licensingClientManager;
      mSdkModuleThread->post(resip::resip_bind(&PhoneInterface::registerInterface, this, "LicensingClientManagerInterface", licensingClientManager));
   }
   else
   {
      DebugLog(<< "using existing interface ");
      licensingClientManager = dynamic_cast<Licensing::LicensingClientManagerInterface*>(Licensing::LicensingClientManager::getInterface(this));
   }
   if (needsCheck)
   {
      // start license validation
      assert(licensingClientManager != NULL);
      mSdkModuleThread->post(resip::resip_bind(&PhoneInterface::doLicensing, this, licensingClientManager));
   }
#else
   Licensing::LicensingClientManagerInterface* licensingClientManager = NULL;
   if (licensingMgr != NULL)
   {
      licensingClientManager = dynamic_cast<Licensing::LicensingClientManagerInterface*>(licensingMgr);
   }
   else
   {
      licensingClientManager = dynamic_cast<Licensing::LicensingClientManagerInterface*>(Licensing::LicensingClientManager::getInterface(this));
   }

   licensingClientManager->addSdkObserver(this);

#endif
}

int PhoneInterface::initialize(const char* licenseKey, const cpc::string& licenseDocumentLocation, const cpc::string& licenseAor)
{
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = licenseKey;
   licenseInfo.licenseDocumentLocation = licenseDocumentLocation;
   licenseInfo.licenseAor = licenseAor;

   return initialize(licenseInfo, (PhoneErrorHandler *) NULL);
}

int PhoneInterface::initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const SslCipherOptions& ciphers, bool useNetworkChangeManager)
{
   setPhoneHandlerImpl(handler);
   mSslCipherList = ciphers;

   ConnectionPreferences::NetworkChangeManagerType netChgMgrType;
#ifdef CPCAPI2_AUTO_TEST
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_Mock;
#else
   netChgMgrType = ConnectionPreferences::NetworkChangeManagerType_PlatformDefault;
#endif

   initializeInternal(licenseInfo, NULL, useNetworkChangeManager, netChgMgrType);

   return kSuccess;
}

int PhoneInterface::initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const ConnectionPreferences& cp, NetworkTransport transport)
{
   setPhoneHandlerImpl(handler);
   mSslCipherList = cp.ciphers;
   mExternalResolver = cp.externalResolver;
   initializeInternal(licenseInfo, NULL, cp.useNetworkChangeManager, cp.networkChangeManagerType, transport);

   return kSuccess;
}

static resip::Data logFile;

int PhoneInterface::setLoggingEnabled(const cpc::string& id, bool enabled)
{
   if (enabled)
   {
      bool canLog = false;
      if (logFile.empty())
      {
         canLog = true;
         resip::DataStream ds(logFile);
         ds << mLogDirectory << "CPCAPI2_" << id << "_" << this << ".log";
      }
      if (canLog)
      {
         resip::Log::setBuildNumber(CPCAPI2_BRAND_BUILD_STAMP);
         resip::Log::initialize(resip::Log::File, resip::Log::Debug, "CPCAPI2", logFile.c_str());

         const int maxFileSizeMb = 500;
         resip::Log::setMaxByteCount(maxFileSizeMb * 1000000);
      }
      if (mWebRtcLogger == NULL)
      {
         mWebRtcLogger = new MediaStackLog();
      }
      mWebRtcLogger->reset();
      mWebRtcLogger->init();
      resip::Log::setLevel(resip::Log::Debug, 0);
      webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDefault);

      logLibVersions();
   }
   else
   {
      logFile.clear();
      webrtc::Trace::set_level_filter(webrtc::kTraceNone);
      resip::Log::setLevel(resip::Log::None, 0);
   }
   return kSuccess;
}

int PhoneInterface::setLoggingEnabled2(PhoneLogger2* logger, bool enabled)
{
   mPhoneLogger2 = logger;
   setLoggingEnabled(static_cast<PhoneLogger*>(nullptr), enabled);
   return kSuccess;
}

int PhoneInterface::setLoggingEnabled(PhoneLogger* logger, bool enabled)
{
   if (!mOwnSdkLoggerThread)
   {
      // Ignore request as logger managed by another phone instance
      mLogger = logger;
      return kSuccess;
   }

   if (enabled)
   {
      if (mLoggerThread == NULL)
      {
         mLogger = logger;
         mLoggerThread = new resip::MultiReactor("CPCAPI2_LoggerThread");
         mLoggerThread->start();

         mLoggerThread->execute(resip::resip_bind(&PhoneInterface::startLogger, this));

         resip::Log::initialize(resip::Log::OnlyExternalNoHeaders, resip::Log::Stack, "CPCAPI2", NULL, this);
         resip::Log::setBuildNumber(CPCAPI2_BRAND_BUILD_STAMP);
         if (mWebRtcLogger == NULL)
         {
            mWebRtcLogger = new MediaStackLog();
         }
         mWebRtcLogger->reset();
         mWebRtcLogger->init();
         resip::Log::setLevel(resip::Log::Stack, 0);
         webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDefault);
      }
   }
   else
   {
      if (mOwnSdkLoggerThread)
      {
         resip::MultiReactor* loggerThread = mLoggerThread;
         mLoggerThread = NULL;
         if (loggerThread)
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceNone);
            resip::Log::setLevel(resip::Log::None, 0);
            //DebugLog(<< "About to execute cleanupLogger");
            PhoneLogger* pl = mLogger;
            mLoggerDisposed.store(true);
            loggerThread->post(resip::resip_static_bind(&PhoneInterface::cleanupLogger, pl));
            loggerThread->stop();
            loggerThread->join();
            delete loggerThread;
         }
      }
      else
      {
         mLoggerThread = NULL;
      }
   }

   return kSuccess;
}

int PhoneInterface::setLogDirectory(const cpc::string& dir)
{
  mLogDirectory = dir;
  FileUtils::CreateDir((mLogDirectory).c_str(), true);
  webrtc_recon::MediaStackImpl::setApmDumpDirectory(dir.c_str());
  return kSuccess;
}

int PhoneInterface::setLogLevel(CPCAPI2::LogLevel level)
{

   switch (level)
   {
      case LogLevel_None:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceNone);
            resip::Log::setLevel(resip::Log::None, 0);
         }
         break;
      case LogLevel_Error:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceError | webrtc::kTraceCritical);
            resip::Log::setLevel(resip::Log::Err, 0);
         }
         break;
      case LogLevel_Warning:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceWarning | webrtc::kTraceError | webrtc::kTraceCritical);
            resip::Log::setLevel(resip::Log::Warning, 0);
         }
         break;
      case LogLevel_Info:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDefault);
            resip::Log::setLevel(resip::Log::Info, 0);
         }
         break;
      case LogLevel_Debug:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDebug | webrtc::kTraceDefault);
            resip::Log::setLevel(resip::Log::Debug, 0);
         }
         break;
      case LogLevel_Max:
         {
            webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDebug | webrtc::kTraceDefault | webrtc::kTraceMemory);
            resip::Log::setLevel(resip::Log::Stack, 0);
         }
         break;
      default:
         {
            ErrLog(<< "Set log level failed");
            return kError;
         }
   }
   InfoLog(<< "Log level set to: " << level);
   return kSuccess;
}

int PhoneInterface::setLocalFileLoggingEnabled(const cpc::string& id, bool enabled)
{
   if (enabled)
   {
      {
         std::lock_guard<std::mutex> lck(PhoneInterface::sLoggerThreadPoolMutex);

         // Make sure that the logger does not get destroyed when the phone instance is destroyed as it is part of the thread pool
         mOwnSdkLoggerThread = false;

         size_t loggerThreadIdx = (sLoggerThreadPoolIdx++ % std::thread::hardware_concurrency());
         while (loggerThreadIdx >= PhoneInterface::loggerThreadPool().size())
         {
            std::ostringstream s;
            s << "CPCAPI2 Logger " << PhoneInterface::loggerThreadPool().size();
            resip::MultiReactor* r = new resip::MultiReactor(resip::Data(s.str()));
            r->start();
            PhoneInterface::loggerThreadPool().push_back(r);
         }

         mLoggerThread = PhoneInterface::loggerThreadPool()[loggerThreadIdx];
      }

      if (mFileLog == NULL)
      {
         std::stringstream ss;
         ss << id.c_str();

         mFileLog = new FileLog(ss.str(), mLogDirectory);
         if (mLocalLogger == NULL)
         {
            mLocalLogger = new LocalLogger();
         }
         mLocalLogger->initialize(mLoggerThread, mFileLog);
      }
      else
      {
         mLocalLogger->updateReactor( mLoggerThread );
      }

      mLocalLogger->setLogLevel(LogLevel_Debug);
      mLocalLogger->logDebugMessage("============================ Local file logging enabled for id {}. PhoneInterface: {} ============================",
                                    id.c_str(), static_cast<void*>(this));
   }
   else
   {
      mLocalLogger->logDebugMessage("Local file logging disabled for id {}", id.c_str());
      mLocalLogger->setLogLevel(LogLevel_None);
      mLocalLogger->updateReactor( NULL );
      // don't delete mLocalLogger because other modules may be referencing it
   }

   return kSuccess;
}

int PhoneInterface::setLocalFileLoggingLevel(CPCAPI2::LogLevel lvl)
{
   return kSuccess;
}

int PhoneInterface::setLocalCallbackLoggingEnabled(LocalLoggerHandler* handler, bool enabled)
{
   if (mFileLog)
   {
      return kError;
   }

   if (enabled)
   {
      if (mLoggerThread == NULL)
      {
         mLoggerThread = new resip::MultiReactor("CPCAPI2_LoggerThread");
         mLoggerThread->start();
      }

      if (mLocalLogger == NULL)
      {
         mLocalLogger = new LocalLogger();
      }

      mLocalLogger->setLogLevel(LogLevel_Debug);
   }

   return kSuccess;
}


void PhoneInterface::startLogger()
{
   // warning: executes on logger thread

   if (!mOwnSdkLoggerThread)
   {
      // Ignore request as logger managed by another phone instance
      return;
   }

#ifdef ANDROID
   DebugLog(<< "Starting logger thread on thread id " << gettid());
#endif

   if (PhoneLoggerInternal* pli = dynamic_cast<PhoneLoggerInternal*>(mLogger))
   {
      pli->startLogger();
   }
}

void PhoneInterface::cleanupLogger(PhoneLogger* pl)
{
   // warning: executes on logger thread
   if (PhoneLoggerInternal* pli = dynamic_cast<PhoneLoggerInternal*>(pl))
   {
      pli->cleanupLogger();
   }
}

void PhoneInterface::externalLog(CPCAPI2::LogLevel level, const cpc::string& msg)
{
#pragma push_macro("RESIPROCATE_SUBSYSTEM")
#undef RESIPROCATE_SUBSYSTEM
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

   switch (level)
   {
   case LogLevel_None:
      break;
   case LogLevel_Error:
      ErrLog(<< msg);
      break;
   case LogLevel_Warning:
      WarningLog(<< msg);
      break;
   case LogLevel_Info:
      InfoLog(<< msg);
      break;
   case LogLevel_Max:
      StackLog(<< msg);
      break;
   case LogLevel_Debug:
   default:
      DebugLog(<< msg);
      break;
   }

#pragma pop_macro("RESIPROCATE_SUBSYSTEM")
}

void PhoneInterface::setErrorHandler(PhoneErrorHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&PhoneInterface::setErrorHandlerImpl, this, handler);
   
   if (handler == NULL)
   {
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
}

void PhoneInterface::setErrorHandlerImpl(PhoneErrorHandler* handler)
{
   if (nullptr != mErrorHandler)
   {
      removeAppHandler(mErrorHandler);
   }
   mErrorHandler = handler;

   if (nullptr != handler)
   {
      addAppHandler(handler);
   }
}

PhoneErrorHandler* PhoneInterface::getErrorHandler()
{
   return mErrorHandler ? mErrorHandler : mDefaultErrorHandler;
}

void PhoneInterface::setPhoneHandler(PhoneHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&PhoneInterface::setPhoneHandlerImpl, this, handler);
   
   if (handler == NULL)
   {
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }
}

void PhoneInterface::setPhoneHandlerImpl(PhoneHandler* handler)
{
   if (nullptr != mPhoneHandler)
   {
      removeAppHandler(mPhoneHandler);
   }
   mPhoneHandler = handler;

   if (nullptr != handler)
   {
      addAppHandler(handler);
   }
}

void PhoneInterface::registerInterface(const cpc::string& name, PhoneModule* iface)
{
   StackLog(<< "PhoneInterface::registerInterface: " << this << " module " << name << " (" << iface<< ") thread " << std::this_thread::get_id());

   if (!mSdkModuleThread->isCurrentThread())
      WarningLog(<< "PhoneInterface::registerInterface: current thread is not sdk, module " << name << " thread " << std::this_thread::get_id());

   if (mIsReleasing)
   {
      // ignore attempts to register modules during the shutdown/release phase;
      assert(0);
      return;
   }

   iface->isLicenseOK() = mLicenseOK;

   {
      mInterfacesMutex.lock();
      std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
      mInterfacesMutex.unlock();

      for (std::vector<PhoneModuleInfo>::iterator it = ifacesCopy.begin(), end = ifacesCopy.end(); it != end; ++it)
      {
         if (it->name == name.c_str())
         {
            WarningLog(<< (iface == it->module ? "Duplicate registerInterface() for module: " : "Duplicate module: ") << name);
         }
      }

      {
         resip::Lock lock (mInterfacesMutex);
         mInterfaces.push_back(PhoneModuleInfo(name, iface));
      }
   }
}

PhoneModule* PhoneInterface::getInterfaceByName(const cpc::string& name)
{
   mInterfacesMutex.lock();
   std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
   mInterfacesMutex.unlock();

   std::vector<PhoneModuleInfo>::iterator it = ifacesCopy.begin();
   for (; it != ifacesCopy.end(); it++)
   {
      if (it->name.compare(name) == 0)
         return it->module;
   }
   return NULL;
}

void PhoneInterface::getAllInterfaces(std::vector<PhoneModule*>& ifaces)
{
   mInterfacesMutex.lock();
   std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
   mInterfacesMutex.unlock();

   std::vector<PhoneModuleInfo>::iterator it = ifacesCopy.begin();
   for (; it != ifacesCopy.end(); it++)
   {
      ifaces.push_back(it->module);
   }
}

bool PhoneInterface::operator()(resip::Log::Level level,
                                const resip::Subsystem& subsystem,
                                const resip::Data& appName,
                                const char* file,
                                int line,
                                const resip::Data& message,
                                const resip::Data& messageWithHeaders)
{
   if (mLoggerDisposed)
      return true;

   resip::MultiReactor* loggerThread = mLoggerThread;
   if (NULL != loggerThread)
   {
      loggerThread->post(resip::resip_bind(&PhoneInterface::logImpl, this, level, subsystem.getSubsystem(), appName, file, line, message, messageWithHeaders, std::this_thread::get_id()));
   }
   return true;
}

void PhoneInterface::prependMessage(resip::oDataStream& messageStream)
{
   messageStream << CPCAPI2_BRAND_BUILD_STAMP << " t" << rtc::CurrentThreadId() << " ";
}

void PhoneInterface::logImpl(resip::Log::Level level,
                             const resip::Data& subsystem,
                             const resip::Data& appName,
                             const resip::Data& file,
                             int line,
                             const resip::Data& message,
                             const resip::Data& messageWithHeaders,
                             const std::thread::id& logThreadSourceId)
{
   //possible: someone is trying to do loggin while shutting down
   if (mLoggerDisposed)
   {
      mLogger = NULL;
   }

   CPCAPI2::LogLevel l = LogLevel_None;
   jsonrpc::CPCAPI2::Phone::LogLevel jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::None;

   switch (level)
   {
      case resip::Log::None:
         l = LogLevel_None;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::None;
         break;
      case resip::Log::Crit:
      case resip::Log::Err:
         l = LogLevel_Error;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::Error;
         break;
      case resip::Log::Warning:
         l = LogLevel_Warning;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::Warning;
         break;
      case resip::Log::Info:
         l = LogLevel_Info;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::Info;
         break;
      case resip::Log::Debug:
         l = LogLevel_Debug;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::Debug;
         break;
      case resip::Log::Stack:
         l = LogLevel_Max;
         jrpcLogLevel = jsonrpc::CPCAPI2::Phone::LogLevel::Max;
         break;
      default:
         break;
   }


   // Fix the file so it doesn't contain any paths
   const char *pfile = file.c_str();
   const char *pos   = pfile + strlen( pfile );

#if defined( WIN32 )
   while( pos != pfile && *pos != '\\' )
      --pos;
#else // #if defined( WIN32 ) || defined( __APPLE__ )
   while( pos != pfile && *pos != '/' )
      --pos;
#endif

   if( pos != pfile )
      ++pos;

   if (mLogger)
   {
      (*mLogger)(l, subsystem.c_str(), appName.c_str(), pos, line,
         message.c_str(),
         messageWithHeaders.c_str());
   }

   if (mPhoneLogger2)
   {
      PhoneLogEvent2 logEvent;
      logEvent.level = l;
      logEvent.subsystem = subsystem.c_str();
      logEvent.file = pos;
      logEvent.line = line;
      logEvent.message = message.c_str();

      // is there a better way to convert?
      std::stringstream ss;
      ss << logThreadSourceId;
      std::string tid = ss.str();
      logEvent.sourceThreadId = tid.c_str();

      (*mPhoneLogger2)(logEvent);
   }

   jsonrpc::CPCAPI2::Phone::PhoneLogEvent jrpcEvt;
   jrpcEvt.level = jrpcLogLevel;
   jrpcEvt.subsystem = subsystem.c_str();
   jrpcEvt.appName = appName.c_str();
   jrpcEvt.file = file.c_str();
   jrpcEvt.line = line;
   jrpcEvt.message = message.c_str();
   jrpcEvt.messageWithHeaders = messageWithHeaders.c_str();
   mEventQueue->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotLog), std::move(jrpcEvt.marshal())));
}

void PhoneInterface::setAddressTransformer(AddressTransformer* transformer)
{
   delete mAddressTransformer;
   mAddressTransformer = transformer;
}

AddressTransformer* PhoneInterface::getAddressTransformer()
{
   return mAddressTransformer;
}

void PhoneInterface::doLicensing(CPCAPI2::Licensing::LicensingClientManagerInterface* licensingClientManager)
{
#if (CPCAPI2_BRAND_SDK_LICENSING == 0) && (CPCAPI2_BRAND_LICENSING_MODULE == 0)
   // intentional compile error
   Bria builds MUST define CPCAPI2_BRAND_LICENSING_MODULE, otherwise the app has no licensing!!!
#endif
#if (CPCAPI2_BRAND_SDK_LICENSING == 1)

#if (CPCAPI2_BRAND_LICENSE_SKIP_LOCAL_CHECK == 1)
   #if (CPCAPI2_BRAND_USE_LICENSE_SERVER == 1 || CPCAPI2_BRAND_LICENSE_EXPIRY_YEAR != 0 || CPCAPI2_BRAND_LICENSE_EXPIRY_MONTH != 0 || CPCAPI2_BRAND_LICENSE_EXPIRY_DAY != 0)
      // intentional compile error
      Unsupported branding configuration
   #else
      // Intended only for our own server side binaries, e.g. cpcapi2conf
      DebugLog(<< "Skipping license check");
   #endif
#else
   if (!isReleasing()) // otherwise licensingClientManager might have been deleted
   {
      // Create a client for license checking
      Licensing::LicensingClientHandle licensingClient = licensingClientManager->createImpl();
      licensingClientManager->setHandlerImpl(licensingClient, this);

      // Configure the licenses (timebomb and/or license key) to validate
      Licensing::LicensingClientSettings licensingSettings;
      licensingClientManager->setBrandedExpiry(licensingSettings);

      licensingSettings.licenseDocumentLocation = mLicenseInfo.licenseDocumentLocation;
      licensingSettings.provisioningId = mLicenseInfo.licenseAor;
      if (!mLicenseInfo.licenseKey.empty())
      {
         DebugLog(<< "doLicensing key " << mLicenseInfo.licenseKey);
         licensingSettings.licenseKeys.push_back(mLicenseInfo.licenseKey);
      }
      else
         DebugLog(<< "doLicensing NO key ");
      licensingSettings.domain = "";

      licensingClientManager->applySettingsImpl(licensingClient, licensingSettings);

      // Validate the licenses
      // A notification (success/failure) will be sent
      licensingClientManager->validateLicensesImpl(licensingClient);
   }
   else
      DebugLog(<< "doLicensing NOT checking");
#endif

#endif // #if (CPCAPI2_BRAND_SDK_LICENSING == 1)
}

int PhoneInterface::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   cpc::string licenseFailureMsg;
   bool licenseOk = true;

   // Check the license(s) validation result
   if (args.licenses.empty() && args.invalidLicenses.empty())
   {
      // No license key specified

      // License check succeeded
      licenseOk = true;
   }
   else if (!args.licenses.empty())
   {
      // License key specified is valid

      // Expect one valid license and no invalid one
      assert(args.licenses.size() == 1);
      assert(args.invalidLicenses.size() == 0);

      // License check succeeded
      licenseOk = true;
   }
   else
   {
      // License key specified is invalid
      licenseFailureMsg = "License key specified is invalid";
      // Expect no valid license and one invalid one
      assert(args.licenses.size() == 0);
      assert(args.invalidLicenses.size() == 1);

      // License check failed
      licenseOk = false;
   }

   InfoLog(<< "License valid: " << licenseOk);

   if (!licenseOk)
   {
      handleLicensingError(Licensing::LicenseStatus_Invalid, licenseFailureMsg);
   }
   else
   {
	   handleLicensingSuccess();
   }

   return kSuccess;
}

int PhoneInterface::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   cpc::string message = "License validation failure. Error code: " + cpc::to_string((long)args.errorCode) + ", status: " + cpc::to_string(args.status);
   handleLicensingError(args.status, message);
   return kSuccess;
}

int PhoneInterface::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   handleLicensingError(Licensing::LicenseStatus_Error, args.errorText);
   return kSuccess;
}

void PhoneInterface::handleLicensingError(Licensing::LicenseStatus status, const cpc::string& message)
{
   StackLog(<< "PhoneInterface::handleLicensingError(" << status << ")");
    
   std::stringstream appendedMessage;
   appendedMessage << message;
   std::string reasonText;

   mLicenseOK = false;

   // fire error event
   jsonrpc::CPCAPI2::Phone::PhoneOnLicensingErrorEvent jrpcEvt;
   LicensingErrorEvent evt;
   switch (status)
   {
   case Licensing::LicenseStatus_NoLicense:
   case Licensing::LicenseStatus_Invalid:
      evt.errorReason = LicensingErrorEvent::LicensingErrorReason_Invalid;
      jrpcEvt.errorReason = jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonInvalid;
      reasonText = "No license or invalid license provided";
      break;
   case Licensing::LicenseStatus_Expired:
      evt.errorReason = LicensingErrorEvent::LicensingErrorReason_Expired;
      jrpcEvt.errorReason = jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonExpired;
      reasonText = "License expired";
      break;
   case Licensing::LicenseStatus_ServerReject:
      evt.errorReason = LicensingErrorEvent::LicensingErrorReason_Rejected;
      jrpcEvt.errorReason = jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonRejected;
      reasonText = "License rejected by server";
      break;
   case Licensing::LicenseStatus_ServerAuthFailed:
   case Licensing::LicenseStatus_ServerBadData:
   case Licensing::LicenseStatus_ServerEmbeddedCert:
   case Licensing::LicenseStatus_ServerUnreachable:
      evt.errorReason = LicensingErrorEvent::LicensingErrorReason_ServerError;
      jrpcEvt.errorReason = jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonServerError;
      reasonText = "License server unreachable or bad response";
      break;
   default:
      evt.errorReason = LicensingErrorEvent::LicensingErrorReason_Unknown;
      jrpcEvt.errorReason = jsonrpc::CPCAPI2::Phone::LicensingErrorReason::LicensingErrorReasonUnknown;
      reasonText = "Unknown";
      break;
   }

   appendedMessage << ". Reason string: " << reasonText;
   evt.errorText = appendedMessage.str().c_str();
   jrpcEvt.errorText = evt.errorText;
   ErrLog(<< "CPCAPI2 licensing failure: " << message << " with reason " << reasonText);

   fireEvent(cpcEvent(PhoneErrorHandler, onLicensingError), evt);
   fireEvent(cpcEvent(PhoneHandler, onLicensingError), evt);

   mEventQueue->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingError), std::move(jrpcEvt.marshal())));

   // notify phone modules
   {
      mInterfacesMutex.lock();
      std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
      mInterfacesMutex.unlock();
      std::vector<PhoneModuleInfo>::iterator it = ifacesCopy.begin();
      for (; it != ifacesCopy.end(); it++)
      {
         PhoneModuleInfo itf = *it;
         itf.module->isLicenseOK() = false;
         itf.module->onLicensingError();
      }
   }
}

void PhoneInterface::handleLicensingSuccess()
{
   StackLog(<< "PhoneInterface::handleLicensingSuccess()");

   // SCORE-1796 We need to update the flag otherwise subsequent attempts to register will
   // fail due to licensing even though licensing is now good.
   mLicenseOK = true;

   // notify phone modules
   {
      mInterfacesMutex.lock();
      std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
      mInterfacesMutex.unlock();
      std::vector<PhoneModuleInfo>::iterator it = ifacesCopy.begin();
      for (; it != ifacesCopy.end(); it++)
      {
         PhoneModuleInfo itf = *it;
         itf.module->isLicenseOK() = true;
//         itf.module->onLicensingSuccess(); is there someone we should call?
      }
   }

   fireEvent(cpcEvent(PhoneHandler, onLicensingSuccess));

   jsonrpc::CPCAPI2::Phone::PhoneOnLicensingSuccessEvent jrpcEvt;
   mEventQueue->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnLicensingSuccess), std::move(jrpcEvt.marshal())));
}

int PhoneInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kPhoneDisabled;
   }

   const UInt64 end(resip::Timer::getTimeMs() + timeout);

   EventSourceBase::process(timeout);

   mInterfacesMutex.lock();
   std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
   mInterfacesMutex.unlock();

   for (auto it = ifacesCopy.begin(); it != ifacesCopy.end() && end > resip::Timer::getTimeMs(); ++it)
   {
      EventSourceBase* pm = dynamic_cast<EventSourceBase*>(it->module);
      if (pm)
      {
         pm->process(timeout);
      }

      if (mShutdown)
      {
        return kPhoneDisabled;
      }
   }

   return kSuccess;
}

std::shared_ptr<CPCAPI2::EventQueue> PhoneInterface::getEventQueue() const
{
   return mEventQueue;
}

int PhoneInterface::createEventQueue(const cpc::vector<cpc::string>& events)
{
   if (mEventQueue)
   {
      std::vector<std::string> evts;
      for (cpc::string str : events)
      {
         evts.push_back(str.c_str());
      }
      return mEventQueue->createEventQueue(evts);
   }

   return kError;
}

void PhoneInterface::destroyEventQueue(int queueId)
{
   if (mEventQueue)
   {
      mEventQueue->destroyEventQueue(queueId);
   }
}

SdkEvent PhoneInterface::getEvent(int queueId, int timeout)
{
   if (mEventQueue)
   {
      return mEventQueue->getEvent(queueId, timeout);
   }

   return SdkEvent();
}

void PhoneInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void PhoneInterface::invokeCallbackHook()
{
   if (mCbHook) { mCbHook(); }
}

void PhoneInterface::execFunction(void (*func)(void))
{
   (*func)();
}

void PhoneInterface::execFunction2(void(*func)(void*), void* context)
{
   (*func)(context);
}

void PhoneInterface::setCallOnDestructFn(void (*func)(void*), void* context)
{
   mCallOnDestructFn.push_back(std::bind(func, context));
}

void PhoneInterface::setCallOnAppReleaseFn(void (*func)(void*), void* context)
{
   mCallOnAppReleaseFn.push_back(std::bind(func, context));
}

void PhoneInterface::setPhoneName(const cpc::string& name)
{
   mPhoneName = name;
}

void PhoneInterface::checkSdkThreadOpenSslErrors(unsigned long& outLastOpenSslError)
{
   mSdkModuleThread->execute(resip_bind(&PhoneInterface::checkSdkThreadOpenSslErrorsImpl, this, &outLastOpenSslError));
}

void PhoneInterface::checkSdkThreadOpenSslErrorsImpl(unsigned long* outLastOpenSslError)
{
   *outLastOpenSslError = ERR_peek_last_error();
}

void PhoneInterface::runOnSdkModuleThread(void (*func)(void))
{
   mSdkModuleThread->post(resip_bind(&PhoneInterface::execFunction, this, func));
}

void PhoneInterface::runOnSdkModuleThread(void(*funcToRun)(void*), void* context)
{
   mSdkModuleThread->post(resip_bind(&PhoneInterface::execFunction2, this, funcToRun, context));
}

void PhoneInterface::blockUntilRanOnSdkModuleThread(void(*funcToRun)(void*), void* context)
{
   mSdkModuleThread->execute(resip_bind(&PhoneInterface::execFunction2, this, funcToRun, context));
}

void PhoneInterface::runOnAsioThread(void(*funcToRun)(void*), void* context)
{
   if (mAsio)
   {
      mAsio->get().post( std::bind( &PhoneInterface::execFunction2, this, funcToRun, context ));
   }
}

void PhoneInterface::setPermissionsHandler(PermissionsHandler* handler)
{
   mPermissionsHandler = handler;
}

void PhoneInterface::onRequestPermissionsResult(int requestCode, std::vector<CPCAPI2::Permission> permissions, std::vector<bool> result)
{
   mSdkModuleThread->post(resip::resip_bind(&PhoneInterface::onRequestPermissionsResultImpl, this, requestCode, permissions, result));
}

void PhoneInterface::onRequestPermissionsResultImpl(int requestCode, std::vector<CPCAPI2::Permission> permissions, std::vector<bool> result)
{
   for (unsigned int i = 0; i < permissions.size(); i++)
   {
      if (result[i])
      {
         switch (permissions[i])
         {
            case Permission_Microphone:
            {
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
               Media::MediaManagerInterface* mMediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(this));
               if (NULL !=  mMediaIf)
               {
                  mMediaIf->onPermissionGranted(requestCode, Permission_Microphone);
               }
#endif

               break;
            }
            case Permission_Camera:
            {
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
               Media::MediaManagerInterface* mMediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(this));
               if (NULL !=  mMediaIf)
               {
                 mMediaIf->onPermissionGranted(requestCode, Permission_Camera);
               }
#endif
               break;
            }
            case Permission_WriteFiles:
            {
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
              SipFileTransfer::SipFileTransferManagerInterface* mSipFtIf = dynamic_cast<SipFileTransfer::SipFileTransferManagerInterface*>(SipFileTransfer::SipFileTransferManagerInterface::getInterface(this));
              if (NULL != mSipFtIf)
              {
                 mSipFtIf->onPermissionGranted(requestCode, Permission_WriteFiles);
              }
#endif

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)
              XmppFileTransfer::XmppFileTransferManagerInterface* mXmppFtIf = dynamic_cast<XmppFileTransfer::XmppFileTransferManagerInterface*>(XmppFileTransfer::XmppFileTransferManager::getInterface(this));
              if (NULL != mXmppFtIf)
              {
                 mXmppFtIf->onPermissionGranted(requestCode, Permission_WriteFiles);
              }
#endif


#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
              Recording::RecordingManagerInterface* mRecIf = dynamic_cast<Recording::RecordingManagerInterface*>(Recording::RecordingManagerInterface::getInterface(this));
              if (NULL != mRecIf)
              {
                 mRecIf->onPermissionGranted(requestCode, Permission_WriteFiles);
              }
#endif
              // WriteFiles also grants ReadFiles so fall through
            }
            case Permission_ReadFiles:
            {
#if (CPCAPI2_BRAND_SIP_FILE_TRANSFER_MODULE == 1)
              SipFileTransfer::SipFileTransferManagerInterface* mSipFtIf = dynamic_cast<SipFileTransfer::SipFileTransferManagerInterface*>(SipFileTransfer::SipFileTransferManagerInterface::getInterface(this));
              if (NULL != mSipFtIf)
              {
                 mSipFtIf->onPermissionGranted(requestCode, Permission_ReadFiles);
              }
#endif

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)
              XmppFileTransfer::XmppFileTransferManagerInterface* mXmppFtIf = dynamic_cast<XmppFileTransfer::XmppFileTransferManagerInterface*>(XmppFileTransfer::XmppFileTransferManager::getInterface(this));
              if (NULL != mXmppFtIf)
              {
                 mXmppFtIf->onPermissionGranted(requestCode, Permission_ReadFiles);
              }
#endif

              Licensing::LicensingClientManagerInterface* mLicIf = dynamic_cast<Licensing::LicensingClientManagerInterface*>(Licensing::LicensingClientManagerInterface::getInterface(this));
              if (NULL != mLicIf)
              {
                 mLicIf->onPermissionGranted(requestCode, Permission_ReadFiles);
              }
              break;
            }
            default:
               break;
         }
      }
   }
}

void PhoneInterface::requestPermission(int requestCode, CPCAPI2::Permission permission)
{
   if (NULL != mPermissionsHandler)
   {
      std::vector<cpc::string> permissions;
#ifdef ANDROID
      std::string str = CPCAPI2::Jni::CallStaticStringMethod("com/counterpath/sdk/android/Utils", "getAndroidPermissionString", "(I)Ljava/lang/String;", permission);
      permissions.push_back(cpc::string(str.c_str()));
#endif

      postToSdkThread(resip::resip_bind(&PermissionsHandler::requestPermissions, mPermissionsHandler, requestCode, permissions));
   }
}

void PhoneInterface::requestPermissions(int requestCode, std::vector<cpc::string> permissions)
{
   if (NULL != mPermissionsHandler)
   {
      postToSdkThread(resip::resip_bind(&PermissionsHandler::requestPermissions, mPermissionsHandler, requestCode, permissions));
   }
}

bool PhoneInterface::hasPermission(CPCAPI2::Permission permission)
{
#ifdef ANDROID
   CPCAPI2::Jni::JniThread env;
   CPCAPI2::Jni::ScopedLocalRef<jobject> p = CPCAPI2::Jni::CallStaticObjectMethod("com/counterpath/sdk/android/Utils", "getAndroidPermissionString", "(I)Ljava/lang/String;", permission);
   return CPCAPI2::Jni::CallStaticBooleanMethod("com/counterpath/sdk/android/Utils", "hasPermission", "(Ljava/lang/String;)Z", *p);
#else
  return true;
#endif
}

bool PhoneInterface::hasFilePermission(CPCAPI2::Permission permission, cpc::string filePath)
{
#ifdef ANDROID
   // The permission check only applies to files.
   if (std::string::npos != std::string(filePath).find(":") && std::string::npos == std::string(filePath).find("file:"))
   {
      return true;
   }

   CPCAPI2::Jni::JniThread env;

   if (hasPermission(permission))
   {
     return true;
   }

   CPCAPI2::Jni::ScopedLocalRef<jstring> file = CPCAPI2::Jni::CreateJavaString(filePath.c_str());

   return CPCAPI2::Jni::CallStaticBooleanMethod("com/counterpath/sdk/android/Utils",
      (Permission_WriteFiles == permission) ? "canWriteFile" : "canReadFile",
      "(Ljava/lang/String;)Z", *file);
#else
  return hasPermission(permission);
#endif
}

void PhoneInterface::logLibVersions() const
{
   // request from VladP for extracting build stamp from binary -- string is in special format
   const std::string buildStamp = "CPCAPI2_build_stamp{" CPCAPI2_BRAND_BUILD_STAMP "}";

   PlatformUtils::CpuArchInfo cpuInfo;
   if (PlatformUtils::PlatformUtils::getCpuArchInfo(cpuInfo))
   {
      std::string info = buildStamp;
      if (!cpuInfo.buildTimeCpuArch.empty())
         info += ", build time arch: " + cpuInfo.buildTimeCpuArch;
      if (!cpuInfo.runTimeCpuInfo.empty())
         info += ", running on: " + cpuInfo.runTimeCpuInfo;
      InfoLog(<< info);
   }
   else
   {
      InfoLog(<< buildStamp);
   }

   InfoLog(<< OPENSSL_VERSION_TEXT);
   if (SSLeay() != OPENSSL_VERSION_NUMBER)
   {
      // if you are hitting this, you may be hitting OBELISK-2718. To work around this issue, either curl or tsm needs to be rebuilt,
      // or the link order needs to be such that cpcapi2's OpenSSL is used instead of curl or tsm's OpenSSL.
      ErrLog(<< "OpenSSL runtime does not match headers from compilation, " << SSLeay() << " vs " << OPENSSL_VERSION_NUMBER);
   }
}

void PhoneInterface::setReleaseCondition(resip::Mutex* mutex, resip::Condition* condition)
{
   mReleaseMutex = mutex;
   mReleaseCondition = condition;
}

void PhoneInterface::onTimer( unsigned short timerId, void* appState )
{
   if( timerId == CPCAPI2_PRERELEASE_TIMER_ID )
   {
      ++mPreReleaseTimerFires;

      std::vector<PhoneModuleInfo> ifacesCopy = mInterfaces;
      std::vector<PhoneModuleInfo>::reverse_iterator it = ifacesCopy.rbegin();
      bool preReleaseDone = true;
      for (; it != ifacesCopy.rend(); ++it)
      {
         PhoneModule* pm = it->module;
         if( pm && !pm->PreReleaseCompleted() )
         {
            preReleaseDone = false;
            break;
         }
      }

      if( !preReleaseDone && ( mPreReleaseTimerFires <= 12 ))
      {
         // If work is still happening, check again in 500 ms
         mPreReleaseTimer.expires_from_now( 500 );
         mPreReleaseTimer.async_wait( this, CPCAPI2_PRERELEASE_TIMER_ID, NULL );
      }
      else
      {
         // Drain the reactor of events (this basically just calls process)
         mSdkModuleThread->detach();

         // Drain the asio io_service
         if( mAsio )
         {
            // Post a handler to the asio io_service and wait for it to complete.
            // The goal is to push out previous items which are queued
            // in the io_service

            // This is frankly a little risky because we are blocking the reactor
            // thread in order to service the io_service thread. There may exist
            // a possibility of deadlock if some task in the io_service queue
            // blocks in order to do something in the reactor. In order to avoid
            // this possibility a timed wait will be used.

            std::mutex mutex;
            std::condition_variable cvar;

            // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
            struct MyFunctor
            {
               MyFunctor( std::mutex& mutex, std::condition_variable& cvar )
                  : doNothing( false ), mMutex( mutex ), mCVar( cvar ) {}

               void operator()( void )
               {
                  if( !doNothing )
                  {
                     // notify the calling thread
                     std::lock_guard< std::mutex > lock( mMutex );
                     mCVar.notify_all();
                  }
                  delete this; // if processed, functor deletes itself
               }

               std::atomic< bool > doNothing;
               std::mutex& mMutex;
               std::condition_variable& mCVar;
            };

            {
               // Block which needs to be synchronized
               std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

               MyFunctor *func = new MyFunctor( mutex, cvar );
               mAsio->get().post( std::bind( &MyFunctor::operator(), func ));

               // releases the mutex and waits on the condition (blocks caller thread)
               if( cvar.wait_for( lock, std::chrono::seconds( 5 )) == std::cv_status::timeout )
                  func->doNothing = true;

               lock.unlock(); // lock is reaquired, so .. release the associated mutex
            }
         }

         // Proceed to Release stage.
         appReleaseImpl();
      }
   }
}

std::ostream& operator<<(std::ostream& os, const PhoneErrorEvent& evt)
{
   return os << "PhoneErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const LicensingErrorEvent& evt)
{
   return os << "LicensingErrorEvent";
}

}
