#pragma once

#if !defined(CPCAPI2_PHONE_INTERFACE_H)
#define CPCAPI2_PHONE_INTERFACE_H

#include "cpcapi2defs.h"
#include "phone/Phone.h"
#include "../experimental/phone/PhoneInternal.h"
#include "phone/PhoneErrorHandler.h"
#include "phone/PhoneHandler.h"
#include "PhoneModule.h"
#include "Cpcapi2EventSource2.h"
#include "AddressTransformers.h"
#include "licensing/LicensingClientHandler.h"
#include "../licensing/LicensingClientSyncHandler.h"

#include <rutil/Logger.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/RecursiveMutex.hxx>
#include <rutil/DeadlineTimer.hxx>

#include "boost/asio/io_service.hpp"

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <vector>
#include <thread>

#ifdef ANDROID
#include <jni.h>
#endif


namespace CPCAPI2
{
class NetworkChangeManagerImpl;
class MediaStackLog;
class LocalLogger;
class FileLog;
class BluetoothManagerImpl;
class EventQueue;

namespace Licensing
{
class LicensingClientManagerInterface;
}
class AsioHolder
{
public:
   AsioHolder() {}
   virtual ~AsioHolder() {}
   virtual void initAsio() = 0;
   virtual void stopAsio() = 0;
   virtual boost::asio::io_service& get() = 0;
};

class PhoneInterface : public EventSource2< EventHandler<PhoneErrorHandler>, EventHandler<PhoneHandler> >,
                       public PhoneInternal,
                       public EventSourcePhone,
                       public resip::ExternalLogger,
                       public Licensing::LicensingClientHandler,
                       public Licensing::LicensingClientSyncHandler,
                       public resip::DeadlineTimerHandler
{
public:
   virtual ~PhoneInterface();

   // Declaring this create function here instead of PhoneInternal, to avoid using void* or exposing the multireactor type
   static PhoneInternal* create(PhoneInternal* phone, resip::MultiReactor* externalLogger);

   // PhoneErrorHandler is now deprecated. It is recommended to use the initialize method with PhoneHandler.
   DEPRECATED virtual int initialize(const LicenseInfo& licenseInfo, PhoneErrorHandler* errorHandler, bool useNetworkChangeManager = true) OVERRIDE;
   virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, bool useNetworkChangeManager = true) OVERRIDE;
   virtual int initialize(CPCAPI2::Licensing::LicensingClientManager* licensingManager, PhoneHandler* handler, bool useNetworkChangeManager) OVERRIDE;
   virtual int initialize(const char* licenseKey, const cpc::string& licenseDocumentLocation, const cpc::string& licenseAor = "") OVERRIDE;
   virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const SslCipherOptions& ciphers, bool useNetworkChangeManager = true) OVERRIDE;
   virtual int initialize(const LicenseInfo& licenseInfo, PhoneHandler* handler, const ConnectionPreferences& conPref, CPCAPI2::NetworkTransport transport = CPCAPI2::TransportWiFi) OVERRIDE;
   bool isInitialized() const { return mInitialized; };
   void addRefImpl();
   void releaseImpl();
   void appPreReleaseImpl();
   void appReleaseImpl();

   virtual int setLoggingEnabled(const cpc::string& id, bool enabled) OVERRIDE;
   virtual int setLoggingEnabled(PhoneLogger *logger, bool enabled) OVERRIDE;
   virtual int setLoggingEnabled2(PhoneLogger2* logger, bool enabled) OVERRIDE;
   virtual int setLogDirectory(const cpc::string& dir) OVERRIDE;
   virtual int setLogLevel(CPCAPI2::LogLevel level) OVERRIDE;

   virtual int setLocalFileLoggingEnabled(const cpc::string& id, bool enabled) OVERRIDE;
   virtual int setLocalFileLoggingLevel(CPCAPI2::LogLevel lvl) OVERRIDE;
   virtual int setLocalCallbackLoggingEnabled(LocalLoggerHandler* handler, bool enabled) OVERRIDE;
   virtual LocalLogger* localLogger() OVERRIDE { return mLocalLogger; }


   virtual void setErrorHandler(PhoneErrorHandler* handler) OVERRIDE;
   virtual PhoneErrorHandler* getErrorHandler() OVERRIDE;

   virtual void setPhoneHandler(PhoneHandler* handler);

   virtual void setAddressTransformer(AddressTransformer* transformer) OVERRIDE;
   virtual AddressTransformer* getAddressTransformer() OVERRIDE;

   virtual const cpc::string& getInstanceId() const OVERRIDE { return mInstanceId; }

   void registerInterface(const cpc::string& name, PhoneModule* iface);
   PhoneModule* getInterfaceByName(const cpc::string& name);
   void getAllInterfaces(std::vector<PhoneModule*>& ifaces);

   virtual resip::MultiReactor& getSdkModuleThread() const OVERRIDE { return *mSdkModuleThread; }
   resip::MultiReactor& getSdkLoggerThread() const { return *mLoggerThread; }
   boost::asio::io_service& getAsioIoService() {
      return mAsio->get();
   }
   std::shared_ptr<AsioHolder> getAsioHolder() {
      return mAsio;
   }
   BluetoothManagerImpl* getBluetoothManager() const { return mBluetoothManager; }
   std::shared_ptr<CPCAPI2::EventQueue> getEventQueue() const;
   virtual void runOnSdkModuleThread(void (*func)(void)) OVERRIDE;
   virtual void runOnSdkModuleThread(void(*funcToRun)(void*), void* context) OVERRIDE;
   virtual void blockUntilRanOnSdkModuleThread(void(*funcToRun)(void*), void* context) OVERRIDE;
   virtual void runOnAsioThread(void(*funcToRun)(void*), void* context) OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE
   {
     EventSource2::interruptProcess();
   }

   virtual int createEventQueue(const cpc::vector<cpc::string>& events) OVERRIDE;
   virtual void destroyEventQueue(int queueId) OVERRIDE;
   virtual SdkEvent getEvent(int queueId, int timeout) OVERRIDE;

   void handleLicensingError(CPCAPI2::Licensing::LicenseStatus status, const cpc::string& message);

   void handleLicensingSuccess();

   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
   virtual void invokeCallbackHook() OVERRIDE;

   // Permissions
   virtual void setPermissionsHandler(PermissionsHandler* handler) OVERRIDE;
   virtual void onRequestPermissionsResult(int requestCode, std::vector<CPCAPI2::Permission> permissions, std::vector<bool> result) OVERRIDE;
   void requestPermission(int requestCode, CPCAPI2::Permission permissions);
   void requestPermissions(int requestCode, std::vector<cpc::string> permissions);
   bool hasPermission(CPCAPI2::Permission permission);
   bool hasFilePermission(CPCAPI2::Permission permission, cpc::string filePath);

   static std::vector<resip::MultiReactor*>& sdkThreadPool() {
      return sSdkThreadPool;
   }

   static std::vector<resip::MultiReactor*>& loggerThreadPool() {
      return sLoggerThreadPool;
   }
   
   static std::vector< std::shared_ptr< AsioHolder > >& asioIOServicePool() {
      return sAsioIOServicePool;
   }

   void setReleaseCondition(resip::Mutex* mutex, resip::Condition* condition);
   bool isReleasing() const { return mIsReleasing; }
   bool isShutdown() const { return mShutdown; }

   SslCipherOptions getSslCipherOptions() { return mSslCipherList; }
   static ConnectionPreferences::ExternalResolverUsage externalResolver() { return mExternalResolver; }
   
   void logLibVersions() const;

private:
   PhoneInterface();
   PhoneInterface(resip::MultiReactor* reactor);
   PhoneInterface(resip::MultiReactor* reactor, resip::MultiReactor* externalLogger);
   friend Phone* Phone::create();

   void initializeAfterConstuction();

   void initializeInternal(const LicenseInfo& licenseInfo, CPCAPI2::Licensing::LicensingClientManager* licensingMgr, bool useNetworkChangeManager,
                           const ConnectionPreferences::NetworkChangeManagerType& networkChangeManagerType, CPCAPI2::NetworkTransport transport = NetworkTransport::TransportWiFi);

   virtual bool operator()(resip::Log::Level level,
                           const resip::Subsystem& subsystem,
                           const resip::Data& appName,
                           const char* file,
                           int line,
                           const resip::Data& message,
                           const resip::Data& messageWithHeaders) OVERRIDE;
                           
   void prependMessage(resip::oDataStream& messageStream) OVERRIDE;
                           
   void logImpl(resip::Log::Level level,
                           const resip::Data& subsystem,
                           const resip::Data& appName,
                           const resip::Data& file,
                           int line,
                           const resip::Data& message,
                           const resip::Data& messageWithHeaders,
                           const std::thread::id& logThreadSourceId);
   void startLogger();
   static void cleanupLogger(PhoneLogger* pl);

   void externalLog(CPCAPI2::LogLevel level, const cpc::string& msg) OVERRIDE;
   void doLicensing(CPCAPI2::Licensing::LicensingClientManagerInterface* licensingClientManager);

   virtual void setErrorHandlerImpl(PhoneErrorHandler* handler);
   virtual void setPhoneHandlerImpl(PhoneHandler* handler);

   // PhoneInternal
   void setCallOnDestructFn(void (*func)(void*), void* context) OVERRIDE;
   void setCallOnAppReleaseFn(void (*func)(void*), void* context) OVERRIDE;
   void setPhoneName(const cpc::string& name) OVERRIDE;
   void checkSdkThreadOpenSslErrors(unsigned long& outLastOpenSslError) OVERRIDE;
   void checkSdkThreadOpenSslErrorsImpl(unsigned long* outLastOpenSslError);

   void execFunction(void (*func)(void));
   void execFunction2(void(*func)(void*), void* context);
   
   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args) OVERRIDE;
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args) OVERRIDE;

   void onRequestPermissionsResultImpl(int requestCode, std::vector<CPCAPI2::Permission> permissions, std::vector<bool> result);

   // DeadlineTimerHandler
   virtual void onTimer( unsigned short timerId, void* appState ) OVERRIDE;

private:

   struct PhoneModuleInfo
   {
      std::string name;
      PhoneModule* module;
      PhoneModuleInfo(const char* moduleName, PhoneModule* moduleInst)
         : name( moduleName ), module( moduleInst ) {}
   };

   /**
    * An ordered list of phone modules which capture their order of instantiation.
    * The order is implicitly derived from the order in which GetInterfaceImpl is
    * invoked in the system. NB: this means that the order can change between runs,
    * however if module B depends on module A, it should be in the vector AFTER
    * module A.
    */
   std::vector< PhoneModuleInfo > mInterfaces;
   
   resip::RecursiveMutex mInterfacesMutex;
   MediaStackLog* mWebRtcLogger;
   PhoneHandler* mPhoneHandler;
   PhoneHandler* mDefaultPhoneHandler;
   PhoneErrorHandler* mErrorHandler;
   PhoneErrorHandler* mDefaultErrorHandler;
   PhoneLogger* mLogger;
   PhoneLogger2* mPhoneLogger2 = nullptr;
   resip::MultiReactor* mLoggerThread;
   std::atomic_bool mLoggerDisposed;
   std::string mLogDirectory;
   resip::MultiReactor* mSdkModuleThread;
   bool mOwnSdkLoggerThread;
   bool mOwnSdkModuleThread;
   LicenseInfo mLicenseInfo;
   AddressTransformer* mAddressTransformer;
   int mRefCnt;
   cpc::string mInstanceId;
   bool mInitialized;
   bool mLicenseOK;
   bool mShutdown;
   std::function<void(void)> mCbHook;
   PermissionsHandler* mPermissionsHandler;
   std::vector<std::function<void(void)>> mCallOnDestructFn;
   std::vector<std::function<void(void)>> mCallOnAppReleaseFn;
   static std::vector<resip::MultiReactor*> sSdkThreadPool;
   static std::vector<resip::MultiReactor*> sLoggerThreadPool;
   static std::vector< std::shared_ptr< AsioHolder > > sAsioIOServicePool;
   static std::atomic_int sLoggerThreadPoolIdx;
   static std::mutex sLoggerThreadPoolMutex;
   static std::atomic<bool> sCurlInitiated;
   SslCipherOptions mSslCipherList;
   BluetoothManagerImpl* mBluetoothManager;
   static ConnectionPreferences::ExternalResolverUsage mExternalResolver;
   resip::DeadlineTimer< resip::MultiReactor > mPreReleaseTimer;
   int mPreReleaseTimerFires;
   std::shared_ptr<CPCAPI2::EventQueue> mEventQueue;
   Licensing::LicensingClientManagerInterface* mBorrowedLicensingClientManager = nullptr;

public:

   std::string mPhoneName;

private:

   friend class PhoneInternal;
   resip::Mutex* mReleaseMutex;
   resip::Condition* mReleaseCondition;
   bool mIsReleasing; // avoid access to mInterfaces if true

   LocalLogger* mLocalLogger;
   FileLog* mFileLog;

   std::shared_ptr<AsioHolder> mAsio;
};

// feel free to specialize a dedicated version of GetInterfaceImpl() to add more functionalities
template<typename ModuleType, typename PhoneType, typename P0>
void GetInterfaceImpl(ModuleType*& module, PhoneType* phone, const cpc::string& name, P0 p0)
{
   if (!phone)
   {
      module = NULL;
      return;
   }

   module = dynamic_cast<ModuleType*>(phone->getInterfaceByName(name));
   if (module == NULL)
   {
      module = new ModuleType(p0);
      phone->registerInterface(name, module);
   }
}

// feel free to specialize a dedicated version of GetInterfaceImplEx() to add more functionalities
template<typename ModuleType, typename PhoneType, typename P0>
void GetInterfaceImplEx(ModuleType*& module, PhoneType* phone, const cpc::string& name, P0 p0)
{
   if (!phone)
   {
      module = NULL;
      return;
   }

   module = dynamic_cast<ModuleType*>(phone->getInterfaceByName(name));
   if (module == NULL)
   {
      module = new ModuleType(p0);
      phone->registerInterface(name, module);
      p0->addSdkObserver(module);
   }
}

// for modules without addition of SDK observer
template<typename ModuleType, typename PhoneType, typename P0>
ModuleType* _GetInterface(PhoneType* phone, const cpc::string& name, P0 p0)
{
   if (!phone) return NULL;

   ModuleType* module = dynamic_cast<ModuleType*>(phone->getInterfaceByName(name));
   if (module == NULL && phone->getSdkModuleThread().isRunning()) phone->getSdkModuleThread().execute(resip::resip_static_bind(&GetInterfaceImpl<ModuleType, PhoneType, P0>, std::ref(module), phone, name, p0));
   return module;
}

// simplified version of _GetInterface without p0 parameter
template<typename ModuleType, typename PhoneType>
ModuleType* _GetInterface(PhoneType* phone, const cpc::string& name)
{
   return _GetInterface<ModuleType>(phone, name, phone);
}

// for modules with addition of SDK observer
template<typename ModuleType, typename PhoneType, typename P0>
ModuleType* _GetInterfaceEx(PhoneType* phone, const cpc::string& name, P0 p0)
{
   if (!phone) return NULL;
   
   ModuleType* module = dynamic_cast<ModuleType*>(phone->getInterfaceByName(name));
   if (module == NULL && phone->getSdkModuleThread().isRunning()) phone->getSdkModuleThread().execute(resip::resip_static_bind(&GetInterfaceImplEx<ModuleType, PhoneType, P0>, std::ref(module), phone, name, p0));
   return module;
}

std::ostream& operator<<(std::ostream& os, const PhoneErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const LicensingErrorEvent& evt);

}

#endif // CPCAPI2_PHONE_INTERFACE_H
