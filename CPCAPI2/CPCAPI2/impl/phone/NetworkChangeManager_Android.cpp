#ifdef ANDROID

#include "NetworkChangeManager_Android.h"

#include "../util/cpc_logger.h"

#include <rutil/DnsUtil.hxx>
#include <rutil/dns/DnsStub.hxx>
#include <algorithm>

#include "JniHelper.h"
#include "AndroidNetworking.h"
#include "PhoneInterface.h"

#include <time.h>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

extern "C"
{
   JNIEXPORT void JNICALL Java_com_counterpath_sdk_android_NetworkChangeManager_onNetworkChange(JNIEnv * env, jclass cls, jint ntkType)
   {
      CPCAPI2::NetworkChangeManager_Android::notifyNetworkChanged(ntkType);
   }

   JNIEXPORT void JNICALL Java_com_counterpath_sdk_android_NetworkChangeManager_onPrivateDnsChange(JNIEnv * env, jclass cls, jboolean secureDns)
   {
      CPCAPI2::NetworkChangeManager_Android::notifyPrivateDnsChanged(secureDns);
   }
}

namespace CPCAPI2
{

// Static Members
std::vector<NetworkChangeManager_Android*> NetworkChangeManager_Android::sNetworkChangeManagers;
std::mutex NetworkChangeManager_Android::smNetworkChangeManagers;
bool NetworkChangeManager_Android::mLastDnsSecure = false;

NetworkChangeManager_Android::NetworkChangeManager_Android(Phone* phone) :
NetworkChangeManagerImpl(phone)
{
   StackLog(<< "NetworkChangeManager_Android");
   switch (PhoneInterface::externalResolver())
   {
   case ConnectionPreferences::ExternalResolverUsage_Disable:
   case ConnectionPreferences::ExternalResolverUsage_Enable:
      DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = false;
      break;
   case ConnectionPreferences::ExternalResolverUsage_Force:
      DnsStub::enableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = true;
      break;
   default:
      DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      mLastDnsSecure = false;
   }
}

NetworkChangeManager_Android::~NetworkChangeManager_Android()
{
   StackLog(<< "~NetworkChangeManager_Android");
   stop();
}

// NetworkChangeManagerImpl

int NetworkChangeManager_Android::start()
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   DebugLog(<< "NetworkChangeManager_Android::start()");
   mShutdown = false;

   if (mStarted)
   {
      if (mPollingImpl)
      {
         DebugLog(<< "NetworkChangeManager_Android::start(): polling-thread has already been spawned");
         return -1;
      }

      DebugLog(<< "NetworkChangeManager_Android::start(): triggering transport initialization");
      mPollingImpl = std::make_shared<NetworkChangePollingImpl>(mPhone, this, this);
      startJniThread();
      sendNetworkInitializeEvent();
   }
   else
   {
      DebugLog(<< "NetworkChangeManager_Android::start(): enabled: " << mStarted << " application not yet initialized, holding off on network initialization");
   }

   return 0;
}

int NetworkChangeManager_Android::stop()
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   DebugLog(<< "NetworkChangeManager_Android::stop()");
   mShutdown = true;

   if (mPollingImpl)
   {
      sendNetworkShutdownEvent();
      stopJniThread();
      mPollingImpl.reset();
      DebugLog(<< "NetworkChangeManager_Android::stop(): network polling shutdown completed");
   }
   return 0;
}

int NetworkChangeManager_Android::addObserver(NetworkChangeHandlerInternal* observer)
{
   mInternalHandlers.insert(observer);
   return kSuccess;
}

int NetworkChangeManager_Android::removeObserver(NetworkChangeHandlerInternal* observer)
{
   mInternalHandlers.erase(observer);
   return kSuccess;
}

NetworkTransport NetworkChangeManager_Android::networkTransport() const
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   if (!mShutdown && mStarted && mPollingImpl)
   {
      StackLog(<< "NetworkChangeManager_Android::networkTransport(): last transport: " << mPollingImpl->getTransport());
      return mPollingImpl->getTransport();
   }
   WarningLog(<< "NetworkChangeManager_Android::networkTransport(): using default transport: " << CPCAPI2::TransportNone << " last transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone) << " network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown);
   return CPCAPI2::TransportNone;
}

bool NetworkChangeManager_Android::isSecureDnsActive()
{
   return mLastDnsSecure;
}

int NetworkChangeManager_Android::currentWifiRssi()
{
   return Android::Networking::currentWifiRssi();
}

int NetworkChangeManager_Android::currentWifiSignalLevel()
{
   int currentRssi = Android::Networking::currentWifiRssi();
   return Android::Networking::currentWifiSignalLevel(currentRssi);
}

int NetworkChangeManager_Android::maxWifiSignalLevel()
{
   return Android::Networking::maxWifiSignalLevel();
}

int NetworkChangeManager_Android::currentWifiFreqMhz()
{
   return Android::Networking::currentWifiFreqMhz();
}

int NetworkChangeManager_Android::currentWifiChannel()
{
   return wifiFreqToChannel(currentWifiFreqMhz());
}

int NetworkChangeManager_Android::currentWifiLinkSpeedMbps()
{
   return Android::Networking::currentWifiLinkSpeedMbps();
}

CPCAPI2::NetworkTransport NetworkChangeManager_Android::queryTransport() const
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   NetworkTransport transport = CPCAPI2::TransportNone;
#ifdef ANDROID_LANGUAGE_WRAPPER
   if (mPollingImpl)
   {
      InfoLog(<< "NetworkChangeManager_Android::queryTransport()");
      if (CPCAPI2::Jni::IsNull(*mJniObject))
      {
         InfoLog(<< "NetworkChangeManager_Android::queryTransport(): jni object not initialized");
      }
      else
      {
         transport = (NetworkTransport)CPCAPI2::Jni::CallIntMethod("com/counterpath/sdk/android/NetworkChangeManager", *mJniObject, "getNetworkTransport", "()I");
      }
   }
   else
   {
      InfoLog(<< "NetworkChangeManager_Android::queryTransport(): ignoring query request - polling fsm not initialized: enabled: " << mStarted << " shutdown: " << mShutdown << " polling-impl: " << mPollingImpl);
   }
#endif
   InfoLog(<< "NetworkChangeManager_Android::queryTransport(): transport: " << transport);
   return transport;
}

void NetworkChangeManager_Android::queryInterfaces(NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet& interfaces)
{
   interfaces = NetworkChangeManagerImpl::getLocalIPAddresses();
}

// NetworkChangePollingHandler
void NetworkChangeManager_Android::onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt)
{
   evt.transport = queryTransport();
   NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet interfaces;
   queryInterfaces(interfaces);
   for (NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet::const_iterator i = interfaces.begin(); i != interfaces.end(); i++)
      evt.interfaces.insert((*i).c_str());
}

void NetworkChangeManager_Android::onPublishNetworkChange(const NetworkChangePublishEvent& evt)
{
   NetworkChangeEvent networkEvt;
   networkEvt.networkTransport = evt.transport;
   sendNetworkChangeEvent(networkEvt);
}

int NetworkChangeManager_Android::addPollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   if (handler)
   {
      if (mPollingImpl)
      {
         DebugLog(<< "NetworkChangeManager_Android::addPollingHandler(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
         mPollingImpl->addPollingHandler(handler);
      }
   }
   return kSuccess;
}

int NetworkChangeManager_Android::removePollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Android::removePollingHandler(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
      mPollingImpl->removePollingHandler(handler);
   }
   return kSuccess;
}

int NetworkChangeManager_Android::setPollingConfig(const NetworkChangePollingConfig& config)
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Android::setPollingConfig(): network-initialized: " << (mPollingImpl ? true : false) << " enabled: " << mStarted << " shutdown: " << mShutdown << " transport: " << (mPollingImpl ? mPollingImpl->getTransport() : CPCAPI2::TransportNone));
      mPollingImpl->setPollingConfig(config);
   }
   return kSuccess;
}

bool NetworkChangeManager_Android::isStarted() const
{
   return mStarted;
}

bool NetworkChangeManager_Android::isShutdown() const
{
   return mShutdown;
}

// JNI Interface

int NetworkChangeManager_Android::enableAndroidJavaNetworkChangeManager()
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   DebugLog(<< "NetworkChangeManager_Android::enableAndroidJavaNetworkChangeManager(): network-initialized: " << (mPollingImpl ? true : false));

   sNetworkChangeManagers.push_back(this);

   if (mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Android::enableAndroidJavaNetworkChangeManager(): polling-thread has already been spawned");
      return -1;
   }

   DebugLog(<< "NetworkChangeManager_Android::enableAndroidJavaNetworkChangeManager(): triggering polling start");
   mPollingImpl = std::make_shared<NetworkChangePollingImpl>(mPhone, this, this);
   startJniThread();
   sendNetworkInitializeEvent();
   mStarted = true;
   return 0;
}

int NetworkChangeManager_Android::disableAndroidJavaNetworkChangeManager()
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   std::vector<NetworkChangeManager_Android*>::iterator it = std::find(sNetworkChangeManagers.begin(), sNetworkChangeManagers.end(), this);
   if (it == sNetworkChangeManagers.end())
   {
      sNetworkChangeManagers.erase(it);
   }
   else
   {
      InfoLog(<< "NetworkChangeManager_Android::disableAndroidJavaNetworkChangeManager(): network manager not found in list");
   }

   DebugLog(<< "NetworkChangeManager_Android::disableAndroidJavaNetworkChangeManager(): triggering polling shutdown");

   sendNetworkShutdownEvent();
   stopJniThread();
   mPollingImpl.reset();
   mStarted = false;

   return 0;
}

void NetworkChangeManager_Android::notifyNetworkChanged(int ntkType)
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   DebugLog(<< "NetworkChangeManager_Android::notifyNetworkChanged(): manager-count: " << sNetworkChangeManagers.size());
   for (int i = 0; i < sNetworkChangeManagers.size(); i++)
   {
      NetworkChangeManager_Android* networkManager = sNetworkChangeManagers.at(i);
      if (NULL != networkManager)
      {
        networkManager->_onNetworkChange(ntkType);
      }
   }
}

void NetworkChangeManager_Android::onNetworkChange(int ntkType)
{
   std::lock_guard<std::mutex> _(smNetworkChangeManagers);
   _onNetworkChange(ntkType);
}

void NetworkChangeManager_Android::_onNetworkChange(int ntkType)
{
   DebugLog(<< "NetworkChangeManager_Android::onNetworkChange(): network-type: " << ntkType << " enabled: " << mStarted << " shutdown: " << mShutdown);
   NetworkChangeEvent evt;
   evt.networkTransport = (CPCAPI2::NetworkTransport)ntkType;
   if (mPollingImpl)
   {
      sendNetworkStatusEvent(evt);
   }
}

void NetworkChangeManager_Android::notifyPrivateDnsChanged(bool secureDns)
{
   if (PhoneInterface::externalResolver() == ConnectionPreferences::ExternalResolverUsage_Enable)
   {
      if (secureDns != mLastDnsSecure)
      {
         mLastDnsSecure = secureDns;
         if (secureDns)
            DnsStub::enableDnsFeatures(ExternalDns::Features::UseExternalResolver);
         else
            DnsStub::disableDnsFeatures(ExternalDns::Features::UseExternalResolver);
      }
   }
}

void NetworkChangeManager_Android::startJniThread()
{
#ifdef ANDROID_LANGUAGE_WRAPPER
   mJniThread.reset(new CPCAPI2::Jni::JniThread());
   CPCAPI2::Jni::ScopedLocalRef<jobject> obj = CPCAPI2::Jni::CallStaticObjectMethod("com/counterpath/sdk/android/NetworkChangeManager", "Instance", "()Lcom/counterpath/sdk/android/NetworkChangeManager;");
   mJniObject = *obj;
#endif
}

void NetworkChangeManager_Android::stopJniThread()
{
#ifdef ANDROID_LANGUAGE_WRAPPER
   mJniThread.reset();
#endif
}

void NetworkChangeManager_Android::sendNetworkStatusEvent(CPCAPI2::NetworkChangeEvent& evt)
{
   if (!(!mShutdown && mStarted && mPollingImpl))
   {
      DebugLog(<< "NetworkChangeManager_Android::sendNetworkStatusEvent(): ignoring request to send network status event: enabled: " << mStarted << " shutdown: " << mShutdown << " polling-impl: " << mPollingImpl);
      return;
   }

   NetworkStatusEvent statusEvt;
   statusEvt.currentTransport = evt.networkTransport;
   NetworkChangeManagerInterfaceSet currentInterfaces = NetworkChangeManagerImpl::getLocalIPAddresses();
   NetworkChangePollingImpl::convertTo(currentInterfaces, statusEvt.currentInterfaces);
   statusEvt.previousTransport = mPollingImpl->getTransport();
   NetworkChangePollingImpl::convertTo(mPollingImpl->getInterfaces(), statusEvt.previousInterfaces);

   for (HandlersSet::iterator i = mInternalHandlers.begin(); i != mInternalHandlers.end(); i++)
   {
      NetworkChangeHandlerInternal* handler = (*i);
      if (handler)
      {
         handler->onNetworkStatusEvent(statusEvt);
      }
   }
}

void NetworkChangeManager_Android::sendNetworkInitializeEvent()
{
   if (!(!mShutdown && mPollingImpl))
   {
      DebugLog(<< "NetworkChangeManager_Android::sendNetworkInitializeEvent(): ignoring request to send network initialize event: enabled: " << mStarted << " shutdown: " << mShutdown << " polling-impl: " << mPollingImpl);
      return;
   }

   DebugLog(<< "NetworkChangeManager_Android::sendNetworkInitializeEvent(): handler-count: " << mInternalHandlers.size());

   NetworkInitializeEvent evt;
   for (HandlersSet::iterator i = mInternalHandlers.begin(); i != mInternalHandlers.end(); i++)
   {
      NetworkChangeHandlerInternal* handler = (*i);
      if (handler)
      {
         handler->onNetworkInitializeEvent(evt);
      }
   }
}

void NetworkChangeManager_Android::sendNetworkShutdownEvent()
{
   if (!mPollingImpl)
   {
      DebugLog(<< "NetworkChangeManager_Android::sendNetworkShutdownEvent(): ignoring request to send network shutdown event: enabled: " << mStarted << " shutdown: " << mShutdown << " polling-impl: " << mPollingImpl);
      return;
   }

   DebugLog(<< "NetworkChangeManager_Android::sendNetworkShutdownEvent(): handler-count: " << mInternalHandlers.size());

   NetworkShutdownEvent evt;
   for (HandlersSet::iterator i = mInternalHandlers.begin(); i != mInternalHandlers.end(); i++)
   {
      NetworkChangeHandlerInternal* handler = (*i);
      if (handler)
      {
         handler->onNetworkShutdownEvent(evt);
      }
   }
}

}

#endif //ANDROID
