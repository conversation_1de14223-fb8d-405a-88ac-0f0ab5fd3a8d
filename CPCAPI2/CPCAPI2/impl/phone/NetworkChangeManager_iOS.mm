#include "NetworkChangeManager_iOS.h"


#if defined( __APPLE__ ) 
#include "TargetConditionals.h"
#if TARGET_OS_IPHONE

#include "util/cpc_logger.h"

#include <rutil/DnsUtil.hxx>
#include <list>

#import "CoreTelephony/CTTelephonyNetworkInfo.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace resip;

namespace CPCAPI2
{

NetworkChangeManager_iOS* NetworkChangeManager_iOS::createInstance(Phone* phone)
{
	// can't use make_shared due to constructor being private
	std::shared_ptr<NetworkChangeManager_iOS> s(new NetworkChangeManager_iOS(phone));
	s->mSharedThis = s;
	return s.get();
}

NetworkChangeManager_iOS::NetworkChangeManager_iOS(Phone* phone) :
	NetworkChangeManagerImpl(phone),
	mNetworkTransport(TransportNone)
{
	struct sockaddr_in zeroAddress;
	bzero(&zeroAddress, sizeof(zeroAddress));
	zeroAddress.sin_len = sizeof(zeroAddress);
	zeroAddress.sin_family = AF_INET;

	mReachabilityRef = SCNetworkReachabilityCreateWithAddress(kCFAllocatorDefault, (const struct sockaddr*)&zeroAddress);
	mDispatchQueue = dispatch_queue_create("com.counterpath.NetworkChangeManager", NULL);
}

NetworkChangeManager_iOS::~NetworkChangeManager_iOS()
{
	stop();

	if(mReachabilityRef!= NULL)
	{
		CFRelease(mReachabilityRef);
	}
}

void NetworkChangeManager_iOS::release()
{
   mSharedThis.reset();
}

int NetworkChangeManager_iOS::start()
{
	if(mReachabilityRef == NULL)
		return -1;

	if(mStarted)
		return 0;
   
	static std::weak_ptr<NetworkChangeManager_iOS> pWeak(mSharedThis);
	
	int retVal = -1;
	SCNetworkReachabilityContext	context = {0, &pWeak, NULL, NULL, NULL};

	if(SCNetworkReachabilitySetCallback(mReachabilityRef, ReachabilityCallback, &context))
	{
		if(SCNetworkReachabilitySetDispatchQueue(mReachabilityRef, mDispatchQueue))
		{
			mStarted = true;
			retVal = 0;

			checkReachability(NULL);
		}
	}

	return retVal;
}

int NetworkChangeManager_iOS::stop()
{
	if(!mStarted)
		return 0;

	if(mReachabilityRef != NULL)
	{
		SCNetworkReachabilitySetDispatchQueue(mReachabilityRef, NULL);
	}

	mStarted = false;
	mNetworkTransport = TransportNone;

	return 0;
}

NetworkTransport NetworkChangeManager_iOS::networkTransport() const
{
	return mNetworkTransport;
}

cpc::string NetworkChangeManager_iOS::wwanType() const
{
   // query on demand since type can fluctuate

   cpc::string ret;

   // logic borrowed from BRICE
   NSString *string = [[[CTTelephonyNetworkInfo alloc] init] currentRadioAccessTechnology];
   if([string isEqualToString:CTRadioAccessTechnologyLTE])
   {
     ret = "4G";
   }
   else if([string isEqualToString:CTRadioAccessTechnologyWCDMA])
   {
     ret = "3G";
   }
   else if([string isEqualToString:CTRadioAccessTechnologyGPRS] || [string isEqualToString:CTRadioAccessTechnologyEdge])
   {
     ret = "2G";
   }
   else if([string isEqualToString:CTRadioAccessTechnologyWCDMA] || [string isEqualToString:CTRadioAccessTechnologyHSDPA] || [string isEqualToString:CTRadioAccessTechnologyHSUPA])
   {
     ret = "3G";
   }
   else if([string isEqualToString:CTRadioAccessTechnologyCDMA1x] || [string isEqualToString:CTRadioAccessTechnologyCDMAEVDORev0] || [string isEqualToString:CTRadioAccessTechnologyCDMAEVDORevA] || [string  isEqualToString:CTRadioAccessTechnologyCDMAEVDORevB] || [string isEqualToString:CTRadioAccessTechnologyeHRPD])
   {
     ret = "3G";
   }
   else
   {
     ret = "3G";
   }

   return ret;
}
	
NetworkTransport NetworkChangeManager_iOS::getCurrentReachabilityStatus() const
{
	NetworkTransport retVal = TransportNone;

	if (mReachabilityRef)
	{
		SCNetworkReachabilityFlags flags;
		if (SCNetworkReachabilityGetFlags(mReachabilityRef, &flags))
		{
			retVal = networkStatusForFlags(flags);
		}
	}

	return retVal;

}

NetworkTransport NetworkChangeManager_iOS::networkStatusForFlags(SCNetworkReachabilityFlags flags) const
{
	printReachabilityFlags(flags);

	if ((flags & kSCNetworkReachabilityFlagsReachable) == 0)
	{
		// if target host is not reachable
		return TransportNone;
	}

	NetworkTransport retVal = TransportNone;

	if ((flags & kSCNetworkReachabilityFlagsConnectionRequired) == 0)
	{
		// if target host is reachable and no connection is required
		//  then we'll assume (for now) that your on Wi-Fi
		retVal = TransportWiFi;
	}


	if ((((flags & kSCNetworkReachabilityFlagsConnectionOnDemand ) != 0) ||
			 (flags & kSCNetworkReachabilityFlagsConnectionOnTraffic) != 0))
	{
		// ... and the connection is on-demand (or on-traffic) if the
		//     calling application is using the CFSocketStream or higher APIs

		if ((flags & kSCNetworkReachabilityFlagsInterventionRequired) == 0)
		{
			// ... and no [user] intervention is needed
			retVal = TransportWiFi;
		}
	}

	if ((flags & kSCNetworkReachabilityFlagsIsWWAN) == kSCNetworkReachabilityFlagsIsWWAN)
	{
		// ... but WWAN connections are OK if the calling application
		//     is using the CFNetwork (CFSocketStream?) APIs.
		retVal = TransportWWAN;
	}
	return retVal;
}


void NetworkChangeManager_iOS::ReachabilityCallback(SCNetworkReachabilityRef target, SCNetworkReachabilityFlags flags, void* info)
{
	NetworkChangeManager_iOS *pThis = (NetworkChangeManager_iOS*)info;

	DebugLog(<< "ReachabilityCallback: " << flags);

	std::weak_ptr<NetworkChangeManager_iOS> &pWeak = *static_cast<std::weak_ptr<NetworkChangeManager_iOS>*>(info);
	
	dispatch_async(dispatch_get_main_queue(), ^
	{
		auto pThis = pWeak.lock();
		
		if(pThis == NULL)
			return;
		
		SCNetworkReachabilityFlags f = flags;
		pThis->checkReachability(&f);
	});
}

void NetworkChangeManager_iOS::checkReachability(const SCNetworkReachabilityFlags *flags)
{
	NetworkTransport netStatus = flags != NULL ? networkStatusForFlags(*flags): getCurrentReachabilityStatus();

	if(netStatus != mNetworkTransport)
	{
		mNetworkTransport = netStatus;
		initializeAddressList();

		NetworkChangeEvent params;
		params.networkTransport = netStatus;
		sendNetworkChangeEventUnconditional(params);
	}
}

void NetworkChangeManager_iOS::printReachabilityFlags(SCNetworkReachabilityFlags flags) const
{
	DebugLog(<< "Reachability Flag Status: "
					 << ((flags & kSCNetworkReachabilityFlagsIsWWAN)				       ? 'W' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsReachable)            ? 'R' : '-')
					 << " "
					 << ((flags & kSCNetworkReachabilityFlagsTransientConnection)  ? 't' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsConnectionRequired)   ? 'c' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsConnectionOnTraffic)  ? 'C' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsInterventionRequired) ? 'i' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsConnectionOnDemand)   ? 'D' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsIsLocalAddress)       ? 'l' : '-')
					 << ((flags & kSCNetworkReachabilityFlagsIsDirect)             ? 'd' : '-'));
}

void NetworkChangeManager_iOS::checkInterfaces()
{
	std::weak_ptr<NetworkChangeManager_iOS> pWeak(mSharedThis);
	
	dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^
	{
		auto pThis = pWeak.lock();
		
		if(pThis == NULL)
			return;
		
		if (pThis->mStarted && pThis->mNetworkTransport == TransportWiFi)
		{
			std::list<resip::Data> addrList;
			pThis->getWiFiAddressList(addrList);

			// Network change will also be triggered if the IP list sequence has changed
			if ((pThis->mWifiAddrs.size() == 0) && (addrList.size() != 0))
			{
				DebugLog(<< "Creating new WiFi addr list");
				pThis->mWifiAddrs = addrList;
			}
			else if ((pThis->mWifiAddrs.size() != 0) && (addrList.size() == 0))
			{
				DebugLog(<< "Clearing WiFi addr list");
				pThis->mWifiAddrs.clear();
			}
			else if (pThis->mWifiAddrs != addrList)
			{
				pThis->mWifiAddrs = addrList;

				DebugLog(<< "Changes detected in WiFi addr list");

				dispatch_async(dispatch_get_main_queue(), ^
				{
					auto pThis = pWeak.lock();
					
					if (pThis == NULL)
						return;
					
					NetworkChangeEvent params;
					params.networkTransport = mNetworkTransport;
					pThis->sendNetworkChangeEventUnconditional(params);
				});
			}
			else
			{
				//StackLog(<< "No changes detected in WiFi addr list");
			}

			pThis->checkInterfaces();
		}
		else
		{
			//StackLog(<< "Clearing WiFi addr list, change manager not started or network transport is not WiFi");
			pThis->mWifiAddrs.clear();
		}
	});
}

void NetworkChangeManager_iOS::initializeAddressList()
{
   DebugLog(<< "NetworkChangeManager_iOS::initializeAddressList()");
   mWifiAddrs.clear();
   if (mStarted && mNetworkTransport == TransportWiFi)
   {
		  getWiFiAddressList(mWifiAddrs);
			checkInterfaces();
   }
}

void NetworkChangeManager_iOS::getWiFiAddressList(std::list<resip::Data>& addrList)
{
   std::list<std::pair<resip::Data,resip::Data> > ifs = resip::DnsUtil::getInterfaces();
   for (std::list<std::pair<resip::Data,resip::Data> >::const_iterator it = ifs.begin(); it != ifs.end(); ++it)
   {
      if (resip::isEqualNoCase(it->first, "en0"))
      {
         //StackLog(<< "NetworkChangeManager_iOS::getWiFiAddressList(): Adding IP from interface: " << it->first << " to list: " << it->second);
         addrList.push_back(it->second);
      }
   }

   //StackLog(<< "NetworkChangeManager_iOS::getWiFiAddressList(): Interface list size: " << ifs.size() << " WiFi address list size: " << addrList.size());
}

}

#endif // #if TARGET_OS_IPHONE
#endif // #if defined( __APPLE__ )
