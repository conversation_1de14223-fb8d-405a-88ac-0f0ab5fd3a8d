#pragma once

#ifndef CPCPAPI2_NETWORK_CHANGE_MANAGER_METRO_H
#define CPCPAPI2_NETWORK_CHANGE_MANAGER_METRO_H

#ifdef WinRT

#include "NetworkChangeManagerImpl.h"


namespace CPCAPI2
{
class NetworkChangeManager_Metro : public NetworkChangeManagerImpl
{
public:
    NetworkChangeManager_Metro(Phone* phone);
    virtual ~NetworkChangeManager_Metro();

    virtual int start();
    virtual int stop();

    virtual NetworkTransport networkTransport() const;

private:
    NetworkChangeManager_Metro();

    NetworkTransport getTransportType(Windows::Networking::Connectivity::ConnectionProfile^ networkInterface);
    bool mRegisteredNetworkStatusNotification;
    Windows::Foundation::EventRegistrationToken mToken;
    void networkChanged();

    NetworkTransport mNetworkTransport;
};
}

#endif // WinRT
#endif // CPCPAPI2_NETWORK_CHANGE_MANAGER_METRO_H
