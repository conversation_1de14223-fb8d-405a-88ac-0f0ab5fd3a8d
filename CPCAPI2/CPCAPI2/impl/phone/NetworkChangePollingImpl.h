#pragma once

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_IMPL_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_IMPL_H

#include "NetworkChangeManagerImpl.h"
#include "NetworkChangeHandlerInternal.h"
#include "NetworkChangePollingHandler.h"
#include "NetworkChangePollingManager.h"

#include "../util/cpc_thread.h"
#include "../util/AutoResetEvent.h"

#include <set>
#include <atomic>
#include <future>
#include <mutex>
#include <rutil/Data.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/RecursiveMutex.hxx>
#include <rutil/DeadlineTimer.hxx>


/***************************************************************************************************************************************************
*
* Network Status Polling FSM
* ===========================
* The network status polling impl runs on a separate polling thread and is intended to synchronize the sdk network status with the current OS
* network status. It triggers a network change status event to all the sdk observers after some delay from the point of receiving the updated
* network status from the OS. This allows the network change manager to filter out the noise of multiple network events - transport and interface
* changes - from the OS until the network stabilizes, that occur when the device actually goes through a network change. It also avoids having
* each of the sdk modules interested in network events, from having to bind to the platform-specific callbacks.
*
* The impl states handle the following events:
* - initialize and shutdown events
* - status, notify and stabilize timeouts
* - transport and interface updates from the OS (e.g. via JNI on the Android platform)
*
* Idle State:
* - impl starts off in this state, and returns to this state upon shutdown
* - initialize-event triggers the impl to initialize the current network transport and interfaces, and change to the sync-state
*
* Sync State:
* - starts the status-timer
* - periodically checks the network status triggered by the status-timeout
* - handles the transport and interfaces updates from the OS
* - any transport or interface changes detected, either by the manual query or by the OS events, result in a change to the notify-state
*
* Notify State:
* - primary goal of this state is to delay the notification of a network status update to the sdk observers, this is to let the
*   network stabilize as multiple network events can be triggered from the OS when network changes occur. Network change handling
*   in the sdk modules can be fairly expensive, as usually it results in a teardown and restart of the network connections, hence
*   the requirement for the notification delay to prevent the sdk modules from going through network change handling multiple times.
* - starts the notify-timer
* - sends out the network status change event to the sdk observers upon notify-timeout, and change to the stabilize-state
* - any transport changes detected, either by the manual query or by the OS events, result in a reset of the notify timer
* - interface updates from the OS are ignored
*
* Stabilize State:
* - primary goal of this state is to ignore interface changes that can occur a few seconds after a transport change. This was required
*   to optimize the total duration required to process the network change handling. Intent was to reduce the default notification delay,
*   based on the assumption that the interfaces changes that occur a few seconds after a transport change, are associated to the same
*   network change. Ignoring the interface changes that occur within the stabilization-timeout, prevents the sdk observer notification
*   from being delayed, as the notification timer is no longer reset based on interface changes associated to the same network change.
* - starts the stabilize-timer
* - changes back to the sync-state upon stabilize-timeout
* - any transport changes detected, either by the manual query or by the OS events, result in a change back to the notify-state
* - interface updates from the OS are ignored
*
*
*
*                                     +--------------------+
*                                     |                    |
*                                     |                    |
*     +------------------------------->        Idle        |
*     |                               |                    |
*     |                               |                    |
*     |                               +----------+---------+
*     |                                          |
*     |                                          | initialize
*     |                                          | - check network status
*     |                                          | - start status timer
*     |                                          |
*     |                               +----------v----------+
*     |                               |                     |
*     |           shutdown            |                     |
*     |<------------------------------+        Sync         <--------------------------------+    sync-state: periodically queries
*     |                               |                     |                                |    network status upon status-timeout
*     |                               |                     |                                |
*     |                               +----------+----------+                                |
*     |                                          |                                           |
*     |                                          | transport or interface update             |
*     |                                          | - start notify timer                      |
*     |                                          |                                           |
*     |                                          |                                           |
*     |                               +----------v----------+                                |
*     |                               |                     |                                |
*     |           shutdown            |                     |                                |
*     |<------------------------------+       Notify        |                                |    notify-state: ignores interface changes,
*     |                               |                     |                                |    resets notify timer upon transport change
*     |                               |                     |                                |
*     |                               +---+-------------^---+                                |
*     |                                   |             |                                    |
*     |       notify-timeout              |             | transport update                   |
*     |       - send network change event |             | - start notify timer               |
*     |       - start stabilize timer     |             |                                    |
*     |                                   |             |                                    |
*     |                               +---v-------------+---+                                |
*     |                               |                     |                                |
*     |           shutdown            |                     |                                |
*     +<------------------------------+      Stabilize      +--------------------------------+    stabilize-state: ignores interface changes
*                                     |                     |     stabilize-timeout
*                                     |                     |     - start status timer
*                                     +---------------------+
*
*
*
***************************************************************************************************************************************************/

namespace CPCAPI2
{

/*
class NetworkChangePollingImpl;

class NetworkChangePollingFactory
{
public:
   static NetworkChangePollingFactory* instance();
   std::shared_ptr<NetworkChangePollingImpl> createImpl();
protected:
   void releaseImpl();
private:
   NetworkChangePollingFactory();
   static NetworkChangePollingFactory* mInstance;
   std::shared_ptr<NetworkChangePollingImpl> mImpl;
};
*/

class NetworkChangePollingImpl : public NetworkChangeHandlerInternal,
                                 public resip::DeadlineTimerHandler,
                                 public resip::ReactorBinded,
                                 public std::enable_shared_from_this<NetworkChangePollingImpl>
{

public:

   NetworkChangePollingImpl(PhoneInterface* phone, NetworkChangePollingHandler* handler, NetworkChangeManagerImpl* manager);
   virtual~ NetworkChangePollingImpl();

   virtual CPCAPI2::NetworkChangePollingStateType getState();
   virtual void changeState(CPCAPI2::NetworkChangePollingStateType stateType);
   CPCAPI2::NetworkTransport getTransport();
   void setTransport(CPCAPI2::NetworkTransport transport);
   std::set<resip::Data>& getInterfaces();
   NetworkChangePollingHandler* getHandler() { return mHandler; };

   // ReactorBinded
   virtual void release() OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   // NetworkChangeHandlerInternal
   virtual void onNetworkInitializeEvent(NetworkInitializeEvent& evt) OVERRIDE;
   virtual void onNetworkShutdownEvent(NetworkShutdownEvent& evt) OVERRIDE;
   virtual void onNetworkStatusEvent(NetworkStatusEvent& evt) OVERRIDE;

   // Based on NetworkChangePollingManager
   virtual void addPollingHandler(NetworkChangePollingHandlerInternal* handler);
   virtual void removePollingHandler(NetworkChangePollingHandlerInternal* handler);
   virtual void setPollingConfig(const NetworkChangePollingConfig& config);

   static void convertTo(const NetworkInterfaces& interfaces, std::set<resip::Data>& interfacesSet);
   static void convertTo(const std::set<resip::Data>& interfacesSet, NetworkInterfaces& interfaces);
   static void convertTo(const std::set<resip::Data>& interfacesData, std::set<std::string>& interfacesString);
   static void convertTo(const std::set<std::string>& interfacesString, std::set<resip::Data>& interfacesData);
   static void convertTo(const std::set<std::string>& interfacesString, cpc::vector<cpc::string>& interfaces);
   static void convertTo(const cpc::vector<cpc::string>& interfaces, std::set<std::string>& interfacesString);
   static std::string getInterfaceList(const std::set<resip::Data>& interfaces);

protected:

   struct StatusTimeoutEvent {};
   struct NotifyTimeoutEvent {};
   struct StabilizeTimeoutEvent {};
   struct PollingConfigUpdateEvent {};

   void createNetworkStatusEvent(NetworkStatusEvent& statusEvt);
   void sendNetworkChangePublishEvent();
   void sendNetworkChangePollingStatusEvent(bool sendAsync = true);
   void sendNetworkChangePollingNotificationResetEvent();

   void cancelTimers();
   void cancelStatusTimer();
   void cancelNotifyTimer();
   void cancelStabilizeTimer();
   void resetStatusTimer();
   void resetNotifyTimer();
   void resetStabilizeTimer();

private:

   class NetworkChangePollingStateFactory;
   class NetworkChangePollingState;

   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkTransport& type);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkStatusEvent& evt);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkInterfaceQueryEvent& evt);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePublishEvent& evt);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStatusEvent& evt);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStateType& type);
   friend std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingConfig& config);

   void postToSdk(resip::ReadCallbackBase* rcb);

   class NetworkChangePollingStateFactory
   {
   public:
      NetworkChangePollingStateFactory();
      virtual~ NetworkChangePollingStateFactory();
      virtual void create();
      virtual NetworkChangePollingState* getState(CPCAPI2::NetworkChangePollingStateType type);
   private:
      NetworkChangePollingState* create(CPCAPI2::NetworkChangePollingStateType type);

      typedef std::map<CPCAPI2::NetworkChangePollingStateType, NetworkChangePollingState*> NetworkChangePollingStates;
      NetworkChangePollingStates mStates;
   };

   class NetworkChangePollingState
   {
   public:
      NetworkChangePollingState(CPCAPI2::NetworkChangePollingStateType state);
      virtual~ NetworkChangePollingState();
      virtual void onEntry(NetworkChangePollingImpl* impl) = 0;
      virtual void onExit(NetworkChangePollingImpl* impl) = 0;
      virtual CPCAPI2::NetworkChangePollingStateType getState();
      virtual std::string getName();
      static std::string getName(CPCAPI2::NetworkChangePollingStateType state);
      CPCAPI2::NetworkChangePollingStateType mState;

      virtual void handleNetworkInitializeEvent(NetworkChangePollingImpl* impl, const NetworkInitializeEvent& evt);
      virtual void handleNetworkShutdownEvent(NetworkChangePollingImpl* impl, const NetworkShutdownEvent& evt);
      virtual void handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt);
      virtual void handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt);
      virtual void handleNotifyTimeoutEvent(NetworkChangePollingImpl* impl, const NotifyTimeoutEvent& evt);
      virtual void handleStabilizeTimeoutEvent(NetworkChangePollingImpl* impl, const StabilizeTimeoutEvent& evt);
      virtual void handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt);

   private:
      NetworkChangePollingState();
   };

   class NetworkChangePollingIdleState : public NetworkChangePollingState
   {
   public:
      NetworkChangePollingIdleState();
      virtual~ NetworkChangePollingIdleState();
      virtual void onEntry(NetworkChangePollingImpl* impl) OVERRIDE;
      virtual void onExit(NetworkChangePollingImpl* impl) OVERRIDE;

      virtual void handleNetworkInitializeEvent(NetworkChangePollingImpl* impl, const NetworkInitializeEvent& evt) OVERRIDE;

   private:
   };

   class NetworkChangePollingNotifyState : public NetworkChangePollingState
   {
   public:
      NetworkChangePollingNotifyState();
      virtual~ NetworkChangePollingNotifyState();
      virtual void onEntry(NetworkChangePollingImpl* impl) OVERRIDE;
      virtual void onExit(NetworkChangePollingImpl* impl) OVERRIDE;

      virtual void handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt) OVERRIDE;
      virtual void handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt) OVERRIDE;
      virtual void handleNotifyTimeoutEvent(NetworkChangePollingImpl* impl, const NotifyTimeoutEvent& evt) OVERRIDE;
      virtual void handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt) OVERRIDE;

      bool shouldNotify(const NetworkStatusEvent& evt);

   private:
   };

   class NetworkChangePollingStabilizeState : public NetworkChangePollingState
   {
   public:
      NetworkChangePollingStabilizeState();
      virtual~ NetworkChangePollingStabilizeState();
      virtual void onEntry(NetworkChangePollingImpl* impl) OVERRIDE;
      virtual void onExit(NetworkChangePollingImpl* impl) OVERRIDE;

      virtual void handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt) OVERRIDE;
      virtual void handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt) OVERRIDE;
      virtual void handleStabilizeTimeoutEvent(NetworkChangePollingImpl* impl, const StabilizeTimeoutEvent& evt) OVERRIDE;
      virtual void handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt) OVERRIDE;

      bool shouldNotify(const NetworkStatusEvent& evt);

   private:
   };

   class NetworkChangePollingSyncState : public NetworkChangePollingState
   {
   public:
      NetworkChangePollingSyncState();
      virtual~ NetworkChangePollingSyncState();
      virtual void onEntry(NetworkChangePollingImpl* impl) OVERRIDE;
      virtual void onExit(NetworkChangePollingImpl* impl) OVERRIDE;

      virtual void handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt) OVERRIDE;
      virtual void handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt) OVERRIDE;
      virtual void handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt) OVERRIDE;

      bool shouldNotify(const NetworkStatusEvent& evt);

   private:
   };

private:

   NetworkChangePollingImpl() {}

   // On polling thread
   // NetworkChangeHandlerInternal
   void onNetworkInitializeEventImpl(const NetworkInitializeEvent& evt);
   void onNetworkShutdownEventImpl(const NetworkShutdownEvent& evt);
   void onNetworkStatusEventImpl(const NetworkStatusEvent& evt);

   // On polling thread
   // Based on NetworkChangePollingManager
   void addPollingHandlerImpl(NetworkChangePollingHandlerInternal* handler);
   void removePollingHandlerImpl(NetworkChangePollingHandlerInternal* handler);
   void setPollingConfigImpl(const NetworkChangePollingConfig& config);

   // On sdk thread
   void sendNetworkChangePollingStatusEventImpl(const NetworkChangePollingStatusEvent& evt);
   void sendNetworkChangePollingNotificationResetEventImpl(const NetworkChangePollingNotificationResetEvent& evt);

   PhoneInterface* mPhone;
   std::shared_ptr<NetworkChangePollingImpl> mInstance;
   std::unique_ptr<NetworkChangePollingStateFactory> mFactory;
   NetworkChangePollingStateType mStateType;
   std::set<resip::Data> mInterfaces;
   volatile CPCAPI2::NetworkTransport mTransport;
   NetworkChangePollingConfig mConfig;
   NetworkChangePollingHandler* mHandler;
   NetworkChangeManagerImpl* mManager;

   std::promise<bool> mPollingStartupFuture;
   std::promise<bool> mPollingShutdownFuture;
   std::unique_ptr<resip::MultiReactor> mPollingThread;

   std::promise<bool> mHandlerRemovalFuture;
   std::recursive_mutex mHandlerMutex;
   std::recursive_mutex mInterfacesMutex;
   typedef std::set<NetworkChangePollingHandlerInternal*> HandlersSet;
   HandlersSet mHandlers;

   std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mStatusTimer;
   std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mNotifyTimer;
   std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mStabilizeTimer;

};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkTransport& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkStatusEvent& evt);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkInterfaceQueryEvent& evt);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePublishEvent& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStatusEvent& evt);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStateType& type);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingConfig& config);


}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_IMPL_H
