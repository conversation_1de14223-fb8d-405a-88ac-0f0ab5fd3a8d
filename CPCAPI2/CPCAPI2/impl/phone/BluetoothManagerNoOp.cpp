#include "BluetoothManagerNoOp.h"

namespace CPCAPI2
{
BluetoothManagerNoOp::BluetoothManagerNoOp(Phone* phone) : 
   BluetoothManagerImpl(phone)
{
}
int BluetoothManagerNoOp::start()
{
   return 0;
}
int BluetoothManagerNoOp::stop()
{
   return 0;
}
bool BluetoothManagerNoOp::isBluetoothHeadsetAvailable() const
{
   return false; 
}

bool BluetoothManagerNoOp::isBluetoothAudioConnected() const
{
   return false;
}
}