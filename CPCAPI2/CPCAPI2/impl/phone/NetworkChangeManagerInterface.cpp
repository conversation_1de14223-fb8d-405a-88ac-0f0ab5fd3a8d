#include "phone/Phone.h"
#include "NetworkChangeManagerImpl.h"
#include "NetworkChangeManagerInterface.h"

#ifdef CPCAPI2_AUTO_TEST
#include "NetworkChangeManager_Mock.h"
#endif

#if _WIN32
// WinRT specific
#if WINAPI_FAMILY_ONE_PARTITION( WINAPI_FAMILY_DESKTOP_APP, WINAPI_PARTITION_APP )
#include "NetworkChangeManager_Metro.h"
#else
#include "NetworkChangeManager_Polling.h"
#endif
#elif TARGET_OS_IPHONE
#include "NetworkChangeManager_iOS.h"
#elif ANDROID
#include "NetworkChangeManager_Android.h"
#else
#include "NetworkChangeManager_Polling.h"
#endif

#include "rutil/Reactor.hxx"
#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"

#include "gen/NetworkChangeManager/datatypes/NetworkChangeManagerEvents.h"
#include "gen/NetworkChangeManager/datatypes/NetworkChangeManagerNetworkChangeEvent.h"
#include "phone/EventQueue.h"


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{
NetworkChangeManager* NetworkChangeManager::getInterface(Phone* cpcPhone)
{
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<NetworkChangeManagerInterface>(phone, "NetworkChangeManagerInterface");
}

NetworkChangeManagerInterface::NetworkChangeManagerInterface(Phone* phone) :
   CPCAPI2::EventSource2<CPCAPI2::EventHandler<NetworkChangeHandler, NetworkChangeManagerHandle> >(dynamic_cast<PhoneInterface*>( phone )),
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mNetworkChangeManager(nullptr),
   mNextHandle(1)
{
  addSdkObserver(this);
}

void NetworkChangeManagerInterface::initImpl(bool useNetworkChangeManager, const ConnectionPreferences::NetworkChangeManagerType& networkChangeManagerType, NetworkTransport transport)
{
  bool usePlatformDefaultNetworkChangeManager = true;
#ifdef CPCAPI2_AUTO_TEST
  if (networkChangeManagerType == ConnectionPreferences::NetworkChangeManagerType_Mock)
  {
     mNetworkChangeManager = new NetworkChangeManager_Mock(mPhone, transport);
     usePlatformDefaultNetworkChangeManager = false;
  }
  else if (networkChangeManagerType == ConnectionPreferences::NetworkChangeManagerType_PlatformDefault)
  {
     usePlatformDefaultNetworkChangeManager = true;
  }
#endif
  if (usePlatformDefaultNetworkChangeManager)
  {
#if _WIN32
#if WINAPI_FAMILY_ONE_PARTITION( WINAPI_FAMILY_DESKTOP_APP, WINAPI_PARTITION_APP )
     mNetworkChangeManager = new NetworkChangeManager_Metro(mPhone);
#else
     mNetworkChangeManager = new NetworkChangeManager_Polling(mPhone);
#endif
#elif TARGET_OS_IPHONE
     mNetworkChangeManager = NetworkChangeManager_iOS::createInstance(mPhone);
#elif defined(ANDROID)
     mNetworkChangeManager = new NetworkChangeManager_Android(mPhone);
#else
     mNetworkChangeManager = new NetworkChangeManager_Polling(mPhone);
#endif
  }

  if (useNetworkChangeManager)
  {
     mNetworkChangeManager->start();
  }
}

NetworkChangeManagerInterface::~NetworkChangeManagerInterface()
{
}

void NetworkChangeManagerInterface::Release()
{
   if (mNetworkChangeManager != NULL)
   {
      mNetworkChangeManager->reactorSafeRelease(&(mPhone->getSdkModuleThread()));
   }
   delete this;
}

NetworkChangeManager::NetworkChangeManagerHandle NetworkChangeManagerInterface::create()
{
   return mNextHandle++;
}

int NetworkChangeManagerInterface::setHandler(NetworkChangeManagerHandle handle, NetworkChangeHandler* handler)
{
   postToSdkThread(resip::resip_bind(&NetworkChangeManagerInterface::setHandlerImpl, this, handle, handler));
   return kSuccess;
}

void NetworkChangeManagerInterface::setHandlerImpl(NetworkChangeManagerHandle handle, NetworkChangeHandler* handler)
{
   if (handler == NULL)
   {
      removeHandlerImpl(handle);
   }
   else
   {
      addAppHandler(handler, handle);
      mAppHandlers[handle] = handler;
      start();
   }
}

int NetworkChangeManagerInterface::removeHandler(NetworkChangeManagerHandle handle)
{
   // removing the handler involves two steps:
   // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
   // use to remove
   process(-1);

   // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
   // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
   // handler
   executeOnSdkThread(resip::resip_bind(&NetworkChangeManagerInterface::removeHandlerImpl, this, handle));
   return kSuccess;
}

void NetworkChangeManagerInterface::removeHandlerImpl(NetworkChangeManagerHandle handle)
{
   if (mAppHandlers.end() != mAppHandlers.find(handle))
   {
     removeAppHandler(mAppHandlers.find(handle)->second);
     mAppHandlers.erase(handle);
   }
}

static jsonrpc::CPCAPI2::NetworkChangeManager::NetworkTransport convert(::CPCAPI2::NetworkTransport item)
{
   switch (item)
   {
      case ::CPCAPI2::NetworkTransport::TransportNone:
         return jsonrpc::CPCAPI2::NetworkChangeManager::NetworkTransport::None;
      case ::CPCAPI2::NetworkTransport::TransportWiFi:
         return jsonrpc::CPCAPI2::NetworkChangeManager::NetworkTransport::Wifi;
      case ::CPCAPI2::NetworkTransport::TransportWWAN:
         return jsonrpc::CPCAPI2::NetworkChangeManager::NetworkTransport::WWan;
   }
}

int NetworkChangeManagerInterface::onNetworkChange(const NetworkChangeEvent& args)
{
  for (HandlersMap::iterator it = mAppHandlers.begin(); it != mAppHandlers.end(); ++it)
  {
     fireEvent(cpcEvent(NetworkChangeHandler, onNetworkChangeEx), it->first, args);
     jsonrpc::CPCAPI2::NetworkChangeManager::NetworkChangeManagerNetworkChangeEvent jrpcEvt;
     jrpcEvt.handle = it->first;
     jrpcEvt.networkTransport = convert(args.networkTransport);

     mPhone->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::NetworkChangeManager::NetworkChangeManagerEvents::NetworkChangeManagerDotOnNetworkChange), 
                                                std::move(jrpcEvt.marshal())));
  }
  return kSuccess;
}

int NetworkChangeManagerInterface::start()
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->start();
   }
   return -1;
};

int NetworkChangeManagerInterface::stop()
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->stop();
   }
   return -1;
};

NetworkTransport NetworkChangeManagerInterface::networkTransport() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->networkTransport();
   }
   return NetworkTransport::TransportNone;
}

cpc::string NetworkChangeManagerInterface::wwanType() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->wwanType();
   }
   return cpc::string();
};

int NetworkChangeManagerInterface::currentWifiRssi() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->currentWifiRssi();
   }
   return -1;
}

int NetworkChangeManagerInterface::currentWifiSignalLevel() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->currentWifiSignalLevel();
   }
   return -1;
}

int NetworkChangeManagerInterface::maxWifiSignalLevel() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->maxWifiSignalLevel();
   }
   return -1;
}

int NetworkChangeManagerInterface::currentWifiFreqMhz() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->currentWifiFreqMhz();
   }
   return -1;
}

int NetworkChangeManagerInterface::currentWifiChannel() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->currentWifiChannel();
   }
   return -1;
}

int NetworkChangeManagerInterface::currentWifiLinkSpeedMbps() const
{
   if (mNetworkChangeManager)
   {
      return mNetworkChangeManager->currentWifiLinkSpeedMbps();
   }
   return -1;
}

#ifdef CPCAPI2_AUTO_TEST
NetworkChangeManager_Mock* NetworkChangeManagerInterface::getMockImpl()
{
  return dynamic_cast<NetworkChangeManager_Mock*>(mNetworkChangeManager);
}
#endif

#ifdef ANDROID
NetworkChangeManager_Android* NetworkChangeManagerInterface::getAndroidImpl()
{
  return dynamic_cast<NetworkChangeManager_Android*>(mNetworkChangeManager);
}
#endif

std::ostream& operator<<(std::ostream& os, const NetworkChangeEvent& evt)
{
   return os << "NetworkChangeEvent";
}

}
