#pragma once

#ifndef CPCAPI2_BLUETOOTH_MANAGER_ANDROID_H
#define CPCAPI2_BLUETOOTH_MANAGER_ANDROID_H

#ifdef ANDROID

#include "BluetoothManagerImpl.h"

#include "../util/cpc_thread.h"
#include "../util/AutoResetEvent.h"

#include <condition_variable>
#include <mutex>

#include <jni.h>

namespace CPCAPI2
{
typedef enum
{
   BluetoothUnitialized = 0,
   BluetoothError = 1,
   BluetoothHeadsetUnavailable = 2,
   BluetoothHeadsetAvailable = 3,
   BluetoothAudioDisconnected = 4,
   BluetoothAudioConnected = 5,
   BluetoothAudioDisconnectedUnexpected = 6,
   BluetoothAudioConnectedUnexpected = 7
} BluetoothUpdate;

static int const BLUETOOTH_STATE_FLAG_UNAVAILABLE      = 0x0;
static int const BLUETOOTH_STATE_FLAG_ERROR            = 0x1;
static int const BLUETOOTH_STATE_FLAG_DEVICE_CONNECTED = 0x2;
static int const BLUETOOTH_STATE_FLAG_AUDIO_CONNECTED  = 0x4;

class BluetoothManager_Android : public BluetoothManagerImpl
{
public:
    BluetoothManager_Android(Phone* phone);
    virtual ~BluetoothManager_Android();
    virtual void release() OVERRIDE;

    virtual int start() OVERRIDE;
    virtual int stop() OVERRIDE;

    virtual bool isBluetoothHeadsetAvailable() const OVERRIDE;
    virtual bool isBluetoothAudioConnected() const OVERRIDE;

    static void notifyBluetooth(int btType, const char* address, const char* name);
    void onBluetoothEvent(int btType, const char* address, const char* name);
    int enableAndroidJavaBluetoothManager();
    int disableAndroidJavaBluetoothManager();

private:
    void btWorkerThread();

private:
    static std::vector<BluetoothManager_Android*> sBluetoothManagers;
    static std::mutex smBluetoothManagers;
    std::mutex smBluetoothEvents;

    BluetoothManager_Android();

    volatile bool mShutdown;
    thread* mWorkerThread;
    AutoResetEvent mBluetoothEvt;
    typedef struct { BluetoothUpdate type;  BluetoothEvent evt; } BluetoothTypeEvent;
    std::deque<BluetoothTypeEvent> mBluetoothEvents;
    int mBluetoothState;
};
}

#endif // ANDROID
#endif // CPCAPI2_BLUETOOTH_MANAGER_ANDROID_H
