#pragma once

#ifndef CPCAPI2_EVENT_SOURCE2_H
#define CPCAPI2_EVENT_SOURCE2_H

#include <cpcapi2defs.h>
#include "impl/phone/EventSyncHandler.h"
#include "impl/util/APILogger.h"
#include "impl/util/AutoTestProcessor.h"
#include "impl/util/DumFpCommand.h"
#include "impl/util/LogSubsystems.h"

#include <rutil/Fifo.hxx>
#include <rutil/Reactor.hxx>
#include <rutil/MultiReactor.hxx>

#include <functional>
#include <memory>
#include <set>
#include <string>
#include <type_traits>

// Placeholder classes to represent no handle or filter type templates are set.
// Need to be different classes for SFINAE to work
class null_handle_t
{
private:
  null_handle_t() {}
};

class null_filter_t
{
private:
  null_filter_t() {}
};

// Helper functions for logging of events
namespace std
{
  inline std::string to_string(const char* str)
  {
    return std::string(str);
  }

  inline std::string to_string(std::string str)
  {
    return str;
  }
}

namespace CPCAPI2
{

// Interface class that PhoneInterface derives from
class EventSourcePhone
{
public:
   virtual resip::MultiReactor& getSdkModuleThread() const = 0;
   virtual void invokeCallbackHook() = 0;
};

// Wrapper around a resip FIFO which store extra state
class CallbackFifoHolder2
{
public:
   CallbackFifoHolder2(EventSourcePhone* phone) :
      mShutdown(false),
      mPhone(phone)
   {
   }

   resip::Fifo<resip::ReadCallbackBase>& callbackFifo()
   {
      return mCallbackFifo;
   }

   void invokeCallbackHook()
   {
      mPhone->invokeCallbackHook();
   }

   bool mShutdown;

private:
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   EventSourcePhone* mPhone;

};

// Macros to be used on the interface of an SDK module to pass through calls to the module to the appropriate EventHandler.
// Without these using statements, these functions are not exposed in the module's interface.
// These macros can be called multiple times on a single interface with different HandlerTypes
#define USING_EVENT_HANDLER(HandlerClass, HandleType) \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::fireEvent; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::addAppHandler; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::addSdkObserver; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::addSdkObserverSafe; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::removeAppHandler; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::removeSdkObserver; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, null_filter_t>::removeSdkObserverSafe;

#define USING_EVENT_HANDLER2(HandlerClass, HandleType, FilterType) \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::fireEvent; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::addAppHandler; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::addSdkObserver; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::addSdkObserverSafe; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::removeAppHandler; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::removeSdkObserver; \
using CPCAPI2::EventHandler<HandlerClass, HandleType, FilterType>::removeSdkObserverSafe;

// Class to track handlers and fire events for a single Handler class (and any derived handler classes ex. SipAccountHandler and SipAccountHandlerInternal)
template<class THandler, typename THandle = null_handle_t, typename TFilter = null_filter_t>
class EventHandler
{
public:
   // resip::MultiReactor& r: Reference to the SDK main thread
   // std::shared_ptr<CPCAPI2::CallbackFifoHolder2> c: The callback FIFO shared by all EventHandlers in this EventSource2 instance
   // bool loggingEnabled: Enable logging of all events fired
   // const CPCAPI2_Subsystem& loggingSubsystem: logging subsystem to used when logging events
   EventHandler(resip::MultiReactor& r, std::shared_ptr<CPCAPI2::CallbackFifoHolder2> c, bool loggingEnabled,
                const CPCAPI2_Subsystem& loggingSubsystem) :
         mReactor(r),
         mCallbackFifoHolder(c),
         mLoggingEnabled(loggingEnabled),
         mLoggingSubsystem(loggingSubsystem)
   {}

   void postCallback(resip::ReadCallbackBase* command)
   {
      mCallbackFifoHolder->callbackFifo().add(command);
      mCallbackFifoHolder->invokeCallbackHook();
   }

protected:
   typedef std::set<THandler*> AppHandlers0;
   typedef std::set<THandler*> SdkObservers0;
   typedef std::set<std::weak_ptr<THandler>, std::owner_less<std::weak_ptr<THandler>>> SdkObserversSafe0;

   typedef std::map<THandle, AppHandlers0> AppHandlers1;
   typedef std::map<THandle, SdkObservers0> SdkObservers1;
   typedef std::map<THandle, SdkObserversSafe0> SdkObserversSafe1;

   typedef std::map<std::tuple<THandle, TFilter>, AppHandlers0> AppHandlers2;
   typedef std::map<std::tuple<THandle, TFilter>, SdkObservers0> SdkObservers2;
   typedef std::map<std::tuple<THandle, TFilter>, SdkObserversSafe0> SdkObserversSafe2;

   // Erases any pending callbacks for a handler which is being removed
   // Removed requriement to drain Fifo before removing a handler
   void eraseCallbacks(THandler* handler)
   {
      // Check if this handler is still registered. Could be registered for a different handle or filter.
      // If the handler is still registered for any callbacks, it should be valid.
      // If a handler was registered multiple times with a different handles (or filters) then removed for a single handle, pending callbacks for the removed handle will not be removed.
      if (mAppHandlers0.end() != mAppHandlers0.find(handler)) return;
      if (mSdkObservers0.end() != mSdkObservers0.find(handler)) return;
      for (auto it = mSdkObserversSafe0.begin(); mSdkObserversSafe0.end() != it; ++it)
      {
         if (std::shared_ptr<THandler> h = (*it).lock())
         {
            if (h.get() == handler)
            {
               return;
            }
         }
      }

      for (auto it = mAppHandlers1.begin(); mAppHandlers1.end() != it; ++it)
      {
         if (it->second.end() != it->second.find(handler)) return;
      }
      for (auto it = mSdkObservers1.begin(); mSdkObservers1.end() != it; ++it)
      {
         if (it->second.end() != it->second.find(handler)) return;
      }
      for (auto it = mSdkObserversSafe1.begin(); mSdkObserversSafe1.end() != it; ++it)
      {
         for (auto itObs = it->second.begin(); it->second.end() != itObs; ++itObs)
         {
            if (std::shared_ptr<THandler> h = (*itObs).lock())
            {
               if (h.get() == handler)
               {
                  return;
               }
            }
         }
      }

      for (auto it = mAppHandlers2.begin(); mAppHandlers2.end() != it; ++it)
      {
         if (it->second.end() != it->second.find(handler)) return;
      }
      for (auto it = mSdkObservers2.begin(); mSdkObservers2.end() != it; ++it)
      {
         if (it->second.end() != it->second.find(handler)) return;
      }
      for (auto it = mSdkObserversSafe2.begin(); mSdkObserversSafe2.end() != it; ++it)
      {
         for (auto itObs = it->second.begin(); it->second.end() != itObs; ++itObs)
         {
            if (std::shared_ptr<THandler> h = (*itObs).lock())
            {
               if (h.get() == handler)
               {
                  return;
               }
            }
         }
      }

      // resip::Fifo has no methods to traverse and remove items not from the front.
      // Make a copy of valid callbacks then re-add them to the Fifo
      std::vector<resip::ReadCallbackBase*> callbacks;
      resip::ReadCallbackBase* fp;
      while ( (fp = mCallbackFifoHolder->callbackFifo().getNext(CPCAPI2::kBlockingModeNonBlocking)) != NULL )
      {
         resip::ReadCallbackObj<int, THandler>* cb = dynamic_cast< resip::ReadCallbackObj<int, THandler>* >(fp);
         if (NULL != cb)
         {
            if (handler != cb->object())
            {
               callbacks.push_back(fp);
            }
         }
         else
         {
            callbacks.push_back(fp);
         }
      }
      for (resip::ReadCallbackBase* rcb : callbacks)
      {
         mCallbackFifoHolder->callbackFifo().add(rcb);
      }
   }

   void addAppHandlerImpl0(THandler* handler)
   {
      mAppHandlers0.insert(handler);
   }

   void addSdkObserverImpl0(THandler* obs)
   {
      mSdkObservers0.insert(obs);
   }

   void addSdkObserverSafeImpl0(const std::shared_ptr<THandler>& obs)
   {
      mSdkObserversSafe0.insert(std::weak_ptr<THandler>(obs));
   }

   void removeAppHandlerImpl0(THandler* handler)
   {
      mAppHandlers0.erase(handler);
      eraseCallbacks(handler);
   }

   void removeSdkObserverImpl0(THandler* obs)
   {
      mSdkObservers0.erase(obs);
      eraseCallbacks(obs);
   }

   void removeSdkObserverSafeImpl0(const std::shared_ptr<THandler>& obs)
   {
      for (auto itObs = mSdkObserversSafe0.begin(); mSdkObserversSafe0.end() != itObs; ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            if (handler.get() == obs.get())
            {
               mSdkObserversSafe0.erase(itObs);
               eraseCallbacks(obs.get());
               break;
            }
         }
         else
         {
            itObs = mSdkObserversSafe0.erase(itObs);
         }
      }
   }

   void addAppHandlerImpl1(THandler* handler, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (handler)
      {
         auto itObservers = mAppHandlers1.find(handle);
         if (mAppHandlers1.end() == itObservers)
         {
            AppHandlers0 observers;
            observers.insert(handler);
            mAppHandlers1[handle] = observers;
         }
         else
         {
            itObservers->second.insert(handler);
         }
      }
   }

   void addSdkObserverImpl1(THandler* obs, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (obs)
      {
         auto itObservers = mSdkObservers1.find(handle);
         if (mSdkObservers1.end() == itObservers)
         {
            SdkObservers0 observers;
            observers.insert(obs);
            mSdkObservers1[handle] = observers;
         }
         else
         {
            itObservers->second.insert(obs);
         }
      }
   }

   void addSdkObserverSafeImpl1(const std::shared_ptr<THandler>& obs, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (obs)
      {
         auto itObservers = mSdkObserversSafe1.find(handle);
         if (mSdkObserversSafe1.end() == itObservers)
         {
            SdkObserversSafe0 observers;
            observers.insert(std::weak_ptr<THandler>(obs));
            mSdkObserversSafe1[handle] = observers;
         }
         else
         {
            itObservers->second.insert(std::weak_ptr<THandler>(obs));
         }
      }
   }

   void removeAppHandlerImpl1(THandler* handler, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (handler)
      {
         auto itObservers = mAppHandlers1.find(handle);
         if (mAppHandlers1.end() != itObservers)
         {
            itObservers->second.erase(handler);
            eraseCallbacks(handler);
            if (itObservers->second.size() == 0)
            {
               mAppHandlers1.erase(handle);
            }
         }
      }
   }

   void removeSdkObserverImpl1(THandler* obs, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (obs)
      {
         auto itObservers = mSdkObservers1.find(handle);
         if (mSdkObservers1.end() != itObservers)
         {
            itObservers->second.erase(obs);
            eraseCallbacks(obs);
            if (itObservers->second.size() == 0)
            {
               mSdkObservers1.erase(handle);
            }
         }
      }
   }

   void removeSdkObserverSafeImpl1(const std::shared_ptr<THandler>& obs, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (obs)
      {
         auto itObservers = mSdkObserversSafe1.find(handle);
         if (mSdkObserversSafe1.end() != itObservers)
         {
            for (auto itObs = itObservers->second.begin(); itObs != itObservers->second.end(); ++itObs)
            {
               if (std::shared_ptr<THandler> handler = (*itObs).lock())
               {
                  if (handler.get() == obs.get())
                  {
                     itObservers->second.erase(itObs);
                     eraseCallbacks(obs.get());
                     break;
                  }
               }
               else
               {
                  itObs = itObservers->second.erase(itObs);
               }
            }

            if (itObservers->second.size() == 0)
            {
               mSdkObservers1.erase(handle);
            }
         }
      }
   }

   void addAppHandlerImpl2(THandler* handler, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (handler)
      {
         auto itObservers = mAppHandlers2.find(std::make_tuple(handle, filter));
         if (mAppHandlers2.end() == itObservers)
         {
            AppHandlers0 observers;
            observers.insert(handler);
            mAppHandlers2[std::make_tuple(handle, filter)] = observers;
         }
         else
         {
            itObservers->second.insert(handler);
         }
      }
   }

   void addSdkObserverImpl2(THandler* obs, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (obs)
      {
         auto itObservers = mSdkObservers2.find(std::make_tuple(handle, filter));
         if (mSdkObservers2.end() == itObservers)
         {
            SdkObservers0 observers;
            observers.insert(obs);
            mSdkObservers2[std::make_tuple(handle, filter)] = observers;
         }
         else
         {
            itObservers->second.insert(obs);
         }
      }
   }

   void addSdkObserverSafeImpl2(const std::shared_ptr<THandler>& obs, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (obs)
      {
         auto itObservers = mSdkObserversSafe2.find(std::make_tuple(handle, filter));
         if (mSdkObserversSafe2.end() == itObservers)
         {
            SdkObserversSafe0 observers;
            observers.insert(std::weak_ptr<THandler>(obs));
            mSdkObserversSafe2[std::make_tuple(handle, filter)] = observers;
         }
         else
         {
            itObservers->second.insert(std::weak_ptr<THandler>(obs));
         }
      }
   }

   void removeAppHandlerImpl2(THandler* handler, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (handler)
      {
         auto itObservers = mAppHandlers2.find(std::make_tuple(handle, filter));
         if (mAppHandlers2.end() != itObservers)
         {
            itObservers->second.erase(handler);
            eraseCallbacks(handler);
            if (itObservers->second.size() == 0)
            {
               mAppHandlers2.erase(std::make_tuple(handle, filter));
            }
         }
      }
   }

   void removeSdkObserverImpl2(THandler* obs, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (obs)
      {
         auto itObservers = mSdkObservers2.find(std::make_tuple(handle, filter));
         if (mSdkObservers2.end() != itObservers)
         {
            itObservers->second.erase(obs);
            eraseCallbacks(obs);
            if (itObservers->second.size() == 0)
            {
               mSdkObservers2.erase(std::make_tuple(handle, filter));
            }
         }
      }
   }

   void removeSdkObserverSafeImpl2(const std::shared_ptr<THandler>& obs, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filtered version with non-filtered instance");
      if (obs)
      {
         auto itObservers = mSdkObserversSafe2.find(std::make_tuple(handle, filter));
         if (mSdkObserversSafe2.end() != itObservers)
         {
            for (auto itObs = itObservers->second.begin(); itObs != itObservers->second.end(); ++itObs)
            {
               if (std::shared_ptr<THandler> handler = (*itObs).lock())
               {
                  if (handler.get() == obs.get())
                  {
                     itObservers->second.erase(itObs);
                     eraseCallbacks(obs.get());
                     break;
                  }
               }
               else
               {
                  itObs = itObservers->second.erase(itObs);
               }
            }

            if (itObservers->second.size() == 0)
            {
               mSdkObserversSafe2.erase(std::make_tuple(handle, filter));
            }
         }
      }
   }

public:
   void addAppHandler(THandler* handler)
   {
      if (handler)
      {
         // addAppHandler is called in different situations currently, sometimes
         // it is already inside of the XYZImpl handler and is currently running
         // in the reactor, and sometimes it is invoked from the impl. Although I
         // don't like methods which behave differently based on thread contexts,
         // the simplest solution here is to call addAppHandlerImpl directly if
         // we're running in the reactor.
         if (mReactor.isCurrentThread())
         {
            addAppHandlerImpl0(handler);
         }
         else
         {
            mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addAppHandlerImpl0, this, handler));
         }
      }
   }

   void addSdkObserver(THandler* observer)
   {
      if (mReactor.isCurrentThread())
      {
         addSdkObserverImpl0(observer);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverImpl0, this, observer));
      }
   }

   void addSdkObserverSafe(const std::shared_ptr<THandler>& observer)
   {
      if (mReactor.isCurrentThread())
      {
         addSdkObserverSafeImpl0(observer);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverSafeImpl0, this, observer));
      }
   }

   void removeAppHandler(THandler* handler)
   {
      if (handler)
      {
         if (mReactor.isCurrentThread())
         {
            removeAppHandlerImpl0(handler);
         }
         else
         {
            // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on our reactor's thread.
            // we block so that we can guarantee that when this setAppHandler(..) method returns, the SDK will not call the existing
            // handler
            mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeAppHandlerImpl0, this, handler));
         }
      }
   }

   void removeSdkObserver(THandler* observer)
   {
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverImpl0(observer);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverImpl0, this, observer));
      }
   }

   void removeSdkObserverSafe(const std::shared_ptr<THandler>& observer)
   {
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverSafeImpl0(observer);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverSafeImpl0, this, observer));
      }
   }

   void addAppHandler(THandler* handler, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (handler)
      {
         // addAppHandler is called in different situations currently, sometimes
         // it is already inside of the XYZImpl handler and is currently running
         // in the reactor, and sometimes it is invoked from the impl. Although I
         // don't like methods which behave differently based on thread contexts,
         // the simplest solution here is to call addAppHandlerImpl directly if
         // we're running in the reactor.
         if (mReactor.isCurrentThread())
         {
            addAppHandlerImpl1(handler, handle);
         }
         else
         {
            mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addAppHandlerImpl1, this, handler, handle));
         }
      }
   }

   void addSdkObserver(THandler* observer, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (mReactor.isCurrentThread())
      {
         addSdkObserverImpl1(observer, handle);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverImpl1, this, observer, handle));
      }
   }

   void addSdkObserverSafe(const std::shared_ptr<THandler>& observer, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (mReactor.isCurrentThread())
      {
         addSdkObserverSafeImpl1(observer, handle);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverSafeImpl1, this, observer, handle));
      }
   }

   void removeAppHandler(THandler* handler, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (handler)
      {
         if (mReactor.isCurrentThread())
         {
            removeAppHandlerImpl1(handler, handle);
         }
         else
         {
            // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on our reactor's thread.
            // we block so that we can guarantee that when this setAppHandler(..) method returns, the SDK will not call the existing
            // handler
            mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeAppHandlerImpl1, this, handler, handle));
         }
      }
   }

   void removeSdkObserver(THandler* observer, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverImpl1(observer, handle);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverImpl1, this, observer, handle));
      }
   }

   void removeSdkObserverSafe(const std::shared_ptr<THandler>& observer, THandle handle)
   {
      static_assert(!std::is_same<THandle, null_handle_t>::value, "Using handle version with non-handled instance");
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverSafeImpl1(observer, handle);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverSafeImpl1, this, observer, handle));
      }
   }

   void addAppHandler(THandler* handler, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (handler)
      {
         // addAppHandler is called in different situations currently, sometimes
         // it is already inside of the XYZImpl handler and is currently running
         // in the reactor, and sometimes it is invoked from the impl. Although I
         // don't like methods which behave differently based on thread contexts,
         // the simplest solution here is to call addAppHandlerImpl directly if
         // we're running in the reactor.
         if (mReactor.isCurrentThread())
         {
            addAppHandlerImpl2(handler, handle, filter);
         }
         else
         {
            mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addAppHandlerImpl2, this, handler, handle, filter));
         }
      }
   }

   void addSdkObserver(THandler* observer, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (mReactor.isCurrentThread())
      {
         addSdkObserverImpl2(observer, handle, filter);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverImpl2, this, observer, handle, filter));
      }
   }

   void addSdkObserverSafe(const std::shared_ptr<THandler>& observer, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (mReactor.isCurrentThread())
      {
         addSdkObserverSafeImpl2(observer, handle, filter);
      }
      else
      {
         mReactor.post(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::addSdkObserverSafeImpl2, this, observer, handle, filter));
      }
   }

   void removeAppHandler(THandler* handler, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (handler)
      {
         if (mReactor.isCurrentThread())
         {
            removeAppHandlerImpl2(handler, handle, filter);
         }
         else
         {
            // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on our reactor's thread.
            // we block so that we can guarantee that when this setAppHandler(..) method returns, the SDK will not call the existing
            // handler
            mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeAppHandlerImpl2, this, handler, handle, filter));
         }
      }
   }

   void removeSdkObserver(THandler* observer, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverImpl2(observer, handle, filter);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverImpl2, this, observer, handle, filter));
      }
   }

   void removeSdkObserverSafe(const std::shared_ptr<THandler>& observer, THandle handle, TFilter filter)
   {
      static_assert(!std::is_same<TFilter, null_filter_t>::value, "Using filter version with non-filtered instance");
      if (mReactor.isCurrentThread())
      {
         removeSdkObserverSafeImpl2(observer, handle, filter);
      }
      else
      {
         mReactor.execute(resip::resip_bind(&EventHandler<THandler, THandle, TFilter>::removeSdkObserverSafeImpl2, this, observer, handle, filter));
      }
   }

protected:
   template<typename TCls, typename TFn>
   void dispatchEvent(TCls* h, std::set<TCls*>& called, TFn func)
   {
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): h: " << h);
      if (NULL != h && ((TCls*)0xDEADBEFF) != h && called.end() == called.find(h))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, h);
         if (dynamic_cast<EventSyncHandlerBase*>(h) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): h: " << h << " failed dynamic-cast to EventSyncHandler");
            postCallback(cb);
         }
         called.insert(h);
      }
      else
      {
         // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): ignoring event");
      }
   }

   template<typename TCls, typename TFn, typename TEvt>
   void dispatchEvent(TCls* h, std::set<TCls*>& called, TFn func, const TEvt& event)
   {
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): h: " << h);
      // Don't dispatch event if:
      // 1. Handler (h) is NULL
      // 2. Handler (h) is 0xDEADBEFF. 0xDEADBEFF is used in the SDK to indicate not to dispatch events. This is deprecated with EventSource2
      // 3. If the event has already been dispatched to the handler. ex. the handler was registered for a specific handle and also all handles
      if (NULL != h && ((TCls*)0xDEADBEFF) != h && called.end() == called.find(h))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, h, event);
         if (dynamic_cast<EventSyncHandlerBase*>(h) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): h: " << h << " failed dynamic-cast to EventSyncHandler");
            postCallback(cb);
         }
         called.insert(h);
      }
      else
      {
         // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): ignoring event");
      }
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void dispatchEvent(TCls* h, std::set<TCls*>& called, TFn func, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): arg: " << std::to_string(eventFirstArg).c_str() << " h: " << h);
      if (NULL != h && ((TCls*)0xDEADBEFF) != h && called.end() == called.find(h))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, h, eventFirstArg, eventSecondArg);
         if (dynamic_cast<EventSyncHandlerBase*>(h) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): arg: " << std::to_string(eventFirstArg).c_str() << " h: " << h << " failed dynamic-cast to EventSyncHandler");
            postCallback(cb);
         }
         called.insert(h);
      }
      else
      {
         // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::dispatchEvent(): ignoring event");
      }
   }

   template<typename TCls, typename TFn>
   void fireSdkEvent(std::set<TCls*>& called, const char* funcName, TFn func)
   {
      static_assert(std::is_base_of<THandler, TCls>::value, "TCls is not base of THandler");

      for (auto itObs = mSdkObserversSafe0.begin(); itObs != mSdkObserversSafe0.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            dispatchEvent(dynamic_cast<TCls*>(handler.get()), called, func);
         }
         else
         {
            // If observer has been deleted, remove it from the list
            itObs = mSdkObserversSafe0.erase(itObs);
         }
      }

      for (auto itObs = mSdkObservers0.begin(); itObs != mSdkObservers0.end(); ++itObs)
      {
         dispatchEvent(dynamic_cast<TCls*>(*itObs), called, func);
      }
   }

   template<typename TCls, typename TFn>
   void fireAppEvent(std::set<TCls*>& called, const char* funcName, TFn func)
   {
      for (auto itObs = mAppHandlers0.begin(); itObs != mAppHandlers0.end(); ++itObs)
      {
#ifdef CPCAPI2_AUTO_TEST
         if ((void*)0xDEADBEEF == *itObs)
         {
            // should be an autotest running
            resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCls*)0xDEADBEEF);
            postCallback(autoTestCb);
         }
         else
#endif
         {
            TCls* h = dynamic_cast<TCls*>(*itObs);
            dispatchEvent(h, called, func);
         }
      }
   }

   template<typename TCls, typename TFn, typename TEvt>
   void fireSdkEvent(std::set<TCls*>& called, const char* funcName, TFn func, const TEvt& event)
   {
      static_assert(std::is_base_of<THandler, TCls>::value, "TCls is not base of THandler");

      for (auto itObs = mSdkObserversSafe0.begin(); itObs != mSdkObserversSafe0.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            dispatchEvent(dynamic_cast<TCls*>(handler.get()), called, func, event);
         }
         else
         {
            // If observer has been deleted, remove it from the list
            itObs = mSdkObserversSafe0.erase(itObs);
         }
      }

      for (auto itObs = mSdkObservers0.begin(); itObs != mSdkObservers0.end(); ++itObs)
      {
         dispatchEvent(dynamic_cast<TCls*>(*itObs), called, func, event);
      }
   }

   template<typename TCls, typename TFn, typename TEvt>
   void fireAppEvent(std::set<TCls*>& called, const char* funcName, TFn func, const TEvt& event)
   {
      for (auto itObs = mAppHandlers0.begin(); itObs != mAppHandlers0.end(); ++itObs)
      {
#ifdef CPCAPI2_AUTO_TEST
         if ((void*)0xDEADBEEF == *itObs)
         {
            // should be an autotest running
            resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCls*)0xDEADBEEF, event);
            postCallback(autoTestCb);
         }
         else
#endif
         {
            TCls* h = dynamic_cast<TCls*>(*itObs);
            dispatchEvent(h, called, func, event);
         }
      }
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireSdkEvent(std::set<TCls*>& called, const char* funcName, TFn func, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      static_assert(std::is_base_of<THandler, TCls>::value, "TCls is not base of THandler");

      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireSdkEvent(): " << funcName << " arg: " << std::to_string(eventFirstArg).c_str() << " safe-observers: " << mSdkObserversSafe0.size() << " observers: " << mSdkObservers0.size());

      for (auto itObs = mSdkObserversSafe0.begin(); itObs != mSdkObserversSafe0.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            dispatchEvent(dynamic_cast<TCls*>(handler.get()), called, func, eventFirstArg, eventSecondArg);
         }
         else
         {
            // If observer has been deleted, remove it from the list
            itObs = mSdkObserversSafe0.erase(itObs);
            // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireSdkEvent(): " << funcName << " arg: " << std::to_string(eventFirstArg).c_str() << " removing safe observer as no longer valid");
         }
      }

      for (auto itObs = mSdkObservers0.begin(); itObs != mSdkObservers0.end(); ++itObs)
      {
         dispatchEvent(dynamic_cast<TCls*>(*itObs), called, func, eventFirstArg, eventSecondArg);
      }
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireAppEvent(std::set<TCls*>& called, const char* funcName, TFn func, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireAppEvent(): " << funcName << " arg: " << std::to_string(eventFirstArg).c_str() << " app-handlers: " << mAppHandlers0.size());

      for (auto itObs = mAppHandlers0.begin(); itObs != mAppHandlers0.end(); ++itObs)
      {
#ifdef CPCAPI2_AUTO_TEST
         if ((void*)0xDEADBEEF == *itObs)
         {
            // should be an autotest running
            resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCls*)0xDEADBEEF, eventFirstArg, eventSecondArg);
            postCallback(autoTestCb);
         }
         else
#endif
         {
            TCls* h = dynamic_cast<TCls*>(*itObs);
            dispatchEvent(h, called, func, eventFirstArg, eventSecondArg);
         }
      }
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireSdkEvent(std::set<TCls*>& called, const char* funcName, TFn func, const THandle handle, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      {
         auto itObserver = mSdkObserversSafe1.find(handle);
         if (itObserver != mSdkObserversSafe1.end())
         {
            for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
            {
               if (std::shared_ptr<THandler> handler = (*itObs).lock())
               {
                  dispatchEvent(dynamic_cast<TCls*>(handler.get()), called, func, eventFirstArg, eventSecondArg);
               }
               else
               {
                  // If observer has been deleted, remove it from the list
                  itObs = itObserver->second.erase(itObs);
               }
            }
         }
      }

      {
         auto itObserver = mSdkObservers1.find(handle);
         if (itObserver != mSdkObservers1.end())
         {
            for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
            {
               dispatchEvent(dynamic_cast<TCls*>(*itObs), called, func, eventFirstArg, eventSecondArg);
            }
         }
      }

      fireSdkEvent(called, funcName, func, eventFirstArg, eventSecondArg);
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireAppEvent(std::set<TCls*>& called, const char* funcName, TFn func, const THandle handle, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      auto itObserver = mAppHandlers1.find(handle);
      if (itObserver != mAppHandlers1.end())
      {
         for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
         {
   #ifdef CPCAPI2_AUTO_TEST
            if ((void*)0xDEADBEEF == *itObs)
            {
               // should be an autotest running
               resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCls*)0xDEADBEEF, eventFirstArg, eventSecondArg);
               postCallback(autoTestCb);
            }
            else
            {
               if (((*itObs) != NULL) && ((*itObs) != (TCls*)0xDEADBEFF))
               {
                  TCls* h = dynamic_cast<TCls*>(*itObs);
                  dispatchEvent(h, called, func, eventFirstArg, eventSecondArg);
               }
               else
               {
                  // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireAppEvent(): " << funcName << " arg: " << std::to_string(eventFirstArg).c_str() << " ignoring event");
               }
            }
   #else
            {
               TCls* h = dynamic_cast<TCls*>(*itObs);
               dispatchEvent(h, called, func, eventFirstArg, eventSecondArg);
            }
   #endif
         }
      }

      fireAppEvent(called, funcName, func, eventFirstArg, eventSecondArg);
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireSdkEvent(std::set<TCls*>& called, const char* funcName, TFn func, const THandle handle, const TFilter filter, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      {
         auto itObserver = mSdkObserversSafe2.find(std::make_tuple(handle, filter));
         if (itObserver != mSdkObserversSafe2.end())
         {
            for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
            {
               if (std::shared_ptr<THandler> handler = (*itObs).lock())
               {
                  dispatchEvent(dynamic_cast<TCls*>(handler.get()), called, func, eventFirstArg, eventSecondArg);
               }
               else
               {
                  // If observer has been deleted, remove it from the list
                  itObs = itObserver->second.erase(itObs);
               }
            }
         }
      }

      {
         auto itObserver = mSdkObservers2.find(std::make_tuple(handle, filter));
         if (itObserver != mSdkObservers2.end())
         {
            for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
            {
               dispatchEvent(dynamic_cast<TCls*>(*itObs), called, func, eventFirstArg, eventSecondArg);
            }
         }
      }

      fireSdkEvent(called, funcName, func, handle, eventFirstArg, eventSecondArg);
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2>
   void fireAppEvent(std::set<TCls*>& called, const char* funcName, TFn func, const THandle handle, const TFilter filter, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      auto itObserver = mAppHandlers2.find(std::make_tuple(handle, filter));
      if (itObserver != mAppHandlers2.end())
      {
         for (auto itObs = itObserver->second.begin(); itObserver->second.end() != itObs; ++itObs)
         {
   #ifdef CPCAPI2_AUTO_TEST
            if ((void*)0xDEADBEEF == *itObs)
            {
               // should be an autotest running
               resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCls*)0xDEADBEEF, eventFirstArg, eventSecondArg);
               postCallback(autoTestCb);
            }
            else
   #endif
            {
               TCls* h = dynamic_cast<TCls*>(*itObs);
               dispatchEvent(h, called, func, eventFirstArg, eventSecondArg);
            }
         }
      }

      fireAppEvent(called, funcName, func, handle, eventFirstArg, eventSecondArg);
   }

   template<typename TCls>
   void logEventTrace(const TCls* cls, const char* funcName)
   {
     if(mLoggingEnabled)
     {
       GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireEvent(): " << typeid(cls).name() << "::" << funcName);
     }
   }

   template<typename TCls, typename TEvt>
   void logEventTrace(const TCls* cls, const char* funcName, const TEvt& eventArg)
   {
     if(mLoggingEnabled)
     {
       GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): " << typeid(cls).name() << "::" << " arg1: " << eventArg);
     }
   }

   template<typename TCls, typename TEvt1, typename TEvt2>
   void logEventTrace(const TCls* cls, const char* funcName, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
     if(mLoggingEnabled)
     {
       GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): proceeding to fire " << typeid(cls).name() << "::" << funcName << " handle: " << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg);
     }
   }

   template<typename TCls, typename TEvt1, typename TEvt2>
   void logEventTrace(const TCls* cls, const char* funcName, const THandle& eventHandle, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
     if(mLoggingEnabled)
     {
       GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): " << typeid(cls).name() << "::" << funcName << " handle: " << eventHandle << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg);
     }
   }

   template<typename TCls, typename TEvt1, typename TEvt2>
   void logEventTrace(const TCls* cls, const char* funcName, const THandle& eventHandle, const TFilter& eventFilter, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
     if(mLoggingEnabled)
     {
       GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): " << typeid(cls).name() << "::" << funcName << " handle: " << eventHandle <<  " filter: " << eventFilter << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg);
     }
   }


public:
   template<typename TCls, typename TFn, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
   void fireEvent(const TCls* cls, const char* funcName, TFn func)
   {
     if (mCallbackFifoHolder->mShutdown)
     {
        if (mLoggingEnabled) GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): ignoring event " << typeid(cls).name() << "::" << funcName << " as fifo shutdown is enabled");
        return;
     }
     logEventTrace(cls, funcName);
     std::set<TCls*> fired;
     // Fire SDK observers before app observers
     fireSdkEvent(fired, funcName, func);
     fireAppEvent(fired, funcName, func);
   }

   template<typename TCls, typename TFn, typename TEvt, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
   void fireEvent(const TCls* cls, const char* funcName, TFn func, const TEvt& event)
   {
     if (mCallbackFifoHolder->mShutdown)
     {
        if (mLoggingEnabled) GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): ignoring event " << typeid(cls).name() << "::" << funcName << "arg: " << event << " as fifo shutdown is enabled");
        return;
     }
     logEventTrace(cls, funcName, event);
     std::set<TCls*> fired;
     // Fire SDK observers before app observers
     fireSdkEvent(fired, funcName, func, event);
     fireAppEvent(fired, funcName, func, event);
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
   void fireEvent(const TCls* cls, const char* funcName, TFn func, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      if (mCallbackFifoHolder->mShutdown)
      {
         if (mLoggingEnabled) GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): ignoring event " << typeid(cls).name() << "::" << funcName << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg << " as fifo shutdown is enabled");
         return;
      }
      logEventTrace(cls, funcName, eventFirstArg, eventSecondArg);
      std::set<TCls*> fired;
      // Fire SDK observers before app observers
      fireSdkEvent(fired, funcName, func, eventFirstArg, eventSecondArg);
      fireAppEvent(fired, funcName, func, eventFirstArg, eventSecondArg);
   }

   template<typename TCls, typename TFn, typename TEvt1, typename TEvt2, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
   void fireEvent(const TCls* cls, const char* funcName, TFn func, const THandle handle, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
   {
      if (mCallbackFifoHolder->mShutdown)
      {
         if (mLoggingEnabled) GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): ignoring event " << typeid(cls).name() << "::" << funcName << " handle: " << handle << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg << " as fifo shutdown is enabled");
         return;
      }
      logEventTrace(cls, funcName, handle, eventFirstArg, eventSecondArg);
      std::set<TCls*> fired;
      // Fire SDK observers before app observers
      fireSdkEvent(fired, funcName, func, handle, eventFirstArg, eventSecondArg);
      fireAppEvent(fired, funcName, func, handle, eventFirstArg, eventSecondArg);
   }

    template<typename TCls, typename TFn, typename TEvt, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
    void fireEvent(const TCls* cls, const char* funcName, TFn func, const THandle& handle, const TEvt& event)
    {
       fireEvent(cls, funcName, func, handle, handle, event);
    }

    template<typename TCls, typename TFn, typename TEvt1, typename TEvt2, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
    void fireEvent(const TCls* cls, const char* funcName, TFn func, const THandle handle, const TFilter filter, const TEvt1& eventFirstArg, const TEvt2& eventSecondArg)
    {
       if (mCallbackFifoHolder->mShutdown)
       {
          if (mLoggingEnabled) GenericLog(mLoggingSubsystem, resip::Log::Debug, << "EventSource2::fireEvent(): ignoring event " << typeid(cls).name() << "::" << funcName << " handle: " << handle << "filter: " << filter << " arg1: " << eventFirstArg << " arg2: " << eventSecondArg << " as fifo shutdown is enabled");
          return;
       }
       logEventTrace(cls, funcName, handle, filter, eventFirstArg, eventSecondArg);
       std::set<TCls*> fired;
       // Fire SDK observers before app observers
       fireSdkEvent(fired, funcName, func, handle, filter, eventFirstArg, eventSecondArg);
       fireAppEvent(fired, funcName, func, handle, filter, eventFirstArg, eventSecondArg);
    }

     template<typename TCls, typename TFn, typename TEvt, std::enable_if_t<std::is_base_of<THandler, TCls>::value, bool> = true>
     void fireEvent(const TCls* cls, const char* funcName, TFn func, const THandle& handle, const TFilter filter, const TEvt& event)
     {
        fireEvent(cls, funcName, func, handle, filter, handle, event);
     }

     bool hasHandlerRegistered()
     {
       return mAppHandlers0.size() > 0 || mSdkObservers0.size() > 0 || mSdkObserversSafe0.size() > 0 || mAppHandlers1.size() > 0 ||  mSdkObservers1.size() > 0 ||  mSdkObserversSafe1.size() > 0 || mAppHandlers2.size() > 0 || mSdkObservers2.size() > 0 || mSdkObserversSafe2.size() > 0;
     }

protected:
   resip::MultiReactor& mReactor;
   std::shared_ptr<CPCAPI2::CallbackFifoHolder2> mCallbackFifoHolder;
   bool mLoggingEnabled;
   const CPCAPI2_Subsystem& mLoggingSubsystem;

   AppHandlers0 mAppHandlers0;
   SdkObservers0 mSdkObservers0;
   SdkObserversSafe0 mSdkObserversSafe0;

   AppHandlers1 mAppHandlers1;
   SdkObservers1 mSdkObservers1;
   SdkObserversSafe1 mSdkObserversSafe1;

   AppHandlers2 mAppHandlers2;
   SdkObservers2 mSdkObservers2;
   SdkObserversSafe2 mSdkObserversSafe2;
};

// Macros for classes which define these functions on their public interfaces to prevent compile errors regarding missing functions
#ifdef CPCAPI2_AUTO_TEST
#define FORWARD_AUTO_TEST(ManagerClass) \
virtual AutoTestReadCallback* process_test(int timeout) override \
{ \
  return EventSource2::process_test(timeout);\
}
#else
#define FORWARD_AUTO_TEST(ManagerClass)
#endif


#define FORWARD_EVENT_PROCESSOR(ManagerClass) \
virtual int process(unsigned int timeout) override \
{ \
  return EventSource2::process(timeout);\
} \
\
FORWARD_AUTO_TEST(ManagerClass) \
\
virtual void interruptProcess() override \
{ \
  EventSource2::interruptProcess();\
} \

class EventSourceBase
#ifdef CPCAPI2_AUTO_TEST
                  : public AutoTestProcessor
#endif
{
public:
   // bool mLog;
   virtual ~EventSourceBase()
   {
      mCallbackFifoHolder->mShutdown = true;
      if (mThreadReactor) mThreadReactor->destroy(); // will delete
      mThreadReactor = nullptr;
   }

   virtual int process(unsigned int timeout)
   {
      // -1 == no wait
      if (mCallbackFifoHolder->mShutdown)
      {
         return CPCAPI2::kModuleDisabled;
      }
      resip::ReadCallbackBase* fp = mCallbackFifoHolder->callbackFifo().getNext(timeout);
      while (fp)
      {
         (*fp)();
         delete fp;

         if (mCallbackFifoHolder->mShutdown)
         {
            return CPCAPI2::kModuleDisabled;
         }
         fp = mCallbackFifoHolder->callbackFifo().getNext(CPCAPI2::kBlockingModeNonBlocking);
      }
      return kSuccess;
   }

#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout)
   {
      // -1 == no wait
      if (mCallbackFifoHolder->mShutdown)
      {
         return NULL;
      }
      resip::ReadCallbackBase* rcb = mCallbackFifoHolder->callbackFifo().getNext(timeout);
      AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
      if (fpCmd != NULL)
      {
         return fpCmd;
      }
      if (rcb != NULL)
      {
         return new AutoTestReadCallback(rcb, "", std::make_tuple(0, 0));
      }
      return NULL;
   }
#endif

   virtual void interruptProcess()
   {
      mSdkReactor.getAsyncProcessHandler()->handleProcessNotification();
      if (mThreadReactor) mThreadReactor->getAsyncProcessHandler()->handleProcessNotification();
   }

   std::shared_ptr<CPCAPI2::CallbackFifoHolder2> callbackFifoHolder()
   {
      return mCallbackFifoHolder;
   }

   void detachThread()
   {
     if (mThreadReactor) mThreadReactor->detach();
   }

   void executeOnSdkThread(resip::ReadCallbackBase* command)
   {
      mSdkReactor.execute(command);
   }

   void postToSdkThread(resip::ReadCallbackBase* command)
   {
      mSdkReactor.post(command);
   }

   void postMSToSdkThread(resip::ReadCallbackBase* command, int msecs)
   {
      mSdkReactor.postMS(command, msecs);
   }

   void executeOnThread(resip::ReadCallbackBase* command)
   {
      if (mThreadReactor)
      {
         mThreadReactor->execute(command);
      }
      else
      {
         mSdkReactor.execute(command);
      }
   }

   void postToThread(resip::ReadCallbackBase* command)
   {
      if (mThreadReactor)
      {
         mThreadReactor->post(command);
      }
      else
      {
         mSdkReactor.post(command);
      }
   }

   void postMSToThread(resip::ReadCallbackBase* command, int msecs)
   {
      if (mThreadReactor)
      {
         mThreadReactor->postMS(command, msecs);
      }
      else
      {
         mSdkReactor.postMS(command, msecs);
      }
   }

   void postCallback(resip::ReadCallbackBase* command)
   {
      mCallbackFifoHolder->callbackFifo().add(command);
      mCallbackFifoHolder->invokeCallbackHook();
   }

protected:
   /* EventSourceBase using an existing callback FIFO; process(..) not expected to be called on this EventSource
   */
   EventSourceBase(resip::MultiReactor& sdkThread,
               std::shared_ptr< resip::MultiReactor > thread,
               std::shared_ptr< CPCAPI2::CallbackFifoHolder2 > callbackFifoHolder,
               bool loggingEnabled = false,
               const CPCAPI2_Subsystem& loggingSubsystem = CPCAPI2_Subsystem::PHONE) :
       mSdkReactor(sdkThread),
       mThreadReactor(thread),
       mCallbackFifoHolder(callbackFifoHolder),
       mLoggingEnabled(loggingEnabled),
       mLoggingSubsystem(loggingSubsystem)
   {
   }
   std::shared_ptr<CPCAPI2::CallbackFifoHolder2> mCallbackFifoHolder;
   resip::MultiReactor& mSdkReactor;
   std::shared_ptr< resip::MultiReactor > mThreadReactor;
   bool mLoggingEnabled;
   const CPCAPI2_Subsystem& mLoggingSubsystem;
};

template<class... Handlers>
class EventSource2 : public EventSourceBase
                  , public Handlers...
{
public:
   using Handlers::fireEvent...;
   using Handlers::addAppHandler...;
   using Handlers::addSdkObserver...;
   using Handlers::addSdkObserverSafe...;
   using Handlers::removeAppHandler...;
   using Handlers::removeSdkObserver...;
   using Handlers::removeSdkObserverSafe...;

   void postCallback(resip::ReadCallbackBase* command)
   {
      EventSourceBase::postCallback(command);
   }

   template<typename TCompletionHandler, typename TCls, typename TFn> void fireCompletionHandlerEvent(TCompletionHandler* completionHandler, const TCls*, const char* funcName, TFn func)
   {
      if (EventSourceBase::mCallbackFifoHolder->mShutdown)
      {
         return;
      }

      if (EventSourceBase::mLoggingEnabled) API_EVENT("EventSource2::fireEvent(): %s ())", funcName);
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireCompletionHandlerEvent(): " << funcName);

#ifdef CPCAPI2_AUTO_TEST
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler))
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
      else
      {
         resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCompletionHandler*)0xDEADBEEF);
         postCallback(autoTestCb);
      }
#else
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
#endif
   }

   template<typename TCompletionHandler, typename TCls, typename TFn, typename TEvt> void fireCompletionHandlerEvent(TCompletionHandler* completionHandler, const TCls*, const char* funcName, TFn func, const TEvt& arg)
   {
      if (EventSourceBase::mCallbackFifoHolder->mShutdown)
      {
         return;
      }

      if (EventSourceBase::mLoggingEnabled) API_EVENT("EventSource2::fireEvent(): %s ( %s )", funcName, typeid(arg).name());
      // GenericLog(mLoggingSubsystem, resip::Log::Info, << "EventSource2::fireCompletionHandlerEvent(): " << funcName);

#ifdef CPCAPI2_AUTO_TEST
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, arg);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler))
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
      else
      {
         resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCompletionHandler*)0xDEADBEEF, arg);
         postCallback(autoTestCb);
      }
#else
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, arg);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
#endif
   }

   template<typename TCompletionHandler, typename TCls, typename TFn, typename THandle, typename TEvt> void fireCompletionHandlerEvent(TCompletionHandler* completionHandler, const TCls* cls, const char* funcName, TFn func, const THandle& eventFirstArg, const TEvt& eventSecondArg)
   {
      if (EventSourceBase::mCallbackFifoHolder->mShutdown)
      {
         return;
      }

      logEventTrace(cls, funcName, func, eventFirstArg, eventSecondArg);

#ifdef CPCAPI2_AUTO_TEST
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, eventFirstArg, eventSecondArg);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler))
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
      else
      {
         resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCompletionHandler*)0xDEADBEEF, eventFirstArg, eventSecondArg);
         postCallback(autoTestCb);
      }
#else
      if (completionHandler != NULL && (completionHandler != (TCompletionHandler*)0xDEADBEFF))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, eventFirstArg, eventSecondArg);
         if (dynamic_cast<EventSyncHandlerBase*>(completionHandler) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
#endif
   }

protected:

   /* EventSource with its own callback FIFO; process(..) should be called regularly
   */
   EventSource2(EventSourcePhone* phone, bool loggingEnabled = false, const CPCAPI2_Subsystem& loggingSubsystem = CPCAPI2_Subsystem::PHONE) :
      EventSource2(phone->getSdkModuleThread(), nullptr, std::shared_ptr< CPCAPI2::CallbackFifoHolder2 >(new CPCAPI2::CallbackFifoHolder2(phone)),
                   loggingEnabled, loggingSubsystem)
   {
   }

   /* EventSource with its own callback FIFO; process(..) should be called regularly. Creates a new thread for the module to use instead of the SDK thread.
   */
   EventSource2(EventSourcePhone* phone, std::string threadName, bool loggingEnabled = false, const CPCAPI2_Subsystem& loggingSubsystem = CPCAPI2_Subsystem::PHONE)
   {
     std::shared_ptr<resip::MultiReactor> thread = std::make_shared<resip::MultiReactor>(threadName);
     thread->start();
     EventSource2(phone->getSdkModuleThread(), thread, std::shared_ptr< CPCAPI2::CallbackFifoHolder2 >(new CPCAPI2::CallbackFifoHolder2(phone)),
                  loggingEnabled, loggingSubsystem);
   }

   /* EventSource using an existing callback FIFO; process(..) not expected to be called on this EventSource
   */
   EventSource2(resip::MultiReactor& sdkThread,
               std::shared_ptr< resip::MultiReactor > moduleThread, // Usually the SDK thread
               std::shared_ptr< CPCAPI2::CallbackFifoHolder2 > callbackFifoHolder,
               bool loggingEnabled = false,
               const CPCAPI2_Subsystem& loggingSubsystem = CPCAPI2_Subsystem::PHONE) :
       EventSourceBase(sdkThread, moduleThread, callbackFifoHolder, loggingEnabled, loggingSubsystem),
       Handlers(sdkThread, callbackFifoHolder, loggingEnabled, loggingSubsystem)...
   {
   }
};

}

#endif // CPCAPI2_EVENT_SOURCE2_H
