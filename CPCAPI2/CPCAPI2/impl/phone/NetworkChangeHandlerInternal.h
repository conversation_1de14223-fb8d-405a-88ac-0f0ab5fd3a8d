#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_HANDLER_INTERNAL_H)
#define CPCAPI2_NETWORK_CHANGE_HANDLER_INTERNAL_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "cpcstl/vector.h"
#include "phone/NetworkChangeManager.h"

namespace CPCAPI2
{

typedef cpc::vector<cpc::string> NetworkInterfaces;

struct NetworkInitializeEvent {};
struct NetworkShutdownEvent {};

struct NetworkStatusEvent
{
   NetworkTransport currentTransport;
   NetworkInterfaces currentInterfaces;
   NetworkTransport previousTransport;
   NetworkInterfaces previousInterfaces;

   NetworkStatusEvent() : currentTransport(CPCAPI2::TransportNone), previousTransport(CPCAPI2::TransportNone) {}
};

/**
 */
class NetworkChangeHandlerInternal
{
public:

   // Notifies the handler when the network change manager has been started, i.e. transports and interfaces have been initialized
   virtual void onNetworkInitializeEvent(NetworkInitializeEvent& evt) = 0;

   // Notifies the handler when the network change manager has been shutdown
   virtual void onNetworkShutdownEvent(NetworkShutdownEvent& evt) = 0;

   // Notifies the handler about the network change, including the transports and interfaces
   virtual void onNetworkStatusEvent(NetworkStatusEvent& evt) = 0;

};

}

#endif // CPCAPI2_NETWORK_CHANGE_HANDLER_INTERNAL_H
