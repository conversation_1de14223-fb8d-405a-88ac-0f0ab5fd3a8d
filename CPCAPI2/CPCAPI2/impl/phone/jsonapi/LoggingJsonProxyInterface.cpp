#include "brand_branded.h"

#if (CPCAPI2_BRAND_CALL_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "LoggingJsonProxyInterface.h"
#include "phone/PhoneInterface.h"
#include "json/JsonHelper.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define JSON_MODULE "LoggingJsonApi"

namespace CPCAPI2
{

LoggingJsonProxyInterface::LoggingJsonProxyInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mTransport(NULL),
     mHandler(NULL)
{
   mFunctionMap["log"] = std::bind(&LoggingJsonProxyInterface::handleLog, this, std::placeholders::_1);

   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());
}

LoggingJsonProxyInterface::~LoggingJsonProxyInterface()
{
}

void LoggingJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

// JsonApiClientModule
void LoggingJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int LoggingJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&LoggingJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void LoggingJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

// LoggingJsonProxy
int LoggingJsonProxyInterface::setFileLoggingEnabled(const cpc::string& id, bool enabled)
{
   post(resip::resip_bind(&LoggingJsonProxyInterface::setFileLoggingEnabledImpl, this, id, enabled));
   return kSuccess;
}

void LoggingJsonProxyInterface::setFileLoggingEnabledImpl(const cpc::string& id, bool enabled)
{
   JsonFunctionCall(mTransport, "setFileLoggingEnabled", JSON_VALUE(id), JSON_VALUE(enabled));
}

int LoggingJsonProxyInterface::setCallbackLoggingEnabled(PhoneLoggerJson* handler, bool enabled)
{
   post(resip::resip_bind(&LoggingJsonProxyInterface::setCallbackLoggingEnabledImpl, this, handler, enabled));
   return kSuccess;
}

void LoggingJsonProxyInterface::setCallbackLoggingEnabledImpl(PhoneLoggerJson* handler, bool enabled)
{
   mHandler = handler;
   JsonFunctionCall(mTransport, "setCallbackLoggingEnabled", JSON_VALUE(enabled));
}

int LoggingJsonProxyInterface::setLogLevel(CPCAPI2::LogLevel level)
{
   post(resip::resip_bind(&LoggingJsonProxyInterface::setLogLevelImpl, this, level));
   return kSuccess;
}

void LoggingJsonProxyInterface::setLogLevelImpl(CPCAPI2::LogLevel level)
{
   JsonFunctionCall(mTransport, "setLogLevel", JSON_VALUE(level));
}

int LoggingJsonProxyInterface::logLibVersions()
{
   post(resip::resip_bind(&LoggingJsonProxyInterface::logLibVersionsImpl, this));
   return kSuccess;
}

void LoggingJsonProxyInterface::logLibVersionsImpl()
{
   JsonFunctionCall(mTransport, "logLibVersions");
}

int LoggingJsonProxyInterface::handleLog(const rapidjson::Value& functionObjectVal)
{
   CPCAPI2::LogLevel level = LogLevel_None;
   /*const char *subsystem;
   const char *appName;
   const char *file;
   int line;
   const char *message;
   const char *messageWithHeaders;

	JsonDeserialize(functionObjectVal, JSON_VALUE(level), JSON_VALUE(subsystem), JSON_VALUE(appName), JSON_VALUE(file), JSON_VALUE(line), JSON_VALUE(message), JSON_VALUE(messageWithHeaders));*/

   std::string subsystem;
   std::string appName;
   std::string file;
   int line = 0;
   std::string message;
   std::string messageWithHeaders;
	JsonDeserialize(functionObjectVal, JSON_VALUE(level), JSON_VALUE(subsystem), JSON_VALUE(appName), JSON_VALUE(file), JSON_VALUE(line), JSON_VALUE(message), JSON_VALUE(messageWithHeaders));

	if (NULL != mHandler)
	{
		//post(resip::resip_bind(&PhoneLoggerJson::log, mHandler, level, subsystem, appName, file, line, message, messageWithHeaders));
		post(resip::resip_bind(&PhoneLoggerJson::log, mHandler, level, subsystem.c_str(), appName.c_str(), file.c_str(), line, message.c_str(), messageWithHeaders.c_str()));
	}
	return kSuccess;
}

} // namespace CPCAPI2
#endif
