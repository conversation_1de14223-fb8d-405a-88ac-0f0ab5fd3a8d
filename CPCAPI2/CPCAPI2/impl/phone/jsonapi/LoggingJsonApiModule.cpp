#include "brand_branded.h"

#include "interface/experimental/phone/LoggingJsonApi.h"
#include "interface/experimental/phone/LoggingJsonProxy.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "LoggingJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif
#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "LoggingJsonProxyInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   LoggingJsonApi* LoggingJsonApi::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<LoggingJsonServerInterface>(phone, "LoggingJsonA<PERSON>");
#else
         return NULL;
#endif
   }

   LoggingJsonProxy* LoggingJsonProxy::getInterface(Phone* cpcPhone)
   {
      if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<LoggingJsonProxyInterface>(phone, "LoggingJsonProxy");
#else
         return NULL;
#endif
   }
} // namespace CPCAPI2
