#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "LoggingJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define JSON_MODULE "LoggingJsonProxy"

namespace CPCAPI2
{

LoggingJsonServerInterface::LoggingJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mLoggingCallbackRegistered(false)
{
   mFunctionMap["setFileLoggingEnabled"] = std::bind(&LoggingJsonServerInterface::handleSetFileLoggingEnabled, this, std::placeholders::_1);
   mFunctionMap["setCallbackLoggingEnabled"] = std::bind(&LoggingJsonServerInterface::handleSetCallbackLoggingEnabled, this, std::placeholders::_1);
   mFunctionMap["setLogLevelImpl"] = std::bind(&LoggingJsonServerInterface::handleSetLogLevel, this, std::placeholders::_1);
   mFunctionMap["logLibVersions"] = std::bind(&LoggingJsonServerInterface::handleLogLibVersions, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

LoggingJsonServerInterface::~LoggingJsonServerInterface()
{
   if (mLoggingCallbackRegistered)
   {
      mPhone->setLoggingEnabled(this, false);
   }
}

// JsonApiServerModule
void LoggingJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void LoggingJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int LoggingJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&LoggingJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void LoggingJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

void LoggingJsonServerInterface::fireLog(CPCAPI2::LogLevel level, std::string subsystem, std::string appName, std::string file, int line, std::string message, std::string messageWithHeaders)
{
  JsonFunctionCall(mTransport, "log", JSON_VALUE(level), JSON_VALUE(subsystem), JSON_VALUE(appName), JSON_VALUE(file), JSON_VALUE(line), JSON_VALUE(message), JSON_VALUE(messageWithHeaders));
}

// Phone Logger
bool LoggingJsonServerInterface::operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file, int line, const char *message, const char *messageWithHeaders)
{
   // This is called on the logger thread and the JSON must be sent on the SDK thread
   post(resip::resip_bind(&LoggingJsonServerInterface::fireLog, this, level, std::string(subsystem), std::string(appName), std::string(file), line, std::string(message), std::string(messageWithHeaders)));
   return true;
}

// LoggingJsonProxy
int LoggingJsonServerInterface::handleSetFileLoggingEnabled(const rapidjson::Value& functionObjectVal)
{
   bool enabled = false;
   cpc::string id;
   JsonDeserialize(functionObjectVal, JSON_VALUE(id), JSON_VALUE(enabled));
   mPhone->setLoggingEnabled(id, enabled);
   return kSuccess;
}

int LoggingJsonServerInterface::handleSetCallbackLoggingEnabled(const rapidjson::Value& functionObjectVal)
{
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(enabled));
   mPhone->setLoggingEnabled(this, enabled);
   mLoggingCallbackRegistered = enabled;
   return kSuccess;
}

int LoggingJsonServerInterface::handleSetLogLevel(const rapidjson::Value& functionObjectVal)
{
   CPCAPI2::LogLevel level = LogLevel_None;
   JsonDeserialize(functionObjectVal, JSON_VALUE(level));
   mPhone->setLogLevel(level);

   return kSuccess;
}

int LoggingJsonServerInterface::handleLogLibVersions(const rapidjson::Value& functionObjectVal)
{
   mPhone->logLibVersions();
   return kSuccess;
}

} // namespace CPCAPI2

#endif
