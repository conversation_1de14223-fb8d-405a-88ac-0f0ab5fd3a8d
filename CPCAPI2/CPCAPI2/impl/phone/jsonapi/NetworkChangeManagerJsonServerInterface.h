#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_INTERFACE_H)
#define CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/phone/NetworkChangeManagerJsonApi.h"
#include "interface/public/phone/NetworkChangeManager.h"
#include "interface/public/phone/NetworkChangeHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"
#include "phone/NetworkChangeManagerImpl.h"


#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
class NetworkChangeManagerJsonServerInterface : public CPCAPI2::NetworkChangeManagerJsonApi,
                                           public CPCAPI2::EventSyncHandler<CPCAPI2::NetworkChangeHandler>,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   NetworkChangeManagerJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~NetworkChangeManagerJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   
   // NetworkChangeHandler
   virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleStart(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStop(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleGetNetworkTransport(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::NetworkChangeManagerInterface* mNetworkChangeManagerIf;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
};
}
#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_JSON_INTERFACE_H
