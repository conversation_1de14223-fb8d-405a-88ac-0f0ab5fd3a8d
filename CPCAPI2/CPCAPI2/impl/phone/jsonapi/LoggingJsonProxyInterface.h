#pragma once

#if !defined(CPCAPI2_LOGGING_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_LOGGING_JSON_PROXY_INTERFACE_H

#include "interface/experimental/phone/LoggingJsonProxy.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>

namespace CPCAPI2
{
class PhoneInterface;

class LoggingJsonProxyInterface : public CPCAPI2::LoggingJsonProxy,
                                  public CPCAPI2::JsonApi::JsonApiClientModule,
                                  public CPCAPI2::PhoneModule
{
public:
   LoggingJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~LoggingJsonProxyInterface();

   // PhoneModule
   virtual void Release() OVERRIDE {};

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // LoggingJsonProxy
   virtual int setFileLoggingEnabled(const cpc::string& id, bool enabled) OVERRIDE;
   virtual int setCallbackLoggingEnabled(PhoneLoggerJson *logger, bool enabled) OVERRIDE;
   virtual int setLogLevel(CPCAPI2::LogLevel level) OVERRIDE;
   virtual int logLibVersions() OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);

   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   // LoggingJsonProxy
   void setFileLoggingEnabledImpl(const cpc::string& id, bool enabled);
   void setCallbackLoggingEnabledImpl(PhoneLoggerJson *logger, bool enabled);
   void setLogLevelImpl(CPCAPI2::LogLevel level);
   void logLibVersionsImpl();

   int handleLog(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   PhoneLoggerJson* mHandler;
}; // class CPCAPI2

} // namespace CPCAPI2
#endif // CPCAPI2_LOGGING_JSON_PROXY_INTERFACE_H
