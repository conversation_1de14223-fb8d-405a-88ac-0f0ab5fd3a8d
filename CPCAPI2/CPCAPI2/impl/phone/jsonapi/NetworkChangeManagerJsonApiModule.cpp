#include "brand_branded.h"

#include <interface/experimental/phone/NetworkChangeManagerJsonApi.h>

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "NetworkChangeManagerJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
      NetworkChangeManagerJsonApi* NetworkChangeManagerJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<NetworkChangeManagerJsonServerInterface>(phone, "NetworkChangeManagerJsonApi");
#else
         return NULL;
#endif
      }
}
