#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "NetworkChangeManagerJsonServerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE
#define JSON_MODULE "NetworkChangeManagerManagerJsonProxy"

namespace CPCAPI2
{
   NetworkChangeManagerJsonServerInterface::NetworkChangeManagerJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mNetworkChangeManagerIf((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone))
{
   mFunctionMap["start"] = std::bind(&NetworkChangeManagerJsonServerInterface::handleStart, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stop"] = std::bind(&NetworkChangeManagerJsonServerInterface::handleStop, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["getNetworkTransport"] = std::bind(&NetworkChangeManagerJsonServerInterface::handleGetNetworkTransport, this, std::placeholders::_1, std::placeholders::_2);
 
   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
   mNetworkChangeManagerIf->addSdkObserver(this);
}

   NetworkChangeManagerJsonServerInterface::~NetworkChangeManagerJsonServerInterface()
{
   mNetworkChangeManagerIf->removeSdkObserver(this);
}

void NetworkChangeManagerJsonServerInterface::Release()
{
}

void NetworkChangeManagerJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void NetworkChangeManagerJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int NetworkChangeManagerJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&NetworkChangeManagerJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

int NetworkChangeManagerJsonServerInterface::onNetworkChange(const NetworkChangeEvent& args)
{
   JsonFunctionCall(mTransport, "onNetworkChange", "networkTransport", args.networkTransport);
   return kSuccess;
}

void NetworkChangeManagerJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   DebugLog(<< "NetworkChangeManagerJsonServerInterface handling " << funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int NetworkChangeManagerJsonServerInterface::handleStart(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mNetworkChangeManagerIf->start();
   return kSuccess;
}

int NetworkChangeManagerJsonServerInterface::handleStop(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mNetworkChangeManagerIf->stop();
   return kSuccess;
}

int NetworkChangeManagerJsonServerInterface::handleGetNetworkTransport(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   JsonFunctionCall(mTransport, "getNetworkTransportResult", "networkTransport", mNetworkChangeManagerIf->networkTransport());
   return kSuccess;
}

}
#endif
