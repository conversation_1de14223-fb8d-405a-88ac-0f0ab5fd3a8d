#pragma once

#if !defined(CPCAPI2_LOGGING_JSON_INTERFACE_H)
#define CPCAPI2_LOGGING_JSON_INTERFACE_H

#include "interface/public/phone/Phone.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/phone/LoggingJsonApi.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

#include <string>

namespace CPCAPI2
{
class PhoneInterface;

class LoggingJsonServerInterface : public CPCAPI2::PhoneLogger,
                                   public CPCAPI2::LoggingJsonApi,
                                   public CPCAPI2::JsonApi::JsonApiServerModule,
                                   public CPCAPI2::PhoneModule
{
public:
   LoggingJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~LoggingJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE {};

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // PhoneLogger
   virtual bool operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file, int line, const char *message, const char *messageWithHeaders) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   void fireLog(CPCAPI2::LogLevel level, std::string subsystem, std::string appName, std::string file, int line, std::string message, std::string messageWithHeaders);

   // LoggingJsonProxy
   int handleSetFileLoggingEnabled(const rapidjson::Value& functionObjectVal);
   int handleSetCallbackLoggingEnabled(const rapidjson::Value& functionObjectVal);
   int handleSetLogLevel(const rapidjson::Value& functionObjectVal);
   int handleLogLibVersions(const rapidjson::Value& functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   bool mLoggingCallbackRegistered;

}; // class LoggingJsonServerInterface
} // namespace CPCAPIe
#endif // CPCAPI2_LOGGING_JSON_INTERFACE_H
