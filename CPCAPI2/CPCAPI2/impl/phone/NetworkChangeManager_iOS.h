#pragma once

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_IOS_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_IOS_H

#if defined( __APPLE__ )
#include "TargetConditionals.h"
#if TARGET_OS_IPHONE

#import <SystemConfiguration/SystemConfiguration.h>
#import <sys/socket.h>
#import <netinet/in.h>
#include "NetworkChangeManagerImpl.h"
#include <list>

namespace CPCAPI2
{
class NetworkChangeManager_iOS : public NetworkChangeManagerImpl, public std::enable_shared_from_this<NetworkChangeManager_iOS>
{
public:
   static NetworkChangeManager_iOS* createInstance(Phone* phone);
   virtual ~NetworkChangeManager_iOS();

   virtual int start();
   virtual int stop();

   virtual NetworkTransport networkTransport() const;
   virtual cpc::string wwanType() const;

   NetworkTransport getCurrentReachabilityStatus() const;

private:
   NetworkChangeManager_iOS(Phone* phone);
   NetworkChangeManager_iOS();
   virtual void release() OVERRIDE;
   
   NetworkTransport networkStatusForFlags(SCNetworkReachabilityFlags flags) const;
   static void ReachabilityCallback(SCNetworkReachabilityRef target, SCNetworkReachabilityFlags flags, void* info);
   void checkReachability(const SCNetworkReachabilityFlags *flags);
   void printReachabilityFlags(SCNetworkReachabilityFlags flags) const;
   void checkInterfaces();
   void initializeAddressList();
   void getWiFiAddressList(std::list<resip::Data>& addrList);

   SCNetworkReachabilityRef mReachabilityRef;
   NetworkTransport mNetworkTransport;
   dispatch_queue_t mDispatchQueue;
   std::list<resip::Data> mWifiAddrs;
   std::shared_ptr<NetworkChangeManager_iOS> mSharedThis;
};
}

#endif // #if TARGET_OS_IPHONE
#endif // #if defined( __APPLE__ )
#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_IOS_H
