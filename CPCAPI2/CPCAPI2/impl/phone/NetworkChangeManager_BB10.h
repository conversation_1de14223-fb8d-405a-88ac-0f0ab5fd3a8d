#pragma once

// Incomplete and untested network change manager for BB10
// Code was adapted from the BB10 client

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_BB10_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_BB10_H

#if defined(BB10)

#include "NetworkChangeManagerImpl.h"

#include "../util/cpc_thread.h"

#include <set>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
class NetworkChangeManager_BB10 : public NetworkChangeManagerImpl
{
public:
    NetworkChangeManager_BB10(Phone* phone);
    virtual ~NetworkChangeManager_BB10();

    virtual int start();
    virtual int stop();

private:
    void pollInterfacesThread();

private:
    NetworkChangeManager_BB10();

    thread* mPollingThread;
};
}

#endif // BB10
#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_BB10_H
