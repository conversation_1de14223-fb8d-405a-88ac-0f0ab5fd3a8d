#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"

namespace jsonrpc
{
namespace CPCAPI2
{
namespace Phone
{
class PhoneCompatibility : public jsonrpc::CPCAPI2::Phone::IPhone
{
public:
	PhoneCompatibility(std::shared_ptr<::CPCAPI2::PhoneInstances> phones);

	virtual int64_t create() override;

	virtual void release(const int64_t handle) override;

	virtual cpc::string getVersion() override;

	virtual void initialize(const int64_t handle, const jsonrpc::CPCAPI2::Phone::LicenseInfo& licenseInfo, const bool useNetworkChangeManager, const std::optional<jsonrpc::CPCAPI2::Phone::SslCipherOptions>& ciphers) override;

	virtual void setLoggingEnabled(const int64_t handle, const bool enabled) override;

	virtual void setFileLoggingEnabled(const int64_t handle, const bool enabled, const std::optional<cpc::string>& id) override;

	virtual void setLogDirectory(const int64_t handle, const cpc::string& directory) override;

	virtual void setLogLevel(const int64_t handle, const LogLevel level) override;

	virtual cpc::string getInstanceId(const int64_t handle) override;

	virtual int64_t createEventQueue(const int64_t handle, const std::optional< cpc::vector<cpc::string> >& events) override;

	virtual void destroyEventQueue(const int64_t handle, const int64_t eventQueueHandle) override;

	virtual jsonrpc::CPCAPI2::Phone::PhoneEvent getEvent(const int64_t handle, const int64_t eventQueueHandle, const int64_t timeout) override;

protected:
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
}; // class PhoneCompatibility
} // namepace Phone
} // namepace CPCAPI2
} // namepace jsonrpc
