#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "NetworkChangeManagerCompatibility.h"
#include "interface/public/phone/NetworkChangeManager.h"
// #include "interface/public/phone/Phone.h"
// #include "interface/experimental/phone/PhoneInternal.h"
#include "jsonrpccxx/common.hpp"

namespace jsonrpc
{
namespace CPCAPI2
{
namespace NetworkChangeManager
{

NetworkChangeManagerCompatibility::NetworkChangeManagerCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances> &phones)
{
   mPhones = phones;
}


NetworkChangeManager::NetworkTransport convert(const ::CPCAPI2::NetworkTransport v)
{
    switch (v)
    {
        case ::CPCAPI2::NetworkTransport::TransportNone:
            return NetworkChangeManager::NetworkTransport::None;
        case ::CPCAPI2::NetworkTransport::TransportWiFi:
            return NetworkChangeManager::NetworkTransport::Wifi;
        case ::CPCAPI2::NetworkTransport::TransportWWAN:
            return NetworkChangeManager::NetworkTransport::WWan;
    }
}

NetworkTransport NetworkChangeManagerCompatibility::networkTransport(const int64_t handle) {
    return convert(getInstance(handle)->networkTransport());
}

::CPCAPI2::NetworkChangeManager* NetworkChangeManagerCompatibility::getInstance(const int64_t phoneHandle)
{
   return ::CPCAPI2::NetworkChangeManager::getInterface(mPhones->get(phoneHandle));
}
} // namepace NetworkChangeManager
} // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE