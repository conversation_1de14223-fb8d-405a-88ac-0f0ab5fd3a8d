#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/NetworkChangeManager/server/INetworkChangeManager.h"

namespace CPCAPI2
{
class NetworkChangeManager;
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace NetworkChangeManager
{


class NetworkChangeManagerCompatibility : public jsonrpc::CPCAPI2::NetworkChangeManager::INetworkChangeManager
{
public:
	NetworkChangeManagerCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);
	virtual NetworkTransport networkTransport(const int64_t handle) override;
private:
	::CPCAPI2::NetworkChangeManager* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
}; // class NetworkChangeManagerCompatibility
} // namepace NetworkChangeManager
} // namepace CPCAPI2
} // namepace jsonrpc
