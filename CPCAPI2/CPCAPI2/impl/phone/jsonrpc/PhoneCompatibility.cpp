#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "PhoneCompatibility.h"
#include "interface/public/phone/Phone.h"
#include "interface/experimental/phone/PhoneInternal.h"
#include "jsonrpccxx/common.hpp"

namespace jsonrpc
{
namespace CPCAPI2
{
namespace Phone
{

PhoneCompatibility::PhoneCompatibility(std::shared_ptr<::CPCAPI2::PhoneInstances> phones)
{
    mPhones = phones;
}

int64_t PhoneCompatibility::create()
{
    return mPhones->add(::CPCAPI2::Phone::create());
}

::CPCAPI2::TLSVersion convert(const TlsVersion v)
{
    switch (v)
    {
        case TlsVersion::Default:
            return ::CPCAPI2::TLSVersion::TLS_DEFAULT;
        case TlsVersion::None:
            return ::CPCAPI2::TLSVersion::TLS_NONE;
        case TlsVersion::V1_0:
            return ::CPCAPI2::TLSVersion::TLS_V1_0;
        case TlsVersion::V1_1:
            return ::CPCAPI2::TLSVersion::TLS_V1_1;
        case TlsVersion::V1_2:
            return ::CPCAPI2::TLSVersion::TLS_V1_2;
        case TlsVersion::V1_3:
            return ::CPCAPI2::TLSVersion::TLS_V1_3;
        case TlsVersion::Highest:
            return ::CPCAPI2::TLSVersion::TLS_HIGHEST;
        case TlsVersion::NonDeprecated:
            return ::CPCAPI2::TLSVersion::TLS_NON_DEPRECATED;
        default:
            return ::CPCAPI2::TLSVersion::TLS_NON_DEPRECATED;
    }
}

::CPCAPI2::LogLevel convert(const LogLevel l)
{
    switch (l)
    {
        case LogLevel::None:
            return ::CPCAPI2::LogLevel::LogLevel_None;
        case LogLevel::Error:
            return ::CPCAPI2::LogLevel::LogLevel_Error;
        case LogLevel::Warning:
            return ::CPCAPI2::LogLevel::LogLevel_Warning;
        case LogLevel::Info:
            return ::CPCAPI2::LogLevel::LogLevel_Info;
        case LogLevel::Debug:
            return ::CPCAPI2::LogLevel::LogLevel_Debug;
        case LogLevel::Max:
            return ::CPCAPI2::LogLevel::LogLevel_Max;
        default:
            return ::CPCAPI2::LogLevel::LogLevel_None;
    }
}

void PhoneCompatibility::release(const int64_t handle)
{
    ::CPCAPI2::Phone* p = mPhones->get(handle);
    mPhones->remove(handle);
    ::CPCAPI2::Phone::release(p);
}

cpc::string PhoneCompatibility::getVersion()
{
    return ::CPCAPI2::Phone::getVersion().c_str();
}

void PhoneCompatibility::PhoneCompatibility::initialize(const int64_t handle, const jsonrpc::CPCAPI2::Phone::LicenseInfo& licenseInfo, const bool useNetworkChangeManager, const std::optional<jsonrpc::CPCAPI2::Phone::SslCipherOptions>& ciphers)
{
    ::CPCAPI2::LicenseInfo li;
    if (licenseInfo.licenseKey)
    {
        li.licenseKey = licenseInfo.licenseKey->c_str();
    }
    if (licenseInfo.licenseDocumentLocation)
    {
        li.licenseDocumentLocation = licenseInfo.licenseDocumentLocation->c_str();
    }
    if (licenseInfo.licenseAor)
    {
        li.licenseAor = licenseInfo.licenseAor->c_str();
    }
    
    ::CPCAPI2::SslCipherOptions c;
    if (ciphers && ciphers->mHttpCiphers)
    {
        c.setCiphers(::CPCAPI2::SslCipherUsageHttp, ciphers->mHttpCiphers->c_str());
    }
    if (ciphers && ciphers->mSipCiphers)
    {
        c.setCiphers(::CPCAPI2::SslCipherUsageSip, ciphers->mSipCiphers->c_str());
    }
    if (ciphers && ciphers->mXmppCiphers)
    {
        c.setCiphers(::CPCAPI2::SslCipherUsageXmpp, ciphers->mXmppCiphers->c_str());
    }
    if (ciphers && ciphers->mWebSocketsCiphers)
    {
        c.setCiphers(::CPCAPI2::SslCipherUsageWebSockets, ciphers->mWebSocketsCiphers->c_str());
    }
    if (ciphers && ciphers->mDtlsSrtpCiphers)
    {
        c.setCiphers(::CPCAPI2::SslCipherUsageDtlsSrtp, ciphers->mDtlsSrtpCiphers->c_str());
    }
    if (ciphers && ciphers->mHttpTlsVersion)
    {
        c.setTLSVersion(::CPCAPI2::SslCipherUsageHttp, convert(*(ciphers->mHttpTlsVersion)));
    }
    if (ciphers && ciphers->mSipTlsVersion)
    {
        c.setTLSVersion(::CPCAPI2::SslCipherUsageSip, convert(*(ciphers->mSipTlsVersion)));
    }
    if (ciphers && ciphers->mXmppTlsVersion)
    {
        c.setTLSVersion(::CPCAPI2::SslCipherUsageXmpp, convert(*(ciphers->mXmppTlsVersion)));
    }
    if (ciphers && ciphers->mWebSocketsTlsVersion)
    {
        c.setTLSVersion(::CPCAPI2::SslCipherUsageWebSockets, convert(*(ciphers->mWebSocketsTlsVersion)));
    }
    if (ciphers && ciphers->mDtlsSrtpTlsVersion)
    {
        c.setTLSVersion(::CPCAPI2::SslCipherUsageDtlsSrtp, convert(*(ciphers->mDtlsSrtpTlsVersion)));
    }

    mPhones->get(handle)->initialize(li, NULL, c, useNetworkChangeManager);
}

void PhoneCompatibility::setLoggingEnabled(const int64_t handle, const bool enabled)
{
    mPhones->get(handle)->setLoggingEnabled(NULL, enabled);
}

void PhoneCompatibility::setFileLoggingEnabled(const int64_t handle, bool enabled, const std::optional<cpc::string>& id)
{
    if (id)
    {
        mPhones->get(handle)->setLoggingEnabled(id->c_str(), enabled);
    }
    else
    {
        mPhones->get(handle)->setLoggingEnabled("", enabled);
    }
}

void PhoneCompatibility::setLogDirectory(const int64_t handle, const cpc::string& directory)
{
    mPhones->get(handle)->setLogDirectory(directory.c_str());
}

void PhoneCompatibility::setLogLevel(const int64_t handle, const LogLevel level)
{
    mPhones->get(handle)->setLogLevel(convert(level));
}

cpc::string PhoneCompatibility::getInstanceId(const int64_t handle)
{
    return mPhones->get(handle)->getInstanceId().c_str();
}

int64_t PhoneCompatibility::createEventQueue(const int64_t handle, const std::optional< cpc::vector<cpc::string> >& events)
{
    if (events)
    {
        return static_cast<::CPCAPI2::PhoneInternal*>(mPhones->get(handle))->createEventQueue(*events);
    }
    else
    {
        return static_cast<::CPCAPI2::PhoneInternal*>(mPhones->get(handle))->createEventQueue(cpc::vector<cpc::string>());
    }
}

void PhoneCompatibility::destroyEventQueue(const int64_t handle, const int64_t eventQueueHandle)
{
    static_cast<::CPCAPI2::PhoneInternal*>(mPhones->get(handle))->destroyEventQueue(eventQueueHandle);
}

jsonrpc::CPCAPI2::Phone::PhoneEvent PhoneCompatibility::getEvent(const int64_t handle, const int64_t eventQueueHandle, const int64_t timeout)
{
    ::CPCAPI2::SdkEvent sevt = static_cast<::CPCAPI2::PhoneInternal*>(mPhones->get(handle))->getEvent(eventQueueHandle, timeout);

    ::jsonrpc::CPCAPI2::Phone::PhoneEvent evt;
    evt.eventType = sevt.type().c_str();
    evt.eventPayload = sevt.json().c_str();

    return evt;
}
} // namepace Phone
} // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE