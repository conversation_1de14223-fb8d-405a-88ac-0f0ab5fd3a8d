#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_MANAGER_MOCK_H)
#define CPCAPI2_NETWORK_CHANGE_MANAGER_MOCK_H

#include "NetworkChangeManagerImpl.h"
#include "NetworkChangePollingImpl.h"
#include "NetworkChangeHandlerInternal.h"

#include "../util/cpc_thread.h"

#include <set>
#include <rutil/Data.hxx>
#include <rutil/Mutex.hxx>

namespace CPCAPI2
{

class CPCAPI2_SHAREDLIBRARY_API NetworkChangeManager_Mock : public NetworkChangeManagerImpl
{

public:

   NetworkChangeManager_Mock(Phone* phone, NetworkTransport transport = NetworkTransport::TransportWiFi);
   virtual ~NetworkChangeManager_Mock();
   virtual void release() OVERRIDE;

   virtual int start() OVERRIDE;
   virtual int stop() OVERRIDE;
   virtual NetworkTransport networkTransport() const OVERRIDE;

   virtual int addObserver(NetworkChangeHandlerInternal* observer) OVERRIDE;
   virtual int removeObserver(NetworkChangeHandlerInternal* observer) OVERRIDE;
   virtual int addPollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE;
   virtual int removePollingHandler(NetworkChangePollingHandlerInternal* handler) OVERRIDE;
   virtual int setPollingConfig(const NetworkChangePollingConfig& config) OVERRIDE;
   virtual NetworkChangeManagerInterfaceSet getLocalIPAddresses_NonStatic(bool includeLoopback) OVERRIDE;

   void setNetworkTransport(NetworkTransport t);
   void setMockInterfaces(const std::set<resip::Data>& mockInterfaces, std::set<resip::Data>* prevMockInterfaces = NULL);

   // NetworkChangePollingHandler
   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) OVERRIDE;
   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) OVERRIDE;

   static bool isSecureDnsActive();
   static void notifyPrivateDnsChanged(bool secureDns);

   void setSendNetworkChangeEventImminentHook(std::function<void()> fn);

protected:

   virtual void sendNetworkChangeEvent(const NetworkChangeEvent& event) OVERRIDE;

private:

   NetworkChangeManager_Mock();

   std::shared_ptr<NetworkChangePollingImpl> mPollingImpl;
   typedef std::set<NetworkChangeHandlerInternal*> HandlersSet;
   HandlersSet mInternalHandlers;

   resip::RecursiveMutex mMockInterfacesMutex;
   NetworkTransport mMockTransport;
   NetworkChangeManagerInterfaceSet mMockInterfaces;
   NetworkChangeManagerInterfaceSet mPrevMockInterfaces;
   static bool mLastDnsSecure;
   std::function<void()> mSendNetworkChangeEventImminentHook;

};

}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_MOCK_H
