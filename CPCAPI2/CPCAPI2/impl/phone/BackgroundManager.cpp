#include "BackgroundManager.h"

#include <chrono>
#ifdef __cpp_lib_string_view
#include <string_view>
#else
#include <string>
#endif
#include <thread>

#include "impl/util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{

BackgroundManager* BackgroundManager::gInstance = nullptr;

ResipReactorSelectHandler::ResipReactorSelectHandler(BackgroundManager* p, resip::MultiReactor* r) :
      parent(p),
      reactor(r)
{
  // Register as the select timeout handler to get notified when reactor goes to sleep
  reactor->setSelectTimeoutHandler(this, 1);
}

ResipReactorSelectHandler::~ResipReactorSelectHandler()
{
  // Clear registration
  reactor->setSelectTimeoutHandler(NULL, 1);
}

// Reactor is about to go to sleep for x milliseconds so update entry for this reactor
void ResipReactorSelectHandler::onSelect(unsigned int milliseconds)
{
  parent->onResipReactorSelect(reactor, milliseconds);
}

BackgroundManager* BackgroundManager::Instance()
{
  if (nullptr == gInstance)
  {
    gInstance = new BackgroundManager();
  }
  return gInstance;
}

void BackgroundManager::start()
{
  if (nullptr == mThread)
  {
    mShutdown = false;
    mThread = new std::thread(&BackgroundManager::thread, this);
  }
}

void BackgroundManager::stop()
{
  if (nullptr != mThread)
  {
    if (!mShutdown)
    {
      {
        std::unique_lock<std::mutex> lock(mLock);
        mShutdown = true;
        mBgThreadWork = true;
      }
      mInterrupt.notify_all();
      try
      {
         mThread->join();
      }
      catch (const std::system_error&)
      {
         // thread might already have exited
      }
    }
    delete mThread;
    mThread = nullptr;
  }
}

WakeLockHandle BackgroundManager::acquireWakeLock()
{
  std::lock_guard<std::mutex> lock(mLock);
  mWakeLocks.insert(mNextWakeLockHandle);
  return mNextWakeLockHandle++;
}

void BackgroundManager::releaseWakeLock(WakeLockHandle handle)
{
  std::lock_guard<std::mutex> lock(mLock);
  mWakeLocks.erase(handle);
}

void BackgroundManager::addResipReactor(resip::MultiReactor* reactor)
{
  DebugLog(<< "BackgroundManager::addReactor(): " << reactor);
  std::lock_guard<std::mutex> lock(mLock);
  if (mResipReactors.end() == mResipReactors.find(reactor))
  {
    mResipReactors[reactor] = 0;
    mResipReactorHandlers[reactor] = new ResipReactorSelectHandler(this, reactor);
    mBgThreadWork = true;
    mInterrupt.notify_all();
  }
}

void BackgroundManager::removeResipReactor(resip::MultiReactor* reactor)
{
  DebugLog(<< "BackgroundManager::removeReactor(): " << reactor);
  std::lock_guard<std::mutex> lock(mLock);
  if (mResipReactorHandlers.end() != mResipReactorHandlers.find(reactor))
  {
    delete mResipReactorHandlers[reactor];
    mResipReactorHandlers.erase(reactor);
    mResipReactors.erase(reactor);
    mBgThreadWork = true;
    mInterrupt.notify_all();
  }
}

void BackgroundManager::onResipReactorSelect(resip::MultiReactor* reactor, unsigned int milliseconds)
{
  if (mResipReactorSelectHook)
  {
    bool exitEarly = mResipReactorSelectHook();
    if (exitEarly)
    {
      return;
    }
  }
  std::lock_guard<std::mutex> lock(mLock);
  mLastSelect = std::chrono::system_clock::now();
  std::chrono::milliseconds diff = std::chrono::duration_cast<std::chrono::milliseconds>(mLastSelect - mLastProcess);
  mResipReactors[reactor] = milliseconds + diff.count();
  //DebugLog(<< "BackgroundManager::onResipReactorSelect(): " << reactor << " diff " << diff.count() << " " << mResipReactors[reactor] << "ms");
  mBgThreadWork = true;
  mInterrupt.notify_all();
}

void BackgroundManager::onResume()
{
  //DebugLog(<< "BackgroundManager::onResume()");
  std::lock_guard<std::mutex> lock(mLock);
  for (auto it = mResipReactors.begin(); it != mResipReactors.end(); it++)
  {
    it->first->interruptSelect();
  }
}

void BackgroundManager::thread()
{
  mLastProcess = std::chrono::system_clock::now();
  int64_t timeout = INT_MAX;
  std::unique_lock<std::mutex> lock(mLock); // unlocked by wait_for

  while (!mShutdown)
  {
    timeout = INT_MAX;  // How long in milliseconds until the next thread wants to do work
    std::chrono::milliseconds diff = std::chrono::duration_cast<std::chrono::milliseconds>(mLastSelect - mLastProcess);
    mLastProcess = std::chrono::system_clock::now();

    for (auto it = mResipReactors.begin(); it != mResipReactors.end(); it++)
    {
       it->second -= diff.count();
       if (mHandler != nullptr)
       {
          timeout = std::min(timeout, it->second);
          if (it->second > 0 && mWakeLocks.empty())
          {
             //DebugLog(<< "BackgroundManager::thread(): INACTIVE: sleep for " << timeout << "ms (" << std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mLastSelect).count() << "ms)");
             mHandler->onInactive(timeout);
          }
          else
          {
             //DebugLog(<< "BackgroundManager::thread(): ACTIVE (" << std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mLastSelect).count() << "ms)");
             mHandler->onActive();
          }
       }
    }
    mInterrupt.wait(lock, [this]{return mBgThreadWork;});
    mBgThreadWork = false;
  }
}

void BackgroundManager::setResipReactorSelectHook(bool(*hook)())
{
   mResipReactorSelectHook = hook;
}

} // namespace CPCAPI2
