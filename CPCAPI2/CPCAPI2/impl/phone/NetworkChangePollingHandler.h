#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_POLLING_HANDLER_H)
#define CPCAPI2_NETWORK_CHANGE_POLLING_HANDLER_H

#include "cpcapi2defs.h"
#include "phone/NetworkChangeManager.h"
#include "NetworkChangeHandlerInternal.h"
#include <set>

namespace CPCAPI2
{

typedef std::set<std::string> NetworkInterfaceSet;

struct NetworkInterfaceQueryEvent
{
   NetworkInterfaceSet interfaces;
   NetworkTransport transport;

   NetworkInterfaceQueryEvent() : transport(CPCAPI2::TransportNone) {}
   virtual~ NetworkInterfaceQueryEvent() {}
};

struct NetworkChangePublishEvent
{
   NetworkInterfaceSet interfaces;
   NetworkTransport transport;

   NetworkChangePublishEvent() : transport(CPCAPI2::TransportNone) {}
   NetworkChangePublishEvent(NetworkTransport transport_, NetworkInterfaceSet interfaces_) : transport(transport_), interfaces(interfaces_) {}
   virtual~ NetworkChangePublishEvent() {}
};

enum NetworkChangePollingStateType
{
   NetworkChangePollingStateType_Idle,
   NetworkChangePollingStateType_Notify,
   NetworkChangePollingStateType_Stabilize,
   NetworkChangePollingStateType_Sync
};

struct NetworkChangePollingStatusEvent
{
   NetworkChangePollingStateType state;
   NetworkTransport transport;
   NetworkInterfaces interfaces;

   NetworkChangePollingStatusEvent() : state(NetworkChangePollingStateType_Idle), transport(CPCAPI2::TransportNone) {}
};

struct NetworkChangePollingNotificationResetEvent {};

class NetworkChangePollingHandler
{
public:

   NetworkChangePollingHandler() {}
   virtual~ NetworkChangePollingHandler() {}

   // Notifies the polling handler to populate the current network interfaces
   virtual void onNetworkInterfaceQuery(NetworkInterfaceQueryEvent& evt) = 0;

   // Notifies the polling handler when network changes have been confirmed, and can be published to all the relevant observers
   virtual void onPublishNetworkChange(const NetworkChangePublishEvent& evt) = 0;

};

/** Currently for auto-test only */
class NetworkChangePollingHandlerInternal
{
public:

   NetworkChangePollingHandlerInternal() {}
   virtual~ NetworkChangePollingHandlerInternal() {}

   // Notifies the handler when the polling manager fsm changes state
   virtual void onPollingStatusUpdate(const NetworkChangePollingStatusEvent& evt) = 0;

   // Notifies the handler when the polling notification has been reset
   virtual void onNotificationReset(const NetworkChangePollingNotificationResetEvent& evt) = 0;

};

}

#endif // CPCAPI2_NETWORK_CHANGE_POLLING_HANDLER_H
