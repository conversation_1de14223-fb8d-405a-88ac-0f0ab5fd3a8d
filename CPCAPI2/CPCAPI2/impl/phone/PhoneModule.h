//
//  PhoneModule.h
//  CPCAPI2
//
//  Created by <PERSON> on 13-03-06.
//  Copyright (c) 2013 CounterPath. All rights reserved.
//

#ifndef CPCAPI2_PhoneModule_h
#define CPCAPI2_PhoneModule_h

#include "impl/phone/Cpcapi2EventSource2.h"

namespace CPCAPI2
{
   /**
    * Interface encapsulating module behavior
    */
   class PhoneModule
   {
   public:   
      virtual ~PhoneModule() {}
      
      /**
       * PreRelease is called to indicate to the module that it needs to get its
       * affairs in order and that destruction is imminent. Some time will be given
       * to the module in order to accomplish this goal (but not too much time since
       * we do not want to block application shutdown if possible).
       * 
       * When the module has completed its PreRelease, it will indicate to the phone
       * module by switching its state to Deactivated.
       * 
       * NB: PreRelease will be called in the SDK Reactor thread.
       */
      virtual void PreRelease() {}
      
      /**
       * When the module is finished its PreRelease stage it should return true.
       * 
       * NB: PreRelease will be called in the SDK Reactor thread.
       */
      virtual bool PreReleaseCompleted() { return true; }
      
      /**
       * There is no real reason for this, we should just delete you directly (Change
       * coming soon)
       */
      virtual void Release() = 0;

      virtual void onLicensingError() {}
      bool &isLicenseOK() { return mLicenseOK; }
      bool isLicenseOK() const { return mLicenseOK; }
      
   protected:
      PhoneModule() : mLicenseOK(true) {}
      bool mLicenseOK;
   };
}

#endif
