#pragma once

#if !defined(CPCAPI2_BLUETOOTH_MANAGER_IMPL_H)
#define CPCAPI2_BLUETOOTH_MANAGER_IMPL_H

#include "phone/BluetoothHandler.h"
#include "phone/BluetoothManager.h"
#include "PhoneInterface.h"

#include <map>

namespace CPCAPI2
{
class BluetoothManagerImpl
{
public:
   typedef int BluetoothManagerImplHandle;

   BluetoothManagerImpl(Phone* phone);
   virtual ~BluetoothManagerImpl() {}
   virtual void release() { delete this; }

   virtual int start() = 0;
   virtual int stop() = 0;

   virtual BluetoothManagerImplHandle addInternalHandler(BluetoothHandler* handler);
   virtual void removeInternalHandler(BluetoothManagerImplHandle handle);

   virtual bool isBluetoothHeadsetAvailable() const = 0;
   virtual bool isBluetoothAudioConnected() const = 0;

protected:
   virtual void sendBluetoothHeadsetUnavailableEvent(const BluetoothEvent& args);
   virtual void sendBluetoothHeadsetUnavailableEventImpl(const BluetoothEvent& args);
   virtual void sendBluetoothHeadsetAvailableEvent(const BluetoothEvent& args);
   virtual void sendBluetoothHeadsetAvailableEventImpl(const BluetoothEvent& args);
   virtual void sendBluetoothAudioDisconnectedEvent(const BluetoothEvent& args);
   virtual void sendBluetoothAudioDisconnectedEventImpl(const BluetoothEvent& args);
   virtual void sendBluetoothAudioConnectedEvent(const BluetoothEvent& args);
   virtual void sendBluetoothAudioConnectedEventImpl(const BluetoothEvent& args);

   PhoneInterface* mPhone;

private:
   typedef std::map<BluetoothManager::BluetoothManagerHandle, BluetoothHandler*> HandlersMap;

   BluetoothManagerImpl();

   HandlersMap mInternalHandlers;
   BluetoothManagerImplHandle mNextInternalHandle;
};
}

#endif // CPCAPI2_BLUETOOTH_MANAGER_IMPL_H
