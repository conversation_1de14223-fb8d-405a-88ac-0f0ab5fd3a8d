#pragma once

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_THREADX_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_THREADX_H

#include "NetworkChangeManagerImpl.h"

namespace CPCAPI2
{
   class NetworkChangeManager_ThreadX : public NetworkChangeManagerImpl
   {
   public:
      NetworkChangeManager_ThreadX(Phone* phone) : NetworkChangeManagerImpl(phone) {};
      virtual ~NetworkChangeManager_ThreadX() {};
      
      virtual int start() {};
      virtual int stop() {};

      virtual NetworkTransport networkTransport() const { return TransportWiFi; /* unrestricted network */ };
   };
}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_THREADX_H
