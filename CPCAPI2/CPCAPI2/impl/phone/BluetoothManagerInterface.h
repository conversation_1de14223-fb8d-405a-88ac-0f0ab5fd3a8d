#pragma once

#if !defined(CPCAPI2_BLUETOOTH_MANAGER_INTERFACE_H)
#define CPCAPI2_BLUETOOTH_MANAGER_INTERFACE_H

#include "BluetoothManagerImpl.h"
#include "PhoneModule.h"
#include "PhoneInterface.h"
#include "phone/BluetoothHandler.h"
#include "phone/BluetoothManager.h"

#include <rutil/Fifo.hxx>

namespace CPCAPI2
{

class Phone;
   
class BluetoothManagerInterface : public BluetoothManager,
                                  public BluetoothHandler,
                                  public PhoneModule
{
public:
   BluetoothManagerInterface(Phone *phone);
   virtual ~BluetoothManagerInterface();
   
   // PhoneModule Interface
   virtual void Release() OVERRIDE;

   virtual BluetoothManagerHandle create() OVERRIDE;

   int process(unsigned int timeout = kBlockingModeNonBlocking) OVERRIDE;
   void setCallbackHook(void (*cbHook)(void*), void* context);

   int setHandler(BluetoothManagerHandle handle, BluetoothHandler* handler) OVERRIDE;
   int removeHandler(BluetoothManagerHandle handle) OVERRIDE;

   virtual bool isBluetoothHeadsetAvailable() const OVERRIDE;
   virtual bool isBluetoothAudioConnected() const OVERRIDE;

   virtual int onBluetoothHeadsetUnavailable(const BluetoothEvent& args) OVERRIDE;
   virtual int onBluetoothHeadsetAvailable(const BluetoothEvent& args) OVERRIDE;
   virtual int onBluetoothAudioDisconnected(const BluetoothEvent& args) OVERRIDE;
   virtual int onBluetoothAudioConnected(const BluetoothEvent& args) OVERRIDE;

private:
   typedef std::map<BluetoothManager::BluetoothManagerHandle, BluetoothHandler*> HandlersMap;
   
   BluetoothManagerInterface();

   void startImpl();
   void setHandlerImpl(BluetoothManagerHandle, BluetoothHandler* handler);
   void removeHandlerImpl(BluetoothManagerHandle handle);

   PhoneInterface *mPhone;
   resip::Fifo<resip::ReadCallbackBase> mCallbacks;
   BluetoothManagerImpl::BluetoothManagerImplHandle mImplHandle;
   HandlersMap mAppHandlers;
   BluetoothManagerHandle mNextHandle;
   std::function<void(void)> mCbHook;
};

}

#endif // CPCAPI2_BLUETOOTH_MANAGER_INTERFACE_H
