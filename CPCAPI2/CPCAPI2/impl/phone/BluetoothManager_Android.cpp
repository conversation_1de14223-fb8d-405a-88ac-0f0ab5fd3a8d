#ifdef ANDROID

#include "BluetoothManager_Android.h"

#include "../util/cpc_logger.h"
#include "JniHelper.h"

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

extern "C"
{
   JNIEXPORT void JNICALL Java_com_counterpath_sdk_android_BluetoothManager_onBluetoothChange(JNIEnv * env, jclass cls, jint btType, jstring jAddress, jstring jName)
   {
      const char* address = env->GetStringUTFChars(jAddress, 0);
      const char* name = env->GetStringUTFChars(jName, 0);
      CPCAPI2::BluetoothManager_Android::notifyBluetooth(btType, address, name);
      env->ReleaseStringUTFChars(jAddress, address);
      env->ReleaseStringUTFChars(jName, name);
   }
}

namespace CPCAPI2
{
// Static Members
std::vector<BluetoothManager_Android*> BluetoothManager_Android::sBluetoothManagers;
std::mutex BluetoothManager_Android::smBluetoothManagers;

BluetoothManager_Android::BluetoothManager_Android(Phone* phone) :
   BluetoothManagerImpl(phone),
   mShutdown(false),
   mWorkerThread(NULL),
   mBluetoothState(BLUETOOTH_STATE_FLAG_UNAVAILABLE)
{
}

BluetoothManager_Android::~BluetoothManager_Android()
{
   stop();
}

void BluetoothManager_Android::release()
{
   stop();
   delete this;
}

void BluetoothManager_Android::btWorkerThread()
{
   const unsigned int sleepInterval = 10000;

   while (!mShutdown)
   {
      mBluetoothEvt.WaitOne(sleepInterval);

      if (mShutdown)
      {
         break;
      }

      bool eventsWaiting = false;
      {
         std::lock_guard<std::mutex> _(smBluetoothEvents);
         eventsWaiting = (mBluetoothEvents.size() > 0);
      }
      while (eventsWaiting)
      {
         BluetoothTypeEvent params;
         //DebugLog(<< "BluetoothManager_Android - woke up from wait");
         {
            std::lock_guard<std::mutex> _(smBluetoothEvents);
            params = mBluetoothEvents.front();
            mBluetoothEvents.pop_front();
         }
         params.evt.unexpectedEvent = false;
         switch (params.type)
         {
         case BluetoothHeadsetUnavailable:
            mBluetoothState = BLUETOOTH_STATE_FLAG_UNAVAILABLE;
            sendBluetoothHeadsetUnavailableEvent(params.evt);
            break;
         case BluetoothHeadsetAvailable:
            mBluetoothState = BLUETOOTH_STATE_FLAG_DEVICE_CONNECTED;
            sendBluetoothHeadsetAvailableEvent(params.evt);
            break;
         case BluetoothAudioDisconnectedUnexpected:
            params.evt.unexpectedEvent = true;
         case BluetoothAudioDisconnected:
            mBluetoothState = BLUETOOTH_STATE_FLAG_DEVICE_CONNECTED;
            sendBluetoothAudioDisconnectedEvent(params.evt);
            break;
         case BluetoothAudioConnectedUnexpected:
            params.evt.unexpectedEvent = true;
         case BluetoothAudioConnected:
            mBluetoothState = BLUETOOTH_STATE_FLAG_AUDIO_CONNECTED | BLUETOOTH_STATE_FLAG_DEVICE_CONNECTED;
            sendBluetoothAudioConnectedEvent(params.evt);
            break;
         default:
            break;
         }
         {
            std::lock_guard<std::mutex> _(smBluetoothEvents);
            eventsWaiting = (mBluetoothEvents.size() > 0);
         }
      }
   }
}

int BluetoothManager_Android::start()
{
    if (mWorkerThread)
    {
       return -1;
    }
    mWorkerThread = new thread(std::bind(&BluetoothManager_Android::btWorkerThread, this));
    return 0;
}

int BluetoothManager_Android::stop()
{
   if (mWorkerThread)
   {
      mShutdown = true;
      mBluetoothEvt.Set();
      mWorkerThread->join();
      delete mWorkerThread;
      mWorkerThread = NULL;
      return 0;
   }
   return -1;
}

int BluetoothManager_Android::enableAndroidJavaBluetoothManager()
{
   DebugLog(<< "BluetoothManager_Android - enableAndroidJavaBluetoothManager");
   std::lock_guard<std::mutex> _(smBluetoothManagers);
   sBluetoothManagers.push_back(this);
   return 0;
}

int BluetoothManager_Android::disableAndroidJavaBluetoothManager()
{
   DebugLog(<< "BluetoothManager_Android - disableAndroidJavaBluetoothManager");
   std::lock_guard<std::mutex> _(smBluetoothManagers);
   std::vector<BluetoothManager_Android*>::iterator it = std::find(sBluetoothManagers.begin(), sBluetoothManagers.end(), this);
   if (it != sBluetoothManagers.end())
   {
     sBluetoothManagers.erase(it);
   }
   return 0;
}

void BluetoothManager_Android::notifyBluetooth(int btType, const char* address, const char* name)
{
   std::lock_guard<std::mutex> _(smBluetoothManagers);
   for (int i = 0; i < sBluetoothManagers.size(); i++)
   {
      BluetoothManager_Android* btManager = sBluetoothManagers.at(i);
      if (NULL != btManager)
      {
         btManager->onBluetoothEvent(btType, address, name);
      }
   }
}

void BluetoothManager_Android::onBluetoothEvent(int btType, const char* address, const char* name)
{
   BluetoothTypeEvent btev;
   btev.type = (BluetoothUpdate)btType;
   btev.evt.deviceAddress = address;
   btev.evt.deviceName = name;
   DebugLog(<< "BluetoothManager_Android - onBluetoothEvent (" << btev.type << ", " << btev.evt.deviceAddress << ")");
   {
      std::lock_guard<std::mutex> _(smBluetoothEvents);
      mBluetoothEvents.push_back(btev);
   }
   mBluetoothEvt.Set();
}

bool BluetoothManager_Android::isBluetoothHeadsetAvailable() const
{
   return mBluetoothState & BLUETOOTH_STATE_FLAG_DEVICE_CONNECTED;
}
bool BluetoothManager_Android::isBluetoothAudioConnected() const
{
   return mBluetoothState & BLUETOOTH_STATE_FLAG_AUDIO_CONNECTED;
}

}

#endif //ANDROID
