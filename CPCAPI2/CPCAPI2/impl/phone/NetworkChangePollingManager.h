#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_POLLING_MANAGER_H)
#define CPCAPI2_NETWORK_CHANGE_POLLING_MANAGER_H

#include "NetworkChangeManagerImpl.h"
#include "NetworkChangePollingHandler.h"

#define NETWORK_CHANGE_POLLING_STATUS_TIMER_ID                        1
#define NETWORK_CHANGE_POLLING_NOTIFY_TIMER_ID                        2
#define NETWORK_CHANGE_POLLING_STABILIZE_TIMER_ID                     3

#define NETWORK_CHANGE_POLLING_STATUS_INTERVAL_MSECS                  300000 // 5 minutes
#define NETWORK_CHANGE_POLLING_NOTIFY_INTERVAL_MSECS                  1000
#define NETWORK_CHANGE_POLLING_STABILIZE_INTERVAL_MSECS               2000

namespace CPCAPI2
{

struct NetworkChangePollingConfig
{
   int statusIntervalMsecs;
   int notifyIntervalMsecs;
   int stabilizeIntervalMsecs;

   NetworkChangePollingConfig() :
      statusIntervalMsecs(NETWORK_CHANGE_POLLING_STATUS_INTERVAL_MSECS),
      notifyIntervalMsecs(NETWORK_CHANGE_POLLING_NOTIFY_INTERVAL_MSECS),
      stabilizeIntervalMsecs(NETWORK_CHANGE_POLLING_STABILIZE_INTERVAL_MSECS) {}
   NetworkChangePollingConfig(int statusMsecs, int notifyMsecs, int stabilizeMsecs) :
      statusIntervalMsecs(statusMsecs),
      notifyIntervalMsecs(notifyMsecs),
      stabilizeIntervalMsecs(stabilizeMsecs) {}
};

class NetworkChangePollingManager
{
public:
   NetworkChangePollingManager() {}
   virtual ~NetworkChangePollingManager() {}

   virtual int addPollingHandler(NetworkChangePollingHandlerInternal* handler) = 0;
   virtual int removePollingHandler(NetworkChangePollingHandlerInternal* handler) = 0;
   virtual int setPollingConfig(const NetworkChangePollingConfig& config) = 0;
};

}

#endif // CPCAPI2_NETWORK_CHANGE_POLLING_MANAGER_H
