#pragma once

#if !defined(CPCAPI2_BLUETOOTH_MANAGER_NOOP_H)
#define CPCAPI2_BLUETOOTH_MANAGER_NOOP_H

#include "phone/BluetoothManagerImpl.h"

namespace CPCAPI2
{
class BluetoothManagerNoOp : public BluetoothManagerImpl
{
public:
   BluetoothManagerNoOp(Phone* phone);
   virtual int start();
   virtual int stop();

   virtual bool isBluetoothHeadsetAvailable() const;
   virtual bool isBluetoothAudioConnected() const;

private:
   BluetoothManagerNoOp();
};
}

#endif // CPCAPI2_BLUETOOTH_MANAGER_NOOP_H
