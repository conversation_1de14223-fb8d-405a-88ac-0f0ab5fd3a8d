#include "phone/Phone.h"
#include "BluetoothManagerImpl.h"
#include "BluetoothManagerInterface.h"

#include "rutil/Reactor.hxx"

namespace CPCAPI2
{
BluetoothManager* BluetoothManager::getInterface(Phone* cpcPhone)
{
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<BluetoothManagerInterface>(phone, "BluetoothManagerInterface");
}

BluetoothManagerInterface::BluetoothManagerInterface(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mNextHandle(1),
   mImplHandle(0),
   mCbHook(NULL)
{
}

BluetoothManagerInterface::~BluetoothManagerInterface()
{
}

void BluetoothManagerInterface::Release()
{
   mPhone->getBluetoothManager()->removeInternalHandler(mImplHandle);
   delete this;
}

void BluetoothManagerInterface::startImpl()
{
   if (!mImplHandle)
   {
      mImplHandle = mPhone->getBluetoothManager()->addInternalHandler(this);
   }
}

BluetoothManager::BluetoothManagerHandle BluetoothManagerInterface::create()
{
   return mNextHandle++;
}

int BluetoothManagerInterface::process(unsigned int timeout)
{
   resip::ReadCallbackBase* fp = mCallbacks.getNext(timeout);
   while (fp)
   {
      (*fp)();
      delete fp;
      fp = mCallbacks.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

void BluetoothManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

int BluetoothManagerInterface::setHandler(BluetoothManagerHandle handle, BluetoothHandler* handler)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&BluetoothManagerInterface::setHandlerImpl, this, handle, handler));
   return kSuccess;
}

void BluetoothManagerInterface::setHandlerImpl(BluetoothManagerHandle handle, BluetoothHandler* handler)
{
   if (handler == NULL)
   {
      mAppHandlers.erase(handle);
   }
   else
   {
      mAppHandlers[handle] = handler;
      startImpl();
   }
}

int BluetoothManagerInterface::removeHandler(BluetoothManagerHandle handle)
{
   /*
   * This is not async to the application but simplifies the API so an additional
   * callback isn't needed to notify the application its handler can be deleted.
   */
   mPhone->getSdkModuleThread().execute(resip::resip_bind(&BluetoothManagerInterface::removeHandlerImpl, this, handle));
   process(-1);
   return kSuccess;
}

void BluetoothManagerInterface::removeHandlerImpl(BluetoothManagerHandle handle)
{
   mAppHandlers.erase(handle);
}

bool BluetoothManagerInterface::isBluetoothHeadsetAvailable() const
{
   return mPhone->getBluetoothManager()->isBluetoothHeadsetAvailable();
}

bool BluetoothManagerInterface::isBluetoothAudioConnected() const
{
   return mPhone->getBluetoothManager()->isBluetoothAudioConnected();
}

int BluetoothManagerInterface::onBluetoothHeadsetUnavailable(const BluetoothEvent& event)
{
   HandlersMap::iterator it = mAppHandlers.begin();
   for (; it != mAppHandlers.end(); ++it)
   {
      mCallbacks.add(resip::resip_bind(&BluetoothHandler::onBluetoothHeadsetUnavailable, it->second, event));
   }

   if (mCbHook)
   {
      mCbHook();
   }

   return 0;
}
int BluetoothManagerInterface::onBluetoothHeadsetAvailable(const BluetoothEvent& event)
{
   HandlersMap::iterator it = mAppHandlers.begin();
   for (; it != mAppHandlers.end(); ++it)
   {
      mCallbacks.add(resip::resip_bind(&BluetoothHandler::onBluetoothHeadsetAvailable, it->second, event));
   }

   if (mCbHook)
   {
      mCbHook();
   }

   return 0;
}
int BluetoothManagerInterface::onBluetoothAudioDisconnected(const BluetoothEvent& event)
{
   HandlersMap::iterator it = mAppHandlers.begin();
   for (; it != mAppHandlers.end(); ++it)
   {
      mCallbacks.add(resip::resip_bind(&BluetoothHandler::onBluetoothAudioDisconnected, it->second, event));
   }

   if (mCbHook)
   {
      mCbHook();
   }

   return 0;
}
int BluetoothManagerInterface::onBluetoothAudioConnected(const BluetoothEvent& event)
{
   HandlersMap::iterator it = mAppHandlers.begin();
   for (; it != mAppHandlers.end(); ++it)
   {
      mCallbacks.add(resip::resip_bind(&BluetoothHandler::onBluetoothAudioConnected, it->second, event));
   }

   if (mCbHook)
   {
      mCbHook();
   }

   return 0;
}

}
