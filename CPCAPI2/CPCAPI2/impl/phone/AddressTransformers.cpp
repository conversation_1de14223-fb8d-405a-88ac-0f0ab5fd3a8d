#include "AddressTransformers.h"
#include "../util/ResipConv.h"

namespace CPCAPI2
{
   int DefaultAddressTransformer::applyTransformation(const cpc::string& targetAddress, AddressTransformationContext context, cpc::string& transformedAddress)
   {
      transformedAddress = targetAddress;

      if(context.registrationDomain.empty())
         return kError;

      //use resip::Uri to check validity      
      resip::Uri domain;
      resip::Uri target;

      //add scheme if not defined so we can construct resip::Uri for the domain:
      // Be Careful! Domains can contain a port which is also separated by a colon
      // so check the result (if found) against known schemes
      size_t position = context.registrationDomain.find( ":" );
      if( position == cpc::string::npos )
      {
         if(!ResipConv::stringToUri(cpc::string("sip:") + context.registrationDomain, domain))
            return kError;
      }
      else
      {
         cpc::string schemeCandidate = context.registrationDomain.substr( 0, position );
         if( schemeCandidate == "sip" || schemeCandidate == "sips" || schemeCandidate == "tel" )
         {
            if(!ResipConv::stringToUri(context.registrationDomain, domain))
               return kError;
         }
         else
         {
            // Not a valid scheme (unless maybe 'sip' or 'sips' is a valid domain .. shudder)
            if(!ResipConv::stringToUri(cpc::string("sip:") + context.registrationDomain, domain))
               return kError;
         }
      }

      //add scheme if not defined so we can construct resip::Uri for targetAddress:
      cpc::string tempAddress = targetAddress;
      int colon = targetAddress.find(":");
      int at = targetAddress.find("@");
      if(colon == cpc::string::npos || (colon != cpc::string::npos && at != cpc::string::npos && colon > at))
      {
         tempAddress = cpc::string("sip:") + tempAddress;
      }

      //add host if not exists, for user part is parsed into host if no host defined for non tel uris:
      if(tempAddress.find("tel:") == cpc::string::npos && tempAddress.find("@") == cpc::string::npos)
      {
         if(!ResipConv::stringToUri(tempAddress + cpc::string("@") + context.registrationDomain, target))
            return kError;
      }
      else
      {
         if(!ResipConv::stringToUri(tempAddress, target))
            return kError;
      }

      if(target.scheme() == "tel")
      {
         if( domain.port() != 0 && domain.port() != 5060 )
            transformedAddress = cpc::string("sip:") + target.user().c_str() + "@" + domain.host().c_str() + ":" + cpc::to_string( domain.port() ).c_str() + ";user=phone";
         else
            transformedAddress = cpc::string("sip:") + target.user().c_str() + "@" + domain.host().c_str() + ";user=phone";
      }
      else 
      {
         transformedAddress = cpc::string("sip:") + target.user().c_str() + "@" + target.host().c_str();
         int port = target.port();
         if( port != 0 && port != 5060 )
            transformedAddress.append(":" + cpc::to_string(port));
      }

      return kSuccess;
   }
}