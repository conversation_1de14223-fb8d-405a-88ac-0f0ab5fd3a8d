#include "EventQueue.h"
#include "PhoneInterface.h"
#include "util/ReactorHelpers.h"

#include "gen/Phone/datatypes/PhoneEvents.h"

#include <boost/algorithm/string.hpp>
#include <regex>

namespace CPCAPI2
{

int EventQueueFactory::sNextQueueId = 1;

int EventQueueFactory::getNext()
{
   return (::time(NULL) + (sNextQueueId++));
}

std::shared_ptr<EventQueue> EventQueue::create(resip::MultiReactor& reactor)
{
   // make_shared fails due to EventQueue ctor being private
   return std::shared_ptr<EventQueue>(new EventQueue(reactor));
}

EventQueue::EventQueue(resip::MultiReactor& reactor) :
mReactor(reactor)
{
   mReactor.addRef();
}

EventQueue::~EventQueue()
{
   mReactor.releaseRef();
}


std::vector<std::regex> EventQueue::toRegex(const std::vector<std::string>& filter) const
{
   std::vector<std::regex> ret;

   for (std::vector<std::string>::const_iterator it = filter.begin(); it != filter.end(); ++it)
   {
      std::string f = *it;

      // Phone.onError -> Phone\.onError
      // Phone.* -> Phone\..*
      
      boost::replace_all(f, ".", "\\."); // regex escape . so it doesn't get treated as the special regex dot matcher (I can add this as a code comment)
      boost::replace_all(f, "*", ".*"); // replace with regex .* to match anything for the rest of the string (I guess this could also be .+ instead..)

      ret.push_back(std::regex(f));
   }

   return ret;
}

int EventQueue::createEventQueue(const std::vector<std::string>& events)
{
   std::unique_lock<std::mutex> lock(mMutex);
   int queueId = EventQueueFactory::getNext();
   mEventSubscriptions[queueId] = std::move(toRegex(events));
   std::deque<SdkEvent> queue;
   mEventQueue[queueId] = std::move(queue);

   return queueId;
}

void EventQueue::destroyEventQueue(int queueId)
{
   if (getSdkReactor().isCurrentThread())
   {
      destroyEventQueueImpl(queueId);
   }
   else
   {
      getSdkReactor().post(resip::resip_bind(&EventQueue::destroyEventQueueImpl, shared_from_this(), queueId));
   }
}

void EventQueue::destroyEventQueueImpl(int queueId)
{
   std::unique_lock<std::mutex> lock(mMutex);
   auto i = mEventQueue.find(queueId);
   if (i != mEventQueue.end())
   {
      auto hF = mDestroyQueueFuture.get_future();
      // Trigger blocked app thread if it exists and destroy the queue
      addEventImpl(queueId, SdkEvent());
      if (hF.wait_for(std::chrono::milliseconds(2000)) == std::future_status::ready)
      {
         queueId = hF.get();
         mEventSubscriptions.erase(queueId);
         mEventQueue.erase(queueId);
      }
      else
      {
         // Timed-out - normal case if the queue is not blocked
         mDestroyQueueFuture.set_value(0);
      }
      mDestroyQueueFuture = std::promise<int>(); // reset for next use
   }
}

SdkEvent EventQueue::getEvent(int queueId, int timeout)
{
   // Timeout in milliseconds
   // 0  - timeout is infinite
   // >0 - return Event with type = “None” if no event is received in the specified duration
   // <0 - return Event with type = “None” immediately if queue is empty

   std::unique_lock<std::mutex> lock(mMutex);

   // TODO: queue gets destroyed while in wait mode, i.e. gets destroyed from a thread other than the one thats waiting on that queue
   // TODO: notification received before wait timeout has expired

   auto i = mEventQueue.find(queueId);
   if (i != mEventQueue.end())
   {
      if (timeout == 0)
      {
         mCondition.wait(lock, [&i]{ return (!(i->second.empty())); });
      }
      else if (timeout > 0)
      {
         mCondition.wait_for(lock, std::chrono::milliseconds(timeout), [&i]{ return (!(i->second.empty())); });
      }
      else 
      {
         // timeout < 0, return immediately
      }

      // Ensure queue still exists
      i = mEventQueue.find(queueId);
      if (i != mEventQueue.end())
      {
         if (!(i->second.empty()))
         {
            // TODO: handle scenario with multiple packets still in list, i.e. there is a None event, but other events to process
            SdkEvent evt = std::move(i->second.front());
            i->second.pop_front();
            if (evt.type() == "None")
            {
               // Looks like the queue is pending destruction
               try
               {
                  mDestroyQueueFuture.set_value(queueId);
               }
               catch (const std::future_error& e)
               {
                  // Possibly stale event or future
                  // assert(0);
               }
            }

            return evt;
         }
      }
   }
   else
   {
      return SdkEvent(to_string(jsonrpc::CPCAPI2::Phone::PhoneEvents::PhoneDotOnEventQueueError), ""); // TODO: event data and json serialization required
   }

   // Return an event with type = "None" in case:
   // - non-blocking mode and no event found in queue
   // - blocking mode and no event received within timeout
   // - invalid queueId
   // - queue being destroyed
   return SdkEvent();
}

// called from any thread
void EventQueue::addEvent(const SdkEvent& evt)
{
   std::unique_lock<std::mutex> lock(mMutex);

   std::vector<int> queues;
   getQueuesForEventImpl(evt, queues);

   for (auto i = queues.begin(); i != queues.end(); ++i)
   {
      auto j = mEventQueue.find(*i);
      if (j != mEventQueue.end())
      {
         j->second.push_back(evt);
      }
   }
   mCondition.notify_all();
}

void EventQueue::getQueuesForEventImpl(const SdkEvent& evt, std::vector<int>& queues)
{
   // TODO: Add support for the * code for pattern matching
   for (auto i = mEventSubscriptions.begin(); i != mEventSubscriptions.end(); ++i)
   {
      if (i->second.empty())
      {
         // If no event types are specified, all SDK events will be returned on the queue
         queues.push_back(i->first);
         continue;
      }

      for (auto j = i->second.begin(); j != i->second.end(); j++)
      {
         if (std::regex_match(evt.type(), *j))
         {
            queues.push_back(i->first);
            break;
         }
      }
   }
}

void EventQueue::addEventImpl(int queueId, SdkEvent&& evt)
{
   auto i = mEventQueue.find(queueId);
   if (i != mEventQueue.end())
   {
      i->second.push_back(std::move(evt));
   }
   mCondition.notify_all();
}

}
