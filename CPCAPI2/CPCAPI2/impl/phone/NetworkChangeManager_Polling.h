#pragma once

#ifndef CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_H
#define CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_H

#include "NetworkChangeManagerImpl.h"

#include "../util/cpc_thread.h"

#include <set>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
class NetworkChangeManager_Polling : public NetworkChangeManagerImpl
{
public:
   NetworkChangeManager_Polling(Phone* phone);
   virtual ~NetworkChangeManager_Polling();
   virtual void release() OVERRIDE;

   virtual int start() OVERRIDE;
   virtual int stop() OVERRIDE;

private:
   NetworkChangeManager_Polling();
   void pollInterfacesThread();

   thread* mPollingThread;
};
}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_POLLING_H
