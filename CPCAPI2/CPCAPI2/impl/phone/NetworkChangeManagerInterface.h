#pragma once

#if !defined(CPCAPI2_NETWORK_CHANGE_MANAGER_INTERFACE_H)
#define CPCAPI2_NETWORK_CHANGE_MANAGER_INTERFACE_H

#include "PhoneModule.h"
#include "PhoneInterface.h"
#include "../util/cpc_thread.h"
#include "../util/AutoTestProcessor.h"
#include "phone/NetworkChangeHandler.h"
#include "phone/NetworkChangeManager.h"

#include <rutil/Fifo.hxx>

namespace CPCAPI2
{

class Phone;
class NetworkChangeManager_Android;
class NetworkChangeManager_Mock;

class NetworkChangeManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<NetworkChangeHandler, NetworkChangeManager::NetworkChangeManagerHandle> >,
                                      public NetworkChangeManager,
                                      public CPCAPI2::EventSyncHandler<NetworkChangeHandler>,
                                      public PhoneModule
{
public:
   NetworkChangeManagerInterface(Phone *phone);
   virtual ~NetworkChangeManagerInterface();

   FORWARD_EVENT_PROCESSOR(NetworkChangeManager)

   // PhoneModule Interface
   virtual void Release() OVERRIDE;

   virtual NetworkChangeManagerHandle create() OVERRIDE;

   int setHandler(NetworkChangeManagerHandle handle, NetworkChangeHandler* handler) OVERRIDE;
   int removeHandler(NetworkChangeManagerHandle handle) OVERRIDE;

   virtual int start();
   virtual int stop();

   virtual NetworkTransport networkTransport() const OVERRIDE;

   virtual cpc::string wwanType() const;

   // current RSSI signal quality rating, if wifi is in use by the device. returns 0xFFFFF if not supported on platform
   virtual int currentWifiRssi() const;
   // current signal strength, if wifi is in use by the device. returns -1 if not supported on platform
   virtual int currentWifiSignalLevel() const;
   // maximum possible signal strength (*not* maximum historial signal strength), if wifi is in use by the device.
   // returns -1 if not supported on platform
   virtual int maxWifiSignalLevel() const;
   virtual int currentWifiFreqMhz() const;
   virtual int currentWifiChannel() const;
   virtual int currentWifiLinkSpeedMbps() const;

   void initImpl(bool useNetworkChangeManager, const ConnectionPreferences::NetworkChangeManagerType& networkChangeManagerType, NetworkTransport transport);

#ifdef CPCAPI2_AUTO_TEST
   NetworkChangeManager_Mock* getMockImpl();
#endif

#ifdef ANDROID
   NetworkChangeManager_Android* getAndroidImpl();
#endif

   // NetworkChangeHandler
   virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

private:
   typedef std::map<NetworkChangeManager::NetworkChangeManagerHandle, NetworkChangeHandler*> HandlersMap;

   NetworkChangeManagerInterface();

   void setHandlerImpl(NetworkChangeManagerHandle, NetworkChangeHandler* handler);
   void removeHandlerImpl(NetworkChangeManagerHandle handle);

   PhoneInterface *mPhone;
   NetworkChangeManagerImpl *mNetworkChangeManager;
   HandlersMap mAppHandlers;
   NetworkChangeManagerHandle mNextHandle;
};

std::ostream& operator<<(std::ostream& os, const NetworkChangeEvent& evt);

}

#endif // CPCAPI2_NETWORK_CHANGE_MANAGER_INTERFACE_H
