#include "NetworkChangeManager_Metro.h"

#ifdef WinRT

using namespace Windows::Networking::Connectivity;

namespace CPCAPI2
{
  NetworkChangeManager_Metro::NetworkChangeManager_Metro(Phone* phone) :
    NetworkChangeManagerImpl(phone),
    mRegisteredNetworkStatusNotification(false),
    mNetworkTransport(NetworkTransport::TransportNone)
  {
  }

  NetworkChangeManager_Metro::~NetworkChangeManager_Metro()
  {
    stop();
  }

  int NetworkChangeManager_Metro::start()
  {
    if (mRegisteredNetworkStatusNotification)
      return -1;

    networkChanged();

    // network change event from WinRT: http://msdn.microsoft.com/en-us/library/windows/apps/windows.networking.connectivity.networkinformation.networkstatuschanged
    mToken = NetworkInformation::NetworkStatusChanged += ref new NetworkStatusChangedEventHandler([=](Platform::Object^ sender) {
      networkChanged();
    });

    mRegisteredNetworkStatusNotification = true;

    return 0;
  }

  void NetworkChangeManager_Metro::networkChanged()
  {
    NetworkChangeEvent params;
    params.networkTransport = getTransportType(NetworkInformation::GetInternetConnectionProfile());
    mNetworkTransport = params.networkTransport;
    sendNetworkChangeEvent(params);
  }

  int NetworkChangeManager_Metro::stop()
  {
    if (!mRegisteredNetworkStatusNotification)
      return -1;

    NetworkInformation::NetworkStatusChanged -= mToken;
    mRegisteredNetworkStatusNotification = false;

    return 0;
  }


  NetworkTransport NetworkChangeManager_Metro::getTransportType(ConnectionProfile^ networkInterface)
  {
    if (networkInterface->GetNetworkConnectivityLevel() == NetworkConnectivityLevel::None)
    {
      return NetworkTransport::TransportNone;
    }

    // Requires Windows 8.1
    if (networkInterface->IsWlanConnectionProfile)
    {
      return NetworkTransport::TransportWiFi;
    }
    else if (networkInterface->IsWwanConnectionProfile) {
      return NetworkTransport::TransportWWAN;
    }
    else
    {
      return NetworkTransport::TransportNone;
    }
  }

  NetworkTransport NetworkChangeManager_Metro::networkTransport() const
  {
    return mNetworkTransport;
  }

}

#endif // WinRT
