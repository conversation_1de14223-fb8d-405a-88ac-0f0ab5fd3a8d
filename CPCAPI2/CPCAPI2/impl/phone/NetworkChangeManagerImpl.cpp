#include "NetworkChangeManagerImpl.h"

#include "PhoneInterface.h"

#include <rutil/DnsUtil.hxx>
#include <rutil/Reactor.hxx>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{
NetworkChangeManagerImpl::NetworkChangeManagerImpl(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mInterface(dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(phone))),
   mLastKnownNetworkTransport(TransportNone),
   mStarted(false),
   mShutdown(false)
{
   mLastKnownInterfaces.insert("UNCHECKED");
}

NetworkChangeManagerImpl::NetworkChangeManagerInterfaceSet NetworkChangeManagerImpl::getLocalIPAddresses(bool includeLoopback)
{
   NetworkChangeManagerInterfaceSet result;
   std::list<std::pair<resip::Data,resip::Data> > ifs = resip::DnsUtil::getInterfaces(resip::Data::Empty, includeLoopback);
   StackLog(<< "NetworkChangeManagerImpl::getLocalIPAddresses(): include-loopback: " << includeLoopback << " interface-count: " << ifs.size());
   std::list<std::pair<resip::Data,resip::Data> >::const_iterator it = ifs.begin();
   for (; it != ifs.end(); ++it)
   {
      std::stringstream ss;
      ss << "interface: " << it->second.c_str();
      if (includeLoopback || ((!resip::isEqualNoCase(it->second, "127.0.0.1") && !resip::isEqualNoCase(it->second, "::1"))))
      {
         ss << " accepted";
         result.insert(it->second);
      }
      else
      {
         ss << " ignored";
      }
      StackLog(<< "NetworkChangeManagerImpl::getLocalIPAddresses(): " << ss.str().c_str());
   }

   return result;
}

void NetworkChangeManagerImpl::compareInterfaceLists(const NetworkChangeManagerInterfaceSet& oldIfs, const NetworkChangeManagerInterfaceSet& newIfs, 
                                                     cpc::vector<cpc::string>& ifsUp, cpc::vector<cpc::string>& ifsDown)
{
   for(NetworkChangeManagerInterfaceSet::iterator itOld = oldIfs.begin(); itOld != oldIfs.end(); ++itOld)
   {
      if (!newIfs.count(*itOld)) ifsDown.push_back(itOld->c_str());
   }
   for(NetworkChangeManagerInterfaceSet::iterator itNew = newIfs.begin(); itNew != newIfs.end(); ++itNew)
   {
      if (!oldIfs.count(*itNew)) ifsUp.push_back(itNew->c_str());
   }
}

// a non-static version allows subclasses (e.g. NetworkChangeManager_Mock) to override behaviour
std::set<resip::Data> NetworkChangeManagerImpl::getLocalIPAddresses_NonStatic(bool includeLocalAddress)
{
   return NetworkChangeManagerImpl::getLocalIPAddresses(includeLocalAddress);
}

std::ostream& operator<<(std::ostream& out, const std::set<resip::Data>& toPrint)
{
   for (std::set<resip::Data>::const_iterator it = toPrint.begin(); it != toPrint.end(); ++it)
   {
      out << "[" << *it << "]";
   }

   return out;
}

void NetworkChangeManagerImpl:: sendNetworkChangeEvent(const NetworkChangeEvent& args)
{
   NetworkChangeManagerInterfaceSet latestLocalIPs = getLocalIPAddresses_NonStatic();
   if (latestLocalIPs != mLastKnownInterfaces || args.networkTransport != mLastKnownNetworkTransport)
   {
      mLastKnownInterfaces = latestLocalIPs;
      sendNetworkChangeEventUnconditional(args);
   }
   else
   {
      DebugLog(<< "Not propagating network change; latestLocalIPs: " << latestLocalIPs << ", mLastKnownInterfaces: " << mLastKnownInterfaces 
               << ", args.networkTransport: " << args.networkTransport << ", mLastKnownNetworkTransport: " << mLastKnownNetworkTransport);
   }
}

void NetworkChangeManagerImpl::sendNetworkChangeEventUnconditional(const NetworkChangeEvent& args)
{
   mPhone->getSdkModuleThread().post(resip::resip_safe_bind(&NetworkChangeManagerImpl::sendNetworkChangeEventUnconditionalImpl, this, args));
}

void NetworkChangeManagerImpl::sendNetworkChangeEventUnconditionalImpl(const NetworkChangeEvent& args)
{
   mLastKnownNetworkTransport = args.networkTransport;
   mInterface->fireEvent(cpcEvent(NetworkChangeHandler, onNetworkChange), args);
}

int NetworkChangeManagerImpl::wifiFreqToChannel(int freqMhz) const
{
   int retChannel = -1;
   //int band;
   if (freqMhz != -1)
   {
      if (freqMhz >= 2412 && freqMhz <= 2472)
      {
         //band = 2.4;
         retChannel = 1 + ((freqMhz - 2412) / 5);
      }
      else if (freqMhz == 2484)
      {
         //band = 2.4;
         retChannel = 14;
      }
      else if (freqMhz >= 4915 && freqMhz <= 4980)
      {
         //band = 5.0;
         retChannel = 183 + ((freqMhz - 4915) / 5);
      }
      else if (freqMhz >= 5035 && freqMhz <= 5865)
      {
         //band = 5.0;
         retChannel = 7 + ((freqMhz - 5035) / 5);
      }
      else
      {
         //band = -1.0;
         retChannel = -1;
      }
   }

   return retChannel;
}


}
