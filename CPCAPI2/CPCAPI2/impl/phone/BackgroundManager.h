#pragma once

#if !defined(CPCAPI2_BACKGROUND_MANAGER_H)
#define CPCAPI2_BACKGROUND_MANAGER_H

#include "PhoneInterface.h"

#include <condition_variable>

namespace CPCAPI2
{
class BackgroundManager;

typedef uint32_t WakeLockHandle;

// Wrapper class so that we get both reactor and milliseconds in onSelect
class ResipReactorSelectHandler : public resip::ReactorSelectTimeoutHandler
{
public:
   ResipReactorSelectHandler(BackgroundManager* p, resip::MultiReactor* r);
   ~ResipReactorSelectHandler();

   // resip::ReactorSelectTimeoutHandler
   void onSelect(unsigned int milliseconds) override;

private:
   BackgroundManager* parent;
   resip::MultiReactor* reactor;
};

class BackgroundManagerHandler
{
public:
   virtual void onActive() = 0;
   virtual void onInactive(int64_t timeout) = 0;
};

class BackgroundManager
{
public:
   static BackgroundManager* Instance();

   void start();
   void stop();

   WakeLockHandle acquireWakeLock();
   void releaseWakeLock(WakeLockHandle handle);

   void setHandler(BackgroundManagerHandler* handler) { mHandler = handler; }

   void addResipReactor(resip::MultiReactor* reactor);
   void removeResipReactor(resip::MultiReactor* reactor);
   void onResipReactorSelect(resip::MultiReactor* reactor, unsigned int milliseconds);

   void onResume();
   void setResipReactorSelectHook(bool(*hook)());

private:
   static BackgroundManager* gInstance;

   BackgroundManager() : mNextWakeLockHandle(1), mResipReactorSelectHook(nullptr) {};

   void thread();

   WakeLockHandle mNextWakeLockHandle;

   BackgroundManagerHandler* mHandler = nullptr;

   std::thread* mThread = nullptr;
   std::mutex mLock;
   std::condition_variable mInterrupt; // use a cond_var so we can wake the thread up for shutdown
   std::atomic_bool mShutdown = false;
   bool mBgThreadWork = false;
   std::chrono::time_point<std::chrono::system_clock> mLastProcess;
   std::chrono::time_point<std::chrono::system_clock> mLastSelect;

   std::map<resip::MultiReactor*, int64_t> mResipReactors;
   std::map<resip::MultiReactor*, ResipReactorSelectHandler*> mResipReactorHandlers;

   std::set<WakeLockHandle> mWakeLocks;
   bool (*mResipReactorSelectHook)();
};
}

#endif // CPCAPI2_BACKGROUND_MANAGER_H
