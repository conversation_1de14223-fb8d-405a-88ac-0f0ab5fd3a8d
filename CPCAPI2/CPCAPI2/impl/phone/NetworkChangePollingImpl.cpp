#include "NetworkChangePollingImpl.h"
#include "NetworkChangePollingManager.h"

#include "../util/cpc_logger.h"

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{

NetworkChangePollingImpl::NetworkChangePollingStateFactory::NetworkChangePollingStateFactory()
{
}

NetworkChangePollingImpl::NetworkChangePollingStateFactory::~NetworkChangePollingStateFactory()
{
   for (NetworkChangePollingStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void NetworkChangePollingImpl::NetworkChangePollingStateFactory::create()
{
   if (mStates.size() > 0)
   {
      ErrLog(<< "NetworkChangePollingStateFactory::create(): state factory already initialized");
      return;
   }

   mStates[NetworkChangePollingStateType_Idle] = create(NetworkChangePollingStateType_Idle);
   mStates[NetworkChangePollingStateType_Stabilize] = create(NetworkChangePollingStateType_Stabilize);
   mStates[NetworkChangePollingStateType_Notify] = create(NetworkChangePollingStateType_Notify);
   mStates[NetworkChangePollingStateType_Sync] = create(NetworkChangePollingStateType_Sync);

   for (NetworkChangePollingStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      StackLog(<< "NetworkChangePollingStateFactory::create(): " << NetworkChangePollingState::getName(i->first) << " : " << i->first);
   }
}

NetworkChangePollingImpl::NetworkChangePollingState* NetworkChangePollingImpl::NetworkChangePollingStateFactory::getState(NetworkChangePollingStateType type)
{
   if (mStates.size() == 0)
   {
      ErrLog(<< "NetworkChangePollingStateFactory::getState(): state factory not initialized");
      return NULL;
   }
   return mStates[type];
}

NetworkChangePollingImpl::NetworkChangePollingState* NetworkChangePollingImpl::NetworkChangePollingStateFactory::create(NetworkChangePollingStateType type)
{
   NetworkChangePollingImpl::NetworkChangePollingState* state = NULL;
   switch (type)
   {
      case NetworkChangePollingStateType_Idle: state = new NetworkChangePollingIdleState(); break;
      case NetworkChangePollingStateType_Notify: state = new NetworkChangePollingNotifyState(); break;
      case NetworkChangePollingStateType_Stabilize: state = new NetworkChangePollingStabilizeState(); break;
      case NetworkChangePollingStateType_Sync: state = new NetworkChangePollingSyncState(); break;
      default: break;
   }

   return state;
}

NetworkChangePollingImpl::NetworkChangePollingImpl(PhoneInterface* phone, NetworkChangePollingHandler* handler, NetworkChangeManagerImpl* manager) :
mPhone(phone),
mHandler(handler),
mManager(manager),
mStateType(NetworkChangePollingStateType_Idle),
mTransport(CPCAPI2::TransportNone)
{
   mFactory.reset(new NetworkChangePollingStateFactory());
   mFactory->create();

   if (manager)
   {
      manager->addObserver(this);
   }
   DebugLog(<< "NetworkChangePollingImpl::NetworkChangePollingImpl()");
}

NetworkChangePollingImpl::~NetworkChangePollingImpl()
{
   DebugLog(<< "NetworkChangePollingImpl::~NetworkChangePollingImpl()");
   cancelTimers();
   if (mManager)
   {
      mManager->removeObserver(this);
   }
   assert(mPollingThread.get() == NULL);
}

// ReactorBinded
void NetworkChangePollingImpl::release()
{
   StackLog(<< "NetworkChangePollingImpl::release()");
   mInstance.reset();
}

void NetworkChangePollingImpl::postToSdk(resip::ReadCallbackBase* rcb)
{
   mPhone->getSdkModuleThread().post(rcb);
}

void NetworkChangePollingImpl::sendNetworkChangePollingStatusEvent(bool sendAsync)
{
#ifdef CPCAPI2_AUTO_TEST
   NetworkChangePollingStatusEvent statusEvt;
   statusEvt.state = mStateType;
   statusEvt.transport = mTransport;
   convertTo(mInterfaces, statusEvt.interfaces);
   if (sendAsync)
   {
      postToSdk(resip::resip_safe_bind(&NetworkChangePollingImpl::sendNetworkChangePollingStatusEventImpl, this, statusEvt));
   }
   else
   {
      // Would be required when shutting-down as the phone interface sdk reactor might have already initiated destruction
      sendNetworkChangePollingStatusEventImpl(statusEvt);
   }
#endif
}

void NetworkChangePollingImpl::sendNetworkChangePollingStatusEventImpl(const NetworkChangePollingStatusEvent& evt)
{
   std::lock_guard<std::recursive_mutex> alock(mHandlerMutex);
   for (HandlersSet::const_iterator i = mHandlers.begin(); i != mHandlers.end(); ++i)
   {
      NetworkChangePollingHandlerInternal* handler = dynamic_cast<NetworkChangePollingHandlerInternal*>(*i);
      if (handler)
      {
         handler->onPollingStatusUpdate(evt);
      }
   }
}

void NetworkChangePollingImpl::sendNetworkChangePollingNotificationResetEvent()
{
#ifdef CPCAPI2_AUTO_TEST
   NetworkChangePollingNotificationResetEvent resetEvt;
   postToSdk(resip::resip_safe_bind(&NetworkChangePollingImpl::sendNetworkChangePollingNotificationResetEventImpl, this, resetEvt));
#endif
}

void NetworkChangePollingImpl::sendNetworkChangePollingNotificationResetEventImpl(const NetworkChangePollingNotificationResetEvent& evt)
{
   std::lock_guard<std::recursive_mutex> alock(mHandlerMutex);
   for (HandlersSet::const_iterator i = mHandlers.begin(); i != mHandlers.end(); ++i)
   {
      NetworkChangePollingHandlerInternal* handler = dynamic_cast<NetworkChangePollingHandlerInternal*>(*i);
      if (handler)
      {
         handler->onNotificationReset(evt);
      }
   }
}

void NetworkChangePollingImpl::changeState(NetworkChangePollingStateType newStateType)
{
   NetworkChangePollingStateType currentStateType = mStateType;
   DebugLog(<< "NetworkChangePollingImpl::changeState(): changing state from: " << NetworkChangePollingImpl::NetworkChangePollingState::getName(currentStateType) << " to: " << NetworkChangePollingImpl::NetworkChangePollingState::getName(newStateType) << " current transport: " << mTransport << " current interfaces: " << getInterfaceList(getInterfaces()));

   if (!mFactory)
   {
      InfoLog(<< "NetworkChangePollingImpl::changeState(): factory not initialized");
      return;
   }

   NetworkChangePollingState* currentState = mFactory->getState(currentStateType);
   if (currentState)
   {
      currentState->onExit(this);
   }

   mStateType = newStateType;
   NetworkChangePollingState* newState = mFactory->getState(newStateType);
   if (newState)
   {
      newState->onEntry(this);
   }

   if (newStateType == NetworkChangePollingStateType_Idle)
   {
      DebugLog(<< "NetworkChangePollingImpl::changeState(): network polling impl being shutdown");
      cancelTimers();
      return;
   }

   sendNetworkChangePollingStatusEvent();
}

CPCAPI2::NetworkChangePollingStateType NetworkChangePollingImpl::getState()
{
   return mStateType;
}

CPCAPI2::NetworkTransport NetworkChangePollingImpl::getTransport()
{
   return mTransport;
}

void NetworkChangePollingImpl::setTransport(CPCAPI2::NetworkTransport transport)
{
   mTransport = transport;
}

std::set<resip::Data>& NetworkChangePollingImpl::getInterfaces()
{
   std::lock_guard<std::recursive_mutex> alock(mInterfacesMutex);
   return mInterfaces;
}

// NetworkChangeHandlerInternal

void NetworkChangePollingImpl::onNetworkStatusEvent(CPCAPI2::NetworkStatusEvent& evt)
{
   if (mPollingThread)
   {
      mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::onNetworkStatusEventImpl, this, evt));
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::handleNetworkStatusEvent(): polling thread not initialized");
      assert(0);
   }
}

void NetworkChangePollingImpl::onNetworkStatusEventImpl(const CPCAPI2::NetworkStatusEvent& evt)
{
   if (mManager && mManager->isShutdown())
   {
      InfoLog(<< "NetworkChangePollingImpl::onNetworkStatusEventImpl(): in shutdown mode");
      return;
   }

   NetworkChangePollingState* state = mFactory->getState(mStateType);
   if (state)
   {
      state->handleNetworkStatusEvent(this, evt);
      return;
   }
   InfoLog(<< "NetworkChangePollingImpl::onNetworkStatusEventImpl(): invalid state");
   assert(0);
}

void NetworkChangePollingImpl::onNetworkInitializeEvent(NetworkInitializeEvent& evt)
{
   mPollingThread.reset(new resip::MultiReactor("NetworkManagerPollingThread"));
   mPollingThread->start();
   auto hF = mPollingStartupFuture.get_future();
   StackLog(<< "NetworkChangePollingImpl::onNetworkInitializeEvent()");
   mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::onNetworkInitializeEventImpl, this, evt));
   if (hF.wait_for(std::chrono::milliseconds(2000)) == std::future_status::ready)
   {
      hF.get();
   }
   mPollingStartupFuture = std::promise<bool>(); // reset for next use
}

void NetworkChangePollingImpl::onNetworkInitializeEventImpl(const NetworkInitializeEvent& evt)
{
   DebugLog(<< "NetworkChangePollingImpl::onNetworkInitializeEventImpl(): starting polling impl");
   mInstance = shared_from_this();
   mStatusTimer.reset(new resip::DeadlineTimer<resip::MultiReactor>(*mPollingThread));
   mNotifyTimer.reset(new resip::DeadlineTimer<resip::MultiReactor>(*mPollingThread));
   mStabilizeTimer.reset(new resip::DeadlineTimer<resip::MultiReactor>(*mPollingThread));

   NetworkChangePollingState* state = mFactory->getState(mStateType);
   if (state)
   {
      state->handleNetworkInitializeEvent(this, evt);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::onNetworkInitializeEventImpl(): invalid state");
      assert(0);
   }

   mPollingStartupFuture.set_value(true);
}

void NetworkChangePollingImpl::onNetworkShutdownEvent(NetworkShutdownEvent& evt)
{
   if (!mPollingThread)
   {
      InfoLog(<< "NetworkChangePollingImpl::onNetworkShutdownEvent(): polling thread not initialized");
      return;
   }
   auto hF = mPollingShutdownFuture.get_future();
   StackLog(<< "NetworkChangePollingImpl::onNetworkShutdownEvent()");
   mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::onNetworkShutdownEventImpl, this, evt));
   if (hF.wait_for(std::chrono::milliseconds(2000)) == std::future_status::ready)
   {
      hF.get();
   }
   sendNetworkChangePollingStatusEvent(false);

   mPollingThread->stop();
   reactorSafeRelease(mPollingThread.get());
   mPollingThread.reset();
   mPollingShutdownFuture = std::promise<bool>(); // reset for next use

   /*
   // Commenting out for now, crashes on linux platform
   if (mManager)
   {
      mManager->removeObserver(this);
   }
   */
}

void NetworkChangePollingImpl::onNetworkShutdownEventImpl(const NetworkShutdownEvent& evt)
{
   DebugLog(<< "NetworkChangePollingImpl::onNetworkShutdownEventImpl(): shutting down polling impl");
   NetworkChangePollingState* state = mFactory->getState(mStateType);
   if (state)
   {
      state->handleNetworkShutdownEvent(this, evt);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::onNetworkShutdownEventImpl(): invalid state");
      assert(0);
   }

   mStatusTimer.reset();
   mNotifyTimer.reset();
   mStabilizeTimer.reset();
   mPollingShutdownFuture.set_value(true);
}

// Based on NetworkChangePollingManager

void NetworkChangePollingImpl::addPollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   if (mPollingThread)
   {
      mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::addPollingHandlerImpl, this, handler));
   }
   else
   {
      DebugLog(<< "NetworkChangePollingImpl::addPollingHandler(): polling thread not initialized");
      addPollingHandlerImpl(handler);
   }
}

void NetworkChangePollingImpl::addPollingHandlerImpl(NetworkChangePollingHandlerInternal* handler)
{
   // DebugLog(<< "NetworkChangePollingImpl::addPollingHandlerImpl()");
   std::lock_guard<std::recursive_mutex> alock(mHandlerMutex);
   mHandlers.insert(handler);
   DebugLog(<< "NetworkChangePollingImpl::addPollingHandlerImpl(): polling handler: " << handler << " added");
}

void NetworkChangePollingImpl::removePollingHandler(NetworkChangePollingHandlerInternal* handler)
{
   // Future required to avoid the case where the polling thread could trigger a callback on a handler that is already destroyed
   auto hF = mHandlerRemovalFuture.get_future();
   if (mPollingThread)
   {
      mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::removePollingHandlerImpl, this, handler));
      if (hF.wait_for(std::chrono::milliseconds(2000)) == std::future_status::ready)
      {
         hF.get();
      }
   }
   else
   {
      DebugLog(<< "NetworkChangePollingImpl::removePollingHandler(): polling thread not initialized");
      removePollingHandlerImpl(handler);
   }
   mHandlerRemovalFuture = std::promise<bool>(); // reset for next use
}

void NetworkChangePollingImpl::removePollingHandlerImpl(NetworkChangePollingHandlerInternal* handler)
{
   std::lock_guard<std::recursive_mutex> alock(mHandlerMutex);
   mHandlers.erase(handler);
   mHandlerRemovalFuture.set_value(true);
}

void NetworkChangePollingImpl::setPollingConfig(const NetworkChangePollingConfig& config)
{
   if (mPollingThread)
   {
      mPollingThread->post(resip::resip_safe_bind(&NetworkChangePollingImpl::setPollingConfigImpl, this, config));
   }
   else
   {
      DebugLog(<< "NetworkChangePollingImpl::setPollingConfig(): polling thread not initialized");
      setPollingConfigImpl(config);
   }
}

void NetworkChangePollingImpl::setPollingConfigImpl(const NetworkChangePollingConfig& config)
{
   mConfig = config;
   DebugLog(<< "NetworkChangePollingImpl::setPollingConfigImpl(): " << config);
   NetworkChangePollingState* state = mFactory->getState(mStateType);
   if (state)
   {
      PollingConfigUpdateEvent evt;
      state->handleConfigUpdateEvent(this, evt);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::setPollingConfigImpl(): invalid state");
      assert(0);
   }
}

void NetworkChangePollingImpl::createNetworkStatusEvent(NetworkStatusEvent& statusEvt)
{
   NetworkInterfaceQueryEvent queryEvt;
   if (mHandler)
   {
      mHandler->onNetworkInterfaceQuery(queryEvt);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::createNetworkStatusEvent(): polling handler not initialized");
      return;
   }

   // in case of VPN IP address does not change, only the transport
   statusEvt.currentTransport = queryEvt.transport;
   convertTo(queryEvt.interfaces, statusEvt.currentInterfaces);
   statusEvt.previousTransport = mTransport;
   convertTo(mInterfaces, statusEvt.previousInterfaces);
}

void NetworkChangePollingImpl::sendNetworkChangePublishEvent()
{
   NetworkTransport transport = getTransport();
   std::set<resip::Data>& interfaces = getInterfaces();
   NetworkChangePublishEvent publishEvent;
   publishEvent.transport = transport;
   convertTo(interfaces, publishEvent.interfaces);

   DebugLog(<< "NetworkChangePollingNotifyState::sendNetworkChangePublishEvent(): sendNetworkChangeEvent for transport: " << transport << " interfaces: " << NetworkChangePollingImpl::getInterfaceList(interfaces));

   if (mHandler)
   {
      mHandler->onPublishNetworkChange(publishEvent);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::sendNetworkChangePublishEvent(): polling handler not initialized");
      return;
   }
}

void NetworkChangePollingImpl::resetStatusTimer()
{
   if (mStatusTimer)
   {
      mStatusTimer->cancel();
      mStatusTimer->expires_from_now(mConfig.statusIntervalMsecs);
      mStatusTimer->async_wait(this, NETWORK_CHANGE_POLLING_STATUS_TIMER_ID, NULL);
      // DebugLog(<< "NetworkChangePollingImpl::resetStatusTimer(): state: " << mStateType);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::resetStatusTimer(): status timer not initialized");
   }
}

void NetworkChangePollingImpl::resetNotifyTimer()
{
   if (mNotifyTimer)
   {
      mNotifyTimer->cancel();
      mNotifyTimer->expires_from_now(mConfig.notifyIntervalMsecs);
      mNotifyTimer->async_wait(this, NETWORK_CHANGE_POLLING_NOTIFY_TIMER_ID, NULL);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::resetNotifyTimer(): notify timer not initialized");
   }
}

void NetworkChangePollingImpl::resetStabilizeTimer()
{
   if (mStabilizeTimer)
   {
      mStabilizeTimer->cancel();
      mStabilizeTimer->expires_from_now(mConfig.stabilizeIntervalMsecs);
      mStabilizeTimer->async_wait(this, NETWORK_CHANGE_POLLING_STABILIZE_TIMER_ID, NULL);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingImpl::resetStabilizeTimer(): stabilize timer not initialized");
   }
}

void NetworkChangePollingImpl::cancelTimers()
{
   cancelStatusTimer();
   cancelNotifyTimer();
   cancelStabilizeTimer();
}

void NetworkChangePollingImpl::cancelStatusTimer()
{
   if (mStatusTimer)
   {
      mStatusTimer->cancel();
   }
}

void NetworkChangePollingImpl::cancelNotifyTimer()
{
   if (mNotifyTimer)
   {
      mNotifyTimer->cancel();
   }
}

void NetworkChangePollingImpl::cancelStabilizeTimer()
{
   if (mStabilizeTimer)
   {
      mStabilizeTimer->cancel();
   }
}

void NetworkChangePollingImpl::onTimer(unsigned short timerId, void* appState)
{
   /*
   // Commenting out for now, crashes on linux platform
   if (mManager && mManager->isShutdown())
   {
      InfoLog(<< "NetworkChangePollingImpl::onTimer(): in shutdown mode");
      return;
   }
   */

   NetworkChangePollingState* state = mFactory->getState(mStateType);
   if (!state)
   {
      InfoLog(<< "NetworkChangePollingImpl::onTimer(): invalid state");
      assert(0);
      return;
   }

   switch (timerId)
   {
      case NETWORK_CHANGE_POLLING_STATUS_TIMER_ID:
      {
         // StackLog(<< "NetworkChangePollingImpl::onTimer(): timerId: " << timerId << " status timeout");
         StatusTimeoutEvent statusEvt;
         state->handleStatusTimeoutEvent(this, statusEvt);
         break;
      }
      case NETWORK_CHANGE_POLLING_NOTIFY_TIMER_ID:
      {
         // StackLog(<< "NetworkChangePollingImpl::onTimer(): timerId: " << timerId << " notify timeout");
         NotifyTimeoutEvent notifyEvt;
         state->handleNotifyTimeoutEvent(this, notifyEvt);
         break;
      }
      case NETWORK_CHANGE_POLLING_STABILIZE_TIMER_ID:
      {
         // StackLog(<< "NetworkChangePollingImpl::onTimer(): timerId: " << timerId << " stabilize timeout");
         StabilizeTimeoutEvent stabilizeEvt;
         state->handleStabilizeTimeoutEvent(this, stabilizeEvt);
         break;
      }
      default:
      {
         InfoLog(<< "NetworkChangePollingImpl::onTimer(): timerId: " << timerId << " is invalid");
         assert(0);
         break;
      }
   }
}

void NetworkChangePollingImpl::convertTo(const NetworkInterfaces& interfaces, std::set<resip::Data>& interfacesSet)
{
   interfacesSet.clear();
   for (NetworkInterfaces::const_iterator i = interfaces.begin(); i != interfaces.end(); ++i)
   {
      interfacesSet.insert((*i).c_str());
   }
}

void NetworkChangePollingImpl::convertTo(const std::set<resip::Data>& interfacesSet, NetworkInterfaces& interfaces)
{
   interfaces.clear();
   for (std::set<resip::Data>::const_iterator i = interfacesSet.begin(); i != interfacesSet.end(); ++i)
   {
      interfaces.push_back((*i).c_str());
   }
}

void NetworkChangePollingImpl::convertTo(const std::set<resip::Data>& interfacesData, std::set<std::string>& interfacesString)
{
   interfacesString.clear();
   for (std::set<resip::Data>::const_iterator i = interfacesData.begin(); i != interfacesData.end(); i++)
   {
      interfacesString.insert((*i).c_str());
   }
}

void NetworkChangePollingImpl::convertTo(const std::set<std::string>& interfacesString, std::set<resip::Data>& interfacesData)
{
   interfacesData.clear();
   for (std::set<std::string>::const_iterator i = interfacesString.begin(); i != interfacesString.end(); i++)
   {
      interfacesData.insert((*i).c_str());
   }
}

void NetworkChangePollingImpl::convertTo(const std::set<std::string>& interfacesString, cpc::vector<cpc::string>& interfaces)
{
   interfaces.clear();
   for (std::set<std::string>::const_iterator i = interfacesString.begin(); i != interfacesString.end(); i++)
   {
      interfaces.push_back((*i).c_str());
   }
}

void NetworkChangePollingImpl::convertTo(const cpc::vector<cpc::string>& interfaces, std::set<std::string>& interfacesString)
{
   interfacesString.clear();
   for (cpc::vector<cpc::string>::const_iterator i = interfaces.begin(); i != interfaces.end(); i++)
   {
      interfacesString.insert((*i).c_str());
   }
}

std::string NetworkChangePollingImpl::getInterfaceList(const std::set<resip::Data>& interfaces)
{
   std::stringstream ss;
   ss << "{";
   for (std::set<resip::Data>::const_iterator i = interfaces.begin(); i != interfaces.end(); i++)
   {
      if (i == interfaces.begin())
      {
         ss << " " << (*i).c_str() << " ";
      }
      else
      {
         ss << (*i).c_str() << " ";
      }
   }
   ss << "}";
   return ss.str();
}

NetworkChangePollingImpl::NetworkChangePollingState::NetworkChangePollingState(NetworkChangePollingStateType state) :
mState(state)
{
}

NetworkChangePollingImpl::NetworkChangePollingState::~NetworkChangePollingState()
{
}

NetworkChangePollingStateType NetworkChangePollingImpl::NetworkChangePollingState::getState()
{
   return mState;
}

std::string NetworkChangePollingImpl::NetworkChangePollingState::getName()
{
   return (getName(mState));
}

std::string NetworkChangePollingImpl::NetworkChangePollingState::getName(NetworkChangePollingStateType stateType)
{
   std::string name("Unknown");
   switch (stateType)
   {
      case NetworkChangePollingStateType_Idle: name = "Idle"; break;
      case NetworkChangePollingStateType_Notify: name = "Notify"; break;
      case NetworkChangePollingStateType_Stabilize: name = "Stabilize"; break;
      case NetworkChangePollingStateType_Sync: name = "Sync"; break;
      default: break;
   }

   return name;
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleNetworkInitializeEvent(NetworkChangePollingImpl* impl, const NetworkInitializeEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleNetworkInitializeEvent(): ignoring network initialize event in current state: " << getName());
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleNetworkShutdownEvent(NetworkChangePollingImpl* impl, const NetworkShutdownEvent& evt)
{
   InfoLog(<< "NetworkChangePollingState::handleNetworkShutdownEvent(): network shutdown event in current state: " << getName());
   impl->changeState(NetworkChangePollingStateType_Idle); return;
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleNetworkChangeEvent(): ignoring network status event in current state: " << getName() << " " << evt);
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleStatusTimeoutEvent(): ignoring status timeout event in current state: " << getName() << " current-transport: " << impl->getTransport() << " interface-list: " << impl->getInterfaces().size());
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleNotifyTimeoutEvent(NetworkChangePollingImpl* impl, const NotifyTimeoutEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleNotifyTimeoutEvent(): ignoring notify timeout event in current state: " << getName() << " current-transport: " << impl->getTransport() << " interface-list: " << impl->getInterfaces().size());
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleStabilizeTimeoutEvent(NetworkChangePollingImpl* impl, const StabilizeTimeoutEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleStabilizeTimeoutEvent(): ignoring stabilize timeout event in current state: " << getName() << " current-transport: " << impl->getTransport() << " interface-list: " << impl->getInterfaces().size());
}

void NetworkChangePollingImpl::NetworkChangePollingState::handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt)
{
   StackLog(<< "NetworkChangePollingState::handleConfigUpdateEvent(): ignoring config update event in current state: " << getName() << " current-transport: " << impl->getTransport() << " interface-list: " << impl->getInterfaces().size());
}

NetworkChangePollingImpl::NetworkChangePollingIdleState::NetworkChangePollingIdleState() :
NetworkChangePollingState(NetworkChangePollingStateType_Idle)
{
}

NetworkChangePollingImpl::NetworkChangePollingIdleState::~NetworkChangePollingIdleState()
{
}

void NetworkChangePollingImpl::NetworkChangePollingIdleState::onEntry(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingIdleState::onEntry()");
   NetworkInterfaces interfaces;
   impl->cancelTimers();
   impl->setTransport(CPCAPI2::TransportNone);
   NetworkChangePollingImpl::convertTo(interfaces, impl->getInterfaces());
}

void NetworkChangePollingImpl::NetworkChangePollingIdleState::onExit(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingIdleState::onExit()");
}

void NetworkChangePollingImpl::NetworkChangePollingIdleState::handleNetworkInitializeEvent(NetworkChangePollingImpl* impl, const NetworkInitializeEvent& evt)
{
   NetworkInterfaceQueryEvent queryEvt;
   if (impl->getHandler())
   {
      impl->getHandler()->onNetworkInterfaceQuery(queryEvt);
   }
   else
   {
      InfoLog(<< "NetworkChangePollingIdleState::handleNetworkInitializeEvent(): polling handler not initialized");
      return;
   }
   impl->setTransport(queryEvt.transport);
   NetworkChangePollingImpl::convertTo(queryEvt.interfaces, impl->getInterfaces());
   DebugLog(<< "NetworkChangePollingIdleState::handleNetworkInitializeEvent(): transport initialized to: " << queryEvt.transport << " with " << queryEvt.interfaces.size() << " interfaces");
   impl->sendNetworkChangePollingStatusEvent();
   impl->changeState(NetworkChangePollingStateType_Sync); return;
}

NetworkChangePollingImpl::NetworkChangePollingNotifyState::NetworkChangePollingNotifyState() :
NetworkChangePollingState(NetworkChangePollingStateType_Notify)
{
}

NetworkChangePollingImpl::NetworkChangePollingNotifyState::~NetworkChangePollingNotifyState()
{
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::onEntry(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingNotifyState::onEntry()");
   impl->resetStatusTimer();
   impl->resetNotifyTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::onExit(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingNotifyState::onExit()");
   impl->cancelTimers();
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt)
{
   impl->setTransport(evt.currentTransport);
   NetworkChangePollingImpl::convertTo(evt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(evt))
   {
      // transport changed, reset notification delay for network stabilization
      DebugLog(<< "NetworkChangePollingNotifyState::handleNetworkStatusEvent(): network status event in state: " << getName() << " " << evt);
      impl->resetNotifyTimer();
      impl->sendNetworkChangePollingNotificationResetEvent();
   }
   else
   {
      DebugLog(<< "NetworkChangePollingNotifyState::handleNetworkStatusEvent(): ignoring network status event in state: " << getName() << " " << evt);
   }
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt)
{
   NetworkStatusEvent statusEvt;
   impl->createNetworkStatusEvent(statusEvt);

   // StackLog(<< "NetworkChangePollingNotifyState::handleStatusTimeoutEvent(): wait for sleep interval or next network change trigger: " << statusEvt);

   impl->setTransport(statusEvt.currentTransport);
   NetworkChangePollingImpl::convertTo(statusEvt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(statusEvt))
   {
      // transport changed, reset notification delay for network stabilization
      DebugLog(<< "NetworkChangePollingNotifyState::handleStatusTimeoutEvent(): state: " << getName() << " " << statusEvt);
      impl->resetNotifyTimer();
      impl->sendNetworkChangePollingNotificationResetEvent();
   }

   impl->resetStatusTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::handleNotifyTimeoutEvent(NetworkChangePollingImpl* impl, const NotifyTimeoutEvent& evt)
{
   DebugLog(<< "NetworkChangePollingNotifyState::handleNotifyTimeoutEvent(): sendNetworkChangeEvent for transport: " << impl->getTransport());
   impl->sendNetworkChangePublishEvent();

   // ignore interface changes for the stabilization duration once a a network-change status event has been sent
   impl->changeState(NetworkChangePollingStateType_Stabilize); return;
}

void NetworkChangePollingImpl::NetworkChangePollingNotifyState::handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt)
{
   DebugLog(<< "NetworkChangePollingNotifyState::handleConfigUpdateEvent(): transport: " << impl->getTransport());
   impl->resetStatusTimer();
   impl->resetNotifyTimer();
}

bool NetworkChangePollingImpl::NetworkChangePollingNotifyState::shouldNotify(const NetworkStatusEvent& evt)
{
   // ignore interface changes
   bool transportChanged = (evt.currentTransport != evt.previousTransport);
   if (transportChanged)
   {
      return true;
   }
   return false;
}

NetworkChangePollingImpl::NetworkChangePollingStabilizeState::NetworkChangePollingStabilizeState() :
NetworkChangePollingState(NetworkChangePollingStateType_Stabilize)
{
}

NetworkChangePollingImpl::NetworkChangePollingStabilizeState::~NetworkChangePollingStabilizeState()
{
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::onEntry(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingStabilizeState::onEntry()");
   impl->resetStatusTimer();
   impl->resetStabilizeTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::onExit(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingStabilizeState::onExit()");
   impl->cancelTimers();
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt)
{
   impl->setTransport(evt.currentTransport);
   NetworkChangePollingImpl::convertTo(evt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(evt))
   {
      // transport changed, delay notification until network stabilization
      DebugLog(<< "NetworkChangePollingStabilizeState::handleNetworkStatusEvent(): network status event in state: " << getName() << " " << evt);
      impl->sendNetworkChangePollingNotificationResetEvent();
      impl->changeState(NetworkChangePollingStateType_Notify); return;
   }
   else
   {
      DebugLog(<< "NetworkChangePollingStabilizeState::handleNetworkStatusEvent(): ignoring network status event in state: " << getName() << " " << evt);
   }
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt)
{
   NetworkStatusEvent statusEvt;
   impl->createNetworkStatusEvent(statusEvt);

   // StackLog(<< "NetworkChangePollingStabilizeState::handleStatusTimeoutEvent(): wait for sleep interval or next network change trigger: " << statusEvt);

   impl->setTransport(statusEvt.currentTransport);
   NetworkChangePollingImpl::convertTo(statusEvt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(statusEvt))
   {
      // transport changed, delay notification until network stabilization
      DebugLog(<< "NetworkChangePollingStabilizeState::handleStatusTimeoutEvent(): state: " << getName() << " " << statusEvt);
      impl->sendNetworkChangePollingNotificationResetEvent();
      impl->changeState(NetworkChangePollingStateType_Notify); return;
   }

   impl->resetStatusTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::handleStabilizeTimeoutEvent(NetworkChangePollingImpl* impl, const StabilizeTimeoutEvent& evt)
{
   DebugLog(<< "NetworkChangePollingStabilizeState::handleNotifyStabilizeEvent()");
   impl->changeState(NetworkChangePollingStateType_Sync);
}

void NetworkChangePollingImpl::NetworkChangePollingStabilizeState::handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt)
{
   DebugLog(<< "NetworkChangePollingStabilizeState::handleConfigUpdateEvent(): transport: " << impl->getTransport());
   impl->resetStatusTimer();
   impl->resetStabilizeTimer();
}

bool NetworkChangePollingImpl::NetworkChangePollingStabilizeState::shouldNotify(const NetworkStatusEvent& evt)
{
   // ignore interface changes
   bool transportChanged = (evt.currentTransport != evt.previousTransport);
   if (transportChanged)
   {
      return true;
   }
   return false;
}

NetworkChangePollingImpl::NetworkChangePollingSyncState::NetworkChangePollingSyncState() :
NetworkChangePollingState(NetworkChangePollingStateType_Sync)
{
}

NetworkChangePollingImpl::NetworkChangePollingSyncState::~NetworkChangePollingSyncState()
{
}

void NetworkChangePollingImpl::NetworkChangePollingSyncState::onEntry(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingSyncState::onEntry()");
   impl->resetStatusTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingSyncState::onExit(NetworkChangePollingImpl* impl)
{
   StackLog(<< "NetworkChangePollingSyncState::onExit()");
   impl->cancelStatusTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingSyncState::handleNetworkStatusEvent(NetworkChangePollingImpl* impl, const NetworkStatusEvent& evt)
{
   DebugLog(<< "NetworkChangePollingSyncState::handleNetworkStatusEvent(): network status event in state: " << getName() << " " << evt);

   impl->setTransport(evt.currentTransport);
   NetworkChangePollingImpl::convertTo(evt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(evt))
   {
      // interface or transport changed, delay notification until network stabilization
      impl->changeState(NetworkChangePollingStateType_Notify); return;
   }
}

void NetworkChangePollingImpl::NetworkChangePollingSyncState::handleStatusTimeoutEvent(NetworkChangePollingImpl* impl, const StatusTimeoutEvent& evt)
{
   NetworkStatusEvent statusEvt;
   impl->createNetworkStatusEvent(statusEvt);

   // StackLog(<< "NetworkChangePollingSyncState::handleStatusTimeoutEvent(): wait for sleep interval or next network change trigger: " << statusEvt);

   impl->setTransport(statusEvt.currentTransport);
   NetworkChangePollingImpl::convertTo(statusEvt.currentInterfaces, impl->getInterfaces());
   if (shouldNotify(statusEvt))
   {
      // interface or transport changed, delay notification until network stabilization
      impl->changeState(NetworkChangePollingStateType_Notify); return;
   }

   impl->resetStatusTimer();
}

void NetworkChangePollingImpl::NetworkChangePollingSyncState::handleConfigUpdateEvent(NetworkChangePollingImpl* impl, const PollingConfigUpdateEvent& evt)
{
   DebugLog(<< "NetworkChangePollingSyncState::handleConfigUpdateEvent(): transport: " << impl->getTransport());
   impl->resetStatusTimer();
}

bool NetworkChangePollingImpl::NetworkChangePollingSyncState::shouldNotify(const NetworkStatusEvent& evt)
{
   bool transportChanged = (evt.currentTransport != evt.previousTransport);
   bool interfacesChanged = !(evt.currentInterfaces == evt.previousInterfaces);
   // DebugLog(<< "NetworkChangePollingSyncState::shouldNotify(): transport-changed: " << transportChanged << " interfaces-changed: " << interfacesChanged);
   if (transportChanged || interfacesChanged)
   {
      return true;
   }
   return false;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkTransport& type)
{
   std::string transport = "unknown";
   switch (type)
   {
      case CPCAPI2::TransportNone: transport = "none"; break;
      case CPCAPI2::TransportWiFi: transport = "wifi"; break;
      case CPCAPI2::TransportWWAN: transport = "wwan"; break;
      default: break;
   }
   os << transport;
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkStatusEvent& evt)
{
   os << "current-transport: " << evt.currentTransport << " previous-transport: " << evt.previousTransport << " current-interface-count: " << evt.currentInterfaces.size() << " previous-interfaces-count: " << evt.previousInterfaces.size();
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkInterfaceQueryEvent& evt)
{
   os << "transport: " << evt.transport << " interfaces: ";
   for (NetworkInterfaceSet::const_iterator i = evt.interfaces.begin(); i != evt.interfaces.end(); i++)
      os << (*i) << " ";
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePublishEvent& evt)
{
   os << "transport: " << evt.transport << " interfaces: ";
   for (NetworkInterfaceSet::const_iterator i = evt.interfaces.begin(); i != evt.interfaces.end(); i++)
      os << (*i) << " ";
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStatusEvent& evt)
{
   os << "state: " << NetworkChangePollingImpl::NetworkChangePollingState::getName(evt.state) << " transport: " << evt.transport << " interfaces: ";
   for (NetworkInterfaces::const_iterator i = evt.interfaces.begin(); i != evt.interfaces.end(); i++)
      os << (*i).c_str() << " ";
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingStateType& type)
{
   os << NetworkChangePollingImpl::NetworkChangePollingState::getName(type);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::NetworkChangePollingConfig& config)
{
   os << " status-timeout: " << config.statusIntervalMsecs << " notify-timeout: " << config.notifyIntervalMsecs << " stabilize-timeout: " << config.stabilizeIntervalMsecs;
   return os;
}

}
