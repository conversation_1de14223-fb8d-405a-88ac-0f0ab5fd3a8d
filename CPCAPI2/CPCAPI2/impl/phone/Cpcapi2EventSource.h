#pragma once

#ifndef CPCAPI2_CPCAPI2_EVENT_SOURCE_H
#define CPCAPI2_CPCAPI2_EVENT_SOURCE_H

#include <cpcapi2defs.h>
#include "../util/AutoTestProcessor.h"
#include "impl/util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <rutil/Reactor.hxx>
#include <rutil/MultiReactor.hxx>

#include <set>
#include <functional>

// #include "../util/cpc_logger.h"


namespace CPCAPI2
{

// #define EVLOG(enabled, log) if (enabled) GenericLog(CPCAPI2::CPCAPI2_Subsystem::PHONE, resip::Log::Info, log);

class CallbackFifoHolder
{

public:

   CallbackFifoHolder()
   {
   }

   resip::Fifo<resip::ReadCallbackBase>& callbackFifo()
   {
      return mCallbackFifo;
   }

   void invokeCallbackHook()
   {
      if (mCbHook)
      {
         mCbHook();
      }
   }

   void setCallbackHook(void(*cbHook)(void*), void* context)
   {
      if (context && mCbHook && cbHook)
      {
         // overwriting an existing cbhook -- are you sure?
         assert(0);
      }
      else if (cbHook == NULL)
      {
         mCbHook = NULL;
      }
      else
      {
         mCbHook = std::bind(cbHook, context);
      }
   }

private:

   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   std::function<void(void)> mCbHook;

};


template<typename THandle, typename THandler, typename TSyncHandler>
#ifdef CPCAPI2_AUTO_TEST
class EventSource : public AutoTestProcessor
#else
class EventSource
#endif
{

public:

   // bool mLog;

   typedef std::map<THandle, THandler*> AppHandlerMap;
   typedef typename AppHandlerMap::iterator apphandlermap_iterator;

   typedef std::set<THandler*> SdkObservers;
   typedef typename SdkObservers::iterator thandler_iterator;
   typedef std::map<THandle, SdkObservers> ObserverHandlerMap;
   typedef typename ObserverHandlerMap::iterator observerhandlermap_iterator;

   typedef std::set<std::weak_ptr<THandler>, std::owner_less<std::weak_ptr<THandler>>> SdkObserversSafe;
   typedef typename SdkObserversSafe::iterator thandlersafe_iterator;

   virtual ~EventSource()
   {
      mShutdown = true;
   }

   virtual int process(unsigned int timeout)
   {
      // -1 == no wait
      if (mShutdown)
      {
         return CPCAPI2::kModuleDisabled;
      }
      resip::ReadCallbackBase* fp = mCallbackFifoHolder->callbackFifo().getNext(timeout);
      while (fp)
      {
         (*fp)();
         delete fp;
         if (mShutdown)
         {
            return CPCAPI2::kModuleDisabled;
         }
         fp = mCallbackFifoHolder->callbackFifo().getNext(CPCAPI2::kBlockingModeNonBlocking);
      }
      return kSuccess;
   }

#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout)
   {
      // -1 == no wait
      if (mShutdown)
      {
         return NULL;
      }
      resip::ReadCallbackBase* rcb = mCallbackFifoHolder->callbackFifo().getNext(timeout);
      AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
      if (fpCmd != NULL)
      {
         return fpCmd;
      }
      if (rcb != NULL)
      {
         return new AutoTestReadCallback(rcb, "", std::make_tuple(0, 0));
      }
      return NULL;
   }
#endif

   virtual void interruptProcess()
   {
      mReactor.getAsyncProcessHandler()->handleProcessNotification();
   }

   virtual void setCallbackHook(void(*cbHook)(void*), void* context)
   {
      if (mReactor.isCurrentThread())
      {
         setCallbackHookImpl(cbHook, context);
      }
      else
      {
         resip::ReadCallbackBase* f = resip::resip_bind(&EventSource<THandle, THandler, TSyncHandler>::setCallbackHookImpl, this, cbHook, context);
         if (cbHook)
         {
            mReactor.post(f);
         }
         else
         {
            mReactor.execute(f);
         }
      }
   }
   
   virtual void setCallbackHookImpl(void(*cbHook)(void*), void* context)
   {
      mCallbackFifoHolder->setCallbackHook(cbHook, context);
   }

   virtual int setAppHandler(THandle h, THandler* handler)
   {
      // setAppHandler is called in different situations currently, sometimes
      // it is already inside of the XYZImpl handler and is currently running
      // in the reactor, and sometimes it is invoked from the impl. Although I
      // don't like methods which behave differently based on thread contexts,
      // the simplest solution here is to call setAppHandlerImpl directly if
      // we're running in the reactor.
      if (mReactor.isCurrentThread())
      {
         setAppHandlerImpl(h, handler);
      }
      else
      {
         resip::ReadCallbackBase* f = resip::resip_bind(&EventSource<THandle, THandler, TSyncHandler>::setAppHandlerImpl, this, h, handler);
         if (handler)
         {
            mReactor.post(f);
         }
         else
         {
            // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on our reactor's thread.
            // we block so that we can guarantee that when this setAppHandler(..) method returns, the SDK will not call the existing
            // handler
            mReactor.execute(f);

            // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
            // use to remove
            process(-1);
         }
      }
      return 0;
   }

   void setAppHandlerImpl(THandle h, THandler* handler)
   {
      mAppHandlers[h] = handler;
   }

   virtual int addSdkObserver(THandle h, THandler* observer)
   {
      if (mReactor.isCurrentThread())
      {
         addSdkObserverImpl(h, observer);
      }
      else
      {
         resip::ReadCallbackBase* f = resip::resip_bind(&EventSource<THandle, THandler, TSyncHandler>::addSdkObserverImpl, this, h, observer);
         mReactor.post(f);
      }

      return 0;
   }

   void addSdkObserverImpl(THandle h, THandler* observer)
   {
      if (observer)
      {
         observerhandlermap_iterator itObservers = mSdkObserversScoped.find(h);
         if (itObservers == mSdkObserversScoped.end())
         {
            SdkObservers observers;
            observers.insert(observer);
            mSdkObserversScoped[h] = observers;
         }
         else
         {
            itObservers->second.insert(observer);
         }
      }
   }

   virtual int removeSdkObserver(THandle h, THandler* observer)
   {
      if (observer)
      {
         if (mReactor.isCurrentThread())
         {
            // If remove is called on same thread, cannot block the thread as it will cause a deadlock, handle it right away
            removeSdkObserverImpl(h, observer);
         }
         else
         {
            resip::ReadCallbackBase* f = resip::resip_bind(&EventSource<THandle, THandler, TSyncHandler>::removeSdkObserverImpl, this, h, observer);

            // We may have a dangling pointer if the observer is destroyed, block the thread and process events in queue
            mReactor.execute(f);
            process(-1);
         }
      }

      return 0;
   }

   void removeSdkObserverImpl(THandle h, THandler* observer)
   {
      if (observer)
      {
         observerhandlermap_iterator itObservers = mSdkObserversScoped.find(h);
         if (itObservers != mSdkObserversScoped.end())
         {
            itObservers->second.erase(observer);
            if (itObservers->second.size() == 0)
            {
               mSdkObserversScoped.erase(h);
            }
         }
      }
   }

   virtual void addSdkObserver(THandler* obs)
   {
      assert(mReactor.isCurrentThread());
      mSdkObservers.insert(obs);
   }

   virtual void removeSdkObserver(THandler* obs)
   {
      assert(mReactor.isCurrentThread());
      mSdkObservers.erase(obs);
   }

   virtual void addSdkObserverSafe(const std::shared_ptr<THandler>& obs)
   {
      assert(mReactor.isCurrentThread());
      mSdkObserversSafe.insert(std::weak_ptr<THandler>(obs));
   }

   virtual void removeSdkObserverSafe(const std::shared_ptr<THandler>& obs)
   {
      assert(mReactor.isCurrentThread());
      for (thandlersafe_iterator itObs = mSdkObserversSafe.begin(); itObs != mSdkObserversSafe.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            if (handler.get() == obs.get())
            {
               mSdkObserversSafe.erase(itObs);
               break;
            }
         }
      }
   }

   virtual bool isSdkObserver(THandle h, THandler* obs)
   {
      assert(mReactor.isCurrentThread());
      observerhandlermap_iterator itObservers = mSdkObserversScoped.find(h);
      if (itObservers == mSdkObserversScoped.end()) return false;
      thandler_iterator itHandlers = itObservers->second.find(obs);
      if (itHandlers == itObservers->second.end()) return false;
      return true;
   }

   virtual SdkObservers getSdkObservers(THandle h)
   {
      assert(mReactor.isCurrentThread());
      SdkObservers observers;
      observerhandlermap_iterator itObservers = mSdkObserversScoped.find(h);
      if (itObservers != mSdkObserversScoped.end())
      {
         observers = itObservers->second;
      }
      return observers;
   }

   std::shared_ptr<CallbackFifoHolder> callbackFifoHolder()
   {
      return mCallbackFifoHolder;
   }

   // Added just so that it can be overridden (apparently it is not possible to
   // have a virtual member function if the arguments are parameterized)
   virtual void logEvent(THandle handle, const char *funcName, const char *eventName)
   {
   }

protected:

   /* EventSource with its own callback FIFO; process(..) should be called regularly
   */
   EventSource(resip::MultiReactor& r) :
      EventSource(r, std::shared_ptr< CallbackFifoHolder >(new CallbackFifoHolder()))
   {
   }

   /* EventSource using an existing callback FIFO; process(..) not expected to be called on this EventSource
   */
   EventSource(resip::MultiReactor& r,
               std::shared_ptr< CallbackFifoHolder > callbackFifoHolder) :
      mShutdown(false), mReactor(r), mCallbackFifoHolder(callbackFifoHolder)
   {
   }

   void executeOnSdkThread(resip::ReadCallbackBase* command)
   {
      mReactor.execute(command);
   }

   void postToSdkThread(resip::ReadCallbackBase* command)
   {
      mReactor.post(command);
   }

   void postMSToSdkThread(resip::ReadCallbackBase* command, int msecs)
   {
      mReactor.postMS(command, msecs);
   }

   void postCallback(resip::ReadCallbackBase* command)
   {
      mCallbackFifoHolder->callbackFifo().add(command);
      mCallbackFifoHolder->invokeCallbackHook();
   }

   /* fires an event to internal SDK observers and/or application handler.
    * THandle h is used to look up which application handler to fire the event to, and is typically an account handle.
    * THandle h will also be passed as the first argument in the event method call.
    */
   template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, THandle h, const TEvt& eventSecondArg)
   {
      logEvent( h, funcName, typeid( eventSecondArg ).name() );
      fireEvent(funcName, func, h, h, eventSecondArg);
   }

   template<typename TFn, typename TEvt> void fireEventObservers(const char* funcName, TFn func, const TEvt& args)
   {
      if (mShutdown)
      {
         return;
      }

      logEvent(0, funcName, typeid(args).name());

      for (thandlersafe_iterator itObs = mSdkObserversSafe.begin(); itObs != mSdkObserversSafe.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            resip::ReadCallbackBase* cb = resip::resip_bind(func, handler.get(), args);
            if (dynamic_cast<TSyncHandler*>(handler.get()) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else
            {
               postCallback(cb);
            }
         }
         else
         {
            assert(0);
         }
      }

      for (thandler_iterator itObs = mSdkObservers.begin(); itObs != mSdkObservers.end(); ++itObs)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itObs, args);
         if (dynamic_cast<TSyncHandler*>(*itObs) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
   }

   template<typename TFn, typename TEvt> void fireEventObservers(const char* funcName, TFn func, THandle h, const TEvt& eventSecondArg)
   {
      if (mShutdown)
      {
         return;
      }

      logEvent(h, funcName, typeid(eventSecondArg).name());

      for (thandlersafe_iterator itObs = mSdkObserversSafe.begin(); itObs != mSdkObserversSafe.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            resip::ReadCallbackBase* cb = resip::resip_bind(func, handler.get(), h, eventSecondArg);
            if (dynamic_cast<TSyncHandler*>(handler.get()) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else
            {
               postCallback(cb);
            }
         }
         else
         {
            assert(0);
         }
      }

      for (thandler_iterator itObs = mSdkObservers.begin(); itObs != mSdkObservers.end(); ++itObs)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itObs, h, eventSecondArg);
         if (dynamic_cast<TSyncHandler*>(*itObs) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      observerhandlermap_iterator itObserver = mSdkObserversScoped.find(h);
      if (itObserver != mSdkObserversScoped.end())
      {
         for (thandler_iterator itObs = itObserver->second.begin(); itObs != itObserver->second.end(); ++itObs)
         {
            if (*itObs != NULL && (*itObs != reinterpret_cast<THandler*>(0xDEADBEFF)))
            {
               resip::ReadCallbackBase* cb = resip::resip_bind(func, *itObs, h, eventSecondArg);
               if (dynamic_cast<TSyncHandler*>(*itObs) != NULL)
               {
                  (*cb)();
                  delete cb;
               }
               else
               {
                  postCallback(cb);
               }
            }
         }
      }
   }

   /* fires an event to internal SDK observers and/or application handler.
    * This variant is useful when the first argument of a callback method is not the same handle used to register the app handler with.
    *
    * THandle h is used to look up which application handler to fire the event to, and is typically an account handle.
    * THandle2 eventFirstArg is passed as the first argument in the event method call.
    */
   template<typename TFn, typename TEvt, typename THandle2> void fireEvent(const char* funcName, TFn func, THandle h, THandle2 eventFirstArg, const TEvt& eventSecondArg)
   {
      if (mShutdown)
      {
         return;
      }

      logEvent( h, funcName, typeid( eventSecondArg ).name() );

      // EVLOG(mLog, << "EventSource::fireEvent(): " << this << " mCallbackFifoHolder: " << mCallbackFifoHolder << " observer count: " << mSdkObservers.size() << " scoped oberver count: " << mSdkObserversScoped.size() << " funcName: " << funcName << " handle: " << h << " handle2: " << eventFirstArg << " event: " << typeid(eventSecondArg).name());

      for (thandlersafe_iterator itObs = mSdkObserversSafe.begin(); itObs != mSdkObserversSafe.end(); ++itObs)
      {
         if (std::shared_ptr<THandler> handler = (*itObs).lock())
         {
            resip::ReadCallbackBase* cb = resip::resip_bind(func, handler.get(), eventFirstArg, eventSecondArg);
            if (dynamic_cast<TSyncHandler*>(handler.get()) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else
            {
               postCallback(cb);
            }
         }
         else
         {
            assert(0);
         }
      }

      for (thandler_iterator itObs = mSdkObservers.begin(); itObs != mSdkObservers.end(); ++itObs)
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, *itObs, eventFirstArg, eventSecondArg);
         if (dynamic_cast<TSyncHandler*>(*itObs) != NULL)
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }

      observerhandlermap_iterator itObserver = mSdkObserversScoped.find(eventFirstArg);
      if (itObserver != mSdkObserversScoped.end())
      {
         for (thandler_iterator itObs = itObserver->second.begin(); itObs != itObserver->second.end(); ++itObs)
         {
            if (*itObs != NULL && (*itObs != reinterpret_cast<THandler*>(0xDEADBEFF)))
            {
               resip::ReadCallbackBase* cb = resip::resip_bind(func, *itObs, eventFirstArg, eventSecondArg);
               if (dynamic_cast<TSyncHandler*>(*itObs) != NULL)
               {
                  (*cb)();
                  delete cb;
               }
               else
               {
                  postCallback(cb);
               }
            }
         }
      }

      apphandlermap_iterator itHandler = mAppHandlers.find(h);
      if (itHandler != mAppHandlers.end())
      {
         THandler* appHandler = itHandler->second;
         if (appHandler == (THandler*)0xDEADBEEF)
         {
            // should be an autotest running
            resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (THandler*)0xDEADBEEF, eventFirstArg, eventSecondArg);
            postCallback(autoTestCb);
         }
         else if (appHandler != NULL && (appHandler != reinterpret_cast<THandler*>(0xDEADBEFF)))
         {
            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, appHandler, eventFirstArg, eventSecondArg);
            if (dynamic_cast<TSyncHandler*>(appHandler) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else
            {
               postCallback(cb);
            }
         }
      }
   }

   template<typename TCompletionHandler, typename TFn, typename TEvt> void fireCompletionHandlerEvent(TCompletionHandler* completionHandler, const char* funcName, TFn func, THandle h, const TEvt& eventSecondArg)
   {
      if (mShutdown)
      {
         return;
      }

      logEvent(h, funcName, typeid(eventSecondArg).name());

      // EVLOG(mLog, << "EventSource::fireEvent(): " << this << " mCallbackFifoHolder: " << mCallbackFifoHolder << " observer count: " << mSdkObservers.size() << " scoped oberver count: " << mSdkObserversScoped.size() << " funcName: " << funcName << " handle: " << h << " handle2: " << eventFirstArg << " event: " << typeid(eventSecondArg).name());

#ifdef CPCAPI2_AUTO_TEST
      if (completionHandler != NULL && (completionHandler != reinterpret_cast<TCompletionHandler*>(0xDEADBEFF)))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, h, eventSecondArg);
         if (completionHandler->synchronous())
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
      else
      {
         resip::ReadCallbackBase* autoTestCb = makeFpCommandNew(funcName, func, (TCompletionHandler*)0xDEADBEEF, h, eventSecondArg);
         postCallback(autoTestCb);
      }
#else
      if (completionHandler != NULL && (completionHandler != reinterpret_cast<TCompletionHandler*>(0xDEADBEFF)))
      {
         resip::ReadCallbackBase* cb = resip::resip_bind(func, completionHandler, h, eventSecondArg);
         if (completionHandler->synchronous())
         {
            (*cb)();
            delete cb;
         }
         else
         {
            postCallback(cb);
         }
      }
#endif
   }

   AppHandlerMap mAppHandlers;
   ObserverHandlerMap mSdkObserversScoped;
   SdkObservers mSdkObservers;
   SdkObserversSafe mSdkObserversSafe;
   std::shared_ptr<CallbackFifoHolder> mCallbackFifoHolder;
   bool mShutdown;
   resip::MultiReactor& mReactor;

};

}

#endif // CPCAPI2_CPCAPI2_EVENT_SOURCE_H
