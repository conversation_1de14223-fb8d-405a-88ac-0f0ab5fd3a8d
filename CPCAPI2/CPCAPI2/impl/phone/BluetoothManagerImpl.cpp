#include "BluetoothManagerImpl.h"

#include <rutil/DnsUtil.hxx>
#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
BluetoothManagerImpl::BluetoothManagerImpl(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mNextInternalHandle(1)
{
}

BluetoothManagerImpl::BluetoothManagerImplHandle BluetoothManagerImpl::addInternalHandler(BluetoothHandler* handler)
{
   BluetoothManagerImplHandle handle = mNextInternalHandle++;
   mInternalHandlers[handle] = handler;
   return handle;
}

void BluetoothManagerImpl::removeInternalHandler(BluetoothManagerImplHandle handle)
{
   mInternalHandlers.erase(handle);
}

void BluetoothManagerImpl::sendBluetoothHeadsetUnavailableEvent(const BluetoothEvent& args)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&BluetoothManagerImpl::sendBluetoothHeadsetUnavailableEventImpl, this, args));
}
void BluetoothManagerImpl::sendBluetoothHeadsetUnavailableEventImpl(const BluetoothEvent& args)
{
   HandlersMap::iterator it = mInternalHandlers.begin();

   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onBluetoothHeadsetUnavailable(args);
   }
}

void BluetoothManagerImpl::sendBluetoothHeadsetAvailableEvent(const BluetoothEvent& args)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&BluetoothManagerImpl::sendBluetoothHeadsetAvailableEventImpl, this, args));
}
void BluetoothManagerImpl::sendBluetoothHeadsetAvailableEventImpl(const BluetoothEvent& args)
{
      HandlersMap::iterator it = mInternalHandlers.begin();

   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onBluetoothHeadsetAvailable(args);
   }
}

void BluetoothManagerImpl::sendBluetoothAudioDisconnectedEvent(const BluetoothEvent& args)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&BluetoothManagerImpl::sendBluetoothAudioDisconnectedEventImpl, this, args));
}
void BluetoothManagerImpl::sendBluetoothAudioDisconnectedEventImpl(const BluetoothEvent& args)
{
   HandlersMap::iterator it = mInternalHandlers.begin();

   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onBluetoothAudioDisconnected(args);
   }
}

void BluetoothManagerImpl::sendBluetoothAudioConnectedEvent(const BluetoothEvent& args)
{
   mPhone->getSdkModuleThread().post(resip::resip_bind(&BluetoothManagerImpl::sendBluetoothAudioConnectedEventImpl, this, args));
}
void BluetoothManagerImpl::sendBluetoothAudioConnectedEventImpl(const BluetoothEvent& args)
{
   HandlersMap::iterator it = mInternalHandlers.begin();

   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onBluetoothAudioConnected(args);
   }
}
}