

#include "NetworkChangeManager_Polling.h"
#include "../util/cpc_logger.h"

#include <rutil/DnsUtil.hxx>
#include <list>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{
NetworkChangeManager_Polling::NetworkChangeManager_Polling(Phone* phone) :
   NetworkChangeManagerImpl(phone),
   mPollingThread(NULL)
{
   mLastKnownNetworkTransport = (NetworkChangeManagerImpl::getLocalIPAddresses().empty() ? TransportNone : TransportWiFi);
}

NetworkChangeManager_Polling::~NetworkChangeManager_Polling()
{
   // Do not call delete directly, use release() instead
   if (mPollingThread)
   {
      ErrLog(<< "NetworkChangeManager_Polling deleted without release()");
      stop();
   }
}

void NetworkChangeManager_Polling::release()
{
   if (mPollingThread)
   {
      DebugLog(<< "NetworkChangeManager_Polling::release() shutting down");
      stop();
   }

   delete this;
}

void NetworkChangeManager_Polling::pollInterfacesThread()
{
   bool shouldNotify = false;
   int notifyWaitCnt = 0;
   const int sleepIntervalMs = 1200;
   std::set<resip::Data> lastRead = NetworkChangeManagerImpl::getLocalIPAddresses();
   while (!mShutdown)
   {
      std::set<resip::Data> ifaces = getLocalIPAddresses();
      if (ifaces == lastRead)
      {
#ifdef _WIN32
         ::Sleep(sleepIntervalMs); // OBELISK-4170 -- adjusting the system clock can affect sleep_for on windows
#else
         this_thread::sleep_for(chrono::milliseconds(sleepIntervalMs));
#endif

         notifyWaitCnt = (notifyWaitCnt >= 1000 ? 0 : notifyWaitCnt + 1);
         if (shouldNotify && (notifyWaitCnt % 5) == 0)
         {
            notifyWaitCnt = 0;
            shouldNotify = false;
            InfoLog(<< "NetworkChangeManager_Polling - sendNetworkChangeEvent (" << ifaces.size() << " interfaces)");
            NetworkChangeEvent params;
            if (ifaces.size() == 0)
            {
               params.networkTransport = TransportNone;
            }
            else
            {
               params.networkTransport = TransportWiFi;
               compareInterfaceLists(lastRead, ifaces, params.interfacesUp, params.interfacesDown);
            }
            sendNetworkChangeEvent(params);
         }
      }
      else
      {
         // interfaces changed, but don't notify until the list has stabilized
         lastRead = ifaces;
         shouldNotify = true;
         notifyWaitCnt = 0;
      }
   }
}

int NetworkChangeManager_Polling::start()
{
   if (mPollingThread)
   {
      ErrLog(<< "NetworkChangeManager_Polling attempting to start thread already started");
      return -1;
   }

   mLastKnownNetworkTransport = (NetworkChangeManagerImpl::getLocalIPAddresses().empty() ? TransportNone : TransportWiFi);
   mPollingThread = new thread(std::bind(&NetworkChangeManager_Polling::pollInterfacesThread, this));
   return 0;
}

int NetworkChangeManager_Polling::stop()
{
   mShutdown = true;
   if (mPollingThread)
   {
      mPollingThread->join();
   }
   delete mPollingThread;
   mPollingThread = NULL;
   mShutdown = false;
   return 0;
}
}
