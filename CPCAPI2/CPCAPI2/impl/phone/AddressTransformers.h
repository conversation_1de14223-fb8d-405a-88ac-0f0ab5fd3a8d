#pragma once

#if !defined(CPCAPI2_ADDRESS_TRANSFORMERS_H)
#define CPCAPI2_ADDRESS_TRANSFORMERS_H

#include "phone/AddressTransformer.h"

namespace CPCAPI2
{

class DefaultAddressTransformer : public AddressTransformer
{ 
public: 
   virtual int applyTransformation(const cpc::string& targetAddress, AddressTransformationContext context, cpc::string& transformedAddress); 
};

}

#endif // CPCAPI2_ADDRESS_TRANSFORMERS_H