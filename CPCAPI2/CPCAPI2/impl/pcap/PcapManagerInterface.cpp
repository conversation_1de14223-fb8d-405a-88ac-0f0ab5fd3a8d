#include "brand_branded.h"

#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
#include "cpcapi2utils.h"
#include "phone/Phone.h"	
#include "../util/DumFpCommand.h"
#include "pcap/PcapHandler.h"
#include "../util/IpHelpers.h"
#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

#include "rutil/GenericIPAddress.hxx"
#include <rutil/DnsUtil.hxx>
#include <rutil/Data.hxx>
#include "account/SipAccountInterface.h"
#include "account/SipAccountImpl.h"

#include "PcapManagerInterface.h"

// HAVE_REMOTE is for winpcap and not libpcap
#ifdef _WIN32
#define HAVE_REMOTE
#endif

#include "pcap.h"

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

namespace CPCAPI2
{
namespace Pcap
{
PcapManagerInterface::PcapManagerInterface(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mPcapHandle(NULL),
   mHandlerCallbackData({NULL, 0}),
   mProgressTimer(NULL),
   mShutdown(false)
{
   mPhone->addRefImpl();
}

PcapManagerInterface::~PcapManagerInterface()
{
   if (mPcapHandle != NULL)
   {
      StopImpl();
   }

   mShutdown = true;

   mPhone->releaseImpl();

   interruptProcess();
}

void PcapManagerInterface::Release()
{
   delete this;
}
	
int PcapManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}
   
void PcapManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}
   
void PcapManagerInterface::interruptProcess()
{
   mCallbackFifo.add(new resip::ReadCallbackNoOp);
}
   
void PcapManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

int PcapManagerInterface::setHandler(PcapHandler* handler)
{
   ReadCallbackBase* f = resip::resip_bind(&PcapManagerInterface::SetHandlerImpl, this, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:

      // 1. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      mPhone->getSdkModuleThread().execute(f);

      // 2. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

int PcapManagerInterface::SetHandlerImpl(PcapHandler* handler)
{
   mAppHandler = handler;
   return kSuccess;
}

int PcapManagerInterface::queryInterfaces()
{
   ReadCallbackBase* rcb = resip::resip_bind(&PcapManagerInterface::QueryInterfacesImpl, this);
   post(rcb);
   return kSuccess;
}

int PcapManagerInterface::QueryInterfacesImpl()
{
   pcap_if_t *allDevs;
   char errBuf[PCAP_ERRBUF_SIZE];
#ifdef _WIN32
   if (pcap_findalldevs_ex(PCAP_SRC_IF_STRING, NULL, &allDevs, errBuf) == -1)
#else
   if (pcap_findalldevs(&allDevs, errBuf) == -1)
#endif
   {
      Pcap::ErrorEvent args;
      args.errorText = "Could not retrieve list of devices. " + cpc::string(errBuf);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      return kError;
   }

   Pcap::QueryInterfacesSuccessEvent args;
   for (pcap_if_t* d = allDevs; d; d = d->next)
   {
      PcapInterface i;
      i.name = d->name;
      i.description = d->description;
      // Addresses can be NULL.
      if (d->addresses)
      {
         resip::Data ip = resip::DnsUtil::inet_ntop(*d->addresses->addr);
         i.addr = cpc::string(ip.c_str());
      }
      i.preferred = !strcmp(i.name, mCaptureInterfaceName.c_str());
      args.interfaces.push_back(i);
   }

   resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onQueryInterfacesSuccess, mAppHandler, args);
   postCallback(callback);

   return kSuccess;
}

int PcapManagerInterface::setCaptureInterface(const cpc::string& interfaceName)
{
   mCaptureInterfaceName = interfaceName;
   return kSuccess;
}

int PcapManagerInterface::start(const cpc::string& filePath)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PcapManagerInterface::StartImpl, this, filePath);
   post(rcb);
   return kSuccess;
}

/* prototype of the packet handler */
void packet_handler(u_char *param, const struct pcap_pkthdr *header, const u_char *pkt_data);

int PcapManagerInterface::StartImpl(const cpc::string& filePath)
{
   pcap_if_t *allDevs;
   pcap_if_t *d;
   char errBuf[PCAP_ERRBUF_SIZE];
   resip::Data preferredIp;

   CPCAPI2::SipAccount::SipAccountInterface* sipAccountManager = dynamic_cast<CPCAPI2::SipAccount::SipAccountInterface*>(CPCAPI2::SipAccount::SipAccountManager::getInterface(mPhone));
   cpc::vector<CPCAPI2::SipAccount::SipAccountHandle> sipAccountHandles = sipAccountManager->getAccountHandles();
   for (cpc::vector<CPCAPI2::SipAccount::SipAccountHandle>::const_iterator sipAccountIter = sipAccountHandles.begin(); sipAccountIter != sipAccountHandles.end(); sipAccountIter++)
   {
      CPCAPI2::SipAccount::SipAccountHandle sipAccountHandle = *sipAccountIter;
      CPCAPI2::SipAccount::SipAccountImpl* sipAccountImpl = sipAccountManager->getAccountImpl(sipAccountHandle);
      resip::Tuple sipTuple;
      if (sipAccountImpl->getSipServerIpFromLastReg(sipTuple))
      {
         CPCAPI2::IpHelpers::getPreferredLocalIpAddress(sipTuple, preferredIp);
         break;
      }
   }

   if (preferredIp.empty())
   {
      CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), preferredIp);
   }

#ifdef _WIN32
   if (pcap_findalldevs_ex(PCAP_SRC_IF_STRING, NULL, &allDevs, errBuf) == -1)
#else
   if (pcap_findalldevs( &allDevs, errBuf ) == - 1)
#endif
   {
      Pcap::ErrorEvent args;
      args.errorText = "Could not retrieve list of devices. " + cpc::string(errBuf);
      ErrLog(<< args.errorText);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      return kError;
   }

   bool supportsBroadcast = false;
   // Select capture device.
   for (d = allDevs; d; d = d->next)
   {
      cpc::string logStr = "Found capture interface: ";

      if (d->description)
      {
         logStr.append(d->description);
      }
      else
      {
         logStr.append("no description");
      }
      
      supportsBroadcast = d->addresses ? d->addresses->broadaddr != NULL : false;

      if (!mCaptureInterfaceName.empty())
      {
         if (strcmp(d->name, mCaptureInterfaceName.c_str()) == 0)
         {
            InfoLog(<< "Capture interface: " << d->name);
            break;
         }
      }
      else
      {
         /* Loopback Address*/
         if (d->flags & PCAP_IF_LOOPBACK)
            logStr.append(" loopback ");

         DebugLog(<< logStr);

         bool addressFound = false;
         for (pcap_addr_t *a = d->addresses; a; a = a->next)
         {
            resip::Data ip = resip::DnsUtil::inet_ntop(*a->addr);
            InfoLog(<< "IP address : " << ip);
            if (ip == preferredIp)
            {
               InfoLog(<< "This is the preferred interface");
               addressFound = true;
               break;
            }
         }
         if (addressFound == true)
         {
            break;
         }
      }
   }

   if (d == NULL)
   {
      Pcap::ErrorEvent args;
      args.errorText = "Could not select capture device.";
      ErrLog(<< args.errorText);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      return kError;
   }

#ifdef _WIN32
   if ((mPcapHandle = pcap_open(d->name, 65536, 0, 1000, NULL, errBuf)) == NULL)
#else
   if ((mPcapHandle = pcap_open_live(d->name, 65536, 0, 1000, errBuf )) == NULL)
#endif
   {
      Pcap::ErrorEvent args;
      args.errorText = "Unable to open the adapter. " + cpc::string(errBuf);
      ErrLog(<< args.errorText);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      pcap_freealldevs(allDevs);
      return kError;
   }

   struct bpf_program fp;
   if (supportsBroadcast)
   {
      if (pcap_compile(mPcapHandle, &fp, "ip and not broadcast and not multicast", 0, 0) < 0)
      {
         Pcap::ErrorEvent args;
         args.errorText = "Could not compile filter.";
         ErrLog(<< args.errorText << " " << pcap_geterr(mPcapHandle) );
         resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
         postCallback(callback);
         pcap_freealldevs(allDevs);
         pcap_close(mPcapHandle);
         mPcapHandle = 0;
         return kError;
      }

      if (pcap_setfilter(mPcapHandle, &fp) < 0)
      {
         Pcap::ErrorEvent args;
         args.errorText = "Error setting the filter.";
         ErrLog(<< args.errorText);
         resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
         postCallback(callback);
         pcap_freealldevs(allDevs);
         pcap_close(mPcapHandle);
         mPcapHandle = 0;
         return kError;
      }
   }

   if (pcap_setnonblock(mPcapHandle, 1, errBuf) == PCAP_ERROR)
   {
      Pcap::ErrorEvent args;
      args.errorText = "Error putting a capture handle into non-blocking mode. " + cpc::string(errBuf);
      ErrLog(<< args.errorText);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      pcap_freealldevs(allDevs);
      pcap_close(mPcapHandle);
      mPcapHandle = 0;

      return kError;
   }

   /* Open the dump file */
   mHandlerCallbackData.pcapDumpFile = pcap_dump_open(mPcapHandle, filePath.c_str());

   if (mHandlerCallbackData.pcapDumpFile == NULL)
   {
      Pcap::ErrorEvent args;
      args.errorText = "Error opening output file.";
      ErrLog(<< args.errorText);
      resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onError, mAppHandler, args);
      postCallback(callback);
      pcap_freealldevs(allDevs);
      pcap_close(mPcapHandle);
      mPcapHandle = 0;
      return kError;
   }

   mCurrentCaptureFilePath = filePath;

   Pcap::CaptureStartedEvent args;
   args.pcapInterface.description = d->description;
   args.pcapInterface.name = d->name;
   resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onCaptureStarted, mAppHandler, args);
   postCallback(callback);

   /* At this point, we no longer need the device list. Free it */
   pcap_freealldevs(allDevs);

   InfoLog(<< "Starting PCAP capture");

   /* start the capture */
   mCaptureThread.reset(new CPCAPI2::thread(std::bind(&PcapManagerInterface::CaptureThread, this)));

   mProgressTimer = new resip::DeadlineTimer<resip::MultiReactor>(mPhone->getSdkModuleThread());
   mProgressTimer->expires_from_now(1000);
   mProgressTimer->async_wait(this, 1, NULL);

   return kSuccess;
}

void PcapManagerInterface::CaptureThread()
{
   // This will block the thread.
   pcap_loop(mPcapHandle, 0, packet_handler, (u_char*)&mHandlerCallbackData);
   
   pcap_close(mPcapHandle);
   mPcapHandle = 0;
   
   if (mHandlerCallbackData.pcapDumpFile != NULL)
   {
      pcap_dump_close(mHandlerCallbackData.pcapDumpFile);
      mHandlerCallbackData.pcapDumpFile = NULL;
   }
   
   mHandlerCallbackData.totalBytesCaptured = 0;
}

/* Callback function invoked by libpcap for every incoming packet
 *
 * Runs on dedicated capture thread
 */
void packet_handler(u_char *args, const struct pcap_pkthdr *header, const u_char *pkt_data)
{
   PcapHandlerCallBackData * callbackData = (PcapHandlerCallBackData *)args;
   callbackData->totalBytesCaptured += header->caplen;

   /* save the packet on the dump file */
   pcap_dump((u_char*)callbackData->pcapDumpFile, header, pkt_data);
}

void PcapManagerInterface::onTimer(unsigned short timerId, void* appState)
{
   resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onCaptureProgress, mAppHandler, (mHandlerCallbackData.totalBytesCaptured));
   postCallback(callback);

   mProgressTimer->expires_from_now(1000);
   mProgressTimer->async_wait(this, 1, NULL);
}

int PcapManagerInterface::stop()
{
   InfoLog(<< "Stopping PCAP capture");
   ReadCallbackBase* rcb = resip::resip_bind(&PcapManagerInterface::StopImpl, this);
   post(rcb);
   return kSuccess;
}

/* 
 * runs in main SDK thread
 */
int PcapManagerInterface::StopImpl()
{
   if (mPcapHandle != NULL)
   {
      // Unblock mCaptureThread.
      
      // note that libpcap documentation indicates we are probably not doing enough here; i.e.
      // the capture thread might not awake with just this call.
      //
      // from https://www.tcpdump.org/manpages/pcap_breakloop.3pcap.html:
      // Note also that, in a multi-threaded application, if one thread is blocked in
      // pcap_dispatch(), pcap_loop(), pcap_next(3PCAP), or pcap_next_ex(3PCAP), a call to
      // pcap_breakloop() in a different thread will not unblock that thread. You will need
      // to use whatever mechanism the OS provides for breaking a thread out of blocking
      // calls in order to unblock the thread, such as thread cancellation or thread
      // signalling in systems that support POSIX threads, or SetEvent() on the result of
      // pcap_getevent() on a pcap_t on which the thread is blocked on Windows.
      // Asynchronous procedure calls will not work on Windows, as a thread blocked on a
      // pcap_t will not be in an alertable state.
      pcap_breakloop(mPcapHandle);
   }

   if (mProgressTimer != NULL)
   {
      mProgressTimer->cancel();
      delete mProgressTimer;
      mProgressTimer = NULL;
   }

   if (mCaptureThread.get() != NULL)
   {
      mCaptureThread->join();
      mCaptureThread.reset();
   }

   Pcap::CaptureCompletedEvent args;
   args.filePath = mCurrentCaptureFilePath;
   resip::ReadCallbackBase* callback = makeFpCommand1(PcapHandler::onCaptureCompleted, mAppHandler, args);
   postCallback(callback);

   return kSuccess;
}

void PcapManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   ReadCallbackBase* rcb = resip::resip_bind(&PcapManagerInterface::setCallbackHookImpl, this, cbHook, context );
   post(rcb);
}

void PcapManagerInterface::setCallbackHookImpl(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

}//OpenPcap
}//CPCAPI2

#endif //CPCAPI2_BRAND_OPEN_PCAP_MODULE
