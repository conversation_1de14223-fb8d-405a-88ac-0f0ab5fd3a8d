#pragma once

#if !defined(__CPCAPI2_PCAP_MANAGER_INTERFACE_H__)
#define __CPCAPI2_PCAP_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "pcap/PcapManager.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_thread.h"

#include <rutil/Fifo.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <mutex>
#include <thread>

#include <boost/asio.hpp>

struct pcap;
struct pcap_dumper;
typedef pcap pcap_t;
typedef pcap_dumper pcap_dumper_t;

namespace CPCAPI2
{
namespace Pcap
{

struct PcapHandlerCallBackData
{
   pcap_dumper_t* pcapDumpFile;
   unsigned long totalBytesCaptured;
};

class PcapManagerInterface : public PhoneModule,
                             public PcapManager,
                             public resip::DeadlineTimerHandler

{
public:
   PcapManagerInterface(Phone* phone);
   virtual ~PcapManagerInterface();

   virtual void interruptProcess();
   virtual int setHandler(PcapHandler* handler) OVERRIDE;

   int queryInterfaces() OVERRIDE;
   int setCaptureInterface(const cpc::string& interfaceName) OVERRIDE;
   int start(const cpc::string& filePath) OVERRIDE;
   int stop() OVERRIDE;
     
   virtual void Release() OVERRIDE;
   virtual int process(unsigned int timeout) OVERRIDE;
   
   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);
   
   virtual void setCallbackHook(void (*cbHook)(void*), void* context);
   
private:
   int SetHandlerImpl(PcapHandler* handler);
   int QueryInterfacesImpl();
   int StartImpl(const cpc::string& filePath);
   int StopImpl();
   void setCallbackHookImpl(void (*cbHook)(void*), void* context);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) override;

   void CaptureThread();

private:
   bool mShutdown;

   PcapHandler* mAppHandler;
   std::unique_ptr<CPCAPI2::thread> mCaptureThread;

   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;

   pcap_t * mPcapHandle;
   PcapHandlerCallBackData mHandlerCallbackData;
   resip::DeadlineTimer<resip::MultiReactor>* mProgressTimer;

   std::function<void(void)> mCbHook;

   cpc::string mCurrentCaptureFilePath;
   cpc::string mCaptureInterfaceName;
};

}//namespace Pcap
}//namespace CPCAPI2

#endif //__CPCAPI2_PCAP_MANAGER_INTERFACE_H__
