#include "brand_branded.h"

#include "interface/experimental/pcap/PcapManager.h"

#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
#include "cpcapi2utils.h"
#include "impl/phone/PhoneInterface.h"
#include "util/cpc_logger.h"
#include "PcapManagerInterface.h"
#include "PcapMisc.h"

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL
#endif

namespace CPCAPI2
{
   namespace Pcap
   {
      PcapManager* PcapManager::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_PCAP_MODULE == 1)
#ifdef WIN32
         if (LoadNpcapDlls() == false)
         {
            ErrLog(<< "Could not load npcap dlls. Make sure that you have npcap installed in C:\\Windows\\system32\\Npcap");
            return NULL;
         }
#endif
         PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
         PcapManager* pcapManager = dynamic_cast<PcapManager*>(pi->getInterfaceByName("PcapManagerInterface"));
         if (pcapManager == NULL)
         {
            PcapManagerInterface* m_if = new PcapManagerInterface(cpcPhone);
            pi->registerInterface("PcapManagerInterface", m_if);
            pcapManager = m_if;
         }
         return pcapManager;
#else
         return NULL;
#endif
      }

   }
}
