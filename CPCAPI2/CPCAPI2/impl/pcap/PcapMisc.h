#pragma once

#ifdef WIN32
#include <tchar.h>
#include <stdio.h>
#endif

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::EXTERNAL

namespace CPCAPI2
{
   namespace Pcap
   {
#ifdef WIN32
      BOOL LoadNpcapDlls()
      {
         _TCHAR npcap_dir[512];
         UINT len;
         len = GetSystemDirectory(npcap_dir, 480);
         if (!len) {
            ErrLog(<< "Error in GetSystemDirectory: " << GetLastError());
            return FALSE;
         }
         _tcscat_s(npcap_dir, 512, _T("\\Npcap"));
         if (SetDllDirectory(npcap_dir) == 0) {
            ErrLog(<< "Error in SetDllDirectory: " << GetLastError());
            return FALSE;
         }
         _tcscat_s(npcap_dir, 512, _T("\\wpcap.dll"));

         wchar_t wtext[512];
         mbstowcs(wtext, npcap_dir, strlen(npcap_dir) + 1); //Plus null
         LPWSTR dllName = wtext;

         HINSTANCE dynamicLib = LoadLibraryW(dllName);
         if (!dynamicLib)
         {
            ErrLog(<< "Could not load wpcap.dll");
            return FALSE;
         }
         return TRUE;
      }
#endif
   }//namespace Pcap
}//namespace CPCAPI2
