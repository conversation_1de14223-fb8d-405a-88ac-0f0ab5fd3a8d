#include "RegInfoContents.h"

#include "../util/LogSubsystems.h"
#include "../util/libXmlHelper.h"

#include <rutil/Random.hxx>
#include <resip/stack/Contents.hxx>
#include <rutil/Logger.hxx>
#include <sstream>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_DIALOGEVENT

namespace CPCAPI2
{
namespace SipRegEvent
{
   
static bool invokeRegInfoContentsInit = RegInfoContents::init();

bool
RegInfoContents::init()
{
   static ContentsFactory<RegInfoContents> factory;
   (void)factory;
   return true;
}

RegInfoContents::RegInfoContents(void)
   : Contents(getStaticType()),
     mRegInfoVersion(0),
     mState(RegInfoContents::full),
     mContentLength(0),
     mParsingError(false)
{
}

RegInfoContents::RegInfoContents(const HeaderFieldValue& hfv, const Mime& contentType)
   : Contents(hfv, contentType),
     mRegInfoVersion(0),
     mState(RegInfoContents::full),
     mContentLength(hfv.getLength()),
     mParsingError(false)
{
}

RegInfoContents::RegInfoContents(const RegInfoContents& rhs)
   : Contents(rhs),
     mRegInfoVersion(rhs.mRegInfoVersion),
     mState(rhs.mState),
     mRegistrations(rhs.mRegistrations),
     mContentLength(rhs.mContentLength),
     mParsingError(rhs.mParsingError)
{
}

RegInfoContents::~RegInfoContents(void)
{
}

RegInfoContents&
RegInfoContents::operator=(const RegInfoContents& rhs)
{
   if (this != &rhs)
   {
      Contents::operator=(rhs);
   }
   return *this;
}

Contents* 
RegInfoContents::clone() const
{
   return new RegInfoContents(*this);
}

const Mime& 
RegInfoContents::getStaticType() 
{
   static Mime type("application","reginfo+xml");
   return type;
}

std::ostream& 
RegInfoContents::encodeParsed(std::ostream& str) const
{
   str << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" << Symbols::CRLF;
   str << "<reginfo xmlns=\"urn:ietf:params:xml:ns:reginfo\"" << Symbols::CRLF;
   str << "   version=\"" << mRegInfoVersion << "\"" << Symbols::CRLF;
   str << "   state=\"" << (mState == RegInfoContents::full ? "full" : "partial") << "\">" << Symbols::CRLF;

   cpc::vector<RegInfoContents::Registration>::const_iterator it = mRegistrations.begin();
   for (; it != mRegistrations.end(); it++)
   {
      it->encodeParsed(str);
   }

   str << "</reginfo>" << Symbols::CRLF;
   return str;
}

resip::Data
registrationStateToString(RegInfoContents::Registration::RegistrationState state)
{
   switch (state)
   {
   case RegInfoContents::Registration::Init:
      return "init";
   case RegInfoContents::Registration::Active:
      return "active";
   case RegInfoContents::Registration::Terminated:
      return "terminated";
   }
   return "";
}

std::ostream&
RegInfoContents::Registration::encodeParsed(std::ostream& str) const
{
   str << "<registration id=\"" << mId << "\" aor=\"" << mAor << "\"" << Symbols::CRLF;
   str << "   state=\"" << registrationStateToString(mState) << "\">" << Symbols::CRLF;

   str << "</registration>" << Symbols::CRLF;
   return str;
}

resip::Data
contactStateToString(RegInfoContents::Contact::ContactState state)
{
   switch (state)
   {
   case RegInfoContents::Contact::Active:
      return "active";
   case RegInfoContents::Contact::Terminated:
      return "terminated";
   }
   assert(0);
   return "";
}

resip::Data
contactEventToString(RegInfoContents::Contact::ContactEvent ev)
{
   switch (ev)
   {
   case RegInfoContents::Contact::Registered:
      return "registered";
   case RegInfoContents::Contact::Created:
      return "created";
   case RegInfoContents::Contact::Refreshed:
      return "refreshed";
   case RegInfoContents::Contact::Shortened:
      return "shortened";
   case RegInfoContents::Contact::Expired:
      return "expired";
   case RegInfoContents::Contact::Deactivated:
      return "deactivated";
   case RegInfoContents::Contact::Probation:
      return "probation";
   case RegInfoContents::Contact::Unregistered:
      return "unregistered";
   case RegInfoContents::Contact::Rejected:
      return "rejected";
   }
   assert(0);
   return "";
}

std::ostream&
RegInfoContents::Contact::encodeParsed(std::ostream& str) const
{
   str << "<contact state=\"" << contactStateToString(mState) << "\" event=\"" << contactEventToString(mEvent) << "\"" << Symbols::CRLF;
   str << "   duration-registered=\"" << mDurationRegistered << "\"" << Symbols::CRLF;
   str << "   expires=\"" << mExpires << "\"" << Symbols::CRLF;
   str << "   retry-after=\"" << mRetryAfter << "\"" << Symbols::CRLF;
   str << "   id=\"" << mId << "\"" << Symbols::CRLF;
   str << "   q=\"" << mQ << "\"" << Symbols::CRLF;
   str << "   callid=\"" << mCallId << "\"" << Symbols::CRLF;
   str << "   cseq=\"" << mCSeq << "\">" << Symbols::CRLF;

   str << "</contact>" << Symbols::CRLF;
   return str;
}

class LibXmlParserInitHelper
{
public:
   LibXmlParserInitHelper() { ::xmlInitParser(); }
   virtual ~LibXmlParserInitHelper() { ::xmlCleanupParser(); }
};

void regevent_libxmlErrorHandler(void* arg, const char* msg, xmlParserSeverities severity, xmlTextReaderLocatorPtr locator)
{
   DebugLog(<< "libxml error: " << msg);
   DebugLog(<< "libxml error severity: " << severity);
   DebugLog(<< "libxml error! in XML at line " << xmlTextReaderLocatorLineNumber(locator));
   RegInfoContents* di = (RegInfoContents*)arg;
   di->setParseError(true);
}

void
RegInfoContents::parse(ParseBuffer& pb)
{
   int ret = 0;

   resip::Data strContents(resip::Data::Share, pb.start(), (int)mContentLength);
   if (strContents.find("</reginfo>") == resip::Data::npos)
   {
      // not a complete <dialog-info>..</dialog-info>
      // either the sender is sending garbage, or perhaps this was sent
      // over UDP and we're missing part of the document
      ErrLog(<< "incomplete reginfo contents!");
      return;
   }

   DebugLog(<< "parsing " << strContents);

   LibXmlParserInitHelper parserHelper; // nice way of making sure that we init/uninit the parser for this scope

   xmlTextReaderPtr reader = xmlReaderForMemory(
      strContents.c_str(),//pb.start(),
      (int)strContents.size(),//len,
      NULL,
      "UTF-8",
      128 // XML_PARSE_PEDANTIC
   );

   if (reader != NULL)
   {
      xmlTextReaderSetErrorHandler(reader, &regevent_libxmlErrorHandler, this);
      ret = xmlTextReaderRead(reader);
      while (ret == 1)
      {
         std::string nodeName = xmlString(xmlTextReaderName(reader));
         if (nodeName == "reginfo" && xmlTextReaderNodeType(reader) == 1)
         {
            if (xmlTextReaderHasAttributes(reader) == 1)
            {
               std::string ver = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"version"));
               mRegInfoVersion = atoi(ver.c_str());

               std::string state = xmlString(xmlTextReaderGetAttribute(reader, (const xmlChar*)"state"));
               mState = (state == "full" ? RegInfoContents::full : RegInfoContents::partial);
            }
         }
         else if (nodeName == "registration")
         {
            RegInfoContents::Registration r;
            r.parse(reader);
            mRegistrations.push_back(r);
         }
         if (mParsingError)
         {
            mRegistrations.clear();
            mRegInfoVersion = 0;
            xmlFreeTextReader(reader);
            return;
         }
         ret = xmlTextReaderRead(reader);
      }
      xmlFreeTextReader(reader);
      if (ret != 0)
      {
         /* SUA_TRACES_ERROR("XML parse error") */
      }
   }
}

RegInfoContents::Registration::RegistrationState
stringToRegistrationState(const std::string& str)
{
   if (str.compare("init") == 0) return RegInfoContents::Registration::Init;
   if (str.compare("active") == 0) return RegInfoContents::Registration::Active;
   if (str.compare("terminated") == 0) return RegInfoContents::Registration::Terminated;

   assert(0); // should never get here
   return RegInfoContents::Registration::Terminated;
}

void
RegInfoContents::Registration::parse(xmlTextReaderPtr reader)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
      {
         do
         {
            std::string attribName = xmlString(xmlTextReaderName(reader));
            std::string attribVal = xmlString(xmlTextReaderValue(reader));

            if (attribName == "id") // required
            {
               mId = attribVal.c_str();
            }
            else if (attribName == "aor") // required
            {
               mAor = resip::Uri(attribVal.c_str());
            }
            else if (attribName == "state") // required
            {
               mState = stringToRegistrationState(attribVal);
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == 1);
      }

      if (xmlTextReaderRead(reader) == 1)
      {
         bool inRegistrationElement = true;
         while (inRegistrationElement)
         {
            std::string elementName = xmlString(xmlTextReaderName(reader));

            if (elementName == "registration" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT) // 15 == end element
            {
               inRegistrationElement = false;
            }
            else if (elementName == "contact")
            {
               RegInfoContents::Contact c;
               c.parse(reader);
               mContacts.push_back(c);
            }
            inRegistrationElement = inRegistrationElement && (xmlTextReaderRead(reader) == 1);
         }
      }
   }
}

RegInfoContents::Contact::ContactState
stringToContactState(const std::string& str)
{
   if (str.compare("active") == 0) return RegInfoContents::Contact::Active;
   else if (str.compare("terminated") == 0) return RegInfoContents::Contact::Terminated;

   return RegInfoContents::Contact::Terminated;
}

RegInfoContents::Contact::ContactEvent
stringToContactEvent(const std::string& str)
{
   if (str.compare("registered") == 0) return RegInfoContents::Contact::Registered;
   else if (str.compare("created") == 0) return RegInfoContents::Contact::Created;
   else if (str.compare("refreshed") == 0) return RegInfoContents::Contact::Refreshed;
   else if (str.compare("shortened") == 0) return RegInfoContents::Contact::Shortened;
   else if (str.compare("expired") == 0) return RegInfoContents::Contact::Expired;
   else if (str.compare("deactivated") == 0) return RegInfoContents::Contact::Deactivated;
   else if (str.compare("probation") == 0) return RegInfoContents::Contact::Probation;
   else if (str.compare("unregistered") == 0) return RegInfoContents::Contact::Unregistered;
   else if (str.compare("rejected") == 0) return RegInfoContents::Contact::Rejected;

   return RegInfoContents::Contact::Registered;
}

void
RegInfoContents::Contact::parse(xmlTextReaderPtr reader)
{
   if (xmlTextReaderHasAttributes(reader) == 1)
   {
      if (xmlTextReaderMoveToFirstAttribute(reader) == 1)
      {
         do
         {
            std::string attribName = xmlString(xmlTextReaderName(reader));
            std::string attribVal = xmlString(xmlTextReaderValue(reader));

            if (attribName == "state") // required
            {
               mState = stringToContactState(attribVal);
            }
            else if (attribName == "event") // required
            {
               mEvent = stringToContactEvent(attribVal);
            }
            else if (attribName == "duration-registered")
            {
               mDurationRegistered = (unsigned long)atol(attribVal.c_str());
            }
            else if (attribName == "expires")
            {
               mExpires = (unsigned long)atol(attribVal.c_str());
            }
            else if (attribName == "retry-after")
            {
               mRetryAfter = (unsigned long)atol(attribVal.c_str());
            }
            else if (attribName == "id") // required
            {
               mId = attribVal.c_str();
            }
            else if (attribName == "q")
            {
               mQ = attribVal.c_str();
            }
            else if (attribName == "callid")
            {
               mCallId = attribVal.c_str();
            }
            else if (attribName == "cseq")
            {
               mCSeq = (unsigned long)atol(attribVal.c_str());
            }
         }
         while (xmlTextReaderMoveToNextAttribute(reader) == 1);
      }

      if (xmlTextReaderRead(reader) == 1)
      {
         bool inContactElement = true;
         while (inContactElement)
         {
            std::string elementName = xmlString(xmlTextReaderName(reader));

            if (elementName == "contact" && xmlTextReaderNodeType(reader) == XML_READER_TYPE_END_ELEMENT) // 15 == end element
            {
               inContactElement = false;
            }
            else if (elementName == "uri")
            {
               const std::string& elementValue = xmlGetElementText(reader);
               mUri = resip::Uri(elementValue.c_str());
            }
            else if (elementName == "display-name")
            {
               mDisplayName = xmlGetElementText(reader).c_str();
            }
            else if (elementName == "unknown-param")
            {
            }
            inContactElement = inContactElement && (xmlTextReaderRead(reader) == 1);
         }
      }
   }
}

UInt32 RegInfoContents::version(void) const
{
   checkParsed();
   return mRegInfoVersion;
}

UInt32& RegInfoContents::version(void)
{
   checkParsed();
   return (UInt32&)mRegInfoVersion;
}

RegInfoContents::StateType RegInfoContents::stateType(void) const
{
   checkParsed();
   return mState;
}

RegInfoContents::StateType& RegInfoContents::stateType(void)
{
   checkParsed();
   return mState;
}

const cpc::vector<RegInfoContents::Registration>& RegInfoContents::registrations(void) const
{
   checkParsed();
   return mRegistrations;
}

cpc::vector<RegInfoContents::Registration>& RegInfoContents::registrations(void)
{
   checkParsed();
   return mRegistrations;
}

RegInfoContents::Registration::Registration(void)
{
}

RegInfoContents::Registration::~Registration(void)
{
}

RegInfoContents::Contact::Contact(void)
{
}



RegInfoContents::Contact::~Contact(void)
{
}

RegistrationState RegInfoContents::regStateToSdk(Registration::RegistrationState s)
{
   switch (s)
   {
   case RegInfoContents::Registration::Active:
      return RegistrationState_Active;
   case RegInfoContents::Registration::Init:
      return RegistrationState_Init;
   default:
      return RegistrationState_Terminated;
   }
}

ContactState RegInfoContents::contactStateToSdk(Contact::ContactState s)
{
   switch (s)
   {
   case RegInfoContents::Contact::Active:
      return ContactState_Active;
   case RegInfoContents::Contact::Terminated:
      return ContactState_Terminated;
   }
   return ContactState_Terminated;
}

RegEvent RegInfoContents::registrationEventToSdk(Contact::ContactEvent evt)
{
   switch (evt)
   {
   case RegInfoContents::Contact::Created:
      return RegEvent_Created;
   case RegInfoContents::Contact::Deactivated:
      return RegEvent_Deactivated;
   case RegInfoContents::Contact::Expired:
      return RegEvent_Expired;
   case RegInfoContents::Contact::Probation:
      return RegEvent_Probation;
   case RegInfoContents::Contact::Refreshed:
      return RegEvent_Refreshed;
   case RegInfoContents::Contact::Registered:
      return RegEvent_Registered;
   case RegInfoContents::Contact::Rejected:
      return RegEvent_Rejected;
   case RegInfoContents::Contact::Shortened:
      return RegEvent_Shortened;
   case RegInfoContents::Contact::Unregistered:
      return RegEvent_Unregistered;
   }
   return RegEvent_Unregistered;
}


}
}
