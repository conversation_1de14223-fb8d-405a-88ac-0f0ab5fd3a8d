#pragma once

#if !defined(CPCAPI2_SIP_REG_EVENT_INTERNAL_EVENT_HANDLER_H)
#define CPCAPI2_SIP_REG_EVENT_INTERNAL_EVENT_HANDLER_H

#include "cpcapi2defs.h"
#include "SipRegEventManagerInterface.h"
#include "regevent/SipRegEventModel.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
   namespace SipRegEvent
   {
      class SipRegEventHandler;

      class SipRegEventInternalEventHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>

      {
      public:
         SipRegEventInternalEventHandler(SipRegEventManagerInterface& iff, CPCAPI2::SipAccount::SipAccountImpl& acct, CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf);
         virtual ~SipRegEventInternalEventHandler();

         virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
         virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
         virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
         virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
         virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

         virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
         virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);

         virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);

      private:
         SipRegEventManagerInterface& mInterface;
         CPCAPI2::SipAccount::SipAccountImpl& mAccount;
         CPCAPI2::SipEvent::SipEventManagerInterface& mSipEventIf;
         cpc::vector<Registration> mRegistrations;
      };
   }
}
#endif // CPCAPI2_SIP_REG_EVENT_INTERNAL_EVENT_HANDLER_H
