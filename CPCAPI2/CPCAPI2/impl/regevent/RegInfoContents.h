#if !defined(CPCAPI2_REG_INFO_CONTENTS)
#define CPCAPI2_REG_INFO_CONTENTS

#include "regevent/SipRegEventModel.h"
#include "resip/stack/Contents.hxx"

#include <libxml/xmlreader.h>

namespace CPCAPI2
{
namespace SipRegEvent
{

class RegInfoContents : public resip::Contents
{
public:
   RegInfoContents(void);
   RegInfoContents(const RegInfoContents& rhs);
   virtual ~RegInfoContents(void);

   static bool init();

   void setParseError(bool error) { mParsingError = error; }

   RegInfoContents(const resip::HeaderFieldValue& hfv, const resip::Mime& contentType);
   RegInfoContents& operator=(const RegInfoContents& rhs);
   virtual Contents* clone() const;
   static const resip::Mime& getStaticType();
   virtual std::ostream& encodeParsed(std::ostream& str) const;
   virtual void parse(resip::Parse<PERSON><PERSON><PERSON>& pb);

   UInt32 version(void) const;
   UInt32& version(void);

   enum StateType
   {
      full = 0,
      partial
   };

   StateType stateType(void) const;
   StateType& stateType(void);

   class Contact
   {
   public:
      Contact(void);
      //Contact(const Contact& rhs);

      virtual ~Contact(void);

      //Contact& operator=(const Contact& rhs);

      std::ostream& encodeParsed(std::ostream& str) const;
      void parse(xmlTextReaderPtr reader);

      const resip::Data& displayName() const { return mDisplayName; }
      const resip::Uri& uri() const { return mUri; }
      const resip::Data& id() const { return mId; }
      const resip::Data& callId() const { return mCallId; } 

      enum ContactState
      {
         Active,
         Terminated
      };

      enum ContactEvent
      {
         Registered,
         Created,
         Refreshed,
         Shortened,
         Expired,
         Deactivated,
         Probation,
         Unregistered,
         Rejected
      };

      const ContactState& state() const { return mState; }
      const ContactEvent& eventType() const { return mEvent; }

   private:
      resip::Uri mUri;
      resip::Data mDisplayName;
      std::pair<resip::Data,resip::Data> mUnknownParam; // first is 'name', second is value
      ContactState mState;
      ContactEvent mEvent;
      unsigned long mDurationRegistered;
      unsigned long mExpires;
      unsigned long mRetryAfter;
      resip::Data mId;
      resip::Data mQ;
      resip::Data mCallId;
      unsigned long mCSeq;
   };

   class Registration
   {
   public:
      Registration(void);
      //Registration(const Registration& rhs);

      virtual ~Registration(void);

      enum RegistrationState
      {
         Init,
         Active,
         Terminated
      };

      //Registration& operator=(const Registration& rhs);

      std::ostream& encodeParsed(std::ostream& str) const;
      void parse(xmlTextReaderPtr reader);

      const resip::Data& id() const { return mId; }
      resip::Data& id() { return mId; }

      const resip::Uri& aor() const { return mAor; }
      resip::Uri& aor() { return mAor; }

      RegistrationState state() const { return mState; }
      RegistrationState& state() { return mState; }

      const cpc::vector<Contact>& contacts() const { return mContacts; }
      cpc::vector<Contact>& contacts() { return mContacts; }

   private:
      resip::Data mId;
      resip::Uri mAor;
      RegistrationState mState;
      cpc::vector<Contact> mContacts;
   };

   const cpc::vector<Registration>& registrations(void) const;
   cpc::vector<Registration>& registrations(void);

   static RegistrationState regStateToSdk(Registration::RegistrationState s);
   static ContactState contactStateToSdk(Contact::ContactState s);
   static RegEvent registrationEventToSdk(Contact::ContactEvent evt);

private:
   unsigned int mRegInfoVersion;
   StateType mState;
   cpc::vector<Registration> mRegistrations;
   unsigned int mContentLength;
   bool mParsingError;

};

}

}

#endif // CPCAPI2_REG_INFO_CONTENTS

