#include "brand_branded.h"

#include "interface/experimental/regevent/SipRegEventManager.h"

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
#include "SipRegEventManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace SipRegEvent
   {
      SipRegEventManager* SipRegEventManager::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SipRegEventManagerInterface>(phone, "SipRegEventManagerInterface");
#else
         return NULL;
#endif
      }
   }
}
