#pragma once

#if !defined(CPCAPI2_SIP_REG_EVENT_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_REG_EVENT_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "regevent/SipRegEventManager.h"
#include "regevent/SipRegEventHandler.h"
#include "../phone/PhoneInterface.h"

#include <map>

namespace CPCAPI2
{

namespace SipAccount
{
class SipAccountInterface;
}

namespace SipEvent
{
class SipEventManagerInterface;
}

namespace SipRegEvent
{
class SipRegEventInternalEventHandler;

class SipRegEventManagerInterface : public CPCAPI2::EventSource2< CPCAPI2::EventHandler<SipRegEventHandler, SipAccount::SipAccountHandle> >,
                                    public SipRegEventManager,
                                    public PhoneModule
{
public:
   SipRegEventManagerInterface(Phone* phone);
   virtual ~SipRegEventManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipRegEventManagerInterface);

   virtual void Release() OVERRIDE;

   /**
   * Set the handler.
   */
   virtual int setHandler(
         CPCAPI2::SipAccount::SipAccountHandle account,
         SipRegEventHandler* handler) OVERRIDE;

   int setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipRegEventHandler* handler);
   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipRegEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;

   virtual int applySubscriptionSettings(SipRegEventSubscriptionHandle subscription, const SipRegEventSubscriptionSettings& settings) OVERRIDE;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   */
   virtual int addParticipant(SipRegEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   */
   virtual int start(SipRegEventSubscriptionHandle subscription) OVERRIDE;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
   */
   virtual int end(SipRegEventSubscriptionHandle subscription) OVERRIDE;

   int endImpl(SipRegEventSubscriptionHandle subscription);

private:
   int startImpl(SipRegEventSubscriptionHandle subscription);

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipRegEventInternalEventHandler*> AccountMap;
   AccountMap mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipRegEventHandler*> mHandlers;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
};

std::ostream& operator<<(std::ostream& os, const NewRegEventSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const RegEventSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const RegEventUpdatedEvent& evt);
std::ostream& operator<<(std::ostream& os, const RegEventSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipRegEvent::ErrorEvent& evt);
}
}
#endif // CPCAPI2_SIP_REG_EVENT_MANAGER_INTERFACE_H
