#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
#include <cpcapi2utils.h>
#include "SipRegEventInternalEventHandler.h"

#include "RegInfoContents.h"
#include "regevent/SipRegEventManager.h"
#include "regevent/SipRegEventHandler.h"
#include "../event/SipEventManagerInterface.h"

#include "../util/DumFpCommand.h"


using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace SipRegEvent
{
SipRegEventInternalEventHandler::SipRegEventInternalEventHandler(SipRegEventManagerInterface& iff, CPCAPI2::SipAccount::SipAccountImpl& acct, CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf)
   : mInterface(iff),
   mAccount(acct),
   mSipEventIf(sipEventIf)
{
}

SipRegEventInternalEventHandler::~SipRegEventInternalEventHandler()
{
}

int SipRegEventInternalEventHandler::onNewSubscription(SipEventSubscriptionHandle subscription, const NewSubscriptionEvent& args)
{
   if(SipSubscriptionType_Outgoing == args.subscriptionType)
   {
      NewRegEventSubscriptionEvent regArgs;
      regArgs.account = mAccount.getHandle();

      mInterface.fireEvent(cpcEvent(SipRegEventHandler, onNewSubscription), mAccount.getHandle(), subscription, regArgs);
   }
   else
   {
      mSipEventIf.reject(subscription, 489);
   }
   return kSuccess;
}

int SipRegEventInternalEventHandler::onSubscriptionEnded(SipEventSubscriptionHandle subscription, const SubscriptionEndedEvent& args)
{
   if(SipSubscriptionType_Outgoing == args.subscriptionType)
   {
      // incoming subscribe is automatically rejected (obelisk-110)
      // only fire events for end of outgoing subscribe
      RegEventSubscriptionEndedEvent regArgs;
      regArgs.endReason = args.endReason;

      mInterface.fireEvent(cpcEvent(SipRegEventHandler, onSubscriptionEnded), mAccount.getHandle(), subscription, regArgs);
   }
   return kSuccess;
}

int SipRegEventInternalEventHandler::onIncomingEventState(SipEventSubscriptionHandle subscription, const IncomingEventStateEvent& args)
{
   using namespace resip;

   if (args.eventState.contentUTF8.empty())
   {
      // Ignore empty body, likely just a re-subscription
      return kSuccess;
   }

   RegEventUpdatedEvent regArgs;

   resip::HeaderFieldValue hfv(args.eventState.contentUTF8.c_str(), args.eventState.contentLength);
         
   RegInfoContents regInfoContents(hfv, RegInfoContents::getStaticType());
   if (regInfoContents.stateType() == RegInfoContents::full)
   {
      mRegistrations.clear();
   }

   const cpc::vector<RegInfoContents::Registration>& registrations = regInfoContents.registrations();
   cpc::vector<RegInfoContents::Registration>::const_iterator itReg = registrations.begin();
   for (; itReg != registrations.end(); ++itReg)
   {
      cpc::vector<Registration>::iterator itMyRegs = mRegistrations.begin();
      for (; itMyRegs != mRegistrations.end(); ++itMyRegs)
      {
         if (itMyRegs->id == itReg->id().c_str())
         {
            break;
         }
      }
      Registration reg = Registration();
      Registration& r = (itMyRegs != mRegistrations.end() ? *itMyRegs : reg);
      r.id = itReg->id().c_str();
      r.addressOfRecord = resip::Data::from(itReg->aor()).c_str();
      r.state = RegInfoContents::regStateToSdk(itReg->state());

      const cpc::vector<RegInfoContents::Contact>& contacts = itReg->contacts();
      cpc::vector<RegInfoContents::Contact>::const_iterator itNotifyContacts = contacts.begin();
      for (; itNotifyContacts != contacts.end(); ++itNotifyContacts)
      {
         // find and update existing contact
         cpc::vector<Contact>& myContacts = r.contacts;
         cpc::vector<Contact>::iterator itMyContacts = myContacts.begin();
         for (; itMyContacts != myContacts.end(); ++itMyContacts)
         {
            if (itMyContacts->id == itNotifyContacts->id().c_str())
            {
               break;
            }
         }

         Contact con = Contact();
         Contact& c = (itMyContacts != myContacts.end() ? *itMyContacts : con);
         c.id = itNotifyContacts->id().c_str();
         c.displayName = itNotifyContacts->displayName().c_str();
         c.uri = resip::Data::from(itNotifyContacts->uri()).c_str();
         c.state = RegInfoContents::contactStateToSdk(itNotifyContacts->state());
         c.callId = itNotifyContacts->callId().c_str();

         if (itMyContacts == myContacts.end())
         {
            myContacts.push_back(c);
         }

         ChangeItem changeItem;
         changeItem.registration = r;
         changeItem.registrationEvent = RegInfoContents::registrationEventToSdk(itNotifyContacts->eventType());
         regArgs.registrationEventInfo.changes.push_back(changeItem);
      }

      if (itMyRegs != mRegistrations.end())
      {
         mRegistrations.push_back(r);
      }
   }

   mInterface.fireEvent(cpcEvent(SipRegEventHandler, onRegStateUpdated), mAccount.getHandle(), subscription, regArgs);

   return kSuccess;
}

int SipRegEventInternalEventHandler::onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const SubscriptionStateChangedEvent& args)
{
   RegEventSubscriptionStateChangedEvent regArgs;
   regArgs.subscriptionState = args.subscriptionState;

   mInterface.fireEvent(cpcEvent(SipRegEventHandler, onSubscriptionStateChanged), mAccount.getHandle(), subscription, regArgs);

   return kSuccess;
}

int SipRegEventInternalEventHandler::onNotifySuccess(SipEventSubscriptionHandle subscription, const NotifySuccessEvent& args)
{
   return kSuccess;
}

int SipRegEventInternalEventHandler::onNotifyFailure(SipEventSubscriptionHandle subscription, const NotifyFailureEvent& args)
{
   return kSuccess;
}

int SipRegEventInternalEventHandler::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   ErrorEvent evt;
   evt.errorText = args.errorText;
   mInterface.fireEvent(cpcEvent(SipRegEventHandler, onError), mAccount.getHandle(), subscription, evt);
   return kSuccess;
}
}
}
#endif
