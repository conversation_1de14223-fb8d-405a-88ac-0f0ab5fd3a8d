#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_REG_EVENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipRegEventManagerInterface.h"
#include "SipRegEventInternalEventHandler.h"
#include "../event/SipEventManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"


using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

namespace CPCAPI2
{
namespace SipRegEvent
{

SipRegEventManagerInterface::SipRegEventManagerInterface(Phone* phone)
   : EventSource2< EventHandler<SipRegEventHandler, SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mPhone(dynamic_cast<PhoneInterface*>(phone)), mAccountIf(NULL)
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
}

SipRegEventManagerInterface::~SipRegEventManagerInterface()
{
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      mSipEventIf->setHandlerImpl(it->first, "reg", NULL);
      delete it->second;
   }
   mAccountMap.clear();
}

void SipRegEventManagerInterface::Release()
{
   delete this;
}

int SipRegEventManagerInterface::setHandler(
   CPCAPI2::SipAccount::SipAccountHandle account,
   SipRegEventHandler* handler)
{
   postToSdkThread(resip::resip_bind(&SipRegEventManagerInterface::setHandlerImpl, this, account, handler));
   return kSuccess;
}

int SipRegEventManagerInterface::setHandlerImpl(
   CPCAPI2::SipAccount::SipAccountHandle account,
   SipRegEventHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipRegEventInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   if (evtMan == NULL)
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipRegEventManager::setHandler");
         return kError;
      }

      evtMan = new SipRegEventInternalEventHandler(*this, *acct, *mSipEventIf);
      mAccountMap[account] = evtMan;
   }

   auto ith = mHandlers.find(account);
   if (mHandlers.end() != ith)
   {
     removeAppHandler(ith->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }

   mSipEventIf->addSdkObserver(evtMan, account, "reg");
   return kSuccess;
}

SipEventSubscriptionHandle SipRegEventManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   return mSipEventIf->createSubscription(account);
}

int SipRegEventManagerInterface::addParticipant(SipRegEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->addParticipant(subscription, targetAddress);
}

int SipRegEventManagerInterface::start(SipEventSubscriptionHandle subscription)
{
   postToSdkThread(resip::resip_bind(&SipRegEventManagerInterface::startImpl, this, subscription));
   return kSuccess;
}

int SipRegEventManagerInterface::startImpl(SipRegEventSubscriptionHandle subscription)
{
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
      if (!acct)
      {
         mAccountIf->fireError("Invalid account handle for SipRegEventManager::start");
         return kError;
      }

      if (ci->targetAddresses.empty())
      {
         cpc::string localUri = "sip:" + acct->getSettings().username + "@" + acct->getSettings().domain;
         mSipEventIf->addParticipant(subscription, localUri);
      }

      mSipEventIf->start(subscription);
   }
   return kSuccess;
}

int SipRegEventManagerInterface::end(SipEventSubscriptionHandle subscription)
{
   return mSipEventIf->end(subscription);
}

int SipRegEventManagerInterface::endImpl(SipRegEventSubscriptionHandle subscription)
{
   return mSipEventIf->endImpl(subscription, SipSubscriptionTerminateReason_NoResource);
}

int SipRegEventManagerInterface::applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipRegEventSubscriptionSettings& settings)
{
   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = "reg";
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(MimeType("application","reginfo+xml"));
   return mSipEventIf->applySubscriptionSettings(subscription, eventSettings);
}

std::ostream& operator<<(std::ostream& os, const NewRegEventSubscriptionEvent& evt)
{
   return os << "NewRegEventSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const RegEventSubscriptionEndedEvent& evt)
{
   return os << "RegEventSubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const RegEventUpdatedEvent& evt)
{
   return os << "RegEventUpdatedEvent";
}

std::ostream& operator<<(std::ostream& os, const RegEventSubscriptionStateChangedEvent& evt)
{
   return os << "RegEventSubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const SipRegEvent::ErrorEvent& evt)
{
   return os << "SipRegEvent::ErrorEvent";
}

}
}
#endif
