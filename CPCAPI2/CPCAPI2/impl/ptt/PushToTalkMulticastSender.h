#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MULTICAST_SENDER_H
#define CPCAPI2_PUSHTOTALK_MULTICAST_SENDER_H

#include "cpcapi2defs.h"

// boost asio
#include <boost/asio.hpp>

namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkMulticastSender
{

public:

   PushToTalkMulticastSender(boost::asio::io_context& io_context,
      const boost::asio::ip::address& multicast_address, const int multicast_port);

   void Send(const std::string& payload);

private:

   void do_send();
   void do_send_ex();
   void do_timeout();

private:

   boost::asio::ip::udp::endpoint endpoint_;
   boost::asio::ip::udp::socket socket_;
   boost::asio::steady_timer timer_;
   int message_count_;
   int retry_count_;
   std::string message_;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MULTICAST_SENDER_H
