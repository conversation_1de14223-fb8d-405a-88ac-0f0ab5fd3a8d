#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkSenderSession.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkManagerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkClientWebSocket.h"
#include "PushToTalkServerWebSocket.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkMulticastSender.h"
#include "PushToTalkMulticastReceiver.h"
#include "../call/SipAVConversationManagerInterface.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "media/AudioInterface.h"
#include "../account/SipAccountInterface.h"
#include "../media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include <MediaStackImpl.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"

#include <CodecFactoryImpl.hxx>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <webrtc/common_types.h>
#include <voe_base.h>
// #ifdef CODECFACTORY_ENABLE_OPUS
#include <codecs/OpusCodecImpl.hxx>
// #endif

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

#define PTT_SENDER_SESSION_LOG_ENABLED                                    0
#if (PTT_SENDER_SESSION_LOG_ENABLED == 1)
#define PttLog(args) StackLog(args)
#else
#define PttLog(args)
#endif

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::PeerConnection;
using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

std::string GetPttIdentityUrl(const CPCAPI2::PushToTalk::PttIdentity& identity)
{
   std::stringstream identityUrl;
   identityUrl << ((identity.identityType == PttIdentityType_SIP) ? "sip" : "xmpp");
   identityUrl << ":" << identity.userName;
   return identityUrl.str().c_str();
};

PttSenderSessionFactory::PttSenderSessionFactory(PushToTalkManagerImpl* manager) : PttSessionFactory(manager)
{
}

PttSenderSessionFactory::~PttSenderSessionFactory()
{
}

PttSession* PttSenderSessionFactory::create(PushToTalkSessionHandle ptt)
{
   PttSession* session = NULL;
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   if (settings.serviceType == PttServiceType_WAN)
   {
      session = new PttSenderWanSession(mManager, ptt);
   }
   else if (settings.serviceType == PttServiceType_LAN)
   {
      session = new PttSenderLanSession(mManager, ptt);
   }

   return session;
}

PttSenderSessionStateFactory::PttSenderSessionStateFactory(PushToTalkManagerImpl* manager) : PttSessionStateFactory(manager)
{
}

PttSenderSessionStateFactory::~PttSenderSessionStateFactory()
{
   for (PttSenderSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void PttSenderSessionStateFactory::create()
{
   if (!mManager) return;

   if (mStates.size() > 0)
   {
      ErrLog(<< "PttSenderSessionStateFactory::create(): " << this << " manager: " << mManager << " state factory already initialized");
      return;
   }

   mStates[PttSessionState_Idle] = create(PttSessionState_Idle);
   mStates[PttSessionState_Initiated] = create(PttSessionState_Initiated);
   mStates[PttSessionState_InitialActive] = create(PttSessionState_InitialActive);
   mStates[PttSessionState_Active] = create(PttSessionState_Active);
   mStates[PttSessionState_Talking] = create(PttSessionState_Talking);
   mStates[PttSessionState_Ending] = create(PttSessionState_Ending);

   for (PttSenderSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      PttLog(<< "PttSenderSessionStateFactory::create(): " << PttSenderSessionState::getName(i->first) << " : " << i->first);
   }
}

PttSenderSessionState* PttSenderSessionStateFactory::create(PttSessionStateType type)
{
   PttSenderSessionState* state = NULL;
   switch (type)
   {
      case PttSessionState_Idle: state = new PttSenderIdleState(mManager); break;
      case PttSessionState_Initiated: state = new PttSenderInitiatedState(mManager); break;
      case PttSessionState_InitialActive: state = new PttSenderInitialActiveState(mManager); break;
      case PttSessionState_Active: state = new PttSenderActiveState(mManager); break;
      case PttSessionState_Talking: state = new PttSenderTalkingState(mManager); break;
      case PttSessionState_Ending: state = new PttSenderEndingState(mManager); break;
      default: break;
   }

   return state;
}

PttSessionState* PttSenderSessionStateFactory::getState(PttSessionStateType type)
{
   if (!mManager || mStates.size() == 0)
   {
      ErrLog(<< "PttSenderSessionStateFactory::getState(): " << this << " manager: " << mManager << " state factory not initialized");
      return NULL;
   }
   return mStates[type];
}

PttSenderSessionState::PttSenderSessionState(PushToTalkManagerImpl* manager, PttSessionStateType type) :
PttSessionState(manager, type),
mPeerConnMgr(manager->getPeerManager())
{
}

PttSenderSessionState::~PttSenderSessionState()
{
}

int PttSenderSessionState::end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason)
{
   PttSenderSession* senderSession = static_cast<PttSenderSession*>(session);
   senderSession->cancelTimers();
   StatLog(PTT_LOGID_SESSION_TX_END, << "PttSenderSessionState::end(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " in state: " << getName() << " reason: " << reason);
   senderSession->handleEnd();
   session->changeState(PttSessionState_Idle);
   return kSuccess;
}

int PttSenderSessionState::onSetLocalSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   PttSenderLanSession* senderSession = dynamic_cast<PttSenderLanSession*>(session);
   if (senderSession)
   {
      std::chrono::duration<long long, std::milli> delta = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - senderSession->mStartTime);
      DebugLog(<< "PttSenderSessionState::onSetLocalSessionDescriptionResult(): ptt: " << session->getPtt() << " pc: " << pc << " time since session start: " << delta.count() << " ms. Connected peer count: " << senderSession->getPttSessionConnectedCallCount() << " in state: " << getName());
   }
   return kSuccess;
}

int PttSenderSessionState::onPttReceiverEndedEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSenderLanSession* senderSession = dynamic_cast<PttSenderLanSession*>(session);
   if (senderSession)
   {
      DebugLog(<< "PttSenderSessionState::onPttReceiverEndedEvent(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " in state: " << getName());
      senderSession->handlePttReceiverEnded(evt, incomingTuple);
   }
   return kSuccess;
}

int PttSenderSessionState::onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args)
{
   PttSenderWanSession* senderSession = dynamic_cast<PttSenderWanSession*>(session);
   if (senderSession)
   {
      StackLog(<< "PttSenderSessionState::onConferenceParticipantListUpdated(): ptt: " << ptt << " conference-connector: " << connector << " cloud-conference: " << args.conference << " handled in " << session->getSessionType() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " session in current state: " << getName());
      senderSession->mCloudParticipantList = args.participantList;
      senderSession->updateCloudParticipant(args);
      for (cpc::vector<ConferenceConnector::CloudConferenceParticipantInfo>::const_iterator i = args.participantList.begin(); i != args.participantList.end(); ++i)
      {
         // if (senderSession->mCloudParticipant != (*i).participant)
         {
            senderSession->mCloudParticipantHistory.insert((*i).participant);
         }
      }
      senderSession->mMediaInfoReceived = senderSession->mCloudParticipantHistory.size() - 1; // ignore the sender participant itself
   }
   return kSuccess;
}

int PttSenderSessionState::onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
#ifdef CPCAPI2_AUTO_TEST
   InfoLog(<< "PttSenderSessionState::onConferenceSessionMediaStatusChanged(): " << this << " service: " << mService << " ptt: " << ptt << " conference: " << connector << " event: " << args);
   if (session->getType() == PttSession::PttSessionType_Wan)
   {
      PttConferenceMediaStatusChangedEvent statEvt;
      statEvt.pttHandle = session->getPtt();
      statEvt.peerConnection = args.peerConnection;
      statEvt.cloudConf = args.conference;
      statEvt.cloudConfSession = args.session;
      statEvt.mediaStatus = (PttConferenceMediaStatusChangedEvent::MediaStatus)args.mediaStatus;

      mManager->getPttManager()->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttConferenceMediaStatusChangedEvent), mManager->getService(), ptt, statEvt);
   }
#endif
   return kSuccess;
}

void PttSenderSessionState::onSpurtTimeout(PttSession* session)
{
   DebugLog(<< "PttSenderSessionState::onSpurtTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in state: " << getName());
}

void PttSenderSessionState::onExpiryTimeout(PttSession* session)
{
   DebugLog(<< "PttSenderSessionState::onExpiryTimeout(): ptt: " << session->getPtt() << " session expiry timeout in " << session->getName() << " session in state: " << getName());
   end(session, session->getPtt(), "sender expiry timeout");
}

void PttSenderSessionState::onConnectedTimeout(PttSession* session)
{
   StackLog(<< "PttSenderSessionState::onConnectedTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in state: " << getName());
}

void PttSenderSessionState::onConnectionErrorTimeout(PttSession* session)
{
   DebugLog(<< "PttSenderSessionState::onConnectionErrorTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in state: " << getName());
}

void PttSenderSessionState::onDisconnectedTimeout(PttSession* session)
{
   StackLog(<< "PttSenderSessionState::onDisconnectedTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in state: " << getName());
}

void PttSenderSessionState::onInitRetryTimeout(PttSession* session)
{
   PttSenderLanSession* senderSession = dynamic_cast<PttSenderLanSession*>(session);
   if (senderSession)
   {
      DebugLog(<< "PttSenderSessionState::onInitRetryTimeout(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " in state: " << getName());
      senderSession->sendPttInitiateToTransport(false);
   }
   else if (session)
   {
      StackLog(<< "PttSenderSessionState::onInitRetryTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in state: " << getName());
   }
}

PttSenderSession::PttSenderSession(PushToTalkManagerImpl* manager, PttSession::PttSessionType sessionType, PushToTalkSessionHandle ptt) :
PttSession(manager, ptt, sessionType),
mFactory(dynamic_cast<PttSenderSessionStateFactory*>(mManager->getSenderStateFactory())),
mSessionId(""),
mChannelId(""),
mTransactionId(0),
mInitRecordingSuccessful(false),
mSpurtTimer(manager->getReactor()),
mSessionTimer(manager->getReactor()),
mConnectedTimer(manager->getReactor()),
mConnectionErrorTimer(manager->getReactor()),
mDisconnectedTimer(manager->getReactor()),
mInitRetryTimer(manager->getReactor())
{
   CPCAPI2::Media::MediaManagerInterface* mediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(mManager->getPhone()));
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaIf->media_stack_ptr()->codecFactory());
   if (codecFactory)
   {
      std::shared_ptr<webrtc_recon::OpusCodecImpl> audioCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", 48000));
      /*
      // only g711 is supported at this time
      PeerConnection::MediaCodec pcmu;
      pcmu.codecPayloadName = "PCMU";
      pcmu.codecFrequency = 8000;
      miAudio.codecs.push_back(pcmu);
      */

      if (audioCodec)
      {
         audioCodec->disableDtx();
         audioCodec->setBitrate(16000);
         audioCodec->allSettings()[0].webrtcCodecInfo.audio.pacsize = 960 * 3;
      }
   }
}

PttSenderSession::~PttSenderSession()
{
   CPCAPI2::Media::MediaManagerInterface* mediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(mManager->getPhone()));
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaIf->media_stack_ptr()->codecFactory());
   std::shared_ptr<webrtc_recon::OpusCodecImpl> audioCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", 48000));
   if (audioCodec)
   {
      audioCodec->allSettings()[0].webrtcCodecInfo.audio.pacsize = 960;
      audioCodec->setBitrate(64000);
      audioCodec->enableDtx();
   }
}

// Associated to DeadlineTimerHandler

void PttSenderSession::onTimer(PttSession* session, unsigned short timerId, void* appState)
{
   StackLog(<< "PttSenderSession::onTimer(): timerId: " << timerId);
   PttSenderSessionState* state = static_cast<PttSenderSessionState*>(getFactory()->getState(mState));
   if (!state) return;

   switch (timerId)
   {
      case PTT_SESSION_WAIT_FOR_TALKSPURT_TIMER_ID: state->onSpurtTimeout(session); break;
      case PTT_SESSION_EXPIRY_TIMER_ID: state->onExpiryTimeout(session); break;
      case PTT_SESSION_CONNECTED_TIMER_ID: state->onConnectedTimeout(session); break;
      case PTT_SESSION_CONNECTION_ERROR_TIMER_ID: state->onConnectionErrorTimeout(session); break;
      case PTT_SESSION_DISCONNECTED_TIMER_ID: state->onDisconnectedTimeout(session); break;
      case PTT_SESSION_INIT_RETRY_TIMER_ID: state->onInitRetryTimeout(session); break;
      default:
      {
         InfoLog(<< "PttSenderSession::onTimer(): " << this << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
}

void PttSenderSession::cancelTimers()
{
   mSpurtTimer.cancel();
   mSessionTimer.cancel();
   mConnectedTimer.cancel();
   mConnectionErrorTimer.cancel();
   mDisconnectedTimer.cancel();
   mInitRetryTimer.cancel();
}

uint32_t PttSenderSession::getPttSessionConnectedCallCount()
{
   uint32_t connectedCount = 0;
   for (PttSessionCalls::iterator j = mCalls.begin(); j != mCalls.end(); ++j)
   {
      if (j->second->state == PttSessionCallStateType_Connected)
      {
         connectedCount++;
      }
   }

   return connectedCount;
}

uint32_t PttSenderSession::getPttSessionEndedCallCount()
{
   uint32_t endedCount = 0;
   for (PttSessionCalls::iterator j = mCalls.begin(); j != mCalls.end(); ++j)
   {
      if (j->second->state == PttSessionCallStateType_Ended)
      {
         endedCount++;
      }
   }

   return endedCount;
}

uint32_t PttSenderSession::getPttSessionTotalCallCount()
{
   uint32_t callCount = 0;
   callCount = mCalls.size();
   return callCount;
}

bool PttSenderSession::doesCallExist(uint32_t call)
{
   if (mCalls.size() != 0)
   {
      if (mCalls.begin()->second->handle == call) return true;
   }
   return false;
}

bool PttSenderSession::doesCallExist(resip::Tuple& endpoint)
{
   PttSessionCalls::iterator j = mCalls.find(endpoint);
   if (j == mCalls.end()) return false;
   return true;
}

PttSessionCall* PttSenderSession::getPttSessionCall(resip::Tuple& endpoint)
{
   PttSessionCalls::iterator j = mCalls.find(endpoint);
   if (j == mCalls.end()) return NULL;
   return (j->second);
}

bool PttSenderSession::areAllPttCallsConnected()
{
   int totalCalls = getPttSessionTotalCallCount();
   if (totalCalls == 0)
   {
      DebugLog(<< "PttSenderSession::areAllPttSessionsConnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " no calls for ptt session, presume not connected");
      return false;
   }

   int callConnectedCount = getPttSessionConnectedCallCount();
   if (callConnectedCount != totalCalls)
   {
      DebugLog(<< "PttSenderSession::areAllPttSessionsConnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " callConnectedCount: " << callConnectedCount << " total-calls: " << totalCalls << " all are not connected");
      return false;
   }

   DebugLog(<< "PttSenderSession::areAllPttSessionsConnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " total-calls: " << totalCalls << " all are connected");

   return true;
}

bool PttSenderSession::areAllPttCallsDisconnected()
{
   int totalCalls = getPttSessionTotalCallCount();
   if (totalCalls == 0)
   {
      DebugLog(<< "PttSenderSession::areAllPttCallsDisconnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " no calls for ptt session, presume disconnected");
      return true;
   }

   int callEndedCount = getPttSessionEndedCallCount();
   if (callEndedCount != totalCalls)
   {
      DebugLog(<< "PttSenderSession::areAllPttCallsDisconnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " callEndedCount: " << callEndedCount << " total-calls: " << totalCalls << " all are not disconnected");
      return false;
   }

   DebugLog(<< "PttSenderSession::areAllPttCallsDisconnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " total-calls: " << totalCalls << " all are disconnected");

   return true;
}

void PttSenderSession::resetSpurtTimer()
{
   mSpurtTimer.cancel();
   mSpurtTimer.expires_from_now(mManager->getInternalSettings().outgoingSessionWaitForTalkSpurtMsecs);
   mSpurtTimer.async_wait(this, PTT_SESSION_WAIT_FOR_TALKSPURT_TIMER_ID, NULL);
}

void PttSenderSession::resetSessionTimer()
{
   mSessionTimer.cancel();
   mSessionTimer.expires_from_now(mManager->getInternalSettings().outgoingSessionExpiryMsecs);
   mSessionTimer.async_wait(this, PTT_SESSION_EXPIRY_TIMER_ID, NULL);
}

void PttSenderSession::resetConnectedTimer()
{
   mConnectedTimer.cancel();
   mConnectedTimer.expires_from_now(mManager->getInternalSettings().outgoingConnectedMsecs);
   mConnectedTimer.async_wait(this, PTT_SESSION_CONNECTED_TIMER_ID, NULL);
}

void PttSenderSession::resetConnectionErrorTimer()
{
   mConnectionErrorTimer.cancel();
   mConnectionErrorTimer.expires_from_now(mManager->getInternalSettings().outgoingConnectionErrorMsecs);
   mConnectionErrorTimer.async_wait(this, PTT_SESSION_CONNECTION_ERROR_TIMER_ID, NULL);
}

void PttSenderSession::resetDisconnectedTimer()
{
   mDisconnectedTimer.cancel();
   mDisconnectedTimer.expires_from_now(mManager->getInternalSettings().outgoingDisconnectedMsecs);
   mDisconnectedTimer.async_wait(this, PTT_SESSION_DISCONNECTED_TIMER_ID, NULL);
}

void PttSenderSession::resetInitRetryTimer()
{
   mInitRetryTimer.cancel();
   mInitRetryTimer.expires_from_now(mManager->getInternalSettings().unicastInitRetryIntervalMsecs);
   mInitRetryTimer.async_wait(this, PTT_SESSION_INIT_RETRY_TIMER_ID, NULL);
}

PttSenderLanSession::PttSenderLanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt) :
PttSenderSession(manager, PttSession::PttSessionType_Lan, ptt),
mConnection((PeerConnectionHandle)(-1)),
mPeerConnMgr(manager->getPeerManager()),
mDeemedConnected(false)
{
}

PttSenderLanSession::~PttSenderLanSession()
{
}

uint32_t PttSenderLanSession::getPttSessionTotalCallCount()
{
   uint32_t callCount = 0;
   for (PttSessionCalls::iterator k = mCalls.begin(); k != mCalls.end(); ++k)
   {
      PeerSenderCall* senderCall = dynamic_cast<PeerSenderCall*>(k->second);
      if (senderCall && senderCall->primary && senderCall->replaced)
      {
         continue; // ignore primary call as it has been replaced by a participant
      }
      callCount++;
   }
   return callCount;
}

bool PttSenderLanSession::isPttSessionDeemedConnected()
{
   return mDeemedConnected;
}

bool PttSenderLanSession::isPttSessionDeemedDisconnected()
{
   bool deemDisconnected = false;

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   int totalCalls = getPttSessionConnectedCallCount();
   if (totalCalls == 0)
   {
      DebugLog(<< "PttSenderLanSession::isPttSessionDeemedDisconnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " no calls for ptt session, presume disconnected");
      // TODO: Should we check the ptt session state
      return true;
   }

   int callEndedCount = getPttSessionEndedCallCount();
   bool usePercentage = ((double)mManager->getInternalSettings().connectedEndpointThresholdPercentage > 0);
   if (usePercentage)
   {
      deemDisconnected = ((((double)callEndedCount / (double)totalCalls) * 100) >= (double)mManager->getInternalSettings().connectedEndpointThresholdPercentage);
   }
   else
   {
      deemDisconnected = (callEndedCount == totalCalls);
   }
   StackLog(<< "PttSenderLanSession::isPttSessionDeemedDisconnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " callEndedCount: " << callEndedCount << " total-calls: " << totalCalls << " deem-disconnected: " << deemDisconnected);

   return deemDisconnected;
}

void PttSenderLanSession::handleEnd()
{
   // always send the end message out, even if no calls are connected,
   // since there's a chance of onClientOffer still coming our way (race condition)

   sendPttEnd();
}

void PttSenderLanSession::sendPttInitiate()
{
   resip::Data sessionId = resip::Random::getCryptoRandomBase64(4);
   mSessionId = sessionId.c_str();

   const std::map<std::string, PttIdentity>& endpoints = mManager->getEndpointList();
   std::stringstream ss;
   ss << "PttSenderLanSession::sendPttInitiate(): current endpoint list: ";
   for (std::map<std::string, PttIdentity>::const_iterator it = endpoints.begin(); it != endpoints.end(); ++it)
   {
      ss << "" << it->first << ",";
   }
   DebugLog(<< ss.str());

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   // TODO: Check of server addresses should be removed once the apps have transitioned to using the service type
   mInitiateEvent.pttHandle = mPtt;
   mInitiateEvent.sessionId = mSessionId.c_str();
   mInitiateEvent.channelId = mChannelId.c_str();
   mInitiateEvent.senderIdentity = settings.localIdentities[0];

   if (mChannelId.empty() && (mRecipients.size() > 0))
   {
      DebugLog(<< "PttSenderLanSession::sendPttInitiate(): lan peer-ptt call to: " << mRecipients.size() << " peers");
   }
   else if (mRecipients.empty() && !mChannelId.empty())
   {
      DebugLog(<< "PttSenderLanSession::sendPttInitiate(): lan channel-ptt call to: " << mChannelId.c_str());
   }
   else
   {
      DebugLog(<< "PttSenderLanSession::sendPttInitiate(): lan ptt call receiver identity or channel not set correctly");
   }

   for (cpc::vector<PttIdentity>::iterator k = mRecipients.begin(); k != mRecipients.end(); ++k)
   {
      mInitiateEvent.receiverIdentity.push_back(*k);
   }

   if (mRecipients.size() == 1)
   {
      PttIdentityType identityTypeTo = mRecipients.begin()->identityType;
      for (auto itLocalIdents = settings.localIdentities.begin(); itLocalIdents != settings.localIdentities.end(); ++itLocalIdents)
      {
         if (itLocalIdents->identityType == identityTypeTo)
         {
            mInitiateEvent.senderIdentity = *itLocalIdents;
            break;
         }
      }
   }

   sendPttInitiateToTransport();
}

void PttSenderLanSession::sendPttInitiateToTransport(bool resetTid)
{
   // unsigned int messageId = (unsigned int)resip::Random::getCryptoRandom();
   if (resetTid)
   {
      if (mInitiateEvent.transactionId > 0)
      {
         mManager->unsendAll(mInitiateEvent.transactionId);
      }
#ifdef CPCAPI2_AUTO_TEST
      mTransactionId = mManager->getInternalSettings().getTransactionID();
#else
      mTransactionId = ::time(NULL);
#endif
      mInitiateEvent.transactionId = mTransactionId;
   }

   DebugLog(<< "PttSenderLanSession::sendPttInitiateToTransport(): sending ptt initiate with TID:" << mTransactionId);
   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onInitiatePtt", true);
   serializer.addValue("PttInitiateEvent", mInitiateEvent);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), false, false, "", true, 0, mInitiateEvent.transactionId);
   resetInitRetryTimer();
}

void PttSenderLanSession::sendPttEnd()
{
   if (mCalls.size() != 0)
   {
      mPeerConnMgr->close(mCalls.begin()->second->handle);
   }

   cpc::vector<cpc::string> endpoints;
   for (PttSessionCalls::iterator k = mCalls.begin(); k != mCalls.end(); ++k)
   {
      InfoLog(<< "PttSenderLanSession::sendPttEnd(): " << this << " service: " << mManager->getService() << " ending outgoing ptt: " << mPtt << " peer call: " << k->second->handle << " endpoint: " << k->first);

      std::stringstream endpoint;
      endpoint << k->first.presentationFormat() << ":" << k->first.getPort();
      endpoints.push_back(endpoint.str().c_str());

      PeerSenderCall* call = dynamic_cast<PeerSenderCall*>(k->second);
      if (!call) return;

      call->state = PttSessionCallStateType_Ended;
      call->connectionState = SignalingState_Closed;

      delete call;
   }
   mCalls.clear();

   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mManager->getServiceSettings().unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif

   cancelInitRetryTimer();
   mManager->unsendAll(mInitiateEvent.transactionId);

   PttEndEvent endEvent;
   endEvent.pttHandle = mPtt;
   endEvent.sessionId = mSessionId.c_str();
   endEvent.transactionId = ::time(NULL);
   endEvent.channelId = mChannelId.c_str();
   endEvent.senderIpAddress = localIpAddress.c_str();
   endEvent.senderIpPort = mManager->getServiceSettings().httpPort;

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onEndPtt", true);
   serializer.addValue("PttEndEvent", endEvent);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), 0, false, "", false);
}

void PttSenderLanSession::enableIncomingMediaInactivityMonitor()
{
   InfoLog(<< "PttSenderLanSession::enableIncomingMediaInactivityMonitor: " << this << " service: " << mService << " ptt: " << getPtt() << " connected call-count: " << getPttSessionConnectedCallCount());
   for (PttSessionCalls::iterator k = mCalls.begin(); k != mCalls.end(); ++k)
   {
      if (k->second)
      {
         InfoLog(<< "PttSenderLanSession::enableIncomingMediaInactivityMonitor(): " << this << " service: " << mManager->getService() << " enabling media activity on outgoing ptt: " << mPtt << " peer call: " << k->second->handle << " endpoint: " << k->first);
#ifdef CPCAPI2_AUTO_TEST
         if (mManager->getInternalSettings().mediaInactivityMonitorEnabled)
         {
            mPeerConnMgr->startMediaInactivityMonitor(k->second->handle);
         }
#else
         mPeerConnMgr->startMediaInactivityMonitor(k->second->handle);
#endif
      }
   }
}

void PttSenderLanSession::disableIncomingMediaInactivityMonitor()
{
   InfoLog(<< "PttSenderLanSession::disableIncomingMediaInactivityMonitor: " << this << " service: " << mService << " ptt: " << getPtt() << " connected call-count: " << getPttSessionConnectedCallCount());
   for (PttSessionCalls::iterator k = mCalls.begin(); k != mCalls.end(); ++k)
   {
      if (k->second)
      {
         InfoLog(<< "PttSenderLanSession::disableIncomingMediaInactivityMonitor(): " << this << " service: " << mManager->getService() << " disabling media activity on outgoing ptt: " << mPtt << " peer call: " << k->second->handle << " endpoint: " << k->first);
#ifdef CPCAPI2_AUTO_TEST
         if (mManager->getInternalSettings().mediaInactivityMonitorEnabled)
         {
            mPeerConnMgr->stopMediaInactivityMonitor(k->second->handle);
         }
#else
         mPeerConnMgr->stopMediaInactivityMonitor(k->second->handle);
#endif
      }
   }
}

void PttSenderLanSession::handleClientOfferEvent(const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSenderLanSession::handleClientOfferEvent(): ptt: " << getPtt() << " incoming endpoint: " << incomingTuple << " sdp: " << evt.sessionDescription.c_str());

   PeerConnection::SessionDescription sdp;
   sdp.sdpString = evt.sessionDescription;
   sdp.sdpLen = evt.sessionDescription.size();
   sdp.sdpType = PeerConnection::SessionDescription::SessionDescriptionType_Offer;

   PeerSenderCall* existingCall = NULL;
   resip::ParseBuffer pb(evt.sessionDescription, evt.sessionDescription.size());
   resip::SdpContents resipSdp;
   resipSdp.parse(pb);

   for (auto icall = mCalls.begin(); icall != mCalls.end(); ++icall)
   {
      PeerSenderCall* senderCall = dynamic_cast<PeerSenderCall*>(icall->second);
      if (senderCall && (senderCall->endpoint.presentationFormat() == incomingTuple.presentationFormat()))
      {
         resip::ParseBuffer pbCurrent(senderCall->sdp.c_str(), senderCall->sdp.size());
         resip::SdpContents resipSdpCurrent;
         resipSdpCurrent.parse(pbCurrent);

         if ((resipSdpCurrent.session().origin().getSessionId() == resipSdp.session().origin().getSessionId()) && (resipSdpCurrent.session().origin().getVersion() == resipSdp.session().origin().getVersion()))
         {
            StackLog(<< "PttSenderLanSession::handleClientOfferEvent(): ptt: " << getPtt() << " ignoring request as request from endpoint: " << incomingTuple << " has already been handled, session: " << getSessionType() << " state: " << PttSessionState::getName(mState));
            return;
         }
         existingCall = senderCall;
         if (!existingCall->primary)
         {
            mCalls.erase(icall);
         }
         break;
      }
   }

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   PeerConnection::PeerConnectionManager* peerMgr = mPeerConnMgr;
   PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(peerMgr);

   std::chrono::duration<long long, std::milli> delta = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mStartTime);
   bool isNewPeer = false;
   if (mConnection == (PeerConnectionHandle)(-1))
   {
      mConnection = peerMgr->createPeerConnection();
      isNewPeer = true;
      StatLog(PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_NEW_CALL, << "PttSenderLanSession::handleClientOfferEvent(): " << this << " ptt: " << evt.pttHandle << " new peer connection: " << mConnection << " endpoint: " << incomingTuple << " time since session start: " << delta.count());
      PeerConnectionSettings pcSettings;
      pcSettings.mixContribution = 0; // do NOT mix this stream with any others
      pcSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
      pcSettings.sessionName = "cpcptt";
      pcSettings.certAor = "<EMAIL>";

      pcSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_None;
      pcSettings.secureMediaRequired = false;
      pcSettings.rtcpEnabled = false;
      pcSettings.mediaDscp = settings.mediaDscp;

      peerMgr->setDefaultSettings(mConnection, pcSettings);
      if (peerMgrInternal)
      {
         peerMgrInternal->setMediaInactivityTimeoutMsecs(mConnection, mManager->getServiceSettings().mediaInactivityIntervalSeconds * 1000);
      }

      MediaStreamHandle audioMediaStream = peerMgr->createMediaStream();
      CPCAPI2::PeerConnection::MediaInfo miAudio;
      miAudio.mediaDirection = PeerConnection::MediaDirection_SendOnly;
      miAudio.mediaType = PeerConnection::MediaType_Audio;

      mManager->setCodecMediaInfo(miAudio);

      peerMgr->configureMedia(mConnection, audioMediaStream, miAudio);
      peerMgr->setRemoteDescription(mConnection, sdp);
      peerMgr->createAnswer(mConnection);
   }
   else
   {
      if (existingCall)
      {
         PeerConnection::SessionDescription sdpCurrent;
         sdpCurrent.sdpString = existingCall->sdp.c_str();
         sdpCurrent.sdpLen = existingCall->sdp.size();
         sdpCurrent.sdpType = PeerConnection::SessionDescription::SessionDescriptionType_Offer;

         if (existingCall->primary)
         {
            peerMgrInternal->pauseTxMedia(mConnection, sdpCurrent);
            peerMgr->addAdditionalDestination(mConnection, sdp);
            existingCall->state = PttSessionCallStateType_Ended;
            existingCall->connectionState = SignalingState_Closed;
            existingCall->replaced = true;
         }
         else
         {
            peerMgr->removeAdditionalDestination(mConnection, sdpCurrent);
            peerMgr->addAdditionalDestination(mConnection, sdp);
         }
         StatLog(PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_UPDATE, << "PttSenderLanSession::handleClientOfferEvent(): " << this << " ptt: " << evt.pttHandle << " updating existing peer connection: " << mConnection << " endpoint: " << incomingTuple << " primary connection: " << existingCall->primary << " call-count: " << getPttSessionConnectedCallCount() << " time since session start: " << delta.count());
      }
      else
      {
         StatLog(PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_EXISTING_CALL, << "PttSenderLanSession::handleClientOfferEvent(): " << this << " ptt: " << evt.pttHandle << " existing peer connection: " << mConnection << " endpoint: " << incomingTuple << " call-count: " << (getPttSessionConnectedCallCount() + 1) << " time since session start: " << delta.count());
         peerMgr->addAdditionalDestination(mConnection, sdp);
      }
   }

   PeerSenderCall* call = NULL;
   if (existingCall && (!existingCall->primary))
   {
      call = existingCall;
   }
   else
   {
      call = new PeerSenderCall(mConnection, incomingTuple);
      call->primary = isNewPeer;
   }

   call->ptt = getPtt();
   call->state = PttSessionCallStateType_Connected;
   call->connectionState = SignalingState_HaveRemoteOffer;
   call->sdp = evt.sessionDescription;
   mCalls[incomingTuple] = call;
   mSessionId = evt.sessionId;
   mMediaInfoReceived++;
   mMediaInfoSent++;

#ifdef CPCAPI2_AUTO_TEST
   mManager->getPttManager()->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttClientOfferEvent), mManager->getService(), getPtt(), evt);

   if (existingCall)
   {
      PttClientOfferUpdateEvent clientOfferEvt;
      clientOfferEvt.pttHandle = getPtt();
      clientOfferEvt.pc = mConnection;
      clientOfferEvt.sessionId = evt.sessionId;
      clientOfferEvt.sessionDescription = evt.sessionDescription.c_str();
      clientOfferEvt.endpoint = incomingTuple.presentationFormat().c_str();
      mManager->getPttManager()->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttClientOfferUpdateEvent), mManager->getService(), getPtt(), clientOfferEvt);
   }
#endif

   // TODO:
   // With multiple streams in one call, need a more accurate way of assuring that the media-info has been sent, currently
   // with one peer call, only one onCreateAnswerResult and one onSetLocalSessionDescriptionResult is received, so can no
   // longer count on them to increment the media-info sent counter.
   DebugLog(<< "PttSenderLanSession::handleClientOfferEvent(): " << this << " ptt: " << evt.pttHandle << " call connection: " << mConnection << " endpoint: " << incomingTuple << " Time since session start: " << delta.count() << " ms. Connected peer call count: " << getPttSessionConnectedCallCount());
}

void PttSenderLanSession::handleCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   StackLog(<< "PttSenderLanSession::handleCreateAnswerResult(): ptt: " << getPtt() << " pc: " << pc << " connected call count: " << getPttSessionConnectedCallCount() << " handled in " << getSessionType() << " session in state: " << PttSessionState::getName(mState));

   for (const auto& icall : mCalls)
   {
      PeerSenderCall* call = dynamic_cast<PeerSenderCall*>(icall.second);
      if (call)
      {
         InfoLog(<< "PttSenderLanSession::handleCreateAnswerResult(): " << this << " ptt: " << getPtt() << " call state: " << call->getState(call->state) << " for service: " << mService << " connection: " << pc);
      }
   }

   mPeerConnMgr->setLocalDescription(pc, args.sdp);

   return;
}

void PttSenderLanSession::handlePttReceiverEnded(const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple)
{
   bool anyMediaInactive = false;
   bool found = false;
   std::string ipAddr = incomingTuple.presentationFormat().c_str();
   for (auto icall = mCalls.begin(); icall != mCalls.end(); ++icall)
   {
      PeerSenderCall* pcall = dynamic_cast<PeerSenderCall*>(icall->second);
      if (pcall && ipAddr.compare(pcall->endpoint.presentationFormat().c_str()) == 0)
      {
         if (pcall->state == PttSessionCallStateType_Connected)
         {
            DebugLog(<< "PttSenderLanSession::onPttReceiverEndedEvent(): ptt: " << getPtt() << " pc: " << mConnection << " marking call to " << ipAddr << ", sessionID: " << evt.sessionId << " as ended, total calls: " << getPttSessionConnectedCallCount());
            pcall->state = PttSessionCallStateType_Ended;
            found = true;

            PeerConnection::SessionDescription sdpCurrent;
            sdpCurrent.sdpString = pcall->sdp.c_str();
            sdpCurrent.sdpLen = pcall->sdp.size();
            sdpCurrent.sdpType = PeerConnection::SessionDescription::SessionDescriptionType_Offer;
            PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(mPeerConnMgr);
            if (peerMgrInternal)
            {
               peerMgrInternal->pauseTxMedia(mConnection, sdpCurrent);
            }
            break;
         }
         else
         {
            DebugLog(<< "PttSenderLanSession::onPttReceiverEndedEvent(): ptt: " << getPtt() << " pc: " << mConnection << " call to " << ipAddr << ", sessionID: " << evt.sessionId << " already ended. Exiting, total calls: " << getPttSessionConnectedCallCount());
            return;
         }
      }
   }
   if (!found)
   {
      DebugLog(<< "PttSenderLanSession::onPttReceiverEndedEvent(): ptt: " << getPtt() << " pc: " << mConnection << " call to " << ipAddr << ", sessionID: " << evt.sessionId << " not found. Exiting, total calls: " << getPttSessionConnectedCallCount());
      return;
   }

   PttReceiverDisconnectedEvent disconnectEvt;
   disconnectEvt.service = mService;
   disconnectEvt.currentState = mState;
   disconnectEvt.channelId = getChannelId();
   disconnectEvt.connectedCalls = getPttSessionConnectedCallCount();
   disconnectEvt.totalCalls = getPttSessionTotalCallCount();
   disconnectEvt.receiverIdentity = evt.receiverIdentity;
   disconnectEvt.reason = getReceiverEndReasonCode(evt.reason.c_str());
   mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttReceiverDisconnected), mService, getPtt(), disconnectEvt);

   if (areAllPttCallsDisconnected())
   {
      DebugLog(<< "PttSenderLanSession::onPttReceiverEndedEvent(): all ptt recipients disconnected on ptt: " << getPtt() << " pc: " << mConnection);
      if (getChannelId().empty() && (disconnectEvt.reason != PttReceiverEndReasonType_MediaInactive))
      {
         // Destroy a p2p call unless media-inactive is the reason for disconnect
         end(getPtt(), PTT_SESSION_END_REASON_ALL_RECEIVERS_DISCONNECTED);
      }
   }
}

void PttSenderLanSession::handleDiscoveryListUpdate(const PttDiscoveryListUpdate& args)
{
   DebugLog(<< "PttSenderLanSession::handleDiscoveryListUpdate(): ptt: " << getPtt());
   sendPttInitiateToTransport();
}

PttSenderWanSession::PttSenderWanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt) :
PttSenderSession(manager, PttSession::PttSessionType_Wan, ptt),
mCloudConnector(0),
mCloudConference(0),
mCloudSession(0),
mCloudParticipant(0),
mCloudSessionStatus(CPCAPI2::ConferenceConnector::SessionStatus_NotConnected),
mCloudPeerConnection(0)
{
}

PttSenderWanSession::~PttSenderWanSession()
{
}

bool PttSenderWanSession::isPttSessionDeemedConnected()
{
   bool deemConnected = false;

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   int ourIdentityCount = 0;
   for (auto i = mCloudParticipantList.begin(); i != mCloudParticipantList.end(); ++i)
   {
      if (mManager->isSessionInStaleSessionList(mCloudConference, (*i)))
      {
         // Ignore session if in stale list
         continue;
      }
      for (auto j = settings.localIdentities.begin(); j != settings.localIdentities.end(); ++j)
      {
         if (i->address == j->userName)
         {
            // TODO: Need to handle same-identity from multiple devices, need the UUID
            // Verify peer connection as an additional filter to allow ptt calls between devices that have the same ptt-identity
            if (i->peerConnection == mCloudPeerConnection)
            {
               ourIdentityCount++;
            }
         }
      }
   }

   // Note that it is possible that the participant list does not contain us when we end talk-spurt, still deem
   // the call connected in those cases, in case the talk-spurt is restarted.
   int participants = (mCloudParticipantList.size() - ourIdentityCount);
   deemConnected = (participants > 0); // Regardless of channel or p2p calls, call can be deemed connected if more than 1 participant exists other than this caller

   DebugLog(<< "PttSenderWanSession::isPttSessionDeemedConnected(): " << this << " service: " << mManager->getService() << " ptt: " << mPtt << " cloud-conference: " << mCloudConference << " cloud-session: " << mCloudSession << " ourIdentityCount: " << ourIdentityCount << " total-participants: " << mCloudParticipantList.size() << " deem-connected: " << deemConnected);

   return deemConnected;
}

bool PttSenderWanSession::isPttSessionDeemedDisconnected()
{
   bool deemDisconnected = false; // TODO: fix this

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   return deemDisconnected;
}

void PttSenderWanSession::handleEnd()
{
   mManager->endConfSession(mCloudSession);
}

void PttSenderWanSession::sendPttInitiate()
{
   resip::Data sessionId = resip::Random::getCryptoRandomBase64(4);
   mSessionId = sessionId.c_str();
#ifdef CPCAPI2_AUTO_TEST
   mTransactionId = mManager->getInternalSettings().getTransactionID();
   DebugLog(<< "PttSenderWanSession::sendPttInitiate(): sending ptt initiate with TID:" << mTransactionId);
#else
   mTransactionId = ::time(NULL);
#endif

   const std::map<std::string, PttIdentity>& endpoints = mManager->getEndpointList();
   std::stringstream ss;
   ss << "PttSenderWanSession::sendPttInitiate(): current endpoint list: ";
   for (std::map<std::string, PttIdentity>::const_iterator it = endpoints.begin(); it != endpoints.end(); ++it)
   {
      ss << "" << it->first << ",";
   }
   DebugLog(<< ss.str());

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   if (mRecipients.size() > 0)
   {
      cpc::vector<PttIdentity>::iterator k = mRecipients.begin();
      if (k != mRecipients.end())
      {
         DebugLog(<< "PttSenderWanSession::sendPttInitiate(): wan peer-ptt call to: " << k->userName);
         mCloudSession = mManager->startConfSessionWith(k->userName, mPtt, k->identityType, mCloudConference);
      }
   }
   else if (mChannelId.size() > 0)
   {
      DebugLog(<< "PttSenderWanSession::sendPttInitiate(): wan channel-ptt call to: " << mChannelId);
      PttIdentityType identityType = PttIdentityType_XMPP;
      if (settings.localIdentities.size() > 0)
      {
         identityType = settings.localIdentities[0].identityType;
      }
      mCloudSession = mManager->startConfSessionWith(mChannelId, mPtt, identityType, mCloudConference);
   }
}

void PttSenderWanSession::sendPttEnd()
{
}

void PttSenderWanSession::handleCloudSessionConnected(const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt)
{
   StackLog(<< "PttSenderWanSession::handleCloudSessionConnected(): ptt: " << getPtt() << " call count: " << mCalls.size() << " handled in " << getSessionType() << " session in state: " << PttSessionState::getName(mState));
   mCloudSessionStatus = evt.sessionStatus;
}

void PttSenderWanSession::updateCloudParticipant(const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& evt)
{
   for (cpc::vector<ConferenceConnector::CloudConferenceParticipantInfo>::const_iterator i = evt.participantList.begin(); i != evt.participantList.end(); ++i)
   {
      if (mCloudParticipant == 0)
      {
         for (auto j = mManager->getServiceSettings().localIdentities.begin(); j != mManager->getServiceSettings().localIdentities.end(); ++j)
         {
            if ((!((*i).address.empty())) && ((*i).address == j->userName) && (mCloudPeerConnection != 0) && (i->peerConnection == mCloudPeerConnection))
            {
               mCloudParticipant = i->participant;
               break;
            }
         }
      }
   }
}

PttSenderIdleState::PttSenderIdleState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_Idle)
{
}

PttSenderIdleState::~PttSenderIdleState()
{
}

void PttSenderIdleState::onEntry(PttSession* session)
{
   PttLog(<< "PttSenderIdleState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());

   if (webrtc::AudioDeviceModule* adm = mManager->getMediaStack()->voe_base()->audio_device())
   {
      int32_t rc = adm->StopRecording();
      DebugLog(<< "PttSenderIdleState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " stop audio device recording, rc: " << rc);
   }
   session->disableWifiStatusMonitor();
}

void PttSenderIdleState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderIdleState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   session->enableWifiStatusMonitor();
}

int PttSenderIdleState::addRecipient(PttSession* session, PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   PttLog(<< "PttSenderIdleState::addRecipient(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " identity: " << CPCAPI2::PushToTalk::GetPttIdentityUrl(identity) << " session: " << session->getSessionType());
   static_cast<PttSenderSession*>(session)->mRecipients.push_back(identity);
   return kSuccess;
}

int PttSenderIdleState::setChannel(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& channel)
{
   PttLog(<< "PttSenderIdleState::setChannel(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " channel: " << channel << " session: " << session->getSessionType());
   static_cast<PttSenderSession*>(session)->mChannelId = channel.c_str();
   return kSuccess;
}

int PttSenderIdleState::start(PttSession* session, PushToTalkSessionHandle ptt)
{
   PttLog(<< "PttSenderIdleState::start(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " session: " << session->getSessionType());
   PttSenderSession* senderSession = static_cast<PttSenderSession*>(session);
   senderSession->mStartTime = std::chrono::system_clock::now();
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);
   if (wanSession)
   {
      // TODO: Should we add protection for recipients as well along with channels
      std::string channelId = static_cast<PttSenderSession*>(session)->mChannelId.c_str();
      if (channelId.size() > 0)
      {
         /*PttIdentityType identityType = PttIdentityType_XMPP;
         if (settings.localIdentities.size() > 0)
         {
            identityType = settings.localIdentities[0].identityType;
         }*/
         CPCAPI2::ConferenceConnector::CloudConferenceHandle conference = mManager->getCloudConference(channelId);
         wanSession->mCloudConference = conference; // mCloudConference is only set after we start the ptt conference, so set it now
         if (mManager->doWeHaveChannelOverride(conference))
         {
            PttLog(<< "PttSenderIdleState::start(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " session: " << session->getSessionType() << " channel: " << channelId << " ending call due to channel-override");
            senderSession->end(senderSession->getPtt(), PTT_SESSION_END_REASON_CHANNEL_OVERRIDE);
            return kSuccess;
         }
      }
   }

   CPCAPI2::Media::MediaManagerInterface* mm = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mm->media_stack_ptr();
   webrtc_recon::MediaStackSettings mediaSettings = *mediaStack->settings();
   senderSession->mMediaSettings.reset(new webrtc_recon::MediaStackSettings(mediaSettings));
   mediaSettings.rtcpXrVoipMetricsEnabled = false;
   mediaStack->updateMediaSettings(mediaSettings);

   senderSession->sendPttInitiate();
   session->changeState(PttSessionState_Initiated);
   return kSuccess;
}

PttSenderInitiatedState::PttSenderInitiatedState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_Initiated)
{
}

PttSenderInitiatedState::~PttSenderInitiatedState()
{
}

void PttSenderInitiatedState::onEntry(PttSession* session)
{
   PttSenderSession* senderSession = static_cast<PttSenderSession*>(session);
   senderSession->cancelTimers();
   senderSession->resetConnectionErrorTimer();
   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);

   mManager->initiateRecording(session->getPtt());

   PttLog(<< "PttSenderInitiatedState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   if (wanSession)
   {
      static_cast<PttSenderSession*>(session)->resetConnectedTimer();
   }
   else
   {
      static_cast<PttSenderSession*>(session)->resetInitRetryTimer();
   }
}

void PttSenderInitiatedState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderInitiatedState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->cancelConnectedTimer();
   static_cast<PttSenderSession*>(session)->cancelConnectionErrorTimer();
}

void PttSenderInitiatedState::onPttSenderInitiateRecordingEvent(PttSession* session, const PttSenderInitiateRecordingEvent& args)
{
   std::chrono::time_point<std::chrono::system_clock> startTime = std::chrono::system_clock::now();
   PttSenderSession* senderSession = static_cast<PttSenderSession*>(session);
   if (webrtc::AudioDeviceModule* adm = mManager->getMediaStack()->voe_base()->audio_device())
   {
      int32_t rc = adm->InitRecording();
      if (rc == 0)
      {
         senderSession->mInitRecordingSuccessful = true;
         std::chrono::duration<long long, std::milli> delta = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - startTime);
         StatLog(PTT_LOGID_SESSION_TX_INIT_RECORDING, << "PttSenderInitiatedState::onPttSenderInitiateRecordingEvent(): " << " ptt: " << session->getPtt() << " early InitRecording took " << delta.count() << " ms - rc: " << rc << " init recording " << (senderSession->mInitRecordingSuccessful ? "success" : "failure"));
         PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
         if (lanSession && lanSession->mDeemedConnected)
         {
            session->changeState(PttSessionState_Active); return;
         }
      }
      else
      {
         ErrLog(<< "PttSenderInitiatedState::onPttSenderInitiateRecordingEvent(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " failed in InitRecording");
         PttSessionErrorEvent evt;
         evt.service = mService;
         evt.errorCode = PttSessionError_InitRecordingFailure;
         mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, session->getPtt(), evt);
         end(session, session->getPtt(), PTT_SESSION_REASON_INIT_RECORDING_FAILURE);
      }
   }
}

void PttSenderInitiatedState::onConnectedTimeout(PttSession* session)
{
   StatLog(PTT_LOGID_SESSION_TX_CONNECTION_TIMEOUT, << "PttSenderInitiatedState::onConnectedTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " channel: " << session->getChannelId() << " local-endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << session->mRemoteEndpoint.presentationFormat().c_str());
   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);
   if (wanSession)
   {
      if (wanSession->isPttSessionDeemedConnected())
      {
         DebugLog(<< "PttSenderInitiatedState::onConnectedTimeout(): " << this << " changing ptt session state to active - service: " << mManager->getService() << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " init-recording completed: " << wanSession->mInitRecordingSuccessful);
         wanSession->cancelTimers();
         if (wanSession->mInitRecordingSuccessful)
         {
            session->changeState(PttSessionState_InitialActive);
         }
      }
   }
}

void PttSenderInitiatedState::onConnectionErrorTimeout(PttSession* session)
{
   StatLog(PTT_LOGID_SESSION_TX_CONNECTION_ERROR_TIMEOUT, << "PttSenderInitiatedState::onConnectionErrorTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " channel: " << session->getChannelId() << " local-endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << session->mRemoteEndpoint.presentationFormat().c_str());
   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);
   if (wanSession)
   {
      if (wanSession->isPttSessionDeemedConnected())
      {
         DebugLog(<< "PttSenderInitiatedState::onConnectionErrorTimeout(): " << this << " changing ptt session state to active - service: " << mService << " ptt: " << session->getPtt()  << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " due to connection error timeout, init-recording completed: " << wanSession->mInitRecordingSuccessful);
         if (wanSession->mInitRecordingSuccessful)
         {
            session->changeState(PttSessionState_InitialActive);
         }
         return;
      }
   }

   if (session)
   {
      PttSessionErrorEvent evt;
      evt.service = mService;
      evt.errorCode = PttSessionError_ConnectionTimeout;
      mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, session->getPtt(), evt);
      end(session, session->getPtt(), PTT_SESSION_END_REASON_CONNECTION_ERROR_TIMEOUT);
   }
}

int PttSenderInitiatedState::onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSenderInitiatedState::onClientOfferEvent(): ptt: " << session->getPtt() << " endpoint: " << incomingTuple << " in state: " << getName());

   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      lanSession->mDeemedConnected = true;
      lanSession->handleClientOfferEvent(evt, incomingTuple);
      DebugLog(<< "PttSenderInitiatedState::onClientOfferEvent(): " << this << " changing ptt session state to active - service: " << mService << " ptt: " << session->getPtt()  << " local endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " due to first response, init-recording completed: " << lanSession->mInitRecordingSuccessful);
      if (lanSession->mInitRecordingSuccessful)
      {
         session->changeState(PttSessionState_Active);
      }
   }
   return kSuccess;
}

int PttSenderInitiatedState::onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      StackLog(<< "PttSenderInitiatedState::onCreateAnswerResult(): ptt: " << session->getPtt() << " pc: " << pc << " handled in " << session->getSessionType()  << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " session in current state: " << getName());
      lanSession->handleCreateAnswerResult(pc, args);
   }
   return kSuccess;
}

int PttSenderInitiatedState::onCloudSessionConnected(PttSession* session, PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt)
{
   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);
   if (wanSession)
   {
      wanSession->handleCloudSessionConnected(evt);
      StackLog(<< "PttSenderInitiatedState::onCloudSessionConnected(): ptt: " << session->getPtt() << " handled in " << session->getSessionType() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " participants: " << wanSession->mCloudParticipantList.size() << " session in current state: " << getName());
      if (wanSession->isPttSessionDeemedConnected())
      {
         StatLog(PTT_LOGID_SESSION_TX_WAN_CLOUD_SESSION_CONNECTED, << "PttSenderInitiatedState::onCloudSessionConnected(): " << this << " changing ptt session state to active - service: " << mService << " ptt: " << session->getPtt() << " local endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " participants: " << wanSession->mCloudParticipantList.size() << " due to deemed connectivity, init-recording completed: " << wanSession->mInitRecordingSuccessful);
         if (wanSession->mInitRecordingSuccessful)
         {
            session->changeState(PttSessionState_InitialActive);
         }
      }
   }
   return kSuccess;
}

int PttSenderInitiatedState::onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args)
{
   PttSenderWanSession* wanSession = dynamic_cast<PttSenderWanSession*>(session);
   if (wanSession)
   {
      wanSession->mCloudParticipantList = args.participantList;
      wanSession->updateCloudParticipant(args);
      for (cpc::vector<ConferenceConnector::CloudConferenceParticipantInfo>::const_iterator i = args.participantList.begin(); i != args.participantList.end(); ++i)
      {
         wanSession->mCloudParticipantHistory.insert((*i).participant);
      }
      wanSession->mMediaInfoReceived = wanSession->mCloudParticipantHistory.size() - 1; // ignore the sender participant itself
      StackLog(<< "PttSenderInitiatedState::onConferenceParticipantListUpdated(): ptt: " << ptt << " conference-connector: " << connector << " cloud-conference: " << args.conference << " handled in " << session->getSessionType() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " participants: " << args.participantList.size() << " total-responses: " << wanSession->mCloudParticipantHistory.size() << " cloud session-state: " << wanSession->mCloudSessionStatus << " session in current state: " << getName());
      if (wanSession->mCloudSessionStatus == CPCAPI2::ConferenceConnector::SessionStatus_Connected)
      {
         if (wanSession->isPttSessionDeemedConnected())
         {
            DebugLog(<< "PttSenderInitiatedState::onConferenceParticipantListUpdated(): " << this << " changing ptt session state to active - service: " << mService << " ptt: " << session->getPtt() << " participants: " << wanSession->mCloudParticipantList.size() << " local endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " due to deemed connectivity, init-recording completed: " << wanSession->mInitRecordingSuccessful);
            if (wanSession->mInitRecordingSuccessful)
            {
               session->changeState(PttSessionState_InitialActive);
            }
         }
      }
   }
   return kSuccess;
}

void PttSenderInitiatedState::onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args)
{
   DebugLog(<< "PttSenderInitiatedState::onDiscoveryListUpdate(): ptt: " << session->getPtt() << " handled in " << session->getSessionType() << " session in current state: " << getName());
   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      lanSession->handleDiscoveryListUpdate(args);
   }
}

PttSenderInitialActiveState::PttSenderInitialActiveState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_InitialActive)
{
   // Currently applicable to WAN implementation only
}

PttSenderInitialActiveState::~PttSenderInitialActiveState()
{
}

void PttSenderInitialActiveState::onEntry(PttSession* session)
{
   PttLog(<< "PttSenderInitialActiveState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->resetSpurtTimer();
}

void PttSenderInitialActiveState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderInitialActiveState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->cancelSpurtTimer();
}

int PttSenderInitialActiveState::startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt)
{
   static_cast<PttSenderSession*>(session)->cancelTimers();
   session->changeState(PttSessionState_Talking);
   return kSuccess;
}

void PttSenderInitialActiveState::onSpurtTimeout(PttSession* session)
{
   DebugLog(<< "PttSenderInitialActiveState::onSpurtTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt());
   static_cast<PttSenderSession*>(session)->cancelTimers();
   end(session, session->getPtt(), "spurt timeout");
}

PttSenderActiveState::PttSenderActiveState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_Active)
{
}

PttSenderActiveState::~PttSenderActiveState()
{
}

void PttSenderActiveState::onEntry(PttSession* session)
{
   PttLog(<< "PttSenderActiveState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->resetSessionTimer();
}

void PttSenderActiveState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderActiveState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->cancelSessionTimer();
   static_cast<PttSenderSession*>(session)->cancelConnectionErrorTimer();
}

int PttSenderActiveState::startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt)
{
   static_cast<PttSenderSession*>(session)->cancelTimers();

   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      static_cast<PttSenderSession*>(session)->resetInitRetryTimer();
   }

   session->changeState(PttSessionState_Talking);
   return kSuccess;
}

int PttSenderActiveState::onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      StackLog(<< "PttSenderActiveState::onClientOfferEvent(): ptt: " << session->getPtt() << " endpoint: " << incomingTuple << " in state: " << getName());
      PttSenderLanSession* senderSession = static_cast<PttSenderLanSession*>(session);
      senderSession->handleClientOfferEvent(evt, incomingTuple);
   }
   return kSuccess;
}

int PttSenderActiveState::onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      StackLog(<< "PttSenderActiveState::onCreateAnswerResult(): ptt: " << session->getPtt() << " pc: " << pc << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " handled in " << session->getSessionType() << " session in current state: " << getName());
      PttSenderLanSession* senderSession = static_cast<PttSenderLanSession*>(session);
      senderSession->handleCreateAnswerResult(pc, args);
   }
   return kSuccess;
}

void PttSenderActiveState::onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args)
{
   DebugLog(<< "PttSenderActiveState::onDiscoveryListUpdate(): ptt: " << session->getPtt() << " handled in " << session->getSessionType() << " session in current state: " << getName());
   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      lanSession->handleDiscoveryListUpdate(args);
   }
}

PttSenderTalkingState::PttSenderTalkingState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_Talking)
{
}

PttSenderTalkingState::~PttSenderTalkingState()
{
}

void PttSenderTalkingState::onEntry(PttSession* session)
{
   PttLog(<< "PttSenderTalkingState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
}

void PttSenderTalkingState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderTalkingState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
}

int PttSenderTalkingState::endTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt)
{
   session->changeState(PttSessionState_Active);
   return kSuccess;
}

int PttSenderTalkingState::onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      StackLog(<< "PttSenderTalkingState::onClientOfferEvent(): ptt: " << session->getPtt() << " endpoint: " << incomingTuple << " in state: " << getName());
      PttSenderLanSession* senderSession = static_cast<PttSenderLanSession*>(session);
      senderSession->handleClientOfferEvent(evt, incomingTuple);
   }
   return kSuccess;
}

int PttSenderTalkingState::onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      StackLog(<< "PttSenderTalkingState::onCreateAnswerResult(): ptt: " << session->getPtt() << " pc: " << pc  << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " handled in " << session->getSessionType() << " session in current state: " << getName());
      PttSenderLanSession* senderSession = static_cast<PttSenderLanSession*>(session);
      senderSession->handleCreateAnswerResult(pc, args);
   }
   return kSuccess;
}

void PttSenderTalkingState::onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args)
{
   DebugLog(<< "PttSenderTalkingState::onDiscoveryListUpdate(): ptt: " << session->getPtt() << " handled in " << session->getSessionType() << " session in current state: " << getName());
   PttSenderLanSession* lanSession = dynamic_cast<PttSenderLanSession*>(session);
   if (lanSession)
   {
      lanSession->handleDiscoveryListUpdate(args);
   }
}

PttSenderEndingState::PttSenderEndingState(PushToTalkManagerImpl* manager) :
PttSenderSessionState(manager, PttSessionStateType::PttSessionState_Ending)
{
}

PttSenderEndingState::~PttSenderEndingState()
{
}

void PttSenderEndingState::onEntry(PttSession* session)
{
   PttLog(<< "PttSenderEndingState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->cancelTimers();
   static_cast<PttSenderSession*>(session)->resetDisconnectedTimer();

}

void PttSenderEndingState::onExit(PttSession* session)
{
   PttLog(<< "PttSenderEndingState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttSenderSession*>(session)->cancelDisconnectedTimer();
}

void PttSenderEndingState::onDisconnectedTimeout(PttSession* session)
{
   StackLog(<< "PttSenderEndingState::onDisconnectedTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt()  << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " destroying session");
   session->changeState(PttSessionState_Idle);
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
