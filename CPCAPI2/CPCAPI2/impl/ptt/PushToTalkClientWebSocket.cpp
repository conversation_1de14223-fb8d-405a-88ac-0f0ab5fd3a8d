#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkClientWebSocket.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>

// rapidjson
#include <stringbuffer.h>
#include <writer.h>


using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT


namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkClientWebSocket::PushToTalkClientWebSocket(const std::function<void(const std::string&, const uint32_t&)>& cb, uint32_t connectionId) : mConnectionId(connectionId), mClientIncomingMessageCb(cb)
{
   StackLog(<< "PushToTalkClientWebSocket()");
}

PushToTalkClientWebSocket::~PushToTalkClientWebSocket()
{
   StackLog(<< "~PushToTalkClientWebSocket()");
}

void PushToTalkClientWebSocket::SetClientConnectionCallback(const std::function<void(PushToTalkClientWebSocket*, const uint32_t&)>& cb)
{
   StackLog(<< "PushToTalkClientWebSocket::SetClientConnectionCallback()");
   mClientConnectionCb = cb;
}

void PushToTalkClientWebSocket::SetClientConnectionErrorCallback(const std::function<void(PushToTalkClientWebSocket*, const uint32_t&)>& cb)
{
   StackLog(<< "PushToTalkClientWebSocket::SetClientConnectionErrorCallback()");
   mClientConnectionErrorCb = cb;
}

void PushToTalkClientWebSocket::on_message(websocketpp::connection_hdl hdl, message_ptr msg)
{
   StackLog(<< "PushToTalkClientWebSocket::on_message()");
   mClientIncomingMessageCb(msg->get_payload(), mConnectionId);
}

void PushToTalkClientWebSocket::on_open(websocketpp::connection_hdl hdl)
{
   DebugLog(<< "PushToTalkClientWebSocket::on_open(): websocket connection opened");
   // mCondConnected.notify_one();
   mClientConnectionCb(this, mConnectionId);
}

void PushToTalkClientWebSocket::on_close(websocketpp::connection_hdl hdl)
{
   DebugLog(<< "PushToTalkClientWebSocket::on_close(): websocket connection closed");
}

void PushToTalkClientWebSocket::on_fail(websocketpp::connection_hdl hdl)
{
   InfoLog(<< "PushToTalkClientWebSocket::on_fail(): websocket connection failed");
   // mCondFailed.notify_one();
   mClientConnectionErrorCb(this, mConnectionId);
}

int PushToTalkClientWebSocket::send(const std::string& jsonData)
{
   StackLog(<< "PushToTalkClientWebSocket::send()");
   mEndpoint.get_io_service().post(std::bind(&PushToTalkClientWebSocket::sendImpl, this, jsonData));
   return kSuccess;
}

void PushToTalkClientWebSocket::sendImpl(const std::string& jsonData)
{
   StackLog(<< "PushToTalkClientWebSocket::sendImpl()");
   if (mConn.get() != NULL)
   {
      if (mConn->get_state() == websocketpp::session::state::open)
      {
         mEndpoint.send(mConn, jsonData, websocketpp::frame::opcode::text);
      }
   }
}

int PushToTalkClientWebSocket::connect(const std::string& serverUri)
{
   StackLog(<< "PushToTalkClientWebSocket::connect(): serverUri: " << serverUri);

   // clear all error/access channels
   mEndpoint.clear_access_channels(websocketpp::log::alevel::all);
   mEndpoint.clear_error_channels(websocketpp::log::elevel::all);

   // Initialize the endpoint
   mEndpoint.init_asio();

   // Mark this endpoint as perpetual. Perpetual endpoints will not exit
   // even if there are no connections.
   mEndpoint.start_perpetual();

   // Start a background thread and run the endpoint in that thread
   mThread.reset(new thread_type(&client::run, &mEndpoint));

   mEndpoint.get_io_service().post(std::bind(&PushToTalkClientWebSocket::connectImpl, this, serverUri));

   /*
   // We want to try multiple attempts at connection
   bool attemptConnect = true;
   int tryCount = 0;
   do
   {
      mEndpoint.get_io_service().post(std::bind(&PushToTalkClientWebSocket::connectImpl, this, serverUri));
      std::unique_lock<std::mutex> lk(mMutex);
      attemptConnect = (std::cv_status::timeout == mCondConnected.wait_for(lk, std::chrono::milliseconds(16000)));
      if (attemptConnect)
      {
         mCondFailed.wait_for(lk, std::chrono::milliseconds(5000));
         std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      }
      else
      {
         // no_timeout --> we connected successfully
         return kSuccess;
      }
   } while (attemptConnect && tryCount++ < 2);
   */

   return kError;
}

void PushToTalkClientWebSocket::connectImpl(const std::string& serverUri)
{
   StackLog(<< "PushToTalkClientWebSocket::connectImpl(): serverUri: " << serverUri);

   websocketpp::lib::error_code ec;

   // connect to this address
   mConn = mEndpoint.get_connection(serverUri.c_str(), ec);
   if (ec)
   {
      mConn.reset();
      return;
   }
   mConn->set_open_handshake_timeout(30000);
   mConn->set_open_handler(bind(&PushToTalkClientWebSocket::on_open, this, std::placeholders::_1));
   mConn->set_fail_handler(bind(&PushToTalkClientWebSocket::on_fail, this, std::placeholders::_1));
   mConn->set_message_handler(bind(&PushToTalkClientWebSocket::on_message, this, std::placeholders::_1, std::placeholders::_2));
   mConn->set_close_handler(bind(&PushToTalkClientWebSocket::on_close, this, std::placeholders::_1));

   mEndpoint.connect(mConn);
}

int PushToTalkClientWebSocket::reconnect(const std::string& serverUri)
{
   mEndpoint.get_io_service().post(std::bind(&PushToTalkClientWebSocket::connectImpl, this, serverUri));
   return kSuccess;
}

int PushToTalkClientWebSocket::shutdown()
{
   StackLog(<< "PushToTalkClientWebSocket::shutdown()");
   mEndpoint.get_io_service().post(std::bind(&PushToTalkClientWebSocket::shutdownImpl, this));
   mThread->join();
   return kSuccess;
}

void PushToTalkClientWebSocket::shutdownImpl()
{
   StackLog(<< "PushToTalkClientWebSocket::shutdownImpl()");
   if (mConn.get() != NULL)
   {
      // for each connection call close
      mConn->set_close_handler([](websocketpp::connection_hdl) {});
      std::error_code ec;
      mConn->close(0, "", ec);
   }

   // Unflag the endpoint as perpetual. This will instruct it to stop once
   // all connections are finished.
   mEndpoint.stop_perpetual();
}

}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
