#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkServerWebSocketSession.h"
#include "PushToTalkServerWebSocket.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT


namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkServerWebSocketSession::PushToTalkServerWebSocketSession(CPCAPI2::PhoneInterface* masterSdkPhone, CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, PushToTalkServerWebSocket* transport, const websocketpp::connection_hdl& conn) :
mConn(conn),
mMasterPhone(masterSdkPhone),
mPttMgr(pttIf),
mTransport(transport),
mConnection(0),
mClosedLocally(false),
mClosedRemotely(false)
{
   StackLog(<< "PushToTalkServerWebSocketSession()");
}

PushToTalkServerWebSocketSession::~PushToTalkServerWebSocketSession()
{
   StackLog(<< "~PushToTalkServerWebSocketSession()");
   mTransport = NULL;
}

void PushToTalkServerWebSocketSession::SendData(const char* payload, unsigned int payloadLen)
{
   StackLog(<< "PushToTalkServerWebSocketSession::SendData()");
   if (mTransport)
   {
      if (mTransport->Send(mConn, payload, payloadLen) == -1)
      {
      }
   }
}

void PushToTalkServerWebSocketSession::onMessage(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
   // Already gets post(ed) in PushToTalkManagerImpl
   // mPttMgr->post(resip::resip_bind(&PushToTalkServerWebSocketSession::onMessageImpl, this, s, hdl, msg));
   StackLog(<< "PushToTalkServerWebSocketSession::onMessage()");
   mTransport->mServerIncomingMessageCb(this, msg->get_payload());
}

void PushToTalkServerWebSocketSession::onMessageImpl(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
/*
   mThreadCheck.test();
   StackLog(<< "PushToTalkServerWebSocketSession::onMessageImpl()");
   mTransport->mIncomingMessageCb(this, msg->get_payload());
*/
}

void PushToTalkServerWebSocketSession::onClose(server* s, websocketpp::connection_hdl hdl)
{
   mPttMgr->post(resip::resip_bind(&PushToTalkServerWebSocketSession::onCloseImpl, this, s, hdl));
}

void PushToTalkServerWebSocketSession::onCloseImpl(server* s, websocketpp::connection_hdl hdl)
{
   mThreadCheck.test();
   if (mTransport)
   {
      mTransport->RemoveSession(hdl);
      mTransport->mServerCloseConnectionCb(this, mConnection);
   }
   delete this;
   /*
   StackLog(<< "PushToTalkServerWebSocketSession::onCloseImpl(): closed locally: " << mClosedLocally  << " closed remotely: " << mClosedRemotely << " connection: " << mConnection);
   if (mClosedLocally)
   {
      mTransport->RemoveSession(hdl);
      delete this;
   }
   else
   {
      mClosedRemotely = true;
      mTransport->mCloseConnectionCb(this, mConnection);
   }
   */
}

void PushToTalkServerWebSocketSession::close()
{
   mThreadCheck.test();
   if (mTransport)
      mTransport->CloseConnection(mConn);
   /*
   StackLog(<< "PushToTalkServerWebSocketSession::close(): closed locally: " << mClosedLocally  << " closed remotely: " << mClosedRemotely << " connection: " << mConnection);
   if (mClosedRemotely)
   {
      mTransport->RemoveSession(mConn);
      delete this;
   }
   else
   {
      mClosedLocally = true;
      mTransport->CloseConnection(mConn);
   }
   */
}

}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
