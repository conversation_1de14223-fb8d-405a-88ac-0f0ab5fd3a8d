#include "brand_branded.h"

#include "interface/experimental/ptt/PushToTalkManager.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)
#include "PushToTalkManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkManager* PushToTalkManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_PTT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<PushToTalkManagerInterface>(phone, "PushToTalkManagerInterface");
#else
   return NULL;
#endif
}

}

}
