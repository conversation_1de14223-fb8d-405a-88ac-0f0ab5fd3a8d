#pragma once

#ifndef CPCAPI2_PUSHTOTALK_UNICAST_SENDER_H
#define CPCAPI2_PUSHTOTALK_UNICAST_SENDER_H

#include "cpcapi2defs.h"
#include "PushToTalkUnicast_NetworkImpairmentExperiment.h"

#include <rutil/DeadlineTimer.hxx>
#include <rutil/MultiReactor.hxx>

// boost asio
#include <boost/asio.hpp>
#include <chrono>
#include <queue>
#include <list>
#include <thread>

namespace CPCAPI2
{
namespace PushToTalk
{
class PushToTalkUnicastSenderHandler
{
public:
   virtual void onSenderStarted() = 0;
   virtual void onSenderStopped() = 0;
   virtual void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets) = 0;
   virtual void onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length) = 0;
};

class PushToTalkSenderReceiveSocketHolder;

class PushToTalkUnicastSender : public std::enable_shared_from_this<PushToTalkUnicastSender>
{
public:
   struct TransmissionParameters
   {
      TransmissionParameters();
      TransmissionParameters(int targetLimit, int retryLimit, std::string& networkMask, std::string& bindAddress_, bool keepAliveEnabled_, int dscp_);
      int maximumUnicastTargetLimit;
      int maximumUnicastMessageRetryLimit;
      std::string networkMaskOverride;
      std::string bindAddress;
      bool keepAliveEnabled;
      int dscp;
   };

   struct DataMessage
   {
      DataMessage(unsigned int messageId_, bool highPriority_, bool noBurst_, const std::string& data, std::vector<boost::asio::ip::udp::endpoint>& endpoints, bool enableHandler_ = false);
      std::string message;
      int retryCount;
      int sendCompleteCount;
      bool enableHandler;
      std::vector<boost::asio::ip::udp::endpoint> targets;
      int targetIndex;
      unsigned int messageId = 0;
      bool highPriority = false;
      bool noBurst = false;
      std::chrono::system_clock::time_point notBefore;
      int uniqueId = -1;

      private: DataMessage();
   };

   PushToTalkUnicastSender(const std::string& bindAddress, PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment_exp, const std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> io_work, const TransmissionParameters& params, const std::shared_ptr<PushToTalkUnicastSenderHandler>& handler, const std::shared_ptr<PushToTalkSenderReceiveSocketHolder>& holder);
   virtual~ PushToTalkUnicastSender();

   void stop();
   void start();
   void send(unsigned int messageId, bool highPriority, bool noBurst, const std::string& payload, std::vector<boost::asio::ip::udp::endpoint>& targets, bool enableHandler = false);
   void unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target);
   void unsendAll(unsigned int messageId);
   void setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable);
   bool isRunning() { return mEnabled; }

   // used from ReceiveSocket
   void dataMessageSent(const DataMessage& dm);
   void socketDoneSending();

   enum ReceiveSocketState
   {
      ReceiveSocketState_Uninitialized,
      ReceiveSocketState_Waiting,
      ReceiveSocketState_Sending,
      ReceiveSocketState_Done,
      ReceiveSocketState_WillBeDeleted
   };
   class ReceiveSocket : public std::enable_shared_from_this<ReceiveSocket>
   {
   public:
      ReceiveSocket(std::shared_ptr<PushToTalkUnicastSender> unicastSender, const std::string& bindAddress, const std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> io_work, const boost::asio::ip::udp::endpoint& remote_endpoint, PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment, const std::shared_ptr<PushToTalkUnicastSenderHandler>& handler, const std::shared_ptr<PushToTalkSenderReceiveSocketHolder>& holder);
      virtual ~ReceiveSocket();

      void startWaiting();
      void startSending();

      void doReceiveImpl();
      void queueMessageImpl(DataMessage& message);
      void onSendTimeoutImpl(const boost::system::error_code& ec);
      void sendImpl(DataMessage& message);
      void unsendImpl(unsigned int messageId);
      void transportSendImpl(const boost::asio::ip::udp::endpoint& endpoint, const std::string& strbuffer);
      void stopImpl();

      std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> mWork;
      std::array<char, 1480> mData;
      boost::asio::ip::udp::endpoint mRemoteEndpoint;
      boost::asio::deadline_timer mSendTimer;
      std::deque<DataMessage> mQ;
      boost::asio::ip::udp::socket mSocket;
      std::weak_ptr<PushToTalkUnicastSenderHandler> mHandler;
      std::weak_ptr<PushToTalkSenderReceiveSocketHolder> mHolder;
      PushToTalkUnicast_NetworkImpairmentExperiment* mNetworkImpairmentExp;
      std::chrono::time_point<std::chrono::system_clock> mLastSendTime;
      std::chrono::time_point<std::chrono::system_clock> mStartTime;
      std::weak_ptr<PushToTalkUnicastSender> mUnicastSender;
      std::string mBindAddress;
      ReceiveSocketState mState;
      bool mIsKeepAliveTarget;
      unsigned int mContinguousOpenFailures;

   private:
      ReceiveSocket(const ReceiveSocket&);
      ReceiveSocket& operator=(const ReceiveSocket&);
   };

private:

   // Block default constructor
   PushToTalkUnicastSender();

   void stopImpl();
   void startImpl();
   void queueMessageImpl(DataMessage& message);
   void unsendAllImpl(unsigned int messageId);
   void unsendImpl(unsigned int messageId, const boost::asio::ip::udp::endpoint& target);
   void setKeepAliveTargetImpl(const boost::asio::ip::udp::endpoint& target, bool enable);
   void onPingTimeoutImpl(const boost::system::error_code& ec);
   size_t countSocketsInUse() const;
   resip::Data guessLocalIpAddress() const;

   bool mSocketSizeSet;
   TransmissionParameters mConfig;
   boost::asio::ip::udp::socket mSocket;
   std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> mWork;
   std::weak_ptr<PushToTalkUnicastSenderHandler> mHandler;
   std::weak_ptr<PushToTalkSenderReceiveSocketHolder> mHolder;
   typedef std::map<boost::asio::ip::udp::endpoint, std::weak_ptr<PushToTalkUnicastSender::ReceiveSocket>> SocketMap;
   SocketMap mSocketMap;

   std::atomic<bool> mEnabled;
   std::deque<DataMessage> mQ;
   boost::asio::deadline_timer mSendTimer;
   std::array<char, 1480> mData;
   PushToTalkUnicast_NetworkImpairmentExperiment* mNetworkImpairmentExp;

   std::map<int, int> mDataMessageCompletionCounter;
   std::string mBindAddress;
   boost::asio::deadline_timer mPingTimer;
   std::promise<bool> mStopPromise;

};

class PushToTalkSenderReceiveSocketHolder
{
public:
   PushToTalkSenderReceiveSocketHolder();
   virtual~ PushToTalkSenderReceiveSocketHolder();
   void add(boost::asio::ip::udp::endpoint recvEndpoint, std::shared_ptr<PushToTalkUnicastSender::ReceiveSocket> recvSocket);
   void remove(boost::asio::ip::udp::endpoint recvEndpoint);
   void clear();
private:
   typedef std::map<boost::asio::ip::udp::endpoint, std::shared_ptr<PushToTalkUnicastSender::ReceiveSocket>> SocketMap;
   SocketMap mSocketMap;
};

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushToTalk::PushToTalkUnicastSender::DataMessage& message);

}

}

#endif // CPCAPI2_PUSHTOTALK_UNICAST_SENDER_H
