#ifndef CPCAPI2_PUSHTOTALK_CONFERENCE_CONNECTOR_SESSION_H
#define CPCAPI2_PUSHTOTALK_CONFERENCE_CONNECTOR_SESSION_H

#include "cpcapi2defs.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"
#include "ptt/PushToTalkHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkManagerInterface.h"
#include <rutil/DeadlineTimer.hxx>
#include <resip/stack/Tuple.hxx>

#include "../util/STAAssertion.h"

#include "confconnector/ConferenceConnectorHandler.h"
#include "confconnector/ConferenceConnectorInterface.h"

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>

#include <vector>
#include <string>
#include <array>


namespace CPCAPI2
{

namespace PushToTalk
{

class PttConferenceConnectorSession;

enum PttConferenceConnectorStateType
{
   PttConferenceConnectorStateType_Idle,
   PttConferenceConnectorStateType_Setup,
   PttConferenceConnectorStateType_Connecting,
   PttConferenceConnectorStateType_Authenticating,
   PttConferenceConnectorStateType_Connected,
   PttConferenceConnectorStateType_NetworkChange,
   PttConferenceConnectorStateType_NetworkChangeFailure,
   PttConferenceConnectorStateType_Disconnecting,
   PttConferenceConnectorStateType_Disconnected,
   PttConferenceConnectorStateType_ConnFailure,
   PttConferenceConnectorStateType_AuthFailure
};

class PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session, PttConferenceConnectorStateType state);
   virtual~ PttConferenceConnectorSessionState();
   virtual void onEntry() = 0;
   virtual void onExit() = 0;

   PttConferenceConnectorStateType getType();
   std::string getName();
   static std::string getName(PttConferenceConnectorStateType type);

   // Associated to PushToTalkManagerImpl
   virtual void setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated);
   virtual void startService(PushToTalkServiceHandle service);
   virtual void shutdownService(PushToTalkServiceHandle service);
   virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

   // Associated to ConferenceConnectorImpl
   virtual void onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args);
   virtual void onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args);

protected:

   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mInterface;
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* mConfConnMgr;
   CPCAPI2::PhoneInterface* mPhone;
   PttConferenceConnectorSession* mSession;
   PttConferenceConnectorStateType mState;
   PushToTalkServiceHandle mService;

};

class PttConferenceConnectorSessionIdleState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionIdleState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionIdleState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   virtual void startService(PushToTalkServiceHandle service) OVERRIDE;
   virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args) OVERRIDE;

private:

   PttConferenceConnectorSessionIdleState();

};

class PttConferenceConnectorSessionSetupState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionSetupState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionSetupState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;
   // virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args) OVERRIDE;

private:

   PttConferenceConnectorSessionSetupState();

};

class PttConferenceConnectorSessionConnectingState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionConnectingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionConnectingState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

private:

   PttConferenceConnectorSessionConnectingState();

};

class PttConferenceConnectorSessionAuthenticatingState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionAuthenticatingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionAuthenticatingState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

private:

   PttConferenceConnectorSessionAuthenticatingState();

};

class PttConferenceConnectorSessionConnectedState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionConnectedState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionConnectedState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;
   
   virtual void setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated) OVERRIDE;
   virtual void onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args) OVERRIDE;

private:

   PttConferenceConnectorSessionConnectedState();

};

class PttConferenceConnectorSessionNetworkChangeState : public PttConferenceConnectorSessionState,
                                                        public resip::DeadlineTimerHandler
{

public:

   PttConferenceConnectorSessionNetworkChangeState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionNetworkChangeState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   virtual void onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   // virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args) OVERRIDE;

private:

   PttConferenceConnectorSessionNetworkChangeState();

   void handleNetworkChange();
   void resetNetworkChangeConnectionTimer();
   void handleNetworkChangeConnectionTimeout();
   resip::DeadlineTimer<resip::MultiReactor> mNetworkChangeConnectionTimer; // Confirm if connection is restored after a network change, othersise restart the json client

};

class PttConferenceConnectorSessionNetworkChangeFailureState : public PttConferenceConnectorSessionState,
                                                               public resip::DeadlineTimerHandler
{

public:

   PttConferenceConnectorSessionNetworkChangeFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionNetworkChangeFailureState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   // virtual void onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   // virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args) OVERRIDE;

private:

   PttConferenceConnectorSessionNetworkChangeFailureState();

   void resetNetworkFailureConnectionTimer();
   void handleNetworkFailureConnectionTimeout();
   resip::DeadlineTimer<resip::MultiReactor> mNetworkFailureConnectionTimer; // Confirm if connection is restored after a network connection failure, othersise restart the json client

};

class PttConferenceConnectorSessionDisconnectingState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionDisconnectingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionDisconnectingState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

private:

   PttConferenceConnectorSessionDisconnectingState();

};

class PttConferenceConnectorSessionDisconnectedState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionDisconnectedState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionDisconnectedState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

private:

   PttConferenceConnectorSessionDisconnectedState();

};

class PttConferenceConnectorSessionConnFailureState : public PttConferenceConnectorSessionState,
                                                      public resip::DeadlineTimerHandler
{

public:

   PttConferenceConnectorSessionConnFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionConnFailureState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;
   
   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   PttConferenceConnectorSessionConnFailureState();
   
   void resetNetworkFailureConnectionTimer();
   void handleNetworkFailureConnectionTimeout();
   resip::DeadlineTimer<resip::MultiReactor> mNetworkFailureConnectionTimer; // Confirm if connection is restored after a network connection failure, othersise restart the json client

};

class PttConferenceConnectorSessionAuthFailureState : public PttConferenceConnectorSessionState
{

public:

   PttConferenceConnectorSessionAuthFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionAuthFailureState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

private:

   PttConferenceConnectorSessionAuthFailureState();

};

class PttConferenceConnectorSessionStateFactory
{

public:

   PttConferenceConnectorSessionStateFactory(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session);
   virtual~ PttConferenceConnectorSessionStateFactory();

   virtual void create();
   virtual PttConferenceConnectorSessionState* getState(PttConferenceConnectorStateType type);

private:

   PttConferenceConnectorSessionStateFactory();

   PttConferenceConnectorSessionState* create(PttConferenceConnectorStateType type);

   PushToTalkManagerImpl* mManager;
   PttConferenceConnectorSession* mSession;

   typedef std::map<PttConferenceConnectorStateType, PttConferenceConnectorSessionState*> PttConferenceConnectorSessionStates;
   PttConferenceConnectorSessionStates mStates;

};

class PttConferenceConnectorSession
{

public:

   PttConferenceConnectorSession(PushToTalkManagerImpl* manager);
   virtual~ PttConferenceConnectorSession();

   void reset();

   PttConferenceConnectorSessionStateFactory* getFactory();

   std::string getLocalIdentity();
   CPCAPI2::ConferenceConnector::CloudConferenceHandle getCloudConference(std::string& identity);
   bool isSubscribedChannel(CPCAPI2::ConferenceConnector::CloudConferenceHandle conn, std::string& channelDisplayName);

   void changeState(PttConferenceConnectorStateType newStateType);

   virtual void setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated);
   virtual void startService(PushToTalkServiceHandle service);
   virtual void shutdownService(PushToTalkServiceHandle service);
   virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);
   virtual void dropIncomingJsonMessages(PushToTalkServiceHandle service, bool enable);
   virtual void disconnectWanConnection(PushToTalkServiceHandle service);

   // ConferenceConnectorHandler
   virtual int onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args);
   virtual int onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args);
   virtual int onConferenceCreated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceCreatedEvent& args);
   virtual int onConferenceEnded(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceEndedEvent& args);
   virtual int onConferenceParticipantListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args) { return kSuccess; }
   virtual int onConferenceSessionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& args) { return kSuccess; }
   virtual int onConferenceSessionMediaStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args) { return kSuccess; }

   void handleConferenceListUpdated(const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args);
   // void handleNetworkChange(const CPCAPI2::NetworkChangeEvent& args);
   void resetConferenceConnector();
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle startConfSessionWith(const cpc::string& ident, PushToTalkSessionHandle pttSession, PttIdentityType identityType, CPCAPI2::ConferenceConnector::CloudConferenceHandle& conf);
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle startConfSessionWithMyConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conf);
   void endConfSession(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session);
   void doConferenceSessionMute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session);
   void doConferenceSessionUnmute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session);

   PttConferenceConnectorStateType mState;
   PushToTalkServiceHandle mService;
   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mInterface;
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* mConfConnMgr;
   
   CPCAPI2::ConferenceConnector::ConferenceConnectorHandle mConfConnHdl;
   CPCAPI2::ConferenceConnector::ConferenceConnectorHandle mDisconnectedConfConnHdl;
   std::set<CPCAPI2::ConferenceConnector::CloudConferenceHandle> mMyConferences;
   cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceInfo> mConfList;
   bool mInSync;
   CPCAPI2::NetworkTransport mNetworkTransport;

   void printCurrentConferences();
   void getMyMissingConferences(cpc::vector<PttIdentity>& myMissingConferences);
   void createNewConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, PttIdentity& identity);
   bool getRootConference(CPCAPI2::ConferenceConnector::CloudConferenceInfo& rootConfInfo);
   void createMissingChannels(const cpc::vector<cpc::string>& channels, bool& conferencesCreated);
   
protected:

   PttConferenceConnectorSession();

   PttConferenceConnectorSessionStateFactory mFactory;

};

}

}

#endif /* CPCAPI2_PUSHTOTALK_CONFERENCE_CONNECTOR_SESSION_H */
