#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "cpcapi2utils.h"
#include "PushToTalkManagerInterface.h"
#include "PushToTalkTypesInternal.h"
#include "../phone/PhoneInterface.h"
#include "PushToTalkManagerImpl.h"
#include "log/LocalLogger.h"
#include "../util/cpc_logger.h"
#include "../util/CharEncodingHelper.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "phone/Phone.h"
#include <ReFlowTransport.hxx>

#include <time.h>
#include <sstream>


#include "json/JsonHelper.h"

#ifdef ANDROID
#include <android/log.h>
#endif

using namespace CPCAPI2::Media;
using namespace CPCAPI2::SipAccount;


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

using namespace resip;

namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkServiceHandle PushToTalkServiceHandleFactory::sNextPushToTalkServiceHandle = 1;
PushToTalkSessionHandle PushToTalkSessionHandleFactory::sNextPushToTalkSessionHandle = 1;

PushToTalkServiceHandle PushToTalkServiceHandleFactory::getNext()
{
   return (::time(NULL) + (sNextPushToTalkServiceHandle++));
}

PushToTalkSessionHandle PushToTalkSessionHandleFactory::getNext()
{
   return (sNextPushToTalkSessionHandle++);
}

PushToTalkManagerInterface::PushToTalkManagerInterface(Phone* phone) :
EventSource<PushToTalkServiceHandle, PushToTalkHandler, PushToTalkSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mPttServiceHandle(0),
mShutdownComplete(false),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mLocalLogger(mPhone->localLogger())
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone))->addSdkObserver(this);
   mPttServiceImpl.reset();
}

PushToTalkManagerInterface::~PushToTalkManagerInterface()
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(mPhone))->removeSdkObserver(this);
   mPttServiceImpl.reset();
   mPttServiceHandle = 0;
}

void PushToTalkManagerInterface::firePhoneError(const cpc::string& errorText)
{
   ErrLog(<< "Firing phone error: " << errorText);

   PhoneErrorEvent evt;
   evt.errorText = errorText;
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), cpc::string("PushToTalkManager"), evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), cpc::string("PushToTalkManager"), evt);
}

// PhoneModule

void PushToTalkManagerInterface::PreRelease()
{
   InfoLog(<< "PushToTalkManagerInterface::PreRelease()");
   if (mPttServiceImpl && (mPttServiceHandle != 0))
   {
      mPttServiceImpl->shutdownService(mPttServiceHandle, false);
   }
}

void PushToTalkManagerInterface::Release()
{
   InfoLog(<< "PushToTalkManagerInterface::PreRelease(): " << this);
   mPttServiceImpl.reset();
   mPttServiceHandle = 0;
}

PhoneInterface* PushToTalkManagerInterface::phoneInterface()
{
   return mPhone;
}

// PushToTalkManager

PushToTalkServiceHandle PushToTalkManagerInterface::createPttService()
{
   auto hF = mServiceHandleFuture.get_future();
   post(resip::resip_bind(&PushToTalkManagerInterface::createPttServiceImpl, this));
   PushToTalkServiceHandle h = hF.get();
   mServiceHandleFuture = std::promise<PushToTalkServiceHandle>(); // reset for next use
   return h;
}

int PushToTalkManagerInterface::createPttServiceImpl()
{
   if (mPttServiceHandle > 0)
   {
      firePhoneError("PushToTalkManager::createPttService: service: " + cpc::to_string(mPttServiceHandle) + " already created");
      mServiceHandleFuture.set_value(0);
      return kError;
   }

   mShutdownComplete = false;
   mPttServiceHandle = PushToTalkServiceHandleFactory::getNext();
   DebugLog(<< "PushToTalkManagerInterface::createPttServiceImpl(): service: " << mPttServiceHandle);
   mServiceHandleFuture.set_value(mPttServiceHandle);
   return kSuccess;
}

int PushToTalkManagerInterface::setHandler(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler)
{
   ReadCallbackBase* f = resip::resip_bind(&PushToTalkManagerInterface::setHandlerImpl, this, service, handler);

   if (handler == NULL)
   {
      // App could be out of sync in shutting one service down, versus starting another one, e.g. with logout-login,
      // where one service is starting up and the previous one is shutting down. If this set handler null is from the shutdown
      // process, it might stall the handlers, if the new starting service has already set the handler
      if ((mPttServiceHandle != service) && (mPttServiceHandle != 0))
      {
         ErrLog(<< "PushToTalkManagerInterface::setHandler(): service: " << mPttServiceHandle << " ignoring NULL set-handler as the service handle provided: " << service << " does not match the current handle: " << mPttServiceHandle);
         // New service has not yet set the handler, need to ensure the previous handler is NULL so that no dangling pointer is retained
         auto hF = mSetHandlerFuture.get_future();
         post(f);
         if (hF.wait_for(std::chrono::milliseconds(5000)) == std::future_status::ready)
         {
            bool handlerSet = hF.get();
            DebugLog(<< "PushToTalkManagerInterface::setHandler(): current service: " << mPttServiceHandle << " with set-handler service: " << service << " got processed: " << handlerSet);
         }
         else
         {
            InfoLog(<< "PushToTalkManagerInterface::setHandler(): service: " << mPttServiceHandle << " timeout on set-handler request");
         }

         mSetHandlerFuture = std::promise<bool>(); // reset for next use
      }
      else
      {
         mPhone->getSdkModuleThread().execute(f);
         process(-1);
         DebugLog(<< "PushToTalkManagerInterface::setHandler(): service: " << mPttServiceHandle << " app shutdown complete");
      }
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

int PushToTalkManagerInterface::process(unsigned int timeout)
{
   //DebugLog(<< "PushToTalkManagerInterface::process");
   return CPCAPI2::EventSource<PushToTalkServiceHandle, PushToTalkHandler, PushToTalkSyncHandler>::process(timeout);
}

int PushToTalkManagerInterface::setHandlerImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler)
{
   DebugLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << service << " handler: " << handler);

   if (mShutdownComplete && (mPttServiceImpl || (mPttServiceHandle != 0)))
   {
      InfoLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << service << " handler: " << handler << " manager in invalid state");
      assert(0);
      return kError;
   }

   int rc = kSuccess;
   if (mPttServiceHandle == 0)
   {
      // Ignore triggering phone error if the handler is NULL, as the app is likely going through shutdown
      if (handler)
      {
         firePhoneError("PushToTalkManager::setHandlerImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized");
      }
      rc = kError;
   }
   else if (mPttServiceImpl && (handler != NULL))
   {
      firePhoneError("PushToTalkManager::setHandlerImpl: service: " + cpc::to_string(mPttServiceHandle) + " service manager already created");
      rc = kError;
   }
   else if (service != mPttServiceHandle)
   {
      // Ignore triggering phone error if the handler is NULL, as the app is likely going through shutdow
      if (handler)
      {
         firePhoneError("PushToTalkManager::setHandlerImpl: service: " + cpc::to_string(mPttServiceHandle) + " handle mismatch with: " + cpc::to_string(service) + " to set new handler");
         rc = kError;
      }
      else
      {
         // Handler is NULL but have a service mismatch, possibility during re-logins or restarts, where app possibliy has different threads for the services
         if (mPttServiceImpl)
         {
            if (mPttServiceImpl->getService() == service)
            {
               // Looks like new handle was created but the old impl has not yet been destroyed
               mPttServiceImpl->setHandler(service, NULL);
               DebugLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << mPttServiceHandle << " handle mismatch with: " << cpc::to_string(service) << " with stale impl");
               mPttServiceImpl.reset();
               setAppHandler(service, NULL);
            }
            else
            {
               // New service seems to have already been established, as both the service handle and service impl are updated,
               // this could be a redundant request to cleanup an already destroyed impl
               firePhoneError("PushToTalkManager::setHandlerImpl: service: " + cpc::to_string(mPttServiceHandle) + " handle mismatch with: " + cpc::to_string(service) + " to set null handler with initialized impl");
               rc = kError;
            }
         }
         else
         {
            DebugLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << mPttServiceHandle << " handle mismatch with: " << cpc::to_string(service) << " with no impl");
            setAppHandler(service, NULL);
         }

         try
         {
            mSetHandlerFuture.set_value(true);
         }
         catch (const std::future_error& e)
         {
            DebugLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << mPttServiceHandle << " handle mismatch with: " << cpc::to_string(service) << " exception setting set-handler future");
         }
      }
   }
   else if (handler == NULL)
   {
      if (!mShutdownComplete)
      {
         if (mPttServiceImpl)
         {
            mPttServiceImpl->setHandler(service, NULL);
            StackLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << service << " impl reference count: " << mPttServiceImpl.use_count());
            mPttServiceImpl.reset();
         }

         setAppHandler(service, NULL);
      }
   }
   else
   {
      DebugLog(<< "PushToTalkManagerInterface::setHandlerImpl(): service: " << service << " triggering ptt impl initialization for service: " << service);
      mPttServiceImpl = std::make_shared<PushToTalkManagerImpl>(mPhone, this, mPhone->localLogger());
      mPttServiceImpl->setHandler(service, handler);
      setAppHandler(service, handler);
   }

   return rc;
}

int PushToTalkManagerInterface::configureService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings)
{
	post(resip::resip_bind(&PushToTalkManagerInterface::configureServiceImpl, this, service, settings));
	return kSuccess;
}

int PushToTalkManagerInterface::configureServiceImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings)
{
	DebugLog(<< "PushToTalkManagerInterface::configureServiceImpl(): service: " << service);
	if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
	{
		firePhoneError("PushToTalkManager::configureServiceImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
		return kError;
	}

	mPttServiceImpl->configureService(service, settings);
	return kSuccess;
}

int PushToTalkManagerInterface::queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::queryEndpointListImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::queryEndpointListImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::queryEndpointListImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::queryEndpointListImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
      return kError;
   }

   mPttServiceImpl->queryEndpointList(service);
   return kSuccess;
}

int PushToTalkManagerInterface::configureEndpointListAutoUpdate(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::configureEndpointListAutoUpdateImpl, this, service, autoUpdate));
   return kSuccess;
}

int PushToTalkManagerInterface::configureEndpointListAutoUpdateImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate)
{
   DebugLog(<< "PushToTalkManagerInterface::configureEndpointListAutoUpdateImpl(): service: " << service << " autoUpdate: " << autoUpdate);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::configureEndpointListAutoUpdateImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
      return kError;
   }

   mPttServiceImpl->configureEndpointListAutoUpdate(service, autoUpdate);
   return kSuccess;
}

int PushToTalkManagerInterface::setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::setChannelSubscriptionsImpl, this, service, channels));
   return kSuccess;
}

int PushToTalkManagerInterface::setChannelSubscriptionsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels)
{
   DebugLog(<< "PushToTalkManagerInterface::setChannelSubscriptionsImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::setChannelSubscriptionsImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
      return kError;
   }
   mPttServiceImpl->setChannelSubscriptions(service, channels);
   return kSuccess;
}

int PushToTalkManagerInterface::startService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::startServiceImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::startServiceImpl(PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::startServiceImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::startServiceImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
      return kError;
   }

   mPttServiceImpl->startService(service);
   return kSuccess;
}

int PushToTalkManagerInterface::shutdownService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::shutdownServiceImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::shutdownServiceImpl(PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::shutdownServiceImpl(): service: " << service);
   int rc = kSuccess;
   if ((mPttServiceHandle == 0) || (service != mPttServiceHandle))
   {
      DebugLog(<< "PushToTalkManagerInterface::shutdownServiceImpl(): service: " << service << " service not initialized correctly");
      firePhoneError("PushToTalkManager::shutdownServiceImpl: service: " + cpc::to_string(mPttServiceHandle) + " service not initialized correctly");
      rc = kError;
   }
   else
   {
      if (mPttServiceImpl)
      {
         mPttServiceImpl->shutdownService(service, true);
      }
      setHandlerImpl(service, NULL);
      mPttServiceHandle = 0;
      mShutdownComplete = true;
   }
   return rc;
}

PushToTalkSessionHandle PushToTalkManagerInterface::createPttSession(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   auto hF = mSessionHandleFuture.get_future();
   post(resip::resip_bind(&PushToTalkManagerInterface::createPttSessionImpl, this, service));
   PushToTalkSessionHandle h = hF.get();
   mSessionHandleFuture = std::promise<PushToTalkSessionHandle>(); // reset for next use
   return h;
}

int PushToTalkManagerInterface::createPttSessionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::createPttSessionImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::createPttSessionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      mSessionHandleFuture.set_value(0);
      return kError;
   }

   PushToTalkSessionHandle ptt = PushToTalkSessionHandleFactory::getNext();
   if (mPttServiceImpl->createPttSession(ptt) == kError)
   {
      ptt = 0;
      DebugLog(<< "PushToTalkManagerInterface::createPttSessionImpl(): service: " << mPttServiceHandle << " failure creating ptt session");
      firePhoneError("PushToTalkManager::createPttSessionImpl: failure creating ptt session");
   }
   else
   {
      DebugLog(<< "PushToTalkManagerInterface::createPttSessionImpl(): service: " << mPttServiceHandle << " ptt: " << ptt << " session created");
   }
   mSessionHandleFuture.set_value(ptt);
   // TODO: handle scenario where a ptt session already exists - incoming or outgoing
   return ((ptt == 0) ? kError : kSuccess);
}

PushToTalkSessionHandle PushToTalkManagerInterface::createPttSessionInternal(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::createPttSessionInternal(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::createPttSessionInternal: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   PushToTalkSessionHandle ptt = PushToTalkSessionHandleFactory::getNext();

   // TODO: handle scenario where a ptt session already exists - incoming or outgoing
   return ptt;
}

int PushToTalkManagerInterface::addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::addRecipientImpl, this, ptt, identity));
   return kSuccess;
}

int PushToTalkManagerInterface::addRecipientImpl(PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   DebugLog(<< "PushToTalkManagerInterface::addRecipientImpl(): ptt: " << ptt << " uri: " << identity.userName);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::addRecipientImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->addRecipient(ptt, identity) == kError)
   {
      firePhoneError("PushToTalkManager::addRecipientImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel)
{
	post(resip::resip_bind(&PushToTalkManagerInterface::setChannelImpl, this, ptt, channel));
	return kSuccess;
}

int PushToTalkManagerInterface::setChannelImpl(PushToTalkSessionHandle ptt, const cpc::string& channel)
{
	DebugLog(<< "PushToTalkManagerInterface::setChannelImpl(): ptt: " << ptt << " channel: " << channel);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::setChannelImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->setChannel(ptt, channel) == kError)
   {
      firePhoneError("PushToTalkManager::setChannelImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

	return kSuccess;
}

int PushToTalkManagerInterface::start(PushToTalkSessionHandle ptt)
{
   #ifdef ANDROID
      __android_log_print(ANDROID_LOG_WARN, "PushToTalkManagerInterface", "start");
   #endif

   post(resip::resip_bind(&PushToTalkManagerInterface::startImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::startImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::startImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::startImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->start(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::startImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::end(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::endImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::endImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::endImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::endImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->end(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::endImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::startTalkSpurt(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::startTalkSpurtImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::startTalkSpurtImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::startTalkSpurtImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::startTalkSpurtImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->startTalkSpurt(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::startTalkSpurtImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::endTalkSpurt(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::endTalkSpurtImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::endTalkSpurtImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::endTalkSpurtImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::endTalkSpurtImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->endTalkSpurt(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::endTalkSpurtImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::accept(PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PushToTalkManagerInterface::accept(): ptt: " << ptt);
   post(resip::resip_bind(&PushToTalkManagerInterface::acceptImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::acceptImpl(PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PushToTalkManagerInterface::acceptImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      InfoLog(<< "PushToTalkManagerInterface::acceptImpl(): ptt: " << ptt << " service: " << mPttServiceHandle << " not initialized correctly");
      firePhoneError("PushToTalkManager::acceptImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->accept(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::acceptImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::reject(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::rejectImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::rejectImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::rejectImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::rejectImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->reject(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::rejectImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::setAudioInputDevice(unsigned int deviceId)
{
   return kSuccess;
}


int PushToTalkManagerInterface::setAudioOutputDevice(unsigned int deviceId)
{
   return kSuccess;
}

int PushToTalkManagerInterface::setAudioDeviceCloseDelay(int audioDeviceCloseDelay)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::setAudioDeviceCloseDelayImpl, this, audioDeviceCloseDelay));
   return kSuccess;
}

int PushToTalkManagerInterface::setAudioDeviceCloseDelayImpl(int audioDeviceCloseDelay)
{
   mPttServiceImpl->setAudioDeviceCloseDelay(audioDeviceCloseDelay);
   return kSuccess;
}

/**
 * Mutes a currently active (Talking state) PTT session, meaning that the incoming RTP stream is disconnected from the audio playout; applicable to incoming PTT only
 */
int PushToTalkManagerInterface::mutePttSession(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::mutePttSessionImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::mutePttSessionImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::mutePttSessionImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::mutePttSessionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->mutePttSession(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::mutePttSessionImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }
   return kSuccess;
}

int PushToTalkManagerInterface::unmutePttSession(PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::unmutePttSessionImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::unmutePttSessionImpl(PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::unmutePttSessionImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::unmutePttSessionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->unmutePttSession(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::unmutePttSessionImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }
   return kSuccess;
}

int PushToTalkManagerInterface::mutePttService(PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::mutePttServiceImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::mutePttServiceImpl(PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::mutePttServiceImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::mutePttServiceImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->mutePttService(service) == kError)
   {
      firePhoneError("PushToTalkManager::mutePttServiceImpl: invalid session handle: " + cpc::to_string(service));
      return kError;
   }
   return kSuccess;
}

int PushToTalkManagerInterface::unmutePttService(PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::unmutePttServiceImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::unmutePttServiceImpl(PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::unmutePttServiceImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::unmutePttServiceImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->unmutePttService(service) == kError)
   {
      firePhoneError("PushToTalkManager::unmutePttServiceImpl: invalid session handle: " + cpc::to_string(service));
      return kError;
   }
   return kSuccess;
}

int PushToTalkManagerInterface::setPttInternalSettings(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::setPttInternalSettingsImpl, this, service, settings));
   return kSuccess;
}

int PushToTalkManagerInterface::setPttInternalSettingsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings)
{
   DebugLog(<< "PushToTalkManagerInterface::setPttInternalSettingsImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::setPttInternalSettingsImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->setPttInternalSettings(service, settings);
   return kSuccess;
}

int PushToTalkManagerInterface::enableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::enableUnicastTransmissionImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::enableUnicastTransmissionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::enableUnicastTransmissionImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::enableUnicastTransmissionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->enableUnicastTransmission(service);
   return kSuccess;
}

int PushToTalkManagerInterface::disableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::disableUnicastTransmissionImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::disableUnicastTransmissionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::disableUnicastTransmissionImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::disableUnicastTransmissionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->disableUnicastTransmission(service);
   return kSuccess;
}

int PushToTalkManagerInterface::enableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::enableUnicastReceiveImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::enableUnicastReceiveImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::enableUnicastReceiveImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::enableUnicastReceiveImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->enableUnicastReceive(service);
   return kSuccess;
}

int PushToTalkManagerInterface::disableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::disableUnicastReceiveImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::disableUnicastReceiveImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::disableUnicastReceiveImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::disableUnicastReceiveImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->disableUnicastReceive(service);
   return kSuccess;
}

int PushToTalkManagerInterface::dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::dropIncomingJsonMessagesImpl, this, service, enable));
   return kSuccess;
}

int PushToTalkManagerInterface::dropIncomingJsonMessagesImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable)
{
   DebugLog(<< "PushToTalkManagerInterface::dropIncomingJsonMessagesImpl(): service: " << service << " enable: " << enable);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::dropIncomingJsonMessagesImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->dropIncomingJsonMessages(service, enable);
   return kSuccess;
}

int PushToTalkManagerInterface::disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::disconnectWanConnectionImpl, this, service));
   return kSuccess;
}

int PushToTalkManagerInterface::disconnectWanConnectionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   DebugLog(<< "PushToTalkManagerInterface::disconnectWanConnectionImpl(): service: " << service);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL) || (service != mPttServiceHandle))
   {
      firePhoneError("PushToTalkManager::disconnectWanConnectionImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->disconnectWanConnection(service);
   return kSuccess;
}

int PushToTalkManagerInterface::queryMediaStatistics(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::queryMediaStatisticsImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::queryMediaStatisticsImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::queryMediaStatisticsImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::queryMediaStatisticsImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->queryMediaStatistics(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::queryMediaStatisticsImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::enableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::enableMediaInactivityMonitorImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::enableMediaInactivityMonitorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::enableMediaInactivityMonitorImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::enableMediaInactivityMonitorImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->enableMediaInactivityMonitor(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::enableMediaInactivityMonitorImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::disableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::disableMediaInactivityMonitorImpl, this, ptt));
   return kSuccess;
}

int PushToTalkManagerInterface::disableMediaInactivityMonitorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   DebugLog(<< "PushToTalkManagerInterface::disableMediaInactivityMonitorImpl(): ptt: " << ptt);
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::disableMediaInactivityMonitorImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   if (mPttServiceImpl->disableMediaInactivityMonitor(ptt) == kError)
   {
      firePhoneError("PushToTalkManager::disableMediaInactivityMonitorImpl: invalid session handle: " + cpc::to_string(ptt));
      return kError;
   }

   return kSuccess;
}

int PushToTalkManagerInterface::addPttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::addPttObserverImpl, this, handler));
   return kSuccess;
}

int PushToTalkManagerInterface::addPttObserverImpl(CPCAPI2::PushToTalk::PushToTalkHandler* handler)
{
   addSdkObserver(handler);
   return kSuccess;
}

int PushToTalkManagerInterface::removePttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::removePttObserverImpl, this, handler));
   return kSuccess;
}

int PushToTalkManagerInterface::removePttObserverImpl(CPCAPI2::PushToTalk::PushToTalkHandler* handler)
{
   removeSdkObserver(handler);
   return kSuccess;
}

int PushToTalkManagerInterface::handleRawPttRequest(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::handleRawPttRequestImpl, this, srcIp, srcPort, pttRequest));
   return kSuccess;
}

int PushToTalkManagerInterface::handleRawPttRequestImpl(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest)
{
   DebugLog(<< "PushToTalkManagerInterface::handleRawPttRequestImpl()");
   if ((mPttServiceHandle == 0) || (mPttServiceImpl.get() == NULL))
   {
      firePhoneError("PushToTalkManager::handleRawPttRequestImpl: service: " + cpc::to_string(mPttServiceHandle) + " not initialized correctly");
      return kError;
   }

   mPttServiceImpl->handleRawPttRequest(srcIp, srcPort, pttRequest);
   return kSuccess;
}

void PushToTalkManagerInterface::post(resip::ReadCallbackBase* cmd)
{
   mPhone->getSdkModuleThread().post(cmd);
}

int PushToTalkManagerInterface::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   post(resip::resip_bind(&PushToTalkManagerInterface::onNetworkChangeImpl, this, args));
   return kSuccess;
}

int PushToTalkManagerInterface::onNetworkChangeImpl(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PushToTalkManagerInterface::onNetworkChangeImpl()");
   if (mPttServiceImpl.get())
   {
      return mPttServiceImpl->onNetworkChange(args);
   }
   return kSuccess;
}

void PushToTalkManagerInternal::dropIncomingMediaPackets(bool enable)
{
   webrtc_recon::ReFlowTransport::sDropIncomingPackets = enable;
}

void PushToTalkManagerInterface::resetStaleSessionList(ConferenceConnector::CloudConferenceHandle conference)
{
   // Clear the list only when we are in sync with the server
   DebugLog(<< "PushToTalkManagerImpl::resetStaleSessionList(): " << this << " service: " << mPttServiceHandle << " clear stale sessions mapped to conference: " << conference);
   mStaleConfSessionList.erase(conference);
}

int PushToTalkManager::decodeProvisioningResponse(const cpc::string& provisioningResponse, cpc::vector<PushToTalkServiceSettings>& outAccountSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("pttAccounts"))
   {
      WarningLog(<< "Invalid provisioning format, pttAccounts node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& accounts = provisionedJSON["pttAccounts"];
   if (!accounts.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, pttAccounts node not an array. Aborting decode.");
      return kError;
   }

   for (rapidjson::Value::ConstValueIterator itr = accounts.Begin(); itr != accounts.End(); ++itr)
   {
      if (!itr->HasMember("pttAccountSettings"))
      {
         WarningLog(<< "Invalid provisioning format, pttAccountSettings node missing.");
         continue;
      }

      const rapidjson::Value& accountSettings = (*itr)["pttAccountSettings"];
      if (!accountSettings.IsObject())
      {
         WarningLog(<< "Invalid provisioning format, pttAccountSettings not an object.");
         continue;
      }

      PushToTalkServiceSettings settings;
      JsonDeserialize(*itr, "pttAccountSettings", settings);
      outAccountSettings.push_back(settings);
   }

   return kSuccess;
}

int PushToTalkManagerInternal::decodeProvisioningResponseInternal(const cpc::string& provisioningResponse, cpc::vector<PushToTalkSettingsInternal>& outSettingsInternal)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("pttAccounts"))
   {
      WarningLog(<< "Invalid provisioning format, pttAccounts node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& accounts = provisionedJSON["pttAccounts"];
   if (!accounts.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, pttAccounts node not an array. Aborting decode.");
      return kError;
   }

   for (rapidjson::Value::ConstValueIterator itr = accounts.Begin(); itr != accounts.End(); ++itr)
   {
      PushToTalkSettingsInternal settings;
      if (itr->HasMember("pttAccountSettingsInternal"))
      {
         const rapidjson::Value& accountSettings = (*itr)["pttAccountSettingsInternal"];
         if (accountSettings.IsObject())
         {
            JsonDeserialize(*itr, "pttAccountSettingsInternal", settings);
         }
         else
         {
            WarningLog(<< "Invalid provisioning format, pttAccountSettingsInternal not an object.");
         }
      }
      outSettingsInternal.push_back(settings);
   }

   return kSuccess;
}

bool PushToTalkManagerInterface::isStaleSession(ConferenceConnector::CloudConferenceHandle conference, const ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity)
{
   std::stringstream sessionId;
   sessionId << speakerIdentity.address << conference << speakerIdentity.participant << speakerIdentity.peerConnection;

   DebugLog(<< "PushToTalkManagerInterface::isStaleSession(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " identity: " << speakerIdentity.address << " stale check for session-id: " << sessionId.str());
   /*
   // TODO: Currently handing clearance based on receipt of an empty participant list
   UInt64 currentTime = resip::ResipClock::getTimeMs();
   if ((currentTime - mLastStaleConfSessionUpdateTime) > PTT_SESSION_STALE_SESSION_INTERVAL_MSECS)
   {
      StackLog(<< "PushToTalkManagerImpl::checkAndUpdateStaleSessionStatus(): " << this << " service: " << mService << " clear the stale session list");
      mStaleConfSessionList.clear();
   }
   */

   bool isStaleSession = false;
   auto i = mStaleConfSessionList.find(conference);
   if (i == mStaleConfSessionList.end())
   {
      StackLog(<< "PushToTalkManagerInterface::isStaleSession(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " not found in stale session list");
   }
   else
   {
      std::set<std::string> confList = i->second;
      auto j = std::find(confList.begin(), confList.end(), sessionId.str());
      if (j == confList.end())
      {
         StackLog(<< "PushToTalkManagerInterface::isStaleSession(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " session: " << sessionId.str() << " not found in stale session list (" << confList.size() << ")");
      }
      else
      {
         InfoLog(<< "PushToTalkManagerInterface::isStaleSession(): " << this << " service: " << mPttServiceHandle
            << " stale ptt conference: " << conference << " with speaker-identity: " << speakerIdentity.address << " session-id: " << sessionId.str() << " stale-sessions: " << confList.size());
         isStaleSession = true;
      }
   }

   return isStaleSession;
}

void PushToTalkManagerInterface::addToStaleSessionList(ConferenceConnector::CloudConferenceHandle conference, const ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity)
{
   std::stringstream sessionId;
   sessionId << speakerIdentity.address << conference << speakerIdentity.participant << speakerIdentity.peerConnection;

   /*
   // TODO: Currently handing clearance based on receipt of an empty participant list
   UInt64 currentTime = resip::ResipClock::getTimeMs();
   if ((currentTime - mLastStaleConfSessionUpdateTime) > PTT_SESSION_STALE_SESSION_INTERVAL_MSECS)
   {
      StackLog(<< "PushToTalkManagerImpl::addToStaleSessionList(): " << this << " service: " << mService << " clear session-id: " << sessionId.str());
      mStaleConfSessionList.clear();
   }
   */

   StackLog(<< "PushToTalkManagerInterface::addToStaleSessionList(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " identity: " << speakerIdentity.address << " adding session-id: " << sessionId.str() << " to stale session list");
   auto i = mStaleConfSessionList.find(conference);
   std::set<std::string> confList;
   if (i == mStaleConfSessionList.end())
   {
      mStaleConfSessionList[conference] = confList;
   }
   else
   {
      confList = i->second;
   }
   confList.insert(sessionId.str());
   mStaleConfSessionList[conference] = confList;
}

void PushToTalkManagerInterface::removeFromStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity)
{
   std::stringstream sessionId;
   sessionId << speakerIdentity.address << conference << speakerIdentity.participant << speakerIdentity.peerConnection;

   StackLog(<< "PushToTalkManagerInterface::removeFromStaleSessionList(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " identity: " << speakerIdentity.address << " removing session-id: " << sessionId.str() << " from stale session list");
   auto i = mStaleConfSessionList.find(conference);
   std::set<std::string> confList;
   if (i != mStaleConfSessionList.end())
   {
      confList = i->second;
      int preCount = confList.size();
      confList.erase(sessionId.str());
      StackLog(<< "PushToTalkManagerInterface::removeFromStaleSessionList(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " identity: " << speakerIdentity.address << " session found in stale session list, updated from: " << preCount << " to: " << confList.size());
   }
   mStaleConfSessionList[conference] = confList;
}

bool PushToTalkManagerInterface::isSessionInStaleSessionList(ConferenceConnector::CloudConferenceHandle conference, const ConferenceConnector::CloudConferenceParticipantInfo& participantInfo)
{
   std::stringstream sessionId;
   sessionId << participantInfo.address << conference << participantInfo.participant << participantInfo.peerConnection;

   auto i = mStaleConfSessionList.find(conference);
   if (i != mStaleConfSessionList.end())
   {
      std::set<std::string> confList = i->second;
      auto j = std::find(confList.begin(), confList.end(), sessionId.str());
      if (j != confList.end())
      {
         DebugLog(<< "PushToTalkManagerInterface::isSessionInStaleSessionList(): " << this << " service: " << mPttServiceHandle << " conference: " << conference << " identity: " << participantInfo.address << " checking session-id: " << sessionId.str() << " found in stale session list");
         return true;
      }
   }

   return false;
}

void PushToTalkManagerInterface::logEvent(PushToTalkServiceHandle handle, const char *funcName, const char *eventName)
{
   DebugLog(<< funcName << " handle: " << handle);
}

}

}

#endif
