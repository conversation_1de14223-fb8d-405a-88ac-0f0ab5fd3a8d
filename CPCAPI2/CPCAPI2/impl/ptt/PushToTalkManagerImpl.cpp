#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkSenderSession.h"
#include "PushToTalkReceiverSession.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "media/AudioInterface.h"
#include "../media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "confconnector/ConferenceConnectorInterface.h"
#include "../util/IpHelpers.h"
#include "../util/ReactorHelpers.h"
#include <rutil/Random.hxx>
#include <MediaStackImpl.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"
#include <resip/stack/Tuple.hxx>

// zlib wrapper
#include "../impl/util/zstr/zstr.hpp"

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

#define PTT_CONF_TAG_SIP           1
#define PTT_CONF_TAG_XMPP          2


using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceConnector;
using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

cpc::string PttIdentity::getIdentityString() const
{
   std::stringstream identityUrl;
   if (userName.size() > 0)
   {
      std::string user = userName.c_str();
      size_t pos = user.find("@");
      if (pos != std::string::npos)
      {
         // Avoid prepending identity prefix for channel calls
         identityUrl << ((identityType == PttIdentityType_SIP) ? "sip:" : "xmpp:");
      }
      identityUrl << userName;
   }
   return identityUrl.str().c_str();
}

cpc::string PushToTalkServiceSettings::getPrimaryLocalIdentity() const
{
   cpc::string identityStr = ((localIdentities.size() > 0) ? localIdentities.begin()->getIdentityString() : "");
   return identityStr;
}

static std::string GetIdentityUrl(const PttIdentity& identity)
{
   return identity.getIdentityString().c_str();
};

PushToTalkSettingsInternal::PushToTalkSettingsInternal() :
unicastTransmitEnabled(true),
unicastReceiveEnabled(true),
mediaInactivityMonitorEnabled(true),
participantUpdateListEnabled(true),
ignoreIncomingInitiate(false),
participantListFetchLimit(PTT_CLOUD_PARTICIPANT_QUERY_FETCH_LIMIT),
connectionRetryMaximum(PTT_SESSION_CLIENT_CONNECTION_RETRY_MAXIMUM),
connectedEndpointThresholdPercentage(PTT_SESSION_STATUS_RESPONSE_THRESHOLD_PERCENTAGE),
outgoingSessionWaitForTalkSpurtMsecs(PTT_SESSION_WAIT_FOR_TALKSPURT_MSECS),
outgoingSessionExpiryMsecs(-1),
outgoingSessionStartupDelayMsecs(PTT_SESSION_OUTGOING_STARTUP_DELAY_MSECS),
outgoingConnectedMsecs(PTT_SESSION_CONNECTED_INTERVAL_MSECS),
outgoingConnectionErrorMsecs(PTT_SESSION_CONNECTION_ERROR_INTERVAL_MSECS),
outgoingDisconnectedMsecs(PTT_SESSION_DISCONNECTED_INTERVAL_MSECS),
incomingSetupMsecs(PTT_SESSION_INCOMING_SETUP_INTERVAL_MSECS),
incomingConnectionRetryMsecs(PTT_SESSION_INCOMING_CONNECTION_RETRY_INTERVAL_MSECS),
incomingConnectedMsecs(PTT_SESSION_INCOMING_CONNECTED_INTERVAL_MSECS),
incomingWaitForAnswerMsecs(PTT_SESSION_INCOMING_WAIT_FOR_ANSWER_INTERVAL_MSECS),
incomingRejectedMsecs(PTT_SESSION_INCOMING_REJECTED_INTERVAL_MSECS),
unicastEndpointListIntervalMsecs(PTT_SESSION_UNICAST_ENDPOINT_LIST_INTERVAL_MSECS),
unicastDiscoverySendIntervalMsecs(PTT_UNICAST_DISCOVERY_SEND_INTERVAL_MSECS),
unicastInitialDiscoveryRetryIntervalMsecs(PTT_UNICAST_INITIAL_DISCOVERY_RETRY_INTERVAL_MSECS),
unicastDiscoveryRetryIntervalMsecs(PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MSECS),
unicastDiscoveryRetryIntervalMaximumMsecs(PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MAXIMUM_MSECS),
networkFailureConnectionIntervalMsecs(PTT_SERVICE_NETWORK_FAILURE_CONNECTION_INTERVAL_MSECS),
networkChangeConnectionIntervalMsecs(PTT_SERVICE_NETWORK_CHANGE_CONNECTION_INTERVAL_MSECS),
networkWifiStatusIntervalMsecs(PTT_SESSION_NETWORK_WIFI_STATUS_INTERVAL_MSECS),
unicastInitRetryIntervalMsecs(PTT_UNICAST_PTT_INIT_RETRY_INTERVAL_MSECS),
maximumUnicastTargetLimit(PTT_UNICAST_MAXIMUM_TARGET_LIMIT),
maximumUnicastMessageRetryLimit(PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT),
networkMaskOverride("")
#ifdef CPCAPI2_AUTO_TEST
, forcedTransactionID(NULL)
#endif
{}

std::ostream& operator<<(std::ostream& os, const PushToTalkSettingsInternal& evt)
{
   std::stringstream ss;
   ss << " unicastTargets: { ";
   for (auto i : evt.unicastTargets)
   {
      ss << i << " ";
   }
   ss << "}";
   os << "ServiceConnectionStatusEvent: "
      << " unicastTransmitEnabled: " << evt.unicastTransmitEnabled
      << " unicastReceiveEnabled: " << evt.unicastReceiveEnabled
      << " mediaInactivityMonitorEnabled: " << evt.mediaInactivityMonitorEnabled
      << " participantUpdateListEnabled: " << evt.participantUpdateListEnabled
      << " ignoreIncomingInitiate: " << evt.ignoreIncomingInitiate
      << " participantListFetchLimit: " << evt.participantListFetchLimit
      << " connectionRetryMaximum: " << evt.connectionRetryMaximum
      << " connectedEndpointThresholdPercentage: " << evt.connectedEndpointThresholdPercentage
      << " outgoingSessionStartupDelayMsecs: " << evt.outgoingSessionStartupDelayMsecs
      << " outgoingSessionWaitForTalkSpurtMsecs: " << evt.outgoingSessionWaitForTalkSpurtMsecs
      << " outgoingSessionExpiryMsecs: " << evt.outgoingSessionExpiryMsecs
      << " outgoingConnectedMsecs: " << evt.outgoingConnectedMsecs
      << " outgoingConnectionErrorMsecs: " << evt.outgoingConnectionErrorMsecs
      << " outgoingDisconnectedMsecs: " << evt.outgoingDisconnectedMsecs
      << " incomingSetupMsecs: " << evt.incomingSetupMsecs
      << " incomingConnectionRetryMsecs: " << evt.incomingConnectionRetryMsecs
      << " incomingConnectedMsecs: " << evt.incomingConnectedMsecs
      << " incomingWaitForAnswerMsecs: " << evt.incomingWaitForAnswerMsecs
      << " incomingRejectedMsecs: " << evt.incomingRejectedMsecs
      << " unicastEndpointListIntervalMsecs: " << evt.unicastEndpointListIntervalMsecs
      << " unicastDiscoverySendIntervalMsecs: " << evt.unicastDiscoverySendIntervalMsecs
      << " unicastInitialDiscoveryRetryIntervalMsecs: " << evt.unicastInitialDiscoveryRetryIntervalMsecs
      << " unicastDiscoveryRetryIntervalMsecs: " << evt.unicastDiscoveryRetryIntervalMsecs
      << " unicastDiscoveryRetryIntervalMaximumMsecs: " << evt.unicastDiscoveryRetryIntervalMaximumMsecs
      << " networkFailureConnectionIntervalMsecs: " << evt.networkFailureConnectionIntervalMsecs
      << " networkChangeConnectionIntervalMsecs: " << evt.networkChangeConnectionIntervalMsecs
      << " networkWifiStatusIntervalMsecs: " << evt.networkWifiStatusIntervalMsecs
      << " unicastSignallingRetryIntervalMsecs: " << evt.unicastInitRetryIntervalMsecs
/*
#ifdef CPCAPI2_AUTO_TEST
      << " forcedTransactionID: " << evt.getTransactionID()
#endif
*/
      << " maximumUnicastTargetLimit: " << evt.maximumUnicastTargetLimit
      << " maximumUnicastMessageRetryLimit: " << evt.maximumUnicastMessageRetryLimit
      << " networkMaskOverride: " << evt.networkMaskOverride
      << ss.str();

   return os;
}

std::string getDebugString(PushToTalk::PttReceiverEndReasonType reason)
{
   std::stringstream ss;
   ss << reason << " (";
   std::string sreason("");
   switch (reason)
   {
      case PushToTalk::PttReceiverEndReasonType_SetupTimeout: sreason = PTT_SESSION_END_REASON_WAIT_FOR_APP_TIMEOUT; break;
      case PushToTalk::PttReceiverEndReasonType_WaitForAnswerTimeout: sreason = PTT_SESSION_END_REASON_WAIT_FOR_ANSWER_TIMEOUT; break;
      case PushToTalk::PttReceiverEndReasonType_App: sreason = PTT_SESSION_END_REASON_APP_REQUEST; break;
      case PushToTalk::PttReceiverEndReasonType_Reject: sreason = PTT_SESSION_END_REASON_REJECT; break;
      case PushToTalk::PttReceiverEndReasonType_MediaInactive: sreason = PTT_SESSION_END_REASON_MEDIA_INACTIVITY; break;
      case PushToTalk::PttReceiverEndReasonType_NetworkChange: sreason = PTT_SESSION_END_REASON_NETWORK_CHANGE; break;
      case PushToTalk::PttReceiverEndReasonType_ChannelOverride: sreason = PTT_SESSION_END_REASON_CHANNEL_OVERRIDE; break;
      default: sreason = "unknown"; break;
   }
   ss << sreason << ")";
   return ss.str();
}

std::string getReceiverEndReasonCode(PushToTalk::PttReceiverEndReasonType reason)
{
   std::string sreason("");
   switch (reason)
   {
      case PushToTalk::PttReceiverEndReasonType_SetupTimeout: sreason = PTT_SESSION_END_REASON_WAIT_FOR_APP_TIMEOUT; break;
      case PushToTalk::PttReceiverEndReasonType_WaitForAnswerTimeout: sreason = PTT_SESSION_END_REASON_WAIT_FOR_ANSWER_TIMEOUT; break;
      case PushToTalk::PttReceiverEndReasonType_App: sreason = PTT_SESSION_END_REASON_APP_REQUEST; break;
      case PushToTalk::PttReceiverEndReasonType_Reject: sreason = PTT_SESSION_END_REASON_REJECT; break;
      case PushToTalk::PttReceiverEndReasonType_MediaInactive: sreason = PTT_SESSION_END_REASON_MEDIA_INACTIVITY; break;
      case PushToTalk::PttReceiverEndReasonType_NetworkChange: sreason = PTT_SESSION_END_REASON_NETWORK_CHANGE; break;
      case PushToTalk::PttReceiverEndReasonType_ChannelOverride: sreason = PTT_SESSION_END_REASON_CHANNEL_OVERRIDE; break;
      default: sreason = "unknown"; break;
   }
   return sreason;
}

std::string getSessionErrorCode(PushToTalk::PttSessionError errorCode)
{
   std::string serror("");
   switch (errorCode)
   {
      case PttSessionError_SetupTimeout: serror = "setup timeout"; break;
      case PttSessionError_ConnectionTimeout: serror = "connection timeout"; break;
      case PttSessionError_CallOverride: serror = "call override"; break;
      case PttSessionError_ChannelOverride: serror = "channel override"; break;
      case PttSessionError_OfferResponseTimeout: serror = "offer response timeout"; break;
      case PttSessionError_InitRecordingFailure: serror = "init recording failure"; break;
#ifdef CPCAPI2_AUTO_TEST
      case PttSessionError_UnicastDiscard: serror = "unicast discard"; break;
#endif
      default: serror = "none"; break;
   }
   return serror;
}
   
PushToTalk::PttReceiverEndReasonType getReceiverEndReasonCode(std::string reason)
{
   PushToTalk::PttReceiverEndReasonType reasonCode = PushToTalk::PttReceiverEndReasonType_None;
   
   if (reason.compare(PTT_SESSION_END_REASON_WAIT_FOR_APP_TIMEOUT) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_SetupTimeout;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_WAIT_FOR_ANSWER_TIMEOUT) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_WaitForAnswerTimeout;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_APP_REQUEST) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_App;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_REJECT) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_Reject;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_MEDIA_INACTIVITY) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_MediaInactive;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_NETWORK_CHANGE) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_NetworkChange;
   }
   else if (reason.compare(PTT_SESSION_END_REASON_CHANNEL_OVERRIDE) == 0)
   {
      reasonCode = PushToTalk::PttReceiverEndReasonType_ChannelOverride;
   }
   return reasonCode;
}

int channelOverrideDurationMs = 1000;

void PushToTalkSettingsInternal::reset_outgoingSessionExpiryMsecs(PttServiceType serviceType)
{
   if (outgoingSessionExpiryMsecs == -1)
   {
      outgoingSessionExpiryMsecs = (serviceType == PttServiceType_LAN ? PTT_SESSION_EXPIRY_INTERVAL_MSECS : PTT_SESSION_EXPIRY_INTERVAL_WAN_MSECS);
   }
}

#ifdef CPCAPI2_AUTO_TEST
unsigned int PushToTalkSettingsInternal::getTransactionID()
{
   if (NULL != forcedTransactionID && *forcedTransactionID > 0)
      return *forcedTransactionID;
   return ::time(NULL);
}
#endif

PushToTalkManagerImpl::PushToTalkManagerImpl(PhoneInterface* cpcPhone, CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, LocalLogger* localLogger) :
mLocalLogger(localLogger),
mPhone(cpcPhone),
mPttMgr(pttIf),
mNextStaleSessionIdx(0),
mNextStaleReceiverEndedTransactionIdx(0),
mService(pttIf->getPttServiceHandle()),
mServiceStatus(PttServiceStatusChangedEvent::Status_Disabled),
mPeerConnMgr(CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(cpcPhone)),
mNetworkChangeInProgress(false),
mPttServiceMuteEnabled(false),
mSenderSessionFactory(new PttSenderSessionFactory(this)),
mReceiverSessionFactory(new PttReceiverSessionFactory(this)),
mSenderStateFactory(new PttSenderSessionStateFactory(this)),
mReceiverStateFactory(new PttReceiverSessionStateFactory(this)),
mTransport(NULL),
mSocketHolder(std::make_shared<PushToTalkSenderReceiveSocketHolder>()),
mConfConnMgr(CPCAPI2::ConferenceConnector::ConferenceConnectorManager::getInterface(cpcPhone)),
mConfConnSession(new PttConferenceConnectorSession(this))
{
   mSenderStateFactory->create();
   mReceiverStateFactory->create();

   CPCAPI2::Media::MediaManagerInterface* mm = dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone));
   mMediaStack = mm->media_stack_ptr();
}

PushToTalkManagerImpl::~PushToTalkManagerImpl()
{
   InfoLog(<< "~PushToTalkManagerImpl(): " << this << " service: " << mService);
   shutdownService(mService, false);

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      i->second->cancelTimers();
      delete (i->second);
   }

   mSessionList.clear();
   mSocketHolder.reset();

   mNetworkChangeInProgress = false;

   delete mSenderSessionFactory; mSenderSessionFactory = NULL;
   delete mReceiverSessionFactory; mReceiverSessionFactory = NULL;
   delete mSenderStateFactory; mSenderStateFactory = NULL;
   delete mReceiverStateFactory; mReceiverStateFactory = NULL;
   delete mConfConnSession; mConfConnSession = NULL;
}

resip::MultiReactor& PushToTalkManagerImpl::getReactor()
{
   return (mPhone->getSdkModuleThread());
}

void PushToTalkManagerImpl::sendServiceStatus(PttServiceStatusChangedEvent::Status status, PttServiceStatusChangedEvent::Reason reason, std::string description)
{
   // TODO: Block or assert some invalid status changes, common to see DISABLED to DISABLED
   bool handleStatusUpdate = true;
   if (mServiceStatus == status)
   {
      // Prevent duplicate status updates to the app
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disconnected) && (status == PttServiceStatusChangedEvent::Status_Disconnecting))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disconnected) && (status == PttServiceStatusChangedEvent::Status_Failure))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Failure) && (status == PttServiceStatusChangedEvent::Status_Disconnecting))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Failure) && (status == PttServiceStatusChangedEvent::Status_Disconnected))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disabled) && (status == PttServiceStatusChangedEvent::Status_Disconnecting))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disabled) && (status == PttServiceStatusChangedEvent::Status_Disconnected))
   {
      handleStatusUpdate = false;
   }
   else if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disabled) && (status == PttServiceStatusChangedEvent::Status_Failure))
   {
      handleStatusUpdate = false;
   }

   if (handleStatusUpdate)
   {
      DebugLog(<< "PushToTalkManagerImpl::sendServiceStatus(): changing service: " << mService << " status from " << mServiceStatus << " to " << status);
      PttServiceStatusChangedEvent statusEvt;
      statusEvt.status = status;
      statusEvt.reason = reason;
      statusEvt.statusDescription = description.c_str();
      statusEvt.transport = NetworkTransport::TransportNone;

      if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
      {
         statusEvt.transport = network->networkTransport();
      }

      if ((mServiceStatus == PttServiceStatusChangedEvent::Status_Disconnecting) && (status == PttServiceStatusChangedEvent::Status_Disabled))
      {
         // TODO: why are we not getting the disconnected status, noted in WAN
         statusEvt.status = PttServiceStatusChangedEvent::Status_Disconnected;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStatusChanged), mService, mService, statusEvt);
         statusEvt.status = status;
      }

      mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStatusChanged), mService, mService, statusEvt);
      mServiceStatus = status;
   }
   else
   {
      DebugLog(<< "PushToTalkManagerImpl::sendServiceStatus(): ignoring invalid service: " << mService << " status update from " << mServiceStatus << " to " << status);
   }
}

int PushToTalkManagerImpl::removeObservers()
{
   if (mPeerConnMgr)
   {
      mPeerConnMgr = NULL;
      PeerConnectionManagerInterface* pcIf = dynamic_cast<PeerConnectionManagerInterface*>(PeerConnectionManager::getInterface(mPhone));
      if (pcIf)
      {
         pcIf->removeSdkObserverSafe(shared_from_this());
      }
   }

   if (mConfConnMgr)
   {
      mConfConnMgr = NULL;
      ConferenceConnectorInterface* ccIf = dynamic_cast<ConferenceConnectorInterface*>(ConferenceConnectorManager::getInterface(mPhone));
      if (ccIf)
      {
         ccIf->removeSdkObserverSafe(shared_from_this());
      }
   }

   return kSuccess;
}

int PushToTalkManagerImpl::setHandler(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler)
{
   DebugLog(<< "PushToTalkManagerImpl::setHandler(): " << this << " service: " << mService << " handler: " << handler);

   if (handler == NULL)
   {
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, false);
      }
      mTransport.reset();

      if (mServiceSettings.serviceType == PttServiceType_WAN)
      {
         if (mConfConnMgr && (mConfConnSession->mConfConnHdl > 0))
         {
            sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnecting);
            mConfConnMgr->disconnectFromConferenceService(mConfConnSession->mConfConnHdl);
            mConfConnSession->mDisconnectedConfConnHdl = mConfConnSession->mConfConnHdl;
            // mConfConnSession->mConfConnHdl = (ConferenceConnectorHandle)(-1);
         }
      }

      removeObservers();
   }
   else
   {
      PeerConnectionManagerInterface* pcIf = dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr);
      if (pcIf)
      {
         pcIf->addSdkObserverSafe(shared_from_this());
      }

      ConferenceConnectorInterface* ccIf = dynamic_cast<ConferenceConnectorInterface*>(mConfConnMgr);
      if (ccIf)
      {
         ccIf->addSdkObserverSafe(shared_from_this());
      }
   }

   return kSuccess;
}

int PushToTalkManagerImpl::configureService(PushToTalkServiceHandle service, const PushToTalkServiceSettings& _settings)
{
   PushToTalkServiceSettings settings = _settings;
   std::string identitiesStr;
   {
      std::stringstream ss;
      const cpc::vector<PttIdentity>& localIdentities = settings.localIdentities;
      for (int i=0; i<localIdentities.size(); i++)
      {
         ss << "\n{";
         ss << localIdentities[i].identityType << "/" << localIdentities[i].userName << "/" << localIdentities[i].displayName;
         ss << "} ";
      }
      identitiesStr = ss.str();
   }

   if (!((settings.signallingDscp >= 0) && (settings.signallingDscp <= 63)))
   {
      WarningLog(<< "PushToTalkManagerImpl::configureService(): service: " << service << " invalid signalling dscp: " << settings.signallingDscp << " overriding dscp value to: " << PTT_SERVICE_SIGNALLING_DSCP);
      settings.signallingDscp = PTT_SERVICE_SIGNALLING_DSCP;
   }

   if (!((settings.mediaDscp >= 0) && (settings.mediaDscp <= 63)))
   {
      WarningLog(<< "PushToTalkManagerImpl::configureService(): service: " << service << " invalid media dscp: " << settings.mediaDscp << " overriding dscp value to: " << PTT_SERVICE_MEDIA_DSCP);
      settings.mediaDscp = PTT_SERVICE_MEDIA_DSCP;
   }

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      InfoLog(<< "PushToTalkManagerImpl::configureService(): " << this << " service: " << mService << " service-type: " << settings.serviceType
         << " settings: localIdentities: " << identitiesStr
         << " auth-server: " << settings.authServiceAddress
         << " conf-server: " << settings.confServiceAddress
         << " username: " << settings.username
         << " signalling-dscp: " << settings.signallingDscp << " media-dscp: " << settings.mediaDscp);
   }
   else
   {
      InfoLog(<< "PushToTalkManagerImpl::configureService(): " << this << " service: " << mService << " service-type: " << settings.serviceType
         << " settings: localIdentities: " << identitiesStr
         << " unicast-address: " << settings.unicastBindAddress.c_str() << ":" << settings.unicastPort
         << " signalling-dscp: " << settings.signallingDscp << " media-dscp: " << settings.mediaDscp);
   }

   mServiceSettings = settings;
   mInternalSettings.reset_outgoingSessionExpiryMsecs(settings.serviceType);

   if (mServiceSettings.localIdentities.empty())
   {
      mServiceSettings.localIdentities.push_back(mServiceSettings.senderIdentity);
   }

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      if (!(mServiceSettings.authServiceAddress.size() > 0 && mServiceSettings.confServiceAddress.size() > 0))
      {
         InfoLog(<< "PushToTalkManagerImpl::configureService(): " << this << " service: " << mService << " server address not configured");
         return kError;
      }
   }

   PttServiceConfiguredEvent cfgEvt;
   cfgEvt.settings = mServiceSettings;
   mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceConfigured), mService, mService, cfgEvt);

	return kSuccess;
}

int PushToTalkManagerImpl::queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::queryEndpointList(): " << this << " service: " << mService);
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->queryEndpointList(service);
   }
   return kSuccess;
}

int PushToTalkManagerImpl::configureEndpointListAutoUpdate(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate)
{
   InfoLog(<< "PushToTalkManagerImpl::configureEndpointListAutoUpdate(): " << this << " service: " << mService << " autoUpdate: " << autoUpdate);
   if (autoUpdate)
   {
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         unicastTransport->queryEndpointList(service);
      }
   }
   mServiceSettings.endpointListAutoUpdateEnabled = autoUpdate;
   return kSuccess;
}

int PushToTalkManagerImpl::setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels)
{
   bool conferencesCreated(false);
   setChannelSubscriptionsImpl(service, channels, conferencesCreated);
   return kSuccess;
}

void PushToTalkManagerImpl::setChannelSubscriptionsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated)
{
   mServiceSettings.subscribedChannels = channels;
   mSubscribedChannels = channels;

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      if (!(mServiceSettings.authServiceAddress.size() > 0 && mServiceSettings.confServiceAddress.size() > 0))
      {
         InfoLog(<< "PushToTalkManagerImpl::setChannelSubscriptionsImpl(): " << this << " service: " << mService << " server address not configured");
         return;
      }
   }

   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::setChannelSubscriptionsImpl(): service: " << service << " conference manager not initialized");
      return;
   }

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      mConfConnSession->setChannelSubscriptions(service, channels, conferencesCreated);
   }

   PttServiceConfiguredEvent cfgEvt;
   cfgEvt.settings = mServiceSettings;
   mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceConfigured), mService, mService, cfgEvt);
}

int PushToTalkManagerImpl::startService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   logWifiStatus();

   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      if (unicastTransport->hasDiscoveryStarted())
      {
         WarningLog(<< "PushToTalkManagerImpl::startService(): " << this << " mService: " << mService << " service already started. Will try to shut service down before continuing");
         shutdownService(service, true, false); // Retain the handlers
      }
   }

   // mDeletedConfList.clear();
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::startService(): service: " << service << " conference manager not initialized");
      return kError;
   }

   resip::Data localIp = guessLocalIpAddress();
   const std::string build = "CPCAPI2_build_stamp{" CPCAPI2_BRAND_BUILD_STAMP "}";
   StatLog(PTT_LOGID_START_SERVICE, << "PushToTalkManagerImpl::startService(): service: " << mService << " local-ip: " << localIp.c_str() << " identity: " << getLocalIdentity() << " build: " << build << " stat-version: " << PTT_STAT_VERSION);

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      mConfConnSession->startService(service);
   }
   else
   {
      sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting);
      if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
      {
         if (network->networkTransport() != TransportWiFi)
         {
            DebugLog(<< "Network transport is not wifi (is " << network << "); firing onPttServicedStarted with startupSuccessful=false for service: " << mService);
            PttServiceStartedEvent startedEvt;
            mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

            sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_WIFI_NOT_AVAILABLE);
            return kSuccess;
         }
      }

      UnicastTransport* unicastTransport = new UnicastTransport(shared_from_this());
      mTransport.reset(unicastTransport);
      mSocketHolder->clear();
      if (unicastTransport->init(mPhone, this, mSocketHolder, mServiceSettings, mInternalSettings) != 0)
      {
         PttServiceStartedEvent startedEvt;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, false);
         mTransport.reset();
         return kError;
      }

      if (mServiceSettings.unicastDiscoveryEnabled)
      {
         unicastTransport->startDiscovery(service);
      }
      else
      {
         PttServiceStartedEvent startedEvt;
         startedEvt.startupSuccessful = true;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

         sendServiceStatus(PttServiceStatusChangedEvent::Status_Connected);
         sendServiceStatus(PttServiceStatusChangedEvent::Status_Ready);
      }
   }
   return kSuccess;
}

int PushToTalkManagerImpl::shutdownService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool sendStatus, bool releaseHandler)
{
   logWifiStatus();

   InfoLog(<< "PushToTalkManagerImpl::shutdownService(): " << this << " ptt service: " << service);
   // mDeletedConfList.clear();
   if (sendStatus)
   {
      sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnecting);
   }

   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, sendStatus); // Will send disconnected status
   }
   mTransport.reset();

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      mConfConnSession->shutdownService(service);
   }

   if (releaseHandler)
   {
      if (sendStatus)
      {
         sendServiceStatus(PttServiceStatusChangedEvent::Status_Disabled);
      }
      setHandler(service, NULL);
   }

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      i->second->cancelTimers();
      delete (i->second);
   }

   mSessionList.clear();

   return kSuccess;
}

int PushToTalkManagerImpl::createPttSession(PushToTalkSessionHandle ptt)
{
   if (getServiceStatus() != PttServiceStatusChangedEvent::Status_Ready)
   {
      InfoLog(<< "PushToTalkManagerImpl::createPttSession(): " << this << " service: " << mService << " ptt: " << ptt << " ignoring request as service is not active");
      return kError;
   }

   if (mNetworkChangeInProgress)
   {
      InfoLog(<< "PushToTalkManagerImpl::createPttSession(): " << this << " service: " << mService << " ptt: " << ptt << " ignoring request as network change handling is in progress");
      return kError;
   }

   PttSessionList::iterator i = mSessionList.find(ptt);
   if (i != mSessionList.end())
   {
      InfoLog(<< "PushToTalkManagerImpl::createPttSession(): " << this << " ptt handle already exists - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   PttSession* session = mSenderSessionFactory->create(ptt);
   mSessionList[session->getPtt()] = session;

   StatLog(PTT_LOGID_SESSION_CREATE, << "PushToTalkManagerImpl::createPttSession(): " << this << " ptt " << session->getSessionType() << " session created - service: " << mService << " ptt: " << ptt << " session-count: " << mSessionList.size());
   return kSuccess;
}

int PushToTalkManagerImpl::addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::addRecipient(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt << " uri: " << GetIdentityUrl(identity));
      return kError;
   }

   i->second->addRecipient(ptt, identity);
   return kSuccess;
}

int PushToTalkManagerImpl::setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::setChannel(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt << " channel: " << channel);
      return kError;
   }

   i->second->setChannel(ptt, channel);
   return kSuccess;
}

int PushToTalkManagerImpl::start(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::start(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->start(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::end(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      StatLog(PTT_LOGID_SESSION_END_INVALID_PTT_HANDLE, << "PushToTalkManagerImpl::end(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->end(ptt, PTT_SESSION_END_REASON_APP_REQUEST);
   return kSuccess;
}

int PushToTalkManagerImpl::startTalkSpurt(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::startTalkSpurt(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->startTalkSpurt(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::endTalkSpurt(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::endTalkSpurt(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->endTalkSpurt(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::accept(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      StatLog(PTT_LOGID_SESSION_RX_ACCEPT_INVALID_PTT_HANDLE, << "PushToTalkManagerImpl::accept(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->accept(ptt);

   if (mPttServiceMuteEnabled)
   {
      i->second->mute(ptt);
   }
   return kSuccess;
}

int PushToTalkManagerImpl::reject(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      StatLog(PTT_LOGID_SESSION_RX_REJECT_INVALID_PTT_HANDLE, << "PushToTalkManagerImpl::reject(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->reject(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::setAudioDeviceCloseDelay(int audioDeviceCloseDelay)
{
   mPeerConnMgr->setAudioDeviceCloseDelay(audioDeviceCloseDelay);
   return kSuccess;
}

int PushToTalkManagerImpl::mutePttSession(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::mutePttSession(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->mute(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::unmutePttSession(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::mutePttSession(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->unmute(ptt);
   return kSuccess;
}

int PushToTalkManagerImpl::mutePttService(PushToTalkServiceHandle service)
{
   mPttServiceMuteEnabled = true;
   PttSessionList::iterator i = mSessionList.begin();
   for (; i != mSessionList.end(); ++i)
   {
      i->second->mute(i->first);
   }
   return kSuccess;
}

int PushToTalkManagerImpl::unmutePttService(PushToTalkServiceHandle service)
{
   mPttServiceMuteEnabled = false;
   PttSessionList::iterator i = mSessionList.begin();
   for (; i != mSessionList.end(); ++i)
   {
      i->second->unmute(i->first);
   }
   return kSuccess;
}

bool cpcVectorIpRangeCompare(const cpc::vector<PushToTalkIpAddressRange>& a, const cpc::vector<PushToTalkIpAddressRange>& b)
{
   if (a.size() != b.size())
      return false;

   for (int i = 0; i < a.size(); i++)
   {
      if (a[i].ipAddrStart != b[i].ipAddrStart || a[i].ipAddrEnd != b[i].ipAddrEnd)
         return false;
   }
   return true;
}

int PushToTalkManagerImpl::setPttInternalSettings(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings)
{
   InfoLog(<< "PushToTalkManagerImpl::setPttInternalSettings(): " << this << " service: " << mService << " settings: " << settings);
   mInternalSettings = settings;
   if (settings.outgoingSessionExpiryMsecs == (-1))
   {
      mInternalSettings.reset_outgoingSessionExpiryMsecs(mServiceSettings.serviceType);
   }
   else
   {
      mInternalSettings.outgoingSessionExpiryMsecs = settings.outgoingSessionExpiryMsecs;
   }
   return kSuccess;
}

int PushToTalkManagerImpl::enableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::enableUnicastTransmission(): " << this << " service: " << service);
   mInternalSettings.unicastTransmitEnabled = true;
   return kSuccess;
}

int PushToTalkManagerImpl::disableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::enableUnicastTransmission(): " << this << " service: " << service);
   mInternalSettings.unicastTransmitEnabled = false;
   return kSuccess;
}

int PushToTalkManagerImpl::enableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::enableUnicastReceive(): " << this << " service: " << service);
   mInternalSettings.unicastReceiveEnabled = true;
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      if (unicastTransport->init(mPhone, this, mSocketHolder, mServiceSettings, mInternalSettings) != 0)
      {
         DebugLog(<< "PushToTalkManagerImpl::enableUnicastReceive(): " << this << " service: " << service << " failure initializing unicast transport");
         PttServiceStartedEvent startedEvt;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, false);
         mTransport.reset();
         return kError;
      }
   }
   return kSuccess;
}

int PushToTalkManagerImpl::disableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::disableUnicastReceive(): " << this << " service: " << service);
   mInternalSettings.unicastReceiveEnabled = false;
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->shutdown();
   }
   return kSuccess;
}

int PushToTalkManagerImpl::dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable)
{
   InfoLog(<< "PushToTalkManagerImpl::dropIncomingJsonMessages(): " << this << " service: " << service << " enable: " << enable);
   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      mConfConnSession->dropIncomingJsonMessages(service, enable);
   }
   return kSuccess;
}

int PushToTalkManagerImpl::disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PushToTalkManagerImpl::disconnectWanConnection(): " << this << " service: " << service);
   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      mConfConnSession->disconnectWanConnection(service);
   }
   return kSuccess;
}

int PushToTalkManagerImpl::queryMediaStatistics(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::queryMediaStatistics(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   PttReceiverWanSession* receiverSession = dynamic_cast<PttReceiverWanSession*>(i->second);
   PttSenderWanSession* senderSession = dynamic_cast<PttSenderWanSession*>(i->second);
   if (receiverSession || senderSession)
   {
      CPCAPI2::PeerConnection::PeerConnectionHandle pc = 0;
      if (receiverSession)
      {
         pc = receiverSession->mCloudPeerConnection;
      }
      else if (senderSession)
      {
         pc = senderSession->mCloudPeerConnection;
      }
      InfoLog(<< "PushToTalkManagerImpl::queryMediaStatistics(): " << this << " service: " << mService << " ptt: " << ptt << " pc: " << pc << " sender-session: " << senderSession << " receiver-session: " << receiverSession);
      PeerConnectionManagerInterface* pcIf = dynamic_cast<PeerConnectionManagerInterface*>(mPeerConnMgr);
      if (pcIf)
      {
         pcIf->queryMediaStatistics(pc);
      }
   }

   return kSuccess;
}

int PushToTalkManagerImpl::enableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::enableMediaInactivityMonitor(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->enableIncomingMediaInactivityMonitor();
   return kSuccess;
}

int PushToTalkManagerImpl::disableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::disableMediaInactivityMonitor(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return kError;
   }

   i->second->disableIncomingMediaInactivityMonitor();
   return kSuccess;
}

int PushToTalkManagerImpl::handleRawPttRequest(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest)
{
   DebugLog(<< "PushToTalkManagerImpl::handleRawPttRequest(): " << this << " received from: " << srcIp << ":" << srcPort);
   onIncomingImpl(srcIp.c_str(), srcPort, std::string(pttRequest.c_str(), pttRequest.size()));
   return kSuccess;
}

// DeadlineTimerHandler

void PushToTalkManagerImpl::onTimer(unsigned short timerId, void* appState)
{
   /*
   StackLog(<< "PushToTalkManagerImpl::onTimer(): timerId: " << timerId);
   switch (timerId)
   {
      default:
      {
         InfoLog(<< "PushToTalkManagerImpl::::onTimer(): " << this << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
   */
}

// PeerConnectionHandler

int PushToTalkManagerImpl::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   if (getServiceSettings().serviceType == PttServiceType_WAN)
   {
      return kSuccess;
   }

   CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
   if (!session)
   {
      InfoLog(<< "PushToTalkManagerImpl::onSignalingStateChange(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
      return kError;
   }

   session->onSignalingStateChange(pc, args);
   return kSuccess;
}

int PushToTalkManagerImpl::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   mThreadCheck.test();
   {
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         return kSuccess;
      }

      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onCreateOfferResult(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onCreateOfferResult(pc, args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   mThreadCheck.test();
   {
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         return kSuccess;
      }

      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onCreateAnswerResult(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onCreateAnswerResult(pc, args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   mThreadCheck.test();
   {
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         return kSuccess;
      }

      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onSetLocalSessionDescriptionResult(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onSetLocalSessionDescriptionResult(pc, args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   mThreadCheck.test();
   {
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         return kSuccess;
      }

      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onSetRemoteSessionDescriptionResult(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onSetRemoteSessionDescriptionResult(pc, args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   mThreadCheck.test();
   {
      cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
      CPCAPI2::PushToTalk::PttSession* session = NULL;
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
         {
            if (dynamic_cast<PttReceiverWanSession*>(i->second))
            {
               if (dynamic_cast<PttReceiverWanSession*>(i->second)->mCloudPeerConnection == pc)
               {
                  DebugLog(<< "PushToTalkManagerImpl::onMediaInactivity(): " << this << " local-identity: " << localIdentStr << " pc: " << pc << " found in ptt session: " << i->first << " for service: " << mService);
                  session = i->second;
                  break;
               }
            }
         }
      }
      else
      {
         session = getPttSessionForCallId(pc);
      }

      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onMediaInactivity(): " << this << " local-identity: " << localIdentStr << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onMediaInactivity(pc, args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   mThreadCheck.test();
   {
      if (getServiceSettings().serviceType == PttServiceType_WAN)
      {
         return kSuccess;
      }

      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForCallId(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onError(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      session->onError(pc, args);
   }

   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
int PushToTalkManagerImpl::onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::PeerConnectionMediaStatisticsEvent& args)
{
   mThreadCheck.test();
   {
      CPCAPI2::PushToTalk::PttSession* session = getPttSessionForPeerConnection(pc);
      if (!session)
      {
         InfoLog(<< "PushToTalkManagerImpl::onPeerConnectionMediaStatistics(): " << this << " invalid call handle - service: " << mService << " pc: " << pc);
         return kError;
      }

      PttMediaStatisticsEvent statEvent;
      for (auto i = args.mediaStreamStats.begin(); i != args.mediaStreamStats.end(); ++i)
      {
         PttMediaStatistics stats;
         stats.connectionId = pc;
         stats.mediaStreamId = i->mediaStream;
         stats.rtpPacketCount = i->rtpPacketCount;
         stats.rtcpPacketCount = i->rtcpPacketCount;
         statEvent.mediaStreamStats.push_back(stats);
      }

      mPttMgr->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttMediaStatistics), mService, session->getPtt(), statEvent);
   }

   return kSuccess;
}
#endif

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
int PushToTalkManagerImpl::onServiceConnectionStatusChanged(ConferenceConnectorHandle conn, const ServiceConnectionStatusEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::onServiceConnectionStatusChanged(): conference manager not initialized - conference: " << conn << " server-uri: " << args.serverUri.c_str() << " connection-status: " << args.connectionStatus);
      return kError;
   }

   StatLog(PTT_LOGID_WAN_CONFERENCE_STATUS_CHANGE, << "PushToTalkManagerImpl::onServiceConnectionStatusChanged(): " << this << " service: " << mService << " local-identity: " << getLocalIdentity() << " connector: " << conn << " status: " << args.connectionStatus << " description: " << args.statusDesc.c_str());
   mConfConnSession->onServiceConnectionStatusChanged(conn, args);
   return kSuccess;
}

int PushToTalkManagerImpl::onConferenceCreated(ConferenceConnectorHandle conn, const ConferenceCreatedEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::onConferenceCreated(): " << this << " conference manager not initialized - conference: " << conn);
      return kError;
   }

   DebugLog(<< "PushToTalkManagerImpl::onConferenceCreated(): " << this << " service: " << mService << " local-identity: " << getLocalIdentity() << " connector: " << conn << " conference: " << args.conference << " display-name: " << args.displayName);
   mConfConnSession->onConferenceCreated(conn, args);
   return kSuccess;
}

int PushToTalkManagerImpl::onConferenceEnded(ConferenceConnectorHandle conn, const ConferenceEndedEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::onConferenceEnded(): " << this << " conference manager not initialized - conference: " << conn);
      return kError;
   }

   DebugLog(<< "PushToTalkManagerImpl::onConferenceEnded(): " << this << " service: " << mService << " local-identity: " << getLocalIdentity() << " connector: " << conn << " conference: " << args.conference << " display-name: " << args.displayName);
   mConfConnSession->onConferenceEnded(conn, args);
   return kSuccess;
}

int PushToTalkManagerImpl::onConferenceListUpdated(ConferenceConnectorHandle conn, const ConferenceListUpdatedEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::onConferenceListUpdated(): " << this << " conference manager not initialized - conference: " << conn);
      return kError;
   }

   StatLog(PTT_LOGID_WAN_CONFERENCE_LIST_UPDATE, << "PushToTalkManagerImpl::handleConferenceListUpdated(): " << this << " service: " << mService << " local-identity: " << getLocalIdentity() << " connector: " << conn << " server-conference count: " << args.conferenceList.size());
   mConfConnSession->onConferenceListUpdated(conn, args);
   return kSuccess;
}

bool PushToTalkManagerImpl::isSubscribedChannel(CloudConferenceHandle conn, std::string& channelDisplayName)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PushToTalkManagerImpl::isSubscribedChannel(): " << this << " conference manager not initialized - conference: " << conn);
      return false;
   }

   return (mConfConnSession->isSubscribedChannel(conn, channelDisplayName));
}

bool PushToTalkManagerImpl::doesSomeoneHaveTheFloor(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& participantList, CloudConferenceParticipantInfo& speakerIdentity)
{
   cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
   bool someoneHasTheFloor = false;

   DebugLog(<< "PushToTalkManagerImpl::doesSomeoneHaveTheFloor(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " participant-list size: " << participantList.size());
   for (auto itPart = participantList.begin(); itPart != participantList.end(); ++itPart)
   {
      if (itPart->hasFloor)
      {
         someoneHasTheFloor = true;
         speakerIdentity = *itPart;
         InfoLog(<< "PushToTalkManagerImpl::doesSomeoneHaveTheFloor(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " participant: " << speakerIdentity.address.c_str() << " has-floor, no stale check");
         break;
      }
   }

   return someoneHasTheFloor;
}

bool PushToTalkManagerImpl::getSpeakerIdentity(CloudConferenceHandle conference, const cpc::vector<CloudConferenceParticipantInfo>& participantList, bool& olderFloorOwnerExists, CloudConferenceParticipantInfo& speakerIdentity)
{
   cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
   InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " participant-list size: " << participantList.size());
   bool someoneHasTheFloor = false;
   uint64_t speakerJoinTimestamp = 0;
   int staleCount = 0;
   cpc::vector<CloudConferenceParticipantInfo> staleFloorOwnerList;
   olderFloorOwnerExists = false;

   for (auto itPart = participantList.begin(); itPart != participantList.end(); ++itPart)
   {
      bool isStaleSession = PushToTalkManagerImpl::isStaleSession(conference, *itPart);
      if (isStaleSession)
      {
         InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " ignoring participant: " << itPart->address << " at it was found in stale ptt session list");
         staleCount++;
         if (itPart->hasFloor)
         {
            staleFloorOwnerList.push_back(*itPart);
         }
         continue;
      }

      if (itPart->hasFloor)
      {
         if (someoneHasTheFloor)
         {
            // If there are multiple floor owners, we'll end up taking the one that joined first, i.e. has the older join timestamp
            DebugLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " multiple speakers, existing speaker identity: " << speakerIdentity.address << " (joined " << speakerIdentity.joinTimestampMsecs << ") alternate identity: " << itPart->address << " (joined " << itPart->joinTimestampMsecs << ")");

            if (speakerIdentity.address == itPart->address)
            {
               // TODO: What if the same identity address is used on multiple devices

               // If multiple floor-owners have the same address, presuming that one of the sessions must be stale. Will have to apply opposite logic in this case,
               // i.e. the newer join timestamp wins as the older join timestamp can be presumed stale.

               if (speakerIdentity.joinTimestampMsecs > (itPart->joinTimestampMsecs))
               {
                  InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " speaker identity: " << speakerIdentity.address << " appearing multiple times, existing speaker wins with join timestamp: " << speakerIdentity.joinTimestampMsecs);
                  addToStaleSessionList(conference, *itPart);
               }
               else
               {
                  InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " speaker identity: " << speakerIdentity.address << " appearing multiple times, overriding existing speaker due to join timestamp: " << itPart->joinTimestampMsecs);
                  addToStaleSessionList(conference, speakerIdentity);
                  speakerIdentity = *itPart;
               }
            }
            else
            {
               // TODO: What if one of the sessions is stale and has not as yet been added to the list, it will end up winning,
               // possibility of occurrence is during the inactive media timeout period for a ptt conference session, or if a
               // dangling media stream prevents a sesssion to be destroyed due to inactive media
               if (itPart->joinTimestampMsecs < speakerIdentity.joinTimestampMsecs)
               {
                  InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " multiple speakers, existing speaker identity: " << speakerIdentity.address << " updating speaker identity with alternate identity: " << itPart->address);
                  speakerIdentity = *itPart;
               }
            }
         }
         else
         {
            someoneHasTheFloor = true;
            speakerIdentity = *itPart;
            InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " identified speaker identity: " << speakerIdentity.address);
         }
      }
   }
   
   if (someoneHasTheFloor)
   {
      for (auto itPart = staleFloorOwnerList.begin(); itPart != staleFloorOwnerList.end(); ++itPart)
      {
         if (speakerIdentity.address != itPart->address)
         {
            if (itPart->joinTimestampMsecs < speakerIdentity.joinTimestampMsecs)
            {
               InfoLog(<< "PushToTalkManagerImpl::getSpeakerIdentity(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " multiple speakers, selected speaker identity: " << speakerIdentity.address << " actually joined later than the stale alternate identity: " << itPart->address);
               olderFloorOwnerExists = true;
            }
         }
      }
   }

   return someoneHasTheFloor;
}

bool PushToTalkManagerImpl::areWeInTheList(const cpc::vector<CloudConferenceParticipantInfo>& participantList)
{
   for (auto i = participantList.begin(); i != participantList.end(); ++i)
   {
      for (auto j = mServiceSettings.localIdentities.begin(); j != mServiceSettings.localIdentities.end(); ++j)
      {
         if (i->address == j->userName)
         {
            return true;
         }
      }
   }

   return false;
}

bool PushToTalkManagerImpl::areThereMultipleFloorOwners(const cpc::vector<CloudConferenceParticipantInfo>& participantList)
{
   int count = 0;
   for (auto i = participantList.begin(); i != participantList.end(); ++i)
   {
      if (i->hasFloor)
      {
         count++;
      }
   }

   return (count > 1);
}

bool PushToTalkManagerImpl::doesReceiverSessionExist(CloudConferenceHandle conference, PttSession*& session)
{
   for (auto i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      if (dynamic_cast<PttReceiverWanSession*>(i->second))
      {
         if (dynamic_cast<PttReceiverWanSession*>(i->second)->mCloudConference == conference)
         {
            session = i->second;
            return true;
         }
      }
   }

   return false;
}

bool PushToTalkManagerImpl::doesSenderSessionExist(CloudConferenceHandle conference, PttSession*& session)
{
   for (auto i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      if (dynamic_cast<PttSenderWanSession*>(i->second))
      {
         if (dynamic_cast<PttSenderWanSession*>(i->second)->mCloudConference == conference)
         {
            session = i->second;
            return true;
         }
      }
   }

   return false;
}

bool PushToTalkManagerImpl::isStaleSession(CloudConferenceHandle conference, const CloudConferenceParticipantInfo& speakerIdentity)
{
   return (mPttMgr->isStaleSession(conference, speakerIdentity));
}

void PushToTalkManagerImpl::addToStaleSessionList(CloudConferenceHandle conference, const CloudConferenceParticipantInfo& speakerIdentity)
{
   mPttMgr->addToStaleSessionList(conference, speakerIdentity);
}

bool PushToTalkManagerImpl::isSessionInStaleSessionList(CloudConferenceHandle conference, const CloudConferenceParticipantInfo& participantInfo)
{
   return (mPttMgr->isSessionInStaleSessionList(conference, participantInfo));
}

void PushToTalkManagerImpl::sendPttParticipantListUpdate(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args)
{
#ifdef CPCAPI2_AUTO_TEST
   if (mInternalSettings.participantUpdateListEnabled)
   {
      PttParticipantListUpdateEvent participantListEvent;
      if (mServiceSettings.localIdentities.size() > 0)
      {
         participantListEvent.localIdentity = *(mServiceSettings.localIdentities.begin());
      }
      participantListEvent.offset = 0;
      participantListEvent.totalParticipantCount = args.participantList.size();

      for (auto j = mSessionList.begin(); j != mSessionList.end(); ++j)
      {
         CPCAPI2::ConferenceConnector::CloudConferenceHandle cloudConference = 0;
         PttSenderWanSession* senderSession = dynamic_cast<PttSenderWanSession*>(j->second);
         PttReceiverWanSession* receiverSession = dynamic_cast<PttReceiverWanSession*>(j->second);
         if (senderSession)
         {
            cloudConference = senderSession->mCloudConference;
         }
         else if (receiverSession)
         {
            cloudConference = receiverSession->mCloudConference;
         }
         if (j->second)
         {
            if (cloudConference == args.conference)
            {
               participantListEvent.channelId = j->second->getChannelId();

               bool sendList = false;
               for (auto k = args.participantList.begin(); k != args.participantList.end(); ++k)
               {
                  participantListEvent.offset++;
                  PttIdentity identity;
                  identity.userName = k->address;
                  identity.displayName = k->displayName;
                  for (int tag : k->tags)
                  {
                     if (tag == 1) identity.identityType = PttIdentityType_SIP;
                     else if (tag == 2) identity.identityType = PttIdentityType_XMPP;
                     break;
                  }
                  participantListEvent.participants.push_back(identity);

                  if (participantListEvent.offset == args.participantList.size())
                  {
                     sendList = true;
                  }
                  else if ((participantListEvent.offset % (mInternalSettings.participantListFetchLimit)) == 0)
                  {
                     sendList = true;
                  }

                  if (sendList)
                  {
                     mPttMgr->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttParticipantListUpdate), mService, j->first, participantListEvent);
                     participantListEvent.participants.clear();
                     sendList = false;
                  }
               }

               break;
            }
         }
      }
   }
#endif
}

bool PushToTalkManagerImpl::doWeHaveChannelOverride(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference)
{
   cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
   PttSession* senderSession = NULL;
   bool senderSessionExists = doesSenderSessionExist(conference, senderSession);
   PttSenderWanSession* senderWanSession = dynamic_cast<PttSenderWanSession*>(senderSession);
   if (!senderWanSession)
   {
      DebugLog(<< "PushToTalkManagerImpl::doWeHaveChannelOverride(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " sender session does not exist");
      return false;
   }

   PttSession* receiverSession = NULL;
   bool receiverSessionExists = doesReceiverSessionExist(conference, receiverSession);
   PttReceiverWanSession* receiverWanSession = dynamic_cast<PttReceiverWanSession*>(receiverSession);
   if (!receiverWanSession)
   {
      DebugLog(<< "PushToTalkManagerImpl::doWeHaveChannelOverride(): " << this << " local identity: " << localIdentStr << " conference: " << conference << " receiver session does not exist");
      return false;
   }

   //CPCAPI2::ConferenceConnector::ConferenceConnectorHandle senderCloudConnector = senderWanSession->mCloudConnector;
   //CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle senderCloudSession = senderWanSession->mCloudSession;
   //CPCAPI2::PeerConnection::PeerConnectionHandle senderCloudPeerConnection = senderWanSession->mCloudPeerConnection;
   cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& receiverCloudParticipantList = receiverWanSession->mCloudParticipantList;

   CloudConferenceParticipantInfo speakerIdentity;
   PttIdentity speakerPttIdentity;
   bool olderFloorOwnerExists(false);
   std::string channelDisplayName("");
   bool isSubscribedChannel = PushToTalkManagerImpl::isSubscribedChannel(conference, channelDisplayName);
   bool someoneHasTheFloor = getSpeakerIdentity(conference, receiverCloudParticipantList, olderFloorOwnerExists, speakerIdentity);

   if (someoneHasTheFloor)
   {
      speakerPttIdentity.displayName = speakerIdentity.displayName;
      speakerPttIdentity.userName = speakerIdentity.address;
      for (int itag : speakerIdentity.tags)
      {
         if (itag == 1)
         {
            speakerPttIdentity.identityType = PttIdentityType_SIP;
         }
         else if (itag == 2)
         {
            speakerPttIdentity.identityType = PttIdentityType_XMPP;
         }
      }
   }

   // TODO: Need to handle same-identity from multiple devices, need the UUID
   bool participantListContainsUs = areWeInTheList(receiverCloudParticipantList);
   bool multipleFloorOwners = areThereMultipleFloorOwners(receiverCloudParticipantList);
   bool isStaleSession = (someoneHasTheFloor ? PushToTalkManagerImpl::isStaleSession(conference, speakerIdentity) : false);
   bool channelOverride(false);

   InfoLog(<< "PushToTalkManagerImpl::doWeHaveChannelOverride(): " << this << " local identity: " << localIdentStr << " conference: " << conference
      << " subscribed channel list size: " << mSubscribedChannels.size() << " channel: " << channelDisplayName << " is subscribed-channel: " << isSubscribedChannel
      << " sender-session exists: " << senderSessionExists << " receiver-session exists: " << receiverSessionExists << " is stale-session: " << isStaleSession << " participant-list contains us: " << participantListContainsUs << " multiple floor-owners: " << multipleFloorOwners << " speaker-identity: " << (someoneHasTheFloor ? speakerIdentity.address : "not found") << " my-conferences: " << mConfConnSession->mMyConferences.size() << " conference-list: " << mConfConnSession->mConfList.size());

   if (someoneHasTheFloor)
   {
      cpc::vector<CloudConferenceParticipantInfo> floorOwners;
      floorOwners.push_back(speakerIdentity);
      bool areWeTheOwner = areWeInTheList(floorOwners);

      if ((multipleFloorOwners && olderFloorOwnerExists) || (!areWeTheOwner))
      {
         channelOverride = true;
         receiverWanSession->mSenderDestroyedDueToOverride = true;
      }

      if (channelOverride)
      {
         InfoLog(<< "PushToTalkManagerImpl::doWeHaveChannelOverride(): " << this << " local identity: " << localIdentStr << ", ending ptt sender session: " << senderSession->getPtt() << " for conference: " << conference << " due to channel override, multipleFloorOwners: " << multipleFloorOwners << " olderFloorOwnerExists: " << olderFloorOwnerExists << " areWeTheOwner: " << areWeTheOwner);
         PttSessionErrorEvent errorEvt;
         errorEvt.service = mService;
         errorEvt.callerIdentity = speakerPttIdentity;
         errorEvt.channelId = (isSubscribedChannel ? channelDisplayName.c_str() : "");
         errorEvt.errorCode = PttSessionError_ChannelOverride;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, senderSession->getPtt(), errorEvt);
      }
   }
   else
   {
      InfoLog(<< "PushToTalkManagerImpl::doWeHaveChannelOverride(): " << this << " local identity: " << localIdentStr << ", ptt sender session: " << senderSession->getPtt() << " for conference: " << conference << " nobody got the floor");
   }

   return channelOverride;
}

int PushToTalkManagerImpl::onConferenceParticipantListUpdated(ConferenceConnectorHandle conn, const ConferenceParticipantListUpdatedEvent& args)
{
   if (conn == mConfConnSession->mConfConnHdl)
   {
      cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");

#ifdef CPCAPI2_AUTO_TEST
      sendPttParticipantListUpdate(conn, args);
#endif

      if (args.participantList.empty())
      {
         // Clear the list, as looks like we are in sync with the server
         DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " service: " << mService << " clear stale sessions mapped to conference: " << args.conference);
         mPttMgr->resetStaleSessionList(args.conference);
      }

      PttSession* receiverExistingSession = NULL;
      PttSession* senderExistingSession = NULL;
      CloudConferenceParticipantInfo speakerIdentity;
      PttIdentity speakerPttIdentity;
      std::string channelDisplayName("");

      // TODO: Need to handle same-identity from multiple devices, need the UUID
      bool olderFloorOwnerExists(false);
      bool isSubscribedChannel = PushToTalkManagerImpl::isSubscribedChannel(args.conference, channelDisplayName);
      bool senderSessionExists = doesSenderSessionExist(args.conference, senderExistingSession);
      bool receiverSessionExists = doesReceiverSessionExist(args.conference, receiverExistingSession);
      bool participantListContainsUs = areWeInTheList(args.participantList);
      bool removedListContainsUs = areWeInTheList(args.removedParticipants);
      bool multipleFloorOwners = areThereMultipleFloorOwners(args.participantList);
      bool someoneHasTheFloor = getSpeakerIdentity(args.conference, args.participantList, olderFloorOwnerExists, speakerIdentity);
      if (someoneHasTheFloor)
      {
         speakerPttIdentity.displayName = speakerIdentity.displayName;
         speakerPttIdentity.userName = speakerIdentity.address;
         for (int itag : speakerIdentity.tags)
         {
            if (itag == 1)
            {
               speakerPttIdentity.identityType = PttIdentityType_SIP;
            }
            else if (itag == 2)
            {
               speakerPttIdentity.identityType = PttIdentityType_XMPP;
            }
         }
      }
      bool isStaleSession = (someoneHasTheFloor ? PushToTalkManagerImpl::isStaleSession(args.conference, speakerIdentity) : false);

      StatLog(PTT_LOGID_WAN_PARTICIPANT_LIST_UPDATE, << "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " connector: " << conn << " conference: " << args.conference
         << " subscribed channel list size: " << mSubscribedChannels.size() << " is subscribed-channel: " << isSubscribedChannel
         << " sender-session exists: " << senderSessionExists << " receiver-session exists: " << receiverSessionExists << " is stale-session: " << isStaleSession << " participant-list contains us: " << participantListContainsUs << " multiple floor-owners: " << multipleFloorOwners << " floor speaker-identity: " << (someoneHasTheFloor ? speakerIdentity.address : "not found") << " my-conferences: " << mConfConnSession->mMyConferences.size() << " conference-list: " << mConfConnSession->mConfList.size());

      if (senderSessionExists)
      {
         PttSenderWanSession* senderSession = dynamic_cast<PttSenderWanSession*>(senderExistingSession);
         if (senderSession)
         {
            if (someoneHasTheFloor)
            {
               cpc::vector<CloudConferenceParticipantInfo> floorOwners;
               floorOwners.push_back(speakerIdentity);
               bool areWeTheOwner = areWeInTheList(floorOwners);
               bool channelOverride(false);
               if (multipleFloorOwners && olderFloorOwnerExists)
               {
                  channelOverride = true;
               }
               else
               {
                  if (areWeTheOwner)
                  {
                     DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << ", propagating to ptt sender session: " << senderSession->getPtt() << " for conference: " << args.conference << " as the existing sender session has the floor");
                     senderSession->onConferenceParticipantListUpdated(senderSession->getPtt(), conn, args);
                  }
                  else
                  {
                     channelOverride = true;
                  }
               }

               if (channelOverride)
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << ", ending ptt sender session: " << senderSession->getPtt() << " for conference: " << args.conference << " due to channel override, multipleFloorOwners: " << multipleFloorOwners << " olderFloorOwnerExists: " << olderFloorOwnerExists << " areWeTheOwner: " << areWeTheOwner);
                  PttSessionErrorEvent errorEvt;
                  errorEvt.service = mService;
                  errorEvt.callerIdentity = speakerPttIdentity;
                  errorEvt.channelId = (isSubscribedChannel ? channelDisplayName.c_str() : "");
                  errorEvt.errorCode = PttSessionError_ChannelOverride;
                  mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, senderSession->getPtt(), errorEvt);

                  senderSession->end(senderSession->getPtt(), PTT_SESSION_END_REASON_CHANNEL_OVERRIDE);
               }
            }
            else
            {
               InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << ", ptt sender session: " << senderSession->getPtt() << " for conference: " << args.conference << " nobody got the floor");
            }
            return kSuccess;
         }
      }

      if (isSubscribedChannel || mConfConnSession->mMyConferences.count(args.conference) > 0)
      {
         if (!someoneHasTheFloor || isStaleSession) // || onlyDanglingSessionListed)
         {
            DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " nobody has the floor or have a dangling session, end any ptt receiver sessions for conference: " << args.conference << " session-count: " << mSessionList.size() << " participantListContainsUs: " << participantListContainsUs << " senderSessionExists: " << senderSessionExists << " receiverSessionExists: " << receiverSessionExists << " isStaleSession: " << isStaleSession);
            // If participant list is empty or if nobody has the floor, cleanup the associated receiver sessions, this will also cleanup any dangling sessions associated to this conference
            std::vector<std::pair<PushToTalkSessionHandle, PttSession*> > sessionsToEnd;
            for (auto itPttSess = mSessionList.begin(); itPttSess != mSessionList.end(); ++itPttSess)
            {
               DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " ptt: " << itPttSess->first);
               PttReceiverWanSession* receiverSession = dynamic_cast<PttReceiverWanSession*>(itPttSess->second);
               if (receiverSession)
               {
                  DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " ptt: " << itPttSess->first << " with cloud-conference: " << receiverSession->mCloudConference << " is a receiver session");
                  if (receiverSession->mCloudConference == args.conference)
                  {
                     sessionsToEnd.push_back({ itPttSess->first, itPttSess->second });
                  }
               }
            }

            DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " sessions to end: " << sessionsToEnd.size());

            for (auto itSte = sessionsToEnd.begin(); itSte != sessionsToEnd.end(); ++itSte)
            {
               // Session might already have handled an end and as such would be in ending state, so an empty participant list would indicate that the session should be destroyed immediately,
               // best to pass this event on so that the session state can apply the appropriate handling
               PttReceiverWanSession* receiverSession = dynamic_cast<PttReceiverWanSession*>(itSte->second);
               bool rejectedDueToOverride = (receiverSession->mRejected && receiverSession->mSenderDestroyedDueToOverride);
               InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " end the dangling ptt receiver session: " << itSte->first << " rejected: " << receiverSession->mRejected << " senderDestroyedDueToOverride: " << receiverSession->mSenderDestroyedDueToOverride << " isStateSession: " << isStaleSession << " someoneHasTheFloor: " << someoneHasTheFloor << " rejectedDueToOverride: " << rejectedDueToOverride);
               receiverSession->onCloudSessionEnded(itSte->first, args.conference);

               bool anyoneHaveTheFloor = doesSomeoneHaveTheFloor(args.conference, args.participantList, speakerIdentity); // No stale check, No multi-owner check
               if (anyoneHaveTheFloor)
               {
                  speakerPttIdentity.displayName = speakerIdentity.displayName;
                  speakerPttIdentity.userName = speakerIdentity.address;
                  for (int itag : speakerIdentity.tags)
                  {
                     if (itag == 1)
                     {
                        speakerPttIdentity.identityType = PttIdentityType_SIP;
                     }
                     else if (itag == 2)
                     {
                        speakerPttIdentity.identityType = PttIdentityType_XMPP;
                     }
                  }
               }
               bool isStaleSession = (anyoneHaveTheFloor ? PushToTalkManagerImpl::isStaleSession(args.conference, speakerIdentity) : false);

               if (isStaleSession && rejectedDueToOverride)
               {
                  PttReceiverWanSession* session = static_cast<PttReceiverWanSession*>(mReceiverSessionFactory->create(mPttMgr->createPttSessionInternal(mService)));
                  session->mCloudConference = args.conference;
                  session->mCloudParticipantList = args.participantList;
                  mSessionList[session->getPtt()] = session;
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " speaker identity: " << speakerIdentity.address << ", firing onCloudNewParticipantInMyConference() where conference = " << channelDisplayName << " to recreate session destroyed due to channel override");
                  mPttMgr->removeFromStaleSessionList(args.conference, speakerIdentity);
                  session->onCloudNewParticipantInMyConference(session->getPtt(), args.conference, speakerIdentity, isSubscribedChannel ? channelDisplayName.c_str() : "");
               }
            }

            if (participantListContainsUs && (sessionsToEnd.size() == 0))
            {
               // TODO: Could also be a scenario with another device with the same identity
               InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " dangling session, we are in the participant list with no floor owner and do not have an existing session");
            }
         }
         else if ((args.participantList.size() >= 1) && (!participantListContainsUs || (participantListContainsUs && !receiverSessionExists && !senderSessionExists)) && someoneHasTheFloor)
         {
            bool destroySession = false;

            if (receiverSessionExists)
            {
               DebugLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                        << " conference: " << args.conference << " already has a session for conference: " << channelDisplayName);

               PttReceiverWanSession* receiverSession = dynamic_cast<PttReceiverWanSession*>(receiverExistingSession);
               // Destroy existing session and start a new one in the following scenarios:
               // - if owner has changed
               // - if floor status of owner has changed
               // - if peer connection number of owner has changed - could occur due to network changes, missed participant list udpates, etc.
               // - if participant number of owner has changed - could occur due to network changes, missed participant list udpates, etc.
               //
               // Session might already be in ending state, so we need to check the participant, peer-connection and floor status,
               // to decide if the existing session needs to be destroyed immediately, or if a new session needs to be created
               if (speakerIdentity.address != (receiverSession->mCloudConferenceOwner.address))
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " speaker identity has changed from: " << receiverSession->mCloudConferenceOwner.address << " to: " << speakerIdentity.address);
                  destroySession = true;

#ifdef CPCAPI2_AUTO_TEST
                  PttSessionErrorEvent errorEvt;
                  errorEvt.callerIdentity = speakerPttIdentity;
                  errorEvt.channelId = (isSubscribedChannel ? channelDisplayName.c_str() : "");
                  errorEvt.errorCode = PttSessionError_ChannelOverride;
                  mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, receiverSession->getPtt(), errorEvt);
#endif
               }
               else if (!speakerIdentity.hasFloor)
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " speaker floor owner status has changed from: " << receiverSession->mCloudConferenceOwner.hasFloor << " to: " << speakerIdentity.hasFloor);
                  destroySession = true;
               }
               else if (speakerIdentity.peerConnection != (receiverSession->mCloudConferenceOwner.peerConnection))
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " speaker peer connection has changed from: " << receiverSession->mCloudConferenceOwner.peerConnection << " to: " << speakerIdentity.peerConnection);
                  destroySession = true;
               }
               else if (speakerIdentity.participant != (receiverSession->mCloudConferenceOwner.participant))
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " speaker participant handle has changed from: " << receiverSession->mCloudConferenceOwner.participant << " to: " << speakerIdentity.participant);
                  destroySession = true;
               }

               if (multipleFloorOwners)
               {
                  // Hack to handle the multiple floor owner scenario, might be better to be fixed on server side
                  //
                  // Handle the scenario where there is a dangling peer connection or participant, issue was noted with
                  // the ptt-receiver where the ptt-sender went through a network change, ptt-sender ended up having
                  // 2 ptt participants with the same identity, both having the floor, the participant number and peer
                  // connection number was incremented. After the call was over, the original participant with the lower
                  // number stayed dangling, as such the ptt call would not be cleaned up or the session would get recreated
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " there are multiple floor owners, delaying session destruction until servers sends an updated participant list");
                  destroySession = false;
               }

               // TODO: How would we distinguish two participants with the same identity are due to dangling sessions, or due to
               // the same identity using multiple platforms, unless something fishy occurs such as both having the floor.
            }

            if (!receiverSessionExists || (receiverSessionExists && destroySession))
            {
               if (removedListContainsUs)
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                          << " conference: " << args.conference << " has previously removed itself from this conference: " << channelDisplayName);
               }
               else
               {
                  if (destroySession)
                  {
                     InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr
                        << " destroy existing session for conference: " << args.conference);

                     receiverExistingSession->onCloudSessionEnded(receiverExistingSession->getPtt(), args.conference);
                  }

                  PttReceiverWanSession* session = static_cast<PttReceiverWanSession*>(mReceiverSessionFactory->create(mPttMgr->createPttSessionInternal(mService)));
                  session->mCloudConference = args.conference;
                  session->mCloudParticipantList = args.participantList;
                  mSessionList[session->getPtt()] = session;
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " speaker identity: " << speakerIdentity.address << ", firing onCloudNewParticipantInMyConference() where conference = " << channelDisplayName);
                  session->onCloudNewParticipantInMyConference(session->getPtt(), args.conference, speakerIdentity, isSubscribedChannel ? channelDisplayName.c_str() : "");
               }
            }
         }
      }
      else
      {
         InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << ", ignoring participant update as not subscribed, and not my conference: " << args.conference);
         for (auto itMyConf = mConfConnSession->mMyConferences.begin(); itMyConf != mConfConnSession->mMyConferences.end(); ++itMyConf)
         {
            auto itConfList = mConfConnSession->mConfList.begin();
            bool foundMyConf = false;
            for (; itConfList != mConfConnSession->mConfList.end(); ++itConfList)
            {
               if (*itMyConf == itConfList->conference)
               {
                  InfoLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " my conference name: " << itConfList->displayName);
                  foundMyConf = true;
               }
            }
            if (!foundMyConf)
            {
               WarningLog(<< "PushToTalkManagerImpl::onConferenceParticipantListUpdated(): " << this << " local identity: " << localIdentStr << " conference: " << args.conference << " could not find conference " << *itMyConf << " in mConfList");
            }
         }
      }
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onConferenceSessionStatusChanged(ConferenceConnectorHandle conn, const ConferenceSessionStatusChangedEvent& args)
{
   cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
   InfoLog(<< "PushToTalkManagerImpl::onConferenceSessionStatusChanged(): local identity: " << localIdentStr << " conference: " << args.conference << " session: " << args.session << " status: " << args.sessionStatus << " pc: " << args.peerConnection);

   if (conn != mConfConnSession->mConfConnHdl)
   {
      InfoLog(<< "PushToTalkManagerImpl::onConferenceSessionStatusChanged(): local-identity: " << localIdentStr << " connection handle mismatch: connection: " << conn << " local-connection: " << mConfConnSession->mConfConnHdl << " ignore conference session status for conference: " << args.conference << " session: " << args.session);
      return kSuccess;
   }

   // TODO: Do we need to handle any of the other conference status events
   // SessionStatus_NotConnected,
   // SessionStatus_Connecting,
   // SessionStatus_Connected,
   // SessionStatus_ConnectionFailed,
   // SessionStatus_Reconnecting

   if (args.sessionStatus == CPCAPI2::ConferenceConnector::SessionStatus_Connected)
   {
      PttSessionList::iterator i = mSessionList.begin();
      for (; i != mSessionList.end(); ++i)
      {
         PttSenderWanSession* peerSess = dynamic_cast<PttSenderWanSession*>(i->second);
         if (peerSess != NULL)
         {
            if (peerSess->mCloudSession == args.session)
            {
               peerSess->mCloudPeerConnection = (CPCAPI2::PeerConnection::PeerConnectionHandle)args.peerConnection;
               break;
            }
         }
      }
      if ((i == mSessionList.end()) || (i->second == NULL))
      {
         return kError;
      }

      DebugLog(<< "PushToTalkManagerImpl::onConferenceSessionStatusChanged(): local identity: " << localIdentStr << " conference: " << args.conference<< " session: " << args.session << " triggering onCloudSessionConnected");
      i->second->onCloudSessionConnected(i->first, args);
   }
   else if (args.sessionStatus == CPCAPI2::ConferenceConnector::SessionStatus_Connecting)
   {
      PttSessionList::iterator i = mSessionList.begin();
      for (; i != mSessionList.end(); ++i)
      {
         PttReceiverWanSession* peerSess = dynamic_cast<PttReceiverWanSession*>(i->second);
         if (peerSess != NULL)
         {
            if (peerSess->mCloudSession == args.session)
            {
               DebugLog(<< "PushToTalkManagerImpl::onConferenceSessionStatusChanged(): local identity: " << localIdentStr << " conference: " << args.conference << " session: " << args.session << " updating cloud pc: " << args.peerConnection);
               peerSess->mCloudPeerConnection = (CPCAPI2::PeerConnection::PeerConnectionHandle)args.peerConnection;
               break;
            }
         }
      }
   }

   return kSuccess;
}

int PushToTalkManagerImpl::onConferenceSessionMediaStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
   cpc::string localIdentStr = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "");
   InfoLog(<< "PushToTalkManagerImpl::onConferenceSessionMediaStatusChanged(): local identity: " << localIdentStr << " conference: " << args.conference << " session: " << args.session << " conf-connector handle: " << conn << " status-event: " << args);

   if (conn != mConfConnSession->mConfConnHdl)
   {
      InfoLog(<< "PushToTalkManagerImpl::onConferenceSessionMediaStatusChanged(): local-identity: " << localIdentStr << " connection handle mismatch: connection: " << conn << " local-connection: " << mConfConnSession->mConfConnHdl << " ignore conference session media status for conference: " << args.conference << " session: " << args.session);
      return kSuccess;
   }

#ifdef CPCAPI2_AUTO_TEST
   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      i->second->onConferenceSessionMediaStatusChanged(i->first, conn, args);
   }
#else
   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      PttReceiverWanSession* peerSess = dynamic_cast<PttReceiverWanSession*>(i->second);
      if (peerSess != NULL)
      {
         peerSess->onConferenceSessionMediaStatusChanged(i->first, conn, args);
         break;
      }
   }
#endif

   return kSuccess;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void PushToTalkManagerImpl::onSenderStarted()
{
   assert(mPhone->getSdkModuleThread().isCurrentThread());
   onSenderStartedImpl();
}

void PushToTalkManagerImpl::onSenderStartedImpl()
{
   DebugLog(<< "PushToTalkManagerImpl::onSenderStartedImpl(): " << this);
   // TODO: do we need to send service status update here
   if (mNetworkChangeInProgress)
   {
      mNetworkChangeInProgress = false;
   }
}

void PushToTalkManagerImpl::onSenderStopped()
{
   DebugLog(<< "PushToTalkManagerImpl::onSenderStopped(): " << this);
   assert(mPhone->getSdkModuleThread().isCurrentThread());
   onSenderStoppedImpl();
}

void PushToTalkManagerImpl::onSenderStoppedImpl()
{
   DebugLog(<< "PushToTalkManagerImpl::onSenderStoppedImpl(): " << this << " network-change in progress: " << mNetworkChangeInProgress);

   // Callback is LAN ptt specific, so no need to check for ptt service type. The network change in progress status
   // is only set if the new network is WIFI, so no need to check the network type of the network change flag is enabled
   if (mNetworkChangeInProgress)
   {
      UnicastTransport* unicastTransport = new UnicastTransport(shared_from_this());
      mTransport.reset(unicastTransport);
      mSocketHolder->clear();
      if (unicastTransport->init(mPhone, this, mSocketHolder, mServiceSettings, mInternalSettings) != 0)
      {
         DebugLog(<< "PushToTalkManagerImpl::onSenderStoppedImpl(): " << this << " failure initializing unicast transport");
         PttServiceStartedEvent startedEvt;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
         mNetworkChangeInProgress = false;
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, false);
         mTransport.reset();
         return;
      }

      if (mServiceSettings.unicastDiscoveryEnabled)
      {
         if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
         {
            std::stringstream ss;
            ss << PTT_SERVICE_REASON_NETWORK_CHANGE << network->networkTransport();
            DebugLog(<< "PushToTalkManagerImpl::onSenderStoppedImpl(): " << this << " restarting unicast discovery: " << ss.str().c_str());
            sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
            unicastTransport->startDiscovery(mService);
         }
         else
         {
            InfoLog(<< "PushToTalkManagerImpl::onSenderStoppedImpl(): " << this << " network change manager not initialized");
         }
      }
      else
      {
         PttServiceStartedEvent startedEvt;
         startedEvt.startupSuccessful = true;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

         sendServiceStatus(PttServiceStatusChangedEvent::Status_Connected);
         sendServiceStatus(PttServiceStatusChangedEvent::Status_Ready);
      }
   }
}

void PushToTalkManagerImpl::onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
}

void PushToTalkManagerImpl::onOutgoingSendCompletedImpl(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
}

void PushToTalkManagerImpl::onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length)
{
}

void PushToTalkManagerImpl::onOutgoingSendFailedImpl(const boost::asio::ip::udp::endpoint& target, const std::string& payload)
{
}

int PushToTalkManagerImpl::onIncoming(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen)
{
   std::string pl(payload, payloadLen);
   try
   {
      mPhone->getSdkModuleThread().post(resip::resip_bind(&PushToTalkManagerImpl::onIncomingImpl, shared_from_this(), ipAddr, port, pl));
   }
   catch (std::bad_weak_ptr& e)
   {
      InfoLog(<< "PushToTalkManagerImpl::onIncoming(): " << this << " ignore message, might be going through shutdown: " << e.what());
   }
   return 0;
}

void PushToTalkManagerImpl::onIncomingImpl(const std::string& ipAddr, unsigned int port, const std::string& payload)
{
   mThreadCheck.test();

   if (mNetworkChangeInProgress)
   {
      InfoLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): " << this << " service: " << mService << " ignoring request as network change handling is in progress");
      return;
   }

   if (payload.compare("\r\n\r\n") == 0)
      return;

   // StackLog(<< "PushToTalkManagerImpl::onIncomingUnicast(): " << this << " service: " << mService << " from endpoint: " << ipAddr << ":" << port << " payload: " << payload);

   resip::Tuple incomingTuple(ipAddr.c_str(), port, resip::UDP);
   std::stringstream callerAddressStr;
   callerAddressStr << incomingTuple.presentationFormat().c_str() << ":" << incomingTuple.getPort();
   std::string callerAddress = callerAddressStr.str().c_str();

   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mServiceSettings.unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif
   std::stringstream localAddressStr;
   localAddressStr << localIpAddress.c_str() << ":" << mServiceSettings.unicastPort;
   std::string localAddress = localAddressStr.str().c_str();

   StackLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): " << this << " service: " << mService << " local address: " << localAddress << " from endpoint: " << ipAddr << ":" << port << " payload: " << payload);

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(payload.c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): service: " << mService << " Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      return;
   }

   if (!jsonRequest->HasMember("functionName"))
   {
      WarningLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): service: " << mService << " Missing function name. Aborting decode.");
      return;
   }

   const rapidjson::Value& functionNameVal = (*jsonRequest)["functionName"];
   if (!functionNameVal.IsString())
   {
      WarningLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): service: " << mService << " function name is not a string. Aborting decode.");
      return;
   }

   resip::Data functionNameStr(resip::Data::Share, functionNameVal.GetString(), functionNameVal.GetStringLength());

   if (std::string(localIpAddress.c_str()).compare(incomingTuple.presentationFormat().c_str()) == 0)
   {
      InfoLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): " << this << " service: " << mService << " dropping incoming " << functionNameStr << " message as it was generated locally on address: " << localAddress);
      return;
   }

   if (resip::isEqualNoCase(functionNameStr, "onQueryEndpointsRequest") && jsonRequest->HasMember("QueryEndpointsRequest") && ((*jsonRequest)["QueryEndpointsRequest"].IsObject()))
   {
      PttQueryEndpointsRequest qeReq;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["QueryEndpointsRequest"];
      Json::Deserialize(eventObjVal, qeReq);
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         unicastTransport->onQueryEndpointsRequest(qeReq, incomingTuple);
      }
   }
   else if (resip::isEqualNoCase(functionNameStr, "onQueryEndpointsResponse") && jsonRequest->HasMember("QueryEndpointsResponse") && ((*jsonRequest)["QueryEndpointsResponse"].IsObject()))
   {
      PttQueryEndpointsResponse queryResponseEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["QueryEndpointsResponse"];
      Json::Deserialize(eventObjVal, queryResponseEvt);
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         unicastTransport->onQueryEndpointsResponse(queryResponseEvt, incomingTuple);
      }
   }
   else if (resip::isEqualNoCase(functionNameStr, "onPttReportEndpointsEvent") && jsonRequest->HasMember("PttReportEndpointsEvent") && ((*jsonRequest)["PttReportEndpointsEvent"].IsObject()))
   {
      PttReportEndpointsEvent reportEndpointsEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["PttReportEndpointsEvent"];
      Json::Deserialize(eventObjVal, reportEndpointsEvt);
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         unicastTransport->onReportEndpointsEvent(reportEndpointsEvt, incomingTuple);
      }
   }
   else if (resip::isEqualNoCase(functionNameStr, "onInitiatePtt") && jsonRequest->HasMember("PttInitiateEvent") && ((*jsonRequest)["PttInitiateEvent"].IsObject()))
   {
      PttInitiateEvent initiateEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["PttInitiateEvent"];
      Json::Deserialize(eventObjVal, initiateEvt);
      onPttInitiateEvent(initiateEvt, incomingTuple);
   }
   else if (resip::isEqualNoCase(functionNameStr, "onEndPtt") && jsonRequest->HasMember("PttEndEvent") && ((*jsonRequest)["PttEndEvent"].IsObject()))
   {
      PttEndEvent endEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["PttEndEvent"];
      Json::Deserialize(eventObjVal, endEvt);
      onPttEndEvent(endEvt, incomingTuple);
   }
   else if (resip::isEqualNoCase(functionNameStr, "onClientOffer") && jsonRequest->HasMember("ClientOfferEvent") && ((*jsonRequest)["ClientOfferEvent"].IsObject()))
   {
      PttClientOfferEvent clientoOfferEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["ClientOfferEvent"];
      Json::Deserialize(eventObjVal, clientoOfferEvt);
      onClientOfferEvent(clientoOfferEvt, incomingTuple);
   }
   else if (resip::isEqualNoCase(functionNameStr, "PttReceiverEnded") && jsonRequest->HasMember("PttReceiverEndedEvent") && ((*jsonRequest)["PttReceiverEndedEvent"].IsObject()))
   {
      PttReceiverEndedEvent receiverEndedEvt;
      const rapidjson::Value& eventObjVal = (*jsonRequest)["PttReceiverEndedEvent"];
      Json::Deserialize(eventObjVal, receiverEndedEvt);
      onPttReceiverEndedEvent(receiverEndedEvt, incomingTuple);
   }
   else
   {
      WarningLog(<< "PushToTalkManagerImpl::onIncomingUnicastImpl(): function: " << functionNameStr.c_str() << " service: " << mService << " not supported or invalid. Aborting decode.");
   }
}

void PushToTalkManagerImpl::onPttInitiateEvent(const PttInitiateEvent& evt, const resip::Tuple& incomingTuple)
{
   std::stringstream callerAddressStr;
   callerAddressStr << incomingTuple.presentationFormat().c_str() << ":" << incomingTuple.getPort();
   std::string callerAddress = callerAddressStr.str().c_str();
   bool recipient = (evt.receiverIdentity.size() > 0);

   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mServiceSettings.unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif
   std::stringstream localAddressStr;
   localAddressStr << localIpAddress.c_str() << ":" << mServiceSettings.unicastPort;
   std::string localAddress = localAddressStr.str().c_str();

   if (std::string(localIpAddress.c_str()).compare(incomingTuple.presentationFormat().c_str()) == 0)
   {
      InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " dropping incoming ptt initiate-event request as it was generated locally on local-address: " << localAddress << " service: " << mService << " remote-address: " << callerAddress);
      return;
   }
#ifdef CPCAPI2_AUTO_TEST
   if (mInternalSettings.ignoreIncomingInitiate)
   {
      StackLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " dropping incoming ptt initiate-event request as handling has been disabled on local-address: " << localAddress << " service: " << mService << " remote-address: " << callerAddress);
      return;
   }
#endif

   if (recipient)
   {
      bool found = false;
      for (cpc::vector<PttIdentity>::const_iterator i = evt.receiverIdentity.begin(); i != evt.receiverIdentity.end(); ++i)
      {
         if (!mServiceSettings.localIdentities.empty())
         {
            for (cpc::vector<PttIdentity>::const_iterator il = mServiceSettings.localIdentities.begin(); il != mServiceSettings.localIdentities.end(); ++il)
            {
               if (resip::isEqualNoCase((*i).userName.c_str(), il->userName.c_str()) && ((*i).identityType == il->identityType))
               {
                  found = true;
                  break;
               }
            }
            if (found)
            {
               break;
            }
         }
         else
         {
            if (resip::isEqualNoCase((*i).userName.c_str(), mServiceSettings.senderIdentity.userName.c_str()) && ((*i).identityType == mServiceSettings.senderIdentity.identityType))
            {
               found = true;
               break;
            }
         }
      }

      if (!found)
      {
         InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " dropping incoming ptt initiate-event request as this receiver was not the intended target, local-address: " << localAddress << " service: " << mService << " remote-address: " << callerAddress);
#ifdef CPCAPI2_AUTO_TEST
         PttSessionErrorEvent errorEvt;
         errorEvt.errorCode = PttSessionError_UnicastDiscard;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, mService, errorEvt);
#endif
         return;
      }
   }

   bool ignorePtt = false;
   bool staleSessionId = std::find(mStaleSessionIds.begin(), mStaleSessionIds.end(), evt.sessionId) != mStaleSessionIds.end();
   bool retransmission = false;
   if (staleSessionId)
   {
      DebugLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " dropping incoming ptt initiate-event request as it is from an already terminated session, incoming ptt: "
         << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " sessionId: " << evt.sessionId
         << " local-address: " << localAddress << " service: " << mService << " remote-address: " << callerAddress);
      ignorePtt = true;
   }
   else
   {
      std::vector<PttSession*> destroySessions;
      for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
      {
         // Even if the tid sequence based on time_t may not be synced across all devices in the network, using
         // tid as the filter will ensure consistency across all the devices in selecting the winning call
         PttReceiverLanSession* session = dynamic_cast<PttReceiverLanSession*>(i->second);
         if (!session) continue;
         bool sameSessionId = (evt.sessionId == session->getSessionId());
         bool sameChannel = (evt.channelId.empty() ? false : resip::isEqualNoCase(evt.channelId.c_str(), session->getChannelId().c_str()));
         PttSessionErrorEvent errorEvt;
         errorEvt.service = mService;
         errorEvt.callerIdentity = evt.senderIdentity;
         errorEvt.channelId = evt.channelId.c_str();

         if (sameSessionId)
         {
            StackLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " ignore initiatePtt request as it is a retransmission, ptt: " << i->first
               << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " local-address: " << localAddress
               << " service: " << mService << " remote-address: " << callerAddress << " sessionId: " << evt.sessionId);
            ignorePtt = true;
            retransmission = true;
         }
         else if (sameChannel)
         {
            if (session->mRemoteEndpoint.isEqualWithMask(incomingTuple, 32, true, true))
            {
               // we already have a PTT from this person, but now they're initiating another one with us???
               // we must have missed something ...
               // tear down the old, and accept the new ...
               InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " destroy existing call (tid:" << session->getTransactionId()
                  << ") on the same channel as it is from the same PTT sender, ptt: " << i->first
                  << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId
                  << " callerAddress: " << callerAddress << " sessionId: " << evt.sessionId << " session remote-endpoint: " << session->mRemoteEndpoint.presentationFormat().c_str());
               errorEvt.errorCode = PttSessionError_ChannelOverride;
               mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, i->first, errorEvt);
               destroySessions.push_back(session);
            }
            else
            {
               auto connectDuration = (std::chrono::system_clock::now() - session->mStartTime);
               if (connectDuration >= std::chrono::milliseconds(mInternalSettings.channelOverrideDurationMs))
               {
                  InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " keep existing call (tid:" << session->getTransactionId()
                     << ") on the same channel and ignore initiatePtt request as existing call has been connected for at least 1 second, ptt: " << i->first
                     << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " local-address: " << localAddress
                     << " service: " << mService << " remote-address: " << callerAddress << " sessionId: " << evt.sessionId);
                  ignorePtt = true;
#ifdef CPCAPI2_AUTO_TEST
                  errorEvt.errorCode = PttSessionError_UnicastDiscard;
                  mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, mService, errorEvt);
#endif
               }
               else
               {
                  if (keepCurrentSession(session, incomingTuple.presentationFormat().c_str(), incomingTuple.getPort()))
                  {
                     // current session tuple >= new incoming tuple
                     InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " keep existing call (tid:" << session->getTransactionId()
                        << ") on the same channel and ignore initiatePtt request as it has larger ip address, ptt: " << i->first
                        << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId
                        << " callerAddress: " << callerAddress << " sessionId: " << evt.sessionId << " session remote-endpoint: " << session->mRemoteEndpoint.presentationFormat().c_str());
                     ignorePtt = true;
#ifdef CPCAPI2_AUTO_TEST
                     errorEvt.errorCode = PttSessionError_UnicastDiscard;
                     mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, mService, errorEvt);
#endif
                  }
                  else
                  {
                     // current session tuple < new incoming tuple
                     InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " destroy existing call (tid:" << session->getTransactionId()
                        << ") on the same channel as it has a lower ip address, ptt: " << i->first
                        << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId
                        << " callerAddress: " << callerAddress << " sessionId: " << evt.sessionId << " session remote-endpoint: " << session->mRemoteEndpoint.presentationFormat().c_str());
                     errorEvt.errorCode = PttSessionError_ChannelOverride;
                     mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, i->first, errorEvt);
                     destroySessions.push_back(session);
                  }
               }
            }
         }
      }

      for (std::vector<PttSession*>::iterator j = destroySessions.begin(); j != destroySessions.end(); ++j)
      {
         PttReceiverLanSession* session = dynamic_cast<PttReceiverLanSession*>(*j);
         if (session)
         {
            PttEndEvent endEvt;
            endEvt.pttHandle = session->mIncomingPtt;
            endEvt.sessionId = session->getSessionId();
            endEvt.transactionId = session->getTransactionId();
            endEvt.channelId = session->getChannelId();
            endEvt.senderIpAddress = incomingTuple.presentationFormat().c_str();
            endEvt.senderIpPort = incomingTuple.getPort();

            if (session->getPttSessionConnectedCallCount() > 0)
               session->sendPttReceiverEnded(PushToTalk::PttReceiverEndReasonType_ChannelOverride);

            session->onPttEndEvent(session->getPtt(), endEvt, incomingTuple);
         }
      }
   }

   if (ignorePtt)
   {
      if (!(staleSessionId || retransmission))
      {
         // Add to stale list to ignore future retransmissions
         cpc::string sessionId = evt.sessionId;
         updateStaleSessionList(sessionId);

         DebugLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " dropping incoming ptt initiate-event request, local-address: "
            << localAddress << " service: " << mService << " session-count: " << mSessionList.size() << " remote-address: " << callerAddress);
      }
      return;
   }

   bool outgoingInProgress = false;
   uint32_t outgoingInProgressPtt = 0;
   for (PttSessionList::iterator k = mSessionList.begin(); k != mSessionList.end(); ++k)
   {
      PttSenderLanSession* session = dynamic_cast<PttSenderLanSession*>(k->second);
      if (session)
      {
         bool sameChannel = (evt.channelId.empty() ? false : resip::isEqualNoCase(evt.channelId.c_str(), session->getChannelId().c_str()));
         if (session->mState == PttSessionState_Initiated)
         {
            InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " call-override for incoming call as an outbound call is being setup, ptt: " << session->getPtt()
               << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " callerAddress: " << callerAddress << " sessionId: " << evt.sessionId);
            outgoingInProgress = true;
            outgoingInProgressPtt = session->getPtt();
            break;
         }
         else if (sameChannel)
         {
            // If this outgoing session is for the same-channel as this incoming-ptt, then all receivers would end the call due to channel-override
            // for the call that lost the call competition and the outgoing session would be destroyed once all the receivers have cleaned-up. If this
            // outgoing session is the one that wins the ptt call competition then there will not be any impact to this current session.
            InfoLog(<< "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " call-override for incoming call as an outbound call exists on same channel, ptt: " << session->getPtt()
               << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " callerAddress: " << callerAddress << " sessionId: " << evt.sessionId);
            outgoingInProgress = true;
            outgoingInProgressPtt = session->getPtt();
            break;
         }
      }
   }

   if (outgoingInProgress)
   {
      // Add to stale list to ignore future retransmissions
      cpc::string sessionId = evt.sessionId;
      updateStaleSessionList(sessionId);

      PttSessionErrorEvent errorEvt;
      errorEvt.service = mService;
      errorEvt.errorCode = PttSessionError_CallOverride;
      errorEvt.callerIdentity = evt.senderIdentity;
      errorEvt.channelId = evt.channelId;

      mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, outgoingInProgressPtt, errorEvt);
      return;
   }

   PttReceiverLanSession* session = static_cast<PttReceiverLanSession*>(mReceiverSessionFactory->create(mPttMgr->createPttSessionInternal(mService)));
   mSessionList[session->getPtt()] = session;

   session->onPttInitiateEvent(session->getPtt(), evt, incomingTuple);

   StatLog(PTT_LOGID_SESSION_RX_LAN_INIT, << "PushToTalkManagerImpl::onPttInitiateEvent(): " << this << " created new ptt: " << session->getPtt() << " session to handle initiatePtt request, incoming ptt: "
      << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " callerAddress: " << callerAddress
      << " sessionId: " << session->getSessionId());
}

void PushToTalkManagerImpl::onPttEndEvent(const PttEndEvent& evt, const resip::Tuple& incomingTuple)
{
   bool staleSessionId = std::find(mStaleSessionIds.begin(), mStaleSessionIds.end(), evt.sessionId) != mStaleSessionIds.end();
   if (staleSessionId)
   {
      DebugLog(<< "PushToTalkManagerImpl::onPttEndEvent(): " << this << " service: " << mService << " dropping incoming ptt end-event request as it is from an already terminated session, incoming ptt: "
         << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " senderIpAddress: " << evt.senderIpAddress << " sessionId: " << evt.sessionId);
      return;
   }

   std::stringstream callerAddressStr;
   callerAddressStr << incomingTuple.presentationFormat().c_str() << ":" << incomingTuple.getPort();
   std::string callerAddress = callerAddressStr.str().c_str();

   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mServiceSettings.unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif
   std::stringstream localAddressStr;
   localAddressStr << localIpAddress.c_str() << ":" << mServiceSettings.unicastPort;
   std::string localAddress = localAddressStr.str().c_str();
   if (localAddress.compare(callerAddress) == 0)
   {
      InfoLog(<< "PushToTalkManagerImpl::onPttEndEvent(): " << this << " dropping incoming ptt end-event request as it was generated locally on address: " << localAddress << " service: " << mService);
      return;
   }

   std::vector<PttSession*> destroySessions;
   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      PttSession* session = i->second;
      if (evt.sessionId == session->getSessionId())
      {
         DebugLog(<< "PushToTalkManagerImpl::onPttEndEvent(): " << this << " destroy current session to handle endPtt request on channel, ptt: "
            << i->first << " incoming ptt: " << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " sessionId: " << evt.sessionId);
         destroySessions.push_back(session);
      }
   }

   if (destroySessions.size() == 0)
   {
      StackLog(<< "PushToTalkManagerImpl::onPttEndEvent(): " << this << " service: " << mService << " ignore endPtt request as there is no associated ptt session, incoming ptt: "
         << evt.pttHandle << " channelId: " << evt.channelId << " tid: " << evt.transactionId << " callerAddress: " << callerAddress);
   }
   else
   {
      for (std::vector<PttSession*>::iterator j = destroySessions.begin(); j != destroySessions.end(); ++j)
      {
         PttReceiverLanSession* session = dynamic_cast<PttReceiverLanSession*>(*j);
         if (session)
         {
            PttEndEvent endEvt;
            endEvt.pttHandle = session->mIncomingPtt;
            endEvt.sessionId = session->getSessionId();
            endEvt.transactionId = session->getTransactionId();
            endEvt.channelId = session->getChannelId();
            endEvt.senderIpAddress = incomingTuple.presentationFormat().c_str();
            endEvt.senderIpPort = incomingTuple.getPort();
            session->onPttEndEvent(session->getPtt(), endEvt, incomingTuple);
         }
      }
   }
}

void PushToTalkManagerImpl::onClientOfferEvent(const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSessionList::iterator i = mSessionList.find(evt.pttHandle);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::onClientOfferEvent(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << evt.pttHandle << " session-id: " << evt.sessionId << " caller-address: " << incomingTuple);
      return;
   }

   i->second->onClientOfferEvent(evt.pttHandle, evt, incomingTuple);
}

void PushToTalkManagerImpl::onPttReceiverEndedEvent(const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple)
{
   bool staleTransactionId = isStaleReceiverEndedTransaction(evt);
   if (staleTransactionId)
   {
      DebugLog(<< "PushToTalkManagerImpl::onPttReceiverEndedEvent(): " << this << " service: " << mService << " dropping incoming ptt receiver-end event as it has already been handled, incoming ptt: "
         << evt.pttHandle << " tid: " << evt.transactionId << " sessionId: " << evt.sessionId << " remote-address: " << incomingTuple);
      return;
   }

   CPCAPI2::PushToTalk::PttSession* session = getPttSessionForSessionId(evt.sessionId);
   if (!session)
   {
      InfoLog(<< "PushToTalkManagerImpl::onPttReceiverEndedEvent(): " << this << " invalid session-id - service: " << mService << " session-id: " << evt.sessionId << " caller-address: " << incomingTuple);
      return;
   }

   session->onPttReceiverEndedEvent(session->mPtt, evt, incomingTuple);
}

void PushToTalkManagerImpl::initiateRecording(PushToTalkSessionHandle pttSession)
{
   mPhone->getSdkModuleThread().postMS(resip::resip_bind(&PushToTalkManagerImpl::initiateRecordingImpl, shared_from_this(), pttSession), 20);
}

void PushToTalkManagerImpl::initiateRecordingImpl(PushToTalkSessionHandle pttSession)
{
   PttSessionList::iterator i = mSessionList.find(pttSession);
   if ((i == mSessionList.end()) || (i->second == NULL))
   {
      InfoLog(<< "PushToTalkManagerImpl::initiateRecordingImpl(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << pttSession);
      return;
   }

   PttSenderInitiateRecordingEvent evt;
   evt.pttHandle = pttSession;
   i->second->onPttSenderInitiateRecordingEvent(evt.pttHandle, evt);
}

bool PushToTalkManagerImpl::keepCurrentSession(PttSession* currentSession, const std::string& ipAddr, unsigned int port)
{
   PttReceiverLanSession* session = dynamic_cast<PttReceiverLanSession*>(currentSession);
   if (session)
   {
      resip::Tuple currentTuple = session->mRemoteEndpoint;
      resip::Tuple newTuple(ipAddr.c_str(), port, resip::UDP);
      if (currentTuple < newTuple)
      {
         return false;
      }
      return true;
   }

   return false;
}

void PushToTalkManagerImpl::setCodecMediaInfo(CPCAPI2::PeerConnection::MediaInfo& mediaInfo) const
{
   PeerConnection::MediaCodec opus;
   opus.codecPayloadName = "opus";
   opus.codecFrequency = 48000;
   mediaInfo.codecs.clear();
   mediaInfo.codecs.push_back(opus);
}

std::vector<CPCAPI2::PushToTalk::PushToTalkSessionHandle> PushToTalkManagerImpl::getPttSessionList()
{
   std::vector<CPCAPI2::PushToTalk::PushToTalkSessionHandle> sessions;

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      sessions.push_back(i->first);
   }

   return sessions;
}

PttSession* PushToTalkManagerImpl::getPttSessionForCallId(uint32_t call)
{
   PttSession* session = NULL;

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      if (i->second->doesCallExist(call))
      {
         StackLog(<< "PushToTalkManagerImpl::getPttSessionForCallId(): " << this << " call: " << call << " found in ptt: " << i->first << " for service: " << mService);
         session = i->second;
         break;
      }
   }

   if (!session)
   {
      DebugLog(<< "PushToTalkManagerImpl::getPttSessionForCallId(): " << this << " no session found for call: " << call << " for service: " << mService);
   }
   return session;
}

PttSession* PushToTalkManagerImpl::getPttSessionForSessionId(const cpc::string& sessionId)
{
   PttSession* session = NULL;

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      if ((i->second->getSessionId()) == sessionId)
      {
         StackLog(<< "PushToTalkManagerImpl::getPttSessionForSessionId(): " << this << " ptt: " << i->first << " found for sessionId: " << sessionId << " for service: " << mService);
         session = i->second;
         break;
      }
   }

   if (!session)
   {
      DebugLog(<< "PushToTalkManagerImpl::getPttSessionForSessionId(): " << this << " no session found for sessionId: " << sessionId << " for service: " << mService);
   }
   return session;
}

PttSession* PushToTalkManagerImpl::getPttSessionForPeerConnection(CPCAPI2::PeerConnection::PeerConnectionHandle pc)
{
   PttSession* session = NULL;
   PttSenderWanSession* senderSession = NULL;
   PttReceiverWanSession* receiverSession = NULL;
   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      senderSession = dynamic_cast<PttSenderWanSession*>(i->second);
      receiverSession = dynamic_cast<PttReceiverWanSession*>(i->second);
      if (senderSession && (senderSession->mCloudPeerConnection == pc))
      {
         StackLog(<< "PushToTalkManagerImpl::getPttSessionForPeerConnection(): " << this << " pc: " << pc << " found in ptt: " << i->first << " for service: " << mService);
         session = senderSession;
         break;
      }
      if (receiverSession && (receiverSession->mCloudPeerConnection == pc))
      {
         StackLog(<< "PushToTalkManagerImpl::getPttSessionForPeerConnection(): " << this << " pc: " << pc << " found in ptt: " << i->first << " for service: " << mService);
         session = receiverSession;
         break;
      }
   }

   if (!session)
   {
      DebugLog(<< "PushToTalkManagerImpl::getPttSessionForPeerConnection(): " << this << " no session found for pc: " << pc << " for service: " << mService);
   }
   return session;
}

CPCAPI2::PushToTalk::PushToTalkSessionHandle PushToTalkManagerImpl::getPttHandleForCall(uint32_t call)
{
   CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt = 0;

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      if (i->second->doesCallExist(call))
      {
         StackLog(<< "PushToTalkManagerImpl::getPttHandleForCall(): " << this << " call: " << call << " found in ptt: " << i->first << " for service: " << mService);
         ptt = i->first;
         break;
      }
   }

   if (ptt == 0)
   {
      DebugLog(<< "PushToTalkManagerImpl::getPttHandleForCall(): " << this << " call: " << call << " not found in any ptt sessions for service: " << mService);
   }
   return ptt;
}

resip::Data PushToTalkManagerImpl::guessLocalIpAddress() const
{
   resip::Data srcIp;
#ifdef CPCAPI2_AUTO_TEST
   srcIp = mServiceSettings.unicastBindAddress.c_str();
#else
   resip::Tuple googDns("*******", 53, resip::UDP);
   IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
#endif

   return srcIp;
}

std::string PushToTalkManagerImpl::getLocalIdentity() const
{
   std::string identity = ((mServiceSettings.localIdentities.size() > 0) ? mServiceSettings.localIdentities.begin()->userName : "").c_str();
   return identity;
}

void PushToTalkManagerImpl::sendToWire(const std::string& payload, bool enableHandler, bool updateParams, const std::string& targetIpAddress, bool noBurst, unsigned int targetPort, unsigned int messageId, bool highPriority)
{
   StackLog(<< "PushToTalkManagerImpl::sendToWire(): " << this << " service: " << mService << " send message-id: " << messageId << " payload to target: " << targetIpAddress << ":" << targetPort);

   try
   {
      if (!mTransport)
      {
         InfoLog(<< "PushToTalkManagerImpl::sendToWire(): " << this << " service: " << mService << " ignoring send requests as the transport is not initialized");
         return;
      }

      if ((mServiceSettings.unicastPort > 0) && mInternalSettings.unicastTransmitEnabled)
      {
         if (!targetIpAddress.empty() && targetPort > 0)
         {
            DebugLog(<< "PushToTalkManagerImpl::sendToWire(): " << this << " service: " << mService << " send payload to specified target: " << targetIpAddress << ":" << targetPort);
            Endpoint ep;
            memset(ep.transportSpecific.unicast.targetIpAddress, 0, sizeof(ep.transportSpecific.unicast.targetIpAddress));
            memcpy(ep.transportSpecific.unicast.targetIpAddress, targetIpAddress.data(), targetIpAddress.size());
            ep.transportSpecific.unicast.targetPort = targetPort;
            mTransport->sendTo(ep, payload, highPriority, noBurst, messageId, enableHandler);
         }
         else
         {
            mTransport->broadcast(payload, highPriority, noBurst, messageId, enableHandler);
         }
      }
   }
   catch (const boost::system::system_error& ex)
   {
      // ex.what() reporting "Invalid argument" can be a sign of no network being available

      // TODO: should we actually log this? what if it results in log spam?
      WarningLog(<< "PushToTalkManagerImpl::sendToWire: " << this << " threw exception " << ex.what());
   }
}

CPCAPI2::ConferenceConnector::CloudConferenceHandle PushToTalkManagerImpl::getCloudConference(std::string& identity)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::getCloudConference(): conference manager not initialized");
      return 0;
   }

   return (mConfConnSession->getCloudConference(identity));
}

CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle PushToTalkManagerImpl::startConfSessionWith(const cpc::string& ident, PushToTalkSessionHandle pttSession, PttIdentityType identityType, CPCAPI2::ConferenceConnector::CloudConferenceHandle& conf)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::startConfSessionWith(): conference manager not initialized, ptt: " << pttSession << " conference: " << conf);
      return 0;
   }

   mMySession = mConfConnSession->startConfSessionWith(ident, pttSession, identityType, conf);
   StatLog(PTT_LOGID_SESSION_RX_WAN_START_CONFERENCE, << "PushToTalkManagerImpl::startConfSessionWith(): service: " << mService << " identity: " << ident.c_str() << " identity-type: " << identityType << " conference: " << conf << " session: " << mMySession);
   return mMySession;
}

CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle PushToTalkManagerImpl::startConfSessionWithMyConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conf)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::startConfSessionWithMyConference(): conference manager not initialized, conference: " << conf);
      return 0;
   }
   mMySession = mConfConnSession->startConfSessionWithMyConference(conf);
   StatLog(PTT_LOGID_SESSION_RX_WAN_START_CONFERENCE_WITH, << "PushToTalkManagerImpl::startConfSessionWithMyConference(): service: " << mService << " conference: " << conf << " session: " << mMySession);
   return mMySession;
}

void PushToTalkManagerImpl::endConfSession(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::endConfSession(): conference manager not initialized, session: " << session);
      return;
   }
   mConfConnSession->endConfSession(session);
   mMySession = 0;
}

void PushToTalkManagerImpl::doConferenceSessionMute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::doConferenceSessionMute(): conference manager not initialized, session: " << session);
      return;
   }
   mConfConnSession->doConferenceSessionMute(session);
}

void PushToTalkManagerImpl::doConferenceSessionUnmute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   if (!mConfConnMgr)
   {
      StackLog(<< "PushToTalkManagerImpl::doConferenceSessionUnmute(): conference manager not initialized, session: " << session);
      return;
   }
   mConfConnSession->doConferenceSessionUnmute(session);
}

int PushToTalkManagerImpl::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   StatLog(PTT_LOGID_NETWORK_CHANGE, << "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " transport: " << mTransport << " network: " << args.networkTransport);

   if (mServiceSettings.serviceType == PttServiceType_WAN)
   {
      std::vector<PushToTalkSessionHandle> sessions = getPttSessionList();
      for (std::vector<PushToTalkSessionHandle>::iterator i = sessions.begin(); i != sessions.end(); ++i)
      {
         PttSessionList::iterator j = mSessionList.find(*i);
         j->second->end(*i, PTT_SESSION_END_REASON_NETWORK_CHANGE);
      }

      if (!mConfConnMgr)
      {
         InfoLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " ignore network change as conference manager is not initialized");
         return kError;
      }

      mConfConnSession->onNetworkChange(args);
   }
   else
   {
      handleNetworkChangeLAN(args);
   }

   return kSuccess;
}

int PushToTalkManagerImpl::handleNetworkChangeLAN(const CPCAPI2::NetworkChangeEvent& args)
{
   resip::Data localIpAddress = guessLocalIpAddress();
   bool handleNetworkChange = true;
   if ((args.networkTransport == TransportNone) || localIpAddress.empty())
   {
      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " local-ip: " << localIpAddress.c_str() << " ignoring network change to network: " << args.networkTransport);
      handleNetworkChange = false;
   }
   else
   {
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         if (unicastTransport->getLocalIp().compare(localIpAddress.c_str()) == 0)
         {
            DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " local-ip: " << localIpAddress.c_str() << " ignoring network change to network: " << args.networkTransport << " as the ip address has not changed");
            handleNetworkChange = false;
         }
      }
   }

   if (!handleNetworkChange)
   {
      std::vector<PushToTalkSessionHandle> sessions = getPttSessionList();
      for (std::vector<PushToTalkSessionHandle>::iterator i = sessions.begin(); i != sessions.end(); ++i)
      {
         PttSessionList::iterator j = mSessionList.find(*i);
         if (j != mSessionList.end())
         {
            // Media inactivity checks will continue
            if (args.networkTransport == TransportWiFi)
            {
               // j->second->enableIncomingMediaInactivityMonitor();
               j->second->enableWifiStatusMonitor();
            }
            else
            {
               // j->second->disableIncomingMediaInactivityMonitor();
               j->second->disableWifiStatusMonitor();
            }
         }
      }
      return kSuccess;
   }

   std::vector<PushToTalkSessionHandle> sessions = getPttSessionList();
   for (std::vector<PushToTalkSessionHandle>::iterator i = sessions.begin(); i != sessions.end(); ++i)
   {
      PttSessionList::iterator j = mSessionList.find(*i);
      if (j != mSessionList.end())
      {
         j->second->end(*i, PTT_SESSION_END_REASON_NETWORK_CHANGE);
      }
   }

   //PttServiceStatusChangedEvent::Status status = mServiceStatus;
   std::stringstream ss;
   ss << PTT_SERVICE_REASON_NETWORK_CHANGE << args.networkTransport;
   DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " local-ip: " << localIpAddress.c_str() << " network: " << args.networkTransport << " send disconnected status: " << ss.str().c_str());
   sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnecting, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());

   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " update existing unicast transport about network change");
      unicastTransport->onNetworkChange(args);
   }

   if (args.networkTransport != TransportWiFi)
   {
      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network transport is not wifi (is " << args.networkTransport << "); firing onPttServicedStarted with startupSuccessful=false");
      if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
      {
         DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " shutdown existing unicast transport due to invalid transport");
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_NetworkChange);
         mTransport.reset();
      }

      PttServiceStartedEvent startedEvt;
      mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

      // sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_WIFI_NOT_AVAILABLE);
      return kSuccess;
   }

   // If transport already exists, need to disable it, and wait for disabled status,
   // before proceeding to open another transport, so cannot start it right away.
   bool waitForTransportShutdown = (mTransport ? true : false);
   mNetworkChangeInProgress = true;
   DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " transport: " << mTransport << " network: " << args.networkTransport << " wait for transport shutdown: " << waitForTransportShutdown);
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " shutdown existing unicast transport before handling network change");
      unicastTransport->shutdown();
      // Wait to receive the transport shutdown callback before destroying transport
      // mTransport.reset();
   }

   if (waitForTransportShutdown)
   {
      sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
   }

   // TODO: Is this necessary to update the existing sessions, if they are going to time-out as incoming-outgoing traffic may be dropped

   // Update local ip address on existing sessions
   resip::Tuple localEndpoint = resip::Tuple(localIpAddress.c_str(), getServiceSettings().unicastPort, resip::UDP);

   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      i->second->mLocalEndpoint = localEndpoint;
   }

   if (mServiceSettings.unicastDiscoveryEnabled && (waitForTransportShutdown == false))
   {
      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " create new unicast transport to handle network change to: " << localEndpoint);
      UnicastTransport* unicastTransport = new UnicastTransport(shared_from_this());
      mTransport.reset(unicastTransport);
      mSocketHolder->clear();
      if (unicastTransport->init(mPhone, this, mSocketHolder, mServiceSettings, mInternalSettings) != 0)
      {
         DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " failure initializing unicast transport: " << localEndpoint);
         PttServiceStartedEvent startedEvt;
         mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
         mNetworkChangeInProgress = false;
         unicastTransport->shutdown(PttServiceStatusChangedEvent::Reason_None, false);
         mTransport.reset();
         return kSuccess;
      }

      DebugLog(<< "PushToTalkManagerImpl::onNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " restarting unicast discovery: " << ss.str().c_str());
      sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
      unicastTransport->startDiscovery(mService);
   }
   else if (!mServiceSettings.unicastDiscoveryEnabled)
   {
      PttServiceStartedEvent startedEvt;
      startedEvt.startupSuccessful = true;
      mPttMgr->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

      sendServiceStatus(PttServiceStatusChangedEvent::Status_Connected);
      sendServiceStatus(PttServiceStatusChangedEvent::Status_Ready);
      // mNetworkChangeInProgress = false;
   }

   return kSuccess;
}

void PushToTalkManagerImpl::logWifiStatus()
{
#ifdef __ANDROID__
   if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
   {
      if (network->networkTransport() == TransportWiFi)
      {
         int wifiRssi = 0xFFFFF, wifiSignalLevel = -1, wifiMaxSignalLevel = -1, wifiFreqMhz = -1, wifiChannel = -1, wifiLinkSpeedMbps = -1;
         wifiRssi = network->currentWifiRssi();
         wifiSignalLevel = network->currentWifiSignalLevel();
         wifiMaxSignalLevel = network->maxWifiSignalLevel();
         wifiFreqMhz = network->currentWifiFreqMhz();
         wifiChannel = network->currentWifiChannel();
         wifiLinkSpeedMbps = network->currentWifiLinkSpeedMbps();
         std::stringstream ss;
         ss << " (" << wifiSignalLevel << "/" << wifiMaxSignalLevel << ")";
         StatLog(PTT_LOGID_SERVICE_WIFI_STATUS, << "PushToTalkManagerImpl::logWifiStatus(): service: " << mService << " wifiRssi: " << wifiRssi << " wifiSignalLevel: " << wifiSignalLevel << " wifiMaxSignalLevel: " << wifiMaxSignalLevel << ss.str() << " wifiFreqMhz: " << wifiFreqMhz << " wifiChannel: " << wifiChannel << " wifiLinkSpeedMbps: " << wifiLinkSpeedMbps);
      }
      else
      {
         DebugLog(<< "PushToTalkManagerImpl::logWifiStatus(): service: " << mService << " ignoring request to log wifi due to mismatch of network: " << network->networkTransport());
      }
   }
#endif
}

void PushToTalkManagerImpl::updateStaleSessionList(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if (i == mSessionList.end())
   {
      InfoLog(<< "PushToTalkManagerImpl::updateStaleSessionList(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return;
   }

   updateStaleSessionList(i->second->getSessionId());
}

void PushToTalkManagerImpl::updateStaleSessionList(cpc::string& sessionId)
{
   mStaleSessionIds[mNextStaleSessionIdx++ % 32] = sessionId;
}

void PushToTalkManagerImpl::updateReceiverEndedStaleTransactionList(const PushToTalk::PttReceiverEndedEvent& evt)
{
   std::stringstream transactionId;
   transactionId << evt.pttHandle << evt.sessionId << evt.transactionId;
   mStaleReceiverEndedTransactionIds[mNextStaleReceiverEndedTransactionIdx++ % 32] = transactionId.str();
}

bool PushToTalkManagerImpl::isStaleReceiverEndedTransaction(const CPCAPI2::PushToTalk::PttReceiverEndedEvent& evt)
{
   std::stringstream transactionId;
   transactionId << evt.pttHandle << evt.sessionId << evt.transactionId;
   bool isStale = std::find(mStaleReceiverEndedTransactionIds.begin(), mStaleReceiverEndedTransactionIds.end(), transactionId.str()) != mStaleReceiverEndedTransactionIds.end();
   return isStale;
}

void PushToTalkManagerImpl::destroySession(PushToTalkSessionHandle ptt)
{
   PttSessionList::iterator i = mSessionList.find(ptt);
   if (i == mSessionList.end())
   {
      InfoLog(<< "PushToTalkManagerImpl::destroySession(): " << this << " invalid ptt handle - service: " << mService << " ptt: " << ptt);
      return;
   }

   delete (i->second);
   mSessionList.erase(ptt);
}

void PushToTalkManagerImpl::unsendAll(unsigned int messageId)
{
   // DebugLog(<< "PushToTalkManagerImpl::unsendAll(): " << this << " service: " << mService << " message-id: " << messageId);
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->unsendAll(messageId);
   }
}

void PushToTalkManagerImpl::unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target)
{
   // InfoLog(<< "PushToTalkManagerImpl::unsend(): " << this << " service: " << mService << " message-id: " << messageId << " target: " << target.address().to_string());
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->unsend(messageId, target);
   }
}

void PushToTalkManagerImpl::setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable)
{
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->setKeepAliveTarget(target, enable);
   }
}

const std::map<std::string, PttIdentity>& PushToTalkManagerImpl::getEndpointList()
{
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      return unicastTransport->getEndpointList();
   }
   static std::map<std::string, PttIdentity> emptyMap;
   return emptyMap;
}

void PushToTalkManagerImpl::addToDiscoveryList(PttIdentity& identity)
{
   if (std::shared_ptr<UnicastTransport> unicastTransport = std::dynamic_pointer_cast<UnicastTransport>(mTransport))
   {
      unicastTransport->addToDiscoveryList(identity);
   }
}

void PushToTalkManagerImpl::onDiscoveryListUpdate(const PttDiscoveryListUpdate& updateEvt)
{
   for (PttSessionList::iterator i = mSessionList.begin(); i != mSessionList.end(); ++i)
   {
      i->second->onDiscoveryListUpdate(updateEvt);
   }
}

int PushToTalkManagerImpl::compressEndpointList(const cpc::vector<cpc::string>& endpoints, cpc::string& compressedList)
{
   std::string endpointsCommaDelim;
   for (auto iteps = endpoints.begin(); iteps != endpoints.end(); )
   {
      endpointsCommaDelim += *iteps;
      if (++iteps == endpoints.end())
         break;
      endpointsCommaDelim += ",";
   }
   std::stringbuf endpointsStrBuf;
   zstr::ostream costream{ &endpointsStrBuf };
   costream << endpointsCommaDelim << std::flush;
   std::string endpointsCompressedStr = endpointsStrBuf.str();
   resip::Data endpointsCompressedData(resip::Data::Share, endpointsCompressedStr.c_str(), endpointsCompressedStr.size());
   compressedList = endpointsCompressedData.base64encode().c_str();
   return 0;
}

int PushToTalkManagerImpl::decompressEndpointList(const cpc::string& compressedList, cpc::vector<cpc::string>& endpoints)
{
   const std::streamsize buff_size = 1500;
   char* decomp_buff = new char[buff_size];
   try
   {
      resip::Data endpointsCB64data(resip::Data::Share, compressedList.c_str(), compressedList.size());
      resip::Data endpointsCompressed = endpointsCB64data.base64decode();
      std::stringstream decompstream;
      std::stringstream strstream;
      strstream << endpointsCompressed;
      zstr::istream decomp(strstream);
      decomp.exceptions(std::ios_base::badbit);

      while (true)
      {
         decomp.read(decomp_buff, buff_size);
         std::streamsize cnt = decomp.gcount();
         if (cnt == 0) break;
         decompstream.write(decomp_buff, cnt);
      }

      std::string decompstr = decompstream.str();
      resip::ParseBuffer pb(decompstr.c_str());
      const char* anchor = pb.position();
      while (!pb.eof())
      {
         pb.skipToChar(',');
         endpoints.push_back(pb.data(anchor).c_str());
         if (pb.eof())
         {
            break;
         }
         pb.skipChar();
         anchor = pb.position();
      }
   }
   catch (const std::exception& zsexc)
   {
      ErrLog(<< "Error decrypting compressed endpoints list: " << zsexc.what());
      delete[] decomp_buff;
      return -1;
   }
   delete[] decomp_buff;
   return 0;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
UnicastTransport::UnicastTransport(std::shared_ptr<PushToTalkManagerImpl> pttManager) :
mPttManager(pttManager),
mHandler(NULL),
mLocalIp("")
{
}

UnicastTransport::~UnicastTransport()
{
   mIoContext.post([]() {});
   mWork.reset();
   if (mThread)
   {
      mThread->join();
      mThread.reset();
   }
   mUnicastSender.reset();
   mUnicastReceiver.reset();
   mIoContext.restart();

   mPttManager.reset();
}

int UnicastTransport::init(CPCAPI2::PhoneInterface* phone, PushToTalkTransportHandler* handler, std::shared_ptr<PushToTalkSenderReceiveSocketHolder> socketHolder, const PushToTalkServiceSettings& serviceSettings, const PushToTalkSettingsInternal& internalSettings)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mHandler = handler;
      mDiscoverySession.reset(new PttEndpointDiscoverySession(manager.get()));

      if (mUnicastReceiver || mUnicastSender)
      {
         shutdown(PttServiceStatusChangedEvent::Reason_None, false);
      }

      resip::Data srcIp = guessLocalIpAddress(serviceSettings);
      mLocalIp = srcIp.c_str();

      // Receiver
      bool receiverEnabled = ((serviceSettings.unicastPort > 0) && internalSettings.unicastReceiveEnabled);
      if (receiverEnabled)
      {
         DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " starting PTT unicast listener on port: " << serviceSettings.unicastBindAddress.c_str() << ":" << serviceSettings.unicastPort);
         int triesToConnect = 0;
         bool receiverCreated = false;
         while ((triesToConnect < 3) && (receiverCreated == false))
         {
            try
            {
               triesToConnect++;
               mUnicastReceiver = std::make_shared<PushToTalkUnicastReceiver>(
                  PushToTalkUnicast_NetworkImpairmentExperiment::GetExperiment(phone),
                  mIoContext,
                  shared_from_this(),
                  serviceSettings.unicastBindAddress.c_str(),
                  serviceSettings.unicastPort);

               mUnicastReceiver->start();
               if (mUnicastReceiver->isRunning())
               {
                  receiverCreated = true;
               }
               else
               {
                  WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " error starting PushToTalkUnicastListener");
                  mUnicastReceiver.reset();
               }
            }
            catch (const boost::system::system_error& ex)
            {
               WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastListener threw exception: " << ex.what());
               mUnicastReceiver.reset();
            }
            catch (const boost::system::error_code& ex)
            {
               WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastListener threw exception " << ex.message());
               mUnicastReceiver.reset();
            }
            catch (...)
            {
               WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastListener threw unknown exception");
               mUnicastReceiver.reset();
            }
         }

         if (!receiverCreated)
         {
            // TODO: should service status be FAILURE or DISCONNECTED, failure indicates re-connect will not be attempted as would be if a listener port is invalid.
            // how will be handle network change in such a scenario, where a reconnect will likely be attempted
            DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " send disconnected status as failed to start unicast listener on port: " << serviceSettings.unicastBindAddress.c_str() << ":" << serviceSettings.unicastPort);
            manager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_ERROR_STARTING_RECEIVER);
            return -1;
         }
      }

      // Sender
      PushToTalkUnicastSender::TransmissionParameters params;
      params.maximumUnicastTargetLimit = internalSettings.maximumUnicastTargetLimit;
      params.maximumUnicastMessageRetryLimit = internalSettings.maximumUnicastMessageRetryLimit;
      params.networkMaskOverride = internalSettings.networkMaskOverride.c_str();
      params.bindAddress = serviceSettings.unicastBindAddress.c_str();
      params.keepAliveEnabled = serviceSettings.keepAliveEnabled;
      params.dscp = serviceSettings.signallingDscp;

      mWork = std::make_shared<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>>(boost::asio::make_work_guard(mIoContext));

      DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " sender-identity: " << serviceSettings.senderIdentity.userName << " starting PTT unicast sender with dscp: " << params.dscp);
      try
      {
         // may throw if no network available. Try to find this out earlier so we can catch it and fire a onPttServiceStarted with startupSuccessful = false,
         // as opposed to waiting until PushToTalkManagerImpl::sendToWire executes
         boost::asio::ip::address_v4 srcIpAddr = boost::asio::ip::make_address_v4(srcIp.c_str());
         (void)srcIpAddr;

         mUnicastSender = std::make_shared<PushToTalkUnicastSender>(serviceSettings.unicastBindAddress.c_str(), PushToTalkUnicast_NetworkImpairmentExperiment::GetExperiment(phone), mWork, params, shared_from_this(), socketHolder);
         DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " sender-identity: " << serviceSettings.senderIdentity.userName << " ip-address: " << srcIp.c_str() << " starting PTT unicast sender with dscp: " << params.dscp);
         mUnicastSender->start();
      }
      catch (const boost::system::system_error& ex)
      {
         // ex.what() reporting "Invalid argument" can be a sign of no network being available
         WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastSender threw exception " << ex.what());
         mUnicastSender.reset();
      }
      catch (const boost::system::error_code& ex)
      {
         WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastSender threw exception " << ex.message());
         mUnicastSender.reset();
      }
      catch (...)
      {
         WarningLog(<< "PushToTalkUnicastImpl::UnicastTransport::init: " << this << " instantiating PushToTalkUnicastSender threw unknown exception");
         mUnicastSender.reset();
      }

      if (!mUnicastSender)
      {
         // TODO: should service status be FAILURE or DISCONNECTED, failure indicates re-connect will not be attempted as would be if a listener port is invalid.
         // how will be handle network change in such a scenario, where a reconnect will likely be attempted
         DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " send disconnected status as failed to start unicast sender for sender-identity: " << serviceSettings.senderIdentity.userName);
         manager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_ERROR_STARTING_SENDER);
         return -1;
      }

      mThread.reset(new std::thread([&]()
      {
         while ((mUnicastSender && mUnicastSender->isRunning()) || (mUnicastReceiver && mUnicastReceiver->isRunning()))
         {
            mIoContext.run_one();
         }
         mUnicastSender.reset();
         mUnicastReceiver.reset();
         InfoLog(<< "PushToTalkUnicastImpl::UnicastTransport::init(): " << this << " exiting PTT unicast transport thread");
      }));
   }

   return 0;
}

int UnicastTransport::shutdown(PttServiceStatusChangedEvent::Reason reason, bool sendStatus)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      stopDiscovery(manager->getService());

      if (mUnicastSender)
      {
         DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::shutdown(): " << this << " stopping PTT unicast sender");
         mUnicastSender->stop();
      }

      if (mUnicastReceiver)
      {
         DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::shutdown(): " << this << " stopping PTT unicast listener");
         mUnicastReceiver->stop();
      }

      if (sendStatus)
      {
         if (reason == PttServiceStatusChangedEvent::Reason_NetworkChange)
         {
            manager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, reason, PTT_SERVICE_REASON_WIFI_NOT_AVAILABLE);
         }
         else
         {
            manager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected);
         }
      }

      mIoContext.post([]() {});
      mWork.reset();
      if (mThread)
      {
         mThread->join();
         mThread.reset();
      }

      mUnicastSender.reset();
      mUnicastReceiver.reset();

      mIoContext.restart();

      // don't re-use the same io context in the future (e.g. if unicastTransport started again):
      // this io context may have items posted to it, e.g. ReceiveSocket::onSendTimeoutImpl which
      // point to the now bogus unicastSender/Receiver objects
   }
   return 0;
}

int UnicastTransport::broadcast(const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      try
      {
         if (!mUnicastSender)
         {
            InfoLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): " << this << " ignoring send requests as the unicast sender is not initialized");
            return -1;
         }

         if ((manager->getServiceSettings().unicastPort > 0) && manager->getInternalSettings().unicastTransmitEnabled)
         {
            std::vector<boost::asio::ip::address_v4> unicastIpAddrs;
            if (mDiscoverySession->isUpdating() || (mDiscoverySession->getEndpointList().empty()))
            {
               for (const PushToTalkIpAddressRange& ipRange : manager->getServiceSettings().unicastIpRanges)
               {
                  boost::asio::ip::address_v4 rangeStart = boost::asio::ip::make_address_v4(ipRange.ipAddrStart);
                  auto rangeStartBytes = rangeStart.to_bytes();
                  boost::asio::ip::address_v4 rangeEnd = boost::asio::ip::make_address_v4(ipRange.ipAddrEnd);
                  auto rangeEndBytes = rangeEnd.to_bytes();
                  if (rangeStartBytes[0] == rangeEndBytes[0] &&
                     rangeStartBytes[1] == rangeEndBytes[1] &&
                     rangeStartBytes[2] == rangeEndBytes[2])
                  {
                     boost::asio::ip::address_v4::bytes_type effectiveAddressBytes = rangeStartBytes;
                     for (int uniqueIp = rangeStartBytes[3]; uniqueIp <= rangeEndBytes[3]; uniqueIp++)
                     {
                        effectiveAddressBytes[3] = uniqueIp;
                        unicastIpAddrs.push_back(boost::asio::ip::address_v4(effectiveAddressBytes));
                     }
                  }
               }
            }
            else
            {
               for (std::map<std::string, PttIdentity>::const_iterator i = mDiscoverySession->getEndpointList().begin(); i != mDiscoverySession->getEndpointList().end(); ++i)
               {
                  unicastIpAddrs.push_back(boost::asio::ip::make_address_v4(i->first.c_str()));
               }
            }

            std::vector<boost::asio::ip::udp::endpoint> targetEndpoints;

            if (unicastIpAddrs.empty())
            {
               resip::Data srcIp = guessLocalIpAddress(manager->getServiceSettings());
               InfoLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): " << this << " using local IP address: " << srcIp << " target port is: " << manager->getServiceSettings().unicastPort);
               boost::asio::ip::address_v4 srcIpAddr = boost::asio::ip::make_address_v4(srcIp.c_str());
               boost::asio::ip::address_v4 netMask;
               if (manager->getInternalSettings().networkMaskOverride.size() > 0)
               {
                  netMask = boost::asio::ip::make_address_v4(manager->getInternalSettings().networkMaskOverride.c_str());
                  WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): using subnet mask override: " << netMask);
               }
               else
               {
                  netMask = boost::asio::ip::make_address_v4(PTT_UNICAST_DEFAULT_NETWORK_MASK); // start with a default, in case we can't find one
                  if (IpHelpers::getSubnetMask(srcIpAddr, netMask) < 0)
                     WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): subnet mask not found for srcIp " << srcIpAddr << ", defaulting to " << netMask);
                  else
                     InfoLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): subnet mask: " << netMask);
               }

               unsigned int limit = manager->getInternalSettings().maximumUnicastTargetLimit;
               if (IpHelpers::getSubnetIpRange(srcIpAddr, netMask, targetEndpoints, manager->getServiceSettings().unicastPort, limit, &srcIpAddr) < 0)
                  WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): ip range limited to " << limit << " addresses");

               if (targetEndpoints.size() > 0)
                  DebugLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): added " << targetEndpoints.size() << " (" << targetEndpoints.front() << "-" << targetEndpoints.back() << ") hosts as targets.");
               else
                  WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast(): no targets added");
            }
            else
            {
               for (const boost::asio::ip::address_v4& unicastIp : unicastIpAddrs)
               {
                  boost::asio::ip::udp::endpoint endpoint(unicastIp, manager->getServiceSettings().unicastPort);
                  targetEndpoints.push_back(endpoint);
               }
            }

            mUnicastSender->send(messageId, highPriority, noBurst, payload, targetEndpoints, enableHandler);

#ifdef CPCAPI2_AUTO_TEST
            // Use to send the payload to different ports rather than the same port for all the addresses
            for (cpc::vector<int>::iterator i = manager->getInternalSettings().unicastTargets.begin(); i != manager->getInternalSettings().unicastTargets.end(); ++i)
            {
               std::vector<boost::asio::ip::udp::endpoint> targetTestEndpoints;
               for (const boost::asio::ip::udp::endpoint& endpointIp : targetEndpoints)
               {
                  boost::asio::ip::udp::endpoint endpoint(endpointIp.address(), (*i));
                  targetTestEndpoints.push_back(endpoint);
               }
               mUnicastSender->send(messageId, highPriority, noBurst, payload, targetTestEndpoints, enableHandler);
            }
#endif
         }
      }
      catch (const boost::system::system_error& ex)
      {
         // ex.what() reporting "Invalid argument" can be a sign of no network being available

         // TODO: should we actually log this? what if it results in log spam?
         WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::broadcast: " << this << " threw exception " << ex.what());
      }
   }
   return 0;
}

int UnicastTransport::sendTo(const Endpoint& endpoint, const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      try
      {
         if (!mUnicastSender)
         {
            InfoLog(<< "PushToTalkManagerImpl::UnicastTransport::sendTo(): " << this << " ignoring send requests as the unicast sender is not initialized");
            return -1;
         }

         if ((manager->getServiceSettings().unicastPort > 0) && manager->getInternalSettings().unicastTransmitEnabled)
         {
            std::vector<boost::asio::ip::address_v4> unicastIpAddrs;
            unicastIpAddrs.push_back(boost::asio::ip::make_address_v4(endpoint.transportSpecific.unicast.targetIpAddress));

            std::vector<boost::asio::ip::udp::endpoint> targetEndpoints;

            for (const boost::asio::ip::address_v4& unicastIp : unicastIpAddrs)
            {
               boost::asio::ip::udp::endpoint udpendpoint(unicastIp, manager->getServiceSettings().unicastPort);
               targetEndpoints.push_back(udpendpoint);
            }

            mUnicastSender->send(messageId, highPriority, noBurst, payload, targetEndpoints, enableHandler);

#ifdef CPCAPI2_AUTO_TEST
            // Use to send the payload to different ports rather than the same port for all the addresses
            for (cpc::vector<int>::iterator i = manager->getInternalSettings().unicastTargets.begin(); i != manager->getInternalSettings().unicastTargets.end(); ++i)
            {
               std::vector<boost::asio::ip::udp::endpoint> targetTestEndpoints;
               for (const boost::asio::ip::udp::endpoint& endpointIp : targetEndpoints)
               {
                  boost::asio::ip::udp::endpoint udpendpoint(endpointIp.address(), (*i));
                  targetTestEndpoints.push_back(udpendpoint);
               }
               mUnicastSender->send(messageId, highPriority, noBurst, payload, targetTestEndpoints, enableHandler);
            }
#endif
         }
      }
      catch (const boost::system::system_error& ex)
      {
         // ex.what() reporting "Invalid argument" can be a sign of no network being available

         // TODO: should we actually log this? what if it results in log spam?
         WarningLog(<< "PushToTalkManagerImpl::UnicastTransport::sendTo(): " << this << " threw exception " << ex.what());
      }
   }
   return 0;
}

resip::Data UnicastTransport::guessLocalIpAddress(const PushToTalkServiceSettings& serviceSettings) const
{
   resip::Data srcIp;
#ifdef CPCAPI2_AUTO_TEST
   srcIp = serviceSettings.unicastBindAddress.c_str();
#else
   resip::Tuple googDns("*******", 53, resip::UDP);
   IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
#endif

   return srcIp;
}

void UnicastTransport::onReceiverStarted()
{
   /*
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "UnicastTransport::onReceiverStarted(): " << this << " manager: " << manager);
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onReceiverStartedImpl, shared_from_this()));
   }
   */
}

void UnicastTransport::onReceiverStartedImpl()
{
   /*
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "UnicastTransport::onReceiverStartedImpl(): " << this << " manager: " << manager);
      mHandler->onReceiverStarted();
   }
   */
}

void UnicastTransport::onReceiverStopped()
{
   /*
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "UnicastTransport::onReceiverStopped(): " << this << " manager: " << manager);
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onReceiverStoppedImpl, shared_from_this()));
   }
   */
}

void UnicastTransport::onReceiverStoppedImpl()
{
   /*
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "UnicastTransport::onReceiverStoppedImpl(): " << this << " manager: " << manager);
      mHandler->onReceiverStopped();
   }
   */
}

void UnicastTransport::onIncomingUnicast(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mHandler->onIncoming(ipAddr, port, payload, payloadLen);
   }
}

void UnicastTransport::onSenderStarted()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onSenderStarted()");
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onSenderStartedImpl, shared_from_this()));
   }
}

void UnicastTransport::onSenderStartedImpl()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::onSenderStartedImpl()");
      manager->onSenderStarted();
      mDiscoverySession->onSenderStarted();
   }
}

void UnicastTransport::onSenderStopped()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onSenderStopped(): discovery-session: " << mDiscoverySession);
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onSenderStoppedImpl, shared_from_this()));
   }
}

void UnicastTransport::onSenderStoppedImpl()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      DebugLog(<< "PushToTalkUnicastImpl::UnicastTransport::onSenderStoppedImpl()");
      manager->onSenderStopped();
   }
}

void UnicastTransport::onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onOutgoingSendCompleted(): discovery-session: " << mDiscoverySession);
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onOutgoingSendCompletedImpl, shared_from_this(), targets));
   }
}

void UnicastTransport::onOutgoingSendCompletedImpl(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onOutgoingSendCompletedImpl(): discovery-session: " << mDiscoverySession);
      if (mDiscoverySession->hasStarted())
      {
         StackLog(<< "PushToTalkUnicastImpl::UnicastTransport::onOutgoingSendCompletedImpl(): ignore as discovery session has started");
         return;
      }

      mDiscoverySession->onOutgoingSendCompleted(targets);
   }
}

void UnicastTransport::onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onOutgoingSendFailed(): discovery-session: " << mDiscoverySession);
      manager->getPhone()->getSdkModuleThread().post(resip::resip_bind(&UnicastTransport::onOutgoingSendFailedImpl, shared_from_this(), target, payload, length));
   }
}

void UnicastTransport::onOutgoingSendFailedImpl(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      // StackLog(<< "UnicastTransport::onOutgoingSendFailedImpl(): discovery-session: " << mDiscoverySession);
      if (manager->isNetworkChangeInProgress())
      {
         // StackLog(<< "UnicastTransport::onOutgoingSendFailedImpl(): ignore as network change is in progress");
         return;
      }

      if (mUnicastSender)
      {
         mUnicastSender->unsend(UINT_MAX, target);
         mUnicastSender->setKeepAliveTarget(target, false);
      }
   }
}

void UnicastTransport::queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      if (mDiscoverySession)
         mDiscoverySession->queryEndpointList(service);
   }
}

void UnicastTransport::startDiscovery(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      StackLog(<< "PushToTalkUnicastImpl::UnicastTransport::startDiscovery(): service: " << service);
      if (mDiscoverySession)
         mDiscoverySession->startService(service);
   }
}

void UnicastTransport::stopDiscovery(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      StackLog(<< "PushToTalkUnicastImpl::UnicastTransport::stopDiscovery(): service: " << service);
      if (mDiscoverySession)
         mDiscoverySession->shutdownService(service);
   }
}

bool UnicastTransport::hasDiscoveryStarted()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      if (mDiscoverySession)
         return mDiscoverySession->hasStarted();
   }
   return false;
}

void UnicastTransport::unsendAll(unsigned int messageId)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      if (mUnicastSender)
      {
         mUnicastSender->unsendAll(messageId);
      }
   }
}

void UnicastTransport::unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      if (mUnicastSender)
      {
         mUnicastSender->unsend(messageId, target);
      }
   }
}

void UnicastTransport::setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      if (mUnicastSender)
      {
         mUnicastSender->setKeepAliveTarget(target, enable);
      }
   }
}

const std::map<std::string, PttIdentity>& UnicastTransport::getEndpointList()
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      return mDiscoverySession->getEndpointList();
   }
   static std::map<std::string, PttIdentity> emptyMap;
   return emptyMap;
}

void UnicastTransport::onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mDiscoverySession->onQueryEndpointsRequest(queryRequestEvt, incomingTuple);
   }
}

void UnicastTransport::addToDiscoveryList(PttIdentity& identity)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mDiscoverySession->addToDiscoveryList(identity);
   }
}

void UnicastTransport::onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mDiscoverySession->onQueryEndpointsResponse(evt, incomingTuple);
   }
}

void UnicastTransport::onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mDiscoverySession->onReportEndpointsEvent(evt, incomingTuple);
   }
}

void UnicastTransport::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   if (std::shared_ptr<PushToTalkManagerImpl> manager = mPttManager.lock())
   {
      mDiscoverySession->onNetworkChange(args);
   }
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
