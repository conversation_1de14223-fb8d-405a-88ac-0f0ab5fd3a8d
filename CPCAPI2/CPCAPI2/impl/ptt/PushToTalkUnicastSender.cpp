#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkUnicastSender.h"
#include "PushToTalkManagerInternal.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include "qos/QosSocketManager.hxx"

#include <rutil/Random.hxx>

#include <ctime>

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#ifdef ANDROID
#include <android/log.h>
#endif

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT


namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkUnicastSender::TransmissionParameters::TransmissionParameters() :
maximumUnicastTargetLimit(PTT_UNICAST_MAXIMUM_TARGET_LIMIT),
maximumUnicastMessageRetryLimit(PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT),
networkMaskOverride(""),
bindAddress(""),
keepAliveEnabled(true),
dscp(PTT_SERVICE_SIGNALLING_DSCP) {}

PushToTalkUnicastSender::TransmissionParameters::TransmissionParameters(
   int targetLimit, int retryLimit, std::string& networkMask, std::string& bindAddress_, bool keepAliveEnabled_, int dscp_) :
maximumUnicastTargetLimit(targetLimit),
maximumUnicastMessageRetryLimit(retryLimit),
networkMaskOverride(networkMask),
bindAddress(bindAddress_),
keepAliveEnabled(keepAliveEnabled_),
dscp(dscp_) {}

PushToTalkUnicastSender::DataMessage::DataMessage(
   unsigned int messageId_, bool highPriority_, bool noBurst_, const std::string& data, std::vector<boost::asio::ip::udp::endpoint>& endpoints, bool enableHandler_) :
message(data),
retryCount(0),
sendCompleteCount(0),
enableHandler(enableHandler_),
targets(endpoints),
targetIndex(0),
messageId(messageId_),
highPriority(highPriority_),
noBurst(noBurst_)
{
   uniqueId = resip::Random::getCryptoRandom();
}

PushToTalkSenderReceiveSocketHolder::PushToTalkSenderReceiveSocketHolder()
{
}

PushToTalkSenderReceiveSocketHolder::~PushToTalkSenderReceiveSocketHolder()
{
   mSocketMap.clear();
}

void PushToTalkSenderReceiveSocketHolder::add(boost::asio::ip::udp::endpoint recvEndpoint, std::shared_ptr<PushToTalkUnicastSender::ReceiveSocket> recvSocket)
{
   mSocketMap[recvEndpoint] = recvSocket;
}

void PushToTalkSenderReceiveSocketHolder::remove(boost::asio::ip::udp::endpoint recvEndpoint)
{
   mSocketMap.erase(recvEndpoint);
}

void PushToTalkSenderReceiveSocketHolder::clear()
{
   mSocketMap.clear();
}

PushToTalkUnicastSender::PushToTalkUnicastSender(
   const std::string& bindAddress,
   PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment_exp,
   const std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> io_work,
   const TransmissionParameters& params,
   const std::shared_ptr<PushToTalkUnicastSenderHandler>& handler,
   const std::shared_ptr<PushToTalkSenderReceiveSocketHolder>& holder) :
#ifdef CPCAPI2_AUTO_TEST
mSocket(io_work->get_executor().context(), boost::asio::ip::udp::endpoint(boost::asio::ip::make_address_v4(params.bindAddress), 0)),
#else
mSocket(io_work->get_executor().context(), boost::asio::ip::udp::socket::protocol_type::v4()),
#endif
mSocketSizeSet(false),
mWork(io_work),
mConfig(params),
mHandler(handler),
mHolder(holder),
mSendTimer(io_work->get_executor().context()),
mNetworkImpairmentExp(network_impairment_exp),
mBindAddress(bindAddress),
mPingTimer(io_work->get_executor().context())
{
   DebugLog(<< "PushToTalkUnicastSender(): " << this << " bind-address: " << mBindAddress.c_str() << " dscp: " << mConfig.dscp);
}

PushToTalkUnicastSender::~PushToTalkUnicastSender()
{
   DebugLog(<< "PushToTalkUnicastSender::~PushToTalkUnicastSender " << this);
   mWork.reset();
   mHandler.reset();
   mHolder.reset();
   mSendTimer.cancel();
   mSocketMap.clear();
}

void PushToTalkUnicastSender::start()
{
   boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::startImpl, shared_from_this()));
}

void PushToTalkUnicastSender::startImpl()
{
   InfoLog(<< "PushToTalkUnicastSender::startImpl(): " << this);
   mEnabled = true;
   // mStartPromise.set_value(true);

#ifdef ANDROID
   if (mConfig.keepAliveEnabled)
   {
      InfoLog(<< "PushToTalkUnicastSender: Ping keep alive enabled");
      mPingTimer.expires_from_now(boost::posix_time::seconds(1));
      mPingTimer.async_wait(std::bind(&PushToTalkUnicastSender::onPingTimeoutImpl, shared_from_this(), std::placeholders::_1));
   }
   else
#endif
   {
      InfoLog(<< "PushToTalkUnicastSender: Ping keep alive disabled");
   }

   if (std::shared_ptr<PushToTalkUnicastSenderHandler> handler = mHandler.lock())
   {
      handler->onSenderStarted();
   }
}

void PushToTalkUnicastSender::stop()
{
   auto hF = mStopPromise.get_future();
   boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::stopImpl, shared_from_this()));
   mStopPromise = std::promise<bool>(); // reset for next use
}

void PushToTalkUnicastSender::stopImpl()
{
   DebugLog(<< "PushToTalkUnicastSender::stopImpl(): " << this);

   SocketMap::iterator it = mSocketMap.begin();
   for (; it != mSocketMap.end(); ++it)
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         rs->stopImpl();
      }
   }
   mSocketMap.clear();
   mStopPromise.set_value(true);

   mEnabled = false;

   if (std::shared_ptr<PushToTalkUnicastSenderHandler> handler = mHandler.lock())
   {
      handler->onSenderStopped();
   }
}

void PushToTalkUnicastSender::send(unsigned int messageId, bool highPriority, bool noBurst, const std::string& payload, std::vector<boost::asio::ip::udp::endpoint>& targets, bool enableHandler)
{
   // DebugLog(<< "PushToTalkUnicastSender::send(): " << this << " targets: " << targets.size() << " payload: " << payload);
   if (!mEnabled)
   {
      DebugLog(<< "PushToTalkUnicastSender::send(): " << this << " ignoring request as sender is disabled");
      return;
   }

   if (mEnabled)
   {
      boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::queueMessageImpl, shared_from_this(), DataMessage(messageId, highPriority, noBurst, payload, targets, enableHandler)));
   }
   else
   {
      DebugLog(<< "PushToTalkUnicastSender::send(): " << this << " ignoring request as sender is disabled");
   }
}

void PushToTalkUnicastSender::unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target)
{
   if (mEnabled)
   {
      boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::unsendImpl, shared_from_this(), messageId, target));
   }
}

void PushToTalkUnicastSender::unsendImpl(unsigned int messageId, const boost::asio::ip::udp::endpoint& target)
{
   SocketMap::iterator it = mSocketMap.find(target);
   if (it != mSocketMap.end())
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         rs->unsendImpl(messageId);
      }
   }
}

void PushToTalkUnicastSender::unsendAll(unsigned int messageId)
{
   if (mEnabled)
   {
      boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::unsendAllImpl, shared_from_this(), messageId));
   }
}

void PushToTalkUnicastSender::unsendAllImpl(unsigned int messageId)
{
   for (SocketMap::iterator it = mSocketMap.begin(); it != mSocketMap.end(); ++it)
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         rs->unsendImpl(messageId);
      }
   }
}

void PushToTalkUnicastSender::setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable)
{
   if (mEnabled)
   {
      boost::asio::post(mWork->get_executor().context(), std::bind(&PushToTalkUnicastSender::setKeepAliveTargetImpl, shared_from_this(), target, enable));
   }
}

void PushToTalkUnicastSender::setKeepAliveTargetImpl(const boost::asio::ip::udp::endpoint& target, bool enable)
{
   SocketMap::iterator it = mSocketMap.find(target);
   if (it != mSocketMap.end())
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         rs->mIsKeepAliveTarget = enable;
      }
   }
}

void PushToTalkUnicastSender::queueMessageImpl(PushToTalkUnicastSender::DataMessage& message)
{
   // StackLog(<< "PushToTalkUnicastSender::queueMessageImpl(): " << this << message);
   message.targetIndex = 0;
   mDataMessageCompletionCounter[message.uniqueId] = 0;

   for (int k = 0; k < message.targets.size(); k++)
   {
      boost::asio::ip::udp::endpoint theTarget = message.targets[k];
      int numRetransmissions = std::min<int>(PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT, 3); // see 'schedule' below

      SocketMap::iterator it = mSocketMap.find(theTarget);
      if (it == mSocketMap.end())
      {
         std::shared_ptr<ReceiveSocket> rs = std::make_shared<ReceiveSocket>(shared_from_this(), mBindAddress, mWork, theTarget, mNetworkImpairmentExp, mHandler.lock(), mHolder.lock());
         if (std::shared_ptr<PushToTalkSenderReceiveSocketHolder> holder = mHolder.lock())
         {
            holder->add(theTarget, rs);
            mSocketMap[theTarget] = rs;
         }
         else
         {
            assert(0);
         }
      }

      if (std::shared_ptr<ReceiveSocket> rs = mSocketMap[theTarget].lock())
      {
         std::vector<boost::asio::ip::udp::endpoint> singleTarget;
         singleTarget.push_back(theTarget);

         DataMessage dm(message.messageId, message.highPriority, message.noBurst, message.message, singleTarget, message.enableHandler);
         dm.uniqueId = message.uniqueId;
         mDataMessageCompletionCounter[dm.uniqueId] = mDataMessageCompletionCounter[dm.uniqueId] + numRetransmissions;

         auto isNow = std::chrono::system_clock::now();
         if (message.noBurst)
         {
            dm.notBefore = isNow;
            rs->queueMessageImpl(dm);
         }
         else
         {
            int randomIntervalMs = resip::Random::getCryptoRandom() % 200;
            static int schedule[] = { 0, 80, 200 };
            for (int i = 0; i < numRetransmissions; i++)
            {
               if (i == (numRetransmissions - 1))
               {
                  dm.notBefore = isNow + std::chrono::milliseconds(schedule[i] + randomIntervalMs);
               }
               else
               {
                  dm.notBefore = isNow + std::chrono::milliseconds(schedule[i] + ((k % 6) * 10));
               }
               rs->queueMessageImpl(dm);
            }
         }
      }
   }

   socketDoneSending();
}

resip::Data PushToTalkUnicastSender::guessLocalIpAddress() const
{
   resip::Data srcIp;
   if (!mBindAddress.empty())
   {
      srcIp = mBindAddress.c_str();
   }
   else
   {
      resip::Tuple googDns("*******", 53, resip::UDP);
      IpHelpers::getPreferredLocalIpAddress(googDns, srcIp);
   }
   if (srcIp.empty())
   {
      srcIp = "127.0.0.1";
   }
   return srcIp;
}

void PushToTalkUnicastSender::onPingTimeoutImpl(const boost::system::error_code& ec)
{
   if (ec)
      return;
   // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!

   static std::vector<boost::asio::ip::udp::endpoint> emptyEndpoints;
   DataMessage dm(0, false, true, "\r\n\r\n", emptyEndpoints, false);
   SocketMap::const_iterator it = mSocketMap.begin();
   int i = 0;
   for (; it != mSocketMap.end(); ++it)
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         if (rs->mIsKeepAliveTarget && i < 3)
         {
            dm.targets.clear();
            dm.targets.push_back(rs->mRemoteEndpoint);
            rs->queueMessageImpl(dm);
            i++;
         }
      }
   }

   if (i == 0)
   {
      boost::system::error_code ipfsec;
      boost::asio::ip::address_v4 dummyIp = boost::asio::ip::address_v4::from_string(guessLocalIpAddress().c_str(), ipfsec);
      if (!ipfsec)
      {
         auto dummyIpBytes = dummyIp.to_bytes();
         if (dummyIpBytes[3] < 254)
         {
            dummyIpBytes[3] = dummyIpBytes[3] + 1;
         }
         else
         {
            dummyIpBytes[3] = dummyIpBytes[3] - 1;
         }
         boost::asio::ip::udp::endpoint ep(boost::asio::ip::address(boost::asio::ip::address_v4(dummyIpBytes)), 9);
         dm.targets.clear();
         dm.targets.push_back(ep);

         std::shared_ptr<ReceiveSocket> rs = std::make_shared<ReceiveSocket>(shared_from_this(), mBindAddress, mWork, ep, mNetworkImpairmentExp, mHandler.lock(), mHolder.lock());
         if (std::shared_ptr<PushToTalkSenderReceiveSocketHolder> holder = mHolder.lock())
         {
            holder->add(ep, rs);
            mSocketMap[ep] = rs;
            rs->queueMessageImpl(dm);
         }
         else
         {
            assert(0);
         }
      }
   }

   socketDoneSending();

   mPingTimer.expires_from_now(boost::posix_time::seconds(1));
   mPingTimer.async_wait(std::bind(&PushToTalkUnicastSender::onPingTimeoutImpl, shared_from_this(), std::placeholders::_1));
}

void PushToTalkUnicastSender::dataMessageSent(const DataMessage& dm)
{
   // StackLog(<< "PushToTalkUnicastSender::dataMessageSent(): " << this << dm << " enableHandler: " << dm.enableHandler << " completion-counter: " << mDataMessageCompletionCounter.size());
   if (dm.enableHandler)
   {
      mDataMessageCompletionCounter[dm.uniqueId] = mDataMessageCompletionCounter[dm.uniqueId] - 1;
      if (mDataMessageCompletionCounter[dm.uniqueId] == 0)
      {
         if (std::shared_ptr<PushToTalkUnicastSenderHandler> handler = mHandler.lock())
         {
            handler->onOutgoingSendCompleted(dm.targets);
         }
      }
   }
}

void PushToTalkUnicastSender::socketDoneSending()
{
   while (countSocketsInUse() < PTT_UNICAST_NEW_MAX_FILE_DESCRIPTORS)
   {
      SocketMap::const_iterator it = mSocketMap.begin();
      for (; it != mSocketMap.end(); ++it)
      {
         if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
         {
            if (rs->mState == ReceiveSocketState_Waiting)
            {
               rs->startSending();
               break;
            }
         }
      }
      if (it == mSocketMap.end())
      {
         break;
      }
   }
}

size_t PushToTalkUnicastSender::countSocketsInUse() const
{
   size_t retVal = 0;
   SocketMap::const_iterator it = mSocketMap.begin();
   for (; it != mSocketMap.end(); ++it)
   {
      if (std::shared_ptr<ReceiveSocket> rs = it->second.lock())
      {
         if (rs->mState == ReceiveSocketState_Sending)
         {
            retVal++;
         }
      }
   }
   return retVal;
}

PushToTalkUnicastSender::ReceiveSocket::ReceiveSocket(
   std::shared_ptr<PushToTalkUnicastSender> unicastSender,
   const std::string& bindAddress,
   const std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> io_work,
   const boost::asio::ip::udp::endpoint& remote_endpoint,
   PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment,
   const std::shared_ptr<PushToTalkUnicastSenderHandler>& handler,
   const std::shared_ptr<PushToTalkSenderReceiveSocketHolder>& holder) :
mWork(io_work),
mRemoteEndpoint(remote_endpoint),
mSendTimer(io_work->get_executor().context()),
mSocket(io_work->get_executor().context()),
mHandler(handler),
mHolder(holder),
mNetworkImpairmentExp(network_impairment),
mLastSendTime(std::chrono::time_point<std::chrono::system_clock>::min()),
mStartTime(std::chrono::time_point<std::chrono::system_clock>::min()),
mUnicastSender(unicastSender),
mBindAddress(bindAddress),
mState(ReceiveSocketState_Uninitialized),
mIsKeepAliveTarget(false),
mContinguousOpenFailures(0)
{
}

PushToTalkUnicastSender::ReceiveSocket::~ReceiveSocket()
{
   DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::~ReceiveSocket(): " << this << " with socket fd: " << mSocket.lowest_layer().native_handle());
   mQ.clear();
   mWork.reset();
   mHolder.reset();
   boost::system::error_code ec;
   mSocket.close(ec);
   if (ec)
   {
      DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::~ReceiveSocket(): " << this << " error closing socket: " << ec.value() << " (" << ec.message() << ")");
   }
}

void PushToTalkUnicastSender::ReceiveSocket::startWaiting()
{
   mState = ReceiveSocketState_Waiting;
}

void PushToTalkUnicastSender::ReceiveSocket::startSending()
{
   std::unique_ptr<boost::asio::ip::udp::endpoint> listen_endpoint;
   if (mBindAddress.empty())
   {
      listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::udp::v4(), 0));
   }
   else
   {
      listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::make_address_v4(mBindAddress), 0));
   }

   boost::system::error_code ec;
   mSocket.open(listen_endpoint->protocol(), ec);

   std::stringstream endpointss;
   endpointss << listen_endpoint->address() << ":" << listen_endpoint->port();
   if (ec)
   {
      // decrease log flooding
      if (mContinguousOpenFailures % 20 == 0)
      {
         ErrLog(<< "PushToTalkUnicastSender::ReceiveSocket::startSending(): " << this <<  " - Could not open socket ec: " << ec << " for endpoint: " << endpointss.str() << " continguous open error-count: " << mContinguousOpenFailures);
         mContinguousOpenFailures = ((mContinguousOpenFailures < UINT_MAX) ? (mContinguousOpenFailures + 1) : 0);
      }
      return;
   }

   resip::Socket fd = mSocket.lowest_layer().native_handle();
   // StackLog(<< "PushToTalkUnicastSender::ReceiveSocket::startSending(): " << this << " socket opened with fd: " << fd << " to endpoint: " << endpointss.str());

   mContinguousOpenFailures = 0;
   mSocket.bind(*listen_endpoint, ec);
   if (ec)
   {
      ErrLog(<< "PushToTalkUnicastSender::ReceiveSocket::startSending(): " << this << " - Could not bind socket with fd: " << fd << " to endpoint: " << endpointss.str());
      mSocket.close(ec);
      if (ec)
      {
         DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::startSending(): " << this << " error closing socket: " << ec.value() << " (" << ec.message() << ")");
      }
      return;
   }

   // StackLog(<< "PushToTalkUnicastSender::ReceiveSocket::startSending(): " << this << " - bind to endpoint: " << endpointss.str() << " with dscp: " << mUnicastSender->mConfig.dscp);
   if (std::shared_ptr<PushToTalkUnicastSender> sender = mUnicastSender.lock())
   {
      if (sender->mConfig.dscp >= 0 && sender->mConfig.dscp <= 63)
      {
         webrtc_recon::QosSocketManager::SocketSetDSCP(mSocket.lowest_layer().native_handle(), sender->mConfig.dscp, true);
      }
   }

   mState = ReceiveSocketState_Sending;

   // at least on Android, if we don't adjust the default send socket size, sending to
   // a whole subnet can take several seconds to finish (all boost socket send callbacks fired).
   // even with in increased send buffer, 2nd or 3rd iterations of sending can take seconds
   // (possible ARP queries by the OS are still pending?)

   const int sockBufReqBytes = 1500 * 100;

   if (!resip::makeSocketNonBlocking(fd))
   {
      assert(0);
   }
   resip::setSocketSndBufLen(fd, sockBufReqBytes);

   mSocket.async_connect(mRemoteEndpoint, [](const boost::system::error_code& connec) {
      if (connec)
         return;
      // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!
   });

   doReceiveImpl();

   mSendTimer.expires_from_now(boost::posix_time::milliseconds(10));
   mSendTimer.async_wait(std::bind(&PushToTalkUnicastSender::ReceiveSocket::onSendTimeoutImpl, shared_from_this(), std::placeholders::_1));
}

void PushToTalkUnicastSender::ReceiveSocket::doReceiveImpl()
{
   mSocket.async_receive(
      boost::asio::buffer(mData),
      [self = this->shared_from_this()](boost::system::error_code ec, std::size_t length)
   {
      if (ec.value() == boost::asio::error::operation_aborted || ec.value() == boost::asio::error::bad_descriptor)
         return;
      // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!

      if (ec)
      {
         //WarningLog(<< "PushToTalkUnicastSender::doReceiveImpl(): " << this << " ec: " << ec.value() << " desc: " << ec.message());
         if (std::shared_ptr<PushToTalkUnicastSenderHandler> handler = self->mHandler.lock())
         {
            handler->onOutgoingSendFailed(self->mRemoteEndpoint, self->mData.data(), length);
         }
      }
      else
      {
         WarningLog(<< "PushToTalkUnicastSender::ReceiveSocket::doReceiveImpl(): " << self.get() << " PushToTalkUnicastSender::doReceiveImpl(): ignoring incoming data");
      }
      self->doReceiveImpl();
   });
}

void PushToTalkUnicastSender::ReceiveSocket::queueMessageImpl(PushToTalkUnicastSender::DataMessage& message)
{
   // StackLog(<< "PushToTalkUnicastSender::queueMessageImpl(): " << this << " queue-size: " << mQ.size() << " send message: " << message);
   if (message.highPriority)
   {
      mQ.push_front(message);
   }
   else
   {
      mQ.push_back(message);
   }

   if (mState == ReceiveSocketState_Uninitialized || mState == ReceiveSocketState_Done)
   {
      startWaiting();
   }
}

void PushToTalkUnicastSender::ReceiveSocket::onSendTimeoutImpl(const boost::system::error_code& ec)
{
   if (ec)
      return;
   // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!

   if (mState == ReceiveSocketState_WillBeDeleted)
      return;

   std::chrono::milliseconds suggestedWait(10);
   auto theNow = std::chrono::system_clock::now();
   if (!mQ.empty())
   {
      DataMessage message = mQ.front();
      if (theNow >= message.notBefore)
      {
         // StackLog(<< "PushToTalkUnicastSender::onSendTimeoutImpl(): " << this << " queue-size: " << mQ.size() << " send message: " << message);
         sendImpl(message);
         mQ.pop_front();
      }
      else
      {
         suggestedWait = std::chrono::duration_cast<std::chrono::milliseconds>(message.notBefore - theNow);
      }
   }

   if (!mQ.empty())
   {
      // StackLog(<< "PushToTalkUnicastSender::onSendTimeoutImpl(): " << this << " queue-size: " << mQ.size() << " reset queue timeout to: " << suggestedWait.count());
      mSendTimer.expires_from_now(boost::posix_time::milliseconds(suggestedWait.count()));
      mSendTimer.async_wait(std::bind(&PushToTalkUnicastSender::ReceiveSocket::onSendTimeoutImpl, shared_from_this(), std::placeholders::_1));
   }
   else
   {
      // StackLog(<< "PushToTalkUnicastSender::onSendTimeoutImpl(): " << this << " queue-size: " << mQ.size() << " shutdown socket");
      mState = ReceiveSocketState_Done;
      boost::system::error_code ec;
      mSocket.shutdown(boost::asio::ip::udp::socket::shutdown_both, ec);
      mSocket.close(ec);
      if (ec)
      {
         DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::onSendTimeoutImpl(): " << this << " error closing socket: " << ec.value() << " (" << ec.message() << ")");
      }
      if (std::shared_ptr<PushToTalkUnicastSender> sender = mUnicastSender.lock())
      {
         sender->socketDoneSending();
      }
   }
}

void PushToTalkUnicastSender::ReceiveSocket::transportSendImpl(const boost::asio::ip::udp::endpoint& endpoint, const std::string& strbuffer)
{
   //if (mNetworkImpairmentExp != NULL)
   //{
   //   cpc::string targetEpStr = endpoint.address().to_string().c_str();
   //   const cpc::vector<cpc::string>& unresponsiveEndpoints = mNetworkImpairmentExp->unresponsive_endpoints();
   //   for (const cpc::string& unrep : unresponsiveEndpoints)
   //   {
   //      if (targetEpStr == unrep)
   //      {
   //         if (std::shared_ptr<PushToTalkUnicastSenderHandler> handler = mHandler.lock())
   //         {
   //            handler->onOutgoingSendFailed(endpoint, strbuffer.c_str(), strbuffer.size());
   //            return;
   //         }
   //      }
   //   }
   //}

   boost::system::error_code ec;
   const int kMaxTriesSend = PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT_FOR_SOCKET_ERROR;
   int loopc = 0;
   do
   {
      ec = boost::system::error_code();
      int res = mSocket.send(boost::asio::buffer(strbuffer), 0, ec);
      if (res == SOCKET_ERROR)
      {
#ifdef WIN32
         if (ec.value() == WSAEWOULDBLOCK)
#else
         if (ec.value() == EAGAIN || ec.value() == EWOULDBLOCK || ec.value() == EINTR)
#endif
         {
            if (loopc == (kMaxTriesSend - 2))
            {
               resip::FdSet fds;
               resip::Socket fd = mSocket.lowest_layer().native_handle();
               fds.setWrite(fd);
               fds.selectMilliSeconds(10);
            }
         }
      }
      loopc++;
   }
#ifdef WIN32
   while (ec.value() == WSAEWOULDBLOCK && loopc < kMaxTriesSend);
#else
   while ((ec.value() == EAGAIN || ec.value() == EWOULDBLOCK || ec.value() == EINTR) && loopc < kMaxTriesSend);
#endif

   if (loopc == kMaxTriesSend)
   {
      WarningLog(<< "PushToTalkUnicastSender::ReceiveSocket::transportSendImpl(): " << this << " [MEDIATRANSPORTS] WOULDBLOCK trying to send from PushToTalkUnicastSender::transportSendImpl(..)");
   }
}

void PushToTalkUnicastSender::ReceiveSocket::sendImpl(PushToTalkUnicastSender::DataMessage& message)
{
   //int targetCount = message.targets.size();
   // StackLog(<< "PushToTalkUnicastSender::sendImpl(): " << this << message);

   for (const boost::asio::ip::udp::endpoint& endpoint : message.targets)
   {
      try
      {
         if (message.message.size() > 4)
         {
            StackLog(<< "PushToTalkUnicastSender::sendImpl(): " << this << " sending message to endpoint: " << endpoint.address().to_string() << ": " << message);
         }
         transportSendImpl(endpoint, message.message);
      }
      catch (const boost::system::system_error& ex)
      {
         // ex.what() reporting "Invalid argument" can be a sign of no network being available
         WarningLog(<< "PushToTalkUnicastSender::sendImpl(): " << this << " send attempt threw exception " << ex.what());
      }
   }
   mLastSendTime = std::chrono::system_clock::now();

   // Filter to make sure that send completed is sent only after the last iteration
   if (message.enableHandler)
   {
      if (std::shared_ptr<PushToTalkUnicastSender> sender = mUnicastSender.lock())
      {
         sender->dataMessageSent(message);
      }
   }
}

void PushToTalkUnicastSender::ReceiveSocket::unsendImpl(unsigned int messageId)
{
   auto it = mQ.begin();
   while (it != mQ.end())
   {
      assert(it->targets[0] == mRemoteEndpoint);
      {
         if (it->messageId == messageId || messageId == UINT_MAX)
         {
            // StackLog(<< "PushToTalkUnicastSender::unsendImpl(): " << this << " messageId: " << messageId << " endpoint: " << mRemoteEndpoint << " queue-size: " << mQ.size() << " message: " << (*it).message);
            if (std::shared_ptr<PushToTalkUnicastSender> sender = mUnicastSender.lock())
            {
               sender->dataMessageSent(*it);
            }
            it = mQ.erase(it);
            continue;
         }
      }
      it++;
   }
   // StackLog(<< "PushToTalkUnicastSender::unsendImpl(): " << this << " queue-size: " << mQ.size());
}

void PushToTalkUnicastSender::ReceiveSocket::stopImpl()
{
   DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::stopImpl(): " << this << " with socket fd: " << mSocket.lowest_layer().native_handle());
   mState = ReceiveSocketState_WillBeDeleted;

   mQ.clear();
   boost::system::error_code ec;
   mSocket.shutdown(boost::asio::ip::udp::socket::shutdown_both, ec);
   mSocket.close(ec);
   if (ec)
   {
      DebugLog(<< "PushToTalkUnicastSender::ReceiveSocket::stopImpl(): " << this << " error closing socket: " << ec.value() << " (" << ec.message() << ")");
   }
   if (std::shared_ptr<PushToTalkSenderReceiveSocketHolder> holder = mHolder.lock())
   {
      holder->remove(mRemoteEndpoint); // Should trigger receive socket destruction
   }
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::PushToTalk::PushToTalkUnicastSender::DataMessage& message)
{
   os << " message: target-count: " << message.targets.size() << " message-id: " << message.messageId << " message-due-in: " << (std::chrono::duration_cast<std::chrono::milliseconds>(message.notBefore - std::chrono::system_clock::now())).count() << " payload: " << message.message;

   /*
   os << " message: target-count: " << message.targets.size() << " message-id: " << message.messageId
      << " retryCount: " << message.retryCount << " sendCompleteCount: " << message.sendCompleteCount <<
      << " targetIndex: " << message.targetIndex << " uniqueId: " << message.uniqueId << " payload: " << message.message;
   */

   /*
   os << " targets:";
   for (std::vector<boost::asio::ip::udp::endpoint>::const_iterator i = message.targets.begin(); i != message.targets.end(); ++i)
   {
      os << " " << (*i).address().to_string();
   }
   */
   return os;
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
