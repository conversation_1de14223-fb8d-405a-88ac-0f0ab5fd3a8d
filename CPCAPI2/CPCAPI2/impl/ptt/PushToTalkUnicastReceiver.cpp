#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkManagerImpl.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include <resip/stack/InternalTransport.hxx>
#include <rutil/Socket.hxx>
#include "../util/FileDescriptorMonitor.h"

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT


namespace CPCAPI2
{

namespace PushToTalk
{

std::map<CPCAPI2::Phone*, PushToTalkUnicast_NetworkImpairmentExperiment*> PushToTalkUnicast_NetworkImpairmentExperiment::s_expMap;
std::mutex PushToTalkUnicast_NetworkImpairmentExperiment::s_expMapMutex;

PushToTalkUnicast_NetworkImpairmentExperiment::PushToTalkUnicast_NetworkImpairmentExperiment()
{
}

void PushToTalkUnicast_NetworkImpairmentExperiment::CreateExperiment(CPCAPI2::Phone* p)
{
   std::unique_lock<std::mutex> lck(s_expMapMutex);
   PushToTalkUnicast_NetworkImpairmentExperiment* retVal = new PushToTalkUnicast_NetworkImpairmentExperiment();
   s_expMap[p] = retVal;
}

void PushToTalkUnicast_NetworkImpairmentExperiment::DestroyExperiment(CPCAPI2::Phone* p)
{
   std::unique_lock<std::mutex> lck(s_expMapMutex);
   auto i = s_expMap.find(p);
   if (i != s_expMap.end())
   {
      delete (i->second);
      s_expMap.erase(i);
   }
}

PushToTalkUnicast_NetworkImpairmentExperiment* PushToTalkUnicast_NetworkImpairmentExperiment::GetExperiment(CPCAPI2::Phone* p)
{
   std::unique_lock<std::mutex> lck(s_expMapMutex);
   auto it = s_expMap.find(p);
   if (it != s_expMap.end())
   {
      return it->second;
   }
   return NULL;
}

PushToTalkUnicastReceiver::PushToTalkUnicastReceiver(
   PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment_exp,
   boost::asio::io_context& io_context,
   const std::shared_ptr<PushToTalkUnicastReceiverHandler> handler,
   const std::string& bindAddr,
   const int listen_port) :
network_impairment_exp_(network_impairment_exp),
socket_(io_context),
bind_address_(bindAddr),
local_ip_(""),
listen_port_(listen_port),
running_(false),
handler_(handler),
loss_idx_(0),
recv_timer_(io_context)
{
   StackLog(<< "PushToTalkUnicastReceiver(): " << this << " bind-address: " << bind_address_ << " listen-port: " << listen_port);
}

PushToTalkUnicastReceiver::~PushToTalkUnicastReceiver()
{
   StackLog(<< "PushToTalkUnicastReceiver::~PushToTalkUnicastReceiver(): " << this << " receiver on address: " << (bind_address_.empty() ? local_ip_ : bind_address_) << " listen-port: " << listen_port_ << " destroyed");
   handler_.reset();
   recv_timer_.cancel();
}

void PushToTalkUnicastReceiver::start()
{
   // Create the socket so that multiple may be bound to the same address.
   std::unique_ptr<boost::asio::ip::udp::endpoint> listen_endpoint;
   if (bind_address_.empty())
   {
   /*
      resip::Data localIpAddress;
      CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
      listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::make_address_v4(localIpAddress.c_str()), listen_port_));
      DebugLog(<< "PushToTalkUnicastReceiver::start(): " << this << " listen-port: " << listen_port_ << " with local-ip: " << localIpAddress.c_str());
      */

      bool useLocalIp = false;

#if __APPLE__
#if TARGET_OS_IPHONE
      useLocalIp = false;
#else
      useLocalIp = true;
#endif
#elif WIN32
      useLocalIp = true;
#else
      useLocalIp = false;
#endif

      resip::Data localIpAddress;
      CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
      local_ip_ = localIpAddress.c_str();

      if (useLocalIp)
      {
         listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::make_address_v4(localIpAddress.c_str()), listen_port_));
         DebugLog(<< "PushToTalkUnicastReceiver::start(): " << this << " bind-address: " << bind_address_ << " listen-port: " << listen_port_ << " with local-ip: " << localIpAddress.c_str());
      }
      else
      {
         listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::udp::v4(), listen_port_));
         DebugLog(<< "PushToTalkUnicastReceiver::start(): " << this << " bind-address: " << bind_address_ << " listen-port: " << listen_port_);
      }
   }
   else
   {
      DebugLog(<< "PushToTalkUnicastReceiver::start(): " << this << " bind-address: " << bind_address_ << " listen-port: " << listen_port_ );
      listen_endpoint.reset(new boost::asio::ip::udp::endpoint(boost::asio::ip::make_address_v4(bind_address_), listen_port_));
   }

   // TODO: provide error callback to caller for socket errors
   boost::system::error_code ec;
   socket_.open(listen_endpoint->protocol(), ec);
   if (ec)
   {
      WarningLog(<< "PushToTalkUnicastReceiver::start(): " << this << " error: " << ec.message() << " when opening socket with listen-port: " << listen_port_ << " address: " << (bind_address_.empty() ? local_ip_ : bind_address_));
      return;
   }

   socket_.set_option(boost::asio::ip::udp::socket::reuse_address(true), ec);
   if (ec)
   {
      WarningLog(<< "PushToTalkUnicastReceiver::start(): " << this << " error: " << ec.message() << " when setting options for socket with listen-port: " << listen_port_ << " address: " << (bind_address_.empty() ? local_ip_ : bind_address_));
      socket_.close();
      return;
   }

   socket_.bind(*listen_endpoint, ec);
   if (ec)
   {
      WarningLog(<< "PushToTalkUnicastReceiver::start(): " << this << " error: " << ec.message() << " when binding socket with listen-port: " << listen_port_ << " address: " << (bind_address_.empty() ? local_ip_ : bind_address_));
      socket_.close();
      return;
   }

   if (network_impairment_exp_ != NULL)
   {
      for (int i = 0; i < 50; i++)
      {
         WarningLog(<< "PushToTalkUnicastReceiver::start(): " << this << " PushToTalkUnicastReceiver_NetworkImpairmentExperiment enabled!");
      }
      resetLossSchedule();
      recv_timer_.expires_from_now(boost::posix_time::milliseconds(10));
      recv_timer_.async_wait(std::bind(&PushToTalkUnicastReceiver::onRecvTimer, shared_from_this(), std::placeholders::_1));
   }
   running_ = true;
   do_receive();
}

void PushToTalkUnicastReceiver::stop()
{
   running_ = false;
}

void PushToTalkUnicastReceiver::resetLossSchedule()
{
   const unsigned int loss_pcnt = network_impairment_exp_->loss_pcnt();
   loss_schedule_.clear();
   loss_idx_ = 0;
   while (loss_schedule_.size() < loss_pcnt)
   {
      // a random number between 0 and 99 inclusive
      int r = std::abs(resip::Random::getCryptoRandom() % 100);
      loss_schedule_.insert(r);
   }
}

void PushToTalkUnicastReceiver::onRecvTimer(const boost::system::error_code& ec)
{
   if (ec)
   {
      return;
   }
   // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!

   boost::posix_time::ptime now = boost::posix_time::microsec_clock::local_time();
   while (!recv_queue_.empty())
   {
      const RecvItem& ri = recv_queue_.front();
      if (now >= ri.recv_time)
      {
         if (std::shared_ptr<PushToTalkUnicastReceiverHandler> handler = handler_.lock())
         {
            handler->onIncomingUnicast(ri.address, ri.port, ri.payload.c_str(), ri.payload.size());
         }
         recv_queue_.pop_front();
      }
      else
      {
         break;
      }
   }
   recv_timer_.expires_from_now(boost::posix_time::milliseconds(10));
   recv_timer_.async_wait(std::bind(&PushToTalkUnicastReceiver::onRecvTimer, shared_from_this(), std::placeholders::_1));
}

void PushToTalkUnicastReceiver::do_receive()
{
   // StackLog(<< "PushToTalkUnicastReceiver::do_receive() " << this);
   socket_.async_receive_from(
      boost::asio::buffer(data_), sender_endpoint_,
      [self = this->shared_from_this()](boost::system::error_code ec, std::size_t length)
   {
      if (ec.value() == boost::asio::error::operation_aborted)
         return;
      // !jjg! !!!! NO CODE BEFORE THIS LINE, NOT EVEN LOG STATEMENTS !!!! 'this' pointer can legitimately be NULL !!!!

      if (ec)
      {
         WarningLog(<< "PushToTalkUnicastReceiver::do_receive(): " << self.get() << " address: " << (self->bind_address_.empty() ? self->local_ip_ : self->bind_address_) << " listen-port: " << self->listen_port_ << " ec: " << ec.value() << " desc: " << ec.message());
      }
      else
      {
         if (std::shared_ptr<PushToTalkUnicastReceiverHandler> handler = self->handler_.lock())
         {
            if (self->network_impairment_exp_ != NULL && (self->network_impairment_exp_->loss_pcnt() > 0 || self->network_impairment_exp_->network_lag_min_ms() > 0))
            {
               if (self->loss_schedule_.find(self->loss_idx_++) == self->loss_schedule_.end())
               {
                  const int network_lag_min_ms = self->network_impairment_exp_->network_lag_min_ms();
                  const int network_lag_max_ms = self->network_impairment_exp_->network_lag_max_ms();
                  int r = std::abs(resip::Random::getCryptoRandom()) % (network_lag_max_ms - network_lag_min_ms + 1) + network_lag_min_ms;
                  boost::posix_time::ptime mst1 = boost::posix_time::microsec_clock::local_time() + boost::posix_time::milliseconds(r);
                  if (!self->recv_queue_.empty())
                  {
                     if (mst1 < self->recv_queue_.back().recv_time)
                     {
                        mst1 = self->recv_queue_.back().recv_time;
                     }
                  }
                  self->recv_queue_.push_back({ self->sender_endpoint_.address().to_string(), self->sender_endpoint_.port(), std::string(self->data_.data(), length), mst1 });
               }
               if (self->loss_idx_ == 100)
               {
                  self->resetLossSchedule();
               }
            }
            else
            {
               //
               resip::Data dataStr(resip::Data::Share, self->data_.data(), length);
               InfoLog(<< "PushToTalkUnicastReceiver::do_receive():  " << self.get() << " incoming message on: " << (self->bind_address_.empty() ? self->local_ip_ : self->bind_address_) << " listen-port: " << self->listen_port_ << " from: " << self->sender_endpoint_.address().to_string() << ":" << self->sender_endpoint_.port() << " data: " << dataStr);
               if (self->running_)
               {
                  handler->onIncomingUnicast(self->sender_endpoint_.address().to_string(), self->sender_endpoint_.port(), self->data_.data(), length);
               }
            }
         }
         else
         {
            InfoLog(<< "PushToTalkUnicastReceiver::do_receive():  " << self.get() << " invalid ptt manager, ignoring message on: " << (self->bind_address_.empty() ? self->local_ip_ : self->bind_address_) << " listen-port: " << self->listen_port_ << " from: " << self->sender_endpoint_.address().to_string() << ":" << self->sender_endpoint_.port());
         }
      }

      if (self->running_)
      {
         // InfoLog(<< "PushToTalkUnicastReceiver::do_receive():  " << this << " running listener on: " << bind_address_.c_str() << ":" << listen_port_);
         self->do_receive();
      }
   });
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
