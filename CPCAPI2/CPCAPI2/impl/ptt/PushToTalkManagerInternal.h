#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MANAGER_INTERNAL_H
#define CPCAPI2_PUSHTOTALK_MANAGER_INTERNAL_H

#include <ptt/PushToTalkManager.h>
#include <ptt/PushToTalkTypes.h>
#include <ptt/PushToTalkHandler.h>
#include <memory>

#define PTT_SESSION_CLIENT_CONNECTION_RETRY_MAXIMUM                       1
#define PTT_SESSION_STATUS_RESPONSE_THRESHOLD_PERCENTAGE                  50

#define PTT_SESSION_OUTGOING_STARTUP_DELAY_MSECS                          200
#define PTT_SESSION_WAIT_FOR_TALKSPURT_MSECS                              1500
#define PTT_SESSION_EXPIRY_INTERVAL_MSECS                                 500
#define PTT_SESSION_EXPIRY_INTERVAL_WAN_MSECS                             100
#define PTT_SESSION_CONNECTED_INTERVAL_MSECS                              1000
#define PTT_SESSION_CONNECTION_ERROR_INTERVAL_MSECS                       6000
#define PTT_SESSION_DISCONNECTED_INTERVAL_MSECS                           4000
#define PTT_SESSION_INCOMING_SETUP_INTERVAL_MSECS                         3000
#define PTT_SESSION_INCOMING_CONNECTION_RETRY_INTERVAL_MSECS              500
#define PTT_SESSION_INCOMING_CONNECTED_INTERVAL_MSECS                     8000
#define PTT_SESSION_INCOMING_WAIT_FOR_ANSWER_INTERVAL_MSECS               8000
#define PTT_SESSION_INCOMING_REJECTED_INTERVAL_MSECS                      10000
#define PTT_SESSION_UNICAST_ENDPOINT_LIST_INTERVAL_MSECS                  4000
#define PTT_SESSION_CHANNEL_ENDPOINT_STALE_INTERVAL_MSECS                 604800000 // 7 Days
#define PTT_SESSION_STALE_SESSION_INTERVAL_MSECS                          120000    // 2 Minutes
#define PTT_SERVICE_NETWORK_FAILURE_CONNECTION_INTERVAL_MSECS             20000
#define PTT_SERVICE_NETWORK_CHANGE_CONNECTION_INTERVAL_MSECS              25000
#define PTT_SESSION_NETWORK_WIFI_STATUS_INTERVAL_MSECS                    1000

#define PTT_SESSION_EXPIRY_TIMER_ID                                       1
#define PTT_SESSION_CONNECTED_TIMER_ID                                    2
#define PTT_SESSION_CONNECTION_ERROR_TIMER_ID                             3
#define PTT_SESSION_DISCONNECTED_TIMER_ID                                 4
#define PTT_SESSION_INIT_RETRY_TIMER_ID                                   5
#define PTT_SESSION_INCOMING_SETUP_TIMER_ID                               6
#define PTT_SESSION_INCOMING_CONNECTION_RETRY_TIMER_ID                    7
#define PTT_SESSION_INCOMING_CONNECTED_TIMER_ID                           8
#define PTT_SESSION_INCOMING_WAIT_FOR_ANSWER_TIMER_ID                     9
#define PTT_SESSION_UNICAST_ENDPOINT_LIST_TIMER_ID                        10
#define PTT_UNICAST_DELAY_INTERVAL_BETWEEN_TARGET_SEND_TIMER_ID           11
#define PTT_UNICAST_TRANSPORT_STARTUP_TIMER_ID                            12
#define PTT_UNICAST_DISCOVERY_SEND_TIMER_ID                               13
#define PTT_UNICAST_DISCOVERY_INITIAL_RETRY_TIMER_ID                      14
#define PTT_UNICAST_DISCOVERY_RETRY_TIMER_ID                              15
#define PTT_SESSION_INCOMING_REJECTED_TIMER_ID                            16
#define PTT_SERVICE_NETWORK_FAILURE_CONNECTION_TIMER_ID                   17
#define PTT_SERVICE_NETWORK_CHANGE_CONNECTION_TIMER_ID                    18
#define PTT_SESSION_WAIT_FOR_TALKSPURT_TIMER_ID                           19
#define PTT_SESSION_OUTGOING_STARTUP_DELAY_TIMER_ID                       20
#define PTT_SESSION_NETWORK_WIFI_STATUS_TIMER_ID                          21


#define PTT_UNICAST_MAXIMUM_TARGET_LIMIT                                  8192
#define PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT                           3
#define PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT_FOR_SOCKET_ERROR          3  // To handle EAGAIN/EWOULDBLOCK socket errors
#define PTT_UNICAST_MAXIMUM_MESSAGE_RETRY_LIMIT_FOR_PRE_ARP               5 // Used to specify the number of retries when an endpoint is being discovered for the first time
#define PTT_UNICAST_DEFAULT_NETWORK_MASK                                  "255.255.255.0"
#define PTT_UNICAST_DISCOVERY_TRANSACTION_HISTORY_LIMIT                   100
#define PTT_UNICAST_TRANSPORT_STARTUP_INTERVAL_MSECS                      10000
#define PTT_UNICAST_DISCOVERY_SEND_INTERVAL_MSECS                         120000
#define PTT_UNICAST_INITIAL_DISCOVERY_RETRY_INTERVAL_MSECS                6000
#define PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MSECS                        8000       // Used to specify when to retry discovery, applicable only when no endpoints have been discovered, the interval keeps on doubling upto maximum with each retry
#define PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MAXIMUM_MSECS                3600000
#define PTT_UNICAST_PTT_INIT_RETRY_INTERVAL_MSECS                         1000 // Used for init signalling messages that needs to be sent periodically

#define PTT_UNICAST_NEW_MAX_FILE_DESCRIPTORS                              64
#define PTT_CLOUD_PARTICIPANT_QUERY_FETCH_LIMIT                           50


#define PTT_SERVICE_REASON_WIFI_NOT_AVAILABLE                             "wifi network not available"
#define PTT_SERVICE_REASON_ERROR_STARTING_SENDER                          "error starting unicast transport sender"
#define PTT_SERVICE_REASON_ERROR_STARTING_RECEIVER                        "error starting unicast transport receiver"
#define PTT_SERVICE_REASON_NETWORK_CHANGE                                 "network change to "
#define PTT_SERVICE_REASON_CONFERENCE_CONNECTION_FAILURE                  "conference connection failure"
#define PTT_SERVICE_REASON_CONFERENCE_AUTHENTICATION_FAILURE              "conference authentication failure"
#define PTT_SESSION_REASON_INIT_RECORDING_FAILURE                         "init recording failure"
#define PTT_SESSION_END_REASON_NETWORK_CHANGE                             "network change"
#define PTT_SESSION_END_REASON_APP_REQUEST                                "app"
#define PTT_SESSION_END_REASON_MEDIA_INACTIVITY                           "media inactivity"
#define PTT_SESSION_END_REASON_REJECT                                     "reject"
#define PTT_SESSION_END_REASON_ALL_RECEIVERS_DISCONNECTED                 "all receivers disconnected"
#define PTT_SESSION_END_REASON_CHANNEL_OVERRIDE                           "channel override"
#define PTT_SESSION_END_REASON_CONNECTION_ERROR_TIMEOUT                   "connection error timeout"
#define PTT_SESSION_END_REASON_WAIT_FOR_APP_TIMEOUT                       "setup timeout"
#define PTT_SESSION_END_REASON_WAIT_FOR_ANSWER_TIMEOUT                    "wait for answer timeout"

#define StatLog(x,y) InfoLog(y << " log-id: " << x)
#define PTT_STAT_VERSION                                                  "1.0.0"
#define PTT_LOGID_START_SERVICE                                           1
#define PTT_LOGID_NETWORK_CHANGE                                          2
#define PTT_LOGID_SESSION_WIFI_STATUS                                     3
#define PTT_LOGID_SERVICE_WIFI_STATUS                                     4
#define PTT_LOGID_LAN_DISCOVERY_START                                     10
#define PTT_LOGID_LAN_DISCOVERY_END                                       11
#define PTT_LOGID_LAN_DISCOVERY_SYNC                                      12
#define PTT_LOGID_LAN_DISCOVERY_COUNT                                     13
#define PTT_LOGID_LAN_DISCOVERY_LIST                                      14
#define PTT_LOGID_WAN_CONFERENCE_STATUS_CHANGE                            20
#define PTT_LOGID_WAN_CONFERENCE_LIST_UPDATE                              21
#define PTT_LOGID_WAN_PARTICIPANT_LIST_UPDATE                             22
#define PTT_LOGID_SESSION_STATE_CHANGE                                    30
#define PTT_LOGID_SESSION_CREATE                                          31
#define PTT_LOGID_SESSION_START                                           32
#define PTT_LOGID_SESSION_END                                             33
#define PTT_LOGID_SESSION_END_INVALID_PTT_HANDLE                          34
#define PTT_LOGID_SESSION_RX_ACCEPT                                       40
#define PTT_LOGID_SESSION_RX_REJECT                                       41
#define PTT_LOGID_SESSION_RX_MUTE                                         42
#define PTT_LOGID_SESSION_RX_UNMUTE                                       43
#define PTT_LOGID_SESSION_RX_ACCEPT_INVALID_PTT_HANDLE                    44
#define PTT_LOGID_SESSION_RX_REJECT_INVALID_PTT_HANDLE                    45
#define PTT_LOGID_SESSION_RX_MEDIA_INACTIVITY                             46
#define PTT_LOGID_SESSION_RX_LAN_INIT                                     50
#define PTT_LOGID_SESSION_RX_LAN_END                                      51
#define PTT_LOGID_SESSION_RX_LAN_RECEIVER_ENDED                           52
#define PTT_LOGID_SESSION_RX_LAN_END_RECEIVED                             53
#define PTT_LOGID_SESSION_RX_WAN_ENDED                                    60
#define PTT_LOGID_SESSION_RX_WAN_START_CONFERENCE                         61
#define PTT_LOGID_SESSION_RX_WAN_START_CONFERENCE_WITH                    62
#define PTT_LOGID_SESSION_TX_ADD_RECIPIENT                                70
#define PTT_LOGID_SESSION_TX_SET_CHANNEL                                  71
#define PTT_LOGID_SESSION_TX_CONNECTION_TIMEOUT                           72
#define PTT_LOGID_SESSION_TX_CONNECTION_ERROR_TIMEOUT                     73
#define PTT_LOGID_SESSION_TX_START_SPURT                                  74
#define PTT_LOGID_SESSION_TX_END_SPURT                                    75
#define PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_NEW_CALL                    76
#define PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_EXISTING_CALL               77
#define PTT_LOGID_SESSION_TX_INIT_RECORDING                               78
#define PTT_LOGID_SESSION_TX_END                                          79
#define PTT_LOGID_SESSION_TX_LAN_CLIENT_OFFER_UPDATE                      80
#define PTT_LOGID_SESSION_TX_WAN_CLOUD_SESSION_CONNECTED                  90

namespace boost
{
namespace asio
{
   class io_context;
}
}

namespace CPCAPI2
{

namespace PushToTalk
{

struct PushToTalkSettingsInternal
{

public:

   PushToTalkSettingsInternal();

   bool unicastTransmitEnabled;
   bool unicastReceiveEnabled;
   bool mediaInactivityMonitorEnabled;
   bool participantUpdateListEnabled;
   bool ignoreIncomingInitiate;
   int participantListFetchLimit;
   int connectionRetryMaximum;
   int connectedEndpointThresholdPercentage;

   int outgoingSessionStartupDelayMsecs;
   int outgoingSessionWaitForTalkSpurtMsecs;
   int outgoingSessionExpiryMsecs;
   int outgoingConnectedMsecs;
   int outgoingConnectionErrorMsecs;
   int outgoingDisconnectedMsecs;
   int incomingSetupMsecs;
   int incomingConnectionRetryMsecs;
   int incomingConnectedMsecs;
   int incomingWaitForAnswerMsecs;
   int incomingRejectedMsecs;
   int unicastEndpointListIntervalMsecs;
   int unicastDiscoverySendIntervalMsecs;
   int unicastInitialDiscoveryRetryIntervalMsecs;
   int unicastDiscoveryRetryIntervalMsecs;
   int unicastDiscoveryRetryIntervalMaximumMsecs;
   int networkFailureConnectionIntervalMsecs;
   int networkChangeConnectionIntervalMsecs;
   int networkWifiStatusIntervalMsecs;
   int unicastInitRetryIntervalMsecs;

   void reset_outgoingSessionExpiryMsecs(PttServiceType serviceType);

#ifdef CPCAPI2_AUTO_TEST
   unsigned int* forcedTransactionID;
   unsigned int getTransactionID();
#endif

   struct IpTarget
   {
      cpc::string address;
      int port;
   };
   cpc::vector<int> unicastTargets;

   int maximumUnicastTargetLimit;
   int maximumUnicastMessageRetryLimit;
   cpc::string networkMaskOverride;

   int channelOverrideDurationMs = 1000;
};

std::ostream& operator<<(std::ostream& os, const PushToTalkSettingsInternal& evt);
std::string getDebugString(CPCAPI2::PushToTalk::PttReceiverEndReasonType reason);
CPCAPI2::PushToTalk::PttReceiverEndReasonType getReceiverEndReasonCode(std::string reason);
std::string getReceiverEndReasonCode(PushToTalk::PttReceiverEndReasonType reason);
std::string getSessionErrorCode(PushToTalk::PttSessionError errorCode);

class CPCAPI2_SHAREDLIBRARY_API PushToTalkManagerInternal
{

public:

   virtual int setPttInternalSettings(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings) = 0;
   virtual int enableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;
   virtual int disableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;
   virtual int enableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;
   virtual int disableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;
   virtual int dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable) = 0;
   virtual int disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) = 0;
   virtual int queryMediaStatistics(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) = 0;
   virtual int enableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) = 0;
   virtual int disableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) = 0;
   virtual int handleRawPttRequest(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest) = 0;
   virtual int addPttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler) = 0;
   virtual int removePttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler) = 0;
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) = 0;

   static void dropIncomingMediaPackets(bool enable);
   static int decodeProvisioningResponseInternal(const cpc::string& provisioningResponse, cpc::vector<PushToTalkSettingsInternal>& outSettingsInternal);

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MANAGER_INTERNAL_H
