#pragma once

#ifndef CPCAPI2_PUSHTOTALK_UNICAST_RECEIVER_H
#define CPCAPI2_PUSHTOTALK_UNICAST_RECEIVER_H

#include "cpcapi2defs.h"
#include "PushToTalkUnicast_NetworkImpairmentExperiment.h"

#include <deque>
#include <set>

// boost asio
#include <boost/asio.hpp>

namespace CPCAPI2
{
namespace PushToTalk
{
class PushToTalkManagerImpl;

class PushToTalkUnicastReceiverHandler
{
public:
   virtual void onReceiverStarted() = 0;
   virtual void onReceiverStopped() = 0;
   virtual void onIncomingUnicast(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen) = 0;
};

class PushToTalkUnicastReceiver : public std::enable_shared_from_this<PushToTalkUnicastReceiver>
{
public:
   PushToTalkUnicastReceiver(
      PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment_exp,
      boost::asio::io_context& io_context,
      const std::shared_ptr<PushToTalkUnicastReceiverHandler> handler,
      const std::string& bindAddr,
      const int listen_port);
   virtual~ PushToTalkUnicastReceiver();

   bool isRunning() const { return running_; }
   void start();
   void stop();

private:

   void do_receive();
   void resetLossSchedule();
   void onRecvTimer(const boost::system::error_code& ec);

   PushToTalkUnicast_NetworkImpairmentExperiment* network_impairment_exp_;
   boost::asio::ip::udp::socket socket_;
   std::string bind_address_;
   std::string local_ip_;
   int listen_port_;
   boost::asio::ip::udp::endpoint sender_endpoint_;
   std::array<char, 1480> data_;
   std::atomic_bool running_;
   std::atomic_bool started_sent_;
   std::atomic_bool stopped_sent_;
   std::weak_ptr<PushToTalkUnicastReceiverHandler> handler_;

   std::set<int> loss_schedule_;
   int loss_idx_;

   struct RecvItem
   {
      std::string address;
      unsigned short port;
      std::string payload;
      boost::posix_time::ptime recv_time;
   };
   std::deque<RecvItem> recv_queue_;
   boost::asio::deadline_timer recv_timer_;
};

}

}

#endif // CPCAPI2_PUSHTOTALK_UNICAST_RECEIVER_H
