#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkConferenceConnectorSession.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"
#include <resip/stack/Tuple.hxx>

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

#define PTT_CONF_TAG_SIP           1
#define PTT_CONF_TAG_XMPP          2


using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceConnector;
using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

/* TODO:
class PushToTalkCreateConferenceSessionHandlerDispatcher : public CPCAPI2::ConferenceConnector::CreateConferenceSessionHandler
{

public:

   PushToTalkCreateConferenceSessionHandlerDispatcher(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt, const std::function<int(CPCAPI2::PushToTalk::PushToTalkSessionHandle, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle, const CPCAPI2::ConferenceConnector::ConferenceSessionCreatedEvent&)>& fn) : mPtt(ptt), mFn(fn) {}
   virtual ~PushToTalkCreateConferenceSessionHandlerDispatcher() {}

   virtual int onConferenceSessionCreated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conference, const CPCAPI2::ConferenceConnector::ConferenceSessionCreatedEvent& evt) OVERRIDE
   {
      DebugLog(<< "PushToTalkCreateConferenceSessionHandlerDispatcher::onConferenceSessionCreated(): ptt: " << mPtt << " conference: " << conference << " participant: " << evt.participant);
      mFn(mPtt, conference, evt);
      delete this;
      return kSuccess;
   }

private:

   CPCAPI2::PushToTalk::PushToTalkSessionHandle mPtt;
   std::function<int(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle, const CPCAPI2::ConferenceConnector::ConferenceSessionCreatedEvent&)> mFn;

}; */

PttConferenceConnectorSessionStateFactory::PttConferenceConnectorSessionStateFactory(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
mManager(manager),
mSession(session)
{
}

PttConferenceConnectorSessionStateFactory::~PttConferenceConnectorSessionStateFactory()
{
   for (PttConferenceConnectorSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void PttConferenceConnectorSessionStateFactory::create()
{
   if (!mManager) return;

   if (mStates.size() > 0)
   {
      ErrLog(<< "PttConferenceConnectorSessionStateFactory::create(): " << this << " manager: " << mManager << " state factory already initialized");
      return;
   }


   mStates[PttConferenceConnectorStateType_Idle] = create(PttConferenceConnectorStateType_Idle);
   mStates[PttConferenceConnectorStateType_Setup] = create(PttConferenceConnectorStateType_Setup);
   mStates[PttConferenceConnectorStateType_Connecting] = create(PttConferenceConnectorStateType_Connecting);
   mStates[PttConferenceConnectorStateType_Authenticating] = create(PttConferenceConnectorStateType_Authenticating);
   mStates[PttConferenceConnectorStateType_Connected] = create(PttConferenceConnectorStateType_Connected);
   mStates[PttConferenceConnectorStateType_NetworkChange] = create(PttConferenceConnectorStateType_NetworkChange);
   mStates[PttConferenceConnectorStateType_NetworkChangeFailure] = create(PttConferenceConnectorStateType_NetworkChangeFailure);
   mStates[PttConferenceConnectorStateType_Disconnecting] = create(PttConferenceConnectorStateType_Disconnecting);
   mStates[PttConferenceConnectorStateType_Disconnected] = create(PttConferenceConnectorStateType_Disconnected);
   mStates[PttConferenceConnectorStateType_ConnFailure] = create(PttConferenceConnectorStateType_ConnFailure);
   mStates[PttConferenceConnectorStateType_AuthFailure] = create(PttConferenceConnectorStateType_AuthFailure);

   for (PttConferenceConnectorSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      StackLog(<< "PttConferenceConnectorSessionStateFactory::create(): " << PttConferenceConnectorSessionState::getName(i->first) << " : " << i->first);
   }
}

PttConferenceConnectorSessionState* PttConferenceConnectorSessionStateFactory::create(PttConferenceConnectorStateType type)
{
   PttConferenceConnectorSessionState* state = NULL;
   switch (type)
   {
      case PttConferenceConnectorStateType_Idle: state = new PttConferenceConnectorSessionIdleState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Setup: state = new PttConferenceConnectorSessionSetupState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Connecting: state = new PttConferenceConnectorSessionConnectingState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Authenticating: state = new PttConferenceConnectorSessionAuthenticatingState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Connected: state = new PttConferenceConnectorSessionConnectedState(mManager, mSession); break;
      case PttConferenceConnectorStateType_NetworkChange: state = new PttConferenceConnectorSessionNetworkChangeState(mManager, mSession); break;
      case PttConferenceConnectorStateType_NetworkChangeFailure: state = new PttConferenceConnectorSessionNetworkChangeFailureState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Disconnecting: state = new PttConferenceConnectorSessionDisconnectingState(mManager, mSession); break;
      case PttConferenceConnectorStateType_Disconnected: state = new PttConferenceConnectorSessionDisconnectedState(mManager, mSession); break;
      case PttConferenceConnectorStateType_ConnFailure: state = new PttConferenceConnectorSessionConnFailureState(mManager, mSession); break;
      case PttConferenceConnectorStateType_AuthFailure: state = new PttConferenceConnectorSessionAuthFailureState(mManager, mSession); break;
      default: break;
   }

   return state;
}

PttConferenceConnectorSessionState* PttConferenceConnectorSessionStateFactory::getState(PttConferenceConnectorStateType type)
{
   if (!mManager || mStates.size() == 0)
   {
      ErrLog(<< "PttConferenceConnectorSessionStateFactory::getState(): " << this << " manager: " << mManager << " state factory not initialized");
      return NULL;
   }

   if ((int)type >= mStates.size())
   {
      ErrLog(<< "PttConferenceConnectorSessionStateFactory::getState(): " << this << " manager: " << mManager << " invalid state-type: " << type);
      return NULL;
   }

   return mStates[type];
}

PttConferenceConnectorSessionState::PttConferenceConnectorSessionState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session, PttConferenceConnectorStateType state) :
mManager(manager),
mConfConnMgr(manager->getConferenceManager()),
mInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mSession(session),
mState(state),
mService(manager->getService())
{
}

PttConferenceConnectorSessionState::~PttConferenceConnectorSessionState()
{
}

PttConferenceConnectorStateType PttConferenceConnectorSessionState::getType()
{
   return mState;
}

std::string PttConferenceConnectorSessionState::getName()
{
   return (PttConferenceConnectorSessionState::getName(mState));
}

std::string PttConferenceConnectorSessionState::getName(PttConferenceConnectorStateType type)
{
   std::string name = "PttConferenceConnectorStateType_Invalid";
   switch (type)
   {
      case PttConferenceConnectorStateType_Idle: name = "PttConferenceConnectorStateType_Idle"; break;
      case PttConferenceConnectorStateType_Setup: name = "PttConferenceConnectorStateType_Setup"; break;
      case PttConferenceConnectorStateType_Connecting: name = "PttConferenceConnectorStateType_Connecting"; break;
      case PttConferenceConnectorStateType_Authenticating: name = "PttConferenceConnectorStateType_Authenticating"; break;
      case PttConferenceConnectorStateType_Connected: name = "PttConferenceConnectorStateType_Connected"; break;
      case PttConferenceConnectorStateType_NetworkChange: name = "PttConferenceConnectorStateType_NetworkChange"; break;
      case PttConferenceConnectorStateType_NetworkChangeFailure: name = "PttConferenceConnectorStateType_NetworkChangeFailure"; break;
      case PttConferenceConnectorStateType_Disconnecting: name = "PttConferenceConnectorStateType_Disconnecting"; break;
      case PttConferenceConnectorStateType_Disconnected: name = "PttConferenceConnectorStateType_Disconnected"; break;
      case PttConferenceConnectorStateType_ConnFailure: name = "PttConferenceConnectorStateType_ConnFailure"; break;
      case PttConferenceConnectorStateType_AuthFailure: name = "PttConferenceConnectorStateType_AuthFailure"; break;
      default: break;
   }

   return name;
}

void PttConferenceConnectorSessionState::setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated)
{
   std::stringstream sc;
   for (const cpc::string& channel : channels) sc << channel << " ";

   DebugLog(<< "PttConferenceConnectorSessionState::setChannelSubscriptions(): service: " << service << " server channel-count: " << mSession->mConfList.size() << " subscribed channel-count: " << channels.size() << " subscribed channels: " << sc.str().c_str());
}

void PttConferenceConnectorSessionState::startService(PushToTalkServiceHandle service)
{
   DebugLog(<< "PttConferenceConnectorSessionState::startService(): service: " << mService << " ignoring startup service request as not handled in current state: " << getName());
}

void PttConferenceConnectorSessionState::shutdownService(PushToTalkServiceHandle service)
{
   DebugLog(<< "PttConferenceConnectorSessionState::shutdownService(): service: " << mService << " shutdown service request received in current state: " << getName());
   if (mConfConnMgr && (mSession->mConfConnHdl > 0))
   {
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnecting);
      mConfConnMgr->disconnectFromConferenceService(mSession->mConfConnHdl);
      mSession->mDisconnectedConfConnHdl = mSession->mConfConnHdl;
      // mConfConnHdl = (ConferenceConnectorHandle)(-1);
   }
   mSession->changeState(PttConferenceConnectorStateType_Idle);
}

void PttConferenceConnectorSessionState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport << " in conference-connector session in current state: " << getName());
   mSession->mNetworkTransport = args.networkTransport;
   /*
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::stringstream ss;
   ss << PTT_SERVICE_REASON_NETWORK_CHANGE << args.networkTransport;
   sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
   if (mConfConnMgr && (mSession->mConfConnHdl > 0))
   {
      mConfConnMgr->destroyConferenceConnector(mConfConnHdl);
      mConfConnHdl = 0;
   }

   if (mSession->mNetworkTransport == TransportNone)
   {
      // Service status updates resulting from json client should update the ptt service status
      DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::handleNetworkChange(): " << this << " service: " << mService << " network: " << args.networkTransport << " service-status: " << mServiceStatus << " no handling required for change to disconnected network");
      mSession->changeState(PttConferenceConnectorStateType_Idle); return;
   }
   */
   mSession->changeState(PttConferenceConnectorStateType_NetworkChange);
}

// Associated to ConferenceConnector

void PttConferenceConnectorSessionState::onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " session in current state: " << getName() << " updated conference-status: " << args.connectionStatus);
   
   switch (args.connectionStatus)
   {
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connected: mSession->changeState(PttConferenceConnectorStateType_Connected); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_AuthFailure: mSession->changeState(PttConferenceConnectorStateType_AuthFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_ConnFailure: mSession->changeState(PttConferenceConnectorStateType_ConnFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Authenticating: mSession->changeState(PttConferenceConnectorStateType_Authenticating); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connecting: mSession->changeState(PttConferenceConnectorStateType_Connecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnecting: mSession->changeState(PttConferenceConnectorStateType_Disconnecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnected: mSession->changeState(PttConferenceConnectorStateType_Disconnected); break;
      default:
      {
         DebugLog(<< "PttConferenceConnectorSessionState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " invalid conference-status: " << args.connectionStatus);
         break;
      }
   }
}

void PttConferenceConnectorSessionState::onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args)
{
   // StackLog(<< "PttConferenceConnectorSessionState::onConferenceListUpdated(): service: " << mService << " conference-connector: " << conn << " ignoring callback as not handled in current state: " << getName());
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::string localIdentStr = mSession->getLocalIdentity();

   InfoLog(<< "PttConferenceConnectorSessionState::onConferenceListUpdated(): " << this << " local-identity: " << localIdentStr << " local-connector: " << mSession->mConfConnHdl << " update-connector: " << conn << " ConferenceListUpdatedEvent: {" << args << "}");
   mSession->handleConferenceListUpdated(args);
}

PttConferenceConnectorSessionIdleState::PttConferenceConnectorSessionIdleState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Idle)
{
}

PttConferenceConnectorSessionIdleState::~PttConferenceConnectorSessionIdleState()
{
}

void PttConferenceConnectorSessionIdleState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionIdleState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mSession->reset();
}

void PttConferenceConnectorSessionIdleState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionIdleState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

void PttConferenceConnectorSessionIdleState::startService(PushToTalkServiceHandle service)
{
   DebugLog(<< "PttConferenceConnectorSessionIdleState::startService(): service: " << mService << " startup service in conference-connector session in current state: " << getName() << " local-endpoint: " << mSession->getLocalIdentity());
   
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (!(settings.authServiceAddress.size() > 0 && settings.confServiceAddress.size() > 0))
   {
      InfoLog(<< "PttConferenceConnectorSessionIdleState::startService(): " << this << " service: " << mService << " ignoring start service as server address is not configured");
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Failure, PttServiceStatusChangedEvent::Reason_None, "server address not configured");
      return;
   }

   mSession->mConfConnHdl = mConfConnMgr->createConferenceConnector();
   ConferenceConnector::ConferenceConnectorSettings confConnSettings;
   confConnSettings.authServerUrl = settings.authServiceAddress;
   confConnSettings.authServerApiKey = settings.authServiceApiKey;
   confConnSettings.joinUrl = settings.confServiceAddress;
   confConnSettings.regionCode = "LOCAL";
   confConnSettings.username = settings.username;
   confConnSettings.password = settings.password;
   confConnSettings.ignoreCertVerification = settings.ignoreCertVerification;
   mConfConnMgr->setConnectionSettings(mSession->mConfConnHdl, confConnSettings);
   mConfConnMgr->connectToConferenceService(mSession->mConfConnHdl);

   mSession->changeState(PttConferenceConnectorStateType_Setup);
}

void PttConferenceConnectorSessionIdleState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionIdleState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport << " in conference-connector session in current state: " << getName());
   mSession->mNetworkTransport = args.networkTransport;
   mSession->changeState(PttConferenceConnectorStateType_Idle);
}

PttConferenceConnectorSessionSetupState::PttConferenceConnectorSessionSetupState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Setup)
{
}

PttConferenceConnectorSessionSetupState::~PttConferenceConnectorSessionSetupState()
{
}

void PttConferenceConnectorSessionSetupState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionSetupState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting);
}

void PttConferenceConnectorSessionSetupState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionSetupState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

/*
void PttConferenceConnectorSessionSetupState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionSetupState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport << " in conference-connector session in current state: " << getName());
   mSession->mNetworkTransport = args.networkTransport;
   mSession->changeState(PttConferenceConnectorStateType_Idle);
}
*/

PttConferenceConnectorSessionConnectingState::PttConferenceConnectorSessionConnectingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Connecting)
{
}

PttConferenceConnectorSessionConnectingState::~PttConferenceConnectorSessionConnectingState()
{
}

void PttConferenceConnectorSessionConnectingState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionConnectingState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting);
}

void PttConferenceConnectorSessionConnectingState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionConnectingState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

PttConferenceConnectorSessionAuthenticatingState::PttConferenceConnectorSessionAuthenticatingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Authenticating)
{
}

PttConferenceConnectorSessionAuthenticatingState::~PttConferenceConnectorSessionAuthenticatingState()
{
}

void PttConferenceConnectorSessionAuthenticatingState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionAuthenticatingState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mSession->mMyConferences.clear();
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Authenticating);
}

void PttConferenceConnectorSessionAuthenticatingState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionAuthenticatingState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

PttConferenceConnectorSessionConnectedState::PttConferenceConnectorSessionConnectedState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Connected)
{
}

PttConferenceConnectorSessionConnectedState::~PttConferenceConnectorSessionConnectedState()
{
}

void PttConferenceConnectorSessionConnectedState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionConnectedState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());

   PttServiceStartedEvent startedEvt;
   startedEvt.startupSuccessful = true;
   mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connected);
   mConfConnMgr->queryConferenceList(mSession->mConfConnHdl);
}

void PttConferenceConnectorSessionConnectedState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionConnectedState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

void PttConferenceConnectorSessionConnectedState::setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated)
{
   std::stringstream sc;
   for (const cpc::string& channel : channels) sc << channel << " ";

   DebugLog(<< "PttConferenceConnectorSessionIdleState::setChannelSubscriptions(): service: " << service << " server channel-count: " << mSession->mConfList.size() << " subscribed channel-count: " << channels.size() << " subscribed channels: " << sc.str().c_str());

   mSession->createMissingChannels(channels, conferencesCreated);
}

void PttConferenceConnectorSessionConnectedState::onConferenceListUpdated(ConferenceConnector::ConferenceConnectorHandle conn, const ConferenceConnector::ConferenceListUpdatedEvent& args)
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::string localIdentStr = mSession->getLocalIdentity();

   InfoLog(<< "PttConferenceConnectorSessionConnectedState::onConferenceListUpdated(): " << this << " local-identity: " << localIdentStr << " local-connector: " << mSession->mConfConnHdl << " update-connector: " << conn << " ConferenceListUpdatedEvent: {" << args << "}");
   
   mSession->handleConferenceListUpdated(args);
   
   if (mSession->mInSync)
   {
      DebugLog(<< "PttConferenceConnectorSessionConnectedState::onConferenceListUpdated(): " << this << " local-identity: " << localIdentStr << " local-connector: " << mSession->mConfConnHdl << " conference list in-sync with server");
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Ready);
   }
}

PttConferenceConnectorSessionNetworkChangeState::PttConferenceConnectorSessionNetworkChangeState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_NetworkChange),
mNetworkChangeConnectionTimer(mPhone->getSdkModuleThread())
{
}

PttConferenceConnectorSessionNetworkChangeState::~PttConferenceConnectorSessionNetworkChangeState()
{
}

void PttConferenceConnectorSessionNetworkChangeState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   // resetNetworkChangeConnectionTimer();
   handleNetworkChange();
}

void PttConferenceConnectorSessionNetworkChangeState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mNetworkChangeConnectionTimer.cancel();
}

void PttConferenceConnectorSessionNetworkChangeState::onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " session in current state: " << getName() << " updated conference-status: " << args.connectionStatus);
   
   switch (args.connectionStatus)
   {
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connected: mSession->changeState(PttConferenceConnectorStateType_Connected); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_AuthFailure: mSession->changeState(PttConferenceConnectorStateType_AuthFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_ConnFailure:
      {
         mSession->changeState(PttConferenceConnectorStateType_NetworkChangeFailure);
         break;
      }
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Authenticating: mSession->changeState(PttConferenceConnectorStateType_Authenticating); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connecting: mSession->changeState(PttConferenceConnectorStateType_Connecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnecting: mSession->changeState(PttConferenceConnectorStateType_Disconnecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnected: mSession->changeState(PttConferenceConnectorStateType_Disconnected); break;
      default:
      {
         DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " invalid conference-status: " << args.connectionStatus);
         break;
      }
   }
}

// DeadlineTimerHandler

void PttConferenceConnectorSessionNetworkChangeState::onTimer(unsigned short timerId, void* appState)
{
   // StackLog(<< "PttConferenceConnectorSessionNetworkChangeState::onTimer(): timerId: " << timerId);
   switch (timerId)
   {
      case PTT_SERVICE_NETWORK_CHANGE_CONNECTION_TIMER_ID: handleNetworkChangeConnectionTimeout(); break;
      default:
      {
         InfoLog(<< "PttConferenceConnectorSessionNetworkChangeState::::onTimer(): " << this << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
}

void PttConferenceConnectorSessionNetworkChangeState::resetNetworkChangeConnectionTimer()
{
   mNetworkChangeConnectionTimer.cancel();
   mNetworkChangeConnectionTimer.expires_from_now(mManager->getInternalSettings().networkChangeConnectionIntervalMsecs);
   mNetworkChangeConnectionTimer.async_wait(this, PTT_SERVICE_NETWORK_CHANGE_CONNECTION_TIMER_ID, NULL);
}

void PttConferenceConnectorSessionNetworkChangeState::handleNetworkChangeConnectionTimeout()
{
   InfoLog(<< "PttConferenceConnectorSessionNetworkChangeState::handleNetworkChangeConnectionTimeout(): " << this << " service: " << mService << " conference-connector: " << mSession->mConfConnHdl << " service-status: " << mManager->getServiceStatus());
   mSession->resetConferenceConnector();
}

/*
void PttConferenceConnectorSessionNetworkChangeState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport << " in conference-connector session in current state: " << getName());
   mSession->mNetworkTransport = args.networkTransport;
   mSession->changeState(PttConferenceConnectorStateType_NetworkChange);
}
*/

void PttConferenceConnectorSessionNetworkChangeState::handleNetworkChange()
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::handleNetworkChange(): " << this << " service: " << mService << " network: " << mSession->mNetworkTransport << " service-status: " << mManager->getServiceStatus());
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::stringstream ss;
   ss << PTT_SERVICE_REASON_NETWORK_CHANGE << mSession->mNetworkTransport;
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
   if (mConfConnMgr && (mSession->mConfConnHdl > 0))
   {
      mConfConnMgr->destroyConferenceConnector(mSession->mConfConnHdl);
      mSession->mConfConnHdl = 0;
   }

   if (mSession->mNetworkTransport == TransportNone)
   {
      // Service status updates resulting from json client should update the ptt service status, else it will be stuck in this state, maybe a seperate timer required
      DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::handleNetworkChange(): " << this << " service: " << mService << " network: " << mSession->mNetworkTransport << " service-status: " << mManager->getServiceStatus() << " no handling required for change to disconnected network");
      // TODO: should we go to idle state
   }
   else
   {
      resetNetworkChangeConnectionTimer();

      mSession->mConfConnHdl = mConfConnMgr->createConferenceConnector();
      ConferenceConnector::ConferenceConnectorSettings confConnSettings;
      confConnSettings.authServerUrl = settings.authServiceAddress;
      confConnSettings.authServerApiKey = settings.authServiceApiKey;
      confConnSettings.joinUrl = settings.confServiceAddress;
      confConnSettings.regionCode = "LOCAL";
      confConnSettings.username = settings.username;
      confConnSettings.password = settings.password;
      confConnSettings.ignoreCertVerification = settings.ignoreCertVerification;
      mConfConnMgr->setConnectionSettings(mSession->mConfConnHdl, confConnSettings);

      DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::handleNetworkChange(): " << this << " service: " << mService << " network: " << mSession->mNetworkTransport << " re-connecting to conference service");
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting, PttServiceStatusChangedEvent::Reason_NetworkChange, ss.str().c_str());
#ifdef CPCAPI2_AUTO_TEST
      mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttServiceRestarted), mService, PttServiceRestartedEvent(PttRestartReasonType_NetworkChange));
#endif
      mConfConnMgr->connectToConferenceService(mSession->mConfConnHdl);
   }
}

PttConferenceConnectorSessionNetworkChangeFailureState::PttConferenceConnectorSessionNetworkChangeFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_NetworkChangeFailure),
mNetworkFailureConnectionTimer(mPhone->getSdkModuleThread())
{
}

PttConferenceConnectorSessionNetworkChangeFailureState::~PttConferenceConnectorSessionNetworkChangeFailureState()
{
}

void PttConferenceConnectorSessionNetworkChangeFailureState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity() << " service: " << mService << " conference-connector: " << mSession->mConfConnHdl << " service-status: " << mManager->getServiceStatus());
   PttServiceStartedEvent startedEvt;
   startedEvt.startupSuccessful = false;
   mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
   // resetNetworkFailureConnectionTimer();

   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_CONFERENCE_CONNECTION_FAILURE);
   // TODO: any timer required to get out of this state, or just depend on the json client callbacks
}

void PttConferenceConnectorSessionNetworkChangeFailureState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mNetworkFailureConnectionTimer.cancel();
}

// DeadlineTimerHandler

void PttConferenceConnectorSessionNetworkChangeFailureState::onTimer(unsigned short timerId, void* appState)
{
   // StackLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::onTimer(): timerId: " << timerId);
   switch (timerId)
   {
      case PTT_SERVICE_NETWORK_FAILURE_CONNECTION_TIMER_ID: handleNetworkFailureConnectionTimeout(); break;
      default:
      {
         InfoLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::::onTimer(): " << this << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
}

void PttConferenceConnectorSessionNetworkChangeFailureState::resetNetworkFailureConnectionTimer()
{
   mNetworkFailureConnectionTimer.cancel();
   mNetworkFailureConnectionTimer.expires_from_now(mManager->getInternalSettings().networkFailureConnectionIntervalMsecs);
   mNetworkFailureConnectionTimer.async_wait(this, PTT_SERVICE_NETWORK_FAILURE_CONNECTION_TIMER_ID, NULL);
}

void PttConferenceConnectorSessionNetworkChangeFailureState::handleNetworkFailureConnectionTimeout()
{
   InfoLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::handleNetworkFailureConnectionTimeout(): " << this << " service: " << mService << " conference-connector: " << mSession->mConfConnHdl << " service-status: " << mManager->getServiceStatus());
   mSession->resetConferenceConnector();
}

/*
void PttConferenceConnectorSessionNetworkChangeFailureState::onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " session in current state: " << getName() << " updated conference-status: " << args.connectionStatus);
   
   switch (args.connectionStatus)
   {
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connected: mSession->changeState(PttConferenceConnectorStateType_Connected); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_AuthFailure: mSession->changeState(PttConferenceConnectorStateType_AuthFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_ConnFailure: mSession->changeState(PttConferenceConnectorStateType_NetworkChangeFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Authenticating: mSession->changeState(PttConferenceConnectorStateType_Authenticating); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connecting: mSession->changeState(PttConferenceConnectorStateType_Connecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnecting: mSession->changeState(PttConferenceConnectorStateType_Disconnecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnected: mSession->changeState(PttConferenceConnectorStateType_Disconnected); break;
      default:
      {
         DebugLog(<< "PttConferenceConnectorSessionNetworkChangeFailureState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " invalid conference-status: " << args.connectionStatus);
         break;
      }
   }
}
*/

/*
void PttConferenceConnectorSessionNetworkChangeState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionNetworkChangeState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport << " in conference-connector session in current state: " << getName());
   mSession->mNetworkTransport = args.networkTransport;
   mSession->changeState(PttConferenceConnectorStateType_NetworkChange);
}
*/

PttConferenceConnectorSessionDisconnectingState::PttConferenceConnectorSessionDisconnectingState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Disconnecting)
{
}

PttConferenceConnectorSessionDisconnectingState::~PttConferenceConnectorSessionDisconnectingState()
{
}

void PttConferenceConnectorSessionDisconnectingState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionDisconnectingState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnecting);
}

void PttConferenceConnectorSessionDisconnectingState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionDisconnectingState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

PttConferenceConnectorSessionDisconnectedState::PttConferenceConnectorSessionDisconnectedState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_Disconnected)
{
}

PttConferenceConnectorSessionDisconnectedState::~PttConferenceConnectorSessionDisconnectedState()
{
}

void PttConferenceConnectorSessionDisconnectedState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionDisconnectedState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected);
}

void PttConferenceConnectorSessionDisconnectedState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionDisconnectedState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

PttConferenceConnectorSessionConnFailureState::PttConferenceConnectorSessionConnFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_ConnFailure),
mNetworkFailureConnectionTimer(mPhone->getSdkModuleThread())
{
}

PttConferenceConnectorSessionConnFailureState::~PttConferenceConnectorSessionConnFailureState()
{
}

void PttConferenceConnectorSessionConnFailureState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionConnFailureState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   PttServiceStartedEvent startedEvt;
   startedEvt.startupSuccessful = false;
   mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

   DebugLog(<< "PttConferenceConnectorSessionConnFailureState::onEntry(): " << this << " service: " << mService << " conference-connector: " << mSession->mConfConnHdl << " service-status: " << mManager->getServiceStatus());

   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, PTT_SERVICE_REASON_CONFERENCE_CONNECTION_FAILURE);
   resetNetworkFailureConnectionTimer();
}

void PttConferenceConnectorSessionConnFailureState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionConnFailureState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   mNetworkFailureConnectionTimer.cancel();
}

// DeadlineTimerHandler

void PttConferenceConnectorSessionConnFailureState::onTimer(unsigned short timerId, void* appState)
{
   // StackLog(<< "PttConferenceConnectorSessionConnFailureState::onTimer(): timerId: " << timerId);
   switch (timerId)
   {
      case PTT_SERVICE_NETWORK_FAILURE_CONNECTION_TIMER_ID: handleNetworkFailureConnectionTimeout(); break;
      default:
      {
         InfoLog(<< "PttConferenceConnectorSessionConnFailureState::::onTimer(): " << this << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
}

void PttConferenceConnectorSessionConnFailureState::resetNetworkFailureConnectionTimer()
{
   mNetworkFailureConnectionTimer.cancel();
   mNetworkFailureConnectionTimer.expires_from_now(mManager->getInternalSettings().networkFailureConnectionIntervalMsecs);
   mNetworkFailureConnectionTimer.async_wait(this, PTT_SERVICE_NETWORK_FAILURE_CONNECTION_TIMER_ID, NULL);
}

void PttConferenceConnectorSessionConnFailureState::handleNetworkFailureConnectionTimeout()
{
   InfoLog(<< "PttConferenceConnectorSessionConnFailureState::handleNetworkFailureConnectionTimeout(): " << this << " service: " << mService << " conference-connector: " << mSession->mConfConnHdl << " service-status: " << mManager->getServiceStatus());
   mSession->resetConferenceConnector();
}

/*
void PttConferenceConnectorSessionConnFailureState::onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args)
{
   DebugLog(<< "PttConferenceConnectorSessionConnFailureState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " session in current state: " << getName() << " updated conference-status: " << args.connectionStatus);
   
   switch (args.connectionStatus)
   {
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connected: mSession->changeState(PttConferenceConnectorStateType_Connected); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_AuthFailure: mSession->changeState(PttConferenceConnectorStateType_AuthFailure); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_ConnFailure:
      {
         mSession->changeState(PttConferenceConnectorStateType_NetworkChangeFailure);
         break;
      }
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Authenticating: mSession->changeState(PttConferenceConnectorStateType_Authenticating); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Connecting: mSession->changeState(PttConferenceConnectorStateType_Connecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnecting: mSession->changeState(PttConferenceConnectorStateType_Disconnecting); break;
      case CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnected: mSession->changeState(PttConferenceConnectorStateType_Disconnected); break;
      default:
      {
         DebugLog(<< "PttConferenceConnectorSessionConnFailureState::onServiceConnectionStatusChanged(): service: " << mService << " conference-connector: " << conn << " invalid conference-status: " << args.connectionStatus);
         break;
      }
   }
}
*/
PttConferenceConnectorSessionAuthFailureState::PttConferenceConnectorSessionAuthFailureState(PushToTalkManagerImpl* manager, PttConferenceConnectorSession* session) :
PttConferenceConnectorSessionState(manager, session, PttConferenceConnectorStateType::PttConferenceConnectorStateType_AuthFailure)
{
}

PttConferenceConnectorSessionAuthFailureState::~PttConferenceConnectorSessionAuthFailureState()
{
}

void PttConferenceConnectorSessionAuthFailureState::onEntry()
{
   DebugLog(<< "PttConferenceConnectorSessionAuthFailureState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
   PttServiceStartedEvent startedEvt;
   startedEvt.startupSuccessful = false;
   mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
   mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Failure, PttServiceStatusChangedEvent::Reason_AuthenticationFailure, PTT_SERVICE_REASON_CONFERENCE_AUTHENTICATION_FAILURE);
}

void PttConferenceConnectorSessionAuthFailureState::onExit()
{
   DebugLog(<< "PttConferenceConnectorSessionAuthFailureState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->getLocalIdentity());
}

PttConferenceConnectorSession::PttConferenceConnectorSession(PushToTalkManagerImpl* manager) :
mState(PttConferenceConnectorStateType_Idle),
mService(manager->getService()),
mManager(manager),
mInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mConfConnMgr(manager->getConferenceManager()),
mConfConnHdl((ConferenceConnector::ConferenceConnectorHandle)(-1)),
mDisconnectedConfConnHdl((ConferenceConnector::ConferenceConnectorHandle)(-1)),
mFactory(manager, this),
mInSync(false),
mNetworkTransport(CPCAPI2::TransportNone)
{
   mFactory.create();

   DebugLog(<< "PttConferenceConnectorSession(): " << this << " created discovery session for ptt manager: " << mManager);
}

PttConferenceConnectorSession::~PttConferenceConnectorSession()
{
   DebugLog(<< "~PttConferenceConnectorSession(): " << this << " destroyed discovery session for ptt manager: " << mManager << " local-endpoint: " << getLocalIdentity().c_str());
}
 
void PttConferenceConnectorSession::reset()
{
   mState = PttConferenceConnectorStateType_Idle;
   mConfConnHdl = (ConferenceConnector::ConferenceConnectorHandle)(-1);
   mDisconnectedConfConnHdl = (ConferenceConnector::ConferenceConnectorHandle)(-1);
   mMyConferences.clear();
   mConfList.clear();
   mInSync = false;
   mNetworkTransport = CPCAPI2::TransportNone;
}

std::string PttConferenceConnectorSession::getLocalIdentity()
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::string identity = ((settings.localIdentities.size() > 0) ? settings.localIdentities.begin()->userName : "").c_str();
   return identity;
}

CPCAPI2::ConferenceConnector::CloudConferenceHandle PttConferenceConnectorSession::getCloudConference(std::string& identity)
{
   CPCAPI2::ConferenceConnector::CloudConferenceHandle conference = (CPCAPI2::ConferenceConnector::CloudConferenceHandle)-1;
   for (auto i = mConfList.begin(); i != mConfList.end(); ++i)
   {
      StackLog(<< "PttConferenceConnectorSession::getCloudConference: " << this << " check identity: " << identity << " conference list contains: " << i->displayName);
      if (i->displayName == identity.c_str())
      {
         conference = i->conference;
         break;
      }
   }

   return conference;
}

bool PttConferenceConnectorSession::isSubscribedChannel(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, std::string& channelDisplayName)
{
   auto itConfList = mConfList.begin();
   for (; itConfList != mConfList.end(); ++itConfList)
   {
      // StackLog(<< "PttConferenceConnectorSession::isSubscribedChannel(): local-identity: " << localIdentStr << " conference: " << itConfList->conference << " name: " << itConfList->displayName);
      if (itConfList->conference == conference)
      {
         InfoLog(<< "PttConferenceConnectorSession::isSubscribedChannel(): " << this << " conference name: " << itConfList->displayName);
         break;
      }
   }

   if (itConfList != mConfList.end())
   {
      for (const cpc::string& subs : mManager->getSubscribedChannels())
      {
         if (subs == itConfList->displayName)
         {
            InfoLog(<< "PttConferenceConnectorSession::isSubscribedChannel(): " << this << " local identity: " << getLocalIdentity() << ", " << subs << " is a subscribed channel");
            channelDisplayName = itConfList->displayName;
            return true;
         }
      }
   }

   return false;
}

void PttConferenceConnectorSession::changeState(PttConferenceConnectorStateType newStateType)
{
   PttConferenceConnectorStateType currentStateType = mState;
   InfoLog(<< "PttConferenceConnectorSession::changeState(): " << this << " service: " << mService << " changing state of conference-connector session from: " << PttConferenceConnectorSessionState::getName(currentStateType) << " to: " << PttConferenceConnectorSessionState::getName(newStateType));

   PttConferenceConnectorSessionState* currentState = mFactory.getState(currentStateType);
   if (currentState)
   {
      currentState->onExit();
   }

   mState = newStateType;

   PttConferenceConnectorSessionState* newState = mFactory.getState(newStateType);
   if (newState)
   {
      newState->onEntry();
   }
}

void PttConferenceConnectorSession::setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated)
{
   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   DebugLog(<< "PttConferenceConnectorSession::setChannelSubscriptions(): service: " << service << " state: " << state << " (" << (state ? state->getName() : "invalid").c_str() << ")");
   if (state) state->setChannelSubscriptions(service, channels, conferencesCreated);
}

void PttConferenceConnectorSession::startService(PushToTalkServiceHandle service)
{
   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   DebugLog(<< "PttConferenceConnectorSession::startService(): service: " << service << " state: " << state << "(" << (state ? state->getName() : "invalid") << ")");
   if (state) state->startService(service);
}

void PttConferenceConnectorSession::shutdownService(PushToTalkServiceHandle service)
{
   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   if (state) state->shutdownService(service);
}

void PttConferenceConnectorSession::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   DebugLog(<< "PttConferenceConnectorSession::onNetworkChange(): service: " << mService << " state: " << state << "(" << (state ? state->getName() : "invalid") << ")");
   if (state) state->onNetworkChange(args);
}

void PttConferenceConnectorSession::dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable)
{
   InfoLog(<< "PttConferenceConnectorSession::dropIncomingJsonMessages(): " << this << " service: " << service << " enable: " << enable);
   if (mConfConnMgr && (mConfConnHdl > 0))
   {
      CPCAPI2::ConferenceConnector::ConferenceConnectorInterface* confConnMgrIf = dynamic_cast<CPCAPI2::ConferenceConnector::ConferenceConnectorInterface*>(mConfConnMgr);
      if (confConnMgrIf)
      {
         confConnMgrIf->dropIncomingJsonMessages(mConfConnHdl, enable);
      }
   }
}

void PttConferenceConnectorSession::disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   InfoLog(<< "PttConferenceConnectorSession::disconnectWanConnection(): " << this << " service: " << service);
   if (mConfConnMgr && (mConfConnHdl > 0))
   {
      CPCAPI2::ConferenceConnector::ConferenceConnectorInterface* confConnMgrIf = dynamic_cast<CPCAPI2::ConferenceConnector::ConferenceConnectorInterface*>(mConfConnMgr);
      if (confConnMgrIf)
      {
         confConnMgrIf->destroyConferenceConnector(mConfConnHdl);
         mConfConnHdl = 0;
      }
   }
}

int PttConferenceConnectorSession::onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args)
{
   if (conn != mConfConnHdl)
   {
      InfoLog(<< "PttConferenceConnectorSession::onServiceConnectionStatusChanged(): " << this << " local-identity: " << getLocalIdentity() << " current conference-connector: " << mConfConnHdl << " ignore conference-connector status update for conference-connector: " << conn << " due to handle mismatch, disconnected connection: " << mDisconnectedConfConnHdl);
      
      // Presuming the mismatched connection is dangling and destroy it
      if ((conn == mDisconnectedConfConnHdl) && (args.connectionStatus != CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnecting) && (args.connectionStatus != CPCAPI2::ConferenceConnector::ServiceConnectionStatus_Disconnected))
      {
         InfoLog(<< "PttConferenceConnectorSession::onServiceConnectionStatusChanged(): local-identity: " << getLocalIdentity() << " destroying disconnected connection: " << mDisconnectedConfConnHdl);
         // TODO: Should we destroy the conference
         // mConfConnMgr->destroyConferenceConnector(conn);
         mDisconnectedConfConnHdl = 0;
      }

      return kSuccess;
   }

   if (!mConfConnMgr)
   {
      InfoLog(<< "PttConferenceConnectorSession::onServiceConnectionStatusChanged(): conference manager not initialized - update conference-connector: " << conn);
      return kError;
   }

   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   if (state) state->onServiceConnectionStatusChanged(conn, args);
   return kSuccess;
}

int PttConferenceConnectorSession::onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args)
{
   if (conn != mConfConnHdl)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceListUpdated(): " << this << " local-identity: " << getLocalIdentity() << " current conference-connector: " << mConfConnHdl << " ignore conference-connector list update for conference-connector: " << conn);
      return kSuccess;
   }

   if (!mConfConnMgr)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceListUpdated(): conference manager not initialized - update conference-connector: " << conn);
      return kError;
   }

   PttConferenceConnectorSessionState* state = mFactory.getState(mState);
   if (state) state->onConferenceListUpdated(conn, args);
   return kSuccess;
}

int PttConferenceConnectorSession::onConferenceCreated(ConferenceConnector::ConferenceConnectorHandle conn, const ConferenceConnector::ConferenceCreatedEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceCreated(): " << this << " conference manager not initialized - conference-connector: " << conn);
      return kError;
   }

   if (conn == mConfConnHdl)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceCreated():  " << this << " local-identity: " << getLocalIdentity() << " conference: " << args.conference << " name: " << args.displayName.c_str());
      mConfConnMgr->queryConferenceList(mConfConnHdl);
   }
   return kSuccess;
}

int PttConferenceConnectorSession::onConferenceEnded(ConferenceConnector::ConferenceConnectorHandle conn, const ConferenceConnector::ConferenceEndedEvent& args)
{
   if (!mConfConnMgr)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceEnded(): " << this << " conference manager not initialized - conference-connector: " << conn);
      return kError;
   }

   if (conn == mConfConnHdl)
   {
      InfoLog(<< "PttConferenceConnectorSession::onConferenceEnded(): local-identity: " << getLocalIdentity() << " conference: " << args.conference << " name: " << args.displayName.c_str());
   }
   return kSuccess;
}

CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle PttConferenceConnectorSession::startConfSessionWith(const cpc::string& ident, PushToTalkSessionHandle pttSession, PttIdentityType identityType, CPCAPI2::ConferenceConnector::CloudConferenceHandle& conf)
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   auto it = mConfList.begin();
   for (; it != mConfList.end(); ++it)
   {
      StackLog(<< "PttConferenceConnectorSession::startConfSessionWith: " << this << " ptt session: " << pttSession << ", conference list contains: " << it->displayName);
      if (it->displayName == ident)
      {
         break;
      }
   }

   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle mySession = 0;
   // std::stringstream ss;
   // for (auto i = mConfList.begin(); i != mConfList.end(); ++i) { ss << i->displayName << ","; }
   // StackLog(<< "PttConferenceConnectorSession::startConfSessionWith: " << this << " request to start conference with identity: " << ident << " on ptt session: " << pttSession << " with conference-list size: " << mConfList.size() << ", {" << ss.str() << "}");
   DebugLog(<< "PttConferenceConnectorSession::startConfSessionWith: " << this << " ptt session: " << pttSession << ", request to start conference with identity: " << ident << " (" << ((it == mConfList.end()) ? "not-" : "") << "found) in conference-list size: " << mConfList.size());

   if (it != mConfList.end())
   {
      conf = it->conference;

      ConferenceConnector::CloudConferenceSessionSettings confSessSettings;

      cpc::vector<PttIdentity>::const_iterator liIt = settings.localIdentities.begin();
      for (; liIt != settings.localIdentities.end(); ++liIt)
      {
         if (liIt->identityType == identityType)
            break;
      }
      if (liIt == settings.localIdentities.end())
      {
         liIt = settings.localIdentities.begin();
      }

      confSessSettings.address = liIt->userName;
      confSessSettings.displayName = liIt->displayName;
      if (liIt->identityType == PttIdentityType_SIP)
      {
         confSessSettings.tags.push_back(PTT_CONF_TAG_SIP);
      }
      else if (liIt->identityType == PttIdentityType_XMPP)
      {
         confSessSettings.tags.push_back(PTT_CONF_TAG_XMPP);
      }
      confSessSettings.role = CloudConferenceRole_Host;
      confSessSettings.addToFloor = true;
      confSessSettings.mediaDscp = settings.mediaDscp;
      mySession = mConfConnMgr->createConferenceSession(it->conference, confSessSettings);
      // mManager->setMyConfSession(mySession);
      InfoLog(<< "PttConferenceConnectorSession::startConfSessionWith(): session handle: " << mySession);
      ConferenceConnector::CloudConferenceSessionMediaSettings confSessMediaSettings;
      confSessMediaSettings.audioDirection = CPCAPI2::ConferenceConnector::MediaDirection_SendRecv;
      confSessMediaSettings.videoDirection = CPCAPI2::ConferenceConnector::MediaDirection_None;
      mConfConnMgr->setSessionMediaSettings(mySession, confSessMediaSettings);
      mConfConnMgr->startSession(mySession);
   }

   return mySession;
}

CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle PttConferenceConnectorSession::startConfSessionWithMyConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conf) // TODO: CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conf)
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle mySession = 0;
   std::string identity = getLocalIdentity();

   DebugLog(<< "PttConferenceConnectorSession::startConfSessionWithMyConference: " << this << " request to start conference with my identity: " << identity << " in conference-list size: " << mConfList.size());

   CloudConferenceSessionSettings confSessSettings;
   confSessSettings.address = ((settings.localIdentities.size() > 0) ? settings.localIdentities.begin()->userName : "");
   confSessSettings.displayName = ((settings.localIdentities.size() > 0) ? settings.localIdentities.begin()->displayName : "");
   confSessSettings.role = CloudConferenceRole_Participant;
   confSessSettings.mediaDscp = settings.mediaDscp;

   mySession = mConfConnMgr->createConferenceSession(conf, confSessSettings); /* TODO: , new PushToTalkCreateConferenceSessionHandlerDispatcher(ptt, [this](PushToTalkSessionHandle pttSession, ConferenceConnectorHandle conference, const ConferenceSessionCreatedEvent& evt)
   {
      if (evt.success)
      {
         mManager->setCloudConferenceParticipant(pttSession, evt.participant);
      }
      return kSuccess;
   })); */

   CloudConferenceSessionMediaSettings confSessMediaSettings;
   confSessMediaSettings.audioDirection = CPCAPI2::ConferenceConnector::MediaDirection_RecvOnly;
   confSessMediaSettings.videoDirection = CPCAPI2::ConferenceConnector::MediaDirection_None;

   mConfConnMgr->setSessionMediaSettings(mySession, confSessMediaSettings);

   if (mManager->getInternalSettings().mediaInactivityMonitorEnabled && (mManager->getServiceSettings().mediaInactivityIntervalSeconds > 0))
   {
      dynamic_cast<ConferenceConnectorInterface*>(mConfConnMgr)->setMediaInactivityMonitor(mySession, true, mManager->getServiceSettings().mediaInactivityIntervalSeconds * 1000);
   }

   /*
    struct ConferenceSessionCreatedEvent
    {
       bool success = false;
       ConferenceConnectorHandle connector;
       CloudConferenceHandle conference;
       CloudConferenceSessionHandle cloudSession;
       CloudConferenceParticipantHandle cloudParticipant;
       unsigned int peerConnection = -1;
       cpc::string address;
    };
    
    it->second->createConferenceSession(conference, session, settings, new CreateConference);
    
    mSessStateMap[session].part = mConfBridgeMgr->createWebParticipant(it->confHandle, identityInfo, settings.addToFloor, new CreateWebParticipantHandlerDispatcher([conference, session, this](ConferenceHandle conf, const WebParticipantCreatedEvent& evt) {
       auto itConf = getConferenceState(conference);
       if (evt.success)
       {
          itConf->part = mSessStateMap[session].part;
          itConf->hasFloor = evt.hasFloor;
       }
       else
       {
          endSession(session, false);

          CPCAPI2::ConferenceBridge::ConferenceEndedEvent confEndedEvt;
          onConferenceEnded(conf, confEndedEvt);
       }
       return kSuccess;
    }));
    */
   mConfConnMgr->startSession(mySession);

   return mySession;
}

void PttConferenceConnectorSession::endConfSession(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   mConfConnMgr->endSession(session);
   // mManager->setMyConfSession(0);
}

void PttConferenceConnectorSession::doConferenceSessionMute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   mConfConnMgr->pauseSessionPlayout(session);
}

void PttConferenceConnectorSession::doConferenceSessionUnmute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session)
{
   mConfConnMgr->resumeSessionPlayout(session);
}

void PttConferenceConnectorSession::printCurrentConferences()
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::string localIdentStr = getLocalIdentity();

   for (auto i = mConfList.begin(); i != mConfList.end(); ++i)
   {
      InfoLog(<< "PttConferenceConnectorSession::printCurrentConferences(): " << this << " conference-connector: " << mConfConnHdl << " ConferenceListUpdatedEvent: {" << (*i) << "}");
   }

   cpc::vector<cpc::string> myConferences;
   for (auto il = settings.localIdentities.begin(); il != settings.localIdentities.end(); ++il)
   {
      myConferences.push_back(il->userName.c_str());
   }
   std::stringstream mc;
   for (const cpc::string& myconf : myConferences) mc << myconf.c_str() << " ";
   InfoLog(<< "PttConferenceConnectorSession::printCurrentConferences(): " << this << " my " << myConferences.size() << " identity conferences: " << mc.str().c_str());
}

void PttConferenceConnectorSession::getMyMissingConferences(cpc::vector<PttIdentity>& myMissingConferences)
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   cpc::vector<cpc::string> myExistingConferences;
   for (auto it = mConfList.begin(); it != mConfList.end(); ++it)
   {
      for (auto il = settings.localIdentities.begin(); il != settings.localIdentities.end(); ++il)
      {
         DebugLog(<< "PttConferenceConnectorSession::getMyMissingConferences(): " << this << " local-identity: " << il->userName << " conference-identity: " << it->displayName);
         if (il->userName == it->displayName)
         {
            DebugLog(<< "PttConferenceConnectorSession::getMyMissingConferences(): " << this << " local-identity: " << il->userName << " conference-identity: " << it->displayName << " added to my conference list");
            mMyConferences.insert(it->conference);
            myExistingConferences.push_back(it->displayName.c_str());
         }
      }
   }

   for (const PttIdentity& myconf : settings.localIdentities)
   {
      bool found = false;
      for (const cpc::string& existingconf : myExistingConferences)
      {
         if (myconf.userName == existingconf)
         {
            found = true;
            break;
         }
      }

      if (!found)
      {
         InfoLog(<< "PttConferenceConnectorSessionStartingState::getMyMissingConferences(): " << this << " my ptt identity conference: " << myconf.displayName.c_str() << " needs to be added to server");
         myMissingConferences.push_back(myconf);
      }
   }
}

void PttConferenceConnectorSession::createMissingChannels(const cpc::vector<cpc::string>& channels, bool& conferencesCreated)
{
   std::set<cpc::string> missingChannels;
   CloudConferenceHandle rootConf = (CloudConferenceHandle)(-1);
   if (mConfList.size() > 0)
   {
      for (const cpc::string& neededChannel : channels)
      {
         bool found = false;
         for (const CloudConferenceInfo& cci : mConfList)
         {
            if (neededChannel == cci.displayName)
            {
               found = true;
            }
            else if (cci.displayName == "root")
            {
               rootConf = cci.conference;
            }
         }
         if (!found)
         {
            missingChannels.insert(neededChannel);
         }
      }

      std::stringstream mc;
      for (const cpc::string& channel : missingChannels) mc << channel << " ";

      InfoLog(<< "PttConferenceConnectorSession::createMissingChannels(): create " << missingChannels.size() << " missing channels: " << mc.str().c_str());
      for (const cpc::string& missingChannel : missingChannels)
      {
         DebugLog(<< "PttConferenceConnectorSession::createMissingChannels(): create missing channel: " << missingChannel);
         CloudConferenceSettings cloudConfSettings;
         cloudConfSettings.conferenceDescription = missingChannel;
         cloudConfSettings.conferenceId = missingChannel;
         cloudConfSettings.conferenceType = CloudConferenceType_AudioVideo_SFU;
         cloudConfSettings.parentConference = rootConf;
         cloudConfSettings.persistent = true;
         cloudConfSettings.conferenceTags.push_back(0x4000); // special value to indicate audio only conference (prevents video-related threads from starting on the server)
         mConfConnMgr->createNewConference(mConfConnHdl, cloudConfSettings);
         conferencesCreated = true;
      }
   }
}

void PttConferenceConnectorSession::createNewConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, PttIdentity& identity)
{
   CloudConferenceSettings cloudConfSettings;
   if (identity.identityType == PttIdentityType_SIP)
   {
      cloudConfSettings.conferenceTags.push_back(PTT_CONF_TAG_SIP);
   }
   else if (identity.identityType == PttIdentityType_XMPP)
   {
      cloudConfSettings.conferenceTags.push_back(PTT_CONF_TAG_XMPP);
   }
   
   cloudConfSettings.conferenceId = identity.userName;
   cloudConfSettings.conferenceDescription = identity.userName;
   cloudConfSettings.conferenceType = CloudConferenceType_AudioVideo_SFU;
   cloudConfSettings.persistent = true;
   cloudConfSettings.parentConference = conference;
   cloudConfSettings.conferenceTags.push_back(0x4000); // special value to indicate audio only conference (prevents video-related threads from starting on the server)
   InfoLog(<< "PttConferenceConnectorSession::createNewConference(): " << this << " local-identity: " << identity.userName << " conference-identity: " << identity.displayName << " creating new conference");
   mConfConnMgr->createNewConference(mConfConnHdl, cloudConfSettings);
}

bool PttConferenceConnectorSession::getRootConference(CPCAPI2::ConferenceConnector::CloudConferenceInfo& rootConfInfo)
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   bool found = false;
   for (auto it = mConfList.begin(); it != mConfList.end(); ++it)
   {
      DebugLog(<< "PttConferenceConnectorSession::getRootConference(): " << this << " checking conference-identity: " << it->displayName);
      if (it->displayName == cpc::string("root"))
      {
         DebugLog(<< "PttConferenceConnectorSession::getRootConference(): " << this << " conference-identity: " << it->displayName << " matches root");
         if (settings.localIdentities.empty())
         {
            InfoLog(<< "PttConferenceConnectorSession::getRootConference(): " << this << " no ptt local identities provisioned");
         }
         else
         {
            DebugLog(<< "PttConferenceConnectorSession::getRootConference(): " << this << " conference-identity: " << it->displayName << " local identities populated");
            found = true;
            rootConfInfo = (*it);
         }
         break;
      }
   }
   return found;
}

void PttConferenceConnectorSession::handleConferenceListUpdated(const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args)
{
   printCurrentConferences();

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   std::string localIdentStr = getLocalIdentity();
      
   InfoLog(<< "PttConferenceConnectorSession::handleConferenceListUpdated(): " << this << " local-identity: " << localIdentStr << " conference-connector: " << mConfConnHdl << " local-conference count: " << mConfList.size() << " server-conference count: " << args.conferenceList.size());
   mConfList = args.conferenceList;
   
   cpc::vector<PttIdentity> myMissingConferences;
   getMyMissingConferences(myMissingConferences);
      
   bool inSync(true);

   if (myMissingConferences.size() > 0)
   {
      InfoLog(<< "PttConferenceConnectorSession::handleConferenceListUpdated(): " << this << " server is missing " << myMissingConferences.size() << " of my ptt identity conferences");
      ConferenceConnector::CloudConferenceInfo rootConfInfo;
      bool found = getRootConference(rootConfInfo);
      if (found)
      {
         for (auto il = myMissingConferences.begin(); il != myMissingConferences.end(); ++il)
         {
            StackLog(<< "PttConferenceConnectorSession::handleConferenceListUpdated(): " << this << " local-identity: " << il->userName << " missing conference-identity: " << rootConfInfo.displayName);
            createNewConference(rootConfInfo.conference, *il);
         }
         mConfConnMgr->queryConferenceList(mConfConnHdl);
         inSync = false;
      }
   }

   bool conferencesCreated = false;
   setChannelSubscriptions(mService, mManager->getSubscribedChannels(), conferencesCreated);
   mInSync = (inSync ? !conferencesCreated : false);
}

void PttConferenceConnectorSession::resetConferenceConnector()
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (NetworkChangeManager* network = NetworkChangeManager::getInterface(mPhone))
   {
      CPCAPI2::NetworkTransport transport = network->networkTransport();

      InfoLog(<< "PttConferenceConnectorSession::resetConferenceConnector(): " << this << " service: " << mService << " network: " << transport << " conference-connector: " << mConfConnHdl << " service-status: " << mManager->getServiceStatus());

      if (transport != TransportNone)
      {
         if (mManager->getServiceStatus() == PttServiceStatusChangedEvent::Status_Disconnected)
         {
            DebugLog(<< "PttConferenceConnectorSession::resetConferenceConnector(): " << this << " service: " << mService << " network: " << transport << " conference-connector: " << mConfConnHdl << " conference service has not yet connected");

            if (mConfConnMgr)
            {
               if (mConfConnHdl > 0)
               {
                  mConfConnMgr->destroyConferenceConnector(mConfConnHdl);
                  mConfConnHdl = 0;
               }

               mConfConnHdl = mConfConnMgr->createConferenceConnector();
               ConferenceConnectorSettings confConnSettings;
               confConnSettings.authServerUrl = settings.authServiceAddress;
               confConnSettings.authServerApiKey = settings.authServiceApiKey;
               confConnSettings.joinUrl = settings.confServiceAddress;
               confConnSettings.regionCode = "LOCAL";
               confConnSettings.username = settings.username;
               confConnSettings.password = settings.password;
               confConnSettings.ignoreCertVerification = settings.ignoreCertVerification;
               mConfConnMgr->setConnectionSettings(mConfConnHdl, confConnSettings);

               DebugLog(<< "PttConferenceConnectorSession::resetConferenceConnector(): " << this << " service: " << mService << " network: " << transport << " connecting to conference service");
         #ifdef CPCAPI2_AUTO_TEST
               mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttServiceRestarted), mService, PttServiceRestartedEvent(PttRestartReasonType_NetworkFailure));
         #endif
               mConfConnMgr->connectToConferenceService(mConfConnHdl);
            }
         }
      }
   }
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE

