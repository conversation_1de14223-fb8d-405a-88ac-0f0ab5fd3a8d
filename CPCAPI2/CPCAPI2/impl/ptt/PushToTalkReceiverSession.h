#pragma once

#ifndef CPCAPI2_PUSHTOTALK_RECEIVER_SESSION_IMPL_H
#define CPCAPI2_PUSHTOTALK_RECEIVER_SESSION_IMPL_H

#include "cpcapi2defs.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSession.h"

namespace CPCAPI2
{

namespace PushToTalk
{

class PttReceiverIdleState;
class PttReceiverInitiatedState;
// class PttReceiverInitialActiveState;
class PttReceiverActiveState;
class PttReceiverTalkingState;
class PttReceiverEndingState;
class PttReceiverSessionState;

class PttReceiverSessionFactory : public PttSessionFactory
{

public:

   PttReceiverSessionFactory(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverSessionFactory();

   virtual PttSession* create(PushToTalkSessionHandle ptt) OVERRIDE;

private:

   PttReceiverSessionFactory();

};

class PttReceiverSessionStateFactory : public PttSessionStateFactory
{

public:

   PttReceiverSessionStateFactory(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverSessionStateFactory();

   virtual void create() OVERRIDE;
   virtual PttSessionState* getState(PttSessionStateType type) OVERRIDE;

private:

   PttReceiverSessionStateFactory();

   PttReceiverSessionState* create(PttSessionStateType type);

   typedef std::map<PttSessionStateType, PttReceiverSessionState*> PttReceiverSessionStates;
   PttReceiverSessionStates mStates;

};

class PttReceiverSession : public PttSession
{

public:

   PttReceiverSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt, PttSessionType sessionType);
   virtual~ PttReceiverSession();

   // virtual PttSessionState* getState() OVERRIDE;
   virtual PttSessionStateFactory* getFactory() OVERRIDE { return mFactory; }
   virtual cpc::string& getChannelId() OVERRIDE { return mInitiateEvent.channelId; }
   virtual uint32_t getTransactionId() OVERRIDE { return mInitiateEvent.transactionId; }
   virtual cpc::string& getSessionId() OVERRIDE { return mInitiateEvent.sessionId; }
   virtual PushToTalkSessionHandle getIncomingPtt() { return mInitiateEvent.pttHandle; }
   virtual void cancelTimers() OVERRIDE;

   // Associated to DeadlineTimerHandler
   virtual void onTimer(PttSession* session, unsigned short timerId, void* appState) OVERRIDE;

   PushToTalkSessionHandle mIncomingPtt;
   PttInitiateEvent mInitiateEvent;

   PttReceiverSessionStateFactory* mFactory;

   virtual void handleAccept() = 0;
   virtual void handleEnd(PushToTalk::PttReceiverEndReasonType reason) = 0;
   virtual void handleSessionMute() = 0;
   virtual void handleSessionUnmute() = 0;

   void resetSetupTimer();
   void resetConnectedTimer();
   void resetConnectionRetryTimer();
   void resetWaitForAnswerTimer();
   void resetRejectedTimer();
   void cancelSetupTimer() { mSetupTimer.cancel(); }
   void cancelConnectedTimer() { mConnectedTimer.cancel(); }
   void cancelConnectionRetryTimer() { mConnectionRetryTimer.cancel(); }
   void cancelWaitForAnswerTimer() { mWaitForAnswerTimer.cancel(); }
   void cancelRejectedTimer() { mRejectedTimer.cancel(); }

   resip::DeadlineTimer<resip::MultiReactor> mSetupTimer; // how long to wait before call is destroyed while waiting for app to accept or reject the incoming call
   resip::DeadlineTimer<resip::MultiReactor> mConnectionRetryTimer; // how long to wait before a reattempt is made to establish a web socket client connection
   resip::DeadlineTimer<resip::MultiReactor> mConnectedTimer; // how long to wait before call is destroyed while waiting for the web socket client connection
   resip::DeadlineTimer<resip::MultiReactor> mWaitForAnswerTimer; // how long to wait before call is destroyed while waiting for the sdk answer
   resip::DeadlineTimer<resip::MultiReactor> mRejectedTimer; // how long to wait before after call has been destroyed, if rejected or terminated

protected:

   PttReceiverSession();

};

class PttReceiverLanSession : public PttReceiverSession
{

public:

   PttReceiverLanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt);
   virtual~ PttReceiverLanSession();

   virtual std::string getSessionType() OVERRIDE { return "lan receiver"; }
   virtual uint32_t getPttSessionConnectedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionEndedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionTotalCallCount() OVERRIDE;
   virtual bool doesCallExist(uint32_t call) OVERRIDE;
   virtual bool doesCallExist(resip::Tuple& endpoint) OVERRIDE;
   virtual PttSessionCall* getPttSessionCall(resip::Tuple& endpoint) OVERRIDE;
   virtual bool areAllPttCallsConnected() OVERRIDE;
   virtual bool areAllPttCallsDisconnected() OVERRIDE;

   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   PeerReceiverCall* mCall;
   bool mMediaInactive;

   virtual void handleAccept() OVERRIDE;
   virtual void handleEnd(PushToTalk::PttReceiverEndReasonType reason) OVERRIDE;
   virtual void handleSessionMute() OVERRIDE;
   virtual void handleSessionUnmute() OVERRIDE;
   virtual void enableIncomingMediaInactivityMonitor() OVERRIDE;
   virtual void disableIncomingMediaInactivityMonitor() OVERRIDE;
   void endCall();
   void sendPttReceiverEnded(PushToTalk::PttReceiverEndReasonType reason);

protected:

   PttReceiverLanSession();

};

class PttReceiverWanSession : public PttReceiverSession
{

public:

   PttReceiverWanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt);
   virtual~ PttReceiverWanSession();

   virtual std::string getSessionType() OVERRIDE { return "wan receiver"; }
   virtual uint32_t getPttSessionConnectedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionEndedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionTotalCallCount() OVERRIDE;
   virtual bool doesCallExist(uint32_t call) OVERRIDE;
   virtual bool doesCallExist(resip::Tuple& endpoint) OVERRIDE;
   virtual PttSessionCall* getPttSessionCall(resip::Tuple& endpoint) OVERRIDE;
   virtual bool areAllPttCallsConnected() OVERRIDE;
   virtual bool areAllPttCallsDisconnected() OVERRIDE;

   virtual void handleAccept() OVERRIDE;
   virtual void handleEnd(PushToTalk::PttReceiverEndReasonType reason) OVERRIDE;
   virtual void handleSessionMute() OVERRIDE;
   virtual void handleSessionUnmute() OVERRIDE;

   CPCAPI2::ConferenceConnector::CloudConferenceHandle mCloudConference;
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle mCloudSession;
   CPCAPI2::PeerConnection::PeerConnectionHandle mCloudPeerConnection;
   CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo mCloudConferenceOwner;
   cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo> mCloudParticipantList;
   bool mSenderDestroyedDueToOverride;
   bool mRejected;

protected:

   PttReceiverWanSession();

};

class PttReceiverSessionState : public PushToTalk::PttSessionState
{

public:

   PttReceiverSessionState(PushToTalkManagerImpl* manager, PttSessionStateType state);
   virtual~ PttReceiverSessionState();

   virtual int end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason) OVERRIDE;
   virtual int onPttEndEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onMediaInactivity(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args) OVERRIDE;
   virtual int onCloudSessionEnded(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference) OVERRIDE;
   virtual int onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args) OVERRIDE;

   virtual void onSetupTimeout(PttSession* session);
   virtual void onConnectedTimeout(PttSession* session);
   virtual void onConnectionRetryTimeout(PttSession* session);
   virtual void onWaitForAnswerTimeout(PttSession* session);
   virtual void onRejectedTimeout(PttSession* session);

   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;

protected:

   PttReceiverSessionState();
   int endInternal(PttSession* session, PushToTalkSessionHandle ptt);
   int endWithoutDelay(PttSession* session, PushToTalkSessionHandle ptt, PushToTalk::PttReceiverEndReasonType reason = PushToTalk::PttReceiverEndReasonType_None);

};

class PttReceiverIdleState : public PttReceiverSessionState
{

public:

   PttReceiverIdleState(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverIdleState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int onPttInitiateEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onCloudNewParticipantInMyConference(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId) OVERRIDE;

private:

   PttReceiverIdleState();

};

class PttReceiverInitiatedState : public PttReceiverSessionState
{

public:

   PttReceiverInitiatedState(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverInitiatedState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int accept(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int reject(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;

   virtual void onSetupTimeout(PttSession* session) OVERRIDE;

private:

   PttReceiverInitiatedState();

};

/*
class PttReceiverInitialActiveState : public PttReceiverSessionState
{

public:

   PttReceiverInitialActiveState(PushToTalkManagerImpl* manager) {}
   virtual~ PttReceiverInitialActiveState() {}
   virtual void onEntry(PttSession* session) OVERRIDE {}
   virtual void onExit(PttSession* session) OVERRIDE {}

private:

   PttReceiverInitialActiveState();

};
*/

class PttReceiverActiveState : public PttReceiverSessionState
{

public:

   PttReceiverActiveState(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverActiveState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;
   virtual int mute(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int unmute(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;

   virtual int onCreateOfferResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;

   virtual int onConferenceSessionMediaStatusChanged(
      PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector,
      const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args) OVERRIDE;

   virtual void onWaitForAnswerTimeout(PttSession* session) OVERRIDE;

private:

   PttReceiverActiveState();

};

class PttReceiverTalkingState : public PttReceiverSessionState
{

public:

   PttReceiverTalkingState(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverTalkingState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;
   virtual int mute(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int unmute(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;

private:

   PttReceiverTalkingState();

};

class PttReceiverEndingState : public PttReceiverSessionState
{

public:

   PttReceiverEndingState(PushToTalkManagerImpl* manager);
   virtual~ PttReceiverEndingState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason) OVERRIDE;

   virtual void onRejectedTimeout(PttSession* session) OVERRIDE;
   // TODO: Need to find out a way to detect a new call on the same conference-id, in case it is re-established

   virtual void setSubState(int subState) OVERRIDE
   {
      mSubState = subState;
   }

   enum PttReceiverEndingSubState
   {
      PttReceiverEndingSubState_Rejected
   };

private:

   PttReceiverEndingState();
   int mSubState;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_RECEIVER_SESSION_IMPL_H
