#pragma once

#ifndef CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_SESSION_H
#define CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_SESSION_H

#include "cpcapi2defs.h"
#include "phone/PhoneInterface.h"
#include "PushToTalkServerWebSocket.h"
#include "PushToTalkServerWebSocketId.h"
#include "PushToTalkManagerInterface.h"
#include "../util/STAAssertion.h"

// websocket server
#include <websocketpp/config/asio.hpp>
#include <websocketpp/server.hpp>

// websocket client
#include <websocketpp/client.hpp>

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>


namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkServerWebSocket;

class PushToTalkServerWebSocketSession
{

public:

   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::connection_ptr conn_ptr;
   typedef server::message_ptr message_ptr;

   PushToTalkServerWebSocketSession(CPCAPI2::PhoneInterface* masterSdkPhone, CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, PushToTalkServerWebSocket* transport, const websocketpp::connection_hdl& conn);
   virtual ~PushToTalkServerWebSocketSession();

   void SendData(const char* payload, unsigned int payloadLen);

   const websocketpp::connection_hdl& websocket_conn_hdl() const { return mConn; }

   void close();
   void onClose(server* s, websocketpp::connection_hdl hdl);
   void onCloseImpl(server* s, websocketpp::connection_hdl hdl);
   void onMessage(server* s, websocketpp::connection_hdl hdl, message_ptr msg);
   void onMessageImpl(server* s, websocketpp::connection_hdl hdl, message_ptr msg);
   void setConnection(uint32_t connection) { mConnection = connection; }

   class IdImpl : public PushToTalkServerWebSocketSessionId
   {
   public:
      IdImpl(const websocketpp::connection_hdl& conn) : mConn(conn) {}
      virtual ~IdImpl() {}

      const websocketpp::connection_hdl& connection() const {
         return mConn;
      }

   private:
      websocketpp::connection_hdl mConn;
   };

   PushToTalkServerWebSocketSessionId* getSessionId() const {
      IdImpl* id = new IdImpl(mConn);
      return id;
   }

private:

   void handleWsServerMessage(server* s, websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc);

   websocketpp::connection_hdl mConn;
   CPCAPI2::PhoneInterface* mMasterPhone;
   PushToTalkServerWebSocket* mTransport;
   CPCAPI2::PushToTalk::PushToTalkManagerInterface* mPttMgr;
   STAAssertion mThreadCheck;
   uint32_t mConnection;
   bool mClosedLocally;
   bool mClosedRemotely;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_SESSION_H
