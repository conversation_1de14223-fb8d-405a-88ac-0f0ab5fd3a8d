#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkMulticastReceiver.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT


namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkMulticastReceiver::PushToTalkMulticastReceiver(
   boost::asio::io_context& io_context,
   const boost::asio::ip::address& listen_address,
   const boost::asio::ip::address& multicast_address,
   const int multicast_port,
   const MulticastReceiverCbType& cb)
   : socket_(io_context), callback_(cb), running_(true)
{
   StackLog(<< "PushToTalkMulticastReceiver(): listen-address: " << listen_address << " muticast-address: " << multicast_address << " multicast-port: " << multicast_port);
   // Create the socket so that multiple may be bound to the same address.
   boost::asio::ip::udp::endpoint listen_endpoint(
      listen_address, multicast_port);
   socket_.open(listen_endpoint.protocol());
   socket_.set_option(boost::asio::ip::udp::socket::reuse_address(true));
   socket_.bind(listen_endpoint);

   // Join the multicast group.
   socket_.set_option(boost::asio::ip::multicast::join_group(multicast_address));

   do_receive();
}

void PushToTalkMulticastReceiver::do_receive()
{
   StackLog(<< "PushToTalkMulticastReceiver::do_receive()");
   socket_.async_receive_from(
      boost::asio::buffer(data_), sender_endpoint_,
      [this](boost::system::error_code ec, std::size_t length)
   {
      if (ec)
      {
         WarningLog(<< "PushToTalkMulticastReceiver::do_receive(): ec: " << ec.value() << " desc: " << ec.message());
      }
      else
      {
         callback_(sender_endpoint_.address().to_string(), sender_endpoint_.port(), data_.data(), length);
         do_receive();
      }
   });
}

}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
