#pragma once

#ifndef CPCAPI2_PUSHTOTALK_ENDPOINTDISCOVERYSESSION_H
#define CPCAPI2_PUSHTOTALK_ENDPOINTDISCOVERYSESSION_H

#include "cpcapi2defs.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"
#include "ptt/PushToTalkHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkManagerInterface.h"
#include <rutil/DeadlineTimer.hxx>
#include <resip/stack/Tuple.hxx>

#include "../util/STAAssertion.h"

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>

#include <vector>
#include <string>
#include <array>


namespace CPCAPI2
{

namespace PushToTalk
{

class PttEndpointDiscoverySession;

enum PttEndpointDiscoveryStateType
{
   PttEndpointDiscoveryStateType_Idle,
   PttEndpointDiscoveryStateType_Starting,
   PttEndpointDiscoveryStateType_Polling,
   PttEndpointDiscoveryStateType_Sync
};

class PttEndpointDiscoverySessionState
{

public:

   PttEndpointDiscoverySessionState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session, PttEndpointDiscoveryStateType state);
   virtual~ PttEndpointDiscoverySessionState();
   virtual void onEntry() = 0;
   virtual void onExit() = 0;

   PttEndpointDiscoveryStateType getType();
   std::string getName();
   static std::string getName(PttEndpointDiscoveryStateType type);
   virtual bool isUpdating();

   // Associated to PushToTalkManagerImpl
   virtual void startService(PushToTalkServiceHandle service);
   virtual void shutdownService(PushToTalkServiceHandle service);
   virtual void onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple);
   virtual void onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple);
   virtual void onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple);
   virtual void onSenderStarted();
   virtual void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets);
   virtual void onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const std::string& payload);
   virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

protected:

   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mInterface;
   CPCAPI2::PhoneInterface* mPhone;
   PttEndpointDiscoverySession* mSession;
   PttEndpointDiscoveryStateType mState;
   PushToTalkServiceHandle mService;

};

class PttEndpointDiscoverySessionIdleState : public PttEndpointDiscoverySessionState
{

public:

   PttEndpointDiscoverySessionIdleState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session);
   virtual~ PttEndpointDiscoverySessionIdleState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   virtual void startService(PushToTalkServiceHandle service) OVERRIDE;

private:

   PttEndpointDiscoverySessionIdleState();

};

class PttEndpointDiscoverySessionStartingState : public PttEndpointDiscoverySessionState,
                                                 public resip::DeadlineTimerHandler
{

public:

   PttEndpointDiscoverySessionStartingState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session);
   virtual~ PttEndpointDiscoverySessionStartingState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   virtual void onSenderStarted() OVERRIDE;

private:

   PttEndpointDiscoverySessionStartingState();

   void resetUnicastTransportStartupTimer();

   resip::DeadlineTimer<resip::MultiReactor> mTransportStartedTimer; // how long to wait for transport start

};

class PttEndpointDiscoverySessionPollingState : public PttEndpointDiscoverySessionState,
                                                public resip::DeadlineTimerHandler
{

public:

   PttEndpointDiscoverySessionPollingState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session);
   virtual~ PttEndpointDiscoverySessionPollingState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;
   virtual bool isUpdating() OVERRIDE { return true; }

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   virtual void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets) OVERRIDE;
   virtual void onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual void onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual void onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;

private:

   PttEndpointDiscoverySessionPollingState();

   void resetUnicastEndpointListTimer();
   void resetUnicastDiscoverySendTimer();
   // void resetUnicastDiscoveryRetryTimer();

   resip::DeadlineTimer<resip::MultiReactor> mUnicastEndpointListTimer; // how long to wait before declaring the unicast endpoint list stable and sending PttReportEndpointsEvent
   resip::DeadlineTimer<resip::MultiReactor> mUnicastTransportSendTimer; // how long to wait before declaring a transmission failure
   // resip::DeadlineTimer<resip::MultiReactor> mUnicastInitialRetryTimer; // how long to wait before retrying the initial discovery
   // bool mInitialRetryCompleted;

};

class PttEndpointDiscoverySessionSyncState : public PttEndpointDiscoverySessionState,
                                             public resip::DeadlineTimerHandler
{

public:

   PttEndpointDiscoverySessionSyncState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session);
   virtual~ PttEndpointDiscoverySessionSyncState();

   virtual void onEntry() OVERRIDE;
   virtual void onExit() OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   virtual void onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual void onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual void onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;

private:

   PttEndpointDiscoverySessionSyncState();

   void resetUnicastDiscoveryRetryTimer();

   resip::DeadlineTimer<resip::MultiReactor> mUnicastDiscoveryRetryTimer; // how long to wait before retrying the discovery, only applicable if discovery list is empty

};

class PttEndpointDiscoverySessionStateFactory
{

public:

   PttEndpointDiscoverySessionStateFactory(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session);
   virtual~ PttEndpointDiscoverySessionStateFactory();

   virtual void create();
   virtual PttEndpointDiscoverySessionState* getState(PttEndpointDiscoveryStateType type);

private:

   PttEndpointDiscoverySessionStateFactory();

   PttEndpointDiscoverySessionState* create(PttEndpointDiscoveryStateType type);

   PushToTalkManagerImpl* mManager;
   PttEndpointDiscoverySession* mSession;

   typedef std::map<PttEndpointDiscoveryStateType, PttEndpointDiscoverySessionState*> PttEndpointDiscoverySessionStates;
   PttEndpointDiscoverySessionStates mStates;

};

class PttEndpointDiscoverySession
{

public:

   PttEndpointDiscoverySession(PushToTalkManagerImpl* manager);
   virtual~ PttEndpointDiscoverySession();

   PttEndpointDiscoverySessionStateFactory* getFactory();

   resip::Tuple getLocalAddress();
   std::string getLocalEndpoint(); // in ip:port format
   PttIdentity getLocalIdentity();
   bool isUpdating();
   bool hasStarted() { return mInitialDiscoveryCompleted; }
   std::map<std::string, PttIdentity>& getEndpointList() { return mEndpointList; }
   unsigned int getDiscoveryRetryTimeoutIntervalMsecs();
   void logEndpointList();

   void changeState(PttEndpointDiscoveryStateType newStateType);

   void sendQueryEndpointsRequest();
   void sendQueryEndpointsResponse(const resip::Tuple& incomingTuple, unsigned int transactionId);
   void sendReportEndpointsEvent();
   void addToDiscoveryList(PttIdentity& identity);

   // Associated to PushToTalkManagerImpl
   void queryEndpointList(PushToTalkServiceHandle service);
   void startService(PushToTalkServiceHandle service);
   void shutdownService(PushToTalkServiceHandle service);
   void onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple);
   void onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple);
   void onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple);
   void onSenderStarted();
   void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets);
   void onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const std::string& payload);
   void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

   void handleOnQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple);
   void handleOnQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple);
   void handleOnReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple);

   PttEndpointDiscoveryStateType mState;
   PushToTalkServiceHandle mService;
   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mInterface;
   CPCAPI2::PhoneInterface* mPhone;

   resip::Tuple mLocalEndpoint;

   UInt64 mLastUpdateTime = 0;
   std::map<std::string, PttIdentity> mEndpointList;
   std::set<boost::asio::ip::udp::endpoint> mDiscoveryTargets;
   std::chrono::time_point<std::chrono::system_clock> mDiscoveryStartTime;
   unsigned int mDiscoveryRequestsSent;
   unsigned int mDiscoveryResponsesReceived;
   bool mInitialDiscoveryCompleted;
   unsigned int mDiscoveryRetryTimeoutIntervalMsecs;
   bool mDiscoveryRetry;

   struct QueryEndpointsResponseId
   {
      resip::Data endpointIp;
      unsigned int tid;

      QueryEndpointsResponseId(const resip::Tuple& tuple, unsigned int tid_)
      {
         endpointIp = tuple.presentationFormat();
         tid = tid_;
      }

      bool operator==(const QueryEndpointsResponseId& rhs)
      {
         return (endpointIp == rhs.endpointIp && tid == rhs.tid);
      }
   };

   std::deque<unsigned int> mLastQueryEndpointsMessage;
   std::deque<QueryEndpointsResponseId> mLastQueryEndpointsResponse;
   std::deque<unsigned int> mLastReportEndpointsMessage;

protected:

   PttEndpointDiscoverySession();
   void sendEndpointList();

   PttEndpointDiscoverySessionStateFactory mFactory;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_ENDPOINTDISCOVERYSESSION_H
