#pragma once

#ifndef CPCAPI2_PUSHTOTALK_SESSION_H
#define CPCAPI2_PUSHTOTALK_SESSION_H

#include "cpcapi2defs.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"
#include "ptt/PushToTalkHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkManagerInterface.h"
#include "../media/MediaManagerInterface.h"
#include <rutil/DeadlineTimer.hxx>
#include <resip/stack/Tuple.hxx>

#include "../util/STAAssertion.h"

#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionSyncHandler.h"

#include "confconnector/ConferenceConnector.h"
#include "confconnector/ConferenceConnectorHandler.h"

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>

#include <vector>
#include <string>
#include <array>

namespace webrtc_recon
{
struct MediaStackSettings;
}

namespace CPCAPI2
{

namespace PushToTalk
{

class PttSession;

enum PttSessionCallStateType
{
   PttSessionCallStateType_Idle,
   PttSessionCallStateType_Setup,
   PttSessionCallStateType_Connected,
   PttSessionCallStateType_Ended
};

class PttSessionCall
{

public:

   PttSessionCall(uint32_t handle_) : handle(handle_), state(PttSessionCallStateType_Idle) {}
   virtual~ PttSessionCall() {}
   uint32_t handle;
   PttSessionCallStateType state;
   PushToTalkSessionHandle ptt;

   static std::string getState(PttSessionCallStateType state);
   static PttSessionCallStateType getState(CPCAPI2::PeerConnection::SignalingState state);

private:

   PttSessionCall();

};

class PeerSessionCall : public PttSessionCall
{

public:

   PeerSessionCall(CPCAPI2::PeerConnection::PeerConnectionHandle handle_) :
      PttSessionCall(handle_), connectionState((CPCAPI2::PeerConnection::SignalingState)-1), connectionRetriesLeft(0) {}
   virtual~ PeerSessionCall() {}
   CPCAPI2::PeerConnection::SignalingState connectionState;
   uint32_t connectionRetriesLeft;

private:

   PeerSessionCall();

};

class PeerSenderCall : public PeerSessionCall
{

public:

   PeerSenderCall(CPCAPI2::PeerConnection::PeerConnectionHandle handle_, const resip::Tuple& endpoint_) :
      PeerSessionCall(handle_), endpoint(endpoint_), sdp(""), primary(false), replaced(false)
   {
   }

   virtual~ PeerSenderCall() {}
   resip::Tuple endpoint;
   std::string sdp;
   bool primary;
   bool replaced;

private:

   PeerSenderCall();

};

class PeerReceiverCall : public PeerSessionCall
{

public:

   PeerReceiverCall(CPCAPI2::PeerConnection::PeerConnectionHandle handle_, const resip::Tuple& endpoint_) :
      PeerSessionCall(handle_), endpoint(endpoint_) {}
   virtual~ PeerReceiverCall() {}
   resip::Tuple endpoint;

private:

   PeerReceiverCall();

};

class PttSessionState
{

public:

   PttSessionState(PushToTalkManagerImpl* manager, PttSessionStateType state);
   virtual~ PttSessionState();
   virtual void onEntry(PttSession* session) = 0;
   virtual void onExit(PttSession* session) = 0;
   virtual void setSubState(int subState) {}

   PttSessionStateType getType();
   std::string getName();
   static std::string getName(PttSessionStateType type);

   // Associated to PushToTalkManager
   virtual int addRecipient(PttSession* session, PushToTalkSessionHandle ptt, const PttIdentity& identity);
   virtual int setChannel(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& channel);
   virtual int start(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason);
   virtual int startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int endTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int accept(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int reject(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int mute(PttSession* session, PushToTalkSessionHandle ptt);
   virtual int unmute(PttSession* session, PushToTalkSessionHandle ptt);

   // Associated to PeerConnectionHandler
   virtual int onSignalingStateChange(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args);
   virtual int onCreateOfferResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args);
   virtual int onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args);
   virtual int onSetLocalSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args);
   virtual int onSetRemoteSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args);
   virtual int onMediaInactivity(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args);
   virtual int onError(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args);

   // Associated to messages received from PushToTalkUnicastReceiver
   virtual int onPttEndEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onPttInitiateEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onPttReceiverEndedEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple);

   virtual int onCloudSessionConnected(PttSession* session, PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt);
   virtual int onCloudNewParticipantInMyConference(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId);
   virtual int onCloudSessionEnded(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference);
   virtual int onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args);
   virtual int onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args);

   // Associated to NetworkChangeHandler
   virtual void onNetworkChange(PttSession* session, const CPCAPI2::NetworkChangeEvent& args);

   virtual void onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args);
   virtual void onPttSenderInitiateRecordingEvent(PttSession* session, const PttSenderInitiateRecordingEvent& args);

protected:

   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mPttInterface;
   CPCAPI2::PhoneInterface* mPhone;
   PttSessionStateType mState;
   PushToTalkServiceHandle mService;

};

class PttSessionStateFactory
{

public:

   PttSessionStateFactory(PushToTalkManagerImpl* manager) : mManager(manager) {}
   virtual~ PttSessionStateFactory() {};

   virtual void create() = 0;
   virtual PttSessionState* getState(PttSessionStateType type) = 0;

protected:

   PushToTalkManagerImpl* mManager;

private:

   PttSessionStateFactory();

};

class PttSession : public resip::DeadlineTimerHandler
{

public:

   enum PttSessionType
   {
      PttSessionType_Lan,
      PttSessionType_Wan
   };

   PttSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt, PttSessionType sessionType);
   virtual~ PttSession();

   virtual PttSessionStateFactory* getFactory() = 0;
   virtual cpc::string& getChannelId() = 0;
   virtual cpc::vector<CPCAPI2::PushToTalk::PttIdentity> getRecipients() { cpc::vector<CPCAPI2::PushToTalk::PttIdentity> recipients; return recipients; }
   virtual uint32_t getTransactionId() = 0;
   virtual cpc::string& getSessionId() = 0;
   virtual std::string getSessionType() = 0;
   virtual void cancelTimers() = 0;
   virtual uint32_t getPttSessionConnectedCallCount() = 0;
   virtual uint32_t getPttSessionEndedCallCount() = 0;
   virtual uint32_t getPttSessionTotalCallCount() = 0;
   virtual bool areAllPttCallsConnected() = 0;
   virtual bool areAllPttCallsDisconnected() = 0;
   virtual bool doesCallExist(uint32_t call) = 0;
   virtual bool doesCallExist(resip::Tuple& endpoint) = 0;
   virtual PttSessionCall* getPttSessionCall(resip::Tuple& endpoint) = 0;
   virtual void onTimer(PttSession* session, unsigned short timerId, void* appState) = 0;

   PttSessionType getType();
   std::string getName();
   static std::string getName(PttSessionType type);
   PushToTalkSessionHandle getPtt() { return mPtt; }
   std::string getLocalEndpoint(); // in ip:port format
   std::string getRemoteEndpoint(); // in ip:port format

   PttSessionType mType;
   PttSessionStateType mState;
   PushToTalkServiceHandle mService;
   PushToTalkSessionHandle mPtt;
   PushToTalkManagerImpl* mManager;
   PushToTalkManagerInterface* mPttInterface;
   CPCAPI2::PhoneInterface* mPhone;

   resip::Tuple mLocalEndpoint;
   resip::Tuple mRemoteEndpoint;

   // for debug purposes / performance timing
   uint32_t mMediaInfoSent;
   uint32_t mMediaInfoReceived;
   std::chrono::time_point<std::chrono::system_clock> mStartTime;

   // Associated to PushToTalkManager
   virtual int addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity);
   virtual int setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel);
   virtual int start(PushToTalkSessionHandle ptt);
   virtual int end(PushToTalkSessionHandle ptt, const cpc::string& reason);
   virtual int startTalkSpurt(PushToTalkSessionHandle ptt);
   virtual int endTalkSpurt(PushToTalkSessionHandle ptt);
   virtual int accept(PushToTalkSessionHandle ptt);
   virtual int reject(PushToTalkSessionHandle ptt);
   virtual int mute(PushToTalkSessionHandle ptt);
   virtual int unmute(PushToTalkSessionHandle ptt);

   // Associated to PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args);
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args);
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args);
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args);
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args);
   virtual int onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args);
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args);

   // Associated to NetworkChangeHandler
   virtual void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;
   virtual void onWifiStatusTimeout();

   // Associated to messages received from PushToTalkUnicastReceiver
   virtual int onPttEndEvent(PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onPttInitiateEvent(PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onClientOfferEvent(PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple);
   virtual int onPttReceiverEndedEvent(PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple);

   virtual int onCloudSessionConnected(PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt);
   virtual int onCloudNewParticipantInMyConference(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId);
   virtual int onCloudSessionEnded(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference);
   virtual int onConferenceParticipantListUpdated(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args);
   virtual int onConferenceSessionMediaStatusChanged(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args);

   virtual void onDiscoveryListUpdate(const PttDiscoveryListUpdate& updateEvt);
   virtual void onPttSenderInitiateRecordingEvent(PushToTalkSessionHandle ptt, const PttSenderInitiateRecordingEvent& args);

   void changeState(PttSessionStateType newStateType, int subState = -1);
   void sendStatusUpdate(PttSessionStateType newStateType);

   virtual void enableIncomingMediaInactivityMonitor();
   virtual void disableIncomingMediaInactivityMonitor();
   virtual void enableWifiStatusMonitor();
   virtual void disableWifiStatusMonitor();
   void logWifiStatus();

private:

   PttSession();

   void resetWifiStatusTimer();
   void cancelWifiStatusTimer();

   resip::DeadlineTimer<resip::MultiReactor> mWifiStatusTimer;

};

class PttSessionFactory
{

public:

   PttSessionFactory(PushToTalkManagerImpl* manager) : mManager(manager) {}
   virtual~ PttSessionFactory() {};

   virtual PttSession* create(PushToTalkSessionHandle ptt) = 0;

protected:

   PushToTalkManagerImpl* mManager;

private:

   PttSessionFactory();

};

}

}

#endif // CPCAPI2_PUSHTOTALK_SESSION_H
