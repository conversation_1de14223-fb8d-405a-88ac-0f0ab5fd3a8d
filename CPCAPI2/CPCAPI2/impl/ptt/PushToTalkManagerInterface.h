#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MANAGER_INTERFACE_H
#define CPCAPI2_PUSHTOTALK_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "../phone/PhoneModule.h"
#include "ptt/PushToTalkManager.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkManagerInternal.h"
#include "PushToTalkSyncHandler.h"
#include "phone/Cpcapi2EventSource.h"
#include "media/audio/AudioHandler.h"
#include "../phone/NetworkChangeManagerImpl.h"

#include "confconnector/ConferenceConnectorHandler.h"
#include "confconnector/ConferenceConnectorInterface.h"

#include <map>
#include <string>
#include <vector>
#include <future>

namespace CPCAPI2
{

class PhoneInterface;
class LocalLogger;

namespace SipAccount
{
class SipAccountInterface;
}

namespace Media
{
class MediaManagerInterface;
}

namespace PushToTalk
{

class PushToTalkManagerImpl;


class PushToTalkManagerInterface : public CPCAPI2::EventSource<PushToTalkServiceHandle, PushToTalkHandler, PushToTalkSyncHandler>,
                                   public PushToTalkManager,
                                   public PushToTalkManagerInternal,
                                   public PhoneModule,
                                   public CPCAPI2::EventSyncHandler<NetworkChangeHandler>
{

public:

   PushToTalkManagerInterface(Phone* phone);
   virtual ~PushToTalkManagerInterface();

   void firePhoneError(const cpc::string& errorText);

   template<typename TFn, typename TEvt> void firePttEvent(const char* funcName, TFn func,
                                                           PushToTalk::PushToTalkServiceHandle serviceHandle, PushToTalkSessionHandle pttHandle,
                                                           const TEvt& args)
   {
      fireEvent(funcName, func, serviceHandle, pttHandle, args);
   }

   template<typename TFn, typename TEvt> void firePttInternalEvent(const char* funcName, TFn func, PushToTalk::PushToTalkServiceHandle serviceHandle, const TEvt& args)
   {
#if defined(CPCAPI2_AUTO_TEST)
      PushToTalkHandlerInternal* internalHandler = NULL;
      apphandlermap_iterator itHandler = mAppHandlers.find(serviceHandle);
      if (itHandler != mAppHandlers.end())
      {
         if (itHandler->second == (void*)0xDEADBEEF)
         {
            internalHandler = static_cast<PushToTalkHandlerInternal*>(itHandler->second);
         }
         else
         {
            internalHandler = dynamic_cast<PushToTalkHandlerInternal*>(itHandler->second);
         }
      }

      if (internalHandler != NULL)
      {
         resip::ReadCallbackBase* rcb = makeFpCommandNew(funcName, func, (PushToTalkHandlerInternal*)0xDEADBEEF, serviceHandle, args);
         postCallback(rcb);
      }
#endif
   }

   template<typename TFn, typename TEvt> void firePttInternalSessionEvent(const char* funcName, TFn func, PushToTalk::PushToTalkServiceHandle serviceHandle, PushToTalk::PushToTalkSessionHandle sessionHandle, const TEvt& args)
   {
#if defined(CPCAPI2_AUTO_TEST)
      PushToTalkHandlerInternal* internalHandler = NULL;
      apphandlermap_iterator itHandler = mAppHandlers.find(serviceHandle);
      if (itHandler != mAppHandlers.end())
      {
         if (itHandler->second == (void*)0xDEADBEEF)
         {
            internalHandler = static_cast<PushToTalkHandlerInternal*>(itHandler->second);
         }
         else
         {
            internalHandler = dynamic_cast<PushToTalkHandlerInternal*>(itHandler->second);
         }
      }

      if (internalHandler != NULL)
      {
         resip::ReadCallbackBase* rcb = makeFpCommandNew(funcName, func, (PushToTalkHandlerInternal*)0xDEADBEEF, sessionHandle, args);
         postCallback(rcb);
      }

      for (thandler_iterator itObs = mSdkObservers.begin(); itObs != mSdkObservers.end(); ++itObs)
      {
         PushToTalkHandlerInternal* internalHandler = dynamic_cast<PushToTalkHandlerInternal*>(*itObs);
         if (internalHandler)
         {
            resip::ReadCallbackBase* cb = resip::resip_bind(func, internalHandler, sessionHandle, args);
            if (dynamic_cast<PushToTalkSyncHandler*>(internalHandler))
            {
               (*cb)();
               delete cb;
            }
            else
            {
               postCallback(cb);
            }
         }
      }
#endif
   }

   // PhoneModule
   virtual void PreRelease() OVERRIDE;
   virtual void Release() OVERRIDE;

   // PushToTalkManager
   virtual int process(unsigned int timeout) OVERRIDE;

   virtual void interruptProcess() OVERRIDE
   {
      CPCAPI2::EventSource<PushToTalkServiceHandle, PushToTalkHandler, PushToTalkSyncHandler>::interruptProcess();
   }
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE
   {
      CPCAPI2::EventSource<PushToTalkServiceHandle, PushToTalkHandler, PushToTalkSyncHandler>::setCallbackHook(cbHook, context);
   }

   virtual void logEvent(PushToTalkServiceHandle handle, const char *funcName, const char *eventName) OVERRIDE;

   virtual PushToTalkServiceHandle createPttService() OVERRIDE;
   virtual int setHandler(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler) OVERRIDE;
   // virtual int process(unsigned int timeout) OVERRIDE;
   virtual int configureService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings) OVERRIDE;
   virtual int queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int configureEndpointListAutoUpdate(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate) OVERRIDE;
   virtual int setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels) OVERRIDE;
   virtual int startService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int shutdownService(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual PushToTalkSessionHandle createPttSession(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity) OVERRIDE;
   virtual int setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel) OVERRIDE;
   virtual int start(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int end(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int startTalkSpurt(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int endTalkSpurt(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int accept(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int reject(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int setAudioInputDevice(unsigned int deviceId) OVERRIDE;
   virtual int setAudioOutputDevice(unsigned int deviceId) OVERRIDE;
   virtual int setAudioDeviceCloseDelay(int audioDeviceCloseDelay) OVERRIDE;
   virtual int mutePttSession(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int unmutePttSession(PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int mutePttService(PushToTalkServiceHandle service) OVERRIDE;
   virtual int unmutePttService(PushToTalkServiceHandle service) OVERRIDE;

   // PushToTalkManagerInternal
   virtual int setPttInternalSettings(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings) OVERRIDE;
   virtual int enableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int disableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int enableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int disableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable) OVERRIDE;
   virtual int disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service) OVERRIDE;
   virtual int queryMediaStatistics(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int enableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int disableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int handleRawPttRequest(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest) OVERRIDE;
   virtual int addPttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler) OVERRIDE;
   virtual int removePttObserver(CPCAPI2::PushToTalk::PushToTalkHandler* handler) OVERRIDE;
   virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

   PhoneInterface* phoneInterface();
   PushToTalkServiceHandle getPttServiceHandle() { return mPttServiceHandle; }
   CPCAPI2::SipAccount::SipAccountHandle getSipAccountHandle(PushToTalkSessionHandle ptt) const;
   void removePttAccountMapping(PushToTalkSessionHandle ptt);
   PushToTalkSessionHandle createPttSessionInternal(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   void post(resip::ReadCallbackBase* cmd);
   void resetStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference);
   void addToStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);
   void removeFromStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);
   bool isSessionInStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& participantIdentity);
   bool isStaleSession(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);

private:

   int createPttServiceImpl();
   int setHandlerImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler);
   int configureServiceImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const CPCAPI2::PushToTalk::PushToTalkServiceSettings& settings);
   int queryEndpointListImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int configureEndpointListAutoUpdateImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate);
   int setChannelSubscriptionsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels);
   int startServiceImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int shutdownServiceImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int createPttSessionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int addRecipientImpl(PushToTalkSessionHandle ptt, const PttIdentity& identity);
   int setChannelImpl(PushToTalkSessionHandle ptt, const cpc::string& channel);
   int startImpl(PushToTalkSessionHandle ptt);
   int endImpl(PushToTalkSessionHandle ptt);
   int startTalkSpurtImpl(PushToTalkSessionHandle ptt);
   int endTalkSpurtImpl(PushToTalkSessionHandle ptt);
   int acceptImpl(PushToTalkSessionHandle ptt);
   int rejectImpl(PushToTalkSessionHandle ptt);
   int setAudioDeviceCloseDelayImpl(int audioDeviceCloseDelay);
   int mutePttSessionImpl(PushToTalkSessionHandle ptt);
   int unmutePttSessionImpl(PushToTalkSessionHandle ptt);
   int mutePttServiceImpl(PushToTalkServiceHandle service);
   int unmutePttServiceImpl(PushToTalkServiceHandle service);

   int setPttInternalSettingsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const PushToTalkSettingsInternal& settings);
   int enableUnicastTransmissionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int disableUnicastTransmissionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int enableUnicastReceiveImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int disableUnicastReceiveImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int dropIncomingJsonMessagesImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable);
   int disconnectWanConnectionImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int queryMediaStatisticsImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int enableMediaInactivityMonitorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int disableMediaInactivityMonitorImpl(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int handleRawPttRequestImpl(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest);
   int addPttObserverImpl(CPCAPI2::PushToTalk::PushToTalkHandler* handler);
   int removePttObserverImpl(CPCAPI2::PushToTalk::PushToTalkHandler* handler);

   void removeHandlerImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);

   int onNetworkChangeImpl(const NetworkChangeEvent& args);

   std::atomic<PushToTalkServiceHandle> mPttServiceHandle;
   std::shared_ptr<PushToTalkManagerImpl> mPttServiceImpl;
   PhoneInterface* mPhone;

   PushToTalkHandler* mHandler;
   LocalLogger* mLocalLogger;

   std::promise<CPCAPI2::PushToTalk::PushToTalkServiceHandle> mServiceHandleFuture;
   std::promise<CPCAPI2::PushToTalk::PushToTalkSessionHandle> mSessionHandleFuture;
   std::promise<bool> mSetHandlerFuture;
   std::atomic<bool> mShutdownComplete;

   std::map<CPCAPI2::ConferenceConnector::CloudConferenceHandle, std::set<std::string> > mStaleConfSessionList; // Temporary list to track recently rejected, cancelled or terminated incoming ptt calls
   UInt64 mLastStaleConfSessionUpdateTime = 0;
};

class PushToTalkServiceHandleFactory
{

public:

   static PushToTalkServiceHandle getNext();

private:

   static PushToTalkServiceHandle sNextPushToTalkServiceHandle;

};

class PushToTalkSessionHandleFactory
{

public:

   static PushToTalkSessionHandle getNext();

private:

   static PushToTalkSessionHandle sNextPushToTalkSessionHandle;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MANAGER_INTERFACE_H
