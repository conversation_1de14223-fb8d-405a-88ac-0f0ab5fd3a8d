#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkSession.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkManagerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkSenderSession.h"
#include "PushToTalkReceiverSession.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "media/AudioInterface.h"
#include "../media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include <MediaStackImpl.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"
#include <resip/stack/Tuple.hxx>

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

using namespace CPCAPI2::PeerConnection;
using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

std::string PttSessionCall::getState(PttSessionCallStateType state)
{
   std::string name = "PttSessionCallStateType_Idle";
   switch (state)
   {
      case PttSessionCallStateType_Setup: name = "PttSessionCallStateType_Setup"; break;
      case PttSessionCallStateType_Connected: name = "PttSessionCallStateType_Connected"; break;
      case PttSessionCallStateType_Ended: name = "PttSessionCallStateType_Ended"; break;
      default: break;
   }

   return name;
}

PushToTalk::PttSessionCallStateType PttSessionCall::getState(PeerConnection::SignalingState state)
{
   // TODO: verify this mapping

   PttSessionCallStateType pttState = PttSessionCallStateType_Idle;
   switch (state)
   {
      case SignalingState_Stable: pttState = PttSessionCallStateType_Connected; break;
      case SignalingState_HaveLocalOffer: pttState = PttSessionCallStateType_Setup; break;
      case SignalingState_HaveRemoteOffer: pttState = PttSessionCallStateType_Setup; break;
      case SignalingState_HaveLocalPranswer: pttState = PttSessionCallStateType_Setup; break;
      case SignalingState_HaveRemotePranswer: pttState = PttSessionCallStateType_Setup; break;
      case SignalingState_Closed: pttState = PttSessionCallStateType_Ended; break;
      default: break;
   };

   return pttState;
}

PttSessionState::PttSessionState(PushToTalkManagerImpl* manager, PttSessionStateType type) :
mManager(manager),
mPttInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mState(type),
mService(manager->getService())
{
}

PttSessionState::~PttSessionState()
{
}

PttSessionStateType PttSessionState::getType()
{
   return mState;
}

std::string PttSessionState::getName()
{
   return (PttSessionState::getName(mState));
}

std::string PttSessionState::getName(PttSessionStateType type)
{
   std::string name = "PttSessionState_Invalid";
   switch (type)
   {
      case PttSessionState_Idle: name = "PttSessionState_Idle"; break;
      case PttSessionState_Initiated: name = "PttSessionState_Initiated"; break;
      case PttSessionState_InitialActive: name = "PttSessionState_InitialActive"; break;
      case PttSessionState_Active: name = "PttSessionState_Active"; break;
      case PttSessionState_Talking: name = "PttSessionState_Talking"; break;
      case PttSessionState_Ending: name = "PttSessionState_Ending"; break;
      default: break;
   }

   return name;
}

// Associated to PushToTalkManager

int PttSessionState::addRecipient(PttSession* session, PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   StackLog(<< "PttSessionState::addRecipient(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::setChannel(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& channel)
{
   StackLog(<< "PttSessionState::setChannel(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::start(PttSession* session, PushToTalkSessionHandle ptt)
{
   StackLog(<< "PttSessionState::start(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason)
{
   StackLog(<< "PttSessionState::end(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt)
{
   StackLog(<< "PttSessionState::startTalkSpurt(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::endTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt)
{
   StackLog(<< "PttSessionState::endTalkSpurt(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::accept(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttSessionState::accept(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::reject(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttSessionState::reject(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::mute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttSessionState::mute(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::unmute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttSessionState::unmute(): ptt: " << ptt << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

// Associated to PeerConnectionHandler

int PttSessionState::onSignalingStateChange(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   StackLog(<< "PttSessionState::onSignalingStateChange(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onCreateOfferResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   StackLog(<< "PttSessionState::onCreateOfferResult(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   StackLog(<< "PttSessionState::onCreateAnswerResult(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onSetLocalSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   StackLog(<< "PttSessionState::onSetLocalSessionDescriptionResult(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onSetRemoteSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   StackLog(<< "PttSessionState::onSetRemoteSessionDescriptionResult(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onMediaInactivity(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   StackLog(<< "PttSessionState::onMediaInactivity(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onError(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   StackLog(<< "PttSessionState::onError(): ptt: " << session->getPtt() << " pc: " << pc << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onPttEndEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSessionState::onPttEndEvent(): ptt: " << session->getPtt() << " session: " << evt.sessionId << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onPttInitiateEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSessionState::onPttInitiateEvent(): ptt: " << session->getPtt() << " session: " << evt.sessionId << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSessionState::onClientOfferEvent(): ptt: " << session->getPtt() << " session: " << evt.sessionId << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onCloudSessionConnected(PttSession* session, PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt)
{
   StackLog(<< "PttSessionState::onCloudSessionConnected(): ptt: " << session->getPtt() << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onCloudNewParticipantInMyConference(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId)
{
   return kSuccess;
}

int PttSessionState::onCloudSessionEnded(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference)
{
   return kSuccess;
}

int PttSessionState::onPttReceiverEndedEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttSessionState::onPttReceiverEndedEvent(): ptt: " << session->getPtt() << " session: " << evt.sessionId << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, ConferenceConnector::ConferenceConnectorHandle connector, const ConferenceConnector::ConferenceParticipantListUpdatedEvent& args)
{
   StackLog(<< "PttSessionState::onConferenceParticipantListUpdated(): ptt: " << ptt << " conference-connector: " << connector << " cloud-conference: " << args.conference << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

int PttSessionState::onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
   StackLog(<< "PttSessionState::onConferenceSessionMediaStatusChanged(): ptt: " << ptt << " conference-connector: " << connector << " cloud-conference: " << args.conference << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
   return kSuccess;
}

// NetworkChangeHandler

void PttSessionState::onNetworkChange(PttSession* session, const CPCAPI2::NetworkChangeEvent& args)
{
   StackLog(<< "PttSessionState::onNetworkChange(): ptt: " << session->getPtt() << " handled in " << session->getSessionType() << " session in current state: " << getName());
   session->changeState(PttSessionState_Idle);
}

void PttSessionState::onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args)
{
   StackLog(<< "PttSessionState::onDiscoveryListUpdate(): ptt: " << session->getPtt() << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
}

void PttSessionState::onPttSenderInitiateRecordingEvent(PttSession* session, const PttSenderInitiateRecordingEvent& args)
{
   StackLog(<< "PttSessionState::onPttSenderInitiateRecordingEvent(): ptt: " << session->getPtt() << " ignoring request as not handled in " << session->getSessionType() << " session in current state: " << getName());
}

PttSession::PttSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt, PttSessionType sessionType) :
mType(sessionType),
mState(PttSessionState_Idle),
mService(manager->getService()),
mPtt(ptt),
mManager(manager),
mPttInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mMediaInfoSent(0),
mMediaInfoReceived(0),
mWifiStatusTimer(manager->getReactor())
{
   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mManager->getServiceSettings().unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif
   mLocalEndpoint = resip::Tuple(localIpAddress.c_str(), mManager->getServiceSettings().unicastPort, resip::UDP);
}

PttSession::~PttSession()
{
   disableWifiStatusMonitor();
   DebugLog(<< "PttSession::~PttSession(): destroyed " << PttSession::getName() << " session with ptt: " << mPtt);
}

PttSession::PttSessionType PttSession::getType()
{
   return mType;
}

std::string PttSession::getName()
{
   return (PttSession::getName(mType));
}

std::string PttSession::getName(PttSessionType type)
{
   std::string name = "PttSessionType_Invalid";
   switch (type)
   {
      case PttSessionType_Lan: name = "PttSessionType_Lan"; break;
      case PttSessionType_Wan: name = "PttSessionType_Wan"; break;
      default: break;
   }

   return name;
}

std::string PttSession::getLocalEndpoint()
{
   std::stringstream os;
   os << mLocalEndpoint.presentationFormat().c_str() << ":" << mLocalEndpoint.getPort();
   return os.str().c_str();
}

std::string PttSession::getRemoteEndpoint()
{
   std::stringstream os;
   os << mRemoteEndpoint.presentationFormat().c_str() << ":" << mRemoteEndpoint.getPort();
   return os.str().c_str();
}

int PttSession::addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_TX_ADD_RECIPIENT, << "PttSession::addRecipient(): " << this << " ptt: " << ptt << " state: " << state->getName() << " identity: " << identity.userName.c_str());
      state->addRecipient(this, ptt, identity);
   }
   return kSuccess;
}

int PttSession::setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_TX_SET_CHANNEL, << "PttSession::setChannel(): " << this << " ptt: " << ptt << " state: " << state->getName() << " channel: " << channel.c_str());
      state->setChannel(this, ptt, channel);
   }
   return kSuccess;
}

int PttSession::start(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_START, << "PttSession::start(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->start(this, ptt);
   }
   return kSuccess;
}

int PttSession::end(PushToTalkSessionHandle ptt, const cpc::string& reason)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_END, << "PttSession::end(): " << this << " ptt: " << ptt << " state: " << state->getName() << " reason: " << reason.c_str());
      state->end(this, ptt, reason);
   }
   return kSuccess;
}

int PttSession::startTalkSpurt(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_TX_START_SPURT, << "PttSession::startTalkSpurt(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->startTalkSpurt(this, ptt);
   }
   return kSuccess;
}

int PttSession::endTalkSpurt(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_TX_END_SPURT, << "PttSession::endTalkSpurt(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->endTalkSpurt(this, ptt);
   }
   return kSuccess;
}

int PttSession::accept(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_RX_ACCEPT, << "PttSession::accept(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->accept(this, ptt);
   }
   return kSuccess;
}

int PttSession::reject(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_RX_REJECT, << "PttSession::reject(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->reject(this, ptt);
   }
   return kSuccess;
}

int PttSession::mute(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_RX_MUTE, << "PttSession::mute(): " << this << " ptt: " << ptt << " state: " << state->getName());
      state->mute(this, ptt);
   }
   return kSuccess;
}

int PttSession::unmute(PushToTalkSessionHandle ptt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state)
   {
      StatLog(PTT_LOGID_SESSION_RX_UNMUTE, << "PttSession::unmute(): " << this << " ptt: " << ptt<< " state: " << state->getName());
      state->unmute(this, ptt);
   }
   return kSuccess;
}

// Associated to PeerConnectionHandler

int PttSession::onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onSignalingStateChange(this, pc, args);
   return kSuccess;
}

int PttSession::onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onCreateOfferResult(this, pc, args);
   return kSuccess;
}

int PttSession::onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onCreateAnswerResult(this, pc, args);
   return kSuccess;
}

int PttSession::onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onSetLocalSessionDescriptionResult(this, pc, args);
   return kSuccess;
}

int PttSession::onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onSetRemoteSessionDescriptionResult(this, pc, args);
   return kSuccess;
}

int PttSession::onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onMediaInactivity(this, pc, args);
   return kSuccess;
}

int PttSession::onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onError(this, pc, args);
   return kSuccess;
}

int PttSession::onPttEndEvent(PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onPttEndEvent(this, ptt, evt, incomingTuple);
   return kSuccess;
}

int PttSession::onPttInitiateEvent(PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onPttInitiateEvent(this, ptt, evt, incomingTuple);
   return kSuccess;
}

int PttSession::onClientOfferEvent(PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onClientOfferEvent(this, ptt, evt, incomingTuple);
   return kSuccess;
}

int PttSession::onCloudSessionConnected(PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onCloudSessionConnected(this, ptt, evt);
   return kSuccess;
}

int PttSession::onCloudNewParticipantInMyConference(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onCloudNewParticipantInMyConference(this, ptt, conference, partInfo, channelId);
   return kSuccess;
}

int PttSession::onCloudSessionEnded(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onCloudSessionEnded(this, ptt, conference);
   return kSuccess;
}

int PttSession::onConferenceParticipantListUpdated(PushToTalkSessionHandle ptt, ConferenceConnector::ConferenceConnectorHandle connector, const ConferenceConnector::ConferenceParticipantListUpdatedEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onConferenceParticipantListUpdated(this, ptt, connector, args);
   return kSuccess;
}

int PttSession::onConferenceSessionMediaStatusChanged(PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onConferenceSessionMediaStatusChanged(this, ptt, connector, args);
   return kSuccess;
}

int PttSession::onPttReceiverEndedEvent(PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onPttReceiverEndedEvent(this, ptt, evt, incomingTuple);
   return kSuccess;
}

// NetworkChangeHandler

void PttSession::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onNetworkChange(this, args);
}

void PttSession::onDiscoveryListUpdate(const PttDiscoveryListUpdate& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onDiscoveryListUpdate(this, args);
}

void PttSession::onPttSenderInitiateRecordingEvent(PushToTalkSessionHandle ptt, const PttSenderInitiateRecordingEvent& args)
{
   PttSessionState* state = getFactory()->getState(mState);
   if (state) state->onPttSenderInitiateRecordingEvent(this, args);
}

// DeadlineTimerHandler

void PttSession::onTimer(unsigned short timerId, void* appState)
{
   switch (timerId)
   {
      case PTT_SESSION_NETWORK_WIFI_STATUS_TIMER_ID: onWifiStatusTimeout(); break;
      default: onTimer(this, timerId, appState); break;
   }
}

void PttSession::enableIncomingMediaInactivityMonitor()
{
}

void PttSession::disableIncomingMediaInactivityMonitor()
{
}

void PttSession::logWifiStatus()
{
#ifdef __ANDROID__
   if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
   {
      if (network->networkTransport() == TransportWiFi)
      {
         int wifiRssi = 0xFFFFF, wifiSignalLevel = -1, wifiMaxSignalLevel = -1, wifiFreqMhz = -1, wifiChannel = -1, wifiLinkSpeedMbps = -1;
         wifiRssi = network->currentWifiRssi();
         wifiSignalLevel = network->currentWifiSignalLevel();
         wifiMaxSignalLevel = network->maxWifiSignalLevel();
         wifiFreqMhz = network->currentWifiFreqMhz();
         wifiChannel = network->currentWifiChannel();
         wifiLinkSpeedMbps = network->currentWifiLinkSpeedMbps();
         std::stringstream ss;
         ss << " (" << wifiSignalLevel << "/" << wifiMaxSignalLevel << ")";
         StatLog(PTT_LOGID_SESSION_WIFI_STATUS, << "PttSession::logWifiStatus(): ptt: " << mPtt << " wifiRssi: " << wifiRssi << " wifiSignalLevel: " << wifiSignalLevel << " wifiMaxSignalLevel: " << wifiMaxSignalLevel << ss.str() << " wifiFreqMhz: " << wifiFreqMhz << " wifiChannel: " << wifiChannel << " wifiLinkSpeedMbps: " << wifiLinkSpeedMbps);
         resetWifiStatusTimer();
      }
      else
      {
         DebugLog(<< "PttSession::logWifiStatus(): ptt: " << mPtt << " ignoring request to log wifi due to mismatch of network: " << network->networkTransport());
      }
   }
#endif
}

void PttSession::enableWifiStatusMonitor()
{
   if (NetworkChangeManagerInterface* network = dynamic_cast<NetworkChangeManagerInterface*>(NetworkChangeManager::getInterface(mPhone)))
   {
      if (network->networkTransport() == TransportWiFi)
      {
         // Get current wifi status and start the monitor
         DebugLog(<< "PttSession::disableWifiStatusMonitor(): ptt: " << mPtt << " enabling wifi status monitor");
         logWifiStatus();
         resetWifiStatusTimer();
      }
      else
      {
         DebugLog(<< "PttSession::enableWifiStatusMonitor(): ptt: " << mPtt << " ignoring request for wifi queries due to mismatch of network: " << network->networkTransport());
      }
   }
}

void PttSession::disableWifiStatusMonitor()
{
   // Get current wifi status and stop the monitor
   DebugLog(<< "PttSession::disableWifiStatusMonitor(): ptt: " << mPtt << " disabling wifi status monitor");
   logWifiStatus();
   cancelWifiStatusTimer();
}

void PttSession::resetWifiStatusTimer()
{
#ifdef __ANDROID__
   mWifiStatusTimer.cancel();
   mWifiStatusTimer.expires_from_now(mManager->getInternalSettings().networkWifiStatusIntervalMsecs);
   mWifiStatusTimer.async_wait(this, PTT_SESSION_NETWORK_WIFI_STATUS_TIMER_ID, NULL);
#endif
}

void PttSession::cancelWifiStatusTimer()
{
   mWifiStatusTimer.cancel();
}

void PttSession::onWifiStatusTimeout()
{
   logWifiStatus();
}

void PttSession::changeState(PttSessionStateType newStateType, int subState)
{
   PttSessionStateType currentStateType = mState;
   StatLog(PTT_LOGID_SESSION_STATE_CHANGE, << "PttSession::changeState(): " << this << " ptt: " << getPtt() << " service: " << mService << " changing state of " << getSessionType() << " session from: " << PttSessionState::getName(currentStateType) << " to: " << PttSessionState::getName(newStateType) << " sub-state: " << subState);

   PttSessionStateFactory* factory = getFactory();
   if (!factory)
   {
      InfoLog(<< "PttSession::changeState(): " << this << " ptt: " << getPtt() << " service: " << mService << " factory not initialized");
      return;
   }

   PttSessionState* currentState = factory->getState(currentStateType);
   if (currentState)
   {
      currentState->onExit(this);
   }

   mState = newStateType;

   PttSessionStateChangedEvent evt;
   evt.service = mService;
   evt.currentState = ((newStateType == PttSessionState_InitialActive) ? PttSessionState_Active : newStateType);
   evt.previousState = ((currentStateType == PttSessionState_InitialActive) ? PttSessionState_Active : currentStateType);
   evt.channelId = getChannelId().c_str();
   evt.recipients = getRecipients();
   evt.connectedCalls = getPttSessionConnectedCallCount();
   evt.totalCalls = getPttSessionTotalCallCount();
   evt.mediaInfoSent = mMediaInfoSent;
   evt.mediaInfoReceived = mMediaInfoReceived;

   if ((newStateType == PttSessionState_Idle) && (currentStateType != PttSessionState_Idle) && (currentStateType != PttSessionState_Ending))
   {
      DebugLog(<< "PttSession::changeState(): " << this << " ptt: " << getPtt() << "  service: " << mService << " send out an ending ptt status update for normal state transitions");
      PttSessionStateChangedEvent endingEvt = evt;
      endingEvt.currentState = PttSessionState_Ending;
      endingEvt.previousState = ((currentStateType == PttSessionState_InitialActive) ? PttSessionState_Active : currentStateType);
      mManager->getPttManager()->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionStateChanged), mService, getPtt(), endingEvt);
      evt.previousState = PttSessionState_Ending;
   }

   mManager->getPttManager()->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionStateChanged), mService, getPtt(), evt);

   PttSessionState* newState = factory->getState(newStateType);
   if (newState)
   {
      newState->setSubState(subState);
      newState->onEntry(this);
   }

   if (newStateType == PttSessionState_Idle)
   {
      DebugLog(<< "PttSession::changeState(): " << this << " ptt: " << getPtt() << " service: " << mService << " destroying " << getSessionType() << " session");

      cancelTimers();
      mManager->destroySession(getPtt());
   }
}

void PttSession::sendStatusUpdate(PttSessionStateType newStateType)
{
   PttSessionStateType currentStateType = mState;
   InfoLog(<< "PttSession::changeState(): " << this << " ptt: " << getPtt() << " service: " << mService << " changing state from: " << PttSessionState::getName(currentStateType) << " to: " << PttSessionState::getName(newStateType));
   mState = newStateType;

   PttSessionStateChangedEvent evt;
   evt.service = mService;
   evt.currentState = ((newStateType == PttSessionState_InitialActive) ? PttSessionState_Active : newStateType);
   evt.previousState = ((mState == PttSessionState_InitialActive) ? PttSessionState_Active : mState);
   evt.channelId = getChannelId().c_str();
   evt.connectedCalls = getPttSessionConnectedCallCount();
   evt.totalCalls = getPttSessionTotalCallCount();
   evt.mediaInfoSent = mMediaInfoSent;
   evt.mediaInfoReceived = mMediaInfoReceived;

   mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionStateChanged), mService, getPtt(), evt);
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
