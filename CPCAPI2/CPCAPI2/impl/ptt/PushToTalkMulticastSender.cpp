#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkMulticastSender.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT
#define PTT_MULTICAST_RESEND_COUNT                                        3
#define PTT_MULTICAST_MAX_MESSAGE_COUNT                                   10


namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkMulticastSender::PushToTalkMulticastSender(
   boost::asio::io_context& io_context,
   const boost::asio::ip::address& multicast_address,
   const int multicast_port) :
endpoint_(multicast_address, multicast_port),
socket_(io_context, endpoint_.protocol()),
timer_(io_context),
message_count_(0),
retry_count_(0)
{
   StackLog(<< "PushToTalkMulticastSender(): target address is: " << endpoint_.address().to_string());
}

void PushToTalkMulticastSender::Send(const std::string& payload)
{
   StackLog(<< "PushToTalkMulticastSender::Send(): payload: " << payload);
   message_ = payload;
   message_count_ = 0;
   retry_count_ = 0;
   do_send_ex();
}

void PushToTalkMulticastSender::do_send()
{
   StackLog(<< "PushToTalkMulticastSender::do_send()");
   if (retry_count_++ < PTT_MULTICAST_RESEND_COUNT)
   {
      message_count_ = 0;
      do_send_ex();
   }
   else
   {
      delete this;
   }
}

void PushToTalkMulticastSender::do_send_ex()
{
   StackLog(<< "PushToTalkMulticastSender::do_send_ex()");
   socket_.async_send_to(
      boost::asio::buffer(message_), endpoint_,
      [this](boost::system::error_code ec, std::size_t)
   {
      if (!ec && message_count_++ < PTT_MULTICAST_MAX_MESSAGE_COUNT)
      {
         do_timeout();
      }
      else
      {
         // do_send();
         delete this;
      }
   });
}

void PushToTalkMulticastSender::do_timeout()
{
   StackLog(<< "PushToTalkMulticastSender::do_timeout()");
   timer_.expires_after(std::chrono::milliseconds(20));
   timer_.async_wait([this](boost::system::error_code ec)
   {
      if (!ec)
         do_send_ex();
   });
}

}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
