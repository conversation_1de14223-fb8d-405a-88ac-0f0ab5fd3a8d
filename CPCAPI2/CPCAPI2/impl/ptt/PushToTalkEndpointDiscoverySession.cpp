#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkEndpointDiscoverySession.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"
#include <resip/stack/Tuple.hxx>

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

PttEndpointDiscoverySessionStateFactory::PttEndpointDiscoverySessionStateFactory(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session) :
mManager(manager),
mSession(session)
{
}

PttEndpointDiscoverySessionStateFactory::~PttEndpointDiscoverySessionStateFactory()
{
   for (PttEndpointDiscoverySessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void PttEndpointDiscoverySessionStateFactory::create()
{
   if (!mManager) return;

   if (mStates.size() > 0)
   {
      ErrLog(<< "PttEndpointDiscoverySessionStateFactory::create(): " << this << " manager: " << mManager << " state factory already initialized");
      return;
   }

   mStates[PttEndpointDiscoveryStateType_Idle] = create(PttEndpointDiscoveryStateType_Idle);
   mStates[PttEndpointDiscoveryStateType_Starting] = create(PttEndpointDiscoveryStateType_Starting);
   mStates[PttEndpointDiscoveryStateType_Polling] = create(PttEndpointDiscoveryStateType_Polling);
   mStates[PttEndpointDiscoveryStateType_Sync] = create(PttEndpointDiscoveryStateType_Sync);

   for (PttEndpointDiscoverySessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      StackLog(<< "PttEndpointDiscoverySessionStateFactory::create(): " << PttEndpointDiscoverySessionState::getName(i->first) << " : " << i->first);
   }
}

PttEndpointDiscoverySessionState* PttEndpointDiscoverySessionStateFactory::create(PttEndpointDiscoveryStateType type)
{
   PttEndpointDiscoverySessionState* state = NULL;
   switch (type)
   {
      case PttEndpointDiscoveryStateType_Idle: state = new PttEndpointDiscoverySessionIdleState(mManager, mSession); break;
      case PttEndpointDiscoveryStateType_Starting: state = new PttEndpointDiscoverySessionStartingState(mManager, mSession); break;
      case PttEndpointDiscoveryStateType_Polling: state = new PttEndpointDiscoverySessionPollingState(mManager, mSession); break;
      case PttEndpointDiscoveryStateType_Sync: state = new PttEndpointDiscoverySessionSyncState(mManager, mSession); break;
      default: break;
   }

   return state;
}

PttEndpointDiscoverySessionState* PttEndpointDiscoverySessionStateFactory::getState(PttEndpointDiscoveryStateType type)
{
   if (!mManager || mStates.size() == 0)
   {
      ErrLog(<< "PttEndpointDiscoverySessionStateFactory::getState(): " << this << " manager: " << mManager << " state factory not initialized");
      return NULL;
   }
   return mStates[type];
}

PttEndpointDiscoverySessionState::PttEndpointDiscoverySessionState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session, PttEndpointDiscoveryStateType state) :
mManager(manager),
mInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mSession(session),
mState(state),
mService(manager->getService())
{
}

PttEndpointDiscoverySessionState::~PttEndpointDiscoverySessionState()
{
}

PttEndpointDiscoveryStateType PttEndpointDiscoverySessionState::getType()
{
   return mState;
}

std::string PttEndpointDiscoverySessionState::getName()
{
   return (PttEndpointDiscoverySessionState::getName(mState));
}

std::string PttEndpointDiscoverySessionState::getName(PttEndpointDiscoveryStateType type)
{
   std::string name = "PttEndpointDiscoveryStateType_Invalid";
   switch (type)
   {
      case PttEndpointDiscoveryStateType_Idle: name = "PttEndpointDiscoveryStateType_Idle"; break;
      case PttEndpointDiscoveryStateType_Starting: name = "PttEndpointDiscoveryStateType_Starting"; break;
      case PttEndpointDiscoveryStateType_Polling: name = "PttEndpointDiscoveryStateType_Polling"; break;
      case PttEndpointDiscoveryStateType_Sync: name = "PttEndpointDiscoveryStateType_Sync"; break;
      default: break;
   }

   return name;
}

bool PttEndpointDiscoverySessionState::isUpdating()
{
   if (mSession->mDiscoveryRetry)
   {
      return true;
   }
   return false;
}

// Associated to PushToTalkManager

void PttEndpointDiscoverySessionState::startService(PushToTalkServiceHandle service)
{
   DebugLog(<< "PttEndpointDiscoverySessionState::startService(): service: " << mService << " ignoring startup service as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::shutdownService(PushToTalkServiceHandle service)
{
   DebugLog(<< "PttEndpointDiscoverySessionState::shutdownService(): service: " << mService << " shutdown service in endpoint discovery session in current state: " << getName());
   mSession->changeState(PttEndpointDiscoveryStateType_Idle);
}

void PttEndpointDiscoverySessionState::onQueryEndpointsRequest(const PttQueryEndpointsRequest& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttEndpointDiscoverySessionState::onQueryEndpointsRequest(): service: " << mService << " ignoring request message from " <<  incomingTuple.presentationFormat() << " as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttEndpointDiscoverySessionState::onQueryEndpointsResponse(): service: " << mService << " ignoring response message from " <<  incomingTuple.presentationFormat() << " as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttEndpointDiscoverySessionState::onReportEndpointsEvent(): service: " << mService << " ignoring report message from " <<  incomingTuple.presentationFormat() << " as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onSenderStarted()
{
   DebugLog(<< "PttEndpointDiscoverySessionState::onSenderStarted(): service: " << mService << " ignoring sender started event as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
   StackLog(<< "PttEndpointDiscoverySessionState::onOutgoingSendCompleted(): service: " << mService << " ignoring outgoing send completed event as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const std::string& payload)
{
   StackLog(<< "PttEndpointDiscoverySessionState::onOutgoingSendFailed(): service: " << mService << " ignoring outgoing send failed event as not handled in endpoint discovery session in current state: " << getName());
}

void PttEndpointDiscoverySessionState::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   std::string localIpAddress = mSession->mLocalEndpoint.presentationFormat().c_str();
   mSession->mLocalEndpoint = mSession->getLocalAddress();
   DebugLog(<< "PttEndpointDiscoverySessionState::onNetworkChange(): service: " << mService << " network change event to: " << args.networkTransport
      << " network with ip-address: " << mSession->getLocalEndpoint() << " (previously: " << localIpAddress << ") in endpoint discovery session in current state: " << getName());

   mSession->changeState(PttEndpointDiscoveryStateType_Idle);
}

PttEndpointDiscoverySessionIdleState::PttEndpointDiscoverySessionIdleState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session) :
PttEndpointDiscoverySessionState(manager, session, PttEndpointDiscoveryStateType::PttEndpointDiscoveryStateType_Idle)
{
}

PttEndpointDiscoverySessionIdleState::~PttEndpointDiscoverySessionIdleState()
{
}

void PttEndpointDiscoverySessionIdleState::onEntry()
{
   StatLog(PTT_LOGID_LAN_DISCOVERY_END, << "PttEndpointDiscoverySessionIdleState::onEntry(): " << this << " manager: " << mManager << " service: " << mService << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   mSession->mDiscoveryRequestsSent = 0;
   mSession->mDiscoveryResponsesReceived = 0;
   mSession->mLastUpdateTime = resip::ResipClock::getTimeMs();
   mSession->mEndpointList.clear();
   mSession->mDiscoveryTargets.clear();
   mSession->mDiscoveryStartTime = std::chrono::system_clock::now();
   mSession->mInitialDiscoveryCompleted = false;
   mSession->mDiscoveryRetryTimeoutIntervalMsecs = 0;
   mSession->mDiscoveryRetry = false;

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   for (cpc::vector<PttIdentity>::iterator i = settings.localIdentities.begin(); i != settings.localIdentities.end(); ++i)
   {
      (*i).ipAddress = "";
   }
}

void PttEndpointDiscoverySessionIdleState::onExit()
{
   DebugLog(<< "PttEndpointDiscoverySessionIdleState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
}

void PttEndpointDiscoverySessionIdleState::startService(PushToTalkServiceHandle service)
{
   StatLog(PTT_LOGID_LAN_DISCOVERY_START, << "PttEndpointDiscoverySessionIdleState::startService(): service: " << mService << " startup service in endpoint discovery session in current state: " << getName() << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (settings.unicastDiscoveryEnabled)
   {
      if (NetworkChangeManager* network = NetworkChangeManager::getInterface(mPhone))
      {
         if (network->networkTransport() == TransportWiFi)
         {
            mSession->mLocalEndpoint = mSession->getLocalAddress();
            for (cpc::vector<PttIdentity>::iterator i = settings.localIdentities.begin(); i != settings.localIdentities.end(); ++i)
            {
               (*i).ipAddress = mSession->mLocalEndpoint.presentationFormat().c_str();
            }

            mSession->changeState(PttEndpointDiscoveryStateType_Starting);
         }
         else
         {
            DebugLog(<< "PttEndpointDiscoverySessionIdleState::startService(): service: " << mService << " ignoring start service as the network transport is not wifi (is " << network->networkTransport() << ")");
            mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, "wifi network not available");
         }
      }
   }
   else
   {
      DebugLog(<< "PttEndpointDiscoverySessionIdleState::startService(): service: " << mService << " ignoring start service as unicast discovery is disabled");
      // TODO: Should this go straight to connected service status, instead of failure
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Failure, PttServiceStatusChangedEvent::Reason_None, "unicast discovery disabled");
   }
}

PttEndpointDiscoverySessionStartingState::PttEndpointDiscoverySessionStartingState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session) :
PttEndpointDiscoverySessionState(manager, session, PttEndpointDiscoveryStateType::PttEndpointDiscoveryStateType_Starting),
mTransportStartedTimer(mPhone->getSdkModuleThread())
{
}

PttEndpointDiscoverySessionStartingState::~PttEndpointDiscoverySessionStartingState()
{
}

void PttEndpointDiscoverySessionStartingState::onEntry()
{
   DebugLog(<< "PttEndpointDiscoverySessionStartingState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   resetUnicastTransportStartupTimer();
}

void PttEndpointDiscoverySessionStartingState::onExit()
{
   DebugLog(<< "PttEndpointDiscoverySessionStartingState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   mTransportStartedTimer.cancel();
}

void PttEndpointDiscoverySessionStartingState::resetUnicastTransportStartupTimer()
{
   mTransportStartedTimer.cancel();
   mTransportStartedTimer.expires_from_now(PTT_UNICAST_TRANSPORT_STARTUP_INTERVAL_MSECS);
   mTransportStartedTimer.async_wait(this, PTT_UNICAST_TRANSPORT_STARTUP_TIMER_ID, NULL);
}

void PttEndpointDiscoverySessionStartingState::onSenderStarted()
{
   StackLog(<< "PttEndpointDiscoverySessionStartingState::onSenderStarted(): service: " << mService << " unicast sender started successfully in endpoint discovery session in current state: " << getName());
   if (NetworkChangeManager* network = NetworkChangeManager::getInterface(mPhone))
   {
      if (network->networkTransport() == TransportWiFi)
      {
         mSession->sendQueryEndpointsRequest();
         mSession->mDiscoveryStartTime = std::chrono::system_clock::now();

         std::string localIpAddress = mSession->mLocalEndpoint.presentationFormat().c_str();
         mSession->mEndpointList[localIpAddress] = mSession->getLocalIdentity();

         // mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connecting);
         mSession->changeState(PttEndpointDiscoveryStateType_Polling);
      }
      else
      {
         DebugLog(<< "PttEndpointDiscoverySessionStartingState::onSenderStarted(): service: " << mService << " not proceeding with network discovery as the network transport is not wifi (is " << network << ")");
         mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Disconnected, PttServiceStatusChangedEvent::Reason_ConnectionFailure, "wifi network not available");
         mSession->changeState(PttEndpointDiscoveryStateType_Idle);
      }
   }
}

// DeadlineTimerHandler

void PttEndpointDiscoverySessionStartingState::onTimer(unsigned short timerId, void* appState)
{
   StackLog(<< "PttEndpointDiscoverySessionStartingState::onTimer(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " timerId: " << timerId);
   if (timerId == PTT_UNICAST_TRANSPORT_STARTUP_TIMER_ID)
   {
      PttServiceStartedEvent startedEvt;
      mManager->getPttManager()->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);
      mSession->changeState(PttEndpointDiscoveryStateType_Idle);
   }
   else
   {
      InfoLog(<< "PttEndpointDiscoverySessionStartingState::onTimer(): " << this << " timerId: " << timerId << " is invalid");
      assert(0);
   }
}

PttEndpointDiscoverySessionPollingState::PttEndpointDiscoverySessionPollingState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session) :
PttEndpointDiscoverySessionState(manager, session, PttEndpointDiscoveryStateType::PttEndpointDiscoveryStateType_Polling),
mUnicastEndpointListTimer(mPhone->getSdkModuleThread()),
mUnicastTransportSendTimer(mPhone->getSdkModuleThread())
// mUnicastInitialRetryTimer(mPhone->getSdkModuleThread()),
// mInitialRetryCompleted(false)
{
}

PttEndpointDiscoverySessionPollingState::~PttEndpointDiscoverySessionPollingState()
{
}

void PttEndpointDiscoverySessionPollingState::onEntry()
{
   DebugLog(<< "PttEndpointDiscoverySessionPollingState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " initial discovery-completed: " << mSession->mInitialDiscoveryCompleted << " retry-discovery: " << mSession->mDiscoveryRetry << " report-timeout: " << mManager->getInternalSettings().unicastEndpointListIntervalMsecs);
   if ((mSession->mInitialDiscoveryCompleted) && (mSession->mDiscoveryRetry == false))
   {
      resetUnicastEndpointListTimer();
   }
   else
   {
      // Add timer for when discovery has been triggered, in case we never get onOutgoingSendCompleted
      if (mSession->mDiscoveryRetry)
      {
         resetUnicastEndpointListTimer();
      }
      else
      {
         resetUnicastDiscoverySendTimer();
      }
   }
}

void PttEndpointDiscoverySessionPollingState::onExit()
{
   DebugLog(<< "PttEndpointDiscoverySessionPollingState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   mSession->mDiscoveryRetry = false;
   mUnicastEndpointListTimer.cancel();
   mUnicastTransportSendTimer.cancel();
   // mUnicastInitialRetryTimer.cancel();
}

void PttEndpointDiscoverySessionPollingState::resetUnicastEndpointListTimer()
{
   mUnicastEndpointListTimer.cancel();
   mUnicastEndpointListTimer.expires_from_now(mManager->getInternalSettings().unicastEndpointListIntervalMsecs);
   mUnicastEndpointListTimer.async_wait(this, PTT_SESSION_UNICAST_ENDPOINT_LIST_TIMER_ID, NULL);
}

void PttEndpointDiscoverySessionPollingState::resetUnicastDiscoverySendTimer()
{
   mUnicastTransportSendTimer.cancel();
   mUnicastTransportSendTimer.expires_from_now(mManager->getInternalSettings().unicastDiscoverySendIntervalMsecs);
   mUnicastTransportSendTimer.async_wait(this, PTT_UNICAST_DISCOVERY_SEND_TIMER_ID, NULL);
}

/*
void PttEndpointDiscoverySessionPollingState::resetUnicastDiscoveryRetryTimer()
{
   mUnicastInitialRetryTimer.cancel();
   mUnicastInitialRetryTimer.expires_from_now(mManager->getInternalSettings().unicastInitialDiscoveryRetryIntervalMsecs);
   mUnicastInitialRetryTimer.async_wait(this, PTT_UNICAST_DISCOVERY_INITIAL_RETRY_TIMER_ID, NULL);
}
*/

void PttEndpointDiscoverySessionPollingState::onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
   mUnicastTransportSendTimer.cancel();

   std::merge(targets.begin(), targets.end(), mSession->mDiscoveryTargets.begin(), mSession->mDiscoveryTargets.end(), std::inserter(mSession->mDiscoveryTargets, mSession->mDiscoveryTargets.begin()));
   mSession->mDiscoveryRequestsSent = targets.size();
   StackLog(<< "PttEndpointDiscoverySessionPollingState::onOutgoingSendCompleted(): " << this << " service: " << mService << " discovery-status: requests-sent: " << mSession->mDiscoveryRequestsSent << " endpoints-discovered: " << mSession->mEndpointList.size());
   resetUnicastEndpointListTimer();
}

void PttEndpointDiscoverySessionPollingState::onQueryEndpointsRequest(const PttQueryEndpointsRequest& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionPollingState::onQueryEndpointsRequest(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str());
   int endpointListCountBeforeMerge = mSession->mEndpointList.size();
   mSession->handleOnQueryEndpointsRequest(evt, incomingTuple);
   if (mSession->mEndpointList.size() > endpointListCountBeforeMerge)
   {
      resetUnicastEndpointListTimer();
   }
}

void PttEndpointDiscoverySessionPollingState::onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionPollingState::onQueryEndpointsResponse(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str());
   int endpointListCountBeforeMerge = mSession->mEndpointList.size();
   mSession->handleOnQueryEndpointsResponse(evt, incomingTuple);
   if (mSession->mEndpointList.size() > endpointListCountBeforeMerge)
   {
      resetUnicastEndpointListTimer();
   }
}

void PttEndpointDiscoverySessionPollingState::onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionPollingState::onReportEndpointsEvent(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str());
   mSession->handleOnReportEndpointsEvent(evt, incomingTuple);
}

// DeadlineTimerHandler

void PttEndpointDiscoverySessionPollingState::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == PTT_SESSION_UNICAST_ENDPOINT_LIST_TIMER_ID)
   {
      StackLog(<< "PttEndpointDiscoverySessionPollingState::onTimer(): " << this << " manager: " << mManager << " local-endpoint: "
         << mSession->mLocalEndpoint.presentationFormat().c_str() << " initial-discovery: " << mSession->mInitialDiscoveryCompleted
         << " discovery-retry: " << mSession->mDiscoveryRetry << " timerId: " << timerId << " endpoint list timeout");
      /*
      if (mSession->mInitialDiscoveryCompleted)
      {
         mSession->changeState(PttEndpointDiscoveryStateType_Sync);
      }
      else
      {
         if (mInitialRetryCompleted)
         {
            mSession->changeState(PttEndpointDiscoveryStateType_Sync);
         }
         else
         {
            resetUnicastDiscoveryRetryTimer();
         }
      }
      */
      mSession->changeState(PttEndpointDiscoveryStateType_Sync);
   }
   else if (timerId == PTT_UNICAST_DISCOVERY_SEND_TIMER_ID)
   {
      WarningLog(<< "PttEndpointDiscoverySessionPollingState::onTimer(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " timerId: " << timerId << " discovery outgoing send timeout");
      mSession->changeState(PttEndpointDiscoveryStateType_Idle);
   }
   /*
   else if (timerId == PTT_UNICAST_DISCOVERY_INITIAL_RETRY_TIMER_ID)
   {
      WarningLog(<< "PttEndpointDiscoverySessionPollingState::onTimer(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " timerId: " << timerId << " discovery initial retry timeout");
      mInitialRetryCompleted = true;
      if (mSession->mEndpointList.size() == 1)
      {
         mSession->sendQueryEndpointsRequest();
         resetUnicastDiscoverySendTimer();
      }
   }
   */
   else
   {
      InfoLog(<< "PttEndpointDiscoverySessionPollingState::onTimer(): " << this << " timerId: " << timerId << " is invalid");
      assert(0);
   }
}

PttEndpointDiscoverySessionSyncState::PttEndpointDiscoverySessionSyncState(PushToTalkManagerImpl* manager, PttEndpointDiscoverySession* session) :
PttEndpointDiscoverySessionState(manager, session, PttEndpointDiscoveryStateType::PttEndpointDiscoveryStateType_Sync),
mUnicastDiscoveryRetryTimer(mPhone->getSdkModuleThread())
{
}

PttEndpointDiscoverySessionSyncState::~PttEndpointDiscoverySessionSyncState()
{
   mUnicastDiscoveryRetryTimer.cancel();
}

void PttEndpointDiscoverySessionSyncState::onEntry()
{
   StatLog(PTT_LOGID_LAN_DISCOVERY_SYNC, << "PttEndpointDiscoverySessionSyncState::onEntry(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " endpoint-count: " << mSession->mEndpointList.size());
   mSession->sendReportEndpointsEvent();
   mSession->mDiscoveryRequestsSent = 0;
   mSession->mDiscoveryResponsesReceived = 0;
   mSession->mDiscoveryTargets.clear();
   mSession->mDiscoveryStartTime = std::chrono::system_clock::now();

   if (mSession->mEndpointList.size() == 1)
   {
      // No remote endpoints discovered
      resetUnicastDiscoveryRetryTimer();
   }
   mSession->logEndpointList();
}

void PttEndpointDiscoverySessionSyncState::onExit()
{
   DebugLog(<< "PttEndpointDiscoverySessionSyncState::onExit(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str());
   mUnicastDiscoveryRetryTimer.cancel();
}

void PttEndpointDiscoverySessionSyncState::resetUnicastDiscoveryRetryTimer()
{
   mUnicastDiscoveryRetryTimer.cancel();
   mUnicastDiscoveryRetryTimer.expires_from_now(mSession->getDiscoveryRetryTimeoutIntervalMsecs());
   mUnicastDiscoveryRetryTimer.async_wait(this, PTT_UNICAST_DISCOVERY_RETRY_TIMER_ID, NULL);
}

void PttEndpointDiscoverySessionSyncState::onQueryEndpointsRequest(const PttQueryEndpointsRequest& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionSyncState::onQueryEndpointsRequest(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str());
   //int endpointListCountBeforeMerge = mSession->mEndpointList.size();
   mSession->handleOnQueryEndpointsRequest(evt, incomingTuple);
   mSession->changeState(PttEndpointDiscoveryStateType_Polling);
}

void PttEndpointDiscoverySessionSyncState::onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionSyncState::onQueryEndpointsResponse(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str());
   //int endpointListCountBeforeMerge = mSession->mEndpointList.size();
   mSession->handleOnQueryEndpointsResponse(evt, incomingTuple);
   mSession->changeState(PttEndpointDiscoveryStateType_Polling);
}

void PttEndpointDiscoverySessionSyncState::onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   DebugLog(<< "PttEndpointDiscoverySessionSyncState::onReportEndpointsEvent(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << incomingTuple.presentationFormat().c_str() << " endpoint count: " << evt.endpoints.size() << " tid: " << evt.transactionId);
   mSession->handleOnReportEndpointsEvent(evt, incomingTuple);
}

// DeadlineTimerHandler

void PttEndpointDiscoverySessionSyncState::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == PTT_UNICAST_DISCOVERY_RETRY_TIMER_ID)
   {
      DebugLog(<< "PttEndpointDiscoverySessionSyncState::onTimer(): " << this << " manager: " << mManager << " local-endpoint: " << mSession->mLocalEndpoint.presentationFormat().c_str() << " endpoint-list: " << mSession->mEndpointList.size() << " timerId: " << timerId << " discovery retry timeout");
      if (mSession->mEndpointList.size() == 1)
      {
         mSession->mDiscoveryRetry = true;
         mSession->sendQueryEndpointsRequest();
         mSession->changeState(PttEndpointDiscoveryStateType_Polling);
         return;
      }
   }
   else
   {
      InfoLog(<< "PttEndpointDiscoverySessionSyncState::onTimer(): " << this << " timerId: " << timerId << " is invalid");
      assert(0);
   }
}

PttEndpointDiscoverySession::PttEndpointDiscoverySession(PushToTalkManagerImpl* manager) :
mState(PttEndpointDiscoveryStateType_Idle),
mService(manager->getService()),
mManager(manager),
mInterface(manager->getPttManager()),
mPhone(manager->getPhone()),
mFactory(manager, this),
mLastUpdateTime(0),
mDiscoveryStartTime(std::chrono::system_clock::now()),
mDiscoveryRequestsSent(0),
mDiscoveryResponsesReceived(0),
mInitialDiscoveryCompleted(false),
mDiscoveryRetryTimeoutIntervalMsecs(0),
mDiscoveryRetry(false)
{
   mFactory.create();

   DebugLog(<< "PttEndpointDiscoverySession(): " << this << " created discovery session for ptt manager: " << mManager);
}

PttEndpointDiscoverySession::~PttEndpointDiscoverySession()
{
   DebugLog(<< "~PttEndpointDiscoverySession(): " << this << " destroyed discovery session for ptt manager: " << mManager << " local-endpoint: " << mLocalEndpoint.presentationFormat().c_str());
}

resip::Tuple PttEndpointDiscoverySession::getLocalAddress()
{
   resip::Data localIpAddress;
#ifdef CPCAPI2_AUTO_TEST
   localIpAddress = mManager->getServiceSettings().unicastBindAddress.c_str();
#else
   CPCAPI2::IpHelpers::getPreferredLocalIpAddress(resip::Tuple("*******", 53, resip::V4), localIpAddress);
#endif
   return resip::Tuple(localIpAddress.c_str(), mManager->getServiceSettings().unicastPort, resip::UDP);
}

std::string PttEndpointDiscoverySession::getLocalEndpoint()
{
   std::stringstream os;
   os << mLocalEndpoint.presentationFormat().c_str() << ":" << mLocalEndpoint.getPort();
   return os.str().c_str();
}
 
PttIdentity PttEndpointDiscoverySession::getLocalIdentity()
{
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   PttIdentity identity;
   if (settings.localIdentities.size() > 0)
   {
      identity = settings.localIdentities[0];
   }
   else
   {
      identity.ipAddress = mLocalEndpoint.presentationFormat().c_str();
   }
   return identity;
}

bool PttEndpointDiscoverySession::isUpdating()
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) return (state->isUpdating());
   return false;
}

unsigned int PttEndpointDiscoverySession::getDiscoveryRetryTimeoutIntervalMsecs()
{
   if (mDiscoveryRetryTimeoutIntervalMsecs == 0)
   {
      mDiscoveryRetryTimeoutIntervalMsecs = mManager->getInternalSettings().unicastDiscoveryRetryIntervalMsecs;
   }
   else
   {
      mDiscoveryRetryTimeoutIntervalMsecs = (2 * mDiscoveryRetryTimeoutIntervalMsecs);
      if (mDiscoveryRetryTimeoutIntervalMsecs > PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MAXIMUM_MSECS)
      {
         mDiscoveryRetryTimeoutIntervalMsecs = PTT_UNICAST_DISCOVERY_RETRY_INTERVAL_MAXIMUM_MSECS;
      }
   }
   return mDiscoveryRetryTimeoutIntervalMsecs;
}

void PttEndpointDiscoverySession::changeState(PttEndpointDiscoveryStateType newStateType)
{
   PttEndpointDiscoveryStateType currentStateType = mState;
   InfoLog(<< "PttEndpointDiscoverySession::changeState(): " << this << " service: " << mService << " changing state of endpoint discovery session from: " << PttEndpointDiscoverySessionState::getName(currentStateType) << " to: " << PttEndpointDiscoverySessionState::getName(newStateType));

   PttEndpointDiscoverySessionState* currentState = mFactory.getState(currentStateType);
   if (currentState)
   {
      currentState->onExit();
   }

   mState = newStateType;

   PttEndpointDiscoverySessionState* newState = mFactory.getState(newStateType);
   if (newState)
   {
      newState->onEntry();
   }
}

void PttEndpointDiscoverySession::queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service)
{
   sendEndpointList();
}

void PttEndpointDiscoverySession::startService(PushToTalkServiceHandle service)
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   DebugLog(<< "PttEndpointDiscoverySession::startService(): service: " << service << " state: " << state << "(" << (state ? state->getName() : "invalid") << ")");
   if (state) state->startService(service);
}

void PttEndpointDiscoverySession::shutdownService(PushToTalkServiceHandle service)
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->shutdownService(service);
}

void PttEndpointDiscoverySession::sendQueryEndpointsRequest()
{
   // StackLog(<< "PttEndpointDiscoverySession::sendQueryEndpointsRequest(): " << this);
   PttQueryEndpointsRequest queryEndpointsReq;
   queryEndpointsReq.transactionId = (unsigned int)resip::Random::getCryptoRandom();
   queryEndpointsReq.senderIdentity = getLocalIdentity();

#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsRequestSent), mService, queryEndpointsReq);
#endif

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onQueryEndpointsRequest", true);
   serializer.addValue("QueryEndpointsRequest", queryEndpointsReq);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   bool sendCallback = false;
   if (mInitialDiscoveryCompleted)
   {
      sendCallback = false;
      if (mDiscoveryRetry)
      {
         sendCallback = true;
      }
   }
   else
   {
      sendCallback = true;
   }
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), sendCallback, true, "", false, 0, queryEndpointsReq.transactionId);

   // logEndpointList();
}

void PttEndpointDiscoverySession::sendQueryEndpointsResponse(const resip::Tuple& incomingTuple, unsigned int transactionId)
{
   // StackLog(<< "PttEndpointDiscoverySession::sendQueryEndpointsResponse(): " << this << " send endpoint response: " << incomingTuple.presentationFormat() << " with tid: " << transactionId);
   std::string localIpAddress = mLocalEndpoint.presentationFormat().c_str();

   PttQueryEndpointsResponse queryEndpointsResp;
   queryEndpointsResp.transactionId = transactionId;
   queryEndpointsResp.endpointIpAddress = localIpAddress.c_str();
   queryEndpointsResp.senderIdentity = getLocalIdentity();

   for (std::map<std::string, PttIdentity>::const_iterator i = mEndpointList.begin(); i != mEndpointList.end(); ++i)
   {
      queryEndpointsResp.endpoints.push_back(i->first.c_str());
   }

   if (queryEndpointsResp.endpoints.size() > 40)
   {
      PushToTalkManagerImpl::compressEndpointList(queryEndpointsResp.endpoints, queryEndpointsResp.endpointsCB64);
      queryEndpointsResp.endpoints.clear();
   }

#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsResponseSent), mService, queryEndpointsResp);
#endif

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onQueryEndpointsResponse", true);
   serializer.addValue("QueryEndpointsResponse", queryEndpointsResp);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), false, false, incomingTuple.presentationFormat().c_str(), false, mManager->getServiceSettings().unicastPort, 0, true);

   // logEndpointList();
}

void PttEndpointDiscoverySession::sendReportEndpointsEvent()
{
   // StackLog(<< "PttEndpointDiscoverySession::sendReportEndpointsEvent(): " << this);
   std::string localIpAddress = mLocalEndpoint.presentationFormat().c_str();

   PttReportEndpointsEvent reportEndpointsEvt;
   reportEndpointsEvt.transactionId = (unsigned int)resip::Random::getCryptoRandom();
   reportEndpointsEvt.senderIdentity = getLocalIdentity();

   for (std::map<std::string, PttIdentity>::const_iterator i = mEndpointList.begin(); i != mEndpointList.end(); ++i)
   {
      reportEndpointsEvt.endpoints.push_back(i->first.c_str());
   }

   if (reportEndpointsEvt.endpoints.size() > 40)
   {
      PushToTalkManagerImpl::compressEndpointList(reportEndpointsEvt.endpoints, reportEndpointsEvt.endpointsCB64);
      reportEndpointsEvt.endpoints.clear();
   }

#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsReportSent), mService, reportEndpointsEvt);
#endif

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onPttReportEndpointsEvent", true);
   serializer.addValue("PttReportEndpointsEvent", reportEndpointsEvt);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), false, true, "", false, 0, 0, true);

   if (!mInitialDiscoveryCompleted)
   {
      int deltaMs = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now() - mDiscoveryStartTime).count();
      mInitialDiscoveryCompleted = true;
      PttServiceStartedEvent startedEvt;
      startedEvt.startupSuccessful = true;
      startedEvt.endpointsDiscovered = reportEndpointsEvt.endpoints.size();
      startedEvt.discoveryRequestsSent = mDiscoveryRequestsSent;
      startedEvt.discoveryResponsesReceived = mDiscoveryResponsesReceived;
      InfoLog(<< "PttEndpointDiscoverySession::sendReportEndpointsEvent(): " << this << " startup discovery completed in " << deltaMs
         << " msecs, requests-sent: " << mDiscoveryRequestsSent
         << " responses-received: " << mDiscoveryResponsesReceived << " endpoints-discovered: " << startedEvt.endpointsDiscovered << " tid: " << reportEndpointsEvt.transactionId);
      mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttServiceStarted), mService, mService, startedEvt);

      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Connected);
      mManager->sendServiceStatus(PttServiceStatusChangedEvent::Status_Ready);
   }
   else
   {
      DebugLog(<< "PttEndpointDiscoverySession::sendReportEndpointsEvent(): " << this << " startup discovery already complete, endpoints-discovered: "
               << reportEndpointsEvt.endpoints.size() << " tid: " << reportEndpointsEvt.transactionId);
   }

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (settings.endpointListAutoUpdateEnabled)
   {
      sendEndpointList();
   }

   PttDiscoveryListUpdate updateEvt;
   mManager->onDiscoveryListUpdate(updateEvt);
   // logEndpointList();
}

void PttEndpointDiscoverySession::onQueryEndpointsRequest(const PttQueryEndpointsRequest& evt, const resip::Tuple& incomingTuple)
{
   if (!((std::find(mLastQueryEndpointsMessage.begin(), mLastQueryEndpointsMessage.end(), evt.transactionId) == mLastQueryEndpointsMessage.end()) || (evt.transactionId == 0)))
   {
      StackLog(<< "PttEndpointDiscoverySession::onQueryEndpointsRequest(): " << this << " service: " << mService << " ignore endpoint request message from " <<  incomingTuple.presentationFormat() << " as it is a retransmission with tid: " << evt.transactionId);
      return;
   }

   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onQueryEndpointsRequest(evt, incomingTuple);
}

void PttEndpointDiscoverySession::onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   if (!((std::find(mLastQueryEndpointsResponse.begin(), mLastQueryEndpointsResponse.end(), QueryEndpointsResponseId(incomingTuple, evt.transactionId)) == mLastQueryEndpointsResponse.end()) || (evt.transactionId == 0)))
   {
      StackLog(<< "PttEndpointDiscoverySession::onQueryEndpointsResponse(): " << this << " service: " << mService << " ignore endpoint response message from " <<  incomingTuple.presentationFormat() << " as it is a retransmission with tid: " << evt.transactionId);
      return;
   }

   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onQueryEndpointsResponse(evt, incomingTuple);
}

void PttEndpointDiscoverySession::onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   if (!((std::find(mLastReportEndpointsMessage.begin(), mLastReportEndpointsMessage.end(), evt.transactionId) == mLastReportEndpointsMessage.end()) || (evt.transactionId == 0)))
   {
      StackLog(<< "PttEndpointDiscoverySession::onReportEndpointsEvent(): " << this << " service: " << mService << " ignore endpoint report message from " <<  incomingTuple.presentationFormat() << " as it is a retransmission with tid: " << evt.transactionId);
      return;
   }

   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onReportEndpointsEvent(evt, incomingTuple);
}

void PttEndpointDiscoverySession::onSenderStarted()
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onSenderStarted();
}

void PttEndpointDiscoverySession::onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets)
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onOutgoingSendCompleted(targets);
}

void PttEndpointDiscoverySession::onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const std::string& payload)
{
   // TODO: is anything required in the session for discovery send failure
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   if (state) state->onOutgoingSendFailed(target, payload);
}

void PttEndpointDiscoverySession::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   PttEndpointDiscoverySessionState* state = mFactory.getState(mState);
   DebugLog(<< "PttEndpointDiscoverySession::onNetworkChange(): service: " << mService << " state: " << state << "(" << (state ? state->getName() : "invalid") << ")");
   if (state) state->onNetworkChange(args);
}

void PttEndpointDiscoverySession::handleOnQueryEndpointsRequest(const PttQueryEndpointsRequest& evt, const resip::Tuple& incomingTuple)
{
   mLastQueryEndpointsMessage.push_front(evt.transactionId);
   if (mLastQueryEndpointsMessage.size() > PTT_UNICAST_DISCOVERY_TRANSACTION_HISTORY_LIMIT)
   {
      mLastQueryEndpointsMessage.pop_back();
   }

   PttIdentity remoteIdentity = evt.senderIdentity;
   if (evt.senderIdentity.ipAddress.empty())
   {
      remoteIdentity.ipAddress = incomingTuple.presentationFormat().c_str(); // backwards compatibility
   }
   mEndpointList[incomingTuple.presentationFormat().c_str()] = remoteIdentity;
   sendQueryEndpointsResponse(incomingTuple, evt.transactionId);
#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsRequestReceived), mService, evt);
#endif

   // DebugLog(<< "PttEndpointDiscoverySession::handleOnQueryEndpointsRequest(): " << this << " received from " << evt.senderIdentity.ipAddress << " endpoint list count: " << mEndpointList.size());
   // logEndpointList();
}

void PttEndpointDiscoverySession::handleOnQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttEndpointDiscoverySession::handleOnQueryEndpointsResponse(): " << this << " handle endpoint response from " << incomingTuple.presentationFormat() << " with tid: " << evt.transactionId);

   mLastQueryEndpointsResponse.push_front(PttEndpointDiscoverySession::QueryEndpointsResponseId(incomingTuple, evt.transactionId));
   if (mLastQueryEndpointsResponse.size() > PTT_UNICAST_DISCOVERY_TRANSACTION_HISTORY_LIMIT)
   {
      mLastQueryEndpointsResponse.pop_back();
   }
   mLastUpdateTime = resip::ResipClock::getTimeMs();

   boost::asio::ip::udp::endpoint incomingTupleEp(boost::asio::ip::address::from_string(incomingTuple.presentationFormat().c_str()), mManager->getServiceSettings().unicastPort);
   mManager->unsend(evt.transactionId, incomingTupleEp);
   mManager->setKeepAliveTarget(incomingTupleEp, true);

#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsResponseReceived), mService, evt);
#endif

   int endpointListCountBeforeMerge = mEndpointList.size();
   PttIdentity remoteIdentity = evt.senderIdentity;
   if (evt.senderIdentity.ipAddress.empty())
   {
      remoteIdentity.ipAddress = incomingTuple.presentationFormat().c_str(); // backwards compatibility
   }
   // will override current name and address if it already exists, as it has been provided by the sender itself
   mEndpointList[incomingTuple.presentationFormat().c_str()] = remoteIdentity;

   std::pair<std::map<std::string, PttIdentity>::iterator, bool> ret;
   PttIdentity emptyId;
   if (evt.endpoints.size() > 0)
   {
      for (const cpc::string& ipAddrStr : evt.endpoints)
      {
         emptyId.ipAddress = ipAddrStr.c_str();
         // will not override identity of an existing endpoint, but a new identity will not be complete and only have the ip-address
         mEndpointList.insert(std::pair<std::string, PttIdentity>(ipAddrStr.c_str(), emptyId));
      }
   }
   else if (!evt.endpointsCB64.empty())
   {
      cpc::vector<cpc::string> endpointsFromCompressed;
      PushToTalkManagerImpl::decompressEndpointList(evt.endpointsCB64, endpointsFromCompressed);
      for (const cpc::string& ipAddrStr : evt.endpoints)
      {
         emptyId.ipAddress = ipAddrStr.c_str();
         mEndpointList.insert(std::pair<std::string, PttIdentity>(ipAddrStr.c_str(), emptyId));
      }
   }

   DebugLog(<< "PttEndpointDiscoverySession::handleOnQueryEndpointsResponse(): " << this << " discovered " << evt.endpointIpAddress << " updated endpoint list size from: " << endpointListCountBeforeMerge << " to " << mEndpointList.size());
   mDiscoveryResponsesReceived++;

   // logEndpointList();
}

void PttEndpointDiscoverySession::handleOnReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple)
{
   // StackLog(<< "PttEndpointDiscoverySession::handleOnReportEndpointsEvent(): " << this << " handle endpoint response from " << incomingTuple.presentationFormat() << " with tid: " << evt.transactionId);

   mLastReportEndpointsMessage.push_front(evt.transactionId);
   if (mLastReportEndpointsMessage.size() > PTT_UNICAST_DISCOVERY_TRANSACTION_HISTORY_LIMIT)
   {
      mLastReportEndpointsMessage.pop_back();
   }

   PttIdentity remoteIdentity = evt.senderIdentity;
   if (evt.senderIdentity.ipAddress.empty())
   {
      remoteIdentity.ipAddress = incomingTuple.presentationFormat().c_str(); // backwards compatibility
   }
   mEndpointList[incomingTuple.presentationFormat().c_str()] = remoteIdentity;
   PttIdentity emptyId;
   if (evt.endpoints.size() > 0)
   {
      for (const cpc::string& ipAddrStr : evt.endpoints)
      {
         emptyId.ipAddress = ipAddrStr.c_str();
         mEndpointList.insert(std::pair<std::string, PttIdentity>(ipAddrStr.c_str(), emptyId));
      }
   }
   else if (!evt.endpointsCB64.empty())
   {
      cpc::vector<cpc::string> endpointsFromCompressed;
      PushToTalkManagerImpl::decompressEndpointList(evt.endpointsCB64, endpointsFromCompressed);
      for (const cpc::string& ipAddrStr : evt.endpoints)
      {
         emptyId.ipAddress = ipAddrStr.c_str();
         mEndpointList.insert(std::pair<std::string, PttIdentity>(ipAddrStr.c_str(), emptyId));
      }
   }

   mLastUpdateTime = resip::ResipClock::getTimeMs();
#ifdef CPCAPI2_AUTO_TEST
   mInterface->firePttInternalEvent(cpcFunc(PushToTalkHandlerInternal::onPttQueryEndpointsReportReceived), mService, evt);
#endif

   // logEndpointList();
}

void PttEndpointDiscoverySession::sendEndpointList()
{
   // StackLog(<< "PttEndpointDiscoverySession::sendEndpointList(): " << this);
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (settings.serviceType == PttServiceType_WAN)
   {
      DebugLog(<< "PttEndpointDiscoverySession::sendEndpointList(): ignoring endpoint list transmission, as ptt wan mode enabled");
      return;
   }
   if (settings.endpointListFetchLimit <= 0)
   {
      DebugLog(<< "PttEndpointDiscoverySession::sendEndpointList(): ignoring endpoint list transmission, as endpoint list fetch limit is invalid");
      return;
   }

   bool sendList = false;
   PttEndpointListEvent listEvt;
   listEvt.localIdentity = getLocalIdentity();
   listEvt.totalEndpointCount = mEndpointList.size();
   listEvt.offset = 0;
   for (std::map<std::string, PttIdentity>::const_iterator j = mEndpointList.begin(); j != mEndpointList.end(); ++j)
   {
      listEvt.offset++;
      listEvt.endpoints.push_back(j->second);

      if (listEvt.offset == mEndpointList.size())
      {
         sendList = true;
      }
      else if ((listEvt.offset % (settings.endpointListFetchLimit)) == 0)
      {
         sendList = true;
      }

      if (sendList)
      {
         mInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttEndpointList), mService, mService, listEvt);
         listEvt.endpoints.clear();
         sendList = false;
      }
   }
}

void PttEndpointDiscoverySession::addToDiscoveryList(PttIdentity& identity)
{
   PttIdentity remoteIdentity = identity;
   // Ensure only complete identities are added through this method, as we don't want to overwrite complete identity data with partial data
   if (identity.ipAddress.empty() || identity.userName.empty() || identity.displayName.empty()) return;

   std::map<std::string, PttIdentity>::iterator i = mEndpointList.find(identity.ipAddress.c_str());
   if (i != mEndpointList.end())
   {
      PttIdentity existingIdentity = i->second;
      if ((existingIdentity.ipAddress == identity.ipAddress)
         && (existingIdentity.userName == identity.userName)
         && (existingIdentity.displayName == identity.displayName)
         && (existingIdentity.identityType == identity.identityType))
      {
         return;
      }
   }

   mEndpointList[identity.ipAddress.c_str()] = identity;
   mLastUpdateTime = resip::ResipClock::getTimeMs();

   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   if (settings.endpointListAutoUpdateEnabled)
   {
      sendEndpointList();
   }
   
   PttDiscoveryListUpdate updateEvt;
   mManager->onDiscoveryListUpdate(updateEvt);
}

void PttEndpointDiscoverySession::logEndpointList()
{
   CPCAPI2::Json::JsonDataPointer json = Json::MakeJsonDataPointer();
   bool supressLog(false);
   bool noOverhead(true);
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, supressLog, JSON_MODULE, "logEndpointList", noOverhead);
   serializer.addValue("totalCount", (uint32_t)mEndpointList.size());

   int count = 0;
   for (std::map<std::string, PttIdentity>::const_iterator i = mEndpointList.begin(); ((i != mEndpointList.end()) && (count < 50)); ++i)
   {
      count++;
      serializer.addValue("PttIdentity", i->second);
   }
   serializer.finalize();
 
   StatLog(PTT_LOGID_LAN_DISCOVERY_LIST, << "PttEndpointDiscoverySession::logEndpointList(): service: " << mService << " local-endpoint: " << getLocalEndpoint() << " total: " << mEndpointList.size() << " data: " << std::string(json->getMessageData(), json->getMessageSize()) << ((mEndpointList.size() > 50) ? " ..." : ""));
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
