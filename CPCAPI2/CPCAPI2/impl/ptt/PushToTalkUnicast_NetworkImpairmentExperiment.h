#pragma once

#include "cpcapi2.h"

#include <mutex>
#include <map>

namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkUnicast_NetworkImpairmentExperiment
{

public:

   ~PushToTalkUnicast_NetworkImpairmentExperiment() {}

   static void CreateExperiment(CPCAPI2::Phone* p);
   static void DestroyExperiment(CPCAPI2::Phone* p);
   static PushToTalkUnicast_NetworkImpairmentExperiment* GetExperiment(CPCAPI2::Phone* p);

   // Receiver Experiments

   unsigned int& loss_pcnt() {
      return loss_pcnt_;
   }
   const unsigned int loss_pcnt() const {
      return loss_pcnt_;
   }

   int& network_lag_min_ms() {
      return network_lag_min_ms_;
   }
   const int network_lag_min_ms() const {
      return network_lag_min_ms_;
   }

   int& network_lag_max_ms() {
      return network_lag_max_ms_;
   }
   const int network_lag_max_ms() const {
      return network_lag_max_ms_;
   }

   // Sender Experiments
   /*
   unsigned int& initial_packet_loss_count() {
      return initial_packet_loss_count_;
   }
   const unsigned int initial_packet_loss_count() const {
      return initial_packet_loss_count_;
   }
   */

   cpc::vector<cpc::string>& unresponsive_endpoints() {
      return unresponsive_endpoints_;
   }
   const cpc::vector<cpc::string>& unresponsive_endpoints() const {
      return unresponsive_endpoints_;
   }

private:

   PushToTalkUnicast_NetworkImpairmentExperiment();

   static std::map<CPCAPI2::Phone*, PushToTalkUnicast_NetworkImpairmentExperiment*> s_expMap;
   static std::mutex s_expMapMutex;

   unsigned int loss_pcnt_ = 0;
   int network_lag_min_ms_ = 0;
   int network_lag_max_ms_ = 0;
   unsigned int initial_packet_loss_count_ = 0;
   cpc::vector<cpc::string> unresponsive_endpoints_;

};

}

}
