#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkServerWebSocket.h"
#include "PushToTalkServerWebSocketSession.h"
#include "../util/cpc_logger.h"
#include "../log/LocalLogger.h"
#include "phone/PhoneInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

namespace CPCAPI2
{

namespace PushToTalk
{

PushToTalkServerWebSocket::PushToTalkServerWebSocket(CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, int httpPort) :
mPhone(NULL), mPttMgr(pttIf), mServerThread(NULL), mHttpPort(httpPort)
{
   StackLog(<< "PushToTalkServerWebSocket(): http port: " << httpPort);
}

PushToTalkServerWebSocket::~PushToTalkServerWebSocket()
{
   StackLog(<< "~PushToTalkServerWebSocket()");
}

void PushToTalkServerWebSocket::StartServer(CPCAPI2::Phone* phone, resip::ReadCallbackBase* startedCb)
{
   StackLog(<< "PushToTalkServerWebSocket::StartServer()");
   if (mServerThread != NULL)
   {
      return; // already started
   }

   mPhone = phone;

   mServerThread = new std::thread([&, startedCb]()
   {
      try {
         mWebSockServer.reset(new server());

         // Set logging settings
         mWebSockServer->set_access_channels(websocketpp::log::alevel::all);
         mWebSockServer->clear_access_channels(websocketpp::log::alevel::all);

         // Initialize Asio
         mWebSockServer->init_asio();

         // Register our message handler
         mWebSockServer->set_open_handler(std::bind(&PushToTalkServerWebSocket::onOpen, this, mWebSockServer.get(), std::placeholders::_1));

         mWebSockServer->set_open_handshake_timeout(10000);
         mWebSockServer->set_listen_backlog(8192 * 8);
         mWebSockServer->set_reuse_addr(true);
         mWebSockServer->set_pong_timeout(30000);

         std::error_code listenEc;
         int listenTryCnt = 0;
         do
         {
            listenEc = std::error_code();
            mWebSockServer->listen(boost::asio::ip::tcp::v4(), mHttpPort, listenEc);
            if (listenEc)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }
         } while (listenEc && listenTryCnt++ < 10);

         if (listenEc)
         {
            ErrLog(<< "Failed to start listening on port " << mHttpPort << "; error = " << listenEc.message());
         }

         // Start the server accept loop
         mWebSockServer->start_accept();

         if (startedCb != NULL)
         {
            PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
            pi->getSdkModuleThread().post(startedCb);
         }

         // Start the ASIO io_service run loop
         //mWebSockServer->run();

         std::vector<std::thread> tg;
         const int threadCount = 2;
         for (int i = 0; i < threadCount; i++) {
            tg.emplace_back(&websocketpp::server<websocketpp::config::asio>::run, mWebSockServer.get());
         }

         for (auto& threadItem : tg)
         {
            if (threadItem.joinable())
            {
               threadItem.join();
            }
         }

         std::cout << "PushToTalkServerWebSocket thread exit..." << std::endl;
      }
      catch (websocketpp::exception const & e)
      {
         std::cout << e.what() << std::endl;
      }
      catch (...)
      {
         std::cout << "other exception" << std::endl;
      }
   });
}

void PushToTalkServerWebSocket::StopServer()
{
   StackLog(<< "PushToTalkServerWebSocket::StopServer()");
   if (mServerThread != NULL)
   {
      websocketpp::lib::error_code ec;
      mWebSockServer->stop_listening(ec);
      mWebSockServer->stop_perpetual();
      mServerThread->join();
      delete mServerThread;
      mServerThread = NULL;
   }
}

int PushToTalkServerWebSocket::CloseConnection(const websocketpp::connection_hdl& hdl)
{
   StackLog(<< "PushToTalkServerWebSocket::CloseConnection()");
   std::error_code ec;
   mWebSockServer->close(hdl, websocketpp::close::status::going_away, "shutdown", ec);
   return 0;
}

void PushToTalkServerWebSocket::SetServerIncomingMessageCallback(const std::function<void(PushToTalkServerWebSocketSession*, const std::string&)>& cb)
{
   StackLog(<< "PushToTalkServerWebSocket::SetServerIncomingMessageCallback()");
   mServerIncomingMessageCb = cb;
}

void PushToTalkServerWebSocket::SetServerCloseConnectionCallback(const std::function<void(PushToTalkServerWebSocketSession*, const uint32_t&)>& cb)
{
   StackLog(<< "PushToTalkServerWebSocket::SetServerCloseConnectionCallback()");
   mServerCloseConnectionCb = cb;
}

int PushToTalkServerWebSocket::Send(websocketpp::connection_hdl hdl, const char* payload, unsigned int payloadLen)
{
   StackLog(<< "PushToTalkServerWebSocket::Send()");
   std::error_code ec;
   if (conn_ptr cn_ptr = mWebSockServer->get_con_from_hdl(hdl, ec))
   {
      //if (useOverflowProtection && cn_ptr->buffered_amount() > (16 * 1024))
      //{
      //   didOverflow = true;
      //   return -1;
      //}
      message_ptr msg_ptr = cn_ptr->get_message(websocketpp::frame::opcode::binary, payloadLen);
      msg_ptr->append_payload(payload, payloadLen);
      cn_ptr->send(msg_ptr);
      return 0;
   }
   else
   {
      WarningLog(<< "PushToTalkServerWebSocket::Send() failed to find connection_hdl");
   }
   return -1;
}

void PushToTalkServerWebSocket::onOpen(server* s, websocketpp::connection_hdl hdl)
{
   DebugLog(<< "PushToTalkServerWebSocket::onOpenImpl()");
   std::error_code ec;
   if (conn_ptr conn = mWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         PushToTalkServerWebSocketSession* session = new PushToTalkServerWebSocketSession(dynamic_cast<PhoneInterface*>(mPhone), mPttMgr, this, hdl);
         conn->set_close_handler(std::bind(&PushToTalkServerWebSocketSession::onClose, session, mWebSockServer.get(), std::placeholders::_1));
         conn->set_message_handler(std::bind(&PushToTalkServerWebSocketSession::onMessage, session, mWebSockServer.get(), std::placeholders::_1, std::placeholders::_2));
         std::lock_guard<std::mutex> lck(mMapLock);
         mSessions[hdl] = session;
      }
   }
}

std::string PushToTalkServerWebSocket::get_password() const
{
   StackLog(<< "PushToTalkServerWebSocket::get_password()");
   // TODO: should this be hard coded?
   return "zCQ/t):*d_N\\G*r=m3-p";
}

void PushToTalkServerWebSocket::RemoveSession(websocketpp::connection_hdl hdl)
{
   StackLog(<< "PushToTalkServerWebSocket::RemoveSession()");
   std::lock_guard<std::mutex> lck(mMapLock);
   mSessions.erase(hdl);
}

size_t PushToTalkServerWebSocket::GetSessionCount()
{
   StackLog(<< "PushToTalkServerWebSocket::RemoveSession()");
   std::lock_guard<std::mutex> lck(mMapLock);
   return mSessions.size();
}

PushToTalkServerWebSocketSessionHolder* PushToTalkServerWebSocket::GetSession(const PushToTalkServerWebSocketSessionId* sessId)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   const PushToTalkServerWebSocketSession::IdImpl* idImpl = dynamic_cast<const PushToTalkServerWebSocketSession::IdImpl*>(sessId);
   auto it = mSessions.find(idImpl->connection());
   if (it != mSessions.end())
   {
      std::error_code ec;
      return new PushToTalkServerWebSocketSessionHolder(it->second, mWebSockServer->get_con_from_hdl(it->second->websocket_conn_hdl(), ec));
   }
   return NULL;
}

}

}

#endif // CPCAPI2_BRAND_CALL_MODULE
