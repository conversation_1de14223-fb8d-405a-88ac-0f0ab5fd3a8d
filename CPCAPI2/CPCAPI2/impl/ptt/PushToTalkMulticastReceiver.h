#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MULTICAST_RECEIVER_H
#define CPCAPI2_PUSHTOTALK_MULTICAST_RECEIVER_H

#include "cpcapi2defs.h"

// boost asio
#include <boost/asio.hpp>


namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkMulticastReceiver
{

public:
   typedef std::function<void(const std::string&, unsigned int, const char*, std::size_t)> MulticastReceiverCbType;

   PushToTalkMulticastReceiver(
      boost::asio::io_context& io_context,
      const boost::asio::ip::address& listen_address,
      const boost::asio::ip::address& multicast_address,
      const int multicast_port,
      const MulticastReceiverCbType& cb);

   bool isRunning() const { return running_; }

   void stop() { running_ = false; }

private:

   void do_receive();

   boost::asio::ip::udp::socket socket_;
   boost::asio::ip::udp::endpoint sender_endpoint_;
   std::array<char, 1024> data_;
   MulticastReceiverCbType callback_;
   std::atomic_bool running_;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MULTICAST_RECEIVER_H
