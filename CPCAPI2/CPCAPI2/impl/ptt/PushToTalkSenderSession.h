#pragma once

#ifndef CPCAPI2_PUSHTOTALK_SENDER_SESSION_IMPL_H
#define CPCAPI2_PUSHTOTALK_SENDER_SESSION_IMPL_H

#include "cpcapi2defs.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSession.h"


namespace CPCAPI2
{

namespace PushToTalk
{

class PttSenderIdleState;
class PttSenderInitiatedState;
class PttSenderInitialActiveState;
class PttSenderActiveState;
class PttSenderTalkingState;
class PttSenderEndingState;
class PttSenderSessionState;

class PttSenderSessionFactory : public PttSessionFactory
{

public:

   PttSenderSessionFactory(PushToTalkManagerImpl* manager);
   virtual~ PttSenderSessionFactory();

   virtual PttSession* create(PushToTalkSessionHandle ptt) OVERRIDE;

private:

   PttSenderSessionFactory();

};

class PttSenderSessionStateFactory : public PttSessionStateFactory
{

public:

   PttSenderSessionStateFactory(PushToTalkManagerImpl* manager);
   virtual~ PttSenderSessionStateFactory();

   virtual void create() OVERRIDE;
   virtual PttSessionState* getState(PttSessionStateType type) OVERRIDE;

private:

   PttSenderSessionStateFactory();

   PttSenderSessionState* create(PttSessionStateType type);

   typedef std::map<PttSessionStateType, PttSenderSessionState*> PttSenderSessionStates;
   PttSenderSessionStates mStates;

};

class PttSenderSession : public PttSession
{

public:

   PttSenderSession(PushToTalkManagerImpl* manager, PttSession::PttSessionType sessionType, PushToTalkSessionHandle ptt);
   virtual ~ PttSenderSession();

   virtual PttSessionStateFactory* getFactory() OVERRIDE { return mFactory; }
   virtual cpc::string& getChannelId() OVERRIDE { return mChannelId; }
   virtual cpc::vector<CPCAPI2::PushToTalk::PttIdentity> getRecipients() OVERRIDE { return mRecipients; }
   virtual uint32_t getTransactionId() OVERRIDE { return mTransactionId; }
   virtual cpc::string& getSessionId() OVERRIDE { return mSessionId; }
   virtual void cancelTimers() OVERRIDE;

   virtual void handleEnd() = 0;

   virtual void sendPttInitiate() = 0;
   virtual void sendPttEnd() = 0;
   virtual uint32_t getPttSessionConnectedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionEndedCallCount() OVERRIDE;
   virtual uint32_t getPttSessionTotalCallCount() OVERRIDE;
   virtual bool doesCallExist(uint32_t call) OVERRIDE;
   virtual bool doesCallExist(resip::Tuple& endpoint) OVERRIDE;
   virtual PttSessionCall* getPttSessionCall(resip::Tuple& endpoint) OVERRIDE;
   virtual bool areAllPttCallsConnected() OVERRIDE;
   virtual bool areAllPttCallsDisconnected() OVERRIDE;

   // Associated to DeadlineTimerHandler
   virtual void onTimer(PttSession* session, unsigned short timerId, void* appState) OVERRIDE;

   virtual bool isPttSessionDeemedConnected() = 0;
   virtual bool isPttSessionDeemedDisconnected() = 0;

   typedef std::map<resip::Tuple, PttSessionCall*> PttSessionCalls;
   PttSessionCalls mCalls;
   PttSenderSessionStateFactory* mFactory;

   cpc::vector<CPCAPI2::PushToTalk::PttIdentity> mRecipients;
   cpc::string mChannelId;
   cpc::string mSessionId;
   uint32_t mTransactionId;
   bool mInitRecordingSuccessful;

   std::unique_ptr<webrtc_recon::MediaStackSettings> mMediaSettings;

   void resetSpurtTimer();
   void resetSessionTimer();
   void resetConnectedTimer();
   void resetConnectionErrorTimer();
   void resetDisconnectedTimer();
   void resetInitRetryTimer();
   void cancelSpurtTimer() { mSpurtTimer.cancel(); }
   void cancelSessionTimer() { mSessionTimer.cancel(); }
   void cancelConnectedTimer() { mConnectedTimer.cancel(); }
   void cancelConnectionErrorTimer() { mConnectionErrorTimer.cancel(); }
   void cancelDisconnectedTimer() { mDisconnectedTimer.cancel(); }
   void cancelInitRetryTimer() { mInitRetryTimer.cancel(); }

   resip::DeadlineTimer<resip::MultiReactor> mSpurtTimer; // how long to wait for talk-spurt after ptt session creation
   resip::DeadlineTimer<resip::MultiReactor> mSessionTimer; // how long to wait for another talk-spurt after after end-spurt, before ptt session is destroyed
   resip::DeadlineTimer<resip::MultiReactor> mConnectedTimer; // how long to wait before call is deemed connected regardless of responses
   resip::DeadlineTimer<resip::MultiReactor> mConnectionErrorTimer; // how long to wait for connection responses, before declaring an error
   resip::DeadlineTimer<resip::MultiReactor> mDisconnectedTimer; // how long to wait before call is deemed disconnected regardless of responses
   resip::DeadlineTimer<resip::MultiReactor> mInitRetryTimer; // how long to wait before resending init request

protected:

   PttSenderSession();

};

class PttSenderLanSession : public PttSenderSession
{

public:

   PttSenderLanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt);
   virtual ~PttSenderLanSession();

   virtual std::string getSessionType() OVERRIDE { return "lan sender"; }

   virtual uint32_t getPttSessionTotalCallCount() OVERRIDE;
   virtual bool isPttSessionDeemedConnected() OVERRIDE;
   virtual bool isPttSessionDeemedDisconnected() OVERRIDE;
   virtual void enableIncomingMediaInactivityMonitor() OVERRIDE;
   virtual void disableIncomingMediaInactivityMonitor() OVERRIDE;
   virtual void handleEnd() OVERRIDE;
   virtual void sendPttInitiate() OVERRIDE;
   virtual void sendPttEnd() OVERRIDE;

   void handleClientOfferEvent(const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple);
   void handleCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args);
   void handlePttReceiverEnded(const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple);
   void handleDiscoveryListUpdate(const PttDiscoveryListUpdate& args);
   void sendPttInitiateToTransport(bool resetTid = true);

   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   PeerConnection::PeerConnectionHandle mConnection;
   bool mDeemedConnected;

protected:

   PttSenderLanSession();
   PttInitiateEvent mInitiateEvent;

};

class PttSenderWanSession : public PttSenderSession
{

public:

   PttSenderWanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt);
   virtual ~PttSenderWanSession();

   virtual std::string getSessionType() OVERRIDE { return "wan sender"; }

   virtual bool isPttSessionDeemedConnected() OVERRIDE;
   virtual bool isPttSessionDeemedDisconnected() OVERRIDE;
   virtual void handleEnd() OVERRIDE;
   virtual void sendPttInitiate() OVERRIDE;
   virtual void sendPttEnd() OVERRIDE;

   void updateCloudParticipant(const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& evt);
   void handleCloudSessionConnected(const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt);

   CPCAPI2::ConferenceConnector::ConferenceConnectorHandle mCloudConnector;
   CPCAPI2::ConferenceConnector::CloudConferenceHandle mCloudConference;
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle mCloudSession;
   CPCAPI2::ConferenceConnector::CloudConferenceParticipantHandle mCloudParticipant;
   CPCAPI2::ConferenceConnector::SessionStatus mCloudSessionStatus;
   CPCAPI2::PeerConnection::PeerConnectionHandle mCloudPeerConnection;
   cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo> mCloudParticipantList; // Current list
   std::set<ConferenceConnector::CloudConferenceParticipantHandle> mCloudParticipantHistory;

protected:

   PttSenderWanSession();

};

class PttSenderSessionState : public PttSessionState
{

public:

   PttSenderSessionState(PushToTalkManagerImpl* manager, PttSessionStateType state);
   virtual~ PttSenderSessionState();

   virtual int end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onPttReceiverEndedEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args) OVERRIDE;
   virtual int onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args) OVERRIDE;

   virtual void onSpurtTimeout(PttSession* session);
   virtual void onExpiryTimeout(PttSession* session);
   virtual void onConnectedTimeout(PttSession* session);
   virtual void onConnectionErrorTimeout(PttSession* session);
   virtual void onDisconnectedTimeout(PttSession* session);
   virtual void onInitRetryTimeout(PttSession* session);

   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;

protected:

   PttSenderSessionState();

};

class PttSenderIdleState : public PttSenderSessionState
{

public:

   PttSenderIdleState(PushToTalkManagerImpl* manager);
   virtual~ PttSenderIdleState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;
   
   virtual int addRecipient(PttSession* session, PushToTalkSessionHandle ptt, const PttIdentity& identity) OVERRIDE;
   virtual int setChannel(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& channel) OVERRIDE;
   virtual int start(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;

private:

   PttSenderIdleState();

};

class PttSenderInitiatedState : public PttSenderSessionState
{

public:

   PttSenderInitiatedState(PushToTalkManagerImpl* manager);
   virtual~ PttSenderInitiatedState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual void onConnectedTimeout(PttSession* session) OVERRIDE;
   virtual void onConnectionErrorTimeout(PttSession* session) OVERRIDE;
   virtual int onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onCloudSessionConnected(PttSession* session, PushToTalkSessionHandle ptt, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& evt) OVERRIDE;
   virtual int onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onConferenceParticipantListUpdated(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args) OVERRIDE;
   virtual void onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args) OVERRIDE;
   virtual void onPttSenderInitiateRecordingEvent(PttSession* session, const PttSenderInitiateRecordingEvent& args) OVERRIDE;

private:

   PttSenderInitiatedState();

};

class PttSenderInitialActiveState : public PttSenderSessionState
{

public:

   PttSenderInitialActiveState(PushToTalkManagerImpl* manager);
   virtual ~PttSenderInitialActiveState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   
   virtual void onSpurtTimeout(PttSession* session) OVERRIDE;

private:

   PttSenderInitialActiveState();

};

class PttSenderActiveState : public PttSenderSessionState
{

public:

   PttSenderActiveState(PushToTalkManagerImpl* manager);
   virtual ~PttSenderActiveState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int startTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual void onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args) OVERRIDE;

private:

   PttSenderActiveState();

};

class PttSenderTalkingState : public PttSenderSessionState
{

public:

   PttSenderTalkingState(PushToTalkManagerImpl* manager);
   virtual~ PttSenderTalkingState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual int endTalkSpurt(PttSession* session, PushToTalkSessionHandle ptt) OVERRIDE;
   virtual int onClientOfferEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple) OVERRIDE;
   virtual int onCreateAnswerResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual void onDiscoveryListUpdate(PttSession* session, const PttDiscoveryListUpdate& args) OVERRIDE;

private:

   PttSenderTalkingState();

};

class PttSenderEndingState : public PttSenderSessionState
{

public:

   PttSenderEndingState(PushToTalkManagerImpl* manager);
   virtual~ PttSenderEndingState();
   virtual void onEntry(PttSession* session) OVERRIDE;
   virtual void onExit(PttSession* session) OVERRIDE;

   virtual void onDisconnectedTimeout(PttSession* session) OVERRIDE;

private:

   PttSenderEndingState();

};

}

}

#endif // CPCAPI2_PUSHTOTALK_SENDER_SESSION_IMPL_H
