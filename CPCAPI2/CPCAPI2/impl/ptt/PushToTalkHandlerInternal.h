#pragma once

#ifndef CPCAPI2_PUSHTOTALK_HANDLER_INTERNAL_H
#define CPCAPI2_PUSHTOTALK_HANDLER_INTERNAL_H

#include <ptt/PushToTalkTypes.h>
#include <ptt/PushToTalkHandler.h>
#include "PushToTalkTypesInternal.h"

namespace CPCAPI2
{

namespace PushToTalk
{
   enum PttStatisticsEventType
   {
      PttStatisticsEventType_UnicastDiscard
   };

   struct PttDiscoveryListUpdate {};
   struct PttStatisticsEvent
   {
      PttStatisticsEventType statisticsEventType;
      PttIdentity callerIdentity;
      cpc::string channelId;

      PttStatisticsEvent() : statisticsEventType(PttStatisticsEventType_UnicastDiscard), channelId("") {}
   };

   enum PttRestartReasonType
   {
      PttRestartReasonType_NetworkChange,
      PttRestartReasonType_NetworkFailure
   };

   struct PttServiceRestartedEvent
   {
      PttRestartReasonType reason;

      PttServiceRestartedEvent() : reason(PttRestartReasonType_NetworkChange) {}
      PttServiceRestartedEvent(PttRestartReasonType reason_) : reason(reason_) {}
   };

   struct PttMediaStatistics
   {
      unsigned int connectionId;
      unsigned int mediaStreamId;
      int rtpPacketCount;
      int rtcpPacketCount;
   };

   struct PttMediaStatisticsEvent
   {
      cpc::vector<PttMediaStatistics> mediaStreamStats;
   };

   class PushToTalkHandlerAdaptor : public PushToTalkHandler
   {

   public:

      DEPRECATED virtual int onPttServiceStarted(PushToTalkServiceHandle service, const PttServiceStartedEvent& args) { return 0; }
      virtual int onPttServiceStatusChanged(PushToTalkServiceHandle service, const PttServiceStatusChangedEvent& args) { return 0; }
      virtual int onPttEndpointList(PushToTalkServiceHandle service, const PttEndpointListEvent& args) { return 0; }
      virtual int onPttServiceConfigured(PushToTalkServiceHandle service, const PttServiceConfiguredEvent& args) { return 0; }
      virtual int onPttSessionStateChanged(PushToTalkSessionHandle ptt, const PttSessionStateChangedEvent& args) { return 0; }
      virtual int onPttIncomingCall(PushToTalkSessionHandle ptt, const PttIncomingCallEvent& args) { return 0; }
      virtual int onPttSessionError(PushToTalkSessionHandle ptt, const PttSessionErrorEvent& args) { return 0; }
      virtual int onPttReceiverDisconnected(PushToTalkSessionHandle ptt, const PttReceiverDisconnectedEvent& args) { return 0; }
   };

   /**
    * Private interface for internal methods on the SDK Observers.
   */
   class PushToTalkHandlerInternal : public PushToTalkHandlerAdaptor
   {

   public:

#ifdef CPCAPI2_AUTO_TEST
      virtual int onPttStatistics(PushToTalkServiceHandle service, const PttStatisticsEvent& args) { return kSuccess; };
      virtual int onPttQueryEndpointsRequestSent(PushToTalkServiceHandle service, const PttQueryEndpointsRequest& args) { return kSuccess; }
      virtual int onPttQueryEndpointsResponseSent(PushToTalkServiceHandle service, const PttQueryEndpointsResponse& args) { return kSuccess; }
      virtual int onPttQueryEndpointsReportSent(PushToTalkServiceHandle service, const PttReportEndpointsEvent& args) { return kSuccess; }
      virtual int onPttQueryEndpointsRequestReceived(PushToTalkServiceHandle service, const PttQueryEndpointsRequest& args) { return kSuccess; }
      virtual int onPttQueryEndpointsResponseReceived(PushToTalkServiceHandle service, const PttQueryEndpointsResponse& args) { return kSuccess; }
      virtual int onPttQueryEndpointsReportReceived(PushToTalkServiceHandle service, const PttReportEndpointsEvent& args) { return kSuccess; }
      virtual int onPttServiceRestarted(PushToTalkServiceHandle service, const PttServiceRestartedEvent& args) { return kSuccess; }
      virtual int onPttParticipantListUpdate(PushToTalkSessionHandle session, const PttParticipantListUpdateEvent& args) { return kSuccess; }
      virtual int onPttClientOfferEvent(PushToTalkSessionHandle session, const PttClientOfferEvent& args) { return kSuccess; }
      virtual int onPttClientOfferUpdateEvent(PushToTalkSessionHandle session, const PttClientOfferUpdateEvent& args) { return kSuccess; }
      virtual int onPttConferenceMediaStatusChangedEvent(PushToTalkSessionHandle session, const PttConferenceMediaStatusChangedEvent& args) { return kSuccess; }
      virtual int onPttMediaStatistics(PushToTalkSessionHandle session, const PttMediaStatisticsEvent& args) { return kSuccess; }
#endif

   };
}

}

#endif /* CPCAPI2_PUSHTOTALK_HANDLER_INTERNAL_H */
