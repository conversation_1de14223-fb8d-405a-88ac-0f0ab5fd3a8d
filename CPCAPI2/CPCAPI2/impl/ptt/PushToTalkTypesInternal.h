#pragma once

#ifndef CPCAPI2_PUSHTOTALKTYPESINTERNAL_H
#define CPCAPI2_PUSHTOTALKTYPESINTERNAL_H

#include "cpcapi2defs.h"
#include <ptt/PushToTalkTypes.h>
#include <ptt/PushToTalkHandler.h>


namespace CPCAPI2
{

namespace PushToTalk
{

struct PttInitiateEvent
{
   PushToTalkSessionHandle pttHandle = (PushToTalkSessionHandle)0;
   cpc::string sessionId;
   unsigned int transactionId = 0;
   cpc::string channelId;
   PttIdentity senderIdentity;
   cpc::vector<PttIdentity> receiverIdentity;
};

struct PttEndEvent
{
   PushToTalkSessionHandle pttHandle = (PushToTalkSessionHandle)0;
   cpc::string sessionId;
   unsigned int transactionId = 0;
   cpc::string channelId;
   cpc::string senderIpAddress;
   unsigned int senderIpPort = 0;
};

struct PttSenderInitiateRecordingEvent
{
   PushToTalkSessionHandle pttHandle = (PushToTalkSessionHandle)0;

   PttSenderInitiateRecordingEvent() : pttHandle(0) {}
};

struct PttReportEndpointsEvent
{
   unsigned int transactionId = 0;
   PttIdentity senderIdentity;
   cpc::vector<cpc::string> endpoints;
   cpc::string endpointsCB64;
};

struct PttQueryEndpointsRequest
{
   unsigned int transactionId = 0;
   PttIdentity senderIdentity;
};

struct PttQueryEndpointsResponse
{
   unsigned int transactionId = 0;
   DEPRECATED cpc::string endpointIpAddress;
   PttIdentity senderIdentity;
   cpc::vector<cpc::string> endpoints;
   cpc::string endpointsCB64;
};

struct PttClientOfferEvent
{
   PushToTalkSessionHandle pttHandle;
   cpc::string sessionId;
   cpc::string sessionDescription;

   PttClientOfferEvent() :
      pttHandle(0),
      sessionId(""),
      sessionDescription("") {}
};


struct PttConferenceMediaStatusChangedEvent
{
   enum MediaStatus
   {
      MediaStatus_None,
      MediaStatus_OfferSent,
      MediaStatus_AnswerReceived
   };

   PushToTalkSessionHandle pttHandle;
   uint32_t peerConnection;
   uint32_t cloudConf;
   uint32_t cloudConfSession;
   MediaStatus mediaStatus;

   PttConferenceMediaStatusChangedEvent() :
      pttHandle(0),
      peerConnection(-1),
      cloudConf(-1),
      cloudConfSession(-1),
      mediaStatus(MediaStatus_None) {}
};

struct PttClientOfferUpdateEvent
{
   PushToTalkSessionHandle pttHandle;
   uint32_t pc;
   cpc::string sessionId;
   cpc::string sessionDescription;
   cpc::string endpoint;

   PttClientOfferUpdateEvent() :
      pttHandle(0),
      pc(0),
      sessionId(""),
      sessionDescription(""),
      endpoint("") {}
};

struct PttReceiverEndedEvent
{
   PushToTalkSessionHandle pttHandle;
   cpc::string sessionId;
   unsigned int transactionId;
   PttIdentity receiverIdentity;
   cpc::string reason;

   PttReceiverEndedEvent() : pttHandle(0), sessionId(""), transactionId(0), reason("") {}
};

struct PttParticipantListUpdateEvent
{
   PttIdentity localIdentity;
   cpc::string channelId;
   cpc::vector<PttIdentity> participants; // Participants from the full participant list included in this query response
   unsigned int totalParticipantCount;    // Participant count of the full participant list
   unsigned int offset;                   // Where this list starts in the full participant list
};

}

}

#endif // CPCAPI2_PUSHTOTALKTYPESINTERNAL_H
