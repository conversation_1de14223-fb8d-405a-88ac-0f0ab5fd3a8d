#pragma once

#ifndef CPCAPI2_PUSHTOTALK_CLIENT_WEBSOCKET_H
#define CPCAPI2_PUSHTOTALK_CLIENT_WEBSOCKET_H

#include "cpcapi2defs.h"

// websocket server
#include <websocketpp/config/asio.hpp>
#include <websocketpp/server.hpp>

// websocket client
#include <websocketpp/client.hpp>

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>


namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkClientWebSocket
{

public:

   PushToTalkClientWebSocket(const std::function<void(const std::string&, const uint32_t&)>& cb, uint32_t connectionId);
   virtual ~PushToTalkClientWebSocket();

   void SetClientConnectionCallback(const std::function<void(PushToTalkClientWebSocket*, const uint32_t&)>& cb);
   void SetClientConnectionErrorCallback(const std::function<void(PushToTalkClientWebSocket*, const uint32_t&)>& cb);

   // JsonApiTransport
   virtual int send(const std::string& jsonData);
   virtual int connect(const std::string& serverUri);
   virtual int reconnect(const std::string& serverUri);
   virtual int shutdown();

private:

   typedef websocketpp::lib::thread thread_type;
   typedef websocketpp::lib::shared_ptr<thread_type> thread_ptr;
   typedef websocketpp::client<websocketpp::config::asio> client;
   typedef client::message_ptr message_ptr;

   void on_open(websocketpp::connection_hdl hdl);
   void on_close(websocketpp::connection_hdl hdl);
   void on_message(websocketpp::connection_hdl hdl, message_ptr msg);
   void on_fail(websocketpp::connection_hdl hdl);

   void sendImpl(const std::string& jsonData);
   void connectImpl(const std::string& serverUri);
   void shutdownImpl();

private:

   client mEndpoint;
   uint32_t mConnectionId;
   client::connection_ptr mConn;
   thread_ptr mThread;
   std::mutex mMutex;
   // std::condition_variable mCondConnected;
   // std::condition_variable mCondFailed;
   std::function<void(const std::string&, const uint32_t&)> mClientIncomingMessageCb;
   std::function<void(PushToTalkClientWebSocket*, const uint32_t&)> mClientConnectionCb;
   std::function<void(PushToTalkClientWebSocket*, const uint32_t&)> mClientConnectionErrorCb;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_CLIENT_WEBSOCKET_H
