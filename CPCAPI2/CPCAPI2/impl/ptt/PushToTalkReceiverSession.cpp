#include "brand_branded.h"

#if (CPCAPI2_BRAND_PTT_MODULE == 1)

#include "PushToTalkReceiverSession.h"
#include "PushToTalkManagerImpl.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkClientWebSocket.h"
#include "PushToTalkServerWebSocket.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkMulticastSender.h"
#include "PushToTalkMulticastReceiver.h"
#include "../call/SipAVConversationManagerInterface.h"
#include "../util/cpc_logger.h"
#include "../phone/NetworkChangeManagerImpl.h"
#include "../log/LocalLogger.h"
#include "media/AudioInterface.h"
#include "../account/SipAccountInterface.h"
#include "../media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "peerconnection/PeerConnectionManagerInterface.h"
#include "../util/IpHelpers.h"
#include <rutil/Random.hxx>
#include <MediaStackImpl.hxx>
#include "json/JsonHelper.h"
#include "json/JsonDataImpl.h"

#include <CodecFactoryImpl.hxx>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <webrtc/common_types.h>
// #ifdef CODECFACTORY_ENABLE_OPUS
#include <codecs/OpusCodecImpl.hxx>
// #endif

#define JSON_MODULE "PushToTalkJsonApi"

#ifdef ANDROID
#include <android/log.h>
#endif

// rapidjson
#include <stringbuffer.h>
#include <writer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PTT

#define PTT_RECEIVER_SESSION_LOG_ENABLED                                  1
#if (PTT_RECEIVER_SESSION_LOG_ENABLED == 1)
#define PttLog(args) StackLog(args)
#else
#define PttLog(args)
#endif

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::PeerConnection;
using resip::ReadCallbackBase;

namespace CPCAPI2
{

namespace PushToTalk
{

PttReceiverSessionFactory::PttReceiverSessionFactory(PushToTalkManagerImpl* manager) : PttSessionFactory(manager)
{
}

PttReceiverSessionFactory::~PttReceiverSessionFactory()
{
}

PttSession* PttReceiverSessionFactory::create(PushToTalkSessionHandle ptt)
{
   PttSession* session = NULL;
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();

   if (settings.serviceType == PttServiceType_WAN)
   {
      session = new PttReceiverWanSession(mManager, ptt);
   }
   else if (settings.serviceType == PttServiceType_LAN)
   {
      session = new PttReceiverLanSession(mManager, ptt);
   }

   return session;
}

PttReceiverSessionStateFactory::PttReceiverSessionStateFactory(PushToTalkManagerImpl* manager) : PttSessionStateFactory(manager)
{
}

PttReceiverSessionStateFactory::~PttReceiverSessionStateFactory()
{
   for (PttReceiverSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      delete (i->second);
   }
   mStates.clear();
}

void PttReceiverSessionStateFactory::create()
{
   if (!mManager) return;

   if (mStates.size() > 0)
   {
      ErrLog(<< "PttReceiverSessionStateFactory::create(): " << this << " manager: " << mManager << " state factory already initialized");
      return;
   }

   mStates[PttSessionState_Idle] = create(PttSessionState_Idle);
   mStates[PttSessionState_Initiated] = create(PttSessionState_Initiated);
   // mStates[PttSessionState_InitialActive] = create(PttSessionState_InitialActive);
   mStates[PttSessionState_Active] = create(PttSessionState_Active);
   mStates[PttSessionState_Talking] = create(PttSessionState_Talking);
   mStates[PttSessionState_Ending] = create(PttSessionState_Ending);

   for (PttReceiverSessionStates::iterator i = mStates.begin(); i != mStates.end(); ++i)
   {
      PttLog(<< "PttReceiverSessionStateFactory::create(): " << PttReceiverSessionState::getName(i->first) << " : " << i->first);
   }
}

PttReceiverSessionState* PttReceiverSessionStateFactory::create(PttSessionStateType type)
{
   PttReceiverSessionState* state = NULL;
   switch (type)
   {
      case PttSessionState_Idle: state = new PttReceiverIdleState(mManager); break;
      case PttSessionState_Initiated: state = new PttReceiverInitiatedState(mManager); break;
      // case PttSessionState_InitialActive: state = new PttReceiverInitialActiveState(mManager); break;
      case PttSessionState_Active: state = new PttReceiverActiveState(mManager); break;
      case PttSessionState_Talking: state = new PttReceiverTalkingState(mManager); break;
      case PttSessionState_Ending: state = new PttReceiverEndingState(mManager); break;
      default: break;
   }

   return state;
}

PttSessionState* PttReceiverSessionStateFactory::getState(PttSessionStateType type)
{
   if (!mManager || (mStates.size() == 0))
   {
      ErrLog(<< "PttReceiverSessionStateFactory::getState(): " << this << " manager: " << mManager << " state factory not initialized");
      return NULL;
   }
   return mStates[type];
}

PttReceiverSession::PttReceiverSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt, PttSessionType sessionType) :
PttSession(manager, ptt, sessionType),
mFactory(dynamic_cast<PttReceiverSessionStateFactory*>(manager->getReceiverStateFactory())),
mIncomingPtt(0),
mSetupTimer(manager->getReactor()),
mConnectionRetryTimer(manager->getReactor()),
mConnectedTimer(manager->getReactor()),
mWaitForAnswerTimer(manager->getReactor()),
mRejectedTimer(manager->getReactor())
{
}

PttReceiverSession::~PttReceiverSession()
{
}

// Associated to DeadlineTimerHandler

void PttReceiverSession::onTimer(PttSession* session, unsigned short timerId, void* appState)
{
   // StackLog(<< "PttReceiverSession::onTimer(): timerId: " << timerId);
   PttReceiverSessionState* state = static_cast<PttReceiverSessionState*>(getFactory()->getState(mState));
   if (!state) return;

   switch (timerId)
   {
      case PTT_SESSION_INCOMING_SETUP_TIMER_ID: state->onSetupTimeout(session); break;
      case PTT_SESSION_INCOMING_CONNECTION_RETRY_TIMER_ID: state->onConnectedTimeout(session); break;
      case PTT_SESSION_INCOMING_CONNECTED_TIMER_ID: state->onConnectionRetryTimeout(session); break;
      case PTT_SESSION_INCOMING_WAIT_FOR_ANSWER_TIMER_ID: state->onWaitForAnswerTimeout(session); break;
      case PTT_SESSION_INCOMING_REJECTED_TIMER_ID: state->onRejectedTimeout(session); break;
      default:
      {
         InfoLog(<< "PttReceiverSession::::onTimer(): " << this << " ptt: " << getPtt() << " timerId: " << timerId << " is invalid");
         assert(0);
      }
   }
}

void PttReceiverSession::cancelTimers()
{
   mSetupTimer.cancel();
   mConnectionRetryTimer.cancel();
   mConnectedTimer.cancel();
   mWaitForAnswerTimer.cancel();
   mRejectedTimer.cancel();
}

void PttReceiverSession::resetSetupTimer()
{
   mSetupTimer.cancel();
   mSetupTimer.expires_from_now(mManager->getInternalSettings().incomingSetupMsecs);
   mSetupTimer.async_wait(this, PTT_SESSION_INCOMING_SETUP_TIMER_ID, NULL);
}

void PttReceiverSession::resetConnectionRetryTimer()
{
   mConnectionRetryTimer.cancel();
   mConnectionRetryTimer.expires_from_now(mManager->getInternalSettings().incomingConnectionRetryMsecs);
   mConnectionRetryTimer.async_wait(this, PTT_SESSION_INCOMING_CONNECTION_RETRY_TIMER_ID, NULL);
}

void PttReceiverSession::resetConnectedTimer()
{
   mConnectedTimer.cancel();
   mConnectedTimer.expires_from_now(mManager->getInternalSettings().incomingConnectedMsecs);
   mConnectedTimer.async_wait(this, PTT_SESSION_INCOMING_CONNECTED_TIMER_ID, NULL);
}

void PttReceiverSession::resetWaitForAnswerTimer()
{
   mWaitForAnswerTimer.cancel();
   mWaitForAnswerTimer.expires_from_now(mManager->getInternalSettings().incomingWaitForAnswerMsecs);
   mWaitForAnswerTimer.async_wait(this, PTT_SESSION_INCOMING_WAIT_FOR_ANSWER_TIMER_ID, NULL);
}

void PttReceiverSession::resetRejectedTimer()
{
   mRejectedTimer.cancel();
   mRejectedTimer.expires_from_now(mManager->getInternalSettings().incomingRejectedMsecs);
   mRejectedTimer.async_wait(this, PTT_SESSION_INCOMING_REJECTED_TIMER_ID, NULL);
}

PttReceiverLanSession::PttReceiverLanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt) :
PttReceiverSession(manager, ptt, PttSession::PttSessionType_Lan),
mCall(NULL),
mPeerConnMgr(manager->getPeerManager()),
mMediaInactive(false)
{
}

PttReceiverLanSession::~PttReceiverLanSession()
{
   PeerConnectionManager* peerMgr = CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(mPhone);
   if (peerMgr && mCall)
   {
      peerMgr->close(mCall->handle);
      delete mCall;
      mCall = NULL;
   }

   if (!mMediaInactive)
   {
      // If call is being destroyed due to media inactivity, do not add to stale list, so that it can be re-instantiated if signalling is received
      mManager->updateStaleSessionList(mPtt);
   }

   CPCAPI2::Media::MediaManagerInterface* mediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(mManager->getPhone()));
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaIf->media_stack_ptr()->codecFactory());
   std::shared_ptr<webrtc_recon::OpusCodecImpl> audioCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", 48000));
   if (audioCodec)
   {
      audioCodec->enableDtx();
   }
}

uint32_t PttReceiverLanSession::getPttSessionConnectedCallCount()
{
   if (mCall && (mCall->state == PttSessionCallStateType_Connected)) return 1;
   return 0;
}

uint32_t PttReceiverLanSession::getPttSessionEndedCallCount()
{
   if (mCall && (mCall->state == PttSessionCallStateType_Ended)) return 1;
   return 0;
}

uint32_t PttReceiverLanSession::getPttSessionTotalCallCount()
{
   if (mCall) return 1;
   return 0;
}

bool PttReceiverLanSession::doesCallExist(uint32_t call)
{
   // if ((mCall && (mCall->handle == call)) || ((mCloudPeerConnection > 0) && (mCloudPeerConnection == call))) return true;
   if (mCall && (mCall->handle == call)) return true;
   return false;
}

bool PttReceiverLanSession::doesCallExist(resip::Tuple& endpoint)
{
   if (mCall && (mCall->endpoint == endpoint)) return true;
   return false;
}

PttSessionCall* PttReceiverLanSession::getPttSessionCall(resip::Tuple& endpoint)
{
   if (mCall && (mCall->endpoint == endpoint)) return mCall;
   return NULL;
}

bool PttReceiverLanSession::areAllPttCallsConnected()
{
   if (!mCall)
   {
      DebugLog(<< "PttReceiverLanSession::areAllPttSessionsConnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call not initialized, presume not connected");
      return false;
   }

   int callConnectedCount = getPttSessionConnectedCallCount();
   if (callConnectedCount == 0)
   {
      DebugLog(<< "PttReceiverLanSession::areAllPttSessionsConnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call is not connected");
      return false;
   }

   DebugLog(<< "PttReceiverLanSession::areAllPttSessionsConnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call is connected");

   return true;
}

bool PttReceiverLanSession::areAllPttCallsDisconnected()
{
   if (!mCall)
   {
      DebugLog(<< "PttReceiverLanSession::areAllPttCallsDisconnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call not initialized, presume disconnected");
      return true;
   }

   int callEndedCount = getPttSessionEndedCallCount();
   if (callEndedCount == 0)
   {
      DebugLog(<< "PttReceiverLanSession::areAllPttCallsDisconnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call is not disconnected");
      return false;
   }

   DebugLog(<< "PttReceiverLanSession::areAllPttCallsDisconnected(): " << this << " ptt: " << getPtt() << " service: " << mService << " call is disconnected");

   return true;
}

void PttReceiverLanSession::handleAccept()
{
   InfoLog(<< "PttReceiverLanSession::handleAccept(): " << this << " ptt: " << getPtt() << " service: " << mManager->getService());
   if (mCall)
   {
      InfoLog(<< "PttReceiverLanSession::handleAccept(): " << this << " ptt: " << getPtt() << " service: " << mManager->getService() << " ignoring request to accept session as there is already an existing call");
      return;
   }

   cancelSetupTimer();

   PttIdentity senderIdentity = mInitiateEvent.senderIdentity;
   if (senderIdentity.ipAddress.empty()) senderIdentity.ipAddress = mRemoteEndpoint.presentationFormat().c_str();
   mManager->addToDiscoveryList(senderIdentity);

   PeerConnectionHandle connection = mPeerConnMgr->createPeerConnection();
   PeerReceiverCall* call = new PeerReceiverCall(connection, mRemoteEndpoint);
   mCall = call;

   call->ptt = getPtt();
   call->connectionRetriesLeft = mManager->getInternalSettings().connectionRetryMaximum;

   PeerConnectionSettings pcSettings;
   pcSettings.mixContribution = 0; // do NOT mix this stream with any others
   pcSettings.natTraversalMode = PeerConnectionSettings::NatTraversalMode_None;
   pcSettings.sessionName = "cpcptt";
   pcSettings.certAor = "<EMAIL>";
   pcSettings.secureMediaMode = PeerConnectionSettings::SecureMediaMode_None;
   pcSettings.secureMediaRequired = false;
   pcSettings.rtcpEnabled = false;
   pcSettings.mediaDscp = mManager->getServiceSettings().mediaDscp;

   mPeerConnMgr->setDefaultSettings(connection, pcSettings);
   PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(mPeerConnMgr);
   if (peerMgrInternal)
   {
      peerMgrInternal->setMediaInactivityTimeoutMsecs(connection, mManager->getServiceSettings().mediaInactivityIntervalSeconds * 1000);
   }

   MediaStreamHandle audioMediaStream = mPeerConnMgr->createMediaStream();
   CPCAPI2::PeerConnection::MediaInfo miAudio;
   miAudio.mediaDirection = PeerConnection::MediaDirection_RecvOnly;
   miAudio.mediaType = PeerConnection::MediaType_Audio;
   mManager->setCodecMediaInfo(miAudio);

   CPCAPI2::Media::MediaManagerInterface* mediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(mManager->getPhone()));
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaIf->media_stack_ptr()->codecFactory());
   std::shared_ptr<webrtc_recon::OpusCodecImpl> audioCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", 48000));
   if (audioCodec)
   {
      audioCodec->disableDtx();
   }

   mPeerConnMgr->configureMedia(connection, audioMediaStream, miAudio);
   mPeerConnMgr->createOffer(connection);

#ifdef CPCAPI2_AUTO_TEST
   if (mManager->getInternalSettings().mediaInactivityMonitorEnabled)
   {
      mPeerConnMgr->startMediaInactivityMonitor(connection);
   }
#else
   mPeerConnMgr->startMediaInactivityMonitor(connection);
#endif
   // peerMgrInternal->setLoggingMediaStatisticsFrequencyMsecs(connection, 3000);
   mPeerConnMgr->startLoggingMediaStatistics(connection);
}

void PttReceiverLanSession::handleEnd(PushToTalk::PttReceiverEndReasonType reason)
{
   StatLog(PTT_LOGID_SESSION_RX_LAN_END, << "PttReceiverLanSession::handleEnd(): " << this << " ptt: " << getPtt() << " service: " << mService << " state: " << PttSessionState::getName(mState) << " reason: " << getDebugString(reason));
   if (getPttSessionConnectedCallCount() > 0)
   {
      sendPttReceiverEnded(reason);
   }

   cancelTimers();
   endCall();
}

void PttReceiverLanSession::handleSessionMute()
{
}

void PttReceiverLanSession::handleSessionUnmute()
{
}

void PttReceiverLanSession::endCall()
{
   if (mCall)
   {
      InfoLog(<< "PttReceiverLanSession::endCall(): " << this << " ptt: " << getPtt() << " ending incoming peer call, remote-endpoint: " << mRemoteEndpoint);
      PeerConnection::PeerConnectionManagerInternal* peerMgrInternal = dynamic_cast<PeerConnection::PeerConnectionManagerInternal*>(mPeerConnMgr);
      if (peerMgrInternal)
      {
         peerMgrInternal->logMediaStatistics(mCall->handle);
      }
      mManager->getPeerManager()->close(mCall->handle);
      delete mCall;
      mCall = NULL;
   }
}

void PttReceiverLanSession::sendPttReceiverEnded(PushToTalk::PttReceiverEndReasonType reason)
{
   StatLog(PTT_LOGID_SESSION_RX_LAN_RECEIVER_ENDED, << "PttReceiverLanSession::sendPttReceiverEnded(): " << this << " ptt: " << getPtt() << " reason: " << getDebugString(reason) << " sending receiver ended, remote-endpoint: " << mRemoteEndpoint << " state: " << PttSessionState::getName(mState));
   PushToTalkServiceSettings& settings = mManager->getServiceSettings();
   PttReceiverEndedEvent recEndedEvt;
   recEndedEvt.pttHandle = getIncomingPtt();
   recEndedEvt.sessionId = getSessionId();
   recEndedEvt.transactionId = ::time(NULL);
   recEndedEvt.receiverIdentity = settings.localIdentities[0];
   recEndedEvt.reason = getReceiverEndReasonCode(reason).c_str();

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "PttReceiverEnded", true);
   serializer.addValue("PttReceiverEndedEvent", recEndedEvt);
   serializer.finalize();
   rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();
   mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), false, false, mRemoteEndpoint.presentationFormat().c_str(), false, mRemoteEndpoint.getPort());
}

void PttReceiverLanSession::enableIncomingMediaInactivityMonitor()
{
   InfoLog(<< "PttReceiverLanSession::enableIncomingMediaInactivityMonitor: " << this << " ptt: " << getPtt() << " service: " << mService << " remote-endpoint: " << mRemoteEndpoint << " connection: " << (mCall ? mCall->handle : -1));
   if (mCall)
   {
#ifdef CPCAPI2_AUTO_TEST
      if (mManager->getInternalSettings().mediaInactivityMonitorEnabled)
      {
         mPeerConnMgr->startMediaInactivityMonitor(mCall->handle);
      }
#else
      mPeerConnMgr->startMediaInactivityMonitor(mCall->handle);
#endif
   }
}

void PttReceiverLanSession::disableIncomingMediaInactivityMonitor()
{
   InfoLog(<< "PttReceiverLanSession::disableIncomingMediaInactivityMonitor: " << this << " ptt: " << getPtt() << " service: " << mService << " remote-endpoint: " << mRemoteEndpoint << " connection: " << (mCall ? mCall->handle : -1));
   if (mCall)
   {
#ifdef CPCAPI2_AUTO_TEST
      if (mManager->getInternalSettings().mediaInactivityMonitorEnabled)
      {
         mPeerConnMgr->stopMediaInactivityMonitor(mCall->handle);
      }
#else
      mPeerConnMgr->stopMediaInactivityMonitor(mCall->handle);
#endif
   }
}

PttReceiverWanSession::PttReceiverWanSession(PushToTalkManagerImpl* manager, PushToTalkSessionHandle ptt) :
PttReceiverSession(manager, ptt, PttSession::PttSessionType_Wan),
mCloudConference(0),
mCloudSession(0),
// TODO: mCloudParticipant(0),
mCloudPeerConnection(0),
mSenderDestroyedDueToOverride(false),
mRejected(false)
{
}

PttReceiverWanSession::~PttReceiverWanSession()
{
   mManager->updateStaleSessionList(mPtt);

   CPCAPI2::Media::MediaManagerInterface* mediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(mManager->getPhone()));
   std::shared_ptr<webrtc_recon::CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaIf->media_stack_ptr()->codecFactory());
   std::shared_ptr<webrtc_recon::OpusCodecImpl> audioCodec = std::dynamic_pointer_cast<webrtc_recon::OpusCodecImpl>(codecFactory->getAudioCodec("opus", 48000));
   if (audioCodec)
   {
      audioCodec->enableDtx();
   }
}

uint32_t PttReceiverWanSession::getPttSessionConnectedCallCount()
{
   return 1;
}

uint32_t PttReceiverWanSession::getPttSessionEndedCallCount()
{
   return 1;
}

uint32_t PttReceiverWanSession::getPttSessionTotalCallCount()
{
   return 1;
}

bool PttReceiverWanSession::doesCallExist(uint32_t call)
{
   return false;
}

bool PttReceiverWanSession::doesCallExist(resip::Tuple& endpoint)
{
   return false;
}

PttSessionCall* PttReceiverWanSession::getPttSessionCall(resip::Tuple& endpoint)
{
   return NULL;
}

bool PttReceiverWanSession::areAllPttCallsConnected()
{
   return true;
}

bool PttReceiverWanSession::areAllPttCallsDisconnected()
{
   return true;
}

void PttReceiverWanSession::handleEnd(PushToTalk::PttReceiverEndReasonType reason)
{
   StatLog(PTT_LOGID_SESSION_RX_WAN_ENDED, << "PttReceiverWanSession::handleEnd(): " << this << " ptt: " << getPtt() << " service: " << mService << " state: " << PttSessionState::getName(mState) << " reason: " << getDebugString(reason));
   mManager->addToStaleSessionList(mCloudConference, mCloudConferenceOwner);
   mManager->endConfSession(mCloudSession);
   cancelTimers();
}

void PttReceiverWanSession::handleAccept()
{
   InfoLog(<< "PttReceiverWanSession::handleAccept(): " << this << " ptt: " << getPtt() << " service: " << mManager->getService());
   if (mCloudSession)
   {
      InfoLog(<< "PttReceiverWanSession::handleAccept(): " << this << " ptt: " << getPtt() << " service: " << mManager->getService() << " ignoring request to accept session as there is already an existing cloud session");
      return;
   }
   cancelSetupTimer();
   // mCloudSession = mManager->startConfSessionWithMyConference(getPtt(), mCloudConference);
   mCloudSession = mManager->startConfSessionWithMyConference(mCloudConference);
}

void PttReceiverWanSession::handleSessionMute()
{
   mManager->doConferenceSessionMute(mCloudSession);
}

void PttReceiverWanSession::handleSessionUnmute()
{
   mManager->doConferenceSessionUnmute(mCloudSession);
}

/*
void PttReceiverWanSession::endWithoutDelay()
{
   InfoLog(<< "PttReceiverWanSession::endWithoutDelay(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " state: " << PttSessionState::getName(mState));
   mManager->endConfSession(mCloudSession);
   cancelTimers();
}
*/

PttReceiverSessionState::PttReceiverSessionState(PushToTalkManagerImpl* manager, PttSessionStateType type) :
PttSessionState(manager, type),
mPeerConnMgr(manager->getPeerManager())
{
}

PttReceiverSessionState::~PttReceiverSessionState()
{
}

int PttReceiverSessionState::end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason)
{
   InfoLog(<< "PttReceiverSessionState::end(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " state: " << PttSessionState::getName(mState) << " reason: " << reason);
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleEnd(getReceiverEndReasonCode(reason.c_str()));

   if (session->getType() == PttReceiverLanSession::PttSessionType_Wan)
   {
      if (reason == PTT_SESSION_END_REASON_REJECT)
      {
         PttReceiverWanSession* receiverWanSession = dynamic_cast<PttReceiverWanSession*>(session);
         receiverWanSession->mRejected = true;
         session->changeState(PttSessionState_Ending, PttReceiverEndingState::PttReceiverEndingSubState_Rejected);
         return kSuccess;
      }
   }
   else
   {
      if (reason == PTT_SESSION_END_REASON_MEDIA_INACTIVITY)
      {
         PttReceiverLanSession* receiverLanSession = dynamic_cast<PttReceiverLanSession*>(session);
         receiverLanSession->mMediaInactive = true;
      }
   }

   // Ignore ending delay handling for LAN ptt calls unless deemed applicable for LAN as well
   session->changeState(PttSessionState_Idle);
   return kSuccess;
}

int PttReceiverSessionState::endWithoutDelay(PttSession* session, PushToTalkSessionHandle ptt, PushToTalk::PttReceiverEndReasonType reason)
{
   InfoLog(<< "PttReceiverSessionState::endWithoutDelay(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " reason: " << getDebugString(reason) << " state: " << PttSessionState::getName(mState));
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleEnd(reason);
   session->changeState(PttSessionState_Idle);
   return kSuccess;
}

int PttReceiverSessionState::onPttEndEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttEndEvent& evt, const resip::Tuple& incomingTuple)
{
   StatLog(PTT_LOGID_SESSION_RX_LAN_END_RECEIVED, << "PttReceiverSessionState::onPttEndEvent(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " incoming ptt: " << evt.pttHandle << " state: " << PttSessionState::getName(mState));
   PttReceiverLanSession* receiverSession = dynamic_cast<PttReceiverLanSession*>(session);
   if (receiverSession)
   {
      receiverSession->cancelTimers();
      receiverSession->endCall();
      session->changeState(PttSessionState_Idle);
   }
   return kSuccess;
}

int PttReceiverSessionState::onMediaInactivity(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args)
{
   session->logWifiStatus();
   PttReceiverLanSession* lanSession = dynamic_cast<PttReceiverLanSession*>(session);
   PttReceiverWanSession* wanSession = dynamic_cast<PttReceiverWanSession*>(session);

   StatLog(PTT_LOGID_SESSION_RX_MEDIA_INACTIVITY, << "PttReceiverSessionState::onMediaInactivity: pc: " << pc << " service: " << mService << " ptt: " << session->getPtt() << " state: " << session->getName());
   if ((lanSession && (!lanSession->mCall)) || (wanSession && (wanSession->mCloudPeerConnection != pc)))
   {
      DebugLog(<< "PttReceiverSessionState::onMediaInactivity(): " << this << " no existing calls in session, service: " << mService << " ptt: " << session->getPtt() << " pc: " << pc);
      return kError;
   }

   end(session, session->getPtt(), PTT_SESSION_END_REASON_MEDIA_INACTIVITY);
   return kSuccess;
}

int PttReceiverSessionState::onCloudSessionEnded(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference)
{
   InfoLog(<< "PttReceiverSessionState::onCloudSessionEnded(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " conference: " << conference << " state: " << PttSessionState::getName(mState));
   return endWithoutDelay(session, ptt);
}

int PttReceiverSessionState::onConferenceSessionMediaStatusChanged(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
#ifdef CPCAPI2_AUTO_TEST
   InfoLog(<< "PttReceiverSessionState::onConferenceSessionMediaStatusChanged(): " << this << " service: " << mService << " ptt: " << ptt << " conference: " << connector << " event: " << args);
   if (session->getType() == PttSession::PttSessionType_Wan)
   {
      PttConferenceMediaStatusChangedEvent statEvt;
      statEvt.pttHandle = session->getPtt();
      statEvt.peerConnection = args.peerConnection;
      statEvt.cloudConf = args.conference;
      statEvt.cloudConfSession = args.session;
      statEvt.mediaStatus = (PttConferenceMediaStatusChangedEvent::MediaStatus)args.mediaStatus;

      mManager->getPttManager()->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttConferenceMediaStatusChangedEvent), mManager->getService(), ptt, statEvt);
   }
#endif
   return kSuccess;
}

void PttReceiverSessionState::onSetupTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverSessionState::onSetupTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in current state: " << getName());
}

void PttReceiverSessionState::onConnectedTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverSessionState::onConnectedTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in current state: " << getName());
}

void PttReceiverSessionState::onConnectionRetryTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverSessionState::onConnectionRetryTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in current state: " << getName());
}

void PttReceiverSessionState::onWaitForAnswerTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverSessionState::onWaitForAnswerTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in current state: " << getName());
}

void PttReceiverSessionState::onRejectedTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverSessionState::onRejectedTimeout(): ptt: " << session->getPtt() << " ignoring timeout as it is not handled in " << session->getName() << " session in current state: " << getName());
}

PttReceiverIdleState::PttReceiverIdleState(PushToTalkManagerImpl* manager) :
PttReceiverSessionState(manager, PttSessionStateType::PttSessionState_Idle)
{
}

PttReceiverIdleState::~PttReceiverIdleState()
{
}

void PttReceiverIdleState::onEntry(PttSession* session)
{
   PttLog(<< "PttReceiverIdleState:onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str() << " remote-endpoint: " << session->getRemoteEndpoint());
   session->disableWifiStatusMonitor();
}

void PttReceiverIdleState::onExit(PttSession* session)
{
   PttLog(<< "PttReceiverIdleState:onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   session->enableWifiStatusMonitor();
}

int PttReceiverIdleState::onPttInitiateEvent(PttSession* session, PushToTalkSessionHandle ptt, const PttInitiateEvent& evt, const resip::Tuple& incomingTuple)
{
   StackLog(<< "PttReceiverIdleState::onPttInitiateEvent(): ptt: " << session->getPtt() << " incoming-ptt: " << evt.pttHandle << " endpoint: " << incomingTuple);
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      PttReceiverLanSession* receiverSession = static_cast<PttReceiverLanSession*>(session);
      receiverSession->mIncomingPtt = evt.pttHandle;
      receiverSession->mInitiateEvent = evt;
      receiverSession->mRemoteEndpoint = incomingTuple;

      // wait for app to accept or reject incoming ptt or for timeout
      PttIncomingCallEvent incomingEvt;
      incomingEvt.currentState = PttSessionState_Idle;
      incomingEvt.callerIdentity = evt.senderIdentity;
      incomingEvt.channelId = evt.channelId.c_str();
      incomingEvt.service = mService;
      incomingEvt.incomingPtt = evt.pttHandle;

      mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttIncomingCall), mService, ptt, incomingEvt);
      session->changeState(PttSessionState_Initiated);
   }

   return kSuccess;
}

int PttReceiverIdleState::onCloudNewParticipantInMyConference(PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& partInfo, const cpc::string& channelId)
{
   if (session->getType() == PttSession::PttSessionType_Wan)
   {
      PttReceiverWanSession* receiverSession = static_cast<PttReceiverWanSession*>(session);
      receiverSession->mIncomingPtt = ptt;
      receiverSession->mCloudConference = conference;
      receiverSession->mCloudConferenceOwner = partInfo;

      // wait for app to accept or reject incoming ptt or for timeout
      PttIncomingCallEvent incomingEvt;
      incomingEvt.currentState = PttSessionState_Idle;
      //incomingEvt.callerIdentity = evt.senderIdentity;
      incomingEvt.callerIdentity.displayName = partInfo.displayName;
      incomingEvt.callerIdentity.userName = partInfo.address;
      for (int itag : partInfo.tags)
      {
         if (itag == 1)
         {
            incomingEvt.callerIdentity.identityType = PttIdentityType_SIP;
         }
         else if (itag == 2)
         {
            incomingEvt.callerIdentity.identityType = PttIdentityType_XMPP;
         }
      }
      incomingEvt.channelId = channelId;
      incomingEvt.service = mService;
      incomingEvt.incomingPtt = ptt;

      PttInitiateEvent initEvt;
      initEvt.pttHandle = ptt;
      initEvt.channelId = channelId;
      initEvt.senderIdentity = incomingEvt.callerIdentity;
      receiverSession->mInitiateEvent = initEvt;

      mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttIncomingCall), mService, ptt, incomingEvt);
      session->changeState(PttSessionState_Initiated);
   }

   return kSuccess;
}

PttReceiverInitiatedState::PttReceiverInitiatedState(PushToTalkManagerImpl* manager) :
PttReceiverSessionState(manager, PttSessionStateType::PttSessionState_Initiated)
{
}

PttReceiverInitiatedState::~PttReceiverInitiatedState()
{
}

void PttReceiverInitiatedState::onEntry(PttSession* session)
{
   PttLog(<< "PttReceiverInitiatedState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   // wait for onClientConnection
   static_cast<PttReceiverSession*>(session)->resetSetupTimer();
}

void PttReceiverInitiatedState::onExit(PttSession* session)
{
   PttLog(<< "PttReceiverInitiatedState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttReceiverSession*>(session)->cancelSetupTimer();
}

int PttReceiverInitiatedState::accept(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverInitiatedState::accept(): " << this << " service: " << mManager->getService() << " ptt: " << session->getPtt());
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleAccept();
   session->changeState(PttSessionState_Active);

   return kSuccess;
}

int PttReceiverInitiatedState::reject(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverInitiatedState::reject(): " << this << " service: " << mService << " ptt: " << session->getPtt());
   if (session->getType() == PttSession::PttSessionType_Wan)
   {
      PttReceiverWanSession* receiverSession = static_cast<PttReceiverWanSession*>(session);
      mManager->addToStaleSessionList(receiverSession->mCloudConference, receiverSession->mCloudConferenceOwner);
   }
   end(session, session->getPtt(), PTT_SESSION_END_REASON_REJECT);
   return kSuccess;
}

void PttReceiverInitiatedState::onSetupTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverInitiatedState::onSetupTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " destroying ptt session due to setup timeout in session state: " << getName());
   PttSessionErrorEvent evt;
   evt.service = mService;
   evt.errorCode = PttSessionError_SetupTimeout;
   mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, session->getPtt(), evt);
   endWithoutDelay(session, session->getPtt(), PushToTalk::PttReceiverEndReasonType_SetupTimeout);
}

PttReceiverActiveState::PttReceiverActiveState(PushToTalkManagerImpl* manager) :
PttReceiverSessionState(manager, PttSessionStateType::PttSessionState_Active)
{
}

PttReceiverActiveState::~PttReceiverActiveState()
{
}

void PttReceiverActiveState::onEntry(PttSession* session)
{
   PttLog(<< "PttReceiverActiveState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   // wait for PeerConnectionHandler::onCreateOfferResult(..)
   static_cast<PttReceiverSession*>(session)->resetWaitForAnswerTimer();
}

void PttReceiverActiveState::onExit(PttSession* session)
{
   PttLog(<< "PttReceiverActiveState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttReceiverSession*>(session)->cancelWaitForAnswerTimer();
}

int PttReceiverActiveState::mute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverActiveState::mute(): " << this << " service: " << mService << " ptt: " << session->getPtt());
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleSessionMute();
   return kSuccess;
}

int PttReceiverActiveState::unmute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverActiveState::unmute(): " << this << " service: " << mService << " ptt: " << session->getPtt());
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleSessionUnmute();
   return kSuccess;
}

int PttReceiverActiveState::onCreateOfferResult(PttSession* session, CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args)
{
   // TODO: Should have seperate timer for create-offer result
   if (session->getType() == PttSession::PttSessionType_Lan)
   {
      PttReceiverLanSession* receiverSession = static_cast<PttReceiverLanSession*>(session);
      if (!receiverSession->mCall)
      {
         InfoLog(<< "PttReceiverActiveState::onCreateOfferResult(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " pc: " << pc << " no existing calls in session");
         return kError;
      }

      InfoLog(<< "PttReceiverActiveState::onCreateOfferResult(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " pc: " << pc << " incoming-ptt: " << receiverSession->mIncomingPtt);

      PeerReceiverCall* call = receiverSession->mCall;
      call->state = PttSessionCallStateType_Setup;
      call->connectionState = SignalingState_HaveLocalOffer;
      mPeerConnMgr->setLocalDescription(pc, args.sdp);

      resip::Data sendSdpData(args.sdp.sdpString.c_str(), args.sdp.sdpLen);
      resip::ParseBuffer sendSdpPb(sendSdpData);
      resip::SdpContents sendSdp;
      sendSdp.parse(sendSdpPb);
      resip::SdpContents::Session::MediumContainer& mc = sendSdp.session().media();
      for (resip::SdpContents::Session::Medium& m : mc)
      {
         m.clearAttribute("mid");
      }
      sendSdpData = resip::Data::from(sendSdp);
      sendSdpData.replace("; maxaveragebitrate=64000", "", 1);

      PttClientOfferEvent clientOffer;
      clientOffer.sessionDescription = cpc::string(sendSdpData.c_str(), sendSdpData.size());
      clientOffer.pttHandle = receiverSession->mIncomingPtt;
      clientOffer.sessionId = receiverSession->getSessionId().c_str();

      CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
      CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onClientOffer", true);
      serializer.addValue("ClientOfferEvent", clientOffer);
      serializer.finalize();
      rapidjson::StringBuffer& buffer = ((Json::JsonDataImpl*)json.get())->getStringBuffer();

      mManager->sendToWire(std::string(buffer.GetString(), buffer.GetSize()), false, false, call->endpoint.presentationFormat().c_str(), false, call->endpoint.getPort());

      call->state = PttSessionCallStateType_Connected;
      session->changeState(PttSessionState_Talking);
   }
   return kSuccess;
}

int PttReceiverActiveState::onConferenceSessionMediaStatusChanged(
   PttSession* session, PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::ConferenceConnectorHandle connector,
   const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args)
{
   InfoLog(<< "PttReceiverActiveState::onConferenceSessionMediaStatusChanged(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " conference: " << connector << " event: " << args);
   if (session->getType() == PttSession::PttSessionType_Wan)
   {
#ifdef CPCAPI2_AUTO_TEST
      PttConferenceMediaStatusChangedEvent statEvt;
      statEvt.pttHandle = session->getPtt();
      statEvt.peerConnection = args.peerConnection;
      statEvt.cloudConf = args.conference;
      statEvt.cloudConfSession = args.session;
      statEvt.mediaStatus = (PttConferenceMediaStatusChangedEvent::MediaStatus)args.mediaStatus;
      mManager->getPttManager()->firePttInternalSessionEvent(cpcFunc(PushToTalkHandlerInternal::onPttConferenceMediaStatusChangedEvent), mManager->getService(), ptt, statEvt);
#endif

      // if ((args.mediaStatus == CPCAPI2::ConferenceConnector::SessionMediaStatus_OfferSent)
      if (args.mediaStatus == CPCAPI2::ConferenceConnector::SessionMediaStatus_AnswerReceived)
      {
         DebugLog(<< "PttReceiverActiveState::onConferenceSessionMediaStatusChanged(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " conference: " << connector << " media parameters exchanged");
         session->changeState(PttSessionState_Talking);
      }
   }
   return kSuccess;
}

void PttReceiverActiveState::onWaitForAnswerTimeout(PttSession* session)
{
   StackLog(<< "PttReceiverActiveState::onWaitForAnswerTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " destroying ptt session due to wait for answer timeout in session state: " << getName());
   PttSessionErrorEvent evt;
   evt.service = mService;
   evt.errorCode = PttSessionError_OfferResponseTimeout;
   mPttInterface->firePttEvent(cpcFunc(PushToTalkHandler::onPttSessionError), mService, session->getPtt(), evt);
   endWithoutDelay(session, session->getPtt(), PushToTalk::PttReceiverEndReasonType_WaitForAnswerTimeout);
}

PttReceiverTalkingState::PttReceiverTalkingState(PushToTalkManagerImpl* manager) :
PttReceiverSessionState(manager, PttSessionStateType::PttSessionState_Talking)
{
}

PttReceiverTalkingState::~PttReceiverTalkingState()
{
}

void PttReceiverTalkingState::onEntry(PttSession* session)
{
   PttLog(<< "PttReceiverTalkingState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   session->mStartTime = std::chrono::system_clock::now();
}

void PttReceiverTalkingState::onExit(PttSession* session)
{
   PttLog(<< "PttReceiverTalkingState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
}

int PttReceiverTalkingState::mute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverTalkingState::mute(): " << this << " service: " << mService << " ptt: " << session->getPtt());
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleSessionMute();
   return kSuccess;
}

int PttReceiverTalkingState::unmute(PttSession* session, PushToTalkSessionHandle ptt)
{
   InfoLog(<< "PttReceiverTalkingState::unmute(): " << this << " service: " << mService << " ptt: " << session->getPtt());
   PttReceiverSession* receiverSession = static_cast<PttReceiverSession*>(session);
   receiverSession->handleSessionUnmute();
   return kSuccess;
}


PttReceiverEndingState::PttReceiverEndingState(PushToTalkManagerImpl* manager) :
PttReceiverSessionState(manager, PttSessionStateType::PttSessionState_Ending),
mSubState(-1)
{
}

PttReceiverEndingState::~PttReceiverEndingState()
{
}

void PttReceiverEndingState::onEntry(PttSession* session)
{
   PttLog(<< "PttReceiverEndingState::onEntry(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   if ((PttReceiverEndingSubState)mSubState == PttReceiverEndingSubState_Rejected)
   {
      static_cast<PttReceiverSession*>(session)->resetRejectedTimer();
   }
}

void PttReceiverEndingState::onExit(PttSession* session)
{
   PttLog(<< "PttReceiverEndingState::onExit(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " endpoint: " << session->mLocalEndpoint.presentationFormat().c_str());
   static_cast<PttReceiverSession*>(session)->cancelRejectedTimer();
}

int PttReceiverEndingState::end(PttSession* session, PushToTalkSessionHandle ptt, const cpc::string& reason)
{
   InfoLog(<< "PttReceiverEndingState::end(): " << this << " service: " << mService << " ptt: " << session->getPtt() << " state: " << PttSessionState::getName(session->mState) << " ignore end request as ending already in progress");
   return kSuccess;
}

void PttReceiverEndingState::onRejectedTimeout(PttSession* session)
{
   DebugLog(<< "PttReceiverEndingState::onRejectedTimeout(): " << this << " manager: " << mManager << " ptt: " << session->getPtt() << " destroying ptt session due to reject timeout in session state: " << getName());
   session->changeState(PttSessionState_Idle);
}

}

}

#endif // CPCAPI2_BRAND_PTT_MODULE
