#pragma once

#ifndef CPCAPI2_PUSHTOTALK_MANAGER_IMPL_H
#define CPCAPI2_PUSHTOTALK_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "ptt/PushToTalkTypes.h"
#include "ptt/PushToTalkManager.h"
#include "ptt/PushToTalkHandler.h"
#include "PushToTalkHandlerInternal.h"
#include "PushToTalkTypesInternal.h"
#include "PushToTalkSyncHandler.h"
#include "PushToTalkUnicastReceiver.h"
#include "PushToTalkUnicastSender.h"
#include "PushToTalkManagerInterface.h"
#include "PushToTalkEndpointDiscoverySession.h"
#include "PushToTalkConferenceConnectorSession.h"
#include "../media/MediaManagerInterface.h"
#include <rutil/DeadlineTimer.hxx>
#include <resip/stack/Tuple.hxx>

#include "../util/STAAssertion.h"

#include "peerconnection/PeerConnectionManager.h"
#include "peerconnection/PeerConnectionHandler.h"
#include "peerconnection/PeerConnectionSyncHandler.h"
#include "peerconnection/PeerConnectionHandlerInternal.h"

#include "confconnector/ConferenceConnectorHandler.h"
#include "confconnector/ConferenceConnectorInterface.h"

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>

#include <vector>
#include <string>
#include <array>

namespace webrtc_recon
{
struct MediaStackSettings;
}

namespace CPCAPI2
{

namespace PushToTalk
{

class PushToTalkSessionHandler;
class PushToTalkUnicastReceiver;
class PttSessionFactory;
class PttSessionStateFactory;
class PttSession;
class PttSessionCall;
class PeerSenderCall;
class PeerReceiverCall;

class PushToTalkTransportHandler
{
public:
   virtual ~PushToTalkTransportHandler() {}

   virtual int onIncoming(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen) = 0;
};

struct UnicastEndpoint
{
   unsigned int targetPort;
   char targetIpAddress[16];
};

struct CloudEndpoint
{
   unsigned int cloudEndpointId;
};

union EndpointUnion
{
   UnicastEndpoint unicast;
   CloudEndpoint cloud;
};

struct Endpoint
{
   int transportType = 0;
   EndpointUnion transportSpecific;
};

class PushToTalkTransport
{

public:

   virtual ~PushToTalkTransport() {}

   virtual int init(CPCAPI2::PhoneInterface* phone, PushToTalkTransportHandler* handler, std::shared_ptr<PushToTalkSenderReceiveSocketHolder> socketHolder, const PushToTalkServiceSettings& serviceSettings, const PushToTalkSettingsInternal& internalSettings) = 0;
   virtual int shutdown(PttServiceStatusChangedEvent::Reason reason = PttServiceStatusChangedEvent::Reason_None, bool sendStatus = true) = 0;
   virtual int broadcast(const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler) = 0;
   virtual int sendTo(const Endpoint& endpoint, const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler) = 0;

};

class UnicastTransport : public std::enable_shared_from_this<UnicastTransport>,
                         public PushToTalkTransport,
                         public PushToTalkUnicastReceiverHandler,
                         public PushToTalkUnicastSenderHandler
{

public:

   UnicastTransport(std::shared_ptr<PushToTalkManagerImpl> pttManager);
   virtual ~UnicastTransport();

   virtual int init(CPCAPI2::PhoneInterface* phone, PushToTalkTransportHandler* handler, std::shared_ptr<PushToTalkSenderReceiveSocketHolder> socketHolder, const PushToTalkServiceSettings& serviceSettings, const PushToTalkSettingsInternal& internalSettings) OVERRIDE;
   virtual int shutdown(PttServiceStatusChangedEvent::Reason reason = PttServiceStatusChangedEvent::Reason_None, bool sendStatus = true) OVERRIDE;
   virtual int broadcast(const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler) OVERRIDE;
   virtual int sendTo(const Endpoint& endpoint, const std::string& payload, bool highPriority, bool noBurst, unsigned int messageId, bool enableHandler) OVERRIDE;

   // PushToTalkUnicastReceiverHandler
   virtual void onReceiverStarted() OVERRIDE;
   virtual void onReceiverStopped() OVERRIDE;
   virtual void onIncomingUnicast(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen) OVERRIDE;

   // PushToTalkUnicastSenderHandler
   virtual void onSenderStarted() OVERRIDE;
   virtual void onSenderStopped() OVERRIDE;
   virtual void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets) OVERRIDE;
   virtual void onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length) OVERRIDE;

   void queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   void startDiscovery(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   void stopDiscovery(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   bool hasDiscoveryStarted();
   std::string getLocalIp() { return mLocalIp; }
   void unsendAll(unsigned int messageId);
   void unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target);
   void setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable);
   const std::map<std::string, PttIdentity>& getEndpointList();
   void addToDiscoveryList(PttIdentity& identity);

   void onQueryEndpointsRequest(const PttQueryEndpointsRequest& queryRequestEvt, const resip::Tuple& incomingTuple);
   void onQueryEndpointsResponse(const PttQueryEndpointsResponse& evt, const resip::Tuple& incomingTuple);
   void onReportEndpointsEvent(const PttReportEndpointsEvent& evt, const resip::Tuple& incomingTuple);

   void onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

private:

   resip::Data guessLocalIpAddress(const PushToTalkServiceSettings& serviceSettings) const;
   void onReceiverStartedImpl();
   void onReceiverStoppedImpl();
   void onSenderStartedImpl();
   void onSenderStoppedImpl();
   void onOutgoingSendCompletedImpl(const std::vector<boost::asio::ip::udp::endpoint>& targets);
   void onOutgoingSendFailedImpl(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length);

private:

   std::weak_ptr<PushToTalkManagerImpl> mPttManager;
   PushToTalkTransportHandler* mHandler;
   std::shared_ptr<PushToTalkUnicastSender> mUnicastSender;
   std::shared_ptr<PushToTalkUnicastReceiver> mUnicastReceiver;
   std::unique_ptr<PttEndpointDiscoverySession> mDiscoverySession;
   std::string mLocalIp;

   boost::asio::io_context mIoContext;
   std::unique_ptr<std::thread> mThread;
   std::shared_ptr<boost::asio::executor_work_guard<boost::asio::io_context::executor_type>> mWork;

};

class PushToTalkManagerImpl : public CPCAPI2::PeerConnection::PeerConnectionHandlerInternal,
                              public CPCAPI2::PeerConnection::PeerConnectionSyncHandler,
                              public CPCAPI2::ConferenceConnector::ConferenceConnectorHandler,
                              public CPCAPI2::ConferenceConnector::ConferenceConnectorSyncHandler,
                              public PushToTalkTransportHandler,
                              public resip::DeadlineTimerHandler,
                              public std::enable_shared_from_this<PushToTalkManagerImpl>
{

public:

   PushToTalkManagerImpl(PhoneInterface* cpcPhone, CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, LocalLogger* localLogger);
   virtual ~PushToTalkManagerImpl();

   // Associated to PushToTalkManager
   int setHandler(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, PushToTalkHandler* handler);
   int configureService(PushToTalkServiceHandle service, const PushToTalkServiceSettings& settings);
   int queryEndpointList(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int configureEndpointListAutoUpdate(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool autoUpdate);
   int setChannelSubscriptions(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels);
   int startService(CPCAPI2::PushToTalk::PushToTalkServiceHandle account);
   int shutdownService(CPCAPI2::PushToTalk::PushToTalkServiceHandle account, bool sendStatus, bool releaseHandler = true);
   int createPttSession(PushToTalkSessionHandle ptt);
   int addRecipient(PushToTalkSessionHandle ptt, const PttIdentity& identity);
   int setChannel(PushToTalkSessionHandle ptt, const cpc::string& channel);
   int start(PushToTalkSessionHandle ptt);
   int end(PushToTalkSessionHandle ptt);
   int startTalkSpurt(PushToTalkSessionHandle ptt);
   int endTalkSpurt(PushToTalkSessionHandle ptt);
   int accept(PushToTalkSessionHandle ptt);
   int reject(PushToTalkSessionHandle ptt);
   int setAudioDeviceCloseDelay(int audioDeviceCloseDelay);
   int mutePttSession(PushToTalkSessionHandle ptt);
   int unmutePttSession(PushToTalkSessionHandle ptt);
   int mutePttService(PushToTalkServiceHandle service);
   int unmutePttService(PushToTalkServiceHandle service);
   int removeObservers();

   // Associated to PushToTalkManagerInternal
   int setPttInternalSettings(CPCAPI2::PushToTalk::PushToTalkServiceHandle account, const PushToTalkSettingsInternal& settings);
   int enableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int disableUnicastTransmission(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int enableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int disableUnicastReceive(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int dropIncomingJsonMessages(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, bool enable);
   int disconnectWanConnection(CPCAPI2::PushToTalk::PushToTalkServiceHandle service);
   int queryMediaStatistics(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int enableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int disableMediaInactivityMonitor(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt);
   int handleRawPttRequest(const cpc::string& srcIp, unsigned int srcPort, const cpc::string& pttRequest);

   // Associated to NetworkChangeHandler
   int onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

   // Inherited from PeerConnectionHandler
   virtual int onSignalingStateChange(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SignalingStateChangeEvent& args) OVERRIDE;
   virtual int onCreateOfferResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateOfferResult& args) OVERRIDE;
   virtual int onCreateAnswerResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::CreateAnswerResult& args) OVERRIDE;
   virtual int onSetLocalSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetLocalSessionDescriptionResult& args) OVERRIDE;
   virtual int onSetRemoteSessionDescriptionResult(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::SetRemoteSessionDescriptionResult& args) OVERRIDE;
   virtual int onMediaInactivity(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::MediaInactivityEvent& args) OVERRIDE;
   virtual int onError(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::ErrorEvent& args) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
   virtual int onPeerConnectionMediaStatistics(CPCAPI2::PeerConnection::PeerConnectionHandle pc, const CPCAPI2::PeerConnection::PeerConnectionMediaStatisticsEvent& args) OVERRIDE;
#endif

   // ConferenceConnectorHandler
   virtual int onServiceConnectionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ServiceConnectionStatusEvent& args) OVERRIDE;
   virtual int onConferenceCreated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceCreatedEvent& args) OVERRIDE;
   virtual int onConferenceEnded(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceEndedEvent& args) OVERRIDE;
   virtual int onConferenceListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceListUpdatedEvent& args) OVERRIDE;
   virtual int onConferenceParticipantListUpdated(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args) OVERRIDE;
   virtual int onConferenceSessionStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionStatusChangedEvent& args) OVERRIDE;
   virtual int onConferenceSessionMediaStatusChanged(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceSessionMediaStatusChangedEvent& args) OVERRIDE;

   // PushToTalkTransportHandler
   virtual int onIncoming(const std::string& ipAddr, unsigned int port, const char* payload, std::size_t payloadLen) OVERRIDE;

   // PushToTalkUnicastSenderHandler
   virtual void onSenderStarted();
   virtual void onSenderStopped();
   virtual void onOutgoingSendCompleted(const std::vector<boost::asio::ip::udp::endpoint>& targets);
   virtual void onOutgoingSendFailed(const boost::asio::ip::udp::endpoint& target, const char* payload, size_t length);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void setCodecMediaInfo(CPCAPI2::PeerConnection::MediaInfo& mediaInfo) const;
   void sendToWire(const std::string& payload, bool enableHandler, bool updateParams = false, const std::string& targetIpAddress = "", bool noBurst = false, unsigned int targetPort = 0, unsigned int messageId = 0, bool highPriority = false);
   void destroySession(PushToTalkSessionHandle ptt);
   void updateStaleSessionList(PushToTalkSessionHandle ptt);
   void updateStaleSessionList(cpc::string& sessionId);
   void addToDiscoveryList(PttIdentity& identity);
   void onDiscoveryListUpdate(const PttDiscoveryListUpdate& updateEvt);
   void initiateRecording(PushToTalkSessionHandle pttSession);
   const std::map<std::string, PttIdentity>&  getEndpointList();

   static int compressEndpointList(const cpc::vector<cpc::string>& endpoints, cpc::string& compressedList);
   static int decompressEndpointList(const cpc::string& compressedList, cpc::vector<cpc::string>& endpoints);

   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle startConfSessionWith(const cpc::string& ident, PushToTalkSessionHandle pttSession, PttIdentityType identityType, CPCAPI2::ConferenceConnector::CloudConferenceHandle& conf);
   // CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle startConfSessionWithMyConference(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceHandle conf);
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle startConfSessionWithMyConference(CPCAPI2::ConferenceConnector::CloudConferenceHandle conf);
   void endConfSession(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle);
   void doConferenceSessionMute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session);
   void doConferenceSessionUnmute(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session);
   void setMyConfSession(CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle session) { mMySession = session; }

   struct PttUnicastState
   {
      UInt64 lastUpdateTime = 0;
      bool isUpdating = false;
      std::set<std::string> endpointList;
   };

   CPCAPI2::PhoneInterface* getPhone() { return mPhone; }
   PushToTalkServiceHandle getService() { return mService; }
   PttServiceStatusChangedEvent::Status getServiceStatus() { return mServiceStatus; }
   PushToTalkManagerInterface* getPttManager() { return mPttMgr; }
   resip::MultiReactor& getReactor();
   CPCAPI2::PeerConnection::PeerConnectionManager* getPeerManager() { return mPeerConnMgr; }
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* getConferenceManager() { return mConfConnMgr; }
   PttSessionStateFactory* getSenderStateFactory() { return mSenderStateFactory; }
   PttSessionStateFactory* getReceiverStateFactory() { return mReceiverStateFactory; }
   PushToTalkSettingsInternal& getInternalSettings() { return mInternalSettings; }
   PushToTalkServiceSettings& getServiceSettings() { return mServiceSettings; }
   cpc::vector<cpc::string>& getSubscribedChannels() { return mSubscribedChannels; }
   bool isNetworkChangeInProgress() { return mNetworkChangeInProgress; }
   void unsendAll(unsigned int messageId);
   void unsend(unsigned int messageId, const boost::asio::ip::udp::endpoint& target);
   void setKeepAliveTarget(const boost::asio::ip::udp::endpoint& target, bool enable);
   void sendServiceStatus(PttServiceStatusChangedEvent::Status status, PttServiceStatusChangedEvent::Reason reason = PttServiceStatusChangedEvent::Reason_None, std::string description = "");
   void addToStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);
   bool isSessionInStaleSessionList(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& participantIdentity);
   CPCAPI2::ConferenceConnector::CloudConferenceHandle getCloudConference(std::string& identity);
   bool doWeHaveChannelOverride(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference);
   std::shared_ptr<webrtc_recon::MediaStackImpl>& getMediaStack() { return mMediaStack; };
   // TODO: void setCloudConferenceParticipant(CPCAPI2::PushToTalk::PushToTalkSessionHandle ptt, CPCAPI2::ConferenceConnector::CloudConferenceParticipantHandle participant);
   void logWifiStatus();

private:

   // Associated to messages received from PushToTalkUnicastReceiver
   void onIncomingImpl(const std::string& ipAddr, unsigned int port, const std::string& payload);
   void onPttEndEvent(const PttEndEvent& evt, const resip::Tuple& incomingTuple);
   void onPttInitiateEvent(const PttInitiateEvent& evt, const resip::Tuple& incomingTuple);
   void onClientOfferEvent(const PttClientOfferEvent& evt, const resip::Tuple& incomingTuple);
   void onPttReceiverEndedEvent(const PttReceiverEndedEvent& evt, const resip::Tuple& incomingTuple);

   // Associated to PushToTalkUnicastSender
   void onSenderStartedImpl();
   void onSenderStoppedImpl();
   void onOutgoingSendCompletedImpl(const std::vector<boost::asio::ip::udp::endpoint>& targets);
   void onOutgoingSendFailedImpl(const boost::asio::ip::udp::endpoint& target, const std::string& payload);

   PttSession* getPttSessionForCallId(uint32_t call);
   PttSession* getPttSessionForSessionId(const cpc::string& sessionId);
   PttSession* getPttSessionForPeerConnection(CPCAPI2::PeerConnection::PeerConnectionHandle pc);
   PushToTalk::PushToTalkSessionHandle getPttHandleForCall(uint32_t call);
   std::vector<CPCAPI2::PushToTalk::PushToTalkSessionHandle> getPttSessionList();
   bool isSubscribedChannel(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, std::string& channelDisplayName);
   bool getSpeakerIdentity(
      CPCAPI2::ConferenceConnector::CloudConferenceHandle conference,
      const cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& participantList,
      bool& olderFloorOwnerExists,
      CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);
   bool areWeInTheList(const cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& participantList);
   bool doesReceiverSessionExist(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, PttSession*& session);
   bool doesSenderSessionExist(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, PttSession*& session);
   bool areThereMultipleFloorOwners(const cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& participantList);
   bool isStaleSession(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity);
   bool doesSomeoneHaveTheFloor(CPCAPI2::ConferenceConnector::CloudConferenceHandle conference, const cpc::vector<CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo>& participantList, CPCAPI2::ConferenceConnector::CloudConferenceParticipantInfo& speakerIdentity); // No stale check
   int handleNetworkChangeWAN(const CPCAPI2::NetworkChangeEvent& args);
   int handleNetworkChangeLAN(const CPCAPI2::NetworkChangeEvent& args);
   void sendPttParticipantListUpdate(CPCAPI2::ConferenceConnector::ConferenceConnectorHandle conn, const CPCAPI2::ConferenceConnector::ConferenceParticipantListUpdatedEvent& args);
   void resetConferenceConnector();
   void setChannelSubscriptionsImpl(CPCAPI2::PushToTalk::PushToTalkServiceHandle service, const cpc::vector<cpc::string>& channels, bool& conferencesCreated);

   bool keepCurrentSession(PttSession* currentSession, const std::string& ipAddr, unsigned int port);
   resip::Data guessLocalIpAddress() const;
   std::string getLocalIdentity() const;

   void initiateRecordingImpl(PushToTalkSessionHandle pttSession);

   void updateReceiverEndedStaleTransactionList(const CPCAPI2::PushToTalk::PttReceiverEndedEvent& evt);
   bool isStaleReceiverEndedTransaction(const CPCAPI2::PushToTalk::PttReceiverEndedEvent& evt);
   std::array<std::string, 32> mStaleReceiverEndedTransactionIds;
   int mNextStaleReceiverEndedTransactionIdx;

   typedef std::map<CPCAPI2::PushToTalk::PushToTalkSessionHandle, PttSession*> PttSessionList;
   PttSessionList mSessionList;
   std::array<resip::Data, 32> mStaleSessionIds;
   int mNextStaleSessionIdx;
   PushToTalkServiceHandle mService;
   PttServiceStatusChangedEvent::Status mServiceStatus;
   LocalLogger* mLocalLogger;
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::PushToTalk::PushToTalkManagerInterface* mPttMgr;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnMgr;
   STAAssertion mThreadCheck;
   std::shared_ptr<webrtc_recon::MediaStackImpl> mMediaStack;

   bool mNetworkChangeInProgress;
   bool mPttServiceMuteEnabled;

   PushToTalk::PushToTalkSettingsInternal mInternalSettings;
   PushToTalk::PushToTalkServiceSettings mServiceSettings;

   PttSessionFactory* mSenderSessionFactory;
   PttSessionFactory* mReceiverSessionFactory;
   PttSessionStateFactory* mSenderStateFactory;
   PttSessionStateFactory* mReceiverStateFactory;

   std::shared_ptr<PushToTalkTransport> mTransport;
   std::shared_ptr<PushToTalkSenderReceiveSocketHolder> mSocketHolder;

   // WAN Specific
   CPCAPI2::ConferenceConnector::ConferenceConnectorManager* mConfConnMgr;
   PttConferenceConnectorSession* mConfConnSession;
   CPCAPI2::ConferenceConnector::CloudConferenceSessionHandle mMySession = 0;
   cpc::vector<cpc::string> mSubscribedChannels;

};

}

}

#endif // CPCAPI2_PUSHTOTALK_MANAGER_IMPL_H
