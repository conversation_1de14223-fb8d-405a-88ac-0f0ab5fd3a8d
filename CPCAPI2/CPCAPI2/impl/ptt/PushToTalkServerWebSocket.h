#pragma once

#ifndef CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_H
#define CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_H

#include "cpcapi2defs.h"
#include "phone/PhoneInterface.h"
#include "PushToTalkServerWebSocketSession.h"
#include "PushToTalkServerWebSocketId.h"
#include "PushToTalkManagerInterface.h"
#include "../util/STAAssertion.h"

// websocket server
#include <websocketpp/config/asio.hpp>
#include <websocketpp/server.hpp>

// websocket client
#include <websocketpp/client.hpp>

// rapidjson
#include <document.h>

// boost asio
#include <boost/asio.hpp>


namespace CPCAPI2
{
namespace PushToTalk
{
class PushToTalkServerWebSocketSession;
class PushToTalkServerWebSocketSessionHolder;

class PushToTalkServerWebSocket
{
public:
   PushToTalkServerWebSocket(CPCAPI2::PushToTalk::PushToTalkManagerInterface* pttIf, int httpPort);
   virtual ~PushToTalkServerWebSocket();

   void StartServer(CPCAPI2::Phone* phone, resip::ReadCallbackBase* startedCb = NULL);
   void StopServer();
   void SetServerIncomingMessageCallback(const std::function<void(PushToTalkServerWebSocketSession*, const std::string&)>& cb);
   void SetServerCloseConnectionCallback(const std::function<void(PushToTalkServerWebSocketSession*, const uint32_t&)>& cb);
   int CloseConnection(const websocketpp::connection_hdl& hdl);
   int Send(websocketpp::connection_hdl hdl, const char* payload, unsigned int payloadLen);
   void RemoveSession(websocketpp::connection_hdl hdl);
   size_t GetSessionCount();
   PushToTalkServerWebSocketSessionHolder* GetSession(const PushToTalkServerWebSocketSessionId* sessId);

private:
   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::connection_ptr conn_ptr;
   typedef server::message_ptr message_ptr;
   typedef std::map<websocketpp::connection_hdl, PushToTalkServerWebSocketSession*, std::owner_less<websocketpp::connection_hdl> > sess_list;

   void onOpen(server* s, websocketpp::connection_hdl hdl);
   void onOpenImpl(server* s, websocketpp::connection_hdl hdl);
   std::string get_password() const;

private:
   PushToTalkServerWebSocket() {};
   friend class PushToTalkServerWebSocketSession;
   friend class PushToTalkServerWebSocketSessionHolder;
   CPCAPI2::Phone* mPhone;
   CPCAPI2::PushToTalk::PushToTalkManagerInterface* mPttMgr;
   std::unique_ptr<server> mWebSockServer;
   sess_list mSessions;
   std::function<void(PushToTalkServerWebSocketSession*, const std::string&)> mServerIncomingMessageCb;
   std::function<void(PushToTalkServerWebSocketSession*, const uint32_t&)> mServerCloseConnectionCb;
   std::thread* mServerThread;
   int mHttpPort;
   STAAssertion mThreadCheck;
   std::mutex mMapLock;

};

class PushToTalkServerWebSocketSessionHolder
{
public:
   PushToTalkServerWebSocketSessionHolder(PushToTalkServerWebSocketSession* sess, const PushToTalkServerWebSocket::conn_ptr& conn) : mSess(sess), mConn(conn) {}
   virtual ~PushToTalkServerWebSocketSessionHolder() {}

   PushToTalkServerWebSocketSession* Session() {
      return mSess;
   }

private:
   // copy and assign are disallowed because it would be too easy to write code that
   // inadvertently keeps mConn alive as a full shared_ptr for way, way too long
   PushToTalkServerWebSocketSessionHolder(const PushToTalkServerWebSocketSessionHolder&) {}
   void operator=(PushToTalkServerWebSocketSessionHolder&) {}

private:
   PushToTalkServerWebSocketSession* mSess;
   PushToTalkServerWebSocket::conn_ptr mConn;
};

}

}

#endif // CPCAPI2_PUSHTOTALK_SERVER_WEBSOCKET_H
