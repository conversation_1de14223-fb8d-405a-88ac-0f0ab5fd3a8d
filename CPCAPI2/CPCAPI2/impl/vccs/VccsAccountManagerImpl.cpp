#include "VccsAccountManagerImpl.h"

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <memory>

#include <vccs/VccsAccountHandler.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::VCCS
#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include <websocket/json/WebSocketCommand.h>
#include <websocket/json/StatusResponse.h>
#include <websocket/states/StateDisconnected.h>
#include <websocket/states/StateConnecting.h>
#include <websocket/states/StateConnected.h>
#include <websocket/states/StateFailed.h>
#include <websocket/WebSocketStateMachine.h>
#include <websocket/WebSocketStateDispatcher.h>

#include "VccsInternalTypes.h"

using namespace CPCAPI2::VCCS::Account;
using CPCAPI2::WebSocket::RequestHandle;
using CPCAPI2::WebSocket::WebSocketCommand;
using CPCAPI2::WebSocket::WebSocketState;
using CPCAPI2::WebSocket::WebSocketStateMachine;
using CPCAPI2::WebSocket::WebSocketStateDispatcher;
using CPCAPI2::WebSocket::StateFailed;

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   ErrorEvent evt; \
   evt.errorText = ( ERRTEXT ); \
   fireEvent( cpcFunc( VccsAccountHandler::onError ),evt ); \
   return kError; \
}
#define INVALID_ACCOUNT_STATE_STR   "Invalid Account State"
#define INVALID_ACCOUNT_HANDLER_STR "Account Handler Already Set"

VccsAccountManagerImpl::VccsAccountManagerImpl(
   VccsAccountHandle hAccount,
   boost::asio::io_service& ioService,
   resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
   CPCAPI2::SslCipherOptions tlsSettings) :
     m_RequestHandle( 1 ),
     m_CallbackFifo( callbackFifo ),
     m_hAccount( hAccount ),
     m_IOService( ioService ),
     m_Handler( NULL ),
     m_CbHook( NULL ),
     m_StateMachine( NULL ),
     m_TlsSettings( tlsSettings ),
     m_SubscribeAfterReconnect(false)
{
}

VccsAccountManagerImpl::~VccsAccountManagerImpl()
{
   m_Handler = NULL;

   delete m_StateMachine;
   m_StateMachine = NULL;
}

int VccsAccountManagerImpl::configureDefaultAccountSettings(const VccsAccountSettings& vccsAccountSettings)
{
   // Always update the settings
   m_Settings = vccsAccountSettings;

   // Setup the state machine and dispatcher if it hasn't been done already.
   // Only do this one time. NB: settings is passed as a reference.
   if( m_StateMachine == NULL )
   {
      // Force this flag to true in the case of VCCS
      m_Settings.wsSettings.backgroundSocketsIfPossible = true;

      // Set the ignore login flag if there's no credentials
      if( m_Settings.userName.empty() )
         m_Settings.wsSettings.isLoginRequired = false;

      if (TLS_DEFAULT == m_Settings.wsSettings.tlsVersion) m_Settings.wsSettings.tlsVersion = m_TlsSettings.getTLSVersion(SslCipherUsageWebSockets);
      if (m_Settings.wsSettings.cipherSuite.empty()) m_Settings.wsSettings.cipherSuite = m_TlsSettings.getCiphers(SslCipherUsageWebSockets);

      DebugLog(<< "Creating state machine for WebSocket: " + m_Settings.wsSettings.webSocketURL);
      m_StateMachine = new WebSocketStateMachine( RESIPROCATE_SUBSYSTEM, m_IOService, m_Settings.wsSettings, "Vccs Account " + std::to_string(m_hAccount) );
   }
   else
   {
      DebugLog(<< "State machine already initialized");
   }

   return kSuccess;
}

int VccsAccountManagerImpl::setHandler(VccsAccountHandler* handler)
{
   // Ensure handler isn't already set.
   if( m_Handler != NULL && handler != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLER_STR );

   m_Handler = handler;
   return kSuccess;
}

int VccsAccountManagerImpl::enable()
{
   // It's only possible to enable the account if it's in an unregistered state.
   if (strcmp(m_StateMachine->getCurrentStateID(), STATE_DISCONNECTED_ID) != 0)
   {
      ErrLog(<< "When enabling VCCS account the state should be " << STATE_DISCONNECTED_ID << " but is " << m_StateMachine->getCurrentStateID());
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_STATE_STR);
   }

   // To trigger the enabling of the account, perform a state transition.
   m_StateMachine->setCurrentState( STATE_CONNECTING_ID );
   return kSuccess;
}

int VccsAccountManagerImpl::disable()
{
   // It should be possible to go to unregistered state from any state.
   if( !m_StateMachine->setCurrentState( STATE_DISCONNECTED_ID ))
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_STATE_STR );

   return kSuccess;
}

void VccsAccountManagerImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if( cbHook != NULL && context != NULL )
      m_CbHook = std::bind( cbHook, context );
}

int VccsAccountManagerImpl::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   if (!m_StateMachine) return kSuccess;

   const char *stateID( m_StateMachine->getCurrentStateID() );

   // we don't want to re-awake the state machine if it was previously disconnected
   if( strcmp( stateID , STATE_DISCONNECTED_ID ) == 0 )
      return kSuccess;

   DebugLog(<< "Network change detected; re-connecting websocket for VccsAccountManagerImpl");

   // reset the failed state expiry time, to account for network instability.
   std::shared_ptr< StateFailed > pFailedState( std::dynamic_pointer_cast< StateFailed >( m_StateMachine->getState( STATE_FAILED_ID )));
   if( pFailedState )
   {
      pFailedState->resetExpiryTime();
      if (m_Settings.autoSubscribeAfterNetworkChange)
         m_SubscribeAfterReconnect = true;
   }

   m_StateMachine->setCurrentState(( strcmp( stateID, STATE_FAILED_ID ) == 0 ) ? STATE_CONNECTING_ID : STATE_FAILED_ID );
   return kSuccess;
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
