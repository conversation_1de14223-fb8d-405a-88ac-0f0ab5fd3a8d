#pragma once
#ifndef __CPCAPI2_VCCSACCOUNTMANAGERINTERFACE_H__
#define __CPCAPI2_VCCSACCOUNTMANAGERINTERFACE_H__

#include <atomic>
#include <thread>
#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <phone/PhoneModule.h>
#include <vccs/VccsAccountManager.h>
#include "../util/AutoTestProcessor.h"
#include <phone/NetworkChangeManagerImpl.h>

namespace CPCAPI2
{
   struct NetworkChangeEvent;

   namespace VCCS
   {
      namespace Account
      {
         // Forward decl's
         struct AccountInfo;
         typedef struct AccountInfo AccountInfo;         

         class VccsAccountManagerImpl;

         class VccsAccountManagerInterface :
            public VccsAccountManager,
            public CPCAPI2::PhoneModule,
            public CPCAPI2::EventSyncHandler<CPCAPI2::NetworkChangeHandler>
#ifdef CPCAPI2_AUTO_TEST
            , public AutoTestProcessor
#endif
         {
         public:
            VccsAccountManagerInterface(Phone* phone);
            virtual ~VccsAccountManagerInterface();

            // VccsAccountManager implementation
            VccsAccountHandle create() OVERRIDE;
            int configureDefaultAccountSettings(VccsAccountHandle hAccount, const VccsAccountSettings& vccsAccountSettings) OVERRIDE;
            int applySettings(VccsAccountHandle hAccount) OVERRIDE;
            int decodeProvisioningResponse(const cpc::string & provisioningResponse, cpc::vector<Account::VccsAccountSettings>& outVccsAccountSettings) OVERRIDE;
            int destroy(VccsAccountHandle hAccount) OVERRIDE;
            int setHandler(VccsAccountHandle hAccount, VccsAccountHandler* handler) OVERRIDE;
            int enable(VccsAccountHandle hAccount) OVERRIDE;
            int disable(VccsAccountHandle hAccount) OVERRIDE;
            bool crackVCCSURL( const cpc::string& inURL, const bool&  secureWebSocketRequired, cpc::string& outWebSocket, cpc::string& serverName, int& portNumber, cpc::string& groupName, cpc::string& subscriptionCode ) OVERRIDE;
            bool crackVCCSURL( const cpc::string& inURL, const cpc::string& expectedScheme, const bool&  secureWebSocketRequired, cpc::string& outWebSocket, cpc::string& serverName, int& portNumber, cpc::string& groupName, cpc::string& subscriptionCode ) OVERRIDE;
            int setSuspendable( VccsAccountHandle hAccount, bool isSuspendable ) OVERRIDE;
            int process(unsigned int timeout) OVERRIDE;
            void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE;
#ifdef CPCAPI2_AUTO_TEST
            AutoTestReadCallback* process_test(int timeout) OVERRIDE;
            int resetRequestHandle();
#endif

            // PhoneModule implementation
            void Release() OVERRIDE;

            // NetworkChangeHandler implementation
            virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

            // Methods which are dispatched to the io_service
            int createImpl( const VccsAccountHandle& hAccount );
            int configureDefaultAccountSettingsImpl(VccsAccountHandle hAccount, const VccsAccountSettings& vccsAccountSettings);
            int applySettingsImpl(VccsAccountHandle hAccount);
            int destroyImpl(VccsAccountHandle hAccount);
            int setHandlerImpl(VccsAccountHandle hAccount, VccsAccountHandler* handler);
            int enableImpl(VccsAccountHandle hAccount);
            int disableImpl(VccsAccountHandle hAccount);
            int setSuspendableImpl(VccsAccountHandle hAccount, bool isSuspendable );

            virtual void setCallbackHook(void (*cbHook)(void*), void* context);
            void setCallbackHookImpl( void (*cbHook)(void*), void* context );
            
            // Only used internally (for initialization of the conference manager interface)
            boost::asio::io_service& getIOServiceRef() { return m_IOService; }

            // returns a pointer (not owned) into the AccountInfo structure
            // for the corresponding handle
            AccountInfo *getAccountInfo( VccsAccountHandle hAccount );

         private: // methods

            int onNetworkChangeImpl(const NetworkChangeEvent& args);
#ifdef CPCAPI2_AUTO_TEST
            int resetRequestHandleImpl();
#endif

         private: // data

            // Static handle counter
            static std::atomic< VccsAccountHandle > s_CurrentHandle;

            // Map of AccountHandle(s) to AccountInfo(s)
            std::map< VccsAccountHandle, AccountInfo* > m_InfoMap;

            // used for dispatching
            boost::asio::io_service& m_IOService;
            
            // Callback Fifo which should be used to marshal the events (process method should be called)
            resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

            // Set to false once shutdown commences (to prevent further events)
            bool m_Shutdown;

            CPCAPI2::Phone* m_Phone;

            void (*m_CbHook)(void*);
            void* m_Context;
         };
         
         std::ostream& operator<<(std::ostream& os, const CPCAPI2::VCCS::Account::VccsAccountSettings& accountSettings);
      }
   }
}

#endif // __CPCAPI2_VCCSACCOUNTMANAGERINTERFACE_H__
