#pragma once
#ifndef __CPCAPI2_VCCS_LISTCONFERENCESCOMMAND_H__
#define __CPCAPI2_VCCS_LISTCONFERENCESCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class ListConferencesCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         bool m_IncludeSubscriptions;
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;

         ListConferencesCommand( bool includeSubscriptions = false )
            : m_IncludeSubscriptions( includeSubscriptions ), m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~ListConferencesCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_LIST_CONFERENCES; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // {"cmd":"LIST_CONFERENCES","requestID":5,"group":"counterpath.com","bridge":"1234","pin":"1111","displayName":"Jamie Strachan","sipAor":"sip:+<EMAIL>"}

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_RequestHandle);

            if( m_IncludeSubscriptions )
               CPCAPI2::Json::Write(writer, CLIENT_INCLUDE_SUBSCRIPTIONS, m_IncludeSubscriptions);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_LISTCONFERENCESCOMMAND_H__
