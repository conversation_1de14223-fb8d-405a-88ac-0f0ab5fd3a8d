#pragma once
#ifndef __CPCAPI2_VCCS_SCREENSHAREUPDATECOMMAND_H__
#define __CPCAPI2_VCCS_SCREENSHAREUPDATECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>
#include <map>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

#include <document.h>

namespace CPCAPI2
{
   namespace VCCS
   {
      class ScreenShareUpdateCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         const Conference::VccsConferenceHandle m_hConference;
         const Conference::VccsConferenceParticipantHandle m_hParticipant;
         const bool m_SetPresenter;
         const bool m_ScreenShareActive;
         const std::string m_ScreenShareURL;
         const Conference::ScreenSharingInfoList m_ScreenShareData;

         // Ctor used for screen presenter changes
         ScreenShareUpdateCommand(
            const Conference::VccsConferenceHandle& hConference,
            const Conference::VccsConferenceParticipantHandle& hParticipant )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_hConference( hConference ),
              m_hParticipant( hParticipant ),
              m_SetPresenter( true ),
              m_ScreenShareActive( false ) {}

         ScreenShareUpdateCommand(
            const Conference::VccsConferenceHandle& hConference,
            const Conference::VccsConferenceParticipantHandle& hParticipant,
            const bool screenShareActive,
            const std::string& screenShareURL,
            const Conference::ScreenSharingInfoList& screenShareData )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_hConference( hConference ),
              m_hParticipant( hParticipant ),
              m_SetPresenter( false ),
              m_ScreenShareActive( screenShareActive ),
              m_ScreenShareURL( screenShareURL ),
              m_ScreenShareData( screenShareData ) {}
         virtual ~ScreenShareUpdateCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SCREENSHARE_UPDATE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // {"cmd":"UNSUBSCRIBE","requestID":5,"group":"counterpath.com","bridge":"1234"}
            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,            getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,         m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID,      m_hConference);
            CPCAPI2::Json::Write(writer, CLIENT_PRESENTER_NUMBER,   m_hParticipant);
            CPCAPI2::Json::Write(writer, CLIENT_SET_PRESENTER,      m_SetPresenter);
            CPCAPI2::Json::Write(writer, CLIENT_SCREENSHARE_ACTIVE, m_ScreenShareActive);

            if( !m_ScreenShareURL.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_SCREENSHARE_URL, m_ScreenShareURL);

            if( m_ScreenShareData.size() > 0 )
            {
               writer.Key(CLIENT_SCREENSHARE_CONNECTION_INFO);
               writer.StartObject();
               Conference::ScreenSharingInfoList::const_iterator iter = m_ScreenShareData.begin();
               for( ; iter != m_ScreenShareData.end() ; ++iter )
               {
                  const Conference::ScreenSharingInfo& info( *iter );
                  CPCAPI2::Json::Write(writer, info.name, info.value);
               }
               writer.EndObject();
            }

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SCREENSHAREUPDATECOMMAND_H__
