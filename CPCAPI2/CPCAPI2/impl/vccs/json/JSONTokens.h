#pragma once
#ifndef __CPCAPI2_JSONTOKENS_H__
#define __CPCAPI2_JSONTOKENS_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

// Macros (defines) which are used for parsing the body of vccs messages

#define VCCS_PROTOCOL_VERSION                 1006

#define CLIENT_API_VERSION                    "apiVersion"
#define CLIENT_COMMAND                        "cmd"
#define CLIENT_REQUEST_ID                     "requestID"
#define CLIENT_CONFERENCE_ID                  "conferenceID"
#define CLIENT_CONFERENCE_DETAIL              "conferenceDetail"
#define CLIENT_CONFERENCE_CODE                "conferenceCode"
#define CLIENT_PARTICIPANT_NUMBER             "participantNumber"
#define CLIENT_PARTICIPANT_TYPE               "participantType"
#define CLIENT_ITEM_LIST                      "items"
#define CLIENT_PASSWORD                       "password"
#define CLIENT_GROUP                          "group"
#define CLIENT_USERNAME                       "username"
#define CLIENT_DISPLAY_NAME                   "displayName"
#define CLIENT_XMPP_USER_NAME                 "xmppUsername"
#define CLIENT_BRIDGE                         "bridge"
#define CLIENT_PIN                            "pin"
#define CLIENT_APPID                          "appId"
#define CLIENT_SIP_AOR                        "sipAor"
#define CLIENT_CALL_ID                        "callId"
#define CLIENT_INCLUDE_SUBSCRIPTIONS          "includeSubscriptions"
#define CLIENT_CONFERENCES_LIST               "conferences"
#define CLIENT_MUTED                          "muted"
#define CLIENT_JOIN_MUTED                     "joinMuted"
#define CLIENT_NEED_GUEST_ACCOUNT             "needGuestAccount"
#define CLIENT_XMPP_DOMAIN                    "xmppDomain"
#define CLIENT_XMPP_PROXY                     "xmppProxy"
#define CLIENT_XMPP_CONNECTPORT               "xmppConnectPort"
#define CLIENT_XMPP_CHATROOMJID               "xmppChatRoomJid"
#define CLIENT_XMPP_USERNAME                  "xmppUsername"
#define CLIENT_XMPP_PASSWORD                  "xmppPassword"
#define CLIENT_PRESENTER_NUMBER               "presenterNumber"
#define CLIENT_SET_PRESENTER                  "setPresenter"
#define CLIENT_SCREENSHARE_ACTIVE             "screenshareActive"
#define CLIENT_SCREENSHARE_URL                "screenshareUrl"
#define CLIENT_SCREENSHARE_CONNECTION_INFO    "screenshareConnectionInfo"
#define CLIENT_CAPABILITIES                   "capabilities"
#define CLIENT_LOCALE                         "locale"
#define CLIENT_PARTICIPANT_PIN                "participantPin"
#define CLIENT_MODERATOR_PIN                  "moderatorPin"
#define CLIENT_IS_RECORDING                   "isRecording"

// Command names
#define COMMAND_SUFFIX                         "_CMD"
#define COMMAND_NAME_UNKNOWN                   "UNKNOWN"
#define COMMAND_NAME_ERROR                     "ERROR"
#define COMMAND_NAME_LOGIN                     "LOGIN"
#define COMMAND_NAME_SUBSCRIBE                 "SUBSCRIBE"
#define COMMAND_NAME_UNSUBSCRIBE               "UNSUBSCRIBE"
#define COMMAND_NAME_LIST_CONFERENCES          "LIST_CONFERENCES"
#define COMMAND_NAME_MUTE_PARTICIPANT          "MUTE_PARTICIPANT"
#define COMMAND_NAME_UNMUTE_PARTICIPANT        "UNMUTE_PARTICIPANT"
#define COMMAND_NAME_KICK_PARTICIPANT          "KICK_PARTICIPANT"
#define COMMAND_NAME_PARTICIPANT_IS_RECORDING  "PARTICIPANT_IS_RECORDING"
#define COMMAND_NAME_PARTICIPANT_NOT_RECORDING "PARTICIPANT_NOT_RECORDING"
#define COMMAND_NAME_GET_XMPP_CONNECTION       "GET_XMPP_CONNECTION"
#define COMMAND_NAME_SET_CONFERENCE_MODE       "SET_CONFERENCE_MODE"
#define COMMAND_NAME_GET_CONFERENCE_DETAIL     "GET_CONFERENCE_DETAIL"
#define COMMAND_NAME_GET_CONFERENCE_CONFIG     "GET_CONFERENCE_CONFIGURATION" COMMAND_SUFFIX
#define COMMAND_NAME_SET_CONFERENCE_CONFIG     "SET_CONFERENCE_CONFIGURATION" COMMAND_SUFFIX
#define COMMAND_NAME_GET_CONFERENCE_HISTORY    "GET_CONFERENCE_HISTORY"       COMMAND_SUFFIX
#define COMMAND_NAME_DELETE_CONFERENCE_HISTORY "DELETE_CONFERENCE_HISTORY"    COMMAND_SUFFIX
#define COMMAND_NAME_GET_CONFERENCE_INVITE     "GET_CONFERENCE_INVITE"
#define COMMAND_NAME_SCREENSHARE_UPDATE        "SCREENSHARE_UPDATE_MSG"
#define COMMAND_NAME_SET_MIXER_OPTIONS         "SET_MIXER_OPTIONS"
#define COMMAND_NAME_GET_CONFERENCE_CONNECTION "GET_CONFERENCE_CONNECTION"    COMMAND_SUFFIX

// Response names (command names appended with response suffix)
#define RESPONSE_SUFFIX                         "_RESPONSE"
#define RESPONSE_NAME_STATUS                    "STATUS" RESPONSE_SUFFIX
#define RESPONSE_NAME_SUBSCRIBE                 COMMAND_NAME_SUBSCRIBE              RESPONSE_SUFFIX
#define RESPONSE_NAME_LIST_CONFERENCES          COMMAND_NAME_LIST_CONFERENCES       RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_XMPP_CONNECTION       COMMAND_NAME_GET_XMPP_CONNECTION    RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_CONFERENCE_DETAIL     COMMAND_NAME_GET_CONFERENCE_DETAIL  RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_CONFERENCE_INVITE     COMMAND_NAME_GET_CONFERENCE_INVITE  RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_CONFERENCE_CONFIG     "GET_CONFERENCE_CONFIGURATION"      RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_CONFERENCE_HISTORY    "GET_CONFERENCE_HISTORY"            RESPONSE_SUFFIX
#define RESPONSE_NAME_GET_CONFERENCE_CONNECTION "GET_CONFERENCE_CONNECTION"         RESPONSE_SUFFIX


// Unsolicited messages coming from the server
#define NOTIFICATION_NAME_CONFERENCE_UPDATED  "CONFERENCE_UPDATED"
#define NOTIFICATION_NAME_PARTICIPANT_UPDATED "PARTICIPANT_UPDATED"
#define NOTIFICATION_NAME_SCREENSHARE_UPDATE  COMMAND_NAME_SCREENSHARE_UPDATE
#define NOTIFICATION_NAME_VOICE_ACTIVITY      "VOICE_ACTIVITY"
#define NOTIFICATION_NAME_SET_CONFERENCE_MODE COMMAND_NAME_SET_CONFERENCE_MODE

#endif // CPCAPI2_BRAND_VCCS_MODULE
#endif // __CPCAPI2_JSONTOKENS_H__
