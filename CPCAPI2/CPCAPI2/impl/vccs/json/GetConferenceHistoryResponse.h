#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCEHISTORYRESPONSE_H__
#define __CPCAPI2_VCCS_GETCONFERENCEHISTORYRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/WebSocketCommonTypes.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>

#include "../json/ParseUtils.h"
#include "../VccsInternalTypes.h"

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceHistoryResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetConferenceHistoryResponse() :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            m_hConference( -1 ),
            m_Offset( 0 ),
            m_Count( 5 ),
            m_Total( 0 )
         {
         }
         virtual ~GetConferenceHistoryResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_GET_CONFERENCE_HISTORY; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         Conference::VccsConferenceHandle getConferenceID() const { return m_hConference; }

         bool getHistoryRecords( cpc::vector< Conference::ConferenceHistoryEntry >& entries )
         {
            entries = m_HistoryRecords;
            return true;
         }

         //{
         //	"cmd": "GET_CONFERENCE_HISTORY_RESPONSE",
         //	"requestID": {integer}
         //	"conferenceID": {integer},
         // "offset": (integer),
         // "count": (integer),
         // "total": (integer),
         // "records": [ (historyRecord)... ]
         //}
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // Fetch the conference ID (mandatory)
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            // Offset required
            if( inDocument.HasMember( "offset" ))
               m_Offset = inDocument[ "offset" ].GetInt64();
            else
               m_Offset = 0;

            // count required
            if( inDocument.HasMember( "count" ))
               m_Count = inDocument[ "count" ].GetInt64();
            else
               m_Count = 5;

            // total required
            if( inDocument.HasMember( "total" ))
               m_Total = inDocument[ "total" ].GetInt64();
            else
               m_Total = 0;

            // Parse any records if present
            m_HistoryRecords.empty();
            if( inDocument.HasMember( "records" ))
            {
               // If it's present however, it had better be an array.
               if( !inDocument[ "records" ].IsArray() )
                  return false;

               rapidjson::Value& recordsVal( inDocument[ "records" ] );
               for( rapidjson::SizeType i = 0 ; i < recordsVal.Size() ; ++i )
               {
                  Conference::ConferenceHistoryEntry record;
                  rapidjson::Value& element( recordsVal[ i ] );
                  if( parseHistoryRecord( element, record ))
                     m_HistoryRecords.push_back( record );
               }
            }

            return true;
         }

         //{
         //	"historyID": (integer),
         //	"conferenceStart": (timestamp),
         //	"conferenceEnd": (timestamp),
         //	"webParticipantCount": (integer),
         //	"dialInParticipantCount": (integer),
         //	"desktopCount": (integer),
         //	"mobileCount": (integer),
         //	"tabletCount" (integer):,
         //	"totalParticipants": (integer),
         //	"kickedParticipants": (integer),
         //	"screenshareUsage": (integer),
         //	"recordingUrl": "(url)",
         //	"participants": [ participant... ],
         //}
         bool parseHistoryRecord( const rapidjson::Value& element, Conference::ConferenceHistoryEntry& outRecord )
         {
            if( !element.HasMember( "historyID" ))
               return false;
            outRecord.historyID = element[ "historyID" ].GetInt64();

            if( !element.HasMember( "conferenceStart" ))
               return false;
            outRecord.conferenceStart = element[ "conferenceStart" ].GetInt64();

            if( !element.HasMember( "conferenceEnd" ))
               return false;
            outRecord.conferenceEnd = element[ "conferenceEnd" ].GetInt64();

            if( !element.HasMember( "webParticipantCount" ))
               return false;
            outRecord.webParticipantCount = element[ "webParticipantCount" ].GetInt64();

            if( !element.HasMember( "dialInParticipantCount" ))
               return false;
            outRecord.dialInParticipantCount = element[ "dialInParticipantCount" ].GetInt64();

            if( !element.HasMember( "desktopCount" ))
               return false;
            outRecord.desktopCount = element[ "desktopCount" ].GetInt64();

            if( !element.HasMember( "mobileCount" ))
               return false;
            outRecord.mobileCount = element[ "mobileCount" ].GetInt64();

            if( !element.HasMember( "tabletCount" ))
               return false;
            outRecord.tabletCount = element[ "tabletCount" ].GetInt64();

            if( !element.HasMember( "totalParticipants" ))
               return false;
            outRecord.totalParticipants = element[ "totalParticipants" ].GetInt64();

            if( !element.HasMember( "kickedParticipants" ))
               return false;
            outRecord.kickedParticipants = element[ "kickedParticipants" ].GetInt64();

            if( !element.HasMember( "screenshareUsage" ))
               return false;
            outRecord.screenshareUsage = element[ "screenshareUsage" ].GetInt64();

            if( !element.HasMember( "recordingUrl" ))
               return false;
            outRecord.recordingUrl = element[ "recordingUrl" ].GetString();

            // Parse any participant records if present
            outRecord.participants.empty();
            if( element.HasMember( "participants" ))
            {
               // If it's present however, it had better be an array.
               if( !element[ "participants" ].IsArray() )
                  return false;

               const rapidjson::Value& recordsVal( element[ "participants" ] );
               for( rapidjson::SizeType i = 0 ; i < recordsVal.Size() ; ++i )
               {
                  Conference::ParticipantHistoryEntry record;
                  const rapidjson::Value& element( recordsVal[ i ] );
                  if( parseParticipantHistory( element, record ))
                     outRecord.participants.push_back( record );
               }
            }

            return true;
         }

         //{
         //	"participantNumber": (integer),
         //	"userName": "(string)",
         //	"participantType": "(UNKNOWN|DIAL_IN|WEB|BRIA_DESKTOP|BRIA_MOBILE|BRIA_TABLET)",
         //	"capabilities": (integer),
         //	"sipAddress": "(string)",
         //	"displayName": "(string)",
         //	"xmppJid": "(string)",
         //	"callStartTime": (timestamp),
         //	"callEndTime": (timestamp),
         //	"moderator": (boolean),
         //	"owner": (boolean),
         //	"recorded": (boolean),
         //	"presenter": (boolean),
         //}
         bool parseParticipantHistory( const rapidjson::Value& element, Conference::ParticipantHistoryEntry& outRecord )
         {
            if( !element.HasMember( CLIENT_PARTICIPANT_NUMBER ))
               return false;
            outRecord.participantNumber = element[ CLIENT_PARTICIPANT_NUMBER ].GetInt();

            std::string temp;
            if( !element.HasMember( CLIENT_PARTICIPANT_TYPE ))
               return false;
            temp = element[ CLIENT_PARTICIPANT_TYPE ].GetString();

            if( temp == "DIAL_IN" )
               outRecord.participantType = Conference::ParticipantType_DialIn;
            else if( temp == "WEB" )
               outRecord.participantType = Conference::ParticipantType_Web;
            else if( temp == "BRIA_DESKTOP" )
               outRecord.participantType = Conference::ParticipantType_BriaDesktop;
            else if( temp == "BRIA_MOBILE" )
               outRecord.participantType = Conference::ParticipantType_BriaMobile;
            else if( temp == "BRIA_TABLET" )
               outRecord.participantType = Conference::ParticipantType_BriaTablet;
            else // if( temp == "UNKNOWN" )
               outRecord.participantType = Conference::ParticipantType_Unknown;

            if( !element.HasMember( CLIENT_CAPABILITIES ))
               return false;
            outRecord.capabilities = element[ CLIENT_CAPABILITIES ].GetInt();

            if( !element.HasMember( "sipAddress" ))
               return false;
            outRecord.sipAddress = element[ "sipAddress" ].GetString();

            if( !element.HasMember( CLIENT_DISPLAY_NAME ))
               return false;
            outRecord.displayName = element[ CLIENT_DISPLAY_NAME ].GetString();

            if( !element.HasMember( "xmppJid" ))
               return false;
            outRecord.xmppJid = element[ "xmppJid" ].GetString();

            if( !element.HasMember( "callStartTime" ))
               return false;
            outRecord.callStartTime = element[ "callStartTime" ].GetInt64();

            if( !element.HasMember( "callEndTime" ))
               return false;
            outRecord.callEndTime = element[ "callEndTime" ].GetInt64();

            if( !element.HasMember( "moderator" ))
               return false;
            outRecord.moderator = element[ "moderator" ].GetBool();

            if( !element.HasMember( "owner" ))
               return false;
            outRecord.owner = element[ "owner" ].GetBool();

            if( !element.HasMember( "recorded" ))
               return false;
            outRecord.recorded = element[ "recorded" ].GetBool();

            if( !element.HasMember( "presenter" ))
               return false;
            outRecord.presenter = element[ "presenter" ].GetBool();

            return true;
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         int64_t m_Offset;
         int64_t m_Count;
         int64_t m_Total;
         cpc::vector< Conference::ConferenceHistoryEntry > m_HistoryRecords;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCEHISTORYRESPONSE_H__
