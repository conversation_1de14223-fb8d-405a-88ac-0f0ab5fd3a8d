#pragma once
#ifndef __CPCAPI2_VCCS_SUBSCRIBECOMMAND_H__
#define __CPCAPI2_VCCS_SUBSCRIBECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"
#include <util/DeviceInfo.h>

namespace CPCAPI2
{
   namespace VCCS
   {
      class SubscribeCommand : public WebSocket::WebSocketCommand
      {
      public:
         std::string m_Group;
         std::string m_Bridge;
         std::string m_ConferenceCode;
         std::string m_Pin;
         std::string m_AppID;
         std::string m_SipAOR;
         std::string m_DisplayName;
         std::string m_XMPPUserName;
         std::string m_SIPInstance;
         std::string m_Locale;
         Conference::ParticipantType m_ParticipantType;
         int32_t m_Capabilities;
         WebSocket::RequestHandle m_RequestHandle;

         SubscribeCommand( const std::string& group,        // provided from settings (usually)
                           const std::string& displayName,  // provided from settings (usually)
                           const std::string& xmppUserName, // provided from settings (usually)
                           const Conference::SubscriptionInfo& info )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_Group( group ),
              m_Bridge( info.bridge ),
              m_ConferenceCode( info.conferenceCode ),
              m_Pin( info.pin ),
              m_AppID( info.applicationID ),
              m_SipAOR( info.sipAor ),
              m_DisplayName( displayName ),
              m_XMPPUserName( xmppUserName ),
              m_ParticipantType( info.participantType ),
              m_Capabilities(( int32_t ) info.capabilities ),
              m_Locale( info.locale )
         {
            // Disable the instance ID for the unit tests, because the test harness can't
            // know in advance what the instance ID is going to be.
#ifndef CPCAPI2_AUTO_TEST
            cpc::string instanceID;
            if( CPCAPI2::DeviceInfo::getInstanceId( instanceID ) == kSuccess )
            {
               /*
               m_SIPInstance  = "<urn:uuid:";
               m_SIPInstance += instanceID.c_str();
               m_SIPInstance += ">";
               */
               // For some reason ALU/Nokia seem to think the sip.instance should not
               // include the "<" or the ">". Not sure which is correct, but make this
               // change here.
               m_SIPInstance  = "urn:uuid:";
               m_SIPInstance += instanceID.c_str();
            }
#endif
         }

         virtual ~SubscribeCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SUBSCRIBE; }
         const WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // {"cmd":"SUBSCRIBE","requestID":5,"group":"counterpath.com","bridge":"1234","pin":"1111","displayName":"Jamie Strachan","sipAor":"sip:+<EMAIL>"}
            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_API_VERSION,  VCCS_PROTOCOL_VERSION);
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,      getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,   m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CAPABILITIES, m_Capabilities);

            if( !m_Locale.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_LOCALE, m_Locale);

            switch( m_ParticipantType )
            {
            case Conference::ParticipantType_DialIn:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "DIAL_IN");
               break;
            case Conference::ParticipantType_Web:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "WEB");
               break;
            case Conference::ParticipantType_BriaDesktop:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "BRIA_DESKTOP");
               break;
            case Conference::ParticipantType_BriaMobile:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "BRIA_MOBILE");
               break;
            case Conference::ParticipantType_BriaTablet:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "BRIA_TABLET");
               break;
            default: // case ParticipantType_Unknown:
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_TYPE, "UNKNOWN");
               break;
            }

            if( !m_Group.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_GROUP, m_Group);

            // Only one or the other, priority given to conference code
            if( !m_ConferenceCode.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_CODE, m_ConferenceCode);
            else if( !m_Bridge.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_BRIDGE, m_Bridge);

            if( !m_Pin.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_PIN, m_Pin);

            if( !m_AppID.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_APPID, m_AppID);

            if( !m_DisplayName.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_DISPLAY_NAME, m_DisplayName);

            if( !m_XMPPUserName.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_XMPP_USER_NAME, m_XMPPUserName);

            if( !m_SipAOR.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_SIP_AOR, m_SipAOR);

            if( !m_SIPInstance.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_CALL_ID, m_SIPInstance);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SUBSCRIBECOMMAND_H__
