#pragma once
#ifndef __CPCAPI2_VCCS_SCREENSHAREUPDATENOTIFICATION_H__
#define __CPCAPI2_VCCS_SCREENSHAREUPDATENOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <vccs/VccsConferenceTypes.h>

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class ScreenShareUpdateNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         ScreenShareUpdateNotification() :
            m_hConference( -1 ),
            m_hParticipant( -1 ),
            m_ScreenShareActive( false ),
            m_SetPresenter( false ) {}

         virtual ~ScreenShareUpdateNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_SCREENSHARE_UPDATE; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // cmd is mandatory
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            // conferenceID is mandatory
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            // presenter (optional?)
            if( inDocument.HasMember( CLIENT_PRESENTER_NUMBER ))
               m_hParticipant = inDocument[ CLIENT_PRESENTER_NUMBER ].GetInt();

            // Whether or not the presenter was changed
            if( inDocument.HasMember( CLIENT_SET_PRESENTER ))
               m_SetPresenter = inDocument[ CLIENT_SET_PRESENTER ].GetBool();

            // screenShareActive (optional; default is false)
            if( inDocument.HasMember( CLIENT_SCREENSHARE_ACTIVE ))
               m_ScreenShareActive = inDocument[ CLIENT_SCREENSHARE_ACTIVE ].GetBool();

            // screenShareURL (optional; default none == off)
            if( inDocument.HasMember( CLIENT_SCREENSHARE_URL ))
               m_ScreenShareURL = inDocument[ CLIENT_SCREENSHARE_URL ].GetString();

            // Read the property list (if any)
            if( inDocument.HasMember( CLIENT_SCREENSHARE_CONNECTION_INFO ))
            {
               rapidjson::Value const & info = inDocument[ CLIENT_SCREENSHARE_CONNECTION_INFO ];

               // Loop through the child items in the info and add them to our vector
               for( rapidjson::Value::ConstMemberIterator iter = info.MemberBegin() ;
                    iter != info.MemberEnd() ; ++iter )
               {
                  if( iter->value.GetType() != rapidjson::kStringType )
                     continue;

                  Conference::ScreenSharingInfo ssInfo;
                  ssInfo.name  = iter->name.GetString();
                  ssInfo.value = iter->value.GetString();
                  m_ScreenShareData.push_back( ssInfo );
               }
            }

            return true;
         }

         Conference::VccsConferenceHandle getConferenceID() const
         {
            return m_hConference;
         }

         Conference::VccsConferenceParticipantHandle getPresenter() const
         {
            return m_hParticipant;
         }

         /**
          * Returns true if the notification contains a request to set
          * the presenter (initiated by the host). False otherwise.
          */
         bool isPresenterSet() const
         {
            return m_SetPresenter;
         }

         bool isScreenShareActive() const
         {
            return m_ScreenShareActive;
         }

         std::string getScreenShareURL() const
         {
            return m_ScreenShareURL;
         }

         Conference::ScreenSharingInfoList getScreenShareData() const
         {
            return m_ScreenShareData; // a little inefficient, but .. meh
         }

      private:
         Conference::VccsConferenceHandle m_hConference;
         Conference::VccsConferenceParticipantHandle m_hParticipant;
         bool m_SetPresenter;
         bool m_ScreenShareActive;
         std::string m_ScreenShareURL;
         Conference::ScreenSharingInfoList m_ScreenShareData;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SCREENSHAREUPDATENOTIFICATION_H__