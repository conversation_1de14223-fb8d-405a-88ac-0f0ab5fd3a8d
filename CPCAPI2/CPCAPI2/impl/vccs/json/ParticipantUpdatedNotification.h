#pragma once
#ifndef __CPCAPI2_VCCS_PARTICIPANTUPDATEDNOTIFICATION_H__
#define __CPCAPI2_VCCS_PARTICIPANTUPDATEDNOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>

#include <vccs/VccsConferenceTypes.h>
#include "JSONTokens.h"
#include "ParseUtils.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class ParticipantUpdatedNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         ParticipantUpdatedNotification()
            : m_ConferenceID( -1 ) {}

         virtual ~ParticipantUpdatedNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_PARTICIPANT_UPDATED; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            if( !inDocument.HasMember( "participantStatus" ))
               return false;

            if( !inDocument.HasMember( "conferenceID" ))
               return false;

            m_ConferenceID = inDocument[ "conferenceID" ].GetInt();
            rapidjson::Value const & details = inDocument[ "participantStatus" ];
            if( !ParseUtils::parseParticipantStatus( details, m_ParticipantStatus ))
               return false;

            return true;
         }

         Conference::VccsConferenceHandle getConferenceID()
         {
            return m_ConferenceID;
         }

         Conference::ParticipantStatus getParticipantStatus()
         {
            return m_ParticipantStatus;
         }
         
      private:
         Conference::ParticipantStatus m_ParticipantStatus;
         Conference::VccsConferenceHandle m_ConferenceID;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_PARTICIPANTUPDATEDNOTIFICATION_H__