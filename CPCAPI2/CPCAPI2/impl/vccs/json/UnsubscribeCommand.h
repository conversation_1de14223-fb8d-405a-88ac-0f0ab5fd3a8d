#pragma once
#ifndef __CPCAPI2_VCCS_UNSUBSCRIBECOMMAND_H__
#define __CPCAPI2_VCCS_UNSUBSCRIBECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class UnsubscribeCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         std::string m_Group;
         std::string m_Bridge;
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;

         UnsubscribeCommand( const std::string& group,             // provided from settings (usually)
                             const std::string& bridge )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_Group( group ),
              m_<PERSON>( bridge ) {}
         virtual ~UnsubscribeCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_UNSUBSCRIBE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // {"cmd":"UNSUBSCRIBE","requestID":5,"group":"counterpath.com","bridge":"1234"}
            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_GROUP,      m_Group);
            CPCAPI2::Json::Write(writer, CLIENT_BRIDGE,     m_Bridge);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_UNSUBSCRIBECOMMAND_H__
