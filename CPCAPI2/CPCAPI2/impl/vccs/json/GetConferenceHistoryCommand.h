#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCEHISTORYCOMMAND_H__
#define __CPCAPI2_VCCS_GETCONFERENCEHISTORYCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include <stringbuffer.h>
#include <writer.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceHistoryCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         Conference::VccsConferenceHandle m_hConference;
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         int64_t m_Offset;
         int64_t m_Count;
         bool    m_IncludeParticipants;
         Conference::HistoryID m_HistoryID;

         GetConferenceHistoryCommand(
            const Conference::VccsConferenceHandle& hConference,
            int64_t offset,
            int64_t count,
            bool includeParticipants,
            Conference::HistoryID historyID ) :
              m_hConference( hConference ),
              m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_Offset( offset ),
              m_Count( count ),
              m_IncludeParticipants( includeParticipants ),
              m_HistoryID( historyID ) {}
         virtual ~GetConferenceHistoryCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_GET_CONFERENCE_HISTORY; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            // { "cmd":"GET_CONFERENCE_CONFIGURATION_RESPONSE", "requestID":5, "conferenceID": 7 }
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,        getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,     m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID,  m_hConference);

            if( m_Offset != 0 )
               CPCAPI2::Json::Write(writer, "offset", m_Offset );

            if( m_Count != 5 )
               CPCAPI2::Json::Write(writer, "count",  m_Count );

            if( m_IncludeParticipants )
               CPCAPI2::Json::Write(writer, "includeParticipants", m_IncludeParticipants );

            if( m_HistoryID != -1 )
               CPCAPI2::Json::Write(writer, "historyID", m_HistoryID );

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCEHISTORYCOMMAND_H__
