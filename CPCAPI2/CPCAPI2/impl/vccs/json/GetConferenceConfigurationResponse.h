#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCECONFIGURATIONRESPONSE_H__
#define __CPCAPI2_VCCS_GETCONFERENCECONFIGURATIONRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/WebSocketCommonTypes.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>

#include "../json/ParseUtils.h"
#include "../VccsInternalTypes.h"

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceConfigurationResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetConferenceConfigurationResponse()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_hConference( -1 )
         {
            // Set some sensible defaults
            m_Config.m_VideoLayout = Conference::VideoLayout_Focus;
            m_Config.m_FrameRate   = 10;
            m_Config.m_IsModerated = false;
            m_Config.m_NoModeratorOverrunMinutes = 0;
            m_Config.m_SendSummaryEmail = true;
         }
         virtual ~GetConferenceConfigurationResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_GET_CONFERENCE_CONFIG; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         Conference::ConferenceConfiguration getConfig() const { return m_Config; }
         Conference::VccsConferenceHandle getConferenceID() const { return m_hConference; }

         //{
         //	"cmd": "GET_CONFERENCE_CONFIGURATION_RESPONSE",
         //	"requestID": {integer}
         //	"conferenceID": {integer},
         //	"participantPin": "{pin}",
         //	"moderatorPin": "{pin}",
         //	"defaultVideoLayout": "{layout}",
         //	"isModerated": {boolean},
         // "nomoderatorOverrun": (integer: minutes 0, 1, 5, 10, 15, or 30),
         // "dropboxAccessToken": "(token)",
         // "sendSummaryEmail": (boolean),
         // "recordAudioOnly": (boolean),
         // "autoRecord": (boolean),
         //}
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // Fetch the conference ID (mandatory)
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            if( inDocument.HasMember( CLIENT_PARTICIPANT_PIN ))
               m_Config.m_ParticipantPin = inDocument[ CLIENT_PARTICIPANT_PIN ].GetString();

            if( inDocument.HasMember( CLIENT_MODERATOR_PIN ))
               m_Config.m_ModeratorPin = inDocument[ CLIENT_MODERATOR_PIN ].GetString();

            if( inDocument.HasMember( "defaultVideoLayout" ))
            {
               std::string videoLayoutStr( inDocument[ "defaultVideoLayout" ].GetString() );
               m_Config.m_VideoLayout = ParseUtils::stringToVideoLayout( videoLayoutStr );
            }

            // Trust the server validated the values
            if( inDocument.HasMember( "isModerated" ))
               m_Config.m_IsModerated = inDocument[ "isModerated" ].GetBool();

            if( inDocument.HasMember( "nomoderatorOverrun" ))
               m_Config.m_NoModeratorOverrunMinutes = inDocument[ "nomoderatorOverrun" ].GetInt();

            if( inDocument.HasMember( "dropboxAccessToken" ))
               m_Config.m_DropBoxToken = inDocument[ "dropboxAccessToken" ].GetString();

            if( inDocument.HasMember( "sendSummaryEmail" ))
               m_Config.m_SendSummaryEmail = inDocument[ "sendSummaryEmail" ].GetBool();

            if( inDocument.HasMember( "recordAudioOnly" ))
               m_Config.m_RecordAudioOnly = inDocument[ "recordAudioOnly" ].GetBool();

            if( inDocument.HasMember( "autoRecord" ))
               m_Config.m_AutoRecord = inDocument[ "autoRecord" ].GetBool();

            if( inDocument.HasMember( CLIENT_JOIN_MUTED ))
               m_Config.m_JoinMuted = inDocument[ CLIENT_JOIN_MUTED ].GetBool();

            return true;
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         Conference::ConferenceConfiguration m_Config;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCECONFIGURATIONRESPONSE_H__
