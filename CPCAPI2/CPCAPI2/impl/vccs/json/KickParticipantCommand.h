#pragma once
#ifndef __CPCAPI2_VCCS_KICKPARTICIPANTCOMMAND_H__
#define __CPCAPI2_VCCS_KICKPARTICIPANTCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <assert.h>
#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class KickParticipantCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         Conference::VccsConferenceHandle m_hConference;
         Conference::VccsConferenceParticipantHandle m_hParticipant;

         KickParticipantCommand(
            const Conference::VccsConferenceHandle& hConference,
            const Conference::VccsConferenceParticipantHandle& hParticipant )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_hConference( hConference ),
              m_hParticipant( hParticipant ) {}
         virtual ~KickParticipantCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_KICK_PARTICIPANT; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID, m_hConference);
            CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_NUMBER, m_hParticipant);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_KICKPARTICIPANTCOMMAND_H__
