#pragma once
#ifndef __CPCAPI2_VCCS_GETXMPPCONNECTIONRESPONSE_H__
#define __CPCAPI2_VCCS_GETXMPPCONNECTIONRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketResponse.h>

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetXMPPConnectionResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetXMPPConnectionResponse() :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            m_hConference( -1 ),
            m_Port( 0 ) {}
         virtual ~GetXMPPConnectionResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_GET_XMPP_CONNECTION; }

         /**
          * Returns an optional conference handle, or -1 if the
          * handle was not set in the status response.
          */
         Conference::VccsConferenceHandle getConferenceHandle() const
         {
            return m_hConference;
         }

         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            const std::string cmdName( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmdName != getCommandName() )
               return false;

            // {"xmppUsername":"<EMAIL>","conferenceID":8,"displayName":"Smokey the Bear","requestID":3,"cmd":"GET_XMPP_CONNECTION_RESPONSE","xmppWebsocketURL":"wss://***********:5291/","xmppDomain":"imap.mobilevoiplive.com","xmppChatRoomJid":"<EMAIL>"}
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            if( inDocument.HasMember( CLIENT_XMPP_DOMAIN ))
               m_Domain = inDocument[ CLIENT_XMPP_DOMAIN ].GetString();

            if( inDocument.HasMember( CLIENT_XMPP_PROXY ))
               m_Proxy = inDocument[ CLIENT_XMPP_PROXY ].GetString();

            if( inDocument.HasMember( CLIENT_XMPP_CONNECTPORT ))
               m_Port = inDocument[ CLIENT_XMPP_CONNECTPORT ].GetInt();

            if( inDocument.HasMember( CLIENT_XMPP_CHATROOMJID ))
               m_ChatRoomJID = inDocument[ CLIENT_XMPP_CHATROOMJID ].GetString();

            if( inDocument.HasMember( CLIENT_XMPP_USERNAME ))
               m_UserName = inDocument[ CLIENT_XMPP_USERNAME ].GetString();

            if( inDocument.HasMember( CLIENT_XMPP_PASSWORD ))
               m_Password = inDocument[ CLIENT_XMPP_PASSWORD ].GetString();

            if( inDocument.HasMember( CLIENT_DISPLAY_NAME ))
               m_DisplayName = inDocument[ CLIENT_DISPLAY_NAME ].GetString();

            return true;
         }

         std::string getDomain() { return m_Domain; }
         std::string getProxy() { return m_Proxy; }
         int getPort() { return m_Port; }
         std::string getChatRoomJID() { return m_ChatRoomJID; }
         std::string getUsername() { return m_UserName; }
         std::string getPassword() { return m_Password; }
         std::string getDisplayName() { return m_DisplayName; }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         std::string m_Domain;
         std::string m_Proxy;
         int m_Port;
         std::string m_ChatRoomJID;
         std::string m_UserName;
         std::string m_Password;
         std::string m_DisplayName;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETXMPPCONNECTIONRESPONSE_H__
