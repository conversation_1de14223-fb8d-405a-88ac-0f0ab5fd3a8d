#pragma once
#ifndef __CPCAPI2_VCCS_VOICEACTIVITY_NOTIFICATION_H__
#define __CPCAPI2_VCCS_VOICEACTIVITY_NOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <vccs/VccsConferenceTypes.h>

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class VoiceActivityNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         VoiceActivityNotification() :
            m_hConference(-1),
            m_hParticipant(-1),
            m_Talking(false),
            m_Floor(false),
            m_Energy(0) {}

         virtual ~VoiceActivityNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_VOICE_ACTIVITY; }

         bool fromString(const std::string &inString) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>(inString.c_str());

            if (!inDocument.HasMember(CLIENT_COMMAND))
               return false;

            // cmd is mandatory
            std::string cmd(inDocument[CLIENT_COMMAND].GetString());
            if (cmd != getNotificationName())
               return false;

            // conferenceID is mandatory
            if (!inDocument.HasMember(CLIENT_CONFERENCE_ID))
               return false;

            m_hConference = inDocument[CLIENT_CONFERENCE_ID].GetInt();

            // participant is mandatory
            if (!inDocument.HasMember(CLIENT_PARTICIPANT_NUMBER))
               return false;

            m_hParticipant = inDocument[CLIENT_PARTICIPANT_NUMBER].GetInt();

            if (inDocument.HasMember("talking"))
               m_Talking = inDocument["talking"].GetBool();

            if (inDocument.HasMember("floor"))
               m_Floor = inDocument["floor"].GetBool();

            if (inDocument.HasMember("energy"))
               m_Energy = inDocument["energy"].GetInt();

            return true;
         }

         Conference::VccsConferenceHandle getConferenceID() const
         {
            return m_hConference;
         }

         Conference::VccsConferenceParticipantHandle getParticipant() const
         {
            return m_hParticipant;
         }

         /**
         * Returns true if the participant is currently talking
         */
         bool isTalking() const
         {
            return m_Talking;
         }

         /**
          * Returns true if the specified participant currently has the AUDIO floor
         */
         bool hasFloor() const
         {
            return m_Floor;
         }

         /**
          * Voice activity energy - log scale, from 0 to 15
          */
         int getEnergy() const
         {
            return m_Energy;
         }

      private:
         Conference::VccsConferenceHandle m_hConference;
         Conference::VccsConferenceParticipantHandle m_hParticipant;
         bool m_Talking;
         bool m_Floor;
         int m_Energy;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_VOICEACTIVITY_NOTIFICATION_H__