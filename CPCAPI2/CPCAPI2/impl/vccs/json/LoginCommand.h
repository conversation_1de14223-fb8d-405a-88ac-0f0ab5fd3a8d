#pragma once
#ifndef __CPCAPI2_VCCS_LOGINCOMMAND_H__
#define __CPCAPI2_VCCS_LOGINCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include <stringbuffer.h>
#include <writer.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class LoginCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         std::string m_Group;
         std::string m_Username;
         std::string m_Password;
         std::string m_DisplayName;
         std::string m_XMPPUserName;
         WebSocket::RequestHandle m_RequestHandle;

         LoginCommand( const std::string& group,
                       const std::string& userName,
                       const std::string& password,
                       const std::string& displayName = "",
                       const std::string& xmppUsername = "" )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_Group( group ),
              m_Username( userName ),
              m_Password( password ),
              m_DisplayName( displayName ),
              m_XMPPUserName( xmppUsername ) {}
         virtual ~LoginCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_LOGIN; }
         const WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_API_VERSION, VCCS_PROTOCOL_VERSION);
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_GROUP,       m_Group);
            CPCAPI2::Json::Write(writer, CLIENT_USERNAME,    m_Username);
            CPCAPI2::Json::Write(writer, CLIENT_PASSWORD,    m_Password);

            if( !m_DisplayName.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_DISPLAY_NAME, m_DisplayName);

            if( !m_XMPPUserName.empty() )
               CPCAPI2::Json::Write(writer, CLIENT_XMPP_USER_NAME, m_XMPPUserName);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_LOGINCOMMAND_H__
