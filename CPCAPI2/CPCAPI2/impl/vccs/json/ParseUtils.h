#pragma once
#ifndef __CPCAPI2_VCCS_PARSEUTILS_H__
#define __CPCAPI2_VCCS_PARSEUTILS_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <map>
#include <string>
#include <document.h>     // rapidjson's DOM-style API

#include <websocket/json/WebSocketResponse.h>
#include <websocket/json/WebSocketNotification.h>

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
         struct ConferenceDetails;
         typedef struct ConferenceDetails ConferenceDetails;

         struct ParticipantStatus;
         typedef struct ParticipantStatus ParticipantStatus; 
      }

      /**
       * Functions to aid in building and parsing JSON documents.
       */
      class ParseUtils
      {
      public:

         /**
          * Helper method for parsing out a participant status object from json
          */
         static bool parseParticipantStatus( const rapidjson::Value& item, Conference::ParticipantStatus& outStatus )
         {
            if( !item.HasMember( CLIENT_PARTICIPANT_NUMBER ))
               return false;

            outStatus.participantNumber = item[ CLIENT_PARTICIPANT_NUMBER ].GetInt();

            if( item.HasMember( "userId" ))
               outStatus.userId = item[ "userId" ].GetInt64();
            if( item.HasMember( "xmppUserId" ))
               outStatus.xmppUserId = item[ "xmppUserId" ].GetInt64();
            if( item.HasMember( "xmppUsername" ))
               outStatus.xmppUsername = item[ "xmppUsername" ].GetString();
            if( item.HasMember( "sipUsername" ))
               outStatus.sipUsername = item[ "sipUsername" ].GetString();

            if( item.HasMember( "participantType" ))
            {
               std::string participantTypeStr( item[ "participantType" ].GetString() );
               if( participantTypeStr == "DIAL_IN" )
                  outStatus.participantType = Conference::ParticipantType_DialIn;
               else if( participantTypeStr == "WEB" )
                  outStatus.participantType = Conference::ParticipantType_Web;
               else if( participantTypeStr == "BRIA_DESKTOP" )
                  outStatus.participantType = Conference::ParticipantType_BriaDesktop;
               else if( participantTypeStr == "BRIA_MOBILE" )
                  outStatus.participantType = Conference::ParticipantType_BriaMobile;
               else if( participantTypeStr == "BRIA_TABLET" )
                  outStatus.participantType = Conference::ParticipantType_BriaTablet;
               else // if( participantTypeStr == "UNKNOWN" )
                  outStatus.participantType = Conference::ParticipantType_Unknown;
            }

            if( item.HasMember( "displayName" ))
               outStatus.displayName = item[ "displayName" ].GetString();
            if( item.HasMember( "emailAddress" ))
               outStatus.emailAddress = item[ "emailAddress" ].GetString();
            if( item.HasMember( "callSessionID" ))
               outStatus.callSessionID = item[ "callSessionID" ].GetString();
            if( item.HasMember( "sipAddress" ))
               outStatus.sipAddress = item[ "sipAddress" ].GetString();
            if( item.HasMember( CLIENT_IS_RECORDING ))
               outStatus.isRecording = item[ CLIENT_IS_RECORDING ].GetBool();
            if( item.HasMember( "recordingURI" ))
               outStatus.recordingURI = item[ "recordingURI" ].GetString();
            if( item.HasMember( "startTime" ))
               outStatus.startTime = item[ "startTime" ].GetInt64();
            if( item.HasMember( "endTime" ))
               outStatus.endTime = item[ "endTime" ].GetInt64();

            // Call Status enum
            if( item.HasMember( "callStatus" ))
            {
               std::string callStatusStr( item[ "callStatus" ].GetString() );
               if( callStatusStr == "ENDED" )
                  outStatus.callStatus = Conference::CallStatus_Ended;
               else if( callStatusStr == "LOCKED_OUT" )
                  outStatus.callStatus = Conference::CallStatus_LockedOut;
               else if( callStatusStr == "KICKED" )
                  outStatus.callStatus = Conference::CallStatus_Kicked;
               else if( callStatusStr == "IN_CONFERENCE" )
                  outStatus.callStatus = Conference::CallStatus_InConference;
               else // if( callStatusStr == "NONE" )
                  outStatus.callStatus = Conference::CallStatus_None;
            }

            if( item.HasMember( "isMuted" ))
               outStatus.isMuted = item[ "isMuted" ].GetBool();
            if( item.HasMember( "isModerator" ))
               outStatus.isModerator = item[ "isModerator" ].GetBool();
            if (item.HasMember("hasVideoFloor"))
               outStatus.hasVideoFloor = item["hasVideoFloor"].GetBool();
            if( item.HasMember( CLIENT_CAPABILITIES ))
               outStatus.capabilities = item[ CLIENT_CAPABILITIES ].GetInt();

            return true;
         }

         /**
          * Helper method for parsing out a conference details object from json
          */
         static bool parseConferenceDetails( const rapidjson::Value& item, Conference::ConferenceDetails& outDetails )
         {
            // Check required field
            if( !item.HasMember( "id" ))
               return false;

            outDetails.id = item[ "id" ].GetInt();

            if( item.HasMember( "created" ))
               outDetails.createdDate = item[ "created" ].GetInt64();
            if( item.HasMember( "title" ))
               outDetails.title = item[ "title" ].GetString();
            if( item.HasMember( "description" ))
               outDetails.description = item[ "description" ].GetString();
            if( item.HasMember( CLIENT_PARTICIPANT_PIN ))
               outDetails.participantPin = item[ CLIENT_PARTICIPANT_PIN ].GetString();
            if( item.HasMember( "lobbySipAddress" ))
               outDetails.lobbySipAddress = item[ "lobbySipAddress" ].GetString();
            if( item.HasMember( "bridgeNumber" ))
               outDetails.bridgeNumber = item[ "bridgeNumber" ].GetString();
            if( item.HasMember( "sipAddress" ))
               outDetails.sipAddress = item[ "sipAddress" ].GetString();
            if( item.HasMember( CLIENT_MODERATOR_PIN ))
               outDetails.moderatorPin = item[ CLIENT_MODERATOR_PIN ].GetString();
            if( item.HasMember( "moderatorSipAddress" ))
               outDetails.moderatorSipAddress = item[ "moderatorSipAddress" ].GetString();
            if( item.HasMember( "socketModerator" ))
               outDetails.socketModerator = item[ "socketModerator" ].GetBool();
            if( item.HasMember( "nomoderatorOverrun" ))
               outDetails.noModeratorOverrunMinutes = item[ "nomoderatorOverrun" ].GetInt();
            if( item.HasMember( "recordNames" ))
               outDetails.recordNames = item[ "recordNames" ].GetBool();
            if( item.HasMember( "supportVideo" ))
               outDetails.supportVideo = item[ "supportVideo" ].GetBool();
            if( item.HasMember( "supportAudio" ))
               outDetails.supportAudio = item[ "supportAudio" ].GetBool();
            if( item.HasMember( "supportScreensharing" ))
               outDetails.supportScreensharing = item[ "supportScreensharing" ].GetBool();
            if( item.HasMember( "supportMessaging" ))
               outDetails.supportMessaging = item[ "supportMessaging" ].GetBool();
            if (item.HasMember( "entryTone" ))
               outDetails.entryTone = item[ "entryTone" ].GetBool();
            if (item.HasMember( "exitTone" ))
               outDetails.exitTone = item[ "exitTone" ].GetBool();
            if( item.HasMember( CLIENT_XMPP_CHATROOMJID ))
               outDetails.xmppChatRoomJid = item[ CLIENT_XMPP_CHATROOMJID ].GetString();
            if( item.HasMember( CLIENT_SCREENSHARE_URL ))
               outDetails.screenshareUrl = item[ CLIENT_SCREENSHARE_URL ].GetString();

            // Set the "isConferenceLive" flag (this is kind of redundant information
            // since it should already be in the active field). oops.
            outDetails.isConferenceLive = item.HasMember( "active" ) ? item[ "active" ].GetBool() : false;

            // continue with additional information parsing for live conferences
            if( item.HasMember( "participants" ))
            {
               const rapidjson::Value& participantArray = item[ "participants" ];
               for( rapidjson::SizeType i = 0 ; i < participantArray.Size() ; ++i )
               {
                  const rapidjson::Value& arrayItem( participantArray[ i ] );
                  Conference::ParticipantStatus participantStatus;
                  if( !parseParticipantStatus( arrayItem, participantStatus ))
                     continue;

                  outDetails.participants.push_back( participantStatus );
               }
            }

            if( item.HasMember( "layout" ))
               outDetails.layout = stringToVideoLayout( item[ "layout" ].GetString() );

            if (item.HasMember("videoLayouts"))
            {
               const rapidjson::Value& videoLayoutsArray = item["videoLayouts"];
               for (rapidjson::SizeType i = 0; i < videoLayoutsArray.Size(); ++i)
               {
                  const rapidjson::Value& arrayItem( videoLayoutsArray[ i ] );
                  cpc::string videoLayoutStr( arrayItem.GetString() );
                  if( videoLayoutStr == videoLayoutToString( Conference::VideoLayout_Grid ))
                     outDetails.videoLayouts.push_back( Conference::VideoLayout_Grid );
                  else if( videoLayoutStr == videoLayoutToString( Conference::VideoLayout_Focus ))
                     outDetails.videoLayouts.push_back( Conference::VideoLayout_Focus );
                  else if( videoLayoutStr == videoLayoutToString( Conference::VideoLayout_Ribbon ))
                     outDetails.videoLayouts.push_back( Conference::VideoLayout_Ribbon );
                  else if( videoLayoutStr == videoLayoutToString( Conference::VideoLayout_Townhall ))
                     outDetails.videoLayouts.push_back( Conference::VideoLayout_Townhall );
               }
            }

            if( item.HasMember( "presenterNumber" ))
               outDetails.presenterNumber = item[ "presenterNumber" ].GetInt();
            if( item.HasMember( "started" ))
               outDetails.started = item[ "started" ].GetInt64();
            if( item.HasMember( "updated" ))
               outDetails.updated = item[ "updated" ].GetInt64();
            if( item.HasMember( "ended" ))
               outDetails.ended = item[ "ended" ].GetInt64();
            if( item.HasMember( "hosted" ))
               outDetails.hosted = item[ "hosted" ].GetBool();
            if( item.HasMember( "hostInConference" ))
               outDetails.hostInConference = item[ "hostInConference" ].GetBool();
            if( item.HasMember( "participantLocked" ))
               outDetails.participantLocked = item[ "participantLocked" ].GetBool();
            if( item.HasMember( "muteLocked" ))
               outDetails.muteLocked = item[ "muteLocked" ].GetBool();
            if( item.HasMember( "active" ))
               outDetails.active = item[ "active" ].GetBool();
            if( item.HasMember( "audioActive" ))
               outDetails.audioActive = item[ "audioActive" ].GetBool();
            if( item.HasMember( "videoActive" ))
               outDetails.videoActive = item[ "videoActive" ].GetBool();
            if( item.HasMember( CLIENT_IS_RECORDING ))
               outDetails.isRecording = item[ CLIENT_IS_RECORDING ].GetBool();
            if( item.HasMember( "socketParticipantNumber" ))
               outDetails.socketParticipantNumber = item[ "socketParticipantNumber" ].GetInt();
            if( item.HasMember( CLIENT_JOIN_MUTED ))
               outDetails.joinMuted = item[ CLIENT_JOIN_MUTED ].GetBool(); 
            if (item.HasMember("videoResolution"))
               outDetails.videoResolution = stringToVideoResolution( item["videoResolution"].GetString() );

            return true;
         }

         static Conference::VideoLayout stringToVideoLayout( const std::string& videoLayoutString )
         {
            Conference::VideoLayout result = Conference::VideoLayout_Focus; // default
            if( videoLayoutString == "grid" )
               result = Conference::VideoLayout_Grid;
            else if( videoLayoutString == "focus" )
               result = Conference::VideoLayout_Focus;
            else if( videoLayoutString == "ribbon" )
               result = Conference::VideoLayout_Ribbon;
            else if( videoLayoutString == "townhall" )
               result = Conference::VideoLayout_Townhall;

            return result;
         }

         static cpc::string videoLayoutToString( Conference::VideoLayout videoLayout )
         {
            switch( videoLayout )
            {
            case Conference::VideoLayout_Grid:
               return "grid";
            case Conference::VideoLayout_Ribbon:
               return "ribbon";
            case Conference::VideoLayout_Townhall:
               return "townhall";
            case Conference::VideoLayout_Focus:
            default:
               return "focus";
            }
         }

         static Conference::VideoResolution stringToVideoResolution(const std::string& videoResolutionString)
         {
            Conference::VideoResolution result = Conference::VideoResolution_1280x720; // default
            if (videoResolutionString == "640x400")
               result = Conference::VideoResolution_640x400;
            else if (videoResolutionString == "640x480")
               result = Conference::VideoResolution_640x480;
            else if (videoResolutionString == "800x600")
               result = Conference::VideoResolution_800x600;
            else if (videoResolutionString == "1024x576")
               result = Conference::VideoResolution_1024x576;
            else if (videoResolutionString == "1024x768")
               result = Conference::VideoResolution_1024x768;
            else if (videoResolutionString == "1280x720")
               result = Conference::VideoResolution_1280x720;
            else if (videoResolutionString == "1280x960")
               result = Conference::VideoResolution_1280x960;
            else if (videoResolutionString == "1440x1080")
               result = Conference::VideoResolution_1440x1080;
            else if (videoResolutionString == "1600x900")
               result = Conference::VideoResolution_1600x900;
            else if (videoResolutionString == "1600x1200")
               result = Conference::VideoResolution_1600x1200;
            else if (videoResolutionString == "1920x1080")
               result = Conference::VideoResolution_1920x1080;
            else if (videoResolutionString == "1920x1440")
               result = Conference::VideoResolution_1920x1440;
            else if (videoResolutionString == "848x480")
               result = Conference::VideoResolution_848x480;
            else if (videoResolutionString == "854x480")
               result = Conference::VideoResolution_854x480;

            return result;
         }

         static cpc::string videoResolutionToString(Conference::VideoResolution videoResolution)
         {
            switch (videoResolution)
            {
            case Conference::VideoResolution_640x400:
               return "640x400";
            case Conference::VideoResolution_640x480:
               return "640x480";
            case Conference::VideoResolution_800x600:
               return "800x600";
            case Conference::VideoResolution_1024x576:
               return "1024x576";
            case Conference::VideoResolution_1024x768:
               return "1024x768";
            case Conference::VideoResolution_1280x960:
               return "1280x960";
            case Conference::VideoResolution_1440x1080:
               return "1440x1080";
            case Conference::VideoResolution_1600x900:
               return "1600x900";
            case Conference::VideoResolution_1600x1200:
               return "1600x1200";
            case Conference::VideoResolution_1920x1080:
               return "1920x1080";
            case Conference::VideoResolution_1920x1440:
               return "1920x1440";
            case Conference::VideoResolution_848x480:
               return "848x480";
            case Conference::VideoResolution_854x480:
               return "854x480";
            case Conference::VideoResolution_1280x720:
            default:
               return "1280x720";
            }
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
#endif // __CPCAPI2_VCCS_PARSEUTILS_H__
