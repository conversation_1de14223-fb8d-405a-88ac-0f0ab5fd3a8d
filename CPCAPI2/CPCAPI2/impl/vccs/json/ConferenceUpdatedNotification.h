#pragma once
#ifndef __CPCAPI2_VCCS_CONFERENCEUPDATEDNOTIFICATION_H__
#define __CPCAPI2_VCCS_CONFERENCEUPDATEDNOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>

#include <vccs/VccsConferenceTypes.h>

#include "JSONTokens.h"
#include "ParseUtils.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class ConferenceUpdatedNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         ConferenceUpdatedNotification() {}
         virtual ~ConferenceUpdatedNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_CONFERENCE_UPDATED; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            if( !inDocument.HasMember( CLIENT_CONFERENCE_DETAIL ))
               return false;

            rapidjson::Value const & details = inDocument[ CLIENT_CONFERENCE_DETAIL ];
            if( !ParseUtils::parseConferenceDetails( details, m_ConferenceDetails ))
               return false;

            return true;
         }

         Conference::ConferenceDetails getConferenceDetails()
         {
            return m_ConferenceDetails;
         }
         
      private:
         Conference::ConferenceDetails m_ConferenceDetails;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_CONFERENCEUPDATEDNOTIFICATION_H__