#pragma once
#ifndef __CPCAPI2_VCCS_SETCONFERENCECONFIGURATIONCOMMAND_H__
#define __CPCAPI2_VCCS_SETCONFERENCECONFIGURATIONCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include <stringbuffer.h>
#include <writer.h>

#include "JSONTokens.h"
#include "ParseUtils.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class SetConferenceConfigurationCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         Conference::VccsConferenceHandle m_hConference;
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;

         SetConferenceConfigurationCommand( const Conference::VccsConferenceHandle& hConference, const Conference::ConferenceConfiguration& config, const Conference::ConferenceConfigurationSet& configSet )
            : m_hConference( hConference ), m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ), m_Config( config ), m_ConfigSet( configSet ) {}
         virtual ~SetConferenceConfigurationCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SET_CONFERENCE_CONFIG; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         //{
         //	"cmd": "SET_CONFERENCE_CONFIGURATION_CMD",
         //	"requestID": {integer},
         //	"conferenceID": {integer},
         //
         //	"participantPin": "{pin}",
         //	"moderatorPin": "{pin}",
         //	"defaultVideoLayout": "{layout}",
         //	"isModerated": {boolean},
         //	"nomoderatorOverrun": {integer: (minutes) 0, 1, 5, 10, 15, or 30 },
         //	"dropboxAccessToken": "{token}"
         // "sendSummaryEmail": (boolean),
         // "recordAudioOnly": (boolean),
         // "autoRecord": (boolean),
         // "joinMuted": (boolean)
         //}
         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // { "cmd":"GET_CONFERENCE_DETAIL", "requestID":5, "conferenceID": 7 }
            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,       getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,    m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID, m_hConference);

            if( m_ConfigSet.m_ParticipantPin )
               CPCAPI2::Json::Write(writer, CLIENT_PARTICIPANT_PIN, m_Config.m_ParticipantPin);

            if( m_ConfigSet.m_ModeratorPin )
               CPCAPI2::Json::Write(writer, CLIENT_MODERATOR_PIN, m_Config.m_ModeratorPin);

            if( m_ConfigSet.m_VideoLayout )
               CPCAPI2::Json::Write(writer, "defaultVideoLayout", ParseUtils::videoLayoutToString( m_Config.m_VideoLayout ));

            if( m_ConfigSet.m_IsModerated )
               CPCAPI2::Json::Write(writer, "isModerated", m_Config.m_IsModerated);

            if( m_ConfigSet.m_NoModeratorOverrunMinutes )
            {
               CPCAPI2::Json::Write(writer, "nomoderatorOverrun", m_Config.m_NoModeratorOverrunMinutes);
            }

            if( m_ConfigSet.m_DropBoxToken )
               CPCAPI2::Json::Write(writer, "dropboxAccessToken", m_Config.m_DropBoxToken);

            if( m_ConfigSet.m_SendSummaryEmail )
               CPCAPI2::Json::Write(writer, "sendSummaryEmail", m_Config.m_SendSummaryEmail);

            if( m_ConfigSet.m_RecordAudioOnly )
               CPCAPI2::Json::Write(writer, "recordAudioOnly", m_Config.m_RecordAudioOnly);

            if( m_ConfigSet.m_AutoRecord )
               CPCAPI2::Json::Write(writer, "autoRecord", m_Config.m_AutoRecord);

            if( m_ConfigSet.m_JoinMuted )
               CPCAPI2::Json::Write(writer, CLIENT_JOIN_MUTED, m_Config.m_JoinMuted );

            writer.EndObject();
            return true;
         }

      private:
         Conference::ConferenceConfiguration m_Config;
         Conference::ConferenceConfigurationSet m_ConfigSet;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SETCONFERENCECONFIGURATIONCOMMAND_H__
