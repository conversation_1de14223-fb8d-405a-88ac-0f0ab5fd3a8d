#pragma once
#ifndef __CPCAPI2_VCCS_SETCONFERENCEMODECOMMAND_H__
#define __CPCAPI2_VCCS_SETCONFERENCEMODECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <assert.h>
#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class SetConferenceModeCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:

         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         Conference::VccsConferenceHandle m_hConference;

         typedef enum Mask
         {
            Mask_ParticipantLock    = 0x001,
            Mask_MuteLock           = 0x002,
            Mask_Hosted             = 0x004,
            Mask_MuteAll            = 0x008,
            Mask_UnmuteAll          = 0x010,
            Mask_EntryExitTones     = 0x020,
            Mask_Recording          = 0x040,
            Mask_AudioOnlyRecording = 0x080,
            Mask_JoinMuted          = 0x100
         } Mask_t;

         bool    m_ParticipantLock;
         bool    m_MuteLock;
         bool    m_Hosted;
         bool    m_MuteAll;
         bool    m_EntryExitTonesEnabled;
         bool    m_Recording;
         bool    m_AudioOnlyRecording;
         bool    m_JoinMuted;
         int32_t m_Bitmask;

         SetConferenceModeCommand(
            const Conference::VccsConferenceHandle& hConference,
            bool participantLock,
            bool muteLock,
            bool hosted,
            bool muteAll,
            bool entryExitTonesEnabled,
            bool recording,
            bool audioOnlyRecording,
            bool joinMuted,
            int32_t bitmask ) :
            m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
            m_hConference( hConference ),
            m_ParticipantLock( participantLock ),
            m_MuteLock( muteLock ),
            m_Hosted( hosted ),
            m_MuteAll( muteAll ),
            m_EntryExitTonesEnabled( entryExitTonesEnabled ),
            m_Recording( recording ),
            m_AudioOnlyRecording( audioOnlyRecording ),
            m_JoinMuted( joinMuted ),
            m_Bitmask( bitmask ) {}
         virtual ~SetConferenceModeCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SET_CONFERENCE_MODE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,       getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,    m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID, m_hConference);

            // Only set each flag if the bitmask is set
            if(( m_Bitmask & Mask_ParticipantLock ) != 0 )
               CPCAPI2::Json::Write(writer, "participantLock", m_ParticipantLock);
            if(( m_Bitmask & Mask_MuteLock ) != 0 )
               CPCAPI2::Json::Write(writer, "muteLock", m_MuteLock);
            if(( m_Bitmask & Mask_Hosted ) != 0 )
               CPCAPI2::Json::Write(writer, "hosted", m_Hosted);
            if( m_MuteAll && (( m_Bitmask & Mask_MuteAll ) != 0 ))
               CPCAPI2::Json::Write(writer, "muteAll", true);
            if( !m_MuteAll && (( m_Bitmask & Mask_UnmuteAll ) != 0 ))
               CPCAPI2::Json::Write(writer, "unmuteAll", true);
            if(( m_Bitmask & Mask_EntryExitTones ) != 0 )
            {
               CPCAPI2::Json::Write(writer, "entryTone", m_EntryExitTonesEnabled);
               CPCAPI2::Json::Write(writer, "exitTone", m_EntryExitTonesEnabled);
            }
            if(( m_Bitmask & Mask_Recording ) != 0 )
               CPCAPI2::Json::Write(writer, "recording", m_Recording);
            if(( m_Bitmask & Mask_AudioOnlyRecording ) != 0 )
               CPCAPI2::Json::Write(writer, "audioOnlyRecording", m_AudioOnlyRecording);
            if(( m_Bitmask & Mask_JoinMuted ) != 0 )
               CPCAPI2::Json::Write(writer, CLIENT_JOIN_MUTED, m_JoinMuted);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SETCONFERENCEMODECOMMAND_H__
