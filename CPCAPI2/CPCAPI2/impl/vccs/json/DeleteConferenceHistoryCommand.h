#pragma once
#ifndef __CPCAPI2_VCCS_DELETECONFERENCEHISTORYCOMMAND_H__
#define __CPCAPI2_VCCS_DELETECONFERENCEHISTORYCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include <stringbuffer.h>
#include <writer.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class DeleteConferenceHistoryCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         Conference::VccsConferenceHandle m_hConference;
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         cpc::vector< Conference::HistoryID > m_HistoryIDs;

         DeleteConferenceHistoryCommand(
            const Conference::VccsConferenceHandle& hConference,
            const cpc::vector< Conference::HistoryID >& historyIDs ) :
              m_hConference( hConference ),
              m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_HistoryIDs( historyIDs ) {}
         virtual ~DeleteConferenceHistoryCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_DELETE_CONFERENCE_HISTORY; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            // { "cmd":"GET_CONFERENCE_CONFIGURATION_RESPONSE", "requestID":5, "conferenceID": 7 }
            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,       getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,    m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID, m_hConference);

            if( !m_HistoryIDs.empty() )
            {
               CPCAPI2::Json::Write(writer, "historyIDs", m_HistoryIDs);
            }

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_DELETECONFERENCEHISTORYCOMMAND_H__
