#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCEINVITERESPONSE_H__
#define __CPCAPI2_VCCS_GETCONFERENCEINVITERESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>
#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceInviteResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetConferenceInviteResponse() :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            m_hConference( CPCAPI2_JSON_NO_REQUEST_ID ) {}

         virtual ~GetConferenceInviteResponse() {}

         const char *getCommandName() const OVER<PERSON><PERSON> { return RESPONSE_NAME_GET_CONFERENCE_INVITE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         Conference::VccsConferenceHandle getConferenceHandle() const { return m_hConference; }
         std::string getHTMLInvite() const { return m_HTMLInvite; }
         std::string getTextInvite() const { return m_TextInvite; }
         std::string getJoinURL() const { return m_JoinURL; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // Fetch the conference ID (mandatory)
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            // Get the INVITES (optional)
            if( inDocument.HasMember( "htmlInvite" ))
               m_HTMLInvite = inDocument[ "htmlInvite" ].GetString();

            // Get the INVITES (optional)
            if( inDocument.HasMember( "textInvite" ))
               m_TextInvite = inDocument[ "textInvite" ].GetString();

            // (optional)
            if( inDocument.HasMember( "joinUrl" ))
               m_JoinURL = inDocument[ "joinUrl" ].GetString();

            return true;
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         std::string m_HTMLInvite;
         std::string m_TextInvite;
         std::string m_JoinURL;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCEINVITERESPONSE_H__
