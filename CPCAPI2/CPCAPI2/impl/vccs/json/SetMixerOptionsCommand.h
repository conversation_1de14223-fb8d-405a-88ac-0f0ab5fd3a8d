#pragma once
#ifndef __CPCAPI2_VCCS_SETMIXEROPTIONSCOMMAND_H__
#define __CPCAPI2_VCCS_SETMIXEROPTIONSCOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <assert.h>
#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "JSONTokens.h"
#include "ParseUtils.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class SetMixerOptionsCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:

         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         const Conference::VccsConferenceHandle m_hConference;
         typedef enum Mask
         {
            Mask_VideoFrameRate = 0x01,
            Mask_VideoFloorParticipant = 0x02,
            Mask_VideoLayout = 0x04,
            Mask_VideoResolution = 0x08
         } Mask_t;

         int32_t m_Bitmask;
         Conference::VccsConferenceParticipantHandle m_hVideoFloorParticipant;
         Conference::VideoLayout m_videoLayout;
         Conference::VideoResolution m_videoResolution;

         SetMixerOptionsCommand(const Conference::VccsConferenceHandle& hConference, Conference::VccsConferenceParticipantHandle hVideoFloorParticipant, Conference::VideoLayout videoLayout, Conference::VideoResolution videoResolution, int32_t bitmask) :
            m_RequestHandle(CPCAPI2_JSON_NO_REQUEST_ID),
            m_hConference(hConference),
            m_hVideoFloorParticipant(hVideoFloorParticipant),
            m_videoLayout(videoLayout),
            m_videoResolution(videoResolution),
            m_Bitmask(bitmask) {}
         virtual ~SetMixerOptionsCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SET_MIXER_OPTIONS; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString(const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND, getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_RequestHandle);
            CPCAPI2::Json::Write(writer, CLIENT_CONFERENCE_ID, m_hConference);

            // Only set each flag if the bitmask is set
            if ((m_Bitmask & Mask_VideoFloorParticipant) != 0)
               CPCAPI2::Json::Write(writer, "videoFloorParticipant", m_hVideoFloorParticipant);
            if ((m_Bitmask & Mask_VideoLayout) != 0)
               CPCAPI2::Json::Write(writer, "videoLayout", ParseUtils::videoLayoutToString(m_videoLayout));
            if ((m_Bitmask & Mask_VideoResolution) != 0)
               CPCAPI2::Json::Write(writer, "videoResolution", ParseUtils::videoResolutionToString(m_videoResolution));

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif //__CPCAPI2_VCCS_SETMIXEROPTIONSCOMMAND_H__
