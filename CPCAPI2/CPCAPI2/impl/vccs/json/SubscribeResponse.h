#pragma once
#ifndef __CPCAPI2_VCCS_SUBSCRIBERESPONSE_H__
#define __CPCAPI2_VCCS_SUBSCRIBERESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>

#include <websocket/json/WebSocketResponse.h>
#include <websocket/WebSocketCommonTypes.h>

#include <vccs/VccsConferenceTypes.h>

#include "JSONTokens.h"
#include "ParseUtils.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class SubscribeResponse :  public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         SubscribeResponse() :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            m_hConference( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~SubscribeResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_SUBSCRIBE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         // Vestigial support for StatusResponse things
         const char *getErrorCode() const { return m_ErrorCode.c_str(); }
         bool isErrorCondition() const { return ( m_ErrorCode != "OK" ); }

         /**
          * Parses the document, and populates the subclass' information
          * (including the request handle)
          */
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            const std::string cmdName( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmdName != getCommandName() )
               return false;

            // {"requestID":1,"errorCode":"OK","cmd":"SUBSCRIBE_RESPONSE"}

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            if( inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            // error code ( error is assumed if not present )
            if( inDocument.HasMember( "errorCode" ))
               m_ErrorCode = inDocument[ "errorCode" ].GetString();

            if( inDocument.HasMember( "directSipAddress" ))
               m_DirectSIPAddress = inDocument[ "directSipAddress" ].GetString();

            if( inDocument.HasMember( "directSipUsername" ))
               m_DirectSIPUsername = inDocument[ "directSipUsername" ].GetString();

            if( inDocument.HasMember( "directSipPassword" ))
               m_DirectSIPPassword = inDocument[ "directSipPassword" ].GetString();

            if( inDocument.HasMember( "directSipWsServer" ))
               m_DirectSIPWsServer = inDocument[ "directSipWsServer" ].GetString();

            if( inDocument.HasMember( "directSipProxy" ))
               m_DirectSIPProxy = inDocument[ "directSipProxy" ].GetString();

            if( inDocument.HasMember( "directSipConnect" ))
            {
               rapidjson::Value& subdoc( inDocument[ "directSipConnect" ] );
               if( subdoc.IsObject() )
               {
                  // stringify this subdocument
                  rapidjson::StringBuffer strbuf( 0, 1024 );
                  rapidjson::Writer<rapidjson::StringBuffer> writer( strbuf );
                  subdoc.Accept( writer );
                  m_DirectSIPProvisioning = strbuf.GetString();
               }
            }
            return true;
         }

         Conference::VccsConferenceHandle getConferenceID() const { return m_hConference; }
         std::string getDirectSIPAddress( void ) const { return m_DirectSIPAddress; }
         std::string getDirectSIPUsername( void ) const { return m_DirectSIPUsername; }
         std::string getDirectSIPPassword( void ) const { return m_DirectSIPPassword; }
         std::string getDirectSIPWebsocketServer( void ) { return m_DirectSIPWsServer; }
         std::string getDirectSIPProxy( void ) const { return m_DirectSIPProxy; }
         std::string getDirectSIPProvisioning( void ) const { return m_DirectSIPProvisioning; }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         std::string m_ErrorCode;

         // deprecated direct config
         std::string m_DirectSIPAddress;
         std::string m_DirectSIPUsername;
         std::string m_DirectSIPPassword;
         std::string m_DirectSIPWsServer;
         std::string m_DirectSIPProxy;

         // Provisioning JSON document
         std::string m_DirectSIPProvisioning;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SUBSCRIBERESPONSE_H__
