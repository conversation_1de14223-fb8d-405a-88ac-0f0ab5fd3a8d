#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCECONNECTIONRESPONSE_H__
#define __CPCAPI2_VCCS_GETCONFERENCECONNECTIONRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/WebSocketCommonTypes.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>

#include "../json/ParseUtils.h"
#include "../VccsInternalTypes.h"

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceConnectionResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetConferenceConnectionResponse()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_hConference( -1 ),
              m_IsPinRequired( false )
         {
         }
         virtual ~GetConferenceConnectionResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_GET_CONFERENCE_CONNECTION; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         Conference::VccsConferenceHandle getConferenceID() const { return m_hConference; }
         std::string getGroup() const { return m_Group; }
         std::string getLobby() const { return m_Lobby; }
         std::string getBridge() const { return m_Bridge; }
         bool isPinRequired() const { return m_IsPinRequired; }

         // {
         //     "cmd":"GET_CONFERENCE_CONNECTION_RESPONSE",
         //     "requestID":"...",
         //     "conferenceID":{integer},
         //     "group":"counterpath.com",
         //     "lobby":"8000",
         //     "bridge":"1001",
         //     "pinRequired":{true|false}
         // }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // Fetch the conference ID (mandatory)
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            if( inDocument.HasMember( CLIENT_GROUP ))
               m_Group = inDocument[ CLIENT_GROUP ].GetString();

            if( inDocument.HasMember( "lobby" ))
               m_Lobby = inDocument[ "lobby" ].GetString();

            if( inDocument.HasMember( CLIENT_BRIDGE ))
               m_Bridge = inDocument[ CLIENT_BRIDGE ].GetString();

            if( inDocument.HasMember( "pinRequired" ))
               m_IsPinRequired = inDocument[ "pinRequired" ].GetBool();

            return true;
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::VccsConferenceHandle m_hConference;
         std::string m_Group;
         std::string m_Lobby;
         std::string m_Bridge;
         bool m_IsPinRequired;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCECONNECTIONRESPONSE_H__
