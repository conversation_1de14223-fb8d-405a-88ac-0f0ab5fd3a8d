#pragma once
#ifndef __CPCAPI2_VCCS_GETCONFERENCEDETAILRESPONSE_H__
#define __CPCAPI2_VCCS_GETCONFERENCEDETAILRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>
#include "../json/ParseUtils.h"

#include "JSONTokens.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class GetConferenceDetailResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         GetConferenceDetailResponse() : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~GetConferenceDetailResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_GET_CONFERENCE_DETAIL; }

         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            if( !inDocument.HasMember( CLIENT_CONFERENCE_DETAIL ))
               return false;

            rapidjson::Value const & details = inDocument[ CLIENT_CONFERENCE_DETAIL ];
            if( !ParseUtils::parseConferenceDetails( details, m_ConferenceDetail ))
               return false;

            return true;
         }

         Conference::ConferenceDetails getConferenceDetail()
         {
            return m_ConferenceDetail;
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         Conference::ConferenceDetails m_ConferenceDetail;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_GETCONFERENCEDETAILRESPONSE_H__
