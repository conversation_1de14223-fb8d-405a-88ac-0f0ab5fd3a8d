#pragma once
#ifndef __CPCAPI2_VCCS_LISTCONFERENCESRESPONSE_H__
#define __CPCAPI2_VCCS_LISTCONFERENCESRESPONSE_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <vector>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketResponse.h>
#include <vccs/VccsConferenceTypes.h>
#include "JSONTokens.h"
#include "ParseUtils.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class ListConferencesResponse : public CPCAPI2::WebSocket::WebSocketResponse
      {
      public:
         ListConferencesResponse() : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~ListConferencesResponse() {}

         const char *getCommandName() const OVERRIDE { return RESPONSE_NAME_LIST_CONFERENCES; }

         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // {"requestID":2,"cmd":"LIST_CONFERENCES_RESPONSE","conferences":[{"nomoderatorOverrun":0,"supportVideo":true,"lobbySipAddress":"sip:+<EMAIL>","created":1445608010252,"description":"Description","title":"Conference Title","bridgeNumber":"1234","supportAudio":true,"sipAddress":"sip:+<EMAIL>","recordNames":false,"supportMessaging":true,"supportScreensharing":true,"id":1}]}
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            rapidjson::Value const & array = inDocument[CLIENT_CONFERENCES_LIST];
            rapidjson::SizeType size = array.Size();
            m_ConferenceDetails.clear();

            // TODO: put constant strings in macros or const char definitions
            for(rapidjson::SizeType i=0;i<size;i++)
            {
               rapidjson::Value const & listItem = array[i];

               Conference::ConferenceDetails item;
               if( !ParseUtils::parseConferenceDetails( listItem, item ))
                  continue;

               m_ConferenceDetails.push_back(item);
            }

            return true;
         }

         std::vector< Conference::ConferenceDetails > getConferenceList()
         {
            return m_ConferenceDetails; // rather inefficient
         }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         std::vector< Conference::ConferenceDetails > m_ConferenceDetails;
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_LISTCONFERENCESRESPONSE_H__
