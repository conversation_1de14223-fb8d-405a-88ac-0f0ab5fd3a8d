#pragma once
#ifndef __CPCAPI2_VCCS_SETCONFERENCEMODENOTIFICATION_H__
#define __CPCAPI2_VCCS_SETCONFERENCEMODENOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <assert.h>
#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>

#include "JSONTokens.h"
#include "../VccsInternalTypes.h"

namespace CPCAPI2
{
   namespace VCCS
   {
      class SetConferenceModeNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:

         Conference::VccsConferenceHandle m_hConference;

         typedef enum Mask
         {
            Mask_ParticipantLock    = 0x01,
            Mask_MuteLock           = 0x02,
            Mask_Hosted             = 0x04,
            Mask_MuteAll            = 0x08,
            Mask_UnmuteAll          = 0x10,
            Mask_EntryExitTones     = 0x20,
            Mask_Recording          = 0x40,
            Mask_AudioOnlyRecording = 0x80
         } Mask_t;

         int32_t m_Bitmask;
         bool    m_ParticipantLock;
         bool    m_MuteLock;
         bool    m_Hosted;
         bool    m_MuteAll;
         bool    m_EntryExitTonesEnabled;
         bool    m_Recording;
         bool    m_AudioOnlyRecording;

         SetConferenceModeNotification() :
            m_hConference( -1 ),
            m_Bitmask( 0 ),
            m_ParticipantLock( false ),
            m_MuteLock( false ),
            m_Hosted( false ),
            m_MuteAll( false ),
            m_EntryExitTonesEnabled( false ),
            m_Recording( false ),
            m_AudioOnlyRecording( false ) {}

         virtual ~SetConferenceModeNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_SET_CONFERENCE_MODE; }
         Conference::VccsConferenceHandle getConferenceID() const { return m_hConference; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // cmd is mandatory
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            // conferenceID is mandatory
            if( !inDocument.HasMember( CLIENT_CONFERENCE_ID ))
               return false;
            m_hConference = inDocument[ CLIENT_CONFERENCE_ID ].GetInt();

            if( inDocument.HasMember( "participantLock" ))
            {
               m_Bitmask |= Mask_ParticipantLock;
               m_ParticipantLock = inDocument[ "participantLock" ].GetBool();
            }

            if( inDocument.HasMember( "muteLock" ))
            {
               m_Bitmask |= Mask_MuteLock;
               m_MuteLock = inDocument[ "muteLock" ].GetBool();
            }

            if( inDocument.HasMember( "hosted" ))
            {
               m_Bitmask |= Mask_Hosted;
               m_Hosted = inDocument[ "hosted" ].GetBool();
            }

            if( inDocument.HasMember( "muteAll" ))
            {
               m_Bitmask |= Mask_MuteAll;
               m_MuteAll = inDocument[ "muteAll" ].GetBool();
            }
            else if( inDocument.HasMember( "unmuteAll" ))
            {
               m_Bitmask |= Mask_UnmuteAll;
               m_MuteAll = inDocument[ "unmuteAll" ].GetBool();
            }

            bool entryToneEnabled( false );
            if( inDocument.HasMember( "entryTone" ))
            {
               m_Bitmask |= Mask_EntryExitTones;
               entryToneEnabled = inDocument[ "entryTone" ].GetBool();
            }

            bool exitToneEnabled( false );
            if( inDocument.HasMember( "exitTone" ))
            {
               m_Bitmask |= Mask_EntryExitTones;
               exitToneEnabled = inDocument[ "exitTone" ].GetBool();
            }

            m_EntryExitTonesEnabled = entryToneEnabled || exitToneEnabled;

            if( inDocument.HasMember( "recording" ))
            {
               m_Bitmask |= Mask_Recording;
               m_Recording = inDocument[ "recording" ].GetBool();
            }

            if( inDocument.HasMember( "audioOnlyRecording" ))
            {
               m_Bitmask |= Mask_Recording;
               m_Recording = inDocument[ "audioOnlyRecording" ].GetBool();
            }

            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE == 1
#endif // __CPCAPI2_VCCS_SETCONFERENCEMODENOTIFICATION_H__
