#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::VCCS

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include <util/statemachine/AbstractState.h>

#include <websocket/WebSocketState.h>
#include <websocket/WebSocketStateMachine.h>
#include <websocket/json/StatusResponse.h>
#include <websocket/states/StateConnected.h>
#include <websocket/states/StateConnecting.h>
#include <websocket/states/StateDisconnected.h>
#include <websocket/states/StateFailed.h>
#include <websocket/states/StateSuspended.h>

#include <vccs/VccsAccountTypes.h>

#include "VccsConferenceManagerImpl.h"
#include "VccsAccountManagerImpl.h"

#include "json/LoginCommand.h"
#include "json/ListConferencesCommand.h"
#include "json/SubscribeCommand.h"
#include "json/SubscribeResponse.h"
#include "json/UnsubscribeCommand.h"
#include "json/MuteParticipantCommand.h"
#include "json/UnmuteParticipantCommand.h"
#include "json/KickParticipantCommand.h"
#include "json/GetXMPPConnectionCommand.h"
#include "json/GetXMPPConnectionResponse.h"
#include "json/SetConferenceModeCommand.h"
#include "json/GetConferenceDetailCommand.h"
#include "json/GetConferenceDetailResponse.h"
#include "json/GetConferenceInviteCommand.h"
#include "json/GetConferenceInviteResponse.h"
#include "json/ScreenShareUpdateCommand.h"
#include "json/ParticipantIsRecordingCommand.h"
#include "json/ParticipantNotRecordingCommand.h"
#include "json/GetConferenceConfigurationCommand.h"
#include "json/GetConferenceConfigurationResponse.h"
#include "json/SetConferenceConfigurationCommand.h"
#include "json/GetConferenceHistoryCommand.h"
#include "json/GetConferenceHistoryResponse.h"
#include "json/DeleteConferenceHistoryCommand.h"
#include "json/GetConferenceConnectionCommand.h"
#include "json/GetConferenceConnectionResponse.h"

#include "json/ConferenceUpdatedNotification.h"
#include "json/ListConferencesResponse.h"
#include "json/ParticipantUpdatedNotification.h"
#include "json/ScreenShareUpdateNotification.h"
#include "json/SetMixerOptionsCommand.h"
#include "json/VoiceActivityNotification.h"
#include "json/SetConferenceModeNotification.h"

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

using namespace CPCAPI2::VCCS::Conference;
using CPCAPI2::AbstractStatePtr;
using CPCAPI2::AbstractStateReasonCode;
using CPCAPI2::WebSocket::WebSocketState;
using CPCAPI2::WebSocket::WebSocketStatePtr;
using CPCAPI2::VCCS::Account::VccsAccountHandle;
using CPCAPI2::VCCS::Account::VccsAccountHandler;
using CPCAPI2::VCCS::Account::AccountInfo;
using CPCAPI2::VCCS::Account::VccsAccountManagerImpl;
using CPCAPI2::VCCS::Conference::VccsConferenceHandler;

VccsConferenceManagerImpl::VccsConferenceManagerImpl( VccsAccountManagerImpl* pAccountMgr )
   : m_pAccountMgr( pAccountMgr ), m_Handler( NULL ), m_LastSubscriptioninfo(NULL)
{
}

VccsConferenceManagerImpl::~VccsConferenceManagerImpl()
{
   WebSocket::WebSocketStateMachine *pSM( NULL );
   pSM = m_pAccountMgr->getStateMachine();
   if( pSM != NULL )
      pSM->removeListener( this );
   if (NULL != m_LastSubscriptioninfo)
   {
      delete m_LastSubscriptioninfo;
      m_LastSubscriptioninfo = NULL;
   }
}

void VccsConferenceManagerImpl::Release()
{
}

int VccsConferenceManagerImpl::setHandler(VccsConferenceHandler* handler)
{
   if( m_Handler != NULL && handler != NULL )
      return kError;

   m_Handler = handler;
   return kSuccess;
}

int VccsConferenceManagerImpl::getConferenceConnectionInfo( const cpc::string& conferenceCode )
{
   GetConferenceConnectionCommand command( conferenceCode.c_str() );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onConferenceConnectionInfoFailure ), m_pAccountMgr->getAccountHandle(), evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::subscribe( const SubscriptionInfo& info )
{
   const Account::VccsAccountSettings& settings( m_pAccountMgr->getSettings() );
   std::string errMessage;
   SubscribeCommand command( settings.group.c_str(), settings.displayName.c_str(), settings.xmppUserName.c_str(), info );
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onSubscribeFailure ), m_pAccountMgr->getAccountHandle(), evt );
      return kError;
   }

   if (NULL == m_LastSubscriptioninfo) m_LastSubscriptioninfo = new SubscriptionInfoEx();
   m_LastSubscriptioninfo->subscriptionInfo = info;

   return kSuccess;
}

int VccsConferenceManagerImpl::unsubscribe(const cpc::string& bridge )
{
   const Account::VccsAccountSettings& settings( m_pAccountMgr->getSettings() );
   UnsubscribeCommand command( settings.group.c_str(), bridge.c_str() );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onUnsubscribeFailure ), m_pAccountMgr->getAccountHandle(), evt );
      return kError;
   }

   if (NULL != m_LastSubscriptioninfo)
   {
      if (m_LastSubscriptioninfo->subscriptionInfo.bridge == bridge || m_LastSubscriptioninfo->subscriptionInfo.conferenceCode == bridge)
      {
         delete m_LastSubscriptioninfo;
         m_LastSubscriptioninfo = NULL;
      }
      else if (m_LastSubscriptioninfo->hConference != 0)
      {
         ConferenceMap_t::iterator iter(m_ConfMap.find(m_LastSubscriptioninfo->hConference));
         if (iter != m_ConfMap.end() && iter->second.mPubConferenceDetails->bridgeNumber == bridge)
         {
            delete m_LastSubscriptioninfo;
            m_LastSubscriptioninfo = NULL;
         }
      }
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::queryConferenceList()
{
   ListConferencesCommand command( true ); // ?
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceListFailure ), m_pAccountMgr->getAccountHandle(), evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::queryConferenceDetails(VccsConferenceHandle hConference)
{
   GetConferenceDetailCommand command( hConference );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceListFailure ), m_pAccountMgr->getAccountHandle(), evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::queryConferenceInvite(VccsConferenceHandle hConference)
{
   GetConferenceInviteCommand command( hConference );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceInviteFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setParticipantLock(VccsConferenceHandle hConference, bool locked)
{
   SetConferenceModeCommand command( hConference, locked, false, false, false, false, false, false, false, SetConferenceModeCommand::Mask_ParticipantLock );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceModeFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setMuteLock(VccsConferenceHandle hConference, bool locked)
{
   SetConferenceModeCommand command( hConference, false, locked, false, false, false, false, false, false, SetConferenceModeCommand::Mask_MuteLock);
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceModeFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setHosted(VccsConferenceHandle hConference, bool hosted)
{
   SetConferenceModeCommand command( hConference, false, false, hosted, false, false, false, false, false, SetConferenceModeCommand::Mask_Hosted );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceModeFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setMuteOthers(VccsConferenceHandle hConference, bool mute)
{
   SetConferenceModeCommand command( hConference, false, false, false, mute, false, false, false, false, mute ? SetConferenceModeCommand::Mask_MuteAll : SetConferenceModeCommand::Mask_UnmuteAll );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceModeFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setEntryExitTonesEnabled(VccsConferenceHandle hConference, bool enabled)
{
   SetConferenceModeCommand command( hConference, false, false, false, false, enabled, false, false, false, SetConferenceModeCommand::Mask_EntryExitTones );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onSetConferenceModeFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setRecording(VccsConferenceHandle hConference, bool enable)
{
   SetConferenceModeCommand command( hConference, false, false, false, false, false, enable, false, false, SetConferenceModeCommand::Mask_Recording );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onSetConferenceModeFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setAudioOnlyRecording(VccsConferenceHandle hConference, bool enable)
{
   SetConferenceModeCommand command( hConference, false, false, false, false, false, false, enable, false, SetConferenceModeCommand::Mask_AudioOnlyRecording );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onSetConferenceModeFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setJoinMuted(VccsConferenceHandle hConference, bool enable)
{
   SetConferenceModeCommand command( hConference, false, false, false, false, false, false, false, enable, SetConferenceModeCommand::Mask_JoinMuted );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onSetConferenceModeFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::kickParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant)
{
   KickParticipantCommand command( hConference, hParticipant );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onKickParticipantFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::muteParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute)
{
   CPCAPI2::WebSocket::WebSocketCommand *command = NULL;
   if( mute )
      command = new MuteParticipantCommand( hConference, hParticipant );
   else
      command = new UnmuteParticipantCommand( hConference, hParticipant );

   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), *command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onMuteParticipantFailure ), hConference, evt );
      delete command;
      return kError;
   }

   delete command;
   return kSuccess;
}

int VccsConferenceManagerImpl::setIsRecording(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording)
{
   CPCAPI2::WebSocket::WebSocketCommand *command = NULL;
   if( recording )
      command = new ParticipantIsRecordingCommand( hConference, hParticipant );
   else
      command = new ParticipantNotRecordingCommand( hConference, hParticipant );

   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), *command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onSetIsRecordingFailure ), hConference, evt );
      delete command;
      return kError;
   }

   delete command;
   return kSuccess;
}

int VccsConferenceManagerImpl::getXMPPAccountInfo(VccsConferenceHandle hConference, bool guestAccount )
{
   GetXMPPConnectionCommand command( hConference, guestAccount );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent( cpcFunc( VccsConferenceHandler::onXMPPAccountInfoFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setScreenSharePresenter(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant )
{
   ScreenShareUpdateCommand command( hConference, hParticipant );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onScreenShareCommandFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::startScreenShare(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant,const cpc::string& screenSharingURL,const ScreenSharingInfoList& infoList )
{
   ScreenShareUpdateCommand command( hConference, hParticipant, true, screenSharingURL.c_str(), infoList );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onScreenShareCommandFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::stopScreenShare(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant )
{
   const ScreenSharingInfoList infoList; // empty data
   ScreenShareUpdateCommand command( hConference, hParticipant, false, "", infoList );
   std::string errMessage;
   if( !m_pAccountMgr->getStateMachine()->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage ))
   {
      Conference::ParticipantFailureEvent evt;
      evt.reasonText   = errMessage.c_str();
      evt.errorCode    = ErrorCode_LocallyGenerated;
      evt.hParticipant = hParticipant;
      fireEvent( cpcFunc( VccsConferenceHandler::onScreenShareCommandFailure ), hConference, evt );
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setVideoFloorParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant)
{
   SetMixerOptionsCommand command(hConference, hParticipant, Conference::VideoLayout_Grid, Conference::VideoResolution_640x400, SetMixerOptionsCommand::Mask_VideoFloorParticipant);
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onMixerOptionsCommandFailure), m_pAccountMgr->getAccountHandle(), evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setVideoLayout(VccsConferenceHandle hConference, VideoLayout videoLayoutType)
{
   SetMixerOptionsCommand command(hConference, 0, videoLayoutType, Conference::VideoResolution_640x400, SetMixerOptionsCommand::Mask_VideoLayout);
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onMixerOptionsCommandFailure), m_pAccountMgr->getAccountHandle(), evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setVideoResolution(VccsConferenceHandle hConference, VideoResolution videoResolutionType)
{
   SetMixerOptionsCommand command(hConference, 0, Conference::VideoLayout_Grid, videoResolutionType, SetMixerOptionsCommand::Mask_VideoResolution);
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onMixerOptionsCommandFailure), m_pAccountMgr->getAccountHandle(), evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::queryConferenceConfig( VccsConferenceHandle hConference )
{
   GetConferenceConfigurationCommand command( hConference );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onQueryConferenceConfigFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::setConferenceConfig(
   VccsConferenceHandle hConference,
   const ConferenceConfiguration & conferenceConfig,
   const ConferenceConfigurationSet & conferenceConfigSet )
{
   SetConferenceConfigurationCommand command( hConference, conferenceConfig, conferenceConfigSet );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onSetConferenceConfigFailure), hConference, evt);
      return kError;
   }

   return kSuccess;
}

int VccsConferenceManagerImpl::queryConferenceHistory(
   VccsConferenceHandle hConference,
   int offset,
   int count,
   bool includeParticipants,
   HistoryID historyID )
{
   GetConferenceHistoryCommand command( hConference, offset, count, includeParticipants, historyID );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onQueryConferenceHistoryFailure), hConference, evt);
      return kError;
   }
   return kSuccess;
}

int VccsConferenceManagerImpl::deleteHistory(
   VccsConferenceHandle hConference,
   const cpc::vector< HistoryID >& historyIDs )
{
   DeleteConferenceHistoryCommand command( hConference, historyIDs );
   std::string errMessage;
   if (!m_pAccountMgr->getStateMachine()->sendCommand(m_pAccountMgr->nextRequestHandle(), command, errMessage))
   {
      Conference::ConferenceFailureEvent evt;
      evt.reasonText = errMessage.c_str();
      evt.errorCode  = ErrorCode_LocallyGenerated;
      fireEvent(cpcFunc(VccsConferenceHandler::onDeleteConferenceHistoryFailure), hConference, evt);
      return kError;
   }
   return kSuccess;
}

bool VccsConferenceManagerImpl::setCache( std::vector< ConferenceDetails >& details )
{
   clearCache();

   for( std::vector< ConferenceDetails >::size_type i = 0 ; i < details.size() ; ++i )
   {
      Conference::ConferenceDetails& detail( details[ i ] );
      m_ConfMap[ detail.id ].mPubConferenceDetails = std::shared_ptr< ConferenceDetails >( new ConferenceDetails( detail ));
   }

   // Fire an event of Query type (as setting the cache only ever results from that)
   return true;
}

bool VccsConferenceManagerImpl::updateCache(
   const VccsConferenceHandle& hConference,
   const ConferenceDetails& details,
   bool fromQuery )
{
   ConferenceMap_t::iterator iter( m_ConfMap.find( hConference ));
   if( iter == m_ConfMap.end() )
   {
      // This is an addition to the cache
      m_ConfMap[ hConference ].mPubConferenceDetails = std::shared_ptr< ConferenceDetails >( new ConferenceDetails( details ));

      // Fire a conference added event
      ConferenceListUpdatedEvent evt;
      ConferenceListChange change;
      change.changeType = fromQuery ? ChangeType_Query : ChangeType_Add;
      change.conference = details;
      evt.changes.push_back( change );
      fireEvent( cpcFunc( VccsConferenceHandler::onConferenceListUpdated ), m_pAccountMgr->m_hAccount, evt );
   }
   else
   {
      std::shared_ptr< ConferenceDetails > oldDetails( iter->second.mPubConferenceDetails );
      iter->second.mPubConferenceDetails.reset( new Conference::ConferenceDetails( details ));

      // Fire a conference updated event
      ConferenceListUpdatedEvent evt;
      ConferenceListChange change;
      change.changeType = fromQuery ? ChangeType_Query : ChangeType_Update;
      change.conference = details;
      evt.changes.push_back( change );
      fireEvent( cpcFunc( VccsConferenceHandler::onConferenceListUpdated ), m_pAccountMgr->m_hAccount, evt );
   }

   return true;
}

bool VccsConferenceManagerImpl::updateCache(
   const VccsConferenceHandle& hConference,
   const VccsConferenceParticipantHandle& hParticipant,
   const ParticipantStatus& details )
{
   if( hParticipant == -1 || hConference == -1 )
      return false;

   ConferenceMap_t::iterator iter( m_ConfMap.find( hConference ));
   if( iter == m_ConfMap.end() )
   {
      // This represents an addition to the conference details. That shouldn't
      // happen when only updating the participant status, as we don't have
      // all the information.
      //
      // NB: This is actually happening on the VCCS (though it shouldn't).
      // Sometimes the participant update comes before the conference update.
      // Since the conference update also contains all the known participant
      // information, we can just ignore this condition, but log the problem.
      //assert( 0 && "Can't find the conference details for this participant" );
      WarningLog( << "Received update for participant: " << hParticipant
                  << " in conference: " << hConference
                  << ". But, we haven't seen conference " << hConference );
      return false;
   }
   else
   {
      // This is an update to the cache. Try to find the matching participant info.
      std::shared_ptr< ConferenceDetails > pCachedConferenceDetails( iter->second.mPubConferenceDetails );
      if( pCachedConferenceDetails.get() == NULL )
         return false;
      
      ConferenceDetailsInternal& confDetailsInternal = iter->second;
      if (confDetailsInternal.mParticipants.find(hParticipant) == confDetailsInternal.mParticipants.end())
      {
         confDetailsInternal.mParticipants[hParticipant] = details;

         // fire a participant added event.
         ParticipantListUpdatedEvent evt;
         ParticipantListChange change;
         change.changeType = ChangeType_Add;
         change.participant = details;
         evt.changes.push_back( change );
         DebugLog(<< "Firing VccsConferenceHandler::onParticipantListUpdated for added participant " << details.displayName);
         fireEvent( cpcFunc( VccsConferenceHandler::onParticipantListUpdated ), hConference, evt );
      }
      else
      {
         confDetailsInternal.mParticipants[hParticipant] = details;

         // Fire a participant updated event.
         ParticipantListUpdatedEvent evt;
         ParticipantListChange change;
         change.changeType = ChangeType_Update;
         change.participant = details;
         evt.changes.push_back( change );
         DebugLog(<< "Firing VccsConferenceHandler::onParticipantListUpdated for updated participant " << details.displayName);
         fireEvent( cpcFunc( VccsConferenceHandler::onParticipantListUpdated ), hConference, evt );
      }
   }

   return true;
}

bool VccsConferenceManagerImpl::clearCache()
{
   m_ConfMap.clear();
   return true;
}

ErrorCode VccsConferenceManagerImpl::toErrorCode( const char * stringVal ) const
{
   if( stringVal != NULL )
   {
      if( strcmp( "UNKNOWN_COMMAND", stringVal ) == 0 )
         return ErrorCode_UnknownCommand;
      else if( strcmp( "UNAUTHENTICATED", stringVal ) == 0 )
         return ErrorCode_Unauthenticated;
      else if( strcmp( "LOGIN_FAILED", stringVal ) == 0 )
         return ErrorCode_LoginFailed;
      else if( strcmp( "LOCKED_OUT", stringVal ) == 0 )
         return ErrorCode_LockedOut;
      else if( strcmp( "SERVER_UNEXPECTED_ERROR", stringVal ) == 0 )
         return ErrorCode_UnexpectedError;
      else if( strcmp( "SERVER_CONFIGURATION_ERROR", stringVal ) == 0 )
         return ErrorCode_ConfigurationError;
      else if( strcmp( "SERVER_DATABASE_PERSIST_ERROR", stringVal ) == 0 )
         return ErrorCode_DatabasePersistError;
      else if( strcmp( "INVALID_REQUEST", stringVal ) == 0 )
         return ErrorCode_InvalidRequest;
      else if( strcmp( "MODERATOR_ONLY", stringVal ) == 0 )
         return ErrorCode_ModeratorOnly;
      else if( strcmp( "CONFERENCE_NOT_FOUND", stringVal ) == 0 )
         return ErrorCode_ConferenceNotFound;
      else if( strcmp( "CONFERENCE_NOT_ACTIVE", stringVal ) == 0 )
         return ErrorCode_ConferenceNotActive;
      else if( strcmp( "NO_BRIDGES_AVAILABLE", stringVal ) == 0 )
         return ErrorCode_NoBridgesAvailable;
      else if( strcmp( "PARTICIPANT_NOT_FOUND", stringVal ) == 0 )
         return ErrorCode_ParticipantNotFound;
      else if( strcmp( "PIN_REQUIRED", stringVal ) == 0 )
         return ErrorCode_PinRequired;
      else if( strcmp( "CONFERENCE_LOCKED", stringVal ) == 0 )
         return ErrorCode_ConferenceLocked;
      else if( strcmp( "TOO_MANY_PARTICIPANTS", stringVal ) == 0 )
         return ErrorCode_TooManyParticipants;
   }

   return ErrorCode_Unknown;
}

static bool StateToEnum( const CPCAPI2::AbstractStatePtr state, CPCAPI2::VCCS::Account::VccsAccountState& outEnum )
{
   if( state == NULL )
      return false;

   const char *stateID = state->getUniqueID();
   if( strcmp( stateID, STATE_CONNECTED_ID ) == 0 )
      outEnum = CPCAPI2::VCCS::Account::VccsAccountState_Registered;
   else if( strcmp( stateID, STATE_CONNECTING_ID ) == 0 )
      outEnum = CPCAPI2::VCCS::Account::VccsAccountState_Registering;
   else if( strcmp( stateID, STATE_DISCONNECTED_ID ) == 0 )
      outEnum = CPCAPI2::VCCS::Account::VccsAccountState_Unregistered;
   else if( strcmp( stateID, STATE_FAILED_ID ) == 0 )
      outEnum = CPCAPI2::VCCS::Account::VccsAccountState_WaitingToRegister;
   else if( strcmp( stateID, STATE_SUSPENDED_ID ) == 0 )
      outEnum = CPCAPI2::VCCS::Account::VccsAccountState_Suspended;
   else
      return false;

   return true;
}

void VccsConferenceManagerImpl::onStateChange( const AbstractStatePtr oldState, const AbstractStatePtr newState, const AbstractStateReasonCode reason )
{
   CPCAPI2::VCCS::Account::VccsAccountStateChangedEvent evt;
   evt.reason = ( CPCAPI2::VCCS::Account::VccsStateReason ) reason; // bit dodgy but should work

   if( !StateToEnum( oldState, evt.oldState ) ||
       !StateToEnum( newState, evt.newState ))
       return;

   m_pAccountMgr->fireEvent( cpcFunc( VccsAccountHandler::onAccountStateChanged ), evt );
}

void VccsConferenceManagerImpl::onLogin( void )
{
   // Check the settings to see whether or not there is enough information
   // to perform a login (the login step is optional).
   if( !m_pAccountMgr->m_Settings.userName.empty() )
   {
      DebugLog(<< "Initiating Login to VCCS" );
      LoginCommand command(
         m_pAccountMgr->m_Settings.group.c_str(),
         m_pAccountMgr->m_Settings.userName.c_str(),
         m_pAccountMgr->m_Settings.password.c_str(),
         m_pAccountMgr->m_Settings.displayName.c_str(),
         m_pAccountMgr->m_Settings.xmppUserName.c_str() );

      std::string errMessage;
      if( !m_pAccountMgr->m_StateMachine->sendCommand( m_pAccountMgr->nextRequestHandle(), command, errMessage, true ))
      {
         Account::ErrorEvent evt;
         evt.errorText = errMessage.c_str();
         m_pAccountMgr->fireEvent( cpcFunc( VccsAccountHandler::onError ), evt );
      }
   }
}

void VccsConferenceManagerImpl::onLoginResponse( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse response;
   if (response.fromString( inString ))
   {
      if (response.isErrorCondition())
      {
         // Login failure from the server, treat this as a failure
         ErrLog( << "StateConnecting: Couldn't login to the server, server responded with error: " << response.getErrorCode() );

         // Depending on the type of failure, we will either retry, or go back
         // to disconnected state potentially.
         m_pAccountMgr->m_StateMachine->setCurrentState( STATE_FAILED_ID );
      }
      else
      {
         // Login successful proceed to Registered state
         DebugLog( << "StateConnecting: Login successful" );
         m_pAccountMgr->m_StateMachine->setCurrentState( STATE_CONNECTED_ID );
         if (m_pAccountMgr->m_SubscribeAfterReconnect)
         {
            m_pAccountMgr->m_SubscribeAfterReconnect = false;
            if (NULL != m_LastSubscriptioninfo)
            {
               DebugLog(<< "Sending SUBSCRIBE after network change...");
               subscribe(m_LastSubscriptioninfo->subscriptionInfo);
            }
            else
               DebugLog(<< "No subscription info available, skipping SUBSCRIBE after network change...");
         }
      }
   }
   else
   {
      ErrLog( << "StateConnecting: Failed to parse message" );
      return;
   }
}

void VccsConferenceManagerImpl::onMessage( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse sResponse;
   if( sResponse.fromString( inString ))
   {
      std::string origCmd( sResponse.getOriginalCommandName() );

      // NB: these are optional (only valid in certain contexts) and could be -1
      VccsConferenceHandle hConference = -1;
      VccsConferenceParticipantHandle hParticipant = -1;
      sResponse.getExtensionAttribute( CLIENT_CONFERENCE_ID, hConference );
      sResponse.getExtensionAttribute( CLIENT_PARTICIPANT_NUMBER, hParticipant );

      if( origCmd == COMMAND_NAME_SUBSCRIBE )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onSubscribeFailure ), m_pAccountMgr->getAccountHandle(), evt );
         }
      }
      else if( origCmd == COMMAND_NAME_UNSUBSCRIBE )
      {
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onUnsubscribeFailure ), m_pAccountMgr->getAccountHandle(), evt );
         }
         else
         {
            Conference::UnsubscribeEvent evt;
            fireEvent( cpcFunc( VccsConferenceHandler::onUnsubscribe ), m_pAccountMgr->getAccountHandle(), evt );
         }
      }
      else if( origCmd == COMMAND_NAME_SET_CONFERENCE_MODE )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceModeFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_MUTE_PARTICIPANT )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ParticipantFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            evt.hParticipant = hParticipant;
            fireEvent( cpcFunc( VccsConferenceHandler::onMuteParticipantFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_PARTICIPANT_IS_RECORDING )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ParticipantFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            evt.hParticipant = hParticipant;
            fireEvent( cpcFunc( VccsConferenceHandler::onSetIsRecordingFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_PARTICIPANT_NOT_RECORDING )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ParticipantFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            evt.hParticipant = hParticipant;
            fireEvent( cpcFunc( VccsConferenceHandler::onSetIsRecordingFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_UNMUTE_PARTICIPANT )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ParticipantFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            evt.hParticipant = hParticipant;
            fireEvent( cpcFunc( VccsConferenceHandler::onMuteParticipantFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_KICK_PARTICIPANT )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ParticipantFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode  = toErrorCode( sResponse.getErrorCode() );
            evt.hParticipant = hParticipant;
            fireEvent( cpcFunc( VccsConferenceHandler::onKickParticipantFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_GET_XMPP_CONNECTION )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onXMPPAccountInfoFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_GET_CONFERENCE_INVITE )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceInviteFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_GET_CONFERENCE_CONFIG )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceConfigFailure ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_SET_CONFERENCE_CONFIG )
      {
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceConfigFailure ), hConference, evt );
         }
         else
         {
            Conference::SetConferenceConfigEvent evt;
            fireEvent( cpcFunc( VccsConferenceHandler::onSetConferenceConfig ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_DELETE_CONFERENCE_HISTORY )
      {
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onDeleteConferenceHistoryFailure ), hConference, evt );
         }
         else
         {
            Conference::DeleteConferenceHistoryEvent evt;
            fireEvent( cpcFunc( VccsConferenceHandler::onDeleteConferenceHistory ), hConference, evt );
         }
      }
      else if( origCmd == COMMAND_NAME_GET_CONFERENCE_CONNECTION )
      {
         // Only fire on error
         if( sResponse.isErrorCondition() )
         {
            Conference::ConferenceFailureEvent evt;
            evt.reasonText = sResponse.getErrorCode();
            evt.errorCode = toErrorCode( sResponse.getErrorCode() );
            fireEvent( cpcFunc( VccsConferenceHandler::onConferenceConnectionInfoFailure ), m_pAccountMgr->getAccountHandle(), evt );
         }
      }
      return;
   }

   // Try to parse a conference connection response
   GetConferenceConnectionResponse gcxr;
   if( gcxr.fromString( inString ))
   {
      ConferenceConnectionInfoEvent evt;
      evt.hConference = gcxr.getConferenceID();
      evt.bridge = gcxr.getBridge().c_str();
      evt.group = gcxr.getGroup().c_str();
      evt.lobby = gcxr.getLobby().c_str();
      evt.pinRequired = gcxr.isPinRequired();
      fireEvent( cpcFunc( VccsConferenceHandler::onConferenceConnectionInfo ), m_pAccountMgr->getAccountHandle(), evt );
      return;
   }

   // Try to parse a subscribe response
   SubscribeResponse subr;
   if( subr.fromString( inString ))
   {
      if( !subr.isErrorCondition() )
      {
         Conference::SubscribeEvent evt;
         evt.hConference = subr.getConferenceID();
         evt.directSIPAddress = subr.getDirectSIPAddress().c_str();
         evt.directSIPUsername = subr.getDirectSIPUsername().c_str();
         evt.directSIPPassword = subr.getDirectSIPPassword().c_str();
         evt.directSIPWebsocketServer = subr.getDirectSIPWebsocketServer().c_str();
         evt.directSIPProxy = subr.getDirectSIPProxy().c_str();
         evt.directSIPProvisioning = subr.getDirectSIPProvisioning().c_str();

         if (NULL != m_LastSubscriptioninfo)
         {
            if (m_LastSubscriptioninfo->hConference != 0)
            {
               WarningLog(<< "Last subscription already has a conference handle!");
            }
            m_LastSubscriptioninfo->hConference = evt.hConference;
         }
         else
         {
            WarningLog(<< "Subscribe response recevied but no last subscription info created!");
         }

         fireEvent( cpcFunc( VccsConferenceHandler::onSubscribe ), m_pAccountMgr->getAccountHandle(), evt );

         return;
      }
   }

   // Try to parse a list conferences response
   ListConferencesResponse lcResponse;
   if( lcResponse.fromString( inString ))
   {
      // Fire an event containing the information
      Conference::ConferenceListUpdatedEvent evt;

      std::vector< Conference::ConferenceDetails > list = lcResponse.getConferenceList();
      for( std::vector< Conference::ConferenceDetails >::size_type i = 0 ; i < list.size() ; ++i )
      {
         Conference::ConferenceListChange change;
         change.changeType = Conference::ChangeType_Query;
         change.conference = list[ i ];
         evt.changes.push_back( change );
      }

      setCache( list ); // erases any previously known information

      fireEvent( cpcFunc( VccsConferenceHandler::onConferenceListUpdated ), m_pAccountMgr->getAccountHandle(), evt );
      return;
   }

   // Try to parse a conference invitation response
   GetConferenceInviteResponse gcir;
   if( gcir.fromString( inString ))
   {
      Conference::ConferenceInviteEvent evt;
      evt.htmlInvite = gcir.getHTMLInvite().c_str();
      evt.textInvite = gcir.getTextInvite().c_str();
      evt.joinUrl    = gcir.getJoinURL().c_str();
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceInvite ), gcir.getConferenceHandle(), evt );
      return;
   }

   // Try to parse an XMPP account configuration response
   GetXMPPConnectionResponse gxcr;
   if( gxcr.fromString( inString ))
   {
      Conference::XMPPAccountInfoEvent evt;
      evt.domain      = gxcr.getDomain().c_str();
      evt.proxy       = gxcr.getProxy().c_str();
      evt.port        = gxcr.getPort();
      evt.chatRoomJid = gxcr.getChatRoomJID().c_str();
      evt.username    = gxcr.getUsername().c_str();
      evt.password    = gxcr.getPassword().c_str();
      evt.displayName = gxcr.getDisplayName().c_str();

      fireEvent( cpcFunc( VccsConferenceHandler::onXMPPAccountInfo ), gxcr.getConferenceHandle(), evt );
      return;
   }

   // Try to parse a GetConferenceDetail response
   GetConferenceDetailResponse gcdr;
   if( gcdr.fromString( inString ))
   {
      const Conference::ConferenceDetails details( gcdr.getConferenceDetail() );
      updateCache( details.id, details, true );
      return;
   }

   // Try to parse the participant update notification
   ParticipantUpdatedNotification puNotification;
   if( puNotification.fromString( inString ))
   {
      const Conference::ParticipantStatus participant( puNotification.getParticipantStatus() );
      updateCache( puNotification.getConferenceID(), participant.participantNumber, participant );
      return;
   }

   // Try to parse a conference updated notification
   ConferenceUpdatedNotification cuNotification;
   if( cuNotification.fromString( inString ))
   {
      const Conference::ConferenceDetails details( cuNotification.getConferenceDetails() );
      updateCache( details.id, details );
      return;
   }

   // Try to parse a GetConferenceConfig response
   GetConferenceConfigurationResponse gccr;
   if( gccr.fromString( inString ))
   {
      Conference::ConferenceConfigEvent evt;
      evt.config = gccr.getConfig();
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceConfig ), gccr.getConferenceID(), evt );
      return;
   }

   // Try to parse a GetConferenceHistory response
   GetConferenceHistoryResponse gchr;
   if( gchr.fromString( inString ))
   {
      Conference::ConferenceHistoryEvent evt;
      gchr.getHistoryRecords( evt.historyEntries );
      fireEvent( cpcFunc( VccsConferenceHandler::onQueryConferenceHistory ), gchr.getConferenceID(), evt );
      return;
   }

   // Try to parse a screen sharing notification
   ScreenShareUpdateNotification ssuNotification;
   if( ssuNotification.fromString( inString ))
   {
      if( ssuNotification.isPresenterSet() )
      {
         Conference::ScreenSharePresenterEvent evt;
         evt.hParticipant = ssuNotification.getPresenter();
         fireEvent( cpcFunc( VccsConferenceHandler::onSetScreenSharePresenter ), ssuNotification.getConferenceID(), evt );
      }
      else
      {
         Conference::ScreenShareConfigEvent configEvt;
         configEvt.hParticipant = ssuNotification.getPresenter();
         configEvt.screenShareURL = ssuNotification.getScreenShareURL().c_str();
         configEvt.screenShareActive = ssuNotification.isScreenShareActive();
         configEvt.config = ssuNotification.getScreenShareData();
         fireEvent( cpcFunc( VccsConferenceHandler::onScreenShareConfigChanged ), ssuNotification.getConferenceID(), configEvt );
      }
      return;
   }

   // Voice activity detection
   VoiceActivityNotification vaNotification;
   if( vaNotification.fromString( inString ))
   {
      Conference::VoiceActivityEvent evt;
      evt.hParticipant = vaNotification.getParticipant();
      evt.isTalking = vaNotification.isTalking();
      evt.hasAudioFloor = vaNotification.hasFloor();
      evt.energyLevel = vaNotification.getEnergy();
      fireEvent(cpcFunc(VccsConferenceHandler::onVoiceActivityChanged), vaNotification.getConferenceID(), evt);
      return;
   }

   // conference mode notification event
   SetConferenceModeNotification scmNotification;
   if( scmNotification.fromString( inString ))
   {
      Conference::ConferenceModeEvent evt;
      evt.participantLock = scmNotification.m_ParticipantLock;
      evt.muteLock = scmNotification.m_MuteLock;
      evt.hosted = scmNotification.m_Hosted;
      evt.muteAll = scmNotification.m_MuteAll;
      evt.entryExitTonesEnabled = scmNotification.m_EntryExitTonesEnabled;
      evt.recording = scmNotification.m_Recording;
      evt.audioOnlyRecording = scmNotification.m_AudioOnlyRecording;

      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_ParticipantLock )
         evt.participantLockChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_MuteLock )
         evt.muteLockChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_Hosted )
         evt.hostedChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_MuteAll )
         evt.muteAllChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_EntryExitTones )
         evt.entryExitTonesEnabledChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_Recording )
         evt.recordingChanged = true;
      if( scmNotification.m_Bitmask & SetConferenceModeNotification::Mask_AudioOnlyRecording )
         evt.audioOnlyRecordingChanged = true;

      fireEvent(cpcFunc(VccsConferenceHandler::onConferenceModeUpdated), scmNotification.getConferenceID(), evt);
      return;
   }
}

void VccsConferenceManagerImpl::onPing( websocketpp::connection_hdl hWebSock )
{
   // Override the ping functionality to send our own message.
   m_pAccountMgr->getStateMachine()->sendPing( hWebSock );
}

bool VccsConferenceManagerImpl::onReconnect( uint16_t code )
{
   switch( code )
   {
	case 1001 : // WSCR_SERVER_DRAIN (aka GOING_AWAY)
	case 4002 : // WSCR_SERVER_COMMUNICATION_ERROR Server Communication Error
	case 4003 : // WSCR_UNEXPECTED_ERROR Unexpected Error
		return true;
	case 4104 : // WSCR_KICKED
	case 4100 : // WSCR_UNAUTHORIZED Unauthorized: User not found or password not valid
	case 4101 : // WSCR_LOCKED_OUT Unauthorized: User locked out
	case 4102 : // WSCR_SUSPENDED Unauthorized: User or group suspended
      WarningLog( << "Connection Close/Failure with error code " << code << " Retry disabled" );
      return false;
   default:
      break;
   }
   return true;
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
