#include "brand_branded.h"

#include "interface/experimental/vccs/VccsAccountManager.h"

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
#include "VccsAccountManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Account
      {
         VccsAccountManager* VccsAccountManager::getInterface( CPCAPI2::Phone* cpcPhone )
         {
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
            PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
            return _GetInterface<VccsAccountManagerInterface>(phone, "VccsAccountManager"); // for now use the impl (need to discuss w/ Jeremy)
#else
            return NULL;
#endif
         }
      }
   }
}
