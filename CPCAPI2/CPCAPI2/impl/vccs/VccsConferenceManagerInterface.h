#pragma once
#ifndef __CPCAPI2_VCCSCONFERENCEMANAGERINTERFACE_H__
#define __CPCAPI2_VCCSCONFERENCEMANAGERINTERFACE_H__

#include <boost/asio.hpp>

#include <phone/PhoneModule.h>
#include <vccs/VccsConferenceManager.h>

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Account
      {
         class VccsAccountManagerInterface;
      }

      namespace Conference
      {
         // Forward decl's
         struct AccountInfo;
         typedef struct AccountInfo AccountInfo;

         class VccsConferenceManagerImpl;

         class VccsConferenceManagerInterface : public VccsConferenceManager, public CPCAPI2::PhoneModule
         {
         public:
            VccsConferenceManagerInterface( Account::VccsAccountManagerInterface* pAccountMgr );
            virtual ~VccsConferenceManagerInterface();

            // VccsConferenceManager implementation
            int setHandler(Account::VccsAccountHandle hAccount, VccsConferenceHandler* handler) OVERRIDE;
            int getConferenceConnectionInfo(Account::VccsAccountHandle account,const cpc::string& conferenceCode ) OVERRIDE;
            int subscribe(Account::VccsAccountHandle hAccount,const SubscriptionInfo& info ) OVERRIDE;
            int subscribe(Account::VccsAccountHandle hAccount, const cpc::string& bridge, const cpc::string& pin, const cpc::string& sipAor, ParticipantType participantType, int capabilities ) OVERRIDE;
            int subscribe2(Account::VccsAccountHandle hAccount, const cpc::string& conferenceCode, const cpc::string& pin, const cpc::string& sipAor, ParticipantType participantType, int capabilities ) OVERRIDE;
            int unsubscribe(Account::VccsAccountHandle hAccount, const cpc::string& bridge ) OVERRIDE;
            int queryConferenceList(Account::VccsAccountHandle hAccount) OVERRIDE;
            int queryConferenceDetails(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference) OVERRIDE;
            int queryConferenceInvite(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference) OVERRIDE;
            int setParticipantLock(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool locked ) OVERRIDE;
            int setMuteLock(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked ) OVERRIDE;
            int setHosted(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool hosted ) OVERRIDE;
            int setMuteOthers(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool mute ) OVERRIDE;
            int setEntryExitTonesEnabled(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool enabled) OVERRIDE;
            int setRecording(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled ) OVERRIDE;
            int setAudioOnlyRecording(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled ) OVERRIDE;
            int setJoinMuted(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled ) OVERRIDE;
            int kickParticipant(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant) OVERRIDE;
            int muteParticipant(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute) OVERRIDE;
            int setIsRecording(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording) OVERRIDE;
            int getXMPPAccountInfo(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool guestAccount = false ) OVERRIDE;
            int setScreenSharePresenter(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant ) OVERRIDE;
            int startScreenShare(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant,const cpc::string& screenSharingURL,const ScreenSharingInfoList& infoList ) OVERRIDE;
            int stopScreenShare(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant ) OVERRIDE;
            int queryConferenceConfig(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference) OVERRIDE;
            int setConferenceConfig(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,const ConferenceConfiguration& conferenceConfig,const ConferenceConfigurationSet& conferenceConfigSet ) OVERRIDE;
            int queryConferenceHistory(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,int offset,int count,bool includeParticipants,HistoryID historyID ) OVERRIDE;
            int deleteHistory(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, const cpc::vector< HistoryID >& historyIDs ) OVERRIDE;

            // Mixer options
            int setVideoFrameRate(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, int videoFrameRate) OVERRIDE { return -1; } // deprecated
            int setVideoFloorParticipant(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant) OVERRIDE;
            int setVideoLayout(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VideoLayout videoLayoutType) OVERRIDE;
            int setVideoResolution(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VideoResolution videoResolutionType) OVERRIDE;


            // PhoneModule implementation
            void Release() OVERRIDE;

            // Methods which are dispatched to the io_service
            int setHandlerImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandler* handler);
            int getConferenceConnectionInfoImpl(Account::VccsAccountHandle account,const cpc::string& conferenceCode );
            int subscribeImpl(Account::VccsAccountHandle hAccount, const SubscriptionInfo& info );
            int unsubscribeImpl(Account::VccsAccountHandle hAccount, const cpc::string& bridge );
            int queryConferenceListImpl(Account::VccsAccountHandle hAccount);
            int queryConferenceDetailsImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference);
            int queryConferenceInviteImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference);
            int setParticipantLockImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool locked);
            int setMuteLockImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked);
            int setHostedImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool hosted);
            int setMuteOthersImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked);
            int setEntryExitTonesEnabledImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool enabled);
            int setRecordingImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled );
            int setAudioOnlyRecordingImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled );
            int setJoinMutedImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled );
            int kickParticipantImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant);
            int muteParticipantImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute);
            int setIsRecordingImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording);
            int getXMPPAccountInfoImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool guestAccount = false );
            int setScreenSharePresenterImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant );
            int startScreenShareImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant,const cpc::string& screenSharingURL,const ScreenSharingInfoList& infoList );
            int stopScreenShareImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant );
            int setVideoFrameRateImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, int videoFrameRate);
            int setVideoFloorParticipantImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant);
            int setVideoLayoutImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VideoLayout videoLayoutType);
            int setVideoResolutionImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference, VideoResolution videoResolutionType);
            int queryConferenceConfigImpl(Account::VccsAccountHandle hAccount, VccsConferenceHandle hConference);
            int setConferenceConfigImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,const ConferenceConfiguration& conferenceConfig,const ConferenceConfigurationSet& conferenceConfigSet );
            int queryConferenceHistoryImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference,int offset,int count,bool includeParticipants,HistoryID historyID );
            int deleteHistoryImpl(Account::VccsAccountHandle hAccount,VccsConferenceHandle hConference, const cpc::vector< HistoryID >& historyIDs );

         private: // data

            Account::VccsAccountManagerInterface* m_pAccountMgr;
            boost::asio::io_service& m_IOService; // used for dispatching
         };
 
 
         std::ostream& operator<<(std::ostream& os, const CPCAPI2::VCCS::Conference::SubscriptionInfo& subscriptionInfo);
      }
   }
}

#endif // __CPCAPI2_VCCSCONFERENCEMANAGERINTERFACE_H__
