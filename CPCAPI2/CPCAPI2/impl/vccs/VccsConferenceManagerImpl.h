#pragma once
#ifndef __CPCAPI2_VCCSCONFERENCEMANAGERIMPL_H__
#define __CPCAPI2_VCCSCONFERENCEMANAGERIMPL_H__

#include <brand_branded.h>
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <phone/PhoneModule.h>
#include <websocket/WebSocketStateMachine.h>

#include <vccs/VccsConferenceManager.h>
#include <vccs/VccsConferenceHandler.h>
#include <vccs/VccsAccountSettings.h>
#include "VccsAccountManagerImpl.h"
#include "VccsInternalTypes.h"
#include "VccsConferenceSyncHandler.h"

#include <map>

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
         // reverse mappings from participant to conference
         typedef struct ParticipantInfo
         {
            VccsConferenceParticipantHandle hParticipant;
            VccsConferenceHandle hParentConference;
         } ParticipantInfo;

         class VccsConferenceManagerImpl : public WebSocket::WebSocketStateMachineListener, public CPCAPI2::PhoneModule
         {
         public:
            VccsConferenceManagerImpl( Account::VccsAccountManagerImpl* pAccountMgr );
            virtual ~VccsConferenceManagerImpl();

            // VccsConferenceManager implementation
            int setHandler(VccsConferenceHandler* handler);
            int getConferenceConnectionInfo( const cpc::string& conferenceCode );
            int subscribe( const SubscriptionInfo& info );
            int unsubscribe(const cpc::string& bridge );
            int queryConferenceList( void );
            int queryConferenceDetails(VccsConferenceHandle hConference);
            int queryConferenceInvite(VccsConferenceHandle hConference);
            int setParticipantLock(VccsConferenceHandle hConference, bool mute);
            int setMuteLock(VccsConferenceHandle hConference, bool mute);
            int setHosted(VccsConferenceHandle hConference, bool hosted);
            int setMuteOthers(VccsConferenceHandle hConference, bool mute);
            int setEntryExitTonesEnabled(VccsConferenceHandle hConference, bool enable);
            int setRecording(VccsConferenceHandle hConference, bool enable);
            int setAudioOnlyRecording(VccsConferenceHandle hConference, bool enable);
            int setJoinMuted(VccsConferenceHandle hConference, bool enable);
            int kickParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant);
            int muteParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute);
            int setIsRecording(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording);
            int getXMPPAccountInfo(VccsConferenceHandle hConference, bool guestAccount = false );
            int setScreenSharePresenter(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant );
            int startScreenShare(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant,const cpc::string& screenSharingURL,const ScreenSharingInfoList& infoList );
            int stopScreenShare(VccsConferenceHandle hConference,VccsConferenceParticipantHandle hParticipant );
            int setVideoFloorParticipant(VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant);
            int setVideoLayout(VccsConferenceHandle hConference, VideoLayout videoLayoutType);
            int setVideoResolution(VccsConferenceHandle hConference, VideoResolution videoResolutionType);
            int queryConferenceConfig( VccsConferenceHandle hConference );
            int setConferenceConfig( VccsConferenceHandle hConference, const ConferenceConfiguration& conferenceConfig, const ConferenceConfigurationSet& conferenceConfigSet );
            int queryConferenceHistory(VccsConferenceHandle hConference, int offset, int count, bool includeParticipants, HistoryID historyID);
            int deleteHistory(VccsConferenceHandle hConference, const cpc::vector< HistoryID >& historyIDs);

            // PhoneModule implementation
            void Release() OVERRIDE;

            // Methods for accessing/updating the cached conference information
            bool setCache( std::vector< ConferenceDetails >& details );

            bool updateCache(
               const VccsConferenceHandle& hConference,
               const ConferenceDetails& details,
               bool fromQuery = false );

            bool updateCache(
               const VccsConferenceHandle& hConference,
               const VccsConferenceParticipantHandle& hParticipant,
               const ParticipantStatus& details );

            bool clearCache();

            // Implementation of WebSocketStateMachineListener
            void onStateChange( const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason ) OVERRIDE;
            void onLogin( void ) OVERRIDE;
            void onLoginResponse( const std::string& inString ) OVERRIDE;
            void onLogout( void ) OVERRIDE {}
            void onMessage( const std::string& inString ) OVERRIDE;
            void onPing( websocketpp::connection_hdl hWebSock ) OVERRIDE;
            bool onReconnect( uint16_t code ) OVERRIDE;

            // template for conference-related events and handlers
            template<typename TFn, typename THdl, typename TEvt> void fireEvent(const char* funcName, TFn func, THdl h, const TEvt& args)
            {
               resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, h, args);
               if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<Account::VccsConferenceSyncHandler*>( m_Handler ) != NULL)
               {
                  (*cb)();
                  delete cb;
               }
               else
               {
                  // Object "cb" should be deleted by whomever is processing this FIFO
                  m_pAccountMgr->m_CallbackFifo.add( cb );
                  if (m_pAccountMgr->m_CbHook) { m_pAccountMgr->m_CbHook(); }
               }
            }

         private: // methods

            ErrorCode toErrorCode( const char* stringVal ) const;

         private: // data
            Account::VccsAccountManagerImpl *m_pAccountMgr; // not owned

            // Application handler for conference related activity
            VccsConferenceHandler *m_Handler;

            struct ConferenceDetailsInternal
            {
               std::shared_ptr< ConferenceDetails > mPubConferenceDetails;
               std::map<VccsConferenceParticipantHandle, ParticipantStatus> mParticipants;
            };      

            // Cache of Conference-related information
            typedef std::map< VccsConferenceHandle, ConferenceDetailsInternal> ConferenceMap_t;
            
            
            ConferenceMap_t m_ConfMap;
            // Cache last SubscriptionInfo that went out in a SUBSCRIBE:
            struct SubscriptionInfoEx
            {
               VccsConferenceHandle hConference;
               SubscriptionInfo subscriptionInfo;

               SubscriptionInfoEx() : hConference(0) {};
            };

            SubscriptionInfoEx *m_LastSubscriptioninfo;
         };
      }
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
#endif // __CPCAPI2_VCCSCONFERENCEMANAGERIMPL_H__
