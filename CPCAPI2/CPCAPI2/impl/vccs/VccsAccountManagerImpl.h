#pragma once
#ifndef __CPCAPI2_VCCSACCOUNTMANAGERIMPL_H__
#define __CPCAPI2_VCCSACCOUNTMANAGERIMPL_H__

#include <brand_branded.h>
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <map>
#include <atomic>
#include <boost/asio.hpp>

#include <websocket/WebSocketHandler.h>
#include <websocket/json/WebSocketCommand.h>
#include <websocket/WebSocketStateMachine.h>
#include <websocket/WebSocketStateDispatcher.h>

#include <phone/PhoneModule.h>
#include <phone/NetworkChangeHandler.h>
#include <vccs/VccsAccountManager.h>
#include <vccs/VccsAccountSettings.h>
#include <vccs/VccsAccountHandler.h>
#include "VccsInternalTypes.h"
#include "VccsAccountSyncHandler.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#include "../util/DumFpCommand.h"

#if defined(TARGET_OS_IPHONE) && (TARGET_OS_IPHONE==1)
#include <CFNetwork/CFNetwork.h>
#endif

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
         class VccsConferenceManagerImpl;
      }

      namespace Account
      {
         class AccountState;
         struct AccountInfo;
         typedef struct AccountInfo AccountInfo;

         class VccsAccountManagerImpl
         {
         public:

            friend class Conference::VccsConferenceManagerImpl;

            VccsAccountManagerImpl(
               VccsAccountHandle hAccount,
               boost::asio::io_service& ioService,
               resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
               SslCipherOptions tlsSettings);
            virtual ~VccsAccountManagerImpl();

            // dispatched methods implementation
            int configureDefaultAccountSettings(const VccsAccountSettings& vccsAccountSettings);
            int setHandler(VccsAccountHandler* handler);
            int enable();
            int disable();
            void setCallbackHook(void (*cbHook)(void*), void* context);
            int onNetworkChange(const CPCAPI2::NetworkChangeEvent& args);

            // Accessor methods
            WebSocket::WebSocketStateMachine *getStateMachine() { return m_StateMachine; }
            const VccsAccountSettings& getSettings() { return m_Settings; }
            const VccsAccountHandle getAccountHandle() { return m_hAccount; }
            WebSocket::RequestHandle nextRequestHandle() { return m_RequestHandle.fetch_add( 1 ); }

#ifdef CPCAPI2_AUTO_TEST
            void resetRequestHandle() { m_RequestHandle.exchange(1); }
#endif


            // Funky macro for firing events
            template<typename TFn, typename TEvt> void fireEvent(
               const char* funcName,
               TFn func,
               const TEvt& args)
            {
               if( m_Handler == NULL )
                  return;

               resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_hAccount, args);
               if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<Account::VccsAccountSyncHandler*>( m_Handler ) != NULL)
               {
                  // Invoke the callback synchronously in these cases
                  ( *cb )();
                  delete cb;
               }
               else
               {
                  // Object "cb" should be deleted by whomever is processing this FIFO
                  m_CallbackFifo.add( cb );
                  if (m_CbHook) { m_CbHook(); }
               }
            }

         private: // methods

         private: // data

            std::atomic< WebSocket::RequestHandle > m_RequestHandle;

            boost::asio::io_service& m_IOService; // passed into websocketpp lib
            resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo; // shared between Interface and Impl
            std::function<void(void)> m_CbHook;

            const VccsAccountHandle m_hAccount;

            // Pointer to the settings for this account (owned)
            VccsAccountSettings m_Settings;

            // State machine instance (owned)
            WebSocket::WebSocketStateMachine *m_StateMachine;

            // Application handler for account related activity
            VccsAccountHandler *m_Handler;

            // Default TLS settings
            SslCipherOptions m_TlsSettings;
            // SUBSCRIBE needed after websocket reconnect
            bool m_SubscribeAfterReconnect;
         };
      }
   }
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
#endif // __CPCAPI2_VCCSACCOUNTMANAGERIMPL_H__
