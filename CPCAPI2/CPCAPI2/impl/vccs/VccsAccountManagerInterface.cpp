#include "VccsAccountManagerInterface.h"

#include <brand_branded.h>

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <mutex>
#include <condition_variable>

#include <sstream>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::VCCS

#include <vccs/VccsAccountHandler.h>
#include <vccs/VccsAccountSettings.h>
#include <phone/PhoneInterface.h>
#include "util/cpc_logger.h"
#include "rutil/Logger.hxx"

#include "VccsAccountManagerImpl.h"
#include "VccsConferenceManagerImpl.h"

#include <utils/msrp_string.h> // for strtok_r and URL encode/decode
#include <utils/msrp_mem.h> // for percent_decode usage
#include <utils/msrp_strndup.h>

#ifdef ANDROID
#include <pthread.h>
#include "JniHelper.h"
#endif

#define VCCS_SERVICE_NAME "join" // first element in the VCCS URL path portion

using namespace CPCAPI2::VCCS::Account;

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   if( info == NULL ) \
   { \
      WarningLog(<< "info is NULL, " ERRTEXT ); \
   } \
   else if( info->acctImpl != NULL ) \
   { \
      ErrorEvent evt; \
      evt.errorText = ( ERRTEXT ); \
      info->acctImpl->fireEvent( cpcFunc( VccsAccountHandler::onError ), evt ); \
   } \
   return kError; \
}
#define INVALID_ACCOUNT_HANDLE_STR  "Invalid Account Handle"

std::atomic< VccsAccountHandle > VccsAccountManagerInterface::s_CurrentHandle( 1 );

VccsAccountManagerInterface::VccsAccountManagerInterface(CPCAPI2::Phone* phone)
   : m_IOService(dynamic_cast<PhoneInterface*>(phone)->getAsioIoService()), m_Shutdown( false ), m_CbHook( NULL ), m_Context( NULL ), m_Phone(phone)
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(m_Phone))->addSdkObserver(this);
}

VccsAccountManagerInterface::~VccsAccountManagerInterface()
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(m_Phone))->removeSdkObserver(this);

   m_Shutdown = true;

   // Free everything in the info map
   std::map< VccsAccountHandle, AccountInfo* >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL )
      {
         delete iter->second->confImpl;     // this will access the WebSocketStateMachine to remove a listener
         delete iter->second->acctImpl;     // this will delete the WebSocketStateMachine
      }
      delete iter->second;
      ++iter;
   }
   m_InfoMap.clear();
}

AccountInfo *VccsAccountManagerInterface::getAccountInfo( VccsAccountHandle hAccount )
{
   std::map< VccsAccountHandle, AccountInfo* >::const_iterator iter( m_InfoMap.find( hAccount ));
   if( iter == m_InfoMap.end() )
      return NULL;

   return iter->second;
}

VccsAccountHandle VccsAccountManagerInterface::create()
{
   DebugLog(<< "VccsAccountManagerInterface::create");

   VccsAccountHandle hAccount = s_CurrentHandle.fetch_add( 1 );
   m_IOService.post( std::bind( &VccsAccountManagerInterface::createImpl, this, hAccount ));
   return hAccount;
}

int VccsAccountManagerInterface::createImpl( const VccsAccountHandle& hAccount )
{
#ifdef ANDROID
   DebugLog(<< "VCCS createImpl for thread id " << gettid());
#endif

   AccountInfo *info = getAccountInfo( hAccount );
   if( info != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   info           = new AccountInfo;
   info->handle   = hAccount;
   info->acctImpl = new VccsAccountManagerImpl( hAccount, m_IOService, m_CallbackFifo, dynamic_cast<PhoneInterface*>(m_Phone)->getSslCipherOptions() );
   info->confImpl = new Conference::VccsConferenceManagerImpl( info->acctImpl );

   info->acctImpl->setCallbackHook(m_CbHook, m_Context);
   
   // Add the confImpl as a listener to the csm
   WebSocket::WebSocketStateMachine *csm = info->acctImpl->getStateMachine();
   if( csm != NULL )
      csm->addListener( info->confImpl );

   m_InfoMap[ hAccount ] = info;
   return kSuccess;
}

void VccsAccountManagerInterface::Release()
{
   delete this; // suicide
}

int VccsAccountManagerInterface::configureDefaultAccountSettings(VccsAccountHandle hAccount, const VccsAccountSettings& vccsAccountSettings)
{
   DebugLog(<< "VccsAccountManagerInterface::configureDefaultAccountSettings, hAccount=" << hAccount << ", vccsAccountSettings=" << vccsAccountSettings);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::configureDefaultAccountSettingsImpl, this, hAccount, vccsAccountSettings ));
   return kSuccess;
}

int VccsAccountManagerInterface::configureDefaultAccountSettingsImpl(VccsAccountHandle hAccount, const VccsAccountSettings& vccsAccountSettings)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if (info == NULL)
   {
      ErrLog(<< "No account information for VccsAccountHandle handle " << hAccount);
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);
   }

   int result = info->acctImpl->configureDefaultAccountSettings( vccsAccountSettings );

   // Register the confimpl as a listener to the state machine
   info->acctImpl->getStateMachine()->addListener( info->confImpl );

   return result;
}

int VccsAccountManagerInterface::applySettings(VccsAccountHandle hAccount)
{
   DebugLog(<< "VccsAccountManagerInterface::applySettings " << hAccount);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::applySettingsImpl, this, hAccount ));
   return kSuccess;
}

int VccsAccountManagerInterface::applySettingsImpl(VccsAccountHandle hAccount)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   //return info->acctImpl->applySettings();
   return kSuccess;
}

int VccsAccountManagerInterface::decodeProvisioningResponse(const cpc::string & provisioningResponse, cpc::vector<VccsAccountSettings>& outVccsAccountSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());
   
   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }
   
   if (!provisionedJSON.HasMember("vccsAccount"))
   {
      WarningLog(<< "Invalid provisioning format, vccsAccount node missing. Aborting decode.");
      return kError;
   }
   
   const rapidjson::Value& account = provisionedJSON["vccsAccount"];
   if (!account.IsArray())
   {
      WarningLog(<< "Invalid provisioning format, vccsAccount node not an array. Aborting decode.");
      return kError;
   }
   
   for (rapidjson::Value::ConstValueIterator itr = account.Begin(); itr != account.End(); ++itr)
   {
      if (!itr->HasMember("vccsAccountSettings"))
      {
         WarningLog(<< "Invalid provisioning format, vccsAccountSettings node missing.");
         continue;
      }
      
      const rapidjson::Value& accountSettings = (*itr)["vccsAccountSettings"];
      if (!accountSettings.IsObject())
      {
         WarningLog(<< "Invalid provisioning format, vccsAccountSettings not an object.");
         continue;
      }
      
      VccsAccountSettings settings;
      JsonDeserialize(*itr, "vccsAccountSettings", settings);
      outVccsAccountSettings.push_back(settings);
   }
   return kSuccess;
}

int VccsAccountManagerInterface::destroy(VccsAccountHandle hAccount)
{
   DebugLog(<< "VccsAccountManagerInterface::destroy " << hAccount);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::destroyImpl, this, hAccount ));
   return kSuccess;
}

int VccsAccountManagerInterface::destroyImpl(VccsAccountHandle hAccount)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   delete info->confImpl;     // this will access the WebSocketStateMachine to remove a listener
   delete info->acctImpl;     // this will delete the WebSocketStateMachine
   delete info;

   m_InfoMap.erase( hAccount );
   return kSuccess;
}

int VccsAccountManagerInterface::setHandler(VccsAccountHandle hAccount, VccsAccountHandler* handler)
{
   int result = kError;
   if( handler != NULL )
   {
      // Just do the normal thing.
      m_IOService.post( std::bind( &VccsAccountManagerInterface::setHandlerImpl, this, hAccount, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( VccsAccountManagerInterface *parent, VccsAccountHandle& hAccount, VccsAccountHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhAccount( hAccount ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhAccount, mHandler );
            mCVar.notify_all();
         }

         VccsAccountManagerInterface *mParent;
         VccsAccountHandle& mhAccount;
         VccsAccountHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hAccount, handler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int VccsAccountManagerInterface::setHandlerImpl(VccsAccountHandle hAccount, VccsAccountHandler* handler)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->acctImpl->setHandler( handler );
}

int VccsAccountManagerInterface::enable(VccsAccountHandle hAccount)
{
   DebugLog(<< "VccsAccountManagerInterface::enable " << hAccount);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::enableImpl, this, hAccount ));
   return kSuccess;
}

int VccsAccountManagerInterface::enableImpl(VccsAccountHandle hAccount)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->acctImpl->enable();
}

int VccsAccountManagerInterface::disable(VccsAccountHandle hAccount)
{
   DebugLog(<< "VccsAccountManagerInterface::disable " << hAccount);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::disableImpl, this, hAccount ));
   return kSuccess;
}

int VccsAccountManagerInterface::disableImpl(VccsAccountHandle hAccount)
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->acctImpl->disable();
}

bool VccsAccountManagerInterface::crackVCCSURL(
   const cpc::string& inURL,
   const bool&  secureWebSocketRequired,
   cpc::string& webSocketURL,
   cpc::string& serverName,
   int& portNumber,
   cpc::string& groupName,
   cpc::string& subscriptionCode )
{
   return crackVCCSURL( inURL, "", secureWebSocketRequired, webSocketURL, serverName, portNumber, groupName, subscriptionCode );
}

bool VccsAccountManagerInterface::crackVCCSURL(
   const cpc::string& inURL,
   const cpc::string& expectedScheme,
   const bool&  secureWebSocketRequired,
   cpc::string& webSocketURL,
   cpc::string& serverName,
   int& portNumber,
   cpc::string& groupName,
   cpc::string& subscriptionCode )
{
   // This method has no dependencies on the rest of the system and therefore we
   // will just implement it inline here.

   // The form of the VCCS URL is:
   //
   // vccs://{server}:{port}/join/{code}/{groupName}
   //
   // (vccs|http|https)://[a-z]+(:[0-9]+)?/join/[a-z]{10}/[a-z]+
   //
   // port is optional, and {conference} is just a string which is not meaningful
   // to the application. All other parameters should be URL encoded, and therefore
   // will have to be URL decoded before passing back to the application.
   std::string outHostName;
   int         outPortNumber = 443;
   std::string outGroupName;
   std::string outConferenceCode;
   bool magicToken = false;
   bool result = false;
   char *temp  = NULL;
   
   const char *path = NULL;
   size_t path_len;
   size_t authority_len;
   size_t scheme_len;
   const char *authority = NULL;
   const char *authority_end = NULL;

   std::string protocol = ( expectedScheme.size() == 0 ) ?
      "vccs" : expectedScheme.c_str();

   // Find the scheme
   const char *scheme     = inURL.c_str();
   const char *scheme_end = strstr( scheme, "://" );
   if( scheme_end == NULL )
      goto done; // we require a scheme.
   scheme_len = scheme_end - scheme;

   // Validate the possible schemes
   if( strncasecmp( scheme, "https",          scheme_len ) != 0 &&
       strncasecmp( scheme, "http",           scheme_len ) != 0 &&
       strncasecmp( scheme, protocol.c_str(), scheme_len ) != 0 )
      goto done; // no match

   // Find the authority start and length
   authority     = scheme + scheme_len + strlen( "://" );
   authority_end = strchr( authority, '/' );
   if( authority_end == NULL )
      goto done; // we require a path.
   authority_len = authority_end - authority;

   temp = strndup( authority, authority_len );
   if( temp != NULL )
   {
      // Skip beyond the userpwd section if there is one.
      const char *userpwd = strchr( temp, '@' );

      // Locate the start of the host
      const char *host = ( userpwd != NULL ) ? ( userpwd + 1 ) : temp;

      // Find the port, if one.
      const char *port = strchr( host, ':' );
      outPortNumber = ( port != NULL ) ? atoi( port + 1 ) : 443;

      // Copy the hostname segment into the outparam
      outHostName.assign( host, port == NULL ? strlen( host ) : port - host );

      free( temp );
      temp = NULL;
   }

   // The path section begins after the authority and is terminated
   // either by a "?", or a "#", or NULL.
   path = authority_end + 1;
   path_len  = strcspn( path, "?#" );

   temp = strndup( path, path_len );
   if( temp != NULL )
   {
      char *saveptr = NULL;

      // Use strtok_r to retrieve the path elements
      char *join = strtok_r( temp, "/", &saveptr );
      if( join != NULL )
      {
         if( strncmp( join, VCCS_SERVICE_NAME, strlen( VCCS_SERVICE_NAME )) == 0 )
         {
            magicToken = true; // litmus test for URL

            char *code = strtok_r( NULL, "/", &saveptr );
            if( code != NULL )
            {
               outConferenceCode.assign( code ); // remember this
               char *groupName = strtok_r( NULL, "/", &saveptr );
               if( groupName != NULL )
                  outGroupName.assign( groupName ); // remember this
            }
         }
      }

      free( temp );
      temp = NULL;
   }

   // Only if we obtained all the required elements, do we consider
   // this to be a success.
   if( outHostName.size() > 0 && outGroupName.size() > 0 &&
       outConferenceCode.size() > 0 && magicToken )
       result = true;

done:

   // Check if there was a positive result. If so, assign all of our
   // discovered/parsed items into the outparams.
   if( result )
   {
      char *decoded = NULL;

      decoded = msrp_string_percent_decode( outHostName.c_str() );
      serverName = decoded;
      msrp_free(( void * ) decoded );

      portNumber = outPortNumber;

      decoded = msrp_string_percent_decode( outGroupName.c_str() );
      groupName = decoded;
      msrp_free(( void * ) decoded );

      decoded = msrp_string_percent_decode( outConferenceCode.c_str() );
      subscriptionCode = decoded;
      msrp_free(( void * ) decoded );

      // Build a proper websocket out of these components and set it
      // as the outparam.
      std::ostringstream strs;
      strs << ( secureWebSocketRequired ? "wss://" : "ws://" ) << serverName << ":" << portNumber << "/" << VCCS_SERVICE_NAME;
      webSocketURL = strs.str().c_str();
   }

   if( temp != NULL )
   {
      free( temp );
      temp = NULL;
   }

   return result;
}

int VccsAccountManagerInterface::setSuspendable( VccsAccountHandle hAccount, bool isSuspendable )
{
   DebugLog(<< "VccsAccountManagerInterface::setSuspendable " << hAccount << ", " << isSuspendable);

   m_IOService.post( std::bind( &VccsAccountManagerInterface::setSuspendableImpl, this, hAccount, isSuspendable ));
   return kSuccess;
}

int VccsAccountManagerInterface::setSuspendableImpl( VccsAccountHandle hAccount, bool isSuspendable )
{
   AccountInfo *info = getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   info->acctImpl->getStateMachine()->setSuspendable( isSuspendable );
   return kSuccess;
}

int VccsAccountManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

void VccsAccountManagerInterface::postToProcessThread(void (*pfun)(void*), void* obj)
{
   // Inner struct just used to stuff a std::bind into the resip Fifo
   struct ReadCallback : resip::ReadCallbackBase
   {
      ReadCallback(std::function<void()> f) : mF(f) {}
      virtual void operator()() { mF(); }
      virtual void* address() { return NULL; }
   private:
      std::function<void()> mF;
   };

   std::function<void()> bfunc = std::bind(pfun, obj);
   ReadCallback* brcb = new ReadCallback( bfunc );
   m_CallbackFifo.add(brcb);
}

void VccsAccountManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   m_IOService.post( std::bind( &VccsAccountManagerInterface::setCallbackHookImpl, this, cbHook, context ));
}

void VccsAccountManagerInterface::setCallbackHookImpl( void (*cbHook)(void*), void* context )
{
   m_CbHook = cbHook;
   m_Context = context;
   
   std::map< VccsAccountHandle, AccountInfo* >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL && iter->second->acctImpl != NULL )
         iter->second->acctImpl->setCallbackHook( cbHook, context );

      ++iter;
   }
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* VccsAccountManagerInterface::process_test(int timeout)
{
   if( m_Shutdown )
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}


int VccsAccountManagerInterface::resetRequestHandle()
{
   DebugLog(<< "VccsAccountManagerInterface::resetRequestHandle");

   m_IOService.post(std::bind(&VccsAccountManagerInterface::resetRequestHandleImpl, this));

   return kSuccess;
}

int VccsAccountManagerInterface::resetRequestHandleImpl()
{
   std::map< VccsAccountHandle, AccountInfo* >::iterator iter = m_InfoMap.begin();
   while (iter != m_InfoMap.end())
   {
      if (iter->second && iter->second->acctImpl)
      {
         iter->second->acctImpl->resetRequestHandle();
      }
      ++iter;
   }

   return kSuccess;
}
#endif // CPCAPI2_AUTO_TEST


int VccsAccountManagerInterface::onNetworkChange(const CPCAPI2::NetworkChangeEvent& args)
{
   m_IOService.post(std::bind(&VccsAccountManagerInterface::onNetworkChangeImpl, this, args));
   return kSuccess;
}

int VccsAccountManagerInterface::onNetworkChangeImpl(const CPCAPI2::NetworkChangeEvent& args)
{
   std::map< VccsAccountHandle, AccountInfo* >::iterator iter = m_InfoMap.begin();
   while (iter != m_InfoMap.end())
   {
      if (iter->second && iter->second->acctImpl)
      {
         iter->second->acctImpl->onNetworkChange(args);
      }      
      ++iter;
   }

   return kSuccess;
}

namespace CPCAPI2
{
namespace VCCS
{
namespace Account
{

std::ostream& operator<<(std::ostream& os, const CPCAPI2::VCCS::Account::VccsAccountSettings& accountSettings)
{
   os << "wsSettings:" << accountSettings.wsSettings
      << " group:" << accountSettings.group
      << " userName:" << accountSettings.userName
      << " password:<redacted>"
      << " displayName:" << accountSettings.displayName
      << " xmppUserName:" << accountSettings.xmppUserName;
      
   return os;
}

}
}
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
