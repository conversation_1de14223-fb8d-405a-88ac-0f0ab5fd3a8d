#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::VCCS

#include "util/cpc_logger.h"
#include "rutil/Logger.hxx"

#include <vccs/VccsAccountHandler.h>
#include <vccs/VccsConferenceHandler.h>

#include "VccsConferenceManagerInterface.h"
#include "VccsConferenceManagerImpl.h"
#include "VccsAccountManagerInterface.h"
#include "VccsAccountManagerImpl.h"
#include "VccsInternalTypes.h"

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   if( info == NULL ) \
   { \
      WarningLog(<< "info is NULL, " ERRTEXT ); \
   } \
   else if( info->acctImpl != NULL ) \
   { \
      Account::ErrorEvent evt; \
      evt.errorText = ( ERRTEXT ); \
      info->acctImpl->fireEvent( cpcFunc( Account::VccsAccountHandler::onError ), evt ); \
   } \
   return kError; \
}
#define INVALID_ACCOUNT_HANDLE_STR  "Invalid Account Handle"

using namespace CPCAPI2::VCCS::Conference;
using CPCAPI2::VCCS::Account::VccsAccountManagerInterface;
using CPCAPI2::VCCS::Account::VccsAccountHandle;

VccsConferenceManagerInterface::VccsConferenceManagerInterface( VccsAccountManagerInterface* pAccountMgr )
   : m_pAccountMgr( pAccountMgr ), m_IOService( pAccountMgr->getIOServiceRef() )
{
}

VccsConferenceManagerInterface::~VccsConferenceManagerInterface()
{
}

void VccsConferenceManagerInterface::Release()
{
   delete this;
}

int VccsConferenceManagerInterface::setHandler(VccsAccountHandle hAccount, VccsConferenceHandler* handler)
{
   int result = kError;
   if( handler != NULL )
   {
      // Just do the normal thing.
      m_IOService.post( std::bind( &VccsConferenceManagerInterface::setHandlerImpl, this, hAccount, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( VccsConferenceManagerInterface *parent, VccsAccountHandle& hAccount, VccsConferenceHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhAccount( hAccount ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhAccount, mHandler );
            mCVar.notify_all();
         }

         VccsConferenceManagerInterface *mParent;
         VccsAccountHandle& mhAccount;
         VccsConferenceHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hAccount, handler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      m_pAccountMgr->process( -1 );
   }
   return result;
}

int VccsConferenceManagerInterface::setHandlerImpl(VccsAccountHandle hAccount, VccsConferenceHandler* handler)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setHandler( handler );
}

int VccsConferenceManagerInterface::getConferenceConnectionInfo( VccsAccountHandle hAccount, const cpc::string& conferenceCode )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::getConferenceConnectionInfoImpl, this, hAccount, conferenceCode ));
   return kSuccess;
}

int VccsConferenceManagerInterface::getConferenceConnectionInfoImpl( VccsAccountHandle hAccount, const cpc::string& conferenceCode )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->getConferenceConnectionInfo( conferenceCode );
}

int VccsConferenceManagerInterface::subscribe(VccsAccountHandle hAccount, const SubscriptionInfo& subinfo )
{
   DebugLog(<< "VccsConferenceManagerInterface::subscribe, hAccount=" << hAccount << " subinfo=" << subinfo);

   m_IOService.post( std::bind( &VccsConferenceManagerInterface::subscribeImpl, this, hAccount, subinfo ));
   return kSuccess;
}

int VccsConferenceManagerInterface::subscribeImpl(VccsAccountHandle hAccount, const SubscriptionInfo& subinfo )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->subscribe( subinfo );
}

int VccsConferenceManagerInterface::subscribe(VccsAccountHandle hAccount, const cpc::string& bridge, const cpc::string& pin, const cpc::string& sipAor, ParticipantType participantType, int capabilities )
{
   SubscriptionInfo info;
   info.bridge = bridge;
   info.pin = pin;
   info.sipAor = sipAor;
   info.participantType = participantType;
   info.capabilities = capabilities;
   return subscribe( hAccount, info );
}

int VccsConferenceManagerInterface::subscribe2(VccsAccountHandle hAccount, const cpc::string& conferenceCode, const cpc::string& pin, const cpc::string& sipAor, ParticipantType participantType, int capabilities )
{
   SubscriptionInfo info;
   info.conferenceCode = conferenceCode;
   info.pin = pin;
   info.sipAor = sipAor;
   info.participantType = participantType;
   info.capabilities = capabilities;
   return subscribe( hAccount, info );
}

int VccsConferenceManagerInterface::unsubscribe(VccsAccountHandle hAccount, const cpc::string& bridge)
{
   DebugLog(<< "VccsConferenceManagerInterface::subscribe, hAccount=" << hAccount << " bridge=" << bridge);

   m_IOService.post( std::bind( &VccsConferenceManagerInterface::unsubscribeImpl, this, hAccount, bridge ));
   return kSuccess;
}

int VccsConferenceManagerInterface::unsubscribeImpl(VccsAccountHandle hAccount, const cpc::string& bridge )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->unsubscribe( bridge );
}

int VccsConferenceManagerInterface::queryConferenceList(VccsAccountHandle hAccount)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::queryConferenceListImpl, this, hAccount ));
   return kSuccess;
}

int VccsConferenceManagerInterface::queryConferenceListImpl(VccsAccountHandle hAccount)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->queryConferenceList();
}

int VccsConferenceManagerInterface::queryConferenceDetails(VccsAccountHandle hAccount,VccsConferenceHandle hConference)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::queryConferenceDetailsImpl, this, hAccount, hConference ));
   return kSuccess;
}

int VccsConferenceManagerInterface::queryConferenceDetailsImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->queryConferenceDetails(hConference);
}

int VccsConferenceManagerInterface::queryConferenceInvite(VccsAccountHandle hAccount,VccsConferenceHandle hConference)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::queryConferenceInviteImpl, this, hAccount, hConference ));
   return kSuccess;
}

int VccsConferenceManagerInterface::queryConferenceInviteImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->queryConferenceInvite(hConference);
}

int VccsConferenceManagerInterface::setParticipantLock(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setParticipantLockImpl, this, hAccount, hConference, locked ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setParticipantLockImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setParticipantLock( hConference, locked );
}

int VccsConferenceManagerInterface::setMuteLock(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setMuteLockImpl, this, hAccount, hConference, locked ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setMuteLockImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setMuteLock( hConference, locked );
}

int VccsConferenceManagerInterface::setHosted(VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool hosted )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setHostedImpl, this, hAccount, hConference, hosted ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setHostedImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool hosted)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setHosted( hConference, hosted );
}

int VccsConferenceManagerInterface::setMuteOthers(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setMuteOthersImpl, this, hAccount, hConference, locked ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setMuteOthersImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool locked)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setMuteOthers( hConference, locked );
}

int VccsConferenceManagerInterface::setEntryExitTonesEnabled(VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool enabled)
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setEntryExitTonesEnabledImpl, this, hAccount, hConference, enabled));
   return kSuccess;
}

int VccsConferenceManagerInterface::setEntryExitTonesEnabledImpl(VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool enabled)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setEntryExitTonesEnabled(hConference, enabled);
}

int VccsConferenceManagerInterface::setRecording(VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled )
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setRecordingImpl, this, hAccount, hConference, enabled));
   return kSuccess;
}

int VccsConferenceManagerInterface::setRecordingImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference,bool enabled )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setRecording(hConference, enabled);
}

int VccsConferenceManagerInterface::setAudioOnlyRecording(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool enabled )
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setAudioOnlyRecordingImpl, this, hAccount, hConference, enabled));
   return kSuccess;
}

int VccsConferenceManagerInterface::setAudioOnlyRecordingImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool enabled )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setAudioOnlyRecording(hConference, enabled);
}

int VccsConferenceManagerInterface::setJoinMuted(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool enabled )
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setJoinMutedImpl, this, hAccount, hConference, enabled));
   return kSuccess;
}

int VccsConferenceManagerInterface::setJoinMutedImpl(VccsAccountHandle hAccount,VccsConferenceHandle hConference, bool enabled )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setJoinMuted(hConference, enabled);
}

int VccsConferenceManagerInterface::kickParticipant(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::kickParticipantImpl, this, hAccount, hConference, hParticipant ));
   return kSuccess;
}

int VccsConferenceManagerInterface::kickParticipantImpl(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->kickParticipant( hConference, hParticipant );
}

int VccsConferenceManagerInterface::muteParticipant(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::muteParticipantImpl, this, hAccount, hConference, hParticipant, mute ));
   return kSuccess;
}

int VccsConferenceManagerInterface::muteParticipantImpl(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool mute)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->muteParticipant( hConference, hParticipant, mute );
}

int VccsConferenceManagerInterface::setIsRecording(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setIsRecordingImpl, this, hAccount, hConference, hParticipant, recording ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setIsRecordingImpl(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, VccsConferenceParticipantHandle hParticipant, bool recording)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setIsRecording( hConference, hParticipant, recording );
}

int VccsConferenceManagerInterface::getXMPPAccountInfo(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool guestAccount )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::getXMPPAccountInfoImpl, this, hAccount, hConference, guestAccount ));
   return kSuccess;
}

int VccsConferenceManagerInterface::getXMPPAccountInfoImpl(
   VccsAccountHandle hAccount, VccsConferenceHandle hConference, bool guestAccount )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->getXMPPAccountInfo( hConference, guestAccount );
}

int VccsConferenceManagerInterface::setScreenSharePresenter(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setScreenSharePresenterImpl, this, hAccount, hConference, hParticipant ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setScreenSharePresenterImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setScreenSharePresenter( hConference, hParticipant );
}

int VccsConferenceManagerInterface::startScreenShare(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant,
   const cpc::string& screenSharingURL,
   const ScreenSharingInfoList& infoList )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::startScreenShareImpl, this, hAccount, hConference, hParticipant, screenSharingURL, infoList ));
   return kSuccess;
}

int VccsConferenceManagerInterface::startScreenShareImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant,
   const cpc::string& screenSharingURL,
   const ScreenSharingInfoList& infoList )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->startScreenShare( hConference, hParticipant, screenSharingURL, infoList );
}

int VccsConferenceManagerInterface::stopScreenShare(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::stopScreenShareImpl, this, hAccount, hConference, hParticipant ));
   return kSuccess;
}

int VccsConferenceManagerInterface::stopScreenShareImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->stopScreenShare( hConference, hParticipant );
}

int VccsConferenceManagerInterface::queryConferenceConfig(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference)
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::queryConferenceConfigImpl, this, hAccount, hConference ));
   return kSuccess;
}

int VccsConferenceManagerInterface::queryConferenceConfigImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->queryConferenceConfig( hConference );
}

int VccsConferenceManagerInterface::setConferenceConfig(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   const ConferenceConfiguration& conferenceConfig,
   const ConferenceConfigurationSet& conferenceConfigSet )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::setConferenceConfigImpl, this, hAccount, hConference, conferenceConfig, conferenceConfigSet ));
   return kSuccess;
}

int VccsConferenceManagerInterface::setConferenceConfigImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   const ConferenceConfiguration& conferenceConfig,
   const ConferenceConfigurationSet& conferenceConfigSet )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->setConferenceConfig( hConference, conferenceConfig, conferenceConfigSet );
}

int VccsConferenceManagerInterface::queryConferenceHistory(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   int offset,
   int count,
   bool includeParticipants,
   HistoryID historyID )
{
   m_IOService.post( std::bind( &VccsConferenceManagerInterface::queryConferenceHistoryImpl, this, hAccount, hConference, offset, count, includeParticipants, historyID ));
   return kSuccess;
}

int VccsConferenceManagerInterface::queryConferenceHistoryImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   int offset,
   int count,
   bool includeParticipants,
   HistoryID historyID )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->queryConferenceHistory( hConference, offset, count, includeParticipants, historyID );
}

int VccsConferenceManagerInterface::deleteHistory(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   const cpc::vector< HistoryID >& historyIDs )
{
   m_IOService.post(std::bind( &VccsConferenceManagerInterface::deleteHistoryImpl, this, hAccount, hConference, historyIDs));
   return kSuccess;
}

int VccsConferenceManagerInterface::deleteHistoryImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   const cpc::vector< HistoryID >& historyIDs )
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo( hAccount );
   if( info == NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLE_STR );

   return info->confImpl->deleteHistory( hConference, historyIDs );
}

int VccsConferenceManagerInterface::setVideoFloorParticipant(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant)
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setVideoFloorParticipantImpl, this, hAccount, hConference, hParticipant));
   return kSuccess;
}

int VccsConferenceManagerInterface::setVideoFloorParticipantImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VccsConferenceParticipantHandle hParticipant)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setVideoFloorParticipant(hConference, hParticipant);
}

int VccsConferenceManagerInterface::setVideoLayout(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VideoLayout videoLayoutType)
{
   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setVideoLayoutImpl, this, hAccount, hConference, videoLayoutType));
   return kSuccess;
}

int VccsConferenceManagerInterface::setVideoLayoutImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VideoLayout videoLayoutType)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setVideoLayout(hConference, videoLayoutType);
}
int VccsConferenceManagerInterface::setVideoResolution(
   VccsAccountHandle hAccount, 
   VccsConferenceHandle hConference, 
   VideoResolution videoResolutionType)
{
   DebugLog(<< __FUNCTION__ << " hAccount=" << hAccount << ", hConference=" << hConference << ", videoResolutionType=" << videoResolutionType);

   m_IOService.post(std::bind(&VccsConferenceManagerInterface::setVideoResolutionImpl, this, hAccount, hConference, videoResolutionType));
   return kSuccess;
}
int VccsConferenceManagerInterface::setVideoResolutionImpl(
   VccsAccountHandle hAccount,
   VccsConferenceHandle hConference,
   VideoResolution videoResolutionType)
{
   Account::AccountInfo *info = m_pAccountMgr->getAccountInfo(hAccount);
   if (info == NULL)
      FIRE_ERROR_AND_RETURN(INVALID_ACCOUNT_HANDLE_STR);

   return info->confImpl->setVideoResolution(hConference, videoResolutionType);
}


namespace CPCAPI2
{
namespace VCCS
{
namespace Conference
{

std::ostream& operator<<(std::ostream& os, const CPCAPI2::VCCS::Conference::SubscriptionInfo& subscriptionInfo)
{
   os << " bridge:" << subscriptionInfo.bridge
      << " conferenceCode:" << subscriptionInfo.conferenceCode
      << " pin: <redacted>"
      << " applicationID:" << subscriptionInfo.applicationID
      << " sipAor:" << subscriptionInfo.sipAor
      << " participantType:" <<  subscriptionInfo.participantType
      << " capabilities:" << subscriptionInfo.capabilities
      << " localte:" << subscriptionInfo.locale;

   return os;
}

}
}
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
