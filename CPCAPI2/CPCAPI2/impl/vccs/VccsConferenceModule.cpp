#include "brand_branded.h"

#include "interface/experimental/vccs/VccsConferenceManager.h"

#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
#include "VccsAccountManagerInterface.h"
#include "VccsConferenceManagerInterface.h"
#include "interface/experimental/vccs/VccsAccountManager.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace VCCS
   {
      namespace Conference
      {
         VccsConferenceManager* VccsConferenceManager::getInterface( CPCAPI2::Phone* cpcPhone )
         {
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)
            Account::VccsAccountManagerInterface* parent = dynamic_cast<Account::VccsAccountManagerInterface*>(Account::VccsAccountManager::getInterface(cpcPhone));
            if (parent == NULL) return NULL;
            PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
            return _GetInterface<VccsConferenceManagerInterface>(phone, "VccsConferenceManager", parent);
#else
            return NULL;
#endif
         }
      }
   }
}
