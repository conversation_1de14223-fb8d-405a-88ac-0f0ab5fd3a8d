#pragma once
#ifndef __CPCAPI2_VCCSINTERNALTYPES_H__
#define __CPCAPI2_VCCSINTERNALTYPES_H__

#include <brand_branded.h>
#if (CPCAPI2_BRAND_VCCS_MODULE == 1)

#include <stdint.h>

#include <websocketpp/config/asio_client.hpp>
#include <websocketpp/client.hpp>
#include <websocketpp/common/thread.hpp>
#include <websocketpp/common/memory.hpp>

#include <vccs/VccsAccountTypes.h>
#include <vccs/VccsConferenceTypes.h>

namespace CPCAPI2
{
   namespace VCCS
   {
      typedef enum ServerErrorCode
      {
         OK,
         UNKNOWN_COMMAND,
         UNAUTHENTICATED,
         NOT_CONNECTED,
         NOT_FOUND, 
         PROTOCOL_MISMATCH,

         CLIENT_CONFIGURATION_ERROR,
         CLIENT_IO_ERROR,
         CLIENT_PARSE_ERROR,
         CLIENT_UNEXPECTED_ERROR,

         SERVER_UNIMPLEMENTED,
         SERVER_UNEXPECTED_ERROR,
         SERVER_DATABASE_PERSIST_ERROR,

         INVALID_REQUEST,
         ACCOUNT_NOT_CONFIGURED,
      	CONFERENCE_NOT_ACTIVE 
      } ServerErrorCode;    

      // Forward decl's
      namespace Conference
      {
         class VccsConferenceManagerImpl;
      }

      // Shared internal types for Account namespace
      namespace Account
      {
         class VccsAccountManagerImpl;

         // Information about the current account, keyed by handle
         typedef struct AccountInfo
         {
            AccountInfo() :
               handle(( VccsAccountHandle ) -1 ),
               acctImpl( NULL ),
               confImpl( NULL )
               {}

            // Handle related to this account
            VccsAccountHandle handle;

            // Pointer to the implementation of the account interface
            VccsAccountManagerImpl *acctImpl;

            // Pointer to the implementation of the conference interface
            Conference::VccsConferenceManagerImpl *confImpl;

         } AccountInfo;
      }
   };
}

#endif // CPCAPI2_BRAND_VCCS_MODULE
#endif // __CPCAPI2_VCCSINTERNALTYPES_H__
