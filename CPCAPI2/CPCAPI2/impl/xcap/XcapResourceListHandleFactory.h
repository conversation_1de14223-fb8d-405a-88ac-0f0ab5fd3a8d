#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_HANDLE_FACTORY_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_HANDLE_FACTORY_H


//includes
#include "cpcapi2defs.h"
#include "xcap/XcapResourceListTypes.h"

namespace CPCAPI2
{
namespace XCAP
{

/**
* 
*/
struct ResourceListCreationInfo
{
   CPCAPI2::SipAccount::SipAccountHandle      account;
   cpc::string                                documentSelector;

   ResourceListCreationInfo()
   {
      account = 0;
      documentSelector = "";
   }
};

typedef std::map<XcapResourceListHandle, ResourceListCreationInfo*> ResourceListCreationInfoMap;

class XcapResourceListHandleFactory
{
public:
   static XcapResourceListHandle getRlsHandle() { return nextResourceListHandle++; }
   static void setRlsHandle(XcapResourceListHandle value) { nextResourceListHandle = value; }

   static XcapResourceListUpdateRequestHandle getRlsUpdateRequestHandle() { return nextResourceListUpdateRequestHandle++; }
   static void setRlsUpdateRequestHandle(XcapResourceListUpdateRequestHandle value) { nextResourceListUpdateRequestHandle = value; }
private:
   static XcapResourceListHandle nextResourceListHandle;
   static XcapResourceListUpdateRequestHandle nextResourceListUpdateRequestHandle;
};

} // XCAP
} // CPCAPI2
#endif // CPCAPI2_XCAP_RESOURCE_LIST_HANDLE_FACTORY_H