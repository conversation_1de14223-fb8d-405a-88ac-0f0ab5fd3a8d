#pragma once

#if !defined(CPCAPI2_XCAP_INTERNAL_INTERFACE_H)
#define CPCAPI2_XCAP_INTERNAL_INTERFACE_H

//includes
#include "cpcapi2defs.h"
#include "xcap/XcapSubscriptionHandler.h"
#include "xcap/XcapPublicationHandler.h"
#include "xcap/XcapSettings.h"
#include "event/SipEventManager.h"
#include "event/SipPublicationManager.h"
#include <rutil/Data.hxx>
#include <string>

namespace CPCAPI2
{
class Phone;

namespace XCAP
{

#define GLOBAL_USER_TREE "global"
#define SPECIFIC_USER_TREE "users"

struct XcapRequestComponents
{
   // location of a document on a server inside xcap root
   resip::Data documentSelector;
   // location of a node inside document
   resip::Data nodeSelector;
   // Application unique ID. pres-rules, resource-lists...
   resip::Data auid;
   // Content type, myme type/mime sub type.
   resip::Data contentType;
   // Set to true if accessing global document
   bool isGlobal;
};

struct PutData
{
   char* data;
   size_t len;
   PutData()
   {
      data = NULL;
      len = 0;
   }
};

struct XCAPResult
{
   long errorCode;
   resip::Data errorMsg;
   resip::Data result;
};

/**
* Internal interface for XCAP basic functionality in the SDK.
*/
class CPCAPI2_SHAREDLIBRARY_API XcapInternalInterface
{
public:
   /**
   * Get a reference to the %XcapInternalInterface interface.
   */
   static XcapInternalInterface* getInterface(Phone* cpcPhone);

   /**
   * Read capabilities of XCAP server. Sets internal variable that is used for checking wheter server supports certain application usages.
   */
   virtual void readXcapCapability(const XcapSettings& settings) = 0;

   /**
   * Set the handler for xcap events on the specified account. Set the handler
   * immediately after creating the account.
   */   
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapSubscriptionHandler* handler) = 0;
   virtual int setPublicationHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapPublicationHandler* handler) = 0;

   /**
   * Function for reading XCAP resource from XCAP server.
   */
   virtual XCAPResult read(const XcapSettings& settings, const XcapRequestComponents& requestComponents) = 0;
   virtual XCAPResult write(const XcapSettings& settings, cpc::string data, const XcapRequestComponents& requestComponents) = 0;
   virtual XCAPResult write(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath) = 0;
   virtual XCAPResult add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, cpc::string data) = 0;
   virtual XCAPResult add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath) = 0;
   virtual XCAPResult remove(const XcapSettings& settings, const XcapRequestComponents& requestComponents) = 0;

   //virtual void subscribe(XcapRequestComponents requestComponents, PutData dataBuffer, unsigned int expires, CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
   * to begin a new outgoing (client) subscription session.
   */
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) = 0;

   /**
   * Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
   * Must be invoked prior to calling start(..) so that the event package and other subscription parameters are configured.
   */
   virtual int applySubscriptionSettings(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SipEventSubscriptionSettings& settings) = 0;

   /**
   * Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
   * Note: To add multiple participants to a subscription session, use this function in concert with the setEventServer(..) function.
   * The format of the targetAddress parameter is sip:<EMAIL>
   */
   virtual int addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) = 0;

   /**
   * Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
   * (see addParticipant(..)) or to the event/resource-list server.
   */
   virtual int start(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
   */
   virtual int end(SipEventSubscriptionHandle subscription) = 0;

   /**
   * Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
   * @param subscription The incoming subscription session to reject.
   * @param rejectReason The SIP response code sent to the originating party.
   */
   virtual int reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason) = 0;

   /**
   * Used to accept an incoming (server) subscription session (200 OK).
   */
   virtual int accept(SipEventSubscriptionHandle subscription, const cpc::string& content) = 0;

   /**
   * Used to send outgoing event state on an incoming (server) subscription session.
   * Sends a NOTIFY request with content as specified by the eventState parameter.
   */
   virtual int notify(SipEventSubscriptionHandle subscription, const cpc::string& content) = 0;
   
   virtual resip::Data createResourceXcapPath(const XcapSettings& settings, const XcapRequestComponents& requestComponents) = 0;

   virtual CPCAPI2::SipEvent::SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, 
                                                         const CPCAPI2::SipEvent::SipEventPublicationSettings& settings) = 0;
   virtual int publish(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::SipEventState& eventState) = 0;
   
protected:
   /*
    * The SDK will manage memory life of %XcapInternalInterface.
    */
   virtual ~XcapInternalInterface() {}
};
}
}
#endif