#include "brand_branded.h"

#include "interface/experimental/xcap/XcapResourceListManager.h"

#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
#include "XcapResourceListManagerInterface.h"
#include "XcapResourceListEventHandler.h"
#endif // CPCAPI2_BRAND_RESOURCE_LIST_MODULE

namespace CPCAPI2
{
namespace XCAP
{

XcapResourceListManager* XcapResourceListManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   CPCAPI2::SipEvent::SipEventManager::getInterface(cpcPhone);
   return _GetInterface<XcapResourceListManagerInterface>(phone, "XcapResourceListInterface");
#else
   return NULL;
#endif
}

}
}
