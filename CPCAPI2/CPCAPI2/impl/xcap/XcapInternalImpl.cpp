#include "brand_branded.h"

#if (CPCAPI2_BRAND_XCAP_MODULE == 1)

#include "cpcapi2utils.h"
#include "XcapInternalImpl.h"
#include "XcapSubscriptionEventHandler.h"
#include <string>
#include <iostream>

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;

namespace CPCAPI2
{
namespace XCAP
{

XcapInternalImpl::XcapInternalImpl(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
   mSipPublicationIf = dynamic_cast<SipEventPublicationManagerInterface*>(SipEventPublicationManager::getInterface(phone));
   xcapCaps = NULL;
}

XcapInternalImpl::~XcapInternalImpl()
{
   for (AccountMap::const_iterator it = mAccountMap.begin(); it!= mAccountMap.end(); it++)
   {
      mSipEventIf->setHandlerImpl(it->first, "xcap-diff", NULL);
      delete it->second;
   }
   mAccountMap.clear();
}

void XcapInternalImpl::Release()
{
   delete this;
}

/**
*   CreateResourceXcapPath function returns UTF-8 encoded XCAP path that points to wanted resource on XCAP server.
*   XCAP path is created from data saved in XCAPSettings structure and XcapRequestComponents structure.
*   In order to be used for resource retrival, path needs to be URL encoded first. URL encoding needs to be 
*   done to the result of this function.
*
*   @customSettings - User specific settings used in xcap path creation. It is same for every request that is created by specific user.
*   @requestComponents - request specific settings used in xcap path creation. Differs for every request and is used for finer determination of resource path
*   return value - xcap resource path suitable for use in curl http request.
*/
resip::Data XcapInternalImpl::createResourceXcapPath(const XcapSettings& settings, const XcapRequestComponents& requestComponents)
{
   resip::Data resource = "http";
   if(settings.port.empty())
   {
      resource += "://" + settings.domain + "/" + settings.xcapRoot + "/" + requestComponents.auid + "/";
   }
   else
   {
      resource += "://" + settings.domain + ":" + settings.port + "/" + settings.xcapRoot + "/" + requestComponents.auid + "/";
   }
   if(requestComponents.isGlobal)
   {
      resource += "global/";
   }
   else
   {
      resource += "users/sip:" + settings.username + "@" + settings.domain + "/";
   }
   resource += requestComponents.documentSelector;
   if(!requestComponents.nodeSelector.empty())

   {
      resource += "/~~/" + requestComponents.nodeSelector;
   }
   resource = resource.urlEncoded();
   return resource;
}

/**
*   Pass a pointer to a function that matches the following prototype: size_t function( char *ptr, size_t size, size_t nmemb, void *userdata); 
*   This function gets called by libcurl as soon as there is data received that needs to be saved. The size of the data pointed to by ptr is size
*   multiplied with nmemb, it will not be zero terminated. Return the number of bytes actually taken care of. If that amount differs from the 
*   amount passed to your function, it'll signal an error to the library. This will abort the transfer and return CURLE_WRITE_ERROR. 
*
*   This is handler function for XcapInternalImpl::read function. It is used to receive data from cURL.
*/
static size_t receiveXcapResource(void *ptr, size_t size, size_t nmemb, void *stream)
{   
   resip::Data* result = (resip::Data*)stream;
   int resultSize = result->size();
   char* buffer = new char[size*nmemb];
   memcpy((void*)buffer, ptr, (size*nmemb));
   result->append(buffer, size*nmemb);
   delete [] buffer;
   return result->size()-resultSize;
}

static size_t receiveHeaders( void *ptr, size_t size, size_t nmemb, void *userdata)
{
   std::map<size_t, resip::Data>* headers = (std::map<size_t, resip::Data>*)userdata;
   resip::Data buffer = (char*)ptr;
 
    int result = 0;
    if (headers != NULL) 
   {
      int separator = buffer.find(":",0);
      int size = buffer.size();
      resip::Data fieldName = buffer.substr(0, separator);
      while(buffer.at(++separator) == ' ' ){}
      resip::Data fieldValue = buffer.substr(separator, size-separator-1);
      headers->insert(std::pair<size_t, resip::Data>(fieldName.hash(), fieldValue));
        result = size;
    }
    return result;       
}

/**
*   This is xcap read function and is used to read resources from xcap server. Resources location is determined with data from XCAPSettings structure
*   from user object and XcapRequestComponents structure passed as parameter.
*   
*   Function creates xcap resource path that determines location of a resource on xcap server, initializes cURL handle, sets cURL options and 
*   calls curl perform in order to make http request and retrive resource described by xcap resource path. After receiveng result, checks it 
*   and repacks it to XCAPResult structure.
*   
*   @requestComponents - request specific data used to determine location of a desired resource on xcap server. It is used in xcap resource path creation.
*   Return value - result structure with result code and result message. In case of error returns error code and error message.
*/
XCAPResult XcapInternalImpl::read(const XcapSettings& settings, const XcapRequestComponents& requestComponents)
{
   CURL *curl;
   CURLcode res;
   XCAPResult xcapResult;
   resip::Data dataBuffer;
   char errorBuffer[CURL_ERROR_SIZE+1];

   resip::Data resourcePath = this->createResourceXcapPath(settings, requestComponents);
   std::map<size_t, resip::Data> headerMap;

   errorBuffer[CURL_ERROR_SIZE] = '\0';

   curl = curl_easy_init();
   if(curl) 
   {
      curl_easy_setopt(curl, CURLOPT_URL, resourcePath.c_str());
      curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, &receiveXcapResource);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, &dataBuffer);
      curl_easy_setopt(curl, CURLOPT_HEADERFUNCTION, &receiveHeaders);
      curl_easy_setopt(curl, CURLOPT_HEADERDATA, &headerMap);
      curl_easy_setopt(curl, CURLOPT_USERNAME, settings.username.c_str());
      curl_easy_setopt(curl, CURLOPT_PASSWORD, settings.password.c_str());
      curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
      /* Perform the request, res will get the return code */ 
      res = curl_easy_perform(curl);
      /* Check for errors */ 
      if(res != CURLE_OK)
      {
         xcapResult.result = "\0";
         xcapResult.errorMsg = errorBuffer;
         xcapResult.errorCode = res;
      }
      else
      {
         curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &xcapResult.errorCode);
         xcapResult.result = dataBuffer; 
         xcapResult.errorMsg = "\0";
         // Read ETag from headers
         EtagMap::iterator it = this->mEtagMap.find(resourcePath.hash());
         if(it == mEtagMap.end())
         {
            this->mEtagMap.insert(std::pair<std::size_t, resip::Data>(resourcePath.hash(), headerMap[resip::Data("ETag").hash()]));
         }
         else
         {
            this->mEtagMap[resourcePath.hash()] = headerMap[resip::Data("ETag").hash()];
         }
      }

      /* always cleanup */ 
      curl_easy_cleanup(curl);
   }
   return xcapResult;
}

/**
*   Pass a pointer to a function that matches the following prototype: size_t function( void *ptr, size_t size, size_t nmemb, void *userdata); 
*   This function gets called by libcurl as soon as it needs to read data in order to send it to the peer. The data area pointed at by the pointer
*   ptr may be filled with at most size multiplied with nmemb number of bytes. Your function must return the actual number of bytes that you stored
*   in that memory area. Returning 0 will signal end-of-file to the library and cause it to stop the current transfer. 
*
*   This is handler function for XcapInternalImpl::write function. It is used to send data to cURL.
*/
static size_t sendXcapResource(void *ptr, size_t size, size_t nmemb, void *stream)
{   
   PutData* userdata = (PutData*)stream;
   size_t curl_size = nmemb * size;
   size_t to_copy = (userdata->len < curl_size) ? userdata->len : curl_size;
   memcpy(ptr, userdata->data, to_copy);
   userdata->data += to_copy;
   userdata->len -= to_copy;
   return to_copy;
}

/**
*   This is xcap write function and is used to write resources to xcap server. Resources location is determined with data from XCAPSettings structure
*   from user object and XcapRequestComponents structure passed as parameter. Resource content type is determined from XcapRequestComponents structure 
*   passed as parameter. Resource itself is passed through dataBuffer.
*   
*   Function creates xcap resource path that determines location of a resource on xcap server, initializes cURL handle, sets cURL options and 
*   calls curl perform in order to make http request and retrive resource described by xcap resource path. After receiveng result, checks it 
*   and repacks it to XCAPResult structure.
*   
*   @dataBuffer -  buffer with resource data to write to xcap server.
*   @requestComponents - request specific data used to determine location of a desired resource on xcap server. It is used in xcap resource path creation.
*   Return value - result structure with result code and result message. In case of error returns error code and error message.
*/
XCAPResult XcapInternalImpl::write(const XcapSettings& settings, cpc::string data, const XcapRequestComponents& requestComponents)
{
   CURL *curl;
   CURLcode res;
   XCAPResult xcapResult;
   PutData dataBuffer;
   char errorBuffer[CURL_ERROR_SIZE+1];
   struct curl_slist *headers = NULL;
   resip::Data contentType = "Content-Type: ";
   char* bufferHandle;

   dataBuffer = stringToPutData(data);
   bufferHandle = dataBuffer.data;
   resip::Data resourcePath = this->createResourceXcapPath(settings, requestComponents);

   errorBuffer[CURL_ERROR_SIZE] = '\0';

   curl = curl_easy_init();
   if(curl) 
   {
      contentType.append( requestComponents.contentType.c_str(), requestComponents.contentType.size());
      headers = curl_slist_append(headers, contentType.c_str());  
      headers = curl_slist_append(headers, "Expect: 100-continue");

      curl_easy_setopt(curl, CURLOPT_URL, resourcePath.c_str());
      curl_easy_setopt(curl, CURLOPT_UPLOAD, 1L);
      curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
      curl_easy_setopt(curl, CURLOPT_READFUNCTION, &sendXcapResource);
      curl_easy_setopt(curl, CURLOPT_READDATA, &dataBuffer);
      curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers); 
      curl_easy_setopt(curl, CURLOPT_USERNAME, settings.username.c_str());
      curl_easy_setopt(curl, CURLOPT_PASSWORD, settings.password.c_str());
      curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
      /* Perform the request, res will get the return code */
      res = curl_easy_perform(curl);
      /* Check for errors */
      if(res != CURLE_OK)
      {
         xcapResult.result = "\0";
         xcapResult.errorMsg = errorBuffer;
         xcapResult.errorCode = res;
      }
      else
      {
         curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &xcapResult.errorCode);
         xcapResult.result = dataBuffer.data;
         xcapResult.errorMsg = "\0";
      }

      /* always cleanup */ 
      curl_easy_cleanup(curl);
   }

   if(bufferHandle != NULL)
   {
      delete [] bufferHandle;
   }

   return xcapResult;
}

/**
*   This is xcap write function and is used to write resources to xcap server. Resources location is determined with data from XCAPSettings structure
*   from user object and XcapRequestComponents structure passed as parameter. Resource content type is determined from XcapRequestComponents structure 
*   passed as parameter. Resource itself is passed through file determined with filePath.
*
*   Function reads data from file determined by filePath and puts it in dataBuffer that is used in call to write(PutData dataBuffer, XcapRequestComponents requestComponents)
*   function together with requestComponents structure. 
*
*   @filePath -  file path to file that contains resource data to write to xcap server.
*   @requestComponents - request specific data used to determine location of a desired resource on xcap server. It is used in xcap resource path creation.
*   Return value - result structure with result code and result message. In case of error returns error code and error message.
*/
XCAPResult XcapInternalImpl::write(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath)
{
   XCAPResult result;
   std::string line = "";
   cpc::string wholeFile = "";
   std::ifstream xmlFile;

   xmlFile.open(filePath.c_str());
   if (!xmlFile.is_open())
   {
      result.errorCode = 404;
      result.errorMsg = "Cannot open file.";
      return result;
   }
   while ( getline(xmlFile,line) )
   {
      wholeFile.append(line.c_str());
   }
   xmlFile.close();                  
   result =  write(settings, wholeFile, requestComponents);

   return result;
}

/**
*   Add function is used to add resources to xcap server. It relies on write function completely. Parameters and return value are same as with write function.
*/
XCAPResult XcapInternalImpl::add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, cpc::string data)
{
   return write(settings, data, requestComponents);
}

/**
*   Add function is used to add resources to xcap server. It relies on write function completely. Parameters and return value are same as with write function.
*/
XCAPResult XcapInternalImpl::add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath)
{
   return write(settings, requestComponents, filePath);
}

/**
*   This is xcap remove function and is used to remove resources from xcap server. Resources location is determined with data from XCAPSettings structure
*   from user object and XcapRequestComponents structure passed as parameter.
*   
*   Function creates xcap resource path that determines location of a resource on xcap server, initializes cURL handle, sets cURL options and 
*   calls curl perform in order to make http request and delete resource described by xcap resource path. After receiveng result, checks it 
*   and repacks it to XCAPResult structure.
*   
*   @requestComponents - request specific data used to determine location of a desired resource on xcap server. It is used in xcap resource path creation.
*   Return value - result structure with result code and result message. In case of error returns error code and error message.
*
*/
XCAPResult XcapInternalImpl::remove(const XcapSettings& settings, const XcapRequestComponents& requestComponents)
{
   CURL *curl;
   CURLcode res;
   XCAPResult xcapResult;
   resip::Data dataBuffer;
   char errorBuffer[CURL_ERROR_SIZE+1];

   resip::Data resourcePath = this->createResourceXcapPath(settings, requestComponents);

   errorBuffer[CURL_ERROR_SIZE] = '\0';
 
    curl = curl_easy_init();
    if(curl) 
   {
      curl_easy_setopt(curl, CURLOPT_URL, resourcePath.c_str());
      curl_easy_setopt(curl, CURLOPT_ERRORBUFFER, errorBuffer);
      curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST,"DELETE");
      curl_easy_setopt(curl, CURLOPT_USERNAME, settings.username.c_str());
      curl_easy_setopt(curl, CURLOPT_PASSWORD, settings.password.c_str());
      curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_DIGEST);
      res = curl_easy_perform(curl);
      if(res != CURLE_OK)
      {
         xcapResult.result = "\0";
         xcapResult.errorMsg = errorBuffer;
         xcapResult.errorCode = res;
      }
      else
      {
         curl_easy_getinfo (curl, CURLINFO_RESPONSE_CODE, &xcapResult.errorCode);
         xcapResult.result = dataBuffer;
         xcapResult.errorMsg = "\0";
      }
        curl_easy_cleanup(curl);
    }
    return xcapResult;
}

/**
*   From RFC 4825: "It will often be necessary for a client to determine what extensions, application usages, or namespaces a server supports
*   before making a request.  To enable that, this specification defines an application usage with the AUID "xcap-caps".  All XCAP servers
*   MUST support this application usage.  This usage defines a single document within the global tree that lists the capabilities of the
*   server.  Clients can read this well-known document, and therefore learn the capabilities of the server."
*
*   This function is used to read capabilities data from xcap server. Every xcap server MUST contain xcap-caps document with all capabilities listed.
*   After capability data is read it is stored in xcapCaps data member in xcap client (this) object. From xcapCaps user can read any time if some AUID 
*   is supported on xcap server.
*/
void XcapInternalImpl::readXcapCapability(const XcapSettings& settings)
{
   XcapRequestComponents requestComp = {"index","","xcap-caps","", true};
   XCAPResult result;
   xmlTextReaderPtr xmlReader;

   if(this->xcapCaps == NULL)
   {
      result = this->read(settings, requestComp);
      if((xmlReader = xmlReaderForMemory(result.result.data(), (int)result.result.size(), NULL, "UTF-8", 128)))
      {
         if(xmlTextReaderRead(xmlReader) == 1)
         {
            this->xcapCaps = xmlTextReaderCurrentDoc(xmlReader);
         }
         else
         {
            this->xcapCaps = NULL;
         }
      }
      else
      {
         this->xcapCaps = NULL;
      }
   }
}

/**
* Allocates a new subscription within the SDK.  This function is used in concert with addParticipant(..) and start(..)
* to begin a new outgoing (client) subscription session.
*/
SipEventSubscriptionHandle XcapInternalImpl::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   return mSipEventIf->createSubscription(account);
}

/**
* Sets parameters for an outgoing subscription session.  Invoked immediately after createSubscription(..)
* Must be invoked prior to calling start(..) so that the event package and other subscription parameters are configured.
*/
int XcapInternalImpl::applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipEventSubscriptionSettings& settings)
{
   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = "xcap-diff";
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(MimeType("application","xcap-diff+xml"));
   return mSipEventIf->applySubscriptionSettings(subscription, eventSettings);
}

/**
* Adds a participant to the subscription session.  Call this function after createSubscription(..) and before start(..).
* Note: To add multiple participants to a subscription session, use this function in concert with the setEventServer(..) function.
* The format of the targetAddress parameter is sip:<EMAIL>
*/
int XcapInternalImpl::addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->addParticipant(subscription, targetAddress);
}

int XcapInternalImpl::addParticipantImpl(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   cpc::string localUri;

   /*SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      SipAccountImpl* acct = mAccountIf->getAccount(ci->account);
      localUri = L"sip:" + acct->getSettings().username + L"@" + acct->getSettings().domain;
   }
   else
   {
      localUri = L"sip:" + customSetings->Username.w_str() + L"@" + customSetings->Domain.w_str();
   }*/
   
   if(targetAddress.empty())
   {
      /*localUri = L"sip:";
      localUri.append(customSetings->Username.w_str());
      localUri.append(L"@");
      localUri.append(customSetings->Domain.w_str());*/
      //+ customSetings->Username.data() + L"@" + customSetings->Domain.data();
   }
   else
   {
      localUri = targetAddress;
   }
   return mSipEventIf->addParticipant(subscription, targetAddress);
}

/**
* Initiates an outgoing (client) subscription session by sending a SUBSCRIBE to the remote participant 
* (see addParticipant(..)) or to the event/resource-list server.
*/
int XcapInternalImpl::start(SipEventSubscriptionHandle subscription)
{
   return mSipEventIf->start(subscription);
}

/**
* Ends a subscription session.  Sends an outgoing SUBSCRIBE with Expires == 0.
*/
int XcapInternalImpl::end(SipEventSubscriptionHandle subscription)
{
   return mSipEventIf->end(subscription);
}

/**
* Used after receiving an incoming subscription session to reject the SUBSCRIBE offered by the remote party.
* @param subscription The incoming subscription session to reject.
* @param rejectReason The SIP response code sent to the originating party.
*/
int XcapInternalImpl::reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason)
{
   return mSipEventIf->reject(subscription, rejectReason);
}

/**
* Used to accept an incoming (server) subscription session (200 OK).
*/
int XcapInternalImpl::accept(SipEventSubscriptionHandle subscription, const cpc::string& content)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::acceptImpl, this, subscription, content));
   return kSuccess;
}

int XcapInternalImpl::acceptImpl(SipEventSubscriptionHandle subscription, const cpc::string& content)
{
   SipEventState eventState;
   eventState.eventPackage = "xcap-diff";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "xcap-diff+xml";
   eventState.contentLength = content.size();
   eventState.contentUTF8 = content.c_str();
   mSipEventIf->accept(subscription, eventState);
   return kSuccess;
}

/**
* Used to send outgoing event state on an incoming (server) subscription session.
* Sends a NOTIFY request with content as specified by the eventState parameter.
*/
int XcapInternalImpl::notify(SipEventSubscriptionHandle subscription, const cpc::string& content)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::notifyImpl, this, subscription, content));
   return kSuccess;
}

int XcapInternalImpl::notifyImpl(SipEventSubscriptionHandle subscription, const cpc::string& content)
{
   SipEventState eventState;
   eventState.eventPackage = "xcap-diff";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "xcap-diff+xml";
   eventState.contentLength = content.size();
   eventState.contentUTF8 = content.c_str();

   return mSipEventIf->notify(subscription, eventState);
}

/**
*
*/
int XcapInternalImpl::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapSubscriptionHandler* handler)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::setHandlerImpl, this, account, handler));
   return kSuccess;
}

/**
*
*/
int XcapInternalImpl::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapSubscriptionHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   XcapSubscriptionEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for XcapInternal::setHandler");
      return kError;
   }

   if (acct->isEnabled())
   {
      mAccountIf->fireError("XcapInternalImpl::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new XcapSubscriptionEventHandler(*acct, *this, *mSipEventIf);
         mAccountMap[account] = evtMan;
      }
      evtMan->setSubscriptionHandler(handler);
      mSipEventIf->setHandlerImpl(account, "xcap-diff", evtMan);
   }
   return kSuccess;
}

/**
*
*/
int XcapInternalImpl::setPublicationHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapPublicationHandler* handler)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::setPublicationHandlerImpl, this, account, handler));
   return kSuccess;
}

/**
*
*/
int XcapInternalImpl::setPublicationHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapPublicationHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   XcapSubscriptionEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for XcapInternal::setPublicationHandler");
      return kError;
   }

   if (acct->isEnabled())
   {
      mAccountIf->fireError("XcapInternalImpl::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new XcapSubscriptionEventHandler(*acct, *this, *mSipEventIf);
         mAccountMap[account] = evtMan;
      }
      evtMan->setPublicationHandler(handler);
      mSipPublicationIf->setHandlerImpl(account, "xcap-diff", evtMan);
   }
   return kSuccess;
}

/**
*
*/
SipEventPublicationHandle XcapInternalImpl::createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationSettings& settings)
{
   SipEventPublicationSettings xcapSettings;
   xcapSettings.eventPackage = "xcap-diff";
   xcapSettings.expiresSeconds = settings.expiresSeconds;
   xcapSettings.supportedMimeTypes.push_back(MimeType("application","xcap-diff+xml"));
   SipEventPublicationHandle publication = mSipPublicationIf->createPublication(account, xcapSettings);

   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::createPublicationImpl, this, account, publication));

   return publication;
}

/**
*
*/
int XcapInternalImpl::createPublicationImpl(CPCAPI2::SipAccount::SipAccountHandle account, const SipEventPublicationHandle& publication)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for XcapInternal::createPublication");
      return kError;
   }

   const SipAccountSettings& actSettings = acct->getSettings();
   cpc::string aor = "sip:" + actSettings.username + "@" + (actSettings.domain.empty() ? "unknown" : actSettings.domain);
   mSipPublicationIf->setTarget(publication, aor);
   return kSuccess;

   //SipAccountImpl* acct = mAccountIf->getAccount(account);
   ////cpc::string aor = L"sip:"; //+ customSetings->Username.w_str() + L"@" + customSetings->Domain.w_str();
   //std::string aor = "sip:"; //+ customSetings->Username.c_str() + "@" + customSetings->Domain.c_str();
   //aor.append(customSetings->Username.c_str());
   //aor.append("@");
   //aor.append(customSetings->Domain.c_str());
   //mSipPublicationIf->setTarget(publication, stringToWstring(aor));
   //return kSuccess;
}

/**
*
*/
int XcapInternalImpl::publish(SipEventPublicationHandle publication, const SipEventState& eventState)
{
   mAccountIf->postToSdkThread(resip::resip_bind(&XcapInternalImpl::publishImpl, this, publication, eventState));
   return kSuccess;
}

/**
*
*/
int XcapInternalImpl::publishImpl(SipEventPublicationHandle publication, const SipEventState& eventState)
{
   SipEventState xcapEventState;
   xcapEventState.eventPackage = "xcap-diff";
   xcapEventState.expiresTimeMs = 3600;
   xcapEventState.mimeType = "application";
   xcapEventState.mimeSubType = "xcap-diff+xml";
   xcapEventState.contentLength = eventState.contentLength;
   xcapEventState.contentUTF8.assign(std::move(eventState.contentUTF8));
   mSipPublicationIf->publish(publication, xcapEventState);
   return kSuccess;
}

PutData XcapInternalImpl::stringToPutData(cpc::string stringData)
{
   PutData buffer;
   buffer.len = stringData.size();
   buffer.data = new char[buffer.len];
   memset(buffer.data, ' ', (sizeof(char)*buffer.len));
   memcpy(buffer.data, stringData.c_str(), stringData.size());
   return buffer;
}

}
}  
#endif
