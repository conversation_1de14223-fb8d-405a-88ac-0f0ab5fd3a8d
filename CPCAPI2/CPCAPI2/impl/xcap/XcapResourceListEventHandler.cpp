#include "brand_branded.h"

#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)

#include "cpcapi2utils.h"
#include "XcapResourceListEventHandler.h"
#include "rutil/Reactor.hxx"

namespace CPCAPI2
{
namespace XCAP
{

XcapResourceListEventHandler::XcapResourceListEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, 
                                                           CPCAPI2::XCAP::XcapResourceListManagerInterface& xcapResourceListManager, 
                                                           CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf)
   : account(acct),
     xcapResourceListManager(xcapResourceListManager),
     sipEventIf(sipEventIf)
{
}

void XcapResourceListEventHandler::setSubscriptionHandler(XcapResourceListHandler* handler)
{
   subscriptionHandler = handler;
}
int XcapResourceListEventHandler::onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args)
{
   return kSuccess;
}
int XcapResourceListEventHandler::onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args)
{
   return kSuccess;
}
int XcapResourceListEventHandler::onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
   XcapResourceListUpdateEvent evt;
   XcapResourceListUpdateRequestHandle updateRequestHandle = atoi(args.eventState.contentUTF8.c_str());
   xcapResourceListManager.getXcapResourceListUpdateEvent(updateRequestHandle, &evt);
   xcapResourceListManager.fireEvent(cpcEvent(XcapResourceListHandler, onResourceListUpdate), evt.resourceList, evt);
   return kSuccess;
}
int XcapResourceListEventHandler::onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args)
{
   return kSuccess;
}
int XcapResourceListEventHandler::onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args)
{
   return kSuccess;
}
int XcapResourceListEventHandler::onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args)
{
   return kSuccess;
}
int XcapResourceListEventHandler::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   CPCAPI2::XCAP::ErrorEvent errorEvent;
   errorEvent.errorText = args.errorText;
   xcapResourceListManager.fireEvent(cpcEvent(XcapResourceListHandler, onError), subscription, errorEvent);
   return kSuccess;
}

int XcapResourceListEventHandler::onResourceListUpdate(CPCAPI2::XCAP::XcapResourceListHandle resourceList, const XcapResourceListUpdateEvent& args)
{
   XcapResourceListUpdateEvent evt;
   evt.resourceList = args.resourceList;
   evt.account = args.account;
   for(unsigned int i=0;i<args.changes.size();i++)
   {
      evt.changes.push_back(args.changes[i]);
   }
   xcapResourceListManager.fireEvent(cpcEvent(XcapResourceListHandler, onResourceListUpdate), resourceList, evt);
   return kSuccess;
}

} // XCAP namespace
} // CPCAPI2 namespace
#endif // CPCAPI2_BRAND_RESOURCE_LIST_MODULE
