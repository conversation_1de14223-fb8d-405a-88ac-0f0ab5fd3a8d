#pragma once

#if !defined(CPCAPI2_XCAP_INTERNAL_IMPL_H)
#define CPCAPI2_XCAP_INTERNAL_IMPL_H


//includes
#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "../event/SipEventManagerInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../event/SipEventPublicationManagerInterface.h"
#include "XcapInternalInterface.h"

#include "curl/curl.h"

#include <fstream>

#include <libxml/tree.h>
#include <libxml/xmlreader.h>

namespace CPCAPI2
{
class Phone;

namespace XCAP
{

class XcapSubscriptionEventHandler;

/**
* Internal interface for XCAP basic functionality in the SDK.
*/
class XcapInternalImpl : public XcapInternalInterface,
                         public PhoneModule
{
public:
   //xcap resource path hash in form of resip::Data.hash() is key, ETag in form of resip::Data is value
   typedef std::map<std::size_t, resip::Data> EtagMap;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapSubscriptionEventHandler*> AccountMap;

   XcapInternalImpl(Phone* phone);
   virtual ~XcapInternalImpl();
   virtual void Release() OVERRIDE;

   virtual void readXcapCapability(const XcapSettings& settings) OVERRIDE;

   /**
   * Set the handler for xcap events on the specified account. Set the handler
   * immediately after creating the account.
   */
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapSubscriptionHandler* handler) OVERRIDE;
   virtual int setPublicationHandler(CPCAPI2::SipAccount::SipAccountHandle account, XcapPublicationHandler* handler) OVERRIDE;

   //geters and setters
   inline EtagMap* getEtagMap(){return &mEtagMap;}

   /**
   * Function for reading XCAP resource from XCAP server.
   */
   virtual XCAPResult read(const XcapSettings& settings, const XcapRequestComponents& requestComponents) OVERRIDE;
   virtual XCAPResult write(const XcapSettings& settings, cpc::string data, const XcapRequestComponents& requestComponents) OVERRIDE;
   virtual XCAPResult write(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath) OVERRIDE;
   virtual XCAPResult add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, cpc::string data) OVERRIDE;
   virtual XCAPResult add(const XcapSettings& settings, const XcapRequestComponents& requestComponents, resip::Data filePath) OVERRIDE;
   virtual XCAPResult remove(const XcapSettings& settings, const XcapRequestComponents& requestComponents) OVERRIDE;

   /**
   * Functions for managing XCAP subscriptions and notifications.
   */
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int applySubscriptionSettings(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SipEventSubscriptionSettings& settings) OVERRIDE;
   virtual int addParticipant(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int start(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int end(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int reject(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, unsigned int rejectReason) OVERRIDE;
   virtual int accept(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& content) OVERRIDE;
   virtual int notify(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& content) OVERRIDE;

   /**
   * Functions for managing XCAP publications.
   */
   virtual CPCAPI2::SipEvent::SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account,
                                                         const CPCAPI2::SipEvent::SipEventPublicationSettings& settings) OVERRIDE;
   virtual int publish(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::SipEventState& eventState) OVERRIDE;

   /**
   * Helper functions
   */
   virtual resip::Data createResourceXcapPath(const XcapSettings& settings, const CPCAPI2::XCAP::XcapRequestComponents& requestComponents) OVERRIDE;
private:
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapSubscriptionHandler* handler);
   int setPublicationHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapPublicationHandler* handler);

   int addParticipantImpl(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& targetAddress);
   int acceptImpl(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const cpc::string& content);
   int notifyImpl(SipEventSubscriptionHandle subscription, const cpc::string& content);
   int createPublicationImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipEvent::SipEventPublicationHandle& publication);
   int publishImpl(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::SipEventState& eventState);
   PutData stringToPutData(cpc::string stringData);

   PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   CPCAPI2::SipEvent::SipEventPublicationManagerInterface* mSipPublicationIf;
   xmlDocPtr xcapCaps;
   AccountMap mAccountMap;
   EtagMap mEtagMap;
   
}; //XcapInternalImpl class

} //XCAP namespace
} //CPCAPI2 namespace
#endif
