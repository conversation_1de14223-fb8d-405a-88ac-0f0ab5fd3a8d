#include "brand_branded.h"

#if (CPCAPI2_BRAND_RESOURCE_LIST_MODULE == 1)

#include "cpcapi2utils.h"
#include "XcapResourceListManagerInterface.h"
#include "XcapResourceListEventHandler.h"

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;

namespace CPCAPI2
{
namespace XCAP
{
XcapResourceListUpdateRequestHandle XcapResourceListHandleFactory::nextResourceListUpdateRequestHandle = 1;
XcapResourceListHandle XcapResourceListHandleFactory::nextResourceListHandle = 1;

XcapResourceListManagerInterface::XcapResourceListManagerInterface(Phone* phone) :
   EventSource2<EventHandler<XCAP::XcapResourceListHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
   mAccountIf(NULL),
   mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
   xcapModule = dynamic_cast<XcapInternalImpl*>(CPCAPI2::XCAP::XcapInternalInterface::getInterface(phone));
}

bool XcapResourceListManagerInterface::initializeResourceList(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& documentSelector)
{
   XcapRequestComponents docPathComponents;
   XCAPResult result;

   docPathComponents.auid = "resource-lists";
   docPathComponents.contentType = "application/resource-lists+xml";
   docPathComponents.isGlobal = false;
   docPathComponents.documentSelector = documentSelector;
   docPathComponents.nodeSelector = "";

   cpc::string s = "<?xml version=\"1.0\" encoding=\"utf-8\"?><resource-lists xmlns=\"urn:ietf:params:xml:ns:resource-lists\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"></resource-lists>";

   AccountXcapSettingsMap::iterator settingsIt = mXcapSettingsMap.find(account);
   if(settingsIt == mXcapSettingsMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::initializeResourceList. Unable to create file on XCAP server. XCAP settings not found for this account." );
      mAccountIf->fireError(msg);
      return false;
   }

   result = xcapModule->add(settingsIt->second, docPathComponents, s);
   if( (result.errorCode < 400) && (result.errorCode > 99) )
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::initializeResourceList. Unable to create file on xcap server. Write to xcap server failed with error code: " 
                           + cpc::to_string(result.errorCode) + "./n"
                           + "Error message: " + result.errorMsg.c_str());
      mAccountIf->fireError(msg);
   }

   return true;
}

int XcapResourceListManagerInterface::setXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings)
{
   postToSdkThread(resip::resip_bind(&XcapResourceListManagerInterface::setXcapSettingsImpl, this, account, xcapSettings));
   return kSuccess;
}

int XcapResourceListManagerInterface::setXcapSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings)
{
   mXcapSettingsMap.insert(std::pair<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapSettings>(account, xcapSettings));
   xcapModule->readXcapCapability(xcapSettings);
   return kSuccess;
}

bool XcapResourceListManagerInterface::findXcapDocument(const cpc::string& documentSelector)
{
   ResourceListCreationInfoMap::const_iterator it = mRlsCreationInfoMap.begin();
   for(; it != mRlsCreationInfoMap.end(); ++it)
   {
      if(it->second->documentSelector.c_str() == documentSelector.c_str())
      {
         return true;
      }
   }
   return false;
}

void XcapResourceListManagerInterface::Release()
{
   delete this;
}

int XcapResourceListManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapResourceListHandler* handler)
{
   postToSdkThread(resip::resip_bind(&XcapResourceListManagerInterface::setHandlerImpl, this, account, handler));
   return kSuccess;
}

int XcapResourceListManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandler* handler)
{ 
   AccountMap::iterator it = mAccountMap.find(account);
   XcapResourceListEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for XcapResourceListManagerInterface::setHandler");
      return kError;
   }

   if (acct->isEnabled())
   {
      mAccountIf->fireError("XcapResourceListManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new XcapResourceListEventHandler(*acct, *this, *mSipEventIf);
         mAccountMap[account] = evtMan;
      }

      auto it = mHandlers.find(account);
      if (mHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }

      mHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }

      mSipEventIf->setHandlerImpl(account, "resource-lists", evtMan);
   }
   return kSuccess;
}

XcapResourceListHandle XcapResourceListManagerInterface::addResourceList(CPCAPI2::SipAccount::SipAccountHandle account, const ResourceList& resourceList, const cpc::string& documentSelector)
{
   XcapResourceListHandle handle = XcapResourceListHandleFactory::getRlsHandle();
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::addResourceListImpl, this, account, handle, resourceList, documentSelector);
   postToSdkThread(cmd);
   return handle;
}

int XcapResourceListManagerInterface::addResourceListImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle listHandle, const ResourceList& resourceList, const cpc::string& documentSelector)
{
   XcapRequestComponents listPathComponents;
   ResourceList rls;
   XCAPResult result;

   if(!findXcapDocument(documentSelector))
   {
      if(!initializeResourceList(account, documentSelector))
      {
         cpc::string msg = cpc::string("XcapResourceListManagerInterface::addResourceListImpl. Resource list not created!! Resource list initialization failed.");
         mAccountIf->fireError(msg);
         return kSuccess;
      }
   }

   AccountXcapSettingsMap::iterator itXcapSettings = mXcapSettingsMap.find(account);
   if(itXcapSettings == mXcapSettingsMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::addResourceListImpl. Resource list not created!! XcapSettings not pressent for specified account.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   if(resourceList.list.itemType != ItemType::List)
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::addResourceListImpl. Resource list not created!! Resource list item has to be of ItemType::List (2) type. "
          "Passed parameter item is of " + cpc::to_string(resourceList.list.itemType) + " type./n");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   listPathComponents.auid = "resource-lists";
   listPathComponents.contentType = "application/xcap-el+xml";
   listPathComponents.isGlobal = false;
   listPathComponents.documentSelector = documentSelector.c_str();
   listPathComponents.nodeSelector = "resource-lists/list[@name=\"" + resourceList.name + "\"]";

   rls.name = resourceList.name;
   rls.list = resourceList.list;

   cpc::string s = resourceListItemToWstring(rls.list);

   result = xcapModule->add(itXcapSettings->second, listPathComponents, s);
   if( (result.errorCode < 400) && (result.errorCode > 99) )
   {
      mResourceListCache.insert(std::pair<XcapResourceListHandle, ResourceList>(listHandle, rls));
      ResourceListCreationInfo* rlsInfo = new ResourceListCreationInfo();
      rlsInfo->account = account;
      rlsInfo->documentSelector = documentSelector;
      mRlsCreationInfoMap[listHandle] = rlsInfo;
   }
   else
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Resource list not created!! Write to xcap server failed with error code: " 
                           + cpc::to_string(result.errorCode) + "./n"
                           + "Error message: " + result.errorMsg.c_str());
      mAccountIf->fireError(msg);
   }
   return kSuccess;
}

int XcapResourceListManagerInterface::removeResourceList(XcapResourceListHandle listHandle)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::removeResourceListImpl, this, listHandle);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::removeResourceListImpl(XcapResourceListHandle listHandle)
{
   XcapRequestComponents listPathComponents;
   XCAPResult result;
   ResourceList resourceList = mResourceListCache[listHandle];

   ResourceListCreationInfoMap::iterator rlsInfoIt = mRlsCreationInfoMap.find(listHandle);
   if(rlsInfoIt == mRlsCreationInfoMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::removeResourceListImpl. Resource list creation info not found. Can't remove list.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   AccountXcapSettingsMap::iterator itXcapSettings = mXcapSettingsMap.find(rlsInfoIt->second->account);
   if(itXcapSettings == mXcapSettingsMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::removeResourceListImpl. Can't read XCAP server. XcapSettings not pressent for specified account.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   ResourceListCache::iterator it = mResourceListCache.find(listHandle);
   if(it == mResourceListCache.end())
   {
      return kError;
   }
   else
   {
      resourceList = it->second;
   }

   listPathComponents.auid = "resource-lists";
   listPathComponents.contentType = "application/xcap-el+xml";
   listPathComponents.isGlobal = false;
   listPathComponents.documentSelector = mRlsCreationInfoMap[listHandle]->documentSelector.c_str();
   listPathComponents.nodeSelector = "resource-lists/list[@name=\"" + resourceList.name + "\"]";

   result = xcapModule->remove(itXcapSettings->second, listPathComponents);
   if( (result.errorCode < 400) && (result.errorCode > 99) )
   {
      resourceList.list.list.clear();
      mResourceListCache.erase(listHandle);
   }
   else
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Delete request to xcap server failed with error code: " + cpc::to_string(result.errorCode) + "./n"
                           + "Error message: " + result.errorMsg.c_str());
      mAccountIf->fireError(msg);
   }
   return kSuccess;
}

int XcapResourceListManagerInterface::queryResourceList(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle handle)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::queryResourceListImpl, this, account, handle);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::queryResourceListImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle handle)
{
   ResourceList list;
   XcapResourceListUpdateEvent rlsEvent;
   ChangeItem item;

   ResourceListCache::iterator it = mResourceListCache.find(handle);
   if(it != mResourceListCache.end())
   {
      list = it->second;
   }
   else
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::queryResourceList. Resource list not found. Can't retrive list.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   item.changeType = ChangeType::ChangeType_Query;
   item.changedItem = list.list;

   rlsEvent.changes.push_back(item);
   rlsEvent.account = account;
   rlsEvent.resourceList = handle;

   mAccountMap[account]->onResourceListUpdate(handle, rlsEvent);
   return kSuccess;
}

int XcapResourceListManagerInterface::getResourceList(XcapResourceListHandle list, ResourceList& resourceList)
{
   ResourceListCache::iterator it = mResourceListCache.find(list);
   if(it == mResourceListCache.end())
   {
      return kError;
   }
   else
   {
      resourceList.name = it->second.name;
      for(cpc::vector<CPCAPI2::XCAP::ResourceListItem>::iterator it = resourceList.list.list.begin(); it!=resourceList.list.list.end(); ++it)
      {
         resourceList.list.list.push_back(*it);
      }
   }
   return kSuccess;
}

XcapResourceListUpdateRequestHandle XcapResourceListManagerInterface::createUpdateRequest(XcapResourceListHandle resourceListHandle)
{
   XcapResourceListUpdateRequestHandle handle = XcapResourceListHandleFactory::getRlsUpdateRequestHandle();
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::createUpdateRequestImpl, this, handle, resourceListHandle);
   postToSdkThread(cmd);
   return handle;
}

int XcapResourceListManagerInterface::createUpdateRequestImpl(XcapResourceListUpdateRequestHandle h, XcapResourceListHandle resourceListHandle)
{
   ResourceListCreationInfoMap::iterator rlsInfoIt = mRlsCreationInfoMap.find(resourceListHandle);
   if(rlsInfoIt == mRlsCreationInfoMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::createUpdateRequestImpl. Resource list creation info not found. Can't create update request.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   SipAccountImpl* acct = mAccountIf->getAccountImpl(rlsInfoIt->second->account);
   if (NULL == acct)
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::createUpdateRequestImpl called with invalid account handle: " + cpc::to_string(rlsInfoIt->second->account));
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   XcapResourceListUpdateEvent updateEvent;
   updateEvent.resourceList = resourceListHandle;
   updateEvent.account = rlsInfoIt->second->account;
   updateMap[h] = updateEvent;
   return kSuccess;
}

int XcapResourceListManagerInterface::addItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::addItemImpl, this, rosterRequest, resourceListItem);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::addItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   return addItemChangeRequest(ChangeType::ChangeType_Add, rosterRequest, resourceListItem);
}

int XcapResourceListManagerInterface::removeItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::removeItemImpl, this, rosterRequest, resourceListItem);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::removeItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   return addItemChangeRequest(ChangeType::ChangeType_Remove, rosterRequest, resourceListItem);
}

int XcapResourceListManagerInterface::updateItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::updateItemImpl, this, rosterRequest, resourceListItem);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::updateItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   return addItemChangeRequest(ChangeType::ChangeType_Update, rosterRequest, resourceListItem);
}

int XcapResourceListManagerInterface::start(XcapResourceListUpdateRequestHandle rosterRequest)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&XcapResourceListManagerInterface::startImpl, this, rosterRequest);
   postToSdkThread(cmd);
   return kSuccess;
}

int XcapResourceListManagerInterface::startImpl(XcapResourceListUpdateRequestHandle rosterRequest)
{
   ResourceList currentList;
   XCAPResult result;
   cpc::vector<CPCAPI2::XCAP::ResourceListItem>::iterator currentIt;

   AccountXcapSettingsMap::iterator itXcapSettings = mXcapSettingsMap.find(updateMap[rosterRequest].account);
   if(itXcapSettings == mXcapSettingsMap.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Can't read XCAP server. XcapSettings not pressent for specified account.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }

   XcapResourceListHandle listHandle = updateMap[rosterRequest].resourceList;
   ResourceListCache::iterator it = mResourceListCache.find(listHandle);
   if(it == mResourceListCache.end())
   {
      cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Can't read XCAP server. XcapSettings not pressent for specified account.");
      mAccountIf->fireError(msg);
      return kSuccess;
   }
   else
   {
      currentList = it->second;
   }

   for(cpc::vector<ChangeItem>::iterator it = updateMap[rosterRequest].changes.begin(); it != updateMap[rosterRequest].changes.end(); ++it) 
   {
      XcapRequestComponents elementChange;
      elementChange.auid = "resource-lists";
      elementChange.contentType = "application/xcap-el+xml";
      elementChange.isGlobal = false;
      elementChange.documentSelector = mRlsCreationInfoMap[listHandle]->documentSelector.c_str();
      cpc::string nodeString = createNodeSelector(it->changedItem, currentList).c_str();
      resip::Data node(nodeString, nodeString.size());
      elementChange.nodeSelector = node;

      cpc::string s = resourceListItemToWstring(it->changedItem);

      switch(it->changeType)
      {
         case ChangeType::ChangeType_Add : 
         {
            result = xcapModule->add(itXcapSettings->second, elementChange, s);
            if( (result.errorCode < 400) && (result.errorCode > 99) )
            {
               currentList.list.list.push_back(it->changedItem);
            }
            else
            {
               cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Write to xcap server failed with error code: " 
                                    + cpc::to_string(result.errorCode) + "./n"
                                    + "Error message: " + result.errorMsg.c_str());
               mAccountIf->fireError(msg);
               return kSuccess;
            }
            break;
         }
         case ChangeType::ChangeType_Remove : 
         {
            result = xcapModule->remove(itXcapSettings->second, elementChange);
            if( (result.errorCode < 400) && (result.errorCode > 99) )
            {
               for(currentIt=currentList.list.list.begin(); currentIt!=currentList.list.list.end(); ++currentIt)
               {
                  if(std::strcmp(currentIt->id.c_str(), it->changedItem.id.c_str()) == 0)
                  {
                     currentList.list.list.erase(currentIt);
                     break;
                  }
               }
            }
            else
            {
               cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Write to xcap server failed with error code: " 
                                    + cpc::to_string(result.errorCode) + "./n"
                                    + "Error message: " + result.errorMsg.c_str());
               mAccountIf->fireError(msg);
               return kSuccess;
            }
            break;
         }
         case ChangeType::ChangeType_Update : 
         {
            result = xcapModule->write(itXcapSettings->second, s, elementChange);
            if( (result.errorCode < 400) && (result.errorCode > 99) )
            {
               for(unsigned int i=0; i<=currentList.list.list.size(); i++)
               {
                  if(std::strcmp(currentList.list.list[i].id.c_str(), it->changedItem.id.c_str()) == 0)
                  {
                     currentList.list.list[i] = it->changedItem;
                     break;
                  }
               }

               
            }
            else
            {
               cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl. Write to xcap server failed with error code: " 
                                    + cpc::to_string(result.errorCode) + "./n"
                                    + "Error message: " + result.errorMsg.c_str());
               mAccountIf->fireError(msg);
               return kSuccess;
            }
            break;
         }
         default : 
         {
            cpc::string msg = cpc::string("XcapResourceListManagerInterface::startImpl.  Unknown ChangeType. ");
            mAccountIf->fireError(msg);
            return kSuccess;
            break;
         }
      }
   }
   XcapResourceListUpdateEvent rlsChangesEvent = updateMap[rosterRequest];
   mAccountMap[rlsChangesEvent.account]->onResourceListUpdate(listHandle, rlsChangesEvent);
   updateMap.erase(rosterRequest);
   return kSuccess;
}

cpc::string XcapResourceListManagerInterface::fieldsToString(cpc::vector<CPCAPI2::XCAP::Field> fields)
{
   cpc::string result;
   for(unsigned int i=0;i<fields.size();i++)
   {
      result.append("<");
      result.append(fields[i].name);
      result.append(">");
      result.append(fields[i].value);
      result.append("</");
      result.append(fields[i].name);
      result.append(">");
   }
   return result;
}

cpc::string XcapResourceListManagerInterface::resourceListItemToWstring(ResourceListItem& item)
{
   cpc::string xmlNode = "";

   switch(item.itemType)
   {
   case ItemType::List : 
      {
         xmlNode.append("<list name=\"");
         xmlNode.append(item.id);
         xmlNode.append("\">");
         if(!item.displayName.empty())
         {
            xmlNode.append("<display-name>");
            xmlNode.append(item.displayName);
            xmlNode.append("</display-name>");
         }
         if(!item.list.empty())
         {
            for(cpc::vector<ResourceListItem>::iterator it = item.list.begin(); it != item.list.end(); ++it) 
            {
               xmlNode.append(resourceListItemToWstring(*it));
            }
         }
         xmlNode.append("</list>");
         break;
      }
   case ItemType::Entry : 
      {
         xmlNode.append("<entry uri=\"");
         xmlNode.append(item.id);
         xmlNode.append("\">");
         xmlNode.append("<display-name>");
         xmlNode.append(item.displayName);
         xmlNode.append("</display-name>");
         if(!item.fields.empty())
         {
            xmlNode.append(fieldsToString(item.fields));
         }
         xmlNode.append("</entry>");
         break;
      }
   case ItemType::Entry_Ref : 
      {
         xmlNode.append("<entry-ref ref=\"");
         xmlNode.append(item.id);
         xmlNode.append("\">");
         if(!item.fields.empty())
         {
            xmlNode.append(fieldsToString(item.fields));
         }
         xmlNode.append("</entry-ref>");
         break;
      }
   case ItemType::External : 
      {
         xmlNode.append("<external anchor=\"");
         xmlNode.append(item.id);
         xmlNode.append("\">");
         xmlNode.append("<display-name>");
         xmlNode.append(item.displayName);
         xmlNode.append("</display-name>");
         if(!item.fields.empty())
         {
            xmlNode.append(fieldsToString(item.fields));
         }
         xmlNode.append("</external>");
         break;
      }
   default : {}
   }

   return xmlNode;
}

ResourceListItem XcapResourceListManagerInterface::wstringToResourceListItem(const cpc::string& xmlString)
{
   ResourceListItem item;
   return item;
}

cpc::string XcapResourceListManagerInterface::createNodeSelector(const ResourceListItem& item, const ResourceList& list)
{
   cpc::string nodeSelector = "resource-lists/";
   nodeSelector.append("list[@name=\"");
   nodeSelector.append(list.name);
   nodeSelector.append("\"]/");
   
   switch(item.itemType)
   {
   case ItemType::List : 
      {
         nodeSelector.append("list[@name=\"");
         break;
      }
   case ItemType::Entry : 
      {
         nodeSelector.append("entry[@uri=\"");
         break;
      }
   case ItemType::Entry_Ref : 
      {
         nodeSelector.append("entry_ref[@ref=\"");
         break;
      }
   case ItemType::External : 
      {
         nodeSelector.append("external[@anchor=\"");
         break;
      }
   default : {}
   }
   nodeSelector.append(item.id);
   nodeSelector.append("\"]");

   return nodeSelector;
}

int XcapResourceListManagerInterface::getXcapResourceListUpdateEvent(XcapResourceListUpdateRequestHandle handle, XcapResourceListUpdateEvent* updateEvent)
{
   XcapResourceListUpdateMap::iterator it = updateMap.find(handle);
   if(it != updateMap.end())
   {
      *updateEvent = it->second;
      return kSuccess;
   }
   else
   {
      return kError;
   }
}

int XcapResourceListManagerInterface::addItemChangeRequest(ChangeType itemChangeType, XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem)
{
   ChangeItem newItem;
   newItem.changedItem = resourceListItem;
   newItem.changeType = itemChangeType;
   updateMap[rosterRequest].changes.push_back(newItem);
   return kSuccess;
}

std::ostream& operator<<(std::ostream& os, const XcapResourceListUpdateEvent& evt)
{
   return os << "XcapResourceListUpdateEvent";
}

std::ostream& operator<<(std::ostream& os, const XcapResourceListUpdateFailedEvent& evt)
{
   return os << "XcapResourceListUpdateFailedEvent";
}

std::ostream& operator<<(std::ostream& os, const XCAP::ErrorEvent& evt)
{
   return os << "XCAP::ErrorEvent";
}

} // XCAP namespace
} // CPCAPI2 namespace
#endif // CPCAPI2_XCAP_MODULE
