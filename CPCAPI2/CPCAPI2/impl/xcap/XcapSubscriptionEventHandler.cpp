#include "brand_branded.h"

#if (CPCAPI2_BRAND_XCAP_MODULE == 1)
#include "cpcapi2utils.h"
#include "XcapSubscriptionEventHandler.h"

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace XCAP
{

XcapSubscriptionEventHandler::XcapSubscriptionEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, 
                                             CPCAPI2::XCAP::XcapInternalImpl& xcapInternal, 
                                             CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf)
   : subscriptionHandler(NULL),
     publicationHandler(NULL),
     account(acct),
     xcapInternal(xcapInternal),
     sipEventIf(sipEventIf)
{
}

void XcapSubscriptionEventHandler::setSubscriptionHandler(XcapSubscriptionHandler* handler)
{
   subscriptionHandler = handler;
}

void XcapSubscriptionEventHandler::setPublicationHandler(XcapPublicationHandler* handler)
{
   publicationHandler = handler;
}

int XcapSubscriptionEventHandler::onNewSubscription(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args)
{
   NewXcapSubscriptionEvent xcapArgs;
   xcapArgs.account = account.getHandle();
   xcapArgs.eventPackage = args.eventPackage;
   xcapArgs.remoteAddress = args.remoteAddress;
   xcapArgs.remoteDisplayName = args.remoteDisplayName;
   xcapArgs.subscriptionType = args.subscriptionType;
   for(unsigned int i=0;i<xcapArgs.supportedMimeTypes.size();i++)
   {
      xcapArgs.supportedMimeTypes[i] = args.supportedMimeTypes[i];
   }

   ReadCallbackBase* cb = makeFpCommand(
      XcapSubscriptionHandler::onNewSubscription,
      subscriptionHandler,
      subscription,
      xcapArgs);
   account.getAccountInterface()->postToSdkThread(cb);
   return kSuccess;
}

int XcapSubscriptionEventHandler::onSubscriptionEnded(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args)
{
      switch (args.endReason)
      {
      case SipSubscriptionEndReason::SipSubscriptionEndReason_ServerEnded : 
         {
            if(args.isNotifyTerminated)
            {
               // Terminated because of notify. It is intentional unsubscribe, so remove it from etag map.
               resip::Data content(args.remoteAddress.c_str(), args.remoteAddress.size());
               xcapInternal.getEtagMap()->erase(content.hash());
            }
            if(args.initialSubscribe)
            {
               // Initial subscribe failed. No need to remove eteg from EtagMap.
            }
            //break;
         }
      case SipSubscriptionEndReason::SipSubscriptionEndReason_ServerError : 
         {
            /*SubscriptionEndedEvent xcapArgs = args;

            ReadCallbackBase* cb = makeFpCommand(
               XcapSubscriptionHandler::onSubscriptionEnded,
               subscriptionHandler,
               subscription,
               xcapArgs);
            account.getAccountInterface()->postToSdkThread(cb);
            break;*/
         }
      case SipSubscriptionEndReason::SipSubscriptionEndReason_Unknown : 
         {
            XcapSubscriptionEndedEvent xcapArgs;
            xcapArgs.endReason = args.endReason;
            xcapArgs.initialSubscribe = args.initialSubscribe;
            xcapArgs.isNotifyTerminated = args.isNotifyTerminated;
            xcapArgs.reason = args.reason;
            xcapArgs.remoteAddress = args.remoteAddress;
            xcapArgs.retryAfter = args.retryAfter;
            xcapArgs.statusCode = args.statusCode;
            xcapArgs.subscriptionType = args.subscriptionType;

            ReadCallbackBase* cb = makeFpCommand(
               XcapSubscriptionHandler::onSubscriptionEnded,
               subscriptionHandler,
               subscription,
               xcapArgs);
            account.getAccountInterface()->postToSdkThread(cb);
            break;
         }
      default:
         break;
      }
   //}
   return kSuccess;
}

int XcapSubscriptionEventHandler::onIncomingEventState(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
   if(args.eventState.eventPackage == "xcap-diff")
   {
      if(args.eventState.contentUTF8.empty())
      {
         return kSuccess;
      }
      resip::Data notifyData(args.eventState.contentUTF8, args.eventState.contentUTF8.size());
      //transfer received data to xml
      updateEtag(notifyData);

      IncomingXcapEventStateEvent xcapArgs;
      xcapArgs.eventState.contentLength = args.eventState.contentLength;
      xcapArgs.eventState.contentUTF8 = args.eventState.contentUTF8;
      xcapArgs.eventState.eventPackage = args.eventState.eventPackage;
      xcapArgs.eventState.expiresTimeMs = args.eventState.expiresTimeMs;
      xcapArgs.eventState.mimeSubType = args.eventState.mimeSubType;
      xcapArgs.eventState.mimeType = args.eventState.mimeType;

      ReadCallbackBase* cb = makeFpCommand(
         XcapSubscriptionHandler::onIncomingEventState,
         subscriptionHandler,
         subscription,
         xcapArgs);
      account.getAccountInterface()->postToSdkThread(cb);
      //TODO: Add read document call here??? This call would refresh clients copy of a document.
      // This would be clients reaction to change of document.
   }
   return kSuccess;
}

int XcapSubscriptionEventHandler::onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args)
{
   XcapSubscriptionStateChangedEvent xcapArgs;
   xcapArgs.subscriptionState = args.subscriptionState;

   ReadCallbackBase* cb = makeFpCommand(
      XcapSubscriptionHandler::onSubscriptionStateChanged,
      subscriptionHandler,
      subscription,
      xcapArgs);
   account.getAccountInterface()->postToSdkThread(cb);

   return kSuccess;
}

int XcapSubscriptionEventHandler::onNotifySuccess(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args)
{
   return kSuccess;
}

int XcapSubscriptionEventHandler::onNotifyFailure(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args)
{
   return kSuccess;
}

int XcapSubscriptionEventHandler::onError(SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   ErrorEvent evt;
   evt.errorText = args.errorText;
   ReadCallbackBase* cb = makeFpCommand(
      XcapSubscriptionHandler::onError,
      subscriptionHandler,
      subscription,
      evt);
   account.getAccountInterface()->postToSdkThread(cb);

   return kSuccess;
}

void XcapSubscriptionEventHandler::updateEtag(resip::Data notifyData)
{
   xmlTextReaderPtr xmlReader;
   xmlDocPtr xmlNotifyData = NULL;
   xmlNodePtr notifyListNode = NULL;
   xmlAttrPtr notifySelAttr = NULL;
   xmlAttrPtr notifyETagAttr = NULL;
   resip::Data etag = NULL;

   if((xmlReader = xmlReaderForMemory(notifyData.data(), (int)notifyData.size(), NULL, "UTF-8", 128)))
   {
      if(xmlTextReaderRead(xmlReader) == 1)
      {
         xmlNotifyData = xmlTextReaderCurrentDoc(xmlReader);
      }
   }
   if(xmlNotifyData)
   {
      xmlChar xmlDocNode[] = "document";
      notifyListNode = xmlStringGetNodeList(xmlNotifyData, xmlDocNode);
      while(notifyListNode)
      {
         xmlChar xmlSelNode[] = "sel";
         if((notifySelAttr = xmlHasProp(notifyListNode, xmlSelNode)))
         {
            resip::Data content(notifySelAttr->children->content, xmlStrlen(notifySelAttr->children->content));
            XcapInternalImpl::EtagMap* xcapEtagMap = xcapInternal.getEtagMap();
            XcapInternalImpl::EtagMap::iterator it = xcapEtagMap->find(content.hash());
            if(it == xcapEtagMap->end())
            {
               // First subscription. Add new etag entry.
               xmlChar xmlNewEtagNode[] = "new-etag";
               if((notifyETagAttr = xmlHasProp(notifyListNode, xmlNewEtagNode)))
               {
                  etag = resip::Data(notifyETagAttr->children->content, xmlStrlen(notifyETagAttr->children->content));
                  xcapEtagMap->insert(std::pair<std::size_t, resip::Data>(content.hash(), etag));
               }
            }
            else
            {
               // Changes made to xcap resource. Update value.
               xmlChar xmlPreviousEtagNode[] = "previous-etag";
               if(/*notifyETagAttr = */xmlHasProp(notifyListNode, xmlPreviousEtagNode))
               {
                  etag = (*xcapEtagMap)[content.hash()];
               }
               xmlChar xmlNewEtag1Node[] = "new-etag";
               if((notifyETagAttr = xmlHasProp(notifyListNode, xmlNewEtag1Node)))
               {
                  etag = resip::Data(notifyETagAttr->children->content, xmlStrlen(notifyETagAttr->children->content));
               }
            }
         }
         notifyListNode = notifyListNode->next;
      }
   }
}

int XcapSubscriptionEventHandler::onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PublicationSuccessEvent& args)
{
   if (!publicationHandler)
   {
      return kSuccess;
   }
   XcapPublicationSuccessEvent evt;
   ReadCallbackBase* cb = makeFpCommand(
      XcapPublicationHandler::onPublicationSuccess,
      publicationHandler,
      publication,
      evt);
   account.getAccountInterface()->postToSdkThread(cb);
   return kSuccess;
}
int XcapSubscriptionEventHandler::onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PublicationFailureEvent& args)
{
   if (!publicationHandler)
   {
      return kSuccess;
   }
   XcapPublicationFailureEvent evt;
   evt.reason = args.reason;
   ReadCallbackBase* cb = makeFpCommand(
      XcapPublicationHandler::onPublicationFailure,
      publicationHandler,
      publication,
      evt);
   account.getAccountInterface()->postToSdkThread(cb);
   switch(evt.reason)
   {
      case SipPublicationFailureReason_Unknown : 
         {
            std::cout << "SipPublicationFailureReason_Unknown" << std::endl; 
            account.fireAccountError( "SipPublicationFailureReason_Unknown" );
            break;
         }
      case SipPublicationFailureReason_ServerError : 
         {
            std::cout << "SipPublicationFailureReason_ServerError" << std::endl; 
            account.fireAccountError( "SipPublicationFailureReason_ServerError" );
            break;
         }
      case SipPublicationFailureReason_ConditionalRequestFailed : 
         {
            std::cout << "SipPublicationFailureReason_ConditionalRequestFailed" << std::endl; 
            account.fireAccountError( "SipPublicationFailureReason_ConditionalRequestFailed" );
            break;
         }
      default: break;
   }
   return kSuccess;
}
int XcapSubscriptionEventHandler::onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PublicationRemoveEvent & args)
{
   if (!publicationHandler)
   {
      return kSuccess;
   }
   XcapPublicationRemoveEvent evt;
   ReadCallbackBase* cb = makeFpCommand(
      XcapPublicationHandler::onPublicationRemove,
      publicationHandler,
      publication,
      evt);
   account.getAccountInterface()->postToSdkThread(cb);
   return kSuccess;
}
int XcapSubscriptionEventHandler::onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const PublicationErrorEvent& args)
{
   if (!publicationHandler)
   {
      return kSuccess;
   }
   XcapPublicationErrorEvent evt;
   evt.errorText = args.errorText;
   ReadCallbackBase* cb = makeFpCommand(
      XcapPublicationHandler::onError,
      publicationHandler,
      publication,
      evt);
   account.getAccountInterface()->postToSdkThread(cb);
   return kSuccess;
}

} // XCAP
} // CPCAPI2
#endif
