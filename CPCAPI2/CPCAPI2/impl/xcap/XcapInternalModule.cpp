#include "brand_branded.h"

#include "impl/xcap/XcapInternalInterface.h"

#if (CPCAPI2_BRAND_XCAP_MODULE == 1)
#include "XcapInternalImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif // CPCAPI2_BRAND_XCAP_MODULE

namespace CPCAPI2
{
namespace XCAP
{

XcapInternalInterface* XcapInternalInterface::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_XCAP_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<XcapInternalImpl>(phone, "XcapInternalInterface");
#else
   return NULL;
#endif
}

} // XCAP
} // CPCAPI2
