#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_INTERFACE_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_INTERFACE_H


//includes
#include "cpcapi2defs.h"
#include "../event/SipEventManagerInterface.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "xcap/XcapResourceListManager.h"
#include "XcapResourceListHandleFactory.h"
#include "XcapInternalImpl.h"
#include "xcap/XcapResourceListTypes.h"

namespace CPCAPI2
{
class Phone;

namespace XCAP
{
class XcapResourceListEventHandler;

/**
* Implementation of resource list manager.
*/
class XcapResourceListManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<CPCAPI2::XCAP::XcapResourceListHand<PERSON>, CPCAPI2::SipAccount::SipAccountHandle> >,
                                         public XcapResourceListManager,
                                         public PhoneModule
{
public:
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapResourceListEventHandler*> AccountMap;
   typedef std::map<XcapResourceListHandle, ResourceList> ResourceListCache;
   typedef std::map<cpc::string, XcapResourceListHandle> ResourceListUriMap;
   typedef std::map<XcapResourceListUpdateRequestHandle, XcapResourceListUpdateEvent> XcapResourceListUpdateMap;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapSettings> AccountXcapSettingsMap;

   XcapResourceListManagerInterface(Phone* phone);
   void Release();

   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapResourceListHandler* handler);
   virtual int setXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings);

   virtual XcapResourceListHandle addResourceList(CPCAPI2::SipAccount::SipAccountHandle account, const ResourceList& resourceList, const cpc::string& documentSelector);
   virtual int removeResourceList(XcapResourceListHandle listHandle);
   virtual int queryResourceList(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle handle);
   virtual int getResourceList(XcapResourceListHandle list, ResourceList& resourceList);
   virtual XcapResourceListUpdateRequestHandle createUpdateRequest(XcapResourceListHandle resourceListHandle);
   virtual int addItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   virtual int removeItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   virtual int updateItem(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   virtual int start(XcapResourceListUpdateRequestHandle rosterRequest);

   int getXcapResourceListUpdateEvent(XcapResourceListUpdateRequestHandle handle, XcapResourceListUpdateEvent* updateEvent);
   inline XcapInternalInterface* getXcapManager(){return xcapModule;}

private:
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandler* handler);
   int setXcapSettingsImpl(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings);
   int addResourceListImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle listHandle, const ResourceList& resourceList, const cpc::string& documentSelector);
   int removeResourceListImpl(XcapResourceListHandle listHandle);
   int queryResourceListImpl(CPCAPI2::SipAccount::SipAccountHandle account, XcapResourceListHandle handle);
   int createUpdateRequestImpl(XcapResourceListUpdateRequestHandle h, XcapResourceListHandle resourceListHandle);
   int addItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   int removeItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   int updateItemImpl(XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);
   int startImpl(XcapResourceListUpdateRequestHandle rosterRequest);
   cpc::string resourceListItemToWstring(ResourceListItem& item);
   ResourceListItem wstringToResourceListItem(const cpc::string& xmlString);
   cpc::string createNodeSelector(const ResourceListItem& item, const ResourceList& list);
   bool initializeResourceList(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& documentSelector);
   bool findXcapDocument(const cpc::string& documentSelector);
   cpc::string fieldsToString(cpc::vector<CPCAPI2::XCAP::Field> fields);
   int addItemChangeRequest(ChangeType itemChangeType, XcapResourceListUpdateRequestHandle rosterRequest, const ResourceListItem& resourceListItem);

   PhoneInterface* mPhone;
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   AccountMap mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapResourceListHandler*> mHandlers;
   ResourceListCache mResourceListCache;
   ResourceListUriMap mResourceListUriMap;
   XcapResourceListUpdateMap updateMap;
   std::vector<CPCAPI2::XCAP::ResourceListItem*> resourceListChanges;
   XcapInternalInterface* xcapModule;
   AccountXcapSettingsMap mXcapSettingsMap;
   ResourceListCreationInfoMap mRlsCreationInfoMap;

}; //XcapResourceListManagerInterface class

std::ostream& operator<<(std::ostream& os, const XcapResourceListUpdateEvent& evt);
std::ostream& operator<<(std::ostream& os, const XcapResourceListUpdateFailedEvent& evt);
std::ostream& operator<<(std::ostream& os, const XCAP::ErrorEvent& evt);
} //XCAP namespace
} //CPCAPI2 namespace
#endif // CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_IMPL_H
