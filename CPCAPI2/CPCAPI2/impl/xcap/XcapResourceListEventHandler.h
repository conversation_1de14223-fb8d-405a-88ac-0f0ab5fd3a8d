#pragma once

#if !defined(CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_IMPL_H)
#define CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_IMPL_H


//includes
#include "cpcapi2defs.h"
#include "xcap/XcapResourceListHandler.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "XcapResourceListManagerInterface.h"

namespace CPCAPI2
{
class Phone;

namespace XCAP
{
class XcapResourceListHandlerImpl;

/**
* Implementation of resource list manager.
*/
class XcapResourceListEventHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>
{
public:
   XcapResourceListEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, 
                                CPCAPI2::XCAP::XcapResourceListManagerInterface& xcapResourceListManager, 
                                CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf);

   virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
   virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
   virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
   virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
   virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

   virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
   virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);

   virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);

   int onResourceListUpdate(CPCAPI2::XCAP::XcapResourceListHandle resourceList, const XcapResourceListUpdateEvent& args);
   void setSubscriptionHandler(CPCAPI2::XCAP::XcapResourceListHandler* handler);
private:
   CPCAPI2::SipAccount::SipAccountImpl& account;
   CPCAPI2::XCAP::XcapResourceListManagerInterface& xcapResourceListManager;
   CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf;
   CPCAPI2::XCAP::XcapResourceListHandler* subscriptionHandler;

}; //XcapResourceListEventHandler class

} //XCAP namespace
} //CPCAPI2 namespace
#endif // CPCAPI2_XCAP_RESOURCE_LIST_MANAGER_IMPL_H
