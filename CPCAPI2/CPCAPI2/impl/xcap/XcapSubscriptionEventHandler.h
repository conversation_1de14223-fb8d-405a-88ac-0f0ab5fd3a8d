#pragma once

#if !defined(CPCAPI2_XCAP_SUBSCRIPTION_EVENT_HANDLER_H)
#define CPCAPI2_XCAP_SUBSCRIPTION_EVENT_HANDLER_H


#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "XcapInternalImpl.h"

#include "../util/DumFpCommand.h"

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountImpl;
};
namespace SipEvent
{
typedef unsigned int SipEventPublicationHandle;
class SipEventManagerInterface;
};
namespace XCAP
{

class XcapSubscriptionEventHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>,
                                     public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventPublicationHandler>
{
public:
   XcapSubscriptionEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, 
                                CPCAPI2::XCAP::XcapInternalImpl& xcapInternal, 
                                CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf);

   void setSubscriptionHandler(XcapSubscriptionHandler* handler);
   void setPublicationHandler(XcapPublicationHandler* handler);

   //
   virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
   virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
   virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
   virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
   virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

   virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
   virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);

   virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);

   // publication functions
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args);
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args);
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args);
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle subscription, const CPCAPI2::SipEvent::PublicationErrorEvent& args);
private:
   XcapSubscriptionHandler* subscriptionHandler;
   XcapPublicationHandler* publicationHandler;
   CPCAPI2::SipAccount::SipAccountImpl& account;
   CPCAPI2::XCAP::XcapInternalImpl& xcapInternal;
   CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf;

   void updateEtag(resip::Data notifyData);

}; // class XcapSubsrciptionEventHandler

} // XCAP
} // CPCAPI2

#endif
