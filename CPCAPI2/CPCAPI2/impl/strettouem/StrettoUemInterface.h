#pragma once

#ifndef CPCAPI2_STRETTOUEMINTERFACE_H
#define CPCAPI2_STRETTOUEMINTERFACE_H

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include "cpcapi2defs.h"
#include "phone/PhoneModule.h"
#include "strettouem/StrettoUem.h"
#include "strettouem/StrettoUemTypes.h"
#include "../util/AutoTestProcessor.h"
#include "analytics1/AnalyticsManager.h"
#include "analytics1/AnalyticsHandler.h"
#include "strettouem/StrettoUemInt.h"


namespace CPCAPI2
{
   class PhoneInterface;

   namespace StrettoUem
   {
      class StrettoUemInterface :
         public StrettoUemManagerInt,
         public CPCAPI2::PhoneModule,
         public Analytics::AnalyticsHandler
#ifdef CPCAPI2_AUTO_TEST
       , public AutoTestProcessor
#endif
      {
      public:
         StrettoUemInterface(PhoneInterface* pi);
         virtual ~StrettoUemInterface();

         // PhoneModule implementation
         void Release() OVERRIDE;

         // StrettoUem Implementation
         virtual void setHandler(StrettoUemHandler * notificationHandler) OVERRIDE;
         
         virtual int decodeProvisioningResponse(const cpc::string& provisioningResponse, StrettoUemSettings& outStrettoUemSettings) OVERRIDE;
         
         virtual StrettoUemHandle open(const StrettoUemSettings& settings, const GeneralStats& general) OVERRIDE;
         
         virtual int close(StrettoUemHandle serverHandle) OVERRIDE;

         virtual int sendReport(StrettoUemHandle serverHandle) OVERRIDE;
         
         virtual int process(unsigned int timeout) OVERRIDE;

         
         virtual void setCallbackHook(void (*cbHook)(void*), void* context);
         virtual void setCallbackHookImpl(void (*cbHook)(void*), void* context);
         
      private:
         // StrettoUemManagerInt
         virtual Analytics::AnalyticsManager* analyticsManager() OVERRIDE { return mAnalyticsManager; }

         // AnalyticsHandler
         int onConnectionFailed( const Analytics::AnalyticsHandle& serverHandle, const Analytics::OnConnectionFailedEvent& evt ) OVERRIDE;
         int onReportResponse( const Analytics::AnalyticsHandle& serverHandle, const Analytics::OnReportResponseEvent& evt ) OVERRIDE;

         std::string timezoneOffsetString(int timezoneOffsetMinutes);
      
      private:
         Analytics::AnalyticsManager* mAnalyticsManager;
         StrettoUemHandler* mHandler;
      
         resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
         bool mShutdown;
         resip::MultiReactor& mReactor;
         std::function<void(void)> mCbHook;

#ifdef CPCAPI2_AUTO_TEST
         // AutoTestProcessor implementation
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif
      };
   }
}
#endif // CPCAPI2_STRETTOUEMINTERFACE_H
