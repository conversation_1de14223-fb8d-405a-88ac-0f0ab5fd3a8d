#include <brand_branded.h>

#include "strettouem/StrettoUem.h"
#include "phone/PhoneInterface.h"

#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
#include "StrettoUemInterface.h"
#endif

using namespace CPCAPI2::StrettoUem;

StrettoUemManager* StrettoUemManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<StrettoUemInterface>(phone, "StrettoUemManager");
#else
   return NULL;
#endif
}
