#include "brand_branded.h"


#if (CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)

#include "StrettoUemInterface.h"
#include "analytics1/AnalyticsManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "util/DumFpCommand.h"
#include "util/PlatformUtils.h"
#include "util/DeviceInfo.h"
#include "util/TimeUtils.h"
#include "util/cpc_logger.h"

#include "brand_branded.h"


#include <iomanip>
#include <sstream>
#include <iostream>

#include "json/JsonHelper.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::STRETTO_UEM



using namespace CPCAPI2;
using namespace CPCAPI2::StrettoUem;

static int64_t sLaunchTimeSeconds = TimeUtils::millisSinceUnixEpoch() / 1000;

StrettoUemInterface::StrettoUemInterface(PhoneInterface* pi) :
   mShutdown(false),
   mReactor(pi->getSdkModuleThread())
{
   mAnalyticsManager = Analytics::AnalyticsManager::getInterface(pi);
}

StrettoUemInterface::~StrettoUemInterface()
{
}

void StrettoUemInterface::Release()
{
   // Release is already called in reactor thread
   mShutdown = true;
   delete this;
}

void StrettoUemInterface::setHandler(StrettoUemHandler* handler)
{
   mHandler = handler;
   mAnalyticsManager->setHandler(this);
}

int StrettoUemInterface::decodeProvisioningResponse(const cpc::string& provisioningResponse, StrettoUemSettings& outStrettoUemSettings)
{
   rapidjson::Document provisionedJSON;
   provisionedJSON.Parse<0>(provisioningResponse.c_str());

   if (provisionedJSON.HasParseError())
   {
      WarningLog(<< "Invalid provisioning format, parse error occured:" << provisionedJSON.GetParseError() << "Aborting decode.");
      return kError;
   }

   if (!provisionedJSON.HasMember("strettoUem"))
   {
      WarningLog(<< "Invalid provisioning format, strettoUem node missing. Aborting decode.");
      return kError;
   }

   const rapidjson::Value& strettoUem = provisionedJSON["strettoUem"];
   if (!strettoUem.HasMember("strettoUemSettings"))
   {
      WarningLog(<< "Invalid provisioning format, strettoUemSettings node missing.");
      return kError;
   }

   const rapidjson::Value& strettoUemSettings = strettoUem["strettoUemSettings"];
   if (!strettoUemSettings.IsObject())
   {
      WarningLog(<< "Invalid provisioning format, strettoUemSettings not an object.");
      return kError;
   }

   StrettoUemSettings uemSet;
   JsonDeserialize(strettoUemSettings, "serverUrl", uemSet.serverUrl, "httpUserName", uemSet.httpUserName, "httpPassword", uemSet.httpPassword, "strettoUserName", uemSet.strettoUserName);

   outStrettoUemSettings = uemSet;

   return kSuccess;
}

StrettoUemHandle StrettoUemInterface::open(const StrettoUemSettings& settings, const GeneralStats& general)
{
   Analytics::AnalyticsSettings analyticsSettings;
   
   analyticsSettings.serverURL = settings.serverUrl;
   analyticsSettings.httpUserName = settings.httpUserName;
   analyticsSettings.httpPassword = settings.httpPassword;
   analyticsSettings.strettoUserName = settings.strettoUserName;
   
   Analytics::GeneralStats analyticsGeneralStats;

   cpc::string instanceID;
   if (DeviceInfo::getInstanceId(instanceID) == kSuccess)
   {
      analyticsGeneralStats.deviceUUID = instanceID;
   }

   PlatformUtils::OSInfo osInfo;
   PlatformUtils::PlatformUtils::getOSInfo(osInfo);

   std::string type;
   switch (osInfo.osType)
   {
   case PlatformUtils::OSType_Windows:
      analyticsGeneralStats.osType = "WINDOWS";
      break;
   case PlatformUtils::OSType_OSX:
      analyticsGeneralStats.osType = "MAC";
      break;
   case PlatformUtils::OSType_Linux:
      analyticsGeneralStats.osType = "linux";
      break;
   case PlatformUtils::OSType_Android:
      analyticsGeneralStats.osType = "android";
      break;
   case PlatformUtils::OSType_iOS:
      analyticsGeneralStats.osType = "ios";
      break;
   case PlatformUtils::OSType_Other:
      analyticsGeneralStats.osType = "other";
      break;
   }

   analyticsGeneralStats.osVersion = osInfo.osVersion.c_str();

   PlatformUtils::DeviceInfo deviceInfo;
   PlatformUtils::PlatformUtils::getDeviceInfo( deviceInfo );

   analyticsGeneralStats.clientVersion = general.clientVersion;
   analyticsGeneralStats.installationDate = general.installationDate;
   analyticsGeneralStats.hardwareModel = deviceInfo.deviceModel.c_str();
// analyticsGeneralStats.publicIPAddress = ""; // currently unsupported

   std::stringstream launchTimeSeconds;
   launchTimeSeconds << sLaunchTimeSeconds;
   analyticsGeneralStats.launchTime = launchTimeSeconds.str().c_str();
// analyticsGeneralStats.language = ""; // currently unsupported
   
   analyticsGeneralStats.timezone = timezoneOffsetString(general.utcTimezoneOffsetMinutes).c_str();

   return mAnalyticsManager->open(analyticsSettings, analyticsGeneralStats);
}

std::string StrettoUemInterface::timezoneOffsetString(int timezoneOffsetMinutes)
{
   int hours = abs(timezoneOffsetMinutes) / 60;
   int leftMins = abs(timezoneOffsetMinutes) % 60;
   
    std::stringstream ret;
    ret << "UTC";
    if (timezoneOffsetMinutes > 0)
      ret << "+";
    else
      ret << "-";

    ret << std::setfill('0') << std::setw(1) << hours << std::setw(0) << ":" << std::setw(2) << leftMins;
   
    return ret.str();
}

int StrettoUemInterface::close(StrettoUemHandle serverHandle)
{
   return mAnalyticsManager->close(serverHandle);
}

int StrettoUemInterface::sendReport(StrettoUemHandle serverHandle)
{
   return mAnalyticsManager->sendReport(serverHandle);
}

int StrettoUemInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if( mShutdown )
      return -1;

   resip::ReadCallbackBase* fp = mCallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( mShutdown )
         return -1;

      fp = mCallbackFifo.getNext( -1 );
   }

   return mAnalyticsManager->process(timeout);
}

void StrettoUemInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if (cbHook != NULL)
   {
      static_cast<Analytics::AnalyticsManagerInterface*>(mAnalyticsManager)->setCallbackHook(cbHook, context);
      mReactor.post( resip::resip_bind( &StrettoUemInterface::setCallbackHookImpl, this, cbHook, context ));
   }
}

void StrettoUemInterface::setCallbackHookImpl(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

int StrettoUemInterface::onConnectionFailed( const Analytics::AnalyticsHandle& handle, const Analytics::OnConnectionFailedEvent& analyticsEvt )
{
   OnConnectionFailedEvent uemEvent;
   uemEvent.errNo = analyticsEvt.errNo;
   uemEvent.errorValue = analyticsEvt.errorValue.c_str();
   
   mCallbackFifo.add(makeFpCommand(StrettoUemHandler::onConnectionFailed, mHandler, handle, uemEvent));
   if (mCbHook) mCbHook();
   
   return kSuccess;
}

int StrettoUemInterface::onReportResponse( const Analytics::AnalyticsHandle& handle, const Analytics::OnReportResponseEvent& analyticsEvt )
{
   OnReportResponseEvent uemEvent;
   uemEvent.errorValue = analyticsEvt.errorValue.c_str();
   uemEvent.responseCode = analyticsEvt.responseCode;

   mCallbackFifo.add(makeFpCommand(StrettoUemHandler::onReportResponse, mHandler, handle, uemEvent));
   if (mCbHook) mCbHook();
   
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* StrettoUemInterface::process_test(int timeout)
{
   if (mShutdown)
      return NULL;

   static_cast<Analytics::AnalyticsManagerInterface*>(mAnalyticsManager)->process(timeout);

   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   
   return NULL;
}
#endif

#endif // CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE
