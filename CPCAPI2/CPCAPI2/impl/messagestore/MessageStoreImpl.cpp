#include "brand_branded.h"

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)
#include <assert.h>

#include <atomic>
#include <stack>
#include <algorithm>

#include <string.h> // strncmp

#include <rutil/Data.hxx>
#include <rutil/FileSystem.hxx>

#include <soci/soci.h>
#include <soci/sqlite3/soci-sqlite3.h>

#include <openssl/sha.h>

#include "cpcapi2defs.h"
#include "util/cpc_logger.h"
#include "MessageStoreImpl.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_ROSTER
#define TRANSACTION_TIMER_ID 1
#define TRANSACTION_WINDOW_MS 500

using namespace CPCAPI2::MessageStore;

// Some quick kluge for file separators
#ifndef _WIN32
#define FILE_SEP '/'
#else
#define FILE_SEP '\\'
#endif

// NB: this class casts uint64_t to int64_t and vice versa when dealing
// with sqlite/soci. This has to do with the fact that sqlite doesn't
// have support for 8 byte unsigned integers (AFAIK). So we use the signed
// variety. This might cause a problem because of the comparison clauses
// used (i.e. 'since'). However due to the fact that the timestamps would
// still be far in the future that would be problemmatic, I am dismissing
// this problem.

MessageStoreImpl::MessageStoreImpl( resip::MultiReactor& reactor, const std::string& directory ) :
   m_Directory( directory ),
   m_WriteTransactionTimer( reactor ),
   m_IsWriteTransactionOpen( false )
{
}

MessageStoreImpl::~MessageStoreImpl( void )
{
   closeWriteTransaction();

   if( m_SOCI.get() != NULL )
   {
      m_SOCI->close();
      m_SOCI.release();
   }
}

std::string MessageStoreImpl::buildFilename( const std::string& roomID ) const
{
   // Digest the appropriate sources in order to produce a filename.
   // Digesting is the method of choice because the JID itself may
   // contain characters which are not safe for the file system, in
   // addition to leaking information the user may not wish to leak.
   assert( !roomID.empty() );

   std::string fileName;
   char *data = new char[ ( SHA256_DIGEST_LENGTH * 2 ) + 1 ]; // ascii representation
   {
      uint8_t digest[ SHA256_DIGEST_LENGTH ]; // on the stack
      SHA256_CTX sha256;
      SHA256_Init( &sha256 );

      // Add the inputs
      SHA256_Update( &sha256, CPCAPI2_MESSAGE_STORE_CACHE_NAMESPACE, sizeof( CPCAPI2_MESSAGE_STORE_CACHE_NAMESPACE ));
      SHA256_Update( &sha256, roomID.c_str(), roomID.size() );
      SHA256_Final( digest, &sha256 );

      for( int i = 0; i < SHA256_DIGEST_LENGTH; ++i )
         snprintf( data + ( i * 2 ), 3, "%02x", digest[ i ] );

      data[ SHA256_DIGEST_LENGTH * 2 ] = 0;
   }
   fileName.assign( data, ( SHA256_DIGEST_LENGTH * 2 ));
   delete [] data;

   // Ensure the extension is added
   fileName += CPCAPI2_MESSAGE_STORE_CACHE_DIVIDER;
   fileName += CPCAPI2_MESSAGE_STORE_CACHE_EXT;
   return fileName;
}

bool MessageStoreImpl::loadFile( const std::string& fileName )
{
   // already loaded! close first
   if( m_SOCI.get() != NULL )
      return false;

   // First, check to ensure we have read/write permissions within
   // the directory
   struct stat dirstat;
   if( stat( m_Directory.c_str(), &dirstat ) != 0 )
      return false;

   // Ensure it is a directory
   if(( dirstat.st_mode & S_IFDIR ) == 0 )
      return false;

   // Make sure it's readable
   if(( dirstat.st_mode & S_IREAD ) == 0 )
      return false;

   // Make sure it's writable
   if(( dirstat.st_mode & S_IWRITE ) == 0 )
      return false;

   // Next, manually check if the file exists and is readable
   std::string absolutePath( m_Directory );
   absolutePath += FILE_SEP;
   absolutePath += fileName;

   struct stat filestat;
   if( stat( absolutePath.c_str(), &filestat ) < 0 )
   {
      // Could mean file doesn't exist, but also other errors,
      // in any case we couldn't load the file so, return false
      return false;
   }

   // File exists, attempt to open the file
   try
   {
      m_SOCI.reset( new soci::session );
      m_SOCI->open( soci::sqlite3, absolutePath );

      closeWriteTransaction(); // shouldn't do anything.
      enableWal();

      soci::transaction tr( *m_SOCI );

      // This will do nothing if the file is setup properly
      if( !initFile() )
         return false;

      // Read the value of the room ID from the file
      ( *m_SOCI ) << "SELECT roomID FROM Magic LIMIT 1;", soci::into( m_RoomID );

      tr.commit(); // end transaction

      assert( m_RoomID.length() > 0 );
      return ( m_RoomID.length() > 0 );
   }
   catch( soci::soci_error& )
   {
      // Could happen for example if we're already
      // connected (or other reasons)
   }

   return false;
}

bool MessageStoreImpl::createNewFile( const std::string& roomID )
{
   // already created! close first
   if( m_SOCI.get() != NULL )
      return false;

   // Check preconditions
   if( roomID.empty() )
      return false;

   // First, check to ensure we have read/write permissions within
   // the directory
   struct stat dirstat;
   if( stat( m_Directory.c_str(), &dirstat ) != 0 )
      return false;

   // Ensure it is a directory
   if(( dirstat.st_mode & S_IFDIR ) == 0 )
      return false;

   // Make sure it's readable
   if(( dirstat.st_mode & S_IREAD ) == 0 )
      return false;

   // Make sure it's writable
   if(( dirstat.st_mode & S_IWRITE ) == 0 )
      return false;

   // After that, just open a new file :-P
   std::string absolutePath( m_Directory );
   absolutePath += FILE_SEP;
   absolutePath += buildFilename( roomID );

   try
   {
      m_SOCI.reset( new soci::session );
      m_SOCI->open( soci::sqlite3, absolutePath );

      closeWriteTransaction(); // shouldn't do anything.
      enableWal();

      soci::transaction tr( *m_SOCI );

      // Open was successful, initialize the file
      if( !initFile() )
         return false;

      ( *m_SOCI ) << "INSERT INTO Magic VALUES( :version, :roomID );",
         soci::use( CPCAPI2_MESSAGE_STORE_CACHE_VERSION_NUM ),
         soci::use( roomID );

      tr.commit(); // end transaction

      m_RoomID = roomID;
      return true;
   }
   catch( soci::soci_error& )
   {
      // Could happen for example if we're already
      // connected (or other reasons)
   }

   return false;
}

std::string MessageStoreImpl::getRoomID( void ) const
{
   return m_RoomID;
}

bool MessageStoreImpl::initFile( void )
{
   if( m_SOCI.get() == NULL )
      return false;

   ( *m_SOCI ) << "CREATE TABLE IF NOT EXISTS Magic ( version INTEGER, roomID TEXT ); ";
   ( *m_SOCI ) <<
      "CREATE TABLE IF NOT EXISTS Messages ("
      "    ID TEXT NOT NULL PRIMARY KEY,"
      "    timestampMillis BIGINT,"
      "    senderID TEXT NOT NULL,"
      "    senderAlias TEXT NOT NULL,"
      "    recipientID TEXT NOT NULL,"
      "    recipientAlias TEXT NOT NULL,"
      "    plainText TEXT,"
      "    HTMLText TEXT,"
      "    isEdited BOOLEAN NOT NULL DEFAULT 0,"
      "    isDeleted BOOLEAN NOT NULL DEFAULT 0,"
      "    isDelivered BOOLEAN NOT NULL DEFAULT 0,"
      "    isRead BOOLEAN NOT NULL DEFAULT 0,"
      "    reactions TEXT"
      ");";

   // next we upgrade the database with changes that were made since this feature rolled out
   try
   {
      ( *m_SOCI ) << "ALTER TABLE Messages " 
         "ADD COLUMN isEdited BOOLEAN NOT NULL DEFAULT 0;";
      ( *m_SOCI ) << "ALTER TABLE Messages "
         "ADD COLUMN isDeleted BOOLEAN NOT NULL DEFAULT 0;";
   }
   catch( soci::soci_error& se )
   {
      // ignore if the error is about the pre-existing column
      if (std::string(se.what()).find("duplicate column name: isEdited") == std::string::npos &&
         std::string(se.what()).find("duplicate column name: isDeleted") == std::string::npos)
         throw se;
   }
   try
   {
      ( *m_SOCI ) << "ALTER TABLE Messages " 
         "ADD COLUMN isDelivered BOOLEAN NOT NULL DEFAULT 0;";
      ( *m_SOCI ) << "ALTER TABLE Messages " 
         "ADD COLUMN isRead BOOLEAN NOT NULL DEFAULT 0;";
      ( *m_SOCI ) << "ALTER TABLE Messages " 
         "ADD COLUMN reactions TEXT;";
   }
   catch( soci::soci_error& se )
   {
      // ignore if the error is about the pre-existing column
      if (std::string(se.what()).find("duplicate column name: isDelivered") == std::string::npos &&
         std::string(se.what()).find("duplicate column name: isRead") == std::string::npos &&
         std::string(se.what()).find("duplicate column name: reactions") == std::string::npos)
         throw se;
   }
   return true;
}

bool MessageStoreImpl::enableWal(void)
{
   // rationale: speed up database access but still maintain protection
   // against database corruption on power loss / crashes
   //
   // from https://www.sqlite.org/pragma.html#pragma_synchronous:
   //
   // WAL mode is safe from corruption with synchronous = NORMAL, and probably
   // DELETE mode is safe too on modern filesystems. WAL mode is always consistent
   // with synchronous = NORMAL, but WAL mode does lose durability. A transaction
   // committed in WAL mode with synchronous = NORMAL might roll back following
   // a power loss or system crash. Transactions are durable across application
   // crashes regardless of the synchronous setting or journal mode. The
   // synchronous = NORMAL setting is a good choice for most applications running
   // in WAL mode.

   (*m_SOCI) << "PRAGMA synchronous = 1"; // aka NORMAL

   std::string synchronous;
   (*m_SOCI) << "PRAGMA synchronous", soci::into(synchronous);

   std::string journalMode;
   (*m_SOCI) << "PRAGMA journal_mode = WAL", soci::into(journalMode);

   bool success = true;
   if (journalMode != "wal")
   {
      WarningLog(<< "Could not set journal_mode = WAL. Fell back to " << journalMode);
      success = false;
   }
   if (synchronous != "1")
   {
      WarningLog(<< "Could not set synchronous = 1. Fell back to " << synchronous);
      success = false;
   }

   return success;
}

void MessageStoreImpl::closeWriteTransaction( void )
{
   m_WriteTransactionTimer.cancel();

   if( m_SOCI.get() == NULL )
      return;

   // When the timer expires, commit the transaction.
   if( m_IsWriteTransactionOpen )
      m_SOCI->commit();

   m_IsWriteTransactionOpen = false;
}

// NOTE: This method does not update the flags (isRead, etc.) nor the reactions as those
// have their own update methods and in some cases the caller would not have this information.
bool MessageStoreImpl::store( MessageInfo& info, const cpc::string& replacesMessageID )
{
   if( m_SOCI.get() == NULL )
      return false;

   const std::string replacesID = replacesMessageID.c_str();
   const std::string uniqueID( replacesMessageID.empty() ? info.uniqueID.c_str() : replacesMessageID.c_str() );
   const std::string senderID( info.sender.uniqueID.c_str() );
   const std::string senderAlias( info.sender.alias.c_str() );
   const std::string recipientID( info.recipient.uniqueID.c_str() );
   const std::string recipientAlias( info.recipient.alias.c_str() );
   const std::string plainContent( info.plainTextContent.c_str() );
   const std::string htmlContent( info.htmlContent.c_str() );

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   // reset the transaction timer
   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   std::string queryStr = "INSERT INTO Messages VALUES( :ID, :timestampMillis, :senderID, :senderAlias, :recipientID, :recipientAlias, :plainText, :HTMLText, 0, 0, 0, 0, \"\" ) "
      "ON CONFLICT(ID) DO UPDATE SET timestampMillis=:timestampMillis, plainText=:plainText, HTMLText=:HTMLText;";
   soci::statement st = (( *m_SOCI ).prepare << queryStr,
         soci::use( uniqueID ),
         soci::use(( int64_t ) info.timestampMillis ),
         soci::use( senderID ),
         soci::use( senderAlias ),
         soci::use( recipientID ),
         soci::use( recipientAlias ),
         soci::use( plainContent ),
         soci::use( htmlContent ));

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error storing a message " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error storing a message " << se.what() );
      return false;
   }

   if (replacesMessageID.empty())
      return true;

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   queryStr = "UPDATE Messages SET IsEdited = 1 WHERE ID = :ID;";
   st = (( *m_SOCI ).prepare << queryStr, 
      soci::use( replacesID ));

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error marking message as edited " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error marking message as edited " << se.what() );
      return false;
   }

   return true;
}

bool MessageStoreImpl::discard( const cpc::string& messageID, uint64_t timestampMillis )
{
   if( m_SOCI.get() == NULL )
      return false;

   const std::string uniqueID( messageID.c_str() );

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   // reset the transaction timer
   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   std::string queryStr = "UPDATE Messages SET timestampMillis = :timestampMillis, isDeleted = 1 WHERE ID = :ID;";
   soci::statement st = (( *m_SOCI ).prepare << queryStr, 
      soci::use(( int64_t ) timestampMillis ),
      soci::use( uniqueID )
   );

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error storing a message " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error storing a message " << se.what() );
      return false;
   }
   return true;
}

bool MessageStoreImpl::markDelivered( const cpc::string& messageID )
{
   if( m_SOCI.get() == NULL )
      return false;

   const std::string uniqueID( messageID.c_str() );

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   // reset the transaction timer
   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   std::string queryStr = "UPDATE Messages SET isDelivered = 1 WHERE ID = :ID;";
   soci::statement st = (( *m_SOCI ).prepare << queryStr, 
      soci::use( uniqueID )
   );

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error marking a message delivered " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error marking a message delivered " << se.what() );
      return false;
   }
   return true;
}

bool MessageStoreImpl::markRead( const cpc::string& messageID )
{
   if( m_SOCI.get() == NULL )
      return false;

   const std::string uniqueID( messageID.c_str() );

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   // reset the transaction timer
   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   std::string queryStr = "UPDATE Messages SET isRead = 1 WHERE ID = :ID;";
   soci::statement st = (( *m_SOCI ).prepare << queryStr, 
      soci::use( uniqueID )
   );

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error marking a message read " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error marking a message read " << se.what() );
      return false;
   }
   return true;
}

bool MessageStoreImpl::updateReactions( const cpc::string& messageID, const cpc::vector<cpc::string>& reactions )
{
   if( m_SOCI.get() == NULL )
      return false;

   const std::string uniqueID( messageID.c_str() );
   std::string reactionsCsv;
   for (cpc::vector<cpc::string>::const_iterator i = reactions.begin(); i != reactions.end(); ++i)
   {
      if (!reactionsCsv.empty() && !i->empty())
         reactionsCsv += ",";
      reactionsCsv += (*i);
   }

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   // reset the transaction timer
   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   // Prepare the statement. sqlite3 doesn't really support prepared statements, but who
   // knows maybe it will in the future.
   std::string queryStr = "UPDATE Messages SET reactions = :Reactions WHERE ID = :ID;";
   soci::statement st = (( *m_SOCI ).prepare << queryStr, 
      soci::use( reactionsCsv ),
      soci::use( uniqueID )
   );

   try
   {
      st.execute( true );
   }
   catch( soci::soci_error& se )
   {
      // Database problem, just return false
      WarningLog(<< "Having a soci_error marking a message read " << se.what() );
      return false;
   }
   catch (soci::sqlite3_soci_error& se)
   {
      WarningLog(<< "Having a sqlite3_soci_error marking a message read " << se.what() );
      return false;
   }
   return true;
}

void MessageStoreImpl::onTimer( unsigned short timerId, void* appState )
{
   // When the timer expires, commit the transaction.
   closeWriteTransaction();
}

static cpc::vector<cpc::string> ConvertToCSV(const std::string& csvString)
{
   std::stringstream ss( csvString );
   cpc::vector<cpc::string> result;

   if (!csvString.empty())
   {
      while( ss.good() )
      {
         std::string substr;
         getline( ss, substr, ',' );
         result.push_back( substr.c_str() );
      }
   }

   return result;
}

bool MessageStoreImpl::retrieve( uint64_t sinceMillis, cpc::vector< MessageInfo >& infos, int limit )
{
   if( m_SOCI.get() == NULL )
      return false;

   closeWriteTransaction();

   std::string queryStr;
   queryStr = "SELECT ID, timestampMillis, senderID, senderAlias, recipientID, recipientAlias, plainText, HTMLText, isEdited, isDeleted, isDelivered, isRead, reactions FROM Messages";
   queryStr += " WHERE timestampMillis >= :timestampMillis";
   queryStr += " ORDER BY timestampMillis, ROWID";
   if( limit > 0 )
   {
      // Ideally I should use soci::use for this, but I don't want to rebuild
      // the query and there's no substitute for LIMIT value which means
      // 'unlimited' ?
      queryStr += " LIMIT ";
      queryStr += cpc::to_string( limit );
   }
   queryStr += ";";

   try
   {
      soci::rowset< soci::row > rows = ((*m_SOCI).prepare << queryStr, soci::use(( int64_t ) sinceMillis ));
      std::stringstream dump;
      for( soci::rowset<soci::row>::const_iterator it = rows.begin(); it != rows.end(); ++it )
      {
         soci::row& row = (*it);
         soci::indicator indi;

         MessageInfo info;
         info.uniqueID = row.get< std::string >( 0 ).c_str();
         info.timestampMillis = ( uint64_t ) row.get< long long >( 1 );
         info.sender.uniqueID = row.get< std::string >( 2 ).c_str();
         info.sender.alias = row.get< std::string >( 3 ).c_str();
         info.recipient.uniqueID = row.get< std::string >( 4 ).c_str();
         info.recipient.alias = row.get< std::string >( 5 ).c_str();

         indi = row.get_indicator( 6 );
         if( indi == soci::i_ok )
            info.plainTextContent = row.get< std::string >( 6 ).c_str();

         indi = row.get_indicator( 7 );
         if( indi == soci::i_ok )
            info.htmlContent = row.get< std::string >( 7 ).c_str();

         info.isEdited = row.get< int >( 8 );
         info.isDeleted = row.get< int >( 9 );
         info.isDelivered = row.get< int >( 10 );
         info.isRead = row.get< int >( 11 );
   
         indi = row.get_indicator( 12 );
         if( indi == soci::i_ok )
            info.reactions = ConvertToCSV( row.get< std::string >( 12 ).c_str() );

         infos.push_back( info );
      }
   }
   catch( soci::soci_error& )
   {
      // Database problem, return false
      return false;
   }

   return true;
}

bool MessageStoreImpl::retrieveBackward( uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit )
{
   if( m_SOCI.get() == NULL )
      return false;

   closeWriteTransaction();

   std::string queryStr;
   queryStr = "SELECT ID, timestampMillis, senderID, senderAlias, recipientID, recipientAlias, plainText, HTMLText, isEdited, isDeleted, isDelivered, isRead, reactions FROM Messages";
   queryStr += " WHERE timestampMillis < :untilMillis";
   queryStr += " ORDER BY timestampMillis DESC, ROWID DESC";
   if( limit > 0 )
   {
      // Ideally I should use soci::use for this, but I don't want to rebuild
      // the query and there's no substitute for LIMIT value which means
      // 'unlimited' ?
      queryStr += " LIMIT ";
      queryStr += cpc::to_string( limit );
   }
   queryStr += ";";

   try
   {
      soci::rowset< soci::row > rows = ((*m_SOCI).prepare << queryStr, soci::use(( int64_t ) untilMillis ));
      for( soci::rowset<soci::row>::const_iterator it = rows.begin(); it != rows.end(); ++it )
      {
         soci::row& row = (*it);
         soci::indicator indi;

         MessageInfo info;
         info.uniqueID = row.get< std::string >( 0 ).c_str();
         info.timestampMillis = ( uint64_t ) row.get< long long >( 1 );
         info.sender.uniqueID = row.get< std::string >( 2 ).c_str();
         info.sender.alias = row.get< std::string >( 3 ).c_str();
         info.recipient.uniqueID = row.get< std::string >( 4 ).c_str();
         info.recipient.alias = row.get< std::string >( 5 ).c_str();

         indi = row.get_indicator( 6 );
         if( indi == soci::i_ok )
            info.plainTextContent = row.get< std::string >( 6 ).c_str();

         indi = row.get_indicator( 7 );
         if( indi == soci::i_ok )
            info.htmlContent = row.get< std::string >( 7 ).c_str();

         info.isEdited = row.get< int >( 8 );
         info.isDeleted = row.get< int >( 9 );
         info.isDelivered = row.get< int >( 10 );
         info.isRead = row.get< int >( 11 );

         indi = row.get_indicator( 12 );
         if( indi == soci::i_ok )
            info.reactions = ConvertToCSV( row.get< std::string >( 12 ).c_str() );

         infos.push_back( info );
      }

      // Needs to be reversed because of the DESC SQL clause.
      // NB: is it safe to mix cpc::vector with std::reverse?
      std::reverse( infos.begin(), infos.end() );
   }
   catch( soci::soci_error& )
   {
      // Database problem, return false
      return false;
   }

   return true;
}

bool MessageStoreImpl::destroy( void )
{
   if( m_SOCI.get() == NULL )
      return false;

   if( m_RoomID.empty() )
      return false;

   closeWriteTransaction();

   try
   {
      ( *m_SOCI ) << "DROP TABLE IF EXISTS Magic;";
      ( *m_SOCI ) << "DROP TABLE IF EXISTS Messages;";
   }
   catch( soci::soci_error& )
   {
      return false;
   }

   m_SOCI->close();
   m_SOCI.release();

   // After that, remove the file(s)
   std::string absolutePath( m_Directory );
   absolutePath += FILE_SEP;
   absolutePath += buildFilename( m_RoomID );

   // Other files sqlite may make
   std::string shm_file = absolutePath + "-shm";
   std::string wal_file = absolutePath + "-wal";
   std::string journal_file = absolutePath + "-journal";

   // Try to unlink the shm and wal files but don't throw
   // any errors if they don't exist
   unlink( shm_file.c_str() );
   unlink( wal_file.c_str() );
   unlink( journal_file.c_str() );

   // Proceed with trying to unlink the main .CAC
   if( unlink( absolutePath.c_str() ) != 0 )
      return false;

   return true;
}

bool MessageStoreImpl::flush( void )
{
   if( m_SOCI.get() == NULL )
      return false;

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   try
   {
      // Drop everything we can drop but maintain the magic
      ( *m_SOCI ) << "DELETE FROM Messages;";
   }
   catch( soci::soci_error& )
   {
      return false;
   }

   return true;
}

bool MessageStoreImpl::flush( uint64_t untilMillis )
{
   if( m_SOCI.get() == NULL )
      return false;

   if( !m_IsWriteTransactionOpen )
   {
      m_SOCI->begin();
      m_IsWriteTransactionOpen = true;
   }

   m_WriteTransactionTimer.cancel();
   m_WriteTransactionTimer.expires_from_now( TRANSACTION_WINDOW_MS );
   m_WriteTransactionTimer.async_wait( this, TRANSACTION_TIMER_ID, NULL );

   try
   {
      ( *m_SOCI ) << "DELETE FROM Messages WHERE timestampMillis < :untilMillis;", soci::use(( int64_t ) untilMillis );
   }
   catch( soci::soci_error& )
   {
      return false;
   }
   return true;
}

bool MessageStoreImpl::getMessageInfo( const cpc::string& messageID, MessageInfo& info )
{
   if( m_SOCI.get() == NULL )
      return false;

   closeWriteTransaction();

   try
   {
      std::string tempID( messageID.c_str() );
      std::string queryStr = "SELECT ID, timestampMillis, senderID, senderAlias, recipientID, recipientAlias, plainText, HTMLText, isEdited, isDeleted, isDelivered, isRead, reactions FROM Messages WHERE ID = :ID LIMIT 1;";
      soci::rowset< soci::row > rows = ((*m_SOCI).prepare << queryStr, soci::use( tempID ));
      for( soci::rowset< soci::row >::const_iterator it = rows.begin() ; it != rows.end() ; ++it )
      {
         soci::row& row = ( *it );
         soci::indicator indi;

         info.uniqueID = row.get< std::string >( 0 ).c_str();
         info.timestampMillis = ( uint64_t ) row.get< long long >( 1 );
         info.sender.uniqueID = row.get< std::string >( 2 ).c_str();
         info.sender.alias = row.get< std::string >( 3 ).c_str();
         info.recipient.uniqueID = row.get< std::string >( 4 ).c_str();
         info.recipient.alias = row.get< std::string >( 5 ).c_str();

         indi = row.get_indicator( 6 );
         if( indi == soci::i_ok )
            info.plainTextContent = row.get< std::string >( 6 ).c_str();

         indi = row.get_indicator( 7 );
         if( indi == soci::i_ok )
            info.htmlContent = row.get< std::string >( 7 ).c_str();

         info.isEdited = row.get< int >( 8 );
         info.isDeleted = row.get< int >( 9 );
         info.isDelivered = row.get< int >( 10 );
         info.isRead = row.get< int >( 11 );

         indi = row.get_indicator( 12 );
         if( indi == soci::i_ok )
            info.reactions = ConvertToCSV( row.get< std::string >( 12 ).c_str() );

         return true; // take the first one
      }
   }
   catch( soci::soci_error& )
   {
      // result will still be returned
   }
   return false;
}

bool MessageStoreImpl::getLastMessageInfo( MessageInfo& lastInfo )
{
   if( m_SOCI.get() == NULL )
      return false;

   closeWriteTransaction();

   try
   {
      // Check for no results
      soci::rowset< soci::row > rows = ( m_SOCI->prepare << "SELECT ID, timestampMillis, senderID, senderAlias, recipientID, recipientAlias, plainText, HTMLText, isEdited, isDeleted, isDelivered, isRead, reactions FROM Messages ORDER BY timestampMillis DESC LIMIT 1");
      for( soci::rowset< soci::row >::const_iterator it = rows.begin(); it != rows.end(); ++it )
      {
         soci::row& row = (*it);
         soci::indicator indi;

         lastInfo.uniqueID = row.get< std::string >( 0 ).c_str();
         lastInfo.timestampMillis = ( uint64_t ) row.get< long long >( 1 );
         lastInfo.sender.uniqueID = row.get< std::string >( 2 ).c_str();
         lastInfo.sender.alias = row.get< std::string >( 3 ).c_str();
         lastInfo.recipient.uniqueID = row.get< std::string >( 4 ).c_str();
         lastInfo.recipient.alias = row.get< std::string >( 5 ).c_str();

         indi = row.get_indicator( 6 );
         if( indi == soci::i_ok )
            lastInfo.plainTextContent = row.get< std::string >( 6 ).c_str();

         indi = row.get_indicator( 7 );
         if( indi == soci::i_ok )
            lastInfo.htmlContent = row.get< std::string >( 7 ).c_str();

         lastInfo.isEdited = row.get< int >( 8 );
         lastInfo.isDeleted = row.get< int >( 9 );
         lastInfo.isDelivered = row.get< int >( 10 );
         lastInfo.isRead = row.get< int >( 11 );

         indi = row.get_indicator( 12 );
         if( indi == soci::i_ok )
            lastInfo.reactions = ConvertToCSV( row.get< std::string >( 12 ).c_str() );

         return true; // Only use the first result
      }
   }
   catch( soci::soci_error& )
   {
      // result will still be returned
   }
   return false;
}

#endif
