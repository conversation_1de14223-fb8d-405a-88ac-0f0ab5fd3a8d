#include "brand_branded.h"
#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

#include <assert.h>
#include <fcntl.h>
#include <sys/types.h>
#include <sys/stat.h>

#include <atomic>
#include <string>

#include <rutil/Data.hxx>
#include <rutil/FileSystem.hxx>

#include <cpcapi2defs.h>
#include <messagestore/MessageStoreHandler.h>
#include <phone/PhoneInterface.h>

#include "MessageStoreManagerInterface.h"

#include "MessageStoreManagerXmppMultiUserImpl.h"
#include "MessageStoreImpl.h"

using namespace CPCAPI2::MessageStore;
using CPCAPI2::XmppAccount::XmppAccountHandle;
using CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle;
using CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent;
using CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent;
using CPCAPI2::XmppMultiUserChat::MultiUserChatNewReactionEvent;
using CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent;
using CPCAPI2::XmppMultiUserChat::MultiUserChatRoomStateChangedEvent;
using CPCAPI2::XmppMultiUserChat::MultiUserChatSubjectChangedEvent;
using CPCAPI2::XmppMultiUserChat::NewRoomEvent;
using CPCAPI2::XmppMultiUserChat::LocalUserLeftEvent;
using CPCAPI2::XmppMultiUserChat::MessageDeliveredEvent;
using CPCAPI2::XmppMultiUserChat::MessageReadEvent;

std::atomic< MessageStoreHandle > MessageStoreManagerXmppMultiUserImpl::s_CurrentHandle( 1 );

MessageStoreManagerXmppMultiUserImpl::MessageStoreManagerXmppMultiUserImpl( MessageStoreManagerInterface& parent, const Settings& settings )
   : m_Parent( parent ), m_Settings( settings ), m_Provider( ProviderType_XMPP_MultiUser )
{
}

MessageStoreManagerXmppMultiUserImpl::~MessageStoreManagerXmppMultiUserImpl( void )
{
   m_STAAssert.test();

   m_CacheMap.clear();

   std::map< std::string, MultiUserChatInfo >::iterator infoIter = m_ChatInfoMap.begin();
   for( ; infoIter != m_ChatInfoMap.end() ; ++infoIter )
   {
      MultiUserChatInfo& info( infoIter->second );
      if( info.pCache != NULL )
         delete info.pCache;
   }
   m_ChatInfoMap.clear();

   std::map< XmppAccountHandle, AccountInfo >::iterator acctIter = m_AccountInfoMap.begin();
   for( ; acctIter != m_AccountInfoMap.end() ; ++acctIter )
   {
      AccountInfo& acctInfo( acctIter->second );
      acctInfo.m_JIDMap.clear();
   }
   m_AccountInfoMap.clear();
}

bool MessageStoreManagerXmppMultiUserImpl::discover( void )
{
   m_STAAssert.test();

   // Get the directory from the settings
   const resip::Data path( m_Settings.cacheDirectory.c_str() );

   // Check that the directory exists and is readable
   struct stat dirstat;
   if( stat( path.c_str(), &dirstat ) != 0 )
      return false;

   // Ensure it is a directory
   if(( dirstat.st_mode & S_IFDIR ) == 0 )
      return false;

   // Make sure it's readable
   if(( dirstat.st_mode & S_IREAD ) == 0 )
      return false;

   // Loop over the files in the directory, looking for files with the magic extension
   resip::FileSystem::Directory cacheDir( path );
   resip::FileSystem::Directory::iterator iter = cacheDir.begin();
   for( ; iter != cacheDir.end() ; ++iter )
   {
      // skip files with wrong extension
      const std::string cacheFileName( iter->c_str() );
      if( cacheFileName.length() < ( sizeof( CPCAPI2_MESSAGE_STORE_CACHE_EXT ) - 1 ))
         continue;

      if( cacheFileName.compare(
         cacheFileName.length() - ( sizeof( CPCAPI2_MESSAGE_STORE_CACHE_EXT ) - 1 ),
         sizeof( CPCAPI2_MESSAGE_STORE_CACHE_EXT ) - 1,
         std::string( CPCAPI2_MESSAGE_STORE_CACHE_EXT )) != 0 )
         continue;

      MessageStoreImpl *pImpl = new MessageStoreImpl(
         dynamic_cast< CPCAPI2::PhoneInterface* >( m_Parent.m_Phone )->getSdkModuleThread(),
         path.c_str() );

      if( !pImpl->loadFile( cacheFileName ))
      {
         // Failed, remove and continue
         delete pImpl;
         continue;
      }

      // Check to see if the chat info already exists
      std::map< std::string, MultiUserChatInfo >::iterator iter = m_ChatInfoMap.find( pImpl->getRoomID() );
      if( iter != m_ChatInfoMap.end() )
      {
         // Do nothing in this situation
         delete pImpl;
         continue;
      }

      // Register the opened file with the system
      MultiUserChatInfo chatInfo;
      chatInfo.chatJID  = pImpl->getRoomID();
      chatInfo.hAccount = -1; // not yet available
      chatInfo.pCache   = pImpl;
      chatInfo.hCache   = s_CurrentHandle.fetch_add( 1 );

      // Add this into the chat info map
      m_ChatInfoMap[ chatInfo.chatJID ] = chatInfo;

      // But also into the cache map (this is just a convenience)
      m_CacheMap[ chatInfo.hCache ] = chatInfo.pCache;

      // Fire the event about creation of a new cache
      KreateEvent kevt;
      kevt.hMessageStore = chatInfo.hCache;
      kevt.roomID = chatInfo.chatJID.c_str();
      m_Parent.fireEvent( cpcFunc( MessageStoreHandler::onCreate ), 0, kevt );

      // Also fire an event to indicate history load was complete.
      HistoryLoadedEvent hle;
      hle.hMessageStore = chatInfo.hCache;
      hle.hAccount = -1; // not yet available
      hle.result.result = kSuccess;
   }

   return true;
}

bool MessageStoreManagerXmppMultiUserImpl::retrieve(
   const MessageStoreHandle& hMessageStore,
   uint64_t timestampMillis,
   cpc::vector< MessageInfo >& infos,
   int limit )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   return iter->second->retrieve( timestampMillis, infos, limit );
}

bool MessageStoreManagerXmppMultiUserImpl::retrieveBackward(
   const MessageStoreHandle& hMessageStore,
   uint64_t untilMillis,
   cpc::vector< MessageInfo >& infos,
   int limit )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   return iter->second->retrieveBackward( untilMillis, infos, limit );
}

bool MessageStoreManagerXmppMultiUserImpl::destroy( const MessageStoreHandle& hMessageStore )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   if( iter->second == NULL )
      return false;

   // Remember the room JID
   const std::string roomJID( iter->second->getRoomID() );

   if( !iter->second->destroy() )
      return false;

   // Delete and remove from both maps
   delete iter->second;
   m_CacheMap.erase( hMessageStore );
   m_ChatInfoMap.erase( roomJID );

   return true;
}

bool MessageStoreManagerXmppMultiUserImpl::flush( const MessageStoreHandle& hMessageStore )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   return iter->second->flush();
}

bool MessageStoreManagerXmppMultiUserImpl::getMessageInfo( const MessageStoreHandle& hMessageStore, const cpc::string& messageID, MessageInfo& outInfo )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   return iter->second->getMessageInfo( messageID, outInfo );
}

bool MessageStoreManagerXmppMultiUserImpl::getLastMessageInfo( const MessageStoreHandle& hMessageStore, MessageInfo& lastInfo )
{
   m_STAAssert.test();

   CacheMap_t::iterator iter = m_CacheMap.find( hMessageStore );
   if( iter == m_CacheMap.end() )
      return false;

   return iter->second->getLastMessageInfo( lastInfo );
}

void MessageStoreManagerXmppMultiUserImpl::onServiceAvailability(
   XmppAccountHandle account,
   const ServiceAvailabilityEvent& evt )
{
   m_STAAssert.test();

   // This event signals two things, first it may be the initial moment where we
   // may see a new XmppAccountHandle (so we must create an entry if it does
   // not exist). Second, it indicates whether the service is available and
   // if so, what the conference JID will be.

   std::map< XmppAccountHandle, AccountInfo >::iterator iter = m_AccountInfoMap.find( account );
   if( iter == m_AccountInfoMap.end() )
   {
      // Create the XMPP Account information
      AccountInfo acctInfo;
      m_AccountInfoMap[ account ] = acctInfo;
      iter = m_AccountInfoMap.find( account );
   }

   if( iter == m_AccountInfoMap.end() )
      return; // shouldn't happen

   iter->second.conferenceJID = evt.service;
}

void MessageStoreManagerXmppMultiUserImpl::onNewRoomHandle(
   XmppMultiUserChatHandle handle,
   const NewRoomEvent& evt )
{
   m_STAAssert.test();

   // Add the XMPP Account if not created already
   std::map< XmppAccountHandle, AccountInfo >::iterator acctIter = m_AccountInfoMap.find( evt.hAccount );
   if (acctIter == m_AccountInfoMap.end())
   {
      // Create the XMPP Account information
      AccountInfo acctInfo;
      m_AccountInfoMap[evt.hAccount] = acctInfo;
      acctIter = m_AccountInfoMap.find(evt.hAccount);
   }

   // Reverse map this from MUC handle to Acct Handle
   m_ChatToAcctMap[ handle ] = evt.hAccount;

   // Remember the room JID
   const cpc::string roomJID( evt.roomjid );

   // Update the MUC handle -> JID map
   acctIter->second.m_JIDMap[ handle ] = roomJID;

   // Update the chat info to contain the necessary settings but delay
   // the actual message store creation until receipt of the first message.
   // This is done this way because of searching and other features which
   // trigger onNewRoomHandle and onMultiUserChatNewMessage without actually
   // entering the room itself.

   // Check the Chat info map
   std::map< std::string, MultiUserChatInfo >::iterator chatIter = m_ChatInfoMap.find( roomJID.c_str() );
   if( chatIter == m_ChatInfoMap.end() )
   {
      // Load an existing file
      MultiUserChatInfo chatInfo;
      chatInfo.chatJID  = roomJID;
      chatInfo.hAccount = evt.hAccount;
      chatInfo.pCache   = NULL;
      chatInfo.hCache   = s_CurrentHandle.fetch_add( 1 ); // reserve a number

      // Add this into the chat info map
      m_ChatInfoMap[ chatInfo.chatJID ] = chatInfo;

      // But also into the cache map (this is just a convenience)
      m_CacheMap[ chatInfo.hCache ] = chatInfo.pCache;
   }

   // Also reset the history count at this point.
   chatIter = m_ChatInfoMap.find( roomJID.c_str() );
   if( chatIter != m_ChatInfoMap.end() )
   {
      chatIter->second.historyCount = 0;
      chatIter->second.isSubjectChanged = false;
   }
}

void MessageStoreManagerXmppMultiUserImpl::onMultiUserChatRoomStateChanged(
   XmppMultiUserChatHandle handle,
   const MultiUserChatRoomStateChangedEvent& evt )
{
   m_STAAssert.test();

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   // Grab the associated JID
   if( acctInfo->second.m_JIDMap.find( handle ) == acctInfo->second.m_JIDMap.end() )
      return;

   // And then the info .. :-P
   std::string roomJID( acctInfo->second.m_JIDMap[ handle ] );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return;

   // remember the config
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   chatInfo.maxHistoryFetch = evt.state.maxHistoryFetch;

   // Check and see if this chatroom should be ignored
   if( evt.state.isPersistent && m_Settings.ignoreNonPersistentMUC )
      m_ChatInfoMap.erase( roomJID ); // If so, just ignore it
}

void MessageStoreManagerXmppMultiUserImpl::onMultiUserChatNewMessage(
   XmppMultiUserChatHandle handle,
   const MultiUserChatNewMessageEvent& evt )
{
   m_STAAssert.test();

   // Only deal with "historical" events
   if( !evt.isDelayedDelivery )
      return;

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return; // didn't find anything, skip it, there should be at least some config

   // Create the cache if necessary
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   if( chatInfo.pCache == NULL )
   {
      MessageStoreImpl *pImpl = new MessageStoreImpl(
         dynamic_cast< CPCAPI2::PhoneInterface* >( m_Parent.m_Phone )->getSdkModuleThread(),
         m_Settings.cacheDirectory.c_str() );
      std::string trialFile( pImpl->buildFilename( roomJID.c_str() ));

      // First try to load the file, if that fails then create a new one.
      if( pImpl->loadFile( trialFile ) ||
          pImpl->createNewFile( roomJID.c_str() ))
      {
         // Remember the new object in the info
         chatInfo.pCache = pImpl;

         // Update this because the cache is now created
         m_CacheMap[ chatInfo.hCache ] = chatInfo.pCache;

         // Fire the event about creation of a new cache
         KreateEvent kevt;
         kevt.hMessageStore = chatInfo.hCache;
         kevt.roomID        = roomJID.c_str();
         m_Parent.fireEvent( cpcFunc( MessageStoreHandler::onCreate ), 0, kevt );
      }
      else
      {
         // Failed, perform cleanup and also remove the chatInfo
         delete pImpl;
         m_ChatInfoMap.erase( roomJID );
         return;
      }
   }

   // Create a new message info structure
   MessageInfo info;
   info.uniqueID           = evt.messageId;
   info.roomID             = chatInfo.chatJID.c_str();
   info.sender.uniqueID    = evt.jid.c_str();
   info.sender.alias       = evt.nickname;
   info.recipient.uniqueID = info.roomID; // this is the case in a multi-user chat
   info.htmlContent        = evt.html.c_str();
   info.plainTextContent   = evt.plain.c_str();
   info.timestampMillis    = ( evt.timestamp * 1000 ) + evt.millisecond;

   // NOTE: This does not save the flags nor the reactions, since we don't have that information here.
   // squirrel that away into the cache
   if( chatInfo.pCache->store( info, evt.replaces ))
      chatInfo.historyCount += 1; // increment upon success

   // Only perform gap analysis if maxHistoryFetch config was detected
   if( chatInfo.maxHistoryFetch > 0 )
   {
      // If this is the first history, remember the timestamp for later,
      // otherwise clear the old history if necessary.
      if( chatInfo.historyCount == 1 )
      {
         chatInfo.firstTimestampMillis = info.timestampMillis;
      }
      else if( chatInfo.historyCount == chatInfo.maxHistoryFetch )
      {
         chatInfo.pCache->flush( chatInfo.firstTimestampMillis );
         chatInfo.firstTimestampMillis = info.timestampMillis;
      }
   }
}

void MessageStoreManagerXmppMultiUserImpl::onMultiUserChatNewReaction(
   XmppMultiUserChatHandle handle,
   const MultiUserChatNewReactionEvent& evt )
{
   m_STAAssert.test();

   // Only deal with "historical" events
   if( !evt.isDelayedDelivery )
      return;

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return; // didn't find anything, skip it, there should be at least some config

   // Get the cache, and if it isn't found then there's nothing to react to
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   if( chatInfo.pCache == NULL )
      return;

   // update the reactions, which doesn't create new history, it just updates 
   // the existing history for the message (if it is found)
   chatInfo.pCache->updateReactions( evt.messageId, evt.reactions );
}

void MessageStoreManagerXmppMultiUserImpl::onMultiUserChatNewMessageRetraction(
   XmppMultiUserChatHandle handle,
   const MultiUserChatNewMessageRetractionEvent& evt )
{
   m_STAAssert.test();

   // Only deal with "historical" events
   if( !evt.isDelayedDelivery )
      return;

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return; // didn't find anything, skip it, there should be at least some config

   // Get the cache, and if it isn't found then there's nothing to remove
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   if( chatInfo.pCache == NULL )
      return;

   // remove it from the cache, which doesn't create new history, it just updates 
   // the existing history for the message (if it is found)
   chatInfo.pCache->discard( evt.messageId, ( evt.timestamp * 1000 ) + evt.millisecond );
}

void MessageStoreManagerXmppMultiUserImpl::onMultiUserChatSubjectChanged(
   XmppMultiUserChatHandle handle,
   const MultiUserChatSubjectChangedEvent& evt )
{
   m_STAAssert.test();

   // this event is fired after the history is completed/loaded for the room (according to the spec)

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );

   // The cache should already be created.
   if( chatInfo.pCache == NULL )
      return;

   // Check if the subject was changed already, just use the first one
   if( chatInfo.isSubjectChanged )
      return;

   // fire an event to indicate we're done loading this room
   HistoryLoadedEvent hle;
   hle.hMessageStore = chatInfo.hCache;
   hle.result.result = kSuccess;
   hle.hAccount = hAccount;
   m_Parent.fireEvent( cpcFunc( MessageStoreHandler::onHistoryLoaded ), 0, hle );

   // Mark it as loaded
   chatInfo.isSubjectChanged = true;
}

void MessageStoreManagerXmppMultiUserImpl::onLocalUserLeft(
   XmppMultiUserChatHandle handle,
   const LocalUserLeftEvent& evt )
{
   m_STAAssert.test();

   // This method is called when the local user 'leaves' a conversation, which
   // for the moment will reset the history count totals. That is because the
   // only time that history can be queried is on a join.

   // traverse from MUC handle to XMPP handle

   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return;

   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return;

   // Use the handle to obtain the JID
   std::string roomJID = acctInfo->second.m_JIDMap[ handle ];
   if( m_ChatInfoMap.find( roomJID ) != m_ChatInfoMap.end() )
   {
      m_ChatInfoMap[ roomJID ].historyCount = 0;
      m_ChatInfoMap[ roomJID ].isSubjectChanged = false;
   }
}

int MessageStoreManagerXmppMultiUserImpl::onMessageDelivered( XmppMultiUserChatHandle handle, const MessageDeliveredEvent& evt )
{
   m_STAAssert.test();

   // Only deal with "historical" events
   if( !evt.isDelayedDelivery )
      return kError;

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return kError;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return kError;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return kError;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return kError; // didn't find anything, skip it, there should be at least some config

   // Get the cache, and if it isn't found then there's nothing to mark delivered
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   if( chatInfo.pCache == NULL )
      return kError;

   // mark it delivered, which doesn't create new history, it just updates 
   // the existing history for the message (if it is found)
   chatInfo.pCache->markDelivered( evt.messageId );

   return kSuccess;
}

int MessageStoreManagerXmppMultiUserImpl::onMessageRead( XmppMultiUserChatHandle handle, const MessageReadEvent& evt )
{
   m_STAAssert.test();

   // Only deal with "historical" events
   if( !evt.isDelayedDelivery )
      return kError;

   // The account should be created already.
   std::map< XmppMultiUserChatHandle, XmppAccountHandle >::iterator iter = m_ChatToAcctMap.find( handle );
   if( iter == m_ChatToAcctMap.end() )
      return kError;

   // get the xmpp account info from the MUC chat handle
   XmppAccountHandle& hAccount( iter->second );
   std::map< XmppAccountHandle, AccountInfo >::iterator acctInfo = m_AccountInfoMap.find( hAccount );
   if( acctInfo == m_AccountInfoMap.end() )
      return kError;

   std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string >::iterator jidIter =
      acctInfo->second.m_JIDMap.find( handle );
   if( jidIter == acctInfo->second.m_JIDMap.end() )
      return kError;

   // get the muc info from the account info
   std::string roomJID( jidIter->second );
   if( m_ChatInfoMap.find( roomJID ) == m_ChatInfoMap.end() )
      return kError; // didn't find anything, skip it, there should be at least some config

   // Get the cache, and if it isn't found then there's nothing to mark read
   MultiUserChatInfo& chatInfo( m_ChatInfoMap[ roomJID ] );
   if( chatInfo.pCache == NULL )
      return kError;

   // mark it read, which doesn't create new history, it just updates 
   // the existing history for the message (if it is found)
   chatInfo.pCache->markRead( evt.messageId );

   return kSuccess;
}


#endif // CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1
#endif // CPCAPI2_BRAND_MESSAGESTORE_MODULE
