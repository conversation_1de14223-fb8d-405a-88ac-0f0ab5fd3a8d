#include "brand_branded.h"

#include "messagestore/MessageStoreManager.h"

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)
#include "MessageStoreManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
#include "xmpp/XmppChatManager.h"
#include "xmpp/XmppMultiUserChatManagerInterface.h"
#endif // CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE
#endif

#include <inttypes.h>
#include "util/APILogger.h"

namespace CPCAPI2
{
   namespace MessageStore
   {
      MessageStoreManager* MessageStoreManager::getInterface( CPCAPI2::Phone* cpcPhone )
      {
         API_INVOKE( "phone instance is: %" PRIdPTR, ( intptr_t ) cpcPhone );

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         MessageStoreManagerInterface *pInterface = _GetInterface<MessageStoreManagerInterface>( phone, "MessageStoreManager" );

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
         CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerInterface *pMUCMgr =
            dynamic_cast< CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerInterface *>(
               CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManager::getInterface( phone ));

         if( pInterface != NULL && pMUCMgr != NULL )
            pInterface->setMUCInterface( pMUCMgr );
#endif // CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE

         return pInterface;
#else
         return NULL;
#endif // CPCAPI2_BRAND_MESSAGESTORE_MODULE
      }
   }
}

