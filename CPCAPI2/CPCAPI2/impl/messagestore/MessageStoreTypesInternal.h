#pragma once
#ifndef __CPCAPI2_MESSAGE_STORE_TYPES_INTERNAL_H__
#define __CPCAPI2_MESSAGE_STORE_TYPES_INTERNAL_H__

#include <messagestore/MessageStoreTypes.h>
#include <xmpp/XmppMultiUserChatHandlerInternal.h>

#include <stdint.h>

// Persistence-related constants for the file cache
#define CPCAPI2_MESSAGE_STORE_CACHE_VERSION_NUM  1
#define CPCAPI2_MESSAGE_STORE_CACHE_NAMESPACE    "XMPPMUC"
#define CPCAPI2_MESSAGE_STORE_CACHE_DIVIDER      '.'
#define CPCAPI2_MESSAGE_STORE_CACHE_EXT          "CAC"
#define CPCAPI2_MESSAGE_STORE_CACHE_GUID_LEN     36

namespace CPCAPI2
{
   namespace MessageStore
   {
      // Forward decl's
      class MessageStoreManagerImpl;
      class MessageStoreImpl;

      // The purpose of this file is to have a common area for
      // implementation-specific types which are otherwise hidden from
      // application-land
   }

   namespace XmppMultiUserChat
   {
      // Convenience Stub for chat handlers (since it's not defined in the XmppMultiUserChat module)
      class XmppMultiUserChatHandlerStub : public XmppMultiUserChatHandlerInternal
      {
      public:

         // XmppMultiUserChatHandler
         void onServiceAvailability( XmppAccount::XmppAccountHandle account, const ServiceAvailabilityEvent& evt ) OVERRIDE {}
         void onRoomListRetrieved( XmppAccount::XmppAccountHandle account, const RoomListRetrievedEvent& evt ) OVERRIDE  {}
         void onParticipantAdded( XmppMultiUserChatHandle handle, const ParticipantAddedEvent& evt ) OVERRIDE {}
         void onParticipantRemoved( XmppMultiUserChatHandle handle, const ParticipantRemovedEvent& evt ) OVERRIDE {}
         void onParticipantUpdated( XmppMultiUserChatHandle handle, const ParticipantUpdatedEvent& evt ) OVERRIDE {}
         void onParticipantSelfUpdated( XmppMultiUserChatHandle handle, const ParticipantSelfUpdatedEvent& evt ) OVERRIDE {}
         void onMultiUserChatReady( XmppMultiUserChatHandle handle, const MultiUserChatReadyEvent& evt ) OVERRIDE {}
         void onMultiUserChatSubjectChanged( XmppMultiUserChatHandle handle, const MultiUserChatSubjectChangedEvent& evt ) OVERRIDE {}
         void onMultiUserChatNewMessage( XmppMultiUserChatHandle handle, const MultiUserChatNewMessageEvent& evt ) OVERRIDE {}
         void onMultiUserChatNewReaction( XmppMultiUserChatHandle handle, const MultiUserChatNewReactionEvent& evt ) OVERRIDE {}
         void onSendMessageSuccess( XmppMultiUserChatHandle handle, const SendMessageSuccessEvent& evt ) OVERRIDE {}
         void onSendMessageFailure( XmppMultiUserChatHandle handle, const SendMessageFailureEvent& evt ) OVERRIDE {}
         void onParticipantChatStateReceived( XmppMultiUserChatHandle handle, const ParticipantChatStateEvent& evt ) OVERRIDE {}
         void onMultiUserChatInvitationReceived( XmppMultiUserChatHandle handle, const MultiUserChatInvitationReceivedEvent& evt ) OVERRIDE {}
         void onMultiUserChatInvitationDeclined( XmppMultiUserChatHandle handle, const MultiUserChatInvitationDeclinedEvent& evt ) OVERRIDE {}
         void onMultiUserChatError( XmppMultiUserChatHandle handle, const MultiUserChatErrorEvent& evt ) OVERRIDE {}
         void onLocalUserLeft( XmppMultiUserChatHandle handle, const LocalUserLeftEvent& evt ) OVERRIDE {}
         void onMultiUserChatConfigurationRequested( XmppMultiUserChatHandle handle, const MultiUserChatConfigurationRequestedEvent& evt ) OVERRIDE {}
         void onMultiUserChatRoomStateChanged( XmppMultiUserChatHandle handle, const MultiUserChatRoomStateChangedEvent& evt ) OVERRIDE {}
         void onMultiUserChatListRequested( XmppMultiUserChatHandle handle, const MultiUserChatListRequestedEvent& evt ) OVERRIDE {}
         void onRoomBookmarksReceived( XmppAccount::XmppAccountHandle account, const RoomBookmarksReceivedEvent& evt ) OVERRIDE {}
         void onNewRoomHandle( XmppMultiUserChatHandle handle, const NewRoomEvent& evt ) OVERRIDE {}
		
		   // XmppMultiUserChatHandlerInternal
		   int onCreateMultiUserChatResult(CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc, const XmppMultiUserChatCreatedResultEvent& args) OVERRIDE { return kSuccess; }
      };
   }
}

#endif // __CPCAPI2_MESSAGE_STORE_TYPES_INTERNAL_H__
