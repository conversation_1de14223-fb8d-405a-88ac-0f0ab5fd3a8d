#include "brand_branded.h"

#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)

#include <cpcapi2defs.h>
#include "MessageStoreManagerXmppImpl.h"

#if ( CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1 )
#if 0
using namespace CPCAPI2::MessageStore;

std::atomic< MessageStoreHandle > MessageStoreManagerXmppImpl::s_CurrentHandle( 1 );

MessageStoreManagerXmppImpl::MessageStoreManagerXmppImpl( void )
   : m_Provider( ProviderType_XMPP )
{
   // instead of create
}

MessageStoreManagerXmppImpl::~MessageStoreManagerXmppImpl( void )
{
   // instead of destroy
}

bool MessageStoreManagerXmppImpl::retrieve( const MessageStoreHandle& hMessageStore, uint64_t timestampMillis, cpc::vector< MessageInfo >& infos, int limit )
{
   return false;
}

bool MessageStoreManagerXmppImpl::retrieveBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit )
{
   return false;
}

bool MessageStoreManagerXmppImpl::destroy( const MessageStoreHandle& hMessageStore )
{
   return false;
}

bool MessageStoreManagerXmppImpl::flush( const MessageStoreHandle& hMessageStore )
{
   return false;
}

bool MessageStoreManagerXmppImpl::getLastMessageInfo( const MessageStoreHandle& hMessageStore, MessageInfo& lastInfo )
{
   return 0;
}

#endif // 0
#endif // CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE

#endif
