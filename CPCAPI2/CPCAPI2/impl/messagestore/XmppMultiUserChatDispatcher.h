#pragma once
#ifndef __CPCAPI2_MESSAGE_STORE_XMPPMUCCHATOBSERVER_H__
#define __CPCAPI2_MESSAGE_STORE_XMPPMUCCHATOBSERVER_H__

#include "cpcapi2defs.h"

#include <assert.h>
#include <rutil/MultiReactor.hxx>

#include <xmpp/XmppAccount.h>
#include <xmpp/XmppMultiUserChatHandlerInternal.h>
#include "MessageStoreManagerImpl.h"
#include "MessageStoreTypesInternal.h"

#include "../xmpp/XmppMultiUserChatSyncHandler.h"

namespace CPCAPI2
{
   namespace MessageStore
   {
      /**
       * This dispatcher is required because of the nature of the chat handler
       * being an SDK observer requires that it be registered very early,
       * however due to our architecture we want to delay the actual handler
       * creation. Hence this listener is created and registered early, but it
       * will dispatch its calls to another component created later.
       */
      class XmppMultiUserChatDispatcher :
         public XmppMultiUserChat::XmppMultiUserChatHandlerStub,
         public XmppMultiUserChat::XmppMultiUserChatSyncHandler
      {
      public:

         XmppMultiUserChatDispatcher( const resip::MultiReactor& reactor ) : m_Reactor( reactor ), m_pHandler( NULL ) {}
         virtual ~XmppMultiUserChatDispatcher( void ) { m_pHandler = NULL; }

         void setDispatchee( XmppMultiUserChat::XmppMultiUserChatHandlerInternal* pHandler )
         {
            m_pHandler = pHandler; // Ensure that it's also a sync handler
         }

         void onServiceAvailability( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::ServiceAvailabilityEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onServiceAvailability( account, evt );
         }

         void onRoomListRetrieved( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::RoomListRetrievedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onRoomListRetrieved( account, evt );
         }

         void onParticipantAdded( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::ParticipantAddedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onParticipantAdded( handle, evt );
         }

         void onParticipantRemoved( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::ParticipantRemovedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onParticipantRemoved( handle, evt );
         }

         void onParticipantUpdated( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::ParticipantUpdatedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onParticipantUpdated( handle, evt );
         }

         void onParticipantSelfUpdated( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::ParticipantSelfUpdatedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onParticipantSelfUpdated( handle, evt );
         }

         void onMultiUserChatReady( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatReadyEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatReady( handle, evt );
         }

         void onMultiUserChatSubjectChanged( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatSubjectChangedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatSubjectChanged( handle, evt );
         }

         void onMultiUserChatNewMessage( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatNewMessage( handle, evt );
         }

         void onMultiUserChatNewReaction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewReactionEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatNewReaction( handle, evt );
         }

         void onMultiUserChatNewMessageRetraction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatNewMessageRetraction( handle, evt );
         }

         void onSendMessageSuccess( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::SendMessageSuccessEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onSendMessageSuccess( handle, evt );
         }

         void onSendMessageFailure( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::SendMessageFailureEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onSendMessageFailure( handle, evt );
         }

         void onParticipantChatStateReceived( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::ParticipantChatStateEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onParticipantChatStateReceived( handle, evt );
         }

         void onMultiUserChatInvitationReceived( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatInvitationReceivedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatInvitationReceived( handle, evt );
         }

         void onMultiUserChatInvitationDeclined( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatInvitationDeclinedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatInvitationDeclined( handle, evt );
         }

         void onMultiUserChatError( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatErrorEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatError( handle, evt );
         }

         void onLocalUserLeft( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::LocalUserLeftEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onLocalUserLeft( handle, evt );
         }

         int onMessageDelivered( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageDeliveredEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) return m_pHandler->onMessageDelivered( handle, evt );
            return kSuccess;
         }

         int onMessageRead( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageReadEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) return m_pHandler->onMessageRead( handle, evt );
            return kSuccess;
         }

         void onMultiUserChatConfigurationRequested( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatConfigurationRequestedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatConfigurationRequested( handle, evt );
         }

         void onMultiUserChatRoomStateChanged( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatRoomStateChanged( handle, evt );
         }

         void onMultiUserChatListRequested( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatListRequestedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onMultiUserChatListRequested( handle, evt );
         }

         void onRoomBookmarksReceived( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::RoomBookmarksReceivedEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onRoomBookmarksReceived( account, evt );
         }

         void onNewRoomHandle( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::NewRoomEvent& evt ) OVERRIDE
         {
            assert( m_Reactor.isCurrentThread() );
            if( m_pHandler != NULL ) m_pHandler->onNewRoomHandle( handle, evt );
         }

		 int onCreateMultiUserChatResult( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle muc, const XmppMultiUserChat::XmppMultiUserChatCreatedResultEvent& args ) OVERRIDE
		 {
			 assert(m_Reactor.isCurrentThread() );
			 if (m_pHandler != NULL) m_pHandler->onCreateMultiUserChatResult(muc, args);
			 return kSuccess;
		 }

      private:

         const resip::MultiReactor& m_Reactor; // chiefly just for thread checks
         XmppMultiUserChat::XmppMultiUserChatHandlerInternal *m_pHandler;
      };
   }
}

#endif // __CPCAPI2_MESSAGE_STORE_XMPPMUCCHATOBSERVER_H__
