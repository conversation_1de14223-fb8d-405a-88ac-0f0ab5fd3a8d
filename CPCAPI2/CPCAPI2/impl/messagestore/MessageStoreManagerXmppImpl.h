#pragma once
#ifndef __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_IMPL_H__
#define __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_IMPL_H__

#include <brand_branded.h>

#if ( CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1 )
#if 0
#include <map>
#include <list>
#include <utility>
#include <atomic>
#include <string>

#include <cpcstl/string.h>
#include "MessageStoreTypesInternal.h"
#include "MessageStoreManagerImpl.h"
#include "MessageStoreImpl.h"

namespace CPCAPI2
{
   namespace MessageStore
   {
      class MessageStoreManagerXmppImpl : public MessageStoreManagerImpl
      {
      public:
         MessageStoreManagerXmppImpl( void );
         virtual ~MessageStoreManagerXmppImpl( void );

         bool retrieve( const MessageStoreHandle& hMessageStore, uint64_t timestampMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) OVERRIDE;
         bool retrieveBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) OVERRIDE;
         bool destroy( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         bool flush( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         bool getLastMessageInfo( const MessageStoreHandle& hMessageStore, MessageInfo& lastInfo ) OVERRIDE;
         ProviderType getProviderType( void ) const OVERRIDE { return ProviderType_XMPP; }

      private: // methods

      private: // data

         // Static handle counter
         static std::atomic< MessageStoreHandle > s_CurrentHandle;

         typedef std::map< MessageStoreHandle, MessageStoreImpl* > CacheMap_t;
         CacheMap_t m_CacheMap;

         /**
          * The provider for messaging
          */
         const ProviderType m_Provider;

         /**
          * The entity (server) ID
          */
         const std::string m_ServerID;
      };
   }
}

#endif // 0
#endif // CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE
#endif // __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_IMPL_H__