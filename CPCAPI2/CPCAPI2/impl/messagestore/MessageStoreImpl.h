#pragma once
#ifndef __CPCAPI2_MESSAGE_STORE_XMPP_IMPL_H__
#define __CPCAPI2_MESSAGE_STORE_XMPP_IMPL_H__

#include <atomic>
#include <string>
#include <map>
#include <set>
#include <memory>

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <cpcstl/vector.h>

#include "MessageStoreTypesInternal.h"

namespace soci
{
   class session;
   class statement;
}

namespace CPCAPI2
{
   namespace MessageStore
   {
      /**
       * The message store cache is responsible for:
       *
       * - merging requests from each view into a single combined
       *   interpretation of the data that needs to be currently cached
       * - Separating the cached information by rooms
       * - Retrieving the requested data from the appropriate network provider
       * - Storing the returned data in a local cache
       * - Deleting/removing the cached data when it is no longer needed
       */
      class MessageStoreImpl : public resip::DeadlineTimerHandler
      {
      public:
         MessageStoreImpl( resip::MultiReactor& reactor, const std::string& directory );
         virtual ~MessageStoreImpl( void );

         /**
          * Tries to load the file from storage. This might fail if the
          * file doesn't currently exist. If the file was loaded successfully,
          * true will be returned, otherwise false will be returned.
          */
         bool loadFile( const std::string& fileName );

         /**
          * If loading a file failed, a new one will need to be created (and
          * initialized).  Returns true if creation was successful, false
          * otherwise.
          */
         bool createNewFile( const std::string& roomID );

         /**
          * Gets the room ID (might be empty if not loaded)
          */
         std::string getRoomID( void ) const;

         /**
          * Remembers (caches) a message info for later fast retrievals. If 
          * replacesMessageID is not empty and a message exists with this ID 
          * then it will be replaced with this one, otherwise a new message 
          * will be added.
          */
         bool store( MessageInfo& info, const cpc::string& replacesMessageID = "" );

         /**
          * Marks a cached message as "Deleted" and gives the time of the deletion.
          */
         bool discard( const cpc::string& messageID, uint64_t timestampMillis );

         /**
          * Marks a cached message as "Delivered".
          */
         bool markDelivered( const cpc::string& messageID );

         /**
          * Marks a cached message as "Read".
          */
         bool markRead( const cpc::string& messageID );

         /**
          * Updates the reactions for a cached message, replacing any that were there.
          */
         bool updateReactions( const cpc::string& messageID, const cpc::vector<cpc::string>& reactions );

         /**
          * retrieves the message infos from a specific timestamp and
          * forward to the current time. NB: it will include messages
          * whose timestamp matches sinceMillis
          *
          * @param sinceMillis the time in millis since the epoch, after which
          *        the message info should be retrieved
          * @param infos (outparam) the message information from the cache
          * @param limit the upper bound on the number of results, -1 if
          *        unlimited
          * @return true if successful, false on error.
          */
         bool retrieve( uint64_t sinceMillis, cpc::vector< MessageInfo >& infos, int limit );

         /**
          * retrieves the message infos from a specific timestamp and backward
          * until a given limit (or the start of the room). NB: it will include
          * messages whose timestamp matches sinceMillis
          *
          * @param sinceMillis the time in millis since the epoch, after which
          *        the message info should be retrieved
          * @param infos (outparam) the message information from the cache
          * @param limit the upper bound on the number of results, -1 if
          *        unlimited
          * @return true if successful, false on error.
          */
         bool retrieveBackward( uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit );

         /**
          * Deletes the file associated with this message store and releases
          * any resources associated with it (in preparation for deletion).
          */
         bool destroy( void );

         /**
          * Clears the cache from the beginning, up to (but not including)
          * any entries at the provided timestamp (this is a tool to remove
          * older blocks).
          */
         bool flush( uint64_t untilMillis );

         /**
          * Flushes all the data in the file, but maintains the header
          * information from the previous version
          */
         bool flush( void );

         /**
          * Returns the stored message information from the specified messageID
          */
         bool getMessageInfo( const cpc::string& messageID, MessageInfo& info );

         /**
          * Returns the last stored message info.
          */
         bool getLastMessageInfo( MessageInfo& lastInfo );

         /**
          * Gets a unique filename based on the roomID. This is a utility
          * method and has no bearing on the underlying object state. The room
          * name however will be reproducible based on the input.
          */
         std::string buildFilename( const std::string& roomID ) const;

         /**
          * Timer implementation
          */
         void onTimer( unsigned short timerId, void* appState ) OVERRIDE;

      private: // methods

         /**
          * Erases and initializes the content of the file
          */
         bool initFile( void );

         /**
          * Enables WAL mode for the database
          */
         bool enableWal( void );

         /**
          * IF there's an open write transaction, close it
          */
         void closeWriteTransaction( void );

      private: // data

         /**
          * The directory under which the database wil be stored
          */
         const std::string m_Directory;

         /**
          * The RoomID, this may initially be empty in the case where
          * the file is discovered (until the value is read from the
          * store).
          */
         std::string m_RoomID;

         /**
          * Member variable for database access
          */
         std::unique_ptr< soci::session > m_SOCI;

         /**
          * Since this class is normally run within the resip thread, a resip
          * timer should be used.
          *
          * Normally sqlite will do one transaction per INSERT, which is
          * very slow. In order to speed things up we use a timer to batch
          * together multiple operations into a single transaction.
          */
         resip::DeadlineTimer< resip::MultiReactor > m_WriteTransactionTimer;
         bool m_IsWriteTransactionOpen;
      };
   }
}

#endif //  __CPCAPI2_MESSAGE_STORE_XMPP_IMPL_H__
