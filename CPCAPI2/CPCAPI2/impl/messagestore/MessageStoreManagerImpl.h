#pragma once
#ifndef __CPCAPI2_MESSAGESTORE_MANAGER_IMPL_H__
#define __CPCAPI2_MESSAGESTORE_MANAGER_IMPL_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include "MessageStoreTypesInternal.h"
#include "MessageStoreImpl.h"

namespace CPCAPI2
{
   namespace MessageStore
   {
      /**
       * Abstract class/interface describing the behaviors for different Impls
       * for the various types of messaging.
       */
      class MessageStoreManagerImpl
      {
      public:

         virtual ~MessageStoreManagerImpl() {}

         /**
          * The purpose of the discover method is to check the file system for
          * cached message stores, and create any which are found for the
          * application to use.
          */
         virtual bool discover( void ) = 0;
         virtual bool retrieve( const MessageStoreHandle& hMessageStore, uint64_t timestampMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) = 0;
         virtual bool retrieveBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) = 0;
         virtual bool flush( const MessageStoreHandle& hMessageStore ) = 0;
         virtual bool destroy( const MessageStoreHandle& hMessageStore ) = 0;
         virtual bool getMessageInfo( const MessageStoreHandle& hMessageStore, const cpc::string& messageID, MessageInfo& outInfo ) = 0;
         virtual bool getLastMessageInfo( const MessageStoreHandle& hMessageStore, MessageInfo& outInfo ) = 0;
         virtual ProviderType getProviderType( void ) const = 0;
      };
   }
}

#endif // __CPCAPI2_MESSAGESTORE_MANAGER_IMPL_H__