#pragma once
#ifndef __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_MULTI_USER_IMPL_H__
#define __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_MULTI_USER_IMPL_H__

#include <map>
#include <list>
#include <utility>
#include <atomic>
#include <string>

#include <cpcstl/string.h>
#include <xmpp/XmppMultiUserChatHandler.h>
#include <xmpp/XmppMultiUserChatSyncHandler.h>
#include <util/STAAssertion.h>

#include "MessageStoreTypesInternal.h"
#include "MessageStoreManagerImpl.h"
#include "MessageStoreImpl.h"

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)

namespace CPCAPI2
{
   namespace MessageStore
   {
      class MessageStoreManagerInterface;

      class MessageStoreManagerXmppMultiUserImpl :
         public MessageStoreManagerImpl,
         public XmppMultiUserChat::XmppMultiUserChatHandlerStub,
         public XmppMultiUserChat::XmppMultiUserChatSyncHandler
      {
      public:
         MessageStoreManagerXmppMultiUserImpl( MessageStoreManagerInterface& parent, const Settings& settings );
         virtual ~MessageStoreManagerXmppMultiUserImpl( void );

         bool discover( void ) OVERRIDE;
         bool retrieve( const MessageStoreHandle& hMessageStore, uint64_t timestampMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) OVERRIDE;
         bool retrieveBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, cpc::vector< MessageInfo >& infos, int limit = -1 ) OVERRIDE;

         bool destroy( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         bool flush( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         bool getMessageInfo( const MessageStoreHandle& hMessageStore, const cpc::string& messageID, MessageInfo& outInfo ) OVERRIDE;
         bool getLastMessageInfo( const MessageStoreHandle& hMessageStore, MessageInfo& lastInfo ) OVERRIDE;
         ProviderType getProviderType( void ) const OVERRIDE { return ProviderType_XMPP_MultiUser; }

         // XmppMultiUserChatHandler
         void onServiceAvailability( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::ServiceAvailabilityEvent& evt ) OVERRIDE;
         void onMultiUserChatNewMessage( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageEvent& evt ) OVERRIDE;
         void onMultiUserChatNewReaction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewReactionEvent& evt ) OVERRIDE;
         void onMultiUserChatNewMessageRetraction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt ) OVERRIDE;
         void onMultiUserChatRoomStateChanged( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt ) OVERRIDE;
         void onMultiUserChatSubjectChanged( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatSubjectChangedEvent& evt ) OVERRIDE;
         void onNewRoomHandle( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::NewRoomEvent& evt ) OVERRIDE;
         void onLocalUserLeft( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::LocalUserLeftEvent& evt ) OVERRIDE;
         int onMessageDelivered( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageDeliveredEvent& evt ) OVERRIDE;
         int onMessageRead( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageReadEvent& evt ) OVERRIDE;

      private: // data

         STAAssertion m_STAAssert;

         // Static handle counter
         static std::atomic< MessageStoreHandle > s_CurrentHandle;

         // Handle back to parent interface
         MessageStoreManagerInterface& m_Parent;

         // Reference to the main settings
         const Settings& m_Settings;

         // Structure holding information related to a specific multi user chat
         // Note that this is scoped under an account.
         typedef struct MultiUserChatInfo
         {
            std::string chatJID; // the MUC Chat JID
            int maxHistoryFetch;
            int historyCount;
            uint64_t firstTimestampMillis; // First timestamp in a chunk of history
            XmppAccount::XmppAccountHandle hAccount;

            MessageStoreImpl* pCache;
            MessageStoreHandle hCache;

            bool isSubjectChanged;

            MultiUserChatInfo( void ) :
               maxHistoryFetch( 0 ),
               historyCount( 0 ),
               firstTimestampMillis( 0 ),
               hAccount( 0 ),
               pCache( NULL ),
               hCache( 0 ),
               isSubjectChanged( false ) {} // initializing ctor

            ~MultiUserChatInfo() {}
         } MultiUserChatInfo;

         /**
          * The map of room jid to chat info. We need this mapping because
          * we also need to handle the disconnected scenario, where the JID
          * (persisted JID) is the only piece of information available.
          */
         std::map< std::string, MultiUserChatInfo > m_ChatInfoMap;

         // Structure defined for holding information about the xmpp account
         typedef struct AccountInfo
         {
            // The JID used for conferencing (configurable on the server)
            std::string conferenceJID;

            // The map handles to JIDs
            std::map< XmppMultiUserChat::XmppMultiUserChatHandle, std::string > m_JIDMap;
         } AccountInfo;

         /**
          * The map of account handles to account info. Note that this impl
          * handles many accounts at the same time.
          */
         std::map< XmppAccount::XmppAccountHandle, AccountInfo > m_AccountInfoMap;

         /**
          * A "reverse" map of multi user chat handles, going back to xmpp account handles.
          */
         std::map< XmppMultiUserChat::XmppMultiUserChatHandle, XmppAccount::XmppAccountHandle > m_ChatToAcctMap;

         /**
          * A convenience map (for API usage mainly) going directly from handle to impl.
          * collection is NOT OWNED (meaning, responsibility to delete pointer lies with
          * the MultiUserChatInfo)
          */
         typedef std::map< MessageStoreHandle, MessageStoreImpl* > CacheMap_t;
         CacheMap_t m_CacheMap;

         /**
          * The provider for messaging
          */
         const ProviderType m_Provider;

         /**
          * The entity (server) ID
          */
         const std::string m_ServerID;
      };
   }
}

#endif // CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1
#endif // __CPCAPI2_MESSAGESTORE_MANAGER_XMPP_MULTI_USER_IMPL_H__
