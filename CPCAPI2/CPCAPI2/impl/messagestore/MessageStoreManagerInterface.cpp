#include "brand_branded.h"
#if (CPCAPI2_BRAND_MESSAGESTORE_MODULE == 1)

#include <assert.h>
#include <algorithm>

// For getcwd function
#ifdef _WIN32
#include <direct.h>
#else
#include <unistd.h>
#endif

#include "cpcapi2defs.h"

#include <phone/PhoneInterface.h>
#include "../xmpp/XmppAccountInterface.h"
#include "../xmpp/XmppMultiUserChatManagerInterface.h"

#include "MessageStoreManagerInterface.h"
#include "MessageStoreManagerImpl.h"
#include "MessageStoreManagerXmppImpl.h"
#include "MessageStoreManagerXmppMultiUserImpl.h"
#include "XmppMultiUserChatDispatcher.h"

#include "util/APILogger.h"
#include <inttypes.h>

using namespace CPCAPI2::MessageStore;

MessageStoreManagerInterface::MessageStoreManagerInterface( CPCAPI2::Phone* pPhone )
   : CPCAPI2::EventSource< MessageStoreHandle, MessageStoreHandler, MessageStoreSyncHandler >( dynamic_cast< CPCAPI2::PhoneInterface* >( pPhone )->getSdkModuleThread() ),
     m_pImpl( NULL ),
     m_Phone( pPhone ),
     m_Dispatcher( mReactor )
{
}

MessageStoreManagerInterface::~MessageStoreManagerInterface( void )
{
   m_Dispatcher.setDispatchee( NULL );
   delete m_pImpl;
}

void MessageStoreManagerInterface::logEvent( MessageStoreHandle handle, const char *funcName, const char *evtName )
{
   // handle is always zero here for .. reasons .. 'real' handle is inside the event
   // but that
   API_EVENT( funcName, "eventName is %s", evtName );
}

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
void MessageStoreManagerInterface::setMUCInterface( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerInterface * pInterface )
{
   pInterface->addSdkObserver( &m_Dispatcher ); // TODO: remove where?
}
#endif

void MessageStoreManagerInterface::Release()
{
   // this method is already inside of the DUM/reactor thread. But check it
   assert( mReactor.isCurrentThread() );
   delete this; // suicide :-P
}

int MessageStoreManagerInterface::setHandler( MessageStoreHandler *handler )
{
   API_INVOKE( "handler value is: %" PRIdPTR, ( intptr_t ) handler );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::setHandlerImpl, this, handler ));
   return kSuccess;
}

int MessageStoreManagerInterface::setHandlerImpl( MessageStoreHandler *handler )
{
   setAppHandlerImpl( 0, handler );

   HandlerEvent evt;
   evt.result.result = kSuccess;
   fireEvent( cpcFunc( MessageStoreHandler::onHandler ), 0, evt );
   return kSuccess;
}

int MessageStoreManagerInterface::applySettings( const Settings& settings )
{
   API_INVOKE( "cache dir set to: %s", settings.cacheDirectory.c_str() == NULL ? "(unset)" : settings.cacheDirectory.c_str() );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::applySettingsImpl, this, settings ));
   return kSuccess;
}

int MessageStoreManagerInterface::applySettingsImpl( const Settings& settings )
{
   m_Settings = settings;

   // For the time being just hard code this. In the future we can make a
   // factory pattern
   switch( settings.provider )
   {
#if 0 // ( CPCAPI2_BRAND_XMPP_ACCOUNT_MODULE == 1 )
   case ProviderType_XMPP:
      m_pImpl = new MessageStoreManagerXmppImpl;
      break;
#endif
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
   case ProviderType_XMPP_MultiUser:
      m_pImpl = new MessageStoreManagerXmppMultiUserImpl( *this, m_Settings );
      m_Dispatcher.setDispatchee( dynamic_cast< CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandlerInternal *>( m_pImpl ));
      break;
#endif
   default:
      assert( 0 && "unknown provider" );
      return kError;
   }

   // TODO: fire async event to indicate success/fail of this operation
   if( m_pImpl == NULL )
      return kError;

   // Ensure that there is a valid working directory
   if( m_Settings.cacheDirectory.empty() )
   {
      char *dir = NULL;
      dir = getcwd( NULL, 0 );
      if( dir != NULL )
      {
         m_Settings.cacheDirectory = dir;
         free( dir ); // NB: dir was allocated using malloc
      }
      else
      {
         return kError;
      }
   }

   // perform a discovery
   if( !m_pImpl->discover())
      return kError; // fire error

   return kSuccess;
}

int MessageStoreManagerInterface::destroy( const MessageStoreHandle& hMessageStore )
{
   API_INVOKE( "hMessageStore is %u", hMessageStore );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::destroyImpl, this, hMessageStore ));
   return kSuccess;
}

int MessageStoreManagerInterface::destroyImpl( const MessageStoreHandle& hMessageStore )
{
   DestroyEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;

   if( !m_pImpl->destroy( hMessageStore ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onDestroy ), 0, evt );
   return evt.result.result;
}


int MessageStoreManagerInterface::queryCachedHistory( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis )
{
   API_INVOKE( "hMessageStore is %u, sinceMillis is %" PRIu64, hMessageStore, sinceMillis );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::queryCachedHistoryImpl, this, hMessageStore, sinceMillis ));
   return kSuccess;
}

int MessageStoreManagerInterface::queryCachedHistoryImpl( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis )
{
   HistoryEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;

   if( !m_pImpl->retrieve( hMessageStore, sinceMillis, evt.history ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onQueryHistory ), 0, evt );
   return evt.result.result;
}

int MessageStoreManagerInterface::queryCachedHistory( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis, int limit )
{
   API_INVOKE( "hMessageStore is %u, sinceMillis is %" PRIu64 ", limit is %d", hMessageStore, sinceMillis, limit );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::queryCachedHistoryImpl2, this, hMessageStore, sinceMillis, limit ));
   return kSuccess;
}

int MessageStoreManagerInterface::queryCachedHistoryImpl2( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis, int limit )
{
   HistoryEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;

   if( !m_pImpl->retrieve( hMessageStore, sinceMillis, evt.history, limit ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onQueryHistory ), 0, evt );
   return evt.result.result;
}

int MessageStoreManagerInterface::queryCachedHistoryBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, int limit )
{
   API_INVOKE( "hMessageStore is %u, untilMillis is %" PRIu64 ", limit is %d", hMessageStore, untilMillis, limit );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::queryCachedHistoryBackwardImpl, this, hMessageStore, untilMillis, limit ));
   return kSuccess;
}

int MessageStoreManagerInterface::queryCachedHistoryBackwardImpl( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, int limit )
{
   HistoryEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;

   if( !m_pImpl->retrieveBackward( hMessageStore, untilMillis, evt.history, limit ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onQueryHistory ), 0, evt );
   return evt.result.result;
}

int MessageStoreManagerInterface::queryMessageInfo( const MessageStoreHandle& hMessageStore, const cpc::string& messageID )
{
   API_INVOKE( "hMessageStore is %u", hMessageStore );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::queryMessageInfoImpl, this, hMessageStore, messageID ));
   return kSuccess;
}

int MessageStoreManagerInterface::queryMessageInfoImpl( const MessageStoreHandle& hMessageStore, const cpc::string& messageID )
{
   MessageInfoEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;
   if( !m_pImpl->getMessageInfo( hMessageStore, messageID, evt.info ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onQueryMessageInfo ), 0, evt );
   return evt.result.result;
}

int MessageStoreManagerInterface::queryLastMessageInfo( const MessageStoreHandle& hMessageStore )
{
   API_INVOKE( "hMessageStore is %u", hMessageStore );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::queryLastMessageInfoImpl, this, hMessageStore ));
   return kSuccess;
}

int MessageStoreManagerInterface::queryLastMessageInfoImpl( const MessageStoreHandle& hMessageStore )
{
   LastMessageInfoEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;
   if( !m_pImpl->getLastMessageInfo( hMessageStore, evt.lastInfo ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onQueryLastMessageInfo ), 0, evt );
   return evt.result.result;
}

int MessageStoreManagerInterface::flushHistory( const MessageStoreHandle& hMessageStore )
{
   API_INVOKE( "hMessageStore is %u", hMessageStore );
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::flushHistoryImpl, this, hMessageStore ));
   return kSuccess;
}

int MessageStoreManagerInterface::flushHistoryImpl( const MessageStoreHandle& hMessageStore )
{
   FlushEvent evt;
   evt.hMessageStore = hMessageStore;
   evt.result.result = kSuccess;

   if( !m_pImpl->flush( hMessageStore ))
      evt.result.result = kError;

   fireEvent( cpcFunc( MessageStoreHandler::onHistoryFlush ), 0, evt );
   return evt.result.result;
}

#ifdef CPCAPI2_AUTO_TEST
#if ( CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1 )
void MessageStoreManagerInterface::fireOnServiceAvailability( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnServiceAvailabilityImpl, this, account, evt ));
}

void MessageStoreManagerInterface::fireOnServiceAvailabilityImpl( CPCAPI2::XmppAccount::XmppAccountHandle account, const CPCAPI2::XmppMultiUserChat::ServiceAvailabilityEvent& evt )
{
   m_Dispatcher.onServiceAvailability( account, evt );
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewMessage( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMultiUserChatNewMessageImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewMessageImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageEvent& evt )
{
   m_Dispatcher.onMultiUserChatNewMessage( handle, evt );
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewReaction( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewReactionEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMultiUserChatNewReactionImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewReactionImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewReactionEvent& evt )
{
   m_Dispatcher.onMultiUserChatNewReaction( handle, evt );
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewMessageRetraction( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMultiUserChatNewMessageRetractionImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMultiUserChatNewMessageRetractionImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt )
{
   m_Dispatcher.onMultiUserChatNewMessageRetraction( handle, evt );
}

void MessageStoreManagerInterface::fireOnMultiUserChatRoomStateChanged( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt)
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMultiUserChatRoomStateChangedImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMultiUserChatRoomStateChangedImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt)
{
   m_Dispatcher.onMultiUserChatRoomStateChanged( handle, evt );
}

void MessageStoreManagerInterface::fireOnNewRoomHandle( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::NewRoomEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnNewRoomHandleImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnNewRoomHandleImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::NewRoomEvent& evt )
{
   m_Dispatcher.onNewRoomHandle( handle, evt );
}

void MessageStoreManagerInterface::fireOnLocalUserLeft( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::LocalUserLeftEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnLocalUserLeftImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnLocalUserLeftImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::LocalUserLeftEvent& evt )
{
   m_Dispatcher.onLocalUserLeft( handle, evt );
}

void MessageStoreManagerInterface::fireOnMessageDelivered( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MessageDeliveredEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMessageDeliveredImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMessageDeliveredImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MessageDeliveredEvent& evt )
{
   m_Dispatcher.onMessageDelivered( handle, evt );
}

void MessageStoreManagerInterface::fireOnMessageRead( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MessageReadEvent& evt )
{
   postToSdkThread( resip::resip_bind( &MessageStoreManagerInterface::fireOnMessageReadImpl, this, handle, evt ));
}

void MessageStoreManagerInterface::fireOnMessageReadImpl( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatHandle handle, const CPCAPI2::XmppMultiUserChat::MessageReadEvent& evt )
{
   m_Dispatcher.onMessageRead( handle, evt );
}

#endif
#endif

#endif
