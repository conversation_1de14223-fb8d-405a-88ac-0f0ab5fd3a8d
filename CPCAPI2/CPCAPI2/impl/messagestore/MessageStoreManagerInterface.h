#pragma once
#ifndef __CPCAPI2_MESSAGESTORE_MANAGER_INTERFACE_H__
#define __CPCAPI2_MESSAGESTORE_MANAGER_INTERFACE_H__

#include <map>
#include <atomic>
#include <thread>
#include <memory>
#include <list>

#include <boost/asio.hpp>

#include "brand_branded.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <phone/PhoneModule.h>
#include <phone/Cpcapi2EventSource.h>

#include <xmpp/XmppMultiUserChatManager.h>
#include <xmpp/XmppMultiUserChatHandler.h>
#include <xmpp/XmppMultiUserChatSyncHandler.h>

#include <messagestore/MessageStoreManager.h>
#include "MessageStoreTypesInternal.h"
#include "MessageStoreSyncHandler.h"
#include "XmppMultiUserChatDispatcher.h"

namespace CPCAPI2
{
   // forward decl's
   class Phone;
   namespace XmppMultiUserChat
   {
      class XmppMultiUserChatManagerInterface;
   }

   namespace MessageStore
   {
      class MessageStoreManagerInterface :
         public CPCAPI2::EventSource< MessageStoreHandle, MessageStoreHandler, MessageStoreSyncHandler >, 
         public MessageStoreManager,
         public PhoneModule
      {
      public:
         MessageStoreManagerInterface( Phone* phone );
         virtual ~MessageStoreManagerInterface( void );

#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
         // Allows registration of the message store to the XMPP MUC system
         void setMUCInterface( CPCAPI2::XmppMultiUserChat::XmppMultiUserChatManagerInterface *pInterface );
#endif

         // MessageStoreManager
         int setHandler( MessageStoreHandler *handler ) OVERRIDE;
         int applySettings( const Settings& newSettings ) OVERRIDE;
         int destroy( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         int queryCachedHistory( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis ) OVERRIDE;
         int queryCachedHistory( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis, int limit ) OVERRIDE;
         int queryCachedHistoryBackward( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, int limit ) OVERRIDE;
         int queryMessageInfo( const MessageStoreHandle& hMessageStore, const cpc::string& messageID ) OVERRIDE;
         int queryLastMessageInfo( const MessageStoreHandle& hMessageStore ) OVERRIDE;
         int flushHistory( const MessageStoreHandle& hMessageStore ) OVERRIDE;

         int process(unsigned int timeout) OVERRIDE {
            return CPCAPI2::EventSource< MessageStoreHandle, MessageStoreHandler, MessageStoreSyncHandler >::process( timeout );
         }

         // Overridden just for API_EVENT logging
         void logEvent( MessageStoreHandle handle, const char *funcName, const char *evtName ) OVERRIDE;

         // thread-marshalled variants of above methods
         int setHandlerImpl( MessageStoreHandler* handler );
         int applySettingsImpl( const Settings& newSettings );
         int destroyImpl( const MessageStoreHandle& hMessageStore );
         int queryCachedHistoryImpl( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis );
         int queryCachedHistoryImpl2( const MessageStoreHandle& hMessageStore, uint64_t sinceMillis, int limit );
         int queryCachedHistoryBackwardImpl( const MessageStoreHandle& hMessageStore, uint64_t untilMillis, int limit );
         int queryMessageInfoImpl( const MessageStoreHandle& hMessageStore, const cpc::string& messageID );
         int queryLastMessageInfoImpl( const MessageStoreHandle& hMessageStore );
         int flushHistoryImpl( const MessageStoreHandle& hMessageStore );

         // PhoneModule
         void Release() OVERRIDE;

         // methods to fill the MUC chat data for unit tests
#ifdef CPCAPI2_AUTO_TEST
#if (CPCAPI2_BRAND_XMPP_MULTI_USER_CHAT_MODULE == 1)
         void fireOnServiceAvailability( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::ServiceAvailabilityEvent& evt );
         void fireOnMultiUserChatNewMessage( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageEvent& evt );
         void fireOnMultiUserChatNewReaction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewReactionEvent& evt );
         void fireOnMultiUserChatNewMessageRetraction( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt );
         void fireOnMultiUserChatRoomStateChanged( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt);
         void fireOnNewRoomHandle( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::NewRoomEvent& evt );
         void fireOnLocalUserLeft( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::LocalUserLeftEvent& evt );
         void fireOnMessageDelivered( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageDeliveredEvent& evt );
         void fireOnMessageRead( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageReadEvent& evt );
      private:
         void fireOnServiceAvailabilityImpl( XmppAccount::XmppAccountHandle account, const XmppMultiUserChat::ServiceAvailabilityEvent& evt );
         void fireOnMultiUserChatNewMessageImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageEvent& evt );
         void fireOnMultiUserChatNewReactionImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewReactionEvent& evt );
         void fireOnMultiUserChatNewMessageRetractionImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatNewMessageRetractionEvent& evt );
         void fireOnMultiUserChatRoomStateChangedImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MultiUserChatRoomStateChangedEvent& evt);
         void fireOnNewRoomHandleImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::NewRoomEvent& evt );
         void fireOnLocalUserLeftImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::LocalUserLeftEvent& evt );
         void fireOnMessageDeliveredImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageDeliveredEvent& evt );
         void fireOnMessageReadImpl( XmppMultiUserChat::XmppMultiUserChatHandle handle, const XmppMultiUserChat::MessageReadEvent& evt );
#endif
#endif

      private: // data
         friend class MessageStoreManagerXmppMultiUserImpl;

         MessageStoreManagerImpl *m_pImpl;
         Settings m_Settings;

         CPCAPI2::Phone* m_Phone;
         XmppMultiUserChatDispatcher m_Dispatcher;
      };
   }
}

#endif //  __CPCAPI2_MESSAGESTORE_MANAGER_INTERFACE_H__
