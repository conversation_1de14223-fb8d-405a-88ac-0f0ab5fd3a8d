#pragma once

#if !defined(CPCAPI2_VIDEO_STREAM_IMPL_RAW_H)
#define CPCAPI2_VIDEO_STREAM_IMPL_RAW_H

#include "cpcapi2defs.h"
#include "videostreaming/VideoStreaming.h"
#include "media/RawVideoFrameServer_WebSocket.h"

#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <map>
#include <memory>

namespace webrtc
{
class EncodedImage;
class EncodedImageCallback;
class EncodedAudioObserver;
class VideoFrame;
}

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class MediaManagerInterface;
}
namespace VideoStreaming
{
class EncodedFrameHandler2;
class VideoStreamDecodedFrameObserver;
class VideoStreamingInterface;
class RawVideoStreamImpl : public CPCAPI2::Media::RawVideoFrameServerObserver,
                           resip::DeadlineTimerHandler
{
public:
   RawVideoStreamImpl(CPCAPI2::PhoneInterface* phone, VideoStreamingInterface* ccif, VideoStreamHandle h);
   virtual ~RawVideoStreamImpl();

   /**/
   void handleEncodedImage(bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen);
   void handleRawVideoFrame(const webrtc::VideoFrame* videoFrame);

   void setVideoStreamSettings(const VideoStreamSettings& settings);
   void startVideoStream();
   void stopVideoStream();
   void initTranscoder();
   void getStreamCount(int& streamCount);

   // RawVideoFrameWebSocketObserver
   virtual void onOpen(int videoStream);

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState);

private:

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::Media::MediaManagerInterface* mMediaManager;
   VideoStreamingInterface* mInterface;
   VideoStreamHandle mHandle;
   VideoStreamSettings mSettings;
   std::unique_ptr<EncodedFrameHandler2> mEncodedFrameHandler;
   std::unique_ptr<VideoStreamDecodedFrameObserver> mDecodedFrameHandler;
   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   int64_t mLastFrameTime;
};
}
}

#endif // CPCAPI2_VIDEO_STREAM_IMPL_RAW_H

