#include "brand_branded.h"

#if (CPCAPI2_BRAND_VIDEO_STREAMING_MODULE == 1)
#include "VideoStreamImpl.h"
#include "VideoStreamingInterface.h"
#include "media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "auth_server/AuthServerJwtUtils.h"
#include "CodecConfig.hxx"


#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <codecs/OpenH264CodecImpl.hxx>
#include <codecs/OpenH264CodecWebRTC.hxx>

// WebRTC
#include <vie_codec.h>
#include <voe_base.h>
#include <vie_base.h>
#include <vie_rtp_rtcp.h>
#include <vie_image_process.h>
#include <webrtc/video_engine/vie_encoder.h>
#include <webrtc/system_wrappers/interface/clock.h>
#include <webrtc/video_encoder.h>
#include <webrtc/video_frame.h>
#include <webrtc/system_wrappers/interface/tick_util.h>
#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>
#include <libyuv.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <sstream>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace CPCAPI2::Media;

namespace CPCAPI2
{
namespace VideoStreaming
{
RawVideoStreamImpl::RawVideoStreamImpl(CPCAPI2::PhoneInterface* phone, VideoStreamingInterface* ccif, VideoStreamHandle h)
   : mPhone(phone),
     mMediaManager(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone))),
     mInterface(ccif),
     mHandle(h),
     mTimer(phone->getSdkModuleThread()),
     mLastFrameTime(0)
{
}

RawVideoStreamImpl::~RawVideoStreamImpl()
{
}

void RawVideoStreamImpl::setVideoStreamSettings(const VideoStreamSettings& settings)
{
   mSettings = settings;
}

class EncodedFrameHandler2 : public webrtc::EncodedImageCallback
{
public:
   EncodedFrameHandler2(RawVideoStreamImpl* frag)
      : mImpl(frag) {}

   virtual ~EncodedFrameHandler2() {}

   int32_t Encoded(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation)
   {
      if (codec_specific_info != NULL)
      {
         if (codec_specific_info->codecType != webrtc::kVideoCodecH264)
         {
            mImpl->initTranscoder();
         }
         else
         {
            mImpl->handleEncodedImage(encoded_image._frameType == webrtc::kKeyFrame, encoded_image._buffer, encoded_image._length);
         }
      }
      return 0;
   }

   virtual int32_t EncodedSpatialLayer(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation,
      int layerId) {
      if (layerId == 1) {
         if (codec_specific_info->codecType != webrtc::kVideoCodecH264)
         {
            mImpl->initTranscoder();
         }
         else
         {
            mImpl->handleEncodedImage(encoded_image._frameType == webrtc::kKeyFrame, encoded_image._buffer, encoded_image._length);
         }
      }
      return 0;
   }

   void SetWantsKeyFrame(bool /*wantsKeyFrame*/) {}
   bool WantsKeyFrame() const { return false; }

private:
   RawVideoStreamImpl* mImpl;
};

class VideoStreamDecodedFrameObserver : public webrtc_recon::FrameMonitorExternalObserver
{
public:
   VideoStreamDecodedFrameObserver(RawVideoStreamImpl* frag)
      : mImpl(frag), mTranscoderChannel(-1), mSentKeyFrame(false) {}
   virtual ~VideoStreamDecodedFrameObserver() 
   {
      if (mOpenH264.get() != NULL)
         mOpenH264->Release();
   }

   int InitTranscoder(const std::shared_ptr<webrtc_recon::MediaStackImpl>& mediaStack, webrtc::EncodedImageCallback* encodedImageCallback)
   {
#if defined(CODECFACTORY_ENABLE_OPENH264)
      std::shared_ptr<webrtc_recon::CpsiCodec> videoCodecH264 = std::dynamic_pointer_cast<webrtc_recon::CodecFactoryImpl>(mediaStack->codecFactory())->getVideoCodec("h264");
      if (videoCodecH264.get() != NULL)
      {
         mOpenH264.reset(new webrtc_recon::OpenH264Encoder);
         webrtc::VideoCodec webrtcCodecH264 = videoCodecH264->allSettings()[0].webrtcCodecInfo.video;
         webrtcCodecH264.width = 1280;
         webrtcCodecH264.height = 720;
         webrtcCodecH264.maxBitrate = 2000;
         webrtcCodecH264.startBitrate = 2000;
         webrtcCodecH264.targetBitrate = 2000;
         webrtcCodecH264.minBitrate = 2000;
         webrtcCodecH264.mode = webrtc::kRealtimeVideo;
         webrtcCodecH264.qpMax = 51;
         webrtcCodecH264.maxFramerate = 30;
         webrtcCodecH264.numberOfSimulcastStreams = 1;
         webrtcCodecH264.codecSpecific.H264.packetization = webrtc::kH264SingleMode;
         webrtcCodecH264.codecSpecific.H264.keyFrameInterval = 0;
         webrtcCodecH264.codecSpecific.H264.profile = webrtc::H264::kProfileBaseline;
         webrtcCodecH264.codecSpecific.H264.level = 51;
         mOpenH264->InitEncode(&webrtcCodecH264, std::thread::hardware_concurrency(), 16000);
         mOpenH264->RegisterEncodeCompleteCallback(encodedImageCallback);
      }
#endif // #if defined(CODECFACTORY_ENABLE_OPENH264)
      return 0;
   }

   // ViEEffectFilter
   virtual int Transform(webrtc::VideoFrame* video_frame) OVERRIDE
   {
      webrtc::CodecSpecificInfo codecInfo;
      std::vector<webrtc::VideoFrameType> frameTypes;
      if (!mSentKeyFrame)
      {
         frameTypes.push_back(webrtc::kKeyFrame);
         mSentKeyFrame = true;
      }
      if (video_frame->ntp_time_ms() == 0)
      {
         video_frame->set_ntp_time_ms(webrtc::TickTime::MillisecondTimestamp());
      }
      if (mOpenH264.get() != NULL)
      {
         mOpenH264->Encode(*video_frame, &codecInfo, &frameTypes);
      }
      else
      {
         mImpl->handleRawVideoFrame(video_frame);
      }
      return 0;
   }

   virtual void Release() OVERRIDE
   {
      delete this;
   }

private:
   RawVideoStreamImpl* mImpl;
   int mTranscoderChannel;
   webrtc_recon::DummyWebrtcExternalTransport mDummyMcuTransport;
   std::unique_ptr<webrtc_recon::OpenH264Encoder> mOpenH264;
   bool mSentKeyFrame;
};

void RawVideoStreamImpl::initTranscoder()
{
   if (!mDecodedFrameHandler && mEncodedFrameHandler)
   {
      std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());

      mediaStack->videoCodec()->DeregisterPreDecodeObserver(mSettings.videoReceiveChannel, mEncodedFrameHandler.get());

      mDecodedFrameHandler.reset(new VideoStreamDecodedFrameObserver(this));
      mDecodedFrameHandler->InitTranscoder(mediaStack, mEncodedFrameHandler.get());
      mixer->registerDecodedFrameObserver(mSettings.videoReceiveChannel, mDecodedFrameHandler.get());
      mediaStack->vie_rtp_rtcp()->RequestKeyFrame(mSettings.videoReceiveChannel);
   }
}

void RawVideoStreamImpl::startVideoStream()
{
   if (!mEncodedFrameHandler && mInterface->websocket_server().get() != NULL)
   {
      std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();

      if (mSettings.streamFormat == StreamFormat_H264)
      {
         mEncodedFrameHandler.reset(new EncodedFrameHandler2(this));
         if (mSettings.videoReceiveChannel >= 0)
         {
            mediaStack->videoCodec()->RegisterPreDecodeObserver(mSettings.videoReceiveChannel, *mEncodedFrameHandler);
            mediaStack->vie_rtp_rtcp()->RequestKeyFrame(mSettings.videoReceiveChannel);
            mInterface->websocket_server()->AddObserver(this);
         }
         else if (mSettings.videoSendChannel >= 0)
         {
            webrtc::ViEEncoder* encoder = mediaStack->vie_base()->GetEncoder(mSettings.videoSendChannel);
            encoder->RegisterPostEncodeImageCallback(mEncodedFrameHandler.get());
            encoder->SendKeyFrame();
            mInterface->websocket_server()->AddObserver(this);
         }
         else if (mSettings.cameraCaptureId >= 0)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
            mDecodedFrameHandler.reset(new VideoStreamDecodedFrameObserver(this));
            mDecodedFrameHandler->InitTranscoder(mediaStack, mEncodedFrameHandler.get());
            mixer->registerCameraFrameObserver(mDecodedFrameHandler.get());
         }
         mTimer.expires_from_now(300);
         mTimer.async_wait(this, 0, NULL);
      }
      else if (mSettings.streamFormat == StreamFormat_I420)
      {
         if (mSettings.videoReceiveChannel >= 0)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
            mDecodedFrameHandler.reset(new VideoStreamDecodedFrameObserver(this));
            mixer->registerDecodedFrameObserver(mSettings.videoReceiveChannel, mDecodedFrameHandler.get());
         }
         else if (mSettings.cameraCaptureId >= 0)
         {
            std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
            mDecodedFrameHandler.reset(new VideoStreamDecodedFrameObserver(this));
            mixer->registerCameraFrameObserver(mDecodedFrameHandler.get());
         }
         mTimer.expires_from_now(300);
         mTimer.async_wait(this, 0, NULL);
      }
   }
}

void RawVideoStreamImpl::stopVideoStream()
{
   if (mDecodedFrameHandler.get() != NULL)
   {
      std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
      std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
      if (mSettings.videoReceiveChannel >= 0)
      {
         mixer->unregisterDecodedFrameObserver(mSettings.videoReceiveChannel, mDecodedFrameHandler.get());
      }
      if (mSettings.cameraCaptureId >= 0)
      {
         mixer->unregisterCameraFrameObserver(mDecodedFrameHandler.get());
      }
   }
   else
   {
      if (mEncodedFrameHandler.get() != NULL)
      {
         std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
         mediaStack->videoCodec()->DeregisterPreDecodeObserver(mSettings.videoReceiveChannel, mEncodedFrameHandler.get());
         if (mSettings.videoSendChannel >= 0)
         {
            mediaStack->vie_base()->GetEncoder(mSettings.videoSendChannel)->UnregisterPostEncodeImageCallback(mEncodedFrameHandler.get());
         }
      }
   }
   mDecodedFrameHandler.reset();
   mEncodedFrameHandler.reset();

   if (mInterface->websocket_server())
   {
      mInterface->websocket_server()->RemoveObserver(this);
      mInterface->websocket_server()->CloseSessions((int)mHandle);
   }
}

void RawVideoStreamImpl::getStreamCount(int& streamCount)
{
   if (mInterface->websocket_server())
   {
      streamCount = mInterface->websocket_server()->GetSessionCount((int)mHandle);
   }
}

void RawVideoStreamImpl::onOpen(int videoStream)
{
   if (videoStream == mHandle)
   {
      if (mSettings.videoSendChannel >= 0)
      {
         std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
         std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mediaStack->mixer());
         mixer->connectVideoMcuMixChannel();
         webrtc::ViEEncoder* encoder = mediaStack->vie_base()->GetEncoder(mSettings.videoSendChannel);
         encoder->SendKeyFrame();
      }
   }
}

void RawVideoStreamImpl::handleEncodedImage(bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen)
{
   int videoStream = mHandle;
   mInterface->websocket_server()->SendBitstreamData(videoStream, isKeyFrame, bitstream, bitstreamLen);
   mLastFrameTime = webrtc::TickTime::MillisecondTimestamp();
}

void RawVideoStreamImpl::handleRawVideoFrame(const webrtc::VideoFrame* videoFrame)
{
   int videoStream = mHandle;
   unsigned int frameWidth = (unsigned int)videoFrame->width();
   unsigned int frameHeight = (unsigned int)videoFrame->height();
   mInterface->websocket_server()->SendRawI420BistreamData(videoStream, frameWidth, frameHeight, videoFrame->buffer(webrtc::kYPlane), videoFrame->stride(webrtc::kYPlane), videoFrame->buffer(webrtc::kUPlane), videoFrame->stride(webrtc::kUPlane), videoFrame->buffer(webrtc::kVPlane), videoFrame->stride(webrtc::kVPlane));
   mLastFrameTime = webrtc::TickTime::MillisecondTimestamp();
}

void RawVideoStreamImpl::onTimer(unsigned short timerId, void* appState)
{
   if (mDecodedFrameHandler.get() == NULL)
      return;

   if (webrtc::TickTime::MillisecondTimestamp() - mLastFrameTime > 500)
   {
      webrtc::VideoFrame videoFrame;
      int stride_y, stride_uv = 0;
      webrtc::Calc16ByteAlignedStride(352, &stride_y, &stride_uv);
      videoFrame.CreateEmptyFrame(352, 288, stride_y, stride_uv, stride_uv);
      libyuv::I420Rect(videoFrame.buffer(webrtc::kYPlane), videoFrame.stride(webrtc::kYPlane),
         videoFrame.buffer(webrtc::kUPlane), videoFrame.stride(webrtc::kUPlane),
         videoFrame.buffer(webrtc::kVPlane), videoFrame.stride(webrtc::kVPlane),
         0, 0, 352, 288,
         0, 128, 128);
      mDecodedFrameHandler->Transform(&videoFrame);
   }
}
}
}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
