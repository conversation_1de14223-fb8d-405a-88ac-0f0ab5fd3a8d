#pragma once

#if !defined(CPCAPI2_VIDEO_STREAMING_INTERFACE_H)
#define CPCAPI2_VIDEO_STREAMING_INTERFACE_H

#include "cpcapi2defs.h"
#include "videostreaming/VideoStreaming.h"
#include "phone/Cpcapi2EventSource.h"
#include "media/RawVideoFrameServer.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"

#include <rutil/Random.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace VideoStreaming
{
class RawVideoStreamImpl;
class VideoStreamSyncHandler {};

class VideoStreamingInterface : public VideoStreamingManager
                              , public PhoneModule
                              , public CPCAPI2::EventSource<VideoStreamHandle, VideoStreamHandler, VideoStreamSyncHandler>
{
public:
   VideoStreamingInterface(Phone* phone);
   virtual ~VideoStreamingInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // VideoStreamingManager
   virtual int startVideoStreamingServer(const VideoStreamingServerConfig& serverConfig) OVERRIDE;
   virtual int stopVideoStreamingServer() OVERRIDE;
   virtual int setVideoStreamingServer(VideoStreamingManager* masterVideoStreamingManager) OVERRIDE;

   virtual VideoStreamHandle createVideoStream() OVERRIDE;
   virtual int setVideoStreamSettings(VideoStreamHandle videoStream, const VideoStreamSettings& settings) OVERRIDE;
   virtual int setVideoStreamHandler(VideoStreamHandle videoStream, VideoStreamHandler* handler) OVERRIDE;
   virtual int startVideoStream(VideoStreamHandle videoStream) OVERRIDE;
   virtual int stopVideoStream(VideoStreamHandle videoStream) OVERRIDE;
   virtual int destroyVideoStream(VideoStreamHandle videoStream) OVERRIDE;
   virtual int getStreamCount(VideoStreamHandle videoStream) OVERRIDE;

   virtual int addVideoStreamCallback(VideoStreamHandle videoStream, void* cbState, void(*funcToRun)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)) OVERRIDE;
   virtual int removeVideoStreamCallback(VideoStreamHandle videoStream) OVERRIDE;

   void setOnPrivilegedAccessCompleted(void(*onPrivilegedAccessCompleted)(void*), void* context) OVERRIDE { mOnPrivilegedAccessCompleted = std::bind(onPrivilegedAccessCompleted, context); }

   void post(resip::ReadCallbackBase* f);

   const std::shared_ptr<CPCAPI2::Media::RawVideoFrameServer>& websocket_server() const {
      return mWsServer;
   }

private:
   void startVideoStreamingServerImpl(const VideoStreamingServerConfig& serverConfig);
   void stopVideoStreamingServerImpl();
   void setVideoStreamingServerImpl(VideoStreamingManager* masterMgr);
   void createVideoStreamImpl(VideoStreamHandle videoStream);
   void setVideoStreamSettingsImpl(VideoStreamHandle videoStream, const VideoStreamSettings& settings);
   void startVideoStreamImpl(VideoStreamHandle videoStream);
   void stopVideoStreamImpl(VideoStreamHandle videoStream);
   void destroyVideoStreamImpl(VideoStreamHandle videoStream);
   void getStreamCountImpl(VideoStreamHandle videoStream, int* streamCount);
   void addVideoStreamCallbackImpl(VideoStreamHandle videoStream, void* cbState, void(*funcToRun)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int));
   void removeVideoStreamCallbackImpl(VideoStreamHandle videoStream);

private:
   PhoneInterface* mPhone;
   typedef std::map<VideoStreamHandle, RawVideoStreamImpl*> InstanceMap;
   InstanceMap mInstMap;
   std::shared_ptr<CPCAPI2::Media::RawVideoFrameServer> mWsServer;
   bool mOwnWsServer;

   std::function<void()> mOnPrivilegedAccessCompleted;
};

class VideoStreamHandleFactory
{
public:
   static VideoStreamHandle getNext() {
      VideoStreamHandle randomHandle = (VideoStreamHandle)(std::abs(resip::Random::getCryptoRandom()) % 0x7fff) + 1;
      return randomHandle;
   }
};
}
}

#endif // CPCAPI2_VIDEO_STREAMING_INTERFACE_H
