#include "brand_branded.h"

#if (CPCAPI2_BRAND_VIDEO_STREAMING_MODULE == 1)
#include "cpcapi2utils.h"
#include "VideoStreamingInterface.h"
#include "VideoStreamImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#if (CPCAPI2_BRAND_RAW_VIDEO_WEBSOCKET_SERVER == 1)
#include "media/RawVideoFrameServer_WebSocket.h"
#endif
#if (CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER == 1)
#include "media/RawVideoFrameServer_Callback.h"
#endif

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace VideoStreaming
{

VideoStreamingInterface::VideoStreamingInterface(Phone* phone)
   : EventSource<VideoStreamHandle, VideoStreamHandler, VideoStreamSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mOwnWsServer(false)
{
   mPhone->addRefImpl();
}

VideoStreamingInterface::~VideoStreamingInterface()
{
   if (mWsServer.get() != NULL)
   {
      mWsServer->StopServer();
   }
   mPhone->releaseImpl();
}

void VideoStreamingInterface::Release()
{
   delete this;
}

void VideoStreamingInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

int VideoStreamingInterface::startVideoStreamingServer(const VideoStreamingServerConfig& serverConfig)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::startVideoStreamingServerImpl, this, serverConfig));
   return 0;
}

void VideoStreamingInterface::startVideoStreamingServerImpl(const VideoStreamingServerConfig& serverConfig)
{
   mOwnWsServer = true;
   if (serverConfig.serverType == ServerType_Websocket)
   {
#if (CPCAPI2_BRAND_RAW_VIDEO_WEBSOCKET_SERVER == 1)
      mWsServer.reset(new CPCAPI2::Media::RawVideoFrameServerWebSocket());
#else
      assert(0);
#endif
   }
   else if (serverConfig.serverType == ServerType_CallbackFunction)
   {
#if (CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER == 1)
      mWsServer.reset(new CPCAPI2::Media::RawVideoFrameServerCallback());
#else
      assert(0);
#endif
   }
   mWsServer->StartServer(mPhone, serverConfig, NULL, mOnPrivilegedAccessCompleted);
}

int VideoStreamingInterface::stopVideoStreamingServer()
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::stopVideoStreamingServerImpl, this));
   return 0;
}

void VideoStreamingInterface::stopVideoStreamingServerImpl()
{
   if (mOwnWsServer)
   {
      mWsServer->StopServer();
   }
   else
   {
      mWsServer.reset();
   }
}

int VideoStreamingInterface::setVideoStreamingServer(VideoStreamingManager* masterVideoStreamingManager)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::setVideoStreamingServerImpl, this, masterVideoStreamingManager));
   return 0;
}

void VideoStreamingInterface::setVideoStreamingServerImpl(VideoStreamingManager* masterMgr)
{
   mOwnWsServer = false;
   VideoStreamingInterface* vsi = dynamic_cast<VideoStreamingInterface*>(masterMgr);
   mWsServer = vsi->websocket_server();
}

VideoStreamHandle VideoStreamingInterface::createVideoStream()
{
   VideoStreamHandle h = VideoStreamHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::createVideoStreamImpl, this, h));
   return h;
}

void VideoStreamingInterface::createVideoStreamImpl(VideoStreamHandle videoStream)
{
   RawVideoStreamImpl* pimpl = new RawVideoStreamImpl(mPhone, this, videoStream);
   mInstMap[videoStream] = pimpl;
}

int VideoStreamingInterface::setVideoStreamSettings(VideoStreamHandle videoStream, const VideoStreamSettings& settings)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::setVideoStreamSettingsImpl, this, videoStream, settings));
   return 0;
}

void VideoStreamingInterface::setVideoStreamSettingsImpl(VideoStreamHandle videoStream, const VideoStreamSettings& settings)
{
   VideoStreamingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->setVideoStreamSettings(settings);
   }
}

int VideoStreamingInterface::setVideoStreamHandler(VideoStreamHandle videoStream, VideoStreamHandler* handler)
{
   return setAppHandler(videoStream, handler);
}

int VideoStreamingInterface::startVideoStream(VideoStreamHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::startVideoStreamImpl, this, videoStream));
   return 0;
}

void VideoStreamingInterface::startVideoStreamImpl(VideoStreamHandle videoStream)
{
   VideoStreamingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->startVideoStream();
   }
}

int VideoStreamingInterface::stopVideoStream(VideoStreamHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::stopVideoStreamImpl, this, videoStream));
   return 0;
}

void VideoStreamingInterface::stopVideoStreamImpl(VideoStreamHandle videoStream)
{
   VideoStreamingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      RawVideoStreamImpl* pimpl = it->second;
      pimpl->stopVideoStream();
   }
}

int VideoStreamingInterface::destroyVideoStream(VideoStreamHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::destroyVideoStreamImpl, this, videoStream));
   return 0;
}

void VideoStreamingInterface::destroyVideoStreamImpl(VideoStreamHandle videoStream)
{
   VideoStreamingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      RawVideoStreamImpl* pimpl = it->second;
      pimpl->stopVideoStream();
      delete pimpl;
      mInstMap.erase(it);
   }
}

int VideoStreamingInterface::getStreamCount(VideoStreamHandle videoStream)
{
   std::unique_ptr<int> streamCount(new int);
   *streamCount = 0;
   executeOnSdkThread(resip::resip_bind(&VideoStreamingInterface::getStreamCountImpl, this, videoStream, streamCount.get()));
   return *streamCount;
}

void VideoStreamingInterface::getStreamCountImpl(VideoStreamHandle videoStream, int* streamCount)
{
   VideoStreamingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->getStreamCount(*streamCount);
   }
}

int VideoStreamingInterface::addVideoStreamCallback(VideoStreamHandle videoStream, void* cbState, void(*funcToRun)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int))
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::addVideoStreamCallbackImpl, this, videoStream, cbState, funcToRun));
   return 0;
}

void VideoStreamingInterface::addVideoStreamCallbackImpl(VideoStreamHandle videoStream, void* cbState, void(*funcToRun)(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int))
{
#if (CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER == 1)
   InfoLog(<< "Adding callback for video stream " << videoStream);
   std::dynamic_pointer_cast<CPCAPI2::Media::RawVideoFrameServerCallback>(mWsServer)->AddCallback(videoStream, cbState, funcToRun);
#endif
}

int VideoStreamingInterface::removeVideoStreamCallback(VideoStreamHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&VideoStreamingInterface::removeVideoStreamCallbackImpl, this, videoStream));
   return 0;
}

void VideoStreamingInterface::removeVideoStreamCallbackImpl(VideoStreamHandle videoStream)
{
#if (CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER == 1)
   std::dynamic_pointer_cast<CPCAPI2::Media::RawVideoFrameServerCallback>(mWsServer)->RemoveCallback(videoStream);
#endif
}

}
}

#endif
