#include "brand_branded.h"

#include "interface/experimental/videostreaming/VideoStreaming.h"

#if (CPCAPI2_BRAND_VIDEO_STREAMING_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "VideoStreamingInterface.h"
#endif

namespace CPCAPI2
{
namespace VideoStreaming
{
   VideoStreamingManager* VideoStreamingManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_VIDEO_STREAMING_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<VideoStreamingInterface>(phone, "VideoStreamingManager");
#else
      return NULL;
#endif
   }

}
}
