#ifndef CPCAPI2_MP4_RECORDING_FILE_WRITER_H
#define CPCAPI2_MP4_RECORDING_FILE_WRITER_H

#include <mp4v2/mp4v2.h>

#include <memory>
#include <string>

namespace webrtc
{
class EncodedImage;
class EncodedImageCallback;
class EncodedAudioObserver;
class RTPFragmentationHeader;
}

namespace CPCAPI2
{
namespace Media
{
class AVRecordingFileWriter;
class AVRecordingEncodedFrameHandler2;
class AVRecordingEncodedAudioObserver;

class AVRecordingFileWriter
{
public:
   AVRecordingFileWriter();
   virtual ~AVRecordingFileWriter();

   void Init(const std::string& filenameUtf8);

   void HandleEncodedImage(const webrtc::EncodedImage& encoded_image, const webrtc::RTPFragmentationHeader* fragmentation);
   webrtc::EncodedImageCallback& EncodedFrameHandler() const;

   void HandleEncodedAudio(uint32_t timeStamp,
      const uint8_t* payloadData,
      size_t payloadSize);
   webrtc::EncodedAudioObserver& EncodedAudioObserver() const;

private:
   std::unique_ptr<AVRecordingEncodedFrameHandler2> mEncodedFrameHandler;
   std::unique_ptr<AVRecordingEncodedAudioObserver> mEncodedAudioObserver;

   MP4FileHandle mMp4File;
   MP4TrackId mAudioTrack;
   MP4TrackId mVideoTrack;
   std::unique_ptr<webrtc::EncodedImage> mPrevFrame;
   int64_t mLastVideoFrameTimestamp;
   int64_t mIsoTimeOffsetVideo;
};
}
}

#endif // CPCAPI2_MP4_RECORDING_FILE_WRITER_H