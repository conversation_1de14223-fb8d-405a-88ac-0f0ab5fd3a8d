#include "brand_branded.h"

#if (CPCAPI2_BRAND_MP4_RECORDING_MODULE == 1)
#include "cpcapi2utils.h"
#include "Mp4RecordingInterface.h"
#include "Mp4RecordingImpl.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{
namespace Mp4Recording
{
std::atomic<Mp4RecordingHandle> Mp4RecordingHandleFactory::sNextHandle(1);

Mp4RecordingInterface::Mp4RecordingInterface(Phone* phone)
   : EventSource<Mp4RecordingHandle, Mp4RecordingHandler, Mp4RecordingSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mPhone->addRefImpl();
}

Mp4RecordingInterface::~Mp4RecordingInterface()
{
   mPhone->releaseImpl();
}

void Mp4RecordingInterface::Release()
{
   delete this;
}

void Mp4RecordingInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

Mp4RecordingHandle Mp4RecordingInterface::createRecordingSession()
{
   Mp4RecordingHandle h = Mp4RecordingHandleFactory::getNext();
   postToSdkThread(resip::resip_bind(&Mp4RecordingInterface::createRecordingSessionImpl, this, h));
   return h;
}

void Mp4RecordingInterface::createRecordingSessionImpl(Mp4RecordingHandle videoStream)
{
   Mp4RecordingImpl* pimpl = new Mp4RecordingImpl(mPhone, this, videoStream);
   mInstMap[videoStream] = pimpl;
}

int Mp4RecordingInterface::setMp4RecordingSettings(Mp4RecordingHandle videoStream, const Mp4RecordingSettings& settings)
{
   postToSdkThread(resip::resip_bind(&Mp4RecordingInterface::setMp4RecordingSettingsImpl, this, videoStream, settings));
   return 0;
}

void Mp4RecordingInterface::setMp4RecordingSettingsImpl(Mp4RecordingHandle videoStream, const Mp4RecordingSettings& settings)
{
   Mp4RecordingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->setMp4RecordingSettings(settings);
   }
}

int Mp4RecordingInterface::setMp4RecordingHandler(Mp4RecordingHandle videoStream, Mp4RecordingHandler* handler)
{
   return setAppHandler(videoStream, handler);
}

int Mp4RecordingInterface::startRecording(Mp4RecordingHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&Mp4RecordingInterface::startRecordingImpl, this, videoStream));
   return 0;
}

void Mp4RecordingInterface::startRecordingImpl(Mp4RecordingHandle videoStream)
{
   Mp4RecordingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->startRecording();
   }
}

int Mp4RecordingInterface::stopRecording(Mp4RecordingHandle videoStream)
{
   postToSdkThread(resip::resip_bind(&Mp4RecordingInterface::stopRecordingImpl, this, videoStream));
   return 0;
}

void Mp4RecordingInterface::stopRecordingImpl(Mp4RecordingHandle videoStream)
{
   Mp4RecordingInterface::InstanceMap::iterator it = mInstMap.find(videoStream);
   if (it != mInstMap.end())
   {
      it->second->stopRecording();
   }
}



}
}

#endif
