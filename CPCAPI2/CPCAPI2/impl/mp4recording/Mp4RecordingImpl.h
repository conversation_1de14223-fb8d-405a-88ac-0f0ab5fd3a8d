#pragma once

#if !defined(CPCAPI2_MP4_RECORDING_IMPL_H)
#define CPCAPI2_MP4_RECORDING_IMPL_H

#include "cpcapi2defs.h"
#include "mp4recording/Mp4Recording.h"

#include <rutil/Data.hxx>

#include <map>
#include <memory>

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class AVRecordingFileWriter;
class MediaManagerInterface;
}
namespace Mp4Recording
{
class Mp4RecordingInterface;
class Mp4RecordingImpl
{
public:
   Mp4RecordingImpl(CPCAPI2::PhoneInterface* phone, Mp4RecordingInterface* ccif, Mp4RecordingHandle h);
   virtual ~Mp4RecordingImpl();

   void setMp4RecordingSettings(const Mp4RecordingSettings& settings);
   void startRecording();
   void stopRecording();
  
private:

private:
   CPCAPI2::PhoneInterface* mPhone;
   CPCAPI2::Media::MediaManagerInterface* mMediaManager;
   Mp4RecordingInterface* mInterface;
   Mp4RecordingHandle mHandle;
   std::unique_ptr<CPCAPI2::Media::AVRecordingFileWriter> mFileWriter;
   Mp4RecordingSettings mSettings;
};
}
}

#endif // CPCAPI2_MP4_RECORDING_IMPL_H

