#include "brand_branded.h"

#if (CPCAPI2_BRAND_MP4_RECORDING_MODULE == 1)
#include "Mp4RecordingImpl.h"
#include "Mp4RecordingInterface.h"
#include "Mp4RecordingFileWriter.h"
#include "media/MediaManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "auth_server/AuthServerJwtUtils.h"

#include <MediaStackImpl.hxx>

// WebRTC
#include <vie_codec.h>
#include <voe_base.h>
#include <vie_base.h>
#include <vie_rtp_rtcp.h>
#include <webrtc/video_engine/vie_encoder.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include <sstream>
#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>

#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace CPCAPI2::Media;

namespace CPCAPI2
{
namespace Mp4Recording
{
Mp4RecordingImpl::Mp4RecordingImpl(CPCAPI2::PhoneInterface* phone, Mp4RecordingInterface* ccif, Mp4RecordingHandle h)
   : mPhone(phone),
     mMediaManager(dynamic_cast<CPCAPI2::Media::MediaManagerInterface*>(CPCAPI2::Media::MediaManager::getInterface(mPhone))),
     mInterface(ccif),
     mHandle(h)
{
}

Mp4RecordingImpl::~Mp4RecordingImpl()
{
}

void Mp4RecordingImpl::setMp4RecordingSettings(const Mp4RecordingSettings& settings)
{
   mSettings = settings;
}

void Mp4RecordingImpl::startRecording()
{
   if (mFileWriter.get() == NULL)
   {
      if (mSettings.videoReceiveChannel >= 0)
      {
         mFileWriter.reset(new CPCAPI2::Media::AVRecordingFileWriter());
         mFileWriter->Init(mSettings.filenameUtf8.c_str());

         std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
         mediaStack->videoCodec()->RegisterPreDecodeObserver(mSettings.videoReceiveChannel, mFileWriter->EncodedFrameHandler());

         mediaStack->vie_rtp_rtcp()->RequestKeyFrame(mSettings.videoReceiveChannel);
      }
      else if (mSettings.videoSendChannel >= 0)
      {
         mFileWriter.reset(new CPCAPI2::Media::AVRecordingFileWriter());
         mFileWriter->Init(mSettings.filenameUtf8.c_str());

         std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
         webrtc::ViEEncoder* encoder = mediaStack->vie_base()->GetEncoder(mSettings.videoSendChannel);
         encoder->RegisterPostEncodeImageCallback(&mFileWriter->EncodedFrameHandler());
         encoder->SendKeyFrame();
      }

      if (mSettings.audioReceiveChannel >= 0)
      {
      }
      else if (mSettings.audioSendChannel >= 0)
      {
         if (mFileWriter)
         {
            std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
            mediaStack->voe_base()->SetEncodedAudioObserver(mSettings.audioSendChannel, &mFileWriter->EncodedAudioObserver());
         }
      }
   }
}

void Mp4RecordingImpl::stopRecording()
{
   std::shared_ptr<webrtc_recon::MediaStackImpl> mediaStack = mMediaManager->media_stack_ptr();
   mediaStack->videoCodec()->DeregisterPreDecodeObserver(mSettings.videoReceiveChannel, &mFileWriter->EncodedFrameHandler());
   mFileWriter.reset();
}

}
}

#endif // CPCAPI2_BRAND_CLOUD_CONNECTOR_MODULE
