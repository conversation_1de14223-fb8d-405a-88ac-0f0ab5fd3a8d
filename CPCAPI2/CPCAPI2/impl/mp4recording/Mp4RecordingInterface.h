#pragma once

#if !defined(CPCAPI2_MP4_RECORDING_INTERFACE_H)
#define CPCAPI2_MP4_RECORDING_INTERFACE_H

#include "cpcapi2defs.h"
#include "mp4recording/Mp4Recording.h"
#include "phone/Cpcapi2EventSource.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{
class PhoneInterface;

namespace Mp4Recording
{
class Mp4RecordingImpl;
class Mp4RecordingSyncHandler {};

class Mp4RecordingInterface : public Mp4RecordingManager
                              , public PhoneModule
                              , public CPCAPI2::EventSource<Mp4RecordingHandle, Mp4RecordingHandler, Mp4RecordingSyncHandler>
{
public:
   Mp4RecordingInterface(Phone* phone);
   virtual ~Mp4RecordingInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // Mp4RecordingManager
   virtual Mp4RecordingHandle createRecordingSession() OVERRIDE;
   virtual int setMp4RecordingSettings(Mp4RecordingHandle videoStream, const Mp4RecordingSettings& settings) OVERRIDE;
   virtual int setMp4RecordingHandler(Mp4RecordingHandle videoStream, Mp4RecordingHandler* handler) OVERRIDE;
   virtual int startRecording(Mp4RecordingHandle videoStream) OVERRIDE;
   virtual int stopRecording(Mp4RecordingHandle videoStream) OVERRIDE;

   void post(resip::ReadCallbackBase* f);

private:
   void createRecordingSessionImpl(Mp4RecordingHandle videoStream);
   void setMp4RecordingSettingsImpl(Mp4RecordingHandle videoStream, const Mp4RecordingSettings& settings);
   void startRecordingImpl(Mp4RecordingHandle videoStream);
   void stopRecordingImpl(Mp4RecordingHandle videoStream);

private:
   PhoneInterface* mPhone;
   typedef std::map<Mp4RecordingHandle, Mp4RecordingImpl*> InstanceMap;
   InstanceMap mInstMap;
};

class Mp4RecordingHandleFactory
{
public:
   static Mp4RecordingHandle getNext() { return sNextHandle++; }
private:
   static std::atomic<Mp4RecordingHandle> sNextHandle;
};
}
}

#endif // CPCAPI2_MP4_RECORDING_INTERFACE_H
