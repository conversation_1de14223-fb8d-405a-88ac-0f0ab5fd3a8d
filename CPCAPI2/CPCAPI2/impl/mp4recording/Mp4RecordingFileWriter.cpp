#include "brand_branded.h"

#if (CPCAPI2_BRAND_MP4_RECORDING_MODULE == 1)
#include "Mp4RecordingFileWriter.h"

#include <rutil/HighPerfReactor.hxx>

#include <webrtc/system_wrappers/interface/clock.h>
#include <webrtc/video_encoder.h>
#include <webrtc/video_frame.h>
#include <webrtc/modules/interface/module_common_types.h>
#include <voe_base.h>

#define TIMESCALE 1000
#define SampleLenFieldSize 4

namespace CPCAPI2
{
namespace Media
{
AVRecordingFileWriter::AVRecordingFileWriter() :
   mMp4File(NULL),
   mAudioTrack(MP4_INVALID_TRACK_ID),
   mVideoTrack(MP4_INVALID_TRACK_ID),
   mLastVideoFrameTimestamp(0)
{
}

AVRecordingFileWriter::~AVRecordingFileWriter()
{
   MP4Close(mMp4File);
}

class AVRecordingEncodedFrameHandler2 : public webrtc::EncodedImageCallback
{
public:
   AVRecordingEncodedFrameHandler2(AVRecordingFileWriter* frag)
      : mFragmenter(frag) {}

   virtual ~AVRecordingEncodedFrameHandler2() {}

   int32_t Encoded(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation)
   {
      mFragmenter->HandleEncodedImage(encoded_image, fragmentation);
      return 0;
   }

   virtual int32_t EncodedSpatialLayer(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation,
      int layerId) {
      if (layerId == 1) {
         mFragmenter->HandleEncodedImage(encoded_image, fragmentation);
      }
      return 0;
   }

   void SetWantsKeyFrame(bool /*wantsKeyFrame*/) {}
   bool WantsKeyFrame() const { return false; }

private:
   AVRecordingFileWriter* mFragmenter;
};

webrtc::EncodedImageCallback& AVRecordingFileWriter::EncodedFrameHandler() const
{
   return *mEncodedFrameHandler;
}

class AVRecordingEncodedAudioObserver : public webrtc::EncodedAudioObserver
{
public:
   AVRecordingEncodedAudioObserver(AVRecordingFileWriter* frag) : mFragmenter(frag) {}
   virtual ~AVRecordingEncodedAudioObserver() {}

   virtual void OnEncodedAudio(
      uint32_t timeStamp,
      const uint8_t* payloadData,
      size_t payloadSize)
   {
      mFragmenter->HandleEncodedAudio(timeStamp, payloadData, payloadSize);
   }

private:
   AVRecordingFileWriter* mFragmenter;
};

webrtc::EncodedAudioObserver& AVRecordingFileWriter::EncodedAudioObserver() const
{
   return *mEncodedAudioObserver;
}

// rough guide:
// https://freeswitch.org/stash/projects/FS/repos/freeswitch/browse/src/mod/applications/mod_mp4v2/mod_mp4v2.c
void AVRecordingFileWriter::Init(const std::string& filenameUtf8)
{ 
   mEncodedFrameHandler.reset(new AVRecordingEncodedFrameHandler2(this));

   mMp4File = MP4Create(filenameUtf8.c_str(), 0);
   if (mMp4File == MP4_INVALID_FILE_HANDLE)
   {
      return;
   }

   MP4SetTimeScale(mMp4File, 1000);

   //mAudioTrack = MP4AddULawAudioTrack(mMp4File, 8000);
   //MP4SetTrackIntegerProperty(mMp4File, mAudioTrack, "mdia.minf.stbl.stsd.ulaw.channels", 1);
   //MP4SetTrackIntegerProperty(mMp4File, mAudioTrack, "mdia.minf.stbl.stsd.ulaw.sampleSize", 8);
   //MP4SetAudioProfileLevel(mMp4File, 0x7F);
}

void init_video_track(MP4FileHandle mp4file, const webrtc::EncodedImage& encoded_image, const webrtc::RTPFragmentationHeader* fragmentation, MP4TrackId* video)
{
   /*
   MP4TrackId track = MP4AddH264VideoTrack(mp4Handle, 90000, 90000/25, WIDTH, HEIGHT, 66, 192, 42, 3);
   MP4SetVideoProfileLevel(mp4Handle, 0x7f);
   */

   for (size_t ifrag = 0; ifrag < fragmentation->fragmentationVectorSize; ifrag++)
   {
      uint8_t* nalData = encoded_image._buffer + fragmentation->fragmentationOffset[ifrag];
      size_t fragLen = fragmentation->fragmentationLength[ifrag];

      uint8_t nalType = nalData[0] & 0x1F;

      if (nalType == 7 /* SPS */)
      {
         uint8_t* spsParams = nalData + 1;

         if (*video == MP4_INVALID_TRACK_ID) {
            *video = MP4AddH264VideoTrack(mp4file, 1000, MP4_INVALID_DURATION, encoded_image._encodedWidth, encoded_image._encodedHeight, *(spsParams), *(spsParams + 1), *(spsParams + 2), SampleLenFieldSize - 1);
            MP4SetVideoProfileLevel(mp4file, 0x7F);
         }

         if (*video == MP4_INVALID_TRACK_ID) {
            return;
         }

         MP4AddH264SequenceParameterSet(mp4file, *video, nalData, fragLen);
      }
      else if (nalType == 8 /* PPS */)
      {
         MP4AddH264PictureParameterSet(mp4file, *video, nalData, fragLen);
      }
   }
}

void AVRecordingFileWriter::HandleEncodedImage(const webrtc::EncodedImage& encoded_image, const webrtc::RTPFragmentationHeader* fragmentation)
{
   webrtc::Clock* clock = webrtc::Clock::GetRealTimeClock();
   int64_t rtp_timestamp = clock->TimeInMilliseconds();
   if (rtp_timestamp <= 0) {
      return;
   }

   while (rtp_timestamp <= mLastVideoFrameTimestamp) {
      rtp_timestamp = rtp_timestamp + 5;
   }

   if (mIsoTimeOffsetVideo == 0) {
      mIsoTimeOffsetVideo = rtp_timestamp;
   }
   rtp_timestamp = rtp_timestamp - mIsoTimeOffsetVideo;

   if (encoded_image._frameType == webrtc::kKeyFrame)
   {
      init_video_track(mMp4File, encoded_image, fragmentation, &mVideoTrack);
   }

   if (mPrevFrame.get() != NULL && mPrevFrame->_length > 0)
   {
      MP4Duration duration = rtp_timestamp - mLastVideoFrameTimestamp;
      std::cout << "writing mp4 sample of length " << mPrevFrame->_length << std::endl;
      if (!MP4WriteSample(mMp4File, mVideoTrack, mPrevFrame->_buffer, mPrevFrame->_length, duration, 0, (mPrevFrame->_frameType == webrtc::kKeyFrame))) {
      }
   }

   mLastVideoFrameTimestamp = rtp_timestamp;

   if (mPrevFrame.get() == NULL || encoded_image._size > mPrevFrame->_size)
   {
      mPrevFrame.reset(new webrtc::EncodedImage());
      mPrevFrame->_size = encoded_image._size + (fragmentation->fragmentationVectorSize*4);
      mPrevFrame->_buffer = new uint8_t[mPrevFrame->_size];
   }

   uint8_t* firstNal = NULL;
   uint32_t numBytes = 0;
   for (size_t ifrag = 0; ifrag < fragmentation->fragmentationVectorSize; ifrag++)
   {
      uint8_t* nalData = encoded_image._buffer + fragmentation->fragmentationOffset[ifrag];
      uint8_t* nalDataCpy = mPrevFrame->_buffer + numBytes;
      size_t fragLen = fragmentation->fragmentationLength[ifrag];

      uint8_t nalType = nalData[0] & 0x1F;

      if (nalType == 5 /* I frame */ || nalType == 1 /* P frame */)
      {
         uint32_t fragSizeN = htonl(fragLen);
         uint8_t* frameData = (uint8_t*)nalDataCpy;
         memcpy(frameData, &fragSizeN, 4);
         if (firstNal == NULL)
         {
            firstNal = frameData;
         }
         memcpy(frameData + 4, nalData, fragLen);
         numBytes += fragLen + 4;
      }
      else if (nalType == 7 || nalType == 8)
      {
      }
      else
      {
         assert(0);
      }
   }

   if (firstNal != NULL)
   {
      mPrevFrame->_buffer = firstNal;
      mPrevFrame->_length = numBytes;

      mPrevFrame->adapt_reason_ = encoded_image.adapt_reason_;
      mPrevFrame->capture_time_ms_ = encoded_image.capture_time_ms_;
      mPrevFrame->ntp_time_ms_ = encoded_image.ntp_time_ms_;
      mPrevFrame->qp_ = encoded_image.qp_;
      mPrevFrame->rotation_ = encoded_image.rotation_;
      mPrevFrame->_completeFrame = encoded_image._completeFrame;
      mPrevFrame->_encodedHeight = encoded_image._encodedHeight;
      mPrevFrame->_encodedWidth = encoded_image._encodedWidth;
      mPrevFrame->_frameType = encoded_image._frameType;
      mPrevFrame->_timeStamp = encoded_image._timeStamp;
   }
}

void AVRecordingFileWriter::HandleEncodedAudio(uint32_t timeStamp,
   const uint8_t* payloadData,
   size_t payloadSize)
{
   //MP4WriteSample(mMp4File, mAudioTrack, payloadData, payloadSize);
}


}
}

#endif // (CPCAPI2_BRAND_MP4_SUPPORT == 1)
