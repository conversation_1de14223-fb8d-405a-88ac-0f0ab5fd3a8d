#include "brand_branded.h"

#include "interface/experimental/mp4recording/Mp4Recording.h"

#if (CPCAPI2_BRAND_MP4_RECORDING_MODULE == 1)
#include "Mp4RecordingInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace Mp4Recording
{
   Mp4RecordingManager* Mp4RecordingManager::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_MP4_RECORDING_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<Mp4RecordingInterface>(phone, "Mp4RecordingManager");
#else
      return NULL;
#endif
   }

}
}
