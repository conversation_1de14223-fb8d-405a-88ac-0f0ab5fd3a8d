#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)

#include "MediaTransportsReactorFactory.h"
#include "../util/cpc_logger.h"

#include <thread>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

namespace CPCAPI2
{
namespace Media
{
MediaTransportsReactorFactory* MediaTransportsReactorFactory::create()
{
   return new MediaTransportsReactorFactoryInterface();
}

MediaTransportsReactorFactoryInterface::MediaTransportsReactorFactoryInterface()
   : mNextReactor(0),
     mSharedInstanceRefCnt(0)
{
}

MediaTransportsReactorFactoryInterface::~MediaTransportsReactorFactoryInterface()
{
}

void MediaTransportsReactorFactoryInterface::initializeNumReactors(unsigned int numReactors)
{
   InfoLog(<< "initializing MediaTransportsReactorFactory with " << numReactors << " reactors");
   for (unsigned int i = 0; i < numReactors; i++)
   {
      resip::Data threadName = "MediaTransportsReactor";
      resip::HighPerfReactor* r = new resip::HighPerfReactor(threadName);
      r->start();
      r->addRef();
      resip::DnsStub::DnsSettings dnsSettings;
      dnsSettings.includeIpv6SystemDnsServers = true;
      dnsSettings.includeSystemDnsServers = true;
      resip::Resolver<resip::HighPerfReactor>* resolver = resip::Resolver<resip::HighPerfReactor>::createResolver(r, dnsSettings);
      resolver->addRef();
      mReactors[r] = resolver;
   }
}

void MediaTransportsReactorFactoryInterface::initialize()
{
   unsigned int concurentThreadsSupported = std::thread::hardware_concurrency();
   initializeNumReactors(concurentThreadsSupported);
}

void MediaTransportsReactorFactoryInterface::initializeSingle()
{
   initializeNumReactors(1);
}

void MediaTransportsReactorFactoryInterface::shutdown()
{
   auto it = mReactors.begin();
   for (; it != mReactors.end(); ++it)
   {
      it->second->releaseRef();
      it->first->releaseRef();
   }
   delete this;
}

void MediaTransportsReactorFactoryInterface::addRef()
{
   resip::Lock l(mSafeDeleteMutex);
   mSharedInstanceRefCnt++;
}

void MediaTransportsReactorFactoryInterface::releaseRef()
{
   bool shouldDestroy = false;
   {
      resip::Lock l(mSafeDeleteMutex);
      mSharedInstanceRefCnt--;
      shouldDestroy = (mSharedInstanceRefCnt == 0);
   }
   if (shouldDestroy)
   {
      shutdown();
   }
}

resip::Reactor<resip::HighPerfReactor::TReactorQueue>* MediaTransportsReactorFactoryInterface::allocateReactor()
{
   auto it = mReactors.begin();
   std::advance(it, mNextReactor);
   resip::HighPerfReactor* ret = it->first;
   mNextReactor = (mNextReactor + 1) % mReactors.size();
   return ret;
}

resip::Resolver<resip::HighPerfReactor>* MediaTransportsReactorFactoryInterface::getResolverForReactor(resip::Reactor<resip::HighPerfReactor::TReactorQueue>* reactor) const
{
   auto it = mReactors.find(reactor);
   if (it != mReactors.end())
   {
      return it->second;
   }
   assert(0); // passing in an unexpected reactor
   return NULL;
}

}
}

#endif
