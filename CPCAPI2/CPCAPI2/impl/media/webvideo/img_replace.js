function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
}

var havePrev = false;
var canvas;
var context;
var imageData;
var scratch;

window.onload = function() {
	canvas = document.getElementById("img1");
	context = canvas.getContext("2d");
	imageData = context.getImageData(0, 0, 1280, 720);
	scratch = new Uint8Array((1280 * 720 * 4) + 1500);
};

function handleImageData(event) { 
	var buffer = event.data;
	//console.log("compressed length: " + buffer.byteLength);
	if (havePrev === true) {
		//console.log("appying diff of length " + uncompressed8.length);
		var uncompressedLen = SnappyJS.uncompress(buffer, scratch) / 4;
		var buffer32 = new Uint32Array(scratch.buffer);
		var imageData32 = new Uint32Array(imageData.data.buffer);
		
		for (var i=0; i<uncompressedLen;) {
			var deltaPos = buffer32[i];
			i = i + 1;
			var deltaSize = buffer32[i];
			i = i + 1;
			//console.log("processing delta at pos " + deltaPos + " of length " + deltaSize + " bytes");
			for (var j=deltaPos; j<(deltaPos+deltaSize); j++) {
				imageData32[j] = buffer32[i++]
			}
		}
	
		context.putImageData(imageData, 0, 0);
	} else {
		SnappyJS.uncompress(buffer, imageData.data);
		context.putImageData(imageData, 0, 0);
		havePrev = true;
	}
}

var exampleSocket = new WebSocket(makeWsUrl(2114));
exampleSocket.binaryType = "arraybuffer";
exampleSocket.onopen = function (event) {
	console.log("connected");
};	
exampleSocket.onmessage = function (event) {
	//console.log("received a message");
	handleImageData(event);
}	


