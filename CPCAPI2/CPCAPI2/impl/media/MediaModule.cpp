#include "brand_branded.h"

#include "phone/PhoneInterface.h"

#include "interface/public/media/audio/AndroidAudio.h"
#include "interface/public/media/audio/IOSAudio.h"
#include "interface/public/media/audio/Audio.h"
#include "media/audio/CodecSettings.h"
#include "media/video/Video.h"

#include "interface/experimental/media/MediaManagerInternal.h"
#include "interface/experimental/audio_ext/AudioExt.h"
#include "interface/experimental/teradici/media/TeradiciAudio.h"
#include "interface/experimental/video_ext/VideoExt.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#include "MediaManagerInterface.h"
#include "VideoInterface.h"
#include "AudioInterface.h"
#if (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE == 1)
#include "impl/teradici/media/TeradiciAudioInterface.h"
#endif

#endif

class MediaTransportsReactorFactory;

namespace CPCAPI2
{
namespace Media
{
void createMediaManagerInternal(PhoneInterface* phone, MediaTransportsReactorFactory* mediaReactorFactory)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
   MediaManagerInternal* mmi = dynamic_cast<MediaManagerInternal*>(phone->getInterfaceByName("MediaManagerInterface"));
   if (mmi == NULL)
   {
      mmi = new MediaManagerInterface(phone, mediaReactorFactory);
      phone->registerInterface("MediaManagerInterface", dynamic_cast<PhoneModule*>(mmi));
   }
#endif
}

MediaManagerInternal* MediaManagerInternal::getInterface(Phone* cpcPhone, MediaTransportsReactorFactory* mediaReactorFactory)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   phone->getSdkModuleThread().execute(resip::resip_static_bind(&createMediaManagerInternal, phone, mediaReactorFactory));
   return _GetInterface<MediaManagerInterface>(phone, "MediaManagerInterface");
#else
   return NULL;
#endif
}

MediaManager* MediaManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<MediaManagerInterface>(phone, "MediaManagerInterface");
#else
   return NULL;
#endif
}

TeradiciAudio* TeradiciAudio::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE == 1)
   MediaManagerInterface* parent = dynamic_cast<MediaManagerInterface*>(cpcMediaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<TeradiciAudioInterface>(phone, "TeradiciAudioInterface", parent);
#else
   return NULL;
#endif
}


Audio* Audio::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE == 0)
   MediaManagerInterface* parent = dynamic_cast<MediaManagerInterface*>(cpcMediaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<AudioInterface>(phone, "AudioInterface", parent);
#else
   return NULL;
#endif
}

AudioExt* AudioExt::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE==1) && (CPCAPI2_BRAND_AUDIO_EXT_MODULE==1)
   MediaManagerInterface* parent = dynamic_cast<MediaManagerInterface*>(cpcMediaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<AudioInterface>(phone, "AudioInterface", parent);
#else
   return NULL;
#endif
}

Video* Video::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE==1) && (CPCAPI2_BRAND_VIDEO_MODULE==1)
   MediaManagerInterface* parent = dynamic_cast<MediaManagerInterface*>(cpcMediaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<VideoInterface>(phone, "VideoInterface", parent);
#else
   return NULL;
#endif
}

VideoExt* VideoExt::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE==1) && (CPCAPI2_BRAND_VIDEO_MODULE==1) && (CPCAPI2_BRAND_VIDEO_EXT_MODULE==1)
   MediaManagerInterface* parent = dynamic_cast<MediaManagerInterface*>(cpcMediaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterface<VideoInterface>(phone, "VideoInterface", parent);
#else
   return NULL;
#endif
}

AndroidAudio* AndroidAudio::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE==1)
   return dynamic_cast<AndroidAudio*>(Audio::getInterface(dynamic_cast<MediaManagerInterface*>(cpcMediaManager)));
#else
   return NULL;
#endif
}

IOSAudio* IOSAudio::getInterface(MediaManager* cpcMediaManager)
{
#if (CPCAPI2_BRAND_MEDIA_MODULE==1)
   return dynamic_cast<IOSAudio*>(Audio::getInterface(dynamic_cast<MediaManagerInterface*>(cpcMediaManager)));
#else
   return NULL;
#endif
}

}
}
