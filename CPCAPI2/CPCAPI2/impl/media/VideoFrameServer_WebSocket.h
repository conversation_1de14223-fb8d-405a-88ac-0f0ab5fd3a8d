#pragma once

#if !defined(CPCAPI2_VIDEO_FRAME_SERVER_WEBSOCKET_H)
#define CPCAPI2_VIDEO_FRAME_SERVER_WEBSOCKET_H

#include "cpcapi2defs.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>

#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
#include <websocketpp/config/asio.hpp>
#else
#include <websocketpp/config/asio_no_tls.hpp>
#endif

#include <vie_image_process.h>
#include <webrtc/system_wrappers/interface/rw_lock_wrapper.h>

#include <websocketpp/server.hpp>
#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>

namespace CPCAPI2
{
namespace Media
{
class RawVideoFrameServerWebSocket;

class VideoFrameServerWebSocket : public CPCAPI2::PhoneE<PERSON><PERSON><PERSON><PERSON><PERSON>,
                                  public webrtc::ViEEffectFilter
{
public:
   VideoFrameServerWebSocket(CPCAPI2::Phone* phone);
   virtual ~VideoFrameServerWebSocket();

   int processRemoteRequest(const cpc::string& request);
   void startServer(resip::ReadCallbackBase* startedCb);
   void stopServer();

   void bindServerToChannel(int channel);

   int channel() const {
      return mChannel;
   }

#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
   typedef websocketpp::server<websocketpp::config::asio_tls> server;
#else
   typedef websocketpp::server<websocketpp::config::asio> server;
#endif
   typedef server::connection_ptr conn_ptr;
   typedef server::message_ptr message_ptr;

   int send(VideoFrameServerWebSocket::message_ptr msgptr, websocketpp::connection_hdl hdl);

   // PhoneErrorHandler
   virtual int onError(const cpc::string& /*sourceModule*/, const PhoneErrorEvent& /*args*/) {
      return 0;
   }
   virtual int onLicensingError(const LicensingErrorEvent& /*args*/) {
      return 0;
   }

   message_ptr get_message_ptr(websocketpp::connection_hdl hdl, size_t msgsize);

   void handleEncodedVideoFrame(const webrtc::EncodedImage& encoded_image);

   webrtc::EncodedImageCallback* encoded_image_callback() const {
      return mEncodedFrameHandler.get();
   }

private:
   void handleConnectionClosed(websocketpp::connection_hdl conn, CPCAPI2::Phone* phone);
   void handleConnectionOpen(websocketpp::connection_hdl conn, CPCAPI2::Phone* phone);
   void handleIncomingMessage(websocketpp::connection_hdl conn, message_ptr msg, CPCAPI2::Phone* phone);

   typedef std::map<websocketpp::connection_hdl, CPCAPI2::Phone*, std::owner_less<websocketpp::connection_hdl> > MapConnToPhone;

   void on_open(server* s, websocketpp::connection_hdl hdl);
   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);
   void on_socket_init(websocketpp::connection_hdl, boost::asio::ip::tcp::socket& s);

private:
   std::unique_ptr<RawVideoFrameServerWebSocket> mWebSockServer;
   std::mutex mMapMutex;
   std::thread* mServerThread;
   CPCAPI2::Phone* mPhone;
   websocketpp::connection_hdl mClientConn;
   unsigned char* mBufferCurr;
   unsigned char* mBufferPrev;
   unsigned char* mDeltaBuff;
   std::atomic_int mSendCompleteFrame;
   int mChannel;
   webrtc::VideoFrame mVideoFrameResampled;
   int mLastWidth;
   int mLastHeight;
   std::unique_ptr<webrtc::RWLockWrapper> mWebSockServerRwLock;
   std::unique_ptr<webrtc::EncodedImageCallback> mEncodedFrameHandler;
};

}
}

#endif // CPCAPI2_VIDEO_FRAME_SERVER_WEBSOCKET_H

