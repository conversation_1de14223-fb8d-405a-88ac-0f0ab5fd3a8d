#pragma once

#if !defined(CPCAPI2_RAW_VIDEO_FRAME_SERVER_H)
#define CPCAPI2_RAW_VIDEO_FRAME_SERVER_H

#include "cpcapi2defs.h"
#include "videostreaming/VideoStreaming.h"

#include <functional>

namespace resip
{
   struct ReadCallbackBase;
}

namespace CPCAPI2
{
   class PhoneInterface;
   namespace Media
   {
      class RawVideoFrameServerObserver
      {
      public:
         virtual ~RawVideoFrameServerObserver() {}
         virtual void onOpen(int videoStream) = 0;
      };

      class RawVideoFrameServer
      {
      public:
         virtual ~RawVideoFrameServer() {}

         virtual void StartServer(CPCAPI2::Phone* phone, const CPCAPI2::VideoStreaming::VideoStreamingServerConfig& serverConfig, resip::ReadCallbackBase* startedCb, std::function<void()> onPrivilegedAccessCompleted) = 0;
         virtual void StopServer() = 0;
         virtual void AddObserver(RawVideoFrameServerObserver* obs) = 0;
         virtual void RemoveObserver(RawVideoFrameServerObserver* obs) = 0;
         virtual void SendBitstreamData(int videoStreamHandle, bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen) {}
         virtual void SendRawI420BistreamData(int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride) {}
         virtual size_t GetSessionCount(int videoStreamHandle) = 0;
         virtual void CloseSessions(int videoStreamHandle) = 0;
      };

   }

}

#endif // CPCAPI2_RAW_VIDEO_FRAME_SERVER_H

