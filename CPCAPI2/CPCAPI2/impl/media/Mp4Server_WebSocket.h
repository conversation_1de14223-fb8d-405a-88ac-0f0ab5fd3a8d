#pragma once

#if !defined(CPCAPI2_MP4_SERVER_WEBSOCKET_H)
#define CPCAPI2_MP4_SERVER_WEBSOCKET_H

#include "cpcapi2defs.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>

#if (USE_WSS_MP4_SERVER == 1)
#include <websocketpp/config/asio.hpp>
#else
#include <websocketpp/config/asio_no_tls.hpp>
#endif
#include <websocketpp/server.hpp>

// rapidjson
#include <document.h>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class Mp4ServerWebSocketObserver
{
public:
   virtual ~Mp4ServerWebSocketObserver() {}
   virtual void onWebsocketOpen(int videoStream) = 0;
};

class Mp4ServerWebSocket;
class Mp4Fragmenter;

#if !defined(DISABLE_WSS_JSON_SERVER)
typedef websocketpp::server<websocketpp::config::asio_tls> server;
#else
typedef websocketpp::server<websocketpp::config::asio> server;
#endif
typedef server::connection_ptr conn_ptr;
typedef server::message_ptr message_ptr;

class Mp4ServerSession
{
public:
   Mp4ServerSession(CPCAPI2::PhoneInterface* masterSdkPhone, Mp4ServerWebSocket* transport, const websocketpp::connection_hdl& conn);
   virtual ~Mp4ServerSession();

   const websocketpp::connection_hdl& websocket_conn_hdl() const {
      return mConn;
   }

   //void onBitstreamData(const CPCAPI2::Media::BitstreamDataEvent& args, Mp4Fragmenter* fragmenter);

   void close();

   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);

private:
   void handleMp4ServerMessage(server* s, websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc);

   websocketpp::connection_hdl mConn;
   CPCAPI2::PhoneInterface* mMasterPhone;
   Mp4ServerWebSocket* mTransport;

   struct PerConnectionStreamState
   {
      PerConnectionStreamState(int vs, int bs)
         : videoStream(vs), bitstreamId(bs)
      {
         mWaitingForSap = true;
      }
      int videoStream;
      int bitstreamId;
      bool mWaitingForSap;
   };
   std::map<int, std::vector<PerConnectionStreamState*> > mStreamStateMap;
};

class Mp4ServerWebSocket
{
public:
   Mp4ServerWebSocket();
   virtual ~Mp4ServerWebSocket();

   void StartServer(CPCAPI2::Phone* phone);
   void StopServer();
   void AddObserver(Mp4ServerWebSocketObserver* obs);
   int CloseConnection(const websocketpp::connection_hdl& hdl);
   void Send(websocketpp::connection_hdl hdl, int videoStreamHandle, int bitstreamId, int bitstreamType, const unsigned char* bitstream, unsigned int bitstreamLen);
   void RequestKeyFrame(int videoStreamHandle);
   void RemoveSession(websocketpp::connection_hdl hdl);

   //void onBitstreamStarted(int bitstreamId, const CPCAPI2::Media::BitstreamStartedEvent& args);
   //void onBitstreamEnded(int bitstreamId, const CPCAPI2::Media::BitstreamEndedEvent& args);
   //void onBitstreamData(const CPCAPI2::Media::BitstreamDataEvent& args, Mp4Fragmenter* fragmenter);

   struct BitstreamState
   {
      int bitstreamId = -1;
      int bitstreamType = -1;
      std::vector<unsigned char> mFtypMoov;
   };
   std::vector<BitstreamState*> GetBitstreamState(int videoStream);

private:
   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::message_ptr message_ptr;
   typedef server::connection_ptr connection_ptr;

   typedef std::map<websocketpp::connection_hdl, Mp4ServerSession*, std::owner_less<websocketpp::connection_hdl> > sess_list;

   void on_open(server* s, websocketpp::connection_hdl hdl);

private:
   CPCAPI2::Phone* mPhone;
   server mWebSockServer;
   sess_list mSessions;
   std::set<Mp4ServerWebSocketObserver*> mObs;
   std::thread* mServerThread;

   std::map<int, std::vector<BitstreamState*> > mBitstreamMap;

   std::mutex mMapLock;
};

}

}

#endif // CPCAPI2_MP4_SERVER_WEBSOCKET_H

