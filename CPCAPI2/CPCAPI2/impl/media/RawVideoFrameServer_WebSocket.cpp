#include "brand_branded.h"

#if (CPCAPI2_BRAND_RAW_VIDEO_WEBSOCKET_SERVER == 1)
#include "RawVideoFrameServer_WebSocket.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#include "util/BoostTlsHelper.h"

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
RawVideoFrameServerWebSocket::RawVideoFrameServerWebSocket()
   : mPhone(NULL), mServerThread(NULL)
{
}

RawVideoFrameServerWebSocket::~RawVideoFrameServerWebSocket()
{
}

void RawVideoFrameServerWebSocket::StartServer(CPCAPI2::Phone* phone, const CPCAPI2::VideoStreaming::VideoStreamingServerConfig& serverConfig, resip::ReadCallbackBase* startedCb, std::function<void()> onPrivilegedAccessCompleted)
{
   if (mServerThread != NULL)
   {
      return; // already started
   }

   mPhone = phone;
   mConfig = serverConfig;
   mOnPrivilegedAccessCompleted = onPrivilegedAccessCompleted;
   int listenPort = serverConfig.listenPort;

   mServerThread = new std::thread([&, startedCb, listenPort]() {
      try {
         mWebSockServer.reset(new server());

         // Set logging settings
         mWebSockServer->set_access_channels(websocketpp::log::alevel::all);
         mWebSockServer->clear_access_channels(websocketpp::log::alevel::all);

         // Initialize Asio
         mWebSockServer->init_asio();

         // Register our message handler
         mWebSockServer->set_open_handler(std::bind(&RawVideoFrameServerWebSocket::on_open, this, mWebSockServer.get(), std::placeholders::_1));
#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
         mWebSockServer->set_tls_init_handler(std::bind(&RawVideoFrameServerWebSocket::on_tls_init, this, mWebSockServer.get(), std::placeholders::_1));
#endif

         mWebSockServer->set_open_handshake_timeout(10000);
         mWebSockServer->set_listen_backlog(8192 * 8);
         mWebSockServer->set_reuse_addr(true);
         mWebSockServer->set_pong_timeout(30000);

         std::error_code listenEc;
         int listenTryCnt = 0;
         do
         {
            listenEc = std::error_code();
            mWebSockServer->listen(boost::asio::ip::tcp::v4(), listenPort, listenEc);
            if (listenEc)
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }
         } while (listenEc && listenTryCnt++ < 10);

         if (listenEc)
         {
            ErrLog(<< "Failed to start listening on port " << listenPort << "; error = " << listenEc.message());
         }

         // Start the server accept loop
         mWebSockServer->start_accept();

         if (startedCb != NULL)
         {
            PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
            pi->getSdkModuleThread().post(startedCb);
         }

         // Start the ASIO io_service run loop
         //mWebSockServer->run();

         std::vector<std::thread> tg;
         const int threadCount = std::thread::hardware_concurrency();
         for (int i = 0; i < threadCount; i++) {
#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
            tg.emplace_back(&websocketpp::server<websocketpp::config::asio_tls>::run, mWebSockServer.get());
#else
            tg.emplace_back(&websocketpp::server<websocketpp::config::asio>::run, mWebSockServer.get());
#endif
         }

         for (auto& threadItem : tg)
         {
            if (threadItem.joinable())
            {
               threadItem.join();
            }
         }

         std::cout << "RawVideoFrameServerWebSocket thread exit..." << std::endl;
      }
      catch (websocketpp::exception const & e) {
         std::cout << e.what() << std::endl;
      }
      catch (...) {
         std::cout << "other exception" << std::endl;
      }
   });
}

void RawVideoFrameServerWebSocket::StopServer()
{
   if (mServerThread != NULL)
   {
      websocketpp::lib::error_code ec;
      mWebSockServer->stop_listening(ec);
      mWebSockServer->stop_perpetual();
      mServerThread->join();
      delete mServerThread;
      mServerThread = NULL;
   }
}

int RawVideoFrameServerWebSocket::CloseConnection(const websocketpp::connection_hdl& hdl)
{
   std::error_code ec;
   mWebSockServer->close(hdl, websocketpp::close::status::going_away, "shutdown", ec);
   return 0;
}

void RawVideoFrameServerWebSocket::AddObserver(RawVideoFrameServerObserver* obs)
{
   mObs.insert(obs);
}

void RawVideoFrameServerWebSocket::RemoveObserver(RawVideoFrameServerObserver* obs)
{
   mObs.erase(obs);
}

int RawVideoFrameServerWebSocket::Send(websocketpp::connection_hdl hdl, const unsigned char* bitstream, unsigned int bitstreamLen, bool useOverflowProtection, bool& didOverflow)
{
   didOverflow = false;
   if (conn_ptr cn_ptr = mWebSockServer->get_con_from_hdl(hdl))
   {
      if (useOverflowProtection && cn_ptr->buffered_amount() > (16 * 1024))
      {
         didOverflow = true;
         return -1;
      }
      message_ptr msg_ptr = cn_ptr->get_message(websocketpp::frame::opcode::binary, bitstreamLen);
      msg_ptr->append_payload(bitstream, bitstreamLen);
      cn_ptr->send(msg_ptr);
      return 0;
   }
   return -1;
}

void RawVideoFrameServerWebSocket::on_open(server* s, websocketpp::connection_hdl hdl)
{
   DebugLog(<< "JsonApiServerWebSocket::on_open()");
   std::error_code ec;
   if (conn_ptr conn = mWebSockServer->get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         RawVideoFrameServerWsSession* session = new RawVideoFrameServerWsSession(dynamic_cast<PhoneInterface*>(mPhone), this, hdl);
         conn->set_close_handler(std::bind(&RawVideoFrameServerWsSession::on_close, session, mWebSockServer.get(), std::placeholders::_1));
         conn->set_message_handler(std::bind(&RawVideoFrameServerWsSession::on_message, session, mWebSockServer.get(), std::placeholders::_1, std::placeholders::_2));
         std::lock_guard<std::mutex> lck(mMapLock);
         mSessions[hdl] = session;
      }
   }
}

std::string RawVideoFrameServerWebSocket::get_password() const
{
   // TODO: should this be hard coded?
   return "zCQ/t):*d_N\\G*r=m3-p";
}

#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
inline bool file_exists(const std::string& name) {
   std::ifstream f(name.c_str());
   return f.good();
}

websocketpp::lib::shared_ptr<boost::asio::ssl::context> RawVideoFrameServerWebSocket::on_tls_init(server* s, websocketpp::connection_hdl hdl)
{
   SslCipherOptions tlsOptions = dynamic_cast<PhoneInterface*>(mPhone)->getSslCipherOptions();
   websocketpp::lib::shared_ptr<boost::asio::ssl::context> context = initializeBoostTlsContext(tlsOptions.getTLSVersion(SslCipherUsageWebSockets), tlsOptions.getCiphers(SslCipherUsageWebSockets).c_str(), resip::SecurityTypes::TLSMode_TLS_Server, mConfig.wssDiffieHellmanParamsFilePath.c_str());
   try
   {
      // https://stackoverflow.com/questions/6452756/exception-running-boost-asio-ssl-example
      context->set_password_callback(std::bind(&RawVideoFrameServerWebSocket::get_password, this));

      if (mConfig.wssCertificateFilePath.size() < 256 && file_exists(mConfig.wssCertificateFilePath.c_str()))
      {
         context->use_certificate_chain_file(mConfig.wssCertificateFilePath.c_str());
      }
      else if (file_exists("server.crt"))
      {
         context->use_certificate_chain_file("server.crt");
      }
      else
      {
         context->use_certificate(boost::asio::const_buffer(mConfig.wssCertificateFilePath.c_str(), mConfig.wssCertificateFilePath.size()), boost::asio::ssl::context::pem);
      }

      if (mConfig.wssPrivateKeyFilePath.size() < 256 && file_exists(mConfig.wssPrivateKeyFilePath.c_str()))
      {
         context->use_private_key_file(mConfig.wssPrivateKeyFilePath.c_str(), boost::asio::ssl::context::pem);
      }
      else if (file_exists("server.key"))
      {
         context->use_private_key_file("server.key", boost::asio::ssl::context::pem);
      }
      else
      {
         context->use_private_key(boost::asio::const_buffer(mConfig.wssPrivateKeyFilePath.c_str(), mConfig.wssPrivateKeyFilePath.size()), boost::asio::ssl::context::pem);
      }

      if (mConfig.wssDiffieHellmanParamsFilePath.size() < 256 && file_exists(mConfig.wssDiffieHellmanParamsFilePath.c_str()))
      {
         context->use_tmp_dh_file(mConfig.wssDiffieHellmanParamsFilePath.c_str());
      }
      else if (file_exists("dh2048.pem"))
      {
         context->use_tmp_dh_file("dh2048.pem");
      }
      else
      {
         context->use_tmp_dh(boost::asio::const_buffer(mConfig.wssDiffieHellmanParamsFilePath.c_str(), mConfig.wssDiffieHellmanParamsFilePath.size()));
      }
   }
   catch (std::exception& e)
   {
      ErrLog(<< "Encountered an exception while trying to setup TLS/certs for the websocket:" << e.what());
   }

   if (mOnPrivilegedAccessCompleted) mOnPrivilegedAccessCompleted();

   return context;
}
#endif // VIDEO_FRAME_SERVER_USE_WSS_JSON_SERVER

void RawVideoFrameServerWebSocket::RequestKeyFrame(int videoStreamHandle)
{
   std::set<RawVideoFrameServerObserver*>::iterator itObs = mObs.begin();
   for (; itObs != mObs.end(); ++itObs)
   {
      (*itObs)->onOpen(videoStreamHandle);
   }
}

void RawVideoFrameServerWebSocket::RemoveSession(websocketpp::connection_hdl hdl)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   mSessions.erase(hdl);
}

void RawVideoFrameServerWebSocket::SendBitstreamData(int videoStreamHandle, bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen)
{
   try {
      std::lock_guard<std::mutex> lck(mMapLock);

      sess_list::iterator it = mSessions.begin();
      for (; it != mSessions.end(); ++it)
      {
         it->second->SendBitstreamData(videoStreamHandle, isKeyFrame, bitstream, bitstreamLen);
      }
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "RawVideoFrameServerWebSocket::SendBitstreamData(..) failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
}

bool RawVideoFrameServerWebSocket::NeedsKeyframe(int videoStreamHandle)
{
   bool retVal = false;
   try {
      std::lock_guard<std::mutex> lck(mMapLock);

      sess_list::iterator it = mSessions.begin();
      for (; it != mSessions.end() && !retVal; ++it)
      {
         retVal |= it->second->NeedsKeyframe(videoStreamHandle);
      }
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "RawVideoFrameServerWebSocket::SendBitstreamData(..) failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
   return retVal;
}

size_t RawVideoFrameServerWebSocket::GetSessionCount(int videoStreamHandle)
{
   size_t retVal = 0;
   try {
      std::lock_guard<std::mutex> lck(mMapLock);

      sess_list::iterator it = mSessions.begin();
      for (; it != mSessions.end() && !retVal; ++it)
      {
         retVal += it->second->GetStreamCount(videoStreamHandle);
      }
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "RawVideoFrameServerWebSocket::SendBitstreamData(..) failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
   return retVal;
}

void RawVideoFrameServerWebSocket::CloseSessions(int videoStreamHandle)
{
   try {
      std::lock_guard<std::mutex> lck(mMapLock);

      sess_list::iterator it = mSessions.begin();
      for (; it != mSessions.end(); ++it)
      {
         it->second->CloseStreams(videoStreamHandle);
      }
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "RawVideoFrameServerWebSocket::SendBitstreamData(..) failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
}
   
RawVideoFrameServerWsSession::RawVideoFrameServerWsSession(CPCAPI2::PhoneInterface* masterSdkPhone, RawVideoFrameServerWebSocket* transport, const websocketpp::connection_hdl& conn) :
   mConn(conn),
   mMasterPhone(masterSdkPhone),
   mTransport(transport)
{
}

RawVideoFrameServerWsSession::~RawVideoFrameServerWsSession()
{
}

size_t RawVideoFrameServerWsSession::GetStreamCount(int videoStreamHandle)
{
   size_t retVal = 0;
   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      retVal = 1;
   }
   return retVal;
}

void RawVideoFrameServerWsSession::CloseStreams(int videoStreamHandle)
{
   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      close();
   }
}

void RawVideoFrameServerWsSession::SendBitstreamData(int videoStreamHandle, bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen)
{
   const int MAX_NUM_FRAMES_LAG = 20;

   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      const std::shared_ptr<PerConnectionStreamState>& pcss = itStrMap->second;
      if (pcss->mSendCnt < pcss->mRecvCnt)
      {
         pcss->mSendCnt = pcss->mRecvCnt;
      }

      if ((pcss->mSendCnt > 100) && ((pcss->mSendCnt - pcss->mRecvCnt) > MAX_NUM_FRAMES_LAG))
      {
         // the receiver is more than 20 frames of video behind; don't send them anything else until a key frame
         // note: the web clients currently only send recvCnt updates every 10 frames!
         pcss->mWaitingForKeyFrame = true;
      }
      else if (isKeyFrame)
      {
         pcss->mWaitingForKeyFrame = false;
      }
      else if (pcss->mWaitingForKeyFrame && ((pcss->mSendCnt - pcss->mRecvCnt) <= MAX_NUM_FRAMES_LAG))
      {
         mTransport->RequestKeyFrame(pcss->videoStream);
      }

      if (!pcss->mWaitingForKeyFrame)
      {
         bool didOverflow = false;
         if (mTransport->Send(mConn, bitstream, bitstreamLen, !isKeyFrame, didOverflow) == -1)
         {
            if (didOverflow)
            {
               pcss->mWaitingForKeyFrame = true;
            }
         }
         else
         {
            pcss->mSendCnt++;
         }
      }
   }
}

bool RawVideoFrameServerWsSession::NeedsKeyframe(int videoStreamHandle)
{
   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      if (itStrMap->second->mWaitingForKeyFrame)
      {
         return true;
      }
   }
   return false;
}

void RawVideoFrameServerWsSession::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
   // format is:
   // {"action":"stream_bind","payload":{"videoStreamId":1}}

   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(msg->get_payload().c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      return;
   }

   if (!jsonRequest->HasMember("action"))
   {
      WarningLog(<< "Missing action. Aborting decode.");
      return;
   }

   if (!jsonRequest->HasMember("payload"))
   {
      WarningLog(<< "Missing payload. Aborting decode.");
      return;
   }

   const rapidjson::Value& payloadVal = (*jsonRequest)["payload"];
   if (!payloadVal.IsObject())
   {
      WarningLog(<< "payload is not an object.  Aborting decode.");
      return;
   }

   if (payloadVal.HasMember("recvCnt") && payloadVal.HasMember("videoStreamId"))
   {
      const rapidjson::Value& recvCntVal = payloadVal["recvCnt"];
      if (!recvCntVal.IsInt())
      {
         WarningLog(<< "recvCnt is not an Int. Abording decode.");
         return;
      }

      int recvCnt = recvCntVal.GetInt();

      const rapidjson::Value& vidStreamIdVal = payloadVal["videoStreamId"];
      if (!vidStreamIdVal.IsInt())
      {
         WarningLog(<< "videoStreamId is not an Int. Aborting decode.");
         return;
      }

      int videoStream = vidStreamIdVal.GetInt();
      auto itStrm = mStreamStateMap.find(videoStream);
      if (itStrm != mStreamStateMap.end())
      {
         itStrm->second->mRecvCnt = recvCnt;
      }
   }
   else if (payloadVal.HasMember("videoStreamId"))
   {
      const rapidjson::Value& vidStreamIdVal = payloadVal["videoStreamId"];
      if (!vidStreamIdVal.IsInt())
      {
         WarningLog(<< "videoStreamId is not an Int. Aborting decode.");
         return;
      }

      int videoStream = vidStreamIdVal.GetInt();

      std::map<int, std::shared_ptr<PerConnectionStreamState> >::iterator itpcss = mStreamStateMap.begin();
      while (itpcss != mStreamStateMap.end())
      {
         if (itpcss->first != videoStream)
         {
            itpcss = mStreamStateMap.erase(itpcss);
         }
         else
         {
            itpcss++;
         }
      }
      if (mStreamStateMap.count(videoStream) == 0)
      {
         mStreamStateMap[videoStream].reset(new PerConnectionStreamState(videoStream));
      }
      else
      {
         mStreamStateMap[videoStream]->mRecvCnt = 0;
         mStreamStateMap[videoStream]->mSendCnt = 0;
         //mStreamStateMap[videoStream]->mWaitingForKeyFrame = true;
      }

      // request key frames
      mTransport->RequestKeyFrame(videoStream);
   }
}

void RawVideoFrameServerWsSession::on_close(server* s, websocketpp::connection_hdl hdl)
{
   DebugLog(<< "RawVideoFrameServerWsSession::on_close()");
   mTransport->RemoveSession(hdl);
   delete this;
}

void RawVideoFrameServerWsSession::close()
{
   mTransport->CloseConnection(mConn);
}

}

}

#endif
