#include <webrtc/modules/desktop_capture/desktop_frame.h>
#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>
#include <libyuv.h>
#include "ScreenShareImpl.h"

using namespace webrtc;

namespace CPCAPI2
{
namespace Media
{
ScreenShare* ScreenShare::Create()
{
   return new ScreenShareImpl();
}

ScreenShareImpl::ScreenShareImpl()
   : mCaptureEvt(webrtc::EventTimerWrapper::Create()),
     mExternalCapture(NULL),
     mCapturing(false),
     mObjHolder(NULL),
     mScreenshareDevice(0),
     mScaleBuffer(NULL),
     mScaleBufferSize(0)
{
}

ScreenShareImpl::~ScreenShareImpl()
{
   if (mObjHolder)
   {
      delete mObjHolder;
   }
   if (mScaleBuffer != NULL)
   {
      delete[] mScaleBuffer;
   }
}

int ScreenShareImpl::Start(unsigned int deviceId)
{
   mScreenshareDevice = deviceId;
   StartImpl();
   return 0;
}

void ScreenShareImpl::StartImpl()
{
   Stop();
   mCapturing = true;
   mCaptureThread.reset(new CPCAPI2::thread(std::bind(&ScreenShareImpl::CaptureThread, this)));
   mBorderThread.reset(new CPCAPI2::thread(std::bind(&ScreenShareImpl::BorderThread, this)));
}

void ScreenShareImpl::SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture)
{
   mExternalCapture = externalCapture;
}

void ScreenShareImpl::SetObserver(ScreenShareObserver* observer)
{
}

void ScreenShareImpl::SetMaxCaptureFrameRate(unsigned int fps)
{
}

int ScreenShareImpl::Stop()
{
   mCapturing = false;
   StopImpl();
   return 0;
}

void ScreenShareImpl::StopImpl()
{
   if (mCaptureThread)
   {
      mCaptureThread->join();
   }
   if (mBorderThread)
   {
      mBorderThread->join();
   }
}

void ScreenShareImpl::ResetScreenCap()
{
}

void ScreenShareImpl::OnCaptureResult(webrtc::DesktopCapturer::Result result,
   std::unique_ptr<webrtc::DesktopFrame> frame)
{
   int targetFrameWidth = 3840;
   int targetFrameHeight = 2160;
   int stride_y = 0;
   int stride_uv = 0;
   webrtc::Calc16ByteAlignedStride(targetFrameWidth, &stride_y, &stride_uv);

   mCapturedFrameI420.CreateEmptyFrame(targetFrameWidth, targetFrameHeight,
      stride_y, stride_uv, stride_uv);

   uint8_t* ybuff = mCapturedFrameI420.buffer(webrtc::kYPlane);
   uint8_t* ubuff = mCapturedFrameI420.buffer(webrtc::kUPlane);
   uint8_t* vbuff = mCapturedFrameI420.buffer(webrtc::kVPlane);

   auto start = std::chrono::time_point<std::chrono::high_resolution_clock>::min();
   auto end = std::chrono::high_resolution_clock::now();
   auto dur = end - start;

   memset(ybuff, (uint8_t)(dur.count() % 128), stride_y*targetFrameHeight);
   memset(ubuff, (uint8_t)(dur.count() + 0x40 % 128), stride_uv* targetFrameHeight/2);
   memset(vbuff, (uint8_t)(dur.count() + 0x40 % 128), stride_uv* targetFrameHeight/2);

   if (mExternalCapture)
   {
      mExternalCapture->IncomingFrame(mCapturedFrameI420);
   }
}

void ScreenShareImpl::InitializeCapture()
{
}

void ScreenShareImpl::CaptureThread()
{
   mCaptureEvt->StartTimer(true, 34);

   while (mCapturing)
   {
      if (mCapturedFrameI420.IsZeroSize() || mCapturedFrameI420.video_frame_buffer_const()->HasOneRef())
      {
         PerformCapture();
      }
      mCaptureEvt->Wait(2000);
   }

   int loopc = 0;
   while (!mCapturedFrameI420.video_frame_buffer_const()->HasOneRef() && loopc < 5)
   {
      mCaptureEvt->Wait(100);
      loopc++;
   }

   mCaptureEvt->StopTimer();
   ResetScreenCap();
}

void ScreenShareImpl::BorderThread()
{
}

void ScreenShareImpl::PerformCapture()
{
   OnCaptureResult(webrtc::DesktopCapturer::Result::SUCCESS, std::unique_ptr<webrtc::DesktopFrame>());
}

int ScreenShareImpl::GetMonitors(std::vector<ScreenShareMonitorListItem>* monitorList)
{
   ScreenShareMonitorListItem li;
   li.id = 1000;
   li.title = "Dummy Screen";
   monitorList->push_back(li);
   return 0;
}

int ScreenShareImpl::GetWindows(std::vector<ScreenShareWindowListItem>* windowList)
{
   return 0;
}

}
}
