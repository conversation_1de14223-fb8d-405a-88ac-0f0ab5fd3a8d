#include "brand_branded.h"

#if (CPCAPI2_BRAND_RAW_VIDEO_CALLBACK_SERVER == 1)
#include "RawVideoFrameServer_Callback.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
RawVideoFrameServerCallback::RawVideoFrameServerCallback()
   : mPhone(NULL)
{
}

RawVideoFrameServerCallback::~RawVideoFrameServerCallback()
{
}

void RawVideoFrameServerCallback::StartServer(CPCAPI2::Phone* phone, const CPCAPI2::VideoStreaming::VideoStreamingServerConfig& serverConfig, resip::ReadCallbackBase* startedCb, std::function<void()> onPrivilegedAccessCompleted)
{
   mPhone = phone;
   mConfig = serverConfig;
}

void RawVideoFrameServerCallback::StopServer()
{
}

int RawVideoFrameServerCallback::CloseConnection(int hdl)
{
   return 0;
}

void RawVideoFrameServerCallback::AddObserver(RawVideoFrameServerObserver* obs)
{
   mObs.insert(obs);
}

void RawVideoFrameServerCallback::RemoveObserver(RawVideoFrameServerObserver* obs)
{
   mObs.erase(obs);
}

std::string RawVideoFrameServerCallback::get_password() const
{
   // TODO: should this be hard coded?
   return "zCQ/t):*d_N\\G*r=m3-p";
}

void RawVideoFrameServerCallback::RequestKeyFrame(int videoStreamHandle)
{
   std::set<RawVideoFrameServerObserver*>::iterator itObs = mObs.begin();
   for (; itObs != mObs.end(); ++itObs)
   {
      (*itObs)->onOpen(videoStreamHandle);
   }
}

void RawVideoFrameServerCallback::RemoveSession(int hdl)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   //mSessions.erase(hdl);
}

void RawVideoFrameServerCallback::SendRawI420BistreamData(int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride)
{
   std::lock_guard<std::mutex> lck(mMapLock);

   sess_list::iterator it = mSessions.begin();
   for (; it != mSessions.end(); ++it)
   {
      it->second->SendBitstreamData(videoStreamHandle, width, height, ybuffer, ystride, ubuffer, ustride, vbuffer, vstride);
   }
}

bool RawVideoFrameServerCallback::NeedsKeyframe(int videoStreamHandle)
{
   bool retVal = false;
   std::lock_guard<std::mutex> lck(mMapLock);

   sess_list::iterator it = mSessions.begin();
   for (; it != mSessions.end() && !retVal; ++it)
   {
      retVal |= it->second->NeedsKeyframe(videoStreamHandle);
   }
   return retVal;
}

size_t RawVideoFrameServerCallback::GetSessionCount(int /*videoStreamHandle*/)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   return mSessions.size();
}

void RawVideoFrameServerCallback::CloseSessions(int /*videoStreamHandle*/)
{
}

void RawVideoFrameServerCallback::AddCallback(int videoStreamHandle, void* cbState, const std::function<void(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)>& cbFunc)
{
   RawVideoFrameServerCbSession* session = new RawVideoFrameServerCbSession(dynamic_cast<PhoneInterface*>(mPhone), this, cbFunc, videoStreamHandle, cbState);
   {
      std::lock_guard<std::mutex> lck(mMapLock);
      mSessions[videoStreamHandle] = session;
   }
   RequestKeyFrame(videoStreamHandle);
}

void RawVideoFrameServerCallback::RemoveCallback(int videoStreamHandle)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   auto it = mSessions.find(videoStreamHandle);
   RawVideoFrameServerCbSession* sess = it->second;
   mSessions.erase(it);
   delete sess;
}
   
RawVideoFrameServerCbSession::RawVideoFrameServerCbSession(CPCAPI2::PhoneInterface* masterSdkPhone, RawVideoFrameServerCallback* transport, const std::function<void(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)>& cbFunc, int videoStream, void* cbState) :
   mCbFunc(cbFunc),
   mMasterPhone(masterSdkPhone),
   mTransport(transport),
   mCbState(cbState)
{
   mStreamStateMap[videoStream].reset(new PerConnectionStreamState(videoStream));
}

RawVideoFrameServerCbSession::~RawVideoFrameServerCbSession()
{
}

void RawVideoFrameServerCbSession::SendBitstreamData(int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride)
{
   const int MAX_NUM_FRAMES_LAG = 20;

   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      const std::shared_ptr<PerConnectionStreamState>& pcss = itStrMap->second;

      pcss->mWaitingForKeyFrame = false;

      if (!pcss->mWaitingForKeyFrame)
      {
         bool didOverflow = false;
         //InfoLog(<< "Invoking video stream callback " << width << "x" << height);
         mCbFunc(mCbState, videoStreamHandle, width, height, ybuffer, ystride, ubuffer, ustride, vbuffer, vstride);
      }
   }
}

bool RawVideoFrameServerCbSession::NeedsKeyframe(int videoStreamHandle)
{
   auto itStrMap = mStreamStateMap.find(videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      if (itStrMap->second->mWaitingForKeyFrame)
      {
         return true;
      }
   }
   return false;
}


}

}

#endif
