
#if !defined(CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_IMPL_H)
#define CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_IMPL_H

#include <stdio.h>

#include <map>
#include <set>

#include "../phone/PhoneInterface.h"
#include "media/audio/AudioHandler.h"
#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
namespace Media
{
   class AudioDeviceChangeHandler
   {
   public:
      // an audio system default device or system default communication device changed
      virtual int onAudioDefaultDeviceChange(AudioDeviceType) = 0;

      // an audio device was added or removed
      virtual int onAudioDeviceChange() = 0;
   };
   
   class AudioDeviceChangeManagerImpl : public resip::ReactorBinded
   {
   public:
      typedef int AudioDeviceChangeMangerImplHandle;
      
      AudioDeviceChangeManagerImpl(resip::MultiReactor&);
      virtual ~AudioDeviceChangeManagerImpl() {}
      virtual void release() {
         delete this;
      }

      virtual AudioDeviceChangeMangerImplHandle addInternalHandler(AudioDeviceChangeHandler* handler);
      virtual void removeInternalHandler(AudioDeviceChangeMangerImplHandle handle);

   protected:
      virtual void sendAudioDefaultSystemDeviceChangeEvent(AudioDeviceType);
      virtual void sendAudioDeviceChangeEvent();
      
   private:
      virtual void sendAudioDefaultSystemDeviceChangeEventImpl(AudioDeviceType);

      virtual void sendAudioDeviceChangeEventImpl();
      
      
      resip::MultiReactor& mReactor;
      
      typedef std::map<AudioDeviceChangeMangerImplHandle, AudioDeviceChangeHandler*> HandlersMap;
      HandlersMap mInternalHandlers;
      
      AudioDeviceChangeMangerImplHandle mNextInternalHandle;

   };
}
}

#endif // CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_IMPL_H
