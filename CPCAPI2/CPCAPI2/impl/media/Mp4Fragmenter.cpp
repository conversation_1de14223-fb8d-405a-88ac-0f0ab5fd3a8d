#include "brand_branded.h"

#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
#include "Mp4Fragmenter.h"

#include <packager/base/template_util.h>
#include <packager/media/codecs/h264_byte_to_unit_stream_converter.h>
#include <packager/media/codecs/avc_decoder_configuration_record.h>
#include <packager/media/codecs/h264_parser.h>
#include <packager/media/formats/mp4/box_reader.h>
#include <packager/media/formats/mp4/box_definitions.h>
#include <packager/media/formats/mp4/mp4_muxer.h>
#include <packager/media/base/buffer_writer.h>
#include <packager/base/atomicops.h>
#include <packager/media/base/muxer_options.h>
#include <packager/file/memory_file.h>
#include <packager/media/base/video_stream_info.h>
#include <packager/media/base/audio_stream_info.h>
#include <packager/media/base/timestamp.h>
#include <packager/media/codecs/h264_parser.h>
#include <packager/media/base/media_sample.h>
#include <packager/media/event/muxer_listener.h>
#include <packager/media/base/media_handler.h>
#include <packager/media/origin/origin_handler.h>
#include <packager/media/chunking/chunking_handler.h>
#include <packager/media/replicator/replicator.h>

#include <rutil/HighPerfReactor.hxx>

#include <webrtc/system_wrappers/interface/clock.h>
#include <webrtc/video_encoder.h>
#include <webrtc/video_frame.h>
#include <voe_base.h>

using namespace shaka::media;
using namespace shaka::media::mp4;

namespace CPCAPI2
{
namespace Media
{
const uint32_t kMpeg2Timescale = 90000;

class CpcMuxListener : public shaka::media::MuxerListener
{
public:
   CpcMuxListener(Mp4Fragmenter* sink);
   virtual ~CpcMuxListener();

   void SetCallbacks(StreamStartedCb* streamStartedCallback,
      StreamEndedCb* streamEndedCallback,
      StreamDataCb* streamDataCallback);

   /// @name MuxerListener implementation overrides.
   /// @{
   void OnEncryptionInfoReady(bool is_initial_encryption_info,
      shaka::media::FourCC protection_scheme,
      const std::vector<uint8_t>& key_id,
      const std::vector<uint8_t>& iv,
      const std::vector<shaka::media::ProtectionSystemSpecificInfo>& key_system_info) override;
   void OnEncryptionStart() override {}
   void OnMediaStart(const shaka::media::MuxerOptions& muxer_options,
      const shaka::media::StreamInfo& stream_info,
      uint32_t time_scale,
      shaka::media::MuxerListener::ContainerType container_type) override;
   void OnSampleDurationReady(uint32_t sample_duration) override;
   void OnMediaEnd(const shaka::media::MuxerListener::MediaRanges& media_ranges,
      float duration_seconds) override;
   void OnNewSegment(const std::string& segment_name,
      uint64_t start_time,
      uint64_t duration,
      uint64_t segment_file_size) override;
   void OnKeyFrame(uint64_t timestamp,
      uint64_t start_byte_offset,
      uint64_t size) override {}
   void OnCueEvent(uint64_t timestamp, const std::string& cue_data) override {}
   void OnSegmentData(const uint8_t* seg_buffer, const size_t segsize, bool sap) override;
   /// @}

private:
   Mp4Fragmenter* mSink;
   std::unique_ptr<StreamStartedCb> mStreamStartedCallback;
   std::unique_ptr<StreamEndedCb> mStreamEndedCallback;
   std::unique_ptr<StreamDataCb> mStreamDataCallback;
};

class CpcMediaHandler : public shaka::media::MediaHandler
{
public:
   CpcMediaHandler();
   virtual ~CpcMediaHandler();

#ifdef WIN32
   void* operator new(size_t i)
   {
      return _mm_malloc(i, 128);
   }

   void operator delete(void* p)
   {
      _mm_free(p);
   }
#endif

   void AddStream(int trackId, const std::shared_ptr<shaka::media::StreamInfo>& streamInfo);
   void AddMediaSample(int trackId, const std::shared_ptr<shaka::media::MediaSample>& mediaSample);

protected:
   /// @name MediaHandler implementation overrides.
   /// @{
   shaka::Status InitializeInternal() override { return shaka::Status::OK; }
   shaka::Status Process(std::unique_ptr<shaka::media::StreamData> stream_data) override {
      return shaka::Status(shaka::error::INTERNAL_ERROR,
         "CpcMediaHandler should not be the downstream handler.");
   }
   bool ValidateOutputStreamIndex(size_t stream_index) const override {
      // We don't know if the stream is valid or not when setting up the graph.
      // Will validate the stream index later when stream info is available.
      return true;
   }
   /// @}

private:
   void AddStreamImpl(int trackId, const std::shared_ptr<shaka::media::StreamInfo>& streamInfo);
   void AddMediaSampleImpl(int trackId, const std::shared_ptr<shaka::media::MediaSample>& mediaSample);

private:
   resip::HighPerfReactor mReactor;
   bool mHaveAudio;
   bool mHaveVideo;
};

Mp4Fragmenter::Mp4Fragmenter()
   : mIsoTimeOffsetVideo(0),
     mIsoTimeOffsetAudio(0),
     mLastVideoFrameTimestamp(0)
{
}

Mp4Fragmenter::~Mp4Fragmenter()
{
}

bool Mp4Fragmenter::IsSidx(const uint8_t* seg_buffer, const size_t segsize)
{
   bool err = false;
   std::unique_ptr<BoxReader> reader(BoxReader::ReadBox(seg_buffer, segsize, &err));
   if (reader.get() == NULL)
   {
      // TODO: throw an error
      return false;
   }
   if (reader->type() == FOURCC_sidx)
   {
      return true;
   }
   return false;
}

class EncodedFrameHandler2 : public webrtc::EncodedImageCallback
{
public:
   EncodedFrameHandler2(Mp4Fragmenter* frag)
      : mFragmenter(frag) {}

   virtual ~EncodedFrameHandler2() {}

   int32_t Encoded(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation)
   {
      mFragmenter->HandleEncodedImage(encoded_image);
      return 0;
   }

   virtual int32_t EncodedSpatialLayer(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation,
      int layerId) {
      if (layerId == 1) {
         mFragmenter->HandleEncodedImage(encoded_image);
      }
      return 0;
   }

   void SetWantsKeyFrame(bool /*wantsKeyFrame*/) {}
   bool WantsKeyFrame() const { return false; }

private:
   Mp4Fragmenter* mFragmenter;
};

webrtc::EncodedImageCallback& Mp4Fragmenter::EncodedFrameHandler() const
{
   return *mEncodedFrameHandler;
}

class Mp4EncodedAudioObserver : public webrtc::EncodedAudioObserver
{
public:
   Mp4EncodedAudioObserver(Mp4Fragmenter* frag) : mFragmenter(frag) {}
   virtual ~Mp4EncodedAudioObserver() {}

   virtual void OnEncodedAudio(
      uint32_t timeStamp,
      const uint8_t* payloadData,
      size_t payloadSize)
   {
      mFragmenter->HandleEncodedAudio(timeStamp, payloadData, payloadSize);
   }

private:
   Mp4Fragmenter* mFragmenter;
};

webrtc::EncodedAudioObserver& Mp4Fragmenter::EncodedAudioObserver() const
{
   return *mEncodedAudioObserver;
}

void Mp4Fragmenter::Init(StreamStartedCb* audioStreamStartedCallback,
   StreamEndedCb* audioStreamEndedCallback,
   StreamDataCb* audioStreamDataCallback,
   StreamStartedCb* videoStreamStartedCallback,
   StreamEndedCb* videoStreamEndedCallback,
   StreamDataCb* videoStreamDataCallback)
{ 
   //std::unique_ptr<CpcMuxListener> audioMuxListener(new CpcMuxListener(this));
   //audioMuxListener->SetCallbacks(audioStreamStartedCallback,
   //   audioStreamEndedCallback,
   //   audioStreamDataCallback);
   std::unique_ptr<CpcMuxListener> videoMuxListener(new CpcMuxListener(this));
   videoMuxListener->SetCallbacks(videoStreamStartedCallback,
      videoStreamEndedCallback,
      videoStreamDataCallback);
   CpcMediaHandler* mh = new CpcMediaHandler();
   mMediaHandler.reset(mh);
   MuxerOptions muxOptions;
   muxOptions.bandwidth = 2000000;
   muxOptions.segment_template = "a"; // meaningless, just has to be > 0 in length to satisfy shaka internals
   muxOptions.mp4_params.num_subsegments_per_sidx = -1;
   //mAudioMp4Mux.reset(new MP4Muxer(muxOptions));
   mVideoMp4Mux.reset(new MP4Muxer(muxOptions));
   //mAudioMp4Mux->SetMuxerListener(std::move(audioMuxListener));
   mVideoMp4Mux->SetMuxerListener(std::move(videoMuxListener));
   mH264parser.reset(new H264Parser());
   mEncodedFrameHandler.reset(new EncodedFrameHandler2(this));
   //mEncodedAudioObserver.reset(new Mp4EncodedAudioObserver(this));
   shaka::ChunkingParams chunking_params;
   chunking_params.segment_duration_in_seconds = 0.5; // 0.001;
   chunking_params.segment_sap_aligned = false;
   chunking_params.subsegment_duration_in_seconds = 0.5; // 0.001;
   chunking_params.subsegment_sap_aligned = false;
   mChunker = std::make_shared<ChunkingHandler>(chunking_params);
   mh->SetHandler(0, mChunker);
   //mh->SetHandler(1, mChunker);
   //mAudioReplicator = std::make_shared<Replicator>();
   mVideoReplicator = std::make_shared<Replicator>();
   mChunker->SetHandler(0, mVideoReplicator);
   //mChunker->SetHandler(1, mAudioReplicator);
   //mChunker->SetHandler(0, mVideoMp4Mux);
   //mChunker->SetHandler(1, mAudioMp4Mux);
   mVideoReplicator->SetHandler(0, mVideoMp4Mux);
   //mAudioReplicator->SetHandler(1, mAudioMp4Mux);
   mh->Initialize();
}

void Mp4Fragmenter::HandleEncodedImage(const webrtc::EncodedImage& encoded_image)
{
   //int64_t rtp_timestamp = encoded_image.ntp_time_ms_;
   webrtc::Clock* clock = webrtc::Clock::GetRealTimeClock();
   int64_t rtp_timestamp = clock->TimeInMilliseconds();
   if (rtp_timestamp <= 0) {
      return;
   }
   
   while (rtp_timestamp <= mLastVideoFrameTimestamp) {
      rtp_timestamp = rtp_timestamp + 5;
   }
   mLastVideoFrameTimestamp = rtp_timestamp;

   NaluReader reader(Nalu::kH264, kIsAnnexbByteStream, encoded_image._buffer, encoded_image._length);

   bool decoder_config_check_pending = false;
   bool is_key_frame = false;
   int pps_id_for_access_unit = 0;
   const H264Sps* sps = NULL;

   while (true) {
      Nalu nalu;
      bool is_eos = false;
      switch (reader.Advance(&nalu)) {
      case NaluReader::kOk:
         break;
      case NaluReader::kEOStream:
         is_eos = true;
         break;
      default:
         //DVLOG(LOG_LEVEL_ES) << "NaluReader error";
         return;
      }
      if (is_eos) {
         break;
      }

      switch (nalu.type()) {
      case Nalu::H264_AUD: {
         //DVLOG(LOG_LEVEL_ES) << "Nalu: AUD";
         break;
      }
      case Nalu::H264_SPS: {
         DVLOG(1) << "Nalu: SPS";
         int sps_id;
         if (mH264parser->ParseSps(nalu, &sps_id) != H264Parser::kOk)
         {
            return;
         }
         decoder_config_check_pending = true;
         break;
      }
      case Nalu::H264_PPS: {
         DVLOG(1) << "Nalu: PPS";
         int pps_id;
         if (mH264parser->ParsePps(nalu, &pps_id) != H264Parser::kOk) {
            // Allow PPS parsing to fail if waiting for SPS.
         }
         else {
            decoder_config_check_pending = true;
         }
         break;
      }
      case Nalu::H264_IDRSlice:
      case Nalu::H264_NonIDRSlice: {
         is_key_frame = (nalu.type() == Nalu::H264_IDRSlice);
         //DVLOG(LOG_LEVEL_ES) << "Nalu: slice IDR=" << is_key_frame;
         H264SliceHeader shdr;
         if (mH264parser->ParseSliceHeader(nalu, &shdr) != H264Parser::kOk) {
            // Only accept an invalid SPS/PPS at the beginning when the stream
            // does not necessarily start with an SPS/PPS/IDR.
         }
         else {
            pps_id_for_access_unit = shdr.pic_parameter_set_id;
         }
         break;
      }
      default: {
         assert(0);
         //DVLOG(LOG_LEVEL_ES) << "Nalu: " << nalu.type();
      }
      }
   }

   // Convert frame to unit stream format.
   H264ByteToUnitStreamConverter streamConverter;
   std::vector<uint8_t> converted_frame;
   if (!streamConverter.ConvertByteStreamToNalUnitStream(
      encoded_image._buffer, encoded_image._length, &converted_frame)) {
      DLOG(ERROR) << "Failure to convert video frame to unit stream format.";
      return;
   }

   if (decoder_config_check_pending && mVideoStreamInfo.get() == NULL)
   {
      const H264Pps* pps = mH264parser->GetPps(pps_id_for_access_unit);
      if (!pps) {
         // Only accept an invalid PPS at the beginning when the stream
         // does not necessarily start with an SPS/PPS/IDR.
         // In this case, the initial frames are conveyed to the upper layer with
         // an invalid VideoDecoderConfig and it's up to the upper layer
         // to process this kind of frame accordingly.
      }
      else {
         sps = mH264parser->GetSps(pps->seq_parameter_set_id);
         if (!sps) {
            DVLOG(1) << "Missing SPS";
            return;
         }
      }

      std::vector<uint8_t> decoder_config_record;
      if (!streamConverter.GetDecoderConfigurationRecord(
         &decoder_config_record)) {
         DLOG(ERROR) << "Failure to construct an AVCDecoderConfigurationRecord";
         return;
      }

      uint32_t coded_width = 0;
      uint32_t coded_height = 0;
      uint32_t pixel_width = 0;
      uint32_t pixel_height = 0;
      if (!ExtractResolutionFromSps(*sps, &coded_width, &coded_height, &pixel_width,
         &pixel_height)) {
         LOG(ERROR) << "Failed to parse SPS.";
         return;
      }

      mVideoStreamInfo.reset(
         new VideoStreamInfo(
         0, // pid
         1000, //kMpeg2Timescale, 
         kInfiniteDuration,
         kCodecH264,
         shaka::media::H26xStreamFormat::kNalUnitStreamWithParameterSetNalus,
         AVCDecoderConfigurationRecord::GetCodecString(shaka::media::FourCC::FOURCC_avc1,
            decoder_config_record[1],
            decoder_config_record[2],
            decoder_config_record[3]),
         decoder_config_record.data(),
         decoder_config_record.size(),
         coded_width,
         coded_height,
         pixel_width,
         pixel_height,
         0,
         H264ByteToUnitStreamConverter::kUnitStreamNaluLengthSize,
         std::string(),
         false));

      dynamic_cast<CpcMediaHandler*>(mMediaHandler.get())->AddStream(0, mVideoStreamInfo);

      //mAudioStreamInfo.reset(
      //   new AudioStreamInfo(
      //      1,
      //      1000,
      //      kInfiniteDuration,
      //      kCodecOpus,
      //      "opus",
      //      NULL,
      //      0,
      //      16,
      //      1,
      //      48000,
      //      0,
      //      0,
      //      64000,
      //      32000,
      //      std::string(),
      //      false)
      //);

      //dynamic_cast<CpcMediaHandler*>(mMediaHandler.get())->AddStream(1, mAudioStreamInfo);
   }

   if (mVideoStreamInfo.get() != NULL) {
      // Create the media sample, emitting always the previous sample after
      // calculating its duration.
      std::shared_ptr<MediaSample> media_sample = MediaSample::CopyFrom(
         converted_frame.data(), converted_frame.size(), is_key_frame);
      if (mIsoTimeOffsetVideo == 0) {
         mIsoTimeOffsetVideo = rtp_timestamp;
      }
      media_sample->set_dts(rtp_timestamp - mIsoTimeOffsetVideo);
      media_sample->set_pts(rtp_timestamp - mIsoTimeOffsetVideo);
      media_sample->set_duration(1); // dummy value

      if (mVideoPendingSample) {
         if (media_sample->dts() > mVideoPendingSample->dts()) {
            int64_t pending_sample_duration = media_sample->dts() - mVideoPendingSample->dts();
            mVideoPendingSample->set_duration(pending_sample_duration);
            dynamic_cast<CpcMediaHandler*>(mMediaHandler.get())->AddMediaSample(0, mVideoPendingSample);
            DVLOG(1) << "Delivered MediaSample (timestamp=" << mVideoPendingSample->dts() << ", key_frame=" << mVideoPendingSample->is_key_frame() << ", size=" << mVideoPendingSample->data_size() << ")";
         }
         else {
            assert(0); // should not happen
         }
      }
      mVideoPendingSample = media_sample;
   }
}

void Mp4Fragmenter::HandleEncodedAudio(uint32_t timeStamp,
   const uint8_t* payloadData,
   size_t payloadSize)
{
   if (!mAudioStreamInfo || !mVideoStreamInfo)
   {
      return;
   }

   if (payloadSize == 0)
   {
      return;
   }

   // Create the media sample
   std::shared_ptr<MediaSample> media_sample = MediaSample::CopyFrom(
      payloadData, payloadSize, false);
   if (mIsoTimeOffsetAudio == 0) {
      mIsoTimeOffsetAudio = timeStamp;
   }
   media_sample->set_dts(timeStamp - mIsoTimeOffsetAudio);
   media_sample->set_pts(timeStamp - mIsoTimeOffsetAudio);
   media_sample->set_duration(20); // dummy value
   media_sample->set_is_key_frame(true);

   if (mAudioPendingSample) {
      if (media_sample->dts() > mAudioPendingSample->dts()) {
         uint64_t pending_sample_duration = media_sample->dts() - mAudioPendingSample->dts();
         mAudioPendingSample->set_duration(pending_sample_duration);
         dynamic_cast<CpcMediaHandler*>(mMediaHandler.get())->AddMediaSample(1, mAudioPendingSample);
         DVLOG(1) << "Delivered MediaSample (timestamp=" << mAudioPendingSample->dts() << ", key_frame=" << mAudioPendingSample->is_key_frame() << ", size=" << mAudioPendingSample->data_size() << ")";
      }
      else {
         assert(0); // should not happen
      }
   }
   mAudioPendingSample = media_sample;
}

CpcMuxListener::CpcMuxListener(Mp4Fragmenter* sink)
   : mSink(sink)
{
}

CpcMuxListener::~CpcMuxListener()
{
}

void CpcMuxListener::SetCallbacks(StreamStartedCb* streamStartedCallback,
   StreamEndedCb* streamEndedCallback,
   StreamDataCb* streamDataCallback)
{
   mStreamStartedCallback.reset(streamStartedCallback);
   mStreamEndedCallback.reset(streamEndedCallback);
   mStreamDataCallback.reset(streamDataCallback);
}

void CpcMuxListener::OnEncryptionInfoReady(bool is_initial_encryption_info, shaka::media::FourCC protection_scheme, const std::vector<uint8_t>& key_id, const std::vector<uint8_t>& iv, const std::vector<shaka::media::ProtectionSystemSpecificInfo>& key_system_info)
{
}

void CpcMuxListener::OnMediaStart(const shaka::media::MuxerOptions& muxer_options,
   const shaka::media::StreamInfo& stream_info,
   uint32_t time_scale,
   shaka::media::MuxerListener::ContainerType container_type)
{
   (*mStreamStartedCallback)();
}

void CpcMuxListener::OnSampleDurationReady(uint32_t sample_duration)
{
}

void CpcMuxListener::OnMediaEnd(const shaka::media::MuxerListener::MediaRanges& media_ranges,
   float duration_seconds)
{
   (*mStreamEndedCallback)();
}

void CpcMuxListener::OnNewSegment(const std::string& segment_name,
   uint64_t start_time,
   uint64_t duration,
   uint64_t segment_file_size)
{
}

void CpcMuxListener::OnSegmentData(const uint8_t* seg_buffer, const size_t segsize, bool sap)
{
   (*mStreamDataCallback)(seg_buffer, segsize, sap);
}

CpcMediaHandler::CpcMediaHandler() : mReactor("MpegStreamingMediaHandler"), mHaveAudio(false), mHaveVideo(false)
{
   mReactor.start();
}

CpcMediaHandler::~CpcMediaHandler()
{
   mReactor.stop();
}

void CpcMediaHandler::AddStream(int trackId, const std::shared_ptr<shaka::media::StreamInfo>& streamInfo)
{
   mReactor.post(resip::resip_bind(&CpcMediaHandler::AddStreamImpl, this, trackId, streamInfo));
}

void CpcMediaHandler::AddStreamImpl(int trackId, const std::shared_ptr<shaka::media::StreamInfo>& streamInfo)
{
   MediaHandler::DispatchStreamInfo(trackId, streamInfo);
}

void CpcMediaHandler::AddMediaSample(int trackId, const std::shared_ptr<shaka::media::MediaSample>& mediaSample)
{
   mReactor.post(resip::resip_bind(&CpcMediaHandler::AddMediaSampleImpl, this, trackId, mediaSample));
}

void CpcMediaHandler::AddMediaSampleImpl(int trackId, const std::shared_ptr<shaka::media::MediaSample>& mediaSample)
{
   MediaHandler::DispatchMediaSample(trackId, mediaSample);
   //if (trackId == 0)
   //{
   //   mHaveVideo = true;
   //}
   //if (trackId == 1)
   //{
   //   mHaveAudio = true;
   //}
   //if (mHaveVideo && mHaveAudio && trackId == 0)
   //{
   //   std::shared_ptr<SegmentInfo> seg_info(new SegmentInfo);
   //   seg_info->is_subsegment = false;
   //   MediaHandler::DispatchSegmentInfo(trackId, seg_info);
   //   mHaveAudio = false;
   //   mHaveVideo = false;
   //}
}



}
}

#endif // (CPCAPI2_BRAND_MP4_SUPPORT == 1)
