#include "AudioDeviceChangeManagerImpl.h"

using namespace CPCAPI2::Media;

AudioDeviceChangeManagerImpl::AudioDeviceChangeManagerImpl(resip::MultiReactor& reactor) :
   mReactor(reactor)
{
}

AudioDeviceChangeManagerImpl::AudioDeviceChangeMangerImplHandle AudioDeviceChangeManagerImpl::addInternalHandler(AudioDeviceChangeHandler* handler)
{
   AudioDeviceChangeMangerImplHandle handle = mNextInternalHandle++;
   mInternalHandlers[handle] = handler;
   return handle;
}

void AudioDeviceChangeManagerImpl::removeInternalHandler(AudioDeviceChangeMangerImplHandle handle)
{
   mInternalHandlers.erase(handle);
}

void AudioDeviceChangeManagerImpl::sendAudioDefaultSystemDeviceChangeEvent(AudioDeviceType deviceType)
{
   mReactor.post(resip::resip_safe_bind(&AudioDeviceChangeManagerImpl::sendAudioDefaultSystemDeviceChangeEventImpl, this, deviceType));
}

void AudioDeviceChangeManagerImpl::sendAudioDefaultSystemDeviceChangeEventImpl(AudioDeviceType deviceType)
{
   HandlersMap::iterator it = mInternalHandlers.begin();
   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onAudioDefaultDeviceChange(deviceType);
   }
}

void AudioDeviceChangeManagerImpl::sendAudioDeviceChangeEvent()
{
   mReactor.post(resip::resip_safe_bind(&AudioDeviceChangeManagerImpl::sendAudioDeviceChangeEventImpl, this));
}

void AudioDeviceChangeManagerImpl::sendAudioDeviceChangeEventImpl()
{
   HandlersMap::iterator it = mInternalHandlers.begin();
   for (; it != mInternalHandlers.end(); ++it)
   {
      it->second->onAudioDeviceChange();
   }
}