#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)

#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
#include "Mp4Fragmenter.h"
#endif

#include "VideoExtImpl.h"
#include "VideoFrameServer_WebSocket.h"
#include "VideoFrameSnapshotHelper.h"
#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"

#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <MediaUtils.hxx>

#include <functional>

#include <vie_codec.h>
#include <vie_rtp_rtcp.h>

#include "../util/PlatformLog.h"

using namespace webrtc_recon;
using namespace resip;

namespace CPCAPI2
{
namespace Media
{
VideoExtImpl::VideoExtImpl(webrtc_recon::MediaStackImpl* mediaStack, VideoInterface *videoInterface, PhoneInterface* phone) :
   mMediaStack(mediaStack),
   mVideoInterface(videoInterface),
   mPhone(phone),
   mMediaHandler(NULL)
#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
   , mMp4Fragmenter(NULL)
#endif
   , mVideoFrameServer(NULL)
{
}

VideoExtImpl::~VideoExtImpl()
{
}

int VideoExtImpl::setHandler(VideoHandler* handler)
{
   mMediaHandler = handler;
   return kSuccess;
}

void VideoExtImpl::postCallback(resip::ReadCallbackBase* fp)
{
   mVideoInterface->postCallback(fp);
}

#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
struct StreamStartedWrapperCb : StreamStartedCb
{
   StreamStartedWrapperCb(int id, VideoBitstreamHandler* h)
      : videoStreamId(id), handler(h) 
   {
   }
   virtual ~StreamStartedWrapperCb() {}

   virtual void operator()()
   {
      handler->onBitstreamStarted(videoStreamId, BitstreamStartedEvent());
   }

   int videoStreamId;
   VideoBitstreamHandler* handler;
};

struct StreamEndedWrapperCb : StreamEndedCb
{
   StreamEndedWrapperCb(int id, VideoBitstreamHandler* h)
      : videoStreamId(id), handler(h)
   {
   }
   virtual ~StreamEndedWrapperCb() {}

   virtual void operator()()
   {
      handler->onBitstreamEnded(videoStreamId, BitstreamEndedEvent());
   }

   int videoStreamId;
   VideoBitstreamHandler* handler;
};

struct StreamDataWrapperCb : StreamDataCb
{
   StreamDataWrapperCb(int id, VideoBitstreamHandler* h)
      : videoStreamId(id), handler(h)
   {
   }
   virtual ~StreamDataWrapperCb() {}

   virtual void operator()(const uint8_t* streamdata, unsigned int datasize, bool sap)
   {
      BitstreamDataEvent evt;
      evt.data = streamdata;
      evt.size = datasize;
      evt.sap = sap;
      handler->onBitstreamData(videoStreamId, evt);
   }

   int videoStreamId;
   VideoBitstreamHandler* handler;
};
#endif

/*
int VideoExtImpl::connectReceiveStreamToMp4Output(int recv_channel, VideoBitstreamHandler* bitstreamHandler)
{
#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
   // .jjg. TODO WEBSS - there should be one fragmenter per recv_channel
   if (mMp4Fragmenter == NULL)
   {
      mMp4Fragmenter = new Mp4Fragmenter();
      StreamStartedCb* streamStartedCb = new StreamStartedWrapperCb(recv_channel, bitstreamHandler);
      StreamEndedCb* streamEndedCb = new StreamEndedWrapperCb(recv_channel, bitstreamHandler);
      StreamDataCb* streamDataCb = new StreamDataWrapperCb(recv_channel, bitstreamHandler);
      //mMp4Fragmenter->Init(streamStartedCb, streamEndedCb, streamDataCb);
      mMediaStack->videoCodec()->RegisterPreDecodeObserver(recv_channel, mMp4Fragmenter->EncodedFrameHandler());
   }
#endif
   return kSuccess;
}
*/

int VideoExtImpl::startWebsocketServer(VideoWebsocketServerHandler* handler)
{
#if (CPCAPI2_BRAND_VIDEO_WEBSOCKET_SERVER == 1)
   if (mVideoFrameServer == NULL)
   {
      mVideoFrameServer = new VideoFrameServerWebSocket(mPhone);
   }
   VideoWebsocketServerStartedEvent args;
   mVideoFrameServer->startServer(resip::resip_bind(&VideoWebsocketServerHandler::onVideoWebsocketServerStarted, handler, -1, args));
#endif   
   return kSuccess;
}

int VideoExtImpl::startWebsocketServerForReceiveStream(int recv_channel)
{
#if (CPCAPI2_BRAND_VIDEO_WEBSOCKET_SERVER == 1)
   if (mVideoFrameServer == NULL)
   {
      return kError;
   }

   if (mVideoFrameServer->channel() != -1)
   {
      mMediaStack->videoCodec()->DeregisterPreDecodeObserver(mVideoFrameServer->channel(), mVideoFrameServer->encoded_image_callback());
   }

   mVideoFrameServer->bindServerToChannel(recv_channel);

   mMediaStack->videoCodec()->RegisterPreDecodeObserver(recv_channel, *mVideoFrameServer->encoded_image_callback());

   mMediaStack->vie_rtp_rtcp()->RequestKeyFrame(recv_channel);

   
   //std::shared_ptr<MixerImpl> mixerImplPtr = std::dynamic_pointer_cast<MixerImpl>(mMediaStack->mixer());
   //if (mVideoFrameServer->channel() != -1)
   //{
   //   mixerImplPtr->unregisterDecodedFrameObserver(mVideoFrameServer->channel(), mVideoFrameServer);
   //}

   //mVideoFrameServer->bindServerToChannel(recv_channel);
   
   //mixerImplPtr->registerDecodedFrameObserver(recv_channel, mVideoFrameServer);
#endif   
   return kSuccess;
}

int VideoExtImpl::stopWebsocketServer()
{
#if (CPCAPI2_BRAND_VIDEO_WEBSOCKET_SERVER == 1)
   if (mVideoFrameServer != NULL)
   {
      if (mVideoFrameServer->channel() != -1)
      {
         //std::shared_ptr<MixerImpl> mixerImplPtr = std::dynamic_pointer_cast<MixerImpl>(mMediaStack->mixer());
         //mixerImplPtr->unregisterDecodedFrameObserver(mVideoFrameServer->channel(), mVideoFrameServer);
         mMediaStack->videoCodec()->DeregisterPreDecodeObserver(mVideoFrameServer->channel(), mVideoFrameServer->encoded_image_callback());
      }
      mVideoFrameServer->stopServer();
   }
#endif
   return kSuccess;
}

int VideoExtImpl::createSnapshotForReceiveStream(int recv_channel, const cpc::string& fileName, VideoSnapshotHandler* snapshotHandler)
{
   return kSuccess;
}

int VideoExtImpl::setBrokenKeyFrameSupportEnabled(bool enable)
{
   mMediaStack->setBrokenKeyFrameSupportEnabled(enable);
   return kSuccess;
}

int VideoExtImpl::setKeyFramePeriod(int periodInFrames)
{
   mMediaStack->setKeyFramePeriod(periodInFrames);
   return kSuccess;
}
}
}
#endif // CPCAPI2_BRAND_VIDEO_EXT_MODULE
#endif // CPCAPI2_BRAND_VIDEO_MODULE
#endif // CPCAPI2_BRAND_MEDIA_MODULE
