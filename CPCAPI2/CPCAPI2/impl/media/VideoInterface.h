#pragma once

#if !defined(CPCAPI2_VIDEO_INTERFACE_H)
#define CPCAPI2_VIDEO_INTERFACE_H

#include "cpcapi2defs.h"
#include "media/video/Video.h"
#include "../experimental/video_ext/VideoExt.h"
#include "media/MediaManager.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "../phone/PhoneModule.h"

#include <rutil/Fifo.hxx>
#include <resip/dum/DumCommand.hxx>

#include <set>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class MediaManagerInterface;
class VideoImpl;
class VideoExtImpl;
class VideoInternalHandler;

class VideoInterface : public Video,
                       public VideoExt,
                       public PhoneModule
{
public:
   VideoInterface(MediaManagerInterface* mm);
   virtual ~VideoInterface();

   virtual void Release() OVERRIDE;

   virtual int setHandler(VideoHandler* handler) OVERRIDE;

   virtual int queryDeviceList() OVERRIDE;

   virtual int setCaptureDevice(unsigned int deviceId) OVERRIDE;
   virtual int setCaptureDeviceOrientation(VideoOrientation orientation) OVERRIDE;
   virtual int setCaptureImageOrientation(VideoImageOrientation orientation) OVERRIDE;
   virtual int setIncomingVideoRenderTarget(void* surface, VideoSurfaceType type) OVERRIDE;
   virtual int setIncomingVideoRenderTarget(int recvVideoStreamId, void* surface, VideoSurfaceType type) OVERRIDE;
   virtual int setLocalVideoRenderTarget(void* surface, VideoSurfaceType type) OVERRIDE;
   virtual int setLocalVideoPreviewResolution(VideoCaptureResolution resolution) OVERRIDE;
   virtual int startCapture() OVERRIDE;
   virtual int stopCapture() OVERRIDE;

   virtual int startScreenshare(ScreenShareHandler*) OVERRIDE;
   virtual int stopScreenshare() OVERRIDE;

   virtual int setPreferredResolution(unsigned int codecId, VideoCaptureResolution resolution) OVERRIDE;

   virtual int queryCodecList() OVERRIDE;

   virtual int setCodecEnabled(unsigned int codecId, bool enabled) OVERRIDE;
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) OVERRIDE;
   virtual int setCodecPayloadType(unsigned int codecId, unsigned int payloadType) OVERRIDE;
   virtual int setCodecEncodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled) OVERRIDE;
   virtual int setCodecDecodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled) OVERRIDE;
   virtual int setPacketLossConfig(unsigned int codecId, const PacketLossConfig& packetLossConfig) OVERRIDE;
   virtual int setVideoMute(bool enabled) OVERRIDE;
   virtual int setVideoMixMode(VideoMixMode mixMode) OVERRIDE;
   virtual int setCodecConfig(const H264Config& config) OVERRIDE;
   virtual int connectVideoStreams(int recvVideoStreamId, int sendVideoStreamId) OVERRIDE;
   virtual int disconnectVideoStreams(int recvVideoStreamId, int sendVideoStreamId) OVERRIDE;

   //virtual int connectReceiveStreamToMp4Output(int videoStreamId, VideoBitstreamHandler* bitstreamHandler);
   virtual int set1080pEnabled(bool enable) OVERRIDE;
   virtual int startWebsocketServer(VideoWebsocketServerHandler* handler) OVERRIDE;
   virtual int startWebsocketServerForReceiveStream(int videoStreamId) OVERRIDE;
   virtual int stopWebsocketServer() OVERRIDE;
   virtual int createSnapshotForReceiveStream(int videoStreamId, const cpc::string& fileNameUtf8, VideoSnapshotHandler* snapshotHandler) OVERRIDE;
   virtual int setBrokenKeyFrameSupportEnabled(bool enable) OVERRIDE;
   virtual int setKeyFramePeriod(int periodInFrames) OVERRIDE;
   virtual int queryScreenshareDeviceList(ScreenshareDeviceListHandler* handler, bool includeMonitors, bool includeWindows) OVERRIDE;
   virtual int setScreenshareCaptureDevice(unsigned int deviceId) OVERRIDE;
   virtual int setScreenshareCaptureMaxFramerate(unsigned int maxFrameRate, bool forceOverride = false) OVERRIDE;
   virtual int setInterleavedModeEnabled(bool enabled) OVERRIDE;

   int setVideoApiEnabled(bool enabled) OVERRIDE;

   int setVideoDscp(unsigned int mediaDscp) OVERRIDE;
   virtual int setVideoUseQosFastlane(bool useQosFastlane, bool useWithDscp) OVERRIDE;

   int showPropertyPage(void* surface) OVERRIDE;

   virtual int requestKeyFrame(int recvVideoStreamId) OVERRIDE;

   webrtc_recon::MediaStackImpl* media_stack() const;
   
   void postCallback(resip::ReadCallbackBase* fp);

   void updatePerformanceProfile(DevicePerformanceProfile perfProfile);

   VideoImpl* getImpl() const {
      return mImpl;
   }

   void addSdkObserver(VideoHandler* observer);
   void removeSdkObserver(VideoHandler* observer);

   void fireVideoDeviceListUpdated(VideoHandler* appHandler, const VideoDeviceListUpdatedEvent& args);
   void fireScreenshareDeviceListUpdated(ScreenshareDeviceListHandler* appHandler, const ScreenshareDeviceListEvent& args);
   void fireScreenShareError(ScreenShareHandler* appHandler);

private:
  bool isVideoEnabled() {return mVideoApiEnabled;};
  void setCodecConfigImpl(const H264Config& config);
  void setInterleavedModeEnabledImpl(bool enabled);

private:
   MediaManagerInterface* mMM;
   VideoImpl* mImpl;
   VideoExtImpl* mExtImpl;
   bool mVideoApiEnabled;
   std::set<VideoHandler*> mSdkObservers;
};
}
}
#endif // CPCAPI2_VIDEO_INTERFACE_H
