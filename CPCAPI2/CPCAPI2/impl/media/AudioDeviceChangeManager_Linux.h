#ifndef CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_LINUX_H
#define CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_LINUX_H
#if defined(__linux__) && !defined(ANDROID)
#include "AudioDeviceChangeManagerImpl.h"
#include "../util/cpc_thread.h"
#include <MediaStackImpl.hxx>
#include <map>

#if defined(USE_PULSEAUDIO)
struct pa_context;
struct pa_devicelist;
struct pa_source_info;
struct pa_sink_info;
#endif

namespace CPCAPI2
{
namespace Media
{
class AudioDeviceChangeManager_Linux : public AudioDeviceChangeManagerImpl
{
public:
   AudioDeviceChangeManager_Linux(resip::MultiReactor&);
   virtual ~AudioDeviceChangeManager_Linux();

private:
   std::atomic<bool> mShutdown;
   std::unique_ptr<CPCAPI2::thread> mPollingThread;

#if defined(USE_PULSEAUDIO)
   void pollAudioInterfacesThread();

   int getDeviceList(std::map<int, std::string> *inputDevices,
                     std::map<int, std::string> *outputDevices);

   /* Callbacks for PulseAudio */
   static void pa_state_cb(pa_context *c, void *userdata);
   static void pa_sinklist_cb(pa_context *c, const pa_sink_info *l, int eol,
                              void *userdata);
   static void pa_sourcelist_cb(pa_context *c, const pa_source_info *l,
                                int eol, void *userdata);
#endif
};

} // namespace Media
} // namespace CPCAPI2

#endif // __linux__
#endif // CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_LINUX_H
