#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)

#include "PlaySoundDevice.h"
#include "MediaStackImpl.hxx"
#include "../util/cpc_logger.h"

#include <voe_base.h>
#include <voe_dtmf.h>
#include <voe_audio_processing.h>
#include <voe_hardware.h>
#include <voe_file.h>
#include <voe_volume_control.h>

#include <cassert>

#ifdef WIN32
#define CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL
#endif

#if defined(ANDROID) && defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
#error Cannot use CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL on Android
#endif

using namespace webrtc;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

namespace CPCAPI2
{
namespace Media
{
PlaySoundDevice::PlaySoundDevice()
   : mVoiceEngine(NULL),
     mVoEBase(NULL),
     mVoEFile(NULL),
     mVoEDtmf(NULL),
     mVoEHardware(NULL),
     mVoEAudioProcessing(NULL),
     mVoEVolumeControl(NULL),
     mOwnMediaStack(true)
{
}

PlaySoundDevice::~PlaySoundDevice()
{
   shutdown();
}

int PlaySoundDevice::init(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, webrtc::AudioLayers audioLayer, int32_t audioUsage)
{
   InfoLog(<< "PlaySoundDevice::init(" << audioLayer << " " << audioUsage << ")");
   if (mVoiceEngine == NULL)
   {
       mVoiceEngine = VoiceEngine::Create();
       if (mVoiceEngine != NULL)
       {
          mVoEHardware = VoEHardware::GetInterface(mVoiceEngine);
          assert(mVoEHardware);
          mVoEHardware->SetAudioDeviceLayer(audioLayer);
          mVoEHardware->SetAudioUsage(audioUsage);
          mVoEHardware->RegisterSystemAudioServiceErrorCallback(systemAudioServiceErrorCallback);

          mVoEBase = VoEBase::GetInterface(mVoiceEngine);
          assert(mVoEBase);
          mVoEBase->Init();

          mVoEAudioProcessing = VoEAudioProcessing::GetInterface(mVoiceEngine);
          assert(mVoEAudioProcessing);

          mVoEFile = VoEFile::GetInterface(mVoiceEngine);
          assert(mVoEFile);

          mVoEDtmf = VoEDtmf::GetInterface(mVoiceEngine);
          assert(mVoEDtmf);

          mVoEVolumeControl = VoEVolumeControl::GetInterface(mVoiceEngine);
          assert(mVoEVolumeControl);
      }
   }
   if (mVoiceEngine != NULL && mVoEBase != NULL)
   {
      return 0;
   }
   ErrLog(<< "PlaySoundDevice::init(" << audioLayer << " " << audioUsage << ") - error");
   return -1;
}

int PlaySoundDevice::shutdown()
{
   InfoLog(<< "PlaySoundDevice::shutdown()");
   if (mVoiceEngine != NULL && mOwnMediaStack)
   {
      mVoEAudioProcessing->Release();
      mVoEVolumeControl->Release();
      mVoEDtmf->Release();
      if (mVoEFile != NULL) { mVoEFile->Release(); }
      mVoEHardware->Release();
      mVoEBase->Release();
      VoiceEngine::Delete(mVoiceEngine);
      mVoiceEngine = NULL;
      mVoEBase = NULL;
      mVoEHardware = NULL;
      mVoEFile = NULL;
      mVoEDtmf = NULL;
      mVoEVolumeControl = NULL;
      mVoEAudioProcessing = NULL;
   }
   return 0;
}

std::shared_ptr<PlaySoundDeviceAllocationHandle> PlaySoundDevicePool::getPlaySoundDevice(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, int32_t audioUsage)
{
#if defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
   std::vector<std::shared_ptr<PlaySoundDeviceAllocation> >::iterator it = mPool.begin();
   for (; it != mPool.end(); ++it)
   {
      if (!(*it)->allocated)
      {
         (*it)->allocated = true;
         return std::shared_ptr<PlaySoundDeviceAllocationHandle>(new PlaySoundDeviceAllocationHandle(*it));
      }
   }
#endif

   std::shared_ptr<PlaySoundDeviceAllocation> alloc(new PlaySoundDeviceAllocation);
   alloc->device = new PlaySoundDevice();
   if (alloc->device->init(systemAudioServiceErrorCallback, webrtc::kAudioPlatformDefault, audioUsage) != 0)
   {
      delete alloc->device;
      alloc->device = NULL;
      return std::shared_ptr<PlaySoundDeviceAllocationHandle>();
   }
   alloc->allocated = true;
#if defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
   mPool.push_back(alloc);
#endif
   return std::shared_ptr<PlaySoundDeviceAllocationHandle>(new PlaySoundDeviceAllocationHandle(alloc));
}

#if defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
void PlaySoundDevicePool::init(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, unsigned int size, webrtc::AudioLayers audioLayer)
{
   for (unsigned int i=0; i<size; i++)
   {
      std::shared_ptr<PlaySoundDeviceAllocation> alloc(new PlaySoundDeviceAllocation);
      alloc->device = new PlaySoundDevice();
      if (alloc->device->init(systemAudioServiceErrorCallback, audioLayer, -1) != 0)
      {
         return;
      }
      alloc->allocated = false;
      mPool.push_back(alloc);
   }
}
#else
void PlaySoundDevicePool::init(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, unsigned int /*size*/, webrtc::AudioLayers /*audioLayer*/)
{
}
#endif

void PlaySoundDevicePool::shutdown()
{
#if defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
   std::vector<std::shared_ptr<PlaySoundDeviceAllocation> >::iterator it = mPool.begin();
   for (; it != mPool.end(); ++it)
   {
      (*it)->device->shutdown();
      delete (*it)->device;
   }
   mPool.clear();
#endif
}

PlaySoundDeviceAllocationHandle::~PlaySoundDeviceAllocationHandle()
{
   mAlloc->allocated = false;
#if !defined(CPCAPI2_USE_PLAY_SOUND_MEMORY_POOL)
   mAlloc->device->shutdown();
   delete mAlloc->device;
#endif
}

}
}

#endif // CPCAPI2_BRAND_MEDIA_MODULE == 1
