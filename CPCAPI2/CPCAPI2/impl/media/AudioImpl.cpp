#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#include "AudioImpl.h"
#include "PlaySoundHelper.h"
#include "PlaySoundDevice.h"
#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"

#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <RtpStreamImpl.hxx>
#include <MixerImpl.hxx>

#include <boost/algorithm/string.hpp>

#include <voe_base.h>
#include <voe_hardware.h>
#include <voe_audio_processing.h>
#include <voe_conference.h>
#include <voe_volume_control.h>
#include <webrtc/modules/audio_device/include/audio_device.h>
#include <webrtc/modules/audio_device/dummy/file_audio_device_factory.h>

#include "MediaManagerInterface.h"
#include "impl/media/jsonrpc/AudioCompatibility.h"
#include "gen/Audio/datatypes/AudioEvents.h"
#include "gen/Audio/datatypes/AudioAudioCodecListUpdatedEvent.h"
#include "phone/EventQueue.h"

#ifdef ANDROID
#include <webrtc/modules/audio_device/audio_device_impl.h>
#include <webrtc/modules/utility/interface/helpers_android.h>
#endif

#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)
#include "AudioDeviceChangeManager_OSX.h"
#elif _WIN32
#include "AudioDeviceChangeManager_Win.h"
#elif __linux__
#include "AudioDeviceChangeManager_Linux.h"
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

static const int kWebRtcDefaultSystemDeviceIndex = -2; // the magic index that represents the default system device for WebRTC on desktop platforms
static const int kWebRtcDefaultSystemCommDeviceIndex = -1; // the magic index that represents the default communications device for WebRTC on windows
static const int kWebRtcNotFoundDeviceIndex = 8000;
static const unsigned short RecordingDeviceActivityTimerIndex = 0;
static const int CheckRecordingDeviceActivityIntervalMS = 3000; // TODO: configurable?

using namespace webrtc_recon;
using namespace resip;

namespace CPCAPI2
{
namespace Media
{

// DRL FIXIT! Move this class to its own file.
class AudioDeviceHeuristics
{
   std::vector<cpc::string> g_cSoundCards;                     // sound card manufacturers/models/ etc.
   std::vector<cpc::string> g_cPreferredDeviceKeywords;        // lowercase keywords we prefer (i.e. we prefer "microphone" but not "line in")
   std::vector<cpc::string> m_cExcludeList;
   std::vector<cpc::string> m_cInadvisableList;
   std::vector<cpc::string> m_cHeadset;
   std::vector<cpc::string> m_cSpeakerPhone;

   void LoadExcludeList()
   {
#ifdef WIN32
      m_cExcludeList.push_back("(default wave in)");
      m_cExcludeList.push_back("(default wave out)");
      m_cInadvisableList.push_back("TotalRecorder");
      m_cInadvisableList.push_back("Modem ");
      m_cInadvisableList.push_back("WsAudio_DeviceS");             // DRL
      m_cInadvisableList.push_back("Stereo Mix");
      m_cInadvisableList.push_back("AirPods Stereo");              // DRL the Mac AirPods have hands-free and stereo output devices and the stereo one
      m_cInadvisableList.push_back("AirPods Pro Stereo");          // prevents mic audio so we don't like it
#endif
#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
      m_cExcludeList.push_back("Built-in Input: Line In");
#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)

      m_cInadvisableList.push_back(" Virtual ");

      // we only need to load this static list once
      g_cSoundCards.push_back("Audigy");
      g_cSoundCards.push_back("SoundMAX Digital Audio");
      g_cSoundCards.push_back("C-Media Wave Device");
      g_cSoundCards.push_back("Realtek AC97");
      g_cSoundCards.push_back("nForce");
      g_cSoundCards.push_back("Realtek HD");
      g_cSoundCards.push_back("Creative SB");          // Creative SB Live!
      g_cSoundCards.push_back("High Definition");      // IDT High Definition Audio (laptop)
      g_cSoundCards.push_back("SmartAudio HD");    // Conexant 20585 SmartAudio HD

      // DRL FIXIT! We'll want to add other language versions here too!

      // these are lowercase keywords we like (i.e. we don't like "line-in" and such compared to these)
      g_cPreferredDeviceKeywords.push_back("microphone");
      g_cPreferredDeviceKeywords.push_back(" mic ");
      g_cPreferredDeviceKeywords.push_back("speaker");
      g_cPreferredDeviceKeywords.push_back("earphone");
   }

   void LoadHeadsetData()
   {
      // This list should contain the name (or a portion thereof) of known headset devices. If the name will
      // also match with a non-headset device we shouldn't include it here.

      // Headsets (in order of priority)
      m_cHeadset.push_back("Headset");             // generic catch
      m_cHeadset.push_back("headset");             // generic catch
      m_cHeadset.push_back("Headphone");           // generic catch
      m_cHeadset.push_back("headphone");           // generic catch
      m_cHeadset.push_back("Earphone");            // generic catch (an earphone is likely to be a headset)
      m_cHeadset.push_back("earphone");            // generic catch (an earphone is likely to be a headset)
      m_cHeadset.push_back("Bluetooth");           // it's very unlikely to be a speakerphone
      m_cHeadset.push_back(" BT ");                // AKA bluetooth, it's very unlikely to be a speakerphone
      m_cHeadset.push_back("Dongle");                     // any dongle is likely to talk to a wireless headset (except for the SLIC dongle)
      m_cHeadset.push_back("Earpiece");            // Android

      m_cHeadset.push_back("Sennheiser");
      m_cHeadset.push_back("Clarisys");
      m_cHeadset.push_back("Mobile Freedom");      // Logitech?
      m_cHeadset.push_back("Zalman");

      // Plantronics
      m_cHeadset.push_back("PLT 510");
      m_cHeadset.push_back("PLTDA60");
      m_cHeadset.push_back("CS50/CS60");
      m_cHeadset.push_back("DA45 Adapter");
      m_cHeadset.push_back("Plantronics BT300");
      m_cHeadset.push_back("BUA-200");
      m_cHeadset.push_back("Savi Office");
      m_cHeadset.push_back("Plantronics D100");
      m_cHeadset.push_back("Plantronics C310");
      m_cHeadset.push_back("Plantronics C420");
      m_cHeadset.push_back("Plantronics C610");
      m_cHeadset.push_back("Plantronics C620");
      m_cHeadset.push_back("Plantronics C710");
      m_cHeadset.push_back("Plantronics Blackwire");
      m_cHeadset.push_back("Plantronics .Audio 400 DSP");

      // GN Netcom/Jabra
      m_cHeadset.push_back("Jabra BIZ");
      m_cHeadset.push_back("Jabra LINK");
      m_cHeadset.push_back("Jabra GO");
      m_cHeadset.push_back("Jabra DIAL");
      m_cHeadset.push_back("Jabra PRO");
      m_cHeadset.push_back("GN Netcom");
      m_cHeadset.push_back("GN 81");               // GN Netcom 8110, 8120
      m_cHeadset.push_back("GN 93");               // GN Netcom 9330, 9350

      // Handsets
      m_cHeadset.push_back("Handset");             // generic catch
      m_cHeadset.push_back("Claritel-i750");

      // Desk Phones
      m_cHeadset.push_back(" Phone");              // generic catch, don't match SpeakerPhone
      m_cHeadset.push_back("Intel Slic");
      //   m_cHeadset.push_back("SLIC Dongle");         // Motorola        already covered by "Dongle" above
      m_cHeadset.push_back("ITA Audio for Skype"); // as above when Skype installed

      /* DRL The following could be for any number of devices (such as a video camera) so it is best
       not to include it here. We have special code below to include it in some cases.
       m_cHeadset.push_back("USB Audio Device"); // Yealink
       */
   }

   void LoadSpeakerPhoneData()
   {
      // This list should contain the name (or a portion thereof) of known devices that could be used
      // in speakerphone mode. If the name will also match with a headset device we shouldn't include it here.

      m_cSpeakerPhone.push_back("SpeakerPhone");   // generic catch
      m_cSpeakerPhone.push_back("Speakerphone");   // generic catch
#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
      m_cSpeakerPhone.push_back("Built-in");
#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)
      m_cSpeakerPhone.push_back("Polycom Communicator");
      m_cSpeakerPhone.push_back("Polycom CX100");
      m_cSpeakerPhone.push_back("Logitech Mic");
      m_cSpeakerPhone.push_back("Logitech QuickCam Microphone");
      m_cSpeakerPhone.push_back("Logitech Camera");
      m_cSpeakerPhone.push_back("LifeCam");
      m_cSpeakerPhone.push_back("WebCam");
      m_cSpeakerPhone.push_back("Webcam");
      m_cSpeakerPhone.push_back("Web Cam");
      m_cSpeakerPhone.push_back("Quickcam");
      m_cSpeakerPhone.push_back("QuickCam");
      m_cSpeakerPhone.push_back("TalkCam");
      m_cSpeakerPhone.push_back(" Camera");
      m_cSpeakerPhone.push_back("Chat 50");
      m_cSpeakerPhone.push_back("Jabra SPEAK");
      m_cSpeakerPhone.push_back("Plantronics P540");
   }

   inline bool ListContainsPartOf(const std::vector<cpc::string>& list, const cpc::string& str)
   {
      for (std::vector<cpc::string>::const_iterator iter = list.begin(); iter != list.end(); ++iter)
         if (str.find(*iter) != cpc::string::npos)
            return true;
      return false;
   }

public:
   AudioDeviceHeuristics()
   {
      LoadHeadsetData();
      LoadSpeakerPhoneData();
      LoadExcludeList();
   }

   bool ShouldBeExcluded(cpc::string cDeviceName, AudioDeviceType deviceType, const cpc::vector<AudioDeviceInfo>& audioDeviceList)
   {
      // we use find() to match with a name that has a number added (as in "USB Audio Device (2)") or
      // wrapped in Windows 7 (as in "Microphone (USB Audio Device)")
      for (std::vector<cpc::string>::iterator iter = m_cExcludeList.begin(); iter != m_cExcludeList.end(); ++iter)
         if (cDeviceName.find(*iter) != cpc::string::npos)
            return true;

#if defined (__APPLE__) && !(TARGET_OS_IPHONE)
      // on Mac the default device will appear twice, once wrapped with "default (Xxx)" so we ignore those duplicates
      // as they will have a different ID and if selected we won't find them by ID if another device becomes the default

      std::string deviceName(cDeviceName.c_str());
      const std::string& defaultPrefix = "default (";
      if (deviceName.find(defaultPrefix) == 0)
      {
         deviceName = deviceName.substr(defaultPrefix.length(), std::string::npos);
         if (deviceName.size() > 0 &&
             deviceName.find_last_of(")", deviceName.size() - 1) != std::string::npos)
         {
            return true;
         }
      }
#endif // #if defined (__APPLE__) && !(TARGET_OS_IPHONE)

      return false;
   }

   bool IsHeadset(cpc::string cDeviceName, const cpc::vector<AudioDeviceInfo>& audioDeviceList)
   {
      // This device name is used by the OS for generic audio devices, including
      // video cameras with a microphone. We only want to include this device as
      // a headset when it appears as both an input and output device in order to
      // avoid including video cameras.
      static const cpc::string USBDevice("USB Audio Device");
      if (cDeviceName == USBDevice)
      {
         int count = 0;
         for (cpc::vector<AudioDeviceInfo>::const_iterator iter = audioDeviceList.begin(); iter != audioDeviceList.end(); ++iter)
         {
            if ((*iter).deviceType == MediaDeviceType_Capture && (*iter).friendlyName == USBDevice)
            {
               count++;
               break;
            }
         }
         for (cpc::vector<AudioDeviceInfo>::const_iterator iter = audioDeviceList.begin(); iter != audioDeviceList.end(); ++iter)
         {
            if ((*iter).deviceType == MediaDeviceType_Render && (*iter).friendlyName == USBDevice)
            {
               count++;
               break;
            }
         }

         return count == 2;
      }

      return ListContainsPartOf(m_cHeadset, cDeviceName);
   }

   bool IsSpeakerPhone(cpc::string cDeviceName, const cpc::vector<AudioDeviceInfo>& audioDeviceList)
   {
      return ListContainsPartOf(m_cSpeakerPhone, cDeviceName);
   }

   bool IsBluetooth(cpc::string cDeviceName, const cpc::vector<AudioDeviceInfo>& audioDeviceList)
   {
#ifdef ANDROID
      if (cDeviceName.find("Bluetooth") != cpc::string::npos)
        return true;
      return false;
#else
      return false;
#endif
   }

   bool IsInadvisableDevice(const AudioDeviceInfo& device)
   {
      for (std::vector<cpc::string>::iterator iter = m_cInadvisableList.begin(); iter != m_cInadvisableList.end(); ++iter)
      {
         if (device.friendlyName.find(*iter) != cpc::string::npos)
         {
            return true;
         }
      }

      return false;
   }
};

static AudioDeviceHeuristics g_heuristic;

AudioImpl::AudioImpl(webrtc_recon::MediaStackImpl* mediaStack, AudioInterface *audioInterface, resip::MultiReactor& reactor) :
   mMediaStack(mediaStack),
   mAudioInterface(audioInterface),
   mMediaHandler(NULL),
   mReactor(reactor),
   mAudioLevelMonitor(NULL),
   mReportingInputAudioLevels(false),
   mReportingOutputAudioLevels(false),
   mRecordingDeviceActivityTimer(new resip::DeadlineTimer(reactor)),
   mIsRecordingDeviceActive(false)
{
   mMainSdkThreadCheck.test();
#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)
   mAudioDeviceChangeManager = new AudioDeviceChangeManager_OSX(reactor);
#elif defined(_WIN32)
   mAudioDeviceChangeManager = new AudioDeviceChangeManager_Win(reactor);
#elif defined(__linux__) && !defined(ANDROID)
   mAudioDeviceChangeManager = new AudioDeviceChangeManager_Linux(reactor);
#else
   mAudioDeviceChangeManager = new AudioDeviceChangeManagerImpl(reactor); // will not fire device change callbacks
#endif

   mAudioDeviceChangeImplHandle = mAudioDeviceChangeManager->addInternalHandler(this);

   onTimer(RecordingDeviceActivityTimerIndex, NULL);
}

int AudioImpl::init()
{
   mMainSdkThreadCheck.test();
   std::shared_ptr<MixerImpl> mixerImplPtr = std::dynamic_pointer_cast<MixerImpl>(mMediaStack->mixer());
   if (mixerImplPtr.get() != NULL)
   {
      mixerImplPtr->registerMixerObserver(this);
   }
   return 0;
}

int AudioImpl::initPlaySoundDevicePool()
{
   mMainSdkThreadCheck.test();
   if (!mMediaStack->settings()->audioOutputDisabled)
   {
      mPlaySoundDevicePool.init(this, 3, static_cast<webrtc::AudioLayers>(mMediaStack->settings()->audioLayer));
   }
   return 0;
}

int AudioImpl::shutdownPlaySoundDevicePool()
{
   mMainSdkThreadCheck.test();
 
   PlaySoundHelperMap::iterator it = mPlaySoundHelpers.begin();
   for (; it != mPlaySoundHelpers.end(); ++it)
   {
      if (it->second.get() != NULL)
      {
         it->second->detatchAudioImpl();
      }
   }
   
   mPlaySoundHelpers.clear();
   if (!mMediaStack->settings()->audioOutputDisabled)
   {
      mPlaySoundDevicePool.shutdown();
   }
   return 0;
}

AudioImpl::~AudioImpl()
{
   assert(mReactor.isCurrentThread());
   mReactor.safeDelete(mRecordingDeviceActivityTimer);

   if (mAudioLevelMonitor != NULL)
   {
      mAudioLevelMonitor->destroy();
   }

   shutdownPlaySoundDevicePool();

   mAudioDeviceChangeManager->removeInternalHandler(mAudioDeviceChangeImplHandle);
   AudioDeviceChangeManagerImpl* audioDeviceChangeManager = mAudioDeviceChangeManager;
   mAudioDeviceChangeManager = NULL;
   if (audioDeviceChangeManager != NULL)
   {
      audioDeviceChangeManager->reactorSafeRelease(&mReactor);
   }

   std::shared_ptr<MixerImpl> mixerImplPtr = std::dynamic_pointer_cast<MixerImpl>(mMediaStack->mixer());
   if (mixerImplPtr)
   {
      mixerImplPtr->unregisterMixerObserver(this);
   }
}

int AudioImpl::setHandler(AudioHandler* handler)
{
   mMediaHandler = handler;
   return kSuccess;
}

// NOTE: The list passed must contain all input and output devices!
static void ApplyHeuristics(cpc::vector<AudioDeviceInfo>& list)
{
   for (unsigned int i = 0; i < list.size(); i++)
   {
      AudioDeviceInfo& info = list[i];

      if (g_heuristic.ShouldBeExcluded(info.friendlyName, info.deviceType, list))
      {
         cpc::vector<AudioDeviceInfo>::iterator it =  list.begin();
         for (unsigned int j = 0; j < i; j++)
         {
            it++;
         }
         list.erase(it);
         i--;
      }
      else if (g_heuristic.IsBluetooth(info.friendlyName, list))
      {
         info.role = AudioDeviceRole_Bluetooth;
      }
      else if (g_heuristic.IsHeadset(info.friendlyName, list))
      {
         info.role = AudioDeviceRole_Headset;
      }
      else if (g_heuristic.IsSpeakerPhone(info.friendlyName, list))
      {
         info.role = AudioDeviceRole_SpeakerPhone;
      }
   }
}

// NOTE: The list passed must contain only input or only output devices!
static void MakeUnique(cpc::vector<AudioDeviceInfo>& list)
{
   for (unsigned int iDevice = 0; iDevice < list.size(); iDevice++)
   {
      AudioDeviceInfo& info = list[iDevice];

      for (unsigned int i = 0; i < iDevice; i++)
      {
         if (list[i].id == info.id && !info.defaultSystemDevice) // SCORE-1799 added: !info.defaultSystemDevice
         {
            info.id++;
            i = -1;     // start back at the beginning
         }
      }

      // cover the case where the name has trailing whitespace
      std::string name(info.friendlyName.c_str());
      boost::trim<std::string>(name);
      info.friendlyName = name.c_str();

      for (unsigned int i = 0; i < iDevice; i++)
      {
         if (list[i].friendlyName == info.friendlyName)
         {
            int last = 1;

            // check if the name already contains an appended number, and strip it if it does
            int j = name.rfind(' ');
            if (j != std::string::npos)
            {
               std::string suffix = name.substr(j+1);
               if (suffix.find_first_not_of("0123456789") == std::string::npos)
               {
                  last = resip::Data(suffix).convertInt();
                  info.friendlyName.resize(j);
               }
            }

            // append a new number to maybe make the name unique
            last++;
            char temp[128];
            snprintf(temp, 128, " %d", (int)last);
            info.friendlyName += temp;

            i = -1;     // start back at the beginning
         }
      }
   }
}

static bool GetDeviceLists(const webrtc_recon::MediaStackImpl* mediaStack, cpc::vector<AudioDeviceInfo>& playoutDeviceList, cpc::vector<AudioDeviceInfo>& recordingDeviceList, cpc::vector<AudioDeviceInfo>& audioDeviceList)
{
   int numPlayoutDevs = 0;
   int numRecordingDevs = 0;

   if (!mediaStack->isInitialized() || mediaStack->hardware()->GetNumOfPlayoutDevices(numPlayoutDevs) != 0 ||
       mediaStack->hardware()->GetNumOfRecordingDevices(numRecordingDevs) != 0)
      return false;

   playoutDeviceList.clear();
   for (int i=0; i<numPlayoutDevs; i++)
   {
      char devNameUtf8[128];
      char devGuidUtf8[128];
      if (mediaStack->hardware()->GetPlayoutDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
      {
         // NOTE: The device GUID is only available on Windows (only Vista and higher)
         AudioDeviceInfo devInfo;
         devInfo.role = AudioDeviceRole_None;
         devInfo.deviceType = MediaDeviceType_Render;
         devInfo.friendlyName = devNameUtf8;
         cpc::string devGuidWstr = devGuidUtf8;
         if(devGuidWstr.size() > 0)
         {
            size_t guidHash = cpc::hash(devGuidWstr);
            devInfo.id = guidHash;
         }
         else
         {
            size_t nameHash = cpc::hash(devInfo.friendlyName);
            devInfo.id = nameHash;
         }

         bool defaultPlayout = false;
         devInfo.defaultSystemDevice = false;
         if (mediaStack->hardware()->GetIsDefaultSystemPlayoutDevice(i, defaultPlayout) == 0)
         {
            devInfo.defaultSystemDevice = defaultPlayout;
         }

         bool defaultCommPlayout = false;
         devInfo.defaultSystemCommDevice = false;
         if (mediaStack->hardware()->GetIsDefaultSystemCommunicationPlayoutDevice(i, defaultCommPlayout) == 0)
         {
            devInfo.defaultSystemCommDevice = defaultCommPlayout;
         }

         devInfo.inadvisable = g_heuristic.IsInadvisableDevice(devInfo);

         char devHidUtf8[128];
         if (mediaStack->hardware()->GetPlayoutDeviceHid(i, devHidUtf8) == 0)
         {
            devInfo.hid = devHidUtf8;
         }

         // SCORE-1799
         if (devInfo.defaultSystemDevice && 0 < i && playoutDeviceList[0].defaultSystemDevice) {
            playoutDeviceList[0].id = devInfo.id;
         }

         playoutDeviceList.push_back(devInfo);
      }
   }

   recordingDeviceList.clear();
   for (int i=0; i<numRecordingDevs; i++)
   {
      char devNameUtf8[128];
      char devGuidUtf8[128];
      if (mediaStack->hardware()->GetRecordingDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
      {
         // NOTE: The device GUID is only available on Windows (only Vista and higher)
         AudioDeviceInfo devInfo;
         devInfo.role = AudioDeviceRole_None;
         devInfo.deviceType = MediaDeviceType_Capture;
         devInfo.friendlyName = devNameUtf8;
         cpc::string devGuidWstr = devGuidUtf8;
         if(devGuidWstr.size() > 0)
         {
            size_t guidHash = cpc::hash(devGuidWstr);
            devInfo.id = guidHash;
         }
         else
         {
            size_t nameHash = cpc::hash(devInfo.friendlyName);
            devInfo.id = nameHash;
         }

         bool defaultRecording = false;
         devInfo.defaultSystemDevice = false;
         if (mediaStack->hardware()->GetIsDefaultSystemRecordingDevice(i, defaultRecording) == 0)
         {
            devInfo.defaultSystemDevice = defaultRecording;
         }

         bool defaultCommRecording = false;
         devInfo.defaultSystemCommDevice = false;
         if (mediaStack->hardware()->GetIsDefaultSystemCommunicationRecordingDevice(i, defaultCommRecording) == 0)
         {
            devInfo.defaultSystemCommDevice = defaultCommRecording;
         }

         devInfo.inadvisable = g_heuristic.IsInadvisableDevice(devInfo);

         char devHidUtf8[128];
         if (mediaStack->hardware()->GetRecordingDeviceHid(i, devHidUtf8) == 0)
         {
            devInfo.hid = devHidUtf8;
         }

         recordingDeviceList.push_back(devInfo);
      }
   }

   // if there are duplicate devices the IDs and names may not be unique, and they must be
   // Note: this is basically for Mac since Windows seems to handle duplicate devices better
   // but it doesn't hurt to check on other OSes as well
   MakeUnique(playoutDeviceList);
   MakeUnique(recordingDeviceList);

   audioDeviceList = playoutDeviceList;
   audioDeviceList.insert(audioDeviceList.end(), recordingDeviceList.begin(), recordingDeviceList.end());

   // fill in some missing info and remove "bad" devices
   ApplyHeuristics(audioDeviceList);

   return true;
}

static bool SameLists(cpc::vector<AudioDeviceInfo>& list1, cpc::vector<AudioDeviceInfo>& list2)
{
   if (list1.size() != list2.size())
      return false;

   for (unsigned int iDevice = 0; iDevice < list1.size(); iDevice++)
   {
      AudioDeviceInfo& info1 = list1[iDevice];
      AudioDeviceInfo& info2 = list2[iDevice];

      if (info1.id != info2.id)
         return false;
   }

   return true;
}

// DRL This is a bad name - basically this method is called by the application layer to
// notify that the OS device list has changed, and therefore needs to be updated in the app.
int AudioImpl::queryDeviceList()
{
   bool noCaptureDevices = (mRecordingDeviceList.size() == 0);
   bool noRenderDevices = (mPlayoutDeviceList.size() == 0);
   cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList;
   if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, mAudioDeviceList))
   {
      mRecordingDeviceList = recordingDeviceList;
      mPlayoutDeviceList = playoutDeviceList;

      AudioDeviceListUpdatedEvent args;
      args.deviceInfo = mAudioDeviceList;
      mAudioInterface->fireAudioDeviceListUpdated(mMediaHandler, args);

      // Calling this on Android opens the audio device causing issues which prevent other apps from using the audio device and can increase battery drain
#if !defined(ANDROID)
      if (noCaptureDevices && recordingDeviceList.size() > 0 && mMediaStack->getRtpStreamCount() > 0)
      {
         // Start sending using newly added recording device
         mMediaStack->voe_base()->ReopenMicrophoneForAllChannels();
      }
      if (noRenderDevices && playoutDeviceList.size() > 0 && mMediaStack->getRtpStreamCount() > 0)
      {
         // Start playout using newly added device
         mMediaStack->voe_base()->ReopenPlayoutForAllChannels();
      }
#endif
   }
   return kSuccess;
}

int AudioImpl::playSound(PlaySoundHandle h, AudioDeviceRole device, int32_t audioUsage, const cpc::string& resourceUri, bool repeat)
{
#ifdef ANDROID
   // Don't use USAGE_VOICE_COMMUNICATION due to OBELISK-4669
   if (audioUsage == /* USAGE_VOICE_COMMUNICATION */ 2)
   {
      audioUsage = /* USAGE_VOICE_COMMUNICATION_SIGNALLING */ 3;
   }
#endif
   if (mPlaySoundHelpers.find(h) == mPlaySoundHelpers.end() &&
       // SABLE-158 Do not play the audio if audio device "None" is selected.
       (mDeviceRoleMap.find(device) == mDeviceRoleMap.end() || mDeviceRoleMap[device] != 0))
   {
      PlaySoundHelperMap::mapped_type psh = std::make_shared<PlaySoundHelper>(this, mReactor);
      mPlaySoundHelpers[h] = psh;

      int webrtcDeviceIdx = getWebRtcDeviceIndexForRenderDeviceRole(device);
#ifdef ANDROID
      if (kWebRtcNotFoundDeviceIndex == webrtcDeviceIdx && AudioDeviceRole_Ringing == device)
      {
         webrtcDeviceIdx = getWebRtcDeviceIndexForRenderDeviceRole(AudioDeviceRole_SpeakerPhone);
      }
#endif // ANDROID
      if (webrtcDeviceIdx == kWebRtcNotFoundDeviceIndex)
      {
#ifdef _WIN32
         webrtcDeviceIdx = kWebRtcDefaultSystemCommDeviceIndex;
         WarningLog(<< "Couldn't find associated device for role " << device << ". Using system default comm device");
#else
         webrtcDeviceIdx = kWebRtcDefaultSystemDeviceIndex;
         WarningLog(<< "Couldn't find associated device for role " << device << ". Using system default device");
#endif
      }

      if (psh->playSound(h, webrtcDeviceIdx, audioUsage, resourceUri, repeat) != 0)
      {
         ErrLog(<< "psh->playSound(..) failed: " << h << ", " << webrtcDeviceIdx << ", " << audioUsage << ", " << resourceUri << ", " << repeat);
         mPlaySoundHelpers.erase(h);
      }
   }

   SoundProperties currentSound;
   currentSound.h = h;
   currentSound.role = device;
   currentSound.audioUsage = audioUsage;
   currentSound.resourceUri = resourceUri;
   currentSound.repeat = repeat;
   mActiveSoundsMap.insert(std::pair<AudioDeviceRole, SoundProperties>(device, currentSound));

   return kSuccess;
}

int AudioImpl::stopPlaySound(PlaySoundHandle h)
{
   ActiveSoundsMap::iterator soundIt = mActiveSoundsMap.begin();
   for (; soundIt != mActiveSoundsMap.end(); soundIt++)
   {
      if (soundIt->second.h == h)
      {
         mActiveSoundsMap.erase(soundIt);
         break;
      }
   }
   PlaySoundHelperMap::const_iterator it = mPlaySoundHelpers.find(h);
   if (it != mPlaySoundHelpers.end())
   {
      it->second->stop();
      it->second->OnLocallyPlayingFileWithHandleFinished(h);
   }
   return kSuccess;
}

void AudioImpl::stopAllSounds()
{
   PlaySoundHelperMap playSoundHelpersCopy = mPlaySoundHelpers;
   PlaySoundHelperMap::const_iterator it = playSoundHelpersCopy.begin();;
   for (; it != playSoundHelpersCopy.end(); ++it)
   {
      it->second->stop();
      it->second->OnLocallyPlayingFileWithHandleFinished(it->first);
   }
}

bool AudioImpl::isPlayingSound() const
{
   return mPlaySoundHelpers.size() > 0;
}

// DRL FIXIT: This is duplicated in AudioCompatibility.xpp but I added it here to get around a Linx only linker problem.
static jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo convert(const ::CPCAPI2::Media::AudioCodecInfo& codecInfo)
{
   jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo result;

   result.codecName = codecInfo.codecName;
   result.id = codecInfo.id;
   result.enabled = codecInfo.enabled;
   result.samplingRate = codecInfo.samplingRate;
   result.minBandwidth = codecInfo.minBandwidth;
   result.maxBandwidth = codecInfo.maxBandwidth;
   result.priority = codecInfo.priority;
   result.payloadType = codecInfo.payloadType;

   return result;
}

static cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo> convert(cpc::vector<AudioCodecInfo> input)
{
   cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo> result;

   for (cpc::vector<AudioCodecInfo>::const_iterator it = input.begin(); it != input.end(); ++it)
   {
      result.push_back(convert(*it));
   }

   return result;
}

int AudioImpl::queryCodecList()
{
   if (mMediaStack->isInitialized())
   {
      mAudioCodecList.clear();
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      CodecFactoryImpl::Codecs::const_iterator itAudioCodecs = audioCodecs.begin();
      for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         std::shared_ptr<CpsiCodec> c = *itAudioCodecs;

         if (resip::isEqualNoCase(c->settings().payload_name, "telephone-event"))
         {
            continue;
         }

         if (c->licenses_total > 0)
         {
            AudioCodecInfo info;

            char samplingRate[16];
            snprintf(samplingRate, 16, "%d", c->settings().webrtcCodecInfo.audio.plfreq );

            cpc::string id_str(c->settings().payload_name.c_str());
            id_str.append(samplingRate);
            info.id = cpc::hash(id_str);

            info.enabled = c->enabled;
            info.priority = c->priority;
            if (c->display_name.empty())
            {
               info.codecName = c->settings().payload_name.c_str();
            }
            else
            {
               info.codecName = c->display_name.c_str();
            }
            info.samplingRate = c->settings().webrtcCodecInfo.audio.plfreq;
            info.minBandwidth = c->min_bandwidth;
            info.maxBandwidth = c->max_bandwidth;
            info.payloadType = c->settings().local_payload_type;
            mAudioCodecList.push_back(info);
         }
      }

      if (mMediaHandler != NULL)
      {
         AudioCodecListUpdatedEvent args;
         args.codecInfo = mAudioCodecList;
         mAudioInterface->fireAudioCodecListUpdated(mMediaHandler, args);
      }
   }
   return kSuccess;
}

int AudioImpl::setCaptureDevice(unsigned int deviceId, AudioDeviceRole role)
{
   // special case for "None" device since it won't be in our list
   if (deviceId == 0)
   {
      return kSuccess;
   }
   else if (isCpcapiPredefinedAudioDeviceId(deviceId))
   {
      DebugLog(<< "AudioImpl::setCaptureDevice " << deviceId << " (predefined)");

      mMediaStack->hardware()->SetRecordingDevice(cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(deviceId));
      return kSuccess;
   }

   // get a new device list in case it has changed and we haven't yet received the notification
   cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList, audioDeviceList;
   if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, audioDeviceList))
   {
      int i = 0;
      for (cpc::vector<AudioDeviceInfo>::const_iterator di =  recordingDeviceList.begin(); di != recordingDeviceList.end(); ++di, i++)
      {
         if (di->id == deviceId)
         {
            DebugLog(<< "AudioImpl::setCaptureDevice " << deviceId << " (" << di->friendlyName << ")");

            mMediaStack->hardware()->SetRecordingDevice(i);
            return kSuccess;
         }
      }
      //assert(!SameLists(audioDeviceList, mAudioDeviceList));   // assume the device list has changed if we can't find it
   }

   return kError;
}

int AudioImpl::getWebRtcDeviceIndexForRenderDeviceRole(AudioDeviceRole role) const
{
   DeviceRoleMap::const_iterator it = mDeviceRoleMap.find(role);
   if (it == mDeviceRoleMap.end())
   {
#if defined(ANDROID)
      cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList, audioDeviceList;
      if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, audioDeviceList))
      {
        int i = 0;
         for (cpc::vector<AudioDeviceInfo>::const_iterator di =  audioDeviceList.begin(); di != audioDeviceList.end(); ++di, i++)
         {
            if (di->role == role)
            {
               return i;
            }
         }
      }
#endif
      return kWebRtcNotFoundDeviceIndex;
   }

   unsigned int deviceId = it->second;
   if (deviceId == 0)
      return kWebRtcNotFoundDeviceIndex;

   return getWebRtcDeviceIndexForRenderCpcapiDeviceId(deviceId);
}

int AudioImpl::getWebRtcDeviceIndexForRenderCpcapiDeviceId(int cpcapiDeviceId) const
{
   if (isCpcapiPredefinedAudioDeviceId(cpcapiDeviceId))
   {
      return cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(cpcapiDeviceId);
   }
   else
   {
      cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList, audioDeviceList;
      if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, audioDeviceList))
      {
         int i = 0;
         for (cpc::vector<AudioDeviceInfo>::const_iterator di = playoutDeviceList.begin(); di != playoutDeviceList.end(); ++di, i++)
         {
            if (di->id == cpcapiDeviceId)
            {
               return i;
            }
         }
      }
   }

   return kWebRtcNotFoundDeviceIndex;
}

int AudioImpl::getWebRtcDeviceIndexForCaptureCpcapiDeviceId(int cpcapiDeviceId) const
{
   if (isCpcapiPredefinedAudioDeviceId(cpcapiDeviceId))
   {
      return cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(cpcapiDeviceId);
   }
   else
   {
      cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList, audioDeviceList;
      if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, audioDeviceList))
      {
         int i = 0;
         for (cpc::vector<AudioDeviceInfo>::const_iterator di = recordingDeviceList.begin(); di != recordingDeviceList.end(); ++di, i++)
         {
            if (di->id == cpcapiDeviceId)
            {
               return i;
            }
         }
      }
   }

   return kWebRtcNotFoundDeviceIndex;
}

int AudioImpl::setRenderDevice(unsigned int deviceId, AudioDeviceRole role)
{
   int retVal = kError;
   bool isPlaying = isPlayingSound();
   SoundProperties currentSound;
   if (isPlaying)
   {
      ActiveSoundsMap::const_iterator it = mActiveSoundsMap.find(role);
      if (it != mActiveSoundsMap.end())
      {
        currentSound = it->second;
        stopPlaySound(currentSound.h);
      }
      else
      {
         isPlaying = false;
      }
   }

   retVal = setRenderDeviceImpl(deviceId, role);

   if (isPlaying)
   {
      playSound(currentSound.h, currentSound.role, currentSound.audioUsage, currentSound.resourceUri, currentSound.repeat);
   }
   return retVal;
}

int AudioImpl::setRenderDeviceImpl(unsigned int deviceId, AudioDeviceRole role)
{
   // special case for "None" device since it won't be in our list
   if (deviceId == 0)
   {
      if(mDeviceRoleMap.find(role) == mDeviceRoleMap.end())
      {
         mDeviceRoleMap.insert(std::pair<AudioDeviceRole,unsigned int>(role, deviceId));
      }
      else
      {
         mDeviceRoleMap[role] = deviceId;
      }
      return kSuccess;
   }
   else if (isCpcapiPredefinedAudioDeviceId(deviceId))
   {
      DebugLog(<< "AudioImpl::setRenderDeviceImpl " << deviceId << " (predefined)");
      mMediaStack->hardware()->SetPlayoutDevice(cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(deviceId));

      if(mDeviceRoleMap.find(role) == mDeviceRoleMap.end())
      {
         mDeviceRoleMap.insert(std::pair<AudioDeviceRole,unsigned int>(role, deviceId));
      }
      else
      {
         mDeviceRoleMap[role] = deviceId;
      }

      return kSuccess;
   }

   // TODO could refactor this method to use getWebRtcDeviceIndexForRenderCpcapiDeviceId

   // get a new device list in case it has changed and we haven't yet received the notification
   cpc::vector<AudioDeviceInfo> playoutDeviceList, recordingDeviceList, audioDeviceList;
   if (GetDeviceLists(mMediaStack, playoutDeviceList, recordingDeviceList, audioDeviceList))
   {
      int i = 0;
      for (cpc::vector<AudioDeviceInfo>::const_iterator di =  playoutDeviceList.begin(); di != playoutDeviceList.end(); ++di, i++)
      {
         if (di->id == deviceId)
         {
            DebugLog(<< "AudioImpl::setRenderDeviceImpl " << deviceId << " (" << di->friendlyName << ")");
            mMediaStack->hardware()->SetPlayoutDevice(i);
            if(mDeviceRoleMap.find(role) == mDeviceRoleMap.end())
            {
               mDeviceRoleMap.insert(std::pair<AudioDeviceRole,unsigned int>(role, deviceId));
            }
            else
            {
               mDeviceRoleMap[role] = deviceId;
            }
            return kSuccess;
         }
      }
      assert(!SameLists(audioDeviceList, mAudioDeviceList));   // assume the device list has changed if we can't find it
   }

   return kError;
}

int AudioImpl::setCodecEnabled(unsigned int id, bool enabled)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<AudioCodecInfo>::iterator ci =  mAudioCodecList.begin(); ci != mAudioCodecList.end(); ++ci)
   {
      if (ci->id == id)
      {
         ci->enabled = enabled;
         std::shared_ptr<CpsiCodec> c = codecFactory->getAudioCodecByDisplayName((ci->codecName).c_str());
         if (c)
         {
            c->enabled = enabled;
         }
      }
   }
   return kSuccess;
}

int AudioImpl::setCodecPriority(unsigned int id, unsigned int priority)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<AudioCodecInfo>::const_iterator ci =  mAudioCodecList.begin(); ci != mAudioCodecList.end(); ++ci)
   {
      if (ci->id == id)
      {
         std::shared_ptr<CpsiCodec> c = codecFactory->getAudioCodecByDisplayName(ci->codecName.c_str());
         if (c)
         {
            c->priority = priority;
         }
         break;
      }
   }
   return kSuccess;
}

int AudioImpl::setCodecPayloadType(unsigned int id, unsigned int payloadType)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<AudioCodecInfo>::const_iterator ci =  mAudioCodecList.begin(); ci != mAudioCodecList.end(); ++ci)
   {
      if (ci->id == id)
      {
         std::shared_ptr<CpsiCodec> c = codecFactory->getAudioCodecByDisplayName(ci->codecName.c_str());
         if (c)
         {
            c->overridePayloadType(payloadType);
         }
         break;
      }
   }
   return kSuccess;
}

int AudioImpl::setTelephoneEventPayloadType(unsigned int payloadType)
{
   if (mMediaStack->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      for (CodecFactoryImpl::Codecs::const_iterator itAudioCodecs = audioCodecs.begin(); itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         if (resip::isEqualNoCase((*itAudioCodecs)->settings().payload_name, "telephone-event"))
         {
            (*itAudioCodecs)->overridePayloadType(payloadType);
         }
      }
   }
   return kSuccess;
}

void AudioImpl::postCallback(ReadCallbackBase* fp)
{
   mAudioInterface->postCallback(fp);
}

int AudioImpl::setMicMute(bool enabled)
{
   InfoLog(<< "AudioImpl::setMicMute(" << enabled << ")");
   if (mMediaStack->volume_control()->SetInputMute(-1, enabled) != 0)
   {
      assert(0);
      // not muting when we want to is a serious bad; take drastic action
      mMediaStack->shutdownMediaStack();
   }
   return kSuccess;
}

int AudioImpl::setSpeakerMute(bool enabled)
{
   mMediaStack->hardware()->SetLoudspeakerStatus(!enabled);
   return kSuccess;
}

int AudioImpl::setMicVolume(unsigned int level)
{
   unsigned int vol = (unsigned int)(((float)level / 100.0f) * 255.0f);
   if (mMediaStack->volume_control()->SetMicVolume(vol) != 0)
   {
      return kError;
   }
   return kSuccess;
}

int AudioImpl::setMicSoftwareVolume( bool enabled, unsigned int level )
{
   // What this math does: interpret the level from 0..100 as being from 0..1.4
   // on the float scale. What this means is that a level of "100" will
   // actually mean a GAIN of 40% to the audio samples (allowing some software
   // ability to raise the volume, but not too much). The idea is to provide
   // MOSTLY the ability to attenuate the audio, but some ability to gain.
   //
   // As a result of this formula, "no gain" is somewhere around 70.
   //
   if( level > 100 )
      level = 100;

   float gain = ((( float ) level) * 1.4f / 100.0f );
   InfoLog(<< "AudioImpl::setMicSoftwareMute(" << enabled << ", " << gain << " )");
   if (mMediaStack->hardware()->SetMicSoftwareVolume( enabled, gain ) != 0)
   {
      assert(0);
      // not muting when we want to is a serious bad; take drastic action
      mMediaStack->shutdownMediaStack();
   }
   return kSuccess;
}

int AudioImpl::setSpeakerVolume(unsigned int level)
{
   unsigned int vol = (unsigned int)(((float)level / 100.0f) * 255.0f);
   if (mMediaStack->volume_control()->SetSpeakerVolume(vol) != 0)
   {
      return kError;
   }
   return kSuccess;
}

int AudioImpl::queryDeviceVolume()
{
   if (mMediaStack->isInitialized())
   {
      bool enabled = false;
      AudioDeviceVolumeEvent args;
      args.micMuted = false;
      args.micVolumeLevel = 50;
      args.speakerMuted = false;
      args.speakerVolumeLevel = 50;

      if (mMediaStack->volume_control()->GetInputMute(-1, enabled) == 0)
      {
         args.micMuted = enabled;
      }

      if (mMediaStack->hardware()->GetLoudspeakerStatus(enabled) == 0)
      {
         args.speakerMuted = !enabled;
      }

      unsigned int vol = 0;
      if (mMediaStack->volume_control()->GetSpeakerVolume(vol) == 0)
      {
         args.speakerVolumeLevel = (unsigned int)(((float)vol / 255.0f) * 100.0f);
      }

      if (mMediaStack->volume_control()->GetMicVolume(vol) == 0)
      {
         args.micVolumeLevel = (unsigned int)(((float)vol / 255.0f) * 100.0f);
      }

      mAudioInterface->fireAudioDeviceVolume(mMediaHandler, args);
      return kSuccess;
   }
   return kError;
}

int AudioImpl::setEchoCancellationMode(AudioDeviceRole role, EchoCancellationMode mode)
{
   InfoLog(<< "setEchoCancellationMode(" << role << ", " << mode << ")");
   mMediaStack->updateEcSettings(mode);
   return kSuccess;
}

int AudioImpl::setNoiseSuppressionMode(AudioDeviceRole role, NoiseSuppressionMode mode)
{
   InfoLog(<< "setNoiseSuppressionMode(" << role << ", " << mode << ")");
   mMediaStack->updateNsSettings(mode);
   return kSuccess;
}

int AudioImpl::setVadMode(AudioDeviceRole role, VadMode mode)
{
   switch (mode)
   {
      case VadMode_None:
         mMediaStack->setVADEnabled(false);
         break;
      case VadMode_Conventional:
         mMediaStack->setVADEnabled(true, webrtc::kVadConventional);
         break;
      case VadMode_AggressiveLow:
         mMediaStack->setVADEnabled(true, webrtc::kVadAggressiveLow);
         break;
      case VadMode_AggressiveMid:
         mMediaStack->setVADEnabled(true, webrtc::kVadAggressiveMid);
         break;
      case VadMode_AggressiveHigh:
         mMediaStack->setVADEnabled(true, webrtc::kVadAggressiveHigh);
         break;
      default:
         return kError;
   }
   return kSuccess;
}

int AudioImpl::setGainSettings(const GainSettings& settings)
{
   // WebRTC offers way too much control of the gain / boost settings;
   // AGC has proven very problematic, and in general we do NOT want to
   // apply it because it is actually harmful to the overall quality of the
   // audio when used e.g. with a headset.
   // SO currently we only look at a subset of GainSettings; specifically
   // we only apply fixed gain for Rx and Tx.
   // We will refactor this in the SDK public interface in the future
   // so that adaptive gain can be used for speakerphone mode.

   if (settings.rxConfig.mode == GainMode_Fixed)
   {
      mMediaStack->applyFixedRxGain(true);
   }
   else if (settings.rxConfig.mode == GainMode_None)
   {
      mMediaStack->applyFixedRxGain(false);
   }

   if (settings.txConfig.mode == GainMode_Fixed)
   {
      mMediaStack->applyFixedTxGain(true, settings.txConfig.targetLeveldB, settings.txConfig.compressionGaindB);
   }
   else if (settings.txConfig.mode == GainMode_None)
   {
      mMediaStack->applyFixedTxGain(false, -1, -1);
   }

   return kSuccess;
}

int AudioImpl::setAudioDscp(unsigned int mediaDscp)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.mediaDscp = mediaDscp;
   mMediaStack->updateMediaSettings(settings);
   return kSuccess;
}

void AudioImpl::handlePlaySoundComplete(PlaySoundHandle psh)
{
   if (mMediaHandler != NULL)
   {
      mAudioInterface->firePlaySoundComplete(mMediaHandler, psh);
   }

   mPlaySoundHelpers.erase(psh);
}

void AudioImpl::handlePlaySoundFailure(PlaySoundHandle psh)
{
   if (mMediaHandler != NULL)
   {
      mAudioInterface->firePlaySoundFailure(mMediaHandler, psh);
   }

   mPlaySoundHelpers.erase(psh);
}

void AudioImpl::OnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle)
{
   mReactor.post(resip::resip_bind(&AudioImpl::ReactorOnLocallyPlayingFileWithHandleFinished, this, handle));
}

void AudioImpl::ReactorOnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle)
{
   PlaySoundHelperMap::iterator it = mPlaySoundHelpers.find(handle);
   if (it != mPlaySoundHelpers.end())
   {
      it->second->OnLocallyPlayingFileWithHandleFinished(handle);
   }
}

int AudioImpl::startMonitoringCaptureDeviceLevelsImpl(unsigned int deviceId)
{
   if (mAudioLevelMonitor == NULL)
   {
	   mAudioLevelMonitor = new AudioLevelMonitor();
      mAudioLevelMonitor->start(this, mAudioInterface);
   }

   int webrtcIndex = getWebRtcDeviceIndexForCaptureCpcapiDeviceId(deviceId);
   if (webrtcIndex != kWebRtcNotFoundDeviceIndex)
   {
      mAudioLevelMonitor->startInputLevelMonitoring(webrtcIndex);
      mReportingInputAudioLevels = true;
   }
   else
   {
      ErrLog(<< "startMonitoringCaptureDeviceLevelsImpl couldn't find deviceId");
   }

	return kSuccess;
}

int AudioImpl::stopMonitoringCaptureDeviceLevelsImpl()
{
   if (mAudioLevelMonitor != NULL)
   {
      mReportingInputAudioLevels = false;
      mAudioLevelMonitor->stopInputLevelMonitoring();
      if (!mReportingOutputAudioLevels)
      {
         mAudioLevelMonitor->destroy();
         mAudioLevelMonitor = NULL;
      }
   }
	return kSuccess;
}

int AudioImpl::startMonitoringRenderDeviceLevelsImpl(unsigned int deviceId, const cpc::string& resourceUri)
{
   if (mAudioLevelMonitor == NULL)
   {
	   mAudioLevelMonitor = new AudioLevelMonitor();
      mAudioLevelMonitor->start(this, mAudioInterface);
   }

   int webrtcIndex = getWebRtcDeviceIndexForRenderCpcapiDeviceId(deviceId);
   if (webrtcIndex != kWebRtcNotFoundDeviceIndex)
   {
      mAudioLevelMonitor->startOutputLevelMonitoring(webrtcIndex, resourceUri.c_str());
      mReportingOutputAudioLevels = true;
   }
   else
   {
      ErrLog(<< "startMonitoringRenderDeviceLevelsImpl couldn't find deviceId");
   }

	return kSuccess;
}

int AudioImpl::stopMonitoringRenderDeviceLevelsImpl()
{
   if (mAudioLevelMonitor != NULL)
   {
      mReportingOutputAudioLevels = false;
      mAudioLevelMonitor->stopOutputLevelMonitoring();
      if (!mReportingInputAudioLevels)
      {
         mAudioLevelMonitor->destroy();
         mAudioLevelMonitor = NULL;
      }
   }
	return kSuccess;
}

void AudioImpl::onAudioLevels(int /*voe_channel*/, unsigned int inputLevel, unsigned int outputLevel)
{
   AudioDeviceLevelChangeEvent args;
   args.inputDeviceLevel = inputLevel;
   args.outputDeviceLevel = outputLevel;
   mAudioInterface->fireAudioDeviceLevelChange(mMediaHandler, args);
}

void AudioImpl::onSystemAudioServiceError(int errorLevel)
{
   SystemAudioServiceErrorEvent args;
   args.errorLevel = (SystemAudioServiceErrorLevel)errorLevel;
   postCallback(makeFpCommand1(AudioHandler::onSystemAudioServiceError, mMediaHandler, args));
}

void AudioImpl::updatePerformanceProfile(DevicePerformanceProfile perfProfile)
{
   if (mMediaStack->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      CodecFactoryImpl::Codecs::const_iterator itAudioCodecs = audioCodecs.begin();
      for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         std::shared_ptr<CpsiCodec> c = *itAudioCodecs;
         if (c->settings().payload_name == "opus")
         {
            if (perfProfile == DevicePerformanceProfile_Mobile)
            {
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.OPUS.complexitySpecified = true;
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.OPUS.complexity = 5;
            }
            else if (perfProfile == DevicePerformanceProfile_SlowMobile)
            {
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.OPUS.complexitySpecified = true;
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.OPUS.complexity = 2;
            }
         }
         else if (c->settings().payload_name == "SILK")
         {
            if (perfProfile == DevicePerformanceProfile_Mobile)
            {
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.SILK.complexitySpecified = true;
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.SILK.complexity = 1;
            }
            else if (perfProfile == DevicePerformanceProfile_SlowMobile)
            {
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.SILK.complexitySpecified = true;
               c->allSettings()[0].webrtcCodecInfo.audio.codecSpecific.SILK.complexity = 0;
            }
         }
      }
   }
}

int AudioImpl::setHardwareEchoCancellationEnabled(const bool enabled)
{
   if (mMediaStack->isInitialized())
   {
      mMediaStack->blacklistHardwareEc(!enabled);
      if (enabled)
         mMediaStack->updateEcSettings(EchoCancellationMode::EchoCancellationMode_NativeAEC);
      else
         mMediaStack->setEcSettings();
   }
   else
   {
      WarningLog(<< "setHardwareEchoCancellationEnabled called before media stack initialization!");
   }
   return kSuccess;
}

int AudioImpl::setHardwareAutomaticGainControlEnabled(const bool enabled)
{
  if (mMediaStack->isInitialized())
  {
    mMediaStack->blacklistHardwareAgc(!enabled);
    mMediaStack->setAgcSettings();
  }
  else
  {
     WarningLog(<< "setHardwareAutomaticGainControlEnabled called before media stack initialization!");
  }
  return kSuccess;
}

int AudioImpl::setHardwareNoiseSuppressionEnabled(const bool enabled)
{
   if (mMediaStack->isInitialized())
   {
      mMediaStack->blacklistHardwareNs(!enabled);
      if (enabled)
         mMediaStack->updateNsSettings(NoiseSuppressionMode::NoiseSuppressionMode_NativeNS);
      else
         mMediaStack->setNsSettings();
   }
   else
   {
      WarningLog(<< "setHardwareNoiseSuppressionEnabled called before media stack initialization!");
   }
   return kSuccess;
}

int AudioImpl::setLowLatencyPlayoutEnabled(const bool enabled)
{
   if (mMediaStack->isInitialized())
   {
      if (mMediaStack->hardware()->GetLowLatencyPlayoutEnabled() == enabled)
        return kSuccess;

      if (mMediaStack->hardware()->SetLowLatencyPlayoutEnabled(enabled) != 0)
         return kError;

      // restart media stack to re-init audio devices
      mMediaStack->restart();
   }
   else
   {
      WarningLog(<< "setLowLatencyPlayoutEnabled called before media stack initialization!");
   }

   return kSuccess;
}

int AudioImpl::setLowLatencyAudioTrackEnabled(const bool enabled)
{
   if (mMediaStack->isInitialized())
   {
      if (mMediaStack->hardware()->GetLowLatencyAudioTrackEnabled() == enabled)
        return kSuccess;

      if (mMediaStack->hardware()->SetLowLatencyAudioTrackEnabled(enabled) != 0)
         return kError;

      // restart media stack to re-init audio devices
      mMediaStack->restart();
   }
   else
   {
      WarningLog(<< "setLowLatencyAudioTrackEnabled called before media stack initialization!");
   }

   return kSuccess;
}

int AudioImpl::setAudioSource(int audioSource)
{
   webrtc::AudioDeviceModule* adm = mMediaStack->voe_base()->audio_device();
   return adm->SetAudioSource(audioSource);
}

int AudioImpl::connectAudioStreams(int recvStreamId, int sendStreamId)
{
   DebugLog(<< "AudioImpl::connectAudioStreams(): recvStreamId: " << recvStreamId << " sendStreamId: " << sendStreamId);
   mMediaStack->conference()->AddToPassthruConference(recvStreamId, sendStreamId);
   return 0;
}

int AudioImpl::disconnectAudioStreams(int recvStreamId, int sendStreamId)
{
   DebugLog(<< "AudioImpl::disconnectAudioStreams(): recvStreamId: " << recvStreamId << " sendStreamId: " << sendStreamId);
   mMediaStack->conference()->RemoveFromPassthruConference(recvStreamId, sendStreamId);
   return 0;
}

int AudioImpl::activatePlayAndRecordMode(bool activate)
{
   webrtc::AudioDeviceModule* adm = mMediaStack->voe_base()->audio_device();
   adm->ActivatePlayAndRecordMode(activate);
   return 0;
}

int AudioImpl::setAudioSessionActivated(bool activated)
{
   webrtc::AudioDeviceModule* adm = mMediaStack->voe_base()->audio_device();
   adm->AudioSessionActivated(activated);
   return 0;
}

int AudioImpl::setUseQosFastlane(bool useQosFastlane, bool useWithDscp)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.useAudioQosFastlane = useQosFastlane;
   settings.useAudioQosFastlaneWithDscp = useQosFastlane ? useWithDscp : false;
   mMediaStack->updateMediaSettings(settings);
   return kSuccess;
}

int AudioImpl::setAudioInterruptionsHandlingEnabled(bool enabled)
{
   webrtc::AudioDeviceModule* adm = mMediaStack->voe_base()->audio_device();
   adm->SetAudioInterruptionsHandlingEnabled(enabled);
   return 0;
}

int AudioImpl::setAudioDeviceFile(const cpc::string& inputAudiofileNameUtf8, const cpc::string& outputAudiofileNameUtf8)
{
   webrtc::FileAudioDeviceFactory::SetFilenamesToUse(inputAudiofileNameUtf8.c_str(), outputAudiofileNameUtf8.c_str());
   return 0;
}

int AudioImpl::setMicAGCEnabled(bool enabled)
{
   mMediaStack->voe_audio_processing()->SetAgcStatus(enabled);
   return 0;
}

AudioInterface* AudioImpl::audioInterface()
{
   return mAudioInterface;
}

int AudioImpl::onAudioDefaultDeviceChange(AudioDeviceType deviceType)
{
   // the WebRTC audio device modules do not handle the case where either capture
   // or playout is set as the default audio device, and the default audio device
   // is changed via OS settings.
   // handling this in the ADMs would be difficult as start/stop operations should
   // happen in the current PhoneInterface thread.

   // calling SetRecordingDevice/SetPlayoutDevice will cause recording/playout to be restarted,
   // and the ADM will use the new device.

   if (mMediaStack->hardware())
   {
      if (deviceType == MediaDeviceType_Render)
      {
         int webrtcPlayoutIndex;
         if (mMediaStack->hardware()->GetPlayoutDeviceIndex(webrtcPlayoutIndex) != 0)
         {
            return kError;
         }

         if (webrtcPlayoutIndex == kWebRtcDefaultSystemDeviceIndex)
         {
            DebugLog(<< "using default system device for render, and it changed; restarting audio");
            mMediaStack->hardware()->SetPlayoutDevice(kWebRtcDefaultSystemDeviceIndex);
         }

   #if _WIN32
         if (webrtcPlayoutIndex == kWebRtcDefaultSystemCommDeviceIndex)
         {
            DebugLog(<< "using default system comm device for render, and it changed; restarting audio");
            mMediaStack->hardware()->SetPlayoutDevice(kWebRtcDefaultSystemCommDeviceIndex);
         }
   #endif
      }

      if (deviceType == MediaDeviceType_Capture)
      {
         int webrtcRecordingIndex;
         if (mMediaStack->hardware()->GetRecordingDeviceIndex(webrtcRecordingIndex) != 0)
         {
            return kError;
         }

         if (webrtcRecordingIndex == kWebRtcDefaultSystemDeviceIndex)
         {
            DebugLog(<< "using default system device for capture, and it changed; restarting audio");
            mMediaStack->hardware()->SetRecordingDevice(kWebRtcDefaultSystemDeviceIndex);
         }

   #if _WIN32
         if (webrtcRecordingIndex == kWebRtcDefaultSystemCommDeviceIndex)
         {
            DebugLog(<< "using default system comm device for capture, and it changed; restarting audio");
            mMediaStack->hardware()->SetRecordingDevice(kWebRtcDefaultSystemCommDeviceIndex);
         }
   #endif
      }
   }
   queryDeviceList();

   return 0;
}

int AudioImpl::onAudioDeviceChange()
{
   DebugLog(<< "device add/remove detected (via internal detection)");
   queryDeviceList();

   return 0;
}

bool AudioImpl::isCpcapiPredefinedAudioDeviceId(int cpcapiDeviceId) const
{
   switch (cpcapiDeviceId)
   {
      case kAudioDefaultSystemDeviceId:
      case kAudioDefaultSystemCommDeviceId:
      case kAudioLoopbackDeviceId:
         return true;
      default:
         return false;
   }
}

int AudioImpl::cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(unsigned int cpcapiDeviceId) const
{
   switch (cpcapiDeviceId)
   {
      case kAudioDefaultSystemCommDeviceId:
         return -1;
      case kAudioDefaultSystemDeviceId:
         return -2;
      case kAudioLoopbackDeviceId:
         return -3;
      default:
         ErrLog(<< "Invalid device id");
         return 0;
   }
}

void AudioImpl::onRtpStreamAdded(const std::shared_ptr<recon::RtpStream>& ms)
{
   AudioStreamStartedEvent evt;
   evt.streamId = std::dynamic_pointer_cast<RtpStreamImpl>(ms)->channel();
   mAudioInterface->fireAudioStreamStarted(mMediaHandler, evt);
}

void AudioImpl::onRtpStreamRemoved(const std::shared_ptr<recon::RtpStream>& ms)
{
   AudioStreamStoppedEvent evt;
   evt.streamId = std::dynamic_pointer_cast<RtpStreamImpl>(ms)->channel();
   mAudioInterface->fireAudioStreamStopped(mMediaHandler, evt);
}

void AudioImpl::onTimer(unsigned short timerId, void* appState)
{

   // OBELISK-6205: code needs to be updated such that the timer does not
   // continuously fire when no audio session is active to avoid battery
   // drain on mobile devices.
#if 0
   switch (timerId)
   {
   case RecordingDeviceActivityTimerIndex:
      if (mMediaStack == NULL) goto next;
      if (!mMediaStack->isInitialized()) goto next;
      if (mMediaStack->voe_base() == NULL) goto next;

      {
         webrtc::AudioDeviceModule* adm = mMediaStack->voe_base()->audio_device();

         if (adm == NULL)
         {
            WarningLog(<< "AudioImpl::onTimer() adm=NULL");
            break;
         }

         bool active = adm->Recording() && adm->IsRecordingDeviceActive(CheckRecordingDeviceActivityIntervalMS);

         uint16_t index;
         char devNameUtf8[webrtc::kAdmMaxDeviceNameSize];
         char devGuidUtf8[webrtc::kAdmMaxGuidSize];

         if (adm->GetRecordingDeviceIndex(&index) != 0 || adm->RecordingDeviceName(index, devNameUtf8, devGuidUtf8) != 0)
         {
            DebugLog(<< "adm=" << adm << " recording=" << adm->Recording() << " active=" << active << " previous=" << mIsRecordingDeviceActive);
         }
         else
         {
            DebugLog(<< "device=" << devNameUtf8 << " deviceID=" << devGuidUtf8 << " recording=" << adm->Recording() << " active=" << active << " previous=" << mIsRecordingDeviceActive);
         }

         if (adm->Recording() && mIsRecordingDeviceActive != active)
         {
            mAudioInterface->onSystemAudioServiceError(active ? Media::SystemAudioServiceErrorLevel_ActiveRecording : Media::SystemAudioServiceErrorLevel_InactiveRecording);
         }

         mIsRecordingDeviceActive = active;
      }

next:
      mRecordingDeviceActivityTimer->expires_from_now(CheckRecordingDeviceActivityIntervalMS);
      mRecordingDeviceActivityTimer->async_wait(this, RecordingDeviceActivityTimerIndex, NULL);

      break;

   default:
      assert(false);
      break;
   }
#endif
}

}
}
#endif // CPCAPI2_BRAND_MEDIA_MODULE
