#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && defined(TARGET_OS_IPHONE) && (TARGET_OS_IPHONE == 1)

#include "PlaySoundHelper.h"
#import <AVFoundation/AVFoundation.h>

#include "../util/resource_to_stream.h"
#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"
#include "../util/dtmf_tone_helper.h"
#include "../util/cpc_logger.h"
#include "AudioImpl.h"
#include <MediaStackImpl.hxx>

#include <voe_base.h>
#include <voe_dtmf.h>

#include <rutil/Data.hxx>
#include <rutil/Log.hxx>

#include <future>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

@interface PlaySoundHelperImpl: NSObject<AVAudioPlayerDelegate>
{
   AVAudioPlayer *_player;
   CPCAPI2::Media::AudioImpl *_audioImpl;
}

@property(nonatomic) CPCAPI2::Media::PlaySoundHandle handle;

- (id) initWithAudio:(CPCAPI2::Media::AudioImpl*)audio;
- (BOOL) playSoundFile:(NSString*)filePath repeat:(BOOL)repeat;
- (void) stop;

@end


@implementation PlaySoundHelperImpl

- (id)initWithAudio:(CPCAPI2::Media::AudioImpl *)audio
{
   self = [super init];

   if (self)
   {
      _audioImpl = audio;
   }

   return self;
}


- (BOOL) playSoundFile:(NSString*)filePath repeat:(BOOL)repeat
{
   NSError *err = nil;

   [self stop];

   NSURL *url = [NSURL fileURLWithPath:filePath];

   _player = [[AVAudioPlayer alloc] initWithContentsOfURL:url error:&err];
   _player.delegate = self;

   if(_player == nil || err != nil)
   {
      _player = nil;

      return FALSE;
   }
   else
   {
      _player.numberOfLoops = repeat ? -1: 0;
      [_player play];
   }

   return TRUE;
}

- (void)stop
{
   if (_player != nil)
   {
      if (_player.playing)
      {
         [_player stop];
      }

      _player = nil;
   }
}

- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag
{
   _audioImpl->postCallback(resip::resip_bind(&CPCAPI2::Media::AudioImpl::OnLocallyPlayingFileWithHandleFinished, _audioImpl, _handle));
}

@end




namespace CPCAPI2
{
namespace Media
{
   PlaySoundHelper::PlaySoundHelper(AudioImpl* audio, resip::MultiReactor& reactor) : mAudio(audio), mChannel(-1), mHandle(0xffffffff), mImpl(NULL), mTimer(reactor), mReactor(reactor), mDevice(NULL)
   {
   }
   PlaySoundHelper::~PlaySoundHelper()
   {
      if (mChannel >= 0)
      {
         mAudio->media_stack()->voe_base()->StopPlayout(mChannel);
         mAudio->media_stack()->voe_base()->DeleteChannel(mChannel);
      }

      CFBridgingRelease(mImpl);
   }

   void PlaySoundHelper::stop()
   {
      if(mChannel >= 0)
      {
         mAudio->media_stack()->dtmf()->StopPlayingDtmfTone();
         mAudio->media_stack()->file()->StopPlayingFileLocally(mChannel);
      }
      else
      {
         [(__bridge PlaySoundHelperImpl*)mImpl stop];
      }
   }

   int PlaySoundHelper::playSound(PlaySoundHandle h, int webrtcDeviceIdx, int32_t audioUsage, const cpc::string& resourceUri, bool repeat)
   {
      mHandle = h;

      if (resourceUri.find("tone:") == 0)
      {
         if (mAudio == NULL)
         {
            return -1;
         }

         do
         {
            mChannel = mAudio->media_stack()->voe_base()->CreateChannel();

            if(mChannel < 0)
            {
               break;
            }

            if (mAudio->media_stack()->file()->RegisterLocalFileEventObserver(mChannel, *this) != 0) break;

            if (mAudio->media_stack()->voe_base()->StartPlayout(mChannel) != 0)
            {
               mAudio->media_stack()->voe_base()->DeleteChannel(mChannel);
               mChannel = (int)-1;
               break;
            }

            if (mAudio->media_stack()->file()->IsPlayingFileLocally(mChannel) != 0)
            {
               mAudio->media_stack()->voe_base()->DeleteChannel(mChannel);
               mChannel = (int)-1;
               break;
            }

            resip::Uri toneUri(resourceUri.c_str());
            int toneIdInt = (int)strtol(toneUri.host().c_str(), NULL, 10);
            int durationMS = toneUri.exists(resip::p_duration) ? toneUri.param(resip::p_duration) : 200;

            if(repeat)
            {
               mAudio->media_stack()->dtmf()->StartPlayingDtmfTone(toneIdInt);
            }
            else
            {
               if (mAudio->media_stack()->dtmf()->PlayDtmfTone(toneIdInt) != 0)
               {
                  mAudio->media_stack()->voe_base()->DeleteChannel(mChannel);
                  mChannel = -1;
                  break;
               }

               mTimer.expires_from_now(durationMS);
               mTimer.async_wait(this, 0, NULL);
            }

            return kSuccess;
         } while(0);

         resip::ReadCallbackBase* failedCallback = makeFpCommand1(AudioHandler::onPlaySoundFailure, mAudio->handler(), mHandle);
         mAudio->postCallback(failedCallback);
         return kError;
      }

      NSString *file = [NSString stringWithUTF8String:resourceUri.c_str()];

      CFBridgingRelease(mImpl);
      mImpl = (__bridge_retained void*)[[PlaySoundHelperImpl alloc] initWithAudio:mAudio];

      [(__bridge PlaySoundHelperImpl*)mImpl setHandle:mHandle];
      return [(__bridge PlaySoundHelperImpl*)mImpl playSoundFile:file repeat:repeat] ? kSuccess: kError;
   }

   void PlaySoundHelper::onTimer(unsigned short timerId, void* appState)
   {
      mAudio->media_stack()->dtmf()->StopPlayingDtmfTone();
      mAudio->media_stack()->file()->StopPlayingFileLocally(mChannel);
      mAudio->media_stack()->voe_base()->StopPlayout(mChannel);
      mAudio->media_stack()->voe_base()->DeleteChannel(mChannel);
      OnLocallyPlayingFileFinished(mChannel);
      mChannel = (int)-1;
   }

   void PlaySoundHelper::OnLocallyPlayingFileFinished(int channel)
   {
      std::weak_ptr<PlaySoundHelper> weakThis = shared_from_this();
      mReactor.post(resip::resip_static_bind(&PlaySoundHelper::onLocallyPlayingFileFinishedImpl, weakThis, channel));
   }

   void PlaySoundHelper::onLocallyPlayingFileFinishedImpl(std::weak_ptr<PlaySoundHelper>& weakThis, int channel)
   {
      if (std::shared_ptr<PlaySoundHelper> strongThis = weakThis.lock())
      {
         PlaySoundHandle temp = strongThis->mHandle;
         strongThis->mHandle = 0xffffffff;
         if (strongThis->mAudio != NULL)
         {
            strongThis->mAudio->handlePlaySoundComplete(temp);
         }
      }
   }

   void PlaySoundHelper::OnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle)
   {
      assert(handle == mHandle);
      PlaySoundHandle temp = mHandle;
      mHandle = 0xffffffff;
      if (mAudio != NULL)
      {
         mAudio->handlePlaySoundComplete(temp);
      }
   }

   void PlaySoundHelper::onSystemAudioServiceError(int errorLevel)
   {
      if (mAudio != NULL)
      {
         mAudio->onSystemAudioServiceError(errorLevel);
      }
   }

   void PlaySoundHelper::detatchAudioImpl()
   {
      mAudio = NULL;
   }
}
}

#endif
