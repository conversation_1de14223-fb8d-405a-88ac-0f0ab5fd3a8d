#ifndef CPCAPI2_MP4FRAGMENTER_H
#define CPCAPI2_MP4FRAGMENTER_H

#include <memory>

namespace shaka
{
namespace media
{
class Muxer;
class ChunkingHandler;
class Replicator;
class H264Parser;
class MediaSample;
class StreamInfo;
class MediaHandler;
}
}

namespace webrtc
{
class EncodedImage;
class EncodedImageCallback;
class EncodedAudioObserver;
}

namespace CPCAPI2
{
namespace Media
{
class Mp4Fragmenter;
class EncodedFrameHandler2;
class Mp4EncodedAudioObserver;

struct StreamStartedCb
{
   virtual void operator()() = 0;
};

struct StreamEndedCb
{
   virtual void operator()() = 0;
};

struct StreamDataCb
{
   virtual void operator()(const uint8_t*, unsigned int, bool) = 0;
};

class Mp4Fragmenter
{
public:
   Mp4Fragmenter();
   virtual ~Mp4Fragmenter();

   void Init(
      StreamStartedCb* audioStreamStartedCallback,
      StreamEndedCb* audioStreamEndedCallback,
      StreamDataCb* audioStreamDataCallback,
      StreamStartedCb* videoStreamStartedCallback,
      StreamEndedCb* videoStreamEndedCallback,
      StreamDataCb* videoStreamDataCallback);

   void HandleEncodedImage(const webrtc::EncodedImage& encoded_image);
   webrtc::EncodedImageCallback& EncodedFrameHandler() const;

   void HandleEncodedAudio(uint32_t timeStamp,
      const uint8_t* payloadData,
      size_t payloadSize);
   webrtc::EncodedAudioObserver& EncodedAudioObserver() const;

   static bool IsSidx(const uint8_t* seg_buffer, const size_t segsize);

private:
   std::shared_ptr<shaka::media::ChunkingHandler> mChunker;
   std::shared_ptr<shaka::media::Replicator> mAudioReplicator;
   std::shared_ptr<shaka::media::Replicator> mVideoReplicator;
   std::shared_ptr<shaka::media::Muxer> mAudioMp4Mux;
   std::shared_ptr<shaka::media::Muxer> mVideoMp4Mux;
   std::unique_ptr<shaka::media::H264Parser> mH264parser;
   std::shared_ptr<shaka::media::MediaSample> mAudioPendingSample;
   std::shared_ptr<shaka::media::MediaSample> mVideoPendingSample;
   int64_t mIsoTimeOffsetVideo;
   int64_t mIsoTimeOffsetAudio;
   std::shared_ptr<shaka::media::StreamInfo> mAudioStreamInfo;
   std::shared_ptr<shaka::media::StreamInfo> mVideoStreamInfo;
   std::unique_ptr<shaka::media::MediaHandler> mMediaHandler;
   std::unique_ptr<EncodedFrameHandler2> mEncodedFrameHandler;
   std::unique_ptr<Mp4EncodedAudioObserver> mEncodedAudioObserver;
   int64_t mLastVideoFrameTimestamp;
};
}
}

#endif // CPCAPI2_MP4FRAGMENTER_H