#pragma once

#if !defined(CPCAPI2_RAW_VIDEO_FRAME_SERVER_CALLBACK_H)
#define CPCAPI2_RAW_VIDEO_FRAME_SERVER_CALLBACK_H

#include "cpcapi2defs.h"
#include "videostreaming/VideoStreaming.h"
#include "RawVideoFrameServer.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>

// rapidjson
#include <document.h>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>
#include <mutex>

namespace CPCAPI2
{
   class PhoneInterface;
   namespace Media
   {
      class RawVideoFrameServerCallback;

      class RawVideoFrameServerCbSession
      {
      public:
         RawVideoFrameServerCbSession(CPCAPI2::PhoneInterface* masterSdkPhone, RawVideoFrameServerCallback* transport, const std::function<void(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)>& cbFunc, int videoStream, void* cbState);
         virtual ~RawVideoFrameServerCbSession();

         void SendBitstreamData(int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride);
         bool NeedsKeyframe(int videoStreamHandle);

      private:
         std::function<void(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)> mCbFunc;
         CPCAPI2::PhoneInterface* mMasterPhone;
         RawVideoFrameServerCallback* mTransport;
         void* mCbState;

         struct PerConnectionStreamState
         {
            PerConnectionStreamState(int vs)
               : videoStream(vs)
            {
               mWaitingForKeyFrame = false;
            }
            int videoStream;
            bool mWaitingForKeyFrame;
         };
         std::map<int, std::shared_ptr<PerConnectionStreamState> > mStreamStateMap;
      };

      class RawVideoFrameServerCallback : public RawVideoFrameServer
      {
      public:
         RawVideoFrameServerCallback();
         virtual ~RawVideoFrameServerCallback();

         virtual void StartServer(CPCAPI2::Phone* phone, const CPCAPI2::VideoStreaming::VideoStreamingServerConfig& serverConfig, resip::ReadCallbackBase* startedCb, std::function<void()> onPrivilegedAccessCompleted) OVERRIDE;
         virtual void StopServer() OVERRIDE;
         virtual void AddObserver(RawVideoFrameServerObserver* obs) OVERRIDE;
         virtual void RemoveObserver(RawVideoFrameServerObserver* obs) OVERRIDE;
         virtual void SendRawI420BistreamData(int videoStreamHandle, int width, int height, const unsigned char* ybuffer, unsigned int ystride, const unsigned char* ubuffer, unsigned int ustride, const unsigned char* vbuffer, unsigned int vstride) OVERRIDE;
         virtual size_t GetSessionCount(int videoStreamHandle) OVERRIDE;
         virtual void CloseSessions(int videoStreamHandle) OVERRIDE;
         int CloseConnection(int hdl);
         void RequestKeyFrame(int videoStreamHandle);
         void RemoveSession(int hdl);
         bool NeedsKeyframe(int videoStreamHandle);

         void AddCallback(int videoStreamHandle, void* cbState, const std::function<void(void*, unsigned int, int, int, const unsigned char*, unsigned int, const unsigned char*, unsigned int, const unsigned char*, unsigned int)>& cbfunc);
         void RemoveCallback(int videoStreamHandle);

      private:
         typedef std::map<int /*videoStreamHandle*/, RawVideoFrameServerCbSession*> sess_list;

         std::string get_password() const;

      private:
         CPCAPI2::Phone* mPhone;
         sess_list mSessions;
         std::set<RawVideoFrameServerObserver*> mObs;
         std::mutex mMapLock;
         CPCAPI2::VideoStreaming::VideoStreamingServerConfig mConfig;
      };

   }

}

#endif // CPCAPI2_RAW_VIDEO_FRAME_SERVER_CALLBACK_H

