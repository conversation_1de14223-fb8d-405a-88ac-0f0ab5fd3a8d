#include "brand_branded.h"

#if (CPCAPI2_BRAND_MOZJPEG == 1)
#include "VideoFrameSnapshotHelper.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"
#include "JpegHelper.h"

// LibYuv includes
#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>
#include <libyuv.h>
#include <libyuv/convert_argb.h>
#include <webrtc/modules/video_processing/main/source/spatial_resampler.h>

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
#include <thread>
#include <future>
//include <codecvt>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
VideoFrameSnapshotHelper::VideoFrameSnapshotHelper(CPCAPI2::Phone* phone, resip::ReadCallbackBase* handler, const cpc::string& filenameUtf8, const std::shared_ptr<webrtc_recon::MixerImpl>& mixer, int recv_channel)
   : mPhone(phone),
     mHandler(handler),
     mFilename(filenameUtf8),
     mMixer(mixer),
     mRecvChannel(recv_channel),
     mWorkerThread(NULL)
{
   mMixer->registerDecodedFrameObserver(recv_channel, this);
}

VideoFrameSnapshotHelper::~VideoFrameSnapshotHelper()
{
   mMixer->unregisterDecodedFrameObserver(mRecvChannel, this);
   if (mWorkerThread != NULL)
   {
      delete mWorkerThread;
   }
}

// ViEEffectFilter
int VideoFrameSnapshotHelper::Transform(webrtc::VideoFrame* video_frame)
{
   webrtc::VPMSimpleSpatialResampler resampler;
   resampler.SetInputFrameResampleMode(webrtc::kBiLinear);

   webrtc::VideoFrame* capturedFrame = new webrtc::VideoFrame();
   resampler.SetTargetFrameSize(176, 144);
   if (resampler.ResampleFrame(*video_frame, capturedFrame) == 0)
   {
      VideoFrameSnapshotHelper* thisPtr = this;
      cpc::string fileNameRef = mFilename;
      resip::ReadCallbackBase* handlerRef = mHandler;
      PhoneInterface* pi = dynamic_cast<PhoneInterface*>(mPhone);
      mWorkerThread = new std::thread([capturedFrame, thisPtr, fileNameRef, handlerRef, pi]() {
         JpegEncoder jpegHelper;
         const unsigned long out_buff_size = JpegEncoder::MaxEncodedSize(capturedFrame->width(), capturedFrame->height());
         unsigned char* out_buff = new unsigned char[out_buff_size];
         unsigned long jpeg_size = jpegHelper.Encode(*capturedFrame, out_buff, out_buff_size);
         if (jpeg_size > 0)
         {
            std::ofstream outstr(fileNameRef.c_str(), std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
            if (outstr.is_open())
            {
               resip::Data dataStr(resip::Data::Share, (const char*)out_buff, (UInt32)jpeg_size);
               outstr << dataStr << std::flush;
               outstr.close();
            }
         }
         delete[] out_buff;
         delete capturedFrame;
         if (handlerRef != NULL)
         {
            pi->getSdkModuleThread().post(handlerRef);
         }
         delete thisPtr;
      });
      mWorkerThread->detach();
   }
   return 0;
}
}
}

#endif
