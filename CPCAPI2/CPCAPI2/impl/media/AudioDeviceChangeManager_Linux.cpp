#if defined(__linux__) && !defined(ANDROID)
#include "AudioDeviceChangeManager_Linux.h"
#include "../util/cpc_logger.h"
#if defined(USE_PULSEAUDIO)
#include <pulse/pulseaudio.h>
#include <utility>
#include <algorithm>
#include <string>
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

using namespace CPCAPI2::Media;

static const int kSleepIntervalMs = 500;

template <typename Map>
bool map_compare (Map const &lhs, Map const &rhs) {
   return lhs.size() == rhs.size()
          && std::equal(lhs.begin(), lhs.end(), rhs.begin());
}

AudioDeviceChangeManager_Linux::AudioDeviceChangeManager_Linux(resip::MultiReactor& reactor) :
   AudioDeviceChangeManagerImpl(reactor),
   mShutdown(false)
{
#if defined(USE_PULSEAUDIO)
   // Start the thread.
   mPollingThread.reset(new CPCAPI2::thread(std::bind(&AudioDeviceChangeManager_Linux::pollAudioInterfacesThread, this)));
#else
   WarningLog(<< "AudioDeviceChangeManager can not be started. Please recompile with USE_PULSEAUDIO.");
#endif
}

AudioDeviceChangeManager_Linux::~AudioDeviceChangeManager_Linux()
{
   mShutdown = true;
   if (mPollingThread.get() != NULL)
   {
      mPollingThread->join();
   }
}

#if defined(USE_PULSEAUDIO)
void AudioDeviceChangeManager_Linux::pollAudioInterfacesThread()
{
   std::map<int, std::string> inputDevices;
   std::map<int, std::string> outputDevices;
   getDeviceList(&inputDevices, &outputDevices);

   while (!mShutdown)
   {
      std::map<int, std::string> newInputDevices;
      std::map<int, std::string> newOutputDevices;
      if (getDeviceList(&newInputDevices, &newOutputDevices) < 0)
      {
         WarningLog(<< "Failed to get audio device list.");
         break;
      }

      if (!map_compare(inputDevices, newInputDevices) ||
          !map_compare(outputDevices, newOutputDevices))
      {
         sendAudioDeviceChangeEvent();
      }

      inputDevices.clear();
      outputDevices.clear();
      inputDevices.insert(newInputDevices.begin(), newInputDevices.end());
      outputDevices.insert(newOutputDevices.begin(), newOutputDevices.end());

      // Sleep a little.
      CPCAPI2::this_thread::sleep_for(chrono::milliseconds(kSleepIntervalMs));
   }
}

int AudioDeviceChangeManager_Linux::getDeviceList(std::map<int, std::string> *inputDevices,
                                                  std::map<int, std::string> *outputDevices)
{
   // Define our pulse audio loop and connection variables.
   pa_mainloop *pa_ml;
   pa_mainloop_api *pa_mlapi;
   pa_operation *pa_op;
   pa_context *pa_ctx;

   // We'll need these state variables to keep track of our requests
   int state = 0;
   int pa_ready = 0;

   // Create a mainloop API and connection to the default server
   pa_ml = pa_mainloop_new();
   pa_mlapi = pa_mainloop_get_api(pa_ml);
   pa_ctx = pa_context_new(pa_mlapi, "test");

   // This function connects to the pulse server
   pa_context_connect(pa_ctx, NULL, static_cast<pa_context_flags_t>(0), NULL);

   // This function defines a callback so the server will tell us it's state.
   // Our callback will wait for the state to be ready.  The callback will
   // modify the variable to 1 so we know when we have a connection and it's
   // ready.
   // If there's an error, the callback will set pa_ready to 2
   pa_context_set_state_callback(pa_ctx, pa_state_cb, &pa_ready);

   // Now we'll enter into an infinite loop until we get the data we receive
   // or if there's an error
   for (;;)
   {
      // We can't do anything until PA is ready, so just iterate the mainloop
      // and continue
      if (pa_ready == 0)
      {
         pa_mainloop_iterate(pa_ml, 1, NULL);
         continue;
      }
      // We couldn't get a connection to the server, so exit out
      if (pa_ready == 2)
      {
         pa_context_disconnect(pa_ctx);
         pa_context_unref(pa_ctx);
         pa_mainloop_free(pa_ml);
         return -1;
      }
      // At this point, we're connected to the server and ready to make
      // requests
      switch (state)
      {
         // State 0: we haven't done anything yet
         case 0:
            // This sends an operation to the server.  pa_sinklist_info is
            // our callback function and a pointer to our devicelist will
            // be passed to the callback The operation ID is stored in the
            // pa_op variable
            pa_op = pa_context_get_sink_info_list(pa_ctx, pa_sinklist_cb,
                                                  outputDevices);

            // Update state for next iteration through the loop
            state++;
            break;
         case 1:
            // Now we wait for our operation to complete.  When it's
            // complete our pa_output_devicelist is filled out, and we move
            // along to the next state
            if (pa_operation_get_state(pa_op) == PA_OPERATION_DONE)
            {
               pa_operation_unref(pa_op);

               // Now we perform another operation to get the source
               // (input device) list just like before.  This time we pass
               // a pointer to our input structure
               pa_op = pa_context_get_source_info_list(pa_ctx, pa_sourcelist_cb,
                                                       inputDevices);
               // Update the state so we know what to do next
               state++;
            }
            break;
         case 2:
            if (pa_operation_get_state(pa_op) == PA_OPERATION_DONE)
            {
               // Now we're done, clean up and disconnect and return
               pa_operation_unref(pa_op);
               pa_context_disconnect(pa_ctx);
               pa_context_unref(pa_ctx);
               pa_mainloop_free(pa_ml);
               return 0;
            }
            break;
         default:
            // We should never see this state
            WarningLog(<< "getDeviceList error in state " << state);
            return -1;
      }
      // Iterate the main loop and go again.  The second argument is whether
      // or not the iteration should block until something is ready to be
      // done.  Set it to zero for non-blocking.
      pa_mainloop_iterate(pa_ml, 1, NULL);
   }
   return 0;
}

// This callback gets called when our context changes state.  We really only
// care about when it's ready or if it has failed
void AudioDeviceChangeManager_Linux::pa_state_cb(pa_context *c,
                                                 void *userdata)
{
   pa_context_state_t state;
   int *pa_ready = static_cast<int*>(userdata);

   state = pa_context_get_state(c);
   switch  (state)
   {
      // There are just here for reference
      case PA_CONTEXT_UNCONNECTED:
      case PA_CONTEXT_CONNECTING:
      case PA_CONTEXT_AUTHORIZING:
      case PA_CONTEXT_SETTING_NAME:
         break;
      case PA_CONTEXT_FAILED:
      case PA_CONTEXT_TERMINATED:
         *pa_ready = 2;
         break;
      case PA_CONTEXT_READY:
         *pa_ready = 1;
         break;
      default:
         break;
   }
}

// pa_mainloop will call this function when it's ready to tell us about a sink.
// Since we're not threading, there's no need for mutexes on the devicelist
// structure
void AudioDeviceChangeManager_Linux::pa_sinklist_cb(pa_context *c,
                                                    const pa_sink_info *l,
                                                    int eol, void *userdata)
{
   // If eol is set to a positive number, you're at the end of the list
   if (eol > 0)
   {
      return;
   }
   std::map<int, std::string> *outputDevices =
                        static_cast<std::map<int, std::string> *>(userdata);

   outputDevices->insert(std::pair<int, std::string>(l->index, l->name));
}

// See above. This callback is pretty much identical to the previous
void AudioDeviceChangeManager_Linux::pa_sourcelist_cb(pa_context *c,
                                                      const pa_source_info *l,
                                                      int eol, void *userdata)
{
   if (eol > 0)
   {
      return;
   }

   std::map<int, std::string> *inputDevices =
                        static_cast<std::map<int, std::string> *>(userdata);

   inputDevices->insert(std::pair<int, std::string>(l->index, l->name));
}


#endif // #if defined(USE_PULSEAUDIO)
#endif // #if defined( __linux__ )
