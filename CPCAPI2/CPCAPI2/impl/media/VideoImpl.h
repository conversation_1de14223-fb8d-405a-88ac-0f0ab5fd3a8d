#pragma once

#if !defined(CPCAPI2_VIDEO_IMPL_H)
#define CPCAPI2_VIDEO_IMPL_H

#include "cpcapi2defs.h"
#include "media/video/VideoHandler.h"
#include "media/video/Video.h"
#include "media/MediaManager.h"
#include "VideoInterface.h"
#include "ScreenShare.h"
#include "CustomVideoSource.h"
#include "phone/PhoneInterface.h"
#ifdef __APPLE__
#include "util/Mac/AppleVideo.h"
#endif

#include <webrtc/common_types.h>
#include <webrtc/video_engine/include/vie_capture.h>
#include <chrono>
#include <atomic>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class PlaySoundHelper;
class VideoInternalHandler;
class Mp4Fragmenter;

class VideoImpl : public ScreenShareObserver, webrtc::ViECaptureObserver, resip::DeadlineTimerHandler
{
public:
   VideoImpl(webrtc_recon::MediaStackImpl* mediaStack, VideoInterface *videoInterface, PhoneInterface* phone);
   virtual ~VideoImpl();

   webrtc_recon::MediaStackImpl* media_stack() const { return mMediaStack; }

   int queryDeviceList();
   int setCaptureDevice(unsigned int deviceId);
   int setCaptureDeviceOrientation(VideoOrientation orientation);
   int setCaptureImageOrientation(VideoImageOrientation orientation);
   int setIncomingVideoRenderTarget(void* surface, VideoSurfaceType type);
   int setIncomingVideoRenderTarget2(int recvVideoStreamId, void* surface, VideoSurfaceType type);
   int setLocalVideoRenderTarget(void* surface, VideoSurfaceType type);
   int setLocalVideoPreviewResolution(VideoCaptureResolution resolution);
   int startCapture();
   int stopCapture();
   int startScreenshare(ScreenShareHandler*);
   int stopScreenshare();
   int queryScreenshareDeviceList(ScreenshareDeviceListHandler* handler, bool includeMonitors, bool includeWindows);
   int setScreenshareCaptureDevice(unsigned int deviceId);
   int setScreenshareCaptureMaxFramerate(unsigned int maxFrameRate);

   int setHandler(VideoHandler* handler);
   void postCallback(resip::ReadCallbackBase* fp);

   VideoHandler* handler() const { return mMediaHandler; }

   int queryCodecList();
   int setCodecEnabled(unsigned int id, bool enabled);
   int setCodecPriority(unsigned int id, unsigned int priority);
   int setCodecPayloadType(unsigned int id, unsigned int payloadType);
   int setCodecEncodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled);
   int setCodecDecodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled);
   int setPacketLossConfig(unsigned int codecId, const PacketLossConfig& packetLossConfig);
   int setPreferredResolution(unsigned int id, VideoCaptureResolution resolution);
   int setVideoMute(bool enabled);
   int setVideoMixMode(int mixMode);
   void updatePerformanceProfile(DevicePerformanceProfile perfProfile);

   int setVideoDscp(unsigned int mediaDscp);
   // iOS speceific setting for QoS Fastlane
   int setVideoUseQosFastlane(bool useQosFastlane, bool useWithDscp);

   int connectVideoStreams(int recv_channel, int send_channel);
   int disconnectVideoStreams(int recv_channel, int send_channel);

   int showPropertyPage(void* surface);

   int requestKeyFrame(int recv_channel);

   int set1080pEnabled(bool enable);

   // ScreenshareObserver
   virtual void onScreenShareError(ScreenShare*) OVERRIDE;

private:
   int readCaptureDeviceInfo(unsigned int listNumber, VideoDeviceInfo& devInfo, cpc::string* devGuid);
   int findCaptureDevice(unsigned int deviceId, VideoDeviceInfo& devInfo, cpc::string* devGuid);

   void attachLocalVideoCaptureRender();
   void startLocalVideoCapture(bool connectToActiveCalls);
   void stopLocalVideoCapture();
   void startLocalVideoRender(void* displaySurface, VideoSurfaceType type);
   void stopLocalVideoRender();

   void signalIncomingVideoRenderTargetChanged(int channel, void* oldSurface, void* newSurface, webrtc::VideoRenderSurfaceType newSurfaceType);
   void signalCaptureStopped();
   void signalCaptureStarted(int captureId);

   bool videoSurfacesAreDifferent(void* s1, webrtc::VideoRenderSurfaceType s1type, void* s2, webrtc::VideoRenderSurfaceType s2type);
   void setCodecLevel(VideoCaptureResolution resolution, webrtc::VideoCodec& video, unsigned int mobileMaxLevel);
   
   void noPictureAlarmImpl(const int capture_id,
                         const webrtc::CaptureAlarm alarm);
                         
   void startLocalCaptureDiagFpsMonitor();
   void stopLocalCaptureDiagFpsMonitor();
   
   // ViECaptureObserver
   virtual void BrightnessAlarm(const int capture_id,
                       const webrtc::Brightness brightness) OVERRIDE;
   virtual void CapturedFrameRate(const int capture_id,
                        const unsigned char frame_rate) OVERRIDE;
   virtual void NoPictureAlarm(const int capture_id,
                      const webrtc::CaptureAlarm alarm) OVERRIDE;
 
                              
   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:
   webrtc_recon::MediaStackImpl* mMediaStack;
   VideoInterface *mVideoInterface;
   cpc::vector<VideoDeviceInfo> mVideoDeviceList;
   VideoHandler* mMediaHandler;
   cpc::string mCaptureDeviceGuidUTF8;
   std::string mCaptureDeviceName;
   int mVidCaptureId;
   VideoOrientation mVidCaptureOrientation;
   VideoImageOrientation mVidImageOrientation;
   bool mCaptureRenderAttached;
   webrtc::VideoRenderSurfaceType mRemoteVideoSurfaceType;
   void* mLocalVideoSurface;
   webrtc::VideoRenderSurfaceType mLocalVideoSurfaceType;
   struct VideoCodecInfoInternal
   {
      VideoCodecInfoInternal()
      {
         video_codec_info.enabled = false;
         video_codec_info.id = 0;
         video_codec_info.maxBandwidth = 0;
         video_codec_info.minBandwidth = 0;
         video_codec_info.priority = 0;
         preferred_resolution = CPCAPI2::Media::VideoCaptureResolution_MaxSupported;
      }

      VideoCodecInfo video_codec_info;
      VideoCaptureResolution preferred_resolution;
   };
   cpc::vector<VideoCodecInfoInternal> mVideoCodecList;
   int mCaptureWidth;
   int mCaptureHeight;
   int mCaptureFramerate;
   bool mAppSpecifiedResolution;
   DevicePerformanceProfile mPerfProfile;
   ScreenShare* mScreenShare;
   ScreenShareHandler* mScreenShareHandler;
   CustomVideoSource* mCustomVideoSource;
   PhoneInterface* mPhone;
   int mMaxSupportedWidth;
   int mMaxSupportedHeight;
   unsigned int mScreenshareDeviceId;
   unsigned int mScreenshareMaxFrameRate;
   
   resip::DeadlineTimer<resip::MultiReactor> mLocalCaptureDiagFpsTimer;
   resip::DeadlineTimer<resip::MultiReactor> mLocalCaptureDiagNoPicAlarmTimer;
   std::atomic_int mLocalCaptureDiagLastFramerate;
   
   
#ifdef __APPLE__
   AppleVideo mAppleVideo;
#endif
};

std::ostream& operator<<(std::ostream& os, const VideoDeviceListUpdatedEvent& type);
std::ostream& operator<<(std::ostream& os, const VideoDeviceInfo& type);

}
}
#endif // CPCAPI2_VIDEO_IMPL_H
