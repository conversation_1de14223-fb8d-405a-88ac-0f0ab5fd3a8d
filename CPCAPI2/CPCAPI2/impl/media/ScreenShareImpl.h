#ifndef CPCAPI2_SCREENSHAREIMPL_H
#define CPCAPI2_SCREENSHAREIMPL_H

#include "ScreenShare.h"
#include "../util/cpc_thread.h"

#include <webrtc/modules/desktop_capture/desktop_capture_options.h>
#include <webrtc/modules/desktop_capture/desktop_and_cursor_composer.h>
#include <webrtc/system_wrappers/interface/event_wrapper.h>
#include <webrtc/system_wrappers/interface/tick_util.h>
#include <vie_capture.h>

#include <atomic>

namespace CPCAPI2
{
   namespace Media
   {
      class ScreenSharePlatformObjHolder
      {
      public:
         virtual ~ScreenSharePlatformObjHolder() {}
      };
      class ScreenShareImpl : public CPCAPI2::Media::ScreenShare,
                              public webrtc::DesktopCapturer::Callback
      {
      public:
         ScreenShareImpl();
         virtual ~ScreenShareImpl();

         // ScreenShare
         virtual int Start(unsigned int deviceId);
         virtual void SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture);
         virtual void SetObserver(ScreenShareObserver* observer);
         virtual void SetMaxCaptureFrameRate(unsigned int fps);
         virtual int Stop();
         virtual void ResetScreenCap();
         virtual void PerformCapture();
         virtual void InitializeCapture();
         virtual int GetMonitors(std::vector<ScreenShareMonitorListItem>* monitorList);
         virtual int GetWindows(std::vector<ScreenShareWindowListItem>* windowList);
         virtual double getFrameCaptureInterval() { return mFrameCaptureInterval;}
         
         // webrtc::DesktopCapturer::Callback
         // Called after a frame has been captured. |frame| is not nullptr if and
         // only if |result| is SUCCESS.
         virtual void OnCaptureResult(webrtc::DesktopCapturer::Result result,
            std::unique_ptr<webrtc::DesktopFrame> frame);
         
         unsigned int GetScreenshareDevice() const { return mScreenshareDevice; }

      private:
         void StartImpl();
         void StopImpl();
         virtual void CaptureThread();
         virtual void BorderThread();

      private:
         std::unique_ptr<CPCAPI2::thread> mCaptureThread;
         std::unique_ptr<CPCAPI2::thread> mBorderThread;
         std::unique_ptr<webrtc::EventTimerWrapper> mCaptureEvt;
         std::unique_ptr<webrtc::DesktopAndCursorComposer> mScreenCap;
         // DesktopFrame used for converting to video frame
         std::unique_ptr<webrtc::DesktopFrame> mOutputFrame;
         webrtc::ViEExternalCapture* mExternalCapture;
         webrtc::VideoFrame mCapturedFrameI420;
         std::atomic_bool mCapturing;
         ScreenShareObserver* mObserver;
         ScreenSharePlatformObjHolder* mObjHolder;
         unsigned int mScreenshareDevice;
         uint8_t* mScaleBuffer;
         unsigned int mScaleBufferSize;
         webrtc::TickTime mLastFpsLogTime;
         std::atomic<double> mFrameCaptureInterval; // ms
      };
   }
}
#endif // CPCAPI2_SCREENSHAREIMPL_H
