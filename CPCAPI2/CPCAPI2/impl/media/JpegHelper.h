/*
 *  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */
#ifndef CPCAPI2_JPEG_HELPER_H
#define CPCAPI2_JPEG_HELPER_H
#include "webrtc/common_video/interface/video_image.h"  // EncodedImage
#include "webrtc/typedefs.h"

namespace CPCAPI2
{
namespace Media
{
// TODO(mikhal): Move this to LibYuv wrapper, when LibYuv will have a JPG
// Encode.
class JpegEncoder
{
public:
    JpegEncoder();
    ~JpegEncoder();

    unsigned long Encode(const webrtc::VideoFrame& inputImage, unsigned char*& outbuff, unsigned long outbuffSize);
    static unsigned long MaxEncodedSize(int width, int height);

};
}
}
#endif /* CPCAPI2_JPEG_HELPER_H  */
