#include <memory>
#include <optional>

#include "media/audio/AudioHandler.h"
#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"
#include "gen/Audio/server/IAudio.h"
#include "gen/Audio/datatypes/AudioAudioDeviceInfo.h"
#include "gen/Audio/datatypes/AudioAudioCodecInfo.h"

namespace CPCAPI2
{
namespace Media
{
class Audio;
}
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace Audio
{
AudioAudioDeviceInfo convert(const ::CPCAPI2::Media::AudioDeviceInfo& deviceInfo);
AudioAudioCodecInfo convert(const ::CPCAPI2::Media::AudioCodecInfo& codecInfo);

class AudioCompatibility : public jsonrpc::CPCAPI2::Audio::IAudio
{
public:
	AudioCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);

    virtual void queryDeviceList(const int64_t phoneHandle) override;

	virtual void setCaptureDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role) override;

	virtual void setRenderDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role) override;

	virtual int64_t playSound(const int64_t phoneHandle, const AudioAudioDeviceRole role, const cpc::string& resourceUri, const bool repeat) override;

	virtual void stopPlaySound(const int64_t phoneHandle, const int64_t sound) override;

	virtual void queryCodecList(const int64_t phoneHandle) override;

	virtual void setCodecEnabled(const int64_t phoneHandle, const int64_t codecId, const bool enabled) override;

	virtual void setCodecPriority(const int64_t phoneHandle, const int64_t codecId, const int64_t priority) override;

	virtual void setCodecPayloadType(const int64_t phoneHandle, const int64_t codecId, const int64_t payloadType) override;

	virtual void setTelephoneEventPayloadType(const int64_t phoneHandle, const int64_t payloadType) override;

	virtual void setMicMute(const int64_t phoneHandle, const bool enabled) override;

	virtual void setSpeakerMute(const int64_t phoneHandle, const bool enabled) override;

	virtual void setMicVolume(const int64_t phoneHandle, const int64_t level) override;

	virtual void setSpeakerVolume(const int64_t phoneHandle, const int64_t level) override;

	virtual void queryDeviceVolume(const int64_t phoneHandle) override;

	virtual void startMonitoringCaptureDeviceLevels(const int64_t phoneHandle, const int64_t deviceId) override;

	virtual void stopMonitoringRenderDeviceLevels(const int64_t phoneHandle) override;

private:
	::CPCAPI2::Media::Audio* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
	


}; // class AudioCompatibility
} // namepace Audio
} // namepace CPCAPI2
} // namepace jsonrpc
