#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "AudioCompatibility.h"
#include "interface/public/media/MediaManager.h"
#include "interface/public/media/audio/Audio.h"
//#include "interface/public/media/audio/AudioTypes.h"
#include "jsonrpccxx/common.hpp"
#include "gen/Audio/datatypes/AudioAudioDeviceRole.h"
#include "gen/Audio/datatypes/AudioAudioDeviceType.h"

namespace jsonrpc
{
namespace CPCAPI2
{
namespace Audio
{

void optionalSet(::CPCAPI2::Media::AudioDeviceRole& toSet, const std::optional<AudioAudioDeviceRole> &v)
{
   if (v)
   {
      switch (*v)
      {
      case AudioAudioDeviceRole::None:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_None;
      case AudioAudioDeviceRole::Headset:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_Headset;
      case AudioAudioDeviceRole::SpeakerPhone:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_SpeakerPhone;
      case AudioAudioDeviceRole::Ringing:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_Ringing;
      case AudioAudioDeviceRole::Bluetooth:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_Bluetooth;
      default:
         toSet = ::CPCAPI2::Media::AudioDeviceRole_None;
      }
   }
}

void optionalSet(::CPCAPI2::Media::AudioDeviceType& toSet, const std::optional<AudioAudioDeviceType> &v)
{
   if (v)
   {
      switch (*v)
      {
      case AudioAudioDeviceType::Capture:
         toSet = ::CPCAPI2::Media::MediaDeviceType_Capture;
      case AudioAudioDeviceType::Render:
         toSet = ::CPCAPI2::Media::MediaDeviceType_Render;
      default:
         toSet = ::CPCAPI2::Media::MediaDeviceType_Capture;
      }
   }
}


template <typename T>
void optionalSet(T &toSet, const std::optional<T> opt)
{
   if (opt)
   {
      toSet = *opt;
   }
}

AudioAudioDeviceRole convert(::CPCAPI2::Media::AudioDeviceRole role)
{
   switch (role)
   {
   case ::CPCAPI2::Media::AudioDeviceRole_None:
      return AudioAudioDeviceRole::None;
   case ::CPCAPI2::Media::AudioDeviceRole_Headset:
      return AudioAudioDeviceRole::Headset;
   case ::CPCAPI2::Media::AudioDeviceRole_SpeakerPhone:
      return AudioAudioDeviceRole::SpeakerPhone;
   case ::CPCAPI2::Media::AudioDeviceRole_Ringing:
      return AudioAudioDeviceRole::Ringing;
   case ::CPCAPI2::Media::AudioDeviceRole_Bluetooth:
      return AudioAudioDeviceRole::Bluetooth;
   default:
      return AudioAudioDeviceRole::None;
   }
}

AudioAudioDeviceType convert(::CPCAPI2::Media::AudioDeviceType type)
{
   switch (type)
   {
      case ::CPCAPI2::Media::MediaDeviceType_Capture:
         return AudioAudioDeviceType::Capture;
      case ::CPCAPI2::Media::MediaDeviceType_Render:
         return AudioAudioDeviceType::Render;
      default:
         return AudioAudioDeviceType::Capture;
   }
}

AudioAudioDeviceInfo convert(const ::CPCAPI2::Media::AudioDeviceInfo& deviceInfo)
{
   AudioAudioDeviceInfo result;

   result.friendlyName = deviceInfo.friendlyName;
   result.hid = deviceInfo.hid;
   result.id = deviceInfo.id;
   result.role = convert(deviceInfo.role);
   result.deviceType = convert(deviceInfo.deviceType);
//   result.inadvisable = deviceInfo.inadvisable;
//   result.defaultSystemDevice = deviceInfo.defaultSystemDevice;
//   result.defaultSystemCommDevice = deviceInfo.defaultSystemCommDevice;

   return result;
}

AudioAudioCodecInfo convert(const ::CPCAPI2::Media::AudioCodecInfo& codecInfo)
{
   AudioAudioCodecInfo result;

   result.codecName = codecInfo.codecName;
   result.id = codecInfo.id;
   result.enabled = codecInfo.enabled;
   result.samplingRate = codecInfo.samplingRate;
   result.minBandwidth = codecInfo.minBandwidth;
   result.maxBandwidth = codecInfo.maxBandwidth;
   result.priority = codecInfo.priority;
   result.payloadType = codecInfo.payloadType;

   return result;
}


AudioCompatibility::AudioCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances> &phones)
{
   mPhones = phones;
}

::CPCAPI2::Media::Audio* AudioCompatibility::getInstance(const int64_t phoneHandle)
{
   ::CPCAPI2::Media::MediaManager* media = ::CPCAPI2::Media::MediaManager::getInterface(mPhones->get(phoneHandle));
   return ::CPCAPI2::Media::Audio::getInterface(media);
}

void AudioCompatibility::queryDeviceList(const int64_t phoneHandle)
{
   getInstance(phoneHandle)->queryDeviceList();
}

void AudioCompatibility::setCaptureDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role)
{
   ::CPCAPI2::Media::AudioDeviceRole inRole;
   optionalSet(inRole, role);

   getInstance(phoneHandle)->setCaptureDevice(deviceId, inRole);
}

void AudioCompatibility::setRenderDevice(const int64_t phoneHandle, const int64_t deviceId, const AudioAudioDeviceRole role)
{
   ::CPCAPI2::Media::AudioDeviceRole inRole;
   optionalSet(inRole, role);

   getInstance(phoneHandle)->setRenderDevice(deviceId, inRole);
}

int64_t AudioCompatibility::playSound(const int64_t phoneHandle, const AudioAudioDeviceRole role, const cpc::string& resourceUri, const bool repeat)
{
   ::CPCAPI2::Media::AudioDeviceRole inRole;
   optionalSet(inRole, role);

   return getInstance(phoneHandle)->playSound(inRole, resourceUri, repeat);
}

void AudioCompatibility::stopPlaySound(const int64_t phoneHandle, const int64_t sound)
{
   getInstance(phoneHandle)->stopPlaySound(sound);
}

void AudioCompatibility::queryCodecList(const int64_t phoneHandle)
{
   getInstance(phoneHandle)->queryCodecList();
}

void AudioCompatibility::setCodecEnabled(const int64_t phoneHandle, const int64_t codecId, const bool enabled)
{
   getInstance(phoneHandle)->setCodecEnabled(codecId, enabled);
}

void AudioCompatibility::setCodecPriority(const int64_t phoneHandle, const int64_t codecId, const int64_t priority)
{
   getInstance(phoneHandle)->setCodecPriority(codecId, priority);
}

void AudioCompatibility::setCodecPayloadType(const int64_t phoneHandle, const int64_t codecId, const int64_t payloadType)
{
   getInstance(phoneHandle)->setCodecPayloadType(codecId, payloadType);
}

void AudioCompatibility::setTelephoneEventPayloadType(const int64_t phoneHandle, const int64_t payloadType)
{
   getInstance(phoneHandle)->setTelephoneEventPayloadType(payloadType);
}

void AudioCompatibility::setMicMute(const int64_t phoneHandle, const bool enabled)
{
   getInstance(phoneHandle)->setMicMute(enabled);
}

void AudioCompatibility::setSpeakerMute(const int64_t phoneHandle, const bool enabled)
{
   getInstance(phoneHandle)->setSpeakerMute(enabled);
}

void AudioCompatibility::setMicVolume(const int64_t phoneHandle, const int64_t level)
{
   getInstance(phoneHandle)->setMicVolume(level);
}

void AudioCompatibility::setSpeakerVolume(const int64_t phoneHandle, const int64_t level)
{
   getInstance(phoneHandle)->setSpeakerVolume(level);
}

void AudioCompatibility::queryDeviceVolume(const int64_t phoneHandle)
{
   getInstance(phoneHandle)->queryDeviceVolume();
}

void AudioCompatibility::startMonitoringCaptureDeviceLevels(const int64_t phoneHandle, const int64_t deviceId)
{
   getInstance(phoneHandle)->startMonitoringCaptureDeviceLevels(deviceId);
}

void AudioCompatibility::stopMonitoringRenderDeviceLevels(const int64_t phoneHandle)
{
   getInstance(phoneHandle)->stopMonitoringRenderDeviceLevels();
}


} // namepace Audio
}    // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE