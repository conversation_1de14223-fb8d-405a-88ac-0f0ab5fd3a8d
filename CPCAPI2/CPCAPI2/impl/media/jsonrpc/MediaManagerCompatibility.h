#include <memory>
#include <optional>

#include "impl/jsonrpc/PhoneInstances.h"
#include "gen/Phone/server/IPhone.h"
#include "gen/MediaManager/server/IMediaManager.h"

namespace CPCAPI2
{
namespace Media
{
class MediaManager;
}
}

namespace jsonrpc
{
namespace CPCAPI2
{
namespace MediaManager
{
class MediaManagerCompatibility : public jsonrpc::CPCAPI2::MediaManager::IMediaManager
{
public:
	MediaManagerCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones);

	virtual void initializeMediaStack(const int64_t phoneHandle) override;

private:
	::CPCAPI2::Media::MediaManager* getInstance(const int64_t phoneHandle);
	std::shared_ptr<::CPCAPI2::PhoneInstances> mPhones;
	


}; // class MediaManagerCompatibility
} // namepace Media
} // namepace CPCAPI2
} // namepace jsonrpc
