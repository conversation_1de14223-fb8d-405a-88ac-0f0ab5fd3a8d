#include "brand_branded.h"

#if (CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE == 1)
#include "MediaManagerCompatibility.h"
#include "interface/public/media/MediaManager.h"
#include "jsonrpccxx/common.hpp"

namespace jsonrpc
{
namespace CPCAPI2
{
namespace MediaManager
{

::CPCAPI2::Media::MediaManager* MediaManagerCompatibility::getInstance(const int64_t phoneHandle)
{
   return ::CPCAPI2::Media::MediaManager::getInterface(mPhones->get(phoneHandle));
}

MediaManagerCompatibility::MediaManagerCompatibility(const std::shared_ptr<::CPCAPI2::PhoneInstances>& phones)
{
   mPhones = phones;
}

void MediaManagerCompatibility::initializeMediaStack(const int64_t phoneHandle)
{
   getInstance(phoneHandle)->initializeMediaStack();
}


} // namepace MediaManager
} // namepace CPCAPI2
} // namespace jsonrpc
#endif // CPCAPI2_BRAND_JSON_RPC_SERVER_MODULE