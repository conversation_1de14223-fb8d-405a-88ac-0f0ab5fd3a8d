#ifndef CPCAPI2_SCREENSHAREBORDER<PERSON>NDOW_H
#define CP<PERSON>PI2_SCREENSHAREBORDERWINDOW_H

#if defined(_WIN32)
#include <webrtc/base/win32window.h>
#include <vector>
#include <webrtc/modules/desktop_capture/win/window_capture_utils.h>

namespace CPCAPI2
{
   namespace Media
   {
      class ScreenShareBorderWindow : public rtc::Win32Window
      {

      public:
         enum WindowLocation
         {
            Left,
            Top,
            Right,
            Bottom,
            FullWindow
         };

         static const wchar_t* WindowTitles[5];

         ScreenShareBorderWindow(WindowLocation windowLocation);
         virtual ~ScreenShareBorderWindow();

         void showWindow(unsigned int screenshareDeviceId);

      private:
         static void CALLBACK WinEventHookProc(HWINEVENTHOOK hWinEventHook, DWORD event, HWND hwnd, LONG idObject, LONG idChild, DWORD dwEventThread, DWORD dwmsEventTime);
         virtual bool OnMessage(UINT message, W<PERSON><PERSON><PERSON> wParam, <PERSON>AR<PERSON> lParam, LRESULT& result) override;
         void onWindowHookEvent(HWND hwnd, DWORD event);
         void updateWindow();
         RECT getCurrentRectForWindow(HWND hwnd);
         RECT getBorderRect();

      private:
         unsigned int mScreenshareDeviceId;
         const WindowLocation mWindowLocation;
         std::vector<HWINEVENTHOOK> mHookEvents;
         HMODULE dwmapi_library_ = nullptr;
         webrtc::DwmGetWindowAttributeFunc dwm_get_window_attribute_func_ = nullptr;
         webrtc::DwmSetWindowAttributeFunc dwm_set_window_attribute_func_ = nullptr;
      };
   }
}
#endif
#endif

