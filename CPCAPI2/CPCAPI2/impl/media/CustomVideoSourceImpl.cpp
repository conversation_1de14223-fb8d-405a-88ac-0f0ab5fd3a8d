#include "CustomVideoSourceImpl.h"
#include "../util/ThreadUtils.h"

#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>

#include <libyuv/planar_functions.h>

#include <stdint.h>

using namespace webrtc;

namespace CPCAPI2
{
namespace Media
{
CustomVideoSource* CustomVideoSource::Create()
{
   return new CustomVideoSourceImpl();
}

resip::MultiReactor* CustomVideoSourceImpl::mReactor(NULL);
bool CustomVideoSourceImpl::mDoCaptureLoop = false;
std::set<CustomVideoSourceImpl*> CustomVideoSourceImpl::mSources;
resip::Mutex* CustomVideoSourceImpl::mReactorMtx(new resip::Mutex());

CustomVideoSourceImpl::CustomVideoSourceImpl()
   : mExternalCapture(NULL),
     mCapturing(false)
{
   int stride_y, stride_uv = 0;
   webrtc::Calc16ByteAlignedStride(1920, &stride_y, &stride_uv);
   mCapturedFrameI420.CreateEmptyFrame(1920, 1080, stride_y, stride_uv, stride_uv);
   libyuv::I420Rect(mCapturedFrameI420.buffer(webrtc::kYPlane), mCapturedFrameI420.stride(webrtc::kYPlane),
      mCapturedFrameI420.buffer(webrtc::kUPlane), mCapturedFrameI420.stride(webrtc::kUPlane),
      mCapturedFrameI420.buffer(webrtc::kVPlane), mCapturedFrameI420.stride(webrtc::kVPlane),
      0, 0, mCapturedFrameI420.width(), mCapturedFrameI420.height(),
      0, 128, 128);
}

CustomVideoSourceImpl::~CustomVideoSourceImpl()
{
}

// reactor thread
void CustomVideoSourceImpl::AddSource()
{
   mSources.insert(this);
   if (!mDoCaptureLoop)
   {
      mReactor->post(resip::resip_static_bind(&CustomVideoSourceImpl::CaptureLoop));
   }
}

// reactor thread
void CustomVideoSourceImpl::RemoveSource()
{
   mSources.erase(this);
}

// reactor thread
void CustomVideoSourceImpl::CaptureLoop()
{
   CPCAPI2::WaitableTimerWrapper wtw(40);
   mDoCaptureLoop = true;

   while (mDoCaptureLoop)
   {
      for (CustomVideoSourceImpl* cvs : mSources)
      {
         cvs->SendFrameImpl();
      }
      mReactor->detach();
      if (mSources.size() == 0)
      {
         break;
      }
      if (wtw.Wait() < 0)
      {
         assert(0);
         break;
      }
   }
   mDoCaptureLoop = false;
}

// reactor thread
void CustomVideoSourceImpl::SendFrameImpl()
{
   if (mCapturedFrameI420.IsZeroSize() || mCapturedFrameI420.video_frame_buffer_const()->HasOneRef())
   {
      mCapturedFrameI420.set_render_time_ms(0);
      mCapturedFrameI420.set_ntp_time_ms(0);
      mExternalCapture->IncomingFrame(mCapturedFrameI420);
   }
}

void CustomVideoSourceImpl::SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture)
{
   mExternalCapture = externalCapture;
}

int CustomVideoSourceImpl::Start()
{
   resip::Lock lck(*mReactorMtx);
   if (mReactor == NULL)
   {
      mReactor = new resip::MultiReactor("CustomVideoSourceImpl");
      mReactor->start();
   }
   mReactor->post(resip::resip_bind(&CustomVideoSourceImpl::AddSource, this));
   return 0;
}

int CustomVideoSourceImpl::Stop()
{
   mReactor->execute(resip::resip_bind(&CustomVideoSourceImpl::RemoveSource, this));
   return 0;
}


}
}
