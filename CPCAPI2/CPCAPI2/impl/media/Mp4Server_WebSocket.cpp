#include "brand_branded.h"

#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
#include "Mp4Server_WebSocket.h"
#include "Mp4Fragmenter.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
Mp4ServerWebSocket::Mp4ServerWebSocket()
   : mPhone(NULL), mServerThread(NULL)
{
}

Mp4ServerWebSocket::~Mp4ServerWebSocket()
{
}

void Mp4ServerWebSocket::StartServer(CPCAPI2::Phone* phone)
{
   mPhone = phone;
   mServerThread = new std::thread([&]() {
      try {
         // Set logging settings
         mWebSockServer.set_access_channels(websocketpp::log::alevel::none);
         mWebSockServer.clear_access_channels(websocketpp::log::alevel::frame_payload);

         // Initialize Asio
         mWebSockServer.init_asio();

         // Register our message handler
         mWebSockServer.set_open_handler(std::bind(&Mp4ServerWebSocket::on_open, this, &mWebSockServer, std::placeholders::_1));

         // TODO: Make configurable
         mWebSockServer.listen(boost::asio::ip::tcp::v4(), 9005);

         // Start the server accept loop
         mWebSockServer.start_accept();

         // Start the ASIO io_service run loop
         mWebSockServer.run();

         std::cout << "web streamer thread exit..." << std::endl;
      }
      catch (websocketpp::exception const & e) {
         std::cout << e.what() << std::endl;
      }
      catch (...) {
         std::cout << "other exception" << std::endl;
      }
   });
}

void Mp4ServerWebSocket::StopServer()
{
   mWebSockServer.get_io_service().post([&]() {
      websocketpp::lib::error_code ec;
      //mWebSockServer.stop_listening(ec);
      mWebSockServer.stop();
   });
   mServerThread->join();
   delete mServerThread;
}

int Mp4ServerWebSocket::CloseConnection(const websocketpp::connection_hdl& hdl)
{
   std::error_code ec;
   mWebSockServer.close(hdl, websocketpp::close::status::going_away, "shutdown", ec);
   return 0;
}

void Mp4ServerWebSocket::AddObserver(Mp4ServerWebSocketObserver* obs)
{
   mObs.insert(obs);
}

void Mp4ServerWebSocket::Send(websocketpp::connection_hdl hdl, int videoStreamHandle, int bitstreamId, int bitstreamType, const unsigned char* bitstream, unsigned int bitstreamLen)
{
   if (connection_ptr conn_ptr = mWebSockServer.get_con_from_hdl(hdl))
   {
      message_ptr msg_ptr = conn_ptr->get_message(websocketpp::frame::opcode::binary, bitstreamLen + 3);
      u_short vshdl = (u_short)videoStreamHandle;
      msg_ptr->append_payload(&vshdl, 1);
      u_short bsid = (u_short)bitstreamId;
      msg_ptr->append_payload(&bsid, 1);
      u_short bstyp = (u_short)bitstreamType;
      msg_ptr->append_payload(&bstyp, 1);
      msg_ptr->append_payload(bitstream, bitstreamLen);
      conn_ptr->send(msg_ptr);

#if 0
      std::stringstream ssfilename;
      ssfilename << "bitstream_debug_stream" << bitstreamId << "_" << videoStreamHandle << ".mp4";
      std::ofstream mp4file;
      mp4file.open(ssfilename.str(), std::ios::out | std::ios::app | std::ios::binary);
      if (mp4file.is_open()) {
         mp4file.write((const char*)bitstream, bitstreamLen);
         mp4file.close();
      }
#endif
   }
}

void Mp4ServerWebSocket::on_open(server* s, websocketpp::connection_hdl hdl)
{
   DebugLog(<< "JsonApiServerWebSocket::on_open()");
   std::error_code ec;
   if (conn_ptr conn = mWebSockServer.get_con_from_hdl(hdl, ec))
   {
      if (!ec)
      {
         Mp4ServerSession* session = new Mp4ServerSession(dynamic_cast<PhoneInterface*>(mPhone), this, hdl);
         conn->set_close_handler(std::bind(&Mp4ServerSession::on_close, session, &mWebSockServer, std::placeholders::_1));
         conn->set_message_handler(std::bind(&Mp4ServerSession::on_message, session, &mWebSockServer, std::placeholders::_1, std::placeholders::_2));
         std::lock_guard<std::mutex> lck(mMapLock);
         mSessions[hdl] = session;
      }
   }

   //std::lock_guard<std::mutex> lck(mMapLock);

   //// request key frames
   //std::set<Mp4ServerWebSocketObserver*>::iterator itObs = mObs.begin();
   //for (; itObs != mObs.end(); ++itObs)
   //{
   //   (*itObs)->onWebsocketOpen();
   //}

   ////mConnections[hdl] = std::shared_ptr<ConnInfo>(new ConnInfo(hdl));

   //auto itBs = mBitstreamMap.begin();
   //for (; itBs != mBitstreamMap.end(); ++itBs)
   //{
   //   send(hdl, itBs->second->bitstreamId, itBs->second->bitstreamType, &itBs->second->mFtypMoov[0], itBs->second->mFtypMoov.size());
   //}

#if 0
   std::ofstream mp4file;
   mp4file.open("bitstream_debug6.mp4", std::ios::out | std::ios::app | std::ios::binary);
   if (mp4file.is_open()) {
      mp4file.write((const char*)&mFtypMoov[0], mFtypMoov.size());
      mp4file.close();
   }
#endif
}

void Mp4ServerWebSocket::onBitstreamStarted(int bitstreamId, const CPCAPI2::Media::BitstreamStartedEvent& args)
{
}

void Mp4ServerWebSocket::onBitstreamEnded(int bitstreamId, const CPCAPI2::Media::BitstreamEndedEvent& args)
{
}

void Mp4ServerWebSocket::onBitstreamData(const CPCAPI2::Media::BitstreamDataEvent& args, Mp4Fragmenter* fragmenter)
{
   try {
      std::lock_guard<std::mutex> lck(mMapLock);

      auto itBs = mBitstreamMap.find(args.videoStreamHandle);
      if (itBs == mBitstreamMap.end())
      {
         BitstreamState* bs = new BitstreamState();
         bs->bitstreamId = args.bitstreamId;
         bs->bitstreamType = args.bitstreamMediaType;
         mBitstreamMap[args.videoStreamHandle].push_back(bs);
         itBs = mBitstreamMap.find(args.videoStreamHandle);
      }
      BitstreamState* bitstreamState = NULL;
      auto itBsItm = itBs->second.begin();
      for (; itBsItm != itBs->second.end(); ++itBsItm)
      {
         if ((*itBsItm)->bitstreamId == args.bitstreamId)
         {
            bitstreamState = *itBsItm;
         }
      }
      if (bitstreamState == NULL)
      {
         return;
      }

      if (bitstreamState->mFtypMoov.empty())
      {
         bitstreamState->mFtypMoov.resize(args.size);
         memcpy(&bitstreamState->mFtypMoov[0], args.data, args.size);
      }

      sess_list::iterator it = mSessions.begin();
      for (; it != mSessions.end(); ++it)
      {
         it->second->onBitstreamData(args, fragmenter);
      }
#if 0
      std::ofstream mp4file;
      mp4file.open("bitstream_debug3.mp4", std::ios::out | std::ios::app | std::ios::binary);
      if (mp4file.is_open()) {
         mp4file.write((const char*)args.data, args.size);
         mp4file.close();
      }
#endif
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "Echo failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
}

std::vector<Mp4ServerWebSocket::BitstreamState*> Mp4ServerWebSocket::GetBitstreamState(int videoStream)
{
   std::vector<BitstreamState*> ret;
   {
      std::lock_guard<std::mutex> lck(mMapLock);
      auto itBs = mBitstreamMap.find(videoStream);
      if (itBs != mBitstreamMap.end())
      {
         ret = itBs->second;
      }
   }
   return ret;
}

void Mp4ServerWebSocket::RequestKeyFrame(int videoStreamHandle)
{
   std::set<Mp4ServerWebSocketObserver*>::iterator itObs = mObs.begin();
   for (; itObs != mObs.end(); ++itObs)
   {
      (*itObs)->onWebsocketOpen(videoStreamHandle);
   }
}

void Mp4ServerWebSocket::RemoveSession(websocketpp::connection_hdl hdl)
{
   std::lock_guard<std::mutex> lck(mMapLock);
   mSessions.erase(hdl);
}
   
Mp4ServerSession::Mp4ServerSession(CPCAPI2::PhoneInterface* masterSdkPhone, Mp4ServerWebSocket* transport, const websocketpp::connection_hdl& conn) :
   mConn(conn),
   mMasterPhone(masterSdkPhone),
   mTransport(transport)
{
}

Mp4ServerSession::~Mp4ServerSession()
{
}

void Mp4ServerSession::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(msg->get_payload().c_str());

   if (jsonRequest->HasParseError())
   {
      WarningLog(<< "Invalid request format, parse error occured:" << jsonRequest->GetParseError() << "Aborting decode.");
      return;
   }

   if (!jsonRequest->HasMember("videoStream"))
   {
      WarningLog(<< "Missing moduleId. Aborting decode.");
      return;
   }

   const rapidjson::Value& moduleIdVal = (*jsonRequest)["videoStream"];
   if (!moduleIdVal.IsInt())
   {
      WarningLog(<< "videoStream is not an Int. Aborting decode.");
      return;
   }

   int videoStream = moduleIdVal.GetInt();
   mStreamStateMap[videoStream].push_back(new PerConnectionStreamState(videoStream, 0));
   mStreamStateMap[videoStream].push_back(new PerConnectionStreamState(videoStream, 1));

   // request key frames
   mTransport->RequestKeyFrame(videoStream);

   std::vector<Mp4ServerWebSocket::BitstreamState*> bitstreamState = mTransport->GetBitstreamState(videoStream);
   auto itBss = bitstreamState.begin();
   for (; itBss != bitstreamState.end(); ++itBss)
   {
      Mp4ServerWebSocket::BitstreamState* bss = *itBss;
      mTransport->Send(mConn, videoStream, bss->bitstreamId, bss->bitstreamType, &bss->mFtypMoov[0], bss->mFtypMoov.size());
   }
}

void Mp4ServerSession::handleMp4ServerMessage(server* s, websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc)
{
}

void Mp4ServerSession::on_close(server* s, websocketpp::connection_hdl hdl)
{
   DebugLog(<< "Mp4ServerSession::on_close()");
   mTransport->RemoveSession(hdl);
   delete this;
}

void Mp4ServerSession::close()
{
   mTransport->CloseConnection(mConn);
}

void Mp4ServerSession::onBitstreamData(const CPCAPI2::Media::BitstreamDataEvent& args, Mp4Fragmenter* fragmenter)
{
   auto itStrMap = mStreamStateMap.find(args.videoStreamHandle);
   if (itStrMap != mStreamStateMap.end())
   {
      auto itStr = itStrMap->second.begin();
      for (; itStr != itStrMap->second.end(); ++itStr)
      {
         PerConnectionStreamState* pcss = *itStr;
         if (pcss->bitstreamId == args.bitstreamId)
         {
            if (args.sap)
            {
               pcss->mWaitingForSap = false;
            }
            if (!pcss->mWaitingForSap)
            {
               mTransport->Send(mConn, args.videoStreamHandle, args.bitstreamId, args.bitstreamMediaType, args.data, args.size);
            }
            break;
         }
      }
   }
}

}

}

#endif
