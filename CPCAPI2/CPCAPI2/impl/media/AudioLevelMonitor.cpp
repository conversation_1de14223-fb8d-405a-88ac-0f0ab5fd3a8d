#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)

#include "AudioLevelMonitor.h"
#include "PlaySoundDevice.h"
#include "util/LogSubsystems.h"

#include <voe_base.h>
#include <voe_volume_control.h>
#include <voe_file.h>

#include <rutil/Data.hxx>
#include <rutil/Logger.hxx>

#include <cstdlib>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA
#define AUDIO_LEVEL_MONITOR_SAMPLE_LEVELS_TIMER 1

namespace CPCAPI2
{
namespace Media
{
	
#define MAX_LOGARITHM 77
//represent rought conversion to logarithmic scale with round to int. The equation to get values is y=2*10*log(i)+1. There i is input value [0,32768].
//The minimal value in table is 3 to avoid noise detection.  
const int KConvLogMas[MAX_LOGARITHM][2] =
{
{ 1, 1}, { 3, 3}, { 5, 5}, { 6, 6}, { 7, 7}, { 8, 8}, { 10, 10}, { 11, 11},
{ 12, 12}, { 14, 14}, { 15, 15}, { 17, 17}, { 19, 29}, { 22, 22}, { 25, 25}, { 28, 28}, { 31, 31}, { 35, 32},
{ 39, 33}, { 44, 34}, { 50, 35}, { 56, 36}, { 63, 37}, { 70, 38}, { 79, 39}, { 89, 40}, { 100, 41}, { 112, 42}, 
{ 125, 43},{ 141, 44},{ 158, 45},{ 177, 46},{ 199, 47},{ 223, 48},{ 251, 49},{ 281, 50},{ 316, 51}, { 354, 52},
{ 398, 53},{ 446, 54},{ 501, 55},{ 562, 56},{ 630, 57},{ 707, 58},{ 794, 59},{ 891, 60},{ 1000, 61},{ 1122, 62},
{ 1258, 63},{ 1412, 64},{ 1584, 65},{ 1778, 66},{ 1995, 67},{ 2238, 68},{ 2511, 69},{ 2818, 70},{ 3162, 71}, { 3548, 72},
{ 3981, 73},{ 4466, 74},{ 5011, 75},{ 5623, 76},{ 6309, 77},{ 7079, 78},{ 7943, 79},{ 8912, 80},{ 10000, 81}, { 11220, 82},
{ 12589, 83},{ 14125, 84},{ 15848, 85},{ 17782, 86},{ 19952, 87},{ 22387, 88},{ 25118, 89},{ 28183, 90}, { 31622, 91}
};
	
	
AudioLevelMonitor::AudioLevelMonitor() 
   : mReactor(NULL),
   mOwnReactor(true),
   mObserver(NULL), 
   mDevice(NULL), 
   mVoEVolumeControl(NULL),
   mInputLevelMonitorChannel(-1), 
   mMonitoringInputLevel(false), 
   mMonitoringOutputLevel(false), 
   mLastInputLevel(0), 
   mNeedTrailingInputLevel(false),
   mCallChannel(-1),
   mSampleLevelsTimer(NULL)
{
}

AudioLevelMonitor::AudioLevelMonitor(resip::MultiReactor* reactor, webrtc::VoEVolumeControl* voeVolumeControl) 
   : mReactor(reactor),
   mOwnReactor(false),
   mObserver(NULL), 
   mDevice(NULL), 
   mVoEVolumeControl(voeVolumeControl),
   mInputLevelMonitorChannel(-1), 
   mMonitoringInputLevel(false), 
   mMonitoringOutputLevel(false), 
   mLastInputLevel(0), 
   mNeedTrailingInputLevel(false),
   mCallChannel(-2),
   mSampleLevelsTimer(new resip::DeadlineTimer<resip::MultiReactor>(*reactor))
{
}

AudioLevelMonitor::~AudioLevelMonitor()
{
   mReactor->execute(resip::resip_bind(&AudioLevelMonitor::shutdownDevice, this));
 
   if (mSampleLevelsTimer != NULL)
   {
      mSampleLevelsTimer->cancel();
      mReactor->safeDelete(mSampleLevelsTimer);
      mSampleLevelsTimer = NULL;
   }
   
   if (mOwnReactor)
   {
      mReactor->destroy();
   }
   
   mReactor = NULL;
}

void AudioLevelMonitor::destroy()
{
   if (mOwnReactor)
   {
      destroyImpl();
   }
   else
   {
      mReactor->post(resip::resip_bind(&AudioLevelMonitor::destroyImpl, this));
   }
}

void AudioLevelMonitor::destroyImpl()
{
   delete this;
}

void AudioLevelMonitor::start(AudioLevelObserver* observer, AudioInterface* audio)
{
   mObserver = observer;
   mAudio = audio;
   if (mReactor == NULL)
   {
      mOwnReactor = true;
      mReactor = new resip::MultiReactor();
      mReactor->start();
      mSampleLevelsTimer = new resip::DeadlineTimer<resip::MultiReactor>(*mReactor);
   }
}

void AudioLevelMonitor::initDevice()
{
   if (mDevice == NULL)
   {
      mDevice = new PlaySoundDevice();
      if (mDevice->init(this, webrtc::kAudioPlatformDefault, /* USAGE_VOICE_COMMUNICATION */ 2) != 0)
      {
         ErrLog(<< "AudioLevelMonitor::initDevice() - failed to init PlaySoundDevice");
      }
      mVoEVolumeControl = mDevice->voe_volume_control();
   }
}

void AudioLevelMonitor::shutdownDevice()
{
   mObserver = NULL;
   stopInputLevelMonitoringImpl();
   stopOutputLevelMonitoringImpl();
   delete mDevice;
   mDevice = NULL;
}

void AudioLevelMonitor::startInputLevelMonitoring(unsigned int deviceId)
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::startInputLevelMonitoringImpl, this, deviceId));
}

void AudioLevelMonitor::stopInputLevelMonitoring()
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::stopInputLevelMonitoringImpl, this));
}

void AudioLevelMonitor::startOutputLevelMonitoring(unsigned int deviceId, const resip::Data& file)
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::startOutputLevelMonitoringImpl, this, deviceId, file));
}

void AudioLevelMonitor::startOutputLevelMonitoringForChannel(int voe_channel)
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::startOutputLevelMonitoringForChannelImpl, this, voe_channel));
}

void AudioLevelMonitor::stopOutputLevelMonitoring()
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::stopOutputLevelMonitoringImpl, this));
}

void AudioLevelMonitor::stopOutputLevelMonitoringForChannel(int voe_channel)
{
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::stopOutputLevelMonitoringForChannelImpl, this, voe_channel));
}

void AudioLevelMonitor::startInputLevelMonitoringImpl(unsigned int deviceId)
{
   if (mMonitoringInputLevel)
   {
      return;
   }
   if (mCallChannel == -1)
   {
      initDevice();
      if (mDevice->voe_hardware()->SetRecordingDevice(deviceId) != 0)
      {
         return;
      }
      mInputLevelMonitorChannel = mDevice->voe_base()->CreateChannel();
      if (mInputLevelMonitorChannel < 0)
      {
         return;
      }
      if (mDevice->voe_base()->StartSend(mInputLevelMonitorChannel) != 0)
      {
         return;
      }
      // no need to try and send out packets for audio level monitoring
      if (mDevice->voe_base()->SetOnHoldStatus(mInputLevelMonitorChannel, true, webrtc::kHoldSendOnly) != 0)
      {
         assert(false);
      }
   }
   else
   {
      mInputLevelMonitorChannel = mCallChannel;
   }
   mMonitoringInputLevel = true;
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::sampleLevels, this));
}

void AudioLevelMonitor::stopInputLevelMonitoringImpl()
{
   mMonitoringInputLevel = false;
   if (mCallChannel == -1 && mDevice != NULL)
   {
      mDevice->voe_base()->StopSend(mInputLevelMonitorChannel);
      mDevice->voe_base()->StopPlayout(mInputLevelMonitorChannel);
      mDevice->voe_base()->DeleteChannel(mInputLevelMonitorChannel);
   }
   mInputLevelMonitorChannel = -1;
   if (!mMonitoringOutputLevel)
   {
      mSampleLevelsTimer->cancel();
   }
}

void AudioLevelMonitor::startOutputLevelMonitoringImpl(unsigned int deviceId, const resip::Data& file)
{
   if (mMonitoringOutputLevel)
   {
      return;
   }
   if (mCallChannel == -1)
   {
      initDevice();
      if (mDevice->voe_hardware()->SetPlayoutDevice(deviceId) != 0)
      {
         return;
      }
      int outputLevelMonitorChannel = mDevice->voe_base()->CreateChannel();
      if (outputLevelMonitorChannel < 0)
      {
         return;
      }
      if (mDevice->voe_base()->StartPlayout(outputLevelMonitorChannel) != 0)
      {
         return;
      }
      if (mDevice->voe_file()->StartPlayingFileLocally(outputLevelMonitorChannel, file.c_str(), true, webrtc::kFileFormatWavFile) != 0)
      {
         return;
      }
      mOutputLevelMonitorChannels.insert(outputLevelMonitorChannel);
   }
   else
   {
      // you should have used startOutputLevelMonitoringForChannel()
      assert(false);
   }
   mMonitoringOutputLevel = true;
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::sampleLevels, this));
}

void AudioLevelMonitor::startOutputLevelMonitoringForChannelImpl(int voe_channel)
{
   if (mMonitoringOutputLevel && mOutputLevelMonitorChannels.find(voe_channel) != mOutputLevelMonitorChannels.end())
   {
      return;
   }
   mCallChannel = voe_channel;
   mOutputLevelMonitorChannels.insert(voe_channel);
   mMonitoringOutputLevel = true;
   mReactor->post(resip::resip_bind(&AudioLevelMonitor::sampleLevels, this));
}

void AudioLevelMonitor::stopOutputLevelMonitoringImpl()
{
   if (mOutputLevelMonitorChannels.size() > 0)
   {
      int outputLevelMonitorChannel = *mOutputLevelMonitorChannels.begin();
      if (mDevice != NULL)
      {
         mDevice->voe_file()->StopPlayingFileLocally(outputLevelMonitorChannel);
         mDevice->voe_base()->StopPlayout(outputLevelMonitorChannel);
         mDevice->voe_base()->DeleteChannel(outputLevelMonitorChannel);
      }
      mOutputLevelMonitorChannels.erase(outputLevelMonitorChannel);
   }
   mMonitoringOutputLevel = (mOutputLevelMonitorChannels.size() > 0);

   if (!mMonitoringOutputLevel && !mMonitoringInputLevel && mObserver != NULL)
   {
      mSampleLevelsTimer->cancel();
   }
}

void AudioLevelMonitor::stopOutputLevelMonitoringForChannelImpl(int voe_channel)
{
   mOutputLevelMonitorChannels.erase(voe_channel);
   mLastOutputLevel.erase(voe_channel);
   mMonitoringOutputLevel = (mOutputLevelMonitorChannels.size() > 0);

   if (!mMonitoringOutputLevel && !mMonitoringInputLevel)
   {
      mSampleLevelsTimer->cancel();
   }
}

int AudioLevelMonitor::doLogarithmicConvert(int val)
{
	if(val < KConvLogMas[0][0])
		return 0;
	
	if(val > KConvLogMas[MAX_LOGARITHM-1][0])
		return 100;
	
	for(int i=0; i<MAX_LOGARITHM-2; i++)
	{
		if(val>=KConvLogMas[i][0] && val<KConvLogMas[i+1][0])
		{
			return KConvLogMas[i][1];
		}
	}
	return 100;
}

void AudioLevelMonitor::sampleLevels()
{
   unsigned int inputLevel = 0;
   unsigned int outputLevel = 0;
   bool inputLevelDidChange = false;
   if (mMonitoringInputLevel)
   {
      if (mVoEVolumeControl->GetSpeechInputLevelFullRange(inputLevel) == 0)
      {
#ifdef ANDROID
         inputLevel = doLogarithmicConvert(inputLevel);
#else
         inputLevel = (inputLevel*100) >> 15;
#endif
      }
      int inputDiff = (int)mLastInputLevel - (int)inputLevel;
      if (mMonitoringInputLevel && std::abs(inputDiff) >= 5)
      {
         mNeedTrailingInputLevel = true;
         if (std::abs(inputDiff) > 10)
         {
            //std::cout << "input level adjusted (was: " << inputLevel << ")" << std::endl;
            inputLevel += (inputDiff / 2);
            //std::cout << "                     (now: " << inputLevel << ")" << std::endl;
         }
         mLastInputLevel = (mMonitoringInputLevel ? inputLevel : 0);
         if (!mMonitoringOutputLevel)
         {
            mObserver->onAudioLevels(-1, inputLevel, outputLevel);
         }
         else
         {
            inputLevelDidChange = true;
         }
      }
      else if((mNeedTrailingInputLevel) && 
#ifdef ANDROID
         inputLevel < 20 
#else
         inputLevel == 0
#endif
         ) //prevent flashing
      {
         mNeedTrailingInputLevel = false;
         inputLevel = 0;
         if (!mMonitoringOutputLevel)
            mObserver->onAudioLevels(-1, inputLevel, outputLevel);
         else
            inputLevelDidChange = true;	  
      }
   }
   if (mMonitoringOutputLevel)
   {
      std::set<int> failedChans;

      if (mOutputLevelMonitorChannels.empty() && inputLevelDidChange)
      {
         mObserver->onAudioLevels(-1, inputLevel, outputLevel);
      }

      for (std::set<int>::iterator itChans = mOutputLevelMonitorChannels.begin(); itChans != mOutputLevelMonitorChannels.end(); ++itChans)
      {

         int outputLevelMonitorChannel = *itChans;
         if (mVoEVolumeControl->GetSpeechOutputLevelFullRange(outputLevelMonitorChannel, outputLevel) == 0)
         {
            outputLevel = (outputLevel * 100) >> 15;
            int outputDiff = (int)mLastOutputLevel[outputLevelMonitorChannel] - (int)outputLevel;
            if (std::abs(outputDiff) >= 5 || inputLevelDidChange)
            {
               if (std::abs(outputDiff) > 10)
               {
                  outputLevel += (outputDiff / 2);
               }
               mLastOutputLevel[outputLevelMonitorChannel] = (mMonitoringOutputLevel ? outputLevel : 0);
               mObserver->onAudioLevels(outputLevelMonitorChannel, inputLevel, outputLevel);
            }
         }
         else
         {
            failedChans.insert(outputLevelMonitorChannel);
         }
      }
      for (std::set<int>::iterator itChans = failedChans.begin(); itChans != failedChans.end(); ++itChans)
      {
         mOutputLevelMonitorChannels.erase(*itChans);
         mLastOutputLevel.erase(*itChans);
      }
   }
   if (mMonitoringInputLevel || mMonitoringOutputLevel)
   {
      int period = (mCallChannel == -1 ? 20 : 200);
      mSampleLevelsTimer->expires_from_now(period);
      mSampleLevelsTimer->async_wait(this, AUDIO_LEVEL_MONITOR_SAMPLE_LEVELS_TIMER, NULL);
   }
}

void AudioLevelMonitor::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == AUDIO_LEVEL_MONITOR_SAMPLE_LEVELS_TIMER)
   {
      sampleLevels();
   }
}

void AudioLevelMonitor::onSystemAudioServiceError(int errorLevel)
{
  if (NULL != mAudio)
  {
    mAudio->onSystemAudioServiceError(errorLevel);
  }
}

}
}
#endif // CPCAPI2_BRAND_MEDIA_MODULE
