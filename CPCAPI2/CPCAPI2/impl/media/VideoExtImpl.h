#pragma once

#if !defined(CPCAPI2_VIDEO_EXT_IMPL_H)
#define CPCAPI2_VIDEO_EXT_IMPL_H

#include "cpcapi2defs.h"
#include "media/video/VideoHandler.h"
#include "media/video/Video.h"
#include "media/MediaManager.h"
#include "VideoInterface.h"
#include "ScreenShare.h"
#include "CustomVideoSource.h"
#include "phone/PhoneInterface.h"

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class Mp4Fragmenter;
class VideoFrameServerWebSocket;

class VideoExtImpl
{
public:
   VideoExtImpl(webrtc_recon::MediaStackImpl* mediaStack, VideoInterface *videoInterface, PhoneInterface* phone);
   virtual ~VideoExtImpl();

   webrtc_recon::MediaStackImpl* media_stack() const { return mMediaStack; }

   int setHandler(VideoHandler* handler);
   void postCallback(resip::ReadCallbackBase* fp);

   VideoHandler* handler() const { return mMediaHandler; }

   //int connectReceiveStreamToMp4Output(int recv_channel, VideoBitstreamHandler* bitstreamHandler);
   int setBrokenKeyFrameSupportEnabled(bool enable);
   int setKeyFramePeriod(int periodInFrames);

   int startWebsocketServer(VideoWebsocketServerHandler* handler);
   int startWebsocketServerForReceiveStream(int recv_channel);
   int stopWebsocketServer();
   int createSnapshotForReceiveStream(int recv_channel, const cpc::string& fileName, VideoSnapshotHandler* snapshotHandler);

private:

private:
   webrtc_recon::MediaStackImpl* mMediaStack;
   VideoInterface* mVideoInterface;
   VideoHandler* mMediaHandler;
   PhoneInterface* mPhone;
#if (CPCAPI2_BRAND_MP4_SUPPORT == 1)
   Mp4Fragmenter* mMp4Fragmenter;
#endif
   VideoFrameServerWebSocket* mVideoFrameServer;
};
}
}
#endif // CPCAPI2_VIDEO_EXT_IMPL_H
