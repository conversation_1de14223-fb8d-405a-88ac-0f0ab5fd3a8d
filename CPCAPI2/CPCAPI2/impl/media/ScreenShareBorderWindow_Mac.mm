#import "ScreenShareBorderWindow_Mac.h"

#if defined( __APPLE__ )
#include "TargetConditionals.h"
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)

#include <webrtc/modules/desktop_capture/mac/desktop_configuration.h>
#include <webrtc/modules/desktop_capture/mac/window_list_utils.h>

@interface ScreenShareBorderView : NSView
{
   // rects of other windows that are currently on top
   NSArray *overlappingRects;
}
@end

@implementation ScreenShareBorderView

- (void) setSize:(CGRect)rect intersectingRects:(NSArray *)rects
{
   if (!CGRectEqualToRect([self frame], rect))
   {
      [self setFrame:rect];
   }
   
   if (![rects isEqualToArray:overlappingRects])
   {
      overlappingRects = rects;
      // force redraw since nothing else may have changed
      [self setNeedsDisplay:YES];
   }
}

- (void)drawRect:(NSRect)dirtyRect {
   
   // Draw the border around the entire view
   NSBezierPath* border = [NSBezierPath bezierPathWithRect:[self bounds]];
   NSColor* borderColor = [NSColor redColor];
   [borderColor set];
   border.lineWidth = 5;
   [border stroke];
   
   // Overlap transparent rects over areas that intersect
   for (NSValue *rect in overlappingRects) {
      CGRect holeRect = [rect rectValue];
      holeRect = [self convertRect:holeRect fromView:self.superview];
      CGRect intersection = CGRectIntersection(holeRect, dirtyRect);
      [[NSColor clearColor] setFill];
      NSRectFill(intersection);
   }
}
@end

@interface ScreenShareBorderWindow ()
{
   webrtc::MacDisplayConfiguration primaryDisplayConfig;
   webrtc::MacDisplayConfiguration displayConfig;
   ScreenShareBorderView* borderView;
   NSTimer *resizeTimer;
   NSArray* previousAboveWindows;
}

@property (atomic) unsigned int screenShareDeviceId;
@property (atomic) BOOL isSharingWindow;
@end

@implementation ScreenShareBorderWindow

-(id)initWithScreenShareDeviceId:(unsigned int)deviceId
{
   webrtc::MacDesktopConfiguration desktopConfig = webrtc::MacDesktopConfiguration::GetCurrent(webrtc::MacDesktopConfiguration::BottomLeftOrigin);
   NSRect borderRect = NSZeroRect;
   
   for (const auto display : desktopConfig.displays)
   {
      if (display.is_primary)
      {
         primaryDisplayConfig = display;
         break;
      }
   }

   // check if we're sharing a window or monitor
   if (!webrtc::IsWindowOnScreen(deviceId))
   {
      _isSharingWindow = false;
      const webrtc::MacDisplayConfiguration* dConfig = desktopConfig.FindDisplayConfigurationById(deviceId);
      
      if (dConfig)
      {
         displayConfig = *dConfig;
         webrtc::DesktopRect screenRect = displayConfig.bounds;
         borderRect = NSMakeRect(screenRect.left(), screenRect.top(), screenRect.width(),screenRect.height());
      }
   }
   else
   {
      _isSharingWindow = true;
      
      // returns GC coordinates
      webrtc::DesktopRect windowRect = webrtc::GetWindowBounds(deviceId);
      CGRect windowRectCG = CGRectMake(windowRect.left(), windowRect.top(), windowRect.width(), windowRect.height());

      webrtc::DesktopRect windowRectDT = [self convertCGToNSDesktopCoordinates:windowRectCG primaryDisplayRect:primaryDisplayConfig.bounds];

      // find the screen this window is displayed on
      for (const auto display : desktopConfig.displays)
      {
         // the Mac behavior seems to be to put the window on the display based on the location of
         // the title bar (remember the origin is at the bottom so we use the bottom of the rectangle)
         if (display.bounds.Contains(webrtc::DesktopVector(windowRectDT.left(), windowRectDT.bottom())))
         {
            displayConfig = display;
            webrtc::DesktopRect screenRect = display.bounds;
            borderRect = NSMakeRect(screenRect.left(), screenRect.top(), screenRect.width(),screenRect.height());
            break;
         }
      }
   }
   
   if (self = [super initWithContentRect:borderRect styleMask:NSWindowStyleMaskBorderless backing:NSBackingStoreBuffered defer:YES])
   {
      _screenShareDeviceId = deviceId;
      
      [self setOpaque:NO];
      [self setBackgroundColor:[NSColor clearColor]];
      [self setMovableByWindowBackground:TRUE];
      [self setHasShadow:NO];
      [self setLevel:CGShieldingWindowLevel() + 1];
      [self setIgnoresMouseEvents:YES];
      [self setCanHide:NO];
      [self setSharingType:NSWindowSharingNone];
      
      borderView = [[ScreenShareBorderView alloc] init];
      borderView.wantsLayer = YES;
      
      if (!_isSharingWindow)
      {
         // when sharing entire screen, the view is static
         [borderView setSize:borderRect intersectingRects:nil];
      }
      
      [self setContentView:borderView];
      
      return self;
   }
   
   return nil;
}


// =============================================================
//
// Coordinate Systems:
//
// CG - the origin is at the top left of the primary display and extends down and right
// NS desktop - the origin is at the bottom left of the primary display and extends up and right
// NS display - the origin is at the bottom left of the specific display and extends up and right
//
// NOTE: The webrtc::DesktopRect expects left<right and top<bottom so in some of the coordinate
// systems the top of a window is actually the bottom value.
//


// Convert CG desktop to NS display
-(CGRect) convertCGToNSDisplayCoordinates: (CGRect) oldRect primaryDisplayRect: (webrtc::DesktopRect) primaryDisplayRect displayRect: (webrtc::DesktopRect) displayRect
{
   CGFloat oldX = oldRect.origin.x;
   CGFloat oldY = oldRect.origin.y;
   CGFloat newX = oldX - displayRect.left();
   CGFloat newY = primaryDisplayRect.height() - (oldY + oldRect.size.height) - displayRect.top();
   CGRect newRect = oldRect;
   newRect.origin.x = newX;
   newRect.origin.y = newY;
   return newRect;
}

// Convert CG desktop to NS desktop
-(webrtc::DesktopRect) convertCGToNSDesktopCoordinates: (CGRect) oldRect primaryDisplayRect: (webrtc::DesktopRect) primaryDisplayRect
{
   int32_t newBottom = primaryDisplayRect.height() - oldRect.origin.y;
   int32_t newTop = primaryDisplayRect.height() - (oldRect.origin.y + oldRect.size.height);
   return webrtc::DesktopRect::MakeLTRB(oldRect.origin.x, newTop, oldRect.origin.x + oldRect.size.width, newBottom);
}

- (void) updateBorder
{
   if (!_isSharingWindow)
      return;
   
   // Grab list of any windows that may be obstructing the border
   // so we can "fake" that the border is same level as current capture window
   NSArray *windowList = (id)CFBridgingRelease(CGWindowListCopyWindowInfo(kCGWindowListOptionOnScreenAboveWindow | kCGWindowListOptionIncludingWindow | kCGWindowListOptionOnScreenOnly | kCGWindowListExcludeDesktopElements, _screenShareDeviceId));
   
   if ([windowList isEqualToArray:previousAboveWindows])
      return;
   
   previousAboveWindows = windowList;
   
   CGRect selectedBounds = CGRectZero;
   NSMutableArray* windowsRects = nil;
   bool displayHasChanged = false;
   bool updateWindowFrame = false;
   int loopCount = 0;
   do
   {
      loopCount++;
      
      if (displayHasChanged)   // might have changed last time through the loop
      {
         // NOTE: This code assumes the primary display won't change during a sharing session.
         
         webrtc::MacDesktopConfiguration desktopConfig = webrtc::MacDesktopConfiguration::GetCurrent(webrtc::MacDesktopConfiguration::BottomLeftOrigin);

         // returns GC coordinates
         webrtc::DesktopRect windowRect = webrtc::GetWindowBounds(_screenShareDeviceId);
         CGRect windowRectCG = CGRectMake(windowRect.left(), windowRect.top(), windowRect.width(), windowRect.height());

         webrtc::DesktopRect windowRectDT = [self convertCGToNSDesktopCoordinates:windowRectCG primaryDisplayRect:primaryDisplayConfig.bounds];

         // find the screen this window is displayed on
         for (const auto display : desktopConfig.displays)
         {
            // the Mac behavior seems to be to put the window on the display based on the location of
            // the title bar (remember the origin is at the bottom so we use the bottom of the rectangle)
            if (display.bounds.Contains(webrtc::DesktopVector(windowRectDT.left(), windowRectDT.bottom())))
            {
               displayConfig = display;
               displayHasChanged = false;
               updateWindowFrame = true;
               break;
            }
         }
      }

      windowsRects = [NSMutableArray new];
      for (NSDictionary* window in windowList)
      {
         CGWindowID windowId = [[window objectForKey:(id)kCGWindowNumber] unsignedIntValue];
         NSString* ownerName = [window objectForKey:(id)kCGWindowOwnerName];
         NSString* winName = [window objectForKey:(id)kCGWindowName];
         NSInteger windowLayer = [[window objectForKey:(id)kCGWindowLayer] integerValue];
         
         // The Dock covers entire screen so we ignore it unless its showing LaunchPad
         // Notification center also covers entire screen and we can't determine actual window size
         if (([ownerName isEqualToString:@"Dock"] && ![winName isEqualToString:@"LPSpringboard"])
             || [ownerName isEqualToString:@"Notification Center"])
            continue;
         
         // Ignore system/overlay windows
         if (windowLayer > kCGScreenSaverWindowLevel)
            continue;
         
         // returns CG coordinates
         CGRect windowRectCG;
         CGRectMakeWithDictionaryRepresentation((CFDictionaryRef)[window objectForKey:(id)kCGWindowBounds], &windowRectCG);

         CGRect windowRectNS = [self convertCGToNSDisplayCoordinates:windowRectCG primaryDisplayRect:primaryDisplayConfig.bounds displayRect:displayConfig.bounds];

         if (windowId == _screenShareDeviceId)
         {
            // save the bounds of captured window so we can draw border
            selectedBounds = windowRectNS;
            
            webrtc::DesktopRect windowRectDT = [self convertCGToNSDesktopCoordinates:windowRectCG primaryDisplayRect:primaryDisplayConfig.bounds];
            
            // the Mac behavior seems to be to put the window on the display based on the location of
            // the title bar (remember the origin is at the bottom so we use the bottom of the rectangle)
            if (!displayConfig.bounds.Contains(webrtc::DesktopVector(windowRectDT.left(), windowRectDT.bottom())))
            {
               // the window has been moved to a different display
               displayHasChanged = true;
            }

            break;
         }
         else
         {
            [windowsRects addObject:[NSValue valueWithRect:windowRectNS]];
         }
      }
      
      // first loop detects display change, second loop handles it, if the displayHasChanged is still set
      // it means the window is partially off the desktop and so we'll keep using the last display
   } while (displayHasChanged && loopCount <= 2);
   
   // Filter to keep only rectangles for windows that are actually occluding the captured window
   NSMutableArray* intersecting = [NSMutableArray new];
   
   BOOL isFullyObscured = NO;
   for (NSValue *v in windowsRects) {
      
      CGRect r = [v rectValue];
      if (CGRectContainsRect(r, selectedBounds))
      {
         // captured window is not visible at all on screen
         isFullyObscured = YES;
         break;
      }
      else if (CGRectIntersectsRect(r, selectedBounds))
      {
         // partially obscured
         [intersecting addObject:v];
      }
   }
   
   // update border view on main thread
   dispatch_async(dispatch_get_main_queue(), ^{
      
      if (updateWindowFrame)
      {
         // the window has been moved to a different display so we have to adjust the coordinates
         // used by the border for the new display and this seems to do the trick
         NSRect rect = NSMakeRect(displayConfig.bounds.left(), displayConfig.bounds.top(), displayConfig.bounds.width(), displayConfig.bounds.height());
         [self setFrame:rect display:true];
      }
      
      if (isFullyObscured)
      {
         [borderView setHidden:YES];
      }
      else
      {
         if (!CGRectEqualToRect([borderView frame], selectedBounds))
         {
            // smooth out flickering by hiding the border until resize is done
            [borderView setHidden:YES];
            
            if (resizeTimer)
            {
               [resizeTimer invalidate];
               resizeTimer = nil;
            }
            
            resizeTimer = [NSTimer scheduledTimerWithTimeInterval:0.2 repeats:NO block:^(NSTimer * _Nonnull timer)
            {
               [borderView setSize:selectedBounds intersectingRects:intersecting];
               [borderView setHidden:NO];
            }];
         }
         else
         {
            [borderView setHidden:NO];
            [borderView setSize:selectedBounds intersectingRects:intersecting];
         }
      }
   });
   
}


@end
#endif
#endif
