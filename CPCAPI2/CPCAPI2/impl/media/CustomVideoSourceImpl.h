#ifndef CPCAPI2_CUSTOM_VIDEO_SOURCE_IMPL_H
#define CPCAPI2_CUSTOM_VIDEO_SOURCE_IMPL_H

#include "CustomVideoSource.h"

#include <rutil/MultiReactor.hxx>

#include <vie_capture.h>

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

namespace CPCAPI2
{
namespace Media
{
class CustomVideoSourceImpl : public CPCAPI2::Media::CustomVideoSource
{
public:
   CustomVideoSourceImpl();
   virtual ~CustomVideoSourceImpl();

   void* operator new(size_t i)
   {
#if defined(ANDROID)
      return memalign(folly::hardware_destructive_interference_size, i);
#else
      return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
   }

   void operator delete(void* p)
   {
#if defined(ANDROID)
      free(p);
#else
      _mm_free(p);
#endif
   }

   // CustomVideoSource
   virtual void SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture);
   virtual int Start();
   virtual int Stop();

private:
   void SendFrameImpl();
   void AddSource();
   void RemoveSource();
   static void CaptureLoop();

private:
   static resip::MultiReactor* mReactor;
   static bool mDoCaptureLoop;
   static std::set<CustomVideoSourceImpl*> mSources;
   static resip::Mutex* mReactorMtx;

   webrtc::ViEExternalCapture* mExternalCapture;
   webrtc::VideoFrame mCapturedFrameI420;
   bool mCapturing;
};
}
}
#endif // CPCAPI2_CUSTOM_VIDEO_SOURCE_IMPL_H