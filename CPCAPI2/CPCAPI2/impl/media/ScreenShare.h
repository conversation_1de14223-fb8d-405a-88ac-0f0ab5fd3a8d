#ifndef CPCAPI2_SCREENSHARE_H
#define CPCAPI2_SCREENSHARE_H

#include <vector>
#include <string>

#define DEFAULT_SCREENSHARE_MAX_FRAMERATE 30

namespace webrtc
{
   class ViEExternalCapture;
}

namespace CPCAPI2
{
   namespace Media
   {
      class ScreenShare;

      struct ScreenShareMonitorListItem
      {
         std::string title;
         unsigned int id;
      };

      struct ScreenShareWindowListItem
      {
         std::string title;
         unsigned int id;
      };

      class ScreenShareObserver
      {
      public:
         ScreenShareObserver() {}
         virtual ~ScreenShareObserver() {}

         virtual void onScreenShareError(ScreenShare*) = 0;
      };

      class ScreenShare
      {
      public:
         static ScreenShare* Create();
         virtual ~ScreenShare() {}

         virtual int Start(unsigned int deviceId) = 0;
         virtual void SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture) = 0;
         virtual void SetObserver(ScreenShareObserver* observer) = 0;
         virtual void SetMaxCaptureFrameRate(unsigned int fps) = 0;
         virtual int Stop() = 0;
         virtual void CaptureThread() = 0;
         virtual void InitializeCapture() = 0;
         virtual void ResetScreenCap() = 0;
         virtual void PerformCapture() {}
         virtual int GetMonitors(std::vector<ScreenShareMonitorListItem>* monitorList) = 0;
         virtual int GetWindows(std::vector<ScreenShareWindowListItem>* windowList) = 0;
         virtual double getFrameCaptureInterval() = 0;
         
      protected:
         ScreenShare() {}
      };
   }
}

#endif // CPCAPI2_SCREENSHARE_H
