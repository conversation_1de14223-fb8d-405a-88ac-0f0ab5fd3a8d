#pragma once

#if !defined(CPCAPI2_VIDEO_JSON_INTERFACE_H)
#define CPCAPI2_VIDEO_JSON_INTERFACE_H

#include "interface/public/media/video/Video.h"
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/media/VideoJsonApi.h"
#include "interface/experimental/video_ext/VideoExt.h"
#include "media/video/VideoHandler.h"
#include "media/VideoSyncHandler.h"
#include "videostreaming/VideoStreaming.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class VideoJsonServerInterface : public CPCAPI2::Media::VideoHandler,
                                           public CPCAPI2::Media::VideoJsonApi,
                                           public CPCAPI2::Media::VideoSyncHandler,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::Media::ScreenshareDeviceList<PERSON><PERSON><PERSON>,
                                           public CPCAPI2::PhoneModule
{
public:
   VideoJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~VideoJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via VideoHandler
   virtual int onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args) OVERRIDE;
   virtual int onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args) OVERRIDE;

   // ScreenshareDeviceListHandler
   virtual void onScreenshareDeviceList(const CPCAPI2::Media::ScreenshareDeviceListEvent& evt) OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleQueryDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartCapture(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopCapture(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetVideoMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartScreenshare(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopScreenshare(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleQueryScreenshareDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetScreenshareCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartCapturePreview(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopCapturePreview(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetPreferredResolution(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetLocalVideoPreviewResolution(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::Media::Video* mVideoMgr;
   CPCAPI2::VideoStreaming::VideoStreamingManager* mVideoStreamingMgr;
   CPCAPI2::VideoStreaming::VideoStreamHandle mVideoStreamHandle;
};
}
}
#endif // CPCAPI2_VIDEO_JSON_INTERFACE_H
