#pragma once

#if !defined(CPCAPI2_AUDIO_JSON_INTERFACE_H)
#define CPCAPI2_AUDIO_JSON_INTERFACE_H

#include "interface/public/media/audio/Audio.h"
#ifdef ANDROID
#include "interface/public/media/audio/AndroidAudio.h"
#endif
#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/media/AudioJsonApi.h"
#include "media/audio/AudioHandler.h"
#include "media/AudioSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace Media
{
class AudioJsonServerInterface : public CPCAPI2::Media::AudioHandler,
                                 public CPCAPI2::Media::AudioJsonApi,
                                 public CPCAPI2::Media::AudioSyncHandler,
                                 public CPCAPI2::JsonApi::JsonApiServerModule,
                                 public CPCAPI2::PhoneModule
{
public:
   AudioJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~AudioJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via AudioHandler
   virtual int onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args) OVERRIDE;
   virtual int onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args) OVERRIDE;
   virtual int onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip) OVERRIDE;
   virtual int onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip) OVERRIDE;
   virtual int onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args) OVERRIDE;
   virtual int onAudioDeviceLevelChange(const CPCAPI2::Media::AudioDeviceLevelChangeEvent& args) OVERRIDE;
   virtual int onSystemAudioServiceError(const CPCAPI2::Media::SystemAudioServiceErrorEvent& args) OVERRIDE { return CPCAPI2::kSuccess; }

   void fireAudioDeviceLevelChange(const CPCAPI2::Media::AudioDeviceLevelChangeEvent& args);
   
private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   void handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleQueryDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetRender(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetMicMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartMonitoringCaptureDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopMonitoringCaptureDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetEchoCancellationMode(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetSpeakerMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetSpeakerVolume(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handlePlaySound(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopPlaySound(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleQueryDeviceVolume(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStartMonitoringRenderDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStopMonitoringRenderDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetAudioSessionActivated(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetCodecPriority(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleQueryCodecList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSetCodecEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::Media::Audio* mAudioMgr;
#ifdef ANDROID
   CPCAPI2::Media::AndroidAudio* mAndroidAudioMgr;
#endif
};
}
}
#endif // CPCAPI2_AUDIO_JSON_INTERFACE_H
