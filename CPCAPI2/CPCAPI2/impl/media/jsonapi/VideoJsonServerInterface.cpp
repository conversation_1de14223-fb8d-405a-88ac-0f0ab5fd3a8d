#include "brand_branded.h"

#if (CPCAPI2_BRAND_VIDEO_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "VideoJsonServerInterface.h"
#include "media/VideoInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

#include <MediaStackImpl.hxx>
#include <MixerImpl.hxx>
#include <RtpStreamImpl.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

using CPCAPI2::PeerConnection::SessionDescription;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA
#define JSON_MODULE "VideoJsonProxy"

namespace CPCAPI2
{
namespace Media
{
VideoJsonServerInterface::VideoJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mVideoMgr(NULL),
     mVideoStreamingMgr(NULL),
     mVideoStreamHandle((CPCAPI2::VideoStreaming::VideoStreamHandle)-1)
{
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(phone);
   mVideoMgr = CPCAPI2::Media::Video::getInterface(mm);
   VideoInterface* videoIf = dynamic_cast<VideoInterface*>(mVideoMgr);
   videoIf->addSdkObserver(this);

   mVideoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(phone);

   mFunctionMap["setHandler"] = std::bind(&VideoJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryDeviceList"] = std::bind(&VideoJsonServerInterface::handleQueryDeviceList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setCaptureDevice"] = std::bind(&VideoJsonServerInterface::handleSetCaptureDevice, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startCapture"] = std::bind(&VideoJsonServerInterface::handleStartCapture, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopCapture"] = std::bind(&VideoJsonServerInterface::handleStopCapture, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setVideoMute"] = std::bind(&VideoJsonServerInterface::handleSetVideoMute, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startScreenshare"] = std::bind(&VideoJsonServerInterface::handleStartScreenshare, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopScreenshare"] = std::bind(&VideoJsonServerInterface::handleStopScreenshare, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryScreenshareDeviceList"] = std::bind(&VideoJsonServerInterface::handleQueryScreenshareDeviceList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setScreenshareCaptureDevice"] = std::bind(&VideoJsonServerInterface::handleSetScreenshareCaptureDevice, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startCapturePreview"] = std::bind(&VideoJsonServerInterface::handleStartCapturePreview, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopCapturePreview"] = std::bind(&VideoJsonServerInterface::handleStopCapturePreview, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setPreferredResolution"] = std::bind(&VideoJsonServerInterface::handleSetPreferredResolution, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setLocalVideoPreviewResolution"] = std::bind(&VideoJsonServerInterface::handleSetLocalVideoPreviewResolution, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

VideoJsonServerInterface::~VideoJsonServerInterface()
{
   dynamic_cast<VideoInterface*>(mVideoMgr)->removeSdkObserver(this);
}

void VideoJsonServerInterface::Release()
{
}

void VideoJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void VideoJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int VideoJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&VideoJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void VideoJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   DebugLog(<< "VideoJsonServerInterface handling " << funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int VideoJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   post(resip::resip_bind(&VideoJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void VideoJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(dynamic_cast<VideoInterface*>(mVideoMgr)->media_stack()->mixer());
   if (mixerImpl->rtpStreams(recon::MediaStack::MediaType_Video).size() == 0)
   {
      mVideoMgr->stopCapture();
      CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
      CPCAPI2::Media::VideoExt* videoExt = CPCAPI2::Media::VideoExt::getInterface(mm);
      videoExt->stopScreenshare();
      videoExt->stopWebsocketServer();
   }
}

int VideoJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   return kSuccess;
}

int VideoJsonServerInterface::handleQueryDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mVideoMgr->queryDeviceList();
   return kSuccess;
}

int VideoJsonServerInterface::handleSetCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int deviceId = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(deviceId));
   mVideoMgr->setCaptureDevice(deviceId);
   return kSuccess;
}

int VideoJsonServerInterface::handleStartCapture(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mVideoMgr->startCapture();
   return kSuccess;
}

int VideoJsonServerInterface::handleStopCapture(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mVideoMgr->stopCapture();
   return kSuccess;
}

int VideoJsonServerInterface::handleSetVideoMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(enabled));
   mVideoMgr->setVideoMute(enabled);
   return kSuccess;
}

int VideoJsonServerInterface::handleStartScreenshare(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   CPCAPI2::Media::VideoExt::getInterface(mm)->startScreenshare(NULL);
   return kSuccess;
}

int VideoJsonServerInterface::handleStopScreenshare(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   CPCAPI2::Media::VideoExt::getInterface(mm)->stopScreenshare();
   return kSuccess;
}

int VideoJsonServerInterface::handleQueryScreenshareDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   bool includeMonitors = true;
   bool includeWindows = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(includeMonitors), JSON_VALUE(includeWindows));
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   CPCAPI2::Media::VideoExt::getInterface(mm)->queryScreenshareDeviceList(this, includeMonitors, includeWindows);
   return kSuccess;
}

int VideoJsonServerInterface::handleSetScreenshareCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int deviceId = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(deviceId));
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(mPhone);
   CPCAPI2::Media::VideoExt::getInterface(mm)->setScreenshareCaptureDevice(deviceId);
   return kSuccess;
}

int VideoJsonServerInterface::handleStartCapturePreview(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   if (mVideoStreamHandle != (CPCAPI2::VideoStreaming::VideoStreamHandle) - 1)
   {
      mVideoStreamingMgr->stopVideoStream(mVideoStreamHandle);
   }
   mVideoStreamHandle = mVideoStreamingMgr->createVideoStream();
   CPCAPI2::VideoStreaming::VideoStreamSettings vsSettings;
   vsSettings.cameraCaptureId = 1; // can be anything >= 0
   vsSettings.streamFormat = VideoStreaming::StreamFormat_I420;
   mVideoStreamingMgr->setVideoStreamSettings(mVideoStreamHandle, vsSettings);
   mVideoStreamingMgr->startVideoStream(mVideoStreamHandle);
   JsonFunctionCall(mTransport, "onCapturePreviewStarted", "videoStreamHandle", mVideoStreamHandle);
   return kSuccess;
}

int VideoJsonServerInterface::handleStopCapturePreview(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mVideoStreamingMgr->stopVideoStream(mVideoStreamHandle);
   mVideoStreamHandle = (CPCAPI2::VideoStreaming::VideoStreamHandle)(-1);
   return kSuccess;
}

int VideoJsonServerInterface::handleSetPreferredResolution(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int codecId = 0;
   VideoCaptureResolution resolution = VideoCaptureResolution_Standard;
   JsonDeserialize(functionObjectVal, JSON_VALUE(codecId));
   JsonDeserialize(functionObjectVal, JSON_VALUE(resolution));
   mVideoMgr->setPreferredResolution(codecId, resolution);
   return kSuccess;
}

int VideoJsonServerInterface::handleSetLocalVideoPreviewResolution(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   VideoCaptureResolution resolution = VideoCaptureResolution_Standard;
   JsonDeserialize(functionObjectVal, JSON_VALUE(resolution));
   mVideoMgr->setLocalVideoPreviewResolution(resolution);
   return kSuccess;
}

int VideoJsonServerInterface::onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onVideoDeviceListUpdated", "deviceInfo", args.deviceInfo);
   return kSuccess;
}

int VideoJsonServerInterface::onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onVideoCodecListUpdated", "codecInfo", args.codecInfo);
   return kSuccess;
}

void VideoJsonServerInterface::onScreenshareDeviceList(const CPCAPI2::Media::ScreenshareDeviceListEvent& evt)
{
   JsonFunctionCall(mTransport, "onScreenshareDeviceList", "devices", evt.devices);
}
}
}
#endif
