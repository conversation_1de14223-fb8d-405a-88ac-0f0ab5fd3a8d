#include "brand_branded.h"

#include <interface/experimental/media/AudioJsonApi.h>

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "AudioJsonServerInterface.h"
#endif

namespace CPCAPI2
{
   namespace Media
   {
      AudioJsonApi* AudioJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<AudioJsonServerInterface>(phone, "AudioJsonApi");
#else
         return NULL;
#endif
      }

   }
}
