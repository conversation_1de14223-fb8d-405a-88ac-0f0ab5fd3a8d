#include "brand_branded.h"

#include <interface/experimental/media/VideoJsonApi.h>

#if (CPCAPI2_BRAND_VIDEO_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "VideoJsonServerInterface.h"
#endif

namespace CPCAPI2
{
   namespace Media
   {
      VideoJsonApi* VideoJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<VideoJsonServerInterface>(phone, "VideoJsonApi");
#else
         return NULL;
#endif
      }

   }
}
