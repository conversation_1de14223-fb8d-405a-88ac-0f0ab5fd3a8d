#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "AudioJsonServerInterface.h"
#include "media/AudioInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA
#define JSON_MODULE "AudioJsonProxy"

namespace CPCAPI2
{
namespace Media
{
AudioJsonServerInterface::AudioJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mAudioMgr(NULL)
#ifdef ANDROID
     , mAndroidAudioMgr(NULL)
#endif
{
   CPCAPI2::Media::MediaManager* mm = CPCAPI2::Media::MediaManager::getInterface(phone);
   mAudioMgr = CPCAPI2::Media::Audio::getInterface(mm);
   AudioInterface* audioIf = dynamic_cast<AudioInterface*>(mAudioMgr);
   audioIf->addSdkObserver(this);

#ifdef ANDROID
   mAndroidAudioMgr = CPCAPI2::Media::AndroidAudio::getInterface(mm);
#endif

   mFunctionMap["setHandler"] = std::bind(&AudioJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryDeviceList"] = std::bind(&AudioJsonServerInterface::handleQueryDeviceList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setCaptureDevice"] = std::bind(&AudioJsonServerInterface::handleSetCaptureDevice, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setRenderDevice"] = std::bind(&AudioJsonServerInterface::handleSetRender, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setMicMute"] = std::bind(&AudioJsonServerInterface::handleSetMicMute, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startMonitoringCaptureDeviceLevels"] = std::bind(&AudioJsonServerInterface::handleStartMonitoringCaptureDeviceLevels, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopMonitoringCaptureDeviceLevels"] = std::bind(&AudioJsonServerInterface::handleStopMonitoringCaptureDeviceLevels, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setEchoCancellationMode"] = std::bind(&AudioJsonServerInterface::handleSetEchoCancellationMode, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setSpeakerMute"] = std::bind(&AudioJsonServerInterface::handleSetSpeakerMute, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setSpeakerVolume"] = std::bind(&AudioJsonServerInterface::handleSetSpeakerVolume, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["playSound"] = std::bind(&AudioJsonServerInterface::handlePlaySound, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopPlaySound"] = std::bind(&AudioJsonServerInterface::handleStopPlaySound, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryDeviceVolume"] = std::bind(&AudioJsonServerInterface::handleQueryDeviceVolume, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["startMonitoringRenderDeviceLevels"] = std::bind(&AudioJsonServerInterface::handleStartMonitoringRenderDeviceLevels, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stopMonitoringRenderDeviceLevels"] = std::bind(&AudioJsonServerInterface::handleStopMonitoringRenderDeviceLevels, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setAudioSessionActivated"] = std::bind(&AudioJsonServerInterface::handleSetAudioSessionActivated, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setCodecPriority"] = std::bind(&AudioJsonServerInterface::handleSetCodecPriority, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["queryCodecList"] = std::bind(&AudioJsonServerInterface::handleQueryCodecList, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setCodecEnabled"] = std::bind(&AudioJsonServerInterface::handleSetCodecEnabled, this, std::placeholders::_1, std::placeholders::_2);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

AudioJsonServerInterface::~AudioJsonServerInterface()
{
   dynamic_cast<AudioInterface*>(mAudioMgr)->removeSdkObserver(this);
}

void AudioJsonServerInterface::Release()
{
}

void AudioJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void AudioJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int AudioJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&AudioJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void AudioJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   DebugLog(<< "AudioJsonServerInterface handling " << funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int AudioJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   //post(resip::resip_bind(&AudioJsonServerInterface::handleConnectionClosedImpl, this, conn));
   return kSuccess;
}

void AudioJsonServerInterface::handleConnectionClosedImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn)
{
}

int AudioJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   return kSuccess;
}

int AudioJsonServerInterface::handleQueryDeviceList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mAudioMgr->queryDeviceList();
   return kSuccess;
}

int AudioJsonServerInterface::handleSetCaptureDevice(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int deviceId = 0;
   AudioDeviceRole role = AudioDeviceRole_None;
   JsonDeserialize(functionObjectVal, JSON_VALUE(role), JSON_VALUE(deviceId));
   mAudioMgr->setCaptureDevice(deviceId, role);
   return kSuccess;
}

int AudioJsonServerInterface::handleSetRender(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int deviceId = 0;
   AudioDeviceRole role = AudioDeviceRole_None;
   JsonDeserialize(functionObjectVal, JSON_VALUE(role), JSON_VALUE(deviceId));
   mAudioMgr->setRenderDevice(deviceId, role);
   return kSuccess;
}

int AudioJsonServerInterface::handleSetMicMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(enabled));
   mAudioMgr->setMicMute(enabled);
   return kSuccess;
}

int AudioJsonServerInterface::handleStartMonitoringCaptureDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int deviceId = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(deviceId));
   mAudioMgr->startMonitoringCaptureDeviceLevels(deviceId);
   return kSuccess;
}

int AudioJsonServerInterface::handleStopMonitoringCaptureDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mAudioMgr->stopMonitoringCaptureDeviceLevels();
   return kSuccess;
}

int AudioJsonServerInterface::handleSetEchoCancellationMode(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   AudioDeviceRole role = AudioDeviceRole_None;
   EchoCancellationMode mode = EchoCancellationMode_None;
   JsonDeserialize(functionObjectVal, JSON_VALUE(role), JSON_VALUE(mode));
   mAudioMgr->setEchoCancellationMode(role, mode);
   return kSuccess;
}

int AudioJsonServerInterface::handleSetSpeakerVolume(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   unsigned int volume = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(volume));
   if (volume > 100 ) volume = 100;
   mAudioMgr->setSpeakerVolume(volume);
   return kSuccess;
}

int AudioJsonServerInterface::handlePlaySound(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   AudioDeviceRole role = AudioDeviceRole_None;
   int32_t audioUsage = 3;
   cpc::string resourceUri;
   bool repeat = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(role), JSON_VALUE(resourceUri), JSON_VALUE(repeat), JSON_VALUE(audioUsage));
   PlaySoundHandle handle;
#if ANDROID
   if (functionObjectVal.HasMember("audioUsage"))
   {
      handle = mAndroidAudioMgr->playSound(role, audioUsage, resourceUri, repeat);
   }
   else
#endif
   {
      handle = mAudioMgr->playSound(role, resourceUri, repeat);
   }
   JsonFunctionCall(mTransport, "playSoundResult", JSON_VALUE(handle));
   return kSuccess;
}

int AudioJsonServerInterface::handleStopPlaySound(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   PlaySoundHandle handle = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(handle));
   mAudioMgr->stopPlaySound(handle);
   return kSuccess;
}

int AudioJsonServerInterface::handleSetSpeakerMute(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(enabled));
   mAudioMgr->setSpeakerMute(enabled);
   return kSuccess;
}

int AudioJsonServerInterface::handleQueryDeviceVolume(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mAudioMgr->queryDeviceVolume();
   return kSuccess;
}

int AudioJsonServerInterface::handleStartMonitoringRenderDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   cpc::string resourceUri;
   unsigned int deviceId = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(deviceId), JSON_VALUE(resourceUri));
   mAudioMgr->startMonitoringRenderDeviceLevels(deviceId, resourceUri);
   return kSuccess;
}

int AudioJsonServerInterface::handleStopMonitoringRenderDeviceLevels(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   mAudioMgr->stopMonitoringRenderDeviceLevels();
   return kSuccess;
}

int AudioJsonServerInterface::handleSetAudioSessionActivated(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value& functionObjectVal)
{
   bool activated = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(activated));
   AudioInterface* audioIf = dynamic_cast<AudioInterface*>(mAudioMgr);
   audioIf->setAudioSessionActivated(activated);
   return kSuccess;
}

int AudioJsonServerInterface::handleSetCodecPriority(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int codecId = 0;
   unsigned int priority = 0;
   JsonDeserialize(functionObjectVal, JSON_VALUE(codecId), JSON_VALUE(priority));
   mAudioMgr->setCodecPriority(codecId, priority);
   return kSuccess;
}

int AudioJsonServerInterface::handleQueryCodecList(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mAudioMgr->queryCodecList();
   return kSuccess;
}

int AudioJsonServerInterface::handleSetCodecEnabled(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   unsigned int codecId = 0;
   bool enabled = false;
   JsonDeserialize(functionObjectVal, JSON_VALUE(codecId), JSON_VALUE(enabled));
   mAudioMgr->setCodecEnabled(codecId, enabled);
   return kSuccess;
}

int AudioJsonServerInterface::onAudioDeviceListUpdated(const AudioDeviceListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onAudioDeviceListUpdated", "deviceInfo", args.deviceInfo);
   return kSuccess;
}

int AudioJsonServerInterface::onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args)
{
   JsonFunctionCall(mTransport, "onAudioCodecListUpdated", "codecInfo", args.codecInfo);
   return kSuccess;
}

int AudioJsonServerInterface::onAudioDeviceLevelChange(const CPCAPI2::Media::AudioDeviceLevelChangeEvent& args)
{
   post(resip::resip_bind(&AudioJsonServerInterface::fireAudioDeviceLevelChange, this, args));
   return kSuccess;
}

void AudioJsonServerInterface::fireAudioDeviceLevelChange(const CPCAPI2::Media::AudioDeviceLevelChangeEvent& args)
{
   JsonFunctionCall(mTransport, "onAudioDeviceLevelChange", "inputDeviceLevel", args.inputDeviceLevel, "outputDeviceLevel", args.outputDeviceLevel);
}

int AudioJsonServerInterface::onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   JsonFunctionCall(mTransport, "onPlaySoundComplete", "handle", soundClip);
   return kSuccess;
}

int AudioJsonServerInterface::onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   JsonFunctionCall(mTransport, "onPlaySoundFailure", "handle", soundClip);
   return kSuccess;
}

int AudioJsonServerInterface::onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args)
{
   DebugLog(<< "AudioJsonServerInterface::onAudioDeviceVolume");
   JsonFunctionCall(mTransport, "onAudioDeviceVolume", "micMuted", args.micMuted, "speakerMuted", args.speakerMuted, "micVolumeLevel", args.micVolumeLevel, "speakerVolumeLevel", args.speakerVolumeLevel);
   return kSuccess;
}

}
}
#endif
