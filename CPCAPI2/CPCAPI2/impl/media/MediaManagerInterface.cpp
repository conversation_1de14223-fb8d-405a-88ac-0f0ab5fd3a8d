#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#include "cpcapi2utils.h"
#include "MediaManagerInterface.h"
#include "MediaTransportsReactorFactory.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "media/audio/Audio.h"
#include "AudioInterface.h"
#include "VideoInterface.h"

#include <voe_base.h>
#include <vie_capture.h>

#include <MediaStackImpl.hxx>

#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

#if __APPLE__
#include "TargetConditionals.h"
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
   
void MediaManagerInterface::Release()
{
   delete this;
}

MediaManagerInterface::MediaManagerInterface(Phone* phone, MediaTransportsReactorFactory* mediaTransportsReactorFactory)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mPerfProfile(DevicePerformanceProfile_Desktop),
     mMediaTransportsReactorFac(mediaTransportsReactorFactory)
{
#if TARGET_OS_IPHONE || defined(ANDROID)
   mPerfProfile = DevicePerformanceProfile_Mobile;
#endif

   if (mediaTransportsReactorFactory == NULL)
   {
      mediaTransportsReactorFactory = new MediaTransportsReactorFactoryInterface();
      mediaTransportsReactorFactory->initialize();
   }

   mediaTransportsReactorFactory->addRef();
   mMediaTransportsReactorFac = mediaTransportsReactorFactory;
   mMediaStack.reset(new webrtc_recon::MediaStackImpl(mPhone->getSdkModuleThread(), dynamic_cast<resip::ReactorFactory<resip::HighPerfReactor::TReactorQueue, resip::Resolver<resip::HighPerfReactor> >*>(mediaTransportsReactorFactory)));
}

MediaManagerInterface::~MediaManagerInterface()
{
   mShutdown = true;
   if (mMediaTransportsReactorFac != NULL)
   {
      mMediaTransportsReactorFac->releaseRef();
   }
   interruptProcess();
   mMediaStack.reset();
}

int MediaManagerInterface::updateMediaSettings(const MediaStackSettings& mediaSettings)
{
   ReadCallbackBase* cmd = resip::resip_bind(&MediaManagerInterface::updateMediaSettingsImpl, this, mediaSettings);
   post(cmd);
   return kSuccess;
}

void MediaManagerInterface::updateMediaSettingsImpl(const MediaStackSettings& mediaSettings)
{
   if (mediaSettings.audioLayer != mSettings.audioLayer ||
       mediaSettings.streamType != mSettings.streamType ||
       mediaSettings.audioSource != mSettings.audioSource)
   {
      if (mMediaStack->getRtpStreamCount() == 0)
      {
         shutdownMediaStackImpl();
         mSettings = mediaSettings;
         initializeMediaStackImpl();
      }
      else
      {
         fireError("Cannot update media settings while RTP streams are active");
         return;
      }
   }
   else
   {
      mSettings = mediaSettings;
      webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
      settings.audioLayer = (webrtc::AudioLayers) mSettings.audioLayer;
      mMediaStack->updateMediaSettings(settings);
   }
}


int MediaManagerInterface::setMoHEnabled(bool enabled)
{
   ReadCallbackBase* cmd = resip::resip_bind(&MediaManagerInterface::setMoHEnabledImpl, this, enabled);
   post(cmd);
   return kSuccess;
}
   
void MediaManagerInterface::setMoHEnabledImpl(bool enabled)
{
   InfoLog(<< "MediaManagerInterface::setMoHEnabled=" << enabled);
   mMediaStack->setMoHEnabled(enabled);
}

int MediaManagerInterface::initializeMediaStack(const MediaStackSettings& settings)
{
   mSettings = settings;
   return initializeMediaStack();
}

int MediaManagerInterface::initializeMediaStack()
{
   ReadCallbackBase* cmd = resip::resip_bind(&MediaManagerInterface::initializeMediaStackImpl, this);
   post(cmd);
   return kSuccess;
}

void MediaManagerInterface::initializeMediaStackImpl()
{
   if (!mMediaStack->isInitialized())
   {
      webrtc_recon::MediaStackSettings settings;
      
      // MediaStackSettings constructor should initialize itself to sane defaults.
      // if we want to override, we can here
      
      settings.numAudioEncoderThreads = mSettings.numAudioEncoderThreads;
      settings.audioLayer = (webrtc::AudioLayers) mSettings.audioLayer;
      settings.audioOutputDisabled = mSettings.audioOutputDisabled;

#if TARGET_OS_IPHONE || defined(ANDROID)
      webrtc_recon::WebRtcObjects* tlWebRtcObjs = new webrtc_recon::WebRtcObjects();
      int result = mMediaStack->initialize(tlWebRtcObjs, settings, this);
#else
      thread_local webrtc_recon::WebRtcObjects tlWebRtcObjs;
      int result = mMediaStack->initialize(&tlWebRtcObjs, settings, this);
#endif

      if (result == kError)
      {
         fireError("Media stack could not be initialized");
         return;
      }

      AudioInterface* audio = dynamic_cast<AudioInterface*>(CPCAPI2::Media::Audio::getInterface(this));
      if (audio != NULL)
      {
         audio->initPlaySoundDevicePool();
      }
   }
}

void MediaManagerInterface::shutdownMediaStackImpl()
{
   mMediaStack->shutdownMediaStack();
   
   AudioInterface* audio = dynamic_cast<AudioInterface*>(CPCAPI2::Media::Audio::getInterface(this));
   if (audio != NULL)
   {
      audio->shutdownPlaySoundDevicePool();
   }
}

int MediaManagerInterface::setRtpKeepAliveIntervalSeconds(unsigned long rtpKeepAliveIntervalSeconds)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.rtpKeepAliveIntervalSeconds = rtpKeepAliveIntervalSeconds;
   mMediaStack->updateMediaSettings(settings);
   return kSuccess;
}

int MediaManagerInterface::setRtcpXrVoIPMetricsReportsEnabled(bool enabled)
{
   post(resip::resip_bind(&MediaManagerInterface::setRtcpXrVoIPMetricsReportsEnabledImpl, this, enabled));
   return kSuccess;
}

void MediaManagerInterface::setRtcpXrVoIPMetricsReportsEnabledImpl(bool enabled)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.rtcpXrVoipMetricsEnabled = enabled;
   mMediaStack->updateMediaSettings(settings);
}
   
int MediaManagerInterface::setRtcpXrStatisticsSummaryReportsEnabled(bool enabled)
{
   post(resip::resip_bind(&MediaManagerInterface::setRtcpXrStatisticsSummaryReportsEnabledImpl, this, enabled));
   return kSuccess;
}
   
void MediaManagerInterface::setRtcpXrStatisticsSummaryReportsEnabledImpl(bool enabled)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.rtcpXrStatisticsSummaryEnabled = enabled;
   mMediaStack->updateMediaSettings(settings);
}

int MediaManagerInterface::setDevicePerformanceProfile(DevicePerformanceProfile performanceProfile)
{
   InfoLog(<< "setDevicePerformanceProfile = " << performanceProfile);
   mPerfProfile = performanceProfile;
   AudioInterface* audio = dynamic_cast<AudioInterface*>(CPCAPI2::Media::Audio::getInterface(this));
   if (audio != NULL)
   {
      audio->updatePerformanceProfile(performanceProfile);
   }
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
   VideoInterface* video = dynamic_cast<VideoInterface*>(CPCAPI2::Media::Video::getInterface(this));
   if (video != NULL)
   {
      video->updatePerformanceProfile(performanceProfile);
   }
#endif
   return kSuccess;
}

void MediaManagerInterface::fireError(const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   evt.errorText = errorText;
   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), "MediaManagerInterface", evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), "MediaManagerInterface", evt);
}

PhoneInterface* MediaManagerInterface::phoneInterface()
{
   return mPhone;
}

webrtc_recon::MediaStackImpl* MediaManagerInterface::media_stack() const 
{ 
   return mMediaStack.get(); 
}

std::shared_ptr<webrtc_recon::MediaStackImpl> MediaManagerInterface::media_stack_ptr() const
{
   return mMediaStack;
}

resip::Fifo<resip::ReadCallbackBase>* MediaManagerInterface::callbackFifo()
{
   return &mCallbackFifo;
}

int MediaManagerInterface::process(unsigned int timeout)
{
   // Check this first, some race condition in the unit test.
   if (mShutdown)
      return kMediaModuleDisabled;

   if (!mMediaStack->isInitialized())
   {
      WarningLog(<< "MediaManagerInterface::initializeMediaStack() was not called explicitly");
      initializeMediaStack();
   }
   
   // -1 == no wait
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kMediaModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* MediaManagerInterface::process_test(int timeout)
{
   if (mShutdown)
      return NULL;

   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}
#endif

void MediaManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void MediaManagerInterface::execute(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}
   
void MediaManagerInterface::interruptProcess()
{
   mCallbackFifo.add(new ReadCallbackNoOp);
}
   
void MediaManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}
   
void MediaManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void MediaManagerInterface::onPermissionGranted(int requestCode, CPCAPI2::Permission permission)
{
   if (Permission_Microphone == permission)
   {
      InfoLog(<< "Granted RECORD_AUDIO permission. Restarting audio capture.");
      media_stack()->voe_base()->RecordPermissionGranted();
   }
   else if (Permission_Camera == permission)
   {
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
      InfoLog(<< "Granted CAMERA permission. Querying video devices.");
      // Triggers a refresh since the device list is empty until permission is granted
      media_stack()->vie_capture()->NumberOfCaptureDevices();
      VideoInterface* video = dynamic_cast<VideoInterface*>(CPCAPI2::Media::Video::getInterface(this));
      if (video != NULL)
      {
         video->queryDeviceList();
      }
#endif
   }
}

void MediaManagerInterface::onSystemAudioServiceError(int errorLevel)
{
  AudioInterface* audio = dynamic_cast<AudioInterface*>(CPCAPI2::Media::Audio::getInterface(this));
  if (audio != NULL)
  {
     audio->onSystemAudioServiceError(errorLevel);
  }
}

void MediaManagerInterface::setVqIntervalReportingEventInterval(int intervalSec)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.vqIntervalReportingEventIntervalSec = intervalSec;
   mMediaStack->updateMediaSettings(settings);
}

}
}
#endif
