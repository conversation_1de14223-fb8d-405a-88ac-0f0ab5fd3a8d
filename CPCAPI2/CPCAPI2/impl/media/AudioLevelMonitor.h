#pragma once

#if !defined(CPCAPI2_AUDIO_LEVEL_MONITOR_H)
#define CPCAPI2_AUDIO_LEVEL_MONITOR_H

#include "AudioInterface.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <rutil/Data.hxx>

#include <voe_hardware.h>

namespace webrtc
{
class VoEVolumeControl;
}

namespace CPCAPI2
{
namespace Media
{
class PlaySoundDevice;
class AudioLevelObserver;

class AudioLevelMonitor : public resip::DeadlineTimerHandler,
                                 webrtc::SystemAudioServiceErrorCallback
{
public:
   AudioLevelMonitor();
   AudioLevelMonitor(resip::MultiReactor* reactor, webrtc::VoEVolumeControl* voeVolumeControl);


   void start(AudioLevelObserver* observer, AudioInterface* audio);
   void startInputLevelMonitoring(unsigned int deviceId);
   void stopInputLevelMonitoring();
   void startOutputLevelMonitoring(unsigned int deviceId, const resip::Data& file);
   void startOutputLevelMonitoringForChannel(int voe_channel);
   void stopOutputLevelMonitoring();
   void stopOutputLevelMonitoringForChannel(int voe_channel);
   void destroy();
   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void startInputLevelMonitoringImpl(unsigned int deviceId);
   void stopInputLevelMonitoringImpl();
   void startOutputLevelMonitoringForChannelImpl(int voe_channel);
   void stopOutputLevelMonitoringForChannelImpl(int voe_channel);

   bool isMonitoringOutput() { return mMonitoringOutputLevel; }

   virtual void onSystemAudioServiceError(int errorLevel) OVERRIDE;

private:
   virtual ~AudioLevelMonitor();
   void initDevice();
   void shutdownDevice();

   void startOutputLevelMonitoringImpl(unsigned int deviceId, const resip::Data& file);

   void stopOutputLevelMonitoringImpl();

   void sampleLevels();
   void destroyImpl();

   int doLogarithmicConvert(int val);

private:
   resip::MultiReactor* mReactor;
   bool mOwnReactor;
   AudioLevelObserver* mObserver;
   AudioInterface* mAudio;
   PlaySoundDevice* mDevice;
   webrtc::VoEVolumeControl* mVoEVolumeControl;
   int mInputLevelMonitorChannel;
   std::set<int> mOutputLevelMonitorChannels;
   bool mMonitoringInputLevel;
   bool mMonitoringOutputLevel;
   unsigned int mLastInputLevel;
   bool mNeedTrailingInputLevel;
   std::map<int, unsigned int> mLastOutputLevel;
   int mCallChannel;
   resip::DeadlineTimer<resip::MultiReactor>* mSampleLevelsTimer;
};

class AudioLevelObserver
{
public:
   virtual ~AudioLevelObserver() {}
   virtual void onAudioLevels(int voe_channel, unsigned int inputLevel, unsigned int outputLevel) = 0;
};

}
}

#endif // CPCAPI2_AUDIO_LEVEL_MONITOR_H
