#pragma once

#if !defined(CPCAPI2_AUDIO_IMPL_H)
#define CPCAPI2_AUDIO_IMPL_H

#include "cpcapi2defs.h"
#include "media/audio/AudioHandler.h"
#include "media/MediaManager.h"
#include "../util/DumFpCommand.h"
#include "call/SipConversationManager.h"
#include "AudioInterface.h"
#include "AudioLevelMonitor.h"
#include "PlaySoundDevice.h"
#include "AudioDeviceChangeManagerImpl.h"
#include "../util/STAAssertion.h"

#include <resip/recon/media/Mixer.hxx>

#include <rutil/Fifo.hxx>
#include <map>

#include <rutil/MultiReactor.hxx>

#include <voe_hardware.h>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class PlaySoundHelper;

class AudioImpl : public CPCAPI2::Media::AudioLevelObserver,
                  public CPCAPI2::Media::AudioDevice<PERSON><PERSON>e<PERSON><PERSON><PERSON>,
                  public webrtc::SystemAudioServiceErrorCallback,
                  public recon::MixerObserver,
                  public resip::DeadlineTimerHandler
{
public:
   AudioImpl(webrtc_recon::MediaStackImpl* mediaStack, AudioInterface *audioInterface, resip::MultiReactor& reactor);
   virtual ~AudioImpl();

   int init();
   int initPlaySoundDevicePool();
   int shutdownPlaySoundDevicePool();

   webrtc_recon::MediaStackImpl* media_stack() const { return mMediaStack; }

   int queryDeviceList();
   int setCaptureDevice(unsigned int deviceId, AudioDeviceRole role);
   int setRenderDevice(unsigned int deviceId, AudioDeviceRole role);
   int playSound(PlaySoundHandle h, AudioDeviceRole device, int32_t audioUsage, const cpc::string& resourceUri, bool repeat);
   int stopPlaySound(PlaySoundHandle h);
   bool isPlayingSound() const;
   void stopAllSounds();
   int queryCodecList();
   int setCodecEnabled(unsigned int id, bool enabled);
   int setCodecPriority(unsigned int id, unsigned int priority);
   int setCodecPayloadType(unsigned int codecId, unsigned int payloadType);
   int setTelephoneEventPayloadType(unsigned int payloadType);
   int setMicMute(bool enabled);
   int setSpeakerMute(bool enabled);
   int setMicVolume(unsigned int level);
   int setMicSoftwareVolume( bool enabled, unsigned int level );
   int setSpeakerVolume(unsigned int level);
   int queryDeviceVolume();
   int setEchoCancellationMode(AudioDeviceRole role, EchoCancellationMode mode);
   int setNoiseSuppressionMode(AudioDeviceRole role, NoiseSuppressionMode mode);
   int setVadMode(AudioDeviceRole role, VadMode mode);
   int setGainSettings(const GainSettings& settings);
   int setAudioDscp(unsigned int mediaDscp);
   int setHardwareEchoCancellationEnabled(const bool enabled);
   int setHardwareAutomaticGainControlEnabled(const bool enabled);
   int setHardwareNoiseSuppressionEnabled(const bool enabled);
   int setLowLatencyPlayoutEnabled(const bool enabled);
   int setLowLatencyAudioTrackEnabled(const bool enabled);
   int setAudioSource(int audioSource);
   int connectAudioStreams(int recvStreamId, int sendStreamId);
   int disconnectAudioStreams(int recvStreamId, int sendStreamId);

   int activatePlayAndRecordMode(bool activate);
   int setAudioSessionActivated(bool activated);
   int setUseQosFastlane(bool useQosFastlane, bool useWithDscp);
   int setAudioInterruptionsHandlingEnabled(bool enabled);
   
   int setAudioDeviceFile(const cpc::string& inputAudiofileNameUtf8, const cpc::string& outputAudiofileNameUtf8);
   int setMicAGCEnabled(bool enabled);

   int setHandler(AudioHandler* handler);
   void postCallback(resip::ReadCallbackBase* fp);

   AudioHandler* handler() const { return mMediaHandler; }

   void handlePlaySoundComplete(PlaySoundHandle psh);
   void handlePlaySoundFailure(PlaySoundHandle psh);

   // .jjg. hack -- here for iOS only
   virtual void OnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle);

   int startMonitoringCaptureDeviceLevelsImpl(unsigned int deviceId);
   int stopMonitoringCaptureDeviceLevelsImpl();
   int startMonitoringRenderDeviceLevelsImpl(unsigned int deviceId, const cpc::string& resourceUri);
   int stopMonitoringRenderDeviceLevelsImpl();
   virtual void onAudioLevels(int voe_channel, unsigned int inputLevel, unsigned int outputLevel) OVERRIDE;
   virtual void onSystemAudioServiceError(int errorLevel) OVERRIDE;

   void updatePerformanceProfile(DevicePerformanceProfile perfProfile);

   PlaySoundDevicePool& playSoundDevicePool() { return mPlaySoundDevicePool; }

   AudioInterface* audioInterface();

   // MixerObserver
   virtual void onRtpStreamAdded(const std::shared_ptr<recon::RtpStream>& ms) OVERRIDE;
   virtual void onRtpStreamRemoved(const std::shared_ptr<recon::RtpStream>& ms) OVERRIDE;

private:
   int getWebRtcDeviceIndexForRenderDeviceRole(AudioDeviceRole role) const;
   int getWebRtcDeviceIndexForRenderCpcapiDeviceId(int cpcapiDeviceId) const;
   int getWebRtcDeviceIndexForCaptureCpcapiDeviceId(int cpcapiDeviceId) const;
   void ReactorOnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle);

   // AudioDeviceChangeHandler
   int onAudioDefaultDeviceChange(AudioDeviceType) OVERRIDE;
   int onAudioDeviceChange() OVERRIDE;

   bool isCpcapiPredefinedAudioDeviceId(int deviceId) const;
   int cpcapiPredefinedAudioDeviceIdToWebrtcDeviceIndex(unsigned int cpcapiDeviceId) const;
   int setRenderDeviceImpl(unsigned int deviceId, AudioDeviceRole role);

      // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:
   webrtc_recon::MediaStackImpl* mMediaStack;
   cpc::vector<AudioDeviceInfo> mPlayoutDeviceList;   // index is used as identity so content and order must be maintained
   cpc::vector<AudioDeviceInfo> mRecordingDeviceList; // index is used as identity so content and order must be maintained
   cpc::vector<AudioDeviceInfo> mAudioDeviceList;
   AudioInterface *mAudioInterface;
   AudioHandler* mMediaHandler;
   typedef std::map<PlaySoundHandle, std::shared_ptr<PlaySoundHelper> > PlaySoundHelperMap;
   PlaySoundHelperMap mPlaySoundHelpers;
   cpc::vector<AudioCodecInfo> mAudioCodecList;
   resip::MultiReactor& mReactor;
   typedef std::map<AudioDeviceRole, unsigned int > DeviceRoleMap;
   DeviceRoleMap mDeviceRoleMap;
   AudioLevelMonitor* mAudioLevelMonitor;
   bool mReportingInputAudioLevels;
   bool mReportingOutputAudioLevels;
   PlaySoundDevicePool mPlaySoundDevicePool;
   AudioDeviceChangeManagerImpl* mAudioDeviceChangeManager;
   AudioDeviceChangeManagerImpl::AudioDeviceChangeMangerImplHandle mAudioDeviceChangeImplHandle;
   STAAssertion mMainSdkThreadCheck;

   struct SoundProperties
   {
      PlaySoundHandle h;
      AudioDeviceRole role;
      int32_t audioUsage;
      cpc::string resourceUri;
      bool repeat;
   };
   typedef std::map<AudioDeviceRole, SoundProperties > ActiveSoundsMap;
   ActiveSoundsMap mActiveSoundsMap;

   resip::DeadlineTimer<resip::MultiReactor>* mRecordingDeviceActivityTimer;
   bool mIsRecordingDeviceActive;
};
}
}
#endif // CPCAPI2_AUDIO_IMPL_H
