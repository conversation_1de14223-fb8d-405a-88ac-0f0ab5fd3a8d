#pragma once

#if !defined(CPCAPI2_MEDIA_MANAGER_INTERFACE_H)
#define CPCAPI2_MEDIA_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include "media/MediaManager.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "../phone/PhoneModule.h"
#include "../experimental/phone/Permissions.h"
#include "media/MediaManagerInternal.h"

#include <rutil/Fifo.hxx>
#include <rutil/HighPerfReactor.hxx>
#include <resip/dum/DumCommand.hxx>

#include "webrtc/voice_engine/include/voe_hardware.h"

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
class PhoneInterface;

namespace Media
{

typedef enum
{
   MediaDscp_BestEffort = 0,
   MediaDscp_Background,
   MediaDscp_Video,
   MediaDscp_Voice,
   MediaDscp_Control,
}MediaDscpSettings;
   
class MediaManagerImpl;
class MediaTransportsReactorFactory;

class MediaManagerInterface : public MediaManager,
                              public MediaManagerInternal,
                              public PhoneModule,
                              public webrtc::SystemAudioServiceErrorCallback
#ifdef CPCAPI2_AUTO_TEST
                            , public AutoTestProcessor
#endif
{
public:
   MediaManagerInterface(Phone* phone, MediaTransportsReactorFactory* mediaTransportsReactorFactory=NULL);
   virtual ~MediaManagerInterface();
   virtual void Release() OVERRIDE;

   virtual int initializeMediaStack(const MediaStackSettings& settings) OVERRIDE;
   virtual int initializeMediaStack() OVERRIDE;
   virtual int updateMediaSettings(const MediaStackSettings& mediaSettings) OVERRIDE;

   virtual int setRtpKeepAliveIntervalSeconds(unsigned long rtpKeepAliveIntervalSeconds) OVERRIDE;
   virtual int setMoHEnabled(bool enabled) OVERRIDE;
   virtual int setDevicePerformanceProfile(DevicePerformanceProfile performanceProfile) OVERRIDE;

   virtual int setRtcpXrVoIPMetricsReportsEnabled(bool enabled) OVERRIDE;
   virtual int setRtcpXrStatisticsSummaryReportsEnabled(bool enabled) OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE;
   
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

   virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;

   webrtc_recon::MediaStackImpl* media_stack() const;
   std::shared_ptr<webrtc_recon::MediaStackImpl> media_stack_ptr() const;

   resip::Fifo<resip::ReadCallbackBase>* callbackFifo();
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

   PhoneInterface* phoneInterface();

   DevicePerformanceProfile getDevicePerformanceProfile() const { return mPerfProfile; }
   
   void onPermissionGranted(int requestCode, CPCAPI2::Permission permission);

   void onSystemAudioServiceError(int errorLevel) OVERRIDE;

   void setVqIntervalReportingEventInterval(int intervalSec);

private:
   void initializeMediaStackImpl();
   void shutdownMediaStackImpl();
   void updateMediaSettingsImpl(const MediaStackSettings& mediaSettings);
   void fireError(const cpc::string& errorText);
   void setRtcpXrVoIPMetricsReportsEnabledImpl(bool enabled);
   void setRtcpXrStatisticsSummaryReportsEnabledImpl(bool enabled);
   void setMoHEnabledImpl(bool enabled);
private:
   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
   std::shared_ptr<webrtc_recon::MediaStackImpl> mMediaStack;
   std::function<void(void)> mCbHook;
   MediaStackSettings mSettings;
   DevicePerformanceProfile mPerfProfile;
   MediaTransportsReactorFactory* mMediaTransportsReactorFac;
};
}
}
#endif // CPCAPI2_MEDIA_MANAGER_INTERFACE_H
