#if !defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0
#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)

#include "PlaySoundHelper.h"
#include "PlaySoundDevice.h"

#include "../util/resource_to_stream.h"
#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"
#include "../util/dtmf_tone_helper.h"
#include "../util/cpc_logger.h"
#include "AudioImpl.h"
#include "MediaManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include <MediaStackImpl.hxx>

#include <voe_base.h>
#include <voe_dtmf.h>
#include <voe_hardware.h>

#include <rutil/Data.hxx>
#include <rutil/Log.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

namespace CPCAPI2
{
namespace Media
{
   PlaySoundHelper::PlaySoundHelper(AudioImpl* audio, resip::MultiReactor& reactor) :
      mAudio(audio),
      mChannel(-1),
      mHandle(0xffffffff),
      mReactor(reactor),
      mTimer(reactor)
   {
   }

   PlaySoundHelper::~PlaySoundHelper()
   {
      if (mChannel >= 0 && mDevice.get() != NULL)
      {
         (*mDevice)->voe_base()->StopPlayout(mChannel);
         (*mDevice)->voe_base()->DeleteChannel(mChannel);
         mDevice.reset();
      }
   }

   void PlaySoundHelper::stop()
   {
      mTimer.cancel();
      if (mDevice.get() != NULL)
      {
         (*mDevice)->voe_dtmf()->StopPlayingDtmfTone();
         (*mDevice)->voe_file()->StopPlayingFileLocally(mChannel);
      }
   }

   int PlaySoundHelper::playSound(PlaySoundHandle h, int webrtcDeviceIdx, int32_t audioUsage, const cpc::string& resourceUri, bool repeat)
   {
      InfoLog(<< "playSound - " << resourceUri.c_str());
      if (mAudio == NULL)
      {
         return -1;
      }

      if (resourceUri.empty())
      {
         ErrLog(<< "playSound - resourceUri empty");
         postPlaySoundFailure(mHandle);
         return -1;
      }

      if (mDevice == NULL)
      {
         mDevice = mAudio->playSoundDevicePool().getPlaySoundDevice(this, audioUsage);
         if (mDevice.get() == NULL)
         {
            ErrLog(<< "playSound - could not create PlaySoundDevice");
            postPlaySoundFailure(mHandle);
            return -1;
         }
      }

      if (webrtcDeviceIdx >= -2)
      {
         // note: webrtcDeviceIdx == -1 just means "use the system default device"
         if ((*mDevice)->voe_hardware()->SetPlayoutDevice(webrtcDeviceIdx) != 0)
         {
            // Some platforms (ex. Android) don't support setting a default device (i.e. webrtcDeviceIdx < 0) so they return an error
            // We can ignore these errors
            if (webrtcDeviceIdx >= 0)
            {
               ErrLog(<< "playSound - unable to SetPlayoutDevice(..) with idx " << webrtcDeviceIdx);
               return -1;
            }
         }
      }

      assert(mHandle == 0xffffffff);
      mHandle = h;

      mChannel = (*mDevice)->voe_base()->CreateChannel();

      if (mChannel < 0)
      {
         ErrLog(<< "playSound - could not CreateChannel");
         postPlaySoundFailure(mHandle);
         return -1;
      }

      if ((*mDevice)->voe_base()->StartPlayout(mChannel) != 0)
      {
         ErrLog(<< "playSound - could not StartPlayout");
         postPlaySoundFailure(mHandle);
         return -1;
      }

      if ((*mDevice)->voe_file()->IsPlayingFileLocally(mChannel) != 0)
      {
         ErrLog(<< "playSound - file is already playing");
         postPlaySoundFailure(mHandle);
         return -1;
      }

      if ((*mDevice)->voe_file()->RegisterLocalFileEventObserver(mChannel, *this) != 0)
      {
         ErrLog(<< "playSound - could not RegisterLocalFileEventObserver");
         postPlaySoundFailure(mHandle);
         return -1;
      }

#if _WIN32
#if 0 //WINAPI_FAMILY_ONE_PARTITION( WINAPI_FAMILY_DESKTOP_APP, WINAPI_PARTITION_APP )
      if (ResourceInStream::IsResource(resourceUri))
      {
         ResourceInStream* inStream = new ResourceInStream(resourceUri, repeat);
         inStream->Init();
         if ((*mDevice)->voe_file()->StartPlayingFileLocally(
            mChannel,
            inStream,
            webrtc::kFileFormatWavFile) != 0)
         {
            return -1;
         }
      }
      else
#endif
#endif
      if (resourceUri.find("tone:") == 0)
      {
         resip::Uri toneUri(resourceUri.c_str());
         int toneIdInt = toneUri.host().size() == 1 ? DtmfToneHelper::dtmfToneIdFromChar(toneUri.host().c_str()[0]) : toneUri.host().convertInt();
         int durationMS = toneUri.exists(resip::p_duration) ? toneUri.param(resip::p_duration) : 200;
         int attdb=10; 
		 
         std::string::size_type pos=resourceUri.find("attdb=");
         if(pos!=std::string::npos)
         {
            std::string my_str = resourceUri.c_str();
            std::string att_str=my_str.substr(pos+6, my_str.size() - 6 - pos);
            InfoLog(<<"Attenuation db: str = " << att_str.c_str());
            attdb=std::atoi( att_str.c_str() );
            InfoLog(<< "playSound - attdb = " << attdb);
			
            if(attdb<0 || attdb>36)
            {
               attdb=10;
               ErrLog(<< "playSound - attenuation parameter out of range[0:36]");
            }
         }
		 
         if (repeat) 
         { 
            if ((*mDevice)->voe_dtmf()->StartPlayingDtmfTone(toneIdInt) != 0)
            {
               ErrLog(<< "playSound - could not StartPlayingDtmfTone");
               postPlaySoundFailure(mHandle);
               return -1;
            }
         }
         else
         {
            if ((*mDevice)->voe_dtmf()->PlayDtmfTone(toneIdInt,durationMS,attdb) != 0)
            {
               (*mDevice)->voe_base()->DeleteChannel(mChannel);
               mChannel = -1;
               ErrLog(<< "playSound - could not PlayDtmfTone");
               postPlaySoundFailure(mHandle);
               return -1;
            }

            //give time for the tone to be played (on Android):
            int processingTime = 0;
#if defined(ANDROID)
            processingTime = 300;
#endif

            mTimer.expires_from_now(durationMS + processingTime);
            mTimer.async_wait(this, 0, NULL);
         }
      }
      else
      {
         if (!mAudio->audioInterface()->mediaManagerInterface()->phoneInterface()->hasFilePermission(Permission_ReadFiles, resourceUri.c_str()))
         {
            InfoLog(<< "playSound - cannot read file due to missing permission");
            mAudio->audioInterface()->mediaManagerInterface()->phoneInterface()->requestPermission(0, Permission_ReadFiles);
            return -1;
         }
         if ((*mDevice)->voe_file()->StartPlayingFileLocally(
            mChannel,
            resourceUri.c_str(),
            repeat,
            webrtc::kFileFormatWavFile) != 0)
         {
            ErrLog(<< "playSound - could not StartPlayingFileLocally");
            postPlaySoundFailure(mHandle);
            return -1;
         }
      }

      return 0;
   }

   void PlaySoundHelper::onTimer(unsigned short timerId, void* appState)
   {
      if (mDevice.get() != NULL)
      {
         (*mDevice)->voe_dtmf()->StopPlayingDtmfTone();
         (*mDevice)->voe_file()->StopPlayingFileLocally(mChannel);
         (*mDevice)->voe_base()->StopPlayout(mChannel);
         (*mDevice)->voe_base()->DeleteChannel(mChannel);
         mDevice.reset();
      }
      OnLocallyPlayingFileFinished(mChannel);
   }
   
   void PlaySoundHelper::OnLocallyPlayingFileFinished(int channel)
   {
      std::weak_ptr<PlaySoundHelper> weakThis = shared_from_this();
      mReactor.post(resip::resip_static_bind(&PlaySoundHelper::onLocallyPlayingFileFinishedImpl, weakThis, channel));
   }

   void PlaySoundHelper::onLocallyPlayingFileFinishedImpl(std::weak_ptr<PlaySoundHelper>& weakThis, int channel)
   {
      if (std::shared_ptr<PlaySoundHelper> strongThis = weakThis.lock())
      {
         InfoLog(<< "PlaySoundHelper::onLocallyPlayingFileFinishedImpl");
         PlaySoundHandle temp = strongThis->mHandle;
         strongThis->mHandle = 0xffffffff;
         if (strongThis->mAudio != NULL)
         {
            strongThis->mAudio->handlePlaySoundComplete(temp);
         }
      }
   }

   void PlaySoundHelper::OnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle)
   {
      InfoLog(<< "PlaySoundHelper::OnLocallyPlayingFileWithHandleFinished");
      assert(handle == mHandle);
      PlaySoundHandle temp = mHandle;
      mHandle = 0xffffffff;
      if (mAudio != NULL) 
      {
         mAudio->handlePlaySoundComplete(temp);
      }
   }

   void PlaySoundHelper::onSystemAudioServiceError(int errorLevel)
   {
     if (mAudio != NULL)
     {
        mAudio->onSystemAudioServiceError(errorLevel);
     }
   }

   void PlaySoundHelper::detatchAudioImpl()
   {
      mAudio = NULL;
   }


   void PlaySoundHelper::postPlaySoundFailure(PlaySoundHandle handle)
   {
      if (mAudio != NULL)
      {
         mAudio->handlePlaySoundFailure(handle);
      }
   }
}
}

#endif
#endif // !TARGET_OS_IPHONE
