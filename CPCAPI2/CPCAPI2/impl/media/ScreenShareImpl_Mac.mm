#include "ScreenShare.h"
#include "ScreenShareImpl.h"

#if defined(__APPLE__)
#include "TargetConditionals.h"
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)

#include "ScreenShareBorderWindow_Mac.h"

#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>

#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>

#define SCREEN_SHARE_IMPL_TIMER 0
#define NOTDONE 0
#define DONE 1

using namespace webrtc;

@interface ScreenShareBorderView : NSView
{
   // rects of other windows that are currently on top
   NSArray *overlappingRects;
}
@end

@interface ThreadStarter : NSObject
{
   CPCAPI2::Media::ScreenShare* ss;
   NSConditionLock* doneLock;
   CFRunLoopSourceRef source;
   unsigned int currentDeviceId;
   ScreenShareBorderWindow* borderWindow;
}

@property (atomic, readwrite) BOOL threadShouldRun;

-(void)Run;
-(void)WaitForExit;
-(id)init:(CPCAPI2::Media::ScreenShare*)screenShare screenshareDeviceId: (unsigned int) deviceId;
-(void)dealloc;
@end

@implementation ThreadStarter
-(id)init:(CPCAPI2::Media::ScreenShare*)screenShare screenshareDeviceId: (unsigned int) deviceId
{
   if (self = [super init])
   {
      ss = screenShare;
      currentDeviceId = deviceId;
      _threadShouldRun = TRUE;
      doneLock = [[NSConditionLock alloc] initWithCondition:NOTDONE];
      
      dispatch_async(dispatch_get_main_queue(), ^
      {
         [self showScreenShareBorder];
      });
      
      // Update border window when screen changes
      [[NSNotificationCenter defaultCenter] addObserver:self
                                               selector:@selector(onScreenChanged:)
                                                   name:NSApplicationDidChangeScreenParametersNotification
                                                 object:nil];
   }
   
   return self;
}

-(void)dealloc
{
   [[NSNotificationCenter defaultCenter] removeObserver:self];
}

-(void)Run
{
   @autoreleasepool {
      [doneLock lock];
      
      webrtc::TickTime now = webrtc::TickTime::Now();
      webrtc::TickTime lastCapture = now;
      while (_threadShouldRun) {
         ss->CaptureThread();
         
         now = webrtc::TickTime::Now();
         int64_t delta = (now - lastCapture).Milliseconds();
   
         // limit capture to 30 fps
         if (delta >= ss->getFrameCaptureInterval())
         {
            lastCapture = now;
            
            ss->PerformCapture();
         }
         
         if (borderWindow)
         {
            [borderWindow updateBorder];
         }
         
         CFRunLoopRunInMode(kCFRunLoopDefaultMode, 0.033, TRUE);
      }
      
      ss->ResetScreenCap();
      
      dispatch_async(dispatch_get_main_queue(), ^
      {
         [borderWindow orderOut:nil];
         borderWindow = nil;
      });
      [doneLock unlockWithCondition:DONE];
   }
}

-(void)WaitForExit
{
   [doneLock lockWhenCondition:DONE];
   [doneLock unlock];
}

-(void)onScreenChanged:(NSNotification*) note {
   // reset border with new parameters
   [self showScreenShareBorder];
}

-(void)showScreenShareBorder
{
   if (borderWindow != nil)
   {
      [borderWindow orderOut:nil];
   }
   
   borderWindow = [[ScreenShareBorderWindow alloc] initWithScreenShareDeviceId:currentDeviceId];
   [borderWindow orderFront:nil];
}


@end

namespace CPCAPI2
{
namespace Media
{
class ScreenShareObjCHolder : public ScreenSharePlatformObjHolder
{
public:
   ScreenShareObjCHolder(ScreenShareImpl* ss) {
      ts_ = [[ThreadStarter alloc]init:ss screenshareDeviceId:ss->GetScreenshareDevice()];
      ssthread_ = [[NSThread alloc] initWithTarget:ts_ selector:@selector(Run) object:nil];
      [ssthread_ start];
   }
   virtual ~ScreenShareObjCHolder() {
      [ts_ setThreadShouldRun:FALSE];
      [ts_ WaitForExit];
   }
   
private:
   __strong NSThread* ssthread_;
   __strong ThreadStarter* ts_;
};
void ScreenShareImpl::StartImpl()
{
   mCapturing = true;
   mObjHolder = new ScreenShareObjCHolder(this);
}

void ScreenShareImpl::StopImpl()
{
   delete mObjHolder;
   mObjHolder = NULL;
}

void ScreenShareImpl::CaptureThread()
{
   if (mScreenCap.get() == NULL)
   {
      InitializeCapture();
   }
}

void ScreenShareImpl::BorderThread()
{
}

void ScreenShareImpl::PerformCapture()
{
   if (mCapturing)
   {
      if (mCapturedFrameI420.IsZeroSize() || mCapturedFrameI420.video_frame_buffer_const()->HasOneRef())
      {
         mScreenCap->CaptureFrame();
      }
   }
}
}
}

#endif
#endif
