
#ifndef CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_OSX_H
#define CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_OSX_H

#if defined( __APPLE__ )
#include <TargetConditionals.h>
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)
#include <stdio.h>
#include <CoreAudio/AudioHardwareBase.h>

#include "AudioDeviceChangeManagerImpl.h"

namespace CPCAPI2
{
namespace Media
{

class MacSystemSleepChangeListener
{
public:
   virtual void onSleep() = 0;
   virtual void onWake() = 0;
};

class AudioDeviceChangeManager_OSX : public AudioDeviceChangeManagerImpl, public MacSystemSleepChangeListener
{
public:
   AudioDeviceChangeManager_OSX(resip::MultiReactor&);
   virtual ~AudioDeviceChangeManager_OSX();

   // MacSystemSleepChangeListener
   void onSleep() override;
   void onWake() override;
   
private:
   static OSStatus
   objectListenerProc(AudioObjectID objectId, UInt32 numberAddresses,
                      const AudioObjectPropertyAddress addresses[],
                      void* clientData);
   OSStatus
   implObjectListenerProc(AudioObjectID objectId, UInt32 numberAddresses,
                          const AudioObjectPropertyAddress addresses[]);

   void* mSleepNotificationSink;
   bool mSystemSleeping = false;

   struct DeviceChangeInfo
   {
      bool mAudioDeviceChanged = false;
      bool mDefaultInputDeviceChanged = false;
      bool mDefaultOutputDeviceChanged = false;
   };

   DeviceChangeInfo mSystemSleepDeviceChanges;

};
   
}
}

#endif
#endif // __APPLE__
#endif // CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_OSX_H
