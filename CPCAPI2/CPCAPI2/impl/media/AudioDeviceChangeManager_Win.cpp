#include "AudioDeviceChangeManager_Win.h"

#if defined( _WIN32 )

#include "Functiondiscoverykeys_devpkey.h"
#include "../util/cpc_logger.h"

#include <sstream>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

using namespace CPCAPI2::Media;


AudioDeviceChangeManager_Win::AudioDeviceChangeManager_Win(resip::MultiReactor& reactor) :
   AudioDeviceChangeManagerImpl(reactor),
   mBogusRefCount(100),
   mDeviceEnum(NULL)
{
   HRESULT hr = CoInitializeEx(NULL, COINIT_MULTITHREADED);

   hr = CoCreateInstance(__uuidof(MMDeviceEnumerator), NULL,
                         CLSCTX_ALL, __uuidof(IMMDeviceEnumerator),
                         (void**)&mDeviceEnum);
   if (FAILED(hr))
   {
      ErrLog(<< "Failed to create instance of IMMDeviceEnumerator; default device change updates will not work (" << hr << ")");
   }
   else
   {
      hr = mDeviceEnum->RegisterEndpointNotificationCallback(this);
      if (FAILED(hr))
      {
         ErrLog(<< "Failed calling RegisterEndpointNotificationCallback; default device change updates will not work (" << hr << ")");
      }
   }
}

AudioDeviceChangeManager_Win::~AudioDeviceChangeManager_Win()
{
   if (mDeviceEnum)
   {
      mDeviceEnum->UnregisterEndpointNotificationCallback(this);
      mDeviceEnum->Release();
   }

   CoUninitialize();
}

ULONG STDMETHODCALLTYPE AudioDeviceChangeManager_Win::AddRef()
{
   return InterlockedIncrement(&mBogusRefCount);
}

ULONG STDMETHODCALLTYPE AudioDeviceChangeManager_Win::Release()
{
   ULONG ulRef = InterlockedDecrement(&mBogusRefCount);   
   return ulRef;
}

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::QueryInterface(REFIID riid, VOID **ppvInterface)
{
   if (IID_IUnknown == riid)
   {
      AddRef();
      *ppvInterface = (IUnknown*)this;
   }
   else if (__uuidof(IMMNotificationClient) == riid)
   {
      AddRef();
      *ppvInterface = (IMMNotificationClient*)this;
   }
   else
   {
      *ppvInterface = NULL;
      return E_NOINTERFACE;
   }

   return S_OK;
}

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::OnDefaultDeviceChanged(
   EDataFlow flow, ERole role,
   LPCWSTR pwstrDeviceId)
{   
   AudioDeviceType deviceType;
   if (flow == eRender)
   {
      deviceType = MediaDeviceType_Render;
   }
   else if (flow == eCapture)
   {
      deviceType = MediaDeviceType_Capture;
   }
   else
   {
      ErrLog(<< "unknown audio device flow type: " << flow << ". assuming render");
      deviceType = MediaDeviceType_Render;
   }

   if (role == eConsole || role == eCommunications)
   {
      sendAudioDefaultSystemDeviceChangeEvent(deviceType);
   }   

   return S_OK;
}

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::OnDeviceAdded(LPCWSTR pwstrDeviceId)
{
   InfoLog(<< "detected device " << GetDeviceName(pwstrDeviceId) << " added");

   sendAudioDeviceChangeEvent();

   return S_OK;
};

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::OnDeviceRemoved(LPCWSTR pwstrDeviceId)
{
   InfoLog(<< "detected device " << GetDeviceName(pwstrDeviceId) << " removed");

   sendAudioDeviceChangeEvent();

   return S_OK;
}

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::OnDeviceStateChanged(
   LPCWSTR pwstrDeviceId,
   DWORD dwNewState)
{
   switch (dwNewState)
   {
   case DEVICE_STATE_ACTIVE:
      break;
   case DEVICE_STATE_DISABLED:
      break;
   case DEVICE_STATE_NOTPRESENT:
      break;
   case DEVICE_STATE_UNPLUGGED:
      break;
   }

   return S_OK;
}

HRESULT STDMETHODCALLTYPE AudioDeviceChangeManager_Win::OnPropertyValueChanged(
   LPCWSTR pwstrDeviceId,
   const PROPERTYKEY key)
{  
   return S_OK;
}

#define SAFE_RELEASE(punk)  \
              if ((punk) != NULL)  \
                     { (punk)->Release(); (punk) = NULL; }

std::string AudioDeviceChangeManager_Win::GetDeviceName(LPCWSTR pwstrId)
{
   if (!mDeviceEnum)
   {
      return std::string();
   }

   IMMDevice *pDevice = NULL;
   IPropertyStore *pProps = NULL;
   PROPVARIANT varString;

   PropVariantInit(&varString);

   HRESULT hr = mDeviceEnum->GetDevice(pwstrId, &pDevice);

   if (hr == S_OK)
   {
      hr = pDevice->OpenPropertyStore(STGM_READ, &pProps);
   }
   if (hr == S_OK)
   {
      hr = pProps->GetValue(PKEY_Device_FriendlyName, &varString);
   }

   std::string deviceName = (hr == S_OK) ? varString.pszVal : "null device";

   PropVariantClear(&varString);

   SAFE_RELEASE(pProps)
      SAFE_RELEASE(pDevice)      
   
   return deviceName;
}

#endif // _WIN32