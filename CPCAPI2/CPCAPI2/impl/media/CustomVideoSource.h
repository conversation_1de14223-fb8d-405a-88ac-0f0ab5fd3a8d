#ifndef CPCAPI2_CUSTOM_VIDEO_SOURCE_H
#define CPCAPI2_CUSTOM_VIDEO_SOURCE_H

namespace webrtc
{
   class ViEExternalCapture;
}

namespace CPCAPI2
{
   namespace Media
   {
      class CustomVideoSource
      {
      public:
         static CustomVideoSource* Create();
         virtual ~CustomVideoSource() {}

         virtual void SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture) = 0;
         virtual int Start() = 0;
         virtual int Stop() = 0;

      protected:
         CustomVideoSource() {}
      };
   }
}

#endif // CPCAPI2_CUSTOM_VIDEO_SOURCE_H