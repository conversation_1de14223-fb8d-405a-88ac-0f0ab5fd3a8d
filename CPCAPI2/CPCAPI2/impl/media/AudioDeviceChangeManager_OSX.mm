#if defined( __APPLE__ )
#include "TargetConditionals.h"
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)
#include "AudioDeviceChangeManager_OSX.h"
#include "../util/cpc_logger.h"

#import <AppKit/AppKit.h>
#import <Foundation/Foundation.h>
#include <CoreAudio/CoreAudio.h>


#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA


#define CA_LOG_ERR(expr)                                                \
   do {                                                                 \
      err = expr;                                                       \
      if (err != noErr) {                                               \
         ErrLog(<< "Error in " << #expr << " - " << err);               \
   }                                                                    \
} while(0)

#define CA_LOG_WARN(expr)                                                   \
   do {                                                                     \
      err = expr;                                                           \
      if (err != noErr) {                                                   \
         WarningLog(<< "Error in " << #expr << " - " << err);               \
   }                                                                        \
} while(0)


using namespace CPCAPI2::Media;


@interface SleepNotificationSink : NSObject
{
@private
   MacSystemSleepChangeListener* _listener;
}

- (id)initWithListener:(MacSystemSleepChangeListener*)listener;
- (void)setListener:(MacSystemSleepChangeListener*)listener;

@property (nonatomic, assign) BOOL systemSleeping; 

@end

@interface SleepNotificationSink (Private)
- (void) receiveSleepNote: (NSNotification*) note;
- (void) receiveWakeNote: (NSNotification*) note;
@end

@implementation SleepNotificationSink

- (id) initWithListener:(MacSystemSleepChangeListener*)listener;
{
   DebugLog(<< "initWithListener entered");
   if (self = [super init])
   {
      DebugLog(<< "initWithListener super object initialized");
      self.systemSleeping = NO;
      _listener = listener;

      [[[NSWorkspace sharedWorkspace] notificationCenter] addObserver: self
         selector: @selector(receiveSleepNote:)
         name: NSWorkspaceWillSleepNotification object: NULL];

      [[[NSWorkspace sharedWorkspace] notificationCenter] addObserver: self
         selector: @selector(receiveWakeNote:)
         name: NSWorkspaceDidWakeNotification object: NULL];
   }
   return self;
}

- (void)setListener:(MacSystemSleepChangeListener*)listener
{
   DebugLog(<< "setListener to nil");
   _listener = nil;
}

- (void) dealloc
{
   [[[NSWorkspace sharedWorkspace] notificationCenter] removeObserver: self];
}

- (void) receiveSleepNote: (NSNotification*) note
{
   self.systemSleeping = YES;
   DebugLog(<< "Detected system sleep");

   if (_listener)
   {
      _listener->onSleep();
   }
}
 
- (void) receiveWakeNote: (NSNotification*) note
{
   self.systemSleeping = NO;
   DebugLog(<< "Detected system wake");

   if (_listener)
   {
      _listener->onWake();
   }
}

@end

AudioDeviceChangeManager_OSX::AudioDeviceChangeManager_OSX(resip::MultiReactor& reactor) :
   AudioDeviceChangeManagerImpl(reactor)
{
   DebugLog(<< "AudioDeviceChangeManager_OSX::AudioDeviceChangeManager_OSX() entered");
   AudioObjectPropertyAddress propertyAddress = {
      kAudioHardwarePropertyRunLoop,
      kAudioObjectPropertyScopeGlobal,
      kAudioObjectPropertyElementMaster };
   CFRunLoopRef runLoop = NULL;
   UInt32 size = sizeof(CFRunLoopRef);
   OSStatus err;

   mSleepNotificationSink = (__bridge_retained void*)[[SleepNotificationSink alloc] initWithListener:this];
   
   CA_LOG_ERR(AudioObjectSetPropertyData(kAudioObjectSystemObject,
                                         &propertyAddress, 0, NULL, size, &runLoop));
   
   propertyAddress.mSelector = kAudioHardwarePropertyDevices;
   CA_LOG_ERR(AudioObjectAddPropertyListener(kAudioObjectSystemObject,
                                             &propertyAddress, &objectListenerProc, this));

   propertyAddress.mSelector = kAudioHardwarePropertyDefaultInputDevice;
   CA_LOG_ERR(AudioObjectAddPropertyListener(kAudioObjectSystemObject,
                                             &propertyAddress, &objectListenerProc, this));
   propertyAddress.mSelector = kAudioHardwarePropertyDefaultOutputDevice;
   CA_LOG_ERR(AudioObjectAddPropertyListener(kAudioObjectSystemObject,
                                             &propertyAddress, &objectListenerProc, this));
}

AudioDeviceChangeManager_OSX::~AudioDeviceChangeManager_OSX()
{
   DebugLog(<< "AudioDeviceChangeManager_OSX::~AudioDeviceChangeManager_OSX() entered");
   SleepNotificationSink* sink = (__bridge_transfer SleepNotificationSink*)mSleepNotificationSink;
   [sink setListener:nil];
   sink = nil;

   OSStatus err;
   
   AudioObjectPropertyAddress propertyAddress = {
      kAudioHardwarePropertyDevices, kAudioObjectPropertyScopeGlobal,
      kAudioObjectPropertyElementMaster };
   CA_LOG_WARN(AudioObjectRemovePropertyListener(kAudioObjectSystemObject,
                                                 &propertyAddress, &objectListenerProc, this));
   
   propertyAddress.mSelector = kAudioHardwarePropertyDefaultInputDevice;
   CA_LOG_WARN(AudioObjectRemovePropertyListener(kAudioObjectSystemObject,
                                                 &propertyAddress, &objectListenerProc, this));
   propertyAddress.mSelector = kAudioHardwarePropertyDefaultOutputDevice;
   CA_LOG_WARN(AudioObjectRemovePropertyListener(kAudioObjectSystemObject,
                                                 &propertyAddress, &objectListenerProc, this));
}

void AudioDeviceChangeManager_OSX::onSleep()
{
   DebugLog(<< "AudioDeviceChangeManager_OSX::onSleep() entered");
   mSystemSleepDeviceChanges = DeviceChangeInfo();
   mSystemSleeping = true;
}

void AudioDeviceChangeManager_OSX::onWake()
{
   DebugLog(<< "AudioDeviceChangeManager_OSX::onWake() entered");
   mSystemSleeping = false;

   if (mSystemSleepDeviceChanges.mAudioDeviceChanged)
   {
      DebugLog(<< "macOS system wake: Now handling deferred audio device change notification");
      sendAudioDeviceChangeEvent();
   }
   if (mSystemSleepDeviceChanges.mDefaultInputDeviceChanged)
   {
      DebugLog(<< "macOS system wake: Now handling deferred default input audio device change notification");
      sendAudioDefaultSystemDeviceChangeEvent(MediaDeviceType_Capture);
   }
   if (mSystemSleepDeviceChanges.mDefaultOutputDeviceChanged)
   {
      DebugLog(<< "macOS system wake: Now handling deferred default ouput audio device change notification");
      sendAudioDefaultSystemDeviceChangeEvent(MediaDeviceType_Render);
   }

   mSystemSleepDeviceChanges = DeviceChangeInfo();
}

OSStatus
AudioDeviceChangeManager_OSX::objectListenerProc(AudioObjectID objectId, UInt32 numberAddresses,
                                                 const AudioObjectPropertyAddress addresses[],
                                                 void* clientData)
{
   if (AudioDeviceChangeManager_OSX* ptrThis = reinterpret_cast<AudioDeviceChangeManager_OSX*>(clientData))
   {
      ptrThis->implObjectListenerProc(objectId, numberAddresses, addresses);
   }
   
   return 0;
}

OSStatus
AudioDeviceChangeManager_OSX::implObjectListenerProc(AudioObjectID objectId, UInt32 numberAddresses,
                                                     const AudioObjectPropertyAddress addresses[])
{
   for (UInt32 i = 0; i < numberAddresses; i++)
   {
      if (addresses[i].mSelector == kAudioHardwarePropertyDevices)
      {
         if (mSystemSleeping)
         {
            DebugLog(<< "Deferring audio device change notification since system is sleeping");
            mSystemSleepDeviceChanges.mAudioDeviceChanged = true;
         }
         else
         {
            sendAudioDeviceChangeEvent();
         }
      }
      else if (addresses[i].mSelector == kAudioHardwarePropertyDefaultInputDevice)
      {
         if (mSystemSleeping)
         {
            DebugLog(<< "Deferring default input audio device change notification since system is sleeping");
            mSystemSleepDeviceChanges.mDefaultInputDeviceChanged = true;
         }
         else
         {
            DebugLog(<< "System default input device changed (will only handle if app is set to use default device");
            sendAudioDefaultSystemDeviceChangeEvent(MediaDeviceType_Capture);
         }

      }
      else if (addresses[i].mSelector == kAudioHardwarePropertyDefaultOutputDevice)
      {
         if (mSystemSleeping)
         {
            DebugLog(<< "Deferring default output audio device change notification since system is sleeping");
            mSystemSleepDeviceChanges.mDefaultOutputDeviceChanged = true;
         }
         else
         {
            DebugLog(<< "System default output device changed (will only handle if app is set to use default device");
            sendAudioDefaultSystemDeviceChangeEvent(MediaDeviceType_Render);
         }

      }
   }
   
   return 0;
}

#endif
#endif // #if defined( __APPLE__ )
