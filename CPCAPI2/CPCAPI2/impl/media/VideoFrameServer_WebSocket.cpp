#include "brand_branded.h"

#if (CPCAPI2_BRAND_VIDEO_WEBSOCKET_SERVER == 1)
#include "VideoFrameServer_WebSocket.h"
#include "RawVideoFrameServer_WebSocket.h"
#include "cpcapi2utils.h"
#include "../util/FileUtils.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"
//include "../../../external/lodepng/lodepng.h"

// blazing fast compression
#include <snappy.h>

// LibYuv includes
#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>
#include <libyuv.h>
#include <libyuv/convert_argb.h>
#include <webrtc/modules/video_processing/main/source/spatial_resampler.h>

// WebRTC
#include <vie_codec.h>
#include <voe_base.h>
#include <vie_base.h>
#include <vie_rtp_rtcp.h>
#include <webrtc/video_engine/vie_encoder.h>
#include <webrtc/system_wrappers/interface/clock.h>
#include <webrtc/video_encoder.h>
#include <webrtc/video_frame.h>

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
//include <codecvt>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES 2560

using namespace resip;

namespace CPCAPI2
{
namespace Media
{
class LocalVideoEncodedFrameHandler : public webrtc::EncodedImageCallback
{
public:
   LocalVideoEncodedFrameHandler(VideoFrameServerWebSocket* frag)
      : mImpl(frag) {}

   virtual ~LocalVideoEncodedFrameHandler() {}

   int32_t Encoded(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation)
   {
      mImpl->handleEncodedVideoFrame(encoded_image);
      return 0;
   }

   virtual int32_t EncodedSpatialLayer(const webrtc::EncodedImage& encoded_image,
      const webrtc::CodecSpecificInfo* codec_specific_info,
      const webrtc::RTPFragmentationHeader* fragmentation,
      int layerId) {
      if (layerId == 1) {
         mImpl->handleEncodedVideoFrame(encoded_image);
      }
      return 0;
   }

   void SetWantsKeyFrame(bool /*wantsKeyFrame*/) {}
   bool WantsKeyFrame() const { return false; }

private:
   VideoFrameServerWebSocket* mImpl;
};

VideoFrameServerWebSocket::VideoFrameServerWebSocket(CPCAPI2::Phone* phone)
   : mWebSockServer(new RawVideoFrameServerWebSocket()),
     mServerThread(NULL),
     mPhone(phone),
     mBufferCurr(NULL),
     mBufferPrev(NULL),
     mSendCompleteFrame(5),
     mChannel(-1),
     mLastWidth(0),
     mLastHeight(0),
     mWebSockServerRwLock(webrtc::RWLockWrapper::CreateRWLock())
{
   mBufferCurr = new unsigned char[webrtc::CalcBufferSize(webrtc::kARGB, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES) + 4]; // = new unsigned char[JpegEncoder::MaxEncodedSize(1920, 1080)];
   mBufferPrev = new unsigned char[webrtc::CalcBufferSize(webrtc::kARGB, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES) + 4]; // = new unsigned char[JpegEncoder::MaxEncodedSize(1920, 1080)];
   mDeltaBuff = new unsigned char[webrtc::CalcBufferSize(webrtc::kARGB, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES) + 4]; // = new unsigned char[JpegEncoder::MaxEncodedSize(1920, 1080)];
}

VideoFrameServerWebSocket::~VideoFrameServerWebSocket()
{
}

int VideoFrameServerWebSocket::processRemoteRequest(const cpc::string& request)
{
   return 0;
}

void VideoFrameServerWebSocket::bindServerToChannel(int channel)
{
   mChannel = channel;
}

void VideoFrameServerWebSocket::startServer(resip::ReadCallbackBase* startedCb)
{
   mSendCompleteFrame = 5;
   mEncodedFrameHandler.reset(new LocalVideoEncodedFrameHandler(this));
   CPCAPI2::VideoStreaming::VideoStreamingServerConfig serverConfig;
   serverConfig.listenPort = 2115;
   mWebSockServer->StartServer(mPhone, serverConfig, startedCb);
}

void VideoFrameServerWebSocket::stopServer()
{
   mWebSockServer->StopServer();
}

uint32_t encodeSimpleDelta_orig(const uint32_t* origBlob, size_t origSize, const uint32_t* newBlob, size_t newSize, uint32_t* deltaBlob)
{
   assert(origSize == newSize);
   uint32_t deltapos = 0;
   bool inCopyMode = false;
   uint32_t sizePos = 0;
   uint32_t deltaSize = 0;
   for (uint32_t i = 0; i < origSize; i++)
   {
      if (origBlob[i] != newBlob[i])
      {
         if (!inCopyMode)
         {
            uint32_t currPosN = i;
            memcpy(&deltaBlob[deltapos], &currPosN, sizeof(uint32_t));
            inCopyMode = true;
            deltapos += 1; // sizeof(uint32_t);
            sizePos = deltapos;
            deltapos += 1; // sizeof(uint32_t);
         }
         deltaBlob[deltapos++] = newBlob[i];
         deltaSize++;
      }

      if (origBlob[i] == newBlob[i] || i == (origSize - 1))
      {
         if (inCopyMode)
         {
            uint32_t deltaSizeN = deltaSize;
            memcpy(&deltaBlob[sizePos], &deltaSizeN, sizeof(uint32_t));
            inCopyMode = false;
            deltaSize = 0;
         }
      }
   }

   return deltapos;
}

/*
sample output:
[0 (4 bytes)] - position of delta
[14 (4 bytes)] - size of delta
[... (N bytes)] - delta
*/
uint32_t encodeSimpleDelta(const uint32_t* origBlob, size_t origSize, const uint32_t* newBlob, size_t newSize, uint32_t* deltaBlob)
{
   const int MIN_DELTA_SIZE = 4;
   assert(origSize == newSize);
   uint32_t deltapos = 0;
   bool inCopyMode = false;
   uint32_t sizePos = 0;
   uint32_t deltaSize = 0;
   for (uint32_t i = 0; i < origSize; i++)
   {
      if (origBlob[i] != newBlob[i] || deltaSize <= MIN_DELTA_SIZE)
      {
         if (!inCopyMode)
         {
            uint32_t currPosN = i;
            memcpy(&deltaBlob[deltapos], &currPosN, sizeof(uint32_t));
            inCopyMode = true;
            deltapos += 1; // sizeof(uint32_t);
            sizePos = deltapos;
            deltapos += 1; // sizeof(uint32_t);
         }
         deltaBlob[deltapos++] = newBlob[i];
         deltaSize++;
      }
      if ((deltaSize > MIN_DELTA_SIZE && origBlob[i] == newBlob[i]) || i == (origSize - 1))
      {
         if (inCopyMode)
         {
            uint32_t deltaSizeN = deltaSize;
            memcpy(&deltaBlob[sizePos], &deltaSizeN, sizeof(uint32_t));
            inCopyMode = false;
            deltaSize = 0;
         }
      }
   }

   return deltapos;
}

void calculateTargetDimensions(int windowWidth, int windowHeight, int frameWidth, int frameHeight, bool& letterbox, int& targetWidth, int& targetHeight)
{
   letterbox = false;
   targetWidth = frameWidth;
   targetHeight = frameHeight;

   if (windowWidth == 0 || windowHeight == 0 || frameWidth == 0 || frameHeight == 0)
   {
      return;
   }

   float widthRatio = (float)windowWidth / (float)frameWidth;
   float heightRatio = (float)windowHeight / (float)frameHeight;

   if (widthRatio < heightRatio)
   {
      if (widthRatio < 1)
      {
         // shrink frame to windowWidth
         letterbox = true;
         // frameWidth * A = windowWidth

         // In case of fitting a HD frame to a low res 4:3 window, crop frame.
         // min res width: 176, high res (720p) width: 1280 -> 176/1280 = 0.1375
         // This applies only to mixing 720p or higher res to min res window
         // (standard res: 352, max supported (1080p): 1920 -> 352/1920 = 0.18333)
         if (widthRatio <= 0.1375)
         {
            targetWidth = windowWidth;
            targetHeight = windowHeight;
         }
         else if (windowWidth << 1 == frameWidth)
         {
            targetWidth = windowWidth;
            targetHeight = frameHeight >> 1;
         }
         else
         {
            float widthMult = widthRatio;
            float adjustedHeight = widthMult * (float)frameHeight;
            targetWidth = windowWidth;
            targetHeight = (int)adjustedHeight;
         }
      }
   }
   else
   {
      if (heightRatio < 1)
      {
         // shrink frame to windowHeight
         letterbox = false;
         // frameHeight * A = windowHeight
         float heightMult = heightRatio;
         float adjustedWidth = heightMult * (float)frameWidth;
         targetWidth = (int)adjustedWidth;
         targetHeight = windowHeight;
      }
   }

   // the current version of libyuv has crash issues if we specify a targetWidth or targetHeight that
   // is an 'odd' number
   targetWidth = (targetWidth >> 1) << 1;
   targetHeight = (targetHeight >> 1) << 1;
}

void VideoFrameServerWebSocket::handleEncodedVideoFrame(const webrtc::EncodedImage& encoded_image)
{
   if (mWebSockServerRwLock->TryAcquireLockShared())
   {
      if (mWebSockServer.get() != NULL)
      {
         mWebSockServer->SendBitstreamData(mChannel, encoded_image._frameType == webrtc::kKeyFrame, encoded_image._buffer, encoded_image._length);
      }
      mWebSockServerRwLock->ReleaseLockShared();
   }
}

#if 0
// ViEEffectFilter
int VideoFrameServerWebSocket::Transform(webrtc::VideoFrame* video_frame)
{
   if (mWebSockServerRwLock->TryAcquireLockShared())
   {
      if (mWebSockServer.get() != NULL)
      {
         {
            {
               const int header_size_bytes = 0;

               webrtc::VideoFrame* capturedFrame = video_frame;
               bool letterbox = false;
               int targetWidth = 0, targetHeight = 0;
               calculateTargetDimensions(CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES, CPCAPI2_VIDEO_FRAME_SERVER_MAX_RES, video_frame->width(), video_frame->height(), letterbox, targetWidth, targetHeight);
               if (targetWidth != video_frame->width() || targetHeight != video_frame->height())
               {
                  webrtc::VPMSimpleSpatialResampler resampler;
                  resampler.SetInputFrameResampleMode(webrtc::kBiLinear);
                  resampler.SetTargetFrameSize(targetWidth, targetHeight);
                  capturedFrame = &mVideoFrameResampled;
                  resampler.ResampleFrame(*video_frame, capturedFrame);
               }

               size_t argb_size = webrtc::CalcBufferSize(webrtc::kARGB, capturedFrame->width(), capturedFrame->height());
               libyuv::I420ToABGR(capturedFrame->buffer(webrtc::kYPlane), capturedFrame->stride(webrtc::kYPlane),
                  capturedFrame->buffer(webrtc::kUPlane), capturedFrame->stride(webrtc::kUPlane),
                  capturedFrame->buffer(webrtc::kVPlane), capturedFrame->stride(webrtc::kVPlane),
                  &mBufferCurr[header_size_bytes], capturedFrame->width() * 4, capturedFrame->width(), capturedFrame->height());

               // png
               //lodepng_encode32_file("test001.png", mBufferCurr, capturedFrame->width(), capturedFrame->height());
               // end png

               if (mLastWidth != capturedFrame->width() || mLastHeight != capturedFrame->height())
               {
                  mSendCompleteFrame = 5;
               }
               mLastWidth = capturedFrame->width();
               mLastHeight = capturedFrame->height();

               if (mSendCompleteFrame > 0 || mWebSockServer->NeedsKeyframe(mChannel))
               {
                  unsigned int cmdId = 0x4001;
                  unsigned int frameWidth = (unsigned int)capturedFrame->width();
                  unsigned int frameHeight = (unsigned int)capturedFrame->height();
                  snappy::string completeFrameHdr;
                  completeFrameHdr.resize(4 + 4 + 4);
                  memcpy(&completeFrameHdr[0], &cmdId, 4);
                  memcpy(&completeFrameHdr[4], &frameWidth, 4);
                  memcpy(&completeFrameHdr[8], &frameHeight, 4);
                  snappy::string compressedAbgr;
                  snappy::Compress((const char*)&mBufferCurr[header_size_bytes], argb_size, &compressedAbgr);
                  mWebSockServer->SendBitstreamData(mChannel, true, (const unsigned char*)completeFrameHdr.c_str(), completeFrameHdr.size());
                  mWebSockServer->SendBitstreamData(mChannel, true, (const unsigned char*)compressedAbgr.c_str(), compressedAbgr.size());
                  mSendCompleteFrame--;
               }
               else
               {
                  uint32_t deltaSize = encodeSimpleDelta((const uint32_t*)&mBufferPrev[header_size_bytes], argb_size / sizeof(uint32_t), (const uint32_t*)&mBufferCurr[header_size_bytes], argb_size / sizeof(uint32_t), (uint32_t*)mDeltaBuff);
                  uint32_t deltaSizeBytes = deltaSize * sizeof(uint32_t);
                  if (deltaSizeBytes > 4)
                  {
                     snappy::string compressedDelta;
                     snappy::Compress((const char*)mDeltaBuff, deltaSizeBytes, &compressedDelta);
                     mWebSockServer->SendBitstreamData(mChannel, false, (const unsigned char*)compressedDelta.c_str(), compressedDelta.size());
                  }
               }

               unsigned char* oldprev = mBufferPrev;
               mBufferPrev = mBufferCurr;
               mBufferCurr = oldprev;
            }
         }
      }
      mWebSockServerRwLock->ReleaseLockShared();
   }
   return 0;
}
#endif 

}
}

#endif
