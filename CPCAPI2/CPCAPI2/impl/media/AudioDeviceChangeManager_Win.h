#ifndef CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_WIN_H
#define CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_WIN_H

#if defined( _WIN32 )

#include <stdio.h>

#include "Mmdeviceapi.h"

#include "AudioDeviceChangeManagerImpl.h"

namespace CPCAPI2
{
namespace Media
{
   class AudioDeviceChangeManager_Win : public AudioDeviceChangeManagerImpl, IMMNotificationClient
   {
   public:
      AudioDeviceChangeManager_Win(resip::MultiReactor&);
      virtual ~AudioDeviceChangeManager_Win();

   private:

      IMMDeviceEnumerator* mDeviceEnum;
      LONG mBogusRefCount;

      ULONG STDMETHODCALLTYPE AddRef();
      ULONG STDMETHODCALLTYPE Release();              
      HRESULT STDMETHODCALLTYPE QueryInterface(REFIID riid, VOID **ppvInterface);
      HRESULT STDMETHODCALLTYPE OnDefaultDeviceChanged(EDataFlow flow, ERole role, <PERSON><PERSON><PERSON><PERSON> pwstrDeviceId);
      HRESULT STDMETHODCALLTYPE OnDeviceAdded(LPCWSTR pwstrDeviceId);         
      HRESULT STDMETHODCALLTYPE OnDeviceRemoved(LPCWSTR pwstrDeviceId);         
      HRESULT STDMETHODCALLTYPE OnDeviceStateChanged(LPCWSTR pwstrDeviceId, DWORD dwNewState);
      HRESULT STDMETHODCALLTYPE OnPropertyValueChanged(LPCWSTR pwstrDeviceId, const PROPERTYKEY key);         
      
      std::string GetDeviceName(LPCWSTR pwstrId);      
   };
}
}

#endif // _WIN32
#endif // CPCAPI2_AUDIO_DEVICE_CHANGE_MANAGER_OSX_H
