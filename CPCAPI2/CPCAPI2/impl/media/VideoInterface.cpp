#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
#include "cpcapi2utils.h"
#include "VideoInterface.h"
#include "VideoImpl.h"
#include "VideoExtImpl.h"
#include "VideoSyncHandler.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"
#include "MediaManagerInterface.h"
#include <MediaStackImpl.hxx>
#include <CodecConfig.hxx>
#include <CodecFactoryImpl.hxx>
#include <codecs/OpenH264CodecImpl.hxx>
#ifdef CODECFACTORY_ENABLE_H264_ANDROID_HW
#include <codecs/H264CodecWrap.hxx>
#endif

#include <functional>

using namespace resip;
using namespace webrtc_recon;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

namespace CPCAPI2
{
namespace Media
{

#define CHECK_VIDEO_ENABLED if(!isVideoEnabled())return kError;

VideoInterface::VideoInterface(MediaManagerInterface* mm)
	: mMM(mm),
	  mVideoApiEnabled(true),
     mImpl(new VideoImpl(mm->media_stack(), this, mm->phoneInterface())),
     mExtImpl(NULL)
{
   updatePerformanceProfile(mm->getDevicePerformanceProfile());
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   mExtImpl = new VideoExtImpl(mm->media_stack(), this, mm->phoneInterface());
#endif
}

VideoInterface::~VideoInterface()
{
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   delete mExtImpl;
#endif
   delete mImpl;
}

void VideoInterface::Release()
{
   mImpl->stopCapture();
   delete this;
}

int VideoInterface::setHandler(VideoHandler* handler)
{
   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&VideoImpl::setHandler, mImpl, handler);
   if (handler == NULL)
   {
      mMM->execute(setHandlerCmd);
      mMM->process(-1);
   }
   else
   {
      mMM->post(setHandlerCmd);
   }
   return kSuccess;
}

int VideoInterface::queryDeviceList()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::queryDeviceList, mImpl);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCaptureDevice(unsigned int deviceId)
{
   DebugLog(<< __FUNCTION__ << " " << deviceId);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::setCaptureDevice, mImpl, deviceId));
   return kSuccess;
}

int VideoInterface::setCaptureDeviceOrientation(VideoOrientation orientation)
{
   DebugLog(<< __FUNCTION__ << " " << orientation);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::setCaptureDeviceOrientation, mImpl, orientation));
   return kSuccess;
}

int VideoInterface::setCaptureImageOrientation(VideoImageOrientation orientation)
{
   DebugLog(<< __FUNCTION__ << " " << orientation);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::setCaptureImageOrientation, mImpl, orientation));
   return kSuccess;
}

int VideoInterface::setIncomingVideoRenderTarget(void* surface, VideoSurfaceType type)
{
   DebugLog(<< __FUNCTION__ << " " << surface <<"," << type);

   CHECK_VIDEO_ENABLED
   mMM->execute(resip::resip_bind(&VideoImpl::setIncomingVideoRenderTarget, mImpl, surface, type));
   return kSuccess;
}

int VideoInterface::setIncomingVideoRenderTarget(int recvVideoStreamId, void* surface, VideoSurfaceType type)
{
   DebugLog(<< __FUNCTION__ << " " << recvVideoStreamId << "," << surface << "," << type);

   CHECK_VIDEO_ENABLED
   mMM->execute(resip::resip_bind(&VideoImpl::setIncomingVideoRenderTarget2, mImpl, recvVideoStreamId, surface, type));
   return kSuccess;
}

int VideoInterface::setLocalVideoRenderTarget(void* surface, VideoSurfaceType type)
{
   DebugLog(<< __FUNCTION__ << "," << surface << "," << type);

   CHECK_VIDEO_ENABLED
   mMM->execute(resip::resip_bind(&VideoImpl::setLocalVideoRenderTarget, mImpl, surface, type));
   return kSuccess;
}

int VideoInterface::setLocalVideoPreviewResolution(VideoCaptureResolution resolution)
{
   DebugLog(<< __FUNCTION__ << " " << resolution);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::setLocalVideoPreviewResolution, mImpl, resolution));
   return kSuccess;
}

int VideoInterface::startCapture()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::startCapture, mImpl));
   return kSuccess;
}

int VideoInterface::stopCapture()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::stopCapture, mImpl));
   return kSuccess;
}

int VideoInterface::startScreenshare(ScreenShareHandler* handler)
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::startScreenshare, mImpl, handler));
   return kSuccess;
}

int VideoInterface::stopScreenshare()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   mMM->post(resip::resip_bind(&VideoImpl::stopScreenshare, mImpl));
   return kSuccess;
}

int VideoInterface::setPreferredResolution(unsigned int codecId, VideoCaptureResolution resolution)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << resolution);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setPreferredResolution, mImpl, codecId, resolution);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::queryCodecList()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::queryCodecList, mImpl);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCodecEnabled(unsigned int codecId, bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << enabled);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setCodecEnabled, mImpl, codecId, enabled);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCodecPriority(unsigned int codecId, unsigned int priority)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << priority);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setCodecPriority, mImpl, codecId, priority);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCodecPayloadType(unsigned int codecId, unsigned int payloadType)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << payloadType);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setCodecPayloadType, mImpl, codecId, payloadType);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCodecEncodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << enabled);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setCodecEncodingHardwareAccelerationEnabled, mImpl, codecId, enabled);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setCodecDecodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << codecId << "," << enabled);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setCodecDecodingHardwareAccelerationEnabled, mImpl, codecId, enabled);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setPacketLossConfig(unsigned int codecId, const PacketLossConfig& packetLossConfig)
{
   DebugLog(<< __FUNCTION__ << " " << codecId);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setPacketLossConfig, mImpl, codecId, packetLossConfig);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setVideoMute(bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << enabled);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setVideoMute, mImpl, enabled);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setVideoMixMode(VideoMixMode mixMode)
{
   DebugLog(<< __FUNCTION__ << " " << mixMode);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setVideoMixMode, mImpl, mixMode);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setVideoApiEnabled(bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << enabled);

  mVideoApiEnabled=enabled;
  return kSuccess;
}

int VideoInterface::setVideoDscp(unsigned int mediaDscp)
{
   DebugLog(<< __FUNCTION__ << " " << mediaDscp);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setVideoDscp, mImpl, mediaDscp);
   mMM->post(queryCmd);
   return kSuccess;
}
int VideoInterface::setVideoUseQosFastlane(bool useQosFastlane, bool useWithDscp)
{
   DebugLog(<< __FUNCTION__ << " " << useQosFastlane);

//#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setVideoUseQosFastlane, mImpl, useQosFastlane, useWithDscp);
   mMM->post(queryCmd);
//#endif
   return kSuccess;
}

int VideoInterface::showPropertyPage(void* surface)
{
   DebugLog(<< __FUNCTION__ << " " << surface);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::showPropertyPage, mImpl, surface);
   mMM->post(queryCmd);
   return kSuccess;
}

webrtc_recon::MediaStackImpl* VideoInterface::media_stack() const
{
   return mImpl->media_stack();
}

void VideoInterface::postCallback(resip::ReadCallbackBase* fp)
{
   mMM->postCallback(fp);
}

void VideoInterface::updatePerformanceProfile(DevicePerformanceProfile perfProfile)
{
   DebugLog(<< __FUNCTION__);

   mMM->post(resip::resip_bind(&VideoImpl::updatePerformanceProfile, mImpl, perfProfile));
}

int VideoInterface::setCodecConfig(const H264Config& config)
{
   DebugLog(<< __FUNCTION__ << " " << config.preferNonInterleavedMode << "," << config.enableNonInterleavedMode);

   mMM->post(resip::resip_bind(&VideoInterface::setCodecConfigImpl, this, config));
   return kSuccess;
}

void VideoInterface::setCodecConfigImpl(const H264Config& config)
{
   if (media_stack()->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(media_stack()->codecFactory());
      CodecFactoryImpl::Codecs videoCodecs = codecFactory->videoCodecs();
      CodecFactoryImpl::Codecs::iterator itVideoCodecs = videoCodecs.begin();
      for (; itVideoCodecs != videoCodecs.end(); ++itVideoCodecs)
      {
#ifdef CODECFACTORY_ENABLE_H264_ANDROID_HW
         std::shared_ptr<H264CodecWrap> h264_codec = std::dynamic_pointer_cast<H264CodecWrap>(*itVideoCodecs);
         if (h264_codec)
         {
            h264_codec->setNonInterleavedModeEnabled(config.enableNonInterleavedMode);
            h264_codec->preferNonInterleavedMode(config.preferNonInterleavedMode);
         }
#elif defined(CODECFACTORY_ENABLE_OPENH264)
         std::shared_ptr<OpenH264CodecImpl> openh264_codec = std::dynamic_pointer_cast<OpenH264CodecImpl>(*itVideoCodecs);
         if (openh264_codec)
         {
            openh264_codec->setNonInterleavedModeEnabled(config.enableNonInterleavedMode);
            openh264_codec->preferNonInterleavedMode(config.preferNonInterleavedMode);
         }
#endif
      }
   }
}

int VideoInterface::connectVideoStreams(int recvVideoStreamId, int sendVideoStreamId)
{
   DebugLog(<< __FUNCTION__ << " " << recvVideoStreamId << "," << sendVideoStreamId);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::connectVideoStreams, mImpl, recvVideoStreamId, sendVideoStreamId);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::disconnectVideoStreams(int recvVideoStreamId, int sendVideoStreamId)
{
   DebugLog(<< __FUNCTION__ << " " << recvVideoStreamId << "," << sendVideoStreamId);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::disconnectVideoStreams, mImpl, recvVideoStreamId, sendVideoStreamId);
   mMM->post(queryCmd);
   return kSuccess;
}

/*
int VideoInterface::connectReceiveStreamToMp4Output(int videoStreamId, VideoBitstreamHandler* bitstreamHandler)
{
   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::connectReceiveStreamToMp4Output, mExtImpl, videoStreamId, bitstreamHandler);
   mMM->post(queryCmd);
   return kSuccess;
#else
   return kError;
#endif
}
*/

int VideoInterface::startWebsocketServer(VideoWebsocketServerHandler* handler)
{
   DebugLog(<< __FUNCTION__ << " " << handler);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
      resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::startWebsocketServer, mExtImpl, handler);
   mMM->post(queryCmd);
   return kSuccess;
#else
      return kError;
#endif
}

int VideoInterface::startWebsocketServerForReceiveStream(int videoStreamId)
{
   DebugLog(<< __FUNCTION__ << " " << videoStreamId);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::startWebsocketServerForReceiveStream, mExtImpl, videoStreamId);
   mMM->post(queryCmd);
   return kSuccess;
#else
   return kError;
#endif
}

int VideoInterface::stopWebsocketServer()
{
   DebugLog(<< __FUNCTION__);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::stopWebsocketServer, mExtImpl);
   mMM->execute(queryCmd);
   return kSuccess;
#else
   return kError;
#endif
}

int VideoInterface::createSnapshotForReceiveStream(int videoStreamId, const cpc::string& fileNameUtf8, VideoSnapshotHandler* snapshotHandler)
{
   DebugLog(<< __FUNCTION__ << " " << videoStreamId << "," << fileNameUtf8 << "," << snapshotHandler);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::createSnapshotForReceiveStream, mExtImpl, videoStreamId, fileNameUtf8, snapshotHandler);
   mMM->post(queryCmd);
   return kSuccess;
#else
      return kError;
#endif
}

int VideoInterface::queryScreenshareDeviceList(ScreenshareDeviceListHandler* handler, bool includeMonitors, bool includeWindows)
{
   DebugLog(<< __FUNCTION__ << " " << handler << "," << includeMonitors << "," << includeWindows);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::queryScreenshareDeviceList, mImpl, handler, includeMonitors, includeWindows);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setScreenshareCaptureDevice(unsigned int deviceId)
{
   DebugLog(<< __FUNCTION__ << " " << deviceId);;

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setScreenshareCaptureDevice, mImpl, deviceId);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setScreenshareCaptureMaxFramerate(unsigned int maxFramerate, bool forceOverride)
{
   DebugLog(<< __FUNCTION__ << " " << maxFramerate);;

   static unsigned int sForcedFrameRate = 0;
   if (forceOverride)
   {
      // useful for debug, ie. set via star code
      sForcedFrameRate = maxFramerate;
   }
   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::setScreenshareCaptureMaxFramerate, mImpl, sForcedFrameRate > 0 ? sForcedFrameRate : maxFramerate);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setInterleavedModeEnabled(bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << enabled);;

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoInterface::setInterleavedModeEnabledImpl, this, enabled);
   mMM->post(queryCmd);
   return kSuccess;
}

void VideoInterface::setInterleavedModeEnabledImpl(bool enabled)
{
   DebugLog(<< __FUNCTION__ << " " << enabled);

   if (media_stack()->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(media_stack()->codecFactory());
      CodecFactoryImpl::Codecs videoCodecs = codecFactory->videoCodecs();
      CodecFactoryImpl::Codecs::iterator itVideoCodecs = videoCodecs.begin();
      for (; itVideoCodecs != videoCodecs.end(); ++itVideoCodecs)
      {
#ifdef CODECFACTORY_ENABLE_H264_ANDROID_HW
         std::shared_ptr<H264CodecWrap> h264_codec = std::dynamic_pointer_cast<H264CodecWrap>(*itVideoCodecs);
         if (h264_codec)
         {
            h264_codec->setInterleavedModeEnabled(enabled);
         }
#elif defined(CODECFACTORY_ENABLE_OPENH264)
         std::shared_ptr<OpenH264CodecImpl> openh264_codec = std::dynamic_pointer_cast<OpenH264CodecImpl>(*itVideoCodecs);
         if (openh264_codec)
         {
            openh264_codec->setInterleavedModeEnabled(enabled);
         }
#endif
      }
   }
}

int VideoInterface::requestKeyFrame(int recvVideoStreamId)
{
   DebugLog(<< __FUNCTION__ << " " << recvVideoStreamId);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::requestKeyFrame, mImpl, recvVideoStreamId);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::set1080pEnabled(bool enable)
{
   DebugLog(<< __FUNCTION__ << " " << enable);

   CHECK_VIDEO_ENABLED
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoImpl::set1080pEnabled, mImpl, enable);
   mMM->post(queryCmd);
   return kSuccess;
}

int VideoInterface::setBrokenKeyFrameSupportEnabled(bool enable)
{
   DebugLog(<< __FUNCTION__ << " " << enable);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
   resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::setBrokenKeyFrameSupportEnabled, mExtImpl, enable);
   mMM->post(queryCmd);
   return kSuccess;
#else
      return kError;
#endif
}

int VideoInterface::setKeyFramePeriod(int periodInFrames)
{
   DebugLog(<< __FUNCTION__ << " " << periodInFrames);

   CHECK_VIDEO_ENABLED
#if (CPCAPI2_BRAND_VIDEO_EXT_MODULE == 1)
      resip::ReadCallbackBase* queryCmd = resip::resip_bind(&VideoExtImpl::setKeyFramePeriod, mExtImpl, periodInFrames);
   mMM->post(queryCmd);
   return kSuccess;
#else
      return kError;
#endif
}

void VideoInterface::addSdkObserver(VideoHandler* observer)
{
   mSdkObservers.insert(observer);
}

void VideoInterface::removeSdkObserver(VideoHandler* observer)
{
   mSdkObservers.erase(observer);
}

void VideoInterface::fireVideoDeviceListUpdated(VideoHandler* appHandler, const VideoDeviceListUpdatedEvent& args)
{
   DebugLog(<< "fireVideoDeviceListUpdated " << args);

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(VideoHandler::onVideoDeviceListUpdated, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<VideoHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      VideoHandler* obs = *itObs;
      if (dynamic_cast<VideoSyncHandler*>(obs) != NULL)
      {
         obs->onVideoDeviceListUpdated(args);
      }
   }
}


void VideoInterface::fireScreenshareDeviceListUpdated(ScreenshareDeviceListHandler* appHandler, const ScreenshareDeviceListEvent& args)
{
   DebugLog(<< "fireScreenshareDeviceListUpdated");

   if (appHandler != NULL)
   {
      if (appHandler != (void*)0xDEADBEEF && dynamic_cast<VideoSyncHandler*>(appHandler) != NULL)
      {
         appHandler->onScreenshareDeviceList(args);
      }
      else
      {
         ReadCallbackBase* fp = makeFpCommand1(ScreenshareDeviceListHandler::onScreenshareDeviceList, appHandler, args);
         mMM->postCallback(fp);
      }
   }

#if 0
   std::set<VideoHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      VideoHandler* obs = *itObs;
      if (dynamic_cast<VideoSyncHandler*>(obs) != NULL && dynamic_cast<ScreenshareDeviceListHandler*>(obs) != NULL)
      {
         dynamic_cast<ScreenshareDeviceListHandler*>(obs)->onScreenshareDeviceList(args);
      }
   }
#endif
}

void VideoInterface::fireScreenShareError(ScreenShareHandler* appHandler)
{
   DebugLog(<< "fireScreenShareError");

   if (appHandler != NULL)
   {
      mMM->postCallback(resip::resip_bind(&ScreenShareHandler::onScreenShareError, appHandler));
   }
}
}
}
#endif //CPCAPI2_BRAND_VIDEO_MODULE
#endif //CPCAPI2_BRAND_MEDIA_MODULE
