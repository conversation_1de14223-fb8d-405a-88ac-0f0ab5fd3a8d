#ifndef CPCAPI2_MEDIATRANSPORTSREACTORFACTORY_H
#define CPCAPI2_MEDIATRANSPORTSREACTORFACTORY_H

#include "media/MediaManagerInternal.h"

#include <rutil/Fifo.hxx>
#include <rutil/HighPerfReactor.hxx>
#include <rutil/Resolver.hxx>

#include <vector>

namespace CPCAPI2
{
namespace Media
{
class MediaTransportsReactorFactoryInterface : 
   public MediaTransportsReactorFactory,
   public resip::ReactorFactory<resip::HighPerfReactor::TReactorQueue, resip::Resolver<resip::HighPerfReactor> >
{
public:
   MediaTransportsReactorFactoryInterface();
   virtual ~MediaTransportsReactorFactoryInterface();

   virtual void initialize();
   virtual void initializeSingle();
   virtual void shutdown();

   // ReactorFactory
   virtual resip::Reactor<resip::HighPerfReactor::TReactorQueue>* allocateReactor();
   virtual void addRef();
   virtual void releaseRef();
   virtual resip::Resolver<resip::HighPerfReactor>* getResolverForReactor(resip::Reactor<resip::HighPerfReactor::TReactorQueue>* reactor) const;

private:
   void initializeNumReactors(unsigned int numReactors);

private:
   std::map<resip::HighPerfReactor*, resip::Resolver<resip::HighPerfReactor>* > mReactors;
   size_t mNextReactor;
   resip::Mutex mSafeDeleteMutex;
   unsigned int mSharedInstanceRefCnt;
};
}
}

#endif // CPCAPI2_MEDIATRANSPORTSREACTORFACTORY_H
