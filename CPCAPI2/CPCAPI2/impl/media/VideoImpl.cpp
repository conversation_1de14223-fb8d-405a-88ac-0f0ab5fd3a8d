#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#if (CPCAPI2_BRAND_VIDEO_MODULE == 1)
#include "VideoImpl.h"
#include "VideoInternalHandler.h"
#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"
#include "../analytics1/AnalyticsManagerInterface.h"

#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>
#include <MixerImpl.hxx>
#include <MediaUtils.hxx>

#include <functional>
#include <limits>

#include <vie_capture.h>
#include <vie_render.h>
#include <vie_conference.h>
#include <vie_codec.h>
#include <vie_rtp_rtcp.h>

#ifdef ANDROID
#include <codecs/AndroidMediaCodec.hxx>
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

#define SCREEN_SHARE_SOURCE_ID "ScreenShare"
#define CUSTOM_VIDEO_SOURCE_ID "CustomVideoSource"

#define CUSTOM_VIDEO_SOURCE_SUPPORTED 1

using namespace webrtc_recon;
using namespace resip;

namespace CPCAPI2
{
namespace Media
{
#ifndef CUSTOM_VIDEO_SOURCE_SUPPORTED
CustomVideoSource* CustomVideoSource::Create()
{
   return NULL;
}
#endif

#if (defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE) || (defined(TARGET_IPHONE_SIMULATOR) && TARGET_IPHONE_SIMULATOR) || (defined(TARGET_OS_ANDROID) && TARGET_OS_ANDROID)
#define MOBILE_OS 1
#else
#define MOBILE_OS 0
#endif

static const int kLocalCaptureDiagFpsTimerId = 0;
static const int kLocalCaptureDiagNoPicAlarmTimerId = 1;
static const int kLocalCaptureDiagFpsTimerIntervalMs = 5000;
static const int kLocalCaptureDiagNoPicAlarmTimerIntervalMs = 3000;


VideoImpl::VideoImpl(webrtc_recon::MediaStackImpl* mediaStack, VideoInterface *videoInterface, PhoneInterface* phone) :
   mMediaStack(mediaStack),
   mVideoInterface(videoInterface),
   mPhone(phone),
   mMediaHandler(NULL),
   mRemoteVideoSurfaceType(webrtc::kRenderSurfaceDefault),
   mLocalVideoSurface(NULL),
   mLocalVideoSurfaceType(webrtc::kRenderSurfaceDefault),
   mVidCaptureId(-2),
   mVidCaptureOrientation(VideoOrientation_0),
   mVidImageOrientation(VideoImageOrientation_Default),
   mCaptureRenderAttached(false),
   mCaptureWidth(0), mCaptureHeight(0), mCaptureFramerate(0),
   mAppSpecifiedResolution(false),
#if MOBILE_OS == 1
   mPerfProfile(DevicePerformanceProfile_Mobile),
#else
   mPerfProfile(DevicePerformanceProfile_Desktop),
#endif
   mScreenShare(NULL),
   mScreenShareHandler(NULL),
   mCustomVideoSource(NULL),
   mScreenshareDeviceId(0),
   mScreenshareMaxFrameRate(DEFAULT_SCREENSHARE_MAX_FRAMERATE),
   mLocalCaptureDiagFpsTimer(phone->getSdkModuleThread()),
   mLocalCaptureDiagNoPicAlarmTimer(phone->getSdkModuleThread()),
   mLocalCaptureDiagLastFramerate(-1),
#if MOBILE_OS == 1
   mMaxSupportedWidth(1280),
   mMaxSupportedHeight(720)
#else
   mMaxSupportedWidth(1920),
   mMaxSupportedHeight(1080)
#endif
{
}

VideoImpl::~VideoImpl()
{
   stopLocalCaptureDiagFpsMonitor();
   if (mVidCaptureId > 0)
   {
      webrtc_recon::MediaStackImpl* ms = mMediaStack;
      ms->vie_capture()->DeregisterObserver(mVidCaptureId);
   }
}

int VideoImpl::set1080pEnabled(bool enable)
{
   bool wasCapturing = (mVidCaptureId >= 0);
   if (wasCapturing)
   {
      stopLocalVideoCapture();
   }

   if (enable)
   {
      mMaxSupportedWidth = 1920;
      mMaxSupportedHeight = 1080;
   }
   else
   {
      mMaxSupportedWidth = 1280;
      mMaxSupportedHeight = 720;
   }

   setLocalVideoPreviewResolution(VideoCaptureResolution_MaxSupported);
   setPreferredResolution(0, VideoCaptureResolution_MaxSupported);

   if (wasCapturing)
   {
      startLocalVideoCapture(true);
   }
   return kSuccess;
}

int VideoImpl::setHandler(VideoHandler* handler)
{
   mMediaHandler = handler;
   return kSuccess;
}

int VideoImpl::readCaptureDeviceInfo(unsigned int listNumber, VideoDeviceInfo& devInfo, cpc::string* devGuid)
{
   char devNameUtf8[128];
   char devGuidUtf8[128];
   DebugLog(<< "VideoImpl::readCaptureDeviceInfo entered");
   if (mMediaStack->vie_capture()->GetCaptureDevice(listNumber, devNameUtf8, sizeof(devNameUtf8), devGuidUtf8, sizeof(devGuidUtf8)) == 0)
   {
      // NOTE: The device GUID is only available on Windows (only Vista and higher)
      devInfo.friendlyName = devNameUtf8;
      devInfo.cameraIndex = listNumber;
      cpc::string devGuidWstr = devGuidUtf8;
      if (devGuidWstr.size() > 0)
      {
         DebugLog(<< "VideoImpl::readCaptureDeviceInfo devGuidWstr.size() > 0");
         size_t guidHash = cpc::hash(devGuidWstr);
         devInfo.id = guidHash;
         webrtc::VideoRotation orientation;
         mMediaStack->vie_capture()->GetOrientation(devGuidUtf8, orientation);
         devInfo.orientation = (CPCAPI2::Media::VideoOrientation)orientation;
      }
      else
      {
         DebugLog(<< "VideoImpl::readCaptureDeviceInfo devGuidWstr.size() <= 0");
         size_t nameHash = cpc::hash(devInfo.friendlyName);
         devInfo.id = nameHash;
      }

      if (devGuid)
      {
         *devGuid = devGuidUtf8;
      }
      return kSuccess;
   }
   return kError;
}

int VideoImpl::queryDeviceList()
{
   DebugLog(<< "VideoImpl::queryDeviceList()");

   int numCaptureDevs = mMediaStack->vie_capture()->NumberOfCaptureDevices();
   {
      DebugLog(<< "VideoImpl::queryDeviceList numCaptureDevs=" << numCaptureDevs);
      mVideoDeviceList.clear();
      for (int i=0; i<numCaptureDevs; i++)
      {
         VideoDeviceInfo devInfo;
         if (readCaptureDeviceInfo(i, devInfo, NULL) == kSuccess)
         {
            mVideoDeviceList.push_back(devInfo);
         }
         else
         {
            ErrLog(<< "VideoImpl::queryDeviceList error");
         }
      }

      VideoDeviceListUpdatedEvent args;
      args.deviceInfo = mVideoDeviceList;
      mVideoInterface->fireVideoDeviceListUpdated(mMediaHandler, args);
   }
   return kSuccess;
}

int VideoImpl::findCaptureDevice(unsigned int deviceId, VideoDeviceInfo& devInfo, cpc::string* devGuid)
{
   for (cpc::vector<VideoDeviceInfo>::const_iterator di = mVideoDeviceList.begin(); di != mVideoDeviceList.end(); ++di )
   {
      // First check that device is in the cached device list
      if (di->id == deviceId)
      {
         // Safety check that the device is still valid (hasn't been unplugged, etc)
         int numCaptureDevs = mMediaStack->vie_capture()->NumberOfCaptureDevices();
         for (int i=0; i<numCaptureDevs; i++)
         {
            if (readCaptureDeviceInfo(i, devInfo, devGuid) == kSuccess &&
                  devInfo.id == deviceId)
            {
               return i;
            }
         }
         // found in cache, but no longer a valid device
         return -1;
      }
   }
   // invalid device id, not in cache
   return -1;
}

int VideoImpl::setCaptureDevice(unsigned int deviceId)
{
   VideoDeviceInfo devInfo;
   cpc::string devGuid;
   if (findCaptureDevice(deviceId, devInfo, &devGuid) >= 0)
   {
      if (mCaptureDeviceGuidUTF8 != devGuid)
      {
         DebugLog(<< "VideoImpl::setCaptureDevice deviceId " << deviceId << " maps to guid " << devGuid << " friendlyName " << devInfo.friendlyName);
         mCaptureDeviceGuidUTF8 = devGuid;
         mCaptureDeviceName = devInfo.friendlyName;
         if (mVidCaptureId >=0)
         {
            stopCapture();
            startCapture();
         }
      }

#ifndef ANDROID // GetCaptureCapability seen to be very slow on jenkins test runs on an EDA51
      int numCapa = mMediaStack->vie_capture()->NumberOfCapabilities(devGuid, devGuid.size());
      if (numCapa > 0)
      {
         webrtc::CaptureCapability capa;
         DebugLog(<< "[" << devInfo.friendlyName << "] supported capture resolutions:");
         for (int i = 0; i < numCapa; i++)
         {
            if (mMediaStack->vie_capture()->GetCaptureCapability(devGuid, devGuid.size(), i, capa) == 0)
            {
               resip::Data codec = capa.codecType == webrtc::kVideoCodecVP8 ? "VP8" :
                  (capa.codecType == webrtc::kVideoCodecH264 ? "H264" : (capa.codecType == webrtc::kVideoCodecUnknown ? "unknown" : "other"));
               DebugLog(<< "Codec " << codec << ", " << capa.width << " x " << capa.height << ", maxFPS: " << capa.maxFPS //<< ", rawType: " << capa.rawType
                  << (capa.interlaced == 0 ? ", non-" : ", ") << "interlaced");
            }
         }
      }
      else DebugLog(<< "Unable to get capture capabilities from [" << devInfo.friendlyName << "]");
#endif // #ifndef ANDROID
      
      #if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
        //Send data to Analytics (UEM) module as well
        static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->CurrentVideoDeviceUpdatedFired(devInfo);
      #endif
      return kSuccess;
   }
   else if (deviceId == kScreenCaptureDeviceId)
   {
      mCaptureDeviceGuidUTF8 = SCREEN_SHARE_SOURCE_ID;
      return kSuccess;
   }
#ifdef CUSTOM_VIDEO_SOURCE_SUPPORTED
   else if (deviceId == kCustomVideoSourceDeviceId)
   {
      mCaptureDeviceGuidUTF8 = CUSTOM_VIDEO_SOURCE_ID;
      return kSuccess;
   }
#endif

   #if (CPCAPI2_BRAND_ANALYTICS_MODULE == 1)
      //Send data to Analytics (UEM) module for no device found / unplugged
      devInfo.friendlyName = "None";
      static_cast<Analytics::AnalyticsManagerInt*>(Analytics::AnalyticsManager::getInterface(mPhone))->CurrentVideoDeviceUpdatedFired(devInfo);
   #endif
   return kError;
}

int VideoImpl::setCaptureDeviceOrientation(VideoOrientation orientation)
{
   mVidCaptureOrientation = orientation;
   if (mVidCaptureId < 0)
   {
      // rotation will be applied later when local capture is started
      return kSuccess;
   }
   if (mMediaStack->vie_capture()->SetVideoRotation(mVidCaptureId, (webrtc::VideoRotation)mVidCaptureOrientation) == 0)
   {
      return kSuccess;
   }
   return kError;
}

int VideoImpl::setCaptureImageOrientation(VideoImageOrientation orientation)
{
   mVidImageOrientation = orientation;

   if (mVidCaptureId < 0)
   {
      // rotation will be applied later when local capture is started
      return kSuccess;
   }

   if (mMediaStack->vie_capture()->SetImageOrientation(mVidCaptureId, (webrtc::VideoImageOrientation)orientation) == 0)
   {
      return kSuccess;
   }

   return kError;
}

int VideoImpl::setIncomingVideoRenderTarget(void* void_surface, VideoSurfaceType type)
{
   return setIncomingVideoRenderTarget2(-1, void_surface, type);
}

int VideoImpl::setIncomingVideoRenderTarget2(int recvVideoStreamId, void* surface, VideoSurfaceType type)
{
   InfoLog(<< "setIncomingVideoRenderTarget2(" << recvVideoStreamId << ", " << surface << ")");
   if (mLocalVideoSurface == surface && mLocalVideoSurface != NULL)
   {
      // we're already using this surface to show local video;
      // let's cleanly stop the local rendering
      stopLocalVideoRender();
   }
   webrtc::VideoRenderSurfaceType remoteVideoSurfaceType = webrtc::kRenderSurfaceDefault;
#ifdef _WIN32
   if (VideoSurfaceType_WindowsHWND == type)
   {
      InfoLog(<< "setIncomingVideoRenderTarget2(" << recvVideoStreamId << ", " << surface << ") - surface is a window");
      remoteVideoSurfaceType = webrtc::kRenderSurfaceWindows;
   }
   else if (VideoSurfaceType_WindowsWpfControl == type)
   {
      InfoLog(<< "setIncomingVideoRenderTarget2(" << recvVideoStreamId << ", " << surface << ") - surface is a WPF control");
      remoteVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
   }

   if (webrtc::kRenderSurfaceDefault == remoteVideoSurfaceType)
   {
      if (::IsWindow((HWND)surface))
      {
         InfoLog(<< "setIncomingVideoRenderTarget2(" << recvVideoStreamId << ", " << surface << ") - detected that surface is a window");
         remoteVideoSurfaceType = webrtc::kRenderSurfaceWindows;
      }
      else
      {
         InfoLog(<< "setIncomingVideoRenderTarget2(" << recvVideoStreamId << ", " << surface << ") - detected that surface is WPF control");
         remoteVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
      }
   }
#endif

   signalIncomingVideoRenderTargetChanged(recvVideoStreamId, NULL, surface == NULL ? (void*)0xDEADBEEF : surface, remoteVideoSurfaceType);


#if (defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE))
   mAppleVideo.trackRemoteSurface(recvVideoStreamId, surface);
#endif

   return kSuccess;
}

int VideoImpl::setLocalVideoRenderTarget(void* surface, VideoSurfaceType type)
{
   startLocalVideoRender(surface, type);

#if (defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE))
   mAppleVideo.trackLocalSurface(surface);
#endif

   return kSuccess;
}

int VideoImpl::setLocalVideoPreviewResolution(VideoCaptureResolution resolution)
{
   //TODO: Try getting maximum camera resolutyion(width/height) using GetCaptureCapability function
   //webrtc::CaptureCapability captureCapabilities;
   //mMediaStack->vie_capture()->GetCaptureCapability(0, 0, 0, captureCapabilities);

#if MOBILE_OS == 1
   return kSuccess;
#endif
   int previewWidth = 352;
   int previewHeight = 288;
   switch (resolution)
   {
      case VideoCaptureResolution_Low:
         previewWidth = 176;
         previewHeight = 144;
         break;
      case VideoCaptureResolution_Standard:
         previewWidth = 352;
         previewHeight = 288;
         break;
      case VideoCaptureResolution_High:
         previewWidth = 640;
         previewHeight = 480;
         break;
      case VideoCaptureResolution_848x480p:
         previewWidth = 848;
         previewHeight = 480;
         break;
      case VideoCaptureResolution_HD_1280x720p:
         previewWidth = 1280;
         previewHeight = 720;
         break;
      case VideoCaptureResolution_HD_1920x1080p:
         previewWidth = 1920;
         previewHeight = 1080;
         break;
      case VideoCaptureResolution_MaxSupported:
         previewWidth = mMaxSupportedWidth;
         previewHeight = mMaxSupportedHeight;
         break;
      default:
         break;
   }

   webrtc_recon::MediaStackImpl* ms = mMediaStack;

   mCaptureWidth = previewWidth;
   mCaptureHeight = previewHeight;
   mCaptureFramerate = 30;
   mAppSpecifiedResolution = true;

   if (mVidCaptureId > 0)
   {
      if (ms->vie_capture()->StopCapture(mVidCaptureId) == 0)
      {
         webrtc::CaptureCapability captureCapa;
         captureCapa.width = mCaptureWidth;
         captureCapa.height = mCaptureHeight;
         captureCapa.maxFPS = mCaptureFramerate;
         DebugLog(<< "VideoImpl::setLocalVideoPreviewResolution: setup capture: " << captureCapa.width << "x" << captureCapa.height << " @ " << captureCapa.maxFPS << "fps");
         if (ms->vie_capture()->StartCapture(mVidCaptureId, captureCapa) != -1)
         {
            DebugLog(<< "VideoImpl::setLocalVideoPreviewResolution: capturing");
         }
      }
   }
   return kSuccess;
}

int VideoImpl::startCapture()
{
   startLocalVideoCapture(true);
   return kSuccess;
}

int VideoImpl::stopCapture()
{
   if (mVidCaptureId >=0)
   {
      stopLocalVideoRender();
      stopLocalVideoCapture();
   }
   return kSuccess;
}

int VideoImpl::startScreenshare(ScreenShareHandler* handler)
{
   mScreenShareHandler = handler;
   webrtc::ViEExternalCapture* externalCapture = NULL;
   int captureId = -1;
   MediaStackImpl* ms = mMediaStack;
   ms->vie_capture()->AllocateExternalCaptureDevice(captureId, externalCapture);

   if (mScreenShare != NULL)
   {
      stopScreenshare();
   }

#if !defined( __APPLE__ ) || TARGET_OS_IPHONE == 0
   mScreenShare = CPCAPI2::Media::ScreenShare::Create();
   mScreenShare->SetExternalCaptureInterface(externalCapture);
   mScreenShare->SetObserver(this);
   mScreenShare->SetMaxCaptureFrameRate(mScreenshareMaxFrameRate);

   mScreenShare->Start(mScreenshareDeviceId);
#endif

   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaStack->mixer());
   mixerImpl->setScreenShareCaptureId(captureId);
   const recon::Mixer::RtpStreams rtpStreams = mixerImpl->rtpStreams(recon::MediaStack::MediaType_Video);
   recon::Mixer::RtpStreams::const_iterator it = rtpStreams.begin();
   for (; it != rtpStreams.end(); ++it)
   {
      // reconnects the capture device to the ViE channel *only*
      // if RtpStream::disabledByUser() is false
      (*it)->resumeRtpSend();
   }

   return kSuccess;
}

int VideoImpl::stopScreenshare()
{
   webrtc_recon::MediaStackImpl* ms = mMediaStack;
   if (mScreenShare != NULL)
   {
      mScreenShare->Stop();
      delete mScreenShare;
      mScreenShare = NULL;
   }

   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaStack->mixer());
   int captureId = mixerImpl->getScreenShareCaptureId();

   if (ms->vie_capture()->ReleaseCaptureDevice(captureId) != -1)
   {
      DebugLog(<< "VideoImpl::stopScreenshare: released capture device");
   }

   mixerImpl->disconnectAllScreenshareVideoChannels();

   mScreenShareHandler = NULL;

   return kSuccess;
}

int VideoImpl::queryScreenshareDeviceList(ScreenshareDeviceListHandler* handler, bool includeMonitors, bool includeWindows)
{
#if !defined( __APPLE__ ) || TARGET_OS_IPHONE == 0
   std::unique_ptr<CPCAPI2::Media::ScreenShare> ss(CPCAPI2::Media::ScreenShare::Create());

   std::vector<CPCAPI2::Media::ScreenShareMonitorListItem> monitorList;
   std::vector<CPCAPI2::Media::ScreenShareWindowListItem> windowList;

   if (includeMonitors)
   {
      ss->GetMonitors(&monitorList);
   }

   if (includeWindows)
   {
      ss->GetWindows(&windowList);
   }

   if (handler != NULL)
   {
      ScreenshareDeviceListEvent args;
      for (const CPCAPI2::Media::ScreenShareMonitorListItem& li : monitorList)
      {
         ScreenshareDeviceInfo ssdi;
         ssdi.deviceDescription = li.title.c_str();
         ssdi.deviceId = li.id;
         ssdi.isWindow = false;
         args.devices.push_back(ssdi);
      }

      for (const CPCAPI2::Media::ScreenShareWindowListItem& li : windowList)
      {
         ScreenshareDeviceInfo ssdi;
         ssdi.deviceDescription = li.title.c_str();
         ssdi.deviceId = li.id;
         ssdi.isWindow = true;
         args.devices.push_back(ssdi);
      }

      mVideoInterface->fireScreenshareDeviceListUpdated(handler, args);
   }
#endif
   return kSuccess;
}

void VideoImpl::onScreenShareError(ScreenShare* /*sender*/)
{
   mVideoInterface->fireScreenShareError(mScreenShareHandler);
}

int VideoImpl::setScreenshareCaptureDevice(unsigned int deviceId)
{
   mScreenshareDeviceId = deviceId;
   return kSuccess;
}

int VideoImpl::setScreenshareCaptureMaxFramerate(unsigned int maxFrameRate)
{
   mScreenshareMaxFrameRate = maxFrameRate;
   if (mScreenShare != NULL)
   {
      mScreenShare->SetMaxCaptureFrameRate(maxFrameRate);
   }
   return kSuccess;
}

int VideoImpl::queryCodecList()
{
   if (mMediaStack->isInitialized())
   {
      mVideoCodecList.clear();
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
      CodecFactoryImpl::Codecs videoCodecs = codecFactory->videoCodecs(); // has to be a copy, since the one in the codec factory might get modified
      CodecFactoryImpl::Codecs::const_iterator itVideoCodecs = videoCodecs.begin();
      for (; itVideoCodecs != videoCodecs.end(); ++itVideoCodecs)
      {
         std::shared_ptr<CpsiCodec> c = *itVideoCodecs;

         if (c->licenses_total > 0)
         {
            if (resip::isEqualNoCase(c->settings().payload_name, "rtx"))
            {
               // we don't want this codec to show up in the UI ...
               // RTX should be enabled separately via Video::setRtxEnabled(..)
               continue;
            }

            VideoCodecInfoInternal info;
            info.video_codec_info.id = cpc::hash(c->settings().payload_name.c_str());
            info.video_codec_info.enabled = c->enabled;
            info.video_codec_info.priority = c->priority;
            if (c->display_name.empty())
            {
               info.video_codec_info.codecName = c->settings().payload_name.c_str();
            }
            else
            {
               info.video_codec_info.codecName = c->display_name.c_str();
            }
            info.video_codec_info.minBandwidth = c->min_bandwidth;
            info.video_codec_info.maxBandwidth = c->max_bandwidth;
            info.video_codec_info.payloadType = c->settings().local_payload_type;
            mVideoCodecList.push_back(info);
         }
      }

      if (mMediaHandler != NULL)
      {
         VideoCodecListUpdatedEvent args;
         for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
         {
            args.codecInfo.push_back(ci->video_codec_info);
         }
         ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCodecListUpdated, mMediaHandler, args);
         postCallback(callback);
      }
   }
   return kSuccess;
}

int VideoImpl::setCodecEnabled(unsigned int id, bool enabled)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<VideoCodecInfoInternal>::iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
   {
      if (ci->video_codec_info.id == id)
      {
         ci->video_codec_info.enabled = enabled;
         std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodecByDisplayName((ci->video_codec_info.codecName).c_str());
         if (c)
         {
            c->enabled = enabled;
         }
      }
   }
   return kSuccess;
}

int VideoImpl::setCodecPriority(unsigned int id, unsigned int priority)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
   {
      if (ci->video_codec_info.id == id)
      {
         std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodecByDisplayName(ci->video_codec_info.codecName.c_str());
         if (c)
         {
            c->priority = priority;
         }
         break;
      }
   }
   return kSuccess;
}

int VideoImpl::setCodecPayloadType(unsigned int id, unsigned int payloadType)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
   {
      if (ci->video_codec_info.id == id)
      {
         std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodecByDisplayName(ci->video_codec_info.codecName.c_str());
         if (c)
         {
            c->overridePayloadType(payloadType);
         }
         break;
      }
   }
   return kSuccess;
}

int VideoImpl::setCodecEncodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
   {
      if (ci->video_codec_info.id == codecId)
      {
         codecFactory->setHardwareAcceleration(ci->video_codec_info.codecName.c_str(), HardwareAccelerationEncoding, enabled);
      }
   }
   return kSuccess;
}

int VideoImpl::setCodecDecodingHardwareAccelerationEnabled(unsigned int codecId, bool enabled)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
   {
      if (ci->video_codec_info.id == codecId)
      {
         codecFactory->setHardwareAcceleration(ci->video_codec_info.codecName.c_str(), HardwareAccelerationDecoding, enabled);
      }
   }
   return kSuccess;
}

int VideoImpl::setPacketLossConfig(unsigned int codecId, const PacketLossConfig& packetLossConfig)
{
   bool shouldEnableRtx = false;
   if (packetLossConfig.packetLossStrategy == PacketLossStrategy_Retransmit_RTX_SSRC_multiplexing)
   {
      shouldEnableRtx = true;
   }

   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodec("rtx");
   if (c)
   {
      c->enabled = shouldEnableRtx;
   }
   return kSuccess;
}

int VideoImpl::setPreferredResolution(unsigned int id, VideoCaptureResolution resolution)
{
   InfoLog(<< "VideoImpl::setPreferredResolution(resolution=" << resolution << ")");
   if (mMediaStack->isInitialized())
   {
      unsigned short capWidth = (mCaptureWidth > 0 && mAppSpecifiedResolution ? (unsigned short)mCaptureWidth : std::numeric_limits<unsigned short>::max());
      unsigned short capHeight = (mCaptureHeight > 0 && mAppSpecifiedResolution ? (unsigned short)mCaptureHeight : std::numeric_limits<unsigned short>::max());
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());

      for (cpc::vector<VideoCodecInfoInternal>::iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
      {
         if (ci->video_codec_info.id == id || id == 0)
         {
            std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodecByDisplayName(ci->video_codec_info.codecName.c_str());
            if (c)
            {
               ci->preferred_resolution = resolution;
               std::vector<CodecSettings>* allSettings = &c->allSettings();
               std::vector<CodecSettings>::iterator itsettings = allSettings->begin();
               for (; itsettings != allSettings->end(); ++itsettings)
               {
                  switch (resolution)
                  {
                     case VideoCaptureResolution_Low:
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(176, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(144, capHeight);
                        break;
                     case VideoCaptureResolution_Standard:
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(352, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(288, capHeight);
                        break;
                     case VideoCaptureResolution_High:
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(640, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(480, capHeight);
                        break;
                     case VideoCaptureResolution_848x480p:
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(848, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(480, capHeight);
                        break;
                     case VideoCaptureResolution_HD_1280x720p:
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(1280, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(720, capHeight);
                        break;
                     case VideoCaptureResolution_HD_1920x1080p:
#if MOBILE_OS == 1
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(1280, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(720, capHeight);
#else
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(1920, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(1080, capHeight);
#endif
                        break;
                     case VideoCaptureResolution_MaxSupported:
#if MOBILE_OS == 1
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(1280, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(720, capHeight);
#else
                        if (itsettings->webrtcCodecInfo.video.mode == webrtc::kScreensharing)
                        {
                           // leave the resolution alone if it is already set
                           if (itsettings->webrtcCodecInfo.video.width == 0 &&
                              itsettings->webrtcCodecInfo.video.height == 0)
                           {
                              itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(1280, capWidth);
                              itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(720, capHeight);
                           }
                        }
                        else if (itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecVP8 ||
                            mPerfProfile == DevicePerformanceProfile_Desktop
                        )
                        {
                           itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(mMaxSupportedWidth, capWidth);
                           itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(mMaxSupportedHeight, capHeight);
                        }
                        else
                        {
                           itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(720, capWidth);
                           itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(480, capHeight);
                        }
#endif
                        break;
                     default:
                        break;
                  }

                  //Limit highest resolution for H265 on non desktop systems to VideoCaptureResolution_High?
                  if (itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecH265 &&
                     resolution > VideoCaptureResolution_High &&
                     mPerfProfile != DevicePerformanceProfile_Desktop)
                  {
                        itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(640, capWidth);
                        itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(480, capHeight);
                  }

                  InfoLog(<< "VideoImpl::setPreferredResolution(..) - set to " << itsettings->webrtcCodecInfo.video.width << " x " << itsettings->webrtcCodecInfo.video.height);

   #if MOBILE_OS == 1
                  itsettings->webrtcCodecInfo.video.maxFramerate = 15;
   #endif
                  if ( ((itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecH263) || (itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecH2631998)) &&
                       ((itsettings->webrtcCodecInfo.video.width > 352) || (itsettings->webrtcCodecInfo.video.height > 288)) )
                  {
                     itsettings->webrtcCodecInfo.video.width = std::min<unsigned short>(352, capWidth);
                     itsettings->webrtcCodecInfo.video.height = std::min<unsigned short>(288, capHeight);
                  }

                  // we're restricted to send no higher resolution than what the app has specified,
                  // even if the camera gives us frames that are bigger
                  itsettings->maxWidth = itsettings->webrtcCodecInfo.video.width;
                  itsettings->maxHeight = itsettings->webrtcCodecInfo.video.height;

                  unsigned int mobileMaxLevel = 11;

#if MOBILE_OS == 1
                  mobileMaxLevel = 31;
#endif

                  // set level to H264/H265 here, it will be used for offering to remote party
                  if (itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecH264
                     || itsettings->webrtcCodecInfo.video.codecType == webrtc::kVideoCodecH265)
                  {
                     setCodecLevel(resolution, itsettings->webrtcCodecInfo.video, mobileMaxLevel);
                     itsettings->fmtp = CodecFactoryImpl::makeFmtp(itsettings->webrtcCodecInfo.video);
                           }
                           }
                           }
            if (id != 0)
            {
               break;
            }
         }
      }

      if (!mAppSpecifiedResolution)
      {
         int highestCodecWidth = 0;
         int highestCodecHeight = 0;
         int highestCodecFramerate = 0;
         for (cpc::vector<VideoCodecInfoInternal>::iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
         {
            std::shared_ptr<CpsiCodec> c = codecFactory->getVideoCodecByDisplayName(ci->video_codec_info.codecName.c_str());
            if (c)
            {
               std::vector<CodecSettings>* allSettings = &c->allSettings();
               std::vector<CodecSettings>::iterator itsettings = allSettings->begin();
               for (; itsettings != allSettings->end(); ++itsettings)
               {
                  if (itsettings->webrtcCodecInfo.video.width > highestCodecWidth)
                  {
                     highestCodecWidth = itsettings->webrtcCodecInfo.video.width;
                  }
                  if (itsettings->webrtcCodecInfo.video.height > highestCodecHeight)
                  {
                     highestCodecHeight = itsettings->webrtcCodecInfo.video.height;
                  }
                  if ((int)itsettings->webrtcCodecInfo.video.maxFramerate > highestCodecFramerate)
                  {
                     highestCodecFramerate = (int)itsettings->webrtcCodecInfo.video.maxFramerate;
                  }
               }
            }
         }

         if (mCaptureWidth != highestCodecWidth || mCaptureHeight != highestCodecHeight || mCaptureFramerate != highestCodecFramerate)
         {
            if (mVidCaptureId > 0)
            {
               DebugLog(<< "VideoImpl::setPreferredResolution: stop capture");
               if (mMediaStack->vie_capture()->StopCapture(mVidCaptureId) == 0)
               {
                  webrtc::CaptureCapability captureCapa;
                  captureCapa.width = highestCodecWidth;
                  captureCapa.height = highestCodecHeight;
                  captureCapa.maxFPS = highestCodecFramerate;
                  DebugLog(<< "VideoImpl::setPreferredResolution: restart capture: " << highestCodecWidth << "x" << highestCodecHeight << " @ " << highestCodecFramerate << "fps");
                  if (mMediaStack->vie_capture()->StartCapture(mVidCaptureId, captureCapa) != -1)
                  {
                     DebugLog(<< "VideoImpl::setPreferredResolution: capturing");
                  }
               }
            }
            mCaptureWidth = highestCodecWidth;
            mCaptureHeight = highestCodecHeight;
            mCaptureFramerate = highestCodecFramerate;
         }
      }

   }
   return kSuccess;
}

void VideoImpl::updatePerformanceProfile(DevicePerformanceProfile perfProfile)
{
   if (mMediaStack->isInitialized())
   {
      mPerfProfile = perfProfile;

      for (cpc::vector<VideoCodecInfoInternal>::const_iterator ci = mVideoCodecList.begin(); ci != mVideoCodecList.end(); ++ci)
      {
         setPreferredResolution(ci->video_codec_info.id, ci->preferred_resolution);
      }
   }
}

int VideoImpl::setVideoMute(bool enabled)
{
   webrtc_recon::MediaStackImpl* ms = mMediaStack;
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(ms->mixer());
   if (enabled)
   {
      mixer->disconnectAllVideoChannels(true);
   }
   else
   {
      mixer->connectVideoCaptureDevice(true);
   }
   return kSuccess;
}

int VideoImpl::setVideoMixMode(int mixMode)
{
   webrtc_recon::MediaStackImpl* ms = mMediaStack;
   std::shared_ptr<webrtc_recon::MixerImpl> mixer = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(ms->mixer());
   mixer->setMixingEnabled(mixMode == (int)VideoMixMode_ClientMixing);
   return kSuccess;
}

int VideoImpl::setVideoDscp(unsigned int mediaDscp)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.mediaDscp = mediaDscp;
   mMediaStack->updateMediaSettings(settings);
   return kSuccess;
}

int VideoImpl::setVideoUseQosFastlane(bool useQosFastlane, bool useWithDscp)
{
   webrtc_recon::MediaStackSettings settings = *mMediaStack->settings();
   settings.useVideoQosFastlane = useQosFastlane;
   settings.useVideoQosFastlaneWithDscp = useQosFastlane ? useWithDscp : false;
   mMediaStack->updateMediaSettings(settings);
   return kSuccess;
}

int VideoImpl::showPropertyPage(void* surface)
{
#ifdef WIN32
   if ( mVidCaptureId >=0 )
   {
      return mMediaStack->vie_capture()->ShowCaptureSettingsDialogBox(mCaptureDeviceGuidUTF8.c_str(), mCaptureDeviceGuidUTF8.size(), "", surface, 0,0);
   }
   else return kError;
#else
   return kSuccess;
#endif
}

int VideoImpl::requestKeyFrame(int recv_channel)
{
   return mMediaStack->vie_rtp_rtcp()->RequestKeyFrame(recv_channel);
}

void VideoImpl::postCallback(resip::ReadCallbackBase* fp)
{
   mVideoInterface->postCallback(fp);
}

void VideoImpl::startLocalVideoCapture(bool connectToActiveCalls)
{
   webrtc_recon::MediaStackImpl* ms = mMediaStack;

   if (mVidCaptureId >= 0)
   {
      DebugLog(<< "VideoImpl::startLocalVideoCapture: already capturing; captureId=" << mVidCaptureId);
      return;
   }

   int numCaptureDevs = ms->vie_capture()->NumberOfCaptureDevices();
   if (numCaptureDevs >= 0)
   {
      startLocalCaptureDiagFpsMonitor();
   
      int captureId = -1;
      if (mCaptureDeviceGuidUTF8.size() == 0)
      {
         char deviceName[255];
         char deviceGuid[255];
         if (numCaptureDevs > 0 && ms->vie_capture()->GetCaptureDevice(0, deviceName,
            sizeof(deviceName), deviceGuid, sizeof(deviceGuid)) == 0)
         {
            DebugLog(<< "VideoImpl::startLocalVideoCapture no capture device selected; defaulting to " << deviceName << " guid " << deviceGuid);
            mCaptureDeviceGuidUTF8 = deviceGuid;
            mCaptureDeviceName = deviceName;
         }
      }

      if (mCaptureDeviceGuidUTF8.size() > 0)
      {
         if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), SCREEN_SHARE_SOURCE_ID) == 0)
         {
            webrtc::ViEExternalCapture* externalCapture = NULL;
            ms->vie_capture()->AllocateExternalCaptureDevice(captureId, externalCapture);

#if !defined( __APPLE__ ) || TARGET_OS_IPHONE == 0
            mScreenShare = CPCAPI2::Media::ScreenShare::Create();
            mScreenShare->SetExternalCaptureInterface(externalCapture);
            mScreenShare->SetObserver(this);
            mScreenShare->SetMaxCaptureFrameRate(mScreenshareMaxFrameRate);
#endif
         }
         else if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), CUSTOM_VIDEO_SOURCE_ID) == 0)
         {
            webrtc::ViEExternalCapture* externalCapture = NULL;
            ms->vie_capture()->AllocateExternalCaptureDevice(captureId, externalCapture);

            mCustomVideoSource = CPCAPI2::Media::CustomVideoSource::Create();
            mCustomVideoSource->SetExternalCaptureInterface(externalCapture);
         }
         else
         {
            ms->vie_capture()->AllocateCaptureDevice(mCaptureDeviceGuidUTF8.c_str(), mCaptureDeviceGuidUTF8.size(), captureId);
            if (captureId == -1)
            {
               // todo: we should probably fire a callback to the app, so they can inform the user
               ErrLog(<< "Failed to allocate capture device " << mCaptureDeviceName << " err: " << mMediaStack->vie_base()->LastError());

               VideoCaptureStateChangedEvent args;
               args.state = VideoCaptureState_Error;
               ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCaptureStateChanged, mMediaHandler, args);
               postCallback(callback);
            }
            else
            {
               // needs to be called after AllocateCaptureDevice
               ms->vie_capture()->RegisterObserver(captureId, *this);
            }
         }
      }

      if (captureId != -1)
      {
         mVidCaptureId = captureId;
         DebugLog(<< "VideoImpl::startLocalVideoCapture: allocated capture device " << mVidCaptureId << " friendlyName: " << mCaptureDeviceName);

//         webrtc::ViEPicture blackPicture;
//         blackPicture.type = webrtc::kVideoI420;
//         blackPicture.width = 176;
//         blackPicture.height = 144;
//         webrtc_recon::MediaUtils::FillI420FrameWithBlack(blackPicture);
//         free(blackPicture.data);

         //ms->vie_capture()->RegisterObserver(mVidCaptureId, sCaptureObserver);
         //sCaptureObserver.registerCallbackNotifications(mVidCaptureId, shared_from_this());

         if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), SCREEN_SHARE_SOURCE_ID) == 0)
         {
            mScreenShare->Start(mScreenshareDeviceId);
         }
         else if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), CUSTOM_VIDEO_SOURCE_ID) == 0)
         {
            // we can't start it until we are receiving an incoming video stream!
            if (mCustomVideoSource)
            {
               mCustomVideoSource->Start();
            }
         }
         else
         {
            webrtc::CaptureCapability captureCapa;
            if (mCaptureWidth > 0 && mCaptureHeight > 0)
            {
               captureCapa.width = mCaptureWidth;
               captureCapa.height = mCaptureHeight;
               captureCapa.maxFPS = (mCaptureFramerate > 0 ? mCaptureFramerate : 15);
            }
            DebugLog(<< "VideoImpl::startLocalVideoCapture: setup capture for " << captureCapa.width << "x" << captureCapa.height << " @ " << captureCapa.maxFPS << "fps");
            if (ms->vie_capture()->StartCapture(mVidCaptureId, captureCapa) != -1)
            {
               DebugLog(<< "VideoImpl::startLocalVideoCapture: capturing");
            }
         }
         signalCaptureStarted(mVidCaptureId);
         // if the local render surface was set previously, start rendering to it
         setCaptureDeviceOrientation(mVidCaptureOrientation);
         setCaptureImageOrientation(mVidImageOrientation);
         attachLocalVideoCaptureRender();
      }
   }
}

void VideoImpl::stopLocalVideoCapture()
{
   stopLocalCaptureDiagFpsMonitor();

   webrtc_recon::MediaStackImpl* ms = mMediaStack;
   ms->vie_capture()->DeregisterObserver(mVidCaptureId);
   if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), CUSTOM_VIDEO_SOURCE_ID) == 0)
   {
      mCustomVideoSource->Stop();
      delete mCustomVideoSource;
      mCustomVideoSource = NULL;
   }
   else
   {
      if (ms->vie_capture()->StopCapture(mVidCaptureId) != -1)
      {
        DebugLog(<< "VideoImpl::stopLocalVideoCapture: stopped capturing (captureId " << mVidCaptureId << ")");
      }
   }

   if (ms->vie_capture()->ReleaseCaptureDevice(mVidCaptureId) != -1)
   {
      DebugLog(<< "VideoImpl::stopLocalVideoCapture: released capture device");
   }
   mVidCaptureId = -2;

   signalCaptureStopped();
}

void VideoImpl::startLocalVideoRender(void* surface, VideoSurfaceType type)
{
   void* previousSurface = mLocalVideoSurface;
   webrtc::VideoRenderSurfaceType previousSurfaceType = mLocalVideoSurfaceType;
   mLocalVideoSurface = surface;

#ifdef _WIN32
   if (VideoSurfaceType_WindowsHWND == type)
   {
      InfoLog(<< "startLocalVideoRender(" << surface << ") - surface is a window");
      mLocalVideoSurfaceType = webrtc::kRenderSurfaceWindows;
   }
   else if (VideoSurfaceType_WindowsWpfControl == type)
   {
      InfoLog(<< "startLocalVideoRender(" << surface << ") - surface is a WPF control");
      mLocalVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
   }

   if (webrtc::kRenderSurfaceDefault == mLocalVideoSurfaceType)
   {
      if (::IsWindow((HWND)surface))
      {
         InfoLog(<< "startLocalVideoRender(" << surface << ") - detected that surface is a window");
         mLocalVideoSurfaceType = webrtc::kRenderSurfaceWindows;
      }
      else
      {
         InfoLog(<< "startLocalVideoRender(" << surface << ") - detected that surface is WPF control");
         mLocalVideoSurfaceType = webrtc::kRenderSurfaceWindowsDirectX;
      }
   }
#endif

   if (!videoSurfacesAreDifferent(mLocalVideoSurface, mLocalVideoSurfaceType, previousSurface, previousSurfaceType))
   {
      WarningLog(<< "VideoImpl::startLocalVideoRender: already rendering local video to surface " << mLocalVideoSurface);
      return;
   }
   stopLocalVideoRender();
   DebugLog(<< "VideoImpl::startLocalVideoRender: set local render surface " << mLocalVideoSurface);
   // if the capture was already started, start rendering to the surface
   attachLocalVideoCaptureRender();
}

void VideoImpl::attachLocalVideoCaptureRender()
{
   stopLocalVideoRender();
   if (mLocalVideoSurface != NULL && mVidCaptureId >= 0)
   {
      // [AB] use video preview directly from capturer, if supported
      bool supported;
      if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), SCREEN_SHARE_SOURCE_ID) == 0)
      {
         supported = false;
      }
      else if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), CUSTOM_VIDEO_SOURCE_ID) == 0)
      {
         supported = false;
      }
      else
      {
         mMediaStack->vie_capture()->IsVideoCapturePreviewSupported(mVidCaptureId, supported);
      }

      if (supported)
      {
         mMediaStack->vie_capture()->SetVideoCapturePreviewTarget(mVidCaptureId,
            mLocalVideoSurface
            );
      }
      else
      {
         if (mMediaStack->vie_render()->AddRenderer(
            mVidCaptureId,
            mLocalVideoSurface,
            mLocalVideoSurfaceType,
            1,
            0.0f, 0.0f,
            1.0f, 1.0f) != -1)
         {
            DebugLog(<< "VideoImpl::attachLocalVideoCaptureRender: added capture renderer " << mLocalVideoSurface);
         }
         else
         {
            ErrLog(<< "VideoImpl::attachLocalVideoCaptureRender: failed to add renderer; last error: " << mMediaStack->vie_base()->LastError());
         }

         if (mMediaStack->vie_render()->StartRender(mVidCaptureId) != -1)
         {
            DebugLog(<< "VideoImpl::attachLocalVideoCaptureRender: started render (captureId " << mVidCaptureId << ")");
         }
         else
         {
            ErrLog(<< "VideoImpl::attachLocalVideoCaptureRender: failed to start renderer; last error: " << mMediaStack->vie_base()->LastError());
         }
      }

      mCaptureRenderAttached = true;
   }
}

void VideoImpl::stopLocalVideoRender()
{
   if (mCaptureRenderAttached && mVidCaptureId >=0)
   {
      //[AB] detach video capture preview, if supported
      bool supported;
      if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), SCREEN_SHARE_SOURCE_ID) == 0)
      {
         supported = false;
      }
      else if (strcasecmp(mCaptureDeviceGuidUTF8.c_str(), CUSTOM_VIDEO_SOURCE_ID) == 0)
      {
         supported = false;
      }
      else
      {
         mMediaStack->vie_capture()->IsVideoCapturePreviewSupported(mVidCaptureId, supported);
      }

      {
         if(supported)
         {
            mMediaStack->vie_capture()->SetVideoCapturePreviewTarget(mVidCaptureId, NULL);
         }
         else
         {
            webrtc_recon::MediaStackImpl* ms = mMediaStack;
            if (ms->vie_render()->StopRender(mVidCaptureId) != -1)
            {
               DebugLog(<< "VideoImpl::stopLocalVideoRender: stopped rendering (captureId " << mVidCaptureId << ")");
            }
            if (ms->vie_render()->RemoveRenderer(mVidCaptureId) != -1)
            {
               DebugLog(<< "VideoImpl::stopLocalVideoRender: removed renderer");
            }
         }

         mCaptureRenderAttached = false;
      }
   }
}

void VideoImpl::signalIncomingVideoRenderTargetChanged(int channel, void* /*oldSurface*/, void* newSurface, webrtc::VideoRenderSurfaceType newSurfaceType)
{
   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaStack->mixer());
   mixerImpl->removeVideoRenderer(channel, newSurface);
   mixerImpl->addVideoRenderer(channel, newSurface, newSurfaceType);
}

void VideoImpl::signalCaptureStopped()
{
   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaStack->mixer());
   mixerImpl->disconnectAllVideoChannels();

   VideoCaptureStateChangedEvent args;
   args.state = VideoCaptureState_Stopped;
   ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCaptureStateChanged, mMediaHandler, args);
   postCallback(callback);
}

void VideoImpl::signalCaptureStarted(int captureId)
{
   webrtc_recon::MixerImplPtr mixerImpl = std::dynamic_pointer_cast<webrtc_recon::MixerImpl>(mMediaStack->mixer());
   mixerImpl->setCaptureId(captureId);
   const recon::Mixer::RtpStreams rtpStreams = mixerImpl->rtpStreams(recon::MediaStack::MediaType_Video);
   recon::Mixer::RtpStreams::const_iterator it = rtpStreams.begin();
   for (; it != rtpStreams.end(); ++it)
   {
      // reconnects the capture device to the ViE channel *only*
      // if RtpStream::disabledByUser() is false
      (*it)->resumeRtpSend();
   }

   VideoCaptureStateChangedEvent args;
   args.state = VideoCaptureState_Started;
   ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCaptureStateChanged, mMediaHandler, args);
   postCallback(callback);
}

int VideoImpl::connectVideoStreams(int recv_channel, int send_channel)
{
   mMediaStack->vie_capture()->DisconnectCaptureDevice(send_channel);
   mMediaStack->vie_conference()->AddToPassthruConference(recv_channel, send_channel);
   mMediaStack->vie_rtp_rtcp()->RequestKeyFrame(recv_channel);
   return kSuccess;
}

int VideoImpl::disconnectVideoStreams(int recv_channel, int send_channel)
{
   mMediaStack->vie_conference()->RemoveFromPassthruConference(recv_channel, send_channel);
   if (mVidCaptureId > 0)
   {
      mMediaStack->vie_capture()->ConnectCaptureDevice(mVidCaptureId, send_channel);
   }
   return kSuccess;
}

bool VideoImpl::videoSurfacesAreDifferent(void* s1, webrtc::VideoRenderSurfaceType s1type, void* s2, webrtc::VideoRenderSurfaceType s2type)
{
#ifdef _WIN32
   if (s1type == s2type)
   {
      return (s1 != s2);
   }
   return true;
#else
   return (s1 != s2);
#endif
}

void VideoImpl::setCodecLevel(VideoCaptureResolution resolution, webrtc::VideoCodec& video, unsigned int mobileMaxLevel)
{
   if (video.codecType == webrtc::kVideoCodecH264)
   {
      switch (resolution)
      {
      case VideoCaptureResolution_Low:
         video.codecSpecific.H264.level = 11;
         break;
      case VideoCaptureResolution_Standard:
         video.codecSpecific.H264.level = 12;
         break;
      case VideoCaptureResolution_High:
         video.codecSpecific.H264.level = 22;
         break;
      case VideoCaptureResolution_848x480p:
         video.codecSpecific.H264.level = 30;
         break;
      case VideoCaptureResolution_HD_1280x720p:
         if (mPerfProfile == DevicePerformanceProfile_Desktop)
         {
            video.codecSpecific.H264.level = 31;
         }
         else
         {
            video.codecSpecific.H264.level = mobileMaxLevel;
         }
         break;
      case VideoCaptureResolution_HD_1920x1080p:
         if (mPerfProfile == DevicePerformanceProfile_Desktop)
         {
            video.codecSpecific.H264.level = 51;
         }
         else
         {
            video.codecSpecific.H264.level = mobileMaxLevel;
         }
         break;
      case VideoCaptureResolution_MaxSupported:
         if (mPerfProfile == DevicePerformanceProfile_Desktop)
         {
            if (mMaxSupportedWidth == 1920 && mMaxSupportedHeight == 1080)
            {
               video.codecSpecific.H264.level = 51;
            }
            else
            {
               video.codecSpecific.H264.level = 31;
            }
         }
         else
         {
            video.codecSpecific.H264.level = mobileMaxLevel;
         }
         break;
      default:
         break;
      }
   }
   else if (video.codecType == webrtc::kVideoCodecH265)
   {
      //no hardware acceleration for h265 yet
      unsigned int maxMobileLevel = 30;
      switch (resolution)
      {
      case VideoCaptureResolution_Low:
         video.codecSpecific.H265.level = 10;
         break;
      case VideoCaptureResolution_Standard:
         video.codecSpecific.H265.level = 20;
         break;
      case VideoCaptureResolution_High:
      case VideoCaptureResolution_848x480p:
         video.codecSpecific.H265.level = 30;
         break;
      case VideoCaptureResolution_HD_1280x720p:
         video.codecSpecific.H265.level =
            (mPerfProfile == DevicePerformanceProfile_Desktop) ? 31 : maxMobileLevel;
         break;
      case VideoCaptureResolution_HD_1920x1080p:
      case VideoCaptureResolution_MaxSupported:
         video.codecSpecific.H265.level =
            (mPerfProfile == DevicePerformanceProfile_Desktop) ? 40 : maxMobileLevel;
         break;
      default:
         break;
      }
   }
}

// This method is called if a bright or dark captured image is detected.
void VideoImpl::BrightnessAlarm(const int capture_id,
                             const webrtc::Brightness brightness)
{
   // warning: called on webrtc process thread
}

// This method is called periodically telling the capture device frame rate.
void VideoImpl::CapturedFrameRate(const int capture_id,
                               const unsigned char frame_rate)
{
   // warning: called on webrtc process thread
   mLocalCaptureDiagLastFramerate = static_cast<int>(frame_rate);
}


// This method is called if the capture device stops delivering images to
// VideoEngine.
void VideoImpl::NoPictureAlarm(const int capture_id,
                             const webrtc::CaptureAlarm alarm)
{
   // warning: called on webrtc process thread
   mPhone->getSdkModuleThread().post(resip::resip_bind(&VideoImpl::noPictureAlarmImpl, this, capture_id, alarm ));
}

void VideoImpl::noPictureAlarmImpl(const int capture_id,
                                 const webrtc::CaptureAlarm alarm)
{
   if (alarm == webrtc::AlarmRaised)
   {
      //DebugLog(<< "no pic Scheduling timer");
      mLocalCaptureDiagNoPicAlarmTimer.cancel();
      mLocalCaptureDiagNoPicAlarmTimer.expires_from_now(kLocalCaptureDiagNoPicAlarmTimerIntervalMs);
      mLocalCaptureDiagNoPicAlarmTimer.async_wait(this, kLocalCaptureDiagNoPicAlarmTimerId, NULL);
   }
   else
   {
      if (!mLocalCaptureDiagNoPicAlarmTimer.isActive())
      {
         // only log if the timer already has fired
         InfoLog(<< "Camera capture no picture alarm cleared");

         VideoCaptureStateChangedEvent args;
         args.state = VideoCaptureState_Resumed;
         ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCaptureStateChanged, mMediaHandler, args);
         postCallback(callback);
      }
      mLocalCaptureDiagNoPicAlarmTimer.cancel();
   }
}

void VideoImpl::startLocalCaptureDiagFpsMonitor()
{
   mLocalCaptureDiagFpsTimer.expires_from_now(kLocalCaptureDiagFpsTimerIntervalMs);
   mLocalCaptureDiagFpsTimer.async_wait(this, kLocalCaptureDiagFpsTimerId, NULL);
   mLocalCaptureDiagLastFramerate = -1;
}

void VideoImpl::stopLocalCaptureDiagFpsMonitor()
{
   mLocalCaptureDiagFpsTimer.cancel();
   mLocalCaptureDiagNoPicAlarmTimer.cancel();
   mLocalCaptureDiagLastFramerate = -1;
}

void VideoImpl::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == kLocalCaptureDiagNoPicAlarmTimerId)
   {
      // todo: we should probably fire a callback to the app, so they can inform the user
      WarningLog(<< "Camera capture no picture alarm active since " << kLocalCaptureDiagNoPicAlarmTimerIntervalMs << " sec ago");
      mLocalCaptureDiagNoPicAlarmTimer.cancel();

      VideoCaptureStateChangedEvent args;
      args.state = VideoCaptureState_Hung;
      ReadCallbackBase* callback = makeFpCommand1(VideoHandler::onVideoCaptureStateChanged, mMediaHandler, args);
      postCallback(callback); 
   }
   else if (timerId == kLocalCaptureDiagFpsTimerId)
   {
      if (mLocalCaptureDiagLastFramerate != -1)
      {
         DebugLog(<< "Camera capture FPS: " << mLocalCaptureDiagLastFramerate);
      }
      mLocalCaptureDiagFpsTimer.cancel();
      mLocalCaptureDiagFpsTimer.expires_from_now(kLocalCaptureDiagFpsTimerIntervalMs);
      mLocalCaptureDiagFpsTimer.async_wait(this, kLocalCaptureDiagFpsTimerId, NULL);
   }
}


std::ostream& operator<<(std::ostream& os, const VideoDeviceListUpdatedEvent& evt)
{
   os << "deviceInfo list: ";
   for (cpc::vector<VideoDeviceInfo>::const_iterator it = evt.deviceInfo.begin(); it != evt.deviceInfo.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   
   return os;
}

std::ostream& operator<<(std::ostream& os, const VideoDeviceInfo& info)
{
   os << "friendlyName: " << info.friendlyName << ", id: " << info.id << ", index: " << info.cameraIndex << ", orientation: " << info.orientation;
   return os;
}

}
}
#endif //CPCAPI2_BRAND_VIDEO_MODULE
#endif // CPCAPI2_BRAND_MEDIA_MODULE
