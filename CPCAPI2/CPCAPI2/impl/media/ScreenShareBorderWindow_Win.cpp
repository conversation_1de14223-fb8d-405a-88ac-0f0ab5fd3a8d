#include "ScreenShareBorderWindow_Win.h"

#if defined( _WIN32 )

#include <webrtc/modules/desktop_capture/win/screen_capture_utils.h>
#include <assert.h>
#include <dwmapi.h>

#define BORDER_WIDTH       3
#define TIMER_ID_UPDATE   999
const COLORREF BORDER_COLOR = RGB(255, 0, 0);

namespace CPCAPI2
{
namespace Media
{

static ScreenShareBorderWindow* s_instance = NULL;

const wchar_t* ScreenShareBorderWindow::WindowTitles[5] =
{
   L"ScreenShare Left",
   L"ScreenShare Top",
   L"ScreenShare Right",
   L"ScreenShare Bottom",
   L"ScreenShare Window"
};

ScreenShareBorderWindow::ScreenShareBorderWindow(WindowLocation windowLocation) : mWindowLocation(windowLocation)
{
   s_instance = this;
   dwmapi_library_ = LoadLibraryW(L"dwmapi.dll");
   if (dwmapi_library_) 
   {
      dwm_get_window_attribute_func_ =
         reinterpret_cast<webrtc::DwmGetWindowAttributeFunc>(
            GetProcAddress(dwmapi_library_, "DwmGetWindowAttribute"));
      dwm_set_window_attribute_func_ =
         reinterpret_cast<webrtc::DwmSetWindowAttributeFunc>(
            GetProcAddress(dwmapi_library_, "DwmSetWindowAttribute"));
   }
}

ScreenShareBorderWindow::~ScreenShareBorderWindow()
{
   for (auto hook : mHookEvents)
   {
      UnhookWinEvent(hook);
   }
   if (dwmapi_library_) {
      FreeLibrary(dwmapi_library_);
   }

   s_instance = NULL;
}

void CALLBACK ScreenShareBorderWindow::WinEventHookProc(HWINEVENTHOOK hWinEventHook, DWORD event, HWND hwnd, LONG idObject, LONG idChild, DWORD dwEventThread, DWORD dwmsEventTime)
{
   if (hwnd == NULL || idObject != OBJID_WINDOW || !(event == EVENT_OBJECT_LOCATIONCHANGE || event == EVENT_SYSTEM_FOREGROUND || event == EVENT_OBJECT_DESTROY))
      return;

   if (s_instance != NULL)
      s_instance->onWindowHookEvent(hwnd, event);
}

RECT ScreenShareBorderWindow::getCurrentRectForWindow(HWND hwnd)
{
   RECT rect = {};
   if (dwm_get_window_attribute_func_ != nullptr)
   {
      // Vista and up - ignores shadows etc
      dwm_get_window_attribute_func_(hwnd, DWMWA_EXTENDED_FRAME_BOUNDS, &rect, sizeof(rect));
   }
   else
   {
      GetWindowRect(hwnd, &rect);
   }
   return rect;
}

RECT ScreenShareBorderWindow::getBorderRect()
{
   RECT rect = getCurrentRectForWindow((HWND)mScreenshareDeviceId);  

   if (::IsZoomed((HWND)mScreenshareDeviceId))
   {
      // Maximized window, border surrounds entire screen
      MONITORINFO monInfo;
      monInfo.cbSize = sizeof(MONITORINFO);
      GetMonitorInfoW(MonitorFromWindow((HWND)mScreenshareDeviceId, MONITOR_DEFAULTTONEAREST), &monInfo); 
      rect.left = monInfo.rcWork.left;
      rect.top = monInfo.rcWork.top;
      rect.bottom = monInfo.rcWork.bottom > rect.bottom ? rect.bottom : monInfo.rcWork.bottom;
      rect.right = monInfo.rcWork.right > rect.right ? rect.right : monInfo.rcWork.right;
   }
   else
   {
      InflateRect(&rect, BORDER_WIDTH, BORDER_WIDTH);
   }

   return rect;

}

void ScreenShareBorderWindow::showWindow(unsigned int screenshareDeviceId)
{
   if (handle() != 0)
   {
      Destroy();
   }

   mScreenshareDeviceId = screenshareDeviceId;

   if (mWindowLocation == FullWindow)
   {
      bool maximized = false;
      RECT winRect = getBorderRect();
      if (Create(NULL, 
         WindowTitles[mWindowLocation], 
         WS_POPUP | WS_VISIBLE | WS_DISABLED, 
         WS_EX_TOOLWINDOW | WS_EX_NOACTIVATE | WS_EX_LAYERED | WS_EX_TRANSPARENT,
         0, 0, 0, 0))
      {
         SetLayeredWindowAttributes(handle(), TRANSPARENT, 255, LWA_COLORKEY);
         // Add hook for updating window position when target changes
         DWORD processId;
         DWORD threadId = GetWindowThreadProcessId((HWND)mScreenshareDeviceId, &processId);
         mHookEvents.push_back((HWINEVENTHOOK)SetWinEventHook(EVENT_OBJECT_LOCATIONCHANGE, EVENT_OBJECT_LOCATIONCHANGE, NULL, &ScreenShareBorderWindow::WinEventHookProc, processId, threadId, WINEVENT_OUTOFCONTEXT));
         mHookEvents.push_back((HWINEVENTHOOK)SetWinEventHook(EVENT_SYSTEM_FOREGROUND, EVENT_SYSTEM_FOREGROUND, NULL, &ScreenShareBorderWindow::WinEventHookProc, processId, threadId, WINEVENT_OUTOFCONTEXT));
         mHookEvents.push_back((HWINEVENTHOOK)SetWinEventHook(EVENT_OBJECT_DESTROY, EVENT_OBJECT_DESTROY, NULL, &ScreenShareBorderWindow::WinEventHookProc, processId, threadId, WINEVENT_OUTOFCONTEXT));
         updateWindow();
      }
   }
   else
   {
      webrtc::DesktopRect screenRect;
      std::wstring screenDeviceKey;
      if (webrtc::IsScreenValid(screenshareDeviceId, &screenDeviceKey))
      {
         screenRect = webrtc::GetScreenRect(screenshareDeviceId, screenDeviceKey);
      }

      if (!screenRect.is_empty() && Create(NULL, WindowTitles[mWindowLocation], WS_POPUP, WS_EX_TOOLWINDOW | WS_EX_TOPMOST, screenRect.left(), screenRect.top(), 100, 100))
      {
         // hide title
         SetWindowLongPtr(handle(), GWL_STYLE, GetWindowLongPtr(handle(), GWL_STYLE) & ~(WS_CAPTION));
         // Exclude from Aero Peek - OBELISK-6024
         if (dwm_set_window_attribute_func_ != nullptr)
         {
            DWMNCRENDERINGPOLICY RenderPolicy = DWMNCRP_ENABLED;

            dwm_set_window_attribute_func_(handle(), DWMWA_EXCLUDED_FROM_PEEK, &RenderPolicy, sizeof(RenderPolicy));
         }         

         RECT rect = { 0 };
         switch (mWindowLocation)
         {
         case Left:
            rect.left = screenRect.left();
            rect.top = screenRect.top();
            rect.right = screenRect.left() + BORDER_WIDTH;
            rect.bottom = screenRect.bottom();
            break;
         case Top:
            rect.left = screenRect.left();
            rect.top = screenRect.top();
            rect.right = screenRect.right();
            rect.bottom = screenRect.top() + BORDER_WIDTH;
            break;
         case Right:
            rect.left = screenRect.right() - BORDER_WIDTH;
            rect.top = screenRect.top();
            rect.right = screenRect.right();
            rect.bottom = screenRect.bottom();
            break;
         case Bottom:
            rect.left = screenRect.left();
            rect.top = screenRect.bottom() - BORDER_WIDTH;
            rect.right = screenRect.right();
            rect.bottom = screenRect.bottom();
            break;
         default:
            assert(0);
            break;
         }

         MoveWindow(handle(), rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, TRUE);
      }
   }

   if (handle())
   {
      ShowWindow(handle(), SW_SHOW);
      UpdateWindow(handle()); // Force paint
   }
}

void ScreenShareBorderWindow::updateWindow()
{
   WINDOWPLACEMENT windowPlacement = { sizeof(WINDOWPLACEMENT), };
   if (::GetWindowPlacement((HWND)mScreenshareDeviceId, &windowPlacement))
   {
      if (windowPlacement.showCmd == SW_SHOWMINIMIZED
         || !IsWindowVisible((HWND)mScreenshareDeviceId))
      {
         ShowWindow(handle(), SW_HIDE);
      }
      else
      {
         RECT rect = getBorderRect();
         ::SetWindowPos(handle(), GetNextWindow((HWND)mScreenshareDeviceId, GW_HWNDPREV), 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
         ::SetWindowPos(handle(), 0, rect.left, rect.top, rect.right - rect.left, rect.bottom - rect.top, SWP_NOZORDER | SWP_SHOWWINDOW);
      }
   }
}

void ScreenShareBorderWindow::onWindowHookEvent(HWND hwnd, DWORD event)
{
   if (event == EVENT_OBJECT_LOCATIONCHANGE)
   {      
      // if the size/position of the window didn't change, ignore
      RECT oldBorderRect = getCurrentRectForWindow((HWND)handle());
      RECT newBorderRect = getBorderRect();
      if (EqualRect(&oldBorderRect, &newBorderRect) && !IsIconic(hwnd))
      {
         return;
      }

      // Delay window repositioning until all events are done to prevent flickering
      ShowWindow(handle(), SW_HIDE);
      SetTimer(handle(), TIMER_ID_UPDATE, 200, NULL);
   }
   else
   {
      updateWindow();
   }
}

bool ScreenShareBorderWindow::OnMessage(UINT message, WPARAM wParam, LPARAM lParam, LRESULT& result)
{
   switch (message)
   {
   case WM_DISPLAYCHANGE:
      // screen resolution change: recreate window
      showWindow(mScreenshareDeviceId);
      return true;
   case WM_PAINT:
      {
         // NOTE: When sharing the full screen this paints one edge only.
         // Paint transparent rectangle with border
         PAINTSTRUCT ps{};
         HDC hdc = BeginPaint(handle(), &ps);

         RECT rc{}; GetClientRect(handle(), &rc);
         HPEN hPen = CreatePen(PS_SOLID, BORDER_WIDTH*2, BORDER_COLOR);
         HBRUSH hBrush = CreateSolidBrush(TRANSPARENT);
         HGDIOBJ hOldPen = SelectObject(hdc, hPen);
         HGDIOBJ hOldBrush = SelectObject(hdc, hBrush);

         Rectangle(hdc, rc.left, rc.top, rc.right, rc.bottom);

         if (hOldPen)
            SelectObject(hdc, hOldPen);
         if (hOldBrush)
            SelectObject(hdc, hOldBrush);
         if (hPen)
            DeleteObject(hPen);
         if (hBrush)
            DeleteObject(hBrush);

         EndPaint(handle(), &ps);
      }
      break;
   case WM_TIMER:
      if (wParam == TIMER_ID_UPDATE)
      {
         KillTimer(handle(), TIMER_ID_UPDATE);
         updateWindow();
         return true;
      }
      break;
   default:
      break;
   }

   return false;
}

}
}

#endif
