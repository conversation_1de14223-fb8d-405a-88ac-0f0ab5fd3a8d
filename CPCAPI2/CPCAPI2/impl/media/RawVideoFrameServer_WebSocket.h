#pragma once

#if !defined(CPCAPI2_RAW_VIDEO_FRAME_SERVER_WEBSOCKET_H)
#define CPCAPI2_RAW_VIDEO_FRAME_SERVER_WEBSOCKET_H

#include "cpcapi2defs.h"
#include "videostreaming/VideoStreaming.h"
#include "RawVideoFrameServer.h"

#include <rutil/MultiReactor.hxx>
#include <contrib/folly/ProducerConsumerQueue.h>

#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
#include <websocketpp/config/asio.hpp>
#else
#include <websocketpp/config/asio_no_tls.hpp>
#endif
#include <websocketpp/server.hpp>

// rapidjson
#include <document.h>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>

namespace CPCAPI2
{
   class PhoneInterface;
   namespace Media
   {
      class RawVideoFrameServerWebSocket;

#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
      typedef websocketpp::server<websocketpp::config::asio_tls> server;
#else
      typedef websocketpp::server<websocketpp::config::asio> server;
#endif
      typedef server::connection_ptr conn_ptr;
      typedef server::message_ptr message_ptr;

      class RawVideoFrameServerWsSession
      {
      public:
         RawVideoFrameServerWsSession(CPCAPI2::PhoneInterface* masterSdkPhone, RawVideoFrameServerWebSocket* transport, const websocketpp::connection_hdl& conn);
         virtual ~RawVideoFrameServerWsSession();

         void SendBitstreamData(int videoStreamHandle, bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen);
         bool NeedsKeyframe(int videoStreamHandle);
         size_t GetStreamCount(int videoStreamHandle);
         void CloseStreams(int videoStreamHandle);

         void close();

         void on_close(server* s, websocketpp::connection_hdl hdl);
         void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);

      private:
         void handleWsServerMessage(server* s, websocketpp::connection_hdl hdl, const std::shared_ptr<rapidjson::Document>& doc);

         websocketpp::connection_hdl mConn;
         CPCAPI2::PhoneInterface* mMasterPhone;
         RawVideoFrameServerWebSocket* mTransport;

         struct PerConnectionStreamState
         {
            PerConnectionStreamState(int vs)
               : videoStream(vs)
            {
               mWaitingForKeyFrame = true;
               mRecvCnt = 0;
               mSendCnt = 0;
            }
            int videoStream;
            bool mWaitingForKeyFrame;
            int mRecvCnt;
            int mSendCnt;
         };
         std::map<int, std::shared_ptr<PerConnectionStreamState> > mStreamStateMap;
      };

      class RawVideoFrameServerWebSocket : public RawVideoFrameServer
      {
      public:
         RawVideoFrameServerWebSocket();
         virtual ~RawVideoFrameServerWebSocket();

         virtual void StartServer(CPCAPI2::Phone* phone, const CPCAPI2::VideoStreaming::VideoStreamingServerConfig& serverConfig, resip::ReadCallbackBase* startedCb, std::function<void()> onPrivilegedAccessCompleted) OVERRIDE;
         virtual void StopServer() OVERRIDE;
         virtual void AddObserver(RawVideoFrameServerObserver* obs) OVERRIDE;
         virtual void RemoveObserver(RawVideoFrameServerObserver* obs) OVERRIDE;
         virtual void SendBitstreamData(int videoStreamHandle, bool isKeyFrame, const unsigned char* bitstream, unsigned int bitstreamLen) OVERRIDE;
         virtual size_t GetSessionCount(int videoStreamHandle) OVERRIDE;
         virtual void CloseSessions(int videoStreamHandle) OVERRIDE;
         int CloseConnection(const websocketpp::connection_hdl& hdl);
         int Send(websocketpp::connection_hdl hdl, const unsigned char* bitstream, unsigned int bitstreamLen, bool useOverflowProtection, bool& didOverflow);
         void RequestKeyFrame(int videoStreamHandle);
         void RemoveSession(websocketpp::connection_hdl hdl);
         bool NeedsKeyframe(int videoStreamHandle);

      private:
         typedef std::map<websocketpp::connection_hdl, RawVideoFrameServerWsSession*, std::owner_less<websocketpp::connection_hdl> > sess_list;

         void on_open(server* s, websocketpp::connection_hdl hdl);
#if !defined(VIDEO_FRAME_SERVER_DISABLE_WSS_JSON_SERVER)
         websocketpp::lib::shared_ptr<boost::asio::ssl::context> on_tls_init(server* s, websocketpp::connection_hdl hdl);
#endif
         std::string get_password() const;

      private:
         CPCAPI2::Phone* mPhone;
         std::unique_ptr<server> mWebSockServer;
         sess_list mSessions;
         std::set<RawVideoFrameServerObserver*> mObs;
         std::thread* mServerThread;
         std::mutex mMapLock;
         CPCAPI2::VideoStreaming::VideoStreamingServerConfig mConfig;
         std::function<void()> mOnPrivilegedAccessCompleted;
      };

   }

}

#endif // CPCAPI2_RAW_VIDEO_FRAME_SERVER_WEBSOCKET_H

