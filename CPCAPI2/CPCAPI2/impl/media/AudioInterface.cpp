#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1)
#include "cpcapi2utils.h"
#include "AudioInterface.h"
#include "AudioImpl.h"
#include "AudioSyncHandler.h"
#include "MediaManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

#include <MediaStackImpl.hxx>
#include <CodecConfig.hxx>
#include <CodecFactoryImpl.hxx>

#include "impl/media/jsonrpc/AudioCompatibility.h"
#include "gen/Audio/datatypes/AudioAudioDeviceRole.h"
#include "gen/Audio/datatypes/AudioAudioDeviceType.h"
#include "gen/Audio/datatypes/AudioEvents.h"
#include "gen/Audio/datatypes/AudioAudioDeviceListUpdatedEvent.h"
#include "gen/Audio/datatypes/AudioAudioDeviceVolumeEvent.h"
#include "gen/Audio/datatypes/AudioAudioDeviceLevelChangeEvent.h"
#include "gen/Audio/datatypes/AudioPlaySoundCompleteEvent.h"
#include "gen/Audio/datatypes/AudioPlaySoundFailureEvent.h"
#include "gen/Audio/datatypes/AudioAudioCodecListUpdatedEvent.h"
#include "phone/EventQueue.h"


#ifdef CODECFACTORY_ENABLE_G729
#include <codecs/G729CodecImpl.hxx>
#endif

#ifdef CODECFACTORY_ENABLE_OPUS
#include <codecs/OpusCodecImpl.hxx>
#endif

#include <functional>

using namespace resip;
using namespace webrtc_recon;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

namespace CPCAPI2
{
namespace Media
{

AudioInterface::AudioInterface(MediaManagerInterface* mm)
   : mMM(mm),
     mImpl(new AudioImpl(mm->media_stack(), this, mm->phoneInterface()->getSdkModuleThread())),
     mNextPlaySoundHandle(5000)
{
   mMM->post(resip::resip_bind(&AudioImpl::init, mImpl));
   updatePerformanceProfile(mm->getDevicePerformanceProfile());
}

AudioInterface::~AudioInterface()
{
    delete mImpl;
}
   
void AudioInterface::Release()
{
   mImpl->stopAllSounds();
   delete this;
}

int AudioInterface::setHandler(AudioHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&AudioImpl::setHandler, mImpl, handler);
   if (handler == NULL)
   {
      mMM->execute(setHandlerCmd);
      mMM->process(-1);
   }
   else
   {
      mMM->post(setHandlerCmd);
   }
   
   return kSuccess;
}

int AudioInterface::queryDeviceList()
{
   DebugLog(<< "AudioInterface::queryDeviceList");

   mMM->post(resip::resip_bind(&AudioImpl::queryDeviceList, mImpl));
   return kSuccess;
}

int AudioInterface::setCaptureDevice(unsigned int deviceId, AudioDeviceRole role)
{
   InfoLog(<< "AudioInterface::setCaptureDevice " << deviceId << ", " << role );

   mMM->post(resip::resip_bind(&AudioImpl::setCaptureDevice, mImpl, deviceId, role));
   return kSuccess;
}

int AudioInterface::setRenderDevice(unsigned int deviceId, AudioDeviceRole role)
{
   InfoLog(<< "AudioInterface::setRenderDevice " << deviceId << ", " << role );

   mMM->post(resip::resip_bind(&AudioImpl::setRenderDevice, mImpl, deviceId, role));
   return kSuccess;
}

PlaySoundHandle AudioInterface::playSound(AudioDeviceRole role, const cpc::string& resourceUri, bool repeat)
{
#ifdef ANDROID
   ErrLog(<< "On Android, playSound must be called with an audio usage");
   return 0;
#else
   PlaySoundHandle h = mNextPlaySoundHandle++;

   DebugLog(<< "AudioInterface::playSound role: " << role << ", resourceUri: " << resourceUri << ", repeat: " << repeat << ", returned handle: " << h);

   mMM->post(resip::resip_bind(&AudioImpl::playSound, mImpl, h, role, -1, resourceUri, repeat));
   return h;
#endif
}

PlaySoundHandle AudioInterface::playSound(AudioDeviceRole role, int32_t audioUsage, const cpc::string& resourceUri, bool repeat)
{
   DebugLog(<< "AudioInterface::playSound role: " << role << ", audioUsage: " << audioUsage << ", resourceUri: " << resourceUri << ", repeat: " << repeat);

   PlaySoundHandle h = mNextPlaySoundHandle++;
   mMM->post(resip::resip_bind(&AudioImpl::playSound, mImpl, h, role, audioUsage, resourceUri, repeat));
   return h;
}

int AudioInterface::stopPlaySound(PlaySoundHandle sound)
{
   DebugLog(<< "AudioInterface::stopPlaySound sound:" << sound);

   mMM->post(resip::resip_bind(&AudioImpl::stopPlaySound, mImpl, sound));
   return kSuccess;
}

bool AudioInterface::isPlayingSound() const
{
   return mImpl->isPlayingSound();
}

int AudioInterface::queryCodecList()
{
   DebugLog(<< "AudioInterface::queryCodecList");

   mMM->post(resip::resip_bind(&AudioImpl::queryCodecList, mImpl));
   return kSuccess;
}

int AudioInterface::setCodecEnabled(unsigned int codecId, bool enabled)
{
   DebugLog(<< "AudioInterface::setCodecEnabled codecId: " << codecId << ", enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setCodecEnabled, mImpl, codecId, enabled));
   return kSuccess;
}

int AudioInterface::setCodecPriority(unsigned int codecId, unsigned int priority)
{
   DebugLog(<< "AudioInterface::setCodecPriority codecId: " << codecId << ", priority: " << priority);
   
   mMM->post(resip::resip_bind(&AudioImpl::setCodecPriority, mImpl, codecId, priority));
   return kSuccess;
}
   
int AudioInterface::setCodecPayloadType(unsigned int codecId, unsigned int payloadType)
{
   DebugLog(<< "AudioInterface::setCodecPayloadType codecId: " << codecId << ", payloadType: " << payloadType);

   mMM->post(resip::resip_bind(&AudioImpl::setCodecPayloadType, mImpl, codecId, payloadType));
   return kSuccess;
}
   
int AudioInterface::setTelephoneEventPayloadType(unsigned int payloadType)
{
   DebugLog(<< "AudioInterface::setTelephoneEventPayloadType payloadType: " << payloadType);

   mMM->post(resip::resip_bind(&AudioImpl::setTelephoneEventPayloadType, mImpl, payloadType));
   return kSuccess;
}

int AudioInterface::setMicMute(bool enabled)
{
   DebugLog(<< "AudioInterface::setMicMute enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setMicMute, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setSpeakerMute(bool enabled)
{
   DebugLog(<< "AudioInterface::setSpeakerMute enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setSpeakerMute, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setMicVolume(unsigned int level)
{
   DebugLog(<< "AudioInterface::setMicVolume level: " << level);

   mMM->post(resip::resip_bind(&AudioImpl::setMicVolume, mImpl, level));
   return kSuccess;
}

int AudioInterface::setMicSoftwareVolume( bool enabled, unsigned int level )
{
   DebugLog(<< "AudioInterface::setMicSoftwareVolume enabled: " << enabled << ", level: " << level);

   mMM->post(resip::resip_bind(&AudioImpl::setMicSoftwareVolume, mImpl, enabled, level));
   return kSuccess;
}

int AudioInterface::setSpeakerVolume(unsigned int level)
{
   DebugLog(<< "AudioInterface::setSpeakerVolume level: " << level);

   mMM->post(resip::resip_bind(&AudioImpl::setSpeakerVolume, mImpl, level));
   return kSuccess;
}

int AudioInterface::queryDeviceVolume()
{
   DebugLog(<< "AudioInterface::queryDeviceVolume");

   mMM->post(resip::resip_bind(&AudioImpl::queryDeviceVolume, mImpl));
   return kSuccess;
}

int AudioInterface::setEchoCancellationMode(AudioDeviceRole role, EchoCancellationMode mode)
{
   DebugLog(<< "AudioInterface::setEchoCancellationMode role: " << role << ", mode: " << mode);

   mMM->post(resip::resip_bind(&AudioImpl::setEchoCancellationMode, mImpl, role, mode));
   return kSuccess;
}

int AudioInterface::setNoiseSuppressionMode(AudioDeviceRole role, NoiseSuppressionMode mode)
{
   DebugLog(<< "AudioInterface::setNoiseSuppressionMode role: " << role << ", mode: " << mode);

   mMM->post(resip::resip_bind(&AudioImpl::setNoiseSuppressionMode, mImpl, role, mode));
   return kSuccess;
}

int AudioInterface::setVadMode(AudioDeviceRole role, VadMode mode)
{
   DebugLog(<< "AudioInterface::setVadMode role: " << role << ", mode: " << mode);

   mMM->post(resip::resip_bind(&AudioImpl::setVadMode, mImpl, role, mode));
   return kSuccess;
}

webrtc_recon::MediaStackImpl* AudioInterface::media_stack() const 
{ 
   return mImpl->media_stack(); 
}

int AudioInterface::setGainSettings(const GainSettings& settings)
{
   DebugLog(<< "AudioInterface::setGainSettings settings: " << settings);

   mMM->post(resip::resip_bind(&AudioImpl::setGainSettings, mImpl, settings));
   return kSuccess;
}

int AudioInterface::setAudioDscp(unsigned int mediaDscp)
{
   DebugLog(<< "AudioInterface::setAudioDscp mediaDscp: " << mediaDscp);

   mMM->post(resip::resip_bind(&AudioImpl::setAudioDscp, mImpl, mediaDscp));
   return kSuccess;
}

void AudioInterface::postCallback(resip::ReadCallbackBase* fp)
{
   mMM->postCallback(fp);
}
   
int AudioInterface::startMonitoringCaptureDeviceLevels(unsigned int deviceId)
{
   DebugLog(<< "AudioInterface::startMonitoringCaptureDeviceLevels deviceId: " << deviceId);

   mMM->post(resip::resip_bind(&AudioImpl::startMonitoringCaptureDeviceLevelsImpl, mImpl, deviceId));
	return kSuccess;
}

int AudioInterface::stopMonitoringCaptureDeviceLevels()
{
   DebugLog(<< "AudioInterface::stopMonitoringCaptureDeviceLevels");

   mMM->post(resip::resip_bind(&AudioImpl::stopMonitoringCaptureDeviceLevelsImpl, mImpl));
	return kSuccess;
}

int AudioInterface::startMonitoringRenderDeviceLevels(unsigned int deviceId, const cpc::string& resourceUri)
{
   DebugLog(<< "AudioInterface::startMonitoringRenderDeviceLevels deviceId: " << deviceId << ", resourceUri: " << resourceUri);

   mMM->post(resip::resip_bind(&AudioImpl::startMonitoringRenderDeviceLevelsImpl, mImpl, deviceId, resourceUri));
	return kSuccess;
}

int AudioInterface::stopMonitoringRenderDeviceLevels()
{
   DebugLog(<< "AudioInterface::stopMonitoringRenderDeviceLevels");

   mMM->post(resip::resip_bind(&AudioImpl::stopMonitoringRenderDeviceLevelsImpl, mImpl));
	return kSuccess;
}

int AudioInterface::setHardwareEchoCancellationEnabled(const bool enabled)
{
   DebugLog(<< "AudioInterface::setHardwareEchoCancellationEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setHardwareEchoCancellationEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setHardwareAutomaticGainControlEnabled(const bool enabled)
{
   DebugLog(<< "AudioInterface::setHardwareAutomaticGainControlEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setHardwareAutomaticGainControlEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setHardwareNoiseSuppressionEnabled(const bool enabled)
{
   DebugLog(<< "AudioInterface::setHardwareNoiseSuppressionEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setHardwareNoiseSuppressionEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setLowLatencyPlayoutEnabled(const bool enabled)
{
   DebugLog(<< "AudioInterface::setLowLatencyPlayoutEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setLowLatencyPlayoutEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setLowLatencyAudioTrackEnabled(const bool enabled)
{
   DebugLog(<< "AudioInterface::setLowLatencyAudioTrackEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setLowLatencyAudioTrackEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setAudioSource(int audioSource)
{
   DebugLog(<< "AudioInterface::setAudioSource audioSource: " << audioSource);

#ifdef ANDROID
   mMM->post(resip::resip_bind(&AudioImpl::setAudioSource, mImpl, audioSource));
   return kSuccess;
#else
   return kError;
#endif
}

void AudioInterface::updatePerformanceProfile(DevicePerformanceProfile perfProfile)
{
   DebugLog(<< "AudioInterface::updatePerformanceProfile perfProfile: " << perfProfile);

   mMM->post(resip::resip_bind(&AudioImpl::updatePerformanceProfile, mImpl, perfProfile));
}

void AudioInterface::onSystemAudioServiceError(int errorLevel)
{
   mMM->post(resip::resip_bind(&AudioImpl::onSystemAudioServiceError, mImpl, errorLevel));
}

int AudioInterface::connectAudioStreams(int recvStreamId, int sendStreamId)
{
   mMM->post(resip::resip_bind(&AudioImpl::connectAudioStreams, mImpl, recvStreamId, sendStreamId));
   return kSuccess;
}

int AudioInterface::disconnectAudioStreams(int recvStreamId, int sendStreamId)
{
   mMM->post(resip::resip_bind(&AudioImpl::disconnectAudioStreams, mImpl, recvStreamId, sendStreamId));
   return kSuccess;
}

int AudioInterface::setCodecConfig(const G729Config& config)
{
   DebugLog(<< "AudioInterface::setCodecConfig config: " << config);

   mMM->post(resip::resip_bind(&AudioInterface::setCodecConfigImpl, this, config));
   return kSuccess;
}

void AudioInterface::setCodecConfigImpl(const G729Config& config)
{
#ifdef CODECFACTORY_ENABLE_G729
   if (media_stack()->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(media_stack()->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      CodecFactoryImpl::Codecs::iterator itAudioCodecs = audioCodecs.begin();
      for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         std::shared_ptr<G729CodecImpl> g729_codec = std::dynamic_pointer_cast<G729CodecImpl>(*itAudioCodecs);
         if (g729_codec)
         {
            if (config.useAnnexB)
               g729_codec->enableAnnexB();
            else
               g729_codec->disableAnnexB();

            g729_codec->enableRFC7261( config.useRFC7261 );
            break;
         }
      }
   }
#endif
}

int AudioInterface::setCodecConfig(const OpusConfig& config)
{
   mMM->post(resip::resip_bind(&AudioInterface::setOpusCodecConfigImpl, this, config));
   return kSuccess;
}

void AudioInterface::setOpusCodecConfigImpl(const OpusConfig& config)
{
#ifdef CODECFACTORY_ENABLE_OPUS
   if (media_stack()->isInitialized())
   {
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(media_stack()->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      CodecFactoryImpl::Codecs::iterator itAudioCodecs = audioCodecs.begin();
      for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         std::shared_ptr<OpusCodecImpl> opus_codec = std::dynamic_pointer_cast<OpusCodecImpl>(*itAudioCodecs);
         if (opus_codec)
         {
            opus_codec->setComplexity(config.complexity);
            if (config.enableDtx)
            {
               opus_codec->enableDtx();
            }
            else
            {
               opus_codec->disableDtx();
            }
            opus_codec->setApplication(config.application);
            opus_codec->setBitrate(config.bitrate);
            break;
         }
      }
   }
#endif
}

   
int AudioInterface::activatePlayAndRecordMode(bool activate)
{
   DebugLog(<< "AudioInterface::activatePlayAndRecordMode activate: " << activate);

   mMM->post(resip::resip_bind(&AudioImpl::activatePlayAndRecordMode, mImpl, activate));
   return kSuccess;
}


int AudioInterface::setAudioSessionActivated(bool activated)
{
   DebugLog(<< "AudioInterface::setAudioSessionActivated activated: " << activated);

   mMM->execute(resip::resip_bind(&AudioImpl::setAudioSessionActivated, mImpl, activated));
   return kSuccess;
}

int AudioInterface::setAudioInterruptionsHandlingEnabled(bool enabled)
{
   DebugLog(<< "AudioInterface::setAudioInterruptionsHandlingEnabled enabled: " << enabled);
   
   mMM->post(resip::resip_bind(&AudioImpl::setAudioInterruptionsHandlingEnabled, mImpl, enabled));
   return kSuccess;
}

int AudioInterface::setAudioDeviceFile(const cpc::string& inputAudiofileNameUtf8, const cpc::string& outputAudiofileNameUtf8)
{
   DebugLog(<< "AudioInterface::setAudioDeviceFile inputAudiofileNameUtf8: " << inputAudiofileNameUtf8 << ", outputAudiofileNameUtf8: " << outputAudiofileNameUtf8);

   mMM->post(resip::resip_bind(&AudioImpl::setAudioDeviceFile, mImpl, inputAudiofileNameUtf8, outputAudiofileNameUtf8));
   return kSuccess;
}

int AudioInterface::setMicAGCEnabled(bool enabled)
{
   DebugLog(<< "AudioInterface::setMicAGCEnabled enabled: " << enabled);

   mMM->post(resip::resip_bind(&AudioImpl::setMicAGCEnabled, mImpl, enabled));
   return kSuccess;
}
   
MediaManagerInterface* AudioInterface::mediaManagerInterface()
{
  return mMM;
}
   
int AudioInterface::setUseQosFastlane(bool useQosFastlane, bool useWithDscp)
{
   DebugLog(<< "AudioInterface::setUseQosFastlane useQosFastlane: " << useQosFastlane);

   mMM->post(resip::resip_bind(&AudioImpl::setUseQosFastlane, mImpl, useQosFastlane, useWithDscp));
   return kSuccess;
}

void AudioInterface::addSdkObserver(AudioHandler* observer)
{
   mSdkObservers.insert(observer);
}

void AudioInterface::removeSdkObserver(AudioHandler* observer)
{
   mSdkObservers.erase(observer);
}

void AudioInterface::initPlaySoundDevicePool()
{
   mImpl->initPlaySoundDevicePool();
}

void AudioInterface::shutdownPlaySoundDevicePool()
{
   mImpl->shutdownPlaySoundDevicePool();
}

// DRL FIXIT: This is duplicated in AudioCompatibility.xpp but I added it here to get around a Linx only linker problem.
static jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole convert(::CPCAPI2::Media::AudioDeviceRole role)
{
   switch (role)
   {
   case ::CPCAPI2::Media::AudioDeviceRole_None:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::None;
   case ::CPCAPI2::Media::AudioDeviceRole_Headset:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::Headset;
   case ::CPCAPI2::Media::AudioDeviceRole_SpeakerPhone:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::SpeakerPhone;
   case ::CPCAPI2::Media::AudioDeviceRole_Ringing:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::Ringing;
   case ::CPCAPI2::Media::AudioDeviceRole_Bluetooth:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::Bluetooth;
   default:
      return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceRole::None;
   }
}

// DRL FIXIT: This is duplicated in AudioCompatibility.xpp but I added it here to get around a Linx only linker problem.
static jsonrpc::CPCAPI2::Audio::AudioAudioDeviceType convert(::CPCAPI2::Media::AudioDeviceType type)
{
   switch (type)
   {
      case ::CPCAPI2::Media::MediaDeviceType_Capture:
         return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceType::Capture;
      case ::CPCAPI2::Media::MediaDeviceType_Render:
         return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceType::Render;
      default:
         return jsonrpc::CPCAPI2::Audio::AudioAudioDeviceType::Capture;
   }
}

// DRL FIXIT: This is duplicated in AudioCompatibility.xpp but I added it here to get around a Linx only linker problem.
static jsonrpc::CPCAPI2::Audio::AudioAudioDeviceInfo convert(const ::CPCAPI2::Media::AudioDeviceInfo& deviceInfo)
{
   jsonrpc::CPCAPI2::Audio::AudioAudioDeviceInfo result;

   result.friendlyName = deviceInfo.friendlyName;
   result.hid = deviceInfo.hid;
   result.id = deviceInfo.id;
   result.role = convert(deviceInfo.role);
   result.deviceType = convert(deviceInfo.deviceType);
//   result.inadvisable = deviceInfo.inadvisable;
//   result.defaultSystemDevice = deviceInfo.defaultSystemDevice;
//   result.defaultSystemCommDevice = deviceInfo.defaultSystemCommDevice;

   return result;
}

// DRL FIXIT: This is duplicated in AudioCompatibility.xpp but I added it here to get around a Linx only linker problem.
static jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo convert(const ::CPCAPI2::Media::AudioCodecInfo& codecInfo)
{
   jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo result;

   result.codecName = codecInfo.codecName;
   result.id = codecInfo.id;
   result.enabled = codecInfo.enabled;
   result.samplingRate = codecInfo.samplingRate;
   result.minBandwidth = codecInfo.minBandwidth;
   result.maxBandwidth = codecInfo.maxBandwidth;
   result.priority = codecInfo.priority;
   result.payloadType = codecInfo.payloadType;

   return result;
}

static cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioDeviceInfo> convert(cpc::vector<AudioDeviceInfo> input)
{
   cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioDeviceInfo> result;

   for (cpc::vector<AudioDeviceInfo>::const_iterator it = input.begin(); it != input.end(); ++it)
   {
      result.push_back(convert(*it));
   }

   return result;
}

static cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo> convert(cpc::vector<AudioCodecInfo> input)
{
   cpc::vector<jsonrpc::CPCAPI2::Audio::AudioAudioCodecInfo> result;

   for (cpc::vector<AudioCodecInfo>::const_iterator it = input.begin(); it != input.end(); ++it)
   {
      result.push_back(convert(*it));
   }

   return result;
}

void AudioInterface::fireAudioDeviceListUpdated(AudioHandler* appHandler, const AudioDeviceListUpdatedEvent& args)
{
   DebugLog(<< "fireAudioDeviceListUpdated args " << args);

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioDeviceListUpdated, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioDeviceListUpdated(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioAudioDeviceListUpdatedEvent jrpcEvt;
   jrpcEvt.deviceInfo = convert(args.deviceInfo);

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnAudioDeviceListUpdated), 
                                                  std::move(jrpcEvt.marshal())));
}

void AudioInterface::fireAudioDeviceLevelChange(AudioHandler* appHandler, const AudioDeviceLevelChangeEvent& args)
{
   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioDeviceLevelChange, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioDeviceLevelChange(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioAudioDeviceLevelChangeEvent jrpcEvt;
   jrpcEvt.inputDeviceLevel = args.inputDeviceLevel;
   jrpcEvt.outputDeviceLevel = args.outputDeviceLevel;

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnAudioDeviceLevelChange), 
      std::move(jrpcEvt.marshal())));
}

void AudioInterface::fireAudioStreamStarted(AudioHandler* appHandler, const AudioStreamStartedEvent& args)
{
   DebugLog(<< "fireAudioStreamStarted");

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioStreamStarted, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioStreamStarted(args);
      }
   }
}

void AudioInterface::fireAudioStreamStopped(AudioHandler* appHandler, const AudioStreamStoppedEvent& args)
{
   DebugLog(<< "fireAudioStreamStopped");

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioStreamStopped, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioStreamStopped(args);
      }
   }
}

void AudioInterface::fireAudioDeviceVolume(AudioHandler* appHandler, const AudioDeviceVolumeEvent& args)
{
   DebugLog(<< "fireAudioDeviceVolume");

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioDeviceVolume, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioDeviceVolume(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioAudioDeviceVolumeEvent jrpcEvt;
   jrpcEvt.micMuted = args.micMuted;
   jrpcEvt.speakerMuted = args.speakerMuted;
   jrpcEvt.micVolumeLevel = args.micVolumeLevel;
   jrpcEvt.speakerVolumeLevel = args.speakerVolumeLevel;

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnAudioDeviceVolume), 
      std::move(jrpcEvt.marshal())));
}

void AudioInterface::firePlaySoundComplete(AudioHandler* appHandler, const PlaySoundHandle& args)
{
   DebugLog(<< "firePlaySoundComplete");

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onPlaySoundComplete, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onPlaySoundComplete(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioPlaySoundCompleteEvent jrpcEvt;
   jrpcEvt.soundClip = args;

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnPlaySoundComplete), 
      std::move(jrpcEvt.marshal())));
}

void AudioInterface::firePlaySoundFailure(AudioHandler* appHandler, const PlaySoundHandle& args)
{
   DebugLog(<< "firePlaySoundFailure");

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onPlaySoundFailure, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onPlaySoundFailure(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioPlaySoundFailureEvent jrpcEvt;
   jrpcEvt.soundClip = args;

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnPlaySoundFailure), 
      std::move(jrpcEvt.marshal())));
}

void AudioInterface::fireAudioCodecListUpdated(AudioHandler* appHandler, const AudioCodecListUpdatedEvent& args)
{
   DebugLog(<< "fireAudioCodecListUpdated args " << args);

   if (appHandler != NULL)
   {
      ReadCallbackBase* fp = makeFpCommand1(AudioHandler::onAudioCodecListUpdated, appHandler, args);
      mMM->postCallback(fp);
   }

   std::set<AudioHandler*>::const_iterator itObs = mSdkObservers.begin();
   for (; itObs != mSdkObservers.end(); ++itObs)
   {
      AudioHandler* obs = *itObs;
      if (dynamic_cast<AudioSyncHandler*>(obs) != NULL)
      {
         obs->onAudioCodecListUpdated(args);
      }
   }

   jsonrpc::CPCAPI2::Audio::AudioAudioCodecListUpdatedEvent jrpcEvt;
   jrpcEvt.codecInfo = convert(args.codecInfo);

   mMM->phoneInterface()->getEventQueue()->addEvent(SdkEvent(to_string(jsonrpc::CPCAPI2::Audio::AudioEvents::AudioDotOnAudioCodecListUpdated), 
                                                  std::move(jrpcEvt.marshal())));
}

std::ostream& operator<<(std::ostream& os, const AudioDeviceListUpdatedEvent& type)
{
   os << "deviceInfo list: ";
   for (cpc::vector<AudioDeviceInfo>::const_iterator it = type.deviceInfo.begin(); it != type.deviceInfo.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   
   return os;
}

std::ostream& operator<<(std::ostream& os, const AudioCodecListUpdatedEvent& type)
{
   os << "codecInfo list: ";
   for (cpc::vector<AudioCodecInfo>::const_iterator it = type.codecInfo.begin(); it != type.codecInfo.end(); ++it)
   {
      os << "[" << *it << "]";
   }
   
   return os;
}

std::ostream& operator<<(std::ostream& os, const AudioDeviceInfo& type)
{
   os << "friendlyName: " << type.friendlyName
      << " hid: " << type.hid
      << " id: " << type.id
      << " role: " << type.role
      << " deviceType: " << (type.deviceType == MediaDeviceType_Capture ? "capture" : "render")
      << " inadvisable: " << type.inadvisable
      << " defaultSystemDevice: " << type.defaultSystemDevice
      << " defaultSystemCommDevice: " << type.defaultSystemCommDevice;

   return os;
}


std::ostream& operator<<(std::ostream& os, const G729Config& type)
{
   os << "useAnnexB: " << type.useAnnexB
      << " useRFC7261: " << type.useRFC7261;

   return os;
}

std::ostream& operator<<(std::ostream& os, const GainSettings& settings)
{
   os << "rxConfig.mode: " << settings.rxConfig.mode
      << " rxConfig.targetLeveldB: " << settings.rxConfig.targetLeveldB
      << " rxConfig.compressionGaindB: " << settings.rxConfig.compressionGaindB
      << " txConfig.mode: " << settings.txConfig.mode
      << " txConfig.targetLeveldB: " << settings.txConfig.targetLeveldB
      << " txConfig.compressionGaindB: " << settings.txConfig.compressionGaindB
      << " spkConfig.enabled: " << settings.spkConfig.enabled
      << " spkConfig.scale: " << settings.spkConfig.scale;
   
   return os;
}

std::ostream& operator<<(std::ostream& os, const AudioCodecInfo& codecInfo)
{
   os << "codecName: " << codecInfo.codecName
      << " id: " << codecInfo.id
      << " enabled: " << (codecInfo.enabled ? "true" : "false")
      << " samplingRate: " << codecInfo.samplingRate
      << " minBandwidth: " << codecInfo.minBandwidth
      << " maxBandwidth: " << codecInfo.maxBandwidth
      << " priority: " << codecInfo.priority
      << " payloadType: " << codecInfo.payloadType;

   return os;
}


}
}
#endif
