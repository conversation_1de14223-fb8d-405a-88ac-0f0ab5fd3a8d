#include "ScreenShareImpl.h"

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

#if defined(_WIN32) || (defined( __APPLE__ ) && TARGET_OS_IPHONE == 0) || (defined(__linux__) && !defined(ANDROID))

#include <webrtc/modules/desktop_capture/desktop_frame.h>
#include <webrtc/modules/desktop_capture/cropped_desktop_frame.h>
#include <webrtc/video_engine/vie_defines.h>
#include <webrtc/common_video/libyuv/include/webrtc_libyuv.h>
#include <libyuv.h>
#include <math.h>
#ifdef WIN32
#include "ScreenShareBorderWindow_Win.h"
#include <webrtc/modules/desktop_capture/win/screen_capture_utils.h>
#endif 

#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

#define FRAMERATE_LOG_INTERVAL 15 // seconds

using namespace webrtc;

// used for unit testing screen capture dimensions
void (*gCustomCapturerFactory)(std::unique_ptr<DesktopCapturer>& outCapturer) = NULL;

namespace CPCAPI2
{
namespace Media
{

ScreenShare* ScreenShare::Create()
{
   return new ScreenShareImpl();
}

ScreenShareImpl::ScreenShareImpl()
   : mCaptureEvt(webrtc::EventTimerWrapper::Create()),
     mExternalCapture(NULL),
     mCapturing(false),
     mObserver(NULL),
     mObjHolder(NULL),
     mScreenshareDevice(0),
     mScaleBuffer(NULL),
     mScaleBufferSize(0),
     mFrameCaptureInterval(1000.0/DEFAULT_SCREENSHARE_MAX_FRAMERATE)
{
}

ScreenShareImpl::~ScreenShareImpl()
{
   if (mObjHolder)
   {
      delete mObjHolder;
   }
   if (mScaleBuffer != NULL)
   {
      delete[] mScaleBuffer;
   }
}

int ScreenShareImpl::Start(unsigned int deviceId)
{
   mScreenshareDevice = deviceId;
   StartImpl();
   return 0;
}

#if defined(WIN32) || defined(__linux__)
void ScreenShareImpl::StartImpl()
{
   Stop();
   mCapturing = true;
   mCaptureThread.reset(new CPCAPI2::thread(std::bind(&ScreenShareImpl::CaptureThread, this)));
   mBorderThread.reset(new CPCAPI2::thread(std::bind(&ScreenShareImpl::BorderThread, this)));
}
#endif

void ScreenShareImpl::SetExternalCaptureInterface(webrtc::ViEExternalCapture* externalCapture)
{
   mExternalCapture = externalCapture;
}

void ScreenShareImpl::SetObserver(ScreenShareObserver* observer)
{
   assert(!mCapturing);
   mObserver = observer;
}

void ScreenShareImpl::SetMaxCaptureFrameRate(unsigned int fps)
{
   if (fps > 0)
   {
      mFrameCaptureInterval = (1000.0 / fps);
      DebugLog(<< "Screen capture max frame rate set to: " << fps);
#ifdef WIN32
      if (mCapturing)
      {
         // Restart if frame rate is changed dynamically (such as star code handling)
         StartImpl();
      }
#endif
   }
}

int ScreenShareImpl::Stop()
{
   mCapturing = false;
   StopImpl();
   return 0;
}

#if defined(WIN32) || defined(__linux__)
void ScreenShareImpl::StopImpl()
{
   if (mCaptureThread)
   {
      mCaptureThread->join();
   }
   if (mBorderThread)
   {
      mBorderThread->join();
   }
}
#endif

void ScreenShareImpl::ResetScreenCap()
{
   if (mScreenCap.get() != NULL)
   {
      mScreenCap.reset();
   }
}

void ScreenShareImpl::OnCaptureResult(webrtc::DesktopCapturer::Result result,
   std::unique_ptr<webrtc::DesktopFrame> frame)
{

   if (!frame || result != webrtc::DesktopCapturer::Result::SUCCESS)
   {
      if (result == webrtc::DesktopCapturer::Result::ERROR_PERMANENT)
      {
         // stop capturing since we can't recover
         mCapturing = false;
         if (mObserver)
         {
            mObserver->onScreenShareError(this);
         }
      }
      return;
   }
   
   // Log frame rate periodically
   static int frameCounter;
   frameCounter++;
   webrtc::TickTime now = webrtc::TickTime::Now();
   int64_t diff = (now - mLastFpsLogTime).Milliseconds();
   if (diff >= (FRAMERATE_LOG_INTERVAL * 1000))
   {
      mLastFpsLogTime = webrtc::TickTime::Now();
      DebugLog(<< "Screen capture frame rate " << (int)(frameCounter * 1000.0f/diff));
      frameCounter = 0;
   }
   
   webrtc::DesktopSize previousFrameSize(mCapturedFrameI420.width(), mCapturedFrameI420.height());
   webrtc::DesktopSize outputSize = frame->size();

   if (!previousFrameSize.equals(frame->size()))
   {
      mOutputFrame.reset();
      
      // There are limits to the sizes of frames we can handle. The WebRTC code imposes limits
      // on the width and height, and the Wels H264 encoder imposes an additional limit on the
      // resulting number of pixels. I expect that each encoder will have its own limits.
      //
      // The code below is intended to calculate a frame size that maintains the original aspect
      // ratio but is reduced as needed to fit within these restrictions. There's also a check
      // to keep the final dimension "even" as it looks like odd boundaries aren't supported by
      // I420.
      //
      // When we exceed the restrictions of WebRTC or a codec we seem to not get any frames at
      // all. I've seen this logging from the Wels encoder:
      // [OpenH264] this = 0x0x600000cc1920, Error:ParamValidationExt(), width > 0, height > 0, width * height <= 9437184, invalid 4096 x 3072 in dependency layer settings!

      // this code borrowed from videocommon.cc ComputeScaleMaxPixels(), and also incorporated alignment code from OpenH264
      int new_frame_width = frame->size().width();
      int new_frame_height = frame->size().height();

       // OpenH264 wants width and height 16 bit aligned and will round up to get it, and I420 frames want them both even. It is likely that 
       // other protocols will want some form of alignment too, and it will most likely be satisfied if we use the 16 bit alignment. Unlike 
       // the OpenH264 logic we'll round up or down in order to get an aspect ratio that is closer to the original, and this will still satisfy
       // the alignment requirement.
#define WELS_ALIGN(x, n) (((x)+(int)(n/2))&~((n)-1))
#define WELS_ALIGN_DOWN(x, n) ((x)&~((n)-1))
#define MB_WIDTH_LUMA  (16)
#define MB_HEIGHT_LUMA  (16)
      new_frame_width = WELS_ALIGN(new_frame_width, MB_WIDTH_LUMA);
      new_frame_height = WELS_ALIGN(new_frame_height, MB_HEIGHT_LUMA);

      // Limit width.
      if (new_frame_width > kViEMaxCodecWidth) {
        // rounding up seems to give better results
        new_frame_height = ((new_frame_height * kViEMaxCodecWidth) + new_frame_width - 1) / new_frame_width;
        new_frame_width = kViEMaxCodecWidth;
      }
      // Limit height.
      if (new_frame_height > kViEMaxCodecHeight) {
        // rounding up seems to give better results
        new_frame_width = ((new_frame_width * kViEMaxCodecHeight) + new_frame_height - 1) / new_frame_height;
        new_frame_height = kViEMaxCodecHeight;
      }

      const int max_pixels = 9437184;  // Wels H264 encoder maximum
      
      // Limit number of pixels.
      if (new_frame_width * new_frame_height > max_pixels) {
        // Compute new width such that width * height is less than maximum but
        // maintains original captured frame aspect ratio.
        new_frame_width = static_cast<int>(sqrtf(static_cast<float>(max_pixels) * new_frame_width / new_frame_height));
        new_frame_height = max_pixels / new_frame_width;
      }

      // If we've made any changes above we'll need to make sure we're still aligning as OpenH264 would do, 
      // but now we're rounding down to make sure we don't exceed any of the maximums.
      new_frame_width = WELS_ALIGN_DOWN(new_frame_width, MB_WIDTH_LUMA);
      new_frame_height = WELS_ALIGN_DOWN(new_frame_height, MB_HEIGHT_LUMA);

      outputSize.set(new_frame_width, new_frame_height);
      if (outputSize.is_empty())
      {
         outputSize.set(2, 2);
      }
   }

   const uint8_t* outputBuffer = NULL;

   const int32_t frame_width = frame->size().width();
   const int32_t frame_height = frame->size().height();

   if (frame_width & 1 || frame_height & 1)
   {
      // Make sure dimensions are even to prevent blurring
      frame = webrtc::CreateCroppedDesktopFrame(std::move(frame), webrtc::DesktopRect::MakeWH(frame_width & ~1, frame_height & ~1));
   }

   if (!frame->size().equals(outputSize))
   {
//      InfoLog(<<"Scaling from from " << frame->size().width() << "x" << frame->size().height() << " to " << outputSize.width() << "x" << outputSize.height());

      // Scale to target format if the frame does not match the output size
      if (!mOutputFrame)
         mOutputFrame.reset(new webrtc::BasicDesktopFrame(outputSize));

      const int modified_frame_size = outputSize.width() * outputSize.height() * webrtc::DesktopFrame::kBytesPerPixel;
      if (mScaleBufferSize != modified_frame_size)
      {
         if (mScaleBuffer != NULL)
         {
            delete[] mScaleBuffer;
         }
         mScaleBuffer = new uint8_t[modified_frame_size];
         mScaleBufferSize = modified_frame_size;
      }

      libyuv::ARGBScale(reinterpret_cast<const uint8_t*>(frame->data()),
         frame->stride(), frame->size().width(),
         frame->size().height(),
         mScaleBuffer,
         mOutputFrame->stride(), outputSize.width(), outputSize.height(),
         libyuv::kFilterBox);

      outputBuffer = mScaleBuffer;
   }
   else if (frame->stride() != frame->size().width() * webrtc::DesktopFrame::kBytesPerPixel)
   {
      // If frame was cropped from a larger frame, create a packet top-to-bottom copy
      if (!mOutputFrame)
      {
         mOutputFrame.reset(new webrtc::BasicDesktopFrame(outputSize));
      }
      mOutputFrame->CopyPixelsFrom(*frame, webrtc::DesktopVector(), webrtc::DesktopRect::MakeSize(frame->size()));
      outputBuffer = mOutputFrame->data();
   }
   else
   {
      outputBuffer = frame->data();
   }

   assert(outputSize.width() > 0);
   int stride_y = 0;
   int stride_uv = 0;
   webrtc::Calc16ByteAlignedStride(outputSize.width(), &stride_y, &stride_uv);
   mCapturedFrameI420.CreateEmptyFrame(outputSize.width(), outputSize.height(),
      stride_y, stride_uv, stride_uv);
   
   if (webrtc::ConvertToI420(webrtc::kARGB,
      outputBuffer,
      0, 0,
      outputSize.width(), outputSize.height(),
      0, kVideoRotation_0,
      &mCapturedFrameI420) != 0)
   {
      return;
   }

   if (mExternalCapture)
   {
      mExternalCapture->IncomingFrame(mCapturedFrameI420);
   }
}

void ScreenShareImpl::InitializeCapture()
{
   DesktopCaptureOptions options = DesktopCaptureOptions::CreateDefault();
   options.set_disable_effects(false);
#ifdef WIN32
   options.set_allow_directx_capturer(true);
   options.set_allow_cropping_window_capturer(true); // support PowerPoint full screen
#else
   options.set_detect_updated_region(false);
#endif
#if defined(WEBRTC_MAC) && !defined(WEBRTC_IOS)
   options.set_allow_iosurface(true);
#endif

   std::unique_ptr<webrtc::DesktopCapturer> capturer;
   if (gCustomCapturerFactory)
   {
      gCustomCapturerFactory(capturer);
   }
#ifndef __linux__ // on Linux we only support the above custom capture
   else
   {
      capturer = webrtc::DesktopCapturer::CreateWindowCapturer(options);
      if (capturer->SelectSource(mScreenshareDevice))
      {
         // window capture
         capturer->FocusOnSelectedSource();
      }
      else
      {
         // monitor capture
         capturer = webrtc::DesktopCapturer::CreateScreenCapturer(options);
         capturer->SelectSource(mScreenshareDevice);
      }
   }
#endif
   
   if (capturer.get())
   {
      mLastFpsLogTime = webrtc::TickTime::Now();
      
      mScreenCap.reset(new webrtc::DesktopAndCursorComposer(std::move(capturer), options));

      mScreenCap->Start(this);
   }
   else
   {
      ResetScreenCap();
   }
}


#if defined(WIN32) || defined(__linux__)
void ScreenShareImpl::CaptureThread()
{
   InitializeCapture();

   mCaptureEvt->StartTimer(true, (unsigned long)getFrameCaptureInterval());

   while (mCapturing)
   {
      if (mCapturedFrameI420.IsZeroSize() || mCapturedFrameI420.video_frame_buffer_const()->HasOneRef())
      {
         PerformCapture();
      }
      mCaptureEvt->Wait(2000);
   }

   mCaptureEvt->StopTimer();
   ResetScreenCap();
}

void ScreenShareImpl::BorderThread()
{
#ifdef WIN32
   std::vector<std::unique_ptr<ScreenShareBorderWindow>> borderWindows;
   std::wstring screenDeviceKey;
   if (webrtc::IsScreenValid(mScreenshareDevice, &screenDeviceKey))
   {
      // When capturing full screen, add 4 thin border windows to prevent messing with the screen content
      borderWindows.push_back(std::unique_ptr<ScreenShareBorderWindow>(new ScreenShareBorderWindow(ScreenShareBorderWindow::WindowLocation::Left)));
      borderWindows.push_back(std::unique_ptr<ScreenShareBorderWindow>(new ScreenShareBorderWindow(ScreenShareBorderWindow::WindowLocation::Top)));
      borderWindows.push_back(std::unique_ptr<ScreenShareBorderWindow>(new ScreenShareBorderWindow(ScreenShareBorderWindow::WindowLocation::Bottom)));
      borderWindows.push_back(std::unique_ptr<ScreenShareBorderWindow>(new ScreenShareBorderWindow(ScreenShareBorderWindow::WindowLocation::Right)));
      
   }
   else
   {
      // When capturing a window, show only a border window that follows the target source
      borderWindows.push_back(std::unique_ptr<ScreenShareBorderWindow>(new ScreenShareBorderWindow(ScreenShareBorderWindow::WindowLocation::FullWindow)));
   }

   // Delay showing the border to ensure capture is started first
   std::this_thread::sleep_for(std::chrono::milliseconds(500));

   for (auto const& win : borderWindows)
   {
      win->showWindow(mScreenshareDevice);
   }

   while (mCapturing)
   {
      // Service border window
      MSG msg;
      if (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE))
      {
         TranslateMessage(&msg);
         DispatchMessage(&msg);
      }
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
   }

   for (int i = 0; i < borderWindows.size(); i++)
   {
      if (borderWindows[i] && borderWindows[i]->handle() != 0)
      {
         borderWindows[i]->Destroy();
         borderWindows[i].reset();
      }
   }
#endif
}

void ScreenShareImpl::PerformCapture()
{
   
   if (mScreenCap.get() != NULL)
   {
      mScreenCap->CaptureFrame();
   }
}
#endif

int ScreenShareImpl::GetMonitors(std::vector<ScreenShareMonitorListItem>* monitorList)
{
   DesktopCaptureOptions options = DesktopCaptureOptions::CreateDefault();
#ifdef WIN32
   options.set_allow_directx_capturer(true);
#endif

   std::unique_ptr<webrtc::DesktopCapturer> desktopCapturer = webrtc::DesktopCapturer::CreateScreenCapturer(options);
   webrtc::DesktopCapturer::SourceList captureSources;
   if (desktopCapturer->GetSourceList(&captureSources))
   {
      for (const webrtc::DesktopCapturer::Source& cs : captureSources)
      {
         ScreenShareMonitorListItem li;
         li.id = cs.id;
         li.title = cs.title;
         monitorList->push_back(li);
      }
   }
   return 0;
}

int ScreenShareImpl::GetWindows(std::vector<ScreenShareWindowListItem>* windowList)
{
   DesktopCaptureOptions options = DesktopCaptureOptions::CreateDefault();
#ifdef WIN32
   options.set_allow_directx_capturer(true);
#endif

   std::unique_ptr<webrtc::DesktopCapturer> desktopCapturer = webrtc::DesktopCapturer::CreateWindowCapturer(options);
   webrtc::DesktopCapturer::SourceList captureSources;
   if (desktopCapturer->GetSourceList(&captureSources))
   {
      for (const webrtc::DesktopCapturer::Source& cs : captureSources)
      {
#ifdef WIN32
         // Ignore our own screenshare border windows
         auto it = std::find(std::begin(ScreenShareBorderWindow::WindowTitles), std::end(ScreenShareBorderWindow::WindowTitles), std::wstring(cs.title.begin(), cs.title.end()));
         if (it != std::end(ScreenShareBorderWindow::WindowTitles))
            continue;
#endif
         ScreenShareWindowListItem li;
         li.id = cs.id;
         li.title = cs.title;
         windowList->push_back(li);
      }
   }
   return 0;
}
}

}
#else
#include "ScreenShareImpl_dummy.cpp"
#endif // defined(_WIN32) || (defined( __APPLE__ ) && TARGET_OS_IPHONE == 0)
