#pragma once

#if !defined(CPCAPI2_AUDIO_INTERFACE_H)
#define CPCAPI2_AUDIO_INTERFACE_H

#include "cpcapi2defs.h"
#include "media/audio/Audio.h"
#include "media/audio/AndroidAudio.h"
#include "media/audio/IOSAudio.h"
#include "../experimental/audio_ext/AudioExt.h"
#include "media/MediaManager.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "../phone/PhoneModule.h"

#include <rutil/Fifo.hxx>
#include <resip/dum/DumCommand.hxx>

#include <set>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class MediaManagerInterface;
class AudioImpl;

class AudioInterface : public Audio, public AndroidAudio, public IOSAudio, public AudioExt,
   public PhoneModule
{
public:
   AudioInterface(MediaManagerInterface* mm);
   virtual ~AudioInterface();

   virtual void Release() OVERRIDE;

   virtual int setHandler(AudioHandler* handler) OVERRIDE;

   virtual int queryDeviceList() OVERRIDE;

   virtual int setCaptureDevice(unsigned int deviceId, AudioDeviceRole role) OVERRIDE;
   virtual int setRenderDevice(unsigned int deviceId, AudioDeviceRole role) OVERRIDE;

   virtual PlaySoundHandle playSound(AudioDeviceRole role, const cpc::string& resourceUri, bool repeat = false) OVERRIDE;
   virtual PlaySoundHandle playSound(AudioDeviceRole role, int32_t audioUsage, const cpc::string& resourceUri, bool repeat = false) OVERRIDE;
   virtual int stopPlaySound(PlaySoundHandle sound) OVERRIDE;
   virtual bool isPlayingSound() const;

   virtual int queryCodecList() OVERRIDE;
   virtual int setCodecEnabled(unsigned int codecId, bool enabled) OVERRIDE;
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) OVERRIDE;
   virtual int setCodecPayloadType(unsigned int codecId, unsigned int payloadType) OVERRIDE;
   virtual int setTelephoneEventPayloadType(unsigned int payloadType) OVERRIDE;

   virtual int setMicMute(bool enabled) OVERRIDE;
   virtual int setSpeakerMute(bool enabled) OVERRIDE;
   virtual int setMicVolume(unsigned int level) OVERRIDE;
   virtual int setMicSoftwareVolume( bool enabled, unsigned int level ) OVERRIDE;
   virtual int setSpeakerVolume(unsigned int level) OVERRIDE;

   virtual int queryDeviceVolume() OVERRIDE;

   virtual int setEchoCancellationMode(AudioDeviceRole role, EchoCancellationMode mode) OVERRIDE;
   virtual int setNoiseSuppressionMode(AudioDeviceRole role, NoiseSuppressionMode mode) OVERRIDE;
   virtual int setVadMode(AudioDeviceRole role, VadMode mode) OVERRIDE;
   virtual int setGainSettings(const GainSettings& settings) OVERRIDE;

   virtual int setAudioDscp(unsigned int mediaDscp) OVERRIDE;

   virtual int startMonitoringCaptureDeviceLevels(unsigned int deviceId) OVERRIDE;
   virtual int stopMonitoringCaptureDeviceLevels() OVERRIDE;
   virtual int startMonitoringRenderDeviceLevels(unsigned int deviceId, const cpc::string& resourceUri) OVERRIDE;
   virtual int stopMonitoringRenderDeviceLevels() OVERRIDE;

   virtual int setCodecConfig(const G729Config& config) OVERRIDE;
   virtual int setCodecConfig(const OpusConfig& config) OVERRIDE;

   virtual int connectAudioStreams(int recvStreamId, int sendStreamId) OVERRIDE;
   virtual int disconnectAudioStreams(int recvStreamId, int sendStreamId) OVERRIDE;

   // Android Audio
   virtual int setHardwareEchoCancellationEnabled(const bool enabled) OVERRIDE;
   virtual int setHardwareAutomaticGainControlEnabled(const bool enabled) OVERRIDE;
   virtual int setHardwareNoiseSuppressionEnabled(const bool enabled) OVERRIDE;
   virtual int setLowLatencyPlayoutEnabled(const bool enabled) OVERRIDE;
   virtual int setLowLatencyAudioTrackEnabled(const bool enabled) OVERRIDE;
   virtual int setAudioSource(int audioSource) OVERRIDE;

   // iOS Audio
   virtual int activatePlayAndRecordMode(bool activate) OVERRIDE;
   virtual int setAudioSessionActivated(bool activated) OVERRIDE;
   virtual int setUseQosFastlane(bool useQosFastlane, bool useWithDscp) OVERRIDE;
   virtual int setAudioInterruptionsHandlingEnabled(bool enabled) OVERRIDE;
   
   // AudioExt
   virtual int setAudioDeviceFile(const cpc::string& inputAudiofileNameUtf8, const cpc::string& outputAudiofileNameUtf8) OVERRIDE;
   virtual int setMicAGCEnabled(bool enabled) OVERRIDE;

   webrtc_recon::MediaStackImpl* media_stack() const;

   void postCallback(resip::ReadCallbackBase* fp);

   void updatePerformanceProfile(DevicePerformanceProfile perfProfile);
   void initPlaySoundDevicePool();
   void shutdownPlaySoundDevicePool();

   void onSystemAudioServiceError(int errorLevel);

   MediaManagerInterface* mediaManagerInterface();

   void addSdkObserver(AudioHandler* observer);
   void removeSdkObserver(AudioHandler* observer);

   void fireAudioDeviceListUpdated(AudioHandler* appHandler, const AudioDeviceListUpdatedEvent& args);
   void fireAudioDeviceLevelChange(AudioHandler* appHandler, const AudioDeviceLevelChangeEvent& args);
   void fireAudioStreamStarted(AudioHandler* appHandler, const AudioStreamStartedEvent& args);
   void fireAudioStreamStopped(AudioHandler* appHandler, const AudioStreamStoppedEvent& args);
   
   void fireAudioDeviceVolume(AudioHandler* appHandler, const AudioDeviceVolumeEvent& args);
   void firePlaySoundComplete(AudioHandler* appHandler, const PlaySoundHandle& args);
   void firePlaySoundFailure(AudioHandler* appHandler, const PlaySoundHandle& args);

   void fireAudioCodecListUpdated(AudioHandler* appHandler, const AudioCodecListUpdatedEvent& args);

private:
   virtual void setCodecConfigImpl(const G729Config& config);
   virtual void setOpusCodecConfigImpl(const OpusConfig& config);

private:
   PlaySoundHandle mNextPlaySoundHandle;
   MediaManagerInterface* mMM;
   AudioImpl* mImpl;
   std::set<AudioHandler*> mSdkObservers;
};

std::ostream& operator<<(std::ostream& os, const AudioDeviceListUpdatedEvent& type);
std::ostream& operator<<(std::ostream& os, const AudioCodecListUpdatedEvent& type);
std::ostream& operator<<(std::ostream& os, const AudioDeviceInfo& type);
std::ostream& operator<<(std::ostream& os, const G729Config& type);
std::ostream& operator<<(std::ostream& os, const GainSettings& settings);
std::ostream& operator<<(std::ostream& os, const AudioCodecInfo& codecInfo);


}
}
#endif // CPCAPI2_AUDIO_INTERFACE_H
