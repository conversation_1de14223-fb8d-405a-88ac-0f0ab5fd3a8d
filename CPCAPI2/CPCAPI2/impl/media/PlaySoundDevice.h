#pragma once

#if !defined(CPCAPI2_PLAY_SOUND_DEVICE_H)
#define CPCAPI2_PLAY_SOUND_DEVICE_H

#include <webrtc/common_types.h>

#include <memory>

namespace webrtc
{
class VoiceEngine;
class VoEHardware;
class VoEFile;
class VoEBase;
class VoEDtmf;
class VoEAudioProcessing;
class VoEVolumeControl;
class SystemAudioServiceErrorCallback;
}

namespace webrtc_recon
{
   class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class AudioImpl;

class PlaySoundDevice
{
public:
   PlaySoundDevice();
   virtual ~PlaySoundDevice();

   int init(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, webrtc::AudioLayers audioLayer, int32_t audioUsage);
   int shutdown();

   webrtc::VoEBase* voe_base() const { return mVoEBase; }
   webrtc::VoEFile* voe_file() const { return mVoEFile; }
   webrtc::VoEDtmf* voe_dtmf() const { return mVoEDtmf; }
   webrtc::VoEHardware* voe_hardware() const { return mVoEHardware; }
   webrtc::VoEVolumeControl* voe_volume_control() const { return mVoEVolumeControl; }

private:
   AudioImpl* mAudio;

   webrtc::VoiceEngine* mVoiceEngine;
   webrtc::VoEBase* mVoEBase;
   webrtc::VoEFile* mVoEFile;
   webrtc::VoEDtmf* mVoEDtmf;
   webrtc::VoEHardware* mVoEHardware;
   webrtc::VoEAudioProcessing* mVoEAudioProcessing;
   webrtc::VoEVolumeControl* mVoEVolumeControl;

   bool mOwnMediaStack;

};

struct PlaySoundDeviceAllocation
{
   PlaySoundDevice* device;
   bool allocated;

   PlaySoundDeviceAllocation()
   {
      device = NULL;
      allocated = false;
   }

   virtual ~PlaySoundDeviceAllocation()
   {
   }
};

class PlaySoundDeviceAllocationHandle
{
public:
   PlaySoundDeviceAllocationHandle(const std::shared_ptr<PlaySoundDeviceAllocation>& alloc)
      : mAlloc(alloc)
   {
   }

   virtual ~PlaySoundDeviceAllocationHandle();

   PlaySoundDevice* operator->() const
   {
      return mAlloc->device;
   }

private:
   std::shared_ptr<PlaySoundDeviceAllocation> mAlloc;
};

class PlaySoundDevicePool
{
public:
   PlaySoundDevicePool() {}
   virtual ~PlaySoundDevicePool() {}
   void init(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, unsigned int size, webrtc::AudioLayers audioLayer);
   std::shared_ptr<PlaySoundDeviceAllocationHandle> getPlaySoundDevice(webrtc::SystemAudioServiceErrorCallback* systemAudioServiceErrorCallback, int32_t audioUsage);
   void shutdown();

private:
   std::vector<std::shared_ptr<PlaySoundDeviceAllocation> > mPool;
};
}
}

#endif // CPCAPI2_PLAY_SOUND_DEVICE_H
