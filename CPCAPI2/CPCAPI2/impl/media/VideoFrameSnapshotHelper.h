#pragma once

#if !defined(CPCAPI2_VIDEO_FRAME_SNAPSHOT_HELPER_H)
#define CPCAPI2_VIDEO_FRAME_SNAPSHOT_HELPER_H

#include "cpcapi2defs.h"

#include "interface/public/phone/Phone.h"

#include <MixerImpl.hxx>

#include <vie_image_process.h>

#include <iostream>
#include <set>
#include <thread>
#include <memory>
#include <atomic>

namespace CPCAPI2
{
namespace Media
{
class VideoFrameSnapshotHelper : public webrtc_recon::FrameMonitorExternalObserver
{
public:
   VideoFrameSnapshotHelper(CPCAPI2::Phone* phone, resip::ReadCallbackBase* handler, const cpc::string& filenameUtf8, const std::shared_ptr<webrtc_recon::MixerImpl>& mixer, int recv_channel);
   virtual ~VideoFrameSnapshotHelper();

   // ViEEffectFilter
   virtual int Transform(webrtc::VideoFrame* video_frame);
   virtual void Release() {}

private:


private:
   CPCAPI2::Phone* mPhone;
   resip::ReadCallbackBase* mHandler;
   cpc::string mFilename;
   std::shared_ptr<webrtc_recon::MixerImpl> mMixer;
   int mRecvChannel;
   std::thread* mWorkerThread;
};

}
}

#endif // CPCAPI2_VIDEO_FRAME_SNAPSHOT_HELPER_H

