#include "brand_branded.h"

#if (CPCAPI2_BRAND_MOZJPEG == 1)
#if defined(WIN32)
#include <basetsd.h>
#endif
#include <setjmp.h>
#include <stdio.h>
#include <string.h>
#include <fstream>
#include <sstream>

#include <rutil/Data.hxx>

// NOTE(ajm): Path provided by gyp.
#include "JpegHelper.h"
#include "webrtc/common_video/libyuv/include/webrtc_libyuv.h"
extern "C" {
#include "turbojpeg.h"
} // extern "C"

namespace CPCAPI2
{
namespace Media
{
JpegEncoder::JpegEncoder()
{
}

JpegEncoder::~JpegEncoder()
{
}

unsigned long JpegEncoder::MaxEncodedSize(int width, int height)
{
   return tjBufSize(width, height, TJSAMP_420);
}

unsigned long
JpegEncoder::Encode(const webrtc::VideoFrame& inputImage, unsigned char*& outbuff, unsigned long outbuffSize)
{
    if (inputImage.IsZeroSize())
    {
        return -1;
    }
    if (inputImage.width() < 1 || inputImage.height() < 1)
    {
        return -1;
    }

    const int width = inputImage.width();
    const int height = inputImage.height();

    tjhandle tj_hdl = tjInitCompress();
    const unsigned char* srcPlanes[3];
    srcPlanes[0] = (unsigned char*)inputImage.buffer(webrtc::kYPlane);
    srcPlanes[1] = (unsigned char*)inputImage.buffer(webrtc::kUPlane);
    srcPlanes[2] = (unsigned char*)inputImage.buffer(webrtc::kVPlane);
    int strides[3];
    strides[0] = inputImage.stride(webrtc::kYPlane);
    strides[1] = inputImage.stride(webrtc::kUPlane);
    strides[2] = inputImage.stride(webrtc::kVPlane);

    unsigned long outSize = outbuffSize;
    tjCompressFromYUVPlanes(tj_hdl, srcPlanes, width, strides, height, TJSAMP_420, &outbuff, &outSize, 100, TJFLAG_NOREALLOC);
    tjDestroy(tj_hdl);

#if 0
    std::ofstream outstr("img8.jpg", std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
    if (outstr.is_open())
    {
       resip::Data dataStr(resip::Data::Share, (const char*)outbuff, (UInt32)outSize);
       outstr << dataStr << std::flush;
       outstr.close();
    }
#endif

    return outSize;
}

}
}
#endif

