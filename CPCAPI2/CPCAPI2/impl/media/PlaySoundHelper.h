#pragma once

#if !defined(CPCAPI2_PLAY_SOUND_HELPER_H)
#define CPCAPI2_PLAY_SOUND_HELPER_H

#include "media/audio/AudioHandler.h"
#include "cpcapi2defs.h"

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <voe_file.h>
#include <voe_hardware.h>


namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class AudioImpl;
class PlaySoundDevice;
class PlaySoundDeviceAllocationHandle;

class PlaySoundHelper :
   public std::enable_shared_from_this<PlaySoundHelper>,
   public resip::DeadlineTimerHandler,
   public webrtc::VoEFileEventObserver,
   public webrtc::SystemAudioServiceErrorCallback
{
public:
   PlaySoundHelper(AudioImpl* audio, resip::MultiReactor& reactor);
   virtual ~PlaySoundHelper();

   void stop();

   int playSound(PlaySoundHandle h, int webrtcDeviceIdx, int32_t audioUsage, const cpc::string& resourceUri, bool repeat);

   int channel() const { return mChannel; }
   PlaySoundHandle handle() const { return mHandle; }

   virtual void OnLocallyPlayingFileFinished(int channel) OVERRIDE;
   virtual void OnLocallyPlayingFileWithHandleFinished(PlaySoundHandle handle);

   virtual void onSystemAudioServiceError(int errorLevel) OVERRIDE;

   void detatchAudioImpl();

private:
   // begin resip::DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;
   // end resip::DeadlineTimerHandler

   static void onLocallyPlayingFileFinishedImpl(std::weak_ptr<PlaySoundHelper>&, int channel);

   void postPlaySoundFailure(PlaySoundHandle handle);

   int mChannel;
   PlaySoundHandle mHandle;
   AudioImpl* mAudio;
   void *mImpl; // used by ObjC wrapper

   resip::MultiReactor& mReactor;
   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   std::shared_ptr<CPCAPI2::Media::PlaySoundDeviceAllocationHandle> mDevice;
};
}
}
#endif // CPCAPI2_PLAY_SOUND_HELPER_H
