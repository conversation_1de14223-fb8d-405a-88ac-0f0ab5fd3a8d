#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "WebSocketServerManagerInterface.h"
#include "WebSocketServerManagerImpl.h"
#include "../util/cpc_logger.h"
#include "../phone/PhoneInterface.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WEBSOCKET_SERVER

using resip::ReadCallbackBase;

namespace CPCAPI2
{
namespace WebSocketServer
{
WebSocketServerManagerInterface::WebSocketServerManagerInterface(Phone* phone)
	: EventSource<WebSocketServerHandle, WebSocketServerHandler, WebSocketServerSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
}

WebSocketServerManagerInterface::~WebSocketServerManagerInterface()
{
}

void WebSocketServerManagerInterface::Release()
{
   delete this;
}

int WebSocketServerManagerInterface::process(unsigned int timeout)
{
   return CPCAPI2::EventSource<WebSocketServerHandle, WebSocketServerHandler, WebSocketServerSyncHandler>::process(timeout);
}

void WebSocketServerManagerInterface::interruptProcess()
{
   CPCAPI2::EventSource<WebSocketServerHandle, WebSocketServerHandler, WebSocketServerSyncHandler>::interruptProcess();
}

void WebSocketServerManagerInterface::setCallbackHook(void(*cbHook)(void*), void* context)
{
   CPCAPI2::EventSource<WebSocketServerHandle, WebSocketServerHandler, WebSocketServerSyncHandler>::setCallbackHook(cbHook, context);
}

PhoneInterface* WebSocketServerManagerInterface::phoneInterface()
{
   return mPhone;
}

int WebSocketServerManagerInterface::setHandler(WebSocketServerHandler* handler)
{
   if (NULL != handler)
   {
      mImpl.reset(new WebSocketServerManagerImpl(this, handler));
   }
   else
   {
      mImpl.reset();
   }

   setAppHandler(0, handler);

   return kSuccess;
}

int WebSocketServerManagerInterface::start(WebSocketServerSettings* settings)
{
   postToSdkThread(resip::resip_bind(&WebSocketServerManagerInterface::startImpl, this, settings));
   return kSuccess;
}

void WebSocketServerManagerInterface::startImpl(WebSocketServerSettings* settings)
{
   if (mImpl) mImpl->start(settings);
}

int WebSocketServerManagerInterface::stop()
{
   postToSdkThread(resip::resip_bind(&WebSocketServerManagerInterface::stopImpl, this));
   return kSuccess;
}

void WebSocketServerManagerInterface::stopImpl()
{
   if (mImpl) mImpl->stop();
}
int WebSocketServerManagerInterface::send(ConnectionHandle connectionID, const cpc::string& data)
{
   postToSdkThread(resip::resip_bind(&WebSocketServerManagerInterface::sendImpl, this, connectionID, data));
   return kSuccess;
}

void WebSocketServerManagerInterface::sendImpl(int connectionID, const cpc::string& data)
{
   if (mImpl) mImpl->send(connectionID, data);
}

int WebSocketServerManagerInterface::close(ConnectionHandle connection, CloseReason closeReason)
{
   postToSdkThread(resip::resip_bind(&WebSocketServerManagerInterface::closeImpl, this, connection, closeReason));
   return kSuccess;
}

void WebSocketServerManagerInterface::closeImpl(int connectionID, CloseReason closeReason)
{
   if (mImpl) mImpl->close(connectionID, closeReason);
}

} //namespace WebSocketServer
} //namespace CPCAPI2

#endif
