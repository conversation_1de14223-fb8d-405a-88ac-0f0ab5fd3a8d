#include "brand_branded.h"

#include "interface/experimental/websocket_server/WebSocketServerManager.h"

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1)
#include "WebSocketServerManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace WebSocketServer
{
WebSocketServerManager* WebSocketServerManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<WebSocketServerManagerInterface>(phone, "WebSocketServerManagerInterface");
#else
   return NULL;
#endif
}

}
}
