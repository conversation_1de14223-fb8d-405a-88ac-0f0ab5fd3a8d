#pragma once

#if !defined(CPCAPI2_WEBSOCKET_SERVER_MANAGER_IMPL_H)
#define CPCAPI2_WEBSOCKET_SERVER_MANAGER_IMPL_H

#include "cpcapi2defs.h"
#include "websocket_server/WebSocketServerManager.h"
#include "websocket_server/WebSocketServerHandler.h"
#include "WebSocketServerSyncHandler.h"

#include <websocketpp/config/asio.hpp>
#include <websocketpp/server.hpp>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
class PhoneInterface;

namespace WebSocketServer
{
class WebSocketServerHandler;
class WebSocketServerManagerInterface;

typedef websocketpp::config::asio::message_type::ptr message_ptr;
typedef websocketpp::lib::shared_ptr<websocketpp::lib::asio::ssl::context> context_ptr;
typedef websocketpp::server <websocketpp::config::asio_tls> wss_server;
//typedef websocketpp::server<websocketpp::config::asio> wss_server;

class WebSocketServerManagerImpl
{
public:
   WebSocketServerManagerImpl(WebSocketServerManagerInterface* iface, WebSocketServerHandler* handler);
   virtual ~WebSocketServerManagerImpl();

   CPCAPI2::PhoneInterface* getPhoneInterface() const;

   void start(WebSocketServerSettings* settings);
   void stop();
   void send(ConnectionHandle connectionID, const cpc::string& data);
   void close(ConnectionHandle connectionID, CloseReason closeReason);

private:
   //websocketpp:
   void onOpen(websocketpp::connection_hdl hdl);
   //void onFail(websocketpp::connection_hdl hdl);
   void onClose(websocketpp::connection_hdl hdl);
   void onMessage(websocketpp::connection_hdl hdl, message_ptr msg);
   //void onHttp(websocketpp::connection_hdl hdl);
   context_ptr onTlsInit(websocketpp::connection_hdl hdl);
   //std::string getPassword();

private:
   WebSocketServerManagerInterface* m_if;
   wss_server mServer;
   websocketpp::lib::shared_ptr<websocketpp::lib::thread> mServerThread;
   std::map<websocketpp::connection_hdl, ConnectionHandle, std::owner_less<websocketpp::connection_hdl>> mConnections;
   static ConnectionHandle sNextConnectionId;
   bool mStopping;
   bool mStarted;
   WebSocketServerSettings mSettings;
   bool logMessages;
};

}
}

#endif // CPCAPI2_WEBSOCKET_SERVER_MANAGER_IMPL_H
