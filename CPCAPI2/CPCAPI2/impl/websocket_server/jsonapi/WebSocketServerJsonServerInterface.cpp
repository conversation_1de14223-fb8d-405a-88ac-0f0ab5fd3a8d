#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "WebSocketServerJsonServerInterface.h"
#include "websocket_server/WebSocketServerManagerInterface.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "util/LogSubsystems.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WEBSOCKET_SERVER
#define JSON_MODULE "WebSocketServerManagerJsonProxy"

namespace CPCAPI2
{
namespace WebSocketServer
{
   WebSocketServerJsonServerInterface::WebSocketServerJsonServerInterface(Phone* phone)
   : mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mWsServerMgr(CPCAPI2::WebSocketServer::WebSocketServerManager::getInterface(phone))
{
   WebSocketServerManagerInterface* wsServerMgrIf = dynamic_cast<WebSocketServerManagerInterface*>(mWsServerMgr);
   wsServerMgrIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&WebSocketServerJsonServerInterface::handleSetHandler, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["start"] = std::bind(&WebSocketServerJsonServerInterface::handleStart, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["stop"] = std::bind(&WebSocketServerJsonServerInterface::handleStop, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["send"] = std::bind(&WebSocketServerJsonServerInterface::handleSend, this, std::placeholders::_1, std::placeholders::_2);
 
   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

   WebSocketServerJsonServerInterface::~WebSocketServerJsonServerInterface()
{
   dynamic_cast<WebSocketServerManagerInterface*>(mWsServerMgr)->removeSdkObserver(this);
}

void WebSocketServerJsonServerInterface::Release()
{
}

void WebSocketServerJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void WebSocketServerJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int WebSocketServerJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&WebSocketServerJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void WebSocketServerJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();
   DebugLog(<< "WebSocketServerJsonServerInterface handling " << funcName);

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int WebSocketServerJsonServerInterface::handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn)
{
   return kSuccess;
}

int WebSocketServerJsonServerInterface::handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mWsServerMgr->setHandler((WebSocketServerHandler*)0xDEADBEFF);
   return kSuccess;
}

int WebSocketServerJsonServerInterface::handleStart(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mWsServerMgr->start();
   return kSuccess;
}

int WebSocketServerJsonServerInterface::handleStop(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   mWsServerMgr->stop();
   return kSuccess;
}

int WebSocketServerJsonServerInterface::handleSend(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal)
{
   ConnectionHandle conn = 0;
   cpc::string data;

   JsonDeserialize(functionObjectVal, "connection", conn, "data", data);

   if (!data.empty())
   {
      mWsServerMgr->send(conn, data);
   }
   return kSuccess;
}

int WebSocketServerJsonServerInterface::onConnectionOpened(CPCAPI2::WebSocketServer::WebSocketServerHandle h, const CPCAPI2::WebSocketServer::ConnectionOpenedEvent& args)
{
   JsonFunctionCall(mTransport, "onConnectionOpened", JSON_VALUE(h), "connection", args.connection);
   return kSuccess;
}

int WebSocketServerJsonServerInterface::onConnectionClosed(CPCAPI2::WebSocketServer::WebSocketServerHandle h, const CPCAPI2::WebSocketServer::ConnectionClosedEvent& args)
{
   JsonFunctionCall(mTransport, "onConnectionClosed", JSON_VALUE(h), "connection", args.connection);
   return kSuccess;
}

int WebSocketServerJsonServerInterface::onMessageReceived(CPCAPI2::WebSocketServer::WebSocketServerHandle h, const CPCAPI2::WebSocketServer::MessageReceivedEvent& args)
{
   JsonFunctionCall(mTransport, "onMessageReceived", JSON_VALUE(h), "connection", args.connection, "data", args.data);
   return kSuccess;
}

int WebSocketServerJsonServerInterface::onServerStateChange(CPCAPI2::WebSocketServer::WebSocketServerHandle h, const CPCAPI2::WebSocketServer::ServerStateChangeEvent& args)
{
   JsonFunctionCall(mTransport, "onServerStateChange", JSON_VALUE(h), "currentState", args.currentState, "message", args.message);
   return kSuccess;
}

int WebSocketServerJsonServerInterface::onError(CPCAPI2::WebSocketServer::WebSocketServerHandle h, const CPCAPI2::WebSocketServer::ErrorEvent& args)
{
   JsonFunctionCall(mTransport, "onError", JSON_VALUE(h), "connection", args.connection, "errorText", args.errorText);
   return kSuccess;
}

}
}
#endif
