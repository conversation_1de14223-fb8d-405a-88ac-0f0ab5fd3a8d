#include "brand_branded.h"

#include <interface/experimental/websocket_server/WebSocketServerJsonApi.h>

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "WebSocketServerJsonServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace WebSocketServer
   {
      WebSocketServerJsonApi* WebSocketServerJsonApi::getInterface(Phone* cpcPhone)
      {
#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<WebSocketServerJsonServerInterface>(phone, "WebSocketServerJsonA<PERSON>");
#else
         return NULL;
#endif
      }

   }
}
