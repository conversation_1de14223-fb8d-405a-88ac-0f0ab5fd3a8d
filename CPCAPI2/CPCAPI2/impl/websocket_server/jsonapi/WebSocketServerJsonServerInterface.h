#pragma once

#if !defined(CPCAPI2_WEBSOCKET_SERVER_JSON_INTERFACE_H)
#define CPCAPI2_WEBSOCKET_SERVER_JSON_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/websocket_server/WebSocketServerJsonApi.h"
#include "interface/experimental/websocket_server/WebSocketServerHandler.h"
#include "interface/experimental/websocket_server/WebSocketServerManager.h"
#include "websocket_server/WebSocketServerSyncHandler.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>

namespace CPCAPI2
{
class PhoneInterface;
namespace WebSocketServer
{
class WebSocketServerJsonServerInterface : public CPCAPI2::WebSocketServer::WebSocketServer<PERSON><PERSON><PERSON>,
                                           public CPCAPI2::WebSocketServer::WebSocketServer<PERSON>son<PERSON><PERSON>,
                                           public CPCAPI2::WebSocketServer::WebSocketServerSyncHand<PERSON>,
                                           public CPCAPI2::JsonApi::JsonApiServerModule,
                                           public CPCAPI2::PhoneModule
{
public:
   WebSocketServerJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~WebSocketServerJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;
   virtual int handleConnectionClosed(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn) OVERRIDE;

   // Inherited via WebSocketServerHandler
   virtual int onConnectionOpened(WebSocketServerHandle handle, const ConnectionOpenedEvent& evt) OVERRIDE;
   virtual int onConnectionClosed(WebSocketServerHandle handle, const ConnectionClosedEvent& evt)  OVERRIDE;
   virtual int onMessageReceived(WebSocketServerHandle handle, const MessageReceivedEvent& evt)  OVERRIDE;
   virtual int onServerStateChange(WebSocketServerHandle handle, const ServerStateChangeEvent& evt)  OVERRIDE;
   virtual int onError(WebSocketServerHandle handle, const ErrorEvent& evt)  OVERRIDE;

private:
   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStart(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleStop(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);
   int handleSend(CPCAPI2::JsonApi::JsonApiRequestInfo connId, const rapidjson::Value & functionObjectVal);

private:
   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo,const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;
   CPCAPI2::WebSocketServer::WebSocketServerManager* mWsServerMgr;
};
}
}
#endif // CPCAPI2_WEBSOCKET_SERVER_JSON_INTERFACE_H
