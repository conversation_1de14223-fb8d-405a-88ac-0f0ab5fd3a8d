#include "brand_branded.h"

#if (CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE == 1)
#include "WebSocketServerManagerImpl.h"
#include "WebSocketServerManagerInterface.h"
#include "websocket_server/WebSocketServerHandler.h"

#include "cpcapi2utils.h"
#include "../util/DumFpCommand.h"
#include "../util/cpc_logger.h"
#include "rutil/Data.hxx"

#include <websocketpp/uri.hpp>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WEBSOCKET_SERVER

using resip::ReadCallbackBase;
using resip::Data;
using websocketpp::lib::placeholders::_1;
using websocketpp::lib::placeholders::_2;
using websocketpp::lib::bind;

namespace CPCAPI2
{
namespace WebSocketServer
{
ConnectionHandle WebSocketServerManagerImpl::sNextConnectionId = 1;

WebSocketServerManagerImpl::WebSocketServerManagerImpl(WebSocketServerManagerInterface* iface, WebSocketServerHandler* handler)
   : m_if(iface), mStopping(false), mStarted(false), logMessages(false)
{
#ifdef CPCAPI2_AUTO_TEST
   logMessages = true;
#endif

#ifdef CPCAPI2_BRAND_WEBSOCKET_SERVER_CERTIFICATE_PEM
   mSettings.certificatePem = CPCAPI2_BRAND_WEBSOCKET_SERVER_CERTIFICATE_PEM;
#endif
#ifdef CPCAPI2_BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PEM
   mSettings.privateKeyPem = CPCAPI2_BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PEM;
#endif
//#ifdef CPCAPI2_BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PASSWORD
// mSettings.password = CPCAPI2_BRAND_WEBSOCKET_SERVER_PRIVATE_KEY_PASSWORD;
//#endif
#ifdef CPCAPI2_BRAND_WEBSOCKET_SERVER_URL
   mSettings.url = CPCAPI2_BRAND_WEBSOCKET_SERVER_URL;
#endif

   mServer.clear_access_channels(websocketpp::log::alevel::all);
   mServer.clear_error_channels(websocketpp::log::elevel::all);
   mServer.init_asio();
   mServer.set_reuse_addr(true);

   mServer.set_open_handler(bind(&WebSocketServerManagerImpl::onOpen, this, ::_1));
   //mServer.set_fail_handler(bind(&WebSocketServerManagerImpl::onFail, this, ::_1));
   mServer.set_close_handler(bind(&WebSocketServerManagerImpl::onClose, this, ::_1));   
   mServer.set_message_handler(bind(&WebSocketServerManagerImpl::onMessage, this, ::_1, ::_2));
   //mServer.set_http_handler(bind(&WebSocketServerManagerImpl::onHttp, this, ::_1));
   mServer.set_tls_init_handler(bind(&WebSocketServerManagerImpl::onTlsInit, this, ::_1));
}

WebSocketServerManagerImpl::~WebSocketServerManagerImpl()
{
   if(mStarted) stop();
}

void WebSocketServerManagerImpl::onOpen(websocketpp::connection_hdl hdl)
{
   mConnections[hdl] = sNextConnectionId++;

   ConnectionOpenedEvent evt(mConnections[hdl]);
   auto c = mServer.get_con_from_hdl(hdl);
   evt.requestHost = c->get_host().c_str();
   evt.requestPort = std::to_string(c->get_port()).c_str();
   evt.requestResource = c->get_resource().c_str();
   m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onConnectionOpened), 0, evt);

   DebugLog(<< "WebSocketServerManagerImpl::onOpen: " << hdl.lock().get() << "[" << mConnections[hdl] << "]");
}

//void WebSocketServerManagerImpl::onFail(websocketpp::connection_hdl hdl)
//{
//   wss_server::connection_ptr con = mServer.get_con_from_hdl(hdl);
//   ErrLog(<< "WebSocketServerManagerImpl::onFail handler for hdl:" << hdl.lock().get());
//   ErrLog(<< "state: " << con->get_state());
//   ErrLog(<< "local close code: " << con->get_local_close_code());
//   ErrLog(<< "local close reason: " << con->get_local_close_reason());
//   ErrLog(<< "remote close code: " << con->get_remote_close_code());
//   ErrLog(<< "remote close reason: " << con->get_remote_close_reason());
//   ErrLog(<< "ec: " << con->get_ec() << " - " << con->get_ec().message());
//}

void WebSocketServerManagerImpl::onClose(websocketpp::connection_hdl hdl)
{
   if (!mStopping)
   {
      auto con = mConnections.find(hdl);
      if (con != mConnections.end())
      {
         DebugLog(<< "WebSocketServerManagerImpl::onClose: " << hdl.lock().get() << "[" << con->second << "]");
         ConnectionClosedEvent evt(con->second);
         m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onConnectionClosed), 0, evt);
         mConnections.erase(hdl);
      }
      else
      {
         DebugLog(<< "WebSocketServerManagerImpl::onClose: unable to find connection with hdl " << hdl.lock().get());
      }
   }
}

void WebSocketServerManagerImpl::onMessage(websocketpp::connection_hdl hdl, message_ptr msg)
{
   auto con = mConnections.find(hdl);
   if (con != mConnections.end())
   {
      DebugLog(<< "WebSocketServerManagerImpl::onMessage: " << hdl.lock().get() << "[" << con->second << "] "
         << (logMessages ? msg->get_payload() : ""));

      MessageReceivedEvent evt(con->second, msg->get_payload().c_str());
      m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onMessageReceived), 0, evt);
   }
   else
   {
      DebugLog(<< "WebSocketServerManagerImpl::onMessage: unable to find connection with hdl " << hdl.lock().get());
   }
}

//void WebSocketServerManagerImpl::onHttp(websocketpp::connection_hdl hdl)
//{
//   DebugLog(<< "WebSocketServerManagerImpl::onHttp: connection with hdl " << hdl.lock().get());
//}

context_ptr WebSocketServerManagerImpl::onTlsInit(websocketpp::connection_hdl hdl)
{
   DebugLog(<< "WebSocketServerManagerImpl::onTlsInit called with hdl: " << hdl.lock().get());

   const Data PEM(".pem");
   const Data BEGINCERTIFICATE("-----BEGIN CERTIFICATE-----");
   const Data ENDCERTIFICATE("-----END CERTIFICATE-----");
   const Data BEGINPRIVATEKEY("-----BEGIN PRIVATE KEY-----");
   const Data ENDPRIVATEKEY("-----END PRIVATE KEY-----");
   const Data NEWLINE("\n");

   namespace ssl = websocketpp::lib::asio::ssl;
   context_ptr ctx = websocketpp::lib::make_shared<ssl::context>(ssl::context::sslv23);
   boost::system::error_code ec;

   ctx->set_options(ssl::context::default_workarounds |
      ssl::context::no_sslv2 |
      ssl::context::no_sslv3 |
      ssl::context::no_tlsv1 |
      ssl::context::no_tlsv1_1, ec);
   if (ec)
   {
      ErrLog(<< "WebSocketServerManagerImpl::onTlsInit: error setting options");
      return ctx;
   }
   /*ctx->set_password_callback(bind(&WebSocketServerManagerImpl::getPassword, this), ec);
   if (ec)
   {
      ErrLog(<< "WebSocketServerManagerImpl::onTlsInit: error setting password callback");
      return ctx;
   }*/

   Data cert(mSettings.certificatePem.c_str());
   if (cert.postfix(PEM))
   {
      ctx->use_certificate_chain_file(cert.c_str(), ec);
   }
   else
   {
      // The following 'patching' to arrive at proper PEM formatting seems silly, especially as we would expect a complete chain
      // of multiple certificates in one blob, but leaving it in, in case some application is relying on just providing the 'meat' 
      // of the PEM structure. 
      if (!cert.prefix(BEGINCERTIFICATE))
      {
         cert = BEGINCERTIFICATE + NEWLINE + cert;
      }
      if (cert.postfix(NEWLINE))
      {
         if (!cert.postfix(ENDCERTIFICATE + NEWLINE))
         {
            cert += ENDCERTIFICATE + NEWLINE;
         }
      }
      else
      {
         if (!cert.postfix(ENDCERTIFICATE))
         {
            cert += NEWLINE + ENDCERTIFICATE + NEWLINE;
         }
         else
         {
            cert += NEWLINE;
         }
      }
      boost::asio::const_buffer buffer(cert.data(), cert.size());
      ctx->use_certificate_chain(buffer, ec);
   }
   if (ec)
   {
      ErrLog(<< "WebSocketServerManagerImpl::onTlsInit: error setting certificate");
      return ctx;
   }

   Data key(mSettings.privateKeyPem.c_str());
   if (key.postfix(PEM))
   {
      ctx->use_private_key_file(key.c_str(), ssl::context::pem, ec);
   }
   else
   {
      if (!key.prefix(BEGINPRIVATEKEY))
      {
         key = BEGINPRIVATEKEY + NEWLINE + key;
      }
      if (key.postfix(NEWLINE))
      {
         if (!key.postfix(ENDPRIVATEKEY + NEWLINE))
         {
            key += ENDPRIVATEKEY + NEWLINE;
         }
      }
      else
      {
         if (!key.postfix(ENDPRIVATEKEY))
         {
            key += NEWLINE + ENDPRIVATEKEY + NEWLINE;
         }
         else
         {
            key += NEWLINE;
         }
      }

      boost::asio::const_buffer buffer(key.data(), key.size());
      ctx->use_private_key(buffer, ssl::context::pem, ec);
   }
   if (ec)
   {
      ErrLog(<< "WebSocketServerManagerImpl::onTlsInit: error setting private key");
      return ctx;
   }

   if (SSL_CTX_set_cipher_list(ctx->native_handle(), mSettings.ciphers.c_str()) != 1)
   {
      ErrLog(<< "WebSocketServerManagerImpl::onTlsInit: error setting ciphers");
   }
   return ctx;
}

//std::string WebSocketServerManagerImpl::getPassword()
//{
//   mSettings.password.c_str();
//}

void WebSocketServerManagerImpl::start(WebSocketServerSettings* settings)
{
   if (!mStarted)
   {
      if (NULL != settings)
      {
         mSettings = *settings;
      }

      websocketpp::uri uri(mSettings.url.c_str());
      uint16_t port = uri.get_port();
      if (port == 0)
      {
         port = CPCAPI2_WEBSOCKET_SERVER_DEFAULT_PORT;
         DebugLog(<< "WebSocketServerManagerImpl::start: using default port: " << port << " (" << mSettings.url.c_str() << ")");
      }
      else
         DebugLog(<< "WebSocketServerManagerImpl::start: got port from settings: " << port << " (" << mSettings.url.c_str() << ")");

      mServer.reset();

      try
      {
         mServer.listen(port);
      }
      catch(const websocketpp::exception& e)
      {
         CritLog(<< "WebSocketServerManagerImpl::start: mServer.listen exception code: " << e.code());
         return;
      }
      
      mStarted = true;
      mServer.start_accept();
      mServerThread.reset(new websocketpp::lib::thread(&wss_server::run, &mServer));
      
      ServerStateChangeEvent evt(ServerState_Running, "Server started.");
      m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onServerStateChange), 0, evt);

      boost::system::error_code ec;
      boost::asio::ip::tcp::endpoint ep = mServer.get_local_endpoint(ec);
      if (!ec)
         DebugLog(<< "WebSocketServerManagerImpl::start: server listening on "<< ep.address().to_string() << ":" << ep.port());
   }
   else
      DebugLog(<< "WebSocketServerManagerImpl::start: server already started.");
}

void WebSocketServerManagerImpl::stop()
{
   if (mStarted)
   {
      mStopping = true;
      mStarted = false;
      mServer.stop_listening();

      for (auto con : mConnections)
      {
         websocketpp::lib::error_code ec;
         mServer.close(con.first, websocketpp::close::status::going_away, "", ec);
         if (ec)
         {
            DebugLog(<< "WebSocketServerManagerImpl::stop: error closing connection " << con.first.lock().get() << " (" << ec.message() << ").");
         }
      }

      mConnections.clear();
      mServerThread->join();
      mStopping = false;

      ServerStateChangeEvent evt(ServerState_Stopped, "Server stopped.");
      m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onServerStateChange), 0, evt);
   }
   else
      DebugLog(<< "WebSocketServerManagerImpl::stop: server already stopped.");
}

void WebSocketServerManagerImpl::send(ConnectionHandle connection, const cpc::string& data)
{
   websocketpp::connection_hdl hdl;
   bool found = false;
   for (auto con : mConnections)
   {
      if (con.second == connection)
      {
         hdl = con.first;
         found = true;
         break;
      }
   }
   if (found)
   {
      websocketpp::lib::error_code ec;
      mServer.send(hdl, data.c_str(), websocketpp::frame::opcode::text, ec);

      if (ec)
      {
         std::stringstream err;
         err << "Send failed: " << ec.message();
         ErrorEvent evt(connection, err.str().c_str());
         DebugLog(<< "WebSocketServerManagerImpl::send: " << hdl.lock().get() << "[" << connection << "] " << err.str().c_str());
         m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onError), 0, evt);
         return;
      }
      DebugLog(<< "WebSocketServerManagerImpl::send: " << hdl.lock().get() << "[" << connection << "] " << (logMessages ? data : ""));
   }
   else
   {
      DebugLog(<< "WebSocketServerManagerImpl::send: Invalid connection handle.");
      ErrorEvent evt(connection, "Invalid connection handle.");
      m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onError), 0, evt);
   }
}

void WebSocketServerManagerImpl::close(ConnectionHandle connection, CloseReason closeReason)
{
   websocketpp::connection_hdl hdl;
   bool found = false;
   for (auto con : mConnections)
   {
      if (con.second == connection)
      {
         hdl = con.first;
         found = true;
         break;
      }
   }

   if (found)
   {
      websocketpp::lib::error_code ec;
      websocketpp::close::status::value reason;

      switch (closeReason)
      {
      case CloseReason_Unsupported:
         reason = websocketpp::close::status::unsupported_data;
         break;
      case CloseReason_Normal:
      default:
         reason = websocketpp::close::status::normal;
         break;
      }

      mServer.close(hdl, reason, websocketpp::close::status::get_string(reason), ec);
      if (ec)
      {
         std::stringstream err;
         err << "Close failed: " << ec.message();
         ErrorEvent evt(connection, err.str().c_str());
         DebugLog(<< "WebSocketServerManagerImpl::close: " << hdl.lock().get() << "[" << connection << "] " << err.str().c_str());
         m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onError), 0, evt);
         return;
      }
   }
   else
   {
      DebugLog(<< "WebSocketServerManagerImpl::close: Invalid connection handle.");
      ErrorEvent evt(connection, "Invalid connection handle.");
      m_if->fireWebSocketServerEvent(cpcFunc(WebSocketServerHandler::onError), 0, evt);
   }


}

CPCAPI2::PhoneInterface* WebSocketServerManagerImpl::getPhoneInterface() const
{
   return m_if->phoneInterface();
}

} //namespace WebSocketServer
} //namespace CPCAPI2

#endif // CPCAPI2_BRAND_WEBSOCKET_SERVER_MODULE
