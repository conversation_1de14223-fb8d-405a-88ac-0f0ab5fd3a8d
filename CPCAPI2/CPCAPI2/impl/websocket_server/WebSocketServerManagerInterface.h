#pragma once

#if !defined(CPCAPI2_WEBSOCKET_SERVER_MANAGER_INTERFACE_H)
#define CPCAPI2_WEBSOCKET_SERVER_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "websocket_server/WebSocketServerManager.h"
#include "websocket_server/WebSocketServerSyncHandler.h"
#include "phone/PhoneModule.h"
#include "phone/Cpcapi2EventSource.h"

namespace CPCAPI2
{
class PhoneInterface;

namespace WebSocketServer
{
class WebSocketServerManagerImpl;

class WebSocketServerManagerInterface : public WebSocketServerManager,
                                        public PhoneModule,
                                        public CPCAPI2::EventSource<WebSocketServerHandle, WebSocketServerHandler, WebSocketServerSyncHandler>
{
public:
   WebSocketServerManagerInterface(Phone* phone);
   virtual ~WebSocketServerManagerInterface();

   virtual void Release() OVERRIDE;
   
   //WebSocketServerManager
   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE;
   virtual void setCallbackHook(void(*cbHook)(void*), void* context) OVERRIDE;
   virtual int setHandler(WebSocketServerHandler* handler) OVERRIDE;
   virtual int start(WebSocketServerSettings* settings = NULL) OVERRIDE;
   virtual int stop() OVERRIDE;
   virtual int send(ConnectionHandle connectionID, const cpc::string& data) OVERRIDE;
   virtual int close(ConnectionHandle connection, CloseReason closeReason) OVERRIDE;
   
   PhoneInterface* phoneInterface();

   template<typename TFn, typename TEvt>
   void fireWebSocketServerEvent(const char* funcName, TFn func, WebSocketServerHandle handle, const TEvt& args)
   {
      fireEvent(funcName, func, handle, args);
   }

private:
   void startImpl(WebSocketServerSettings* settings);
   void stopImpl();
   void sendImpl(int connectionID, const cpc::string& data);
   void closeImpl(int connectionID, const CloseReason closeReason);

private:
   PhoneInterface* mPhone;
   std::unique_ptr<WebSocketServerManagerImpl> mImpl;
};

}
}

#endif // CPCAPI2_WEBSOCKET_SERVER_MANAGER_INTERFACE_H
