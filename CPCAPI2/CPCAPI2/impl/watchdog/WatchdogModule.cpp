#include <brand_branded.h>


#if (CPCAPI2_BRAND_WATCHDOG_MODULE == 1)
#include "watchdog/WatchdogManagerInterface.h"
#endif

#include "phone/PhoneInterface.h"
#include "watchdog/WatchdogManager.h"

using namespace CPCAPI2;
using namespace CPCAPI2::Watchdog;

WatchdogManager* WatchdogManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_WATCHDOG_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<WatchdogManagerInterface>(phone, "WatchdogManager");
#else
   return NULL;
#endif
}
