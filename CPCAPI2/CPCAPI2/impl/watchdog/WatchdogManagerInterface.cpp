#include "watchdog/WatchdogManagerInterface.h"
#include "util/cpc_thread.h"
#include "MemoryDumpGenerator.h"

#include "phone/PhoneInterface.h"

#include <rutil/Logger.hxx>
#include <rutil/Timer.hxx>

#include "../util/cpc_logger.h"
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WATCHDOG

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

using namespace CPCAPI2::Watchdog;

static const int kSleepIntervalMs = 500;
static const int kQueryIterations = 1;
static const int kBackoffQueryIterations = 20;
static const int kResponseLimitIterations = 10;
static const int kResponseLimitMs = kResponseLimitIterations * kSleepIntervalMs;


WatchdogManagerInterface::WatchdogManagerInterface(CPCAPI2::Phone* phone) :
   mTargetReactor(dynamic_cast<CPCAPI2::PhoneInterface*>(phone)->getSdkModuleThread()),
   mResponsePending(false),
   mShutdown(false),
   mHandler(NULL),
   mReactor(NULL)
{
}

void WatchdogManagerInterface::Release()
{
   if (mReactor)
   {
      mShutdown = true;
      mReactor->destroy(); // will delete mReactor on thread exit
      mReactor = NULL;
   }
   else
   {
      delete this;
   }
}

int WatchdogManagerInterface::configureSettings(const WatchdogSettings& settings)
{
   mSettings = settings;

   return kSuccess;
}

int WatchdogManagerInterface::startThreadWatchdog()
{
   if (!mReactor)
   {
      InfoLog(<< "Starting watchdog");
      mReactor = new resip::MultiReactor("CPCAPI2_Watchdog");
      mReactor->start();
      mReactor->post(resip::resip_bind(&WatchdogManagerInterface::reactorThread, this));
   }
   else
   {
      InfoLog(<< "Watchdog already started");
   }
   return kSuccess;
}

int WatchdogManagerInterface::setHandler(WatchdogHandler* handler)
{
   // setHandler is on an internal only interface; and only used by the unit tests at this point
   // currently setHandler is not thread safe

   mHandler = handler;

   return kSuccess;
}

int WatchdogManagerInterface::reactorThread()
{
   mResponsePending = false; // whether or not we're currently waiting for mTargetReactor to respond with our post

   int iterationsRemainingUntilQuery = kQueryIterations;
   int iterationsRemainingUntilAlert = kResponseLimitIterations;
   for (;;)
   {
      if (mShutdown)
      {
         break;
      }

      {
         resip::Lock lock(mMutex);

         if (mResponsePending)
         {
            if (--iterationsRemainingUntilAlert == 0)
            {
               doAlert();

               // to avoid immediately making another dump, wait a bit
               iterationsRemainingUntilQuery = kBackoffQueryIterations;
            }
         }
         else
         {
            if (--iterationsRemainingUntilQuery == 0)
            {
               iterationsRemainingUntilQuery = kQueryIterations;

               sendTargetReactorQuery();

               mResponsePending = true;
               iterationsRemainingUntilAlert = kResponseLimitIterations;
            }
         }
      }

      // sleep is used to time responses such that if the entire process is suspended for any
      // reason (i.e. the current process receives no CPU time), we won't think the target reactor is stuck.
      // this is as opposed to using wall-clock based timing, where we would be very susceptible to false
      // positives in the target reactor being stuck.

      UInt64 t1 = resip::Timer::getTimeMs();
      CPCAPI2::this_thread::sleep_for(chrono::milliseconds(kSleepIntervalMs));
      UInt64 t2 = resip::Timer::getTimeMs();

      if (t2 - t1 > kSleepIntervalMs + 4000)
      {
         WarningLog(<< "Watchdog sleep should have taken " << kSleepIntervalMs << "ms "
                    << "but took " << t2 - t1 << "ms");
      }
   }

   return kSuccess;
}

int WatchdogManagerInterface::sendTargetReactorQuery()
{
   resip::ReadCallbackBase* f = resip::resip_bind(&WatchdogManagerInterface::targetReactorResponse, this);
   mTargetReactor.post(f);

   return kSuccess;
}

int WatchdogManagerInterface::targetReactorResponse()
{
   // WARNING: runs on mTargetReactor's thread

   resip::Lock lock(mMutex);
   mResponsePending = false;

   return kSuccess;
}

int WatchdogManagerInterface::doAlert()
{
   if (mHandler)
   {
      mHandler->threadAlert();
   }

   WarningLog(<< "Detected target reactor " << mTargetReactor.getThreadName()
              << " not responding longer than limit of "
              << kResponseLimitMs << " ms");

   // currently only supported on mac
#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || !(TARGET_OS_IPHONE)) && !defined(CPCAPI2_AUTO_TEST)
   MemoryDumpGenerator::dumpCurrentProcess(mSettings.dumpFolder.c_str());

#endif

   return kSuccess;
}
