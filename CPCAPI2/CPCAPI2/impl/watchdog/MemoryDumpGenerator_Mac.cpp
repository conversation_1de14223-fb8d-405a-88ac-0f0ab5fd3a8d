#include "MemoryDumpGenerator.h"

#if defined(__APPLE__)
#include "TargetConditionals.h"
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)

#include <unistd.h>
#include <sstream>
#include <rutil/Logger.hxx>
#include <rutil/Random.hxx>
#include <boost/algorithm/string.hpp>

#include <assert.h>
#include <stdbool.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/sysctl.h>

#include "../util/cpc_logger.h"
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::WATCHDOG


using namespace CPCAPI2::Watchdog;


// https://developer.apple.com/library/content/qa/qa1361/_index.html
static bool AmIBeingDebugged(void)
    // Returns true if the current process is being debugged (either 
    // running under the debugger or has a debugger attached post facto).
{
    //int                 junk;
    int                 mib[4];
    struct kinfo_proc   info;
    //size_t              size;

    // Initialize the flags so that, if sysctl fails for some bizarre 
    // reason, we get a predictable result.

    info.kp_proc.p_flag = 0;

    // Initialize mib, which tells sysctl the info we want, in this case
    // we're looking for information about a specific process ID.

    mib[0] = CTL_KERN;
    mib[1] = KERN_PROC;
    mib[2] = KERN_PROC_PID;
    mib[3] = getpid();

    // Call sysctl.

    //size = sizeof(info);
    //junk = sysctl(mib, sizeof(mib) / sizeof(*mib), &info, &size, NULL, 0);
    //assert(junk == 0);

    // We're being debugged if the P_TRACED flag is set.

    return ( (info.kp_proc.p_flag & P_TRACED) != 0 );
}

void MemoryDumpGenerator::dumpCurrentProcess(std::string dumpFolder)
{
   if (AmIBeingDebugged())
   {
      // have seen the process freeze from the dump and take upwards of 40 seconds if the debugger is attached to the process
      WarningLog(<< "Watchdog will NOT dump process due to active debugger detected; would hang process");
      return;
   }

   const pid_t currentProcessId = getpid();
   const int sampleDurationSecs = 2;
   
   
   boost::replace_all(dumpFolder, " ", "\\ ");
   
   std::stringstream ss;
   
   // postfix with & to run the command in the background, such that the call so system(..) does not block
   ss << "sample " << currentProcessId << " " << sampleDurationSecs << " ";
   if (!dumpFolder.empty())
   {
      std::stringstream dumpPath;
      dumpPath << dumpFolder << "/watchdogSample_" << resip::Random::getCryptoRandomHex(6) << ".txt";
   
      ss << "-file " << dumpPath.str() << " ";
   }

   ss << "&";
   
   
   DebugLog(<< "Watchdog will execute: " << ss.str());
   
   // example of dump analysis (the output of the 'sample' app appears very similar to the 'spindump' app):
   // http://stackoverflow.com/questions/10709577/spindump-analysis-instructions
   system(ss.str().c_str());
}

#endif
#endif // #if defined( __APPLE__ )