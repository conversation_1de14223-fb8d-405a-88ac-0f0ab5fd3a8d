#pragma once
#ifndef WatchdogManagerInterface_hpp
#define WatchdogManagerInterface_hpp

#include "WatchdogManagerInt.h"
#include <phone/PhoneModule.h>
#include <rutil/MultiReactor.hxx>


namespace CPCAPI2
{
namespace Watchdog
{
   class WatchdogManagerInterface :
      public WatchdogManagerInt,
      public PhoneModule
   {
   public:
      WatchdogManagerInterface(Phone* phone);
   
      int configureSettings(const WatchdogSettings&) OVERRIDE;
      
      int startThreadWatchdog() OVERRIDE;
      
      // WatchdogManagerInt
      int setHandler(WatchdogHandler*) OVERRIDE;
      
      // PhoneModule
      void Release() OVERRIDE;
      
   private:
      resip::MultiReactor* mReactor;
      resip::MultiReactor& mTargetReactor;
      
      int reactorThread();
      int sendTargetReactorQuery();
      int targetReactorResponse();
      int doAlert();
      
      bool mResponsePending;
      bool mShutdown;
      resip::Mutex mMutex;
      WatchdogSettings mSettings;
      
      WatchdogHandler* mHandler;
   
   };
}
}


#endif /* WatchdogManagerInterface_hpp */
