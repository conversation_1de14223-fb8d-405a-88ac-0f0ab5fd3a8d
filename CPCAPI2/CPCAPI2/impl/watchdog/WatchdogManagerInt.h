#pragma once
#ifndef WATCHDOG_MANAGER_INT_h
#define WATCHDOG_MANAGER_INT_h

#include "watchdog/WatchdogManager.h"

namespace CPCAPI2
{
namespace Watchdog
{
   class WatchdogHandler
   {
   public:
      virtual int threadAlert() = 0;
   };

   class CPCAPI2_SHAREDLIBRARY_API WatchdogManagerInt : public WatchdogManager
   {
   public:
      // WatchdogManager
      virtual int configureSettings(const WatchdogSettings&) = 0;
      virtual int startThreadWatchdog() = 0;
      
      // WatchdogManagerInt
      virtual int setHandler(WatchdogHandler*) = 0;
      
   protected:
      /*
       * The SDK will manage memory life of %WatchdogManagerInt.
       */
      virtual ~WatchdogManagerInt() {}
   };
}
}



#endif /* WATCHDOG_MANAGER */
