#include "log/FileLog.h"

#include "spdlog/sinks/rotating_file_sink.h"

#include "rutil/Random.hxx"
#include "rutil/Log.hxx"
#include "util/FileUtils.h"
#include "util/LogSubsystems.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace CPCAPI2;
namespace spd = spdlog;

FileLog::FileLog(const std::string& _id, const std::string& dir)
{
   std::string id = _id;
   if (spdlog::get(id))
   {
      id = id + resip::Random::getRandomHex(8).c_str();
   }

   std::string directory = dir;
   if (!directory.empty())
      directory = "/logs";
   else
      directory = "logs";
   FileUtils::CreateDir(directory.c_str(), false);

   const int bytesPerMB = 1000000;

   DebugLog(<< "About to create a per user log for id " << id);
   
   // spdlog comment on rotating logs:
   // Rotate files:
   // log.txt -> log.1.txt
   // log.1.txt -> log.2.txt
   // log.2.txt -> log.3.txt
   // log.3.txt -> delete
   spdlog::sink_ptr sink = std::make_shared<spdlog::sinks::rotating_file_sink_st>(directory + id + ".log", 4 * bytesPerMB, 3);

   // custom pattern to avoid logging the id, since id is in the filename
   // https://github.com/gabime/spdlog/wiki/3.-Custom-formatting
   sink->set_pattern("[%D %H:%M:%S:%e][%^%L%$][thrd %t] %v");

   mLogger = std::unique_ptr<MyLogger>(new MyLogger(id, sink));
   
   // filtering performed in LocalLogger
   mLogger->set_level(spdlog::level::trace);
   // todo: can we more efficiently flush?
   mLogger->flush_on(spdlog::level::trace);
}

FileLog::~FileLog()
{
}

void FileLog::onLogMessage(LocalLogMessage& log)
{
   LogMessageImpl* logImpl = static_cast<LogMessageImpl*>(&log);

   spdlog::level::level_enum spdLevel = spdlog::level::debug;

   switch (log.getLevel())
   {
      case CPCAPI2::LogLevel_Max:
         spdLevel = spdlog::level::trace;
         break;
      case CPCAPI2::LogLevel_Debug:
         spdLevel =  spdlog::level::debug;
         break;
      case CPCAPI2::LogLevel_Info:
         spdLevel =  spdlog::level::info;
         break;
      case CPCAPI2::LogLevel_Warning:
         spdLevel =  spdlog::level::warn;
         break;
      case CPCAPI2::LogLevel_Error:
         spdLevel =  spdlog::level::err;
         break;
      case CPCAPI2::LogLevel_None:
         spdLevel =  spdlog::level::off;
         break;
   }
   
   mLogger->log(spdLevel, logImpl->mBuffer);
}
