#include "log/LocalLogger.h"

#include <rutil/Data.hxx>

using namespace CPCAPI2;
namespace spd = spdlog;

// The previous LocalLogger implementation grew too complex and buggy with ability to support using an external
// logger thread and not. 

LocalLogger::LocalLogger()
{
}

LocalLogger::~LocalLogger()
{
}

void LocalLogger::initialize(resip::MultiReactor* loggerThread, LocalLoggerHandler* handler)
{
}

void LocalLogger::release()
{
   delete this;
}

void LocalLogger::updateReactor( resip::MultiReactor* loggerThread )
{
}

void LocalLogger::setLogLevel(CPCAPI2::LogLevel level)
{
}

void LocalLogger::handleMessage(LogMessageImpl& log)
{
}
