
#ifndef LocalLoggerImpl_h
#define LocalLoggerImpl_h

#if !defined(_WIN32) && !defined(ANDROID)
#include <mm_malloc.h>
#endif

#include <string>
#include <sstream>
#include <stdio.h>

#define FMT_HEADER_ONLY 1
#define FMT_USE_RVALUE_REFERENCES 1
#include <fmt/format.h>
#include <fmt/ostream.h> // must be included to support printing custom types

#ifdef __clang__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wreturn-stack-address"
#endif

#define SPDLOG_FMT_EXTERNAL 1
#include <spdlog/spdlog.h>

#ifdef __clang__
#pragma GCC diagnostic pop
#endif

#include <contrib/folly/ProducerConsumerQueue.h>
#include <rutil/MultiReactor.hxx>
#include "log/LocalLoggerHandler.h"

#define LocalMaxLog(fmt_, ...)                                                         \
LocalGenericLog(RESIPROCATE_SUBSYSTEM, CPCAPI2::LogLevel_Max, fmt_, ##__VA_ARGS__)

#define LocalDebugLog(fmt_, ...) \
LocalGenericLog(RESIPROCATE_SUBSYSTEM, CPCAPI2::LogLevel_Debug, fmt_, ##__VA_ARGS__)

#define LocalInfoLog(fmt_, ...) \
LocalGenericLog(RESIPROCATE_SUBSYSTEM, CPCAPI2::LogLevel_Info, fmt_, ##__VA_ARGS__)

#define LocalWarningLog(fmt_, ...) \
LocalGenericLog(RESIPROCATE_SUBSYSTEM, CPCAPI2::LogLevel_Warning, fmt_, ##__VA_ARGS__)

#define LocalErrLog(fmt_, ...) \
LocalGenericLog(RESIPROCATE_SUBSYSTEM, CPCAPI2::LogLevel_Error, fmt_, ##__VA_ARGS__)


#define LOCALLOG_STR_H(x) #x
#define LOCALLOG_STR_HELPER(x) LOCALLOG_STR_H(x)

#ifdef __GNUC__
//#define CPCPAI2_BASE_FILENAME __BASE_FILE__
// .jza. doesn't seem to be any good simple compile time way of getting filename without path
#define CPCPAI2_BASE_FILENAME
#else
#define CPCPAI2_BASE_FILENAME
#endif

// do/while allows a {} block in an expression
#define LocalGenericLog(system_, level_, fmt_, ...)                                    \
   do                                                                                  \
   {                                                                                   \
      if (CP_LOCAL_LOGGER_VAR)                                                         \
      {                                                                                \
         CP_LOCAL_LOGGER_VAR->logMessage(level_, "[" CPCPAI2_BASE_FILENAME             \
                                                 ":" LOCALLOG_STR_HELPER(__LINE__) "] "\
                                                 fmt_, ##__VA_ARGS__ );                \
      }                                                                                \
                                                                                       \
   } while (false)


namespace resip
{
   class Data;
}

namespace spdlog
{
   class logger;
}

namespace CPCAPI2
{
   template <typename T>
   inline const void *log_ptr(const T *p) { return p; }

   class LogMessageImpl : public LocalLogMessage
   {
   public:
      template<typename... Args>
      LogMessageImpl(CPCAPI2::LogLevel level, const char *fmt, const Args &... args) :
         mLevel(level)
      {
         // fmt::memory_buffer uses a memory buffer stored in the object itself for the first `fmt::inline_buffer_size` elements,
         // and only uses dynamic memory allocation if this size is exceeded. we limit the size of the formatted string we
         // put into the memory_buffer instance here, to avoid dynamic memory allocations.
         
         const size_t maxSizeToWrite = fmt::inline_buffer_size - 1;
         try
         {
            fmt::format_to_n_result<char*> res = fmt::format_to_n(mBuffer.data(), maxSizeToWrite, fmt, args...);
            
            const size_t sizeWritten = std::min(res.size, maxSizeToWrite);
         
            mBuffer.resize(sizeWritten + 1);
            mBuffer.data()[sizeWritten] = 0;
         }
         catch (fmt::format_error err)
         {
            // avoid trying to format again with fmtlib, to avoid another exception
         
            std::stringstream ss;
            ss << "LocalLog fmt error; unformatted line: " << fmt << ", fmtlib error: " << err.what();
            const std::string& s = ss.str();
            mBuffer.resize(s.size() + 1);
            strncpy(mBuffer.data(), s.c_str(), s.size());
            mBuffer.data()[s.size()] = 0;
            
            mLevel = CPCAPI2::LogLevel_Error;
         }
      }
      
      CPCAPI2::LogLevel mLevel;
      fmt::memory_buffer mBuffer;
      
      //// LogMessage
      
      const char* getMessage()
      {
         return mBuffer.data();
      }
      
      CPCAPI2::LogLevel getLevel()
      {
         return mLevel;
      }

   };

   class LocalLogger
   {
   public:
      LocalLogger();
      virtual ~LocalLogger();

      void* operator new(size_t i)
      {
#if defined(ANDROID)
         return memalign(folly::hardware_destructive_interference_size, i);
#else
         return _mm_malloc(i, folly::hardware_destructive_interference_size);
#endif
      }

      void operator delete(void* p)
      {
#if defined(ANDROID)
         free(p);
#else
         _mm_free(p);
#endif
      }

      void initialize(resip::MultiReactor* loggerThread, LocalLoggerHandler* handler);
      void release();

      // NB: can be updated to NULL during shutdown
      void updateReactor( resip::MultiReactor* loggerThread );

      void setLogLevel(CPCAPI2::LogLevel level);

      template<typename... Args>
      void logErrorMessage(const char *fmt, const Args &... args)
      {
         logMessage(CPCAPI2::LogLevel_Error, fmt, args...);
      }

      template<typename... Args>
      void logWarningMessage(const char *fmt, const Args &... args)
      {
         logMessage(CPCAPI2::LogLevel_Warning, fmt, args...);
      }

      template<typename... Args>
      void logInfoMessage(const char *fmt, const Args &... args)
      {
         logMessage(CPCAPI2::LogLevel_Info, fmt, args...);
      }
      
      template<typename... Args>
      void logDebugMessage(const char *fmt, const Args &... args)
      {
         logMessage(CPCAPI2::LogLevel_Debug, fmt, args...);
      }
      
      template<typename... Args>
      void logMaxMessage(const char *fmt, const Args &... args)
      {
         logMessage(CPCAPI2::LogLevel_Max, fmt, args...);
      }
      
      template<typename... Args>
      void logMessage(CPCAPI2::LogLevel logLevel, const char *fmt, const Args &... args)
      {
      }
      
   private:
   
      bool shouldLog(CPCAPI2::LogLevel inLogLevel)
      {
         return false;
      }
   
   
      void handleMessage(LogMessageImpl& log);

   private:
   };
}



#endif /* LocalLoggerImpl_h */
