#ifndef FileLog_h
#define FileLog_h

#include <string>
#include "log/LocalLogger.h"

namespace spdlog
{
   class logger;
}

namespace CPCAPI2
{

   class FileLog : public LocalLoggerHandler
   {
   public:
      FileLog(const std::string& id, const std::string& dir);
      virtual ~FileLog();
   
   private:
      void onLogMessage(LocalLogMessage&);

      // subclass spdlog::logger so we can create a log method which uses std::move to avoid extra string copy
      
      class MyLogger : public spdlog::logger
      {
      public:

         MyLogger(const std::string &name, spdlog::sink_ptr single_sink) :
            spdlog::logger(name, single_sink)
         {
         }

         template<typename... Args>
         void log(spdlog::level::level_enum lvl, fmt::memory_buffer& buffer)
         {
            // we already check in LocalLogger
            //if (!should_log(lvl))
            //{
            //   return;
            //}

            assert(0);
            // todo: fix for newer spdlog version
            //try
            //{
            //   spdlog::details::log_msg log_msg(&name_, lvl);
            //   log_msg.raw = std::move(buffer);
            //   sink_it_(log_msg);
            //}
            //SPDLOG_CATCH_AND_HANDLE
         }
      };
      

   private:
      std::unique_ptr<MyLogger> mLogger;
   };
}


#endif /* FileLog_h */
