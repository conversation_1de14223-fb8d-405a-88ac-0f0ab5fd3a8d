#include "brand_branded.h"

#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)

#include "WatcherInfoManagerInterface.h"
#include "WatcherInfoSubscriptionHandlerImpl.h"

#include <libxml/tree.h>
#include <libxml/xmlreader.h>
#include <libxml/xmlwriter.h>
#include <libxml/parser.h>

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::XCAP;

namespace CPCAPI2
{
namespace WatcherInfo
{

WatcherInfoManagerInterface::WatcherInfoManagerInterface(Phone* phone) :
   CPCAPI2::EventSource2< CPCAPI2::EventHandler<WatcherInfoSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
   mAccountIf(NULL),
   mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
   xcapModule = dynamic_cast<XcapInternalInterface*>(XcapInternalInterface::getInterface(phone));
}

void WatcherInfoManagerInterface::Release()
{
   delete this;
}

int WatcherInfoManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, WatcherInfoSubscriptionHandler* handler)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::setHandlerImpl, this, account, handler));
   return kSuccess;
}

int WatcherInfoManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, WatcherInfoSubscriptionHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   WatcherInfoSubscriptionHandlerImpl* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);

   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for WatcherInfoManager::setHandler");
      return kError;
   }

   if (acct->isEnabled())
   {
      mAccountIf->fireError("WatcherInfoManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new WatcherInfoSubscriptionHandlerImpl(*acct, *mSipEventIf, *this);
         mAccountMap[account] = evtMan;
      }

      auto it = mHandlers.find(account);
      if (mHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }

      mHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }

      // do this synchronously (we're already in the SDK thread)
      mSipEventIf->setHandlerImpl(account, WINFO_PACKAGE, evtMan);
   }
   return kSuccess;
}
   
int WatcherInfoManagerInterface::removeHandler(CPCAPI2::SipAccount::SipAccountHandle account)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&WatcherInfoManagerInterface::removeHandlerImpl, this, account);
   executeOnSdkThread(f);
   mPhone->process(-1);

   return kSuccess;
}

int WatcherInfoManagerInterface::removeHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account)
{
   AccountMap::iterator it = mAccountMap.find(account);
   WatcherInfoSubscriptionHandlerImpl* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for WatcherInfoManager::removeHandler");
      return kError;
   }
   
   if (evtMan == NULL)
   {
      mAccountIf->fireError("Could not remove existing handle for WatcherInfoManager::removeHandler. Was setHandler not called previously?");
      return kError;
   }

   return kSuccess;
}

int WatcherInfoManagerInterface::setXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings)
{
   mXcapSettingsMap.insert(std::pair<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapSettings>(account, xcapSettings));
   xcapModule->readXcapCapability(xcapSettings);
   //postToSdkThread(resip::resip_bind(&XcapInternalInterface::readXcapCapability, xcapModule, xcapSettings));
   return kSuccess;
}

WatcherInfoSubscriptionHandle WatcherInfoManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   return mSipEventIf->createSubscription(account);
}

int WatcherInfoManagerInterface::applySubscriptionSettings(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventSubscriptionSettings& settings)
{
   // Support for the GENBAND "X-nt-eow" extension
   if (settings.eow)
   {
      mEowSubscriptions.insert(subscription);
      mSipEventIf->addSupportedEowHeader(subscription);
   }
   
   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = WINFO_PACKAGE;
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(MimeType(WINFO_MIME_TYPE, WINFO_MIME_SUBTYPE));
   return mSipEventIf->applySubscriptionSettings(subscription, eventSettings);
}

int WatcherInfoManagerInterface::addParticipant(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->addParticipant(subscription, targetAddress);
}

int WatcherInfoManagerInterface::setEventServer(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->setEventServer(subscription, targetAddress);
}

int WatcherInfoManagerInterface::start(WatcherInfoSubscriptionHandle subscription)
{
   return mSipEventIf->start(subscription);
}

int WatcherInfoManagerInterface::end(WatcherInfoSubscriptionHandle subscription)
{
   return mSipEventIf->end(subscription);
}

int WatcherInfoManagerInterface::refresh(WatcherInfoSubscriptionHandle subscription)
{
   return mSipEventIf->refresh(subscription);
}

int WatcherInfoManagerInterface::notify(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::notifyImpl, this, subscription, eventState));
   return kSuccess;
}

int WatcherInfoManagerInterface::notifyImpl(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState)
{
   SipEventState newEventState;
   cpc::string subscriber = "";
   cpc::string content = "";

   newEventState.eventPackage = eventState.eventPackage;    // presence.mWinfoMap
   newEventState.expiresTimeMs = eventState.expiresTimeMs;
   newEventState.mimeType = eventState.mimeType;            // application
   newEventState.mimeSubType = eventState.mimeSubType;      // watcherinfo+xml
   content = convertWinfoToUtf8(eventState.winfo);
   newEventState.contentUTF8 = content.c_str();
   newEventState.contentLength = content.size();

   return mSipEventIf->notify(subscription, newEventState);
}

/**
* Used to accept an incoming (server) subscription session (200 OK).
*/
int WatcherInfoManagerInterface::accept(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::acceptImpl, this, subscription, eventState));
   return kSuccess;
}

int WatcherInfoManagerInterface::acceptImpl(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState)
{
   SipEventState newEventState;
   cpc::string content;

   newEventState.eventPackage = eventState.eventPackage;
   newEventState.expiresTimeMs = eventState.expiresTimeMs;
   newEventState.mimeType = eventState.mimeType;
   newEventState.mimeSubType = eventState.mimeSubType;
   content = convertWinfoToUtf8(eventState.winfo);
   newEventState.contentLength = content.size();
   newEventState.contentUTF8 = content.c_str();
   mSipEventIf->accept(subscription, newEventState);
   return kSuccess;
}

int WatcherInfoManagerInterface::reject(WatcherInfoSubscriptionHandle subscription, unsigned int rejectReason)
{
   mSipEventIf->reject(subscription, rejectReason);
   return kSuccess;
}

int WatcherInfoManagerInterface::setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account)
{
   cpc::string presRulesXml = generatePresRulesDoc();
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::setPresenceAuthenticationRulesImpl, this, account, presRulesXml));
   return kSuccess;
}

int WatcherInfoManagerInterface::setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& presRulesXml)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::setPresenceAuthenticationRulesImpl, this, account, presRulesXml));
   return kSuccess;
}

int WatcherInfoManagerInterface::setPresenceAuthenticationRulesImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& presRulesXml)
{
   CPCAPI2::XCAP::XCAPResult result;
   XcapSettings xcapSettings;
   XcapRequestComponents requestComp;

   if(getXcapSettings(account, xcapSettings))
   {
      requestComp.auid = PRES_RULES_AUID;
      requestComp.contentType = PRES_RULES_MIME;
      requestComp.documentSelector = "index";
      requestComp.isGlobal = false;
      requestComp.nodeSelector = "";

      result = xcapModule->add(xcapSettings, requestComp, presRulesXml);
      if( (result.errorCode > 399) || (result.errorCode < 100) )
      {
         cpc::string msg = cpc::string("WatcherInfoManagerInterface::setPresenceAuthenticationRulesImpl. Unable to create file on xcap server. Write to xcap server failed with error code: " 
                              + cpc::to_string(result.errorCode) + "./n"
                              + "Error message: " + result.errorMsg.c_str());
         mAccountIf->fireError(msg);
         return kError;
      }
   }
   return kSuccess;
}

int WatcherInfoManagerInterface::addWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::addWatcherImpl, this, account, uri));
   return kSuccess;
}

int WatcherInfoManagerInterface::addWatcherImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri)
{
   /*CPCAPI2::XCAP::XCAPResult result;
   XcapSettings xcapSettings;
   XcapRequestComponents requestComp;
   cpc::string data = "<one id=\"" + uri + "\"/>";
   cpc::string nodeSelector = "";

   if(getXcapSettings(account, xcapSettings))
   {
      nodeSelector = "ruleset/rule[@id=\"whitelist\"]/conditions/identity/one[@id=\"";
      nodeSelector.append(uri);
      nodeSelector.append("\"]");

      requestComp.auid = PRES_RULES_AUID;
      requestComp.contentType = XCAP_ELEMENT_MIME;
      requestComp.documentSelector = "index";
      requestComp.isGlobal = false;
      requestComp.nodeSelector = nodeSelector.c_str();

      result = xcapModule->add(xcapSettings, requestComp, data);
      if( (result.errorCode > 399) || (result.errorCode < 100) )
      {
         cpc::string msg = cpc::string("WatcherInfoManagerInterface::addWatcherImpl. Write to xcap server failed with error code: " 
                              + cpc::to_string(result.errorCode) + "./n"
                              + "Error message: " + result.errorMsg.c_str());
         mAccountIf->fireError(msg);
         return kError;
      }
   }*/
   CPCAPI2::XCAP::XCAPResult result;
   XcapSettings xcapSettings;
   XcapRequestComponents requestComp;
   cpc::string data = "";

   presenceWhitelist.insert(uri);
   return kSuccess;
}

int WatcherInfoManagerInterface::removeWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri)
{
   postToSdkThread(resip::resip_bind(&WatcherInfoManagerInterface::removeWatcherImpl, this, account, uri));
   return kSuccess;
}

int WatcherInfoManagerInterface::removeWatcherImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri)
{
   
   cpc::string data = "";
   std::set<cpc::string>::iterator it;

   it = presenceWhitelist.find(uri);
   if(it == presenceWhitelist.end())
   {
      return kSuccess;
   }
   presenceWhitelist.erase(it);
   return kSuccess;
}

cpc::string WatcherInfoManagerInterface::generatePresRulesDoc()
{
   std::set<cpc::string>::iterator it;
   cpc::string presRulesXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>";

   presRulesXml.append("<ruleset xmlns=\"urn:ietf:params:xml:ns:common-policy\" xmlns:pr=\"urn:ietf:params:xml:ns:pres-rules\" ><rule id=\"whitelist\"><conditions><identity>");
   if(presenceWhitelist.size()>0)
   {
      for (it=presenceWhitelist.begin(); it!=presenceWhitelist.end(); ++it)
      {
         presRulesXml.append("<one id=\"");
         presRulesXml.append(*it);
         presRulesXml.append("\"/>");
      }
   }
   else
   {
      presRulesXml.append("<many/>");
   }
   presRulesXml.append("</identity></conditions>");
   presRulesXml.append(PRES_RULES_ACTIONS);
   presRulesXml.append(PRES_RULES_TRANSFORMATIONS);
   presRulesXml.append("</rule></ruleset>");
   return presRulesXml;
}

int WatcherInfoManagerInterface::initializeWatcherInfo(CPCAPI2::SipAccount::SipAccountHandle account)
{
   CPCAPI2::XCAP::XcapRequestComponents docPathComponents = {"index","","watchers","application/watcherinfo+xml", true};
   return loadWatcherInfo(account, docPathComponents);
}

int WatcherInfoManagerInterface::loadWatcherInfo(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapRequestComponents& docPathComponents)
{
   CPCAPI2::XCAP::XcapSettings xcapSettings;
   if(!getXcapSettings(account, xcapSettings))
   {
      return kSuccess;
   }

   CPCAPI2::XCAP::XCAPResult result = xcapModule->read(xcapSettings, docPathComponents);
   xmlDocPtr xmlWinfo = NULL;
   xmlTextReaderPtr xmlReader;
   if( (result.errorCode > 399) || (result.errorCode < 100) )
   {
      cpc::string msg = cpc::string("WatcherInfoManagerInterface::loadWatcherInfo. Unable to read data from xcap server. Read from xcap server failed with error code: " 
                              + cpc::to_string(result.errorCode) + "./n"
                              + "Error message: " + result.errorMsg.c_str());
      mAccountIf->fireError(msg);
      return kError;
   }
   if((xmlReader = xmlReaderForMemory(result.result.data(), (int)result.result.size(), NULL, "UTF-8", 128)))
   {
      WatcherInformation localWinfo = parseXmlWinfo(xmlReader);
      mWinfoMap.insert(std::pair<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::WatcherInfo::WatcherInformation>(account, localWinfo));
   }
   else
   {
      return kError;
   }
   return kSuccess;
}
   
WatcherInformation WatcherInfoManagerInterface::parseXmlWinfo(xmlTextReaderPtr xmlWinfo)
{
   WatcherInformation localWinfo;
   xmlChar watcherInfoNodeName[12] = {'w','a','t','c','h','e','r','i','n','f','o','\0'};
   xmlChar watcherListNodeName[13] = {'w','a','t','c','h','e','r','-','l','i','s','t','\0'};
   xmlChar watcherNodeName[8] = {'w','a','t','c','h','e','r','\0'};
   xmlChar watcherVersionAttribName[8] = {'v','e','r','s','i','o','n','\0'};
   xmlChar watcherStateAttribName[6] = {'s','t','a','t','e','\0'};
   xmlChar watcherEowAttribName[4] = {'e','o','w','\0'};
   xmlChar watcherResourceAttribName[9] = {'r','e','s','o','u','r','c','e','\0'};
   xmlChar watcherPackageAttribName[8] = {'p','a','c','k','a','g','e','\0'};
   xmlChar watcherStatusAttribName[7] = {'s','t','a','t','u','s','\0'};
   xmlChar watcherIdAttribName[3] = {'i','d','\0'};
   xmlChar watcherExpirationAttribName[11] = {'e','x','p','i','r','a','t','i','o','n','\0'};
   xmlChar watcherDurationSubAttribName[20] = {'d','u','r','a','t','i','o','n','-','s','u','b','s','c','r','i','b','e','d','\0'};
   xmlChar watcherDisplayNameAttribName[13] = {'d','i','s','p','l','a','y','-','n','a','m','e','\0'};
   xmlChar watcherEventAttribName[6] = {'e','v','e','n','t','\0'};
   WatcherList* currentWatcherList = NULL;
   Watcher* currentWatcher = NULL;
   
   while (xmlTextReaderRead(xmlWinfo))
   {
      xmlChar* nodeName = xmlTextReaderName(xmlWinfo);
      if ((xmlStrcmp(nodeName, watcherInfoNodeName) == 0) && (xmlTextReaderNodeType(xmlWinfo) == XML_READER_TYPE_ELEMENT))
      {
         xmlChar* attrVal;
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherVersionAttribName);
         if (attrVal != NULL)
         {
            localWinfo.version = atoi((char*)attrVal);
            xmlFree(attrVal);
         }

         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherStateAttribName);
         if (attrVal != NULL)
         {
            localWinfo.state = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherEowAttribName);
         if (attrVal != NULL)
         {
            localWinfo.eow = !strcmp((char*)attrVal, "true");
            xmlFree(attrVal);
         }
      }
      else if ((xmlStrcmp(nodeName, watcherListNodeName) == 0) && (xmlTextReaderNodeType(xmlWinfo) == XML_READER_TYPE_ELEMENT))
      {
         currentWatcherList = new WatcherList();
         
         xmlChar* attrVal;
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherResourceAttribName);
         if (attrVal != NULL)
         {
            currentWatcherList->resource = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherPackageAttribName);
         if (attrVal != NULL)
         {
            currentWatcherList->package = (char*)attrVal;
            xmlFree(attrVal);
         }
      }
      else if ((xmlStrcmp(nodeName, watcherListNodeName) == 0) && (xmlTextReaderNodeType(xmlWinfo) == XML_READER_TYPE_END_ELEMENT))
      {
         if (currentWatcherList)
         {
            localWinfo.watcherLists.push_back(*currentWatcherList);
            currentWatcherList = NULL;
         }
      }
      else if ((xmlStrcmp(nodeName, watcherNodeName) == 0) && (xmlTextReaderNodeType(xmlWinfo) == XML_READER_TYPE_ELEMENT))
      {
         currentWatcher = new Watcher();
         
         xmlChar* attrVal;
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherStatusAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->status = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherIdAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->id = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherDisplayNameAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->displayName = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherDurationSubAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->durationSubscribed = atoi((char*)attrVal);
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherExpirationAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->expiration = atoi((char*)attrVal);
            xmlFree(attrVal);
         }
         
         attrVal = xmlTextReaderGetAttribute(xmlWinfo, watcherEventAttribName);
         if (attrVal != NULL)
         {
            currentWatcher->event = (char*)attrVal;
            xmlFree(attrVal);
         }
         
         xmlChar* nodeValue;
         nodeValue = xmlTextReaderReadString(xmlWinfo);
         if (nodeValue != NULL)
         {
            currentWatcher->watcherURI = (char*)nodeValue;
            xmlFree(nodeValue);
         }
      }
      else if ((xmlStrcmp(nodeName, watcherNodeName) == 0) && (xmlTextReaderNodeType(xmlWinfo) == XML_READER_TYPE_END_ELEMENT))
      {
         if (currentWatcher && currentWatcherList)
         {
            currentWatcherList->watchers.push_back(*currentWatcher);
            currentWatcher = NULL;
         }
      }
      
      xmlFree(nodeName);
   }
   
   return localWinfo;
}

   
/*
 * This version of the parser works, but capturing the xmlDocPtr (previously done in convertUtf8ToWinfo) doesn't
 * seem to reliably get the full document. The replacement parseXmlWinfo(xmlTextReaderPtr wmlWinfo) method uses
 * the xmlTextReader to walk the document and retrieve the results.
 *
 */
WatcherInformation WatcherInfoManagerInterface::parseXmlWinfo(xmlDocPtr xmlWinfo)
{
   xmlAttrPtr docAttribute = xmlWinfo->children->properties;
   xmlAttrPtr watcherListAttribute = NULL;
   xmlAttrPtr watcherAttribute = NULL;
   xmlNodePtr watcherListsNode = xmlWinfo->children->children->next;
   xmlNodePtr winfoListNode = NULL;
   WatcherInformation localWinfo;
   WatcherList* currentWatcherList;
   Watcher* currentWatcher;
   cpc::string attribute;
   xmlChar watcherListNodeName[13] = {'w','a','t','c','h','e','r','-','l','i','s','t','\0'}; 
   xmlChar watcherNodeName[8] = {'w','a','t','c','h','e','r','\0'};

   while(docAttribute != NULL)
   {
      attribute = (char *)docAttribute->name;
      if(strcmp(attribute, WINFO_STATE_ATTRIB) == 0)
      {
         localWinfo.state = (char *)docAttribute->children->content;
      }
      else if(strcmp(attribute, WINFO_VERSION_ATTRIB) == 0)
      {
         localWinfo.version = atoi((char *)(docAttribute->children->content));
      }
      else if(strcmp(attribute, WINFO_EOW_ATTRIB) == 0)
      {
         char *eowVal = (char *)docAttribute->children->content;
         if(strcmp(eowVal, "true") == 0)
         {
            localWinfo.eow = true;
         }
         else
         {
            localWinfo.eow = false;
         }
      }
      docAttribute = docAttribute->next;
   }
   
   while(watcherListsNode != NULL)
   {
      if( xmlStrcmp(watcherListNodeName, watcherListsNode->name) == 0 )
      {
         currentWatcherList = new WatcherInfo::WatcherList();
         watcherListAttribute = watcherListsNode->properties;
         while(watcherListAttribute != NULL)
         {
            attribute = (char *)watcherListAttribute->name;
            if(strcmp(attribute, WINFO_RESOURCE_ATTRIB) == 0)
            {
               currentWatcherList->resource = (char *)watcherListAttribute->children->content;
            }
            else if(strcmp(attribute, WINFO_PACKAGE_ATTRIB) == 0)
            {
               currentWatcherList->package = (char *)watcherListAttribute->children->content;
            }
            watcherListAttribute = watcherListAttribute->next;
         }
         if(watcherListsNode->children != NULL)
         {
            winfoListNode = watcherListsNode->children->next;
         }
         while(winfoListNode != NULL)
         {
            if(xmlStrcmp(watcherNodeName, winfoListNode->name) == 0)
            {
               currentWatcher = new WatcherInfo::Watcher();
               watcherAttribute = winfoListNode->properties;
               while(watcherAttribute != NULL)
               {
                  attribute = (char *)watcherAttribute->name;
                  if(strcmp(attribute, WINFO_ID_ATTRIB) == 0)
                  {
                     currentWatcher->id = (char *)watcherAttribute->children->content;
                  }
                  else if(strcmp(attribute, WINFO_STATUS_ATTRIB) == 0)
                  {
                     currentWatcher->status = (char *)watcherAttribute->children->content;
                  }
                  else if(strcmp(attribute, WINFO_EVENT_ATTRIB) == 0)
                  {
                     currentWatcher->event = (char *)watcherAttribute->children->content;
                  }
                  else if(strcmp(attribute, WINFO_DISPLAY_NAME_ATTRIB) == 0)
                  {
                     currentWatcher->displayName = (char *)watcherAttribute->children->content;
                  }
                  else if(strcmp(attribute, WINFO_EXPIRATION_ATTRIB) == 0)
                  {
                     currentWatcher->expiration = atoi((char *)watcherAttribute->children->content);
                  }
                  else if(strcmp(attribute, WINFO_DURATION_SUB_ATTRIB) == 0)
                  {
                     currentWatcher->durationSubscribed = atoi((char *)watcherAttribute->children->content);
                  }
                  watcherAttribute = watcherAttribute->next;
               }
               if(winfoListNode->children != NULL)
               {
                  currentWatcher->watcherURI = (char *)winfoListNode->children->content;
               }
               currentWatcherList->watchers.push_back(*currentWatcher);
            }
            winfoListNode = winfoListNode->next;
         }
         localWinfo.watcherLists.push_back(*currentWatcherList);
      }
      watcherListsNode = watcherListsNode->next;
   }
   return localWinfo;
}

cpc::string WatcherInfoManagerInterface::convertWatcherToUtf8(const Watcher& watcherParam)
{
   cpc::string result = "<watcher";
   if(!watcherParam.displayName.empty())
   {
      result += "display-name=\"" + watcherParam.displayName + "\" ";
   }
   if(watcherParam.durationSubscribed != 0)
   {
      result += "duration-subscribed=\"" + cpc::to_string(watcherParam.durationSubscribed) + "\" ";
   }
   if(!watcherParam.event.empty())
   {
      result += "event=\"" + watcherParam.event + "\" ";
   }
   if(watcherParam.expiration != 0)
   {
      result += "expiration=\"" + cpc::to_string(watcherParam.expiration) + "\" ";
   }
   if(!watcherParam.id.empty())
   {
      result += "id=\"" + watcherParam.id + "\" ";
   }
   if(!watcherParam.status.empty())
   {
      result += "status=\"" + watcherParam.status + "\" ";
   }
   result += ">";
   result += watcherParam.watcherURI;
   result += "</watcher>";

   return result;
}

cpc::string WatcherInfoManagerInterface::convertWinfoListToUtf8(const WatcherList& listParam)
{
   cpc::string result = "<watcher-list resource=\"" + listParam.resource + "\" package=\"" + listParam.package + "\">";

   for(unsigned int i=0;i<listParam.watchers.size();i++)
   {
      result += convertWatcherToUtf8(listParam.watchers[i]);
   }
   result += "</watcher-list>";

   return result;
}

cpc::string WatcherInfoManagerInterface::convertWinfoToUtf8(const WatcherInformation& eventState)
{
   WatcherList currentList;
   
   cpc::string result = "<watcherinfo version=\"";
   result.append(cpc::to_string(eventState.version).c_str());
   result.append("\" state=\"");
   result.append(eventState.state);
   result.append("\">");

   for(unsigned int i=0;i<eventState.watcherLists.size();i++)
   {
      currentList = eventState.watcherLists[i];
      result += convertWinfoListToUtf8(currentList);
   }
   result += "</watcherinfo>";
   return result;
}

WatcherInformation* WatcherInfoManagerInterface::convertUtf8ToWinfo(const cpc::string& winfoString)
{
   WatcherInformation* winfoResult = new WatcherInformation();

   xmlTextReaderPtr xmlReader;

   if((xmlReader = xmlReaderForMemory(winfoString.c_str(), winfoString.size(), NULL, "UTF-8", 128)))
   {
      *winfoResult = parseXmlWinfo(xmlReader);
   }

   return winfoResult;
}

cpc::string WatcherInfoManagerInterface::createWinfoListNodeSelector(const cpc::string& resourceUri)
{
   cpc::string selector = "watcherinfo/watcher-list[@resource=\"";
   selector.append(resourceUri);
   selector.append("\"]");
   return selector;
}

cpc::string WatcherInfoManagerInterface::createWatcherNodeSelector(const cpc::string& resourceUri, const Watcher& watcherParam)
{
   cpc::string selector = createWinfoListNodeSelector(resourceUri);
   selector.append("/watcher[@id=\"");
   selector.append(watcherParam.id);
   selector.append("\"]");
   return selector;
}

int WatcherInfoManagerInterface::getWatcherList(CPCAPI2::SipAccount::SipAccountHandle account, 
                                                const cpc::string& package, 
                                                const cpc::string& resourceUri,
                                                WatcherInformation& winfoParam, 
                                                WatcherList& list)
{
   for(cpc::vector<WatcherList>::iterator it = winfoParam.watcherLists.begin();it != winfoParam.watcherLists.end();++it)
   {
      if( (strcmp(it->resource, resourceUri) == 0) && (strcmp(it->package, package) == 0) )
      {
         list = *it;
         return kSuccess;
      }
   }
   return kError;
}

bool WatcherInfoManagerInterface::getXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapSettings& settings)
{
   AccountXcapSettingsMap::iterator itXcapSettings = mXcapSettingsMap.find(account);
   if(itXcapSettings == mXcapSettingsMap.end())
   {
      cpc::string msg = cpc::string("WatcherInfoManagerInterface::getXcapSettings. XcapSettings not pressent for specified account.");
      mAccountIf->fireError(msg);
      return false;
   }
   settings = itXcapSettings->second;
   return true;
}

WatcherInfoEventState WatcherInfoManagerInterface::prepareWinfoEventState(CPCAPI2::SipAccount::SipAccountHandle account, 
                                                                          const cpc::string& winfoState, 
                                                                          const cpc::string& package, 
                                                                          const cpc::string& resource, 
                                                                          const Watcher& watcherParam)
{
   WatcherInfoEventState state;
   WatcherInformation localWinfo;
   WatcherList eventWinfoList;

   state.eventPackage = WINFO_PACKAGE;
   state.expiresTimeMs = 3600000;
   state.mimeType = WINFO_MIME_TYPE;
   state.mimeSubType = WINFO_MIME_SUBTYPE;

   eventWinfoList.package = package;
   eventWinfoList.resource = resource;
   if(strcmp(watcherParam.status.c_str(), "terminated") != 0)
   {
      eventWinfoList.watchers.push_back(watcherParam);
   }

   localWinfo.state = winfoState;
   localWinfo.version = mWinfoMap[account].version;
   localWinfo.watcherLists.push_back(eventWinfoList);

   state.winfo = localWinfo;

   return state;
}

WatcherInfoEventState WatcherInfoManagerInterface::prepareWinfoEventState(CPCAPI2::SipAccount::SipAccountHandle account, 
                                                                          const cpc::string& winfoState,
                                                                          const WatcherList& watcherListParam)
{
   WatcherInfoEventState state;
   WatcherInformation localWinfo;

   state.eventPackage = WINFO_PACKAGE;
   state.expiresTimeMs = 3600000;
   state.mimeType = WINFO_MIME_TYPE;
   state.mimeSubType = WINFO_MIME_SUBTYPE;

   localWinfo.state = winfoState;
   localWinfo.version = mWinfoMap[account].version;
   if(watcherListParam.watchers.size() != 0)
   {
      localWinfo.watcherLists.push_back(watcherListParam);
   }

   state.winfo = localWinfo;

   return state;
}

int WatcherInfoManagerInterface::notifyWinfoSubscribers(CPCAPI2::SipAccount::SipAccountHandle account, WatcherInfoEventState state)
{
   cpc::vector<SipEventSubscriptionHandle> subs;
   subs = mSipEventIf->getSubscriptions(account);
   for(unsigned int i=0;i<subs.size();i++)
   {
      if( strcmp(mSipEventIf->getCreationInfo(subs[i])->eventType.c_str(), state.eventPackage) == 0 )
      {
         notify(subs[i], state);
      }
   }
   return kSuccess;
}

bool WatcherInfoManagerInterface::increaseVersionNumber(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapSettings xcapSettings)
{
   CPCAPI2::XCAP::XcapRequestComponents requestComp;
   CPCAPI2::XCAP::XCAPResult result;

   requestComp.auid = WINFO_AUID;
   requestComp.contentType = WINFO_MIME_TYPE;
   requestComp.contentType.append("/", 1);
   requestComp.contentType.append(WINFO_MIME_SUBTYPE, strlen(WINFO_MIME_SUBTYPE));
   requestComp.isGlobal = false;
   requestComp.documentSelector = "index";

   requestComp.nodeSelector = "watcherinfo/@version";
   cpc::string s = cpc::to_string(mWinfoMap[account].version + 1).c_str();
   result = xcapModule->write(xcapSettings, s, requestComp);
   if( (result.errorCode > 399) || (result.errorCode < 100) )
   {
      cpc::string msg = cpc::string("WatcherInfoManagerInterface::increaseVersionNumber. Unable to write to xcap server. Write to xcap server failed with error code: " 
                              + cpc::to_string(result.errorCode) + "./n"
                              + "Error message: " + result.errorMsg.c_str());
      mAccountIf->fireError(msg);
      return false;
   }
   mWinfoMap[account].version++;
   return true;
}
   
bool WatcherInfoManagerInterface::subscriptionUsesEow(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription)
{
   std::set<CPCAPI2::SipEvent::SipEventSubscriptionHandle>::iterator it = mEowSubscriptions.find(subscription);
   return (it != mEowSubscriptions.end());
}

std::ostream& operator<<(std::ostream& os, const NewWatcherInfoSubscriptionEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const WatcherInfoSubscriptionEndedEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const WatcherInfoSubscriptionStateChangedEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const IncomingWatcherInfoEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const NotifyWatcherInfoSuccessEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const NotifyWatcherInfoFailureEvent& evt)
{
   return os;
}

std::ostream& operator<<(std::ostream& os, const WatcherInfo::ErrorEvent& evt)
{
   return os;
}

}
}

#endif // CPCAPI2_BRAND_WATCHER_INFO_MODULE
