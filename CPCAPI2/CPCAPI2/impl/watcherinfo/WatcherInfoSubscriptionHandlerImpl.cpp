#include "brand_branded.h"

#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
#include "cpcapi2utils.h"
#include "WatcherInfoSubscriptionHandlerImpl.h"

using namespace CPCAPI2::SipEvent;
using namespace resip;

namespace CPCAPI2
{
namespace WatcherInfo
{

WatcherInfoSubscriptionHandlerImpl::WatcherInfoSubscriptionHandlerImpl(CPCAPI2::SipAccount::SipAccountImpl& acct, 
                                                      CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf,
                                                      CPCAPI2::WatcherInfo::WatcherInfoManagerInterface& winfoManager)
   : account(acct),
     sipEventIf(sipEventIf),
     winfoManager(winfoManager)
{
}

int WatcherInfoSubscriptionHandlerImpl::onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args)
{
   NewWatcherInfoSubscriptionEvent winfoArgs;

   winfoArgs.account = args.account;
   winfoArgs.remoteAddress = args.remoteAddress;
   winfoArgs.subscriptionType = args.subscriptionType;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onNewSubscription), subscription, winfoArgs);
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args)
{
   WatcherInfoSubscriptionEndedEvent winfoArgs;

   winfoArgs.endReason = args.endReason;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onSubscriptionEnded), subscription, winfoArgs);
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args)
{
   IncomingWatcherInfoEvent winfoArgs;

   if(args.eventState.eventPackage == WINFO_PACKAGE)
   {
      if(args.eventState.contentUTF8.empty())
      {
         return kSuccess;
      }

      winfoArgs.eventState.eventPackage = args.eventState.eventPackage;
      winfoArgs.eventState.expiresTimeMs = args.eventState.expiresTimeMs;
      winfoArgs.eventState.mimeSubType = args.eventState.mimeSubType;
      winfoArgs.eventState.mimeType = args.eventState.mimeType;
      winfoArgs.eventState.winfo = *this->winfoManager.convertUtf8ToWinfo(args.eventState.contentUTF8);
      
      if (winfoManager.subscriptionUsesEow(subscription))
      {
         std::map<CPCAPI2::SipEvent::SipEventSubscriptionHandle, WatcherInformation>::iterator winfo = mPartialWatcherInfoMap.find(subscription);
         if (winfo != mPartialWatcherInfoMap.end())
         {
            // if we have previous list, lets merge with the new one.
            mergeWatcherInformation(winfoArgs.eventState.winfo, winfo->second);
         }
         else if (strcmp(winfoArgs.eventState.winfo.state, WINFO_STATE_PARTIAL) == 0)
         {
            // no previous list. also state="partial", no need to wait for eow.
            winfoArgs.eventState.winfo.eow = true;
         }
         
         // if it is not time to do callback yet, save for later merging
         if (winfoArgs.eventState.winfo.eow == false)
         {
            mPartialWatcherInfoMap[subscription] = winfoArgs.eventState.winfo;
         }
      }

      if( (strcmp(winfoArgs.eventState.mimeType, WINFO_MIME_TYPE) == 0) &&
         (strcmp(winfoArgs.eventState.mimeSubType, WINFO_MIME_SUBTYPE) == 0) &&
          (!winfoManager.subscriptionUsesEow(subscription) || winfoArgs.eventState.winfo.eow) )
      {
         std::map<CPCAPI2::SipEvent::SipEventSubscriptionHandle, WatcherInformation>::iterator winfo = mPartialWatcherInfoMap.find(subscription);
         if (winfo != mPartialWatcherInfoMap.end())
         {
            mPartialWatcherInfoMap.erase(winfo);
         }
         winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onIncomingWatcherInfo), subscription, winfoArgs);
      }
   }
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args)
{
   WatcherInfoSubscriptionStateChangedEvent winfoArgs;

   winfoArgs.subscriptionState = (WatcherInfoSubscriptionState)args.subscriptionState;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onSubscriptionStateChanged), subscription, winfoArgs);
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args)
{
   NotifyWatcherInfoSuccessEvent winfoArgs;

   winfoArgs.sipResponseCode = 200;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onNotifyWatcherInfoSuccess), subscription, winfoArgs);
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args)
{
   NotifyWatcherInfoFailureEvent winfoArgs;

   winfoArgs.sipResponseCode = args.sipResponseCode;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onNotifyWatcherInfoFailure), subscription, winfoArgs);
   return kSuccess;
}

int WatcherInfoSubscriptionHandlerImpl::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   ErrorEvent winfoArgs;

   winfoArgs.errorText = args.errorText;

   winfoManager.fireEvent(cpcEvent(WatcherInfoSubscriptionHandler, onError), subscription, winfoArgs);
   return kSuccess;
}
   
void WatcherInfoSubscriptionHandlerImpl::mergeWatcherInformation(WatcherInformation& combined, WatcherInformation& partial)
{
   cpc::vector<WatcherList>::iterator wl1 = combined.watcherLists.begin();
   for (; wl1 != combined.watcherLists.end(); wl1++)
   {
      cpc::vector<WatcherList>::iterator wl2 = partial.watcherLists.begin();
      for (; wl2 != partial.watcherLists.end(); wl2++)
      {
         if ((strcmp(wl1->package, wl2->package) ==0) && (strcmp(wl1->resource, wl2->resource) == 0))
         {
            for (cpc::vector<Watcher>::iterator w = wl2->watchers.begin(); w != wl2->watchers.end(); w++)
            {
               wl1->watchers.push_back((*w));
            }
         }
      }
   }
}

}
}

#endif // CPCAPI2_BRAND_WATCHER_INFO_MODULE
