#pragma once

#if !defined(CPCAPI2_WATCHER_INFO_CREATION_INFO_H)
#define CPCAPI2_WATCHER_INFO_CREATION_INFO_H

//includes
#include "cpcapi2defs.h"
#include "watcherinfo/WatcherInfoTypes.h"

namespace CPCAPI2
{
namespace WatcherInfo
{

/**
* 
*/
struct WatcherInfoCreationInfo
{
   CPCAPI2::SipAccount::SipAccountHandle      account;

   WatcherInfoCreationInfo()
   {
      account = 0;
   }
};
typedef std::map<CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandle, CPCAPI2::WatcherInfo::WatcherInfoCreationInfo> WinfoCreationInfoMap;

} // WatcherInfo
} // CPCAPI2
#endif // CPCAPI2_WATCHER_INFO_CREATION_INFO_H