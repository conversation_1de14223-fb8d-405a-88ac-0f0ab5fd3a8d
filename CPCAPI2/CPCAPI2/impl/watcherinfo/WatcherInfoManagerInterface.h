#pragma once

#if !defined(__CPCAPI2_WATCHER_INFO_MANAGER_INTERFACE_H__)
#define __CPCAPI2_WATCHER_INFO_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "../event/SipEventManagerInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../account/SipAccountImpl.h"
#include "../xcap/XcapInternalImpl.h"
#include "watcherinfo/WatcherInfoManager.h"
#include "watcherinfo/WatcherInfoSubscriptionHandler.h"
#include "WatcherInfoCreationInfo.h"

// forward decls
typedef struct _xmlDoc xmlDoc;
typedef xmlDoc *xmlDocPtr;

namespace CPCAPI2
{

class Phone;

namespace SipEvent
{
class SipEventManagerInterface;
};

namespace WatcherInfo
{

class WatcherInfoSubscriptionHandlerImpl;

class WatcherInfoManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<WatcherInfoSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                    public WatcherInfoManager, 
                                    public PhoneModule
{
public:
   typedef std::map<SipAccount::SipAccountHandle, CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandlerImpl*> AccountMap;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::XCAP::XcapSettings> AccountXcapSettingsMap;
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::WatcherInfo::WatcherInformation> AccountWinfoMap;

   WatcherInfoManagerInterface(Phone* phone);
   virtual void Release() OVERRIDE;

   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandler* handler) OVERRIDE;
   virtual int removeHandler(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int applySubscriptionSettings(CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandle subscription, const CPCAPI2::WatcherInfo::WatcherInfoEventSubscriptionSettings& settings) OVERRIDE;
   virtual int addParticipant(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int setEventServer(WatcherInfoSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int start(WatcherInfoSubscriptionHandle subscription) OVERRIDE;
   virtual int end(WatcherInfoSubscriptionHandle subscription) OVERRIDE;
   virtual int refresh(WatcherInfoSubscriptionHandle subscription) OVERRIDE;
   virtual int notify(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState) OVERRIDE;
   virtual int reject(WatcherInfoSubscriptionHandle subscription, unsigned int rejectReason) OVERRIDE;
   virtual int accept(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState) OVERRIDE;
   virtual int setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual int setPresenceAuthenticationRules(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& presRulesXml) OVERRIDE;
   virtual int addWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri) OVERRIDE;
   virtual int removeWatcher(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri) OVERRIDE;

   WatcherInformation* convertUtf8ToWinfo(const cpc::string& winfoString);
   inline CPCAPI2::XCAP::XcapInternalInterface* getXcapManager(){return xcapModule;}
   bool subscriptionUsesEow(CPCAPI2::SipEvent::SipEventSubscriptionHandle);

private:
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, WatcherInfoSubscriptionHandler* handler);
   int removeHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account);
   int notifyImpl(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState);
   int acceptImpl(WatcherInfoSubscriptionHandle subscription, const WatcherInfoEventState& eventState);
   int setPresenceAuthenticationRulesImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& path);
   int addWatcherImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri);
   int removeWatcherImpl(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& uri);
   int initializeWatcherInfo(CPCAPI2::SipAccount::SipAccountHandle account);
   int loadWatcherInfo(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapRequestComponents& docPathComponents);
   WatcherInformation parseXmlWinfo(xmlDocPtr xmlWinfo);
   WatcherInformation parseXmlWinfo(xmlTextReaderPtr xmlWinfo);
   cpc::string convertWinfoToUtf8(const WatcherInformation& eventState);
   cpc::string convertWinfoListToUtf8(const WatcherList& listParam);
   cpc::string convertWatcherToUtf8(const Watcher& watcherParam);
   cpc::string createWinfoListNodeSelector(const cpc::string& resourceUri);
   cpc::string createWatcherNodeSelector(const cpc::string& listParam, const Watcher& watcherParam);
   int setXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::XCAP::XcapSettings& xcapSettings);
   int getWatcherList(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& package, const cpc::string& resourceUri, WatcherInformation& winfoParam, WatcherList& list);
   bool getXcapSettings(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapSettings& settings);
   WatcherInfoEventState prepareWinfoEventState(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& winfoState, const cpc::string& package, 
                                                                                               const cpc::string& resource, const Watcher& watcherParam);
   WatcherInfoEventState prepareWinfoEventState(CPCAPI2::SipAccount::SipAccountHandle account, const cpc::string& winfoState, const WatcherList& watcherListParam);
   int notifyWinfoSubscribers(CPCAPI2::SipAccount::SipAccountHandle account, WatcherInfoEventState state);
   bool increaseVersionNumber(CPCAPI2::SipAccount::SipAccountHandle account, CPCAPI2::XCAP::XcapSettings xcapSettings);
   cpc::string generatePresRulesDoc();

   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   AccountMap mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, CPCAPI2::WatcherInfo::WatcherInfoSubscriptionHandler*> mHandlers;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   CPCAPI2::PhoneInterface* mPhone;
   AccountWinfoMap mWinfoMap;
   CPCAPI2::XCAP::XcapInternalInterface* xcapModule;
   AccountXcapSettingsMap mXcapSettingsMap;
   WinfoCreationInfoMap mWinfoCreationInfoMap;
   std::set<cpc::string> presenceWhitelist;
   std::set<CPCAPI2::SipEvent::SipEventSubscriptionHandle> mEowSubscriptions;
};

std::ostream& operator<<(std::ostream& os, const NewWatcherInfoSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const WatcherInfoSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const WatcherInfoSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const IncomingWatcherInfoEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyWatcherInfoSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const NotifyWatcherInfoFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const WatcherInfo::ErrorEvent& evt);
}
}

#endif // __CPCAPI2_WATCHER_INFO_MANAGER_INTERFACE_H__
