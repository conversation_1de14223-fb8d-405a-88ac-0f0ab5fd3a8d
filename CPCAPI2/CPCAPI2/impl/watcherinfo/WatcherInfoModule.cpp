#include "brand_branded.h"

#include "interface/experimental/watcherinfo/WatcherInfoManager.h"

#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
#include "WatcherInfoManagerInterface.h"
#include "phone/PhoneInterface.h"
#endif // CPCAPI2_WATCHER_INFO_MODULE

namespace CPCAPI2
{
namespace WatcherInfo
{

WatcherInfoManager* WatcherInfoManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<WatcherInfoManagerInterface>(phone, "WatcherInfoManagerInterface");
#else
   return NULL;
#endif
}

} // WatcherInfo
} // CPCAPI2
