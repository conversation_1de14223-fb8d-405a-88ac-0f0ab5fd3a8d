#pragma once

#if !defined(CPCAPI2_WATCHER_INFO_SUBSCRIPTION_IMPL_HANDLER_H)
#define CPCAPI2_WATCHER_INFO_SUBSCRIPTION_IMPL_HANDLER_H

#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "WatcherInfoManagerInterface.h"
#include "watcherinfo/WatcherInfoSubscriptionHandler.h"

#include "../util/DumFpCommand.h"


namespace CPCAPI2
{
namespace SipEvent
{
class SipEventManagerInterface;
};
namespace WatcherInfo
{
class WatcherInfoSubscriptionHandler;

class WatcherInfoSubscriptionHandlerImpl : public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>
{
public:
    WatcherInfoSubscriptionHandlerImpl(CPCAPI2::SipAccount::SipAccountImpl& acct,
                                       CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf,
                                       CPCAPI2::WatcherInfo::WatcherInfoManagerInterface& winfoManager);

    virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
    virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
    virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
    virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
    virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

    virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
    virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);
    virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);
private:
   void mergeWatcherInformation(WatcherInformation& combined, WatcherInformation& partial);

   CPCAPI2::SipAccount::SipAccountImpl& account;
   CPCAPI2::SipEvent::SipEventManagerInterface& sipEventIf;
   CPCAPI2::WatcherInfo::WatcherInfoManagerInterface& winfoManager;
   std::map<CPCAPI2::SipEvent::SipEventSubscriptionHandle, WatcherInformation> mPartialWatcherInfoMap;
};

}
}

#endif // CPCAPI2_WATCHER_INFO_SUBSCRIPTION_IMPL_HANDLER_H
