#pragma once

#if !defined(CPCAPI2_STUNCLIENT_H)
#define CPCAPI2_STUNCLIENT_H

#include <cpcstl/string.h>
#include <rutil/dns/DnsStub.hxx>
#include <rutil/dns/DnsSrvRecord.hxx>
#include <resip/stack/Tuple.hxx>
#include <resip/stack/UdpTransport.hxx>
#include <rutil/FdSetIOObserver.hxx>
#include <rutil/Reactor.hxx>

namespace CPCAPI2
{

// TODO: Update required for IP V4/V6 compatibility
   
struct StunDnsHostRecord
{
   StunDnsHostRecord() {
      memset(&addr, 0, sizeof(addr));
   }
   in_addr addr;
};

struct StunDnsSrvRecord
{
   resip::Data target;
   int port;
};

class StunClient : public resip::DnsResultSink
{
public:
   StunClient(const resip::DnsStub::DnsSettings& dnsSettings = resip::DnsStub::DnsSettings(),
              resip::UdpTransport* udpTransport = 0);

   StunDnsHostRecord getDnsHostRecord(const resip::Data& target);
   StunDnsSrvRecord getDnsSrvRecord(const resip::Data& domainName);
   resip::Tuple getStunAddress(const resip::Data& stunServer, unsigned int stunPort);
   void reInit(const resip::DnsStub::DnsSettings& dnsSettings, resip::UdpTransport* udpTransport = 0);
   bool hasValidTransport(resip::UdpTransport* transport);

private:
   resip::UdpTransport* mUdpTransport;
   std::vector<resip::DnsSrvRecord> mDnsSrvRecords;
   std::vector<resip::DnsHostRecord> mDnsHostRecords;
   bool mOnDnsResultHostCalled;
   bool mOnDnsResultSrvCalled;
   //resip::DnsStub::DnsSettings mDnsSettings;
   resip::SharedPtr<resip::DnsStub> mDns;

   // DnsResultSink
   virtual void onDnsResult(const resip::DNSResult<resip::DnsHostRecord>&);
   virtual void onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>&);
   virtual void onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>&) {}
   virtual void onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>&) {}
   virtual void onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>&) {}

   void sendStunTest(const resip::Data& stunServer, unsigned int stunPort);
   static void process(resip::ReactorEventHandler* observer);
};

}

#endif // CPCAPI2_STUNCLIENT_H