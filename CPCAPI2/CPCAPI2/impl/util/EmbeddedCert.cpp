#include <cpcapi2defs.h>

#include "CertHelper.h"

#include <openssl/aes.h>
#include <openssl/evp.h>
#include <openssl/err.h>

#include <rutil/Data.hxx>

#include "EmbeddedCert.h"

#define key "b19d0fc36ead9dda47fd30a7e694bce0"
#define iv "EmbeddedCert.h"

#define SUA_SUBSYSTEM cpsi::Subsystem::Security

using namespace resip;

namespace CPCAPI2
{
   // !ds! command seems to be something like:
   //
   // aes-256-cbc -in cert.crt -out encrypted.dat -e -aes256 -k b19d0fc36ead9dda47fd30a7e694bce0
   //
   // .. and then base64-encode the result. That's what goes into the input of this function.
   //
   X509* readEmbeddedCert(const std::string& cert)
   {
      X509 *xCert = 0;

      //Step 1: decode the base64-encoded blob
      Data base46Data(cert);
      Data decodedData = base46Data.base64decode();
      if(decodedData.size() == 0)
      {
         //SUA_TRACE_ERROR("No embedded certificate!")
         return 0;
      }

      //Step 2: decrypt the AES-256-encypted certificate
      unsigned char *pemCert = new unsigned char[decodedData.size() + EVP_CIPHER_block_size(EVP_aes_256_cbc()) + 1];
      if(pemCert == 0)
      {
         //SUA_TRACE_ERROR("Cannot allocate memory")
         return 0;
      }

      int pemCertLen = 0, tmplen = 0;
      EVP_CIPHER_CTX* x = EVP_CIPHER_CTX_new();
      EVP_DecryptInit_ex(x, EVP_aes_256_cbc(), NULL, (const unsigned char*)key, (const unsigned char*)iv);

      if (!EVP_DecryptUpdate(x, pemCert, &pemCertLen,(const unsigned char*)decodedData.data(), decodedData.size()))
      {
         //SUA_TRACE_ERROR(" Error decrypting certificate ");
         delete [] pemCert;
         return 0;
      }

      if (!EVP_DecryptFinal_ex(x, (unsigned char*)pemCert + pemCertLen, &tmplen))
      {

         unsigned long error, reason;
         char buf[128];
         error = ERR_peek_error();
         reason = ERR_GET_REASON(error);
         ERR_error_string_n(reason,buf,128);
         //SUA_TRACES_ERROR(" OpenSSL decryption error " << buf);

         delete [] pemCert;
         return 0;
      }

      pemCertLen += tmplen;
      EVP_CIPHER_CTX_free(x);

      //Step 3: convert PEM format to X509 structure
      BIO *b = BIO_new_mem_buf((void*)pemCert, pemCertLen);
      if(b)
      {
         xCert = PEM_read_bio_X509(b,0,0,0);
         if(xCert == 0)
         {
            //SUA_TRACE_ERROR("Cannot parse certificate");
         }
         BIO_free(b);
      }
      delete[] pemCert;
      //SUA_TRACE_INFO("Embedded root certificate succesfully read")

      return xCert;
   }

}
