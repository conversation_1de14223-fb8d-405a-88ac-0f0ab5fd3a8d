#include "brand_branded.h"

#include "HttpClient.h"
#include "HttpClientImpl.h"
#include "ProxyHelper.h"
#include "cpc_logger.h"

#include <curl/curl.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace CPCAPI2;
using namespace xten;

HTTPClient::HTTPClient(Phone* p)
   : mImpl(new HTTPClientImpl(dynamic_cast<PhoneInterface*>(p)))
{
}

HTTPClient::~HTTPClient() 
{
   delete mImpl;
}

HTTPClient::EHTTPError HTTPClient::ConvertErrorCode(int code)
{
   return mImpl->ConvertErrorCode(code);
}

void HTTPClient::HTTPSendMessage(
      HTTPClient::EHTTPVerb eInVerb,                       
      const char* szInURL,                   
      const char* szInMIMEType,               
      const char* szInUsername,              
      const char* szInPassword,    
      const char* szInClientCertificate,        
      const char* szInClientCertificatePasswd,    
      const char* pInMessageBody,          
      unsigned long ulInMessageLengthInBytes, 
      unsigned long ulInMaxReadRate,  
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      bool verboseLogging,
      bool suppressLogging,
      int& resultErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress,
      unsigned long timeoutSeconds,
      size_t maxResponseBodySizeBytes,
      cpc::vector<cpc::string> requiredCertPublicKeys,
      unsigned long dynamicTimeoutSeconds
      )
{
   // not exposed; planning to deprecate this method in favour of the one that passes a struct instead
   bool useHttp2 = false;

   int curlError;
   mImpl->HTTPSendMessage(eInVerb, szInURL, szInMIMEType, szInUsername, szInPassword, szInClientCertificate, szInClientCertificatePasswd, pInMessageBody, ulInMessageLengthInBytes, ulInMaxReadRate, TLS_DEFAULT, "", bInUseEmbeddedCert1, bInUseEmbeddedCert2, bInIgnoreCertErrors, bInEnableCookies, szInCookieFile, customHeaders, verboseLogging, suppressLogging, resultErrorCode, curlError, responseStatus, contentType, result, redirectInfo, szInUserAgent, overrideProxyAddress, useHttp2, timeoutSeconds, maxResponseBodySizeBytes, requiredCertPublicKeys, dynamicTimeoutSeconds);
}

// struct-ified version of above method
void HTTPClient::HTTPSendMessage(
      const char* inUrl,
      const RequestConfig& requestConfig,
      ResponseResult& responseResult
   )
{
   mImpl->HTTPSendMessage(requestConfig.verb, inUrl, requestConfig.mimeType, requestConfig.username, requestConfig.password,
                           requestConfig.clientCertificate, requestConfig.clientCertificatePasswd, requestConfig.messageBody,
                           requestConfig.messageLengthInBytes, requestConfig.maxReadRate, requestConfig.tlsVersion,
                           requestConfig.cipherSuite, requestConfig.useEmbeddedCert1,
                           requestConfig.useEmbeddedCert2, requestConfig.ignoreCertErrors, requestConfig.enableCookies,
                           requestConfig.cookieFile, requestConfig.customHeaders, requestConfig.verboseLogging,
                           requestConfig.suppressLogging, responseResult.errorCode, responseResult.curlErrorCode, responseResult.status, responseResult.contentType,
                           responseResult.messageBody, responseResult.redirectInfo, requestConfig.userAgent,
                           requestConfig.overrideProxyAddress, requestConfig.useHttp2, requestConfig.timeoutSeconds,
                           requestConfig.maxResponseBodySizeBytes, requestConfig.requiredCertPublicKeys, requestConfig.dynamicTimeoutSeconds);
}

void HTTPClient::StartHTTPSession(
      HTTPClient::EHTTPVerb eInVerb,                       
      const char* szInURL,                   
      const char* szInMIMEType,               
      const char* szInUsername,              
      const char* szInPassword,    
      const char* szInClientCertificate,        
      const char* szInClientCertificatePasswd,    
      const char* pInMessageBody,          
      unsigned long ulInMessageLengthInBytes, 
      unsigned long ulInMaxReadRate,  
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress,
      unsigned long ulInDynamicTimeoutSec
      )
{
   // not exposed; planning to deprecate this method in favour of the one that passes a struct instead
   int timeoutSeconds = 0;
   size_t maxResponseBodySizeBytes = 0;
   bool useHttp2 = false;
   int curlError;
   mImpl->StartHTTPSession(eInVerb, szInURL, szInMIMEType, szInUsername, szInPassword, szInClientCertificate, szInClientCertificatePasswd, pInMessageBody, ulInMessageLengthInBytes, ulInMaxReadRate, TLS_DEFAULT, "", bInUseEmbeddedCert1, bInUseEmbeddedCert2, bInIgnoreCertErrors, bInEnableCookies, szInCookieFile, customHeaders, resultErrorCode, curlError, responseStatus, contentType, result, redirectInfo, szInUserAgent, overrideProxyAddress, useHttp2, timeoutSeconds,
       maxResponseBodySizeBytes, false, ulInDynamicTimeoutSec);
}

void HTTPClient::StartHTTPSession(
   const char* inUrl,
   const RequestConfig& requestConfig,
   ResponseResult& responseResult
)
{
   mImpl->StartHTTPSession(requestConfig.verb, inUrl, requestConfig.mimeType, requestConfig.username, requestConfig.password,
                           requestConfig.clientCertificate, requestConfig.clientCertificatePasswd, requestConfig.messageBody,
                           requestConfig.messageLengthInBytes, requestConfig.maxReadRate, requestConfig.tlsVersion,
                           requestConfig.cipherSuite, requestConfig.useEmbeddedCert1,
                           requestConfig.useEmbeddedCert2, requestConfig.ignoreCertErrors, requestConfig.enableCookies,
                           requestConfig.cookieFile, requestConfig.customHeaders,
                           responseResult.errorCode, responseResult.curlErrorCode, responseResult.status, responseResult.contentType,
                           responseResult.messageBody, responseResult.redirectInfo, requestConfig.userAgent,
                           requestConfig.overrideProxyAddress, requestConfig.useHttp2,
                           requestConfig.timeoutSeconds, requestConfig.maxResponseBodySizeBytes, requestConfig.verboseLogging,
                           requestConfig.dynamicTimeoutSeconds);
}

void HTTPClient::DoSessionRequest(
      HTTPClient::EHTTPVerb eInVerb,                       
      const char* szInURL,                   
      const char* szInMIMEType,               
      const char* szInUsername,              
      const char* szInPassword,    
      const char* szInClientCertificate,        
      const char* szInClientCertificatePasswd,    
      const char* pInMessageBody,          
      unsigned long ulInMessageLengthInBytes, 
      unsigned long ulInMaxReadRate,  
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      int& resultErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress
      )
{
   cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders;
   int curlError;
   mImpl->DoSessionRequest(eInVerb, szInURL, szInMIMEType, szInUsername, szInPassword, szInClientCertificate, szInClientCertificatePasswd, pInMessageBody, ulInMessageLengthInBytes, ulInMaxReadRate, TLS_DEFAULT, "", bInUseEmbeddedCert1, bInUseEmbeddedCert2, bInIgnoreCertErrors, bInEnableCookies, szInCookieFile, customHeaders, resultErrorCode, curlError, responseStatus, contentType, result, redirectInfo, szInUserAgent, overrideProxyAddress, false);
}

void HTTPClient::DoSessionRequest(
   const char* inUrl,                                // Must include { "http" | "https" }
   const RequestConfig& requestConfig,
   ResponseResult& responseResult
)
{
   mImpl->DoSessionRequest(requestConfig.verb, inUrl, requestConfig.mimeType, requestConfig.username, requestConfig.password,
      requestConfig.clientCertificate, requestConfig.clientCertificatePasswd, requestConfig.messageBody,
      requestConfig.messageLengthInBytes, requestConfig.maxReadRate, requestConfig.tlsVersion,
      requestConfig.cipherSuite, requestConfig.useEmbeddedCert1,
      requestConfig.useEmbeddedCert2, requestConfig.ignoreCertErrors, requestConfig.enableCookies,
      requestConfig.cookieFile, requestConfig.customHeaders,
      responseResult.errorCode, responseResult.curlErrorCode, responseResult.status, responseResult.contentType,
      responseResult.messageBody, responseResult.redirectInfo, requestConfig.userAgent,
      requestConfig.overrideProxyAddress, requestConfig.verboseLogging);
}

void HTTPClient::Abort()
{
   mImpl->Abort();
}

cpc::string HTTPClient::toString(HTTPClient::acceptedCertFailures eFailures)
{
   switch (eFailures)
   {
      case E_CERT_NOT_YET_VALID: return "E_CERT_NOT_YET_VALID";
      case E_CERT_EXPIRED: return "E_CERT_EXPIRED";
      case E_CERT_ID_MISMATCH: return "E_CERT_ID_MISMATCH";
      case E_CERT_NOT_TRUSTED: return "E_CERT_NOT_TRUSTED";
      case E_CERT_OTHER_ERROR: return "E_CERT_OTHER_ERROR";
      default: return "E_CERT_WHATEVER_ERROR";
   }
   
   return "E_CERT_WHATEVER_ERROR";
}

cpc::string HTTPClient::toString(HTTPClient::EHTTPVerb eVerb)
{
   switch (eVerb)
   {
      case EHTTPVerbUnparsed: return "EHTTPVerbUnparsed";
      case EHTTPVerbUnknown: return "EHTTPVerbUnknown";
      case EHTTPVerbInvalid: return "EHTTPVerbInvalid";
      case EHTTPVerbOPTIONS: return "EHTTPVerbOPTIONS";
      case EHTTPVerbGET: return "EHTTPVerbGET";
      case EHTTPVerbHEAD: return "EHTTPVerbHEAD";
      case EHTTPVerbPOST: return "EHTTPVerbPOST";
      case EHTTPVerbPUT: return "EHTTPVerbPUT";
      case EHTTPVerbDELETE: return "EHTTPVerbDELETE";
      case EHTTPVerbTRACE: return "EHTTPVerbTRACE";
      case EHTTPVerbCONNECT: return "EHTTPVerbCONNECT";
      case EHTTPVerbTRACK: return "EHTTPVerbTRACK";
      case EHTTPVerbMOVE: return "EHTTPVerbMOVE";
      case EHTTPVerbCOPY: return "EHTTPVerbCOPY";
      case EHTTPVerbPROPFIND: return "EHTTPVerbPROPFIND";
      case EHTTPVerbPROPPATCH: return "EHTTPVerbPROPPATCH";
      case EHTTPVerbMKCOL: return "EHTTPVerbMKCOL";
      case EHTTPVerbLOCK: return "EHTTPVerbLOCK";
      case EHTTPVerbUNLOCK: return "EHTTPVerbUNLOCK";
      case EHTTPVerbSEARCH: return "EHTTPVerbSEARCH";
      case EHTTPVerbMaximum: return "EHTTPVerbMaximum";
      default: return "EHTTPVerbMaximum";
   }
   
   return "EHTTPVerbMaximum";
}

cpc::string HTTPClient::toString(HTTPClient::EHTTPError eError)
{
   switch (eError)
   {
      case EHTTPOk: return "EHTTPOk";
      case EHTTPLookup: return "EHTTPLookup";
      case EHTTPAuth: return "EHTTPAuth";
      case EHTTPConnect: return "EHTTPConnect";
      case EHTTPEmbedded: return "EHTTPEmbedded";
      case EHTTPUnknownError: return "EHTTPUnknownError";
      default: return "EHTTPUnknownError";
   }

   return "EHTTPUnknownError";
}

namespace CPCAPI2
{
std::ostream& operator<<(std::ostream& os, const HTTPClient::StringPair& stringPair)
{
   os << "first: " << stringPair.first << " second: " << stringPair.second;
   return os;
}

std::ostream& operator<<(std::ostream& os, const HTTPClient::RedirectInfo& redirectInfo)
{
   os << "code: " << redirectInfo.code << " count: " << redirectInfo.count << " url: " << redirectInfo.url;
   return os;
}

std::ostream& operator<<(std::ostream& os, const HTTPClient::RequestConfig& requestConfig)
{
   os << "verb: " << HTTPClient::toString(requestConfig.verb) << " mimeType: " << requestConfig.mimeType
      << " username: " << requestConfig.username << " password: " << requestConfig.password
      << " clientCertificate: " << requestConfig.clientCertificate << " clientCertificatePasswd: " << requestConfig.clientCertificatePasswd
      << " messageLengthInBytes: " << requestConfig.messageLengthInBytes << " maxReadRate: " << requestConfig.maxReadRate
      << " useEmbeddedCert1: " << (requestConfig.useEmbeddedCert1 ? "true" : "false") << " useEmbeddedCert2: " << (requestConfig.useEmbeddedCert2 ? "true" : "false")
      << " ignoreCertErrors: " << (requestConfig.ignoreCertErrors ? "true" : "false") << " enableCookies: " << (requestConfig.enableCookies ? "true" : "false")
      << " cookieFile: " << requestConfig.cookieFile << " verboseLogging: " << (requestConfig.verboseLogging ? "true" : "false")
      << " userAgent: " << requestConfig.userAgent << " overrideProxyAddress: " << requestConfig.overrideProxyAddress
      << " timeoutSeconds: " << requestConfig.timeoutSeconds << " useHttp2: " << requestConfig.useHttp2;

   for (cpc::vector<CPCAPI2::HTTPClient::StringPair>::const_iterator i = requestConfig.customHeaders.begin(); i != requestConfig.customHeaders.end(); i++)
   {
      os << " Header: " << (*i);
   }

   if (requestConfig.messageBody)
      os << " messageBody: " << requestConfig.messageBody;

   return os;
}

std::ostream& operator<<(std::ostream& os, const HTTPClient::ResponseResult& responseResult)
{
   os << "errorCode: " << responseResult.errorCode << " status: " << responseResult.status << " contentType: "
      << responseResult.contentType << " RedirectInfo:: " << responseResult.redirectInfo << " messageBody: " << responseResult.messageBody;
   return os;
}
} // namespace CPCAPI2
