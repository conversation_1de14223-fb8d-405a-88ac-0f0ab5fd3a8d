#pragma once

#ifndef __CPCAPI2_IPHELPERS_H__
#define __CPCAPI2_IPHELPERS_H__

#include <cpcapi2defs.h>
#include <rutil/Data.hxx>
#include <resip/stack/Tuple.hxx>
#include <boost/asio/ip/address_v4.hpp>
#include <boost/asio/ip/udp.hpp>

namespace CPCAPI2
{
   class CPCAPI2_SHAREDLIBRARY_API IpHelpers
   {
   public:
      /**
       * Returns the first IP address which may be used
       */
      static resip::Data getPreferredLocalIpAddress();
      
      /**
       * Returns the first IP address from list of local network interfaces which matches ipVersion. Note that loopback, link local interfaces are excluded
       */
      static resip::Data getAnyLocalIpAddress(resip::IpVersion ipVersion);

      /**
       * Uses the route table to find the best srcIp to reach target
       */
      static void getPreferredLocalIpAddress(const resip::Tuple& target, resip::Data& srcIp);

      /**
       * Determines if the specified IP address is configured on the system
       */
      static bool isAvailableIpAddress(const resip::Data& address);

      /**
      * Retrieves the subnet mask of the interface with the given ip address
      */
      static int getSubnetMask(const boost::asio::ip::address_v4& address, boost::asio::ip::address_v4& netmask);

      /**
      * Creates a range of ip addresses from the given address and subnet mask. The range can be limited.
      */
      static int getSubnetIpRange(const boost::asio::ip::address_v4& address, const  boost::asio::ip::address_v4& netmask, std::vector<boost::asio::ip::address_v4>& targets, unsigned int limit = 0, boost::asio::ip::address_v4* skipAddress = NULL);
      static int getSubnetIpRange(const boost::asio::ip::address_v4& address, const  boost::asio::ip::address_v4& netmask, std::vector<boost::asio::ip::udp::endpoint>& targets, int port, unsigned int limit = 0, boost::asio::ip::address_v4* skipAddress = NULL);

      /**
      * Strips the port portion of an IPv4 address or domain name
      */
      static resip::Data stripPort(const resip::Data& addr);
      
   };
}

#endif // __CPCAPI2_IPHELPERS_H__
