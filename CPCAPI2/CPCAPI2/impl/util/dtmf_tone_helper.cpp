#include "dtmf_tone_helper.h"

namespace CPCAPI2
{
   static boost::array<char, 16> s_dtmfToneIdLookup = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '#', 'A', 'B', 'C', 'D' };

   #define MAX_EXT_TONE 35 // according to dtmf_ext.h, this should be 38 (use <= for comparison) or 39 (use < for comparison)

   int DtmfToneHelper::dtmfToneIdFromChar(char tone)
   {
      for (int i=0; i<sizeof(s_dtmfToneIdLookup); ++i)
      {
         if (s_dtmfToneIdLookup[i] == tone)
            return i;
      }
      return 0;
   }

   char DtmfToneHelper::dtmfCharFromToneId(int toneId)
   {
      if (toneId < sizeof(s_dtmfToneIdLookup))
      {
         return s_dtmfToneIdLookup[toneId];
      }
      if(toneId>sizeof(s_dtmfToneIdLookup) && toneId<MAX_EXT_TONE)
      {
         return (char)toneId;
      }
      return '0';
   }
}
