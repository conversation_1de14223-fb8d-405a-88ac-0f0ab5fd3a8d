#ifndef _CURL_DAV_H_
#define _CURL_DAV_H_

#include <time.h>
#include <string>

#include "CurlHttp.h"

namespace xten
{

class CurlDav
{
   public:
            
      //check to see if null path is accepted by neon
      CurlDav(const char* hostUri, const char* user, const char* pass);  

      enum Depth
      {
         Zero, 
         One, 
         Infinity
      };

      enum Result
      {
         Ok = 0, // Success 
         Error,  // Generic error; use Session::getLastError for message 
         Lookup,  // Server or proxy hostname lookup failed 
         Auth,  // User authentication failed on server 
         Proxyauth,  // User authentication failed on proxy 
         Connect,  // Could not connect to server 
         Timeout,  // Connection timed out 
         Failed, // The precondition failed 
         Retry,  // Retry request (ne_end_request ONLY) 
         Redirect,  // See ne_redirect.h 
         Aborted
      };

      class Session
      {
         public:
            Session(CurlDav& dav);

            ~Session();
            void setProxyServer(const char *hostname, unsigned int port, const char *bypassList = "");
            void setAcceptableSSLFailures(int);

            Result copy(const char* src, const char* dest, Depth depth, bool overwrite, int& statusCode);
            Result move(const char* src, const char* dest, bool overwrite, int& statusCode);
            Result deleteUri(const char* path, int& statusCode);

            Result getModTime(const char *path, time_t *modtime, int& statusCode);

            //Create a collection at 'path', which MUST have a trailing slash.
            Result mkcol(const char *path, int& statusCode);

            Result put(const char* path, int fd, int& statusCode);
            Result put(const char* path, const char* buffer, size_t size, int& statusCode);

            Result putIfNotModified(const char* path, const char* buffer, size_t size, 
                                    time_t modtime, int& statusCode);

            Result putIfNotModified(const char *path, int fd, time_t modtime, int& statusCode);
            
            Result get(const char* path, int fd, int& statusCode);
            Result get(const char* path, char*& result, int& length, 
                       bool& hasModTime, time_t *modtime, int& statusCode);
         private:
            CurlDav&                  mCurlDav;            
            CurlHttp*                 mCurlHttp;
            CurlHttp::Session*        mCurlHttpSession;
            CurlHttp::HttpHeaderList  mDavHeaders;
      };

      


   private:
      friend class Session;      
      static int provideAuth(void *userdata, const char *realm, int attempt,
                             char *username, char *password);

      std::string mUser;
      std::string mPassword;
      CurlURI     mUri;

};
   
}


#endif
