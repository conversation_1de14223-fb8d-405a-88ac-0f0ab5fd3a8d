#pragma once

#include <rutil/DeadlineTimer.hxx>
#include <rutil/MultiReactor.hxx>
#include <memory>
#include <mutex>

namespace CPCAPI2
{

namespace Utils
{
   class ThrottledInvoker : public resip::Dead<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                            public resip::ReactorBinded
   {
   public:
      /*
       * Class which helps throttle requests to invoke a function.
       *
       * reactor: thread on which re<PERSON><PERSON><PERSON><PERSON> will be fired on
       * minTimeBetweenInvokeMsec: Minimum time between handling invoke requests. 
       */
      ThrottledInvoker(resip::MultiReactor* reactor, int minTimeBetweenInvokeMsec);

      /*
       * Request to invoke function; if a previous invoke request has been made
       * within [throttleDelayMsec], this class will delay dispatching this new
       * invoke request until [throttleDelayMsec] has elapsed since the previous
       * request.
       * 
       * If no previous request has been made within [throttleDelayMsec], the invoke
       * will happen immediately on the reactor thread.
       * 
       * Invoke requests here may be ignored if a previous invoke request is imminently
       * about to be dispatched.
       *
       * reqInvoke may be called from non-reactor thread; all calls by this method
       * to the invoke function will occur on the reactor thread.
       *
       * 
       * Return value: Returns true is the invoke request was accepted; false if it was
       * dropped/ignore (since a previous invoke request is about to be dispatched per
       * above).
       */
      bool reqInvoke(std::function<void()> invoke);

      virtual ~ThrottledInvoker();

      // ReactorBinded
      void release() { delete this; }

   private:
      void onTimer(unsigned short timerId, void* appState);
      int nowMs() const;

      void doInvokeImpl();
      void queueTimerImpl();

      const int mMinTimeBetweenInvokeMsec = 3000;
      int mLastInvokeMs = 0;
      std::atomic_bool mQueuedInvokePending = false;

      std::unique_ptr<resip::DeadlineTimer<resip::MultiReactor> > mTimer;
      resip::MultiReactor* mReactor;

      std::function<void()> mReqHandler;
      std::recursive_mutex mReqHandlerMutex;
      
   };
}

}