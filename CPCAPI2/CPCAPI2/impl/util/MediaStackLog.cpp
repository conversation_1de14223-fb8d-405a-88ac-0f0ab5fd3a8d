#include "MediaStackLog.h"
#include "LogSubsystems.h"

#include <rutil/Logger.hxx>

using namespace resip;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA_STACK

namespace CPCAPI2
{
MediaStackLog::MediaStackLog() :
   mInitialized(false),
   mStreamInterface(NULL)
{
}

MediaStackLog::~MediaStackLog()
{
   reset();
}

void MediaStackLog::init()
{
   webrtc::Trace::CreateTrace(); // .jjg. without this line, webrtc logging on iOS does not work
   webrtc::Trace::set_level_filter(webrtc::kTraceTerseInfo | webrtc::kTraceDefault);
   webrtc::Trace::SetTraceCallback(this);
   rtc::LogMessage::LogToDebug(rtc::LoggingSeverity::LS_WARNING);
   mStreamInterface = new MediaStackLog2();
   rtc::LogMessage::AddLogToStream(mStreamInterface, rtc::LoggingSeverity::LS_WARNING);
   mInitialized = true;
}

void MediaStackLog::reset()
{
   webrtc::Trace::SetTraceCallback(NULL);
   if (mInitialized) {
      // every call to CreateTrace() must have a corresponding ReturnTrace()
      webrtc::Trace::ReturnTrace();
      
      rtc::LogMessage::RemoveLogToStream(mStreamInterface);
      delete mStreamInterface;
   }
   mInitialized = false;
}

// WebRTC trace callback
void MediaStackLog::Print(const webrtc::TraceLevel level,
                     const char *traceString,
                     int length)
{
   switch (level)
   {
      case webrtc::TraceLevel::kTraceNone:
      {
         break;
      }
      case webrtc::TraceLevel::kTraceWarning:
      {
         WarningLog(<< traceString);
         break;
      }
      case webrtc::TraceLevel::kTraceError:
      {
         ErrLog(<< traceString);
         break;
      }
      case webrtc::TraceLevel::kTraceCritical:
      {
         CritLog(<< traceString);
         break;
      }
      case webrtc::TraceLevel::kTraceStateInfo:
      case webrtc::TraceLevel::kTraceTerseInfo:
      case webrtc::TraceLevel::kTraceDefault:
      {
         InfoLog(<< traceString);
         break;
      }
      case webrtc::TraceLevel::kTraceMemory:
      case webrtc::TraceLevel::kTraceTimer:
      case webrtc::TraceLevel::kTraceStream:
      case webrtc::TraceLevel::kTraceDebug:
      case webrtc::TraceLevel::kTraceInfo:  //this is debug info
      case webrtc::TraceLevel::kTraceApiCall:
      case webrtc::TraceLevel::kTraceModuleCall:
      {
         DebugLog(<< traceString);
         break;
      }
      case webrtc::TraceLevel::kTraceAll:
      {
         StackLog(<< traceString);
         break;
      }
      default:
         break;
   }
}

rtc::StreamResult MediaStackLog2::Write(const void* data,
                                       size_t data_len,
                                       size_t* written,
                                       int* error)
{
   // We don't get priority information
   WarningLog(<< std::string((const char*)data, data_len));
   *written = data_len;
   return rtc::StreamResult::SR_SUCCESS;
}

}
