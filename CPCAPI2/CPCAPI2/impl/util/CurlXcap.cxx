#include "CurlXcap.h"

#include <assert.h>

using namespace xten;

std::string CurlXcap::EmptyNodeSelector;


CurlXcap::CurlXcap(const std::string & hostUri, const std::string & xcapRoot, const std::string & user,
         const std::string& pass, const std::string& authuser, const std::string& authpass,
		 const std::string& clientCert, const std::string& clientCertType ,bool global):
   CurlHttp(hostUri, user, pass, clientCert, clientCertType, "", false, false, false, authuser,authpass) ,
   mIsGlobal(global),
   mXcapRoot(xcapRoot)
{
}


CurlXcap::XcapSession::XcapSession(CurlXcap& neonXcap)
   : CurlHttp::Session(neonXcap),
     mCurlXcap(neonXcap)
{
}


std::shared_ptr<CurlHttp::Result>
CurlXcap::XcapSession::deleteUri(const Auid& auid, const std::string& documentPath,
                       const std::string& nodeSelector,
                       const CurlHttp::HttpHeaderList& additionalHeaders)
{
   std::string path = buildXcapPath(auid, documentPath, nodeSelector);
   return Session::deleteUri(path, additionalHeaders);
}

std::shared_ptr<CurlHttp::Result>
CurlXcap::XcapSession::put(const Auid& auid, const char* buffer, size_t size,
                           const std::string& documentPath, const std::string& nodeSelector,
                           const CurlHttp::HttpHeaderList& additionalHeaders)
{
   std::string path = buildXcapPath(auid, documentPath, nodeSelector);
   HttpHeaderList headers = additionalHeaders;
   headers.push_back(chooseMimeType(auid, nodeSelector));
   return Session::put(path, buffer, size, headers);
}

std::shared_ptr<CurlHttp::Result>
CurlXcap::XcapSession::putIfMatch(const Auid& auid, const char* buffer, size_t size, const std::string& etag,
                                  const std::string& documentPath, const std::string& nodeSelector,
                                  const CurlHttp::HttpHeaderList& additionalHeaders)
{
   std::string path = buildXcapPath(auid, documentPath, nodeSelector);
   HttpHeaderList headers = additionalHeaders;
   headers.push_back(chooseMimeType(auid, nodeSelector));
   return Session::putIfMatch(path, buffer, size, etag, headers);
}

std::shared_ptr<CurlHttp::Result>
CurlXcap::XcapSession::get(const Auid& auid, const std::string& documentPath,
                                             const std::string& nodeSelector,
                                             const CurlHttp::HttpHeaderList& additionalHeaders)
{
   std::string path = buildXcapPath(auid, documentPath, nodeSelector);
   return Session::get(path, additionalHeaders);
}

std::shared_ptr<CurlHttp::Result>
CurlXcap::XcapSession::getIfNoneMatch(const Auid& auid, const std::string& etag,
                                      const std::string& documentPath,
                                      const std::string& nodeSelector,
                                      const CurlHttp::HttpHeaderList& additionalHeaders)
{
   std::string path = buildXcapPath(auid, documentPath, nodeSelector);
   return Session::getIfNoneMatch(path, etag, additionalHeaders);
}

std::string
CurlXcap::XcapSession::buildXcapPath(const Auid& auid, const std::string& documentPath, const std::string& nodeSelector)
{
   //very inefficient--cache in session, efficient replacement
   std::string path = mCurlXcap.mXcapRoot;
   if (!path.empty() && path[path.length() - 1] != '/')
      path += "/";
   path += auid.name;
   if (!mCurlXcap.mIsGlobal)
   {
      path += "/";
      path += "users/";
      path += mCurlXcap.mUser;
      path += "/";
      path += documentPath;
   } else {
	   path += "/global";
	   if (documentPath.length() > 0) {
			path += "/";
			path += documentPath;
	   }
   }

   if (!nodeSelector.empty())
   {
      path += "/~~/";
      path += nodeSelector;
   }
   size_t pos;
   while ((pos = path.find("\\")) != std::string::npos)
   {
      path.replace(pos, 2, "/");
   }
   return path;
}


CurlXcap::HttpHeader
CurlXcap::XcapSession::chooseMimeType(const Auid& auid, const std::string& nodeSelector)
{
   static HttpHeader xcapEl("Content-Type", "application/xcap-el+xml");
   static HttpHeader xcapAtt("Content-Type", "application/xcap-att+xml");

   if (nodeSelector.empty())
   {
      return HttpHeader("Content-Type", auid.mimeType);
   }
   size_t atPos = nodeSelector.rfind('@');
   if (atPos == std::string::npos)
   {
      return xcapEl;
   }
   else
   {
      if(atPos == 0 || nodeSelector[atPos - 1] != '[')
      {
         return xcapEl;
      }
      else
      {
         return xcapAtt;
      }
   }
}
