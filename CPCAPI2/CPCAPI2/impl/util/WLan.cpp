#include "WLan.h"
#include <wlanapi.h>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE
#define INVALID_MAC     "01-00-5E-FF-FF-FF"

using namespace CPCAPI2::Utils;

cpc::string WLan::getBSSID()
{
   //exlude for now, needs wlanapi.lib from Windows SDK:
#if 0
   unsigned long dwMaxClient = 2;
   unsigned long dwCurVersion = 0;
   void* hClient = NULL;
   unsigned long dwResult = WlanOpenHandle(dwMaxClient, NULL, &dwCurVersion, &hClient);
   if (dwResult != ERROR_SUCCESS)
   {
      DebugLog(<< "WlanOpenHandle failed with error:" << dwResult);
      return "";
   }

   PWLAN_INTERFACE_INFO_LIST pIfList = NULL;
   PWLAN_INTERFACE_INFO pIfInfo = NULL;
   dwResult = WlanEnumInterfaces(hClient, NULL, &pIfList);
   if (dwResult != ERROR_SUCCESS)
   {
      DebugLog(<< "WlanEnumInterfaces failed with error: " << dwResult);
      return "";
   }

   for (int i = 0; i < (int)pIfList->dwNumberOfItems; i++)
   {
      pIfInfo = (WLAN_INTERFACE_INFO *)& pIfList->InterfaceInfo[i];
      if (pIfInfo->isState == wlan_interface_state_connected)
      {
         PWLAN_CONNECTION_ATTRIBUTES pConnectInfo = NULL;
         DWORD connectInfoSize = sizeof(WLAN_CONNECTION_ATTRIBUTES);
         WLAN_OPCODE_VALUE_TYPE opCode = wlan_opcode_value_type_invalid;

         dwResult = WlanQueryInterface(hClient,
            &pIfInfo->InterfaceGuid,
            wlan_intf_opcode_current_connection,
            NULL,
            &connectInfoSize,
            (PVOID *)&pConnectInfo,
            &opCode);

         if (dwResult != ERROR_SUCCESS)
         {
            DebugLog(<< "WlanQueryInterface failed with error: " << dwResult);
            return "";
         }

         char macAddress[18];
         for (int k = 0; k < sizeof(pConnectInfo->wlanAssociationAttributes.dot11Bssid); k++)
         {
            if (k == 5)
               snprintf(&macAddress[k*3], 3, "%.2X\0", pConnectInfo->wlanAssociationAttributes.dot11Bssid[k]);
            else
               snprintf(&macAddress[k*3], 4, "%.2X-", pConnectInfo->wlanAssociationAttributes.dot11Bssid[k]);
         }
         return cpc::string(macAddress);
      }
   }
#endif
   return "";
}
