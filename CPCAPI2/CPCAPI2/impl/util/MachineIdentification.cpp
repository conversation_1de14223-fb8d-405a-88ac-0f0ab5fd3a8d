﻿#if __APPLE__
#include "TargetConditionals.h"
#endif

#include "MachineIdentification.h"
#include <cpcapi2utils.h>
#ifdef _WIN32
#include "RegistryHelpers.h"
#include "HDDiskId.h"
#endif
#include "../util/DeviceInfo.h"
#include "../util/IpHelpers.h"

#include <vector>
#include <algorithm>
#include <boost/algorithm/string.hpp>

#if _WIN32 && !defined(WP8)
  #include <ObjBase.h>
  #include <atlbase.h>
  #include <comutil.h>
  #include <codecvt>
  #if !defined(WinRT)
    #include <Wbemidl.h>
  #endif
std::string wstringToUtf8(const wchar_t* in)
{
   std::string ret;
   try
   {
      std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>,wchar_t> convert;
      ret = convert.to_bytes(in);
   }
   catch(...)
   {
   }
   return ret;
}
std::string wstringToUtf8(const std::wstring& in)
{
   return wstringToUtf8(in.c_str());
}
#endif

#if __APPLE__
#if TARGET_OS_IPHONE
#else
#include "../util/Mac/CMacSecurityAccess.h"
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include "rutil/GenericIPAddress.hxx"
#include <net/if.h>
#include <sys/ioctl.h>
#include <sys/sockio.h>
#include <IOKit/IOKitLib.h>
#include <IOKit/network/IOEthernetInterface.h>
#include <IOKit/network/IOEthernetController.h>
#include <IOKit/network/IONetworkInterface.h>
#include <SystemConfiguration/SystemConfiguration.h>
#endif
#endif

#ifdef ANDROID
#include "JniHelper.h"
#endif

#if defined(__linux__)
#include <sys/ioctl.h>
#include <sys/utsname.h>
#include <net/if.h>
#if !defined(ANDROID)
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <ifaddrs.h>
#include <netpacket/packet.h>
#endif
#endif

#include <rutil/ssl/SHA1Stream.hxx>
#include <rutil/Random.hxx>

namespace CPCAPI2
{
#if _WIN32

class ScopedCoInitialize
{
public:
   // !jjg! NOTE: There's a nasty dependency here -- WebRTC's audio_device_core_win.h
   //             also calls ::CoInitializeEx(..) on the same thread, and so we need
   //             to make sure both spots are using COINIT_MULTITHREADED.
   ScopedCoInitialize() { hr = ::CoInitializeEx(NULL, COINIT_MULTITHREADED); }
   ~ScopedCoInitialize() { if SUCCEEDED(hr) ::CoUninitialize(); }
private:
   HRESULT hr;
};

#endif //_WIN32


#if __APPLE__
#if TARGET_OS_IPHONE
#else
    // routines to help out GetURLFromMacros()

    // Returns an iterator containing the primary (built-in) Ethernet interface. The caller is responsible for
    // releasing the iterator after the caller is done with it.
    static kern_return_t FindPrimaryEthernetInterface(io_iterator_t *outMatchingServices)
    {
        kern_return_t kernResult = KERN_FAILURE;
        CFMutableDictionaryRef matchingDict = NULL;
        CFMutableDictionaryRef propertyMatchDict = NULL;

        matchingDict = ::IOServiceMatching(kIOEthernetInterfaceClass);
        // Note that another option here would be:
        // matchingDict = IOBSDMatching("en0");

        if (NULL != matchingDict)
        {
            propertyMatchDict = ::CFDictionaryCreateMutable(
                                                            kCFAllocatorDefault,
                                                            0,
                                                            &kCFTypeDictionaryKeyCallBacks,
                                                            &kCFTypeDictionaryValueCallBacks
                                                            );

            if (NULL != propertyMatchDict)
            {
                // if you want all available MAC addresses, comment out this line
                ::CFDictionarySetValue(propertyMatchDict, CFSTR(kIOPrimaryInterface), kCFBooleanTrue);

                // Now add the dictionary containing the matching value for kIOPrimaryInterface to our main
                // matching dictionary. This call will retain propertyMatchDict, so we can release our reference
                // on propertyMatchDict after adding it to matchingDict.
                ::CFDictionarySetValue(
                                       matchingDict,
                                       CFSTR(kIOPropertyMatchKey),
                                       propertyMatchDict
                                       );
                ::CFRelease(propertyMatchDict);
            }
        }

        // IOServiceGetMatchingServices retains the returned iterator, so release the iterator when we're done with it.
        // IOServiceGetMatchingServices also consumes a reference on the matching dictionary so we don't need to release
        // the dictionary explicitly.
        kernResult = ::IOServiceGetMatchingServices(
                                                    kIOMasterPortDefault,
                                                    matchingDict,
                                                    outMatchingServices
                                                    );
        return kernResult;
    }

    static kern_return_t GetMACAddress(
                                       io_iterator_t inIntfIterator,
                                       std::string& outMACAddress
                                       )
    {
        io_object_t intfService;
        io_object_t controllerService;
        kern_return_t kernResult = KERN_FAILURE;
        UInt8 MACAddress[kIOEthernetAddressSize] = { 0 };

        // IOIteratorNext retains the returned object, so release it when we're done with it.
        while ( 0 != (intfService = ::IOIteratorNext(inIntfIterator)) )
        {
            CFTypeRef MACAddressAsCFData;

            // IONetworkControllers can't be found directly by the IOServiceGetMatchingServices call,
            // since they are hardware nubs and do not participate in driver matching. In other words,
            // registerService() is never called on them. So we've found the IONetworkInterface and will
            // get its parent controller by asking for it specifically.

            // IORegistryEntryGetParentEntry retains the returned object, so release it when we're done with it.
            kernResult = ::IORegistryEntryGetParentEntry(
                                                         intfService,
                                                         kIOServicePlane,
                                                         &controllerService
                                                         );

            if (KERN_SUCCESS == kernResult)
            {
                // Retrieve the MAC address property from the I/O Registry in the form of a CFData
                MACAddressAsCFData = ::IORegistryEntryCreateCFProperty(
                                                                       controllerService,
                                                                       CFSTR(kIOMACAddress),
                                                                       kCFAllocatorDefault,
                                                                       0
                                                                       );
                if (MACAddressAsCFData)
                {
                    //::CFShow(MACAddressAsCFData);

                    // Get the raw bytes of the MAC address from the CFData
                    ::CFDataGetBytes(
                                     reinterpret_cast<CFDataRef>(MACAddressAsCFData),
                                     CFRangeMake(0, kIOEthernetAddressSize),
                                     MACAddress
                                     );
                    ::CFRelease(MACAddressAsCFData);

                    char formattedAddress[3 * (kIOEthernetAddressSize + 1)] = { 0 };
                    snprintf(
                            formattedAddress,
                            3 * (kIOEthernetAddressSize + 1),
                            "%02X-%02X-%02X-%02X-%02X-%02X",
                            MACAddress[0],
                            MACAddress[1],
                            MACAddress[2],
                            MACAddress[3],
                            MACAddress[4],
                            MACAddress[5]
                            );

                    outMACAddress = formattedAddress;
                }

                // Done with the parent Ethernet controller object so we release it.
                ::IOObjectRelease(controllerService);
            }

            // Done with the Ethernet interface object so we release it.
            ::IOObjectRelease(intfService);
        }

        return kernResult;
    }
#endif
#endif

std::string MachineIdentification::GetHardwareId()
{
   std::string idstring;
   std::vector<std::string> allMacs;
   static std::string sHardwareID;
   if (!sHardwareID.empty())
      return sHardwareID;

#if _WIN32
   // try from registry
#if !defined(WinRT)
   sHardwareID = RegistryHelpers::ReadRegistryStringValue("hid");
#endif

   allMacs = GetAllMACs_Win32();

   if (!sHardwareID.empty())
   {
      // found saved value in registry, check if still valid on current hardware
      std::vector<std::string> allHdds = CPCAPI2::HDDiskId::GetAllHddInfo();
      if (CPCAPI2::RegistryHelpers::VerifyHardware(allHdds, allMacs))
         return sHardwareID;
   }

   std::sort(allMacs.begin(), allMacs.end());

   // Now changing this to be a hash of all the MACs and Hard Drives (hashes) found in windows land
   std::vector<std::string>::iterator iter = allMacs.begin();
   for(; iter < allMacs.end(); iter++)
   {
      idstring = idstring + *iter;
   }

#elif defined(ANDROID)
   cpc::string cpcId;
   DeviceInfo::getPlatformUniqueId(cpcId);
   idstring = cpcId.c_str();
#elif __APPLE__

#if TARGET_OS_IPHONE
   cpc::string cpcidstr;
   DeviceInfo::getPlatformUniqueId(cpcidstr);
   idstring = cpcidstr.c_str();
#else
   idstring = CSecurityAccess::instance()->getHardwareID().c_str();
#endif

#elif __linux__
   // use list of all MAC addresses on the system
   // this is the best we can do on Linux as other info requires root
   allMacs = GetAllLinuxMACs();

   std::sort(allMacs.begin(), allMacs.end());

   std::vector<std::string>::iterator iter = allMacs.begin();
   for (; iter < allMacs.end(); iter++)
   {
      idstring = idstring + *iter;
   }
#endif

   resip::SHA1Stream cHash;
   cHash << resip::Data(idstring.c_str(), idstring.size());
   sHardwareID = cHash.getHex().c_str();

   return sHardwareID;
}

#if __APPLE__ && TARGET_OS_IPHONE
#else
std::string MachineIdentification::ComputerName()
{
   std::string name = "Unknown Device (Unsupported Platform)";
#if defined(WP8)
  name = "Windows Phone 8.1 Device";

  WSADATA wsadata;
  int error = WSAStartup(MAKEWORD(2, 2), &wsadata);
  if (0 == error)
  {
    ADDRINFOA *result = nullptr;
    ADDRINFOA hints = {};
    hints.ai_flags = AI_CANONNAME;
    error = ::getaddrinfo("", "0", &hints, &result);
    if (0 == error)
      name = std::string(result->ai_canonname);

    ::freeaddrinfo(result);
  }
#elif defined(WinRT)
   Windows::Security::ExchangeActiveSyncProvisioning::EasClientDeviceInformation^ deviceInfo = ref new Windows::Security::ExchangeActiveSyncProvisioning::EasClientDeviceInformation();

   std::wstring buf = std::wstring(deviceInfo->FriendlyName->Begin(), deviceInfo->FriendlyName->Length());
   name = wstringToUtf8(buf);
#elif _WIN32
   wchar_t computerName[1024];
   DWORD computerNameSize = 1024;
   ::GetComputerNameW(computerName, &computerNameSize);
   computerName[computerNameSize] = 0;
   name = wstringToUtf8(computerName);
#elif __APPLE__
#if TARGET_OS_IPHONE
   name = "";
#else
	name = CSecurityAccess::instance()->getComputerName().c_str();
#endif
#elif defined(ANDROID)
   name = CPCAPI2::Jni::GetStaticStringField("android/os/Build", "MODEL");;
#elif defined(__linux__)
   struct utsname buf;
   if(uname(&buf) == 0)
      name = buf.nodename;
#endif

   return name;
}
#endif

#if defined(WinRT)
std::vector<std::string> MachineIdentification::GetAllMACs_Win32(){
	// Not actually a MAC, but it identifies a single adapter like a MAC

	std::vector<std::string> allMACs;
	std::string mac = "";

	Windows::Foundation::Collections::IVectorView<Windows::Networking::Connectivity::ConnectionProfile^>^ adapters = Windows::Networking::Connectivity::NetworkInformation::GetConnectionProfiles();
	for (int i = 0; i < adapters->Size; i++) {
		Windows::Networking::Connectivity::ConnectionProfile^ adapter = adapters->GetAt(i);

    Platform::String^ id = adapter->NetworkAdapter->NetworkAdapterId.ToString();
    int length = WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), NULL, 0, 0, 0) + 2;
    LPSTR mbstr = new char[length];
    WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), mbstr, length, 0, 0);

    mac.assign(mbstr);

    allMACs.push_back(mac);
	}

	return allMACs;
}
#elif _WIN32

#define XMLCHAR_CAST(x) reinterpret_cast<const xmlChar*>(x)

std::vector<std::string> MachineIdentification::GetAllMACs_Win32(bool hash){

   static std::vector<std::string> sAllMACsHash;
   if (hash && !sAllMACsHash.empty())
      return sAllMACsHash;

   std::vector<std::string> allMACs;
   std::string mac = "";

   ScopedCoInitialize comInit;

   IWbemLocator *pLoc = 0;
   HRESULT hr;

   // connect to WMI
   hr = CoCreateInstance(CLSID_WbemLocator, 0,
      CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID *) &pLoc);

   if ( FAILED( hr ) )
   {
      return allMACs;
   }

   // connect to local service with current credentials
   IWbemServices *pSvc = 0;

   hr =  pLoc->ConnectServer(
      BSTR(L"root\\cimv2"),
      NULL, NULL, 0, NULL, 0, 0, &pSvc);

   if (FAILED(hr))
   {
      pLoc->Release();
      return allMACs;
   }
   if ( SUCCEEDED( hr ) )
   {
      //set security

      hr = CoSetProxyBlanket(pSvc,
         RPC_C_AUTHN_WINNT,
         RPC_C_AUTHZ_NONE,
         NULL,
         RPC_C_AUTHN_LEVEL_CALL,
         RPC_C_IMP_LEVEL_IMPERSONATE,
         NULL,
         EOAC_NONE
         );

      if (FAILED(hr))
      {
         pSvc->Release();
         pLoc->Release();
         return allMACs;      // Program has failed.
      }

      // execute a query Get all Physical adapters.
      IEnumWbemClassObject *enumerator = 0;
      hr = pSvc->ExecQuery(
         BSTR(L"WQL"),
         BSTR(L"SELECT * FROM Win32_NetworkAdapter"),
         WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY,
         NULL,
         &enumerator);
      if ( hr == 0  )
      {
         IWbemClassObject *networkadapter = NULL;
         ULONG retcnt;
         std::string pnpID;

         while (enumerator)
         {
            retcnt = 0;
            hr = enumerator->Next(WBEM_INFINITE, 1L, &networkadapter, &retcnt);

            if (FAILED(hr) || (hr == S_FALSE) || retcnt == 0)
            {
               break;
            }

            //get PNP id and check for USB or PCI
            VARIANT var_val_netpnpdeviceid;
            hr = networkadapter->Get(L"PNPDeviceID", 0, &var_val_netpnpdeviceid, NULL, NULL);
            if ( SUCCEEDED( hr ) )
            {
               if (V_VT(&var_val_netpnpdeviceid) != VT_NULL)
               {
                  std::wstring wpnpID(var_val_netpnpdeviceid.bstrVal, SysStringLen(var_val_netpnpdeviceid.bstrVal));
                  pnpID = wstringToUtf8(wpnpID);
               }
            }
            VariantClear(&var_val_netpnpdeviceid);
            std::string prefix = pnpID.substr(0,3);
            resip::SHA1Stream cHash;
            // check for PCI, USB, PCMCIA connected MAC addresses. First one is primary.
            if (prefix == "PCI" || prefix == "USB" || prefix == "PCM") //get the MAC now
            {
               VARIANT var_val_mac;
               hr = networkadapter->Get(L"MACAddress", 0, &var_val_mac, NULL, NULL);
               if ( SUCCEEDED( hr ) )
               {
                  if (V_VT(&var_val_mac) != VT_NULL)
                  {
                     std::wstring wmac(var_val_mac.bstrVal, var_val_mac.bstrVal == NULL ? 0 : SysStringLen(var_val_mac.bstrVal) + 1);
                     mac = wstringToUtf8(wmac);
                     if (!mac.empty())
                     {
                        if (hash)
                        {
                           boost::ireplace_all(mac, ":", "-");

                           cHash << resip::Data(mac.data(), mac.size());

                           mac = cHash.getHex().c_str();
                        }

                        // WRITE MAC TO XML HERE!
                        allMACs.push_back(mac);
                     }
                  }
               }
               VariantClear(&var_val_mac);
            }

            networkadapter->Release();
         }
         enumerator->Release();
      }
   }

   pSvc->Release();
   pLoc->Release();

   if (hash)
   {
      // save for future use
      sAllMACsHash = allMACs;
   }

   return allMACs;

}
#endif

std::string MachineIdentification::GetLocalMACAddress()
{
   std::string mac = "";

#if defined(WinRT)
   // Not actually a MAC, but it identifies a single adapter like a MAC
   Windows::Foundation::Collections::IVectorView<Windows::Networking::Connectivity::ConnectionProfile^>^ adapters = Windows::Networking::Connectivity::NetworkInformation::GetConnectionProfiles();
   if (0 < adapters->Size) {
	   Windows::Networking::Connectivity::ConnectionProfile^ adapter = adapters->First()->Current;

     Platform::String^ id = adapter->NetworkAdapter->NetworkAdapterId.ToString();
     int length = WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), NULL, 0, 0, 0) + 2;
     LPSTR mbstr = new char[length];
     WideCharToMultiByte(CP_ACP, 0, id->Begin(), id->Length(), mbstr, length, 0, 0);

     mac.assign(mbstr);
   }
#elif _WIN32
   ScopedCoInitialize comInit;

   IWbemLocator *pLoc = 0;
   HRESULT hr;

   // connect to WMI
   hr = CoCreateInstance(CLSID_WbemLocator, 0,
      CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID *) &pLoc);

   if ( FAILED( hr ) )
   {
      return mac;
   }

   // connect to local service with current credentials
   IWbemServices *pSvc = 0;

   hr =  pLoc->ConnectServer(
      BSTR(L"root\\cimv2"),
      NULL, NULL, 0, NULL, 0, 0, &pSvc);

   if (FAILED(hr))
   {
      pLoc->Release();
      return mac;
   }
   if ( SUCCEEDED( hr ) )
   {
      //set security

      hr = CoSetProxyBlanket(pSvc,
         RPC_C_AUTHN_WINNT,
         RPC_C_AUTHZ_NONE,
         NULL,
         RPC_C_AUTHN_LEVEL_CALL,
         RPC_C_IMP_LEVEL_IMPERSONATE,
         NULL,
         EOAC_NONE
         );

      if (FAILED(hr))
      {
         pSvc->Release();
         pLoc->Release();
         return mac;      // Program has failed.
      }

      // execute a query Get all Physical adapters.
      IEnumWbemClassObject *enumerator = 0;
      hr = pSvc->ExecQuery(
         BSTR(L"WQL"),
         BSTR(L"SELECT * FROM Win32_NetworkAdapter"),
         WBEM_FLAG_RETURN_IMMEDIATELY,
         NULL,
         &enumerator);
      if ( hr == 0  )
      {
         IWbemClassObject *networkadapter = NULL;
         ULONG retcnt;
         std::wstring pnpID;

         while (enumerator)
         {
            retcnt = 0;
            hr = enumerator->Next(WBEM_INFINITE, 1L, reinterpret_cast<IWbemClassObject**>( &networkadapter ), &retcnt);

            if (FAILED(hr) || (hr == S_FALSE) || retcnt == 0)
            {
               break;
            }
            //get PNP id and check for USB or PCI
            VARIANT var_val_netpnpdeviceid;
            hr = networkadapter->Get(L"PNPDeviceID", 0, &var_val_netpnpdeviceid, NULL, NULL);
            if ( SUCCEEDED( hr ) )
            {
               if (V_VT(&var_val_netpnpdeviceid) != VT_NULL)
                  pnpID = std::wstring(var_val_netpnpdeviceid.bstrVal, SysStringLen(var_val_netpnpdeviceid.bstrVal));
            }
            VariantClear(&var_val_netpnpdeviceid);
            std::wstring prefix = pnpID.substr(0,3);
            // check for PCI, USB, PCMCIA connected MAC addresses. First one is primary.
            if (prefix == L"PCI" || prefix == L"USB" || prefix == L"PCM") //get the MAC now
            {
               VARIANT var_val_mac;
               hr = networkadapter->Get(L"MACAddress", 0, &var_val_mac, NULL, NULL);
               if ( SUCCEEDED( hr ) )
               {
                  if (V_VT(&var_val_mac) != VT_NULL)
                  {
                     std::wstring wmac(var_val_mac.bstrVal, SysStringLen(var_val_mac.bstrVal));
                     if (!wmac.empty())
                     {
                        boost::ireplace_all(wmac, L":", L"-");
                        mac = wstringToUtf8(wmac);
                        break;
                     }
                  }
               }
               VariantClear(&var_val_mac);
            }
         }
      }
   }

   pSvc->Release();
   pLoc->Release();

   //if (mac.empty())
   //{
   //   mac = OldWindowsGetLocalMAC();
   //}

#elif __APPLE__
#if TARGET_OS_IPHONE
   cpc::string uniqueId;
   DeviceInfo::getPlatformUniqueId(uniqueId);
   mac = uniqueId.c_str();
#else
   // assume here proper MAC address to send is from the primary (builtin) interface
   io_iterator_t intfIterator = 0;
   kern_return_t kernResult = FindPrimaryEthernetInterface(&intfIterator);
//   check(KERN_SUCCESS == kernResult); //TODO: [AB] check is not defined
   if (KERN_SUCCESS == kernResult)
   {
      /*kernResult = */GetMACAddress(
         intfIterator,
         mac
      );
//      check(KERN_SUCCESS == kernResult); //TODO: [AB] check is not defined
   }
   ::IOObjectRelease(intfIterator);
#endif
#elif defined(__linux__)
   static const char *ifaces[] = {
           "eth0", "wlan0",
           "eth1", "wlan1",
           "eth2", "wlan2",
           "eth3", "wlan3",
           0
   };

   for(int i = 0; ifaces[i] != 0; i++){
      int fd;
      struct ifreq ifr;

      fd = socket(AF_INET, SOCK_DGRAM, 0);

      ifr.ifr_addr.sa_family = AF_INET;
         strncpy(ifr.ifr_name, ifaces[i], IFNAMSIZ-1);

         if(ioctl(fd, SIOCGIFHWADDR, &ifr) == 0){
            char str[1024];
            snprintf(str, 1024, "%02X-%02X-%02X-%02X-%02X-%02X",
               (unsigned char)ifr.ifr_hwaddr.sa_data[0],
               (unsigned char)ifr.ifr_hwaddr.sa_data[1],
               (unsigned char)ifr.ifr_hwaddr.sa_data[2],
               (unsigned char)ifr.ifr_hwaddr.sa_data[3],
               (unsigned char)ifr.ifr_hwaddr.sa_data[4],
               (unsigned char)ifr.ifr_hwaddr.sa_data[5]);
            mac = str;
            break;
         }

         close(fd);
   }
#endif

   return mac;
}

std::string MachineIdentification::GetIPAddress()
{
   resip::Data ipData = IpHelpers::getPreferredLocalIpAddress();
   return std::string(ipData.data());
}

std::string MachineIdentification::GetMotherboardInfo()
{
   std::string retManufacturer;
#if defined(WP8)
   // Should use DeviceStatus.DeviceName, but it isn't available through C++
   retManufacturer = "Microsoft WP8 Device";
#elif defined(WinRT)
   Windows::Security::ExchangeActiveSyncProvisioning::EasClientDeviceInformation^ deviceInfo = ref new Windows::Security::ExchangeActiveSyncProvisioning::EasClientDeviceInformation();

   std::wstring buf = std::wstring(deviceInfo->SystemManufacturer->Begin(), deviceInfo->SystemManufacturer->Length());
   retManufacturer = wstringToUtf8(buf);

   buf = std::wstring(deviceInfo->SystemProductName->Begin(), deviceInfo->SystemProductName->Length());
   retManufacturer += wstringToUtf8(buf);

   buf = std::wstring(deviceInfo->SystemHardwareVersion->Begin(), deviceInfo->SystemHardwareVersion->Length());
   retManufacturer += wstringToUtf8(buf);
#elif _WIN32

   ScopedCoInitialize comInit;

   HRESULT hr;

   // connect to WMI
   CComPtr< IWbemLocator > locator;
   hr = CoCreateInstance( CLSID_WbemAdministrativeLocator, NULL, CLSCTX_INPROC_SERVER | CLSCTX_LOCAL_SERVER,
                          IID_IWbemLocator, reinterpret_cast< void** >( &locator ) );
   if ( FAILED( hr ) )
   {
      //assert(0);
      return retManufacturer;
   }

   // connect to local service with current credentials
   CComPtr< IWbemServices > service;
   hr = locator->ConnectServer( L"ROOT\\CIMV2", NULL, NULL, NULL, 0, NULL, NULL, &service );
   if ( SUCCEEDED( hr ) )
   {
		// execute a query
      CComPtr< IEnumWbemClassObject > enumerator;
      hr = service->ExecQuery( L"WQL", L"SELECT * FROM Win32_BaseBoard", WBEM_FLAG_FORWARD_ONLY, NULL, &enumerator );
      if ( SUCCEEDED( hr ) )
      {
         // read the first instance from the enumeration (only one motherboard in a machines)
         CComPtr< IWbemClassObject > motherboard = NULL;
         ULONG retcnt;
         hr = enumerator->Next( WBEM_INFINITE, 1L, reinterpret_cast<IWbemClassObject**>( &motherboard ), &retcnt );
         if ( SUCCEEDED( hr ) )
         {
            if ( retcnt > 0 )
            {
               // now extract a property value
               _variant_t var_val_model;
               _variant_t var_val_name;
               _variant_t var_val_manufacturer;
               _variant_t var_val_product;
               std::string cSeparator = "::";

               hr = motherboard->Get( L"Manufacturer", 0, &var_val_manufacturer, NULL, NULL );
               if ( SUCCEEDED( hr ) )
               {
                  std::wstring wManufac;
                  if (V_VT(&var_val_manufacturer) != VT_NULL)
                  {
                     wManufac = std::wstring(var_val_manufacturer.bstrVal, SysStringLen(var_val_manufacturer.bstrVal));
                     retManufacturer = wstringToUtf8(wManufac);
                  }
               }

               retManufacturer += cSeparator;

               hr = motherboard->Get( L"Name", 0, &var_val_name, NULL, NULL );
               if ( SUCCEEDED( hr ) )
               {
                  std::wstring wstr_val_name;
                  if (V_VT(&var_val_name) != VT_NULL)
                  {
                     wstr_val_name = std::wstring(var_val_name.bstrVal, SysStringLen(var_val_name.bstrVal));
                     retManufacturer += wstringToUtf8(wstr_val_name);
                  }
               }

               retManufacturer += cSeparator;

               hr = motherboard->Get( L"Model", 0, &var_val_model, NULL, NULL );
               if ( SUCCEEDED( hr ) )
               {
                  std::wstring wstr_val_model;
                  if (V_VT(&var_val_model) != VT_NULL)
                  {
                     wstr_val_model = std::wstring(var_val_model.bstrVal, SysStringLen(var_val_model.bstrVal));
                     retManufacturer += wstringToUtf8(wstr_val_model);
                  }
               }

               retManufacturer += cSeparator;

               hr = motherboard->Get( L"Product", 0, &var_val_product, NULL, NULL );
               if ( SUCCEEDED( hr ) )
               {
                  std::wstring wstr_val_prod;
                  if (V_VT(&var_val_product) != VT_NULL)
                  {
                     wstr_val_prod = std::wstring(var_val_product.bstrVal, SysStringLen(var_val_product.bstrVal));
                     retManufacturer += wstringToUtf8(wstr_val_prod);
                  }
               }
            }
            else
            {
               assert(false);
            }
         }
         else
         {
            assert(false);
         }
      }
      else
      {
         //XS_ASSERT_FAILURE("Query failed");
      }
   }
   else
   {
      assert(false);
   }
#endif

   return retManufacturer;
}

std::string MachineIdentification::GetHarddiskPnpId()
{
   std::string pnpID;

#ifdef _WIN32
   ScopedCoInitialize comInit;

   HRESULT hr;

   // connect to WMI
   CComPtr< IWbemLocator > locator;
   hr = CoCreateInstance( CLSID_WbemAdministrativeLocator, NULL, CLSCTX_INPROC_SERVER, IID_IWbemLocator, reinterpret_cast< void** >( &locator ) );
   if ( FAILED( hr ) )
   {
//      XS_ASSERT_FAILURE("Instantiation of IWbemLocator failed");
      return pnpID;
   }

   // connect to local service with current credentials
   CComPtr< IWbemServices > service;
   hr = locator->ConnectServer(L"root\\cimv2", NULL, NULL, NULL, WBEM_FLAG_CONNECT_USE_MAX_WAIT, NULL, NULL, &service);
   if ( SUCCEEDED( hr ) )
   {
		// execute a query
      CComPtr< IEnumWbemClassObject > enumerator;
      hr = service->ExecQuery(L"WQL", L"SELECT * FROM Win32_DiskDrive", WBEM_FLAG_FORWARD_ONLY, NULL, &enumerator);
      if ( SUCCEEDED( hr ) )
      {
//         SUA_TRACES_DEBUG("getting a hard disk...");

         CComPtr< IWbemClassObject > harddisk = NULL;
         ULONG retcnt;
         ULONG index = 0xffff;

         while (enumerator)
         {
            hr = enumerator->Next(WBEM_INFINITE, 1L, reinterpret_cast<IWbemClassObject**>( &harddisk ), &retcnt);
            if (retcnt == 0)
            {
               break;
            }

            VARIANT var_val_index;
            hr = harddisk->Get(L"Index", 0, &var_val_index, NULL, NULL);
            if ( SUCCEEDED( hr ) )
            {
               if (V_VT(&var_val_index) != VT_NULL) index = var_val_index.uintVal;
            }
            VariantClear(&var_val_index);

            if (index == 0)
            {
               // First physical disk?
               break;
            }

            harddisk = NULL;
         }

         if (index == 0)
         {
            VARIANT var_val_pnpdeviceid;
            hr = harddisk->Get(L"PNPDeviceID", 0, &var_val_pnpdeviceid, NULL, NULL);
            if ( SUCCEEDED( hr ) )
            {
               if (V_VT(&var_val_pnpdeviceid) != VT_NULL) pnpID = std::string((LPCSTR) var_val_pnpdeviceid.bstrVal);
            }
            VariantClear(&var_val_pnpdeviceid);
         }
      }
      else
      {
         //XS_ASSERT_FAILURE("Query failed");
      }
   }
   else
   {
//      XS_ASSERT_FAILURE("Couldn't connect to service");
   }
#elif defined(__APPLE__) && !defined(TARGET_OS_IPHONE)
	// harddiscs on MAC do not expose an pnpID so we use the machine type insted.
	pnpID = (LPCSTR)CSecurityAccess::instance()->getMachineType().c_str();
#endif

//   SUA_TRACES_DEBUG("got a hard disk" << pnpID);
   return pnpID;
}

#if defined(__linux__) && !defined(ANDROID)
std::vector<std::string>
MachineIdentification::GetAllLinuxMACs()
{
   std::vector<std::string> allMACs;
   struct ifaddrs *pIfaddrs = NULL;
   struct sockaddr_ll *sll = NULL;

   if (getifaddrs(&pIfaddrs) < 0)
      return allMACs;

   struct ifaddrs *pIface = pIfaddrs;
   for (; pIface; pIface = pIface->ifa_next)
   {
      // ignore some useless interfaces
      if (!pIface->ifa_addr ||
          (pIface->ifa_flags & IFF_LOOPBACK) ||
          (pIface->ifa_addr->sa_family != AF_PACKET))
         continue;

      sll = (struct sockaddr_ll *)pIface->ifa_addr;
      if (sll)
      {
         std::string mac;
         char str[1024];
         snprintf(str, 1024, "%02X-%02X-%02X-%02X-%02X-%02X",
                    sll->sll_addr[0],
                    sll->sll_addr[1],
                    sll->sll_addr[2],
                    sll->sll_addr[3],
                    sll->sll_addr[4],
                    sll->sll_addr[5]);
         mac = str;
         allMACs.push_back(mac);
      }
   }

   freeifaddrs(pIfaddrs);

   return allMACs;
}
#endif // __linux__

}
