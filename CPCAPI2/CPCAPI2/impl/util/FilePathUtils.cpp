#include "FilePathUtils.h"
#include <filesystem>

namespace CPCAPI2
{

bool FilePathUtils::getCABundlePath(std::string& result)
{
#if (__linux__ && !(ANDROID)) 
    // These paths are taken from curl's CMakefile:
    std::vector<std::string> bundles = {  "/etc/ssl/certs/ca-certificates.crt", 
                                                               "/etc/pki/tls/certs/ca-bundle.crt",
                                                               "/usr/share/ssl/certs/ca-bundle.crt",
                                                               "/usr/local/share/certs/ca-root-nss.crt",
                                                               "/etc/ssl/cert.pem"
                                                            };
   for(std::vector<std::string>::iterator it = bundles.begin(); it != bundles.end(); it++)
   {
      const std::filesystem::path bundle{*it};
      if(std::filesystem::exists(bundle))
      {
         result = bundle.string();
         return true;
      }
   }
#endif
   return false;
}

}
