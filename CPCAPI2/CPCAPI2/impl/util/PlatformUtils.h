#pragma once

#ifndef __CPCAPI2_PLATFORMUTILS_H__
#define __CPCAPI2_PLATFORMUTILS_H__

#include <string>

#ifdef ANDROID
#include <jni.h>
#endif

#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

namespace CPCAPI2
{
   namespace PlatformUtils
   {
      typedef enum OSType
      {
         OSType_Windows, // Desktop
         OSType_OSX,     // Desktop
         OSType_Linux,   // Desktop
         OSType_Android, // Mobile
         OSType_iOS,     // Mobile
         OSType_Other    // Anything else
      } OSType;

      /**
      * Information pertaining to the operating system
      */
      typedef struct OSInfo
      {
         /**
         * The Operating System Type
         */
         OSType osType;

         /**
         * The version of the operating system (usually in dotted decimal notation)
         */
         std::string osVersion;

         OSInfo() : osType(OSType_Windows) {}
      } OSInfo;

      /**
      * Enum representing different form factors which could influence StrettoProvisioning.
      */
      typedef enum DeviceFormFactor
      {
         DeviceFormFactor_Phone,
         DeviceFormFactor_Tablet,
         DeviceFormFactor_Computer // Includes desktop + laptop
      } DeviceFormFactor;

      typedef struct DeviceInfo
      {
         /**
         * Device Model Name (set to "" for desktop/laptops)
         */
         std::string deviceModel;

         /**
         * Includes the form factor of the device (defaults to "computer")
         */
         DeviceFormFactor deviceFormFactor;

         DeviceInfo() : deviceFormFactor(DeviceFormFactor_Computer) {}
      } DeviceInfo;

      typedef struct CpuArchInfo
      {
         /* String representing the CPU architecture that the SDK
          * running was built for. Empty string if unknown.
          */
         std::string buildTimeCpuArch;
         /* String representing the CPU architecture that the SDK
          * is currently running on. Empty string if unknown.
          */
         std::string runTimeCpuInfo;
      } CpuArchInfo;

      /**
       * Definition for platform-specific static methods which are required in
       * order to fill-in some provisioning information (this is done as a
       * convenience to the SDK user). Implementation of all of these methods
       * is left in platform-specific files which are only included with each
       * platform.
       */
      class PlatformUtils
      {
      public:

         /**
          * Returns true if OSInfo structure could be filled, false otherwise.
          */
         static bool getOSInfo( OSInfo& outOSInfo );

         /**
          * Returns true if the device info was filled, false otherwise.
          */
         static bool getDeviceInfo( DeviceInfo& outDeviceInfo );

         static bool getCpuArchInfo( CpuArchInfo& outCpuArchInfo );

#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)
         static void disableMacOsAppNap();
#endif
      };
   }
}

#endif // __CPCAPI2_STRETTOPROVISIONING_PLATFORMUTILS_H__
