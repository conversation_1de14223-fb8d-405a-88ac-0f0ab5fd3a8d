#ifdef __APPLE__
#include <TargetConditionals.h>
#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE == 1

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#include <string>
#include "DeviceInfo.h"

@class SimpleKeychainUserPass;

@interface SimpleKeychain : NSObject

+ (void)save:(NSString *)service data:(id)data;
+ (id)load:(NSString *)service;
+ (void)delete:(NSString *)service;

@end

@implementation SimpleKeychain

+ (NSMutableDictionary *)getKeychainQuery:(NSString *)service {
   return [NSMutableDictionary dictionaryWithObjectsAndKeys:
           (id)kSecClassGenericPassword, (id)kSecClass,
           service, (id)kSecAttrService,
           service, (id)kSecAttrAccount,
           (id)kSecAttrAccessibleAfterFirstUnlock, (id)kSecAttrAccessible,
           nil];
}

+ (void)save:(NSString *)service data:(id)data {
   NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
   SecItemDelete((CFDictionaryRef)keychainQuery);
   [keychainQuery setObject:[NSKeyedArchiver archivedDataWithRootObject:data] forKey:(id)kSecValueData];
   SecItemAdd((CFDictionaryRef)keychainQuery, NULL);
}

+ (id)load:(NSString *)service {
   id ret = nil;
   NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
   [keychainQuery setObject:(id)kCFBooleanTrue forKey:(id)kSecReturnData];
   [keychainQuery setObject:(id)kSecMatchLimitOne forKey:(id)kSecMatchLimit];
   CFDataRef keyData = NULL;
   if (SecItemCopyMatching((CFDictionaryRef)keychainQuery, (CFTypeRef *)&keyData) == noErr) {
      @try {
         ret = [NSKeyedUnarchiver unarchiveObjectWithData:(__bridge NSData *)keyData];
      }
      @catch (NSException *e) {
         NSLog(@"Unarchive of %@ failed: %@", service, e);
      }
      @finally {}
   }
   if (keyData) CFRelease(keyData);
   return ret;
}

+ (void)delete:(NSString *)service {
   NSMutableDictionary *keychainQuery = [self getKeychainQuery:service];
   SecItemDelete((CFDictionaryRef)keychainQuery);
}

@end

namespace CPCAPI2
{
int DeviceInfo::getPlatformUniqueId(cpc::string& outParam)
{
   NSString* storedId = [SimpleKeychain load:@"CPCAPI2_PlatformUniqueId"];
   if (storedId == nil) {
      NSUUID *uuid = [UIDevice currentDevice].identifierForVendor;
      NSString *string = [uuid UUIDString];
      [SimpleKeychain save:@"CPCAPI2_PlatformUniqueId" data:string];
      outParam = [[string lowercaseString] UTF8String];
   }
   else {
      outParam = [[storedId lowercaseString] UTF8String];
   }
   return 0;
}
}
#endif // defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE == 1
#endif