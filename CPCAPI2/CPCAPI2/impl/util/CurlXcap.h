#ifndef _CURL_XCAP_H_
#define _CURL_XCAP_H_

#include "CurlHttp.h"

namespace xten
{

class CurlXcap: public CurlHttp
{
   public:
      //if it's global username & password is only for authentication, otherwise
      //the document selector included the user
      CurlXcap(const std::string& hostUri, const std::string& xcapRoot, const std::string& user,
               const std::string& pass, const std::string& authuser, const std::string& authpass, const std::string& clientCert, const std::string& clientCertType ,bool global=false);
      virtual ~CurlXcap() {};

      //may be extended to deal with Auid specific processing

      static std::string EmptyNodeSelector;

      class Auid
      {
         public:
            std::string name;
            std::string mimeType;
      };

      class XcapSession : public CurlHttp::Session
      {
         public:
            XcapSession(CurlXcap& neonXcap);
            virtual ~XcapSession() {};

            virtual std::shared_ptr<Result> deleteUri(const Auid& auid, const std::string& documentPath,
                                                const std::string& nodeSelector,
                                                const CurlHttp::HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);

            virtual std::shared_ptr<Result> put(const Auid& auid, const char* buffer, size_t size,
                                const std::string& documentPath, const std::string& nodeSelector = EmptyNodeSelector,
                                const CurlHttp::HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);

            virtual std::shared_ptr<Result> putIfMatch(const Auid& auid, const char* buffer, size_t size, const std::string& etag,
                                       const std::string& documentPath, const std::string& nodeSelector = EmptyNodeSelector,
                                       const CurlHttp::HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);

            virtual std::shared_ptr<Result> get(const Auid& auid, const std::string& documentPath,
                                const std::string& nodeSelector = CurlXcap::EmptyNodeSelector,
                                const CurlHttp::HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);

            virtual std::shared_ptr<Result> getIfNoneMatch(const Auid& auid, const std::string& etag,
                                           const std::string& documentPath,
                                           const std::string& nodeSelector = EmptyNodeSelector,
                                           const CurlHttp::HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);

            std::string buildXcapPath(const Auid& auid, const std::string& documentPath, const std::string& nodeSelector);

         private:
            CurlXcap& mCurlXcap;
            HttpHeader chooseMimeType(const Auid& auid, const std::string& nodeSelector);
      };
   private:
      friend class XcapSession;
      bool mIsGlobal;
      std::string mXcapRoot;
};

}
#endif
