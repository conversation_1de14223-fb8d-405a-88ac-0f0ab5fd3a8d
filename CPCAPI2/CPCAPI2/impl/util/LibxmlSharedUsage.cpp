#include "LibxmlSharedUsage.h"
#include "../util/cpc_logger.h"

#include <rutil/Mutex.hxx>
#include <libxml/parser.h>

#include <xmlsec/xmlsec.h>
#include <xmlsec/crypto.h>
#include <xmlsec/errors.h>

#include <sstream>
#include <memory>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

using namespace CPCAPI2;

static resip::Mutex sLibxmlSharedUsageMutex;
static int sLibxmlSharedUsageCount = 0;

void LibxmlSharedUsage::addRef()
{
   sLibxmlSharedUsageMutex.lock();

   ++sLibxmlSharedUsageCount;
   if (sLibxmlSharedUsageCount == 1)
   {
      xmlInitParser();
   }

   sLibxmlSharedUsageMutex.unlock();
}

void LibxmlSharedUsage::release()
{
   sLibxmlSharedUsageMutex.lock();

   --sLibxmlSharedUsageCount;
   if (sLibxmlSharedUsageCount == 0)
   {
      xmlCleanupParser();
   }

   sLibxmlSharedUsageMutex.unlock();
}




void myXmlSecErrorsCallback(const char* file,
						    		 int line,
				    				 const char* func,
								    const char* errorObject,
								    const char* errorSubject,
								    int reason,
								    const char* msg)
{
   std::stringstream ss;

   ss << "xmlSecErrorsCallBack: "
      << " " << file << ":" << line << " (" << func << ") + "
      << "Object: " << (errorObject ? errorObject : "") << " + "
      << "Subject: " << (errorSubject ? errorSubject : "") << " + "
      << "Reason: " << reason << " + "
      << "Msg: " << (msg ? msg : "");

   ErrLog(<< std::string(ss.str().c_str()));
   return;
}

// moved from XmlSignatureVerify.cpp
class XmlSecLifetime
{
public:
   XmlSecLifetime()
   {
      if (xmlSecInit() < 0 ||
          xmlSecCheckVersion() != 1 ||
#ifdef XMLSEC_CRYPTO_DYNAMIC_LOADING
          xmlSecCryptoDLLoadLibrary(BAD_CAST XMLSEC_CRYPTO) < 0 ||
#endif
          xmlSecCryptoAppInit(0) < 0 ||
          xmlSecCryptoInit() < 0)
      {
         ErrLog(<< "Cannot initialize XML security library");
      }
      else
      {
         mIsInitialized = true;
      }

      xmlSecErrorsSetCallback(&myXmlSecErrorsCallback);
   }

   ~XmlSecLifetime()
   {
      xmlSecCryptoShutdown();
      xmlSecCryptoAppShutdown();
      xmlSecShutdown();
   }

private:
   bool mIsInitialized;
};


static resip::Mutex sXmlSecSharedUsageMutex;
static int sXmlSecSharedUsageCount = 0;
static XmlSecLifetime* sXmlSecLifetime;

void XmlSecSharedUsage::addRef()
{
   // moved comment from XmlSignatureVerify.cpp:
   // Changed the implementation of this class for a lazy initialization of XmlSec instead
   // of being performed during the loading of the shared library (i.e. in the constructor)
   // which is causing a deadlock on Android versions 4.0.X.

   resip::Lock lock(sXmlSecSharedUsageMutex);

   ++sXmlSecSharedUsageCount;
   if (sXmlSecSharedUsageCount == 1)
   {
      sXmlSecLifetime = new XmlSecLifetime();
   }
}

void XmlSecSharedUsage::releaseRef()
{
   resip::Lock lock(sXmlSecSharedUsageMutex);

   --sXmlSecSharedUsageCount;
   if (sXmlSecSharedUsageCount == 0)
   {
      // previous version used a unique_ptr instead; however this can
      // result in crashes if the app does not shutdown the SDK before
      // process exit (sXmlSecLifetime gets released during global static
      // cleanup, and we have the same problem as originally in OBELISK-5731).
      // instead. we'll purposely leak XmlSecLifetime if the app neglects to
      // shut down the SDK before process exit, to avoid crashes.
      delete sXmlSecLifetime;
   }
}
