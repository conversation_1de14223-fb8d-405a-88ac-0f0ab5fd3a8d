
#include "DnsClient.h"
#include <sstream>

#include <rutil/dns/DnsStub.hxx>
#include <rutil/dns/QueryTypes.hxx>
#include <rutil/Timer.hxx>
#include <rutil/DnsUtil.hxx>
#include <cpcapi2utils.h>
#include "../util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

#define DNS_PROBE_TARGET "secure.counterpath.com"

using namespace CPCAPI2;
using namespace CPCAPI2::Utils;


static void SleepMs(int msecs) {
#ifdef _WIN32
   Sleep(msecs);
#else
   struct timespec short_wait;
   struct timespec remainder;
   short_wait.tv_sec = msecs / 1000;
   short_wait.tv_nsec = (msecs % 1000) * 1000 * 1000;
#if defined(ANDROID)
   clock_nanosleep(CLOCK_BOOTTIME, 0, &short_wait, &remainder);
#else
   nanosleep(&short_wait, &remainder);
#endif
#endif
}

DnsClient::DnsClient(const resip::DnsStub::DnsSettings& dnsSettings) :
   mOnDnsResultSrvCalled(false),
   mOnDnsResultHostACalled(false),
   mOnDnsResultHostAAAACalled(false),
   mDnsSettings(dnsSettings)
{
   mDnsStub = new resip::DnsStub(dnsSettings);
}

DnsClient::~DnsClient()
{
   delete mDnsStub;
}

DnsAorAAAARecord DnsClient::getDnsAorAAAARecord(const resip::Data& target, SipAccount::IpVersion ipVersion, float timeoutSec, bool anyResult)
{
   if (resip::DnsUtil::isIpAddress(target))
   {
      DnsAorAAAARecord dnsHostRecord;
      resip::Tuple t(target, 0, resip::UDP);

      dnsHostRecord.ipAddr = t.getSockaddr();
      dnsHostRecord.valid = true;

      return dnsHostRecord;
   }

   mOnDnsResultHostACalled = false;
   mOnDnsResultHostAAAACalled = false;
   mDnsHostARecords.clear();
   mDnsHostAAAARecords.clear();
   
   bool doARecordLookup = false;
   bool doAAAARecordLookup = false;

   if (ipVersion == SipAccount::IpVersion_V4)
   {
      doARecordLookup = true;
      mDnsStub->lookup<resip::RR_A>(target, this);
   }
   else if ((ipVersion == SipAccount::IpVersion_Auto) || (ipVersion == SipAccount::IpVersion_Auto_PreferV6))
   {
      doARecordLookup = true;
      mDnsStub->lookup<resip::RR_A>(target, this);
#if defined(IPPROTO_IPV6)
      doAAAARecordLookup = true;
      mDnsStub->lookup<resip::RR_AAAA>(target, this);
#endif
   }
   else
   {
#if defined(IPPROTO_IPV6)
      doAAAARecordLookup = true;
      mDnsStub->lookup<resip::RR_AAAA>(target, this);
#else
      DnsAorAAAARecord dnsHostRecord;
      dnsHostRecord.valid = false;
      return dnsHostRecord;
#endif
   }

   resip::FdSet dnsFdSet;
   UInt64 lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < timeoutSec * 1000)
   {
      mDnsStub->buildFdSet(dnsFdSet);
      dnsFdSet.selectMilliSeconds(100);
      mDnsStub->process(dnsFdSet);

      if ((ipVersion == SipAccount::IpVersion_V4) && mOnDnsResultHostACalled)
      {
         break;
      }
      else if ((ipVersion == SipAccount::IpVersion_V6) && mOnDnsResultHostAAAACalled)
      {
         break;
      }
      else
      {
#if defined(IPPROTO_IPV6)
         if (anyResult)
         {
            if (mOnDnsResultHostACalled || mOnDnsResultHostAAAACalled)
            {
               break;
            }
         }
         else
         {
            if (mOnDnsResultHostACalled && mOnDnsResultHostAAAACalled)
            {
               break;
            }
         }
#else
         if (mOnDnsResultHostACalled)
         {
            break;
         }
#endif
      }

      // Wait before checking for the response again
      SleepMs(100);
   }

   if (anyResult)
   {
      if (!mOnDnsResultHostAAAACalled && !mOnDnsResultHostACalled)
      {
         WarningLog(<< "DNS query for " << target << " timed out");
      }
   }
   else
   {
      if (doARecordLookup && !mOnDnsResultHostACalled)
      {
         WarningLog(<< "DNS A query for " << target << " timed out");
      }

      if (doAAAARecordLookup && !mOnDnsResultHostAAAACalled)
      {
         WarningLog(<< "DNS AAAA query for " << target << " timed out");
      }
   }

   DnsAorAAAARecord ret;
   ret.valid = false;
   SipAccount::IpVersion ipVersion2 = ipVersion;
   if ((ipVersion == SipAccount::IpVersion_Auto) || (ipVersion == SipAccount::IpVersion_Auto_PreferV6))
   {
      if ((ipVersion == SipAccount::IpVersion_Auto) && (mDnsHostARecords.size() > 0))
      {
         ipVersion2 = SipAccount::IpVersion_V4;
      }
      else if ((ipVersion == SipAccount::IpVersion_Auto_PreferV6) && (mDnsHostAAAARecords.size() > 0))
      {
         ipVersion2 = SipAccount::IpVersion_V6;
      }
      else if ((ipVersion == SipAccount::IpVersion_Auto) && (mDnsHostAAAARecords.size() > 0))
      {
         ipVersion2 = SipAccount::IpVersion_V6;
      }
      else if ((ipVersion == SipAccount::IpVersion_Auto_PreferV6) && (mDnsHostARecords.size() > 0))
      {
         ipVersion2 = SipAccount::IpVersion_V4;
      }
   }

   if (ipVersion2 == SipAccount::IpVersion_V4 && mDnsHostARecords.size() > 0)
   {
      resip::DnsHostRecord dnsHostRecord = mDnsHostARecords.front();
      resip::Tuple t(dnsHostRecord.addr(), 0, resip::UNKNOWN_TRANSPORT);

      t.copySockaddrAnyPort(&ret.ipAddr.address, sizeof(ret.ipAddr.pad));
      ret.valid = true;
   }
#if defined(IPPROTO_IPV6)
   else if (ipVersion2 == SipAccount::IpVersion_V6 && mDnsHostAAAARecords.size() > 0)
   {
      resip::DnsAAAARecord dnsHostRecord = mDnsHostAAAARecords.front();
      resip::Tuple t(resip::Tuple(dnsHostRecord.v6Address(), 0, resip::UNKNOWN_TRANSPORT));

      t.copySockaddrAnyPort(&ret.ipAddr.address, sizeof(ret.ipAddr.pad));
      ret.valid = true;
   }
#endif

   return ret;
}

size_t DnsClient::getAllDnsRecordsP(const resip::Data& target, std::vector<resip::Data>& result)
{
   result.clear();

   if (resip::DnsUtil::isIpAddress(target))
   {
      DnsAorAAAARecord dnsHostRecord;
      resip::Tuple t(target, 0, resip::UDP);

      dnsHostRecord.ipAddr = t.getSockaddr();
      dnsHostRecord.valid = true;

      result.push_back(resip::DnsUtil::inet_ntop(dnsHostRecord.ipAddr.address));
      return result.size();
   }

   mOnDnsResultHostACalled = false;
   mOnDnsResultHostAAAACalled = false;
   mDnsHostARecords.clear();
   mDnsHostAAAARecords.clear();

   resip::FdSet dnsFdSet;
   UInt64 lastTime;

#if defined(IPPROTO_IPV6)
   mDnsStub->lookup<resip::RR_AAAA>(target, this);

   lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      mDnsStub->buildFdSet(dnsFdSet);
      dnsFdSet.selectMilliSeconds(100);
      mDnsStub->process(dnsFdSet);
      if (mOnDnsResultHostAAAACalled)
      {
         break;
      }
      // Wait before checking for the response again
      SleepMs(100);
   }
#endif

   mDnsStub->lookup<resip::RR_A>(target, this);

   lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      mDnsStub->buildFdSet(dnsFdSet);
      dnsFdSet.selectMilliSeconds(100);
      mDnsStub->process(dnsFdSet);
      if (mOnDnsResultHostACalled)
      {
         break;
      }
      // Wait before checking for the response again
      SleepMs(100);
   }

   std::vector<resip::DnsHostRecord>::iterator itv4 = mDnsHostARecords.begin();
   std::vector<resip::DnsAAAARecord>::iterator itv6 = mDnsHostAAAARecords.begin();

   while (!(itv4 == mDnsHostARecords.end() && itv6 == mDnsHostAAAARecords.end()))
   {
      if (itv6 != mDnsHostAAAARecords.end())
      {
         result.push_back(resip::DnsUtil::inet_ntop(itv6->v6Address()));
         ++itv6;
      }
      if (itv4 != mDnsHostARecords.end())
      {
         result.push_back(resip::DnsUtil::inet_ntop(itv4->addr()));
         ++itv4;
      }
   }

   return result.size();
}

DnsNaptrRecord DnsClient::getDnsNaptrRecord(int serviceType, const cpc::string& t)
{
   StackLog(<< "DnsClient::getDnsNaptrRecord(): service: " << serviceType << " domain: " << t);
   DnsNaptrRecord ret;

   // Perform the NAPTR lookup using the domain passed in
   resip::Data target(t);

   mOnDnsResultNaptrCalled = false;

   mDnsStub->lookup<resip::RR_NAPTR>(resip::Data(target), serviceType, this); // service example: resip:Protocol::Sip

   // Wait a maximum of 5 seconds for the NAPTR response
   resip::FdSet dnsFdSet;
   UInt64 lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      // Flush pending outgoing NAPTR request and parse pending incoming SRV responses
      mDnsStub->buildFdSet(dnsFdSet);
      dnsFdSet.selectMilliSeconds(100);
      mDnsStub->process(dnsFdSet);

      // Stop testing once we have a NAPTR response is received
      if (mOnDnsResultNaptrCalled)
      {
         break;
      }

      // Wait before checking for the response again
      SleepMs(100);
   }

   // Return the first SRV record found
   if (mDnsNaptrRecords.size() > 0)
   {
      resip::DnsNaptrRecord dnsNaptrRecord = mDnsNaptrRecords.front();
      /*
      StackLog(<< "DnsClient::getDnsNaptrRecord(): naptr: order: " << dnsNaptrRecord.order()
         << " preference: " << dnsNaptrRecord.preference() << " flags: " << dnsNaptrRecord.flags()
         << " service: " << dnsNaptrRecord.service() << " replacement: " << dnsNaptrRecord.replacement()
         << " name: " << dnsNaptrRecord.name() << " regex: " << dnsNaptrRecord.regexp().regexp()
         << " regex replacement: " << dnsNaptrRecord.regexp().replacement());
      StackLog(<< "DnsClient::getDnsNaptrRecord(): naptr: " << dnsNaptrRecord);
      */
      ret.target = dnsNaptrRecord.replacement();
   }

   return ret;
}

DnsSrvRecord DnsClient::getDnsSrvRecord(int serviceType, const cpc::string& t)
{
   cpc::vector<DnsSrvRecord> results;
   getDnsSrvRecords(serviceType, t, results);
   if (results.size() > 0)
   {
      return results[0];
   }
   static DnsSrvRecord emptyRecord;
   return emptyRecord;
}

int DnsClient::getDnsSrvRecords(int serviceType, const cpc::string& domainName, cpc::vector<DnsSrvRecord>& results)
{
   // Perform the SRV lookup using the domain passed in
   resip::Data target(domainName);

   mOnDnsResultSrvCalled = false;

   mDnsStub->lookup<resip::RR_SRV>(target, serviceType, this);

   // Wait a maximum of 5 seconds for the SRV response
   resip::FdSet dnsFdSet;
   UInt64 lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      // Flush pending outgoing SRV request and parse pending incoming SRV responses
      mDnsStub->buildFdSet(dnsFdSet);
      dnsFdSet.selectMilliSeconds(100);
      mDnsStub->process(dnsFdSet);

      // Stop testing once we have a SRV response is received
      if (mOnDnsResultSrvCalled)
      {
         break;
      }

      // Wait before checking for the response again
      SleepMs(100);
   }

   // Return the first SRV record found
   if (mDnsSrvRecords.size() > 0)
   {
      for (const resip::DnsSrvRecord& dnsSrvRecord : mDnsSrvRecords)
      {
         DnsSrvRecord ret;
         ret.target = dnsSrvRecord.target();
         ret.port = dnsSrvRecord.port();
         results.push_back(ret);
      }
   }
   else if (!mOnDnsResultSrvCalled)
   {
      WarningLog(<< "DNS SRV query for " << target << " timed out");
      return -1;
   }

   return 0;
}

void DnsClient::onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>& result)
{
   mDnsNaptrRecords.clear();
   if (result.status == 0)
   {
      mDnsNaptrRecords = result.records;
   }

   // StackLog(<< "DnsClient::onDnsResult(): naptr result status: " << result.status);
   mOnDnsResultNaptrCalled = true;
}

void DnsClient::onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>& result)
{
   mDnsSrvRecords.clear();
   if (result.status == 0)
   {
      mDnsSrvRecords = result.records;
   }

   // StackLog(<< "DnsClient::onDnsResult(): srv result status: " << result.status);
   mOnDnsResultSrvCalled = true;
}

void DnsClient::onDnsResult(const resip::DNSResult<resip::DnsHostRecord>& result)
{
   mDnsHostARecords.clear();
   if (result.status == 0)
   {
      mDnsHostARecords = result.records;
   }

   // StackLog(<< "DnsClient::onDnsResult(): host A result status: " << result.status);
   mOnDnsResultHostACalled = true;
}

void DnsClient::onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>& result)
{
   mDnsHostAAAARecords.clear();
   if (result.status == 0)
   {
      mDnsHostAAAARecords = result.records;
   }

   // StackLog(<< "DnsClient::onDnsResult(): host AAAA result status: " << result.status);
   mOnDnsResultHostAAAACalled = true;
}

bool DnsClient::hasResponse(SipAccount::IpVersion ipVersion)
{
   if ((ipVersion == SipAccount::IpVersion_V4) && mOnDnsResultHostACalled)
   {
      return true;
   }
   else if ((ipVersion == SipAccount::IpVersion_V6) && mOnDnsResultHostAAAACalled)
   {
      return true;
   }
   else
   {
#if defined(IPPROTO_IPV6)
      if (mOnDnsResultHostAAAACalled)
      {
         return true;
      }
#endif
      if (mOnDnsResultHostACalled)
      {
         return true;
      }
   }
   return false;
}

bool DnsClient::probeDns(const resip::Data& target, SipAccount::IpVersion ipVersion, resip::DnsStub::DnsSettings& settings)
{
   DebugLog(<< "DnsClient::probeDns(target:" << target << ", ipVersion:" << ipVersion << ", numServers:" << settings.nameServers.size() << ")");

   //probe only if there's more than one dns server configured:
   int numServers = (settings.includeSystemDnsServers) ? (settings.nameServers.size() + 1) : settings.nameServers.size();
   if (numServers < 2)
   {
      DebugLog(<< "DnsClient::probeDns: Skipping DNS probe, includeSystemDnsServers: " << settings.includeSystemDnsServers << ", nameServers.size: " << settings.nameServers.size());
      return true;
   }

   resip::Data t = target.empty() ? DNS_PROBE_TARGET : target;
   
   DebugLog(<<"DnsClient::probeDns: Probing DNS with " << t.c_str());

   DnsAorAAAARecord result = getDnsAorAAAARecord(t, ipVersion, CPCAPI2_DNS_TIMEOUT + 0.5f, true);
   if (!result.valid)
   {
      bool updated = mDnsStub->serverListChanged(settings);
      if (hasResponse(ipVersion))
      {
         DebugLog(<< "DnsClient::probeDns: Got empty DNS result, assuming server is online.");
         return true;
      }
      if (!updated)
      {
         ErrLog(<< "DnsClient::probeDns: No DNS result and no server list change, aborting!");
         return true;
      }

      result = getDnsAorAAAARecord(t, ipVersion, (numServers - 1) * CPCAPI2_DNS_TIMEOUT + 0.5f, true);
   }

   mDnsStub->serverListChanged(settings);

   if (!result.valid && hasResponse(ipVersion))
   {
      DebugLog(<< "DnsClient::probeDns: Got empty DNS result, assuming server is online.");
      return true;
   }
   
   DebugLog(<< "DnsClient::probeDns finished");

   return result.valid;
}
