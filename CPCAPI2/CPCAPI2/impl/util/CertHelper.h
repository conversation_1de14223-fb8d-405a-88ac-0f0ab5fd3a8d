#ifndef _CERT_HELPER_H_
#define _CERT_HELPER_H_

#include <openssl/ossl_typ.h>
#include <openssl/x509.h>
#include <openssl/x509v3.h>
#include <openssl/ssl.h>


namespace xten
{
   /*
    * The other half of the encrypted root certificate
    */
   static unsigned char OtherHalf[880] = { 
   0x92,0xa5,0xf8,0xe7,0x1b,0x94,0xc1,0xd0,0x18,0x48,0x72,0x79,0xbe,0x7c,0x61,0x71,
   0x77,0x48,0x50,0x3b,0x98,0xf2,0xe5,0x06,0xfb,0x7b,0x14,0xca,0xef,0x22,0x93,0xc7,
   0x7f,0xa4,0x1b,0x5c,0x3f,0x03,0x32,0x83,0x24,0x7e,0xd6,0x83,0x83,0xae,0x1e,0x6c,
   0x4c,0x56,0x5c,0x76,0x77,0x1c,0x95,0x44,0xbc,0xc5,0xfb,0x5f,0x62,0x03,0x5b,0x45,
   0x39,0xb3,0xe1,0x28,0x3a,0x02,0x45,0xe9,0xaf,0x86,0x9b,0x08,0x11,0xca,0x27,0x49,
   0x5d,0x2b,0x56,0xbe,0xf0,0xdf,0x1e,0x5b,0xc4,0x95,0x2b,0xe8,0xc1,0xb2,0x1b,0xc4,
   0xb3,0x19,0xfe,0x84,0x94,0xe5,0x4e,0x64,0xb0,0x20,0x5c,0x11,0x00,0x24,0x36,0x4b,
   0xb5,0x79,0xd1,0xbb,0x85,0x77,0x3c,0x14,0xff,0x5e,0x7b,0x50,0xd9,0x8a,0xb0,0x3b,
   0xdf,0x80,0x71,0x03,0x9b,0x6a,0xaf,0x6f,0x40,0x96,0xae,0x39,0x59,0xf5,0x4d,0xea,
   0xbe,0xd6,0xd4,0x49,0xd4,0x06,0x2a,0x87,0x48,0x04,0x54,0xbf,0x0d,0xde,0x1f,0x07,
   0x82,0x72,0x42,0xc2,0x9b,0xb3,0x46,0x67,0x38,0x07,0xdf,0x5b,0x9e,0x7c,0x24,0x53,
   0xf2,0xf5,0x42,0xc3,0x76,0xe1,0x4d,0x32,0x1a,0x33,0x3e,0xbb,0xff,0xf0,0xc9,0x30,
   0x6c,0x3b,0xb9,0xd8,0xea,0x99,0x27,0xea,0xf6,0xf2,0x63,0xa9,0x07,0xb4,0x24,0x26,
   0xb0,0x44,0xee,0xe1,0x3c,0x77,0x26,0xfb,0x29,0xc0,0xd4,0xc6,0xc8,0xab,0x43,0x12,
   0x79,0x7f,0x5a,0x19,0x77,0x2c,0xbb,0xae,0x6f,0x91,0x7e,0xbc,0x60,0x23,0x28,0x62,
   0x0b,0xeb,0x08,0xaa,0xb2,0xf2,0x72,0x59,0xe2,0x2a,0x35,0xb7,0x19,0x55,0xf3,0x2f,
   0x45,0x26,0x9b,0x75,0x9f,0xa5,0xd1,0x58,0x5d,0x6e,0xe9,0x49,0x1b,0xb3,0xba,0xeb,
   0xa1,0x2d,0x06,0x37,0x73,0xfa,0x4d,0x06,0x99,0xa5,0xc8,0x1f,0xc5,0xb0,0xa4,0x70,
   0x86,0x5c,0x4c,0xf9,0xe1,0xdc,0xe8,0x96,0x99,0x48,0xe5,0x6a,0xa0,0x5c,0x60,0xbc,
   0xb6,0xd4,0x87,0xd3,0x0b,0x23,0x71,0x99,0x58,0x9a,0x76,0xec,0x76,0xd4,0x57,0x3a,
   0x68,0xdf,0x7d,0x14,0xcf,0x24,0xb8,0x8e,0x70,0x75,0x9a,0xf8,0x30,0x84,0x33,0xd1,
   0xd7,0x44,0xa2,0xf9,0x28,0x67,0xc8,0x45,0x68,0xd2,0x08,0x9f,0x3f,0x28,0x37,0x13,
   0xc5,0xd6,0x94,0x12,0x03,0x71,0xf9,0x5a,0x11,0x40,0xcb,0xeb,0xd4,0x5d,0x27,0xc9,
   0x9a,0x1b,0xbb,0x81,0x75,0x2b,0xe8,0x4c,0x11,0x68,0x8d,0xd0,0x70,0xcf,0x6a,0xb8,
   0x81,0x8a,0xa3,0x9c,0xc4,0x71,0x2a,0xf4,0x98,0x2e,0xf4,0xde,0x63,0x69,0xe3,0x89,
   0x86,0xb7,0x20,0xbc,0x99,0x7c,0xcc,0x69,0xbe,0x13,0xa8,0xe9,0xf4,0x94,0xb1,0x85,
   0x43,0x66,0x4f,0x6e,0x9b,0x81,0x58,0xed,0xe4,0x54,0x03,0xd4,0x6c,0xd3,0x7f,0xd9,
   0xf2,0x23,0x03,0x74,0x89,0x1b,0x81,0x84,0xc8,0xb2,0x86,0xb3,0xd6,0xdf,0x8c,0xa1,
   0x52,0x67,0x3a,0x4e,0xb7,0x15,0xa2,0x73,0x46,0x62,0x4a,0xf0,0x37,0x04,0x96,0x02,
   0x9c,0xe3,0x76,0xcc,0x26,0x74,0xcf,0x8e,0x42,0x97,0xe1,0x03,0xb3,0x67,0x76,0xbe,
   0xee,0x09,0x22,0x07,0x3f,0xd1,0x04,0x18,0xb8,0xfa,0x27,0x3d,0x51,0x42,0x72,0x62,
   0x31,0xe1,0xac,0xa5,0xfd,0x3c,0xd2,0xfc,0xe2,0xee,0x1c,0x7f,0xf0,0x1c,0x55,0xc1,
   0x14,0x29,0x04,0x86,0xea,0x82,0xc7,0x1e,0x6d,0x5b,0x47,0x78,0x68,0x27,0x45,0x77,
   0x6b,0x32,0x70,0x6e,0x53,0xcf,0xbd,0x0f,0xa0,0x54,0xcf,0x9a,0x01,0x35,0xc6,0xf5,
   0x36,0x6e,0x14,0x0b,0xcf,0xd6,0xd0,0xbb,0xad,0x56,0x65,0xd5,0x0b,0xb4,0x4a,0x80,
   0x7c,0xab,0xa3,0x08,0x3a,0x2a,0x6e,0xd9,0xe9,0x45,0x08,0xad,0xbb,0x0f,0x02,0xbe,
   0x2f,0x2d,0x36,0x47,0xd7,0x8a,0x7b,0x87,0xc8,0x77,0x5a,0xcc,0x9e,0x5a,0x3c,0x04,
   0xc4,0x74,0x2c,0xa3,0xc5,0xe4,0x36,0x6f,0xc7,0x6e,0x67,0xf1,0x53,0x89,0x11,0x3e,
   0x69,0x2a,0x45,0x65,0xe7,0xa3,0x97,0x35,0xa5,0x46,0x95,0xbb,0x89,0x36,0x39,0x9e,
   0x76,0xbe,0x82,0x2d,0x9d,0x62,0x07,0xf8,0x1b,0x0e,0x81,0x10,0xfd,0xe3,0x9d,0x04,
   0x10,0xc3,0x2a,0xc0,0x08,0xba,0xcc,0x51,0x1b,0xff,0x0d,0xe4,0xc6,0x91,0x7e,0x21,
   0x5d,0x02,0xd2,0x5e,0x64,0xca,0xfc,0x69,0xd3,0xcd,0x2a,0x0b,0x5c,0x2d,0xa5,0x83,
   0xe2,0xe6,0xb0,0xdb,0x2a,0x6d,0xb6,0x56,0x2e,0xa9,0x86,0x4d,0x3a,0x1e,0x72,0xa0,
   0xb3,0x7f,0xdf,0x8d,0xc0,0x45,0x26,0x75,0x06,0x28,0x00,0xf5,0xd7,0xb7,0x70,0x43,
   0xf9,0xd8,0x00,0x3f,0xea,0xfe,0x6f,0x26,0x99,0x80,0x3b,0x4d,0x90,0x95,0x31,0x93,
   0x22,0x01,0x73,0x7a,0x52,0x4f,0xad,0x8a,0xbf,0xff,0xbb,0x70,0x3a,0xcd,0x51,0x84,
   0x66,0x5d,0x8e,0xf7,0xcb,0x28,0x3b,0x0f,0x27,0x7f,0xc8,0x21,0x1d,0xf7,0xc5,0x1a,
   0x3c,0xcc,0xf4,0x5c,0x7c,0x36,0x9a,0x9b,0xfd,0xd9,0xb2,0xc7,0x1a,0x7a,0x74,0x19,
   0xdd,0x82,0x54,0xf5,0xcd,0x62,0x00,0xa0,0x3e,0x70,0x80,0xf0,0x1b,0x59,0x0a,0x3c,
   0x0c,0x59,0xbf,0x18,0x72,0xac,0xdd,0x85,0x21,0x09,0xd4,0x14,0xce,0x8e,0x90,0x30,
   0xbf,0x5b,0xe9,0x95,0x72,0xc9,0x07,0x24,0x61,0x3e,0xab,0xa9,0x53,0xbf,0xa6,0xfa,
   0xad,0x57,0x17,0xd5,0x02,0x89,0x52,0xc3,0xd4,0x11,0xc7,0x6d,0x3f,0x6d,0x57,0xc9,
   0xdc,0xca,0x1d,0x22,0x6e,0x6a,0xe5,0xb2,0x5c,0x3d,0x4d,0x20,0xe5,0x07,0x26,0x0a,
   0x93,0x57,0x24,0xdd,0x1a,0xea,0x62,0x99,0xaf,0xa5,0xf7,0xb1,0x8d,0x75,0x45,0xdb,
   0x5d,0x89,0x82,0x07,0xe6,0x8c,0x69,0x91,0xe3,0xec,0x2f,0xd4,0x36,0x89,0x38,0xdd };

}

#endif
