#ifndef __CPCAPI2_CURLPPSSL_H__
#define __CPCAPI2_CURLPPSSL_H__

#include "phone/SslCipherOptions.h"

#include <curl/curl.h>

#include <openssl/x509.h>

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"

namespace CPCAPI2
{
   /**
    * Class contains functor to load system certs into SSL. Usage:
    *
    * CurlPPSSL sslFunctor;
    * request.setOpt( new SslCtxFunction( sslFunctor ));
    *
    * Note that this class has dependencies on resip and therefore will not be
    * exported to the applications along with the other CurlPP* classes (in
    * this case it will be up to the application to provide the appropriate ssl
    * glue code)
    */
   class CPCAPI2_SHAREDLIBRARY_API CurlPPSSL
   {
   public:

      /**
       * Different kinds of acceptable certificate failures.
       */
      enum acceptedCertFailures
      {
         E_CERT_NOT_YET_VALID = 1,
         E_CERT_EXPIRED = 2,
         E_CERT_ID_MISMATCH = 4,
         E_CERT_NOT_TRUSTED = 8,
         E_CERT_OTHER_ERROR = 16,
         E_CERT_WHATEVER_ERROR = E_CERT_NOT_YET_VALID | E_CERT_EXPIRED | E_CERT_ID_MISMATCH | E_CERT_NOT_TRUSTED | E_CERT_OTHER_ERROR
      };

      CurlPPSSL( SslCipherOptions tlsSettings, int acceptableFailures, const cpc::string& certFolder = "", bool useCertFolderOnly = false, const cpc::string& clientCert = "", const cpc::string& clientCertPassword = "" );
      CurlPPSSL( const CurlPPSSL& that );
      virtual ~CurlPPSSL();

      /**
       * Functor for the initialization of the SSL context. Note that this will
       * also install a cert verification callback, so that this CurlPPSSL object
       * should remain in memory in order to service the cert verifications.
       */
      virtual CURLcode operator()( void* ctx );

   private: // methods and types

      enum NameType
      {
         SubjectAltName,
         CommonName
      };

      struct PeerName
      {
         NameType mType;
         cpc::string mName;
         PeerName(NameType type, cpc::string name): mType(type), mName(name){}
      };

      void getCertNames( X509 * cert, cpc::vector< PeerName >& peerNames );
      cpc::string getCertName( X509 * cert );

      static int certVerifyCallback(X509_STORE_CTX *ctx, void *arg);
      int certVerifyCallback(X509_STORE_CTX *ctx);

   private: // data

      cpc::string m_CertificatesFolder;
      bool m_UseCertFolderOnly;

      cpc::string m_ClientCertificate;
      cpc::string m_ClientCertificatePassword;

      int m_AcceptableFailures;

      SslCipherOptions m_TlsSettings;
   };
}

#endif // __CPCAPI2_CURLPPSSL_H__
