#pragma once

#if !defined(CPCAPI2_HTTP_CLIENT_IMPL_H)
#define CPCAPI2_HTTP_CLIENT_IMPL_H

#include "HttpClient.h"
#include "CurlHttp.h"
#include "../phone/PhoneInterface.h"

#include <string>

namespace CPCAPI2
{
class HTTPClientImpl
{  
public:
   HTTPClientImpl(PhoneInterface* p);
   ~HTTPClientImpl();

   HTTPClient::EHTTPError ConvertErrorCode(int code);

   //
   // Interface IHTTP
   //
   void HTTPSendMessage(
      HTTPClient::EHTTPVerb eInVerb,            // See above enum.
      const char* szInURL,                      // Must include { "http" | "https" }.
      const char* szInMIMEType,                 // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                 // Login to server (optional, NULL for none).
      const char* szInPassword,                 // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,        // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,  // type for client certificate (PEM or DER)
      const char* pInMessageBody,               // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,   // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,            // Bytes per second to read response, 0 for no limit.
      TLSVersion tlsVersion,                    // TLS protocol version to use.
      CipherSuite cipherSuite,                  // Override default cipher suite to use for connection. See SslCipherOptions.h
      bool bInUseEmbeddedCert1,                 // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                 // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                 // ignore any (server) certificate verification error
      bool bInEnableCookies,                    // enable use of cookies
      const char* szInCookieFile,               // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders, // custom headers
      bool verboseLogging,                      // enable verbose logging (i.e. get curl to do verbose logging)
      bool suppressLogging,                     // no logging for this request
      int& resultErrorCode,                     // [out] error code of request
      int& curlErrorCode,                       // [out] raw error code from cURL
      int& responseStatus,                      // [out] status of response
      cpc::string& contentType,                 // [out] document content type
      cpc::string& result,                      // [out] result of the http request
      HTTPClient::RedirectInfo& redirectInfo,   // [out] Info regarding if the request was redirected,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress,  //
      bool bUseHttp2,                           // Request session to specifically use HTTP2, can change to a SDK curl enum wrapper if other versions are required
      unsigned long timeoutSeconds,             // how long a transfer can go for before its considered too long and times out with -1 code
      size_t maxResponseBodySizeBytes,
      cpc::vector<cpc::string> requiredCertPublicKeys,    // certificate pinning
      unsigned long dynamicTimeoutSeconds
   );

   //
   // Interface IHTTP
   //
   void StartHTTPSession(
      HTTPClient::EHTTPVerb eInVerb,            // See above enum.
      const char* szInURL,                      // Must include { "http" | "https" }.
      const char* szInMIMEType,                 // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                 // Login to server (optional, NULL for none).
      const char* szInPassword,                 // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,        // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,  // type for client certificate (PEM or DER)
      const char* pInMessageBody,               // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,   // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,            // Bytes per second to read response, 0 for no limit.
      TLSVersion tlsVersion,                    // TLS protocol version to use.
      CipherSuite cipherSuite,                  // Override default cipher suite to use for connection. See SslCipherOptions.h
      bool bInUseEmbeddedCert1,                 // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                 // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                 // ignore any (server) certificate verification error
      bool bInEnableCookies,                    // enable use of cookies
      const char* szInCookieFile,               // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,                     // [out] error code of request
      int& curlErrorCode,                       // [out] raw error code from cURL
      int& responseStatus,                      // [out] status of response
      cpc::string& contentType,                 // [out] document content type
      cpc::string& result,                      // [out] result of the http request
      HTTPClient::RedirectInfo& redirectInfo,   // [out] Info regarding if the request was redirected,
      const char* szInUserAgent,                // User Agent string,
      const cpc::string& overrideProxyAddress,  //
      bool bUseHttp2,                           // Request session to specifically use HTTP2, can change to a SDK curl enum wrapper if other versions are required
      unsigned long timeoutSeconds,
      size_t maxResponseBodySizeBytes,
      bool verboseLogging,                       // enable verbose logging (i.e. get curl to do verbose logging)
      unsigned long dynamicTimeoutSeconds
   );

   //
   // Interface IHTTP
   //
   void DoSessionRequest(
	   HTTPClient::EHTTPVerb eInVerb,            // See above enum.
      const char* szInURL,                      // Must include { "http" | "https" }.
      const char* szInMIMEType,                 // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                 // Login to server (optional, NULL for none).
      const char* szInPassword,                 // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,        // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,  // type for client certificate (PEM or DER)
      const char* pInMessageBody,               // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,   // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,            // Bytes per second to read response, 0 for no limit.
      TLSVersion tlsVersion,                    // TLS protocol version to use.
      CipherSuite cipherSuite,                  // Override default cipher suite to use for connection. See SslCipherOptions.h
      bool bInUseEmbeddedCert1,                 // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                 // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                 // ignore any (server) certificate verification error
      bool bInEnableCookies,                    // enable use of cookies
      const char* szInCookieFile,               // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,                     // [out] error code of request
      int& curlErrorCode,                       // [out] raw error code from cURL
      int& responseStatus,                      // [out] status of response
      cpc::string& contentType,                 // [out] document content type
      cpc::string& result,                      // [out] result of the http request
      HTTPClient::RedirectInfo& redirectInfo,   // [out] Info regarding if the request was redirected,
      const char* szInUserAgent,                // User Agent string,
      const cpc::string& overrideProxyAddress,  //
      bool verboseLogging                       // enable verbose logging (i.e. get curl to do verbose logging)
   );

   void Abort();

private:
   void setProxy(xten::CurlHttp::Session* httpSession, const cpc::string& destination, const cpc::string& overrideProxy);

private:
   void processTLSOptions(xten::CurlHttp::CurlOptions& curlOptions, TLSVersion tlsVersion, CipherSuite cipherSuite, SslCipherOptions cipherOptions);

   cpc::string mCertStorageFolder;

   // for DoSessionRequest()
   xten::CurlHttp* mHTTP;
   xten::CurlHttp::Session* mHTTPSession;

   // for HTTPSendMessage()
   xten::CurlHttp* mHTTPIndependent;
   xten::CurlHttp::Session* mHTTPSessionIndependent;

   resip::Mutex mMutex; // protect all CurlHttp pointers

   PhoneInterface* mPhone;
};

}

#endif
