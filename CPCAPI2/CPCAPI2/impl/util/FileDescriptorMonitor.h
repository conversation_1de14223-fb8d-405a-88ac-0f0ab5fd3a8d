#pragma once
#ifndef __CPCAPI2_FILEDESCRIPTORMONITOR_H__
#define __CPCAPI2_FILEDESCRIPTORMONITOR_H__

#include <string>
#include <vector>

namespace CPCAPI2
{
   namespace Utils
   {
      class FileDescriptorMonitor
      {
      public:
         FileDescriptorMonitor(int fdThold, bool log = true);
         bool isOverThold();
         // returns how many file descriptors beyond threshold are in use.
         // returns 0 if not currently over threshold.
         int fdsOverThold();

         static std::vector<std::string> getSnapshot()
         {
            int sockets, files, devices, other;
            std::vector<std::string> list;
            getOpenFileDescriptors(sockets, files, devices, other, list);
            return list;
         }
         static bool logSnapshotDifferences(std::vector<std::string> firstSnapshot, std::vector<std::string> secondSnapshot, std::string additionalInfo = "", std::vector<std::string> skipItems = std::vector<std::string>());
         static void logOpenFileDescriptors(std::string additionalInfo = "");

      private:
         int mStartupFdCount;
         int mFdThold;
         int s, f, d, o;
         bool mLog;

         static std::string getTypeFromName(char *name, int& sockets, int& files, int& devices, int& other, bool isLink = false);
         static int getOpenFileDescriptors(int& sockets, int& files, int& devices, int& other, std::vector<std::string> &list);
      };
   
      class FileDescriptorsCheckpoint
      {
         FileDescriptorMonitor mFD;
         std::vector<std::string> mLastSnapshot;
         
      public:
         FileDescriptorsCheckpoint() : mFD(0, false)
         {
            mLastSnapshot = mFD.getSnapshot();
         }
         
         ~FileDescriptorsCheckpoint()
         {
         }
         
         void reset()
         {
            mLastSnapshot = mFD.getSnapshot();
         }
         
         bool newCheckpoint(std::string additionalInfo = "", std::vector<std::string> skipItems = std::vector<std::string>())
         {
            std::vector<std::string> newSnapshot = mFD.getSnapshot();

            bool ret = mFD.logSnapshotDifferences(mLastSnapshot, newSnapshot, additionalInfo, skipItems);

            mLastSnapshot = newSnapshot;
            
            return ret;
         }
         
         void logOpenFileDescriptors(std::string additionalInfo = "")
         {
            mFD.logOpenFileDescriptors(additionalInfo);
         }
      };
   }
}

#endif // __CPCAPI2_FILEDESCRIPTORMONITOR_H__
