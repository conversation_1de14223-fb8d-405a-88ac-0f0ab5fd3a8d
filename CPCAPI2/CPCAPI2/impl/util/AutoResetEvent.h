#pragma once

#ifndef CPCAPI2_AUTO_RESET_EVENT_H
#define CPCAPI2_AUTO_RESET_EVENT_H

#include <condition_variable>
#include <mutex>

namespace CPCAPI2
{
class AutoResetEvent
{
public:
   explicit AutoResetEvent(bool initial = false);

   void Set();
   void Reset();

   bool WaitOne(int millisec);

private:
   AutoResetEvent(const AutoResetEvent&);
   AutoResetEvent& operator=(const AutoResetEvent&); // non-copyable
   bool flag_;
   std::mutex protect_;
   std::condition_variable signal_;
};
}

#endif // CPCAPI2_AUTO_RESET_EVENT_H
