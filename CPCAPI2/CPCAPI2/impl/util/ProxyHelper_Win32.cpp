#include "ProxyHelper.h"

#if defined( _WIN32 )
#include <Windows.h>
#include <winhttp.h>
#include <codecvt>

#include <boost/algorithm/string.hpp>

#include "cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{


static cpc::string WideToString(const LPCWSTR in)
{
   cpc::string ret;
   std::wstring wIn = in;
   try
   {
      std::wstring_convert<std::codecvt_utf8_utf16<wchar_t>, wchar_t> convert;
      ret = convert.to_bytes(wIn.c_str()).c_str();
   }
   catch (...)
   {
   }

   return ret;
}

/*
 * Return scheme specific proxy from on a proxy list
 */
static cpc::string ParseProxyList(const cpc::string proxyList, bool useHttps)
{
   cpc::string result = proxyList;
   // check for scheme (http vs https) specific proxy
   size_t iSplit = proxyList.find("=");
   if (iSplit != cpc::string::npos)
   {
      // extract https proxy
      cpc::string scheme = useHttps ? "https=" : "http=";
      size_t startPos = proxyList.find(scheme);
      if (startPos != cpc::string::npos)
      {
         result = proxyList.substr(startPos + scheme.size());
      }
      else
      {
         result = "";
      }         
   }
   size_t endPos = result.find(";");
   if (endPos != cpc::string::npos)
      result = result.substr(0, endPos);

   return result;
}

cpc::string
ProxyHelper::GetProxyServerInfo(const cpc::string & destination, cpc::string& bypassList)
{
   cpc::string proxyStr;

   WINHTTP_CURRENT_USER_IE_PROXY_CONFIG ieConfig;
   memset(&ieConfig, 0, sizeof(ieConfig));

   if (!WinHttpGetIEProxyConfigForCurrentUser(&ieConfig))
   {
      DWORD err = ::GetLastError();      
      if (err == ERROR_FILE_NOT_FOUND)
      {
         // per https://code.msdn.microsoft.com/windowsdesktop/WinHTTP-proxy-sample-eea13d0c
         // No IE proxy settings found, just do autodetect
         ieConfig.fAutoDetect = TRUE;
      }
      else
      {
         ErrLog(<< "Could not retrieve proxy configuration for current user, error: " << ::GetLastError());
         return proxyStr;
      }
   }

   if (ieConfig.lpszProxy)
   {
      proxyStr = ParseProxyList(WideToString(ieConfig.lpszProxy), destination.find("https:") == 0);
      if (ieConfig.lpszProxyBypass)
      {
         std::string bypassListStr = WideToString(ieConfig.lpszProxyBypass);

         // convert to Curl friendly format: https://curl.haxx.se/libcurl/c/CURLOPT_NOPROXY.html
         boost::replace_all(bypassListStr, ";", ",");
         boost::replace_all(bypassListStr, "*.", ".");

         bypassList = bypassListStr.c_str();
      }
   } 
   else if (ieConfig.fAutoDetect || ieConfig.lpszAutoConfigUrl)
   {
      HINTERNET handle = WinHttpOpen(L"CPCAPI2 Proxy Lookup/1.0",
         WINHTTP_ACCESS_TYPE_NO_PROXY,
         WINHTTP_NO_PROXY_NAME,
         WINHTTP_NO_PROXY_BYPASS, 0);

      if (!handle)
      {
         ErrLog(<< "Could not create WinHttp session, error: " << ::GetLastError());
      }
      else
      {
         WINHTTP_AUTOPROXY_OPTIONS options;
         WINHTTP_PROXY_INFO info;

         memset(&options, 0, sizeof(options));

         options.fAutoLogonIfChallenged = TRUE;

         // automatic proxy detection (network discovery, slow)
         if (ieConfig.fAutoDetect)
         {
            options.dwFlags |= WINHTTP_AUTOPROXY_AUTO_DETECT;
            options.dwAutoDetectFlags |= WINHTTP_AUTO_DETECT_TYPE_DHCP | WINHTTP_AUTO_DETECT_TYPE_DNS_A;
         }

         // proxy configuration script (.pac)
         if (ieConfig.lpszAutoConfigUrl)
         {
            options.dwFlags |= WINHTTP_AUTOPROXY_CONFIG_URL;
            options.lpszAutoConfigUrl = ieConfig.lpszAutoConfigUrl;
         }

         std::wstring destUrl = destination;
         if (!WinHttpGetProxyForUrl(handle, destUrl.c_str(), &options, &info))
         {
            DWORD errCode = ::GetLastError();
            if (errCode != ERROR_SUCCESS && errCode != ERROR_WINHTTP_AUTODETECTION_FAILED)
            {
               DebugLog(<< "Cannot retrieve proxy configuration, auto-detect: " << ieConfig.fAutoDetect << ", error: " << errCode);
            }
         }
         else if (info.lpszProxy)
         {
            proxyStr = WideToString(info.lpszProxy);
            // if multiple proxies provided, just use the first one
            size_t iSplit = proxyStr.find(";");
            if (iSplit != cpc::string::npos)
            {
               proxyStr = proxyStr.substr(0, iSplit);
            }
         }

         // clean up
         if (info.lpszProxy != NULL)
            GlobalFree(info.lpszProxy);

         if (info.lpszProxyBypass != NULL)
            GlobalFree(info.lpszProxyBypass);

         WinHttpCloseHandle(handle);
      }
   }

   if (ieConfig.lpszAutoConfigUrl)
      GlobalFree(ieConfig.lpszAutoConfigUrl);
   if (ieConfig.lpszProxy)
      GlobalFree(ieConfig.lpszProxy);
   if (ieConfig.lpszProxyBypass)
      GlobalFree(ieConfig.lpszProxyBypass);

   return proxyStr;
}

}

#endif // defined( _WIN32 )