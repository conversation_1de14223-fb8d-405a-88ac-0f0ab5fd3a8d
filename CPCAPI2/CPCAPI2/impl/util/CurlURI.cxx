

#include "CurlURI.h"

#include <curl/curl.h>
#include <boost/algorithm/string.hpp>

using namespace xten;


CurlURI::CurlURI()
{
   mValid = false;
}

CurlURI::CurlURI(const CurlURI& uri)
{
   mValid = uri.mValid;
   mUri = uri.mUri;
   mAuthinfo = uri.mAuthinfo;
   mScheme = uri.mScheme;
   mHost = uri.mHost;
   mPort = uri.mPort;
   mPath = uri.mPath;
}


CurlURI::CurlURI(const std::string& uri)
{
   mValid = false;

   mPort.clear();
   mScheme.clear();
   mHost.clear();
   mAuthinfo.clear();
   mPath.clear();

   std::string parse = uri;

   boost::replace_all(parse,"\\","/");

   size_t p = std::string::npos;

   // user
   if ((p = parse.find("@")) != std::string::npos)
   {
      mAuthinfo = parse.substr(0,p);
      parse = parse.substr(p+1);
   }

   // scheme
   if ((p = parse.find(":")) != std::string::npos)
   {
      if (parse.substr(0,p).find(".") == std::string::npos) // must be scheme if no . for domain/host before
      {
         mScheme = parse.substr(0,p);
         parse = parse.substr(p+1);
         while (parse.size() && parse[0] == '/')
         {
            parse = parse.substr(1);
         }
      }
   }

   // host with port
   if ((p = parse.find(":")) != std::string::npos)
   {
      mHost = parse.substr(0,p);
      parse = parse.substr(p+1);

      if ((p = parse.find("/")) != std::string::npos)
      {
         mPort = parse.substr(0,p);
         parse = parse.substr(p+1);
      }
      else
      {
         mPort = parse;
         parse.clear();
      }
   }
   else // no port specified
   {
      if ((p = parse.find("/")) != std::string::npos)
      {
         mHost = parse.substr(0,p);
         parse = parse.substr(p+1);
      }
      else
      {
         mHost = parse;
         parse.clear();
      }
   }

   mPath = parse;

   if (mScheme.empty())
   {
      mScheme = "http";
   }

   if (mPort.empty())
   {
      if (mScheme.compare("http") == 0 || mScheme.compare("HTTP") == 0)
      {
         mPort = "80"; // PORT_HTTP;
      }
      else if (mScheme.compare("https") == 0 || mScheme.compare("HTTPS") == 0)
      {
         mPort = "443"; // PORT_HTTPS;
      }
      // add other schemes here as well
   }

   if (!mPort.empty() && !mHost.empty())
   {
      mValid = true;
   }

   mUri = uri;
}
