#pragma once

#include <functional>

#if (_MSC_VER >= 1600)
  #ifndef CPCAPI2_USE_BUILTIN_THREAD
    #define CPCAPI2_USE_STD_THREAD
  #endif
#endif

#if defined(__linux__)
  #define CPCAPI2_USE_STD_THREAD
#endif

#if defined(CPCAPI2_USE_STD_THREAD)
#include <thread>
#else
#include <boost/function.hpp>
#if defined(WIN32)
#include <windows.h>
#else
#include <pthread.h>
#endif
#endif

namespace CPCAPI2
{
#if defined(CPCAPI2_USE_STD_THREAD)
   typedef std::thread thread;
   namespace this_thread = std::this_thread;
   namespace chrono = std::chrono;
#else
   class thread
   {
   public:
      thread();
      explicit thread(std::function<void(void)> threadStart);
      ~thread();

      void operator=(const thread& rhs);

      void join();
      void detach();
      void threadProc();

#ifdef WIN32
      typedef DWORD Id;
#else
      typedef pthread_t Id;
#endif

   private:

   private:
      std::function<void(void)> mThreadStart;

#ifdef WIN32
      HANDLE mThread;
#endif
      Id mId;
   };

   class timeval
   {
   public:
       timeval(unsigned int milliseconds) : mMS(milliseconds) {}
       ~timeval() {}

       unsigned int ms() const { return mMS; }
   private:
       unsigned int mMS;
   };

   class chrono
   {
   public:
       static timeval milliseconds(unsigned int ms)
       {
           return timeval(ms);
       }
   };

   class this_thread
   {
   public:
       static void sleep_for(const timeval& interval);
   };
#endif
}
