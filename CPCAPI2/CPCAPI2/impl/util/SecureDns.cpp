#include "SecureDns.h"

#include "cpc_logger.h"
#include "CurlURI.h"
#include "DnsClient.h"
#include "../phone/PhoneInterface.h"

#ifdef ANDROID
#include "../phone/NetworkChangeManager_Android.h"
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace CPCAPI2::Utils;
using namespace xten;

bool SecureDns::isSecureDnsActive()
{
#ifdef ANDROID
   if (PhoneInterface::externalResolver() == ConnectionPreferences::ExternalResolverUsage_Disable)
      return false;
   if (PhoneInterface::externalResolver() == ConnectionPreferences::ExternalResolverUsage_Force)
      return true;
   return NetworkChangeManager_Android::isSecureDnsActive();
#else
   return false;
#endif
}

bool SecureDns::doCurloptResolve(const std::string& uri, struct curl_slist *)
{
   if (isSecureDnsActive())
   {
      CurlURI curi(uri);
      resip::DnsStub::DnsSettings ds;
      CPCAPI2::Utils::DnsClient dc(ds);
      std::vector<resip::Data> dns;

      dc.getAllDnsRecordsP(curi.host().c_str(), dns);

      std::stringstream result;
      result << curi.host() << ":" << curi.port() << ":";
      
      if (dns.size() > 0)
      {
         //Support for providing multiple IP addresses per entry for CURLOPT_RESOLVE added in cURL 7.59.0.
#if ( ( LIBCURL_VERSION_MAJOR > 07 ) || (( LIBCURL_VERSION_MAJOR == 07 ) && ( LIBCURL_VERSION_MINOR >= 59 )) )
         for (int i = 0; i < dns.size(); i++)
         {
            result << dns[i].c_str();
            if (i < (dns.size() - 1)) result << ",";
         }
#else
         result << dns.front().c_str();
#endif // LIBCURL_VERSION
      }
      else
      {
         WarningLog(<< "SecureDns::doCurloptResolve: no DNS result");
      }

      DebugLog(<< "SecureDns::doCurloptResolve: " << result.str());

      return true;
   }
   
   return false;
}
