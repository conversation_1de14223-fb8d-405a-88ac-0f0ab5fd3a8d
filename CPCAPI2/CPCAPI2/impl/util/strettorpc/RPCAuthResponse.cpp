#include "RPCAuthResponse.h"
#include "RPCTimestamp.h"

using namespace CPCAPI2;

// {
//    "strettorpc" : "1.0",
//    "result" : {
//       "session" : "<server-side-session-id>",
//       "expires" : "<timestamp>",
//       "token" : "<the-auth-token>",
//       "scope" : [
//          "<authentication-realm>",
//          "<authentication-realm>",
//          "<authentication-realm>", ...
//       ],
//       "services" : [
//          { "<service-name>" : "<service-location>" },
//          { "<service-name>" : "<service-location>" }
//       ]
//    },
//    "id" : "~auth~" <= since there was no id for auth it will mirror the method name. Auth is always synchronous
// }

bool RPCAuthResponse::getSession( std::string & outString )
{
   std::map< std::string, RPCParam >::const_iterator iter = m_Params.find( "session" );
   if( iter == m_Params.end() )
      return false;

   if( iter->second.m_Value.m_Type != RPCValueType_string )
      return false;

   outString = iter->second.m_Value.m_StringVal;
   return true;
}

bool RPCAuthResponse::getExpires( int64_t & outMillisSinceEpoch )
{
   std::map< std::string, RPCParam >::const_iterator iter = m_Params.find( "expires" );
   if( iter == m_Params.end() )
      return false;

   if( iter->second.m_Value.m_Type != RPCValueType_string )
      return false;

   RPCTimestamp timeStamp( iter->second.m_Value.m_StringVal );
   if( !timeStamp.toMillis( outMillisSinceEpoch ))
      return false;

   return true;
}

bool RPCAuthResponse::getAuthToken( std::string & outToken )
{
   std::map< std::string, RPCParam >::const_iterator iter = m_Params.find( "token" );
   if( iter == m_Params.end() )
      return false;

   if( iter->second.m_Value.m_Type != RPCValueType_string )
      return false;

   outToken = iter->second.m_Value.m_StringVal;
   return true;
}

bool RPCAuthResponse::getRealms( std::list<std::string>& outRealms )
{
   std::map< std::string, RPCParam >::const_iterator iter = m_Params.find( "scope" );
   if( iter == m_Params.end() )
      return false;

   if( iter->second.m_Value.m_Type != RPCValueType_array )
      return false;

   const std::list< RPCValue >& arrayVal( iter->second.m_Value.m_ArrayVal );
   std::list< RPCValue >::const_iterator arrayIter = arrayVal.begin();
   for( ; arrayIter != arrayVal.end() ; ++arrayIter )
   {
      const RPCValue &arrayItem( *arrayIter );
      if( arrayItem.m_Type != RPCValueType_string )
         continue;

      outRealms.push_back( arrayItem.m_StringVal );
   }
   return false;
}
