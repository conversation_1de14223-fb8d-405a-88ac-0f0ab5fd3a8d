#pragma once
#ifndef __CPCAPI2_RPCRESPONSE_H__
#define __CPCAPI2_RPCRESPONSE_H__

#include <string>
#include <map>

#include <stdint.h>
#include <document.h>

#include "RPCIdentifier.h"
#include "RPCParam.h"

namespace CPCAPI2
{
   // Different error codes returned by stretto-rpc servers
   typedef enum RPCErrorCode
   {
      RPCErrorCode_Syntax         = -32000, // Internal server error: call syntax error	
      RPCErrorCode_Unsupported    = -32001, // Internal server error: unsupported API version	
      RPCErrorCode_Parse          = -32700, // Parse error, invalid JSON supplied	
      RPCErrorCode_Invalid_Req    = -32600, // Invalid request, something is missing from the request	
      RPCErrorCode_Method         = -32601, // Method not found	
      RPCErrorCode_Param          = -32602, // Invalid method parameter(s)	
      RPCErrorCode_RPC            = -32503, // Internal STRETTO-RPC error	
      RPCErrorCode_Unauthorized   = -32401, // Unauthorized, you can't execute this method	
      RPCErrorCode_Forbidden      = -32403, // Forbidden, authentication failed	
      RPCErrorCode_Invalid_Sess   = -32404, // Invalid session	
      RPCErrorCode_Authentication = -32407, // Authentication Required, invalid auth parameter	
      RPCErrorCode_Session_Expiry = -32408  // Session expired
   } RPCErrorCode;

   class RPCResponse
   {
   public:

      RPCResponse( void );
      virtual ~RPCResponse() {}

      bool isSuccessResponse();
      int64_t getErrorCode(); // Only valid if isSuccessResponse() returns false;
      std::string getErrorMessage(); // Only valid if isSuccessResponse() returns false;
      bool getResultParams( std::map< std::string, RPCParam >& resultParams ); // Only valid if isSuccessResponse() returns true;

      virtual bool fromJSON( const rapidjson::Document& JSONdoc );

      RPCIdentifier getID( void ) const { return m_ID; }

   protected:

      RPCIdentifier m_ID;
      bool m_IsSuccess;
      int64_t m_ErrorCode;
      std::string m_ErrorMessage;

      std::map< std::string, RPCParam > m_Params;
   };
}

#endif // __CPCAPI2_RPCRESPONSE_H__