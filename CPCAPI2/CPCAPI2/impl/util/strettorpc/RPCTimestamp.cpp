#ifdef WIN32
#include <Windows.h>
#endif

#if defined ( __APPLE__ ) || defined( __linux__ )
#include <sys/time.h>
#else
#include <time.h>
#endif

#include <sstream>
#include <iostream>
#include <cstdio>
#include <ctime>
#include "RPCTimestamp.h"

using namespace CPCAPI2;

// should really be moved somewhere generically into Utils package.
static int64_t millisSinceEpoch()
{
   int64_t result(0);
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = 11644473600000 * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
#endif
   return result;
}

/* struct std::tm to seconds since Unix epoch. Interesting/weird little function! */
// See http://www.catb.org/esr/time-programming/
// And also http://stackoverflow.com/questions/530519/stdmktime-and-timezone-info
static time_t my_timegm( struct std::tm * t )
{
    long year;
    time_t result;
#define MONTHSPERYEAR   12      /* months per calendar year */
    static const int cumulativedays[MONTHSPERYEAR] =
        { 0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334 };

    year = 1900 + t->tm_year + t->tm_mon / MONTHSPERYEAR;
    result = (year - 1970) * 365 + cumulativedays[t->tm_mon % MONTHSPERYEAR];
    result += (year - 1968) / 4;
    result -= (year - 1900) / 100;
    result += (year - 1600) / 400;
    if ((year % 4) == 0 && ((year % 100) != 0 || (year % 400) == 0) &&
        (t->tm_mon % MONTHSPERYEAR) < 2)
        result--;
    result += t->tm_mday - 1;
    result *= 24;
    result += t->tm_hour;
    result *= 60;
    result += t->tm_min;
    result *= 60;
    result += t->tm_sec;
    if (t->tm_isdst == 1)
        result -= 3600;
    return (result);
}

RPCTimestamp::RPCTimestamp()
{
   m_MillisSinceEpoch = millisSinceEpoch();
}

RPCTimestamp::RPCTimestamp( const int64_t & millisSinceEpoch )
{
   m_MillisSinceEpoch = millisSinceEpoch;
}

RPCTimestamp::RPCTimestamp( const std::string & stringFormatTime )
{
   m_MillisSinceEpoch = millisSinceEpoch(); // initialize

   // <timestamp> is a timestamp in the format "Y-m-d H:i:s" (e.g. 1971-09-28
   // 14:03:00) which indicates the time and date the session will expire.
   // After this a new authentication request is required.
   //
   // Time values are in GMT which helps slightly.

   int year, month, day, hour, minute, second;
   if( sscanf( stringFormatTime.c_str(), "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second ) == 6 )
   {
      struct std::tm expiresTime;
      expiresTime.tm_year  = year - 1900;
      expiresTime.tm_mon   = month;
      expiresTime.tm_mday  = day;
      expiresTime.tm_hour  = hour;
      expiresTime.tm_min   = minute;
      expiresTime.tm_sec   = second;
      expiresTime.tm_isdst = 0;

      // I rather doubt this tricky time function handles the leap second
      // correctly. But that will just result in the 'session' perhaps expiring
      // slightly sooner (or later), which should be covered.
      time_t secSinceEpoch = my_timegm( &expiresTime );
      m_MillisSinceEpoch = secSinceEpoch * 1000;
   }
}

RPCTimestamp::RPCTimestamp( const RPCTimestamp& that )
   : m_MillisSinceEpoch( that.m_MillisSinceEpoch )
{
}

bool RPCTimestamp::toString( std::string& outString ) const
{
   const time_t secSinceEpoch = m_MillisSinceEpoch / 1000;
   struct std::tm *tmVal = gmtime( &secSinceEpoch ); // statically allocated result, don't free it.
   if( tmVal == NULL )
      return false;

   const char *fmt  = "%04d-%02d-%02d %02d:%02d:%02d";
   char  buf[ 20 ];

   int charsWritten = snprintf( buf, sizeof(buf), fmt,
                        ( 1900 + tmVal->tm_year ),
                        tmVal->tm_mon + 1, // month starts at index zero!
                        tmVal->tm_mday,
                        tmVal->tm_hour,
                        tmVal->tm_min,
                        tmVal->tm_sec );
   // the return value of this snprintf variant is, according to cppreference.com:
   // Number of characters that would have been written for a sufficiently large buffer if successful
   // (not including the terminating null character), or a negative value if an error occurred. Thus,
   // the (null-terminated) output has been completely written if and only if the returned value is nonnegative
   // and less than buf_size.
   if (charsWritten > 0 && charsWritten < sizeof(buf) )
   {
      buf[ 19 ] = '\0';
      outString = buf;
      return true;
   }
   return false;
}

bool RPCTimestamp::toMillis( int64_t& outMillis ) const
{
   outMillis = m_MillisSinceEpoch;
   return true;
}

RPCTimestamp & RPCTimestamp::operator=( const RPCTimestamp & that )
{
   if( this == &that )
      return *this;

   this->m_MillisSinceEpoch = that.m_MillisSinceEpoch;
   return *this;
}
