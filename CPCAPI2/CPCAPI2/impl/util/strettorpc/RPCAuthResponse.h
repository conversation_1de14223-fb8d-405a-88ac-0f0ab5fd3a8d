#pragma once
#ifndef __CPCAPI2_RPCAUTHRESPONSE_H__
#define __CPCAPI2_RPCAUTHRESPONSE_H__

#include <list>
#include <cpcapi2defs.h>
#include <document.h>

#include "RPCResponse.h"

namespace CPCAPI2
{
   class RPCAuthResponse : public RPCResponse
   {
   public:
      RPCAuthResponse( void ) {};
      ~RPCAuthResponse( void ) {};

      bool getSession( std::string& outString );
      bool getExpires( int64_t& outMillisSinceEpoch );
      bool getAuthToken( std::string& outToken );
      bool getRealms( std::list< std::string >& outRealms );
   };
}

#endif //  __CPCAPI2_RPCAUTHRESPONSE_H__