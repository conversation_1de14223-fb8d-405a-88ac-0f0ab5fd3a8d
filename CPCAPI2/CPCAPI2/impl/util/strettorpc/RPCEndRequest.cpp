
#include "RPCEndRequest.h"

using namespace CPCAPI2;

RPCEndRequest::RPCEndRequest( const std::string& sessionID, const std::string& authToken )
   : RPCRequest( "~end~", sessionID, authToken )
{
   // Example
   //
   // {
	//    "strettorpc" : "1.0",
	//    "method" : "~end~",
	//    "id" : "_<arbitrary-client-side-id>_",
	//    "auxiliary" : {
	// 	   "auth_method"  : "token",
	// 	   "auth_session" : "_<session-id from authentication>_",
	// 	   "auth_data"    : "_<computed-token>_"
	//    }
   // }

   // superclass ctor does everything
}
