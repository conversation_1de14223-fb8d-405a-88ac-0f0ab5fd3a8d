#pragma once
#ifndef __CPCAPI2_RPCREQUEST_H__
#define __CPCAPI2_RPCREQUEST_H__

#include <string>
#include <map>
#include "RPCParam.h"
#include "RPCIdentifier.h"

namespace CPCAPI2
{
   class RPCIdentifier;

   class RPCRequest
   {
   public:
      /**
       * @param methodName the method "name" used for uniquely identifying this request
       * @param authSession the session string returned int the authentication response
       * @param authToken the authentication token (string) returned in the authentication response
       */
      RPCRequest( const char *methodName, const std::string& sessionID, const std::string& authToken );
      virtual ~RPCRequest() {};

      const char *getMethodName() const;
      bool addParameter( const RPCParam& value );

      /**
       * Marshals the request to a string in JSON format, as per STRETTO-RPC
       * documentation.
       */
      void toJSON( CPCAPI2::Json::StdStringBuffer& buffer ) const;

      RPCIdentifier getID( void ) const;

   protected:
      // This method of constructing a request is only available to
      // subclasses (as it constructs outside of an authentication session)
      RPCRequest( const char *methodName );

   private:
      std::string m_MethodName;
      std::string m_SessionID;  // optional
      std::string m_AuthToken;  // optional
      mutable std::string m_AuthTokenSHA256;
      std::map< std::string, RPCParam > m_Params;

      RPCIdentifier m_ID;
   };
}

#endif // __CPCAPI2_RPCREQUEST_H__
