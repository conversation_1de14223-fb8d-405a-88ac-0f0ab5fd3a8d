#include "RPCValue.h"

#include <json/JsonHelper.h>

using namespace CPCAPI2;

// operator defined for collections
RPCValue& RPCValue::operator=( const RPCValue& that )
{
   if( this == &that )
      return *this;

   m_Type      = that.m_Type;
   m_BoolVal   = that.m_BoolVal;
   m_IntVal    = that.m_IntVal;
   m_DoubleVal = that.m_DoubleVal;
   m_StringVal = that.m_StringVal;
   m_ObjectVal = that.m_ObjectVal;
   m_ArrayVal  = that.m_ArrayVal;
   return *this;
}

// operator defined for collections
bool RPCValue::operator==( const RPCValue& that ) const
{
   if( this == &that )
      return true;

   if( this->m_Type != that.m_Type )
      return false;

   switch( this->m_Type )
   {
   case RPCValueType_bool:
      return m_BoolVal == that.m_BoolVal;
   case RPCValueType_int:
      return m_IntVal == that.m_IntVal;
   case RPCValueType_double:
      return m_DoubleVal == that.m_DoubleVal;
   case RPCValueType_string:
      return m_StringVal == that.m_StringVal;
   case RPCValueType_object:
      return m_ObjectVal == that.m_ObjectVal;
   case RPCValueType_array:
      return m_ArrayVal == that.m_ArrayVal;
   default:
      return false;
   }
}

// (recursively) populate values from rapidjson value (unmarshal)
bool RPCValue::fromJSONValue( const rapidjson::Value& inValue )
{
   if( inValue.IsBool() )
   {
      m_Type    = RPCValueType_bool;
      m_BoolVal = inValue.GetBool();
   }
   else if( inValue.IsInt() ) // TODO: unsigned int types?
   {
      m_Type   = RPCValueType_int;
      m_IntVal = inValue.GetInt();
   }
   else if( inValue.IsInt64() )
   {
      m_Type   = RPCValueType_int;
      m_IntVal = inValue.GetInt64();
   }
   else if( inValue.IsDouble() )
   {
      m_Type      = RPCValueType_double;
      m_DoubleVal = inValue.GetDouble();
   }
   else if( inValue.IsString() )
   {
      m_Type      = RPCValueType_string;
      m_StringVal = inValue.GetString();
   }
   else if( inValue.IsObject() )
   {
      m_Type = RPCValueType_object;
      for( rapidjson::Value::ConstMemberIterator itr = inValue.MemberBegin();
            itr != inValue.MemberEnd(); ++itr )
      {
         const rapidjson::Value& nameItem( itr->name );
         const rapidjson::Value& valueItem( itr->value );

         RPCValue val;
         if( !val.fromJSONValue( valueItem ))
            return false;

         m_ObjectVal[ nameItem.GetString() ] = val;
      }
   }
   else if( inValue.IsArray() )
   {
      m_Type = RPCValueType_array;
      for( rapidjson::SizeType i = 0 ; i < inValue.Size() ; ++i )
      {
         RPCValue val;
         if( !val.fromJSONValue( inValue[ i ] ))
            return false;

         m_ArrayVal.push_back( val );
      }
   }
   else
   {
      return false;
   }

   return true;
}

// (recursively) convert types into a rapidjson::Value object (marshal)
bool RPCValue::toJSONValue( CPCAPI2::Json::StdStringWriter& writer ) const
{
   switch( m_Type )
   {
   case RPCValueType_bool:
      Json::Serialize(writer, m_BoolVal);
      break;
   case RPCValueType_int:
      Json::Serialize(writer, m_IntVal);
      break;
   case RPCValueType_double:
      Json::Serialize(writer, m_DoubleVal);
      break;
   case RPCValueType_string:
      Json::Serialize(writer, m_StringVal);
      break;
   case RPCValueType_object:
   {
      writer.StartObject();
      for( std::map< std::string, RPCValue >::const_iterator iter = m_ObjectVal.begin();
            iter != m_ObjectVal.end() ; ++iter )
      {
         const std::string& key( iter->first );
         const RPCValue& val( iter->second );

         writer.Key(key.c_str(), key.size());
         if(!val.toJSONValue(writer))
         {
            writer.EndObject();
            return false;
         }
      }
      writer.EndObject();
   } break;
   case RPCValueType_array:
   {
      writer.StartArray();
      for( std::list< RPCValue >::const_iterator iter = m_ArrayVal.begin() ;
            iter != m_ArrayVal.end() ; ++iter )
      {
         const RPCValue& arrayItem(*iter);
         if(!arrayItem.toJSONValue(writer))
         {
            writer.EndArray();
            return false;
         }
      }
      writer.EndArray();
   } break;
   default:
      // unknown type
      return false;
   }

   return true;
}
