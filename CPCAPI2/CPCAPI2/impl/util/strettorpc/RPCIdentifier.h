#pragma once
#ifndef __CPCAPI2_RPCIDENTIFIER_H__
#define __CPCAPI2_RPCIDENTIFIER_H__

namespace CPCAPI2
{
   /**
    * Please ensure OpenSSL random number has been seeded properly before using this
    * class, as the ID generation depends on this step being done properly.
    */
   class RPCIdentifier
   {
   public:
      RPCIdentifier(); // create a new unique identifier
      RPCIdentifier( const RPCIdentifier& that ); // copy ctor
      RPCIdentifier( const char *IDstr ); // parsed identifier
      ~RPCIdentifier();

      /**
       * Returns a string which is always 65 bytes long (64 plus a null char).
       * It is the ascii "hex" encoding of a sha256 value representing the ID.
       */
      const char *c_str() const;

      RPCIdentifier &operator=( const RPCIdentifier& that );
      bool operator==( const RPCIdentifier& that );

   private:
      char *m_pData;
   };
}

#endif // __CPCAPI2_RPCIDENTIFIER_H__
