#include "RPCResponse.h"

#include <document.h>

using namespace CPCAPI2;

#define STRETTO_RPC_VERSION_KEY "strettorpc"
#define STRETTO_RPC_VERSION_VAL "1.0"
#define STRETTO_RPC_ID_KEY      "id"
#define STRETTO_RESPONSE_RESULT "result"
#define STRETTO_RESPONSE_ERROR  "error"

RPCResponse::RPCResponse( void )
   : m_IsSuccess( false ), m_ErrorCode( 0 )
{
}

bool RPCResponse::isSuccessResponse()
{
   return m_IsSuccess;
}

int64_t RPCResponse::getErrorCode()
{
   return m_ErrorCode;
}

std::string RPCResponse::getErrorMessage()
{
   return m_ErrorMessage;
}

bool RPCResponse::getResultParams( std::map<std::string,RPCParam>& resultParams )
{
   resultParams = m_Params;
   return true;
}

bool RPCResponse::fromJSON( const rapidjson::Document & JSONdoc )
{
   // Success format:
   //
   // {
   //    "strettorpc" : "1.0",
   //    "result" : {
   //       "<res1-name>" : "<value1>",
   //       "<res2-name>" : "<value2>",
   //       "<res3-name>" : <value3>, ...
   //    },
   //    "id" : "<id-provided-by-request>"
   // }
   //
   // Error format:
   //
   // {
   //    "strettorpc" : "1.0",
   //    "error" : {
   //       "code" : <error-code>,
   //       "message" : "<error-description>"
   //    },
   //    "id" : "<id-provided-by-request>"
   // }

   if( !JSONdoc.HasMember( STRETTO_RPC_VERSION_KEY ))
      return false;

   if( !JSONdoc.HasMember( STRETTO_RPC_ID_KEY ))
      return false;

   // Parse out the ID
   m_ID = JSONdoc[ STRETTO_RPC_ID_KEY ].GetString();

   if( JSONdoc.HasMember( STRETTO_RESPONSE_ERROR ))
   {
      for( rapidjson::Value::ConstMemberIterator itr = JSONdoc.MemberBegin();
            itr != JSONdoc.MemberEnd(); ++itr )
      {
         if( strcmp( itr->name.GetString(), STRETTO_RESPONSE_ERROR ) != 0 )
            continue;

         if( !itr->value.HasMember( "code" ))
            return false;

         m_ErrorCode = itr->value[ "code" ].GetInt64();

         if( !itr->value.HasMember( "message" ))
            return false;

         m_ErrorMessage = itr->value[ "message" ].GetString();
      }
      m_IsSuccess = false;
      return true;
   }
   else if( JSONdoc.HasMember( STRETTO_RESPONSE_RESULT ))
   {
      // Top level should be a JSON 'object'
      for( rapidjson::Value::ConstMemberIterator itr = JSONdoc.MemberBegin();
            itr != JSONdoc.MemberEnd(); ++itr )
      {
         if( strcmp( itr->name.GetString(), STRETTO_RESPONSE_RESULT ) != 0 )
            continue;

         if( itr->value.IsObject() )
         {
            // items under "result" go into m_Params
            for( rapidjson::Value::ConstMemberIterator itr2 = itr->value.MemberBegin();
                 itr2 != itr->value.MemberEnd() ; ++itr2 )
            {
               RPCParam param;
               param.m_ParamName = itr2->name.GetString();
               if( !param.m_Value.fromJSONValue( itr2->value ))
                  return false;

               m_Params[ param.m_ParamName ] = param;
            }
         }
         else if( itr->value.IsArray() )
         {
            // This path happens on processing responses to "~end~" requests.
         }
         else
         {
            return false;
         }
      }
      m_IsSuccess = true;
      return true;
   }
   else
   {
      return false;
   }
}
