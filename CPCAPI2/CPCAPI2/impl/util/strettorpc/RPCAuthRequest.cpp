#include <openssl/sha.h>

#include <document.h>

#include "../MachineIdentification.h"
#include "../PlatformUtils.h"
#include "RPCAuthRequest.h"

using namespace CPCAPI2;

RPCAuthRequest::RPCAuthRequest(
   const std::string& authUser,
   const std::string& authPass,
   const std::string& sessionID )
   : RPCRequest( "~auth~" )
{
   // Example
   //
   // {
   //    "strettorpc" : "1.0",
   //    "method" : "~auth~",
   //    "params" : {
   //        "method" : "token",
   //        "mode" : "<auth-mode>",
   //        "user" : "<auth-user>",
   //        "passwd" : "<auth-password>",
   //        "session" : "<session-id>", // optional
   //    }
   // }

   // Create a sha256 digest of the password.
   char *data = new char[ ( SHA256_DIGEST_LENGTH * 2 ) + 1 ]; // ascii representation
   {
      uint8_t digest[ SHA256_DIGEST_LENGTH ]; // on the stack
      SHA256_CTX sha256;
      SHA256_Init( &sha256 );

      // Input the password
      SHA256_Update( &sha256, &authPass[ 0 ], authPass.size() );
      SHA256_Final( digest, &sha256 );

      for( int i = 0; i < SHA256_DIGEST_LENGTH; ++i )
         snprintf( data + ( i * 2 ), 3, "%02x", digest[ i ] );

      data[ SHA256_DIGEST_LENGTH * 2 ] = 0;
   }

   // Add params as outlined above
   addParameter( RPCParam( "method", RPCValue( "token" )));
   addParameter( RPCParam( "mode",   RPCValue( "SHA256" )));
   addParameter( RPCParam( "user",   RPCValue( authUser )));
   addParameter( RPCParam( "passwd", RPCValue( std::string( data ))));
   delete [] data;

   if( sessionID.length() > 0 ) // optional
      addParameter( RPCParam( "session", RPCValue( sessionID )));
}
