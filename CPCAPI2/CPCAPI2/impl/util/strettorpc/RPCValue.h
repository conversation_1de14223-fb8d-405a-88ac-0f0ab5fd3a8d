#pragma once
#ifndef __CPCAPI2_RPCVALUE_H__
#define __CPCAPI2_RPCVALUE_H__

#include <document.h>

#include <stdint.h>
#include <string>
#include <map>
#include <list>

#include <cpcstl/string.h>
#include <json/JsonHelper.h>

namespace CPCAPI2
{
   typedef enum RPCValueType
   {
      RPCValueType_bool,   // Bool is a special case of number
      RPCValueType_int,    // 64-bit
      RPCValueType_double,
      RPCValueType_string,
      RPCValueType_object,
      RPCValueType_array
   } RPCValueType_t;

   struct RPCValue
   {
      // ctors defined so RPCValue can be used in collections
      RPCValue() : m_Type( RPCValueType_bool ), m_BoolVal( false ) {}
      RPCValue( const bool& boolVal ) : m_Type( RPCValueType_bool ), m_BoolVal( boolVal ) {}
      RPCValue( const int64_t& intVal ) : m_Type( RPCValueType_int ), m_IntVal( intVal ) {}
      RPCValue( const double& doubleVal ) : m_Type( RPCValueType_double ), m_DoubleVal( doubleVal ) {}
      RPCValue( const char* strVal ) : m_Type( RPCValueType_string ), m_StringVal( strVal ) {}
      RPCValue( const std::string& strVal ) : m_Type( RPCValueType_string ), m_StringVal( strVal ) {}
      RPCValue( const cpc::string& strVal ) : m_Type( RPCValueType_string ), m_StringVal( strVal.c_str() ) {}

      RPCValue( const RPCValue& that ) :
         m_Type( that.m_Type ),
         m_BoolVal( that.m_BoolVal ),
         m_IntVal( that.m_IntVal ),
         m_DoubleVal( that.m_DoubleVal ),
         m_StringVal( that.m_StringVal ),
         m_ObjectVal( that.m_ObjectVal ),
         m_ArrayVal( that.m_ArrayVal ) {}

      // operator defined for collections
      RPCValue& operator=( const RPCValue& that );

      // operator defined for collections
      bool operator==( const RPCValue& that ) const;

      // populate values from rapidjson value (unmarshal)
      bool fromJSONValue( const rapidjson::Value& inValue );

      // convert types into a rapidjson::Value object (marshal)
      bool toJSONValue( CPCAPI2::Json::StdStringWriter& writer ) const;

      // Similar to a union, only one of these variables are
      // valid subject to m_Type.
      RPCValueType_t m_Type;

      // Primitive Value types
      bool        m_BoolVal;
      int64_t     m_IntVal;
      double      m_DoubleVal;
      std::string m_StringVal;

      // "An object is an unordered set of name/value pairs"
      std::map< std::string, RPCValue > m_ObjectVal;

      // "An array is an ordered collection of values"
      std::list< RPCValue > m_ArrayVal;
   };
}

#endif // __CPCAPI2_RPCVALUE_H__