#include "RPCIdentifier.h"
#include "RPCTimestamp.h"

#include <stdint.h>
#include <string.h>
#include <openssl/sha.h>
#include <openssl/rand.h>

#include <atomic>

#ifndef _WIN32
#include <sys/types.h>
#include <unistd.h>
#endif

#include "../MachineIdentification.h"

using namespace CPCAPI2;

#define SALT_SIZE 32

static std::atomic< bool > s_isSalty( 0 );
static uint8_t *s_MySalt = NULL;
static std::atomic< unsigned int > s_Counter( 0 );

/**
 * The ID just has to be 64 chars of uniquely generated content. As it turns
 * out, sha256 creates 64 chars as its output. So use that with some good
 * unique inputs.
 */
RPCIdentifier::RPCIdentifier()
   : m_pData( NULL )
{
   // Generate 32 bytes of salt if need be
   bool expected = false;
   if( s_isSalty.compare_exchange_strong( expected, true )) // thread protection
   {
      s_MySalt = new uint8_t[ SALT_SIZE ];
      RAND_bytes( s_MySalt, SALT_SIZE );
   }

   m_pData = new char[ ( SHA256_DIGEST_LENGTH * 2 ) + 1 ]; // ascii representation
   uint8_t digest[ SHA256_DIGEST_LENGTH ]; // on the stack
   SHA256_CTX sha256;
   SHA256_Init( &sha256 );

   // Input 1) The current time (milliseconds from the epoch)
   RPCTimestamp ts;
   int64_t curTime;
   if( ts.toMillis( curTime ))
      SHA256_Update(&sha256, &curTime, sizeof( int64_t ));

   // Input 2) The process ID
#ifdef _WIN32
   DWORD pid = GetCurrentProcessId();
   SHA256_Update( &sha256, &pid, sizeof( DWORD ));
#else
   pid_t pid = getpid();
   SHA256_Update( &sha256, &pid, sizeof( pid_t ));
#endif

   // Input 3) The machine ID
   std::string machineID( MachineIdentification::GetHardwareId() );
   SHA256_Update( &sha256, machineID.c_str(), machineID.length() );

   // Input 4) Throw in an instance count
   unsigned int count = s_Counter.fetch_add( 1 );
   SHA256_Update( &sha256, &count, sizeof( unsigned int ));

   // Input 5) Throw in a dash of salt
   SHA256_Update( &sha256, s_MySalt, SALT_SIZE );
   SHA256_Final( digest, &sha256 );

   for(int i = 0; i < SHA256_DIGEST_LENGTH; i++)
      snprintf( m_pData + ( i * 2 ), 3, "%02x", digest[ i ] );

   m_pData[ SHA256_DIGEST_LENGTH * 2 ] = 0;
}

RPCIdentifier::RPCIdentifier( const RPCIdentifier& that )
   : m_pData( NULL )
{
   int len = strlen( that.m_pData );
   m_pData = new char[ len + 1 ];
   strcpy( m_pData, that.m_pData );
   m_pData[ len ] = '\0';
}

RPCIdentifier::RPCIdentifier( const char * IDstr )
   : m_pData( NULL )
{
   if( IDstr != NULL )
   {
      int len = strlen( IDstr );
      m_pData = new char[ len + 1 ];
      strcpy( m_pData, IDstr );
      m_pData[ len ] = '\0';
   }
}

RPCIdentifier::~RPCIdentifier()
{
   delete [] m_pData;
}

const char *RPCIdentifier::c_str() const
{
   return m_pData;
}

RPCIdentifier & RPCIdentifier::operator=( const RPCIdentifier & that )
{
   // TODO: insert return statement here
   if( this == &that )
      return *this;

   if( m_pData != NULL )
      delete [] m_pData;

   int len = strlen( that.m_pData );
   m_pData = new char[ len + 1 ];
   strcpy( m_pData, that.m_pData );
   m_pData[ len ] = '\0';
   return *this;
}

bool RPCIdentifier::operator==( const RPCIdentifier& that )
{
   if( this == &that )
      return true; // referential comparison

   if( m_pData == that.m_pData )
      return true; // both NULL, or both point to the same thing

   if( m_pData == NULL || that.m_pData == NULL )
      return false; // One of them is null, and they are not both null

   // Neither is null. compare using traditional means
   return ( strcmp( m_pData, that.m_pData ) == 0 );
}
