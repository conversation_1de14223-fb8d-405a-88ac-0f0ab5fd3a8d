#pragma once
#ifndef __CPCAPI2_RPCAUTHREQUEST_H__
#define __CPCAPI2_RPCAUTHREQUEST_H__

#include <string>
#include "RPCRequest.h"

namespace CPCAPI2
{
   class RPCAuthRequest : public RPCRequest
   {
   public:
      RPCAuthRequest(
         const std::string& authUser,
         const std::string& authPass,
         const std::string& sessionID = "" );

      virtual ~RPCAuthRequest( void ) {}
   };
}

#endif // __CPCAPI2_RPCAUTHREQUEST_H__