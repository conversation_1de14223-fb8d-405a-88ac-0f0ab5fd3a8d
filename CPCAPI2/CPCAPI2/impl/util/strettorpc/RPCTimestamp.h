#pragma once
#ifndef __CPCAPI2_RPCTIMESTAMP_H__
#define __CPCAPI2_RPCTIMESTAMP_H__

#include <string>
#include <stdint.h>

namespace CPCAPI2
{
   class RPCTimestamp
   {
   public:
      RPCTimestamp(); // current time
      RPCTimestamp( const int64_t& millisSinceEpoch );
      RPCTimestamp( const std::string& stringFormatTime ); // Assumes UTC time
      RPCTimestamp( const RPCTimestamp& that );
      ~RPCTimestamp() {}

      bool toString( std::string& outString ) const;
      bool toMillis( int64_t& outMillis ) const;

      RPCTimestamp& operator=( const RPCTimestamp& that );

   private:
      int64_t m_MillisSinceEpoch; // internal representation
   };
}

#endif // __CPCAPI2_RPCTIMESTAMP_H__