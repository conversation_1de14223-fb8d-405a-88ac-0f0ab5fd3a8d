#include "RPCRequest.h"

#include <openssl/bio.h>
#include <openssl/evp.h>
#include <openssl/buffer.h>
#include <openssl/sha.h>

#include <document.h>
#include <stringbuffer.h>
#include <writer.h>

#include "RPCParam.h"

using namespace CPCAPI2;

RPCRequest::RPCRequest( const char *methodName )
{
   m_MethodName = methodName;
}

RPCRequest::RPCRequest( const char *methodName, const std::string& sessionID, const std::string& authToken )
{
   m_MethodName = methodName;
   m_SessionID  = sessionID;
   m_AuthToken  = authToken;
}

const char *RPCRequest::getMethodName() const
{
   return m_MethodName.c_str();
}

bool RPCRequest::addParameter( const RPCParam& value )
{
   std::map< std::string, RPCParam >::const_iterator iter = m_Params.find( value.m_ParamName );
   if( iter != m_Params.end() )
      return false; // param already exists with this name

   m_Params[ value.m_ParamName ] = value;
   return true;
}

void RPCRequest::toJSON( CPCAPI2::Json::StdStringBuffer& buffer ) const
{
   // EXAMPLE:
   //
   // {
   //    "strettorpc" : "1.0",
   //    "method" : "<method-name>",
   //    "params" : {
   //       "<p1-name>" : "<param1>",
   //       "<p2-name>" : <param2>,
   //       "<p3-name>" : "<param3>", ...
   //    },
   //    "id" : "<arbitrary-client-side-id>",
   //    "auxiliary" : {
   //       "auth-method"  : "token",
   //       "auth-session" : "<session-id from authentication>",
   //       "auth-data"    : "<authorization-token>"
   //    }
   // }
   //

   CPCAPI2::Json::StdStringWriter writer(buffer);

   writer.StartObject();

   // Basics of each request
   CPCAPI2::Json::Write( writer, "strettorpc", "1.0" );
   CPCAPI2::Json::Write( writer, "method",     getMethodName() );
   CPCAPI2::Json::Write( writer, "id",         m_ID.c_str() );

   // Add parameters
   if( m_Params.size() > 0 )
   {
      writer.Key("params");
      writer.StartObject(); // Start params

      std::map< std::string, RPCParam >::const_iterator iter = m_Params.begin();
      for( ; iter != m_Params.end() ; ++iter )
      {
         const RPCParam &param( iter->second );
         writer.Key(param.m_ParamName.c_str(), param.m_ParamName.size());
         param.m_Value.toJSONValue(writer);
      }

      writer.EndObject(); // End params
   }

   // Add the authentication section
   if( !m_AuthToken.empty() )
   {
      // Create a sha256 digest of the id and auth token.
      if( m_AuthTokenSHA256.empty() )
      {
         // To avoid reply attacks on less secure connections the method
         // authentication token is a one time token only and calculated each time
         // by base64 encode sha256(<id>:<obtained auth token for the session>).
         char *data = new char[ ( SHA256_DIGEST_LENGTH * 2 ) + 1 ]; // ascii representation
         {
            uint8_t digest[ SHA256_DIGEST_LENGTH ]; // on the stack
            SHA256_CTX sha256;
            SHA256_Init( &sha256 );

            // Input the items
            SHA256_Update( &sha256, m_ID.c_str(), strlen( m_ID.c_str() ));
            SHA256_Update( &sha256, ":", sizeof( char ));
            SHA256_Update( &sha256, m_AuthToken.c_str(), m_AuthToken.size() );
            SHA256_Final( digest, &sha256 );

            for( int i = 0; i < SHA256_DIGEST_LENGTH; ++i )
               snprintf( data + ( i * 2 ), 3, "%02x", digest[ i ] );

            data[ SHA256_DIGEST_LENGTH * 2 ] = 0;
         }

         m_AuthTokenSHA256 = data;
         delete [] data;

         // Still not done. the result has to be base64 encoded.
         BIO *bio, *b64;
         BUF_MEM *bufferPtr;

         b64 = BIO_new(BIO_f_base64());
         bio = BIO_new(BIO_s_mem());
         bio = BIO_push(b64, bio);

         BIO_set_flags( bio, BIO_FLAGS_BASE64_NO_NL ); // Ignore newlines
         BIO_write( bio, m_AuthTokenSHA256.c_str(), m_AuthTokenSHA256.size() );
         BIO_flush( bio );
         BIO_get_mem_ptr( bio, &bufferPtr );
         //BIO_set_close( bio, BIO_NOCLOSE );

         m_AuthTokenSHA256.assign( bufferPtr->data, bufferPtr->length );
         BIO_free_all( bio );
      }

      CPCAPI2::Json::WriteObject(writer, "auxiliary",
         "auth_method",  "token",
         "auth_session", m_SessionID,
         "auth_data", m_AuthTokenSHA256);
   }
   writer.EndObject();
}

RPCIdentifier RPCRequest::getID( void ) const
{
   return m_ID;
}
