#pragma once
#ifndef __CPCAPI2_RPCPARAM_H__
#define __CPCAPI2_RPCPARAM_H__

#include <document.h>
#include <stringbuffer.h>
#include <writer.h>

#include "RPCValue.h"

namespace CPCAPI2
{
   struct RPCParam
   {
      std::string m_ParamName;
      RPCValue    m_Value;

      RPCParam()
      {
         // only needed for container semantics
      }

      RPCParam( const char *name, const RPCValue& value )
      {
         m_ParamName = name;
         m_Value     = value;
      }

      // copy ctor
      RPCParam( const RPCParam& that )
      {
         this->m_ParamName        = that.m_ParamName;
         this->m_Value            = that.m_Value;
      }

      // operator overload for collection semantics
      RPCParam &operator=( const RPCParam& that )
      {
         if( this == &that )
            return *this;

         this->m_ParamName = that.m_ParamName;
         this->m_Value     = that.m_Value;
         return *this;
      }

      // operator overload for collection semantics
      bool operator==( const RPCParam& that )
      {
         if( this == &that )
            return true;

         if( this->m_ParamName != that.m_ParamName )
            return false;

         if( this->m_Value == that.m_Value )
            return true;

         return false;
      }
   };
}

#endif // __CPCAPI2_RPCPARAM_H__