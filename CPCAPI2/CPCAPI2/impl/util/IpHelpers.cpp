#include "IpHelpers.h"

#include <list>
#include <rutil/DnsUtil.hxx>
#include <rutil/WinCompat.hxx>
#include <resip/stack/Tuple.hxx>
#include <resip/stack/InternalTransport.hxx>
#include <rutil/GenericIPAddress.hxx>
#include "../util/cpc_logger.h"

using namespace CPCAPI2;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

//#error Posix implementation required

resip::Data IpHelpers::getPreferredLocalIpAddress()
{
   resip::Data result;
   std::list<std::pair<resip::Data,resip::Data> > ifs = resip::DnsUtil::getInterfaces();

   std::list<std::pair<resip::Data,resip::Data> >::const_iterator it = ifs.begin();
   for (; it != ifs.end(); ++it)
   {
      resip::Tuple t(it->second, 0, resip::UNKNOWN_TRANSPORT);
#ifdef CPCAPI2_USE_IPV6
      if (!t.isV4())
#else
      if (t.isV4())
#endif
      {
         return it->second;
      }
   }

   // if we get here, then try to return an IPv6 address
   it = ifs.begin();
   for (; it != ifs.end(); ++it)
   {
      return it->second;
   }

   return resip::Data::Empty;
}

resip::Data IpHelpers::getAnyLocalIpAddress(resip::IpVersion ipVersion)
{
   resip::Data result;
   std::list<std::pair<resip::Data,resip::Data> > ifs = resip::DnsUtil::getInterfaces();

   std::list<std::pair<resip::Data,resip::Data> >::const_iterator it = ifs.begin();
   for (; it != ifs.end(); ++it)
   {
      resip::Tuple t(it->second, 0, resip::UNKNOWN_TRANSPORT);

      if (t.ipVersion() == ipVersion)
      {
         return it->second;
      }
   }

   return resip::Data::Empty;
}

void IpHelpers::getPreferredLocalIpAddress(const resip::Tuple& target, resip::Data& srcIp)
{
   using namespace resip;
   {
      srcIp = Data::Empty;
      Tuple source(target);

#if defined(WIN32) && !defined(NO_IPHLPAPI)
      // !ds! TODO: technically this is wrong. the existence of the IP helper API should be
      // determined at RUNTIME and not at build-time (by trying to load the library). This
      // is because we build on machines that have the support but run on machines that
      // might not (assuming we support those machines anymore, I dunno)

      try
      {
         GenericIPAddress addr = WinCompat::determineSourceInterface(target.toGenericIPAddress());
         source.setSockaddr(addr);
      }
      catch (WinCompat::Exception&)
      {
         //XS_ASSERT_FAILURE("Can't find source interface to use");
         // !ds! may be normal with mixed IPv4/IPv6 setup, since DNS servers could come back
         // for interfaces which aren't up currently ?
      }
#else

      // !kh!
      // The connected UDP technique doesn't work all the time.
      // 1. Might not work on all implementaions as stated in UNP vol.1 8.14.
      // 2. Might not work under unspecified condition on Windows,
      //    search "getsockname" in MSDN library.
      // 3. We've experienced this issue on our production software.

      // this process will determine which interface the kernel would use to
      // send a packet to the target by making a connect call on a udp socket.
      Socket tmp = INVALID_SOCKET;
      Socket s = INVALID_SOCKET;
      Socket s6 = INVALID_SOCKET;
      if (target.isV4())
      {
         s = InternalTransport::socket(UDP, V4); // may throw
         tmp = s;
      }
      else
      {
         s6 = InternalTransport::socket(UDP, V6); // may throw
         tmp = s6;
      }

      int ret = connect(tmp, &target.getSockaddr(), target.length());

      if (ret < 0)
      {
         int e = getErrno();
         Transport::error( e );
         StackLog(<< "IpHelpers::getPreferredLocalIpAddress(): closing socket with fd: " << tmp << " after udp connect with ec: " << e);
         closeSocket(tmp);
         return;
      }

#ifdef __THREADX
      int len = source.length();
#else
      socklen_t len = source.length();
#endif
      ret = getsockname(tmp, &source.getMutableSockaddr(), &len);
      if (ret < 0)
      {
         int e = getErrno();
         Transport::error(e);
         StackLog(<< "IpHelpers::getPreferredLocalIpAddress(): closing socket with fd: " << tmp << " after getsockname with ec: " << e);
         closeSocket(tmp);
         return;
      }

      // Unconnect.
      // !jf! This is necessary, but I am not sure what we can do if this
      // fails. I'm not sure the stack can recover from this error condition.
      if (target.isV4())
      {
         GenericIPAddress unspecified;
         memset(&unspecified.v4Address, 0, sizeof(sockaddr_in));
         unspecified.v4Address.sin_family = AF_UNSPEC;

         ret = connect(s,
                       (struct sockaddr*)&unspecified.v4Address,
                       sizeof(unspecified.v4Address));
      }
#ifdef USE_IPV6
      else
      {
         GenericIPAddress unspecified6;
         memset(&unspecified6.v6Address, 0, sizeof(sockaddr_in6));
         unspecified6.v6Address.sin6_family = AF_UNSPEC;

         ret = connect(s6,
                       (struct sockaddr*)&unspecified6.v6Address,
                       sizeof(unspecified6.v6Address));
      }
#else
      else
      {
         assert(0);
      }
#endif

      if (ret < 0)
      {
         int e =  getErrno();
         //.dcm. OS X 10.5 workaround, we could #ifdef for specific OS X version.
         StackLog(<< "IpHelpers::getPreferredLocalIpAddress(): connect with fd: " << tmp << " rc: " << ret << " ec: " << e);
         if  (!(e ==EAFNOSUPPORT || e == EADDRNOTAVAIL))
         {
            Transport::error(e);
            StackLog(<< "IpHelpers::getPreferredLocalIpAddress(): closing socket with fd: " << tmp << " after connect with ec: " << e);
            closeSocket(tmp);
            return;
         }
      }
#endif

      source.setPort(0);
      srcIp = source.presentationFormat();

#if !defined(WIN32) || defined(NO_IPHLPAPI)
      StackLog(<< "IpHelpers::getPreferredLocalIpAddress(): closing socket with fd: " << tmp << " with acquired ip: " << srcIp);
      closeSocket(tmp);
#endif
   }
}

bool IpHelpers::isAvailableIpAddress(const resip::Data& address)
{
   using namespace resip;
   {
      if (isEqualNoCase(address, "127.0.0.1") || isEqualNoCase(address, "::1"))
      {
         return true;
      }

      typedef std::list<std::pair<Data,Data> > InterfaceList;
      InterfaceList interfaces = DnsUtil::getInterfaces();
      InterfaceList::const_iterator it = interfaces.begin();
      for (; it != interfaces.end(); it++)
      {
         if (isEqualNoCase(it->second, address))
         {
            return true;
         }
      }
   }
   return false;
}

using namespace boost::asio::ip;

#ifdef ANDROID
#include <arpa/inet.h>
#include <sys/socket.h>
#include <webrtc/base/ifaddrs-android.h>

int IpHelpers::getSubnetMask(const address_v4& address, address_v4& mask)
{
   struct ifaddrs *ifap, *ifa;
   struct sockaddr_in *sa, *ifsa;
   struct in_addr addr;

   inet_pton(AF_INET, address.to_string().c_str(), &addr);

   rtc::getifaddrs(&ifap);

   for (ifa = ifap; ifa; ifa = ifa->ifa_next)
   {
      ifsa = (struct sockaddr_in *) ifa->ifa_addr;
      if (ifsa->sin_addr.s_addr == addr.s_addr)
      {
         sa = (struct sockaddr_in *) ifa->ifa_netmask;
         mask = make_address_v4(inet_ntoa(sa->sin_addr));
         break;
      }
   }

   rtc::freeifaddrs(ifap);

   if (mask.is_unspecified())
   {
      mask = address_v4::netmask(address);
      return -1;
   }

   return 0;
}

#elif defined(__APPLE__)

#include <ifaddrs.h>

int IpHelpers::getSubnetMask(const address_v4& address, address_v4& mask)
{
   struct ifaddrs *ifap, *ifa;
   struct sockaddr_in *sa, *ifsa;
   struct in_addr addr;

   inet_pton(AF_INET, address.to_string().c_str(), &addr);

   if (getifaddrs(&ifap) == 0)
   {
      for (ifa = ifap; ifa; ifa = ifa->ifa_next)
      {
         ifsa = (struct sockaddr_in *) ifa->ifa_addr;
         if (ifsa->sin_addr.s_addr == addr.s_addr)
         {
            sa = (struct sockaddr_in *) ifa->ifa_netmask;
            mask = make_address_v4(inet_ntoa(sa->sin_addr));
            break;
         }
      }
   }

   freeifaddrs(ifap);

   if (mask.is_unspecified())
   {
      mask = address_v4::netmask(address);
      return -1;
   }

   return 0;
}

#elif defined (_WIN32)

#include <winsock2.h>
#include <iphlpapi.h>
#include <stdio.h>
#include <stdlib.h>

// Link with Iphlpapi.lib
#pragma comment(lib, "IPHLPAPI.lib")

#define WORKING_BUFFER_SIZE 15000
#define MAX_TRIES 3

#define MALLOC(x) HeapAlloc(GetProcessHeap(), 0, (x))
#define FREE(x) HeapFree(GetProcessHeap(), 0, (x))

// lifted from https://docs.microsoft.com/en-us/windows/win32/api/iphlpapi/nf-iphlpapi-getadaptersaddresse
int IpHelpers::getSubnetMask(const address_v4& address, address_v4& retMask)
{
   bool foundSubnet = false;

   /* Declare and initialize variables */

   DWORD dwSize = 0;
   DWORD dwRetVal = 0;

   unsigned int i = 0;

   // Set the flags to pass to GetAdaptersAddresses
   ULONG flags = GAA_FLAG_INCLUDE_PREFIX;

   // default to unspecified address family (both)
   ULONG family = AF_UNSPEC;

   LPVOID lpMsgBuf = NULL;

   PIP_ADAPTER_ADDRESSES pAddresses = NULL;
   ULONG outBufLen = 0;
   ULONG Iterations = 0;

   PIP_ADAPTER_ADDRESSES pCurrAddresses = NULL;
   PIP_ADAPTER_UNICAST_ADDRESS pUnicast = NULL;
   PIP_ADAPTER_ANYCAST_ADDRESS pAnycast = NULL;
   PIP_ADAPTER_MULTICAST_ADDRESS pMulticast = NULL;
   IP_ADAPTER_DNS_SERVER_ADDRESS* pDnServer = NULL;
   IP_ADAPTER_PREFIX* pPrefix = NULL;

   family = AF_INET;

   struct in_addr targetAddr;
   inet_pton(AF_INET, address.to_string().c_str(), &targetAddr);

   // Allocate a 15 KB buffer to start with.
   outBufLen = WORKING_BUFFER_SIZE;

   do {

      pAddresses = (IP_ADAPTER_ADDRESSES*)MALLOC(outBufLen);
      if (pAddresses == NULL) {
         return kError;
      }

      dwRetVal =
         GetAdaptersAddresses(family, flags, NULL, pAddresses, &outBufLen);

      if (dwRetVal == ERROR_BUFFER_OVERFLOW) {
         FREE(pAddresses);
         pAddresses = NULL;
      }
      else {
         break;
      }

      Iterations++;

   } while ((dwRetVal == ERROR_BUFFER_OVERFLOW) && (Iterations < MAX_TRIES));

   if (dwRetVal == NO_ERROR) {
      // If successful, output some information from the data we received
      pCurrAddresses = pAddresses;
      while (pCurrAddresses) {

         pUnicast = pCurrAddresses->FirstUnicastAddress;
         if (pUnicast != NULL) 
         {
            for (i = 0; pUnicast != NULL; i++)
            {
               //resip::Data a = resip::DnsUtil::inet_ntop(targetAddr);
               //resip::Data b = resip::DnsUtil::inet_ntop(((sockaddr_in*)pUnicast->Address.lpSockaddr)->sin_addr);

               if (memcmp(&targetAddr,
                          &((sockaddr_in*)pUnicast->Address.lpSockaddr)->sin_addr, 
                          4) == 0)
               {
                  ULONG mask;
                  if (NO_ERROR == ConvertLengthToIpv4Mask(pUnicast->OnLinkPrefixLength, &mask))
                  {
                     retMask = address_v4(ntohl(mask));
                     foundSubnet = true;
                     break;
                  }
               }

               pUnicast = pUnicast->Next;
            }
         }

         pCurrAddresses = pCurrAddresses->Next;
      }
   }
   else 
   {
      ErrLog(<< "Call to GetAdaptersAddresses failed with error: " << dwRetVal);
   }

   if (pAddresses) 
   {
      FREE(pAddresses);
   }

   return foundSubnet ? kSuccess : kError;
}

#else

int IpHelpers::getSubnetMask(const address_v4& address, address_v4& mask)
{
   return -1;
}
#endif

int IpHelpers::getSubnetIpRange(const address_v4& address, const address_v4& netmask, std::vector<address_v4>& targets, unsigned int limit, boost::asio::ip::address_v4* skipAddress)
{
   //TODO: check if netmask is valid?

   unsigned int minAddr = address.to_uint() & netmask.to_uint();
   unsigned int maxAddr = address.to_uint() | ~netmask.to_uint();

   targets.clear();

   while (++minAddr < maxAddr)
   {
      address_v4 target = make_address_v4(minAddr);
      if (skipAddress && *skipAddress == target)
      {
         continue;
      }

      targets.push_back(target);

      if (limit > 0 && targets.size() >= limit)
      {
         return -1;
      }
   }

   return 0;
}

int IpHelpers::getSubnetIpRange(const address_v4& address, const address_v4& netmask, std::vector<boost::asio::ip::udp::endpoint>& targets, int port, unsigned int limit, boost::asio::ip::address_v4* skipAddress)
{
   std::vector<address_v4> ipAddrs;
   int ret = getSubnetIpRange(address, netmask, ipAddrs, limit, skipAddress);
   for (const boost::asio::ip::address_v4& ipV4 : ipAddrs)
   {
      boost::asio::ip::udp::endpoint endpoint(ipV4, port);
      targets.push_back(endpoint);
   }

   return ret;
}

resip::Data IpHelpers::stripPort(const resip::Data& addr)
{
   unsigned int pos = addr.find(":");
   if (pos != resip::Data::npos)
   {
      if (addr.find(":", pos + 1) != resip::Data::npos)
      {
         //this can be an IPv6 address, abort
         return addr;
      }
      return addr.substr(0, pos);
   }
   return addr;
}
