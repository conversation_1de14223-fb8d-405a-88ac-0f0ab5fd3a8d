#include "CurlPPHelper.h"

#include <string>
#include <functional>

#include <curlpp/Types.hpp>
#include "../util/cpc_logger.h"
#include <openssl/err.h>

#include "SecureDns.h"
#include "FilePathUtils.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace CPCAPI2;
using namespace curlpp::options;

// do-nothing progress function which is just here to avoid having lubcurl print
// out nonsensical stats to stdout.
static int defaultProgress( double, double, double, double )
{
   return 0;
}

CurlPPHelper::CurlPPHelper( void )
{
   // Use default ctor for settings
}

CurlPPHelper::~CurlPPHelper( void )
{
   // avoid leaving any OpenSSL errors hanging around
   ERR_clear_error();
}

bool CurlPPHelper::setDefaultOptions(
   curlpp::Easy & request,
   const std::string& url,
   const std::string& method,
   size_t bodySize )
{
   request.setOpt( new Verbose( false ));
   request.setOpt( new NoSignal( true ));
   request.setOpt( new Encoding( "" ));
   request.setOpt( new FollowLocation( true ));
   request.setOpt( new MaxRedirs( 3 ));
   request.setOpt( new UnrestrictedAuth( true ));
   request.setOpt( new HttpVersion( CURL_HTTP_VERSION_NONE ));
   request.setOpt( new NoProgress( 0 ));

#if !defined(__linux__) // causes crash on linux; TODO figure out why
// TODO: temporary workaround for linker error on xmpp agent on osx
#if !(defined(TARGET_OS_MAC) && (PRODUCT_NAME == cpcapi_xmpp_agent))
   request.setOpt( new ProgressFunction( defaultProgress ));
#endif
#endif

#if (__linux__ && !(ANDROID)) 
   // TODO: should we clear CURLOPT_CAPATH and CURLOPT_CAINFO on linux too? 
   std::string caInfo;
   if (FilePathUtils::getCABundlePath(caInfo))
   {
      CURL *h = request.getHandle();
      curl_easy_setopt( h, CURLOPT_CAINFO, caInfo.c_str() );
   }
#else 
   // Had to do it this way since setting it via the curlpp API doesn't
   // seem to do the trick.
   CURL *h = request.getHandle();
   curl_easy_setopt( h, CURLOPT_CAPATH, NULL );
   curl_easy_setopt( h, CURLOPT_CAINFO, NULL );
#endif

   // now prepare the method specific stuff.
   if( method == "POST" )
   {
      request.setOpt( new Post( true ));
      request.setOpt( new PostFieldSize( bodySize ));
      //request.setOpt( new PostFieldSizeLarge( bodySize ));
      //request.setOpt( PostFields( 0 ));
   }
   else if( method == "PUT" )
   {
      request.setOpt( new Upload( true ));
      request.setOpt( new InfileSize( bodySize ));
   }
   else if( method == "DELETE" )
   {
      request.setOpt( new CustomRequest( "DELETE" ));
      request.setOpt( new PostFieldSize( bodySize ));
      //request.setOpt( new PostFieldSizeLarge( bodySize ));
      //request.setOpt( PostFields( 0 ));
   }
   else if( method == "HEAD" )
   {
      request.setOpt( new NoBody( true ));
   }
   else // if( method == "GET" )
   {
      request.setOpt( new HttpGet( true ));
   }

#if (__linux__ && !(ANDROID)) 
#else 
   struct curl_slist *hostaddrs = NULL;
   if (Utils::SecureDns::doCurloptResolve(url, hostaddrs))
   {
      curl_easy_setopt(h, CURLOPT_RESOLVE, hostaddrs);
   }
   curl_slist_free_all(hostaddrs);
#endif

   request.setOpt( new Url( url ));
   return true;
}

void CurlPPHelper::setProxyOptions( curlpp::Easy & request, std::string proxyAddr, int proxyPort, std::string proxyBypassList )
{
   request.setOpt( new Proxy( proxyAddr ));
   if( proxyPort > 0 )
      request.setOpt( new ProxyPort( proxyPort ));

   if( proxyBypassList.size() > 0 )
      request.setOpt( new curlpp::OptionTrait< std::string, CURLOPT_NOPROXY >( proxyBypassList ));
}

void CurlPPHelper::setDebugLoggingOptions( curlpp::Easy & request, bool enableDebugLogging )
{
   request.setOpt(Verbose(enableDebugLogging));
   if (enableDebugLogging)
   {
      curlpp::types::DebugFunctionFunctor f = [](curl_infotype infoType, char * msg, size_t msgSize)
      {
         if (infoType == CURLINFO_TEXT || infoType == CURLINFO_HEADER_IN || infoType == CURLINFO_HEADER_OUT)
         {
            DebugLog(<< "curl debug log: " << std::string(msg, msgSize));
         }
         return 0;
      };
      request.setOpt(DebugFunction(f));
   }
   else
   {
      request.setOpt(DebugFunction());
   }
}

void CurlPPHelper::setCookieOptions( curlpp::Easy & request, std::string cookieFile )
{
   if( cookieFile.size() > 0 )
   {
      request.setOpt( new CookieFile( cookieFile ));
      request.setOpt( new CookieJar( cookieFile ));
   }
}

void CurlPPHelper::setAuthOptions( curlpp::Easy & request, const std::string& authUser, const std::string& authPasswd )
{
   // user authentication
   request.setOpt( new HttpAuth( CURLAUTH_ANY ));
   request.setOpt( new curlpp::OptionTrait< std::string, CURLOPT_USERNAME >( authUser ));
   request.setOpt( new curlpp::OptionTrait< std::string, CURLOPT_PASSWORD >( authPasswd ));
}

void CurlPPHelper::setCertMismatchOptions( curlpp::Easy & request, bool allowed )
{
   request.setOpt( SslVerifyHost( !allowed ));
   request.setOpt( SslVerifyPeer( !allowed ));
}

void CurlPPHelper::setTimeoutOption( curlpp::Easy & request, unsigned long timeoutSeconds )
{
   if( timeoutSeconds > 0 )
      request.setOpt( new Timeout( timeoutSeconds ));
}
