#pragma once

#if !defined(CPCAPI2_DUM_FP_COMMAND_H)
#define CPCAPI2_DUM_FP_COMMAND_H

#include "cpcapi2defs.h"

#include <rutil/Reactor.hxx>

#define STRINGIZE2(s) #s
#if defined(CPCAPI2_AUTO_TEST)
  #define makeFpCommand(_func, _handler, _arg0, _arg1) (_handler != NULL ? new AutoTestReadCallback(resip::resip_bind(&_func, _handler, _arg0, _arg1), #_func, std::make_tuple(_arg0, _arg1)) : NULL)
  #define makeFpCommand1(_func, _handler, _arg0) (_handler != NULL ? new AutoTestReadCallback(resip::resip_bind(&_func, _handler, _arg0), #_func, std::make_tuple(0, _arg0)) : NULL)
  #define cpcFunc(_func) #_func,&_func
  #define cpcEvent(_class, _func) (_class*)nullptr, STRINGIZE2(_class::_func), &_class::_func
  #define makeFpCommandNew(_funcName, _func, _handler, ...) (_handler != NULL ? new AutoTestReadCallback(resip::resip_bind(_func, _handler, ##__VA_ARGS__), _funcName, std::make_tuple(__VA_ARGS__)) : NULL)
#else
  #define makeFpCommand(_func, _handler, ...) (_handler != NULL ? resip::resip_bind(&_func, _handler, __VA_ARGS__) : NULL)
  #define makeFpCommand1(_func, _handler, ...) (_handler != NULL ? resip::resip_bind(&_func, _handler, __VA_ARGS__) : NULL)
  #define cpcFunc(_func) #_func,&_func
  #define cpcEvent(_class, _func) (_class*)nullptr, STRINGIZE2(_class::_func), &_class::_func
  #define makeFpCommandNew(_funcName, _func, _handler, ...) (_handler != NULL ? resip::resip_bind(_func, _handler, ##__VA_ARGS__) : NULL)
#endif

namespace CPCAPI2
{
struct ReadCallback : public resip::ReadCallbackBase
{
   ReadCallback(std::function<void()> f) : mF(f) {}
   virtual void operator()() { mF(); }
   virtual void* address() { return NULL; }
private:
   std::function<void()> mF;
};

#ifdef CPCAPI2_AUTO_TEST
class DumFpCommandArgsBase
{
public:
   DumFpCommandArgsBase() {}
   virtual ~DumFpCommandArgsBase() {}

   virtual int numArgs() = 0;
};

class DumFpCommandArgs0 : public DumFpCommandArgsBase
{
public:
   DumFpCommandArgs0() {}
   virtual ~DumFpCommandArgs0() {}

   virtual int numArgs() { return 0; };
};

template<typename TArg0>
class DumFpCommandArgs1 : public DumFpCommandArgsBase
{
public:
   DumFpCommandArgs1(const std::tuple<TArg0>& args) : mArgs(args) {}
   virtual ~DumFpCommandArgs1() {}

   virtual int numArgs() { return 1; };
   const std::tuple<TArg0>& args() const { return mArgs; }

private:
   std::tuple<TArg0> mArgs;
};

template<typename TArg0, typename TArg1>
class DumFpCommandArgs2 : public DumFpCommandArgsBase
{
public:
   DumFpCommandArgs2(const std::tuple<TArg0, TArg1>& args) : mArgs(args) {}
   virtual ~DumFpCommandArgs2() {}

   virtual int numArgs() { return 2; };
   const std::tuple<TArg0, TArg1>& args() const { return mArgs; }

private:
   std::tuple<TArg0, TArg1> mArgs;
};

class AutoTestReadCallback : public resip::ReadCallbackBase
{
public:
   AutoTestReadCallback(resip::ReadCallbackBase* cb,
                       const std::string& eventName,
                       const std::tuple<>& args)
     : mF(cb), mEventName(eventName), mArgs(new DumFpCommandArgs0())
   {}

   template<typename TArg0>
   AutoTestReadCallback(resip::ReadCallbackBase* cb,
                         const std::string& eventName,
                         const std::tuple<TArg0>& args)
       : mF(cb), mEventName(eventName), mArgs(new DumFpCommandArgs1<TArg0>(args))
   {}

   template<typename TArg0, typename TArg1>
   AutoTestReadCallback(resip::ReadCallbackBase* cb,
                        const std::string& eventName,
                        const std::tuple<TArg0, TArg1>& args)
      : mF(cb), mEventName(eventName), mArgs(new DumFpCommandArgs2<TArg0, TArg1>(args))
   {}

   virtual ~AutoTestReadCallback()
   {
      delete mF;
      delete mArgs;
   }

   virtual void operator()()
   {
      (*mF)();
   }

   const std::string& eventName() const { return mEventName; }

   virtual void* address() { return NULL; }

   int numArgs()
   {
      return mArgs->numArgs();
   }

   template<typename TArg0> std::tuple<TArg0> args() const
   {
      DumFpCommandArgs1<TArg0>* args = dynamic_cast<DumFpCommandArgs1<TArg0>*>(mArgs);
      if (args)
      {
         return args->args();
      }
      else
      {
         throw "Invalid AutoTestReadCallback type params. Check the type of arg0 you are passing to expectEvent";
      }
   }

   template<typename TArg0, typename TArg1> std::tuple<TArg0, TArg1> args() const
   {
      DumFpCommandArgs2<TArg0, TArg1>* args = dynamic_cast<DumFpCommandArgs2<TArg0, TArg1>*>(mArgs);
      if (args)
      {
         return args->args();
      }
      else
      {
         throw "Invalid AutoTestReadCallback type params. Check the type and order of arg0, arg1 you are passing to expectEvent";
      }
   }

   CPCAPI2::SipAccount::SipAccountHandle accountHandle;

private:
   resip::ReadCallbackBase* mF;
   std::string mEventName;
   DumFpCommandArgsBase* mArgs;
};
#else
#endif

}

#endif // CPCAPI2_DUM_FP_COMMAND_H
