#pragma once

#if !defined(CPCAPI2_CPC_LOGGER_H)
#define CPCAPI2_CPC_LOGGER_H

#include "LogSubsystems.h"
#include <rutil/Logger.hxx>

namespace CPCAPI2
{
template<typename T>
inline resip::Data printTokens(const T& toks)
{
   resip::Data retVal;
   {
      resip::DataStream ds(retVal);
      typename T::const_iterator it = toks.begin();
      for (; it != toks.end(); )
      {
         ds << *it;
         ++it;
         if (it != toks.end())
         {
            ds << ", ";
         }
      }
   }
   return retVal;
}
}

#endif