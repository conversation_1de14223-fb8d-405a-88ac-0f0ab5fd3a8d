#include "brand_branded.h"

#include "HttpClientImpl.h"
#include "ProxyHelper.h"
#include "cpc_logger.h"

#include <curl/curl.h>

#include <openssl/ssl.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

#if (OPENSSL_VERSION_NUMBER < 0x10100000)
#define SSL_OP_NO_TLSv1_3 0
#endif

using namespace CPCAPI2;
using namespace xten;

HTTPClientImpl::HTTPClientImpl(PhoneInterface* p)
   : mHTTP(NULL)
   , mHTTPSession(NULL)
   , mHTTPIndependent(NULL)
   , mHTTPSessionIndependent(NULL)
   , mPhone(p)
{
}

HTTPClientImpl::~HTTPClientImpl()
{
   delete mHTTPSessionIndependent;
   delete mHTTPIndependent;
   delete mHTTPSession;
   delete mHTTP;
}

HTTPClient::EHTTPError HTTPClientImpl::ConvertErrorCode(int code)
{
   switch (code)
   {
   case CurlHttp::Ok:
      return HTTPClient::EHTTPOk;
   case CurlHttp::Lookup:
      return HTTPClient::EHTTPLookup;
   case CurlHttp::Auth:
      return HTTPClient::EHTTPAuth;
   case CurlHttp::Connect:
      return HTTPClient::EHTTPConnect;
   case CurlHttp::Embedded:
      return HTTPClient::EHTTPEmbedded;
   case CurlHttp::ResponseBodySizeLimitExceeded:
      return HTTPClient::EHTTPResponseBodySizeLimitExceeded;
   default:
      return HTTPClient::EHTTPUnknownError;
   }
}

static int curlWriter(char *data, size_t size, size_t nmemb, std::string *buffer)
{
   int result = 0;

   if (buffer && data)
   {
      buffer->append(data, size * nmemb);
      result = size * nmemb;
   }

   return result;
}

void HTTPClientImpl::processTLSOptions(CurlHttp::CurlOptions& curlOptions, TLSVersion tlsVersion, CipherSuite cipherSuite, SslCipherOptions cipherOptions)
{
   curlOptions.sslCiphers = (cipherSuite.empty()) ? cipherOptions.getCiphers(SslCipherUsageHttp) : cipherSuite;
   switch ((TLS_DEFAULT == tlsVersion) ? cipherOptions.getTLSVersion(SslCipherUsageHttp) : tlsVersion)
   {
      case TLS_NONE:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_2 | SSL_OP_NO_TLSv1_3;
         break;
      case TLS_V1_0:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_2 | SSL_OP_NO_TLSv1_3;
         break;
      case TLS_V1_1:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_2 | SSL_OP_NO_TLSv1_3;
         break;
      case TLS_V1_2:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_3;
         break;
      case TLS_V1_3:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_1 | SSL_OP_NO_TLSv1_2;
         break;
      case TLS_DEFAULT:
      case TLS_HIGHEST:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3;
         break;
      case TLS_NON_DEPRECATED:
         curlOptions.sslOptions = SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3 | SSL_OP_NO_TLSv1 | SSL_OP_NO_TLSv1_1;
         break;
   }
}

void HTTPClientImpl::HTTPSendMessage(
      HTTPClient::EHTTPVerb eInVerb,
      const char* szInURL,
      const char* szInMIMEType,
      const char* szInUsername,
      const char* szInPassword,
      const char* szInClientCertificate,
      const char* szInClientCertificatePasswd,
      const char* pInMessageBody,
      unsigned long ulInMessageLengthInBytes,
      unsigned long /*ulInMaxReadRate*/,
      TLSVersion tlsVersion,
      CipherSuite cipherSuite,
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      bool verboseLogging,
      bool suppressLogging,
      int& resultErrorCode,
      int& curlErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      HTTPClient::RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string,
      const cpc::string& overrideProxyAddress,
      bool bUseHttp2,
      unsigned long timeoutSeconds,
      size_t maxResponseBodySizeBytes,
      cpc::vector<cpc::string> requiredCertPublicKeys,
      unsigned long dynamicTimeoutSeconds
      )
{
#if (CPCAPI2_BRAND_HTTP_MODULE == 1)
   bool bReturnResult = false;
   //IHTTPSinkPtr ipSink = xs_proxy_cast_with_synchronous_fallback<IHTTPSink>(ipInSink.get());

   {
      if (szInURL == NULL)
      {
         WarningLog(<< "Empty URL given for HTTP request");
         return;
      }

      // Opens an HTTP session for a given site

      std::string cURL(szInURL);
      std::string cName, cPassword, cClientCertificate, cClientCertificatePasswd, cCookiefile, cUserAgent;
      if (szInUsername) cName = szInUsername;
      if (szInPassword) cPassword = szInPassword;
      if (szInClientCertificate) cClientCertificate = szInClientCertificate;
      if (szInClientCertificatePasswd) cClientCertificatePasswd = szInClientCertificatePasswd;
      if (szInCookieFile) cCookiefile = szInCookieFile;
      if (szInUserAgent) cUserAgent = szInUserAgent;

      std::string certFolder = mCertStorageFolder.c_str();
      bool bUseCertFolderStorageOnly = false;

      std::vector<std::string> tRequiredPublicKeys;
      for (cpc::vector<cpc::string>::const_iterator it = requiredCertPublicKeys.begin(); it != requiredCertPublicKeys.end(); it++)
      {
         tRequiredPublicKeys.push_back((*it).c_str());
      }

      {
         resip::Lock lock (mMutex);

         CurlHttp::CurlOptions curlOptions;
         curlOptions.maxResponseBodySizeBytes = maxResponseBodySizeBytes;
         if (NULL != mPhone)
            processTLSOptions(curlOptions, tlsVersion, cipherSuite, mPhone->getSslCipherOptions());
         else
            processTLSOptions(curlOptions, tlsVersion, cipherSuite, SslCipherOptions());

         if (mHTTPIndependent != NULL) delete mHTTPIndependent;
         mHTTPIndependent = new CurlHttp(cURL, cName, cPassword, cClientCertificate, cClientCertificatePasswd, certFolder, bUseCertFolderStorageOnly,
                                         bInUseEmbeddedCert1, bInUseEmbeddedCert2, "", "", bInEnableCookies, cCookiefile, curlOptions, tRequiredPublicKeys);
         if (mHTTPSessionIndependent != NULL) delete mHTTPSessionIndependent;
         mHTTPSessionIndependent = new CurlHttp::Session(*mHTTPIndependent);

         if (bUseHttp2)
            mHTTPSessionIndependent->setHttpVersion(CURL_HTTP_VERSION_2_0);
      }

      // should we be sloppy about the certificates when using TLS ?

      unsigned long flags = 0;

#if SUA_SSL_ALLOW_NOT_YET_VALID
      flags |= HTTPClient::E_CERT_NOT_YET_VALID;
#endif

#if SUA_SSL_ALLOW_ID_MISMATCH
      flags |= HTTPClient::E_CERT_ID_MISMATCH;
#endif

#if SUA_SSL_ALLOW_UNTRUSTED_CERT
      flags |= HTTPClient::E_CERT_NOT_TRUSTED;
#endif

#if SUA_SSL_ALLOW_EXPIRED_CERT
      flags |= HTTPClient::E_CERT_EXPIRED;
#endif

      if(bInIgnoreCertErrors)
         flags = HTTPClient::E_CERT_WHATEVER_ERROR;

      if(flags) mHTTPSessionIndependent->setAcceptableFailures(flags);

      if (suppressLogging)
      {
         mHTTPSessionIndependent->disableLogging();
      }
      else
      {
         mHTTPSessionIndependent->setVerboseLogging(verboseLogging);
      }

      if(timeoutSeconds > 0) mHTTPSessionIndependent->setTimeout(timeoutSeconds);
      mHTTPSessionIndependent->setDynamicTimeout(dynamicTimeoutSeconds); // if 0, need to pass in

      // Send HTTP request
      std::string szVerb;
      switch (eInVerb)
      {
         case HTTPClient::EHTTPVerbUnparsed:
         case HTTPClient::EHTTPVerbUnknown:
         case HTTPClient::EHTTPVerbInvalid:
         case HTTPClient::EHTTPVerbMaximum:
            {
               WarningLog(<< "Invalid EHTTPVerb given.");
               return;
            }
         case HTTPClient::EHTTPVerbOPTIONS:     szVerb = "OPTIONS";     break;
         case HTTPClient::EHTTPVerbGET:         szVerb = "GET";         break;
         case HTTPClient::EHTTPVerbHEAD:        szVerb = "HEAD";        break;
         case HTTPClient::EHTTPVerbPOST:        szVerb = "POST";        break;
         case HTTPClient::EHTTPVerbPUT:         szVerb = "PUT";         break;
         case HTTPClient::EHTTPVerbDELETE:      szVerb = "DELETE";      break;
         case HTTPClient::EHTTPVerbTRACE:       szVerb = "TRACE";       break;
         case HTTPClient::EHTTPVerbCONNECT:     szVerb = "CONNECT";     break;
         case HTTPClient::EHTTPVerbTRACK:       szVerb = "TRACK";       break;
         case HTTPClient::EHTTPVerbMOVE:        szVerb = "MOVE";        break;
         case HTTPClient::EHTTPVerbCOPY:        szVerb = "COPY";        break;
         case HTTPClient::EHTTPVerbPROPFIND:    szVerb = "PROPFIND";    break;
         case HTTPClient::EHTTPVerbPROPPATCH:   szVerb = "PROPPATCH";   break;
         case HTTPClient::EHTTPVerbMKCOL:       szVerb = "MKCOL";       break;
         case HTTPClient::EHTTPVerbLOCK:        szVerb = "LOCK";        break;
         case HTTPClient::EHTTPVerbUNLOCK:      szVerb = "UNLOCK";      break;
         case HTTPClient::EHTTPVerbSEARCH:      szVerb = "SEARCH";      break;
      }// switch (eInVerb)

      // Create request header
      // Accept header
      CurlHttp::HttpHeaderList tHeaderList;
      std::string cHeadername("Accept");
      std::string cHeaderValue("*/*");
      CurlHttp::HttpHeader tHeaderAccept(cHeadername, cHeaderValue);
      tHeaderList.push_back(tHeaderAccept);

      // User-Agent header
      cHeadername = "User-Agent";
      CurlHttp::HttpHeader tHeaderClientID(cHeadername, cUserAgent);
      tHeaderList.push_back(tHeaderClientID);

      // All custom headers specified
      for (cpc::vector<CPCAPI2::HTTPClient::StringPair>::const_iterator it = customHeaders.begin(); it != customHeaders.end(); it++)
      {
         CurlHttp::HttpHeader customHeader(it->first.c_str(), it->second.c_str());
         tHeaderList.push_back(customHeader);
      }

      // Use a proxy to connect?
      // TO-DO: Retry scheme with/without using the proxy server?
      setProxy(mHTTPSessionIndependent, szInURL, overrideProxyAddress);

      if (!suppressLogging)
      {
         InfoLog(<< "Sending HTTP request to " << szInURL << ".");
      }
      std::shared_ptr<CurlHttp::Result> pHTTPResult;
      if (szVerb == "GET")
      {
         CurlURI uri(szInURL);
         pHTTPResult = mHTTPSessionIndependent->get(uri.path(), tHeaderList);
      }
      else
      {
         if (szInMIMEType)
         {
            cHeadername = "Content-Type";
            cHeaderValue = szInMIMEType;
            CurlHttp::HttpHeader tHeaderClientID(cHeadername, cHeaderValue);
            tHeaderList.push_back(tHeaderClientID);
         }// if (szInMIMEType)

         CurlURI uri(szInURL);

         if (ulInMessageLengthInBytes && pInMessageBody != NULL)
            pHTTPResult = mHTTPSessionIndependent->doRequest(szVerb, uri.path(), pInMessageBody, ulInMessageLengthInBytes, tHeaderList);
         else
            pHTTPResult = mHTTPSessionIndependent->doRequest(szVerb, uri.path(), tHeaderList);
      }
      if (!pHTTPResult->success())
      {
         if (!suppressLogging)
         {
            InfoLog(<< "HTTP request to " << szInURL << " has failed.");
         }
         resultErrorCode = -1;
         curlErrorCode = pHTTPResult->getCurlError();
         responseStatus = ConvertErrorCode(pHTTPResult->getResultCode());
      }
      else
      {
         if (!suppressLogging)
         {
            InfoLog(<< "HTTP request to " << szInURL << " was successful. Response code: " << pHTTPResult->getStatusCode() << ".");
         }
         resultErrorCode = 0;
         curlErrorCode = 0;
         responseStatus = pHTTPResult->getStatusCode();

         contentType = pHTTPResult->getContentType();


         try
         {
            result.assign(pHTTPResult->getBodyOctets(), pHTTPResult->getBodyLength());
         }
         catch (const std::bad_alloc&)
         {
            InfoLog(<< "HTTP request to " << szInURL << " has failed -- bad alloc (response too big?)");
            resultErrorCode = -1;
            responseStatus = HTTPClient::EHTTPUnknownError;
         }

         redirectInfo.code = pHTTPResult->getRedirectCode();
         redirectInfo.count = pHTTPResult->getRedirectCount();
         redirectInfo.url = pHTTPResult->getRedirectUrl().c_str();
      }
   }
#else
   resultErrorCode = -1;
#endif // CPCAPI2_BRAND_HTTP_MODULE
}

void HTTPClientImpl::StartHTTPSession(
      HTTPClient::EHTTPVerb eInVerb,
      const char* szInURL,
      const char* szInMIMEType,
      const char* szInUsername,
      const char* szInPassword,
      const char* szInClientCertificate,
      const char* szInClientCertificatePasswd,
      const char* pInMessageBody,
      unsigned long ulInMessageLengthInBytes,
      unsigned long /*ulInMaxReadRate*/,
      TLSVersion tlsVersion,
      CipherSuite cipherSuite,
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,
      int& curlErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      HTTPClient::RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress,
      bool bUseHttp2,
      unsigned long timeoutSeconds,
      size_t maxResponseBodySizeBytes,
      bool verboseLogging,
      unsigned long dynamicTimeoutSeconds
      )
{
#if (CPCAPI2_BRAND_HTTP_MODULE == 1)
   bool bReturnResult = false;
   //IHTTPSinkPtr ipSink = xs_proxy_cast_with_synchronous_fallback<IHTTPSink>(ipInSink.get());

   {
      if (szInURL == NULL)
      {
         return;
      }

      // Opens an HTTP session for a given site

      std::string cURL(szInURL);
      std::string cName, cPassword, cClientCertificate, cClientCertificatePasswd, cCookiefile, cUserAgent;
      if (szInUsername) cName = szInUsername;
      if (szInPassword) cPassword = szInPassword;
      if (szInClientCertificate) cClientCertificate = szInClientCertificate;
      if (szInClientCertificatePasswd) cClientCertificatePasswd = szInClientCertificatePasswd;
      if (szInCookieFile) cCookiefile = szInCookieFile;
      if (szInUserAgent) cUserAgent = szInUserAgent;

      std::string certFolder = mCertStorageFolder.c_str();
      bool bUseCertFolderStorageOnly = false;

      {
         resip::Lock lock (mMutex);

         CurlHttp::CurlOptions curlOptions;
         curlOptions.maxResponseBodySizeBytes = maxResponseBodySizeBytes;
         if (NULL != mPhone)
            processTLSOptions(curlOptions, tlsVersion, cipherSuite, mPhone->getSslCipherOptions());
         else
            processTLSOptions(curlOptions, tlsVersion, cipherSuite, SslCipherOptions());

         if (mHTTP != NULL) delete mHTTP;
         mHTTP = new CurlHttp(cURL, cName, cPassword, cClientCertificate, cClientCertificatePasswd, certFolder,
                              bUseCertFolderStorageOnly, bInUseEmbeddedCert1, bInUseEmbeddedCert2, "", "",
                              bInEnableCookies, szInCookieFile, curlOptions);
         if (mHTTPSession != NULL) delete mHTTPSession;
         mHTTPSession = new CurlHttp::Session(*mHTTP);

         if (bUseHttp2)
            mHTTPSession->setHttpVersion(CURL_HTTP_VERSION_2_0);
      }

      // should we be sloppy about the certificates when using TLS ?

      unsigned long flags = 0;

#if SUA_SSL_ALLOW_NOT_YET_VALID
      flags |= HTTPClient::E_CERT_NOT_YET_VALID;
#endif

#if SUA_SSL_ALLOW_ID_MISMATCH
      flags |= HTTPClient::E_CERT_ID_MISMATCH;
#endif

#if SUA_SSL_ALLOW_UNTRUSTED_CERT
      flags |= HTTPClient::E_CERT_NOT_TRUSTED;
#endif

#if SUA_SSL_ALLOW_EXPIRED_CERT
      flags |= HTTPClient::E_CERT_EXPIRED;
#endif

      if(bInIgnoreCertErrors)
         flags = HTTPClient::E_CERT_WHATEVER_ERROR;

      if(flags) mHTTPSession->setAcceptableFailures(flags);

      mHTTPSession->setVerboseLogging(verboseLogging);

      if(timeoutSeconds > 0) mHTTPSession->setTimeout(timeoutSeconds);
      mHTTPSession->setDynamicTimeout(dynamicTimeoutSeconds); // if 0, need to pass in

      // Send HTTP request
      std::string szVerb;
      switch (eInVerb)
      {
         case HTTPClient::EHTTPVerbUnparsed:
         case HTTPClient::EHTTPVerbUnknown:
         case HTTPClient::EHTTPVerbInvalid:
         case HTTPClient::EHTTPVerbMaximum:
            {
               return;
            }
         case HTTPClient::EHTTPVerbOPTIONS:     szVerb = "OPTIONS";     break;
         case HTTPClient::EHTTPVerbGET:         szVerb = "GET";         break;
         case HTTPClient::EHTTPVerbHEAD:        szVerb = "HEAD";        break;
         case HTTPClient::EHTTPVerbPOST:        szVerb = "POST";        break;
         case HTTPClient::EHTTPVerbPUT:         szVerb = "PUT";         break;
         case HTTPClient::EHTTPVerbDELETE:      szVerb = "DELETE";      break;
         case HTTPClient::EHTTPVerbTRACE:       szVerb = "TRACE";       break;
         case HTTPClient::EHTTPVerbCONNECT:     szVerb = "CONNECT";     break;
         case HTTPClient::EHTTPVerbTRACK:       szVerb = "TRACK";       break;
         case HTTPClient::EHTTPVerbMOVE:        szVerb = "MOVE";        break;
         case HTTPClient::EHTTPVerbCOPY:        szVerb = "COPY";        break;
         case HTTPClient::EHTTPVerbPROPFIND:    szVerb = "PROPFIND";    break;
         case HTTPClient::EHTTPVerbPROPPATCH:   szVerb = "PROPPATCH";   break;
         case HTTPClient::EHTTPVerbMKCOL:       szVerb = "MKCOL";       break;
         case HTTPClient::EHTTPVerbLOCK:        szVerb = "LOCK";        break;
         case HTTPClient::EHTTPVerbUNLOCK:      szVerb = "UNLOCK";      break;
         case HTTPClient::EHTTPVerbSEARCH:      szVerb = "SEARCH";      break;
      }// switch (eInVerb)

      // Create request header
      CurlHttp::HttpHeaderList tHeaderList;
      std::string cHeadername("Accept");
      std::string cHeaderValue("*/*");
      CurlHttp::HttpHeader tHeaderAccept(cHeadername, cHeaderValue);
      tHeaderList.push_back(tHeaderAccept);
      cHeadername = "User-Agent";

      CurlHttp::HttpHeader tHeaderClientID(cHeadername, cUserAgent);

      tHeaderList.push_back(tHeaderClientID);

      // All custom headers specified
      for (cpc::vector<CPCAPI2::HTTPClient::StringPair>::const_iterator it = customHeaders.begin(); it != customHeaders.end(); it++)
      {
         CurlHttp::HttpHeader customHeader(it->first.c_str(), it->second.c_str());
         tHeaderList.push_back(customHeader);
      }

      // Use a proxy to connect?
      // TO-DO: Retry scheme with/without using the proxy server?
      setProxy(mHTTPSession, szInURL, overrideProxyAddress);

      std::shared_ptr<CurlHttp::Result> pHTTPResult;
      if (szVerb == "GET")
      {
         CurlURI uri(szInURL);
         pHTTPResult = mHTTPSession->get(uri.path(), tHeaderList);
      }
      else
      {
         if (szInMIMEType)
         {
            cHeadername = "Content-Type";
            cHeaderValue = szInMIMEType;
            CurlHttp::HttpHeader tHeaderClientID(cHeadername, cHeaderValue);
            tHeaderList.push_back(tHeaderClientID);
         }// if (szInMIMEType)

         CurlURI uri(szInURL);

         if (ulInMessageLengthInBytes && pInMessageBody != NULL)
            pHTTPResult = mHTTPSession->doRequest(szVerb, uri.path(), pInMessageBody, ulInMessageLengthInBytes, tHeaderList);
         else
            pHTTPResult = mHTTPSession->doRequest(szVerb, uri.path(), tHeaderList);
      }
      if (!pHTTPResult->success())
      {
         resultErrorCode = -1;
         curlErrorCode = pHTTPResult->getCurlError();
         responseStatus = ConvertErrorCode(pHTTPResult->getResultCode());
      }
      else
      {
         resultErrorCode = 0;
         curlErrorCode = 0;
         responseStatus = pHTTPResult->getStatusCode();

         contentType = pHTTPResult->getContentType();

         redirectInfo.code = pHTTPResult->getRedirectCode();
         redirectInfo.count = pHTTPResult->getRedirectCount();
         redirectInfo.url = pHTTPResult->getRedirectUrl().c_str();

         try
         {
            result.assign(pHTTPResult->getBodyOctets(), pHTTPResult->getBodyLength());
         }
         catch (const std::bad_alloc&)
         {
            InfoLog(<< "HTTP request to " << szInURL << " has failed -- bad alloc (response too big?)");
            resultErrorCode = -1;
            responseStatus = HTTPClient::EHTTPUnknownError;
         }
      }
   }
#else
   resultErrorCode = -1;
#endif // CPCAPI2_BRAND_HTTP_MODULE
}

void
HTTPClientImpl::setProxy(CurlHttp::Session * httpSession, const cpc::string& destination, const cpc::string& overrideProxy)
{
   std::string strProxyHost;
   cpc::string bypassList;

   if (!overrideProxy.empty())
   {
      strProxyHost = overrideProxy.c_str();
   }
   else
   {
      strProxyHost = ProxyHelper::GetProxyServerInfo(destination, bypassList);
   }

   if (!strProxyHost.empty())
   {
      DebugLog(<< "Proxy server set to " << strProxyHost << ", exceptions list: " << bypassList << ".");
      // no need to set the port
      httpSession->setProxyServer(strProxyHost.c_str(), 0, bypassList);
   }

}

void HTTPClientImpl::DoSessionRequest(
      HTTPClient::EHTTPVerb eInVerb,
      const char* szInURL,
      const char* szInMIMEType,
      const char* szInUsername,
      const char* szInPassword,
      const char* szInClientCertificate,
      const char* szInClientCertificatePasswd,
      const char* pInMessageBody,
      unsigned long ulInMessageLengthInBytes,
      unsigned long /*ulInMaxReadRate*/,
      TLSVersion tlsVersion,
      CipherSuite cipherSuite,
      bool bInUseEmbeddedCert1,
      bool bInUseEmbeddedCert2,
      bool bInIgnoreCertErrors,
      bool bInEnableCookies,
      const char* szInCookieFile,
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,
      int& curlErrorCode,
      int& responseStatus,
      cpc::string& contentType,
      cpc::string& result,
      HTTPClient::RedirectInfo& redirectInfo,
      const char* szInUserAgent,                // User Agent string
      const cpc::string& overrideProxyAddress,
      bool verboseLogging
      )
{
#if (CPCAPI2_BRAND_HTTP_MODULE == 1)
   bool bReturnResult = false;
   //IHTTPSinkPtr ipSink = xs_proxy_cast_with_synchronous_fallback<IHTTPSink>(ipInSink.get());

   {
      if (szInURL == NULL)
      {
         return;
      }

      // Opens an HTTP session for a given site

      //std::string cURL(szInURL);
      std::string cName, cPassword, cClientCertificate, cClientCertificatePasswd, cCookiefile, cUserAgent;
      if (szInUsername) cName = szInUsername;
      if (szInPassword) cPassword = szInPassword;
      if (szInClientCertificate) cClientCertificate = szInClientCertificate;
      if (szInClientCertificatePasswd) cClientCertificatePasswd = szInClientCertificatePasswd;
      if (szInCookieFile) cCookiefile = szInCookieFile;
      if (szInUserAgent) cUserAgent = szInUserAgent;

      std::string certFolder = mCertStorageFolder.c_str();
      bool bUseCertFolderStorageOnly = false;

      //{
      //   resip::Lock lock (mMutex);
      //   mHTTP = new CurlHttp(cURL, cName, cPassword, cClientCertificate, cClientCertificatePasswd, certFolder, bUseCertFolderStorageOnly, bInUseEmbeddedCert1, bInUseEmbeddedCert2, "", "", bInEnableCookies, szInCookieFile);
      //   mHTTPSession = new CurlHttp::Session(*mHTTP);
      //}

      // should we be sloppy about the certificates when using TLS ?

      unsigned long flags = 0;

#if SUA_SSL_ALLOW_NOT_YET_VALID
      flags |= HTTPClient::E_CERT_NOT_YET_VALID;
#endif

#if SUA_SSL_ALLOW_ID_MISMATCH
      flags |= HTTPClient::E_CERT_ID_MISMATCH;
#endif

#if SUA_SSL_ALLOW_UNTRUSTED_CERT
      flags |= HTTPClient::E_CERT_NOT_TRUSTED;
#endif

#if SUA_SSL_ALLOW_EXPIRED_CERT
      flags |= HTTPClient::E_CERT_EXPIRED;
#endif

      if(bInIgnoreCertErrors)
         flags = HTTPClient::E_CERT_WHATEVER_ERROR;

      if(flags) mHTTPSession->setAcceptableFailures(flags);

      mHTTPSession->setVerboseLogging(verboseLogging);

      // Send HTTP request
      std::string szVerb;
      switch (eInVerb)
      {
         case HTTPClient::EHTTPVerbUnparsed:
         case HTTPClient::EHTTPVerbUnknown:
         case HTTPClient::EHTTPVerbInvalid:
         case HTTPClient::EHTTPVerbMaximum:
            {
               return;
            }
         case HTTPClient::EHTTPVerbOPTIONS:     szVerb = "OPTIONS";     break;
         case HTTPClient::EHTTPVerbGET:         szVerb = "GET";         break;
         case HTTPClient::EHTTPVerbHEAD:        szVerb = "HEAD";        break;
         case HTTPClient::EHTTPVerbPOST:        szVerb = "POST";        break;
         case HTTPClient::EHTTPVerbPUT:         szVerb = "PUT";         break;
         case HTTPClient::EHTTPVerbDELETE:      szVerb = "DELETE";      break;
         case HTTPClient::EHTTPVerbTRACE:       szVerb = "TRACE";       break;
         case HTTPClient::EHTTPVerbCONNECT:     szVerb = "CONNECT";     break;
         case HTTPClient::EHTTPVerbTRACK:       szVerb = "TRACK";       break;
         case HTTPClient::EHTTPVerbMOVE:        szVerb = "MOVE";        break;
         case HTTPClient::EHTTPVerbCOPY:        szVerb = "COPY";        break;
         case HTTPClient::EHTTPVerbPROPFIND:    szVerb = "PROPFIND";    break;
         case HTTPClient::EHTTPVerbPROPPATCH:   szVerb = "PROPPATCH";   break;
         case HTTPClient::EHTTPVerbMKCOL:       szVerb = "MKCOL";       break;
         case HTTPClient::EHTTPVerbLOCK:        szVerb = "LOCK";        break;
         case HTTPClient::EHTTPVerbUNLOCK:      szVerb = "UNLOCK";      break;
         case HTTPClient::EHTTPVerbSEARCH:      szVerb = "SEARCH";      break;
      }// switch (eInVerb)

      // Create request header
      CurlHttp::HttpHeaderList tHeaderList;
      std::string cHeadername("Accept");
      std::string cHeaderValue("*/*");
      CurlHttp::HttpHeader tHeaderAccept(cHeadername, cHeaderValue);
      tHeaderList.push_back(tHeaderAccept);
      cHeadername = "User-Agent";

      CurlHttp::HttpHeader tHeaderClientID(cHeadername, cUserAgent);

      tHeaderList.push_back(tHeaderClientID);

      // All custom headers specified
      for (cpc::vector<CPCAPI2::HTTPClient::StringPair>::const_iterator it = customHeaders.begin(); it != customHeaders.end(); it++)
      {
         CurlHttp::HttpHeader customHeader(it->first.c_str(), it->second.c_str());
         tHeaderList.push_back(customHeader);
      }

      // Use a proxy to connect?
      // TO-DO: Retry scheme with/without using the proxy server?
      setProxy(mHTTPSession, szInURL, overrideProxyAddress);

      std::shared_ptr<CurlHttp::Result> pHTTPResult;
      if (szVerb == "GET")
      {
         CurlURI uri(szInURL);
         pHTTPResult = mHTTPSession->get(uri.path(), tHeaderList);
      }
      else
      {
         if (szInMIMEType)
         {
            cHeadername = "Content-Type";
            cHeaderValue = szInMIMEType;
            CurlHttp::HttpHeader tHeaderClientID(cHeadername, cHeaderValue);
            tHeaderList.push_back(tHeaderClientID);
         }// if (szInMIMEType)

         CurlURI uri(szInURL);

         if (ulInMessageLengthInBytes && pInMessageBody != NULL)
            pHTTPResult = mHTTPSession->doRequest(szVerb, uri.path(), pInMessageBody, ulInMessageLengthInBytes, tHeaderList);
         else
            pHTTPResult = mHTTPSession->doRequest(szVerb, uri.path(), tHeaderList);
      }
      if (!pHTTPResult->success())
      {
         resultErrorCode = -1;
         curlErrorCode = pHTTPResult->getCurlError();
         responseStatus = ConvertErrorCode(pHTTPResult->getResultCode());
      }
      else
      {
         resultErrorCode = 0;
         curlErrorCode = 0;
         responseStatus = pHTTPResult->getStatusCode();

         contentType = pHTTPResult->getContentType();

         try
         {
            result.assign(pHTTPResult->getBodyOctets(), pHTTPResult->getBodyLength());
         }
         catch (const std::bad_alloc&)
         {
            InfoLog(<< "HTTP request to " << szInURL << " has failed -- bad alloc (response too big?)");
            responseStatus = HTTPClient::EHTTPUnknownError;
         }
      }
   }
#else
   resultErrorCode = -1;
#endif // CPCAPI2_BRAND_HTTP_MODULE
}

void HTTPClientImpl::Abort()
{
#if (CPCAPI2_BRAND_HTTP_MODULE == 1)
   resip::Lock lock (mMutex);
   if (mHTTPSession != NULL) mHTTPSession->abort();
   if (mHTTPSessionIndependent != NULL) mHTTPSessionIndependent->abort();
#else
#endif // CPCAPI2_BRAND_HTTP_MODULE
}
