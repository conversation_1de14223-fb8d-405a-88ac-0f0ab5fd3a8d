#ifndef _CURL_HTTP_H_
#define _CURL_HTTP_H_

#include <atomic>

#include <time.h>
#include <string>
#include <list>
#include <vector>
#include <map>
#include <memory>
#include <chrono>

#include <rutil/Mutex.hxx>

#include <curl/curl.h>
#undef X509_NAME
#include <openssl/x509.h>

#include "CurlURI.h"

namespace xten
{

///////////////////////////////////////////////////////////////////////////////
class CurlHttp
{
   public:

      enum NameType
      {
         SubjectAltName,
         CommonName
      };

      struct PeerName
      {
         NameType mType;
         std::string mName;
         PeerName(NameType type, std::string name): mType(type), mName(name){}
      };

      static bool initializeCurl();
      static void uninitializeCurl();
      static void abortAll();
      static std::string getCertName(X509 *cert);

      struct CurlOptions
      {
         // maximum response body size in bytes. 0 for unlimited. cURL will not start the transfer if the file requested
         // is found to be larger. Note that some servers will not return the size of the file, so this may
         // not always work. maximum supported size is 2 GB.
         int maxResponseBodySizeBytes;
         std::string sslCiphers;
         long sslOptions;

         CurlOptions()
         {
            maxResponseBodySizeBytes = 0;
            sslCiphers = "";
            sslOptions = 0;
         }

      };

      //check to see if null path is accepted by neon
      CurlHttp(const std::string& hostUri, const std::string& user, const std::string& pass, const std::string& clientCertificate,
               const std::string& clientCertificatePasswd, const std::string& certificateFolder = "", bool bUseCertFolderOnly = false,
               bool bUseEmbeddedCert1 = false, bool bUseEmbeddedCert2 = false,/*bool bUseEmbeddedCert = false,*/
               const std::string& authuser= "", const std::string& authpass="",
               bool bUseCookies = false, const std::string cookieFile = "", const CurlOptions& options = CurlOptions(), const std::vector<std::string> requiredCertPublicKeys = std::vector<std::string>());
      virtual ~CurlHttp() {};

      // general helper functions
      static std::string make_rfc1123_date(const time_t&);
      static time_t parse_time_string(const std::string&);

      enum ResultCode
      {
         Ok = 0, // Success
         Error,  // Generic error; use Session::getLastError for message
         Lookup,  // Server or proxy hostname lookup failed:   CURLE_COULDNT_RESOLVE_PROXY
                  //                                           CURLE_COULDNT_RESOLVE_HOST
         Auth,  // User authentication failed on server      ?
         Proxyauth,  // User authentication failed on proxy  ?
         Connect,  // Could not connect to server              CURLE_COULDNT_CONNECT
                   //                                          CURLE_SSL_CONNECT_ERROR
         Timeout,  // Connection timed out                     CURLE_OPERATION_TIMEDOUT
         Failed, // The precondition failed                    CURLE_FAILED_INIT
         Retry,  // Retry request (ne_end_request ONLY)
         Redirect, // See ne_redirect.h

         Embedded,  //Embedded cert could not be loaded

         Aborted,

         ResponseBodySizeLimitExceeded // size specified in maxResponseBodySizeBytes was exceeded
      };

      static ResultCode translateCurlError(CURLcode);

      class Session;

      class Result
      {
         public:
            Result();
            Result(const std::list<std::string>&, const std::string&);  // parsing constructor headerlist, bodydata

            bool success() const { return mResultCode == Ok; }

            ResultCode getResultCode() const { return mResultCode; }
            int getCurlError() const { return mCurlError; }
            int getStatusCode() const { return mStatusCode; }

            //length 0 & body null if no body encountered in response
            size_t getBodyLength() const { return mBodyLength; }
            const char* getBodyOctets() const { return mBodyOctets; }

            //Result will no longer clean up body.
            void takeOwnershipOfBody() { mOwnBody = false; }

            //null if no modtime was returned
            const time_t* getModtime() const { return mModTime; }

            const char* getEtag() const { return mEtag; }
            const char* getContentType() const { return mContentType; }

            std::vector<std::string> getHeaders(){ return mHeaders; }

            unsigned int getRedirectCode() const { return mRedirectCode; }
            unsigned int getRedirectCount() const { return static_cast<int>(mRedirectCount); }
            std::vector<std::string> getRedirectHeaders() { return mRedirectHeaders; }
            std::string getRedirectUrl() { return mRedirectUrl; }

            virtual ~Result();
         protected:
            friend class Session;

            int mStatusCode;
            ResultCode mResultCode;
            int mCurlError;
            unsigned int mRedirectCode;
            long mRedirectCount;
            size_t mBodyLength;
            char* mBodyOctets;
            time_t* mModTime;
            char* mEtag;
            bool mOwnBody;
            char* mContentType;
            std::vector<std::string> mHeaders;
            std::vector<std::string> mRedirectHeaders;
            std::string mRedirectUrl;

      };

      class HttpHeader
      {
         public:
            HttpHeader(const std::string& name, const std::string& value) :
               mName(name),
               mValue(value)
            {}

            const std::string& getName() const { return mName; }
            const std::string& getValue() const { return mValue; }

         private:
            std::string mName;
            std::string mValue;
      };

      typedef std::list<HttpHeader> HttpHeaderList;
      static HttpHeaderList EmptyHttpHeaderList;

      typedef void (*onProgressCallback)(void *userdata, unsigned int progress);

      class Session
      {
         public:
            Session(CurlHttp& CurlHttp);
            virtual ~Session();

            // callback functions used by cURL
            static int curlWriter(char *data, size_t size, size_t nmemb, void *userdata);
            static int curlHeaderWriter(char *data, size_t size, size_t nmemb, void *userdata);
            static size_t curlReader(void *ptr, size_t size, size_t nmemb, void *userdata);
            static int curlProgress(void *p, double dltotal, double dlnow, double ult, double uln);
            static curlioerr ioctlCallback(CURL*,int, void*);
            static int seekCallback(void*, curl_off_t, int);

            // setup functions
            const char *getLastError()
            {
               const char *err = curl_easy_strerror(mLastError);
               return err;
            }
            void setProxyServer(const char *hostname, unsigned int port, const char *bypassList = "");
            void setTimeout(unsigned long timeoutSec);
            void setDynamicTimeout(unsigned long timeoutSec);
            void setVerboseLogging(bool enabled);
            void disableLogging();
            void setHttpVersion(unsigned int version);
            virtual void setAcceptableFailures(int failures)
            {
               mAcceptFailures = failures;
            }
            virtual int getAcceptableFailures() {return mAcceptFailures;}
            void setProgressCallback(onProgressCallback, void*);
            void abort();
            bool aborted();
            std::string& getClientCert(){return mCurlHttp.mClientCertificate;}
            std::string& getClientCertPasswd(){return mCurlHttp.mClientCertificatePasswd;}
            std::string& getCertificatesFolder(){return mCurlHttp.mCertificatesFolder;}
            bool getUseCertFolderOnly(){return mCurlHttp.mUseCertificatefolderOnly;}
            bool getUseEmbeddedCert1(){return mCurlHttp.mUseEmbeddedCert1;}
            bool getUseEmbeddedCert2(){return mCurlHttp.mUseEmbeddedCert2;}
            void setCommonName(const char* name){if(name) mCommonName = name;}
            void setEmbeddedCertError(bool bErr){mEmbeddedCertError = bErr;}
            std::string& getCommonName(){return mCommonName;}
            //bool getUseCookies(){return mCurlHttp.mUseCookies;}
            //std::string& getCookieFile(){return mCurlHttp.mCookieFile;}

            void getRedirectInfo(long &redirectCount, unsigned int &redirectCode, std::string& redirectUrl);

            // operations
            virtual std::unique_ptr<Result> deleteUri(const std::string&, const HttpHeaderList& = EmptyHttpHeaderList);
            virtual std::unique_ptr<Result> head(const std::string&, const HttpHeaderList& = EmptyHttpHeaderList);

            virtual std::unique_ptr<Result> put(const std::string&, const char*, size_t, const HttpHeaderList& = EmptyHttpHeaderList);
            virtual std::unique_ptr<Result> putIfNotModified(const std::string&, const char*, size_t, time_t, const HttpHeaderList& = EmptyHttpHeaderList);
            virtual std::unique_ptr<Result> putIfMatch(const std::string&, const char*, size_t, const std::string&, const HttpHeaderList& = EmptyHttpHeaderList);


            virtual std::unique_ptr<Result> get(const std::string&, const HttpHeaderList& = EmptyHttpHeaderList);
            virtual std::unique_ptr<Result> getIfModified(const std::string&, time_t, const HttpHeaderList& = EmptyHttpHeaderList);
            virtual std::unique_ptr<Result> getIfNoneMatch(const std::string& path, const std::string& etag,
                                           const HttpHeaderList& additionalHeaders = EmptyHttpHeaderList);


            virtual std::unique_ptr<Result> doRequest(const std::string&, const std::string&, const HttpHeaderList&);
            virtual std::unique_ptr<Result> doRequest(const std::string&, const std::string&, const char*, size_t, const HttpHeaderList&);

            long sslCtxOptions() { return mSslCtxOptions; }

         protected:

            //flavour(webdav, xcap) specific request handling. Default is a null-op
            //virtual void flavourSpecific(ne_request *req, const std::string& method, const std::string& path);
            CurlHttp& mCurlHttp;
            CURL *mHandle;
            int mAcceptFailures;
            std::string mBodyBuffer;
            std::list<std::string> mHeaderBuffer;
            std::list<std::string> mRedirectBuffer; // for redirect headers
            char* mOutBuffer;
            size_t mOutBufferSize;
            curl_off_t mOutBufferPos;
            int  mAuthenticating;
            onProgressCallback mProgressCallback;
            void*              mProgressUserData;
            volatile bool mAborted;
            bool mEmbeddedCertError;
            std::string mCommonName;
            CURLcode mLastError;
            resip::Mutex mAbortedLock;

            unsigned long mDynamicTimeoutSec;
            double mCurrentUpload;
            double mCurrentDownload;
            UInt64 mLastProgressTimeSec;
            long mSslCtxOptions;
            bool mDisableLogging;
      };

   protected:
      static void getCertNames(X509 *cert, std::list<PeerName> &peerNames);
      std::string explodePathMacros(const std::string&);

   protected:
      friend class Session;

      std::string mUser;
      std::string mPassword;

	  // XCAP will use different username and password for authentication/authorization
	  // in server credential mode
      std::string mAuthUser;
      std::string mAuthPassword;

      //std::string mScheme;
      //std::string mHost;
      //int mPort;
      std::string mURI;
      std::string mClientCertificate;
      std::string mClientCertificatePasswd;
      std::string mCertificatesFolder;
      bool mUseCertificatefolderOnly;
      bool mUseEmbeddedCert1;
      bool mUseEmbeddedCert2;
      bool mUseCookies;
      std::string mCookieFile;
      std::vector<std::string> mRequiredCertPublicKeys;

      CurlOptions mOptions;

      static std::atomic< bool > sCurlInitialized;
      static std::map<std::string,int> mMonthToNum;

};

}
#endif   // _NEON_HTTP_H_
