#include "BrandingHelper.h"
#include <regex>
#include <boost/algorithm/string/case_conv.hpp>

using namespace CPCAPI2;

// Convert wilcard string from branding into usable regex:
// *.cymbus.net *.alianza.com
// ([a-z0-9-]+\.)*cymbus\.net$|([a-z0-9-]+\.)*alianza\.com$
int BrandingHelper::wildcardDomainToRegEx(const std::string& wildcard, std::string& regEx)
{
   regEx.clear();
   for (int i = 0; i < wildcard.length(); i++)
   {
      if (wildcard[i] == '.')
      {
         if (i > 0 && wildcard[i - 1] == '*')
            regEx += "([a-z0-9-]+\\.)*";
         else
            regEx += "\\.";
      }
      else if (wildcard[i] == ' ')
         regEx += "$|";
      else if (wildcard[i] != '*')
         regEx += tolower(wildcard[i]);
   }
   regEx += '$';
   return 0;
}

// Check if domain matches wildcard string:
bool BrandingHelper::isDomainWildcardMatch(const cpc::string& domain, const cpc::string& wildcard)
{
   std::string regEx;
   wildcardDomainToRegEx(wildcard.c_str(), regEx);

   std::string domainLC = domain.c_str();
   boost::algorithm::to_lower(domainLC);

   //cout << "domain: " << domainLC << " wildcard: " << wildcard.c_str() << " regEx: " << regEx << endl;

   return std::regex_match(domainLC, std::regex(regEx));
}
