#include "util/PlatformUtils.h"
#ifdef __APPLE__

#include <string.h>
#include <sstream>
#include <iostream>
#include <sys/sysctl.h>

#include "TargetConditionals.h"
#ifdef __OBJC__
#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
#import <UIKit/UIKit.h>
#import <sys/utsname.h>
#else
#import <Foundation/Foundation.h>
#endif
#endif


using namespace CPCAPI2::PlatformUtils;


bool PlatformUtils::getOSInfo( OSInfo& outOSInfo )
{
   bool result( false );
   memset( &outOSInfo, 0, sizeof( OSInfo ));

#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
   outOSInfo.osType = OSType_iOS;
#else
   outOSInfo.osType = OSType_OSX;
#endif

   NSOperatingSystemVersion osVer;
   osVer = [[NSProcessInfo processInfo] operatingSystemVersion];

   // Format the result into a string.
   std::ostringstream strs;
   strs << ( int ) osVer.majorVersion;
   strs << ".";
   strs << ( int ) osVer.minorVersion;
   strs << ".";
   strs << ( int ) osVer.patchVersion;
   outOSInfo.osVersion = strs.str();

   result = true;
   return result;
}

bool PlatformUtils::getDeviceInfo( DeviceInfo &outDeviceInfo )
{
   bool result( false );

#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
   struct utsname systemInfo;
   uname( &systemInfo );

   outDeviceInfo.deviceModel = systemInfo.machine;
   outDeviceInfo.deviceFormFactor = [[UIDevice currentDevice] userInterfaceIdiom] == UIUserInterfaceIdiomPad ? DeviceFormFactor_Tablet: DeviceFormFactor_Phone;

#else
   outDeviceInfo.deviceModel = "";
   outDeviceInfo.deviceFormFactor = DeviceFormFactor_Computer;
   result = true;
#endif

   return result;
}

// Returns 1 if running in Rosetta
static int processIsTranslated()
{
   int ret = 0;
   size_t size = sizeof(ret);
   // Call the sysctl and if successful return the result
   if (sysctlbyname("sysctl.proc_translated", &ret, &size, NULL, 0) != -1)
      return ret;
   // If "sysctl.proc_translated" is not present then must be native
   if (errno == ENOENT)
      return 0;
   return -1;
}

static std::string cpuInfoStr()
{
   char buffer[1024];
   size_t size=sizeof(buffer);
   if (sysctlbyname("machdep.cpu.brand_string", &buffer, &size, NULL, 0) < 0) {
      perror("sysctl");
      return std::string();
   }
   return buffer;
}

bool PlatformUtils::getCpuArchInfo( CpuArchInfo& outCpuArchInfo )
{
   outCpuArchInfo.runTimeCpuInfo = cpuInfoStr();
   if (processIsTranslated())
      outCpuArchInfo.runTimeCpuInfo += " running under Rosetta";

   outCpuArchInfo.buildTimeCpuArch = "";

#if defined(__aarch64__)
   outCpuArchInfo.buildTimeCpuArch = "ARM64";
   return true;
#elif defined(__x86_64__)
   outCpuArchInfo.buildTimeCpuArch = "x86_64";
   return true;
#else
#error Unsupported architecture?
#endif

   return false;
}

#endif // __APPLE__

#if defined(__APPLE__) && (!defined(TARGET_OS_IPHONE) || TARGET_OS_IPHONE == 0)
void PlatformUtils::disableMacOsAppNap()
{
   static id sDisableAppNapActivity = nil;

   // begin a never ending activity such that this process does not get placed into App Nap.

   // NSActivityUserInitiatedAllowingIdleSystemSleep rationale: keep the process from going into App Nap, but don't prevent the
   // entire system from sleeping (e.g. user closes lid of A macbook).
   if (sDisableAppNapActivity == nil)
   {
      sDisableAppNapActivity = [[NSProcessInfo processInfo] beginActivityWithOptions:NSActivityUserInitiatedAllowingIdleSystemSleep
                                                                                        reason:@"Disabled by default for network activity"];
   }
}
#endif
