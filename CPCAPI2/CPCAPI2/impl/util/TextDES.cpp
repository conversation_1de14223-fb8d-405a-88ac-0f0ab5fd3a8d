#include <new>
#include <string.h>
#include "TextDES.h"

namespace CPCAPI2
{

TextDES::TextDES(TextDES_MODE mode, std::string key) : _key(key), _mode(mode), _iv("76102429"), _state(ST_INIT), _outlen(0), _outbuf(NULL)
{
}

TextDES::~TextDES()
{
   delete[] _outbuf;
}

void TextDES::writeRawInput(unsigned char* input, int inputsz)
{
   doProcessData(input, inputsz);
}

void TextDES::closeRawInput()
{
   doFinalizeData();
}

bool TextDES::readProcessedOutput(unsigned char *dataout, int sz)
{
   if (_state != ST_FINAL)
      return false;

   memcpy((void *)dataout, (void *)_outbuf, sz);

   return true;
}

int TextDES::getOutputSize()
{
   if (_state != ST_FINAL)
      return 0;

   return _outlen;
}

void TextDES::doInitialize()
{
   if (_state != ST_INIT)
      return;

   this->_context = EVP_CIPHER_CTX_new();

   if (!EVP_CipherInit_ex(this->_context, EVP_des_ecb(), NULL, (const unsigned char *)_key.c_str(),
         (const unsigned char *)_iv.c_str(), (_mode == Mode_Encrypt ? 1 : 0)))
   {
      _state = ST_ERR;
      doCleanup();
   }
}

void TextDES::doProcessData(unsigned char* data, int datasz)
{
   if (_state == ST_INIT)
      doInitialize();

   if (_state == ST_FINAL || _state == ST_ERR)
      return;

   _state = ST_IN_PROGRESS;

   if (_outlen > 0)
   {
      unsigned char *tmpbuf = _outbuf;
      _outbuf = new unsigned char[datasz + _outlen + EVP_MAX_BLOCK_LENGTH];
      memcpy((void *)_outbuf, (void *)tmpbuf, _outlen);
      delete[] tmpbuf;
   }
   else {
      _outbuf = new unsigned char[datasz + EVP_MAX_BLOCK_LENGTH];
   }

   int len = 0;
   if (!EVP_CipherUpdate(this->_context, _outbuf + _outlen, &len, data, datasz))
   {
      _state = ST_ERR;
      doCleanup();
   }

   _outlen += len;
}

void TextDES::doFinalizeData()
{
   if (_state == ST_FINAL || _state == ST_ERR)
      return;

   _state = ST_FINAL;

   int len = 0;
   if (!EVP_CipherFinal_ex(this->_context, _outbuf + _outlen, &len))
   {
      _state = ST_ERR;
   }

   _outlen += len;
   doCleanup();
}

void TextDES::doCleanup()
{
   EVP_CIPHER_CTX_free(this->_context);
}

}
