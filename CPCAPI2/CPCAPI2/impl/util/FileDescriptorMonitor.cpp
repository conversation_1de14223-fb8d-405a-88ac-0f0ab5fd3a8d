#include "FileDescriptorMonitor.h"
#include "cpc_logger.h"

#ifdef ANDROID

#include <ctime>
#include <sstream>
#include <string.h>
#include <stdio.h>
#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <errno.h>
#include <sys/resource.h>

#else

#include <fcntl.h>
#include <sys/stat.h>

#ifndef VOLUME_NAME_DOS
#include <unistd.h>
#endif

#endif

using namespace CPCAPI2::Utils;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

//#define MyInfoLog(a)  InfoLog(a)
//#define MyErrLog(a)   ErrLog(a)
// the above didn't flush the output when I was debugging so I switch to the below
#define MyInfoLog(a)    {std::cout a << std::endl; std::cout.flush();}
#define MyErrLog(a)     {std::cout a << std::endl; std::cout.flush();}

FileDescriptorMonitor::FileDescriptorMonitor(int fdThold, bool log) :
   mFdThold(fdThold),
   mLog(log)
{
   std::vector<std::string> list;
   mStartupFdCount = getOpenFileDescriptors(s, f, d, o, list);
   if (mLog)
   {
      MyInfoLog(<< "Initialy open file descriptors: " << mStartupFdCount << " (sockets:" << s << " devices:" << d << " files:" << f << " other:" << o << ")");
   }
}

bool FileDescriptorMonitor::isOverThold()
{
   std::vector<std::string> list;
   int t = getOpenFileDescriptors(s, f, d, o, list);
   bool over = t > (mStartupFdCount + mFdThold);
   if (mLog && over)
   {
      MyErrLog(<< "Open file descriptors: " << t << " (sockets:" << s << " devices:" << d << " files:" << f << " other:" << o << ")");
   }
   return over;
}

int FileDescriptorMonitor::fdsOverThold()
{
   int s, f, d, o;
   std::vector<std::string> list;
   int t = getOpenFileDescriptors(s, f, d, o, list);
   if (t > (mStartupFdCount + mFdThold))
   {
      return t - (mStartupFdCount + mFdThold);
   }
   return 0;
}

std::string FileDescriptorMonitor::getTypeFromName(char *name, int& sockets, int& files, int& devices, int& other, bool isLink)
{
   std::string result;

   if (NULL != strstr(name, "socket:"))
   {
      result = std::string("Socket: ") + name + (isLink ? " (LINK)" : "");
      sockets++;
   }
   else if (NULL != strstr(name, "pipe:") || NULL != strstr(name, "anon_inode:"))
   {
      result = std::string("Pipe (other): ") + name + (isLink ? " (LINK)" : "");
      other++;
   }
   else if (NULL != strstr(name, "/dev/"))
   {
      result = std::string("Device: ") + name + (isLink ? " (LINK)" : "");
      devices++;
   }
   else if (NULL != strstr(name, "/"))
   {
      result = std::string("File: ") + name + (isLink ? " (LINK)" : "");
      files++;
   }
   else
   {
      result = std::string("Other: ") + name + (isLink ? " (LINK)" : "");
      other++;
   }

   return result;
}

#if __APPLE__
std::string getTypeFromStatMode(struct stat& stats)
{
   std::string mode("");
   switch (stats.st_mode & S_IFMT)
   {
      case S_IFBLK: mode = "block special"; break;
      case S_IFCHR: mode = "character special"; break;
      case S_IFDIR: mode = "directory"; break;
      case S_IFIFO: mode = "named pipe (fifo)"; break;
      case S_IFLNK: mode = "symbolic link"; break;
      case S_IFREG: mode = "regular"; break;
      case S_IFSOCK: mode = "socket"; break;
      case S_IFWHT: mode = "whiteout"; break;
      default: mode = "unknown"; break;
   }
   return mode;
}
#endif

int FileDescriptorMonitor::getOpenFileDescriptors(int& sockets, int& files, int& devices, int& other, std::vector<std::string> &list)
{
   sockets = 0;
   files = 0;
   devices = 0;
   other = 0;
   list.clear();
#ifdef ANDROID
   char buf[64];
   struct dirent *entry;

   snprintf(buf, 64, "/proc/%i/fd/", getpid());

   //MyInfoLog(<< "FDM: opening " << buf);
   DIR *dir = opendir(buf);
   if (dir == NULL)
   {
      WarningLog(<< "FDM: unable to open: " << buf);
      return 0;
   }
   while ((entry = readdir(dir)) != NULL)
   {
      char pn[64], target[PATH_MAX];
      ssize_t lres;
      if (strcmp(entry->d_name, ".") == 0 || strcmp(entry->d_name, "..") == 0)
         continue;
      switch (entry->d_type)
      {
      case DT_BLK:
      case DT_CHR:
         list.push_back(std::string("Block or character device: ") + entry->d_name);
         devices++;
         break;
      case DT_LNK:
         //MyInfoLog(<< "FDM: d_type is DT_LNK, name: " << dp->d_name);
         snprintf(pn, 64, "%s/%s", buf, entry->d_name);
         lres = readlink(pn, target, PATH_MAX);
         if (lres == -1)
         {
            list.push_back(std::string("Unable to find target for symlink: ") + entry->d_name);
            files++;
         }
         else
         {
            //MyInfoLog(<< "FDM: Found target for symlink: " << res);
            target[lres] = '\0'; // readlink doesn't null terminate the string
            list.push_back(getTypeFromName(target, sockets, files, devices, other, true));
         }
         break;
      case DT_DIR:
      case DT_REG:
            list.push_back(std::string("Directory or file: ") + entry->d_name);
         files++;
         break;
      case DT_SOCK:
            list.push_back(std::string("Socket: ") + entry->d_name);
         sockets++;
         break;
      case DT_UNKNOWN:
            list.push_back(getTypeFromName(entry->d_name, sockets, files, devices, other, false));
         break;
      case DT_FIFO:
      default:
            list.push_back(std::string("Other: ") + entry->d_name);
         other++;
         break;
      }
   }
   closedir(dir);
   //technically, we added a file desriptor when reading the dir
   files--;
#else
#ifndef VOLUME_NAME_DOS
   MyInfoLog(<< "On this platform select() can handle " << FD_SETSIZE << " and there are " << getdtablesize() << " possible file handles.");
#endif

   for (int i = 0; i < FD_SETSIZE; i++)
   {
#if defined(F_GETPATH)
      struct stat stats;
      if (fstat(i, &stats) == 0)
      {
         char id[64];
         snprintf(id, sizeof(id), "%d", (int)i);

         char filePath[PATH_MAX];
         if (fcntl(i, F_GETPATH, filePath) != -1)
         {
            struct std::tm *timeinfo = localtime(&stats.st_birthtimespec.tv_sec);
            list.push_back(std::string("FD ") + id + " created " + asctime(timeinfo) + ": " + filePath);
            files++;
         }
         else
         {
            // list.push_back(std::string("FD ") + id + ": not a file, stat-mode: " + std::to_string(stats.st_mode) + " (" + getTypeFromStatMode(stats) + ")");
            list.push_back(std::string("FD ") + id + ": not a file");
            other++;  // we don't know the type?
         }
      }
#elif defined(VOLUME_NAME_DOS)
/* This isn't working on Windows.
      HANDLE handle = (HANDLE)_get_osfhandle(i);
      if (errno != EBADF)
      {
         DWORD size = GetFinalPathNameByHandleW(handle, NULL, 0, VOLUME_NAME_DOS);
         if (size > 0)
         {
            std::wstring filePath(size, L'0');
            size = GetFinalPathNameByHandleW(handle, &filePath.front(), size, VOLUME_NAME_DOS);

            std::wstring created("unknown");
            WIN32_FILE_ATTRIBUTE_DATA fad;
            SYSTEMTIME st;
            if (GetFileAttributesEx(name, GetFileExInfoStandard, &fad) && FileTimeToSystemTime(&fad.ftCreationTime, &st))
            {
               created =
                  std::to_wstring(st.wMonth) + L"/" +
                  std::to_wstring(st.wDay) + L"/" +
                  std::to_wstring(st.wYear) + " " +
                  std::to_wstring(st.wHours) + L":" +
                  std::to_wstring(st.wMinutes) + L":" +
                  std::to_wstring(st.wSeconds) + L"." +
                  std::to_wstring(st.wMilliseconds);
            }
               std::wstring filePath(size, L'0');
               size = GetFinalPathNameByHandleW(handle, &filePath.front(), size, VOLUME_NAME_DOS);

            list.push_back("FD " + i + " created " + created + ": " + filePath);
               std::wstring created("unknown");
               WIN32_FILE_ATTRIBUTE_DATA fad;
               SYSTEMTIME st;
               if (GetFileAttributesEx(name, GetFileExInfoStandard, &fad) && FileTimeToSystemTime(&fad.ftCreationTime, &st))
               {
                  created =
                     std::to_wstring(st.wMonth) + L"/" +
                     std::to_wstring(st.wDay) + L"/" +
                     std::to_wstring(st.wYear) + " " +
                     std::to_wstring(st.wHours) + L":" +
                     std::to_wstring(st.wMinutes) + L":" +
                     std::to_wstring(st.wSeconds) + L"." +
                     std::to_wstring(st.wMilliseconds);
               }

               InfoLog(<< "FD " << i << " created " << created << ": " << filePath);
            }
            other++;  // we don't know the type?
         }
         else
         {
            list.push_back("FD " + i + ": not a file");
            other++;  // we don't know the type?
         }
      }
*/
#else
      int fd_dup;
      fd_dup = dup(i);
      if (fd_dup != -1)
      {
         close(fd_dup);

         char buf[64];
         snprintf(buf, 64, "FD %i", (int)i);
         list.push_back(buf);
         // it appears getting a filename from an FD is difficult on Linux
         other++;  // we don't know the type?
      }
#endif
   }
#endif

   return sockets + files + devices + other;
}

bool FileDescriptorMonitor::logSnapshotDifferences(std::vector<std::string> firstSnapshot, std::vector<std::string> secondSnapshot, std::string additionalInfo, std::vector<std::string> skipItems)
{
   for (auto j = secondSnapshot.begin(); j != secondSnapshot.end(); )
   {
      bool found = false;
      for (int i = 0; i < skipItems.size(); i++)
      {
         if (j->find(skipItems[i]) != std::string::npos)
         {
            found = true;
            break;
         }
      }
      if (!found)
      {
         for (int i = 0; i < firstSnapshot.size(); i++)
         {
            if (firstSnapshot[i] == *j)
            {
               found = true;
               break;
            }
         }
      }
      if (found)
         j = secondSnapshot.erase(j);
      else
         j++;
   }

   MyErrLog(<< "Still open file descriptors (" << secondSnapshot.size() << ") " << additionalInfo);
   std::string out;
   for (std::string item : secondSnapshot)
   {
      out += "    " + item + "\n";
   }
   MyErrLog(<< out);

   return secondSnapshot.size() > 0;
}

void FileDescriptorMonitor::logOpenFileDescriptors(std::string additionlInfo)
{
   int sockets, files, devices, other;
   std::vector<std::string> list;
   int total = getOpenFileDescriptors(sockets, files, devices, other, list);

   MyInfoLog(<< "Open file descriptors: " << total << " (sockets:" << sockets << " files:" << files << " devices:" << devices << " other:" << other << ") " << additionlInfo);
   std::string out;
   for (std::string item : list)
   {
      out += "    " + item + "\n";
   }
   MyInfoLog(<< out);
}
