#include "CurlPPProgress.h"

#if defined( __APPLE__ ) || defined( __linux__ )
#include <sys/time.h>
#endif

#ifdef _WIN32
#include <Windows.h>
#endif

using namespace CPCAPI2;

// copy of a copy (this thing should really be somewhere common)
// put it on the todo list.
static int64_t millisSinceEpoch()
{
   int64_t result(0);
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = 11644473600000 * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
#endif
   return result;
}


CurlPPProgress::CurlPPProgress( unsigned int timeoutSeconds )
{
   m_TimeoutMillis = timeoutSeconds * 1000;
   m_LastDownload  = 0;
   m_LastUpload    = 0;
   m_LastTimestamp = 0;
   m_IsAborted.reset( new std::atomic< int >( 0 ));
}

CurlPPProgress::CurlPPProgress( const CurlPPProgress& that )
{
   m_TimeoutMillis = that.m_TimeoutMillis;
   m_LastDownload  = that.m_LastDownload;
   m_LastUpload    = that.m_LastUpload;
   m_LastTimestamp = that.m_LastTimestamp;
   m_IsAborted     = that.m_IsAborted;
}

CurlPPProgress::~CurlPPProgress()
{
}

void CurlPPProgress::abort( void )
{
   m_IsAborted->store( 1 );
}

/**
 * note that this operator() can be extended in order to obtain
 * percentage information.
 */
int CurlPPProgress::operator()( double /*dltotal*/, double dlnow, double /*ultotal*/, double ulnow )
{
   if( m_IsAborted->fetch_add( 0 ) == 1 )
      return -1;

   if( m_TimeoutMillis > 0 )
   {
      int64_t curTime( millisSinceEpoch() );
      if( m_LastDownload != dlnow || m_LastUpload != ulnow || m_LastTimestamp == 0 )
      {
         // If anything changed, reset the timestamp and remember the values
         m_LastDownload  = ( int64_t ) dlnow;
         m_LastUpload    = ( int64_t ) ulnow;
         m_LastTimestamp = curTime;
      }
      else
      {
         // No change, so check to see if the time has expired
         if(( curTime - m_LastTimestamp ) >= m_TimeoutMillis )
         {
            return -1;
         }
      }
   }

   return 0;
}
