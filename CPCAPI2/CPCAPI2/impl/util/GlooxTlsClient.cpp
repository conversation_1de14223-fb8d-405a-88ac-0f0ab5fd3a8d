//
// Refine the Gloox TLS client implementation to dip into a platform-specific
// store of trusted certificates for validating TLS connections.
//

#include "cpc_logger.h"

#if defined(WIN32)
#include <WinSock2.h>
#endif

#include "GlooxTlsClient.h"

#include <openssl/err.h>
#include <resip/stack/ssl/SecurityHelper.hxx>

#if defined(WinRT)
#include <resip/stack/ssl/counterpath/XWinRTSecurity.hxx>
#elif _WIN32
#include <resip/stack/ssl/counterpath/XWinSecurity.hxx>
#elif __linux__
#if ANDROID
#include "resip/stack/ssl/counterpath/AndroidSecurity.hxx"
#else
#include "resip/stack/ssl/counterpath/LinuxSecurity.hxx"
#endif
#elif BB10
#include <resip/stack/ssl/counterpath/BlackberrySecurity.hxx>
#elif __APPLE__
#include "TargetConditionals.h"
#include "resip/stack/ssl/IOSSecurity.hxx"
#elif __THREADX
#include <resip/stack/ssl/counterpath/ThreadXSecurity.hxx>
#endif

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::XMPP_OPENSSL

namespace CPCAPI2
{

GlooxTlsClient::GlooxTlsClient(TLSHandler* th, const std::string& server, resip::SecurityTypes::SSLType sslType, const std::string tlsCiphers)
   : OpenSSLClient(th, server)
   , mSslType(sslType)
   , mTlsCiphers(tlsCiphers)
{
}

GlooxTlsClient::~GlooxTlsClient()
{
}

bool GlooxTlsClient::setType()
{
   m_ctx = resip::SecurityHelper::initializeSslContext(mSslType, mTlsCiphers, resip::SecurityTypes::TLSMode_TLS);
   // initializeSslContext asserts on failure

   return true;
}

bool GlooxTlsClient::privateInit()
{
   resip::Data pathToCerts;

#if _WIN32
   unsigned short usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
#if defined(WinRT)
   resip::Security* sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType, resip::BaseSecurity::CipherList(mTlsCiphers.c_str()), mSslType);
#else
   resip::Security* sec = new resip::XWinSecurity(pathToCerts, usCertStorageType, resip::BaseSecurity::CipherList(mTlsCiphers.c_str()), mSslType);
#endif
#elif __APPLE__
   unsigned short usCertStorageType = resip::CERT_FILESYSTEM_STORAGE | resip::CERT_OS_SPECIFIC_STORAGE;
   resip::Security* sec = new resip::IOSSecurity(pathToCerts, usCertStorageType, resip::BaseSecurity::CipherList(mTlsCiphers.c_str()), mSslType);
#elif __linux__
#if ANDROID
   resip::Security* sec = new resip::AndroidSecurity(pathToCerts);
#else
   unsigned short usCertStorageType = resip::CERT_FILESYSTEM_STORAGE;
   pathToCerts = ".";
   resip::Security* sec = new resip::LinuxSecurity(pathToCerts, usCertStorageType, resip::BaseSecurity::CipherList(mTlsCiphers.c_str()), mSslType);
#endif
#elif BB10
   resip::Security* sec = new resip::BlackberrySecurity();
#elif __THREADX
   resip::Security* sec = new resip::ThreadXSecurity();
#else
   resip::Security* sec = NULL;
   abort();
#endif

   // load all the certificates
   sec->preload();
   SSL_CTX *sslCtx = sec->getSslCtx();
   X509_STORE* rootSSLCerts = 0;
   if (sslCtx)
   {
      rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
      // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
      sslCtx->cert_store = 0;
#else
      X509_STORE_up_ref(rootSSLCerts);
#endif
   }
   else
   {
      abort();
   }

   // set the cert store
   if (rootSSLCerts)
   {
      SSL_CTX_set_cert_store(m_ctx, rootSSLCerts);
   }

   //destroy (resip) security object after retrieving the certificates;
   delete sec;

   return gloox::OpenSSLClient::privateInit();
}

} // CPCAPI2
