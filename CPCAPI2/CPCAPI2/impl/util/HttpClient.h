#pragma once

#if !defined(CPCAPI2_HTTP_CLIENT_H)
#define CPCAPI2_HTTP_CLIENT_H

#include "cpcapi2defs.h"
#include "cpcstl/vector.h"

#include <string>
#include <phone/SslCipherOptions.h>

namespace CPCAPI2
{
class Phone;
class PhoneInterface;
class HTTPClientImpl;
class CPCAPI2_SHAREDLIBRARY_API HTTPClient
{
public:
   enum acceptedCertFailures {
      E_CERT_NOT_YET_VALID = 1,
      E_CERT_EXPIRED = 2,
      E_CERT_ID_MISMATCH = 4,
      E_CERT_NOT_TRUSTED = 8,
      E_CERT_OTHER_ERROR = 16,
      E_CERT_WHATEVER_ERROR = E_CERT_NOT_YET_VALID | E_CERT_EXPIRED | E_CERT_ID_MISMATCH | E_CERT_NOT_TRUSTED | E_CERT_OTHER_ERROR
   };

   //-------------------------------------------------------------------------
   // Summary: Indices for all supported HTTP verbs.
   // NOTES:   As defined in Win32 <http.h>.
   enum EHTTPVerb
   {
      EHTTPVerbUnparsed,   // unparsed verb
      EHTTPVerbUnknown,    // unknown verb
      EHTTPVerbInvalid,    // invalid verb
      EHTTPVerbOPTIONS,    // OPTIONS
      EHTTPVerbGET,        // GET
      EHTTPVerbHEAD,       // HEAD
      EHTTPVerbPOST,       // POST
      EHTTPVerbPUT,        // PUT
      EHTTPVerbDELETE,     // DELETE
      EHTTPVerbTRACE,      // TRACE
      EHTTPVerbCONNECT,    // CONNECT
      EHTTPVerbTRACK,      // Used by Microsoft Cluster Server for a non-logged trace.
      EHTTPVerbMOVE,       // MOVE
      EHTTPVerbCOPY,       // COPY
      EHTTPVerbPROPFIND,   // PROPFIND
      EHTTPVerbPROPPATCH,  // PROPPATCH
      EHTTPVerbMKCOL,      // MKCOL
      EHTTPVerbLOCK,       // LOCK
      EHTTPVerbUNLOCK,     // UNLOCK
      EHTTPVerbSEARCH,     // SEARCH
      EHTTPVerbMaximum     // always the last value
   };

   enum EHTTPError
   {
      EHTTPOk = 0,          // No error
      EHTTPLookup = 1,          // Resolution of host failed
      EHTTPAuth = 2,            // User authentication failed on server, or server certificate verification failed
      EHTTPConnect = 3,         // Could not connect to server,
      EHTTPEmbedded = 4,        // Could not load embedded certificate,
      EHTTPUnknownError = 5,
      EHTTPResponseBodySizeLimitExceeded = 6
      // TBD: Perhaps we'll need to add more later, for now the Auth is used for Server SSL Certificate failures
   };

   class StringPair
   {
   public:
      cpc::string first;
      cpc::string second;
   };

   struct RedirectInfo
   {
      unsigned int code;
      unsigned int count;
      cpc::string url;
      RedirectInfo()
      {
         code = 0;
         count = 0;
      }
   };

   struct RequestConfig
   {
      EHTTPVerb verb;                           // See above enum.
      cpc::string mimeType;                     // "Content-Type" header field value (optional, NULL for none).
      cpc::string username;                     // Login to server (optional, NULL for none).
      cpc::string password;                     // Password for szUsername (optional, NULL for none).
      cpc::string clientCertificate;            // path for client certificate(for reciprocal HTPPS authentication).
      cpc::string clientCertificatePasswd;      // type for client certificate (PEM or DER)
      const char* messageBody;                  // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long messageLengthInBytes;       // Length of the message body in bytes (only used if messageBody is not NULL).
      unsigned long maxReadRate;                // Bytes per second to read response, 0 for no limit.
      TLSVersion tlsVersion;                    // TLS protocol version to use.
      CipherSuite cipherSuite;                  // Override default cipher suite to use for connection. See SslCipherOptions.h
      bool useEmbeddedCert1;                    // use embedded cert for server authentication
      bool useEmbeddedCert2;                    // use embedded cert for server authentication
      bool ignoreCertErrors;                    // ignore any (server) certificate verification error
      bool enableCookies;                       // enable use of cookies
      cpc::string cookieFile;                   // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders; // custom headers
      bool verboseLogging;                      // enable verbose logging (i.e. get curl to do verbose logging)
      bool suppressLogging;                     // disable logging for this request
      cpc::string userAgent;                    // User Agent string
      const cpc::string overrideProxyAddress;   // use specified proxy instead of OS detected one
      unsigned long timeoutSeconds;             // Absolute timeout, so even if still transferring data (but slowly) the timeout would kill request with -1 response. 0/zero means no timeout.
      unsigned long dynamicTimeoutSeconds;      // If a transfer is stuck, wait this long from time of transfer being stuck before timing out.
                                                // Total transfer time may take longer than dynamicTimeoutSeconds; e.g. if connection is slow. This differs from timeoutSeconds setting.
                                                // 0/zero means no dynamic timeout
      bool useHttp2;                            // Request session to specifically use HTTP2, can change to a SDK curl enum wrapper if other versions are required. Currently only supported on Windows, macOS, Linux
      size_t maxResponseBodySizeBytes;          // The maximum size in bytes for the response body. If the body (file) is larger than this,
                                                // the request will fail. 0 indicates no limit. Note not all server return a size, 
                                                // so this may not always work. Maximum limit is 2 GB.
      cpc::vector<cpc::string> requiredCertPublicKeys; // Base 64 encoded public keys, for certificate pinning. If specified, certificate verification is performed as normal,
                                                       // with an additional check to ensure that the public key in the server certificate matches at least one of the values specified here.
                                                       // Public keys specified here when ignoreCertErrors is set to 'true' are still checked and may still cause certificate validation to fail.

      RequestConfig()
      {
         verb = EHTTPVerbGET;
         mimeType = "text/plain";
         username = "";
         password = "";
         clientCertificate = "";
         clientCertificatePasswd = "";
         messageBody = NULL;
         messageLengthInBytes = 0;
         maxReadRate = 0;
         tlsVersion = TLS_DEFAULT;
         cipherSuite = "";
         useEmbeddedCert1 = false;
         useEmbeddedCert2 = false;
         ignoreCertErrors = false;
         enableCookies = false;
         cookieFile = "";
         verboseLogging = false;
         suppressLogging = false;
         userAgent = "CounterPath-HTTP";
         timeoutSeconds = 0;
         dynamicTimeoutSeconds = 10;
         useHttp2 = false;
         maxResponseBodySizeBytes = 0;
      }
   };

   struct ResponseResult
   {
      int errorCode;                      // [out] error code of request
      int curlErrorCode;                  // [out] raw error code from cURL
      int status;                         // [out] status of response
      cpc::string contentType;            // [out] document content type
      cpc::string messageBody;            // [out] body of the http response
      RedirectInfo redirectInfo;          // [out] Info regarding if the request was redirected,
   };


public:

   friend std::ostream& operator<<(std::ostream& os, const HTTPClient::StringPair& stringPair);
   friend std::ostream& operator<<(std::ostream& os, const HTTPClient::RedirectInfo& redirectInfo);
   friend std::ostream& operator<<(std::ostream& os, const HTTPClient::RequestConfig& requestConfig);
   friend std::ostream& operator<<(std::ostream& os, const HTTPClient::ResponseResult& responseResult);

   static cpc::string toString(acceptedCertFailures eFailures);
   static cpc::string toString(EHTTPVerb eVerb);
   static cpc::string toString(EHTTPError eError);

   HTTPClient(Phone* p = NULL);
   ~HTTPClient();

   EHTTPError ConvertErrorCode(int code);

   //
   // Interface IHTTP
   //
   void HTTPSendMessage(
      EHTTPVerb eInVerb,                                // See above enum.
      const char* szInURL,                              // Must include { "http" | "https" }.
      const char* szInMIMEType,                         // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                         // Login to server (optional, NULL for none).
      const char* szInPassword,                         // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,                // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,          // type for client certificate (PEM or DER)
      const char* pInMessageBody,                       // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,           // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,                    // Bytes per second to read response, 0 for no limit.
      bool bInUseEmbeddedCert1,                         // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                         // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                         // ignore any (server) certificate verification error
      bool bInEnableCookies,                            // enable use of cookies
      const char* szInCookieFile,                       // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders, // custom headers
      bool verboseLogging,                      // enable verbose logging (i.e. get curl to do verbose logging)
      bool suppressLogging,                     // no logging for this request
      int& resultErrorCode,                     // [out] error code of request
      int& responseStatus,                      // [out] status of response
      cpc::string& contentType,                 // [out] document content type
      cpc::string& result,                      // [out] result of the http request
      RedirectInfo& redirectInfo,               // [out] Info regarding if the request was redirected,
      const char* szInUserAgent = "CounterPath-HTTP",     // User Agent string
      const cpc::string& overrideProxyAddress = "",              // use specified proxy instead of OS detected one
      unsigned long timeoutSeconds = 0,             // Absolute timeout, so even if still transferring data (but slowly) the timeout would kill request with -1 response. 0/zero means no timeout.
      size_t maxResponseBodySizeBytes = 0,       // The maximum size in bytes for the response body. If the body (file) is larger than this,
                                                // the request will fail. 0 indicates no limit. Note not all server return a size, 
                                                // so this may not always work. Maximum limit is 2 GB.
      cpc::vector<cpc::string> requiredCertPublicKeys = cpc::vector<cpc::string>(),
                                                // Base 64 encoded public keys, for certificate pinning. If specified, certificate verification is performed as normal,
                                                // with an additional check to ensure that the public key in the server certificate matches at least one of the values specified here.
                                                // Public keys specified here when ignoreCertErrors is set to 'true' are still checked and may still cause certificate validation to fail.
      unsigned long dynamicTimeoutSeconds = 10  // If a transfer is stuck, wait this long from time of transfer being stuck before timing out.
                                                // Total transfer time may take longer than dynamicTimeoutSeconds; e.g. if connection is slow. This differs from timeoutSeconds setting.
                                                // 0/zero means no dynamic timeout.
   );

   // struct-ified version of above method
   void HTTPSendMessage(
      const char* inUrl,                                // Must include { "http" | "https" }
      const RequestConfig& requestConfig,
      ResponseResult& responseResult
   );

   //
   // Interface IHTTP
   //
   void StartHTTPSession(
      EHTTPVerb eInVerb,                                // See above enum.
      const char* szInURL,                              // Must include { "http" | "https" }.
      const char* szInMIMEType,                         // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                         // Login to server (optional, NULL for none).
      const char* szInPassword,                         // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,                // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,          // type for client certificate (PEM or DER)
      const char* pInMessageBody,                       // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,           // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,                    // Bytes per second to read response, 0 for no limit.
      bool bInUseEmbeddedCert1,                         // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                         // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                         // ignore any (server) certificate verification error
      bool bInEnableCookies,                            // enable use of cookies
      const char* szInCookieFile,                       // cookie storage (full path) - in user setting folder
      cpc::vector<CPCAPI2::HTTPClient::StringPair> customHeaders,
      int& resultErrorCode,                             // [out] error code of request
      int& responseStatus,                              // [out] status of response
      cpc::string& contentType,                         // [out] document content type
      cpc::string& result,                              // [out] result of the http request
      RedirectInfo& redirectInfo,                       // [out] Info regarding if the request was redirected,
      const char* szInUserAgent = "CounterPath-HTTP",   // User Agent string,
      const cpc::string& overrideProxyAddress = "",     // use specified proxy instead of OS detected one
      unsigned long dynamicTimeoutSeconds = 10          // If a transfer is stuck, wait this long from time of transfer being stuck before timing out.
                                                        // Total transfer time may take longer than dynamicTimeoutSeconds; e.g. if connection is slow. This differs from timeoutSeconds setting.
                                                        // 0/zero means no dynamic timeout.
   );


   // struct-ified version of above method
   //
   // ! note: currently does not use verboseLogging param
   void StartHTTPSession(
      const char* inUrl,                                // Must include { "http" | "https" }
      const RequestConfig& requestConfig,
      ResponseResult& responseResult
   );

   //
   // Interface IHTTP
   //
   void DoSessionRequest(
      EHTTPVerb eInVerb,                                // See above enum.
      const char* szInURL,                              // Must include { "http" | "https" }.
      const char* szInMIMEType,                         // "Content-Type" header field value (optional, NULL for none).
      const char* szInUsername,                         // Login to server (optional, NULL for none).
      const char* szInPassword,                         // Password for szUsername (optional, NULL for none).
      const char* szInClientCertificate,                // path for client certificate(for reciprocal HTPPS authentication).
      const char* szInClientCertificatePasswd,          // type for client certificate (PEM or DER)
      const char* pInMessageBody,                       // Pointer containing data for request body; callee will delete with VsFree (optional, NULL for no body).
      unsigned long ulInMessageLengthInBytes,           // Length of the message body in bytes (only used if pInMessageBody is not NULL).
      unsigned long ulInMaxReadRate,                    // Bytes per second to read response, 0 for no limit.
      bool bInUseEmbeddedCert1,                         // use embedded cert for server authentication
      bool bInUseEmbeddedCert2,                         // use embedded cert for server authentication
      bool bInIgnoreCertErrors,                         // ignore any (server) certificate verification error
      bool bInEnableCookies,                            // enable use of cookies
      const char* szInCookieFile,                       // cookie storage (full path) - in user setting folder
      int& resultErrorCode,                             // [out] error code of request
      int& responseStatus,                              // [out] status of response
      cpc::string& contentType,                         // [out] document content type
      cpc::string& result,                              // [out] result of the http request
      RedirectInfo& redirectInfo,                       // [out] Info regarding if the request was redirected,
      const char* szInUserAgent = "CounterPath-HTTP",   // User Agent string,
      const cpc::string& overrideProxyAddress = ""      // use specified proxy instead of OS detected one
   );

   void DoSessionRequest(
      const char* inUrl,                                // Must include { "http" | "https" }
      const RequestConfig& requestConfig,
      ResponseResult& responseResult
   );

   void Abort();

private:
   HTTPClientImpl* mImpl;
   PhoneInterface* mPhone;
};

}

#endif
