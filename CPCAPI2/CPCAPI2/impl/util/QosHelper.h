#pragma once
#if !defined(CPCAPI2_QOS_HELPER)
#define CPCAPI2_QOS_HELPER

#include "account/SipAccountSettings.h"
#include <rutil/Socket.hxx>
#include <qos/QosSocketManager.hxx>

#define QosHelperFun(x) static void QosFun_##x(resip::Socket s, int transportType, const char* file, int line) \
{ webrtc_recon::QosSocketManager::SocketSetDSCP(s, x, transportType == resip::UDP); }

#define getQosHelperFun(x, y) if (x == y) return QosFun_##x

namespace CPCAPI2
{

class SignalingQos
{ 
public:
   static resip::AfterSocketCreationFuncPtr getSocketCreateQosFun(unsigned int dscp);
   static resip::BeforeSocketCloseFuncPtr getSocketCloseQosFun(){ return unsetQos; }

private:
   static void unsetQos(resip::Socket s, int transportType, const char* file, int line);
   QosHelperFun(0);
   QosHelperFun(1);
   QosHelperFun(2);
   QosHelperFun(3);
   QosHelperFun(4);
   QosHelperFun(5);
   QosHelperFun(6);
   QosHelperFun(7);
   QosHelperFun(8);
   QosHelperFun(9);
   QosHelperFun(10);
   QosHelperFun(11);
   QosHelperFun(12);
   QosHelperFun(13);
   QosHelperFun(14);
   QosHelperFun(15);
   QosHelperFun(16);
   QosHelperFun(17);
   QosHelperFun(18);
   QosHelperFun(19);
   QosHelperFun(20);
   QosHelperFun(21);
   QosHelperFun(22);
   QosHelperFun(23);
   QosHelperFun(24);
   QosHelperFun(25);
   QosHelperFun(26);
   QosHelperFun(27);
   QosHelperFun(28);
   QosHelperFun(29);
   QosHelperFun(30);
   QosHelperFun(31);
   QosHelperFun(32);
   QosHelperFun(33);
   QosHelperFun(34);
   QosHelperFun(35);
   QosHelperFun(36);
   QosHelperFun(37);
   QosHelperFun(38);
   QosHelperFun(39);
   QosHelperFun(40);
   QosHelperFun(41);
   QosHelperFun(42);
   QosHelperFun(43);
   QosHelperFun(44);
   QosHelperFun(45);
   QosHelperFun(46);
   QosHelperFun(47);
   QosHelperFun(48);
   QosHelperFun(49);
   QosHelperFun(50);
   QosHelperFun(51);
   QosHelperFun(52);
   QosHelperFun(53);
   QosHelperFun(54);
   QosHelperFun(55);
   QosHelperFun(56);
};

}

#endif   //CPCAPI2_QOS_HELPER