#include "ThrottledInvoker.h"

using namespace CPCAPI2::Utils;

ThrottledInvoker::ThrottledInvoker(resip::MultiReactor* reactor, int minTimeBetweenPliMsec) :
   mReactor(reactor), mMinTimeBetweenInvokeMsec(minTimeBetweenPliMsec)
{
}

ThrottledInvoker::~ThrottledInvoker()
{
}

// returns the number of milliseconds since system_clock's epoch
int ThrottledInvoker::nowMs() const
{
   unsigned long milliseconds_since_epoch =
      std::chrono::system_clock::now().time_since_epoch() / 
      std::chrono::milliseconds(1);

   return milliseconds_since_epoch;
}

// can be invoked from any thread
bool ThrottledInvoker::reqInvoke(std::function<void()> reqHandler)
{
   if (mQueuedInvokePending)
   {
      return false;
   }

   {
      std::lock_guard<std::recursive_mutex> lock(mReqHandlerMutex);
      mReqHandler = reqHandler;
   }

   const int timeSinceLastPliMs = nowMs() - mLastInvokeMs;
   if (mLastInvokeMs == 0 || (timeSinceLastPliMs > mMinTimeBetweenInvokeMsec))
   {
      mQueuedInvokePending = true;
      mReactor->post(resip::resip_bind(&ThrottledInvoker::doInvokeImpl, this));
   }
   else
   {
      mQueuedInvokePending = true;
      mReactor->post(resip::resip_bind(&ThrottledInvoker::queueTimerImpl, this));
   }

   return true;
}

void ThrottledInvoker::doInvokeImpl()
{
   mLastInvokeMs = nowMs();

   {
      std::lock_guard<std::recursive_mutex> lock(mReqHandlerMutex);
      mReqHandler();
   }

   mQueuedInvokePending = false;
}

void ThrottledInvoker::queueTimerImpl()
{
   if (!mTimer)
   {
      mTimer = std::make_unique<resip::DeadlineTimer<resip::MultiReactor> >(*mReactor);
   }
   mTimer->expires_from_now(mMinTimeBetweenInvokeMsec);
   mTimer->async_wait(this, 0, nullptr);
}

void ThrottledInvoker::onTimer(unsigned short timerId, void*)
{
   mTimer->cancel();
   doInvokeImpl();
}