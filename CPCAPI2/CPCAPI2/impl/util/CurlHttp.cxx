#include <cpcapi2defs.h>
#include "EmbeddedCert.h"

#include <ctime>
#include <sstream>
#include <assert.h>
#include <algorithm>
#include <boost/algorithm/string.hpp>
#include <brand_branded.h>
#include "CurlHttp.h"
#include "HttpClient.h"
#include "cpc_logger.h"
#include "SecureDns.h"
#include "FilePathUtils.h"

#include <rutil/Lock.hxx>
#include <rutil/Mutex.hxx>
#include <rutil/Lock.hxx>
#include <rutil/Timer.hxx>

#if __APPLE__
#include "TargetConditionals.h"
#if TARGET_OS_IPHONE
#  include "resip/stack/ssl/IOSSecurity.hxx"
#else
#  include "CertHelper.h"
#  include "resip/stack/ssl/IOSSecurity.hxx"
#  include <stdio.h>
#endif
#endif

#if BB10
#include "resip/stack/ssl/counterpath/BlackberrySecurity.hxx"
#endif

//#ifdef XS_TARGET_OS_MAC
//#elif defined(XS_TARGET_OS_LINUX)
//#  include "security/Linux/XLinuxSecurity.hxx"
//#elif defined(XS_TARGET_OS_SUNOS)
//#  include "resip/stack/ssl/Security.hxx"
//#else
#if defined(WinRT)
#include "resip/stack/ssl/counterpath/XWinRTSecurity.hxx"
#elif _WIN32
#include "resip/stack/ssl/counterpath/XWinSecurity.hxx"
#endif

#if __linux__
  #if ANDROID
  #include "resip/stack/ssl/counterpath/AndroidSecurity.hxx"
  #else
  #include "resip/stack/ssl/counterpath/LinuxSecurity.hxx"
  #endif
#endif


//#endif //ndef XS_TARGET_OS_MAC

// WinCrypt macros related to X509 stomp on the openssl typedefs of the same name
#if _WIN32
#undef X509_NAME
#endif
#include <openssl/ssl.h>
#include <openssl/x509v3.h>
#include <openssl/pkcs12.h>
#include <openssl/err.h>

#if defined(WIN32)
#ifndef strcasecmp
#  define strcasecmp(a,b)    stricmp(a,b)
#endif
#ifndef strncasecmp
#  define strncasecmp(a,b,c) strnicmp(a,b,c)
#endif
#endif

#define SUA_SUBSYSTEM cpsi::Subsystem::Storage
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

#define CPCAPI2_EXPECTED_LIBCURL_VERSION_MAJOR 8
#define CPCAPI2_EXPECTED_LIBCURL_VERSION_MINOR 5
#define CPCAPI2_EXPECTED_LIBCURL_VERSION_PATCH 0

#ifndef CPCAPI2_DISABLE_CURL_VERSION_CHECK
#if (( LIBCURL_VERSION_MAJOR != CPCAPI2_EXPECTED_LIBCURL_VERSION_MAJOR ) || ( LIBCURL_VERSION_MINOR != CPCAPI2_EXPECTED_LIBCURL_VERSION_MINOR ) || ( LIBCURL_VERSION_PATCH != CPCAPI2_EXPECTED_LIBCURL_VERSION_PATCH ))
#error Mismatched Curl headers!
#endif
#endif

using namespace xten;
using namespace resip;
using namespace CPCAPI2;


static resip::Mutex                        sCurlSessionsLock;
static std::list<CurlHttp::Session*>       sCurlSessions;

void CurlHttp::getCertNames(X509 *cert, std::list<PeerName> &peerNames)
{
   //stolen from resip but change to return the fullname instead of just hostname
   if(NULL == cert)
      return;

   if(peerNames.size() > 0)
      peerNames.clear();

   std::string commonName;

   // look at the Common Name to find the peerName of the cert
   X509_NAME* subject = X509_get_subject_name(cert);
   if(NULL == subject)
   {
      ////SUA_TRACES_ERROR("Invalid certificate: subject not found ");
      return;
   }

   int i =-1;
   while( true )
   {
      i = X509_NAME_get_index_by_NID(subject, NID_commonName,i);
      if ( i == -1 )
      {
         break;
      }
      assert( i != -1 );
      X509_NAME_ENTRY* entry = X509_NAME_get_entry(subject,i);
      assert( entry );

      ASN1_STRING*	s = X509_NAME_ENTRY_get_data(entry);
      assert( s );

      //int t = ASN1_STRING_type(s);
      int l = ASN1_STRING_length(s);
      const unsigned char* d = ASN1_STRING_get0_data(s);

      std::string name((const char*)d, l);
      ////SUA_TRACES_DEBUG("got x509 string type=" << t << " len="<< l << " data=" << d );
      assert( name.size() == (unsigned)l );

      ////SUA_TRACES_DEBUG("Found common name in cert of " << name );

      commonName = name;
   }

   // Look at the SubjectAltName, and if found, set as peerName
   GENERAL_NAMES* gens;
   gens = (GENERAL_NAMES*)X509_get_ext_d2i(cert, NID_subject_alt_name, NULL, NULL);
   for(int i = 0; i < sk_GENERAL_NAME_num(gens); i++)
   {
      GENERAL_NAME* gen = sk_GENERAL_NAME_value(gens, i);

      ////SUA_TRACES_DEBUG("subjectAltName of cert contains type <" << gen->type << ">" );

      if (gen->type == GEN_DNS)
      {
         ASN1_IA5STRING* asn = gen->d.dNSName;
         std::string dns((const char*)asn->data, asn->length);
         PeerName peerName(SubjectAltName, dns);
         peerNames.push_back(peerName);
         ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains DNS <" << dns << ">" );
      }

      if (gen->type == GEN_EMAIL)
      {
         ////SUA_TRACES_DEBUG("subjectAltName of cert has EMAIL type" );
      }

      if(gen->type == GEN_URI)
      {
         ASN1_IA5STRING* asn = gen->d.uniformResourceIdentifier;
         std::string uri((const char*)asn->data, asn->length);
         try
         {
             PeerName peerName(SubjectAltName, uri);
             peerNames.push_back(peerName);
             ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains URI <" << uri << ">" );
         }
         catch (...)
         {
             ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains unparseable URI");
         }
      }
   }
   sk_GENERAL_NAME_pop_free(gens, GENERAL_NAME_free);

   // If there are no peer names from the subjectAltName, then use the commonName
   if(peerNames.empty())
   {
      PeerName peerName(CommonName, commonName);
      peerNames.push_back(peerName);
   }
}

std::string CurlHttp::getCertName(X509 *cert)
{
   std::string certName;
   std::list<PeerName> cNames;

   //get all the names (subjectAltName or CommonName)
   getCertNames(cert, cNames);

   //prefere the subjectAltName
   for(std::list<PeerName>::const_iterator it = cNames.begin(); it != cNames.end(); it++)
   {
      if(it->mType == SubjectAltName)
      {
         return it->mName;
      }
   }

   //if not subjectAltName found, get the CommonName
   for(std::list<PeerName>::const_iterator it = cNames.begin(); it != cNames.end(); it++)
   {
      if(it->mType == CommonName)
      {
         return it->mName;
      }
   }
   ////SUA_TRACES_ERROR("This certificate doesn't have neither subjectAltName nor commonName");
   return "";
}


static int curlTrace(CURL *handle, curl_infotype type, char *data, size_t size, void *userp)
{
  const char *text = NULL;

  switch (type) {
   case CURLINFO_TEXT:
      text = "=> CURL info: ";
      break;
   case CURLINFO_HEADER_OUT:
      text = "=> Send header: ";
      break;
   case CURLINFO_HEADER_IN:
      text = "<= Recv header: ";
      break;
   // too verbose / not useful / logs garbage strings
   //case CURLINFO_DATA_IN:
   //   text = "<= Recv data: ";
   //   break;
   //case CURLINFO_DATA_OUT:
   //   text = "=> Send data: ";
   //   break;
   //case CURLINFO_SSL_DATA_IN:
   //   text = "<= Recv SSL data: ";
   //   break;
   //case CURLINFO_SSL_DATA_OUT:
   //   text = "=> Send SSL data: ";
   //   break;

   default:
      break;
  }

  if (text)
  {
     if (size > 5000)
        DebugLog(<< "CURL message: " << text << std::string(data, 5000).c_str() << "[Trimmed at 5000 characters]");
     else
        DebugLog(<< "CURL message: " << text << std::string(data, size));
  }
  //dump(text, stderr, (unsigned char *)data, size, config->trace_ascii);
  return 0;
}

static int curlTraceNoop(CURL *handle, curl_infotype type, char *data, size_t size, void *userp)
{
   return 0;
}

static char errorBuffer[CURL_ERROR_SIZE];
std::map<std::string,int> CurlHttp::mMonthToNum;


static int
certVerifyCallback(X509_STORE_CTX *ctx, void *arg)
{
   CurlHttp::Session *sess = reinterpret_cast<CurlHttp::Session*>(arg);
   int failures = 0;

#if defined(USE_SSL)
#if (CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECK != 1)
   if (SSLeay() != OPENSSL_VERSION_NUMBER)
   {
      // if you are hitting this, you may be hitting OBELISK-2718. To work around this issue, either curl or tsm needs to be rebuilt,
      // or the link order needs to be such that cpcapi2's OpenSSL is used instead of curl or tsm's OpenSSL.
      ErrLog(<< "OpenSSL runtime does not match headers from compilation, " << SSLeay() << " vs " << OPENSSL_VERSION_NUMBER);
      assert(0);
   }
#endif // CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECKS
#endif // USE_SSL

   int res = X509_verify_cert(ctx);
   if(!res)
   {
      switch(X509_STORE_CTX_get_error(ctx))
      {
         case X509_V_ERR_CERT_NOT_YET_VALID:
            failures |= CPCAPI2::HTTPClient::E_CERT_NOT_YET_VALID;
            break;

		   case X509_V_ERR_CERT_HAS_EXPIRED:
            failures |= CPCAPI2::HTTPClient::E_CERT_EXPIRED;
            break;

		   case X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY:
         case X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN:
         case X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT:
            failures |= CPCAPI2::HTTPClient::E_CERT_NOT_TRUSTED;
            break;

         default:
            failures |= CPCAPI2::HTTPClient::E_CERT_OTHER_ERROR;
            break;
      }

      char cBuf1[500];
      char cBuf2[500];
      X509 *pErrCert;
      //int iErr = 0;
      int iDepth = 0;
      pErrCert = X509_STORE_CTX_get_current_cert(ctx);
      /*iErr = */X509_STORE_CTX_get_error(ctx);
      iDepth = X509_STORE_CTX_get_error_depth(ctx);

      if (NULL != pErrCert)
         X509_NAME_oneline(X509_get_subject_name(pErrCert),cBuf1,256);

      snprintf(cBuf2, 500, ", depth=%d %s\n", iDepth, cBuf1);
      ////SUA_TRACES_ERROR("Error when verifying server's chain of certificates: " << X509_verify_cert_error_string(ctx->error) << cBuf2 );

      // !jza! hack for Rogers:
      // when on mac and provisioning against https server with self signed cert,
      // OpenSSL is returning 0 for the result of X509_verify_cert (error condition, which makes sense)
      // but ctx->error is also 0 which indicates no error. on windows ctx->error has value meaning
      // untrusted cert. not sure why there is a difference.
      bool openSSLMacWorkaroundForSelfSigned = false;
#if defined(XS_TARGET_OS_MAC)
      if (sess->getAcceptableFailures() & CPCAPI2::HTTPClient::E_CERT_NOT_TRUSTED)
      {
         openSSLMacWorkaroundForSelfSigned = true;
      }
#endif

      if((failures & sess->getAcceptableFailures()) || openSSLMacWorkaroundForSelfSigned)
      {
         res = 1;
         X509_STORE_CTX_set_error(ctx, 0);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
         ctx->error_depth = 0;
#else
         X509_STORE_CTX_set_error_depth(ctx, 0);
#endif
         ////SUA_TRACES_INFO("Allowed error, proceed further with https");
      }
   }
   return res;
}

//curl callback for cert verification
static CURLcode sslctxfun(CURL * curl, void * sslctx, void * parm) {

   SSL_CTX * ctx = (SSL_CTX *) sslctx;

   CurlHttp::Session *sess = reinterpret_cast<CurlHttp::Session*>(parm);
   if(!sess)
      return CURLE_FAILED_INIT;

   SSL_CTX_set_options(ctx, sess->sslCtxOptions() /*SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3*/);

   X509_STORE* rootSSLCerts = 0;

   bool bUseEmbeddedCert1 = sess->getUseEmbeddedCert1();
   bool bUseEmbeddedCert2 = sess->getUseEmbeddedCert2();
   if(bUseEmbeddedCert1 || bUseEmbeddedCert2)
   {
      // OBELISK-2767: removed references to CPCAPI2_BRAND_LICENSE_EMBEDDED_CERTIFICATE and CPCAPI2_BRAND_ANALYTICS_EMBEDDED_CERTIFICATE since they are not currently being used, and are expiring in the future.
      // If they need to be used again, a strategy for dealing with their expiration needs to be created.
      ErrLog(<< "Attempted to use an embedded certificate which does not exist");
      return CURLE_FAILED_INIT;
   }
   else
   {

      //here to add the cert from folder storage
      resip::Data pathToCerts = sess->getCertificatesFolder().c_str();
      bool bUseCertFolderOnly = sess->getUseCertFolderOnly();

      unsigned short usCertStorageType = 0;

      //logic for alternate certificate/credentials storage (only for Windos and MAC, Linux only uses folder storage)
      // - if 'pathToCerts' empty, only OS-specific storage will be used
      // - if if 'pathToCerts' not empty:
      //     - if 'bFolderStorageOnly' flag is 'true', only the pathToCerts folder will be used;
      //     - if 'bFolderStorageOnly' flag is 'false', both the pathToCerts folder and OS-specific storage will be used;
      if(pathToCerts.size() == 0)
        usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
      else
      {
        if(bUseCertFolderOnly)
           usCertStorageType = resip::CERT_FILESYSTEM_STORAGE;
        else
           usCertStorageType = resip::CERT_FILESYSTEM_STORAGE | resip::CERT_OS_SPECIFIC_STORAGE;
      }

#if defined(WinRT)
	  resip::Security* sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType);
#elif _WIN32
      resip::Security* sec = new resip::XWinSecurity(pathToCerts, usCertStorageType);
#elif __APPLE__
      resip::Security* sec = new resip::IOSSecurity(pathToCerts, usCertStorageType);
#elif __linux__
  #if ANDROID
      resip::Security* sec = new resip::AndroidSecurity(pathToCerts);
  #else
      pathToCerts = ".";
      resip::Security* sec = new resip::LinuxSecurity(pathToCerts);
  #endif
#elif BB10
      resip::Security* sec = new resip::BlackberrySecurity(pathToCerts);
#else
      assert(0);
#endif

      //load all the certificates
      sec->preload();
      SSL_CTX *sslCtx = sec->getSslCtx();
      if (sslCtx)
      {
         rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);

#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
         // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
         sslCtx->cert_store = 0;
#else
         X509_STORE_up_ref(rootSSLCerts);
#endif
      }
      else
      {
         //SUA_TRACE_ERROR("Could not retrieve the cert store")
         return CURLE_FAILED_INIT;
      }
      //destroy (resip) security object after retrieving the certificates;
      delete sec;

      //set the cert store
      if(rootSSLCerts)
      {
         SSL_CTX_set_cert_store(ctx, rootSSLCerts);
      }
      else
      {
         //SUA_TRACE_ERROR("Could not set the cert store")
         return CURLE_FAILED_INIT;
      }

   }//end if(mUseEmbeddedCert)

   unsigned int sslFalgs = 0;
   if((sess->getClientCert()).length())
   {
      sslFalgs = SSL_VERIFY_PEER|SSL_VERIFY_CLIENT_ONCE|SSL_VERIFY_FAIL_IF_NO_PEER_CERT;

      FILE *fp = NULL;
      PKCS12 *p12 = 0;
      EVP_PKEY *pkey = 0;
      X509 *cert = 0;
      STACK_OF(X509) *ca = 0;

      if (!(fp = fopen((sess->getClientCert()).c_str(), "rb")))
      {
	      ////SUA_TRACES_ERROR("Error opening " << (sess->getClientCert()).c_str() << " file.");
	      return CURLE_SSL_CERTPROBLEM;
      }
      p12 = d2i_PKCS12_fp(fp, NULL);
      fclose (fp);
      if (!p12)
      {
	      ////SUA_TRACES_ERROR("Error reading PKCS#12 client cert file");
         return CURLE_SSL_CERTPROBLEM;
      }
      /*int ret = */PKCS12_parse(p12, (sess->getClientCertPasswd()).c_str(), &pkey, &cert, &ca);
      if (!pkey || !cert)
      {
	      ////SUA_TRACES_ERROR("Error parsing PKCS#12 client cert file");
         return CURLE_SSL_CERTPROBLEM;
      }
      PKCS12_free(p12);
      p12 = NULL;

      /*ret = */SSL_CTX_use_certificate(ctx, cert);
      /*ret = */SSL_CTX_use_PrivateKey(ctx, pkey);

      //extract commonName (subjectAltName for later use); the format should be sip:user@domain
      std::string name;
      std::string uri = CurlHttp::getCertName(cert);
      {
         size_t p = std::string::npos;

         // user
         if ((p = uri.find(":")) != std::string::npos)
         {
            size_t pp = std::string::npos;
            if ((pp = uri.find("@")) != std::string::npos && pp > p)
            {
               name = uri.substr(p+1 ,pp-p-1);
            }
            else
            {
               name = uri.substr(0,p);
            }
         }
         else
         {
            size_t pp = std::string::npos;
            if ((pp = uri.find("@")) != std::string::npos)
            {
               name = uri.substr(0 ,pp);
            }
            else
            {
               name = uri;
            }
         }
      }
      sess->setCommonName(name.c_str());
      //TBD - too see if i need to free cert and pkey or OpenSSL is doing it
   }
   else
   {
      sslFalgs = SSL_VERIFY_PEER;
   }

#ifdef ANDROID
   SSL_CTX_set_verify(ctx, sslFalgs, AndroidSecurity::verifyCallback);
#else
   SSL_CTX_set_verify(ctx, sslFalgs, 0L);
   SSL_CTX_set_cert_verify_callback(ctx, certVerifyCallback, parm);
#endif

   ERR_clear_error();

   return CURLE_OK;
}


std::atomic< bool > CurlHttp::sCurlInitialized( false );

CurlHttp::ResultCode CurlHttp::translateCurlError(CURLcode err)
{
   switch(err)
   {
      case CURLE_OK:
         return Ok;

      case CURLE_UNSUPPORTED_PROTOCOL:    /* 1 */
      case CURLE_URL_MALFORMAT:           /* 3 */
      //case CURLE_OBSOLETE4:               /* 4 - NOT USED */
      case CURLE_FTP_WEIRD_SERVER_REPLY:  /* 8 */
      case CURLE_OBSOLETE10:              /* 10 - NOT USED */
      case CURLE_FTP_WEIRD_PASS_REPLY:    /* 11 */
      case CURLE_OBSOLETE12:              /* 12 - NOT USED */
      case CURLE_FTP_WEIRD_PASV_REPLY:    /* 13 */
      case CURLE_FTP_WEIRD_227_FORMAT:    /* 14 */
      case CURLE_FTP_CANT_GET_HOST:       /* 15 */
      case CURLE_OBSOLETE16:              /* 16 - NOT USED */
      case CURLE_FTP_COULDNT_SET_TYPE:    /* 17 */
      case CURLE_PARTIAL_FILE:            /* 18 */
      case CURLE_FTP_COULDNT_RETR_FILE:   /* 19 */
      case CURLE_OBSOLETE20:              /* 20 - NOT USED */
      case CURLE_QUOTE_ERROR:             /* 21 - quote command failure */
      case CURLE_HTTP_RETURNED_ERROR:     /* 22 */
      case CURLE_WRITE_ERROR:             /* 23 */
      case CURLE_OBSOLETE24:              /* 24 - NOT USED */
      case CURLE_UPLOAD_FAILED:           /* 25 - failed upload "command" */
      case CURLE_READ_ERROR:              /* 26 - couldn't open/read from file */
      case CURLE_OUT_OF_MEMORY:           /* 27 */
  /* Note: CURLE_OUT_OF_MEMORY may sometimes indicate a conversion error
           instead of a memory allocation error if CURL_DOES_CONVERSIONS
           is defined
  */
      case CURLE_OBSOLETE29:              /* 29 - NOT USED */
      case CURLE_FTP_PORT_FAILED:         /* 30 - FTP PORT operation failed */
      case CURLE_FTP_COULDNT_USE_REST:    /* 31 - the REST command failed */
      case CURLE_OBSOLETE32:              /* 32 - NOT USED */
      case CURLE_RANGE_ERROR:             /* 33 - RANGE "command" didn't work */
      case CURLE_HTTP_POST_ERROR:         /* 34 */
      case CURLE_BAD_DOWNLOAD_RESUME:     /* 36 - couldn't resume download */
      case CURLE_FILE_COULDNT_READ_FILE:  /* 37 */
      case CURLE_LDAP_CANNOT_BIND:        /* 38 */
      case CURLE_LDAP_SEARCH_FAILED:      /* 39 */
      case CURLE_OBSOLETE40:              /* 40 - NOT USED */
      case CURLE_FUNCTION_NOT_FOUND:      /* 41 */
      case CURLE_ABORTED_BY_CALLBACK:     /* 42 */
      case CURLE_BAD_FUNCTION_ARGUMENT:   /* 43 */
      case CURLE_OBSOLETE44:              /* 44 - NOT USED */
      case CURLE_INTERFACE_FAILED:        /* 45 - CURLOPT_INTERFACE failed */
      case CURLE_OBSOLETE46:              /* 46 - NOT USED */
      case CURLE_UNKNOWN_TELNET_OPTION:   /* 48 - User specified an unknown option */
      case CURLE_TELNET_OPTION_SYNTAX :   /* 49 - Malformed telnet option */
      case CURLE_OBSOLETE50:              /* 50 - NOT USED */
      case CURLE_GOT_NOTHING:             /* 52 - when this is a specific error */
      case CURLE_SSL_ENGINE_NOTFOUND:     /* 53 - SSL crypto engine not found */
      case CURLE_SSL_ENGINE_SETFAILED:    /* 54 - can not set SSL crypto engine as
                                    default */
      case CURLE_SEND_ERROR:              /* 55 - failed sending network data */
      case CURLE_RECV_ERROR:              /* 56 - failure in receiving network data */
      case CURLE_OBSOLETE57:              /* 57 - NOT IN USE */
      case CURLE_BAD_CONTENT_ENCODING:    /* 61 - Unrecognized transfer encoding */
      case CURLE_LDAP_INVALID_URL:        /* 62 - Invalid LDAP URL */
      case CURLE_USE_SSL_FAILED:          /* 64 - Requested FTP SSL level failed */
      case CURLE_SEND_FAIL_REWIND:        /* 65 - Sending the data requires a rewind that failed */
      case CURLE_SSL_ENGINE_INITFAILED:   /* 66 - failed to initialise ENGINE */

      case CURLE_TFTP_NOTFOUND:           /* 68 - file not found on server */
      case CURLE_TFTP_PERM:               /* 69 - permission problem on server */
      case CURLE_REMOTE_DISK_FULL:        /* 70 - out of disk space on server */
      case CURLE_TFTP_ILLEGAL:            /* 71 - Illegal TFTP operation */
      case CURLE_TFTP_UNKNOWNID:          /* 72 - Unknown transfer ID */
      case CURLE_REMOTE_FILE_EXISTS:      /* 73 - File already exists */
      case CURLE_TFTP_NOSUCHUSER:         /* 74 - No such user */
      case CURLE_CONV_FAILED:             /* 75 - conversion failed */
      case CURLE_CONV_REQD:               /* 76 - caller must register conversion
                                    callbacks using curl_easy_setopt options
                                    CURLOPT_CONV_FROM_NETWORK_FUNCTION:
                                    CURLOPT_CONV_TO_NETWORK_FUNCTION, and
                                    CURLOPT_CONV_FROM_UTF8_FUNCTION */

      case CURLE_REMOTE_FILE_NOT_FOUND:   /* 78 - remote file not found */
      case CURLE_SSH:                     /* 79 - error from the SSH layer, somewhat
                                    generic so the error message will be of
                                    interest when this has happened */

      case CURLE_SSL_SHUTDOWN_FAILED:     /* 80 - Failed to shut down the SSL
                                    connection */
      case CURLE_AGAIN:                   /* 81 - socket is not ready for send/recv,
                                    wait till it's ready and try again (Added
                                    in 7.18.2) */

         return Error;

      case CURLE_FILESIZE_EXCEEDED:       /* 63 - Maximum file size exceeded */
         return ResponseBodySizeLimitExceeded;

      case CURLE_OPERATION_TIMEDOUT:      /* 28 - the timeout time was reached */
         return Timeout;

      case CURLE_FAILED_INIT:             /* 2 */
         return Failed;

      case CURLE_COULDNT_RESOLVE_PROXY:   /* 5 */
      case CURLE_COULDNT_RESOLVE_HOST:    /* 6 */
         return Lookup;

      case CURLE_COULDNT_CONNECT:         /* 7 */
      case CURLE_REMOTE_ACCESS_DENIED:    /* 9 a service was denied by the server
                                    due to lack of access - when login fails
                                    this is not returned. */
      case CURLE_SSL_CONNECT_ERROR:       /* 35 - wrong when connecting with SSL */
         return Connect;

      case CURLE_TOO_MANY_REDIRECTS :     /* 47 - catch endless re-direct loops */
         return Redirect;

      case CURLE_LOGIN_DENIED:            /* 67 - user, password or similar was not accepted and we failed to login */
      case CURLE_SSL_CACERT_BADFILE:      /* 77 - could not load CACERT file, missing or wrong format */
      case CURLE_SSL_CRL_BADFILE:         /* 82 - could not load CRL file, missing or wrong format (Added in 7.19.0) */
      case CURLE_SSL_ISSUER_ERROR:        /* 83 - Issuer check failed.  (Added in 7.19.0) */
      case CURLE_SSL_CERTPROBLEM:         /* 58 - problem with the local certificate */
      case CURLE_SSL_CIPHER:              /* 59 - couldn't use specified cipher */
      case CURLE_SSL_CACERT:              /* 60 - problem with the CA cert (path?) */
#if CURLE_SSL_CACERT != CURLE_PEER_FAILED_VERIFICATION
      case CURLE_PEER_FAILED_VERIFICATION: /* 51 - peer's certificate or fingerprint wasn't verified fine */
#endif
         return Auth;

      default:
         return Error;
    }
}

// curl initialize is moved to PhoneInterface::initialize
bool CurlHttp::initializeCurl()
{
   if (!sCurlInitialized)
   {
      //curl_global_init(CURL_GLOBAL_DEFAULT);
      sCurlInitialized = true;

      mMonthToNum["jan"] = 1;
      mMonthToNum["january"] = 1;
      mMonthToNum["feb"] = 2;
      mMonthToNum["february"] = 2;
      mMonthToNum["march"] = 3;
      mMonthToNum["mar"] = 3;
      mMonthToNum["april"] = 4;
      mMonthToNum["apr"] = 4;
      mMonthToNum["may"] = 5;
      mMonthToNum["jun"] = 6;
      mMonthToNum["june"] = 6;
      mMonthToNum["jul"] = 7;
      mMonthToNum["july"] = 7;
      mMonthToNum["aug"] = 8;
      mMonthToNum["august"] = 8;
      mMonthToNum["sep"] = 9;
      mMonthToNum["september"] = 9;
      mMonthToNum["oct"] = 10;
      mMonthToNum["october"] = 10;
      mMonthToNum["nov"] = 11;
      mMonthToNum["november"] = 11;
      mMonthToNum["dec"] = 12;
      mMonthToNum["december"] = 12;

   }
   return true;
}

void CurlHttp::uninitializeCurl()
{
   if (sCurlInitialized)
   {
      //curl_global_cleanup();
      sCurlInitialized = false;
   }
}

void CurlHttp::abortAll()
{
   resip::Lock lock(sCurlSessionsLock);
   for ( std::list<CurlHttp::Session*>::iterator i = sCurlSessions.begin();
         i != sCurlSessions.end();
         ++i)
   {
      (*i)->abort();
   }
}

CurlHttp::CurlHttp(const std::string& hostUri, const std::string& user, const std::string& pass, const std::string& clientCert,
                   const std::string& clientCertType, const std::string& certificateFolder, bool bUseCertFolderOnly,
                   bool bUseEmbeddedCert1, bool bUseEmbeddedCert2, const std::string& authuser,
                   const std::string& authpass, bool bUseCookies, const std::string cookieFile,
                   const CurlOptions& options, const std::vector<std::string> requiredCertPublicKeys) :
   mUser(user),
   mPassword(pass),
   mClientCertificate(clientCert),
   mClientCertificatePasswd(clientCertType),
   mUseCertificatefolderOnly(bUseCertFolderOnly),
   mUseEmbeddedCert1(bUseEmbeddedCert1),
   mUseEmbeddedCert2(bUseEmbeddedCert2),
   mAuthUser(authuser),
   mAuthPassword(authpass),
   mUseCookies(bUseCookies),
   mCookieFile(cookieFile),
   mOptions(options),
   mRequiredCertPublicKeys(requiredCertPublicKeys)
{
   initializeCurl(); // make sure static info is set

   //TBD: see how we poarse the URI
   /*TCurlUri uri;
   if (ne_uri_parse(hostUri.c_str(), &uri) || uri.host == 0)
   {
      throw new InputException("Could not parse URL: ", hostUri.c_str(), __FILE__, __LINE__);
   }
   if (uri.scheme == 0)
   {
      uri.scheme = ne_strdup("http");
   }
   if (uri.port == 0)
   {
      uri.port = ne_uri_defaultport(uri.scheme);
   }*/

   //mScheme = uri.scheme;
   //mHost = uri.host;
   //mPort = uri.port;
   mURI = hostUri;

   // RLJ 09132011 Removed cast to (LPCTSTR) for certificateFolder below as it resulted in garbage
   mCertificatesFolder = explodePathMacros(certificateFolder);
}

//result nested class methods
CurlHttp::Result::Result() :
   mStatusCode(-1),
   mResultCode(CurlHttp::Error),
   mCurlError(0),
   mBodyLength(0),
   mBodyOctets(0),
   mModTime(0),
   mEtag(0),
   mContentType(0),
   mRedirectCode(0),
   mRedirectCount(0),
   mOwnBody(true)
{}

CurlHttp::Result::Result(const std::list<std::string>& headers, const std::string& body) :
   mStatusCode(-1),
   mResultCode(CurlHttp::Error),
   mCurlError(0),
   mBodyLength(0),
   mBodyOctets(0),
   mModTime(0),
   mEtag(0),
   mRedirectCode(0),
   mRedirectCount(0),
   mContentType(0),
   mOwnBody(true)
{
   if (headers.size() < 1)
   {
      return;
   }

   mResultCode = CurlHttp::Ok;

   if (body.size() > 0)
   {
         mBodyOctets = new char[body.size()+1];
         memcpy(mBodyOctets,body.data(),body.size());
         mBodyOctets[body.size()] = 0; // null terminate, just in case;
         mBodyLength = body.size();
   }

   // parse the headers ... the first one will be the status code

   mHeaders.clear();

   std::list<std::string>::const_iterator iter = headers.begin();
   while (iter != headers.end())
   {
      std::string header = *iter;
      boost::trim_if(header,boost::is_any_of(" \n\r"));
      size_t hLen = header.length();
      if (boost::istarts_with(header,"HTTP/1.") && hLen > 7) // extract the return code mStatusCode ...
      {
         header = header.substr(8);
         mStatusCode = atoi(header.c_str());
      }
      else if (boost::istarts_with(header, "HTTP/2") && hLen > 6) // extract the return code mStatusCode ...
      {
         header = header.substr(7);
         mStatusCode = atoi(header.c_str());
      }
      if (boost::istarts_with(header,"Content-Type:") && hLen > 13)
      {
         std::string buf = header.substr(14);
         mContentType = new char[buf.length()+1];
         memcpy(mContentType,buf.c_str(),buf.length());
         mContentType[buf.length()] = 0;
      }
      if (boost::istarts_with(header,"ETag:") && hLen > 5)
      {
         std::string buf = header.substr(6);
         mEtag = new char[buf.length()+1];
         memcpy(mEtag,buf.c_str(),buf.length());
         mEtag[buf.length()] = 0;
      }
      if (boost::istarts_with(header,"Last-Modified:") && hLen > 14)
      {
         std::string buf = header.substr(15);
         mModTime = new time_t;
         *mModTime = CurlHttp::parse_time_string(buf);
      }
      mHeaders.push_back(header);
      iter++;
   }


}

CurlHttp::Result::~Result()
{
   delete [] mContentType;
   delete [] mEtag;
   delete mModTime;
   if (mOwnBody)
   {
      delete[] mBodyOctets;
   }
}




CurlHttp::HttpHeaderList CurlHttp::EmptyHttpHeaderList;

CurlHttp::Session::Session(CurlHttp& curlHttp)
   : mCurlHttp(curlHttp)
   , mDynamicTimeoutSec(10)
   , mCurrentUpload(0.0)
   , mCurrentDownload(0.0)
   , mLastProgressTimeSec(0)
   , mDisableLogging(false)
{
   mAcceptFailures=0;
   mProgressUserData = NULL;
   mProgressCallback = NULL;
   mAborted = false;
   mEmbeddedCertError = false;
   mHandle = curl_easy_init();
   assert(mHandle);
   {
      resip::Lock lock(sCurlSessionsLock);
      sCurlSessions.push_back(this);
   }

   // check the curl version here.
   curl_version_info_data *pInfo = curl_version_info( CURLVERSION_NOW );
   if( pInfo != NULL )
   {
      if( ((( pInfo->version_num >> 16 ) & 0xFF ) != CPCAPI2_EXPECTED_LIBCURL_VERSION_MAJOR ) ||
          ((( pInfo->version_num >> 8  ) & 0xFF ) != CPCAPI2_EXPECTED_LIBCURL_VERSION_MINOR ) ||
          (( pInfo->version_num & 0xFF ) != CPCAPI2_EXPECTED_LIBCURL_VERSION_PATCH ))
      {
         ErrLog(<< "Curl version does not match headers from compilation" );
         assert( 0 );
      }
   }

   //////SUA_TRACES_DEBUG("Curl library version: " << curl_version( ));

   mLastError = curl_easy_setopt(mHandle, CURLOPT_DEBUGDATA, 0);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_VERBOSE, 0L);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_ERRORBUFFER, errorBuffer);

   struct curl_slist *hostaddrs = NULL;
   if (Utils::SecureDns::doCurloptResolve(mCurlHttp.mURI, hostaddrs))
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_RESOLVE, hostaddrs);
   }
   curl_slist_free_all(hostaddrs);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_URL, mCurlHttp.mURI.c_str());
   //////SUA_TRACES_DEBUG("URL set to: " << mCurlHttp.mURI.c_str());

   mLastError = curl_easy_setopt(mHandle, CURLOPT_WRITEFUNCTION, curlWriter);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_WRITEDATA, this);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_HEADERFUNCTION, curlHeaderWriter);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_HEADERDATA, this);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_IOCTLFUNCTION, ioctlCallback);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_IOCTLDATA, this);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_SEEKFUNCTION , seekCallback);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_SEEKDATA , this);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_NOPROGRESS, 0L);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_PROGRESSFUNCTION, curlProgress);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_PROGRESSDATA, this);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_NOSIGNAL, 1);

   mLastError = curl_easy_setopt(mHandle, CURLOPT_ENCODING, ""); // zlib support

   //deal with 3xx answer codes (maximum 3 redirects, maintain request type, use same authentication credentials)
   mLastError = curl_easy_setopt(mHandle, CURLOPT_FOLLOWLOCATION, 1);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_MAXREDIRS, 3);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_POSTREDIR, CURL_REDIR_POST_ALL); //for 301 and 302
   mLastError = curl_easy_setopt(mHandle, CURLOPT_UNRESTRICTED_AUTH, 1);

   mSslCtxOptions = (curlHttp.mOptions.sslOptions != 0) ? curlHttp.mOptions.sslOptions : (SSL_OP_NO_SSLv2 | SSL_OP_NO_SSLv3);

   if (curlHttp.mOptions.maxResponseBodySizeBytes > 0)
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_MAXFILESIZE, curlHttp.mOptions.maxResponseBodySizeBytes);
   }

   if (curlHttp.mOptions.sslCiphers.size() > 0)
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_SSL_CIPHER_LIST, curlHttp.mOptions.sslCiphers.c_str());
      if (mLastError != CURLE_OK)
      {
         std::stringstream ss;
         ss << "Unable to set curl cipher list: " << curl_easy_strerror(mLastError);
         throw std::runtime_error(ss.str().c_str());
      }
   }

   //cookie support
   if(mCurlHttp.mUseCookies)
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_COOKIEFILE, mCurlHttp.mCookieFile.c_str());
      mLastError = curl_easy_setopt(mHandle, CURLOPT_COOKIEJAR, mCurlHttp.mCookieFile.c_str());
   }

   //mLastError = curl_easy_setopt(mHandle, CURLOPT_HEADER, 1L);

#if (__linux__ && !(ANDROID))
   // TODO: should we clear CURLOPT_CAPATH and CURLOPT_CAINFO on linux too?
   std::string caInfo;
   if (FilePathUtils::getCABundlePath(caInfo))
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_CAINFO, caInfo.c_str());
   }
#else
   mLastError = curl_easy_setopt(mHandle, CURLOPT_CAPATH, NULL);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_CAINFO, NULL);
#endif

   mLastError = curl_easy_setopt(mHandle, CURLOPT_SSL_CTX_FUNCTION, sslctxfun);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_SSL_CTX_DATA, this);

#if ((LIBCURL_VERSION_MAJOR > 7) || ((LIBCURL_VERSION_MAJOR == 7) && (LIBCURL_VERSION_MINOR >= 39)))
   // CURLOPT_PINNEDPUBLICKEY available since curl 7.39.0
   if (mCurlHttp.mRequiredCertPublicKeys.size() > 0)
   {
      std::string pinnedKeysStr;
      for (std::vector<std::string>::const_iterator it = mCurlHttp.mRequiredCertPublicKeys.begin(); it != mCurlHttp.mRequiredCertPublicKeys.end(); it++)
      {
         if (!pinnedKeysStr.empty())
            pinnedKeysStr += ";";

         // expected that keys are provided in PEM format
         resip::Data derFormat = resip::Data(*it).base64decode();
         // Curl expects key in base 64 encoded SHA256 format
         unsigned char digest[SHA256_DIGEST_LENGTH]; // on the stack
         SHA256(reinterpret_cast<const unsigned char*>(derFormat.c_str()), derFormat.size(), digest);
         resip::Data encoded = resip::Data(digest, SHA256_DIGEST_LENGTH).base64encode();

         pinnedKeysStr += "sha256//" + std::string(encoded.c_str());
      }

      mLastError = curl_easy_setopt(mHandle, CURLOPT_PINNEDPUBLICKEY, pinnedKeysStr.c_str());
   }
#endif

   mLastError = curl_easy_setopt(mHandle, CURLOPT_SSLVERSION, CURL_SSLVERSION_DEFAULT);
   if (mLastError != CURLE_OK)
   {
      std::stringstream ss;
      ss << "Unable to set curl ssl version: " << curl_easy_strerror(mLastError);
      //Android doesn't recognize CURL_SSLVERSION_DEFAULT at this point for some reason, don't throw:
      WarningLog(<< ss.str());
   }
}

CurlHttp::Session::~Session()
{
   {
      resip::Lock lock(sCurlSessionsLock);
      sCurlSessions.remove(this);
   }

   if (mHandle)
   {
      curl_easy_cleanup(mHandle);
      mHandle = 0;
   }
}


int
CurlHttp::Session::curlHeaderWriter(char *data, size_t size, size_t nmemb, void *userdata)
{
   if (!userdata)
   {
      return -1;
   }

   CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(userdata);

   size_t n = size * nmemb;

   if (data && n > 0)
   {
      std::string header;
      header.append(data,n);

      ////SUA_TRACES_DEBUG("Received header: " << header.c_str());

      if (boost::istarts_with(header,"HTTP/1.")) // mark a new response ...
      {
         sess->mHeaderBuffer.clear();
      }
      if (header == "\r\n") // mark the end of a response header
      {
         // we could call somethign like the onHttpResponse here
      }
      else
      {
         boost::trim_if(header,boost::is_any_of(" \n\r"));
         sess->mHeaderBuffer.push_back(header);

         // Boost regex for recognizing redirects "HTTP/1.[01] 30"
         if (!header.compare(0, 7, "HTTP/1.") && (header.at(7) == '1' || header.at(7) == '0') && !header.compare(8, 4, " 301"))
         {
            sess->mRedirectBuffer.clear();
            sess->mRedirectBuffer.push_back(header);
         }
      }
   }
   else
   {
      n=0;
   }

   return (int)n;
}


int
CurlHttp::Session::curlWriter(char *data, size_t size, size_t nmemb, void *userdata)
{
   // CURLOPT_WRITEFUNCTION from curl.haxx.se:
   // Your callback should return the number of bytes actually taken care of.If that amount differs
   // from the amount passed to your callback function, it'll signal an error condition to the library.
   // This will cause the transfer to get aborted and the libcurl function used will return CURLE_WRITE_ERROR.

   if (!userdata)
   {
      return -1;
   }

   CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(userdata);

   size_t n = size * nmemb;

   if (data && n > 0)
   {
      try
      {
         sess->mBodyBuffer.append(data, n);
      }
      catch (const std::bad_alloc&)
      {
         sess->mBodyBuffer.clear();
         return -1;
      }
      ////SUA_TRACES_DEBUG("Received body so far: " << sess->mBodyBuffer);
   }
   else
   {
      n=0;
   }

   return (int) n;
}


curlioerr
CurlHttp::Session::ioctlCallback(CURL*,int cmd,void *userdata)
{
   if (!userdata)
   {
      return CURLIOE_UNKNOWNCMD;
   }

   CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(userdata);

   switch (cmd)
   {
      case CURLIOCMD_RESTARTREAD:
         sess->mOutBufferPos = 0;
         break;

      default:
         return CURLIOE_UNKNOWNCMD;
   }

   return CURLIOE_OK;
}


int
CurlHttp::Session::seekCallback(void* userdata,curl_off_t off,int orig)
{
   if (!userdata)
   {
      return -1;
   }

   CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(userdata);

   curl_off_t np;
   switch(orig)
   {
      case SEEK_SET:
         np = off;
         break;

      case SEEK_END:
         np = sess->mOutBufferSize + off;
         break;

      case SEEK_CUR:
         np = sess->mOutBufferPos + off;
         break;

      default:
         np = 0;
         break;
   }

   if (np < 0) np = 0;
   if (np > sess->mOutBufferSize) np = sess->mOutBufferSize;

   sess->mOutBufferPos = np;


   return 0;
}


size_t
CurlHttp::Session::curlReader(void *ptr, size_t size, size_t nmemb, void *userdata)
{
   ////SUA_TRACES_DEBUG("CURL: Give me data to send ");
   if (!userdata)
   {
      ////SUA_TRACES_DEBUG("CURL: oops no usedata - not good I will die");
      return -1;
   }

  curl_off_t requested = size * nmemb;

  CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(userdata);



  if (sess->mOutBufferPos >= sess->mOutBufferSize)
  {
     return 0;
  }

  if(requested > sess->mOutBufferSize - sess->mOutBufferPos)
  {
     requested = sess->mOutBufferSize - sess->mOutBufferPos;
  }
  memcpy (ptr, &sess->mOutBuffer[sess->mOutBufferPos], (size_t)requested);
  sess->mOutBufferPos += requested;


  return (size_t)requested;
}




int
CurlHttp::Session::curlProgress (void *p, double dlt, double dln, double ult, double uln)
{
   if (!p)
   {
      return -1;
   }

   CurlHttp::Session* sess = reinterpret_cast<CurlHttp::Session*>(p);

   if (sess->aborted())
   {
      return -1;
   }
   else
   {
      // bliu: if no data was transmitted over the timeout period, abort the operation
      if (sess->mDynamicTimeoutSec > 0)
      {
         UInt64 current = resip::Timer::getTimeSecs();

         if (dln == sess->mCurrentDownload && uln == sess->mCurrentUpload && sess->mLastProgressTimeSec > 0)
         {
            if (current > sess->mLastProgressTimeSec && current - sess->mLastProgressTimeSec >= sess->mDynamicTimeoutSec)
            {
               WarningLog(<< "dynamic HTTP timeout (" << sess->mDynamicTimeoutSec << " sec) reached");
               return -1;
            }
         }
         else
         {
            sess->mCurrentDownload = dln;
            sess->mCurrentUpload = uln;
            sess->mLastProgressTimeSec = current;
         }
      }

      if (sess->mProgressCallback)
      {
         if (dlt > 0 && dln > 0)
         {
            sess->mProgressCallback(sess->mProgressUserData,(int)(dlt / dln));
         }
         else if (ult > 0  && uln > 0)
         {
            sess->mProgressCallback(sess->mProgressUserData,(int)(ult / uln));
         }
      }
      ////SUA_TRACES_DEBUG("progress download:" << dln << "/" << dlt << " upload: " << uln << "/" << ult);
      return 0;
   }
}

void
CurlHttp::Session::setProxyServer(const char *hostname, unsigned int port, const char *bypassList)
{
   ////SUA_TRACES_INFO("Set HTTP(S) proxy: " << hostname << ":" << port)
   if (strlen(hostname) > 0)
      curl_easy_setopt(mHandle, CURLOPT_PROXY, hostname);

   if (port > 0)
      curl_easy_setopt(mHandle, CURLOPT_PROXYPORT, port);

   if (strlen(bypassList) > 0)
      curl_easy_setopt(mHandle, CURLOPT_NOPROXY, bypassList);
}

void
CurlHttp::Session::setTimeout(unsigned long timeoutSec)
{
   curl_easy_setopt(mHandle, CURLOPT_TIMEOUT, timeoutSec);
}

void
CurlHttp::Session::setDynamicTimeout(unsigned long timeoutSec)
{
   // bliu: use dynamic timeout as mentioned in https://curl.haxx.se/libcurl/c/CURLOPT_TIMEOUT.html
   mDynamicTimeoutSec = timeoutSec;
}

void
CurlHttp::Session::setVerboseLogging(bool enabled)
{
   curl_easy_setopt(mHandle, CURLOPT_VERBOSE, enabled);

   if (enabled)
   {
      curl_easy_setopt(mHandle, CURLOPT_DEBUGFUNCTION, curlTrace);
   }
   else
   {
      disableLogging();
   }
}

void
CurlHttp::Session::disableLogging()
{
   curl_easy_setopt(mHandle, CURLOPT_DEBUGFUNCTION, curlTraceNoop);
   mDisableLogging = true;
}

void CurlHttp::Session::setHttpVersion(unsigned int version)
{
   // CURL_HTTP_VERSION_NONE,                /* setting this means we don't care, and that we'd like the library to choose the best possible for us! */
   // CURL_HTTP_VERSION_1_0,                 /* please use HTTP 1.0 in the request */
   // CURL_HTTP_VERSION_1_1,                 /* please use HTTP 1.1 in the request */
   // CURL_HTTP_VERSION_2_0,                 /* please use HTTP 2 in the request */
   // CURL_HTTP_VERSION_2TLS,                /* use version 2 for HTTPS, version 1.1 for HTTP */
   // CURL_HTTP_VERSION_2_PRIOR_KNOWLEDGE,   /* please use HTTP 2 without HTTP/1.1 Upgrade */
   // CURL_HTTP_VERSION_LAST                 /* *ILLEGAL* http version */

   curl_easy_setopt(mHandle, CURLOPT_HTTP_VERSION, version);
   //curl_easy_setopt(mHandle, CURLOPT_PIPEWAIT, 1);
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::doRequest(const std::string& method, const std::string& path, const CurlHttp::HttpHeaderList& additionalHeaders)
{
   return std::move(doRequest(method, path, 0, 0, additionalHeaders));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::doRequest(const std::string& method, const std::string& path, const char* body, size_t bodySize, const CurlHttp::HttpHeaderList& additionalHeaders)
{
   // initialize some global request session parameters (new for each request)
   mHeaderBuffer.clear();
   mBodyBuffer.clear();
   mOutBuffer = NULL;
   mOutBufferSize = 0;
   mOutBufferPos = 0;
   mAuthenticating = 0;

   mCurrentUpload = 0.0;
   mCurrentDownload = 0.0;
   mLastProgressTimeSec = resip::Timer::getTimeSecs();

   std::unique_ptr<Result> res(new Result());
   if (aborted())
   {
      res->mResultCode = CurlHttp::Aborted;
      return std::move(res);
   }

   //add request headers
   struct curl_slist *headerList = NULL;
   for(HttpHeaderList::const_iterator it = additionalHeaders.begin(); it != additionalHeaders.end(); it++)
   {
      std::string header =  it->getName() + ": " + it->getValue();
      headerList = curl_slist_append(headerList, header.c_str());
   }
   headerList = curl_slist_append(headerList, "Expect:");   // remove this
   mLastError = curl_easy_setopt(mHandle, CURLOPT_HTTPHEADER, headerList);

   //add request body
   if(body && bodySize)
   {
      mOutBuffer = new char[bodySize];
      mOutBufferSize = bodySize;
      memcpy(mOutBuffer,body,mOutBufferSize);
      mOutBufferPos = 0;
      mLastError = curl_easy_setopt(mHandle, CURLOPT_READDATA, this);
      mLastError = curl_easy_setopt(mHandle, CURLOPT_READFUNCTION, curlReader);
   }

   // setting up seek and IO control functions needed by some PUT/POST implementations
   // trying to fix the path if present
   if (!path.empty())
   {
      size_t p = mCurlHttp.mURI.find("//");
      p=(p==std::string::npos)?0:p+2;
      p = mCurlHttp.mURI.find("/",p);
      if (p != std::string::npos)
      {
         std::string url = mCurlHttp.mURI.substr(0,p+1) + path;
         boost::replace_all(url,"\\","/");
         mLastError = curl_easy_setopt(mHandle, CURLOPT_URL, url.c_str());
      }
      else
      {
         std::string url = mCurlHttp.mURI;
         if (path[0]!='/' && path[0]!='\\') // the path has to start with a /
         {
            url += std::string("/") + path;
         }
         else
         {
            url += path;
         }
         boost::replace_all(url,"\\","/");
         mLastError = curl_easy_setopt(mHandle, CURLOPT_URL, url.c_str());
      }
   }

   // reset verb params in case we're reusing this object
   mLastError = curl_easy_setopt(mHandle, CURLOPT_POST, 0);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_UPLOAD, 0);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_CUSTOMREQUEST, 0);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_NOBODY, 0);
   mLastError = curl_easy_setopt(mHandle, CURLOPT_HTTPGET, 0);
   // set verb
   if (method == "POST")
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_POST,1);
      mLastError = curl_easy_setopt(mHandle, CURLOPT_POSTFIELDSIZE, bodySize);
      mLastError = curl_easy_setopt(mHandle, CURLOPT_POSTFIELDS, 0);
      ////SUA_TRACES_DEBUG("CURL :perform POST request " );
   }
   else if (method == "PUT")
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_UPLOAD,1);
      mLastError = curl_easy_setopt(mHandle, CURLOPT_INFILESIZE, bodySize);
      ////SUA_TRACES_DEBUG("CURL :perform PUT request ");
   }
   else if (method == "DELETE")
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_CUSTOMREQUEST, "DELETE");
      mLastError = curl_easy_setopt(mHandle, CURLOPT_POSTFIELDSIZE, bodySize);
      mLastError = curl_easy_setopt(mHandle, CURLOPT_POSTFIELDS, 0);
      ////SUA_TRACES_DEBUG("CURL :perform DELETE request ");
   }
   else if (method == "HEAD")
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_NOBODY, 1);
      ////SUA_TRACES_DEBUG("CURL :perform HEAD request ");
   }
   else if (method == "MKCOL")
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_CUSTOMREQUEST, "MKCOL");
      ////SUA_TRACES_DEBUG("CURL :perform MKCOL request ");
   }
   else // default to get
   {
      mLastError = curl_easy_setopt(mHandle, CURLOPT_HTTPGET, 1);
      ////SUA_TRACES_DEBUG("CURL :perform GET request ");
   }

   // user authentication
   mLastError = curl_easy_setopt(mHandle, CURLOPT_HTTPAUTH, CURLAUTH_ANY);

   if (!mCurlHttp.mAuthUser.empty()) //XCAP authentication
   {
      // XCAP authentication using separated username and password
	  mLastError = curl_easy_setopt(mHandle, CURLOPT_USERNAME, std::string(mCurlHttp.mAuthUser).c_str());
	  mLastError = curl_easy_setopt(mHandle, CURLOPT_PASSWORD, std::string(mCurlHttp.mAuthPassword).c_str());
   }
   else
   {
	  mLastError = curl_easy_setopt(mHandle, CURLOPT_USERNAME, std::string(mCurlHttp.mUser).c_str());
	  mLastError = curl_easy_setopt(mHandle, CURLOPT_PASSWORD, std::string(mCurlHttp.mPassword).c_str());
   }

   //check the "allowed failures" flag for certificate verification
   if(mAcceptFailures & CPCAPI2::HTTPClient::E_CERT_ID_MISMATCH)
   {
      curl_easy_setopt(mHandle, CURLOPT_SSL_VERIFYHOST, 0L);
      curl_easy_setopt(mHandle, CURLOPT_SSL_VERIFYPEER, 0L);
   }
   else
   {
      curl_easy_setopt(mHandle, CURLOPT_SSL_VERIFYHOST, 1L);
      curl_easy_setopt(mHandle, CURLOPT_SSL_VERIFYPEER, 1L);
   }

   // perform the operation
   curl_easy_setopt(mHandle, CURLOPT_VERBOSE, 1L);
   curl_easy_setopt(mHandle, CURLOPT_DEBUGDATA, 4);

   mLastError = curl_easy_perform(mHandle);
   if(mLastError != CURLE_OK && !mDisableLogging)
   {
      const char *err = curl_easy_strerror(mLastError);
      WarningLog(<< "CURL error: " << err << ", error buffer: " << errorBuffer);
   }

   // avoid leaving any OpenSSL errors hanging around
   ERR_clear_error();

   // clear the header list
   curl_slist_free_all(headerList);

   if (mOutBuffer)
   {
      delete[] mOutBuffer;
      mOutBufferSize = 0;
      mOutBufferPos = 0;
   }

   if (aborted())
   {
      res->mResultCode = CurlHttp::Aborted;
      return std::move(res);
   }

   if (mLastError == CURLE_OK)
   {
      std::unique_ptr<Result> noErrorRes;
      try
      {
         noErrorRes.reset(new Result(mHeaderBuffer,mBodyBuffer));
      }
      catch (const std::bad_alloc&)
      {
         res->mResultCode = CurlHttp::Aborted;
         return res;
      }
      long redirectCount = 0;
      unsigned int redirectCode = 0;
      std::string redirectUrl;
      getRedirectInfo(redirectCount, redirectCode, redirectUrl);
      noErrorRes->mRedirectCount = redirectCount;
      noErrorRes->mRedirectCode = redirectCode;
      noErrorRes->mRedirectUrl = redirectUrl;
      return std::move(noErrorRes);
   }

   res->mCurlError = mLastError;
   res->mResultCode = CurlHttp::translateCurlError(mLastError);
   if(mEmbeddedCertError)
   {
      res->mResultCode = Embedded;
   }

   return std::move(res);
}

bool
CurlHttp::Session::aborted()
{
   resip::Lock lock(mAbortedLock);
   return mAborted;
}

void
CurlHttp::Session::abort()
{
   resip::Lock lock(mAbortedLock);
   mAborted = true;
}



void
CurlHttp::Session::setProgressCallback(onProgressCallback function, void* userdata)
{
   mProgressCallback = function;
   mProgressUserData = userdata;
}



std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::deleteUri(const std::string& path, const CurlHttp::HttpHeaderList& additionalHeaders)
{
   return std::move(doRequest("DELETE",  path, additionalHeaders));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::head(const std::string& path, const CurlHttp::HttpHeaderList& additionalHeaders)
{
   return std::move(doRequest("HEAD", path, additionalHeaders));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::put(const std::string& path, const char* buffer, size_t size,
                       const CurlHttp::HttpHeaderList& additionalHeaders)
{
   return std::move(doRequest("PUT", path, buffer, size, additionalHeaders));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::putIfNotModified(const std::string& path, const char* buffer, size_t size, time_t since,
                                    const CurlHttp::HttpHeaderList& additionalHeaders)
{
   HttpHeaderList additional = additionalHeaders;

   additional.push_back(HttpHeader("If-Modified-Since", CurlHttp::make_rfc1123_date(since).c_str()));
   return std::move(doRequest("PUT", path, buffer, size, additional));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::putIfMatch(const std::string& path, const char* buffer, size_t size, const std::string& etag,
                              const CurlHttp::HttpHeaderList& additionalHeaders)
{
   HttpHeaderList list = additionalHeaders;
   list.push_back(HttpHeader("If-Match", etag));
   return std::move(doRequest("PUT", path, buffer, size, list));
}



std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::get(const std::string& path, const CurlHttp::HttpHeaderList& additionalHeaders)
{
   return std::move(doRequest("GET", path, additionalHeaders));
}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::getIfModified(const std::string& path, time_t since,
                                 const CurlHttp::HttpHeaderList& additionalHeaders)
{
   HttpHeaderList list = additionalHeaders;

   list.push_back(HttpHeader("If-Modified-Since", CurlHttp::make_rfc1123_date(since).c_str()));
   return std::move(doRequest("GET", path, list));

}

std::unique_ptr<CurlHttp::Result>
CurlHttp::Session::getIfNoneMatch(const std::string& path, const std::string& etag,
                                  const CurlHttp::HttpHeaderList& additionalHeaders)
{
   HttpHeaderList list = additionalHeaders;

   list.push_back(HttpHeader("If-None-Match", etag));
   return std::move(doRequest("GET", path, list));
}

void
CurlHttp::Session::getRedirectInfo(long& redirectCount, unsigned int& redirectCode, std::string& redirectUrl)
{
   char *redir;
   // previously redirectCount was an int, which per curl docs is *not* a valid type
   // to pass in; must be a long (among a few other valid types)
   if (curl_easy_getinfo(mHandle, CURLINFO_REDIRECT_COUNT, &redirectCount) == CURLE_OK)
   {
      curl_easy_getinfo(mHandle, CURLINFO_EFFECTIVE_URL, &redir);
      if (redirectCount > 0)
      {
         redirectUrl = std::string(redir);
         std::list<std::string>::const_iterator it = mRedirectBuffer.begin();
         for (; it != mRedirectBuffer.end(); it++)
         {
            int index = it->find("30");
            if (index != std::string::npos)
            {
               std::string redirCodeStr = it->substr(index, 3);
               if (it->at(index+2) >= '1' && it->at(index+2) <= '9')
               {
                  redirectCode = atoi(redirCodeStr.c_str());
               }
            }
         }
      }
   }
}


/// helper functions of CurlHTTP that can be used by CurlXcap and outside as well.


std::string
CurlHttp::make_rfc1123_date(const time_t& t)
{
   static const char *weekdays[7] = { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" };
   static const char *months[12] = { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" };

   struct std::tm *gmt;
   gmt = gmtime(&t);

   if (gmt == NULL)
   {
      return std::string("");
   }

   char buf[30] = {0};
   snprintf(buf, 30,
            "%3s, %02d %3s %4d %02d:%02d:%02d GMT",
            weekdays[gmt->tm_wday],
            gmt->tm_mday,
            months[gmt->tm_mon],
            1900 + gmt->tm_year,
            gmt->tm_hour,
            gmt->tm_min,
            gmt->tm_sec);

   return std::string(buf);
}

#ifdef WIN32
int calc_gmt_offset(void)
{
   TIME_ZONE_INFORMATION tzinfo;
   switch (GetTimeZoneInformation(&tzinfo))
   {
      case TIME_ZONE_ID_UNKNOWN:
         break;
      case TIME_ZONE_ID_STANDARD:
         return -((tzinfo.Bias + tzinfo.StandardBias) * 60);
      case TIME_ZONE_ID_DAYLIGHT:
         return -((tzinfo.Bias + tzinfo.DaylightBias) * 60);
   }
   return 0;
}
#else
int calc_gmt_offset(void)
{
   return 0;
}
#endif

time_t
CurlHttp::parse_time_string(const std::string& time)
{
   std::string frmt(time);
   struct std::tm gmt = {0};
   size_t p = frmt.find(" ");  // trim the first Day off we don't need it.
   if (p != std::string::npos)
   {
      frmt = frmt.substr(p);
      boost::trim(frmt);
   }
   if (atoi(frmt.c_str())==0) // we have a short or long month here ...
   {
      return 0;
   }
   else // ohter formats have the day here
   {
      // day
      gmt.tm_mday = atoi(frmt.c_str());
      boost::trim_left_if(frmt,boost::is_any_of(" -") || boost::is_digit());

      // month
      p = frmt.find("-");
      if (p==std::string::npos) p=frmt.find(" ");
      if (p==std::string::npos) return 0;
      std::map<std::string,int>::iterator i = mMonthToNum.find(boost::to_lower_copy(frmt.substr(0,p)));
      if (i==mMonthToNum.end()) return 0;
      gmt.tm_mon = i->second-1;

      // year
      frmt = frmt.substr(p);
      boost::trim_left_if(frmt,boost::is_any_of(" -"));
      p=frmt.find(" ");
      if (p==std::string::npos) return 0;
      gmt.tm_year = atoi(frmt.substr(0,p).c_str());
      if (gmt.tm_year>1900) gmt.tm_year-=1900;

      // time
      frmt = frmt.substr(p);
      boost::trim(frmt);
      gmt.tm_hour = atoi(frmt.substr(0,2).c_str());
      gmt.tm_min = atoi(frmt.substr(3,2).c_str());
      gmt.tm_sec = atoi(frmt.substr(6,2).c_str());
   }
   return mktime(&gmt) + calc_gmt_offset();
}

std::string CurlHttp::explodePathMacros(const std::string& path)
{
	std::string resultPath(path);
	std::string programFolder;
#if defined(WinRT)
	#pragma warning("Exploding path macros is not implemented on WinRT")
	programFolder = "";
#elif defined WIN32
   HMODULE hModule = ::GetModuleHandle(NULL);
#ifdef UNICODE
   wchar_t p[_MAX_PATH];
#else
   char p[_MAX_PATH];
#endif
   ::ZeroMemory(p, sizeof(p));
   ::GetModuleFileName(hModule, p, sizeof(p)/sizeof(p[0]));
#ifdef UNICODE
	cpc::string pt(p);
   // strip out executable file name
   int pos = pt.find_last_of(L"/\\");
#else
   std::string pt(p);
   // strip out executable file name
   int pos = pt.find_last_of("/\\");
#endif
   pt = pt.substr(0, pos);

   programFolder = std::string(pt.begin(),pt.end());
#elif defined XS_TARGET_OS_MAC

	CFBundleRef bRef = CFBundleGetMainBundle();
	CFURLRef rUrl = CFBundleCopyBundleURL(bRef);
	CFStringRef rPath = CFURLCopyFileSystemPath(rUrl,kCFURLPOSIXPathStyle);
	char cPath[255];
	CFStringGetCString(rPath,cPath,255,kCFStringEncodingUTF8);
	programFolder = std::string(cPath);
	CFRelease(rUrl);
	CFRelease(rPath);
	programFolder += "/Contents/Resources";

#elif defined __linux__

	#warning TODO implement exploding file Macros onLinux ... need the location of the bundle/resources
	programFolder = "";

#endif

	boost::ireplace_all(resultPath,"%PROGRAM_FOLDER%",programFolder);

#ifdef WIN32
	boost::ireplace_all(resultPath,"/","\\");
#elif defined XS_TARGET_OS_MAC
	boost::ireplace_all(resultPath,"\\","/");
#endif

	return resultPath;
}
