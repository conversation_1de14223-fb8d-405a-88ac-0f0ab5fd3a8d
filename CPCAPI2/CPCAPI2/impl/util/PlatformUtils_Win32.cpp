#include "PlatformUtils.h"
#ifdef _WIN32
#include <Windows.h>
#include <brand_branded.h>
#if 1 //(CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1 || CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1 || CPCAPI2_BRAND_BIEVENTS_MODULE == 1)

#include <sstream>
#include <iostream>

#include <string.h>
#include <tchar.h>

using namespace CPCAPI2::PlatformUtils;

#pragma comment(lib, "version.lib")

bool PlatformUtils::getOSInfo( OSInfo& outOSInfo )
{
   TCHAR *systemDir( NULL );
   UINT systemDirSize( 0 );
   const TCHAR *kernelDLLName = _T("\\kernel32.dll");
   UINT   kernelDLLPathSize( 0 );
   TCHAR *kernelDLLPath( NULL );
   DWORD versionInfoSize( 0 );
   UINT blockSize( 0 );
   void *block( NULL );
   void *versionInfo( NULL );
   VS_FIXEDFILEINFO *vInfo( NULL );
   std::ostringstream strs;
   bool result( false );

   // Do the easy part. set the operating system type.
   outOSInfo.osType = OSType_Windows;
   outOSInfo.osVersion = "";

   // Now get the operating system version as described by this article:
   // https://msdn.microsoft.com/en-us/library/windows/desktop/ms724429(v=vs.85).aspx
   //
   // Note that the GetVersionEx function is deprecated and does not work
   // properly past Windows 8.1 anymore. So now we have to do all of these
   // awkward machinations as describe in the above article:
   //
   // " ... To obtain the full version number for the operating system, call
   // the GetFileVersionInfo function on one of the system DLLs, such as
   // Kernel32.dll, then call VerQueryValue to obtain the
   // \\StringFileInfo\\<lang><codepage>\\ProductVersion subblock of the file
   // version information. ..."
   //
   // :-|  <--- unimpressed face.
   //
   // Thankfully the good folks over at stack overflow provided a solution
   // at: http://stackoverflow.com/questions/25986331/how-to-determine-windows-version-in-future-proof-way
   // ( Which I modified to fit our purposes. )

   // Get the size of the system directory
   systemDirSize = GetSystemDirectory( NULL, 0 );
   if( systemDirSize == 0 )
      goto cleanup;

   // Allocate enough space (systemDirSize includes null char)
   systemDir = ( TCHAR * ) calloc( systemDirSize, sizeof( TCHAR ));
   if( systemDir == NULL )
      goto cleanup;

   // Fetch the system directory
   if( GetSystemDirectory( systemDir, systemDirSize ) == 0 )
      goto cleanup;

   // Create the full path to the kernel.dll
   kernelDLLPathSize = systemDirSize + _tcslen( kernelDLLName );
   kernelDLLPath = ( TCHAR * ) calloc( kernelDLLPathSize, sizeof( TCHAR ));
   if( kernelDLLPath == NULL )
      goto cleanup;

   _tcsncpy( kernelDLLPath, systemDir, systemDirSize - 1 );
   _tcsncpy( kernelDLLPath + systemDirSize - 1, kernelDLLName, _tcslen( kernelDLLName ));
   kernelDLLPath[ kernelDLLPathSize - 1 ] = _T( '\0' );

   // OK. now that that's done.. Get the File Version Info
   versionInfoSize = GetFileVersionInfoSize( kernelDLLPath, NULL );
   if( versionInfoSize == 0 )
      goto cleanup;

   versionInfo = malloc( versionInfoSize );
   if( versionInfo == NULL )
      goto cleanup;

   if( GetFileVersionInfo( kernelDLLPath, 0, versionInfoSize, versionInfo ) == 0 )
      goto cleanup;

   // Having completed this, call VerQueryValue with the right path
   if( VerQueryValue( versionInfo, _T( "\\" ), &block, &blockSize ) == 0 )
      goto cleanup;

   if( blockSize < sizeof( VS_FIXEDFILEINFO ))
      goto cleanup;

   vInfo = ( VS_FIXEDFILEINFO * ) block;

   // Finally: the version.
   strs << ( int ) HIWORD( vInfo->dwProductVersionMS );
   strs << ".";
   strs << ( int ) LOWORD( vInfo->dwProductVersionMS );
   strs << ".";
   strs << ( int ) HIWORD( vInfo->dwProductVersionLS );
   outOSInfo.osVersion = strs.str();
   result = true;

cleanup:

   if( systemDir != NULL )
   {
      free( systemDir );
      systemDir = NULL;
   }

   if( kernelDLLPath != NULL )
   {
      free( kernelDLLPath );
      kernelDLLPath = NULL;
   }

   if( versionInfo != NULL )
   {
      free( versionInfo );
      versionInfo = NULL;
   }

   return result;
}

bool PlatformUtils::getDeviceInfo( DeviceInfo& outDeviceInfo )
{
   bool result( false );

   outDeviceInfo.deviceModel = "";
   outDeviceInfo.deviceFormFactor = DeviceFormFactor_Computer;
   result = true;

   return result;
}

bool PlatformUtils::getCpuArchInfo( CpuArchInfo& outCpuArchInfo )
{
   return false;
}

#endif // #if (CPCAPI2_BRAND_STRETTO_PROVISIONING_MODULE == 1 || CPCAPI2_BRAND_STRETTO_UEM_MODULE == 1)
#endif // _WIN32
