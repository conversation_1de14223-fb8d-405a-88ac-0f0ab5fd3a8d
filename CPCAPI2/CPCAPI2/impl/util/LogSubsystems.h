#pragma once

#if !defined(CPCAPI2_LOG_SUBSYSTEMS_H)
#define CPCAPI2_LOG_SUBSYSTEMS_H

#include <rutil/Subsystem.hxx>

namespace CPCAPI2
{

class CPCAPI2_Subsystem : public resip::Subsystem
{

public:

   static CPCAPI2_Subsystem PHONE;
   static CPCAPI2_Subsystem MEDIA_STACK; // actually WEBRTC
   static CPCAPI2_Subsystem SIP_ACCOUNT;
   static CPCAPI2_Subsystem MEDIA;
   static CPCAPI2_Subsystem SIP_CALL;
   static CPCAPI2_Subsystem PTT;
   static CPCAPI2_Subsystem SIP_EVENT;
   static CPCAPI2_Subsystem SIP_PRESENCE;
   static CPCAPI2_Subsystem SIP_BLF;
   static CPCAPI2_Subsystem SIP_MWI;
   static CPCAPI2_Subsystem SIP_FILETRANSFER;
   static CPCAPI2_Subsystem SIP_CHAT;
   static CPCAPI2_Subsystem LICENSING;
   static CPCAPI2_Subsystem SIP_DIALOGEVENT;
   static CPCAPI2_Subsystem SIP_REGEVENT;
   static CPCAPI2_Subsystem XMPP_STACK;
   static CPCAPI2_Subsystem XMPP_CONNECTION;
   static CPCAPI2_Subsystem XMPP_ACCOUNT;
   static CPCAPI2_Subsystem XMPP_CHAT;
   static CPCAPI2_Subsystem XMPP_ROSTER;
   static CPCAPI2_Subsystem XMPP_FILETRANSFER;
   static CPCAPI2_Subsystem XMPP_MULTI_USER_CHAT;
   static CPCAPI2_Subsystem XMPP_VCARD;
   static CPCAPI2_Subsystem XMPP_OPENSSL;
   static CPCAPI2_Subsystem XMPP_PUSH;
   static CPCAPI2_Subsystem REMOTE_SYNC;
   static CPCAPI2_Subsystem SNS;
   static CPCAPI2_Subsystem VCCS;
   static CPCAPI2_Subsystem BI_EVENTS;
   static CPCAPI2_Subsystem UEM;
   static CPCAPI2_Subsystem PEERCONNECTION;
   static CPCAPI2_Subsystem PROVISIONING;
   static CPCAPI2_Subsystem REMOTE_CONTROL;
   static CPCAPI2_Subsystem BROADSOFT_XSI;
   static CPCAPI2_Subsystem EXTERNAL;
   static CPCAPI2_Subsystem WATCHDOG;
   static CPCAPI2_Subsystem STRETTO_UEM;
   static CPCAPI2_Subsystem LDAP;
   static CPCAPI2_Subsystem PUSH_SERVER;
   static CPCAPI2_Subsystem WATCHERINFO;
   static CPCAPI2_Subsystem CONF_CONNECTOR;
   static CPCAPI2_Subsystem CALL_QUALITY;
   static CPCAPI2_Subsystem PUBLIC_API;
   static CPCAPI2_Subsystem MESSAGE_STORE;
   static CPCAPI2_Subsystem FILE_ACCESS;
   static CPCAPI2_Subsystem WEBSOCKET_SERVER;
   static CPCAPI2_Subsystem BACKGROUNDING;

private:

   explicit CPCAPI2_Subsystem(const char* name) : resip::Subsystem(name) {}

};

}

#endif
