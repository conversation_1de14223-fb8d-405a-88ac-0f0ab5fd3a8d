#pragma once

#if !defined(CPCAPI2_REGUTILS_H)
#define CPCAPI2_REGUTILS_H

#include <cpcapi2defs.h>

#include <string>
#include <boost/shared_array.hpp>

namespace CPCAPI2
{

//=============================================================================
// SUMMARY: Instantiated as an utility object that maintains
//          system registry reading/writing operations.
class RegUtils
{
public:

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to create a value in the system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool CreateKey(
      HKEY hInBaseKey,
      const char *szInSubKeyPath
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to read a string value from system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool QueryStringValue(
      HKEY hBaseKey,
      const char *szInSubKeyPath,
      const char *szInValueName,
      std::string &cOutValue
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to read a DWORD value from system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static DWORD QueryDWORDValue(
      HKEY hBaseKey,
      const char *szInSubKeyPath,
      const char *szInValueName,
      const DWORD dwInDefault = 0xFFFFFFFF
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to read a BLOB from system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool QueryBLOBValue(
	   HKEY hInBaseKey,
      const char *szInSubKeyPath,
      const char *szInValueName,
      boost::shared_array<BYTE>& pOutBlob,
      DWORD& dwOutBlobSize
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to write a string value to system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool SetStringValue(
      HKEY hBaseKey,
      const char *szInSubKeyPath,
      const char *szInValueName,
      const char *szInValue
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to write a DWORD value to system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool SetDWORDValue(
      HKEY hBaseKey,
      const char *szInSubKeyPath,
      const char *szInValueName,
      const DWORD dwInValue
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to delete a value from system registry.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool DeleteValue(
      HKEY hBaseKey,
      const char *szSubKeyPath,
      const char *szValueName
   );

   static void AddHashFromRegistryBlob(
      HKEY hInBaseKey,
      LPCSTR szInLocation,
      LPCSTR szInValue,
      std::string& cIoHashInfo
   );

};

}// namespace

#endif
