#include "PlatformLog.h"
#include <iostream>
#include <stdlib.h>

#ifdef ANDROID
#include <android/log.h>
#include <stdio.h>
#endif

#ifdef BB10
#include <stdio.h>
#include <malloc.h>
#include <sys/types.h>
#endif

#ifdef __THREADX
#include <teradici/tera_event.h>
#include <teradici/tera_errors.h>
#endif

namespace CPCAPI2
{

#ifdef __THREADX
bool sPlatformLogEnabled = false;
#endif

LOGGER_FN s_logFn = NULL;

void stdLog(CPCAPI2::LogLevel level, const char * format, ...)
{

   if (LogLevel_None == level)
   {
      return;
   }

   size_t bufSize;
   {
      // determine needed buffer size
      va_list args;
      va_start(args, format);
      #ifdef _WIN32
      bufSize = _vscprintf(format, args);
      #else
      bufSize = vsnprintf(NULL, 0, format, args);
      #endif
      va_end(args);
   }

   char* buffer;
   {
      // format string to buffer
      va_list args;
      va_start(args, format);
      buffer = (char*)malloc(bufSize + 1);
      #ifdef _WIN32
      vsnprintf_s(buffer, bufSize+1, _TRUNCATE, format, args);
      #else
      vsnprintf(buffer, bufSize+1, format, args);
      #endif

#ifdef __THREADX
      if (sPlatformLogEnabled)
      {
         std::string logContent(buffer);
         std::string delimiter = "\n";

         size_t pos = 0;
         std::string token;
         while ((pos = logContent.find(delimiter)) != std::string::npos) {
            token = logContent.substr(0, pos);
            mTERA_EVENT_LOG_MESSAGE(TERA_EVENT_CAT_MGMT_UC,
               TERA_EVENT_LEVEL_DEBUG,
               TERA_SUCCESS,
               "%s",
               token.c_str());
            logContent.erase(0, pos + delimiter.length());
         }
         if (!logContent.empty())
         {
            // log remainder
            mTERA_EVENT_LOG_MESSAGE(TERA_EVENT_CAT_MGMT_UC,
               TERA_EVENT_LEVEL_DEBUG,
               TERA_SUCCESS,
               "%s",
               logContent.c_str());
         }
      }
#endif

      va_end(args);
   }

#ifdef ANDROID
   __android_log_print(ANDROID_LOG_INFO, "CPCAPI2", "%s\n", buffer);
#else

   if (NULL == s_logFn)
   {
      std::cout << buffer << std::endl;
   }
   else
   {
      s_logFn(level, buffer);
   }
#endif
   free(buffer);
}

void setLogger(LOGGER_FN logFn)
{
   s_logFn = logFn;
}

}
