#include "ThreadUtils.h"

#include <assert.h>

#if defined(__linux__)
#include <sys/timerfd.h>
#include <sys/resource.h>
#include <pthread.h>
#include <unistd.h>
#include <stdint.h>

namespace CPCAPI2
{
const struct sched_param kRealTimePrio = { 8 };
const struct sched_param kResetPrio = { 0 };
}
#elif defined(__APPLE__)
#endif

namespace CPCAPI2
{
#ifdef WIN32
WaitableTimerWrapper::WaitableTimerWrapper(int intervalMs)
   : mTimerHandle(NULL)
{
   LONGLONG ns = intervalMs * 100000LL;
   /* Declarations */
   LARGE_INTEGER li;	/* Time defintion */
                     /* Create timer */
   if (!(mTimerHandle = CreateWaitableTimer(NULL, FALSE, NULL)))
      return;
   /* Set timer properties */
   li.QuadPart = -ns;
   SetWaitableTimer(mTimerHandle, &li, intervalMs, NULL, NULL, FALSE);
}
#elif defined(__linux__)
WaitableTimerWrapper::WaitableTimerWrapper(int intervalMs)
{
   int ret;
   unsigned int ns;
   unsigned int sec;
   int fd;
   struct itimerspec itval;

   /* Create the timer */
   fd = timerfd_create(CLOCK_MONOTONIC, 0);
   mInfo.wakeups_missed = 0;
   mInfo.timer_fd = fd;
   if (fd == -1)
      return;

   /* Make the timer periodic */
   sec = intervalMs / 1000;
   ns = (intervalMs % 1000) * 1000000;
   itval.it_interval.tv_sec = sec;
   itval.it_interval.tv_nsec = ns;
   itval.it_value.tv_sec = sec;
   itval.it_value.tv_nsec = ns;
   ret = timerfd_settime(fd, 0, &itval, NULL);
}
#elif defined (__APPLE__)
WaitableTimerWrapper::WaitableTimerWrapper(int intervalMs)
{
   /* create a new kernel event queue */
   if ((mKq = kqueue()) == -1)
   {
      assert(0);
   }

   /* initalise kevent structure */
   EV_SET(&mChange, 1, EVFILT_TIMER, EV_ADD | EV_ENABLE, 0, intervalMs, 0);

}
#else
WaitableTimerWrapper::WaitableTimerWrapper(int intervalMs)
   : mIntervalMs(intervalMs)
{
}
#endif
WaitableTimerWrapper::~WaitableTimerWrapper()
{
#ifdef WIN32
   CloseHandle(mTimerHandle);
#elif defined(__linux__)
   close(mInfo.timer_fd);
#elif defined(__APPLE__)
   close(mKq);
#endif
}
int WaitableTimerWrapper::Wait()
{
#ifdef WIN32
   WaitForSingleObject(mTimerHandle, INFINITE);
   return 0;
#elif defined(__linux__)
   uint64_t num_exp = 0;

   /* Wait for the next timer event. If we have missed any the
   number is written to "missed" */
   int ret = read(mInfo.timer_fd, &num_exp, sizeof(uint64_t));
   if (ret == -1)
   {
      return -1;
   }

   return (num_exp - 1);
#elif defined(__APPLE__)

   struct kevent change;    /* event we want to monitor */
   struct kevent event;     /* event that was triggered */

   int nev = kevent(mKq, &mChange, 1, &event, 1, NULL);

   if (nev < 0)
   {
      return -1;
   }
   else if (nev > 0)
   {
      if (event.flags & EV_ERROR)
      {
         return -1;
      }
   }
   return 0;
#else
   return -1;
#endif
}

#ifdef WIN32
void SetCurrentThreadPriority_Platform(CpcThreadPriority priority) {
   int desired_priority = THREAD_PRIORITY_ERROR_RETURN;
   switch (priority) {
   case CpcThreadPriority_BACKGROUND:
      desired_priority = THREAD_PRIORITY_LOWEST;
      break;
   case CpcThreadPriority_NORMAL:
      desired_priority = THREAD_PRIORITY_NORMAL;
      break;
   case CpcThreadPriority_DISPLAY:
      desired_priority = THREAD_PRIORITY_ABOVE_NORMAL;
      break;
   case CpcThreadPriority_REALTIME_AUDIO:
      desired_priority = THREAD_PRIORITY_TIME_CRITICAL;
      break;
   default:
      break;
   }
   assert(desired_priority != THREAD_PRIORITY_ERROR_RETURN);

   ::SetThreadPriority(::GetCurrentThread(), desired_priority);
}
#elif defined(__linux__)
struct ThreadPriorityToNiceValuePair {
   CpcThreadPriority priority;
   int nice_value;
};
const ThreadPriorityToNiceValuePair kThreadPriorityToNiceValueMap[4] = {
   { CpcThreadPriority_BACKGROUND, 10 },
   { CpcThreadPriority_NORMAL, 0 },
   { CpcThreadPriority_DISPLAY, -6 },
   { CpcThreadPriority_REALTIME_AUDIO, -10 },
};

int ThreadPriorityToNiceValue(CpcThreadPriority priority) {
   for (const ThreadPriorityToNiceValuePair& pair :
      kThreadPriorityToNiceValueMap) {
      if (pair.priority == priority)
         return pair.nice_value;
   }
   return 0;
}

bool GetCurrentThreadPriority_Linux(CpcThreadPriority* priority) {
   int maybe_sched_rr = 0;
   struct sched_param maybe_realtime_prio = { 0 };
   if (pthread_getschedparam(pthread_self(), &maybe_sched_rr,
      &maybe_realtime_prio) == 0 &&
      maybe_sched_rr == SCHED_RR &&
      maybe_realtime_prio.sched_priority == kRealTimePrio.sched_priority) {
      *priority = CpcThreadPriority_REALTIME_AUDIO;
      return true;
   }
   return false;
}

bool SetCurrentThreadPriority_Linux(CpcThreadPriority priority)
{
   CpcThreadPriority current_priority;
   if (priority != CpcThreadPriority_REALTIME_AUDIO &&
      GetCurrentThreadPriority_Linux(&current_priority) &&
      current_priority == CpcThreadPriority_REALTIME_AUDIO) {
      // If the pthread's round-robin scheduler is already enabled, and the new
      // priority will use setpriority() instead, the pthread scheduler should be
      // reset to use SCHED_OTHER so that setpriority() just works.
      pthread_setschedparam(pthread_self(), SCHED_OTHER, &kResetPrio);
      return false;
   }
   return priority == CpcThreadPriority_REALTIME_AUDIO  &&
      pthread_setschedparam(pthread_self(), SCHED_RR, &kRealTimePrio) == 0;
}

void SetCurrentThreadPriority_Platform(CpcThreadPriority priority) {
   if (SetCurrentThreadPriority_Linux(priority))
      return;

   // setpriority(2) should change the whole thread group's (i.e. process)
   // priority. However, as stated in the bugs section of
   // http://man7.org/linux/man-pages/man2/getpriority.2.html: "under the current
   // Linux/NPTL implementation of POSIX threads, the nice value is a per-thread
   // attribute". Also, 0 is prefered to the current thread id since it is
   // equivalent but makes sandboxing easier (https://crbug.com/399473).
   const int nice_setting = ThreadPriorityToNiceValue(priority);
   setpriority(PRIO_PROCESS, 0, nice_setting);
}
#else
void SetCurrentThreadPriority_Platform(CpcThreadPriority priority) {
   // if you hit this we should log an OBELISK ticket with context of call stack / usage
   assert(false);
}
#endif

void ThreadPriorityHelper::SetCurrentThreadPriority(CpcThreadPriority prio)
{
   SetCurrentThreadPriority_Platform(prio);
}

}
