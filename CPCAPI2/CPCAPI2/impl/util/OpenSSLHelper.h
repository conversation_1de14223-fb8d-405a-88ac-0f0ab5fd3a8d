#pragma once

#if !defined(CPCAPI2_OPENSSL_HELPER_H)
#define CPCAPI2_OPENSSL_HELPER_H

#include <memory>
#include <iostream>

#include <openssl/ec.h>
#include <openssl/pem.h>
#include <openssl/hmac.h>

namespace CPCAPI2
{
template<class T>
class openssl_shared_ptr : public std::shared_ptr<T>
{
};

template<class T>
class openssl_unique_ptr : public std::unique_ptr<T>
{
};

auto BIGNUM_DELETER = [](BIGNUM* o) { BN_free(o); }; \

template<>
class openssl_unique_ptr<BIGNUM> : public std::unique_ptr<BIGNUM, decltype(BIGNUM_DELETER)>
{
public:
  openssl_unique_ptr(BIGNUM* obj = nullptr) : std::unique_ptr<BIGNUM, decltype(BIGNUM_DELETER)>(obj, BIGNUM_DELETER) {}
};

template<>
class openssl_shared_ptr<BIGNUM> : public std::shared_ptr<BIGNUM>
{
public:
   openssl_shared_ptr(BIGNUM* obj = nullptr) : std::shared_ptr<BIGNUM>(obj, BIGNUM_DELETER) {}
};

#define OPENSSL_TYPE(type) \
auto type##_DELETER = [](type* o) { type##_free(o); }; \
template<> \
class openssl_unique_ptr<type> : public std::unique_ptr<type, decltype(type##_DELETER)> \
{ \
public: \
  openssl_unique_ptr(type* obj) : std::unique_ptr<type, decltype(type##_DELETER)>(obj, type##_DELETER) {} \
}; \
template<> \
class openssl_shared_ptr<type> : public std::shared_ptr<type> \
{ \
public: \
  openssl_shared_ptr(type* obj = nullptr) : std::shared_ptr<type>(obj, type##_DELETER) {} \
};

OPENSSL_TYPE(BIO);
OPENSSL_TYPE(EVP_PKEY);
OPENSSL_TYPE(ECDSA_SIG);
OPENSSL_TYPE(EC_GROUP);
OPENSSL_TYPE(EC_POINT);
OPENSSL_TYPE(EC_KEY);
OPENSSL_TYPE(EVP_CIPHER_CTX);
OPENSSL_TYPE(EVP_MD_CTX);
OPENSSL_TYPE(HMAC_CTX);

}
#endif // CPCAPI2_OPENSSL_HELPER_H
