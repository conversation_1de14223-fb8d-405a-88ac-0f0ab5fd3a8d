#pragma once

#if !defined(__CPCAPI2_REGISTRY_HELPERS_H__)
#define __CPCAPI2_REGISTRY_HELPERS_H__

#ifdef _WIN32

#include <string>
#include <vector>


namespace CPCAPI2
{

//=============================================================================
// SUMMARY: Instantiated as an utility object that maintains
//          License doc operations on Windows platform
class RegistryHelpers
{
public:

   // return the location of the space registry key
   static std::string RegistryLocation();
   // write with fallback a registry key
   static bool WriteRegistryStringValue(std::string location, std::string value);

   // write all the hardware id's out to some subkey list
   static bool WriteHardwareToRegistry(std::string keynamePrefix, const std::vector<std::string>&);

   // verify hardware against registry keys.
   static bool VerifyHardware( const std::vector<std::string>& hdds, const std::vector<std::string>& macs);

   //
   static std::string ReadRegistryStringValue(std::string location);
private:
   static bool WriteHKLMStringKey(std::string location, std::string value);
   static bool WriteHKCUStringKey(std::string location, std::string value);

};
}
#endif
#endif // __CPCAPI2_REGISTRY_HELPERS_H__
