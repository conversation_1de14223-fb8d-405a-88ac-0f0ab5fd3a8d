#ifdef __APPLE__
#include <TargetConditionals.h>
#endif

#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE == 1
#import <Foundation/Foundation.h>
#import <UIKit/UIDevice.h>

#include <string>
#include "MachineIdentification.h"

namespace CPCAPI2
{
std::string MachineIdentification::ComputerName()
{
   std::string ret = [[UIDevice currentDevice].name UTF8String];
   if (ret.size() == 0)
   {
      ret = "Unknown iOS Device";
   }
   return ret;
}
}
#endif // defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE == 1
