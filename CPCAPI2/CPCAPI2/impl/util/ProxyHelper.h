#pragma once

#ifndef CPCAPI2_PROXY_HELPER_H
#define CPCAPI2_PROXY_HELPER_H

#include <cpcstl/string.h>

namespace CPCAPI2
{
class ProxyHelper
{
public:
   // Retrieve OS configured web proxy (server:port) and include proxy exception list
#if defined(WIN32) || defined(__linux__) || defined(__APPLE__)
   static cpc::string GetProxyServerInfo(const cpc::string& destination, cpc::string& bypassList);
#else
   static cpc::string GetProxyServerInfo(const cpc::string& destination, cpc::string& bypassList) {return "";}
#endif

};
}

#endif // CPCAPI2_PROXY_HELPER_H
