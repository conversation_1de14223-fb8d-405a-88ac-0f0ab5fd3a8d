//
//  AppleVideo.m
//  CPCObjAPI2
//
//  Created by <PERSON><PERSON> on 4/9/19.
//  Copyright © 2019 CounterPath. All rights reserved.
//

#include "util/Mac/AppleVideo.h"
#import <Foundation/Foundation.h>
#include <TargetConditionals.h>
#if TARGET_OS_IPHONE == 1
#else
#import "webrtc/modules/video_render/mac/RTCMTLNSVideoView.h"
#endif

AppleVideo::AppleVideo()
{
   mLocalSurface = NULL;
}

AppleVideo::~AppleVideo()
{
   trackLocalSurface(NULL);
   
   for (std::map<int, void*>::iterator it = mRemoteSurfaces.begin(); it != mRemoteSurfaces.end(); ++it)
   {
      void* oldSurface = it->second;
      if (oldSurface != NULL)
      {
         NSObject* obj = (NSObject*)oldSurface;
         [obj release];
      }
   }
}

void AppleVideo::trackRemoteSurface(int streamId, void* surface)
{
#if TARGET_OS_IPHONE == 1
#else
   if ([RTCMTLNSVideoView isMetalAvailable])
   {
      return;
   }
#endif
   
   std::map<int, void*>::iterator it = mRemoteSurfaces.find(streamId);
   if (it != mRemoteSurfaces.end())
   {
      void* oldSurface = it->second;
      NSObject* obj = (NSObject*)oldSurface;
      [obj release];
   }
   
   if (surface == NULL)
   {
      mRemoteSurfaces.erase(streamId);
   }
   else
   {
      NSObject* obj = (NSObject*)surface;
      [obj retain];
      mRemoteSurfaces[streamId] = surface;
   }
}

void AppleVideo::trackLocalSurface(void* surface)
{
#if TARGET_OS_IPHONE == 1
#else
   if ([RTCMTLNSVideoView isMetalAvailable])
   {
      return;
   }
#endif
   
   if (mLocalSurface != NULL)
   {
      void* oldSurface = mLocalSurface;
      NSObject* obj = (NSObject*)oldSurface;
      [obj release];
   }
   
   if (surface == NULL)
   {
      mLocalSurface = NULL;
   }
   else
   {
      NSObject* newSurface = (NSObject*)surface;
      [newSurface retain];
      mLocalSurface = surface;
   }
}
