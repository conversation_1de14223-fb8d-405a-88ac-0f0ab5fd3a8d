//
//  MacRenderSurfaceTracker.m
//  CPCObjAPI2
//
//  Created by <PERSON><PERSON> on 4/2/19.
//  Copyright © 2019 CounterPath. All rights reserved.
//

#include "MacRenderSurfaceTracker.h"
#import <Foundation/Foundation.h>

void MacRenderSurfaceTracker::trackSurface(int streamId, void* surface)
{
   std::map<int, void*>::iterator it = mSurfaces.find(streamId);
   if (it != mSurfaces.end())
   {
      void* oldSurface = it->second;
      NSObject* obj = (NSObject*)oldSurface;
      [obj release];
   }
   
   NSObject* obj = (NSObject*)surface;
   [obj retain];
   mSurfaces[streamId] = surface;
}

void MacRenderSurfaceTracker::releaseSurface(int streamId)
{
   std::map<int, void*>::iterator it = mSurfaces.find(streamId);
   if (it != mSurfaces.end())
   {
      NSObject* obj = (NSObject*)it->second;
      [obj release];
      mSurfaces.erase(it);
   }
}
