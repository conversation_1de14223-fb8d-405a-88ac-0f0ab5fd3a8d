//
//  MacRenderSurfaceTracker.h
//  CPCObjAPI2
//
//  Created by <PERSON><PERSON> on 4/2/19.
//  Copyright © 2019 CounterPath. All rights reserved.
//

#ifndef MacRenderSurfaceTracker_h
#define MacRenderSurfaceTracker_h

#include <map>

class MacRenderSurfaceTracker
{
public:
   void trackSurface(int streamId, void* surface);
   void releaseSurface(int streamId);
private:
   std::map<int /*stream id */, void* /* surface */> mSurfaces;
};


#endif /* MacRenderSurfaceTracker_h */
