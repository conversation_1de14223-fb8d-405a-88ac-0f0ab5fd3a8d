//
//  AppleVideo.h
//  CPCObjAPI2
//
//  Created by <PERSON><PERSON> on 4/9/19.
//  Copyright © 2019 CounterPath. All rights reserved.
//

#ifndef AppleVideo_h
#define AppleVideo_h

#include <map>

class AppleVideo
{
public:
   AppleVideo();
   ~AppleVideo();

   void trackRemoteSurface(int streamId, void* surface);
   void trackLocalSurface(void* surface);
private:
   std::map<int /*stream id */, void* /* surface */> mRemoteSurfaces;
   void* mLocalSurface;
};

#endif /* AppleVideo_h */
