#ifndef CMAC_SECURITY_ACCESS_H
#define CMAC_SECURITY_ACCESS_H 


#include <string>
#include <Security/Security.h>


namespace CPCAPI2
{

	
class CSecurityAccess
{
public:
	
	typedef enum {
		KC_SYSTEM,
		KC_ROOT,
		KC_LOGIN,
	} KC_TYPES;
	
	static CSecurityAccess* instance();
	
	virtual ~CSecurityAccess();
	
	std::string getPrimaryMAC()		{ return mPrimaryMAC; }
	std::string getHardwareID()		{ return mHardwareID; }
	std::string getSerialNumber()		{ return mSerialNumber; }
	std::string getComputerName()		{ return mComputerName; }
	std::string getMachineType()		{ return mMachineType; }
	
	bool openCertStore(KC_TYPES types);
	const char* getNextCert(int& size);

private:
	
	CSecurityAccess();
	
	static CSecurityAccess* mInstance;
	
	std::string mSerialNumber;
	std::string mHardwareID;
	std::string mPrimaryMAC;
	std::string mComputerName;
	std::string mMachineType;
	
	SecKeychainSearchRef mKeyChainSearch;
};

}

#endif
