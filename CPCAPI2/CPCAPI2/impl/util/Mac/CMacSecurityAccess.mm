/*
 *  CMacSecurityAccess.cpp
 *  MACCPSI
 *
 *  Created by <PERSON><PERSON> on 27/08/2009.
 *  Copyright 2009 CounterPath Corporation. All rights reserved.
 *
 */

#if defined(__APPLE__)
#include "TargetConditionals.h"
#if (!defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE)

#import <Foundation/Foundation.h>

#include <CoreFoundation/CoreFoundation.h>
#include <CoreServices/CoreServices.h>
#include <SystemConfiguration/SCDynamicStoreCopySpecific.h>

//#include "abstractphone/CAbstractPhone.h"
//#include "helper/CUtils.h"
#include "../MachineIdentification.h"

#include "CMacSecurityAccess.h"




using namespace CPCAPI2;

CSecurityAccess* CSecurityAccess::mInstance = NULL;


CSecurityAccess*
CSecurityAccess::instance()
{
	if (mInstance == NULL) {
		mInstance = new CSecurityAccess();
	}
	return mInstance;
}


CSecurityAccess::CSecurityAccess() :
	mKeyChainSearch(NULL)
{
	@autoreleasepool
   {
      io_service_t    platformExpert = IOServiceGetMatchingService(kIOMasterPortDefault,
                                                                   IOServiceMatching("IOPlatformExpertDevice"));

      if (platformExpert)
      {
         CFTypeRef serial = IORegistryEntryCreateCFProperty(platformExpert,
                                                            CFSTR(kIOPlatformSerialNumberKey),
                                                            kCFAllocatorDefault, 0);
         if (serial) {
            NSString* sn = [[NSString alloc] initWithString:(__bridge NSString*)serial];
            if (sn) {
               mSerialNumber = [sn UTF8String];
            }
            CFRelease(serial);
         }

         CFTypeRef hardware = IORegistryEntryCreateCFProperty(platformExpert,
                                                              CFSTR(kIOPlatformUUIDKey),
                                                              kCFAllocatorDefault, 0);

         if (hardware) {
            NSString* hid = [[NSString alloc] initWithString:(__bridge NSString*)hardware];
            if (hid) {
               mHardwareID = [hid UTF8String];
            }
            CFRelease(hardware);
         }

         IOObjectRelease(platformExpert);
      }

      mPrimaryMAC = MachineIdentification::GetLocalMACAddress();

      NSString* computerName = (__bridge_transfer NSString*)SCDynamicStoreCopyComputerName(NULL,NULL);
      // .jza. try and solve a weird case where SCDynamicStoreCopyComputerName returns NULL if no computer name set
      if (computerName == nil)
      {
         computerName = @"nonamecp";
      }

      mComputerName = [computerName UTF8String];

#if TARGET_CPU_X86
      // [AB] this code fails to compile for x64
      OSErr err;
      char *machineName=NULL;    // This is really a Pascal-string with a length byte.
      err = Gestalt(gestaltUserVisibleMachineName, (long*) &machineName);
      if (err == noErr) {
         NSString* machineType  = [[NSString alloc] initWithCString: machineName +1 length: machineName[0]];
         mMachineType = [machineType UTF8String];
         [machineType release];
      }
#endif
   }
}


CSecurityAccess::~CSecurityAccess()
{
}


bool
CSecurityAccess::openCertStore(KC_TYPES type)
{
	std::string pathName;
	OSStatus status = noErr;
	@autoreleasepool
   {

      switch (type) {
         case KC_SYSTEM:
            pathName = "/Library/Keychains/System.keychain";
            break;

         case KC_ROOT:
            pathName = "/System/Library/Keychains/SystemRootCertificates.keychain";
            break;

         default:
            NSString* p = [@"~" stringByExpandingTildeInPath];
            pathName = [[p stringByAppendingPathComponent:@"/Library/Keychains/login.keychain"] UTF8String];
            break;
      }

      if (mKeyChainSearch) {
         CFRelease(mKeyChainSearch);
         mKeyChainSearch = NULL;
      }

      SecKeychainRef systemCertsKeyChain;
      status = SecKeychainOpen(pathName.c_str(), &systemCertsKeyChain);

      if (status != noErr) {
         return false;
      }

      status = ::SecKeychainSearchCreateFromAttributes(systemCertsKeyChain, kSecCertificateItemClass, NULL, &mKeyChainSearch);

      CFRelease(systemCertsKeyChain);

      if (status != noErr) {
         return false;
      }

   }
	return true;
}


const char*
CSecurityAccess::getNextCert(int& size)
{
	@autoreleasepool
   {
      size = 0;
      if (mKeyChainSearch == NULL) {
         return NULL;
      }

      OSStatus status = noErr;
      SecKeychainItemRef itemRef = nil;

      status = ::SecKeychainSearchCopyNext(mKeyChainSearch, &itemRef);

      if (status != noErr) {
         CFRelease(mKeyChainSearch);
         mKeyChainSearch = NULL;
         return NULL;
      }

      void *data;
      UInt32 dataSize;
      status = ::SecKeychainItemCopyAttributesAndData(itemRef, NULL, NULL, NULL, &dataSize, &data);

      if (status == noErr && data != NULL)
      {
         char* buffer = new char[dataSize];
         memcpy(buffer, data, dataSize);
         size = dataSize;
         /* status = */ ::SecKeychainItemFreeAttributesAndData(NULL, data);
         CFRelease(itemRef);
         return (const char*)buffer;
      }

      if (itemRef != NULL) {
         CFRelease(itemRef);
      }

      CFRelease(mKeyChainSearch);
      mKeyChainSearch = NULL;

   }
	return NULL;
}

#endif
#endif
