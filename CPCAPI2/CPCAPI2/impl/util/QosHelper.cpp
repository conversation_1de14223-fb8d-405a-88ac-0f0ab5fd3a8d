#include "QosHelper.h"
#include <qos/QosSocketManager.hxx>

namespace CPCAPI2
{
void SignalingQos::unsetQos(resip::Socket s, int transportType, const char* file, int line)
{
   webrtc_recon::QosSocketManager::SocketClose(s);
}
resip::AfterSocketCreationFuncPtr SignalingQos::getSocketCreateQosFun(unsigned int dscp)
{
   getQosHelperFun(0, dscp);
   getQosHelperFun(1, dscp);
   getQosHelperFun(2, dscp);
   getQosHelperFun(3, dscp);
   getQosHelperFun(4, dscp);
   getQosHelperFun(5, dscp);
   getQosHelperFun(6, dscp);
   getQosHelperFun(7, dscp);
   getQosHelper<PERSON>un(8, dscp);
   getQosHelper<PERSON>un(9, dscp);
   getQosHelperFun(10, dscp);
   getQosHelperFun(11, dscp);
   getQosHelperFun(12, dscp);
   getQosHelperFun(13, dscp);
   getQosHelperFun(14, dscp);
   getQosHelperFun(15, dscp);
   getQosHelperFun(16, dscp);
   getQosHelperFun(17, dscp);
   getQosHelperFun(18, dscp);
   getQosHelperFun(19, dscp);
   getQosHelperFun(20, dscp);
   getQosHelperFun(21, dscp);
   getQosHelperFun(22, dscp);
   getQosHelperFun(23, dscp);
   getQosHelperFun(24, dscp);
   getQosHelperFun(25, dscp);
   getQosHelperFun(26, dscp);
   getQosHelperFun(27, dscp);
   getQosHelperFun(28, dscp);
   getQosHelperFun(29, dscp);
   getQosHelperFun(30, dscp);
   getQosHelperFun(31, dscp);
   getQosHelperFun(32, dscp);
   getQosHelperFun(33, dscp);
   getQosHelperFun(34, dscp);
   getQosHelperFun(35, dscp);
   getQosHelperFun(36, dscp);
   getQosHelperFun(37, dscp);
   getQosHelperFun(38, dscp);
   getQosHelperFun(39, dscp);
   getQosHelperFun(40, dscp);
   getQosHelperFun(41, dscp);
   getQosHelperFun(42, dscp);
   getQosHelperFun(43, dscp);
   getQosHelperFun(44, dscp);
   getQosHelperFun(45, dscp);
   getQosHelperFun(46, dscp);
   getQosHelperFun(47, dscp);
   getQosHelperFun(48, dscp);
   getQosHelperFun(49, dscp);
   getQosHelperFun(50, dscp);
   getQosHelperFun(51, dscp);
   getQosHelperFun(52, dscp);
   getQosHelperFun(53, dscp);
   getQosHelperFun(54, dscp);
   getQosHelperFun(55, dscp);
   getQosHelperFun(56, dscp);
   return 0;
}
}
