#include "CurlDav.h"

#include <assert.h>

#ifdef sun
#include <strings.h>
#endif


using namespace xten;

CurlDav::CurlDav(const char* hostUri, const char* user, const char* pass) :
   mUser(user),
   mPassword(pass),
   mUri(hostUri)
{
   if (!mUri.valid())
   {
      throw std::string("CurlDav: Could not parse URL");
   }
}

int
CurlDav::provideAuth(void *userdata, const char * /*realm*/, int attempt, char *username, char *password)
{
   //TBD
 /*  CurlDav* dav = static_cast<CurlDav*>(userdata);

   std::string::size_type sz = dav->mUser.copy(username, NE_ABUFSIZ-1);   // copy up to NE_ABUFSIZ, *not* std::string::npos
   username[sz] = 0;

   sz = dav->mPassword.copy(password, NE_ABUFSIZ-1);
   password[sz] = 0;

   return attempt;*/
   return 0;
}






CurlDav::Session::Session(CurlDav& dav) :
   mCurlDav(dav),
   mCurlHttp(NULL),
   mCurlHttpSession(NULL)
{
   mCurlHttp = new CurlHttp(mCurlDav.mUri.uri(),mCurlDav.mUser,mCurlDav.mPassword,"","");
   mCurlHttpSession = new CurlHttp::Session(*mCurlHttp);
   mDavHeaders.clear();
   mDavHeaders.push_back(CurlHttp::HttpHeader("Connection","TE"));
   mDavHeaders.push_back(CurlHttp::HttpHeader("TE","trailers"));
}

CurlDav::Session::~Session()
{
   if (mCurlHttpSession)
   {
      delete mCurlHttpSession;
      mCurlHttpSession = NULL;
   }

   if (mCurlHttp)
   {
      delete mCurlHttp;
      mCurlHttp = NULL;
   }
}


void CurlDav::Session::setProxyServer(const char *hostname, unsigned int port, const char *bypassList)
{
   if (mCurlHttpSession)
   {
      mCurlHttpSession->setProxyServer(hostname,port, bypassList);
   }
}


void CurlDav::Session::setAcceptableSSLFailures(int failures)
{
   if (mCurlHttpSession)
   {
      mCurlHttpSession->setAcceptableFailures(failures);
   }
}


CurlDav::Result
CurlDav::Session::get(const char* path, char*& result, int& length,
                      bool& hasModTime, time_t *modtime, int& statusCode)
{
   //TBD
   /*
   ne_request *req = ne_request_create(mSession, "GET", path);

   ContentReader reader;

   LastModifiedReader lmReader(modtime);

   ne_add_response_header_handler(req, "Last-Modified", LastModifiedReader::handleLastModified, &lmReader);
*/
   /* Read the value of the Content-Length header into ctx.total */
/*   ne_add_response_header_handler(req, "Content-Length",
                                  ContentReader::handleContentLength,
                                  &reader);

   ne_add_response_header_handler(req, "Transfer-Encoding",
                                  ContentReader::handleTransferEncoding,
                                  &reader);

   ne_add_response_body_reader(req, ne_accept_2xx, ContentReader::handleBodyRead, &reader);

   int ret = ne_request_dispatch(req);

   if (ret == NE_OK && ne_get_status(req)->klass != 2)
   {
      ret = NE_ERROR;
   }

   statusCode = ne_get_status(req)->code;
   hasModTime = lmReader.headerWasParsed();

   if (ret == NE_OK) {
      reader.getResult(result, length);
   }
   ne_request_destroy(req);
*/
   int ret = 0;

   if (mCurlHttpSession)
   {
      std::unique_ptr<CurlHttp::Result> result(mCurlHttpSession->get(mCurlDav.mUri.path(),mDavHeaders));
      ret = (int) result->getResultCode();
   }

   return (Result) ret;
}

static int getCurlDepth(CurlDav::Depth depth)
{
   //TBD
   /*
   switch(depth)
   {
      case CurlDav::Zero:
         return NE_DEPTH_ZERO;
      case CurlDav::One:
         return NE_DEPTH_ONE;
      case CurlDav::Infinity:
         return NE_DEPTH_INFINITE;
      default:
         assert(0);
         return -1;
   }*/
   return 0;
}

CurlDav::Result
CurlDav::Session::copy(const char* src, const char* dest, Depth depth, bool overwrite, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_copy(mSession, overwrite, getCurlDepth(depth), src, dest);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::move(const char* src, const char* dest, bool overwrite, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_move(mSession, overwrite, src, dest);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::deleteUri(const char* path, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_delete(mSession, path);
   */
   return (Result)0;

}

CurlDav::Result
CurlDav::Session::getModTime(const char *path, time_t *modtime, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_getmodtime(mSession, path, modtime);
   */
   return (Result)0;
}

//Create a collection at 'path', which MUST have a trailing slash.
CurlDav::Result
CurlDav::Session::mkcol(const char *path, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_mkcol(mSession, path);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::put(const char* path, int fd, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_put(mSession, path, fd);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::put(const char* path, const char* buffer, size_t size, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_put_from_memory(mSession, path, buffer, size);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::get(const char* path, int fd, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_get(mSession, path, fd);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::putIfNotModified(const char *path, int fd,
                                   time_t modtime, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result)ne_put_if_unmodified(mSession, path, fd, modtime);
   */
   return (Result)0;
}

CurlDav::Result
CurlDav::Session::putIfNotModified(const char* path, const char* buffer, size_t size,
                                   time_t modtime, int& statusCode)
{
   //TBD
   /*
   statusCode = -1;
   return (Result) ne_put_from_memory_if_unmodified(mSession, path, buffer, size, modtime);
   */
   return (Result)0;
}
