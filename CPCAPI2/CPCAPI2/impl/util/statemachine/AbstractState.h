#pragma once
#ifndef __CPCAPI2_ABSTRACTSTATE_H__
#define __CPCAPI2_ABSTRACTSTATE_H__

#include <memory>

namespace CPCAPI2
{
   /**
    * The purpose of this interface is to describe what each state in the
    * state machine is able to do.
    */
   class AbstractState
   {
   public:
      
      AbstractState() {}
      virtual ~AbstractState() {}
      
      /**
       * Returns a constant name for the state, which can be used to print
       * out debug information. The name should be unique, though there is
       * no hard requirement for uniqueness.
       */
      virtual const char *getName( void ) const = 0;

      /**
       * Returns a constant string which is required to be a unique ID
       * representing this state.  A GUID or similar naming algorithm should
       * be used in order to guarantee uniqueness.
       */
      virtual const char *getUniqueID( void ) const = 0;

      /**
       * Returns the list of state IDs INTO which the current state is
       * allowed to transition. This is used for verifying the correctness
       * of requested state transitions in the system.
       *
       * @param outNextIDs (out param) a list of string IDs (a pointer to a
       *        list of strings), optionally NULL terminated.
       * @param outNumIDs (out param) a count of the number of IDs to consider.
       */
      virtual void getNextStateIDs( const char ***outNextIDs, unsigned int *outNumIDs ) = 0;

      /**
       * Invoked when entering the state from another state (includes the
       * previous state, in case there is some special behavior along that
       * transition)
       */
      virtual void enter( const char* prevStateID ) = 0;

      /**
       * Invoked when leaving this state, into another state (includes the
       * new target state, in case some special behavior needs to be
       * triggered for that transition)
       */
      virtual void leave( const char* nextStateID ) = 0;
   };
   typedef std::shared_ptr< AbstractState > AbstractStatePtr;
   typedef std::weak_ptr< AbstractState > AbstractStateWPtr;

   /**
    * Sometimes it's just not quite enough information to only know the old and
    * the new state and some additional reason code needs to be transmitted as
    * to the cause of the state change. By default it will be set to zero which
    * means, 'no reason'
    */
   typedef int AbstractStateReasonCode;

}

#endif // __CPCAPI2_ABSTRACTSTATE_H__
