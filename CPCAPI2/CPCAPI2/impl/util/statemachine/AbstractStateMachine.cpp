#include <assert.h>
#include <algorithm>

#include <rutil/Logger.hxx>

#include "AbstractStateMachine.h"
#include "AbstractStateMachineListener.h"

using namespace CPCAPI2;
using CPCAPI2::CPCAPI2_Subsystem;

AbstractStateMachine::AbstractStateMachine( CPCAPI2_Subsystem loggingSubsystem, const std::string& machineName ) :
   m_LoggingSubsystem( loggingSubsystem ),
   m_MachineName( machineName )
{
}

AbstractStateMachine::~AbstractStateMachine()
{
   m_StateImpls.clear(); // shared_ptr will delete things
   m_Listeners.clear();
}

bool AbstractStateMachine::addListener( AbstractStateMachineListener* listener )
{
   if( listener == NULL )
      return false;

   if( std::find( m_Listeners.begin(), m_Listeners.end(), listener ) != m_Listeners.end() )
      return false;

   m_Listeners.push_back( listener );
   return true;
}

bool AbstractStateMachine::removeListener( AbstractStateMachineListener* listener )
{
   m_Listeners.remove( listener );
   return true;
}

AbstractStatePtr AbstractStateMachine::getState( const char *stateID )
{
   if( stateID == NULL )
      return NULL;

   AbstractStatePtr result;
   std::map< std::string, AbstractStatePtr >::iterator iter ( m_StateImpls.find( stateID ));
   if( iter != m_StateImpls.end() )
      result = iter->second;

   return result;
}

AbstractStatePtr AbstractStateMachine::addState( AbstractStatePtr pState )
{
   if( pState == NULL )
      return NULL;

   AbstractStatePtr result;
   std::string stateID( pState->getUniqueID() );
   std::map< std::string, AbstractStatePtr >::iterator iter ( m_StateImpls.find( stateID ));
   if( iter != m_StateImpls.end() )
      result = iter->second;

   m_StateImpls[ stateID ] = pState;
   return result;
}

bool AbstractStateMachine::setCurrentState( const char *newStateID, AbstractStateReasonCode reason )
{
   bool valid( false );

   AbstractStatePtr pNewState = getState( newStateID );
   if (pNewState.get() == NULL)
   {
      GenericLog(m_LoggingSubsystem, resip::Log::Err, << "AbstractStateMachine(" << m_MachineName << ")::setCurrentState()  Unable to get abstract state machine state for " << newStateID);
      return valid;
   }

   // If there is an existing state, check to see if the transition
   // makes sense according to the rules (the initial setup of the
   // state machine will involve the current state being set to NULL,
   // so allow transition to any state from NULL).
   AbstractStatePtr pCurrentState = m_CurrentState.lock();
   if( pCurrentState.get() != NULL )
   {
      GenericLog(m_LoggingSubsystem, resip::Log::Debug, << "AbstractStateMachine(" << m_MachineName << ")::setCurrentState() Transitioning from " << pCurrentState->getName() << " to "
         << pNewState->getName());

      const char **nextIDs = NULL;
      unsigned int nextCount = 0;
      pCurrentState->getNextStateIDs( &nextIDs, &nextCount );
      if (nextCount == 0 || nextIDs == NULL)
      {
         GenericLog(m_LoggingSubsystem, resip::Log::Err, << "AbstractStateMachine(" << m_MachineName << ")::setCurrentState() No state machine transitions found from "
            << pCurrentState->getName() << " when going to " << pNewState->getName());
         return valid;
      }

      // Ensure that the new state is an allowed transition
      for( unsigned int i = 0 ; i < nextCount ; ++i )
      {
         if( nextIDs[ i ] == NULL )
            break;

         std::string candidateID( nextIDs[ i ] );
         if( candidateID == newStateID )
         {
            // Found a match.
            valid = true;
            break;
         }
      }
   }
   else
   {
      valid = true; // m_CurrentState == NULL
   }

   if( valid )
   {
      // Use local vars to prevent re-entrancy issues while calling
      // leave and enter (this can cause another switch)
      const char *curStateID( getCurrentStateID() );
      //const char *prevStateID( getPreviousStateID() );

      // First do the actual state transition
      GenericLog( m_LoggingSubsystem, resip::Log::Debug,
         << "AbstractStateMachine(" << m_MachineName << ")::setCurrentState(): State transition from "
         << ( pCurrentState.get() != NULL ? pCurrentState->getName() : "(initial state)" )
         << " to " << pNewState->getName() );

      m_PreviousState = pCurrentState;
      m_CurrentState  = pNewState;

      // Fire an event to notify of the state transition
      std::list< AbstractStateMachineListener* >::const_iterator iter( m_Listeners.begin() );
      for( ; iter != m_Listeners.end() ; ++iter )
      {
         if( *iter == NULL )
            continue;

         (*iter)->onStateChange( pCurrentState, pNewState, reason );
      }

      // Now invoke the callbacks to the states. These are done at
      // the end because they could result in further state transitions

      // leave the current state
      if( pCurrentState != NULL )
         pCurrentState->leave( newStateID ); // param: switching into

      // enter the new state
      pNewState->enter( curStateID ); // param: switching from
   }
   else
   {
      GenericLog(m_LoggingSubsystem, resip::Log::Err, << "AbstractStateMachine(" << m_MachineName << ")::setCurrentState() Invalid state machine transition from "
         << pCurrentState.get() << " to " << pNewState.get());
   }
   return valid;
}

AbstractStatePtr AbstractStateMachine::getCurrentState()
{
   return m_CurrentState.lock();
}

const char *AbstractStateMachine::getCurrentStateID()
{
   AbstractStatePtr pState( m_CurrentState.lock() );
   if( pState.get() != NULL )
      return pState->getUniqueID();

   return NULL;
}

AbstractStatePtr AbstractStateMachine::getPreviousState()
{
   return m_PreviousState.lock();
}

const char *AbstractStateMachine::getPreviousStateID()
{
   AbstractStatePtr pState( m_PreviousState.lock() );
   if( pState.get() != NULL )
      return pState->getUniqueID();

   return NULL;
}
