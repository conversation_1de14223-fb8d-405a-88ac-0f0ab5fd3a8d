#pragma once
#ifndef __CPCAPI2_ABSTRACTSTATEMACHINELISTENER_H__
#define __CPCAPI2_ABSTRACTSTATEMACHINELISTENER_H__

#include "AbstractState.h"

namespace CPCAPI2
{
   /**
    * Listener(s) to the state machine. All callbacks are invoked
    * directly inside of the state machine thread.
    */
   class AbstractStateMachineListener
   {
   public:
      /**
       * Callback to notify listeners of a change of state.
       *
       * @param oldState the state from which we are switching
       * @param newState the state to which we are switching
       * @param reason the 'reason' (by default zero which means, no reason
       *        specified) as to why the state was changed. This can sometimes
       *        be useful especially in server-side initiated disconnects.
       */
      virtual void onStateChange(
         const AbstractStatePtr oldState,
         const AbstractStatePtr newState,
         const AbstractStateReasonCode reason = 0 ) = 0;
   };
}

#endif // __CPCAPI2_ABSTRACTSTATEMACHINELISTENER_H__
