#pragma once
#ifndef __CPCAPI2_ABSTRACTSTATEMACHINE_H__
#define __CPCAPI2_ABSTRACTSTATEMACHINE_H__

#include <string>
#include <map>
#include <list>

#include "AbstractState.h"
#include "../LogSubsystems.h"

namespace CPCAPI2
{
   // Forward decl's
   class AbstractStateMachineListener;
   
   /**
    * This class implements the state machine. There is no actual
    * implementation in this file except for the state transitions,
    * which delegate to the actual state objects.
    *
    * The responsibility of this file is to implement the state
    * transitions and ensure that the states move in the correct
    * way.
    */
   class AbstractStateMachine
   {
   public:
      /**
       */
      AbstractStateMachine( CPCAPI2_Subsystem loggingSubsystem, const std::string& machineName );
      virtual ~AbstractStateMachine();

      /**
       * Adds a listener to the state change notifications (listener
       * ownerwhip remains with the caller).
       */
      bool addListener( AbstractStateMachineListener* listener );

      /**
       * Remove the listener from the list of state change notifications
       */
      bool removeListener( AbstractStateMachineListener* listener );

      /**
       * Adds the state to the state machine. Once added the state will be
       * "live" in the sense that it could be transitioned to.  The
       * NotificationStateMachine assumes ownership of the state and will be
       * responsible for deletion of the state.
       *
       * @return NULL, or a pointer to the previous state (if it existed)
       */
      AbstractStatePtr addState( AbstractStatePtr pState );

      /**
       * Returns a pointer to the state implementation associated with a
       * specific state ID.
       */
      AbstractStatePtr getState( const char *stateID );

      /**
       * Sets the current state to the new state, if possible.
       *
       * @returns false if a transition to the new state was not part
       * of the "state transition rules", or if no state implementation
       * was provided for that state.
       */
      bool setCurrentState( const char *stateID, AbstractStateReasonCode reason = 0 );

      /**
       * Return the current state as known by the state machine. This may
       * return an empty ptr if the current state was removed and/or
       * deleted.
       */
      AbstractStatePtr getCurrentState( void );
      const char       *getCurrentStateID( void );

      /**
       * Return the previous state, this may be useful in order to determine
       * a course of action while transitioning. This may return an empty
       * ptr if the previous state was removed and/or deleted.
       */
      AbstractStatePtr getPreviousState( void );
      const char       *getPreviousStateID( void );

   protected:

      CPCAPI2_Subsystem m_LoggingSubsystem; // where to send log statements
      const std::string m_MachineName; // each machine needs a name for differentiation of logs

      // A map which goes from the state to the implementation (owned)
      std::map< std::string, AbstractStatePtr > m_StateImpls;

      // The list of listeners registered for state changes (not owned)
      std::list< AbstractStateMachineListener * > m_Listeners;

      // Tracks the current and previous states (not owned)
      AbstractStateWPtr m_CurrentState;
      AbstractStateWPtr m_PreviousState;
   };
}

#endif // __CPCAPI2_ABSTRACTSTATEMACHINE_H__
