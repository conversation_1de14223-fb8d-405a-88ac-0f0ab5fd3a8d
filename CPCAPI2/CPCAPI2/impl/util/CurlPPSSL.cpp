#include <cpcapi2defs.h>
#include "EmbeddedCert.h"

#include "cpc_logger.h"

#include "CurlPPSSL.h"

#include "resip/stack/ssl/SecurityHelper.hxx"
#if __APPLE__
#include "TargetConditionals.h"
#if TARGET_OS_IPHONE
#  include "resip/stack/ssl/IOSSecurity.hxx"
#else
#  include "CertHelper.h"
#  include "resip/stack/ssl/IOSSecurity.hxx"
#  include <stdio.h>
#endif
#endif

#if BB10
#include "resip/stack/ssl/counterpath/BlackberrySecurity.hxx"
#endif

//#ifdef XS_TARGET_OS_MAC
//#elif defined(XS_TARGET_OS_LINUX)
//#  include "security/Linux/XLinuxSecurity.hxx"
//#elif defined(XS_TARGET_OS_SUNOS)
//#  include "resip/stack/ssl/Security.hxx"
//#else
#if defined(WinRT)
#include "resip/stack/ssl/counterpath/XWinRTSecurity.hxx"
#elif _WIN32
#include "resip/stack/ssl/counterpath/XWinSecurity.hxx"
#endif

#if __linux__
  #if ANDROID
  #include "resip/stack/ssl/counterpath/AndroidSecurity.hxx"
  #else
  #include "resip/stack/ssl/counterpath/LinuxSecurity.hxx"
  #endif
#endif

// WinCrypt macros related to X509 stomp on the openssl typedefs of the same name
#if _WIN32
#undef X509_NAME
#endif
#include <openssl/ssl.h>
#include <openssl/x509v3.h>
#include <openssl/pkcs12.h>
#include <openssl/err.h>


#define SUA_SUBSYSTEM cpsi::Subsystem::Storage
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

using namespace CPCAPI2;

CurlPPSSL::CurlPPSSL(
   SslCipherOptions tlsSettings,
   int acceptableFailures,
   const cpc::string& certFolder,
   bool useCertFolderOnly,
   const cpc::string& clientCert,
   const cpc::string& clientCertPassword )
   : m_CertificatesFolder( certFolder ),
     m_UseCertFolderOnly( useCertFolderOnly ),
     m_ClientCertificate( clientCert ),
     m_ClientCertificatePassword( clientCertPassword ),
     m_AcceptableFailures( acceptableFailures ),
     m_TlsSettings( tlsSettings )
{
}

CurlPPSSL::CurlPPSSL( const CurlPPSSL& that )
{
   m_CertificatesFolder        = that.m_CertificatesFolder;
   m_UseCertFolderOnly         = that.m_UseCertFolderOnly;
   m_ClientCertificate         = that.m_ClientCertificate;
   m_ClientCertificatePassword = that.m_ClientCertificatePassword;
   m_AcceptableFailures        = that.m_AcceptableFailures;
   m_TlsSettings               = that.m_TlsSettings;
}

CurlPPSSL::~CurlPPSSL()
{
}

CURLcode CurlPPSSL::operator()( void * sslctx )
{
   SSL_CTX * ctx = (SSL_CTX *) sslctx;

   resip::SecurityHelper::configureSslContext(ctx, (resip::SecurityTypes::SSLType)m_TlsSettings.getTLSVersion(SslCipherUsageHttp), m_TlsSettings.getCiphers(SslCipherUsageHttp).c_str(), resip::SecurityTypes::TLSMode_TLS_Client);

   X509_STORE* rootSSLCerts = 0;

   //here to add the cert from folder storage
   resip::Data pathToCerts = m_CertificatesFolder.c_str();
   bool bUseCertFolderOnly = m_UseCertFolderOnly;

   unsigned short usCertStorageType = 0;

   //logic for alternate certificate/credentials storage (only for Windos and MAC, Linux only uses folder storage)
   // - if 'pathToCerts' empty, only OS-specific storage will be used
   // - if if 'pathToCerts' not empty:
   //     - if 'bFolderStorageOnly' flag is 'true', only the pathToCerts folder will be used;
   //     - if 'bFolderStorageOnly' flag is 'false', both the pathToCerts folder and OS-specific storage will be used;
   if(pathToCerts.size() == 0)
      usCertStorageType = resip::CERT_OS_SPECIFIC_STORAGE;
   else
   {
      if(bUseCertFolderOnly)
         usCertStorageType = resip::CERT_FILESYSTEM_STORAGE;
      else
         usCertStorageType = resip::CERT_FILESYSTEM_STORAGE | resip::CERT_OS_SPECIFIC_STORAGE;
   }

#if defined(WinRT)
	resip::Security* sec = new resip::XWinRTSecurity(pathToCerts, usCertStorageType);
#elif _WIN32
   resip::Security* sec = new resip::XWinSecurity(pathToCerts, usCertStorageType);
#elif __APPLE__
   resip::Security* sec = new resip::IOSSecurity(pathToCerts, usCertStorageType);
#elif __linux__
#if ANDROID
   resip::Security* sec = new resip::AndroidSecurity(pathToCerts, usCertStorageType);
#else
   resip::Security* sec = new resip::LinuxSecurity(pathToCerts);
#endif
#elif BB10
   resip::Security* sec = new resip::BlackberrySecurity(pathToCerts);
#else
   assert(0);
#endif
      
   //load all the certificates
   sec->preload();
   SSL_CTX *sslCtx = sec->getSslCtx();
   if (sslCtx)
   {
      rootSSLCerts = SSL_CTX_get_cert_store(sslCtx);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
      // hack, we only need the certifcates from the cert store, so set the reference to null, to avoid destruction
      sslCtx->cert_store = 0;
#else
      X509_STORE_up_ref(rootSSLCerts);
#endif
   }
   else
   {
      //SUA_TRACE_ERROR("Could not retrieve the cert store")
      return CURLE_FAILED_INIT;
   }
   //destroy (resip) security object after retrieving the certificates;
   delete sec;

   //set the cert store
   if(rootSSLCerts)
   {
      SSL_CTX_set_cert_store(ctx, rootSSLCerts);
   }
   else
   {
      //SUA_TRACE_ERROR("Could not set the cert store")
      return CURLE_FAILED_INIT;
   }

   unsigned int sslFlags = 0;
   if( m_ClientCertificate.size() > 0 )
   {
      sslFlags = SSL_VERIFY_PEER|SSL_VERIFY_CLIENT_ONCE|SSL_VERIFY_FAIL_IF_NO_PEER_CERT;

      FILE *fp = NULL;
      PKCS12 *p12 = 0;
      EVP_PKEY *pkey = 0;
      X509 *cert = 0;
      STACK_OF(X509) *ca = 0;

      if (!(fp = fopen( m_ClientCertificate.c_str(), "rb"))) 
      {
	      ////SUA_TRACES_ERROR("Error opening " << (sess->getClientCert()).c_str() << " file.");
	      return CURLE_SSL_CERTPROBLEM;
      }
      p12 = d2i_PKCS12_fp(fp, NULL);
      fclose (fp);
      if (!p12)
      {
	      ////SUA_TRACES_ERROR("Error reading PKCS#12 client cert file");
         return CURLE_SSL_CERTPROBLEM;
      }
      /*int ret = */PKCS12_parse(p12, m_ClientCertificatePassword.c_str(), &pkey, &cert, &ca);
      if (!pkey || !cert) 
      {
	      ////SUA_TRACES_ERROR("Error parsing PKCS#12 client cert file");
         return CURLE_SSL_CERTPROBLEM;
      }
      PKCS12_free(p12);
      p12 = NULL;
      
      /*ret = */SSL_CTX_use_certificate(ctx, cert);
      /*ret = */SSL_CTX_use_PrivateKey(ctx, pkey);

      //extract commonName (subjectAltName for later use); the format should be sip:user@domain
      /* .. seems this is not needed -- !ds!
      cpc::string name;
      cpc::string uri = getCertName( cert );
      {
         size_t p = cpc::string::npos;

         // user
         if ((p = uri.find(":")) != cpc::string::npos)
         {
            size_t pp = cpc::string::npos;
            if ((pp = uri.find("@")) != cpc::string::npos && pp > p) 
               name = uri.substr(p+1 ,pp-p-1);
            else
               name = uri.substr(0,p);
         }
         else
         {
            size_t pp = cpc::string::npos;
            if ((pp = uri.find("@")) != cpc::string::npos) 
               name = uri.substr(0 ,pp);
            else
               name = uri;
         }
      }
      sess->setCommonName(name.c_str());
      */
      //TBD - too see if i need to free cert and pkey or OpenSSL is doing it
   }
   else
   {
      sslFlags = SSL_VERIFY_PEER;
   }

#ifdef ANDROID
   if (usCertStorageType == resip::CERT_OS_SPECIFIC_STORAGE) SSL_CTX_set_verify(ctx, sslFlags, resip::AndroidSecurity::verifyCallback);
   else
#endif
   {
      SSL_CTX_set_verify(ctx, sslFlags, 0L);
      SSL_CTX_set_cert_verify_callback(ctx, certVerifyCallback, this);
   }

   ERR_clear_error();

   return CURLE_OK;
}

void CurlPPSSL::getCertNames(X509 *cert, cpc::vector<PeerName> &peerNames) 
{
   //stolen from resip but change to return the fullname instead of just hostname
   if(NULL == cert)
      return;

   if(peerNames.size() > 0)
      peerNames.clear();

   cpc::string commonName;

   // look at the Common Name to find the peerName of the cert 
   X509_NAME* subject = X509_get_subject_name(cert);
   if(NULL == subject)
   {
      ////SUA_TRACES_ERROR("Invalid certificate: subject not found ");
      return;
   }

   int i =-1;
   while( true )
   {
      i = X509_NAME_get_index_by_NID(subject, NID_commonName,i);
      if ( i == -1 )
      {
         break;
      }
      assert( i != -1 );
      X509_NAME_ENTRY* entry = X509_NAME_get_entry(subject,i);
      assert( entry );
      
      ASN1_STRING*	s = X509_NAME_ENTRY_get_data(entry);
      assert( s );

      //int t = ASN1_STRING_type(s);
      int l = ASN1_STRING_length(s);
      const unsigned char* d = ASN1_STRING_get0_data(s);

      cpc::string name((const char*)d, l);
      ////SUA_TRACES_DEBUG("got x509 string type=" << t << " len="<< l << " data=" << d );
      assert( name.size() == (unsigned)l );
      
      ////SUA_TRACES_DEBUG("Found common name in cert of " << name );
      
      commonName = name;
   }

   // Look at the SubjectAltName, and if found, set as peerName
   GENERAL_NAMES* gens;
   gens = (GENERAL_NAMES*)X509_get_ext_d2i(cert, NID_subject_alt_name, NULL, NULL);
   for(int i = 0; i < sk_GENERAL_NAME_num(gens); i++)
   {  
      GENERAL_NAME* gen = sk_GENERAL_NAME_value(gens, i);

      ////SUA_TRACES_DEBUG("subjectAltName of cert contains type <" << gen->type << ">" );

      if (gen->type == GEN_DNS)
      {
         ASN1_IA5STRING* asn = gen->d.dNSName;
         cpc::string dns((const char*)asn->data, asn->length);
         PeerName peerName(SubjectAltName, dns);
         peerNames.push_back(peerName);
         ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains DNS <" << dns << ">" );
      }
          
      if (gen->type == GEN_EMAIL)
      {
         ////SUA_TRACES_DEBUG("subjectAltName of cert has EMAIL type" );
      }
          
      if(gen->type == GEN_URI) 
      {
         ASN1_IA5STRING* asn = gen->d.uniformResourceIdentifier;
         cpc::string uri((const char*)asn->data, asn->length);
         try
         {
             PeerName peerName(SubjectAltName, uri);
             peerNames.push_back(peerName);
             ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains URI <" << uri << ">" );
         }
         catch (...)
         {
             ////SUA_TRACES_DEBUG("subjectAltName of TLS session cert contains unparseable URI");
         }
      }
   }
   sk_GENERAL_NAME_pop_free(gens, GENERAL_NAME_free);

   // If there are no peer names from the subjectAltName, then use the commonName
   if(peerNames.empty())
   {
      PeerName peerName(CommonName, commonName);
      peerNames.push_back(peerName);
   }
}

cpc::string CurlPPSSL::getCertName(X509 *cert)
{
   cpc::string certName;
   cpc::vector<PeerName> cNames;

   //get all the names (subjectAltName or CommonName)
   getCertNames(cert, cNames);

   //prefere the subjectAltName
   for(cpc::vector<PeerName>::const_iterator it = cNames.begin(); it != cNames.end(); it++)
   {
      if(it->mType == SubjectAltName)
      {
         return it->mName;
      }
   }

   //if not subjectAltName found, get the CommonName
   for(cpc::vector<PeerName>::const_iterator it = cNames.begin(); it != cNames.end(); it++)
   {
      if(it->mType == CommonName)
      {
         return it->mName;
      }
   }
   ////SUA_TRACES_ERROR("This certificate doesn't have neither subjectAltName nor commonName");
   return "";
}

int CurlPPSSL::certVerifyCallback( X509_STORE_CTX *ctx )
{
   int failures = 0;

#if defined(USE_SSL)
#if (CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECK != 1)
   if (SSLeay() != OPENSSL_VERSION_NUMBER)
   {
      // if you are hitting this, you may be hitting OBELISK-2718. To work around this issue, either curl or tsm needs to be rebuilt,
      // or the link order needs to be such that cpcapi2's OpenSSL is used instead of curl or tsm's OpenSSL.
      ErrLog(<< "OpenSSL runtime does not match headers from compilation, " << SSLeay() << " vs " << OPENSSL_VERSION_NUMBER);
      assert(0);
   }
#endif // CPCAPI2_BRAND_DISABLE_OPENSSL_VERSION_CHECKS
#endif // USE_SSL

   int res = X509_verify_cert(ctx);
   if(!res)
   {
      switch(X509_STORE_CTX_get_error(ctx))
      {
         case X509_V_ERR_CERT_NOT_YET_VALID:
            failures |= E_CERT_NOT_YET_VALID;
            break;

		   case X509_V_ERR_CERT_HAS_EXPIRED:
            failures |= E_CERT_EXPIRED;
            break;

		   case X509_V_ERR_UNABLE_TO_GET_ISSUER_CERT_LOCALLY:
         case X509_V_ERR_SELF_SIGNED_CERT_IN_CHAIN:
         case X509_V_ERR_DEPTH_ZERO_SELF_SIGNED_CERT:
            failures |= E_CERT_NOT_TRUSTED;
            break;

         default: 
            failures |= E_CERT_OTHER_ERROR;
            break;
      }

      char cBuf1[500];
      char cBuf2[500];
      X509 *pErrCert;
      //int iErr = 0;
      int iDepth = 0;
      pErrCert = X509_STORE_CTX_get_current_cert(ctx);
      /*iErr = */X509_STORE_CTX_get_error(ctx);
      iDepth = X509_STORE_CTX_get_error_depth(ctx);

      if (NULL != pErrCert)
         X509_NAME_oneline(X509_get_subject_name(pErrCert),cBuf1,256);

      snprintf(cBuf2, 500, ", depth=%d %s\n", iDepth, cBuf1);
      ////SUA_TRACES_ERROR("Error when verifying server's chain of certificates: " << X509_verify_cert_error_string(ctx->error) << cBuf2 );

      // !jza! hack for Rogers:
      // when on mac and provisioning against https server with self signed cert,
      // OpenSSL is returning 0 for the result of X509_verify_cert (error condition, which makes sense)
      // but ctx->error is also 0 which indicates no error. on windows ctx->error has value meaning
      // untrusted cert. not sure why there is a difference.
      bool openSSLMacWorkaroundForSelfSigned = false;
#if defined(XS_TARGET_OS_MAC)
      if (m_AcceptableFailures & E_CERT_NOT_TRUSTED)
      {
         openSSLMacWorkaroundForSelfSigned = true;
      }
#endif

      if((failures & m_AcceptableFailures) || openSSLMacWorkaroundForSelfSigned) 
      {
         res = 1;
         X509_STORE_CTX_set_error(ctx, 0);
#if defined OPENSSL_VERSION_NUMBER && ( OPENSSL_VERSION_NUMBER < 0x10100000 )
         ctx->error_depth = 0;
#else
         X509_STORE_CTX_set_error_depth(ctx, 0);
#endif
         ////SUA_TRACES_INFO("Allowed error, proceed further with https");
      }
   }
   return res;
}

int CurlPPSSL::certVerifyCallback( X509_STORE_CTX *ctx, void *arg )
{
   CurlPPSSL *This = reinterpret_cast< CurlPPSSL * >( arg );
   if( This != NULL )
      return This->certVerifyCallback( ctx );

   return 0;
}
