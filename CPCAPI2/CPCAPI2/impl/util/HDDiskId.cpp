#include "HDDiskId.h"
#include <boost/algorithm/string.hpp>
#include <openssl/sha.h>
#include <openssl/evp.h>
#include <sstream>

//#ifdef XS_TARGET_OS_WINDOWS
#ifdef WIN32

#include <tchar.h>

#define SUA_SUBSYSTEM cpsi::Subsystem::Utilities

namespace CPCAPI2
{

#define  IDE_IDENTIFY_ATAPI            0xA1           //  ATAPI ID sector.
#define  IDE_IDENTIFY_ATA              0xEC           //   ATA ID sector.

#define  FILE_DEVICE_SCSI              0x0000001b
#define  IOCTL_SCSI_MINIPORT_IDENTIFY  ((FILE_DEVICE_SCSI << 16) + 0x0501)
#define  IOCTL_SCSI_MINIPORT           0x0004D008     //  see NTDDSCSI.H for definition

#define  SENDIDLENGTH  sizeof(SENDCMDOUTPARAMS) + 512

class SHA1Str
{
public:
   SHA1Str() {}
   ~SHA1Str() {}
   
   void SetMessage(std::string msg)
   {
      _msg = msg;
   }

   std::string GetDigestStr()
   {
      unsigned char digest[20];
      EVP_MD_CTX* mdctx = EVP_MD_CTX_create();

      EVP_DigestInit_ex(mdctx, EVP_sha1(), NULL);

      // Set bytes
      EVP_DigestUpdate(mdctx, (const void *)_msg.c_str(), _msg.size());

      // Finalize
      EVP_DigestFinal_ex(mdctx, digest, NULL);
      
      EVP_MD_CTX_destroy(mdctx);

      // output bytes as hex
      std::ostringstream os;
      os.flags(std::ios::hex);
      os.fill('0');
      os.width(2);

      for (int i = 0; i < 20; i++)
      {
         os << (unsigned int)digest[i];
      }
      _digest = os.str();
      boost::to_upper(_digest);
      return _digest;
   }

private:
   std::string _msg;
   std::string _digest;
};

static BOOL IdentifyDrive (
   HANDLE hDriveHandle, 
   PSENDCMDINPARAMS pSendInParams,
   PSENDCMDOUTPARAMS pSendOutParams, 
   BYTE byIDCmd, 
   BYTE bDriveNum,
   PDWORD lpcbBytesReturned
)
{
   //sends an IDENTIFY command (ATA or ATAPI) to a drive
   pSendInParams -> cBufferSize = 512;
   pSendInParams -> irDriveRegs.bFeaturesReg = 0;
   pSendInParams -> irDriveRegs.bSectorCountReg = 1;
   pSendInParams -> irDriveRegs.bCylLowReg = 0;
   pSendInParams -> irDriveRegs.bCylHighReg = 0;
   pSendInParams -> irDriveRegs.bDriveHeadReg = 0xA0 | ((bDriveNum & 1) << 4);
   pSendInParams -> irDriveRegs.bCommandReg = byIDCmd;
   pSendInParams -> bDriveNumber = bDriveNum;
   pSendInParams -> cBufferSize = 512;

   BOOL bRet = DeviceIoControl (
               hDriveHandle, 
               0x0007c088,
               (LPVOID) pSendInParams,
               sizeof(SENDCMDINPARAMS) - 1,
               (LPVOID) pSendOutParams,
               sizeof(SENDCMDOUTPARAMS) + 512 - 1,
               lpcbBytesReturned, NULL
             );

   return bRet;
}

void ConvertToReadableString(DWORD dwDiskData [256], int firstIndex, int lastIndex, std::string &cOutString)
{
   char cString [1024];
   int iIndex = 0;
   int iPosition = 0;

   // so, each integer has two characters stored in it backwards: 
   // hibyte for the first character and lobyte for the second character
   for (iIndex = firstIndex; iIndex <= lastIndex; iIndex++)
   {
      cString [iPosition] = (char) (dwDiskData [iIndex] / 256);
      iPosition++;

      cString [iPosition] = (char) (dwDiskData [iIndex] % 256);
      iPosition++;
   }
   cString [iPosition] = '\0';

   cOutString = std::string(cString);
}

static bool SelectDrive(int iDrive, DWORD dwDiskData [256])
{
   //select the first master fixed iDrive
   bool bChoose = false;

   if (iDrive % 2)
   {
      ////SUA_TRACES_INFO("Slave iDrive, search further ...")
   }
   else
   {
      ////SUA_TRACES_INFO("Master iDrive; choose this one")
      bChoose = true;
   }
   return bChoose;
}

static int GetHexValue(char ch)
{
   int ret = -1;

   switch (ch)
   {
   case '0': ret = 0; break;
   case '1': ret = 1; break;
   case '2': ret = 2; break;
   case '3': ret = 3; break;
   case '4': ret = 4; break;
   case '5': ret = 5; break;
   case '6': ret = 6; break;
   case '7': ret = 7; break;
   case '8': ret = 8; break;
   case '9': ret = 9; break;
   case 'A': case 'a': ret = 10; break;
   case 'B': case 'b': ret = 11; break;
   case 'C': case 'c': ret = 12; break;
   case 'D': case 'd': ret = 13; break;
   case 'E': case 'e': ret = 14; break;
   case 'F': case 'f': ret = 15; break;
   }

   return ret;
}

std::string HDDiskId::ConvertHexBytesToString(std::string in)
{
   std::string out;

   int length = in.size();

   if ((length % 4) != 0)
   {
      return in;  // If not a whole number of HEX byte pairs, return the input
   }

   boost::replace_all(in, " 0", "20"); // On some WD drives spaces comes back as <space>0 instead of 20 hex?

   int val0, val1, temp;

   for (int i=0; i<length; /**/)
   {
      // If -1 is returned for any value it is not a valid HEX byte, give up and return input
      if ((temp = GetHexValue(in[i++])) == -1) return in;
      val0 = temp * 16;
      
      if ((temp = GetHexValue(in[i++])) == -1) return in;
      val0 += temp;   
      
      if ((temp = GetHexValue(in[i++])) == -1) return in;
      val1 = temp * 16;
      
      if ((temp = GetHexValue(in[i++])) == -1) return in;
      val1 += temp;   

      out += (char)val1;
      out += (char)val0;
   }

   return out;
}

std::string HDDiskId::GetHddInfoAdminRights (void)
{
   std::string cHddInfo;
   
   //for Win NT, Win2K and Win XP admin rights are required
   //cOutInfo will store serial number concatenated with model number for the first
   //master fixed iDrive found; 
   //to do: if no such a iDrive is found, we could use a master iDrive
   bool bDone = false;

   for (int iDrive = 0; iDrive < 8; iDrive++)
   {
      HANDLE hDriveHandle = 0;

      TCHAR tchDriveName [256];
      _stprintf_s(tchDriveName, 256, _T("\\\\.\\PhysicalDrive%d"), iDrive);

      hDriveHandle = CreateFile (
                              tchDriveName,
                              GENERIC_READ | GENERIC_WRITE, 
                              FILE_SHARE_READ | FILE_SHARE_WRITE ,
                              NULL,
                              OPEN_EXISTING, 
                              0, 
                              NULL
                              );
      if (hDriveHandle == INVALID_HANDLE_VALUE)
      {
         ////SUA_TRACES_DEBUG("Cannot open physical iDrive " << iDrive << ", error code: " << GetLastError ())
         continue;
      }

      GETVERSIONOUTPARAMS tParams;
      DWORD               dwBytesReturned = 0;

      memset ((void*) &tParams, 0, sizeof(tParams));
      if ( ! DeviceIoControl (
               hDriveHandle, 
               0x00074080,
               NULL, 
               0,
               &tParams,
               sizeof(tParams),
               &dwBytesReturned, NULL) )
      {         
         //SUA_TRACES_ERROR("DFP_GET_VERSION failed for iDrive " << iDrive)
      }
      else
      {
         if (tParams.bIDEDeviceMap > 0)
         {
            BYTE             byIDCmd = 0;   // IDE or ATAPI IDENTIFY cmd
            SENDCMDINPARAMS  tSendCmdInParams;

			   // Now, get the ID sector for all IDE devices in the system.
            // here's the trick: 
            //    - for ATAPI device use the ATAPI command;
            //    - anything else the ATA command
            byIDCmd = (tParams.bIDEDeviceMap >> iDrive & 0x10) ? IDE_IDENTIFY_ATAPI : IDE_IDENTIFY_ATA;

            memset (&tSendCmdInParams, 0, sizeof(tSendCmdInParams));

            BYTE byOutCmd [sizeof (SENDCMDOUTPARAMS) + 512 - 1];
            memset (byOutCmd, 0, sizeof(byOutCmd));

            if ( IdentifyDrive (hDriveHandle, 
                     &tSendCmdInParams, 
                     (PSENDCMDOUTPARAMS)&byOutCmd, 
                     (BYTE) byIDCmd,
                     (BYTE) iDrive,
                     &dwBytesReturned))
            {
               DWORD dwDiskData[256];
               int ijk = 0;
               USHORT *pIdSector = (USHORT *)
                           ((PSENDCMDOUTPARAMS) byOutCmd) -> bBuffer;

               for (ijk=0; ijk<256; ijk++)
                  dwDiskData[ijk] = pIdSector[ijk];

               if (SelectDrive(iDrive, dwDiskData))
               {
                  //SUA_TRACES_DEBUG("Found master fixed iDrive no:" << iDrive);
                     
                  std::string cProductId;
                  ConvertToReadableString(dwDiskData, 27, 46, cProductId);
                  if ( !cProductId.empty() )
                  {
                     boost::trim(cProductId);
                     //SUA_TRACES_DEBUG("Found valid model number: " + cProductId);
                     cHddInfo = cProductId;
                  }

                  std::string cSerialNumber;
                  ConvertToReadableString(dwDiskData, 10, 19, cSerialNumber);
                  if ( !cSerialNumber.empty() )
                  {
                     cHddInfo += "::";  // Separator token

                     boost::trim(cSerialNumber);
                     //SUA_TRACES_DEBUG("Found valid serial number: " + cSerialNumber);
                     cHddInfo += cSerialNumber;
                     bDone = true;
                  }
               }
            }
	      }
      }
      CloseHandle (hDriveHandle);
      if (bDone)
      {
         break;
      }
   }
   
   return cHddInfo;
}


std::string HDDiskId::GetHddInfoNoRights(void)
{
   std::string cHddInfo;
   bool bDone = false;
   
   //for Win NT, Win2K and Win XP no admin rights are required
   //to do: selection for master/fixed iDrive
   for (int iDrive = 0; iDrive < 8; iDrive++)
   {
      HANDLE hDriveHandle = 0;

      TCHAR tchDriveName [256];
      _stprintf_s(tchDriveName, 256, _T("\\\\.\\PhysicalDrive%d"), iDrive);

      hDriveHandle = CreateFile (
                              tchDriveName, 
                              0,
                              FILE_SHARE_READ | FILE_SHARE_WRITE, 
                              NULL,
                              OPEN_EXISTING, 
                              0, 
                              NULL
                           );
      if (hDriveHandle == INVALID_HANDLE_VALUE)
      {
         //SUA_TRACES_DEBUG("Cannot open physical iDrive " << iDrive << ", error code: " << GetLastError ())
         continue;
      }
         
      STORAGE_PROPERTY_QUERY tQuery;
      DWORD dwBytesReturned = 0;
      char cBuffer [10000];

      memset ((void *) & tQuery, 0, sizeof (tQuery));
      tQuery.PropertyId = StorageDeviceProperty;
      tQuery.QueryType = PropertyStandardQuery;

      memset (cBuffer, 0, sizeof (cBuffer));

      if ( !DeviceIoControl (
            hDriveHandle, 
            IOCTL_STORAGE_QUERY_PROPERTY,
            & tQuery,
            sizeof (tQuery),
            & cBuffer,
            sizeof (cBuffer),
            & dwBytesReturned, 
            NULL) 
         )
      {
			//SUA_TRACES_ERROR("\nDeviceIOControl IOCTL_STORAGE_QUERY_PROPERTY error " << GetLastError ());
		}
      else
      {
			STORAGE_DEVICE_DESCRIPTOR * pDescrip = (STORAGE_DEVICE_DESCRIPTOR *) & cBuffer;

         if (pDescrip->RemovableMedia)
         {
            // If this is a removable media storage, i.e. CD-ROM drive etc. let's continue searching
            //SUA_TRACES_DEBUG(" STORAGE_DEVICE skipping removable media ProductId=" << &cBuffer[pDescrip->ProductIdOffset]);
            continue;
         }

         //get the product ID
         std::string cProductId(&cBuffer[pDescrip->ProductIdOffset]);
         if( !cProductId.empty() )
         {
            boost::trim(cProductId);
            //SUA_TRACES_DEBUG("Non-admin got productID: " + cProductId);
            cHddInfo += cProductId;
         }

         //get the serial number
         std::string cSerialNumber = ConvertHexBytesToString(&cBuffer[pDescrip->SerialNumberOffset]);
         if( !cSerialNumber.empty() )
         {
            cHddInfo += "::"; // Separator token

            boost::trim(cSerialNumber);
            //SUA_TRACES_DEBUG("Non-admin getting serialNo: " + cSerialNumber);
            cHddInfo += cSerialNumber;
            bDone = true;
         }
      }
      CloseHandle (hDriveHandle);
      if (bDone)
      {
         break;
      }
   }
   
   if ( !bDone)
   {
      cHddInfo = GetHddSerialAsScsiDevice();
   }

   return cHddInfo;
}

std::string HDDiskId::GetHddSerialAsScsiDevice()
{
   // This is a 'backdoor' to get information, if everything else fails, access the drive as a SCSI device
   // Returns the first scsi disk device.
   std::string cHddInfo;
   bool bDone = false;
   
   for (int controller = 0; controller < 16; controller++)
   {
      HANDLE hScsiDriveIOCTL = 0;
         
      TCHAR  tchDriveName [256];
      _stprintf_s(tchDriveName, 256, _T("\\\\.\\Scsi%d:"), controller);

      //  Windows NT, Windows 2000, any rights should do
      hScsiDriveIOCTL = CreateFile (tchDriveName,
                                    GENERIC_READ | GENERIC_WRITE, 
                                    FILE_SHARE_READ | FILE_SHARE_WRITE, 
                                    NULL,
                                    OPEN_EXISTING, 
                                    0, 
                                    NULL
                                 );

      if (hScsiDriveIOCTL != INVALID_HANDLE_VALUE)
      {
         for (int iDrive = 0; iDrive < 2; iDrive++)
         {
            char buffer [sizeof(SRB_IO_CONTROL) + SENDIDLENGTH];
            SRB_IO_CONTROL *p = (SRB_IO_CONTROL *) buffer;
            SENDCMDINPARAMS *pin = (SENDCMDINPARAMS *) (buffer + sizeof (SRB_IO_CONTROL));
            DWORD dummy;
      
            memset (buffer, 0, sizeof (buffer));
            p -> HeaderLength = sizeof (SRB_IO_CONTROL);
            p -> Timeout = 10000;
            p -> Length = SENDIDLENGTH;
            p -> ControlCode = IOCTL_SCSI_MINIPORT_IDENTIFY;
            strncpy ((char *)p->Signature, "SCSIDISK", 8);
     
            pin -> irDriveRegs.bCommandReg = IDE_IDENTIFY_ATA;
            pin -> bDriveNumber = iDrive;

            if (DeviceIoControl (hScsiDriveIOCTL, IOCTL_SCSI_MINIPORT, 
                                 buffer, sizeof (SRB_IO_CONTROL) + sizeof (SENDCMDINPARAMS) - 1,
                                 buffer, sizeof (SRB_IO_CONTROL) + SENDIDLENGTH,
                                 &dummy, NULL))
            {
               SENDCMDOUTPARAMS *pOut = (SENDCMDOUTPARAMS *) (buffer + sizeof (SRB_IO_CONTROL));
               PIDSECTOR pId = (PIDSECTOR) (pOut->bBuffer);
               if (pId->sModelNumber[0])
               {
                  DWORD dwDiskData[256];
                  int ijk = 0;
                  USHORT *pIdSector = (USHORT *) pId;
             
                  for (ijk = 0; ijk < 256; ijk++)
                     dwDiskData[ijk] = pIdSector[ijk];

                  if(SelectDrive (iDrive, dwDiskData))
                  {
                     //SUA_TRACES_DEBUG("(SCSI) Found master fixed iDrive no:" << iDrive);
                        
                     std::string cProductId;
                     ConvertToReadableString(dwDiskData, 27, 46, cProductId);
                     if( !cProductId.empty() )
                     {
                        boost::trim(cProductId);
                        //SUA_TRACES_DEBUG("(SCSI) Found valid model number: " + cProductId);
                        cHddInfo = cProductId;
                     }

                     std::string cSerialNumber;
                     ConvertToReadableString(dwDiskData, 10, 19, cSerialNumber);
                     if( !cSerialNumber.empty())
                     {
                        cHddInfo += "::";  // Separator token

                        boost::trim(cSerialNumber);
                        //SUA_TRACES_DEBUG("(SCSI) Found valid serial number: " + cSerialNumber);
                        cHddInfo += cSerialNumber;
                        bDone = true;
                     }
                  }
               }
            }
            if (bDone)
            {
               break;
            }
         }
         CloseHandle (hScsiDriveIOCTL);
      }
   }

   if ( !bDone)
   {
      //SUA_TRACE_ERROR("Cannot get data for HDD through SCSI mini-port interface!");
   }

   return cHddInfo;
}

// this doesn't necessarily return the "primary master".
// in windows Vista and higher, it returns whatever is "physicaldisk0" which may not be the OS drive.
std::string HDDiskId::GetPrimaryMasterHDDId()
{
   std::string cHddID;
   
   //OSVERSIONINFO tVersion;
   //memset (&tVersion, 0, sizeof (tVersion));
   //tVersion.dwOSVersionInfoSize = sizeof (OSVERSIONINFO);
   //GetVersionEx (&tVersion);

   //if (tVersion.dwPlatformId == VER_PLATFORM_WIN32_NT)
	{
		//only for the accounts with administator rights
		cHddID = GetHddInfoAdminRights();
      if(cHddID.empty())
      {
		   cHddID = GetHddInfoNoRights ();
         if(cHddID.empty())
         {
            //SUA_TRACE_ERROR("Cannot retrieve HDD ID");
            //GOTOFINALLY();
         }
      }
      //SUA_TRACES_DEBUG("HDD Info: " << cHddID);
   }
   //else
   //{
      //SUA_TRACES_ERROR("Does not work on Win9x OS");
   //}
   
   return cHddID;
}

std::vector<std::string> HDDiskId::GetAllHddInfo()
{

   std::string cHddInfo;
   std::vector<std::string> sAllHddInfo;
   if (!sAllHddInfo.empty())
      return sAllHddInfo;
     
   //for Win NT, Win2K and Win XP no admin rights are required
   //to do: selection for master/fixed iDrive
   for (int iDrive = 0; iDrive < 8; iDrive++)
   {
      cHddInfo = "";
      cHddInfo = HDDiskId::GetDriveInfoAdmin(iDrive);
      if (cHddInfo.empty())
      {
         cHddInfo = HDDiskId::GetDriveInfoNonAdmin(iDrive);
      }
      if (!cHddInfo.empty())
      {
         SHA1Str cHash;
         cHash.SetMessage(cHddInfo);
         cHddInfo = cHash.GetDigestStr();

         sAllHddInfo.push_back(cHddInfo);
      }
   }
   if (sAllHddInfo.empty())
   {
      sAllHddInfo = HDDiskId::GetAllHddThroughScsi();
   }
   return sAllHddInfo;

}


std::string HDDiskId::GetDriveInfoAdmin(int iDrive)
{

   //for Win NT, Win2K and Win XP admin rights are required
   //cOutInfo will store serial number concatenated with model number for the first
   //master fixed iDrive found; 
   //to do: if no such a iDrive is found, we could use a master iDrive
   //bool bDone = false;

   //for (int iDrive = 0; iDrive < 8; iDrive++)
   //{
   std::string cHddInfo = "";
   HANDLE hDriveHandle = 0;

   TCHAR tchDriveName [256];
   _stprintf_s(tchDriveName, 256, _T("\\\\.\\PhysicalDrive%d"), iDrive);

   hDriveHandle = CreateFile (
      tchDriveName,
      GENERIC_READ | GENERIC_WRITE, 
      FILE_SHARE_READ | FILE_SHARE_WRITE ,
      NULL,
      OPEN_EXISTING, 
      0, 
      NULL
      );
   if (hDriveHandle == INVALID_HANDLE_VALUE)
   {
      //SUA_TRACES_DEBUG("Cannot open physical iDrive " << iDrive << ", error code: " << GetLastError ())
        return cHddInfo;
   }

   GETVERSIONOUTPARAMS tParams;
   DWORD               dwBytesReturned = 0;

   memset ((void*) &tParams, 0, sizeof(tParams));
   if ( ! DeviceIoControl (
      hDriveHandle, 
      0x00074080,
      NULL, 
      0,
      &tParams,
      sizeof(tParams),
      &dwBytesReturned, NULL) )
   {         
      //SUA_TRACES_ERROR("DFP_GET_VERSION failed for iDrive " << iDrive)
   }
   else
   {
      if (tParams.bIDEDeviceMap > 0)
      {
         BYTE             byIDCmd = 0;   // IDE or ATAPI IDENTIFY cmd
         SENDCMDINPARAMS  tSendCmdInParams;

         // Now, get the ID sector for all IDE devices in the system.
         // here's the trick: 
         //    - for ATAPI device use the ATAPI command;
         //    - anything else the ATA command
         byIDCmd = (tParams.bIDEDeviceMap >> iDrive & 0x10) ? IDE_IDENTIFY_ATAPI : IDE_IDENTIFY_ATA;

         memset (&tSendCmdInParams, 0, sizeof(tSendCmdInParams));

         BYTE byOutCmd [sizeof (SENDCMDOUTPARAMS) + 512 - 1];
         memset (byOutCmd, 0, sizeof(byOutCmd));

         if ( IdentifyDrive (hDriveHandle, 
            &tSendCmdInParams, 
            (PSENDCMDOUTPARAMS)&byOutCmd, 
            (BYTE) byIDCmd,
            (BYTE) iDrive,
            &dwBytesReturned))
         {
            DWORD dwDiskData[256];
            int ijk = 0;
            USHORT *pIdSector = (USHORT *)
               ((PSENDCMDOUTPARAMS) byOutCmd) -> bBuffer;

            for (ijk=0; ijk<256; ijk++)
               dwDiskData[ijk] = pIdSector[ijk];

            //if (SelectDrive(iDrive, dwDiskData))
            //{
            //SUA_TRACES_DEBUG("Found master fixed iDrive no:" << iDrive);

            std::string cProductId;
            ConvertToReadableString(dwDiskData, 27, 46, cProductId);
            if ( !cProductId.empty() )
            {
               boost::trim(cProductId);
               //SUA_TRACES_DEBUG("Found valid model number: " + cProductId);
               cHddInfo = cProductId;
            }

            std::string cSerialNumber;
            ConvertToReadableString(dwDiskData, 10, 19, cSerialNumber);
            if ( !cSerialNumber.empty() )
            {
               cHddInfo += "::";  // Separator token

               boost::trim(cSerialNumber);
               //SUA_TRACES_DEBUG("Found valid serial number: " + cSerialNumber);
               cHddInfo += cSerialNumber;
            }
            //}
         }
      }
   }
   CloseHandle (hDriveHandle);

   return cHddInfo;

}


std::string HDDiskId::GetDriveInfoNonAdmin(int iDrive)
{
   std::string cHddInfo = "";
   HANDLE hDriveHandle = 0;

   TCHAR tchDriveName [256];
   _stprintf_s(tchDriveName, 256, _T("\\\\.\\PhysicalDrive%d"), iDrive);

   hDriveHandle = CreateFile (
      tchDriveName, 
      0,
      FILE_SHARE_READ | FILE_SHARE_WRITE, 
      NULL,
      OPEN_EXISTING, 
      0, 
      NULL
      );
   if (hDriveHandle == INVALID_HANDLE_VALUE)
   {
      //SUA_TRACES_DEBUG("Cannot open physical iDrive " << iDrive << ", error code: " << GetLastError ())
         return cHddInfo;
   }

   STORAGE_PROPERTY_QUERY tQuery;
   DWORD dwBytesReturned = 0;
   char cBuffer [10000];

   memset ((void *) & tQuery, 0, sizeof (tQuery));
   tQuery.PropertyId = StorageDeviceProperty;
   tQuery.QueryType = PropertyStandardQuery;

   memset (cBuffer, 0, sizeof (cBuffer));

   if ( !DeviceIoControl (
      hDriveHandle, 
      IOCTL_STORAGE_QUERY_PROPERTY,
      & tQuery,
      sizeof (tQuery),
      & cBuffer,
      sizeof (cBuffer),
      & dwBytesReturned, 
      NULL) 
      )
   {
      //SUA_TRACES_ERROR("\nDeviceIOControl IOCTL_STORAGE_QUERY_PROPERTY error " << GetLastError ());
   }
   else
   {
      STORAGE_DEVICE_DESCRIPTOR * pDescrip = (STORAGE_DEVICE_DESCRIPTOR *) & cBuffer;

      if (pDescrip->RemovableMedia)
      {
         // If this is a removable media storage, i.e. CD-ROM drive etc. let's continue searching
         //SUA_TRACES_DEBUG(" STORAGE_DEVICE skipping removable media ProductId=" << &cBuffer[pDescrip->ProductIdOffset]);
         return cHddInfo;
      }

      //get the product ID
      std::string cProductId(&cBuffer[pDescrip->ProductIdOffset]);
      if( !cProductId.empty() )
      {
         boost::trim(cProductId);
         //SUA_TRACES_DEBUG("Non-admin got productID: " + cProductId);
         cHddInfo += cProductId;
      }

      //get the serial number            
      std::string cSerialNumber = ConvertHexBytesToString(&cBuffer[pDescrip->SerialNumberOffset]);
      if( !cSerialNumber.empty() )
      {
         cHddInfo += "::"; // Separator token

         boost::trim(cSerialNumber);
         //SUA_TRACES_DEBUG("Non-admin getting serialNo: " + cSerialNumber);
         cHddInfo += cSerialNumber;
      }
   }
   CloseHandle (hDriveHandle);
   return cHddInfo;
}

std::vector<std::string> HDDiskId::GetAllHddThroughScsi()
{
   // This is a 'backdoor' to get information, if everything else fails, access the drive as a SCSI device
   std::string cHddInfo;   
   SHA1Str cHash;
   std::vector<std::string> allHdds;
      
   for (int controller = 0; controller < 16; controller++)
   {
      HANDLE hScsiDriveIOCTL = 0;

      TCHAR  tchDriveName [256];
      _stprintf_s(tchDriveName, 256, _T("\\\\.\\Scsi%d:"), controller);

      //  Windows NT, Windows 2000, any rights should do
      hScsiDriveIOCTL = CreateFile (tchDriveName,
         GENERIC_READ | GENERIC_WRITE, 
         FILE_SHARE_READ | FILE_SHARE_WRITE, 
         NULL,
         OPEN_EXISTING, 
         0, 
         NULL
         );

      if (hScsiDriveIOCTL != INVALID_HANDLE_VALUE)
      {
         for (int iDrive = 0; iDrive < 2; iDrive++)
         {
            char buffer [sizeof(SRB_IO_CONTROL) + SENDIDLENGTH];
            SRB_IO_CONTROL *p = (SRB_IO_CONTROL *) buffer;
            SENDCMDINPARAMS *pin = (SENDCMDINPARAMS *) (buffer + sizeof (SRB_IO_CONTROL));
            DWORD dummy;

            memset (buffer, 0, sizeof (buffer));
            p -> HeaderLength = sizeof (SRB_IO_CONTROL);
            p -> Timeout = 10000;
            p -> Length = SENDIDLENGTH;
            p -> ControlCode = IOCTL_SCSI_MINIPORT_IDENTIFY;
            strncpy ((char *)p->Signature, "SCSIDISK", 8);

            pin -> irDriveRegs.bCommandReg = IDE_IDENTIFY_ATA;
            pin -> bDriveNumber = iDrive;

            if (DeviceIoControl (hScsiDriveIOCTL, IOCTL_SCSI_MINIPORT, 
               buffer, sizeof (SRB_IO_CONTROL) + sizeof (SENDCMDINPARAMS) - 1,
               buffer, sizeof (SRB_IO_CONTROL) + SENDIDLENGTH,
               &dummy, NULL))
            {
               SENDCMDOUTPARAMS *pOut = (SENDCMDOUTPARAMS *) (buffer + sizeof (SRB_IO_CONTROL));
               PIDSECTOR pId = (PIDSECTOR) (pOut->bBuffer);
               if (pId->sModelNumber[0])
               {
                  DWORD dwDiskData[256];
                  int ijk = 0;
                  USHORT *pIdSector = (USHORT *) pId;

                  for (ijk = 0; ijk < 256; ijk++)
                     dwDiskData[ijk] = pIdSector[ijk];


                  ////SUA_TRACES_DEBUG("(SCSI) Found master fixed iDrive no:" << iDrive);

                  std::string cProductId;
                  ConvertToReadableString(dwDiskData, 27, 46, cProductId);
                  if( !cProductId.empty() )
                  {  
                     boost::trim(cProductId);
                     ////SUA_TRACES_DEBUG("(SCSI) Found valid model number: " + cProductId);
                     cHddInfo = cProductId;
                  }

                  std::string cSerialNumber;
                  ConvertToReadableString(dwDiskData, 10, 19, cSerialNumber);
                  if( !cSerialNumber.empty() )
                  {
                     cHddInfo += "::";  // Separator token

                     boost::trim(cSerialNumber);
                     ////SUA_TRACES_DEBUG("(SCSI) Found valid serial number: " + cSerialNumber);
                     cHddInfo += cSerialNumber;
                  }
                  if (!cHddInfo.empty())
                  {  
                     cHash.SetMessage(cHddInfo);
                     cHddInfo = cHash.GetDigestStr();
                     allHdds.push_back(cHddInfo);
                     //xmlTextWriterWriteElement   (writer, XMLCHAR_CAST("hdd"),   XMLCHAR_CAST(cHddInfo.c_str()));
                  }
               }
            }

         }
         CloseHandle (hScsiDriveIOCTL);
      }
   }   

   if ( cHddInfo.empty())
   {
      //SUA_TRACE_ERROR("Cannot get data for HDD through SCSI mini-port interface!");
   }

   return allHdds;
}

}
#endif //WIN32
