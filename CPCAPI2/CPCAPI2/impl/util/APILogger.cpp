#include "APILogger.h"

// For the time being use the resip logger :-P
#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>
#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUBLIC_API

// Logging function moved here purely to avoid resip logging
// macro redefinition(s)
void CPCAPI2_PublicAPILog( const char *format, ... )
{
   char _buf[ API_BUFSIZ ];

   va_list args;
   va_start( args, format );
   vsnprintf( _buf, API_BUFSIZ, format, args );
   _buf[ API_BUFSIZ - 1 ] = '\0';
   va_end( args );
   InfoLog( << _buf );
}
