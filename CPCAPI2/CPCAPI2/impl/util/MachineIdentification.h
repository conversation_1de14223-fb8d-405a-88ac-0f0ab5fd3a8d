#pragma once

#if !defined(CPCAPI2_MACHINE_IDENTIFICATION)
#define CPCAPI2_MACHINE_IDENTIFICATION

#include "cpcapi2defs.h"

#include <string>
#include <vector>

namespace CPCAPI2
{
class CPCAPI2_SHAREDLIBRARY_API MachineIdentification
{
public:
   static std::string GetHardwareId();
   static std::string ComputerName();
#if _WIN32
   static std::vector<std::string> GetAllMACs_Win32(bool hash = true);
#endif
   static std::string GetLocalMACAddress();
   static std::string GetIPAddress();
   static std::string GetMotherboardInfo();
   static std::string GetHarddiskPnpId();
#if defined(__linux__) && !defined(ANDROID)
    static std::vector<std::string> GetAllLinuxMACs();
#endif
};

}
#endif // CPCAPI2_MACHINE_IDENTIFICATION