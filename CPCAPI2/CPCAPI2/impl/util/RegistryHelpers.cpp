#include "RegistryHelpers.h"
#include "brand_branded.h"

#if defined(WIN32) && !defined(NO_REGISTRY)

#include "RegUtils.h"

namespace CPCAPI2
{
   std::string RegistryHelpers::RegistryLocation()
   {
      std::string location;
      std::string company((LPCSTR)CPCAPI2_BRAND_COMPANY_SHORT_NAME);
      std::string product(CPCAPI2_BRAND_PRODUCT_NAME);
      location = "SOFTWARE\\" + company + "\\" + product + "\\" + "Data";
      return location;
   }

   bool RegistryHelpers::WriteHKLMStringKey(std::string location, std::string value)
   {
      return RegUtils::SetStringValue(
         HKEY_LOCAL_MACHINE,
         LPCSTR(RegistryLocation().c_str()),
         LPCSTR(location.c_str()),
         LPCSTR(value.c_str())
         );
   }
   bool RegistryHelpers::WriteHKCUStringKey(std::string location, std::string value)
   {
      return RegUtils::SetStringValue(
         HKEY_CURRENT_USER,
         LPCSTR(RegistryLocation().c_str()),
         LPCSTR(location.c_str()),
         LPCSTR(value.c_str())
         );
   }

   std::string RegistryHelpers::ReadRegistryStringValue(std::string location)
   {
      std::string value;
      RegUtils::QueryStringValue(HKEY_LOCAL_MACHINE,
         LPCSTR(RegistryLocation().c_str()),
         location.c_str(),
         value);
      if (value.empty())
      {
         RegUtils::QueryStringValue(HKEY_CURRENT_USER,
            LPCSTR(RegistryLocation().c_str()),
            location.c_str(),
            value);
      }
      return value;
   }

   bool RegistryHelpers::WriteRegistryStringValue(std::string location, std::string value)
   {
      WriteHKLMStringKey(location, value);
      std::string hklmValue;
      RegUtils::QueryStringValue(HKEY_LOCAL_MACHINE,
         LPCSTR(RegistryLocation().c_str()),
         location.c_str(),
         hklmValue);
      if (hklmValue.empty())
      {
         WriteHKCUStringKey(location, value);
         std::string hkcuValue;
         RegUtils::QueryStringValue(HKEY_CURRENT_USER,
            LPCSTR(RegistryLocation().c_str()),
            location.c_str(),
            hkcuValue);
         if (hkcuValue.empty())
         {
            return false;
         }
         else
         {
            return true;
         }
      }
      else
      {
         return true;
      }
   }

   bool RegistryHelpers::WriteHardwareToRegistry(std::string keynamePrefix, const std::vector<std::string>& hardware)
   {
      bool success = false;
      for(unsigned int i = 0; i < hardware.size(); i++)
      {
         char number[2];
         itoa(i, number, 10);
         std::string keyname = keynamePrefix + number;
         success = WriteRegistryStringValue(keyname, hardware[i].c_str());
      }

      return success;
   }

   // Check that newly retrieved hardware ID's match with existing saved ones
   bool RegistryHelpers::VerifyHardware( const std::vector<std::string>& hdds, const std::vector<std::string>& macs)
   {
      // get the stored drives in the registry.
      std::vector<std::string> storedMacs;
      std::vector<std::string> storedHdds;
      int match = 0;

      int numDevices = hdds.size() + macs.size();
      int qualifier = numDevices / 3;
      if(qualifier == 0)
      {
         qualifier = 1;
      }

      bool reader = true;
      int i = 0;
      std::string hwstring = "m";
      while(reader)
      {
         char number[2];
         itoa(i, number, 10);
         std::string name = hwstring + number;
         std::string macString;
         macString = ReadRegistryStringValue(name);
         if(!macString.empty())
         {
            storedMacs.push_back(macString);
            i++;
         }
         else
         {
            reader = false;
         }
      }

      for (unsigned int i = 0; i < macs.size(); i++)
      {
         for (unsigned int j = 0; j < storedMacs.size(); j++)
         {
            if (storedMacs[j] == macs[i])
            {
               match++;
            }
         }
      }

      // matched already?
      if (match >= qualifier)
      {
         return true;
      }

      reader = true;
      i = 0;
      hwstring = "h";
      while(reader)
      {
         char number[2];
         itoa(i, number, 10);
         std::string name = hwstring + number;
         std::string hddstring;
         hddstring = ReadRegistryStringValue(name);
         if(!hddstring.empty())
         {
            storedHdds.push_back(hddstring);
            i++;
         }
         else
         {
            reader = false;
         }
      }

      for (unsigned int i = 0; i < hdds.size(); i++)
      {
         for (unsigned int j = 0; j < storedHdds.size(); j++)
         {
            if (storedHdds[j] == hdds[i])
            {
               match++;
            }
         }
      }

      if (match >= qualifier)
      {
         return true;
      }

      return false;
   }
}
#endif
