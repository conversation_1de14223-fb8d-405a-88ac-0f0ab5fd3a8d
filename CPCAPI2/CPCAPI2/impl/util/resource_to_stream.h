#pragma once
#if _WIN32
#if WINAPI_FAMILY_ONE_PARTITION( WINAPI_FAMILY_DESKTOP_APP, WINAPI_PARTITION_APP )
#include <common_types.h>
#include <string>
#include <ppltasks.h>
#include <wrl.h>
#include <robuffer.h>

namespace CPCAPI2
{
   class ResourceInStream : public webrtc::InStream
   {
   public:
      ResourceInStream(const cpc::string& resourceUri, bool repeat) : mUri(resourceUri), mRepeat(repeat), mDataReader(nullptr), mReady(false) {}
      virtual ~ResourceInStream() {}

      static bool IsResource(const cpc::string& resourceUri) { return resourceUri.find(L"ms-appx:") == 0; }

      void Init()
      {
         mReady = true;

         try
         {
            Windows::Foundation::Uri^ uri = ref new Windows::Foundation::Uri(ref new Platform::String(mUri.c_str()));
            Concurrency::create_task(Windows::Storage::StorageFile::GetFileFromApplicationUriAsync(uri)).then([this](Concurrency::task<Windows::Storage::StorageFile^> t)
            {
               try
               {
                  auto storageFile = t.get();
                  auto f = storageFile->FileType;
                  Concurrency::create_task(storageFile->OpenAsync(Windows::Storage::FileAccessMode::Read)).then([this, storageFile](Concurrency::task<Windows::Storage::Streams::IRandomAccessStream^> task) 
                  {
                     try
                     {
                        Windows::Storage::Streams::IRandomAccessStream^ readStream = task.get(); 
                        mDataReader = ref new Windows::Storage::Streams::DataReader(readStream); 
                        Concurrency::create_task(mDataReader->LoadAsync(static_cast<UINT32>(readStream->Size))).then([](unsigned int numBytesLoaded) 
                        { 
                        }).get();
                     }
                     catch (Platform::COMException^ exc2)
                     {
                        mReady = false;
                     }
                  }).get();
               }
               catch (Platform::COMException^ exc1)
               {
                  mReady = false;
               }
            }).get();
         }
         catch (Platform::COMException^ exc3)
         {
            mReady = false;
         }
      }

      virtual int Rewind()
      {
         if (!mRepeat)
         {
            return -1;
         }
         Init();
         return 0;
      }

      virtual int Read(void *buf,int len)
      {
         if (!mReady)
         {
            return 0;
         }

         Windows::Storage::Streams::IBuffer^ buff = mDataReader->ReadBuffer(len);
         
         Microsoft::WRL::ComPtr<Windows::Storage::Streams::IBufferByteAccess> byteBuffer;

         Microsoft::WRL::ComPtr<IUnknown> comBuffer((IUnknown*)buff);
         comBuffer.As(&byteBuffer);
         byte* rawBytes = NULL;
         if (byteBuffer->Buffer(&rawBytes) == S_OK)
         {
            memcpy(buf, rawBytes, buff->Length);
         }

         //OutputDebugString(L"unconsumed: ");
         //wchar_t uncons[512];
         //_itow(mDataReader->UnconsumedBufferLength, uncons, 10);
         //OutputDebugString(uncons);
         //OutputDebugString(L"\r\n");
         return buff->Length;
      }

   private:
      cpc::string mUri;
      bool mRepeat;
      Windows::Storage::Streams::DataReader^ mDataReader;
      bool mReady;
   };
}
#endif
#endif

