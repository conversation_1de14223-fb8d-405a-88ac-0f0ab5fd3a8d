#include "TimeUtils.h"

#include <cstddef>

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

using namespace CPCAPI2;

/**
 * Return the number of milliseconds since the UNIX epoch (Jan 1st, 1970).
 * NB: Win32 epoch is not the same.
 */
uint64_t TimeUtils::millisSinceUnixEpoch()
{
   uint64_t result(0);
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = 11644473600000 * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = ((now.tv_sec) * 1000ull) + ((now.tv_usec) / 1000ull);
#endif
   return result;
}
