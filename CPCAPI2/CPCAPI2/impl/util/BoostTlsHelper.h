#ifndef _BOOST_TLS_HELPER_H_
#define _BOOST_TLS_HELPER_H_

#include <phone/SslCipherOptions.h>

#include "resip/stack/ssl/SecurityHelper.hxx"

#include <boost/asio/ssl/context.hpp>

#include <memory>

namespace CPCAPI2
{
  std::shared_ptr<boost::asio::ssl::context> initializeBoostTlsContext(const CPCAPI2::TLSVersion& version, const std::string& cipherSuite, const resip::SecurityTypes::TLSMode& mode = resip::SecurityTypes::TLSMode_TLS, const std::string& DHParamsFile = "");
}

#endif // _BOOST_TLS_HELPER_H_
