#pragma once

#if !defined(CPCAPI2_RESIP_CONV)
#define CPCAPI2_RESIP_CONV

#include "cpcapi2defs.h"
#include "cpcstl/string.h"
#include <cpcapi2utils.h>
#include "resip/stack/NameAddr.hxx" 
#include "resip/stack/Uri.hxx"

namespace CPCAPI2
{

class ResipConv
{
public:

   /** attempt to parse a wstring to a sip: address
      * Returns true or false for success or failure parsing.
      * This is prefered to using Uri::parse because it does not throw ParseException on failure */
   static bool stringToAddr(const cpc::string& stringAddr, resip::NameAddr& sipAddr);

   /** attempt to parse a wstring to a sip: uri
      * Returns true or false for success or failure parsing.
      * This is prefered to using Uri::parse because it does not throw ParseException on failure */
   static bool stringToUri(const cpc::string& stringUri, resip::Uri& sipUri);
};

}
#endif // CPCAPI2_RESIP_CONV