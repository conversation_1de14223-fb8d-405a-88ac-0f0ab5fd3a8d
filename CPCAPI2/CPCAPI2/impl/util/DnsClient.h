#ifndef DnsClient_hpp
#define DnsClient_hpp

#include <cpcstl/string.h>
#include <rutil/dns/DnsStub.hxx>
#include <rutil/dns/DnsSrvRecord.hxx>
#include <resip/stack/Tuple.hxx>
#include <resip/stack/UdpTransport.hxx>
#include <rutil/FdSetIOObserver.hxx>
#include <rutil/Reactor.hxx>
#include "account/SipAccountSettings.h"

#define CPCAPI2_DNS_TIMEOUT 5

namespace CPCAPI2
{

namespace Utils
{

   struct DnsNaptrRecord
   {
      resip::Data target;
   };

   struct DnsSrvRecord
   {
      resip::Data target;
      int port;
   };

   struct DnsAorAAAARecord
   {
      bool valid;
      resip::GenericIPAddress ipAddr;
   };

   class DnsClient : public resip::DnsResultSink
   {

   public:

      DnsClient(const resip::DnsStub::DnsSettings& dnsSettings);
      ~DnsClient();

      DnsNaptrRecord getDnsNaptrRecord(int serviceType, const cpc::string& domainName);
      DnsSrvRecord getDnsSrvRecord(int serviceType, const cpc::string& domainName);
      int getDnsSrvRecords(int serviceType, const cpc::string& domainName, cpc::vector<DnsSrvRecord>& results);
      DnsAorAAAARecord getDnsAorAAAARecord(const resip::Data& target, SipAccount::IpVersion ipVersion, float timeoutSec = 5.0f, bool anyResult = false);
      size_t getAllDnsRecordsP(const resip::Data& target, std::vector<resip::Data>& result);
      bool probeDns(const resip::Data& target, SipAccount::IpVersion ipVersion, resip::DnsStub::DnsSettings& settings);

   private:

      std::vector<resip::DnsNaptrRecord> mDnsNaptrRecords;
      std::vector<resip::DnsSrvRecord> mDnsSrvRecords;
      std::vector<resip::DnsHostRecord> mDnsHostARecords;
      std::vector<resip::DnsAAAARecord> mDnsHostAAAARecords;

      bool mOnDnsResultHostACalled;
      bool mOnDnsResultHostAAAACalled;
      bool mOnDnsResultSrvCalled;
      bool mOnDnsResultNaptrCalled;
      const resip::DnsStub::DnsSettings mDnsSettings;
      resip::DnsStub* mDnsStub;

      bool hasResponse(SipAccount::IpVersion ipVersion);
      // DnsResultSink
      virtual void onDnsResult(const resip::DNSResult<resip::DnsHostRecord>&);
      virtual void onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>&);
      virtual void onDnsResult(const resip::DNSResult<resip::DnsAAAARecord>&);
      virtual void onDnsResult(const resip::DNSResult<resip::DnsNaptrRecord>&);
      virtual void onDnsResult(const resip::DNSResult<resip::DnsCnameRecord>&) {}
   };

} // namespace Utils

} // namespace CPCAPI2


#endif /* DnsClient_hpp */
