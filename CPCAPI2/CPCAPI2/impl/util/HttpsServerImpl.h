#pragma once

#if !defined(CPCAPI2_HTTPS_SERVER_H)
#define CPCAPI2_HTTPS_SERVER_H

#include "BoostTlsHelper.h"
#include "phone/SslCipherOptions.h"

#include <string>

// Simple-Http-Server
#include <server_https.hpp>

namespace CPCAPI2
{
  class HttpsServerImpl : public SimpleWeb::Server<SimpleWeb::HTTPS>
  {
  public:
    HttpsServerImpl(const SslCipherOptions &tlsSettings, const std::string &cert_file, const std::string &private_key_file, const std::string &dh_params_file = std::string(), const std::string &verify_file = std::string()) :
        SimpleWeb::Server<SimpleWeb::HTTPS>(cert_file, private_key_file, verify_file)
    {
      resip::SecurityHelper::configureSslContext(context.native_handle(), (resip::SecurityTypes::SSLType)tlsSettings.getTLSVersion(SslCipherUsageWebSockets), tlsSettings.getCiphers(SslCipherUsageWebSockets).c_str(), resip::SecurityTypes::TLSMode_TLS_Server, dh_params_file);
    }
  };
}

#endif // CPCAPI2_HTTPS_SERVER_H
