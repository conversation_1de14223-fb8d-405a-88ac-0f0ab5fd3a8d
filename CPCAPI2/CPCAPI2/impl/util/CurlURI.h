#ifndef _CURL_URI_H_
#define _CURL_URI_H_


#include <string>
#include <cstdlib>

#ifdef BB10
#include <stdlib.h>
#endif //BB10

namespace xten
{


class CurlURI
{
public:
   CurlURI();
   CurlURI(const CurlURI&);
   CurlURI(const std::string&);
   virtual ~CurlURI() {};

   bool valid() { return mValid; }
   const std::string& scheme() { return mScheme; }
   const std::string& host() { return mHost; }
   const std::string& path() { return mPath; }
   const std::string& authinfo() { return mAuthinfo; }
   const std::string& uri() { return mUri; }
   unsigned int port() { return atoi(mPort.c_str()); }


private:
   std::string mUri;
   std::string mHost;
   std::string mPath;
   std::string mScheme;
   std::string mAuthinfo;
   std::string mPort;

   bool mValid;
};


}


#endif // _CURL_URI_H_
