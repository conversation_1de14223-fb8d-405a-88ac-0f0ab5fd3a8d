#include "ProxyHelper.h"

#if defined( __APPLE__ )

#include "cpc_logger.h"

#include <CFNetwork/CFNetwork.h>
#include <CoreFoundation/CoreFoundation.h>
#include <SystemConfiguration/SystemConfiguration.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

namespace CPCAPI2
{

static void ResultCallback(void * client, CFArrayRef proxies, CFErrorRef error)
// Callback for CFNetworkExecuteProxyAutoConfigurationURL.  client is a
// pointer to a CFTypeRef.  This stashes either error or proxies in that
// location.
{
   CFTypeRef *	resultPtr;
   
   resultPtr = (CFTypeRef *) client;
   
   if (error != NULL)
      *resultPtr = ::CFRetain(error);
   else
      *resultPtr = ::CFRetain(proxies);
   ::CFRunLoopStop(::CFRunLoopGetCurrent());
}
   
cpc::string ProxyHelper::GetProxyServerInfo(const cpc::string& destination, cpc::string &bypassList)
{
   cpc::string result = "";
   CFStringRef targetStr = NULL;
   CFURLRef targetUrl = NULL;
   CFDictionaryRef proxyDict = NULL;
   CFArrayRef proxiesArr = NULL;
   CFDictionaryRef bestProxy = NULL;
   
   targetStr = ::CFStringCreateWithCString(NULL, destination.c_str(), kCFStringEncodingUTF8);
   if (!targetStr)
      goto done;
   
   targetUrl = ::CFURLCreateWithString(kCFAllocatorDefault, targetStr, NULL);
   if (!targetUrl)
      goto done;
   
   proxyDict = ::CFNetworkCopySystemProxySettings();
   if(!proxyDict)
   {
      WarningLog(<< "Can't retrieve key-value pairs that represent the current internet proxy settings");
      goto done;
   }
   
   // retrieve list of proxies in order of priority for the given URL
   proxiesArr = ::CFNetworkCopyProxiesForURL(targetUrl, proxyDict);
   if (!proxiesArr || ::CFArrayGetCount(proxiesArr) == 0)
   {
      WarningLog(<< "Can't retrieve list of proxies or no proxy configured.");
      goto done;
   }
   
   // we're only going to try using the first one
   bestProxy = (CFDictionaryRef) ::CFArrayGetValueAtIndex(proxiesArr, 0);
   if (bestProxy)
   {
      CFStringRef proxyType = (CFStringRef) ::CFDictionaryGetValue(bestProxy, kCFProxyTypeKey);
      if (proxyType)
      {
         if (::CFEqual(proxyType, kCFProxyTypeNone))
            goto done;
         
         if (::CFEqual(proxyType, kCFProxyTypeAutoConfigurationURL))
         {
            DebugLog(<< "Using auto configuration script to determine web proxy");
            
            // auto configuration script (pac)
            CFURLRef scriptURL = (CFURLRef) ::CFDictionaryGetValue(bestProxy, kCFProxyAutoConfigurationURLKey);
            if (scriptURL)
            {
               CFTypeRef res;
               CFStreamClientContext context = { 0, &res, NULL, NULL, NULL };
               
               // Work around Apple bug.  This dummy call to
               // CFNetworkCopyProxiesForURL initialise some state within CFNetwork
               // that is required by CFNetworkCopyProxiesForAutoConfigurationScript.

#pragma clang diagnostic push
#pragma GCC diagnostic ignored "-Wnonnull"
               ::CFRelease(::CFNetworkCopyProxiesForURL(targetUrl, NULL));
#pragma clang diagnostic pop           
               
               CFRunLoopSourceRef rls = ::CFNetworkExecuteProxyAutoConfigurationURL(scriptURL, targetUrl, ResultCallback, &context);
               
               if (rls)
               {
                  ::CFRunLoopAddSource(::CFRunLoopGetCurrent(), rls, kCFRunLoopDefaultMode);
                  
                  ::CFRunLoopRunInMode(kCFRunLoopDefaultMode, 1.0e10, false);
                  
                  ::CFRunLoopRemoveSource(::CFRunLoopGetCurrent(), rls, kCFRunLoopDefaultMode);
                  
                  ::CFRelease(rls);
                  
                  if (res && CFGetTypeID(res) == CFArrayGetTypeID() && ::CFArrayGetCount((CFArrayRef)res) > 0)
                  {
                     bestProxy = (CFDictionaryRef) ::CFArrayGetValueAtIndex((CFArrayRef)res, 0);
                     if (bestProxy != NULL)
                        proxyType = (CFStringRef) ::CFDictionaryGetValue(bestProxy, kCFProxyTypeKey);
                  }
               }
            }
         }
         
         if (bestProxy != NULL && proxyType != NULL &&
             (::CFEqual(proxyType, kCFProxyTypeHTTP) || ::CFEqual(proxyType,kCFProxyTypeHTTPS)))
         {
            CFStringRef hostStr = (CFStringRef) ::CFDictionaryGetValue(bestProxy, kCFProxyHostNameKey);
            
            if (hostStr != NULL)
            {
               CFNumberRef portNum = (CFNumberRef) ::CFDictionaryGetValue(bestProxy, kCFProxyPortNumberKey );
               
               if (portNum != NULL)
               {
                  CFStringRef fullHost = ::CFStringCreateWithFormat( NULL, NULL, CFSTR("%@:%@"), hostStr, portNum);
                  if (fullHost != NULL)
                  {
                     char hostAsCStr[2048]; // 2K is a good upper limit
                     ::CFStringGetCString(fullHost, hostAsCStr,
                                        (CFIndex) 2048, kCFStringEncodingASCII);
                     result = hostAsCStr;
                     ::CFRelease(fullHost);
                  }
               }
            }
         }
      }
   }
   
   
done:
   
   if (targetStr) ::CFRelease(targetStr);
   if (targetUrl) ::CFRelease(targetUrl);
   if (proxiesArr) ::CFRelease(proxiesArr);
   if (proxyDict) ::CFRelease(proxyDict);
   
   return result;
   
   
}

}

#endif // defined( __APPLE__ )
