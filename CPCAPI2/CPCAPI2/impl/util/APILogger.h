#pragma once
#ifndef __CPCAPI2_APILOGGER_H__
#define __CPCAPI2_APILOGGER_H__

#include <stdint.h>
#include <stdarg.h>
#include <string.h>
#include <sstream>
#include <thread>

#define API_LOGS   1
#define API_BUFSIZ 2048

/* Disable warnings about nonliteral format string */
#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wformat-nonliteral"
#endif

/* Formatting tricks to remove path component of __FILE__ macro */
#ifdef _WIN32
#define APILOG_FILE (strrchr(__FILE__, '\\') ? strrchr(__FILE__, '\\') + 1 : __FILE__)
#else
#define APILOG_FILE (strrchr(__FILE__, '/') ? strrchr(__FILE__, '/') + 1 : __FILE__)
#endif


extern void CPCAPI2_PublicAPILog( const char *format, ... );

#ifndef NO_API_LOGS
#define API_INVOKE( FORMAT, ... ) { \
   const char *myfmt = "| PUBLIC_API | INVOKE | %s | %s (%d) | %s | %s"; \
   char _buf[ API_BUFSIZ ]; \
   std::stringstream ss; \
   ss << std::this_thread::get_id(); \
   snprintf( _buf, API_BUFSIZ, myfmt, ss.str().c_str(), APILOG_FILE, __LINE__, __FUNCTION__, FORMAT ); \
   CPCAPI2_PublicAPILog( _buf, ##__VA_ARGS__ ); \
}
#define API_EVENT( EVENT_NAME, FORMAT, ... ) { \
   const char *myfmt = "| PUBLIC_API | EVENT  | %s | %s (%d) | %s | %s"; \
   char _buf[ API_BUFSIZ ]; \
   std::stringstream ss; \
   ss << std::this_thread::get_id(); \
   snprintf( _buf, API_BUFSIZ, myfmt, ss.str().c_str(), APILOG_FILE, __LINE__, EVENT_NAME, FORMAT ); \
   CPCAPI2_PublicAPILog( _buf, ##__VA_ARGS__ ); \
}
#else
#define API_INVOKE( ... ) {}
#define API_EVENT( ... ) {}
#endif /* API_LOGS */

#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif
#endif //__CPCAPI2_APILOGGER_H__
