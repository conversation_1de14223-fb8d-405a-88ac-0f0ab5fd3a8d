#ifndef __CPCAPI2_CURLPPHELPER_H__
#define __CPCAPI2_CURLPPHELPER_H__

#include <atomic>

#include <curl/curl.h>
#include <curlpp/cURLpp.hpp>
#include <curlpp/Easy.hpp>
#include <curlpp/Options.hpp>
#include <curlpp/Infos.hpp>

namespace CPCAPI2
{
   /**
    * The goal of this class is not to hide the CurlPP API (rather, it is
    * recommended that both the application and the SDK use the CurlPP API
    * directly). However, the goal of this class is to eliminate a certain
    * amount of boilerplate code in doing so.
    *
    * This class may be used outside of the SDK. therefore it should never
    * depend on internal SDK classes but rather the dependencies should be
    * limited only to libcurl.
    */
   class CurlPPHelper
   {
   public: // methods

      CurlPPHelper( void );
      virtual ~CurlPPHelper( void );

      /**
       * The following methods are convenience wrappers for the SDK/application
       */
      bool setDefaultOptions( curlpp::Easy& request, const std::string& url, const std::string& method = "GET", size_t bodySize = 0 );
      void setProxyOptions( curlpp::Easy& request, std::string proxyAddr, int proxyPort = 0, std::string proxyBypassList = "" );
      void setCookieOptions( curlpp::Easy& request, std::string cookieFile );
      void setAuthOptions( curlpp::Easy& request, const std::string& authUser, const std::string& authPasswd );
      void setCertMismatchOptions( curlpp::Easy& request, bool mismatchAllowed );

      /**
       * NB: this method is for setting a "HARD" timeout, not a "dynamic" one.
       * The difference is that for a 'hard' timeout, the duration of the
       * timeout has to be known or calculated ahead of time, whereas with a
       * dynamic timeout, specified via the CurlPPProgress class, the timer
       * begins when data transfer stalls in the middle of an operation..
       */
      void setTimeoutOption( curlpp::Easy& request, unsigned long timeoutSeconds );

      /**
       * @brief Enables or disables curl debug logging; if enabled is printed to SDK logging
       * 
       * @param request 
       * @param enableDebugLogging 
       */
      void setDebugLoggingOptions( curlpp::Easy & request, bool enableDebugLogging );

   private:
      int debug(curl_infotype, char *, size_t);
   };
}

#endif // __CPCAPI2_CURLPPHELPER_H__
