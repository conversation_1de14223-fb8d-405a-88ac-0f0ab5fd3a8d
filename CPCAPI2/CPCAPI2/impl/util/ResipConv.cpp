#include "ResipConv.h"
#include <resip/stack/ExtensionParameter.hxx>
#include <map>

namespace CPCAPI2
{

bool ResipConv::stringToAddr(const cpc::string& stringAddr, resip::NameAddr& sipAddr)
{
   resip::Uri uri;
   
   if(!stringToUri(stringAddr, uri))
   {
      return false;
   }
   
   // Copy any unknown parameters from the original NameAddr.uri
   std::vector<resip::Data> unknownParamNames;
   std::map<resip::Data, resip::Data> unknownParameters;
   
   unknownParamNames = sipAddr.uri().getUnknownParameterNames();
   std::vector<resip::Data>::iterator it;
   for (it = unknownParamNames.begin(); it != unknownParamNames.end(); it++)
   {
      unknownParameters[*it] = sipAddr.uri().param(resip::ExtensionParameter(*it));
   }
   
   sipAddr = resip::NameAddr(uri);
   
   // put the unknown parameters back
   for (std::vector<resip::Data>::const_iterator it = unknownParamNames.begin(); it != unknownParamNames.end(); it++)
   {
      sipAddr.uri().param(resip::ExtensionParameter(*it)) = unknownParameters[*it];
   }
   return true;
}

bool ResipConv::stringToUri(const cpc::string& stringUri, resip::Uri& sipUri)
{
   try
   {
      sipUri = resip::Uri(stringUri.c_str());
      return true;
   }
   catch (resip::ParseException& e)
   {
      return false;
   }
}


}