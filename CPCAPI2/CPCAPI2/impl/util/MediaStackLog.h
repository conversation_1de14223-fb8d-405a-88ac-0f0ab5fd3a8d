#pragma once

#if !defined(CPCAPI2_LOG_IMPL_H)
#define CPCAPI2_LOG_IMPL_H

#include <rutil/Subsystem.hxx>
#include <webrtc/system_wrappers/interface/trace.h>
#include <webrtc/base/stream.h>

namespace CPCAPI2
{
class MediaStackLog : public webrtc::TraceCallback
{
public:
   MediaStackLog();
   virtual ~MediaStackLog();

   void init();
   void reset();

   // WebRTC trace callback
   virtual void Print(webrtc::TraceLevel level, const char* message, int length);

private:
   bool mInitialized;
   rtc::StreamInterface* mStreamInterface;
};

class MediaStackLog2 : public rtc::NullStream
{
public:
   rtc::StreamResult Write(const void* data,
                      size_t data_len,
                      size_t* written,
                      int* error) override;
};
}

#endif
