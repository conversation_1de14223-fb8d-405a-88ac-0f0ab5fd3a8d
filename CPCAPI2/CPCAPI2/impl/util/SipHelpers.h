#pragma once

#ifndef __CPCAPI2_SIP_HELPERS_H__
#define __CPCAPI2_SIP_HELPERS_H__

#include "cpcapi2types.h"
#include "call/SipConversationTypes.h"
#include <resip/stack/SipMessage.hxx>

namespace CPCAPI2
{

class CPCAPI2_SHAREDLIBRARY_API SipHelpers
{

public:

   static cpc::string getHeader(const resip::SipMessage& msg, const cpc::string& headerName, const cpc::string& fallbackToHeaderName = "");
   static void setHeader(resip::SipMessage& msg, const cpc::string& headerName, const cpc::string& headerValue);
   static DialogId getDialogId(const resip::SipMessage& msg);
   static cpc::string escapeSipString(const cpc::string& input);
   static cpc::string escapeSipUser(const cpc::string& input);
   static cpc::string getFirstAlertInfoOrCallInfo(const resip::SipMessage& msg);
   static cpc::string getAnswerMode(const resip::SipMessage& msg, bool& privileged, bool& autoAnswer, bool& required);
   static cpc::string getFirstWarning(const resip::SipMessage& msg);
   static void populateHistoryInfos(const resip::SipMessage& msg, cpc::vector<HistoryInfo>& histInfo);
   static void populatePCalledPartyId(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName);
   static void populatePAssertedIdentities(const resip::SipMessage& msg, cpc::vector<cpc::string>& identities);
   static void populateSessionId(const resip::SipMessage& msg, cpc::string& value);
   static void populateReferredBy(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName);
   static void populateDiversion(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName, cpc::string& reason);

private:

   SipHelpers() {}

};

}

#endif // __CPCAPI2_SIP_HELPERS_H__
