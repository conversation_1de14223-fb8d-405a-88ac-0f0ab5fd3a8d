#ifndef LIB_XML_HELPER_H
#define LIB_XML_HELPER_H

#include <string>

inline const std::string xmlString(xmlChar* str)
{
	std::string result;
	if (str) result = (const char*)str;
	xmlFree(str);
	return result;
}


static std::string xmlGetElementText(xmlTextReaderPtr reader)
{
	int ret = 1;
	std::string result("");
	
	if (xmlTextReaderIsEmptyElement(reader)) {
		return result;	
	}
	
	while (ret > 0 && xmlTextReaderNodeType(reader) != XML_READER_TYPE_END_ELEMENT)
	{
		if (xmlTextReaderNodeType(reader) == XML_READER_TYPE_TEXT) {
			result += xmlString(xmlTextReaderValue(reader));
		}
		ret = xmlTextReaderRead(reader);
	}
	
	return result;
}

#endif
