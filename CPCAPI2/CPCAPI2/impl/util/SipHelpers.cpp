#include "SipHelpers.h"
#include "CharEncodingHelper.h"

#include <resip/stack/SipMessage.hxx>
#include <resip/stack/ExtensionHeader.hxx>
#include <resip/stack/ExtensionParameter.hxx>
#include <resip/dum/DialogId.hxx>
#include <boost/algorithm/string.hpp>

namespace CPCAPI2
{

void SipHelpers::setHeader(resip::SipMessage& msg, const cpc::string& headerName, const cpc::string& headerValue)
{
   resip::Headers::Type headerType = resip::Headers::getType(headerName.c_str(), headerName.size());
   if (headerType == resip::Headers::UNKNOWN)
   {
      resip::ExtensionHeader header(headerName);
      msg.header(header).clear();
      msg.header(header).push_back(resip::StringCategory(headerValue.c_str()));
   }
   else
   {
      resip::HeaderFieldValueList hfvl;
      char *newValue = NULL;

      if (headerValue.size() > 0)
      {
         newValue = new char[ headerValue.size() + 1 ]; // new must be used as delete [] will be used later
         memcpy(newValue, headerValue, headerValue.size());
         newValue[headerValue.size()] = '\0';
      }

      hfvl.push_back(newValue, headerValue.size(), true);
      msg.setRawHeader(&hfvl, headerType);
   }
}

cpc::string SipHelpers::getFirstAlertInfoOrCallInfo(const resip::SipMessage& msg)
{
   resip::Data headerStr;
   {
      resip::DataStream ds(headerStr);
      if (msg.exists(resip::h_AlertInfos) && !msg.header(resip::h_AlertInfos).empty())
      {
         msg.header(resip::h_AlertInfos).front().encode(ds);
      }
      else if (msg.exists(resip::h_CallInfos) && !msg.header(resip::h_CallInfos).empty())
      {
         msg.header(resip::h_CallInfos).front().encode(ds);
      }
   }
   return headerStr.c_str();
}

cpc::string SipHelpers::getFirstWarning(const resip::SipMessage& msg)
{
   resip::Data headerStr;
   {
      resip::DataStream ds(headerStr);
      if (msg.exists(resip::h_Warnings) && !msg.header(resip::h_Warnings).empty())
      {
         msg.header(resip::h_Warnings).front().encode(ds);
      }
   }
   return headerStr.c_str();
}

cpc::string SipHelpers::getAnswerMode(const resip::SipMessage& msg, bool& privileged, bool& autoAnswer, bool& required)
{
   resip::Data headerStr;
   {
      privileged = false;
      autoAnswer = false;
      required = false;
      resip::DataStream ds(headerStr);
      if (msg.exists(resip::h_PrivAnswerMode))
      {
         headerStr = msg.header(resip::h_PrivAnswerMode).value();
         privileged = true;
         required = msg.header(resip::h_PrivAnswerMode).exists(resip::p_require);
         autoAnswer = ((msg.header(resip::h_PrivAnswerMode).value() == "Auto") ? true : false);
      }
      else if (msg.exists(resip::h_AnswerMode))
      {
         headerStr = msg.header(resip::h_AnswerMode).value();
         privileged = false;
         required = msg.header(resip::h_AnswerMode).exists(resip::p_require);
         autoAnswer = ((msg.header(resip::h_AnswerMode).value() == "Auto") ? true : false);
      }
   }
   return headerStr.c_str();
}

DialogId SipHelpers::getDialogId(const resip::SipMessage& msg)
{
   DialogId ret;

   resip::DialogId dialogId(msg);
   ret.callId = dialogId.getCallId().c_str();
   ret.localTag = dialogId.getLocalTag().c_str();
   ret.remoteTag = dialogId.getRemoteTag().c_str();

   return ret;
}

cpc::string SipHelpers::escapeSipString(const cpc::string& input)
{
   if (input.empty())
      return input;

   std::string escaped = input.c_str();
   boost::algorithm::replace_all(escaped, "\\", "\\\\");
   boost::algorithm::replace_all(escaped, "\"", "\\\"");

   return cpc::string(escaped.c_str());
}

cpc::string SipHelpers::escapeSipUser(const cpc::string& input)
{
   if (input.empty())
      return input;

   std::string escaped = input.c_str();
   boost::algorithm::replace_all(escaped, "%", "%25");
   boost::algorithm::replace_all(escaped, "@", "%40");
   boost::algorithm::replace_all(escaped, ":", "%3A");

   return cpc::string(escaped.c_str());
}

void SipHelpers::populateHistoryInfos(const resip::SipMessage& msg, cpc::vector<HistoryInfo>& histInfo)
{
   if (msg.exists(resip::h_HistoryInfos))
   {
      const resip::NameAddrs& hinfos = msg.header(resip::h_HistoryInfos);
      for (const resip::NameAddr& hi : hinfos)
      {
         HistoryInfo cpcHi;
         resip::Uri uriNoParams(hi.uri().getAorAsUri());
         cpcHi.remoteAddress = CharEncodingHelper::unEscape(uriNoParams).c_str();
         cpcHi.remoteDisplayName = resip::Data::from(hi.displayName()).c_str();
         if (hi.uri().hasEmbedded())
         {
            if (hi.uri().embedded().exists(resip::h_Reasons) && !hi.uri().embedded().header(resip::h_Reasons).empty())
            {
               for (resip::H_Reasons::Type::const_iterator it = hi.uri().embedded().header(resip::h_Reasons).begin(); it != hi.uri().embedded().header(resip::h_Reasons).end(); ++it)
               {
                  if (it->isWellFormed() && it->value().find("SIP") != resip::Data::npos)
                  {
                     resip::Data reasonTxt;
                     {
                        resip::DataStream ds(reasonTxt);
                        ds << it->value().c_str();

                        if (it->exists(resip::p_cause))
                        {
                           ds << ";cause=" << it->param(resip::p_cause);
                        }

                        if (it->exists(resip::p_text) && !it->param(resip::p_text).empty())
                        {
                           ds << ";text=\"" << it->param(resip::p_text).c_str() << "\"";
                        }
                     }
                     cpcHi.reason = reasonTxt.c_str();
                  }
               }
            }
         }
         histInfo.push_back(cpcHi);
      }
   }
}

void SipHelpers::populatePCalledPartyId(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName)
{
   if (msg.exists(resip::h_PCalledPartyId))
   {
      const resip::NameAddr pCalledPartyId = msg.header(resip::h_PCalledPartyId);
      resip::Uri uriNoParams(pCalledPartyId.uri().getAorAsUri());
      addr = CharEncodingHelper::unEscape(uriNoParams).c_str();
      displayName = resip::Data::from(pCalledPartyId.displayName()).c_str();
   }
}

void SipHelpers::populatePAssertedIdentities(const resip::SipMessage& msg, cpc::vector<cpc::string>& identities)
{
    identities.clear();
    if (msg.exists(resip::h_PAssertedIdentities))
    {
        for (const auto& pAssertedIdentity : msg.header(resip::h_PAssertedIdentities)) {
            const auto uri {pAssertedIdentity.uri()};
            identities.push_back(uri.toString().c_str());
        }
    }
}

void SipHelpers::populateSessionId(const resip::SipMessage& msg, cpc::string& sessionId)
{
   // Session-ID is specified in RFC 7329 but resiprocate does not have explicit support for this
   // RFC (even upstream as of 2023). We could define it in resip/stack/Headers.hxx, but does
   // it really matter?

   resip::ExtensionHeader xh_SessionId("Session-ID");
   if (msg.exists(xh_SessionId))
   {
      const resip::StringCategories& rhdrs = msg.header(xh_SessionId);
      for (const resip::StringCategory& rhdr : rhdrs)
      {
         sessionId = rhdr.value().c_str();
         break;
      }
   }
}

void SipHelpers::populateReferredBy(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName)
{
   if (msg.exists(resip::h_ReferredBy))
   {
      const resip::NameAddr referredBy = msg.header(resip::h_ReferredBy);
      resip::Uri uriNoParams(referredBy.uri().getAorAsUri());
      addr = CharEncodingHelper::unEscape(uriNoParams).c_str();
      displayName = resip::Data::from(referredBy.displayName()).c_str();
   }
}

/*
   Diversion = "Diversion" ":" 1# (name-addr *( ";" diversion_params ))
   diversion-params = diversion-reason | diversion-counter |
                      diversion-limit | diversion-privacy |
                      diversion-screen | diversion-extension
   diversion-reason = "reason" "="
                   ( "unknown" | "user-busy" | "no-answer" |
                     "unavailable" | "unconditional" |
                     "time-of-day" | "do-not-disturb" |
                     "deflection" | "follow-me" |
                     "out-of-service" | "away" |
                     token | quoted-string )
   diversion-counter = "counter" "=" 1*2DIGIT
   diversion-limit = "limit" "=" 1*2DIGIT
   diversion-privacy = "privacy" "=" ( "full" | "name" |
                       "uri" | "off" | token | quoted-string )
   diversion-screen = "screen" "=" ( "yes" | "no" | token |
                                        quoted-string )
   diversion-extension = token ["=" (token | quoted-string)]
*/
void SipHelpers::populateDiversion(const resip::SipMessage& msg, cpc::string& addr, cpc::string& displayName, cpc::string& reason)
{
   resip::ExtensionHeader xh_Diversion("Diversion");
   if (msg.exists(xh_Diversion))
   {
      const resip::StringCategories& rhdrs = msg.header(xh_Diversion);
      resip::Data name;
      resip::Data value;
      for (const resip::StringCategory& rhdr : rhdrs)
      {
         resip::ParseBuffer pb(rhdr.value());
         const char* anchor =  pb.skipWhitespace();
         try
         {
            pb.skipToChar(resip::Symbols::RA_QUOTE[0]);
            pb.skipToOneOf(resip::Symbols::SEMI_COLON, resip::ParseBuffer::Whitespace);
            pb.data(value, anchor);
            if(!pb.eof() && *(pb.position()) == resip::Symbols::SEMI_COLON[0])
            {
               pb.skipChar(resip::Symbols::SEMI_COLON[0]);
            }
            
            const resip::NameAddr nameAddr = resip::NameAddr(value, true);
            resip::Uri uriNoParams(nameAddr.uri().getAorAsUri());
            addr = CharEncodingHelper::unEscape(uriNoParams).c_str();
            displayName = resip::Data::from(nameAddr.displayName()).c_str();
         }
         catch (std::exception e)
         {
            if (addr.empty()) addr = rhdr.value().c_str();
         }
         try
         {
            while(!pb.eof())
            {
               anchor = pb.skipWhitespace();

               pb.skipToChar(resip::Symbols::EQUALS[0]);
               pb.data(name, anchor);

               anchor = pb.skipChar(resip::Symbols::EQUALS[0]);
               pb.skipToOneOf(resip::Symbols::SEMI_COLON, resip::ParseBuffer::Whitespace);
               pb.data(value, anchor);
               if (resip::isEqualNoCase(name, "reason"))
               {
                  reason = value.c_str();
                  break;
               }

               if(!pb.eof() && *(pb.position()) == resip::Symbols::SEMI_COLON[0])
               {
                  pb.skipChar(resip::Symbols::SEMI_COLON[0]);
               }

               pb.skipWhitespace();
            }
         }
         catch (std::exception e)
         {
         }
         break;
      }
   }
}

}
