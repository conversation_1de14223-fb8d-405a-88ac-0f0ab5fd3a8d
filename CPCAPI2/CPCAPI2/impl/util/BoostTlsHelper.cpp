#include "BoostTlsHelper.h"

#include "util/LogSubsystems.h"
#include "rutil/Logger.hxx"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PHONE

std::shared_ptr<boost::asio::ssl::context> CPCAPI2::initializeBoostTlsContext(const CPCAPI2::TLSVersion& version, const std::string& cipherSuite, const resip::SecurityTypes::TLSMode& mode, const std::string& DHParamsFile)
{
   if (mode == resip::SecurityTypes::TLSMode_DTLS || mode == resip::SecurityTypes::TLSMode_DTLS_Client || mode == resip::SecurityTypes::TLSMode_DTLS_Server)
   {
      ErrLog(<< "Boost asio does not support DTLS");
      throw std::invalid_argument("Boost asio does not support DTLS");
   }

   std::shared_ptr<boost::asio::ssl::context> ctx;

   switch (version)
   {
      case TLS_V1_0:
      {
         InfoLog(<< "Enabling TLS 1.0");
         switch (mode)
         {
         case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv1); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv1_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv1_server); break;
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
         break;
      }
      case TLS_V1_1:
      {
         InfoLog(<< "Enabling TLS 1.1");
         switch (mode)
         {
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv11); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv11_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv11_server); break;
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
         break;
      }
      case TLS_V1_3:
      {
#if (OPENSSL_VERSION_NUMBER < 0x10100000)
         InfoLog(<< "Cannot enable TLS 1.3 due to old version of OpenSSL. Enabling TLS 1.2");
         FALL_THROUGH;
#else
         InfoLog(<< "Enabling TLS 1.3");
         switch (mode)
         {
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv13); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv13_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv13_server); break;
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
         break;
#endif
      }
      case TLS_V1_2:
      {
         InfoLog(<< "Enabling TLS 1.2");
         switch (mode)
         {
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv12); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv12_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv12_server); break;
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
         break;
      }
      case TLS_HIGHEST:
      {
         InfoLog(<< "Enabling SSL version negotiation - TLS 1.0 and above");
         switch (mode)
         {
#if (OPENSSL_VERSION_NUMBER < 0x10100000)
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23_server); break;
#else
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls_server); break;
#endif
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
        break;
      }
      case TLS_NON_DEPRECATED:
      {
         InfoLog(<< "Enabling SSL version negotiation - TLS 1.2 and above");
         switch (mode)
         {
#if (OPENSSL_VERSION_NUMBER < 0x10100000)
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::sslv23_server); break;
#else
            case resip::SecurityTypes::TLSMode_TLS: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls); break;
            case resip::SecurityTypes::TLSMode_TLS_Client: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls_client); break;
            case resip::SecurityTypes::TLSMode_TLS_Server: ctx = std::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tls_server); break;
#endif
            case resip::SecurityTypes::TLSMode_DTLS: break;
            case resip::SecurityTypes::TLSMode_DTLS_Client: break;
            case resip::SecurityTypes::TLSMode_DTLS_Server: break;
         }
        break;
      }
      default:
      {
         ErrLog(<< "Unsupported SSL version specified: " << version);
         throw std::invalid_argument("Unsupported SecurityTypes::SSLType");
      }
   }

   resip::SecurityHelper::configureSslContext(ctx->native_handle(), (resip::SecurityTypes::SSLType)version, cipherSuite, mode, DHParamsFile);

   return ctx;
}
