#include "AutoResetEvent.h"

namespace CPCAPI2
{
AutoResetEvent::AutoResetEvent(bool initial)
   : flag_(initial)
{
}

void AutoResetEvent::Set()
{
   std::lock_guard<std::mutex> _(protect_);
   flag_ = true;
   signal_.notify_one();
}

void AutoResetEvent::Reset()
{
   std::lock_guard<std::mutex> _(protect_);
   flag_ = false;
}

bool AutoResetEvent::WaitOne(int millisec)
{
   std::unique_lock<std::mutex> lk(protect_);
   while( !flag_ ) // prevent spurious wakeups from doing harm
   {
      if (signal_.wait_for(lk, std::chrono::milliseconds(millisec)) == std::cv_status::timeout)
      {
         return false;
      }
   }
   flag_ = false; // waiting resets the flag
   return true;
}
}