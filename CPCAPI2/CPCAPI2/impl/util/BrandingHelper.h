#pragma once

#if !defined(CPCAPI2_BRANDING_HELPER)
#define CPCAPI2_BRANDING_HELPER

#include "cpcstl/string.h"

namespace CPCAPI2
{

   class BrandingHelper
   {
   public:
      // Convert wilcard string from branding into usable regex:
      static int wildcardDomainToRegEx(const std::string& wildcard, std::string& regEx);
      // Check if domain matches wildcard string
      static bool isDomainWildcardMatch(const cpc::string& domain, const cpc::string& wildcard);
   };
}

#endif
