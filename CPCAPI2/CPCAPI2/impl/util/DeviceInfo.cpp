
#include "DeviceInfo.h"
#include <openssl/sha.h>
#include "cpcapi2utils.h"
#include <cstdio>

#if defined(_WIN32) && !defined(WinRT)
#include <iphlpapi.h>
#endif

#ifdef BB10
#include <bb/device/HardwareInfo>
#endif //BB10

#ifdef ANDROID
#include <stdio.h>
#include "common/Java/JniHelper.h"
#endif //ANDROID

#if defined(__APPLE__)
#include "TargetConditionals.h"
#if !defined(TARGET_OS_IPHONE) || !TARGET_OS_IPHONE
#include <IOKit/IOKitLib.h>
#endif
#endif

#define UUID_LENGTH 16

namespace CPCAPI2
{

bool _instanceIdLoaded = false;
cpc::string _instanceId;

int DeviceInfo::getInstanceId(cpc::string& instanceId)
{
   if (_instanceIdLoaded)
   {
      instanceId = _instanceId;
      return instanceId.size() > 0 ? kSuccess : kError;
   }

   cpc::string input;
   getPlatformUniqueId(input);

#if defined(TARGET_OS_IPHONE) && TARGET_OS_IPHONE
   _instanceId = input;
#else
   unsigned char hash[SHA_DIGEST_LENGTH];
   SHA1((unsigned char*)input.c_str(), input.size(), hash);
   hash[6] = hash[6] & 0x0F;
   hash[6] = hash[6] | 0x50;// Set version. RFC 4122 4.3 version 5 (0101) - SHA1
   hash[8] = hash[8] & 0x3F;
   hash[8] = hash[8] | 0x80;// Set mask. RFC 4122 4.3 set 2 most significant bits to 1 and 0
   char output[UUID_LENGTH * 2 + 4 + 1]; // length + space for dashes (4) + end of string marker
   size_t pos = 0;
   for (size_t i = 0; i < UUID_LENGTH; i++)
    {
        snprintf(output + pos, 3, "%02x", hash[i]);
        pos += 2;
        if ((i==3) || (i==5) || (i==7) || (i==9))
        {
           snprintf(output + pos, 2, "%c", '-');
           pos += 1;
        }
    }
   output[UUID_LENGTH * 2 + 4] = 0;
   _instanceId = output;
#endif
   _instanceIdLoaded = true;
   instanceId = _instanceId;
   return kSuccess;
}


#ifdef ANDROID

bool _androidIdSet = false;
cpc::string _androidId;

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
   if (!_androidIdSet)
   {
     _androidId = CPCAPI2::Jni::CallStaticStringMethod("com/counterpath/sdk/android/Utils", "getDeviceId", "(Landroid/content/Context;)Ljava/lang/String;", CPCAPI2::Jni::GetContext()).c_str();
     _androidIdSet = true;
   }
   id = _androidId;
   return kSuccess;
}

#elif defined(WP8)

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
  Platform::String^ serial = Windows::Phone::System::Analytics::HostInformation::PublisherHostId;

  int length = WideCharToMultiByte(CP_ACP, 0, serial->Begin(), serial->Length(), NULL, 0, 0, 0) + 2;
  LPSTR mbstr = new char[length];
  WideCharToMultiByte(CP_ACP, 0, serial->Begin(), serial->Length(), mbstr, length, 0, 0);

  id.assign(mbstr, length);

  return kSuccess;
}

#elif defined(WinRT)

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
	// We could pass in a nonce and get back a signature to verify that Windows generated the ID.
	Windows::Storage::Streams::IBuffer^ nonce;
	Windows::System::Profile::HardwareToken^ packageSpecificToken = Windows::System::Profile::HardwareIdentification::GetPackageSpecificToken(nonce);
	Windows::Storage::Streams::IBuffer^ hardwareId = packageSpecificToken->Id;

	Windows::Storage::Streams::DataReader^ reader = Windows::Storage::Streams::DataReader::FromBuffer(hardwareId);
  const int stringLength = reader->UnconsumedBufferLength;
  unsigned char* data = new unsigned char[stringLength];
  reader->ReadBytes(::Platform::ArrayReference<unsigned char>(&data[0], stringLength));

  const int newStringLength = (3 * stringLength - 1);
  char* buf = new char[3 * stringLength - 1];
  for (int i = 0; i < stringLength; i++)
    snprintf(&buf[3 * i], 3, "%02X", data[i]);

  for (int i = 2; i < newStringLength; i += 3)
    buf[i] = '-';

  id.assign(buf, newStringLength);

	return kSuccess;
}

#elif defined(_WIN32)

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
   DWORD adaptInfoSize = 0;
	DWORD err = GetAdaptersInfo(NULL, &adaptInfoSize);
   int result = kError;

   // ref: https://github.com/Caloni/Samples/blob/master/GetAdaptersInfo/GetAdaptersInfo.cpp
   // find first valid mac address to use as unique instance id
	if( err == ERROR_BUFFER_OVERFLOW )
	{
		PIP_ADAPTER_INFO adaptInfo = (PIP_ADAPTER_INFO) new BYTE[adaptInfoSize];
		DWORD err = GetAdaptersInfo(adaptInfo, &adaptInfoSize);

		if( err == ERROR_SUCCESS )
		{
			PIP_ADAPTER_INFO currAdaptInfo = adaptInfo;

			while( currAdaptInfo )
			{
				if( currAdaptInfo->Type == MIB_IF_TYPE_ETHERNET )
				{
               char buf[100];

					if( currAdaptInfo->AddressLength == 6 ) // MAC Address
					{
						snprintf(buf, 100, "%02X:%02X:%02X:%02X:%02X:%02X",
							currAdaptInfo->Address[0], currAdaptInfo->Address[1],
							currAdaptInfo->Address[2], currAdaptInfo->Address[3],
							currAdaptInfo->Address[4], currAdaptInfo->Address[5]);
                  id = buf;
                  result = kSuccess;
                  break;
					}
				}
				currAdaptInfo = currAdaptInfo->Next;
			}
		}

		delete [] (PBYTE) adaptInfo;
	}
   return result;
}

#elif defined(BB10)

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
  QString machinePin = bb::device::HardwareInfo().pin();
  id = machinePin.toStdString().c_str();
  return machinePin.isEmpty() ? kError : kSuccess;
}

#elif defined(__THREADX)

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
   // .jza. TODO
   id = "0123456789";
   return kSuccess;
}

#elif defined(__APPLE__)

#if !TARGET_OS_IPHONE
int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
	io_service_t platformExpert = IOServiceGetMatchingService(kIOMasterPortDefault, IOServiceMatching("IOPlatformExpertDevice"));
	if (!platformExpert)
		return 0;

	CFStringRef serialNumberAsCFString = (CFStringRef)IORegistryEntryCreateCFProperty(platformExpert,CFSTR(kIOPlatformUUIDKey),kCFAllocatorDefault, 0);
	IOObjectRelease(platformExpert);

	if (!serialNumberAsCFString)
		return kError;

	id = cpc::string(CFStringGetCStringPtr(serialNumberAsCFString, kCFStringEncodingUTF8));

	CFRelease(serialNumberAsCFString);

	return kSuccess;
}
#endif

#else // Any system not in above list
#warning "getPlatformUniqueId not implemented correctly"

int DeviceInfo::getPlatformUniqueId(cpc::string& id)
{
   // TODO
   id = "1234567890";
   return kSuccess;
}

#endif

int DeviceInfo::getDeviceModel(cpc::string& deviceModel)
{
#ifdef ANDROID
   deviceModel = CPCAPI2::Jni::CallStaticStringMethod("com/counterpath/sdk/android/Utils", "getDeviceModel", "()Ljava/lang/String;", CPCAPI2::Jni::GetContext()).c_str();
#else
   deviceModel = "";
#endif
   return kSuccess;
}

}
