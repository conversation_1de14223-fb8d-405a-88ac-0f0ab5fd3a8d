#pragma once
#ifndef __CPCAPI2_SECUREDNS_H__
#define __CPCAPI2_SECUREDNS_H__

#include <cpcapi2defs.h>
#include <curl/curl.h>
#include <string>

namespace CPCAPI2
{
namespace Utils
{
   class CPCAPI2_SHAREDLIBRARY_API SecureDns
   {
   public:
      static bool isSecureDnsActive();
      static bool doCurloptResolve(const std::string& uri, struct curl_slist *opt);
   };
}
}

#endif // __CPCAPI2_SECUREDNS_H__
