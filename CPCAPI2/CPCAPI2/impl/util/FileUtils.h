#pragma once

#if !defined(CPCAPI2_FILEUTILS_H)
#define CPCAPI2_FILEUTILS_H

/*
General file loading/saving utility class.
*/

namespace CPCAPI2
{

//=============================================================================
// SUMMARY: Instantiated as an utility object that maintains
//          functionality for loading/saving files.
class FileUtils
{
public:
   
   //--------------------------------------------------------------------------
   // SUMMARY: Gets the size of a file
   // RETURNS: The size of the file
   static unsigned long GetFileSize(const char* szInFileName);
   
   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to load a file from a storage device into system memory.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool LoadFileToMemory(
      const char* szInFileName,
      unsigned char * &pOutResult,
      unsigned long *pOutSize,
      unsigned long dwInMaxSize = 0xffffffff
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to load a text-only file from a storage device into
   //          system memory.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool LoadTextFileToMemory(
      const char* szInFileName,
      char * &pFileContents,
      unsigned long *pdwOutSize = 0
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to load a text-only file from a storage device into
   //          system memory. If isEncrypted is true the data is decrypted
   //          using the proveded key.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool LoadTextFileToMemory(
      const char* szInFileName,
      char * &pFileContents,
      bool isEncrypted,
      const char * encryptionKey,
      unsigned long *pdwOutSize = 0
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to save a file from system memory to a storage device.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool SaveMemoryToFile(
      const char* szInFilePath,
      unsigned char * pInData,
      unsigned long ulDataLength
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Set a file to have normal attributes.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool ResetFileAttributes(
      const char*  szInFilePath
   );

   //--------------------------------------------------------------------------
   // SUMMARY: Attempts to save a file from system memory to a storage device.
   //          If shouldEncrypt is true the file is encrypted using the given key.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool SaveMemoryToFile(
      const char* szInFilePath,
      unsigned char * pInData,
      unsigned long ulDataLength,
      bool shouldEncrypt,
      const char * encryptionKey
   );
   

    //--------------------------------------------------------------------------
   // SUMMARY: Attempts to create a new directory on a given storage device.
   // RETURNS: A <C>bool</C> representing whether or not the attempt was successful.
   static bool CreateDir(
      const char* szDir, 
      bool bRecursive
   );

   static void NullPad(unsigned char*& pBuffer, unsigned long ulLength);
   
};

} // namespace sua

#endif
