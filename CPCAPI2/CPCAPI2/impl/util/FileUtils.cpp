#if __APPLE__
#include "TargetConditionals.h"
#endif

#include "FileUtils.h"

#include <cpcstl/string.h>
#include <cpcapi2utils.h>

#include "TextDES.h"
#include <algorithm>
#include <string>
#include <vector>

#if defined(WIN32)
#include <tchar.h>
#include <direct.h>
#include <io.h>
#endif

#include <cpcapi2defs.h>

#ifndef _WIN32
#include <fcntl.h>
#include <unistd.h>
#include <sys/stat.h>
#include <cerrno>
#include <algorithm>
#include <cstring>
#endif

#if __APPLE__
#include <AssertMacros.h>
#endif

namespace CPCAPI2
{

void FileUtils::NullPad(unsigned char*& pBuffer, unsigned long ulLength)
{
   if(ulLength > 0 && pBuffer[ulLength - 1] == '\0')
   return;

   char* pData = new char[ulLength + 1];
   if(pData == NULL)
   return;

   memcpy(pData, pBuffer, ulLength);
   pData[ulLength] = '\0';

   delete [] pBuffer;
   pBuffer = (unsigned char*) pData;
}


bool FileUtils::LoadTextFileToMemory(
   const char* szInFileName,
   char * &pFileContents,
   unsigned long *pdwOutSize
)
{
   unsigned long dwOutSize = 0;

   bool bLoaded = LoadFileToMemory(
      szInFileName,
      (unsigned char*&) pFileContents,
      &dwOutSize,
      0xffffffff
   );

   if(bLoaded == false)
      return false;

   NullPad((unsigned char*&)pFileContents, dwOutSize);

   if (pdwOutSize)
      *pdwOutSize = dwOutSize;

   return bLoaded;
}


bool FileUtils::LoadTextFileToMemory(
   const char* szInFileName,
   char * &pFileContents,
   bool isEncrypted,
   const char * encryptionKey,
   unsigned long *pdwOutSize)
{
   if (!isEncrypted)
   {
      return LoadTextFileToMemory(szInFileName, pFileContents,pdwOutSize);
   }
   else
   {
      unsigned long fileContentSize = 0;
      bool bLoaded = LoadFileToMemory(
         szInFileName,
         (unsigned char*&) pFileContents,
         &fileContentSize,
         0xffffffff
      );
      if(bLoaded == false)
         return false;

      TextDES cDES(TextDES::Mode_Decrypt, encryptionKey);
      cDES.writeRawInput((unsigned char*)pFileContents, fileContentSize);
      cDES.closeRawInput();

      unsigned long decodedSize = cDES.getOutputSize();
      unsigned char * decodedData = new unsigned char[decodedSize];
      bool success = cDES.readProcessedOutput(decodedData, decodedSize);
      //XS_ASSERT(!hasMoreData);
      (void)success;

      NullPad(decodedData, decodedSize);
      if (pdwOutSize)
         *pdwOutSize = decodedSize;
      delete [] pFileContents;
      pFileContents = (char*)decodedData;
      return true;
   }
}

unsigned long FileUtils::GetFileSize(const char* szInFileName)
{
   unsigned long size = 0;

   //   GOTOFINALLY_IF(NULL == szInFileName)
   if (NULL == szInFileName)
      return size;

//#if defined(XS_TARGET_OS_WINDOWS) || defined(XS_TARGET_OS_CE)
#if defined(WinRT)

   long filenameSize = strlen(szInFileName) + 1;
   TCHAR* tFileName = new TCHAR[filenameSize];
   mbstowcs(tFileName, szInFileName, filenameSize);

   HANDLE hFile = ::CreateFile2(tFileName, GENERIC_READ, FILE_SHARE_READ, OPEN_EXISTING, NULL);

   delete[] tFileName;

   if (INVALID_HANDLE_VALUE == hFile || NULL == hFile)
	   return size;

   FILE_STANDARD_INFO fileInfo;
   if (GetFileInformationByHandleEx(hFile, FileStandardInfo, &fileInfo, sizeof(fileInfo)))
	   size = fileInfo.AllocationSize.QuadPart;

   ::CloseHandle(hFile);
#elif defined(WIN32)
#if defined(UNICODE)
   HANDLE hFile = ::CreateFile(
      szInFileName,
      GENERIC_READ,
      FILE_SHARE_READ,
      NULL,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#else
   std::wstring a = cpc::string(szInFileName);
   HANDLE hFile = ::CreateFileW(
      a.c_str(),
      GENERIC_READ,
      FILE_SHARE_READ,
      NULL,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#endif
   //GOTOFINALLY_IF(INVALID_HANDLE_VALUE == hFile || NULL == hFile)
   if (INVALID_HANDLE_VALUE == hFile || NULL == hFile)
      return size;

   size = ::GetFileSize(hFile, NULL);
   ::CloseHandle(hFile);
#else // not XS_TARGET_OS_WINDOWS
    int iFile = open(szInFileName, O_RDONLY);
   //GOTOFINALLY_IF(iFile < 0)
   struct stat tStatBuf;
   if (fstat(iFile, &tStatBuf) < 0)
   {
      size = tStatBuf.st_size;
   }
   close(iFile);
#endif

   return size;
}


bool FileUtils::LoadFileToMemory(
   const char* szInFileName,
   unsigned char * &pOutResult,
   unsigned long *pOutSize,
   unsigned long dwInMaxSize)
{
//#if defined(XS_TARGET_OS_WINDOWS) || defined(XS_TARGET_OS_CE)
#if defined(WIN32)
   bool bReturnResult = false;
   HANDLE hFile = INVALID_HANDLE_VALUE;


   if (NULL != pOutSize)
      *pOutSize = 0;

   //GOTOFINALLY_IF(NULL == szInFileName)
   if (NULL == szInFileName)
      return bReturnResult;

#if defined(WinRT)
   long filenameSize = strlen(szInFileName) + 1;
   TCHAR* tFileName = new TCHAR[filenameSize];
   mbstowcs(tFileName, szInFileName, filenameSize);

   hFile = ::CreateFile2(tFileName, GENERIC_READ, FILE_SHARE_READ, OPEN_EXISTING, NULL);

   delete[] tFileName;
#elif defined(UNICODE)
   hFile = ::CreateFile(
      szInFileName,
      GENERIC_READ,
      FILE_SHARE_READ,
      NULL,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#else
   std::wstring a = cpc::string(szInFileName);
   hFile = ::CreateFileW(
      a.c_str(),
      GENERIC_READ,
      FILE_SHARE_READ,
      NULL,
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#endif

   //GOTOFINALLY_IF(INVALID_HANDLE_VALUE == hFile || NULL == hFile)
   if (INVALID_HANDLE_VALUE == hFile || NULL == hFile)
      return bReturnResult;

#if defined(WinRT)
   unsigned long dwSize = 0;
   FILE_STANDARD_INFO fileInfo;
   if (GetFileInformationByHandleEx(hFile, FileStandardInfo, &fileInfo, sizeof(fileInfo)))
	   dwSize = fileInfo.AllocationSize.QuadPart;
#else
   unsigned long dwSize = ::GetFileSize(hFile, NULL);
#endif

   if (0 != dwInMaxSize)
      dwSize = std::min<unsigned long>(dwSize, dwInMaxSize);

   // heap allocated blocks do not require memset
   pOutResult = new unsigned char[dwSize+1];
   pOutResult[dwSize] = 0;

   unsigned long dwTotalRead = 0;

   while (dwTotalRead < dwSize)
   {
      unsigned long dwReadSize = 0;
      if (!::ReadFile(hFile, (LPVOID)(pOutResult+dwTotalRead), dwSize-dwTotalRead, &dwReadSize, NULL))
      {
         delete pOutResult;
         pOutResult = NULL;
         break;
      }
      if (0 == dwReadSize)
      {
         break;
      }
      dwTotalRead += dwReadSize;
   }
   if (NULL != pOutSize)
      *pOutSize = dwTotalRead;

   if (pOutResult != NULL)
      bReturnResult = true;


   if (INVALID_HANDLE_VALUE != hFile)
   {
      ::CloseHandle(hFile);
      hFile = INVALID_HANDLE_VALUE;
   }
   return  bReturnResult;
#else // XS_TARGET_OS_WINDOWS
   bool bResult = false;
   int iFile = -1;

   //FIRST
   //{
    std::string cFileName = szInFileName;
    iFile = open(cFileName.c_str(), O_RDONLY);

    if (iFile >= 0)
    {
       struct stat tStatBuf;
       if (fstat(iFile, &tStatBuf) < 0)
       {
          close(iFile);
          return false;
       }

       tStatBuf.st_size = std::min((unsigned long)tStatBuf.st_size, dwInMaxSize);

       unsigned char *pBuffer = new unsigned char [tStatBuf.st_size + 1];
       pBuffer[tStatBuf.st_size] = 0;

       if (read(iFile, pBuffer, tStatBuf.st_size) < tStatBuf.st_size)
       {
          delete[] pBuffer;
          //GOTOFINALLY();
           close(iFile);
           return bResult;
       }

       pOutResult = pBuffer;
       *pOutSize = tStatBuf.st_size;
   //}
   //NEXT
   //{
      bResult = true;
    }
   //}
   //FINALLY
   //{
      if (iFile > -1)
         close(iFile);
      return bResult;
   //}
#endif
}


bool FileUtils::SaveMemoryToFile(
   const char*  szInFilePath,
   unsigned char * pInData,
   unsigned long ulDataLength)
{
//#if defined(XS_TARGET_OS_WINDOWS) || defined(XS_TARGET_OS_CE)
#if defined(WIN32)
   bool bSuccess = false;

   if (NULL == szInFilePath || NULL == pInData || 0 == ulDataLength)
      return bSuccess;

#if defined(WinRT)
   long filenameSize = strlen(szInFilePath) + 1;
   TCHAR* tFileName = new TCHAR[filenameSize];
   mbstowcs(tFileName, szInFilePath, filenameSize);

   HANDLE hFile = ::CreateFile2(tFileName, GENERIC_WRITE, 0, CREATE_ALWAYS, NULL);

   delete[] tFileName;
#elif defined(UNICODE)
   HANDLE hFile = CreateFile(
      szInFilePath,
      GENERIC_WRITE,
      0,
      NULL,
      CREATE_ALWAYS,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#else
   std::wstring a = cpc::string(szInFilePath);
   HANDLE hFile = ::CreateFileW(
      a.c_str(),
      GENERIC_WRITE,
      0,
      NULL,
      CREATE_ALWAYS,
      FILE_ATTRIBUTE_NORMAL,
      NULL
   );
#endif
   if (hFile == INVALID_HANDLE_VALUE)
   {
      unsigned long err = ::GetLastError();
   }
   //GOTOFINALLY_IF(hFile == INVALID_HANDLE_VALUE)  // Not much we can do here.
   if (INVALID_HANDLE_VALUE == hFile)
      return bSuccess;

   unsigned long dwOutBytes = 0;
   bSuccess = (WriteFile(
      hFile,
      pInData,
      ulDataLength,
      &dwOutBytes,
      NULL
   ) != 0);
   CloseHandle(hFile);

   if (!bSuccess)
   {
#if defined(UNICODE)
	  long filenameSize = strlen(szInFilePath) + 1;
      TCHAR* tFileName = new TCHAR[filenameSize];
	  mbstowcs(tFileName, szInFilePath, filenameSize);

	  DeleteFile(tFileName);

	  delete[] tFileName;
#else
      std::wstring a = cpc::string(szInFilePath);
      DeleteFileW(a.c_str());
#endif
   }


   return bSuccess;
#else // XS_TARGET_OS_WINDOWS
   std::string cPath = szInFilePath;
   char *path = (char *)cPath.c_str();
   int iFile = open(path, O_WRONLY | O_CREAT | O_TRUNC, 0660);
   if (iFile < 0)
	   return false;

   unsigned long ulResult = write(iFile, pInData, ulDataLength);
   close(iFile);

   return ulResult == ulDataLength;
#endif
}

bool FileUtils::ResetFileAttributes(
   const char*  szInFilePath)
{
//#if defined(XS_TARGET_OS_WINDOWS)
#if defined(WIN32)
#if defined(UNICODE)
	long filenameSize = strlen(szInFilePath) + 1;
	TCHAR* tFileName = new TCHAR[filenameSize];
	mbstowcs(tFileName, szInFilePath, filenameSize);

	bool ret = ::SetFileAttributes(tFileName, FILE_ATTRIBUTE_NORMAL) == TRUE;

	delete[] tFileName;

	return ret;
#else
   std::wstring a = cpc::string(szInFilePath);
   return ::SetFileAttributesW(a.c_str(), FILE_ATTRIBUTE_NORMAL) == TRUE;
#endif
#else
   #pragma warn TODO: implement for mac
	return true;
#endif

}

bool FileUtils::SaveMemoryToFile(
   const char*  szInFilePath,
   unsigned char* pInData,
   unsigned long ulDataLength,
   bool shouldEncrypt,
   const char* encryptionKey)
{
   if (!shouldEncrypt)
   {
      return SaveMemoryToFile(szInFilePath, pInData, ulDataLength);
   }
   else
   {
      TextDES cDES(TextDES::Mode_Encrypt, encryptionKey);
      cDES.writeRawInput((unsigned char*)pInData, ulDataLength);
      cDES.closeRawInput();

      unsigned long encodedDataLen = cDES.getOutputSize();
      unsigned char * encodedData = new unsigned char[encodedDataLen];
      bool success = cDES.readProcessedOutput(encodedData, encodedDataLen);
      //XS_ASSERT(!hasMoreData);
      (void)success;

      bool result = SaveMemoryToFile(szInFilePath, encodedData, encodedDataLen);
      delete [] encodedData;
      return result;
   }
}

bool FileUtils::CreateDir(const char* szDir, bool bRecursive)
{
  if (!bRecursive)
  {
#ifdef WIN32
      int ret = _mkdir(szDir);
#else
      int ret = mkdir(szDir, 0777);
#endif
return ret == 0 || ret == EEXIST;
  } else {
    std::string fullDir = cpc::string(szDir).c_str();

    std::size_t i = 0;
    while ((i = fullDir.find("\\", i)) != std::string::npos)
    {
       fullDir.replace(i, 1, "/");
    }

    i = fullDir.find(":");
    if(std::string::npos == i)
    {
       if (fullDir.size() > 1)
       {
          i = 1;
       }
       else
       {
          i = 0;
       }
    }
    else
    {
       i += 2;
    }

    std::string tmpDir;
    std::string::iterator it = fullDir.begin();
    std::advance(it, i);
    tmpDir += fullDir.substr(0, i);

    for(; fullDir.end() != it; ++it)
    {
      char c = *it;
      tmpDir += c;
      if ('/' == c)
      {
#ifdef WIN32
         if(_mkdir(tmpDir.c_str()) == -1)
#else
         if(mkdir(tmpDir.c_str(), 0777) == -1)
#endif
         {
            if (errno != EEXIST)
            {
               return false;
            }
         }
      }
    }

#ifdef WIN32
    if(_mkdir(tmpDir.c_str()) == -1)
#else
    if(mkdir(tmpDir.c_str(), 0777) == -1)
#endif
    {
       if (errno != EEXIST)
       {
          return false;
       }
    }

    return true;
  }
}

}
