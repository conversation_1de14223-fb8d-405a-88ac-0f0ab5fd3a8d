#pragma once

#ifndef REACTORHELPERS_H
#define REACTORHELPERS_H

#include <rutil/Reactor.hxx>

#include <memory>

namespace resip
{

template<typename T, typename Fn, typename... Args>
struct ReadCallbackWeakPtr : ReadCallbackBase {
   ReadCallbackWeakPtr(const std::weak_ptr<T>& ptr, Fn f, Args... args) : ptr_(ptr), fn_(f), args_(args...) { assert(f != NULL); }
   virtual void operator()() { if (std::shared_ptr<T> ptr = ptr_.lock()) std::apply(fn_, std::tuple_cat(std::make_tuple(ptr.get()), args_)); else GenericLog(Subsystem::SIP, resip::Log::Crit, << "silently discard " << typeid(Fn).name()); }
   virtual void* address() { return *reinterpret_cast<void **>(&fn_); }

private:
   std::weak_ptr<T> ptr_;
   Fn fn_;
   std::tuple<Args...> args_;
};

template<typename T, typename Fn, typename... Args>
ReadCallbackBase* resip_bind(Fn f, const std::weak_ptr<T>& ptr, Args... args)
{
   return new ReadCallbackWeakPtr<T, Fn, Args...>(ptr, f, args...);
}

template<typename T, typename Fn, typename... Args>
ReadCallbackBase* resip_bind(Fn f, const std::shared_ptr<T>& ptr, Args... args)
{
   return new ReadCallbackWeakPtr<T, Fn, Args...>(ptr, f, args...);
}

template <typename T>
static void default_delete(T* p)
{
   delete p;
}
}

#endif // REACTORHELPERS_H
