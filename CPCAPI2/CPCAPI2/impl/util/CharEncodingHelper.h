#pragma once

#ifndef __CPCAPI2_CHARENCODINGHELPER_H__
#define __CPCAPI2_CHARENCODINGHELPER_H__

#include <rutil/Data.hxx>

namespace CPCAPI2
{
   class CharEncodingHelper
   {
   public:
      template<class T>
      static resip::Data unEscape(const T& x)
      {
         resip::Data d = resip::Data::from(x);

         const char* p = d.data();
         const char* e = d.data() + d.size();

         resip::Data r;
         {
            resip::DataStream s(r);
            while (p < e)
            {
               if (*p == '%'
                  && (e - p) > 2
                  && resip::DataHelper::isCharHex[*(p + 1)]
                  && resip::DataHelper::isCharHex[*(p + 2)])
               {
                  s << char((inversehexmap[*(p + 1)] << 4) + inversehexmap[*(p + 2)]);
                  p += 3;
               }
               else
               {
                  s << *p++;
               }
            }
         }

         return r;
      }

      static const char inversehexmap[];
   };
}

#endif // __CPCAPI2_CHARENCODINGHELPER_H__
