#pragma once

#if !defined(CPCAPI2_LIBXML_SHARED_USAGE_H)
#define CPCAPI2_LIBXML_SHARED_USAGE_H


namespace CPCAPI2
{
   class LibxmlSharedUsage
   {
   public:
      // according to libxml documentation, calling xmlCleanupParser will deallocate all memory associated with libxml.
      // this is bad news if another thread is in the midst of using libxml.

      // this class uses reference counting to call xmlInitParser and xmlCleanupParser safely

      static void addRef();
      static void release();
   };
   
   // class to control lifetime of xmlsec library (i.e. how long it stays initialized for).
   // we need to make sure we don't shut down xmlsec after OpenSSL deallocates; this means
   // not letting xmlsec shutdown during C++ global static deallocation; per
   // https://www.openssl.org/docs/man1.1.1/man3/OPENSSL_cleanup.html:
   //
   //    The OPENSSL_cleanup() function deinitialises OpenSSL (both libcrypto and libssl).
   //    All resources allocated by OpenSSL are freed. Typically there should be no need
   //    to call this function directly as it is initiated automatically on application exit.
   //    This is done via the standard C library atexit() function.
   //
   class XmlSecSharedUsage
   {
   public:
      static void addRef();
      static void releaseRef();
   };
   
   // convenience class
   class ScopedXmlSecSharedUsage
   {
   public:
      ScopedXmlSecSharedUsage()
      {
         XmlSecSharedUsage::addRef();
      }
      
      ~ScopedXmlSecSharedUsage()
      {
         XmlSecSharedUsage::releaseRef();
      }
   };
}

#endif
