#include "cpc_thread.h"

#if defined(CPCAPI2_USE_STD_THREAD)
// empty
#else
#if defined(WIN32)
typedef unsigned(__stdcall *CPC_THREAD_START_ROUTINE)(void*);
#include <process.h> // for _beginthreadex()
#else
#include <pthread.h>
#include <rutil/ThreadIf.hxx>
#endif

extern "C"
{
static void*
#ifdef WIN32
__stdcall
cpcThreadWrapper(std::function<void(void)>* funcAsPtrParam)
#else
cpcThreadWrapper(void* funcAsPtrParam)
#endif
{
   std::function<void(void)>* stdFunc = (std::function<void(void)>* )(funcAsPtrParam);
   (*stdFunc)();
   delete funcAsPtrParam;
#ifdef WIN32
   _endthreadex(0);
#endif
   return 0;
}
}

namespace CPCAPI2
{
thread::thread() :
   mId(0)
#if defined(WIN32)
    ,
    mThread(0)
#endif
{
}

thread::thread(std::function<void(void)> threadStart) :
   mThreadStart(threadStart)
{
   if (!threadStart)
   {
      mId = 0;
   }
   std::function<void(void)>* funcAsPtrParam = new std::function<void(void)>(threadStart);
#if defined(WIN32)
   mThread =
       (HANDLE)_beginthreadex 
         (
         NULL, // LPSECURITY_ATTRIBUTES lpThreadAttributes,  // pointer to security attributes
         0, // DWORD dwStackSize,                         // initial thread stack size
         CPC_THREAD_START_ROUTINE
         (cpcThreadWrapper), // LPTHREAD_START_ROUTINE lpStartAddress,     // pointer to thread function
         (LPVOID)funcAsPtrParam, //LPVOID lpParameter,                        // argument for new thread
         0, //DWORD dwCreationFlags,                     // creation flags
         (unsigned*)&mId// LPDWORD lpThreadId                         // pointer to receive thread ID
         );
   //assert( hThread != 0 );
#else
   // spawn the thread

   pthread_attr_t threadAttr = {};
   int attrRet = pthread_attr_init(&threadAttr);
   assert(attrRet == 0);
#ifdef __THREADX
   attrRet = pthread_attr_setstacksize(&threadAttr, 1024 * 128);
   assert(attrRet == 0);
#endif

   if ( int retval = pthread_create( &mId, &threadAttr, cpcThreadWrapper, funcAsPtrParam) )
   {
      // TODO - ADD LOGING HERE
      assert(0);
   }
   pthread_attr_destroy(&threadAttr);
#endif
}

thread::~thread()
{
}

void thread::operator =(const thread& rhs)
{
    this->mId = rhs.mId;
#if defined(WIN32)
    this->mThread = rhs.mThread;
#endif
    this->mThreadStart = rhs.mThreadStart;
}

void thread::join()
{
   if (mId == 0)
   {
      return;
   }

#if defined(WIN32)
   DWORD exitCode;
   while (true)
   {
      if (GetExitCodeThread(mThread,&exitCode) != 0)
      {
         if (exitCode != STILL_ACTIVE)
         {
            break;
         }
         else
         {
            WaitForSingleObject(mThread,INFINITE);
         }
      }
      else
      {
         // log something here
         break;
      }
   }

   //  !kh!
   CloseHandle(mThread);
   mThread=0;
#else
   void* stat;
   if (mId != pthread_self())
   {
      int r = pthread_join( mId , &stat );
      if ( r != 0 )
      {
         // TODO
      }
   }
   
#endif

   mId = 0;
}

void thread::detach()
{
#if !defined(WIN32)
   pthread_detach(mId);
#else
   if(mThread)
   {
      CloseHandle(mThread);
      mThread = 0;
   }
#endif
   mId = 0;
}
void thread::threadProc()
{
   mThreadStart();
}

void this_thread::sleep_for(const timeval& interval)
{
   unsigned int msecs = interval.ms();
#ifdef _WIN32
   Sleep(msecs);
#else
   struct timespec short_wait;
   struct timespec remainder;
   short_wait.tv_sec = msecs / 1000;
   short_wait.tv_nsec = (msecs % 1000) * 1000 * 1000;
   #if defined(ANDROID)
      clock_nanosleep(CLOCK_BOOTTIME, 0, &short_wait, &remainder);
   #else
      nanosleep(&short_wait, &remainder);
   #endif
#endif
}

}

#endif // CPCAPI2_USE_STD_THREAD
