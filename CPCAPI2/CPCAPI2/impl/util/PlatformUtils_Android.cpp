#include "PlatformUtils.h"

#if defined( __linux__ ) && defined( ANDROID )

#include "JniHelper.h"

#include <android/log.h>

using namespace CPCAPI2::PlatformUtils;
using namespace CPCAPI2::Jni;

static bool s_isTablet = false;

bool PlatformUtils::getOSInfo( OSInfo& outOSInfo )
{
   CPCAPI2::Jni::JniThread env;

   outOSInfo.osType = OSType_Android;
   bool result = false;

   outOSInfo.osVersion = CPCAPI2::Jni::GetStaticStringField("android/os/Build$VERSION", "RELEASE").c_str();

   return result;
}

bool PlatformUtils::getDeviceInfo( DeviceInfo& outDeviceInfo )
{
   CPCAPI2::Jni::JniThread env;

   std::string manufacturer = CPCAPI2::Jni::GetStaticStringField("android/os/Build", "MANUFACTURER");
   std::string model = CPCAPI2::Jni::GetStaticStringField("android/os/Build", "MODEL");

   outDeviceInfo.deviceModel = manufacturer + " " + model;

   // "Solution" stolen from this stackoverflow article:
   // http://stackoverflow.com/questions/5832368/tablet-or-phone-android
   //
   // Please note that there is no foolproof way to do this. The best way
   // involves using some resources which are bundled with the application.
   // However since we are the SDK (a library), we can't and won't bundle
   // resources. So the next best option seems to be this solution.
   //
   // This may yeild incorrect results sometimes. But it is felt that the
   // level of errors should be OK for our solution.
   //

   CPCAPI2::Jni::ScopedLocalRef<jobject> resources = CPCAPI2::Jni::CallObjectMethod("android/content/Context", CPCAPI2::Jni::GetContext(), "getResources", "()Landroid/content/res/Resources;");
   CPCAPI2::Jni::ScopedLocalRef<jobject> configuration = CPCAPI2::Jni::CallObjectMethod("android/content/res/Resources", *resources, "getConfiguration", "()Landroid/content/res/Configuration;");

   jint screenLayout = CPCAPI2::Jni::GetIntField("android/content/res/Configuration", *configuration, "screenLayout");
   jint sizeMask     = CPCAPI2::Jni::GetStaticIntField("android/content/res/Configuration", "SCREENLAYOUT_SIZE_MASK");
   jint largeVal     = CPCAPI2::Jni::GetStaticIntField("android/content/res/Configuration", "SCREENLAYOUT_SIZE_LARGE");
   outDeviceInfo.deviceFormFactor = (( screenLayout & sizeMask ) >= largeVal ) ? DeviceFormFactor_Tablet : DeviceFormFactor_Phone;

   return true;
}

bool PlatformUtils::getCpuArchInfo( CpuArchInfo& outCpuArchInfo )
{
   outCpuArchInfo.buildTimeCpuArch = "";

#ifdef __aarch64__
   outCpuArchInfo.buildTimeCpuArch = "ARM64";
   return true;
#elif __arm__
   outCpuArchInfo.buildTimeCpuArch = "ARM";
   return true;
#endif

   return false;
}
#endif // defined( __linux__ ) && defined( ANDROID )
