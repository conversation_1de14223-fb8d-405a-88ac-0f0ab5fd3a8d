#include "LogSubsystems.h"

namespace CPCAPI2
{
CPCAPI2_Subsystem CPCAPI2_Subsystem::PHONE(                 "            PHONE");
CPCAPI2_Subsystem CPCAPI2_Subsystem::MEDIA_STACK(           "      MEDIA_STACK");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_ACCOUNT(           "      SIP_ACCOUNT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::MEDIA(                 "            MEDIA");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_CALL(              "         SIP_CALL");
CPCAPI2_Subsystem CPCAPI2_Subsystem::PTT(                   "              PTT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_EVENT(             "        SIP_EVENT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_PRESENCE(          "     SIP_PRESENCE");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_BLF(               "          SIP_BLF");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_MWI(               "          SIP_MWI");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_FILETRANSFER(      " SIP_FILETRANSFER");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_CHAT(              "         SIP_CHAT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_DIALOGEVENT(       "  SIP_DIALOGEVENT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SIP_REGEVENT(          "     SIP_REGEVENT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::LICENSING(             "        LICENSING");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_STACK(            "       XMPP_STACK");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_CONNECTION(       "  XMPP_CONNECTION");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_ACCOUNT(          "     XMPP_ACCOUNT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_CHAT(             "        XMPP_CHAT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_ROSTER(           "      XMPP_ROSTER");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_FILETRANSFER(     "XMPP_FILETRANSFER");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_MULTI_USER_CHAT(  "   XMPP_GROUPCHAT");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_VCARD(            "       XMPP_VCARD");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_OPENSSL(          "     XMPP_OPENSSL");
CPCAPI2_Subsystem CPCAPI2_Subsystem::XMPP_PUSH(             "        XMPP_PUSH");
CPCAPI2_Subsystem CPCAPI2_Subsystem::REMOTE_SYNC(           "      REMOTE_SYNC");
CPCAPI2_Subsystem CPCAPI2_Subsystem::VCCS(                  "             VCCS");
CPCAPI2_Subsystem CPCAPI2_Subsystem::BI_EVENTS(             "        BI_EVENTS");
CPCAPI2_Subsystem CPCAPI2_Subsystem::SNS(                   "              SNS");
CPCAPI2_Subsystem CPCAPI2_Subsystem::UEM(                   "              UEM");
CPCAPI2_Subsystem CPCAPI2_Subsystem::PEERCONNECTION(        "   PEERCONNECTION");
CPCAPI2_Subsystem CPCAPI2_Subsystem::PROVISIONING(          "     PROVISIONING");
CPCAPI2_Subsystem CPCAPI2_Subsystem::REMOTE_CONTROL(        "   REMOTE_CONTROL");
CPCAPI2_Subsystem CPCAPI2_Subsystem::BROADSOFT_XSI(         "    BROADSOFT_XSI");
CPCAPI2_Subsystem CPCAPI2_Subsystem::EXTERNAL(              "         EXTERNAL");
CPCAPI2_Subsystem CPCAPI2_Subsystem::WATCHDOG(              "         WATCHDOG");
CPCAPI2_Subsystem CPCAPI2_Subsystem::STRETTO_UEM(           "      STRETTO_UEM");
CPCAPI2_Subsystem CPCAPI2_Subsystem::LDAP(                  "             LDAP");
CPCAPI2_Subsystem CPCAPI2_Subsystem::PUSH_SERVER(           "      PUSH_SERVER");
CPCAPI2_Subsystem CPCAPI2_Subsystem::WATCHERINFO(           "      WATCHERINFO");
CPCAPI2_Subsystem CPCAPI2_Subsystem::CONF_CONNECTOR(        "   CONF_CONNECTOR");
CPCAPI2_Subsystem CPCAPI2_Subsystem::CALL_QUALITY(          "     CALL_QUALITY");
CPCAPI2_Subsystem CPCAPI2_Subsystem::PUBLIC_API(            "       PUBLIC_API");
CPCAPI2_Subsystem CPCAPI2_Subsystem::MESSAGE_STORE(         "    MESSAGE_STORE");
CPCAPI2_Subsystem CPCAPI2_Subsystem::WEBSOCKET_SERVER(      " WEBSOCKET_SERVER");
CPCAPI2_Subsystem CPCAPI2_Subsystem::FILE_ACCESS(           "             FILE");
CPCAPI2_Subsystem CPCAPI2_Subsystem::BACKGROUNDING(         "    BACKGROUNDING");
}
