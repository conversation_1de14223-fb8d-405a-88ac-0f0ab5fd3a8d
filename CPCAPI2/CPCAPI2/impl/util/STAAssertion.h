#pragma once

#ifndef __CPCAPI2__STAASSERTION_H__
#define __CPCAPI2__STAASSERTION_H__

#include <thread>
#include <atomic>

#include <assert.h>

namespace CPCAPI2
{
   /**
    * The point of this class is to inform the developer of inappropriate thread usage.
    * Upon first usage, it grabs the current thread ID. After that, whenever it is used,
    * it will compare the current thread ID against the thread ID previously stored, and
    * assert (via regular assert debug macros) if there is a difference. The class will
    * do nothing if not compiled in DEBUG mode.
    *
    * Also an assertion created from another assertion will inherit the thread ID.
    */
   class STAAssertion
   {
   public:

      STAAssertion( void )
         : m_FirstUse( true )
      {
      }

      STAAssertion( const STAAssertion& that )
         : m_FirstUse( false ), m_ID( that.m_ID )
      {
      }

      void test( void )
      {
         // store the current thread ID on first use
         std::thread::id id( std::this_thread::get_id() );
         if( m_FirstUse.exchange( false ))
            m_ID = id;

         assert( m_ID == id );

         // auto tests are often run in release mode
#ifdef CPCAPI2_AUTO_TEST
#ifdef __GNUC__
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wnull-dereference"
#endif
#ifndef __clang_analyzer__
         if ( m_ID != id )
         {
            // force crash (borrowed from gtest)
            *static_cast<volatile int*>(NULL) = 1;
         }
#endif // __clang_analyzer__
#ifdef __GNUC__
#pragma GCC diagnostic pop
#endif
#endif // CPCAPI2_AUTO_TEST
      }

   private:
      std::atomic< bool > m_FirstUse;
      std::thread::id m_ID;
   };
}

#endif // __CPCAPI2__STAASSERTION_H__