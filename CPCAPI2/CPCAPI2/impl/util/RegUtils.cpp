#ifdef _WIN32

#include <tchar.h>
#include <sstream>
#include "RegUtils.h"
#include <cpcapi2utils.h>

using boost::shared_array;
//using namespace XsLib;

namespace CPCAPI2
{

bool RegUtils::CreateKey(
   HKEY hInBaseKey,
   const char *szInSubKeyPath
)
{
   bool bReturnResult = false;

   //FIRST
   {
      std::string cSubKeyPath = szInSubKeyPath;

      HKEY hKeyResult = NULL;
      DWORD dwDisposition = 0;

#ifdef UNICODE
      cpc::string cwSubKeyPath = StringConv::utf8ToWstring(cSubKeyPath.c_str());
      LONG lResult = ::RegCreateKeyEx(
         hInBaseKey,
         cwSubKeyPath.c_str(),
         0,
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_ALL_ACCESS,
         NULL,
         &hKeyResult,
         &dwDisposition
      );
#else
      LONG lResult = ::RegCreateKeyEx(
         hInBaseKey,
         cSubKeyPath.c_str(),
         0,
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_ALL_ACCESS,
         NULL,
         &hKeyResult,
         &dwDisposition
      );
#endif

      //GOTOFINALLY_IF(ERROR_SUCCESS != lResult)
      if (ERROR_SUCCESS != lResult)
         return bReturnResult;

      ::RegCloseKey(hKeyResult);
   }
   //NEXT
   {
      bReturnResult = true;
   }
   //FINALLY
   {
   }

   return bReturnResult;
}


bool RegUtils::QueryStringValue(
   HKEY hBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName,
   std::string &cOutValue
)
{
   bool bReturnResult = false;

   HKEY hKey = NULL;
   TCHAR *tsValueData = NULL;

   //FIRST
   {
      cOutValue.clear();
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey,
         wstrInSubKeyPath.c_str(),
         NULL,
         KEY_READ,
         &hKey
         );
#else 
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey,
         szInSubKeyPath,
         NULL,
         KEY_READ,
         &hKey
         );
#endif
      if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }

      DWORD dwMaxValueDataLen = 0;
      if (ERROR_SUCCESS == lResult)
      {
         lResult = ::RegQueryInfoKey(
            hKey,
            NULL,            // key class string
            NULL,            // key class string length
            NULL,            // not used
            NULL,            // number of subkeys
            NULL,            // longest subkey name length
            NULL,            // maximum subkey class string length
            NULL,            // number of values
            NULL,            // longest value name length
            &dwMaxValueDataLen,   // longest value data length
            NULL,            // security descriptor length
            NULL               // last modification time
            );
      }
      if (lResult == ERROR_SUCCESS)
      {
         tsValueData = new TCHAR[dwMaxValueDataLen+1];

         DWORD dwValueDataLen = dwMaxValueDataLen;
         DWORD dwValueType = 0;
#ifdef UNICODE
         cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
         lResult = ::RegQueryValueEx(
            hKey,
            wstrInValueName.c_str(),
            NULL,
            &dwValueType,
            (BYTE*)tsValueData,
            &dwValueDataLen
            );
#else
         lResult = ::RegQueryValueEx(
            hKey,
            szInValueName,
            NULL,
            &dwValueType,
            (BYTE*)tsValueData,
            &dwValueDataLen
            );
#endif
      }

      if (lResult == ERROR_SUCCESS)
      {
         lResult = ::RegCloseKey(hKey);
         hKey = NULL;
      }

      /*if (lResult == ERROR_SUCCESS)
      {         
         GOTOFINALLY()
      }
      if ((dwValueType != REG_SZ) &&
         (dwValueType != REG_EXPAND_SZ))
      {
         GOTOFINALLY()
      }*/
      if (lResult == ERROR_SUCCESS)
      {
#ifdef UNICODE
         cOutValue = StringConv::wstringToUtf8(tsValueData);
#else
         cOutValue = tsValueData;
#endif
      
      }
      //lResult = ERROR_SUCCESS;
   }
   //NEXT
   {
      bReturnResult = true;
   }
   //FINALLY
   {
      if (hKey != NULL)
      {
         ::RegCloseKey(hKey);
         hKey = NULL;
      }
      if (NULL != tsValueData)
      {
         delete[] tsValueData;
         tsValueData = NULL;
      }
   }
   return bReturnResult;
}

DWORD RegUtils::QueryDWORDValue(
   HKEY hBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName,
   const DWORD dwInDefault
)
{
   DWORD dwReturnResult = dwInDefault;

   HKEY hKey = NULL;

   //FIRST
   {
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey,
         wstrInSubKeyPath.c_str(),
         NULL,
         KEY_READ,
         &hKey
         );
#else
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey,
         szInSubKeyPath,
         NULL,
         KEY_READ,
         &hKey
         );
#endif
      if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }

      DWORD dwValueType = 0;
      DWORD dwValueZise = sizeof(DWORD);
      if (lResult == ERROR_SUCCESS)
      {
#ifdef UNICODE
         cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
         lResult = ::RegQueryValueEx(
            hKey,
            wstrInValueName.c_str(),
            NULL,
            &dwValueType,
            (BYTE*)&dwReturnResult,
            &dwValueZise
            );
#else
         lResult = ::RegQueryValueEx(
            hKey,
            szInValueName,
            NULL,
            &dwValueType,
            (BYTE*)&dwReturnResult,
            &dwValueZise
            );
#endif
      }
   }
   //FINALLY
   {
      if (hKey != NULL)
      {
         ::RegCloseKey(hKey);
         hKey = NULL;
      }
   }

   return dwReturnResult;
}

bool RegUtils::QueryBLOBValue(
	HKEY hInBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName,
   shared_array<BYTE>& pOutBlob,
   DWORD& dwOutBlobSize
)
{
   bool bReturnResult = false;

   HKEY hKey = NULL;
   
   //FIRST
   {
      dwOutBlobSize = 0;
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
	   LONG lResult = ::RegOpenKeyEx(
		   hInBaseKey,
         wstrInSubKeyPath.c_str(),
		   NULL,
		   KEY_READ,
		   &hKey
      );
#else
      LONG lResult = ::RegOpenKeyEx(
		   hInBaseKey,
         szInSubKeyPath,
		   NULL,
		   KEY_READ,
		   &hKey
      );
#endif

	   if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }

      DWORD dwMaxValueDataLen = 0;
      if (lResult == ERROR_SUCCESS)
      {
         lResult = ::RegQueryInfoKey(
		       hKey,
		       NULL,				// key class string
		       NULL,				// key class string length
		       NULL,				// not used
		       NULL,				// number of subkeys
		       NULL,				// longest subkey name length
		       NULL,				// maximum subkey class string length
		       NULL,				// number of values
		       NULL,				// longest value name length
		       &dwMaxValueDataLen,	// longest value data length
		       NULL,				// security descriptor length
		       NULL				   // last modification time
	      );
      }

	   //GOTOFINALLY_IF(lResult != ERROR_SUCCESS)

      pOutBlob.reset(new BYTE[dwMaxValueDataLen + sizeof(TCHAR)]);
      //GOTOABANDON_IF(NULL == pOutBlob.get())

      DWORD dwValueDataLen = dwMaxValueDataLen;
      DWORD dwValueType = 0;

      if (lResult == ERROR_SUCCESS && pOutBlob.get() != NULL)
      {
#ifdef UNICODE
         cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
         lResult = ::RegQueryValueEx(
		      hKey,
            wstrInValueName.c_str(),
		      NULL,
		      &dwValueType,
		      pOutBlob.get(),
		      &dwValueDataLen
         );
#else
         lResult = ::RegQueryValueEx(
		      hKey,
            szInValueName,
		      NULL,
		      &dwValueType,
		      pOutBlob.get(),
		      &dwValueDataLen
         );
      }
#endif

	   /*GOTOFINALLY_IF(lResult != ERROR_SUCCESS)

	   lResult = ::RegCloseKey(hKey);
      hKey = NULL;

	   GOTOFINALLY_IF(lResult != ERROR_SUCCESS)
	   GOTOFINALLY_IF((dwValueType != REG_BINARY) &&
                    (dwValueType != REG_MULTI_SZ))*/

      if (lResult == ERROR_SUCCESS && (dwValueType == REG_BINARY || dwValueType == REG_MULTI_SZ))
      {
         dwOutBlobSize = dwValueDataLen;
         bReturnResult = true;
      }
   }
   //NEXT
   {
      //bReturnResult = true;
   }
   //ABANDON
   {
   }
   //FINALLY
   {
      if (hKey != NULL)
      {
		   ::RegCloseKey(hKey);
         hKey = NULL;
      }
   }

   return bReturnResult;
}

bool RegUtils::SetStringValue(
   HKEY hBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName,
   LPCSTR szInValue
)
{
   bool bReturnResult = false;

   HKEY hKey = NULL;

   //FIRST
   {
      DWORD dwDisposition = NULL;
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
      LONG lResult = ::RegCreateKeyEx(
         hBaseKey, 
         wstrInSubKeyPath.c_str(), 
         NULL, 
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_WRITE,
         NULL,
         &hKey,
         &dwDisposition
         );
#else
      LONG lResult = ::RegCreateKeyEx(
         hBaseKey, 
         szInSubKeyPath, 
         NULL, 
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_WRITE,
         NULL,
         &hKey,
         &dwDisposition
         );
#endif

      if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }
      
#ifdef UNICODE
      cpc::string cValue = StringConv::utf8ToWstring(szInValue);
      cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
      lResult = ::RegSetValueEx(
         hKey,
         wstrInValueName.c_str(),
         NULL,
         REG_SZ,
         (BYTE*)((LPCTSTR)cValue.c_str()),
         (DWORD) (_tcslen((LPCTSTR)cValue.c_str()) + 1)*sizeof(TCHAR)
      );
#else
      std::string cValue = szInValue;

      lResult = ::RegSetValueEx(
         hKey,
         szInValueName,
         NULL,
         REG_SZ,
         (BYTE*)((LPCTSTR)cValue.c_str()),
         (DWORD) (_tcslen((LPCTSTR)cValue.c_str()) + 1)*sizeof(TCHAR)
      );
#endif


      /*GOTOFINALLY_IF(lResult != ERROR_SUCCESS)

      lResult = ::RegCloseKey(hKey);
      hKey = NULL;

      GOTOFINALLY_IF(lResult != ERROR_SUCCESS)

      lResult = ERROR_SUCCESS;*/
      if (lResult == ERROR_SUCCESS)
         bReturnResult = true;
   }
   //FINALLY
   {
      if (hKey != NULL)
      {
         ::RegCloseKey(hKey);
         hKey = NULL;
      }
   }
   return bReturnResult;
}


bool RegUtils::SetDWORDValue(
   HKEY hBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName,
   const DWORD dwInValue
)
{
   bool bReturnResult = false;

   HKEY hKey = NULL;

   //FIRST
   {
      DWORD dwDisposition = NULL;
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
      LONG lResult = ::RegCreateKeyEx(
         hBaseKey, 
         wstrInSubKeyPath.c_str(), 
         NULL, 
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_WRITE,
         NULL,
         &hKey,
         &dwDisposition
         );
#else
      LONG lResult = ::RegCreateKeyEx(
         hBaseKey, 
         szInSubKeyPath, 
         NULL, 
         NULL,
         REG_OPTION_NON_VOLATILE,
         KEY_WRITE,
         NULL,
         &hKey,
         &dwDisposition
         );
#endif

      if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }

      if (lResult == ERROR_SUCCESS)
      {
#ifdef UNICODE
         cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
         lResult = ::RegSetValueEx(
            hKey,
            wstrInValueName.c_str(),
            NULL,
            REG_DWORD,
            (BYTE*)&dwInValue,
            sizeof(DWORD)
         );
#else
         lResult = ::RegSetValueEx(
            hKey,
            szInValueName,
            NULL,
            REG_DWORD,
            (BYTE*)&dwInValue,
            sizeof(DWORD)
         );
#endif
      }
      
      if (lResult == ERROR_SUCCESS)
         bReturnResult = true;
   }
   //FINALLY
   {
      if (hKey != NULL)
      {
         ::RegCloseKey(hKey);
         hKey = NULL;
      }
   }

   return bReturnResult;
}

bool RegUtils::DeleteValue(
   HKEY hBaseKey,
   LPCSTR szInSubKeyPath,
   LPCSTR szInValueName
)
{
   bool bReturnResult = false;

   HKEY hKey = NULL;

   //FIRST
   {
#ifdef UNICODE
      cpc::string wstrInSubKeyPath = StringConv::utf8ToWstring(szInSubKeyPath);
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey, 
         wstrInSubKeyPath.c_str(),
         NULL, 
         KEY_READ | KEY_WRITE | KEY_SET_VALUE,
         &hKey
      );
#else
      LONG lResult = ::RegOpenKeyEx(
         hBaseKey, 
         szInSubKeyPath,
         NULL, 
         KEY_READ | KEY_WRITE | KEY_SET_VALUE,
         &hKey
      );
#endif
      if (lResult != ERROR_SUCCESS)
      {
         hKey = NULL;
      }

      if (lResult == ERROR_SUCCESS)
      {
#ifdef UNICODE
         cpc::string wstrInValueName = StringConv::utf8ToWstring(szInValueName);
         lResult = RegDeleteValue(
            hKey,
            wstrInValueName.c_str());
#else
         lResult = RegDeleteValue(
            hKey,
            szInValueName);
#endif
      }
      if (lResult == ERROR_SUCCESS)
         bReturnResult = true;
   }
   //NEXT
   {
      //bReturnResult = true;
   }
   //FINALLY
   {
      if (NULL != hKey)
      {
         ::RegCloseKey(hKey);
         hKey = NULL;
      }
   }
   return bReturnResult;
}

void RegUtils::AddHashFromRegistryBlob(
   HKEY hInBaseKey,
   LPCSTR szInLocation,
   LPCSTR szInValue,
   std::string& cIoHashInfo
)
{
   shared_array<BYTE> pBlob;
   DWORD dwBlobLength = 0;

   std::string cRegData;
   if (QueryBLOBValue(
            hInBaseKey,
            szInLocation,
            szInValue,
            pBlob,
            dwBlobLength
         ) && NULL != pBlob.get()
      )
   {
      DWORD dwLoop = 0;
      while (0 != dwBlobLength)
      {
         std::ostringstream cHex;
         
         cHex.flags(std::ios::hex);
         cHex.width(2);
         cHex.fill('0');
         cHex << (unsigned int)(pBlob[dwLoop]);

         cIoHashInfo += cHex.str();

         --dwBlobLength;
         ++dwLoop;
      }
   }

   cIoHashInfo += ":";
}

}// namespace

#endif
