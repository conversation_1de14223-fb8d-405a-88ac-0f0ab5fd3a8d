#include "StunClient.h"

#include <rutil/dns/DnsStub.hxx>
#include <rutil/dns/QueryTypes.hxx>
#include <rutil/Timer.hxx>
#include <rutil/DnsUtil.hxx>
#include <cpcapi2utils.h>

namespace CPCAPI2
{
static void SleepMs(int msecs) {
#ifdef _WIN32
  Sleep(msecs);
#else
  struct timespec short_wait;
  struct timespec remainder;
  short_wait.tv_sec = msecs / 1000;
  short_wait.tv_nsec = (msecs % 1000) * 1000 * 1000;
  #if defined(ANDROID)
      clock_nanosleep(CLOCK_BOOTTIME, 0, &short_wait, &remainder);
   #else
      nanosleep(&short_wait, &remainder);
   #endif
#endif
}

StunClient::StunClient(const resip::DnsStub::DnsSettings& dnsSettings,
                       resip::UdpTransport* udpTransport)
   : mUdpTransport(udpTransport)
   , mOnDnsResultHostCalled(false)
   , mOnDnsResultSrvCalled(false)
   , mDns(new resip::DnsStub(dnsSettings))
{
}

StunDnsHostRecord StunClient::getDnsHostRecord(const resip::Data& target)
{
   // Don't perform a lookup if the specified target is already an IP address
   if (resip::DnsUtil::isIpAddress(target))
   {
      in_addr addr;
      resip::DnsUtil::inet_pton(target, addr);
      StunDnsHostRecord dnsHostRecord;
      dnsHostRecord.addr = addr;
      return dnsHostRecord;
   }

   mOnDnsResultHostCalled = false;

   // Perform the DNS 'A' lookup using the hostname passed in
   mDns->lookup<resip::RR_A>(target, this);

   // Wait a maximum of 5 seconds for the DNS 'A' response
   resip::FdSet dnsFdSet;
   UInt64 lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      // Flush pending outgoing DNS 'A' request and parse pending incoming DNS 'A' responses
      mDns->buildFdSet(dnsFdSet);
      if (dnsFdSet.selectMilliSeconds(200) > 0)
      {
         mDns->process(dnsFdSet);
      }

      // Stop testing once we have a DNS 'A' response is received
      if (mOnDnsResultHostCalled)
      {
         break;
      }
   }

   // Return the first DNS 'A' record found
   StunDnsHostRecord ret;
   if (mDnsHostRecords.size() > 0)
   {
      resip::DnsHostRecord dnsHostRecord = mDnsHostRecords.front();

      in_addr addr;
      addr.s_addr = dnsHostRecord.addr().s_addr;
      ret.addr = addr;
   }

   return ret;
}

StunDnsSrvRecord StunClient::getDnsSrvRecord(const resip::Data& domainName)
{
   StunDnsSrvRecord ret;

   // Perform the SRV lookup using the domain passed in
   resip::Data target = resip::Data((domainName).c_str());

   // check for port and IP address
   resip::Data::size_type colonPos = target.find(":");
   if (colonPos != resip::Data::npos)
   {
      target = target.substr(0, colonPos);
   }

   // Don't perform a lookup if the specified target is already an IP address
   if (resip::DnsUtil::isIpAddress(target))
   {
      return ret;
   }

   mOnDnsResultSrvCalled = false;

   mDns->lookup<resip::RR_SRV>(resip::Data("_stun._udp.") + target, resip::Protocol::Stun, this);

   // Wait a maximum of 5 seconds for the SRV response
   resip::FdSet dnsFdSet;
   UInt64 lastTime = resip::Timer::getTimeMs();
   while ((resip::Timer::getTimeMs() - lastTime) < 5 * 1000)
   {
      // Flush pending outgoing SRV request and parse pending incoming SRV responses
      mDns->buildFdSet(dnsFdSet);
      mDns->process(dnsFdSet);

      // Stop testing once we have a SRV response is received
      if (mOnDnsResultSrvCalled)
      {
         break;
      }

      // Wait before checking for the response again
      SleepMs(200);
   }

   // Return the first SRV record found   
   if (mDnsSrvRecords.size() > 0)
   {
      resip::DnsSrvRecord dnsSrvRecord = mDnsSrvRecords.front();
      ret.target = dnsSrvRecord.target();
      ret.port = dnsSrvRecord.port();
   }

   return ret;
}

void StunClient::onDnsResult(const resip::DNSResult<resip::DnsSrvRecord>& result)
{
   if (result.status == 0)
   {
      mDnsSrvRecords = result.records;
   }

   mOnDnsResultSrvCalled = true;
}

void StunClient::onDnsResult(const resip::DNSResult<resip::DnsHostRecord>& result)
{
   if (result.status == 0)
   {
      mDnsHostRecords = result.records;
   }

   mOnDnsResultHostCalled = true;
}

resip::Tuple StunClient::getStunAddress(const resip::Data& stunServer, unsigned int stunPort)
{
   assert(mUdpTransport);

   resip::Tuple mappedAddress;
   mappedAddress.setPort(0);

   // Send the STUN request
   sendStunTest(stunServer, stunPort);

   // Wait a maximum of 5 seconds for the STUN response
   UInt64 lastStunTestTime = resip::Timer::getTimeMs() + (5 * 1000);
   while (resip::Timer::getTimeMs() < lastStunTestTime)
   {
      // Flush pending outgoing STUN requests and parse pending incoming STUN responses
      mUdpTransport->processPollEvent(FPEM_Read | FPEM_Write);

      // Stop testing once we have a valid STUN response
      if (mUdpTransport->stunResult(mappedAddress))
      {
         break;
      }

      // Wait before checking for the response again
      SleepMs(200);
   }

   return mappedAddress;
}

void StunClient::sendStunTest(const resip::Data& stunServer, unsigned int stunPort)
{
   // NOTE: We perform a DNS 'A' lookup here instead of calling gethostbyname()
   //       to be in line with how it is being done for NAT traversal for media traffic:
   //       when SRV is ON, the result of the DNS 'SRV' lookup (i.e. the STUN server hostname) 
   //       is fed into a DNS 'A' lookup in order to get the IP address of the STUN server.

   // Get the STUN server IP address and port
   StunDnsHostRecord dnsHostRecord = getDnsHostRecord(stunServer);
   in_addr hostAddr = dnsHostRecord.addr;
   resip::Tuple stunDest(hostAddr, stunPort, resip::UDP);

   // Send the STUN request
   mUdpTransport->stunSendTest(stunDest);
}

void StunClient::reInit(const resip::DnsStub::DnsSettings& dnsSettings, resip::UdpTransport* udpTransport)
{
   mDns->reInit(dnsSettings);
   if (udpTransport)
   {
      mUdpTransport = udpTransport;
   }
}

bool StunClient::hasValidTransport(resip::UdpTransport* transport)
{
   if (mUdpTransport != transport)
   {
      mUdpTransport = transport;
   }
   return (mUdpTransport != NULL);
}

}
