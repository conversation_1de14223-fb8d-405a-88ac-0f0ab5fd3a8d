#pragma once

#if !defined(CPCAPI2_DEVICE_INFO)
#define CPCAPI2_DEVICE_INFO

#ifdef ANDROID
#include <jni.h>
#endif

#include "cpcapi2defs.h"
#include "cpcstl/string.h"

namespace CPCAPI2
{

class DeviceInfo
{

public:

   // Provides instanceID as per https://tools.ietf.org/html/rfc5626#section-4.1
   static int getInstanceId(cpc::string& instanceId);

   static int getPlatformUniqueId(cpc::string& id);

   static int getDeviceModel(cpc::string& deviceModel);
};

}
#endif // CPCAPI2_DEVICE_INFO
