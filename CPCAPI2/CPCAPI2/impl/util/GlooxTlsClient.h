#pragma once

#if !defined(CPCAPI2_GLOOX_TLS_CLIENT_H)
#define CPCAPI2_GLOOX_TLS_CLIENT_H

#include <tlsopensslclient.h>
#include <resip/stack/SecurityTypes.hxx>

#include <cpcapi2defs.h>

using namespace gloox;

namespace CPCAPI2
{

class GlooxTlsClient : public OpenSSLClient
{
public:
   GlooxTlsClient(TLSHandler* th, const std::string& server, resip::SecurityTypes::SSLType sslType, const std::string tlsCiphers);

   virtual ~GlooxTlsClient();

private:
   virtual bool setType() OVERRIDE;
   virtual bool privateInit() OVERRIDE;

private:
   const resip::SecurityTypes::SSLType mSslType;
   const std::string mTlsCiphers;
};

}

#endif // CPCAPI2_GLOOX_TLS_CLIENT_H
