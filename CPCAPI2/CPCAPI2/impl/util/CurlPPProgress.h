#ifndef __CPCAPI2_CURLPPPROGRESS_H__
#define __CPCAPI2_CURLPPPROGRESS_H__

#include <atomic>
#include <memory>
#include <stdint.h>

namespace CPCAPI2
{
   /**
    * If you want the ability to abort the request, it is recommended
    * you use an instance of CurlPPProgress, like this:
    *
    * CurlPPProgress progressFunctor;
    * request.setOpt( new ProgressFunction( progressFunctor ));
    * ...
    * progressFunctor.abort();
    */
   class CurlPPProgress
   {
   public:
      CurlPPProgress( unsigned int timeoutSeconds = 0 );
      CurlPPProgress( const CurlPPProgress& that );
      virtual ~CurlPPProgress();
      void abort( void );

      /**
       * note that this operator() can be extended in order to obtain
       * percentage information.
       */
      virtual int operator()( double dltotal, double dlnow, double ultotal, double ulnow );

   private:
      // need a shared_ptr since the progress is copied
      std::shared_ptr< std::atomic< int > > m_IsAborted;

      int64_t m_TimeoutMillis;
      int64_t m_LastTimestamp;
      int64_t m_LastDownload;
      int64_t m_LastUpload;
   };
}

#endif // __CPCAPI2_CURLPPPROGRESS_H__
