#pragma once

#if !defined(CPCAPI2_THREADUTILS_H)
#define CPCAPI2_THREADUTILS_H

#if defined(WIN32)
#include <Windows.h>
#elif defined(__APPLE__)
#include <sys/types.h>
#include <sys/event.h>
#include <sys/time.h>
#include <unistd.h>
#endif

namespace CPCAPI2
{
class WaitableTimerWrapper
{
public:
   WaitableTimerWrapper(int intervalMs);
   ~WaitableTimerWrapper();
   int Wait();
private:
#ifdef WIN32
   HANDLE mTimerHandle;
#elif defined(__linux__)
   struct periodic_info
   {
      int timer_fd;
      unsigned long long wakeups_missed;
   };
   periodic_info mInfo;
#elif defined(__APPLE__)
   int mKq;
   struct kevent mChange;
#else
   int mIntervalMs;
#endif
};

// Valid values for priority of Thread::Options and SimpleThread::Options, and
// SetCurrentThreadPriority(), listed in increasing order of importance.
enum CpcThreadPriority {
   // Suitable for threads that shouldn't disrupt high priority work.
   CpcThreadPriority_BACKGROUND,
   // Default priority level.
   CpcThreadPriority_NORMAL,
   // Suitable for threads which generate data for the display (at ~60Hz).
   CpcThreadPriority_DISPLAY,
   // Suitable for low-latency, glitch-resistant audio.
   CpcThreadPriority_REALTIME_AUDIO,
};

class ThreadPriorityHelper
{
public:
   static void SetCurrentThreadPriority(CpcThreadPriority prio);
};

}

#endif // CPCAPI2_THREADUTILS_H
