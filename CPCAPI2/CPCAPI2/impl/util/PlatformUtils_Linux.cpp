#include "PlatformUtils.h"
#if defined( __linux__ ) && !defined( ANDROID )

#include <sys/utsname.h>

using namespace CPCAPI2::PlatformUtils;

bool PlatformUtils::getOSInfo( OSInfo& outOSInfo )
{
   struct utsname unameData;
   if (uname(&unameData) != 0) {
      return false;
   }

   outOSInfo.osType = OSType_Linux;
   outOSInfo.osVersion = std::string(unameData.release);

   return true;
}

bool PlatformUtils::getDeviceInfo( DeviceInfo& outDeviceInfo )
{
   bool result( false );

   outDeviceInfo.deviceModel = "";
   outDeviceInfo.deviceFormFactor = DeviceFormFactor_Computer;
   result = true;

   return result;
}

bool PlatformUtils::getCpuArchInfo( CpuArchInfo& outCpuArchInfo )
{
   return false;
}

#endif // defined( __linux__ ) && !defined( ANDROID )
