#ifndef CPCAPI2_TEXTDES_H
#define CPCAPI2_TEXTDES_H

#include <string>
#include <openssl/evp.h>

namespace CPCAPI2
{

class TextDES
{
public:
   enum TextDES_MODE
   {
      Mode_Encrypt,
      Mode_Decrypt
   };

public:
   TextDES(TextDES_MODE mode, std::string key);
   ~TextDES();

   void writeRawInput(unsigned char *input, int inputsz);
   void closeRawInput();
   bool readProcessedOutput(unsigned char *dataout, int sz);
   int getOutputSize();

private:
   enum TextDES_STATE
   {
      ST_INIT,
      ST_IN_PROGRESS,
      ST_FINAL,
      ST_ERR
   };

private:
   void doInitialize();
   void doProcessData(unsigned char *data, int datasz);
   void doFinalizeData();
   void doCleanup();

private:
   TextDES_STATE _state;
   std::string _key;
   std::string _iv;
   TextDES_MODE _mode;
   EVP_CIPHER_CTX* _context;
   int _outlen;
   unsigned char *_outbuf;

};

}

#endif
