#pragma once

#ifndef WIN8STR_WIN8STR_H
#define WIN8STR_WIN8STR_H

namespace win8str
{
static char* ToUtf8(Platform::String^ pstr)
{
	int req_size = WideCharToMultiByte(
		CP_UTF8,
		0,
		pstr->Data(),
		pstr->Length(),
		NULL,
		0, NULL, NULL);
	char* outbuff = new char[req_size+1];
	WideCharToMultiByte(
		CP_UTF8,
		0,
		pstr->Data(),
		pstr->Length(),
		outbuff,
		req_size,
		NULL, NULL);
	outbuff[req_size] = '\0';
	return outbuff;
}
}

#endif // WIN8STR_WIN8STR_H
