/******************************************************************************
 *
 * File   : XMLReader.h 
 * Date   : 2012-08-02
 * Auther : Ming
 *			Copyright (C) 2012  CounterPath Corp.
 *
 *	This Header file declares class XMLReader, which is designed to parse
 *	xml content from a file or memory and hold a xmlTextReader Pointer to a
 *  xml tree. XMLNode instances will walk through the tree to find the 
 *  target node of tree
 *
 *****************************************************************************/
#pragma once

#ifndef __XML_READER_H__
#define __XML_READER_H__

#include <string>
#include <libxml/xmlreader.h>


namespace LibxmlWrp{

class XMLReader
{
public:
	/**
	 *
	 *  constructor -- build xmlTextReader pointer from an XML file 
	 *  Arguments:
	 *		sFileName	:	The full path of a file
	 *      sVerifyRoot	:	Root name of xml to Verify, default "" to disable verify
	 *		sEncode		:	Encode of xml, default is "UTF-8"
     *
	 **/
	XMLReader(const std::string& sFileName,const std::string& sVerifyRoot = "",
		const std::string & sEncode= "UTF-8");

	/**
	 *
	 *  constructor -- build xmlTextReader pointer from a memory chunk 
	 *  Arguments:
	 *		sBuf		:	Memory buffer contains xml content
	 *      iSize		:   size of sBuf
	 *      sVerifyRoot	:	Root node name of xml to Verify, default "" to disable verify
	 *		sEncode		:	Encode of xml, default is "UTF-8"
     *
	 **/
	XMLReader(const std::string& sBuf, const int iSize, const std::string& sVerifyRoot = "",
		const std::string & sEncode= "UTF-8");

	virtual ~XMLReader();

	// Document parse successful. */
	bool IsOk() const { return m_ok; }

	// convert *this to xmlNodeptr type
	operator xmlNodePtr();

private:
	// block the default constructor, assignment operator
	// copy constructor
	XMLReader() {};
	XMLReader& operator=(const XMLReader& ) { return *this; } 
	XMLReader(const XMLReader& ) {} // copy constructor

	/*
	 * Handler of xml parsed tree
	 */
	xmlTextReaderPtr m_pReader;

	/*
	 *xmlNode pointer pointing to the Root node of parsed tree.
	 */
	xmlNodePtr       m_pRoot;
	bool m_ok;
};


}

#endif // __XML_READER_H__
