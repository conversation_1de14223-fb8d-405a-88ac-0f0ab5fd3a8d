/*
 * This file gives some examples on how to use xmlWrp clesses
 *
 */

#include <iostream>
#include <sstream>
#include <stdio.h>
#include <stdlib.h>
#include "XMLException.h"
#include "XMLNode.h"
#include "XMLReader.h"

using namespace LibxmlWrp;

int main()
{

  std::ostringstream memStream;
   
  memStream <<"<?xml version='1.0'?>" << "\n" <<
  "<presence xmlns='urn:ietf:params:xml:ns:pidf' xmlns:pdm='urn:ietf:params:xml:ns:pidf:data-model'" << "\n" 
               "xmlns:rpid='urn:ietf:params:xml:ns:pidf:rpid' xmlns:op='urn:ietf:params:xml:ns:pidf:oma-pres'" << "\n"
               "xmlns:c='urn:ietf:params:xml:ns:pidf:cipid'  entity='sip:max.sampleATnsn.com'>"
  "\n" << "<tuple id='T_1'>"
  "\n" << "   <status>"
  "\n" << "      <basic>open</basic>"
  "\n" << "   </status>"
  "\n" << "   <rpid:user-input>idle</rpid:user-input>"
  "\n" << "   <op:willingness>"
  "\n" << "      <op:basic>open</op:basic>"
  "\n" << "   </op:willingness>"
  "\n" << "   <rpid:class>message</rpid:class>"
  "\n" << "   <op:service-description>"
  "\n" << "      <op:service-id>org.openmobilealliance:IM-pager-mode</op:service-id>"
  "\n" << "      <op:version>3.1</op:version>"
  "\n" << "      <op:description>This is the NCS Message service</op:description>"
  "\n" << "   </op:service-description>"
  "\n" << "   <contact priority='1.0'>sip:10.143.xxx.xxx:4060</contact>"
  "\n" << "   <note xml:lang='en'>Don't Disturb Please!</note>"
  "\n" << "   <timestamp>2010-07-01T09:37:47Z</timestamp>"
  "\n" << "</tuple>"
  "\n" << "<tuple id='s1'>"
  "\n" << "   <status>"
  "\n" << "       <basic>open</basic>"
  "\n" << "   </status>"
  "\n" << "   <rpid:user-input>idle</rpid:user-input>"
  "\n" << "   <op:willingness>"
  "\n" << "      <op:basic>open</op:basic>"
  "\n" << "   </op:willingness>"
  "\n" << "   <rpid:class>voice</rpid:class>"
  "\n" << "   <op:service-description>"
  "\n" << "       <op:service-id>org.3gpp.cs-speech</op:service-id>"
  "\n" << "       <op:version>3.1</op:version>"
  "\n" << "       <op:description>This is the NCS voice service</op:description>"
  "\n" << "    </op:service-description>"
  "\n" << "    <contact priority='1.0'>sip:10.143.xxx.xxx:4060</contact>"
  "\n" << "   <note xml:lang='en'>Don not Disturb Please!</note>"
  "\n" << "   <timestamp>2010-07-01T09:37:47Z</timestamp>"
  "\n" << "</tuple>"
  "\n" << "<pdm:person id='bob'>"
  "\n" << "    <rpid:activities from='2005-05-30T12:00:00+05:00'"
  "\n" << "                      until='2005-05-30T17:00:00+05:00'>"
  "\n" << "         <rpid:note lang='en'>Far away</rpid:note>"
  "\n" << "         <rpid:away/>"
  "\n" << "    </rpid:activities>"
  "\n" << "    <op:overriding-willingness>"
  "\n" << "       <op:basic>open</op:basic>"
  "\n" << "    </op:overriding-willingness>"
  "\n" << "    <c:homepage/>"
  "\n" << "    <c:display-name>Bbbbob</c:display-name>"
  "\n" << "    <pdm:note xml:lang='en'>Call me!</pdm:note>"
  "\n" << "    <pdm:timestamp>2010-07-01T09:37:47Z</pdm:timestamp>"
  "\n" << "</pdm:person>"
  "\n" << "</presence>";

/*
 *	This sample explains how to use xml wrapper to parse the XML content. 
 *  basic steps listed as below:
 *		1. Constructing an XMLreader object.
 *		2. Constructing XMLNode object/objects that pointing to the target node
 *		   based on the an XMLreader object
 *      3. Constructing other XMLNode object or using methods to get node/content/
 *         properity of target nodes
 */


/*
 * step 1. creating an XMLreader object from XML in memory. also we can parse
 *         a xml file by calling another constructor of XMLreader
 */
  const std::string buf = memStream.str();

  // Creating an XMLreader object to parse xml content in memory with root node "presence"
  // and encode "UTF-8". also XML file can be parsed by another overloading constructor
  try
  {
     XMLReader xReader(buf, buf.length(), "presence", "UTF-8");
  }
  catch( const Exception& e )
  {
	  std::cerr << e.ToString() << std::endl;
	  // or you can log the error message 
  }

  // check the result
  if(!xReader.IsOk())
  {
	  //return error
	  return 1;
  }

  // Creating an XMLreader object from xml file
  try
  {
      XMLReader fReader("presence.xml","presence","UTF-8");
  }
  catch( const Exception& e )
  {
	  std::cerr << e.ToString() << std::endl;
	  // or you can log the error message 
  }

/*
 * step 2. Constructing  XMLNode object
 */

  // constructing an XMLNode object pointing to node "person" in namespace
  // "pdm"
  XMLNode objNode(xReader,"person","pdm");

/*
 * step 3. Constructing  XMLNode object, finding target nodes
 */

  /* 
   * using relative searching methods to get subnode/content/properity in 
   * person subtree
   */

  // get the propery id of node 'person'
  std::string sId = objNode.GetProperty("id");					// get the property id of node "person"

  // get some data from node 'activities' by creating 
  // a new node pointing to 'activities' from node
  // 'person', not from 'presence'
  XMLNode actvt(objNode, "activities", "rpid");					// created node pointing to "rpid:activities"
  std::string sFrom;
  if( actvt.IsValid())
  {
	  sFrom = actvt.GetProperty("from");						// get the property lang
  }

  // get some data from node 'note' by creating 
  // a new node pointing to 'note' from node from 
  // 'presence' ( root node )
  XMLNode note(xReader, "note", "rpid");						// created node pointing to "rpid:note"
  std::string sLang = note.GetProperty("lang");					// get the content of property lang
  std::string sContent =  note.GetContent();					// get the content of node "rpid:note"

  // ++ operator
  ++objNode;													// objNode pointing to next node "rpid:activities"
  sFrom = objNode.GetProperty("from");							// get the property from
 
  // MoveToNode example, 
  objNode.MoveFromCurrent("overriding-willingness","op");		// objNode now pointing to the node 'overriding-willingness'
  objNode++;													// objNode now pointing to the node 'basic'
  sContent = objNode.GetContent();								// Get the content of node 'basic', which is 'open'


  /*
   * sample of MoveToSibling()
   * MoveToSibling() quickly searching nodes on same level
   * instead of travel through entire tree.
   */
  XMLNode tupleNode(xReader, "tuple");							// Pointing to the first 'tuple' node
  if(tupleNode.MoveToSibling("tuple"))							// Pointing to the second 'tuple' node
  {
	  tupleNode.MoveFromCurrent("class","rpid");				// Pointing to the node 'class'
	  sContent = tupleNode.GetContent();						// get the content of node 'class' which is 'voice'
  }

   /*
    * sample of operator [], pointer, ++ and travel xml tree
    */
  XMLNode objRoot(xReader);										// Creates a Node object pointing to Root node
  XMLNode *pNode = NULL, Node;
  std::string nodename("tuple");

  Node = objRoot[nodename];										// Node object pointing to the first 'tuple' node

  // travel through all nodes from node 'tuple'
  // print out the content if there is
  while(Node)
  {
     std::cout<< "Node: "<<Node.GetNodeName()
		      << "\t\tcontent:  " 
			  << Node.GetContent()<< std::endl;
     ++Node;
  }

  // puse here to check the result of printing on console
  std::cin.get();
}