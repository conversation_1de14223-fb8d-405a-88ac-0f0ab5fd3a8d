========================================================================
    xml wrapper Classes Overview
========================================================================

This project contains three classes. all three classes are in one namespace
'LibxmlWrp'


Class XMLReader
    includes two files, 
	XMLReader.h 
	XMLReader.cpp. 
    
    it uses libxml lib to build the xmlTextReader object from xml file or 
    xml content in memory. data member m_pRoot holds the pointer pointing 
    to the Root Node of parsed xml tree.

class XMLNode
    includes two files, 
	XMLNode.h 
	XMLNode.cpp. 
    
    It gets m_pRoot from object of class XMLReader. its object can point 
    to a target node of parsed xml tree. 

class XMLException
    includes files, 
        XMLException.h 
        XMLException.cpp. 
    
    it is an exception class for two classes above.



there is a sample named sample.cpp, examples can be found in it. Please following
steps listed below when you use the xmlWrp class,
    1. Set including Path
       * in your project, open 'Properities'-> 'C/C++'->'General'->'Additional
         Include Directories', add 'libxml', 'libxml\include' and 'util\xmlWrp'

    2. Set linkage
       * in your project, open 'Properities'-> 'Linker' -> 'General'->
         'Additional Library Directories', add '......\libxml\lib\$(Configuration)'
       * in your project, open 'Properities'-> 'Linker' -> 'Input'->
         'Additional Dependencies', add libxml.lib

    3. Include xmlWrp source files in your project
       * including source files in your project
	 	'XMLNode.cpp'
		'XMLReader.cpp'
		'XMLException.cpp'

    4. Include header files in your cpp file
       * including following header files in your cpp file when it refering xmlWrp
         	#include <libxml/xmlreader.h>
		#include "XMLException.h"
		#include "XMLNode.h"
		#include "XMLReader.h"

Please refer to XMLNode.h and XMLReader.h for more detailed about two
classes.