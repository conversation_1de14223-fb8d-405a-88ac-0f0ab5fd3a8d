/******************************************************************************
 *
 * File   : XMLNode.h 
 * Date   : 2012-08-02
 * Auther : Ming
 *			Copyright (C) 2012  CounterPath Corp.
 *
 *	This Header file declares class XMLNode, which is designed to implement
 *	node searching related features in a parsed XML tree provided by xmlTextReader.
 *  data member m_pRoot that is pointing to root node of xmlTextReader tree.
 *
 *  Class XMLNode provides two different ways for searching Nodes in XML Tree.
 *  The first one is absolute way, all searching is staring at Root Node of XML
 *  Tree. 
 *  The second one is relative way, all earching is string at a node of XML Tree
 *
 *****************************************************************************/
#pragma once

#ifndef __XMLNode_H__
#define __XMLNode_H__

#include <string>
#include <libxml/xmlreader.h>

namespace LibxmlWrp {		// namespace for C++ wrapper of libxml lib

class XMLReader;

/*
 * class XMLNode holds the two pointer data members, m_pRoot and m_pCurrent,
 * m_pRoot is pointing to the Root Node and m_pCurrent is pointing to the 
 * Node the XMLNode object is pointing to.
 *
 * Normal steps of using this class:
 *		1. Constructing an XMLreader object.
 *		2. Constructing XMLNode object/objects that pointing to the target node
 *		   based on the an XMLreader object
 *      3. Constructing other XMLNode object or using methods to get node/content/
 *         properity of target nodes
 *
 */
class XMLNode
{
public:
	/*
	 *	Default constructor, it creates an object with NULL value for two pointers, 
	 *  m_pCurrent and m_pRoot. It can be used as variable of XMLNode pointing
	 *  to different Node within its life scope
	 */
	XMLNode(): m_pRoot(NULL), m_pCurrent( NULL) {};

	/*
	 *	Constructor, it creates an object pointing to Root Node of parsed XML tree
	 *      m_pRoot		: pointing to Root Node of parsed XML tree
	 *		m_pCurrent	: pointing to Root Node of parsed XML tree
	 */
	XMLNode(XMLReader& tReader);
    
	/*
	 *	Constructor, it creates an object pointing to Node named "sNodePath" 
	 *  in namespace "sNameSpace", result as:
	 *      m_pRoot		: pointing to Root Node of parsed XML tree
	 *		m_pCurrent	: pointing to Node Node named "sNodePath" if succeeded,
	 *                    otherwise NULL is set
	 */
	XMLNode(XMLReader& tReader, const std::string& sNodeName, const std::string & sNameSpace = "");
    
	/*
	 *	Constructor, it creates an object pointing to Node named "sNodeName" in namespace
	 *  "sNameSpace" searching from start position at pNode, result as:
	 *      m_pRoot		: pointing to Root Node of parsed XML tree
	 *		m_pCurrent	: pointing to Node Node named "sNodeName" if succeeded, 
	 *                    otherwise NULL is set
	 */
	XMLNode(const XMLNode& pNode, const std::string& sNodeName,const std::string & sNameSpace = "");

	~XMLNode();

	/*
	 *  convert type XMLNode to type xmlNodePtr which actually is m_pCurrent
	 */
	operator xmlNodePtr() ;
	
	/*
	 *  Return XMLNode object pointing to node ""sName" from m_pCurrent of this
	 *  instead of m_pRoot of XML tree.
	 */
	XMLNode operator[](const std::string& sName) ;
	
	/*
	 *  convert type XMLNode to std::string which actually return the 
	 *  content of node
	 */
	//operator std::string() { return GetContent(); }
	
	/*
	 *	prefix ++ operator, make itself pointing to next node, it means that
	 *  m_pCurrent pointing to next validated node of Parsed xml tree.
	 */
	void operator++() ;

	/*
	 *	postfix ++ operator
	 */
	XMLNode operator++(int);
	
	/*
	 * assignment operator
	 */
	XMLNode & operator=(const XMLNode& Node);

	/*
	 * to check whether *this is validated object pointing to a validated 
	 * node of parsed XML tree
	 */
	bool IsValid() const { return m_pCurrent ? true : false; }

	/*
	 * Return property 'propname' for 'current node'. 
	 */
	std::string GetProperty(const std::string& sPropname) ;

	/*
	 * Return node name of *this. 
	 */
	const std::string GetNodeName() ;

	/*
	 * Return content of *this. 
	 */
	const std::string GetContent() ;

	/*
	 * return Namespace prefix of 'current node'. 
	 */
	const std::string GetNodeNsPrefix() ;
	
	/*
	 * return Namespace href of 'current node'. 
	 */
	const std::string GetNodeNsHref() ;

	/*
	 * Search named 'sName' in m_pRoot child nodes and then make itself pointing to
	 * 'sName' node. 
	 */
	xmlNodePtr MoveFromRoot(const std::string& sName,const std::string & sNameSpace = "") ;

	/*
	 * Search named 'sName' in m_pCurrent child nodes and then make itself pointing to
	 * 'sName' node. 
	 */
	xmlNodePtr MoveFromCurrent(const std::string& sName,
		const std::string & sNameSpace = "") ;

	/*
	 * Find node named 'sName' on same level as '*this'.
	 * Set 'm_pCurrent' to node 'sName'. 
	 */
	bool MoveToSibling(const std::string& sName, 
		const std::string & sNameSpace = "") ;

private:            

	/* Constructor, it creates an object with initialization:
	 *     p_mRoot    = pNode->m_pRoot
	 *     m_pCurrent = ptr->m_pCurrent
	 * it can be used as a sub xml tree started from node 'pNode'
	 */
	XMLNode(xmlNodePtr pNode, xmlNodePtr ptr);

	/*
	 * Assign next non-blank node of 'current node' to 'current node'.
	 * return Current Node ( xmlNodePtr type) 
	 */
	xmlNodePtr GetNextNode() ;

	/*
	 * return next non-blank node of 'pNode'  
	 */
	xmlNodePtr GetNextNode(xmlNodePtr pNode) ;

	/*
	 * return next non-blank sibling node of 'pNode' 
	 */
	xmlNodePtr GetNextSibling(xmlNodePtr pNode) ;

	/*
	 * Sets 'current node' pointing to node 'pCrrtNode'
	 */
	void SetCurrent(xmlNodePtr pCrrtNode) ; 

	/* 
	 * verify the namespce of node pNode against 'sNameSpace'
	 */
	bool VerifyNameSpace( xmlNodePtr & pNode, const std::string sNameSpace);



private:
	/*
	 * data member to point to the Root Node of parsed XML content, it is set when
	 * this object is created or passed by other XMLNode object. in fact, it is 
	 * the orignal point to start with when searching target node.
	 */
	xmlNodePtr	m_pRoot;	

	/*
	 * Data member represents which node this object pointing to. all data of 
	 * target nodes is obtained through this member internally.
	 */
	xmlNodePtr	m_pCurrent;
};


}

#endif // _XMLNode_H
