/******************************************************************************
 *
 * File   : XMLException.h 
 * Date   : 2012-08-02
 * Auther : Ming
 *			Copyright (C) 2012  CounterPath Corp.
 *
 *	This Header file declares class XMLException
 *
 *****************************************************************************/
#pragma once

#ifndef __XML_Exception_H__
#define __XML_Exception_H__

#include <string>
#include <exception>

namespace LibxmlWrp {		// namespace for C++ wrapper of libxml lib

class XMLException : public std::exception
{
public:
	XMLException(const std::string & descr);

	XMLException(const XMLException & x) : exception(x) {} // copy constructor

private:
	XMLException& operator=(const XMLException& ) { return *this; } // assignment operator

};


}

#endif // __XML_Exception_H__
