/******************************************************************************
 *
 * File   : XMLNode.cpp 
 * Date   : 2012-08-02
 * Auther : Ming
 *			Copyright (C) 2012  CounterPath Corp.
 *
 *	Implementation of class XMLNode, more detailed description of class is in 
 *	header file
 *  
 *****************************************************************************/

#include "XMLNode.h"
#include "XMLReader.h"
#include "XMLException.h"

namespace LibxmlWrp {		// namespace for C++ wrapper of libxml lib

XMLNode::XMLNode(XMLReader& tReader)
: m_pRoot(tReader)
, m_pCurrent( tReader ) 
{
}


XMLNode::XMLNode(XMLReader& tReader, const std::string& sNodeName,
	const std::string & sNameSpace)
: m_pRoot(tReader)
{
	// *this pointing to node named 'sNodeName' if succeed
	m_pCurrent = MoveFromRoot( sNodeName, sNameSpace);
}


XMLNode::XMLNode(const XMLNode& pNode, const std::string& sNodeName,
	const std::string & sNameSpace)
: m_pRoot( pNode.m_pRoot )

{
	// *this pointing to node named 'sNodeName' if succeed
	m_pCurrent = MoveFromRoot( sNodeName, sNameSpace );
}


XMLNode::XMLNode(xmlNodePtr pNode, xmlNodePtr ptr)
: m_pRoot(pNode)
, m_pCurrent( ptr )
{
	// Create a node to be used as a sub tree
}

XMLNode::~XMLNode()
{
}


void XMLNode::SetCurrent(xmlNodePtr pCrrtNode)
{ 
	m_pCurrent = pCrrtNode;
}

std::string XMLNode::GetProperty(const std::string& sName) 
{
	xmlChar *p = m_pCurrent ? xmlGetProp(m_pCurrent, (const xmlChar *) sName.c_str() ) : NULL;

	std::string str = (char *)p;
	
	xmlFree(p);
	return str;
}

xmlNodePtr XMLNode::GetNextNode() 
{
	return GetNextNode(m_pCurrent);
}

xmlNodePtr XMLNode::GetNextNode(xmlNodePtr pNode) 
{
	// cycle through the pNode to find first node named "name"
    while ( pNode ) 
	{
		// switch to the children if possible
		if( pNode->children != NULL)
		{
			pNode = pNode->children ;
		}
		else if (pNode->next) // switch to the next node
		{
		    pNode  = pNode ->next;
		}
		else
		{
			/* 
			  recursion is not used here for performance concerned
            */
			// step back to parent's next node if possible
			if( pNode ->parent )
			{
				pNode  = pNode ->parent->next;
			}
			else
			{
				// Can not find node after seaching whole subtree nodes
				return NULL ;
			}
		}

		// check the Node name and type
		if( pNode  && pNode ->type == XML_ELEMENT_NODE )
		{
            // find the Node
			return pNode ;
		}
	}

	return pNode ;
}

xmlNodePtr XMLNode::GetNextSibling(xmlNodePtr pNode) 
{
	// cycle through the pNode to find first sibling node named "name"
    while ( pNode ) 
	{
		// switch to the next node
		if (pNode->next) 
		{
		    pNode  = pNode ->next;
		}
		else
		{
			// Can not find node after seaching whole subtree nodes
			return NULL ;
		}

		// check the Node name and type
		if( pNode  && pNode ->type == XML_ELEMENT_NODE )
		{
            // find the Node
			return pNode ;
		}
	}

	return pNode ;
}

const std::string XMLNode::GetNodeName()
{
	if (m_pCurrent)
	{
		return ((char *)m_pCurrent -> name);
	}
	else
	{
		return "";
	}
}


const std::string XMLNode::GetContent() 
{
	std::string m_sContent = "";
	if (m_pCurrent)
	{
		xmlNodePtr p = m_pCurrent->children;
		
		/* 
		 * Alert, a bug of libxml
		 * in libxml, 4 spaces are intented to be filled in content
		 * if there is not content for the node. but, in fact, the first
		 * character is '\10', not space. 
		 */
		if (p && p -> content && p->content[0] != 10)
			//xmlStrcasecmp(p->content , BAD_CAST("    ")) != 0)
		{
			m_sContent = (char *)p -> content;
		}
	}
	return m_sContent;
}


const std::string XMLNode::GetNodeNsPrefix()
{
	if (m_pCurrent && m_pCurrent -> ns &&
		m_pCurrent -> ns -> prefix)
	{
		return (char *)m_pCurrent -> ns -> prefix;
	}
	else
	{
		return "";
	}
}


const std::string XMLNode::GetNodeNsHref()
{
	if (m_pCurrent && m_pCurrent -> ns && 
		m_pCurrent -> ns -> href)
	{
		return (char *)m_pCurrent -> ns -> href;
	}
	else
	{
		return "";
	}
}

bool XMLNode::VerifyNameSpace( xmlNodePtr & pNode, const std::string sNameSpace) 
{
	if(sNameSpace == "")	// do not check namaspace
		return true;

	if (xmlStrcasecmp(pNode->ns->prefix , BAD_CAST(sNameSpace.c_str())) == 0 ||
		xmlStrcasecmp(pNode->ns->href , BAD_CAST(sNameSpace.c_str())) == 0)
		return true ;
	else 
		return false;
}

xmlNodePtr XMLNode::MoveFromRoot(const std::string& sName, 
	const std::string & sNameSpace) 
{
	m_pCurrent = m_pRoot;
	return MoveFromCurrent( sName, sNameSpace);
}

xmlNodePtr XMLNode::MoveFromCurrent(const std::string& sName, 
	const std::string & sNameSpace) 
{
	// get next validated node from node 'm_pCurrent'
	xmlNodePtr p = GetNextNode( m_pCurrent );

	while (p)
	{
		// check the node name and name space
		if (xmlStrcasecmp(p->name , BAD_CAST(sName.c_str())) == 0 && 
			VerifyNameSpace( p, sNameSpace))
		{
			// set *this object pointing to target node
			SetCurrent(p);
			return p;
		}

		// get next validated node from node p
		p = GetNextNode(p);
	}

	SetCurrent(0);
	return NULL;
}

bool XMLNode::MoveToSibling(const std::string& sName,
	const std::string & sNameSpace)
{
	// get next validated sibling node from node 'm_pCurrent'
	xmlNodePtr p = GetNextSibling(m_pCurrent);

	while (p)
	{
		// check node name and name space
		if (xmlStrcasecmp(p->name , BAD_CAST(sName.c_str())) == 0&& 
			VerifyNameSpace( p, sNameSpace)) 
		{
			SetCurrent(p);
			return true;
		}

		// get next validated sibling node from node 'p'
		p = GetNextSibling(p);
	}
	return false;
}


XMLNode::operator xmlNodePtr()
{
	return m_pCurrent;
}


XMLNode XMLNode::operator[](const std::string& sName) 
{
	xmlNodePtr p = MoveFromCurrent( sName);

	if (p)
		return XMLNode( m_pRoot, p );
	else
		//return XMLNode();
		throw XMLException("Didn't find node: " + sName);
}

XMLNode & XMLNode::operator=(const XMLNode & Node)
{
    this->m_pCurrent = Node.m_pCurrent;
	this->m_pRoot = Node.m_pRoot;

	return *this;
}

void XMLNode::operator++()
{
	m_pCurrent = GetNextNode();
}

XMLNode XMLNode::operator++(int)
{
	XMLNode tmp(*this);

	++(*this);

	return tmp;
}

}


