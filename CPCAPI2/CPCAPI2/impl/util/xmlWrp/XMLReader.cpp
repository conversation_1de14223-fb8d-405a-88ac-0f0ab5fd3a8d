/******************************************************************************
 *
 * File   : XMLReader.cpp 
 * Date   : 2012-08-02
 * Auther : Ming
 *			Copyright (C) 2012  CounterPath Corp.
 *
 *	Implementation of class XMLReader, more detailed description of class is in 
 *	header file
 *  
 *****************************************************************************/

#include "XMLReader.h"
#include "XMLException.h"

namespace LibxmlWrp {		// namespace for C++ wrapper of libxml lib

XMLReader::XMLReader(const std::string& sBuf, const int iSize, 
	     const std::string& sVerifyRoot, const std::string & sEncode)
: m_pReader(NULL)
, m_ok(false)
{
	// Read the xml in memory and build xmlTextReader
	if (!(m_pReader = xmlReaderForMemory(sBuf.c_str(), iSize, "", sEncode.c_str(),0)))
	{
		throw XMLException("Parse of memory failed! ");
	}

	// check whether it is empty or not, if succeeded, m_pReader points
	// to the root node of parsed xml tree  
    if( xmlTextReaderRead(m_pReader) != 1) 
	{
		xmlFreeTextReader(m_pReader);
		m_pReader = NULL;
		throw XMLException("memory contains empty xml file");
	}
	else
	{
		// store the root node
		m_pRoot = xmlTextReaderExpand(m_pReader);
	}

	// verify the root
	if (sVerifyRoot.size())
	{
		// verify the root node
		if (xmlStrcmp(m_pRoot->name, (const xmlChar *) sVerifyRoot.c_str() ))
		{
			xmlFreeTextReader(m_pReader);
			m_pReader = NULL;
			throw XMLException("Document root != " + sVerifyRoot);
		}
	}

	// set the status of file parsing
	m_ok = true;
}

// Read the xml from a file and build xmlTextReader
XMLReader::XMLReader(const std::string& sFileName, const std::string& sVerifyRoot,
	                 const std::string & sEncode)
: m_pReader(NULL)
, m_ok(false)
{
	// Read the xml file and build xml tree
	if (!(m_pReader = xmlReaderForFile(sFileName.c_str(),sEncode.c_str(),0)))  
	{
		throw XMLException("Parse of file failed: " + sFileName);
	}

	// Verify the whether it is empty or not
    if( xmlTextReaderRead(m_pReader) != 1) 
	{
		xmlFreeTextReader(m_pReader);
		m_pReader = NULL;
		throw XMLException("Document is empty : " + sFileName);
	}

	// verify the root
	if (sVerifyRoot.size())
	{
		// store the root node
		m_pRoot = xmlTextReaderExpand(m_pReader);

		// verify the root name
		if (xmlStrcmp(m_pRoot->name, (const xmlChar *) sVerifyRoot.c_str() ))
		{
			xmlFreeTextReader(m_pReader);
			m_pReader = NULL;
			throw XMLException("Document root != " + sVerifyRoot);
		}
	}

	// set the status of file parsing
	m_ok = true;
}

XMLReader::~XMLReader()
{
	if (m_pReader)
	{
		// release the resouces associated with m_pReader
		xmlFreeTextReader(m_pReader);
	}
}

XMLReader::operator xmlNodePtr()
{
	return m_pRoot;
}


}
