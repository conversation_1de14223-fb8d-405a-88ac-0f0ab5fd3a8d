#if !defined(CPCAPI2_HDDISKID_H)
#define CPCAPI2_HDDISKID_H

#ifdef WIN32
#include <cpcapi2defs.h>
#include <WinIoCtl.h>

#include <string>
#include <vector>


namespace CPCAPI2
{

#pragma pack(push,1)

typedef struct _GETVERSIONOUTPARAMS
{
   BYTE bVersion;      
   BYTE bRevision;     
   BYTE bReserved;     
   BYTE bIDEDeviceMap; 
   DWORD fCapabilities; 
   DWORD dwReserved[4]; 
}GETVERSIONOUTPARAMS, *PGETVERSIONOUTPARAMS, *LPGETVERSIONOUTPARAMS;

typedef struct _IDEREGS
{
   BYTE bFeaturesReg;      
   BYTE bSectorCountReg;    
   BYTE bSectorNumberReg;   
   BYTE bCylLowReg;         
   BYTE bCylHighReg;        
   BYTE bDriveHeadReg;      
   BYTE bCommandReg;        
   BYTE bReserved;          
} IDEREGS, *PIDEREGS, *LPIDEREGS;

typedef struct _SENDCMDINPARAMS
{
   DWORD     cBufferSize;   
   IDEREGS   irDriveRegs;   
   BYTE bDriveNumber;       
                            
   BYTE bReserved[3];       
   DWORD     dwReserved[4]; 
   BYTE      bBuffer[1];    
} SENDCMDINPARAMS, *PSENDCMDINPARAMS, *LPSENDCMDINPARAMS;

typedef struct _DRIVERSTATUS
{
   BYTE  bDriverError;  
   BYTE  bIDEStatus;    
                        
   BYTE  bReserved[2];  
   DWORD  dwReserved[2];  
} DRIVERSTATUS, *PDRIVERSTATUS, *LPDRIVERSTATUS;

typedef struct _SENDCMDOUTPARAMS
{
   DWORD         cBufferSize;   
   DRIVERSTATUS  DriverStatus;  
   BYTE          bBuffer[1];    
} SENDCMDOUTPARAMS, *PSENDCMDOUTPARAMS, *LPSENDCMDOUTPARAMS;

typedef struct _SRB_IO_CONTROL
{
   ULONG HeaderLength;
   UCHAR Signature[8];
   ULONG Timeout;
   ULONG ControlCode;
   ULONG ReturnCode;
   ULONG Length;
} SRB_IO_CONTROL, *PSRB_IO_CONTROL;

typedef struct _IDSECTOR
{
   USHORT  wGenConfig;
   USHORT  wNumCyls;
   USHORT  wReserved;
   USHORT  wNumHeads;
   USHORT  wBytesPerTrack;
   USHORT  wBytesPerSector;
   USHORT  wSectorsPerTrack;
   USHORT  wVendorUnique[3];
   CHAR    sSerialNumber[20];
   USHORT  wBufferType;
   USHORT  wBufferSize;
   USHORT  wECCSize;
   CHAR    sFirmwareRev[8];
   CHAR    sModelNumber[40];
   USHORT  wMoreVendorUnique;
   USHORT  wDoubleWordIO;
   USHORT  wCapabilities;
   USHORT  wReserved1;
   USHORT  wPIOTiming;
   USHORT  wDMATiming;
   USHORT  wBS;
   USHORT  wNumCurrentCyls;
   USHORT  wNumCurrentHeads;
   USHORT  wNumCurrentSectorsPerTrack;
   ULONG   ulCurrentSectorCapacity;
   USHORT  wMultSectorStuff;
   ULONG   ulTotalAddressableSectors;
   USHORT  wSingleWordDMA;
   USHORT  wMultiWordDMA;
   BYTE    bReserved[128];
} IDSECTOR, *PIDSECTOR;

#pragma pack(pop) // set default
#pragma pack(push, 4)

typedef enum _STORAGE_QUERY_TYPE {
    PropertyStandardQuery = 0,         
    PropertyExistsQuery,                
    PropertyMaskQuery,                 
    PropertyQueryMaxDefined     
}STORAGE_QUERY_TYPE, *PSTORAGE_QUERY_TYPE;

typedef enum _STORAGE_PROPERTY_ID {
    StorageDeviceProperty = 0,
    StorageAdapterProperty
}STORAGE_PROPERTY_ID, *PSTORAGE_PROPERTY_ID;

typedef struct _STORAGE_PROPERTY_QUERY
{
    STORAGE_PROPERTY_ID PropertyId;   
    STORAGE_QUERY_TYPE QueryType;     
    UCHAR AdditionalParameters[1];    
}STORAGE_PROPERTY_QUERY, *PSTORAGE_PROPERTY_QUERY;


#define IOCTL_STORAGE_QUERY_PROPERTY   CTL_CODE(IOCTL_STORAGE_BASE, 0x0500, METHOD_BUFFERED, FILE_ANY_ACCESS)

//#pragma pack(4)

typedef struct _STORAGE_DEVICE_DESCRIPTOR {

    ULONG Version;               
    ULONG Size;                  
    UCHAR DeviceType;            
    UCHAR DeviceTypeModifier;    
    BOOLEAN RemovableMedia;      
    BOOLEAN CommandQueueing;     
    ULONG VendorIdOffset;        
    ULONG ProductIdOffset;       
    ULONG ProductRevisionOffset; 
    ULONG SerialNumberOffset;    
    STORAGE_BUS_TYPE BusType;    
    ULONG RawPropertiesLength;   
    UCHAR RawDeviceProperties[1];
}STORAGE_DEVICE_DESCRIPTOR, *PSTORAGE_DEVICE_DESCRIPTOR;

typedef struct _MEDIA_SERIAL_NUMBER_DATA {
  ULONG  SerialNumberLength; 
  ULONG  Result;
  ULONG  Reserved[2];
  UCHAR  SerialNumberData[1];
}MEDIA_SERIAL_NUMBER_DATA, *PMEDIA_SERIAL_NUMBER_DATA;

#pragma pack(pop)

class HDDiskId
{
public:
   static std::string GetPrimaryMasterHDDId(void);
   static std::vector<std::string> HDDiskId::GetAllHddInfo();

private:

   static std::string HDDiskId::GetHddInfoAdminRights (void);
   static std::string HDDiskId::GetHddInfoNoRights (void);
   static std::string HDDiskId::GetHddSerialAsScsiDevice (void);

   static std::string HDDiskId::GetDriveInfoAdmin(int);
   static std::string HDDiskId::GetDriveInfoNonAdmin(int);
   static std::vector<std::string> HDDiskId::GetAllHddThroughScsi();
 
   static std::string HDDiskId::ConvertHexBytesToString (std::string);
};

}
#endif //WIN32

#endif 
