#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)
#include "SipSharedCallAppearanceSetInfo.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

SipSharedCallAppearanceSetHandle SipSharedCallAppearanceSetInfo::nextSharedCallAppearanceSet = 1;

SipSharedCallAppearanceInfo::SipSharedCallAppearanceInfo(SipSharedCallAppearanceHandle handle, const SipSharedCallAppearanceSettings& settings)
   : mHandle(handle), 
     mSettings(settings),
     mSubscription(0),
     mPublication(0),
     mConversation(0)
{
}

SipSharedCallAppearanceInfo::~SipSharedCallAppearanceInfo()
{
}

SipSharedCallAppearanceSetInfo::SipSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle handle)
   : mHandle(handle),
     mAccountHandle(0)
{
}

SipSharedCallAppearanceSetInfo::~SipSharedCallAppearanceSetInfo()
{
   for (SipSharedCallAppearanceInfoMap::const_iterator it = mSharedCallAppearanceInfo.begin(); it != mSharedCallAppearanceInfo.end(); it++)
   {
      SipSharedCallAppearanceInfo* scaInfo = it->second;
      delete scaInfo;
   }
}

}
}
#endif
