#include "brand_branded.h"

#include "interface/experimental/sharedcallappearance/SipSharedCallAppearanceManager.h"
#include "interface/experimental/sharedcallappearance/SipSharedCallAppearanceState.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)
#include "SipSharedCallAppearanceManagerInterface.h"
#include "SipSharedCallAppearanceStateImpl.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

SipSharedCallAppearanceManager* SipSharedCallAppearanceManager::getInterface(CPCAPI2::Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<SipSharedCallAppearanceManagerInterface>(phone, "SipSharedCallAppearanceManagerInterface");
#else
   return NULL;
#endif
}

SipSharedCallAppearanceStateManager* SipSharedCallAppearanceStateManager::getInterface(SipSharedCallAppearanceManager* scaManager)
{
#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)
   SipSharedCallAppearanceManagerInterface* parent = dynamic_cast<SipSharedCallAppearanceManagerInterface*>(scaManager);
   if (parent == NULL) return NULL;
   PhoneInterface* phone = parent->phoneInterface();
   return _GetInterfaceEx<SipSharedCallAppearanceStateImpl>(phone, "SharedCallAppearanceStateManager", parent);
#else
   return NULL;
#endif
}

}
}

// CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE
