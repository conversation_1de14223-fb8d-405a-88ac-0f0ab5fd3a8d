#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)

#include "SipSharedCallAppearanceManagerInterface.h"
#include "SipSharedCallAppearanceManagerImpl.h"
#include "SipSharedCallAppearanceSetInfo.h"
#include "SipSharedCallAppearanceHelper.h"
#include "phone/PhoneInterface.h"

using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipConversation;

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

const int SipSharedCallAppearanceManagerInterface::MAX_SUBSCRIPTION_COUNT = 21;

SipSharedCallAppearanceManagerInterface::SipSharedCallAppearanceManagerInterface(Phone* phone)
   : EventSource2<EventHandler<SipSharedCallAppearanceHand<PERSON>, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(mPhone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mStateManager = SipSharedCallAppearanceStateManager::getInterface(this);
   mSipDialogEventSubscriptionIf = dynamic_cast<SipDialogEventSubscriptionManagerInterface*>(SipDialogEventSubscriptionManager::getInterface(phone));
   mSipDialogEventPublicationIf = dynamic_cast<SipDialogEventPublicationManagerInterface*>(SipDialogEventPublicationManagerInterface::getInterface(phone));
   mSipConversationIf = dynamic_cast<SipAVConversationManagerInterface*>(SipConversationManager::getInterface(phone));

   mSipConversationIf->addSdkObserver(this);
}

SipSharedCallAppearanceManagerInterface::~SipSharedCallAppearanceManagerInterface()
{
   resip::Lock locker(mMutex);

   // Delete all the registered handlers and clear the map
   for (AccountMap::iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipSharedCallAppearanceManagerImpl* scaManager = it->second;
      delete scaManager;
   }
   mAccountMap.clear();
}

void SipSharedCallAppearanceManagerInterface::Release()
{
   delete this;
}

PhoneInterface* SipSharedCallAppearanceManagerInterface::phoneInterface()
{
   return mPhone;
}

int SipSharedCallAppearanceManagerInterface::setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipSharedCallAppearanceManagerInterface::setHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

void SipSharedCallAppearanceManagerInterface::setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceHandler* handler)
{
   resip::Lock locker(mMutex);

   // Retrieve the manager associated with the account specified
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(account);
   if (!scaManager)
   {
      // No manager associated with the account

      // Get the associated account object
      CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
      if (acct)
      {
         // Create a new manager
         scaManager = new SipSharedCallAppearanceManagerImpl(this, *acct, mStateManager, mSipDialogEventSubscriptionIf, mSipDialogEventPublicationIf, mSipConversationIf); // Destroyed in the destructor of this class

         // Keep mapping account -> manager
         mAccountMap[account] = scaManager;
      }
      else
      {
         mAccountIf->fireError("Invalid account handle for SharedCallAppearanceManagerInterface::setHandler");
         return;
      }

      mSipDialogEventSubscriptionIf->setHandlerImpl(account, scaManager);
      mSipDialogEventPublicationIf->setHandlerImpl(account, scaManager);
   }

   auto it = mHandlers.find(account);
   if (mHandlers.end() != it)
   {
     removeAppHandler(it->second, account);
   }

   mHandlers[account] = handler;
   if (nullptr != handler)
   {
      addAppHandler(handler, account);
   }
}

SipSharedCallAppearanceSetHandle SipSharedCallAppearanceManagerInterface::createSharedCallAppearanceSet(CPCAPI2::SipAccount::SipAccountHandle account, const SipSharedCallAppearanceSetSettings& settings)
{
   SipSharedCallAppearanceSetHandle scaSetHandle = SipSharedCallAppearanceSetInfo::nextSharedCallAppearanceSet++;
   postToSdkThread(resip::resip_bind(&SipSharedCallAppearanceManagerInterface::createSharedCallAppearanceSetImpl, this, account, scaSetHandle, settings));
   return scaSetHandle;
}

void SipSharedCallAppearanceManagerInterface::createSharedCallAppearanceSetImpl(SipAccount::SipAccountHandle account, SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceSetSettings& settings)
{
   resip::Lock locker(mMutex);

   // Retrieve the account
   CPCAPI2::SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      // No account found. Send an error
      cpc::string msg = cpc::string("Creating SCA set with invalid account handle: ") + cpc::to_string(account) +
         ", SipSharedCallAppearanceSetHandle invalid: " + cpc::to_string(scaSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Make sure the account is enabled
   if (!acct->isEnabled())
   {
      // Account not enabled. Send an error
      cpc::string msg = cpc::string("Creating SCA set before account enabled: ") + cpc::to_string(account) +
         ", SipSharedCallAppearanceSetHandle invalid: " + cpc::to_string(scaSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Retrieve the manager associated with the account specified
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(account);
   if (!scaManager)
   {
      cpc::string msg = cpc::string("Creating SCA set with no handler set for account: ") + cpc::to_string(account) +
         ", SipSharedCallAppearanceSetHandle invalid: " + cpc::to_string(scaSet);
      mAccountIf->fireError(msg);
      return;
   }

   // Create a new SCA set using the newly allocated handle
   SipSharedCallAppearanceSetInfo* scaSetInfo = scaManager->createSharedCallAppearanceSetInfo(scaSet);
   scaSetInfo->mSettings = settings;
}

int SipSharedCallAppearanceManagerInterface::addSharedCallAppearance(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const SipSharedCallAppearanceSettings& scaSettings)
{
   postToSdkThread(resip::resip_bind(&SipSharedCallAppearanceManagerInterface::addSharedCallAppearanceImpl, this, scaSet, sca, scaSettings));
   return kSuccess;
}

void SipSharedCallAppearanceManagerInterface::addSharedCallAppearanceImpl(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const SipSharedCallAppearanceSettings& scaSettings)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return;
   }

   // Add the SCA
   assert(!scaSetInfo->mSharedCallAppearanceInfo[sca]);
   scaSetInfo->mSharedCallAppearanceInfo[sca] = new SipSharedCallAppearanceInfo(sca, scaSettings); // Destroyed by SipSharedCallAppearanceSetInfo's destructor
}

int SipSharedCallAppearanceManagerInterface::start(SipSharedCallAppearanceSetHandle scaSet)
{
   postToSdkThread(resip::resip_bind(&SipSharedCallAppearanceManagerInterface::startImpl, this, scaSet));
   return kSuccess;
}

void SipSharedCallAppearanceManagerInterface::startImpl(SipSharedCallAppearanceSetHandle scaSet)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return;
   }

   // Get the manager associated with the account asssociated with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return;
   }

   // Make sure there is at least one SCA added to the set
   if (scaSetInfo->mSharedCallAppearanceInfo.empty())
   {
      // No SCA specified. Send an error
      scaManager->fireError(scaSet, "Cannot start subscriptions. No SCA have been added");
      return;
   }

   // Make sure that the number of SCA subscriptions does not exceed the max allowed
   if (scaSetInfo->mSharedCallAppearanceInfo.size() > MAX_SUBSCRIPTION_COUNT)
   {
      // Max number of SCA exceeded. Send an error
      scaManager->fireError(scaSet, "Cannot start subscriptions. Too many SCAs have been added");
      return;
   }

   // Start subscriptions of the SCAs
   scaManager->startSubscriptions(scaSetInfo);
}

int SipSharedCallAppearanceManagerInterface::end(SipSharedCallAppearanceSetHandle scaSet)
{
   postToSdkThread(resip::resip_bind(&SipSharedCallAppearanceManagerInterface::endImpl, this, scaSet));
   return kSuccess;
}

void SipSharedCallAppearanceManagerInterface::endImpl(SipSharedCallAppearanceSetHandle scaSet)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return;
   }

   // Terminate subscriptions of the SCAs
   scaManager->endSubscriptions(scaSetInfo);
}

SipConversationHandle SipSharedCallAppearanceManagerInterface::makeCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Make the call
   return scaManager->makeCall(scaSetInfo, sca, to, mediaDescriptor);
}

SipConversationHandle SipSharedCallAppearanceManagerInterface::makeCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& audioDescriptor, const CPCAPI2::SipConversation::MediaInfo& videoDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Make the call
   return scaManager->makeCall(scaSetInfo, sca, to, audioDescriptor, videoDescriptor);
}

SipConversationHandle SipSharedCallAppearanceManagerInterface::makeConferenceCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::vector<SipConversationHandle>& otherConversations, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Make the call
   return scaManager->makeConferenceCall(scaSetInfo, sca, otherConversations, mediaDescriptor);
}

int SipSharedCallAppearanceManagerInterface::scapHold(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle &sca, CPCAPI2::SipConversation::SipConversationHandle conversation)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Perform a SCAP hold operation
   scaManager->scapHold(scaSetInfo, sca, conversation);

   return kSuccess;
}

SipConversationHandle SipSharedCallAppearanceManagerInterface::scapJoin(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Perform a SCAP join operation
   return scaManager->scapJoin(scaSetInfo, sca, appearance, mediaDescriptor);
}

SipConversationHandle SipSharedCallAppearanceManagerInterface::scapBridgeIn(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Perform a SCAP bridge-in operation
   return scaManager->scapBridgeIn(scaSetInfo, sca, appearance, mediaDescriptor);
}

int SipSharedCallAppearanceManagerInterface::makeExclusive(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, bool exclusive)
{
   resip::Lock locker(mMutex);

   // Get info on the SCA set
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaSet);
   if (!scaSetInfo)
   {
      return 0;
   }

   // Get the manager associated with with the SCA set
   SipSharedCallAppearanceManagerImpl* scaManager = getSharedCallAppearanceManager(scaSetInfo->mAccountHandle);
   if (!scaManager)
   {
      return 0;
   }

   // Terminate the call
   scaManager->makeExclusive(scaSetInfo, sca, appearance, exclusive);

   return kSuccess;
}

int SipSharedCallAppearanceManagerInterface::getAppearanceForScapCall(const cpc::string& alertInfoHeader)
{
   int appearanceIndex = SipSharedCallAppearanceHelper::getAppearance(alertInfoHeader, SipSharedCallAppearanceHelper::APPEARANCE_PARAM);
   // try with Broadsoft param name also
   if (appearanceIndex == kError)
      appearanceIndex = SipSharedCallAppearanceHelper::getAppearance(alertInfoHeader, SipSharedCallAppearanceHelper::ALTERNATE_APPEARANCE_PARAM);
   return appearanceIndex;
}

SipSharedCallAppearanceManagerImpl* SipSharedCallAppearanceManagerInterface::getSharedCallAppearanceManager(CPCAPI2::SipAccount::SipAccountHandle account) const
{
   AccountMap::const_iterator it = mAccountMap.find(account);
   return (it != mAccountMap.end()) ? it->second : NULL;
}

SipSharedCallAppearanceSetInfo* SipSharedCallAppearanceManagerInterface::getSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle scaSet) const
{
   // Browse through all the registered accounts to find
   // the manager associated with the handle specified
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipSharedCallAppearanceManagerImpl* scaManager = it->second;
      SipSharedCallAppearanceSetInfo* scaSetInfo = scaManager->getSharedCallAppearanceSetInfo(scaSet);
      if (scaSetInfo)
      {
         return scaSetInfo;
      }
   }

   return NULL;
}

int SipSharedCallAppearanceManagerInterface::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   // Broadcast to all SCA manager instances
   // It's the SCA manager's responsibility to determine if the conversation passed in is of interest to it
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipSharedCallAppearanceManagerImpl* scaManager = it->second;
      scaManager->onConversationStateChanged(conversation, args);
   }

   return kSuccess;
}

int SipSharedCallAppearanceManagerInterface::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   // Broadcast to all SCA manager instances
   // It's the SCA manager's responsibility to determine if the conversation passed in is of interest to it
   for (AccountMap::const_iterator it = mAccountMap.begin(); it != mAccountMap.end(); it++)
   {
      SipSharedCallAppearanceManagerImpl* scaManager = it->second;
      scaManager->onConversationEnded(conversation, args);
   }

   return kSuccess;
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceNewSubscriptionEvent& evt)
{
   return os << "SharedCallAppearanceNewSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceSubscriptionStateChangedEvent& evt)
{
   return os << "SharedCallAppearanceSubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceSubscriptionEndedEvent& evt)
{
   return os << "SharedCallAppearanceSubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceStateChangedEvent& evt)
{
   return os << "SharedCallAppearanceStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceMakeExclusiveSuccessEvent& evt)
{
   return os << "SharedCallAppearanceMakeExclusiveSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceMakeExclusiveFailureEvent& evt)
{
   return os << "SharedCallAppearanceMakeExclusiveFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const SipSharedCallAppearance::ErrorEvent& evt)
{
   return os << "SipSharedCallAppearance::ErrorEvent";
}

}
}

#endif // CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE
