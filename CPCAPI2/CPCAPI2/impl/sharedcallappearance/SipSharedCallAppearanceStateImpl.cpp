#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)

#include "SipSharedCallAppearanceStateImpl.h"
#include "SipSharedCallAppearanceHelper.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

SipSharedCallAppearanceState SipSharedCallAppearanceStateImpl::SCA_STATE_NOT_FOUND;
SipSharedCallAppearanceCallInfo SipSharedCallAppearanceStateImpl::APPEARANCE_CALL_NOT_FOUND;

SipSharedCallAppearanceStateImpl::SipSharedCallAppearanceStateImpl(SipSharedCallAppearanceManagerInterface* scaManagerIf)
   : mScaManagerIf(scaManagerIf)
{
}

SipSharedCallAppearanceStateImpl::~SipSharedCallAppearanceStateImpl()
{
}

void SipSharedCallAppearanceStateImpl::Release()
{
   delete this;
}

int SipSharedCallAppearanceStateImpl::getState(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceSetState& scaSetState)
{
   std::map<SipSharedCallAppearanceSetHandle, SipSharedCallAppearanceSetState>::const_iterator it = mStateMap.find(scaSet);
   if (it != mStateMap.end())
   {
      scaSetState = it->second;
      return kSuccess;
   }

   return kError;
}

int SipSharedCallAppearanceStateImpl::getState(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceState& scaState)
{
   SipSharedCallAppearanceSetState scaSetState;
   if (getState(scaSet, scaSetState) == kSuccess)
   {
      scaState = getSharedCallAppearanceState(scaSetState, sca, false);
      return !scaState.sca.empty() ? kSuccess : kError;
   }

   return kError;
}

int SipSharedCallAppearanceStateImpl::getCall(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, SipSharedCallAppearanceCallInfo& call)
{
   SipSharedCallAppearanceState scaState;
   if (getState(scaSet, sca, scaState) == kSuccess)
   {
      call = getAppearanceCall(scaState, appearance);
      return !call.dialog.id.empty() ? kSuccess : kError;
   }

   return kError;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceNewSubscription(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceNewSubscriptionEvent& args)
{
   SipSharedCallAppearanceState& scaState = getSharedCallAppearanceState(args.account, scaSet, args.sca);
   scaState.subscriptionStarted = true;
   scaState.subscriptionState = CPCAPI2::SipEvent::SipSubscriptionState_Pending;
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceSubscriptionStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionStateChangedEvent& args)
{
   SipSharedCallAppearanceState& scaState = getSharedCallAppearanceState(args.account, scaSet, args.sca);
   scaState.subscriptionState = args.subscriptionState;
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceSubscriptionEnded(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionEndedEvent& args)
{
   SipSharedCallAppearanceState& scaState = getSharedCallAppearanceState(args.account, scaSet, args.sca);
   scaState.subscriptionState = CPCAPI2::SipEvent::SipSubscriptionState_Terminated;
   scaState.subscriptionStarted = false;
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceStateChangedEvent& args)
{
   SipSharedCallAppearanceState& scaState = getSharedCallAppearanceState(args.account, scaSet, args.sca);
   scaState.calls = args.calls;
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceMakeExclusiveSuccess(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveSuccessEvent& args)
{
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onSharedCallAppearanceMakeExclusiveFailure(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveFailureEvent& args)
{
   return kSuccess;
}

int SipSharedCallAppearanceStateImpl::onError(SipSharedCallAppearanceSetHandle scaSet, const ErrorEvent& args)
{
   return kSuccess;
}

SipSharedCallAppearanceState& SipSharedCallAppearanceStateImpl::getSharedCallAppearanceState(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{  
   std::map<SipSharedCallAppearanceSetHandle, SipSharedCallAppearanceSetState>::iterator it = mStateMap.find(scaSet);
   if (it == mStateMap.end())
   {
      // Create a new SCA set state entry in the map
      SipSharedCallAppearanceSetState scaSetState;
      scaSetState.account = account;
      scaSetState.scaSet = scaSet;
      mStateMap[scaSet] = scaSetState;
   }

   return getSharedCallAppearanceState(mStateMap[scaSet], sca, true);
}

SipSharedCallAppearanceState& SipSharedCallAppearanceStateImpl::getSharedCallAppearanceState(SipSharedCallAppearanceSetState& scaSetState, SipSharedCallAppearanceHandle sca, bool create)
{
   // Cycle through the SCA states to find the SCA specified and update its state
   for (unsigned int i = 0; i <  scaSetState.scaStates.size(); i++)
   {
      SipSharedCallAppearanceState& scaState = scaSetState.scaStates[i];
      if (scaState.sca == sca)
      {
         // SCA found
         return scaState;
      }
   }

   if (create)
   {
      // SCA not found
      // Add a new entry to the SCA states
      SipSharedCallAppearanceState scaState;
      scaState.sca = sca;
      scaSetState.scaStates.push_back(scaState);
      return scaSetState.scaStates.back();
   }
   else
   {   
      return SCA_STATE_NOT_FOUND;
   }
}

SipSharedCallAppearanceCallInfo& SipSharedCallAppearanceStateImpl::getAppearanceCall(SipSharedCallAppearanceState& scaState, int appearance)
{
   // Cycle through all the calls on the SCA and find the one with the appearance number specified
   for (unsigned int i = 0; i <  scaState.calls.size(); i++)
   {
      SipSharedCallAppearanceCallInfo& call = scaState.calls[i];
      if (call.dialog.appearance == appearance)
      {
         return call;
      }
   }

   return APPEARANCE_CALL_NOT_FOUND;
}

}
}

#endif // CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE