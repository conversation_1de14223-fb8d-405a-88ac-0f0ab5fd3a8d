#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)

#include "SipSharedCallAppearanceManagerImpl.h"
#include "SipSharedCallAppearanceStateImpl.h"
#include "SipSharedCallAppearanceHelper.h"
#include "cpm/CpmHelper.h"
#include "../dialogevent/DialogInfoDocumentHelper.h"
#include "../util/ResipConv.h"

using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipConversation;

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

const SharedCallAppearancePair SipSharedCallAppearanceManagerImpl::SCA_SET_NOT_FOUND;

SipSharedCallAppearanceManagerImpl::SipSharedCallAppearanceManagerImpl(SipSharedCallAppearanceManagerInterface* iff, CPCAPI2::SipAccount::SipAccountImpl& account, SipSharedCallAppearanceStateManager* stateManager, SipDialogEventSubscriptionManagerInterface* sipDialogEventSubscriptionIf, SipDialogEventPublicationManagerInterface* sipDialogEventPublicationIf, SipAVConversationManagerInterface *sipConversationIf)
   : mInterface(iff),
     mAccount(account),
     mStateManager(stateManager),
     mSipDialogEventSubscriptionIf(sipDialogEventSubscriptionIf),
     mSipDialogEventPublicationIf(sipDialogEventPublicationIf),
     mSipConversationIf(sipConversationIf)
{
}

SipSharedCallAppearanceManagerImpl::~SipSharedCallAppearanceManagerImpl()
{
   // Destroy the SCA set info objects
   for (SipSharedCallAppearanceSetInfoMap::const_iterator it = mSharedCallAppearanceSetInfoMap.begin(); it != mSharedCallAppearanceSetInfoMap.end(); it++)
   {
      SipSharedCallAppearanceSetInfo* scaSetInfo = it->second;
      delete scaSetInfo;
   }
}

void SipSharedCallAppearanceManagerImpl::startSubscriptions(SipSharedCallAppearanceSetInfo* scaSetInfo)
{
   assert(scaSetInfo);

   // Create the dialog-event related subscription settings
   SipDialogEventSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = scaSetInfo->mSettings.expires;
   subsSettings.enableSharedAppearanceDialogExtensions = true;
   if (scaSetInfo->mSettings.useSharedDialogParameter)
      subsSettings.additionalEventParameterName = DialogInfoDocumentHelper::SHARED_APPEARANCE_DIALOG_EXTENSIONS_PARAM_NAME;
   subsSettings.includeSessionDescription = false;

   // Subscribe each SCA individually
   for (SipSharedCallAppearanceInfoMap::const_iterator it = scaSetInfo->mSharedCallAppearanceInfo.begin(); it != scaSetInfo->mSharedCallAppearanceInfo.end(); it++)
   {
      SipSharedCallAppearanceHandle sca = it->first;
      SipSharedCallAppearanceInfo* scaInfo = it->second;
      assert(scaInfo);
      
      // Create and start the subscription (we're already on SDK thread here)
      SipDialogEventSubscriptionHandle subscription = mSipDialogEventSubscriptionIf->createSubscriptionImpl(mAccount.getHandle());
      mSipDialogEventSubscriptionIf->applySubscriptionSettingsImpl(subscription, subsSettings);
      mSipDialogEventSubscriptionIf->addParticipantImpl(subscription, sca);
      mSipDialogEventSubscriptionIf->startImpl(subscription);

      // Associate the subscription with the SCA
      assert(subscription > 0);
      scaInfo->mSubscription = subscription;
   }
}

void SipSharedCallAppearanceManagerImpl::endSubscriptions(SipSharedCallAppearanceSetInfo* scaSetInfo)
{
   assert(scaSetInfo);

   // Unsubscribe each SCA individually
   for (SipSharedCallAppearanceInfoMap::const_iterator it = scaSetInfo->mSharedCallAppearanceInfo.begin(); it != scaSetInfo->mSharedCallAppearanceInfo.end(); it++)
   {
      SipSharedCallAppearanceInfo* scaInfo = it->second;
      assert(scaInfo);

      SipDialogEventSubscriptionHandle subscription = scaInfo->mSubscription;
      mSipDialogEventSubscriptionIf->endImpl(subscription);
   }
}

SipConversationHandle SipSharedCallAppearanceManagerImpl::makeCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   assert(scaSetInfo);

   // Create the conversation, add the destination's URI and start the conversation
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount.getHandle());
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);
   mSipConversationIf->setFromAddress(conversation, sca);
   mSipConversationIf->addHeader(conversation, SipSharedCallAppearanceHelper::createPreferredIdentityHeader(mAccount));
   mSipConversationIf->addParticipant(conversation, to);
   mSipConversationIf->start(conversation);

   return conversation;
}

SipConversationHandle SipSharedCallAppearanceManagerImpl::makeCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& audioDescriptor, const CPCAPI2::SipConversation::MediaInfo& videoDescriptor)
{
   assert(scaSetInfo);

   // Create the conversation, add the destination's URI and start the conversation
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount.getHandle());
   mSipConversationIf->configureMedia(conversation, audioDescriptor);
   mSipConversationIf->configureMedia(conversation, videoDescriptor);
   mSipConversationIf->setFromAddress(conversation, sca);
   mSipConversationIf->addHeader(conversation, SipSharedCallAppearanceHelper::createPreferredIdentityHeader(mAccount));
   mSipConversationIf->addParticipant(conversation, to);
   mSipConversationIf->start(conversation);
      
   return conversation;
}

SipConversationHandle SipSharedCallAppearanceManagerImpl::makeConferenceCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::vector<SipConversationHandle>& otherConversations, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   assert(scaSetInfo);

   // Get info on the SCA
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[sca];
   assert(scaInfo);

   // Create the conversation, add the conference-factory's URI and start the conversation
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount.getHandle());
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);
   mSipConversationIf->setFromAddress(conversation, sca);
   mSipConversationIf->addHeader(conversation, SipSharedCallAppearanceHelper::createPreferredIdentityHeader(mAccount));
   mSipConversationIf->addParticipant(conversation, scaSetInfo->mSettings.conferenceFactoryUri);
   mSipConversationIf->start(conversation);

   // Store the context around the transfer entities involved
   scaInfo->mConversation = conversation;
   scaInfo->mConversationsToTransfer = otherConversations;

   return conversation;
}

void SipSharedCallAppearanceManagerImpl::scapHold(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, SipConversationHandle conversation)
{
   assert(scaSetInfo);
   assert(conversation);

   // Get info on the SCA
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[sca];
   assert(scaInfo);

   // Verify that the SCAP hold operation is allowed
   if (!scaInfo->mSettings.allowScapHoldJoin)
   {
      fireError(scaSetInfo->mHandle, "Not allowed to perform a SCAP hold operation on SCA: " + sca);
      return;
   }

   // Transfer the call to the SCAP server. The server is responsible for terminating the call once the transfer has succeeded.
   mSipConversationIf->transfer(conversation, scaSetInfo->mSettings.scapHoldUri);
}

SipConversationHandle SipSharedCallAppearanceManagerImpl::scapJoin(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   assert(scaSetInfo);

   // Get info on the SCA
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[sca];
   assert(scaInfo);

   // Verify that the SCAP join operation is allowed
   if (!scaInfo->mSettings.allowScapHoldJoin)
   {
      fireError(scaSetInfo->mHandle, "Not allowed to perform a SCAP join operation on SCA: " + sca);
      return 0;
   }

   // Get call information on the selected SCA
   CPCAPI2::SipDialogEvent::DialogInfo dialogInfo = getDialogForAppearance(scaSetInfo->mHandle, sca, appearance);
   if (!SipSharedCallAppearanceHelper::isCall(dialogInfo))
   {
      fireError(scaSetInfo->mHandle, "Cannot perform SCAP join operation on SCA - No call available: " + sca);
      return 0;
   }

   // Make sure the call on the SCA is in 'Confirmed' state
   if (dialogInfo.stateInfo.state != DialogState_Confirmed)
   {
      fireError(scaSetInfo->mHandle, "Cannot perform SCAP join operation on SCA - Call not in confirmed state: " + sca);
      return 0;
   }

   // Make sure the call on the SCA is SCAP held
   if (!SipSharedCallAppearanceHelper::isScapHeld(dialogInfo) && !SipSharedCallAppearanceHelper::isHeld(dialogInfo))
   {
      fireError(scaSetInfo->mHandle, "Cannot perform SCAP join operation on SCA - Call not in SCAP held mode: " + sca);
      return 0;
   }

   // Make sure the dialog ID is valid
   assert(SipSharedCallAppearanceHelper::isDialogIdComplete(dialogInfo));

   // Create a new conversation with the remote endpoint
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount.getHandle());
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);
   mSipConversationIf->setFromAddress(conversation, sca);
   mSipConversationIf->addHeader(conversation, SipSharedCallAppearanceHelper::createPreferredIdentityHeader(mAccount));
   mSipConversationIf->addParticipant(conversation, sca);

   // Set Replaces header with Dialog-ID of remote call
   mSipConversationIf->setCallToReplace(conversation, dialogInfo.dialogId.callId, dialogInfo.dialogId.localTag, dialogInfo.dialogId.remoteTag);

   // Start the conversation
   mSipConversationIf->start(conversation);

   return conversation;
}

SipConversationHandle SipSharedCallAppearanceManagerImpl::scapBridgeIn(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor)
{
   assert(scaSetInfo);

   // Get info on the SCA
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[sca];
   assert(scaInfo);

   // Verify that the SCAP join operation is allowed
   if (!scaInfo->mSettings.allowScapBridgeIn)
   {
      fireError(scaSetInfo->mHandle, "Not allowed to perform a SCAP bridge-in operation on SCA: " + sca);
      return 0;
   }

   // Get call information on the selected SCA
   CPCAPI2::SipDialogEvent::DialogInfo dialogInfo = getDialogForAppearance(scaSetInfo->mHandle, sca, appearance);
   if(!SipSharedCallAppearanceHelper::isCall(dialogInfo))
   {
      fireError(scaSetInfo->mHandle, "Cannot perform bridge-in operation on SCA - No call available: " + sca);
      return 0;
   }

   // Make sure the call on the SCA is in 'Confirmed' state
   if (dialogInfo.stateInfo.state != DialogState_Confirmed)
   {
      fireError(scaSetInfo->mHandle, "Cannot perform bridge-in operation on SCA - Call not in confirmed state: " + sca);
      return 0;
   }

   // Make sure this is not an exclusive call
   if (dialogInfo.exclusive)
   {
      fireError(scaSetInfo->mHandle, "Cannot perform bridge-in operation on SCA - Exclusive call: " + sca);
      return 0;
   }

   // Make sure the call is not in held or SCAP held
   if (SipSharedCallAppearanceHelper::isScapHeld(dialogInfo) || SipSharedCallAppearanceHelper::isHeld(dialogInfo) || SipSharedCallAppearanceHelper::isParked(dialogInfo))
   {
      fireError(scaSetInfo->mHandle, "Cannot perform bridge-in operation on SCA - Call is held or SCAP held: " + sca);
      return 0;
   }

   // Make sure the dialog ID is valid
   assert(SipSharedCallAppearanceHelper::isDialogIdComplete(dialogInfo));

   // Create a new conversation with the remote endpoint
   SipConversationHandle conversation = mSipConversationIf->createConversation(mAccount.getHandle());
   mSipConversationIf->configureMedia(conversation, mediaDescriptor);
   mSipConversationIf->setFromAddress(conversation, sca);
   mSipConversationIf->addHeader(conversation, SipSharedCallAppearanceHelper::createPreferredIdentityHeader(mAccount));
   mSipConversationIf->addParticipant(conversation, sca);

   // Set Join header with Dialog-ID of remote call
   cpc::vector<Parameter> joinHeaderParams;
   mSipConversationIf->setCallToJoin(conversation, dialogInfo.dialogId.callId, dialogInfo.dialogId.localTag, dialogInfo.dialogId.remoteTag, joinHeaderParams);

   // Start the conversation
   mSipConversationIf->start(conversation);

   return conversation;
}

void SipSharedCallAppearanceManagerImpl::makeExclusive(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, bool exclusive)
{
   assert(scaSetInfo);

   // Get info on the SCA
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[sca];
   assert(scaInfo);

   // Verify that the make exclusive operation is allowed
   if (!scaInfo->mSettings.allowMakeExclusiveCalls)
   {
      fireError(scaSetInfo->mHandle, "Not allowed to perform a make exclusive operation on SCA: " + sca);
      return;
   }

   // Get call information on the selected SCA
   CPCAPI2::SipDialogEvent::DialogInfo dialogInfo = getDialogForAppearance(scaSetInfo->mHandle, sca, appearance);
   if(!SipSharedCallAppearanceHelper::isCall(dialogInfo))
   {
      fireError(scaSetInfo->mHandle, "Cannot perform make exclusive operation on SCA - No call available: " + sca);
      return;
   }

   // Make sure the call on the SCA is in 'Confirmed' state
   if (dialogInfo.stateInfo.state != DialogState_Confirmed)
   {
      fireError(scaSetInfo->mHandle, "Cannot perform make exclusive operation on SCA - Call not in confirmed state: " + sca);
      return;
   }

   // Make sure the call on the SCA is not already in the same exclusive state as the one requested
   if (dialogInfo.exclusive == exclusive)
   {
      fireError(scaSetInfo->mHandle, "Cannot perform make exclusive operation on SCA - Call is already in the specified exclusive state: " + sca);
      return;
   }

   // Create the dialog-event related publication settings
   DialogInfoDocument dialogInfoDoc = DialogInfoDocumentHelper::createEmptyDocument(1, sca);
   dialogInfo.exclusive = exclusive;
   dialogInfoDoc.dialogs.push_back(dialogInfo);

   // Create and start the publication
   SipDialogEventPublicationSettings pubSettings;
   pubSettings.expiresSeconds = 0; // This is what ALU expects i.e. a one time publish
   SipDialogEventPublicationHandle publication = mSipDialogEventPublicationIf->createPublication(mAccount.getHandle(), pubSettings);
   mSipDialogEventPublicationIf->setTarget(publication, sca);
   mSipDialogEventPublicationIf->publish(publication, dialogInfoDoc);

   // Keep the publication handle for the callbacks
   scaInfo->mPublication = publication;
}   

SipSharedCallAppearanceSetInfo* SipSharedCallAppearanceManagerImpl::createSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle scaSetHandle)
{
   assert(mSharedCallAppearanceSetInfoMap[scaSetHandle] == NULL);

   SipSharedCallAppearanceSetInfo* scaSetInfo = new SipSharedCallAppearanceSetInfo(scaSetHandle);
   scaSetInfo->mAccountHandle = mAccount.getHandle();
   mSharedCallAppearanceSetInfoMap[scaSetHandle] = scaSetInfo;

   return scaSetInfo;
}

SipSharedCallAppearanceSetInfo* SipSharedCallAppearanceManagerImpl::getSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle scaSet) const
{
   SipSharedCallAppearanceSetInfoMap::const_iterator it = mSharedCallAppearanceSetInfoMap.find(scaSet);
   return (it != mSharedCallAppearanceSetInfoMap.end()) ? it->second : NULL;
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceNewSubscription(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceNewSubscriptionEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceNewSubscription), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceSubscriptionStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionStateChangedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceSubscriptionStateChanged), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceSubscriptionEnded(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionEndedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceSubscriptionEnded), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceStateChangedEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceStateChanged), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceMakeExclusiveSuccess(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveSuccessEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceMakeExclusiveSuccess), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireSharedCallAppearanceMakeExclusiveFailure(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveFailureEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onSharedCallAppearanceMakeExclusiveFailure), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireError(SipSharedCallAppearanceSetHandle scaSet, const ErrorEvent& event)
{
   mInterface->fireEvent(cpcEvent(SipSharedCallAppearanceHandler, onError), scaSet, event);
}

void SipSharedCallAppearanceManagerImpl::fireError(SipSharedCallAppearanceSetHandle scaSet, const cpc::string& msg)
{
   // Create the event
   ErrorEvent event;
   event.account = mAccount.getHandle();
   event.errorText = msg;

   // Publish the event
   fireError(scaSet, event);
}

int SipSharedCallAppearanceManagerImpl::onNewSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NewDialogEventSubscriptionEvent& args)
{
   // Get the SCA that contains the subscription
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForSubscription(subscription);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceNewSubscriptionEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;

   // Publish the event
   fireSharedCallAppearanceNewSubscription(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onSubscriptionEnded(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionEndedEvent& args)
{
   // Get the SCA that contains the subscription
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForSubscription(subscription);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceSubscriptionEndedEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;
   scaArgs.endReason = args.endReason;

   // Publish the event
   fireSharedCallAppearanceSubscriptionEnded(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onIncomingDialogInfo(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::IncomingDialogInfoEvent& args)
{
   // Get the SCA that contains the subscription
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForSubscription(subscription);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceStateChangedEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;
   scaArgs.calls = createCalls(scaPair.sca, args.dialogInfoDoc);

   // Publish the event
   fireSharedCallAppearanceStateChanged(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onDialogResourceListUpdated(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogResourceListEvent& args)
{
   // not yet used
   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onSubscriptionStateChanged(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionStateChangedEvent& args)
{
   // Get the SCA that contains the subscription
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForSubscription(subscription);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceSubscriptionStateChangedEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;
   scaArgs.subscriptionState = args.subscriptionState;

   // Publish the event
   fireSharedCallAppearanceSubscriptionStateChanged(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onNotifyDialogInfoFailure(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NotifyDialogInfoFailureEvent& args)
{
   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onError(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::ErrorEvent& args)
{
   // Get the SCA that contains the subscription
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForSubscription(subscription);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   ErrorEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;
   scaArgs.errorText = args.errorText;

   // Publish the event
   fireError(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onPublicationSuccess(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationSuccessEvent& args)
{
   // NOTE: In a successful publication, onPublicationRemove() is called because the PUBLISH has its Expires header set to 0
   assert(false);

   return kError;
}

int SipSharedCallAppearanceManagerImpl::onPublicationFailure(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationFailureEvent& args)
{
   // Get the SCA that contains the publication
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForPublication(publication);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceMakeExclusiveFailureEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;

   // Publish the event
   fireSharedCallAppearanceMakeExclusiveFailure(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onPublicationRemove(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationRemoveEvent & args)
{
   // NOTE: This is the success case because the PUBLISH has its Expires header set to 0

   // Get the SCA that contains the publication
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForPublication(publication);
   if (!isScaPairValid(scaPair))
      return kError;

   // Create the event
   SharedCallAppearanceMakeExclusiveSuccessEvent scaArgs;
   scaArgs.account = mAccount.getHandle();
   scaArgs.sca = scaPair.sca;

   // Publish the event
   fireSharedCallAppearanceMakeExclusiveSuccess(scaPair.scaSet, scaArgs);

   return kSuccess;
}

int SipSharedCallAppearanceManagerImpl::onError(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationErrorEvent& args)
{
   assert(false);

   return kSuccess;
}

SharedCallAppearancePair SipSharedCallAppearanceManagerImpl::getSharedCallAppearanceForSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription)
{
   // Browse through all the SCA sets to find the one that contains the subscription specified
   for (SipSharedCallAppearanceSetInfoMap::const_iterator scaSetInfoIter = mSharedCallAppearanceSetInfoMap.begin(); scaSetInfoIter != mSharedCallAppearanceSetInfoMap.end(); scaSetInfoIter++)
   {
      SipSharedCallAppearanceSetHandle scaSet = scaSetInfoIter->first;
      SipSharedCallAppearanceSetInfo* scaSetInfo  = scaSetInfoIter->second;
      assert(scaSetInfo);

      // Browse through all the subscriptions in the SCA set to find the subscription specified
      for (SipSharedCallAppearanceInfoMap::const_iterator scaInfoIter = scaSetInfo->mSharedCallAppearanceInfo.begin(); scaInfoIter != scaSetInfo->mSharedCallAppearanceInfo.end(); scaInfoIter++)
      {
         SipSharedCallAppearanceHandle sca = scaInfoIter->first;
         SipSharedCallAppearanceInfo* scaInfo = scaInfoIter->second;
         assert(scaInfo);

         // Check to see if this is the subscription we are looking for
         if (scaInfo->mSubscription == subscription)
         {
            // Subscription found. Return SCA
            SharedCallAppearancePair scaPair;
            scaPair.sca = sca;
            scaPair.scaSet = scaSet;
            return scaPair;
         }
      }
   }

   // SCA set not found
   return SCA_SET_NOT_FOUND;
}

SharedCallAppearancePair SipSharedCallAppearanceManagerImpl::getSharedCallAppearanceForPublication(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication)
{
   // Browse through all the SCA sets to find the one that contains the publication specified
   for (SipSharedCallAppearanceSetInfoMap::const_iterator scaSetInfoIter = mSharedCallAppearanceSetInfoMap.begin(); scaSetInfoIter != mSharedCallAppearanceSetInfoMap.end(); scaSetInfoIter++)
   {
      SipSharedCallAppearanceSetHandle scaSet = scaSetInfoIter->first;
      SipSharedCallAppearanceSetInfo* scaSetInfo  = scaSetInfoIter->second;
      assert(scaSetInfo);

      // Browse through all the publications in the SCA set to find the publication specified
      for (SipSharedCallAppearanceInfoMap::const_iterator scaInfoIter = scaSetInfo->mSharedCallAppearanceInfo.begin(); scaInfoIter != scaSetInfo->mSharedCallAppearanceInfo.end(); scaInfoIter++)
      {
         SipSharedCallAppearanceHandle sca = scaInfoIter->first;
         SipSharedCallAppearanceInfo* scaInfo = scaInfoIter->second;
         assert(scaInfo);

         // Check to see if this is the publication we are looking for
         if (scaInfo->mPublication == publication)
         {
            // Publication found. Return SCA
            SharedCallAppearancePair scaPair;
            scaPair.sca = sca;
            scaPair.scaSet = scaSet;
            return scaPair;
         }
      }
   }

   // SCA set not found
   return SCA_SET_NOT_FOUND;
}

SharedCallAppearancePair SipSharedCallAppearanceManagerImpl::getSharedCallAppearanceForConversation(CPCAPI2::SipConversation::SipConversationHandle conversation)
{
   // Browse through all the SCA sets to find the one that contains the conversation specified
   for (SipSharedCallAppearanceSetInfoMap::const_iterator scaSetInfoIter = mSharedCallAppearanceSetInfoMap.begin(); scaSetInfoIter != mSharedCallAppearanceSetInfoMap.end(); scaSetInfoIter++)
   {
      SipSharedCallAppearanceSetHandle scaSet = scaSetInfoIter->first;
      SipSharedCallAppearanceSetInfo* scaSetInfo  = scaSetInfoIter->second;
      assert(scaSetInfo);

      // Browse through all the conversations in the SCA set to find the conversation specified
      for (SipSharedCallAppearanceInfoMap::const_iterator scaInfoIter = scaSetInfo->mSharedCallAppearanceInfo.begin(); scaInfoIter != scaSetInfo->mSharedCallAppearanceInfo.end(); scaInfoIter++)
      {
         SipSharedCallAppearanceHandle sca = scaInfoIter->first;
         SipSharedCallAppearanceInfo* scaInfo = scaInfoIter->second;
         assert(scaInfo);

         // Check to see if this is the conversation we are looking for
         if (scaInfo->mConversation == conversation)
         {
            // Conversation found. Return SCA
            SharedCallAppearancePair scaPair;
            scaPair.sca = sca;
            scaPair.scaSet = scaSet;
            return scaPair;
         }
      }
   }

   // SCA set not found
   return SCA_SET_NOT_FOUND;
}

cpc::vector<SipSharedCallAppearanceCallInfo> SipSharedCallAppearanceManagerImpl::createCalls(SipSharedCallAppearanceHandle sca, const DialogInfoDocument& dialogInfoDoc)
{
   cpc::vector<SipSharedCallAppearanceCallInfo> ret;

   for (cpc::vector<DialogInfo>::const_iterator it = dialogInfoDoc.dialogs.begin(); it != dialogInfoDoc.dialogs.end(); it++)
   {
      DialogInfo dialogInfo = *it;
      SipSharedCallAppearanceCallInfo callInfo;
      callInfo.sca = sca;
      callInfo.dialog = dialogInfo;
      callInfo.isHeld = SipSharedCallAppearanceHelper::isHeld(dialogInfo);
      callInfo.isParked = SipSharedCallAppearanceHelper::isParked(dialogInfo);
      callInfo.isScapHeld = SipSharedCallAppearanceHelper::isScapHeld(dialogInfo);
      ret.push_back(callInfo);
   }

   return ret;
}

CPCAPI2::SipDialogEvent::DialogInfo SipSharedCallAppearanceManagerImpl::getDialogForAppearance(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance)
{
   // Get the call on the appearance specified
   SipSharedCallAppearanceCallInfo callInfo;
   mStateManager->getCall(scaSet, sca, appearance, callInfo);

   return callInfo.dialog;
}

void SipSharedCallAppearanceManagerImpl::onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args)
{
   // Check to see if this is a conversation of interest
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForConversation(conversation);
   if (!isScaPairValid(scaPair))
   {
      return;
   }

   // This is a conversation related to a transfer to a conference

   // Get the SCA info
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaPair.scaSet);
   assert(scaSetInfo);
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[scaPair.sca];
   assert(scaInfo);

   // Get the contact address (from the 200/OK) which contains the address to redirect the conversations to
   resip::NameAddr contactHeader;
   if (ResipConv::stringToAddr(args.contactHeaderField, contactHeader))
   {
      cpc::string contactAddress = contactHeader.uri().toString().c_str();

      // Transfer the conversations to the conference
      for (cpc::vector<SipConversationHandle>::const_iterator it = scaInfo->mConversationsToTransfer.begin(); it != scaInfo->mConversationsToTransfer.end(); it++)
      {
         // Intiate the transfer
         mSipConversationIf->transfer(*it, contactAddress);
      }

      // Clear the cached info
      scaInfo->mConversation = 0;
      scaInfo->mConversationsToTransfer.clear();
   }
}

void SipSharedCallAppearanceManagerImpl::onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args)
{
   // Check to see if this is a conversation of interest
   SharedCallAppearancePair scaPair = getSharedCallAppearanceForConversation(conversation);
   if (!isScaPairValid(scaPair))
   {
      return;
   }

   // This is a conversation related to a transfer to a conference

   // Get the SCA info
   SipSharedCallAppearanceSetInfo* scaSetInfo = getSharedCallAppearanceSetInfo(scaPair.scaSet);
   assert(scaSetInfo);
   SipSharedCallAppearanceInfo* scaInfo = scaSetInfo->mSharedCallAppearanceInfo[scaPair.sca];
   assert(scaInfo);

   // Clear the cached info
   scaInfo->mConversation = 0;
   scaInfo->mConversationsToTransfer.clear();
}

bool SipSharedCallAppearanceManagerImpl::isScaPairValid(SharedCallAppearancePair scaPair) const
{
   if (scaPair.scaSet > 0 && !scaPair.sca.empty())
   {
      return true;
   }

   return false;
}

}
}

#endif // CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE
