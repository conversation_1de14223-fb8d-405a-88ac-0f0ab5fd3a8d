#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)
#include "SipSharedCallAppearanceHelper.h"
#include <assert.h>

using namespace CPCAPI2::SipDialogEvent;

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

const cpc::string SipSharedCallAppearanceHelper::SIP_RENDERING_PARAM = "+sip.rendering";
const cpc::string SipSharedCallAppearanceHelper::SIP_CALL_PARK_PARAM = "+sip.call-parked";
const cpc::string SipSharedCallAppearanceHelper::SIP_SCAP_HELD_PARAM = "+sip.scap-held";
const cpc::string SipSharedCallAppearanceHelper::APPEARANCE_PARAM = "appearance";
const cpc::string SipSharedCallAppearanceHelper::ALTERNATE_APPEARANCE_PARAM = "appearance-index";
const cpc::string SipSharedCallAppearanceHelper::PREFERRED_IDENTITY_HEADER = "P-Preferred-Identity";

cpc::string SipSharedCallAppearanceHelper::getParameterValue(const ParticipantInfo& participant, const cpc::string& name)
{
   for (cpc::vector<Parameter>::const_iterator it = participant.target.params.begin(); it != participant.target.params.end(); it++)
   {
      if (it->name == name)
      {
         // Parameter found
         return it->value;
      }
   }

   // Parameter not found
   return "";
}

bool SipSharedCallAppearanceHelper::isHeld(const DialogInfo& dialogInfo)
{
   return getParameterValue(dialogInfo.localParticipant, SIP_RENDERING_PARAM) == "no" ||
          getParameterValue(dialogInfo.remoteParticipant, SIP_RENDERING_PARAM) == "no";
}

bool SipSharedCallAppearanceHelper::isParked(const DialogInfo& dialogInfo)
{
   return getParameterValue(dialogInfo.localParticipant, SIP_CALL_PARK_PARAM) == "yes" ||
          getParameterValue(dialogInfo.remoteParticipant, SIP_CALL_PARK_PARAM) == "yes" ||
          dialogInfo.parkedParticipant.identity.address.size() > 0;
}

bool SipSharedCallAppearanceHelper::isScapHeld(const DialogInfo& dialogInfo)
{
   return getParameterValue(dialogInfo.localParticipant, SIP_SCAP_HELD_PARAM) == "yes" ||
          getParameterValue(dialogInfo.remoteParticipant, SIP_SCAP_HELD_PARAM) == "yes";
}

int SipSharedCallAppearanceHelper::getAppearance(const cpc::string& alertInfoHeader, const cpc::string& appearanceParamName)
{
   int pos = alertInfoHeader.find(appearanceParamName + "=");
   if (pos != cpc::string::npos)
   {
      cpc::string appearanceStr = alertInfoHeader.substr(pos + appearanceParamName.size() + 1);
      if (appearanceStr.size() > 0) 
      {
         return strtol(appearanceStr.c_str(), NULL, 10);
      }
   }

   return kError;
}

CPCAPI2::SipHeader SipSharedCallAppearanceHelper::createPreferredIdentityHeader(const CPCAPI2::SipAccount::SipAccountImpl& account)
{
   CPCAPI2::SipHeader ret;

   ret.header = PREFERRED_IDENTITY_HEADER;
   cpc::string uri = "sip:" + account.getSettings().username + "@" + account.getSettings().domain;
   ret.value = "<" + uri + ">";

   return ret;
}

bool SipSharedCallAppearanceHelper::isCall(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo)
{
   return !dialogInfo.id.empty() && dialogInfo.stateInfo.state != DialogState_NotSpecified && !dialogInfo.dialogId.callId.empty();
}

bool SipSharedCallAppearanceHelper::isDialogIdComplete(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo)
{
   return !dialogInfo.dialogId.callId.empty() && !dialogInfo.dialogId.localTag.empty() && !dialogInfo.dialogId.remoteTag.empty();
}

}
}
#endif
