#pragma once   

#if !defined(CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_IMPL_H)
#define CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_IMPL_H

#include "cpcapi2defs.h"
#include "sharedcallappearance/SipSharedCallAppearanceManager.h"
#include "sharedcallappearance/SipSharedCallAppearanceState.h"
#include "sharedcallappearance/SipSharedCallAppearanceHandler.h"
#include "phone/PhoneModule.h"

#include <map>

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{
class SipSharedCallAppearanceManagerInterface;

class SipSharedCallAppearanceStateImpl : public SipSharedCallAppearanceStateManager,
                                         public PhoneModule,
                                         public SipSharedCallAppearanceHandler

{
public:
   SipSharedCallAppearanceStateImpl(SipSharedCallAppearanceManagerInterface* scaManagerIf);
   virtual ~SipSharedCallAppearanceStateImpl();

   virtual void Release() OVERRIDE;

   // SharedCallAppearanceHandler interface
   virtual int getState(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceSetState& scaState) OVERRIDE;
   virtual int getState(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceState& scaState) OVERRIDE;
   virtual int getCall(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, SipSharedCallAppearanceCallInfo& call) OVERRIDE;

private:
   SipSharedCallAppearanceManagerInterface* mScaManagerIf;
   std::map<SipSharedCallAppearanceSetHandle, SipSharedCallAppearanceSetState> mStateMap;

   static SipSharedCallAppearanceState SCA_STATE_NOT_FOUND;
   static SipSharedCallAppearanceCallInfo APPEARANCE_CALL_NOT_FOUND;

   // SipSharedCallAppearanceHandler interface
   virtual int onSharedCallAppearanceNewSubscription(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceNewSubscriptionEvent& args) OVERRIDE;
   virtual int onSharedCallAppearanceSubscriptionStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionStateChangedEvent& args) OVERRIDE;
   virtual int onSharedCallAppearanceSubscriptionEnded(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionEndedEvent& args) OVERRIDE;
   virtual int onSharedCallAppearanceStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceStateChangedEvent& args) OVERRIDE;
   virtual int onSharedCallAppearanceMakeExclusiveSuccess(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveSuccessEvent& args) OVERRIDE;
   virtual int onSharedCallAppearanceMakeExclusiveFailure(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveFailureEvent& args) OVERRIDE;
   virtual int onError(SipSharedCallAppearanceSetHandle scaSet, const ErrorEvent& args);

   SipSharedCallAppearanceState& getSharedCallAppearanceState(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   SipSharedCallAppearanceState& getSharedCallAppearanceState(SipSharedCallAppearanceSetState& scaSetState, SipSharedCallAppearanceHandle sca, bool create);
   SipSharedCallAppearanceCallInfo& getAppearanceCall(SipSharedCallAppearanceState& scaState, int appearance);
};

}
}

#endif // CPCAPI2_SIP_SHARED_CALL_APPEARANCE_STATE_IMPL_H
