#pragma once

#if !defined(__CPCAPI2_SHARED_CALL_APPEARANCE_HELPER_H__)
#define __CPCAPI2_SHARED_CALL_APPEARANCE_HELPER_H__

#include "cpcapi2defs.h"
#include "sharedcallappearance/SipSharedCallAppearanceHandler.h"
#include "dialogevent/SipDialogEventModel.h"
#include "call/SipConversationManager.h"
#include "../account/SipAccountImpl.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

class SipSharedCallAppearanceHelper
{
public:
   static bool isHeld(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static bool isParked(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static bool isScapHeld(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static cpc::string getParameterValue(const CPCAPI2::SipDialogEvent::ParticipantInfo& participant, const cpc::string& name);
   static int getAppearance(const cpc::string& alertInfoHeader, const cpc::string& appearanceParamName);
   static CPCAPI2::SipHeader createPreferredIdentityHeader(const CPCAPI2::SipAccount::SipAccountImpl& account);
   static bool isCall(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);
   static bool isDialogIdComplete(const CPCAPI2::SipDialogEvent::DialogInfo& dialogInfo);

   static const cpc::string SIP_RENDERING_PARAM;
   static const cpc::string SIP_CALL_PARK_PARAM;
   static const cpc::string SIP_SCAP_HELD_PARAM;
   static const cpc::string APPEARANCE_PARAM;
   static const cpc::string ALTERNATE_APPEARANCE_PARAM;
   static const cpc::string PREFERRED_IDENTITY_HEADER;

private:
   SipSharedCallAppearanceHelper() {}
};

}
}

#endif // __CPCAPI2_SHARED_CALL_APPEARANCE_HELPER_H__
