#pragma once

#if !defined(__CPCAPI2_SIP_SHAD_CALL_APPEARANCE_SET_INFO_H__)
#define __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_SET_INFO_H__

#include "cpcapi2defs.h"
#include "sharedcallappearance/SipSharedCallAppearanceTypes.h"
#include "call/SipConversationTypes.h"

#include <map>

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

struct SipSharedCallAppearanceInfo
{
   SipSharedCallAppearanceInfo(SipSharedCallAppearanceHandle handle, const SipSharedCallAppearanceSettings& settings);
   ~SipSharedCallAppearanceInfo();
                      
   SipSharedCallAppearanceHandle mHandle;                                   // SIP URI i.e. SCA handle
   SipSharedCallAppearanceSettings mSettings;                               // SCA related settings
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle mSubscription; // Subscription
   CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle mPublication;   // Publication
   CPCAPI2::SipConversation::SipConversationHandle mConversation;           // Conversation
   cpc::vector<CPCAPI2::SipConversation::SipConversationHandle> mConversationsToTransfer; // List of conversations to transfer
};
typedef std::map<SipSharedCallAppearanceHandle, SipSharedCallAppearanceInfo*> SipSharedCallAppearanceInfoMap;

struct SipSharedCallAppearanceSetInfo
{
   SipSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle handle);
   ~SipSharedCallAppearanceSetInfo();

   SipSharedCallAppearanceSetHandle mHandle;
   CPCAPI2::SipAccount::SipAccountHandle mAccountHandle;
   SipSharedCallAppearanceSetSettings mSettings;
   SipSharedCallAppearanceInfoMap mSharedCallAppearanceInfo;

   static SipSharedCallAppearanceSetHandle nextSharedCallAppearanceSet;
};
typedef std::map<SipSharedCallAppearanceSetHandle, SipSharedCallAppearanceSetInfo*> SipSharedCallAppearanceSetInfoMap;

}
}

#endif // __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_SET_INFO_H__
