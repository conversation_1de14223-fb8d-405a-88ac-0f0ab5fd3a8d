#pragma once

#if !defined(__CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_INTERFACE_H__)
#define __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_INTERFACE_H__

#include "cpcapi2defs.h"
#include "sharedcallappearance/SipSharedCallAppearanceManager.h"
#include "sharedcallappearance/SipSharedCallAppearanceState.h"
#include "sharedcallappearance/SipSharedCallAppearanceHandler.h"
#include "dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "dialogevent/SipDialogEventPublicationManagerInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "account/SipAccountInterface.h"
#include "phone/PhoneModule.h"

#include <rutil/RecursiveMutex.hxx>

namespace CPCAPI2
{
class Phone;

namespace SipSharedCallAppearance
{
class SipSharedCallAppearanceManagerImpl;
struct SipSharedCallAppearanceSetInfo;

typedef std::map<SipAccount::SipAccountHandle, SipSharedCallAppearanceManagerImpl*> AccountMap;

class SipSharedCallAppearanceManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipSharedCallAppearanceHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                                public SipSharedCallAppearanceManager,
                                                public CPCAPI2::SipConversation::SipConversationHandlerInternal,
                                                public PhoneModule
{
public:
   SipSharedCallAppearanceManagerInterface(Phone* phone);
   virtual ~SipSharedCallAppearanceManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipSharedCallAppearanceManagerInterface);

   PhoneInterface* phoneInterface();

   // SipSharedCallAppearanceManager interface
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceHandler* handler) OVERRIDE;
   virtual SipSharedCallAppearanceSetHandle createSharedCallAppearanceSet(CPCAPI2::SipAccount::SipAccountHandle account, const SipSharedCallAppearanceSetSettings& settings) OVERRIDE;
   virtual int addSharedCallAppearance(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const SipSharedCallAppearanceSettings& scaSettings) OVERRIDE;
   virtual int start(SipSharedCallAppearanceSetHandle scaSet) OVERRIDE;
   virtual int end(SipSharedCallAppearanceSetHandle scaSet) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle makeCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle makeCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& audioDescriptor, const CPCAPI2::SipConversation::MediaInfo& videoDescriptor) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle makeConferenceCall(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const cpc::vector<CPCAPI2::SipConversation::SipConversationHandle>& otherConversations, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int scapHold(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, CPCAPI2::SipConversation::SipConversationHandle conversation) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle scapJoin(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual CPCAPI2::SipConversation::SipConversationHandle scapBridgeIn(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor) OVERRIDE;
   virtual int makeExclusive(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, int appearance, bool exclusive = true) OVERRIDE;
   virtual int getAppearanceForScapCall(const cpc::string& alertInfoHeader) OVERRIDE;

private:
   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* mSipDialogEventSubscriptionIf;
   CPCAPI2::SipDialogEvent::SipDialogEventPublicationManagerInterface* mSipDialogEventPublicationIf;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mSipConversationIf;
   SipSharedCallAppearanceStateManager *mStateManager;
   AccountMap mAccountMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipSharedCallAppearanceHandler*> mHandlers;
   PhoneInterface* mPhone;
   resip::RecursiveMutex mMutex;

   static const int MAX_SUBSCRIPTION_COUNT;

   // PhoneModule interface
   virtual void Release() OVERRIDE;

   // SipConversationHandler
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args) OVERRIDE;
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args) OVERRIDE;
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args) OVERRIDE { return kSuccess; }
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args) OVERRIDE { return kSuccess; }
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) OVERRIDE { return kSuccess; }
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args) OVERRIDE { return kSuccess; }
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args) OVERRIDE { return kSuccess; }
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args) OVERRIDE { return kSuccess; }
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle subscription, const CPCAPI2::SipConversation::ErrorEvent& args) OVERRIDE { return kSuccess; }
   virtual int onConversationInitiated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationInitiatedEvent& args ) OVERRIDE { return kSuccess; }

   void setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipSharedCallAppearanceHandler* handler);
   void createSharedCallAppearanceSetImpl(SipAccount::SipAccountHandle account, SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceSetSettings& settings);
   void addSharedCallAppearanceImpl(SipSharedCallAppearanceSetHandle scaSet, const SipSharedCallAppearanceHandle& sca, const SipSharedCallAppearanceSettings& scaSettings);
   void startImpl(SipSharedCallAppearanceSetHandle scaSet);
   void endImpl(SipSharedCallAppearanceSetHandle scaSet);
   SipSharedCallAppearanceManagerImpl* getSharedCallAppearanceManager(CPCAPI2::SipAccount::SipAccountHandle account) const;
   SipSharedCallAppearanceSetInfo* getSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle scaSet) const;
};

std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceNewSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceMakeExclusiveSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const SharedCallAppearanceMakeExclusiveFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipSharedCallAppearance::ErrorEvent& evt);
}
}

#endif // __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_INTERFACE_H__
