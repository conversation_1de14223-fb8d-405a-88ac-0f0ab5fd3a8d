#pragma once

#if !defined(__CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_IMPL_H__)
#define __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_IMPL_H__

#include "cpcapi2defs.h"
#include "SipSharedCallAppearanceSetInfo.h"
#include "SipSharedCallAppearanceStateImpl.h"
#include "SipSharedCallAppearanceManagerInterface.h"
#include "sharedcallappearance/SipSharedCallAppearanceHandler.h"
#include "dialogevent/SipDialogEventSubscriptionManagerInterface.h"
#include "dialogevent/SipDialogEventPublicationManagerInterface.h"
#include "call/SipAVConversationManagerInterface.h"
#include "account/SipAccountImpl.h"

namespace CPCAPI2
{
namespace SipSharedCallAppearance
{

struct SharedCallAppearancePair
{
   SipSharedCallAppearanceSetHandle scaSet;
   SipSharedCallAppearanceHandle sca;

   SharedCallAppearancePair()
   {
      scaSet = 0;
      sca = "";
   }
};

class SipSharedCallAppearanceManagerImpl : public CPCAPI2::EventSyncHandler<CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandler>,
                                           public CPCAPI2::EventSyncHandler<CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandler>
{
public:
   SipSharedCallAppearanceManagerImpl(SipSharedCallAppearanceManagerInterface* iff, CPCAPI2::SipAccount::SipAccountImpl& account, SipSharedCallAppearanceStateManager* stateManager, CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* sipDialogEventSubscriptionIf, CPCAPI2::SipDialogEvent::SipDialogEventPublicationManagerInterface* sipDialogEventPublicationIf, CPCAPI2::SipConversation::SipAVConversationManagerInterface* sipConversationIf);
   virtual ~SipSharedCallAppearanceManagerImpl();

   // Invoked by the interface
   SipSharedCallAppearanceSetInfo* createSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle handle);
   SipSharedCallAppearanceSetInfo* getSharedCallAppearanceSetInfo(SipSharedCallAppearanceSetHandle handle) const;
   void addSdkObserver(SipSharedCallAppearanceHandler* sdkObserver);
   void removeSdkObserver(SipSharedCallAppearanceHandler* sdkObserver);
   void setHandler(SipSharedCallAppearanceHandler* handler);
   void startSubscriptions(SipSharedCallAppearanceSetInfo* scaSetInfo);
   void endSubscriptions(SipSharedCallAppearanceSetInfo* scaSetInfo);
   CPCAPI2::SipConversation::SipConversationHandle makeCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor);
   CPCAPI2::SipConversation::SipConversationHandle makeCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::string& to, const CPCAPI2::SipConversation::MediaInfo& audioDescriptor, const CPCAPI2::SipConversation::MediaInfo& videoDescriptor);
   CPCAPI2::SipConversation::SipConversationHandle makeConferenceCall(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, const cpc::vector<CPCAPI2::SipConversation::SipConversationHandle>& scas, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor);
   void scapHold(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, CPCAPI2::SipConversation::SipConversationHandle conversation);
   CPCAPI2::SipConversation::SipConversationHandle scapJoin(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor);
   CPCAPI2::SipConversation::SipConversationHandle scapBridgeIn(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, const CPCAPI2::SipConversation::MediaInfo& mediaDescriptor);
   void makeExclusive(SipSharedCallAppearanceSetInfo* scaSetInfo, const SipSharedCallAppearanceHandle& sca, int appearance, bool exclusive);
   void fireError(SipSharedCallAppearanceSetHandle scaSetHandle, const cpc::string& msg);
   void onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);
   void onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);

private:
   SipSharedCallAppearanceManagerInterface* mInterface;
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   SipSharedCallAppearanceStateManager* mStateManager;
   CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionManagerInterface* mSipDialogEventSubscriptionIf;
   CPCAPI2::SipDialogEvent::SipDialogEventPublicationManagerInterface* mSipDialogEventPublicationIf;
   CPCAPI2::SipConversation::SipAVConversationManagerInterface* mSipConversationIf;
   SipSharedCallAppearanceSetInfoMap mSharedCallAppearanceSetInfoMap;

   static const SharedCallAppearancePair SCA_SET_NOT_FOUND;

   // SipDialogEventSubscriptionHandler interface
   virtual int onNewSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NewDialogEventSubscriptionEvent& args);
   virtual int onSubscriptionEnded(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionEndedEvent& args);
   virtual int onIncomingDialogInfo(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::IncomingDialogInfoEvent& args);
   virtual int onDialogResourceListUpdated(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogResourceListEvent& args);
   virtual int onSubscriptionStateChanged(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::DialogEventSubscriptionStateChangedEvent& args);
   virtual int onNotifyDialogInfoFailure(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::NotifyDialogInfoFailureEvent& args);
   virtual int onError(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription, const CPCAPI2::SipDialogEvent::ErrorEvent& args);

   // SipDialogEventPublicationHandler interface
   virtual int onPublicationSuccess(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationSuccessEvent& args);
   virtual int onPublicationFailure(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationFailureEvent& args);
   virtual int onPublicationRemove(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationRemoveEvent & args);
   virtual int onError(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication, const CPCAPI2::SipDialogEvent::DialogEventPublicationErrorEvent& args);

   cpc::vector<SipSharedCallAppearanceCallInfo> createCalls(SipSharedCallAppearanceHandle sca, const CPCAPI2::SipDialogEvent::DialogInfoDocument& dialogInfoDoc);
   SharedCallAppearancePair getSharedCallAppearanceForSubscription(CPCAPI2::SipDialogEvent::SipDialogEventSubscriptionHandle subscription);
   SharedCallAppearancePair getSharedCallAppearanceForPublication(CPCAPI2::SipDialogEvent::SipDialogEventPublicationHandle publication);
   SharedCallAppearancePair getSharedCallAppearanceForConversation(CPCAPI2::SipConversation::SipConversationHandle conversation);
   CPCAPI2::SipDialogEvent::DialogInfo getDialogForAppearance(SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance);
   void fireSharedCallAppearanceNewSubscription(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceNewSubscriptionEvent& event);
   void fireSharedCallAppearanceSubscriptionStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionStateChangedEvent& event);
   void fireSharedCallAppearanceSubscriptionEnded(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceSubscriptionEndedEvent& event);
   void fireSharedCallAppearanceStateChanged(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceStateChangedEvent& event);
   void fireSharedCallAppearanceMakeExclusiveSuccess(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveSuccessEvent& event);
   void fireSharedCallAppearanceMakeExclusiveFailure(SipSharedCallAppearanceSetHandle scaSet, const SharedCallAppearanceMakeExclusiveFailureEvent& event);
   void fireError(SipSharedCallAppearanceSetHandle scaSet, const ErrorEvent& event);
   bool isScaPairValid(SharedCallAppearancePair scaPair) const;
};

}
}

#endif // __CPCAPI2_SIP_SHARED_CALL_APPEARANCE_MANAGER_IMPL_H__
