#pragma once

#ifndef CPCAPI2_NOTIFICATIONSERVICEIMPL_H
#define CPCAPI2_NOTIFICATIONSERVICEIMPL_H

#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/MultiReactor.hxx>
#include <rutil/Fifo.hxx>

#include "cpcapi2defs.h"

#include <websocket/WebSocketStateMachine.h>
#include <websocket/WebSocketStateDispatcher.h>

#include <notificationservice/NotificationService.h>
#include <notificationservice/NotificationServiceTypes.h>
#include <notificationservice/NotificationHandler.h>
#include "NotificationServiceTypesInternal.h"
#include "NotificationSyncHandler.h"

#include "../util/DumFpCommand.h"

namespace CPCAPI2
{
   namespace WebSocket
   {
      class WebSocketCommand;
   }

   namespace Notification
   {
      class NotificationServiceImpl : public WebSocket::WebSocketStateMachineListener
      {
      public:
         NotificationServiceImpl(
            ChannelHandle hChannel,
            boost::asio::io_service& ioService,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
            SslCipherOptions tlsOptions);
         virtual ~NotificationServiceImpl();

         // PhoneModule implementation
         void Release();

         // NotificationService Implementation
         int configureChannelSettings( ChannelInfo* info, const ChannelSettings& settings );
         int applySettings();
         int connect();
         int disconnect();
         int setHandler( NotificationHandler* handler);

         void setCallbackHook(void (*cbHook)(void*), void* context);
         
         WebSocket::WebSocketStateMachine *getStateMachine() { return m_StateMachine.get(); }
         const ChannelSettings& getSettings() { return m_Settings; }
         const ChannelHandle getChannelHandle() { return m_hChannel; }

         // State machine listener implementation
         void onStateChange( const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason ) OVERRIDE;
         void onLogin( void ) OVERRIDE;
         void onLoginResponse( const std::string& inString ) OVERRIDE;
         void onLogout( void ) OVERRIDE;
         void onMessage( const std::string& inString ) OVERRIDE;
         void onPing( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         bool onReconnect( uint16_t code ) OVERRIDE;

         // Funky macro for firing events
         template<typename TFn, typename TEvt> void fireEvent(
            const char* funcName,
            TFn func,
            const TEvt& args)
         {
            if( m_Handler == NULL )
               return;

            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_hChannel, args);
            if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<NotificationSyncHandler*>( m_Handler ) != NULL)
            {
               // Invoke the callback synchronously in these cases
               ( *cb )();
               delete cb;
            }
            else
            {
               // Object "cb" should be deleted by whomever is processing this FIFO
               m_CallbackFifo.add( cb );
               if (m_CbHook) { m_CbHook(); }
            }
         }

      private: // data

         // passed into websocketpp lib
         boost::asio::io_service& m_IOService;

         // shared between Interface and Impl
         resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo;
         std::function<void(void)> m_CbHook;
         // Counter for current request handle
         std::atomic< CPCAPI2::WebSocket::RequestHandle > m_CurRequestHandle;

         // The channel for which this notification service applies.
         ChannelHandle m_hChannel;

         // Pointer to the settings for this account (owned)
         ChannelSettings m_Settings;

         // State machine instance
         std::shared_ptr< WebSocket::WebSocketStateMachine > m_StateMachine;

         // Application handler for account related activity
         NotificationHandler *m_Handler;

         // Default TLS settings
         SslCipherOptions m_TlsOptions;
      };
   }
}
#endif // CPCAPI2_NOTIFICATIONSERVICEIMPL_H
