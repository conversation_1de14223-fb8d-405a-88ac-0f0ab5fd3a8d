#pragma once
#ifndef __CPCAPI2_NOTIFICATION_EVENTNOTIFICATION_H__
#define __CPCAPI2_NOTIFICATION_EVENTNOTIFICATION_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <notificationservice/NotificationServiceTypes.h>

#include "SNSTokens.h"

namespace CPCAPI2
{
   namespace Notification
   {
      class EventNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         EventNotification()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_EventType( EventType_Unknown ),
              m_EventDateMillis( 0 ) {}

         virtual ~EventNotification() {}

         const char *getNotificationName() const OVERRIDE { return NOTIFICATION_NAME_EVENT; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            if( !inDocument.HasMember( CLIENT_EVENTTYPE ))
               return false;
            m_EventType = inDocument[ CLIENT_EVENTTYPE ].GetString();

            if( !inDocument.HasMember( CLIENT_EVENTDATA ))
               return false;
            m_EventData = inDocument[ CLIENT_EVENTDATA ].GetString();

            if( !inDocument.HasMember( CLIENT_EVENTDATE ))
               return false;
            m_EventDateMillis = inDocument[ CLIENT_EVENTDATE ].GetInt64();

            return true;
         }

         EventType getEventType() { return m_EventType; }
         cpc::string getEventData() { return m_EventData; }
         int64_t getEventDateMillis() { return m_EventDateMillis; }

      private:
         CPCAPI2::WebSocket::RequestHandle m_hRequest;
         EventType     m_EventType;
         cpc::string   m_EventData;
         int64_t       m_EventDateMillis;
      };
   }
}

#endif // CPCAPI2_BRAND_SNS_MODULE == 1
#endif // __CPCAPI2_NOTIFICATION_EVENTNOTIFICATION_H__