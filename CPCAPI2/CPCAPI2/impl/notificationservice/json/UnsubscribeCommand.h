#pragma once
#ifndef __CPCAPI2_NOTIFICATION_UNSUBSCRIBECOMMAND_H__
#define __CPCAPI2_NOTIFICATION_UNSUBSCRIBECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include <notificationservice/NotificationServiceTypes.h>
#include "SNSTokens.h"

namespace CPCAPI2
{
   namespace Notification
   {
      class UnsubscribeCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         const AuthInfo& m_AuthInfo;
         const cpc::string m_DeviceUUID;

         UnsubscribeCommand( const AuthInfo& authInfo, const cpc::string& deviceUUID )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_AuthInfo( authInfo ),
              m_DeviceUUID( deviceUUID ) {}
         virtual ~UnsubscribeCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_UNSUBSCRIBE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer,  CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer,  CLIENT_REQUEST_ID,  m_RequestHandle);

            // Add auth info
            CPCAPI2::Json::Write(writer,  CLIENT_GROUP,       m_AuthInfo.groupName);
            CPCAPI2::Json::Write(writer,  CLIENT_USERNAME,    m_AuthInfo.userName);
            CPCAPI2::Json::Write(writer,  CLIENT_PASSWORD,    m_AuthInfo.password);

            // Add the device UUID
            CPCAPI2::Json::Write(writer,  CLIENT_DEVICEUUID,  m_DeviceUUID);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_SNS_MODULE == 1
#endif // __CPCAPI2_NOTIFICATION_UNSUBSCRIBECOMMAND_H__
