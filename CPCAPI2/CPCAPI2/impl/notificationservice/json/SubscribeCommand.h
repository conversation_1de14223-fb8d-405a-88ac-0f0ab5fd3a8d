#pragma once
#ifndef __CPCAPI2_NOTIFICATION_SUBSCRIBECOMMAND_H__
#define __CPCAPI2_NOTIFICATION_SUBSCRIBECOMMAND_H__

#include <brand_branded.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#include <string>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketCommand.h>

#include "SNSTokens.h"
#include <notificationservice/NotificationServiceTypes.h>

namespace CPCAPI2
{
   namespace Notification
   {
      class SubscribeCommand : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         CPCAPI2::WebSocket::RequestHandle m_RequestHandle;
         const ChannelSettings& m_Settings;

         SubscribeCommand( const ChannelSettings& settings )
            : m_RequestHandle( CPCAPI2_JSON_NO_REQUEST_ID ),
              m_Settings( settings ) {}
         virtual ~SubscribeCommand() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SUBSCRIBE; }
         const CPCAPI2::WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_RequestHandle; }

         bool toString( const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString ) OVERRIDE
         {
            // string for sending on wire
            return toString(hRequest, outString, false);
         }
         
         std::string getLogString( const CPCAPI2::WebSocket::RequestHandle& hRequest ) OVERRIDE
         {
            std::string s;
            if (toString(hRequest, s, true))
            {
               return s;
            }
            else
            {
               return "Error generating command text";
            }
         }
         
      private:
         bool toString(const CPCAPI2::WebSocket::RequestHandle& hRequest, std::string & outString, bool forLogOnly)
         {
            m_RequestHandle = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer,  CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer,  CLIENT_REQUEST_ID,  m_RequestHandle);

            // Add auth info
            CPCAPI2::Json::Write(writer,  CLIENT_GROUP,       m_Settings.authInfo.groupName);
            CPCAPI2::Json::Write(writer,  CLIENT_USERNAME,    m_Settings.authInfo.userName);
            CPCAPI2::Json::Write(writer,  CLIENT_PASSWORD,    forLogOnly ? "<omitted>" : m_Settings.authInfo.password);

            // Add build info
            if( !m_Settings.buildInfo.productCode.empty() )
               CPCAPI2::Json::Write(writer,  CLIENT_PRODUCTCODE, m_Settings.buildInfo.productCode);
            if( !m_Settings.buildInfo.licenseKey.empty() )
               CPCAPI2::Json::Write(writer,  CLIENT_LICENSEKEY, m_Settings.buildInfo.licenseKey);
            if( !m_Settings.buildInfo.locale.empty() )
               CPCAPI2::Json::Write(writer,  CLIENT_LOCALE, m_Settings.buildInfo.locale);
            if( !m_Settings.buildInfo.applicationID.empty() )
               CPCAPI2::Json::Write(writer,  CLIENT_APPLICATIONID, m_Settings.buildInfo.applicationID);

            // add device uuid
            CPCAPI2::Json::Write(writer,  CLIENT_DEVICEUUID, m_Settings.deviceUUID);

            // add push info
            switch( m_Settings.pushInfo.pushType )
            {
            case PushType_ApplePushKit:
               CPCAPI2::Json::Write(writer,  CLIENT_PUSHTYPE, "ApplePushKit");
               CPCAPI2::Json::Write(writer,  CLIENT_PUSHTOKEN, m_Settings.pushInfo.pushToken);
               break;
            case PushType_GoogleCloudMessaging:
               CPCAPI2::Json::Write(writer,  CLIENT_PUSHTYPE, "GoogleCloudMessaging");
               CPCAPI2::Json::Write(writer,  CLIENT_PUSHTOKEN, m_Settings.pushInfo.pushToken);
               break;
            case PushType_None:
            default:
               CPCAPI2::Json::Write(writer,  CLIENT_PUSHTYPE, "None");
               break;
            }

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // CPCAPI2_BRAND_SNS_MODULE == 1
#endif // __CPCAPI2_NOTIFICATION_SUBSCRIBECOMMAND_H__
