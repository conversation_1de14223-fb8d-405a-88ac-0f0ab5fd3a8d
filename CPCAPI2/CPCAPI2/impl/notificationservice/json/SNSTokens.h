#pragma once
#ifndef __CPCAPI2_NOTIFICATION_SNSTOKENS_H__
#define __CPCAPI2_NOTIFICATION_SNSTOKENS_H__

namespace CPCAPI2
{
   namespace Notification
   {
      // Constant strings which are used for building/parsing json messages to/from the SNS server.

      extern const char *CLIENT_COMMAND;
      extern const char *CLIENT_REQUEST_ID;
      extern const char *CLIENT_ITEM_LIST;
      extern const char *CLIENT_PASSWORD;
      extern const char *CLIENT_GROUP;
      extern const char *CLIENT_USERNAME;
      extern const char *CLIENT_DISPLAY_NAME;
      extern const char *CLIENT_PIN;
      extern const char *CLIENT_CALL_ID;
      extern const char *CLIENT_PRODUCTCODE;
      extern const char *CLIENT_LICENSEKEY;
      extern const char *CLIENT_LOCALE;
      extern const char *CLIENT_APPLICATIONID;
      extern const char *CLIENT_DEVICEUUID;
      extern const char *CLIENT_MAKE;
      extern const char *CLIENT_MODEL;
      extern const char *CLIENT_PUSHTYPE;
      extern const char *CLIENT_PUSHTOKEN;
      extern const char *CLIENT_EVENTTYPE;
      extern const char *CLIENT_EVENTDATA;
      extern const char *CLIENT_EVENTDATE;

      // Command names
      extern const char *COMMAND_NAME_UNKNOWN;
      extern const char *COMMAND_NAME_ERROR;
      extern const char *COMMAND_NAME_SUBSCRIBE;
      extern const char *COMMAND_NAME_UNSUBSCRIBE;

      // Response names (command names appended with response suffix)
      extern const char *RESPONSE_NAME_STATUS;

      // Unsolicited messages coming from the server
      // const char *NOTIFICATION_NAME_SCREENSHARE_UPDATE  = COMMAND_NAME_SCREENSHARE_UPDATE;
      extern const char *NOTIFICATION_NAME_EVENT;
   }
}

#endif // __CPCAPI2_NOTIFICATION_SNSTOKENS_H__
