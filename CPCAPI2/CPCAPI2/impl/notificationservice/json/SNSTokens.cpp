#include "SNSTokens.h"

// Constant strings and ints which are used for building/parsing json messages to/from the SNS server.

const char *CPCAPI2::Notification::CLIENT_COMMAND                     = "cmd";
const char *CPCAPI2::Notification::CLIENT_REQUEST_ID                  = "requestID";
const char *CPCAPI2::Notification::CLIENT_ITEM_LIST                   = "items";
const char *CPCAPI2::Notification::CLIENT_PASSWORD                    = "password";
const char *CPCAPI2::Notification::CLIENT_GROUP                       = "group";
const char *CPCAPI2::Notification::CLIENT_USERNAME                    = "userName";
const char *CPCAPI2::Notification::CLIENT_DISPLAY_NAME                = "displayName";
const char *CPCAPI2::Notification::CLIENT_PIN                         = "pin";
const char *CPCAPI2::Notification::CLIENT_CALL_ID                     = "callId";
const char *CPCAPI2::Notification::CLIENT_PRODUCTCODE                 = "productCode";
const char *CPCAPI2::Notification::CLIENT_LICENSEKEY                  = "licenseKey";
const char *CPCAPI2::Notification::CLIENT_LOCALE                      = "locale";
const char *CPCAPI2::Notification::CLIENT_APPLICATIONID               = "applicationID";
const char *CPCAPI2::Notification::CLIENT_DEVICEUUID                  = "deviceUUID";
const char *CPCAPI2::Notification::CLIENT_PUSHTYPE                    = "pushType";
const char *CPCAPI2::Notification::CLIENT_PUSHTOKEN                   = "pushToken";
const char *CPCAPI2::Notification::CLIENT_EVENTTYPE                   = "eventType";
const char *CPCAPI2::Notification::CLIENT_EVENTDATA                   = "eventData";
const char *CPCAPI2::Notification::CLIENT_EVENTDATE                   = "eventDate";

// Command names
const char *CPCAPI2::Notification::COMMAND_NAME_UNKNOWN               = "UNKNOWN";
const char *CPCAPI2::Notification::COMMAND_NAME_ERROR                 = "ERROR";
const char *CPCAPI2::Notification::COMMAND_NAME_SUBSCRIBE             = "SUBSCRIBE";
const char *CPCAPI2::Notification::COMMAND_NAME_UNSUBSCRIBE           = "UNSUBSCRIBE";

// Response names (command names appended with response suffix)
const char *CPCAPI2::Notification::RESPONSE_NAME_STATUS               = "STATUS_RESPONSE";

// Unsolicited messages coming from the server
// const char *NOTIFICATION_NAME_SCREENSHARE_UPDATE  = COMMAND_NAME_SCREENSHARE_UPDATE;
const char *CPCAPI2::Notification::NOTIFICATION_NAME_EVENT            = "EVENT_NOTIFICATION";
