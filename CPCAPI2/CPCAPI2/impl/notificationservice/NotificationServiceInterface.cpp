#include <brand_branded.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#include <mutex>
#include <condition_variable>

#include <assert.h>

#include "NotificationServiceInterface.h"
#include "NotificationServiceImpl.h"
#include "phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

using namespace CPCAPI2::Notification;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SNS

#define INVALID_CHANNEL_HANDLE_STR  "Invalid Channel Handle"
std::atomic< ChannelHandle > NotificationServiceInterface::s_CurrentHandle( 1 );

NotificationServiceInterface::NotificationServiceInterface(CPCAPI2::Phone* phone)
   : m_IOService(dynamic_cast<PhoneInterface*>(phone)->getAsioIoService()), m_Shutdown( false ), m_CbHook( NULL ), m_Context( NULL ), m_TlsOptions( dynamic_cast<PhoneInterface*>(phone)->getSslCipherOptions() )
{
}

NotificationServiceInterface::~NotificationServiceInterface()
{
}

// PhoneModule implementation
void NotificationServiceInterface::Release()
{
   // Release is actually called in the resip reactor thread already. So no need to
   // delegate into the IO service, do everything inline.
   m_Shutdown = true;
   std::map< ChannelHandle, ChannelInfo* >::iterator iter = m_InfoMap.begin();
   for( ; iter != m_InfoMap.end() ; ++iter )
   {
      ChannelInfo *info( iter->second );
      if( info != NULL )
      {
         delete info->pImpl; info->pImpl = NULL;
         delete info;
      }
   }
   m_InfoMap.clear();

   delete this; // suicide
}

bool NotificationServiceInterface::getChannelInfo( const ChannelHandle& hChannel, ChannelInfo*& pOutInfo )
{
   bool result = false;

   std::map< ChannelHandle, ChannelInfo* >::iterator iter = m_InfoMap.find( hChannel );
   if( iter == m_InfoMap.end() )
   {
      ErrorEvent evt;
      evt.errNum    = 0;
      evt.errorText = INVALID_CHANNEL_HANDLE_STR;
      // TODO
      // info->acctImpl->fireEvent( cpcFunc( NotificationHandler::onError ), evt ); // how ?
   }
   else
   {
      pOutInfo = iter->second;
      result = true;
   }

   return result;
}

// NotificationService Implementation
ChannelHandle NotificationServiceInterface::createChannel( void )
{
   ChannelHandle hChannel = s_CurrentHandle.fetch_add( 1 );
   DebugLog(<< __FUNCTION__ << " channel returned is " << hChannel);
   m_IOService.post( std::bind( &NotificationServiceInterface::createChannelImpl, this, hChannel ));
   return hChannel;
}

int NotificationServiceInterface::createChannelImpl( const ChannelHandle& hChannel )
{
   ChannelInfo *info = new ChannelInfo;
   info->pImpl = new NotificationServiceImpl( hChannel, m_IOService, m_CallbackFifo, m_TlsOptions);
   m_InfoMap[ hChannel ] = info;
   info->pImpl->setCallbackHook(m_CbHook, m_Context);
   return kSuccess;
}

int NotificationServiceInterface::configureChannelSettings( const ChannelHandle& hChannel, const ChannelSettings& settings )
{
   DebugLog(<< __FUNCTION__ << " channel: " << hChannel << ", ChannelSettings: [" << settings << "]");

   m_IOService.post( std::bind( &NotificationServiceInterface::configureChannelSettingsImpl, this, hChannel, settings ));
   return kSuccess;
}

int NotificationServiceInterface::configureChannelSettingsImpl( const ChannelHandle& hChannel, const ChannelSettings& settings )
{
   ChannelInfo* info = NULL;
   if( !getChannelInfo( hChannel, info ))
      return kError;

   if( info->pImpl == NULL )
      return kError;

   info->authInfo   = settings.authInfo;
   info->buildInfo  = settings.buildInfo;
   info->deviceUUID = settings.deviceUUID;
   info->pushInfo   = settings.pushInfo;

   return info->pImpl->configureChannelSettings( info, settings );
}

int NotificationServiceInterface::applySettings( const ChannelHandle& hChannel )
{
   DebugLog(<< __FUNCTION__ << " channel: " << hChannel);

   m_IOService.post( std::bind( &NotificationServiceInterface::applySettingsImpl, this, hChannel ));
   return kSuccess;
}

int NotificationServiceInterface::applySettingsImpl( const ChannelHandle& hChannel )
{
   return kSuccess;
}

int NotificationServiceInterface::connect( const ChannelHandle& hChannel )
{
   DebugLog(<< __FUNCTION__ << " channel: " << hChannel);

   m_IOService.post( std::bind( &NotificationServiceInterface::connectImpl, this, hChannel ));
   return kSuccess;
}

int NotificationServiceInterface::connectImpl( const ChannelHandle& hChannel )
{
   ChannelInfo* info = NULL;
   if( !getChannelInfo( hChannel, info ))
      return kError;

   return info->pImpl->connect();
}

int NotificationServiceInterface::disconnect( const ChannelHandle& hChannel )
{
   DebugLog(<< __FUNCTION__ << " channel: " << hChannel);

   m_IOService.post( std::bind( &NotificationServiceInterface::disconnectImpl, this, hChannel ));
   return kSuccess;
}

int NotificationServiceInterface::disconnectImpl( const ChannelHandle& hChannel )
{
   ChannelInfo* info = NULL;
   if( !getChannelInfo( hChannel, info ))
      return kError;

   return info->pImpl->disconnect();
}

int NotificationServiceInterface::destroy( const ChannelHandle& hChannel )
{
   DebugLog(<< __FUNCTION__ << " channel: " << hChannel);

   m_IOService.post( std::bind( &NotificationServiceInterface::destroyImpl, this, hChannel ));
   return kSuccess;
}

int NotificationServiceInterface::destroyImpl( const ChannelHandle& hChannel )
{
   ChannelInfo* info = NULL;
   if( !getChannelInfo( hChannel, info ))
      return kError;

   delete info->pImpl;
   delete info;

   m_InfoMap.erase( hChannel );
   return kSuccess;
}

int NotificationServiceInterface::setHandler( const ChannelHandle& hChannel, NotificationHandler* handler)
{
   int result = kError;
   if( handler != NULL )
   {
      m_IOService.post( std::bind( &NotificationServiceInterface::setHandlerImpl, this, hChannel, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      // Special case that needs to block the caller until operation is complete.

      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( NotificationServiceInterface *parent, const ChannelHandle& hChannel, NotificationHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhChannel( hChannel ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhChannel, mHandler );
            mCVar.notify_all();
         }

         NotificationServiceInterface *mParent;
         const ChannelHandle& mhChannel;
         NotificationHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hChannel, handler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int NotificationServiceInterface::setHandlerImpl( const ChannelHandle& hChannel, NotificationHandler* handler)
{
   ChannelInfo* info = NULL;
   if( !getChannelInfo( hChannel, info ))
      return kError;

   return info->pImpl->setHandler( handler );
}

int NotificationServiceInterface::process( unsigned int timeout )
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}

void NotificationServiceInterface::postToProcessThread( void (*pfun)(void*), void* obj )
{
   // Inner struct just used to stuff a std::bind into the resip Fifo
   struct ReadCallback : resip::ReadCallbackBase
   {
      ReadCallback(std::function<void()> f) : mF(f) {}
      virtual void operator()() { mF(); }
      virtual void* address() { return NULL; }
   private:
      std::function<void()> mF;
   };

   std::function<void()> bfunc = std::bind(pfun, obj);
   ReadCallback* brcb = new ReadCallback( bfunc );
   m_CallbackFifo.add(brcb);
}

void NotificationServiceInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   m_IOService.post( std::bind( &NotificationServiceInterface::setCallbackHookImpl, this, cbHook, context ));
}

void NotificationServiceInterface::setCallbackHookImpl( void (*cbHook)(void*), void* context )
{
   m_CbHook = cbHook;
   m_Context = context;
   
   std::map< ChannelHandle, ChannelInfo* >::iterator iter = m_InfoMap.begin();
   while( iter != m_InfoMap.end() )
   {
      if( iter->second != NULL && iter->second->pImpl != NULL )
         iter->second->pImpl->setCallbackHook( cbHook, context );
      
      ++iter;
   }
}


#ifdef CPCAPI2_AUTO_TEST
CPCAPI2::AutoTestReadCallback* NotificationServiceInterface::process_test(int timeout)
{
   if( m_Shutdown )
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}
#endif


namespace CPCAPI2
{
namespace Notification
{

std::ostream& operator<<(std::ostream& os, const ChannelSettings& settings)
{
   os << "authInfo: [" << settings.authInfo << "], buildInfo: [" << settings.buildInfo << "], pushInfo: ["
      << settings.pushInfo << "], wsSettings: [" << settings.wsSettings << "], deviceUUID: " << settings.deviceUUID;
   return os;
}

std::ostream& operator<<(std::ostream& os, const AuthInfo& authInfo)
{
   os << "groupName: " << authInfo.groupName << ", userName: " << authInfo.userName << ", password: <omitted>";
   return os;
}

std::ostream& operator<<(std::ostream& os, const BuildInfo& buildInfo)
{
   os << "productCode: " << buildInfo.productCode << ", licenseKey: " << buildInfo.licenseKey << ", locale: "
      << buildInfo.locale << ", applicationID: " << buildInfo.applicationID;
   return os;
}

std::ostream& operator<<(std::ostream& os, const PushInfo& pushInfo)
{
   os << "pushToken: " << pushInfo.pushToken << ", pushType: " << pushInfo.pushType;
   return os;
}

}
}

#endif // CPCAPI2_BRAND_SNS_MODULE
