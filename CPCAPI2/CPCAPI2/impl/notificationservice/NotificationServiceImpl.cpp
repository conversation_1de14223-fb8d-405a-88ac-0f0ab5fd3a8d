#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <brand_branded.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SNS
#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "cpcapi2defs.h"
#include <phone/PhoneModule.h>
#include <util/statemachine/AbstractState.h>
#include <websocket/json/WebSocketCommand.h>
#include <websocket/json/StatusResponse.h>
#include <websocket/states/StateConnected.h>
#include <websocket/states/StateConnecting.h>
#include <websocket/states/StateDisconnected.h>
#include <websocket/states/StateFailed.h>
#include "../util/DumFpCommand.h"

#include <writer.h>
#include <stringbuffer.h>

#include <websocket/WebSocketStateMachine.h>

#include <notificationservice/NotificationService.h>
#include <notificationservice/NotificationServiceTypes.h>
#include <notificationservice/NotificationHandler.h>
#include "NotificationServiceTypesInternal.h"
#include "NotificationServiceImpl.h"

#include "json/EventNotification.h"
#include "json/SubscribeCommand.h"
#include "json/UnsubscribeCommand.h"

using namespace CPCAPI2::Notification;
using CPCAPI2::AbstractStatePtr;
using CPCAPI2::AbstractStateReasonCode;
using CPCAPI2::WebSocket::WebSocketCommand;
using CPCAPI2::WebSocket::WebSocketStatePtr;
using CPCAPI2::WebSocket::WebSocketSettings;
using CPCAPI2::WebSocket::WebSocketState;
using CPCAPI2::WebSocket::WebSocketStateMachine;
using CPCAPI2::WebSocket::WebSocketStateDispatcher;

#define FIRE_ERROR_AND_RETURN( ERRTEXT ) \
{ \
   ErrorEvent evt; \
   evt.errorText = ( ERRTEXT ); \
   evt.errNum = 0; \
   fireEvent( cpcFunc( NotificationHandler::onError ),evt ); \
   return kError; \
}
#define INVALID_ACCOUNT_STATE_STR   "Invalid Account State"
#define INVALID_ACCOUNT_HANDLER_STR "Account Handler Already Set"

NotificationServiceImpl::NotificationServiceImpl(
   ChannelHandle hChannel,
   boost::asio::io_service& ioService,
   resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
   CPCAPI2::SslCipherOptions tlsOptions)
   : m_IOService( ioService ), m_CallbackFifo( callbackFifo ), m_Handler( NULL ),
     m_CurRequestHandle( 1 ), m_CbHook( NULL ), m_TlsOptions(tlsOptions), m_hChannel(hChannel)
{
}

NotificationServiceImpl::~NotificationServiceImpl()
{
   m_Handler = NULL;
   if (m_StateMachine)
      m_StateMachine->removeListener( this );

   m_StateMachine.reset();
}

// PhoneModule implementation
void NotificationServiceImpl::Release()
{
}

// NotificationService Implementation
int NotificationServiceImpl::configureChannelSettings( ChannelInfo* info, const ChannelSettings& settings )
{
   websocketpp::lib::error_code ec;

   // OBELISK-5621/STRETTO-5107: Stretto now responsible for sending periodic pings. This was done to avoid on
   // Android the SDK having to wakeup periodically via android.app.AlarmManager + acquire wake locks
   m_Settings.wsSettings.pingIntervalSeconds = 0;

   // Always update the settings
   m_Settings = settings;

   // Setup the state machine and dispatcher if it hasn't been done already.
   // Only do this one time. NB: settings is passed as a reference.
   if( m_StateMachine.get() == NULL )
   {
      if (TLS_DEFAULT == m_Settings.wsSettings.tlsVersion) m_Settings.wsSettings.tlsVersion = m_TlsOptions.getTLSVersion(SslCipherUsageWebSockets);
      if (m_Settings.wsSettings.cipherSuite.empty()) m_Settings.wsSettings.cipherSuite = m_TlsOptions.getCiphers(SslCipherUsageWebSockets);
      m_StateMachine.reset( new WebSocketStateMachine( RESIPROCATE_SUBSYSTEM, m_IOService, m_Settings.wsSettings, "Notification Channel " + std::to_string(m_hChannel) ));

      // Add myself as a listener to the state machine
      m_StateMachine->addListener( this );
   }

   return kSuccess;
}

int NotificationServiceImpl::applySettings()
{
   return kSuccess;
}

int NotificationServiceImpl::connect()
{
   // It's only possible to connect the channel if it's in a disconnected state.
   if( strcmp( m_StateMachine->getCurrentStateID(), STATE_DISCONNECTED_ID ) != 0 )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_STATE_STR );

   // To trigger the enabling of the account, perform a state transition.
   m_StateMachine->setCurrentState( STATE_CONNECTING_ID );
   return kSuccess;
}

int NotificationServiceImpl::disconnect()
{
   // Unsubscribe on our way out. No need to get the response
   UnsubscribeCommand command(
      m_Settings.authInfo, m_Settings.deviceUUID);

   std::string errMessage;
   DebugLog(<< "onLogout state: " << m_StateMachine->getCurrentState()->getName());
   if (!m_StateMachine->sendCommand(m_CurRequestHandle.fetch_add(1), command, errMessage))
   {
      ErrorEvent evt;
      evt.errorText = errMessage.c_str();
      fireEvent(cpcFunc(NotificationHandler::onError), evt);

      // We failed to unsubscribe properly. So I guess we just go to
      // the disconnected state here. Server may have a problem .. ?
      DebugLog(<< "ChannelStateConnected: couldn't unsubscribe");
   }

   // Perform a state transition (should be legal from any state)
   if( !m_StateMachine->setCurrentState( STATE_DISCONNECTED_ID ))
      return kError;

   return kSuccess;
}

void NotificationServiceImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if( cbHook != NULL && context != NULL )
      m_CbHook = std::bind( cbHook, context );
}

int NotificationServiceImpl::setHandler( NotificationHandler* handler )
{
   // Ensure handler isn't already set.
   if( m_Handler != NULL && handler != NULL )
      FIRE_ERROR_AND_RETURN( INVALID_ACCOUNT_HANDLER_STR );

   m_Handler = handler;
   return kSuccess;
}

static bool StateToEnum( const CPCAPI2::AbstractStatePtr state, ChannelState& outEnum )
{
   if( state == NULL )
      return false;

   const char *stateID = state->getUniqueID();

   if( strcmp( stateID, STATE_CONNECTED_ID ) == 0 )
      outEnum = ChannelState_Connected;
   else if( strcmp( stateID, STATE_CONNECTING_ID ) == 0 )
      outEnum = ChannelState_Connecting;
   else if( strcmp( stateID, STATE_DISCONNECTED_ID ) == 0 )
      outEnum = ChannelState_Disconnected;
   else if( strcmp( stateID, STATE_FAILED_ID ) == 0 )
      outEnum = ChannelState_Failed;
   else
      return false;

   return true;
}

void NotificationServiceImpl::onStateChange( const AbstractStatePtr oldState, const AbstractStatePtr newState, const AbstractStateReasonCode reason )
{
   ChannelStateChangedEvent evt;

   if( !StateToEnum( oldState, evt.oldState ) ||
       !StateToEnum( newState, evt.newState ))
       return;

   fireEvent( cpcFunc( NotificationHandler::onChannelStateChanged ), evt );
}

void NotificationServiceImpl::onLogin( void )
{
   // Check the settings to see whether or not there is enough information
   // to perform a login (the login step is optional).
   if( !m_Settings.authInfo.userName.empty() )
   {
      DebugLog(<< "ChannelStateConnecting: Initiating Login to SNS" );
      SubscribeCommand command( m_Settings );

      std::string errMessage;
      if( !m_StateMachine->sendCommand( m_CurRequestHandle.fetch_add( 1 ), command, errMessage, false ))
      {
         ErrorEvent evt;
         evt.errorText = errMessage.c_str();
         fireEvent( cpcFunc( NotificationHandler::onError ), evt );
      }
   }
}

void NotificationServiceImpl::onLoginResponse( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse response;
   if (response.fromString( inString ))
   {
      if (response.isErrorCondition())
      {
         // Login failure from the server, treat this as a failure
         ErrLog( << "StateConnecting: Couldn't login to the server, server responded with error: " << response.getErrorCode() );

         // Depending on the type of failure, we will either retry, or go back
         // to disconnected state potentially.
         m_StateMachine->setCurrentState( STATE_FAILED_ID );
      }
      else
      {
         // Login successful proceed to Registered state
         DebugLog( << "StateConnecting: Login successful" );
         m_StateMachine->setCurrentState( STATE_CONNECTED_ID );
      }
   }
   else
   {
      ErrLog( << "StateConnecting: Failed to parse message" );
      return;
   }
}

void NotificationServiceImpl::onLogout( void )
{
}

void NotificationServiceImpl::onMessage( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse sResponse;
   if( sResponse.fromString( inString ))
   {
      std::string origCmd( sResponse.getOriginalCommandName() );

      if( origCmd == COMMAND_NAME_UNSUBSCRIBE )
      {
         // This probably won't happen since we've already left the
         // state at this point (and the socket will be closed).
         // Nevertheless, add code for handling the response.

         if( sResponse.isErrorCondition() )
         {
            ErrorEvent evt;
            evt.errorText = sResponse.getErrorCode();
            fireEvent( cpcFunc( NotificationHandler::onError ), evt );

            // We failed to unsubscribe properly. So I guess we just go to
            // the disconnected state here. Server may have a problem .. ?
            DebugLog(<< "ChannelStateConnected: couldn't unsubscribe (onMessage)" );
         }

         // Upon receiving an unsubscribe result, move to disconnected state.
         getStateMachine()->setCurrentState( STATE_DISCONNECTED_ID );
      }
      return;
   }

   // Parse any incoming notifications
   EventNotification en;
   if( en.fromString( inString ))
   {
      NotificationEvent ne;
      ne.eventType = en.getEventType();
      ne.eventData = en.getEventData();
      ne.eventDateMillis = en.getEventDateMillis();
      fireEvent( cpcFunc( NotificationHandler::onNotification ), ne );
      return;
   }
}

void NotificationServiceImpl::onPing( websocketpp::connection_hdl hWebSock )
{
   m_StateMachine->sendPing( hWebSock );
}

bool NotificationServiceImpl::onReconnect( uint16_t code )
{
   switch( code )
   {
   case 4001 : // Internal Server Error
   case 4002 : // Server Communication Error
   case 4003 : // Unexpected Error
   	return true;
   case 4100 : // Unauthorized: User not found or password not valid
   case 4101 : // Unauthorized: User locked out
      WarningLog( << "Connection Close/Failure with error code " << code << " Retry disabled" );
      return false;
   default:
      break;
   }
   return true;
}

#endif // #if (CPCAPI2_BRAND_SNS_MODULE == 1)
