#include "brand_branded.h"

#include <notificationservice/NotificationService.h>

#if (CPCAPI2_BRAND_SNS_MODULE == 1)
#include "NotificationServiceInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace Notification
   {
      NotificationService* NotificationService::getInterface( CPCAPI2::Phone* cpcPhone )
      {
#if (CPCAPI2_BRAND_SNS_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<NotificationServiceInterface>(phone, "NotificationService");
#else
         return NULL;
#endif
      }
   }
}
