#pragma once

#ifndef CPCAPI2_NOTIFICATIONSERVICEINTERFACE_H
#define CPCAPI2_NOTIFICATIONSERVICEINTERFACE_H

#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include "cpcapi2defs.h"
#include <phone/PhoneModule.h>
#include <notificationservice/NotificationService.h>
#include <notificationservice/NotificationServiceTypes.h>
#include <notificationservice/NotificationHandler.h>
#include "NotificationServiceTypesInternal.h"
#include "../util/AutoTestProcessor.h"

namespace CPCAPI2
{
   class Phone;

   namespace Notification
   {
      class NotificationServiceInterface :
         public NotificationService,
         public CPCAPI2::PhoneModule
#ifdef CPCAPI2_AUTO_TEST
       , public AutoTestProcessor
#endif
      {
      public:
         NotificationServiceInterface(Phone* phone);
         virtual ~NotificationServiceInterface();

         // PhoneModule implementation
         void Release() OVERRIDE;

         // NotificationService Implementation
         virtual ChannelHandle createChannel( void ) OVERRIDE;
         virtual int configureChannelSettings( const ChannelHandle& hChannel, const ChannelSettings& settings ) OVERRIDE;
         virtual int applySettings( const ChannelHandle& hChannel ) OVERRIDE;
         virtual int connect( const ChannelHandle& hChannel ) OVERRIDE;
         virtual int disconnect( const ChannelHandle& hChannel ) OVERRIDE;
         virtual int destroy( const ChannelHandle& hChannel ) OVERRIDE;
         virtual int setHandler( const ChannelHandle& hChannel, NotificationHandler* handler) OVERRIDE;
         virtual int process(unsigned int timeout) OVERRIDE;
         virtual void postToProcessThread(void (*pfun)(void*), void* obj) OVERRIDE;

         virtual void setCallbackHook(void (*cbHook)(void*), void* context);
         void setCallbackHookImpl( void (*cbHook)(void*), void* context );
         
         int createChannelImpl( const ChannelHandle& hChannel );
         int configureChannelSettingsImpl( const ChannelHandle& hChannel, const ChannelSettings& settings );
         int applySettingsImpl( const ChannelHandle& hChannel );
         int connectImpl( const ChannelHandle& hChannel );
         int disconnectImpl( const ChannelHandle& hChannel );
         int destroyImpl( const ChannelHandle& hChannel );
         int setHandlerImpl( const ChannelHandle& hChannel, NotificationHandler* handler);

#ifdef CPCAPI2_AUTO_TEST
         // AutoTestProcessor implementation
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

      private: // methods

         // This method will set the out param to the channel info (internal) if it
         // exists, and return true. Otherwise, an error will be thrown to the application
         // and false will be returned.
         bool getChannelInfo( const ChannelHandle& hChannel, ChannelInfo*& pOutInfo );

      private: // data

         // Static handle counter
         static std::atomic< ChannelHandle > s_CurrentHandle;

         // Map of AccountHandle(s) to AccountInfo(s)
         std::map< ChannelHandle, ChannelInfo* > m_InfoMap;

         // used for dispatching
         boost::asio::io_service& m_IOService;
            
         // Callback Fifo which should be used to marshal the events (process method should be called)
         resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;
         
         void (*m_CbHook)(void*);
         void* m_Context;

         SslCipherOptions m_TlsOptions;
      };
      
      std::ostream& operator<<(std::ostream& os, const ChannelSettings& settings);
      std::ostream& operator<<(std::ostream& os, const AuthInfo& authInfo);
      std::ostream& operator<<(std::ostream& os, const BuildInfo& buildInfo);
      std::ostream& operator<<(std::ostream& os, const PushInfo& pushInfo);
   }
}
#endif // CPCAPI2_NOTIFICATIONSERVICEINTERFACE_H
