#pragma once
#ifndef __NOTIFICATION_NOTIFICATIONSERVICETYPESINTERNAL_H__
#define __NOTIFICATION_NOTIFICATIONSERVICETYPESINTERNAL_H__

#include <notificationservice/NotificationServiceTypes.h>

namespace CPCAPI2
{
   namespace Notification
   {
      class NotificationServiceImpl;

      typedef struct ChannelInfo
      {
         AuthInfo authInfo;
         BuildInfo buildInfo;
         PushInfo pushInfo;
         cpc::string deviceUUID;

         NotificationServiceImpl *pImpl;
      } ChannelInfo;
   }
}

#endif /* __NOTIFICATION_NOTIFICATIONSERVICETYPESINTERNAL_H__ */
