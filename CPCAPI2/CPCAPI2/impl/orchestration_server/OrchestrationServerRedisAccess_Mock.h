#pragma once

#if !defined(CPCAPI2_ORCH_SERVER_REDIS_ACCESS_MOCK)
#define CPCAPI2_ORCH_SERVER_REDIS_ACCESS_MOCK

#include "OrchestrationServerRedisAccessIf.h"

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <boost/asio.hpp>

#include <functional>
#include <thread>
#include <map>
#include <set>

namespace CPCAPI2
{

namespace OrchestrationServer
{

class RedisAccessMock : public RedisAccessBase
{

public:

   RedisAccessMock();
   virtual ~RedisAccessMock();

   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) OVERRIDE;
   virtual void shutdown() OVERRIDE;

   virtual int addServer(const AddServerArgs& serverInfo, const std::function<void(bool, const AddServerResult&)>& serverAddedCb) OVERRIDE;
   virtual int removeServer(const RemoveServerArgs& removeReq, const std::function<void(bool, const RemoveServerResult&)>& removeServerCb) OVERRIDE;
   virtual int setServerTtl(const SetServerTtlArgs& ttlReq, const std::function<void(bool, const SetServerTtlResult&)>& setServerTtlCb) OVERRIDE;
   virtual int queryServerTtl(const QueryServerTtlArgs& query, const std::function<void(bool, const QueryServerTtlResult&)>& queryResultCb) OVERRIDE;
   virtual int queryAvailableServers(const QueryAvailableServersArgs& serviceRequest, const std::function<void(bool, const QueryAvailableServersResult&)>& queryAvailableServersCb) OVERRIDE;

   virtual int addUserSession(const AddUserSessionArgs& addUserSessionArgs, const std::function<void(bool, const AddUserSessionResult&)>& addUserSessionCb) OVERRIDE;

   virtual int queryUserSessions(const QueryUserSessionsArgs& queryArgs, const std::function<void(bool, const QueryUserSessionsResult&)>& queryUserSessionsCb) OVERRIDE;

   virtual int queryServerUsers(const QueryServerUsersArgs& queryArgs, const std::function<void(bool, const QueryServerUsersResult&)>& queryServerUsersCb) OVERRIDE;

   virtual int flushAll() OVERRIDE;
   
   virtual int setHandler(RedisConnectionHandler* handler) OVERRIDE;

private:

   std::unique_ptr<std::thread> mClientThread;
   boost::asio::io_service mIOS;

   std::map<std::string, std::vector<std::string>> mMapServiceRegionToUri;
   std::map<std::string, AddServerArgs> mMapUriToServerInfo;
   std::map<std::string, uint64_t> mMapServerUriToLastUpdateTimeMs;

   int64_t mServerAvailTimeoutSec;
   std::map<std::string, std::string> mUserSessionsMap;
   std::map<std::string, std::set<std::string>> mMapServerToUsers;
   
   RedisConnectionHandler* mHandler;

};

}

}

#endif // CPCAPI2_ORCH_SERVER_REDIS_ACCESS_MOCK
