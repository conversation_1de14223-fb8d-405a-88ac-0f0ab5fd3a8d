#include "brand_branded.h"

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
#include "cpcapi2utils.h"
#include "OrchestrationServerInterface.h"
#include "OrchestrationServerRedisAccess.h"
#include "OrchestrationServerRedisAccess_Mock.h"
#include "confbridge/ConferenceRegistrar.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"
#include "../phone/PhoneInterface.h"
#include "../util/cpc_logger.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include <future>

using namespace resip;

using resip::ReadCallbackBase;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL
#define JSON_MODULE "OrchestrationServer"

#define REDIS_CONNECTION_RETRY_INTERVAL_MSECS                                 7500
#define REDIS_CONNECTION_RETRY_INTERVAL_MAXIMUM_MSECS                         120000
#define REDIS_CONNECTION_TIMER_ID                                             1

namespace CPCAPI2
{

namespace OrchestrationServer
{

OrchestrationServerInterface::OrchestrationServerInterface(Phone* phone) :
CPCAPI2::EventSource<OrchestrationServerHandle, OrchestrationServerHandler, OrchestrationServerSyncHandler>(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mShutdown(false),
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mRedisConnectionTimer(dynamic_cast<PhoneInterface*>(phone)->getSdkModuleThread()),
mRedisConnectionTimeoutMsecs(REDIS_CONNECTION_RETRY_INTERVAL_MSECS)
{
   mFunctionMap["setServerInfo"] = std::bind(&OrchestrationServerInterface::handleSetServerInfo, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["setServerTtl"] = std::bind(&OrchestrationServerInterface::handleSetServerTtl, this, std::placeholders::_1, std::placeholders::_2);
   mFunctionMap["requestService"] = std::bind(&OrchestrationServerInterface::handleRequestService, this, std::placeholders::_1, std::placeholders::_2);
   mTransport = dynamic_cast<CPCAPI2::JsonApi::JsonApiServerSendTransportInternal*>(CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone));
}

OrchestrationServerInterface::~OrchestrationServerInterface()
{
   mShutdown = true;
   
   // if (mRedis.get() != NULL)
   // {
   //    mRedis->shutdown();
   // }
}

void OrchestrationServerInterface::Release()
{
   delete this;
}

int OrchestrationServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void OrchestrationServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(conn, functionObjectVal);
   }
}

int OrchestrationServerInterface::start(const OrchestrationServerConfig& serverConfig)
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::startImpl, this, serverConfig));
   return kSuccess;
}

int OrchestrationServerInterface::startImpl(const OrchestrationServerConfig& serverConfig)
{
   InfoLog(<< "Orchestration Server start(): initiating connection with database server: " << serverConfig.redisIp << ":" << serverConfig.redisPort);

   if (resip::isEqualNoCase(serverConfig.redisIp.c_str(), "mock"))
   {
      mRedis.reset(new RedisAccessMock());
   }
   else
   {
      mRedis.reset(new RedisAccess());
   }

   mRedis->setHandler(this);
   mConfig = serverConfig;
   mRedisConnectionTimer.cancel();
   
   RedisAccessConfig redisAccessConfig;
   redisAccessConfig.ip = serverConfig.redisIp;
   redisAccessConfig.port = serverConfig.redisPort;
   redisAccessConfig.serverAvailableTimeoutSec = 180; // 3628800; // 6 weeks
   mRedis->initialize(redisAccessConfig, NULL);

   return kSuccess;
}

int OrchestrationServerInterface::shutdown()
{
   mPhone->getSdkModuleThread().execute(resip::resip_bind(&OrchestrationServerInterface::shutdownImpl, this));
   return kSuccess;
}

int OrchestrationServerInterface::shutdownImpl()
{
   InfoLog(<< "Orchestration Server shutdown()");

   if (mRedis.get() != NULL)
   {
      mRedis->shutdown();
   }

   return kSuccess;
}

int OrchestrationServerInterface::setHandler(OrchestrationServerHandler* handler)
{
   setAppHandler(0, handler);
   return kSuccess;
}

int OrchestrationServerInterface::setServerInfo(const ServerInfo& serverInfo)
{
   // StackLog(<< "OrchestrationServerInterface::setServerInfo()");
   postToSdkThread(resip::resip_bind(static_cast<int(OrchestrationServerInterface::*)(const ServerInfo&)>(&OrchestrationServerInterface::setServerInfoImpl), this, serverInfo));
   return kSuccess;
}

int OrchestrationServerInterface::setServerInfoImpl(const ServerInfo& serverInfo)
{
   SetServerInfoResult result;
   setServerInfoImpl(serverInfo, result);
   return kSuccess;
}

int OrchestrationServerInterface::setServerInfoImpl(const ServerInfo& serverInfo, SetServerInfoResult& ssiResult)
{
   {
      cpc::string services;

      for (cpc::vector<cpc::string>::const_iterator it = serverInfo.services.begin(), end = serverInfo.services.end(); it != end; ++it)
      {
         services += *it + " ";
      }

      InfoLog(<< "Orchestration Server setServerInfoImpl(): " << "\n\tregion: " << serverInfo.region.c_str() << "\n\turi: " << serverInfo.uri.c_str() << "\n\tservices: " << services.c_str());
   }

   int reqHandle = 0;

   RedisAccess::AddServerArgs addServerArgs;
   addServerArgs.region = serverInfo.region;
   addServerArgs.uri = serverInfo.uri;
   addServerArgs.services = serverInfo.services;
   addServerArgs.started = serverInfo.started;
   addServerArgs.shutdown = serverInfo.shutdown;
   
   auto pr1 = std::make_shared<std::promise<RedisAccess::AddServerResult> >();
   std::future<RedisAccess::AddServerResult> f1 = pr1->get_future();
   mRedis->addServer(addServerArgs, [pr1](bool, const RedisAccess::AddServerResult& asr) {
      pr1->set_value(asr);
   });

   bool success = false;
   if (f1.wait_for(std::chrono::milliseconds(2000)) == std::future_status::ready)
   {
      if (f1.get().result == 0)
      {
         success = true;
      }
   }
   else
   {
      InfoLog(<< "OrchestrationServerInterface::setServerInfoImpl(): redis request timeout on addServer request");
   }

   ssiResult.success = success;
   ssiResult.serverInfo = serverInfo;
   fireEvent(cpcFunc(OrchestrationServerHandler::onSetServerInfoResult), reqHandle, ssiResult);

   return kSuccess;
}

int OrchestrationServerInterface::setServerTtl(const cpc::string& uri, uint64_t ttlMs)
{
   // StackLog(<< "OrchestrationServerInterface::setServerTtl()");
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::setServerTtlImpl, this, uri, ttlMs));
   return kSuccess;
}

int OrchestrationServerInterface::setServerTtlImpl(const cpc::string& uri, uint64_t ttlMs)
{
   // StackLog(<< "OrchestrationServerInterface::setServerTtlImpl()");
   RedisAccess::SetServerTtlArgs args;
   args.ttl = ttlMs;
   args.uri = uri;
   mRedis->setServerTtl(args, [](bool, const RedisAccess::SetServerTtlResult&) {
   });
   return kSuccess;
}

int OrchestrationServerInterface::removeServer(const cpc::string& service, const cpc::string& region, const cpc::string& uri)
{
   // StackLog(<< "OrchestrationServerInterface::removeServer(): service: " << service.c_str() << " region: " << region.c_str() << " uri: " << uri.c_str());
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::removeServerImpl, this, service, region, uri));
   return kSuccess;
}

int OrchestrationServerInterface::removeServerImpl(const cpc::string& service, const cpc::string& region, const cpc::string& uri)
{
   DebugLog(<< "OrchestrationServerInterface::removeServerImpl(): service: " << service.c_str() << " region: " << region.c_str() << " uri: " << uri.c_str());
   RedisAccess::RemoveServerArgs args;
   args.service = service;
   args.region = region;
   args.uri = uri;
   mRedis->removeServer(args, [](bool, const RedisAccess::RemoveServerResult&) {});
   return kSuccess;
}

int OrchestrationServerInterface::queryServers(const cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services)
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::queryServersImpl, this, services));
   return kSuccess;
}

int OrchestrationServerInterface::queryServersImpl(const cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services)
{
   QueryServersResult qsResult;
   for (const CPCAPI2::CloudConnector::ServiceDesc& sr : services)
   {
      std::shared_ptr<std::promise<bool> > p1 = std::make_shared<std::promise<bool> >();
      std::future<bool> f1 = p1->get_future();
      RedisAccess::QueryAvailableServersArgs query;
      query.service = sr.service;
      query.region = sr.region;
      StackLog(<< "OrchestrationServerInterface::queryServerImpl(): service: " << sr.service.c_str() << " region: " << sr.region.c_str());
      mRedis->queryAvailableServers(query, [p1, sr, &qsResult](bool, const RedisAccess::QueryAvailableServersResult& qasr) {
         const cpc::vector<RedisAccess::QueryAvailableServersResult::ServerInfo>& servers = qasr.availableServers;
         for (RedisAccess::QueryAvailableServersResult::ServerInfo rsi : servers)
         {
            CPCAPI2::OrchestrationServer::ServerInfo si;
            si.uri = rsi.uri;
            si.ttlSeconds = rsi.ttlSeconds;
            si.services.push_back(sr.service);
            si.region = sr.region;
            si.started = rsi.started;
            si.shutdown = rsi.shutdown;
            qsResult.servers.push_back(si);
            StackLog(<< "OrchestrationServerInterface::queryServerImpl(): result: uri: " << si.uri << " ttl: " << si.ttlSeconds << " region: " << si.region << " started: " << si.started << " shutdown: " << si.shutdown << " services: " << si.services.size());
         }
         p1->set_value(true);
      });

      if (f1.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
      {
         InfoLog(<< "OrchestrationServerInterface::queryServersImpl(): redis request timeout on queryAvailableServers request");
         break;
      }
   }
   fireEvent(cpcFunc(OrchestrationServerHandler::onQueryServersResult), 0, qsResult);
   return kSuccess;
}

int OrchestrationServerInterface::queryServerTtl(const cpc::string& uri)
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::queryServerTtlImpl, this, uri));
   return kSuccess;
}

int OrchestrationServerInterface::queryServerTtlImpl(const cpc::string& uri)
{
   RedisAccess::QueryServerTtlArgs query;
   query.uri = uri;
   mRedis->queryServerTtl(query, [&](bool, const RedisAccess::QueryServerTtlResult& res) {
      QueryServerTtlResult ttlResult;
      ttlResult.expiresInMs = res.expiresInMs;
      fireEvent(cpcFunc(OrchestrationServerHandler::onQueryServerTtlResult), 0, ttlResult);
   });
   return kSuccess;
}

int OrchestrationServerInterface::queryServerUsers(const cpc::string& uri)
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::queryServerUsersImpl, this, uri));
   return kSuccess;
}

int OrchestrationServerInterface::queryServerUsersImpl(const cpc::string& uri)
{
   RedisAccess::QueryServerUsersArgs query;
   query.uri = uri;
   mRedis->queryServerUsers(query, [&](bool, const RedisAccess::QueryServerUsersResult& res) {
      QueryServerUsersResult qr;
      qr.uri = query.uri;
      qr.endpointIds = res.endpointIds;
      fireEvent(cpcFunc(OrchestrationServerHandler::onQueryServerUsersResult), 0, qr);
   });
   return kSuccess;
}

int OrchestrationServerInterface::handleSetServerInfo(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   if (!functionObjectVal.HasMember("serverInfo"))
   {
      WarningLog(<< "Invalid request, missing serverInfo");
      return kError;
   }

   const rapidjson::Value& serverInfoVal = functionObjectVal["serverInfo"];
   if (!serverInfoVal.IsObject())
   {
      WarningLog(<< "Invalid request, serverInfo is not an object");
      return kError;
   }

   ServerInfo addServerArgs;
   JsonDeserialize(serverInfoVal, "region", addServerArgs.region, "uri", addServerArgs.uri, "ttlSeconds", addServerArgs.ttlSeconds, "started", addServerArgs.started, "shutdown", addServerArgs.shutdown, "services", addServerArgs.services);
   SetServerInfoResult ssiResult;
   setServerInfoImpl(addServerArgs, ssiResult);

   CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
   CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onSetServerInfoResult");
   serializer.addValue( "success", ssiResult.success);
   serializer.finalize();
   mTransport->send(conn, json);

   return kSuccess;
}

int OrchestrationServerInterface::handleSetServerTtl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   if (!functionObjectVal.HasMember("serverInfo"))
   {
      WarningLog(<< "Invalid request, missing serverTtlInfo");
      return kError;
   }

   const rapidjson::Value& serverInfoVal = functionObjectVal["serverInfo"];
   if (!serverInfoVal.IsObject())
   {
      WarningLog(<< "Invalid request, serverInfo is not an object");
      return kError;
   }

   ServerInfo serverArgs;
   JsonDeserialize(serverInfoVal, "region", serverArgs.region, "uri", serverArgs.uri, "ttlSeconds", serverArgs.ttlSeconds, "services", serverArgs.services);

   uint64_t ttlSec = 90;
   setServerTtlImpl(serverArgs.uri, ttlSec);

   return kSuccess;
}

int OrchestrationServerInterface::flushAll()
{
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::flushAllImpl, this));
   return kSuccess;
}

int OrchestrationServerInterface::flushAllImpl()
{
   InfoLog(<< "Orchestration Server flushAll()");

   if (mRedis)
      mRedis->flushAll();

   return kSuccess;
}

class OrchServerConfLookupResultHandler : public CPCAPI2::ConferenceBridge::LookupConferenceResultHandler
{
public:
   OrchServerConfLookupResultHandler(const std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::LookupConferenceResult&)>& cbOnLookupComplete)
   : mCbOnLookupComplete(cbOnLookupComplete) {}
   virtual ~OrchServerConfLookupResultHandler() {}

   virtual int onLookupConferenceComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::LookupConferenceResult& args) {
      mCbOnLookupComplete(registrar, args);
      delete this;
      return 0;
   }
   virtual bool synchronous() const {
      return true;
   }
private:
   std::function<void(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::LookupConferenceResult&)> mCbOnLookupComplete;
};

int OrchestrationServerInterface::handleRequestService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal)
{
   DebugLog(<< "OrchestrationServerInterface::handleRequestService");
   
   if (!functionObjectVal.HasMember("serviceRequests"))
   {
      WarningLog(<< "Invalid request, missing serviceRequests");
      return kError;
   }

   const rapidjson::Value& serviceRequestsVal = functionObjectVal["serviceRequests"];
   if (!serviceRequestsVal.IsArray())
   {
      WarningLog(<< "Invalid request, serviceRequests is not an array");
      return kError;
   }

   std::map<CPCAPI2::CloudConnector::ServiceDesc, resip::Data> mapServiceToUri;

   for (rapidjson::Value::ConstValueIterator it = serviceRequestsVal.Begin(); it != serviceRequestsVal.End(); ++it)
   {
      const rapidjson::Value& servReqItem = *it;
      if (servReqItem.IsObject())
      {
         if (!servReqItem.HasMember("service"))
         {
            continue;
         }
         if (!servReqItem.HasMember("region"))
         {
            continue;
         }

         cpc::string region, resource, service;
         JsonDeserialize(servReqItem, JSON_VALUE(region), JSON_VALUE(service));
         resource = CPCAPI2::JsonApi::JsonApiServerInterface::doEncrypt(conn.authToken.cp_user).c_str();

         InfoLog(<< "Orchestration Server requestService(): " << "\n\tregion: " << region << "\n\tresource: " << resource << "\n\tservice: " << service);

         CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mPhone);
         OrchServerConfLookupResultHandler* confLookupResultHandler = new OrchServerConfLookupResultHandler([&, conn, service, region, resource](CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle, const CPCAPI2::ConferenceBridge::LookupConferenceResult& lookupResult) {
            CPCAPI2::Json::JsonDataPointer json = CPCAPI2::Json::MakeJsonDataPointer();
            CPCAPI2::Json::JsonFunctionSerialize serializer(json, false, JSON_MODULE, "onRequestServiceResult");
            rapidjson::Writer<rapidjson::StringBuffer>& writer = serializer.getWriter();

            writer.Key("serviceMappings");

            if (lookupResult.success)
            {
               writer.StartArray();

               {
                  writer.StartObject();
                  serializer.addValue("service", service);
                  resip::Data serviceUri;
                  {
                     resip::DataStream ds(serviceUri);
                     ds << lookupResult.wsUrl;
                     //ds << "/jsonApi";
                  }
                  serializer.addValue("uri", serviceUri.c_str());
                  serializer.addValue("region", region);
                  writer.EndObject();
               }

               writer.EndArray();
            }

            serializer.finalize();

            InfoLog(<< "lookup result: " << lookupResult.success << ": " << resource << " --> " << lookupResult.wsUrl);
            SimpleWeb::string_view message(json->getMessageData(), json->getMessageSize());
            conn.https_response->write(SimpleWeb::StatusCode::success_ok, message);
         });
         confRegistrar->lookupConference(resource, confLookupResultHandler);
         break;
      }
   }

   return kSuccess;
}

int OrchestrationServerInterface::onRedisConnectionStatusChanged(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args)
{
   // StackLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChanged(): server: " << mConfig.redisIp << ":" << mConfig.redisPort << " status: " << args.connectionStatus  << " thread-id: " << std::this_thread::get_id());
   postToSdkThread(resip::resip_bind(&OrchestrationServerInterface::onRedisConnectionStatusChangedImpl, this, args));
   return kSuccess;
}
   
int OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args)
{
   StackLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(): server: " << mConfig.redisIp << ":" << mConfig.redisPort << " status: " << args.connectionStatus);
   mRedisConnectionTimer.cancel();
      
   if (args.connectionStatus == RedisConnectionStatus_Connected)
   {
      // Nothing to do
      StackLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(): server is connected, stop connection retry timer");
      mRedisConnectionTimeoutMsecs = REDIS_CONNECTION_RETRY_INTERVAL_MSECS;
   }
   else if (args.connectionStatus == RedisConnectionStatus_Disconnected)
   {
      // Nothing to do
      StackLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(): server is disconnected, stop connection retry timer");
      mRedisConnectionTimeoutMsecs = REDIS_CONNECTION_RETRY_INTERVAL_MSECS;
   }
   else if (args.connectionStatus == RedisConnectionStatus_ConnFailure)
   {
      StackLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(): connection failure to server: " << mConfig.redisIp << ":" << mConfig.redisPort);
      
      // Restart timer
      mRedisConnectionTimer.expires_from_now(mRedisConnectionTimeoutMsecs);
      mRedisConnectionTimer.async_wait(this, REDIS_CONNECTION_TIMER_ID, NULL);
      
      // Update duration
      if (mRedisConnectionTimeoutMsecs < REDIS_CONNECTION_RETRY_INTERVAL_MAXIMUM_MSECS)
         mRedisConnectionTimeoutMsecs = 2 * mRedisConnectionTimeoutMsecs;
   }
   else
   {
      InfoLog(<< "OrchestrationServerInterface::onRedisConnectionStatusChangedImpl(): invalid redis connection state: " << args.connectionStatus);
      assert(0);
   }
      
   return kSuccess;
}
   
void OrchestrationServerInterface::onTimer(unsigned short timerId, void* appState)
{
   StackLog(<< "OrchestrationServerInterface::onTimer(): timerId: " << timerId);
      
   if (timerId == REDIS_CONNECTION_TIMER_ID)
   {
      StackLog(<< "OrchestrationServerInterface::onTimer(): redis connection timerId: " << timerId);
      if (mRedis)
      {
         mRedis->shutdown();
      }
         
      // Retry connection
      DebugLog(<< "OrchestrationServerInterface::onTimer(): timerId: " << timerId << " database server: " << mConfig.redisIp << ":" << mConfig.redisPort);
      startImpl(mConfig);
   }
   else
   {
      InfoLog(<< "OrchestrationServerInterface::onTimer(): timerId: " << timerId << " is invalid");
      assert(0);
   }
}

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::ServiceMapping& args)
{
   std::stringstream ss;
   ss << "service: " << args.service << " uri: " << args.uri;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   std::stringstream ss;
   ss << "success: " << (args.success ? "true" : "false");
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RequestServiceResult& args)
{
   std::stringstream ss;
   ss << "service mappings (" << args.serviceMappings.size() << "): ";
   for (uint32_t i = 0; i < args.serviceMappings.size(); i++)
   {
      ss << get_debug_string(args.serviceMappings[i]) << " ";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServersResult& args)
{
   std::stringstream ss;
   ss << "servers (" << args.servers.size() << "): ";
   for (uint32_t i = 0; i < args.servers.size(); i++)
   {
      ss << get_debug_string(args.servers[i]) << " ";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args)
{
   std::stringstream ss;
   ss << "expiresInMs: " << args.expiresInMs;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args)
{
   std::stringstream ss;
   ss << "endpoints (" << args.endpointIds.size() << "): ";
   for (uint32_t i = 0; i < args.endpointIds.size(); i++)
   {
      ss << args.endpointIds[i].c_str() << " ";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::OrchestrationServerConfig& args)
{
   std::stringstream ss;
   ss << "redis server: " << args.redisIp << ":" << args.redisPort;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::ServerInfo& args)
{
   std::stringstream ss;
   ss << "region: " << args.region.c_str() << " uri: " << args.uri.c_str() << " watchdog: " << (args.watchdog ? "true" : "false") << " ttlSeconds: " << args.ttlSeconds << " started: " << (args.started ? "true" : "false") << " shutdown: " << (args.shutdown ? "true" : "false") << " services (";
   ss << args.services.size() << "): ";
   for (uint32_t i = 0; i < args.services.size(); i++)
   {
      ss << args.services[i].c_str() << " ";
   }
   return ss.str().c_str();
}
  
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::ServiceMapping& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RequestServiceResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::QueryServersResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::OrchestrationServerConfig& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::ServerInfo& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}

}

}

#endif
