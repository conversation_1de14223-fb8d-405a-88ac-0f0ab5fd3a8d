#include "brand_branded.h"

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)

#include "OrchestrationServerRedisAccess_Mock.h"

#include <future>

#include "../util/cpc_logger.h"

#include <rutil/Time.hxx>
#include <rutil/Data.hxx>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

namespace CPCAPI2
{

namespace OrchestrationServer
{
RedisAccessMock::RedisAccessMock() :
mServerAvailTimeoutSec(3628800), // 6 weeks
mHandler(NULL)
{
}

RedisAccessMock::~RedisAccessMock()
{
   shutdown();
}

int RedisAccessMock::initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   mServerAvailTimeoutSec = serverConfig.serverAvailableTimeoutSec;

   if (mClientThread.get() != NULL)
   {
      return 0;
   }

   mClientThread.reset(new std::thread([this, serverConfig, initCb]()
   {
      if (mHandler)
      {
         RedisConnectionStatusEvent args;
         args.connectionStatus = RedisConnectionStatus_Connected;
         args.statusDesc = "OK";
         mHandler->onRedisConnectionStatusChanged(args);
      }
      
      mIOS.run();
   }));

   return 0;
}

void RedisAccessMock::shutdown()
{
   if (mClientThread.get() == NULL)
   {
      return;
   }

   mClientThread->join();
   mIOS.stop();
   mClientThread.reset();
}

int RedisAccessMock::addServer(const AddServerArgs& serverInfo, const std::function<void(bool, const AddServerResult&)>& serverAddedCb)
{
   cpc::vector<cpc::string>::const_iterator it = serverInfo.services.begin();
   for (; it != serverInfo.services.end(); ++it)
   {
      std::stringstream serviceAndRegion;
      serviceAndRegion << *it << ":" << serverInfo.region;
      std::stringstream serverUri;
      serverUri << serverInfo.uri;

      std::map<std::string, std::vector<std::string> >::iterator itMap = mMapServiceRegionToUri.find(serviceAndRegion.str());
      if (itMap != mMapServiceRegionToUri.end())
      {
         std::vector<std::string>& servers = itMap->second;
         std::vector<std::string>::iterator itSrv = servers.begin();
         bool didUpdate = false;
         for (; itSrv != servers.end(); ++itSrv)
         {
            if (*itSrv == serverUri.str())
            {
               // update
               didUpdate = true;
               break;
            }
         }
         if (!didUpdate)
         {
            servers.push_back(serverUri.str());
         }
      }
      else
      {
         mMapServiceRegionToUri[serviceAndRegion.str()].push_back(serverUri.str());
      }
   }
   
   mMapUriToServerInfo[std::string(serverInfo.uri.c_str())] = serverInfo;
   
   AddServerResult res;
   res.result = 0;
   
   StackLog(<< "RedisAccessMock::addServer(): region: " << serverInfo.region << " uri: " << serverInfo.uri << " services: " << serverInfo.services.size() << " service-map count: " << mMapServiceRegionToUri.size());
   serverAddedCb(true, res);

   return 0;
}

int RedisAccessMock::removeServer(const RemoveServerArgs& removeReq, const std::function<void(bool, const RemoveServerResult&)>& removeServerCb)
{
   std::map<std::string, std::vector<std::string> >::iterator itMap = mMapServiceRegionToUri.begin();
   for (; itMap != mMapServiceRegionToUri.end(); ++itMap)
   {
      std::vector<std::string>& serverUris = itMap->second;
      std::vector<std::string>::iterator itSrv = serverUris.begin();
      for (; itSrv != serverUris.end(); ++itSrv)
      {
         if (std::strncmp(itSrv->c_str(), removeReq.uri.c_str(), removeReq.uri.size()) == 0)
         {
            serverUris.erase(itSrv);
            mMapServerUriToLastUpdateTimeMs.erase(*itSrv);
            break;
         }
      }
   }

   RemoveServerResult res;
   res.result = 0;
   removeServerCb(true, res);

   return 0;
}

int RedisAccessMock::setServerTtl(const SetServerTtlArgs& ttlReq, const std::function<void(bool, const SetServerTtlResult&)>& setServerTtlCb)
{
   uint64_t lastUpdateTimeMs = resip::ResipClock::getTimeMs();
   mMapServerUriToLastUpdateTimeMs[ttlReq.uri.c_str()] = lastUpdateTimeMs;
   
   SetServerTtlResult res;
   res.result = 0;
   setServerTtlCb(true, res);
   return 0;
}

int RedisAccessMock::queryServerTtl(const QueryServerTtlArgs& query, const std::function<void(bool, const QueryServerTtlResult&)>& queryResultCb)
{
   QueryServerTtlResult res;
   res.result = -1;
   std::map<std::string, uint64_t>::iterator itExp = mMapServerUriToLastUpdateTimeMs.find(query.uri.c_str());
   if (itExp != mMapServerUriToLastUpdateTimeMs.end())
   {
      res.result = 0;
      res.expiresInMs = std::max<int64_t>(0, (mServerAvailTimeoutSec * 1000) - (int64_t)(resip::ResipClock::getTimeMs() - itExp->second));
   }
   queryResultCb(true, res);
   return 0;
}

int RedisAccessMock::queryAvailableServers(const QueryAvailableServersArgs& serviceRequest, const std::function<void(bool, const QueryAvailableServersResult&)>& queryAvailableServersCb)
{
   uint64_t nowMs = resip::ResipClock::getTimeMs();
   std::stringstream serverAndRegion;
   serverAndRegion << serviceRequest.service << ":" << serviceRequest.region;
   std::map<std::string, std::vector<std::string> >::iterator it = mMapServiceRegionToUri.begin();
   QueryAvailableServersResult res;
   for (; it != mMapServiceRegionToUri.end(); ++it)
   {
      if ((it->first.find(serviceRequest.service) != std::string::npos) &&
         (it->first.find(serviceRequest.region) != std::string::npos))
      {
         const std::vector<std::string>& servers = it->second;
         std::vector<std::string>::const_iterator itSrv = servers.begin();
         for (; itSrv != servers.end(); ++itSrv)
         {
            std::map<std::string, uint64_t>::iterator itExp = mMapServerUriToLastUpdateTimeMs.find(*itSrv);
            QueryAvailableServersResult::ServerInfo si;
            si.uri = std::move(cpc::string(itSrv->c_str(), itSrv->size()));
            si.watchdog = false;
            
            std::map<std::string, AddServerArgs>::iterator itExp2 = mMapUriToServerInfo.find(std::string(si.uri.c_str()));
            if (itExp2 != mMapUriToServerInfo.end())
            {
               si.started = itExp2->second.started;
               si.shutdown = itExp2->second.shutdown;
            }
            
            if (itExp == mMapServerUriToLastUpdateTimeMs.end() || (int64_t)(nowMs - itExp->second) < (mServerAvailTimeoutSec * 1000))
            {
               if (itExp == mMapServerUriToLastUpdateTimeMs.end())
               {
                  si.ttlSeconds = INT64_MAX;
               }
               else
               {
                  si.ttlSeconds = std::max<int64_t>(0, (mServerAvailTimeoutSec * 1000) - (int64_t)(resip::ResipClock::getTimeMs() - itExp->second));
               }
               if (std::find(res.availableServers.begin(), res.availableServers.end(), si) == res.availableServers.end())
               {
                  res.availableServers.push_back(si);
               }
            }
         }
      }
   }
   res.result = 0;
   
   StackLog(<< "RedisAccessMock::queryAvailableServers(): region: " << serviceRequest.region << " service: " << serviceRequest.service << " service-map count: " << mMapServiceRegionToUri.size() << " available-servers: " << res.availableServers.size());
   queryAvailableServersCb(true, res);

   return 0;
}

int RedisAccessMock::addUserSession(const AddUserSessionArgs& addUserSessionArgs, const std::function<void(bool, const AddUserSessionResult&)>& addUserSessionCb)
{
   std::stringstream keyName;
   keyName << addUserSessionArgs.userIdentity << ":" << addUserSessionArgs.service;
   mUserSessionsMap[keyName.str()] = addUserSessionArgs.uri.c_str();
   AddUserSessionResult res;
   res.result = 0;

   std::stringstream userDeviceVal;
   userDeviceVal << addUserSessionArgs.userIdentity << ":" << addUserSessionArgs.deviceIdentity;
   mMapServerToUsers[addUserSessionArgs.uri.c_str()].insert(userDeviceVal.str());
   
   StackLog(<< "RedisAccessMock::addUserSession(): user-identity: " << addUserSessionArgs.userIdentity << " service: " << addUserSessionArgs.service << " uri: " << addUserSessionArgs.uri << " user-map count: " << mUserSessionsMap.size());
   addUserSessionCb(true, res);
   return 0;
}

int RedisAccessMock::queryUserSessions(const QueryUserSessionsArgs& queryArgs, const std::function<void(bool, const QueryUserSessionsResult&)>& queryUserSessionsCb)
{
   std::stringstream keyName;
   keyName << queryArgs.userIdentity << ":" << queryArgs.service;
   std::map<std::string, std::string>::iterator it = mUserSessionsMap.find(keyName.str());
   if (it != mUserSessionsMap.end())
   {
      QueryUserSessionsResult res;
      res.result = 0;
      res.uri = it->second.c_str();
      queryUserSessionsCb(true, res);
      return 0;
   }

   QueryUserSessionsResult res;
   res.result = -1;
   
   StackLog(<< "RedisAccessMock::queryUserSessions(): user-identity: " << queryArgs.userIdentity << " service: " << queryArgs.service << " user-map count: " << mUserSessionsMap.size() << " service-uri: " << res.uri);
   queryUserSessionsCb(false, res);
   return 0;
}

int RedisAccessMock::queryServerUsers(const QueryServerUsersArgs& queryArgs, const std::function<void(bool, const QueryServerUsersResult&)>& queryServerUsersCb)
{
   QueryServerUsersResult res;
   res.result = 0;

   auto it = mMapServerToUsers.find(queryArgs.uri.c_str());
   if (it != mMapServerToUsers.end())
   {
      const std::set<std::string>& users = it->second;
      auto itUsers = users.begin();
      for (; itUsers != users.end(); ++itUsers)
      {
         res.endpointIds.push_back(itUsers->c_str());
      }
   }

   queryServerUsersCb(false, res);
   return 0;
}

int RedisAccessMock::flushAll()
{
   mMapServiceRegionToUri.clear();
   mUserSessionsMap.clear();
   return 0;
}

int RedisAccessMock::setHandler(RedisConnectionHandler* handler)
{
   DebugLog(<< "RedisAccessMock::setHandler(): mHandler: " << mHandler << " will be updated to: handler: " << handler);
   mHandler = handler;
   return kSuccess;
}
   
}

}

#endif
