#pragma once

#if !defined(CPCAPI2_ORCH_SERVER_REDIS_ACCESS)
#define CPCAPI2_ORCH_SERVER_REDIS_ACCESS

#include "OrchestrationServerRedisAccessIf.h"

#include <cpcapi2defs.h>
#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <boost/asio.hpp>
#include <redisclient.h>

#include <functional>
#include <thread>

namespace CPCAPI2
{

namespace OrchestrationServer
{

class RedisAccess : public RedisAccessBase
{

public:

   RedisAccess();
   virtual ~RedisAccess();

   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) OVERRIDE;
   virtual void shutdown() OVERRIDE;

   virtual int addServer(const AddServerArgs& serverInfo, const std::function<void(bool, const AddServerResult&)>& serverAddedCb) OVERRIDE;
   virtual int removeServer(const RemoveServerArgs& removeReq, const std::function<void(bool, const RemoveServerResult&)>& removeServerCb) OVERRIDE;
   virtual int setServerTtl(const SetServerTtlArgs& ttlReq, const std::function<void(bool, const SetServerTtlResult&)>& setServerTtlCb) OVERRIDE;
   virtual int queryServerTtl(const QueryServerTtlArgs& query, const std::function<void(bool, const QueryServerTtlResult&)>& queryResultCb) OVERRIDE;
   virtual int queryAvailableServers(const QueryAvailableServersArgs& serviceRequest, const std::function<void(bool, const QueryAvailableServersResult&)>& queryAvailableServersCb) OVERRIDE;

   virtual int addUserSession(const AddUserSessionArgs& addUserSessionArgs, const std::function<void(bool, const AddUserSessionResult&)>& addUserSessionCb) OVERRIDE;

   virtual int queryUserSessions(const QueryUserSessionsArgs& queryArgs, const std::function<void(bool, const QueryUserSessionsResult&)>& queryUserSessionsCb) OVERRIDE;
   virtual int queryServerUsers(const QueryServerUsersArgs& queryArgs, const std::function<void(bool, const QueryServerUsersResult&)>& queryServerUsersCb) OVERRIDE;

   virtual int flushAll() OVERRIDE;

   virtual int setHandler(RedisConnectionHandler* handler) OVERRIDE;

private:

   void handleConnected(const std::function<void(bool, const cpc::string&)>& initCb, bool success, const std::string& msg);

private:

   std::unique_ptr<std::thread> mClientThread;
   boost::asio::io_service mIOS;
   std::unique_ptr<redisclient::RedisAsyncClient> mRedisClient;
   int64_t mServerAvailTimeoutSec;

   // Handler to allow database connectivity owner to be informed about connection status
   RedisConnectionHandler* mHandler;

};

}

}

#endif // CPCAPI2_ORCH_SERVER_REDIS_ACCESS
