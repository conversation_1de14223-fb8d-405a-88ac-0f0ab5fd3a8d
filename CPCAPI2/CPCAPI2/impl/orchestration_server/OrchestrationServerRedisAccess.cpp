#include "brand_branded.h"

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)

#include "OrchestrationServerRedisAccess.h"

#include <rutil/Time.hxx>
#include <rutil/Data.hxx>

#include <future>

#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_CONTROL

#define ORCH_DB_NAMESPACE std::string("cpcapi2::orchestrationserver::")
#define ORCH_DB_SERVICEANDREGION_NAMESPACE(_str) ORCH_DB_NAMESPACE + std::string("serviceandregion::" + _str)
#define ORCH_DB_SERVERINFO_NAMESPACE(_str) ORCH_DB_NAMESPACE + std::string("serverinfo::" + _str)
#define ORCH_DB_USERSESSIONS_NAMESPACE(_str) ORCH_DB_NAMESPACE + std::string("usersessions::" + _str)
#define ORCH_DB_USERANDSERVICE_NAMESPACE(_str) ORCH_DB_NAMESPACE + std::string("userandservice::" + _str)
#define ORCH_DB_SERVERINFO_STARTED_ELEMENT std::string("started")
#define ORCH_DB_SERVERINFO_SHUTDOWN_ELEMENT std::string("shutdown")


namespace CPCAPI2
{

namespace OrchestrationServer
{

RedisAccess::RedisAccess() :
mRedisClient(new redisclient::RedisAsyncClient(mIOS)),
mServerAvailTimeoutSec(3628800), // 6 weeks
mHandler(NULL)
{
   // StackLog(<< "OrchestrationServerRedisAccess(): " << this);
}

RedisAccess::~RedisAccess()
{
   // StackLog(<< "~OrchestrationServerRedisAccess(): " << this);
}

int RedisAccess::initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb)
{
   DebugLog(<< "OrchestrationServerRedisAccess::initialize(): serverConfig: " << serverConfig);

   mServerAvailTimeoutSec = serverConfig.serverAvailableTimeoutSec;

   if (mClientThread.get() != NULL)
   {
      return 0;
   }

   mClientThread.reset(new std::thread([this, serverConfig, initCb]()
   {
      boost::asio::ip::address address = boost::asio::ip::address::from_string(serverConfig.ip.c_str());
      const unsigned short port = serverConfig.port;
      boost::asio::ip::tcp::endpoint endpoint(address, port);

      try
      {
         mRedisClient->connect(endpoint, std::bind(&RedisAccess::handleConnected, this, initCb, std::placeholders::_1, std::placeholders::_2));
         mIOS.run();
         DebugLog(<< "OrchestrationServerRedisAccess::initialize(): run completed");
      }
      catch (std::exception& e)
      {
         WarningLog(<< "OrchestrationServerRedisAccess::initialize(): exception: " << e.what());
         if (mHandler)
         {
            RedisConnectionStatusEvent args;
            args.connectionStatus = RedisConnectionStatus_ConnFailure;
            args.statusDesc = e.what();
            mHandler->onRedisConnectionStatusChanged(args);
         }
      }
   }));

   return 0;
}

void RedisAccess::shutdown()
{
   if (mClientThread.get() == NULL)
   {
      return;
   }

   mIOS.post([this]() {
      mRedisClient->disconnect();
   });

   mClientThread->join();
   mRedisClient.reset();
   mIOS.stop();
   mClientThread.reset();
   
   mHandler = NULL;
}

int RedisAccess::addServer(const AddServerArgs& serverInfo, const std::function<void(bool, const AddServerResult&)>& serverAddedCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::addServer(): redis client not connected");
      AddServerResult rerr;
      rerr.result = -1;
      serverAddedCb(true, rerr);
      return 0;
   }
   
   std::stringstream serverUri;
   serverUri << serverInfo.uri;
   
   cpc::vector<cpc::string>::const_iterator it = serverInfo.services.begin();
   for (; it != serverInfo.services.end(); ++it)
   {
      std::stringstream serviceAndRegion;
      serviceAndRegion << *it << ":" << serverInfo.region;
      
      // ZADD key [NX|XX] [CH] [INCR] score member [score member ...]
      // NX: Don't update already existing elements. Always add new elements.
      // .jjg. The score is hardcoded to 0 for now, but in the future we can use it to specify primary and backup servers.
      // InfoLog(<< "OrchestrationServerRedisAccess::addServer(): command: " << ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()));
      mRedisClient->command("ZADD", { ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()), "NX", "0", serverUri.str().c_str() });
   }
      
   std::stringstream started;
   started << serverInfo.started;
   std::stringstream shutdown;
   shutdown << serverInfo.shutdown;
   // InfoLog(<< "OrchestrationServerRedisAccess::addServer(): command: " << ORCH_DB_SERVERINFO_NAMESPACE(serverUri.str()));
   mRedisClient->command("HMSET", { ORCH_DB_SERVERINFO_NAMESPACE(serverUri.str()), "started", started.str().c_str(), "shutdown", shutdown.str().c_str() });
   
   AddServerResult res;
   res.result = 0;
   serverAddedCb(true, res);
   return 0;
}

int RedisAccess::removeServer(const RemoveServerArgs& serverInfo, const std::function<void(bool, const RemoveServerResult&)>& serverRemovedCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::removeServer(): redis client not connected");
      RemoveServerResult rerr;
      rerr.result = -1;
      serverRemovedCb(true, rerr);
      return 0;
   }
   
   std::stringstream serviceAndRegion;
   serviceAndRegion << serverInfo.service << ":" << serverInfo.region;
   // InfoLog(<< "OrchestrationServerRedisAccess::removeServer(): command: " << ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()));
   mRedisClient->command("ZREM", { ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()), serverInfo.uri.c_str() });

   RemoveServerResult res;
   res.result = 0;
   serverRemovedCb(true, res);
   return 0;
}

int RedisAccess::setServerTtl(const SetServerTtlArgs& ttlReq, const std::function<void(bool, const SetServerTtlResult&)>& setServerTtlCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::setServerTtl(): redis client not connected");
      SetServerTtlResult rerr;
      rerr.result = -1;
      setServerTtlCb(true, rerr);
      return 0;
   }
   
   std::stringstream serverKeyName;
   serverKeyName << ttlReq.uri;
   // InfoLog(<< "OrchestrationServerRedisAccess::setServerTtl(): command: " << ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()));
   mRedisClient->command("EXPIRE", { ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()), resip::Data::from(ttlReq.ttl).c_str() });
   SetServerTtlResult res;
   res.result = 0;
   setServerTtlCb(true, res);
   return 0;
}

int RedisAccess::queryServerTtl(const QueryServerTtlArgs& query, const std::function<void(bool, const QueryServerTtlResult&)>& queryResultCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryServerTtl(): redis client not connected");
      QueryServerTtlResult rerr;
      rerr.result = -1;
      queryResultCb(true, rerr);
      return 0;
   }
   
   QueryServerTtlResult rerr;
   rerr.result = -1;
   rerr.expiresInMs = -1;
   queryResultCb(true, rerr);
   return 0;
}

int RedisAccess::queryAvailableServers(const QueryAvailableServersArgs& serviceRequest, const std::function<void(bool, const QueryAvailableServersResult&)>& queryAvailableServersCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis client not connected");
      QueryAvailableServersResult rerr;
      rerr.result = -1;
      queryAvailableServersCb(true, rerr);
      return 0;
   }
   
   std::stringstream serviceAndRegion;
   serviceAndRegion << serviceRequest.service << ":" << serviceRequest.region;
   
   // StackLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): server: " << serviceAndRegion.str().c_str());
   
   std::shared_ptr<std::promise<std::vector<std::string>>> p1 = std::make_shared<std::promise<std::vector<std::string>>>();
   std::future<std::vector<std::string>> f1 = p1->get_future();
   // InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): command: " << ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()));
   mRedisClient->command("ZRANGE", { ORCH_DB_SERVICEANDREGION_NAMESPACE(serviceAndRegion.str()), "0", "1" }, [p1](const redisclient::RedisValue& v1)
   {
      std::vector<std::string> serverUris;
      if (v1.isOk() && v1.isArray())
      {
         std::vector<redisclient::RedisValue> resultVals = v1.toArray();
         std::vector<redisclient::RedisValue>::const_iterator itVals = resultVals.begin();
         for (; itVals != resultVals.end(); ++itVals)
         {
            serverUris.push_back(itVals->toString());
         }
      }
      p1->set_value(serverUris);
   });

   if (f1.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis request timeout");
      QueryAvailableServersResult rerr;
      rerr.result = -1;
      queryAvailableServersCb(true, rerr);
      return 0;
   }

   std::vector<std::string> serverUris = f1.get();
   if (serverUris.size() == 0)
   {
      QueryAvailableServersResult rerr;
      rerr.result = -1;
      queryAvailableServersCb(true, rerr);
      return 0;
   }

   QueryAvailableServersResult res;
   for (const std::string& serverUri : serverUris)
   {
      auto p2 = std::make_shared<std::promise<int64_t>>();
      std::future<int64_t> f2 = p2->get_future();
      std::stringstream serverKeyName;
      serverKeyName << serverUri;
      
      // StackLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): server-key: " << serverKeyName.str().c_str());
      // InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): command: TTL " << ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()));
      mRedisClient->command("TTL", { ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()) }, [p2](const redisclient::RedisValue& v2)
      {
         p2->set_value(v2.toInt());
      });

      /*
       The command returns -2 if the key does not exist.
       The command returns -1 if the key exists but has no associated expire.
       */
      if (f2.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
      {
         InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis request timeout for TTL");
         res.result = -1;
         break;
      }

      std::shared_ptr<std::promise<QueryAvailableServersResult::ServerInfo>> p3 = std::make_shared<std::promise<QueryAvailableServersResult::ServerInfo>>();
      std::future<QueryAvailableServersResult::ServerInfo> f3 = p3->get_future();
      // InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): command: HGETALL " << ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()));
      mRedisClient->command("HGETALL", { ORCH_DB_SERVERINFO_NAMESPACE(serverKeyName.str()) }, [p3](const redisclient::RedisValue& v3)
      {
         // InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis response received for server info started status");
         if (v3.isOk() && v3.isArray())
         {
            QueryAvailableServersResult::ServerInfo si;
            std::vector<redisclient::RedisValue> results = v3.toArray();
            // StackLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis response received for server info with array with size: " << results.size());
            for (int i = 0; i < results.size(); ++i)
            {
               if (results[i].isOk() && results[i].isString())
               {
                  // InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis array iteration is OK with value: " << results[i].toString());
                  
                  if (results[i].toString().compare(ORCH_DB_SERVERINFO_STARTED_ELEMENT) == 0)
                  {
                     ++i;
                     if (i < results.size())
                     {
                        si.started = resip::Data(results[i].toString()).convertInt();
                     }
                  }
                  else if (results[i].toString().compare(ORCH_DB_SERVERINFO_SHUTDOWN_ELEMENT) == 0)
                  {
                     ++i;
                     if (i < results.size())
                     {
                        si.shutdown = resip::Data(results[i].toString()).convertInt();
                     }
                  }
               }
            }
            
            p3->set_value(si);
         }
      });
      
      if (f3.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
      {
         InfoLog(<< "OrchestrationServerRedisAccess::queryAvailableServers(): redis request timeout for server info started status");
         res.result = -1;
         break;
      }
      
      QueryAvailableServersResult::ServerInfo si = f3.get();
      si.uri = serverUri.c_str();
      si.watchdog = false;
      si.ttlSeconds = f2.get(); // !jjg! fixme
      // si.started;
      // si.shutdown;
      si.service = serviceRequest.service;
      si.region = serviceRequest.region;
      res.availableServers.push_back(si);
      res.result = 0;
   }

   queryAvailableServersCb(true, res);
   return 0;
}

int RedisAccess::addUserSession(const AddUserSessionArgs& addUserSessionArgs, const std::function<void(bool, const AddUserSessionResult&)>& addUserSessionCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::addUserSession(): redis client not connected");
      AddUserSessionResult rerr;
      rerr.result = -1;
      addUserSessionCb(true, rerr);
      return 0;
   }
   
   std::stringstream keyName;
   keyName << addUserSessionArgs.userIdentity << ":" << addUserSessionArgs.service;
   // InfoLog(<< "OrchestrationServerRedisAccess::addUserSession(): command: " << ORCH_DB_USERANDSERVICE_NAMESPACE(keyName.str()));
   mRedisClient->command("SET", { ORCH_DB_USERANDSERVICE_NAMESPACE(keyName.str()), addUserSessionArgs.uri.c_str() }, [addUserSessionCb](const redisclient::RedisValue& v) {
   });
   std::stringstream userVal;
   userVal << addUserSessionArgs.userIdentity << ":" << addUserSessionArgs.deviceIdentity;
   std::stringstream serverKeyName;
   serverKeyName << addUserSessionArgs.uri;
   // InfoLog(<< "OrchestrationServerRedisAccess::addUserSession(): command: " << ORCH_DB_USERSESSIONS_NAMESPACE(serverKeyName.str()));
   mRedisClient->command("SADD", { ORCH_DB_USERSESSIONS_NAMESPACE(serverKeyName.str()), userVal.str() }, [](const redisclient::RedisValue& v) {
   });

   AddUserSessionResult res;
   res.result = 0;
   addUserSessionCb(true, res);
   return 0;
}

int RedisAccess::queryUserSessions(const QueryUserSessionsArgs& queryArgs, const std::function<void(bool, const QueryUserSessionsResult&)>& queryUserSessionsCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryUserSessions(): redis client not connected");
      QueryUserSessionsResult rerr;
      rerr.result = -1;
      queryUserSessionsCb(true, rerr);
      return 0;
   }
   
   std::stringstream keyName;
   keyName << queryArgs.userIdentity << ":" << queryArgs.service;
   std::shared_ptr<std::promise<QueryUserSessionsResult> > p1 = std::make_shared<std::promise<QueryUserSessionsResult> >();
   std::future<QueryUserSessionsResult> f1 = p1->get_future();
   // InfoLog(<< "OrchestrationServerRedisAccess::queryUserSessions(): command: " << ORCH_DB_USERANDSERVICE_NAMESPACE(keyName.str()));
   mRedisClient->command("GET", { ORCH_DB_USERANDSERVICE_NAMESPACE(keyName.str()) }, [p1](const redisclient::RedisValue& v) {
      QueryUserSessionsResult res;
      if (v.isOk() && v.isString())
      {
         res.result = 0;
         res.uri = v.toString().c_str();
      }
      else
      {
         res.result = -1;
      }
      p1->set_value(res);
   });
   
   if (f1.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryUserSessions(): redis request timeout");
      QueryUserSessionsResult rerr;
      rerr.result = -1;
      queryUserSessionsCb(true, rerr);
      return 0;
   }

   queryUserSessionsCb(true, f1.get());
   return 0;
}

int RedisAccess::queryServerUsers(const QueryServerUsersArgs& queryArgs, const std::function<void(bool, const QueryServerUsersResult&)>& queryServerUsersCb)
{
   if (!mRedisClient->isConnected())
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryServerUsers(): redis client not connected");
      QueryServerUsersResult rerr;
      rerr.result = -1;
      queryServerUsersCb(true, rerr);
      return 0;
   }
   
   std::stringstream serverKeyName;
   serverKeyName << queryArgs.uri;
   auto p1 = std::make_shared<std::promise<QueryServerUsersResult> >();
   std::future<QueryServerUsersResult> f1 = p1->get_future();
   // InfoLog(<< "OrchestrationServerRedisAccess::queryServerUsers(): command: " << ORCH_DB_USERSESSIONS_NAMESPACE(serverKeyName.str()));
   mRedisClient->command("SMEMBERS", { ORCH_DB_USERSESSIONS_NAMESPACE(serverKeyName.str()) }, [p1](const redisclient::RedisValue& v) {
      QueryServerUsersResult res;
      if (v.isOk() && v.isArray())
      {
         res.result = 0;
         std::vector<redisclient::RedisValue> resultArray(std::move(v.toArray()));
         for (redisclient::RedisValue rv : resultArray)
         {
            res.endpointIds.push_back(rv.toString().c_str());
         }
      }
      else
      {
         res.result = -1;
      }
      p1->set_value(res);
   });
   
   if (f1.wait_for(std::chrono::milliseconds(2000)) != std::future_status::ready)
   {
      InfoLog(<< "OrchestrationServerRedisAccess::queryServerUsers(): redis request timeout");
      QueryServerUsersResult rerr;
      rerr.result = -1;
      queryServerUsersCb(true, rerr);
      return 0;
   }
   
   queryServerUsersCb(true, f1.get());
   return 0;
}

int RedisAccess::flushAll()
{
   std::promise<void> p1;
   std::future<void> f1 = p1.get_future();
   mRedisClient->command("FLUSHALL", { }, [&p1](const redisclient::RedisValue& v) {
      p1.set_value();
   });
   f1.wait();
   return 0;
}
   
int RedisAccess::setHandler(RedisConnectionHandler* handler)
{
   DebugLog(<< "OrchestrationServerRedisAccess::setHandler(): mHandler: " << mHandler << " will be updated to: handler: " << handler);
   mHandler = handler;
   return 0;
}
   
void RedisAccess::handleConnected(const std::function<void(bool, const cpc::string&)>& initCb, bool success, const std::string& msg)
{
   InfoLog(<< "OrchestrationServerRedisAccess::handleConnected(): success: " << success << " status: " << msg);
      
   if (mHandler)
   {
      RedisConnectionStatusEvent args;
      (success ? args.connectionStatus = RedisConnectionStatus_Connected : args.connectionStatus = RedisConnectionStatus_ConnFailure);
      args.statusDesc = msg.c_str();
      mHandler->onRedisConnectionStatusChanged(args);
   }
}
      
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessConfig& args)
{
   std::stringstream ss;
   ss << " RedisAccessConfig: ip: " << args.ip.c_str() << " port: " << args.port << " serverAvailableTimeoutSec: " << args.serverAvailableTimeoutSec;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerArgs& args)
{
   std::stringstream ss;
   ss << " AddServerArgs: region: " << args.region << " uri: " << args.uri << " started: " << args.started
      << " shutdown: " << args.shutdown << " services (" << args.services.size() << "): ";
   for (int i = 0; i < args.services.size(); i++) { ss << "{" << args.services[i] << "}"; }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerResult& args)
{
   std::stringstream ss;
   ss << " AddServerResult: result: " << args.result;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerArgs& args)
{
   std::stringstream ss;
   ss << " RemoveServerArgs: uri: " << args.uri << " service: " << args.service << " region: " << args.region;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerResult& args)
{
   std::stringstream ss;
   ss << " RemoveServerResult: result: " << args.result;
   return ss.str().c_str();
}
  
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersArgs& args)
{
   std::stringstream ss;
   ss << " QueryAvailableServersArgs: service: " << args.service << " region: " << args.region;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult::ServerInfo& args)
{
   std::stringstream ss;
   ss << " ServerInfo: uri: " << args.uri << " watchdog: " << (args.watchdog ? "enabled" : "disabled") << " ttlSeconds: " << args.ttlSeconds
      << " started: " << args.started << " shutdown: " << args.shutdown << " service: " << args.service << " region: " << args.region;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult& args)
{
   std::stringstream ss;
   ss << " QueryAvailableServersResult: result: " << args.result << " available servers (" << args.availableServers.size() << "): ";
   for (int i = 0; i < args.availableServers.size(); i++) { ss << "{" << get_debug_string(args.availableServers[i]) << "}"; }
   return ss.str().c_str();
}
  
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlArgs& args)
{
   std::stringstream ss;
   ss << " SetServerTtlArgs: uri: " << args.uri << " ttl: " << args.ttl;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlResult& args)
{
   std::stringstream ss;
   ss << " SetServerTtlResult: result: " << args.result;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlArgs& args)
{
   std::stringstream ss;
   ss << " QueryServerTtlArgs: uri: " << args.uri;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlResult& args)
{
   std::stringstream ss;
   ss << " QueryServerTtlResult: result: " << args.result << " expiresInMs: " << args.expiresInMs;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionArgs& args)
{
   std::stringstream ss;
   ss << " AddUserSessionArgs: userIdentity: " << args.userIdentity << " deviceIdentity: " << args.deviceIdentity << " service: " << args.service << " uri: " << args.uri;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionResult& args)
{
   std::stringstream ss;
   ss << " AddUserSessionResult: result: " << args.result;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsArgs& args)
{
   std::stringstream ss;
   ss << " QueryUserSessionsArgs: userIdentity: " << args.userIdentity << " service: " << args.service;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsResult& args)
{
   std::stringstream ss;
   ss << " QueryUserSessionsResult: result: " << args.result << " uri: " << args.uri;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersArgs& args)
{
   std::stringstream ss;
   ss << " QueryServerUsersArgs: uri: " << args.uri;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersResult& args)
{
   std::stringstream ss;
   ss << " QueryServerUsersResult: result: " << args.result << " endpointIds (" << args.endpointIds.size() << "): ";
   for (int i = 0; i < args.endpointIds.size(); i++) { ss << "{" << args.endpointIds[i] << "}"; }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisConnectionStatus& args)
{
   switch (args)
   {
      case RedisConnectionStatus_Connected: return "connected";
      case RedisConnectionStatus_ConnFailure: return "failure";
      default: return "disconnected";
   }
      
   return "disconnected";
}
   
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args)
{
   std::stringstream ss;
   ss << " RedisConnectionStatusEvent: status: " << args.connectionStatus << " description: " << args.statusDesc;
   return ss.str().c_str();
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessConfig& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult::ServerInfo& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersArgs& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersResult& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisConnectionStatus& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args)
{
   os << CPCAPI2::OrchestrationServer::get_debug_string(args);
   return os;
}
   
}

}

#endif
