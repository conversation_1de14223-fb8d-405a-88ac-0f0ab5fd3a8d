#pragma once

#if !defined(CPCAPI2_ORCH_SERVER_REDIS_ACCESS_IF)
#define CPCAPI2_ORCH_SERVER_REDIS_ACCESS_IF

#include <cpcstl/string.h>
#include <cpcstl/vector.h>

#include <functional>

namespace CPCAPI2
{

namespace OrchestrationServer
{

enum RedisConnectionStatus
{
   RedisConnectionStatus_Connected = 0,
   RedisConnectionStatus_Disconnected = 1,
   RedisConnectionStatus_ConnFailure = 2
};
   
struct RedisConnectionStatusEvent
{
   RedisConnectionStatus connectionStatus;
   cpc::string statusDesc;
      
   RedisConnectionStatusEvent() : connectionStatus(RedisConnectionStatus_Disconnected), statusDesc("") {}
};
   
class RedisConnectionHandler
{
      
public:
      
   virtual~ RedisConnectionHandler() {}
      
   virtual int onRedisConnectionStatusChanged(const RedisConnectionStatusEvent& args) = 0;
   
};
   
struct RedisAccessConfig
{
   cpc::string ip;
   int port;
   int64_t serverAvailableTimeoutSec;
   
   RedisAccessConfig() : ip(""), port(0), serverAvailableTimeoutSec(3628800) {}
};

class RedisAccessBase
{

public:

   struct AddServerArgs
   {
      cpc::vector<cpc::string> services;
      cpc::string region;
      cpc::string uri;
      bool started;
      bool shutdown;
      
      AddServerArgs() : region(""), uri(""), started(false), shutdown(false) {}
   };

   struct AddServerResult
   {
      int result;
      
      AddServerResult() : result(-1) {}
   };

   struct RemoveServerArgs
   {
      cpc::string uri;
      cpc::string service;
      cpc::string region;
      
      RemoveServerArgs() : uri(""), service(""), region("") {}
   };

   struct RemoveServerResult
   {
      int result;
      
      RemoveServerResult() : result(-1) {}
   };

   struct QueryAvailableServersArgs
   {
      cpc::string service;
      cpc::string region;
      
      QueryAvailableServersArgs() : service(""), region("") {}
   };

   struct QueryAvailableServersResult
   {
      int result;

      struct ServerInfo
      {
         cpc::string uri;
         bool watchdog;
         int64_t ttlSeconds;
         bool started;
         bool shutdown;
         cpc::string service;
         cpc::string region;

         ServerInfo() : uri(""), watchdog(false), ttlSeconds(-1), started(false), shutdown(false), service(""), region("") {}

         bool operator==(const ServerInfo& rhs)
         {
            return (uri == rhs.uri);
         }
      };
   
      cpc::vector<QueryAvailableServersResult::ServerInfo> availableServers;
      
      QueryAvailableServersResult() : result(-1) {}
   };

   struct SetServerTtlArgs
   {
      cpc::string uri;
      int64_t ttl;
      
      SetServerTtlArgs() : uri(""), ttl(-1) {}
   };

   struct SetServerTtlResult
   {
      int result;
      
      SetServerTtlResult() : result(-1) {}
   };

   struct QueryServerTtlArgs
   {
      cpc::string uri;
      
      QueryServerTtlArgs() : uri("") {}
   };

   struct QueryServerTtlResult
   {
      int result;
      int64_t expiresInMs;
      
      QueryServerTtlResult() : result(-1), expiresInMs(-1) {}
   };

   struct AddUserSessionArgs
   {
      cpc::string userIdentity;
      cpc::string deviceIdentity;
      cpc::string service;
      cpc::string uri;
      
      AddUserSessionArgs() : userIdentity(""), deviceIdentity(""), service(""), uri("") {}
   };

   struct AddUserSessionResult
   {
      int result;
      
      AddUserSessionResult() : result(-1) {}
   };

   struct QueryUserSessionsArgs
   {
      cpc::string userIdentity;
      cpc::string service;
      
      QueryUserSessionsArgs() : userIdentity(""), service("") {}
   };

   struct QueryUserSessionsResult
   {
      int result;
      cpc::string uri;
      
      QueryUserSessionsResult() : result(-1), uri("") {}
   };

   struct QueryServerUsersArgs
   {
      cpc::string uri;
      
      QueryServerUsersArgs() : uri("") {}
   };

   struct QueryServerUsersResult
   {
      int result;
      cpc::vector<cpc::string> endpointIds;
      
      QueryServerUsersResult() : result(-1) {}
   };

   virtual ~RedisAccessBase() {}
   
   virtual int initialize(const RedisAccessConfig& serverConfig, const std::function<void(bool, const cpc::string&)>& initCb) = 0;
   
   virtual void shutdown() = 0;
   
   virtual int addServer(const AddServerArgs& serverInfo, const std::function<void(bool, const AddServerResult&)>& serverAddedCb) = 0;
   
   virtual int removeServer(const RemoveServerArgs& removeReq, const std::function<void(bool, const RemoveServerResult&)>& removeServerCb) = 0;
   
   virtual int queryAvailableServers(const QueryAvailableServersArgs& serviceRequest, const std::function<void(bool, const QueryAvailableServersResult&)>& queryAvailableServersCb) = 0;

   virtual int setServerTtl(const SetServerTtlArgs& ttlReq, const std::function<void(bool, const SetServerTtlResult&)>& setServerTtlCb) = 0;

   virtual int queryServerTtl(const QueryServerTtlArgs& query, const std::function<void(bool, const QueryServerTtlResult&)>& queryResultCb) = 0;

   virtual int addUserSession(const AddUserSessionArgs& addUserSessionArgs, const std::function<void(bool, const AddUserSessionResult&)>& addUserSessionCb) = 0;

   virtual int queryUserSessions(const QueryUserSessionsArgs& queryArgs, const std::function<void(bool, const QueryUserSessionsResult&)>& queryUserSessionsCb) = 0;
   
   virtual int queryServerUsers(const QueryServerUsersArgs& queryArgs, const std::function<void(bool, const QueryServerUsersResult&)>& queryServerUsersCb) = 0;

   virtual int flushAll() = 0;
   
   virtual int setHandler(RedisConnectionHandler* handler) = 0;

};

cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessConfig& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult::ServerInfo& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersArgs& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersResult& args);
cpc::string get_debug_string(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args);
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessConfig& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddServerResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::RemoveServerResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult::ServerInfo& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryAvailableServersResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::SetServerTtlResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerTtlResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::AddUserSessionResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryUserSessionsResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersArgs& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisAccessBase::QueryServerUsersResult& args);
std::ostream& operator<<(std::ostream& os, const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args);
   
}

}

#endif // CPCAPI2_ORCH_SERVER_REDIS_ACCESS_IF
