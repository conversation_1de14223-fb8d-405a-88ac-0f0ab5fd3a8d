Servers
------------------
server:2000 => [hostname=xmppagent1.counterpath.net]
server:2001 => [hostname=xmppagent2.counterpath.net]




Regions
--------------------
NorthAmerica => 1
Europe => 2
Asia => 3




AvailableServers
-------------------
xmppagent:1 => [2000, 2001]
xmppagent:2 => [2002]
xmppaccount:1 => [2000, 2001]
xmppaccount:2 => [2002]





Users
-------------------
user:1000 => [username=jgeras]





Sessions - std::map<int, std::set<int> >
-------------------
sessions:1000 => [2000]


