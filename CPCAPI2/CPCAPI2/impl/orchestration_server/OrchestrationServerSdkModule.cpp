#include "brand_branded.h"

#include <interface/experimental/orchestration_server/OrchestrationServer.h>

#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
#include "OrchestrationServerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace OrchestrationServer
{
   OrchestrationServer* OrchestrationServer::getInterface(Phone* cpcPhone)
   {
#if (CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)
      PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
      return _GetInterface<OrchestrationServerInterface>(phone, "OrchestrationServer");
#else
      return NULL;
#endif
   }

}
}
