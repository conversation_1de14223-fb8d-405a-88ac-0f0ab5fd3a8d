#pragma once

#if !defined(CPCAPI2_ORCHESTRATION_SERVER_INTERFACE_H)
#define CPCAPI2_ORCHESTRATION_SERVER_INTERFACE_H

#include "cpcapi2defs.h"
#include "cloudconnector/ServiceDesc.h"
#include "orchestration_server/OrchestrationServer.h"
#include "orchestration_server/OrchestrationServerHandler.h"
#include "orchestration_server/OrchestrationServerRedisAccessIf.h"
#include "phone/Cpcapi2EventSource.h"

#include "../phone/PhoneModule.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "jsonapi/JsonApiServerModule.h"
#include "jsonapi/JsonApiServerSendTransportInternal.h"

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <map>
#include <thread>

namespace CPCAPI2
{

class PhoneInterface;

namespace OrchestrationServer
{

class OrchestrationServerModule;
class RedisAccessBase;
typedef unsigned int OrchestrationServerHandle;
class OrchestrationServerSyncHandler {};

class OrchestrationServerInterface : public OrchestrationServer,
                                     public RedisConnectionHandler,
                                     public resip::DeadlineTimerHandler,
                                     public PhoneModule,
                                     public CPCAPI2::JsonApi::JsonApiServerModule,
                                     public CPCAPI2::EventSource<OrchestrationServerHandle, OrchestrationServerHandler, OrchestrationServerSyncHandler>
{

public:

   OrchestrationServerInterface(Phone* phone);
   virtual ~OrchestrationServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // OrchestrationServer
   virtual int start(const OrchestrationServerConfig& serverConfig = OrchestrationServerConfig()) OVERRIDE;
   virtual int shutdown() OVERRIDE;
   virtual int setHandler(OrchestrationServerHandler* handler) OVERRIDE;
   virtual int setServerInfo(const ServerInfo& serverInfo) OVERRIDE;
   virtual int setServerTtl(const cpc::string& uri, uint64_t ttlMs) OVERRIDE;
   virtual int removeServer(const cpc::string& service, const cpc::string& region, const cpc::string& uri) OVERRIDE;
   virtual int queryServers(const cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services) OVERRIDE;
   virtual int queryServerTtl(const cpc::string& uri) OVERRIDE;
   virtual int queryServerUsers(const cpc::string& uri) OVERRIDE;
   virtual int flushAll() OVERRIDE;
   
   // RedisConnectionHandler
   virtual int onRedisConnectionStatusChanged(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args) OVERRIDE;
   
   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   int startImpl(const OrchestrationServerConfig& serverConfig);
   int shutdownImpl();
   int setServerInfoImpl(const ServerInfo& serverInfo);
   int setServerInfoImpl(const ServerInfo& serverInfo, SetServerInfoResult& ssiResult);
   int setServerTtlImpl(const cpc::string& uri, uint64_t ttlMs);
   int removeServerImpl(const cpc::string& service, const cpc::string& region, const cpc::string& uri);
   int queryServersImpl(const cpc::vector<CPCAPI2::CloudConnector::ServiceDesc>& services);
   int queryServerTtlImpl(const cpc::string& uri);
   int queryServerUsersImpl(const cpc::string& uri);
   int flushAllImpl();
   int onRedisConnectionStatusChangedImpl(const CPCAPI2::OrchestrationServer::RedisConnectionStatusEvent& args);
   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);
   int handleRequestService(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetServerInfo(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);
   int handleSetServerTtl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const rapidjson::Value& functionObjectVal);

private:

   bool mShutdown;
   PhoneInterface* mPhone;
   OrchestrationServerConfig mConfig;
   std::unique_ptr<RedisAccessBase> mRedis;
   resip::DeadlineTimer<resip::MultiReactor> mRedisConnectionTimer;
   unsigned int mRedisConnectionTimeoutMsecs;

   typedef std::map<std::string, std::function<int(CPCAPI2::JsonApi::JsonApiRequestInfo, const rapidjson::Value&)> > FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransportInternal* mTransport;

};

}

}

#endif // CPCAPI2_ORCHESTRATION_SERVER_INTERFACE_H
