#include "TeradiciLoggerImpl.h"

#if (CPCAPI2_BRAND_TERADICI_LOGGER_MODULE==1)


#include <sstream>
#include <assert.h>

#include "teradici/tera_event_log.h"
#include "teradici/tera_event.h"
#include "teradici/tera_errors.h"
#include <rutil/DnsUtil.hxx>
#include "impl/util/IpHelpers.h"

#include "TeradiciStdErrorStreamRedirect.h"

namespace CPCAPI2
{
   // PlatformLog.cpp
   extern bool sPlatformLogEnabled;
}


using namespace CPCAPI2;
using namespace CPCAPI2::Teradici;


static bool sTeradiciLoggerImplAlive = false;


#ifdef __THREADX
// instantiating this will redirect std:cerr to Teradici logger
static TeradiciStdErrorStreamRedirect sTeradiciStdErrorStreamRedirect;
#endif


TeradiciLoggerImpl::TeradiciLoggerImpl(Phone* phone) : mPhone(phone), mHandler(NULL)
{
   if (sTeradiciLoggerImplAlive)
   {
      assert(0); // only one logger impl is expected to be alive
   }

   tera_mgmt_uc_util_init(teradiciMgmtUcUtilCallback, this);

   sTeradiciLoggerImplAlive = true;
}

TeradiciLoggerImpl::~TeradiciLoggerImpl()
{
   sTeradiciLoggerImplAlive = false;

   mPhone->setLoggingEnabled(NULL, false);
}

void TeradiciLoggerImpl::Release()
{
   delete this;
}

std::string CPCAPI2LogLevelToStr(LogLevel logLevel)
{
   switch (logLevel)
   {
      case LogLevel_Error:
         return "ERR";
      case LogLevel_Warning:
         return "WRN";
      default:
         return "";
   }
}

bool TeradiciLoggerImpl::operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
												int line, const char *message, const char *messageWithHeaders)
{
   (void)appName;
   (void)file;
   (void)line;
   (void)messageWithHeaders;

   const int maxCharsPerLine = TERA_EVENT_ENTRY_STR_STRING_LEN - 1; // TERA_EVENT_ENTRY_STR_STRING_LEN also accounts for null terminator
   int totalMessageLength = strlen(message);
   std::string messageToSplit(message);

   for (int charIndex = 0; charIndex < totalMessageLength; )
   {
      std::string prefix;
      if (charIndex == 0)
      {
         std::string logLevel = CPCAPI2LogLevelToStr(level);
         if (logLevel.length() > 0)
         {
            prefix.append(logLevel);
            prefix.append("-");
         }

         prefix.append(subsystem);
      }
      else
      {
         prefix = "*";
      }

      int maxCharsForMessage = maxCharsPerLine - prefix.length();    
      std::string l = messageToSplit.substr(charIndex, maxCharsForMessage);
      charIndex += l.length();

#ifdef __THREADX
      mTERA_EVENT_LOG_MESSAGE(TERA_EVENT_CAT_MGMT_UC,
         TERA_EVENT_LEVEL_DEBUG,
         TERA_SUCCESS,
         "%s%s",
         prefix.c_str(), l.c_str());
#endif
   }

   return true;
}


int TeradiciLoggerImpl::startLogging(bool verbose)
{
   // TODO: post to other thread?
   
   sPlatformLogEnabled = verbose;

   int ret = mPhone->setLoggingEnabled(this, true);

   if (zeroClientFirmwareLoggingEnabled())
   {
      return kSuccess;
   }

   // turn on both CP SDK logging, and Zero Client enhanced logging

   uTERA_MGMT_UC_UTIL_CMD_DATA data;
   data.log_level_data.log_level = TERA_MGMT_UC_UTIL_LOG_LEVEL_ENHANCED;

   tera_mgmt_uc_util_command_send(TERA_MGMT_UC_UTIL_CMD_LOG_LEVEL_SET, &data);

   return ret;
}

int TeradiciLoggerImpl::stopLogging()
{
   // TODO: post to other thread?

   sPlatformLogEnabled = false;

   int ret = mPhone->setLoggingEnabled(this, false);

   if (!zeroClientFirmwareLoggingEnabled())
   {
      return kSuccess;
   }

   uTERA_MGMT_UC_UTIL_CMD_DATA data;
   data.log_level_data.log_level = TERA_MGMT_UC_UTIL_LOG_LEVEL_DEFAULT;

   tera_mgmt_uc_util_command_send(TERA_MGMT_UC_UTIL_CMD_LOG_LEVEL_SET, &data);

   return ret;
}

int TeradiciLoggerImpl::requestZeroClientNetworkInfo()
{
   sendNetworkInfoLogToBria();

   return kSuccess;
}


bool TeradiciLoggerImpl::zeroClientFirmwareLoggingEnabled()
{
   bool enabled = false;

   uTERA_MGMT_UC_UTIL_CMD_DATA data;

   eTERA_STATUS cmdStatus = tera_mgmt_uc_util_command_send(TERA_MGMT_UC_UTIL_CMD_LOG_LEVEL_GET, &data);

   if (cmdStatus == TERA_SUCCESS)
   {
      if (data.log_level_data.log_level == TERA_MGMT_UC_UTIL_LOG_LEVEL_ENHANCED)
      {
         enabled = true;
      }
      else if (data.log_level_data.log_level == TERA_MGMT_UC_UTIL_LOG_LEVEL_DEFAULT)
      {
         enabled = false;
      }
   }

   return enabled;
}

int TeradiciLoggerImpl::setHandler(TeradiciLoggerHandler* handler)
{
   mHandler = handler;

   sendCurrentZeroClientLoggingStateToBria();   

   return kSuccess;
}

void TeradiciLoggerImpl::sendCurrentZeroClientLoggingStateToBria()
{
   if (mHandler)
   {
      uTERA_MGMT_UC_UTIL_CMD_DATA data;

      if (zeroClientFirmwareLoggingEnabled())
      {
         mHandler->onZeroClientLogLevelChanged(ZeroClientLogLevel_Enhanced);
      }
      else
      {
         mHandler->onZeroClientLogLevelChanged(ZeroClientLogLevel_Default);
      }
   }
}

void TeradiciLoggerImpl::sendNetworkInfoLogToBria()
{
   if (mHandler)
   {
      std::stringstream ss;

      std::string ipAddr(IpHelpers::getPreferredLocalIpAddress().c_str());
      std::string fqdn(resip::DnsUtil::getLocalHostName().c_str());

      ss << "Message from Zero Client: ";
      ss << "IP Address: " << ipAddr << " ";
      ss << "FQDN: " << fqdn;

      cpc::string msg(ss.str().c_str());

      mHandler->onLogMessageForAppLog(msg);
   }
}


// fTERA_MGMT_UC_UTIL_CBACK

void TeradiciLoggerImpl::teradiciMgmtUcUtilCallback(void                                    *user,
                                                    eTERA_MGMT_UC_UTIL_CBACK_EVENT          event,
                                                    uTERA_MGMT_UC_UTIL_CBACK_EVENT_DATA     *data)
{   
   if (!sTeradiciLoggerImplAlive)
   {
      return;
   }

   TeradiciLoggerImpl* self = reinterpret_cast<TeradiciLoggerImpl*>(user);

   if (event == TERA_MGMT_UC_UTIL_CBACK_EVENT_LOG_LEVEL_CHANGE)
   {
      eTERA_MGMT_UC_UTIL_LOG_LEVEL newLevel = data->log_level_data.log_level;
      if (newLevel == TERA_MGMT_UC_UTIL_LOG_LEVEL_ENHANCED)
      {
         if (self && self->mHandler)
         {
            self->mHandler->onZeroClientLogLevelChanged(ZeroClientLogLevel_Enhanced);
         }
      }
      else if (newLevel == TERA_MGMT_UC_UTIL_LOG_LEVEL_DEFAULT)
      {
         if (self && self->mHandler)
         {
            self->mHandler->onZeroClientLogLevelChanged(ZeroClientLogLevel_Default);
         }
      }
   }
}


#endif // CPCAPI2_BRAND_TERADICI_LOGGER_MODULE