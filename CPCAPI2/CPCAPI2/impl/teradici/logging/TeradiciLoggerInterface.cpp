#include "brand_branded.h"

#include "teradici/logging/TeradiciLoggerInterface.h"
#include "TeradiciLoggerImpl.h"
#include "impl/phone/PhoneInterface.h"

using namespace CPCAPI2;
using namespace CPCAPI2::Teradici;

TeradiciLoggerInterface* TeradiciLoggerInterface::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_TERADICI_LOGGER_MODULE==1)

	PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
	TeradiciLoggerImpl* instance = dynamic_cast<TeradiciLoggerImpl*>(pi->getInterfaceByName("TeradiciLoggerModule"));
	if (instance == NULL)
	{
		instance = new TeradiciLoggerImpl(cpcPhone);
		pi->registerInterface("TeradiciLoggerModule", instance);
	}
  return instance;
#else
  return NULL;
#endif
}
