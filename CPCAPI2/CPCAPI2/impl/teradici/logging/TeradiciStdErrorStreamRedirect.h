#ifndef TERADICI_STD_ERROR_STREAM_REDIRECT
#define TERADICI_STD_ERROR_STREAM_REDIRECT

#ifdef __THREADX

#include <iostream>


class TeradiciStreamBufLogger;
static TeradiciStreamBufLogger* sTeardiciStreamBufLogger = NULL;


class TeradiciStreamBufLogger : public std::streambuf
{
private:
   std::streamsize xsputn(const char* s, std::streamsize n)
   {
      mTERA_EVENT_LOG_MESSAGE(TERA_EVENT_CAT_MGMT_UC,
         TERA_EVENT_LEVEL_ERROR,
         TERA_SUCCESS,
         "%s",
         s);
      return n;
   }
};

class TeradiciStdErrorStreamRedirect
{
public:
   TeradiciStdErrorStreamRedirect()
   {
      sTeardiciStreamBufLogger = new TeradiciStreamBufLogger();
      std::cerr.rdbuf(sTeardiciStreamBufLogger);
   }

   virtual ~TeradiciStdErrorStreamRedirect()
   {
      delete sTeardiciStreamBufLogger;
   }
};

#endif // __THREADX

#endif // TERADICI_STD_ERROR_STREAM_REDIRECT
