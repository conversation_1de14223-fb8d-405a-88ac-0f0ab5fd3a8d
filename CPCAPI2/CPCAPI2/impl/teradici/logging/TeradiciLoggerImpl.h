#pragma once

#if !defined(CPCAPI2_TERADICI_LOGGER_IMPL_H)
#define CPCAPI2_TERADICI_LOGGER_IMPL_H

#include "brand_branded.h"

#if (CPCAPI2_BRAND_TERADICI_LOGGER_MODULE==1)

#include "../../interface/experimental/teradici/logging/TeradiciLoggerInterface.h"
#include "../../interface/experimental/teradici/logging/TeradiciLoggerHandler.h"
#include "impl/phone/PhoneModule.h"

#include "teradici/tera_errors.h"
#include "teradici/tera_event.h"
#include "teradici/tera_mgmt_uc_util.h"


namespace CPCAPI2
{
namespace Teradici
{

class TeradiciLoggerImpl : public TeradiciLoggerInterface, 
                           public PhoneModule,
                           public PhoneLogger
{
public:
   TeradiciLoggerImpl(Phone*);
   virtual ~TeradiciLoggerImpl();

   // PhoneModule
   void Release();

   // PhoneLogger
	virtual bool operator()(CPCAPI2::LogLevel level, const char *subsystem, const char *appName, const char *file,
													int line, const char *message, const char *messageWithHeaders);

   // TeradiciLoggerInterface

   virtual int startLogging(bool verbose);
   virtual int stopLogging();
   virtual int requestZeroClientNetworkInfo();
   virtual int setHandler(TeradiciLoggerHandler*);

private:
   static void teradiciMgmtUcUtilCallback(void *user,
                                          eTERA_MGMT_UC_UTIL_CBACK_EVENT          event,
                                          uTERA_MGMT_UC_UTIL_CBACK_EVENT_DATA     *data);

   bool zeroClientFirmwareLoggingEnabled();
   void sendCurrentZeroClientLoggingStateToBria();
   void sendNetworkInfoLogToBria();

private:
   Phone* mPhone;
   TeradiciLoggerHandler* mHandler;
};

}
}

#endif // CPCAPI2_BRAND_TERADICI_LOGGER_MODULE


#endif // CPCAPI2_TERADICI_LOGGER_IMPL_H
