#pragma once

#if !defined(CPCAPI2_TERADICI_CALLSTATE_INTERFACE_H)
#define CPCAPI2_TERADICI_CALLSTATE_INTERFACE_H

#include "brand_branded.h"

#if (CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE == 1)

#include "call/SipConversationHandler.h"
#include "cpcapi2defs.h"
#include "../../impl/phone/PhoneInterface.h"
#include "call/SipConversationTypes.h"
#include "../../interface/experimental/teradici/callstate/TeradiciCallstateInterface.h"

namespace CPCAPI2
{
namespace Teradici
{

class TeradiciCallstateImpl : public TeradiciCallstateInterface, public PhoneModule, public CPCAPI2::SipConversation::SipConversationHandler
{
public:
   TeradiciCallstateImpl(Phone*);
   virtual ~TeradiciCallstateImpl();

private:   

   // PhoneModule
   void Release();

   // SipConversationHandler
   int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);

   int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);

   int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args) { return 0; }
   int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args) { return 0; }
   int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args) { return 0; }
   int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args) { return 0; }
   int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args) { return 0; }
   int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args) { return 0; }
   int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args) { return 0; }

   int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args) { return 0; }
   int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args) { return 0; }

   int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args) { return 0; }
   int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args) { return 0; }
   int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args) { return 0; }

   int onError(CPCAPI2::SipConversation::SipConversationHandle subscription, const CPCAPI2::SipConversation::ErrorEvent& args) { return 0; }

private:

   void setTeraFirmwareCallState(bool callActive);

   Phone* mPhone;
   int mActiveCallCount;
};

}
}

#endif // CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE


#endif // CPCAPI2_TERADICI_CALLSTATE_MODULE_H