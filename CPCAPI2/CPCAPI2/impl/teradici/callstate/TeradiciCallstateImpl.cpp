
#include "brand_branded.h"
#if (CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE == 1)

#include "TeradiciCallStateImpl.h"
#include "interface/public/call/SipConversationManager.h"
#include "impl/call/SipAVConversationManagerInterface.h"

#ifdef __THREADX
#include "teradici/tera_rtos.h"
#include "teradici/tera_errors.h"
#include "teradici/tera_mgmt_uc_audio_device.h"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::Teradici;


TeradiciCallstateImpl::TeradiciCallstateImpl(Phone* phone)
   : mActiveCallCount(0)
{
   setTeraFirmwareCallState(false);

   mPhone = dynamic_cast<CPCAPI2::PhoneInterface*>(phone);

   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(phone);
   SipConversation::SipAVConversationManagerInterface* convManager = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(pi->getInterfaceByName("SipAVConversationManagerInterface"));
   if (convManager)
   {
      convManager->addSdkObserver(this);
   }   
}

TeradiciCallstateImpl::~TeradiciCallstateImpl()
{
   // assume that if we destruct, SDK is shutting down
   setTeraFirmwareCallState(false);
}

void TeradiciCallstateImpl::Release()
{
   delete this;
}

int TeradiciCallstateImpl::onNewConversation(SipConversation::SipConversationHandle conversation, const SipConversation::NewConversationEvent& args)
{
   ++mActiveCallCount;

   if (mActiveCallCount == 1)
   {
      setTeraFirmwareCallState(true);
   }

   return 0;
}

int TeradiciCallstateImpl::onConversationEnded(SipConversation::SipConversationHandle conversation, const SipConversation::ConversationEndedEvent& args)
{
   --mActiveCallCount;

   if (mActiveCallCount == 0)
   {
      setTeraFirmwareCallState(false);
   }

   return 0;
}

void TeradiciCallstateImpl::setTeraFirmwareCallState(bool callActive)
{
#ifdef __THREADX
   if (callActive)
   {
      tera_mgmt_uc_audio_device_call_state_set(TERA_MGMT_UC_AUDIO_DEVICE_CALL_STATE_IN_CALL);
   }
   else
   {
      tera_mgmt_uc_audio_device_call_state_set(TERA_MGMT_UC_AUDIO_DEVICE_CALL_STATE_NOT_IN_CALL);
   }
#endif
}

#endif
