#include "brand_branded.h"

#if (CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE == 1)

#include "TeradiciCallstateImpl.h"
using namespace CPCAPI2;
using namespace CPCAPI2::Teradici;

TeradiciCallstateInterface* TeradiciCallstateInterface::getInterface(Phone* cpcPhone)
{
#ifdef CPCAPI2_BRAND_TERADICI_CALLSTATE_MODULE

   // TeradiciCallstateModule depends on SipAVConversationManagerInterface being setup
   SipConversation::SipConversationManager::getInterface(cpcPhone);
  
	PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
	TeradiciCallstateImpl* instance = dynamic_cast<TeradiciCallstateImpl*>(pi->getInterfaceByName("TeradiciCallstateModule"));
	if (instance == NULL)
	{
		instance = new TeradiciCallstateImpl(cpcPhone);
		pi->registerInterface("TeradiciCallstateModule", instance);
	}
  return instance;
#else
  return NULL;
#endif
}


#endif
