#pragma once

#if !defined(CPCAPI2_TERADICI_AUDIO_INTERFACE_H)
#define CPCAPI2_TERADICI_AUDIO_INTERFACE_H

#include "cpcapi2defs.h"
#include "teradici/media/TeradiciAudio.h"
#include "../../util/cpc_thread.h"
#include "../../util/DumFpCommand.h"
#include "impl/phone/PhoneModule.h"

#include <rutil/Fifo.hxx>
#include <resip/dum/DumCommand.hxx>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class MediaManagerInterface;
class TeradiciAudioImpl;

class TeradiciAudioInterface : public TeradiciAudio,
   public PhoneModule
{
public:
   TeradiciAudioInterface(MediaManagerInterface* mm);
   virtual ~TeradiciAudioInterface();

   virtual int initializeHid() OVERRIDE;

   virtual void Release() OVERRIDE;

   virtual int setHandler(TeradiciAudioHandler* handler) OVERRIDE;

   virtual int queryDeviceList() OVERRIDE;

   virtual int setCaptureDevice(unsigned int deviceId, TeradiciAudioDeviceRole role) OVERRIDE;
   virtual int setRenderDevice(unsigned int deviceId, TeradiciAudioDeviceRole role) OVERRIDE;

   virtual PlaySoundHandle playSound(TeradiciAudioDeviceRole role, const cpc::string& resourceUri, bool repeat = false) OVERRIDE;
   virtual int stopPlaySound(PlaySoundHandle sound) OVERRIDE;

   virtual int queryCodecList() OVERRIDE;
   virtual int setCodecEnabled(unsigned int codecId, bool enabled) OVERRIDE;
   virtual int setCodecPriority(unsigned int codecId, unsigned int priority) OVERRIDE;

   virtual int setMicMute(bool enabled) OVERRIDE;
   virtual int setSpeakerMute(bool enabled) OVERRIDE;
   virtual int setMicVolume(unsigned int level) OVERRIDE;
   virtual int setSpeakerVolume(unsigned int level) OVERRIDE;

   virtual int queryDeviceVolume() OVERRIDE;

   virtual int setEchoCancellationMode(TeradiciAudioDeviceRole role, TeradiciEchoCancellationMode mode) OVERRIDE;
   virtual int setNoiseSuppressionMode(TeradiciAudioDeviceRole role, TeradiciNoiseSuppressionMode mode) OVERRIDE;

   virtual int queryOnHook() OVERRIDE;
   virtual int setOnHook(bool onHook) OVERRIDE;
   virtual int setRinging(bool ringingOn) OVERRIDE;

   webrtc_recon::MediaStackImpl* media_stack() const;

private:
   void releaseImpl();

private:
   PlaySoundHandle mNextPlaySoundHandle;
   MediaManagerInterface* mMM;
   TeradiciAudioImpl* mImpl;
};
}
}
#endif // CPCAPI2_TERADICI_AUDIO_INTERFACE_H
