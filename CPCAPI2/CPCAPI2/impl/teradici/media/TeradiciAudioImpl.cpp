#include "brand_branded.h"

#if (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE==1)
#include "TeradiciAudioImpl.h"
#include "impl/teradici/media/TeradiciPlaySoundHelper.h"
#include "cpcapi2utils.h"
#include "../../util/DumFpCommand.h"

#include <MediaStackImpl.hxx>
#include <CodecFactoryImpl.hxx>

#include <functional>

#include <voe_base.h>
#include <voe_hardware.h>
#include <voe_audio_processing.h>
#include <voice_engine/voe_hardware_impl.h>
#include <modules/audio_device/threadx/audio_device_threadx.h>
#include <voe_volume_control.h>

using namespace webrtc_recon;
using namespace resip;

namespace CPCAPI2
{
namespace Media
{
TeradiciAudioImpl::TeradiciAudioImpl(webrtc_recon::MediaStackImpl* mediaStack, resip::Fifo<resip::ReadCallbackBase>* callbackFifo, const std::function<void(void)>& cbHook) :
   mMediaStack(mediaStack),
   mCallbackFifo(callbackFifo),
   mMediaHandler(NULL),
   mCbHook(cbHook),
   mPlaySoundHelper(NULL)
{  
}

TeradiciAudioImpl::~TeradiciAudioImpl()
{
   delete mPlaySoundHelper;

   if (webrtc::AudioDeviceThreadx* txa = threadxAudioImpl())
   {
      threadxAudioImpl()->SetHidHandler(NULL);
      threadxAudioImpl()->SetDeviceOnHook(true);
      threadxAudioImpl()->SetDeviceRinging(false);
      threadxAudioImpl()->SetSpeakerMute(false);
      threadxAudioImpl()->SetMicrophoneMute(false);
   }
}

int TeradiciAudioImpl::initializeHid()
{
   threadxAudioImpl()->SetDeviceOnHook(true);
   threadxAudioImpl()->SetDeviceRinging(false);
   threadxAudioImpl()->SetHidHandler(this);
   threadxAudioImpl()->SetSpeakerMute(false);
   threadxAudioImpl()->SetMicrophoneMute(false);
   return kSuccess;
}

int TeradiciAudioImpl::setHandler(TeradiciAudioHandler* handler)
{
   mMediaHandler = handler;
   return kSuccess;
}

int TeradiciAudioImpl::queryDeviceList()
{
   int numPlayoutDevs = 0;
   int numRecordingDevs = 0;

   if (mMediaStack->hardware()->GetNumOfPlayoutDevices(numPlayoutDevs) == 0 &&
       mMediaStack->hardware()->GetNumOfRecordingDevices(numRecordingDevs) == 0)
   {
      mAudioDeviceList.clear();
      for (int i=0; i<numPlayoutDevs; i++)
      {
         char devNameUtf8[128];
         char devGuidUtf8[128];
         if (mMediaStack->hardware()->GetPlayoutDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
         {
            // NOTE: The device GUID is only available on Windows (only Vista and higher)
            TeradiciAudioDeviceInfo devInfo;
            devInfo.deviceType = TeradiciMediaDeviceType_Render;
            devInfo.friendlyName = devNameUtf8;
            cpc::string devGuidWstr = devGuidUtf8;
            devInfo.id = (devGuidWstr.size() > 0 ? cpc::hash(devGuidWstr) : cpc::hash(devInfo.friendlyName));
            devInfo.role = TeradiciAudioDeviceRole_None; // TODO: use GetPlayoutDeviceName(-2, ...) and GetPlayoutDeviceName(-1, ...) to make a best guess?
            
            // only 1 device at a time is exposed to us via Teradici's audio API
            int32_t queryDeviceImplError = 0;
            queryDeviceImplError |= threadxAudioImpl()->DeviceInternalVolumeAvailable(devInfo.internalVolumeSupported);
            queryDeviceImplError |= threadxAudioImpl()->DeviceInternalRingAvailable(devInfo.internalRingSupported);
            devInfo.internalMuteSupported = false;

            mAudioDeviceList.push_back(devInfo);
         }
      }

      for (int i=0; i<numRecordingDevs; i++)
      {
         char devNameUtf8[128];
         char devGuidUtf8[128];
         if (mMediaStack->hardware()->GetRecordingDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
         {
            // NOTE: The device GUID is only available on Windows (only Vista and higher)
            TeradiciAudioDeviceInfo devInfo;
            devInfo.deviceType = TeradiciMediaDeviceType_Capture;
            devInfo.friendlyName = devNameUtf8;
            cpc::string devGuidWstr = devGuidUtf8;
            devInfo.id = (devGuidWstr.size() > 0 ? cpc::hash(devGuidWstr) : cpc::hash(devInfo.friendlyName));
            devInfo.role = TeradiciAudioDeviceRole_None; // TODO: use GetPlayoutDeviceName(-2, ...) and GetPlayoutDeviceName(-1, ...) to make a best guess?

            // only 1 device at a time is exposed to us via Teradici's audio API
            int32_t queryDeviceImplError = 0;
            devInfo.internalVolumeSupported = false;
            devInfo.internalRingSupported = false;
            queryDeviceImplError |= threadxAudioImpl()->DeviceInternalMuteAvailable(devInfo.internalMuteSupported);

            mAudioDeviceList.push_back(devInfo);
         }
      }

      if (mMediaHandler != NULL)
      {
         TeradiciAudioDeviceListUpdatedEvent args;
         args.deviceInfo = mAudioDeviceList;
         ReadCallbackBase* callback = makeFpCommand(TeradiciAudioHandler::onAudioDeviceListUpdated, mMediaHandler, args);
         postCallback(callback);
      }
   }
   return kSuccess;
}

int TeradiciAudioImpl::playSound(PlaySoundHandle h, TeradiciAudioDeviceRole device, const cpc::string& resourceUri, bool repeat)
{
   if (mPlaySoundHelper == NULL)
   {
      mPlaySoundHelper = new TeradiciPlaySoundHelper(this);
   }

   mPlaySoundHelper->playSound(h, device, resourceUri, repeat);

   return kSuccess;
}

int TeradiciAudioImpl::stopPlaySound(PlaySoundHandle h)
{
   return kSuccess;
}

int TeradiciAudioImpl::queryCodecList()
{
   if (mMediaStack->isInitialized())
   {
      mAudioCodecList.clear();
      std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
      CodecFactoryImpl::Codecs audioCodecs = codecFactory->audioCodecs();
      CodecFactoryImpl::Codecs::const_iterator itAudioCodecs = audioCodecs.begin();
      for (; itAudioCodecs != audioCodecs.end(); ++itAudioCodecs)
      {
         std::shared_ptr<CpsiCodec> c = *itAudioCodecs;

         if (resip::isEqualNoCase(c->settings().payload_name, "telephone-event"))
         {
            continue;
         }

         if (c->settings().licenses_total > 0)
         {
            TeradiciAudioCodecInfo info;
            info.id = cpc::hash(c->settings().payload_name.c_str());
            info.enabled = c->settings().enabled;
            info.priority = c->settings().priority;
            if (c->settings().display_name.empty())
            {
               info.codecName = c->settings().payload_name.c_str();
            }
            else
            {
               info.codecName = c->settings().display_name.c_str();
            }
            info.samplingRate = c->settings().webrtcCodecInfo.audio.rate;
            info.minBandwidth = c->settings().min_bandwidth;
            info.maxBandwidth = c->settings().max_bandwidth;
            mAudioCodecList.push_back(info);
         }
      }

      if (mMediaHandler != NULL)
      {
         TeradiciAudioCodecListUpdatedEvent args;
         args.codecInfo = mAudioCodecList;
         ReadCallbackBase* callback = makeFpCommand(TeradiciAudioHandler::onAudioCodecListUpdated, mMediaHandler, args);
         postCallback(callback);
      }
   }
   return kSuccess;
}

int TeradiciAudioImpl::setCaptureDevice(unsigned int deviceId, TeradiciAudioDeviceRole role)
{
   for (std::vector<TeradiciAudioDeviceInfo>::const_iterator di =  mAudioDeviceList.begin(); di != mAudioDeviceList.end(); ++di)
   {
      if (di->deviceType == TeradiciMediaDeviceType_Capture && di->id == deviceId)
      {
         int numRecordingDevs = 0;
         mMediaStack->hardware()->GetNumOfRecordingDevices(numRecordingDevs);
         for (int i=0; i<numRecordingDevs; i++)
         {
            char devNameUtf8[128];
            char devGuidUtf8[128];
            if (mMediaStack->hardware()->GetRecordingDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
            {
               // NOTE: The device GUID is only available on Windows (only Vista and higher)
               TeradiciAudioDeviceInfo devInfo;
               devInfo.friendlyName = devNameUtf8;
               cpc::string devGuidWstr = devGuidUtf8;
               devInfo.id = (devGuidWstr.size() > 0 ? cpc::hash(devGuidWstr) : cpc::hash(devInfo.friendlyName));
               if (devInfo.id == deviceId)
               {
                  mMediaStack->hardware()->SetRecordingDevice(i);
                  return kSuccess;
               }
            }
         }
      }
   }
   return kSuccess;
}

int TeradiciAudioImpl::setRenderDevice(unsigned int deviceId, TeradiciAudioDeviceRole role)
{
   for (std::vector<TeradiciAudioDeviceInfo>::const_iterator di =  mAudioDeviceList.begin(); di != mAudioDeviceList.end(); ++di)
   {
      if (di->deviceType == TeradiciMediaDeviceType_Render && di->id == deviceId)
      {
         int numPlayoutDevs = 0;
         mMediaStack->hardware()->GetNumOfPlayoutDevices(numPlayoutDevs);
         for (int i=0; i<numPlayoutDevs; i++)
         {
            char devNameUtf8[128];
            char devGuidUtf8[128];
            if (mMediaStack->hardware()->GetPlayoutDeviceName(i, devNameUtf8, devGuidUtf8) == 0)
            {
               // NOTE: The device GUID is only available on Windows (only Vista and higher)
               TeradiciAudioDeviceInfo devInfo;
               devInfo.friendlyName = devNameUtf8;
               cpc::string devGuidWstr = devGuidUtf8;
               devInfo.id = (devGuidWstr.size() > 0 ? cpc::hash(devGuidWstr) : cpc::hash(devInfo.friendlyName));
               if (devInfo.id == deviceId)
               {
                  mMediaStack->hardware()->SetPlayoutDevice(i);
                  return kSuccess;
               }
            }
         }
      }
   }
   return kSuccess;
}

int TeradiciAudioImpl::setCodecEnabled(unsigned int id, bool enabled)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (std::vector<TeradiciAudioCodecInfo>::iterator ci =  mAudioCodecList.begin(); ci != mAudioCodecList.end(); ++ci)
   {
      if (ci->id == id)
      {
         ci->enabled = enabled;
         std::shared_ptr<CpsiCodec> c = codecFactory->getAudioCodecByDisplayName((ci->codecName).c_str());
         if (c)
         {
            c->settings().enabled = enabled;
         }
      }
   }
   return kSuccess;
}

int TeradiciAudioImpl::setCodecPriority(unsigned int id, unsigned int priority)
{
   std::shared_ptr<CodecFactoryImpl> codecFactory = std::dynamic_pointer_cast<CodecFactoryImpl>(mMediaStack->codecFactory());
   for (std::vector<TeradiciAudioCodecInfo>::const_iterator ci =  mAudioCodecList.begin(); ci != mAudioCodecList.end(); ++ci)
   {
      if (ci->id == id)
      {
         std::shared_ptr<CpsiCodec> c = codecFactory->getAudioCodecByDisplayName((ci->codecName).c_str());
         if (c)
         {
            c->settings().priority = priority;
         }
         break;
      }
   }
   return kSuccess;
}

void TeradiciAudioImpl::OnLocallyPlayingFileFinished(int channel)
{
   // TODO
   return;
}

int TeradiciAudioImpl::cleanupImpl(webrtc_recon::MediaStackImpl* ms, int channel)
{
   if (channel >= 0)
   {
      ms->voe_base()->StopPlayout(channel);
      ms->voe_base()->DeleteChannel(channel);
   }
   return kSuccess;
}

void TeradiciAudioImpl::postCallback(ReadCallbackBase* fp)
{
   mCallbackFifo->add(fp);
   if (mCbHook) { mCbHook(); }
}

int TeradiciAudioImpl::setMicMute(bool enabled)
{
   mMediaStack->hardware()->SetMicStatus(!enabled);
   return kSuccess;
}

int TeradiciAudioImpl::setSpeakerMute(bool enabled)
{
   mMediaStack->hardware()->SetLoudspeakerStatus(!enabled);
   return kSuccess;
}

int TeradiciAudioImpl::setMicVolume(unsigned int level)
{
   unsigned int vol = (unsigned int)(((float)level / 100.0f) * 255.0f);
   if (mMediaStack->volume_control()->SetMicVolume(vol) != 0)
   {
      return kError;
   }
   return kSuccess;
}

int TeradiciAudioImpl::setSpeakerVolume(unsigned int level)
{
   unsigned int vol = (unsigned int)(((float)level / 100.0f) * 255.0f);
   if (mMediaStack->volume_control()->SetSpeakerVolume(vol) != 0)
   {
      return kError;
   }
   return kSuccess;
}

int TeradiciAudioImpl::queryDeviceVolume()
{
   if (mMediaStack->isInitialized())
   {
      bool enabled = false;
      TeradiciAudioDeviceVolumeEvent args;
      args.micMuted = false;
      args.micVolumeLevel = 50;
      args.speakerMuted = false;
      args.speakerVolumeLevel = 50;

      if (mMediaStack->hardware()->GetMicStatus(enabled) == 0)
      {
         args.micMuted = !enabled;
      }

      if (mMediaStack->hardware()->GetLoudspeakerStatus(enabled) == 0)
      {
         args.speakerMuted = !enabled;
      }

      unsigned int vol = 0;
      if (mMediaStack->volume_control()->GetSpeakerVolume(vol) == 0)
      {
         args.speakerVolumeLevel = (unsigned int)(((float)vol / 255.0f) * 100.0f);
      }

      if (mMediaStack->volume_control()->GetMicVolume(vol) == 0)
      {
         args.micVolumeLevel = (unsigned int)(((float)vol / 255.0f) * 100.0f);
      }

      ReadCallbackBase* callback = makeFpCommand(TeradiciAudioHandler::onAudioDeviceVolume, mMediaHandler, args);
      postCallback(callback);
      return kSuccess;
   }
   return kError;
}

int TeradiciAudioImpl::setEchoCancellationMode(TeradiciAudioDeviceRole role, TeradiciEchoCancellationMode mode)
{
   if (mode == TeradiciEchoCancellationMode_AggressiveEchoCancellation)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcConference);
   }
   else if (mode == TeradiciEchoCancellationMode_DefaultEchoCancellation)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcAec);
   }
   else if (mode == TeradiciEchoCancellationMode_LoudSpeakerphoneEchoSuppression)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcAecm);
      mMediaStack->voe_audio_processing()->SetAecmMode(webrtc::kAecmLoudSpeakerphone, true);
   }
   else if (mode == TeradiciEchoCancellationMode_SpeakerphoneEchoSuppression)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcAecm);
      mMediaStack->voe_audio_processing()->SetAecmMode(webrtc::kAecmSpeakerphone, true);
   }
   else if (mode == TeradiciEchoCancellationMode_LoudEarpieceEchoSuppression)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcAecm);
      mMediaStack->voe_audio_processing()->SetAecmMode(webrtc::kAecmLoudEarpiece, true);
   }
   else if (mode == TeradiciEchoCancellationMode_EarpieceEchoSuppression)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(true, webrtc::kEcAecm);
      mMediaStack->voe_audio_processing()->SetAecmMode(webrtc::kAecmEarpiece, true);
   }
   else if (mode == TeradiciEchoCancellationMode_None)
   {
      mMediaStack->voe_audio_processing()->SetEcStatus(false, webrtc::kEcAecm);
      mMediaStack->voe_audio_processing()->SetEcStatus(false, webrtc::kEcConference);
      mMediaStack->voe_audio_processing()->SetEcStatus(false, webrtc::kEcAec);
   }
   return kSuccess;
}

int TeradiciAudioImpl::setNoiseSuppressionMode(TeradiciAudioDeviceRole role, TeradiciNoiseSuppressionMode mode)
{
   if (mode == TeradiciNoiseSuppressionMode_VeryHigh)
   {
      mMediaStack->voe_audio_processing()->SetNsStatus(true, webrtc::kNsVeryHighSuppression);
   }
   else if (mode == TeradiciNoiseSuppressionMode_High)
   {
      mMediaStack->voe_audio_processing()->SetNsStatus(true, webrtc::kNsHighSuppression);
   }
   else if (mode == TeradiciNoiseSuppressionMode_Moderate)
   {
      mMediaStack->voe_audio_processing()->SetNsStatus(true, webrtc::kNsModerateSuppression);
   }
   else if (mode == TeradiciNoiseSuppressionMode_Low)
   {
      mMediaStack->voe_audio_processing()->SetNsStatus(true, webrtc::kNsLowSuppression);
   }
   else if (mode == TeradiciNoiseSuppressionMode_None)
   {
      mMediaStack->voe_audio_processing()->SetNsStatus(false);
   }
   return kSuccess;
}

webrtc::AudioDeviceThreadx* TeradiciAudioImpl::threadxAudioImpl()
{
   webrtc::AudioDeviceThreadx* threadxImpl = NULL;

   webrtc::VoEHardwareImpl* hardwareImpl = static_cast<webrtc::VoEHardwareImpl*>(mMediaStack->hardware());
   if (hardwareImpl)
   {
      webrtc::AudioDeviceGeneric* genericImpl = hardwareImpl->PlatformSpecificAudioImplementation();
      threadxImpl = static_cast<webrtc::AudioDeviceThreadx*>(genericImpl);
   }

   return threadxImpl;
}


int TeradiciAudioImpl::queryOnHook()
{
  bool onHook;
  if (threadxAudioImpl()->DeviceIsOnHook(onHook) == 0)
  {
    TeradiciAudioDeviceHookStateEvent args;
    args.onHook = onHook;

    ReadCallbackBase* callback = makeFpCommand(TeradiciAudioHandler::onAudioDeviceOnHookQueryResult, mMediaHandler, args);
    postCallback(callback);

    return kSuccess;
  }

  return kError;
}

int TeradiciAudioImpl::setOnHook(bool onHook)
{
  threadxAudioImpl()->SetDeviceOnHook(onHook);
}

int TeradiciAudioImpl::setRinging(bool ringingOn)
{
  threadxAudioImpl()->SetDeviceRinging(ringingOn);
}


void TeradiciAudioImpl::OnThreadxAudioDeviceKeyPress(webrtc::ThreadxAudioDeviceKeyPressType webrtcKey)
{
    TeradiciAudioDeviceKeyPressEvent args;
    switch (webrtcKey)
    {
    case webrtc::ThreadxAudioDeviceKeyPressType_OnHook:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_OnHook;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_OffHook:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_OffHook;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_MuteOn:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_OnMute;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_MuteOff:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_OffMute;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_VolumeUp:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_VolumeUp;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_VolumeDown:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_VolumeDown;
      break;
    case webrtc::ThreadxAudioDeviceKeyPressType_Flash:
      args.keyPressed = TeradiciAudioDeviceKeyPressType_Flash;
      break;
    default:      
    }

    ReadCallbackBase* callback = makeFpCommand(TeradiciAudioHandler::onAudioDeviceKeyPress, mMediaHandler, args);
    postCallback(callback);
}

void TeradiciAudioImpl::OnThreadxAudioDeviceConnected()
{
    ReadCallbackBase* callback = makeFpCommand(TeradiciAudioImpl::queryDeviceListForDeviceChange, this, 0);
    postCallback(callback);
}

void TeradiciAudioImpl::queryDeviceListForDeviceChange(int)
{
   queryDeviceList();
}

}
}
#endif // CPCAPI2_TERADICI_AUDIO_MODULE
