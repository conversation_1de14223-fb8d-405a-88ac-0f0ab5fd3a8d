#include "brand_branded.h"

#if (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE==1)
#include "cpcapi2utils.h"
#include "TeradiciAudioInterface.h"
#include "TeradiciAudioImpl.h"
#include "impl/media/MediaManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#include "media/MediaManager.h"
#include <MediaStackImpl.hxx>

#include <functional>

using namespace resip;

namespace CPCAPI2
{
namespace Media
{

TeradiciAudioInterface::TeradiciAudioInterface(MediaManagerInterface* mm)
	: mMM(mm),
     mImpl(new TeradiciAudioImpl(mm->media_stack(), mm->callbackFifo(), mm->cbHook())),
     mNextPlaySoundHandle(5000)
{
}

TeradiciAudioInterface::~TeradiciAudioInterface()
{
   delete mImpl;
}
   
void TeradiciAudioInterface::Release()
{
   mMM->post(resip::resip_bind(&TeradiciAudioInterface::releaseImpl, this));
}

void TeradiciAudioInterface::releaseImpl()
{
   delete this;
}

int TeradiciAudioInterface::initializeHid()
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::initializeHid, mImpl));
   return kSuccess;
}

int TeradiciAudioInterface::setHandler(TeradiciAudioHandler* handler)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setHandler, mImpl, handler));
   return kSuccess;
}

int TeradiciAudioInterface::queryDeviceList()
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::queryDeviceList, mImpl));
   return kSuccess;
}

int TeradiciAudioInterface::setCaptureDevice(unsigned int deviceId, TeradiciAudioDeviceRole role)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setCaptureDevice, mImpl, deviceId, role));
   return kSuccess;
}

int TeradiciAudioInterface::setRenderDevice(unsigned int deviceId, TeradiciAudioDeviceRole role)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setRenderDevice, mImpl, deviceId, role));
   return kSuccess;
}

PlaySoundHandle TeradiciAudioInterface::playSound(TeradiciAudioDeviceRole role, const cpc::string& resourceUri, bool repeat)
{
   PlaySoundHandle h = mNextPlaySoundHandle++;
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::playSound, mImpl, h, role, resourceUri, repeat));
   return h;
}

int TeradiciAudioInterface::stopPlaySound(PlaySoundHandle sound)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::stopPlaySound, mImpl, sound));
   return kSuccess;
}

int TeradiciAudioInterface::queryCodecList()
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::queryCodecList, mImpl));
   return kSuccess;
}

int TeradiciAudioInterface::setCodecEnabled(unsigned int codecId, bool enabled)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setCodecEnabled, mImpl, codecId, enabled));
   return kSuccess;
}

int TeradiciAudioInterface::setCodecPriority(unsigned int codecId, unsigned int priority)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setCodecPriority, mImpl, codecId, priority));
   return kSuccess;
}

int TeradiciAudioInterface::setMicMute(bool enabled)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setMicMute, mImpl, enabled));
   return kSuccess;
}

int TeradiciAudioInterface::setSpeakerMute(bool enabled)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setSpeakerMute, mImpl, enabled));
   return kSuccess;
}

int TeradiciAudioInterface::setMicVolume(unsigned int level)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setMicVolume, mImpl, level));
   return kSuccess;
}

int TeradiciAudioInterface::setSpeakerVolume(unsigned int level)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setSpeakerVolume, mImpl, level));
   return kSuccess;
}

int TeradiciAudioInterface::queryDeviceVolume()
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::queryDeviceVolume, mImpl));
   return kSuccess;
}

int TeradiciAudioInterface::setEchoCancellationMode(TeradiciAudioDeviceRole role, TeradiciEchoCancellationMode mode)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setEchoCancellationMode, mImpl, role, mode));
   return kSuccess;
}

int TeradiciAudioInterface::setNoiseSuppressionMode(TeradiciAudioDeviceRole role, TeradiciNoiseSuppressionMode mode)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setNoiseSuppressionMode, mImpl, role, mode));
   return kSuccess;
}

webrtc_recon::MediaStackImpl* TeradiciAudioInterface::media_stack() const 
{ 
   return mImpl->media_stack(); 
}

int TeradiciAudioInterface::queryOnHook()
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::queryOnHook, mImpl));
   return kSuccess;
}

int TeradiciAudioInterface::setOnHook(bool onHook)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setOnHook, mImpl, onHook));
   return kSuccess;
}

int TeradiciAudioInterface::setRinging(bool ringingOn)
{
   mMM->post(resip::resip_bind(&TeradiciAudioImpl::setRinging, mImpl, ringingOn));
   return kSuccess;
}


}
}

#endif // TERADICI_AUDIO_MODULE