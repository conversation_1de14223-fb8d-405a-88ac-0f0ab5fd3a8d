#pragma once

#if !defined(CPCAPI2_TERADICI_PLAY_SOUND_HELPER_H)
#define CPCAPI2_TERADICI_PLAY_SOUND_HELPER_H

#include "teradici/media/TeradiciAudioHandler.h"
#include "TeradiciAudioImpl.h"
#include "cpcapi2defs.h"

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace CPCAPI2
{
namespace Media
{
class AudioImpl;

class TeradiciPlaySoundHelper
{
public:
   TeradiciPlaySoundHelper(TeradiciAudioImpl* audio);
   virtual ~TeradiciPlaySoundHelper();

   void stop();

   int playSound(PlaySoundHandle h, TeradiciAudioDeviceRole device, const cpc::string& resourceUri, bool repeat);

   int channel() const { return mChannel; }
   PlaySoundHandle handle() const { return mHandle; }

private:
   int mChannel;
   PlaySoundHandle mHandle;
   TeradiciAudioImpl* mAudio;
};
}
}
#endif // CPCAPI2_PLAY_SOUND_HELPER_H