#include "brand_branded.h"

#if (CPCAPI2_BRAND_MEDIA_MODULE == 1) && (CPCAPI2_BRAND_TERADICI_AUDIO_MODULE==1)

#include "TeradiciPlaySoundHelper.h"

#include "impl/util/resource_to_stream.h"
#include "cpcapi2utils.h"
#include "impl/util/DumFpCommand.h"
#include "impl/util/dtmf_tone_helper.h"
#include "TeradiciAudioImpl.h"
#include <MediaStackImpl.hxx>

#include <voe_base.h>
#include <voe_dtmf.h>

#include "rutil/Data.hxx"

#ifndef CPCAPI2_NO_CXX11
#include <future>
#endif

namespace CPCAPI2
{
namespace Media
{
   TeradiciPlaySoundHelper::TeradiciPlaySoundHelper(TeradiciAudioImpl* audio) : mAudio(audio), mChannel(-1), mHandle(0xffffffff)
   {
      mChannel = mAudio->media_stack()->voe_base()->CreateChannel();
   }
   TeradiciPlaySoundHelper::~TeradiciPlaySoundHelper()
   {
   }

   void TeradiciPlaySoundHelper::stop()
   {
   }

   int TeradiciPlaySoundHelper::playSound(PlaySoundHandle h, TeradiciAudioDeviceRole device, const cpc::string& resourceUri, bool repeat)
   {
      mHandle = h;
      resip::ReadCallbackBase* failedCallback = NULL;

      if (mChannel < 0)
      {
         mAudio->postCallback(failedCallback);
         return -1;
      }
      else
      {
         if (mAudio->media_stack()->voe_base()->StartPlayout(mChannel) != 0)
         {
            mAudio->postCallback(failedCallback);
            return -1;
         }

         if (resourceUri.size() == 0)
         {
            mAudio->postCallback(failedCallback);
            return -1;
         }
         else
         {
            if (resourceUri.find("tone:") == 0)
            {
               resip::Uri toneUri(resourceUri.c_str());
               int toneIdInt = DtmfToneHelper::dtmfToneIdFromChar(toneUri.host().c_str()[0]);
               int durationMS = toneUri.exists(resip::p_duration) ? toneUri.param(resip::p_duration) : 200;
               mAudio->media_stack()->dtmf()->PlayDtmfTone(toneIdInt);
            }
         }
      }
      return 0;
   }
}
}

#endif
