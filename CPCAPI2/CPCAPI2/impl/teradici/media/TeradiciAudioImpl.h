#pragma once

#if !defined(CPCAPI2_TERADICI_AUDIO_IMPL_H)
#define CPCAPI2_TERADICI_AUDIO_IMPL_H

#include "cpcapi2defs.h"
#include "teradici/media/TeradiciAudioHandler.h"
#include <modules/audio_device/threadx/audio_device_hid_handler_threadx.h>
#include "../../util/DumFpCommand.h"

#include <rutil/Fifo.hxx>
#include <voe_file.h>
#include <map>

namespace webrtc_recon
{
class MediaStackImpl;
}

namespace webrtc
{
class AudioDeviceThreadx;
}

namespace CPCAPI2
{
namespace Media
{
class TeradiciPlaySoundHelper;

class TeradiciAudioImpl : public webrtc::AudioDeviceHidHandlerThreadx
{
public:
   TeradiciAudioImpl(webrtc_recon::MediaStackImpl* mediaStack, resip::Fifo<resip::ReadCallbackBase>* callbackFifo, const std::function<void(void)>& cbHook);
   virtual ~TeradiciAudioImpl();

   virtual void OnLocallyPlayingFileFinished(int channel);

   webrtc_recon::MediaStackImpl* media_stack() const { return mMediaStack; }

   int initializeHid();
   int queryDeviceList();
   int setCaptureDevice(unsigned int deviceId, TeradiciAudioDeviceRole role);
   int setRenderDevice(unsigned int deviceId, TeradiciAudioDeviceRole role);
   int playSound(PlaySoundHandle h, TeradiciAudioDeviceRole device, const cpc::string& resourceUri, bool repeat);
   int stopPlaySound(PlaySoundHandle h);
   int queryCodecList();
   int setCodecEnabled(unsigned int id, bool enabled);
   int setCodecPriority(unsigned int id, unsigned int priority);
   int setMicMute(bool enabled);
   int setSpeakerMute(bool enabled);
   int setMicVolume(unsigned int level);
   int setSpeakerVolume(unsigned int level);
   int queryDeviceVolume();
   int setEchoCancellationMode(TeradiciAudioDeviceRole role, TeradiciEchoCancellationMode mode);
   int setNoiseSuppressionMode(TeradiciAudioDeviceRole role, TeradiciNoiseSuppressionMode mode);

   virtual int queryOnHook();
   virtual int setOnHook(bool onHook);
   virtual int setRinging(bool ringingOn);

   int setHandler(TeradiciAudioHandler* handler);
   void postCallback(resip::ReadCallbackBase* fp);

   TeradiciAudioHandler* handler() const { return mMediaHandler; }

   // AudioDeviceHidHandlerThreadx
   void OnThreadxAudioDeviceKeyPress(webrtc::ThreadxAudioDeviceKeyPressType key);
   void OnThreadxAudioDeviceConnected();

private:
   int cleanupImpl(webrtc_recon::MediaStackImpl* ms, int channel);
   webrtc::AudioDeviceThreadx* threadxAudioImpl();
   void queryDeviceListForDeviceChange(int dummy);

private:
	webrtc_recon::MediaStackImpl* mMediaStack;
   cpc::vector<TeradiciAudioDeviceInfo> mAudioDeviceList;
   resip::Fifo<resip::ReadCallbackBase>* mCallbackFifo;
   TeradiciAudioHandler* mMediaHandler;
   TeradiciPlaySoundHelper* mPlaySoundHelper;
   cpc::vector<TeradiciAudioCodecInfo> mAudioCodecList;
   std::function<void(void)> mCbHook;
};
}
}
#endif // CPCAPI2_AUDIO_IMPL_H
