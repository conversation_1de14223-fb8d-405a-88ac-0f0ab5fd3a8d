#pragma once

#include <remotesync/RemoteSyncHandler.h>
#include <remotesync/RemoteSyncItem.h>
#include <remotesync/RemoteSyncItemUpdate.h>
#include <remotesync/RemoteSyncConversationThreadItem.h>
#include <rutil/Reactor.hxx>
#include <ostream>

namespace CPCAPI2
{

namespace RemoteSync
{

/**
* Internal marker class for synchronous callbacks
*/
class RemoteSyncSyncHandler
{
public:
   virtual ~RemoteSyncSyncHandler() {}
};

class RemoteSyncAsyncHandler
{
public:
   virtual ~RemoteSyncAsyncHandler() {}
   virtual void onEvent(resip::ReadCallbackBase* rcb) = 0;
};

}

}

