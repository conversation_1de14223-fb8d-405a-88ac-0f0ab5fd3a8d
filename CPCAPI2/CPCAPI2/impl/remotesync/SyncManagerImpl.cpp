#include <brand_branded.h>

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_SYNC
#include "util/LogSubsystems.h"
#include "util/APILogger.h"

#include <remotesync/RemoteSyncManager.h>
#include <phone/Phone.h>
#include <phone/NetworkChangeHandler.h>
#include "../phone/PhoneInterface.h"

#include "SyncManagerImpl.h"

#include <util/statemachine/AbstractState.h>

#include <websocket/json/WebSocketCommand.h>
#include <websocket/json/StatusResponse.h>
#include <websocket/WebSocketStateMachine.h>
#include <websocket/states/StateConnected.h>
#include <websocket/states/StateConnecting.h>
#include <websocket/states/StateDisconnected.h>
#include <websocket/states/StateFailed.h>

#include "SyncManagerInterface.h"
#include "commands/LoginCmd.h"
#include "commands/Command.h"
#include "commands/SyncCmd.h"
#include "commands/SyncCmdResponse.h"
#include "commands/UpdateItemCmd.h"
#include "commands/UpdateItemCmdResponse.h"
#include "commands/FetchRangeCmd.h"
#include "commands/FetchRangeResponse.h"
#include "commands/FetchConversationsCmd.h"
#include "commands/FetchConversationResponse.h"
#include "commands/SetAccountsCmd.h"
#include "commands/UpdateNotification.h"
#include "commands/MessageReactions.h"
#include "commands/GetMessageCountCmd.h"
#include "commands/GetMessageCountResponse.h"
#include "commands/UpdateConversationCmd.h"
#include "commands/UpdateConversationNotification.h"
#include "commands/PingCmd.h"
#include "commands/PingResponse.h"
#include "commands/UpdateItemsCmd.h"
#include "commands/UpdateItemsMessage.h"

#include <rutil/Reactor.hxx>
#include <iostream>
#include <string>

#include <writer.h>
#include <stringbuffer.h>

#ifdef _WIN32
#include <Windows.h>
#else
#include <sys/time.h>
#endif

#if defined (__APPLE__) && defined (TARGET_OS_IPHONE)
#include <openssl/ssl.h> // needed to use SSL_get_fd() method
#endif

static const char* kSyncManagerNullErrStr("Sync state machine is null; aborting request. Was configureSettings not called yet?");

#define FIRE_ERROR_AND_RETURN( HREQUEST, ERRTEXT ) \
{ \
   OnErrorEvent evt; \
   evt.requestID    = ( HREQUEST ); \
   evt.errorMessage = ( ERRTEXT ); \
   API_EVENT( "RemoteSync::OnErrorEvent", "requestID: %" PRId64", errorMessage: %s", evt.requestID, evt.errorMessage.c_str() ); \
   fireEvent( cpcFunc( RemoteSyncHandler::onError ), evt ); \
   return kError; \
}

using namespace CPCAPI2::RemoteSync;
using CPCAPI2::WebSocket::WebSocketState;
using CPCAPI2::WebSocket::WebSocketStatePtr;
using CPCAPI2::WebSocket::WebSocketStateMachine;
using CPCAPI2::WebSocket::WebSocketStateDispatcher;
using CPCAPI2::WebSocket::StateFailed;

// ============================================================================
/**
* Return the number of milliseconds since the UNIX epoch (Jan 1st, 1970).
* NB: Win32 epoch is not the same.
*/
int64_t SyncManagerImpl::millisSinceEpoch()
{
   int64_t result(0);
#ifdef _WIN32
   FILETIME ft;
   GetSystemTimeAsFileTime(&ft);

   LARGE_INTEGER date, adjust;
   date.HighPart = ft.dwHighDateTime;
   date.LowPart = ft.dwLowDateTime;

   // 100-nanoseconds = milliseconds * 10000
   adjust.QuadPart = 11644473600000 * 10000;

   // removes the diff between 1970 and 1601
   date.QuadPart -= adjust.QuadPart;

   // result in millis, not nano-intervals
   result = date.QuadPart / 10000;
#else
   struct timeval now;
   gettimeofday(&now, NULL);
   result = (((int64_t)now.tv_sec) * 1000L) + (((int64_t)now.tv_usec) / 1000L);
#endif
   return result;
}

static std::string ToString( const cpc::vector< RemoteSyncItem >& itemsToSync )
{
   std::string idlist;
   idlist += "( ";
   for( cpc::vector< RemoteSyncItem >::const_iterator iter = itemsToSync.begin() ; iter != itemsToSync.end() ; ++iter )
   {
      if( iter->uniqueID.size() > 0 )
      {
         idlist += iter->uniqueID;
         idlist += " ";
      }
   }
   idlist += ")";
   return idlist;
}

static std::string ToString( const cpc::vector<RemoteSyncReaction>& reactions )
{
   std::string idlist;
   for( cpc::vector< RemoteSyncReaction >::const_iterator iter = reactions.begin() ; iter != reactions.end() ; ++iter )
   {
      if (idlist.length() > 0)
         idlist += ", ";
      idlist += iter->value;
   }
   idlist = "( " + idlist + " )";
   return idlist;
}

static std::string ToString( const cpc::vector< RemoteSyncConversationThreadItem >& items )
{
   std::string idlist;
   idlist += "( ";
   for( cpc::vector< RemoteSyncConversationThreadItem >::const_iterator iter = items.begin() ; iter != items.end() ; ++iter )
   {
      if( iter->latestMessage.uniqueID.size() > 0 )
      {
         idlist += iter->latestMessage.uniqueID;
         idlist += " ";
      }
   }
   idlist += ")";
   return idlist;
}

// ============================================================================
SyncManagerImpl::SyncManagerImpl( SessionHandle hSession,
            boost::asio::io_service& ioService,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
            CPCAPI2::SslCipherOptions tlsSettings ) :
   m_hSession( hSession ),
   m_IOService( ioService ),
   m_CallbackFifo( callbackFifo ),
   m_Handler( NULL ),
   m_StateMachine( NULL ),
   mTlsSettings( tlsSettings ),
   m_destructOnDisconnected( false ),
   m_deleteThisQueued( false )   // used to avoid double delete
{
   mCbHook = NULL;
}

SyncManagerImpl::~SyncManagerImpl()
{
   m_Handler = NULL;

   delete m_StateMachine;
   m_StateMachine = NULL;
}

void SyncManagerImpl::addSdkObserver(RemoteSyncHandler* sdkObserver)
{
   mSdkObservers.push_back(sdkObserver);
}

int SyncManagerImpl::configureSettings( const struct RemoteSyncSettings& settings )
{
   // Always update the settings
   m_Settings = settings;

   if (TLS_DEFAULT == m_Settings.wsSettings.tlsVersion) m_Settings.wsSettings.tlsVersion = mTlsSettings.getTLSVersion(SslCipherUsageWebSockets);
   if (m_Settings.wsSettings.cipherSuite.empty()) m_Settings.wsSettings.cipherSuite = mTlsSettings.getCiphers(SslCipherUsageWebSockets);

   // Setup the state machine and dispatcher if it hasn't been done already.
   // Only do this one time. NB: settings is passed as a reference.
   if( m_StateMachine == NULL )
   {
      m_StateMachine = new WebSocketStateMachine( RESIPROCATE_SUBSYSTEM, m_IOService, m_Settings.wsSettings, "Remote Sync " + std::to_string(m_hSession) );

      // Add myself as a listener to the state machine
      m_StateMachine->addListener( this );
   }
   return kSuccess;
}

int SyncManagerImpl::setHandler( RemoteSyncHandler * notificationHandler )
{
   m_Handler = notificationHandler;
   return kSuccess;
}

int SyncManagerImpl::connect()
{
   StackLog(<< "SyncManagerImpl::connect(): " << this << " settings: " << m_Settings);
   
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }
   
   // Make sure we only call connect from the right state
   if( strcmp( m_StateMachine->getCurrentStateID(), STATE_DISCONNECTED_ID ) != 0 )
      return kError;

   m_StateMachine->setCurrentState( STATE_CONNECTING_ID );
   return kSuccess;
}

int SyncManagerImpl::disconnect()
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   // Should be possible to move to disconnected state from any state
   if( !m_StateMachine->setCurrentState( STATE_DISCONNECTED_ID ))
      return kError;

   return kSuccess;
}

int SyncManagerImpl::destroy()
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }
   
   m_destructOnDisconnected = true;
   
   const char* stateID(m_StateMachine->getCurrentStateID());
   if (strcmp(stateID , STATE_DISCONNECTED_ID) == 0)
   {
      // already in disconnected state
      if (std::shared_ptr<WebSocket::StateDisconnected> state = std::dynamic_pointer_cast<WebSocket::StateDisconnected>(m_StateMachine->getCurrentState()))
      {
         if (state->closeCalled())
         {
            m_destructOnDisconnected = false;

            if (!m_deleteThisQueued)
            {
               m_IOService.post( std::bind( &SyncManagerImpl::deleteThis, this ));
               m_deleteThisQueued = true;
            }
         }
         else
         {
            // in disconnected state, but onClose not called yet; not safe to destruct.
            // wait for onClose to be called
         }
      }
   }
   else if (strcmp(stateID , STATE_FAILED_ID) == 0)
   {
      m_destructOnDisconnected = false;
      
      // in failed state; go to disconnected to make sure failure retry timer stops
      m_StateMachine->setCurrentState( STATE_DISCONNECTED_ID );
      
      // onClose shouldn't fire, so queue destruction now
      if (!m_deleteThisQueued)
      {
         m_IOService.post( std::bind( &SyncManagerImpl::deleteThis, this ));
         m_deleteThisQueued = true;
      }
   }
   else if ( !m_StateMachine->setCurrentState( STATE_DISCONNECTED_ID ))
   {
      return kError;
   }

   return kSuccess;
}

void SyncManagerImpl::updateTimestampDelta( const int64_t& clientTime, const int64_t& serverTime )
{
   const int64_t now = millisSinceEpoch();
   OnTimestampDeltaEvent newEvent;
   newEvent.timestampDelta = serverTime - (clientTime + (now - clientTime) / 2);
   API_EVENT( "RemoteSync::OnTimestampDeltaEvent", "timestampDelta: %" PRId64, newEvent.timestampDelta );
   fireEvent( cpcFunc( RemoteSyncHandler::onTimestampDelta ), newEvent );
}

int SyncManagerImpl::setAccounts( RequestHandle hRequest, const cpc::vector<cpc::string>& accounts )
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( hRequest, kSyncManagerNullErrStr );
   }

   std::string errString;
   SetAccountsCmd cmd( accounts );
   if( !m_StateMachine->sendCommand( hRequest, cmd, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::syncItem( RequestHandle hRequest, RemoteSyncItem const & itemToSync)
{
   cpc::vector<RemoteSyncItem> items;
   items.push_back(itemToSync);
   return syncItems( hRequest, items );
}

int SyncManagerImpl::syncItems( RequestHandle hRequest, const cpc::vector<RemoteSyncItem>& itemsToSync)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( hRequest, kSyncManagerNullErrStr );
   }

   std::string errString;
   SyncCmd sync( itemsToSync );
   if( !m_StateMachine->sendCommand( hRequest, sync, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::updateItem( RequestHandle hRequest, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   UpdateItemCmd update( serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp);
   if( !m_StateMachine->sendCommand( hRequest, update, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::fetchRangeRevision(
   RequestHandle hRequest,
   Revision lowestRevision,
   Revision highestRevision,
   const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
   const cpc::string& conversationID,
   const cpc::string& account,
   bool includeDeleted,
   int count,
   int offset,
   bool ascending)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   FetchRangeCmd fetch( offset, count, ascending, lowestRevision, highestRevision, true, itemTypes, conversationID, account, includeDeleted );
   if( !m_StateMachine->sendCommand( hRequest, fetch, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::fetchRangeCreatedTime(
   RequestHandle hRequest, 
   int64_t lowestCreatedTime,
   int64_t highestCreatedTime,
   const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
   const cpc::string& conversationID,
   const cpc::string& account,
   bool includeDeleted,
   int count,
   int offset,
   bool ascending)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   FetchRangeCmd fetch( offset, count, ascending, lowestCreatedTime, highestCreatedTime, false, itemTypes, conversationID, account, includeDeleted );
   if( !m_StateMachine->sendCommand( hRequest, fetch, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::fetchConversations(
   RequestHandle hRequest,
   int64_t lowestClientCreatedTime,
   int64_t highestClientCreatedTime,
   int count,
   int offset)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   FetchConversationsCmd cmd( lowestClientCreatedTime, highestClientCreatedTime, offset, count );
   if( !m_StateMachine->sendCommand( hRequest, cmd, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::fetchConversations( RequestHandle hRequest, const cpc::vector< cpc::string >& conversationIDs)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   FetchConversationsCmd cmd( conversationIDs );
   if( !m_StateMachine->sendCommand( hRequest, cmd, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::updateConversation( RequestHandle hRequest, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   UpdateConversationCmd cmd( accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted );
   if( !m_StateMachine->sendCommand( hRequest, cmd, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::updateItems(RequestHandle hRequest, 
                                 const cpc::vector<cpc::string>& accounts, 
                                 const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, 
                                 const cpc::vector<cpc::string>& conversationIDs,
                                 const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   UpdateItemsCmd cmd(accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp);
   if (!m_StateMachine->sendCommand(hRequest, cmd, errString))
      FIRE_ERROR_AND_RETURN(hRequest, errString.c_str());

   return kSuccess;
}

int SyncManagerImpl::sendReaction(RequestHandle hRequest, const RemoteSyncReaction& reaction)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   SendReactionCmd cmd(reaction);
   if (!m_StateMachine->sendCommand(hRequest, cmd, errString))
      FIRE_ERROR_AND_RETURN(hRequest, errString.c_str());

   return kSuccess;
}

int SyncManagerImpl::fetchMessagesReactions(RequestHandle hRequest, bool ascending, int offset, int count)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   FetchMessagesReactionsCmd cmd(ascending, offset, count);
   if (!m_StateMachine->sendCommand(hRequest, cmd, errString))
      FIRE_ERROR_AND_RETURN(hRequest, errString.c_str());

   return kSuccess;
}

int SyncManagerImpl::getMessageCount( RequestHandle hRequest, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types)
{
   if (!m_StateMachine)
   {
      FIRE_ERROR_AND_RETURN( CPCAPI2_REMOTESYNC_NO_REQUEST_ID, kSyncManagerNullErrStr );
   }

   std::string errString;
   GetMessageCountCmd cmd( accountID, types );
   if( !m_StateMachine->sendCommand( hRequest, cmd, errString ))
      FIRE_ERROR_AND_RETURN( hRequest, errString.c_str() );

   return kSuccess;
}

int SyncManagerImpl::onNetworkChange( const CPCAPI2::NetworkChangeEvent& args )
{
   if (!m_StateMachine) return kSuccess;

   const char *stateID( m_StateMachine->getCurrentStateID() );

   // we don't want to re-awake the state machine if it was previously disconnected
   if( strcmp( stateID , STATE_DISCONNECTED_ID ) == 0 )
      return kSuccess;

   DebugLog(<< "Network change detected; re-connecting websocket for SyncManagerImpl");

   // reset the failed state expiry time, to account for network instability.
   std::shared_ptr< StateFailed > pFailedState( std::dynamic_pointer_cast< StateFailed >( m_StateMachine->getState( STATE_FAILED_ID )));
   if( pFailedState )
      pFailedState->resetExpiryTime();

   // Move to either failed or connecting state, depending.
   m_StateMachine->setCurrentState(( strcmp( stateID, STATE_FAILED_ID ) == 0 ) ? STATE_CONNECTING_ID : STATE_FAILED_ID );
   return kSuccess;
}

void SyncManagerImpl::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if( cbHook != NULL && context != NULL )
      mCbHook = std::bind( cbHook, context );
}

static bool StateToEnum( const CPCAPI2::AbstractStatePtr state, ConnectionState& outEnum )
{
   if( state.get() == NULL )
      return false;

   const char *stateID = state->getUniqueID();

   if( strcmp( stateID, STATE_CONNECTED_ID ) == 0 )
      outEnum = ConnectionState_Connected;
   else if( strcmp( stateID, STATE_CONNECTING_ID ) == 0 )
      outEnum = ConnectionState_Connecting;
   else if( strcmp( stateID, STATE_DISCONNECTED_ID ) == 0 )
      outEnum = ConnectionState_Disconnected;
   else if( strcmp( stateID, STATE_FAILED_ID ) == 0 )
      outEnum = ConnectionState_Failed;
   else
      return false;

   return true;
}

void SyncManagerImpl::onStateChange( const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason )
{
   OnConnectionStateEvent evt;

   if( !StateToEnum( oldState, evt.previousState ) ||
       !StateToEnum( newState, evt.currentState ))
       return;

   API_EVENT( "RemoteSync::OnConnectionStateEvent", "prevState: %d, curState: %d", evt.previousState, evt.currentState );
   fireEvent( cpcFunc( RemoteSyncHandler::onConnectionState ), evt );
}

void SyncManagerImpl::onLogin( void )
{
   // TODO: Connection succeeded, perform a login (once server protocol doc available)
   DebugLog(<< "SyncStateConnecting: connected to remote sync server, starting LOGIN" );

   // Check the settings to see whether or not there is enough information
   // to perform a login (the login step is optional).
   if( !m_Settings.password.empty() )
   {
      DebugLog(<< "SyncStateConnecting: Initiating Login to Remote Sync server" );
      LoginCmd command( m_Settings.password, m_Settings.accounts, m_Settings.clientDeviceInfo );

      std::string errMessage;
      RequestHandle hRequest = SyncManagerInterface::nextRequestHandle(); // throwaway handle
      if( !m_StateMachine->sendCommand( hRequest, command, errMessage, true ))
      {
         OnErrorEvent evt;
         evt.errorMessage = errMessage.c_str();
         API_EVENT( "RemoteSync::OnErrorEvent", "errorMessage: %s", evt.errorMessage.c_str() );
         fireEvent( cpcFunc( RemoteSyncHandler::onError ), evt );
      }
   }
}

void SyncManagerImpl::onLoginResponse( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse response;
   if (response.fromString( inString ))
   {
      if (response.isErrorCondition())
      {
         // Login failure from the server, treat this as a failure
         ErrLog( << "StateConnecting: Couldn't login to the server, server responded with error: " << response.getErrorCode() );

         // Depending on the type of failure, we will either retry, or go back
         // to disconnected state potentially.
         m_StateMachine->setCurrentState( STATE_FAILED_ID );
      }
      else
      {
         // Login successful proceed to Registered state
         DebugLog( << "StateConnecting: Login successful" );
         m_StateMachine->setCurrentState( STATE_CONNECTED_ID );
      }
   }
   else
   {
      ErrLog( << "StateConnecting: Failed to parse message" );
      return;
   }
}

void SyncManagerImpl::onMessage( const std::string& inString )
{
   // Try to parse a status response
   WebSocket::StatusResponse sResponse;
   if( sResponse.fromString( inString ))
   {
      std::string origCmd( sResponse.getOriginalCommandName() );

      if( sResponse.isErrorCondition() )
      {
         // Handle status response errors 'generically'
         // (note that the command name is not checked for errors)
         std::string errorMessage;
         sResponse.getExtensionAttribute( "errorMessage", errorMessage );

         OnErrorEvent evt;
         evt.requestID = sResponse.getRequestHandle();
         evt.errorCode = sResponse.getErrorCode();
         evt.errorMessage = errorMessage.c_str();

         API_EVENT( "RemoteSync::OnErrorEvent", "requestID: %" PRId64 ", errorCode: %s, errorMessage: %s", evt.requestID, evt.errorCode.c_str(), evt.errorMessage.c_str() );
         fireEvent( cpcFunc( RemoteSyncHandler::onError ), evt );
         return;
      }
      else if( origCmd == COMMAND_NAME_SET_ACCOUNTS )
      {
         SetAccountsEvent evt;
         evt.requestID = sResponse.getRequestHandle();

         API_EVENT( "RemoteSync::SetAccountsEvent", "requestID: %" PRId64, evt.requestID );
         fireEvent( cpcFunc( RemoteSyncHandler::onSetAccounts ), evt );
         return;
      }

      WarningLog( << "Unrecognized original command in status response: " << origCmd );
      return;
   }

   /* Parse the UPDATE_ITEMS message which could be either a response or a notification */
   UpdateItemsMessage uiMessage;
   if( uiMessage.fromString( inString ))
   {
      if( uiMessage.isResponse() )
      {
         UpdateItemsCompleteEvent evt;
         evt.requestID        = uiMessage.m_hRequest;
         evt.isRead           = uiMessage.isRead;
         evt.isDeleted        = uiMessage.isDeleted;
         evt.isEdited         = uiMessage.isEdited;
         evt.accounts         = uiMessage.accounts;
         evt.itemTypes        = uiMessage.item_types;
         evt.conversationIDs  = uiMessage.conversation_ids;
         evt.serverIDs        = uiMessage.server_ids;
         evt.readTimestamp    = uiMessage.readTimestamp;

         API_EVENT("RemoteSync::UpdateItemsCompleteEvent", "requestID: %" PRId64, evt.requestID);
         fireEvent(cpcFunc(RemoteSyncHandler::onUpdateItemsComplete), evt);
         return;
      }
      else
      {
         ItemsUpdatedEvent evt;
         evt.isRead           = uiMessage.isRead;
         evt.isDeleted        = uiMessage.isDeleted;
         evt.isEdited         = uiMessage.isEdited;
         evt.accounts         = uiMessage.accounts;
         evt.itemTypes        = uiMessage.item_types;
         evt.conversationIDs  = uiMessage.conversation_ids;
         evt.serverIDs        = uiMessage.server_ids;
         evt.readTimestamp    = uiMessage.readTimestamp;

         API_EVENT("RemoteSync::ItemsUpdatedEvent", "requestID: %" PRId64 ", isRead: %d, isDeleted: %d, isEdited: %d, accounts: %d itemTypes: %d conversationIDs: %d serverIDs: %d",
            0, evt.isRead, evt.isDeleted, evt.isEdited, evt.accounts.size(), evt.itemTypes.size(), evt.conversationIDs.size(), evt.serverIDs.size());
         fireEvent(cpcFunc(RemoteSyncHandler::onItemsUpdated), evt);
         return;
      }
   }

   SyncCmdResponse scResponse;
   if( scResponse.fromString( inString ))
   {
      SyncItemsCompleteEvent evt;
      evt.requestID = scResponse.getRequestHandle();
      evt.items     = scResponse.items;
      evt.rev       = scResponse.revision;

      API_EVENT( "RemoteSync::SyncItemsCompleteEvent", "requestID: %" PRId64 ", revision: %" PRId64 , evt.requestID, evt.rev );
      fireEvent( cpcFunc( RemoteSyncHandler::onSyncItemsComplete ), evt );
      return;
   }

   UpdateItemCmdResponse uicResponse;
   if( uicResponse.fromString( inString ))
   {
      UpdateItemCompleteEvent evt;
      evt.requestID = uicResponse.getRequestHandle();
      evt.delta     = uicResponse.delta;
      evt.rev       = uicResponse.revision;

      API_EVENT( "RemoteSync::UpdateItemCompleteEvent", "requestID: %" PRId64 ", revision: %" PRId64, evt.requestID, evt.rev );
      fireEvent( cpcFunc( RemoteSyncHandler::onUpdateItemComplete ), evt );
      return;
   }

   FetchRangeResponse frResponse;
   if( frResponse.fromString( inString ))
   {
      FetchRangeCompleteEvent evt;
      evt.requestID      = frResponse.getRequestHandle();
      evt.items          = frResponse.items;
      evt.request_offset = frResponse.request_offset;
      evt.request_count  = frResponse.request_maxcount;

      API_EVENT( "RemoteSync::FetchRangeCompleteEvent", "requestID: %" PRId64 ", offset: %d, count: %d, clientIDs: %s",
         evt.requestID, evt.request_offset, evt.request_count, ToString( evt.items ).c_str() );
      fireEvent( cpcFunc( RemoteSyncHandler::onFetchRangeComplete ), evt );
      return;
   }

   FetchConversationResponse fcResponse;
   if( fcResponse.fromString( inString ))
   {
      FetchConversationsCompleteEvent evt;
      evt.requestID      = fcResponse.getRequestHandle();
      evt.items          = fcResponse.items;
      evt.request_offset = fcResponse.request_offset;
      evt.request_count  = fcResponse.request_maxcount;

      API_EVENT( "RemoteSync::FetchConversationsCompleteEvent", "requestID: %" PRId64 ", offset: %d, count: %d, clientIDs: %s",
         evt.requestID, evt.request_offset, evt.request_count, ToString( evt.items ).c_str() );
      fireEvent( cpcFunc( RemoteSyncHandler::onFetchConversationsComplete ), evt );
      return;
   }

   FetchMessagesReactionsResponse fmrResponse;
   if( fmrResponse.fromString( inString ))
   {
      FetchMessagesReactionsCompleteEvent evt;
      evt.requestID      = fmrResponse.getRequestHandle();
      evt.reactions      = fmrResponse.reactions;
      evt.request_offset = fmrResponse.request_offset;
      evt.request_maxcount = fmrResponse.request_maxcount;

      API_EVENT( "RemoteSync::FetchMessagesReactionsCompleteEvent", "requestID: %" PRId64 ", offset: %d, maxcount: %d, reactions: %s",
         evt.requestID, evt.request_offset, evt.request_maxcount, ToString( evt.reactions ).c_str() );
      fireEvent( cpcFunc( RemoteSyncHandler::onFetchMessagesReactionsComplete ), evt );
      return;
   }

   GetMessageCountResponse gmcResponse;
   if( gmcResponse.fromString( inString ))
   {
      MessageCountEvent evt;
      evt.requestID = gmcResponse.getRequestHandle();
      evt.unread    = gmcResponse.unreadMessages;
      evt.total     = gmcResponse.totalMessages;
      evt.unreadConversations = gmcResponse.unreadConversations;
      evt.totalConversations = gmcResponse.totalConversations;

      API_EVENT( "RemoteSync::MessageCountEvent", "requestID: %" PRId64 ", unread: %d, total: %d, unreadConv: %d, totalConv: %d",
         evt.requestID, evt.unread, evt.total, evt.unreadConversations, evt.totalConversations );
      fireEvent( cpcFunc( RemoteSyncHandler::onMessageCount ), evt );
      return;
   }

   PingResponse pResponse;
   if( pResponse.fromString( inString ))
   {
      updateTimestampDelta( pResponse.clientClock, pResponse.serverClock );
      return;
   }

   UpdateNotification uNotification;
   if( uNotification.fromString( inString ))
   {
      NotificationUpdateEvent evt;
      evt.items = uNotification.items;
      evt.rev   = uNotification.revision;

      API_EVENT( "RemoteSync::NotificationUpdateEvent", "revision: %" PRId64 ", clientIDs: %s",
         evt.rev, ToString( evt.items ).c_str() );
      fireEvent( cpcFunc( RemoteSyncHandler::onNotificationUpdate ), evt );
      return;
   }

   MessageReactions mrNotification;
   if( mrNotification.fromString( inString ))
   {
      MessageReactionsEvent evt;
      evt.requestID = mrNotification.requestID;
      evt.rev = mrNotification.rev;
      evt.created_time = mrNotification.created_time;
      evt.server_id = mrNotification.server_id;
      evt.address = mrNotification.address;
      evt.value = mrNotification.value;

      API_EVENT( "RemoteSync::MessageReactionsEvent", "requestID: %" PRId64 ", rev: %" PRId64 ", created_time: %" PRId64 ", server_id: %" PRId64 ", address: %s, value: %s",
         evt.requestID, evt.rev, evt.created_time, evt.server_id, evt.address.c_str(), evt.value.c_str() );
      fireEvent( cpcFunc( RemoteSyncHandler::onMessageReactions ), evt );
      return;
   }

   // NB: this functions both as response (for success) and notification
   UpdateConversationNotification ucNotification;
   if( ucNotification.fromString( inString ))
   {
      ConversationUpdatedEvent evt;
      evt.requestID                = ucNotification.m_hRequest; // zero if notification
      evt.conversationID           = ucNotification.conversationID;
      evt.rev                      = ucNotification.revision;
      evt.highestClientCreatedTime = ucNotification.highestClientCreatedTime;
      evt.setItemsDeleted          = ucNotification.setItemsDeleted;
      evt.setItemsRead             = ucNotification.setItemsRead;

      API_EVENT( "RemoteSync::ConversationUpdatedEvent", "requestID: %" PRId64 ", convID: %s, rev: %" PRId64 ", highestClientCreatedTime: %" PRId64 ", setItemsDeleted: %d, setItemsRead: %d",
         evt.requestID, evt.conversationID.c_str(), evt.rev, evt.highestClientCreatedTime, evt.setItemsDeleted, evt.setItemsRead );
      fireEvent( cpcFunc( RemoteSyncHandler::onConversationUpdated), evt );
      return;
   }

   WarningLog(<< "Unhandled protocol message" );
}

void SyncManagerImpl::onPing( websocketpp::connection_hdl hWebSock )
{
   RequestHandle hRequest = SyncManagerInterface::nextRequestHandle();
   std::string errString;

#ifdef CPCAPI2_AUTO_TEST
   PingCmd ping( ********** );
#else
   PingCmd ping( millisSinceEpoch() );
#endif
   m_StateMachine->sendCommand( hRequest, ping, errString );
}

bool SyncManagerImpl::onReconnect( uint16_t code )
{
   switch( code )
   {
	case 1002 : // PROTOCOL_ERROR
	case 4002 : // WSCR_SERVER_COMMUNICATION_ERROR Server Communication Error
	case 4003 : // WSCR_UNEXPECTED_ERROR Unexpected Error
		return true;
	case 1003 : // CANNOT_ACCEPT
	case 4015 : // TOO_MANY_CONNECTIONS
	case 4100 : // WSCR_UNAUTHORIZED Unauthorized: User not found or password not valid
	case 4101 : // WSCR_LOCKED_OUT Unauthorized: User locked out
	case 4102 : // WSCR_SUSPENDED Unauthorized: User or group suspended
	case 4103 : // WSCR_GROUP_NOT_CONFIGURED Unauthorized: Group not configured
      WarningLog( << "Connection Close/Failure with error code " << code << " Retry disabled" );
      return false;
   default:
      break;
   }
   return true;
}

void SyncManagerImpl::onClose( )
{
   if ( m_destructOnDisconnected && !m_deleteThisQueued)
   {
      m_IOService.post( std::bind( &SyncManagerImpl::deleteThis, this ));
      m_deleteThisQueued = true;
   }
}

#endif
