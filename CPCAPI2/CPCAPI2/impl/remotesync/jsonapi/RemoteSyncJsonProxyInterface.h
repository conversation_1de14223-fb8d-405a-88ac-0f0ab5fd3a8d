#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_JSON_PROXY_INTERFACE_H)
#define CPCAPI2_REMOTE_SYNC_JSON_PROXY_INTERFACE_H

#include "interface/experimental/remotesync/RemoteSyncJsonProxy.h"
#include "interface/experimental/remotesync/RemoteSyncHandler.h"
#include "remotesync/SyncManagerInterface.h"
#include "jsonapi/JsonApiClientModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>
#include <rutil/Fifo.hxx>
#include <mutex>
#include <condition_variable>


namespace CPCAPI2
{

class PhoneInterface;

namespace RemoteSync 
{

class RemoteSyncJsonProxyInterface : public CPCAPI2::RemoteSync::RemoteSyncJsonProxy,
                                     public CPCAPI2::JsonApi::JsonApiClientModule,
                                     public CPCAPI2::PhoneModule
#ifdef CPCAPI2_AUTO_TEST
                                   , public AutoTestProcessor
#endif
{

public:

   RemoteSyncJsonProxyInterface(CPCAPI2::Phone* phone);
   virtual ~RemoteSyncJsonProxyInterface();

   virtual void setCallbackHook(void (*cbHook)(void*), void* context);
   
   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiClientModule
   virtual void setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport) OVERRIDE;
   virtual int processIncoming(const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // AutoTestProcessor
#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif
   
   // RemoteSyncManager
   virtual int process(int timeout) OVERRIDE;
   virtual int setHandler(const SessionHandle& sessionHandle, RemoteSyncHandler* notificationHandler) OVERRIDE;
   virtual SessionHandle create(void) OVERRIDE;
   virtual int configureSettings(const SessionHandle& sessionHandle, const struct RemoteSyncSettings& settings) OVERRIDE;
   virtual int connect(const SessionHandle& sessionHandle) OVERRIDE;
   virtual int disconnect(const SessionHandle& sessionHandle) OVERRIDE;
   virtual int destroy(const SessionHandle& sessionHandle) OVERRIDE;
   virtual RequestHandle setAccounts(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts) OVERRIDE;
   virtual RequestHandle syncItem(const SessionHandle& sessionHandle, const RemoteSyncItem& itemToSync) OVERRIDE;
   virtual RequestHandle syncItems(const SessionHandle& sessionHandle, const cpc::vector<RemoteSyncItem>& itemsToSync) OVERRIDE;
   virtual RequestHandle fetchRangeRevision(
      const SessionHandle& sessionHandle,
      Revision lowestRevision,
      Revision highestRevision,
      const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
      const cpc::string& conversationID,
      const cpc::string& account,
      bool includeDeleted,
      int count,
      int offset,
      bool ascending) OVERRIDE;
   virtual RequestHandle fetchRangeCreatedTime(
      const SessionHandle& sessionHandle,
      int64_t lowestCreatedTime,
      int64_t highestCreatedTime,
      const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
      const cpc::string& conversationID,
      const cpc::string& account,
      bool includeDeleted,
      int count,
      int offset,
      bool ascending) OVERRIDE;
   virtual RequestHandle fetchConversations(const SessionHandle& sessionHandle, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset) OVERRIDE;
   virtual RequestHandle fetchConversations(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& conversationIDs) OVERRIDE;
   virtual RequestHandle updateConversation(
      const SessionHandle& sessionHandle,
      const cpc::string& accountID,
      const cpc::string& conversationID,
      int64_t highestClientCreatedTime,
      bool setItemsRead,
      bool setItemsDeleted,
      bool setItemsUserDeleted) OVERRIDE;
   virtual RequestHandle getMessageCount(const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types) OVERRIDE;
   virtual RequestHandle updateItem(const SessionHandle& sessionHandle, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp) OVERRIDE;
   virtual RequestHandle sendReaction(const SessionHandle& sessionHandle, const RemoteSyncReaction& reaction) OVERRIDE;
   virtual RequestHandle fetchMessagesReactions(const SessionHandle& hSession, bool ascending, int offset, int count) OVERRIDE;
   virtual RequestHandle updateItems(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs,
                                     const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase* fp);
   void processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request);

   int setHandlerImpl(const SessionHandle& sessionHandle, RemoteSyncHandler* notificationHandler);
   int createImpl();
   int configureSettingsImpl(const SessionHandle& sessionHandle, const struct RemoteSyncSettings& settings);
   int connectImpl(const SessionHandle& sessionHandle);
   int disconnectImpl(const SessionHandle& sessionHandle);
   int destroyImpl(const SessionHandle& sessionHandle);
   int setAccountsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts);
   int syncItemImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const RemoteSyncItem& itemToSync);
   int syncItemsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<RemoteSyncItem>& itemsToSync);
   int fetchRangeRevisionImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, Revision lowestRevision, Revision highestRevision, const struct SyncManagerInterface::FetchRangeParams& params);
   int fetchRangeCreatedTimeImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, int64_t lowestCreatedTime, int64_t highestCreatedTime, const struct SyncManagerInterface::FetchRangeParams& params);
   
   int fetchConversationsByTimeImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset);
   int fetchConversationsByIdImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& conversationIDs);
   int updateConversationImpl(
      RequestHandle requestHandle,
      const SessionHandle& sessionHandle,
      const cpc::string& accountID,
      const cpc::string& conversationID,
      int64_t highestClientCreatedTime,
      bool setItemsRead,
      bool setItemsDeleted,
      bool setItemsUserDeleted);
   int getMessageCountImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types);
   int updateItemImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp);
   int sendReactionImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const RemoteSyncReaction& reaction);
   int fetchMessagesReactionsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, bool ascending, int offset, int count);
   int updateItemsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp);

   int handleCreateResult(const rapidjson::Value& functionObjectVal);
   int handleSetAccounts(const rapidjson::Value& functionObjectVal);
   int handleNotificationUpdate(const rapidjson::Value& functionObjectVal);
   int handleMessageReactions(const rapidjson::Value& functionObjectVal);
   int handleSyncItemsComplete(const rapidjson::Value& functionObjectVal);
   int handleUpdateItemComplete(const rapidjson::Value& functionObjectVal);
   int handleFetchRangeComplete(const rapidjson::Value& functionObjectVal);
   int handleFetchConversationsComplete(const rapidjson::Value& functionObjectVal);
   int handleConversationUpdated(const rapidjson::Value& functionObjectVal);
   int handleMessageCount(const rapidjson::Value& functionObjectVal);
   int handleError(const rapidjson::Value& functionObjectVal);
   int handleConnectionState(const rapidjson::Value& functionObjectVal);
   int handleTimestampDelta(const rapidjson::Value& functionObjectVal);
   int handleUpdateItemsComplete(const rapidjson::Value& functionObjectVal);
   int handleItemsUpdated(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)>> FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiTransport* mTransport;
   std::mutex mMutex;
   std::condition_variable mCondCreated;
   SessionHandle mServerCreatedHandle;
   std::map<SessionHandle, RemoteSyncHandler*> mAppHandlers;
   resip::Fifo<resip::ReadCallbackBase> mCallbacks;
   std::function<void(void)> mCbHook;

};

}

}

#endif // CPCAPI2_REMOTE_SYNC_JSON_PROXY_INTERFACE_H
