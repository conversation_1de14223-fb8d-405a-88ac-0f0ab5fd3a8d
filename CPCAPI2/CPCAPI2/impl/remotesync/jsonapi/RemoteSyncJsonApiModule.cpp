#include "brand_branded.h"
#include <interface/experimental/remotesync/RemoteSyncJsonProxy.h>
#include <interface/experimental/remotesync/RemoteSyncJsonApi.h>
#include <util/APILogger.h>

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "RemoteSyncJsonServerInterface.h"
#endif
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "phone/PhoneInterface.h"
#include "RemoteSyncJsonProxyInterface.h"
#endif

namespace CPCAPI2
{
   namespace RemoteSync
   {
      RemoteSyncJsonApi* RemoteSyncJsonApi::getInterface(Phone* cpcPhone)
      {
         API_INVOKE( "cpcPhone: %d", cpcPhone );
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<RemoteSyncJsonServerInterface>(phone, "RemoteSyncJsonApi");
#else
         return NULL;
#endif
      }

      RemoteSyncJsonProxy* RemoteSyncJsonProxy::getInterface(Phone* cpcPhone)
      {
         API_INVOKE( "cpcPhone: %d", cpcPhone );
         if (!cpcPhone) return NULL;

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<RemoteSyncJsonProxyInterface>(phone, "RemoteSyncJsonProxy");
#else
         return NULL;
#endif
      }
   }
}
