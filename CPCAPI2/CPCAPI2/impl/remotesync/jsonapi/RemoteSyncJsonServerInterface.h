#pragma once

#if !defined(CPCAPI2_REMOTE_SYNC_JSON_SERVER_INTERFACE_H)
#define CPCAPI2_REMOTE_SYNC_JSON_SERVER_INTERFACE_H

#include "interface/experimental/jsonapi/JsonApiServerSendTransport.h"
#include "interface/experimental/remotesync/RemoteSyncJsonApi.h"
#include "interface/experimental/remotesync/RemoteSyncHandler.h"
#include "remotesync/RemoteSyncInternalHandlerTypes.h"
#include "jsonapi/JsonApiServerModule.h"
#include "phone/PhoneModule.h"

#include <rutil/Reactor.hxx>


namespace CPCAPI2
{

class PhoneInterface;

namespace RemoteSync 
{

class RemoteSyncJsonServerInterface : public CPCAPI2::RemoteSync::RemoteSyncJsonApi,
                                      public CPCAPI2::RemoteSync::RemoteSyncHandler,
                                      public CPCAPI2::RemoteSync::RemoteSyncAsyncHand<PERSON>,
                                      public CPCAPI2::JsonApi::JsonApiServerModule,
                                      public CPCAPI2::PhoneModule
{

public:

   RemoteSyncJsonServerInterface(CPCAPI2::Phone* phone);
   virtual ~RemoteSyncJsonServerInterface();

   // PhoneModule
   virtual void Release() OVERRIDE;

   // JsonApiServerModule
   virtual int processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request) OVERRIDE;

   // RemoteSyncHandler
   virtual int onSetAccounts(const SessionHandle& sessionHandle, const SetAccountsEvent& evt) OVERRIDE;
   virtual int onNotificationUpdate(const SessionHandle& sessionHandle, const NotificationUpdateEvent& evt) OVERRIDE;
   virtual int onMessageReactions(const SessionHandle& sessionHandle, const MessageReactionsEvent& evt) OVERRIDE;
   virtual int onFetchMessagesReactionsComplete(const SessionHandle& sessionHandle, const FetchMessagesReactionsCompleteEvent& evt) OVERRIDE;
   virtual int onSyncItemsComplete(const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& evt) OVERRIDE;
   virtual int onUpdateItemComplete(const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& evt) OVERRIDE;
   virtual int onFetchRangeComplete(const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& evt) OVERRIDE;
   virtual int onFetchConversationsComplete(const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& evt) OVERRIDE;
   virtual int onConversationUpdated(const SessionHandle& sessionHandle, const ConversationUpdatedEvent& evt) OVERRIDE;
   virtual int onMessageCount(const SessionHandle& sessionHandle, const MessageCountEvent& evt) OVERRIDE;
   virtual int onError(const SessionHandle& sessionHandle, const OnErrorEvent& evt) OVERRIDE;
   virtual int onConnectionState(const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt) OVERRIDE;
   virtual int onTimestampDelta(const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& evt) OVERRIDE;
   virtual int onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& evt) OVERRIDE;
   virtual int onItemsUpdated(const SessionHandle& sessionHandle, const ItemsUpdatedEvent& evt) OVERRIDE;

   // RemoteSyncAsyncHandler
   virtual void onEvent(resip::ReadCallbackBase* rcb) OVERRIDE;

private:

   void post(resip::ReadCallbackBase* f);
   void execute(resip::ReadCallbackBase* f);

   void processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request);

   int handleSetHandler(const rapidjson::Value& functionObjectVal);
   int handleCreate(const rapidjson::Value& functionObjectVal);
   int handleConfigureSettings(const rapidjson::Value& functionObjectVal);
   int handleConnect(const rapidjson::Value& functionObjectVal);
   int handleDisconnect(const rapidjson::Value& functionObjectVal);
   int handleSetAccounts(const rapidjson::Value& functionObjectVal);
   int handleSyncItem(const rapidjson::Value& functionObjectVal);
   int handleSyncItems(const rapidjson::Value& functionObjectVal);
   int handleFetchRangeRevision(const rapidjson::Value& functionObjectVal);
   int handleFetchRangeCreatedTime(const rapidjson::Value& functionObjectVal);
   int handleFetchConversationsByTime(const rapidjson::Value& functionObjectVal);
   int handleFetchConversationsById(const rapidjson::Value& functionObjectVal);
   int handleUpdateConversation(const rapidjson::Value& functionObjectVal);
   int handleGetMessageCount(const rapidjson::Value& functionObjectVal);
   int handleUpdateItem(const rapidjson::Value& functionObjectVal);
   int handleSendReaction(const rapidjson::Value& functionObjectVal);
   int handleFetchMessagesReactions(const rapidjson::Value& functionObjectVal);
   int handleUpdateItems(const rapidjson::Value& functionObjectVal);

private:

   CPCAPI2::PhoneInterface* mPhone;
   typedef std::map<std::string, std::function<int(const rapidjson::Value&)>> FunctionMap;
   FunctionMap mFunctionMap;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mTransport;

};

}

}

#endif // CPCAPI2_REMOTE_SYNC_JSON_SERVER_INTERFACE_H
