#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_CLIENT_MODULE == 1)
#include "RemoteSyncJsonProxyInterface.h"
#include "remotesync/RemoteSyncManager.h"
#include "remotesync/RemoteSyncHandler.h"
#include "remotesync/commands/Command.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiClient.h"
#include "jsonapi/JsonApiClientInterface.h"
#include "json/JsonHelper.h"
#include "util/cpc_logger.h"

#include <rutil/Random.hxx>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_SYNC
#define JSON_MODULE "RemoteSyncJsonApi"

namespace CPCAPI2
{

namespace RemoteSync
{

RemoteSyncJsonProxyInterface::RemoteSyncJsonProxyInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone)),
mTransport(NULL),
mServerCreatedHandle(0)
{
   mFunctionMap["onCreateResult"] = std::bind(&RemoteSyncJsonProxyInterface::handleCreateResult, this, std::placeholders::_1);
   mFunctionMap["onSetAccounts"] = std::bind(&RemoteSyncJsonProxyInterface::handleSetAccounts, this, std::placeholders::_1);
   mFunctionMap["onNotificationUpdate"] = std::bind(&RemoteSyncJsonProxyInterface::handleNotificationUpdate, this, std::placeholders::_1);
   mFunctionMap["onMessageReactions"] = std::bind(&RemoteSyncJsonProxyInterface::handleMessageReactions, this, std::placeholders::_1);
   mFunctionMap["onSyncItemsComplete"] = std::bind(&RemoteSyncJsonProxyInterface::handleSyncItemsComplete, this, std::placeholders::_1);
   mFunctionMap["onUpdateItemComplete"] = std::bind(&RemoteSyncJsonProxyInterface::handleUpdateItemComplete, this, std::placeholders::_1);
   mFunctionMap["onFetchRangeComplete"] = std::bind(&RemoteSyncJsonProxyInterface::handleFetchRangeComplete, this, std::placeholders::_1);
   mFunctionMap["onFetchConversationsComplete"] = std::bind(&RemoteSyncJsonProxyInterface::handleFetchConversationsComplete, this, std::placeholders::_1);
   mFunctionMap["onConversationUpdated"] = std::bind(&RemoteSyncJsonProxyInterface::handleConversationUpdated, this, std::placeholders::_1);
   mFunctionMap["onMessageCount"] = std::bind(&RemoteSyncJsonProxyInterface::handleMessageCount, this, std::placeholders::_1);
   mFunctionMap["onError"] = std::bind(&RemoteSyncJsonProxyInterface::handleError, this, std::placeholders::_1);
   mFunctionMap["onConnectionState"] = std::bind(&RemoteSyncJsonProxyInterface::handleConnectionState, this, std::placeholders::_1);
   mFunctionMap["onTimestampDelta"] = std::bind(&RemoteSyncJsonProxyInterface::handleTimestampDelta, this, std::placeholders::_1);
   mFunctionMap["onUpdateItemsComplete"] = std::bind(&RemoteSyncJsonProxyInterface::handleUpdateItemsComplete, this, std::placeholders::_1);
   mFunctionMap["onItemsUpdated"] = std::bind(&RemoteSyncJsonProxyInterface::handleItemsUpdated, this, std::placeholders::_1);
   
   JsonApi::JsonApiClientInterface* jsonApiClientIf = dynamic_cast<JsonApi::JsonApiClientInterface*>(JsonApi::JsonApiClient::getInterface(phone));
   setTransport(jsonApiClientIf->getTransport());
}

RemoteSyncJsonProxyInterface::~RemoteSyncJsonProxyInterface()
{
}
   
void RemoteSyncJsonProxyInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

void RemoteSyncJsonProxyInterface::Release()
{
}

void RemoteSyncJsonProxyInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void RemoteSyncJsonProxyInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

void RemoteSyncJsonProxyInterface::postCallback(resip::ReadCallbackBase* fp)
{
   mCallbacks.add(fp);
   if (mCbHook)
   {
      mCbHook();
   }
}

void RemoteSyncJsonProxyInterface::setTransport(CPCAPI2::JsonApi::JsonApiTransport* transport)
{
   mTransport = transport;
}

int RemoteSyncJsonProxyInterface::processIncoming(const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::processIncomingImpl, this, request));
   return kSuccess;
}

void RemoteSyncJsonProxyInterface::processIncomingImpl(const std::shared_ptr<rapidjson::Document>& request)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::processIncomingImpl(): function map size: " << mFunctionMap.size() << " handler list size: " << mAppHandlers.size());
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      DebugLog(<< "RemoteSyncJsonProxyInterface::processIncomingImpl(): triggering " << funcName << " callback");
      it->second(functionObjectVal);
   }
}
   
int RemoteSyncJsonProxyInterface::process(int timeout)
{
   resip::ReadCallbackBase* fp = mCallbacks.getNext(timeout);
   while (fp)
   {
      (*fp)();
      delete fp;
      fp = mCallbacks.getNext(kBlockingModeNonBlocking);
   }

   return kSuccess;
}
   
int RemoteSyncJsonProxyInterface::setHandler(const SessionHandle& sessionHandle, RemoteSyncHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&RemoteSyncJsonProxyInterface::setHandlerImpl, this, sessionHandle, handler);
   if (handler == NULL)
   {
      execute(f);
      process(-1);
   }
   else
   {
      post(f);
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::setHandlerImpl(const SessionHandle& sessionHandle, RemoteSyncHandler* handler)
{
   mAppHandlers[sessionHandle] = handler;
   StackLog(<< "RemoteSyncJsonProxyInterface::setHandlerImpl(): handler: " << handler << " with session handle: " << sessionHandle << " being added to handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "setHandler", JSON_VALUE(sessionHandle));

   return kSuccess;
}

SessionHandle RemoteSyncJsonProxyInterface::create()
{
   StackLog(<< "RemoteSyncJsonProxyInterface::create(): handler list: " << mAppHandlers.size());
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::createImpl, this));
   
   std::unique_lock<std::mutex> lk(mMutex);
   mCondCreated.wait(lk);
   
   StackLog(<< "RemoteSyncJsonProxyInterface::create(): server created handle: " << mServerCreatedHandle << " handler list: " << mAppHandlers.size());
   
   return mServerCreatedHandle;
}

int RemoteSyncJsonProxyInterface::createImpl()
{
   StackLog(<< "RemoteSyncJsonProxyInterface::createImpl(): handler list: " << mAppHandlers.size());
   mServerCreatedHandle = -1;

   JsonFunctionCall(mTransport, "create");

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::configureSettings(const SessionHandle& sessionHandle, const struct RemoteSyncSettings& settings)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::configureSettings(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::configureSettingsImpl, this, sessionHandle, settings));
   return kSuccess;
}

int RemoteSyncJsonProxyInterface::configureSettingsImpl(const SessionHandle& sessionHandle, const struct RemoteSyncSettings& settings)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::configureSettingsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "configureSettings", JSON_VALUE(sessionHandle), JSON_VALUE(settings));

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::connect(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::connect(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::connectImpl, this, sessionHandle));
   return kSuccess;
}

int RemoteSyncJsonProxyInterface::connectImpl(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::connectImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "connect", JSON_VALUE(sessionHandle));

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::disconnect(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::disconnect(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::disconnectImpl, this, sessionHandle));
   return kSuccess;
}

int RemoteSyncJsonProxyInterface::disconnectImpl(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::disconnectImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "disconnect", JSON_VALUE(sessionHandle));

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::destroy(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::destroy(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::destroyImpl, this, sessionHandle));
   return kSuccess;
}

int RemoteSyncJsonProxyInterface::destroyImpl(const SessionHandle& sessionHandle)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::destroyImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "destroy", JSON_VALUE(sessionHandle));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::setAccounts(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::setAccounts(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::setAccountsImpl, this, requestHandle, sessionHandle, accounts));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::setAccountsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::setAccountsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "setAccounts", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accounts));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::syncItem(const SessionHandle& sessionHandle, const RemoteSyncItem& itemToSync)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::syncItem(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::syncItemImpl, this, requestHandle, sessionHandle, itemToSync));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::syncItemImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const RemoteSyncItem& syncItem)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::syncItemImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "syncItem", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(syncItem));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::syncItems(const SessionHandle& sessionHandle, const cpc::vector<RemoteSyncItem>& itemsToSync)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::syncItems(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::syncItemsImpl, this, requestHandle, sessionHandle, itemsToSync));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::syncItemsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<RemoteSyncItem>& syncItems)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::syncItemsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size() << " itemsToSync: " << syncItems.size());

   JsonFunctionCall(mTransport, "syncItems", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(syncItems));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::fetchRangeRevision(const SessionHandle& sessionHandle, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchRangeRevision(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   
   struct SyncManagerInterface::FetchRangeParams params;
   params.itemTypes = itemTypes;
   params.conversationID = conversationID;
   params.account = account;
   params.includeDeleted = includeDeleted;
   params.count = count;
   params.offset = offset;
   params.ascending = ascending;
   
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::fetchRangeRevisionImpl, this, requestHandle, sessionHandle, lowestRevision, highestRevision, params));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::fetchRangeRevisionImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, Revision lowestRevision, Revision highestRevision, const struct SyncManagerInterface::FetchRangeParams& params)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchRangeRevisionImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   JsonFunctionCall(mTransport, "fetchRangeRevision",
      JSON_VALUE(sessionHandle),
      JSON_VALUE(requestHandle),
      JSON_VALUE(lowestRevision),
      JSON_VALUE(highestRevision),
      "itemTypes", params.itemTypes,
      "conversationID", params.conversationID,
      "account", params.account,
      "includeDeleted", params.includeDeleted,
      "count", params.count,
      "offset", params.offset,
      "ascending", params.ascending);

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::fetchRangeCreatedTime(const SessionHandle& sessionHandle, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchRangeCreatedTime(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   
   struct SyncManagerInterface::FetchRangeParams params;
   params.itemTypes = itemTypes;
   params.conversationID = conversationID;
   params.account = account;
   params.includeDeleted = includeDeleted;
   params.count = count;
   params.offset = offset;
   params.ascending = ascending;
   
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::fetchRangeCreatedTimeImpl, this, requestHandle, sessionHandle, lowestCreatedTime, highestCreatedTime, params));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::fetchRangeCreatedTimeImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, int64_t lowestCreatedTime, int64_t highestCreatedTime, const struct SyncManagerInterface::FetchRangeParams& params)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchRangeCreatedTimeImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   JsonFunctionCall(mTransport, "fetchRangeCreatedTime",
      JSON_VALUE(sessionHandle),
      JSON_VALUE(requestHandle),
      JSON_VALUE(lowestCreatedTime),
      JSON_VALUE(highestCreatedTime),
      "itemTypes", params.itemTypes,
      "conversationID", params.conversationID,
      "account", params.account,
      "includeDeleted", params.includeDeleted,
      "count", params.count,
      "offset", params.offset,
      "ascending", params.ascending);

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::fetchConversations(const SessionHandle& sessionHandle, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchConversations(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::fetchConversationsByTimeImpl, this, requestHandle, sessionHandle, lowestClientCreatedTime, highestClientCreatedTime, count, offset));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::fetchConversationsByTimeImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchConversationsByTimeImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "fetchConversationsByTime", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(lowestClientCreatedTime), JSON_VALUE(highestClientCreatedTime), JSON_VALUE(count), JSON_VALUE(offset));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::fetchConversations(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& conversationIDs)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchConversations(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::fetchConversationsByIdImpl, this, requestHandle, sessionHandle, conversationIDs));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::fetchConversationsByIdImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& conversationIDs)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchConversationsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "fetchConversationsById", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(conversationIDs));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::updateConversation(const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateConversation(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::updateConversationImpl, this, requestHandle, sessionHandle, accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::updateConversationImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateConversationImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "updateConversation", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accountID), JSON_VALUE(conversationID), JSON_VALUE(highestClientCreatedTime), JSON_VALUE(setItemsRead), JSON_VALUE(setItemsDeleted), JSON_VALUE(setItemsUserDeleted));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::getMessageCount(const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::vector<RemoteSyncItem::ItemType>& types)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::getMessageCount(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::getMessageCountImpl, this, requestHandle, sessionHandle, accountID, types));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::getMessageCountImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::string& accountID, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::getMessageCountImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "getMessageCount", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accountID), JSON_VALUE(itemTypes));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::updateItem(const SessionHandle& sessionHandle, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateItem(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::updateItemImpl, this, requestHandle, sessionHandle, serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::updateItemImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateItemImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "updateItem", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(serverID), JSON_VALUE(clientID), JSON_VALUE(originalID), JSON_VALUE(stableID), JSON_VALUE(itemRead), JSON_VALUE(itemDeleted), JSON_VALUE(itemEdited), JSON_VALUE(itemUserDeleted), JSON_VALUE(itemState), JSON_VALUE(callDuration), JSON_VALUE(deliveryTimestamp), JSON_VALUE(readTimestamp));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::sendReaction(const SessionHandle& sessionHandle, const RemoteSyncReaction& reaction)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::sendReaction(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::sendReactionImpl, this, requestHandle, sessionHandle, reaction));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::sendReactionImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const RemoteSyncReaction& reaction)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::sendReactionImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "sendReaction", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(reaction));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::fetchMessagesReactions(const SessionHandle& sessionHandle, bool ascending, int offset, int count)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchMessagesReactions(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::fetchMessagesReactionsImpl, this, requestHandle, sessionHandle, ascending, offset, count));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::fetchMessagesReactionsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, bool ascending, int offset, int count)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::fetchMessagesReactionsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "fetchMessagesReactions", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(ascending), JSON_VALUE(offset), JSON_VALUE(count));

   return kSuccess;
}

RequestHandle RemoteSyncJsonProxyInterface::updateItems(const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateItemsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());
   RequestHandle requestHandle = SyncManagerInterface::nextRequestHandle();
   post(resip::resip_bind(&RemoteSyncJsonProxyInterface::updateItemsImpl, this, requestHandle, sessionHandle, accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp));
   return requestHandle;
}

int RemoteSyncJsonProxyInterface::updateItemsImpl(RequestHandle requestHandle, const SessionHandle& sessionHandle, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::updateItemsImpl(): session handle: " << sessionHandle << " handler list: " << mAppHandlers.size());

   JsonFunctionCall(mTransport, "updateItems", JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accounts), JSON_VALUE(itemTypes), JSON_VALUE(conversationIDs), JSON_VALUE(serverIDs), JSON_VALUE(isRead), JSON_VALUE(isDeleted), JSON_VALUE(isEdited), JSON_VALUE(readTimestamp));

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleCreateResult(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleCreateResult(): handler list: " << mAppHandlers.size());
   JsonDeserialize(functionObjectVal, "sessionHandle", mServerCreatedHandle);
   mCondCreated.notify_one();
   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleSetAccounts(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleSetAccounts(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   SetAccountsEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onSetAccounts, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleNotificationUpdate(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleNotificationUpdate(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   NotificationUpdateEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onNotificationUpdate, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleMessageReactions(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleMessageReactions(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   MessageReactionsEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onMessageReactions, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleSyncItemsComplete(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleSyncItemsComplete(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   SyncItemsCompleteEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onSyncItemsComplete, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleUpdateItemComplete(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleUpdateItemComplete(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   UpdateItemCompleteEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onUpdateItemComplete, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleFetchRangeComplete(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleFetchRangeComplete(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   FetchRangeCompleteEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onFetchRangeComplete, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleFetchConversationsComplete(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleFetchConversationsComplete(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   FetchConversationsCompleteEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onFetchConversationsComplete, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleConversationUpdated(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleConversationUpdated(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   ConversationUpdatedEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onConversationUpdated, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleUpdateItemsComplete(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleUpdateItemsComplete(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   UpdateItemsCompleteEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onUpdateItemsComplete, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleItemsUpdated(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleItemsUpdated(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   ItemsUpdatedEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onItemsUpdated, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleMessageCount(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleMessageCount(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   MessageCountEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onMessageCount, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleError(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleError(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   OnErrorEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      StackLog(<< "RemoteSyncJsonProxyInterface::handleError(): handler: " << i->second << " found for session handle: " << sessionHandle << " in list: " << mAppHandlers.size());
      postCallback(makeFpCommand(RemoteSyncHandler::onError, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleConnectionState(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleConnectionState(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   OnConnectionStateEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onConnectionState, i->second, sessionHandle, event));
   }

   return kSuccess;
}

int RemoteSyncJsonProxyInterface::handleTimestampDelta(const rapidjson::Value& functionObjectVal)
{
   StackLog(<< "RemoteSyncJsonProxyInterface::handleTimestampDelta(): handler list: " << mAppHandlers.size());
   SessionHandle sessionHandle = -1;
   OnTimestampDeltaEvent event;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(event));

   std::map<SessionHandle, RemoteSyncHandler*>::iterator i = mAppHandlers.find(sessionHandle);
   if (i != mAppHandlers.end())
   {
      postCallback(makeFpCommand(RemoteSyncHandler::onTimestampDelta, i->second, sessionHandle, event));
   }

   return kSuccess;
}
   
#ifdef CPCAPI2_AUTO_TEST
   
AutoTestReadCallback* RemoteSyncJsonProxyInterface::process_test(int timeout)
{
   resip::ReadCallbackBase* rcb = mCallbacks.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0, 0));
   }
   return NULL;
}
   
#endif
   
}

}

#endif
