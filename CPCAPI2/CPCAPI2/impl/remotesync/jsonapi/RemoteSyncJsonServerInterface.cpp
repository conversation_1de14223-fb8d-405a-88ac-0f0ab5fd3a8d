#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1) && (CPCAPI2_BRAND_JSON_API_SERVER_MODULE == 1)

#include "RemoteSyncJsonServerInterface.h"
#include "remotesync/SyncManagerInterface.h"
#include "remotesync/commands/Command.h"
#include "phone/PhoneInterface.h"
#include "jsonapi/JsonApiServer.h"
#include "jsonapi/JsonApiServerInterface.h"
#include "json/JsonHelper.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

#include "util/cpc_logger.h"

// TODO: (kfritzke) The serialization for some of these events are different than before ex. event name

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_SYNC
#define JSON_MODULE "RemoteSyncJsonProxy"

namespace CPCAPI2
{

namespace RemoteSync
{

RemoteSyncJsonServerInterface::RemoteSyncJsonServerInterface(Phone* phone) :
mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   //RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(phone);
   //SyncManagerInterface* syncIf = dynamic_cast<SyncManagerInterface*>(syncMgr);
   // syncIf->addSdkObserver(this);

   mFunctionMap["setHandler"] = std::bind(&RemoteSyncJsonServerInterface::handleSetHandler, this, std::placeholders::_1);
   mFunctionMap["create"] = std::bind(&RemoteSyncJsonServerInterface::handleCreate, this, std::placeholders::_1);
   mFunctionMap["configureSettings"] = std::bind(&RemoteSyncJsonServerInterface::handleConfigureSettings, this, std::placeholders::_1);
   mFunctionMap["connect"] = std::bind(&RemoteSyncJsonServerInterface::handleConnect, this, std::placeholders::_1);
   mFunctionMap["disconnect"] = std::bind(&RemoteSyncJsonServerInterface::handleDisconnect, this, std::placeholders::_1);
   mFunctionMap["setAccounts"] = std::bind(&RemoteSyncJsonServerInterface::handleSetAccounts, this, std::placeholders::_1);
   mFunctionMap["syncItem"] = std::bind(&RemoteSyncJsonServerInterface::handleSyncItem, this, std::placeholders::_1);
   mFunctionMap["syncItems"] = std::bind(&RemoteSyncJsonServerInterface::handleSyncItems, this, std::placeholders::_1);
   mFunctionMap["fetchRangeRevision"] = std::bind(&RemoteSyncJsonServerInterface::handleFetchRangeRevision, this, std::placeholders::_1);
   mFunctionMap["fetchRangeCreatedTime"] = std::bind(&RemoteSyncJsonServerInterface::handleFetchRangeCreatedTime, this, std::placeholders::_1);
   mFunctionMap["fetchConversationsByTime"] = std::bind(&RemoteSyncJsonServerInterface::handleFetchConversationsByTime, this, std::placeholders::_1);
   mFunctionMap["fetchConversationsById"] = std::bind(&RemoteSyncJsonServerInterface::handleFetchConversationsById, this, std::placeholders::_1);
   mFunctionMap["updateConversation"] = std::bind(&RemoteSyncJsonServerInterface::handleUpdateConversation, this, std::placeholders::_1);
   mFunctionMap["getMessageCount"] = std::bind(&RemoteSyncJsonServerInterface::handleGetMessageCount, this, std::placeholders::_1);
   mFunctionMap["updateItem"] = std::bind(&RemoteSyncJsonServerInterface::handleUpdateItem, this, std::placeholders::_1);
   mFunctionMap["updateItems"] = std::bind(&RemoteSyncJsonServerInterface::handleUpdateItems, this, std::placeholders::_1);

   mTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(phone);
}

RemoteSyncJsonServerInterface::~RemoteSyncJsonServerInterface()
{
}

void RemoteSyncJsonServerInterface::Release()
{
}

void RemoteSyncJsonServerInterface::post(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void RemoteSyncJsonServerInterface::execute(resip::ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().execute(f);
}

int RemoteSyncJsonServerInterface::processIncoming(const CPCAPI2::JsonApi::JsonApiRequestInfo& conn, const std::shared_ptr<rapidjson::Document>& request)
{
   post(resip::resip_bind(&RemoteSyncJsonServerInterface::processIncomingImpl, this, conn, request));
   return kSuccess;
}

void RemoteSyncJsonServerInterface::processIncomingImpl(CPCAPI2::JsonApi::JsonApiRequestInfo conn, const std::shared_ptr<rapidjson::Document>& request)
{
   const rapidjson::Value& functionObjectVal = (*request)["functionObject"];
   const char* funcName = functionObjectVal["functionName"].GetString();

   FunctionMap::iterator it = mFunctionMap.find(funcName);
   if (it != mFunctionMap.end())
   {
      it->second(functionObjectVal);
   }
}

int RemoteSyncJsonServerInterface::handleSetHandler(const rapidjson::Value& functionObjectVal)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   DebugLog(<< "RemoteSyncJsonServerInterface::handleSetHandler(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   // TODO: Verify the setHandle vs handle created from calling create, generated by client sdk or cloud sdk
   RemoteSync::SessionHandle handle;
   JsonDeserialize(functionObjectVal, "session", handle);
   syncMgr->setHandler(handle, (RemoteSyncHandler*)0xDEADBEFF); // note: this ends in FF, meaning that we don't want callbacks at all
   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleCreate(const rapidjson::Value& functionObjectVal)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   DebugLog(<< "RemoteSyncJsonServerInterface::handleCreate(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   // TODO: Verify the setHandle vs handle created from calling create, generated by client sdk or cloud sdk
   RemoteSync::SessionHandle sessionHandle = dynamic_cast<SyncManagerInterface*>(syncMgr)->create();
   syncMgr->setHandler(sessionHandle, (RemoteSyncHandler*)this);

   JsonFunctionCall(mTransport, "onCreateResult", JSON_VALUE(sessionHandle));

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleConfigureSettings(const rapidjson::Value& functionObjectVal)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   DebugLog(<< "RemoteSyncJsonServerInterface::handleConfigureSettings(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle;
   RemoteSyncSettings settings;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(settings));

   syncMgr->configureSettings(sessionHandle, settings);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleConnect(const rapidjson::Value& functionObjectVal)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   DebugLog(<< "RemoteSyncJsonServerInterface::handleConnect(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle;
   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle));

   syncMgr->connect(sessionHandle);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleDisconnect(const rapidjson::Value& functionObjectVal)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);
   DebugLog(<< "RemoteSyncJsonServerInterface::handleDisconnect(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle;
   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle));

   syncMgr->disconnect(sessionHandle);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleSetAccounts(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleSetAccounts(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   cpc::vector<cpc::string> accounts;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accounts));

   syncMgr->setAccounts(requestHandle, sessionHandle, accounts);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleSyncItem(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleSyncItem(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   RemoteSyncItem syncItem;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(syncItem));

   syncMgr->syncItem(requestHandle, sessionHandle, syncItem);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleSyncItems(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleSyncItems(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   cpc::vector<RemoteSyncItem> syncItems;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(syncItems));

   syncMgr->syncItems(requestHandle, sessionHandle, syncItems);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleFetchRangeRevision(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleFetchRangeRevision(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   RemoteSync::Revision lowestRevision = 0;
   RemoteSync::Revision highestRevision = 0;
   cpc::vector<RemoteSyncItem::ItemType> itemTypes;
   cpc::string conversationID("");
   cpc::string account("");
   bool includeDeleted(false);
   int count(0);
   int offset(0);
   bool ascending(false);

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(lowestRevision), JSON_VALUE(highestRevision), JSON_VALUE(itemTypes),
      JSON_VALUE(conversationID), JSON_VALUE(account), JSON_VALUE(includeDeleted), JSON_VALUE(count), JSON_VALUE(offset), JSON_VALUE(ascending));

   syncMgr->fetchRangeRevision(requestHandle, sessionHandle, lowestRevision, highestRevision, itemTypes, conversationID, account, includeDeleted, count, offset, ascending);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleFetchRangeCreatedTime(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleFetchRangeCreatedTime(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   int64_t lowestCreatedTime(0);
   int64_t highestCreatedTime(0);
   cpc::vector<RemoteSyncItem::ItemType> itemTypes;
   cpc::string conversationID("");
   cpc::string account("");
   bool includeDeleted(false);
   int count(0);
   int offset(0);
   bool ascending(false);

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(lowestCreatedTime), JSON_VALUE(highestCreatedTime), JSON_VALUE(itemTypes),
      JSON_VALUE(conversationID), JSON_VALUE(account), JSON_VALUE(includeDeleted), JSON_VALUE(count), JSON_VALUE(offset), JSON_VALUE(ascending));

   syncMgr->fetchRangeCreatedTime(requestHandle, sessionHandle, lowestCreatedTime, highestCreatedTime, itemTypes, conversationID, account, includeDeleted, count, offset, ascending);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleFetchConversationsByTime(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleFetchConversationsByTime(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   int64_t lowestClientCreatedTime(0);
   int64_t highestClientCreatedTime(0);
   int count(0);
   int offset(0);

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(lowestClientCreatedTime), JSON_VALUE(highestClientCreatedTime), JSON_VALUE(count), JSON_VALUE(offset));

   syncMgr->fetchConversations(requestHandle, sessionHandle, lowestClientCreatedTime, highestClientCreatedTime, count, offset);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleFetchConversationsById(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleFetchConversationsById(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   cpc::vector<cpc::string> conversationIDs;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(conversationIDs));

   syncMgr->fetchConversations(requestHandle, sessionHandle, conversationIDs);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleUpdateConversation(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleUpdateConversation(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   cpc::string accountID("");
   cpc::string conversationID("");
   int64_t highestClientCreatedTime(0);
   bool setItemsRead(false);
   bool setItemsDeleted(false);
   bool setItemsUserDeleted(false);

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accountID), JSON_VALUE(conversationID), JSON_VALUE(highestClientCreatedTime), JSON_VALUE(setItemsRead), JSON_VALUE(setItemsDeleted), JSON_VALUE(setItemsUserDeleted));

   syncMgr->updateConversation(requestHandle, sessionHandle, accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleGetMessageCount(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleGetMessageCount(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   cpc::string accountID("");
   cpc::vector<RemoteSyncItem::ItemType> itemTypes;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accountID), JSON_VALUE(itemTypes));

   syncMgr->getMessageCount(requestHandle, sessionHandle, accountID, itemTypes);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleUpdateItem(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleUpdateItem(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   int64_t serverID(0);
   cpc::string clientID("");
   cpc::string originalID("");
   cpc::string stableID("");
   bool itemRead(false);
   bool itemDeleted(false);
   bool itemEdited(false);
   bool itemUserDeleted(false);
   int itemState(0);
   int callDuration(0);
   int64_t deliveryTimestamp(0);
   int64_t readTimestamp(0);

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(serverID), JSON_VALUE(clientID), JSON_VALUE(originalID), JSON_VALUE(stableID), JSON_VALUE(itemRead), JSON_VALUE(itemDeleted), JSON_VALUE(itemEdited), JSON_VALUE(itemUserDeleted), JSON_VALUE(itemState),
      JSON_VALUE(callDuration), JSON_VALUE(deliveryTimestamp), JSON_VALUE(readTimestamp));

   syncMgr->updateItem(requestHandle, sessionHandle, serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleSendReaction(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleSendReaction(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   RemoteSyncReaction reaction;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(reaction));

   syncMgr->sendReaction(requestHandle, sessionHandle, reaction);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleFetchMessagesReactions(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleFetchMessagesReactions(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   bool ascending = false;
   int offset = 0;
   int count = 0;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(ascending), JSON_VALUE(offset), JSON_VALUE(count));

   syncMgr->fetchMessagesReactions(requestHandle, sessionHandle, ascending, offset, count);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::handleUpdateItems(const rapidjson::Value& functionObjectVal)
{
   SyncManagerInterface* syncMgr = dynamic_cast<SyncManagerInterface*>(RemoteSyncManager::getInterface(mPhone));
   DebugLog(<< "RemoteSyncJsonServerInterface::handleUpdateItems(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   RemoteSync::SessionHandle sessionHandle = 0;
   RemoteSync::RequestHandle requestHandle = 0;
   bool isRead(false);
   bool isDeleted(false);
   bool isEdited(false);
   int64_t readTimestamp(0);

   cpc::vector<cpc::string> accounts;
   cpc::vector<RemoteSyncItem::ItemType> itemTypes;
   cpc::vector<cpc::string> conversationIDs;
   cpc::vector<int64_t> serverIDs;

   JsonDeserialize(functionObjectVal, JSON_VALUE(sessionHandle), JSON_VALUE(requestHandle), JSON_VALUE(accounts), JSON_VALUE(itemTypes), JSON_VALUE(conversationIDs), JSON_VALUE(serverIDs), JSON_VALUE(isRead),
      JSON_VALUE(isDeleted), JSON_VALUE(isEdited), JSON_VALUE(readTimestamp));

   syncMgr->updateItems(requestHandle, sessionHandle, accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp);

   return kSuccess;
}

int RemoteSyncJsonServerInterface::onSetAccounts(const SessionHandle& sessionHandle, const SetAccountsEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onSetAccounts(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onSetAccounts", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onNotificationUpdate(const SessionHandle& sessionHandle, const NotificationUpdateEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onNotificationUpdate(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onNotificationUpdate", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onMessageReactions(const SessionHandle& sessionHandle, const MessageReactionsEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onMessageReactions(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onMessageReactions", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onFetchMessagesReactionsComplete(const SessionHandle& sessionHandle, const FetchMessagesReactionsCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onFetchMessagesReactionsComplete(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onFetchMessagesReactionsComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onSyncItemsComplete(const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onSyncItemsComplete(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onSyncItemsComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onUpdateItemComplete(const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onUpdateItemComplete(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onUpdateItemComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onFetchRangeComplete(const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onFetchRangeComplete(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onFetchRangeComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onFetchConversationsComplete(const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onFetchConversationsComplete(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onFetchConversationsComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onConversationUpdated(const SessionHandle& sessionHandle, const ConversationUpdatedEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onConversationUpdated(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onConversationUpdated", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onMessageCount(const SessionHandle& sessionHandle, const MessageCountEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onMessageCount(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onMessageCount", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onError(const SessionHandle& sessionHandle, const OnErrorEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onError(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onError", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onConnectionState(const SessionHandle& sessionHandle, const OnConnectionStateEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onConnectionState(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onConnectionState", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onTimestampDelta(const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onTimestampDelta(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);

   JsonFunctionCall(mTransport, "onTimestampDelta", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onConversationUpdated(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);
   JsonFunctionCall(mTransport, "onUpdateItemsComplete", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

int RemoteSyncJsonServerInterface::onItemsUpdated(const SessionHandle& sessionHandle, const ItemsUpdatedEvent& event)
{
   RemoteSyncManager* syncMgr = RemoteSyncManager::getInterface(mPhone);

   DebugLog(<< "RemoteSyncJsonServerInterface::onConversationUpdated(): " << this << " RemoteSyncManager: " << syncMgr << " mPhone: " << mPhone);
   JsonFunctionCall(mTransport, "onItemsUpdated", JSON_VALUE(sessionHandle), JSON_VALUE(event));
   return kSuccess;
}

void RemoteSyncJsonServerInterface::onEvent(resip::ReadCallbackBase* rcb)
{
   mPhone->getSdkModuleThread().post(rcb);
}

}

}

#endif
