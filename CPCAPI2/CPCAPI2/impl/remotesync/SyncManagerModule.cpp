#include "brand_branded.h"

#include <util/APILogger.h>
#include "interface/experimental/remotesync/RemoteSyncManager.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
#include "SyncManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
   namespace RemoteSync
   {
      RemoteSyncManager* RemoteSyncManager::getInterface( CPCAPI2::Phone* cpcPhone )
      {
         API_INVOKE( "cpcPhone: %d", cpcPhone );
#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
         PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
         return _GetInterface<SyncManagerInterface>(phone, "RemoteSyncManager");
#else
         return NULL;
#endif
      }

   }
}
