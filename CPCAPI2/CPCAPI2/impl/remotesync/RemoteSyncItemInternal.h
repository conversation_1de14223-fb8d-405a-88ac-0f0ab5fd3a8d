#pragma once
#ifndef __CPCAPI2_REMOTESYNCINTERNAL_SYNC_ITEM_H_
#define __CPCAPI2_REMOTESYNCINTERNAL_SYNC_ITEM_H_

#include <string>
#include <map>

#include <remotesync/RemoteSyncItem.h>

namespace CPCAPI2
{
   namespace RemoteSync
   {
      struct RemoteSyncItemInternal
      {
         // Static maps which are used for mapping strings to types
         static std::map< std::string, RemoteSyncItem::Source >   s_mapStringToSource;
         static std::map< RemoteSyncItem::Source, std::string >   s_mapSourceToString;
         static std::map< std::string, RemoteSyncItem::ItemType > s_mapStringToItemType;
         static std::map< RemoteSyncItem::ItemType, std::string > s_mapItemTypeToString;
      };
   }
}

#endif // __CPCAPI2_REMOTESYNCINTERNAL_SYNC_ITEM_H_
