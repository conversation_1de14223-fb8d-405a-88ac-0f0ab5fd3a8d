#include <brand_branded.h>

// Cannot be disabled via branding because it is referenced in the official
// implementation header file. Ideally this implementation (of the ctors)
// should be in the header file, however we have a problem with the
// RemoteSyncGroupChatItem and its methods (and the resulting inclusion of
// C runtime headers).
//
// TODO: Solve the RemoteSyncGroupChatItem problem.
//
// #if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

#include <remotesync/RemoteSyncTypes.h>
#include <remotesync/RemoteSyncItem.h>
#include <remotesync/RemoteSyncItemInternal.h>
#include <stdlib.h>

#include <stdint.h>
#include <string.h>
#include <ctype.h>

using namespace CPCAPI2::RemoteSync;

// Static initialization code for various maps
// ============================================================================
std::map< std::string, RemoteSyncItem::Source > init_map_string_to_source()
{
#define entry(x) { m[ #x ] = RemoteSyncItem::Source::x; }
   std::map< std::string, RemoteSyncItem::Source > m;
   entry(unknown);
   entry(original);
   entry(copy);
   return m;
#undef entry
}
std::map< std::string, RemoteSyncItem::Source > RemoteSyncItemInternal::s_mapStringToSource = init_map_string_to_source();


std::map< RemoteSyncItem::Source, std::string > init_map_source_to_string()
{
#define entry(x) { m[ RemoteSyncItem::Source::x ] = #x; }
   std::map< RemoteSyncItem::Source, std::string > m;
   entry(unknown);
   entry(original);
   entry(copy);
   return m;
#undef entry
}
std::map< RemoteSyncItem::Source, std::string > RemoteSyncItemInternal::s_mapSourceToString = init_map_source_to_string();


std::map< std::string, RemoteSyncItem::ItemType > init_map_string_to_item()
{
#define entry(x) { m[ #x ] = RemoteSyncItem::ItemType::x; }
   std::map< std::string, RemoteSyncItem::ItemType > m;
   entry(im);
   entry(chatinfo);
   entry(sms);
   entry(callhistory);
   entry(contact);
   return m;
#undef entry
}
std::map< std::string, RemoteSyncItem::ItemType > RemoteSyncItemInternal::s_mapStringToItemType = init_map_string_to_item();


std::map< RemoteSyncItem::ItemType, std::string > init_map_item_to_string()
{
#define entry(x) { m[ RemoteSyncItem::ItemType::x ] = #x; }
   std::map< RemoteSyncItem::ItemType, std::string > m;
   entry(im);
   entry(chatinfo);
   entry(sms);
   entry(callhistory);
   entry(contact);
   return m;
#undef entry
}
std::map< RemoteSyncItem::ItemType, std::string > RemoteSyncItemInternal::s_mapItemTypeToString = init_map_item_to_string();

RemoteSyncItem::RemoteSyncItem() :
   source(Source_null),
   conversationID(NULL),
   contentType(NULL),
   content(NULL),
   clientTimestamp(0),
   deliveryTimestamp(0),
   readTimestamp(0),
   itemRead(false),
   itemDeleted(false),
   itemEdited(false),
   itemUserDeleted(false)
{
}

RemoteSyncItem::RemoteSyncItem(RemoteSyncItem const &other) :
   serverID(other.serverID),
   clientID(other.clientID),
   account(other.account),
   source(other.source),
   itemType(other.itemType),
   itemRead(other.itemRead),
   itemDeleted(other.itemDeleted),
   itemEdited(other.itemEdited),
   itemUserDeleted(other.itemUserDeleted),
   reactions(other.reactions),
   originalID(other.originalID),
   stableID(other.stableID),
   state(other.state),
   clientTimestamp(other.clientTimestamp),
   deliveryTimestamp(other.deliveryTimestamp),
   readTimestamp(other.readTimestamp),
   from(other.from),
   to(other.to),
   conversationID(other.conversationID),
   contentType(other.contentType),
   content(other.content),
   uniqueID(other.uniqueID),
   callHistory(other.callHistory)
{
}

RemoteSyncItem::RemoteSyncItem(ServerID serverId,
      cpc::string const & clientId,
      cpc::string const & account,
      Source source,
      cpc::string const & from,
      cpc::string const & to,
      cpc::string const & contentType,
      cpc::string const & message,
      cpc::string const & threadId,
      cpc::string const & uniqueID,
      cpc::string const & originalID,
      cpc::string const & stableID,
      bool isOutbound,
      bool isRead,
      bool isDeleted,
      bool isEdited,
      bool isUserDeleted,
      bool isDelivered,      
      cpc::vector<RemoteSyncReaction> const & reactions,
      int64_t messageTimestamp,
      int64_t deliveryTimestamp,
      int64_t readTimestamp) :
   serverID(serverId),
   clientID(clientId),
   account(account),
   source(source),
   itemType(im),
   itemRead(isRead),
   itemDeleted(isDeleted),
   itemEdited(isEdited),
   itemUserDeleted(isUserDeleted),
   reactions(reactions),
   originalID(originalID),
   stableID(stableID),
   clientTimestamp(messageTimestamp),
   deliveryTimestamp(deliveryTimestamp),
   readTimestamp(readTimestamp),
   from(from),
   to(to),
   conversationID(threadId),
   contentType(contentType),
   content(message),
   uniqueID(uniqueID)
{
   this->state = isOutbound ? 0 : ITEM_STATE_MASK_INBOUND;
   if (isDelivered)
      setStateDeliveryBits(DeliveryStatusDelivered, this->state);
}

RemoteSyncItem::RemoteSyncItem(ServerID serverId,
      cpc::string const & clientId,
      cpc::string const & account,
      Source source,
      cpc::string const & from,
      cpc::string const & to,
      cpc::string const & contentType,
      cpc::string const & message,
      cpc::string const & threadId,
      cpc::string const & uniqueID,
      cpc::string const & originalID,
      cpc::string const & stableID,
      bool isOutbound,
      bool isRead,
      bool isDeleted,
      bool isEdited,
      bool isUserDeleted,
      cpc::vector<RemoteSyncReaction> const & reactions,
      DeliveryStatus deliveryStatus,
      int64_t messageTimestamp,
      int64_t deliveryTimestamp,
      int64_t readTimestamp) :
   serverID(serverId),
   clientID(clientId),
   account(account),
   source(source),
   itemType(im),
   itemRead(isRead),
   itemDeleted(isDeleted),
   itemEdited(isEdited),
   itemUserDeleted(isUserDeleted),
   reactions(reactions),
   originalID(originalID),
   stableID(stableID),
   clientTimestamp(messageTimestamp),
   deliveryTimestamp(deliveryTimestamp),
   readTimestamp(readTimestamp),
   from(from),
   to(to),
   conversationID(threadId),
   contentType(contentType),
   content(message),
   uniqueID(uniqueID)
{
   this->state = isOutbound ? 0 : ITEM_STATE_MASK_INBOUND;
   setStateDeliveryBits(deliveryStatus, this->state);
}

/**
* ctor variant taking ivars directly
*/
RemoteSyncItem::RemoteSyncItem(ServerID serverId,
      cpc::string const & clientId,
      cpc::string const & account,
      Source source,
      ItemType itemType,
      cpc::string const & from,
      cpc::string const & to,
      cpc::string const & contentType,
      cpc::string const & content,
      cpc::string const & conversationID,
      cpc::string const & uniqueID,
      cpc::string const & originalID,
      cpc::string const & stableID,
      bool isRead,
      bool isDeleted,
      bool isEdited,
      bool isUserDeleted,
      cpc::vector<RemoteSyncReaction> const & reactions,
      int  state,
      int64_t messageTimestamp,
      int64_t deliveryTimestamp,
      int64_t readTimestamp) :
   serverID(serverId),
   clientID(clientId),
   account(account),
   source(source),
   itemType(itemType),
   itemRead(isRead),
   itemDeleted(isDeleted),
   itemEdited(isEdited),
   itemUserDeleted(isUserDeleted),
   reactions(reactions),
   originalID(originalID),
   stableID(stableID),
   state(state),
   clientTimestamp(messageTimestamp),
   deliveryTimestamp(deliveryTimestamp),
   readTimestamp(readTimestamp),
   from(from),
   to(to),
   conversationID(conversationID),
   contentType(contentType),
   content(content),
   uniqueID(uniqueID)
{
}

RemoteSyncItem::RemoteSyncItem(ServerID serverId,
   cpc::string const & account,
   Source source,
   ItemType itemType,
   cpc::string const & from,
   cpc::string const & to,
   cpc::string const & conversationID,
   cpc::string const & uniqueID,
   cpc::string const & originalID,
   cpc::string const & stableID,
   bool isRead,
   bool isDeleted,
   bool isEdited,
   bool isUserDeleted,
   cpc::vector<RemoteSyncReaction> const & reactions,
   int  state,
   int64_t messageTimestamp,
   cpc::string remoteName,
   cpc::string associatedUri,
   cpc::string associatedNumber,
   int callDuration,
   cpc::string primaryDeviceHash,
   cpc::string primaryDevicePlatform,
   cpc::string primaryDeviceName,
   int64_t deliveryTimestamp,
   int64_t readTimestamp) :
   serverID(serverId),
   account(account),
   source(source),
   itemType(itemType),
   itemRead(isRead),
   itemDeleted(isDeleted),
   itemEdited(isEdited),
   itemUserDeleted(isUserDeleted),
   reactions(reactions),
   originalID(originalID),
   stableID(stableID),
   state(state),
   clientTimestamp(messageTimestamp),
   deliveryTimestamp(deliveryTimestamp),
   readTimestamp(readTimestamp),
   from(from),
   to(to),
   conversationID(conversationID),
   uniqueID(uniqueID),
   callHistory(RemoteSyncCallHistory(remoteName, associatedUri, associatedNumber, callDuration, primaryDeviceHash, primaryDevicePlatform, primaryDeviceName))
{}

void RemoteSyncItem::setStateDeliveryBits(DeliveryStatus deliveryStatus, int& itemState)
{
   switch (deliveryStatus)
   {
   case DeliveryStatusError:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_ERROR << 1);
      break;
   case DeliveryStatusQueued:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_QUEUED << 1);
      break;
   case DeliveryStatusDelivered:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_DELIVERED << 1);
      break;
   case DeliveryStatusReceived:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_RECEIVED << 1);
      break;
   case DeliveryStatusRead:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_READ << 1);
      break;
   default:
      itemState |= (ITEM_STATE_DELIVERY_STATUS_UNKNOWN << 1);
      break;
   }
}

RemoteSyncItem::DeliveryStatus RemoteSyncItem::getStateDeliveryStatus(int itemState)
{
   switch ((itemState & ITEM_STATE_MASK_DELIVERY_STATUS) >> 1)
   {
   case ITEM_STATE_DELIVERY_STATUS_ERROR:
      return DeliveryStatusError;
   case ITEM_STATE_DELIVERY_STATUS_QUEUED:
      return DeliveryStatusQueued;
   case ITEM_STATE_DELIVERY_STATUS_DELIVERED:
      return DeliveryStatusDelivered;
   case ITEM_STATE_DELIVERY_STATUS_RECEIVED:
      return DeliveryStatusReceived;
   case ITEM_STATE_DELIVERY_STATUS_READ:
      return DeliveryStatusRead;
   default:
      return DeliveryStatusUnknown;
   }
}

// ===============================================================================
// RemoteSyncGroupChatItem
// ===============================================================================

static cpc::string& rtrim( cpc::string& s )
{
   // NB: rewritten this way because cpc::string does not contain
   // "erase"
   size_t size = s.size();
   if (size == 0)
      return s;

   const char *buf = s.c_str();
   size_t i = size - 1;
   do
   {
      if (!isspace( buf[ i ] ))
         break;

      if (i == 0)
         break;

      --i;
   } while (true);

   // i is either pointing at the start, or at the last
   // non-space character.
   if (!isspace( buf[ i ] ))
      s.resize( i + 1 );
   else
      s.resize( 0 );

   return s;
}

static cpc::string& ltrim( cpc::string& s )
{
   // NB: rewritten this way because cpc::string does not contain "erase"
   size_t size = s.size();
   if (size == 0)
      return s;

   const char *buf = s.c_str();
   size_t i;
   for (i = 0 ; i < size ; ++i)
   {
      if (!isspace( buf[ i ] ))
         break;
   }

   // i is either pointing at the end, or at the first
   // non-space character.
   if (i < size)
      s = s.substr( i );

   return s;
}

static cpc::string& trim( cpc::string& s )
{
   return ltrim( rtrim( s ) );
}


cpc::vector<cpc::string> RemoteSyncGroupChatItem::getOccupants()
{
   // Convert the content CSV into a list of occupants.
   cpc::vector<cpc::string> result;
#if _WIN32
   char *temp = _strdup( content.c_str() );
#else
   char *temp = strdup( content.c_str() );
#endif
   char *token   = NULL;
   char *context = NULL;

   // Use strtok_s/r ( no getline in cpc:: )
#if _WIN32
   token = strtok_s( temp, ",", &context );
#else
   token = strtok_r( temp, ",", &context );
#endif
   while( token != NULL )
   {
      cpc::string substr( token );
      trim( substr );
      if (!substr.empty())
         result.push_back( substr );

#if _WIN32
      token = strtok_s( NULL, ",", &context );
#else
      token = strtok_r( NULL, ",", &context );
#endif
   }

   free( temp ); // strdup uses malloc
   return result;
}

void RemoteSyncGroupChatItem::setOccupants( cpc::vector<cpc::string> occupants )
{
   // Convert the occupants list into a CSV.

   if (occupants.empty())
   {
      content = "";
   }
   else
   {
      cpc::string sb = occupants[ 0 ];

      for (size_t i = 1; i < occupants.size(); i++)
      {
         sb += "," + occupants[ i ];
      }
      content = sb;
   }
}

// ===============================================================================
// RemoteSyncCallHistoryItem
// ===============================================================================

RemoteSyncCallHistory::RemoteSyncCallHistory() :
   remoteName(NULL),
   associatedUri(NULL),
   associatedNumber(NULL),
   callDuration(0),
   primaryDeviceHash(NULL),
   primaryDevicePlatform(NULL),
   primaryDeviceName(NULL),
   statusCode(0)
{}

RemoteSyncCallHistory::RemoteSyncCallHistory(RemoteSyncCallHistory const &other) :
   remoteName(other.remoteName),
   associatedUri(other.associatedUri),
   associatedNumber(other.associatedNumber),
   callDuration(other.callDuration),
   primaryDeviceHash(other.primaryDeviceHash),
   primaryDevicePlatform(other.primaryDevicePlatform),
   primaryDeviceName(other.primaryDeviceName),
   statusCode(other.statusCode)
{}

RemoteSyncCallHistory::RemoteSyncCallHistory(
   cpc::string remoteName,
   cpc::string associatedUri,
   cpc::string associatedNumber,
   int callDuration,
   cpc::string primaryDeviceHash,
   cpc::string primaryDevicePlatform,
   cpc::string primaryDeviceName,
   int statusCode):
   remoteName(remoteName),
   associatedUri(associatedUri),
   associatedNumber(associatedNumber),
   callDuration(callDuration),
   primaryDeviceHash(primaryDeviceHash),
   primaryDevicePlatform(primaryDevicePlatform),
   primaryDeviceName(primaryDeviceName),
   statusCode(statusCode)

{}

void RemoteSyncCallHistory::setCallHistoryTypeBits(RemoteSyncCallHistory::CallHistoryType callHistoryType, int& itemState)
{
   switch (callHistoryType)
   {
   case CallHistoryTypeDialed:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_DIALED;
      break;
   case CallHistoryTypeAnswered:
      itemState |= RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_ANSWERED;
      break;
   case CallHistoryTypeMissed:
      itemState |= RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_MISSED;
      break;
   case CallHistoryTypeForwarded:
      itemState |= RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_FORWARDED;
      break;
   case CallHistoryTypeCompletedElsewhere:
      itemState |= RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_COMPLETED_ELSEWHERE;
      break;
   case CallHistoryTypeBlocked:
      itemState |= RemoteSyncItem::ITEM_STATE_MASK_INBOUND;
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_BLOCKED;
      break;
   case CallHistoryTypeReserved0:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_RESERVED_0;
      break;
   case CallHistoryTypeReserved1:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_RESERVED_1;
      break;
   case CallHistoryTypeReserved2:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_RESERVED_2;
      break;
   case CallHistoryTypeConference:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE;
      break;
   case CallHistoryTypeConferenceHosted:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE_HOSTED;
      break;
   default:
      itemState |= ITEM_STATE_CALLHISTORY_TYPE_UNKNOWN;
      break;
   }
}

RemoteSyncCallHistory::CallHistoryType RemoteSyncCallHistory::getCallHistoryType(int itemState)
{
   switch (itemState & ITEM_STATE_MASK_CALLHISTORY_TYPE)
   {
   case ITEM_STATE_CALLHISTORY_TYPE_DIALED:
      return CallHistoryTypeDialed;
   case ITEM_STATE_CALLHISTORY_TYPE_ANSWERED:
      return CallHistoryTypeAnswered;
   case ITEM_STATE_CALLHISTORY_TYPE_MISSED:
      return CallHistoryTypeMissed;
   case ITEM_STATE_CALLHISTORY_TYPE_FORWARDED:
      return CallHistoryTypeForwarded;
   case ITEM_STATE_CALLHISTORY_TYPE_COMPLETED_ELSEWHERE:
      return CallHistoryTypeCompletedElsewhere;
   case ITEM_STATE_CALLHISTORY_TYPE_BLOCKED:
      return CallHistoryTypeBlocked;
   case ITEM_STATE_CALLHISTORY_TYPE_RESERVED_0:
      return CallHistoryTypeReserved0;
   case ITEM_STATE_CALLHISTORY_TYPE_RESERVED_1:
      return CallHistoryTypeReserved1;
   case ITEM_STATE_CALLHISTORY_TYPE_RESERVED_2:
      return CallHistoryTypeReserved2;
   case ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE:
      return CallHistoryTypeConference;
   case ITEM_STATE_CALLHISTORY_TYPE_CONFERENCE_HOSTED:
      return CallHistoryTypeConferenceHosted;
   default:
      return CallHistoryTypeUnknown;
   }
}

void RemoteSyncCallHistory::setCallFeatureUtilizationBits(RemoteSyncCallHistory::CallFeatureUtilization callFeatureUtilization, int& itemState)
{
   switch (callFeatureUtilization)
   {
   case CallFeatureUtilizationVideo:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_VIDEO;
      break;
   case CallFeatureUtilizationLocalMixer:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_LOCAL_MIXER;
      break;
   case CallFeatureUtilizationAudioRecording:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_AUDIO_RECORDING;
      break;
   case CallFeatureUtilizationScreenshareHost:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_HOST;
      break;
   case CallFeatureUtilizationScreenshareViewer:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_VIEWER;
      break;
   default:
      itemState |= ITEM_STATE_MASK_FEATURE_UTILIZATION_NONE;
      break;
   }
}

bool RemoteSyncCallHistory::hasCallFeatureUtilization(CallFeatureUtilization callFeatureUtilization, int itemState)
{
   switch (callFeatureUtilization)
   {
   case CallFeatureUtilizationVideo:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_VIDEO) == ITEM_STATE_MASK_FEATURE_UTILIZATION_VIDEO);
   case CallFeatureUtilizationLocalMixer:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_LOCAL_MIXER) == ITEM_STATE_MASK_FEATURE_UTILIZATION_LOCAL_MIXER);
   case CallFeatureUtilizationAudioRecording:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_AUDIO_RECORDING) == ITEM_STATE_MASK_FEATURE_UTILIZATION_AUDIO_RECORDING);
   case CallFeatureUtilizationScreenshareHost:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_HOST) == ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_HOST);
   case CallFeatureUtilizationScreenshareViewer:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_VIEWER) == ITEM_STATE_MASK_FEATURE_UTILIZATION_SCREENSHARE_VIEWER);
   default:
      return ((itemState & ITEM_STATE_MASK_FEATURE_UTILIZATION_NONE) == ITEM_STATE_MASK_FEATURE_UTILIZATION_NONE);
   }
}

// ===============================================================================
// RemoteSyncReactionItem
// ===============================================================================

RemoteSyncReaction::RemoteSyncReaction() :
   created_time(0),
   rev(0),
   server_id(0)
{}

RemoteSyncReaction::RemoteSyncReaction(RemoteSyncReaction const &other) :
   created_time(other.created_time),
   rev(other.rev),
   address(other.address),
   server_id(other.server_id),
   value(other.value)
{
   
}

RemoteSyncReaction::RemoteSyncReaction(
   int64_t created_time,
   int64_t rev,
   cpc::string address,
   int64_t server_id,
   cpc::string value):
   created_time(created_time),
   rev(rev),
   address(address),
   server_id(server_id),
   value(value)
{
   created_time = created_time;
}

// #endif
