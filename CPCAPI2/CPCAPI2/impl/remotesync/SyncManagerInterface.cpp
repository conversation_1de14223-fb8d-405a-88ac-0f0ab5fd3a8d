#include "brand_branded.h"

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)

#include "SyncManagerInterface.h"
#include "SyncManagerImpl.h"
#include "phone/PhoneInterface.h"
#include <sstream>
#include <inttypes.h>

#include <util/DumFpCommand.h>
#include <util/APILogger.h>

#include "log/LocalLogger.h"
#include "util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::REMOTE_SYNC

using namespace CPCAPI2::RemoteSync;

std::atomic< RequestHandle > SyncManagerInterface::s_RequestHandle( 0 );
std::atomic< SessionHandle > SyncManagerInterface::s_SessionHandle( 0 );

SessionHandle SyncManagerInterface::nextSessionHandle()
{
   return s_SessionHandle.fetch_add( 1 );
}

RequestHandle SyncManagerInterface::nextRequestHandle()
{
   return s_RequestHandle.fetch_add( 1 );
}

#ifdef CPCAPI2_AUTO_TEST
void RemoteSyncManagerInternal::resetRequestHandleCount()
{
   SyncManagerInterface::s_RequestHandle.exchange( 0 );
}
#endif

SyncManagerInterface::SyncManagerInterface(CPCAPI2::Phone* phone)
   : m_IOService(dynamic_cast<PhoneInterface*>(phone)->getAsioIoService()), m_Shutdown( false ), m_CbHook( NULL ), m_Context( NULL ), m_Phone(phone)
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(m_Phone))->addSdkObserver(this);
}

SyncManagerInterface::~SyncManagerInterface()
{
   ((NetworkChangeManagerInterface*)NetworkChangeManager::getInterface(m_Phone))->removeSdkObserver(this);

   m_Shutdown = true;

   // Free everything in the session map
   std::map< SessionHandle, SyncManagerImpl* >::const_iterator iter = m_SessionMap.begin();
   for (; iter != m_SessionMap.end() ; ++iter)
   {
      // session will take care of calling delete on itself
      iter->second->destroy();
   }
   m_SessionMap.clear();
}

void SyncManagerInterface::addSdkObserver(RemoteSyncHandler* handler)
{
   m_IOService.post(std::bind(&SyncManagerInterface::addSdkObserverImpl, this, handler));
}

void SyncManagerInterface::addSdkObserverImpl(RemoteSyncHandler* observer)
{
   StackLog( << "SyncManagerInterface::addSdkObserver(): " << this << " observer: " << observer );
   mSdkObservers.insert(observer);

   std::map< SessionHandle, SyncManagerImpl* >::const_iterator iter = m_SessionMap.begin();
   for (; iter != m_SessionMap.end(); ++iter)
   {
      iter->second->addSdkObserver(observer);
   }
}

void SyncManagerInterface::removeSdkObserver(RemoteSyncHandler* observer)
{   
   m_IOService.post(std::bind(&SyncManagerInterface::removeSdkObserverImpl, this, observer));
}

void SyncManagerInterface::removeSdkObserverImpl(RemoteSyncHandler* observer)
{
   StackLog(<< "SyncManagerInterface::removeSdkObserver(): " << this << " observer: " << observer);
   mSdkObservers.erase(observer);
}

void SyncManagerInterface::Release()
{
   delete this; // suicide (called from resip/DUM thread already)
}

SyncManagerImpl* SyncManagerInterface::getSessionImpl( const SessionHandle& hSession )
{
   SyncManagerImpl* result( NULL );
   
   std::map< SessionHandle, SyncManagerImpl* >::const_iterator iter = m_SessionMap.find( hSession );
   if( iter != m_SessionMap.end() )
      result = iter->second;

   return result;
}

// RemoteSyncManager Implementation
int SyncManagerInterface::setHandler( const SessionHandle& hSession, RemoteSyncHandler * handler )
{
   API_INVOKE( "hSession: %d", hSession );
   int result = kError;
   if( handler != NULL )
   {
      m_IOService.post( std::bind( &SyncManagerInterface::setHandlerImpl, this, hSession, handler ));
      result = kSuccess;
   }
   else // handler == NULL
   {
      // Special case that needs to block the caller until operation is complete.

      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor( SyncManagerInterface *parent, const SessionHandle& hChannel, RemoteSyncHandler*& handler, std::mutex& mutex, std::condition_variable& cvar, int& result )
            : mParent( parent ), mhChannel( hChannel ), mHandler( handler ), mMutex( mutex ), mCVar( cvar ), mResult( result ) {}

         void operator()( void )
         {
            std::lock_guard< std::mutex > lock( mMutex );
            mResult = mParent->setHandlerImpl( mhChannel, mHandler );
            mCVar.notify_all();
         }

         SyncManagerInterface *mParent;
         const SessionHandle& mhChannel;
         RemoteSyncHandler*& mHandler;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
         int& mResult;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock< std::mutex > lock( mutex ); // acquires the mutex

         MyFunctor *func = new MyFunctor( this, hSession, handler, mutex, cvar, result );
         m_IOService.post( std::bind( &MyFunctor::operator(), func ));
         cvar.wait( lock ); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process( -1 );
   }
   return result;
}

int SyncManagerInterface::setHandlerImpl( const SessionHandle& hSession, RemoteSyncHandler * notificationHandler)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if (pSession != NULL)
   {
      return pSession->setHandler(notificationHandler);
   }

   return kError;
}

SessionHandle SyncManagerInterface::create( void )
{
   SessionHandle hSession = nextSessionHandle();
   DebugLog( << "SyncManagerInterface::create(): handle created: " << hSession );
   API_INVOKE( "returning hSession: %d", hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::createImpl, this, hSession ));
   return hSession;
}

int SyncManagerInterface::configureSettings( const SessionHandle& hSession, const struct RemoteSyncSettings& settings )
{
   API_INVOKE( "hSession: %d", hSession );
   DebugLog( << "Settings configured to: " << settings );
   m_IOService.post( std::bind( &SyncManagerInterface::configureSettingsImpl, this, hSession, settings ));
   return kSuccess;
}

int SyncManagerInterface::connect( const SessionHandle& hSession )
{
   API_INVOKE( "hSession: %d", hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::connectImpl, this, hSession ));
   return kSuccess;
}

int SyncManagerInterface::disconnect(const SessionHandle& hSession)
{
   API_INVOKE( "hSession: %d", hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::disconnectImpl, this, hSession ));
   return kSuccess;
}

int SyncManagerInterface::destroy(const SessionHandle& hSession)
{
   API_INVOKE( "hSession: %d", hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::destroyImpl, this, hSession ));
   return kSuccess;
}

RequestHandle SyncManagerInterface::setAccounts(const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts)
{
   RequestHandle hRequest = nextRequestHandle();
   setAccounts(hRequest, hSession, accounts);
   return hRequest;
}

void SyncManagerInterface::setAccounts(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::setAccountsImpl, this, hRequest, hSession, accounts ));
}

RequestHandle SyncManagerInterface::syncItem(const SessionHandle& hSession, const RemoteSyncItem& itemToSync)
{
   RequestHandle hRequest = nextRequestHandle();
   syncItem(hRequest, hSession, itemToSync);
   return hRequest;
}

void SyncManagerInterface::syncItem(const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncItem& itemToSync)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d, uniqueID: %s", hRequest, hSession, itemToSync.uniqueID.c_str() );
   m_IOService.post( std::bind( &SyncManagerInterface::syncItemImpl, this, hRequest, hSession, itemToSync ));
}

RequestHandle SyncManagerInterface::syncItems(const SessionHandle& hSession, const cpc::vector< RemoteSyncItem >& itemsToSync)
{
   RequestHandle hRequest = nextRequestHandle();
   syncItems(hRequest, hSession, itemsToSync);
   return hRequest;
}

void SyncManagerInterface::syncItems(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector< RemoteSyncItem >& itemsToSync)
{
   std::string idlist;
   idlist += "( ";
   for( cpc::vector< RemoteSyncItem >::const_iterator iter = itemsToSync.begin() ; iter != itemsToSync.end() ; ++iter )
   {
      if( iter->uniqueID.size() > 0 )
      {
         idlist += iter->uniqueID;
         idlist += " ";
      }
   }
   idlist += ")";

   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d, clientIDs: %s", hRequest, hSession, idlist.c_str() );
   m_IOService.post( std::bind( &SyncManagerInterface::syncItemsImpl, this, hRequest, hSession, itemsToSync ));
}

RequestHandle SyncManagerInterface::fetchRangeRevision(const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   RequestHandle hRequest = nextRequestHandle();
   fetchRangeRevision(hRequest, hSession, lowestRevision, highestRevision, itemTypes, conversationID, account, includeDeleted, count, offset, ascending);
   return hRequest;
}

void SyncManagerInterface::fetchRangeRevision(const RequestHandle& hRequest, const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   struct FetchRangeParams params;
   params.itemTypes      = itemTypes;
   params.conversationID = conversationID;
   params.account        = account;
   params.includeDeleted = includeDeleted;
   params.count          = count;
   params.offset         = offset;
   params.ascending      = ascending;

   m_IOService.post( std::bind( &SyncManagerInterface::fetchRangeRevisionImpl, this, hRequest, hSession, lowestRevision, highestRevision, params ));
}

RequestHandle SyncManagerInterface::fetchRangeCreatedTime(const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   RequestHandle hRequest = nextRequestHandle();
   fetchRangeCreatedTime(hRequest, hSession, lowestCreatedTime, highestCreatedTime, itemTypes, conversationID, account, includeDeleted, count, offset, ascending);
   return hRequest;
}

void SyncManagerInterface::fetchRangeCreatedTime(const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   struct FetchRangeParams params;
   params.itemTypes      = itemTypes;
   params.conversationID = conversationID;
   params.account        = account;
   params.includeDeleted = includeDeleted;
   params.count          = count;
   params.offset         = offset;
   params.ascending      = ascending;

   m_IOService.post( std::bind( &SyncManagerInterface::fetchRangeCreatedTimeImpl, this, hRequest, hSession, lowestCreatedTime, highestCreatedTime, params ));
}

RequestHandle SyncManagerInterface::fetchConversations(const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset)
{
   RequestHandle hRequest = nextRequestHandle();
   fetchConversations(hRequest, hSession, lowestClientCreatedTime, highestClientCreatedTime, count, offset);
   return hRequest;
}

void SyncManagerInterface::fetchConversations(const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post([=]() { fetchConversationsImpl(hRequest, hSession, lowestClientCreatedTime, highestClientCreatedTime, count, offset); });
}

RequestHandle SyncManagerInterface::fetchConversations(const SessionHandle& hSession, const cpc::vector<cpc::string>& conversationIDs)
{
   RequestHandle hRequest = nextRequestHandle();
   fetchConversations(hRequest, hSession, conversationIDs);
   return hRequest;
}

void SyncManagerInterface::fetchConversations(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& conversationIDs)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post([=]() { fetchConversationsImpl(hRequest, hSession, conversationIDs); });
}

RequestHandle SyncManagerInterface::updateConversation(const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   RequestHandle hRequest = nextRequestHandle();
   updateConversation(hRequest, hSession, accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted);
   return hRequest;
}

void SyncManagerInterface::updateConversation(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post(std::bind(&SyncManagerInterface::updateConversationImpl, this, hRequest, hSession, accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted));
}

RequestHandle SyncManagerInterface::getMessageCount(const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types)
{
   RequestHandle hRequest = nextRequestHandle();
   getMessageCount(hRequest, hSession, accountID, types);
   return hRequest;
}

void SyncManagerInterface::getMessageCount(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector<RemoteSyncItem::ItemType>& types)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post(std::bind(&SyncManagerInterface::getMessageCountImpl, this, hRequest, hSession, accountID, types));
}

RequestHandle SyncManagerInterface::updateItem(const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   RequestHandle hRequest = nextRequestHandle();
   updateItem(hRequest, hSession, serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp);
   return hRequest;
}

void SyncManagerInterface::updateItem(const RequestHandle& hRequest, const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::updateItemImpl, this, hRequest, hSession, serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp));
}

RequestHandle SyncManagerInterface::sendReaction(const SessionHandle& hSession, const RemoteSyncReaction& reaction)
{
   RequestHandle hRequest = nextRequestHandle();
   sendReaction(hRequest, hSession, reaction);
   return hRequest;
}

void SyncManagerInterface::sendReaction(const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncReaction& reaction)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::sendReactionImpl, this, hRequest, hSession, reaction));
}

RequestHandle SyncManagerInterface::fetchMessagesReactions(const SessionHandle& hSession, bool ascending, int offset, int count)
{
   RequestHandle hRequest = nextRequestHandle();
   fetchMessagesReactions(hRequest, hSession, ascending, offset, count);
   return hRequest;
}

void SyncManagerInterface::fetchMessagesReactions(const RequestHandle& hRequest, const SessionHandle& hSession, bool ascending, int offset, int count)
{
   API_INVOKE( "hRequest: %" PRId64 ", hSession: %d", hRequest, hSession );
   m_IOService.post( std::bind( &SyncManagerInterface::fetchMessagesReactionsImpl, this, hRequest, hSession, ascending, offset, count));
}

RequestHandle SyncManagerInterface::updateItems(const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   RequestHandle hRequest = nextRequestHandle();
   updateItems(hRequest, hSession, accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp);
   return hRequest;
}

void SyncManagerInterface::updateItems(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   API_INVOKE("hRequest: %" PRId64 ", hSession: %d", hRequest, hSession);
   m_IOService.post(std::bind(&SyncManagerInterface::updateItemsImpl, this, hRequest, hSession, accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp));
}

// NotificationService delegates

int SyncManagerInterface::createImpl( const SessionHandle& hSession )
{
   SyncManagerImpl *pImpl = new SyncManagerImpl( hSession, m_IOService, m_CallbackFifo, dynamic_cast<PhoneInterface*>(m_Phone)->getSslCipherOptions() );

   if( m_CbHook != NULL )
      pImpl->setCallbackHook(m_CbHook, m_Context);

   for (std::set<RemoteSyncHandler*>::iterator it = mSdkObservers.begin(), end = mSdkObservers.end(); it != end; ++it)
   {
      pImpl->addSdkObserver(*it);
   }

   m_SessionMap[ hSession ] = pImpl;
   return kSuccess;
}

int SyncManagerInterface::configureSettingsImpl( const SessionHandle& hSession, const struct RemoteSyncSettings& settings )
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->configureSettings( settings );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::connectImpl( const SessionHandle& hSession )
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->connect();

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::disconnectImpl(const SessionHandle& hSession)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->disconnect();

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::destroyImpl(const SessionHandle& hSession)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
   {
      m_SessionMap.erase(hSession);
      // pSession will take care of calling delete on itself
      return pSession->destroy();
   }
    
   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::setAccountsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->setAccounts( hRequest, accounts );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::syncItemImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncItem& itemToSync)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->syncItem( hRequest, itemToSync );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::syncItemsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<RemoteSyncItem>& itemsToSync)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->syncItems( hRequest, itemsToSync );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::fetchRangeRevisionImpl( const RequestHandle& hRequest, const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const FetchRangeParams& params )
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->fetchRangeRevision( hRequest, lowestRevision, highestRevision, params.itemTypes, params.conversationID, params.account, params.includeDeleted, params.count, params.offset, params.ascending );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::fetchRangeCreatedTimeImpl( const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const FetchRangeParams& params )
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->fetchRangeCreatedTime( hRequest, lowestCreatedTime, highestCreatedTime, params.itemTypes, params.conversationID, params.account, params.includeDeleted, params.count, params.offset, params.ascending );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::fetchConversationsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->fetchConversations( hRequest, lowestClientCreatedTime, highestClientCreatedTime, count, offset );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::fetchConversationsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector< cpc::string >& conversationIDs)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->fetchConversations( hRequest, conversationIDs );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::updateConversationImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->updateConversation( hRequest, accountID, conversationID, highestClientCreatedTime, setItemsRead, setItemsDeleted, setItemsUserDeleted );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::getMessageCountImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->getMessageCount( hRequest, accountID, types );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::updateItemImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->updateItem( hRequest, serverID, clientID, originalID, stableID, itemRead, itemDeleted, itemEdited, itemUserDeleted, itemState, callDuration, deliveryTimestamp, readTimestamp);

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::sendReactionImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncReaction& reaction)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->sendReaction( hRequest, reaction);

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::fetchMessagesReactionsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, bool ascending, int offset, int count)
{
   SyncManagerImpl* pSession = getSessionImpl( hSession );
   if( pSession != NULL )
      return pSession->fetchMessagesReactions( hRequest, ascending, offset, count );

   DebugLog( << __FUNCTION__ << " could not locate sync session " << hSession );
   return kError;
}

int SyncManagerInterface::updateItemsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp)
{
   SyncManagerImpl* pSession = getSessionImpl(hSession);
   if (pSession != NULL)
      return pSession->updateItems(hRequest, accounts, itemTypes, conversationIDs, serverIDs, isRead, isDeleted, isEdited, readTimestamp);

   DebugLog(<< __FUNCTION__ << " could not locate sync session " << hSession);
   return kError;
}

void SyncManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   if (context)
   {
      m_IOService.post(std::bind(&SyncManagerInterface::setCallbackHookImpl, this, cbHook, context));
   }
   else // context == NULL
   {
      // Special case that needs to block the caller until operation is complete.

      std::mutex mutex;
      std::condition_variable cvar;

      // Unfortunately verbose functor class which is here to avoid use of C++ lambdas.
      struct MyFunctor
      {
         MyFunctor(SyncManagerInterface* parent, void (*&cbHook)(void*), void*& context, std::mutex& mutex, std::condition_variable& cvar) :
            mParent(parent),
            mCbHook(cbHook),
            mContext(context),
            mMutex(mutex),
            mCVar(cvar) {}

         void operator()(void)
         {
            std::lock_guard<std::mutex> lock(mMutex);
            mParent->setCallbackHookImpl(mCbHook, mContext);
            mCVar.notify_all();
         }

         SyncManagerInterface* mParent;
         void (*&mCbHook)(void*);
         void*& mContext;
         std::mutex& mMutex;
         std::condition_variable& mCVar;
      };

      {
         // Block which needs to be synchronized
         std::unique_lock<std::mutex> lock(mutex); // acquires the mutex

         MyFunctor* func = new MyFunctor(this, cbHook, context, mutex, cvar);
         m_IOService.post(std::bind(&MyFunctor::operator(), func));
         cvar.wait(lock); // releases the mutex and waits on the condition (blocks caller thread)
         delete func; func = NULL; // Safe to delete functor now.
         lock.unlock(); // lock is reaquired, so .. release the associated mutex
      }

      // Force any events to run as a result of this operation
      process(-1);
   }
}

void SyncManagerInterface::setCallbackHookImpl( void (*cbHook)(void*), void* context )
{
   m_CbHook = cbHook;
   m_Context = context;

   std::map< SessionHandle, SyncManagerImpl* >::iterator iter = m_SessionMap.begin();
   while( iter != m_SessionMap.end() )
   {
      if( iter->second != NULL )
         iter->second->setCallbackHook( cbHook, context );

      ++iter;
   }
}

void SyncManagerInterface::addEventObserver(RemoteSyncHandler* handler)
{
   addSdkObserver(handler);
}

void SyncManagerInterface::removeEventObserver(RemoteSyncHandler* handler)
{
   removeSdkObserver(handler);
}

RemoteSyncManagerInternal* RemoteSyncManagerInternal::getInternalInterface(CPCAPI2::Phone* cpcPhone)
{
   return static_cast<RemoteSyncManagerInternal*>(RemoteSyncManager::getInterface(cpcPhone));
}

int SyncManagerInterface::process(int timeout)
{
   // -1 == no wait
   if( m_Shutdown )
      return -1;

   resip::ReadCallbackBase* fp = m_CallbackFifo.getNext( timeout );
   while( fp )
   {
      (*fp)();
      delete fp;
      if( m_Shutdown )
         return -1;

      fp = m_CallbackFifo.getNext( -1 );
   }

   return kSuccess;
}


int SyncManagerInterface::onNetworkChange( const CPCAPI2::NetworkChangeEvent& args )
{
   m_IOService.post( std::bind( &SyncManagerInterface::onNetworkChangeImpl, this, args ));
   return kSuccess;
}

int SyncManagerInterface::onNetworkChangeImpl( const CPCAPI2::NetworkChangeEvent& args )
{
   std::map< SessionHandle, SyncManagerImpl* >::iterator iter = m_SessionMap.begin();
   while( iter != m_SessionMap.end() )
   {
      if( iter->second != NULL )
      {
         iter->second->onNetworkChange(args);
      }
      
      ++iter;
   }
   
   return kSuccess;
}

namespace CPCAPI2
{

namespace RemoteSync
{

cpc::string get_debug_string(const CPCAPI2::RemoteSync::ConnectionState& state)
{
   switch (state)
   {
      case ConnectionState_Disconnected: return "disconnected";
      case ConnectionState_Connecting: return "connecting";
      case ConnectionState_Connected: return "connected";
      case ConnectionState_Failed: return "failed";
      default: return "invalid";
   }
   return "invalid";
}

cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncSettings& settings)
{
   std::stringstream ss;
   ss << " password: " << (settings.password.size() > 0 ? "\"is populated\"" : "\"is not populated\"") << " WebSocketSettings: " << settings.wsSettings << " account count: " << settings.accounts.size();
   for (cpc::vector<cpc::string>::const_iterator i = settings.accounts.begin(); i != settings.accounts.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& item)
{
   std::stringstream ss;
   ss << "unreadMessages: " << item.unreadMessages << " totalMessages: " << item.totalMessages << " hasLatestMessage: " << item.hasLatestMessage << " hasLatestChatInfo: " << item.hasLatestChatInfo << " RemoteSyncGroupChatItem (latestChatInfo): " << item.latestChatInfo << " RemoteSyncItem (latestMessage): " << item.latestMessage;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " request_offset: " << event.request_offset << " request_count: " << event.request_count << " RemoteSyncConversationThreadItem count: " << event.items.size();
   for (cpc::vector<RemoteSyncConversationThreadItem>::const_iterator i = event.items.begin(); i != event.items.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " request_offset: " << event.request_offset << " request_count: " << event.request_count << " RemoteSyncItem count: " << event.items.size();
   for (cpc::vector<RemoteSyncItem>::const_iterator i = event.items.begin(); i != event.items.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::NotificationUpdateEvent& event)
{
   std::stringstream ss;
   ss << "rev: " << event.rev << " RemoteSyncItem count: " << event.items.size();
   for (cpc::vector<RemoteSyncItem>::const_iterator i = event.items.begin(); i != event.items.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::MessageReactionsEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " rev: " << event.rev << " created_time: " << event.created_time << " server_id: " << event.server_id << " address: " << event.address << " value: " << event.value;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " rev: " << event.rev << " RemoteSyncItemUpdate: " << event.delta;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::ConversationUpdatedEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " rev: " << event.rev << " conversationID: " << event.conversationID << " highestClientCreatedTime: " << event.highestClientCreatedTime << " setItemsRead: " << event.setItemsRead << " setItemsDeleted: " << event.setItemsDeleted;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::SetAccountsEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " rev: " << event.rev << " item count: " << event.items.size();
   for (cpc::vector<RemoteSyncItemUpdate>::const_iterator i = event.items.begin(); i != event.items.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::MessageCountEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " unread: " << event.unread << " total: " << event.total << " unreadConversations: " << event.unreadConversations << " totalConversations: " << event.totalConversations;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnConnectionStateEvent& event)
{
   std::stringstream ss;
   ss << "previous-state: " << event.previousState << " current-state: " << event.currentState;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& event)
{
   std::stringstream ss;
   ss << "timestampDelta: " << event.timestampDelta;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::OnErrorEvent& event)
{
   std::stringstream ss;
   ss << "requestID: " << event.requestID << " errorCode: " << event.errorCode << " errorMessage: " << event.errorMessage;
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem::Source& source)
{
   switch (source)
   {
      case RemoteSyncItem::Source_null: return "null";
      case RemoteSyncItem::unknown: return "unknown";
      case RemoteSyncItem::original: return "original";
      case RemoteSyncItem::copy: return "copy";
      default: return "invalid";
   }
   return "invalid";
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem::ItemType& type)
{
   switch (type)
   {
      case RemoteSyncItem::ItemType_null: return "null";
      case RemoteSyncItem::im: return "im";
      case RemoteSyncItem::chatinfo: return "chatinfo";
      case RemoteSyncItem::sms: return "sms";
      case RemoteSyncItem::callhistory: return "callhistory";
      case RemoteSyncItem::contact: return "contact";
      default: return "invalid";
   }
   return "invalid";
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItem& item)
{
   std::stringstream ss;
   ss << "serverID: " << item.serverID << " clientID: " << item.clientID << " account: " << item.account << " itemType: " << item.itemType << " itemRead: " << item.itemRead << " itemDeleted: " << item.itemDeleted << " state: " << item.state << " clientTimestamp: " << item.clientTimestamp << " from: " << item.from << " to: " << item.to << " conversationID: " << item.conversationID << " contentType: " << item.contentType << " content: " << item.content << " uniqueID: " << item.uniqueID
      << item.callHistory;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncItemUpdate& item)
{
   std::stringstream ss;
   ss << "serverID: " << item.serverID << " clientID: " << item.clientID << " itemRead: " << item.itemRead << " itemDeleted: " << item.itemDeleted << " itemState: " << item.itemState << " preexists: " << item.preexists << " clientCreatedTime: " << item.clientCreatedTime;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& item)
{
   std::stringstream ss;
   cpc::vector<cpc::string> occupants = const_cast<RemoteSyncGroupChatItem&>(item).getOccupants();
   ss << "RemoteSyncItem: " << static_cast<const RemoteSyncItem&>(item) << " occupant count: " << occupants.size();
   for (cpc::vector<cpc::string>::const_iterator i = occupants.begin(); i != occupants.end(); i++)
   {
      ss << " [" << (*i) << "]";
   }
   return ss.str().c_str();
}

cpc::string get_debug_string(const CPCAPI2::RemoteSync::RemoteSyncCallHistory& item)
{
   std::stringstream ss;
   ss << " remoteName: " << item.remoteName << " associatedUri: " << item.associatedUri << " associatedNumber: " << item.associatedNumber << " callDuration: " << item.callDuration
      << " primaryDeviceHash: " << item.primaryDeviceHash << " primaryDevicePlatform: " << item.primaryDevicePlatform << " primaryDeviceName: " << item.primaryDeviceName;
   return ss.str().c_str();
}
   
cpc::string get_debug_string(const cpc::vector<CPCAPI2::RemoteSync::RemoteSyncReaction>& items)
{
   std::stringstream ss;
   ss << items.size() << " RemoteSyncReactions: ";
   for (cpc::vector<CPCAPI2::RemoteSync::RemoteSyncReaction>::const_iterator it = items.begin(); it != items.end(); it++)
   {
      ss << " [created_time: " << it->created_time << " rev: " << it->rev << " address: " << it->address 
         << " server_id: " << it->server_id << " value: " << it->value << "]";
   }
   return ss.str().c_str();
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::ConnectionState& state)
{
   os << CPCAPI2::RemoteSync::get_debug_string(state);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncSettings& settings)
{
   os << CPCAPI2::RemoteSync::get_debug_string(settings);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& item)
{
   os << CPCAPI2::RemoteSync::get_debug_string(item);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::NotificationUpdateEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::MessageReactionsEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::SetAccountsEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::MessageCountEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnConnectionStateEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnErrorEvent& event)
{
   os << CPCAPI2::RemoteSync::get_debug_string(event);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem::Source& source)
{
   os << CPCAPI2::RemoteSync::get_debug_string(source);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem::ItemType& type)
{
   os << CPCAPI2::RemoteSync::get_debug_string(type);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem& item)
{
   os << CPCAPI2::RemoteSync::get_debug_string(item);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItemUpdate& item)
{
   os << CPCAPI2::RemoteSync::get_debug_string(item);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& item)
{
   os << CPCAPI2::RemoteSync::get_debug_string(item);
   return os;
}

std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncCallHistory& item)
{
   os << CPCAPI2::RemoteSync::get_debug_string(item);
   return os;
}
   
std::ostream& operator<<(std::ostream& os, const cpc::vector<CPCAPI2::RemoteSync::RemoteSyncReaction>& items)
{
   os << CPCAPI2::RemoteSync::get_debug_string(items);
   return os;
}
   
}
   
}

#ifdef CPCAPI2_AUTO_TEST
// AutoTestProcessor implementation
CPCAPI2::AutoTestReadCallback* SyncManagerInterface::process_test(int timeout)
{
   if (m_Shutdown)
      return NULL;

   resip::ReadCallbackBase* rcb = m_CallbackFifo.getNext( timeout );
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
      return fpCmd;
   if (rcb != NULL)
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   return NULL;
}
#endif


#endif // CPCAPI2_BRAND_REMOTE_SYNC_MODULE
