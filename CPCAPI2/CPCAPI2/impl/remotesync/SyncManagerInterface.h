#pragma once

#ifndef CPCAPI2_SYNCMANAGERINTERFACE_H
#define CPCAPI2_SYNCMANAGERINTERFACE_H

#include <atomic>
#include <map>
#include <thread>

#include <boost/asio.hpp>

#include <rutil/Fifo.hxx>
#include <rutil/MultiReactor.hxx>

#include "cpcapi2defs.h"
#include <remotesync/RemoteSyncTypes.h>
#include <remotesync/RemoteSyncManager.h>
#include <remotesync/RemoteSyncManagerInternal.h>
#include <remotesync/RemoteSyncHandler.h>
#include "../phone/PhoneModule.h"
#include "../util/AutoTestProcessor.h"
#include "phone/NetworkChangeManagerImpl.h"


namespace CPCAPI2
{
   class Phone;
   struct NetworkChangeEvent;

   namespace RemoteSync
   {
      class SyncManagerImpl;

      class SyncManagerInterface : public RemoteSyncManagerInternal,
                                   public PhoneModule,
#ifdef CPCAPI2_AUTO_TEST
                                   public AutoTestProcessor,
#endif
                                   public CPCAPI2::EventSyncHandler<NetworkChangeHandler>
      {

      public:

         SyncManagerInterface(Phone* phone);
         virtual ~SyncManagerInterface();

         // SDK observer
         void addSdkObserver(RemoteSyncHandler* observer);
         // warning: does not block; observer may be removed some time later. To guarantee this has executed on SDK thread, call setHandler(.., NULL) after
         void removeSdkObserver(RemoteSyncHandler* observer);

         // PhoneModule implementation
         void Release() OVERRIDE;

         // NetworkChangeHandler implementation
         virtual int onNetworkChange(const NetworkChangeEvent& args) OVERRIDE;

         // RemoteSyncManagerInternal
         // static RemoteSyncManagerInternal* getInternalInterface(Phone* cpcPhone);
         virtual void setCallbackHook(void (*cbHook)(void*), void* context) OVERRIDE;
         virtual void addEventObserver(RemoteSyncHandler* handler) OVERRIDE;
         virtual void removeEventObserver(RemoteSyncHandler* handler) OVERRIDE;

         // RemoteSyncManager Implementation
         virtual int           setHandler(const SessionHandle& hSession, RemoteSyncHandler * notificationHandler) OVERRIDE;
         virtual SessionHandle create(void) OVERRIDE;
         virtual int           configureSettings(const SessionHandle& hSession, const struct RemoteSyncSettings& settings) OVERRIDE;
         virtual int           connect(const SessionHandle& hSession) OVERRIDE;
         virtual int           disconnect(const SessionHandle& hSession) OVERRIDE;
         virtual int           destroy(const SessionHandle& hSession) OVERRIDE;
         virtual RequestHandle setAccounts(const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts) OVERRIDE;
         virtual RequestHandle syncItem(const SessionHandle& hSession, const RemoteSyncItem& itemToSync) OVERRIDE;
         virtual RequestHandle syncItems(const SessionHandle& hSession, const cpc::vector<RemoteSyncItem>& itemsToSync) OVERRIDE;
         virtual RequestHandle fetchRangeRevision(const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending) OVERRIDE;
         virtual RequestHandle fetchRangeCreatedTime(const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending) OVERRIDE;
         virtual RequestHandle fetchConversations(const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset) OVERRIDE;
         virtual RequestHandle fetchConversations(const SessionHandle& hSession, const cpc::vector< cpc::string >& conversationIDs) OVERRIDE;
         virtual RequestHandle updateConversation(const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted) OVERRIDE;
         virtual RequestHandle getMessageCount(const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types) OVERRIDE;
         virtual RequestHandle updateItem(const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp) OVERRIDE;
         virtual RequestHandle sendReaction(const SessionHandle& hSession, const RemoteSyncReaction& reaction) OVERRIDE;
         virtual RequestHandle fetchMessagesReactions(const SessionHandle& hSession, bool ascending, int offset, int count) OVERRIDE;
         virtual RequestHandle updateItems(const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp) OVERRIDE;
         virtual int           process(int timeout) OVERRIDE;

         // RemoteSyncManager Implementation Wrapper Functions to provide the request handles
         void setAccounts(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts);
         void syncItem(const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncItem& itemToSync);
         void syncItems(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<RemoteSyncItem>& itemsToSync);
         void fetchRangeRevision(const RequestHandle& hRequest, const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending);
         void fetchRangeCreatedTime(const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending);
         void fetchConversations(const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset);
         void fetchConversations(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector< cpc::string >& conversationIDs);
         void updateConversation(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted);
         void getMessageCount(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types);
         void updateItem(const RequestHandle& hRequest, const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp);
         void sendReaction(const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncReaction& reaction);
         void fetchMessagesReactions(const RequestHandle& hRequest, const SessionHandle& hSession, bool ascending, int offset, int count);
         void updateItems(const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp);

         // Struct used to workaround boost::bind limitations
         struct FetchRangeParams
         {
            cpc::vector<RemoteSyncItem::ItemType> itemTypes;
            cpc::string conversationID;
            cpc::string account;
            bool includeDeleted;
            int count;
            int offset;
            bool ascending;
         };

         // NotificationService delegates
         int  setHandlerImpl( const SessionHandle& hSession, RemoteSyncHandler * notificationHandler);
         int  createImpl( const SessionHandle& hSession );
         int  configureSettingsImpl( const SessionHandle& hSession, const struct RemoteSyncSettings& settings );
         int  connectImpl( const SessionHandle& hSession );
         int  disconnectImpl( const SessionHandle& hSession );
         int  destroyImpl(const SessionHandle& hSession);
         int  setAccountsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts);
         int  syncItemImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncItem& itemToSync);
         int  syncItemsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<RemoteSyncItem>& itemsToSync);
         int  fetchRangeRevisionImpl( const RequestHandle& hRequest, const SessionHandle& hSession, Revision lowestRevision, Revision highestRevision, const struct FetchRangeParams& params );
         int  fetchRangeCreatedTimeImpl( const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestCreatedTime, int64_t highestCreatedTime, const struct FetchRangeParams& params );
         int  fetchConversationsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset);
         int  fetchConversationsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector< cpc::string >& conversationIDs);
         int  updateConversationImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted);
         int  getMessageCountImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types);
         int  updateItemImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp);
         int  sendReactionImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const RemoteSyncReaction& reaction);
         int  fetchMessagesReactionsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, bool ascending, int offset, int count);
         int  updateItemsImpl( const RequestHandle& hRequest, const SessionHandle& hSession, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp);

         void setCallbackHookImpl( void (*cbHook)(void*), void* context );
         void addSdkObserverImpl(RemoteSyncHandler* handler);
         void removeSdkObserverImpl(RemoteSyncHandler* handler);

         // Static handle counting functions
         static SessionHandle nextSessionHandle();
         static RequestHandle nextRequestHandle();

#ifdef CPCAPI2_AUTO_TEST
         // AutoTestProcessor implementation
         AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

      private: // methods

         SyncManagerImpl *getSessionImpl( const SessionHandle& hChannel );
         int onNetworkChangeImpl( const NetworkChangeEvent& args );

      private: // data

         // Static handle counters
         static std::atomic< SessionHandle > s_SessionHandle;
         static std::atomic< RequestHandle > s_RequestHandle;

         // SDK observer
         std::set<RemoteSyncHandler*> mSdkObservers;

         CPCAPI2::Phone* m_Phone;

         // Map of SessionHandle(s) to SyncManagerImpl(s) (owned)
         std::map< SessionHandle, SyncManagerImpl* > m_SessionMap;

         // used for dispatching
         boost::asio::io_service& m_IOService;

         // Callback Fifo which should be used to marshal the events (process method should be called)
         resip::Fifo< resip::ReadCallbackBase > m_CallbackFifo;

         // Set to false once shutdown commences (to prevent further events)
         bool m_Shutdown;

         void (*m_CbHook)(void*);
         void* m_Context;

         friend RemoteSyncManagerInternal; // for access to static RemoteSyncManagerInternal::resetRequestHandleCount()
      };

      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::ConnectionState& state);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncSettings& settings);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncConversationThreadItem& item);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::NotificationUpdateEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::MessageReactionsEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::SetAccountsEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::MessageCountEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnConnectionStateEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::OnErrorEvent& event);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem::Source& source);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem::ItemType& type);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItem& item);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncItemUpdate& item);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncGroupChatItem& item);
      std::ostream& operator<<(std::ostream& os, const CPCAPI2::RemoteSync::RemoteSyncCallHistory& item);
      std::ostream& operator<<(std::ostream& os, const cpc::vector<CPCAPI2::RemoteSync::RemoteSyncReaction>& items);

   }
}

#endif // CPCAPI2_SYNCMANAGERINTERFACE_H
