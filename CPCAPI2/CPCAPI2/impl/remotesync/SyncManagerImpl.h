#pragma once
#ifndef __CPCAPI2_REMOTESYNC_STRETTOSYNCIMPL_H__
#define __CPCAPI2_REMOTESYNC_STRETTOSYNCIMPL_H__

#include <cpcapi2defs.h>
#include <memory> // websocketpp seems to use this for shared_ptr anyway
#include <cstdlib>
#include <iostream>
#include <map>
#include <string>
#include <sstream>
#include <mutex>

#include <websocket/WebSocketStateMachine.h>
#include <websocket/WebSocketStateDispatcher.h>
#include <websocket/json/WebSocketCommand.h>

#include "../phone/PhoneModule.h"
#include <remotesync/RemoteSyncTypes.h>
#include "remotesync/RemoteSyncInternalHandlerTypes.h"
#include <remotesync/RemoteSyncManager.h>
#include <remotesync/RemoteSyncHandler.h>
#include "commands/Command.h"
#include "commands/LoginCmd.h"
#include "util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "../util/APILogger.h"
#include <rutil/Fifo.hxx>
#include "RemoteSyncHandlerAdapter.h"
#include "phone/NetworkChangeHandler.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class SyncManagerImpl;

      typedef websocketpp::client<websocketpp::config::asio_tls_client> client;
      typedef websocketpp::config::asio_tls_client::message_type::ptr message_ptr;

      // Implementation of RemoteSyncManager interface
      class SyncManagerImpl :
         public RemoteSyncHandlerAdapter,
         public WebSocket::WebSocketStateMachineListener
      {
         friend struct RemoteSyncSessionInfo;

      public:
         SyncManagerImpl( SessionHandle hSession,
            boost::asio::io_service& ioService,
            resip::Fifo< resip::ReadCallbackBase >& callbackFifo,
            SslCipherOptions tlsSettings);
         virtual ~SyncManagerImpl();

         // Static utility function for timing
         static int64_t millisSinceEpoch();

         // SDK observer
         void addSdkObserver(RemoteSyncHandler* sdkObserver);

         int onNetworkChange( const NetworkChangeEvent& args );
         virtual void setCallbackHook(void (*cbHook)(void*), void* context);
         WebSocket::WebSocketStateMachine *getStateMachine() { return m_StateMachine; }
         const RemoteSyncSettings& getSettings() { return m_Settings; }

         //
         // Interface implementation
         //
         int configureSettings( const struct RemoteSyncSettings& settings );
         int setHandler(RemoteSyncHandler * notificationHandler);
         int connect();
         int disconnect();
         int destroy();
         int setAccounts( RequestHandle hRequest, const cpc::vector<cpc::string>& accounts);
         int syncItem( RequestHandle hRequest, RemoteSyncItem const & itemToSync);
         int syncItems( RequestHandle hRequest, const cpc::vector< RemoteSyncItem >& itemsToSync);
         int updateItem( RequestHandle hRequest, const ServerID& serverID, const cpc::string& clientID, const cpc::string& originalID, const cpc::string& stableID, bool itemRead, bool itemDeleted, bool itemEdited, bool itemUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp, int64_t readTimestamp);
         int fetchRangeRevision( RequestHandle hRequest, Revision lowestRevision, Revision highestRevision, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending );
         int fetchRangeCreatedTime( RequestHandle hRequest, int64_t lowestCreatedTime, int64_t highestCreatedTime, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::string& conversationID, const cpc::string& account, bool includeDeleted, int count, int offset, bool ascending );
         int fetchConversations( RequestHandle hRequest, int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int count, int offset);
         int fetchConversations( RequestHandle hRequest, const cpc::vector< cpc::string >& conversationIDs);
         int updateConversation( RequestHandle hRequest, const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted);
         int updateItems(RequestHandle hRequest, const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& itemTypes, const cpc::vector<cpc::string>& conversationIDs, const cpc::vector<int64_t>& serverIDs, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp);
         int sendReaction(RequestHandle hRequest, const RemoteSyncReaction& reaction);
         int fetchMessagesReactions(RequestHandle hRequest, bool ascending, int offset, int count);
         int getMessageCount( RequestHandle hRequest, const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types);

         // Update the timestamp delta and fire an event
         void updateTimestampDelta( const int64_t& clientTime, const int64_t& serverTime );

         // Listener to state machine changes (dispatch to API events)
         void onStateChange( const CPCAPI2::AbstractStatePtr oldState, const CPCAPI2::AbstractStatePtr newState, const CPCAPI2::AbstractStateReasonCode reason ) OVERRIDE;
         void onLogin( void ) OVERRIDE;
         void onLoginResponse( const std::string& inString ) OVERRIDE;
         void onLogout( void ) OVERRIDE {}
         void onMessage( const std::string& inString ) OVERRIDE;
         void onPing( websocketpp::connection_hdl hWebSock ) OVERRIDE;
         bool onReconnect( uint16_t code ) OVERRIDE;
         void onClose( ) OVERRIDE;

         // Ugly template for marshalling events back into proper thread.
         template<typename TFn, typename TEvt> void fireEvent(const char* funcName, TFn func, const TEvt& args)
         {
            for (size_t i = 0; i < mSdkObservers.size(); ++i)
            {
               resip::ReadCallbackBase* cb = resip::resip_bind(func, mSdkObservers[i], m_hSession, args);
               if (dynamic_cast<RemoteSyncSyncHandler*>(mSdkObservers[i]) != NULL)
               {
                  (*cb)();
                  delete cb;
               }
               else if (dynamic_cast<RemoteSyncAsyncHandler*>(mSdkObservers[i]) != NULL)
               {
                  dynamic_cast<RemoteSyncAsyncHandler*>(mSdkObservers[i])->onEvent(cb);
               }
               else
               {
                  // Object "cb" should be deleted by whomever is processing this FIFO
                  m_CallbackFifo.add(cb);
                  if (mCbHook) { mCbHook(); }
               }
            }

            if( m_Handler == NULL )
               return;

            resip::ReadCallbackBase* cb = makeFpCommandNew(funcName, func, m_Handler, m_hSession, args);
            if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<RemoteSyncSyncHandler*>(m_Handler) != NULL)
            {
               (*cb)();
               delete cb;
            }
            else if (m_Handler != (void*)0xDEADBEEF && dynamic_cast<RemoteSyncAsyncHandler*>(m_Handler) != NULL)
            {
               dynamic_cast<RemoteSyncAsyncHandler*>(m_Handler)->onEvent(cb);
            }
            else
            {
               // Object "cb" should be deleted by whomever is processing this FIFO
               m_CallbackFifo.add( cb );
               if (mCbHook) { mCbHook(); }
            }
         }
      private:
         void deleteThis() { delete this; }

      private:

         SessionHandle m_hSession;

         // passed into websocketpp lib
         boost::asio::io_service& m_IOService;

         // shared between Interface and Impl
         resip::Fifo< resip::ReadCallbackBase >& m_CallbackFifo;

         std::function<void(void)> mCbHook;

         // Settings used for this remote sync connection
         RemoteSyncSettings m_Settings;

         // State machine instance (owned)
         WebSocket::WebSocketStateMachine *m_StateMachine;

         // Application handler for account related activity
         RemoteSyncHandler *m_Handler;

         // SDK observer
         std::vector<RemoteSyncHandler*> mSdkObservers;

         // Default TLS Settings
         SslCipherOptions mTlsSettings;
         
         bool m_destructOnDisconnected;
         bool m_deleteThisQueued;
      };
   }
}

#endif
