#pragma once

#ifndef __CPCAPI2__REMOTESYNCHANDLERADAPTER_H__
#define __CPCAPI2__REMOTESYNCHANDLERADAPTER_H__

#include "cpcapi2defs.h"
#include <remotesync/RemoteSyncHandler.h>

namespace CPCAPI2
{
   namespace RemoteSync
   {
      /**
      * The purpose of this class is to extend and stub out all the methods inside of the RemoteSyncHandler,
      * such that it is easier for implementing classes to override but a single method.
      */
      class RemoteSyncHandlerAdapter : public RemoteSyncHandler
      {
      public:
         int onSetAccounts(const SessionHandle& sessionHandle, const SetAccountsEvent& evt) OVERRIDE{ return kSuccess; }
         int onNotificationUpdate(const SessionHandle& sessionHandle, const NotificationUpdateEvent& evt) OVERRIDE{ return kSuccess; }
         int onMessageReactions(const SessionHandle& sessionHandle, const MessageReactionsEvent& evt) OVERRIDE{ return kSuccess; }
         int onFetchMessagesReactionsComplete(const SessionHandle& sessionHandle, const FetchMessagesReactionsCompleteEvent& evt) OVERRIDE{ return kSuccess; }
         int onSyncItemsComplete(const SessionHandle& sessionHandle, const SyncItemsCompleteEvent& evt) OVERRIDE{ return kSuccess; }
         int onUpdateItemComplete(const SessionHandle& sessionHandle, const UpdateItemCompleteEvent& evt) OVERRIDE{ return kSuccess; }
         int onFetchRangeComplete(const SessionHandle& sessionHandle, const FetchRangeCompleteEvent& evt) OVERRIDE{ return kSuccess; }
         int onFetchConversationsComplete(const SessionHandle& sessionHandle, const FetchConversationsCompleteEvent& evt) OVERRIDE{ return kSuccess; }
         int onConversationUpdated(const SessionHandle& sessionHandle, const ConversationUpdatedEvent& evt) OVERRIDE{ return kSuccess; }
         int onMessageCount(const SessionHandle& sessionHandle, const MessageCountEvent& evt) OVERRIDE{ return kSuccess; }
         int onError(const SessionHandle& sessionHandle, const OnErrorEvent& evt) OVERRIDE{ return kSuccess; }
         int onConnectionState( const SessionHandle& sessionHandle, const OnConnectionStateEvent& evt ) OVERRIDE{ return kSuccess; }
         int onTimestampDelta( const SessionHandle& sessionHandle, const OnTimestampDeltaEvent& evt ) OVERRIDE{ return kSuccess; }
         int onUpdateItemsComplete(const SessionHandle& sessionHandle, const UpdateItemsCompleteEvent& evt) OVERRIDE { return kSuccess; }
         int onItemsUpdated( const SessionHandle& sessionHandle, const ItemsUpdatedEvent& evt ) OVERRIDE { return kSuccess; }
      };
   }
}
#endif /* __CPCAPI2__REMOTESYNCHANDLERADAPTER_H__ */
