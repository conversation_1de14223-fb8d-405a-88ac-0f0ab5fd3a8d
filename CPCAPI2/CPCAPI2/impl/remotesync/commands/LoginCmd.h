#pragma once
#ifndef _CPCAPI2_REMOTESYNC_LOGINCMD_H_
#define _CPCAPI2_REMOTESYNC_LOGINCMD_H_

#include <cpcstl/vector.h>
#include <cpcstl/string.h>
#include <document.h>
#include <writer.h>
#include <stringbuffer.h>

#include <cpcapi2defs.h>

#include <json/JsonHelper.h>
#include <websocket/json/WebSocketCommand.h>
#include "RemoteSyncTokens.h"

#define PROTOCOL_VERSION 1001

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class LoginCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         int const protocolVersion;
         cpc::string password;
         cpc::vector< cpc::string > accounts;
         ClientDeviceInfo clientDeviceInfo;

         LoginCmd(const cpc::string& password, const cpc::vector<cpc::string>& accounts, const ClientDeviceInfo& clientDevice)
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              protocolVersion( PROTOCOL_VERSION )
         {
            this->accounts = accounts;
            this->password = password;
            this->clientDeviceInfo = clientDevice;
         }
         virtual ~LoginCmd() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_LOGIN; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);
            CPCAPI2::Json::Write(writer, "password",         password);
            CPCAPI2::Json::Write(writer, "protoversion",     protocolVersion);
            CPCAPI2::Json::Write(writer, "accounts",         accounts);
            
            if (!clientDeviceInfo.clientDeviceHash.empty())
               CPCAPI2::Json::Write(writer, "client_device_hash",     clientDeviceInfo.clientDeviceHash);
            if (!clientDeviceInfo.clientDeviceName.empty())
               CPCAPI2::Json::Write(writer, "client_device_name",     clientDeviceInfo.clientDeviceName);
            if (!clientDeviceInfo.clientDevicePlatform.empty())
               CPCAPI2::Json::Write(writer, "client_device_platform", clientDeviceInfo.clientDevicePlatform);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // _CPCAPI2_REMOTESYNC_LOGINCMD_H_
