#include <brand_branded.h>

#if (CPCAPI2_BRAND_REMOTE_SYNC_MODULE == 1)
#include "Command.h"

using namespace CPCAPI2::RemoteSync;

/**
* An abstract base class for a transactional command unit to be written over the socket in a single message.
*/

Command::COMMAND_NAME commandName;

char const * const Command::CLIENT_SYNC_ITEM_LIST            = "sync_item_list";
char const * const Command::CLIENT_SYNC_ITEM_TYPE            = "item_type";
char const * const Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID = "client_id";
char const * const Command::CLIENT_SYNC_ITEM_CREATED_TIME    = "item_created_time";
char const * const Command::CLIENT_SYNC_ITEM_LAST_MODIFIED   = "lastmod";
char const * const Command::CLIENT_SYNC_ITEM_READ            = "item_read";
char const * const Command::CLIENT_SYNC_ITEM_DELETED         = "item_deleted";
char const * const Command::CLIENT_SYNC_ITEM_EDITED          = "item_edited";
char const * const Command::CLIENT_SYNC_ITEM_USERDELETED     = "item_userdeleted";
char const * const Command::CLIENT_SYNC_ITEM_REACTIONS       = "reactions";
char const * const Command::CLIENT_SYNC_ITEM_ORIGINAL_ID     = "original_id";
char const * const Command::CLIENT_SYNC_ITEM_STABLE_ID       = "stable_id";
char const * const Command::CLIENT_SYNC_ITEM_SOURCE          = "item_source";
char const * const Command::CLIENT_SYNC_ITEM_STATE           = "item_state";
char const * const Command::CLIENT_SYNC_ITEM_FROM            = "item_from";
char const * const Command::CLIENT_SYNC_ITEM_TO              = "item_to";
char const * const Command::CLIENT_SYNC_ITEM_THREAD_ID       = "thread_id";
char const * const Command::CLIENT_SYNC_ITEM_SERVER_ID       = "server_id";
char const * const Command::CLIENT_SYNC_ITEM_CONTENT         = "content";
char const * const Command::CLIENT_SYNC_ITEM_CONTENT_TYPE    = "content_type";
char const * const Command::CLIENT_SYNC_ITEM_PREEXISTS       = "preexists";
char const * const Command::CLIENT_SYNC_ITEM_ACCOUNT         = "account";
char const * const Command::CLIENT_SYNC_ITEM_UNIQUE_ID       = "uid";
char const * const Command::CLIENT_SYNC_ITEM_DELIVERY_TIME   = "item_delivered_time";
char const * const Command::CLIENT_SYNC_ITEM_READ_TIME       = "item_read_time";

//CallHistory sync
char const * const Command::CLIENT_SYNC_ITEM_REMOTE_NAME     = "item_remote_name";
char const * const Command::CLIENT_SYNC_ITEM_URI             = "item_associated_uri";
char const * const Command::CLIENT_SYNC_ITEM_NUMBER          = "item_associated_number";
char const * const Command::CLIENT_SYNC_ITEM_CALL_DURATION   = "item_call_duration";
char const * const Command::CLIENT_SYNC_ITEM_STATUS_CODE     = "status_code";
char const * const Command::CLIENT_SYNC_ITEM_DEVICE_HASH     = "primary_device_hash";
char const * const Command::CLIENT_SYNC_ITEM_DEVICE_PLATFORM = "primary_device_platform";
char const * const Command::CLIENT_SYNC_ITEM_DEVICE_NAME     = "primary_device_name";

#endif
