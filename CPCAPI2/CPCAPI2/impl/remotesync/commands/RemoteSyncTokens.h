#pragma once
#ifndef __CPCAPI2_REMOTESYNC_TOKENS_H__
#define __CPCAPI2_REMOTESYNC_TOKENS_H__

namespace CPCAPI2
{
   namespace RemoteSync
   {
      // Constant strings which are used for building/parsing json messages to/from the SNS server.

      extern const char *CLIENT_COMMAND;
      extern const char *CLIENT_REQUEST_ID;
      extern const char *CLIENT_REVISION;
      extern const char *CLIENT_ITEM_LIST;
      extern const char *CLIENT_PASSWORD;
      extern const char *CLIENT_GROUP;
      extern const char *CLIENT_USERNAME;
      extern const char *CLIENT_DISPLAY_NAME;
      extern const char *CLIENT_PIN;
      extern const char *CLIENT_CALL_ID;
      extern const char *CLIENT_PRODUCTCODE;
      extern const char *CLIENT_LICENSEKEY;
      extern const char *CLIENT_LOCALE;
      extern const char *CLIENT_APPLICATIONID;
      extern const char *CLIENT_DEVICEUUID;

      // Command names
      extern const char *COMMAND_NAME_UNKNOWN;
      extern const char *COMMAND_NAME_ERROR;
      extern const char *COMMAND_NAME_LOGIN;
      extern const char *COMMAND_NAME_SET_ACCOUNTS;
      extern const char *COMMAND_NAME_UPDATE_CONVERSATION;
      extern const char *COMMAND_NAME_UPDATE_ITEMS;

      // Response names (command names appended with response suffix)
      extern const char *RESPONSE_NAME_STATUS;

      // Unsolicited messages coming from the server
      extern const char *NOTIFICATION_NAME_EVENT;
   }
}

#endif // __CPCAPI2_REMOTESYNC_TOKENS_H__
