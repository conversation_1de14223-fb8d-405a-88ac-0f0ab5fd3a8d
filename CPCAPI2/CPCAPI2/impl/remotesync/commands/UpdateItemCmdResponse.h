#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATEITEMCMDRESPONSE_H__
#define __CPCAPI2_REMOTESYNC_UPDATEITEMCMDRESPONSE_H__

#include <vector>
#include <string>
#include <document.h>

#include <websocket/json/WebSocketResponse.h>
#include "Command.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class UpdateItemCmdResponse : public WebSocket::WebSocketResponse
      {
      public:
         RequestHandle m_hRequest;
         RemoteSyncItemUpdate delta;
         Revision revision;

         UpdateItemCmdResponse()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              revision( -1 )
         {
         }

         const char *getCommandName() const OVERRIDE { return "UPDATE_ITEM_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            CPCAPI2::Json::Read(inDocument, "delta", delta);
            revision = inDocument[ CLIENT_REVISION ].GetInt64();
            return true;
         }
      };
   }
}

#endif
