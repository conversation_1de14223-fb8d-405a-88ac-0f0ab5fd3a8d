#pragma once
#ifndef __CPCAPI2_REMOTESYNC_PINGCMD_H__
#define __CPCAPI2_REMOTESYNC_PINGCMD_H__

#include <document.h>
#include <websocket/json/WebSocketCommand.h>

#define CLIENT_TIME "clientTime"
#define SERVER_TIME "serverTime"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class PingCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
	      int64_t clientClock;
	      int64_t serverClock;
	
	      PingCmd(int64_t clientClock, int64_t serverClock = 0 ) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            clientClock( clientClock ),
            serverClock( serverClock )
	      {
	      }
	
         const char *getCommandName() const OVERRIDE { return "PING_CMD"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_hRequest);
            CPCAPI2::Json::Write(writer, CLIENT_TIME,       clientClock);
            CPCAPI2::Json::Write(writer, SERVER_TIME,       serverClock);
            writer.EndObject();
            return true;
	      }
      };
   }
}

#endif // __CPCAPI2_REMOTESYNC_PINGCMD_H__
