#pragma once
#ifndef _CPCAPI2_REMOTESYNC_UPDATENOTIFICATION_H_
#define _CPCAPI2_REMOTESYNC_UPDATENOTIFICATION_H_

#include <vector>
#include <string>
#include <document.h>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include "Command.h"

#define REVISION "rev"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class UpdateNotification : public WebSocket::WebSocketNotification
      {
      public:
         cpc::vector< RemoteSyncItem > items;
         Revision revision;

         UpdateNotification( void ) {}
         virtual ~UpdateNotification() {}

         const char *getNotificationName() const OVERRIDE { return "UPDATE_NOTIFICATION"; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            CPCAPI2::Json::Read(inDocument, "items", items);

            revision = inDocument[ REVISION ].GetInt64();
            return true;
         }
      };

   }
}

#endif
