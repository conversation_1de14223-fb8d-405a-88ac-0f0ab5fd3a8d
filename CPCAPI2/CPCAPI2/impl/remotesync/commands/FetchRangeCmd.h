#pragma once
#ifndef __CPCAPI2_REMOTESYNC_FETCHRANGECMD_H__
#define __CPCAPI2_REMOTESYNC_FETCHRANGECMD_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <document.h>

#include <cpcapi2defs.h>

#include <json/JsonHelper.h>
#include <websocket/json/WebSocketCommand.h>

#include <remotesync/RemoteSyncItemInternal.h>

#define ASCENDING         "ascending"
#define COUNT             "count"
#define OFFSET            "offset"
#define ITEM_TYPES        "itemTypes"
#define THREAD_ID         "threadID"
#define ACCOUNT           "account"
#define LOWEST            "lowest"
#define HIGHEST           "highest"
#define BY_REVISIONS      "byrev"
#define INCLUDE_DELETED   "include_deleted"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class FetchRangeCmd : public WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;

         // Time / Revision range filtering:
         Revision lowest;	// 0 means unlimited
         Revision highest;	// 0 means unlimited
         bool byRevisions; // true: filter by server revision; false: filter by client created time
         cpc::vector<RemoteSyncItem::ItemType> itemTypes;
         cpc::string conversationID;
         bool includeDeleted;

         int offset;
         int count;
         bool ascending;
         cpc::string account;

      public:
         FetchRangeCmd(int offset, int count, bool ascending,
                       Revision lowest, Revision highest, bool byRevisions,
                       const cpc::vector<RemoteSyncItem::ItemType>& itemTypes,
                       const cpc::string& conversationID,
                       const cpc::string& account,
                       bool includeDeleted ) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            lowest( lowest ),
            highest( highest ),
            byRevisions( byRevisions ),
            conversationID(conversationID),
            includeDeleted(includeDeleted),
            offset(offset),
            count(count),
            ascending(ascending),
            account(account)
         {
            size_t itemTypeCount = itemTypes.size();
            for(size_t i=0;i<itemTypeCount;i++)
               this->itemTypes.push_back(itemTypes[i]);
         }

         const char *getCommandName() const OVERRIDE { return "FETCH_RANGE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);
            CPCAPI2::Json::Write(writer, ASCENDING, ascending);
            CPCAPI2::Json::Write(writer, COUNT, count);
            CPCAPI2::Json::Write(writer, OFFSET, offset);

            if (includeDeleted)
               CPCAPI2::Json::Write(writer, INCLUDE_DELETED, true);

            if (byRevisions)
               CPCAPI2::Json::Write(writer, BY_REVISIONS, true);

            if (lowest != 0)
               CPCAPI2::Json::Write(writer, LOWEST, lowest);

            if (highest != 0)
               CPCAPI2::Json::Write(writer, HIGHEST, highest);

            if (!itemTypes.empty())
            {
               writer.Key(ITEM_TYPES);
               writer.StartArray();
               for (unsigned int i=0; i < itemTypes.size(); i++)
               {
                  CPCAPI2::Json::Serialize(writer, RemoteSyncItemInternal::s_mapItemTypeToString[itemTypes[i]]);
               }
               writer.EndArray();
            }

            if (!conversationID.empty())
               CPCAPI2::Json::Write(writer, THREAD_ID, conversationID);

            if (!account.empty())
               CPCAPI2::Json::Write(writer, ACCOUNT, account);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
