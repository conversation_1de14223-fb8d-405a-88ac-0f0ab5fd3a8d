#pragma once
#ifndef __CPCAPI2_REMOTESYNC_SYNCCMDRESPONSE_H__
#define __CPCAPI2_REMOTESYNC_SYNCCMDRESPONSE_H__

#include <cpcstl/vector.h>
#include <document.h>
#include <websocket/json/WebSocketResponse.h>

#include <remotesync/RemoteSyncItemUpdate.h>
#include "RemoteSyncTokens.h"
#include "Command.h"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class SyncCmdResponse : public WebSocket::WebSocketResponse
      {
      public:
         cpc::vector<RemoteSyncItemUpdate> items;
         Revision revision;
         RequestHandle m_hRequest;

         SyncCmdResponse( void )
            : revision( -1 ), m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ) {}
         virtual ~SyncCmdResponse( void ) {}

         const char *getCommandName() const OVERRIDE { return "SYNC_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            CPCAPI2::Json::Read(inDocument, CLIENT_ITEM_LIST, items);

            revision = inDocument[ CLIENT_REVISION ].GetInt64();
            return true;
         }
      };
   }
}

#endif // __CPCAPI2_REMOTESYNC_SYNCCMDRESPONSE_H__