#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATECONVERSATIONNOTIFICATION_H__
#define __CPCAPI2_REMOTESYNC_UPDATECONVERSATIONNOTIFICATION_H__

#include <cpcstl/string.h>
#include <document.h>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <remotesync/RemoteSyncTypes.h>
#include "RemoteSyncTokens.h"

#define ACCOUNTID       "accountID"
#define CONVERSATIONID  "conversationID"
#define RANGEHIGHEST    "highest"
#define SETITEMSREAD    "setItemsRead"
#define SETITEMSDELETED "setItemsDeleted"
#define REVISION        "rev"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class UpdateConversationNotification : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         RequestHandle m_hRequest;
         cpc::string accountID;
         cpc::string conversationID;
         int64_t highestClientCreatedTime; // set to 0 to indicate all items. This refers to the client created timestamp
         Revision revision; // Ignored during requests.  Returns the revision of the [sic]
         bool setItemsRead;
         bool setItemsDeleted;

         UpdateConversationNotification( void )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              highestClientCreatedTime( 0 ),
              revision( 0 ),
              setItemsRead( false ),
              setItemsDeleted( false ) {}
         virtual ~UpdateConversationNotification() {}
	
         const char *getNotificationName() const OVERRIDE { return COMMAND_NAME_UPDATE_CONVERSATION; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            // Fetch the request ID (this will be zero in the event of a notification,
            // otherwise it will be set to something if the client initiated the update)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            accountID                = inDocument[ ACCOUNTID ].GetString();
            conversationID           = inDocument.HasMember( CONVERSATIONID ) ? inDocument[ CONVERSATIONID ].GetString() : "";
            highestClientCreatedTime = inDocument.HasMember( RANGEHIGHEST ) ? inDocument[ RANGEHIGHEST ].GetInt64() : 0;
            setItemsRead             = inDocument[ SETITEMSREAD ].GetBool();
            setItemsDeleted          = inDocument[ SETITEMSDELETED ].GetBool();
            revision                 = inDocument.HasMember( REVISION ) ? inDocument[ REVISION ].GetInt64() : 0;
            return true;
         }
      };
   }
}

#endif
