#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATEITEMSCMD_H__
#define __CPCAPI2_REMOTESYNC_UPDATEITEMSCMD_H__

#include <cpcstl/string.h>
#include <remotesync/RemoteSyncTypes.h>
#include <document.h>

#include <websocket/json/WebSocketCommand.h>

#define ISREAD          "isRead"
#define ISDELETED       "isDeleted"
#define ISEDITED        "isEdited"
#define ACCOUNTS        "accounts"
#define ITEMTYPES       "item_types"
#define CONVERSATIONIDS "conversation_ids"
#define SERVERIDS       "server_ids"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class UpdateItemsCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         bool isRead;
         bool isDeleted;
         bool isEdited;
         int64_t readTimestamp;

         cpc::vector<cpc::string> accounts;
         cpc::vector<RemoteSyncItem::ItemType> item_types;
         cpc::vector<cpc::string> conversation_ids;
         cpc::vector<int64_t> server_ids;

         UpdateItemsCmd( const cpc::vector<cpc::string>& accounts, const cpc::vector<RemoteSyncItem::ItemType>& item_types, const cpc::vector<cpc::string>& conversation_ids, const cpc::vector<int64_t>& server_ids, bool isRead, bool isDeleted, bool isEdited, int64_t readTimestamp = 0 )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            accounts(accounts),
            item_types(item_types),
            conversation_ids(conversation_ids),
            server_ids(server_ids),
            isRead(isRead),
            isDeleted(isDeleted),
            isEdited(isEdited),
            readTimestamp(readTimestamp)
         {
         }

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_UPDATE_ITEMS; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         const std::string getLogIdentifier() OVERRIDE
         {
            std::string ident = getCommandName();

            for (size_t i = 0; i < item_types.size(); i++)
            {
               ident += " " + RemoteSyncItemInternal::s_mapItemTypeToString[item_types[i]];
            }

            return ident;
         }

         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);

            if (isRead)
               CPCAPI2::Json::Write(writer, ISREAD, isRead);

            if (isDeleted)
               CPCAPI2::Json::Write(writer, ISDELETED, isDeleted);

            if (isEdited)
               CPCAPI2::Json::Write(writer, ISEDITED, isEdited);

            if( !accounts.empty() )
               CPCAPI2::Json::Write(writer, ACCOUNTS, accounts);

            if (!item_types.empty())
            {
               writer.Key(ITEMTYPES);
               writer.StartArray();
               for (unsigned int i = 0; i < item_types.size(); i++)
               {
                  CPCAPI2::Json::Serialize(writer, RemoteSyncItemInternal::s_mapItemTypeToString[item_types[i]]);
               }
               writer.EndArray();
            }

            if( !conversation_ids.empty() )
               CPCAPI2::Json::Write(writer, CONVERSATIONIDS, conversation_ids);

            if (!server_ids.empty())
               CPCAPI2::Json::Write(writer, SERVERIDS, server_ids);

            if (readTimestamp > 0)
               CPCAPI2::Json::Write(writer, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, readTimestamp);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
