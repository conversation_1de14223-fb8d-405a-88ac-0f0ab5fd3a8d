#include "RemoteSyncTokens.h"

// Constant strings and ints which are used for building/parsing json messages to/from the server.

const char *CPCAPI2::RemoteSync::CLIENT_COMMAND                     = "cmd";
const char *CPCAPI2::RemoteSync::CLIENT_REQUEST_ID                  = "requestID";
const char *CPCAPI2::RemoteSync::CLIENT_REVISION                    = "rev";
const char *CPCAPI2::RemoteSync::CLIENT_ITEM_LIST                   = "items";
const char *CPCAPI2::RemoteSync::CLIENT_PASSWORD                    = "password";
const char *CPCAPI2::RemoteSync::CLIENT_GROUP                       = "group";
const char *CPCAPI2::RemoteSync::CLIENT_USERNAME                    = "userName";
const char *CPCAPI2::RemoteSync::CLIENT_DISPLAY_NAME                = "displayName";
const char *CPCAPI2::RemoteSync::CLIENT_PIN                         = "pin";
const char *CPCAPI2::RemoteSync::CLIENT_CALL_ID                     = "callId";
const char *CPCAPI2::RemoteSync::CLIENT_PRODUCTCODE                 = "productCode";
const char *CPCAPI2::RemoteSync::CLIENT_LICENSEKEY                  = "licenseKey";
const char *CPCAPI2::RemoteSync::CLIENT_LOCALE                      = "locale";
const char *CPCAPI2::RemoteSync::CLIENT_APPLICATIONID               = "applicationID";
const char *CPCAPI2::RemoteSync::CLIENT_DEVICEUUID                  = "deviceUUID";

// Command names
const char *CPCAPI2::RemoteSync::COMMAND_NAME_UNKNOWN               = "UNKNOWN";
const char *CPCAPI2::RemoteSync::COMMAND_NAME_ERROR                 = "ERROR";
const char *CPCAPI2::RemoteSync::COMMAND_NAME_LOGIN                 = "LOGIN";
const char *CPCAPI2::RemoteSync::COMMAND_NAME_SET_ACCOUNTS          = "SET_ACCOUNTS";
const char *CPCAPI2::RemoteSync::COMMAND_NAME_UPDATE_CONVERSATION   = "UPDATE_CONVERSATION";
const char *CPCAPI2::RemoteSync::COMMAND_NAME_UPDATE_ITEMS          = "UPDATE_ITEMS";

// Response names (command names appended with response suffix)
const char *CPCAPI2::RemoteSync::RESPONSE_NAME_STATUS               = "STATUS_RESPONSE";

// Unsolicited messages coming from the server
const char *CPCAPI2::RemoteSync::NOTIFICATION_NAME_EVENT            = "EVENT_NOTIFICATION";
