#pragma once
#ifndef __CPCAPI2_REMOTESYNC_GETMESSAGECOUNTCMD_H__
#define __CPCAPI2_REMOTESYNC_GETMESSAGECOUNTCMD_H__

#include <cpcstl/string.h>
#include <cpcstl/vector.h>
#include <document.h>
#include <remotesync/RemoteSyncTypes.h>
#include <remotesync/RemoteSyncItemInternal.h>

#include <json/JsonHelper.h>
#include <websocket/json/WebSocketCommand.h>

#define ACCOUNT_ID "accountID"
#define TYPES      "types"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class GetMessageCountCmd : public WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
	      cpc::string accountID;
         cpc::vector< RemoteSyncItem::ItemType > types;

         GetMessageCountCmd(const cpc::string& accountID, const cpc::vector< RemoteSyncItem::ItemType >& types)
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              accountID( accountID ),
              types( types )
	      {
	      }

         const char *getCommandName() const OVERRIDE { return "GET_MESSAGE_COUNT"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         const std::string getLogIdentifier() OVERRIDE
         {
            std::string ident = getCommandName();

            for (unsigned int i=0; i < types.size(); i++)
            {
               ident += " " + RemoteSyncItemInternal::s_mapItemTypeToString[types[i]];
            }

            return getCommandName();
         }

         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);

            if( !accountID.empty() )
               CPCAPI2::Json::Write(writer, ACCOUNT_ID, accountID);

            writer.Key(TYPES);
            writer.StartArray();
            for (unsigned int i=0; i < types.size(); i++)
            {
               CPCAPI2::Json::Serialize(writer, RemoteSyncItemInternal::s_mapItemTypeToString[types[i]]);
            }
            writer.EndArray();

            writer.EndObject();
            return true;
	      }
      };
   }
}
#endif
