#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATEITEMCMD_H__
#define __CPCAPI2_REMOTESYNC_UPDATEITEMCMD_H__

#include <vector>
#include <string>
#include <document.h>
#include <stringbuffer.h>
#include <writer.h>

#include <websocket/json/WebSocketCommand.h>
#include <remotesync/RemoteSyncTypes.h>

#define SERVER_ID       "serverId"
#define CLIENT_ID       "client_id"
#define ORIGINAL_ID     "original_id"
#define STABLE_ID       "stable_id"
#define IS_READ         "isRead"
#define IS_DELETED      "isDeleted"
#define IS_EDITED       "isEdited"
#define ITEM_STATE      "itemState"
#define CALL_DURATION   "callDuration"
#define IS_USER_DELETED "isUserDeleted"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      /**
       * This class represents the data to send a command to update the specified item.
       */
      class UpdateItemCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         ServerID      serverId;
         cpc::string   clientId;
         cpc::string   originalId;
         cpc::string   stableId;
         bool          isRead;
         bool          isDeleted;
         bool          isEdited;
         bool          isUserDeleted;
         int           itemState;
         int           callDuration;
         int64_t       deliveryTimestamp;
         int64_t       readTimestamp;

         UpdateItemCmd(ServerID serverId, const cpc::string& clientId, const cpc::string& originalId, const cpc::string& stableId, bool isRead, bool isDeleted, bool isEdited, bool isUserDeleted, int itemState, int callDuration, int64_t deliveryTimestamp = 0, int64_t readTimestamp = 0) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            serverId(serverId),
            clientId(clientId),
            originalId(originalId),
            stableId(stableId),
            isRead(isRead),
            isDeleted(isDeleted),
            isEdited(isEdited),
            isUserDeleted(isUserDeleted),
            itemState(itemState),
            callDuration(callDuration),
            deliveryTimestamp(deliveryTimestamp),
            readTimestamp(readTimestamp) {}
         virtual ~UpdateItemCmd() {}

         const char *getCommandName() const OVERRIDE { return "UPDATE_ITEM"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_hRequest);
            CPCAPI2::Json::Write(writer, SERVER_ID,         serverId);
            CPCAPI2::Json::Write(writer, CLIENT_ID,         clientId);
            CPCAPI2::Json::Write(writer, ORIGINAL_ID,       originalId);
            CPCAPI2::Json::Write(writer, STABLE_ID,         stableId);
            CPCAPI2::Json::Write(writer, IS_READ,           isRead);
            CPCAPI2::Json::Write(writer, IS_DELETED,        isDeleted);
            CPCAPI2::Json::Write(writer, IS_EDITED,         isEdited);
            CPCAPI2::Json::Write(writer, IS_USER_DELETED,   isUserDeleted);
            CPCAPI2::Json::Write(writer, ITEM_STATE,        itemState);
            CPCAPI2::Json::Write(writer, CALL_DURATION,     callDuration);

            if (deliveryTimestamp > 0)
               CPCAPI2::Json::Write(writer, RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME, deliveryTimestamp);

            if (readTimestamp > 0)
               CPCAPI2::Json::Write(writer, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, readTimestamp);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
