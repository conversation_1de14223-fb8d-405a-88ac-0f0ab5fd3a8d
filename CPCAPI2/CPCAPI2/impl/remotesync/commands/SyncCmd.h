#pragma once
#ifndef __CPCAPI2_REMOTESYNC_SYNCCMD_H__
#define __CPCAPI2_REMOTESYNC_SYNCCMD_H__

#include <cpcstl/vector.h>
#include <document.h>
#include <writer.h>
#include <stringbuffer.h>

#include <websocket/json/WebSocketCommand.h>

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class SyncCmd : public WebSocket::WebSocketCommand
      {
         RequestHandle m_hRequest;
         cpc::vector< RemoteSyncItem > items;

      public:
         SyncCmd(const cpc::vector<RemoteSyncItem>& itemsToSync) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            items( itemsToSync ) {}
         virtual ~SyncCmd() {}

         const char *getCommandName() const OVERRIDE { return "SYNC"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         const std::string getLogIdentifier() OVERRIDE
         {
            std::string ident = getCommandName();

            std::set<RemoteSyncItem::ItemType> types;
            for (size_t i = 0; i < items.size(); i++)
            {
               if (types.end() == types.find(items[i].itemType))
               {
                  types.insert(items[i].itemType);
                  ident += " " + RemoteSyncItemInternal::s_mapItemTypeToString[items[i].itemType];
               }
            }

            return ident;
         }

         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);
            CPCAPI2::Json::Write(writer, "items",            items);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif // __CPCAPI2_REMOTESYNC_SYNCCMD_H__
