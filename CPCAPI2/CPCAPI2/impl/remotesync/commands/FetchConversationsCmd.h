#pragma once
#ifndef _CPCAPI2_FETCHCONVERSATIONSCMD_H_
#define _CPCAPI2_FETCHCONVERSATIONSCMD_H_

#include <cpcstl/vector.h>
#include <cpcstl/string.h>
#include <document.h>

#include <websocket/json/WebSocketCommand.h>

#define RANGELOWEST   "lowest"
#define RANGEHIGHEST  "highest"
#define COUNT         "count"
#define OFFSET        "offset"
#define CONVERSATIONS "conversations"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class FetchConversationsCmd : public WebSocket::WebSocketCommand
      {
      public: 
         RequestHandle m_hRequest;
         int64_t lowestClientCreatedTime;  // 0 means unlimited
         int64_t highestClientCreatedTime; // 0 means unlimited

         int count;
         int offset;
         cpc::vector< cpc::string > conversationIDs;

         FetchConversationsCmd(int64_t lowestClientCreatedTime, int64_t highestClientCreatedTime, int offset, int count) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            lowestClientCreatedTime(lowestClientCreatedTime),
            highestClientCreatedTime(highestClientCreatedTime),
            count(count),
            offset(offset)
         {
         }

         FetchConversationsCmd(const cpc::vector< cpc::string >& conversationIDs) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            lowestClientCreatedTime(0),
            highestClientCreatedTime(0),
            count(0),
            offset(0),
            conversationIDs( conversationIDs )
         {
         }

         const char *getCommandName() const OVERRIDE { return "FETCH_CONVERSATIONS"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);

            if( lowestClientCreatedTime != 0 )
               CPCAPI2::Json::Write(writer, RANGELOWEST, lowestClientCreatedTime);

            if( highestClientCreatedTime != 0 )
               CPCAPI2::Json::Write(writer, RANGEHIGHEST, highestClientCreatedTime);

            CPCAPI2::Json::Write(writer, COUNT, count);
            CPCAPI2::Json::Write(writer, OFFSET, offset);

            if( !conversationIDs.empty() )
               CPCAPI2::Json::Write(writer, CONVERSATIONS, conversationIDs);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
