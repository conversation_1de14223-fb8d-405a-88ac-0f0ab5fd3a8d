#pragma once
#ifndef __CPCAPI2_GETMESSAGECOUNTRESPONSE_H__
#define __CPCAPI2_GETMESSAGECOUNTRESPONSE_H__

#include <document.h>
#include <websocket/json/WebSocketResponse.h>

#define UNREAD "unread"
#define TOTAL  "total"
#define UNREAD_CONVERSATIONS "unreadConversations"
#define TOTALCONVERSATIONS "totalConversations"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class GetMessageCountResponse : public WebSocket::WebSocketResponse
      {
      public:
         RequestHandle m_hRequest;
         int unreadMessages;
	      int totalMessages;
         int unreadConversations;
         int totalConversations;
		
	      GetMessageCountResponse()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              unreadMessages( 0 ),
              totalMessages( 0 )
	      {
	      }

         const char *getCommandName() const OVERRIDE { return "GET_MESSAGE_COUNT_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;

            m_hRequest     = inDocument[ CLIENT_REQUEST_ID ].GetInt64();
            unreadMessages = inDocument[ UNREAD ].GetInt();
            totalMessages  = inDocument[ TOTAL ].GetInt();
            unreadConversations = inDocument.HasMember(UNREAD_CONVERSATIONS) ? inDocument[ UNREAD_CONVERSATIONS ].GetInt(): 0;
            totalConversations = inDocument.HasMember(TOTALCONVERSATIONS) ? inDocument[ TOTALCONVERSATIONS ].GetInt(): 0;

            return true;
         }
      };
   }
}

#endif // __CPCAPI2_GETMESSAGECOUNTRESPONSE_H__
