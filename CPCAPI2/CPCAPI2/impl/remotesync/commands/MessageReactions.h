#pragma once
#ifndef _CPCAPI2_REMOTESYNC_MESSAGEREACTIONS_H_
#define _CPCAPI2_REMOTESYNC_MESSAGEREACTIONS_H_

#include <vector>
#include <string>
#include <document.h>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <websocket/json/WebSocketCommand.h>
#include "Command.h"

#define REVISION     "rev"
#define REQUEST_ID   "requestID"
#define REV          "rev"
#define CREATED_TIME "created_time"
#define SERVER_ID2   "server_id"       // 2 to avoid conflict with UpdateItemCmd.h
#define ADDRESS      "address"
#define VALUE        "value"
#define ASCENDING    "ascending"
#define OFFSET       "offset"
#define COUNT        "count"
#define REACTIONS    "reactions"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class MessageReactions : public WebSocket::WebSocketNotification
      {
      public:
         RequestHandle requestID;
         Revision rev;
         int64_t created_time;
         int64_t server_id;
         cpc::string address;
         cpc::string value;

         MessageReactions( void ) : rev(0), created_time(0), server_id(0) {}
         virtual ~MessageReactions() {}

         const char *getNotificationName() const OVERRIDE { return "MESSAGES_REACTIONS"; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            requestID = inDocument[ REQUEST_ID ].GetInt64();
            CPCAPI2::Json::Read(inDocument, REVISION, rev);
            CPCAPI2::Json::Read(inDocument, CREATED_TIME, created_time);
            server_id = inDocument[ SERVER_ID2 ].GetInt64();
            CPCAPI2::Json::Read(inDocument, ADDRESS, address);
            CPCAPI2::Json::Read(inDocument, VALUE, value);
            
            return true;
         }
      };


      /**
       * This class represents the data to send a command to add a reaction to the specified item.
       */
      class SendReactionCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         RemoteSyncReaction reaction;

         SendReactionCmd(const RemoteSyncReaction& reaction) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            reaction(reaction) {}
         virtual ~SendReactionCmd() {}

         const char *getCommandName() const OVERRIDE { return "MESSAGES_REACTIONS"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_hRequest);
            CPCAPI2::Json::Write(writer, REV,               reaction.rev);
            CPCAPI2::Json::Write(writer, CREATED_TIME,      reaction.created_time);
            CPCAPI2::Json::Write(writer, SERVER_ID2,        reaction.server_id);
            CPCAPI2::Json::Write(writer, ADDRESS,           reaction.address);
            CPCAPI2::Json::Write(writer, VALUE,             reaction.value);
            writer.EndObject();
            return true;
         }
      };

      /**
       * This class represents the data to send a command to get reactions.
       */
      class FetchMessagesReactionsCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         bool ascending;
         int offset;
         int count;

         FetchMessagesReactionsCmd(bool ascending, int offset, int count) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            ascending(ascending),
            offset(offset),
            count(count) {}
         virtual ~FetchMessagesReactionsCmd() {}

         const char *getCommandName() const OVERRIDE { return "FETCH_MESSAGES_REACTIONS"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,    getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID, m_hRequest);
            CPCAPI2::Json::Write(writer, ASCENDING,         ascending);
            CPCAPI2::Json::Write(writer, OFFSET,            offset);
            CPCAPI2::Json::Write(writer, COUNT,             count);
            writer.EndObject();
            return true;
         }
      };

      class FetchMessagesReactionsResponse : public WebSocket::WebSocketResponse
      {
      public:
         RequestHandle m_hRequest;
         cpc::vector<RemoteSyncReaction> reactions;
         int request_offset;
         int request_maxcount;

         FetchMessagesReactionsResponse( void )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              request_offset( 0 ),
              request_maxcount( 0 ) {}
         virtual ~FetchMessagesReactionsResponse( void ) {}

         const char *getCommandName() const OVERRIDE { return "MESSAGES_REACTIONS_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            CPCAPI2::Json::Read(inDocument, REACTIONS, reactions);
            request_maxcount = inDocument[ REQ_MAXCOUNT ].GetInt();
            request_offset = inDocument[ REQ_OFFSET ].GetInt();
            return true;
         }
      };
   }
}

#endif
