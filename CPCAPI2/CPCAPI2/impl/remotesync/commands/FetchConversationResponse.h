#pragma once
#ifndef _CPCAPI2_REMOTESYNC_FETCHCONVERSATIONRESPONSE_H_
#define _CPCAPI2_REMOTESYNC_FETCHCONVERSATIONRESPONSE_H_

#include <cpcstl/vector.h>
#include <document.h>

#include <websocket/json/WebSocketResponse.h>
#include <remotesync/RemoteSyncConversationThreadItem.h>
#include "Command.h"

#define REQ_MAXCOUNT "request_maxcount"
#define REQ_OFFSET   "request_offset"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class FetchConversationResponse : public WebSocket::WebSocketResponse
      {
      public:
         cpc::vector<RemoteSyncConversationThreadItem> items;
         RequestHandle m_hRequest;
         int request_offset;
         int request_maxcount;

         FetchConversationResponse()
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              request_offset( 0 ),
              request_maxcount( 0 )
         {}

         const char *getCommandName() const OVERRI<PERSON> { return "FETCH_CON_TOPS_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }

         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            rapidjson::Value const & array = inDocument[ CLIENT_ITEM_LIST ];
            int size = array.Size();

            for(int i=0;i<size;i++)
            {
               rapidjson::Value const & listItem = array[i];

               RemoteSyncConversationThreadItem item;

               if (listItem.HasMember("latestMessage"))
               {
                  CPCAPI2::Json::Read(listItem, "latestMessage", item.latestMessage);
                  item.hasLatestMessage = true;
               }

               if (listItem.HasMember("latestChatInfo"))
               {
                  CPCAPI2::Json::Read(listItem, "latestChatInfo", item.latestChatInfo);
                  item.hasLatestChatInfo = true;
               }

               item.unreadMessages = listItem.HasMember( "unreadMessages" ) ? listItem[ "unreadMessages" ].GetInt() : -1;
               item.totalMessages = listItem.HasMember( "totalMessages" ) ? listItem[ "totalMessages" ].GetInt() : -1;
               items.push_back(item);
            }
            request_maxcount = inDocument[ REQ_MAXCOUNT ].GetInt();
            request_offset = inDocument[ REQ_OFFSET ].GetInt();
            return true;
         }
      };
   }
}
#endif
