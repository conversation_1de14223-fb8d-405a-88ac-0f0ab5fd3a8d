#pragma once
#ifndef __CPCAPI2_REMOTESYNC_PINGRESPONSE_H__
#define __CPCAPI2_REMOTESYNC_PINGRESPONSE_H__

#include <cpcapi2defs.h>

#include <websocket/json/WebSocketResponse.h>
#include <document.h>
#include "RemoteSyncTokens.h"

#define CLIENT_TIME "clientTime"
#define SERVER_TIME "serverTime"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class PingResponse : public WebSocket::WebSocketResponse
      {
      public:
         WebSocket::RequestHandle m_hRequest;
	      int64_t clientClock;
	      int64_t serverClock;
	
	      PingResponse( void ) :
            m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
            clientClock( 0 ),
            serverClock( 0 ) {}
         virtual ~PingResponse() {}
	
         const char *getCommandName() const OVERRIDE { return "PING_CMD"; }
         const WebSocket::RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            // mandatory client time
            if( !inDocument.HasMember( CLIENT_TIME ))
               return false;
            clientClock = inDocument[ CLIENT_TIME ].GetInt64();

            // mandatory server time
            if( !inDocument.HasMember( SERVER_TIME ))
               return false;
            serverClock = inDocument[ SERVER_TIME ].GetInt64();

            return true;
	      }
      };
   }
}

#endif // __CPCAPI2_REMOTESYNC_PINGRESPONSE_H__