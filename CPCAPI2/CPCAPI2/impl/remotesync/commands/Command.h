#pragma once
#ifndef _CPCAPI2_REMOTESYNC_COMMAND_H_
#define _CPCAPI2_REMOTESYNC_COMMAND_H_

#include <cpcstl/string.h>
#include <map>
#include <string>
#include <stdint.h>

#include <remotesync/RemoteSyncItem.h>
#include <remotesync/RemoteSyncItemInternal.h>
#include <remotesync/RemoteSyncTypes.h>
#include <json/SerializationHelperMacros.h>

namespace CPCAPI2
{
   namespace RemoteSync
   {

      /**
      * An abstract base class for a transactional command unit to be written over the socket in a single message.
      */
      class Command
      {
      public:

         enum class COMMAND_NAME
         {
            COMMAND_NAME_UNKNOWN, COMMAND_NAME_ERROR,

            COMMAND_NAME_STATUS_RESPONSE,
            COMMAND_NAME_UPDATE_NOTIFICATION,

            // PING is used in both directions
            COMMAND_NAME_PING_CMD,

            COMMAND_NAME_LOGIN,               // Returns STATUS_RESPONSE
            COMMAND_NAME_SET_ACCOUNTS,        // Returns STATUS_RESPONSE
            COMMAND_NAME_UPDATE_CONVERSATION, // Returns STATUS_RESPONSE

            COMMAND_NAME_SYNC,                COMMAND_NAME_SYNC_RESPONSE,
            COMMAND_NAME_UPDATE_ITEM,         COMMAND_NAME_UPDATE_ITEM_RESPONSE,
            COMMAND_NAME_FETCH_RANGE,         COMMAND_NAME_FETCH_RESPONSE,
            COMMAND_NAME_FETCH_CONVERSATIONS, COMMAND_NAME_FETCH_CON_TOPS_RESPONSE,
      		COMMAND_NAME_GET_MESSAGE_COUNT,   COMMAND_NAME_GET_MESSAGE_COUNT_RESPONSE
         };

         // Fairly common JSON field names
         //static char const * const CLIENT_COMMAND;
         //static char const * const CLIENT_REQUEST_ID;
         //static char const * const CLIENT_ITEM_LIST;

         // Some fixed serialization strings: JSON parameter names
         static char const * const CLIENT_SYNC_ITEM_LIST;
         static char const * const CLIENT_SYNC_ITEM_TYPE;
         static char const * const CLIENT_SYNC_ITEM_CLIENTREQUESTID;
         static char const * const CLIENT_SYNC_ITEM_CREATED_TIME;
         static char const * const CLIENT_SYNC_ITEM_LAST_MODIFIED;
         static char const * const CLIENT_SYNC_ITEM_READ;
         static char const * const CLIENT_SYNC_ITEM_DELETED;
         static char const * const CLIENT_SYNC_ITEM_EDITED;
         static char const * const CLIENT_SYNC_ITEM_USERDELETED;
         static char const * const CLIENT_SYNC_ITEM_REACTIONS;
         static char const * const CLIENT_SYNC_ITEM_ORIGINAL_ID;
         static char const * const CLIENT_SYNC_ITEM_STABLE_ID;
         static char const * const CLIENT_SYNC_ITEM_SOURCE;
         static char const * const CLIENT_SYNC_ITEM_STATE;
         static char const * const CLIENT_SYNC_ITEM_FROM;
         static char const * const CLIENT_SYNC_ITEM_TO;
         static char const * const CLIENT_SYNC_ITEM_THREAD_ID;
         static char const * const CLIENT_SYNC_ITEM_SERVER_ID;
         static char const * const CLIENT_SYNC_ITEM_CONTENT;
         static char const * const CLIENT_SYNC_ITEM_CONTENT_TYPE;
         static char const * const CLIENT_SYNC_ITEM_PREEXISTS;
         static char const * const CLIENT_SYNC_ITEM_ACCOUNT;
         static char const * const CLIENT_SYNC_ITEM_UNIQUE_ID;
         static char const * const CLIENT_SYNC_ITEM_DELIVERY_TIME;
         static char const * const CLIENT_SYNC_ITEM_READ_TIME;

         //CallHistory sync
         static char const * const CLIENT_SYNC_ITEM_REMOTE_NAME;
         static char const * const CLIENT_SYNC_ITEM_URI;
         static char const * const CLIENT_SYNC_ITEM_NUMBER;
         static char const * const CLIENT_SYNC_ITEM_CALL_DURATION;
         static char const * const CLIENT_SYNC_ITEM_STATUS_CODE;
         static char const * const CLIENT_SYNC_ITEM_DEVICE_HASH;
         static char const * const CLIENT_SYNC_ITEM_DEVICE_PLATFORM;
         static char const * const CLIENT_SYNC_ITEM_DEVICE_NAME;
      };
   } // namespace RemoteSync

   namespace Json
   {
      template<class Writer>
      inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncItem& v)
      {
         w.StartObject();

         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_TYPE, RemoteSync::RemoteSyncItemInternal::s_mapItemTypeToString[v.itemType]);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_READ, v.itemRead);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DELETED, v.itemDeleted);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_EDITED, v.itemEdited);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_USERDELETED, v.itemUserDeleted);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_REACTIONS, v.reactions);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_ORIGINAL_ID, v.originalID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_STABLE_ID, v.stableID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_STATE, v.state);

         // Possibly null parameters:
         if (v.serverID > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_SERVER_ID, v.serverID);

         if (v.clientTimestamp > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CREATED_TIME, v.clientTimestamp);

         if (!v.account.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_ACCOUNT, v.account);

         if (!v.from.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_FROM, v.from);

         if (!v.to.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_TO, v.to);

         if (!v.conversationID.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_THREAD_ID, v.conversationID);

         if (!v.uniqueID.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_UNIQUE_ID, v.uniqueID);

         if (v.source != RemoteSync::RemoteSyncItem::Source::Source_null)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_SOURCE, RemoteSync::RemoteSyncItemInternal::s_mapSourceToString[v.source]);

         if (!v.clientID.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID, v.clientID);

         if (!v.contentType.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT_TYPE, v.contentType);

         if (!v.content.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT, v.content);

         if (v.deliveryTimestamp > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME, v.deliveryTimestamp);

         if (v.readTimestamp > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, v.readTimestamp);

         if (v.itemType == RemoteSync::RemoteSyncItem::callhistory)
         {
            if (!v.callHistory.remoteName.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_REMOTE_NAME, v.callHistory.remoteName);

            if (!v.callHistory.associatedUri.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_URI, v.callHistory.associatedUri);

            if (!v.callHistory.associatedNumber.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_NUMBER, v.callHistory.associatedNumber);

            if (v.callHistory.callDuration > 0)
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CALL_DURATION, v.callHistory.callDuration);

            if (!v.callHistory.primaryDeviceHash.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_HASH, v.callHistory.primaryDeviceHash);

            if (!v.callHistory.primaryDevicePlatform.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_PLATFORM, v.callHistory.primaryDevicePlatform);

            if (!v.callHistory.primaryDeviceName.empty())
               CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_NAME, v.callHistory.primaryDeviceName);
         }

         w.EndObject();
      }

      inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncItem& v)
      {
         // ServerID may be null when sending from the client to the server.
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_SERVER_ID, v.serverID, (RemoteSync::ServerID)-1);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DELETED, v.itemDeleted);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_EDITED, v.itemEdited);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_USERDELETED, v.itemUserDeleted);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_REACTIONS, v.reactions);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_ORIGINAL_ID, v.originalID);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_STABLE_ID, v.stableID);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_READ, v.itemRead);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_STATE, v.state);
        
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_FROM, v.from);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_TO, v.to);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_THREAD_ID, v.conversationID);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_UNIQUE_ID, v.uniqueID);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CREATED_TIME, v.clientTimestamp);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_ACCOUNT, v.account);

         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_TYPE))
         {
            std::string value = json[RemoteSync::Command::CLIENT_SYNC_ITEM_TYPE].GetString();
            v.itemType = RemoteSync::RemoteSyncItemInternal::s_mapStringToItemType[value];
         }
         else
         {
            // Ummm.... what to put here?
            v.itemType = RemoteSync::RemoteSyncItem::ItemType::ItemType_null;
         }

         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_SOURCE))
         {
            std::string value = json[RemoteSync::Command::CLIENT_SYNC_ITEM_SOURCE].GetString();
            v.source = RemoteSync::RemoteSyncItemInternal::s_mapStringToSource[value];
         }
         else
         {
            v.source = RemoteSync::RemoteSyncItem::Source::Source_null;
         }

         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID))
            CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID, v.clientID);
         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT_TYPE))
            CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT_TYPE, v.contentType);
         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT))
            CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CONTENT, v.content);
         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME))
            CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME, v.deliveryTimestamp);
         if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME))
            CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, v.readTimestamp);

         if (v.itemType == RemoteSync::RemoteSyncItem::callhistory)
         {
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_REMOTE_NAME))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_REMOTE_NAME, v.callHistory.remoteName);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_URI))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_URI, v.callHistory.associatedUri);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_NUMBER))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_NUMBER, v.callHistory.associatedNumber);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_CALL_DURATION))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CALL_DURATION, v.callHistory.callDuration);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_HASH))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_HASH, v.callHistory.primaryDeviceHash);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_PLATFORM))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_PLATFORM, v.callHistory.primaryDevicePlatform);
            if (json.HasMember(RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_NAME))
               CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_NAME, v.callHistory.primaryDeviceName);
         }
      }

      template<class Writer>
      inline void Serialize(Writer& w, const CPCAPI2::RemoteSync::RemoteSyncItemUpdate& v)
      {
         w.StartObject();

         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_READ, v.itemRead);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DELETED, v.itemDeleted);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_EDITED, v.itemEdited);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_USERDELETED, v.itemUserDeleted);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_ORIGINAL_ID, v.originalID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_STABLE_ID, v.stableID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_STATE, v.itemState);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_SERVER_ID, v.serverID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CREATED_TIME, v.clientCreatedTime);
         if (!v.clientID.empty())
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID, v.clientID);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_PREEXISTS, v.preexists);

         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_CALL_DURATION, v.callDuration);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_STATUS_CODE, v.statusCode);
         CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_HASH, v.primaryDeviceHash);

         if (v.deliveryTimestamp > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME, v.deliveryTimestamp);

         if (v.readTimestamp > 0)
            CPCAPI2::Json::Write(w, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, v.readTimestamp);

         w.EndObject();
      }

      inline void Deserialize(const rapidjson::Value& json, CPCAPI2::RemoteSync::RemoteSyncItemUpdate& v)
      {
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_SERVER_ID, v.serverID);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_READ, v.itemRead);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DELETED, v.itemDeleted);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_EDITED, v.itemEdited);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_USERDELETED, v.itemUserDeleted);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_ORIGINAL_ID, v.originalID);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_STABLE_ID, v.stableID);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_STATE, v.itemState);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CLIENTREQUESTID, v.clientID);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CREATED_TIME, v.clientCreatedTime);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_PREEXISTS, v.preexists);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_CALL_DURATION, v.callDuration);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_STATUS_CODE, v.statusCode);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DEVICE_HASH, v.primaryDeviceHash);

         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_DELIVERY_TIME, v.deliveryTimestamp);
         CPCAPI2::Json::Read(json, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, v.readTimestamp);
      }
   } // namespace Json
}

#endif // _CPCAPI2_REMOTESYNC_COMMAND_H_
