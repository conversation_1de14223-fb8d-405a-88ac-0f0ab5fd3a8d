#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATECONVERSATIONCMD_H__
#define __CPCAPI2_REMOTESYNC_UPDATECONVERSATIONCMD_H__

#include <cpcstl/string.h>
#include <remotesync/RemoteSyncTypes.h>
#include <document.h>

#include <websocket/json/WebSocketCommand.h>

#define ACCOUNTID             "accountID"
#define CONVERSATIONID        "conversationID"
#define RANGEHIGHEST          "highest"
#define SETITEMSREAD          "setItemsRead"
#define SETITEMSDELETED       "setItemsDeleted"
#define SETITEMSUSERDELETED   "setItemsUserDeleted"
#define REVISION              "rev"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class UpdateConversationCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         cpc::string accountID;
         cpc::string conversationID;
         int64_t highestClientCreatedTime; // set to 0 to indicate all items. This refers to the client created timestamp
         Revision revision; // Ignored during requests.  Returns the revision of the [sic]
         bool setItemsRead;
         bool setItemsDeleted;
         bool setItemsUserDeleted;

         // Client constructor without revision
         UpdateConversationCmd( const cpc::string& accountID, const cpc::string& conversationID, int64_t highestClientCreatedTime, bool setItemsRead, bool setItemsDeleted, bool setItemsUserDeleted )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              accountID( accountID ),
              conversationID( conversationID ),
              highestClientCreatedTime( highestClientCreatedTime ),
              setItemsRead( setItemsRead ),
              setItemsDeleted( setItemsDeleted ),
              setItemsUserDeleted( setItemsUserDeleted ),
              revision(0)
         {
         }
	
         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_UPDATE_CONVERSATION; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);

            if( !accountID.empty() )
               CPCAPI2::Json::Write(writer, ACCOUNTID, accountID);

            if( !conversationID.empty() )
               CPCAPI2::Json::Write(writer, CONVERSATIONID, conversationID);

            if( highestClientCreatedTime != 0 )
               CPCAPI2::Json::Write(writer, RANGEHIGHEST, highestClientCreatedTime);

            CPCAPI2::Json::Write(writer, SETITEMSDELETED, setItemsDeleted);
            CPCAPI2::Json::Write(writer, SETITEMSREAD, setItemsRead);
            CPCAPI2::Json::Write(writer, SETITEMSUSERDELETED, setItemsUserDeleted);
            CPCAPI2::Json::Write(writer, REVISION, revision);

            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
