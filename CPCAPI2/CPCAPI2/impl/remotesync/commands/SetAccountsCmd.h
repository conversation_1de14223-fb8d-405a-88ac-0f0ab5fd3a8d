#pragma once
#ifndef _CPCAPI2_REMOTESYNC_SETACCOUNTSCMD_H_
#define _CPCAPI2_REMOTESYNC_SETACCOUNTSCMD_H_

#include <cpcstl/vector.h>
#include <cpcstl/string.h>
#include <document.h>

#include <cpcapi2defs.h>

#include <json/JsonHelper.h>
#include <websocket/json/WebSocketCommand.h>

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class SetAccountsCmd : public CPCAPI2::WebSocket::WebSocketCommand
      {
      public:
         RequestHandle m_hRequest;
         cpc::vector<cpc::string> accounts;

         SetAccountsCmd(const cpc::vector<cpc::string>& accounts)
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              accounts(accounts) {}
         virtual ~SetAccountsCmd() {}

         const char *getCommandName() const OVERRIDE { return COMMAND_NAME_SET_ACCOUNTS; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool toString( const RequestHandle& hRequest, std::string &outString ) OVERRIDE
         {
            m_hRequest = hRequest;
            CPCAPI2::Json::StdStringBuffer buffer(outString);
            CPCAPI2::Json::StdStringWriter writer(buffer);

            writer.StartObject();
            CPCAPI2::Json::Write(writer, CLIENT_COMMAND,     getCommandName());
            CPCAPI2::Json::Write(writer, CLIENT_REQUEST_ID,  m_hRequest);
            CPCAPI2::Json::Write(writer, "accounts",  accounts);
            writer.EndObject();
            return true;
         }
      };
   }
}

#endif
