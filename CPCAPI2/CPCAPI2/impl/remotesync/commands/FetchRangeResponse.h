#pragma once
#ifndef __CPCAPI2_REMOTESYNC_FETCHRANGERESPONSE_H__
#define __CPCAPI2_REMOTESYNC_FETCHRANGERESPONSE_H__

#include <cpcstl/vector.h>
#include <document.h>

#include <websocket/json/WebSocketResponse.h>
#include <remotesync/RemoteSyncItem.h>
#include "Command.h"

#define REQ_MAXCOUNT "request_maxcount"
#define REQ_OFFSET   "request_offset"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      class FetchRangeResponse : public WebSocket::WebSocketResponse
      {
      public:
         RequestHandle m_hRequest;
         cpc::vector<RemoteSyncItem> items;
         int request_offset;
         int request_maxcount;

         FetchRangeResponse( void )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              request_offset( 0 ),
              request_maxcount( 0 ) {}
         virtual ~FetchRangeResponse( void ) {}

         const char *getCommandName() const OVERRIDE { return "FETCH_RESPONSE"; }
         const RequestHandle getRequestHandle() const OVERRIDE { return m_hRequest; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getCommandName() )
               return false;

            // Fetch the request ID (mandatory)
            if( !inDocument.HasMember( CLIENT_REQUEST_ID ))
               return false;
            m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            CPCAPI2::Json::Read(inDocument, CLIENT_ITEM_LIST, items);
            request_maxcount = inDocument[ REQ_MAXCOUNT ].GetInt();
            request_offset = inDocument[ REQ_OFFSET ].GetInt();
            return true;
         }
      };

   }
}

#endif
