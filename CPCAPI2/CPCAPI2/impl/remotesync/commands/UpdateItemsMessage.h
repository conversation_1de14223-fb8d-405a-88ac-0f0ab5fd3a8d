#pragma once
#ifndef __CPCAPI2_REMOTESYNC_UPDATEITEMSMESSAGE_H__
#define __CPCAPI2_REMOTESYNC_UPDATEITEMSMESSAGE_H__

#include <cpcstl/string.h>
#include <document.h>

#include <cpcapi2defs.h>
#include <websocket/json/WebSocketNotification.h>
#include <remotesync/RemoteSyncTypes.h>
#include "RemoteSyncTokens.h"

#define ISREAD          "isRead"
#define ISDELETED       "isDeleted"
#define ISEDITED        "isEdited"
#define ACCOUNTS        "accounts"
#define ITEMTYPES       "item_types"
#define CONVERSATIONIDS "conversation_ids"
#define SERVERIDS       "server_ids"
#define REACTIONS       "reactions"

namespace CPCAPI2
{
   namespace RemoteSync
   {
      /**
       * This class is 'so named', i.e. as "message" and not "response" or
       * "notification", because the message itself is pulling DOUBLE DUTY
       * as both a response and a notification :-P
       *
       * I'm not sure why that happened but that's just the way it is on the
       * server side.
       *
       * Also note that the Command "(Cmd)" is essentially ALSO the same thing,
       * but since that's going to a string the code isn't reused here
       *
       * Anyway. The point is that the only differentiation between a response
       * and a notification is that, a response will have a proper requestID
       * whereas a notification will have it set to zero, or not at all.
       */
      class UpdateItemsMessage : public CPCAPI2::WebSocket::WebSocketNotification
      {
      public:
         RequestHandle m_hRequest;
         bool isRead;
         bool isDeleted;
         bool isEdited;

         cpc::vector<cpc::string> accounts;
         cpc::vector<RemoteSyncItem::ItemType> item_types;
         cpc::vector<cpc::string> conversation_ids;
         cpc::vector<int64_t> server_ids;

         int64_t readTimestamp;

         UpdateItemsMessage( void )
            : m_hRequest( CPCAPI2_JSON_NO_REQUEST_ID ),
              isRead( false ),
              isDeleted( false ),
              isEdited( false ),
              readTimestamp( 0 ) {}
         virtual ~UpdateItemsMessage() {}

         /**
          * Returns true if this is a valid response, otherwise it will be a
          * notification. This is determined based on the presence of the
          * client request ID. This method is only valid to be checked AFTER
          * parsing the document (calling fromString)
          */
         bool isResponse( void ) const
         {
            return ( m_hRequest != CPCAPI2_JSON_NO_REQUEST_ID && m_hRequest != 0 );
         }

         const char *getNotificationName() const OVERRIDE { return COMMAND_NAME_UPDATE_ITEMS; }
         bool fromString( const std::string &inString ) OVERRIDE
         {
            rapidjson::Document inDocument;
            inDocument.Parse<0>( inString.c_str() );

            if( !inDocument.HasMember( CLIENT_COMMAND ))
               return false;

            // Compare the command name
            std::string cmd( inDocument[ CLIENT_COMMAND ].GetString() );
            if( cmd != getNotificationName() )
               return false;

            // Fetch the request ID (this will be zero in the event of a notification,
            // otherwise it will be set to something if the client initiated the update)
            if( inDocument.HasMember( CLIENT_REQUEST_ID ))
               m_hRequest = inDocument[ CLIENT_REQUEST_ID ].GetInt64();

            if (inDocument.HasMember(ISREAD))
               isRead = inDocument[ISREAD].GetBool();

            if (inDocument.HasMember(ISDELETED))
               isDeleted = inDocument[ISDELETED].GetBool();

            if (inDocument.HasMember(ISEDITED))
               isEdited = inDocument[ISEDITED].GetBool();

            CPCAPI2::Json::Read(inDocument, ACCOUNTS, accounts);
            CPCAPI2::Json::Read(inDocument, CONVERSATIONIDS, conversation_ids);
            CPCAPI2::Json::Read(inDocument, SERVERIDS, server_ids);

            std::vector<std::string> cItemTypes;
            CPCAPI2::Json::Read(inDocument, ITEMTYPES, cItemTypes);
            for (const std::string& itp : cItemTypes)
            {
               item_types.push_back(RemoteSyncItemInternal::s_mapStringToItemType[itp]);
            }

            CPCAPI2::Json::Read(inDocument, RemoteSync::Command::CLIENT_SYNC_ITEM_READ_TIME, readTimestamp);

            return true;
         }
      };
   }
}

#endif
