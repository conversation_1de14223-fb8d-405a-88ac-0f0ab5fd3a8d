#pragma once

#if !defined(CPCAPI2_RECORDING_MANAGER_INTERFACE_H)
#define CPCAPI2_RECORDING_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "call/SipConversationTypes.h"
#include "recording/RecordingHandler.h"
#include "recording/RecordingManager.h"
#include "../util/cpc_thread.h"
#include "../util/DumFpCommand.h"
#include "../util/AutoTestProcessor.h"
#include "../phone/PhoneModule.h"
#include "../experimental/phone/Permissions.h"

#include <rutil/Fifo.hxx>
#include <resip/dum/DumCommand.hxx>

#include <voe_file.h>

namespace CPCAPI2
{
class PhoneInterface;
   
namespace Media
{
class MediaManagerInterface;
}

namespace Recording
{

class RecordingManagerInterface : public RecordingManager,
                              public PhoneModule, public webrtc::VoEFileRecorderObserver
#ifdef CPCAPI2_AUTO_TEST
                            , public AutoTestProcessor
#endif
{
public:
   RecordingManagerInterface(Phone* phone);
   virtual ~RecordingManagerInterface();
   virtual void Release() OVERRIDE;

   virtual int setHandler(RecorderHandle recorder, RecordingHandler* handler) OVERRIDE;
   virtual RecorderHandle audioRecorderCreate(const cpc::string& filePath, bool splitMedia = false) OVERRIDE;
   virtual int recorderAddConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation) OVERRIDE;
   virtual int recorderRemoveConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation) OVERRIDE;
   virtual int recorderStart(RecorderHandle recorder) OVERRIDE;
   virtual int recorderPause(RecorderHandle recorder) OVERRIDE;
   virtual int recorderDestroy(RecorderHandle recorder) OVERRIDE;
   virtual int getRecorderFilenames(RecorderHandle recorder) OVERRIDE;

   int setHandlerImpl(RecorderHandle recorder, RecordingHandler* handler);
   int audioRecorderCreateImpl(RecorderHandle recorder, const cpc::string& filePath, bool splitMedia);
   int audioRecorderNotifyCreateErrorImpl(RecorderHandle recorder);
   int recorderStartImpl(RecorderHandle recorder);
   int recorderPauseImpl(RecorderHandle recorder);
   int recorderDestroyImpl(RecorderHandle recorder);
   int getRecorderFilenamesImpl(RecorderHandle recorder);

   // webrtc::VoEFileRecorderObserver
   void OnRecordingStopped(int fileRecorder) OVERRIDE;

   virtual int process(unsigned int timeout) OVERRIDE;
   virtual void interruptProcess() OVERRIDE;
   virtual void setCallbackHook(void (*cbHook)(void*), void* context);

   resip::Fifo<resip::ReadCallbackBase>* callbackFifo();
   const std::function<void(void)> cbHook() const;
   void post(resip::ReadCallbackBase* f);
   void postCallback(resip::ReadCallbackBase*);

   PhoneInterface* phoneInterface();
   void onPermissionGranted(int requestCode, CPCAPI2::Permission permission);

#ifdef CPCAPI2_AUTO_TEST
   virtual AutoTestReadCallback* process_test(int timeout) OVERRIDE;
#endif

private:
   void fireRecorderFilenames(int fileRecorder, bool completed);

   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
   PhoneInterface* mPhone;
   Media::MediaManagerInterface *mMediaIf;
   std::function<void(void)> mCbHook;

   RecorderHandle mNextRecorderHandle;
   std::map<RecorderHandle, RecordingHandler*> mHandlers;

   typedef std::map<RecorderHandle, cpc::string> RecorderStringMap;
   typedef std::map<RecorderHandle, enum RecordingStreamError> RecorderErrorMap;
   RecorderStringMap mFileNames;
   RecorderErrorMap mFileErrors;

   typedef std::set<RecorderHandle> PendingList;
   PendingList mPendingCreateRecording;
   PendingList mPendingStartRecording;
};
}
}
#endif // CPCAPI2_RECORDING_MANAGER_INTERFACE_H
