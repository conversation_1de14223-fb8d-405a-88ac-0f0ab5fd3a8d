#include "brand_branded.h"

#include "interface/public/recording/RecordingManager.h"

#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
#include "RecordingManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace Recording
{

RecordingManager* RecordingManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<RecordingManagerInterface>(phone, "RecordingManager");
#else
   return NULL;
#endif
}

}
}
