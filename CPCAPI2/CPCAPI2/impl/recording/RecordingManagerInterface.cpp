#include "brand_branded.h"

#if (CPCAPI2_BRAND_RECORDING_MODULE == 1)
#include "cpcapi2utils.h"
#include "RecordingManagerInterface.h"
#include "../phone/PhoneModule.h"
#include "../phone/PhoneInterface.h"
#include "../media/MediaManagerInterface.h"
#include "../call/SipAVConversationManagerInterface.h"

#include "../util/cpc_logger.h"
#include <rutil/Log.hxx>

#include <MediaStackImpl.hxx>
#include <RtpStreamImpl.hxx>

#include <voe_base.h>
#include <voe_errors.h>

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::MEDIA

using namespace resip;

namespace CPCAPI2
{
namespace Recording
{
   
void RecordingManagerInterface::Release()
{
   delete this;
}

RecordingManagerInterface::RecordingManagerInterface(Phone* phone)
   : mShutdown(false),
     mPhone(dynamic_cast<PhoneInterface*>(phone)),
     mNextRecorderHandle(1)
{
   mMediaIf = dynamic_cast<Media::MediaManagerInterface*>(Media::MediaManager::getInterface(phone));
}

RecordingManagerInterface::~RecordingManagerInterface()
{
   mShutdown = true;
   interruptProcess();
}

int RecordingManagerInterface::setHandler(RecorderHandle recorder, RecordingHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&RecordingManagerInterface::setHandlerImpl, this, recorder, handler);
   if (handler == NULL)
   {
      mPhone->getSdkModuleThread().execute(f);
      process(-1);
   }
   else
   {
      post(f);
   }
   
   return kSuccess;
}

int RecordingManagerInterface::setHandlerImpl(RecorderHandle recorder, RecordingHandler* handler)
{
   if (handler == NULL)
   {
      mHandlers.erase(recorder);
      mFileNames.erase(recorder);
      mFileErrors.erase(recorder);
      mPendingCreateRecording.erase(recorder);
      mPendingStartRecording.erase(recorder);
   }
   else
   {
      mHandlers[recorder] = handler;
   }

   return kSuccess;
}

RecorderHandle RecordingManagerInterface::audioRecorderCreate(const cpc::string& filePath, bool splitMedia)
{
   RecorderHandle h = mNextRecorderHandle++;

   mFileNames[h] = filePath;

   if (!phoneInterface()->hasFilePermission(Permission_WriteFiles, filePath))
   {
      phoneInterface()->requestPermission(0, Permission_WriteFiles);
      mPendingCreateRecording.insert(h);
      mFileErrors[h] = RecordingStreamError_Permissions;
      InfoLog(<< "Cannot open recording output file due to missing permission.");
   }

   post(resip::resip_bind(&RecordingManagerInterface::audioRecorderCreateImpl, this, h, filePath, splitMedia));
   return h;
}

int RecordingManagerInterface::audioRecorderCreateImpl(RecorderHandle recorder, const cpc::string& filePath, bool splitMedia)
{
   if( mMediaIf->media_stack()->file()->CreateFileRecorder(recorder, filePath.c_str(), this, NULL, -1, splitMedia) == -1 )
   {
      // dispatch to queue to let app attach callback
      post(resip::resip_bind(&RecordingManagerInterface::audioRecorderNotifyCreateErrorImpl, this, recorder));
   }
   
   return kSuccess;
}

int RecordingManagerInterface::audioRecorderNotifyCreateErrorImpl(RecorderHandle recorder)
{
   std::map<RecorderHandle, RecordingHandler*>::const_iterator it = mHandlers.find(recorder);
   
   if(it != mHandlers.end())
   {
      int lastError = mMediaIf->media_stack()->voe_base()->LastError();
      
      RecordingStoppedEvent args;
      args.recorder = recorder;
      
      if(lastError == VE_BAD_FILE)
         args.reason = RecordingStopReason_BadFile;
      else
         args.reason = RecordingStopReason_Unknown;

      if(lastError == VE_BAD_FILE)
         mFileErrors[it->first] = RecordingStreamError_InvalidPath;
      else if(lastError == VE_NOT_AUTHORISED)
         mFileErrors[it->first] = RecordingStreamError_Permissions;
      else
         mFileErrors[it->first] = RecordingStreamError_Unknown;

      ReadCallbackBase* callback = makeFpCommand(RecordingHandler::onRecordingStoppedEx, it->second, recorder, args);
      postCallback(callback);
      
      // Deprecated version...
      callback = makeFpCommand1(RecordingHandler::onRecordingStopped, it->second, args);
      postCallback(callback);
   }
   
   return kSuccess;
}

int RecordingManagerInterface::recorderAddConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation)
{
   SipConversation::SipAVConversationManagerInterface *sipAVConversationManagerIf = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(phoneInterface()));
   
   return sipAVConversationManagerIf->addToRecorder(conversation, recorder);
}

int RecordingManagerInterface::recorderRemoveConversation(RecorderHandle recorder, CPCAPI2::SipConversation::SipConversationHandle conversation)
{
   SipConversation::SipAVConversationManagerInterface *sipAVConversationManagerIf = dynamic_cast<SipConversation::SipAVConversationManagerInterface*>(SipConversation::SipConversationManager::getInterface(phoneInterface()));
   
   return sipAVConversationManagerIf->removeFromRecorder(conversation, recorder);
}
   
int RecordingManagerInterface::recorderStart(RecorderHandle recorder)
{
   RecorderStringMap::iterator filePath = mFileNames.find(recorder);
   if (filePath != mFileNames.end() && !phoneInterface()->hasFilePermission(Permission_WriteFiles, filePath->second))
   {
      phoneInterface()->requestPermission(0, Permission_WriteFiles);
      mPendingStartRecording.insert(recorder);
      mFileErrors[recorder] = RecordingStreamError_Permissions;
      InfoLog(<< "Cannot open recording output file due to missing permission.");
      return kError;
   }

   post(resip::resip_bind(&RecordingManagerInterface::recorderStartImpl, this, recorder));
   return kSuccess;
}

int RecordingManagerInterface::recorderStartImpl(RecorderHandle recorder)
{
   int res = mMediaIf->media_stack()->file()->StartRecording(recorder);
   if (res == -1)
   {
      // dispatch to queue to let app attach callback
      post(resip::resip_bind(&RecordingManagerInterface::audioRecorderNotifyCreateErrorImpl, this, recorder));
   }
   return res;
}

int RecordingManagerInterface::recorderPause(RecorderHandle recorder)
{
   post(resip::resip_bind(&RecordingManagerInterface::recorderPauseImpl, this, recorder));
   return kSuccess;
}

int RecordingManagerInterface::recorderPauseImpl(RecorderHandle recorder)
{
   mPendingStartRecording.erase(recorder);
   return mMediaIf->media_stack()->file()->StopRecording(recorder);
}

int RecordingManagerInterface::recorderDestroy(RecorderHandle recorder)
{
   post(resip::resip_bind(&RecordingManagerInterface::recorderDestroyImpl, this, recorder));
   return kSuccess;
}

int RecordingManagerInterface::recorderDestroyImpl(RecorderHandle recorder)
{
   // make sure any currently recording files are properly closed, the app may want to rename those files in the event fired below
   int ret = mMediaIf->media_stack()->file()->CloseFileRecorder(recorder);

   std::map<RecorderHandle, RecordingHandler*>::const_iterator it = mHandlers.find(recorder);
   
   if(it != mHandlers.end())
   {
      fireRecorderFilenames(recorder, true);
   }

   mFileNames.erase(recorder);
   mFileErrors.erase(recorder);
   mPendingCreateRecording.erase(recorder);
   mPendingStartRecording.erase(recorder);
   return ret;
}

int RecordingManagerInterface::getRecorderFilenames(RecorderHandle recorder)
{
   post(resip::resip_bind(&RecordingManagerInterface::getRecorderFilenamesImpl, this, recorder));
   return kSuccess;
}

int RecordingManagerInterface::getRecorderFilenamesImpl(RecorderHandle recorder)
{
   fireRecorderFilenames(recorder, false);

   return kSuccess;
}

// webrtc::VoEFileRecorderObserver
void RecordingManagerInterface::OnRecordingStopped(int fileRecorder)
{
   std::map<RecorderHandle, RecordingHandler*>::const_iterator it = mHandlers.find((RecorderHandle)fileRecorder);
   
   if(it != mHandlers.end())
   {
      RecordingStoppedEvent args;
      args.recorder = (RecorderHandle)fileRecorder;
      args.reason = RecordingStopReason_NoSpace;
      
      ReadCallbackBase* callback = makeFpCommand(RecordingHandler::onRecordingStoppedEx, it->second, fileRecorder, args);
      postCallback(callback);

      // Deprecated version...
      callback = makeFpCommand1(RecordingHandler::onRecordingStopped, it->second, args);
      postCallback(callback);
   }
   
   fireRecorderFilenames(fileRecorder, true);
}
   
void RecordingManagerInterface::fireRecorderFilenames(int fileRecorder, bool completed)
{
   std::map<RecorderHandle, RecordingHandler*>::const_iterator it = mHandlers.find((RecorderHandle)fileRecorder);
   
   if(it != mHandlers.end())
   {
      std::vector<std::string> filenames = mMediaIf->media_stack()->file()->GetFileRecorderFilenames(fileRecorder);
      RecordingStreams streams = filenames.size() == 1 ? RecordingStreams::RecordingStreams_Both : RecordingStreams::RecordingStreams_Local;

      enum RecordingStreamError error = RecordingStreamError_None;
      RecorderErrorMap::iterator it2 = mFileErrors.find((RecorderHandle)fileRecorder);
      if (it2 != mFileErrors.end())
         error = it2->second;

      RecorderFilenamesEvent args2;
      for (std::vector<std::string>::const_iterator it2 = filenames.begin(); it2 != filenames.end(); it2++)
      {
         RecorderFilenameInfo info;
         
         info.completed = completed;
         info.includedStreams = streams;
         info.filename = it2->c_str();
         info.error = error;
         args2.files.push_back(info);
         
         streams = RecordingStreams::RecordingStreams_Remote; // if we go around the loop again
      }

      ReadCallbackBase* callback = makeFpCommand(RecordingHandler::onRecorderFilenames, it->second, (RecorderHandle)fileRecorder, args2);
      postCallback(callback);
   }
}
   
PhoneInterface* RecordingManagerInterface::phoneInterface()
{
   return mPhone;
}

void RecordingManagerInterface::onPermissionGranted(int requestCode, CPCAPI2::Permission permission)
{
   if (Permission_WriteFiles == permission)
   {
      for (PendingList::iterator it = mPendingCreateRecording.begin(); it != mPendingCreateRecording.end(); ++it)
      {
         mFileErrors.erase(*it);
         RecorderStringMap::iterator filePath = mFileNames.find(*it);
         if (filePath != mFileNames.end())
         {
            mFileErrors.erase(filePath->first);
           InfoLog(<< "Granted write files permission. Reopening output file " << filePath->second);
            mMediaIf->media_stack()->file()->CloseFileRecorder(*it);
            mMediaIf->media_stack()->file()->CreateFileRecorder(*it, filePath->second, this);
         }
      }
      mPendingCreateRecording.clear();
      
      for (PendingList::iterator it = mPendingStartRecording.begin(); it != mPendingStartRecording.end(); ++it)
      {
         InfoLog(<< "Granted write files permission. Starting recorder.");
         recorderStart(*it);
      }
      mPendingStartRecording.clear();
   }
}

resip::Fifo<resip::ReadCallbackBase>* RecordingManagerInterface::callbackFifo()
{
   return &mCallbackFifo;
}

int RecordingManagerInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kRecordingModuleDisabled;
   }
   ReadCallbackBase* fp = mCallbackFifo.getNext(timeout);
   while(fp)
   {
      (*fp)();
      delete fp;
      if (mShutdown)
      {
         return kRecordingModuleDisabled;
      }
      fp = mCallbackFifo.getNext(kBlockingModeNonBlocking);
   }
   return kSuccess;
}

void RecordingManagerInterface::post(ReadCallbackBase* f)
{
   mPhone->getSdkModuleThread().post(f);
}

void RecordingManagerInterface::interruptProcess()
{
   mCallbackFifo.add(new ReadCallbackNoOp);
}
   
void RecordingManagerInterface::postCallback(ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}
   
void RecordingManagerInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

const std::function<void(void)> RecordingManagerInterface::cbHook() const
{
   return mCbHook;
}

#ifdef CPCAPI2_AUTO_TEST

AutoTestReadCallback* RecordingManagerInterface::process_test(int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return NULL;
   }
   resip::ReadCallbackBase* rcb = mCallbackFifo.getNext(timeout);
   AutoTestReadCallback* fpCmd = dynamic_cast<AutoTestReadCallback*>(rcb);
   if (fpCmd != NULL)
   {
      return fpCmd;
   }
   if (rcb != NULL)
   {
      return new AutoTestReadCallback(rcb, "", std::make_tuple(0,0));
   }
   return NULL;
}

#endif

}
}
#endif
