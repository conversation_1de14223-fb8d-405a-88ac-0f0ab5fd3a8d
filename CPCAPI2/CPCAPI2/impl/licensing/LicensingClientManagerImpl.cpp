#include "brand_branded.h"
#include "LicensingClientManagerImpl.h"
#include "LicensingClientSyncHandler.h"
#include "timebomb/TimeBombLicense.h"
#include "signature/SignatureManager.h"
#include "../util/cpc_logger.h"
#include "../util/DumFpCommand.h"

// Macros to simplify firing of events
//
// Assumptions:
// 1) Classes using these macros have a list of SDK observers in a variable named "mSdkObservers",
// 2) They also have the application handler in a variable named "mAppHandler".
// 3) The account is accessable via a variable named "mAccount".
//
// Should these assumptions change the macros will need additional parameters.
//
#ifndef FIRE_INTERNAL
#define FIRE_INTERNAL( CBK, HANDLE, ARG ) { \
   for (std::set<LicensingClientHandler*>::iterator itHandler = mSdkObservers.begin(); itHandler != mSdkObservers.end(); ++itHandler) \
   { \
      resip::ReadCallbackBase* cb = resip::resip_bind( &CBK, *itHandler, (HANDLE), (ARG)); \
      if( dynamic_cast< LicensingClientSyncHandler * >( *itHandler ) != NULL ) \
      { \
         (*cb)(); \
         delete cb; \
      } \
      else \
      { \
         mLicensingClientManagerInterface.postCallback(cb); \
      } \
   } \
}
#endif /* FIRE_INTERNAL */

#ifndef FIRE_ALL
#define FIRE_ALL( CBK, HANDLE, ARG ) { \
   FIRE_INTERNAL( CBK, HANDLE, ARG ); \
   resip::ReadCallbackBase* cb = makeFpCommand( CBK, mHandler, (HANDLE), (ARG) ); \
   if (mHandler != (void*)0xDEADBEEF && dynamic_cast< LicensingClientSyncHandler* >(mHandler) != NULL) \
   { \
      (*cb)(); \
      delete cb; \
   } \
   else \
   { \
      mLicensingClientManagerInterface.postCallback(cb); \
   } \
}
#endif /* FIRE_ALL */

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::LICENSING

namespace CPCAPI2
{
namespace Licensing
{

bool LicensingClientManagerImpl::sValidateAttempted = false;

LicensingClientManagerImpl::LicensingClientManagerImpl(LicensingClientHandle handle, LicensingClientManagerInterface& licensingClientManagerInterface) : 
   mHandle(handle),
   mHandler(NULL),
   mLicensingClientManagerInterface(licensingClientManagerInterface)
{
}

LicensingClientManagerImpl::~LicensingClientManagerImpl()
{
   // Destroy the client info objects
   for (LicensingClientInfoMap::const_iterator it = mLicensingClientInfoMap.begin(); it != mLicensingClientInfoMap.end(); it++)
   {
      LicensingClientInfo* clientInfo = it->second;
      delete clientInfo;
   }
}

void LicensingClientManagerImpl::addSdkObserver(LicensingClientHandler* sdkObserver)
{
   mSdkObservers.insert(sdkObserver);
}

void LicensingClientManagerImpl::removeSdkObserver(LicensingClientHandler* sdkObserver)
{
   mSdkObservers.erase(sdkObserver);
}

void LicensingClientManagerImpl::setHandler(LicensingClientHandler* handler)
{
   mHandler = handler;
}

LicensingClientInfo* LicensingClientManagerImpl::createLicensingClientInfo(LicensingClientHandle client)
{
   assert(mLicensingClientInfoMap[client] == NULL);

   LicensingClientInfo* clientInfo = new LicensingClientInfo(mLicensingClientManagerInterface.getPhoneInterface(), client, this);
   mLicensingClientInfoMap[client] = clientInfo;

   return clientInfo;
}

LicensingClientInfo* LicensingClientManagerImpl::getLicensingClientInfo(LicensingClientHandle client) const
{
   LicensingClientInfoMap::const_iterator it = mLicensingClientInfoMap.find(client);
   return (it != mLicensingClientInfoMap.end()) ? it->second : NULL;
}

LicensingClientInfo* LicensingClientManagerImpl::removeLicensingClientInfo(LicensingClientHandle client)
{
   // Remove the client info from the map
   LicensingClientInfoMap::const_iterator it = mLicensingClientInfoMap.find(client);
   if (it != mLicensingClientInfoMap.end())
   {
      LicensingClientInfo* clientInfo = it->second;
      assert(clientInfo->mHandle == client);
      mLicensingClientInfoMap.erase(it);
      return clientInfo;
   }

   // Client info not found
   return NULL;
}

bool LicensingClientManagerImpl::appResourceSignatureCheckEnabled()
{
#if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)
   #if (defined(SKIP_SIGNATURE_CHECK))
      return false;
   #else
      return true;
   #endif // SKIP_SIGNATURE_CHECK
#else
   return false;
#endif // CPCAPI2_BRAND_SIGNATURE_MODULE
}

void LicensingClientManagerImpl::validateLicenses(LicensingClientInfo *clientInfo)
{
   assert(clientInfo);

   // This is checked by the SDK to ensure that the application layer did indeed use the LicensingClientManager interface
   // to do licensing; SDK customers don't have this interface/header so they won't be able to manually call it without some hacking on their part.
   // The goal here is to ensure that an SDK customer cannot simply grab the SDK DLL from a Bria install and start using that instead of the DLL
   // that came with their SDK package.
   LicensingClientManagerImpl::sValidateAttempted = true;

   LicensingClientSettings& settings = clientInfo->mSettings;
   int expiryYear = settings.expiryYear;
   int expiryMonth = settings.expiryMonth;
   int expiryDay = settings.expiryDay;

   // Check the time limit license if specified
    if (expiryYear > 0 && expiryMonth > 0 && expiryDay > 0)
   {
      DebugLog(<< "Checking time limit license.");
      clientInfo->mTimeLimitLicense = true;
      if (!TimeBombLicense::isLicenseValid(expiryYear, expiryMonth, expiryDay, clientInfo->mTimeLimitRemaining))
      {
         ValidateLicensesFailureEvent event;
         event.status = LicenseStatus_Expired;
         fireValidateLicensesFailure(clientInfo->mHandle, event);
         return;
      }
   }
   else
   {
      // No time limit check
      clientInfo->mTimeLimitLicense = false;
      clientInfo->mTimeLimitRemaining = 0;
   }
   
#if (CPCAPI2_BRAND_SIGNATURE_MODULE == 1)
   if (LicensingClientManagerImpl::appResourceSignatureCheckEnabled())
   {
      PhoneInterface* phone = mLicensingClientManagerInterface.getPhoneInterface();
      bool sigValid = Signature::SignatureManager::getInterface(phone)->brandedSignatureCheck();
      if (!sigValid)
      {
         ValidateLicensesFailureEvent event;
         event.status = LicenseStatus_BadAppResourceSignature;
         fireValidateLicensesFailure(clientInfo->mHandle, event);
         return;
      }
   }
#endif

#ifdef CPCAPI2_AUTO_TEST
   if (settings.licenseKeys.empty())
   {
      // Fire the event that contains the result of the license/timebomb check
      ValidateLicensesSuccessEvent event;
      event.timeLimitLicense = clientInfo->mTimeLimitLicense;
      event.timeLimitRemaining = clientInfo->mTimeLimitRemaining;
      fireValidateLicensesSuccess(clientInfo->mHandle, event);
      return;
   }
#endif

   // The OnLicenseState() callback will be invoked with the license validation results and will then fire the response event
   InfoLog(<< "Doing licensing (full)");
   clientInfo->mLicensing->Initialize(settings, clientInfo);
}

void LicensingClientManagerImpl::OnLicenseState(const CPCAPI2::Licensing::LicenseStateEvent &event)
{
   LicensingClientInfo* clientInfo = (LicensingClientInfo*) event.getContext();

   if (event.GetLicenseStatus() == LicenseStatus_Pending)
   {
      return;
   }

   if (event.GetLicenseStatus() == LicenseStatus_Valid)
   {
      // License key validation process successfully completed
      ValidateLicensesSuccessEvent args = licenseStateEventToValidateLicensesSuccessEvent(event);
      args.licenseUrl = event.GetLicenseURL().c_str();
      args.timeLimitLicense = clientInfo->mTimeLimitLicense;
      args.timeLimitRemaining = clientInfo->mTimeLimitRemaining;
      fireValidateLicensesSuccess(clientInfo->mHandle, args);
   }
   else
   {
      // The license key validation process failed
      ValidateLicensesFailureEvent args = licenseStateEventToValidateLicensesFailureEvent(event);
      fireValidateLicensesFailure(clientInfo->mHandle, args);
   }
}

void LicensingClientManagerImpl::fireValidateLicensesSuccess(LicensingClientHandle client, const ValidateLicensesSuccessEvent& args)
{
   FIRE_ALL(LicensingClientHandler::onValidateLicensesSuccess, client, args);
}

void LicensingClientManagerImpl::fireValidateLicensesFailure(LicensingClientHandle client, const ValidateLicensesFailureEvent& args)
{
   FIRE_ALL(LicensingClientHandler::onValidateLicensesFailure, client, args);
}

void LicensingClientManagerImpl::fireError(LicensingClientHandle client, const ErrorEvent& event)
{
   FIRE_ALL(LicensingClientHandler::onError, client, event);
}

void LicensingClientManagerImpl::fireError(LicensingClientHandle client, const cpc::string& msg)
{
   // Create the event
   ErrorEvent event;
   event.errorText = msg;

   // Publish the event
   fireError(client, event);
}

ValidateLicensesSuccessEvent LicensingClientManagerImpl::licenseStateEventToValidateLicensesSuccessEvent(const LicenseStateEvent& event)
{
   ValidateLicensesSuccessEvent args;

   // Copy the hardware ID
   args.hardwareId = event.GetHardwareID().c_str();

   // Copy the valid licenses
   for (LicenseList::const_iterator licenseIter = event.GetLicenses().begin(); licenseIter != event.GetLicenses().end(); licenseIter++)
   {
      LicenseInfo licenseInfo;
      licenseInfo.key = licenseIter->key.c_str();
      licenseInfo.type = licenseIter->type.c_str();
      licenseInfo.expiry = (unsigned long) licenseIter->expiry;
      licenseInfo.gracePeriod = (unsigned long) licenseIter->gracePeriod;
      for (std::vector<std::string>::const_iterator featureIter = licenseIter->features.begin(); featureIter != licenseIter->features.end(); featureIter++)
      {
         licenseInfo.features.push_back(featureIter->c_str());
      }
      args.licenses.push_back(licenseInfo);
   }

   // Copy the invalid licenses
   for (InvalidLicenseList::const_iterator invalidLicenseIter = event.GetInvalidLicenses().begin(); invalidLicenseIter != event.GetInvalidLicenses().end(); invalidLicenseIter++)
   {
      InvalidLicenseInfo invalidLicenseInfo;
      invalidLicenseInfo.id = invalidLicenseIter->id.c_str();
      invalidLicenseInfo.code = invalidLicenseIter->code;
      invalidLicenseInfo.message = invalidLicenseIter->message.c_str();
      invalidLicenseInfo.key = invalidLicenseIter->key.c_str();
      invalidLicenseInfo.type = invalidLicenseIter->type.c_str();
      invalidLicenseInfo.expiry = (unsigned long) invalidLicenseIter->expiry;
      invalidLicenseInfo.gracePeriod = (unsigned long) invalidLicenseIter->gracePeriod;
      for (std::vector<std::string>::const_iterator featureIter = invalidLicenseIter->features.begin(); featureIter != invalidLicenseIter->features.end(); featureIter++)
      {
         invalidLicenseInfo.features.push_back(featureIter->c_str());
      }
      args.invalidLicenses.push_back(invalidLicenseInfo);
   }

   return args;
}

ValidateLicensesFailureEvent LicensingClientManagerImpl::licenseStateEventToValidateLicensesFailureEvent(const LicenseStateEvent& event)
{
   ValidateLicensesFailureEvent args;

   // Copy the error code
   args.errorCode = event.GetErrorCode();
   args.status = event.GetLicenseStatus();

   return args;
}

// See comments where mValidateAttempted is set above in validateLicenses(..)
bool LicensingClientManagerImpl::ensureValidateAttempted()
{
   return LicensingClientManagerImpl::sValidateAttempted;
}

void LicensingClientManagerImpl::resetValidateAttempted()
{
   LicensingClientManagerImpl::sValidateAttempted = false;
}

}
}