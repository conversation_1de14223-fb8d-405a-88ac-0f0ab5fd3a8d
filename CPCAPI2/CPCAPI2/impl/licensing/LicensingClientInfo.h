#pragma once

#if !defined(__CPCAPI2_LICENSING_CLIENT_INFO_H__)
#define __CPCAPI2_LICENSING_CLIENT_INFO_H__

#include "brand_branded.h"
#include "cpcapi2defs.h"
#include "licensing/LicensingClientTypes.h"
#include "licensing/LicensingClientSettings.h"
#include "licensekey/Licensing.h"
#include <map>

namespace CPCAPI2
{
namespace Licensing
{

struct LicensingClientInfo
{
   LicensingClientInfo(CPCAPI2::PhoneInterface* cpcPhone, LicensingClientHandle handle, LicensingHandler* licensingHandler);
   virtual ~LicensingClientInfo();

   LicensingClientHandle mHandle;
   LicensingClientSettings mSettings;
   bool mTimeLimitLicense;     // Time limit license specified
   double mTimeLimitRemaining; // Time remaining to time limit license (in milliseconds)

   std::shared_ptr<Licensing> mLicensing;

   static LicensingClientHandle nextLicensingClientHandle;
};
typedef std::map<LicensingClientHandle, LicensingClientInfo*> LicensingClientInfoMap;

}
}

#endif // __CPCAPI2_LICENSING_CLIENT_INFO_H__
