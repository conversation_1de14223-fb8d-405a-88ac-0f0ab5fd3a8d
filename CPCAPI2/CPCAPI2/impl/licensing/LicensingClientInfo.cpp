#include "LicensingClientInfo.h"

namespace CPCAPI2
{
namespace Licensing
{

LicensingClientHandle LicensingClientInfo::nextLicensingClientHandle = 1;

LicensingClientInfo::LicensingClientInfo(CPCAPI2::PhoneInterface* cpcPhone, LicensingClientHandle handle, LicensingHandler* licensingHandler)
   : mLicensing(new Licensing(cpcPhone, licensingHandler)),
     mHandle(handle),
     mTimeLimitLicense(false),
     mTimeLimitRemaining(0)
{
}

LicensingClientInfo::~LicensingClientInfo()
{
   mLicensing->Shutdown();
}

}
}