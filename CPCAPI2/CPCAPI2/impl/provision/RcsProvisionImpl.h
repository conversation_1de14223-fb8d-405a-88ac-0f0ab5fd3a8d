#pragma once

#if !defined(CPCAPI2_PROVISION_IMPL_H)
#define CPCAPI2_PROVISION_IMPL_H

#include "cpcapi2defs.h"

#include "provision/RcsProvision.h"
#include "provision/RcsProvisionHandler.h"
#include "RcsProvisionInterface.h"

#include <thread>
#include <memory>

namespace CPCAPI2
{
namespace RcsProvision
{
class RcsProvisionHandler;

class RcsProvisionImpl
{
public:
   RcsProvisionImpl(RcsProvisionHandle handle, const RcsProvisionSettings& settings, RcsProvisionInterface& intf, PhoneInterface* phone);
   virtual ~RcsProvisionImpl();

   int initialize();
   int setHandler(RcsProvisionHandler* handler);

   int requestForProvision(bool forcedUpdate);

   int onTimer();

private:
   //RcsProvisionHandle getSdkHandleFrom(resip::Data transactionId);
   void fireError(const RcsProvisionHandle& provision, const cpc::string& errorText);
   void requestForProvisionImpl(bool forcedUpdate);
   void onRequestCompleted(const std::string response);

private:
   RcsProvisionHandler* mProvisionHandler;
   RcsProvisionHandle mHandle;
   RcsProvisionSettings mSettings;
   RcsProvisionInterface& mInterface;
   PhoneInterface* mPhone;
   unsigned int mElapsedSinceLastUpdatedMs;
   int mValidityMs;
   cpc::string mVersion;
   std::shared_ptr<std::thread> mThread; // run curl outside the SDK thread
};

}
}
#endif // CPCAPI2_PROVISION_IMPL_H
