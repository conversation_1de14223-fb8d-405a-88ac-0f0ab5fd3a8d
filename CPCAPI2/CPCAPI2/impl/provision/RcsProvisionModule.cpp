#include "brand_branded.h"

#include "interface/experimental/provision/RcsProvision.h"

#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)
#include "RcsProvisionInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace RcsProvision
{
RcsProvisionManager* RcsProvisionManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   return _GetInterface<RcsProvisionInterface>(phone, "RcsProvisionInterface");
#else
   return NULL;
#endif
}

}
}
