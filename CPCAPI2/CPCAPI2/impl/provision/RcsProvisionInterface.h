#pragma once

#if !defined(CPCAPI2_PROVISION_INTERFACE_H)
#define CPCAPI2_PROVISION_INTERFACE_H

#include "cpcapi2defs.h"
#include "provision/RcsProvision.h"
#include "provision/RcsProvisionHandler.h"
#include "../phone/PhoneModule.h"

#include <map>

#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>
#include <rutil/Fifo.hxx>
#include <rutil/RecursiveMutex.hxx>

namespace CPCAPI2
{

class PhoneInterface;

namespace SipAccount
{
class SipAccountInterface;
}

namespace RcsProvision
{
class RcsProvisionImpl;

/**
* Encapsulates functionality for RCS provision parameter
*/
struct RcsProvisionParam
{
   cpc::string name;
   cpc::string value;
};

/**
* Encapsulates functionality for RCS provision option
*/
struct RcsProvisionOption
{
   cpc::string name;
   std::vector<RcsProvisionParam> params;
};

typedef std::vector<RcsProvisionOption> RcsProvisionOptions;

class RcsProvisionInterface :
   public RcsProvisionManager,
   public PhoneModule,
   public resip::DeadlineTimerHandler
{
public:
   RcsProvisionInterface(Phone* phone);
   virtual ~RcsProvisionInterface();

   virtual void Release() OVERRIDE;

   // begin: RcsProvisionManager
   virtual RcsProvisionHandle create(
      const RcsProvisionSettings& settings) OVERRIDE;

   virtual int setHandler(
      RcsProvisionHandle provision,
      RcsProvisionHandler* handler) OVERRIDE;

   virtual int process(
      unsigned int timeout) OVERRIDE;

   virtual void interruptProcess() OVERRIDE;

   virtual void setCallbackHook(void (*cbHook)(void*), void* context);

   virtual int requestForProvision(
      RcsProvisionHandle provision,
      bool forcedUpdate) OVERRIDE;

   virtual SipAccount::SipAccountHandle createAccount(
      RcsProvisionHandle provision,
      const SipAccount::SipAccountSettings& sipAccountSettings) OVERRIDE;

   virtual int populateSettings(
      RcsProvisionHandle provision,
      RcsCapabilityDiscovery::RcsCapabilityDiscoverySettings& settings) OVERRIDE;

   virtual int populateSettings(
      RcsProvisionHandle provision,
      SipAccount::SipAccountSettings& settings) OVERRIDE;

   virtual int populateCapabilitySet(
      RcsProvisionHandle provision,
      RcsCapabilityDiscovery::RcsCapabilitySet& caps) OVERRIDE;

   virtual int loadProfile(
      RcsProvisionHandle provision,
      const cpc::string& path) OVERRIDE;

   virtual int saveProfile(
      RcsProvisionHandle provision,
      const cpc::string& path) OVERRIDE;

   void postCallback(resip::ReadCallbackBase*);

private:
   void createImpl(RcsProvisionHandle provision, const RcsProvisionSettings& settings);
   void setHandlerImpl(RcsProvisionHandle provision, RcsProvisionHandler* handler);
   void requestForProvisionImpl(RcsProvisionHandle provision, bool forcedUpdate);
   RcsProvisionImpl* getProvisionImpl(RcsProvisionHandle provision);

   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:
   friend class RcsProvisionImpl;

   typedef std::map<RcsProvisionHandle, RcsProvisionImpl*> ProvisionMap;
   ProvisionMap mProvisionMap;
   PhoneInterface* mPhone;
   RcsProvisionHandle mNextProvisionHandle;
   std::function<void(void)> mCbHook;

   struct ProvisionInfo
   {
      std::set<SipAccount::SipAccountHandle> accounts;
      RcsProvisionOptions options;
      std::string raw;
   };

   typedef std::map<RcsProvisionHandle, ProvisionInfo> CacheMap;
   CacheMap mCache;
   resip::RecursiveMutex mCacheLock;

   resip::DeadlineTimer<resip::MultiReactor> mTimer;
   static const unsigned int sTimeoutIntervalMs;

   bool mShutdown;
   resip::Fifo<resip::ReadCallbackBase> mCallbackFifo;
};

}
}
#endif // CPCAPI2_PROVISION_INTERFACE_H
