// bliu: need to reset mElapsedSinceLastUpdate and mThread upon receiving response from the server or request timeout
// bliu: curl need to be running in separated thread other than the SDK thread
// bliu: check encoding issue against libxml char conversion
// bliu: multiple "application" characteristics
// bliu: prefer xmlHasProp() instead of xmlGetProp()??
// bliu: provision may not have an associated SipAccountImpl. How to fireError()??
// bliu: convert curl_easy_xxx to curl_multi_xxx??
// bliu: call onProvisionStatusChanged upon error cases??
// bliu: message locale??
// bliu: employ xpath, efficiency issue because of multiple iterations?
// bliu: lexical_cast bad_cast()?
// bliu: _GetProvisionSetting() on non-existing node?

#include "brand_branded.h"

#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)
#include "RcsProvisionImpl.h"

#include "cpcapi2utils.h"
#include "provision/RcsProvisionHandler.h"
#include "provision/RcsProvision.h"
#include "../phone/PhoneInterface.h"
#include "../util/ResipConv.h"
#include "../util/DumFpCommand.h"

#include <curl/curl.h>
#include <libxml/xmlreader.h>

#include <sstream>

using namespace CPCAPI2;

// begin: helper

#define __verify(condition, handler) if (!(condition)) { /* assert(false); */ handler; }

static void _ExtractProvisionOption(RcsProvision::RcsProvisionOptions& options, const xmlNode* node, const std::string& path)
{
   assert(node);
   assert(xmlUTF8Strlen(node->name) > 0);

   if (node->type != XML_ELEMENT_NODE) return;
   if (!xmlStrEqual(node->name, BAD_CAST "characteristic")) return;

   RcsProvision::RcsProvisionOption option;

   xmlChar* typeAttr = xmlGetProp(const_cast<xmlNode*>(node), BAD_CAST "type");

   if (!typeAttr) return;

   std::string newPath = path + "." + (char*)typeAttr; // bliu: attribute "type" may not exist

   option.name = (newPath.c_str());

   for (xmlNode* child = node->children; child; child = child->next)
   {
      if (child->type != XML_ELEMENT_NODE) continue;

      if (xmlStrEqual(child->name, BAD_CAST "characteristic"))
      {
         _ExtractProvisionOption(options, child, newPath);
      }
      else if (xmlStrEqual(child->name, BAD_CAST "parm"))
      {
         RcsProvision::RcsProvisionParam param;

         xmlChar* nameAttr = xmlGetProp(const_cast<xmlNode*>(child), BAD_CAST "name");
         if (!nameAttr) continue;
         param.name = ((char*)nameAttr);

         xmlChar* valueAttr = xmlGetProp(const_cast<xmlNode*>(child), BAD_CAST "value");
         param.value = valueAttr ? ((char*)valueAttr) : "";

         option.params.push_back(param);
      }
      else
      {

#ifdef WIN32
         ::OutputDebugStringA((std::string("non-characteristic node name: ") + (char*)child->name).c_str());
#endif

      }
   }

   options.push_back(option);
}

extern "C" int _ExtractProvisionOptions(const std::string& xml, RcsProvision::RcsProvisionOptions& options)
{
   std::string::size_type beginOfXml = xml.find_first_of('<');
   if (beginOfXml == xml.npos) return kError;

   std::string::size_type endOfXml = xml.find_last_of('>');
   if (endOfXml == xml.npos) return kError;

   if (beginOfXml >= endOfXml) return kError;

   xmlDocPtr doc = xmlReadMemory(xml.substr(beginOfXml, endOfXml - beginOfXml + 1).c_str(), xml.length(), NULL, NULL, 0);

   if (!doc) return kError;

   xmlNodePtr root = xmlDocGetRootElement(doc);
   if (!root) return kError;

   for (xmlNode* child = root->children; child; child = child->next)
   {
      _ExtractProvisionOption(options, child, "");
   }

   xmlFreeDoc(doc);

   return kSuccess;
}

extern "C" size_t _RecvData(char* ptr, size_t size, size_t nmemb, std::string* buffer)
{
   std::vector<char> chunk (size * nmemb);

   if (chunk.size() > 0)
   {
      memcpy(&chunk[0], ptr, chunk.size());
      buffer->insert(buffer->end(), chunk.begin(), chunk.end());
   }

   return chunk.size();
}

extern "C" const cpc::string _GetProvisionSetting(const RcsProvision::RcsProvisionOptions& options, const cpc::string& name, const cpc::string& param)
{
   for (RcsProvision::RcsProvisionOptions::const_iterator it = options.begin(); it != options.end(); ++it)
   {
      const RcsProvision::RcsProvisionOptions::value_type& item = *it;

      if (item.name != name) continue;

      for (std::vector<RcsProvision::RcsProvisionParam>::const_iterator it_param = item.params.begin(); it_param != item.params.end(); ++it_param)
      {
         const RcsProvision::RcsProvisionParam& p = *it_param;

         if (p.name != param) continue;

         return p.value;
      }
   }

   return "";
}

static const std::string _EscapeParam(CURL* curl, const cpc::string& utf8)
{
   char* escaped = curl_easy_escape(curl, utf8.c_str(), utf8.size());
   std::string result = escaped;
   curl_free(escaped);
   return result;
}

template<typename target_t, typename source_t>
static inline const target_t lexical_cast(const source_t& source)
{
   std::stringstream ss;
   target_t target;
   __verify(ss << source && /*!is_pointer<target_t>::value &&*/ ss >> target && (ss >> std::ws).eof(), throw std::bad_cast());

   return target;
}

// end: helper

namespace CPCAPI2
{

namespace RcsProvision
{

const unsigned int RcsProvisionInterface::sTimeoutIntervalMs = 1000;

RcsProvisionImpl::RcsProvisionImpl(RcsProvisionHandle handle, const RcsProvisionSettings& settings, RcsProvisionInterface& intf, PhoneInterface* phone) :
   mHandle(handle),
   mProvisionHandler(NULL),
   mSettings(settings),
   mInterface(intf),
   mPhone(phone),
   mElapsedSinceLastUpdatedMs(0),
   mValidityMs(0),
   mVersion("0")
{
}

RcsProvisionImpl::~RcsProvisionImpl()
{
   if (mThread) mThread->join();
}

int RcsProvisionImpl::initialize()
{
   return kSuccess;
}

int RcsProvisionImpl::setHandler(RcsProvisionHandler* handler)
{
   mProvisionHandler = handler;

   return kSuccess;
}

void RcsProvisionImpl::fireError(const RcsProvisionHandle& provision, const cpc::string& errorText)
{
   PhoneErrorEvent evt;
   evt.errorText = errorText;

   mPhone->fireEvent(cpcEvent(PhoneHandler, onError), "RcsProvision", evt);
   mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), "RcsProvision", evt);
}

int RcsProvisionImpl::requestForProvision(bool forcedUpdate)
{
   if (mThread) return kSuccess;

   mThread = std::make_shared<std::thread>(std::bind(&RcsProvisionImpl::requestForProvisionImpl, this, forcedUpdate));

   return kSuccess;
}

void RcsProvisionImpl::requestForProvisionImpl(bool forcedUpdate)
{
   CURLcode statusCode = CURLE_OK;

   CURL* curl = curl_easy_init();
   curl_slist *cookies = NULL;
   int httpCode = 0;
   std::string errorText;
   std::string response;
   assert(errorText.empty());

   do {
      if (!curl)
      {
         errorText = "cannot initialize curl";
         break;
      }

      curl_easy_setopt(curl, CURLOPT_URL, (mSettings.http_url).c_str());
      //curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
      curl_easy_setopt(curl, CURLOPT_COOKIEFILE, ""); /* just to start the cookie engine */ 
      //curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L); // follow redirection
      curl_easy_setopt(curl, CURLOPT_USERPWD, (mSettings.username + ":" + mSettings.password).c_str());
      curl_easy_setopt(curl, CURLOPT_HTTPAUTH, CURLAUTH_ANY);
      curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0); // disable cert checking
      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, _RecvData);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
      statusCode = curl_easy_perform(curl);

      if (statusCode != CURLE_OK)
      {
         errorText = "curl_easy_perform error " + std::string(curl_easy_strerror(statusCode));
         break;
      }

      statusCode = curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &httpCode);

      if (statusCode != CURLE_OK)
      {
         errorText = "curl_easy_getinfo error " + std::string(curl_easy_strerror(statusCode));
         break;
      }

      if (httpCode != 200)
      {
         errorText = "HTTP error " + cpc::to_string(httpCode) + ": " + response.c_str();
         break;
      }

      statusCode = curl_easy_getinfo(curl, CURLINFO_COOKIELIST, &cookies);

      if (statusCode != CURLE_OK)
      {
         errorText = "curl_easy_getinfo CURLINFO_COOKIELIST error " + std::string(curl_easy_strerror(statusCode));
         break;
      }

      response.clear();

      std::string httpsUrl = mSettings.https_url.c_str();
      httpsUrl += "?vers=";
      httpsUrl += forcedUpdate ? "0" : _EscapeParam(curl, mVersion);

#define SET_URL_PARAM(param)\
   if (!mSettings.param.empty())\
   {\
      httpsUrl += "&" # param "=";\
      httpsUrl += _EscapeParam(curl, mSettings.param);\
   }

      SET_URL_PARAM(IMSI);
      SET_URL_PARAM(rcs_version);
      SET_URL_PARAM(client_vendor);
      SET_URL_PARAM(client_version);
      SET_URL_PARAM(terminal_vendor);
      SET_URL_PARAM(terminal_model);
      SET_URL_PARAM(terminal_sw_version);
      SET_URL_PARAM(IMEI);
      SET_URL_PARAM(friendly_device_name);

      for (cpc::vector<cpc::string>::iterator it = mSettings.rcs_profiles.begin(); it != mSettings.rcs_profiles.end(); ++it)
      {
         const cpc::string& profile = *it;

         if (profile.empty()) continue;

         httpsUrl += "&rcs_profile=";
         httpsUrl += _EscapeParam(curl, profile);
      }

      curl_easy_setopt(curl, CURLOPT_URL, httpsUrl.c_str());
      curl_easy_setopt(curl, CURLOPT_COOKIELIST, cookies);
      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, _RecvData);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);

      statusCode = curl_easy_perform(curl);

      if (statusCode != CURLE_OK)
      {
         errorText = "curl_easy_perform error " + std::string(curl_easy_strerror(statusCode));
         break;
      }

      statusCode = curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &httpCode);

      if (statusCode != CURLE_OK)
      {
         errorText = "curl_easy_getinfo CURLINFO_RESPONSE_CODE error " + std::string(curl_easy_strerror(statusCode));
         break;
      }

      if (httpCode != 200)
      {
         errorText = "HTTPS error " + cpc::to_string(httpCode) + ": " + response.c_str();
         break;
      }

   } while (0);

   curl_slist_free_all(cookies);
   curl_easy_cleanup(curl);

   resip::ReadCallbackBase* cmd = errorText.empty() ?
      resip::resip_bind(&RcsProvisionImpl::onRequestCompleted, this, response) :
      resip::resip_bind(&RcsProvisionImpl::fireError, this, mHandle, (errorText.c_str()));
   mInterface.mPhone->getSdkModuleThread().post(cmd);
}

void RcsProvisionImpl::onRequestCompleted(const std::string response)
{
   RcsOnProvisionStatusChangedEvent evt;
   RcsProvisionOptions options;

   int result = _ExtractProvisionOptions(response, options);

   if (result != kSuccess)
   {
      fireError(mHandle, ("invalid provision profile format: " + response).c_str());
      return;
   }

   const cpc::string version = _GetProvisionSetting(options, ".VERS", "version");
   const cpc::string validity = _GetProvisionSetting(options, ".VERS", "validity");

   if (version.empty())
   {
      fireError(mHandle, "empty provision version");
      return;
   }

   if (validity.empty())
   {
      fireError(mHandle, "empty provision validity");
      return;
   }

   if (version == "0" /*&& validity == L"0"*/) evt.status = RcsProvisionStatus_DisabledTemporarily;
   else if (version == "-1" /*&& validity == L"-1"*/) evt.status = RcsProvisionStatus_DisabledPermanently;
   else if (version == "-2" /*&& validity == L"-2"*/) evt.status = RcsProvisionStatus_DisabledUntilUserAction;
   else if (version == "-3") evt.status = RcsProvisionStatus_Dormant;
   else evt.status = RcsProvisionStatus_Enabled;

   RcsOnProvisionUserMessageEvent userMessageEvent;
   userMessageEvent.title = _GetProvisionSetting(options, ".MSG", "title");
   userMessageEvent.message = _GetProvisionSetting(options, ".MSG", "message");
   userMessageEvent.showAcceptButton = _GetProvisionSetting(options, ".MSG", "Accept_btn") == "1";
   userMessageEvent.showRejectButton = _GetProvisionSetting(options, ".MSG", "Reject_btn") == "1";

   if (!userMessageEvent.title.empty() || !userMessageEvent.message.empty())
   {
      if (mVersion != version)
      {
         resip::ReadCallbackBase* callback = makeFpCommand(RcsProvisionHandler::onProvisionUserMessage, mProvisionHandler, mHandle, userMessageEvent);
         mInterface.postCallback(callback);
      }
   }

   {
      resip::Lock locker (mInterface.mCacheLock);

      RcsProvisionInterface::CacheMap::mapped_type& item = mInterface.mCache[mHandle];

      if (item.accounts.empty())
      {
         evt.account = 0;
         resip::ReadCallbackBase* callback = makeFpCommand(RcsProvisionHandler::onProvisionStatusChanged, mProvisionHandler, mHandle, evt);
         mInterface.postCallback(callback);
      }
      else
      {
         for (std::set<SipAccount::SipAccountHandle>::iterator it_account = item.accounts.begin(); it_account != item.accounts.end(); ++it_account)
         {
            evt.account = *it_account;
            resip::ReadCallbackBase* callback = makeFpCommand(RcsProvisionHandler::onProvisionStatusChanged, mProvisionHandler, mHandle, evt);
            mInterface.postCallback(callback);
         }
      }

      RcsProvisionOptions& storedOptions = item.options;

      // if dormant, stored options stays unchanged
      // if the versions match, stored options stays unchanged
      if (evt.status != RcsProvisionStatus_Dormant && version != _GetProvisionSetting(storedOptions, ".VERS", "version"))
      {
         storedOptions = options;
         item.raw = response;
      }
   }

   mVersion = version;
   mValidityMs = 1000 * lexical_cast<int>((validity));
   mElapsedSinceLastUpdatedMs = 0;

   mThread->join();
   mThread.reset();
}

int RcsProvisionImpl::onTimer()
{
   if (lexical_cast<int>((mVersion)) <= 0) return kSuccess;

   mElapsedSinceLastUpdatedMs += RcsProvisionInterface::sTimeoutIntervalMs;

   if (mElapsedSinceLastUpdatedMs < mValidityMs) return kSuccess;

   requestForProvision(false);

   return kSuccess;
}

}
}
#endif // CPCAPI2_PROVISION_MODULE
