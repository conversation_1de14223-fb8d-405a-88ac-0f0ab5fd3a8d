// bliu: multiple Application settings
// bliu: populateXXX() should remove the non-existing item from the input parameter??

#include "brand_branded.h"

#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)
#include "RcsProvisionInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"

#include "RcsProvisionImpl.h"
#include "../account/SipAccountImpl.h"

#include <sstream>
#include <fstream>

using namespace CPCAPI2;

// begin: helper

#define __verify(condition, handler) if (!(condition)) { /* assert(false); */ handler; }

extern "C" const cpc::string _GetProvisionSetting(const RcsProvision::RcsProvisionOptions& options, const cpc::string& name, const cpc::string& param);
extern "C" int _ExtractProvisionOptions(const std::string& xml, RcsProvision::RcsProvisionOptions& options);

template<typename target_t, typename source_t>
static inline const target_t lexical_cast(const source_t& source)
{
   std::stringstream ss;
   target_t target;
   __verify(ss << source && /*!is_pointer<target_t>::value &&*/ ss >> target && (ss >> std::ws).eof(), throw std::bad_cast());

   return target;
}

template <typename ResultType>
static inline const ResultType _GetProvisionSetting(const RcsProvision::RcsProvisionOptions& options, const cpc::string& name, const cpc::string& param)
{
   try {
      return lexical_cast<ResultType>((::_GetProvisionSetting(options, name, param)));
   }
   catch (...)
   {
      return ResultType();
   }
}

// end: helper

namespace CPCAPI2
{
namespace RcsProvision
{

RcsProvisionInterface::RcsProvisionInterface(Phone* phone) :
   mPhone(dynamic_cast<PhoneInterface*>(phone)),
   mNextProvisionHandle(1),
   mTimer(mPhone->getSdkModuleThread()),
   mShutdown(false)
{
}

RcsProvisionInterface::~RcsProvisionInterface()
{
   mShutdown = true;
   interruptProcess();
}

void RcsProvisionInterface::Release()
{
   delete this;
}

RcsProvisionHandle RcsProvisionInterface::create(const RcsProvisionSettings& settings)
{
   RcsProvisionHandle handle = mNextProvisionHandle += 0x100;

   resip::ReadCallbackBase* cmd = resip::resip_bind(&RcsProvisionInterface::createImpl, this, handle, settings);

   mPhone->getSdkModuleThread().post(cmd);
   return handle;
}

void RcsProvisionInterface::createImpl(RcsProvisionHandle provision, const RcsProvisionSettings& settings)
{
   mProvisionMap[provision] = new RcsProvisionImpl(provision, settings, *this, mPhone);
}

int RcsProvisionInterface::setHandler(
   RcsProvisionHandle provision,
   RcsProvisionHandler* handler)
{
   resip::ReadCallbackBase* setHandlerCmd = resip::resip_bind(&RcsProvisionInterface::setHandlerImpl, this, provision, handler);
   if (handler == NULL)
   {
      mPhone->getSdkModuleThread().execute(setHandlerCmd);
      process(-1);
   }
   else
   {
      mPhone->getSdkModuleThread().post(setHandlerCmd);
   }
   
   return kSuccess;
}

void RcsProvisionInterface::postCallback(resip::ReadCallbackBase* command)
{
   mCallbackFifo.add(command);
   if (mCbHook) { mCbHook(); }
}

void RcsProvisionInterface::setCallbackHook(void (*cbHook)(void*), void* context)
{
   mCbHook = std::bind(cbHook, context);
}

int RcsProvisionInterface::process(unsigned int timeout)
{
   // -1 == no wait
   if (mShutdown)
   {
      return kModuleDisabled;
   }

   for (resip::ReadCallbackBase* fp = mCallbackFifo.getNext(timeout); fp; fp = mCallbackFifo.getNext(kBlockingModeNonBlocking))
   {
      (*fp)();
      delete fp;

      if (mShutdown) return kModuleDisabled;
   }

   return kSuccess;
}

void RcsProvisionInterface::interruptProcess()
{
   mPhone->getSdkModuleThread().getAsyncProcessHandler()->handleProcessNotification();
}

void RcsProvisionInterface::setHandlerImpl(
   RcsProvisionHandle provision,
   RcsProvisionHandler* handler)
{
   if (RcsProvisionImpl* provisionImpl = getProvisionImpl(provision))
   {
      provisionImpl->setHandler(handler);
   }
   else
   {
      PhoneErrorEvent evt;
      evt.errorText = "Invalid provision handle for setHandler";
      mPhone->fireEvent(cpcEvent(PhoneHandler, onError), "Provision", evt);
      mPhone->fireEvent(cpcEvent(PhoneErrorHandler, onError), "Provision", evt);
   }
}

int RcsProvisionInterface::requestForProvision(
   RcsProvisionHandle provision,
   bool forcedUpdate)
{
   resip::ReadCallbackBase* cmd = resip::resip_bind(&RcsProvisionInterface::requestForProvisionImpl, this, provision, forcedUpdate);
   mPhone->getSdkModuleThread().post(cmd);

   return kSuccess;
}

void RcsProvisionInterface::requestForProvisionImpl(
   RcsProvisionHandle provision,
   bool forcedUpdate)
{
   RcsProvisionImpl* impl = getProvisionImpl(provision);

   impl->requestForProvision(forcedUpdate);
}

SipAccount::SipAccountHandle RcsProvisionInterface::createAccount(
   RcsProvisionHandle provision,
   const SipAccount::SipAccountSettings& sipAccountSettings)
{
   SipAccount::SipAccountManager* accountInterface = dynamic_cast<SipAccount::SipAccountManager*>(mPhone->getInterfaceByName("SipAccountInterface"));
   assert(accountInterface != NULL);

   SipAccount::SipAccountHandle account = accountInterface->create(sipAccountSettings);

   resip::Lock locker (mCacheLock);
   mCache[provision].accounts.insert(account);

   return account;
}

int RcsProvisionInterface::populateSettings(
   RcsProvisionHandle provision,
   RcsCapabilityDiscovery::RcsCapabilityDiscoverySettings& settings)
{
   resip::Lock locker (mCacheLock);

   CacheMap::iterator it = mCache.find(provision);

   if (it == mCache.end()) return kError;

   CacheMap::mapped_type& item = it->second;

   settings.pollingPeriodMs = 1000 * _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "pollingPeriod");
   settings.pollingRate = _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "pollingRate");
   settings.pollingRatePeriodMs = _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "pollingRatePeriod");
   settings.capInfoExpiryMs = 1000 * _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "capInfoExpiry");
   settings.defaultDisc = _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "defaultDisc");
   settings.capDiscCommonStack = _GetProvisionSetting<int>(item.options, ".APPLICATION.CAPDISCOVERY", "capDiscCommonStack");
   settings.isIMCapAlwaysON = _GetProvisionSetting<bool>(item.options, ".APPLICATION.IM", "imCapAlwaysON");

   return kSuccess;
}

int RcsProvisionInterface::populateSettings(
   RcsProvisionHandle provision,
   SipAccount::SipAccountSettings& settings)
{
   resip::Lock locker (mCacheLock);

   CacheMap::iterator it = mCache.find(provision);

   if (it == mCache.end()) return kError;

   CacheMap::mapped_type& item = it->second;

   // bliu: unassigned fields are not yet mapped to provision configurations yet
   settings.username = ::_GetProvisionSetting(item.options, ".APPLICATION.APPAUTH", "UserName");
   settings.domain = ::_GetProvisionSetting(item.options, ".APPLICATION.APPAUTH", "Realm");
   settings.password = ::_GetProvisionSetting(item.options, ".APPLICATION.APPAUTH", "UserPwd");
   settings.displayName;
   settings.auth_username;
   settings.useRegistrar;
   settings.outboundProxy = ::_GetProvisionSetting(item.options, ".APPLICATION.LBO_P-CSCF_Address", "Address");
   settings.registrationIntervalSeconds;
   settings.minimumRegistrationIntervalSeconds;
   settings.maximumRegistrationIntervalSeconds;
   settings.useRport;
   settings.sipTransportType;
   settings.userAgent;
   settings.udpKeepAliveTime;
   settings.tcpKeepAliveTime;
   settings.useOutbound;

   return kSuccess;
}

int RcsProvisionInterface::populateCapabilitySet(
   RcsProvisionHandle provision,
   RcsCapabilityDiscovery::RcsCapabilitySet& caps)
{
   resip::Lock locker (mCacheLock);

   CacheMap::iterator it = mCache.find(provision);

   if (it == mCache.end()) return kError;

   CacheMap::mapped_type& item = it->second;

#define populateCap(param, cap)\
   if (bool value = _GetProvisionSetting<bool>(item.options, ".APPLICATION.SERVICES", param)) caps.push_back(cap)

   populateCap("presencePrfl", RcsCapabilityDiscovery::RcsCapability::SocialPresenceInformation);
   populateCap("ChatAuth", RcsCapabilityDiscovery::RcsCapability::Chat);
   //populateCap("GroupChatAuth", RcsCapabilityDiscovery::RcsCapability::GroupChat); // bliu: TODO add group chat
   populateCap("ftAuth", RcsCapabilityDiscovery::RcsCapability::FileTransfer);
   populateCap("standaloneMsgAuth", RcsCapabilityDiscovery::RcsCapability::IPBasedStandaloneMessaging);
   populateCap("geolocPullAuth", RcsCapabilityDiscovery::RcsCapability::GeolocationPULL);
   populateCap("geolocPushAuth", RcsCapabilityDiscovery::RcsCapability::GeolocationPUSH);
   populateCap("vsAuth", RcsCapabilityDiscovery::RcsCapability::VideoShare);
   populateCap("isAuth", RcsCapabilityDiscovery::RcsCapability::ImageShare);

   if (int value = _GetProvisionSetting<int>(item.options, ".APPLICATION.SERVICES", "rcsIPVoiceCallAuth") & 0xFF) // only take the least significant byte
   {
      caps.push_back(RcsCapabilityDiscovery::RcsCapability::IPVoiceCall);
   }

   if (int value = _GetProvisionSetting<int>(item.options, ".APPLICATION.SERVICES", "rcsIPVideoCallAuth") & 0xFF) // only take the least significant byte
   {
      caps.push_back(RcsCapabilityDiscovery::RcsCapability::RCSIPVideoCall);
   }

   return kSuccess;
}

int RcsProvisionInterface::loadProfile(
   RcsProvisionHandle provision,
   const cpc::string& path)
{
   std::ifstream in (path.c_str());
   if (!in.is_open()) return kError;

   std::stringstream ss;
   ss << in.rdbuf();

   RcsProvisionOptions options;
   if (_ExtractProvisionOptions(ss.str(), options) != kSuccess) return kError;

   resip::Lock locker (mCacheLock);

   mCache[provision].options = options;
   mCache[provision].raw = ss.str();

   return kSuccess;
}

int RcsProvisionInterface::saveProfile(
   RcsProvisionHandle provision,
   const cpc::string& path)
{
   std::ofstream out (path.c_str(), std::ios_base::out | std::ios_base::trunc);
   if (!out.is_open()) return kError;

   resip::Lock locker (mCacheLock);

   CacheMap::iterator it = mCache.find(provision);

   if (it == mCache.end()) return kError;

   out << it->second.raw << std::flush;

   return kSuccess;
}

void RcsProvisionInterface::onTimer(unsigned short timerId, void* appState)
{
   for (ProvisionMap::iterator it = mProvisionMap.begin(); it != mProvisionMap.end(); ++it)
   {
      ProvisionMap::value_type& item = *it;
      RcsProvisionImpl* provisionImpl = getProvisionImpl(item.first);
      provisionImpl->onTimer();
   }

   mTimer.expires_from_now(sTimeoutIntervalMs);
   mTimer.async_wait(this, 0, NULL);
}

RcsProvisionImpl* RcsProvisionInterface::getProvisionImpl(RcsProvisionHandle provision)
{
   ProvisionMap::iterator it = mProvisionMap.find(provision);
   return it == mProvisionMap.end() ? NULL : it->second;
}

}
}
#endif // CPCAPI2_PROVISION_MODULE
