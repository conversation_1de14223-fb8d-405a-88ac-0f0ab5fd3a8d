#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_INTERNAL_EVENT_HANDLER_H)
#define CPCAPI2_SIP_PRESENCE_INTERNAL_EVENT_HANDLER_H

#include "cpcapi2defs.h"
#include "event/SipEventState.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "../account/SipAccountImpl.h"
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
#include "../watcherinfo/WatcherInfoManagerInterface.h"
#endif
#include "phone/EventSyncHandler.h"
#include "presence/SipPresenceModel.h"
#include "presence/SipPresenceManager.h"

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountInterface;
}

namespace SipPresence
{
class SipPresenceSubscriptionHandler;
class SipPresencePublicationHandler;
class SipPresenceManagerInterface;
class SipPresenceConfigurationManagerInterface;

class SipPresenceInternalEventHandler : public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventSubscriptionHandler>,
                                        public CPCAPI2::EventSyncHandler<CPCAPI2::SipEvent::SipEventPublicationHandler>
{
public:
   SipPresenceInternalEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, CPCAPI2::SipPresence::SipPresenceManager& presenceManager);
   virtual ~SipPresenceInternalEventHandler();

   void setSubscriptionHandler(SipPresenceSubscriptionHandler* handler);
   void setPublicationHandler(SipPresencePublicationHandler* handler);

   virtual int onNewSubscription(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NewSubscriptionEvent& args);
   virtual int onSubscriptionEnded(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionEndedEvent& args);
   virtual int onIncomingEventState(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingEventStateEvent& args);
   virtual int onIncomingResourceList(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::IncomingResourceListEvent& args) { return kSuccess; }
   virtual int onSubscriptionStateChanged(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::SubscriptionStateChangedEvent& args);

   virtual int onNotifySuccess(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifySuccessEvent& args);
   virtual int onNotifyFailure(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::NotifyFailureEvent& args);
   virtual int onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args);

   
   virtual int onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args);
   virtual int onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args);
   virtual int onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args);
   virtual int onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationErrorEvent& args);

   int preparePresence(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, CannedStatus presenceStatus);
   CPCAPI2::SipPresence::Presence buildPresenceDoc(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, CannedStatus presenceStatus);

private:
   SipPresence::Presence& getOutgoingPresence();

private:
   CPCAPI2::SipAccount::SipAccountImpl& mAccount;
   CPCAPI2::SipPresence::Presence* mOutgoingPresence;
   CPCAPI2::SipPresence::SipPresenceManagerInterface* mPresenceManager;
   CPCAPI2::SipPresence::SipPresenceConfigurationManagerInterface* mPresenceConfigurationManager;
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   CPCAPI2::WatcherInfo::WatcherInfoManagerInterface* mWinfoManager;
#endif
};
}
}
#endif // CPCAPI2_SIP_PRESENCE_INTERNAL_EVENT_HANDLER_H
