#if !defined(CPCAPI2_GENBAND_PRESENCE_MODEL_H)
#define CPCAPI2_GENBAND_PRESENCE_MODEL_H

#include "presence/SipPresenceModel.h"

/*
 * This file contains extensions of the base SipPresenceModel to accomodate
 * GENBAND specific presence documents.
 * Rather than redefine most basic objects, SipPresenceModel is included here
 * and those definitions are re-used where possible. This file will define new
 * objects where the GENBAND presence model differs from the base presence model
 */

namespace CPCAPI2
{

namespace SipPresence
{

struct GBPerson
   {
      Id id;
      Notes notes;
      Optional<Timestamp> timestamp;
      Optional<Activities> activities;
      Optional<Class> classEnt; /* 'class' is reserved */
      Optional<Moods> mood;
      Optional<PlaceIs> placeIs;
      Optional<PlaceType> placeType;
      Optional<Privacy> privacy;
      Optional<Sphere> sphere;
      Optional<StatusIcon> statusIcon;
      Optional<TimeOffset> timeOffset;
      Optional<UserInput> userInput;   //RFC4480
      Optional<Status> status;
   };

struct GBTuple
   {
      Id id;
      Notes notes;
      Optional<Timestamp> timestamp;
      Status status;
      Optional<Contact> contact;
      Optional<DeviceId> deviceID;
      Optional<Relationship> relationship;
      Optional<ServiceClass> serviceClass;
      Optional<UserInput> userInput;   //RFC4480
      Optional<Class> classEnt;
   };

struct GBPresence
   {
      cpc::string entity;
      Notes notes;
      cpc::vector<GBTuple> tuples;
      cpc::vector<Device> devices;
      cpc::vector<GBPerson> persons;
   };

} // namespace SipPresence

} // namespace CPCAPI2
#endif