#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_MANAGER_INTERFACE_H)
#define CPCAPI2_SIP_PRESENCE_MANAGER_INTERFACE_H

#include "cpcapi2defs.h"
#include "presence/SipPresenceManager.h"
#include "presence/SipPresencePublicationHandler.h"
#include "presence/SipPresenceSubscriptionHandler.h"
#include "event/SipEventSubscriptionHandler.h"
#include "event/SipEventPublicationHandler.h"
#include "SipPresenceInternalEventHandler.h"
#include "SipPresenceManagerInternal.h"
#include "../phone/PhoneModule.h"
#include <rutil/MultiReactor.hxx>
#include <rutil/DeadlineTimer.hxx>

namespace CPCAPI2
{
namespace SipAccount
{
class SipAccountInterface;
}

namespace SipEvent
{
class SipEventManagerInterface;
class SipEventPublicationManagerInterface;
}

namespace SipPresence
{

struct PresenceRefresh
{
   CPCAPI2::SipAccount::SipAccountHandle                                   account;
   std::shared_ptr<resip::DeadlineTimer<resip::MultiReactor> >           m_Timer;
   cpc::string                                                             buddyAddress;
   SipEventSubscriptionHandle                                              subscription;
   int                                                                     statusCode;

   PresenceRefresh(resip::MultiReactor& service, cpc::string address, SipEventSubscriptionHandle handle)
   {
      m_Timer.reset(new resip::DeadlineTimer<resip::MultiReactor>(service));
      buddyAddress = address;
      account = 0xffffffff;
      subscription = handle;
      statusCode = 0;
   }
};

class SipPresenceManagerInterface : public CPCAPI2::EventSource2<CPCAPI2::EventHandler<SipPresenceSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle>, CPCAPI2::EventHandler<SipPresencePublicationHandler, CPCAPI2::SipAccount::SipAccountHandle> >,
                                    public SipPresenceManager,
                                    public SipPresenceManagerInternal,
                                    public PhoneModule,
                                    public resip::DeadlineTimerHandler
{
   
public:
   
   SipPresenceManagerInterface(Phone* phone);
   virtual ~SipPresenceManagerInterface();

   FORWARD_EVENT_PROCESSOR(SipPresenceManagerInterface);

   USING_EVENT_HANDLER(SipPresenceSubscriptionHandler, CPCAPI2::SipAccount::SipAccountHandle);
   USING_EVENT_HANDLER(SipPresencePublicationHandler, CPCAPI2::SipAccount::SipAccountHandle);

   virtual int processNewSubscription(SipAccount::SipAccountHandle account, SipEventSubscriptionHandle subscription, const SipEvent::NewSubscriptionEvent& event);
   virtual int processSubscriptionEnd(SipAccount::SipAccountHandle account, SipEventSubscriptionHandle subscription, const SipEvent::SubscriptionEndedEvent& event);
   virtual int start(SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account) OVERRIDE;
   virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle, SipEventSubscriptionHandle h) OVERRIDE;
   virtual int reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason) OVERRIDE;

   /** Functions with associated Impl functions */
   virtual void Release() OVERRIDE;
   virtual int setHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipPresenceSubscriptionHandler* handler) OVERRIDE;
   virtual int setPublicationHandler(CPCAPI2::SipAccount::SipAccountHandle account, SipPresencePublicationHandler* handler) OVERRIDE;
   virtual SipEventPublicationHandle createPublication(CPCAPI2::SipAccount::SipAccountHandle account, const SipPresencePublicationSettings& settings) OVERRIDE;
   virtual int publish(SipEventPublicationHandle publication, const Presence& presenceState) OVERRIDE;
   virtual int publish(SipEventPublicationHandle publication, CannedStatus presenceStatus) OVERRIDE;
   virtual int publish(SipEventPublicationHandle publication, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams) OVERRIDE;
   virtual int endPublish(SipEventPublicationHandle publication) OVERRIDE;
   virtual int refreshPublish(SipEventPublicationHandle publication) OVERRIDE;
   virtual int addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress) OVERRIDE;
   virtual int end(SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int refresh(SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int accept(SipEventSubscriptionHandle subscription, const Presence& presenceState) OVERRIDE;
   virtual int accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) OVERRIDE;
   virtual int accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams) OVERRIDE;
   virtual int provisionalAccept(SipEventSubscriptionHandle subscription) OVERRIDE;
   virtual int notify(SipEventSubscriptionHandle subscription, const Presence& presenceState) OVERRIDE;
   virtual int notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) OVERRIDE;
   virtual int notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams) OVERRIDE;
   virtual int preparePresence(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus) OVERRIDE;
   virtual int applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings) OVERRIDE;

private:

   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

   void restartFailedSubscription(PresenceRefresh* refreshState);
   int applyPresenceSettings(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings);
   
   /** Impl Functions */
   int setHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account,SipPresenceSubscriptionHandler* handler);
   int setPublicationHandlerImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipPresencePublicationHandler* handler);
   int createPublicationImpl(CPCAPI2::SipAccount::SipAccountHandle account, SipEventPublicationHandle publication);
   int publishImpl(SipEventPublicationHandle publication, const Presence& presenceState);
   int publishCannedImpl(SipEventPublicationHandle publication, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams);
   int endPublishImpl(SipEventPublicationHandle publication);
   int refreshPublishImpl(SipEventPublicationHandle publication);
   int addParticipantImpl(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress);
   int endImpl(SipEventSubscriptionHandle subscription);
   int refreshImpl(SipEventSubscriptionHandle subscription);
   int acceptImpl(SipEventSubscriptionHandle subscription, const Presence& presenceState);
   int acceptCannedImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams);
   int provisionalAcceptImpl(SipEventSubscriptionHandle subscription);
   int notifyImpl(SipEventSubscriptionHandle subscription, const Presence& presenceState);
   int notifyCannedImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams);
   int preparePresenceImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus);
   int applySubscriptionSettingsImpl(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings);

private:
   
   typedef std::map<CPCAPI2::SipAccount::SipAccountHandle, SipPresenceInternalEventHandler*> AccountMap;
   typedef std::map<SipEventSubscriptionHandle, SipPresenceSubscriptionSettings> SettingsMap;
   typedef std::map<SipEventSubscriptionHandle, PresenceRefresh*> RefreshMap;
   AccountMap mAccountMap;
   SettingsMap mSettingsMap;
   RefreshMap mRefreshMap;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipPresenceSubscriptionHandler*> mSubscriptionHandlers;
   std::map<CPCAPI2::SipAccount::SipAccountHandle, SipPresencePublicationHandler*> mPublicationHandlers;

   CPCAPI2::SipAccount::SipAccountInterface* mAccountIf;
   CPCAPI2::SipEvent::SipEventManagerInterface* mSipEventIf;
   CPCAPI2::SipEvent::SipEventPublicationManagerInterface* mSipPublicationIf;
   PhoneInterface* mPhone;
   
};

std::ostream& operator<<(std::ostream& os, const PresencePublicationSuccessEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresencePublicationFailureEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresencePublicationRemoveEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresencePublicationErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const NewPresenceSubscriptionEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresenceSubscriptionEndedEvent& evt);
std::ostream& operator<<(std::ostream& os, const IncomingPresenceStatusEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresenceSubscriptionStateChangedEvent& evt);
std::ostream& operator<<(std::ostream& os, const PresenceReadyToSendEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipPresence::ErrorEvent& evt);
std::ostream& operator<<(std::ostream& os, const SipPresencePublicationSettings&);
std::ostream& operator<<(std::ostream& os, const SipPresenceSubscriptionSettings&);
std::ostream& operator<<(std::ostream& os, const Presence&);
std::ostream& operator<<(std::ostream& os, const StatusUpdateParameters&);


}
}
#endif // CPCAPI2_SIP_PRESENCE_MANAGER_INTERFACE_H
