#if !defined(CPCPAPI2_SIPPRESENCECONFIGURATIONMANAGERIMPL_H)
#define CPCAPI2_SIPPRESENCECONFIGURATIONMANAGERIMPL_H

#include "SipPresenceConfiguration/SipPresenceConfigurationManager.h"
#include "SipPresenceManagerInterface.h"

namespace CPCAPI2
{
namespace SipPresence
{

   class SipPresenceConfigurationManagerImpl
   {
   public:
      SipPresenceConfigurationManagerImpl();

      void setSipPresenceModel(SipPresenceModelType modelType);
      SipPresenceModelType getSipPresenceModelType() { return mModelType; }

   private:
      SipPresenceModelType mModelType;
   };
} // namespace SipPresence
} // namespace CPCPAPI2
#endif