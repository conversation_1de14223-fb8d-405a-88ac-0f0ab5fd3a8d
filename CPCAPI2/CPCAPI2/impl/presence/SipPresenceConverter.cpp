#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipPresenceConverter.h"

namespace CPCAPI2
{
namespace SipPresence
{

void SipPresenceConverter::prepareCannedStatus(CannedStatus presenceStatus, Presence& presDoc, SipPresenceModelType model)
{
      if (model == GENBAND_PRESENCE_MODEL)
      {
         prepareCannedStatusGBModel(presenceStatus, presDoc);
         return;
      }
   
      switch (presenceStatus)
      {
      case CannedStatus_AppearOffline:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            presDoc.persons[0].notes.clear();
            presDoc.persons[0].activities = SipPresence::Optional<SipPresence::Activities>();
         }
         break;
      case CannedStatus_Available:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            presDoc.persons[0].activities = SipPresence::Optional<SipPresence::Activities>();
            SipPresence::Note n;
            n.text = "Available";
            n.lang = "en";
            presDoc.persons[0].notes.push_back(n);
         }
         break;
      case CannedStatus_Away:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Away;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Away";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
     case CannedStatus_Lunch:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Lunch;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Lunch";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_Vacation:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Vacation;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Vacation";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
            break;
      case CannedStatus_Idle:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Away;

            SipPresence::Activity otherActivity;
            otherActivity.activity = SipPresence::ActivityType_Other;
            SipPresence::Note otherNote;
            otherNote.text = "Idle";
            otherNote.lang = "en";
            otherActivity.otherValue = otherNote;

            SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(otherActivity);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Idle";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_Busy:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Busy;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Busy";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_DND:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Busy;
            SipPresence::Activity other;
            other.activity = SipPresence::ActivityType_Other;
            other.otherValue.text = "DND";
            SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(other);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Do not disturb";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_NotAvailable:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Busy;

            SipPresence::Activity otherActivity;
            otherActivity.activity = SipPresence::ActivityType_Other;
            SipPresence::Note otherNote;
            otherNote.text = "Not available for calls";
            otherNote.lang = "en";
            otherActivity.otherValue = otherNote;

            SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(otherActivity);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "Not available for calls";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_OnThePhone:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_OnThePhone;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            SipPresence::Note n;
            n.text = "On the phone";
            n.lang = "en";
            SipPresence::Notes ns;
            ns.push_back(n);
            presDoc.persons[0].notes = ns;
         }
         break;
      case CannedStatus_Other:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Other;
            a.otherValue.text = "Other";
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            presDoc.persons[0].notes = SipPresence::Notes();
         }
         break;
      default:
            break;
      }
}

CannedStatus SipPresenceConverter::parseCannedStatus(const Presence& presence, SipPresenceModelType model)
{
   if (presence.persons.size() != 1)
      return CannedStatus_Other; // either 0 person or more than 1, can't deal with that
   
   if (model == GENBAND_PRESENCE_MODEL)
      return parseCannedStatusGBModel(presence);

   if (presence.tuples.size() == 1)
   {
      const Status& status = presence.tuples[0].status;
      if (status.basic.present && status.basic.value == BasicStatusType_Closed)
         return CannedStatus_AppearOffline;
   }

   const Person& person = presence.persons[0];
   const Note* note = person.notes.size() == 1 ? &person.notes[0] : NULL;
   unsigned long activitiesCount = person.activities.value.activities.size();
   ActivityType currentActivity;

   if (person.activities.present && activitiesCount > 0)
   {
      if (activitiesCount > 1)
      {
         for(unsigned int j=0;j<activitiesCount;j++)
         {
            if(person.activities.value.activities[j].activity == ActivityType_Other)
            {
               note = &person.activities.value.activities[j].otherValue;
            }
            else
            {
               currentActivity = person.activities.value.activities[j].activity;
            }
         }
      }
      else
      {
         currentActivity = person.activities.value.activities[0].activity;
      }

      switch(currentActivity)
      {
         case ActivityType_Away:
            if (note && note->text == cpc::string("Idle"))
               return CannedStatus_Idle;
            else
               return CannedStatus_Away;
         case ActivityType_Busy:
            if (note && (note->text == cpc::string("Do not disturb") ||
                         note->text == cpc::string("DND") ))
               return CannedStatus_DND;
            else if (note && note->text == cpc::string("Not available for calls"))
               return CannedStatus_NotAvailable;            
            return CannedStatus_Busy;
         case ActivityType_OnThePhone:
            return CannedStatus_OnThePhone;
         default:
            return CannedStatus_Other;
      }
   }   
   else
   {
	   if(!note)
	   {
			return CannedStatus_Available;
	   }
   }

   if (note)
      return CannedStatus_Available;

   return CannedStatus_Other; 
}

void SipPresenceConverter::updateStatusParameters(Presence& presence, const StatusUpdateParameters& params)
{
   if(!params.note.empty())
   {
      Note n;
      n.text = params.note;

      if(presence.persons.size() > 0)
      {
         Person &person = presence.persons[0];
         person.notes.clear();
         person.notes.push_back(n);
      }
      
      if(presence.tuples.size() > 0)
      {
         Tuple &tuple = presence.tuples[0];
         tuple.notes.clear();
         tuple.notes.push_back(n);
      }
      
      if(presence.persons.size() == 0 && presence.tuples.size() == 0)
      {
         presence.notes.push_back(n);
      }
   }
}

// Model specific methods
void SipPresenceConverter::prepareCannedStatusGBModel(CannedStatus presenceStatus, Presence& presDoc)
{
      switch (presenceStatus)
      {
         
         case CannedStatus_Available:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            presDoc.persons[0].activities = SipPresence::Optional<SipPresence::Activities>();
         }
            break;
         case CannedStatus_Away:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Away;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_Lunch:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Lunch;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_Idle:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Away;
            
            SipPresence::Activity otherActivity;
            otherActivity.activity = SipPresence::ActivityType_Other;
            SipPresence::Note otherNote;
            otherActivity.otherValue = otherNote;
            
            SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(otherActivity);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_NotAvailable:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Busy;
            
            SipPresence::Activity otherActivity;
            otherActivity.activity = SipPresence::ActivityType_Other;
            SipPresence::Note otherNote;
            otherNote.text = "Not available for calls";
            otherActivity.otherValue = otherNote;
            
            SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(otherActivity);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_OnThePhone:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_OnThePhone;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_Other:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Open;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Other;
            a.otherValue.text = "Other";
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
            presDoc.persons[0].notes = SipPresence::Notes();
         }
            break;
         case CannedStatus_AppearOffline:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            presDoc.persons[0].notes.clear();
            presDoc.persons[0].activities = SipPresence::Optional<SipPresence::Activities>();
         }
            break;
         case CannedStatus_Vacation:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Vacation;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_Busy:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Busy;
            SipPresence::Activities as;
            as.activities.push_back(a);
            presDoc.persons[0].activities = as;
         }
            break;
         case CannedStatus_DND:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            CPCAPI2::SipPresence::Activity a;
            a.activity = CPCAPI2::SipPresence::ActivityType_Busy;
            CPCAPI2::SipPresence::Activity other;
            other.activity = CPCAPI2::SipPresence::ActivityType_Other;
            other.otherValue.text = "DND";
            CPCAPI2::SipPresence::Activities as;
            as.activities.push_back(a);
            as.activities.push_back(other);
            presDoc.persons[0].activities = as;
         }
            break;
         default:
         {
            presDoc.tuples[0].status.basic = BasicStatusType_Closed;
            presDoc.persons[0].notes.clear();
            SipPresence::Activity a;
            a.activity = SipPresence::ActivityType_Unknown;
         }
            break;
      }
}
   
CannedStatus SipPresenceConverter::parseCannedStatusGBModel(const Presence& presence)
{
   const Status& status = presence.tuples[0].status;

   const Person& person = presence.persons[0];
   const Note* note = person.notes.size() == 1 ? &person.notes[0] : NULL;
   unsigned long activitiesCount = person.activities.value.activities.size();
   ActivityType currentActivity;

   if (person.activities.present && activitiesCount > 0)
   {
      int lastActivityIndex = (int)activitiesCount-1;
      currentActivity = person.activities.value.activities[lastActivityIndex].activity;
      
      if(person.activities.value.activities[lastActivityIndex].activity == ActivityType_Other)
      {
         note = &person.activities.value.activities[lastActivityIndex].otherValue;
      }
      
      switch(currentActivity)
      {
         case ActivityType_Away:
            if (note && note->text == cpc::string("Idle"))
               return CannedStatus_Idle;
            else
               return CannedStatus_Away;
         case ActivityType_Busy:
            if (note && (note->text == cpc::string("Do not disturb") ||
                         note->text == cpc::string("DND") ))
               return CannedStatus_DND;
            else if (note && note->text == cpc::string("Not available for calls"))
               return CannedStatus_NotAvailable;            
            return CannedStatus_Busy;
         case ActivityType_Vacation:
            return CannedStatus_Vacation;
         case ActivityType_OnThePhone:
            return CannedStatus_OnThePhone;
         case ActivityType_Lunch:
            return CannedStatus_Lunch;
         case ActivityType_Other:
            if (status.basic.present && status.basic.value == BasicStatusType_Closed && (note && note->text == cpc::string("Offline")))
               return CannedStatus_AppearOffline;
            return CannedStatus_Other;
         default:
         {
            if (status.basic.present && currentActivity == ActivityType_Unknown)
            {
               if (status.basic.value == BasicStatusType_Open)
                  return CannedStatus_Available;
               else if (status.basic.value == BasicStatusType_Closed)
                  return CannedStatus_AppearOffline;
            }
            return CannedStatus_Other;
         }
      }
   }   
   else
   {
	   if(!note)
	   {
			return CannedStatus_Available;
	   }
   }

   if (note)
      return CannedStatus_Available;

   return CannedStatus_Other; 
}
}
}
#endif
