#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_XML_ENCODER_H)
#define CPCAPI2_SIP_PRESENCE_XML_ENCODER_H

#include <libxml/parser.h>
#include <sstream>
#include <map>
#include "presence/SipPresenceModel.h"
#include "SipPresenceXml.h"

namespace CPCAPI2
{
namespace SipPresence
{

/** base class shared by both encoder and parser, defines common mappings between string and enum values
  */
class CPCAPI2_SHAREDLIBRARY_API XmlBase {
protected:
   typedef const XmlKey Key;
   XmlBase();

   typedef EnumMap<ActivityType> ActivityMap;
   ActivityMap m_activityMap;
   typedef EnumMap<MoodValue> MoodMap;
   MoodMap m_moodMap;
   typedef EnumMap<AudioIsType> AudioIsMap;
   AudioIsMap m_audioIsMap;
   typedef EnumMap<VideoIsType> VideoIsMap;
   VideoIsMap m_videoIsMap;
   typedef EnumMap<TextIsType> TextIsMap;
   TextIsMap m_textIsMap;
   typedef EnumMap<PrivacyType> PrivacyTypeMap;
   PrivacyTypeMap m_privacyTypeMap;
   typedef EnumMap<RelationshipType> RelationshipTypeMap;
   RelationshipTypeMap m_relationshipTypeMap;
   typedef EnumMap<ServiceClassType> ServiceClassTypeMap;
   ServiceClassTypeMap m_serviceClassTypeMap;
   typedef EnumMap<SphereType> SphereTypeMap;
   SphereTypeMap m_sphereTypeMap;
   typedef EnumMap<ActiveIdle> ActiveIdleMap;
   ActiveIdleMap m_activeIdleMap;
   typedef EnumMap<BasicStatus> BasicStatusMap;
   BasicStatusMap m_basicStatusMap;
private:
   void initActivityMap();
   void initMoodMap();
   void initAudioIsMap();
   void initVideoIsMap();
   void initTextIsMap();
   void initPrivacyTypeMap();
   void initRelationshipTypeMap();
   void initServiceClassTypeMap();
   void initSphereTypeMap();
   void initActiveIdleMap();
   void initBasicStatusMap();
};

/** XML parser which takes a dom node and translates it into a simple struct mapping of the data type
    parse methods will return false if they encounter any problems parsing, though a best effort will always be made to parse
    as much of the whole element */
class CPCAPI2_SHAREDLIBRARY_API XmlParser: public XmlBase
{
/* The public parse/encode methods are all named parse and encode 
   with the same params to allow template format usage (eg use parseOptional without writing one for each parse method) */
public:
   bool parse(xmlNodePtr node, Note& output);
   bool parse(xmlNodePtr node, Activity& output);
   bool parse(xmlNodePtr node, Activities& output);
   bool parse(xmlNodePtr node, Mood& output);
   bool parse(xmlNodePtr node, Moods& output);
   bool parse(xmlNodePtr node, AudioIsType& output);
   bool parse(xmlNodePtr node, VideoIsType& output);
   bool parse(xmlNodePtr node, TextIsType& output);
   bool parse(xmlNodePtr node, PlaceIs& output);
   bool parse(xmlNodePtr node, PlaceType& output);
   bool parse(xmlNodePtr node, PrivacyType& output);
   bool parse(xmlNodePtr node, Privacy& output);
   bool parse(xmlNodePtr node, RelationshipValue& output);
   bool parse(xmlNodePtr node, Relationship& output);
   bool parse(xmlNodePtr node, ServiceClassType& output);
   bool parse(xmlNodePtr node, ServiceClass& output);
   bool parse(xmlNodePtr node, SphereType& output);
   bool parse(xmlNodePtr node, Sphere& output);
   bool parse(xmlNodePtr node, StatusIcon& output);
   bool parse(xmlNodePtr node, TimeOffset& output);
   bool parse(xmlNodePtr node, UserInput& output);
   bool parse(xmlNodePtr node, Device& output);
   bool parse(xmlNodePtr node, Person& output);
   bool parse(xmlNodePtr node, Contact& output);
   bool parse(xmlNodePtr node, Status& output);
   bool parse(xmlNodePtr node, Other& output);
   bool parse(xmlNodePtr node, Tuple& output);
   bool parse(xmlNodePtr node, Presence& output);
private:
   /** parse text which appears inside a given key (eg <basic>open</basic>) */
   bool parseInKey(xmlNodePtr node, Key& key, cpc::string& output);
   /** same as above, but store result in an 'optional'. Optional value is true if there
     * is a text node type child of the given node, though the text itself might still be empty */
   bool parseInKey(xmlNodePtr node, Key& key, Optional<cpc::string>& output);
   /** parse an optional of any type with a 'parse' method */
   template<typename T> bool parseOptional(xmlNodePtr node, Optional<T>& output);
   /** parse any type of element with a 'parse' method and, if parse returns true, adds the
     * result to the given vector and returns true. Allows for short syntax to parse children
     * eg parseAdd(child, output.notes) is all that's required to use the parse<Note> parse to parse
     *    a child node and, if it is actually a 'note' type child, add it to the notes vector */
   template<typename T> bool parseAdd(xmlNodePtr node, cpc::vector<T>& output);
   /** Parse a generic optional attribute, returns true if the attribute was found is now true */
   bool parseOptionalAttrib(xmlNodePtr node, Key& key, Optional<cpc::string>& output);
   /** Convenience method to parse both the 'from' and 'until' attribs which are common in many elements */
   void parseAttribsFromUntil(xmlNodePtr node, FromUntil& output);
   /** Convenience methods to parse optional or required 'id' attributes */
   void parseAttribId(xmlNodePtr node, Optional<Id>& output);
   bool parseAttribId(xmlNodePtr node, Id& output);
   /** Internal parser for Note_t type elements, parses node content and xml:lang, but does NOT check the node name
     * This allows use in both a <node> element and a <rpid:other> element to parse the content */
   void parseNoteGeneric(xmlNodePtr node, Note& output);
   /** parse Asterisk/Eyebeam style activities node, for backwards compatibility */
   bool parseOldActivities(xmlNodePtr node, Optional<Activities>& output);
   /** Parser for enum types which have a mapping defined in the XmlBase
     * Finds the matching enum value for the node name in the map and stores it in the output
     * If an optional 'storeBody' param is included, the node text will be written out to the addressed note */
   template<typename T> bool parseGenericEnum(xmlNodePtr node, EnumMap<T>& map, T& output, Note* storeBody=NULL);
   /** Find the first child of the given node which can be parsed using 'parseGenericEnum'
     * This is used for the place-is children like <rpid:audio><rpid:noisy/></rpid:audio>
     */
   template<typename T> bool parseGenericEnumParent(xmlNodePtr node, Key& key, EnumMap<T>& map, T& output, Note* storeBody=NULL);
};

/** XML encoder which encodes elements to xml which is written directly to an output stream given in the constructor
  * The encoder can be reused safely (but the output stream should probably be reset between uses) */
class CPCAPI2_SHAREDLIBRARY_API XmlEncoder: public XmlBase
{
/* The public parse/encode methods are all named parse and encode 
   with the same params to allow template format usage (eg use parseOptional without writing one for each parse method) */
public:
   XmlEncoder(std::ostream& output): m_output(output){}

   void encode(const Note& source);
   void encode(const Activity& source);
   void encode(const Activities& source);
   void encode(const Mood& source);
   void encode(const Moods& source);
   void encode(const AudioIsType& source);
   void encode(const VideoIsType& source);
   void encode(const TextIsType& source);
   void encode(const PlaceIs& source);
   void encode(const PlaceType& source);
   void encode(const PrivacyType& source);
   void encode(const Privacy& source);
   void encode(const RelationshipValue& source);
   void encode(const Relationship& source);
   void encode(const ServiceClassType& source);
   void encode(const ServiceClass& source);
   void encode(const SphereType& source);
   void encode(const Sphere& source);
   void encode(const StatusIcon& source);
   void encode(const TimeOffset& source);
   void encode(const UserInput& source);
   void encode(const Other& source);
   void encode(const Device& source);
   void encode(const Person& source);
   void encode(const Contact& source);
   void encode(const Status& source);
   void encode(const Tuple& source);
   void encode(const Presence& source);
private:
   /** Encode a given string as xml text content */
   void encodeString(const cpc::string& source);
   /** Encode a given string inside of a key, eg <status>open</status> */
   void encodeInKey(const cpc::string& source, Key& key, bool newline=true);
   void encodeInKey(const Optional<cpc::string>& source, Key& key, bool newline=true);

   /** encode an optional of any type with an 'encode' method */
   template<typename T> void encodeOptional(const Optional<T>& output);
   /** Encode each element in the given vector using its 'encode' method */
   template<typename T> void encodeEach(const cpc::vector<T>& source);
   /** Helper which encodes an attrib in a key=value format) */
   void encodeAttrib(const cpc::string& source, Key& key);
   /** encode a generic optional attrib, writes nothing (no whitespace even) if the attrib is not present */
   void encodeOptionalAttrib(const Optional<cpc::string>& source, Key& key);
   /** Convenience encoder which handles the 'from' and 'until' optional attribs which appear in many elements */
   void encodeAttribsFromUntil(const FromUntil& source);

   /** Convenience functions for the common 'id' attribs, both optional and required */
   void encodeAttribId(const Optional<Id>& source);
   void encodeAttribId(const Id& source);

   /** Generic note encoder which writes a note using the provided key. This is used both by the actual <note> element and
     * by other note types like <rpid:other> */
   void encodeNoteGeneric(const Note& source, Key& key);

   /** Translates an enum value into the corresponding string using an enum map (initialized in XmlBase) */
   template<typename T> void encodeGenericEnum(const T& source, const char* ns, EnumMap<T>& map);
   /** Same as above, but has an additional params to support element body. This allows for the 'other' enum values which often appear in
     * Presence enums and have 'note' type content. <rpid:other>myvalue</rpid:other>. If the source key matches hasBodyKey then the bodyNote value is written out
     * Instead of writing as an empty self-closed tag <rpid:away/> */
   template<typename T> void encodeGenericEnum(const T& source, const char* ns, EnumMap<T>& map, const T& hasBodyKey, const Note& bodyNote);
   /** Uses encodeGenericEnum to encode the given value, but wraps it inside of a key
     * This is a convenience function for the place-is children like <rpid:audio><rpid:noisy/></rpid:audio> */
   template<typename T> void encodeGenericEnumParent(const T& source, Key& key, EnumMap<T>& map);

   static std::string xmlEscapeString(const std::string& stringToEscape);
   
private:
   std::ostream& m_output;
};

}
}
#endif // CPCAPI2_SIP_PRESENCE_XML_ENCODER_H
