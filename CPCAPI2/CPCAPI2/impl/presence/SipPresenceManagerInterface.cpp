#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "cpcapi2utils.h"
#include "SipPresenceManagerInterface.h"
#include "../phone/PhoneInterface.h"
#include "../account/SipAccountInterface.h"
#include "../event/SipEventPublicationManagerInterface.h"
#include "../event/SipEventManagerInterface.h"
#include "../event/SipEventSubscriptionCreationInfo.h"
#include "../event/SipEventPublicationCreationInfo.h"
#include "SipPresenceXmlEncoder.h"
#include "SipPresenceConverter.h"

#include "../util/cpc_logger.h"

#include <rutil/Log.hxx>
#include <resip/stack/PlainContents.hxx>
#include <resip/stack/Mime.hxx>
#include <resip/dum/ClientSubscription.hxx>
#include <resip/dum/ServerSubscription.hxx>
#include <rutil/Random.hxx>

using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipAccount;

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_PRESENCE

namespace CPCAPI2
{
namespace SipPresence
{

SipPresenceManagerInterface::SipPresenceManagerInterface(Phone* phone)
   : EventSource2<EventHandler<SipPresenceSubscriptionHandler, SipAccount::SipAccountHandle>, EventHandler<SipPresencePublicationHandler, SipAccount::SipAccountHandle> >(dynamic_cast<PhoneInterface*>(phone)),
     mAccountIf(NULL),
     mPhone(dynamic_cast<PhoneInterface*>(phone))
{
   mAccountIf = dynamic_cast<SipAccountInterface*>(SipAccountManager::getInterface(phone));
   mSipEventIf = dynamic_cast<SipEventManagerInterface*>(SipEventManager::getInterface(phone));
   mSipPublicationIf = dynamic_cast<SipEventPublicationManagerInterface*>(SipEventPublicationManager::getInterface(phone));
}

SipPresenceManagerInterface::~SipPresenceManagerInterface()
{
   RefreshMap::iterator itRef = mRefreshMap.begin();
   for (; itRef != mRefreshMap.end(); ++itRef)
   {
      if (itRef->second->m_Timer)
      {
         itRef->second->m_Timer->cancel();
      }
      delete itRef->second;
   }
   for (AccountMap::const_iterator it = mAccountMap.begin(); it!= mAccountMap.end(); it++)
   {
      mSipEventIf->removeSdkObserver(it->second, it->first, "presence");
      delete it->second;
   }
   mAccountMap.clear();
}

void SipPresenceManagerInterface::onTimer(unsigned short timerId, void* appState)
{
   PresenceRefresh* refreshState = static_cast<PresenceRefresh*>(appState);
   restartFailedSubscription(refreshState);
}

void SipPresenceManagerInterface::Release()
{
   delete this;
}

int SipPresenceManagerInterface::setHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipPresenceSubscriptionHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipPresenceManagerInterface::setHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

int SipPresenceManagerInterface::setHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipPresenceSubscriptionHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipPresenceManager::setHandler");
      return kError;
   }

   if (acct->isEnabled() && handler != NULL)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new SipPresenceInternalEventHandler(*acct, *this);
         mAccountMap[account] = evtMan;
      }

      auto it = mSubscriptionHandlers.find(account);
      if (mSubscriptionHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }

      mSubscriptionHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }

      mSipEventIf->addSdkObserver(evtMan, account, "presence");
   }

   return kSuccess;
}

int SipPresenceManagerInterface::setPublicationHandler(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipPresencePublicationHandler* handler)
{
   resip::ReadCallbackBase* f = resip::resip_bind(&SipPresenceManagerInterface::setPublicationHandlerImpl, this, account, handler);

   if (handler == NULL)
   {
      // removing the handler involves two steps:
      // 1. process all pending items in the callback queue, since they could be bound to the handler which the app is asking
      // use to remove
      process(-1);

      // 2. block the calling thread (possibly the app's UI thread) until we can remove the handler on the main SDK thread
      // we block so that we can guarantee that when this setHandler(..) method returns, the SDK will not call the existing
      // handler
      executeOnSdkThread(f);
   }
   else
   {
      postToSdkThread(f);
   }

   return kSuccess;
}

int SipPresenceManagerInterface::setPublicationHandlerImpl(
      CPCAPI2::SipAccount::SipAccountHandle account,
      SipPresencePublicationHandler* handler)
{
   AccountMap::iterator it = mAccountMap.find(account);
   SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipPresenceManager::setPublicationHandler");
      return kError;
   }

   if (acct->isEnabled() && handler != NULL)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::setHandler was called after account enabled: " + cpc::to_string(account));
   }
   else
   {
      if (evtMan == NULL)
      {
         evtMan = new SipPresenceInternalEventHandler(*acct, *this);
         mAccountMap[account] = evtMan;
      }

      auto it = mPublicationHandlers.find(account);
      if (mPublicationHandlers.end() != it)
      {
        removeAppHandler(it->second, account);
      }

      mPublicationHandlers[account] = handler;
      if (nullptr != handler)
      {
         addAppHandler(handler, account);
      }

      mSipPublicationIf->addSdkObserver(evtMan, account, "presence");
   }
   return kSuccess;
}

SipEventPublicationHandle SipPresenceManagerInterface::createPublication(SipAccountHandle account, const SipPresencePublicationSettings& pubSettings)
{
   DebugLog(<< __FUNCTION__ << " " << account << ", " << pubSettings);

   SipEventPublicationSettings settings;
   settings.eventPackage = "presence";
   settings.expiresSeconds = pubSettings.expiresSeconds;
   settings.supportedMimeTypes.push_back(MimeType("application","pidf+xml"));
   SipEventPublicationHandle publication = mSipPublicationIf->createPublication(account, settings);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::createPublicationImpl, this, account, publication));

   return publication;
}

int SipPresenceManagerInterface::createPublicationImpl(SipAccountHandle account, SipEventPublicationHandle publication)
{
   SipAccountImpl* acct = mAccountIf->getAccountImpl(account);
   if (!acct)
   {
      mAccountIf->fireError("Invalid account handle for SipPresenceManager::createPublication");
      return kError;
   }

   const SipAccountSettings& actSettings = acct->getSettings();
   cpc::string aor = "sip:" + actSettings.username + "@" + (actSettings.domain.empty() ? "unknown" : actSettings.domain);
   mSipPublicationIf->setTarget(publication, aor);
   return kSuccess;
}

int SipPresenceManagerInterface::publish(SipEventPublicationHandle publication, const Presence& presenceState)
{
   DebugLog(<< __FUNCTION__ << " " << publication << ", " << presenceState);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::publishImpl, this, publication, presenceState));
   return kSuccess;
}

int SipPresenceManagerInterface::publishImpl(SipEventPublicationHandle publication, const Presence& presenceState)
{
   std::string presenceXML;
   {
      std::ostringstream out;
      XmlEncoder encoder(out);
      encoder.encode(presenceState);
      presenceXML = (out.str());
   }
   SipEventState eventState;
   eventState.eventPackage = "presence";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "pidf+xml";
   eventState.contentLength = presenceXML.size();
   eventState.contentUTF8.assign(presenceXML.c_str());
   mSipPublicationIf->publish(publication, eventState);
   return kSuccess;
}

int SipPresenceManagerInterface::publish(SipEventPublicationHandle publication, CannedStatus presenceStatus)
{
   DebugLog(<< __FUNCTION__ << " " << publication << ", " << presenceStatus);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::publishCannedImpl, this, publication, presenceStatus, StatusUpdateParameters()));
   return kSuccess;
}

int SipPresenceManagerInterface::publish(SipEventPublicationHandle publication, CannedStatus presenceStatus, const StatusUpdateParameters& params)
{
   DebugLog(<< __FUNCTION__ << " " << publication << ", " << presenceStatus << ", " << params);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::publishCannedImpl, this, publication, presenceStatus, params));
   return kSuccess;
}

int SipPresenceManagerInterface::publishCannedImpl(SipEventPublicationHandle publication, CannedStatus presenceStatus, const StatusUpdateParameters& params)
{
   SipEvent::SipEventPublicationCreationInfo* ci = mSipPublicationIf->getCreationInfo(publication);
   if (!ci)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::publish was called with invalid publication handle: " + cpc::to_string(publication));
   }
   else
   {
      AccountMap::iterator it = mAccountMap.find(ci->account);
      SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
      if (evtMan != NULL)
      {
         Presence presenceState = evtMan->buildPresenceDoc(publication, presenceStatus);
         SipPresenceConverter::updateStatusParameters(presenceState, params);
         return publish(publication, presenceState);
      }
      else
      {
         mAccountIf->fireError("SipPresenceManagerInterface::publish was called, but no SipPresenceInternalEventHandler was found for account: " + cpc::to_string(ci->account));
      }
   }
   return kSuccess;
}

int SipPresenceManagerInterface::endPublish(SipEventPublicationHandle publication)
{
   DebugLog(<< __FUNCTION__ << " " << publication);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::endPublishImpl, this, publication));
   return kSuccess;
}

int SipPresenceManagerInterface::endPublishImpl(SipEventPublicationHandle publication)
{
   mSipPublicationIf->end(publication);
   return kSuccess;
}

int SipPresenceManagerInterface::refreshPublish(SipEventPublicationHandle publication)
{
   DebugLog(<< __FUNCTION__ << " " << publication);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::refreshPublishImpl, this, publication));
   return kSuccess;
}

int SipPresenceManagerInterface::refreshPublishImpl(SipEventPublicationHandle publication)
{
   mSipPublicationIf->refresh(publication);
   return kSuccess;
}

int SipPresenceManagerInterface::addParticipant(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << targetAddress);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::addParticipantImpl, this, subscription, targetAddress));
   return kSuccess;
}

int SipPresenceManagerInterface::addParticipantImpl(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   cpc::string transformedAddress = targetAddress;
   SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL)
   {
      AddressTransformer* transformer = mPhone->getAddressTransformer();
      if(transformer != NULL)
      {
         AddressTransformationContext context;
         context.addressUsageType = AddressUsageType_SipPresence;

         SipAccount::SipAccountImpl* acct = mAccountIf->getAccountImpl(ci->account);
         if (!acct)
         {
            mAccountIf->fireError("Invalid account handle for SipPresenceManager::addParticipant");
            return kError;
         }

         context.registrationDomain = acct->getSettings().domain;
         transformer->applyTransformation(targetAddress, context, transformedAddress);
      }
   }
   return mSipEventIf->addParticipantImpl(subscription, transformedAddress);
}

int SipPresenceManagerInterface::end(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::endImpl, this, subscription));
   return kSuccess;
}

int SipPresenceManagerInterface::endImpl(SipEventSubscriptionHandle subscription)
{
   PresenceRefresh* refresh = NULL;
   RefreshMap::iterator itr = mRefreshMap.begin();
   // Remove this entry from the refresh map
   while (itr != mRefreshMap.end())
   {
      refresh = itr->second;
      if (refresh->subscription == subscription)
      {
         refresh->m_Timer->cancel();
         mRefreshMap.erase(itr);
         delete refresh;
         break;
      }
      else
      {
         ++itr;
      }
   }

   // Remove its settings also - client will need to apply them again
   SettingsMap::iterator settingsIt = mSettingsMap.find(subscription);
   if (settingsIt != mSettingsMap.end())
   {
      mSettingsMap.erase(settingsIt);
   }

   // in preparation for handling OBELISK-2155, we need to avoid re-introducing the problem in OBELISK-665,
   // so switch to reason of noresource such that remote ends don't immediately try to re-subscribe.
   return mSipEventIf->end(subscription, CPCAPI2::SipEvent::SipSubscriptionTerminateReason_NoResource);
}

int SipPresenceManagerInterface::refresh(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::refreshImpl, this, subscription));
   return kSuccess;
}

int SipPresenceManagerInterface::refreshImpl(SipEventSubscriptionHandle subscription)
{
   SipEvent::SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (ci != NULL && ci->dumClientSubscriptionHandle.isValid())
   {
      ci->dumClientSubscriptionHandle->requestRefresh();
   }
   else
   {
      RefreshMap::iterator itr = mRefreshMap.find(subscription);
      if (itr != mRefreshMap.end())
      {
         PresenceRefresh* refresh = itr->second;
         restartFailedSubscription(refresh);
      }
   }

   return kSuccess;
}

int SipPresenceManagerInterface::accept(SipEventSubscriptionHandle subscription, const Presence& presenceState)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceState);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::acceptImpl, this, subscription, presenceState));
   return kSuccess;
}

int SipPresenceManagerInterface::acceptImpl(SipEventSubscriptionHandle subscription, const Presence& presenceState)
{
   std::string presenceXML;
   {
      std::ostringstream out;
      XmlEncoder encoder(out);
      encoder.encode(presenceState);
      presenceXML = (out.str());
   }

   SipEventState eventState;
   eventState.eventPackage = "presence";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "pidf+xml";
   eventState.contentLength = presenceXML.size();
   eventState.contentUTF8 = presenceXML.c_str();
   mSipEventIf->accept(subscription, eventState);
   return kSuccess;
}

int SipPresenceManagerInterface::accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceStatus);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::acceptCannedImpl, this, subscription, presenceStatus, StatusUpdateParameters()));
   return kSuccess;
}

int SipPresenceManagerInterface::accept(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceStatus << ", " << statusUpdateParams);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::acceptCannedImpl, this, subscription, presenceStatus, statusUpdateParams));
   return kSuccess;
}

int SipPresenceManagerInterface::acceptCannedImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams)
{
   SipEvent::SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (!ci)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::accept was called with invalid subscription handle: " + cpc::to_string(subscription));
   }
   else
   {
      AccountMap::iterator it = mAccountMap.find(ci->account);
      SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
      if (evtMan != NULL)
      {
         Presence presenceState = evtMan->buildPresenceDoc(subscription, presenceStatus);
         SipPresenceConverter::updateStatusParameters(presenceState, statusUpdateParams);
         std::string presenceXML;
         {
            std::ostringstream out;
            XmlEncoder encoder(out);
            encoder.encode(presenceState);
            presenceXML = (out.str());
         }

         SipEventState eventState;
         eventState.eventPackage = "presence";
         eventState.expiresTimeMs = 3600;
         eventState.mimeType = "application";
         eventState.mimeSubType = "pidf+xml";
         eventState.contentLength = presenceXML.size();
         eventState.contentUTF8 = presenceXML.c_str();
         mSipEventIf->accept(subscription, eventState);
      }
      else
      {
         mAccountIf->fireError("SipPresenceManagerInterface::accept was called, but no SipPresenceInternalEventHandler was found for account: " + cpc::to_string(ci->account));
      }
   }
   return kSuccess;
}

int SipPresenceManagerInterface::provisionalAccept(SipEventSubscriptionHandle subscription)
{
   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::provisionalAcceptImpl, this, subscription));
   return kSuccess;
}

int SipPresenceManagerInterface::provisionalAcceptImpl(SipEventSubscriptionHandle subscription)
{
   SipEventState eventState;
   eventState.eventPackage = "presence";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "pidf+xml";
   mSipEventIf->provisionalAccept(subscription, eventState);
   return kSuccess;
}

int SipPresenceManagerInterface::notify(SipEventSubscriptionHandle subscription, const Presence& presenceState)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceState);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::notifyImpl, this, subscription, presenceState));
   return kSuccess;
}

int SipPresenceManagerInterface::notifyImpl(SipEventSubscriptionHandle subscription, const Presence& presenceState)
{
   std::unique_ptr<std::string> presenceXML(new std::string());
   {
      std::ostringstream out;
      XmlEncoder encoder(out);
      encoder.encode(presenceState);
      *presenceXML = (out.str());
   }

   SipEventState eventState;
   eventState.eventPackage = "presence";
   eventState.expiresTimeMs = 3600;
   eventState.mimeType = "application";
   eventState.mimeSubType = "pidf+xml";
   eventState.contentLength = presenceXML->size();
   //eventState.contentUTF8 = presenceXML.c_str();    // optimization -- pass the contents as the 3rd param to notifyImpl to prevent a bunch of copies
   mSipEventIf->notifyImpl(subscription, eventState, presenceXML.get());

   return kSuccess;
}

int SipPresenceManagerInterface::notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceStatus);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::notifyCannedImpl, this, subscription, presenceStatus, StatusUpdateParameters()));
   return kSuccess;
}

int SipPresenceManagerInterface::notify(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceStatus << ", " << statusUpdateParams);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::notifyCannedImpl, this, subscription, presenceStatus, statusUpdateParams));
   return kSuccess;
}

int SipPresenceManagerInterface::notifyCannedImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus, const StatusUpdateParameters& statusUpdateParams)
{
   SipEvent::SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (!ci)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::notify was called with invalid subscription handle: " + cpc::to_string(subscription));
   }
   else
   {
      AccountMap::iterator it = mAccountMap.find(ci->account);
      SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
      if (evtMan != NULL)
      {
         Presence presenceState = evtMan->buildPresenceDoc(subscription, presenceStatus);
         SipPresenceConverter::updateStatusParameters(presenceState, statusUpdateParams);
         std::unique_ptr<std::string> presenceXML(new std::string());
         {
            std::ostringstream out;
            XmlEncoder encoder(out);
            encoder.encode(presenceState);
            *presenceXML = (out.str());
         }

         SipEventState eventState;
         eventState.eventPackage = "presence";
         eventState.expiresTimeMs = 3600;
         eventState.mimeType = "application";
         eventState.mimeSubType = "pidf+xml";
         eventState.contentLength = presenceXML->size();
         //eventState.contentUTF8 = presenceXML->c_str();    // optimization -- pass the contents as the 3rd param to notifyImpl to prevent a bunch of copies
         mSipEventIf->notifyImpl(subscription, eventState, presenceXML.get());
      }
      else
      {
         mAccountIf->fireError("SipPresenceManagerInterface::notify was called, but no SipPresenceInternalEventHandler was found for account: " + cpc::to_string(ci->account));
      }
   }
   return kSuccess;
}

int SipPresenceManagerInterface::preparePresence(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << presenceStatus);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::preparePresenceImpl, this, subscription, presenceStatus));
   return kSuccess;
}

int SipPresenceManagerInterface::preparePresenceImpl(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   SipEvent::SipEventSubscriptionCreationInfo* ci = mSipEventIf->getCreationInfo(subscription);
   if (!ci)
   {
      mAccountIf->fireError("SipPresenceManagerInterface::preparePresence was called with invalid subscription handle: " + cpc::to_string(subscription));
   }
   else
   {
      AccountMap::iterator it = mAccountMap.find(ci->account);
      SipPresenceInternalEventHandler* evtMan = (it == mAccountMap.end() ? NULL : it->second);
      if (evtMan != NULL)
      {
         evtMan->preparePresence(subscription, presenceStatus);
      }
      else
      {
         mAccountIf->fireError("SipPresenceManagerInterface::preparePresence was called, but no SipPresenceInternalEventHandler was found for account: " + cpc::to_string(ci->account));
      }
   }
   return kSuccess;
}

int SipPresenceManagerInterface::applySubscriptionSettings(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << settings);

   postToSdkThread(resip::resip_bind(&SipPresenceManagerInterface::applySubscriptionSettingsImpl, this, subscription, settings));
   return kSuccess;
}

int SipPresenceManagerInterface::applySubscriptionSettingsImpl(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings)
{
   // Save the Presence subscription settings (needed for presence refresh in case of failure)
   mSettingsMap[subscription] = settings;
   return applyPresenceSettings(subscription, settings);
}

int SipPresenceManagerInterface::processSubscriptionEnd(SipAccount::SipAccountHandle account, SipEventSubscriptionHandle subscription, const SipEvent::SubscriptionEndedEvent& event)
{
   if (event.subscriptionType != SipSubscriptionType_Outgoing)
   {
      // Only outgoing subscriptions are handled here
      return kSuccess;
   }

   if (event.isNotifyTerminated && event.reason == "deactivated")
   {
      // This scenario is handled by SipEventManager. No need to perform any retry here
      return kSuccess;
   }

   SettingsMap::iterator settingsIt = mSettingsMap.find(subscription);
   if (settingsIt == mSettingsMap.end())
   {
      // not handled - subscription didn't have any settings applied
      return kSuccess;
   }

   SipPresenceSubscriptionSettings subscriptionSetting = settingsIt->second;
   cpc::vector<int>::iterator it = std::find(subscriptionSetting.retryWithTheseResponseCodesOnly.begin(), subscriptionSetting.retryWithTheseResponseCodesOnly.end(), event.statusCode);
   if (event.isNotifyTerminated || it != subscriptionSetting.retryWithTheseResponseCodesOnly.end())
   {
      unsigned int resub_delay = 0;
      // Retry logic similar to pj mechanism
      // if req = subscribe n code = 481 and failure is for a subcription refresh, retry immediately resub_delay = 500ms;
      // if req = notify n term reason = timeout  resub_delay = 500
      // if req = notify n term reason = probation or given-up and retry-after in Subscription-State header, resub_delay = rety_after * 1000;
      //if (resub_delay == -1) {resub_delay = 300*1000 - 2500 + (rand()%5000);  }
      if (event.statusCode == 481 && !event.initialSubscribe)
      {
         resub_delay = 500;
      }
      else if (event.isNotifyTerminated)
      {
         if (event.reason == "timeout")
            resub_delay = 500;
         else if ((event.reason == "probation" || event.reason == "given-up") && event.retryAfter > 0)
            resub_delay = event.retryAfter * 1000;
      }
      const int DEFAULT_REFRESH_TIMER = 300;

      if (resub_delay == 0)
      {
         resub_delay = DEFAULT_REFRESH_TIMER * 1000 - 2500 + (resip::Random::getRandom() % 5000);
      }

      if (resub_delay/1000 < subscriptionSetting.minTimeBeforeResubscribeInSeconds)
         resub_delay = 1000 * subscriptionSetting.minTimeBeforeResubscribeInSeconds;
      else if (resub_delay/1000 > subscriptionSetting.maxTimeBeforeResubscribeInSeconds)
         resub_delay = 1000 * subscriptionSetting.maxTimeBeforeResubscribeInSeconds;

      // OBELISK-4896 -- need a different way of letting app know of retry time
      //*retryTime = resub_delay;

      PresenceRefresh* refresh = new PresenceRefresh(mPhone->getSdkModuleThread(), event.remoteAddress, subscription);
      refresh->m_Timer->expires_from_now(resub_delay);
      refresh->account = account;
      refresh->statusCode = event.statusCode;
      refresh->m_Timer->async_wait(this, account, refresh);
      mRefreshMap[subscription] = refresh;
   }
   else
   {
      // no retry possible - settings are not needed anymore, client will need to restart subscription
      mSettingsMap.erase(settingsIt);
   }

   return kSuccess;
}

int SipPresenceManagerInterface::processNewSubscription(SipAccount::SipAccountHandle account, SipEventSubscriptionHandle subscription, const SipEvent::NewSubscriptionEvent& event)
{
   if (event.subscriptionType != SipSubscriptionType_Incoming)
   {
      return kSuccess;
   }

   PresenceRefresh* refresh = NULL;
   RefreshMap::iterator itr = mRefreshMap.begin();

   // Remove this entry from the refresh map
   while (itr != mRefreshMap.end())
   {
      refresh = itr->second;
      // Received a subscription from the remote address, we can cancel the timer and start a subscription right away
      if (refresh->account == account && refresh->buddyAddress == event.remoteAddress)
      {
         restartFailedSubscription(refresh);
         break;
      }
      else
      {
         ++itr;
      }
   }

   // note: if the other endpoint crashed and is now restarted, we won't know until the subscription expires and we try to re-register.
   // cpsi use to handle this by sending a re-subscribe for the old subscription dialog here, and if that failed, it sent out a new subscription
   return kSuccess;
}

void SipPresenceManagerInterface::restartFailedSubscription(PresenceRefresh* refreshState)
{
   // (Re)start the subscription
   if (!mSipEventIf->recreateSubscription(refreshState->account, refreshState->subscription))
      return;

   // re-apply presence event settings, in case the creation info was destroyed in the meantime
   SettingsMap::const_iterator settingsIt = mSettingsMap.find(refreshState->subscription);
   if (settingsIt != mSettingsMap.end())
   {
      SipPresenceSubscriptionSettings setting = settingsIt->second;
      applyPresenceSettings(refreshState->subscription, setting);
   }

   PresenceRefresh* refresh = NULL;
   RefreshMap::iterator itr = mRefreshMap.find(refreshState->subscription);

   // Remove this entry from the refresh map
   if (itr != mRefreshMap.end())
   {
      refresh = itr->second;
      if (refresh->account == refreshState->account && refresh->subscription == refreshState->subscription)
      {
         refresh->m_Timer->cancel();

         addParticipant(refreshState->subscription, refresh->buddyAddress);
         start(refreshState->subscription);
         mRefreshMap.erase(itr);
         delete refresh;
      }
   }
}

int SipPresenceManagerInterface::start(SipEventSubscriptionHandle subscription)
{
   DebugLog(<< __FUNCTION__ << " " << subscription);

   return mSipEventIf->start(subscription);
}

int SipPresenceManagerInterface::setEventServer(SipEventSubscriptionHandle subscription, const cpc::string& targetAddress)
{
   return mSipEventIf->setEventServer(subscription, targetAddress);
}

SipEventSubscriptionHandle SipPresenceManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account)
{
   DebugLog(<< __FUNCTION__ << " " << account);

   return mSipEventIf->createSubscription(account);
}

SipEventSubscriptionHandle SipPresenceManagerInterface::createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h)
{
   DebugLog(<< __FUNCTION__ << " " << account << ", " << h);

   return mSipEventIf->createSubscription(account, h);
}

int SipPresenceManagerInterface::applyPresenceSettings(SipEventSubscriptionHandle subscription, const SipPresenceSubscriptionSettings& settings)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << settings);

   SipEventSubscriptionSettings eventSettings;
   eventSettings.eventPackage = "presence";
   eventSettings.expiresSeconds = settings.expiresSeconds;
   eventSettings.supportedMimeTypes.push_back(MimeType("application", "pidf+xml"));
   return mSipEventIf->applySubscriptionSettingsImpl(subscription, eventSettings);
}

int SipPresenceManagerInterface::reject(SipEventSubscriptionHandle subscription, unsigned int rejectReason)
{
   DebugLog(<< __FUNCTION__ << " " << subscription << ", " << rejectReason);

   return mSipEventIf->reject(subscription, rejectReason);
}

std::ostream& operator<<(std::ostream& os, const PresencePublicationSuccessEvent& evt)
{
  return os << "PresencePublicationSuccessEvent";
}

std::ostream& operator<<(std::ostream& os, const PresencePublicationFailureEvent& evt)
{
  return os << "PresencePublicationFailureEvent";
}

std::ostream& operator<<(std::ostream& os, const PresencePublicationRemoveEvent& evt)
{
  return os << "PresencePublicationRemoveEvent";
}

std::ostream& operator<<(std::ostream& os, const PresencePublicationErrorEvent& evt)
{
  return os << "PresencePublicationErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const NewPresenceSubscriptionEvent& evt)
{
  return os << "NewPresenceSubscriptionEvent";
}

std::ostream& operator<<(std::ostream& os, const PresenceSubscriptionEndedEvent& evt)
{
  return os << "PresenceSubscriptionEndedEvent";
}

std::ostream& operator<<(std::ostream& os, const IncomingPresenceStatusEvent& evt)
{
  return os << "IncomingPresenceStatusEvent";
}

std::ostream& operator<<(std::ostream& os, const PresenceSubscriptionStateChangedEvent& evt)
{
  return os << "PresenceSubscriptionStateChangedEvent";
}

std::ostream& operator<<(std::ostream& os, const PresenceReadyToSendEvent& evt)
{
  return os << "PresenceReadyToSendEvent";
}

std::ostream& operator<<(std::ostream& os, const SipPresence::ErrorEvent& evt)
{
  return os << "SipPresence::ErrorEvent";
}

std::ostream& operator<<(std::ostream& os, const SipPresencePublicationSettings& settings)
{
   return os << "SipPresencePublicationSettings";
}

std::ostream& operator<<(std::ostream& os, const SipPresenceSubscriptionSettings& settings)
{
   return os << "SipPresenceSubscriptionSettings";
}

std::ostream& operator<<(std::ostream& os, const Presence&)
{
   return os << "Presence";
}

std::ostream& operator<<(std::ostream& os, const StatusUpdateParameters&)
{
   return os << "StatusUpdateParameters";
}

}
}
#endif
