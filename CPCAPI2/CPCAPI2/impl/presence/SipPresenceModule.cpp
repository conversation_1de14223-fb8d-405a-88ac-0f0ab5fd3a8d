#include "brand_branded.h"

#include "interface/public/presence/SipPresenceManager.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipPresenceManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipPresence
{
SipPresenceManager* SipPresenceManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
   PhoneInterface* phone = dynamic_cast<PhoneInterface*>(cpcPhone);
   SipEvent::SipEventManager::getInterface(cpcPhone); // register dependency
   return _GetInterface<SipPresenceManagerInterface>(phone, "SipPresenceManagerInterface");
#else
   return NULL;
#endif
}
}
}
