#include "SipPresenceConfigurationManagerInterface.h"

namespace CPCAPI2
{
   namespace SipPresence
   {
      SipPresenceConfigurationManagerInterface::SipPresenceConfigurationManagerInterface(Phone* phone) : mPhone(dynamic_cast<PhoneInterface*>(phone)), mModelType(DEFAULT_PRESENCE_MODEL)
      {
      }

      void
      SipPresenceConfigurationManagerInterface::Release()
      {
         delete this;
      }

      void
      SipPresenceConfigurationManagerInterface::setSipPresenceModel(const SipPresenceModelType model)
      {
         mModelType = model;
      }

      SipPresenceModelType
      SipPresenceConfigurationManagerInterface::getSipPresenceModel() const
      {
         return mModelType;
      }
   }
}