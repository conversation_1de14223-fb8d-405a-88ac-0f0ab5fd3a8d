#include "SipPresenceModelOps.h"
#include "cpcapi2utils.h"


namespace CPCAPI2
{
namespace SipPresence
{

template <typename T>
bool operator==(const Optional<T> &lhs, const Optional<T> &rhs)
{
   if (!lhs.present)
   {
      return rhs.present==false;
      // value must not be compared when present==false
   }
   else
   {
      return lhs.value == rhs.value;
   }
}

bool operator==(const FromUntil &lhs, const FromUntil &rhs)
{
   return lhs.from == rhs.from && 
          lhs.until == rhs.until;
}

bool operator==(const Note &lhs, const Note &rhs)
{
   return lhs.lang == rhs.lang &&
          lhs.text == rhs.text;
}

bool operator==(const Activity &lhs, const Activity &rhs)
{
   if (lhs.activity == rhs.activity)
   {
      if (lhs.activity == ActivityType_Other)
      {
         return lhs.otherValue == rhs.otherValue;
      } 
      else
      {
         // otherValue only needs to be compared when activity is Other
         return true;
      }
   }
   return false;
}

bool operator==(const Activities &lhs, const Activities &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.notes == rhs.notes &&
         lhs.activities == rhs.activities;
}

bool operator==(const Mood &lhs, const Mood &rhs)
{
   if (lhs.mood == rhs.mood)
   {
      if (lhs.mood == MoodValue_Other)
      {
         return lhs.otherValue == rhs.otherValue;
      } 
      else
      {
         // otherValue only needs to be compared when mood is Other
         return true;
      }
   }
   return false;
}

bool operator==(const Moods &lhs, const Moods &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.notes == rhs.notes &&
         lhs.moods == rhs.moods;
}

bool operator==(const PlaceIs &lhs, const PlaceIs &rhs)
{
   return  lhs.id == rhs.id && 
         lhs.fromUntil == rhs.fromUntil &&
         lhs.notes == rhs.notes &&
         lhs.audio == rhs.audio &&
         lhs.video == rhs.video &&
         lhs.text == rhs.text;
}

bool operator==(const PlaceType &lhs, const PlaceType &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.notes == rhs.notes &&
         lhs.placeType.otherValue == rhs.placeType.otherValue;
}

bool operator==(const Privacy &lhs, const Privacy &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.notes == rhs.notes &&
         lhs.privacy == rhs.privacy;
}

bool operator==(const RelationshipValue &lhs, const RelationshipValue &rhs)
{
   if (lhs.relationship == rhs.relationship)
   {
      if (lhs.relationship == RelationshipType_Other)
      {
         return lhs.otherValue == rhs.otherValue;
      }
      else
      {
         return true;
      }
   }
   return false;
}

bool operator==(const Relationship &lhs, const Relationship &rhs)
{
   return lhs.relationship == rhs.relationship && 
         lhs.notes == rhs.notes;
}

bool operator==(const ServiceClass &lhs, const ServiceClass &rhs)
{
   return lhs.notes == rhs.notes &&
         lhs.serviceClass == rhs.serviceClass;
}

bool operator==(const Sphere &lhs, const Sphere &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.sphere == rhs.sphere;
}

bool operator==(const StatusIcon &lhs, const StatusIcon &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.uri == rhs.uri;
}

bool operator==(const TimeOffset &lhs, const TimeOffset &rhs)
{
   return lhs.id == rhs.id &&
         lhs.fromUntil == rhs.fromUntil &&
         lhs.description == rhs.description &&
         lhs.offset == rhs.offset;
}

bool operator==(const UserInput &lhs, const UserInput &rhs)
{
   return lhs.id == rhs.id &&
         lhs.activeIdle == rhs.activeIdle &&
         lhs.idleThreshold == rhs.idleThreshold &&
         lhs.lastInput == rhs.lastInput;
}

bool operator==(const Device &lhs, const Device &rhs)
{
   return lhs.id == rhs.id &&
         lhs.deviceID == rhs.deviceID &&
         lhs.notes == rhs.notes &&
         lhs.timestamp == rhs.timestamp;
}


bool operator==(const Person &lhs, const Person &rhs)
{
   return lhs.id == rhs.id &&
         lhs.notes == rhs.notes &&
         lhs.timestamp == rhs.timestamp &&
         lhs.activities == rhs.activities &&
         lhs.classEnt == rhs.classEnt &&
         lhs.mood == rhs.mood &&
         lhs.placeIs == rhs.placeIs &&
         lhs.placeType == rhs.placeType &&
         lhs.privacy == rhs.privacy &&
         lhs.sphere == rhs.sphere &&
         lhs.statusIcon == rhs.statusIcon &&
         lhs.timeOffset == rhs.timeOffset;
}

bool operator==(const Contact &lhs, const Contact &rhs)
{
   return lhs.priority == rhs.priority &&
      lhs.contact == rhs.contact;
}

bool operator==(const Status &lhs, const Status &rhs)
{
   return lhs.basic == rhs.basic;
}

bool operator==(const Tuple &lhs, const Tuple &rhs)
{
   return lhs.id == rhs.id &&
         lhs.notes == rhs.notes &&
         lhs.timestamp == rhs.timestamp &&
         lhs.status == rhs.status &&
         lhs.contact == rhs.contact &&
         lhs.deviceID == rhs.deviceID &&
         lhs.relationship == rhs.relationship &&
         lhs.serviceClass == rhs.serviceClass;
}

bool operator==(const Presence &lhs, const Presence &rhs)
{
    return lhs.notes == rhs.notes &&
         lhs.tuples == rhs.tuples &&
         lhs.devices == rhs.devices &&
         lhs.persons == rhs.persons;
}

}
}