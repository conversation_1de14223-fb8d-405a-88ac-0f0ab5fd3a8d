#include "SipPresenceXml.h"
#include <iostream>
#include <string>
#include <string.h>
#include "cpcapi2utils.h"
#include <boost/algorithm/string.hpp>

namespace CPCAPI2
{
namespace SipPresence
{

XmlRoot::XmlRoot(const std::string xml)
{
   init(xml.c_str(), xml.length());
}

XmlRoot::XmlRoot(const char* xml, unsigned int len)
{
   init(xml, len);
}

void XmlRoot::init(const char* xml, unsigned int len)
{
   m_doc = ::xmlReadMemory(xml, len, "noname.xml", NULL, XML_PARSE_NONET | XML_PARSE_NOERROR);
   if (m_doc == NULL)
   {
      std::cerr << "Failed to parse document: " << xml;
   }
}

xmlNodePtr XmlRoot::root()
{
   return ::xmlDocGetRootElement(m_doc);
}

XmlRoot::~XmlRoot()
{
   if (m_doc){
      ::xmlFreeDoc(m_doc);
   }
   // do not call here; can cause crashes. see LibxmlSharedUsage.h
   //::xmlCleanupParser();
}

std::string xmlEscapeStringReverse(const std::string& stringToEscape)
{
   std::string escapedString(stringToEscape);

   boost::replace_all(escapedString, "&quot;", "\"");
   boost::replace_all(escapedString, "&apos;", "'");
   boost::replace_all(escapedString, "&lt;", "<");
   boost::replace_all(escapedString, "&gt;", ">");
   boost::replace_all(escapedString, "&amp;", "&");

   return escapedString;
}

/** read a property on an xml node
  * If the property is found, it is stored in 'output'
  * Returns whether the property was found
  */
bool xmlNodeProperty(xmlNodePtr node, const char* propKey, cpc::string& output)
{
   xmlChar* chars = ::xmlGetProp(node, (const xmlChar*) propKey);
   cpc::string result;
   if(chars)
   {
      output = (const char*)chars;
      ::xmlFree(chars);
      return true;
   }
   output = "";
   return false;
}


bool xmlNodeText(xmlNodePtr node, cpc::string& output)
{
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE)
      {
         if (child->content)
         {
            output = xmlEscapeStringReverse((const char*)child->content).c_str();
         }
         else
         {
            output = "";
         }
         return true;
      }
   }
   output = "";
   return false;
}

bool XmlKey::nameEq(const xmlNodePtr node) const
{
   return strcmp((const char*)node->name, name) == 0;
}

std::string XmlKey::str() const
{
   if(ns)
      return std::string(ns) + ":" + std::string(name);
   return std::string(name);
}

std::ostream& operator<<(std::ostream& out, const XmlKey& key)
{
   if(key.ns)
      out << key.ns << ":";
   out << key.name;
   return out;
}

}
}
