#include "brand_branded.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipPresenceInternalEventHandler.h"
#include "presence/SipPresenceSubscriptionHandler.h"
#include "presence/SipPresencePublicationHandler.h"
#include "SipPresenceXml.h"
#include "SipPresenceXmlEncoder.h"
#include "../util/DumFpCommand.h"
#include "SipPresenceConverter.h"
#include "../util/cpc_logger.h"
#include <rutil/Random.hxx>
#include "SipPresenceManagerInterface.h"
#include "SipPresenceConfigurationManagerInterface.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::SIP_PRESENCE

using namespace CPCAPI2::SipEvent;
using namespace resip;
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
using namespace CPCAPI2::WatcherInfo;
#endif

namespace CPCAPI2
{
namespace SipPresence
{
SipPresenceInternalEventHandler::SipPresenceInternalEventHandler(CPCAPI2::SipAccount::SipAccountImpl& acct, CPCAPI2::SipPresence::SipPresenceManager& presenceManager)
   : mAccount(acct),
     mPresenceConfigurationManager(NULL),
     mOutgoingPresence(NULL)
{
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   mWinfoManager = dynamic_cast<WatcherInfoManagerInterface*>(mAccount.getPhone()->getInterfaceByName("WatcherInfoManagerInterface"));
#endif
   mPresenceManager = dynamic_cast<SipPresenceManagerInterface*>(mAccount.getPhone()->getInterfaceByName("SipPresenceManagerInterface"));
}

SipPresenceInternalEventHandler::~SipPresenceInternalEventHandler()
{
   delete mOutgoingPresence;
}

SipPresence::Presence& SipPresenceInternalEventHandler::getOutgoingPresence()
{
   if (mOutgoingPresence == NULL)
   {
      mOutgoingPresence = new SipPresence::Presence();

      SipPresence::Tuple t;
      t.id = (resip::Random::getCryptoRandomHex(9).c_str());
      t.status.basic.value = BasicStatusType_Open;
      SipPresence::Person p;
      p.id = (resip::Random::getCryptoRandomHex(9).c_str());

      mOutgoingPresence->tuples.push_back(t);
      mOutgoingPresence->persons.push_back(p);
      mOutgoingPresence->entity = "sip:" + mAccount.getSettings().username + "@" + mAccount.getSettings().domain;
   }

   return *mOutgoingPresence;
}

int SipPresenceInternalEventHandler::onNewSubscription(SipEventSubscriptionHandle subscription, const NewSubscriptionEvent& args)
{
   NewPresenceSubscriptionEvent presenceArgs;
   presenceArgs.account = args.account;
   presenceArgs.remoteAddress = args.remoteAddress;
   presenceArgs.remoteDisplayName = args.remoteDisplayName;
   presenceArgs.subscriptionType = args.subscriptionType;
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   mWinfoManager = dynamic_cast<WatcherInfoManagerInterface*>(mAccount.getPhone()->getInterfaceByName("WatcherInfoManagerInterface"));
   if (mWinfoManager)
   {
      mWinfoManager->addWatcher(args.account, args.remoteAddress);
   }
#endif
   mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onNewSubscription), mAccount.getHandle(), subscription, presenceArgs);
   mAccount.getPhone()->getSdkModuleThread().post(resip::resip_bind(&SipPresenceManagerInterface::processNewSubscription, mPresenceManager, mAccount.getHandle(), subscription, args));
   return kSuccess;
}

int SipPresenceInternalEventHandler::onSubscriptionEnded(SipEventSubscriptionHandle subscription, const SubscriptionEndedEvent& args)
{
   PresenceSubscriptionEndedEvent presenceArgs;
   presenceArgs.endReason = args.endReason;
   presenceArgs.retryTime = 0;
   presenceArgs.statusCode = args.statusCode;
   mAccount.getPhone()->getSdkModuleThread().post(resip::resip_bind(&SipPresenceManagerInterface::processSubscriptionEnd, mPresenceManager, mAccount.getHandle(), subscription, args));
   mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onSubscriptionEnded), mAccount.getHandle(), subscription, presenceArgs);
#if (CPCAPI2_BRAND_WATCHER_INFO_MODULE == 1)
   mWinfoManager = dynamic_cast<WatcherInfoManagerInterface*>(mAccount.getPhone()->getInterfaceByName("WatcherInfoManagerInterface"));
   if (mWinfoManager)
   {
      mWinfoManager->removeWatcher(mAccount.getHandle(), args.remoteAddress);
   }
#endif

   return kSuccess;
}

int SipPresenceInternalEventHandler::onIncomingEventState(SipEventSubscriptionHandle subscription, const IncomingEventStateEvent& args)
{
   if (args.eventState.contentUTF8.empty())
   {
      // Ignore empty presence body, likely just a re-subscription
      return kSuccess;
   }
   IncomingPresenceStatusEvent presenceArgs;
   XmlParser parser;
   XmlRoot xmlRoot(args.eventState.contentUTF8.c_str());
   if (xmlRoot.root())
   {
      parser.parse(xmlRoot.root(), presenceArgs.presence);
      // set the presence model
      //SipPresenceModelType model = mPresenceConfigurationManager->getSipPresenceModel();
      SipPresenceModelType model;
      mPresenceConfigurationManager = dynamic_cast<SipPresenceConfigurationManagerInterface*>(mAccount.getPhone()->getInterfaceByName("SipPresenceConfigurationManagerInterface"));
      if (mPresenceConfigurationManager)
         model = mPresenceConfigurationManager->getSipPresenceModel();
      else
         model = DEFAULT_PRESENCE_MODEL;
      presenceArgs.status = SipPresenceConverter::parseCannedStatus(presenceArgs.presence, model);
      for (unsigned int i = 0; i < presenceArgs.presence.persons.size();i++)
      {
         for (unsigned int j = 0; j < presenceArgs.presence.persons[i].notes.size(); j++)
         if (strcmp(presenceArgs.presence.persons[i].notes[j].lang.value.c_str(),"en") == 0)
         {
            presenceArgs.presence.persons[i].notes[j] = Note();
         }
      }
      mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onIncomingPresenceStatus), mAccount.getHandle(), subscription, presenceArgs);
      return kSuccess;
   }
   else
   {
      std::cerr << "SipPresenceInternalEventHandler::onIncomingEventState failed due to invalid presence XML" << std::endl;
      return kError;
   }
}

int SipPresenceInternalEventHandler::onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const SubscriptionStateChangedEvent& args)
{
   PresenceSubscriptionStateChangedEvent presenceArgs;
   presenceArgs.subscriptionState = args.subscriptionState;

   mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onSubscriptionStateChanged), mAccount.getHandle(), subscription, presenceArgs);

   return kSuccess;
}

int SipPresenceInternalEventHandler::onNotifySuccess(SipEventSubscriptionHandle subscription, const NotifySuccessEvent& args)
{
   return kSuccess;
}

int SipPresenceInternalEventHandler::onNotifyFailure(SipEventSubscriptionHandle subscription, const NotifyFailureEvent& args)
{
   return kSuccess;
}

int SipPresenceInternalEventHandler::onError(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, const CPCAPI2::SipEvent::ErrorEvent& args)
{
   ErrorEvent evt;
   evt.errorText = args.errorText;
   mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onError), mAccount.getHandle(), subscription, evt);

   return kSuccess;
}

int SipPresenceInternalEventHandler::onPublicationSuccess(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationSuccessEvent& args)
{
   PresencePublicationSuccessEvent evt;
   mPresenceManager->fireEvent(cpcEvent(SipPresencePublicationHandler, onPublicationSuccess), mAccount.getHandle(), publication, evt);
   return kSuccess;
}

int SipPresenceInternalEventHandler::onPublicationFailure(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationFailureEvent& args)
{
   PresencePublicationFailureEvent evt;
   evt.reason = args.reason;
   mPresenceManager->fireEvent(cpcEvent(SipPresencePublicationHandler, onPublicationFailure), mAccount.getHandle(), publication, evt);
   return kSuccess;
}

int SipPresenceInternalEventHandler::onPublicationRemove(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationRemoveEvent & args)
{
   PresencePublicationRemoveEvent evt;
   mPresenceManager->fireEvent(cpcEvent(SipPresencePublicationHandler, onPublicationRemove), mAccount.getHandle(), publication, evt);
   return kSuccess;
}

int SipPresenceInternalEventHandler::onError(CPCAPI2::SipEvent::SipEventPublicationHandle publication, const CPCAPI2::SipEvent::PublicationErrorEvent& args)
{
   PresencePublicationErrorEvent evt;
   evt.errorText = args.errorText;
   mPresenceManager->fireEvent(cpcEvent(SipPresencePublicationHandler, onError), mAccount.getHandle(), publication, evt);
   return kSuccess;
}

CPCAPI2::SipPresence::Presence SipPresenceInternalEventHandler::buildPresenceDoc(CPCAPI2::SipEvent::SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   Presence presDoc = getOutgoingPresence(); // copy, since we don't want to blow away our id's
   
   SipPresenceModelType model;
   mPresenceConfigurationManager = dynamic_cast<SipPresenceConfigurationManagerInterface*>(mAccount.getPhone()->getInterfaceByName("SipPresenceConfigurationManagerInterface"));
   if (mPresenceConfigurationManager)
      model = mPresenceConfigurationManager->getSipPresenceModel();
   else
      model = DEFAULT_PRESENCE_MODEL;
   
   SipPresenceConverter::prepareCannedStatus(presenceStatus, presDoc, model);
   return presDoc;
}

int SipPresenceInternalEventHandler::preparePresence(SipEventSubscriptionHandle subscription, CannedStatus presenceStatus)
{
   PresenceReadyToSendEvent presenceArgs;
   presenceArgs.presence = buildPresenceDoc(subscription, presenceStatus);
   mPresenceManager->fireEvent(cpcEvent(SipPresenceSubscriptionHandler, onPresenceReadyToSend), mAccount.getHandle(), subscription, presenceArgs);
   return kSuccess;
}

}
}
#endif
