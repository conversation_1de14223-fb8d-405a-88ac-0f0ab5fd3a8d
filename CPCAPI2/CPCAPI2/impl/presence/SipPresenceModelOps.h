#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_MODEL_OPS_H)
#define CPCAPI2_SIP_PRESENCE_MODEL_OPS_H

#include "presence/SipPresenceModel.h"

namespace CPCAPI2
{
namespace SipPresence
{

   /* Here are defined the == operators to compare all of the SipPresence data types
    * The other  parsing/encoding classes do _not_ depend on these and do not include this header
    * Currently these are only used for unit testing comparison of objects to expected results */
   bool operator==(const FromUntil &lhs, const FromUntil &rhs);
   bool operator==(const Note &lhs, const Note &rhs);
   bool operator==(const Activity &lhs, const Activity &rhs);
   bool operator==(const Activities &lhs, const Activities &rhs);
   bool operator==(const Mood &lhs, const Mood &rhs);
   bool operator==(const Moods &lhs, const Moods &rhs);
   bool operator==(const PlaceIs &lhs, const PlaceIs &rhs);
   bool operator==(const PlaceType &lhs, const PlaceType &rhs);
   bool operator==(const Privacy &lhs, const Privacy &rhs);
   bool operator==(const RelationshipValue &lhs, const RelationshipValue &rhs);
   bool operator==(const Relationship &lhs, const Relationship &rhs);
   bool operator==(const ServiceClass &lhs, const ServiceClass &rhs);
   bool operator==(const Sphere &lhs, const Sphere &rhs);
   bool operator==(const StatusIcon &lhs, const StatusIcon &rhs);
   bool operator==(const TimeOffset &lhs, const TimeOffset &rhs);
   bool operator==(const UserInput &lhs, const UserInput &rhs);
   bool operator==(const Device &lhs, const Device &rhs);
   bool operator==(const Person &lhs, const Person &rhs);
   bool operator==(const Contact &lhs, const Contact &rhs);
   bool operator==(const Status &lhs, const Status &rhs);
   bool operator==(const Tuple &lhs, const Tuple &rhs);
   bool operator==(const Presence &lhs, const Presence &rhs);

}
}
#endif // CPCAPI2_SIP_PRESENCE_MODEL_OPS_H
