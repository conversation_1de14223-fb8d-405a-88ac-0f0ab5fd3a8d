#include "SipPresenceXmlEncoder.h"
#include <iostream>
#include <sstream>
#include <stdio.h>
#include <stdarg.h>
#include <boost/algorithm/string.hpp>
#include "cpcapi2utils.h"

namespace CPCAPI2
{
namespace SipPresence
{

typedef const XmlKey Key;

/** Log an error in decoding
  */
std::ostream& error(xmlNodePtr node)
{
   std::cerr << "XML Presence - ERROR";
   if (node)
   {
      // walk parents to get a path to error
      std::vector<std::string> parentNames;
      for(xmlNodePtr n = node; n; n=n->parent)
      {
         std::string name;
         if (n->name)
            name = (const char*)n->name;
         else if (!n->parent)
            name = "ROOT";
         parentNames.push_back(name);
      }
      // print parents in order
      std::cerr << " @";
      for(size_t i=0; i<parentNames.size(); i++)
      {
         if (i > 0)
            std::cerr << ".";
         std::cerr << parentNames[parentNames.size()-1-i];
      }
   }
   std::cerr << ": ";
   return std::cerr;
}

void XmlBase::initActivityMap()
{
   m_activityMap.add("appointment", ActivityType_Appointment);
   m_activityMap.add("away", ActivityType_Away);
   m_activityMap.add("breakfast", ActivityType_Breakfast);
   m_activityMap.add("busy", ActivityType_Busy);
   m_activityMap.add("dinner", ActivityType_Dinner);
   m_activityMap.add("holiday", ActivityType_Holiday);
   m_activityMap.add("in-transit", ActivityType_InTransit);
   m_activityMap.add("looking-for-work", ActivityType_LookingForWork);
   m_activityMap.add("meal", ActivityType_Meal);
   m_activityMap.add("on-the-phone", ActivityType_OnThePhone);
   m_activityMap.add("performance", ActivityType_Performance);
   m_activityMap.add("permanent-absence", ActivityType_PermanentAbsence);
   m_activityMap.add("playing", ActivityType_Playing);
   m_activityMap.add("presentation", ActivityType_Presentation);
   m_activityMap.add("shopping", ActivityType_Shopping);
   m_activityMap.add("sleeping", ActivityType_Sleeping);
   m_activityMap.add("spectator", ActivityType_Spectator);
   m_activityMap.add("steering", ActivityType_Steering);
   m_activityMap.add("travel", ActivityType_Travel);
   m_activityMap.add("tv", ActivityType_Tv);
   m_activityMap.add("vacation", ActivityType_Vacation);
   m_activityMap.add("working", ActivityType_Working);
   m_activityMap.add("worship", ActivityType_Worship);
   m_activityMap.add("unknown", ActivityType_Unknown);
   m_activityMap.add("other", ActivityType_Other);
   m_activityMap.add("lunch", ActivityType_Lunch);
}

void XmlBase::initMoodMap()
{
   m_moodMap.add("afraid", MoodValue_Afraid);
   m_moodMap.add("amazed", MoodValue_Amazed);
   m_moodMap.add("angry", MoodValue_Angry);
   m_moodMap.add("annoyed", MoodValue_Annoyed);
   m_moodMap.add("anxious", MoodValue_Anxious);
   m_moodMap.add("ashamed", MoodValue_Ashamed);
   m_moodMap.add("bored", MoodValue_Bored);
   m_moodMap.add("brave", MoodValue_Brave);
   m_moodMap.add("calm", MoodValue_Calm);
   m_moodMap.add("cold", MoodValue_Cold);
   m_moodMap.add("confused", MoodValue_Confused);
   m_moodMap.add("contented", MoodValue_Contented);
   m_moodMap.add("cranky", MoodValue_Cranky);
   m_moodMap.add("curious", MoodValue_Curious);
   m_moodMap.add("depressed", MoodValue_Depressed);
   m_moodMap.add("disappointed", MoodValue_Disappointed);
   m_moodMap.add("disgusted", MoodValue_Disgusted);
   m_moodMap.add("distracted", MoodValue_Distracted);
   m_moodMap.add("embarrassed", MoodValue_Embarrassed);
   m_moodMap.add("excited", MoodValue_Excited);
   m_moodMap.add("flirtatious", MoodValue_Flirtatious);
   m_moodMap.add("frustrated", MoodValue_Frustrated);
   m_moodMap.add("grumpy", MoodValue_Grumpy);
   m_moodMap.add("guilty", MoodValue_Guilty);
   m_moodMap.add("happy", MoodValue_Happy);
   m_moodMap.add("hot", MoodValue_Hot);
   m_moodMap.add("humbled", MoodValue_Humbled);
   m_moodMap.add("humiliated", MoodValue_Humiliated);
   m_moodMap.add("hungry", MoodValue_Hungry);
   m_moodMap.add("hurt", MoodValue_Hurt);
   m_moodMap.add("impressed", MoodValue_Impressed);
   m_moodMap.add("in_awe", MoodValue_InAwe);
   m_moodMap.add("in_love", MoodValue_InLove);
   m_moodMap.add("indignant", MoodValue_Indignant);
   m_moodMap.add("interested", MoodValue_Interested);
   m_moodMap.add("invincible", MoodValue_Invincible);
   m_moodMap.add("jealous", MoodValue_Jealous);
   m_moodMap.add("lonely", MoodValue_Lonely);
   m_moodMap.add("mean", MoodValue_Mean);
   m_moodMap.add("moody", MoodValue_Moody);
   m_moodMap.add("nervous", MoodValue_Nervous);
   m_moodMap.add("neutral", MoodValue_Neutral);
   m_moodMap.add("offended", MoodValue_Offended);
   m_moodMap.add("playful", MoodValue_Playful);
   m_moodMap.add("proud", MoodValue_Proud);
   m_moodMap.add("relieved", MoodValue_Relieved);
   m_moodMap.add("remorseful", MoodValue_Remorseful);
   m_moodMap.add("restless", MoodValue_Restless);
   m_moodMap.add("sad", MoodValue_Sad);
   m_moodMap.add("sarcastic", MoodValue_Sarcastic);
   m_moodMap.add("serious", MoodValue_Serious);
   m_moodMap.add("shocked", MoodValue_Shocked);
   m_moodMap.add("shy", MoodValue_Shy);
   m_moodMap.add("sick", MoodValue_Sick);
   m_moodMap.add("sleepy", MoodValue_Sleepy);
   m_moodMap.add("stressed", MoodValue_Stressed);
   m_moodMap.add("surprised", MoodValue_Surprised);
   m_moodMap.add("thirsty", MoodValue_Thirsty);
   m_moodMap.add("worried", MoodValue_Worried);
   m_moodMap.add("unknown", MoodValue_Unknown);
   m_moodMap.add("other", MoodValue_Other);
}

void XmlBase::initAudioIsMap()
{
   m_audioIsMap.add("noisy", AudioIsType_Noisy);
   m_audioIsMap.add("ok", AudioIsType_Ok);
   m_audioIsMap.add("quiet", AudioIsType_Quiet);
   m_audioIsMap.add("unknown", AudioIsType_Unknown);
}

void XmlBase::initVideoIsMap()
{
   m_videoIsMap.add("toobright", VideoIsType_TooBright);
   m_videoIsMap.add("ok", VideoIsType_Ok);
   m_videoIsMap.add("dark", VideoIsType_Dark);
   m_videoIsMap.add("unknown", VideoIsType_Unknown);
}

void XmlBase::initTextIsMap()
{
   m_textIsMap.add("uncomfortable", TextIsType_Uncomfortable);
   m_textIsMap.add("inappropriate", TextIsType_Inappropriate);
   m_textIsMap.add("ok", TextIsType_Ok);
   m_textIsMap.add("unknown", TextIsType_Unknown);
}

void XmlBase::initPrivacyTypeMap()
{
   m_privacyTypeMap.add("audio",PrivacyType_Audio);
   m_privacyTypeMap.add("text",PrivacyType_Text);
   m_privacyTypeMap.add("video",PrivacyType_Video);
   m_privacyTypeMap.add("unknown",PrivacyType_Unknown);

}

void XmlBase::initRelationshipTypeMap()
{
   m_relationshipTypeMap.add("assistant", RelationshipType_Assistant);
   m_relationshipTypeMap.add("associate", RelationshipType_Associate); 
   m_relationshipTypeMap.add("family", RelationshipType_Family); 
   m_relationshipTypeMap.add("friend", RelationshipType_Friend); 
   m_relationshipTypeMap.add("self", RelationshipType_Self);
   m_relationshipTypeMap.add("supervisor", RelationshipType_Supervisor);
   m_relationshipTypeMap.add("unknown", RelationshipType_Unknown);
   m_relationshipTypeMap.add("other", RelationshipType_Other);
}

void XmlBase::initServiceClassTypeMap()
{
   m_serviceClassTypeMap.add("courier", ServiceClassType_Courier);
   m_serviceClassTypeMap.add("electronic", ServiceClassType_Electronic); 
   m_serviceClassTypeMap.add("freight", ServiceClassType_Freight); 
   m_serviceClassTypeMap.add("in-person", ServiceClassType_InPerson); 
   m_serviceClassTypeMap.add("postal", ServiceClassType_Postal);
   m_serviceClassTypeMap.add("unknown", ServiceClassType_Unknown);
}

void XmlBase::initSphereTypeMap()
{
   m_sphereTypeMap.add("home", SphereType_Home);
   m_sphereTypeMap.add("work", SphereType_Work);
   m_sphereTypeMap.add("unknown", SphereType_Unknown);
}

void XmlBase::initActiveIdleMap()
{
   m_activeIdleMap.add("active", ActiveIdle_Active);
   m_activeIdleMap.add("idle", ActiveIdle_Idle); 
}

void XmlBase::initBasicStatusMap()
{
   m_basicStatusMap.add("open", BasicStatusType_Open);
   m_basicStatusMap.add("closed", BasicStatusType_Closed); 
}

XmlBase::XmlBase()
{
   initActivityMap();
   initMoodMap();
   initAudioIsMap();
   initVideoIsMap();
   initTextIsMap();
   initPrivacyTypeMap();
   initRelationshipTypeMap();
   initServiceClassTypeMap();
   initSphereTypeMap();
   initActiveIdleMap();
   initBasicStatusMap();
}

const char* NS_DM = "dm";
const char* NS_RPID = "rpid";

Key KEY_ID("id");
Key KEY_FROM("from");
Key KEY_UNTIL("until");
Key KEY_XML_LANG("xml", "lang");

Key KEY_RPID_ACTIVITIES(NS_RPID, "activities");
Key KEY_RPID_MOODS(NS_RPID, "mood");
Key KEY_RPID_AUDIO_IS(NS_RPID, "audio");
Key KEY_RPID_VIDEO_IS(NS_RPID, "video");
Key KEY_RPID_TEXT_IS(NS_RPID, "text");
Key KEY_RPID_PLACE_IS(NS_RPID, "place-is");
Key KEY_RPID_PLACE_TYPE(NS_RPID, "place-type");
Key KEY_RPID_PRIVACY(NS_RPID, "privacy");
Key KEY_RPID_RELATIONSHIP(NS_RPID, "relationship");
Key KEY_RPID_SERVICE_CLASS(NS_RPID, "service-class");
Key KEY_RPID_SPHERE(NS_RPID, "sphere");
Key KEY_RPID_STATUS_ICON(NS_RPID, "status-icon");
Key KEY_RPID_TIME_OFFSET(NS_RPID, "time-offset");
Key KEY_RPID_USER_INPUT(NS_RPID, "user-input");

Key KEY_DEVICE(NS_DM, "device");
Key KEY_PERSON(NS_DM, "person");
Key KEY_CONTACT("contact");
Key KEY_BASIC("basic");
Key KEY_STATUS("status");

Key KEY_TUPLE("tuple");
Key KEY_PRESENCE("presence");

Key KEY_NOTE("note"); // Presence/Tuple notes
Key KEY_RPID_NOTE(NS_RPID, "note"); // Privacy/Moods notes
Key KEY_DM_NOTE(NS_DM, "note"); // Person notes

bool parseAttrib(xmlNodePtr node, Key& key, cpc::string& output)
{
   if (!xmlNodeProperty(node, key.name, output))
   {
      error(node) << "Missing required property " << key.name << std::endl;
      return false;
   }
   return true;
}

void XmlEncoder::encodeAttrib(const cpc::string& source, Key& key)
{
   //TODO encode xml attrib value
   m_output << " " << key << "=\"" << source << "\"";
}

bool XmlParser::parseOptionalAttrib(xmlNodePtr node, Key& key, Optional<cpc::string>& output)
{
   output.present = xmlNodeProperty(node, key.name, output.value);
   return output.present;
}

void XmlEncoder::encodeOptionalAttrib(const Optional<cpc::string>& source, Key& key)
{
   if(source.present)
      encodeAttrib(source.value, key);
}


template<typename T>
bool XmlParser::parseGenericEnumParent(xmlNodePtr node, Key& key, EnumMap<T>& map, T& output, Note* storeBody)
{
   if (!key.nameEq(node))
      return false;
   for(xmlNodePtr child = node->children; child; child=child->next)
   {
      if (child->type != XML_TEXT_NODE)
      {
         if (parseGenericEnum(child, map, output, storeBody))
            return true;
         error(child) << "Unexpected child" << std::endl;
      }
   }
   return false;
}

template<typename T>
void XmlEncoder::encodeGenericEnumParent(const T& source, Key& key, EnumMap<T>& map)
{
   m_output << "<" << key << ">\n";
   encodeGenericEnum(source, key.ns, map);
   m_output << "\n</" << key << ">";
}

template<typename T>
bool XmlParser::parseGenericEnum(xmlNodePtr node, EnumMap<T>& map, T& output, Note* storeBody)
{
   std::string name((const char*)node->name);
   const T* value = map.find(name);
   if (value)
   {
      output = *value;
      if(storeBody)
         parseNoteGeneric(node, *storeBody);
      return true;
   }
   return false;
}

template<typename T>
void XmlEncoder::encodeGenericEnum(const T& source, const char* ns, EnumMap<T>& map)
{
   const std::string* str = map.findKey(source);
   if (str)
      m_output << "<" << Key(ns, str->c_str()) << "/>";
   else
      error(NULL) << "No key found in map for enum value " << source << std::endl;
}

template<typename T>
void XmlEncoder::encodeGenericEnum(const T& source, const char* ns, EnumMap<T>& map, const T& hasBodyKey, const Note& bodyNote)
{
   const std::string* str = map.findKey(source);
   if (str)
   {
      Key key(ns, str->c_str());
      if(source == hasBodyKey)
         encodeNoteGeneric(bodyNote, key);
      else
         m_output << "<" << key << "/>";
   }
   else
      error(NULL) << "No key found in map for enum value " << source << std::endl;
}


void XmlParser::parseAttribsFromUntil(xmlNodePtr node, FromUntil& output)
{
   parseOptionalAttrib(node, KEY_FROM, output.from);
   parseOptionalAttrib(node, KEY_UNTIL, output.until);
}

void XmlEncoder::encodeAttribsFromUntil(const FromUntil& source)
{
   encodeOptionalAttrib(source.from, KEY_FROM);
   encodeOptionalAttrib(source.until, KEY_UNTIL);
}

void XmlParser::parseAttribId(xmlNodePtr node, Optional<Id>& output)
{
   parseOptionalAttrib(node, KEY_ID, output);
}

bool XmlParser::parseAttribId(xmlNodePtr node, Id& output)
{
   return parseAttrib(node, KEY_ID, output);
}

void XmlEncoder::encodeAttribId(const Optional<Id>& source)
{
   encodeOptionalAttrib(source, KEY_ID);
}

void XmlEncoder::encodeAttribId(const Id& source)
{
   encodeAttrib(source, KEY_ID);
}

template<typename T> bool XmlParser::parseOptional(xmlNodePtr node, Optional<T>& output)
{
   if(!output.present)
   {
      output.present = parse(node, output.value);
      return output.present;
   }
   return false;
}


template<typename T> void XmlEncoder::encodeOptional(const Optional<T>& source)
{
   if(source.present)
   {
      encode(source.value);
      m_output << "\n";
   }
}

void XmlEncoder::encodeString(const cpc::string& source)
{
   m_output << xmlEscapeString(source.c_str());
}

void XmlEncoder::encodeInKey(const cpc::string& source, Key& key, bool newline)
{
   m_output << "<" << key << ">";
   m_output << source;
   m_output << "</" << key << ">";
   if(newline)
      m_output << "\n";
}

void XmlEncoder::encodeInKey(const Optional<cpc::string>& source, Key& key, bool newline)
{
   if(source.present)
      encodeInKey(source.value, key, newline);
}

bool XmlParser::parseInKey(xmlNodePtr node, Key& key, Optional<cpc::string>& output)
{
   if (key.nameEq(node))
   {
      output.present = xmlNodeText(node, output.value);
      return output.present;
   }
   return false;
}

bool XmlParser::parseInKey(xmlNodePtr node, Key& key, cpc::string& output)
{
   if (key.nameEq(node))
      return xmlNodeText(node, output);
   return false;
}


template<typename T> bool XmlParser::parseAdd(xmlNodePtr node, cpc::vector<T>& output)
{
   T obj;
   if (parse(node, obj))
   {
      output.push_back(obj);
      return true;
   }
   return false;
}

template<typename T> void XmlEncoder::encodeEach(const cpc::vector<T>& source)
{
   for(size_t i=0; i<source.size(); i++)
   {
      encode(source[i]);
      m_output << "\n";
   }
}

void XmlParser::parseNoteGeneric(xmlNodePtr node, Note& output)
{
   xmlNodeText(node, output.text);
   parseOptionalAttrib(node, KEY_XML_LANG, output.lang);
}

bool XmlParser::parseOldActivities(xmlNodePtr node, Optional<Activities>& output)
{
   /* 
   Try to parse old RPID format based on draft-ietf-simple-rpid-02 which looks like this:
   <pp:person><status>
      <ep:activities><ep:busy/></ep:activities>
   </status></pp:person>
   */

   if (!output.present && KEY_STATUS.nameEq(node))
   {
      for(xmlNodePtr child=node->children; child; child=child->next)
      {
         if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
         else if (parseOptional(child, output)) {
            return true;
         }
      }
   }

   return false;
}

bool XmlParser::parse(xmlNodePtr node, Note& output)
{
   if(KEY_NOTE.nameEq(node))
   {
      parseNoteGeneric(node, output);
      return true;
   }
   return false;
}

void XmlEncoder::encodeNoteGeneric(const Note& source, Key& key)
{
   m_output << "<" << key;
   encodeOptionalAttrib(source.lang, KEY_XML_LANG);
   m_output << ">";
   encodeString(source.text);
   m_output << "</" << key << ">";
}

void XmlEncoder::encode(const Note& source)
{
   if (!source.ns.present) encodeNoteGeneric(source, KEY_NOTE);
   else if (source.ns.value == NS_DM) encodeNoteGeneric(source, KEY_DM_NOTE);
   else if (source.ns.value == NS_RPID) encodeNoteGeneric(source, KEY_RPID_NOTE);
   else encodeNoteGeneric(source, KEY_NOTE);
}

bool XmlParser::parse(xmlNodePtr node, Activity& output)
{
   return parseGenericEnum(node, m_activityMap, output.activity, &output.otherValue);
}

void XmlEncoder::encode(const Activity& source)
{
   encodeGenericEnum(source.activity, "rpid", m_activityMap, ActivityType_Other, source.otherValue);
}

bool XmlParser::parse(xmlNodePtr node, Activities& output)
{
   if (!KEY_RPID_ACTIVITIES.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.activities) ||
            parseAdd(child, output.notes)) {} // child node handled ok
      else
      {
         // no parser handled the child node
         error(child) << "Unrecognized child of 'Activities' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Activities& source)
{
   m_output << "<" << KEY_RPID_ACTIVITIES;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   encodeEach(source.notes);
   encodeEach(source.activities);
   m_output << "</" << KEY_RPID_ACTIVITIES << ">";
}


bool XmlParser::parse(xmlNodePtr node, Mood& output)
{
   return parseGenericEnum(node, m_moodMap, output.mood, &output.otherValue);
}

void XmlEncoder::encode(const Mood& source)
{
   encodeGenericEnum(source.mood, "rpid", m_moodMap, MoodValue_Other, source.otherValue);
}

bool XmlParser::parse(xmlNodePtr node, Moods& output)
{
   if (!KEY_RPID_MOODS.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.moods) ||
            parseAdd(child, output.notes)) {} // child node handled ok
      else
      {
         // no parser handled the child node
         error(child) << "Unrecognized child of 'Moods' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Moods& source)
{
   m_output << "<" << KEY_RPID_MOODS;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   Notes notes = source.notes;
   for (Notes::iterator it = notes.begin(); it != notes.end(); ++it) it->ns = NS_RPID;
   encodeEach(notes);
   encodeEach(source.moods);
   m_output << "</" << KEY_RPID_MOODS << ">";
}


bool XmlParser::parse(xmlNodePtr node, AudioIsType& output)
{
   return parseGenericEnumParent<AudioIsType>(node, KEY_RPID_AUDIO_IS, m_audioIsMap, output);
}

void XmlEncoder::encode(const AudioIsType& source)
{
   encodeGenericEnumParent<AudioIsType>(source, KEY_RPID_AUDIO_IS, m_audioIsMap);
}
   
bool XmlParser::parse(xmlNodePtr node, VideoIsType& output)
{
   return parseGenericEnumParent<VideoIsType>(node, KEY_RPID_VIDEO_IS, m_videoIsMap, output);
}

void XmlEncoder::encode(const VideoIsType& source)
{
   encodeGenericEnumParent<VideoIsType>(source, KEY_RPID_VIDEO_IS, m_videoIsMap);
}
   
bool XmlParser::parse(xmlNodePtr node, TextIsType& output)
{
   return parseGenericEnumParent<TextIsType>(node, KEY_RPID_TEXT_IS, m_textIsMap, output);
}

void XmlEncoder::encode(const TextIsType& source)
{
   encodeGenericEnumParent<TextIsType>(source, KEY_RPID_TEXT_IS, m_textIsMap);
}

bool XmlParser::parse(xmlNodePtr node, PlaceIs& output)
{
   if (!KEY_RPID_PLACE_IS.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   output.audio.present = output.video.present = output.text.present = false;

   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseOptional<AudioIsType>(child, output.audio) ||
         parseOptional<VideoIsType>(child, output.video) ||
         parseOptional<TextIsType>(child, output.text) ||
         parseAdd(child, output.notes)) {} // child node handled ok
      else
      {
         // no parser handled the child node
         error(child) << "Unrecognized child of 'place-is' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const PlaceIs& source)
{
   m_output << "<" << KEY_RPID_PLACE_IS;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   encodeEach(source.notes);
   encodeOptional(source.audio);
   encodeOptional(source.video);
   encodeOptional(source.text);
   m_output << "</" << KEY_RPID_PLACE_IS << ">";
}

bool XmlParser::parse(xmlNodePtr node, PlaceType& output)
{
   if (!KEY_RPID_PLACE_TYPE.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.notes)) {} // node handled ok
      else
      {
         if (Key("other").nameEq(child))
            parseNoteGeneric(child, output.placeType.otherValue);
         else
         {
            error(child) << "Unrecognized child of 'place-type' element" << std::endl;
            success = false;
         }
      }
   }
   return success;
}
void XmlEncoder::encode(const PlaceType& source)
{
   m_output << "<" << KEY_RPID_PLACE_TYPE;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   encodeEach(source.notes);
   encodeNoteGeneric(source.placeType.otherValue, Key("rpid","other"));
   m_output << "\n";
   m_output << "</" << KEY_RPID_PLACE_TYPE << ">";
}

bool XmlParser::parse(xmlNodePtr node, PrivacyType& output)
{
   return parseGenericEnum(node, m_privacyTypeMap, output);
}

void XmlEncoder::encode(const PrivacyType& source)
{
   encodeGenericEnum(source, KEY_RPID_PRIVACY.ns, m_privacyTypeMap);
}

bool XmlParser::parse(xmlNodePtr node, Privacy& output)
{
   if (!KEY_RPID_PRIVACY.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.notes) ||
            parseAdd(child, output.privacy)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'privacy' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Privacy& source)
{
   m_output << "<" << KEY_RPID_PRIVACY;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   Notes notes = source.notes;
   for (Notes::iterator it = notes.begin(); it != notes.end(); ++it) it->ns = NS_RPID;
   encodeEach(notes);
   encodeEach(source.privacy);
   m_output << "</" << KEY_RPID_PRIVACY << ">";
}

bool XmlParser::parse(xmlNodePtr node, RelationshipValue& output)
{
   return parseGenericEnum(node, m_relationshipTypeMap, output.relationship, &output.otherValue);
}

void XmlEncoder::encode(const RelationshipValue& source)
{
   encodeGenericEnum(source.relationship, "rpid", m_relationshipTypeMap, RelationshipType_Other, source.otherValue);
}

bool XmlParser::parse(xmlNodePtr node, Relationship& output)
{
   if (!KEY_RPID_RELATIONSHIP.nameEq(node))
      return false;
   bool success = true;
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.notes) ||
            parse(child, output.relationship)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'relationship' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Relationship& source)
{
   m_output << "<" << KEY_RPID_RELATIONSHIP;
   m_output << ">\n";
   encodeEach(source.notes);
   encode(source.relationship);
   m_output << "\n</" << KEY_RPID_RELATIONSHIP << ">";
}

bool XmlParser::parse(xmlNodePtr node, ServiceClassType& output)
{
   return parseGenericEnum(node, m_serviceClassTypeMap, output);
}

void XmlEncoder::encode(const ServiceClassType& source)
{
   encodeGenericEnum(source, "rpid", m_serviceClassTypeMap);
}

bool XmlParser::parse(xmlNodePtr node, ServiceClass& output)
{
   if (!KEY_RPID_SERVICE_CLASS.nameEq(node))
      return false;
   bool success = true;
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.notes) ||
            parse(child, output.serviceClass)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'service-class' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const ServiceClass& source)
{
   m_output << "<" << KEY_RPID_SERVICE_CLASS;
   m_output << ">\n";
   encodeEach(source.notes);
   encode(source.serviceClass);
   m_output << "\n</" << KEY_RPID_SERVICE_CLASS << ">";
}

bool XmlParser::parse(xmlNodePtr node, SphereType& output)
{
   return parseGenericEnum(node, m_sphereTypeMap, output);
}

void XmlEncoder::encode(const SphereType& source)
{
   encodeGenericEnum(source, "rpid", m_sphereTypeMap);
}

bool XmlParser::parse(xmlNodePtr node, Sphere& output)
{
   if (!KEY_RPID_SPHERE.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseOptional(child, output.sphere)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'sphere' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Sphere& source)
{
   m_output << "<" << KEY_RPID_SPHERE;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">\n";
   encodeOptional(source.sphere);
   m_output << "</" << KEY_RPID_SPHERE << ">";
}

bool XmlParser::parse(xmlNodePtr node, StatusIcon& output)
{
   if (!KEY_RPID_STATUS_ICON.nameEq(node))
      return false;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   xmlNodeText(node, output.uri);
   return true;
}

void XmlEncoder::encode(const StatusIcon& source)
{
   m_output << "<" << KEY_RPID_STATUS_ICON;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   m_output << ">";
   encodeString(source.uri);
   m_output << "</" << KEY_RPID_STATUS_ICON << ">";
}

bool XmlParser::parse(xmlNodePtr node, TimeOffset& output)
{
   if (!KEY_RPID_TIME_OFFSET.nameEq(node))
      return false;
   parseAttribId(node, output.id);
   parseAttribsFromUntil(node, output.fromUntil);
   parseOptionalAttrib(node, Key("description"), output.description);
   cpc::string text;
   xmlNodeText(node, text);
   output.offset = atoi(text);
   return true;
}

void XmlEncoder::encode(const TimeOffset& source)
{
   m_output << "<" << KEY_RPID_TIME_OFFSET;
   encodeAttribId(source.id);
   encodeAttribsFromUntil(source.fromUntil);
   encodeOptionalAttrib(source.description, Key("description"));
   m_output << ">";
   m_output << source.offset;
   m_output << "</" << KEY_RPID_TIME_OFFSET << ">";
}

Key KEY_IDLE_THRESHOLD("idle-threshold");
Key KEY_LAST_INPUT("last-input");
bool XmlParser::parse(xmlNodePtr node, UserInput& output)
{
   if (!KEY_RPID_USER_INPUT.nameEq(node))
      return false;
   parseAttribId(node, output.id);
   Optional<cpc::string> thresholdStr;
   output.idleThreshold.present = parseOptionalAttrib(node, KEY_IDLE_THRESHOLD, thresholdStr);
   if (output.idleThreshold.present)
      output.idleThreshold.value = atoi(thresholdStr.value);
   
   parseOptionalAttrib(node, KEY_LAST_INPUT, output.lastInput);
   cpc::string text;
   xmlNodeText(node, text);
   const ActiveIdle* activeIdle = m_activeIdleMap.find(text.c_str());
   if (activeIdle)
      output.activeIdle = *activeIdle;
   else
   {
      error(node) << "Invalid active/idle value '" << (text) << "'";
      return false;
   }
   return true;
}

void XmlEncoder::encode(const UserInput& source)
{
   m_output << "<" << KEY_RPID_USER_INPUT;
   encodeAttribId(source.id);
   if(source.idleThreshold.present)
   {
      encodeAttrib(cpc::to_string(source.idleThreshold.value), KEY_IDLE_THRESHOLD);
   }
   encodeOptionalAttrib(source.lastInput, KEY_LAST_INPUT);
   m_output << ">";
   const std::string* activeIdle = m_activeIdleMap.findKey(source.activeIdle);
   if(activeIdle)
      m_output << activeIdle->c_str();
   else
      error(NULL) << "Invalid active/idle value for user-input";
   m_output << "</" << KEY_RPID_USER_INPUT << ">";
}

Key KEY_TIMESTAMP("dm","timestamp");
Key KEY_DEVICE_ID("dm","deviceID");
bool XmlParser::parse(xmlNodePtr node, Device& output)
{
   if (!KEY_DEVICE.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseInKey(child, KEY_TIMESTAMP, output.timestamp) ||
            parseInKey(child, KEY_DEVICE_ID, output.deviceID) ||
            parseOptional(child, output.userInput) ||
            parseAdd(child, output.notes)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'device' element" << std::endl;
         success = false;
      }
   }
   return success;

}

void XmlEncoder::encode(const Device& source)
{
   m_output << "<" << KEY_DEVICE;
   encodeAttribId(source.id);
   m_output << ">\n";
   encodeEach(source.notes);
   encodeOptional(source.userInput);
   encodeInKey(source.deviceID, KEY_DEVICE_ID);
   encodeInKey(source.timestamp, KEY_TIMESTAMP);
   m_output << "</" << KEY_DEVICE << ">";
}

Key KEY_CLASS(NS_RPID, "class");
bool XmlParser::parse(xmlNodePtr node, Person& output)
{
   if (!KEY_PERSON.nameEq(node))
      return false;
   // In Person element ID attribute is required, all others are NOT. 
   // However due to customer complains, we will discontinue this check in order to receive even not well formed Person elements by RFC 4479. 
   // bool success = parseAttribId(node, output.id);
   bool success = true;
   parseAttribId(node, output.id);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseInKey(child, KEY_TIMESTAMP, output.timestamp) ||
            parseOptional(child, output.activities) ||
            parseInKey(child, KEY_CLASS, output.classEnt) ||
            parseOptional(child, output.mood) ||
            parseOptional(child, output.placeIs) ||
            parseOptional(child, output.placeType) ||
            parseOptional(child, output.privacy) ||
            parseOptional(child, output.sphere) ||
            parseOptional(child, output.statusIcon) ||
            parseOptional(child, output.timeOffset) ||
            parseAdd(child, output.notes) ||
            parseOptional(child, output.userInput) ||
            parseOptional(child, output.status) ||
            parseOptional(child, output.other) ||
            parseOldActivities(child, output.activities)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'person' element" << std::endl;
      }
   }
   return success;
}

void XmlEncoder::encode(const Person& source)
{
   m_output << "<" << KEY_PERSON;
   encodeAttribId(source.id);
   m_output << ">\n";
   Notes notes = source.notes;
   for (Notes::iterator it = notes.begin(); it != notes.end(); ++it) it->ns = NS_DM;
   encodeEach(notes);
   encodeOptional(source.other);
   encodeOptional(source.activities);
   encodeInKey(source.classEnt, KEY_CLASS);
   encodeOptional(source.mood);
   encodeOptional(source.placeIs);
   encodeOptional(source.placeType);
   encodeOptional(source.privacy);
   encodeOptional(source.sphere);
   encodeOptional(source.statusIcon);
   encodeOptional(source.timeOffset);
   encodeInKey(source.timestamp, KEY_TIMESTAMP);
   encodeOptional(source.userInput);
   m_output << "</" << KEY_PERSON << ">";
}

Key KEY_PRIORITY("priority");
bool XmlParser::parse(xmlNodePtr node, Contact& output)
{
   if (!KEY_CONTACT.nameEq(node))
      return false;
   cpc::string priorityStr;
   output.priority.present = parseAttrib(node, KEY_PRIORITY, priorityStr);
   if (output.priority.present)
      output.priority.value = (float)atof(priorityStr);
   xmlNodeText(node, output.contact);
   return true;
}

void XmlEncoder::encode(const Contact& source)
{
   m_output << "<" << KEY_CONTACT;
   if (source.priority.present)
      encodeAttrib(cpc::to_string(source.priority.value), KEY_PRIORITY);
   m_output << ">";
   encodeString(source.contact);
   m_output << "</" << KEY_CONTACT << ">";
}

bool XmlParser::parse(xmlNodePtr node, Status& output)
{
   if (!KEY_STATUS.nameEq(node))
      return false;
   bool success = true;
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (KEY_BASIC.nameEq(child))
      {
         cpc::string body;
         xmlNodeText(child, body);
         const BasicStatus* status = m_basicStatusMap.find(body.c_str());
         if(status)
         {
            output.basic.present = true;
            output.basic.value = *status;
         }
         else
         {
            error(child) << "Invalid basic status value '" << (body) << "'" << std::endl;
            success = false;
         }
      }
      else
      {
         error(child) << "Unrecognized child of 'status' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Status& source)
{
   m_output << "<" << KEY_STATUS << ">\n";
   if (source.basic.present)
   {
      m_output << "<" << KEY_BASIC << ">";
      const std::string* str = m_basicStatusMap.findKey(source.basic.value);
      if (str)
         m_output << str->c_str();
      else
         error(NULL) << "Invalid value of status.basic '" << source.basic.value << "'" << std::endl;
      m_output << "</" << KEY_BASIC << ">\n";
   }
   m_output << "</" << KEY_STATUS << ">";
}

bool XmlParser::parse(xmlNodePtr node, Tuple& output)
{
   if (!KEY_TUPLE.nameEq(node))
      return false;
   bool success = true;
   parseAttribId(node, output.id);
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseInKey(child, KEY_TIMESTAMP, output.timestamp) ||
            parse(child, output.status) ||
            parseOptional(child, output.contact) ||
            parseInKey(child, KEY_DEVICE_ID, output.deviceID) ||
            parseOptional(child, output.relationship) ||
            parseOptional(child, output.serviceClass) ||
            parseAdd(child, output.notes) ||
            parseOptional(child, output.userInput) ||
            parseInKey(child, KEY_CLASS, output.classEnt)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'tuple' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Tuple& source)
{
   m_output << "<" << KEY_TUPLE;
   encodeAttribId(source.id);
   m_output << ">\n";
   encodeEach(source.notes);
   encode(source.status);
   m_output << "\n";
   encodeInKey(source.deviceID, KEY_DEVICE_ID);
   encodeOptional(source.relationship);
   encodeOptional(source.serviceClass);
   encodeOptional(source.contact);
   encodeInKey(source.timestamp, KEY_TIMESTAMP);
   encodeOptional(source.userInput);
   m_output << "</" << KEY_TUPLE << ">";
}

Key KEY_ENTITY("entity");
bool XmlParser::parse(xmlNodePtr node, Presence& output)
{
   if (!node)
      throw "Invalid argument, node is NULL";
   if (!KEY_PRESENCE.nameEq(node))
      return false;
   bool success = true;
   if (!parseAttrib(node, KEY_ENTITY, output.entity))
   {
      success = false;
      error(node) << "Missing required 'entity' attribute on presence element" << std::endl;
   }
   for(xmlNodePtr child=node->children; child; child=child->next)
   {
      if (child->type == XML_TEXT_NODE) {} // ignored, this is probably just \n
      else if (parseAdd(child, output.notes) ||
            parseAdd(child, output.devices) ||
            parseAdd(child, output.tuples) ||
            parseAdd(child, output.persons)) {} // node handled ok
      else
      {
         error(child) << "Unrecognized child of 'presence' element" << std::endl;
         success = false;
      }
   }
   return success;
}

void XmlEncoder::encode(const Presence& source)
{
   m_output << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
   m_output << "<" << KEY_PRESENCE;
   m_output << " xmlns=\"urn:ietf:params:xml:ns:pidf\"\nxmlns:dm=\"urn:ietf:params:xml:ns:pidf:data-model\"\nxmlns:rpid=\"urn:ietf:params:xml:ns:pidf:rpid\"\n";
   encodeAttrib(source.entity, KEY_ENTITY);
   m_output << ">\n";
   encodeEach(source.notes);
   encodeEach(source.tuples);
   encodeEach(source.devices);
   encodeEach(source.persons);
   m_output << "</" << KEY_PRESENCE << ">";
}

Key KEY_OTHER("rpid", "other");
bool XmlParser::parse(xmlNodePtr node, Other& output)
{
   if (!KEY_OTHER.nameEq(node))
      return false;
   return xmlNodeText(node, output);
}

void XmlEncoder::encode(const Other& other)
{
   encodeInKey(other, KEY_OTHER, false);
}
   
std::string XmlEncoder::xmlEscapeString(const std::string& stringToEscape)
{
   std::string escapedString(stringToEscape);
   
   boost::replace_all(escapedString, "&", "&amp;");
   boost::replace_all(escapedString, "\"", "&quot;");
   boost::replace_all(escapedString, "'", "&apos;");
   boost::replace_all(escapedString, "<", "&lt;");
   boost::replace_all(escapedString, ">", "&gt;");
   
   return escapedString;
}

}

}
