#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_XML_H)
#define CPCAPI2_SIP_PRESENCE_XML_H

#include <vector>
#include <map>
#include <libxml/parser.h>
#include "presence/SipPresenceModel.h"
#include "cpcapi2utils.h"

namespace CPCAPI2
{
namespace SipPresence
{

/* Useful utilities for parsing/encoding xml */

/** Represents an xml key to be read or written
  * Supports the << operator and correctly handles empty namespace */
struct CPCAPI2_SHAREDLIBRARY_API XmlKey
{
   XmlKey(const char* name): ns(NULL), name(name) {}
   XmlKey(const char* ns, const char* name): ns(ns), name(name) {}
   const char* ns;
   const char* name;
   bool nameEq(const xmlNodePtr node) const;
   std::string str() const;
};
std::ostream& operator<<(std::ostream& out, const XmlKey& key);

/** Simple wrapper for parsing an xml string to a dom and cleaning up afterwards */
class CPCAPI2_SHAREDLIBRARY_API XmlRoot
{
public:
   XmlRoot(const std::string xml);
   XmlRoot(const char* xml, unsigned int len);
   virtual ~XmlRoot();
   xmlNodePtr root();
private:
      void init(const char* xml, unsigned int len);
      xmlDocPtr m_doc;
};


/** Defines a mapping from strings to enum types for use in parsing and encoding xml */

/* unfortunately the template functions need to be defined here in the header
 * see: http://stackoverflow.com/a/456716 */
template<typename T>
class CPCAPI2_SHAREDLIBRARY_API EnumMap
{
private:
   typedef std::map<std::string, T> MapType;
   MapType m_map;
public:
   void add(const std::string& str, const T& obj)
   {
      m_map[str] = obj;
   }

   const T* find(const std::string& str)
   {
       typename MapType::const_iterator it = m_map.find(str);
       if (it != m_map.end())
       {
         return &it->second;
       }
       return NULL;
   }

   const std::string* findKey(const T& obj)
   {
      for(typename MapType::const_iterator it=m_map.begin(); it!=m_map.end(); it++)
      {
         if (it->second == obj)
         {
            return &it->first;
         }
      }
      return NULL;
   }
};
/** Read an xml attribute from a node into a string
  * This is in this function to ensure cleanup is done correctly, since libxml allocates memory 
  * which must be freed each time you access a property */
bool xmlNodeProperty(xmlNodePtr node, const char* propKey, cpc::string& output);

/** Read the xml text content of the given node into a string (requires finding the child of xml_text_node type) */
bool xmlNodeText(xmlNodePtr node, cpc::string& output);

}
}
#endif // CPCAPI2_SIP_PRESENCE_XML_H
