#if !defined(CPCPAPI2_SIPPRESENCECONFIGURATIONMANAGERINTERFACE_H)
#define CPCPAPI2_SIPPRESENCECONFIGURATIONMANAGERINTERFACE_H

#include "cpcapi2defs.h"
#include "../phone/PhoneInterface.h"
#include "sippresenceconfiguration/SipPresenceConfigurationManager.h"
#include "SipPresenceManagerInterface.h"

namespace CPCAPI2
{
   class Phone;

   namespace SipPresence
   {

   class SipPresenceConfigurationManagerInterface : public PhoneModule,
                                                    public SipPresenceConfigurationManager
   {
   public:
      SipPresenceConfigurationManagerInterface(Phone* phone);
      virtual ~SipPresenceConfigurationManagerInterface() {}

      virtual void setSipPresenceModel(const SipPresenceModelType model) OVERRIDE;
      virtual SipPresenceModelType getSipPresenceModel() const OVERRIDE;

   private:
      virtual void Release() OVERRIDE;

      PhoneInterface* mPhone;
      SipPresenceModelType mModelType;
   };

   } // namespace SipPresence
} // namespace CPCAPI2

#endif
