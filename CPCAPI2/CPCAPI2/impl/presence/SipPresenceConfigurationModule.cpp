#include "brand_branded.h"

#include "interface/experimental/sippresenceconfiguration/SipPresenceConfigurationManager.h"

#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
#include "SipPresenceConfigurationManagerInterface.h"
#include "impl/phone/PhoneInterface.h"
#endif

namespace CPCAPI2
{
namespace SipPresence
{
SipPresenceConfigurationManager* SipPresenceConfigurationManager::getInterface(Phone* cpcPhone)
{
#if (CPCAPI2_BRAND_SIP_EVENT_MODULE == 1)
   PhoneInterface* pi = dynamic_cast<PhoneInterface*>(cpcPhone);
   SipEvent::SipEventManager::getInterface(cpcPhone); // register dependency
   SipPresenceConfigurationManager* eventManager = dynamic_cast<SipPresenceConfigurationManager*>(pi->getInterfaceByName("SipPresenceConfigurationManagerInterface"));
   if (eventManager == NULL)
   {
      SipPresenceConfigurationManagerInterface* m_if = new SipPresenceConfigurationManagerInterface(cpcPhone);
      pi->registerInterface("SipPresenceConfigurationManagerInterface", m_if);
      eventManager = m_if;
   }
   return eventManager;
#else
   return NULL;
#endif
}
}
}
