#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_CONVERTER_H)
#define CPCAPI2_SIP_PRESENCE_CONVERTER_H

#include "presence/SipPresenceModel.h"
#include "presence/SipPresenceManager.h"
#include "sippresenceconfiguration/SipPresenceConfigurationManager.h"

namespace CPCAPI2
{
namespace SipPresence
{
class SipPresenceConverter
{
public:
   static void prepareCannedStatus(CannedStatus canned, Presence& presence, SipPresenceModelType model = DEFAULT_PRESENCE_MODEL);
   static CannedStatus parseCannedStatus(const Presence& presence, SipPresenceModelType model = DEFAULT_PRESENCE_MODEL);
   static void updateStatusParameters(Presence& presence, const StatusUpdateParameters& params);

private:
   static void prepareCannedStatusGBModel(CannedStatus canned, Presence& presence);
   static CannedStatus parseCannedStatusGBModel(const Presence& presence);
};
}
}
#endif // CPCAPI2_SIP_PRESENCE_CONVERTER_H
