#pragma once

#if !defined(CPCAPI2_SIP_PRESENCE_MANAGER_INTERNAL_H)
#define CPCAPI2_SIP_PRESENCE_MANAGER_INTERNAL_H

#include <presence/SipPresenceManager.h>

namespace CPCAPI2
{

   namespace SipPresence
   {

      class CPCAPI2_SHAREDLIBRARY_API SipPresenceManagerInternal
      {
      public:
         virtual SipEventSubscriptionHandle createSubscription(CPCAPI2::SipAccount::SipAccountHandle account, SipEventSubscriptionHandle h) = 0;
      };

   }
}
#endif // CPCAPI2_SIP_PRESENCE_MANAGER_INTERNAL_H