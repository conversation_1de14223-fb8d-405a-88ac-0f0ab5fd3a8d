﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{A47BD7EC-163E-4B3E-BDE0-BCA937D9E226}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{1C2DF66D-E078-454C-9632-4BC8F8B1B393}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{276270AE-CCDB-4022-9394-60318D6EA313}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\rutil">
      <UniqueIdentifier>{AE25AFE8-F278-4AC4-B20F-46D2204A7326}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Cpcapi2Runner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CPCAPI2\interface\public\cpcapi2.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CpcRcWebsocketServer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CpcRcWebsocketServerObserver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Cpcapi2Runner.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ThreadIf.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Condition.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Data.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Lock.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Mutex.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\BaseException.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\DataStream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ParseBuffer.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Log.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\MD5Stream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ParseException.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Subsystem.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\vmd5.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\SysLogStream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\SysLogBuf.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="cpcapi2_rc_server.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CpcRcWebsocketServer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>