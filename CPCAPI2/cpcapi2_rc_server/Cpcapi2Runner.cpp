#include "stdafx.h"
#include "Cpcapi2Runner.h"

#include "util/cpc_logger.h"
#include "brand_branded.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::RemoteControl;

namespace cp
{
namespace rcserver
{
Cpcapi2Runner::Cpcapi2Runner() 
   : mPhone(NULL), 
     mAccount(NULL), 
     mConversation(NULL), 
     mMedia(NULL), 
     mVidSourceId(0)
{
}

Cpcapi2Runner::~Cpcapi2Runner()
{
}

void Cpcapi2Runner::run()
{
   ThreadIf::run();
}

void Cpcapi2Runner::shutdown()
{
   if (mPhone != NULL)
   {
      mPhone->interruptProcess();
   }
   ThreadIf::shutdown();
}

void Cpcapi2Runner::thread()
{
   mPhone = dynamic_cast<PhoneInternal*>(CPCAPI2::Phone::create());
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
   licenseInfo.licenseAor = "";
   mPhone->initialize(licenseInfo, this);
   mPhone->setLoggingEnabled("cpcsfu", true);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy phone instance initialized.");

   mAccount = SipAccountManager::getInterface(mPhone);
   initAccountSettings();
   mAccountHandle = mAccount->create();
   mAccount->configureDefaultAccountSettings(mAccountHandle, mAccountSettings);
   mAccount->applySettings(mAccountHandle);
   mAccount->setHandler(mAccountHandle, this);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy account initialized");

   mConversation = SipConversationManager::getInterface(mPhone);
   SipConversationSettings convSettings;
   convSettings.natTraversalMode = NatTraversalMode_None;
   convSettings.natTraversalServerSource = NatTraversalServerSource_SRV;
   
   mConversation->setDefaultSettings(mAccountHandle, convSettings);
   mConversation->setHandler(mAccountHandle, this);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy conversation module initialized");

   mMedia = MediaManager::getInterface(mPhone);
   mMedia->initializeMediaStack();
   
   Video::getInterface(mMedia)->setHandler(this);
   Video::getInterface(mMedia)->setCaptureDevice(CPCAPI2::Media::kScreenCaptureDeviceId);
   Video::getInterface(mMedia)->startCapture();
   Video::getInterface(mMedia)->queryCodecList();

   Audio::getInterface(mMedia)->setCaptureDevice(CPCAPI2::Media::kAudioLoopbackDeviceId, AudioDeviceRole_Headset);

   mRCServer = RemoteControlServer::getInterface(mPhone);
   mRCServer->start();

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy media module initialized");

   mAccount->enable(mAccountHandle);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy account enabled");

   mWebStreamer.StartServer(mPhone, this);

   while (!isShutdown())
   {
      mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      mAccount->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      mMedia->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      mRCServer->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
      resip::sleepMilliseconds(20);
   }

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy shutting down!");

   mRCServer->shutdown();
   mAccount->disable(mAccountHandle, true);
   mAccount->process(5000);
   mAccount->process(10000);
   Video::getInterface(mMedia)->stopCapture();
   mPhone->process(100);

   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy is shutdown!");
}

void Cpcapi2Runner::initAccountSettings()
{
   mAccountSettings.username = "1297"; //"cpcsfu_" + mPhone->getInstanceId().substr(0, 4);
   mAccountSettings.password = "3G96nK9A2HhJshtM";
   mAccountSettings.domain = /*"cpsipv6.counterpath.net";*/ /*"opsip.silverstar.counterpath.net";*/ "counterpath.com"; 
   mAccountSettings.useOutbound = false;
   mAccountSettings.outboundProxy = "sbc.counterpath.com:15060";
   mAccountSettings.sipTransportType = SipAccountTransport_TCP;
   mAccountSettings.useRegistrar = true;
   mAccountSettings.useRport = false; //true;
   mAccountSettings.registrationIntervalSeconds = 45;
   mAccountSettings.ipVersion = CPCAPI2::SipAccount::IpVersion_V4;
   mAccountSettings.userAgent = "CPCSFU";
   std::cout << "CPCSFU account name: " << mAccountSettings.username << "@" << mAccountSettings.domain << std::endl;
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   std::cout << "onAccountStatusChanged: " << account << std::endl;
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// SipConversationHandler
int Cpcapi2Runner::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   std::cout << "Cpcapi2Runner::onNewConversation: " << conversation << " from " << args.remoteAddress << std::endl;
   //InfoLog(<< "Cpcapi2Runner::onNewConversation: " << conversation << " from " << args.remoteAddress);
   CallInfo callInfo;
   callInfo.address = args.remoteAddress;
   callInfo.displayName = args.remoteDisplayName;
   if(mCalls.size() == 0)
   {   
      callInfo.isPresenter = true;
   }
   else
   {
      callInfo.isPresenter = false;
   }
   mCalls[conversation] = callInfo;

   mConversation->setMediaEnabled(conversation, SipConversation::MediaType_Audio, true);

   SipConversation::MediaInfo mediaDescVideo;
   mediaDescVideo.mediaType = SipConversation::MediaType_Video;
   mediaDescVideo.mediaDirection = MediaDirection_ReceiveOnly;
   mConversation->configureMedia(conversation, mediaDescVideo);

   mConversation->accept(conversation);
   
   //InfoLog(<< "Cpcapi2Runner::onNewConversation: Conversation from " << args.remoteAddress << "accepted");

   return 0;
}

int Cpcapi2Runner::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   //InfoLog(<< "Cpcapi2Runner::onConversationEnded: Conversation " << conversation << "ended");
   std::cout << "Cpcapi2Runner::onConversationEnded: Conversation " << conversation << " ended" << std::endl;
   std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);
   mCalls.erase(callsIt);
   mConversation->refreshConversationStatistics(conversation, true, true, true);
   return 0;
}

int Cpcapi2Runner::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   mConversation->accept(conversation);
   return 0;
}

int Cpcapi2Runner::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
   std::cout << "=========== CONVERSATION STATISTICS =============" << std::endl;
   CPCAPI2::SipConversation::ConversationStatistics conversationStatistics = args.conversationStatistics;
   if (conversationStatistics.audioChannels.size() > 0)
   {
      std::cout << "---------- LOCAL ----------" << std::endl
         << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
         << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
         << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
         << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
         << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
         << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port << std::endl;
   }
   if (conversationStatistics.videoChannels.size() > 0)
   {
      std::cout << "---------- LOCAL (video) ----------" << std::endl
         << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
         << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
         << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
         << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
         << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
         << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
         << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port << std::endl;
   }
   if (conversationStatistics.remoteAudioChannels.size() > 0)
   {
      std::cout << "---------- REMOTE ----------" << std::endl
         << "cumulativeLost:      " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
         << "remoteEndpoint:      " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port << std::endl;
   }
   if (conversationStatistics.remoteVideoChannels.size() > 0)
   {
      std::cout << "---------- REMOTE (video) ----------" << std::endl
         << "cumulativeLost:      " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
         << "fractionLost:        " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
         << "jitterSamples:       " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
         << "rttMs:               " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
         << "remoteEndpoint:      " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port << std::endl;
   }
   std::cout << "======> Call Quality: " << conversationStatistics.callQuality << std::endl;

   return 0;
}

int Cpcapi2Runner::onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// VideoHandler
int Cpcapi2Runner::onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::VideoCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName,"H.264") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, true);
         CPCAPI2::Media::H264Config h264config;
         h264config.enableNonInterleavedMode = false;
         Video::getInterface(mMedia)->setCodecConfig(h264config);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_MaxSupported);
      }
      else
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// CpcWebStreamerObserver
void Cpcapi2Runner::onWebSocketOpen()
{
}

void Cpcapi2Runner::onWebSocketMessage(const std::string& data)
{
   mRCServer->processRemoteRequest(cpc::string(data.c_str(), data.size()));
}

}
}
