ROOT_PATH = ../..
RESIP_PATH = $(ROOT_PATH)/shared/sipfoundry/main
RESIP_STACK_PATH = $(RESIP_PATH)/resip/stack
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DUSE_SSL -DRESIP_USE_STL_STREAMS -DUSE_DTLS -DUSE_IPV6 -DUSE_DNS_VIP
INCLUDE_DIRS = -I$(RESIP_STACK_PATH) -I$(RESIP_STACK_PATH)/ssl -I $(RESIP_PATH) -I $(RESIP_PATH)/resip -I$(ROOT_PATH)/external/OpenSSL/openssl-current/include
CXXFLAGS = -frtti -fexceptions $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
RESIP_STACK_SRCS = \
   Aor.cxx ApiCheck.cxx ApplicationSip.cxx Auth.cxx BasicNonceHelper.cxx BranchParameter.cxx CallId.cxx Compression.cxx  \
   Connection.cxx ConnectionBase.cxx ConnectionManager.cxx Contents.cxx ContentsFactoryBase.cxx Cookie.cxx CpimContents.cxx  \
   CSeqCategory.cxx DataParameter.cxx DateCategory.cxx DeprecatedDialog.cxx DnsInterface.cxx DnsResult.cxx DtlsMessage.cxx  \
   Embedded.cxx EventStackThread.cxx ExistsOrDataParameter.cxx ExistsParameter.cxx ExpiresCategory.cxx ExtensionHeader.cxx  \
   ExtensionParameter.cxx ExternalBodyContents.cxx FloatParameter.cxx GenericContents.cxx GenericUri.cxx  \
   HeaderFieldValue.cxx HeaderFieldValueList.cxx HeaderHash.cxx Headers.cxx HeaderTypes.cxx Helper.cxx IntegerCategory.cxx  \
   IntegerParameter.cxx InternalTransport.cxx InteropHelper.cxx InterruptableStackThread.cxx InvalidContents.cxx  \
   KeepAliveMessage.cxx LazyParser.cxx makeCert.cxx Message.cxx MessageFilterRule.cxx MessageWaitingContents.cxx  \
   MethodHash.cxx MethodTypes.cxx Mime.cxx MsgHeaderScanner.cxx MultipartAlternativeContents.cxx MultipartMixedContents.cxx  \
   MultipartRelatedContents.cxx MultipartSignedContents.cxx NameAddr.cxx NonceHelper.cxx OctetContents.cxx Parameter.cxx  \
   ParameterHash.cxx ParameterTypes.cxx ParserCategories.cxx ParserCategory.cxx ParserContainerBase.cxx Pidf.cxx  \
   Pkcs7Contents.cxx Pkcs8Contents.cxx PlainContents.cxx PrivacyCategory.cxx QuotedDataParameter.cxx QValue.cxx  \
   QValueParameter.cxx RAckCategory.cxx RequestLine.cxx Rlmi.cxx RportParameter.cxx SdpContents.cxx SecurityAttributes.cxx  \
   SERNonceHelper.cxx SipFrag.cxx SipMessage.cxx SipStack.cxx StackThread.cxx StatelessHandler.cxx StatisticsHandler.cxx  \
   StatisticsManager.cxx StatisticsMessage.cxx StatusLine.cxx StringCategory.cxx Symbols.cxx TcpBaseTransport.cxx  \
   TcpConnection.cxx TcpTransport.cxx TimeAccumulate.cxx TimerMessage.cxx TimerQueue.cxx Token.cxx TransactionController.cxx  \
   TransactionMap.cxx TransactionState.cxx TransactionUser.cxx TransactionUserMessage.cxx Transport.cxx TransportFailure.cxx  \
   TransportSelector.cxx TransportThread.cxx TuIM.cxx Tuple.cxx TupleMarkManager.cxx TuSelector.cxx UdpTransport.cxx  \
   UInt32Category.cxx UInt32Parameter.cxx UnknownParameter.cxx Uri.cxx Via.cxx WarningCategory.cxx WsBaseTransport.cxx \
   WsConnection.cxx WsConnectionBase.cxx WsCookieContext.cxx WsDecorator.cxx  \
   WsFrameExtractor.cxx WsTransport.cxx X509Contents.cxx ssl/DtlsTransport.cxx ssl/Security.cxx ssl/counterpath/LinuxSecurity.cxx \
   ssl/TlsBaseTransport.cxx ssl/TlsConnection.cxx ssl/TlsTransport.cxx ssl/WssConnection.cxx ssl/WssTransport.cxx
SRCS = $(RESIP_STACK_SRCS:%=$(RESIP_STACK_PATH)/%)
OBJS = $(SRCS:%.cxx=%.o)
TARGET = libresip_stack.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@
