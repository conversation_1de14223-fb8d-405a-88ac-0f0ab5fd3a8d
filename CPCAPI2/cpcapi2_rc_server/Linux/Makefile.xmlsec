ROOT_PATH = ../..
XMLSEC_PATH = $(ROOT_PATH)/external/xmlsec/xmlsec/xmlsec1-1.2.20
LIBXML_PATH = $(ROOT_PATH)/external/libxml
OPENSSL_PATH = $(ROOT_PATH)/external/OpenSSL/openssl-current
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS =
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DXMLSEC_NO_CRYPTO_DYNAMIC_LOADINGXML -DXMLSEC_NO_TMPL_TEST -DPACKAGE="\"xmlsec\"" -DHAVE_STDIO_H -DHAVE_STDLIB_H -DHAVE_STRING_H -DHAVE_CTYPE_H -DHAVE_MALLOC_H -DHAVE_MEMORY_H -DXMLSEC_NO_XSLT -DXMLSEC_CRYPTO_OPENSSL -DXMLSEC_CRYPTO="openssl" -DLTDL_OBJDIR="" -DXMLSEC_STATIC -DLIBXML_STATIC -DXMLSEC_NO_AES -DXMLSEC_NO_DES
LDFLAGS = -L.
INCLUDE_DIRS = -I$(LIBXML_PATH)/include -I$(XMLSEC_PATH)/include -I$(OPENSSL_PATH)/include
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
XMLSEC_SRCS = \
 app.c base64.c bn.c buffer.c c14n.c dl.c enveloped.c errors.c io.c keyinfo.c keys.c keysdata.c keysmngr.c list.c membuf.c nodeset.c parser.c soap.c \
 strings.c templates.c transforms.c x509.c xkms.c xmldsig.c xmlenc.c xmlsec.c xmltree.c xpath.c xslt.c \
 openssl/app.c openssl/bn.c openssl/ciphers.c openssl/crypto.c openssl/digests.c openssl/evp.c openssl/hmac.c openssl/kt_rsa.c openssl/kw_aes.c \
 openssl/kw_des.c openssl/signatures.c openssl/symkeys.c openssl/x509.c openssl/x509vfy.c
SRCS = $(XMLSEC_SRCS:%=$(XMLSEC_PATH)/src/%)
OBJS = $(SRCS:%.c=%.o)
HEADERS = $(SRCS:%.c=%.h)
TARGET = libxmlsec.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
