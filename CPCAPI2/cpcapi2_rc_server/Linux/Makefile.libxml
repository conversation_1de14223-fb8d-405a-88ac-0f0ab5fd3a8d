ROOT_PATH = ../..
LIBXML_PATH = $(ROOT_PATH)/external/libxml
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = 
LDFLAGS = -L.
INCLUDE_DIRS = -I$(LIBXML_PATH) -I$(LIBXML_PATH)/include
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
LIBXML_SRCS = \
 SAX.c entities.c encoding.c error.c parserInternals.c  \
               parser.c tree.c hash.c list.c xmlIO.c xmlmemory.c uri.c  \
                valid.c xlink.c HTMLparser.c HTMLtree.c debugXML.c xpath.c  \
                xpointer.c xinclude.c nanohttp.c nanoftp.c DOCBparser.c \
                catalog.c globals.c threads.c c14n.c xmlstring.c \
                xmlregexp.c xmlschemas.c xmlschemastypes.c xmlunicode.c \
                xmlreader.c relaxng.c dict.c SAX2.c \
                xmlwriter.c legacy.c chvalid.c pattern.c xmlsave.c 
SRCS = $(LIBXML_SRCS:%=$(LIBXML_PATH)/%)
OBJS = $(SRCS:%.c=%.o)
HEADERS = $(SRCS:%.c=%.h)
TARGET = libxml.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
