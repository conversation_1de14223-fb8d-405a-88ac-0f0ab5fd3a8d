#include "stdafx.h"
#include "CpcRcWebsocketServer.h"
#include "CpcRcWebsocketServerObserver.h"

#include <thread>
#include <future>
#include <iostream>
#include <fstream>

using namespace CPCAPI2::Media;

namespace cp
{
namespace rcserver
{
CpcRcWebsocketServer::CpcRcWebsocketServer()
   : mPhone(NULL), mObs(NULL)
{
}

CpcRcWebsocketServer::~CpcRcWebsocketServer()
{
}

void CpcRcWebsocketServer::StartServer(CPCAPI2::Phone* phone, CpcRcWebsocketServerObserver* obs)
{
   mPhone = phone;
   mObs = obs;
   auto sdkThread = std::async(std::launch::async, [&]() {
      try {
         // Set logging settings
         mWebSockServer.set_access_channels(websocketpp::log::alevel::all);
         mWebSockServer.clear_access_channels(websocketpp::log::alevel::frame_payload);

         // Initialize Asio
         mWebSockServer.init_asio();

         // Register our message handler
         mWebSockServer.set_open_handler(std::bind(&CpcRcWebsocketServer::on_open, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_close_handler(std::bind(&CpcRcWebsocketServer::on_close, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_message_handler(std::bind(&CpcRcWebsocketServer::on_message, this, &mWebSockServer, std::placeholders::_1, std::placeholders::_2));

         // Listen on port 9002
         mWebSockServer.listen(9003);

         // Start the server accept loop
         mWebSockServer.start_accept();

         // Start the ASIO io_service run loop
         mWebSockServer.run();
      }
      catch (websocketpp::exception const & e) {
         std::cout << e.what() << std::endl;
      }
      catch (...) {
         std::cout << "other exception" << std::endl;
      }
   });
}

void CpcRcWebsocketServer::StopServer()
{
}

void CpcRcWebsocketServer::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
   mObs->onWebSocketMessage(msg->get_payload());
}

void CpcRcWebsocketServer::on_open(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "accepted a websocket connection" << std::endl;
   mObs->onWebSocketOpen();
   mConnections[hdl] = std::shared_ptr<ConnInfo>(new ConnInfo(hdl));
}

void CpcRcWebsocketServer::on_close(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection closed" << std::endl;
   mConnections.erase(hdl);
}



}
}
