#pragma once

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <iostream>
#include <set>

namespace cp
{
namespace rcserver
{
class CpcRcWebsocketServerObserver;

class CpcRcWebsocketServer
{
public:
   CpcRcWebsocketServer();
   virtual ~CpcRcWebsocketServer();

   void StartServer(CPCAPI2::Phone* phone, CpcRcWebsocketServerObserver* obs);
   void StopServer();

private:
   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::message_ptr message_ptr;

   struct ConnInfo
   {
      ConnInfo(websocketpp::connection_hdl h) : conn(h) {}
      websocketpp::connection_hdl conn;
   };
   typedef std::map<websocketpp::connection_hdl, std::shared_ptr<ConnInfo>, std::owner_less<websocketpp::connection_hdl> > con_list;

   void on_open(server* s, websocketpp::connection_hdl hdl);
   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);

private:
   CPCAPI2::Phone* mPhone;
   server mWebSockServer;
   con_list mConnections;
   CpcRcWebsocketServerObserver* mObs;
};
}
}
