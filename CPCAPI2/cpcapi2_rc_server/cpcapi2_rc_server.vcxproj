﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D21DC4F5-834A-49B1-9698-D0E40F7F7215}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>cpcapi2_rtp_proxy</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v120</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;_LIB;_WIN32_WINNT=0x0600;USE_IPV6;USE_ARES;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;BOOST_ALL_NO_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\CPCAPI2\interface\public;..\CPCAPI2\interface\experimental;..\shared\sipfoundry\main;..\CPCAPI2\impl;..\CPCAPI2;..\external\websocketpp;..\external\boost\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>CPCAPI2_SharedLibrary_Debug.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\build;..\lib</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>if exist  "$(ProjectDir)..\build\" xcopy "$(ProjectDir)..\build\*.*" "$(OutDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;_LIB;_WIN32_WINNT=0x0600;USE_IPV6;USE_CARES;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;BOOST_ALL_NO_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\CPCAPI2\interface\public;..\CPCAPI2\interface\experimental;..\shared\sipfoundry\main;..\external\websocketpp;..\external\boost\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PrecompiledHeaderFile />
      <PrecompiledHeaderOutputFile />
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>CPCAPI2_SharedLibrary.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>..\build;..\lib</AdditionalLibraryDirectories>
    </Link>
    <PostBuildEvent>
      <Command>if exist  "$(ProjectDir)..\build\" xcopy "$(ProjectDir)..\build\*.*" "$(OutDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\CPCAPI2\interface\public\cpcapi2.h" />
    <ClInclude Include="Cpcapi2Runner.h" />
    <ClInclude Include="CpcRcWebsocketServer.h" />
    <ClInclude Include="CpcRcWebsocketServerObserver.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\shared\sipfoundry\main\rutil\BaseException.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Condition.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Data.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\DataStream.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Lock.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Log.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\MD5Stream.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Mutex.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ParseBuffer.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ParseException.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\Subsystem.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\SysLogBuf.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\SysLogStream.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\ThreadIf.cxx" />
    <ClCompile Include="..\shared\sipfoundry\main\rutil\vmd5.cxx" />
    <ClCompile Include="Cpcapi2Runner.cpp" />
    <ClCompile Include="cpcapi2_rc_server.cpp" />
    <ClCompile Include="CpcRcWebsocketServer.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
