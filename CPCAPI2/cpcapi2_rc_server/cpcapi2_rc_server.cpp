#include "stdafx.h"

#ifdef __linux__
#include <sys/types.h>
#include <sys/stat.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <errno.h>
#include <unistd.h>
#include <syslog.h>
#include <string.h>
#endif

#include "Cpcapi2Runner.h"

using namespace cp::rcserver;

#if _WIN32
int _tmain(int argc, _TCHAR* argv[])
#else
int main(int argc, char * argv[])
#endif
{
#if 0 //def __linux__
   /* Our process ID and Session ID */
   pid_t pid, sid;

   /* Fork off the parent process */
   pid = fork();
   if (pid < 0)
   {
      exit(EXIT_FAILURE);
   }

   /* If we got a good PID, then we can exit the parent process. */
   if (pid > 0)
   {
      exit(EXIT_SUCCESS);
   }

   /* Change the file mode mask */
   umask(0);

   /* Open any logs here */
   openlog("cpcapi2_log", LOG_PID | LOG_CONS, LOG_USER);

   /* Create a new SID for the child process */
   sid = setsid();
   if (sid < 0)
   {
      /* Log the failure */
      system("echo SID creation error.");
      exit(EXIT_FAILURE);
   }

   /* Close out the standard file descriptors */
   close(STDIN_FILENO);
   close(STDOUT_FILENO);
   close(STDERR_FILENO);

   /* Daemon-specific initialization goes here */

   /* The Big Loop. Run CPCAPI2 RTP proxy tasks here */
   syslog(LOG_INFO, "Start a sdkrunner.");
#endif
   Cpcapi2Runner sdkrunner;
   sdkrunner.run();

   std::string answer;
   std::cerr << "Please press <return> key to continue ..." << std::flush;
   std::getline(std::cin, answer);

   std::cout << "exiting" << std::endl;

   sdkrunner.shutdown();
   sdkrunner.waitForShutdown(10000);

#if 0 //def __linux__
   closelog();
   exit(EXIT_SUCCESS);
#endif

	return 0;
}

