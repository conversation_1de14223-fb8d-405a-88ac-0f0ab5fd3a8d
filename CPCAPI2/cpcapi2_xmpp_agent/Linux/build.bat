if not exist "build" mkdir build

docker build -t cpcapi2_builder:ubuntu.clang -f ..\..\projects\docker_build\Dockerfile.ubuntu.clang ..\..\projects\docker_build
docker build -t cpcapi2_xmpp_agent_builder:ubuntu.clang -f Dockerfile.builder.ubuntu.clang .
docker run --name cpcapi2_xmpp_agent_builder --rm --mount type=bind,source="%cd%"\..\..\..\,target=/CPCAPI2 --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_xmpp_agent_builder:ubuntu.clang "%*"
