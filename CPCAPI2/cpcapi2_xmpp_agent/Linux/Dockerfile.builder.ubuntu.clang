FROM cpcapi2_builder:ubuntu.clang

WORKDIR /CPCAPI2/core/cpcapi2_xmpp_agent/Linux/build

# Run app.py when the container launches
#ENTRYPOINT ["sh", "-c", "BUILD_DIR=/CPCAPI2/core/cpcapi2_xmpp_agent/Linux/build && mkdir -p $BUILD_DIR && cd $BUILD_DIR && cmake ../../ && time make -j`grep -c ^processor \"/proc/cpuinfo\"` $@", "--"]

ENTRYPOINT ["/bin/bash", "-c", "python3 ../../../tools/conan_detect.py && cmake -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo ../../ && time ninja  $@", "--"]
