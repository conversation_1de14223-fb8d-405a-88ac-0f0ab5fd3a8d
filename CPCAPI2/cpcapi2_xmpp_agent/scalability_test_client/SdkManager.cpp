#include "SdkManager.h"

#include <chrono>
#include <iostream>
#include <thread>
#include <map>
#include <string>

volatile int SdkManager::threadCount = 0;

SdkManager::SdkManager(int* conf) :
						mWebSocketConnected(false),
						mJsonLogin(false),
						numAccountsRegistering(0),
						numIMsSent(0),
						numAccountsDeregistering(0)

{
	mShared = conf;
	mStart = conf[0];
	mCount = conf[1];

	threadCount++;

	mPhone = Phone::create();

	LicenseInfo licenseInfo;
	licenseInfo.licenseKey = "lkj";
#ifdef WIN32
	licenseInfo.licenseDocumentLocation = "C:\\Temp";
#else
	licenseInfo.licenseDocumentLocation = "./";
#endif
	licenseInfo.licenseAor = "";

	mPhone->initialize(licenseInfo, this);

	mPhone->setLoggingEnabled("xmpp_push_scalability_test_client", true);
}

SdkManager::~SdkManager() {}

void SdkManager::runTest()
{
	std::map<unsigned int, XmppAccountHandle> accountMap;
	std::map<unsigned int, XmppChatHandle> chatMap;

	JsonApiClient* jsonApiClient = JsonApiClient::getInterface(mPhone);
	jsonApiClient->setHandler(this);
	
	JsonApiClientSettings jsonSettings;
	jsonSettings.serverUri = "ws://192.168.2.90:9003";
	jsonApiClient->configureDefaultSettings(jsonSettings);

	jsonApiClient->enable();
	std::cout << "Connecting WebSocket" << std::endl;

	while (!mWebSocketConnected)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(20));
		mPhone->process(Phone::kBlockingModeNonBlocking);
		jsonApiClient->process(Phone::kBlockingModeNonBlocking);
	}

	//std::cout << "Json Login" << std::endl;
	cpc::string loginString("{\"authProvider\" : \"fake\",\"username\" : \"alice");
	loginString.append(std::to_string(mStart).c_str());
	loginString.append("@localhost\",\"password\" : \"foo\"\}");
	jsonApiClient->login(loginString);

	while (!mJsonLogin)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(20));
		mPhone->process(Phone::kBlockingModeNonBlocking);
		jsonApiClient->process(Phone::kBlockingModeNonBlocking);
	}

	XmppAccountManagerJsonProxy* accountJson = XmppAccountManagerJsonProxy::getInterface(mPhone);
	
	//std::cout << "Registering accounts" << std::endl;
	for (unsigned int i = mStart; i <= mStart + mCount; i++)
	{
		XmppAccountSettings settings;
		settings.username = std::to_string(i).c_str();
		settings.password = "openfire";
		settings.domain = "openfire.kfritzke.ca";
		settings.ignoreCertVerification = true;

		XmppAccountHandle acc = accountJson->create(settings);
		accountMap[i] = acc;

		accountJson->setHandler(acc, this);
		accountJson->enable(acc);

		numAccountsRegistering++;
	}
	//std::cout << "Waiting for accounts to register..." << std::endl;

	while (true)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(20));
		mPhone->process(Phone::kBlockingModeNonBlocking);
		accountJson->process(Phone::kBlockingModeNonBlocking);
		if (numAccountsRegistering <= 0)
		{
			break;
		}
	}
	std::cout << "Accounts finished registering " << std::to_string(mStart) << std::endl;

	/*mChatJson = XmppChatManagerJsonProxy::getInterface(mPhone);
	//std::cout << "Sending IMs" << std::endl;
	for (std::map<unsigned int, XmppAccountHandle>::iterator it = accountMap.begin(); it != accountMap.end(); ++it)
	{
		mChatJson->setHandler(it->second, this);

		XmppChatHandle chat = mChatJson->createChat(it->second);
		mChatJson->addParticipant(chat, std::to_string(it->first).append("@openfire.kfritzke.ca").c_str());
		mChatJson->start(chat);
		chatMap[it->first] = chat;

		mChatJson->sendMessage(chat, std::to_string(it->first).c_str());
		numIMsSent++;
	}
	//std::cout << "Waiting for IMs to send..." << std::endl;

	while (true)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(20));
		mPhone->process(Phone::kBlockingModeNonBlocking);
		accountJson->process(Phone::kBlockingModeNonBlocking);
		if (numIMsSent <= 0)
		{
			break;
		}
	}
	std::cout << "Finished sending IMs " << std::to_string(mStart) << std::endl;*/
	
	for (std::map<unsigned int, XmppChatHandle>::iterator it = chatMap.begin(); it != chatMap.end(); ++it)
	{
		mChatJson->end(it->second);
	}

	numAccountsDeregistering = 0;
	//std::cout << "Deregistering accounts" << std::endl;

	for (std::map<unsigned int, XmppAccountHandle>::iterator it = accountMap.begin(); it != accountMap.end(); ++it)
	{
		accountJson->disable(it->second);
		numAccountsDeregistering++;
	}

	//std::cout << "Waiting for accounts to deregistering..." << std::endl;

	while (true)
	{
		std::this_thread::sleep_for(std::chrono::milliseconds(20));
		mPhone->process(Phone::kBlockingModeNonBlocking);
		accountJson->process(Phone::kBlockingModeNonBlocking);
		if (numAccountsDeregistering <= 0)
		{
			break;
		}
	}
	std::cout << "Finished deregistering accounts " << std::to_string(mStart) << std::endl;
	
	jsonApiClient->disable();
}

int SdkManager::onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args)
{
	if (args.accountStatus == XmppAccountStatusChangedEvent::Status::Status_Connected ||
		args.accountStatus == XmppAccountStatusChangedEvent::Status::Status_Failure)
	{
		numAccountsRegistering--;
	}
	else if (args.accountStatus == XmppAccountStatusChangedEvent::Status::Status_Disconnected)
	{
		numAccountsDeregistering--;
	}
	return kSuccess;
}

int SdkManager::onLoginResult(JsonApiLoginHandle h, const LoginResultEvent& args)
{
	mJsonLogin = true;
	return kSuccess;
};

int SdkManager::onStatusChanged(JsonApiLoginHandle h, StatusChangedEvent& args)
{
	if (args.status == StatusChangedEvent::Status_Connected)
	{
		mWebSocketConnected = true;
	}
	else if (args.status == StatusChangedEvent::Status_Failed)
	{
		std::cout << "WebSocket Connection Failed" << std::endl;
	}
	
	return kSuccess;
};

int SdkManager::onNewChat(XmppChatHandle chat, const NewChatEvent& args)
{
	//std::cout << "Accepting chat from " << args.remote << std::endl;
	return kSuccess;
}

int SdkManager::onNewMessage(XmppChatHandle chat, const NewMessageEvent& args)
{
	//std::cout << "Received message " << args.message << " from " << args.from << std::endl;
	numIMsSent--;
	return kSuccess;
}

int SdkManager::onNewReaction(XmppChatHandle chat, const NewReactionEvent& args)
{
	//std::cout << "Received reaction " << args.message << " from " << args.from << std::endl;
	return kSuccess;
}

int SdkManager::onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent& args)
{
	//std::cout << "Received message retraction " << args.message << " from " << args.from << std::endl;
	return kSuccess;
}

int SdkManager::onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent& args)
{
	//std::cout << "onSendMessageSuccess for chat " << chat << std::endl;
	return kSuccess;
}

int SdkManager::onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent& args)
{
	//std::cout << "onSendMessageFailure for chat " << chat << std::endl;
	return kSuccess;
}