#pragma once
#define CPCAPI2_INCLUDE_UNRELEASED_HEADERS 1
#include "cpcapi2.h"

using namespace CPCAPI2;
using namespace CPCAPI2::JsonA<PERSON>;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppChat;

class SdkManager : CPCAPI2::<PERSON><PERSON><PERSON><PERSON>,
				   CPCAPI2::JsonApi::JsonApiClientHandler,
				   CPCAPI2::XmppAccount::XmppAccountHandler,
				   CPCAPI2::XmppChat::XmppChatHandler
{
public:
	SdkManager(int* conf);
	~SdkManager();

	void runTest();

// PhoneHandler
	int onError(const cpc::string& sourceModule, const PhoneErrorEvent& args) { return kSuccess; };
	int onLicensingError(const CPCAPI2::LicensingErrorEvent& args) { return kSuccess; };
	int onLicensingSuccess() { return kSuccess; };
// End PhoneHandler

// JsonApiClientHandler
	int onLoginResult(JsonApiLoginHandle h, const LoginResultEvent& args);
	int onStatusChanged(JsonApiLoginHandle h, StatusChangedEvent& args);
// End JsonApiClientHandler

// XmppAccountHandler
  int onAccountStatusChanged(XmppAccountHandle account, const XmppAccountStatusChangedEvent& args);
  int onError(XmppAccountHandle account, const XmppAccount::ErrorEvent& args) { return kSuccess; };
  int onEntityTime(XmppAccountHandle account, const EntityTimeEvent& args) { return kSuccess; };
  int onEntityFeature(XmppAccountHandle account, const EntityFeatureEvent& args) { return kSuccess; };
// End XmppAccountHandler

// XmppChatHandler
  int onNewChat(XmppChatHandle chat, const NewChatEvent& args);
  int onIsComposingMessage(XmppChatHandle chat, const IsComposingMessageEvent& args) { return kSuccess; };
  int onNewMessage(XmppChatHandle chat, const NewMessageEvent& args);
  int onNewReaction(XmppChatHandle chat, const NewReactionEvent& args);
  int onNewMessageRetraction(XmppChatHandle chat, const NewMessageRetractionEvent& args);
  int onSendMessageSuccess(XmppChatHandle chat, const SendMessageSuccessEvent& args);
  int onSendMessageFailure(XmppChatHandle chat, const SendMessageFailureEvent& args);
  int onMessageDelivered(XmppChatHandle chat, const MessageDeliveredEvent& args) { return kSuccess; };
  int onMessageDeliveryError(XmppChatHandle chat, const MessageDeliveryErrorEvent& args) { return kSuccess; };
  int onMessageDisplayed(XmppChatHandle chat, const MessageDisplayedEvent& args) { return kSuccess; };
  int onChatEnded(XmppChatHandle chat, const ChatEndedEvent& event) { return kSuccess; };
  int onChatDiscoCompleted(XmppChatHandle chat, const ChatDiscoEvent& args) { return kSuccess; };
  int onChatDiscoError(XmppChatHandle chat, const ChatDiscoErrorEvent& args) { return kSuccess; };
  int onError(XmppChatHandle chat, const XmppChat::ErrorEvent& event) { return kSuccess; };
// End XmppChatHandler

private:
	volatile static int threadCount;
	int* mShared;
	int mStart;
	int mCount;

	Phone* mPhone;
	XmppChatManagerJsonProxy* mChatJson;

	bool mWebSocketConnected;
	bool mJsonLogin;

	int numAccountsRegistering;
	int numIMsSent;
	int numAccountsDeregistering;
};

