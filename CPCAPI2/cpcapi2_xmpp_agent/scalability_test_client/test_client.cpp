#include "SdkManager.h"

#define THREADS 16
#define PER_THREAD 1000

DWORD WINAPI MyThreadFunction(LPVOID lpParam)
{
	SdkManager sdk((int*)lpParam);
	sdk.runTest();
	return 0;
}

int main()
{
	DWORD   dwThreadIdArray[THREADS];
	HANDLE  hThreadArray[THREADS];
	int pDataArray[THREADS][3];

	for (int i = 0; i < THREADS; i++)
	{
		pDataArray[i][0] = i * PER_THREAD + 1;
		pDataArray[i][1] = PER_THREAD;
	}

	ULONGLONG startTime = GetTickCount64();

	// Create MAX_THREADS worker threads.
	for (int i = 0; i < THREADS; i++)
	{
		// Create the thread to begin execution on its own.

		hThreadArray[i] = CreateThread(
			NULL,                   // default security attributes
			0,                      // use default stack size  
			MyThreadFunction,       // thread function name
			pDataArray[i],          // argument to thread function 
			0,                      // use default creation flags 
			&dwThreadIdArray[i]);   // returns the thread identifier 
	} // End of main thread creation loop.

    // Wait until all threads have terminated.
	WaitForMultipleObjects(THREADS, hThreadArray, TRUE, INFINITE);

	ULONGLONG elapsedTime = GetTickCount64() - startTime;
	std::cout << "Tests took " << elapsedTime << "ms" << std::endl;

	/*ULONGLONG threadTime = 0;
	for (int i = 0; i < THREADS; i++)
	{
		threadTime += pDataArray[i][2];
	};
	threadTime /= THREADS;
	std::cout << "IM sending took " << threadTime << "ms" << std::endl;*/

	// Close all thread handles and free memory allocations.
	for (int i = 0; i < THREADS; i++)
	{
		CloseHandle(hThreadArray[i]);
	}
	
	std::cin.get();

    return 0;
}

