// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		967D2D481ECD13C700839639 /* cpcapi2_xmpp_agent.cpp in Sources */ = {isa = PBXBuildFile; fileRef = 967D2D471ECD13C700839639 /* cpcapi2_xmpp_agent.cpp */; };
		96A9EAE61F5F102400D2D12E /* cpcapi2_xmpp_agent.config in CopyFiles */ = {isa = PBXBuildFile; fileRef = 96A9EAE21F5F0FEB00D2D12E /* cpcapi2_xmpp_agent.config */; };
		96C7C2E82151B45400AFD58C /* libcodecLib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2E32151B45400AFD58C /* libcodecLib.a */; };
		96C7C2E92151B45400AFD58C /* libamrwb.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2E72151B45400AFD58C /* libamrwb.a */; };
		96C7C2EF2151B45900AFD58C /* libg722_codecPro.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2EA2151B45900AFD58C /* libg722_codecPro.a */; };
		96C7C2F02151B45900AFD58C /* libg729b.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2EB2151B45900AFD58C /* libg729b.a */; };
		96C7C2F12151B45900AFD58C /* libg729ab.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2EC2151B45900AFD58C /* libg729ab.a */; };
		96C7C2F22151B45900AFD58C /* libg729a.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2ED2151B45900AFD58C /* libg729a.a */; };
		96C7C2F32151B45900AFD58C /* libg729.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 96C7C2EE2151B45900AFD58C /* libg729.a */; };
		BE019D231E6A1A4000944820 /* Cpcapi2Runner.cpp in Sources */ = {isa = PBXBuildFile; fileRef = BE019D1D1E6A1A4000944820 /* Cpcapi2Runner.cpp */; };
		BE019D261E6A1A4000944820 /* SdkManager.cpp in Sources */ = {isa = PBXBuildFile; fileRef = BE019D201E6A1A4000944820 /* SdkManager.cpp */; };
		BE019D6F1E6DC15900944820 /* libcrypto.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE019D6D1E6DC15900944820 /* libcrypto.a */; };
		BE019D701E6DC15900944820 /* libssl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE019D6E1E6DC15900944820 /* libssl.a */; };
		BE45E4A920486A3A00945D93 /* APNsAuthKey_462WVPCXB7.p8 in CopyFiles */ = {isa = PBXBuildFile; fileRef = BE45E4A6204869EC00945D93 /* APNsAuthKey_462WVPCXB7.p8 */; };
		BE45E4AA20486A3A00945D93 /* p256-public-key.spki in CopyFiles */ = {isa = PBXBuildFile; fileRef = BE45E4A8204869EC00945D93 /* p256-public-key.spki */; };
		BE5032EB20BDEBA90084CF89 /* libsoci_core.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5032E620BDEBA80084CF89 /* libsoci_core.a */; };
		BE5032EC20BDEBA90084CF89 /* libsoci_sqlite3.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5032EA20BDEBA80084CF89 /* libsoci_sqlite3.a */; };
		BE5032F020BDF8940084CF89 /* libsqlite3.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE5032EF20BDF8940084CF89 /* libsqlite3.a */; };
		BE56BAF321FA3198005475C5 /* IOSurface.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE56BAEF21FA3198005475C5 /* IOSurface.framework */; };
		BE68057C1E7093B600A87F22 /* silence16.pcm in CopyFiles */ = {isa = PBXBuildFile; fileRef = BE68057A1E7093B600A87F22 /* silence16.pcm */; };
		BE9158172283737500480953 /* librtc_base.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE9158132283737500480953 /* librtc_base.a */; };
		BE990B2E1E6E03AA008DFE98 /* libCPCAPI2_Static.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE019D7E1E6DFB2600944820 /* libCPCAPI2_Static.a */; };
		BE990B731E6E06CE008DFE98 /* libaudio_coding_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B331E6E06CE008DFE98 /* libaudio_coding_module.a */; };
		BE990B741E6E06CE008DFE98 /* libaudio_conference_mixer.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B341E6E06CE008DFE98 /* libaudio_conference_mixer.a */; };
		BE990B751E6E06CE008DFE98 /* libaudio_decoder_interface.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B351E6E06CE008DFE98 /* libaudio_decoder_interface.a */; };
		BE990B761E6E06CE008DFE98 /* libaudio_device.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B361E6E06CE008DFE98 /* libaudio_device.a */; };
		BE990B771E6E06CE008DFE98 /* libaudio_encoder_interface.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B371E6E06CE008DFE98 /* libaudio_encoder_interface.a */; };
		BE990B781E6E06CE008DFE98 /* libaudio_processing_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B381E6E06CE008DFE98 /* libaudio_processing_sse2.a */; };
		BE990B791E6E06CE008DFE98 /* libaudio_processing.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B391E6E06CE008DFE98 /* libaudio_processing.a */; };
		BE990B7A1E6E06CE008DFE98 /* libbitrate_controller.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3A1E6E06CE008DFE98 /* libbitrate_controller.a */; };
		BE990B7B1E6E06CE008DFE98 /* libCNG.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3B1E6E06CE008DFE98 /* libCNG.a */; };
		BE990B7C1E6E06CE008DFE98 /* libcommon_audio_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3C1E6E06CE008DFE98 /* libcommon_audio_sse2.a */; };
		BE990B7D1E6E06CE008DFE98 /* libcommon_audio.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3D1E6E06CE008DFE98 /* libcommon_audio.a */; };
		BE990B7E1E6E06CE008DFE98 /* libcommon_video.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3E1E6E06CE008DFE98 /* libcommon_video.a */; };
		BE990B7F1E6E06CE008DFE98 /* libdesktop_capture_differ_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B3F1E6E06CE008DFE98 /* libdesktop_capture_differ_sse2.a */; };
		BE990B801E6E06CE008DFE98 /* libdesktop_capture.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B401E6E06CE008DFE98 /* libdesktop_capture.a */; };
		BE990B811E6E06CE008DFE98 /* libfield_trial_default.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B411E6E06CE008DFE98 /* libfield_trial_default.a */; };
		BE990B821E6E06CE008DFE98 /* libG711.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B421E6E06CE008DFE98 /* libG711.a */; };
		BE990B831E6E06CE008DFE98 /* libG722.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B431E6E06CE008DFE98 /* libG722.a */; };
		BE990B841E6E06CE008DFE98 /* libgenperf_libs.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B441E6E06CE008DFE98 /* libgenperf_libs.a */; };
		BE990B851E6E06CE008DFE98 /* libiSAC.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B451E6E06CE008DFE98 /* libiSAC.a */; };
		BE990B861E6E06CE008DFE98 /* libiSACFix.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B461E6E06CE008DFE98 /* libiSACFix.a */; };
		BE990B871E6E06CE008DFE98 /* libjpeg_turbo.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B471E6E06CE008DFE98 /* libjpeg_turbo.a */; };
		BE990B881E6E06CE008DFE98 /* libmedia_file.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B481E6E06CE008DFE98 /* libmedia_file.a */; };
		BE990B891E6E06CE008DFE98 /* libmetrics_default.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B491E6E06CE008DFE98 /* libmetrics_default.a */; };
		BE990B8A1E6E06CE008DFE98 /* libneteq.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4A1E6E06CE008DFE98 /* libneteq.a */; };
		BE990B8B1E6E06CE008DFE98 /* libopenmax_dl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4B1E6E06CE008DFE98 /* libopenmax_dl.a */; };
		BE990B8C1E6E06CE008DFE98 /* libopus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4C1E6E06CE008DFE98 /* libopus.a */; };
		BE990B8D1E6E06CE008DFE98 /* libpaced_sender.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4D1E6E06CE008DFE98 /* libpaced_sender.a */; };
		BE990B8E1E6E06CE008DFE98 /* libPCM16B.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4E1E6E06CE008DFE98 /* libPCM16B.a */; };
		BE990B8F1E6E06CE008DFE98 /* libred.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B4F1E6E06CE008DFE98 /* libred.a */; };
		BE990B901E6E06CE008DFE98 /* libremote_bitrate_estimator.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B501E6E06CE008DFE98 /* libremote_bitrate_estimator.a */; };
		BE990B911E6E06CE008DFE98 /* librtc_base_approved.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B511E6E06CE008DFE98 /* librtc_base_approved.a */; };
		BE990B921E6E06CE008DFE98 /* librtp_rtcp.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B521E6E06CE008DFE98 /* librtp_rtcp.a */; };
		BE990B931E6E06CE008DFE98 /* libsystem_wrappers.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B531E6E06CE008DFE98 /* libsystem_wrappers.a */; };
		BE990B941E6E06CE008DFE98 /* libvideo_capture_module_internal_impl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B541E6E06CE008DFE98 /* libvideo_capture_module_internal_impl.a */; };
		BE990B951E6E06CE008DFE98 /* libvideo_capture_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B551E6E06CE008DFE98 /* libvideo_capture_module.a */; };
		BE990B961E6E06CE008DFE98 /* libvideo_capture.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B561E6E06CE008DFE98 /* libvideo_capture.a */; };
		BE990B971E6E06CE008DFE98 /* libvideo_coding_utility.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B571E6E06CE008DFE98 /* libvideo_coding_utility.a */; };
		BE990B981E6E06CE008DFE98 /* libvideo_engine_core.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B581E6E06CE008DFE98 /* libvideo_engine_core.a */; };
		BE990B991E6E06CE008DFE98 /* libvideo_processing_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B591E6E06CE008DFE98 /* libvideo_processing_sse2.a */; };
		BE990B9A1E6E06CE008DFE98 /* libvideo_processing.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5A1E6E06CE008DFE98 /* libvideo_processing.a */; };
		BE990B9B1E6E06CE008DFE98 /* libvideo_render_module_internal_impl.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5B1E6E06CE008DFE98 /* libvideo_render_module_internal_impl.a */; };
		BE990B9C1E6E06CE008DFE98 /* libvideo_render_module.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5C1E6E06CE008DFE98 /* libvideo_render_module.a */; };
		BE990B9D1E6E06CE008DFE98 /* libvideo_render.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5D1E6E06CE008DFE98 /* libvideo_render.a */; };
		BE990B9E1E6E06CE008DFE98 /* libvoice_engine.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5E1E6E06CE008DFE98 /* libvoice_engine.a */; };
		BE990B9F1E6E06CE008DFE98 /* libvpx_intrinsics_avx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B5F1E6E06CE008DFE98 /* libvpx_intrinsics_avx.a */; };
		BE990BA01E6E06CE008DFE98 /* libvpx_intrinsics_avx2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B601E6E06CE008DFE98 /* libvpx_intrinsics_avx2.a */; };
		BE990BA11E6E06CE008DFE98 /* libvpx_intrinsics_mmx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B611E6E06CE008DFE98 /* libvpx_intrinsics_mmx.a */; };
		BE990BA21E6E06CE008DFE98 /* libvpx_intrinsics_sse2.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B621E6E06CE008DFE98 /* libvpx_intrinsics_sse2.a */; };
		BE990BA31E6E06CE008DFE98 /* libvpx_intrinsics_sse4_1.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B631E6E06CE008DFE98 /* libvpx_intrinsics_sse4_1.a */; };
		BE990BA41E6E06CE008DFE98 /* libvpx_intrinsics_ssse3.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B641E6E06CE008DFE98 /* libvpx_intrinsics_ssse3.a */; };
		BE990BA51E6E06CE008DFE98 /* libvpx.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B651E6E06CE008DFE98 /* libvpx.a */; };
		BE990BA61E6E06CE008DFE98 /* libwebrtc_AMRWB.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B661E6E06CE008DFE98 /* libwebrtc_AMRWB.a */; };
		BE990BA71E6E06CE008DFE98 /* libwebrtc_common.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B671E6E06CE008DFE98 /* libwebrtc_common.a */; };
		BE990BA81E6E06CE008DFE98 /* libwebrtc_g729.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B681E6E06CE008DFE98 /* libwebrtc_g729.a */; };
		BE990BA91E6E06CE008DFE98 /* libwebrtc_i420.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B691E6E06CE008DFE98 /* libwebrtc_i420.a */; };
		BE990BAA1E6E06CE008DFE98 /* libwebrtc_opus.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6A1E6E06CE008DFE98 /* libwebrtc_opus.a */; };
		BE990BAB1E6E06CE008DFE98 /* libwebrtc_SILK.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6B1E6E06CE008DFE98 /* libwebrtc_SILK.a */; };
		BE990BAC1E6E06CE008DFE98 /* libwebrtc_speex.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6C1E6E06CE008DFE98 /* libwebrtc_speex.a */; };
		BE990BAD1E6E06CE008DFE98 /* libwebrtc_utility.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6D1E6E06CE008DFE98 /* libwebrtc_utility.a */; };
		BE990BAE1E6E06CE008DFE98 /* libwebrtc_video_coding.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6E1E6E06CE008DFE98 /* libwebrtc_video_coding.a */; };
		BE990BAF1E6E06CE008DFE98 /* libwebrtc_vp8.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B6F1E6E06CE008DFE98 /* libwebrtc_vp8.a */; };
		BE990BB01E6E06CE008DFE98 /* libwebrtc_vp9.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B701E6E06CE008DFE98 /* libwebrtc_vp9.a */; };
		BE990BB11E6E06CE008DFE98 /* libwebrtc.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B711E6E06CE008DFE98 /* libwebrtc.a */; };
		BE990BB21E6E06CE008DFE98 /* libyuv.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B721E6E06CE008DFE98 /* libyuv.a */; };
		BE990BB61E6E090F008DFE98 /* AVFoundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BB31E6E090F008DFE98 /* AVFoundation.framework */; };
		BE990BB71E6E090F008DFE98 /* AVKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BB41E6E090F008DFE98 /* AVKit.framework */; };
		BE990BB81E6E090F008DFE98 /* CoreMedia.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BB51E6E090F008DFE98 /* CoreMedia.framework */; };
		BE990BC21E6E09B8008DFE98 /* AppKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BB91E6E09B8008DFE98 /* AppKit.framework */; };
		BE990BC31E6E09B8008DFE98 /* AudioToolbox.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBA1E6E09B8008DFE98 /* AudioToolbox.framework */; };
		BE990BC41E6E09B8008DFE98 /* CoreAudio.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBB1E6E09B8008DFE98 /* CoreAudio.framework */; };
		BE990BC51E6E09B8008DFE98 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBC1E6E09B8008DFE98 /* Foundation.framework */; };
		BE990BC61E6E09B8008DFE98 /* IOKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBD1E6E09B8008DFE98 /* IOKit.framework */; };
		BE990BC71E6E09B8008DFE98 /* OpenGL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBE1E6E09B8008DFE98 /* OpenGL.framework */; };
		BE990BC81E6E09B8008DFE98 /* QuartzCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BBF1E6E09B8008DFE98 /* QuartzCore.framework */; };
		BE990BC91E6E09B8008DFE98 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BC01E6E09B8008DFE98 /* Security.framework */; };
		BE990BCA1E6E09B8008DFE98 /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BC11E6E09B8008DFE98 /* SystemConfiguration.framework */; };
		BE990BCE1E6E0B68008DFE98 /* libresolv.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BCB1E6E0B68008DFE98 /* libresolv.tbd */; };
		BE990BD21E6E0BE2008DFE98 /* libvqmep.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BD11E6E0BE2008DFE98 /* libvqmep.a */; };
		BE990BEE1E6F172E008DFE98 /* libresiprocate.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BED1E6F172E008DFE98 /* libresiprocate.a */; };
		BE990BEF1E6F17A8008DFE98 /* libwebrtc_recon.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B311E6E0570008DFE98 /* libwebrtc_recon.a */; };
		BE990BF11E6F17BC008DFE98 /* libSRTP.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990BF01E6F17BC008DFE98 /* libSRTP.a */; };
		BE990BF21E6F17CC008DFE98 /* libCPCObjAPI2_OSX.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BE990B2F1E6E03AA008DFE98 /* libCPCObjAPI2_OSX.a */; };
		BEE1501D213609BD005FBD5E /* APNsSandboxAuthKey_462WVPCXB7.p8 in CopyFiles */ = {isa = PBXBuildFile; fileRef = BEE15019213609BD005FBD5E /* APNsSandboxAuthKey_462WVPCXB7.p8 */; };
		BEE9AAA120A4E880001F40A0 /* libredisclient.a in Frameworks */ = {isa = PBXBuildFile; fileRef = BEE9AAA020A4E880001F40A0 /* libredisclient.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		BE019D7D1E6DFB2600944820 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 75CF0AB5166982A700558408;
			remoteInfo = CPCAPI2_Static;
		};
		BE019D7F1E6DFB2600944820 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 1785FAAC1958611500D217F3;
			remoteInfo = CPCAPI2_UnitTest;
		};
		BE019D811E6DFB2600944820 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 756EEC7B199BF90D0028D689;
			remoteInfo = CPCAPI2_Shared;
		};
		BE019D831E6DFB2600944820 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 6E3A6F011A24FFD600836E4A;
			remoteInfo = CPCAPI2;
		};
		BE990B281E6E023B008DFE98 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 75CF0AB4166982A700558408;
			remoteInfo = CPCAPI2_Static;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		BE019CE21E6A187700944820 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 16;
			files = (
				BEE1501D213609BD005FBD5E /* APNsSandboxAuthKey_462WVPCXB7.p8 in CopyFiles */,
				BE45E4A920486A3A00945D93 /* APNsAuthKey_462WVPCXB7.p8 in CopyFiles */,
				BE45E4AA20486A3A00945D93 /* p256-public-key.spki in CopyFiles */,
				BE68057C1E7093B600A87F22 /* silence16.pcm in CopyFiles */,
				96A9EAE61F5F102400D2D12E /* cpcapi2_xmpp_agent.config in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		967D2D471ECD13C700839639 /* cpcapi2_xmpp_agent.cpp */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.cpp; name = cpcapi2_xmpp_agent.cpp; path = ../../cpcapi2_xmpp_agent.cpp; sourceTree = "<group>"; };
		96A9EAE21F5F0FEB00D2D12E /* cpcapi2_xmpp_agent.config */ = {isa = PBXFileReference; lastKnownFileType = text; name = cpcapi2_xmpp_agent.config; path = ../../cpcapi2_xmpp_agent.config; sourceTree = "<group>"; };
		96C7C2E32151B45400AFD58C /* libcodecLib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcodecLib.a; path = ../../../../osx_libs/codecpro/amrwb/libcodecLib.a; sourceTree = "<group>"; };
		96C7C2E72151B45400AFD58C /* libamrwb.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libamrwb.a; path = ../../../../osx_libs/codecpro/amrwb/libamrwb.a; sourceTree = "<group>"; };
		96C7C2EA2151B45900AFD58C /* libg722_codecPro.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libg722_codecPro.a; path = ../../../../osx_libs/codecpro/g729/libg722_codecPro.a; sourceTree = "<group>"; };
		96C7C2EB2151B45900AFD58C /* libg729b.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libg729b.a; path = ../../../../osx_libs/codecpro/g729/libg729b.a; sourceTree = "<group>"; };
		96C7C2EC2151B45900AFD58C /* libg729ab.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libg729ab.a; path = ../../../../osx_libs/codecpro/g729/libg729ab.a; sourceTree = "<group>"; };
		96C7C2ED2151B45900AFD58C /* libg729a.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libg729a.a; path = ../../../../osx_libs/codecpro/g729/libg729a.a; sourceTree = "<group>"; };
		96C7C2EE2151B45900AFD58C /* libg729.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libg729.a; path = ../../../../osx_libs/codecpro/g729/libg729.a; sourceTree = "<group>"; };
		BE019CE41E6A187700944820 /* cpcapi2_xmpp_agent */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = cpcapi2_xmpp_agent; sourceTree = BUILT_PRODUCTS_DIR; };
		BE019D1D1E6A1A4000944820 /* Cpcapi2Runner.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = Cpcapi2Runner.cpp; path = ../../Cpcapi2Runner.cpp; sourceTree = "<group>"; };
		BE019D1E1E6A1A4000944820 /* Logger.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; name = Logger.h; path = ../../Logger.h; sourceTree = "<group>"; };
		BE019D201E6A1A4000944820 /* SdkManager.cpp */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.cpp; name = SdkManager.cpp; path = ../../SdkManager.cpp; sourceTree = "<group>"; };
		BE019D6D1E6DC15900944820 /* libcrypto.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcrypto.a; path = ../../../../osx_libs/openssl/lib/libcrypto.a; sourceTree = "<group>"; };
		BE019D6E1E6DC15900944820 /* libssl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libssl.a; path = ../../../../osx_libs/openssl/lib/libssl.a; sourceTree = "<group>"; };
		BE019D721E6DF58600944820 /* libcurl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcurl.a; path = "../../../external/curl/curl-osx/lib/libcurl.a"; sourceTree = "<group>"; };
		BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = CPCAPI2_OSX.xcodeproj; path = ../../../projects/xcode/CPCAPI2/CPCAPI2_OSX.xcodeproj; sourceTree = "<group>"; };
		BE45E4A6204869EC00945D93 /* APNsAuthKey_462WVPCXB7.p8 */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = APNsAuthKey_462WVPCXB7.p8; path = ../../APNsAuthKey_462WVPCXB7.p8; sourceTree = "<group>"; };
		BE45E4A7204869EC00945D93 /* http_fcm_config.json */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.json; name = http_fcm_config.json; path = ../../http_fcm_config.json; sourceTree = "<group>"; };
		BE45E4A8204869EC00945D93 /* p256-public-key.spki */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = "p256-public-key.spki"; path = "../../p256-public-key.spki"; sourceTree = "<group>"; };
		BE5032E620BDEBA80084CF89 /* libsoci_core.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsoci_core.a; path = ../../../../osx_libs/soci/lib/Debug/libsoci_core.a; sourceTree = "<group>"; };
		BE5032EA20BDEBA80084CF89 /* libsoci_sqlite3.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsoci_sqlite3.a; path = ../../../../osx_libs/soci/lib/Debug/libsoci_sqlite3.a; sourceTree = "<group>"; };
		BE5032ED20BDF8050084CF89 /* libsqlite3.0.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libsqlite3.0.dylib; path = ../../../../osx_libs/sqlite3/lib/libsqlite3.0.dylib; sourceTree = "<group>"; };
		BE5032EF20BDF8940084CF89 /* libsqlite3.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsqlite3.a; path = ../../../../osx_libs/sqlite3/lib/libsqlite3.a; sourceTree = "<group>"; };
		BE56BAEF21FA3198005475C5 /* IOSurface.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOSurface.framework; path = System/Library/Frameworks/IOSurface.framework; sourceTree = SDKROOT; };
		BE6805791E7093B600A87F22 /* settings0.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = settings0.json; path = ../../settings0.json; sourceTree = "<group>"; };
		BE68057A1E7093B600A87F22 /* silence16.pcm */ = {isa = PBXFileReference; lastKnownFileType = file; name = silence16.pcm; path = ../../silence16.pcm; sourceTree = "<group>"; };
		BE8A95D61F43B09E00CACD5A /* http_apn_config.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; name = http_apn_config.json; path = ../../http_apn_config.json; sourceTree = "<group>"; };
		BE8A95DB1F44962100CACD5A /* version */ = {isa = PBXFileReference; lastKnownFileType = text; name = version; path = ../../../../osx_libs/curl/version; sourceTree = "<group>"; };
		BE9158132283737500480953 /* librtc_base.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtc_base.a; path = ../../../../osx_libs/webrtc/Debug/librtc_base.a; sourceTree = "<group>"; };
		BE990B2F1E6E03AA008DFE98 /* libCPCObjAPI2_OSX.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libCPCObjAPI2_OSX.a; path = "../../../../../Users/<USER>/Library/Developer/Xcode/DerivedData/cpcapi2_conf-frblbrlxevqodhcfkbrkombbbkly/Build/Products/Debug/libCPCObjAPI2_OSX.a"; sourceTree = "<group>"; };
		BE990B311E6E0570008DFE98 /* libwebrtc_recon.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_recon.a; path = "../../../../../Users/<USER>/Library/Developer/Xcode/DerivedData/cpcapi2_conf-frblbrlxevqodhcfkbrkombbbkly/Build/Products/Debug/libwebrtc_recon.a"; sourceTree = "<group>"; };
		BE990B331E6E06CE008DFE98 /* libaudio_coding_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_coding_module.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_coding_module.a; sourceTree = "<group>"; };
		BE990B341E6E06CE008DFE98 /* libaudio_conference_mixer.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_conference_mixer.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_conference_mixer.a; sourceTree = "<group>"; };
		BE990B351E6E06CE008DFE98 /* libaudio_decoder_interface.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_decoder_interface.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_decoder_interface.a; sourceTree = "<group>"; };
		BE990B361E6E06CE008DFE98 /* libaudio_device.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_device.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_device.a; sourceTree = "<group>"; };
		BE990B371E6E06CE008DFE98 /* libaudio_encoder_interface.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_encoder_interface.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_encoder_interface.a; sourceTree = "<group>"; };
		BE990B381E6E06CE008DFE98 /* libaudio_processing_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_processing_sse2.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_processing_sse2.a; sourceTree = "<group>"; };
		BE990B391E6E06CE008DFE98 /* libaudio_processing.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libaudio_processing.a; path = ../../../../osx_libs/webrtc/Debug/libaudio_processing.a; sourceTree = "<group>"; };
		BE990B3A1E6E06CE008DFE98 /* libbitrate_controller.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libbitrate_controller.a; path = ../../../../osx_libs/webrtc/Debug/libbitrate_controller.a; sourceTree = "<group>"; };
		BE990B3B1E6E06CE008DFE98 /* libCNG.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libCNG.a; path = ../../../../osx_libs/webrtc/Debug/libCNG.a; sourceTree = "<group>"; };
		BE990B3C1E6E06CE008DFE98 /* libcommon_audio_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_audio_sse2.a; path = ../../../../osx_libs/webrtc/Debug/libcommon_audio_sse2.a; sourceTree = "<group>"; };
		BE990B3D1E6E06CE008DFE98 /* libcommon_audio.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_audio.a; path = ../../../../osx_libs/webrtc/Debug/libcommon_audio.a; sourceTree = "<group>"; };
		BE990B3E1E6E06CE008DFE98 /* libcommon_video.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libcommon_video.a; path = ../../../../osx_libs/webrtc/Debug/libcommon_video.a; sourceTree = "<group>"; };
		BE990B3F1E6E06CE008DFE98 /* libdesktop_capture_differ_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libdesktop_capture_differ_sse2.a; path = ../../../../osx_libs/webrtc/Debug/libdesktop_capture_differ_sse2.a; sourceTree = "<group>"; };
		BE990B401E6E06CE008DFE98 /* libdesktop_capture.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libdesktop_capture.a; path = ../../../../osx_libs/webrtc/Debug/libdesktop_capture.a; sourceTree = "<group>"; };
		BE990B411E6E06CE008DFE98 /* libfield_trial_default.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libfield_trial_default.a; path = ../../../../osx_libs/webrtc/Debug/libfield_trial_default.a; sourceTree = "<group>"; };
		BE990B421E6E06CE008DFE98 /* libG711.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libG711.a; path = ../../../../osx_libs/webrtc/Debug/libG711.a; sourceTree = "<group>"; };
		BE990B431E6E06CE008DFE98 /* libG722.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libG722.a; path = ../../../../osx_libs/webrtc/Debug/libG722.a; sourceTree = "<group>"; };
		BE990B441E6E06CE008DFE98 /* libgenperf_libs.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libgenperf_libs.a; path = ../../../../osx_libs/webrtc/Debug/libgenperf_libs.a; sourceTree = "<group>"; };
		BE990B451E6E06CE008DFE98 /* libiSAC.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libiSAC.a; path = ../../../../osx_libs/webrtc/Debug/libiSAC.a; sourceTree = "<group>"; };
		BE990B461E6E06CE008DFE98 /* libiSACFix.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libiSACFix.a; path = ../../../../osx_libs/webrtc/Debug/libiSACFix.a; sourceTree = "<group>"; };
		BE990B471E6E06CE008DFE98 /* libjpeg_turbo.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libjpeg_turbo.a; path = ../../../../osx_libs/webrtc/Debug/libjpeg_turbo.a; sourceTree = "<group>"; };
		BE990B481E6E06CE008DFE98 /* libmedia_file.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmedia_file.a; path = ../../../../osx_libs/webrtc/Debug/libmedia_file.a; sourceTree = "<group>"; };
		BE990B491E6E06CE008DFE98 /* libmetrics_default.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmetrics_default.a; path = ../../../../osx_libs/webrtc/Debug/libmetrics_default.a; sourceTree = "<group>"; };
		BE990B4A1E6E06CE008DFE98 /* libneteq.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libneteq.a; path = ../../../../osx_libs/webrtc/Debug/libneteq.a; sourceTree = "<group>"; };
		BE990B4B1E6E06CE008DFE98 /* libopenmax_dl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libopenmax_dl.a; path = ../../../../osx_libs/webrtc/Debug/libopenmax_dl.a; sourceTree = "<group>"; };
		BE990B4C1E6E06CE008DFE98 /* libopus.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libopus.a; path = ../../../../osx_libs/webrtc/Debug/libopus.a; sourceTree = "<group>"; };
		BE990B4D1E6E06CE008DFE98 /* libpaced_sender.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libpaced_sender.a; path = ../../../../osx_libs/webrtc/Debug/libpaced_sender.a; sourceTree = "<group>"; };
		BE990B4E1E6E06CE008DFE98 /* libPCM16B.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libPCM16B.a; path = ../../../../osx_libs/webrtc/Debug/libPCM16B.a; sourceTree = "<group>"; };
		BE990B4F1E6E06CE008DFE98 /* libred.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libred.a; path = ../../../../osx_libs/webrtc/Debug/libred.a; sourceTree = "<group>"; };
		BE990B501E6E06CE008DFE98 /* libremote_bitrate_estimator.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libremote_bitrate_estimator.a; path = ../../../../osx_libs/webrtc/Debug/libremote_bitrate_estimator.a; sourceTree = "<group>"; };
		BE990B511E6E06CE008DFE98 /* librtc_base_approved.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtc_base_approved.a; path = ../../../../osx_libs/webrtc/Debug/librtc_base_approved.a; sourceTree = "<group>"; };
		BE990B521E6E06CE008DFE98 /* librtp_rtcp.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = librtp_rtcp.a; path = ../../../../osx_libs/webrtc/Debug/librtp_rtcp.a; sourceTree = "<group>"; };
		BE990B531E6E06CE008DFE98 /* libsystem_wrappers.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libsystem_wrappers.a; path = ../../../../osx_libs/webrtc/Debug/libsystem_wrappers.a; sourceTree = "<group>"; };
		BE990B541E6E06CE008DFE98 /* libvideo_capture_module_internal_impl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture_module_internal_impl.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_capture_module_internal_impl.a; sourceTree = "<group>"; };
		BE990B551E6E06CE008DFE98 /* libvideo_capture_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture_module.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_capture_module.a; sourceTree = "<group>"; };
		BE990B561E6E06CE008DFE98 /* libvideo_capture.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_capture.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_capture.a; sourceTree = "<group>"; };
		BE990B571E6E06CE008DFE98 /* libvideo_coding_utility.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_coding_utility.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_coding_utility.a; sourceTree = "<group>"; };
		BE990B581E6E06CE008DFE98 /* libvideo_engine_core.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_engine_core.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_engine_core.a; sourceTree = "<group>"; };
		BE990B591E6E06CE008DFE98 /* libvideo_processing_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_processing_sse2.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_processing_sse2.a; sourceTree = "<group>"; };
		BE990B5A1E6E06CE008DFE98 /* libvideo_processing.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_processing.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_processing.a; sourceTree = "<group>"; };
		BE990B5B1E6E06CE008DFE98 /* libvideo_render_module_internal_impl.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render_module_internal_impl.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_render_module_internal_impl.a; sourceTree = "<group>"; };
		BE990B5C1E6E06CE008DFE98 /* libvideo_render_module.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render_module.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_render_module.a; sourceTree = "<group>"; };
		BE990B5D1E6E06CE008DFE98 /* libvideo_render.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvideo_render.a; path = ../../../../osx_libs/webrtc/Debug/libvideo_render.a; sourceTree = "<group>"; };
		BE990B5E1E6E06CE008DFE98 /* libvoice_engine.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvoice_engine.a; path = ../../../../osx_libs/webrtc/Debug/libvoice_engine.a; sourceTree = "<group>"; };
		BE990B5F1E6E06CE008DFE98 /* libvpx_intrinsics_avx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_avx.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_avx.a; sourceTree = "<group>"; };
		BE990B601E6E06CE008DFE98 /* libvpx_intrinsics_avx2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_avx2.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_avx2.a; sourceTree = "<group>"; };
		BE990B611E6E06CE008DFE98 /* libvpx_intrinsics_mmx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_mmx.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_mmx.a; sourceTree = "<group>"; };
		BE990B621E6E06CE008DFE98 /* libvpx_intrinsics_sse2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_sse2.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_sse2.a; sourceTree = "<group>"; };
		BE990B631E6E06CE008DFE98 /* libvpx_intrinsics_sse4_1.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_sse4_1.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_sse4_1.a; sourceTree = "<group>"; };
		BE990B641E6E06CE008DFE98 /* libvpx_intrinsics_ssse3.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx_intrinsics_ssse3.a; path = ../../../../osx_libs/webrtc/Debug/libvpx_intrinsics_ssse3.a; sourceTree = "<group>"; };
		BE990B651E6E06CE008DFE98 /* libvpx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvpx.a; path = ../../../../osx_libs/webrtc/Debug/libvpx.a; sourceTree = "<group>"; };
		BE990B661E6E06CE008DFE98 /* libwebrtc_AMRWB.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_AMRWB.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_AMRWB.a; sourceTree = "<group>"; };
		BE990B671E6E06CE008DFE98 /* libwebrtc_common.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_common.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_common.a; sourceTree = "<group>"; };
		BE990B681E6E06CE008DFE98 /* libwebrtc_g729.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_g729.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_g729.a; sourceTree = "<group>"; };
		BE990B691E6E06CE008DFE98 /* libwebrtc_i420.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_i420.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_i420.a; sourceTree = "<group>"; };
		BE990B6A1E6E06CE008DFE98 /* libwebrtc_opus.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_opus.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_opus.a; sourceTree = "<group>"; };
		BE990B6B1E6E06CE008DFE98 /* libwebrtc_SILK.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_SILK.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_SILK.a; sourceTree = "<group>"; };
		BE990B6C1E6E06CE008DFE98 /* libwebrtc_speex.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_speex.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_speex.a; sourceTree = "<group>"; };
		BE990B6D1E6E06CE008DFE98 /* libwebrtc_utility.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_utility.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_utility.a; sourceTree = "<group>"; };
		BE990B6E1E6E06CE008DFE98 /* libwebrtc_video_coding.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_video_coding.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_video_coding.a; sourceTree = "<group>"; };
		BE990B6F1E6E06CE008DFE98 /* libwebrtc_vp8.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_vp8.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_vp8.a; sourceTree = "<group>"; };
		BE990B701E6E06CE008DFE98 /* libwebrtc_vp9.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc_vp9.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc_vp9.a; sourceTree = "<group>"; };
		BE990B711E6E06CE008DFE98 /* libwebrtc.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libwebrtc.a; path = ../../../../osx_libs/webrtc/Debug/libwebrtc.a; sourceTree = "<group>"; };
		BE990B721E6E06CE008DFE98 /* libyuv.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libyuv.a; path = ../../../../osx_libs/webrtc/Debug/libyuv.a; sourceTree = "<group>"; };
		BE990BB31E6E090F008DFE98 /* AVFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVFoundation.framework; path = System/Library/Frameworks/AVFoundation.framework; sourceTree = SDKROOT; };
		BE990BB41E6E090F008DFE98 /* AVKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AVKit.framework; path = System/Library/Frameworks/AVKit.framework; sourceTree = SDKROOT; };
		BE990BB51E6E090F008DFE98 /* CoreMedia.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreMedia.framework; path = System/Library/Frameworks/CoreMedia.framework; sourceTree = SDKROOT; };
		BE990BB91E6E09B8008DFE98 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = System/Library/Frameworks/AppKit.framework; sourceTree = SDKROOT; };
		BE990BBA1E6E09B8008DFE98 /* AudioToolbox.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AudioToolbox.framework; path = System/Library/Frameworks/AudioToolbox.framework; sourceTree = SDKROOT; };
		BE990BBB1E6E09B8008DFE98 /* CoreAudio.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreAudio.framework; path = System/Library/Frameworks/CoreAudio.framework; sourceTree = SDKROOT; };
		BE990BBC1E6E09B8008DFE98 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		BE990BBD1E6E09B8008DFE98 /* IOKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = IOKit.framework; path = System/Library/Frameworks/IOKit.framework; sourceTree = SDKROOT; };
		BE990BBE1E6E09B8008DFE98 /* OpenGL.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = OpenGL.framework; path = System/Library/Frameworks/OpenGL.framework; sourceTree = SDKROOT; };
		BE990BBF1E6E09B8008DFE98 /* QuartzCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuartzCore.framework; path = System/Library/Frameworks/QuartzCore.framework; sourceTree = SDKROOT; };
		BE990BC01E6E09B8008DFE98 /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		BE990BC11E6E09B8008DFE98 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		BE990BCB1E6E0B68008DFE98 /* libresolv.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libresolv.tbd; path = usr/lib/libresolv.tbd; sourceTree = SDKROOT; };
		BE990BD11E6E0BE2008DFE98 /* libvqmep.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libvqmep.a; path = "../../../external/vqmon/vqmon/lib/macosx-i386_x86_64/debug/libvqmep.a"; sourceTree = "<group>"; };
		BE990BED1E6F172E008DFE98 /* libresiprocate.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libresiprocate.a; path = "../../../../../Users/<USER>/Library/Developer/Xcode/DerivedData/cpcapi2_conf-frblbrlxevqodhcfkbrkombbbkly/Build/Products/Debug/libresiprocate.a"; sourceTree = "<group>"; };
		BE990BF01E6F17BC008DFE98 /* libSRTP.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libSRTP.a; path = "../../../../../Users/<USER>/Library/Developer/Xcode/DerivedData/cpcapi2_conf-frblbrlxevqodhcfkbrkombbbkly/Build/Products/Debug/libSRTP.a"; sourceTree = "<group>"; };
		BEC9CAE11F72FAEA00CAB43C /* libbsoncxx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libbsoncxx.a; path = ../../db/mongodb/mongocxx/osx/lib/bsoncxx/libbsoncxx.a; sourceTree = "<group>"; };
		BEC9CAE31F72FB1400CAB43C /* libmongocxx.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libmongocxx.a; path = ../../db/mongodb/mongocxx/osx/lib/mongocxx/libmongocxx.a; sourceTree = "<group>"; };
		BEE15019213609BD005FBD5E /* APNsSandboxAuthKey_462WVPCXB7.p8 */ = {isa = PBXFileReference; lastKnownFileType = text; name = APNsSandboxAuthKey_462WVPCXB7.p8; path = ../../APNsSandboxAuthKey_462WVPCXB7.p8; sourceTree = "<group>"; };
		BEE9AAA020A4E880001F40A0 /* libredisclient.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; path = libredisclient.a; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		BE019CE11E6A187700944820 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BE9158172283737500480953 /* librtc_base.a in Frameworks */,
				BE56BAF321FA3198005475C5 /* IOSurface.framework in Frameworks */,
				96C7C2EF2151B45900AFD58C /* libg722_codecPro.a in Frameworks */,
				96C7C2F02151B45900AFD58C /* libg729b.a in Frameworks */,
				96C7C2F12151B45900AFD58C /* libg729ab.a in Frameworks */,
				96C7C2F22151B45900AFD58C /* libg729a.a in Frameworks */,
				96C7C2F32151B45900AFD58C /* libg729.a in Frameworks */,
				96C7C2E82151B45400AFD58C /* libcodecLib.a in Frameworks */,
				96C7C2E92151B45400AFD58C /* libamrwb.a in Frameworks */,
				BE5032F020BDF8940084CF89 /* libsqlite3.a in Frameworks */,
				BE5032EB20BDEBA90084CF89 /* libsoci_core.a in Frameworks */,
				BE5032EC20BDEBA90084CF89 /* libsoci_sqlite3.a in Frameworks */,
				BEE9AAA120A4E880001F40A0 /* libredisclient.a in Frameworks */,
				BE019D6F1E6DC15900944820 /* libcrypto.a in Frameworks */,
				BE019D701E6DC15900944820 /* libssl.a in Frameworks */,
				BE990BF21E6F17CC008DFE98 /* libCPCObjAPI2_OSX.a in Frameworks */,
				BE990BF11E6F17BC008DFE98 /* libSRTP.a in Frameworks */,
				BE990BEF1E6F17A8008DFE98 /* libwebrtc_recon.a in Frameworks */,
				BE990BEE1E6F172E008DFE98 /* libresiprocate.a in Frameworks */,
				BE990BD21E6E0BE2008DFE98 /* libvqmep.a in Frameworks */,
				BE990BCE1E6E0B68008DFE98 /* libresolv.tbd in Frameworks */,
				BE990BC21E6E09B8008DFE98 /* AppKit.framework in Frameworks */,
				BE990BC31E6E09B8008DFE98 /* AudioToolbox.framework in Frameworks */,
				BE990BC41E6E09B8008DFE98 /* CoreAudio.framework in Frameworks */,
				BE990BC51E6E09B8008DFE98 /* Foundation.framework in Frameworks */,
				BE990BC61E6E09B8008DFE98 /* IOKit.framework in Frameworks */,
				BE990BC71E6E09B8008DFE98 /* OpenGL.framework in Frameworks */,
				BE990BC81E6E09B8008DFE98 /* QuartzCore.framework in Frameworks */,
				BE990BC91E6E09B8008DFE98 /* Security.framework in Frameworks */,
				BE990BCA1E6E09B8008DFE98 /* SystemConfiguration.framework in Frameworks */,
				BE990BB61E6E090F008DFE98 /* AVFoundation.framework in Frameworks */,
				BE990BB71E6E090F008DFE98 /* AVKit.framework in Frameworks */,
				BE990BB81E6E090F008DFE98 /* CoreMedia.framework in Frameworks */,
				BE990B731E6E06CE008DFE98 /* libaudio_coding_module.a in Frameworks */,
				BE990B741E6E06CE008DFE98 /* libaudio_conference_mixer.a in Frameworks */,
				BE990B751E6E06CE008DFE98 /* libaudio_decoder_interface.a in Frameworks */,
				BE990B761E6E06CE008DFE98 /* libaudio_device.a in Frameworks */,
				BE990B771E6E06CE008DFE98 /* libaudio_encoder_interface.a in Frameworks */,
				BE990B781E6E06CE008DFE98 /* libaudio_processing_sse2.a in Frameworks */,
				BE990B791E6E06CE008DFE98 /* libaudio_processing.a in Frameworks */,
				BE990B7A1E6E06CE008DFE98 /* libbitrate_controller.a in Frameworks */,
				BE990B7B1E6E06CE008DFE98 /* libCNG.a in Frameworks */,
				BE990B7C1E6E06CE008DFE98 /* libcommon_audio_sse2.a in Frameworks */,
				BE990B7D1E6E06CE008DFE98 /* libcommon_audio.a in Frameworks */,
				BE990B7E1E6E06CE008DFE98 /* libcommon_video.a in Frameworks */,
				BE990B7F1E6E06CE008DFE98 /* libdesktop_capture_differ_sse2.a in Frameworks */,
				BE990B801E6E06CE008DFE98 /* libdesktop_capture.a in Frameworks */,
				BE990B811E6E06CE008DFE98 /* libfield_trial_default.a in Frameworks */,
				BE990B821E6E06CE008DFE98 /* libG711.a in Frameworks */,
				BE990B831E6E06CE008DFE98 /* libG722.a in Frameworks */,
				BE990B841E6E06CE008DFE98 /* libgenperf_libs.a in Frameworks */,
				BE990B851E6E06CE008DFE98 /* libiSAC.a in Frameworks */,
				BE990B861E6E06CE008DFE98 /* libiSACFix.a in Frameworks */,
				BE990B871E6E06CE008DFE98 /* libjpeg_turbo.a in Frameworks */,
				BE990B881E6E06CE008DFE98 /* libmedia_file.a in Frameworks */,
				BE990B891E6E06CE008DFE98 /* libmetrics_default.a in Frameworks */,
				BE990B8A1E6E06CE008DFE98 /* libneteq.a in Frameworks */,
				BE990B8B1E6E06CE008DFE98 /* libopenmax_dl.a in Frameworks */,
				BE990B8C1E6E06CE008DFE98 /* libopus.a in Frameworks */,
				BE990B8D1E6E06CE008DFE98 /* libpaced_sender.a in Frameworks */,
				BE990B8E1E6E06CE008DFE98 /* libPCM16B.a in Frameworks */,
				BE990B8F1E6E06CE008DFE98 /* libred.a in Frameworks */,
				BE990B901E6E06CE008DFE98 /* libremote_bitrate_estimator.a in Frameworks */,
				BE990B911E6E06CE008DFE98 /* librtc_base_approved.a in Frameworks */,
				BE990B921E6E06CE008DFE98 /* librtp_rtcp.a in Frameworks */,
				BE990B931E6E06CE008DFE98 /* libsystem_wrappers.a in Frameworks */,
				BE990B941E6E06CE008DFE98 /* libvideo_capture_module_internal_impl.a in Frameworks */,
				BE990B951E6E06CE008DFE98 /* libvideo_capture_module.a in Frameworks */,
				BE990B961E6E06CE008DFE98 /* libvideo_capture.a in Frameworks */,
				BE990B971E6E06CE008DFE98 /* libvideo_coding_utility.a in Frameworks */,
				BE990B981E6E06CE008DFE98 /* libvideo_engine_core.a in Frameworks */,
				BE990B991E6E06CE008DFE98 /* libvideo_processing_sse2.a in Frameworks */,
				BE990B9A1E6E06CE008DFE98 /* libvideo_processing.a in Frameworks */,
				BE990B9B1E6E06CE008DFE98 /* libvideo_render_module_internal_impl.a in Frameworks */,
				BE990B9C1E6E06CE008DFE98 /* libvideo_render_module.a in Frameworks */,
				BE990B9D1E6E06CE008DFE98 /* libvideo_render.a in Frameworks */,
				BE990B9E1E6E06CE008DFE98 /* libvoice_engine.a in Frameworks */,
				BE990B9F1E6E06CE008DFE98 /* libvpx_intrinsics_avx.a in Frameworks */,
				BE990BA01E6E06CE008DFE98 /* libvpx_intrinsics_avx2.a in Frameworks */,
				BE990BA11E6E06CE008DFE98 /* libvpx_intrinsics_mmx.a in Frameworks */,
				BE990BA21E6E06CE008DFE98 /* libvpx_intrinsics_sse2.a in Frameworks */,
				BE990BA31E6E06CE008DFE98 /* libvpx_intrinsics_sse4_1.a in Frameworks */,
				BE990BA41E6E06CE008DFE98 /* libvpx_intrinsics_ssse3.a in Frameworks */,
				BE990BA51E6E06CE008DFE98 /* libvpx.a in Frameworks */,
				BE990BA61E6E06CE008DFE98 /* libwebrtc_AMRWB.a in Frameworks */,
				BE990BA71E6E06CE008DFE98 /* libwebrtc_common.a in Frameworks */,
				BE990BA81E6E06CE008DFE98 /* libwebrtc_g729.a in Frameworks */,
				BE990BA91E6E06CE008DFE98 /* libwebrtc_i420.a in Frameworks */,
				BE990BAA1E6E06CE008DFE98 /* libwebrtc_opus.a in Frameworks */,
				BE990BAB1E6E06CE008DFE98 /* libwebrtc_SILK.a in Frameworks */,
				BE990BAC1E6E06CE008DFE98 /* libwebrtc_speex.a in Frameworks */,
				BE990BAD1E6E06CE008DFE98 /* libwebrtc_utility.a in Frameworks */,
				BE990BAE1E6E06CE008DFE98 /* libwebrtc_video_coding.a in Frameworks */,
				BE990BAF1E6E06CE008DFE98 /* libwebrtc_vp8.a in Frameworks */,
				BE990BB01E6E06CE008DFE98 /* libwebrtc_vp9.a in Frameworks */,
				BE990BB11E6E06CE008DFE98 /* libwebrtc.a in Frameworks */,
				BE990BB21E6E06CE008DFE98 /* libyuv.a in Frameworks */,
				BE990B2E1E6E03AA008DFE98 /* libCPCAPI2_Static.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		BE019CDB1E6A187700944820 = {
			isa = PBXGroup;
			children = (
				BEE15019213609BD005FBD5E /* APNsSandboxAuthKey_462WVPCXB7.p8 */,
				BE45E4A6204869EC00945D93 /* APNsAuthKey_462WVPCXB7.p8 */,
				BE45E4A7204869EC00945D93 /* http_fcm_config.json */,
				BE45E4A8204869EC00945D93 /* p256-public-key.spki */,
				BE8A95D61F43B09E00CACD5A /* http_apn_config.json */,
				96A9EAE21F5F0FEB00D2D12E /* cpcapi2_xmpp_agent.config */,
				967D2D471ECD13C700839639 /* cpcapi2_xmpp_agent.cpp */,
				BE6805791E7093B600A87F22 /* settings0.json */,
				BE68057A1E7093B600A87F22 /* silence16.pcm */,
				BE019D1D1E6A1A4000944820 /* Cpcapi2Runner.cpp */,
				BE019D1E1E6A1A4000944820 /* Logger.h */,
				BE019D201E6A1A4000944820 /* SdkManager.cpp */,
				BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */,
				BE019CE61E6A187700944820 /* cpcapi2_conf */,
				BE019CE51E6A187700944820 /* Products */,
				BE019D6C1E6DC15900944820 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		BE019CE51E6A187700944820 /* Products */ = {
			isa = PBXGroup;
			children = (
				BE019CE41E6A187700944820 /* cpcapi2_xmpp_agent */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BE019CE61E6A187700944820 /* cpcapi2_conf */ = {
			isa = PBXGroup;
			children = (
			);
			path = cpcapi2_conf;
			sourceTree = "<group>";
		};
		BE019D6C1E6DC15900944820 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				BE9158132283737500480953 /* librtc_base.a */,
				BE56BAEF21FA3198005475C5 /* IOSurface.framework */,
				96C7C2EA2151B45900AFD58C /* libg722_codecPro.a */,
				96C7C2EE2151B45900AFD58C /* libg729.a */,
				96C7C2ED2151B45900AFD58C /* libg729a.a */,
				96C7C2EC2151B45900AFD58C /* libg729ab.a */,
				96C7C2EB2151B45900AFD58C /* libg729b.a */,
				96C7C2E72151B45400AFD58C /* libamrwb.a */,
				96C7C2E32151B45400AFD58C /* libcodecLib.a */,
				BE5032EF20BDF8940084CF89 /* libsqlite3.a */,
				BE5032ED20BDF8050084CF89 /* libsqlite3.0.dylib */,
				BE5032E620BDEBA80084CF89 /* libsoci_core.a */,
				BE5032EA20BDEBA80084CF89 /* libsoci_sqlite3.a */,
				BEE9AAA020A4E880001F40A0 /* libredisclient.a */,
				BEC9CAE31F72FB1400CAB43C /* libmongocxx.a */,
				BEC9CAE11F72FAEA00CAB43C /* libbsoncxx.a */,
				BE8A95DB1F44962100CACD5A /* version */,
				BE990BF01E6F17BC008DFE98 /* libSRTP.a */,
				BE990BED1E6F172E008DFE98 /* libresiprocate.a */,
				BE990BD11E6E0BE2008DFE98 /* libvqmep.a */,
				BE990BCB1E6E0B68008DFE98 /* libresolv.tbd */,
				BE990BB91E6E09B8008DFE98 /* AppKit.framework */,
				BE990BBA1E6E09B8008DFE98 /* AudioToolbox.framework */,
				BE990BBB1E6E09B8008DFE98 /* CoreAudio.framework */,
				BE990BBC1E6E09B8008DFE98 /* Foundation.framework */,
				BE990BBD1E6E09B8008DFE98 /* IOKit.framework */,
				BE990BBE1E6E09B8008DFE98 /* OpenGL.framework */,
				BE990BBF1E6E09B8008DFE98 /* QuartzCore.framework */,
				BE990BC01E6E09B8008DFE98 /* Security.framework */,
				BE990BC11E6E09B8008DFE98 /* SystemConfiguration.framework */,
				BE990BB31E6E090F008DFE98 /* AVFoundation.framework */,
				BE990BB41E6E090F008DFE98 /* AVKit.framework */,
				BE990BB51E6E090F008DFE98 /* CoreMedia.framework */,
				BE990B331E6E06CE008DFE98 /* libaudio_coding_module.a */,
				BE990B341E6E06CE008DFE98 /* libaudio_conference_mixer.a */,
				BE990B351E6E06CE008DFE98 /* libaudio_decoder_interface.a */,
				BE990B361E6E06CE008DFE98 /* libaudio_device.a */,
				BE990B371E6E06CE008DFE98 /* libaudio_encoder_interface.a */,
				BE990B381E6E06CE008DFE98 /* libaudio_processing_sse2.a */,
				BE990B391E6E06CE008DFE98 /* libaudio_processing.a */,
				BE990B3A1E6E06CE008DFE98 /* libbitrate_controller.a */,
				BE990B3B1E6E06CE008DFE98 /* libCNG.a */,
				BE990B3C1E6E06CE008DFE98 /* libcommon_audio_sse2.a */,
				BE990B3D1E6E06CE008DFE98 /* libcommon_audio.a */,
				BE990B3E1E6E06CE008DFE98 /* libcommon_video.a */,
				BE990B3F1E6E06CE008DFE98 /* libdesktop_capture_differ_sse2.a */,
				BE990B401E6E06CE008DFE98 /* libdesktop_capture.a */,
				BE990B411E6E06CE008DFE98 /* libfield_trial_default.a */,
				BE990B421E6E06CE008DFE98 /* libG711.a */,
				BE990B431E6E06CE008DFE98 /* libG722.a */,
				BE990B441E6E06CE008DFE98 /* libgenperf_libs.a */,
				BE990B451E6E06CE008DFE98 /* libiSAC.a */,
				BE990B461E6E06CE008DFE98 /* libiSACFix.a */,
				BE990B471E6E06CE008DFE98 /* libjpeg_turbo.a */,
				BE990B481E6E06CE008DFE98 /* libmedia_file.a */,
				BE990B491E6E06CE008DFE98 /* libmetrics_default.a */,
				BE990B4A1E6E06CE008DFE98 /* libneteq.a */,
				BE990B4B1E6E06CE008DFE98 /* libopenmax_dl.a */,
				BE990B4C1E6E06CE008DFE98 /* libopus.a */,
				BE990B4D1E6E06CE008DFE98 /* libpaced_sender.a */,
				BE990B4E1E6E06CE008DFE98 /* libPCM16B.a */,
				BE990B4F1E6E06CE008DFE98 /* libred.a */,
				BE990B501E6E06CE008DFE98 /* libremote_bitrate_estimator.a */,
				BE990B511E6E06CE008DFE98 /* librtc_base_approved.a */,
				BE990B521E6E06CE008DFE98 /* librtp_rtcp.a */,
				BE990B531E6E06CE008DFE98 /* libsystem_wrappers.a */,
				BE990B541E6E06CE008DFE98 /* libvideo_capture_module_internal_impl.a */,
				BE990B551E6E06CE008DFE98 /* libvideo_capture_module.a */,
				BE990B561E6E06CE008DFE98 /* libvideo_capture.a */,
				BE990B571E6E06CE008DFE98 /* libvideo_coding_utility.a */,
				BE990B581E6E06CE008DFE98 /* libvideo_engine_core.a */,
				BE990B591E6E06CE008DFE98 /* libvideo_processing_sse2.a */,
				BE990B5A1E6E06CE008DFE98 /* libvideo_processing.a */,
				BE990B5B1E6E06CE008DFE98 /* libvideo_render_module_internal_impl.a */,
				BE990B5C1E6E06CE008DFE98 /* libvideo_render_module.a */,
				BE990B5D1E6E06CE008DFE98 /* libvideo_render.a */,
				BE990B5E1E6E06CE008DFE98 /* libvoice_engine.a */,
				BE990B5F1E6E06CE008DFE98 /* libvpx_intrinsics_avx.a */,
				BE990B601E6E06CE008DFE98 /* libvpx_intrinsics_avx2.a */,
				BE990B611E6E06CE008DFE98 /* libvpx_intrinsics_mmx.a */,
				BE990B621E6E06CE008DFE98 /* libvpx_intrinsics_sse2.a */,
				BE990B631E6E06CE008DFE98 /* libvpx_intrinsics_sse4_1.a */,
				BE990B641E6E06CE008DFE98 /* libvpx_intrinsics_ssse3.a */,
				BE990B651E6E06CE008DFE98 /* libvpx.a */,
				BE990B661E6E06CE008DFE98 /* libwebrtc_AMRWB.a */,
				BE990B671E6E06CE008DFE98 /* libwebrtc_common.a */,
				BE990B681E6E06CE008DFE98 /* libwebrtc_g729.a */,
				BE990B691E6E06CE008DFE98 /* libwebrtc_i420.a */,
				BE990B6A1E6E06CE008DFE98 /* libwebrtc_opus.a */,
				BE990B6B1E6E06CE008DFE98 /* libwebrtc_SILK.a */,
				BE990B6C1E6E06CE008DFE98 /* libwebrtc_speex.a */,
				BE990B6D1E6E06CE008DFE98 /* libwebrtc_utility.a */,
				BE990B6E1E6E06CE008DFE98 /* libwebrtc_video_coding.a */,
				BE990B6F1E6E06CE008DFE98 /* libwebrtc_vp8.a */,
				BE990B701E6E06CE008DFE98 /* libwebrtc_vp9.a */,
				BE990B711E6E06CE008DFE98 /* libwebrtc.a */,
				BE990B721E6E06CE008DFE98 /* libyuv.a */,
				BE990B311E6E0570008DFE98 /* libwebrtc_recon.a */,
				BE990B2F1E6E03AA008DFE98 /* libCPCObjAPI2_OSX.a */,
				BE019D721E6DF58600944820 /* libcurl.a */,
				BE019D6D1E6DC15900944820 /* libcrypto.a */,
				BE019D6E1E6DC15900944820 /* libssl.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		BE019D771E6DFB2600944820 /* Products */ = {
			isa = PBXGroup;
			children = (
				BE019D7E1E6DFB2600944820 /* libCPCAPI2_Static.a */,
				BE019D801E6DFB2600944820 /* libCPCAPI2_UnitTest.a */,
				BE019D821E6DFB2600944820 /* libCPCAPI2_Shared.dylib */,
				BE019D841E6DFB2600944820 /* CPCAPI2.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		BE019CE31E6A187700944820 /* cpcapi2_xmpp_agent */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BE019CEB1E6A187700944820 /* Build configuration list for PBXNativeTarget "cpcapi2_xmpp_agent" */;
			buildPhases = (
				BE019CE01E6A187700944820 /* Sources */,
				BE019CE11E6A187700944820 /* Frameworks */,
				BE019CE21E6A187700944820 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
				BE990B291E6E023B008DFE98 /* PBXTargetDependency */,
			);
			name = cpcapi2_xmpp_agent;
			productName = cpcapi2_conf;
			productReference = BE019CE41E6A187700944820 /* cpcapi2_xmpp_agent */;
			productType = "com.apple.product-type.tool";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		BE019CDC1E6A187700944820 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0820;
				ORGANIZATIONNAME = "Vineet Seth";
				TargetAttributes = {
					BE019CE31E6A187700944820 = {
						CreatedOnToolsVersion = 8.2.1;
						DevelopmentTeam = UQC9N9AMZM;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = BE019CDF1E6A187700944820 /* Build configuration list for PBXProject "cpcapi2_xmpp_agent" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = BE019CDB1E6A187700944820;
			productRefGroup = BE019CE51E6A187700944820 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = BE019D771E6DFB2600944820 /* Products */;
					ProjectRef = BE019D761E6DFB2600944820 /* CPCAPI2_OSX.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				BE019CE31E6A187700944820 /* cpcapi2_xmpp_agent */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		BE019D7E1E6DFB2600944820 /* libCPCAPI2_Static.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_Static.a;
			remoteRef = BE019D7D1E6DFB2600944820 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BE019D801E6DFB2600944820 /* libCPCAPI2_UnitTest.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_UnitTest.a;
			remoteRef = BE019D7F1E6DFB2600944820 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BE019D821E6DFB2600944820 /* libCPCAPI2_Shared.dylib */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libCPCAPI2_Shared.dylib;
			remoteRef = BE019D811E6DFB2600944820 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		BE019D841E6DFB2600944820 /* CPCAPI2.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = CPCAPI2.framework;
			remoteRef = BE019D831E6DFB2600944820 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXSourcesBuildPhase section */
		BE019CE01E6A187700944820 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				BE019D231E6A1A4000944820 /* Cpcapi2Runner.cpp in Sources */,
				BE019D261E6A1A4000944820 /* SdkManager.cpp in Sources */,
				967D2D481ECD13C700839639 /* cpcapi2_xmpp_agent.cpp in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		BE990B291E6E023B008DFE98 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = CPCAPI2_Static;
			targetProxy = BE990B281E6E023B008DFE98 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		BE019CE91E6A187700944820 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					MONGOCXX_STATIC,
					BSONCXX_STATIC,
					MONGOC_STATIC,
					BSON_STATIC,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		BE019CEA1E6A187700944820 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = (
					MONGOCXX_STATIC,
					BSONCXX_STATIC,
					MONGOC_STATIC,
					BSON_STATIC,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = macosx;
			};
			name = Release;
		};
		BE019CEC1E6A187700944820 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEVELOPMENT_TEAM = UQC9N9AMZM;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					RESIP_USE_STL_STREAMS,
					RESIP_APPLE_USE_SYSTEM_TYPES,
					CPCAPI2_USE_BUILT_IN_STRINGCONV,
					CPCAPI2_USE_STD_THREAD,
					CPCAPI2_USE_LICENSING,
					CPCAPI2_INCLUDE_UNRELEASED_HEADERS,
					RESIP_USE_GETIFADDRS,
					BOOST_ALL_NO_LIB,
					BOOST_ASIO_DISABLE_VISIBILITY,
				);
				HEADER_SEARCH_PATHS = (
					../..,
					../../../shared/sipfoundry/main,
					../../../CPCAPI2/interface/public,
					../../../CPCAPI2/interface/experimental,
					../../../external/websocketpp,
					../../../external/boost/include,
					../../../CPCAPI2,
					../../../../osx_libs/x86_64/openssl/debug/include,
					../../../external/rapidjson,
					../../../CPCAPI2/impl/jsonapi,
					../../../CPCAPI2/impl/call/jsonapi,
					../../../CPCAPI2/impl/account/jsonapi,
					../../../CPCAPI2/impl,
					../../../shared/sipfoundry/main/resip/recon,
					../../../shared/sipfoundry/main/reflow,
					../../../shared/libsrtp/srtp/include,
					../../../shared/libsrtp/srtp/crypto/include,
					../../../shared/webrtc_recon,
					../../../shared/WebRTCv/trunk/webrtc/video_engine/include,
					../../../shared/WebRTCv/trunk,
					../../../shared/WebRTCv/trunk/webrtc/voice_engine/include,
					../../../shared/sipfoundry/main/reTurn,
					../../../shared/gloox/src,
					../../../../osx_libs/x86_64/nghttp2/debug/include,
					../../../../osx_libs/x86_64/curl/debug/include,
					../../../../osx_libs/x86_64/curlpp/debug/include,
					../../../../osx_libs/x86_64/curlpp/debug/include/curlpp/internal,
					../../../cpcapi2_xmpp_agent/db/mongodb/mongocxx/osx/include,
					../../../CPCAPI2/impl/remotesync/jsonapi,
					"../../../external/Simple-Web-Server",
					../../../../osx_libs/sqlite3/include,
					../../../external/fmt/include,
					../../../external/spdlog/include,
				);
				LIBRARY_SEARCH_PATHS = (
					../../../../osx_libs/x86_64/openssl/debug/lib,
					../../../../osx_libs/webrtc/Debug,
					"../../../external/vqmon/vqmon/lib/macosx-i386_x86_64/debug",
					../../../../osx_libs/x86_64/curl/debug/lib,
					../../../../osx_libs/x86_64/nghttp2/debug/lib,
					../../../../osx_libs/x86_64/curlpp/debug/lib,
					../../../../osx_libs/x86_64/libxml2/debug/lib,
					../../../../osx_libs/x86_64/xmlsec/debug/lib,
					../../../cpcapi2_xmpp_agent/db/mongodb/mongocxx/osx/lib/bsoncxx,
					../../../cpcapi2_xmpp_agent/db/mongodb/mongocxx/osx/lib/mongocxx,
					../../../../osx_libs/soci/lib/Debug,
					../../../../osx_libs/sqlite3/lib,
					../../../../osx_libs/codecpro/amrwb,
					../../../../osx_libs/codecpro/g729,
					"../../../../osx_libs/vqmon/macosx-i386_x86_64/debug",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = (
					"-lz",
					"-lcrypto",
					"-lssl",
					"-lnghttp2",
					"-lcurl",
					"-lcurlpp",
					"-lxml2",
					"-lxmlsec1",
					"-lxmlsec1-openssl",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		BE019CED1E6A187700944820 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DEVELOPMENT_TEAM = UQC9N9AMZM;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					RESIP_USE_STL_STREAMS,
					RESIP_APPLE_USE_SYSTEM_TYPES,
					CPCAPI2_USE_BUILT_IN_STRINGCONV,
					CPCAPI2_USE_STD_THREAD,
					CPCAPI2_USE_LICENSING,
					CPCAPI2_INCLUDE_UNRELEASED_HEADERS,
					RESIP_USE_GETIFADDRS,
					BOOST_ALL_NO_LIB,
					BOOST_ASIO_DISABLE_VISIBILITY,
				);
				HEADER_SEARCH_PATHS = (
					../..,
					../../../shared/sipfoundry/main,
					../../../CPCAPI2/interface/public,
					../../../CPCAPI2/interface/experimental,
					../../../external/websocketpp,
					../../../external/boost/include,
					../../../CPCAPI2,
					../../../../osx_libs/x86_64/openssl/release/include,
					../../../external/rapidjson,
					../../../CPCAPI2/impl/jsonapi,
					../../../CPCAPI2/impl/call/jsonapi,
					../../../CPCAPI2/impl/account/jsonapi,
					../../../CPCAPI2/impl,
					../../../shared/sipfoundry/main/resip/recon,
					../../../shared/sipfoundry/main/reflow,
					../../../shared/libsrtp/srtp/include,
					../../../shared/libsrtp/srtp/crypto/include,
					../../../shared/webrtc_recon,
					../../../shared/WebRTCv/trunk/webrtc/video_engine/include,
					../../../shared/WebRTCv/trunk,
					../../../shared/WebRTCv/trunk/webrtc/voice_engine/include,
					../../../shared/sipfoundry/main/reTurn,
					../../../shared/gloox/src,
					../../../../osx_libs/x86_64/nghttp2/release/include,
					../../../../osx_libs/x86_64/curl/release/include,
					../../../../osx_libs/x86_64/curlpp/release/include,
					../../../../osx_libs/x86_64/curlpp/release/include/curlpp/internal,
					../../../cpcapi2_xmpp_agent/db/mongodb/mongocxx/osx/include,
					../../../CPCAPI2/impl/remotesync/jsonapi,
					"../../../external/Simple-Web-Server",
					../../../../osx_libs/sqlite3/include,
					../../../external/fmt/include,
					../../../external/spdlog/include,
				);
				LIBRARY_SEARCH_PATHS = (
					../../../../osx_libs/x86_64/openssl/release/lib,
					../../../../osx_libs/x86_64/nghttp2/release/lib,
					../../../../osx_libs/webrtc/Release,
					"../../../external/vqmon/vqmon/lib/macosx-i386_x86_64/release",
					../../../../osx_libs/x86_64/curl/release/lib,
					../../../../osx_libs/x86_64/curlpp/release/lib,
					../../../../osx_libs/x86_64/libxml2/release/lib,
					../../../../osx_libs/x86_64/xmlsec/release/lib,
					../../../../osx_libs/soci/lib/Release,
					../../../../osx_libs/sqlite3/lib,
					"../../../../osx_libs/vqmon/macosx-i386_x86_64/release",
				);
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				OTHER_LDFLAGS = (
					"-lz",
					"-lcrypto",
					"-lssl",
					"-lnghttp2",
					"-lcurl",
					"-lcurlpp",
					"-lxml2",
					"-lxmlsec1",
					"-lxmlsec1-openssl",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		BE019CDF1E6A187700944820 /* Build configuration list for PBXProject "cpcapi2_xmpp_agent" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BE019CE91E6A187700944820 /* Debug */,
				BE019CEA1E6A187700944820 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		BE019CEB1E6A187700944820 /* Build configuration list for PBXNativeTarget "cpcapi2_xmpp_agent" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				BE019CEC1E6A187700944820 /* Debug */,
				BE019CED1E6A187700944820 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = BE019CDC1E6A187700944820 /* Project object */;
}
