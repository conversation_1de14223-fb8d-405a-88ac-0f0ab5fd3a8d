#pragma once

// SDK includes
#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerInternal.h>
#include <phone/PhoneInternal.h>
#include <xmpp/XmppAccountInternal.h>
#include <xmpp_agent/XmppAgentInternal.h>
#include <remotesync/RemoteSyncManagerInternal.h>
#include <push_endpoint/PushNotificationEndpointManagerInternal.h>
#include <servicenotifications_server/ServiceNotificationsTypes.h>
#include "SdkManager.h"

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>


namespace CPCAPI2
{

namespace Agent
{

class Cpcapi2Runner : public CPCAPI2::PhoneHandler,
                      public CPCAPI2::AppRunner
{

public:

   Cpcapi2Runner(
      int sdkThreadPoolThreadIdx,
      resip::MultiReactor* appReactor,
      CPCAPI2::Phone* masterSdkPhone,
      CPCAPI2::JsonApi::JsonApiServer* jsonApiServer,
      CPCAPI2::PushService::PushNotificationServiceManager* pushServiceManager,
      const resip::Data& context);
   virtual ~Cpcapi2Runner();

   resip::Data getContext() const;

   void handleSdkCallback(unsigned int timeoutMs);

   // AppRunner
   virtual int shutdown() OVERRIDE;

   CPCAPI2::Phone* getPhone() const { return mPhone; }
   int getThreadId() const { return mSdkThreadPoolThreadIdx; }

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;

   void addJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, bool isHttp);
   void removeJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser);
   size_t countJsonApiUsers() const;
   size_t countHttpJsonApiUsers() const;
   size_t countWsJsonApiUsers() const;
   void logoutJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser);
   bool isLoggedOut(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const;
   bool isAnyLoggedOut() const;
   bool isDestroyed() const;
   bool isHttp(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const;
   bool isWs(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const;
   void getHttpJsonApiUsers(std::set<CPCAPI2::JsonApi::JsonApiUserHandle>& jsonApiUsers) const;
   void getWsJsonApiUsers(std::set<CPCAPI2::JsonApi::JsonApiUserHandle>& jsonApiUsers) const;
   void setDestroyed();

private:

   void appInit();
   void appShutdown();
   static void createModules(void* context);
   void shutdownImpl();
   void handleSdkCallbackImpl(unsigned int timeoutMs);

private:

   int mSdkThreadPoolThreadIdx;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   XmppAccount::XmppAccountManagerInternal* mXmppAccountManager;
   XmppAgent::XmppAgentManagerInternal* mXmppAgentManager;
   CPCAPI2::RemoteSync::RemoteSyncManagerInternal* mRemoteSyncManager;

   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   CPCAPI2::PushService::PushNotificationServiceManager* mPushNotificationServiceManager;
   CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal* mPushNotificationEndpointManager;
   CPCAPI2::ServiceNotifications::ServiceNotificationsManager* mServiceNotificationsManager;

   std::set<CPCAPI2::JsonApi::JsonApiUserHandle> mWsJsonApiUsers;
   std::set<CPCAPI2::JsonApi::JsonApiUserHandle> mLoggedOutJsonApiUsers;
   std::set<CPCAPI2::JsonApi::JsonApiUserHandle> mHttpJsonApiUsers;
   resip::Data mContext;
   bool mDestroyed;

};

}

}
