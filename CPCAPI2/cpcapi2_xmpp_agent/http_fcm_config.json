{"pushServerAccount": [{"pushServerAccountSettings": {"pushNetworkType": 20, "serverUrl": "https://fcm.googleapis.com/fcm/send", "apiKey": "AAAAl52NiXc:APA91bFmg4ymYoK7oA49AgpT5EOKJkM15RcE2LQ8ik5V1qgbM0FihBwHFciNZpo2eHXkUvXDMP-IrI3l_xHo7KXUd-T2G-ip2jzyfj6-ryIHLV-wgNEBD4l6xppCVdj2puj1xGe8CqvyiqQ-Jw2sWvixXV-_wI2PSA", "verb": 6, "mimeType": "application/json", "username": "", "password": "", "clientCertificate": "", "clientCertificatePasswd": "", "messageBody": "", "messageLengthInBytes": 0, "maxReadRate": 0, "useEmbeddedCert1": false, "useEmbeddedCert2": false, "ignoreCertErrors": false, "enableCookies": false, "cookieFile": "", "verboseLogging": true, "userAgent": "CounterPath-HTTP", "overrideProxyAddress": "", "useHttp2": true}}]}