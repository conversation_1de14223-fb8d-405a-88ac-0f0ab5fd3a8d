#pragma once

#if !defined(CPCAPI2_PUSH_NOTIFICATION_XMPP_AGENT_DATABASE_H)
#define CPCAPI2_PUSH_NOTIFICATION_XMPP_AGENT_DATABASE_H

#include "cpcapi2.h"
#include "SdkManager.h"
#include <interface/experimental/jsonapi/JsonApiClientHandler.h>

#include <bsoncxx/builder/stream/document.hpp>
#include <mongocxx/client.hpp>
#include <mongocxx/instance.hpp>

namespace CPCAPI2
{

namespace XmppAgent
{

class XmppAgentDatabase
{
   
public:
   
   static XmppAgentDatabase* instance();
   virtual~ XmppAgentDatabase();
   
   void init();
   
   // Authorized User Data
   bool addAuthorizedUser(const cpc::string& jsonLoginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle);
   bool addAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle);
   bool updateAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, const CPCAPI2::JsonApi::JsonApiLoginHandle handle);
   bool deleteAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle);
   bool deleteAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext);
   bool getAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle, CPCAPI2::Agent::SdkManager::LoginContext& loginContext);
   bool getAuthorizedUserHandle(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle);
   uint32_t getAllAuthorizedUsers(cpc::vector<CPCAPI2::Agent::SdkManager::LoginContext>& users);
   bool doesAuthorizedUserExist(const CPCAPI2::JsonApi::JsonApiLoginHandle handle);
   bool doesAuthorizedUserExist(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext);
   uint32_t getAuthorizedUserCount();
   
   // Device Data
   bool addDevice(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo, CPCAPI2::PushNotification::PushNotificationDeviceHandle& handle, CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle);
   bool updateDevice(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo, const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle);
   bool deleteDevice(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle);
   bool deleteDevice(const cpc::string& deviceToken);
   bool getDevice(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle, CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo);
   bool getDevicePushRegistrationInfo(const CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle, CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo);
   bool getDeviceHandle(const CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo, CPCAPI2::PushNotification::PushNotificationDeviceHandle& handle);
   bool getDevicePushRegistrationHandle(const CPCAPI2::PushNotification::PushNotificationDeviceHandle& deviceHandle, const CPCAPI2::XmppAccount::XmppAccountHandle& accountHandle, CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle);
   uint32_t getAllDevices(cpc::vector<CPCAPI2::PushNotification::PushNotificationRegistrationInfo>& devices);
   uint32_t getAllDevicePushRegistrations(cpc::vector<CPCAPI2::XmppAgent::XmppPushRegistrationInfo>& pushRegistrations);
   bool doesDeviceExist(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle);
   bool doesDeviceExist(const CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo);
   uint32_t getDeviceCount();
   
   // Account Data
   bool addXmppAccount(const CPCAPI2::XmppAccount::XmppAccountSettings& account, const CPCAPI2::JsonApi::JsonApiLoginHandle userHandle, const CPCAPI2::XmppAccount::XmppAccountHandle& accountHandle);
   bool updateXmppAccount(const CPCAPI2::XmppAccount::XmppAccountSettings& account, const CPCAPI2::JsonApi::JsonApiLoginHandle userHandle, const CPCAPI2::XmppAccount::XmppAccountHandle accountHandle);
   bool deleteXmppAccount(const CPCAPI2::XmppAccount::XmppAccountHandle handle);
   bool deleteAllXmppAccounts(const CPCAPI2::JsonApi::JsonApiLoginHandle handle);
   bool getXmppAccount(const CPCAPI2::XmppAccount::XmppAccountHandle handle, CPCAPI2::XmppAccount::XmppAccountSettings& account);
   bool getXmppAccountHandle(const CPCAPI2::XmppAccount::XmppAccountSettings& account, CPCAPI2::XmppAccount::XmppAccountHandle& handle);
   uint32_t getXmppAccountsForAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle, cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>& accounts);
   uint32_t getAllXmppAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountSettings>& accounts);
   uint32_t getAllXmppAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>& accounts);
   bool doesXmppAccountExist(const CPCAPI2::XmppAccount::XmppAccountHandle handle);
   bool doesXmppAccountExist(const CPCAPI2::XmppAccount::XmppAccountSettings& account);
   uint32_t getXmppAccountCount();
   uint32_t getXmppAccountCount(const CPCAPI2::JsonApi::JsonApiLoginHandle handle);

private:
   
   XmppAgentDatabase();
   
   static XmppAgentDatabase* mInstance;
   
   bool createTables();
   uint32_t getNextAuthorizedUserIndex();
   uint32_t getNextDeviceIndex();
   uint32_t getNextXmppAccountIndex();
   uint32_t getNextXmppPushRegistrationIndex();
   void resetIndexes();
   
   mongocxx::instance mMongo;
   mongocxx::client* mClient;
   mongocxx::database* mDb;
   
   mongocxx::collection* mAuthorizedUsers;
   mongocxx::collection* mDevices;
   mongocxx::collection* mXmppAccounts;
   
   // XMPP Push Registration Mapping Table
   // Each json api user can have multiple xmpp account handles, as the same user can have multiple accounts.
   // Each xmpp-account handle can have multiple xmpp-push-registration handles, as the same account can register from different devices.
   // Each push-device handle can have multiple xmpp-push-registration handles, as the same device can register for different accounts.
   // Each push-device handle will be mapped to a single device token.
   // Each xmpp-push-registration handle will be mapped to a single combination of an xmpp-account handle and a push-device handle.
   mongocxx::collection* mPushRegistration;
   
   /********************************************************/
   /** WARNING: Test functions will destroy the database. **/
   /********************************************************/
   void test();
   void testAuthorizedUser();
   void testDevice();
   void testXmppAccount();

};

}

}

#endif /* CPCAPI2_PUSH_NOTIFICATION_XMPP_AGENT_DATABASE_H */
