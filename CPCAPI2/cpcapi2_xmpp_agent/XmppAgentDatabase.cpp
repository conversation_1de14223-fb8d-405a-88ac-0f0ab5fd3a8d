#include "brand_branded.h"
#include <cpcapi2.h>

#if (CPCAPI2_BRAND_XMPP_AGENT_MODULE == 1)

#include "XmppAgentDatabase.h"
#include "Cpcapi2Runner.h"
#include "phone/PhoneInterface.h"


#include <fstream>
#include <iostream>
#include <exception>

#include <bsoncxx/json.hpp>

#include "Logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

#define AUTHORIZED_USER_ACCOUNT_TABLE "AUTHORIZED_USER_ACCOUNT"
#define DEVICE_TABLE "DEVICE"
#define XMPP_ACCOUNT_TABLE "XMPP_ACCOUNT"
#define XMPP_PUSH_REGISTRATION_TABLE "XMPP_PUSH_REGISTRATION"


namespace CPCAPI2
{

namespace XmppAgent
{

static uint32_t gAuthorizedUserIndex = 0;
static uint32_t gDeviceIndex = 0;
static uint32_t gXmppAccountIndex = 0;
static uint32_t gXmppPushRegistrationIndex = 0;
XmppAgentDatabase* XmppAgentDatabase::mInstance = NULL;
   
XmppAgentDatabase* XmppAgentDatabase::instance()
{
   if (!mInstance)
      mInstance = new XmppAgentDatabase();
   
   return mInstance;
}
   
XmppAgentDatabase::XmppAgentDatabase() :
mClient(NULL),
mDb(NULL),
mAuthorizedUsers(NULL),
mDevices(NULL),
mXmppAccounts(NULL),
mPushRegistration(NULL)
{
}
   
XmppAgentDatabase::~XmppAgentDatabase()
{
}
 
uint32_t XmppAgentDatabase::getNextAuthorizedUserIndex()
{
   gAuthorizedUserIndex++;
   return gAuthorizedUserIndex;
}
  
uint32_t XmppAgentDatabase::getNextDeviceIndex()
{
   gDeviceIndex++;
   return gDeviceIndex;
}
   
uint32_t XmppAgentDatabase::getNextXmppAccountIndex()
{
   gXmppAccountIndex++;
   return gXmppAccountIndex;
}
 
uint32_t XmppAgentDatabase::getNextXmppPushRegistrationIndex()
{
   gXmppPushRegistrationIndex++;
   return gXmppPushRegistrationIndex;
}

void XmppAgentDatabase::resetIndexes()
{
   gAuthorizedUserIndex = 0;
   gDeviceIndex = 0;
   gXmppAccountIndex = 0;
   gXmppPushRegistrationIndex = 0;
}
   
void XmppAgentDatabase::init()
{
   if (mClient)
   {
      ErrLog(<< "XmppAgentDatabase::init(): database already initialized");
      return;
   }
   
   // Enter address here, e.g. "mongodb://********:27017", if db server is not localhost
   mClient = new mongocxx::client(mongocxx::uri());
   
   mDb = new mongocxx::database(mClient->database("xmppagentdb"));
   
   // TODO: Temporary to start testing with an empty database
   mDb->drop();
   // END
   
   createTables();
   
   // TODO: Temporary to test database
   test();
   // END
}
   
bool XmppAgentDatabase::createTables()
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::createTables(): database not initialized");
      return false;
   }
   
   try
   {
      if (mDb->has_collection(AUTHORIZED_USER_ACCOUNT_TABLE))
      {
         DebugLog(<< "XmppAgentDatabase::createTables(): authorized user table already exists in database: " << mDb->name());
         mAuthorizedUsers = new mongocxx::collection(mDb->collection(AUTHORIZED_USER_ACCOUNT_TABLE));
      }
      else
      {
         mAuthorizedUsers = new mongocxx::collection(mDb->create_collection(AUTHORIZED_USER_ACCOUNT_TABLE));
      }
   
      if (mDb->has_collection(DEVICE_TABLE))
      {
         DebugLog(<< "XmppAgentDatabase::createTables(): authorized user table already exists in database: " << mDb->name());
         mDevices = new mongocxx::collection(mDb->collection(DEVICE_TABLE));
         auto index = bsoncxx::builder::stream::document{} << "deviceToken" << 1 << bsoncxx::builder::stream::finalize;
         mDevices->create_index(std::move(index));
      }
      else
      {
         mDevices = new mongocxx::collection(mDb->create_collection(DEVICE_TABLE));
      }
   
      if (mDb->has_collection("XMPP_ACCOUNT"))
      {
         DebugLog(<< "XmppAgentDatabase::createTables(): authorized user table already exists in database: " << mDb->name());
         mXmppAccounts = new mongocxx::collection(mDb->collection(XMPP_ACCOUNT_TABLE));
      }
      else
      {
         mXmppAccounts = new mongocxx::collection(mDb->create_collection(XMPP_ACCOUNT_TABLE));
         auto uriIndex = bsoncxx::builder::stream::document{} << "username" << 1 << "domain" << 1 << bsoncxx::builder::stream::finalize;
         mXmppAccounts->create_index(std::move(uriIndex));
         auto accountIndex = bsoncxx::builder::stream::document{} << "account" << 1 << bsoncxx::builder::stream::finalize;
         mXmppAccounts->create_index(std::move(accountIndex));
         auto authIndex = bsoncxx::builder::stream::document{} << "authId" << 1 << bsoncxx::builder::stream::finalize;
         mXmppAccounts->create_index(std::move(authIndex));
      }
   
      if (mDb->has_collection("XMPP_PUSH_REGISTRATION_TABLE"))
      {
         DebugLog(<< "XmppAgentDatabase::createTables(): device to xmpp account mapping table already exists in database: " << mDb->name());
         mPushRegistration = new mongocxx::collection(mDb->collection(XMPP_PUSH_REGISTRATION_TABLE));
      }
      else
      {
         mPushRegistration = new mongocxx::collection(mDb->create_collection(XMPP_PUSH_REGISTRATION_TABLE));
         auto deviceIndex = bsoncxx::builder::stream::document{} << "deviceId" << 1 << bsoncxx::builder::stream::finalize;
         mDevices->create_index(std::move(deviceIndex));
         auto accountIndex = bsoncxx::builder::stream::document{} << "xmppAccountId" << 1 << bsoncxx::builder::stream::finalize;
         mDevices->create_index(std::move(accountIndex));
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::createTables(): exception: " << e.what());
      return false;
   }
   
   return true;
}


/********************************************************/
/**              Authorized User Schema                **/
/********************************************************/
   
   
// Expected String: {\"authProvider\":\"stretto.com\",\"username\":\"user3\",\"password\":\"user3\"}"}
bool XmppAgentDatabase::addAuthorizedUser(const cpc::string& jsonLoginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle)
{
   handle = 0;
      
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::addAuthorizedUser(): database not initialized");
      return false;
   }
      
   if (jsonLoginContext.size() == 0)
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): incomplete json authorized user data");
      return false;
   }

   bsoncxx::builder::stream::document document{};
   bsoncxx::document::view view = bsoncxx::from_json(jsonLoginContext.c_str()).view();
   document << "_id" << cpc::to_string(getNextAuthorizedUserIndex()).c_str()
      << "authProvider" << view["authProvider"].get_utf8()
      << "username" << view["username"].get_utf8()
      << "password" << view["password"].get_utf8();
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext;
   loginContext.authProvider = view["authProvider"].get_utf8().value.to_string().c_str();
   loginContext.username = view["username"].get_utf8().value.to_string().c_str();
   loginContext.password = view["password"].get_utf8().value.to_string().c_str();
   
   if (doesAuthorizedUserExist(loginContext))
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): authorized user: " << loginContext.username << "@" << loginContext.authProvider << " already exists");
      return false;
   }

   try
   {
      mAuthorizedUsers->insert_one(document.view());
      bsoncxx::document::view view = document.view();
      bsoncxx::document::element element = view["_id"];
      if (element.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::addAuthorizedUser(): id element type is invalid");
         return false;
      }
         
      handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
         
      DebugLog(<< "XmppAgentDatabase::addAuthorizedUser(): successfully added user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): error adding authorized user: " << loginContext.username << "@" << loginContext.authProvider << " exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::addAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle)
{
   handle = 0;
   
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::addAuthorizedUser(): database not initialized");
      return false;
   }
      
   if ((loginContext.authProvider.size() == 0) || (loginContext.username.size() == 0) || (loginContext.password.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): incomplete authorized user data, authProvider: " << loginContext.authProvider << " username: " << loginContext.username << " password empty: " << ((loginContext.password.size() == 0) ? "true" : "false"));
      return false;
   }
   
   if (doesAuthorizedUserExist(loginContext))
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): authorized user: " << loginContext.username << "@" << loginContext.authProvider << " already exists");
      return false;
   }
   
   bsoncxx::builder::stream::document document{};
   document << "_id" << cpc::to_string(getNextAuthorizedUserIndex()).c_str()
      << "authProvider" << loginContext.authProvider.c_str()
      << "username" << loginContext.username.c_str()
      << "password" << loginContext.password.c_str();
   
   try
   {
      mAuthorizedUsers->insert_one(document.view());
      bsoncxx::document::view view = document.view();
      bsoncxx::document::element element = view["_id"];
      if (element.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::addAuthorizedUser(): id element type is invalid");
         return false;
      }
   
      handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
      
      DebugLog(<< "XmppAgentDatabase::addAuthorizedUser(): successfully added user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::addAuthorizedUser(): error adding authorized user: " << loginContext.username << "@" << loginContext.authProvider << " exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::updateAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, const CPCAPI2::JsonApi::JsonApiLoginHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::updateAuthorizedUser(): database not initialized");
      return false;
   }
   
   if ((loginContext.authProvider.size() == 0) || (loginContext.username.size() == 0) || (loginContext.password.size() == 0) || (handle == 0))
   {
      InfoLog(<< "XmppAgentDatabase::updateAuthorizedUser(): incomplete authorized user data, authProvider: " << loginContext.authProvider << " username: " << loginContext.username << " password empty: " << ((loginContext.password.size() == 0) ? "true" : "false") << " handle: " << handle);
      return false;
   }

   if (!doesAuthorizedUserExist(loginContext))
   {
      InfoLog(<< "XmppAgentDatabase::updateAuthorizedUser(): authorized user: " << loginContext.username << "@" << loginContext.authProvider << " does not exist");
      return false;
   }
   
   bsoncxx::builder::stream::document document;
   document << "$set" << bsoncxx::builder::stream::open_document << "authProvider" << loginContext.authProvider.c_str()
      << "username" << loginContext.username.c_str()
      << "password" << loginContext.password.c_str() << bsoncxx::builder::stream::close_document;
      
   try
   {
      bsoncxx::builder::stream::document filter;
      filter << "_id" << cpc::to_string(handle).c_str();
      mAuthorizedUsers->update_one(filter.view(), document.view());
   
      DebugLog(<< "XmppAgentDatabase::updateAuthorizedUser(): successfully updated user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::updateAuthorizedUser(): error updating authorized user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle << " exception: " << e.what());
      return false;
   }
   
   return true;
}

bool XmppAgentDatabase::deleteAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): database not initialized");
      return false;
   }
   
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): invalid handle: " << handle);
      return false;
   }
   
   try
   {
      // TODO: Do we want to delete the xmpp accounts when the auth user gets deleted, or block the
      // deletion of the auth user, until all associated accounts have been deleted by the user.
      if (!deleteAllXmppAccounts(handle))
      {
         InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): error deleting xmpp account associated to authorized user handle: " << handle);
         return false;
      }
      
      bsoncxx::builder::stream::document filter;
      filter << "_id" << cpc::to_string(handle).c_str();
      bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one_and_delete(filter.view());
      if (value)
      {
         DebugLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): successfully deleted authorized user with handle: " << handle);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): no authorized user found with handle: " << handle);
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): error deleting authorized user with handle: " << handle << " exception: " << e.what());
      return false;
   }
   
   return true;
}

   
bool XmppAgentDatabase::deleteAuthorizedUser(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): database not initialized");
      return false;
   }
      
   if ((loginContext.authProvider.size() == 0) || (loginContext.username.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): incomplete authorized user data, authProvider: " << loginContext.authProvider << " username: " << loginContext.username);
      return false;
   }
      
   try
   {
      CPCAPI2::JsonApi::JsonApiLoginHandle handle = 0;
      if ((!getAuthorizedUserHandle(loginContext, handle)) || (handle == 0))
      {
         InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): error retreiving authorized user handle for: " << loginContext.username << "@" << loginContext.authProvider);
         return false;
      }
      
      // TODO: Do we want to delete the xmpp accounts when the auth user gets deleted, or block the
      // deletion of the auth user, until all associated accounts have been deleted by the user.
      if (!deleteAllXmppAccounts(handle))
      {
         InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): error deleting xmpp account associated to authorized user handle: " << handle << " for: " << loginContext.username << "@" << loginContext.authProvider);
         return false;
      }

      bsoncxx::builder::stream::document filter;
      filter << "username" << loginContext.username.c_str() << "authProvider" << loginContext.authProvider.c_str();
      
      bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one_and_delete(filter.view());
      if (value)
      {
         DebugLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): successfully deleted authorized user: " << loginContext.username << "@" << loginContext.authProvider);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): no authorized user: " << loginContext.username << "@" << loginContext.authProvider<< " found");
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteAuthorizedUser(): error deleting authorized user: " << loginContext.username << "@" << loginContext.authProvider<< " exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::getAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle, CPCAPI2::Agent::SdkManager::LoginContext& loginContext)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUser(): database not initialized");
      return false;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "_id" << cpc::to_string(handle).c_str();
   
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getAuthorizedUser(): failed to get user with handle: " << handle);
      return false;
   }
   
   DebugLog(<< "XmppAgentDatabase::getAuthorizedUser(): authorized user table row: " << bsoncxx::to_json(*value));
   bsoncxx::document::view view = *value;
   bsoncxx::document::element authProviderElement = view["authProvider"];
   bsoncxx::document::element usernameElement = view["username"];
   bsoncxx::document::element passwordElement = view["password"];
      
   if (authProviderElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUser(): authProvider element type is invalid");
      return false;
   }
      
   if (usernameElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUser(): username element type is invalid");
      return false;
   }
      
   if (passwordElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUser(): password element type is invalid");
      return false;
   }
      
   loginContext.authProvider = authProviderElement.get_utf8().value.to_string().c_str();
   loginContext.username = usernameElement.get_utf8().value.to_string().c_str();
   loginContext.password = passwordElement.get_utf8().value.to_string().c_str();
   
   DebugLog(<< "XmppAgentDatabase::getAuthorizedUser(): successfully got user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle);
   return true;
}

bool XmppAgentDatabase::getAuthorizedUserHandle(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext, CPCAPI2::JsonApi::JsonApiLoginHandle& handle)
{
   handle = 0;
   
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUserHandle(): database not initialized");
      return false;
   }
   
   if ((loginContext.authProvider.size() == 0) || (loginContext.username.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::getAuthorizedUserHandle(): incomplete authorized user data, authProvider: " << loginContext.authProvider << " username: " << loginContext.username);
      return false;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "username" << loginContext.username.c_str() << "authProvider" << loginContext.authProvider.c_str();
   
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getAuthorizedUserHandle(): authorized user with username: " << loginContext.username << " authProvider: " << loginContext.authProvider << " does not exist");
      return false;
   }
   
   bsoncxx::document::view view = *value;
   bsoncxx::document::element element = view["_id"];
   if (element.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getAuthorizedUserHandle(): id element type is invalid");
      return false;
   }
   
   handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
   DebugLog(<< "XmppAgentDatabase::getAuthorizedUserHandle(): successfully got user: " << loginContext.username << "@" << loginContext.authProvider << " with handle: " << handle);
   
   return true;
}
   
uint32_t XmppAgentDatabase::getAllAuthorizedUsers(cpc::vector<CPCAPI2::Agent::SdkManager::LoginContext>& users)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): database not initialized");
      return false;
   }
      
   auto cursor = mAuthorizedUsers->find({});
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): authorized user table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element authProviderElement = view["authProvider"];
      bsoncxx::document::element usernameElement = view["username"];
      bsoncxx::document::element passwordElement = view["password"];
      
      if (authProviderElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): authProvider element type is invalid");
         continue;
      }
      
      if (usernameElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): username element type is invalid");
         continue;
      }
      
      if (passwordElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): password element type is invalid");
         continue;
      }
      
      CPCAPI2::Agent::SdkManager::LoginContext user;
      user.authProvider = authProviderElement.get_utf8().value.to_string().c_str();
      user.username = usernameElement.get_utf8().value.to_string().c_str();
      user.password = passwordElement.get_utf8().value.to_string().c_str();
      
      users.push_back(user);
   }
      
   DebugLog(<< "XmppAgentDatabase::getAllAuthorizedUsers(): authorized user count: " << users.size() << " database row count: " << mAuthorizedUsers->count({}));
   return (uint32_t)users.size();
}
   
bool XmppAgentDatabase::doesAuthorizedUserExist(const CPCAPI2::JsonApi::JsonApiLoginHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): database not initialized");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "_id" << cpc::to_string(handle).c_str();
   
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one(filter.view());
   if (!value)
   {
      StackLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): authorized user with handle: " << handle << " does not exist");
      return false;
   }
   
   StackLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): found authorized user with handle: " << handle);
   
   return true;
}
   
bool XmppAgentDatabase::doesAuthorizedUserExist(const CPCAPI2::Agent::SdkManager::LoginContext& loginContext)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): database not initialized");
      return false;
   }
   
   if ((loginContext.authProvider.size() == 0) || (loginContext.username.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): incomplete authorized user data, authProvider: " << loginContext.authProvider << " username: " << loginContext.username);
      return false;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "username" << loginContext.username.c_str() << "authProvider" << loginContext.authProvider.c_str();

   bsoncxx::stdx::optional<bsoncxx::document::value> value = mAuthorizedUsers->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): authorized user with username: " << loginContext.username << " authProvider: " << loginContext.authProvider << " does not exist");
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::doesAuthorizedUserExist(): found authorized user with username: " << loginContext.username << " authProvider: " << loginContext.authProvider);
   return true;
}
   
uint32_t XmppAgentDatabase::getAuthorizedUserCount()
{
   return ((uint32_t)mAuthorizedUsers->count({}));
}
 
   
/********************************************************/
/**                   Device Schema                    **/
/********************************************************/

   
bool XmppAgentDatabase::addDevice(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo, CPCAPI2::PushNotification::PushNotificationDeviceHandle& handle, CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle)
{
   handle = 0;
      
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::addDevice(): database not initialized");
      return false;
   }
   
   /*
   if ((regInfo.xmppAccountHandle == 0) || (regInfo.pushNotificationDev == 0) || (regInfo.pushServerUrl.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::addDevice(): incomplete device data, xmpp account: " << regInfo.xmppAccountHandle << " device-handle: " << regInfo.pushNotificationDev << " server-url: " << regInfo.pushServerUrl);
      return false;
   }
 
   if (doesDeviceExist(regInfo.pushRegistrationInfo))
   {
      InfoLog(<< "XmppAgentDatabase::addDevice(): device: " << regInfo.pushRegistrationInfo.deviceToken << " already exists");
      return false;
   }
  
   bsoncxx::builder::stream::document document{};
   document << "_id" << cpc::to_string(getNextDeviceIndex()).c_str()
      << "pushNetworkType" << static_cast<int32_t>(regInfo.pushRegistrationInfo.pushNetworkType)
      << "deviceToken" << regInfo.pushRegistrationInfo.deviceToken.c_str()
      << "apnsTopic" << regInfo.pushRegistrationInfo.apnInfo.apnsTopic.c_str();
   
   try
   {
      mDevices->insert_one(document.view());
      bsoncxx::document::view view = document.view();
      bsoncxx::document::element element = view["_id"];
      if (element.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::addDevice(): id element type is invalid");
         return false;
      }
         
      handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
      
      DebugLog(<< "XmppAgentDatabase::addDevice(): successfully added device: " << regInfo.pushRegistrationInfo.deviceToken << " with device handle: " << handle);
      
      bsoncxx::builder::stream::document pushDocument{};
      pushDocument << "_id" << cpc::to_string(getNextXmppPushRegistrationIndex()).c_str()
         << "xmppAccountId" << static_cast<int32_t>(regInfo.xmppAccountHandle)
         << "deviceId" << static_cast<int32_t>(handle);
      
      try
      {
         mPushRegistration->insert_one(pushDocument.view());
         bsoncxx::document::view pushView = pushDocument.view();
         bsoncxx::document::element pushElement = pushView["_id"];
         if (pushElement.type() != bsoncxx::type::k_utf8)
         {
            ErrLog(<< "XmppAgentDatabase::addDevice(): push id element type is invalid");
            return false;
         }
         
         pushHandle = cpc::to_int(pushElement.get_utf8().value.to_string().c_str());
         
         DebugLog(<< "XmppAgentDatabase::addDevice(): successfully added mapping between account handle: " << regInfo.xmppAccountHandle << " device handle: " << handle << " push handle: " << pushHandle);
         
      }
      catch (std::exception& e)
      {
         InfoLog(<< "XmppAgentDatabase::addDevice(): error adding push registration mapping with exception: " << e.what());
         return false;
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::addDevice(): error adding device with exception: " << e.what());
      return false;
   }
   */
      
   return true;
}
 
bool XmppAgentDatabase::updateDevice(const CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo, const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::updateDevice(): database not initialized");
      return false;
   }

   /*
   if ((regInfo.xmppAccountHandle == 0) || (regInfo.pushRegistrationInfo.deviceToken.size() == 0) || (regInfo.pushRegistrationInfo.apnInfo.apnsTopic.size() == 0)
       || (regInfo.pushRegistrationInfo.pushNetworkType == CPCAPI2::PushNotification::PushNetworkType_Unknown))
   {
      InfoLog(<< "XmppAgentDatabase::updateDevice(): incomplete device data, xmpp account: " << regInfo.xmppAccountHandle << " device type: " << regInfo.pushRegistrationInfo.pushNetworkType << " token: " << regInfo.pushRegistrationInfo.deviceToken << " topic: " << regInfo.pushRegistrationInfo.apnInfo.apnsTopic);
      return false;
   }
   
   if (!doesDeviceExist(regInfo.pushRegistrationInfo))
   {
      InfoLog(<< "XmppAgentDatabase::updateDevice(): device: " << regInfo.pushRegistrationInfo.deviceToken << " does not exist");
      return false;
   }
   
   bsoncxx::builder::stream::document document;
   document << "$set" << bsoncxx::builder::stream::open_document
      << "pushNetworkType" << static_cast<int32_t>(regInfo.pushRegistrationInfo.pushNetworkType)
      << "deviceToken" << regInfo.pushRegistrationInfo.deviceToken.c_str()
      << "apnsTopic" << regInfo.pushRegistrationInfo.apnInfo.apnsTopic.c_str() << bsoncxx::builder::stream::close_document;
   
   try
   {
      bsoncxx::builder::stream::document filter;
      filter << "_id" << cpc::to_string(handle).c_str();
      mDevices->update_one(filter.view(), document.view());
         
      DebugLog(<< "XmppAgentDatabase::updateDevice(): successfully updated device: " << regInfo.pushRegistrationInfo.deviceToken << " with handle: " << handle);
      
      // TODO: any updates required to the mapping table
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::updateDevice(): error updating device: " << regInfo.pushRegistrationInfo.deviceToken << " with handle: " << handle << " exception: " << e.what());
      return false;
   }
   */
      
   return true;
}
   
bool XmppAgentDatabase::deleteDevice(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteDevice(): database not initialized");
      return false;
   }
      
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::deleteDevice(): invalid handle: " << handle);
      return false;
   }
      
   try
   {
      bsoncxx::builder::stream::document pushFilter;
      pushFilter << "deviceId" << static_cast<int32_t>(handle);
      
      DebugLog(<< "XmppAgentDatabase::deleteDevice(): " << mPushRegistration->count(pushFilter.view()) << " push registration mappings with device handle: " << handle);
      
      bsoncxx::stdx::optional<mongocxx::result::delete_result> result = mPushRegistration->delete_many(pushFilter.view());
      DebugLog(<< "XmppAgentDatabase::deleteDevice(): successfully deleted: " << result->deleted_count() << " push registration mappings with device handle: " << handle);
      
      bsoncxx::builder::stream::document filter;
      filter << "_id" << cpc::to_string(handle).c_str();
      bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one_and_delete(filter.view());
      if (value)
      {
         DebugLog(<< "XmppAgentDatabase::deleteDevice(): successfully deleted device with handle: " << handle);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::deleteDevice(): no device found with handle: " << handle);
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteDevice(): error deleting device with handle: " << handle << " exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::deleteDevice(const cpc::string& deviceToken)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteDevice(): database not initialized");
      return false;
   }
   
   if (deviceToken.size() == 0)
   {
      InfoLog(<< "XmppAgentDatabase::deleteDevice(): invalid device token string");
      return false;
   }
      
   try
   {
      bsoncxx::builder::stream::document filter;
      filter << "deviceToken" << deviceToken.c_str();
      
      bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one(filter.view());
      if (value)
      {
         DebugLog(<< "XmppAgentDatabase::deleteDevice(): device found with token: " << deviceToken);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::deleteDevice(): no device found with token: " << deviceToken);
         return true;
      }
      
      bsoncxx::document::view view = *value;
      bsoncxx::document::element element = view["_id"];
      if (element.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::deleteDevice(): id element type is invalid");
         return false;
      }
      
      int handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
      
      bsoncxx::builder::stream::document pushFilter;
      pushFilter << "deviceId" << handle;
      
      DebugLog(<< "XmppAgentDatabase::deleteDevice(): " << mPushRegistration->count(pushFilter.view()) << " push registration mappings with device handle: " << handle);
      
      bsoncxx::stdx::optional<mongocxx::result::delete_result> pushResult = mPushRegistration->delete_many(pushFilter.view());
      DebugLog(<< "XmppAgentDatabase::deleteDevice(): successfully deleted: " << pushResult->deleted_count() << " push registration mappings with device handle: " << handle);
      
      bsoncxx::stdx::optional<mongocxx::result::delete_result> result = mDevices->delete_one(filter.view());
      DebugLog(<< "XmppAgentDatabase::deleteDevice(): successfully deleted: " << result->deleted_count() << " device with token: " << deviceToken);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteDevice(): error deleting device: " << deviceToken << " exception: " << e.what());
      return false;
   }
      
   return true;
}

bool XmppAgentDatabase::getDevice(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle, CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getDevice(): database not initialized");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "_id" << cpc::to_string(handle).c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getDevice(): failed to get device with handle: " << handle);
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::getDevice(): device table row: " << bsoncxx::to_json(*value));
   bsoncxx::document::view view = *value;
   bsoncxx::document::element pushNetworkTypeElement = view["pushNetworkType"];
   bsoncxx::document::element deviceTokenElement = view["deviceToken"];
   bsoncxx::document::element apnsTopicElement = view["apnsTopic"];
      
   if (pushNetworkTypeElement.type() == bsoncxx::type::k_int32)
   {
      // TODO: Add similar validation for alternate network types
      int32_t value = pushNetworkTypeElement.get_int32().value;
      if (value != (uint32_t)CPCAPI2::PushNotification::PushNetworkType_Apple)
      {
         ErrLog(<< "XmppAgentDatabase::getDevice(): pushNetworkType element value: " << value << " does not map to a valid enum type");
         return false;
      }
   }
   else
   {
      ErrLog(<< "XmppAgentDatabase::getDevice(): pushNetworkType element type is invalid");
      return false;
   }
      
   if (deviceTokenElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getDevice(): deviceToken element type is invalid");
      return false;
   }
      
   if (apnsTopicElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getDevice(): apnsTopic element type is invalid");
      return false;
   }
   
   regInfo.pushNetworkType = (CPCAPI2::PushNotification::PushNetworkType)pushNetworkTypeElement.get_int32().value;
   regInfo.deviceToken = deviceTokenElement.get_utf8().value.to_string().c_str();
   regInfo.apnInfo.apnsTopic = apnsTopicElement.get_utf8().value.to_string().c_str();
      
   DebugLog(<< "XmppAgentDatabase::getDevice(): successfully got device: " << regInfo.deviceToken << " with device handle: " << handle);
   return true;
}

bool XmppAgentDatabase::getDevicePushRegistrationInfo(const CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle, CPCAPI2::XmppAgent::XmppPushRegistrationInfo& regInfo)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): database not initialized");
      return false;
   }
   
   /*
   bsoncxx::builder::stream::document filter;
   filter << "_id" << cpc::to_string(pushHandle).c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mPushRegistration->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): failed to get push registration mapping with handle: " << pushHandle);
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): device table row: " << bsoncxx::to_json(*value));
   bsoncxx::document::view view = *value;
   bsoncxx::document::element deviceIdElement = view["deviceId"];
   bsoncxx::document::element xmppAccountIdElement = view["xmppAccountId"];
      
   if (deviceIdElement.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): deviceId element type is invalid");
      return false;
   }
   
   if (xmppAccountIdElement.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): xmppAccountId element type is invalid");
      return false;
   }
   
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle = (CPCAPI2::PushNotification::PushNotificationDeviceHandle)deviceIdElement.get_int32().value;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle = (CPCAPI2::XmppAccount::XmppAccountHandle)xmppAccountIdElement.get_int32().value;
   
   DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): successfully got push registration mapping handle: " << pushHandle << " device handle: " << deviceHandle << " xmpp account handle: " << accountHandle);
   
   if (!getDevice(deviceHandle, regInfo.pushRegistrationInfo))
   {
      DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): error retrieving device information for device handle: " << deviceHandle << " push handle: " << pushHandle << " xmpp account handle: " << accountHandle);
      return false;
   }
   
   regInfo.xmppAccountHandle = xmppAccountIdElement.get_int32().value;
      
   DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): successfully got device: " << regInfo.pushRegistrationInfo.deviceToken << " with device handle: " << deviceHandle << " push handle: " << pushHandle << " xmpp account handle: " << accountHandle);
   */
   
   return true;
}
   
bool XmppAgentDatabase::getDeviceHandle(const CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo, CPCAPI2::PushNotification::PushNotificationDeviceHandle& handle)
{
   handle = 0;
      
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getDeviceHandle(): database not initialized");
      return false;
   }
   
   /*
   if (regInfo.deviceToken.size() == 0)
   {
      InfoLog(<< "XmppAgentDatabase::getDeviceHandle(): invalid device token string");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "deviceToken" << regInfo.deviceToken.c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getDeviceHandle(): device with token: " << regInfo.deviceToken << " does not exist");
      return false;
   }
      
   bsoncxx::document::view view = *value;
   bsoncxx::document::element element = view["_id"];
   if (element.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getDeviceHandle(): id element type is invalid");
      return false;
   }
   
   handle = cpc::to_int(element.get_utf8().value.to_string().c_str());
   DebugLog(<< "XmppAgentDatabase::getDeviceHandle(): successfully got device: " << regInfo.deviceToken << " with handle: " << handle);
   */
   
   return true;
}
 
bool XmppAgentDatabase::getDevicePushRegistrationHandle(const CPCAPI2::PushNotification::PushNotificationDeviceHandle& deviceHandle, const CPCAPI2::XmppAccount::XmppAccountHandle& accountHandle, CPCAPI2::XmppAgent::XmppPushRegistrationHandle& pushHandle)
{
   pushHandle = 0;
      
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationHandle(): database not initialized");
      return false;
   }
      
   if ((deviceHandle == 0) || (accountHandle == 0))
   {
      InfoLog(<< "XmppAgentDatabase::getDevicePushRegistrationHandle(): invalid device handle: " << deviceHandle << " account handle: " << accountHandle);
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "deviceId" << static_cast<int32_t>(deviceHandle) << "xmppAccountId" << static_cast<int32_t>(accountHandle);
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mPushRegistration->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationHandle(): push registration handle for device: " << deviceHandle << " and account: " << accountHandle << " does not exist");
      return false;
   }
      
   bsoncxx::document::view view = *value;
   bsoncxx::document::element element = view["_id"];
   if (element.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationHandle(): id element type is invalid");
      return false;
   }
      
   pushHandle = cpc::to_int(element.get_utf8().value.to_string().c_str());
   DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationHandle(): successfully got push registration handle: " << pushHandle << " for device: " << deviceHandle << " and account: " << accountHandle);
      
   return true;
}
   
uint32_t XmppAgentDatabase::getAllDevices(cpc::vector<CPCAPI2::PushNotification::PushNotificationRegistrationInfo>& devices)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAllDevices(): database not initialized");
      return false;
   }
      
   auto cursor = mDevices->find({});
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getAllDevices(): device table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element pushNetworkTypeElement = view["pushNetworkType"];
      bsoncxx::document::element deviceTokenElement = view["deviceToken"];
      bsoncxx::document::element apnsTopicElement = view["apnsTopic"];
      
      if (pushNetworkTypeElement.type() == bsoncxx::type::k_int32)
      {
         // TODO: Add similar validation for alternate network types
         int32_t value = pushNetworkTypeElement.get_int32().value;
         if (value != (uint32_t)CPCAPI2::PushNotification::PushNetworkType_Apple)
         {
            ErrLog(<< "XmppAgentDatabase::getAllDevices(): pushNetworkType element value: " << value << " does not map to a valid enum type");
            continue;
         }
      }
      else
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevices(): pushNetworkType element type is invalid");
         continue;
      }
         
      if (deviceTokenElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevices(): username element type is invalid");
         continue;
      }
         
      if (apnsTopicElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevices(): password element type is invalid");
         continue;
      }
         
      CPCAPI2::PushNotification::PushNotificationRegistrationInfo device;
      device.pushNetworkType = (CPCAPI2::PushNotification::PushNetworkType)pushNetworkTypeElement.get_int32().value;
      device.deviceToken = deviceTokenElement.get_utf8().value.to_string().c_str();
      device.apnInfo.apnsTopic = apnsTopicElement.get_utf8().value.to_string().c_str();
         
      devices.push_back(device);
   }
   
   DebugLog(<< "XmppAgentDatabase::getAllDevices(): device count: " << devices.size() << " database row count: " << mDevices->count({}));
   return (uint32_t)devices.size();
}
   
uint32_t XmppAgentDatabase::getAllDevicePushRegistrations(cpc::vector<CPCAPI2::XmppAgent::XmppPushRegistrationInfo>& pushRegistrations)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): database not initialized");
      return false;
   }
   
   /*
   auto cursor = mDevices->find({});
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): device table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element idElement = view["_id"];
      bsoncxx::document::element pushNetworkTypeElement = view["pushNetworkType"];
      bsoncxx::document::element deviceTokenElement = view["deviceToken"];
      bsoncxx::document::element apnsTopicElement = view["apnsTopic"];
      
      if (idElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): id element type is invalid");
         continue;
      }
      
      if (pushNetworkTypeElement.type() == bsoncxx::type::k_int32)
      {
         // TODO: Add similar validation for alternate network types
         int32_t value = pushNetworkTypeElement.get_int32().value;
         if (value != (uint32_t)CPCAPI2::PushNotification::PushNetworkType_Apple)
         {
            ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): pushNetworkType element value: " << value << " does not map to a valid enum type");
            continue;
         }
      }
      else
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): pushNetworkType element type is invalid");
         continue;
      }
         
      if (deviceTokenElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): username element type is invalid");
         continue;
      }
         
      if (apnsTopicElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): password element type is invalid");
         continue;
      }
         
      CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushRegistration;
      pushRegistration.pushRegistrationInfo.pushNetworkType = (CPCAPI2::PushNotification::PushNetworkType)pushNetworkTypeElement.get_int32().value;
      pushRegistration.pushRegistrationInfo.deviceToken = deviceTokenElement.get_utf8().value.to_string().c_str();
      pushRegistration.pushRegistrationInfo.apnInfo.apnsTopic = apnsTopicElement.get_utf8().value.to_string().c_str();
      
      CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle = cpc::to_int(idElement.get_utf8().value.to_string().c_str());
      
      bsoncxx::builder::stream::document filter;
      filter << "deviceId" << static_cast<int32_t>(deviceHandle);
      
      auto pushCursor = mPushRegistration->find(filter.view());
      for (auto&& pushView : pushCursor)
      {
         DebugLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): device table row: " << bsoncxx::to_json(pushView));
         bsoncxx::document::element accountElement = pushView["xmppAccountId"];
      
         if (accountElement.type() != bsoncxx::type::k_int32)
         {
            ErrLog(<< "XmppAgentDatabase::getDevicePushRegistrationInfo(): xmppAccountId element type is invalid for device with handle: " << deviceHandle);
            continue;
         }
      
         pushRegistration.xmppAccountHandle = (CPCAPI2::XmppAccount::XmppAccountHandle)accountElement.get_int32().value;
         pushRegistrations.push_back(pushRegistration);
      }
   }
      
   DebugLog(<< "XmppAgentDatabase::getAllDevicePushRegistrations(): push registration count: " << pushRegistrations.size() << " database row count: " << mPushRegistration->count({}));
   */
   return (uint32_t)pushRegistrations.size();
}
   
bool XmppAgentDatabase::doesDeviceExist(const CPCAPI2::PushNotification::PushNotificationDeviceHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesDeviceExist(): database not initialized");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "_id" << cpc::to_string(handle).c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one(filter.view());
   if (!value)
   {
      StackLog(<< "XmppAgentDatabase::doesDeviceExist(): device with handle: " << handle << " does not exist");
      return false;
   }
      
   StackLog(<< "XmppAgentDatabase::doesDeviceExist(): found device with handle: " << handle);
      
   return true;
}
   
bool XmppAgentDatabase::doesDeviceExist(const CPCAPI2::PushNotification::PushNotificationRegistrationInfo& regInfo)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesDeviceExist(): database not initialized");
      return false;
   }
      
   if (regInfo.deviceToken.size() == 0)
   {
      InfoLog(<< "XmppAgentDatabase::doesDeviceExist(): invalid device token string");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "deviceToken" << regInfo.deviceToken.c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mDevices->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::doesDeviceExist(): device with token: " << regInfo.deviceToken << " does not exist");
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::doesDeviceExist(): found device with token: " << regInfo.deviceToken);
   return true;
}
   
uint32_t XmppAgentDatabase::getDeviceCount()
{
   return ((uint32_t)mDevices->count({}));
}
   
   
/********************************************************/
/**                XMPP Account Schema                 **/
/********************************************************/
/*
    cpc::string                           username;
    cpc::string                           domain;
    cpc::string                           password;
    cpc::string                           proxy;
    unsigned int                          port;
    cpc::string                           resource;
    unsigned int                          priority;
    cpc::string                           softwareName;
    cpc::string                           softwareVersion;
    cpc::string                           softwareOS;
    cpc::string                           identityCategory;
    cpc::string                           identityType;
    unsigned int                          connectTimeOut;
    unsigned int                          keepAliveTime;
    bool                                  usePingKeepAlive;
    cpc::vector<cpc::string>              fileTransfileProxies;
    bool                                  enableLocalSocks5Proxy;
    bool                                  enableRemoteStreamHostDiscovery;
    SSLVersion                            sslVersion;
    bool                                  ignoreCertVerification;
    cpc::vector<cpc::string>              additionalCertPeerNames;
    cpc::vector<cpc::string>              acceptedCertPublicKeys;
    cpc::vector<cpc::string>              requiredCertPublicKeys;
    bool                                  logXmppStanzas;
    IpVersion                             ipVersion;
    cpc::vector<cpc::string>              nameServers;
    cpc::vector<cpc::string>              additionalNameServers;
    bool                                  enableStreamManagement;
    bool                                  enableStreamResumption; // relies on enableStreamManagement to be true
    cpc::string                           streamManagementId;
    int                                   streamManagementSequence;
    bool                                  publishInitialPresenceAsAvailable;
    
    XmppAccountHandle accountHandle;
    JSONParser::assignUint(accountHandle, functionObjectVal, "account");
    XmppAccountSettings acctSettings;
    JSONParser::assignString(acctSettings.domain, functionObjectVal, "domain");
    JSONParser::assignString(acctSettings.username, functionObjectVal, "username");
    JSONParser::assignString(acctSettings.password, functionObjectVal, "password");
    JSONParser::assignBool(acctSettings.ignoreCertVerification, functionObjectVal, "ignoreCertVerification");
    JSONParser::assignString(acctSettings.proxy, functionObjectVal, "proxy");
    JSONParser::assignString(acctSettings.resource, functionObjectVal, "resource");
    JSONParser::assignUint(acctSettings.connectTimeOut, functionObjectVal, "connectTimeOut");
*/
   
   
// TODO: Should account handle be generated on server, or provided by the client as is currently done
bool XmppAgentDatabase::addXmppAccount(const CPCAPI2::XmppAccount::XmppAccountSettings& account, const CPCAPI2::JsonApi::JsonApiLoginHandle userHandle, const CPCAPI2::XmppAccount::XmppAccountHandle& accountHandle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::addXmppAccount(): database not initialized");
      return false;
   }
   
   if ((accountHandle == 0) || (account.username.size() == 0) || (account.domain.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::addXmppAccount(): incomplete account data, xmpp account: " << accountHandle << " username: " << account.username << " domain: " << account.domain);
      return false;
   }
      
   if (doesXmppAccountExist(accountHandle))
   {
      InfoLog(<< "XmppAgentDatabase::addXmppAccount(): account with handle: " << accountHandle << " already exists");
      return false;
   }
   
   if (doesXmppAccountExist(account))
   {
      InfoLog(<< "XmppAgentDatabase::addXmppAccount(): account with xmpp uri: " << account.username << "@" << account.domain << " already exists");
      return false;
   }

   if (!doesAuthorizedUserExist(userHandle))
   {
      InfoLog(<< "XmppAgentDatabase::addXmppAccount(): authorized user: " << userHandle << " does not exist");
      return false;
   }
   
   // TODO: Should we allow mapping an account to multiple auth users
   bsoncxx::builder::stream::document document{};
   document << "_id" << cpc::to_string(getNextXmppAccountIndex()).c_str()
      << "account" << static_cast<int32_t>(accountHandle)
      << "authId" << static_cast<int32_t>(userHandle)
      << "username" << account.username.c_str()
      << "domain" << account.domain.c_str()
      << "password" << account.password.c_str()
      << "ignoreCertVerification" << account.ignoreCertVerification
      << "proxy" << account.proxy.c_str()
      << "resource" << account.resource.c_str()
      << "connectTimeOut" << static_cast<int32_t>(account.connectTimeOut);
   
   try
   {
      mXmppAccounts->insert_one(document.view());
      bsoncxx::document::view view = document.view();
      bsoncxx::document::element element = view["_id"];
      if (element.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::addXmppAccount(): id element type is invalid");
         return false;
      }
         
      int dbAccountHandle = cpc::to_int(element.get_utf8().value.to_string().c_str());
      
      DebugLog(<< "XmppAgentDatabase::addXmppAccount(): successfully added account: " << account.username << "@" << account.domain << " with account handle: " << accountHandle << " db account index: " << dbAccountHandle);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::addXmppAccount(): error adding device with exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::updateXmppAccount(const CPCAPI2::XmppAccount::XmppAccountSettings& account, const CPCAPI2::JsonApi::JsonApiLoginHandle userHandle, const CPCAPI2::XmppAccount::XmppAccountHandle accountHandle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::updateXmppAccount(): database not initialized");
      return false;
   }
   
   if ((accountHandle == 0) || (account.username.size() == 0) || (account.domain.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::updateXmppAccount(): incomplete account data, xmpp account: " << accountHandle << " username: " << account.username << " domain: " << account.domain);
      return false;
   }
   
   if (!doesXmppAccountExist(accountHandle))
   {
      InfoLog(<< "XmppAgentDatabase::updateXmppAccount(): account with handle: " << accountHandle << " does not exist");
      return false;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "account" << static_cast<int32_t>(accountHandle);
   
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::updateXmppAccount(): error retrieving data for account with handle: " << accountHandle);
      return false;
   }
   
   DebugLog(<< "XmppAgentDatabase::updateXmppAccount(): xmpp account table row: " << bsoncxx::to_json(*value));
   bsoncxx::document::view view = *value;
   bsoncxx::document::element authIdElement = view["authId"];
   
   if (authIdElement.type() == bsoncxx::type::k_int32)
   {
      CPCAPI2::JsonApi::JsonApiLoginHandle currentAuthId = authIdElement.get_int32().value;
      if (currentAuthId != userHandle)
      {
         ErrLog(<< "XmppAgentDatabase::updateXmppAccount(): cannot change authenticated users after account creation: currentAuthId: " << currentAuthId << " userHandle: " << userHandle);
         return false;
      }
   }
   else
   {
      ErrLog(<< "XmppAgentDatabase::updateXmppAccount(): authId element type is invalid");
      return false;
   }
   
   bsoncxx::builder::stream::document document{};
   document << "$set" << bsoncxx::builder::stream::open_document
      << "account" << static_cast<int32_t>(accountHandle)
      << "authId" << static_cast<int32_t>(userHandle)
      << "username" << account.username.c_str()
      << "domain" << account.domain.c_str()
      << "password" << account.password.c_str()
      << "ignoreCertVerification" << account.ignoreCertVerification
      << "proxy" << account.proxy.c_str()
      << "resource" << account.resource.c_str()
      << "connectTimeOut" << static_cast<int32_t>(account.connectTimeOut) << bsoncxx::builder::stream::close_document;
      
   try
   {
      mXmppAccounts->update_one(filter.view(), document.view());
         
      DebugLog(<< "XmppAgentDatabase::updateXmppAccount(): successfully updated account with handle: " << accountHandle);
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::updateXmppAccount(): error updating account with handle: " << accountHandle << " exception: " << e.what());
      return false;
   }
   
   return true;
}
   
bool XmppAgentDatabase::deleteXmppAccount(const CPCAPI2::XmppAccount::XmppAccountHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteXmppAccount(): database not initialized");
      return false;
   }
      
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::deleteXmppAccount(): invalid xmpp account handle: " << handle);
      return false;
   }
      
   try
   {
      bsoncxx::builder::stream::document pushFilter;
      pushFilter << "xmppAccountId" << static_cast<int32_t>(handle);
      
      DebugLog(<< "XmppAgentDatabase::deleteXmppAccount(): " << mPushRegistration->count(pushFilter.view()) << " push registration mappings with xmpp account handle: " << handle);
         
      bsoncxx::stdx::optional<mongocxx::result::delete_result> result = mPushRegistration->delete_many(pushFilter.view());
      DebugLog(<< "XmppAgentDatabase::deleteXmppAccount(): successfully deleted: " << result->deleted_count() << " push registration mappings with xmpp account handle: " << handle);
         
      bsoncxx::builder::stream::document filter;
      filter << "account" << static_cast<int32_t>(handle);
      bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one_and_delete(filter.view());
      if (value)
      {
         DebugLog(<< "XmppAgentDatabase::deleteXmppAccount(): successfully deleted xmpp account with handle: " << handle);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::deleteXmppAccount(): no xmmp account found with handle: " << handle);
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteXmppAccount(): error deleting xmpp account with handle: " << handle << " exception: " << e.what());
      return false;
   }
      
   return true;
}

bool XmppAgentDatabase::deleteAllXmppAccounts(const CPCAPI2::JsonApi::JsonApiLoginHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): database not initialized");
      return false;
   }
      
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): invalid auth handle: " << handle);
      return false;
   }
      
   try
   {
      cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle> accounts;
      if (getXmppAccountsForAuthorizedUser(handle, accounts) == 0)
      {
         DebugLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): no accounts associated to auth handle: " << handle);
      }
      else
      {
         cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>::iterator i;
         for (i = accounts.begin(); i != accounts.end(); i++)
         {
            bsoncxx::builder::stream::document pushFilter;
            pushFilter << "xmppAccountId" << static_cast<int32_t>(*i);
      
            DebugLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): " << mPushRegistration->count(pushFilter.view()) << " push registration mappings with xmpp account handle: " << (*i));
         
            bsoncxx::stdx::optional<mongocxx::result::delete_result> push_delete_result = mPushRegistration->delete_many(pushFilter.view());
            DebugLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): successfully deleted: " << push_delete_result->deleted_count() << " push registration mappings with xmpp account handle: " << (*i));
         }
         
         bsoncxx::builder::stream::document filter;
         filter << "authId" << static_cast<int32_t>(handle);
         bsoncxx::stdx::optional<mongocxx::result::delete_result> account_delete_result = mXmppAccounts->delete_many(filter.view());
         DebugLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): successfully deleted: " << account_delete_result->deleted_count() << " accounts associated with auth user: " << handle);
      }
   }
   catch (std::exception& e)
   {
      InfoLog(<< "XmppAgentDatabase::deleteAllXmppAccounts(): error deleting xmpp account with handle: " << handle << " exception: " << e.what());
      return false;
   }
      
   return true;
}
   
bool XmppAgentDatabase::getXmppAccount(const CPCAPI2::XmppAccount::XmppAccountHandle handle, CPCAPI2::XmppAccount::XmppAccountSettings& account)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): database not initialized");
      return false;
   }
   
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::getXmppAccount(): invalid xmpp account handle: " << handle);
      return false;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "account" << static_cast<int32_t>(handle);
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getXmppAccount(): failed to get xmpp account with handle: " << handle);
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::getXmppAccount(): xmpp account table row: " << bsoncxx::to_json(*value));
   bsoncxx::document::view view = *value;
   bsoncxx::document::element accountElement = view["account"];
   bsoncxx::document::element authIdElement = view["authId"];
   bsoncxx::document::element usernameElement = view["username"];
   bsoncxx::document::element domainElement = view["domain"];
   bsoncxx::document::element passwordElement = view["password"];
   bsoncxx::document::element ignoreCertVerificationElement = view["ignoreCertVerification"];
   bsoncxx::document::element proxyElement = view["proxy"];
   bsoncxx::document::element resourceElement = view["resource"];
   bsoncxx::document::element connectTimeOutElement = view["connectTimeOut"];
   
   if (accountElement.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): account element type is invalid");
      return false;
   }
      
   if (authIdElement.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): authId element type is invalid");
      return false;
   }
      
   if (usernameElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): username element type is invalid");
      return false;
   }
   
   if (domainElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): domain element type is invalid");
      return false;
   }
   
   if (passwordElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): password element type is invalid");
      return false;
   }
   
   if (ignoreCertVerificationElement.type() != bsoncxx::type::k_bool)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): ignoreCertVerification element type is invalid");
      return false;
   }
   
   if (proxyElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): proxy element type is invalid");
      return false;
   }
   
   if (resourceElement.type() != bsoncxx::type::k_utf8)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): resource element type is invalid");
      return false;
   }
   
   if (connectTimeOutElement.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccount(): connectTimeOut element type is invalid");
      return false;
   }
   
   account.username = usernameElement.get_utf8().value.to_string().c_str();
   account.domain = domainElement.get_utf8().value.to_string().c_str();
   account.password = passwordElement.get_utf8().value.to_string().c_str();
   account.ignoreCertVerification = ignoreCertVerificationElement.get_bool().value;
   account.proxy = proxyElement.get_utf8().value.to_string().c_str();
   account.resource = resourceElement.get_utf8().value.to_string().c_str();
   account.connectTimeOut = connectTimeOutElement.get_int32().value;
      
   DebugLog(<< "XmppAgentDatabase::getXmppAccount(): successfully got xmpp account with handle: " << handle);
   return true;
}
   
bool XmppAgentDatabase::getXmppAccountHandle(const CPCAPI2::XmppAccount::XmppAccountSettings& account, CPCAPI2::XmppAccount::XmppAccountHandle& handle)
{
   handle = 0;
      
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccountHandle(): database not initialized");
      return false;
   }
      
   if ((account.username.size() == 0) || (account.domain.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::getXmppAccountHandle(): invalid account data, username: " << account.username << " domain: " << account.domain);
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "username" << account.username.c_str() << "domain" << account.domain.c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::getXmppAccountHandle(): xmpp account for: " << account.username << "@" << account.domain << " does not exist");
      return false;
   }
      
   bsoncxx::document::view view = *value;
   bsoncxx::document::element element = view["account"];
   if (element.type() != bsoncxx::type::k_int32)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccountHandle(): xmpp account element type is invalid");
      return false;
   }
      
   handle = element.get_int32().value;
   DebugLog(<< "XmppAgentDatabase::getXmppAccountHandle(): successfully got xmpp account handle: " << handle << " for: " << account.username << "@" << account.domain);
      
   return true;
}
   
uint32_t XmppAgentDatabase::getXmppAccountsForAuthorizedUser(const CPCAPI2::JsonApi::JsonApiLoginHandle handle, cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>& accounts)
{
   accounts.clear();
   
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getXmppAccountsForAuthorizedUser(): database not initialized");
      return 0;
   }
   
   if (handle == 0)
   {
      InfoLog(<< "XmppAgentDatabase::getXmppAccountsForAuthorizedUser(): invalid auth handle: " << handle);
      return 0;
   }
   
   bsoncxx::builder::stream::document filter;
   filter << "authId" << static_cast<int32_t>(handle);
   
   auto cursor = mXmppAccounts->find(filter.view());
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getXmppAccountsForAuthorizedUser(): xmpp account table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element accountElement = view["account"];
      
      if (accountElement.type() != bsoncxx::type::k_int32)
      {
         ErrLog(<< "XmppAgentDatabase::getXmppAccountsForAuthorizedUser(): account element type is invalid");
         continue;
      }
   
      CPCAPI2::XmppAccount::XmppAccountHandle account = accountElement.get_int32().value;
      
      accounts.push_back(account);
   }
      
   DebugLog(<< "XmppAgentDatabase::getXmppAccountsForAuthorizedUser(): xmpp account count: " << accounts.size() << " for auth user: " << handle);
   return (uint32_t)accounts.size();
}

uint32_t XmppAgentDatabase::getAllXmppAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle>& accounts)
{
   accounts.clear();
   
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): database not initialized");
      return 0;
   }
   
   auto cursor = mXmppAccounts->find({});
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getAllXmppAccounts(): xmpp account table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element accountElement = view["account"];
         
      if (accountElement.type() != bsoncxx::type::k_int32)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): account element type is invalid");
         continue;
      }
         
      CPCAPI2::XmppAccount::XmppAccountHandle account = accountElement.get_int32().value;
         
      accounts.push_back(account);
   }
      
   DebugLog(<< "XmppAgentDatabase::getAllXmppAccounts(): xmpp account count: " << accounts.size() << " database row count: " << mXmppAccounts->count({}));
   return (uint32_t)accounts.size();
}
   
uint32_t XmppAgentDatabase::getAllXmppAccounts(cpc::vector<CPCAPI2::XmppAccount::XmppAccountSettings>& accounts)
{
   accounts.clear();
   
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): database not initialized");
      return 0;
   }
   
   auto cursor = mXmppAccounts->find({});
   for (auto&& view : cursor)
   {
      DebugLog(<< "XmppAgentDatabase::getAllXmppAccounts(): xmpp account table row: " << bsoncxx::to_json(view));
      bsoncxx::document::element accountElement = view["account"];
      bsoncxx::document::element authIdElement = view["authId"];
      bsoncxx::document::element usernameElement = view["username"];
      bsoncxx::document::element domainElement = view["domain"];
      bsoncxx::document::element passwordElement = view["password"];
      bsoncxx::document::element ignoreCertVerificationElement = view["ignoreCertVerification"];
      bsoncxx::document::element proxyElement = view["proxy"];
      bsoncxx::document::element resourceElement = view["resource"];
      bsoncxx::document::element connectTimeOutElement = view["connectTimeOut"];
         
      if (accountElement.type() != bsoncxx::type::k_int32)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): account element type is invalid");
         return false;
      }
         
      if (authIdElement.type() != bsoncxx::type::k_int32)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): authId element type is invalid");
         return false;
      }
         
      if (usernameElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): username element type is invalid");
         return false;
      }
         
      if (domainElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): domain element type is invalid");
         return false;
      }
         
      if (passwordElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): password element type is invalid");
         return false;
      }
         
      if (ignoreCertVerificationElement.type() != bsoncxx::type::k_bool)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): ignoreCertVerification element type is invalid");
         return false;
      }
         
      if (proxyElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): proxy element type is invalid");
         return false;
      }
         
      if (resourceElement.type() != bsoncxx::type::k_utf8)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): resource element type is invalid");
         return false;
      }
         
      if (connectTimeOutElement.type() != bsoncxx::type::k_int32)
      {
         ErrLog(<< "XmppAgentDatabase::getAllXmppAccounts(): connectTimeOut element type is invalid");
         return false;
      }
         
      CPCAPI2::XmppAccount::XmppAccountSettings account;
      account.username = usernameElement.get_utf8().value.to_string().c_str();
      account.domain = domainElement.get_utf8().value.to_string().c_str();
      account.password = passwordElement.get_utf8().value.to_string().c_str();
      account.ignoreCertVerification = ignoreCertVerificationElement.get_bool().value;
      account.proxy = proxyElement.get_utf8().value.to_string().c_str();
      account.resource = resourceElement.get_utf8().value.to_string().c_str();
      account.connectTimeOut = connectTimeOutElement.get_int32().value;
         
      accounts.push_back(account);
   }
      
   DebugLog(<< "XmppAgentDatabase::getAllXmppAccounts(): xmpp account count: " << accounts.size() << " database row count: " << mXmppAccounts->count({}));
   return (uint32_t)accounts.size();
}
   
bool XmppAgentDatabase::doesXmppAccountExist(const CPCAPI2::XmppAccount::XmppAccountHandle handle)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesXmppAccountExist(): database not initialized");
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "account" << static_cast<int32_t>(handle);
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one(filter.view());
   if (!value)
   {
      StackLog(<< "XmppAgentDatabase::doesXmppAccountExist(): xmpp account with handle: " << handle << " does not exist");
      return false;
   }
      
   StackLog(<< "XmppAgentDatabase::doesXmppAccountExist(): found xmpp account with handle: " << handle);
      
   return true;
}
   
bool XmppAgentDatabase::doesXmppAccountExist(const CPCAPI2::XmppAccount::XmppAccountSettings& account)
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::doesXmppAccountExist(): database not initialized");
      return false;
   }
   
   if ((account.username.size() == 0) || (account.domain.size() == 0))
   {
      InfoLog(<< "XmppAgentDatabase::doesXmppAccountExist(): invalid account data, username: " << account.username << " domain: " << account.domain);
      return false;
   }
      
   bsoncxx::builder::stream::document filter;
   filter << "username" << account.username.c_str() << "domain" << account.domain.c_str();
      
   bsoncxx::stdx::optional<bsoncxx::document::value> value = mXmppAccounts->find_one(filter.view());
   if (!value)
   {
      DebugLog(<< "XmppAgentDatabase::doesXmppAccountExist(): xmpp account does not exist for: " << account.username << "@" << account.domain);
      return false;
   }
      
   DebugLog(<< "XmppAgentDatabase::doesXmppAccountExist(): found xmpp account for: " << account.username << "@" << account.domain);
   return true;
}
   
uint32_t XmppAgentDatabase::getXmppAccountCount()
{
   return ((uint32_t)mXmppAccounts->count({}));
}
   
uint32_t XmppAgentDatabase::getXmppAccountCount(const CPCAPI2::JsonApi::JsonApiLoginHandle handle)
{
   bsoncxx::builder::stream::document filter;
   filter << "authId" << static_cast<int32_t>(handle);
   return ((uint32_t)mXmppAccounts->count({filter.view()}));
}

   
/********************************************************/
/** WARNING: Test functions will destroy the database. **/
/********************************************************/

   
void XmppAgentDatabase::test()
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::test(): database not initialized");
      return;
   }
      
   testAuthorizedUser();
   testDevice();
   testXmppAccount();
}
   
void XmppAgentDatabase::testAuthorizedUser()
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: database not initialized");
      return;
   }
   
   InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): Database: " << mDb->name() << " has the following tables: "
           << " AUTHORIZED_USER_ACCOUNT: " << (mDb->has_collection(AUTHORIZED_USER_ACCOUNT_TABLE) ? "yes" : "no")
           << " DEVICE: " << (mDb->has_collection(DEVICE_TABLE) ? "yes" : "no")
           << " XMPP_ACCOUNT: " << (mDb->has_collection(XMPP_ACCOUNT_TABLE) ? "yes" : "no")
           << " XMPP_PUSH_REGISTRATION: " << (mDb->has_collection(XMPP_PUSH_REGISTRATION_TABLE) ? "yes" : "no"));
   
   if (mDb->has_collection(AUTHORIZED_USER_ACCOUNT_TABLE))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user table found in: " << mDb->name() << " with row count: " << mAuthorizedUsers->count({}));
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: authorized user table not found in: " << mDb->name());
      return;
   }
   
   resetIndexes();
   mPushRegistration->delete_many({});
   mXmppAccounts->delete_many({});
   mDevices->delete_many({});
   mAuthorizedUsers->delete_many({});
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext1;
   loginContext1.authProvider = "stretto.com";
   loginContext1.username = "user1";
   loginContext1.password = "user1";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext2;
   loginContext2.authProvider = "stretto.com";
   loginContext2.username = "user2";
   loginContext2.password = "user2";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext3;
   loginContext3.authProvider = "stretto.com";
   loginContext3.username = "user3";
   loginContext3.password = "user3";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext1Updated = loginContext1;
   loginContext1Updated.password = "UPDATED";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContextInvalid;
   loginContextInvalid.authProvider = "invalid";
   loginContextInvalid.username = "invalid";
   loginContextInvalid.password = "invalid";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext1Empty;
   CPCAPI2::JsonApi::JsonApiLoginHandle handle1 = 0;
   CPCAPI2::JsonApi::JsonApiLoginHandle handle2 = 0;
   CPCAPI2::JsonApi::JsonApiLoginHandle handle3 = 0;
   CPCAPI2::JsonApi::JsonApiLoginHandle handleInvalid = 99;
   
   // Network String: {"functionName":"login","context":"{\"authProvider\":\"stretto.com\",\"username\":\"user3\",\"password\":\"user3\"}"}}
   // Expected String: {\"authProvider\":\"stretto.com\",\"username\":\"user3\",\"password\":\"user3\"}"}
   cpc::string jsonLoginContext3 = "{\"authProvider\":\"stretto.com\",\"username\":\"user3\",\"password\":\"user3\"}";
   
   if (addAuthorizedUser(loginContext1, handle1))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user: " << loginContext1.username << "@" << loginContext1.authProvider << " successfully added with handle: " << handle1);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when adding authorized user: " << loginContext1.username << "@" << loginContext1.authProvider);
      return;
   }
   
   if (addAuthorizedUser(loginContext2, handle2))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user: " << loginContext2.username << "@" << loginContext2.authProvider << " successfully added with handle: " << handle2);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when adding authorized user: " << loginContext2.username << "@" << loginContext2.authProvider);
      return;
   }
   
   if (addAuthorizedUser(jsonLoginContext3, handle3))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): successful when adding authorized user using json: " << jsonLoginContext3 << " with handle: " << handle3);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when adding authorized user using json: " << jsonLoginContext3);
      return;
   }
   
   auto cursor1 = mAuthorizedUsers->find({});
   
   for (auto&& doc : cursor1)
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user table row: " << bsoncxx::to_json(doc));
   }
   
   if (updateAuthorizedUser(loginContext1Updated, handle1))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user with handle: " << handle1 << " successfully updated");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when updating authorized user with handle: " << handle1);
      return;
   }
   
   auto cursor2 = mAuthorizedUsers->find({});
   
   for (auto&& doc : cursor2)
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user table row: " << bsoncxx::to_json(doc));
   }
   
   DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user table has a row count: " << getAuthorizedUserCount());
   
   if (getAuthorizedUser(handle1, loginContext1Empty))
   {
      bool authValid = (loginContext1Empty.authProvider == loginContext1Updated.authProvider);
      bool userValid = (loginContext1Empty.username == loginContext1Updated.username);
      bool passwordValid = (loginContext1Empty.password == loginContext1Updated.password);
      DebugLog(<< "XmppAgentDatabase::testDevice(): device info retrieved successfully: device token: "
               << " authProvider: " << loginContext1Empty.authProvider << " valid: " << (authValid ? "yes" : "no")
               << " username: " << loginContext1Empty.username << " valid: " << (userValid ? "yes" : "no")
               << " password valid: " << (passwordValid ? "yes" : "no")
               << " for handle: " << handle1);
      
      if (!(authValid && userValid && passwordValid))
      {
         DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: data mismatch in authorized user info retrieved");
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when retrieving authorized user with handle: " << handle1);
      return;
   }
   
   CPCAPI2::JsonApi::JsonApiLoginHandle handle = 0;
   if (getAuthorizedUserHandle(loginContext2, handle))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): handle: " << handle << " valid: " << ((handle == handle2) ? "yes" : "no") << ", for authorized user: " << loginContext2.username << "@" << loginContext2.authProvider << " retrieved successfully");
      
      if (handle != handle2)
      {
         DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: data mismatch in authorized user info retrieved");
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure retrieving handle for authorized user: " << loginContext2.username << "@" << loginContext2.authProvider);
      return;
   }
   
   cpc::vector<CPCAPI2::Agent::SdkManager::LoginContext> users;
   uint32_t count = getAllAuthorizedUsers(users);
   
   if (count == 3)
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): successfully retrieved all authorized users, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure when retrieving all authorized users, count: " << count);
      return;
   }
   
   if (doesAuthorizedUserExist(handle1))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user with handle: " << handle1 << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: authorized user with handle: " << handle1 << " does not exist");
      return;
   }
   
   if (doesAuthorizedUserExist(handleInvalid))
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: authorized user with invalid handle: " << handleInvalid << " exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user with invalid handle: " << handleInvalid << " does not exist");
   }
   
   if (doesAuthorizedUserExist(loginContext3))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user: " << loginContext3.username << "@" << loginContext3.authProvider << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: authorized user: " << loginContext3.username << "@" << loginContext3.authProvider << " does not exist");
      return;
   }
   
   if (doesAuthorizedUserExist(loginContextInvalid))
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: invalid authorized user: " << loginContext1.username << "@" << loginContext1.authProvider << " exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): invalid authorized user: " << loginContext1.username << "@" << loginContext1.authProvider << " does not exist");
   }
   
   if (deleteAuthorizedUser(handle2))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user with handle: " << handle2 << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: error deleting authorized user with handle: " << handle2);
      return;
   }
   
   if (deleteAuthorizedUser(loginContext3))
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): authorized user: " << loginContext3.username << "@" << loginContext3.authProvider << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: error deleting authorized user: " << loginContext3.username << "@" << loginContext3.authProvider);
      return;
   }
   
   users.clear();
   count = getAllAuthorizedUsers(users);
   
   if (count == 1)
   {
      DebugLog(<< "XmppAgentDatabase::testAuthorizedUser(): successfully completed tests for authorized user table, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testAuthorizedUser(): ERROR: failure during testing of authorized user table, count: " << count);
      return;
   }
}
   
void XmppAgentDatabase::testDevice()
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::testDevice(): ERROR: database not initialized");
      return;
   }
   
   /*
   InfoLog(<< "XmppAgentDatabase::testDevice(): Database: " << mDb->name() << " has the following tables: "
      << " AUTHORIZED_USER_ACCOUNT: " << (mDb->has_collection(AUTHORIZED_USER_ACCOUNT_TABLE) ? "yes" : "no")
      << " DEVICE: " << (mDb->has_collection(DEVICE_TABLE) ? "yes" : "no")
      << " XMPP_ACCOUNT: " << (mDb->has_collection(XMPP_ACCOUNT_TABLE) ? "yes" : "no")
      << " XMPP_PUSH_REGISTRATION: " << (mDb->has_collection(XMPP_PUSH_REGISTRATION_TABLE) ? "yes" : "no"));
      
   if (mDb->has_collection(DEVICE_TABLE))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device table found in: " << mDb->name() << " with row count: " << mDevices->count({}));
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device table not found in: " << mDb->name());
      return;
   }
   
   resetIndexes();
   mPushRegistration->delete_many({});
   mXmppAccounts->delete_many({});
   mDevices->delete_many({});
   mAuthorizedUsers->delete_many({});
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo1;
   regInfo1.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfo1.deviceToken = "********11";
   regInfo1.apnInfo.apnsTopic = "********11";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo2;
   regInfo2.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfo2.deviceToken = "**********";
   regInfo2.apnInfo.apnsTopic = "**********";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo3;
   regInfo3.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfo3.deviceToken = "********33";
   regInfo3.apnInfo.apnsTopic = "********33";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo4;
   regInfo4.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfo4.deviceToken = "********44";
   regInfo4.apnInfo.apnsTopic = "********44";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo5;
   regInfo5.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfo5.deviceToken = "5555555555";
   regInfo5.apnInfo.apnsTopic = "5555555555";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfoInvalidNetworkType;
   regInfoInvalidNetworkType.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Unknown;
   regInfoInvalidNetworkType.deviceToken = "INVALID";
   regInfoInvalidNetworkType.apnInfo.apnsTopic = "INVALID";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfoInvalidDeviceToken;
   regInfoInvalidDeviceToken.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfoInvalidDeviceToken.deviceToken = "";
   regInfoInvalidDeviceToken.apnInfo.apnsTopic = "INVALID";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfoInvalidApnsTopic;
   regInfoInvalidApnsTopic.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfoInvalidApnsTopic.deviceToken = "INVALID";
   regInfoInvalidApnsTopic.apnInfo.apnsTopic = "";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo1Updated = regInfo1;
   regInfo1Updated.apnInfo.apnsTopic = "UPDATED";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfoInvalidShouldNotExist;
   regInfoInvalidNetworkType.pushNetworkType = CPCAPI2::PushNotification::PushNetworkType_Apple;
   regInfoInvalidNetworkType.deviceToken = "SHOULD_NOT_EXIST";
   regInfoInvalidNetworkType.apnInfo.apnsTopic = "SHOULD_NOT_EXIST";
   
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfo;
   CPCAPI2::PushNotification::PushNotificationRegistrationInfo regInfoEmpty;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo1;
   pushInfo1.xmppAccountHandle = 1;
   pushInfo1.pushRegistrationInfo = regInfo1;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo2;
   pushInfo2.xmppAccountHandle = 2;
   pushInfo2.pushRegistrationInfo = regInfo2;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo3;
   pushInfo3.xmppAccountHandle = 3;
   pushInfo3.pushRegistrationInfo = regInfo3;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo4;
   pushInfo4.xmppAccountHandle = 4;
   pushInfo4.pushRegistrationInfo = regInfo4;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfoInvalidAccountHandle;
   pushInfoInvalidAccountHandle.xmppAccountHandle = 0;
   pushInfoInvalidAccountHandle.pushRegistrationInfo = regInfo5;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfoInvalidNetworkType;
   pushInfoInvalidNetworkType.xmppAccountHandle = 5;
   pushInfoInvalidNetworkType.pushRegistrationInfo = regInfoInvalidNetworkType;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfoInvalidDeviceToken;
   pushInfoInvalidDeviceToken.xmppAccountHandle = 6;
   pushInfoInvalidDeviceToken.pushRegistrationInfo = regInfoInvalidDeviceToken;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfoInvalidApnsTopic;
   pushInfoInvalidApnsTopic.xmppAccountHandle = 7;
   pushInfoInvalidApnsTopic.pushRegistrationInfo = regInfoInvalidApnsTopic;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo;
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfoEmpty;
   pushInfoEmpty.xmppAccountHandle = 0;
   pushInfoEmpty.pushRegistrationInfo = regInfoEmpty;
   
   CPCAPI2::XmppAgent::XmppPushRegistrationInfo pushInfo1Updated;
   pushInfo1Updated.xmppAccountHandle = 1;
   pushInfo1Updated.pushRegistrationInfo = regInfo1Updated;

   CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushHandle = 0;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushHandle1 = 1;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushHandle2 = 2;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushHandle3 = 3;
   CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushHandle4 = 4;
   
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle = 0;
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle1 = 1;
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle2 = 2;
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle3 = 3;
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandle4 = 4;
   CPCAPI2::PushNotification::PushNotificationDeviceHandle deviceHandleInvalid = 99;
   
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle = 0;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle1 = 1;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle2 = 2;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle3 = 3;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle4 = 4;
   
   if (addDevice(pushInfo1, deviceHandle, pushHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device: " << pushInfo1.pushRegistrationInfo.deviceToken << " successfully added with device handle: " << deviceHandle << " push handle: " << pushHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when adding device: " << pushInfo1.pushRegistrationInfo.deviceToken);
      return;
   }
   deviceHandle = 0;
   pushHandle = 0;
   
   if (addDevice(pushInfo2, deviceHandle, pushHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device: " << pushInfo2.pushRegistrationInfo.deviceToken << " successfully added with device handle: " << deviceHandle << " push handle: " << pushHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when adding device: " << pushInfo2.pushRegistrationInfo.deviceToken);
      return;
   }
   deviceHandle = 0;
   pushHandle = 0;
   
   if (addDevice(pushInfo3, deviceHandle, pushHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device: " << pushInfo3.pushRegistrationInfo.deviceToken << " successfully added with device handle: " << deviceHandle << " push handle: " << pushHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when adding device: " << pushInfo3.pushRegistrationInfo.deviceToken);
      return;
   }
   deviceHandle = 0;
   pushHandle = 0;
   
   if (addDevice(pushInfo4, deviceHandle, pushHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device: " << pushInfo4.pushRegistrationInfo.deviceToken << " successfully added with device handle: " << deviceHandle << " push handle: " << pushHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when adding device: " << pushInfo4.pushRegistrationInfo.deviceToken);
      return;
   }
   deviceHandle = 0;
   pushHandle = 0;
   
   auto cursor1 = mDevices->find({});
      
   for (auto&& doc : cursor1)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device table row: " << bsoncxx::to_json(doc));
   }
      
   if (updateDevice(pushInfo1Updated, deviceHandle1))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with handle: " << deviceHandle1 << " successfully updated");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when updating device with handle: " << deviceHandle1);
      return;
   }
   
   auto cursor2 = mDevices->find({});
      
   for (auto&& doc : cursor2)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device table row: " << bsoncxx::to_json(doc));
   }
      
   DebugLog(<< "XmppAgentDatabase::testDevice(): device table has a row count: " << getDeviceCount());
   
   regInfo = regInfoEmpty;
   if (getDevice(deviceHandle2, regInfo))
   {
      bool tokenValid = (regInfo2.deviceToken == regInfo.deviceToken);
      bool apnsValid = (regInfo2.apnInfo.apnsTopic == regInfo.apnInfo.apnsTopic);
      bool networkValid = (regInfo2.pushNetworkType == regInfo.pushNetworkType);
      DebugLog(<< "XmppAgentDatabase::testDevice(): device info retrieved successfully: device token: "
         << " device token: " << regInfo.deviceToken << " valid: " << (tokenValid ? "yes" : "no")
         << " apns topic: " << regInfo.apnInfo.apnsTopic << " valid: " << (apnsValid ? "yes" : "no")
         << " network type: " << regInfo.pushNetworkType << " valid: " << (networkValid ? "yes" : "no")
         << " for device handle: " << deviceHandle2);
      
      if (!(tokenValid && apnsValid && networkValid))
      {
         DebugLog(<< "XmppAgentDatabase::testDevice(): data mismatch in device info retrieved");
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving device with handle: " << deviceHandle2);
      return;
   }
      
   regInfo = regInfoEmpty;
   pushInfo = pushInfoEmpty;
   if (getDevicePushRegistrationInfo(pushHandle3, pushInfo))
   {
      bool tokenValid = (pushInfo3.pushRegistrationInfo.deviceToken == pushInfo.pushRegistrationInfo.deviceToken);
      bool apnsValid = (pushInfo3.pushRegistrationInfo.apnInfo.apnsTopic == pushInfo.pushRegistrationInfo.apnInfo.apnsTopic);
      bool networkValid = (pushInfo3.pushRegistrationInfo.pushNetworkType == pushInfo.pushRegistrationInfo.pushNetworkType);
      bool accountValid = (pushInfo3.xmppAccountHandle == pushInfo.xmppAccountHandle);
      DebugLog(<< "XmppAgentDatabase::testDevice(): push info retrieved successfully: "
         << " device token: " << pushInfo.pushRegistrationInfo.deviceToken << " valid: " << (tokenValid ? "yes" : "no")
         << " apns topic: " << pushInfo.pushRegistrationInfo.apnInfo.apnsTopic << " valid: " << (apnsValid ? "yes" : "no")
         << " network type: " << pushInfo.pushRegistrationInfo.pushNetworkType << " valid: " << (networkValid ? "yes" : "no")
         << " account handle: " << pushInfo.xmppAccountHandle << " valid: " << (accountValid ? "yes" : "no")
         << " for push handle: " << pushHandle3);
      
      if (!(tokenValid && apnsValid && networkValid && accountValid))
      {
         DebugLog(<< "XmppAgentDatabase::testDevice(): ERROR: data mismatch in device info retrieved");
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving push info with handle: " << pushHandle3);
      return;
   }
      
   cpc::vector<CPCAPI2::PushNotification::PushNotificationRegistrationInfo> devices;
   uint32_t count = getAllDevices(devices);
      
   if (count == 4)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): successfully retrieved all devices, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving all devices, count: " << count);
      return;
   }
   
   cpc::vector<CPCAPI2::XmppAgent::XmppPushRegistrationInfo> pushRegistrations;
   count = getAllDevicePushRegistrations(pushRegistrations);
   
   if (count == 4)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): successfully retrieved all push registrations, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving all push registrations, count: " << count);
      return;
   }
   
   if (getDeviceHandle(regInfo4, deviceHandle))
   {
      bool handleValid = (deviceHandle4 == deviceHandle);
      DebugLog(<< "XmppAgentDatabase::testDevice(): device handle retrieved successfully: "
         << " device handle: " << deviceHandle << " valid: " << (handleValid ? "yes" : "no")
         << " for device: " << regInfo4.deviceToken);
      
      if (!handleValid)
      {
         DebugLog(<< "XmppAgentDatabase::testDevice(): ERROR: data mismatch in device info retrieved");
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving device handle for device: " << regInfo4.deviceToken);
      return;
   }
   deviceHandle = 0;
   
   if (getDevicePushRegistrationHandle(deviceHandle3, accountHandle3, pushHandle))
   {
      bool handleValid = (pushHandle3 == pushHandle);
      DebugLog(<< "XmppAgentDatabase::testDevice(): push handle retrieved successfully: "
               << " push handle: " << pushHandle << " valid: " << (handleValid ? "yes" : "no")
               << " for device handle: " << deviceHandle3 << " and account handle: " << accountHandle3);
      
      if (!handleValid)
      {
         DebugLog(<< "XmppAgentDatabase::testDevice(): ERROR: data mismatch in device info retrieved");
         return;
      }
      
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure when retrieving push handle for device handle: " << deviceHandle3 << " and account handle: " << accountHandle3);
      return;
   }
   pushHandle = 0;
   
   if (doesDeviceExist(deviceHandle2))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with handle: " << deviceHandle2 << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device with handle: " << deviceHandle2 << " does not exist");
      return;
   }
      
   if (doesDeviceExist(deviceHandleInvalid))
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device with invalid handle exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with invalid handle does not exist");
   }
      
   if (doesDeviceExist(regInfo3))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device: " << regInfo3.deviceToken << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device: " << regInfo3.deviceToken << " does not exist");
      return;
   }
   
   if (doesDeviceExist(regInfoInvalidApnsTopic))
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device with invalid apns topic exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with invalid apns topic does not exist");
   }
   
   if (doesDeviceExist(regInfoInvalidDeviceToken))
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device with invalid device token exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with invalid device token does not exist");
   }
   
   if (doesDeviceExist(regInfoInvalidNetworkType))
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device with invalid network type exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with invalid network type does not exist");
   }
   
   if (doesDeviceExist(regInfoInvalidShouldNotExist))
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: device not added exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device not added does not exist");
   }
   
   if (deleteDevice(deviceHandle2))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device handle: " << deviceHandle2 << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: error deleting device with handle: " << deviceHandle2);
      return;
   }
   
   if (deleteDevice(regInfo4.deviceToken))
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device with token: " << regInfo4.deviceToken << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: error deleting device with token: " << regInfo4.deviceToken);
      return;
   }
   
   auto cursor3 = mDevices->find({});
   
   for (auto&& doc : cursor3)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): device table row: " << bsoncxx::to_json(doc));
   }
   
   devices.clear();
   count = getAllDevices(devices);
      
   if (count == 2)
   {
      DebugLog(<< "XmppAgentDatabase::testDevice(): successfully completed tests for device table, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testDevice(): ERROR: failure during testing of device table, count: " << count);
      return;
   }
   */
}

void XmppAgentDatabase::testXmppAccount()
{
   if (!mDb)
   {
      ErrLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: database not initialized");
      return;
   }
   
   InfoLog(<< "XmppAgentDatabase::testXmppAccount(): Database: " << mDb->name() << " has the following tables: "
           << " AUTHORIZED_USER_ACCOUNT: " << (mDb->has_collection(AUTHORIZED_USER_ACCOUNT_TABLE) ? "yes" : "no")
           << " DEVICE: " << (mDb->has_collection(DEVICE_TABLE) ? "yes" : "no")
           << " XMPP_ACCOUNT: " << (mDb->has_collection(XMPP_ACCOUNT_TABLE) ? "yes" : "no")
           << " XMPP_PUSH_REGISTRATION: " << (mDb->has_collection(XMPP_PUSH_REGISTRATION_TABLE) ? "yes" : "no"));
   
   if (mDb->has_collection(XMPP_ACCOUNT_TABLE))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account table found in: " << mDb->name() << " with row count: " << mXmppAccounts->count({}));
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account table not found in: " << mDb->name());
      return;
   }
   
   resetIndexes();
   mPushRegistration->delete_many({});
   mXmppAccounts->delete_many({});
   mDevices->delete_many({});
   mAuthorizedUsers->delete_many({});
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext1;
   loginContext1.authProvider = "stretto.com";
   loginContext1.username = "user1";
   loginContext1.password = "user1";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext2;
   loginContext2.authProvider = "stretto.com";
   loginContext2.username = "user2";
   loginContext2.password = "user2";
   
   CPCAPI2::Agent::SdkManager::LoginContext loginContext;
   CPCAPI2::Agent::SdkManager::LoginContext loginContextEmpty;
   
   CPCAPI2::XmppAccount::XmppAccountSettings account1;
   account1.username = "********";
   account1.password = "********";
   account1.domain = "counterpath.com";
   account1.connectTimeOut = ********;
   account1.ignoreCertVerification = true;
   account1.proxy = "********";
   account1.resource = "********";
   
   CPCAPI2::XmppAccount::XmppAccountSettings account2;
   account2.username = "********";
   account2.password = "********";
   account2.domain = "counterpath.com";
   account2.connectTimeOut = ********;
   account2.ignoreCertVerification = true;
   account2.proxy = "********";
   account2.resource = "********";
   
   CPCAPI2::XmppAccount::XmppAccountSettings account3;
   account3.username = "********";
   account3.password = "********";
   account3.domain = "counterpath.com";
   account3.connectTimeOut = ********;
   account3.ignoreCertVerification = true;
   account3.proxy = "********";
   account3.resource = "********";
   
   CPCAPI2::XmppAccount::XmppAccountSettings account4;
   account4.username = "********";
   account4.password = "********";
   account4.domain = "counterpath.com";
   account4.connectTimeOut = ********;
   account4.ignoreCertVerification = true;
   account4.proxy = "********";
   account4.resource = "********";
   
   CPCAPI2::XmppAccount::XmppAccountSettings account;
   CPCAPI2::XmppAccount::XmppAccountSettings accountEmpty;
   
   CPCAPI2::XmppAccount::XmppAccountSettings account1Updated = account1;
   account1Updated.password = "UPDATED";
   
   CPCAPI2::XmppAccount::XmppAccountSettings account1EmptyUsername = account1;
   account1EmptyUsername.username = "";
   
   CPCAPI2::XmppAccount::XmppAccountSettings accountShouldNotExist;
   accountShouldNotExist.username = "SHOULD_NOT_EXIST";
   accountShouldNotExist.password = "SHOULD_NOT_EXIST";
   accountShouldNotExist.domain = "SHOULD_NOT_EXIST";
   
   CPCAPI2::JsonApi::JsonApiLoginHandle userHandle = 0;
   CPCAPI2::JsonApi::JsonApiLoginHandle userHandle1 = 1;
   CPCAPI2::JsonApi::JsonApiLoginHandle userHandle2 = 2;
   CPCAPI2::JsonApi::JsonApiLoginHandle userHandleInvalid = 99;
   
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle = 0;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle1 = ********;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle2 = ********;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle3 = ********;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandle4 = ********;
   CPCAPI2::XmppAccount::XmppAccountHandle accountHandleInvalid = 99;
   
   if (addAuthorizedUser(loginContext1, userHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): authorized user: " << loginContext1.username << "@" << loginContext1.authProvider << " successfully added with handle: " << userHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding authorized user: " << loginContext1.username << "@" << loginContext1.authProvider);
      return;
   }
   userHandle = 0;
   
   if (addAuthorizedUser(loginContext2, userHandle))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): authorized user: " << loginContext2.username << "@" << loginContext2.authProvider << " successfully added with handle: " << userHandle);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding authorized user: " << loginContext2.username << "@" << loginContext2.authProvider);
      return;
   }
   userHandle = 0;
   
   if (addXmppAccount(account1, userHandle1, accountHandle1))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): account: " << account1.username << " successfully added with auth handle: " << userHandle1 << " account handle: " << accountHandle1);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding account: " << account1.username);
      return;
   }
   
   if (addXmppAccount(account2, userHandle1, accountHandle2))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): account: " << account2.username << " successfully added with auth handle: " << userHandle1 << " account handle: " << accountHandle2);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding account: " << account2.username);
      return;
   }
   
   if (addXmppAccount(account3, userHandle1, accountHandle3))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): account: " << account3.username << " successfully added with auth handle: " << userHandle1 << " account handle: " << accountHandle3);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding account: " << account3.username);
      return;
   }
   
   if (addXmppAccount(account4, userHandle2, accountHandle4))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): account: " << account4.username << " successfully added with auth handle: " << userHandle2 << " account handle: " << accountHandle4);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when adding account: " << account4.username);
      return;
   }
   
   if (addXmppAccount(account4, userHandle2, accountHandle4))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: duplicate account: " << account4.username << " successfully added with auth handle: " << userHandle2 << " account handle: " << accountHandle4);
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): duplicate xmpp account: " << account4.username << " could not be added");
   }
   
   if (addXmppAccount(account2, userHandleInvalid, accountHandle2))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: account: " << account4.username << " with invalid auth handle: " << userHandleInvalid << " successfully added");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account: " << account4.username << " with invalid auth handle: " << userHandleInvalid << " could not be added");
   }
   
   auto cursor1 = mAuthorizedUsers->find({});
   
   for (auto&& doc : cursor1)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): authorized user table row: " << bsoncxx::to_json(doc));
   }
   
   auto cursor2 = mXmppAccounts->find({});
   
   for (auto&& doc : cursor2)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account table row: " << bsoncxx::to_json(doc));
   }
   
   if (updateXmppAccount(account1Updated, userHandle1, accountHandle1))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): account: " << account1.username << " with auth handle: " << userHandle1 << " account handle: " << accountHandle1 << " successfully updated");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when updating xmpp account with handle: " << accountHandle1);
      return;
   }
   
   if (updateXmppAccount(account2, userHandleInvalid, accountHandle2))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: account: " << account2.username << " with invalid auth handle: " << userHandleInvalid << " successfully updated");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account: " << account2.username << " with invalid auth handle: " << userHandleInvalid << " could not be updated");
   }
   
   if (updateXmppAccount(account3, userHandle1, accountHandleInvalid))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: account: " << account3.username << " with invalid account handle: " << accountHandleInvalid << " successfully updated");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account: " << account3.username << " with invalid account handle: " << accountHandleInvalid << " could not be updated");
   }
   
   if (updateXmppAccount(account3, userHandle2, accountHandle3))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: account: " << account3.username << " with modified user handle: " << userHandle2 << " successfully updated");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account: " << account3.username << " with modified user handle: " << userHandle2 << " could not be updated");
   }
   
   auto cursor3 = mXmppAccounts->find({});
   
   for (auto&& doc : cursor3)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account table row: " << bsoncxx::to_json(doc));
   }
   
   DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account table has a row count: " << getXmppAccountCount());
   
   account = accountEmpty;
   if (getXmppAccount(accountHandle3, account))
   {
      bool usernameValid = (account.username == account3.username);
      bool passwordValid = (account.password == account3.password);
      bool domainValid = (account.domain == account3.domain);
      bool connectTimeOutValid = (account.connectTimeOut == account3.connectTimeOut);
      bool ignoreCertVerificationValid = (account.ignoreCertVerification == account3.ignoreCertVerification);
      bool proxyValid = (account.proxy == account3.proxy);
      bool resourceValid = (account.resource == account3.resource);
      
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account info retrieved successfully: "
               << " username: " << account.username << " valid: " << (usernameValid ? "yes" : "no")
               << " password: " << account.password << " valid: " << (passwordValid ? "yes" : "no")
               << " domain: " << account.domain << " valid: " << (domainValid ? "yes" : "no")
               << " connectTimeOut: " << account.connectTimeOut << " valid: " << (connectTimeOutValid ? "yes" : "no")
               << " ignoreCertVerification: " << account.ignoreCertVerification << " valid: " << (ignoreCertVerificationValid ? "yes" : "no")
               << " proxy: " << account.proxy << " valid: " << (proxyValid ? "yes" : "no")
               << " resource: " << account.resource << " valid: " << (resourceValid ? "yes" : "no")
               << " for xmpp account handle: " << accountHandle3);
      
      if (!(usernameValid && passwordValid && domainValid && connectTimeOutValid && ignoreCertVerificationValid && proxyValid && resourceValid))
      {
         DebugLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: data mismatch in xmpp account info retrieved with handle: " << accountHandle3);
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when retrieving xmpp account with handle: " << accountHandle3);
      return;
   }
   
   account = accountEmpty;
   cpc::vector<CPCAPI2::XmppAccount::XmppAccountHandle> accounts;
   if (getXmppAccountsForAuthorizedUser(userHandle2, accounts) == 1)
   {
      if (accounts[0] == accountHandle4)
      {
         DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account handle: " << accounts[0] << " retrieved successfully for auth user: " << userHandle2);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account handle mismatch: " << accounts[0] << " retrieved for auth user: " << userHandle2 << " should be xmpp account handle: " << accountHandle4);
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp accounts count mismatch: " << accounts.size() << " accounts retrieved with auth user handle: " << userHandle2 << " should be an xmpp account count of 1");
      return;
   }
   
   account = accountEmpty;
   accounts.clear();
   accountHandle = 0;
   if (getXmppAccountHandle(account3, accountHandle))
   {
      if (accountHandle == accountHandle3)
      {
         DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account handle: " << accountHandle << " retrieved successfully for xmpp account: " << account3.username);
      }
      else
      {
         DebugLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account handle mismatch: " << accountHandle << " retrieved for xmpp account: " << account3.username << " should be xmpp account handle: " << accountHandle3);
         return;
      }
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when retrieving xmpp account handle for account: " << account3.username);
      return;
   }
   
   account = accountEmpty;
   accounts.clear();
   accountHandle = 0;

   uint32_t count = getAllXmppAccounts(accounts);
   
   if (count == 4)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): successfully retrieved all xmpp account handles, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when retrieving all xmpp account handles, count: " << count);
      return;
   }
   
   account = accountEmpty;
   accounts.clear();
   accountHandle = 0;
   count = 0;
   
   cpc::vector<CPCAPI2::XmppAccount::XmppAccountSettings> accountSettings;
   count = getAllXmppAccounts(accountSettings);
   
   if (count == 4)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): successfully retrieved all xmpp account settings, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: failure when retrieving all xmpp account settings, count: " << count);
      return;
   }
   
   if (doesXmppAccountExist(accountHandle3))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account with handle: " << accountHandle3 << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account with handle: " << accountHandle3 << " does not exist");
      return;
   }
   
   if (doesXmppAccountExist(accountHandleInvalid))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account with invalid handle exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account with invalid handle does not exist");
   }
   
   if (doesXmppAccountExist(account2))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account: " << account2.username << " exists");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: xmpp account: " << account2.username << " does not exist");
      return;
   }
   
   if (doesXmppAccountExist(accountShouldNotExist))
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: invalid xmpp account: " << accountShouldNotExist.username << " exists");
      return;
   }
   else
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): invalid xmpp account: " << accountShouldNotExist.username << " does not exist");
   }
   
   if (deleteXmppAccount(accountHandle3))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account handle: " << accountHandle3 << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: error deleting xmpp account with handle: " << accountHandle3);
      return;
   }
   
   if (deleteAllXmppAccounts(userHandle2))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp accounts associated with auth user: " << userHandle2 << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: error deleting xmpp accounts with auth user: " << userHandle2);
      return;
   }
   
   auto cursor4 = mXmppAccounts->find({});
   
   for (auto&& doc : cursor4)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): xmpp account table row: " << bsoncxx::to_json(doc));
   }
   
   count = 0;
   count = getXmppAccountCount(userHandle1);
   
   if (count == 2)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): successfully retrieved xmpp account count: " << count << " for auth user: " << userHandle2);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: count mismatch: " << count << " xmpp accounts existing for auth user: " << userHandle1);
      return;
   }
   
   if (deleteAuthorizedUser(userHandle1))
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): auth user: " << userHandle1 << " deleted");
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): ERROR: error deleting auth user: " << userHandle1);
      return;
   }
   
   count = 0;
   count = getXmppAccountCount();
   if (count == 0)
   {
      DebugLog(<< "XmppAgentDatabase::testXmppAccount(): successfully completed tests for xmpp account table, count: " << count);
   }
   else
   {
      InfoLog(<< "XmppAgentDatabase::testXmppAccount(): failure during testing of xmpp account table, count: " << count);
      return;
   }
}
   
}
   
}

#endif
