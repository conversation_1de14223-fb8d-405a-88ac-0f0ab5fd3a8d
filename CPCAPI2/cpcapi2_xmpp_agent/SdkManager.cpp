#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2Runner.h"

#include <cpcapi2.h>
#include <push_service/PushNotificationServiceManagerInternal.h>
#include <push_endpoint/PushNotificationEndpointManagerInternal.h>
#include <jsonapi/JsonApiServerInternal.h>
#include <remotesync/RemoteSyncManagerInternal.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <servicenotifications_server/ServiceNotificationsManager.h>
#include <phone/PhoneInternal.h>

#include <fstream>
#include <thread>
#include <ctime>
#include <iomanip> // put_time
#include <signal.h>

#include <boost/preprocessor/stringize.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

#include "rutil/ConfigParse.hxx"
#include "impl/util/cpc_logger.h"
#include "impl/log/LocalLogger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER
#define CP_LOCAL_LOGGER_VAR mLocalLogger
#define CPCAPI2_CONF_NUM_SDK_REACTORS 12
#define CONTEXT_CLEANUP_TIMERID 1
// cleanup abandoned sessions every 60 days
#define CPCAPI2_AGENT_CLEANUP_INTERVAL_SECONDS (60*60*24*60)
#define CPCAPI2_QUERY_TIMEOUT_SECONDS 3

#ifdef BUILD_INFO
#define BUILD_INFO_STRING BOOST_PP_STRINGIZE(BUILD_INFO)
#else
#define BUILD_INFO_STRING "No build info"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::PushEndpoint;
using namespace CPCAPI2::PushService;
using namespace CPCAPI2::CloudServiceConfig;
using namespace CPCAPI2::ServiceNotifications;

namespace CPCAPI2
{

namespace Agent
{


class MyConfigParse : public resip::ConfigParse
{

private:

   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}

};


std::atomic_int SdkManager::sNextSdkThreadPoolIdx(0);

SdkManager* SdkManager::mInstance = 0;

SdkManager* SdkManager::getInstance()
{
   if (!mInstance)
      mInstance = new SdkManager();

   return mInstance;
}

SdkManager::SdkManager() :
mOrchServer(NULL),
mContextCleanupTimer(mReactor),
mLocalLogger(NULL),
mDebugDumpEnabled(false),
mHealthCheckPushNotification(true),
mHealthCheckRedis(true),
mHealthCheckLogin(true),
mUserSessionCount(mMapContextToRunner, mMapRunnerToJsonApiUsers, mMapJsonApiUserToRunner),
mPushRegistrationInfo(this, mMapContextToRunner, mMapRunnerToJsonApiUsers, mMapJsonApiUserToRunner, mPushQueryListResult, mPushQueryInfoResult, mQueryListSignal, mQueryInfoSignal, mQueryHttpSignal, mReactor, mQueryStream, mRunnerMutex),
mXmppRegistrationInfo(this, mMapContextToRunner, mMapRunnerToJsonApiUsers, mMapJsonApiUserToRunner, mAgentQueryListResult, mAgentQueryInfoResult, mQueryListSignal, mQueryInfoSignal, mQueryHttpSignal, mReactor, mQueryStream, mRunnerMutex),
mRunnerInfo(mMapContextToRunner, mMapRunnerToJsonApiUsers, mMapJsonApiUserToRunner, mQueryHttpSignal, mReactor, mQueryStream, mRunnerMutex)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
   mPushServiceManager->process(CPCAPI2::PushService::PushNotificationServiceManager::kBlockingModeNonBlocking);
}

void SdkManager::appInit()
{
#ifndef WIN32
   // OBELISK-4439: prevent SIGPIPE from killing the whole process by ignoring SIGPIPE;
   // write errors will be handled locally instead
   if (signal(SIGPIPE, SIG_IGN) == SIG_ERR)
   {
      std::cerr << "Error assigning signal handler for SIGPIPE" << std::endl;
      return;
   }

   // commenting out until OBELISK-4642 is investigated/resolved
   /*
   if (signal(SIGTERM, stop_agent) == SIG_ERR)
   {
      std::cerr << "Error assigning signal handler for SIGTERM" << std::endl;
      return;
   }

   if (signal(SIGINT, stop_agent) == SIG_ERR)
   {
      std::cerr << "Error assigning signal handler for SIGINT" << std::endl;
      return;
   }
   */
#endif

   // Make sure to use sNextSdkThreadPoolIdx here just to keep the paradigm
   // consistent and not force a thread re-use immediately
   // mPhone = CPCAPI2::PhoneInternal::create( sNextSdkThreadPoolIdx % CPCAPI2_CONF_NUM_SDK_REACTORS );
   // sNextSdkThreadPoolIdx++;
   mPhone = static_cast<PhoneInternal*>(CPCAPI2::Phone::create());
   sNextSdkThreadPoolIdx = 0;

   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
#ifdef WIN32
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
#else
   licenseInfo.licenseDocumentLocation = "./";
#endif
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   // old style logging is on until we properly handle logging in modules
   // used by both clients and the XMPP agent
   mPhone->setLoggingEnabled("cpcapi2_xmpp_agent", true);
   mLocalLogger = mPhone->localLogger();
   static_cast<PhoneInternal*>(mPhone)->setLocalFileLoggingEnabled("xmppagent", true);

   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_xmpp_agent.config");

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServerInternal::getInternalInterface(mPhone);
   mJsonApiServer->setHandler(this);
   mJsonApiServer->setCallbackHook(SdkManager_sdkCallbackHook, this);

   CPCAPI2::JsonApi::JsonApiServerConfig jsonApiServerConfig;
   fileConfig.getConfigValue("jsonapi_websocket_port", jsonApiServerConfig.websocketPort);
   fileConfig.getConfigValue("jsonapi_http_port", jsonApiServerConfig.httpPort);

   resip::Data httpsCertificateFilePath;
   if (fileConfig.getConfigValue("jsonapi_http_cert_file_path", httpsCertificateFilePath))
   {
      jsonApiServerConfig.httpsCertificateFilePath = httpsCertificateFilePath.c_str();
   }

   resip::Data httpsPrivateKeyFilePath;
   if (fileConfig.getConfigValue("jsonapi_http_priv_key_file_path", httpsPrivateKeyFilePath))
   {
      jsonApiServerConfig.httpsPrivateKeyFilePath = httpsPrivateKeyFilePath.c_str();
   }

   resip::Data httpsDiffieHellmanParamsFilePath;
   if (fileConfig.getConfigValue("jsonapi_http_dh_params_file_path", httpsDiffieHellmanParamsFilePath))
   {
      jsonApiServerConfig.httpsDiffieHellmanParamsFilePath = httpsDiffieHellmanParamsFilePath.c_str();
   }

   resip::Data certFilePath;
   fileConfig.getConfigValue("certificate_file_path", certFilePath);
   jsonApiServerConfig.certificateFilePath = certFilePath.c_str();

   mJsonApiServer->start(jsonApiServerConfig);

   // bliu: wait for a while to allow the HTTP server's thread to be active. otherwise addHttpResourceHandler would have no effect
   std::this_thread::sleep_for(std::chrono::seconds(3));

   if (auto httpServer = dynamic_cast<JsonApi::HttpServerInternal*>(mJsonApiServer))
   {
      httpServer->addRequestHandler("^/statusApi/redis$", &mHealthCheckRedis);
      httpServer->addRequestHandler("^/statusApi/push_notification$", &mHealthCheckPushNotification);
      httpServer->addRequestHandler("^/statusApi/login$", &mHealthCheckLogin);
      httpServer->addRequestHandler("^/statusApi/remotesync$", &mHealthCheckRemoteSync);
      httpServer->addRequestHandler("^/statusApi/numUserSessions$", &mUserSessionCount);
      httpServer->addRequestHandler("^/statusApi/build$", &mBuildInfo);
      httpServer->addRequestHandler("^/statusApi/pushRegInfo$", &mPushRegistrationInfo);
      httpServer->addRequestHandler("^/statusApi/xmppRegInfo$", &mXmppRegistrationInfo);
      httpServer->addRequestHandler("^/statusApi/runnerInfo$", &mRunnerInfo);
   }

   // Create the send transport instance and set the json server. Though not likely to be used
   // to send json messages by the master phone, the common json server configuration may possibly
   // get accessed, so best not to leave it uninitialized.
   mJsonSendTransport = CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone);
   mJsonSendTransport->setJsonApiServer(mJsonApiServer);

   mPushServiceManager = CPCAPI2::PushService::PushNotificationServiceManager::getInterface(mPhone);
   CPCAPI2::PushService::PushNotificationServiceManagerInternal::getInternalInterface(mPhone)->setCallbackHook(SdkManager_sdkCallbackHook, this);

   mPhone->runOnSdkModuleThread(&SdkManager::addSdkObserver, this);

   PushDatabaseSettings pushDatabaseSettings;
   resip::Data pushdbRedisIp;
   if (fileConfig.getConfigValue("pushdb_redis_ip", pushdbRedisIp))
   {
      pushDatabaseSettings.redisIp = pushdbRedisIp.c_str();
   }

   fileConfig.getConfigValue("pushdb_redis_port", pushDatabaseSettings.redisPort);
   mPushServiceManager->configureDatabaseAccess(pushDatabaseSettings);

   PushProviderSettings apnPushProviderSettings;
   apnPushProviderSettings.pushNetworkType = PushNetworkType_Apple;

   resip::Data p8file;
   resip::Data authKeyId;
   resip::Data teamId;
   resip::Data apnServerUrl;

   if (fileConfig.getConfigValue("pushprovider_apns_p8file", p8file))
      apnPushProviderSettings.apnSettings.p8file = p8file.c_str();

   if (fileConfig.getConfigValue("pushprovider_apns_authkeyid", authKeyId))
      apnPushProviderSettings.apnSettings.authKeyId = authKeyId.c_str();

   if (fileConfig.getConfigValue("pushprovider_apns_teamid", teamId))
      apnPushProviderSettings.apnSettings.teamId = teamId.c_str();

   if (fileConfig.getConfigValue("pushprovider_apns_sandbox_p8file", p8file))
      apnPushProviderSettings.apnSandboxSettings.p8file = p8file.c_str();

   if (fileConfig.getConfigValue("pushprovider_apns_sandbox_authkeyid", authKeyId))
      apnPushProviderSettings.apnSandboxSettings.authKeyId = authKeyId.c_str();

   if (fileConfig.getConfigValue("pushprovider_apns_sandbox_teamid", teamId))
      apnPushProviderSettings.apnSandboxSettings.teamId = teamId.c_str();

   if (fileConfig.getConfigValue("apn_server_url", apnServerUrl))
      apnPushProviderSettings.apnSettings.apnUrl = apnServerUrl.c_str();

   if (fileConfig.getConfigValue("apn_sandbox_server_url", apnServerUrl))
      apnPushProviderSettings.apnSandboxSettings.apnUrl = apnServerUrl.c_str();

   mPushServiceManager->configurePushProvider(apnPushProviderSettings);

   PushProviderSettings fcmPushProviderSettings;
   fcmPushProviderSettings.pushNetworkType = PushNetworkType_FCM;

   resip::Data fcmServerUrl;
   if (fileConfig.getConfigValue("fcm_server_url", fcmServerUrl))
   {
      fcmPushProviderSettings.fcmSettings.fcmUrl = fcmServerUrl.c_str();
   }

   resip::Data fcmKeyId;
   if (fileConfig.getConfigValue("pushprovider_fcm_keyid", fcmKeyId))
   {
      fcmPushProviderSettings.fcmSettings.fcmKey = fcmKeyId.c_str();
   }

   mPushServiceManager->configurePushProvider(fcmPushProviderSettings);

   mOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(mPhone);
   mOrchServer->setHandler(this);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig serverConfig;
   resip::Data orchdbRedisIp;
   if (fileConfig.getConfigValue("orchdb_redis_ip", orchdbRedisIp))
   {
      serverConfig.redisIp = orchdbRedisIp.c_str();
   }

   fileConfig.getConfigValue("orchdb_redis_port", serverConfig.redisPort);
   mOrchServer->start(serverConfig);

   CPCAPI2::Auth::AuthProvider* authProviderParent = CPCAPI2::Auth::AuthProvider::getInterface(mPhone);

   mAuthProviderMock = CPCAPI2::Auth::AuthProviderMock::getInterface(authProviderParent);

   resip::Data serverUsername;
   if (!fileConfig.getConfigValue("cloud_server_username", serverUsername))
   {
      return;
   }

   resip::Data serverPassword;
   if (!fileConfig.getConfigValue("cloud_server_password", serverPassword))
   {
      return;
   }

   resip::Data authServerUrl;
   if (!fileConfig.getConfigValue("auth_server_url", authServerUrl))
   {
      return;
   }

   // currently hardcoded since the agent should always use a local orch server
   resip::Data orchServerUrl = "http://inproc.local:18089/jsonApi";
   if (!fileConfig.getConfigValue("orch_server_url", orchServerUrl))
   {
   //   return;
   }

   resip::Data thisServerRegion;
   if (!fileConfig.getConfigValue("jsonapi_server_region", thisServerRegion))
   {
      return;
   }

   resip::Data thisServerUrl;
   if (!fileConfig.getConfigValue("jsonapi_server_ws_url", thisServerUrl))
   {
      return;
   }

   resip::Data debugDumpEnabled;
   if (fileConfig.getConfigValue("debug_dump_enabled", debugDumpEnabled))
   {
      mDebugDumpEnabled = ((std::strncmp(debugDumpEnabled.c_str(),"1",1) == 0) ? true : false);
   }

   std::cout << "Configuration File Parsed: " << std::endl << fileConfig;

   // in-process orchestration server might not have its connection to redis up yet, so wait a bit
   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   cpc::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId();
   registerWithOrchServer(xmppAgentServiceId, authServerUrl.c_str(), serverUsername.c_str(), serverPassword.c_str(), orchServerUrl.c_str(), thisServerRegion.c_str(), thisServerUrl.c_str());

   cpc::string pushNotificationClientServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId();
   registerWithOrchServer(pushNotificationClientServiceId, authServerUrl.c_str(), serverUsername.c_str(), serverPassword.c_str(), orchServerUrl.c_str(), thisServerRegion.c_str(), thisServerUrl.c_str());

   UInt64 expiresTimeMs( CPCAPI2_AGENT_CLEANUP_INTERVAL_SECONDS );
   expiresTimeMs *= 1000;

   mContextCleanupTimer.expires_from_now(expiresTimeMs);
   mContextCleanupTimer.async_wait(this, CONTEXT_CLEANUP_TIMERID, NULL);
}

void SdkManager::registerWithOrchServer(const cpc::string& serviceId, const cpc::string& authServerUrl, const cpc::string& username, const cpc::string& password, const cpc::string& orchServerUrl, const cpc::string& thisServerRegion, const cpc::string& thisServerUrl)
{
   CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudServiceConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(mPhone);
   if (cloudServiceConfigMgr != NULL)
   {
      mServerInfo.region = thisServerRegion;
      mServerInfo.uri = thisServerUrl;
      mServerInfo.services.push_back(serviceId);
      mServerInfo.started = true;
      mServiceConfigSettings.authServerUrl = authServerUrl;
      mServiceConfigSettings.orchestrationServerUrl = orchServerUrl;
      mServiceConfigSettings.username = username;
      mServiceConfigSettings.password = password;
      cloudServiceConfigMgr->setServerInfo(mServiceConfigSettings, mServerInfo);
   }
}

void SdkManager::setServerShutdownFlag()
{
   CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudServiceConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(mPhone);
   if (cloudServiceConfigMgr != NULL)
   {
      mServerInfo.shutdown = true;
      cloudServiceConfigMgr->setServerInfo(mServiceConfigSettings, mServerInfo);
   }
}

void SdkManager::shutdown()
{
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   std::map<resip::Data, Cpcapi2Runner*> contextMapCpy = mMapContextToRunner;
   std::map<resip::Data, Cpcapi2Runner*>::iterator it = contextMapCpy.begin();
   for (; it != contextMapCpy.end(); ++it)
   {
      Cpcapi2Runner* runner = it->second;
      runner->shutdown();
      delete runner;
   }

   mMapContextToRunner.clear();
   mMapJsonApiUserToRunner.clear();
   mMapRunnerToJsonApiUsers.clear();
   mJsonApiServer->shutdown();
}

void SdkManager::join()
{
   mReactor.join();
}

int SdkManager::parseLoginContext(const std::string& loginContextString, LoginContext& outLoginContext)
{
   std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
   jsonRequest->Parse<0>(loginContextString.c_str());

   if (jsonRequest->HasParseError())
   {
      return kError;
   }

   LoginContext out;

   if (!jsonRequest->HasMember("authProvider"))
   {
      return kError;
   }

   const rapidjson::Value& authProviderVal = (*jsonRequest)["authProvider"];
   if (!authProviderVal.IsString())
   {
      return kError;
   }

   out.authProvider = authProviderVal.GetString();

   if (!jsonRequest->HasMember("username"))
   {
      return kError;
   }

   const rapidjson::Value& usernameVal = (*jsonRequest)["username"];
   if (!usernameVal.IsString())
   {
      return kError;
   }

   out.username = usernameVal.GetString();

   if (!jsonRequest->HasMember("password"))
   {
      return kError;
   }

   const rapidjson::Value& passwordVal = (*jsonRequest)["password"];
   if (!passwordVal.IsString())
   {
      return kError;
   }

   out.password = passwordVal.GetString();

   outLoginContext = out;

   return kSuccess;
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   // bliu: need to handle login failure as well
   mHealthCheckLogin.mValue = true;

   resip::Data contextStr(args.userIdentity.c_str(), args.userIdentity.size());
   cpc::string protocol = (args.isHttp ? "http" : "ws");
   LoginContext contextStruct;

   StackLog(<< "SdkManager::onNewLogin(): userIdentity: " << args.userIdentity << " auth-token: " << args.authToken << " user-runner count: " << " contextStr: " << contextStr.c_str() << " json-user count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());

   bool loginSuccess = false;

   if (jsonApiUser != 0)
   {
      std::map<resip::Data, Cpcapi2Runner*>::const_iterator it = mMapContextToRunner.find(contextStr);
      if (it != mMapContextToRunner.end())
      {
         DebugLog(<< "SdkManager::onNewLogin(): existing runner for context: " << contextStr.c_str() << " " << protocol.c_str() << " jsonApiUser: " << jsonApiUser << " json-user count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());
         LocalDebugLog("SdkManager::onNewLogin(): existing runner for context {} for {} jsonApiUser {} user-runner count: {} data-runner count: {} runner-user count: {}", contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
         Cpcapi2Runner* sdkInst = it->second;
         if (sdkInst)
         {
            if (sdkInst->getPhone())
            {
               // TODO: What if there are other users logged in to this runner, currently we only handle one user per runner, but this will have
               // to be updated if we do finally end up supporting multiple users on the same runner

               if (sdkInst->isAnyLoggedOut())
               {
                  LocalDebugLog("SdkManager::onNewLogin(): existing runner in logout mode: {} new {} jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
               }
               else
               {
                  mMapJsonApiUserToRunner[jsonApiUser] = it->second;
                  std::set<CPCAPI2::JsonApi::JsonApiUserHandle> jsonApiUsers;
                  if (mMapRunnerToJsonApiUsers.find(it->second) != mMapRunnerToJsonApiUsers.end())
                  {
                     jsonApiUsers = mMapRunnerToJsonApiUsers[it->second];
                  }

                  cpc::vector<cpc::string> permissions;
                  permissions.push_back("*");
                  mJsonApiServer->setJsonApiUserContext(jsonApiUser, sdkInst->getPhone(), permissions);
                  sdkInst->addJsonApiUser(jsonApiUser, args.isHttp);
                  if (!args.isHttp)
                  {
                     for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator j = jsonApiUsers.begin(); j != jsonApiUsers.end(); ++j)
                     {
                        mJsonApiServer->updateUser(*j, JsonApi::UpdateUserEvent(jsonApiUser, args.authToken, args.userIdentity));
                     }
                  }
                  jsonApiUsers.insert(jsonApiUser);
                  mMapRunnerToJsonApiUsers[it->second] = jsonApiUsers;
                  loginSuccess = true;
               }
            }
            else
            {
               // TODO: What if reconnection request occurs before previous cpcapi runner has been cleaned up for this context. Handle this.
               LocalDebugLog("SdkManager::onNewLogin(): phone not initialized for existing runner for context: {} {} jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
            }
         }
         else
         {
            LocalDebugLog("SdkManager::onNewLogin(): existing runner not initialized for context: {} {} jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
         }
      }
      else
      {
         DebugLog(<< "SdkManager::onNewLogin(): new runner for context: " << contextStr.c_str() << " " << protocol.c_str() << " jsonApiUser: " << jsonApiUser << " json-user count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());
         LocalDebugLog("SdkManager::onNewLogin(): new runner for context {} for {} jsonApiUser {} user-runner count: {} data-runner count: {} runner-user count: {}", contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
         Cpcapi2Runner* sdk = new Cpcapi2Runner(sNextSdkThreadPoolIdx % CPCAPI2_CONF_NUM_SDK_REACTORS, &mReactor, mPhone, mJsonApiServer, mPushServiceManager, contextStr);
         RemoteSync::RemoteSyncManagerInternal::getInternalInterface(sdk->getPhone())->addEventObserver(this);
         sNextSdkThreadPoolIdx++;
         mMapContextToRunner[contextStr] = sdk;
         mMapJsonApiUserToRunner[jsonApiUser] = sdk;
         std::set<CPCAPI2::JsonApi::JsonApiUserHandle> jsonApiUsers;
         jsonApiUsers.insert(jsonApiUser);
         mMapRunnerToJsonApiUsers[sdk] = jsonApiUsers;
         cpc::vector<cpc::string> permissions;
         permissions.push_back("*");
         mJsonApiServer->setJsonApiUserContext(jsonApiUser, sdk->getPhone(), permissions);
         sdk->addJsonApiUser(jsonApiUser, args.isHttp);
         loginSuccess = true;
      }
   }

   LocalDebugLog("SdkManager::onNewLogin(): login success {} for context {} for {} jsonApiUser {}", loginSuccess, contextStr.c_str(), protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());

   CPCAPI2::JsonApi::LoginResultEvent loginResult;
   loginResult.success = loginSuccess;
   loginResult.requestId = args.requestId;
   mJsonApiServer->sendLoginResult(jsonApiUser, loginResult);

   dump();

   return 0;
}

int SdkManager::onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::SessionStateEvent& args)
{
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator it = mMapJsonApiUserToRunner.find(jsonApiUser);
   if (it != mMapJsonApiUserToRunner.end())
   {
      Cpcapi2Runner* runner = it->second;
      cpc::string protocol = (runner->isHttp(jsonApiUser) ? "http" : "ws");
      UInt64 currentTimeMs = resip::ResipClock::getTimeMs();
      UInt64 cleanupIntervalMs(CPCAPI2_AGENT_CLEANUP_INTERVAL_SECONDS * 1000);
      resip::Data context = runner->getContext();

      if (runner->isLoggedOut(jsonApiUser))
      {
         LocalDebugLog("SdkManager::onSessionState(): remove cpcapi runner with {} jsonApiUser: {} context: {}", protocol.c_str(), jsonApiUser, context.c_str());
         std::lock_guard<std::recursive_mutex> lock(mRunnerMutex);

         std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator i = mMapRunnerToJsonApiUsers.find(it->second);
         if (i != mMapRunnerToJsonApiUsers.end())
         {
            cpc::vector<CPCAPI2::JsonApi::JsonApiUserHandle> wsJsonUsers;
            std::set<CPCAPI2::JsonApi::JsonApiUserHandle> wsUsers;
            runner->getWsJsonApiUsers(wsUsers);
            for (CPCAPI2::JsonApi::JsonApiUserHandle user : wsUsers) wsJsonUsers.push_back(user);
            std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
            runner->getHttpJsonApiUsers(httpUsers);

            i->second.erase(jsonApiUser);

            mMapJsonApiUserToRunner.erase(jsonApiUser);
            runner->removeJsonApiUser(jsonApiUser);

            LocalDebugLog("SdkManager::onSessionState(): user: {} http-user-count: {} user-to-runner: {}", jsonApiUser, httpUsers.size(), mMapJsonApiUserToRunner.size());

            for (CPCAPI2::JsonApi::JsonApiUserHandle user : httpUsers)
            {
               i->second.erase(user);
               mMapJsonApiUserToRunner.erase(user);
               runner->removeJsonApiUser(user);
            }

            // If no other ws json users exist, proceed to destroy the runner
            if (runner->countWsJsonApiUsers() == 0)
            {
               LocalDebugLog("SdkManager::onSessionState(): destroy cpcapi runner: {} with context: {}", (void*)runner, context.c_str());
               mMapContextToRunner.erase(context);
               mMapRunnerToJsonApiUsers.erase(i);
               runner->setDestroyed();
               mJsonApiServer->destroyRunner(new CPCAPI2::JsonApi::JsonApiServerInternal::RunnerInfo(runner, wsJsonUsers));
            }
            else
            {
               LocalInfoLog("SdkManager::onSessionState(): holding off on destroying runner for jsonApiUser: {} context: {} as it is also mapped to other json user handles", jsonApiUser, context.c_str());
            }
         }
      }
      else if (args.isActive || args.disconnectTimeMs > (currentTimeMs - cleanupIntervalMs))
      {
         // ensure the user is tracked if they are active, or were active recently
         runner->addJsonApiUser(jsonApiUser, false);
         StackLog(<< "SdkManager::onSessionState(): adding " << protocol.c_str() << " jsonApiUser: " << jsonApiUser << " user-runner count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());
      }
      else
      {
         std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator i = mMapRunnerToJsonApiUsers.find(it->second);
         if (i != mMapRunnerToJsonApiUsers.end())
         {
            std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
            runner->getHttpJsonApiUsers(httpUsers);

            mMapJsonApiUserToRunner.erase(jsonApiUser);
            runner->removeJsonApiUser(jsonApiUser);

            std::set<CPCAPI2::JsonApi::JsonApiUserHandle> jsonApiUsers = i->second;
            jsonApiUsers.erase(jsonApiUser);

            for (CPCAPI2::JsonApi::JsonApiUserHandle user : httpUsers)
            {
               mMapJsonApiUserToRunner.erase(user);
               runner->removeJsonApiUser(user);
               jsonApiUsers.erase(user);
            }

            i->second = jsonApiUsers;
            mJsonApiServer->destroyUser(jsonApiUser);
            LocalDebugLog("SdkManager::onSessionState(): runner: {} still has {} ws users", (void*)runner, jsonApiUsers.size());
         }

         LocalDebugLog("SdkManager::onSessionState(): removing {} jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", protocol.c_str(), jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
      }
   }
   return 0;
}

int SdkManager::onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args)
{
   StackLog(<< "SdkManager::onReLogin(): jsonApiUser: " << jsonApiUser << " newJsonApiUser: " << args.newJsonApiUser << " user-runner count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());
   return 0;
}

int SdkManager::onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args)
{
   StackLog(<< "SdkManager::onPreLogout(): jsonApiUser: " << jsonApiUser << " endpoint: " << args.userIdentity << " user-runner count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());

   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator it = mMapJsonApiUserToRunner.find(jsonApiUser);
   if (it != mMapJsonApiUserToRunner.end())
   {
      Cpcapi2Runner* runner = it->second;
      resip::Data context = runner->getContext();
      runner->logoutJsonApiUser(jsonApiUser);

      LocalDebugLog("SdkManager::onPreLogout(): logout jsonApiUser: {} with context: {} user-runner count: {} data-runner count: {} runner-user count: {}", jsonApiUser, context.c_str(), mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
   }
   else
   {
      LocalDebugLog("SdkManager::onPreLogout(): invalid jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
   }

   return 0;
}

int SdkManager::onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args)
{
   StackLog(<< "SdkManager::onLogout(): jsonApiUser: " << jsonApiUser << " endpoint: " << args.userIdentity << " user-runner count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());

   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator it = mMapJsonApiUserToRunner.find(jsonApiUser);
   if (it != mMapJsonApiUserToRunner.end())
   {
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = true;
      mJsonApiServer->sendLogoutResult(jsonApiUser, logoutResult);

      Cpcapi2Runner* runner = it->second;
      resip::Data context = runner->getContext();
      runner->logoutJsonApiUser(jsonApiUser);

      // Don't delete the runner until the web session is destroyed, which will be confirmed in onSessionState. The session state will
      // be triggered when the web socket is closed.

      LocalDebugLog("SdkManager::onLogout(): logout jsonApiUser: {} with context: {} user-runner count: {} data-runner count: {} runner-user count: {}", jsonApiUser, context.c_str(), mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
   }
   else
   {
      LocalDebugLog("SdkManager::onLogout(): invalid jsonApiUser: {} user-runner count: {} data-runner count: {} runner-user count: {}", jsonApiUser, mMapJsonApiUserToRunner.size(), mMapContextToRunner.size(), mMapRunnerToJsonApiUsers.size());
      CPCAPI2::JsonApi::LogoutResultEvent logoutResult;
      logoutResult.success = false;
      mJsonApiServer->sendLogoutResult(jsonApiUser, logoutResult);
   }

   dump();

   return 0;
}

// XmppAgentHandler

int SdkManager::onXmppAgentQueryListResult(const CPCAPI2::XmppAgent::XmppAgentQueryListResult& args)
{
   for (cpc::vector<XmppAgent::XmppPushRegistrationHandle>::const_iterator i = args.registrationList.begin(); i != args.registrationList.end(); ++i)
   {
      mAgentQueryListResult.push_back(*i);
   }
   mQueryListSignal.notify_one();
   return 0;
}

int SdkManager::onXmppAgentQueryInfoResult(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppAgentQueryInfoResult& args)
{
   mAgentQueryInfoResult[pushRegistration] = args;
   mQueryInfoSignal.notify_one();
   return 0;
}

// PushNotificationEndpointHandler

int SdkManager::onPushRegistrationQueryListResult(const CPCAPI2::PushEndpoint::PushRegistrationQueryListResult& args)
{
   for (cpc::vector<PushEndpoint::PushNotificationEndpointHandle>::const_iterator i = args.registrationList.begin(); i != args.registrationList.end(); ++i)
   {
      mPushQueryListResult.push_back(*i);
   }
   mQueryListSignal.notify_one();
   return 0;
}

int SdkManager::onPushRegistrationQueryInfoResult(CPCAPI2::PushEndpoint::PushNotificationEndpointHandle pushRegistration, const CPCAPI2::PushEndpoint::PushRegistrationQueryInfoResult& args)
{
   mPushQueryInfoResult[pushRegistration] = args;
   mQueryInfoSignal.notify_one();
   return 0;
}

void SdkManager::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == CONTEXT_CLEANUP_TIMERID)
   {
      std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator it = mMapJsonApiUserToRunner.begin();
      for (; it != mMapJsonApiUserToRunner.end(); ++it)
      {
         mJsonApiServer->queryUserState(it->first);
      }
      mPhone->runOnSdkModuleThread(&SdkManager::finalizeCleanup, this);
      mContextCleanupTimer.async_wait(this, CONTEXT_CLEANUP_TIMERID, NULL);
   }
}

void SdkManager::finalizeCleanup(void* context)
{
   SdkManager* sdkManager = (SdkManager*)context;
   sdkManager->mReactor.post(resip::resip_bind(&SdkManager::finalizeCleanupImpl, sdkManager));
}

void SdkManager::finalizeCleanupImpl()
{
   std::set<Cpcapi2Runner*> forCleanup;
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator it = mMapJsonApiUserToRunner.begin();
   for (; it != mMapJsonApiUserToRunner.end(); )
   {
      Cpcapi2Runner* runner = it->second;
      if (runner->countJsonApiUsers() == 0)
      {
         std::map<resip::Data, Cpcapi2Runner*>::iterator itCtx = mMapContextToRunner.begin();
         for (; itCtx != mMapContextToRunner.end(); )
         {
            if (itCtx->second == runner)
            {
               mMapContextToRunner.erase(itCtx);
               break;
            }
            itCtx++;
         }

         DebugLog(<< "Cleaning up stale Cpcapi2Runner for jsonApiUser " << it->first);
         it = mMapJsonApiUserToRunner.erase(it);
         forCleanup.insert(runner);
         continue;
      }
      it++;
   }

   std::set<Cpcapi2Runner*>::iterator itCln = forCleanup.begin();
   for (; itCln != forCleanup.end(); ++itCln)
   {
      mMapRunnerToJsonApiUsers.erase(*itCln);
      (*itCln)->shutdown();
      delete (*itCln);
   }

   DebugLog(<< "SdkManager::finalizeCleanupImpl(): user-runner count: " << mMapJsonApiUserToRunner.size() << " data-runner count: " << mMapContextToRunner.size() << " runner-user count: " << mMapRunnerToJsonApiUsers.size());
}

void SdkManager::addSdkObserver(void* context)
{
   SdkManager* sdkManager = (SdkManager*)context;
   // CPCAPI2::JsonApi::JsonApiServerInternal::getInternalInterface(sdkManager->mPhone)->addEventObserver(sdkManager);
   CPCAPI2::PushService::PushNotificationServiceManagerInternal::getInternalInterface(sdkManager->mPhone)->addEventObserver(sdkManager);
}

int SdkManager::onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   mHealthCheckRedis.mValue = args.success;

   if (!args.success)
   {
      ErrLog(<< "error registering server in Orch Service database");
   }
   return 0;
}

void SdkManager::stop_agent(int signal)
{
   std::cout << std::endl << "***** signal received to stop xmpp agent process: " << signal << " *****" << std::endl;
   SdkManager::getInstance()->setServerShutdownFlag();
   std::this_thread::sleep_for(std::chrono::milliseconds(2000)); // Ensure time to update the db server
   SdkManager::getInstance()->shutdown();
   exit(signal);
}

int SdkManager::onRequestServiceResult(int requestHandle, const CPCAPI2::OrchestrationServer::RequestServiceResult& args)
{
   // bliu: TODO check result

   return 0;
}

int SdkManager::onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args)
{
   // bliu: TODO check result

   return 0;
}

int SdkManager::onQueryServerTtlResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args)
{
   mHealthCheckRedis.mValue = args.expiresInMs != -1;

   return 0;
}

int SdkManager::onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args)
{
   // bliu: TODO check result

   return 0;
}

int SdkManager::onNotificationSuccess(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationSuccessEvent& evt)
{
   mHealthCheckPushNotification.mValue = true;

   return 0;
}

int SdkManager::onNotificationFailure(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationFailureEvent& evt)
{
   mHealthCheckPushNotification.mValue = evt.statusCode == StatusCode_ExpiredDeviceToken;

   return 0;
}

int SdkManager::onNotificationServiceRegistration(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& evt)
{
   return 0;
}

int SdkManager::onSetAccounts(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SetAccountsEvent& evt)
{
   return 0;
}

int SdkManager::onNotificationUpdate(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::NotificationUpdateEvent& evt)
{
   return 0;
}

int SdkManager::onMessageReactions(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageReactionsEvent& evt)
{
   return 0;
}

int SdkManager::onFetchMessagesReactionsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& evt)
{
   return 0;
}

int SdkManager::onSyncItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& evt)
{
   return 0;
}

int SdkManager::onUpdateItemComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& evt)
{
   return 0;
}

int SdkManager::onFetchRangeComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& evt)
{
   return 0;
}

int SdkManager::onFetchConversationsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& evt)
{
   return 0;
}

int SdkManager::onConversationUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& evt)
{
   return 0;
}

int SdkManager::onUpdateItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemsCompleteEvent& evt)
{
  return 0;
}

int SdkManager::onMessageCount(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageCountEvent& evt)
{
   return 0;
}

int SdkManager::onError(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnErrorEvent& evt)
{
   return 0;
}

int SdkManager::onConnectionState(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnConnectionStateEvent& evt)
{
   std::lock_guard<std::recursive_mutex> lock(mHealthCheckRemoteSync.mMutex);

   switch (evt.currentState)
   {
   case CPCAPI2::RemoteSync::ConnectionState_Connected:
      mHealthCheckRemoteSync.mValues[sessionHandle] = true;
      break;

   case CPCAPI2::RemoteSync::ConnectionState_Disconnected:
   case CPCAPI2::RemoteSync::ConnectionState_Failed:
      mHealthCheckRemoteSync.mValues[sessionHandle] = false;
      break;

   default:
      break;
   }

   return 0;
}

int SdkManager::onTimestampDelta(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& evt)
{
   return 0;
}

int SdkManager::onItemsUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ItemsUpdatedEvent& evt)
{
   return 0;
}

void SdkManager::dump()
{
   if (!mDebugDumpEnabled) return;

   for (std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator i = mMapJsonApiUserToRunner.begin(); i != mMapJsonApiUserToRunner.end(); ++i)
   {
      resip::Data context = i->second->getContext();
      LocalDebugLog("SdkManager::dump(): user-runner: jsonApiUser: {} context: {} runner: {}", (i->first), context.c_str(), (void*)(i->second));
   }

   for (std::map<resip::Data, Cpcapi2Runner*>::iterator j = mMapContextToRunner.begin(); j != mMapContextToRunner.end(); ++j)
   {
      resip::Data context = j->first;
      LocalDebugLog("SdkManager::dump(): context-runner: context: {} runner: {} phone: {}", context.c_str(), (void*)(j->second), (void*)(j->second->getPhone()));
   }

   for (std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator k = mMapRunnerToJsonApiUsers.begin(); k != mMapRunnerToJsonApiUsers.end(); ++k)
   {
      std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
      std::set<CPCAPI2::JsonApi::JsonApiUserHandle> wsUsers;
      k->first->getHttpJsonApiUsers(httpUsers);
      k->first->getWsJsonApiUsers(wsUsers);

      std::ostringstream os1;
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator m = (k->second.begin()); m != (k->second.end()); ++m)
      {
         os1 << " " << (*m);
      }

      std::ostringstream os2;
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator n = httpUsers.begin(); n != httpUsers.end(); ++n)
      {
         os2 << " " << (*n);
      }

      std::ostringstream os3;
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator o = wsUsers.begin(); o != wsUsers.end(); ++o)
      {
         os3 << " " << (*o);
      }

      resip::Data context = k->first->getContext();
      LocalDebugLog("SdkManager::dump(): runner-users: runner: {} context: {} jsonApiUsers: {} httpUsers: {} wsUsers: {}", (void*)(k->first), context.c_str(), os1.str().c_str(), os2.str().c_str(), os3.str().c_str());
   }
}

// HttpServerRequestHandler
cpc::string SdkManager::HealthCheckBoolean::output()
{
   std::lock_guard<std::recursive_mutex> lock(mValue.mutex);

   std::ostringstream ss;
   ss << (mValue ? "All seems well" : "Failure occurred") << std::endl;

   ss << std::endl;
   ss << "Status change history (most recent " << mValue.MaxTracked << " records):" << std::endl;

   for (auto& i : mValue.updates)
   {
      time_t t = std::chrono::system_clock::to_time_t(i.second);
      ss << std::put_time(std::localtime(&t), "%c %Z") << ": " << (i.first ? "success" : "failure") << std::endl;
   }

   ss << std::endl;
   ss << BUILD_INFO_STRING << std::endl;

   return ss.str().c_str();
}

cpc::string SdkManager::HealthCheckMap::output()
{
   std::lock_guard<std::recursive_mutex> lock(mMutex);

   std::ostringstream iss;

   bool good = true;
   for (auto& i : mValues)
   {
      iss << "SessionHandle=" << i.first << ": " << (i.second ? "success" : "failure") << std::endl;

      if (!i.second) good = false;
   }

   std::ostringstream ss;
   ss << (good ? "All seems well" : "Failure occurred") << std::endl;

   ss << std::endl;
   ss << "Status details:" << std::endl;
   ss << iss.str() << std::endl;

   ss << std::endl;
   ss << BUILD_INFO_STRING << std::endl;

   return ss.str().c_str();
}

cpc::string SdkManager::UserSessionCount::output()
{
   std::lock_guard<std::recursive_mutex> lock(mMutex);

   std::ostringstream ss;
   ss << m_ContextToRunnerMap.size() << std::endl;

   ss << std::endl;
   ss << BUILD_INFO_STRING << std::endl;

   return ss.str().c_str();

   /*
   std::ostringstream ss;
   ss << "Context-Runner Count: " << m_ContextToRunnerMap.size();
   ss << " Runner-Users Count: " << m_RunnerToUsersMap.size();
   ss << " User-Runner Count: " << m_UserToRunnerMap.size();
   ss << std::endl;

   return ss.str().c_str(); */
}

cpc::string SdkManager::BuildInfo::output()
{
   return BUILD_INFO_STRING;
}

cpc::string SdkManager::HealthPushRegistrationInfo::output()
{
   /*
   std::mutex queryMutex;
   std::unique_lock<std::mutex> aLock(queryMutex);
   m_Reactor.post(resip::resip_bind(&SdkManager::HealthPushRegistrationInfo::outputImpl, this));
   m_QueryHttpSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS)); */
   outputImpl();
   return m_Stream.str().c_str();
}

void SdkManager::HealthPushRegistrationInfo::outputImpl()
{
   std::lock_guard<std::recursive_mutex> lock(m_RunnerMutex);

   m_Stream.str("");
   m_Stream.clear();
   m_Stream << "Push Registration Info (" << m_RunnerToUsersMap.size() << ")" << std::endl;
   m_Stream << "======================" << std::endl << std::endl;

   if (m_RunnerToUsersMap.empty())
   {
      return;
   }

   // TODO: sanctity of the runner or phone cannot be assured when iterating through the loop, e.g. logout triggers runner and phone destruction
   // TODO: sanctity of the runner-to-user map cannot be assured when iterating through the loop, e.g. login or logout modifies the runner list
   // TODO: optimize this
   std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>> tmpRunnerToUsersMap = m_RunnerToUsersMap;
   for (std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator i = tmpRunnerToUsersMap.begin(); i != tmpRunnerToUsersMap.end(); ++i)
   {
      Cpcapi2Runner* runner = i->first;
      if (runner)
      {
         m_Stream << "runner:" << runner << " ";
         m_Stream << "thread-id:" << runner->getThreadId() << " ";
         std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
         std::set<CPCAPI2::JsonApi::JsonApiUserHandle> wsUsers;
         resip::Data context = runner->getContext();
         runner->getHttpJsonApiUsers(httpUsers);
         runner->getWsJsonApiUsers(wsUsers);

         m_Stream << "context:" << context.c_str() << " ";
         m_Stream << "http-users:{";
         for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator j = httpUsers.begin(); j != httpUsers.end(); ++j)
         {
            if (j == httpUsers.begin())
               m_Stream << (*j);
            else
               m_Stream << "," << (*j);
         }
         m_Stream << "} ";
         m_Stream << "ws-users:{";
         for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator k = wsUsers.begin(); k != wsUsers.end(); ++k)
         {
            if (k == wsUsers.begin())
               m_Stream << (*k);
            else
               m_Stream << "," << (*k);
         }
         m_Stream << "} ";

         CPCAPI2::Phone* phone = runner->getPhone();
         if (phone)
         {
            m_Stream << "phone:" << phone << " ";
            std::unique_lock<std::mutex> aLock(m_QueryMutex);
            PushEndpoint::PushNotificationEndpointManagerInternal* pushMgr = dynamic_cast<PushEndpoint::PushNotificationEndpointManagerInternal*>(CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getInterface(phone));
            pushMgr->addEventObserver(m_Manager);
            pushMgr->queryPushRegistrationList();
            m_QueryListSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS));

            std::vector<PushEndpoint::PushNotificationEndpointHandle> tmpListResult = m_QueryListResult;
            m_QueryListResult.clear();
            for (std::vector<PushEndpoint::PushNotificationEndpointHandle>::iterator j = tmpListResult.begin(); j != tmpListResult.end(); ++j)
            {
               pushMgr->queryPushRegistrationInfo(*j);
               m_QueryInfoSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS));

               std::map<PushEndpoint::PushNotificationEndpointHandle, PushEndpoint::PushRegistrationQueryInfoResult> tmpInfoResult = m_QueryInfoResult;
               m_Stream << "push-endpoint-registrations:{";
               for (std::map<PushEndpoint::PushNotificationEndpointHandle, PushEndpoint::PushRegistrationQueryInfoResult>::iterator k = tmpInfoResult.begin(); k != tmpInfoResult.end(); ++k)
               {
                  m_Stream << "{push-endpoint-handle:" << k->second.pushEndpointHandle << " ";
                  m_Stream << "json-user-handle:" << k->second.jsonUserHandle << " ";
                  m_Stream << "push-device-handle:" << k->second.pushEndpointId << " ";
                  m_Stream << "push-network-type:" << k->second.pushNetworkType << " ";
                  m_Stream << "apns-topic:" << k->second.apnsTopic << " ";
                  m_Stream << "device-token:" << k->second.deviceToken << "}";
               }
               m_Stream << "} ";

               m_QueryInfoResult.clear();
            }
            pushMgr->removeEventObserver(m_Manager);
         }
         m_Stream << std::endl << std::endl;
      }
   }

   m_Stream << std::endl;

   // m_QueryHttpSignal.notify_one();
}

cpc::string SdkManager::HealthXmppRegistrationInfo::output()
{
   /*
   std::mutex queryMutex;
   std::unique_lock<std::mutex> aLock(queryMutex);
   m_Reactor.post(resip::resip_bind(&SdkManager::HealthXmppRegistrationInfo::outputImpl, this));
   m_QueryHttpSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS)); */
   outputImpl();
   return m_Stream.str().c_str();
}

void SdkManager::HealthXmppRegistrationInfo::outputImpl()
{
   std::lock_guard<std::recursive_mutex> lock(m_RunnerMutex);

   m_Stream.str("");
   m_Stream.clear();
   m_Stream << "XMPP Registration Info (" << m_RunnerToUsersMap.size() << ")" << std::endl;
   m_Stream << "======================" << std::endl << std::endl;

   if (m_RunnerToUsersMap.empty())
   {
      return;
   }

   // TODO: sanctity of the runner or phone cannot be assured when iterating through the loop, e.g. logout triggers runner and phone destruction
   // TODO: sanctity of the runner-to-user map cannot be assured when iterating through the loop, e.g. login or logout modifies the runner list
   // TODO: optimize this
   std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>> tmpRunnerToUsersMap = m_RunnerToUsersMap;
   for (std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator i = tmpRunnerToUsersMap.begin(); i != tmpRunnerToUsersMap.end(); ++i)
   {
      Cpcapi2Runner* runner = i->first;
      if (runner)
      {
         m_Stream << "runner:" << runner << " ";
         m_Stream << "thread-id:" << runner->getThreadId() << " ";
         std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
         std::set<CPCAPI2::JsonApi::JsonApiUserHandle> wsUsers;
         resip::Data context = runner->getContext();
         runner->getHttpJsonApiUsers(httpUsers);
         runner->getWsJsonApiUsers(wsUsers);

         m_Stream << "context:" << context.c_str() << " ";
         m_Stream << "http-users:{";
         for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator j = httpUsers.begin(); j != httpUsers.end(); ++j)
         {
            if (j == httpUsers.begin())
               m_Stream << (*j);
            else
               m_Stream << "," << (*j);
         }
         m_Stream << "} ";
         m_Stream << "ws-users:{";
         for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator k = wsUsers.begin(); k != wsUsers.end(); ++k)
         {
            if (k == wsUsers.begin())
               m_Stream << (*k);
            else
               m_Stream << "," << (*k);
         }
         m_Stream << "} ";

         CPCAPI2::Phone* phone = runner->getPhone();
         if (phone)
         {
            m_Stream << "phone:" << phone << " ";
            std::unique_lock<std::mutex> aLock(m_QueryMutex);
            XmppAgent::XmppAgentManagerInternal* xmppAgent = dynamic_cast<XmppAgent::XmppAgentManagerInternal*>(CPCAPI2::XmppAgent::XmppAgentManager::getInterface(phone));
            xmppAgent->addSdkObserver(m_Manager);
            xmppAgent->queryXmppRegistrationList();
            m_QueryListSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS));

            std::vector<XmppAgent::XmppPushRegistrationHandle> tmpListResult = m_QueryListResult;
            m_QueryListResult.clear();
            for (std::vector<XmppAgent::XmppPushRegistrationHandle>::iterator j = tmpListResult.begin(); j != tmpListResult.end(); ++j)
            {
               xmppAgent->queryXmppRegistrationInfo(*j);
               m_QueryInfoSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS));

               std::map<XmppAgent::XmppPushRegistrationHandle, XmppAgent::XmppAgentQueryInfoResult> tmpInfoResult = m_QueryInfoResult;
               m_Stream << "xmpp-push-registrations:{";
               for (std::map<XmppAgent::XmppPushRegistrationHandle, XmppAgent::XmppAgentQueryInfoResult>::iterator k = tmpInfoResult.begin(); k != tmpInfoResult.end(); ++k)
               {
                  m_Stream << "{xmpp-push-handle:" << k->second.xmppPushRegistration << " ";
                  m_Stream << "account:" << k->second.xmppAccount << " ";
                  m_Stream << "xmpp-handle:" << k->second.xmppAccountHandle << " ";
                  m_Stream << "push-device-handle:" << k->second.pushNotificationDev << " ";
                  m_Stream << "push-service-handle:" << k->second.pushNotificationServiceHandle << " ";
                  m_Stream << "json-user-handle:" << k->second.jsonUserHandle << " ";
                  m_Stream << "sync-session-handle:" << k->second.syncSessionHandle << " ";
                  m_Stream << "registerForPush:" << k->second.isRegisteredForPush << " ";
                  m_Stream << "isLoggedOut:" << k->second.isLoggedOut << " ";
                  m_Stream << "serviceDownAtMs:" << k->second.serviceDownAtMs << "}";
               }
               m_Stream << "} ";
               m_QueryInfoResult.clear();
            }
            xmppAgent->removeSdkObserver(m_Manager);
         }
         m_Stream << std::endl << std::endl;
      }
   }

   m_Stream << std::endl;

   // m_QueryHttpSignal.notify_one();
}

cpc::string SdkManager::HealthRunnerInfo::output()
{
   /*
   std::mutex queryMutex;
   std::unique_lock<std::mutex> aLock(queryMutex);
   m_Reactor.post(resip::resip_bind(&SdkManager::HealthRunnerInfo::outputImpl, this));
   m_QueryHttpSignal.wait_for(aLock, std::chrono::seconds(CPCAPI2_QUERY_TIMEOUT_SECONDS)); */
   outputImpl();
   return m_Stream.str().c_str();
}

void SdkManager::HealthRunnerInfo::outputImpl()
{
   std::lock_guard<std::recursive_mutex> lock(m_RunnerMutex);

   std::map<resip::Data, Cpcapi2Runner*> tmpContextToRunner = m_ContextToRunnerMap;
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*> tmpUserToRunner = m_UserToRunnerMap;
   std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>> tmpRunnerToUsers = m_RunnerToUsersMap;

   m_Stream.str("");
   m_Stream.clear();
   m_Stream << "Agent Runner Info" << std::endl;
   m_Stream << "=================" << std::endl << std::endl;

   m_Stream << "context-runner: (" << tmpContextToRunner.size() << ")" << std::endl << "{" << std::endl;
   for (std::map<resip::Data, Cpcapi2Runner*>::iterator j = tmpContextToRunner.begin(); j != tmpContextToRunner.end(); ++j)
   {
      resip::Data context = j->first;
      if (j->second)
      {
         m_Stream << "  context:" << context.c_str() << " runner:" << (void*)(j->second) << " phone:" << (void*)(j->second->getPhone()) << std::endl;
      }
   }
   m_Stream << "}" << std::endl << std::endl;

   m_Stream << "runner-users: (" << tmpRunnerToUsers.size() << ")" << std::endl << "{" << std::endl;
   for (std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>::iterator k = tmpRunnerToUsers.begin(); k != tmpRunnerToUsers.end(); ++k)
   {
      std::set<CPCAPI2::JsonApi::JsonApiUserHandle> httpUsers;
      std::set<CPCAPI2::JsonApi::JsonApiUserHandle> wsUsers;
      k->first->getHttpJsonApiUsers(httpUsers);
      k->first->getWsJsonApiUsers(wsUsers);
      resip::Data context = k->first->getContext();

      m_Stream << "  runner:" << (void*)(k->first) << " context:" << context.c_str() << " {";

      std::ostringstream os1;
      os1 << "json-users:{";
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator m = (k->second.begin()); m != (k->second.end()); ++m)
      {
         if (m == (k->second.begin()))
            os1 << (*m);
         else
            os1 << " " << (*m);
      }
      os1 << "}";

      std::ostringstream os2;
      os2 << " http-users:{";
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator n = httpUsers.begin(); n != httpUsers.end(); ++n)
      {
         if (n == httpUsers.begin())
            os2 << (*n);
         else
            os2 << " " << (*n);
      }
      os2 << "}";

      std::ostringstream os3;
      os3 << " ws-users:{";
      for (std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator o = wsUsers.begin(); o != wsUsers.end(); ++o)
      {
         if (o == wsUsers.begin())
            os3 << (*o);
         else
            os3 << " " << (*o);
      }
      os3 << "}";

      m_Stream << os1.str().c_str() << os2.str().c_str() << os3.str().c_str() << "}" << std::endl;
   }
   m_Stream << "}" << std::endl;

   m_Stream << "user-runner: (" << tmpUserToRunner.size() << ")" << std::endl << "{" << std::endl;
   for (std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>::iterator i = tmpUserToRunner.begin(); i != tmpUserToRunner.end(); ++i)
   {
      if (i->second)
      {
         resip::Data context = i->second->getContext();
         m_Stream << "  json-user-handle:" << (i->first) << " context:" << context.c_str() << " runner:" << (void*)(i->second) << std::endl;
      }
   }
   m_Stream << "}" << std::endl << std::endl;

   // m_QueryHttpSignal.notify_one();
}

}

}
