<html>
    <head>
    	<meta charset="UTF-8">
		<title>CounterPath SDK in the Cloud</title>
		<!-- Include basic JQuery and JQuery UI files -->
		<script src="lib/jquery/jquery.js"></script>		
  		<link href="lib/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css">
		<script src="lib/jquery-ui/jquery-ui.min.js"></script>		
		<style>
#infoDiv { 
    height: 100px; 
    position: fixed; 
    bottom:0%;
    width:90%; 
    background-color: #999898; 
    opacity: 1;
	padding: 6px;
	overflow: auto;
}		
		</style>
    </head>
	<body>
		<script src="sdkconnector.js"></script>
		<script src="call.js"></script>
		<script src="index.js"></script>
	<h1>CounterPath SDK</h1>
	<br>
	<br>
	<input type="text" name="loginContext" value="<EMAIL>" id="inputLoginContext" />
	<input type="submit" value="Login" onclick="doLogin()" />
	<br>
	<br>
    <table id="conversationsTable">
	<tbody id="conversationsTableBody">
	<tbody>
	</table>

	Enter a number to call:
	<input type="text" name="destAddr" value="<EMAIL>" id="inputDestAddr" />
	<input type="submit" value="Call" onclick="makeCall()" />
	
    <div id="infoDiv">
	<text
	</div>
	<br>
    </body>
</html>
