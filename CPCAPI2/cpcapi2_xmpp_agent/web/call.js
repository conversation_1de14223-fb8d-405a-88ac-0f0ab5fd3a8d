
function SipConversationManager() {
	this.conversationState = null;
	this.callback = null;
	this.sdkConnector = null;
	this.createConversationHandler = null;
}

SipConversationManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

SipConversationManager.prototype.setCallback = function(_account, _callback) {
	this.callback = _callback;
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "SipConversationJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	setHandlerFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

SipConversationManager.prototype.conversationState = function() {
	return this.conversationState;
}

SipConversationManager.prototype.createConversation = function(_account, _createdCb) {
	this.createConversationHandler = _createdCb;
	var createFunc = {};
	createFunc.moduleId = "SipConversationJsonApi";
	createFunc.functionObject = {};
	createFunc.functionObject.functionName = "createConversation";
	createFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(createFunc));
	return this;
}

SipConversationManager.prototype.addParticipant = function(_conversation, _destAddr) {
	var addPartFunc = {};
	addPartFunc.moduleId = "SipConversationJsonApi";
	addPartFunc.functionObject = {};
	addPartFunc.functionObject.functionName = "addParticipant";
	addPartFunc.functionObject.conversation = _conversation;
	addPartFunc.functionObject.participantAddress = _destAddr;
	this.sdkConnector.send(JSON.stringify(addPartFunc));
	return this;
}

SipConversationManager.prototype.start = function(_conversation) {
	var startFunc = {};
	startFunc.moduleId = "SipConversationJsonApi";
	startFunc.functionObject = {};
	startFunc.functionObject.functionName = "start";
	startFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(startFunc));
	return this;
}

SipConversationManager.prototype.end = function(_conversation) {
	var endFunc = {};
	endFunc.moduleId = "SipConversationJsonApi";
	endFunc.functionObject = {};
	endFunc.functionObject.functionName = "end";
	endFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(endFunc));
	return this;
}

SipConversationManager.prototype.requestStateAllConversations = function() {
	var requestStateFunc = {};
	requestStateFunc.moduleId = "SipConversationJsonApi";
	requestStateFunc.functionObject = {};
	requestStateFunc.functionObject.functionName = "requestStateAllConversations";
	this.sdkConnector.send(JSON.stringify(requestStateFunc));
	return this;
}

SipConversationManager.prototype.configureMedia = function(_conversation, _mediaType, _mediaDirection, _mediaCryptoSuite, _mediaEncryptionMode, _secureMediaRequired) {
	var configMediaFunc = {};
	configMediaFunc.moduleId = "SipConversationJsonApi";
    configMediaFunc.functionObject = {};
	configMediaFunc.functionObject.functionName = "configureMedia";
	configMediaFunc.functionObject.conversation = _conversation;
	configMediaFunc.functionObject.mediaType = _mediaType;
	configMediaFunc.functionObject.mediaDirection = _mediaDirection;
	configMediaFunc.functionObject.mediaCryptoSuite = _mediaCryptoSuite;
	configMediaFunc.functionObject.mediaEncryptionMode = _mediaEncryptionMode;
	configMediaFunc.functionObject.secureMediaRequired = _secureMediaRequired;
	this.sdkConnector.send(JSON.stringify(configMediaFunc));
    return this;
}

SipConversationManager.prototype.sendMediaChangeRequest = function(_conversation) {
	var sendMediaChangeRequestFunc = {};
	sendMediaChangeRequestFunc.moduleId = "SipConversationJsonApi";
	sendMediaChangeRequestFunc.functionObject = {};
	sendMediaChangeRequestFunc.functionObject.functionName = "sendMediaChangeRequest";
	sendMediaChangeRequestFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(sendMediaChangeRequestFunc));
	return this;
}

SipConversationManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.functionObject.functionName == "createConversationResult") {
		if (this.createConversationHandler) {
			this.createConversationHandler(_jsonEventObj.functionObject.conversation);
		}
		this.createConversationHandler = null;
	}
	else {
		if (_jsonEventObj.functionObject.functionName == "onConversationState") {
			this.conversationState = _jsonEventObj.functionObject.conversationStateArray;
		}
		this.callback(_jsonEventObj.functionObject);
    }
	return this;
}
