#pragma once

#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <jsonapi/JsonApiServerInternal.h>
#include <jsonapi/JsonApiServerHandler.h>
#include <jsonapi/JsonApiServerSendTransport.h>
#include <jsonapi/HttpServerInternal.h>
#include <phone/PhoneInternal.h>
#include <servicenotifications_server/ServiceNotificationsManager.h>
#include <orchestration_server/OrchestrationServer.h>
#include <orchestration_server/OrchestrationServerHandler.h>
#include <push_service/PushNotificationServiceHandler.h>
#include <push_endpoint/PushNotificationEndpointHandler.h>
#include <remotesync/RemoteSyncHandler.h>
#include <xmpp_agent/XmppAgentHandler.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>
#include <rutil/DeadlineTimer.hxx>

#include <atomic>
#include <csignal>
#include <chrono>
#include <utility>
#include <mutex>
#include <list>
#include <set>
#include <condition_variable>
#include <mutex>

namespace CPCAPI2
{

class LocalLogger;

namespace Agent
{

class Cpcapi2Runner;

class SdkManager : public CPCAPI2::PhoneHandler,
                   public CPCAPI2::JsonApi::JsonApiServerHandler,
                   public CPCAPI2::OrchestrationServer::OrchestrationServerHandler,
                   public CPCAPI2::PushService::PushNotificationServiceHandler,
                   public CPCAPI2::PushEndpoint::PushNotificationEndpointHandler,
                   public CPCAPI2::RemoteSync::RemoteSyncHandler,
                   public CPCAPI2::XmppAgent::XmppAgentHandler,
                   public resip::DeadlineTimerHandler
{

public:

   struct LoginContext
   {
      cpc::string authProvider;
      cpc::string username;
      cpc::string password;

      cpc::string getUrl() { return (username + "@" + authProvider); }
   };

   static SdkManager* getInstance();

   virtual~ SdkManager();

   void run();
   void shutdown();
   void join();

   void handleSdkCallback();

   PhoneInternal* getPhone() { return mPhone; }

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args) OVERRIDE;
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args) OVERRIDE;
   virtual int onLicensingSuccess() OVERRIDE { return 0; }

   // JsonApiServerHandler
   virtual int onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args) OVERRIDE;
   virtual int onSessionState(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::SessionStateEvent& args) OVERRIDE;
   virtual int onLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::LogoutEvent& args) OVERRIDE;
   virtual int onReLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ReLoginEvent& args) OVERRIDE;
   virtual int onPreLogout(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::PreLogoutEvent& args) OVERRIDE;

   // OrchestrationServerHandler
   virtual int onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args) OVERRIDE;
   virtual int onRequestServiceResult(int requestHandle, const CPCAPI2::OrchestrationServer::RequestServiceResult& args) OVERRIDE;
   virtual int onQueryServersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServersResult& args) OVERRIDE;
   virtual int onQueryServerTtlResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerTtlResult& args) OVERRIDE;
   virtual int onQueryServerUsersResult(int requestHandle, const CPCAPI2::OrchestrationServer::QueryServerUsersResult& args) OVERRIDE;

   // PushNotificationServiceHandler
   virtual int onNotificationSuccess(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationSuccessEvent& evt) OVERRIDE;
   virtual int onNotificationFailure(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationFailureEvent& evt) OVERRIDE;
   virtual int onNotificationServiceRegistration(CPCAPI2::PushService::PushNotificationServiceHandle h, const CPCAPI2::PushService::NotificationServiceRegistrationEvent& evt) OVERRIDE;

   // PushNotificationEndpointHandler
   virtual int onPushNotification(CPCAPI2::PushEndpoint::PushNotificationEndpointHandle h, const CPCAPI2::PushEndpoint::PushNotificationEvent& evt) OVERRIDE { return 0; };
   virtual int onPushRegistrationSuccess(CPCAPI2::PushEndpoint::PushNotificationEndpointHandle h, const CPCAPI2::PushEndpoint::PushRegistrationSuccessEvent& evt) OVERRIDE { return 0; };
   virtual int onPushRegistrationFailure(CPCAPI2::PushEndpoint::PushNotificationEndpointHandle h, const CPCAPI2::PushEndpoint::PushRegistrationFailureEvent& evt) OVERRIDE { return 0; };
   virtual int onPushRegistrationQueryListResult(const CPCAPI2::PushEndpoint::PushRegistrationQueryListResult& args) OVERRIDE;
   virtual int onPushRegistrationQueryInfoResult(CPCAPI2::PushEndpoint::PushNotificationEndpointHandle h, const CPCAPI2::PushEndpoint::PushRegistrationQueryInfoResult& args) OVERRIDE;

   // RemoteSyncHandler
   virtual int onSetAccounts(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SetAccountsEvent& evt) OVERRIDE;
   virtual int onNotificationUpdate(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::NotificationUpdateEvent& evt) OVERRIDE;
   virtual int onMessageReactions(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageReactionsEvent& evt) OVERRIDE;
   virtual int onFetchMessagesReactionsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchMessagesReactionsCompleteEvent& evt) OVERRIDE;
   virtual int onSyncItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::SyncItemsCompleteEvent& evt) OVERRIDE;
   virtual int onUpdateItemComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemCompleteEvent& evt) OVERRIDE;
   virtual int onFetchRangeComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchRangeCompleteEvent& evt) OVERRIDE;
   virtual int onFetchConversationsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::FetchConversationsCompleteEvent& evt) OVERRIDE;
   virtual int onConversationUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ConversationUpdatedEvent& evt) OVERRIDE;
   virtual int onUpdateItemsComplete(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::UpdateItemsCompleteEvent& evt) OVERRIDE;
   virtual int onMessageCount(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::MessageCountEvent& evt) OVERRIDE;
   virtual int onError(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnErrorEvent& evt) OVERRIDE;
   virtual int onConnectionState(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnConnectionStateEvent& evt) OVERRIDE;
   virtual int onTimestampDelta(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::OnTimestampDeltaEvent& evt) OVERRIDE;
   virtual int onItemsUpdated(const CPCAPI2::RemoteSync::SessionHandle& sessionHandle, const CPCAPI2::RemoteSync::ItemsUpdatedEvent& evt) OVERRIDE;

   // XmppAgentHandler
   virtual int onPushRegistrationSuccess(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppPushRegistrationSuccessEvent& args) OVERRIDE { return 0; };
   virtual int onPushRegistrationFailure(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppPushRegistrationFailureEvent& args) OVERRIDE { return 0; };
   virtual int onEventHistory(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppEventHistory& args) OVERRIDE { return 0; };
   virtual int onRemoteSyncRegisterResult(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppAgentRemoteSyncRegisterResult& args) OVERRIDE { return 0; };
   virtual int onLogout(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::LogoutResult& args) OVERRIDE { return 0; };
   virtual int onXmppAgentQueryListResult(const CPCAPI2::XmppAgent::XmppAgentQueryListResult& args) OVERRIDE;
   virtual int onXmppAgentQueryInfoResult(CPCAPI2::XmppAgent::XmppPushRegistrationHandle pushRegistration, const CPCAPI2::XmppAgent::XmppAgentQueryInfoResult& args) OVERRIDE;

   // DeadlineTimerHandler
   virtual void onTimer(unsigned short timerId, void* appState) OVERRIDE;

private:

   void appInit();
   void appShutdown();
   void setServerShutdownFlag();
   void handleSdkCallbackImpl();
   void registerWithOrchServer(const cpc::string& serviceId, const cpc::string& authServerUrl, const cpc::string& username, const cpc::string& password, const cpc::string& orchServerUrl, const cpc::string& thisServerRegion, const cpc::string& thisServerUrl);
   void dump();

   int parseLoginContext(const std::string& loginContextString, LoginContext& outLoginContext);

   static void finalizeCleanup(void* context);
   void finalizeCleanupImpl();

   static void stop_agent(int signal);

   static void addSdkObserver(void* context);

private:

   SdkManager();
   static SdkManager* mInstance;

   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::JsonApi::JsonApiServerInternal* mJsonApiServer;
   CPCAPI2::PushService::PushNotificationServiceManager* mPushServiceManager;
   CPCAPI2::OrchestrationServer::OrchestrationServer* mOrchServer;
   CPCAPI2::JsonApi::JsonApiServerSendTransport* mJsonSendTransport;
   std::map<resip::Data, Cpcapi2Runner*> mMapContextToRunner;
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*> mMapJsonApiUserToRunner;
   std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>> mMapRunnerToJsonApiUsers;

   CPCAPI2::Auth::AuthProviderMock* mAuthProviderMock;
   static std::atomic_int sNextSdkThreadPoolIdx;

   resip::DeadlineTimer<resip::MultiReactor> mContextCleanupTimer;

   template<typename T>
   struct TimeTrackedValue
   {
      TimeTrackedValue(const T& other) : value(other) {}

      TimeTrackedValue<T>& operator =(const T& other)
      {
         std::lock_guard<std::recursive_mutex> lock(mutex);
         value = other;

         if (updates.size() == MaxTracked) updates.pop_back();
         updates.push_front(std::make_pair(other, std::chrono::system_clock::now()));

         return *this;
      }

      operator T()
      {
         std::lock_guard<std::recursive_mutex> lock(mutex);
         return value;
      }

      T value;
      std::list<std::pair<T, std::chrono::time_point<std::chrono::system_clock> > > updates;
      std::recursive_mutex mutex;

      static const int MaxTracked = 10;
   };

   struct HealthCheckBoolean : JsonApi::HttpServerRequestHandler
   {
      HealthCheckBoolean(bool value) : mValue(value) {}

      virtual cpc::string output() OVERRIDE;

      TimeTrackedValue<bool> mValue;
   };

   struct HealthCheckMap : JsonApi::HttpServerRequestHandler
   {
      virtual cpc::string output() OVERRIDE;

      std::map<RemoteSync::SessionHandle, bool> mValues;

      std::recursive_mutex mMutex;
   };

   struct UserSessionCount : JsonApi::HttpServerRequestHandler
   {
      UserSessionCount(std::map<resip::Data, Cpcapi2Runner*>& mMapContextToRunner, std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& mMapRunnerToJsonApiUsers, std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& mMapJsonApiUserToRunner) : m_ContextToRunnerMap(mMapContextToRunner), m_RunnerToUsersMap(mMapRunnerToJsonApiUsers), m_UserToRunnerMap(mMapJsonApiUserToRunner) {}

      virtual cpc::string output() OVERRIDE;

      const std::map<resip::Data, Cpcapi2Runner*>& m_ContextToRunnerMap;
      const std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& m_RunnerToUsersMap;
      const std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& m_UserToRunnerMap;

      std::recursive_mutex mMutex;
   };

   struct BuildInfo : JsonApi::HttpServerRequestHandler
   {
      virtual cpc::string output() OVERRIDE;
   };

   struct HealthPushRegistrationInfo : JsonApi::HttpServerRequestHandler
   {
      HealthPushRegistrationInfo(
         SdkManager* manager,
         std::map<resip::Data, Cpcapi2Runner*>& mMapContextToRunner,
         std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& mMapRunnerToJsonApiUsers,
         std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& mMapJsonApiUserToRunner,
         std::vector<PushEndpoint::PushNotificationEndpointHandle>& mPushQueryListResult,
         std::map<PushEndpoint::PushNotificationEndpointHandle, PushEndpoint::PushRegistrationQueryInfoResult>& mPushQueryInfoResult,
         std::condition_variable& mQueryListSignal,
         std::condition_variable& mQueryInfoSignal,
         std::condition_variable& mQueryHttpSignal,
         resip::MultiReactor& mReactor,
         std::ostringstream& mStream,
         std::recursive_mutex& mRunnerMutex) :
      m_Manager(manager),
      m_ContextToRunnerMap(mMapContextToRunner),
      m_RunnerToUsersMap(mMapRunnerToJsonApiUsers),
      m_UserToRunnerMap(mMapJsonApiUserToRunner),
      m_QueryListResult(mPushQueryListResult),
      m_QueryInfoResult(mPushQueryInfoResult),
      m_QueryListSignal(mQueryListSignal),
      m_QueryInfoSignal(mQueryInfoSignal),
      m_QueryHttpSignal(mQueryHttpSignal),
      m_Reactor(mReactor),
      m_Stream(mStream),
      m_RunnerMutex(mRunnerMutex) {}

      virtual cpc::string output() OVERRIDE;
      void outputImpl();

      SdkManager* m_Manager;
      std::map<CPCAPI2::JsonApi::JsonApiUserHandle, PushEndpoint::PushRegistrationQueryInfoResult> mValues;

      const std::map<resip::Data, Cpcapi2Runner*>& m_ContextToRunnerMap;
      const std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& m_RunnerToUsersMap;
      const std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& m_UserToRunnerMap;
      std::vector<PushEndpoint::PushNotificationEndpointHandle>& m_QueryListResult;
      std::map<PushEndpoint::PushNotificationEndpointHandle, PushEndpoint::PushRegistrationQueryInfoResult>& m_QueryInfoResult;
      std::condition_variable& m_QueryListSignal;
      std::condition_variable& m_QueryInfoSignal;
      std::condition_variable& m_QueryHttpSignal;

      std::recursive_mutex& m_RunnerMutex;
      resip::MultiReactor& m_Reactor;
      std::ostringstream& m_Stream;
      std::mutex m_QueryMutex;
   };

   struct HealthXmppRegistrationInfo : JsonApi::HttpServerRequestHandler
   {
      HealthXmppRegistrationInfo(
         SdkManager* manager,
         std::map<resip::Data, Cpcapi2Runner*>& mMapContextToRunner,
         std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& mMapRunnerToJsonApiUsers,
         std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& mMapJsonApiUserToRunner,
         std::vector<XmppAgent::XmppPushRegistrationHandle>& mAgentQueryListResult,
         std::map<XmppAgent::XmppPushRegistrationHandle, XmppAgent::XmppAgentQueryInfoResult>& mAgentQueryInfoResult,
         std::condition_variable& mQueryListSignal,
         std::condition_variable& mQueryInfoSignal,
         std::condition_variable& mQueryHttpSignal,
         resip::MultiReactor& mReactor,
         std::ostringstream& mStream,
         std::recursive_mutex& mRunnerMutex) :
      m_Manager(manager),
      m_ContextToRunnerMap(mMapContextToRunner),
      m_RunnerToUsersMap(mMapRunnerToJsonApiUsers),
      m_UserToRunnerMap(mMapJsonApiUserToRunner),
      m_QueryListResult(mAgentQueryListResult),
      m_QueryInfoResult(mAgentQueryInfoResult),
      m_QueryListSignal(mQueryListSignal),
      m_QueryInfoSignal(mQueryInfoSignal),
      m_QueryHttpSignal(mQueryHttpSignal),
      m_Reactor(mReactor),
      m_Stream(mStream),
      m_RunnerMutex(mRunnerMutex) {}

      virtual cpc::string output() OVERRIDE;
      void outputImpl();

      SdkManager* m_Manager;
      std::map<CPCAPI2::JsonApi::JsonApiUserHandle, XmppAgent::XmppAgentQueryInfoResult> mValues;

      const std::map<resip::Data, Cpcapi2Runner*>& m_ContextToRunnerMap;
      const std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& m_RunnerToUsersMap;
      const std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& m_UserToRunnerMap;
      std::vector<XmppAgent::XmppPushRegistrationHandle>& m_QueryListResult;
      std::map<XmppAgent::XmppPushRegistrationHandle, XmppAgent::XmppAgentQueryInfoResult>& m_QueryInfoResult;
      std::condition_variable& m_QueryListSignal;
      std::condition_variable& m_QueryInfoSignal;
      std::condition_variable& m_QueryHttpSignal;

      std::recursive_mutex& m_RunnerMutex;
      resip::MultiReactor& m_Reactor;
      std::ostringstream& m_Stream;
      std::mutex m_QueryMutex;
   };

   struct HealthRunnerInfo : JsonApi::HttpServerRequestHandler
   {
      HealthRunnerInfo(
         std::map<resip::Data, Cpcapi2Runner*>& mMapContextToRunner,
         std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& mMapRunnerToJsonApiUsers,
         std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& mMapJsonApiUserToRunner,
         std::condition_variable& mQueryHttpSignal,
         resip::MultiReactor& mReactor,
         std::ostringstream& mStream,
         std::recursive_mutex& mRunnerMutex) :
      m_ContextToRunnerMap(mMapContextToRunner),
      m_RunnerToUsersMap(mMapRunnerToJsonApiUsers),
      m_UserToRunnerMap(mMapJsonApiUserToRunner),
      m_QueryHttpSignal(mQueryHttpSignal),
      m_Reactor(mReactor),
      m_Stream(mStream),
      m_RunnerMutex(mRunnerMutex) {}

      virtual cpc::string output() OVERRIDE;
      void outputImpl();

      const std::map<resip::Data, Cpcapi2Runner*>& m_ContextToRunnerMap;
      const std::map<Cpcapi2Runner*, std::set<CPCAPI2::JsonApi::JsonApiUserHandle>>& m_RunnerToUsersMap;
      const std::map<CPCAPI2::JsonApi::JsonApiUserHandle, Cpcapi2Runner*>& m_UserToRunnerMap;
      std::condition_variable& m_QueryHttpSignal;
      std::recursive_mutex& m_RunnerMutex;
      std::ostringstream& m_Stream;
      resip::MultiReactor& m_Reactor;
   };

   CPCAPI2::OrchestrationServer::ServerInfo mServerInfo;
   CPCAPI2::CloudServiceConfig::ServiceConfigSettings mServiceConfigSettings;
   bool mDebugDumpEnabled;
   LocalLogger* mLocalLogger;
   HealthCheckBoolean mHealthCheckPushNotification;
   HealthCheckBoolean mHealthCheckRedis;
   HealthCheckBoolean mHealthCheckLogin;
   HealthCheckMap mHealthCheckRemoteSync;
   UserSessionCount mUserSessionCount;
   BuildInfo mBuildInfo;
   HealthPushRegistrationInfo mPushRegistrationInfo;
   HealthXmppRegistrationInfo mXmppRegistrationInfo;
   HealthRunnerInfo mRunnerInfo;
   std::vector<PushEndpoint::PushNotificationEndpointHandle> mPushQueryListResult;
   std::map<PushEndpoint::PushNotificationEndpointHandle, PushEndpoint::PushRegistrationQueryInfoResult> mPushQueryInfoResult;
   std::vector<XmppAgent::XmppPushRegistrationHandle> mAgentQueryListResult;
   std::map<XmppAgent::XmppPushRegistrationHandle, XmppAgent::XmppAgentQueryInfoResult> mAgentQueryInfoResult;
   std::condition_variable mQueryListSignal;
   std::condition_variable mQueryInfoSignal;
   std::condition_variable mQueryHttpSignal;
   std::ostringstream mQueryStream;
   std::recursive_mutex mRunnerMutex;

};

}

}
