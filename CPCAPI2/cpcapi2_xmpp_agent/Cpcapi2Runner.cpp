#if _WIN32
#include "stdafx.h"
#endif

#include "Cpcapi2Runner.h"
#include "brand_branded.h"
#include "Logger.h"

#include <interface/experimental/xmpp/XmppAccountJsonApi.h>
#include <interface/experimental/xmpp/XmppChatJsonApi.h>
#include <interface/experimental/xmpp/XmppRosterJsonApi.h>
#include <interface/experimental/xmpp/XmppVCardJsonApi.h>
#include <interface/experimental/xmpp/XmppMultiUserChatJsonApi.h>
#include <interface/experimental/xmpp_agent/XmppAgentJsonApi.h>
#include <interface/experimental/xmpp_agent/XmppAgentManager.h>
#include <interface/experimental/remotesync/RemoteSyncManager.h>
#include <interface/experimental/remotesync/RemoteSyncJsonApi.h>
#include <interface/experimental/push_endpoint/PushNotificationEndpointJsonApi.h>
#include <fstream>
#include <chrono>
#include "impl/util/cpc_logger.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::PUSH_SERVER

using namespace CPCAPI2;
using namespace CPCAPI2::XmppAccount;
using namespace CPCAPI2::XmppAgent;
using namespace CPCAPI2::RemoteSync;
using namespace CPCAPI2::PushEndpoint;

namespace CPCAPI2
{

namespace Agent
{

Cpcapi2Runner::Cpcapi2Runner(
   int sdkThreadPoolThreadIdx,
   resip::MultiReactor* appReactor,
   CPCAPI2::Phone* masterSdkPhone,
   CPCAPI2::JsonApi::JsonApiServer* jsonApiServer,
   CPCAPI2::PushService::PushNotificationServiceManager* pushServiceManager,
   const resip::Data& context) :
CPCAPI2::AppRunner(),
mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx),
mAppReactor(appReactor),
mPhone(NULL),
mMasterSdkPhone(masterSdkPhone),
mJsonApiServer(jsonApiServer),
mPushNotificationServiceManager(pushServiceManager),
mPushNotificationEndpointManager(NULL),
mServiceNotificationsManager(NULL),
mContext(context),
mDestroyed(false)
{
   appInit();
   DebugLog(<< "Cpcapi2Runner::Cpcapi2Runner(): " << this << " creating cpcapi-runner mPhone: " << mPhone << " mMasterSdkPhone: " << mMasterSdkPhone << " mContext: " << mContext);
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   DebugLog(<< "Cpcapi2Runner::~Cpcapi2Runner(): " << this << " destroying cpcapi-runner mPhone: " << mPhone << " mMasterSdkPhone: " << mMasterSdkPhone << " mContext: " << mContext);
   mAppReactor->detach();
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::addJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, bool isHttp)
{
   if (isHttp)
   {
      mHttpJsonApiUsers.insert(jsonApiUser);
   }
   else
   {
      mWsJsonApiUsers.insert(jsonApiUser);
   }

   mLoggedOutJsonApiUsers.clear();
}

void Cpcapi2Runner::removeJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser)
{
   mHttpJsonApiUsers.erase(jsonApiUser);
   mWsJsonApiUsers.erase(jsonApiUser);
   mLoggedOutJsonApiUsers.erase(jsonApiUser);

   if (mWsJsonApiUsers.size() == 0)
   {
      mHttpJsonApiUsers.clear();
   }
}

size_t Cpcapi2Runner::countJsonApiUsers() const
{
   return (mHttpJsonApiUsers.size() + mWsJsonApiUsers.size());
}

size_t Cpcapi2Runner::countHttpJsonApiUsers() const
{
   return (mHttpJsonApiUsers.size());
}

size_t Cpcapi2Runner::countWsJsonApiUsers() const
{
   return (mWsJsonApiUsers.size());
}

int Cpcapi2Runner::shutdown()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::shutdownImpl, this));
   return kSuccess;
}

void Cpcapi2Runner::shutdownImpl()
{
   assert(mAppReactor->isCurrentThread());
   appShutdown();
}

void Cpcapi2Runner::handleSdkCallback(unsigned int timeoutMs)
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::handleSdkCallbackImpl, this, timeoutMs));
}

void Cpcapi2Runner::handleSdkCallbackImpl(unsigned int timeoutMs)
{
   if (mPhone)
      mPhone->process(timeoutMs);
   if (mXmppAccountManager)
      mXmppAccountManager->process(timeoutMs);
   if (mXmppAgentManager)
      mXmppAgentManager->process(timeoutMs);
   if (mRemoteSyncManager)
      mRemoteSyncManager->process(timeoutMs);
   if (mPushNotificationEndpointManager)
      mPushNotificationEndpointManager->process(timeoutMs);
   // if (mServiceNotificationsManager)
   // mServiceNotificationsManager->process(timeoutMs);
}

void sdkCallbackHook(void* context)
{
   if (context)
   {
      Cpcapi2Runner* cpcRunner = (Cpcapi2Runner*)context;
      cpcRunner->handleSdkCallback(CPCAPI2::Phone::kBlockingModeNonBlocking);
   }
}

void Cpcapi2Runner::createModules(void* context)
{
   Cpcapi2Runner* thisPtr = (Cpcapi2Runner*)context;

   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
#ifdef WIN32
   licenseInfo.licenseDocumentLocation = "C:\\Temp";
#else
   licenseInfo.licenseDocumentLocation = "./";
#endif
   licenseInfo.licenseAor = "";
   thisPtr->mPhone->initialize(licenseInfo, thisPtr, false);

   // old style logging is on until we properly handle logging in modules
   // used by both clients and the XMPP agent
   thisPtr->mPhone->setLoggingEnabled("cpcapi2_xmpp_agent", true);

   static_cast<PhoneInternal*>(thisPtr->mPhone)->setLocalFileLoggingEnabled(thisPtr->mContext.c_str(), true);
   static_cast<PhoneInternal*>(thisPtr->mPhone)->setLocalFileLoggingLevel(CPCAPI2::LogLevel_Max);

   thisPtr->mXmppAccountManager = XmppAccountManagerInternal::getInternalInterface(thisPtr->mPhone);
   thisPtr->mXmppAccountManager->setCallbackHook(sdkCallbackHook, thisPtr);

   thisPtr->mXmppAgentManager = XmppAgentManagerInternal::getInternalInterface(thisPtr->mPhone);
   thisPtr->mXmppAgentManager->setCallbackHook(sdkCallbackHook, thisPtr);

   thisPtr->mRemoteSyncManager = RemoteSyncManagerInternal::getInternalInterface(thisPtr->mPhone);
   thisPtr->mRemoteSyncManager->setCallbackHook(sdkCallbackHook, thisPtr);

   /*
   thisPtr->mServiceNotificationsManager = CPCAPI2::ServiceNotifications::ServiceNotificationsManager::getInterface( thisPtr->mPhone );
   thisPtr->mServiceNotificationsManager->setPushNotificationServiceManager(thisPtr->mPushNotificationServiceManager);
   */

   thisPtr->mPushNotificationEndpointManager = CPCAPI2::PushEndpoint::PushNotificationEndpointManagerInternal::getInternalInterface(thisPtr->mPhone);
   thisPtr->mPushNotificationEndpointManager->setEventCallbackHook(sdkCallbackHook, thisPtr);
   thisPtr->mPushNotificationEndpointManager->setPushNotificationService(thisPtr->mPushNotificationServiceManager);

   // need to explicitly create the modules we want to expose
   CPCAPI2::PushEndpoint::PushNotificationEndpointJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::RemoteSync::RemoteSyncJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppAccount::XmppAccountJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppChat::XmppChatJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppRoster::XmppRosterJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppVCard::XmppVCardJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppAgent::XmppAgentJsonApi::getInterface(thisPtr->mPhone);
   CPCAPI2::XmppMultiUserChat::XmppMultiUserChatJsonApi::getInterface(thisPtr->mPhone);
   // setup a mapping between the SDK instance for this user (mPhone) and the single-instance WebSocket server managed by mMasterSdkPhone (i.e. jsonApiServer)
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(thisPtr->mPhone)->setJsonApiServer(thisPtr->mJsonApiServer);

   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(thisPtr->mPhone)->setPushNotificationManager(thisPtr->mPushNotificationServiceManager);
   CPCAPI2::XmppAgent::XmppAgentManager::getInterface(thisPtr->mPhone)->setJsonApiServer(thisPtr->mJsonApiServer);

   StackLog(<< "Cpcapi2Runner::createModules(): mPhone: " << thisPtr->mPhone << " mMasterPhone: " << thisPtr->mMasterSdkPhone);
}

void Cpcapi2Runner::appInit()
{
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   mPhone->setCallbackHook(sdkCallbackHook, this);

   DebugLog(<< "Cpcapi2Runner::appInit(): Cpcapi2Runner: " << this << " thread-index: " << mSdkThreadPoolThreadIdx << " mPhone: " << mPhone
      << " instance initialized, mAppReactor: " << mAppReactor << " mJsonApiServer: " << mJsonApiServer
      << " mPushNotificationServiceManager: " << mPushNotificationServiceManager);
   mPhone->blockUntilRanOnSdkModuleThread(&Cpcapi2Runner::createModules, this);
}

void Cpcapi2Runner::appShutdown()
{
   DebugLog(<< "Cpcapi2Runner::appShutdown(): " << this);
   assert(mAppReactor->isCurrentThread());

   if (!isDestroyed())
   {
      InfoLog(<< "Cpcapi2Runner::appShutdown(): " << this << " shutdown triggered but destroyed flag is not set");
   }

   if (mRemoteSyncManager)
      mRemoteSyncManager->setCallbackHook(sdkCallbackHook, NULL);

   if (mXmppAgentManager)
      mXmppAgentManager->setCallbackHook(sdkCallbackHook, NULL);

   if (mXmppAgentManager)
      mXmppAccountManager->setCallbackHook(sdkCallbackHook, NULL);

   if (mPushNotificationEndpointManager)
      mPushNotificationEndpointManager->setEventCallbackHook(sdkCallbackHook, NULL);

   if (mPhone)
      mPhone->setCallbackHook(sdkCallbackHook, NULL);

   // Drain the process queues
   for (int i = 0 ; i < 3; ++i)
   {
      handleSdkCallbackImpl(50);
   }

   if (mPhone)
   {
      Phone::release(mPhone);
   }

   mRemoteSyncManager = NULL;
   mXmppAgentManager = NULL;
   mXmppAccountManager = NULL;
   mPushNotificationEndpointManager = NULL;
   mPhone = NULL;
   delete this;
}

resip::Data Cpcapi2Runner::getContext() const
{
   return mContext;
}

void Cpcapi2Runner::logoutJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser)
{
   mLoggedOutJsonApiUsers.insert(jsonApiUser);
}

bool Cpcapi2Runner::isLoggedOut(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const
{
   std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator i = mLoggedOutJsonApiUsers.find(jsonApiUser);
   return (i != mLoggedOutJsonApiUsers.end());
}

bool Cpcapi2Runner::isDestroyed() const
{
   return mDestroyed;
}

bool Cpcapi2Runner::isAnyLoggedOut() const
{
   return (mLoggedOutJsonApiUsers.size() != 0);
}

bool Cpcapi2Runner::isHttp(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const
{
   std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator i = mHttpJsonApiUsers.find(jsonApiUser);
   return (i != mHttpJsonApiUsers.end());
}

bool Cpcapi2Runner::isWs(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser) const
{
   std::set<CPCAPI2::JsonApi::JsonApiUserHandle>::iterator i = mWsJsonApiUsers.find(jsonApiUser);
   return (i != mWsJsonApiUsers.end());
}

void Cpcapi2Runner::getHttpJsonApiUsers(std::set<CPCAPI2::JsonApi::JsonApiUserHandle>& jsonApiUsers) const
{
   jsonApiUsers = mHttpJsonApiUsers;
}

void Cpcapi2Runner::getWsJsonApiUsers(std::set<CPCAPI2::JsonApi::JsonApiUserHandle>& jsonApiUsers) const
{
   jsonApiUsers = mWsJsonApiUsers;
}

void Cpcapi2Runner::setDestroyed()
{
   mDestroyed = true;
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

}

}
