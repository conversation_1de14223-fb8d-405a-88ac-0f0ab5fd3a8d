#ifndef CORE_RANGE_HPP
#define CORE_RANGE_HPP

#include <iterator>
#include <istream>
#include <utility>
#include <memory>

#include <cstdlib>

#include <bsoncxx/third_party/mnmlstc/core/type_traits.hpp>

namespace core {
inline namespace v1 {
namespace impl {

template <class T>
class begin {
  template <class U>
  static auto check (U&& u) noexcept -> decltype(::std::begin(u));
  static void check (...) noexcept(false);
public:
  using type = decltype(check(::std::declval<T>()));
  static constexpr bool value = noexcept(check(::std::declval<T>()));
};

template <class T>
class end {
  template <class U>
  static auto check (U&& u) noexcept -> decltype(::std::end(u));
  static void check (...) noexcept(false);
public:
  using type = decltype(check(::std::declval<T>()));
  static constexpr bool value = noexcept(check(::std::declval<T>()));
};

} /* namespace impl */

template <class R>
struct is_range : ::std::integral_constant<bool,
  impl::begin<R>::value and impl::end<R>::value
> { };

template <class Iterator>
struct range {
  using traits = ::std::iterator_traits<Iterator>;

  using iterator_category = typename traits::iterator_category;

  using difference_type = typename traits::difference_type;
  using value_type = typename traits::value_type;

  using reference = typename traits::reference;
  using pointer = typename traits::pointer;

  using iterator = Iterator;

  static constexpr bool is_input = ::std::is_convertible<
    iterator_category,
    ::std::input_iterator_tag
  >::value;

  static constexpr bool is_output = ::std::is_convertible<
    iterator_category,
    ::std::output_iterator_tag
  >::value;

  static constexpr bool is_forward = ::std::is_convertible<
    iterator_category,
    ::std::forward_iterator_tag
  >::value;

  static constexpr bool is_bidirectional = ::std::is_convertible<
    iterator_category,
    ::std::bidirectional_iterator_tag
  >::value;

  static constexpr bool is_random_access = ::std::is_convertible<
    iterator_category,
    ::std::random_access_iterator_tag
  >::value;

  template <
    typename Range,
    typename=enable_if_t<
      not ::std::is_pointer<iterator>::value and
      is_range<Range>::value and
      ::std::is_convertible<typename impl::begin<Range>::type, iterator>::value
    >
  > explicit range (Range&& r) noexcept :
    range { ::std::begin(r), ::std::end(r) }
  { }

  range (::std::pair<iterator, iterator> pair) noexcept :
    range { ::std::get<0>(pair), ::std::get<1>(pair) }
  { }

  range (iterator begin_, iterator end_) noexcept :
    begin_ { begin_ },
    end_ { end_ }
  { }

  range (range const& that) :
    range { that.begin_, that.end_ }
  { }

  range (range&& that) noexcept :
    range { ::std::move(that.begin_), ::std::move(that.end_) }
  { that.begin_ = that.end_; }

  range () = default;
  ~range () = default;

  range& operator = (range const& that) {
    return *this = range { that };
  }

  range& operator = (range&& that) {
    range { ::std::move(that) }.swap(*this);
    return *this;
  }

  reference operator [](difference_type idx) const {
    static_assert(is_random_access, "can only subscript into random-access");
    return idx < 0 ? this->end()[idx] : this->begin()[idx];
  }

  iterator begin () const { return this->begin_; }
  iterator end () const { return this->end_; }

  reference front () const { return *this->begin(); }
  reference back () const {
    static_assert(is_bidirectional, "can only get back of bidirectional");
    return *::std::prev(this->end());
  }

  bool empty () const { return this->begin() == this->end(); }

  difference_type size () const {
    static_assert(is_forward, "can only get size of forward-range");
    return ::std::distance(this->begin(), this->end());
  }

  /* Creates an open-ended range of [start, stop) */
  range slice (difference_type start, difference_type stop) const {
    static_assert(is_forward, "can only slice forward-range");
    /* Behavior is:
     * if start is negative, the begin marker is this->end() - start
     * if stop is negative, the end marker is this->end() - stop
     * if start is positive, the begin marker is this->begin() + start
     * if stop is positive, the end marker is this->begin() + stop
     *
     * if start and stop are positive, and stop is less than or equal to start,
     * an empty range is returned.
     *
     * if start and stop are negative and stop is less than or equal to start,
     * an empty range is returned.
     *
     * if start is positive and stop is negative and abs(stop) + start is
     * greater than or equal to this->size(), an empty range is returned.
     *
     * if start is negative and stop is positive and this->size() + start is
     * greater or equal to stop, an empty range is returned.
     *
     * The first two conditions can be computed cheaply, while the third and
     * fourth are a bit more expensive, but WILL be required no matter what
     * iterator type we are. However we don't compute the size until after
     * we've checked the first two conditions
     *
     * An example with python style slicing for each would be:
     * [4:3] -> empty range
     * [-4:-4] -> empty range
     * [7:-4] -> empty range for string of size 11 or more
     * [-4:15] -> empty range for a string of size 19 or less.
     */
    bool const start_positive = start > 0;
    bool const stop_positive = stop > 0;
    bool const stop_less = stop < start;
    bool const first_return_empty =
      (start_positive and stop_positive and stop_less) or
      (not start_positive and not stop_positive and stop_less);
    if (first_return_empty) { return range { }; }

    /* now safe to compute size */
    auto const size = this->size();
    auto third_empty = ::std::abs(stop) + start;

    bool const second_return_empty =
      (start_positive and not stop_positive and third_empty >= size) or
      (not start_positive and stop_positive and size + start >= stop);
    if (second_return_empty) { return range { }; }

    /* While the code below technically works for all iterators it is
     * ineffecient in some cases for bidirectional ranges, where either of
     * start or stop are negative.
     * TODO: Specialize for bidirectional operators
     */
    if (not start_positive) { start = size + start; }
    if (not stop_positive) { stop = size + stop; }

    auto begin = this->begin();
    ::std::advance(begin, start);

    auto end = begin;
    ::std::advance(end, stop - start);

    return range { begin, end };
  }

  /* Creates an open-ended range of [start, end()) */
  range slice (difference_type start) const {
    static_assert(is_forward, "can only slice forward-range");
    return range { split(start).second };
  }

  ::std::pair<range, range> split (difference_type idx) const {
    static_assert(is_forward,"can only split a forward-range");
    if (idx >= 0) {
      range second { *this };
      second.pop_front_upto(idx);
      return ::std::make_pair(range { this->begin(), second.begin() }, second);
    }

    range first { *this };
    first.pop_back_upto(-idx);
    return ::std::make_pair(first, range { first.end(), this->end() });
  }

  /* mutates range */
  void pop_front (difference_type n) { ::std::advance(this->begin_, n); }
  void pop_front () { ++this->begin_; }

  void pop_back (difference_type n) {
    static_assert(is_bidirectional, "can only pop-back bidirectional-range");
    ::std::advance(this->end_, -n);
  }

  void pop_back () {
    static_assert(is_bidirectional, "can only pop-back bidirectional-range");
    --this->end_;
  }

  /* Negative argument causes no change */
  void pop_front_upto (difference_type n) {
    ::std::advance(
      this->begin_,
      ::std::min(::std::max<difference_type>(0, n), this->size())
    );
  }

  /* Negative argument causes no change */
  void pop_back_upto (difference_type n) {
    static_assert(is_bidirectional, "can only pop-back-upto bidirectional");
    ::std::advance(
      this->end_,
      -::std::min(::std::max<difference_type>(0, n), this->size())
    );
  }

  void swap (range& that) noexcept(is_nothrow_swappable<iterator>::value) {
    using ::std::swap;
    swap(this->begin_, that.begin_);
    swap(this->end_, that.end_);
  }

private:
  iterator begin_;
  iterator end_;
};

template <class Iterator>
auto make_range (Iterator begin, Iterator end) -> range<Iterator> {
  return range<Iterator> { begin, end };
}

template <class Range>
auto make_range (Range&& value) -> range<decltype(::std::begin(value))> {
  return make_range(::std::begin(value), ::std::end(value));
}

template <class Range>
auto make_ptr_range (Range&& value) -> range<
  decltype(::std::addressof(*::std::begin(value)))
>;

/* Used like: core::make_range<char>(::std::cin) */
template <
  class T,
  class CharT,
  class Traits=::std::char_traits<CharT>
> auto make_range (::std::basic_istream<CharT, Traits>& stream) -> range<
  ::std::istream_iterator<T, CharT, Traits>
> {
  using iterator = ::std::istream_iterator<T, CharT, Traits>;
  return make_range(iterator { stream }, iterator { });
}

template <class CharT, class Traits=::std::char_traits<CharT>>
auto make_range (::std::basic_streambuf<CharT, Traits>* buffer) -> range<
  ::std::istreambuf_iterator<CharT, Traits>
> {
  using iterator = ::std::istreambuf_iterator<CharT, Traits>;
  return make_range(iterator { buffer }, iterator { });
}

template <class Iterator>
void swap (range<Iterator>& lhs, range<Iterator>& rhs) noexcept(
  noexcept(lhs.swap(rhs))
) { lhs.swap(rhs); }

}} /* namespace core::v1 */

#endif /* CORE_RANGE_HPP */
