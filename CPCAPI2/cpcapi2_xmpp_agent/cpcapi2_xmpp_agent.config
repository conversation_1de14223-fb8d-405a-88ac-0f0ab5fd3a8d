#--------------------------------------------------------------------
# Apple Push Network (APN) Settings For Production
#--------------------------------------------------------------------

# The URL (as provided by Apple) for the production APN
apn_server_url = https://api.push.apple.com:443/3/device/

# The "p8" file, which is obtained from the apple developer portal,
# is used for authentication purposes to the production APN
pushprovider_apns_p8file = APNsAuthKey_462WVPCXB7.p8

# The auth key (also obtained from Apple and required for push)
pushprovider_apns_authkeyid = 462WVPCXB7

# "team" Identifier for the Apple Developer Portal
pushprovider_apns_teamid = UQC9N9AMZM

#--------------------------------------------------------------------
# Apple Push Network (APN) Settings For Sandbox (developer)
#--------------------------------------------------------------------

# The URL (as provided by Apple) for the development APN
apn_sandbox_server_url = https://api.development.push.apple.com:443/3/device/

# The "p8" file, which is obtained from the apple developer portal,
# is used for authentication purposes to the developer APN
pushprovider_apns_sandbox_p8file = APNsSandboxAuthKey_462WVPCXB7.p8

# The auth key (also obtained from Apple and required for push)
pushprovider_apns_sandbox_authkeyid = 462WVPCXB7

# "team" Identifier for the Apple Developer Portal
pushprovider_apns_sandbox_teamid = UQC9N9AMZM

#--------------------------------------------------------------------
# Google Firebase (FCM) Settings
#--------------------------------------------------------------------

# The URL (as provided by Google) for sending FCM push messages
fcm_server_url = https://fcm.googleapis.com/fcm/send

# The key ID is an authentication token provided by google and
# required for use with the FCM server
pushprovider_fcm_keyid = AAAAl52NiXc:APA91bFmg4ymYoK7oA49AgpT5EOKJkM15RcE2LQ8ik5V1qgbM0FihBwHFciNZpo2eHXkUvXDMP-IrI3l_xHo7KXUd-T2G-ip2jzyfj6-ryIHLV-wgNEBD4l6xppCVdj2puj1xGe8CqvyiqQ-Jw2sWvixXV-_wI2PSA

#--------------------------------------------------------------------
# Authentication settings
#--------------------------------------------------------------------

# The location of the CP Auth server
auth_server_url = https://cloudsdk4.bria-x.net:18082/login_v1

# The username used by the xmpp agent for the CP auth server
cloud_server_username = server

# The password used by the xmpp agent for the CP auth server
cloud_server_password = 6f8adc5c68f6e9de2c658d9cdffae8eb9debb02251f3c6c2d0b02bb6a80222e5

#--------------------------------------------------------------------
# JSON API (RPC) settings for receiving messages via JSON
#--------------------------------------------------------------------

# The authentication domain
jsonapi_server_region = LOCAL

# The local websocket URL for answering json queries. This is the
# value returned by the local orchestrator service. It should match
# the actual port listening value below
jsonapi_server_ws_url = ws://127.0.0.1:9003

# the local websocket port to use
jsonapi_websocket_port = 9003

# the local http port for HTTP services (of which there are a few)
jsonapi_http_port = 18080

# Certificate file path (diretory) for handling HTTPS services.
# Leaves blank for HTTP
jsonapi_http_cert_file_path =

# Similar to the cert file path, but for private keys. Also leave
# blank for HTTP
jsonapi_http_priv_key_file_path = 

# This file contains a public key by which the xmpp agent will verify
# the auth token used by clients of the xmpp agent (in spki format)
certificate_file_path = p256-public-key.spki

#--------------------------------------------------------------------
# Redis (network cache) settings for the push DB and the orchestrator
#--------------------------------------------------------------------
pushdb_redis_ip = pushdbsqlite
pushdb_redis_port = 9
orchdb_redis_ip = mock
orchdb_redis_port = 6379
