{"type": "object", "$schema": "http://json-schema.org/draft-06/schema#", "properties": {"sipAccount": {"description": "Array of SIP account settings; use more than one object in the array if you wish to have multiple SIP accounts provisioned", "type": "array", "items": {"type": "object", "properties": {"sipAccountSettings": {"type": "object", "properties": {"username": {"type": "string", "description": "The user portion of the SIP identity for this account, for example, the \"kperera\" portion of \"<EMAIL>\"", "default": "", "examples": ["testuser"]}, "domain": {"type": "string", "description": "The  host portion of the SIP identity for this account, for example, the \"zippy-phone.com\" portion of \"<EMAIL>\"", "default": "", "examples": ["counterpath.com"]}, "password": {"type": "string", "description": "The password for this account at the SIP registrar.", "default": "", "examples": ["s3cr3t$ipP4$$word"]}, "displayName": {"type": "string", "description": "Any display name for this account; used in SIP From headers.", "default": "", "examples": ["<PERSON>"]}, "auth_username": {"type": "string", "description": "The authorization name, if required for this account by the SIP registrar.", "default": "", "examples": ["<PERSON><PERSON><PERSON><PERSON>"]}, "auth_realm": {"type": "string", "description": "The authorization realm, if required for this account by the SIP registrar. If not specified, SipAccountSettings::domain is used.", "default": "", "examples": ["exampleauthrealm"]}, "useRegistrar": {"type": "boolean", "description": "True to send a SIP REGISTER request to the proxy/registrar.", "default": false}, "outboundProxy": {"type": "string", "description": "The fully qualified host name or IP address of the outbound proxy. These formats are allowed: (1) abc.com - uses DNS SRV to resolve the _sip services at abc.com (2) sip.abc.com (fully qualified) uses DNS A/AAAA to resolve the address of the proxy/registrar (3) *************:5060 - DNS is not used to resolve the address of the proxy/registrar", "default": "", "examples": ["abc.com", "sip.abc.com", "*************:5060", "sip.abc.com:5060"]}, "alwaysRouteViaOutboundProxy": {"type": "boolean", "description": "True to send all outgoing requests/reponses via the outbound proxy specified in SipAccountSettings::outboundProxy. Note that this should only be used for interoperability with broken or incorrectly configured implementations, since it will break compliance with RFC-3261 mid-dialog/target refresh request handling.", "default": false}, "registrationIntervalSeconds": {"type": "integer", "description": "The time interval between the application's attempts to reregister in order to refresh the account registration. A value of 0 means not to reregister after the initial registration. This value is placed in the Expires header field of the register message.", "default": 3600, "examples": [3600, 180, 0]}, "minimumRegistrationIntervalSeconds": {"type": "integer", "description": "If the registration fails, the application will atleast wait this amount of time, before attempting to reregister.", "default": 5, "examples": [5, 30]}, "maximumRegistrationIntervalSeconds": {"type": "integer", "description": "If the registration fails, the application will atleast wait this amount of time, before attempting to reregister.", "default": 3600, "examples": [3600, 1800]}, "useRport": {"type": "boolean", "description": "Set to true if your proxy is able to support rport (RFC 3581). A value of true instructs the application to include the rport parameter in the REGISTER request, with the expectation that your proxy will provide the appropriate rport value in the 200 OK. When rport is true, the local SDK will attempt SIP NAT traversal by leveraging the rport and received parameters; if the rport/received parameters do not match the values in the Contact header, the account will un-REGISTER and re-REGISTER with an updated Contact header, reflecting the discovered values.Set to false if your proxy cannot support rport.If enabled, STUN will be disabled.", "default": false}, "sipTransportType": {"type": "integer", "description": "The transport to use for signaling; enter one of the values from SipAccountTransportType.", "default": 1, "oneOf": [{"const": 1, "title": "Auto", "description": "The application sets up the transport based on the capabilities of the network and the the application computer; the preferred method will be obtained via DNS (NAPTR/srv) queries."}, {"const": 2, "title": "UDP", "description": "This transport provides no signalling security."}, {"const": 3, "title": "TCP", "description": "This transport provides no signalling security."}, {"const": 4, "title": "TLS", "description": "Choose this option if you want to request signalling encryption."}]}, "excludeEncryptedTransports": {"type": "boolean", "description": "Exclude encrypted transports when sipTransportType is set to auto", "default": false}, "userAgent": {"type": "string", "description": "The name to put in the user agent in the SIP User-Agent header of outgoing traffic.", "default": "", "examples": ["Example UA"]}, "udpKeepAliveTime": {"type": "integer", "description": "Time between CR/LF keepalive messages in seconds, when the transport is set to UDP. Set to 0 to disable.", "default": 30, "examples": [30, 0]}, "tcpKeepAliveTime": {"type": "integer", "description": "Time between CR/LF keepalive messages in seconds, when the transport is set to TCP or TLS. Set to 0 to disable.", "default": 120, "examples": [120, 0]}, "useOutbound": {"type": "boolean", "description": "True to enable SIP Outbound support (RFC 5626). If enabled, STUN will be disabled.", "default": false}, "useGruu": {"type": "boolean", "description": "True to enable GRUU support.", "default": false}, "otherNonEscapedCharsInUri": {"type": "string", "description": "Allow to add other non-escaped chars to the existing set. Applicable to U<PERSON> only. Current non-escaped char set is: \"abcdefghijklmnopqrstuvwxyz\", \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\", \"0123456789\", \"-_.!~*\\()&=+$,;?/\"", "default": "", "examples": ["$%^"]}, "nameServers": {"type": "array", "description": "Optional list of name servers to be used by the SIP stack.  Used in place of any default/system nameservers returned by the OS.  Specified in the format \"*******\" (for IPv4) or \"2001:4860:4860::8888\" (for IPv6).", "items": {"type": "string", "default": "", "examples": ["*******", "2001:4860:4860::8888"]}}, "additionalNameServers": {"type": "array", "description": "Optional list of name servers to be used by the SIP stack.  Used in addition to any default/system nameservers returned by the OS.  The server specified here will be used as fall-backs in the event the OS/network does not provide any DNS servers or if those servers are not reachable. Specified in the format \"*******\" (for IPv4) or \"2001:4860:4860::8888\" (for IPv6).", "items": {"type": "string", "default": "", "examples": ["*******", "2001:4860:4860::8888"]}}, "sessionTimerMode": {"type": "integer", "description": "Session timer (RFC 4028) mode.", "default": 1, "oneOf": [{"const": 1, "title": "Inactive", "description": "When this flag is specified, Session Timers will not be used in any session, except it is explicitly required in the remote request."}, {"const": 2, "title": "Optional", "description": "When this flag is specified, Session Timers will be used in all sessions whenever remote supports and uses it."}, {"const": 3, "title": "Required", "description": "When this flag is specified, Session Timers support will be a requirement for the remote to be able to establish a session."}, {"const": 4, "title": "Always", "description": "When this flag is specified, Session Timers will always be used in all sessions, regardless whether remote supports/uses it or not."}]}, "sessionTimeSeconds": {"type": "integer", "description": "Session timer value in seconds. Must be at least 90 seconds, or session timers will not be used.", "default": 0, "examples": [600]}, "stunServerSource": {"type": "integer", "description": "The source of the STUN server to use. If useRport or useOutbound are enabled, STUN is disabled", "default": 0, "oneOf": [{"const": 1, "title": "None", "description": "When this flag is specified, no STUN server will be used."}, {"const": 2, "title": "SRV", "description": "When this flag is specified, the STUN server found as a result of a DNS SRV lookup on the configured domain will be used."}, {"const": 3, "title": "Custom", "description": "When this flag is specified, the configured STUN server will be used."}]}, "stunServer": {"type": "string", "description": "STUN server host address[:port]. Only used when stunServerSource is set to custom", "default": "", "examples": ["stun.counterpath.com"]}, "ignoreCertVerification": {"type": "boolean", "description": "Ignore certificate verification.", "default": false}, "additionalCertPeerNames": {"type": "array", "items": {"type": "string", "description": "Additional DNS names checked during certificate verification. Useful in situations where server certificates do not conform to RFC-specified constraints. The DNS names specified in this list are checked in addition to the domain. DNS names specified here when ignoreCertVerification is set to 'true' are ignored.", "default": "", "examples": ["xyz.com", "*.xyz.com"]}}, "acceptedCertPublicKeys": {"type": "array", "items": {"type": "string", "description": "Base 64 encoded public keys, checked during certificate verification. The public keys specified in this list are checked against the value in the server certificate.  If any of the keys match, the certificate will be considered valid, regardless of other certificate validation errors (even if ignoreCertVerification is set to 'false'). If the server certificate does not match any of the keys specified in this list, the certificate may still be considered valid subject to default validation procedures.", "default": "", "examples": ["c2hvdWxkIGJlIGEgcmVhbCBwdWJsaWMgY2VydCBrZXk="]}}, "requiredCertPublicKeys": {"type": "array", "items": {"type": "string", "description": "Base 64 encoded public keys, checked during certificate verification. If specified, certificate verification is performed as normal, with an additional check to ensure that the public key in the server certificate matches at least one of the values specified here. Public keys specified here when ignoreCertVerification is set to 'true' are still checked and may still cause certificate validation to fail.", "default": "", "examples": ["c2hvdWxkIGJlIGEgcmVhbCBwdWJsaWMgY2VydCBrZXk="]}}, "sipQosSettings": {"type": "integer", "description": "QoS value to be tagged at IP layer for SIP traffic.", "default": 41}, "useImsAuthHeader": {"type": "boolean", "description": "True to include an Authorization header in initial outgoing requests.", "default": false}, "minSipPort": {"type": "integer", "description": "Minimal value for SIP port. Use 0 for no minimum.", "default": 0, "examples": [12000]}, "maxSipPort": {"type": "integer", "description": "Maximal value for SIP port. Use 0 for no maximum", "default": 0, "examples": [14000]}, "useMethodParamInReferTo": {"type": "boolean", "description": "Controls whether or not the method parameter is enabled in the Refer-To header. Parameter use specified in RFC 3515 and RFC 4579.", "default": false}, "useInstanceId": {"type": "boolean", "description": "Indicator whether to use \"sip.instance\" media feature tag which appears as a \"+sip.instance\" Contact header field parameter.  This is a Uniform Resource Name (URN) that uniquely identifies this specific UA instance.", "default": false}, "ipVersion": {"type": "integer", "description": "SIP transport IP version.", "default": 0, "oneOf": [{"const": 0, "title": "IPv4", "description": "Use only IPv4"}, {"const": 1, "title": "IPv6", "description": "Use only IPv6"}, {"const": 2, "title": "Auto - Prefer IPv4", "description": "Auto detect IP version, but if both are present prefer IPv4"}, {"const": 3, "title": "Auto - Prefer IPv4", "description": "Auto detect IP version, but if both are present prefer IPv6"}]}, "sslVersion": {"type": "integer", "description": "SSL/TLS protocol version to use.", "default": -1, "oneOf": [{"const": -1, "title": "Phone default", "description": "Use the value set in SslCipherOptions during Phone init"}, {"const": 1, "title": "SSLv2", "description": "Use SSLv2. Not supported."}, {"const": 2, "title": "SSLv3", "description": "Use SSLv3. Not supported."}, {"const": 3, "title": "TLS 1.0", "description": "Use TLS 1.0"}, {"const": 4, "title": "TLS 1.1", "description": "Use TLS 1.1"}, {"const": 5, "title": "TLS 1.2", "description": "Use TLS 1.2"}, {"const": 6, "title": "TLS 1.3", "description": "Use TLS 1.3"}, {"const": 1000, "title": "Highest", "description": "SDK will negotiated highest supported version between client and server TLS 1.0 and above."}, {"const": 1002, "title": "Non-deprecated", "description": "SDK will negotiated highest supported version between client and server TLS 1.2 and above."}]}, "reRegisterOnResponseTypes": {"type": "array", "description": "Re-register when any of the response types specified here are received.", "items": {"type": "object", "properties": {"method": {"type": "string", "description": "SIP method name the response is for", "default": "", "examples": ["INVITE", "REGISTER"]}, "responseCode": {"type": "integer", "description": "SIP response code of the response", "default": 0, "examples": [407, 503]}}}}, "enableRegeventDeregistration": {"type": "boolean", "description": "True to automatically handle network de-registration requests using the \"reg\" SUBSCRIBE/NOTIFY event package", "default": false}, "XCAPRoot": {"type": "string", "description": "xcap root location inside domain. Part of the path that goes between domain and AUID", "default": "", "examples": ["/XCAP/test"]}, "tunnelConfig": {"type": "object", "description": "Configuration to use for for transporting signaling and media via tunnel. Not Supported"}, "capabilities": {"type": "array", "description": "Additional parameters to be added to SIP Contact headers.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Parameter name", "default": "", "examples": ["testpname"]}, "value": {"type": "string", "description": "Parameter value", "default": "", "examples": ["testpvalue"]}}}}, "additionalFromParameters": {"type": "array", "description": "Additional parameters to be added to SIP From header on outgoing requests", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Parameter name", "default": "", "examples": ["testpname"]}, "value": {"type": "string", "description": "Parameter value", "default": "", "examples": ["testpvalue"]}}}}, "sourceAddress": {"type": "string", "description": "Limit networking usage to this address if specified.  Address can be IPv4 or IPv6 depending on setting of ipVersion.", "default": "", "examples": ["***********", "2001:0db8:85a3:0000:0000:8a2e:0370:7334"]}, "preferPAssertedIdentity": {"type": "boolean", "description": "Provide the value of the P-Asserted-Identity header instead of From/To as the identity of the sender for received messages, if present. (RFC 3325)", "default": true}, "autoRetryOnTransportDisconnect": {"type": "boolean", "description": "Automatically re-open connections for registration when closed. This setting is for platforms that make preferable solutions difficult or impossible to deploy. Applies to TCP/TLS only.", "default": false}, "keepAliveMode": {"type": "integer", "description": "Configures the keep-alive strategy used by the SDK for SIP.", "default": 0, "oneOf": [{"const": 0, "title": "<PERSON><PERSON><PERSON>", "description": "Use transport-specific default keep-alives; currently this implies CRLFCRLF for both SIP/UDP and SIP/TCP."}, {"const": 1, "title": "No keep-alives", "description": "No keep-alives sent by the SDK client. NOTE: Using this setting is not practical in deployments where an SDK client may be behind a NAT/firewall, since the SIP proxy will have no means of forwarding requests to the client if/when the firewall pinhole closes (for UDP) or the TCP connection is closed."}, {"const": 2, "title": "CRLFCRLF keep-alives", "description": "Use CRLFCRLF keep-alives for both SIP/UDP and SIP/TCP."}]}, "useRinstance": {"type": "boolean", "description": "Configures the SDK's preferred mechanism for identifying its registration binding. Rinstance is enabled by default.  It permits rport re-registration (and thus SIP NAT traversal) to succeed in a wide variety of networks. Rinstance should only be disabled if specific interop issues with it are encountered.", "default": true}, "enableNat64Support": {"type": "boolean", "description": "Support the use of IPv4-only SIP services/infrastructure from an IPv6-only client. Uses DNS64 to translate IPv4 addresses into IPv6 addresses. Uses fake IPv4 addresses in SIP Via and Contact headers, as well as SDP (media) c= lines. Requires a Session Border Controller (SBC) with latching support, or a STUN server. Enabled by default, but only used when IPv6 is used to contact an IPv4-only SIP proxy/registrar. On platforms/networks where alternative IPv4-to-IPv6 transition mechanisms are used (e.g. 464XLAT, Android CLAT) this feature is typically not necessary. NOTE: Requires that the configured name servers (e.g. from the OS or using SipAccountSettings) support DNS64.", "default": true}, "usePrivacyHeaderOnlyForAnonymous": {"type": "boolean", "description": "True to prevent modifying the From/Contact headers and only include the Privacy header for anonymous requests (RFC 3323).", "default": false}, "transportHoldover": {"type": "integer", "description": "If any SIP calls are active, the transport IP verson the call started on matches that of this setting, and the device has transitioned from WWAN to WiFi, the SDK will attempt to maintain the existing connection to the SIP server over the WWAN transport until all calls have ended. This strategy is an alternate to the SDK's default behaviour for call continuity where it will attempt a re-INVITE with updated c-line when transitioning to a new network. Currently only supported on iOS.", "default": 0, "oneOf": [{"const": 0, "title": "None", "description": "Do not use transport holdover."}, {"const": 1, "title": "All", "description": "Use transport holdover on both IPv4 and IPv6 networks."}, {"const": 2, "title": "IPv4", "description": "Use transport holdover on only IPv4 networks."}, {"const": 3, "title": "IPv6", "description": "Use transport holdover on only IPv6 networks."}]}, "useOptionsPing": {"type": "boolean", "description": "Enable pinging the server using OPTIONS messages to check that it is alive. See Packetizer's POCS-1 (hive1.hive.packetizer.com/users/packetizer/pocs/POCS-1.pdf). On a failed ping, the account will attempt to reregister and the account status will change to Status_Refreshing.", "default": false}, "optionsPingInterval": {"type": "integer", "description": "The interval in milliseconds between OPTIONS pings.", "default": 0}, "userCertificatePEM": {"type": "string", "description": "PEM client certificate used in mutual authentication. Can be a path to a *.pem file or a string containing the certificate.", "default": "none"}, "userPrivateKeyPEM": {"type": "string", "description": "PEM client private key used in mutual authentication. Can be a path to a *.pem file or a string containing the private key.", "default": ""}, "forceListenSocket": {"type": "boolean", "description": "Forces the SDK to open a listen socket for SIP. In some cases the SDK will automatically not open a listen socket for TCP or TLS: a) On iOS, both TCP and TLS listen socket will not be opened: to work around iOS defects (O-4002).  b) On any platform, for TLS if no client certificate is provided via userCertificatePEM. The application should be aware of repercussions of forcing a listen socket.", "default": false}, "enableDNSResetOnRegistrationRefresh": {"type": "boolean", "description": "If DNS failover is in use, enable to have the SDK account periodically attempt to revert back to the primary SIP server, if it had previously failed over to the secondary (non-preferred) SIP server. If not enabled, the SDK account will not proactively attempt to revert to the primary SIP server in the above case. When this setting is enabled, the following pre-conditions are required before a DNS reset (and revert to primary SIP server) are triggered:  - Account is registered   - Current registration does not match preferred target (based on DNS results) - No active calls - Preferred target is active (based on response to SIP OPTIONS request the SDK account sends out)  - History of triggered DNS resets is less than threshold (10 resets in 600 seconds)", "default": false}, "enableAuthResetUponDNSReset": {"type": "boolean", "description": "Enabling will reset the authentication everytime the DNS is reset, only applicable if DNS reset has been enabled.", "default": false}, "cipherSuite": {"type": "string", "description": "OpenSSL cipher suite string to be used for TLS connections.", "default": ""}}}}}}, "conversation": {"type": "array", "description": "Array of conversation settings; use more than one object in the array if you wish to have different sets of conversation settings; e.g. one for each SIP account", "items": {"type": "object", "properties": {"conversationSettings": {"type": "object", "properties": {"sessionName": {"type": "string", "description": "Sets the session name that appears in SDP bodies sent by the SDK.", "default": "", "examples": ["CPCAPI-based-UA"]}, "natTraversalMode": {"type": "integer", "description": "Sets the NAT traversal mode used for media.", "default": 0, "oneOf": [{"const": 0, "title": "None", "description": "No NAT traversal."}, {"const": 1, "title": "Auto", "description": "Attempt ICE if a STUN server is configured and the remote end supports ICEttempt ICE if a STUN server is configured and the remote end supports ICE"}, {"const": 2, "title": "STUN", "description": "Use STUN as NAT traversal method."}, {"const": 3, "title": "TURN", "description": "Use TURN as NAT traversal method."}, {"const": 4, "title": "ICE", "description": "Use ICE as NAT traversal method."}]}, "natTraversalServerSource": {"type": "integer", "description": "The source of the NAT traversal server to use.", "default": 0, "oneOf": [{"const": 0, "title": "None", "description": "When this flag is specified, no NAT traversal server will be used."}, {"const": 1, "title": "SRV", "description": "When this flag is specified, the NAT traversal server found as a result of a DNS SRV lookup on the configured domain will be used"}, {"const": 2, "title": "Custom", "description": "When this flag is specified, the configured NAT traversal server will be used (of natTraversalServer setting)."}]}, "natTraversalServer": {"type": "string", "description": "Sets the address of the NAT traversal server (STUN or TURN). Only used when natTraversalServerSource == NatTraversalServerSource_Custom", "default": "", "examples": ["stun.counterpath.com"]}, "holdMode": {"type": "integer", "description": "Used to specify the RFC support level for call 'hold' functionality.", "default": 1, "oneOf": [{"const": 1, "title": "RFC3264", "description": "Use RFC 3264 standard."}, {"const": 2, "title": "RFC2543", "description": "Use RFC 2543 standard."}]}, "prackMode": {"type": "integer", "description": "Used to specify the PRACK support. Can only be set before account is enabled. If the account has been enabled, it must be disabled and re-enabled to change the PRACK mode.", "default": 0, "oneOf": [{"const": 0, "title": "Disabled", "description": "PRACK use is not supported."}, {"const": 1, "title": "Supported", "description": "PRACK use is supported."}, {"const": 2, "title": "Required", "description": "PRACK use is required."}, {"const": 3, "title": "SupportedUasAndUac", "description": "PRACK use is supported both as server and client."}]}, "minRtpPort": {"type": "integer", "description": "Used to specify the port range for non-audio and non-video RTP.", "default": 0, "examples": [60024]}, "maxRtpPort": {"type": "integer", "description": "Used to specify the port range for non-audio and non-video RTP.", "default": 0, "examples": [60030]}, "minRtpPortAudio": {"type": "integer", "description": "Used to specify the audio RTP port range.", "default": 0, "examples": [60034]}, "maxRtpPortAudio": {"type": "integer", "description": "Used to specify the audio RTP port range.", "default": 0, "examples": [60040]}, "minRtpPortVideo": {"type": "integer", "description": "Used to specify the video RTP port range.", "default": 0, "examples": [60044]}, "maxRtpPortVideo": {"type": "integer", "description": "Used to specify the video RTP port range.", "default": 0, "examples": [60050]}, "turnUsername": {"type": "string", "description": "Used to specify the username used when connecting to a TURN server.", "default": ""}, "turnPassword": {"type": "string", "description": "Used to specify the password used when connecting to a TURN server.", "default": ""}, "includePPreferredIdentity": {"type": "boolean", "description": "If true, the P-Preferred-Identity header will be included in outgoing INVITE requests.", "default": false}, "includePAssertedIdentity": {"type": "boolean", "description": "If true, the P-Asserted-Identity header will be included in 200 OK responses.", "default": false}, "includeAttribsForStaticPLs": {"type": "boolean", "description": "If true, these attribute lines will be included in the SDP:  a=rtpmap:9 G722/8000       a=rtpmap:8 PCMA/8000         a=rtpmap:0 PCMU/8000", "default": false}}}}}}, "xmppAccount": {"type": "array", "description": "Array of XMPP account settings; use more than one object in the array if you wish to have multiple XMPP accounts provisioned", "items": {"type": "object", "properties": {"xmppAccountSettings": {"type": "object", "properties": {"username": {"type": "string", "description": "The user portion of the XMPP identity (JID) for this account, for example, the \"kperera\" portion of \"<EMAIL>\"", "default": "", "examples": ["testuser"]}, "domain": {"type": "string", "description": "The host portion of the XMPP identity (JID) for this account, for example, the \"zippy-phone.com\" portion of \"<EMAIL>\"", "default": "", "examples": ["counterpath.com"]}, "password": {"type": "string", "description": "The password for this account", "default": "", "examples": ["s3cr3tXmPpP4$$word"]}, "proxy": {"type": "string", "description": "The fully qualified host name or IP address of the outbound proxy.", "default": "", "examples": ["abc.com", "xmpp.abc.com", "*************:5222", "sip.abc.com:5222"]}, "port": {"type": "integer", "description": "The listening port of the server or proxy. Specifying a non-zero port would turn off DNS SRV lookup.", "default": 0, "examples": [12000]}, "resource": {"type": "string", "description": "The resource portion of the full XMPP identity for this account, leave empty to have server generate one for the account.", "default": ""}, "priority": {"type": "integer", "description": "The priority for this account's XMPP resource.", "default": 0}, "softwareName": {"type": "string", "description": "Part of entity capability discovery.", "default": "CPCAPI2-based Client"}, "softwareVersion": {"type": "string", "description": "Part of entity capability discovery.", "default": "1.0"}, "softwareOS": {"type": "string", "description": "Part of entity capability discovery.", "default": ""}, "identityCategory": {"type": "string", "description": "Part of entity capability discovery.", "default": "client"}, "identityType": {"type": "string", "description": "Part of entity capability discovery.", "default": "phone"}, "connectTimeOut": {"type": "integer", "description": "Time (in seconds) to wait for TCP to connect to a host before giving up.", "default": 10}, "keepAliveTime": {"type": "integer", "description": "Periodic connection keep alive paramter. Specifying zero keepAliveTime (in seconds) will disable keep alive.", "default": 30}, "usePingKeepAlive": {"type": "boolean", "description": "Periodic connection keep alive paramter. Specifying true for usePingKeepAlive will use XEP instead of sending white space keep alive.", "default": false}, "fileTransfileProxies": {"type": "array", "description": "List of external file transfer proxies. Format: [<username>[:<password>]@]<server>[:<port>];jid=<jid>", "items": {"type": "string", "default": "", "examples": ["username:<EMAIL>:7777;jid=proxy.jabber.org"]}}, "enableLocalSocks5Proxy": {"type": "boolean", "description": "Specifying whether to host local Socks5 proxy or not.", "default": true}, "enableRemoteStreamHostDiscovery": {"type": "boolean", "description": "Specifying whether to discovery remote stream host or not.", "default": true}, "sslVersion": {"type": "integer", "description": "SSL/TLS protocol version to use.", "default": -1, "oneOf": [{"const": -1, "title": "Phone default", "description": "Use the value set in SslCipherOptions during Phone init"}, {"const": 1, "title": "SSLv2", "description": "Use SSLv2. Not supported."}, {"const": 2, "title": "SSLv3", "description": "Use SSLv3. Not supported."}, {"const": 3, "title": "TLS 1.0", "description": "Use TLS 1.0"}, {"const": 4, "title": "TLS 1.1", "description": "Use TLS 1.1"}, {"const": 5, "title": "TLS 1.2", "description": "Use TLS 1.2"}, {"const": 6, "title": "TLS 1.3", "description": "Use TLS 1.3"}, {"const": 1000, "title": "Highest", "description": "SDK will negotiated highest supported version between client and server TLS 1.0 and above."}, {"const": 1002, "title": "Non-deprecated", "description": "SDK will negotiated highest supported version between client and server TLS 1.2 and above."}]}, "cipherSuite": {"type": "string", "description": "OpenSSL cipher suite string to be used for TLS connections.", "default": "", "examples": ["TLS_AES_256_GCM_SHA384:DHE-RSA-AES128-GCM-SHA256:TLS_AES_128_GCM_SHA256:ECDHE-RSA-AES256-GCM-SHA384"]}, "ignoreCertVerification": {"type": "boolean", "description": "Ignore certificate verification.", "default": false}, "additionalCertPeerNames": {"type": "array", "items": {"type": "string", "description": "Additional DNS names checked during certificate verification. Useful in situations where server certificates do not conform to RFC-specified constraints. The DNS names specified in this list are checked in addition to the domain. DNS names specified here when ignoreCertVerification is set to 'true' are ignored.", "default": "", "examples": ["xyz.com", "*.xyz.com"]}}, "acceptedCertPublicKeys": {"type": "array", "items": {"type": "string", "description": "Base 64 encoded public keys, checked during certificate verification. The public keys specified in this list are checked against the value in the server certificate.  If any of the keys match, the certificate will be considered valid, regardless of other certificate validation errors (even if ignoreCertVerification is set to 'false'). If the server certificate does not match any of the keys specified in this list, the certificate may still be considered valid subject to default validation procedures.", "default": "", "examples": ["c2hvdWxkIGJlIGEgcmVhbCBwdWJsaWMgY2VydCBrZXk="]}}, "requiredCertPublicKeys": {"type": "array", "items": {"type": "string", "description": "Base 64 encoded public keys, checked during certificate verification. If specified, certificate verification is performed as normal, with an additional check to ensure that the public key in the server certificate matches at least one of the values specified here. Public keys specified here when ignoreCertVerification is set to 'true' are still checked and may still cause certificate validation to fail.", "default": "", "examples": ["c2hvdWxkIGJlIGEgcmVhbCBwdWJsaWMgY2VydCBrZXk="]}}, "logXmppStanzas": {"type": "boolean", "description": "Output all sent and received XMPP stanzas to debug log.", "default": true}, "ipVersion": {"type": "integer", "description": "XMPP transport IP version.", "default": 0, "oneOf": [{"const": 0, "title": "IPv4", "description": "Use only IPv4"}, {"const": 1, "title": "IPv6", "description": "Use only IPv6"}, {"const": 2, "title": "Auto - Prefer IPv4", "description": "Auto detect IP version, but if both are present prefer IPv4"}, {"const": 3, "title": "Auto - Prefer IPv4", "description": "Auto detect IP version, but if both are present prefer IPv6"}]}, "nameServers": {"type": "array", "description": "Optional list of name servers to be used by the XMPP stack.  Used in place of any default/system nameservers returned by the OS.  Specified in the format \"*******\" (for IPv4) or \"2001:4860:4860::8888\" (for IPv6).", "items": {"type": "string", "default": "", "examples": ["*******", "2001:4860:4860::8888"]}}, "additionalNameServers": {"type": "array", "description": "Optional list of name servers to be used by the XMPP stack.  Used in addition to any default/system nameservers returned by the OS.  The server specified here will be used as fall-backs in the event the OS/network does not provide any DNS servers or if those servers are not reachable. Specified in the format \"*******\" (for IPv4) or \"2001:4860:4860::8888\" (for IPv6).", "items": {"type": "string", "default": "", "examples": ["*******", "2001:4860:4860::8888"]}}, "enableStreamManagement": {"type": "boolean", "description": "Stream Management setting (XEP-0198).", "default": true}, "enableStreamResumption": {"type": "boolean", "description": "Stream Management setting (XEP-0198). Relies on enableStreamManagement to be true", "default": false}, "streamManagementId": {"type": "string", "description": "DEPRECATED: the SDK handles stream resumption internally if possible. No more app layer intervene is required. Used for stream resumption. Ignored if enableStreamResumption is false.", "default": ""}, "streamManagementSequence": {"type": "integer", "description": "DEPRECATED: the SDK handles stream resumption internally if possible. No more app layer intervene is required. Used for stream resumption. Ignored if enableStreamResumption is false.", "default": 0}, "publishInitialPresenceAsAvailable": {"type": "boolean", "description": "If this parameter is true, the SDK will automatically publish initial presence as Available after the account is connected but before the account status is changed to Status_Connected. If this paremeter is false, the SDK will not publish any initial presence and it is up to the app layer to publish a desired initial presence upon the account status is changed to Status_Connected.", "default": true}, "fallbackOnResourceConflict": {"type": "boolean", "description": "A non-empty XmppAccountSettings.resource renders this parameter as false. If this parameter is true, the SDK will request for server resource bind for reconnection instead. If this parameter is false, the SDK will reuse the previous successful bound resource for reconnection.", "default": false}, "enableCompression": {"type": "boolean", "description": "If this parameter is true, the SDK will automatically negotiate compression as stream feature. If this paremeter is false, the SDK will not negotiate compression as stream feature.", "default": true}, "enableXmppPresence": {"type": "boolean", "description": "If this parameter is true, the SDK will register for XMPP presence events. Debug use only.", "default": true}, "enableXmppStanza": {"type": "boolean", "description": "If this parameter is true, the SDK will register for XMPP stanza events. Debug use only.", "default": true}, "logTlsEncryptionKey": {"type": "boolean", "description": "If this parameter is true, the SDK will log TLS encryption keys.", "default": false}}}}}}, "customSettings": {"type": "object", "description": "An example of a custom settings section. The SDK will ignore any section it does not recognize (such as a section like this one). To use custom settings, an app must manually parse this JSON document received from Stretto.", "properties": {"appSettingA": {"type": "boolean", "description": "An example of a custom boolean setting.", "default": false}, "appSettingB": {"type": "integer", "description": "An example of a custom integer setting.", "default": 0, "examples": [56]}, "appSettingZ": {"type": "string", "description": "An example of a custom string setting.", "default": "", "examples": ["foobar"]}}}}}