/* THIS IS A CUSTOM CSS TO MAKE strettoProvisioning.html 
   LOOK LIKE THE REST OF THE FLARE PROJECT. DO NOT 
   OVERWRITE.   */
   
   
@font-face 
{
	font-family: 'Lato';
	src: url('../../../Resources/Fonts/Lato-Regular.eot') format('embedded-opentype'), url('../../../Resources/Fonts/Lato-Regular.woff') format('woff'), url('../../../Resources/Fonts/Lato-Regular.ttf') format('truetype');
}

@font-face 
{
	font-family: 'Ubuntu';
	src: url('../../../Resources/Fonts/Ubuntu-Regular.eot') format('embedded-opentype'), url('../../../Resources/Fonts/Ubuntu-Regular.woff') format('woff'), url('../../../Resources/Fonts/Ubuntu-Regular.ttf') format('truetype');
}

* {
  margin: 0;
  padding: 0;
}
/* body,
html {
  min-height: 100%;
  margin: 0;
  padding: 0;
  font-family: "Verdana";
  color: #002633;
  font-size: 12px;
} */
body {
	font-family: 'Lato', sans-serif;
	border: 0;
	mc-hyphenate: never;
	padding-bottom: 0px;
	padding-left: 0px;
	padding-right: 0px;
	color: #666666;
	background-image: none;
	background-color: transparent;
	margin-bottom: 0px;
	margin: 0px;
	counter-reset: h2nums;
	top: 0px;
	padding-top: 4em;
	font-size: 1.0em;
}
header
{
	
}
article {
  background: #fff;
  margin: 40px;
  padding: 40px;
  border-radius: 3px;
  overflow: hidden;
}
article > header {
}
article > header p {
	display: none;
  /* width: 600px; */
}
article > header a {
	display: none;
}
footer {
  display:none;
}
.container {
	display: none;
}
.container.top {
}
.container.bot {
}
.required {
  color: #ad0000;
  position: absolute;
  font-size: 8px;
  left: -10px;
  top: 1px;
  /* text-shadow: 0.5px 0.5px #002633; */
}
dl {
  float: left;
  width: 100%;
}
dt {
  font-weight: bold;
  position: relative;
  width: 200px;
  color: #666666;
  clear: left;
  float: left;
  margin: 0 0 7px 3px;
}
dt.parent {
  cursor: pointer;
  color:#ff6600;
}
dd {
  display: inline-block;
  overflow: hidden;
  margin: 0 0 7px 3px;
  float: left;
}
dd.new-block {
  clear: left;
  display: block;
  padding: 0 0 0 17px;
}
dd:last-child {
  margin-bottom: 0;
}
dd p {
  margin: 0;
  max-width: 600px;
}
.index ul {
  padding: 0;
}
li {
  clear: left;
  overflow: hidden;
  padding: 7px;
  border-radius: 3px;
  position: relative;
}
li:nth-child(odd) {
  background: #eee;
}
li:nth-child(even) {
  background: #ddd;
}
h1 {
	font-family: 'Ubuntu', sans-serif;
	font-size: 1.5em;
	color: #000000;
	margin-bottom: 8px;
	left: 0px;
	right: 0px;
	padding-left: 10px;
	padding-right: 10px;
	position: fixed;
	top: 0;
	margin-top: 0px;
	padding-top: 10px;
	padding-bottom: 10px;
	background-color: #f9f9f9;
	mc-disable-glossary-terms: true;
	border-bottom: solid 4px #ffffff;
	page-break-after: avoid;
	counter-reset: h2step;
	z-index: 1;
}
h1:before
{
	/* Insert a title */
	content: "Stretto Platform provisioning response";
}
h2 {
	font-family: 'Ubuntu', sans-serif;
	font-size: 1.5em;
	font-weight: normal;
	margin-bottom: 4px;
	margin-top: 20px;
	color: #333333;
	mc-disable-glossary-terms: true;
	page-break-after: avoid;
}
h3
{
	font-family: 'Ubuntu', sans-serif;
	font-size: 1.1em;
	font-weight: bold;
	margin-bottom: 4px;
	margin-top: 16px;
	color: #333333;
	mc-disable-glossary-terms: true;
	page-break-after: avoid;
}

h4
{
	color: #333333;
	mc-disable-glossary-terms: true;
	page-break-after: avoid;
}

h5
{
	color: #333333;
	mc-disable-glossary-terms: true;
	page-break-after: avoid;
}

h6
{
	color: #333333;
	mc-disable-glossary-terms: true;
}
a:link
{
	text-decoration: none;
	color: #ff6600;
}

a:visited
{
	text-decoration: none;
	color: #ff9900;
}

a:hover
{
	color: #ff9900;
	text-decoration: underline;
}

a:active
{
	color: #ffcc00;
	text-decoration: underline;
}
