To generate documentation for JSON files first create stripped down schema files for JSON files you want to document. 
This can be done either by writing it yourself using example/sipProvisioned.json as example or by generating schema file
with https://jsonschema.net/ and then stripping it down to look like example/sipProvisioned.json. Schema files are 
stripped down to remove unnecessary data from documentation files. When this is done use matic to generate html 
documentation. Resulting HTML files will be found in web folder. 

Matic instructions:

Prerequisits: npm - Node Package Manager

-----------------------------------------
Installation
-----------------------------------------

    npm install -g matic
    npm install jade

-----------------------------------------
Usage
-----------------------------------------

Matic is designed to be highly configurable through a .maticrc file at the root of your project but is configured for 
a basic set up by defult.

A typical project layout using default settings.

|____.maticrc [optional]
|____schemas
| |____my-schema.json
|____templates
  |____default.jade

Essentially you will need:

    A folder with at least one schema document.
    A folder with at least one template file.
    an optional .maticrc file for custom settings.

By default Matic will use your schema(s) and template(s) to generate a set of HTML files into a folder called web. 
However there are many ways in which this can be customised through the .maticrc file.

Then to build your documenation; from the route of your project in npm console, run:
 
    matic


For more information on matic usage see:
https://github.com/mattyod/matic
https://github.com/mattyod/matic-draft4-example