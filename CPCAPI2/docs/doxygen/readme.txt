How SDK Docs are structured.

There are 3 main areas:
A <branch>\docs\doxygen
B <branch>\docs\to_publish
C <branch>\package\docs

(A) contains the doxygen project files used to generate the HTML:
- doxygen, which contains shared project files. These files are committed. 
- one folder per language, the folder contains the doxy project file for that language.  *committed*
- Each lang folder has a subfolder called html_<lang>. This is the "html target" where the generated html files are built to. ***never committed***

(B) is the holding area for docs. It contains:
- one folder per OS platform (NOT per lang) *committed*
- each folder contains the PDF docs (dev guide, platform guide, 3rd party credits) relevant to that OS platform. *Committed*

(C) this is the target package folder. To build the docs for each package, run doxygen on the doxyfile file from the relevant folder from (A) ***never Committed***

Creating doxygen
Each file has different values in some fields: 

project_name. example: CounterPath Softphone SDK for C++

output_directory: location in package for this programming language. The destination directory can get changed each time doxygen is run. For example, when documneting and testing, you may want to have files put right under the doxy folder. But when running in order to create package, the folder must be in the package/docs folder.

optimize_xx, extension_mapping (only for some langs). For example, OPTIMIZE_OUTPUT_JAVA = yes when building java

input. For example, ../../CPCAPI2/interface

file patterns: For Objective-c, this says *.h

extension_mapping. For objective-c, this says: h=Objective-C





