{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Getting list of chatrooms and joining an Xmpp Multi user chat room\
\
Client->XmppMultiUserChatManager:getInterface(phone)\
Client->XmppMultiUserChatManager:setHandler(accountHandle, handler)\
note right of SDK Process,XmppMultiUserChatHandler:Be prepared to handle errors:\
SDK Process->XmppMultiUserChatHandler:onError()\
\
note right of Client,XmppMultiUserChatManager\
Get a list of all multi user chat \
rooms accessible to this account.\
end note\
Client->XmppMultiUserChatManager: getRoomList(accountHandle)\
\
note right of SDK Process,XmppMultiUserChatHandler\
Notifies that a list of rooms has been \
retrieved from the server.\
end note\
SDK Process->XmppMultiUserChatHandler:onRoomListRetrieved(accountHandle,RoomListRetrievedEvent) \
\
note right of Client,XmppMultiUserChatManager\
Allocates a new multi user chat \
handle within the SDK. This \
function is used in concert with \
other operations e.g. join(...).\
end note\
Client->XmppMultiUserChatManager: create(accountHandle)\
\
Client->XmppMultiUserChatManager: join(multiUserChatHandle,room,nickname,password,historyRequester,historyToAdd)\
\
note right of SDK Process,XmppMultiUserChatHandler\
Notifies that the multi user chat \
is ready to be used.\
end note\
 SDK Process->XmppMultiUserChatHandler:onMultiUserChatReady(multiUserChatHandle,MultiUserChatReadyEvent)\
\
\
}