{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Publishing remote presence changes for Xmpp Roster\
Client->XmppAccount:getInterface(phone)\
Client->XmppAccount:setHandler(accountHandle, handler)\
note right of SDK Process,XmppRosterHandler:Be prepared to handle errors:\
SDK Process->XmppRosterHandler:onError()\
note left of Client,XmppAccount: Send out presence status\
Client->XmppAccount: publishPresence( account, presence, note)\
}