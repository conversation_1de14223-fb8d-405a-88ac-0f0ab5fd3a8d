{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Getting Initial roster and presence, and presence changes for items on the roster\
\
Client->XmppRoster:getInterface(phone)\
Client->XmppRoster:setHandler(accountHandle, handler)\
note right of SDK Process,XmppRosterHandler:Be prepared to handle errors:\
SDK Process->XmppRosterHandler:onError()\
note right of Client,XmppRoster\
Create a roster handle\
for the specified account \
and start querying the roster\
as soon as possible\
end note\
Client->XmppRoster: createRoster(accountHandle)\
\
\
\
SDK Process->XmppRosterHandler:\\n\\n\\n\\n\\nonRosterUpdate(rosterHandle, rosterUpdateEvent)\
note right of SDK Process, XmppRosterHandler\
 \\n\
 Notifies the application when one or more items in \
the XmppRoster have updated presence status \
information available.\\n\\n\
end note\
\
SDK Process->XmppRosterHandler:onRosterPresence(rosterHandle, rosterPresenceEvent)\
\
\
onRosterUpdate note:\
 Will be called the first time with the current roster \
after account is enabled. Notifies the application \
when the XmppRoster has been updated on the \
server.\
}