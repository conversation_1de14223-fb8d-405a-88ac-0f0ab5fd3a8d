option footer=none

participant Client
participant "SipEventManager" as SM
participant "SDK process" as SDKP
participant "SipEventSubscriptionHandler" as SH

Client->>SM: getInterface()
Client->>SM: setHandler()

Note right of SDKP: Be prepared to handle errors:
SDKP->>SH: onError()

 createSubscription() returns a subscription handle
Client->>SM: createSubscription()

 The settings parameter specifies the event to subscribe to
Client->>SM: \n\napplySubscriptionSettings(subscription handle, settings)

 Calling addParticipant() adds the target address to the internal list maintained by the SDK
Client->>SM:  \n\naddParticipant(subscription handle, target address)

Client->>SM: start(subscription handle)



SDKP->>SH: onNewSubscription\n(subscription handle, event)

Note right of SDKP: When the subscription request is \naccepted or rejected by the other party:
SDKP->>SH: onSubscriptionStateChanged\n(subscription handle, event)

 Also be prepared to handle \nonIncomingEventState() \nasynchronously; it is called each \ntime the other target publishes \nan event.
SDKP->>SH: onIncomingEventState\n(subscription handle, event)

Note right of Client: \nOptionally, to terminate subscribing

Client->>SM: end(subscription handle)
SDKP->>SH: onSubscriptionEnded\n(subscription handle, event)