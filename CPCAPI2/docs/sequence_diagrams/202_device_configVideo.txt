option footer=none

participant Client
participant "VideoManager" as AM
participant "SDK process" as SDKP
participant "VideoHandler" as AH

Client->>AM: getInterface()
Client->>AM: setHandler()

Client->>AM: queryDeviceList()
SDKP->>AH: onVideoDeviceListUpdated()
Client->>Client: process the list
Client->>AM: setCaptureDevice()
 Note right of Client: Be prepared to handle onVideoDeviceListUpdated \nat any time; this function will be called \nwhenever a device change is detected, so, each \ntime the user unplugs a device or plugs in a device.