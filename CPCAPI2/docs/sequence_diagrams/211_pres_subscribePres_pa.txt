option footer=none

participant Client
participant "SipPresenceManager" as PM
participant "SDK process" as SDKP
participant "Sip<PERSON>resence<PERSON><PERSON><PERSON>" as PH

Client->>PM: getInterface()
Client->>PM: set<PERSON><PERSON><PERSON>()

Note right of SDKP: Be prepared to handle errors:
SDKP->>PH: onError()


Client->>PM: createSubscription()

Client->>PM: setEventServer(subscription handle, \naddress of PA)
Client->>PM: applySubscriptionSettings\n(subscription handle, settings)

Note right of Client: Call addParticipant() for \nevery target to subscribe to
Client->>PM:  addParticipant\n(subscription handle, target address)


Client->>PM: start(subscription handle)

SDKP->>PH: onNewSubscription\n(subscription handle, event)
SDKP->>PH: onSubscriptionStateChanged\n(subscription handle, event)

 Also be prepared to handle onIncomingPresenceStatus() asynchronously; it is called each time another target's presence changes 
SDKP->>PH: onIncomingPresenceStatus\n(subscription handle, event)


Note right of Client: To terminate subscribing to \nall targets
Client->>PM: end(subscription handle)
SDKP->>PH: onSubscriptionEnded\n(subscription handle, event)

