option footer=none
participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHand<PERSON>" as SCH

note left of Client: Local user holds or unholds
Client->>SCM: 	hold() or unhold()
SDKP->>SCH: 	onConversationMediaChanged()

note left of Client: Local party requests a change to media
Client->>SCM: 	sendMediaChangeRequest()
 when remote party accepts the change
SDKP->>SCH: 	onConversationMediaChanged()

note left of Client: Remote party requests a change to media

SDKP->>SCH: 	onConversationMediaChangeRequest()
Client->>SCM: 	accept() or reject()
SDKP->>SCH: 	onConversationMediaChanged()

note left of Client: Remote party hangs up
SDKP->>SCH: 	onIncomingHangupRequest()
Client->>SCM: 	end()
SDKP->>SCH: 	onConversationEnded()
