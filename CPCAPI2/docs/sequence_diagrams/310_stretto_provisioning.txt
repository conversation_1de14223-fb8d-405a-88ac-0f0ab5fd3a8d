participant "App" as D
participant "StrettoProvisioning Module" as A
participant "Account Module" as B
participant "Stretto" as C

D->A:create()

A-->D:StrettoProvisioningHandle

note left of D: Pass login credentials to \nconfigureSettings
D->A:configureSettings(StrettoProvisioningHandle, \nStrettoProvisioningSettings)

D->A:applySettings(StrettoProvisioningHandle)

note left of D: Perform provisioning\nrequest
D->A:request(StrettoProvisioningHandle)

A->C: Provisioning request
note right of C: Stretto authenticates login credentials
C ->A: Provisioning response
A->D: onProvisioningSuccess(StrettoProvisioningEvent)
D->B: decodeProvisioningResponse(string)
B-->D: SipAccountSettings[]

state over A: Initial state
