{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Receiving a file with Xmpp File Transfer\
\
Client->XmppFileTransferManager:getInterface(phone)              \
\
Client->XmppFileTransferManager:\\n\\n\\n\\n\\nsetHandler(accountHandle, handler)\
note right of SDK Process,XmppFileTransferHandler:Be prepared to handle errors:\
SDK Process->XmppFileTransferHandler:onError()\
\
\
\
\
note right of SDK Process,XmppFileTransferHandler\
\\n\
Callback invoked by the SDK when a new file transfer \
is created, either by sending or receiving.\
\\n\
end note\
 SDK Process->XmppFileTransferHandler:onNewFileTransfer(fileTransferHandle,NewFileTransferEvent)\
note left of Client, XmppFileTransferManager:\
Receive incoming file\
end note\
note right of Client, XmppFileTransferManager:\
This method should be called before accepting/starting a\
 file transfer, it informs the SDK which file transfer\
 items will be used and the local name/path can be\
 modified as well.\
end note\
Client->XmppFileTransferManager:\\n\\n\\n\\n\\nconfigureFileTransferItems(fileTransferHandle, fileTransferItems)\
note right of Client,XmppFileTransferManager:\
Accepts the file transfer\
\
end note\
Client->XmppFileTransferManager:\\n\\n\\naccept(fileTransferHandle)\
\
SDK Process->XmppFileTransferHandler:\\n\\n\\n\\n\\nonFileTransferItemProgress(fileTransferHandle, FileTransferItemProgressEvent) \
\
\
\
\
note right of SDK Process, XmppFileTransferHandler\
 \\n  \
 Callback invoked by the SDK when a file transfer has\
 completed(including all of its file transfer items)\
\\n\
end note\
SDK Process->XmppFileTransferHandler:onFileTransferEnded(fileTransferHandle,FileTransferEndedEvent )\
\
\
createFileTransfer note:\
Creates a new (empty) file transfer item handle, \
used for outbound transfers. The handle will be \
unique across the SDK. After items are created, \
they should be "configured" using the \
configureFileTransferItems method, before \
start() is called.\
\
ocnfigureFileTransferItem note:\
This method should be called before accepting/starting\
 a file transfer, it informs the SDK which file transfer items\
 will be used.\
\
onFileTransferItemProgress note:\
Progress indication (in percent) of an ongoing file transfer item \
\
\
end\
}