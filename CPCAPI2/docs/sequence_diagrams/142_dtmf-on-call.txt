option footer=none

participant Client
participant Audio
participant "SipConversationManager" as SCM


Note left of Client
Near application init time
end note

loop Set DTMF deliver types
 Client->>SCM: setDtmfMode(account, ordinal)
end



Client<<-->>SCM: \n\n\n\nCall setup

Note left of Client
During a call
end note



Note left of SCM
Sends DTMF over wire. Set playLocally\n
to true if the local user should hear\n
the tone.
end note

Client->>SCM: startDtmfTone(conv, toneId, playLocally)
Note left of SCM
Not necessary to call stopDtmfTone;\n
tone length automatically controlled\n
by SDK.
end note
