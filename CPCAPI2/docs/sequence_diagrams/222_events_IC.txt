option footer=none

participant Client
participant "SipEventManager" as SM
participant "SDK process" as SDKP
participant "SipEventSubscriptionHandler" as SH

Client->>SM: getInterface()
Client->>SM: setHandler()

Note right of SDKP: Be prepared to handle errors:
SDKP->>SH: onError()


Note right of SDKP: onNewSubscription() is called when an \nincoming subscription request is received
SDKP->>SH: onNewSubscription\n(subscription handle, event)

Client->>SM:  accept() or reject()
SDKP->>SH: onSubscriptionStateChanged(..event)


Note right of Client: Optionally, to terminate this subscription:
Client->>SM: end()
SDKP->>SH: onSubscriptionEnded()
