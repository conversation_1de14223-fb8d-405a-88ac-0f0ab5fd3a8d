option footer=none
participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHandler" as SCH

note left of Client: If local user is Alice
Client->>Client: outgoing call is ringing
SDKP->>SCH: 	onIncomingRedirectRequest\n(handle X, event)
note right of Client: If the redirect is acceptable, do nothing
note right of Client: Or if it is not acceptable:
Client->>SCM: end(handle X) 

note left of Client: If local user is Bob
Client->>Client: incoming call \nhas come in \n(onNewConversation(handle X))
Client->>SCM: redirect(handle X) 
SDKP->>SCH: onConversationEnded(handle X)
Client->>SCM: end(handle X) 

note left of Client: If local user is Carlos
SDKP->>SCH: onIncomingTargetChangeRequest/n(handle X, event)
note right of Client: If the redirect is acceptable
Client->>SCM: accept(handle X) 
note right of Client: call is now handled in same way as a regular incoming call
note right of Client: Or if it is not acceptable:
Client->>SCM: reject(handle X) or redirect(handle X)
SDKP->>SCH: onConversationEnded(handle X)


