

option footer=none

participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHandler" as SCH


Client->>Client: callX is established with Alice
 The event includes a handle to conversation Y, which represents the transfer request
SDKP->>SCH: 	onIncomingTransferRequest(event)

note left of Client: To agree to \nbeing transferred
Client->>SCM: accept(handle Y)

Now, handle conversation Y as you would any incoming phone call that has been accepted; see the sequence diagram for incoming calls.


note left of Client: Else
Client->>SCM: reject (handle Y)
