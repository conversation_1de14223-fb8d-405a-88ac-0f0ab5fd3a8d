option footer=none

participant Client
participant Phone
participant "Media\nManager" as MM
participant "SipAccount\nManager" as SAM
participant "SipConversation\nManager" as SCM
participant SDKP
participant "PhoneError\nHandler" as PEH
participant "SipAccount\nHandler" as SAH
participant "SipConversation\nHandler" as SCH

Client->>Phone:  create()
Client->>Phone:  initialize()
Client->>Phone:  setErrorHandler()
Client->>MM:  getInterface()
Client->>MM: initializeMediaStack()
Client->>MM: process()
Client->>SAM:  getInterface()
Client->>SAM:  create(SipAccountSettings)
Client->>SAM:  setHandler()

Client->>SCM:  getInterface()
Client->>SCM:  setHandler()

Client->>SAM:  setDefaultSettings\n(SipConversationSettings)
Client->>SAM:  enable()
Client->>SAM:  process ()


 if a general Phone error
SDKP->>PEH:  onError()
 if a general Account error
SDKP->>SAH:  onError()
 if a general Conversation error
SDKP->>SCH:  onError()