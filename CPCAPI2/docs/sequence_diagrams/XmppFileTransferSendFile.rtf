{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Sending a file with Xmpp File Transfer\
\
Client->XmppFileTransferManager:getInterface(phone)\
Client->XmppFileTransferManager:setHandler(accountHandle, handler)\
note right of SDK Process,XmppFileTransferHandler:Be prepared to handle errors:\
SDK Process->XmppFileTransferHandler:onError()\
note right of Client,XmppFileTransferManager\
 Allocates a new file transfer handle within the \
SDK, for outbound transfer.\
end note\
Client->XmppFileTransferManager: createFileTransfer(accountHandle)\
\
Client->XmppFileTransferManager: \\n\\n\\n\\n\\nconfigureFileTransferItems(fileTransferHandle,fileItems)\
\
\
note right of Client,XmppFileTransferManager \
\\n\\nInitiates an outgoing (client) session \
to the remote participant.\
end note\
Client->XmppFileTransferManager: start(fileTransferHandle)\
\
note right of SDK Process,XmppFileTransferHandler\
Callback invoked by the SDK when a new file transfer \
is created, either by sending or receiving.\
end note\
 SDK Process->XmppFileTransferHandler:onNewFileTransfer(fileTransferHandle,NewFileTransferEvent)\
\
\
note right of Client,XmppFileTransferManager\
 Optional: Ends an (already connected) session. \
Does nothing if the file transfer is not connected.\
end note\
Client->XmppFileTransferManager: end(fileTransferHandle) \
\
\
createFileTransfer note:\
Creates a new (empty) file transfer item handle, \
used for outbound transfers. The handle will be \
unique across the SDK. After items are created, \
they should be "configured" using the \
configureFileTransferItems method, before \
start() is called.\
\
ocnfigureFileTransferItem note:\
This method should be called before accepting/starting\
 a file transfer, it informs the SDK which file transfer items\
 will be used.\
\
onFileTransferItemProgress note:\
Progress indication (in percent) of an ongoing file transfer item }