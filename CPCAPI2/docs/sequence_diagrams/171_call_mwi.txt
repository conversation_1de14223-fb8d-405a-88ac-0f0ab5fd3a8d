option footer=none

participant Client
participant "SipMessageWaitingIndicationManager" as MWIM
participant "SDK process" as SDKP
participant "SipMessageWaitingIndicationHandler" as MWIH

note left of Client: Subscribe to the voicemail server

Client->>MWIM: getInterface()
Client->>MWIM: setHandler()
 createSubscription() returns a subscription handle
Client->>MWIM: createSubscription()
Client->>MWIM: applyMWISubscriptionSettings\n(subscription handle, SipMWISubscriptionSettings)

Client->>MWIM: start(subscription handle)

SDKP->>MWIH: onNewSubscription(subscription handle, event)
SDKP->>MWIH: onSubscriptionStateChanged(pending)
SDKP->>MWIH: onSubscriptionStateChanged(active)

note left of Client: Receive a MWI
SDKP->>MWIH: onIncomingMWIStatus(event)
Client->>Client: Read the event for information \non the type and count of \nmessages on the voicemail server

note right of Client: Be prepared to handle any error
SDKP->>MWIH: onError()

note right of Client: End the subscription (optional)
Client->>MWIM: end()
SDKP->>MWIH: onSubscriptionEnded()