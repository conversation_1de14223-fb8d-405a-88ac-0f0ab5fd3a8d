option footer=none

participant Client
participant "AudioManager" as AM
participant "SDK process" as SDKP
participant "AudioHandler" as AH

Client->>AM: queryDeviceVolume()
SDKP->>AH: onAudioDeviceVolume()

Client->>AM: setMicMute()
SDKP->>AH: onAudioDeviceVolume()

Client->>AM: setSpeakerMute()
SDKP->>AH: onAudioDeviceVolume()

Client->>AM: setMicVolume or setSpeakerVolume()
SDKP->>AH: onAudioDeviceVolume()


Client->>AM: setEchoCancellationMode()
Client->>AM: setNoiseSuppressionMode()

