option footer=none
participant Client
participant "SipConversation\nManager" as SCM
participant "SDK process" as SDKP
participant "SipConversation\nHandler" as SCH

 returns SipConversationHandle
Client->>SCM: createConversation()
Client->>SCM: addParticipant(SipConversationHandle)
 specify the audio and video you want
Client->>SCM: configureMedia(SipConversationHandle, MediaInfo)
Client->>SCM: setMediaEnabled(SipConversationHandle)
Client->>SCM: setAnonymousMode(SipConversationHandle)
Client->>SCM: start(SipConversationHandle)
SDKP->>SCH: onNewConversation\n(SipConversationHandle)
 called for each state change as the call is negotiated
SDKP->>SCH: onConversationStateChanged()
  called as the audio and video is negotiated
SDKP->>SCH: onConversationMediaChanged()
  the event contains a struct that will eventually identify that the conversation is connected (established)
SDKP->>SCH: onConversationStateChanged\n(ConversationStateChangedEvent)
Note right of Client: If the call includes video, handle incoming \nand local video. See the section on displaying \nvideo. Note that there is no need to \nexplicitly handle audio.
