option footer=none

participant Client
participant "SipPresenceManager" as PM
participant "SDK process" as SDKP
participant "SipPresenceHand<PERSON>" as PH

Client->>PM: getInterface()
Client->>PM: set<PERSON><PERSON><PERSON>()

Note right of SDKP: Be prepared to handle errors:
SDKP->>PH: onError()


Note right of Client: Perform the following steps \nfor each target:

Client->>PM: createSubscription()

Client->>PM: applySubscriptionSettings\n(subscription handle, settings)

Client->>PM:  addParticipant\n(subscription handle, target address)

Client->>PM: start(subscription handle)

SDKP->>PH: onNewSubscription\n(subscription handle, event)
Note right of SDKP: When the subscription request is \naccepted or rejected by the other target:
SDKP->>PH: onSubscriptionStateChanged\n(subscription handle, event)

 Also be prepared to handle onIncomingPresenceStatus() asynchronously; it is called each time the other target's presence changes 


SDKP->>PH: onIncomingPresenceStatus\n(subscription handle, event)


Note right of Client: Optionally, to terminate subscribing:
Client->>PM: end(subscription handle)
SDKP->>PH: onSubscriptionEnded\n(subscription handle, event)





