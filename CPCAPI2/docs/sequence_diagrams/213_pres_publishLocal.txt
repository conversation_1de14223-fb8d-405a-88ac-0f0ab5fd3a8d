option footer=none

participant Client
participant "SipPresenceManager" as PM
participant "SDK process" as SDKP
participant "SipPresencePublicationHandler" as PH


Note left of Client: If working peer-to-peer:
Note right of Client: Call notify() separately on each \nperson subscribing to the local user:
Client->>PM:  notify(publication handle, presence)

SDKP->>PH: onNotifySuccess() or \nonNotifyFailure()



Note left of Client: If working with a presence agent:
Client->>PM: getInterface()
Client->>PM: setPublicationHandler()

Note right of SDKP: Be prepared to handle errors:
SDKP->>PH: onError()

Note right of Client: To start publishing:
 This method returns a SipEventPublicationHandle (a publication handle)
Client->>PM: createPublication(..settings)
Client->>PM: publish (publication handle, presence)

SDKP->>PH: onPublicationSuccess() or \nonPublicationFailure()

Note right of Client: To stop publishing:
 calling end() tells the PA that you will no longer be publishing the local user's presence
Client->>PM: end()
SDKP->>PH: onPublicationRemoved()



