option footer=none

participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHandler" as SCH


Client->>Client: callX is established with Bob
Client->>SCM: hold() 
Client->>SCM: transfer(handle X, <PERSON> address) 

Note right of SDKP: Optional:
SDKP->>SCH: 	onTransferProgress(handle X)

Note right of Client: When you are no longer interested in X, \noptionally call:
Client->>SCM: end() 
 
