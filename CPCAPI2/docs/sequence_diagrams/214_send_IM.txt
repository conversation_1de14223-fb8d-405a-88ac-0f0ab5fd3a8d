option footer=none

participant Client
participant "SipPresenceManager" as SM
participant "SDK process" as SDKP
participant "SipPresencePublicationHandler" as SH

Client->>SM: getInterface()
Client->>SM: setHandler()
Note right of Client: Call the following methods to specify which \nmime types the local client can handle:
Client->>SM: acceptMimeType()
Client->>SM: rejectIncomingMimeType()

Note right of SDKP: Be prepared to handle errors:
SDKP->>SH: onError()

Note left of Client: Send an IM:
 This method returns a SipInstantMessageHandle (an IM handle)
Client->>SM: sendMessage(..target, content)
SDKP->>SH: onOutgoingInstantMessageSuccess() \nor onOutgoingInstantMessageFailure() 

Note left of Client: Receive an incoming IM:
SDKP->>SH: onIncomingInstantMessage(event)
Client->>SM: acceptIncoming() or rejectIncoming()

