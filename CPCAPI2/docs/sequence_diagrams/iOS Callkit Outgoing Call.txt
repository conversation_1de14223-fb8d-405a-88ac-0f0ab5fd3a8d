title Outgoing call

participant CPCAudio
participant SipConversationManager
participant Client
participant CXProvider \n (CallKit API)
participant CXStartCallAction \n (CallKit API)

note right of Client: Call button tapped (CallButtonDidTap)

note right of Client: Client reports outgoing call with CallKit API

Client->CXProvider \n (CallKit API): reportOutgoingCallWithUUID:startedConnectingAtDate:dateStartedConnecting

CXProvider \n (CallKit API)-->Client:provider:performStartCallAction

Client->SipConversationManager: setCallKitMode:YES

note left of Client: Tell SDK to start the call

Client->SipConversationManager: addParticipant:targetAddress:

Client->SipConversationManager: start:

CXProvider \n (CallKit API)->CXStartCallAction \n (CallKit API): fulfill 

note right of Client: Client responds to CallKit callback, and calls setAudioSessionActivated

CXProvider \n (CallKit API)-->Client: provider:didActivateAudioSession:

Client->CPCAudio: setAudioSessionActivated:YES

SipConversationManager->Client: onNewConversation:

note right of SipConversationManager: Once the call is connected 

SipConversationManager->Client: onConversationStateChanged

Client->CXProvider \n (CallKit API): reportOutgoingCallWithUUID:connectedAtDate:dateConnected