participant CPCAudio
participant Si<PERSON><PERSON>onversation\nManager as SipConversationManager
participant Client
participant CXProvider\n(CallKit API) as CXProvider
participant CXAnswerCallAction\n(CallKit API) as CXAnswerCallAction


SipConversationManager->+Client: onNewConversation:
note right of Client: Client reports incoming call with CallKit API
Client->CXProvider: reportNewIncomingCallWithUUID:update:completion:

Client->SipConversationManager: sendRingingResponse:
Client->-SipConversationManager: setCallKitMode:YES
CXProvider-->+Client: provider:didActivateAudioSession:
note right of Client: Client responds to CallKit callback,\n and calls setAudioSessionActivated
Client->-CPCAudio: setAudioSessionActivated:YES
note left of CXProvider: Once the user taps the accept button,\nCallKit fires the below event,\nand the client answers the call
CXProvider-->+Client: provider:performAnswerCallAction: 
Client->SipConversationManager: accept:
Client->-CXAnswerCallAction: fulfill
