option footer=none

participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHandler" as SCH


Client->>Client: callX is established with Alice
 The event includes a handle to conversation Z, which represents the transfer request
SDKP->>SCH: 	onIncomingTransferRequest(event)

note left of Client: To agree to \nbeing transferred
Client->>SCM: accept(handle Z)

note left of Client: Else
Client->>SCM: reject (handle Z)
