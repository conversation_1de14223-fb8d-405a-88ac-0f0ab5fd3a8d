participant Client
participant SipAccount\n(SDK) as SipAccount
participant SIP Server as SS

note right of Client: Client populates SipAccountSettings struct;\nas usual, these values may be obtained\nlocally by the client, or via Stretto Provisioning
Client->SipAccount: create(accountSettings)
Client->SipAccount: enable()
note right of SipAccount: SDK reads SipAccountSettings::userCertificatePEM and\nSipAccountSettings::userPrivateKeyPEM. These can be\neither a path to a PEM file, or the contents of a\nPEM file.\n\nuserCertificatePEM is the client certificate chain \nused in mutual authentication.\nuserPrivateKeyPEM is the client private key used\nin mutual authentication.\n\nIf a certificate is specified but cannot be loaded,\naccount enablement will fail.
SipAccount->SipAccount: load client certificate/key

SipAccount->+SS: \n\nOpens TCP connection for TLS handshake
SipAccount-->SS: ClientHello
SS-->SipAccount: ServerHello
note right of SS: Server sends its \ncertificate per RFC 5246.
SS-->SipAccount: Certificate
SS-->SipAccount: ServerKeyExchange

opt Server requests mutual auth
note right of SS: Server requests client's certificate chain.
SS-->SipAccount: CertificateRequest
SipAccount->SipAccount: Prepare client \ncertificate for sending
note right of SipAccount: If a valid client certificate (userCertificatePEM)\nwas specified earlier, the SDK will prepare\nthese for responding to the client certificate\nrequest; otherwise the SDK will respond with\nan empty certificate list.
SipAccount-->SS: Certificate
note right of SS: SIP Server as a standard TLS server examines the \nclient's certificate chain (even if empty), and at its\ndiscretion either proceeds with the handshake, or\naborts by sending a fatal handshake alert.
SipAccount-->SS: ClientKeyExchange
note right of SipAccount: Client sends signature over previous handshake \nmessage using client's private key\n(userPrivateKeyPEM).
SipAccount-->SS: CertificateVerify
end
note right of SS: Note: Specifics of non mutual \nauth flow omitted for brevity.
SipAccount-->SS: ChangeCipherSpec
SipAccount-->SS: [Finished] (Sent encrypted)
SS-->SipAccount: ChangeCipherSpec
SS-->SipAccount: [Finished] (Sent encrypted)

note right of SS: TLS handshake is complete:\nRegistration and calls proceed\nas per Standard.
SipAccount->SS: [SIP REGISTER] (Sent encrypted)
SS->SipAccount: [200 OK] (Sent encrypted)