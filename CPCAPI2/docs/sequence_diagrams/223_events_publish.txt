option footer=none

participant Client
participant "SipEventPublicationManager" as EM
participant "SDK process" as SDKP
participant "SipEventPublicationHandler" as EH

Client->>EM: getInterface()
Client->>EM: setHandler()

Note right of SDKP: Be prepared to handle errors:
SDKP->>EH: onError()

 This method returns a SipEventPublicationHandle (a publication handle)
Client->>EM: createPublication(..settings)


 Call setTarget() only if you are publishing via a publication agent:
Client->>EM: \nsetTarget(..target address)
 The state parameter contains the information you want to publish
Client->>EM: \npublish (publication handle, state)

SDKP->>EH: \n\nonPublicationSuccess() or onPublicationFailure()

Note right of Client: To stop publishing:
 calling end() tells the publication agent that you will no longer be publishing information about this event package
Client->>EM: end()
SDKP->>EH: onPublicationRemove()
