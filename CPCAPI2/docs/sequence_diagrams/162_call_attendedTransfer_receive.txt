option footer=none

participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHand<PERSON>" as SCH

 The event includes a handle to conversation Z, which represents the tranfer request.
SDKP->>SCH: onIncomingTransferRequest(event)
Client->>SCM: acceptIncomingTransferRequest(handle Z) or \nrejectIncomingTransferRequest(handle Z)

Note right of SDKP: Continue processing Z as for any incoming \nphone call.


