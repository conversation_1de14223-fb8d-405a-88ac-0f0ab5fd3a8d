# title End push call

participant Client
participant "CPCiOS\nBackground\nManager" as CPCiOSBackgroundManager
participant CPCAudio
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSip\nConversation\nManager" as CPCSipConversationManager
participant "CXProvider\n(CallKit API)" as CXProvider
participant <PERSON><PERSON><PERSON>

note over Client: The tunnel account is handling \na push-initiated ongoing call.

Client ->> Client: User stops call.

Client->>CPCSipConversationManager: end:conversationHandle

CPCSipConversationManager->>Client: onConversationEnded:

note right of Client: Client calls activatePlayAndRecordMode:NO to match call to\nactivatePlayAndRecordMode:YES at start of push call. 
Client->>CPCAudio: activatePlayAndRecordMode:NO

CXProvider-->Client: provider:didDeactivateAudioSession:
note right of Client: Once the call has disconnected, CallKit will invoke\nprovider:didDeactivateAudioSession. setAudioSessionActivated\nshould be invoked when this event is received by the app
Client->>CPCAudio: setAudioSessionActivated:NO

note right of Client: Client cleans up the tunnel account.

Client->>CPCSipAccountManager: disableAccount(strettoTunnelAccountHandle)
Client->>CPCSipAccountManager: destroy(strettoTunnelAccountHandle)

opt If Client is in foreground, and Single Device Takeover is in use
    note right of Client: Client now enables the local SIP account, and sends a\nnetworkDeregistered to the push server.
    Client->>CPCSipAccountManager: enableAccount(localSipAccountHandle)
    Client->>Stretto: HTTP request: networkDeregistered(accountUUID)
end

opt If Client is in background, and Single Device Takeover is in use
    note right of Client: Client should enable the local SIP account here because it was \nexplicitly disabled by Client during setup of the tunnel account. \nCPCiOSBackgroundManager suppresses any registrations \nuntil the application is in foreground.
    Client->>CPCSipAccountManager: enableAccount(localSipAccountHandle)
end