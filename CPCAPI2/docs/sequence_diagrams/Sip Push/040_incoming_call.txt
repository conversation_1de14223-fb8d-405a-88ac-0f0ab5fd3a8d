# title Incoming call comes in when app in background

participant Client
participant "CPCiOS\nBackground\nManager" as CPCiOSBackgroundManager
participant CPCAudio
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSip\nConversation\nManager" as CPCSipConversationManager
participant CXProvider\n(CallKit API) as CXProvider
participant Stretto
participant SIP server
participant APNs


note right of Client: The local SIP account is inactive, but\n Stretto is active and monitoring calls.

# When an incoming call event occurs
SIP server->>Stretto: Incoming call\ni.e. SIP INVITE
Stretto ->> APNs: Stret<PERSON> informs APNs.
note right of Client: Payload from APNs contains \ntunnel information from Stretto.
APNs->>Client: [didReceiveIncomingPushWithPayload:payload forType:PKPushTypeVoIP]

# opt Single Device Takeover mode
note right of Client: For Single Device Takeover mode only, the\nClient disables the local SIP account until\nthe push-initiated call is finished.
    Client->>CPCSipAccountManager: [disableAccount:localSipAccountHandle] - only for Single Device Takeover
# end

note right of Client: The Client sets up another SIP account and\n uses the tunnel as the SIP transport.
Client->>CPCSipAccountManager: [createAccount:strettoTunnelAccountSettings]
Client->>CPCSipAccountManager: [addHandler:strettoTunnelAccountHandle]

# Set appropriate NAT settings
Client->>CPCSipConversationManager: [setDefaultSettings:conversationSettings forAccount:strettoTunnelAccountHandle]

# Set up the Apple Callkit UI
Client->>CPCSipConversationManager: [addHandler:callViewController forAccount:strettoTunnelAccountHandle]

Client->>CPCSipAccountManager: [enableAccount:strettoTunnelAccountHandle]

# Report the new call to CallKit
note right of Client: The Client reports incoming call\n with CallKit API
Client->>CXProvider: [reportNewIncomingCallWithUUID:update:completion:]

CXProvider-->>Client: reportNewIncomingCallWithUUID completion handler
note right of Client: Client invokes activatePlayAndRecordMode:YES\n in completion handler
Client->>CPCAudio: [activatePlayAndRecordMode:YES]


note right of Client: The user can accept or reject the incoming call.

CXProvider-->>Client: [provider:didActivateAudioSession:YES]
note right of Client: If the call was accepted, CallKit will invoke\nprovider:didActivateAudioSession.\nsetAudioSessionActivated should be invoked \nwhen this event is received by the app
Client->>CPCAudio: [setAudioSessionActivated:YES]