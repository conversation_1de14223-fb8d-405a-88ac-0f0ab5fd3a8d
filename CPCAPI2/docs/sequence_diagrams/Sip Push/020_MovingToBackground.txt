title Moving to background (Single Device Takeover Mode)

participant Client
participant CPCiOSBackgroundManager
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant Stretto
participant SIP server
participant APNs

Client->>Client: Enter background
Client->>Stretto: HTTP request: Subscribe(accountUUID)
Stretto->>SIP server: SIP REGISTER
Stretto->>Client: 200 OK with response data

note over CPCiOSBackgroundManager: The background manager detects entry into background\n and disables the local SIP account on behalf of the client.
CPCiOSBackgroundManager->>CPCSipAccountManager: [disableAccount: localSipAccountHandle]
note over CPCSipAccountManager: No SIP UNREGISTER sent to SIP server due to \ndiscardRegistrationOnRestriction set to true.
note right of Client: The local SIP account is inactive, but\n Stretto is active and monitoring calls.
