title Initial app start setup

participant Client
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSipConversation\nManager" as CPCSipConversationManager
participant Stretto
participant APN

Client->Client: App starts in foreground

# On client startup.
Client->>APN: Request for device token
APN-->Client: didUpdatePushCredentials: credentials forType: PKPushTypeVoIP

# The "deleteAccountsWithToken" REST API request should be called when the app is installed and run the first time.
note right of Client: The token is an ID sent by the APN to identify the iOS device.
Client->>Stretto: HTTP request: deleteAccountsWithToken(token)
Stretto-->Client: 200 OK with response data

# Local sip account is enabled in the foreground.
note right of Client: Set discardRegistrationOnRestriction to true\n in the Sip account settings object.
note right of Client: Set usePushNotifications to true in the Sip account settings object.
note right of Client: Now, SDK stops sending REGISTER messages pertinent to the local SIP account \nwhen the client is subscribed to Stretto.
Client->>CPCSipAccountManager: enableAccount(localSipAccountHandle)

# The "updateAccount" REST API request should be called (should be called) when the SIP account is configured possibly while app is in foreground. When the SIP account configuration change, it is called again.
note right of Client: The accountUUID is a GUID that should be generated and persisted.
Client->>Stretto: HTTP request: updateAccount(accountUUID)
Stretto-->Client: 200 OK with response data
