title Make an outgoing call from foreground after receiving an incoming push call

participant Client
participant CPCiOSBackgroundManager
participant CPCSipAccountManager
participant CPCSipConversationManager
participant <PERSON><PERSON><PERSON>

# Assuming the Client's account is already registered with the APN and has sent a subscribe http request.

Client->Client: Enter foreground
note right of CPCiOSBackgroundManager: The background manager detects entry\n into foreground and automatically enables\n the local sip account on behalf of the client.
CPCiOSBackgroundManager-->CPCSipAccountManager: [enableAccount:localSipAccount]
Client->>Stretto: HTTP request: networkDeregister(accountUUID)
note left of Stretto: Stret<PERSON> now stops sending SIP REGISTER messages\n including UNREGISTER.
Stretto-->Client: 200 OK

# The Client is now registered to the sip server with the local sip account.
Client->CPCSipConversationManager: [createConversation: localSipAccount]
Client->CPCSipConversationManager: [configureMedia: newConversationHandle media: newConversationMediaInfo]
Client->CPCSipConversationManager: [start: newConversationH<PERSON>le]

note right of Client: Outgoing call is in progress.
