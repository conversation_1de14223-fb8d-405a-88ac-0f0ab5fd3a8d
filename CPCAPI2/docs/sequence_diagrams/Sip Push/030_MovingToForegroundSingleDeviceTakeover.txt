title Entering foreground (Single Device Takeover Mode)

participant Client
participant CPCiOSBackgroundManager
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant Stretto
participant SIP server
participant APNs

# Assuming the Client's account is already registered with the APNs and has sent a subscribe http request.

Client->>Client: Enter foreground
note over CPCiOSBackgroundManager: The background manager detects entry\n into foreground and automatically enables\n the local SIP account on behalf of the client.
CPCiOSBackgroundManager->>CPCSipAccountManager: [enableAccount:localSipAccount]
CPCSipAccountManager ->> SIP server: SIP REGISTER
Client->>Stretto: HTTP request: networkDeregistered(accountUUID)
note over St<PERSON>to: <PERSON><PERSON><PERSON> now stops sending SIP REGISTER \nmessages to the SIP server.
note over Stretto: <PERSON><PERSON><PERSON> does NOT send UNREGISTER to the SIP server \nwhen networkDeregistered is called.
Stretto->>Client: 200 OK
note right of Client: The local SIP account is active\n and <PERSON><PERSON><PERSON> is inactive.
