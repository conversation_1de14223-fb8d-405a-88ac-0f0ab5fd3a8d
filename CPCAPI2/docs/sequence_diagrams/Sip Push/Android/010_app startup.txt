title Initial app startup (Single Device Takeover Mode)

participant Client
participant SipAccount
participant Stretto
participant SIP server
participant FCM

Client->>Client: App starts in foreground

# On client startup.
Client->>FCM: Request for a device registration token
FCM->>Client: FcmReceiverService.onNewToken

# The "deleteAccountsWithToken" REST API request should be called only when a new token is received.
note right of Client: The token is an ID sent by FCM \nto identify the Android device.
Client->>Stretto: HTTP request: deleteAccountsWithToken(token)
Stretto->>Client: 200 OK with response data
Client->>SipAccount: localAccount = SipAccountApi.newAccount(settings)
# Local sip account is enabled in the foreground.
Client->>SipAccount: localAccount.enable()
SipAccount->>SIP server: SIP REGISTER

# The "updateAccount" REST API request should be called when the SIP account is configured possibly while app is in foreground. When the SIP account configuration changes, it is called again.
note right of Client: The accountUUID is a GUID that \nshould be generated and persisted.
Client->>Stretto: HTTP request: updateAccount(accountUUID)
Stretto->>Client: 200 OK with response data
