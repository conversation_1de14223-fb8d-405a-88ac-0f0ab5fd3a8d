title Moving to background (Single Device Takeover Mode)

participant Client
participant SipAccount
participant Android OS
participant Stretto
participant SIP server
participant FCM

Client->>Client: Enter background
note right of Client: When the application enters background,\nthe OS calls Activity.onPause()

Android OS->>Client: Activity.onPause()
note over Client: The Client subscribes to push and \ndisables the local SIP account.

Client->>Stretto: HTTP request: Subscribe(accountUUID)
Stretto->>SIP server: SIP REGISTER
Stretto->>Client: 200 OK with response data

Client->>SipAccount: localAccount.disable(suppressUnregister=true)
note over SipAccount: No SIP UNREGISTER sent to SIP server due to \nsuppressUnregister set to true.
note right of Client: The local SIP account is inactive, but\n Stretto is active and monitoring calls.


