title Incoming call comes in when app in background

participant Client
participant SipAccount
participant SipConversation
participant Stretto
participant SIP server
participant FCM


note right of Client: The local SIP account is inactive, but\n Stretto is active and monitoring calls.

# When an incoming call event occurs
SIP server->>Stretto: Incoming call i.e. SIP INVITE
Stretto->> FCM: <PERSON><PERSON><PERSON> informs FCM.
FCM->>Client: FirebaseMessagingService.onMessageReceived

note right of Client: Payload from FCM contains \ntunnel information from Stretto.
note right of Client: The Client sets up another SIP account and\n uses the tunnel as the SIP transport.
Client->>SipAccount: tunnelAccount = SipAccountApi.newAccount(tunnelAccountSettings)

# Set appropriate NAT settings
Client->>SipConversation: SipConversationApi.setDefaultSettings(tunnelConversationSettings)

Client->>SipAccount: tunnelAccount.enable()

note right of Client: The user can accept or reject the incoming call.