title Make an outgoing call (Single Device Takeover Mode)

participant Client
participant SipAccount
participant Sip<PERSON>onversation
participant Android OS
participant <PERSON><PERSON><PERSON>

# Assuming the Client's account is already registered with FCM and has sent a subscribe http request.

Client->>Client: Enter foreground
Android OS->>Client: Activity.onResume()
note right of Client: The Client enables the local SIP account.
Client->>SipAccount: localAccount.enable()
SipAccount->>SIP server: SIP REGISTER
Client->>Stretto: HTTP request: networkDeregistered(accountUUID)
note over St<PERSON><PERSON>: <PERSON><PERSON><PERSON> now stops sending SIP REGISTER \nmessages to the SIP server.
note over Stretto: <PERSON><PERSON><PERSON> does NOT send UNREGISTER to the SIP server \nwhen networkDeregistered is called.
Stretto->>Client: 200 OK
note right of Client: The local SIP account is active\n and <PERSON><PERSON><PERSON> is inactive.

# The Client is now registered to the sip server with the local sip account.
Client->>SipConversation: SipConversation.start()

note right of Client: Outgoing call is in progress.