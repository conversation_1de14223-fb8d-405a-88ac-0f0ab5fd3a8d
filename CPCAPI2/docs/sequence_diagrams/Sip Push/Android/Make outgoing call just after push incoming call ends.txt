title Make an outgoing call from foreground after receiving an incoming push call

participant Client
participant SipAccountManager
participant SipConversationManager
participant <PERSON><PERSON><PERSON>

# Assuming the Client's account is already registered with FCM and has sent a subscribe http request.

Client->Client: Enter foreground
note right of Client: The applications Activity onResume()\nmethod enables the local SIP account.
Client-->SipAccountManager: [enableAccount:localSipAccount]
Client->>Stretto: HTTP request: networkDeregister(accountUUID)
note left of Stretto: Stretto now stops sending SIP REGISTER messages\n including UNREGISTER.
Stretto-->Client: 200 OK

# The Client is now registered to the sip server with the local sip account.
Client->SipConversationManager: [createConversation: localSipAccount]
Client->SipConversationManager: [configureMedia: newConversation media: newConversationMediaInfo]
Client->SipConversationManager: [start: newConversation]

note right of Client: Outgoing call is in progress.



