# title End push call

participant Client
participant SipAccount
participant SipConversation
participant Stretto
participant SIP server
participant FCM

note over Client: The tunnel account is handling \na push-initiated ongoing call.

Client->>Client: User stops call.

Client->>SipConversation: SipConversation.end()

note right of Client: The Client cleans up the tunnel account.

Client->>SipAccount: tunnelAccount.disable()
Client->>SipAccount: tunnelAccount.destroy()