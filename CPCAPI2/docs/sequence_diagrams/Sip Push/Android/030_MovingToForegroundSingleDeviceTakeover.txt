title Entering foreground (Single Device Takeover Mode)

participant Client
participant SipAccount
participant Android OS
participant Stretto
participant SIP server
participant <PERSON>M

# Assuming the Client's account is already registered with FCM and has sent a subscribe http request.

Client->>Client: Enter foreground
note right of Client: When the application enters foreground,\nthe OS calls Activity.onResume()
Android OS->>Client: Activity.onResume()
note over Client: The Client enables \nthe local SIP account.
Client->>SipAccount: localAccount.enable()
SipAccount->>SIP server: SIP REGISTER
Client->>Stretto: HTTP request: networkDeregistered(accountUUID)
note over Stretto: <PERSON><PERSON><PERSON> now stops sending SIP REGISTER \nmessages to the SIP server.
note over Stretto: <PERSON><PERSON><PERSON> does NOT send UNREGISTER to the SIP server \nwhen networkDeregistered is called.
Stretto->>Client: 200 OK
note right of Client: The local SIP account is active\n and <PERSON><PERSON><PERSON> is inactive.