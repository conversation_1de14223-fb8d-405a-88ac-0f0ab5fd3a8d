title Initial app startup (Single Device Takeover Mode)

participant Client
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant Stretto
participant SIP server
participant APNs

Client->>Client: App starts in \nforeground

# On client startup.
Client->>APNs: Request for PushKit device token
APNs->>Client: [didUpdatePushCredentials: credentials forType: PKPushTypeVoIP]
Client->>APNs: Request for APNs device token for Remote Notification
APNs->>Client: [didRegisterForRemoteNotificationsWithDeviceToken]

# The "deleteAccountsWithToken" REST API request should be called when the app is installed and run the first time.
note right of Client: These tokens are an ID sent by the APNs \nto identify the iOS device.
Client->>Stretto: HTTP request: deleteAccountsWithToken(token)
Stretto->>Client: 200 OK with response data

# Local sip account is enabled in the foreground.
note right of Client: Set discardRegistrationOnRestriction to true \nin CPCSipAccountSettings.
note right of Client: Set usePushNotifications to true \nin CPCSipAccountSettings.
# note right of Client: Now, SDK stops sending REGISTER messages pertinent to the local SIP account \nwhen the client is subscribed to Stretto.
Client->>CPCSipAccountManager: [enableAccount:localSipAccountHandle]
CPCSipAccountManager->>SIP server: SIP REGISTER

# The "updateAccount" REST API request should be called when the SIP account is configured possibly while app is in foreground. When the SIP account configuration changes, it is called again.
note right of Client: The accountUUID is a GUID that \nshould be generated and persisted.
Client->>Stretto: HTTP request: updateAccount(accountUUID)
Stretto->>Client: 200 OK with response data