title End push initiated call with app in foreground

participant Client
participant CPCiOSBackgroundManager
participant CPCSipConversationManager
participant CPCSipAccountManager
participant <PERSON><PERSON><PERSON>

# User has now accepted the call in the background and now the app is returning to the foreground.

Client->Client: Enter foreground

note right of CPCiOSBackgroundManager: The background manager detects entry into foreground and automatically enables the local sip account on behalf of the client.
CPCiOSBackgroundManager->>CPCSipAccountManager: [enableAccount:localSipAccount]

Client->>Stretto: HTTP request: networkDeregister(accountUUID)
note left of Stretto: <PERSON>ret<PERSON> stops sending SIP REGISTER messages including UNREGISTER.

Stretto-->Client: 200 OK

note right of Client: The Client is now locally registered to the Sip server.
note right of Client: User stops call via UI or call gets terminated remotely.

Client->>CPCSipConversationManager: end(conversationHandle)

note right of Client: Call is now over.

Client->>CPCSipAccountManager: disableAccount(strettoSipAccountHandle)
Client->>CPCSipAccountManager: destroy(strettoSipAccountHandle)
