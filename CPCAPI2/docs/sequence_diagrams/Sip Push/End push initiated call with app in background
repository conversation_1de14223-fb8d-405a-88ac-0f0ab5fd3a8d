title End push initiated call with app in background

participant Client
participant CPCiOSBackgroundManager
participant CPCSipAccountManager
participant CPCSipConversationManager
participant <PERSON><PERSON><PERSON>

note right of Client: User has now accepted the call in the background and chooses to remain in the background
note right of Client: User stops call via Callkit or call gets terminated remotely.
Client->>CPCSipConversationManager: end(conversationHandle)

note right of Client: Call is now over.

Client->>CPCSipAccountManager: disableAccount(strettoSipAccountHandle)
Client->>CPCSipAccountManager: destroy(strettoSipAccountHandle)
