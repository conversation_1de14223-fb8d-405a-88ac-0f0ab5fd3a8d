title Make an outgoing call (Single Device Takeover Mode)

participant Client
participant "CPCiOSBackground\nManager" as CPCiOSBackgroundManager
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSipConversation\nManager" as CPCSipConversationManager
participant CXProvider\n(CallKit API) as CXProvider
participant CXCallController\n(CallKit API) as CXCallController
participant <PERSON><PERSON><PERSON>

# Assuming the Client's account is already registered with the APNs and has sent a subscribe http request.

Client->>Client: Enter\n foreground
note over CPCiOSBackgroundManager: The background manager detects entry\n into foreground and automatically enables\n the local SIP account on behalf of the client.
CPCiOSBackgroundManager->>CPCSipAccountManager: [enableAccount:localSipAccount]
CPCSipAccountManager ->> SIP server: SIP REGISTER
Client->>Stretto: HTTP request: networkDeregistered(accountUUID)
note over Stretto: <PERSON>ret<PERSON> now stops sending SIP REGISTER \nmessages to the SIP server.
note over Stretto: <PERSON>ret<PERSON> does NOT send UNREGISTER\n to the SIP server \nwhen networkDeregistered is called.
Stretto->>Client: 200 OK
note right of Client: The local SIP account is active\n and Stretto is inactive.


Client->>Client: User initiates call

note right of Client: Client reports outgoing call with CallKit API,\ncreating CXStartCallAction instance and\npassing to CXCallController.

Client->>CXCallController: [requestTransaction:completion]

CXProvider-->+Client:[provider:performStartCallAction]

# The Client is now registered to the sip server with the local sip account.
Client->>CPCSipConversationManager: [createConversation: localSipAccount]
Client->>CPCSipConversationManager: [setCallKitMode: newConversationHandle]
Client->>CPCSipConversationManager: [configureMedia: newConversationHandle media: newConversationMediaInfo]
Client->>CPCSipConversationManager: [start: newConversationHandle]

note right of Client: Outgoing call is in progress; app monitors\nSDK onConversationStateChanged: callback\nfor changes.
