title SIP account configuraiton for incoming calls via PUSH

participant Client
participant CPCiOSBackgroundManager
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSipConversation\nManager" as CPCSipConversationManager
participant Stretto
participant Sip server
participant APN

Client->Client: Enter background mode
Client->>Stretto: HTTP request: Subscribe(accountUUID)
Stretto->>Sip server: SIP REGISTER
Stretto-->Client: 200 OK with response data

note right of CPCiOSBackgroundManager: The background manager detects entry into background\n disables the local sip account on behalf of the client.
note right of CPCiOSBackgroundManager: Setting discardRegistrationOnRestriction to true causes\n the SDK to not send a SIP UNREGISTER.
CPCiOSBackgroundManager-->CPCSipAccountManager: [disableAccount: localSipAccountHandle]
note right of Client: The local sip account is inactive but,\n Stretto is active and monitoring calls.

# When an incoming call event occurs
Sip server->Stretto: Incoming call i.e. SIP INVITE
note right of Stretto: Incoming call event causes <PERSON>ret<PERSON>\n to inform the APN.
APN-->Client: didReceiveIncomingPushWithPayload:payload forType:PKPushTypeVoIP

note right of Client: Payload from APN contains\n tunnel information from Stretto.
note right of Client: The Client sets up another SIP account and\n uses the tunnel as the SIP transport.
Client->CPCSipAccountManager: [createAccount:strettoTunnelAccountSettings]
Client->CPCSipAccountManager: [addHandler:strettoTunnelAccountHandle]

# Set appropriate NAT settings
Client->CPCSipConversationManager: [setDefaultSettings:conversationSettings forAccount:strettoTunnelAccountHandle]

# Set up the Apple Callkit UI
Client->CPCSipConversationManager: [addHandler:callViewController forAccount:strettoTunnelAccountHandle]

Client->CPCSipAccountManager: [enableAccount:strettoTunnelAccountHandle]

note right of Client: The user can accept or reject the incoming call.
