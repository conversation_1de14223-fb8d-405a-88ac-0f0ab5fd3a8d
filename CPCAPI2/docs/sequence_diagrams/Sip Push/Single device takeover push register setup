title Single device takeover push register setup

participant Client
participant CPCiOSBackgroundManager
participant "CPCSipAccount\nManager" as CPCSipAccountManager
participant "CPCSipConversation\nManager" as CPCSipConversationManager
participant Stretto
participant Sip server
participant APN

Client->Client: App starts in foreground

# On client startup.
Client->>APN: Request for device token
APN-->Client: didUpdatePushCredentials: credentials forType: PKPushTypeVoIP

# The "deleteAccountsWithToken" REST API request should be called when the app is installed and run the first time.
note right of Client: The token is an ID sent by the APN to identify the iOS device.
Client->>Stretto: HTTP request: deleteAccountsWithToken(token)
Stretto-->Client: 200 OK with response data

# Local sip account is enabled in the foreground.
note right of Client: Set discardRegistrationOnRestriction to true\n in the Sip account settings object.
note right of Client: Set usePushNotifications to true\n in the Sip account settings object.
note right of Client: Now, SDK stops sending REGISTER messages \npertinent to the local SIP account\n when the client is subscribed to Stretto.
Client->>CPCSipAccountManager: enableAccount(localSipAccountHandle)

# The "updateAccount" REST API request should be called (should be called) when the SIP account is configured possibly while app is in foreground. When the SIP account configuration change, it is called again.
note right of Client: The accountUUID is a GUID \nthat should be generated and persisted.
Client->>Stretto: HTTP request: updateAccount(accountUUID)
Stretto-->Client: 200 OK with response data

Client->Client: Enter background mode
Client->>Stretto: HTTP request: Subscribe(accountUUID)
Stretto->>Sip server: SIP REGISTER
Stretto-->Client: 200 OK with response data

note right of CPCiOSBackgroundManager: The background manager detects entry into background\n disables the local sip account on behalf of the client.
note right of CPCiOSBackgroundManager: Setting discardRegistrationOnRestriction to true causes\n the SDK to not send a SIP UNREGISTER.
CPCiOSBackgroundManager-->CPCSipAccountManager: [disableAccount: localSipAccountHandle]
note right of Client: The local sip account is inactive but, \nStretto is active and monitoring calls.

# When an incoming call event occurs
Sip server->Stretto: Incoming call i.e. SIP INVITE
note right of Stretto: Incoming call event causes Stretto\n to inform the APN.
APN-->Client: didReceiveIncomingPushWithPayload:payload forType:PKPushTypeVoIP

note right of Client: Payload from APN contains\n tunnel information from Stretto.
note right of Client: The Client sets up another SIP account and\n uses the tunnel as the SIP transport.
Client->CPCSipAccountManager: [createAccount:strettoTunnelAccountSettings]
Client->CPCSipAccountManager: [addHandler:strettoTunnelAccountHandle]

# Set appropriate NAT settings
Client->CPCSipConversationManager: [setDefaultSettings:conversationSettings forAccount:strettoTunnelAccountHandle]

# Set up the Apple Callkit UI
Client->CPCSipConversationManager: [addHandler:callViewController forAccount:strettoTunnelAccountHandle]

Client->CPCSipAccountManager: [enableAccount:strettoTunnelAccountHandle]

note right of Client: The user can accept or reject the incoming call.
