option footer=none

participant Client
participant "SipPresenceManager" as PM
participant "SDK process" as SDKP
participant "SipPresenceSubscriptionHandler" as PH

Client->>PM: getInterface()
Client->>PM: set<PERSON><PERSON><PERSON>()

Note right of SDKP: Be prepared to handle errors:
SDKP->>PH: onError()

 onNewSubscription() is called when an incoming presence request is received
SDKP->>PH: onNewSubscription\n(subscription handle, event)

Note right of Client: To accept the request:
Client->>PM: preparePresence(subscription handle, \npresence)
SDKP->>PH: onPresenceReadyToSend()
Client->>PM:  accept()
SDKP->>PH: onSubscriptionStateChanged(..event)
Client->>Client: add this person to the \ninternal list of subscribers

Note right of Client: ...or to reject the request:
Client->>PM:  reject()
SDKP->>PH: onSubscriptionStateChanged(..event)

Note right of Client: Optionally, to terminate this subscription:
Client->>PM: end()
SDKP->>PH: onSubscriptionEnded()
