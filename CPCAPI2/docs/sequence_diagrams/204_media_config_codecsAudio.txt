option footer=none

participant Client
participant "AudioManager" as AM
participant "SDK process" as SDKP
participant "AudioHandler" as AH

Client->>AM: queryCodecList()
SDKP->>AH: onAudioCodecListUpdated()
Client->>Client: process the list
Client->>AM: setCodecPriority(codec, priority)
SDKP->>AH: onAudioCodecListUpdated()
Client->>AM: setCodecEnabled(codec,enabled)
SDKP->>AH: onAudioCodecListUpdated()

Note right of Client: Repeat for each codec
Note right of Client: When done:

Client->>AM: queryCodecList()
SDKP->>AH: onAudioCodecListUpdated()
Client->>Client: process the final list