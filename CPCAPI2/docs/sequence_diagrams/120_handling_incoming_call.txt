option footer=none
participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHand<PERSON>" as SCH

SDKP->>SCH: onNewConversation\n(SipConversationHandle)
Client->>SCM: sendRingingResponse()

Note left of Client: Examine the callback
SDKP->>SCH: onConversationStateChanged\n(ConversationStateChangedEvent)
Note left of Client: Negotiate media

Note left of Client: Accept or reject
Client->>SCM: accept() or reject() or redirect()

Note left of Client: Handle state changes

Note right of SDKP:  called repeatedly as conversation \nis processed; eventually the state \nin the event specifies "connected"

SDKP->>SCH: onConversationStateChanged\n(ConversationStateChangedEvent)

