option footer=none

participant Client
participant "VideoManager" as VM
participant "SDK process" as SDKP
participant "VideoHandler" as VH

Client->>VM: queryCodecList()
SDKP->>VH: onVideoCodecListUpdated()
Client->>Client: process the list
Client->>VM: setCodecPriority(codec, priority)
SDKP->>VH: onVideoCodecListUpdated()
Client->>VM: setCodecEnabled(codec,enabled)
SDKP->>VH: onVideoCodecListUpdated()

Note right of Client: Repeat for each codec
Note right of Client: When done:

Client->>VM: queryCodecList()
SDKP->>VH: onVideoCodecListUpdated()
Client->>Client: process the final list
