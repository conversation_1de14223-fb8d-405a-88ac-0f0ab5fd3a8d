{\rtf1\ansi\ansicpg1252\cocoartf1347\cocoasubrtf570
{\fonttbl\f0\fswiss\fcharset0 Helvetica;}
{\colortbl;\red255\green255\blue255;}
\margl1440\margr1440\vieww10800\viewh8400\viewkind0
\pard\tx566\tx1133\tx1700\tx2267\tx2834\tx3401\tx3968\tx4535\tx5102\tx5669\tx6236\tx6803\pardirnatural

\f0\fs24 \cf0 title Setting an Xmpp Vcard\
\
\
Client->XmppVCardManager:getInterface(phone)\
Client->XmppVCardManager:setHandler(accountHandle, handler)\
note right of SDK Process,XmppVCardHandler:Be prepared to handle errors:\
SDK Process->XmppVCardHandler:onError()\
note right of Client,XmppVCardManager\
 Allocates a new vcard handle\
within the SDK.\
end note\
Client->XmppVCardManager: create(accountHandle)\
\
note right of Client,XmppVCardManager: Update own vcard\
Client->XmppVCardManager:storeVCard(VCardHandle,VCardDetail)\
\
//note right of Client,XmppVCardManager\
//Optional:\
//Cancel all pending operations \
//(fetch or store)\
//end note\
//Client-//>XmppVCardManager: cancelVCardOperations(VCardHandle)\
\
note right of SDK Process,XmppVCardHandler\
This function is called to indicate the result of \
a VCard operation\
end note\
SDK Process->XmppVCardHandler:onVCardOperationResult(VCardHandle, VCardOperationResultEvent)\
}