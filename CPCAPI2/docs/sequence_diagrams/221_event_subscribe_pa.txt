option footer=none

participant Client
participant "SipEventManager" as SM
participant "SDK process" as SDKP
participant "SipEventSubscriptionHandler" as SH

Client->>SM: getInterface()
Client->>SM: setHandler()

Note right of SDKP: Be prepared to handle errors:
SDKP->>SH: onError()

Note right of SDKP: Be prepared to handle errors:
SDKP->>SH: onError()

 createSubscription() returns a subscription handle
Client->>SM: createSubscription()

Client->>SM: \n\nsetEventServer(subscription handle, \naddress of subscription agent)


 The settings parameter specifies the event to subscribe to
Client->>SM: \n\napplySubscriptionSettings(subscription handle, settings)

Note right of Client: repeat addParticipant() in order to \nsubscribe to this same event at another target

 Calling addParticipant() adds the target address to the internal list maintained by the SDK
Client->>SM:  addParticipant(subscription handle, target address)

Client->>SM: \n\n\nstart(subscription handle)



SDKP->>SH: onNewSubscription\n(subscription handle, event)

Note right of SDKP: When the subscription request is \naccepted or rejected by the other parties:
SDKP->>SH: onSubscriptionStateChanged\n(subscription handle, event)

 Also be prepared to handle onIncomingEventState() asynchronously; it is called each time the other target publishes an event.
Note right of SDKP: As the subscription request is accepted \nby the other parties:

SDKP->>SH: onIncomingEventState\n(subscription handle, event)

Note right of Client: \nOptionally, to terminate subscribing

Client->>SM: end(subscription handle)
SDKP->>SH: onSubscriptionEnded\n(subscription handle, event)

 