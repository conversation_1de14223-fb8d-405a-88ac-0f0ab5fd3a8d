
option footer=none

participant Client
participant "SipConversationManager" as SCM
participant "SDK process" as SDKP
participant "SipConversationHand<PERSON>" as SCH


Client->>Client: conversation X is established with <PERSON>
Client->>Client: establish conversation Y with <PERSON>
Client->>SCM: hold (handle Y)
Client->>SCM: transfer(handle X, <PERSON> address) 

 Alice gets an event for <PERSON> that contains information about the progress of the transfer
SDKP->>SCH: 	onTransferProgress(handle Z, event)

note right of SDKP: If the transfer succeeds, the event for Z \neventually specifies "connected" The other two \nconversations will then end

SDKP->>SCH: 	onConversationEnded(handle X)
SDKP->>SCH: 	onConversationEnded(handle Y)

note right of SDKP: If the transfer fails, the event for <PERSON> specifies "rejected" 

Client->>Client: Both conversation X and \nconversation Y still exist \n(Y is on hold).

