option footer=none

participant Client
participant "AudioManager" as AM
participant "SDK process" as SDKP
participant "AudioHandler" as AH

Client->>AM: getInterface()
Client->>AM: setHandler()

Client->>AM: queryDeviceList()
SDKP->>AH: onAudioDeviceListUpdated()
Client->>Client: process the list
Client->>AM: setCaptureDevice()
 Note right of Client: Be prepared to handle onAudioDeviceListUpdated \nat any time; this function will be called \nwhenever a device change is detected, so, each \ntime the user unplugs a device or plugs in a device.