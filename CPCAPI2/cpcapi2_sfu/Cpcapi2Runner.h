#pragma once

// SDK includes
#include <cpcapi2.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/ThreadIf.hxx>

#include "CpcWebStreamer.h"
#include "CpcWebStreamerObserver.h"

namespace cp
{
namespace sfu
{
class Cpcapi2Runner : public CPCAPI2::PhoneErrorHandler,
                      public CPCAPI2::SipAccount::SipAccountHandler,
                      public CPCAPI2::SipConversation::SipConversationHandler,
                      public CPCAPI2::Media::VideoHandler,
                      public resip::ThreadIf,
                      public CpcWebStreamerObserver
{
public:
   Cpcapi2Runner();
   virtual ~Cpcapi2Runner();

   // ThreadIf
   virtual void run();
   virtual void shutdown();

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

   // SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args);

   // SipConversationHandler
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args);
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args);
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args);
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args);
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args);
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args);
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args);
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args);

   // VideoHandler
   virtual int onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args);
   virtual int onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args);

   // CpcWebStreamerObserver
   virtual void onWebSocketOpen();

private:
   virtual void thread();
   void initAccountSettings();
   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;

private:
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::SipAccount::SipAccountManager* mAccount;
   CPCAPI2::SipAccount::SipAccountHandle mAccountHandle;
   CPCAPI2::SipAccount::SipAccountSettings mAccountSettings;
   CPCAPI2::SipConversation::SipConversationManager* mConversation;
   CPCAPI2::Media::MediaManager* mMedia;

   int mVidSourceId;

   struct CallInfo
   {
      cpc::string address;
      cpc::string displayName;
      bool isPresenter;
   };
   std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo> mCalls;
   CpcWebStreamer mWebStreamer;
};
}
}
