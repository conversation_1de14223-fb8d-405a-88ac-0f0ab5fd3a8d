#pragma once

#include <websocketpp/config/asio_no_tls.hpp>
#include <websocketpp/server.hpp>
#include <iostream>
#include <set>
#include <thread>

namespace cp
{
namespace sfu
{
class CpcWebStreamerObserver;

class CpcWebStreamer : public CPCAPI2::Media::VideoBitstreamHandler
{
public:
   CpcWebStreamer();
   virtual ~CpcWebStreamer();

   void StartServer(CPCAPI2::Phone* phone, CpcWebStreamerObserver* obs);
   void StopServer();

   // VideoBitstreamHandler
   virtual void onBitstreamStarted(int videoStreamId, const CPCAPI2::Media::BitstreamStartedEvent& args);
   virtual void onBitstreamEnded(int videoStreamId, const CPCAPI2::Media::BitstreamEndedEvent& args);
   virtual void onBitstreamData(int videoStreamId, const CPCAPI2::Media::BitstreamDataEvent& args);

private:
   typedef websocketpp::server<websocketpp::config::asio> server;
   typedef server::message_ptr message_ptr;

   struct ConnInfo
   {
      ConnInfo(websocketpp::connection_hdl h) : conn(h), waitingForSap(true), nextSeqNum(0), decodeTimeOffset(0) {}
      websocketpp::connection_hdl conn;
      bool waitingForSap;
      uint32_t nextSeqNum;
      uint64_t decodeTimeOffset;
   };
   typedef std::map<websocketpp::connection_hdl, std::shared_ptr<ConnInfo>, std::owner_less<websocketpp::connection_hdl> > con_list;

   void on_open(server* s, websocketpp::connection_hdl hdl);
   void on_close(server* s, websocketpp::connection_hdl hdl);
   void on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg);

private:
   CPCAPI2::Phone* mPhone;
   server mWebSockServer;
   con_list mConnections;
   std::vector<unsigned char> mFtypMoov;
   unsigned int mInitSegCnt;
   CpcWebStreamerObserver* mObs;
   std::thread* mServerThread;
};
}
}
