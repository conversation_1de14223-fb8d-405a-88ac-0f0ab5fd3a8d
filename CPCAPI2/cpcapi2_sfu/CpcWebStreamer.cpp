#include "stdafx.h"
#include "CpcWebStreamer.h"
#include "CpcWebStreamerObserver.h"

#include <thread>
#include <future>
#include <iostream>
#include <fstream>

using namespace CPCAPI2::Media;

namespace cp
{
namespace sfu
{
CpcWebStreamer::CpcWebStreamer()
   : mPhone(NULL), mInitSegCnt(2), mObs(NULL), mServerThread(NULL)
{
}

CpcWebStreamer::~CpcWebStreamer()
{
}

void CpcWebStreamer::StartServer(CPCAPI2::Phone* phone, CpcWebStreamerObserver* obs)
{
   mPhone = phone;
   mObs = obs;
   mServerThread = new std::thread([&]() {
      try {
         // Set logging settings
         mWebSockServer.set_access_channels(websocketpp::log::alevel::all);
         mWebSockServer.clear_access_channels(websocketpp::log::alevel::frame_payload);

         // Initialize Asio
         mWebSockServer.init_asio();

         // Register our message handler
         mWebSockServer.set_open_handler(std::bind(&CpcWebStreamer::on_open, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_close_handler(std::bind(&CpcWebStreamer::on_close, this, &mWebSockServer, std::placeholders::_1));
         mWebSockServer.set_message_handler(std::bind(&CpcWebStreamer::on_message, this, &mWebSockServer, std::placeholders::_1, std::placeholders::_2));

         // Listen on port 9002
         mWebSockServer.listen(9003);

         // Start the server accept loop
         mWebSockServer.start_accept();

         // Start the ASIO io_service run loop
         mWebSockServer.run();

         std::cout << "web streamer thread exit..." << std::endl;
      }
      catch (websocketpp::exception const & e) {
         std::cout << e.what() << std::endl;
      }
      catch (...) {
         std::cout << "other exception" << std::endl;
      }
   });
}

void CpcWebStreamer::StopServer()
{
   mWebSockServer.get_io_service().post([&]() {
      websocketpp::lib::error_code ec;
      //mWebSockServer.stop_listening(ec);
      mWebSockServer.stop();
   });
   mServerThread->join();
   delete mServerThread;
}

void CpcWebStreamer::on_message(server* s, websocketpp::connection_hdl hdl, message_ptr msg)
{
}

void CpcWebStreamer::on_open(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "accepted a websocket connection" << std::endl;
   mObs->onWebSocketOpen();
   mConnections[hdl] = std::shared_ptr<ConnInfo>(new ConnInfo(hdl));
   assert(mFtypMoov.size() > 0);
   //assert(mInitSegCnt == 0);
   mWebSockServer.send(hdl, &mFtypMoov[0], mFtypMoov.size(), websocketpp::frame::opcode::binary);
   //mConnections[hdl]->nextSeqNum++;
#if 0
   std::ofstream mp4file;
   mp4file.open("bitstream_debug4.mp4", std::ios::out | std::ios::app | std::ios::binary);
   if (mp4file.is_open()) {
      mp4file.write((const char*)&mFtypMoov[0], mFtypMoov.size());
      mp4file.close();
   }
#endif
}

void CpcWebStreamer::on_close(server* s, websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection closed" << std::endl;
   mConnections.erase(hdl);
}

void CpcWebStreamer::onBitstreamStarted(int videoStreamId, const CPCAPI2::Media::BitstreamStartedEvent& args)
{
}

void CpcWebStreamer::onBitstreamEnded(int videoStreamId, const CPCAPI2::Media::BitstreamEndedEvent& args)
{
}

void CpcWebStreamer::onBitstreamData(int videoStreamId, const CPCAPI2::Media::BitstreamDataEvent& args)
{
   try {
      if (mFtypMoov.empty())
      {
         mFtypMoov.resize(args.size);
         memcpy(&mFtypMoov[0], args.data, args.size);
      }
      //else if (mInitSegCnt > 0) {
      //   VideoExt::getInterface(MediaManager::getInterface(mPhone))->fixMp4MoofSequenceNum(args, 0);
      //   size_t origSize = mFtypMoov.size();
      //   mFtypMoov.resize(mFtypMoov.size() + args.size);
      //   memcpy(&mFtypMoov[origSize], args.data, args.size);
      //   mInitSegCnt--;
      //}
      con_list::iterator it = mConnections.begin();
      for (; it != mConnections.end(); ++it)
      {
         if (args.sap)
         {
            it->second->waitingForSap = false;
         }
         if (!it->second->waitingForSap)
         {
            VideoExt* videoExt = VideoExt::getInterface(MediaManager::getInterface(mPhone));
            if (videoExt->fixMp4MoofSequenceNum(args, it->second->nextSeqNum) == CPCAPI2::kSuccess)
            {
               it->second->nextSeqNum++;
            }
            if (videoExt->fixBaseMediaDecodeTime(args, it->second->decodeTimeOffset) == CPCAPI2::kSuccess)
            {

            }
            mWebSockServer.send(it->second->conn, args.data, args.size, websocketpp::frame::opcode::binary);
#if 0
            std::ofstream mp4file;
            mp4file.open("bitstream_debug4.mp4", std::ios::out | std::ios::app | std::ios::binary);
            if (mp4file.is_open()) {
               mp4file.write((const char*)args.data, args.size);
               mp4file.close();
            }
#endif
         }
      }
#if 0
      std::ofstream mp4file;
      mp4file.open("bitstream_debug3.mp4", std::ios::out | std::ios::app | std::ios::binary);
      if (mp4file.is_open()) {
         mp4file.write((const char*)args.data, args.size);
         mp4file.close();
      }
#endif
   }
   catch (const websocketpp::lib::error_code& e) {
      std::cout << "Echo failed because: " << e
         << "(" << e.message() << ")" << std::endl;
   }
}

}
}
