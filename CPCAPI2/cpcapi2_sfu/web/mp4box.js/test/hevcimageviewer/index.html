<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>MP4Box.js - HEVC image extraction from MP4 and display using BPG</title>

        <!-- Include basic JQuery and JQuery UI files -->
        <link rel="stylesheet" type="text/css" href="../lib/jquery-ui/jquery-ui.min.css">
        <script src="../lib/jquery/jquery.js"></script>        
        <script src="../lib/jquery-ui/jquery-ui.min.js"></script>      
        <script src="../lib/jdataview/jdataview.js"></script>        
        <script src="../lib/bpg/bpgdec.js"></script>        

        <link rel="stylesheet" type="text/css" href="../style.css">
        <script src="../../dist/mp4box.all.js"></script>
        <script src="../downloader.js"></script>
        <script src="bitstream.js"></script>
        <script src="bpg.js"></script>
        <script src="hevcframe.js"></script>
        <script src="index_bpg.js"></script>
        <script src="sample_urls.js"></script>
    </head>

    <body>
        <div id="menu"> 
            <h2>MP4Box.js</h2>
            <h3>HEVC image extraction from MP4 and display as BPG</h3>
           
            <div id="tabs">
                <ul>
                    <li><a href="#tabs-1">File upload</a></li>
                    <li><a href="#tabs-2">HTTP URL input</a></li>
                </ul>
                <div id="tabs-1">
                    <legend>Select an MP4-HEVC or BPG file</legend>
                    </br>
                    <input type="file" id="fileInput" onchange="loadFromFile();">
                </div>
                <div id="tabs-2">
                    <legend>Select or enter an HTTP URL to an MP4-HEVC or BPG resource</legend>
                    </br>
                    <label for="urlInput">Enter URL:</label>
                    <select id="urlSelector" onchange="setUrl(this.value);" onfocus="this.selectedIndex = -1;">
                    </select>
                    <input type="url" id="urlInput">
                    <button onclick="loadFromHttpUrl();">Load</button>
                </div>
            </div>
            <div id="progressbar">
                <div id="progressLabel">
                    <img id="stopButton" src="stop_icon.png" onclick="toggleStopExtraction(true);">
                    <span id="progressText">0%</span>
                </div>
            </div>
        </div>
        

        <div id="timeline"></div>
        <div id="popup"><div id="image"></div></div>

    </body>
</html>