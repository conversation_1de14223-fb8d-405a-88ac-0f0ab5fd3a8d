/*! mp4box 12-07-2016 */

var Log=function(){var a=new Date,b=4,c=3,d=2,e=1,f=b,g={setLogLevel:function(a){f=a==this.debug?e:a==this.info?d:a==this.warn?c:a==this.error?b:b},debug:function(b,c){e>=f&&console.debug("["+Log.getDurationString(new Date-a,1e3)+"]","["+b+"]",c)},info:function(b,c){d>=f&&console.info("["+Log.getDurationString(new Date-a,1e3)+"]","["+b+"]",c)},warn:function(b,d){c>=f&&console.warn("["+Log.getDurationString(new Date-a,1e3)+"]","["+b+"]",d)},error:function(c,d){b>=f&&console.error("["+Log.getDurationString(new Date-a,1e3)+"]","["+c+"]",d)}};return g}();Log.getDurationString=function(a,b){function c(a,b){for(var c=""+a,d=c.split(".");d[0].length<b;)d[0]="0"+d[0];return d.join(".")}var d;0>a?(d=!0,a=-a):d=!1;var e=b||1,f=a/e,g=Math.floor(f/3600);f-=3600*g;var h=Math.floor(f/60);f-=60*h;var i=1e3*f;return f=Math.floor(f),i-=1e3*f,i=Math.floor(i),(d?"-":"")+g+":"+c(h,2)+":"+c(f,2)+"."+c(i,3)},Log.printRanges=function(a){var b=a.length;if(b>0){for(var c="",d=0;b>d;d++)d>0&&(c+=","),c+="["+Log.getDurationString(a.start(d))+","+Log.getDurationString(a.end(d))+"]";return c}return"(empty)"};var MP4BoxStream=function(a){if(!(a instanceof ArrayBuffer))throw"Needs an array buffer";this.buffer=a,this.uint8=new Uint8Array(a),this.position=0};MP4BoxStream.prototype.getPosition=function(){return this.position},MP4BoxStream.prototype.getEndPosition=function(){return this.buffer.byteLength},MP4BoxStream.prototype.getLength=function(){return this.buffer.byteLength},MP4BoxStream.prototype.seek=function(a){var b=Math.max(0,Math.min(this.uint8.length,a));this.position=isNaN(b)||!isFinite(b)?0:b},MP4BoxStream.prototype.isEos=function(){return this.getPosition()>=this.getEndPosition()},MP4BoxStream.prototype.readUint8=function(){var a;if(this.position+1<=this.uint8.length)return a=this.uint8[this.position],this.position++,a;throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readUint16=function(){var a,b,c;if(this.position+2<=this.uint8.length)return a=this.uint8[this.position],this.position++,b=this.uint8[this.position],this.position++,c=a<<8|b;throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readUint24=function(){var a;if(this.position+3<=this.uint8.length)return a=this.uint8[this.position]<<16,this.position++,a|=this.uint8[this.position]<<8,this.position++,a|=this.uint8[this.position],this.position++,a;throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readUint32=function(){var a;if(this.position+4<=this.uint8.length)return a=this.uint8[this.position]<<24,this.position++,a|=this.uint8[this.position]<<16,this.position++,a|=this.uint8[this.position]<<8,this.position++,a|=this.uint8[this.position],this.position++,a;throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readUint64=function(){var a;if(this.position+8<=this.uint8.length)return a=this.readUint32()<<32,a|=this.readUint32();throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readString=function(a){if(this.position+a<=this.uint8.length){for(var b="",c=0;a>c;c++)b+=String.fromCharCode(this.readUint8());return b}throw"Not enough bytes in buffer"},MP4BoxStream.prototype.readCString=function(){for(var a=[];;){var b=this.readUint8();if(0===b)break;a.push(b)}return String.fromCharCode.apply(null,a)},MP4BoxStream.prototype.readInt8=function(){return this.readUint8()},MP4BoxStream.prototype.readInt16=function(){return this.readUint16()},MP4BoxStream.prototype.readInt32=function(){return this.readUint32()},MP4BoxStream.prototype.readUint8Array=function(a){for(var b=[],c=0;a>c;c++)b[c]=this.readUint8();return b},MP4BoxStream.prototype.readInt16Array=function(a){for(var b=[],c=0;a>c;c++)b[c]=this.readUint16();return b},MP4BoxStream.prototype.readUint32Array=function(a){for(var b=[],c=0;a>c;c++)b[c]=this.readUint32();return b},MP4BoxStream.prototype.readInt32Array=function(a){for(var b=[],c=0;a>c;c++)b[c]=this.readInt32();return b};var DataStream=function(a,b,c){this._byteOffset=b||0,a instanceof ArrayBuffer?this.buffer=a:"object"==typeof a?(this.dataView=a,b&&(this._byteOffset+=b)):this.buffer=new ArrayBuffer(a||0),this.position=0,this.endianness=null==c?DataStream.LITTLE_ENDIAN:c};DataStream.prototype={},DataStream.prototype.getPosition=function(){return this.position},DataStream.prototype._realloc=function(a){if(this._dynamicSize){var b=this._byteOffset+this.position+a,c=this._buffer.byteLength;if(c>=b)return void(b>this._byteLength&&(this._byteLength=b));for(1>c&&(c=1);b>c;)c*=2;var d=new ArrayBuffer(c),e=new Uint8Array(this._buffer),f=new Uint8Array(d,0,e.length);f.set(e),this.buffer=d,this._byteLength=b}},DataStream.prototype._trimAlloc=function(){if(this._byteLength!=this._buffer.byteLength){var a=new ArrayBuffer(this._byteLength),b=new Uint8Array(a),c=new Uint8Array(this._buffer,0,b.length);b.set(c),this.buffer=a}},DataStream.BIG_ENDIAN=!1,DataStream.LITTLE_ENDIAN=!0,DataStream.prototype._byteLength=0,Object.defineProperty(DataStream.prototype,"byteLength",{get:function(){return this._byteLength-this._byteOffset}}),Object.defineProperty(DataStream.prototype,"buffer",{get:function(){return this._trimAlloc(),this._buffer},set:function(a){this._buffer=a,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(DataStream.prototype,"byteOffset",{get:function(){return this._byteOffset},set:function(a){this._byteOffset=a,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._buffer.byteLength}}),Object.defineProperty(DataStream.prototype,"dataView",{get:function(){return this._dataView},set:function(a){this._byteOffset=a.byteOffset,this._buffer=a.buffer,this._dataView=new DataView(this._buffer,this._byteOffset),this._byteLength=this._byteOffset+a.byteLength}}),DataStream.prototype.seek=function(a){var b=Math.max(0,Math.min(this.byteLength,a));this.position=isNaN(b)||!isFinite(b)?0:b},DataStream.prototype.isEof=function(){return this.position>=this._byteLength},DataStream.prototype.mapUint8Array=function(a){this._realloc(1*a);var b=new Uint8Array(this._buffer,this.byteOffset+this.position,a);return this.position+=1*a,b},DataStream.prototype.readInt32Array=function(a,b){a=null==a?this.byteLength-this.position/4:a;var c=new Int32Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readInt16Array=function(a,b){a=null==a?this.byteLength-this.position/2:a;var c=new Int16Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readInt8Array=function(a){a=null==a?this.byteLength-this.position:a;var b=new Int8Array(a);return DataStream.memcpy(b.buffer,0,this.buffer,this.byteOffset+this.position,a*b.BYTES_PER_ELEMENT),this.position+=b.byteLength,b},DataStream.prototype.readUint32Array=function(a,b){a=null==a?this.byteLength-this.position/4:a;var c=new Uint32Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readUint16Array=function(a,b){a=null==a?this.byteLength-this.position/2:a;var c=new Uint16Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readUint8Array=function(a){a=null==a?this.byteLength-this.position:a;var b=new Uint8Array(a);return DataStream.memcpy(b.buffer,0,this.buffer,this.byteOffset+this.position,a*b.BYTES_PER_ELEMENT),this.position+=b.byteLength,b},DataStream.prototype.readFloat64Array=function(a,b){a=null==a?this.byteLength-this.position/8:a;var c=new Float64Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readFloat32Array=function(a,b){a=null==a?this.byteLength-this.position/4:a;var c=new Float32Array(a);return DataStream.memcpy(c.buffer,0,this.buffer,this.byteOffset+this.position,a*c.BYTES_PER_ELEMENT),DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=c.byteLength,c},DataStream.prototype.readInt32=function(a){var b=this._dataView.getInt32(this.position,null==a?this.endianness:a);return this.position+=4,b},DataStream.prototype.readInt16=function(a){var b=this._dataView.getInt16(this.position,null==a?this.endianness:a);return this.position+=2,b},DataStream.prototype.readInt8=function(){var a=this._dataView.getInt8(this.position);return this.position+=1,a},DataStream.prototype.readUint32=function(a){var b=this._dataView.getUint32(this.position,null==a?this.endianness:a);return this.position+=4,b},DataStream.prototype.readUint16=function(a){var b=this._dataView.getUint16(this.position,null==a?this.endianness:a);return this.position+=2,b},DataStream.prototype.readUint8=function(){var a=this._dataView.getUint8(this.position);return this.position+=1,a},DataStream.prototype.readFloat32=function(a){var b=this._dataView.getFloat32(this.position,null==a?this.endianness:a);return this.position+=4,b},DataStream.prototype.readFloat64=function(a){var b=this._dataView.getFloat64(this.position,null==a?this.endianness:a);return this.position+=8,b},DataStream.endianness=new Int8Array(new Int16Array([1]).buffer)[0]>0,DataStream.memcpy=function(a,b,c,d,e){var f=new Uint8Array(a,b,e),g=new Uint8Array(c,d,e);f.set(g)},DataStream.arrayToNative=function(a,b){return b==this.endianness?a:this.flipArrayEndianness(a)},DataStream.nativeToEndian=function(a,b){return this.endianness==b?a:this.flipArrayEndianness(a)},DataStream.flipArrayEndianness=function(a){for(var b=new Uint8Array(a.buffer,a.byteOffset,a.byteLength),c=0;c<a.byteLength;c+=a.BYTES_PER_ELEMENT)for(var d=c+a.BYTES_PER_ELEMENT-1,e=c;d>e;d--,e++){var f=b[e];b[e]=b[d],b[d]=f}return a},DataStream.prototype.failurePosition=0,String.fromCharCodeUint8=function(a){for(var b=[],c=0;c<a.length;c++)b[c]=a[c];return String.fromCharCode.apply(null,b)},DataStream.prototype.readString=function(a,b){return null==b||"ASCII"==b?String.fromCharCodeUint8.apply(null,[this.mapUint8Array(null==a?this.byteLength-this.position:a)]):new TextDecoder(b).decode(this.mapUint8Array(a))},DataStream.prototype.readCString=function(a){var b=this.byteLength-this.position,c=new Uint8Array(this._buffer,this._byteOffset+this.position),d=b;null!=a&&(d=Math.min(a,b));for(var e=0;d>e&&0!==c[e];e++);var f=String.fromCharCodeUint8.apply(null,[this.mapUint8Array(e)]);return null!=a?this.position+=d-e:e!=b&&(this.position+=1),f};var MAX_SIZE=Math.pow(2,32);DataStream.prototype.readUint64=function(){return this.readUint32()*MAX_SIZE+this.readUint32()},DataStream.prototype.readUint24=function(){return(this.readUint8()<<16)+(this.readUint8()<<8)+this.readUint8()},"undefined"!=typeof exports&&(exports.DataStream=DataStream),DataStream.prototype.save=function(a){var b=new Blob([this.buffer]),c=window.webkitURL||window.URL;if(!c||!c.createObjectURL)throw"DataStream.save: Can't create object URL.";var d=c.createObjectURL(b),e=document.createElement("a");e.setAttribute("href",d),e.setAttribute("download",a),e.click(),c.revokeObjectURL(d)},DataStream.prototype._dynamicSize=!0,Object.defineProperty(DataStream.prototype,"dynamicSize",{get:function(){return this._dynamicSize},set:function(a){a||this._trimAlloc(),this._dynamicSize=a}}),DataStream.prototype.shift=function(a){var b=new ArrayBuffer(this._byteLength-a),c=new Uint8Array(b),d=new Uint8Array(this._buffer,a,c.length);c.set(d),this.buffer=b,this.position-=a},DataStream.prototype.writeInt32Array=function(a,b){if(this._realloc(4*a.length),a instanceof Int32Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapInt32Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeInt32(a[c],b)},DataStream.prototype.writeInt16Array=function(a,b){if(this._realloc(2*a.length),a instanceof Int16Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapInt16Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeInt16(a[c],b)},DataStream.prototype.writeInt8Array=function(a){if(this._realloc(1*a.length),a instanceof Int8Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapInt8Array(a.length);else for(var b=0;b<a.length;b++)this.writeInt8(a[b])},DataStream.prototype.writeUint32Array=function(a,b){if(this._realloc(4*a.length),a instanceof Uint32Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapUint32Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeUint32(a[c],b)},DataStream.prototype.writeUint16Array=function(a,b){if(this._realloc(2*a.length),a instanceof Uint16Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapUint16Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeUint16(a[c],b)},DataStream.prototype.writeUint8Array=function(a){if(this._realloc(1*a.length),a instanceof Uint8Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapUint8Array(a.length);else for(var b=0;b<a.length;b++)this.writeUint8(a[b])},DataStream.prototype.writeFloat64Array=function(a,b){if(this._realloc(8*a.length),a instanceof Float64Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapFloat64Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeFloat64(a[c],b)},DataStream.prototype.writeFloat32Array=function(a,b){if(this._realloc(4*a.length),a instanceof Float32Array&&this.byteOffset+this.position%a.BYTES_PER_ELEMENT===0)DataStream.memcpy(this._buffer,this.byteOffset+this.position,a.buffer,0,a.byteLength),this.mapFloat32Array(a.length,b);else for(var c=0;c<a.length;c++)this.writeFloat32(a[c],b)},DataStream.prototype.writeInt32=function(a,b){this._realloc(4),this._dataView.setInt32(this.position,a,null==b?this.endianness:b),this.position+=4},DataStream.prototype.writeInt16=function(a,b){this._realloc(2),this._dataView.setInt16(this.position,a,null==b?this.endianness:b),this.position+=2},DataStream.prototype.writeInt8=function(a){this._realloc(1),this._dataView.setInt8(this.position,a),this.position+=1},DataStream.prototype.writeUint32=function(a,b){this._realloc(4),this._dataView.setUint32(this.position,a,null==b?this.endianness:b),this.position+=4},DataStream.prototype.writeUint16=function(a,b){this._realloc(2),this._dataView.setUint16(this.position,a,null==b?this.endianness:b),this.position+=2},DataStream.prototype.writeUint8=function(a){this._realloc(1),this._dataView.setUint8(this.position,a),this.position+=1},DataStream.prototype.writeFloat32=function(a,b){this._realloc(4),this._dataView.setFloat32(this.position,a,null==b?this.endianness:b),this.position+=4},DataStream.prototype.writeFloat64=function(a,b){this._realloc(8),this._dataView.setFloat64(this.position,a,null==b?this.endianness:b),this.position+=8},DataStream.prototype.writeUCS2String=function(a,b,c){null==c&&(c=a.length);for(var d=0;d<a.length&&c>d;d++)this.writeUint16(a.charCodeAt(d),b);for(;c>d;d++)this.writeUint16(0)},DataStream.prototype.writeString=function(a,b,c){var d=0;if(null==b||"ASCII"==b)if(null!=c){var e=Math.min(a.length,c);for(d=0;e>d;d++)this.writeUint8(a.charCodeAt(d));for(;c>d;d++)this.writeUint8(0)}else for(d=0;d<a.length;d++)this.writeUint8(a.charCodeAt(d));else this.writeUint8Array(new TextEncoder(b).encode(a.substring(0,c)))},DataStream.prototype.writeCString=function(a,b){var c=0;if(null!=b){var d=Math.min(a.length,b);for(c=0;d>c;c++)this.writeUint8(a.charCodeAt(c));for(;b>c;c++)this.writeUint8(0)}else{for(c=0;c<a.length;c++)this.writeUint8(a.charCodeAt(c));this.writeUint8(0)}},DataStream.prototype.writeStruct=function(a,b){for(var c=0;c<a.length;c+=2){var d=a[c+1];this.writeType(d,b[a[c]],b)}},DataStream.prototype.writeType=function(a,b,c){var d;if("function"==typeof a)return a(this,b);if("object"==typeof a&&!(a instanceof Array))return a.set(this,b,c);var e=null,f="ASCII",g=this.position;switch("string"==typeof a&&/:/.test(a)&&(d=a.split(":"),a=d[0],e=parseInt(d[1])),"string"==typeof a&&/,/.test(a)&&(d=a.split(","),a=d[0],f=parseInt(d[1])),a){case"uint8":this.writeUint8(b);break;case"int8":this.writeInt8(b);break;case"uint16":this.writeUint16(b,this.endianness);break;case"int16":this.writeInt16(b,this.endianness);break;case"uint32":this.writeUint32(b,this.endianness);break;case"int32":this.writeInt32(b,this.endianness);break;case"float32":this.writeFloat32(b,this.endianness);break;case"float64":this.writeFloat64(b,this.endianness);break;case"uint16be":this.writeUint16(b,DataStream.BIG_ENDIAN);break;case"int16be":this.writeInt16(b,DataStream.BIG_ENDIAN);break;case"uint32be":this.writeUint32(b,DataStream.BIG_ENDIAN);break;case"int32be":this.writeInt32(b,DataStream.BIG_ENDIAN);break;case"float32be":this.writeFloat32(b,DataStream.BIG_ENDIAN);break;case"float64be":this.writeFloat64(b,DataStream.BIG_ENDIAN);break;case"uint16le":this.writeUint16(b,DataStream.LITTLE_ENDIAN);break;case"int16le":this.writeInt16(b,DataStream.LITTLE_ENDIAN);break;case"uint32le":this.writeUint32(b,DataStream.LITTLE_ENDIAN);break;case"int32le":this.writeInt32(b,DataStream.LITTLE_ENDIAN);break;case"float32le":this.writeFloat32(b,DataStream.LITTLE_ENDIAN);break;case"float64le":this.writeFloat64(b,DataStream.LITTLE_ENDIAN);break;case"cstring":this.writeCString(b,e);break;case"string":this.writeString(b,f,e);break;case"u16string":this.writeUCS2String(b,this.endianness,e);break;case"u16stringle":this.writeUCS2String(b,DataStream.LITTLE_ENDIAN,e);break;case"u16stringbe":this.writeUCS2String(b,DataStream.BIG_ENDIAN,e);break;default:if(3==a.length){for(var h=a[1],i=0;i<b.length;i++)this.writeType(h,b[i]);break}this.writeStruct(a,b)}null!=e&&(this.position=g,this._realloc(e),this.position=g+e)},DataStream.prototype.writeUint64=function(a){var b=Math.floor(a/MAX_SIZE);this.writeUint32(b),this.writeUint32(4294967295&a)},DataStream.prototype.writeUint24=function(a){this.writeUint8((16711680&a)>>16),this.writeUint8((65280&a)>>8),this.writeUint8(255&a)},DataStream.prototype.adjustUint32=function(a,b){var c=this.position;this.seek(a),this.writeUint32(b),this.seek(c)},DataStream.prototype.mapInt32Array=function(a,b){this._realloc(4*a);var c=new Int32Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=4*a,c},DataStream.prototype.mapInt16Array=function(a,b){this._realloc(2*a);var c=new Int16Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=2*a,c},DataStream.prototype.mapInt8Array=function(a){this._realloc(1*a);var b=new Int8Array(this._buffer,this.byteOffset+this.position,a);return this.position+=1*a,b},DataStream.prototype.mapUint32Array=function(a,b){this._realloc(4*a);var c=new Uint32Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=4*a,c},DataStream.prototype.mapUint16Array=function(a,b){this._realloc(2*a);var c=new Uint16Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=2*a,c},DataStream.prototype.mapFloat64Array=function(a,b){this._realloc(8*a);var c=new Float64Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=8*a,c},DataStream.prototype.mapFloat32Array=function(a,b){this._realloc(4*a);var c=new Float32Array(this._buffer,this.byteOffset+this.position,a);return DataStream.arrayToNative(c,null==b?this.endianness:b),this.position+=4*a,c};var MultiBufferStream=function(a){this.buffers=[],this.bufferIndex=-1,a&&(this.insertBuffer(a),this.bufferIndex=0)};MultiBufferStream.prototype=new DataStream(new ArrayBuffer,0,DataStream.BIG_ENDIAN),MultiBufferStream.prototype.initialized=function(){var a;return this.bufferIndex>-1?!0:this.buffers.length>0?(a=this.buffers[0],0===a.fileStart?(this.buffer=a,this.bufferIndex=0,Log.debug("MultiBufferStream","Stream ready for parsing"),!0):(Log.warn("MultiBufferStream","The first buffer should have a fileStart of 0"),this.logBufferLevel(),!1)):(Log.warn("MultiBufferStream","No buffer to start parsing from"),this.logBufferLevel(),!1)},ArrayBuffer.concat=function(a,b){Log.debug("ArrayBuffer","Trying to create a new buffer of size: "+(a.byteLength+b.byteLength));var c=new Uint8Array(a.byteLength+b.byteLength);return c.set(new Uint8Array(a),0),c.set(new Uint8Array(b),a.byteLength),c.buffer},MultiBufferStream.prototype.reduceBuffer=function(a,b,c){var d;return d=new Uint8Array(c),d.set(new Uint8Array(a,b,c)),d.buffer.fileStart=a.fileStart+b,d.buffer.usedBytes=0,d.buffer},MultiBufferStream.prototype.insertBuffer=function(a){for(var b=!0,c=0;c<this.buffers.length;c++){var d=this.buffers[c];if(a.fileStart<=d.fileStart){if(a.fileStart===d.fileStart){if(a.byteLength>d.byteLength){this.buffers.splice(c,1),c--;continue}Log.warn("MultiBufferStream","Buffer (fileStart: "+a.fileStart+" - Length: "+a.byteLength+") already appended, ignoring")}else a.fileStart+a.byteLength<=d.fileStart||(a=this.reduceBuffer(a,0,d.fileStart-a.fileStart)),Log.debug("MultiBufferStream","Appending new buffer (fileStart: "+a.fileStart+" - Length: "+a.byteLength+")"),this.buffers.splice(c,0,a),0===c&&(this.buffer=a);b=!1;break}if(a.fileStart<d.fileStart+d.byteLength){var e=d.fileStart+d.byteLength-a.fileStart,f=a.byteLength-e;if(!(f>0)){b=!1;break}a=this.reduceBuffer(a,e,f)}}b&&(Log.debug("MultiBufferStream","Appending new buffer (fileStart: "+a.fileStart+" - Length: "+a.byteLength+")"),this.buffers.push(a),0===c&&(this.buffer=a))},MultiBufferStream.prototype.logBufferLevel=function(a){var b,c,d,e,f,g=[],h="";for(d=0,e=0,b=0;b<this.buffers.length;b++)c=this.buffers[b],0===b?(f={},g.push(f),f.start=c.fileStart,f.end=c.fileStart+c.byteLength,h+="["+f.start+"-"):f.end===c.fileStart?f.end=c.fileStart+c.byteLength:(f={},f.start=c.fileStart,h+=g[g.length-1].end-1+"], ["+f.start+"-",f.end=c.fileStart+c.byteLength,g.push(f)),d+=c.usedBytes,e+=c.byteLength;g.length>0&&(h+=f.end-1+"]");var i=a?Log.info:Log.debug;0===this.buffers.length?i("MultiBufferStream","No more buffer in memory"):i("MultiBufferStream",""+this.buffers.length+" stored buffer(s) ("+d+"/"+e+" bytes): "+h)},MultiBufferStream.prototype.cleanBuffers=function(){var a,b;for(a=0;a<this.buffers.length;a++)b=this.buffers[a],b.usedBytes===b.byteLength&&(Log.debug("MultiBufferStream","Removing buffer #"+a),this.buffers.splice(a,1),a--)},MultiBufferStream.prototype.mergeNextBuffer=function(){var a;if(this.bufferIndex+1<this.buffers.length){if(a=this.buffers[this.bufferIndex+1],a.fileStart===this.buffer.fileStart+this.buffer.byteLength){var b=this.buffer.byteLength,c=this.buffer.usedBytes,d=this.buffer.fileStart;return this.buffers[this.bufferIndex]=ArrayBuffer.concat(this.buffer,a),this.buffer=this.buffers[this.bufferIndex],this.buffers.splice(this.bufferIndex+1,1),this.buffer.usedBytes=c,this.buffer.fileStart=d,Log.debug("ISOFile","Concatenating buffer for box parsing (length: "+b+"->"+this.buffer.byteLength+")"),!0}return!1}return!1},MultiBufferStream.prototype.findPosition=function(a,b,c){var d,e=null,f=-1;for(d=a===!0?0:this.bufferIndex;d<this.buffers.length&&(e=this.buffers[d],e.fileStart<=b);)f=d,c&&(e.fileStart+e.byteLength<=b?e.usedBytes=e.byteLength:e.usedBytes=b-e.fileStart,this.logBufferLevel()),d++;return-1!==f?(e=this.buffers[f],e.fileStart+e.byteLength>=b?(Log.debug("MultiBufferStream","Found position in existing buffer #"+f),f):-1):-1},MultiBufferStream.prototype.findEndContiguousBuf=function(a){var b,c,d,e=void 0!==a?a:this.bufferIndex;if(c=this.buffers[e],this.buffers.length>e+1)for(b=e+1;b<this.buffers.length&&(d=this.buffers[b],d.fileStart===c.fileStart+c.byteLength);b++)c=d;return c.fileStart+c.byteLength},MultiBufferStream.prototype.getEndFilePositionAfter=function(a){var b=this.findPosition(!0,a,!1);return-1!==b?this.findEndContiguousBuf(b):a},MultiBufferStream.prototype.addUsedBytes=function(a){this.buffer.usedBytes+=a,this.logBufferLevel()},MultiBufferStream.prototype.setAllUsedBytes=function(){this.buffer.usedBytes=this.buffer.byteLength,this.logBufferLevel()},MultiBufferStream.prototype.seek=function(a,b,c){var d;return d=this.findPosition(b,a,c),-1!==d?(this.buffer=this.buffers[d],this.bufferIndex=d,this.position=a-this.buffer.fileStart,Log.debug("MultiBufferStream","Repositioning parser at buffer position: "+this.position),!0):(Log.debug("MultiBufferStream","Position "+a+" not found in buffered data"),!1)},MultiBufferStream.prototype.getPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.position},MultiBufferStream.prototype.getLength=function(){return this.byteLength},MultiBufferStream.prototype.getEndPosition=function(){if(-1===this.bufferIndex||null===this.buffers[this.bufferIndex])throw"Error accessing position in the MultiBufferStream";return this.buffers[this.bufferIndex].fileStart+this.byteLength};var MPEG4DescriptorParser=function(){var a=3,b=4,c=5,d=6,e=[];e[a]="ES_Descriptor",e[b]="DecoderConfigDescriptor",e[c]="DecoderSpecificInfo",e[d]="SLConfigDescriptor";var f=this,g={};return this.parseOneDescriptor=function(a){var b,c,d,f=0,h=0;for(b=a.readUint8(),f++,d=a.readUint8(),f++;128&d;)h=(127&d)<<7,d=a.readUint8(),f++;return h+=127&d,Log.debug("MPEG4DescriptorParser","Found "+(e[b]||"Descriptor "+b)+", size "+h+" at position "+a.getPosition()),c=e[b]?new g[e[b]](h):new g.Descriptor(h),c.parse(a),c},g.Descriptor=function(a,b){this.tag=a,this.size=b,this.descs=[]},g.Descriptor.prototype.parse=function(a){this.data=a.readUint8Array(this.size)},g.Descriptor.prototype.findDescriptor=function(a){for(var b=0;b<this.descs.length;b++)if(this.descs[b].tag==a)return this.descs[b];return null},g.Descriptor.prototype.parseRemainingDescriptors=function(a){for(var b=a.position;a.position<b+this.size;){var c=f.parseOneDescriptor(a);this.descs.push(c)}},g.ES_Descriptor=function(b){g.Descriptor.call(this,a,b)},g.ES_Descriptor.prototype=new g.Descriptor,g.ES_Descriptor.prototype.parse=function(a){if(this.ES_ID=a.readUint16(),this.flags=a.readUint8(),this.size-=3,128&this.flags?(this.dependsOn_ES_ID=a.readUint16(),this.size-=2):this.dependsOn_ES_ID=0,64&this.flags){var b=a.readUint8();this.URL=a.readString(b),this.size-=b+1}else this.URL=null;32&this.flags?(this.OCR_ES_ID=a.readUint16(),this.size-=2):this.OCR_ES_ID=0,this.parseRemainingDescriptors(a)},g.ES_Descriptor.prototype.getOTI=function(a){var c=this.findDescriptor(b);return c?c.oti:0},g.ES_Descriptor.prototype.getAudioConfig=function(a){var d=this.findDescriptor(b);if(!d)return null;var e=d.findDescriptor(c);return e&&e.data?(248&e.data[0])>>3:null},g.DecoderConfigDescriptor=function(a){g.Descriptor.call(this,b,a)},g.DecoderConfigDescriptor.prototype=new g.Descriptor,g.DecoderConfigDescriptor.prototype.parse=function(a){this.oti=a.readUint8(),this.streamType=a.readUint8(),this.bufferSize=a.readUint24(),this.maxBitrate=a.readUint32(),this.avgBitrate=a.readUint32(),this.size-=13,this.parseRemainingDescriptors(a)},g.DecoderSpecificInfo=function(a){g.Descriptor.call(this,c,a)},g.DecoderSpecificInfo.prototype=new g.Descriptor,g.SLConfigDescriptor=function(a){g.Descriptor.call(this,d,a)},g.SLConfigDescriptor.prototype=new g.Descriptor,this},BoxParser={ERR_INVALID_DATA:-1,ERR_NOT_ENOUGH_DATA:0,OK:1,boxCodes:["mdat","idat","free","skip","avcC","hvcC","ftyp","styp","payl","vttC","rtp ","sdp ","btrt","frma","trpy","tpyl","totl","tpay","dmed","dimm","drep","nump","npck","maxr","tmin","tmax","dmax","pmax","payt","vmhd","smhd","hmhd","idat","meco","udta","strk","free","skip"],fullBoxCodes:["mvhd","tkhd","mdhd","hdlr","vmhd","smhd","hmhd","nmhd","url ","urn ","ctts","cslg","stco","co64","stsc","stss","stsz","stz2","stts","stsh","mehd","trex","mfhd","tfhd","trun","tfdt","esds","subs","txtC","sidx","emsg","prft","pssh","elst","dref","url ","urn ","sbgp","sgpd","cprt","iods","ssix","tfra","mfro","pdin","tsel","trep","leva","stri","stsg","schm","stvi","padb","stdp","sdtp","saio","saiz","meta","xml ","bxml","iloc","pitm","ipro","iinf","infe","iref","mere","kind","elng"],containerBoxCodes:[["moov",["trak","sidx"]],["trak"],["edts"],["mdia"],["minf"],["dinf"],["stbl",["sgpd","sbgp"]],["mvex",["trex"]],["moof",["traf"]],["traf",["trun","sgpd","sbgp"]],["vttc"],["tref"],["iref"],["udta"],["mfra"],["meco"],["hnti"],["hinf"],["strk"],["strd"],["sinf"],["rinf"],["schi"],["trgr"],["udta",["kind"]],["iprp"]],sampleEntryCodes:[{prefix:"Visual",types:["mp4v","avc1","avc2","avc3","avc4","avcp","drac","encv","mjp2","mvc1","mvc2","resv","s263","svc1","vc-1","hvc1","hev1"]},{prefix:"Audio",types:["mp4a","ac-3","alac","dra1","dtsc","dtse",,"dtsh","dtsl","ec-3","enca","g719","g726","m4ae","mlpa","raw ","samr","sawb","sawp","sevc","sqcp","ssmv","twos",".mp3"]},{prefix:"Hint",types:["fdp ","m2ts","pm2t","prtp","rm2t","rrtp","rsrp","rtp ","sm2t","srtp"]},{prefix:"Metadata",types:["metx","mett","urim"]},{prefix:"Subtitle",types:["stpp","wvtt","sbtt","tx3g","stxt"]},{prefix:"System",types:["mp4s"]}],sampleGroupEntryCodes:["roll","prol","alst","rap ","tele","avss","avll","sync","tscl","tsas","stsa","scif","mvif","scnm","dtrt","vipr","tele","rash"],trackGroupTypes:["msrc"],initialize:function(){var a,b,c;for(BoxParser.FullBox.prototype=new BoxParser.Box,BoxParser.ContainerBox.prototype=new BoxParser.Box,BoxParser.SampleEntry.prototype=new BoxParser.FullBox,BoxParser.TrackGroupTypeBox.prototype=new BoxParser.FullBox,c=BoxParser.boxCodes.length,a=0;c>a;a++)BoxParser[BoxParser.boxCodes[a]+"Box"]=function(a){return function(b){BoxParser.Box.call(this,BoxParser.boxCodes[a],b)}}(a),BoxParser[BoxParser.boxCodes[a]+"Box"].prototype=new BoxParser.Box;for(c=BoxParser.fullBoxCodes.length,a=0;c>a;a++)BoxParser[BoxParser.fullBoxCodes[a]+"Box"]=function(a){return function(b){BoxParser.FullBox.call(this,BoxParser.fullBoxCodes[a],b)}}(a),BoxParser[BoxParser.fullBoxCodes[a]+"Box"].prototype=new BoxParser.FullBox;for(c=BoxParser.containerBoxCodes.length,a=0;c>a;a++)BoxParser[BoxParser.containerBoxCodes[a][0]+"Box"]=function(a,b){return function(c){if(BoxParser.ContainerBox.call(this,BoxParser.containerBoxCodes[a][0],c),b){this.subBoxNames=b;for(var d=b.length,e=0;d>e;e++)this[b[e]+"s"]=[]}}}(a,BoxParser.containerBoxCodes[a][1]),BoxParser[BoxParser.containerBoxCodes[a][0]+"Box"].prototype=new BoxParser.ContainerBox;for(c=BoxParser.sampleEntryCodes.length,b=0;c>b;b++){var d=BoxParser.sampleEntryCodes[b].prefix,e=BoxParser.sampleEntryCodes[b].types,f=e.length;for(BoxParser[d+"SampleEntry"]=function(a,b){BoxParser.SampleEntry.call(this,a,b)},BoxParser[d+"SampleEntry"].prototype=new BoxParser.SampleEntry,a=0;f>a;a++)BoxParser[e[a]+"SampleEntry"]=function(a,b){return function(c){BoxParser[BoxParser.sampleEntryCodes[a].prefix+"SampleEntry"].call(this,BoxParser.sampleEntryCodes[a].types[b],c)}}(b,a),BoxParser[e[a]+"SampleEntry"].prototype=new BoxParser[d+"SampleEntry"]}for(c=BoxParser.sampleGroupEntryCodes.length,a=0;c>a;a++)BoxParser[BoxParser.sampleGroupEntryCodes[a]+"SampleGroupEntry"]=function(a){return function(b){BoxParser.SampleGroupEntry.call(this,BoxParser.sampleGroupEntryCodes[a],b);
}}(a),BoxParser[BoxParser.sampleGroupEntryCodes[a]+"SampleGroupEntry"].prototype=new BoxParser.SampleGroupEntry;for(c=BoxParser.trackGroupTypes.length,a=0;c>a;a++)BoxParser[BoxParser.trackGroupTypes[a]+"Box"]=function(a){return function(b){BoxParser.TrackGroupTypeBox.call(this,BoxParser.trackGroupTypes[a],b)}}(a),BoxParser[BoxParser.trackGroupTypes[a]+"Box"].prototype=new BoxParser.TrackGroupTypeBox},Box:function(a,b){this.type=a,this.size=b},FullBox:function(a,b){BoxParser.Box.call(this,a,b),this.flags=0,this.version=0},ContainerBox:function(a,b){BoxParser.Box.call(this,a,b),this.boxes=[]},SampleEntry:function(a,b,c,d){BoxParser.Box.call(this,a,b),this.hdr_size=c,this.start=d,this.boxes=[]},SampleGroupEntry:function(a){this.grouping_type=a},TrackGroupTypeBox:function(a,b){BoxParser.FullBox.call(this,a,b)}};BoxParser.initialize(),BoxParser.TKHD_FLAG_ENABLED=1,BoxParser.TKHD_FLAG_IN_MOVIE=2,BoxParser.TKHD_FLAG_IN_PREVIEW=4,BoxParser.TFHD_FLAG_BASE_DATA_OFFSET=1,BoxParser.TFHD_FLAG_SAMPLE_DESC=2,BoxParser.TFHD_FLAG_SAMPLE_DUR=8,BoxParser.TFHD_FLAG_SAMPLE_SIZE=16,BoxParser.TFHD_FLAG_SAMPLE_FLAGS=32,BoxParser.TFHD_FLAG_DUR_EMPTY=65536,BoxParser.TFHD_FLAG_DEFAULT_BASE_IS_MOOF=131072,BoxParser.TRUN_FLAGS_DATA_OFFSET=1,BoxParser.TRUN_FLAGS_FIRST_FLAG=4,BoxParser.TRUN_FLAGS_DURATION=256,BoxParser.TRUN_FLAGS_SIZE=512,BoxParser.TRUN_FLAGS_FLAGS=1024,BoxParser.TRUN_FLAGS_CTS_OFFSET=2048,BoxParser.Box.prototype.add=function(a){var b=new BoxParser[a+"Box"];return this.boxes.push(b),this[a+"s"]?this[a+"s"].push(b):this[a]=b,b},BoxParser.Box.prototype.set=function(a,b){return this[a]=b,this},BoxParser.Box.prototype.addEntry=function(a,b){var c=b||"entries";return this[c]||(this[c]=[]),this[c].push(a),this},"undefined"!=typeof exports&&(exports.BoxParser=BoxParser),BoxParser.SampleEntry.prototype.isVideo=function(){return!1},BoxParser.SampleEntry.prototype.isAudio=function(){return!1},BoxParser.SampleEntry.prototype.isSubtitle=function(){return!1},BoxParser.SampleEntry.prototype.isMetadata=function(){return!1},BoxParser.SampleEntry.prototype.isHint=function(){return!1},BoxParser.SampleEntry.prototype.getCodec=function(){return this.type.replace(".","")},BoxParser.SampleEntry.prototype.getWidth=function(){return""},BoxParser.SampleEntry.prototype.getHeight=function(){return""},BoxParser.SampleEntry.prototype.getChannelCount=function(){return""},BoxParser.SampleEntry.prototype.getSampleRate=function(){return""},BoxParser.SampleEntry.prototype.getSampleSize=function(){return""},BoxParser.VisualSampleEntry.prototype.isVideo=function(){return!0},BoxParser.VisualSampleEntry.prototype.getWidth=function(){return this.width},BoxParser.VisualSampleEntry.prototype.getHeight=function(){return this.height},BoxParser.AudioSampleEntry.prototype.isAudio=function(){return!0},BoxParser.AudioSampleEntry.prototype.getChannelCount=function(){return this.channel_count},BoxParser.AudioSampleEntry.prototype.getSampleRate=function(){return this.samplerate},BoxParser.AudioSampleEntry.prototype.getSampleSize=function(){return this.samplesize},BoxParser.SubtitleSampleEntry.prototype.isSubtitle=function(){return!0},BoxParser.MetadataSampleEntry.prototype.isMetadata=function(){return!0},BoxParser.decimalToHex=function(a,b){var c=Number(a).toString(16);for(b="undefined"==typeof b||null===b?b=2:b;c.length<b;)c="0"+c;return c},BoxParser.avc1SampleEntry.prototype.getCodec=function(){var a=BoxParser.SampleEntry.prototype.getCodec.call(this);return this.avcC?a+"."+BoxParser.decimalToHex(this.avcC.AVCProfileIndication)+BoxParser.decimalToHex(this.avcC.profile_compatibility)+BoxParser.decimalToHex(this.avcC.AVCLevelIndication):a},BoxParser.hvc1SampleEntry.prototype.getCodec=function(){var a,b=BoxParser.SampleEntry.prototype.getCodec.call(this);if(this.hvcC){switch(b+=".",this.hvcC.general_profile_space){case 0:b+="";break;case 1:b+="A";break;case 2:b+="B";break;case 3:b+="C"}b+=this.hvcC.general_profile_idc,b+=".";var c=this.hvcC.general_profile_compatibility,d=0;for(a=0;32>a&&(d|=1&c,31!=a);a++)d<<=1,c>>=1;b+=BoxParser.decimalToHex(d,0),b+=".",b+=0===this.hvcC.general_tier_flag?"L":"H",b+=this.hvcC.general_level_idc;var e=!1,f="";for(a=5;a>=0;a--)(this.hvcC.general_constraint_indicator[a]||e)&&(f="."+BoxParser.decimalToHex(this.hvcC.general_constraint_indicator[a],0)+f,e=!0);b+=f}return b},BoxParser.mp4aSampleEntry.prototype.getCodec=function(){var a=BoxParser.SampleEntry.prototype.getCodec.call(this);if(this.esds&&this.esds.esd){var b=this.esds.esd.getOTI(),c=this.esds.esd.getAudioConfig();return a+"."+BoxParser.decimalToHex(b)+(c?"."+c:"")}return a},BoxParser.stxtSampleEntry.prototype.getCodec=function(){var a=BoxParser.SampleEntry.prototype.getCodec.call(this);return this.mime_format?a+"."+this.mime_format:a},BoxParser.parseOneBox=function(a,b,c){var d,e,f,g=a.getPosition(),h=0;if(a.getEndPosition()-g<8)return Log.debug("BoxParser","Not enough data in stream to parse the type and size of the box"),{code:BoxParser.ERR_NOT_ENOUGH_DATA};var i=a.readUint32(),j=a.readString(4);if(Log.debug("BoxParser","Found box of type "+j+" and size "+i+" at position "+g),h=8,"uuid"==j&&(f=a.readUint8Array(16),h+=16),1==i){if(a.getEndPosition()-a.getPosition()<8)return a.seek(g),Log.warn("BoxParser",'Not enough data in stream to parse the extended size of the "'+j+'" box'),{code:BoxParser.ERR_NOT_ENOUGH_DATA};i=a.readUint64(),h+=8}else if(0===i&&"mdat"!==j)throw"Unlimited box size not supported";return c&&i>c?(Log.error("BoxParser","Box of type "+j+" has a size "+i+" greater than its container size "+c),{code:BoxParser.ERR_NOT_ENOUGH_DATA,type:j,size:i,hdr_size:h,start:g}):g+i>a.getEndPosition()?(a.seek(g),Log.warn("BoxParser",'Not enough data in stream to parse the entire "'+j+'" box'),{code:BoxParser.ERR_NOT_ENOUGH_DATA,type:j,size:i,hdr_size:h,start:g}):b?{code:BoxParser.OK,type:j,size:i,hdr_size:h,start:g}:(BoxParser[j+"Box"]?d=new BoxParser[j+"Box"](i):("uuid"!==j&&Log.warn("BoxParser","Unknown box type: "+j),d=new BoxParser.Box(j,i),f&&(d.uuid=f)),d.hdr_size=h,d.start=g,d.write===BoxParser.Box.prototype.write&&"mdat"!==d.type&&(Log.warn("BoxParser",d.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),d.parseDataAndRewind(a)),d.parse(a),e=a.getPosition()-(d.start+d.size),0>e?(Log.warn("BoxParser","Parsing of box "+d.type+" did not read the entire indicated box data size (missing "+-e+" bytes), seeking forward"),a.seek(d.start+d.size)):e>0&&(Log.error("BoxParser","Parsing of box "+d.type+" read "+e+" more bytes than the indicated box data size, seeking backwards"),a.seek(d.start+d.size)),{code:BoxParser.OK,box:d,size:d.size})},BoxParser.Box.prototype.parse=function(a){"mdat"!=this.type?this.data=a.readUint8Array(this.size-this.hdr_size):0===this.size?a.seek(a.getEndPosition()):a.seek(this.start+this.size)},BoxParser.Box.prototype.parseDataAndRewind=function(a){this.data=a.readUint8Array(this.size-this.hdr_size),a.position-=this.size-this.hdr_size},BoxParser.FullBox.prototype.parseDataAndRewind=function(a){this.parseFullHeader(a),this.data=a.readUint8Array(this.size-this.hdr_size),this.hdr_size-=4,a.position-=this.size-this.hdr_size},BoxParser.FullBox.prototype.parseFullHeader=function(a){this.version=a.readUint8(),this.flags=a.readUint24(),this.hdr_size+=4},BoxParser.FullBox.prototype.parse=function(a){this.parseFullHeader(a),this.data=a.readUint8Array(this.size-this.hdr_size)},BoxParser.ContainerBox.prototype.parse=function(a){for(var b,c;a.getPosition()<this.start+this.size;){if(b=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;c=b.box,this.boxes.push(c),this.subBoxNames&&-1!=this.subBoxNames.indexOf(c.type)?this[this.subBoxNames[this.subBoxNames.indexOf(c.type)]+"s"].push(c):this[c.type]=c}},BoxParser.Box.prototype.parseLanguage=function(a){this.language=a.readUint16();var b=[];b[0]=this.language>>10&31,b[1]=this.language>>5&31,b[2]=31&this.language,this.languageString=String.fromCharCode(b[0]+96,b[1]+96,b[2]+96)},BoxParser.TrackGroupTypeBox.prototype.parse=function(a){this.parseFullHeader(a),this.track_group_id=a.readUint32()},BoxParser.TrackReferenceTypeBox=function(a,b,c,d){BoxParser.Box.call(this,a,b),this.hdr_size=c,this.start=d},BoxParser.TrackReferenceTypeBox.prototype=new BoxParser.Box,BoxParser.TrackReferenceTypeBox.prototype.parse=function(a){this.track_ids=a.readUint32Array((this.size-this.hdr_size)/4)},BoxParser.avcCBox.prototype.parse=function(a){var b,c,d,e;for(this.configurationVersion=a.readUint8(),this.AVCProfileIndication=a.readUint8(),this.profile_compatibility=a.readUint8(),this.AVCLevelIndication=a.readUint8(),this.lengthSizeMinusOne=3&a.readUint8(),c=31&a.readUint8(),e=this.size-this.hdr_size-6,this.SPS=new Array(c),b=0;c>b;b++)d=a.readUint16(),this.SPS[b]=a.readUint8Array(d),e-=2+d;for(c=a.readUint8(),e--,this.PPS=new Array(c),b=0;c>b;b++)d=a.readUint16(),this.PPS[b]=a.readUint8Array(d),e-=2+d;e>0&&(this.ext=a.readUint8Array(e))},BoxParser.btrtBox.prototype.parse=function(a){this.bufferSizeDB=a.readUint32(),this.maxBitrate=a.readUint32(),this.avgBitrate=a.readUint32()},BoxParser.co64Box.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),b=a.readUint32(),this.chunk_offsets=[],0===this.version)for(c=0;b>c;c++)this.chunk_offsets.push(a.readUint64())},BoxParser.cprtBox.prototype.parse=function(a){this.parseFullHeader(a),this.parseLanguage(a),this.notice=a.readCString()},BoxParser.cslgBox.prototype.parse=function(a){this.parseFullHeader(a),0===this.version&&(this.compositionToDTSShift=a.readInt32(),this.leastDecodeToDisplayDelta=a.readInt32(),this.greatestDecodeToDisplayDelta=a.readInt32(),this.compositionStartTime=a.readInt32(),this.compositionEndTime=a.readInt32())},BoxParser.cttsBox.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),b=a.readUint32(),this.sample_counts=[],this.sample_offsets=[],0===this.version)for(c=0;b>c;c++)this.sample_counts.push(a.readUint32()),this.sample_offsets.push(a.readInt32());else if(1==this.version)for(c=0;b>c;c++)this.sample_counts.push(a.readUint32()),this.sample_offsets.push(a.readInt32())},BoxParser.dimmBox.prototype.parse=function(a){this.bytessent=a.readUint64()},BoxParser.dmaxBox.prototype.parse=function(a){this.time=a.readUint32()},BoxParser.dmedBox.prototype.parse=function(a){this.bytessent=a.readUint64()},BoxParser.drefBox.prototype.parse=function(a){var b,c;this.parseFullHeader(a),this.entries=[];for(var d=a.readUint32(),e=0;d>e;e++){if(b=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;c=b.box,this.entries.push(c)}},BoxParser.drepBox.prototype.parse=function(a){this.bytessent=a.readUint64()},BoxParser.elngBox.prototype.parse=function(a){this.parseFullHeader(a),this.extended_language=a.readString(this.size-this.hdr_size)},BoxParser.elstBox.prototype.parse=function(a){this.parseFullHeader(a),this.entries=[];for(var b=a.readUint32(),c=0;b>c;c++){var d={};this.entries.push(d),1===this.version?(d.segment_duration=a.readUint64(),d.media_time=a.readInt64()):(d.segment_duration=a.readUint32(),d.media_time=a.readInt32()),d.media_rate_integer=a.readInt16(),d.media_rate_fraction=a.readInt16()}},BoxParser.emsgBox.prototype.parse=function(a){this.parseFullHeader(a),this.scheme_id_uri=a.readCString(),this.value=a.readCString(),this.timescale=a.readUint32(),this.presentation_time_delta=a.readUint32(),this.event_duration=a.readUint32(),this.id=a.readUint32();var b=this.size-this.hdr_size-(16+(this.scheme_id_uri.length+1)+(this.value.length+1));this.message_data=a.readUint8Array(b)},BoxParser.esdsBox.prototype.parse=function(a){this.parseFullHeader(a);var b=a.readUint8Array(this.size-this.hdr_size);if("undefined"!=typeof MPEG4DescriptorParser){var c=new MPEG4DescriptorParser;this.esd=c.parseOneDescriptor(new DataStream(b.buffer,0,DataStream.BIG_ENDIAN))}},BoxParser.frmaBox.prototype.parse=function(a){this.data_format=a.readString(4)},BoxParser.ftypBox.prototype.parse=function(a){var b=this.size-this.hdr_size;this.major_brand=a.readString(4),this.minor_version=a.readUint32(),b-=8,this.compatible_brands=[];for(var c=0;b>=4;)this.compatible_brands[c]=a.readString(4),b-=4,c++},BoxParser.hdlrBox.prototype.parse=function(a){this.parseFullHeader(a),0===this.version&&(a.readUint32(),this.handler=a.readString(4),a.readUint32Array(3),this.name=a.readString(this.size-this.hdr_size-20),"\x00"===this.name[this.name.length-1]&&(this.name=this.name.slice(0,-1)))},BoxParser.hvcCBox.prototype.parse=function(a){var b,c,d,e;this.configurationVersion=a.readUint8(),e=a.readUint8(),this.general_profile_space=e>>6,this.general_tier_flag=(32&e)>>5,this.general_profile_idc=31&e,this.general_profile_compatibility=a.readUint32(),this.general_constraint_indicator=a.readUint8Array(6),this.general_level_idc=a.readUint8(),this.min_spatial_segmentation_idc=4095&a.readUint16(),this.parallelismType=3&a.readUint8(),this.chromaFormat=3&a.readUint8(),this.bitDepthLumaMinus8=7&a.readUint8(),this.bitDepthChromaMinus8=7&a.readUint8(),this.avgFrameRate=a.readUint16(),e=a.readUint8(),this.constantFrameRate=e>>6,this.numTemporalLayers=(13&e)>>3,this.temporalIdNested=(4&e)>>2,this.lengthSizeMinusOne=3&e,this.nalu_arrays=[];var f=a.readUint8();for(b=0;f>b;b++){var g=[];this.nalu_arrays.push(g),e=a.readUint8(),g.completeness=(128&e)>>7,g.nalu_type=63&e;var h=a.readUint16();for(c=0;h>c;c++){var i={};g.push(i),d=a.readUint16(),i.data=a.readUint8Array(d)}}},BoxParser.iinfBox.prototype.parse=function(a){var b;this.parseFullHeader(a),0===this.version?this.entry_count=a.readUint16():this.entry_count=a.readUint32(),this.item_infos=[];for(var c=0;c<this.entry_count;c++){if(b=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;"infe"!==b.box.type&&Log.error("BoxParser","Expected 'infe' box, got "+b.box.type),this.item_infos[c]=b.box}},BoxParser.ilocBox.prototype.parse=function(a){var b;this.parseFullHeader(a),b=a.readUint8(),this.offset_size=b>>4&15,this.length_size=15&b,b=a.readUint8(),this.base_offset_size=b>>4&15,1===this.version||2===this.version?this.index_size=15&b:this.index_size=0,this.items=[];var c=0;if(this.version<2)c=a.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";c=a.readUint32()}for(var d=0;c>d;d++){var e={};if(this.items.push(e),this.version<2)e.item_ID=a.readUint16();else{if(2!==this.version)throw"version of iloc box not supported";e.item_ID=a.readUint16()}switch(1!==this.version&&2!==this.version||(e.construction_method=15&a.readUint16()),e.data_reference_index=a.readUint16(),this.base_offset_size){case 0:e.base_offset=0;break;case 4:e.base_offset=a.readUint32();break;case 8:e.base_offset=a.readUint64();break;default:throw"Error reading base offset size"}var f=a.readUint16();e.extents=[];for(var g=0;f>g;g++){var h={};if(e.extents.push(h),1===this.version||2===this.version)switch(this.index_size){case 0:h.extent_index=0;break;case 4:h.extent_index=a.readUint32();break;case 8:h.extent_index=a.readUint64();break;default:throw"Error reading extent index"}switch(this.offset_size){case 0:h.extent_offset=0;break;case 4:h.extent_offset=a.readUint32();break;case 8:h.extent_offset=a.readUint64();break;default:throw"Error reading extent index"}switch(this.length_size){case 0:h.extent_length=0;break;case 4:h.extent_length=a.readUint32();break;case 8:h.extent_length=a.readUint64();break;default:throw"Error reading extent index"}}}},BoxParser.infeBox.prototype.parse=function(a){return this.parseFullHeader(a),0!==this.version&&1!==this.version||(this.item_ID=a.readUint16(),this.item_protection_index=a.readUint16(),this.item_name=a.readCString(),this.content_type=a.readCString(),this.content_encoding=a.readCString()),1===this.version?(this.extension_type=a.readString(4),Log.warn("BoxParser","Cannot parse extension type"),void a.seek(this.start+this.size)):void(this.version>=2&&(2===this.version?this.item_ID=a.readUint16():3===this.version&&(this.item_ID=a.readUint32()),this.item_protection_index=a.readUint16(),this.item_type=a.readString(4),this.name=a.readCString(),"mime"===this.item_type?(this.content_type=a.readCString(),this.content_encoding=a.readCString()):"uri "===this.item_type&&(this.item_uri_type=a.readCString())))},BoxParser.irefBox=function(a){BoxParser.FullBox.call(this,"iref",a),this.references=[]},BoxParser.irefBox.prototype=new BoxParser.FullBox,BoxParser.irefBox.prototype.parse=function(a){var b,c;for(this.parseFullHeader(a);a.getPosition()<this.start+this.size;){if(b=BoxParser.parseOneBox(a,!0,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;c=0===this.version?new BoxParser.SingleItemTypeReferenceBox(b.type,b.size,b.hdr_size,b.start):new BoxParser.SingleItemTypeReferenceBoxLarge(b.type,b.size,b.hdr_size,b.start),c.write===BoxParser.Box.prototype.write&&"mdat"!==c.type&&(Log.warn("BoxParser",c.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),c.parseDataAndRewind(a)),c.parse(a),this.references.push(c)}},BoxParser.kindBox=function(a){BoxParser.FullBox.call(this,"kind",a),this.schemeURI="",this.value=""},BoxParser.kindBox.prototype=new BoxParser.FullBox,BoxParser.kindBox.prototype.parse=function(a){this.parseFullHeader(a),this.schemeURI=a.readCString(),this.value=a.readCString()},BoxParser.levaBox.prototype.parse=function(a){this.parseFullHeader(a);var b=a.readUint8();this.levels=[];for(var c=0;b>c;c++){var d={};this.levels[c]=d,d.track_ID=a.readUint32();var e=a.readUint8();switch(d.padding_flag=e>>7,d.assignment_type=127&e,d.assignment_type){case 0:d.grouping_type=a.readUint32();break;case 1:d.grouping_type=a.readUint32(),d.grouping_type_parameter=a.readUint32();break;case 2:break;case 3:break;case 4:d.sub_track_id=a.readUint32();break;default:Log.warn("BoxParser","Unknown leva assignement type")}}},BoxParser.maxrBox.prototype.parse=function(a){this.period=a.readUint32(),this.bytes=a.readUint32()},BoxParser.mdhdBox.prototype.parse=function(a){this.parseFullHeader(a),1==this.version?(this.creation_time=a.readUint64(),this.modification_time=a.readUint64(),this.timescale=a.readUint32(),this.duration=a.readUint64()):(this.creation_time=a.readUint32(),this.modification_time=a.readUint32(),this.timescale=a.readUint32(),this.duration=a.readUint32()),this.parseLanguage(a),a.readUint16()},BoxParser.mehdBox.prototype.parse=function(a){this.parseFullHeader(a),1|this.flags&&(Log.warn("BoxParser","mehd box incorrectly uses flags set to 1, converting version to 1"),this.version=1),1==this.version?this.fragment_duration=a.readUint64():this.fragment_duration=a.readUint32()},BoxParser.metaBox.prototype.parse=function(a){this.parseFullHeader(a),this.boxes=[],BoxParser.ContainerBox.prototype.parse.call(this,a)},BoxParser.mfhdBox.prototype.parse=function(a){this.parseFullHeader(a),this.sequence_number=a.readUint32()},BoxParser.mfroBox.prototype.parse=function(a){this.parseFullHeader(a),this._size=a.readUint32()},BoxParser.mvhdBox.prototype.parse=function(a){this.flags=0,this.parseFullHeader(a),1==this.version?(this.creation_time=a.readUint64(),this.modification_time=a.readUint64(),this.timescale=a.readUint32(),this.duration=a.readUint64()):(this.creation_time=a.readUint32(),this.modification_time=a.readUint32(),this.timescale=a.readUint32(),this.duration=a.readUint32()),this.rate=a.readUint32(),this.volume=a.readUint16()>>8,a.readUint16(),a.readUint32Array(2),this.matrix=a.readUint32Array(9),a.readUint32Array(6),this.next_track_id=a.readUint32()},BoxParser.npckBox.prototype.parse=function(a){this.packetssent=a.readUint32()},BoxParser.numpBox.prototype.parse=function(a){this.packetssent=a.readUint64()},BoxParser.padbBox.prototype.parse=function(a){this.parseFullHeader(a);var b=a.readUint32();this.padbits=[];for(var c=0;c<Math.floor((b+1)/2);c++)this.padbits=a.readUint8()},BoxParser.paylBox.prototype.parse=function(a){this.text=a.readString(this.size-this.hdr_size)},BoxParser.paytBox.prototype.parse=function(a){this.payloadID=a.readUint32();var b=a.readUint8();this.rtpmap_string=a.readString(b)},BoxParser.pdinBox.prototype.parse=function(a){this.parseFullHeader(a);var b=(this.size-this.hdr_size)/8;this.rate=[],this.initial_delay=[];for(var c=0;b>c;c++)this.rate[c]=a.readUint32(),this.initial_delay[c]=a.readUint32()},BoxParser.pitmBox.prototype.parse=function(a){this.parseFullHeader(a),0===this.version?this.item_id=a.readUint16():this.item_id=a.readUint32()},BoxParser.pmaxBox.prototype.parse=function(a){this.bytes=a.readUint32()},BoxParser.prftBox.prototype.parse=function(a){this.parseFullHeader(a),this.ref_track_id=a.readUint32(),this.ntp_timestamp=a.readUint64(),0===this.version?this.media_time=a.readUint32():this.media_time=a.readUint64()},BoxParser.psshBox.prototype.parse=function(a){if(this.parseFullHeader(a),this.system_id=a.readUint8Array(16),this.version>0){var b=a.readUint32();this.kid=[];for(var c=0;b>c;c++)this.kid[c]=a.readUint8Array(16)}var d=a.readUint32();d>0&&(this.data=a.readUint8Array(d))},BoxParser["rtp Box"].prototype.parse=function(a){this.descriptionformat=a.readString(4),this.sdptext=a.readString(this.size-this.hdr_size-4)},BoxParser.saioBox.prototype.parse=function(a){this.parseFullHeader(a),1&this.flags&&(this.aux_info_type=a.readUint32(),this.aux_info_type_parameter=a.readUint32());var b=a.readUint32();this.offset=[];for(var c=0;b>c;c++)0===this.version?this.offset[c]=a.readUint32():this.offset[c]=a.readUint64()},BoxParser.saizBox.prototype.parse=function(a){this.parseFullHeader(a),1&this.flags&&(this.aux_info_type=a.readUint32(),this.aux_info_type_parameter=a.readUint32()),this.default_sample_info_size=a.readUint8();var b=a.readUint32();if(this.sample_info_size=[],0===this.default_sample_info_size)for(var c=0;b>c;c++)this.sample_info_size[c]=a.readUint8()},BoxParser.mettSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.content_encoding=a.readCString(),this.mime_format=a.readCString(),this.parseFooter(a)},BoxParser.metxSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.content_encoding=a.readCString(),this.namespace=a.readCString(),this.schema_location=a.readCString(),this.parseFooter(a)},BoxParser.SampleEntry.prototype.parseHeader=function(a){a.readUint8Array(6),this.data_reference_index=a.readUint16(),this.hdr_size+=8},BoxParser.SampleEntry.prototype.parse=function(a){this.parseHeader(a),this.data=a.readUint8Array(this.size-this.hdr_size)},BoxParser.SampleEntry.prototype.parseDataAndRewind=function(a){this.parseHeader(a),this.data=a.readUint8Array(this.size-this.hdr_size),this.hdr_size-=8,a.position-=this.size-this.hdr_size},BoxParser.SampleEntry.prototype.parseFooter=function(a){for(var b,c;a.getPosition()<this.start+this.size;){if(b=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;c=b.box,this.boxes.push(c),this[c.type]=c}},BoxParser.VisualSampleEntry.prototype.parse=function(a){this.parseHeader(a),a.readUint16(),a.readUint16(),a.readUint32Array(3),this.width=a.readUint16(),this.height=a.readUint16(),this.horizresolution=a.readUint32(),this.vertresolution=a.readUint32(),a.readUint32(),this.frame_count=a.readUint16(),this.compressorname=a.readString(32),this.depth=a.readUint16(),a.readUint16(),this.parseFooter(a)},BoxParser.AudioSampleEntry.prototype.parse=function(a){this.parseHeader(a),a.readUint32Array(2),this.channel_count=a.readUint16(),this.samplesize=a.readUint16(),a.readUint16(),a.readUint16(),this.samplerate=a.readUint32()/65536,this.parseFooter(a)},BoxParser.sbttSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.content_encoding=a.readCString(),this.mime_format=a.readCString(),this.parseFooter(a)},BoxParser.stppSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.namespace=a.readCString(),this.schema_location=a.readCString(),this.auxiliary_mime_types=a.readCString(),this.parseFooter(a)},BoxParser.stxtSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.content_encoding=a.readCString(),this.mime_format=a.readCString(),this.parseFooter(a)},BoxParser.tx3gSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.displayFlags=a.readUint32(),this.horizontal_justification=a.readInt8(),this.vertical_justification=a.readInt8(),this.bg_color_rgba=a.readUint8Array(4),this.box_record=a.readInt16Array(4),this.style_record=a.readUint8Array(12),this.parseFooter(a)},BoxParser.wvttSampleEntry.prototype.parse=function(a){this.parseHeader(a),this.parseFooter(a)},BoxParser.alstSampleGroupEntry.prototype.parse=function(a){var b,c=a.readUint16();for(this.first_output_sample=a.readUint16(),this.sample_offset=[],b=0;c>b;b++)this.sample_offset[b]=a.readUint32();var d=this.description_length-4-4*c;for(this.num_output_samples=[],this.num_total_samples=[],b=0;d/4>b;b++)this.num_output_samples[b]=a.readUint16(),this.num_total_samples[b]=a.readUint16()},BoxParser.avllSampleGroupEntry.prototype.parse=function(a){this.layerNumber=a.readUint8(),this.accurateStatisticsFlag=a.readUint8(),this.avgBitRate=a.readUint16(),this.avgFrameRate=a.readUint16()},BoxParser.avssSampleGroupEntry.prototype.parse=function(a){this.subSequenceIdentifier=a.readUint16(),this.layerNumber=a.readUint8();var b=a.readUint8();this.durationFlag=b>>7,this.avgRateFlag=b>>6&1,this.durationFlag&&(this.duration=a.readUint32()),this.avgRateFlag&&(this.accurateStatisticsFlag=a.readUint8(),this.avgBitRate=a.readUint16(),this.avgFrameRate=a.readUint16()),this.dependency=[];for(var c=a.readUint8(),d=0;c>d;d++){var e={};this.dependency.push(e),e.subSeqDirectionFlag=a.readUint8(),e.layerNumber=a.readUint8(),e.subSequenceIdentifier=a.readUint16()}},BoxParser.dtrtSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.mvifSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.prolSampleGroupEntry.prototype.parse=function(a){this.roll_distance=a.readInt16()},BoxParser["rap SampleGroupEntry"].prototype.parse=function(a){var b=a.readUint8();this.num_leading_samples_known=b>>7,this.num_leading_samples=127&b},BoxParser.rashSampleGroupEntry.prototype.parse=function(a){if(this.operation_point_count=a.readUint16(),this.description_length!==2+(1===this.operation_point_count?2:6*this.operation_point_count)+9)Log.warn("BoxParser","Mismatch in "+this.grouping_type+" sample group length"),this.data=a.readUint8Array(this.description_length-2);else{if(1===this.operation_point_count)this.target_rate_share=a.readUint16();else{this.target_rate_share=[],this.available_bitrate=[];for(var b=0;b<this.operation_point_count;b++)this.available_bitrate[b]=a.readUint32(),this.target_rate_share[b]=a.readUint16()}this.maximum_bitrate=a.readUint32(),this.minimum_bitrate=a.readUint32(),this.discard_priority=a.readUint8()}},BoxParser.rollSampleGroupEntry.prototype.parse=function(a){this.roll_distance=a.readInt16()},BoxParser.SampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Unknown Sample Group type: "+this.grouping_type),this.data=a.readUint8Array(this.description_length)},BoxParser.scifSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.scnmSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.stsaSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.syncSampleGroupEntry.prototype.parse=function(a){var b=a.readUint8();this.NAL_unit_type=63&b},BoxParser.teleSampleGroupEntry.prototype.parse=function(a){var b=a.readUint8();this.level_independently_decodable=b>>7},BoxParser.tsasSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.tsclSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.viprSampleGroupEntry.prototype.parse=function(a){Log.warn("BoxParser","Sample Group type: "+this.grouping_type+" not fully parsed")},BoxParser.sbgpBox.prototype.parse=function(a){this.parseFullHeader(a),this.grouping_type=a.readString(4),1===this.version?this.grouping_type_parameter=a.readUint32():this.grouping_type_parameter=0,this.entries=[];for(var b=a.readUint32(),c=0;b>c;c++){var d={};this.entries.push(d),d.sample_count=a.readInt32(),d.group_description_index=a.readInt32()}},BoxParser.schmBox.prototype.parse=function(a){this.parseFullHeader(a),this.scheme_type=a.readString(4),this.scheme_version=a.readUint32(),1&this.flags&&(this.scheme_uri=a.readString(this.size-this.hdr_size-8))},BoxParser["sdp Box"].prototype.parse=function(a){this.sdptext=a.readString(this.size-this.hdr_size)},BoxParser.sdtpBox.prototype.parse=function(a){var b;this.parseFullHeader(a);var c=this.size-this.hdr_size;this.is_leading=[],this.sample_depends_on=[],this.sample_is_depended_on=[],this.sample_has_redundancy=[];for(var d=0;c>d;d++)b=a.readUint8(),this.is_leading[d]=b>>6,this.sample_depends_on[d]=b>>4&3,this.sample_is_depended_on[d]=b>>2&3,this.sample_has_redundancy[d]=3&b},BoxParser.sgpdBox.prototype.parse=function(a){this.parseFullHeader(a),this.grouping_type=a.readString(4),Log.debug("BoxParser","Found Sample Groups of type "+this.grouping_type),1===this.version?this.default_length=a.readUint32():this.default_length=0,this.version>=2&&(this.default_group_description_index=a.readUint32()),this.entries=[];for(var b=a.readUint32(),c=0;b>c;c++){var d;d=BoxParser[this.grouping_type+"SampleGroupEntry"]?new BoxParser[this.grouping_type+"SampleGroupEntry"](this.grouping_type):new BoxParser.SampleGroupEntry(this.grouping_type),this.entries.push(d),1===this.version&&0===this.default_length?d.description_length=a.readUint32():d.description_length=this.default_length,d.write===BoxParser.SampleGroupEntry.prototype.write&&(Log.warn("BoxParser"," SampleEntry for type "+this.grouping_type+" writing not yet implemented, keeping unparsed data in memory for later write"),d.data=a.readUint8Array(d.description_length),a.position-=d.description_length),d.parse(a)}},BoxParser.sidxBox.prototype.parse=function(a){this.parseFullHeader(a),this.reference_ID=a.readUint32(),this.timescale=a.readUint32(),0===this.version?(this.earliest_presentation_time=a.readUint32(),this.first_offset=a.readUint32()):(this.earliest_presentation_time=a.readUint64(),this.first_offset=a.readUint64()),a.readUint16(),this.references=[];for(var b=a.readUint16(),c=0;b>c;c++){var d={};this.references.push(d);var e=a.readUint32();d.reference_type=e>>31&1,d.referenced_size=2147483647&e,d.subsegment_duration=a.readUint32(),e=a.readUint32(),d.starts_with_SAP=e>>31&1,d.SAP_type=e>>28&7,d.SAP_delta_time=268435455&e}},BoxParser.SingleItemTypeReferenceBox=function(a,b,c,d){BoxParser.Box.call(this,a,b),this.hdr_size=c,this.start=d},BoxParser.SingleItemTypeReferenceBox.prototype=new BoxParser.Box,BoxParser.SingleItemTypeReferenceBox.prototype.parse=function(a){this.from_item_ID=a.readUint16();var b=a.readUint16();this.references=[];for(var c=0;b>c;c++)this.references[c]=a.readUint16()},BoxParser.SingleItemTypeReferenceBoxLarge=function(a,b,c,d){BoxParser.Box.call(this,a,b),this.hdr_size=c,this.start=d},BoxParser.SingleItemTypeReferenceBoxLarge.prototype=new BoxParser.Box,BoxParser.SingleItemTypeReferenceBoxLarge.prototype.parse=function(a){this.from_item_ID=a.readUint32();var b=a.readUint16();this.references=[];for(var c=0;b>c;c++)this.references[c]=a.readUint32()},BoxParser.ssixBox.prototype.parse=function(a){this.parseFullHeader(a),this.subsegments=[];for(var b=a.readUint32(),c=0;b>c;c++){var d={};this.subsegments.push(d),d.ranges=[];for(var e=a.readUint32(),f=0;e>f;f++){var g={};d.ranges.push(g),g.level=a.readUint8(),g.range_size=a.readUint24()}}},BoxParser.stcoBox.prototype.parse=function(a){var b;this.parseFullHeader(a),b=a.readUint32(),0===this.version&&(this.chunk_offsets=a.readUint32Array(b))},BoxParser.stdpBox.prototype.parse=function(a){
this.parseFullHeader(a);var b=(this.size-this.hdr_size)/2;this.priority=[];for(var c=0;b>c;c++)this.priority[c]=a.readUint16()},BoxParser.striBox.prototype.parse=function(a){this.parseFullHeader(a),this.switch_group=a.readUint16(),this.alternate_group=a.readUint16(),this.sub_track_id=a.readUint32();var b=(this.size-this.hdr_size-8)/4;this.attribute_list=[];for(var c=0;b>c;c++)this.attribute_list[c]=a.readUint32()},BoxParser.stscBox.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),b=a.readUint32(),this.first_chunk=[],this.samples_per_chunk=[],this.sample_description_index=[],0===this.version)for(c=0;b>c;c++)this.first_chunk.push(a.readUint32()),this.samples_per_chunk.push(a.readUint32()),this.sample_description_index.push(a.readUint32())},BoxParser.stsdBox=function(a){BoxParser.FullBox.call(this,"stsd",a),this.entries=[]},BoxParser.stsdBox.prototype=new BoxParser.FullBox,BoxParser.stsdBox.prototype.parse=function(a){var b,c,d,e;for(this.parseFullHeader(a),d=a.readUint32(),b=1;d>=b;b++){if(c=BoxParser.parseOneBox(a,!0,this.size-(a.getPosition()-this.start)),c.code!==BoxParser.OK)return;BoxParser[c.type+"SampleEntry"]?(e=new BoxParser[c.type+"SampleEntry"](c.size),e.hdr_size=c.hdr_size,e.start=c.start):(Log.warn("BoxParser","Unknown sample entry type: "+c.type),e=new BoxParser.SampleEntry(c.type,c.size,c.hdr_size,c.start)),e.write===BoxParser.SampleEntry.prototype.write&&(Log.warn("BoxParser",e.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),e.parseDataAndRewind(a)),e.parse(a),this.entries.push(e)}},BoxParser.stsgBox.prototype.parse=function(a){this.parseFullHeader(a),this.grouping_type=a.readUint32();var b=a.readUint16();this.group_description_index=[];for(var c=0;b>c;c++)this.group_description_index[c]=a.readUint32()},BoxParser.stshBox.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),b=a.readUint32(),this.shadowed_sample_numbers=[],this.sync_sample_numbers=[],0===this.version)for(c=0;b>c;c++)this.shadowed_sample_numbers.push(a.readUint32()),this.sync_sample_numbers.push(a.readUint32())},BoxParser.stssBox.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),c=a.readUint32(),0===this.version)for(this.sample_numbers=[],b=0;c>b;b++)this.sample_numbers.push(a.readUint32())},BoxParser.stszBox.prototype.parse=function(a){var b;if(this.parseFullHeader(a),this.sample_sizes=[],0===this.version)if(this.sample_size=a.readUint32(),this.sample_count=a.readUint32(),0===this.sample_size)this.sample_sizes=a.readUint32Array(this.sample_count);else for(b=0;b<this.sample_count;b++)this.sample_sizes[b]=this.sample_size},BoxParser.sttsBox.prototype.parse=function(a){var b,c,d;if(this.parseFullHeader(a),b=a.readUint32(),this.sample_counts=[],this.sample_deltas=[],0===this.version)for(c=0;b>c;c++)this.sample_counts.push(a.readUint32()),d=a.readInt32(),0>d&&(Log.warn("BoxParser","File uses negative stts sample delta, using value 1 instead, sync may be lost!"),d=1),this.sample_deltas.push(d)},BoxParser.stviBox.prototype.parse=function(a){this.parseFullHeader(a);var b=a.readUint32();this.single_view_allowed=3&b,this.stereo_scheme=a.readUint32();var c=a.readUint32();this.stereo_indication_type=a.readString(c);var d,e;for(this.boxes=[];a.getPosition()<this.start+this.size;){if(d=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),d.code!==BoxParser.OK)return;e=d.box,this.boxes.push(e),this[e.type]=e}},BoxParser.stypBox.prototype.parse=function(a){BoxParser.ftypBox.prototype.parse.call(this,a)},BoxParser.stz2Box.prototype.parse=function(a){var b,c;if(this.parseFullHeader(a),this.sample_sizes=[],0===this.version)if(this.reserved=a.readUint24(),this.field_size=a.readUint8(),c=a.readUint32(),4===this.field_size)for(b=0;c>b;b+=2){var d=a.readUint8();this.sample_sizes[b]=d>>4&15,this.sample_sizes[b+1]=15&d}else if(8===this.field_size)for(b=0;c>b;b++)this.sample_sizes[b]=a.readUint8();else if(16===this.field_size)for(b=0;c>b;b++)this.sample_sizes[b]=a.readUint16();else Log.error("BoxParser","Error in length field in stz2 box")},BoxParser.subsBox.prototype.parse=function(a){var b,c,d,e;for(this.parseFullHeader(a),d=a.readUint32(),this.entries=[],b=0;d>b;b++){var f={};if(this.entries[b]=f,f.sample_delta=a.readUint32(),f.subsamples=[],e=a.readUint16(),e>0)for(c=0;e>c;c++){var g={};f.subsamples.push(g),1==this.version?g.size=a.readUint32():g.size=a.readUint16(),g.priority=a.readUint8(),g.discardable=a.readUint8(),g.codec_specific_parameters=a.readUint32()}}},BoxParser.tfdtBox.prototype.parse=function(a){this.parseFullHeader(a),1==this.version?this.baseMediaDecodeTime=a.readUint64():this.baseMediaDecodeTime=a.readUint32()},BoxParser.tfhdBox.prototype.parse=function(a){var b=0;this.parseFullHeader(a),this.track_id=a.readUint32(),this.size-this.hdr_size>b&&this.flags&BoxParser.TFHD_FLAG_BASE_DATA_OFFSET?(this.base_data_offset=a.readUint64(),b+=8):this.base_data_offset=0,this.size-this.hdr_size>b&&this.flags&BoxParser.TFHD_FLAG_SAMPLE_DESC?(this.default_sample_description_index=a.readUint32(),b+=4):this.default_sample_description_index=0,this.size-this.hdr_size>b&&this.flags&BoxParser.TFHD_FLAG_SAMPLE_DUR?(this.default_sample_duration=a.readUint32(),b+=4):this.default_sample_duration=0,this.size-this.hdr_size>b&&this.flags&BoxParser.TFHD_FLAG_SAMPLE_SIZE?(this.default_sample_size=a.readUint32(),b+=4):this.default_sample_size=0,this.size-this.hdr_size>b&&this.flags&BoxParser.TFHD_FLAG_SAMPLE_FLAGS?(this.default_sample_flags=a.readUint32(),b+=4):this.default_sample_flags=0},BoxParser.tfraBox.prototype.parse=function(a){this.parseFullHeader(a),this.track_ID=a.readUint32(),a.readUint24();var b=a.readUint8();this.length_size_of_traf_num=b>>4&3,this.length_size_of_trun_num=b>>2&3,this.length_size_of_sample_num=3&b,this.entries=[];for(var c=a.readUint32(),d=0;c>d;d++)1===this.version?(this.time=a.readUint64(),this.moof_offset=a.readUint64()):(this.time=a.readUint32(),this.moof_offset=a.readUint32()),this.traf_number=a["readUint"+8*(this.length_size_of_traf_num+1)](),this.trun_number=a["readUint"+8*(this.length_size_of_trun_num+1)](),this.sample_number=a["readUint"+8*(this.length_size_of_sample_num+1)]()},BoxParser.tkhdBox.prototype.parse=function(a){this.parseFullHeader(a),1==this.version?(this.creation_time=a.readUint64(),this.modification_time=a.readUint64(),this.track_id=a.readUint32(),a.readUint32(),this.duration=a.readUint64()):(this.creation_time=a.readUint32(),this.modification_time=a.readUint32(),this.track_id=a.readUint32(),a.readUint32(),this.duration=a.readUint32()),a.readUint32Array(2),this.layer=a.readInt16(),this.alternate_group=a.readInt16(),this.volume=a.readInt16()>>8,a.readUint16(),this.matrix=a.readInt32Array(9),this.width=a.readUint32(),this.height=a.readUint32()},BoxParser.tmaxBox.prototype.parse=function(a){this.time=a.readUint32()},BoxParser.tminBox.prototype.parse=function(a){this.time=a.readUint32()},BoxParser.totlBox.prototype.parse=function(a){this.bytessent=a.readUint32()},BoxParser.tpayBox.prototype.parse=function(a){this.bytessent=a.readUint32()},BoxParser.tpylBox.prototype.parse=function(a){this.bytessent=a.readUint64()},BoxParser.trefBox.prototype.parse=function(a){for(var b,c;a.getPosition()<this.start+this.size;){if(b=BoxParser.parseOneBox(a,!0,this.size-(a.getPosition()-this.start)),b.code!==BoxParser.OK)return;c=new BoxParser.TrackReferenceTypeBox(b.type,b.size,b.hdr_size,b.start),c.write===BoxParser.Box.prototype.write&&"mdat"!==c.type&&(Log.warn("BoxParser",c.type+" box writing not yet implemented, keeping unparsed data in memory for later write"),c.parseDataAndRewind(a)),c.parse(a),this.boxes.push(c)}},BoxParser.trepBox.prototype.parse=function(a){for(this.parseFullHeader(a),this.track_ID=a.readUint32(),this.boxes=[];a.getPosition()<this.start+this.size;){if(ret=BoxParser.parseOneBox(a,!1,this.size-(a.getPosition()-this.start)),ret.code!==BoxParser.OK)return;box=ret.box,this.boxes.push(box)}},BoxParser.trexBox.prototype.parse=function(a){this.parseFullHeader(a),this.track_id=a.readUint32(),this.default_sample_description_index=a.readUint32(),this.default_sample_duration=a.readUint32(),this.default_sample_size=a.readUint32(),this.default_sample_flags=a.readUint32()},BoxParser.trpyBox.prototype.parse=function(a){this.bytessent=a.readUint64()},BoxParser.trunBox.prototype.parse=function(a){var b=0;if(this.parseFullHeader(a),this.sample_count=a.readUint32(),b+=4,this.size-this.hdr_size>b&&this.flags&BoxParser.TRUN_FLAGS_DATA_OFFSET?(this.data_offset=a.readInt32(),b+=4):this.data_offset=0,this.size-this.hdr_size>b&&this.flags&BoxParser.TRUN_FLAGS_FIRST_FLAG?(this.first_sample_flags=a.readUint32(),b+=4):this.first_sample_flags=0,this.sample_duration=[],this.sample_size=[],this.sample_flags=[],this.sample_composition_time_offset=[],this.size-this.hdr_size>b)for(var c=0;c<this.sample_count;c++)this.flags&BoxParser.TRUN_FLAGS_DURATION&&(this.sample_duration[c]=a.readUint32()),this.flags&BoxParser.TRUN_FLAGS_SIZE&&(this.sample_size[c]=a.readUint32()),this.flags&BoxParser.TRUN_FLAGS_FLAGS&&(this.sample_flags[c]=a.readUint32()),this.flags&BoxParser.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?this.sample_composition_time_offset[c]=a.readUint32():this.sample_composition_time_offset[c]=a.readInt32())},BoxParser.tselBox.prototype.parse=function(a){this.parseFullHeader(a),this.switch_group=a.readUint32();var b=(this.size-this.hdr_size-4)/4;this.attribute_list=[];for(var c=0;b>c;c++)this.attribute_list[c]=a.readUint32()},BoxParser.txtCBox.prototype.parse=function(a){this.parseFullHeader(a),this.config=a.readCString()},BoxParser["url Box"].prototype.parse=function(a){this.parseFullHeader(a),1!==this.flags&&(this.location=a.readCString())},BoxParser["urn Box"].prototype.parse=function(a){this.parseFullHeader(a),this.name=a.readCString(),this.size-this.hdr_size-this.name.length-1>0&&(this.location=a.readCString())},BoxParser.vmhdBox.prototype.parse=function(a){this.parseFullHeader(a),this.graphicsmode=a.readUint16(),this.opcolor=a.readUint16Array(3)},BoxParser.vttCBox.prototype.parse=function(a){this.text=a.readString(this.size-this.hdr_size)},BoxParser.Box.prototype.writeHeader=function(a,b){this.size+=8,this.size>MAX_SIZE&&(this.size+=8),"uuid"===this.type&&(this.size+=16),Log.debug("BoxWriter","Writing box "+this.type+" of size: "+this.size+" at position "+a.getPosition()+(b||"")),this.size>MAX_SIZE?a.writeUint32(1):(this.sizePosition=a.getPosition(),a.writeUint32(this.size)),a.writeString(this.type,null,4),"uuid"===this.type&&a.writeUint8Array(this.uuid),this.size>MAX_SIZE&&a.writeUint64(this.size)},BoxParser.FullBox.prototype.writeHeader=function(a){this.size+=4,BoxParser.Box.prototype.writeHeader.call(this,a," v="+this.version+" f="+this.flags),a.writeUint8(this.version),a.writeUint24(this.flags)},BoxParser.Box.prototype.write=function(a){"mdat"===this.type?this.data&&(this.size=this.data.length,this.writeHeader(a),a.writeUint8Array(this.data)):(this.size=this.data?this.data.length:0,this.writeHeader(a),this.data&&a.writeUint8Array(this.data))},BoxParser.ContainerBox.prototype.write=function(a){this.size=0,this.writeHeader(a);for(var b=0;b<this.boxes.length;b++)this.boxes[b]&&(this.boxes[b].write(a),this.size+=this.boxes[b].size);Log.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),a.adjustUint32(this.sizePosition,this.size)},BoxParser.TrackReferenceTypeBox.prototype.write=function(a){this.size=4*this.track_ids.length,this.writeHeader(a),a.writeUint32Array(this.track_ids)},BoxParser.avcCBox.prototype.write=function(a){var b;for(this.size=7,b=0;b<this.SPS.length;b++)this.size+=2+this.SPS[b].length;for(b=0;b<this.PPS.length;b++)this.size+=2+this.PPS[b].length;for(this.ext&&(this.size+=this.ext.length),this.writeHeader(a),a.writeUint8(this.configurationVersion),a.writeUint8(this.AVCProfileIndication),a.writeUint8(this.profile_compatibility),a.writeUint8(this.AVCLevelIndication),a.writeUint8(this.lengthSizeMinusOne+252),a.writeUint8(this.SPS.length+224),b=0;b<this.SPS.length;b++)a.writeUint16(this.SPS[b].length),a.writeUint8Array(this.SPS[b]);for(a.writeUint8(this.PPS.length),b=0;b<this.PPS.length;b++)a.writeUint16(this.PPS[b].length),a.writeUint8Array(this.PPS[b]);this.ext&&a.writeUint8Array(this.ext)},BoxParser.co64Box.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=4+8*this.chunk_offsets.length,this.writeHeader(a),a.writeUint32(this.chunk_offsets.length),b=0;b<this.chunk_offsets.length;b++)a.writeUint64(this.chunk_offsets[b])},BoxParser.cslgBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=20,this.writeHeader(a),a.writeInt32(this.compositionToDTSShift),a.writeInt32(this.leastDecodeToDisplayDelta),a.writeInt32(this.greatestDecodeToDisplayDelta),a.writeInt32(this.compositionStartTime),a.writeInt32(this.compositionEndTime)},BoxParser.cttsBox.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(a),a.writeUint32(this.sample_counts.length),b=0;b<this.sample_counts.length;b++)a.writeUint32(this.sample_counts[b]),1===this.version?a.writeInt32(this.sample_offsets[b]):a.writeUint32(this.sample_offsets[b])},BoxParser.drefBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4,this.writeHeader(a),a.writeUint32(this.entries.length);for(var b=0;b<this.entries.length;b++)this.entries[b].write(a),this.size+=this.entries[b].size;Log.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),a.adjustUint32(this.sizePosition,this.size)},BoxParser.elngBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=this.extended_language.length,this.writeHeader(a),a.writeString(this.extended_language)},BoxParser.elstBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4+12*this.entries.length,this.writeHeader(a),a.writeUint32(this.entries.length);for(var b=0;b<this.entries.length;b++){var c=this.entries[b];a.writeUint32(c.segment_duration),a.writeInt32(c.media_time),a.writeInt16(c.media_rate_integer),a.writeInt16(c.media_rate_fraction)}},BoxParser.emsgBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=16+this.message_data.length+(this.scheme_id_uri.length+1)+(this.value.length+1),this.writeHeader(a),a.writeCString(this.scheme_id_uri),a.writeCString(this.value),a.writeUint32(this.timescale),a.writeUint32(this.presentation_time_delta),a.writeUint32(this.event_duration),a.writeUint32(this.id),a.writeUint8Array(this.message_data)},BoxParser.ftypBox.prototype.write=function(a){this.size=8+4*this.compatible_brands.length,this.writeHeader(a),a.writeString(this.major_brand,null,4),a.writeUint32(this.minor_version);for(var b=0;b<this.compatible_brands.length;b++)a.writeString(this.compatible_brands[b],null,4)},BoxParser.hdlrBox.prototype.write=function(a){this.size=20+this.name.length+1,this.version=0,this.flags=0,this.writeHeader(a),a.writeUint32(0),a.writeString(this.handler,null,4),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeCString(this.name)},BoxParser.kindBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=this.schemeURI.length+1+(this.value.length+1),this.writeHeader(a),a.writeCString(this.schemeURI),a.writeCString(this.value)},BoxParser.mdhdBox.prototype.write=function(a){this.size=20,this.flags=0,this.version=0,this.writeHeader(a),a.writeUint32(this.creation_time),a.writeUint32(this.modification_time),a.writeUint32(this.timescale),a.writeUint32(this.duration),a.writeUint16(this.language),a.writeUint16(0)},BoxParser.mehdBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4,this.writeHeader(a),a.writeUint32(this.fragment_duration)},BoxParser.mfhdBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4,this.writeHeader(a),a.writeUint32(this.sequence_number)},BoxParser.mvhdBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=96,this.writeHeader(a),a.writeUint32(this.creation_time),a.writeUint32(this.modification_time),a.writeUint32(this.timescale),a.writeUint32(this.duration),a.writeUint32(this.rate),a.writeUint16(this.volume<<8),a.writeUint16(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32Array(this.matrix),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32(this.next_track_id)},BoxParser.SampleEntry.prototype.writeHeader=function(a){this.size=8,BoxParser.Box.prototype.writeHeader.call(this,a),a.writeUint8(0),a.writeUint8(0),a.writeUint8(0),a.writeUint8(0),a.writeUint8(0),a.writeUint8(0),a.writeUint16(this.data_reference_index)},BoxParser.SampleEntry.prototype.writeFooter=function(a){for(var b=0;b<this.boxes.length;b++)this.boxes[b].write(a),this.size+=this.boxes[b].size;Log.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),a.adjustUint32(this.sizePosition,this.size)},BoxParser.SampleEntry.prototype.write=function(a){this.writeHeader(a),a.writeUint8Array(this.data),this.size+=this.data.length,Log.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),a.adjustUint32(this.sizePosition,this.size)},BoxParser.VisualSampleEntry.prototype.write=function(a){this.writeHeader(a),this.size+=70,a.writeUint16(0),a.writeUint16(0),a.writeUint32(0),a.writeUint32(0),a.writeUint32(0),a.writeUint16(this.width),a.writeUint16(this.height),a.writeUint32(this.horizresolution),a.writeUint32(this.vertresolution),a.writeUint32(0),a.writeUint16(this.frame_count),a.writeString(this.compressorname,null,32),a.writeUint16(this.depth),a.writeInt16(-1),this.writeFooter(a)},BoxParser.AudioSampleEntry.prototype.write=function(a){this.writeHeader(a),this.size+=20,a.writeUint32(0),a.writeUint32(0),a.writeUint16(this.channel_count),a.writeUint16(this.samplesize),a.writeUint16(0),a.writeUint16(0),a.writeUint32(this.samplerate<<16),this.writeFooter(a)},BoxParser.SampleGroupEntry.prototype.write=function(a){a.writeUint8Array(this.data)},BoxParser.sbgpBox.prototype.write=function(a){this.version=1,this.flags=0,this.size=12+8*this.entries.length,this.writeHeader(a),a.writeString(this.grouping_type,null,4),a.writeUint32(this.grouping_type_parameter),a.writeUint32(this.entries.length);for(var b=0;b<this.entries.length;b++){var c=this.entries[b];a.writeInt32(c.sample_count),a.writeInt32(c.group_description_index)}},BoxParser.sgpdBox.prototype.write=function(a){var b,c;for(this.flags=0,this.size=12,b=0;b<this.entries.length;b++)c=this.entries[b],1===this.version&&(0===this.default_length&&(this.size+=4),this.size+=c.data.length);for(this.writeHeader(a),a.writeString(this.grouping_type,null,4),1===this.version&&a.writeUint32(this.default_length),this.version>=2&&a.writeUint32(this.default_sample_description_index),a.writeUint32(this.entries.length),b=0;b<this.entries.length;b++)c=this.entries[b],1===this.version&&0===this.default_length&&a.writeUint32(c.description_length),c.write(a)},BoxParser.sidxBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=20+12*this.references.length,this.writeHeader(a),a.writeUint32(this.reference_ID),a.writeUint32(this.timescale),a.writeUint32(this.earliest_presentation_time),a.writeUint32(this.first_offset),a.writeUint16(0),a.writeUint16(this.references.length);for(var b=0;b<this.references.length;b++){var c=this.references[b];a.writeUint32(c.reference_type<<31|c.referenced_size),a.writeUint32(c.subsegment_duration),a.writeUint32(c.starts_with_SAP<<31|c.SAP_type<<28|c.SAP_delta_time)}},BoxParser.stcoBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4+4*this.chunk_offsets.length,this.writeHeader(a),a.writeUint32(this.chunk_offsets.length),a.writeUint32Array(this.chunk_offsets)},BoxParser.stscBox.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=4+12*this.first_chunk.length,this.writeHeader(a),a.writeUint32(this.first_chunk.length),b=0;b<this.first_chunk.length;b++)a.writeUint32(this.first_chunk[b]),a.writeUint32(this.samples_per_chunk[b]),a.writeUint32(this.sample_description_index[b])},BoxParser.stsdBox.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=0,this.writeHeader(a),a.writeUint32(this.entries.length),this.size+=4,b=0;b<this.entries.length;b++)this.entries[b].write(a),this.size+=this.entries[b].size;Log.debug("BoxWriter","Adjusting box "+this.type+" with new size "+this.size),a.adjustUint32(this.sizePosition,this.size)},BoxParser.stshBox.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=4+8*this.shadowed_sample_numbers.length,this.writeHeader(a),a.writeUint32(this.shadowed_sample_numbers.length),b=0;b<this.shadowed_sample_numbers.length;b++)a.writeUint32(this.shadowed_sample_numbers[b]),a.writeUint32(this.sync_sample_numbers[b])},BoxParser.stssBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4+4*this.sample_numbers.length,this.writeHeader(a),a.writeUint32(this.sample_numbers.length),a.writeUint32Array(this.sample_numbers)},BoxParser.stszBox.prototype.write=function(a){var b,c=!0;if(this.version=0,this.flags=0,this.sample_sizes.length>0)for(b=0;b+1<this.sample_sizes.length;){if(this.sample_sizes[b+1]!==this.sample_sizes[0]){c=!1;break}b++}else c=!1;this.size=8,c||(this.size+=4*this.sample_sizes.length),this.writeHeader(a),c?a.writeUint32(this.sample_sizes[0]):a.writeUint32(0),a.writeUint32(this.sample_sizes.length),c||a.writeUint32Array(this.sample_sizes)},BoxParser.sttsBox.prototype.write=function(a){var b;for(this.version=0,this.flags=0,this.size=4+8*this.sample_counts.length,this.writeHeader(a),a.writeUint32(this.sample_counts.length),b=0;b<this.sample_counts.length;b++)a.writeUint32(this.sample_counts[b]),a.writeUint32(this.sample_deltas[b])},BoxParser.tfdtBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=4,1===this.version&&(this.size+=4),this.writeHeader(a),1===this.version?a.writeUint64(this.baseMediaDecodeTime):a.writeUint32(this.baseMediaDecodeTime)},BoxParser.tfhdBox.prototype.write=function(a){this.version=0,this.size=4,this.flags&BoxParser.TFHD_FLAG_BASE_DATA_OFFSET&&(this.size+=8),this.flags&BoxParser.TFHD_FLAG_SAMPLE_DESC&&(this.size+=4),this.flags&BoxParser.TFHD_FLAG_SAMPLE_DUR&&(this.size+=4),this.flags&BoxParser.TFHD_FLAG_SAMPLE_SIZE&&(this.size+=4),this.flags&BoxParser.TFHD_FLAG_SAMPLE_FLAGS&&(this.size+=4),this.writeHeader(a),a.writeUint32(this.track_id),this.flags&BoxParser.TFHD_FLAG_BASE_DATA_OFFSET&&a.writeUint64(this.base_data_offset),this.flags&BoxParser.TFHD_FLAG_SAMPLE_DESC&&a.writeUint32(this.default_sample_description_index),this.flags&BoxParser.TFHD_FLAG_SAMPLE_DUR&&a.writeUint32(this.default_sample_duration),this.flags&BoxParser.TFHD_FLAG_SAMPLE_SIZE&&a.writeUint32(this.default_sample_size),this.flags&BoxParser.TFHD_FLAG_SAMPLE_FLAGS&&a.writeUint32(this.default_sample_flags)},BoxParser.tkhdBox.prototype.write=function(a){this.version=0,this.size=80,this.writeHeader(a),a.writeUint32(this.creation_time),a.writeUint32(this.modification_time),a.writeUint32(this.track_id),a.writeUint32(0),a.writeUint32(this.duration),a.writeUint32(0),a.writeUint32(0),a.writeInt16(this.layer),a.writeInt16(this.alternate_group),a.writeInt16(this.volume<<8),a.writeUint16(0),a.writeInt32Array(this.matrix),a.writeUint32(this.width),a.writeUint32(this.height)},BoxParser.trexBox.prototype.write=function(a){this.version=0,this.flags=0,this.size=20,this.writeHeader(a),a.writeUint32(this.track_id),a.writeUint32(this.default_sample_description_index),a.writeUint32(this.default_sample_duration),a.writeUint32(this.default_sample_size),a.writeUint32(this.default_sample_flags)},BoxParser.trunBox.prototype.write=function(a){this.version=0,this.size=4,this.flags&BoxParser.TRUN_FLAGS_DATA_OFFSET&&(this.size+=4),this.flags&BoxParser.TRUN_FLAGS_FIRST_FLAG&&(this.size+=4),this.flags&BoxParser.TRUN_FLAGS_DURATION&&(this.size+=4*this.sample_duration.length),this.flags&BoxParser.TRUN_FLAGS_SIZE&&(this.size+=4*this.sample_size.length),this.flags&BoxParser.TRUN_FLAGS_FLAGS&&(this.size+=4*this.sample_flags.length),this.flags&BoxParser.TRUN_FLAGS_CTS_OFFSET&&(this.size+=4*this.sample_composition_time_offset.length),this.writeHeader(a),a.writeUint32(this.sample_count),this.flags&BoxParser.TRUN_FLAGS_DATA_OFFSET&&(this.data_offset_position=a.getPosition(),a.writeInt32(this.data_offset)),this.flags&BoxParser.TRUN_FLAGS_FIRST_FLAG&&a.writeUint32(this.first_sample_flags);for(var b=0;b<this.sample_count;b++)this.flags&BoxParser.TRUN_FLAGS_DURATION&&a.writeUint32(this.sample_duration[b]),this.flags&BoxParser.TRUN_FLAGS_SIZE&&a.writeUint32(this.sample_size[b]),this.flags&BoxParser.TRUN_FLAGS_FLAGS&&a.writeUint32(this.sample_flags[b]),this.flags&BoxParser.TRUN_FLAGS_CTS_OFFSET&&(0===this.version?a.writeUint32(this.sample_composition_time_offset[b]):a.writeInt32(this.sample_composition_time_offset[b]))},BoxParser["url Box"].prototype.write=function(a){this.version=0,this.location?(this.flags=0,this.size=this.location.length+1):(this.flags=1,this.size=0),this.writeHeader(a),this.location&&a.writeCString(this.location)},BoxParser["urn Box"].prototype.write=function(a){this.version=0,this.flags=0,this.size=this.name.length+1+(this.location?this.location.length+1:0),this.writeHeader(a),a.writeCString(this.name),this.location&&a.writeCString(this.location)},BoxParser.vmhdBox.prototype.write=function(a){this.version=0,this.flags=1,this.size=8,this.writeHeader(a),a.writeUint16(this.graphicsmode),a.writeUint16Array(this.opcolor)},BoxParser.cttsBox.prototype.unpack=function(a){var b,c,d;for(d=0,b=0;b<this.sample_counts.length;b++)for(c=0;c<this.sample_counts[b];c++)a[d].pts=a[d].dts+this.sample_offsets[b],d++},BoxParser.sttsBox.prototype.unpack=function(a){var b,c,d;for(d=0,b=0;b<this.sample_counts.length;b++)for(c=0;c<this.sample_counts[b];c++)0===d?a[d].dts=0:a[d].dts=a[d-1].dts+this.sample_deltas[b],d++},BoxParser.stcoBox.prototype.unpack=function(a){var b;for(b=0;b<this.chunk_offsets.length;b++)a[b].offset=this.chunk_offsets[b]},BoxParser.stscBox.prototype.unpack=function(a){var b,c,d,e,f;for(e=0,f=0,b=0;b<this.first_chunk.length;b++)for(c=0;c<(b+1<this.first_chunk.length?this.first_chunk[b+1]:1/0);c++)for(f++,d=0;d<this.samples_per_chunk[b];d++){if(!a[e])return;a[e].description_index=this.sample_description_index[b],a[e].chunk_index=f,e++}},BoxParser.stszBox.prototype.unpack=function(a){var b;for(b=0;b<this.sample_sizes.length;b++)a[b].size=this.sample_sizes[b]};var VTTin4Parser=function(){};VTTin4Parser.prototype.parseSample=function(a){var b,c,d=new MP4BoxStream(a.buffer);for(b=[];!d.isEos();)c=BoxParser.parseOneBox(d,!1),c.code===BoxParser.OK&&"vttc"===c.box.type&&b.push(c.box);return b},VTTin4Parser.prototype.getText=function(a,b,c){function d(a,b,c){return c=c||"0",a+="",a.length>=b?a:new Array(b-a.length+1).join(c)+a}function e(a){var b=Math.floor(a/3600),c=Math.floor((a-3600*b)/60),e=Math.floor(a-3600*b-60*c),f=Math.floor(1e3*(a-3600*b-60*c-e));return""+d(b,2)+":"+d(c,2)+":"+d(e,2)+"."+d(f,3)}for(var f=this.parseSample(c),g="",h=0;h<f.length;h++){var i=f[h];g+=e(a)+" --> "+e(b)+"\r\n",g+=i.payl.text}return g};var XMLSubtitlein4Parser=function(){};XMLSubtitlein4Parser.prototype.parseSample=function(a){var b,c={};c.resources=[];var d=new MP4BoxStream(a.data.buffer);if(a.subsamples&&0!==a.subsamples.length){if(c.documentString=d.readString(a.subsamples[0].size),a.subsamples.length>1)for(b=1;b<a.subsamples.length;b++)c.resources[b]=d.readUint8Array(a.subsamples[b].size)}else c.documentString=d.readString(a.data.length);return c.document=(new DOMParser).parseFromString(c.documentString,"application/xml"),c};var Textin4Parser=function(){};Textin4Parser.prototype.parseSample=function(a){var b,c=new MP4BoxStream(a.data.buffer);return b=c.readString(a.data.length)},Textin4Parser.prototype.parseConfig=function(a){var b,c=new MP4BoxStream(a.buffer);return c.readUint32(),b=c.readCString()};var ISOFile=function(a){this.stream=a,this.boxes=[],this.mdats=[],this.moofs=[],this.isProgressive=!1,this.moovStartFound=!1};ISOFile.prototype.parse=function(){var a,b,c=!1;if(!this.restoreParsePosition||this.restoreParsePosition())for(;;){if(this.hasIncompleteMdat&&this.hasIncompleteMdat()){if(this.processIncompleteMdat())continue;return}if(this.saveParsePosition&&this.saveParsePosition(),a=BoxParser.parseOneBox(this.stream,c),a.code===BoxParser.ERR_NOT_ENOUGH_DATA){if(this.processIncompleteBox){if(this.processIncompleteBox(a))continue;return}return}switch(b=a.box,this.boxes.push(b),b.type){case"mdat":this.mdats.push(b);break;case"moof":this.moofs.push(b);break;case"moov":this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0);default:void 0!==this[b.type]&&Log.warn("ISOFile","Duplicate Box of type: "+b.type+", overriding previous occurrence"),this[b.type]=b}this.updateUsedBytes&&this.updateUsedBytes(b,a)}},ISOFile.prototype.getBox=function(a){var b=this.getBoxes(a,!0);return b.length?b[0]:null},ISOFile.prototype.getBoxes=function(a,b){var c=[];return ISOFile._sweep.call(this,a,c,b),c},ISOFile._sweep=function(a,b,c){this.type&&this.type==a&&b.push(this);for(var d in this.boxes){if(b.length&&c)return;ISOFile._sweep.call(this.boxes[d],a,b,c)}},"undefined"!=typeof exports&&(exports.ISOFile=ISOFile),ISOFile.prototype.lastBoxStartPosition=0,ISOFile.prototype.parsingMdat=null,ISOFile.prototype.nextParsePosition=0,ISOFile.prototype.discardMdatData=!1,ISOFile.prototype.processIncompleteBox=function(a){var b,c,d;return"mdat"===a.type?(b=new BoxParser[a.type+"Box"](a.size),this.parsingMdat=b,this.boxes.push(b),this.mdats.push(b),b.start=a.start,b.hdr_size=a.hdr_size,this.stream.addUsedBytes(b.hdr_size),this.lastBoxStartPosition=b.start+b.size,d=this.stream.seek(b.start+b.size,!1,this.discardMdatData),d?(this.parsingMdat=null,!0):(this.moovStartFound?this.nextParsePosition=this.stream.findEndContiguousBuf():this.nextParsePosition=b.start+b.size,!1)):("moov"===a.type&&(this.moovStartFound=!0,0===this.mdats.length&&(this.isProgressive=!0)),c=this.stream.mergeNextBuffer(),c?(this.nextParsePosition=this.stream.getEndPosition(),!0):(a.type?this.moovStartFound?this.nextParsePosition=this.stream.getEndPosition():this.nextParsePosition=this.stream.getPosition()+a.size:this.nextParsePosition=this.stream.getEndPosition(),!1))},ISOFile.prototype.hasIncompleteMdat=function(){return null!==this.parsingMdat},ISOFile.prototype.processIncompleteMdat=function(){var a,b;return a=this.parsingMdat,b=this.stream.seek(a.start+a.size,!1,this.discardMdatData),b?(Log.debug("ISOFile","Found 'mdat' end in buffered data"),this.parsingMdat=null,!0):(this.nextParsePosition=this.stream.findEndContiguousBuf(),!1)},ISOFile.prototype.restoreParsePosition=function(){return this.stream.seek(this.lastBoxStartPosition,!0,this.discardMdatData)},ISOFile.prototype.saveParsePosition=function(){this.lastBoxStartPosition=this.stream.getPosition()},ISOFile.prototype.updateUsedBytes=function(a,b){"mdat"===a.type?(this.stream.addUsedBytes(a.hdr_size),this.discardMdatData&&this.stream.addUsedBytes(a.size-a.hdr_size)):this.stream.addUsedBytes(a.size)},ISOFile.prototype.add=BoxParser.Box.prototype.add,ISOFile.prototype.init=function(){var a=this.add("moov");return a.add("mvhd").set("timescale",600).set("rate",1).set("creation_time",0).set("modification_time",0).set("duration",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("next_track_id",1),a.add("mvex"),this},ISOFile.prototype.addTrack=function(a){this.moov||this.init();var b=a||{};b.width=b.width||320,b.height=b.height||320,b.id=b.id||this.moov.mvhd.next_track_id,b.type=b.type||"avc1";var c=this.moov.add("trak");this.moov.mvhd.next_track_id=b.id+1,c.add("tkhd").set("creation_time",0).set("modification_time",0).set("track_id",b.id).set("duration",0).set("layer",0).set("alternate_group",0).set("volume",1).set("matrix",[0,0,0,0,0,0,0,0,0]).set("width",b.width).set("height",b.height);var d=c.add("mdia");d.add("mdhd").set("creation_time",0).set("modification_time",0).set("timescale",b.timescale||1).set("duration",0).set("language",b.language||0),d.add("hdlr").set("handler",b.hdlr||"vide").set("name",b.name||"Track created with MP4Box.js"),
d.add("elng").set("extended_language",b.language||"fr-FR");for(var e=d.add("minf"),f=new BoxParser[b.type+"SampleEntry"],g="",h=0;h<BoxParser.sampleEntryCodes.length;h++){var i=BoxParser.sampleEntryCodes[h];if(i.types.indexOf(b.type)>-1){g=i.prefix;break}}switch(g){case"Visual":e.add("vmhd").set("graphicsmode",0).set("opcolor",[0,0,0]),f.set("width",b.width).set("height",b.height).set("horizresolution",72<<16).set("vertresolution",72<<16).set("frame_count",1).set("compressorname",b.type+" Compressor").set("depth",24),f.add("avcC").set("SPS",[]).set("PPS",[]).set("configurationVersion",0).set("AVCProfileIndication",0).set("profile_compatibility",0).set("AVCLevelIndication",0).set("lengthSizeMinusOne",0);break;case"Audio":e.add("smhd");break;case"Hint":e.add("hmhd");break;case"Subtitle":e.add("sthd");break;case"Metadata":e.add("nmhd");break;case"System":e.add("nmhd");break;default:e.add("nmhd")}e.add("dinf").add("dref").addEntry((new BoxParser["url Box"]).set("flags",1));var j=d.add("stbl");return j.add("stsd").addEntry(f),j.add("stts").set("sample_counts",[]).set("sample_deltas",[]),j.add("stsc").set("first_chunk",[]).set("samples_per_chunk",[]).set("sample_description_index",[]),this.moov.mvex.add("trex").set("track_id",b.id).set("default_sample_description_index",b.default_sample_description_index||0).set("default_sample_duration",b.default_sample_duration||0).set("default_sample_size",b.default_sample_size||0).set("default_sample_flags",b.default_sample_flags||0),c},ISOFile.prototype.getBuffer=function(){var a=new DataStream;return a.endianness=DataStream.BIG_ENDIAN,this.write(a),a.buffer},ISOFile.prototype.addSample=function(a,b,c){},ISOFile.prototype.lastMoofIndex=0,ISOFile.prototype.samplesDataSize=0,ISOFile.prototype.resetTables=function(){var a,b,c,d,e,f,g,h;for(this.initial_duration=this.moov.mvhd.duration,this.moov.mvhd.duration=0,a=0;a<this.moov.traks.length;a++){b=this.moov.traks[a],b.tkhd.duration=0,b.mdia.mdhd.duration=0,c=b.mdia.minf.stbl.stco||b.mdia.minf.stbl.co64,c.chunk_offsets=[],d=b.mdia.minf.stbl.stsc,d.first_chunk=[],d.samples_per_chunk=[],d.sample_description_index=[],e=b.mdia.minf.stbl.stsz||b.mdia.minf.stbl.stz2,e.sample_sizes=[],f=b.mdia.minf.stbl.stts,f.sample_counts=[],f.sample_deltas=[],g=b.mdia.minf.stbl.ctts,g&&(g.sample_counts=[],g.sample_offsets=[]),h=b.mdia.minf.stbl.stss;var i=b.mdia.minf.stbl.boxes.indexOf(h);-1!=i&&(b.mdia.minf.stbl.boxes[i]=null)}},ISOFile.initSampleGroups=function(a,b,c,d,e){function f(a,b,c){this.grouping_type=a,this.grouping_type_parameter=b,this.sbgp=c,this.last_sample_in_run=-1,this.entry_index=-1}var g,h,i,j;for(b&&(b.sample_groups_info=[]),a.sample_groups_info||(a.sample_groups_info=[]),h=0;h<c.length;h++){for(j=c[h].grouping_type+"/"+c[h].grouping_type_parameter,i=new f(c[h].grouping_type,c[h].grouping_type_parameter,c[h]),b&&(b.sample_groups_info[j]=i),a.sample_groups_info[j]||(a.sample_groups_info[j]=i),g=0;g<d.length;g++)d[g].grouping_type===c[h].grouping_type&&(i.description=d[g],i.description.used=!0);if(e)for(g=0;g<e.length;g++)e[g].grouping_type===c[h].grouping_type&&(i.fragment_description=e[g],i.fragment_description.used=!0,i.is_fragment=!0)}if(b){if(e)for(h=0;h<e.length;h++)!e[h].used&&e[h].version>=2&&(j=e[h].grouping_type+"/0",i=new f(e[h].grouping_type,0),i.is_fragment=!0,b.sample_groups_info[j]||(b.sample_groups_info[j]=i))}else for(h=0;h<d.length;h++)!d[h].used&&d[h].version>=2&&(j=d[h].grouping_type+"/0",i=new f(d[h].grouping_type,0),a.sample_groups_info[j]||(a.sample_groups_info[j]=i))},ISOFile.setSampleGroupProperties=function(a,b,c,d){var e,f;b.sample_groups=[];for(e in d)if(b.sample_groups[e]={},b.sample_groups[e].grouping_type=d[e].grouping_type,b.sample_groups[e].grouping_type_parameter=d[e].grouping_type_parameter,c>=d[e].last_sample_in_run&&(d[e].last_sample_in_run<0&&(d[e].last_sample_in_run=0),d[e].entry_index++,d[e].entry_index<=d[e].sbgp.entries.length-1&&(d[e].last_sample_in_run+=d[e].sbgp.entries[d[e].entry_index].sample_count)),d[e].entry_index<=d[e].sbgp.entries.length-1?b.sample_groups[e].group_description_index=d[e].sbgp.entries[d[e].entry_index].group_description_index:b.sample_groups[e].group_description_index=-1,0!==b.sample_groups[e].group_description_index){var g;g=d[e].fragment_description?d[e].fragment_description:d[e].description,b.sample_groups[e].group_description_index>0?(f=b.sample_groups[e].group_description_index>65535?(b.sample_groups[e].group_description_index>>16)-1:b.sample_groups[e].group_description_index-1,g&&f>=0&&(b.sample_groups[e].description=g.entries[f])):g&&g.version>=2&&g.default_group_description_index>0&&(b.sample_groups[e].description=g.entries[g.default_group_description_index-1])}},ISOFile.process_sdtp=function(a,b,c){b&&(a?(b.is_leading=a.is_leading[c],b.depends_on=a.sample_depends_on[c],b.is_depended_on=a.sample_is_depended_on[c],b.has_redundancy=a.sample_has_redundancy[c]):(b.is_leading=0,b.depends_on=0,b.is_depended_on=0,b.has_redundancy=0))},ISOFile.prototype.buildSampleLists=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z;for(a=0;a<this.moov.traks.length;a++)if(c=this.moov.traks[a],c.samples=[],c.samples_duration=0,c.samples_size=0,d=c.mdia.minf.stbl.stco||c.mdia.minf.stbl.co64,e=c.mdia.minf.stbl.stsc,f=c.mdia.minf.stbl.stsz||c.mdia.minf.stbl.stz2,g=c.mdia.minf.stbl.stts,h=c.mdia.minf.stbl.ctts,i=c.mdia.minf.stbl.stss,j=c.mdia.minf.stbl.stsd,k=c.mdia.minf.stbl.subs,n=c.mdia.minf.stbl.stdp,l=c.mdia.minf.stbl.sbgps,m=c.mdia.minf.stbl.sgpds,t=-1,u=-1,v=-1,w=-1,x=0,y=0,z=0,ISOFile.initSampleGroups(c,null,l,m),"undefined"!=typeof f){for(b=0;b<f.sample_sizes.length;b++){var A={};A.number=b,A.track_id=c.tkhd.track_id,A.timescale=c.mdia.mdhd.timescale,A.alreadyRead=0,c.samples[b]=A,A.size=f.sample_sizes[b],c.samples_size+=A.size,0===b?(p=1,o=0,A.chunk_index=p,A.chunk_run_index=o,s=e.samples_per_chunk[o],r=0,q=o+1<e.first_chunk.length?e.first_chunk[o+1]-1:1/0):s>b?(A.chunk_index=p,A.chunk_run_index=o):(p++,A.chunk_index=p,r=0,q>=p||(o++,q=o+1<e.first_chunk.length?e.first_chunk[o+1]-1:1/0),A.chunk_run_index=o,s+=e.samples_per_chunk[o]),A.description_index=e.sample_description_index[A.chunk_run_index]-1,A.description=j.entries[A.description_index],A.offset=d.chunk_offsets[A.chunk_index-1]+r,r+=A.size,b>t&&(u++,0>t&&(t=0),t+=g.sample_counts[u]),b>0?(c.samples[b-1].duration=g.sample_deltas[u],c.samples_duration+=c.samples[b-1].duration,A.dts=c.samples[b-1].dts+c.samples[b-1].duration):A.dts=0,h?(b>=v&&(w++,0>v&&(v=0),v+=h.sample_counts[w]),A.cts=c.samples[b].dts+h.sample_offsets[w]):A.cts=A.dts,i?b==i.sample_numbers[x]-1?(A.is_sync=!0,x++):A.is_sync=!1:A.is_sync=!0,ISOFile.process_sdtp(c.mdia.minf.stbl.sdtp,A,A.number),n?A.degradation_priority=n.priority[b]:A.degradation_priority=0,k&&k.entries[y].sample_delta+z==b&&(A.subsamples=k.entries[y].subsamples,z+=k.entries[y].sample_delta),(l.length>0||m.length>0)&&ISOFile.setSampleGroupProperties(c,A,b,c.sample_groups_info)}b>0&&(c.samples[b-1].duration=Math.max(c.mdia.mdhd.duration-c.samples[b-1].dts,0),c.samples_duration+=c.samples[b-1].duration)}},ISOFile.prototype.updateSampleLists=function(){var a,b,c,d,e,f,g,h,i,j,k,l,m,n,o;if(void 0!==this.moov)for(;this.lastMoofIndex<this.moofs.length;)if(i=this.moofs[this.lastMoofIndex],this.lastMoofIndex++,"moof"==i.type)for(j=i,a=0;a<j.trafs.length;a++){for(k=j.trafs[a],l=this.getTrackById(k.tfhd.track_id),m=this.getTrexById(k.tfhd.track_id),d=k.tfhd.flags&BoxParser.TFHD_FLAG_SAMPLE_DESC?k.tfhd.default_sample_description_index:m?m.default_sample_description_index:1,e=k.tfhd.flags&BoxParser.TFHD_FLAG_SAMPLE_DUR?k.tfhd.default_sample_duration:m?m.default_sample_duration:0,f=k.tfhd.flags&BoxParser.TFHD_FLAG_SAMPLE_SIZE?k.tfhd.default_sample_size:m?m.default_sample_size:0,g=k.tfhd.flags&BoxParser.TFHD_FLAG_SAMPLE_FLAGS?k.tfhd.default_sample_flags:m?m.default_sample_flags:0,k.sample_number=0,k.sbgps.length>0&&ISOFile.initSampleGroups(l,k,k.sbgps,l.mdia.minf.stbl.sgpds,k.sgpds),b=0;b<k.truns.length;b++){var p=k.truns[b];for(c=0;c<p.sample_count;c++){n={},n.number_in_traf=k.sample_number,k.sample_number++,n.number=l.samples.length,k.first_sample_index=l.samples.length,l.samples.push(n),n.track_id=l.tkhd.track_id,n.timescale=l.mdia.mdhd.timescale,n.description_index=d-1,n.description=l.mdia.minf.stbl.stsd.entries[n.description_index],n.size=f,p.flags&BoxParser.TRUN_FLAGS_SIZE&&(n.size=p.sample_size[c]),l.samples_size+=n.size,n.duration=e,p.flags&BoxParser.TRUN_FLAGS_DURATION&&(n.duration=p.sample_duration[c]),l.samples_duration+=n.duration,l.first_traf_merged||c>0?n.dts=l.samples[l.samples.length-2].dts+l.samples[l.samples.length-2].duration:(k.tfdt?n.dts=k.tfdt.baseMediaDecodeTime:n.dts=0,l.first_traf_merged=!0),n.cts=n.dts,p.flags&BoxParser.TRUN_FLAGS_CTS_OFFSET&&(n.cts=n.dts+p.sample_composition_time_offset[c]),o=g,p.flags&BoxParser.TRUN_FLAGS_FLAGS?o=p.sample_flags[c]:0===c&&p.flags&BoxParser.TRUN_FLAGS_FIRST_FLAG&&(o=p.first_sample_flags),n.is_sync=!(o>>16&1),n.is_leading=o>>26&3,n.depends_on=o>>24&3,n.is_depended_on=o>>22&3,n.has_redundancy=o>>20&3,n.degradation_priority=65535&o,ISOFile.process_sdtp(k.sdtp,n,n.number_in_traf);var q=!!(k.tfhd.flags&BoxParser.TFHD_FLAG_BASE_DATA_OFFSET),r=!!(k.tfhd.flags&BoxParser.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),s=!!(p.flags&BoxParser.TRUN_FLAGS_DATA_OFFSET),t=0;t=q?k.tfhd.base_data_offset:r?j.start:0===b?j.start:h,0===b&&0===c?s?n.offset=t+p.data_offset:n.offset=t:n.offset=h,h=n.offset+n.size,(k.sbgps.length>0||k.sgpds.length>0||l.mdia.minf.stbl.sbgps.length>0||l.mdia.minf.stbl.sgpds.length>0)&&ISOFile.setSampleGroupProperties(l,n,n.number_in_traf,k.sample_groups_info)}}if(k.subs){l.has_fragment_subsamples=!0;var u=k.first_sample_index;for(b=0;b<k.subs.entries.length;b++)u+=k.subs.entries[b].sample_delta,n=l.samples[u-1],n.subsamples=k.subs.entries[b].subsamples}}},ISOFile.prototype.getSample=function(a,b){var c,d=a.samples[b];if(!this.moov)return null;if(d.data){if(d.alreadyRead==d.size)return d}else d.data=new Uint8Array(d.size),d.alreadyRead=0,this.samplesDataSize+=d.size,Log.debug("ISOFile","Allocating sample #"+b+" on track #"+a.tkhd.track_id+" of size "+d.size+" (total: "+this.samplesDataSize+")");var e=this.stream.findPosition(!0,d.offset+d.alreadyRead,!1);if(e>-1){c=this.stream.buffers[e];var f=c.byteLength-(d.offset+d.alreadyRead-c.fileStart);return d.size-d.alreadyRead<=f?(Log.debug("ISOFile","Getting sample #"+b+" data (alreadyRead: "+d.alreadyRead+" offset: "+(d.offset+d.alreadyRead-c.fileStart)+" read size: "+(d.size-d.alreadyRead)+" full size: "+d.size+")"),DataStream.memcpy(d.data.buffer,d.alreadyRead,c,d.offset+d.alreadyRead-c.fileStart,d.size-d.alreadyRead),c.usedBytes+=d.size-d.alreadyRead,this.stream.logBufferLevel(),d.alreadyRead=d.size,d):(Log.debug("ISOFile","Getting sample #"+b+" partial data (alreadyRead: "+d.alreadyRead+" offset: "+(d.offset+d.alreadyRead-c.fileStart)+" read size: "+f+" full size: "+d.size+")"),DataStream.memcpy(d.data.buffer,d.alreadyRead,c,d.offset+d.alreadyRead-c.fileStart,f),d.alreadyRead+=f,c.usedBytes+=f,this.stream.logBufferLevel(),null)}return null},ISOFile.prototype.releaseSample=function(a,b){var c=a.samples[b];return c.data?(this.samplesDataSize-=c.size,c.data=null,c.alreadyRead=0,c.size):0},ISOFile.prototype.getAllocatedSampleDataSize=function(){return this.samplesDataSize},ISOFile.prototype.getCodecs=function(){var a,b="";for(a=0;a<this.moov.traks.length;a++){var c=this.moov.traks[a];a>0&&(b+=","),b+=c.mdia.minf.stbl.stsd.entries[0].getCodec()}return b},ISOFile.prototype.getTrexById=function(a){var b;if(!this.moov||!this.moov.mvex)return null;for(b=0;b<this.moov.mvex.trexs.length;b++){var c=this.moov.mvex.trexs[b];if(c.track_id==a)return c}return null},ISOFile.prototype.getTrackById=function(a){if(void 0===this.moov)return null;for(var b=0;b<this.moov.traks.length;b++){var c=this.moov.traks[b];if(c.tkhd.track_id==a)return c}return null},ISOFile.prototype.items=[],ISOFile.prototype.itemsDataSize=0,ISOFile.prototype.flattenItemInfo=function(){var a,b,c,d=this.items,e=this.meta;if(null!==e&&void 0!==e&&void 0!==e.hdlr&&void 0!==e.iinf){for(a=0;a<e.iinf.item_infos.length;a++)c={},c.id=e.iinf.item_infos[a].item_ID,d[c.id]=c,c.ref_to=[],c.name=e.iinf.item_infos[a].item_name,e.iinf.item_infos[a].protection_index>0&&(c.protection=e.ipro.protections[e.iinf.item_infos[a].protection_index-1]),e.iinf.item_infos[a].item_type?c.type=e.iinf.item_infos[a].item_type:c.type="mime",c.content_type=e.iinf.item_infos[a].content_type,c.content_encoding=e.iinf.item_infos[a].content_encoding;if(e.iloc)for(a=0;a<e.iloc.items.length;a++){var f=e.iloc.items[a];if(c=d[f.item_ID],0!==f.data_reference_index&&(Log.warn("Item storage with reference to other files: not supported"),c.source=e.dinf.boxes[f.data_reference_index-1]),void 0!==f.construction_method)switch(Log.warn("Item storage with construction_method : not supported"),f.construction_method){case 0:break;case 1:break;case 2:}else for(c.extents=[],c.size=0,b=0;b<f.extents.length;b++)c.extents[b]={},c.extents[b].offset=f.extents[b].extent_offset+f.base_offset,c.extents[b].length=f.extents[b].extent_length,c.extents[b].alreadyRead=0,c.size+=c.extents[b].length}if(e.pitm&&(d[e.pitm.item_id].primary=!0),e.iref)for(a=0;a<e.iref.references.length;a++){var g=e.iref.references[a];for(b=0;b<g.references.length;b++)d[g.from_item_ID].ref_to.push({type:g.type,id:g.references[b]})}}},ISOFile.prototype.getItem=function(a){var b,c;if(!this.meta)return null;if(c=this.items[a],!c.data&&c.size)c.data=new Uint8Array(c.size),c.alreadyRead=0,this.itemsDataSize+=c.size,Log.debug("ISOFile","Allocating item #"+a+" of size "+c.size+" (total: "+this.itemsDataSize+")");else if(c.alreadyRead===c.size)return c;for(var d=0;d<c.extents.length;d++){var e=c.extents[d];if(e.alreadyRead!==e.length){var f=this.stream.findPosition(!0,e.offset+e.alreadyRead,!1);if(!(f>-1))return null;b=this.stream.buffers[f];var g=b.byteLength-(e.offset+e.alreadyRead-b.fileStart);if(!(e.length-e.alreadyRead<=g))return Log.debug("ISOFile","Getting item #"+a+" extent #"+d+" partial data (alreadyRead: "+e.alreadyRead+" offset: "+(e.offset+e.alreadyRead-b.fileStart)+" read size: "+g+" full extent size: "+e.length+" full item size: "+c.size+")"),DataStream.memcpy(c.data.buffer,c.alreadyRead,b,e.offset+e.alreadyRead-b.fileStart,g),e.alreadyRead+=g,c.alreadyRead+=g,b.usedBytes+=g,this.stream.logBufferLevel(),null;Log.debug("ISOFile","Getting item #"+a+" extent #"+d+" data (alreadyRead: "+e.alreadyRead+" offset: "+(e.offset+e.alreadyRead-b.fileStart)+" read size: "+(e.length-e.alreadyRead)+" full extent size: "+e.length+" full item size: "+c.size+")"),DataStream.memcpy(c.data.buffer,c.alreadyRead,b,e.offset+e.alreadyRead-b.fileStart,e.length-e.alreadyRead),b.usedBytes+=e.length-e.alreadyRead,this.stream.logBufferLevel(),e.alreadyRead=e.length,c.alreadyRead+=e.length}}return c.alreadyRead===c.size?c:null},ISOFile.prototype.releaseItem=function(a){var b=this.items[a];if(b.data){this.itemsDataSize-=b.size,b.data=null,b.alreadyRead=0;for(var c=0;c<b.extents.length;c++){var d=b.extents[c];d.alreadyRead=0}return b.size}return 0},ISOFile.prototype.processItems=function(a){for(var b in this.items){var c=this.items[b];this.getItem(c.id),a&&!c.sent&&(a(c),c.sent=!0,c.data=null)}},ISOFile.prototype.hasItem=function(a){for(var b in this.items){var c=this.items[b];if(c.name===a)return c.id}return-1},ISOFile.prototype.getMetaHandler=function(){return this.meta?this.meta.hdlr.handler:null},ISOFile.prototype.getPrimaryItem=function(){return this.meta&&this.meta.pitm?this.getItem(this.meta.pitm.item_id):null},ISOFile.prototype.write=function(a){for(var b=0;b<this.boxes.length;b++)this.boxes[b].write(a)},ISOFile.writeInitializationSegment=function(a,b,c,d){var e;Log.debug("ISOFile","Generating initialization segment");var f=new DataStream;f.endianness=DataStream.BIG_ENDIAN,a.write(f);var g=b.add("mvex");for(c&&g.add("mehd").set("fragment_duration",c),e=0;e<b.traks.length;e++)g.add("trex").set("track_id",b.traks[e].tkhd.track_id).set("default_sample_description_index",1).set("default_sample_duration",d).set("default_sample_size",0).set("default_sample_flags",65536);return b.write(f),f.buffer};var MP4Box=function(a){this.inputStream=new MultiBufferStream,this.keepMdatData=void 0!==a?a:!0,this.inputIsoFile=new ISOFile(this.inputStream),this.inputIsoFile.discardMdatData=!this.keepMdatData,this.onMoovStart=null,this.moovStartSent=!1,this.onReady=null,this.readySent=!1,this.onSegment=null,this.onSamples=null,this.onError=null,this.sampleListBuilt=!1,this.fragmentedTracks=[],this.extractedTracks=[],this.isFragmentationInitialized=!1,this.sampleProcessingStarted=!1,this.nextMoofNumber=0,this.itemListBuilt=!1};MP4Box.prototype.setSegmentOptions=function(a,b,c){var d=this.inputIsoFile.getTrackById(a);if(d){var e={};this.fragmentedTracks.push(e),e.id=a,e.user=b,e.trak=d,d.nextSample=0,e.segmentStream=null,e.nb_samples=1e3,e.rapAlignement=!0,c&&(c.nbSamples&&(e.nb_samples=c.nbSamples),c.rapAlignement&&(e.rapAlignement=c.rapAlignement))}},MP4Box.prototype.unsetSegmentOptions=function(a){for(var b=-1,c=0;c<this.fragmentedTracks.length;c++){var d=this.fragmentedTracks[c];d.id==a&&(b=c)}b>-1&&this.fragmentedTracks.splice(b,1)},MP4Box.prototype.setExtractionOptions=function(a,b,c){var d=this.inputIsoFile.getTrackById(a);if(d){var e={};this.extractedTracks.push(e),e.id=a,e.user=b,e.trak=d,d.nextSample=0,e.nb_samples=1e3,e.samples=[],c&&c.nbSamples&&(e.nb_samples=c.nbSamples)}},MP4Box.prototype.unsetExtractionOptions=function(a){for(var b=-1,c=0;c<this.extractedTracks.length;c++){var d=this.extractedTracks[c];d.id==a&&(b=c)}b>-1&&this.extractedTracks.splice(b,1)},MP4Box.prototype.createSingleSampleMoof=function(a){var b=new BoxParser.moofBox;b.add("mfhd").set("sequence_number",this.nextMoofNumber),this.nextMoofNumber++;var c=b.add("traf");return c.add("tfhd").set("track_id",a.track_id).set("flags",BoxParser.TFHD_FLAG_DEFAULT_BASE_IS_MOOF),c.add("tfdt").set("baseMediaDecodeTime",a.dts),c.add("trun").set("flags",BoxParser.TRUN_FLAGS_DATA_OFFSET|BoxParser.TRUN_FLAGS_DURATION|BoxParser.TRUN_FLAGS_SIZE|BoxParser.TRUN_FLAGS_FLAGS|BoxParser.TRUN_FLAGS_CTS_OFFSET).set("data_offset",0).set("first_sample_flags",0).set("sample_count",1).set("sample_duration",[a.duration]).set("sample_size",[a.size]).set("sample_flags",[0]).set("sample_composition_time_offset",[a.cts-a.dts]),b},MP4Box.prototype.createFragment=function(a,b,c,d){var e=this.inputIsoFile.getTrackById(b),f=this.inputIsoFile.getSample(e,c);if(null==f)return f=e.samples[c],this.nextSeekPosition?this.nextSeekPosition=Math.min(f.offset+f.alreadyRead,this.nextSeekPosition):this.nextSeekPosition=e.samples[c].offset+f.alreadyRead,null;var g=d||new DataStream;g.endianness=DataStream.BIG_ENDIAN;var h=this.createSingleSampleMoof(f);h.write(g),h.trafs[0].truns[0].data_offset=h.size+8,Log.debug("MP4Box","Adjusting data_offset with new value "+h.trafs[0].truns[0].data_offset),g.adjustUint32(h.trafs[0].truns[0].data_offset_position,h.trafs[0].truns[0].data_offset);var i=new BoxParser.mdatBox;return i.data=f.data,i.write(g),g},MP4Box.prototype.processSamples=function(){var a,b;if(this.sampleProcessingStarted){if(this.isFragmentationInitialized&&null!==this.onSegment)for(a=0;a<this.fragmentedTracks.length;a++){var c=this.fragmentedTracks[a];for(b=c.trak;b.nextSample<b.samples.length&&this.sampleProcessingStarted;){Log.debug("MP4Box","Creating media fragment on track #"+c.id+" for sample "+b.nextSample);var d=this.createFragment(this.inputIsoFile,c.id,b.nextSample,c.segmentStream);if(!d)break;if(c.segmentStream=d,b.nextSample++,(b.nextSample%c.nb_samples===0||b.nextSample>=b.samples.length)&&(Log.info("MP4Box","Sending fragmented data on track #"+c.id+" for samples ["+Math.max(0,b.nextSample-c.nb_samples)+","+(b.nextSample-1)+"]"),Log.info("MP4Box","Sample data size in memory: "+this.inputIsoFile.getAllocatedSampleDataSize()),this.onSegment&&this.onSegment(c.id,c.user,c.segmentStream.buffer,b.nextSample),c.segmentStream=null,c!==this.fragmentedTracks[a]))break}}if(null!==this.onSamples)for(a=0;a<this.extractedTracks.length;a++){var e=this.extractedTracks[a];for(b=e.trak;b.nextSample<b.samples.length&&this.sampleProcessingStarted;){Log.debug("MP4Box","Exporting on track #"+e.id+" sample #"+b.nextSample);var f=this.inputIsoFile.getSample(b,b.nextSample);if(!f)break;if(b.nextSample++,e.samples.push(f),(b.nextSample%e.nb_samples===0||b.nextSample>=b.samples.length)&&(Log.debug("MP4Box","Sending samples on track #"+e.id+" for sample "+b.nextSample),this.onSamples&&this.onSamples(e.id,e.user,e.samples),e.samples=[],e!==this.extractedTracks[a]))break}}}},MP4Box.prototype.checkBuffer=function(a){if(null===a||void 0===a)throw"Buffer must be defined and non empty";if(void 0===a.fileStart)throw"Buffer must have a fileStart property";return 0===a.byteLength?(Log.warn("MP4Box","Ignoring empty buffer (fileStart: "+a.fileStart+")"),this.inputStream.logBufferLevel(),!1):(Log.info("MP4Box","Processing buffer (fileStart: "+a.fileStart+")"),a.usedBytes=0,this.inputStream.insertBuffer(a),this.inputStream.logBufferLevel(),this.inputStream.initialized()?!0:(Log.warn("MP4Box","Not ready to start parsing"),!1))},MP4Box.prototype.appendBuffer=function(a){var b;if(!this.checkBuffer||this.checkBuffer(a))return this.inputIsoFile.parse(),this.inputIsoFile.moovStartFound&&!this.moovStartSent&&(this.moovStartSent=!0,this.onMoovStart&&this.onMoovStart()),this.inputIsoFile.moov?(this.processSamples&&(this.sampleListBuilt||(this.inputIsoFile.buildSampleLists(),this.sampleListBuilt=!0),this.inputIsoFile.updateSampleLists()),this.onReady&&!this.readySent&&(this.readySent=!0,this.onReady(this.getInfo())),this.processSamples&&this.processSamples(),this.nextSeekPosition?(b=this.nextSeekPosition,this.nextSeekPosition=void 0):b=this.inputIsoFile.nextParsePosition,this.inputStream.getEndFilePositionAfter&&(b=this.inputStream.getEndFilePositionAfter(b))):b=null!==this.inputIsoFile?this.inputIsoFile.nextParsePosition:0,this.inputIsoFile.meta&&(this.inputIsoFile.flattenItemInfo&&!this.itemListBuilt&&(this.inputIsoFile.flattenItemInfo(),this.itemListBuilt=!0),this.inputIsoFile.processItems&&this.inputIsoFile.processItems(this.onItem)),this.inputStream.cleanBuffers&&(Log.info("MP4Box","Done processing buffer (fileStart: "+a.fileStart+") - next buffer to fetch should have a fileStart position of "+b),this.inputStream.logBufferLevel(),this.inputStream.cleanBuffers(),this.inputStream.logBufferLevel(!0),Log.info("MP4Box","Sample data size in memory: "+this.inputIsoFile.getAllocatedSampleDataSize())),b},MP4Box.prototype.getInfo=function(){var a,b,c,d,e,f={},g=new Date(4,0,1,0,0,0,0).getTime();if(this.inputIsoFile.moov)for(f.hasMoov=!0,f.duration=this.inputIsoFile.moov.mvhd.duration,f.timescale=this.inputIsoFile.moov.mvhd.timescale,f.isFragmented=null!=this.inputIsoFile.moov.mvex,f.isFragmented&&this.inputIsoFile.moov.mvex.mehd&&(f.fragment_duration=this.inputIsoFile.moov.mvex.mehd.fragment_duration),f.isProgressive=this.inputIsoFile.isProgressive,f.hasIOD=null!=this.inputIsoFile.moov.iods,f.brands=[],f.brands.push(this.inputIsoFile.ftyp.major_brand),f.brands=f.brands.concat(this.inputIsoFile.ftyp.compatible_brands),f.created=new Date(g+1e3*this.inputIsoFile.moov.mvhd.creation_time),f.modified=new Date(g+1e3*this.inputIsoFile.moov.mvhd.modification_time),f.tracks=[],f.audioTracks=[],f.videoTracks=[],f.subtitleTracks=[],f.metadataTracks=[],f.hintTracks=[],f.otherTracks=[],a=0;a<this.inputIsoFile.moov.traks.length;a++){if(c=this.inputIsoFile.moov.traks[a],e=c.mdia.minf.stbl.stsd.entries[0],d={},f.tracks.push(d),d.id=c.tkhd.track_id,d.name=c.mdia.hdlr.name,d.references=[],c.tref)for(b=0;b<c.tref.boxes.length;b++)ref={},d.references.push(ref),ref.type=c.tref.boxes[b].type,ref.track_ids=c.tref.boxes[b].track_ids;c.edts&&(d.edits=c.edts.elst.entries),d.created=new Date(g+1e3*c.tkhd.creation_time),d.modified=new Date(g+1e3*c.tkhd.modification_time),d.movie_duration=c.tkhd.duration,d.movie_timescale=f.timescale,d.layer=c.tkhd.layer,d.alternate_group=c.tkhd.alternate_group,d.volume=c.tkhd.volume,d.matrix=c.tkhd.matrix,d.track_width=c.tkhd.width/65536,d.track_height=c.tkhd.height/65536,d.timescale=c.mdia.mdhd.timescale,d.cts_shift=c.mdia.minf.stbl.cslg,d.duration=c.mdia.mdhd.duration,d.samples_duration=c.samples_duration,d.codec=e.getCodec(),d.kind=c.udta&&c.udta.kinds.length?c.udta.kinds[0]:{schemeURI:"",value:""},d.language=c.mdia.elng?c.mdia.elng.extended_language:c.mdia.mdhd.languageString,d.nb_samples=c.samples.length,d.size=c.samples_size,d.bitrate=8*d.size*d.timescale/d.samples_duration,e.isAudio()?(d.type="audio",f.audioTracks.push(d),d.audio={},d.audio.sample_rate=e.getSampleRate(),d.audio.channel_count=e.getChannelCount(),d.audio.sample_size=e.getSampleSize()):e.isVideo()?(d.type="video",f.videoTracks.push(d),d.video={},d.video.width=e.getWidth(),d.video.height=e.getHeight()):e.isSubtitle()?(d.type="subtitles",f.subtitleTracks.push(d)):e.isHint()?(d.type="metadata",f.hintTracks.push(d)):e.isMetadata()?(d.type="metadata",f.metadataTracks.push(d)):(d.type="metadata",f.otherTracks.push(d))}else f.hasMoov=!1;for(f.mime="",f.videoTracks.length>0?f.mime+='video/mp4; codecs="':f.audioTracks.length>0?f.mime+='audio/mp4; codecs="':f.mime+='application/mp4; codecs="',a=0;a<f.tracks.length;a++)0!==a&&(f.mime+=","),f.mime+=f.tracks[a].codec;return f.mime+='"; profiles="',f.mime+=this.inputIsoFile.ftyp.compatible_brands.join(),f.mime+='"',f},MP4Box.prototype.writeFile=function(){var a=new DataStream;return a.endianness=DataStream.BIG_ENDIAN,this.inputIsoFile.write(a),a.buffer},MP4Box.prototype.initializeSegmentation=function(){var a,b,c,d;for(null===this.onSegment&&Log.warn("MP4Box","No segmentation callback set!"),this.isFragmentationInitialized||(this.isFragmentationInitialized=!0,this.nextMoofNumber=0,this.inputIsoFile.resetTables()),b=[],a=0;a<this.fragmentedTracks.length;a++){var e=new BoxParser.moovBox;e.mvhd=this.inputIsoFile.moov.mvhd,e.boxes.push(e.mvhd),c=this.inputIsoFile.getTrackById(this.fragmentedTracks[a].id),e.boxes.push(c),e.traks.push(c),d={},d.id=c.tkhd.track_id,d.user=this.fragmentedTracks[a].user,d.buffer=ISOFile.writeInitializationSegment(this.inputIsoFile.ftyp,e,this.inputIsoFile.moov.mvex&&this.inputIsoFile.moov.mvex.mehd?this.inputIsoFile.moov.mvex.mehd.fragment_duration:void 0,this.inputIsoFile.moov.traks[a].samples.length>0?this.inputIsoFile.moov.traks[a].samples[0].duration:0),b.push(d)}return b},MP4Box.prototype.releaseUsedSamples=function(a,b){var c=0,d=this.inputIsoFile.getTrackById(a);d.lastValidSample||(d.lastValidSample=0);for(var e=d.lastValidSample;b>e;e++)c+=this.inputIsoFile.releaseSample(d,e);Log.info("MP4Box","Track #"+a+" released samples up to "+b+" (released size: "+c+", remaining: "+this.inputIsoFile.samplesDataSize+")"),d.lastValidSample=b},MP4Box.prototype.flush=function(){Log.info("MP4Box","Flushing remaining samples"),this.inputIsoFile.updateSampleLists(),this.processSamples(),this.inputStream.cleanBuffers(),this.inputStream.logBufferLevel(!0)},MP4Box.prototype.seekTrack=function(a,b,c){var d,e,f,g=1/0,h=0,i=0;if(0===c.samples.length)return Log.info("MP4Box","No sample in track, cannot seek! Using time "+Log.getDurationString(0,1)+" and offset: 0"),{offset:0,time:0};for(d=0;d<c.samples.length;d++){if(e=c.samples[d],0===d)i=0,f=e.timescale;else if(e.cts>a*e.timescale){i=d-1;break}b&&e.is_sync&&(h=d)}for(b&&(i=h),a=c.samples[i].cts,c.nextSample=i;c.samples[i].alreadyRead===c.samples[i].size;)i++;return g=c.samples[i].offset+c.samples[i].alreadyRead,Log.info("MP4Box","Seeking to "+(b?"RAP":"")+" sample #"+c.nextSample+" on track "+c.tkhd.track_id+", time "+Log.getDurationString(a,f)+" and offset: "+g),{offset:g,time:a/f}},MP4Box.prototype.seek=function(a,b){var c,d,e,f=this.inputIsoFile.moov,g={offset:1/0,time:1/0};if(this.inputIsoFile.moov){for(e=0;e<f.traks.length;e++)c=f.traks[e],d=this.seekTrack(a,b,c),d.offset<g.offset&&(g.offset=d.offset),d.time<g.time&&(g.time=d.time);return Log.info("MP4Box","Seeking at time "+Log.getDurationString(g.time,1)+" needs a buffer with a fileStart position of "+g.offset),g.offset===1/0?g={offset:this.inputIsoFile.nextParsePosition,time:0}:g.offset=this.inputStream.getEndFilePositionAfter(g.offset),Log.info("MP4Box","Adjusted seek position (after checking data already in buffer): "+g.offset),g}throw"Cannot seek: moov not received!"},MP4Box.prototype.getTrackSamplesInfo=function(a){var b=this.inputIsoFile.getTrackById(a);return b?b.samples:void 0},MP4Box.prototype.getTrackSample=function(a,b){var c=this.inputIsoFile.getTrackById(a),d=this.inputIsoFile.getSample(c,b);return d},MP4Box.prototype.start=function(){this.sampleProcessingStarted=!0,this.processSamples()},MP4Box.prototype.stop=function(){this.sampleProcessingStarted=!1},"undefined"!=typeof exports&&(exports.MP4Box=MP4Box);
//# sourceMappingURL=mp4box.all.min.js.map