<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>MP4Box.js QUnit tests</title>
		<link rel="stylesheet" href="../node_modules/qunitjs/qunit/qunit.css">
		<script src="../node_modules/qunitjs/qunit/qunit.js"></script>
		<script src="lib/blanket/blanket.min.js" data-cover-flags="debug"></script>
		<script src="../dist/mp4box.all.js"></script>
		<script src="qunit-helper.js"></script>
		<script src="qunit-media-data.js"></script>
		<script src="qunit-box-data.js"></script>
		<script src="qunit-tests.js"></script>
		<script src="qunit-mse-tests.js"></script>
		<script src="qunit-isofile-tests.js"></script>
		<script src="iso-conformance-files.js"></script>
		<script src="qunit-iso-conformance.js"></script>
	</head>
	<body>
		<div id="qunit"></div>
		<div id="qunit-fixture"></div>
		<div id="video-placeholder"></div>
	</body>
</html>