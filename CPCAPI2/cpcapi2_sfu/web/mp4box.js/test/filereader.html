<!DOCTYPE html>
<html>
    <head>
    	<meta charset="UTF-8">
		<title>MP4Box.js - JavaScript MP4 Reader/Fragmenter</title>
		
		<!-- Include basic JQuery and JQuery UI files -->
		<script src="lib/jquery/jquery.js"></script>		
  		<link href="lib/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css">
		<script src="lib/jquery-ui/jquery-ui.min.js"></script>
		<script src="lib/jquery-ui/jquery-ui.slider.custom.js"></script>
		
		<!-- Include JQuery-based Fancytree skin, library, and extensions -->
  		<link href="lib/fancytree/ui.fancytree.min.css" rel="stylesheet" type="text/css">
  		<script src="lib/fancytree/jquery.fancytree-all.min.js" type="text/javascript"></script>

  		<script type="text/javascript" src="lib/d3/d3.js"></script>

		<script src="../dist/mp4box.all.js"></script>
		<script src="sample-urls.js"></script>
		<script src="movieInfoDisplay.js"></script>
		<script src="filereader.js"></script>
		<script src="downloader.js"></script>

		<style type="text/css">
		.box{
			background-color:gray;
			height:200px;
			width:250px;
			margin-top:120px;
			margin-left:500px;
			border:1px ridge #aaa;
			-moz-box-shadow: 10px 10px 10px #212121;
			-webkit-box-shadow: 10px 10px 10px #212121;
			box-shadow: 10px 10px 10px #212121;
		}

		table, td, th {
			text-align: left;
			vertical-align: text-top;
			border-spacing: 0;
			border: 0;
			border-collapse: collapse;
		}

		td:first-child { width: 20%; }
		
		ul.fancytree-container {
		  height: 50vh;
		  overflow: auto;
		  position: relative;
		}

		/* D3 Treemap styles */
		#treemap {
		  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
		  margin: auto;
		  position: relative;
		  width: 960px;
		}
		
		.node {
		  border: solid 1px white;
		  font: 10px sans-serif;
		  line-height: 12px;
		  overflow: hidden;
		  position: absolute;
		  text-indent: 2px;
		}

		.chart {
		  display: block;
		  margin: auto;
		  font-size: 11px;
		}

		rect {
		  stroke: #eee;
		  fill: #aaa;
		  fill-opacity: .8;
		}

		rect.parent {
		  cursor: pointer;
		  fill: steelblue;
		}

		text {
		  pointer-events: none;
		}		

		.axis path,
		.axis line, {
		  fill: none;
		  stroke: #000;
		  shape-rendering: crispEdges;
		}

		.x.axis path {
		}

		.area.above {
		  fill: rgb(252,141,89);
		}

		.area.below {
		  fill: rgb(145,207,96);
		}

		.line {
		  fill: none;
		  stroke: #000;
		  stroke-width: 1px;
		}

		.ui-selectmenu-button {
		    vertical-align: middle;
		}

		</style>
		<link rel="stylesheet" href="style.css" />
    </head>
	<body>
		<!-- GitHub Fork Me Ribbon -->
	    <a href="https://github.com/gpac/mp4box.js"><img style="position: absolute; z-index: 1; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_orange_ff7600.png" alt="Fork me on GitHub" ></a>
		<!-- GitHub Stat Buttons -->
		<div style="position: absolute; top: 10px; right: 150px; border: 0;" >
			<a class="github-button" href="https://github.com/gpac/mp4box.js" data-style="mega" data-count-href="/gpac/mp4box.js/stargazers" data-count-api="/repos/gpac/mp4box.js#stargazers_count">Star</a>
			<a class="github-button" href="https://github.com/gpac/mp4box.js/fork" data-style="mega" data-count-href="/gpac/mp4box.js/network" data-count-api="/repos/gpac/mp4box.js#forks_count">Fork</a>
			<a class="github-button" href="https://github.com/gpac/mp4box.js" data-style="mega" data-count-href="/gpac/mp4box.js/watchers" data-count-api="/repos/gpac/mp4box.js#subscribers_count">Watch</a>
			<script async defer id="github-bjs" src="https://buttons.github.io/buttons.js"></script>
		</div>

		<h3>MP4Box.js / ISOBMFF Box Structure Viewer (see also <a href="./">File Player</a>)</h3>
		<div style="width: 100%;">
			<div style="width: 15%; float: left;">
			<label for="input_type">Load: </label>
		    <select name="input_type" id="input_type">
		      <option>File</option>
		      <option>URL</option>
		      <option selected="selected">Example</option>
		    </select>
		    </div>
		    <div style="width: 45%; float: left;">
			  <div id="tabs-1">
				<input type='file' id='fileinput' onchange='drop()'>
			  </div>
			  <div id="tabs-2">
			    <input type='url' name='urlinput' id='urlinput'>
			  </div>
			  <div id="tabs-3">
				<select id="urlSelector">
				</select>
			  </div>
			</div>
		    <button style="width: 5%; float: left;" onclick='load()' id="LoadButton">Load</button>
			<div id="progressbar" style="width: 34%; float: left;">
				<div id="progress-label" style="text-align: center;">0%</div>
			</div>
		</div>
		<div id="resulttabs" style="clear: both;">
		  <ul>
		    <li><a href="#movieview">File Overview</a></li>
		    <li><a href="#boxview">Box View</a></li>
		    <li><a href="#sampleview">Sample View</a></li>
		    <li><a href="#itemview">Item View</a></li>
		  </ul>
			<div id="movieview">
			</div>
			<div id="boxview">
				<ul>
					<li><a href="#boxtreeview">Tree View</a></li>
					<li><a href="#boxpartitionview">Partition View</a></li>
					<li><a href="#boxmapview">Treemap View</a></li>
				</ul>
				<div id="boxtreeview">
					<table>
						<thead>
							<tr>
								<th>Box Tree View</th>
								<th>Box Property View</th>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td style="width: 30%">
									<div id="boxtree">
									</div>
								</td>
								<td>
									<div id="boxtable">
									</div>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div id="boxpartitionview">
				</div>
				<div id="boxmapview">
				</div>
			</div>
			<div id="sampleview">
				<label>View type:<select name="sampleviewselector" id="sampleviewselector">
					<option selected="selected">Sample Table</option>
					<option>Sample Graph</option>
					<option>Sample Map</option>
					<option>Sample Timeline</option>
				</select></label>
				<label>Track ID: <select id="trackSelect" name="trackSelect"></select></label>&nbsp;
				<label>Codec: <input type="text" disabled id="trackinfo" style="width: 120px;"></label>&nbsp;
				<label>Sample range: <input type="text" disabled id="sample-range-value" style="width: 200px;"></label>
				<div id="sample-range"></div>
				<div>
					<div id="sampletable"></div>
					<div id="samplegraph"></div>
					<div id="samplemap"></div>
					<div id="sampletimeline"></div>
				</div>
			</div>
			<div id="itemview">
			</div>
		</div>			
	</body>
</html>