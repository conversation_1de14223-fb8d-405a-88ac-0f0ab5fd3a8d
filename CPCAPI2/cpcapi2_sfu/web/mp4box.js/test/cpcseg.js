var index = 0;
var track_id;
var buffer;
var queue = [];
var videodiv = document.createElement("div");
document.body.appendChild(videodiv)
var video = document.createElement("video");
videodiv.innerHTML='';
videodiv.appendChild(video);
var ms = new MediaSource();
var downloader = new Downloader();
downloader.setCallback(
	function (response, end, error) { 
		var nextStart = 0;
		if (response) {
			console.log("downloaded " + response.byteLength + " bytes");
			if (typeof response !== 'string') {
				if (buffer.updating || queue.length > 0) {
					queue.push(response);
				} else {
					console.log("downloaded doing appendBuffer");
					buffer.appendBuffer(response);
				}
			}
		}
	}
);


ms.addEventListener('sourceopen', function(e) {
  downloader.start();
  video.play();

  buffer = ms.addSourceBuffer('video/mp4; codecs=\"avc1.42c020\"');
  //buffer.mode = 'sequence';

  buffer.addEventListener('updatestart', function(e) { console.log('updatestart: ' + ms.readyState); });
  buffer.addEventListener('update', function(e) { console.log('update: ' + ms.readyState); });
  buffer.addEventListener('updateend', function(e) { console.log('updateend: ' + ms.readyState); });
  buffer.addEventListener('error', function(e) { console.log('error: ' + ms.readyState); });
  buffer.addEventListener('abort', function(e) { console.log('abort: ' + ms.readyState); });

  buffer.addEventListener('update', function() { // Note: Have tried 'updateend'
    console.log('update2: ' + ms.readyState + ', queue length: ' + queue.length); 
    if (queue.length > 0 && !buffer.updating) {
	  console.log('update2 (doing appendBuffer): ' + ms.readyState); 
      buffer.appendBuffer(queue.shift());
    }
  });
}, false);

ms.addEventListener('sourceopen', function(e) { console.log('sourceopen: ' + ms.readyState); });
ms.addEventListener('sourceended', function(e) { console.log('sourceended: ' + ms.readyState); });
ms.addEventListener('sourceclose', function(e) { console.log('sourceclose: ' + ms.readyState); });
ms.addEventListener('error', function(e) { console.log('error: ' + ms.readyState); });



/*
function onSourceClose(e) {
	console.log("MediaSource closed!");
}

function onInitAppended(e) {
	console.log("onInitAppended");
}

mp4box.onReady = function(info) { 
	console.log("onReady");
	track_id = info.tracks[0].id;
	mp4box.setSegmentOptions(track_id, null, { nbSamples: 10, rapAlignement: true } );
	var initSegs = mp4box.initializeSegmentation();
	var mime = 'video/mp4; codecs=\"'+info.tracks[0].codec+'\"';
	this.sb = ms.addSourceBuffer(mime);
	this.sb.addEventListener("updateend", onInitAppended);
	this.sb.appendBuffer(initSegs[0].buffer);
	mp4box.start();
}

mp4box.onSegment = function(id, user, buffer, sampleNum) {	
	console.log("onSegment");
	//this.sb.appendBuffer(buffer);
}

function onSourceOpen(e) {
	console.log("onSourceOpen");
	downloader.start();
}

ms.addEventListener("sourceopen", onSourceOpen);
ms.addEventListener("sourceclose", onSourceClose);
*/

video.src = window.URL.createObjectURL(ms);
