function Downloader() {
	this.isActive = false;
	this.realtime = false;
	this.chunkStart = 0;
	this.chunkSize = 0;
	this.totalLength = 0;
	this.chunkTimeout = 1000;
	this.url = null;
	this.callback = null;
	this.eof = false;
	this.setDownloadTimeoutCallback = null;
	this.exampleSocket = null;
}

Downloader.prototype.setDownloadTimeoutCallback = function(callback) {
	this.setDownloadTimeoutCallback = callback;
	return this;
}

Downloader.prototype.reset = function() {
	this.chunkStart = 0;
	this.totalLength = 0;
	this.eof = false;
	return this;
}

Downloader.prototype.setRealTime = function(_realtime) {
	this.realtime = _realtime;
	return this;
}

Downloader.prototype.setChunkSize = function(_size) {
	this.chunkSize = _size;
	return this;
}

Downloader.prototype.setChunkStart = function(_start) {
	this.chunkStart = _start;
	this.eof = false;
	return this;
}

Downloader.prototype.setInterval = function(_timeout) {
	this.chunkTimeout = _timeout;
	return this;
}

Downloader.prototype.setUrl = function(_url) {
	this.url = _url;
	return this;
}

Downloader.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

Downloader.prototype.isStopped = function () {
	return !this.isActive;
}

Downloader.prototype.getFileLength = function () {
	return this.totalLength;
}

Downloader.prototype.getFile = function() {
    return;
}

function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
}

Downloader.prototype.start = function() {
	//Log.info("Downloader", "Starting file download");
    var dl = this;
	this.chunkStart = 0;
    this.exampleSocket = new WebSocket(makeWsUrl(9003));
	this.exampleSocket.binaryType = "arraybuffer";
    this.exampleSocket.onopen = function (event) {
        // log something
		//Log.info("Downloader", "Connected to WebSocket server");
    };	
    this.exampleSocket.onmessage = function (event) {
        //Log.info("Downloader", "Received data from WebSocket server (" + event.data.byteLength + " bytes)");
		var vidbuff = event.data;
		vidbuff.fileStart = dl.chunkStart;
		dl.callback(event.data, false);
    }	
	this.isActive = true;
	//this.resume();
	return this;
}

Downloader.prototype.resume = function() {
	//Log.info("Downloader", "Resuming file download");
	this.isActive = true;
	if (this.chunkSize === 0) {
		this.chunkSize = Infinity;
	}
	//this.getFile();
	return this;
}

Downloader.prototype.stop = function() {
	//Log.info("Downloader", "Stopping file download");
	this.isActive = false;
	if (this.timeoutID) {
		window.clearTimeout(this.timeoutID);
		delete this.timeoutID;
	}
	return this;
}
