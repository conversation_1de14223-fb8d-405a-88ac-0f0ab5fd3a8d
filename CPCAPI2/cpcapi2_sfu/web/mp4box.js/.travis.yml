language: node_js
sudo: false
node_js:
- 'node'
before_install:
- echo "//registry.npmjs.org/:_authToken=\${NPM_TOKEN}" > .npmrc
- "export CHROME_BIN=chromium-browser"
- "export DISPLAY=:99.0"
- "sh -e /etc/init.d/xvfb start"
- sleep 3 # give xvfb some time to start
before_script:
- npm install -g grunt-cli
deploy:
  - provider: releases
    api_key:
      secure: FJF3zi4Rk+sHkBsRyNF7IanjGgcQvRX87Mvykp6Afd8t/B+WeDfbxGrqyyHjANgDcUSquFoJZlIt8lQ+Jy8V0rog9M1be+HpENOHOnyYnnrTrgL+ZodZrYJ5Mfbjdd451MTZCcxAFGyCxBvYz/pUWA0wDq9/7WFmF0hGVWevAio=
    file:
      - dist/mp4box.all.js
      - dist/mp4box.all.min.js
      - dist/mp4box.all.min.js.map
      - dist/mp4box.simple.js
      - dist/mp4box.simple.min.js
      - dist/mp4box.simple.min.js.map
    skip_cleanup: true
    on:
      tags: true
  - provider: npm
    email: <EMAIL>
    api_key:
      secure: qT37keNDM4h+9o7XvnCKDF/Hxt+zAcUtaqI78EHu77J22tWKFW45MJw8iL6yyJCit7kRyYAd/YSgk1LwwdJQwlWmuDIYYiF27NfGeLD+md1OlaPztUNhO9h6mm1jjSSYhhMEf3p2ugNmFih2EkXvodYcvhEpJyAMAeSU4jdmtGk=
    skip_cleanup: true
    on:
      tags: true

