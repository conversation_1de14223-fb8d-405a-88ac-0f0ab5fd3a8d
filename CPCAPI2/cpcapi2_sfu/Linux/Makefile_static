ARCH = $(shell arch)
# CXX = /opt/gcc-4.8.2/bin/g++
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS =
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -DCPCAPI2_STATIC_LIB -DUSE_IPV6 -DUSE_ARES -DASIO_DISABLE_IOCP -DUSE_SSL -DUSE_DTLS -DLEAK_CHECK -DASIO_ENABLE_CANCELIO -DBOOST_ASIO_ENABLE_CANCELIO -DCPCAPI2_AUTO_TEST -D__MSRP_FLOW_C__ -DRESIP_USE_STL_STREAMS -DCPCAPI2_INCLUDE_UNRELEASED_HEADERS -DCPSI_MEDIA_CODEC_SPEEX -DCPCAPI2_NO_INTERNAL_PROXY -DXMLSEC_NO_XSLT -DXMLSEC_CRYPTO_OPENSSL -DXMLSEC_STATIC
CC = $(CXX)
CPCAPI2_ROOT = ../..
CPCAPI2_IMPL = $(CPCAPI2_ROOT)/CPCAPI2/impl
CPCAPI2_INTERFACE = $(CPCAPI2_ROOT)/CPCAPI2/interface
SHARED_DIR = $(CPCAPI2_ROOT)/shared
EXTERNAL_DIR = $(CPCAPI2_ROOT)/external
INCLUDE_DIRS = \
	-I$(EXTERNAL_DIR)/boost/include \
	-I$(EXTERNAL_DIR)/libmsrp \
	-I$(EXTERNAL_DIR)/libxml/include \
	-I$(EXTERNAL_DIR)/OpenSSL/openssl-current/include \
	-I$(EXTERNAL_DIR)/curl/curl-linux/include \
	-I$(EXTERNAL_DIR)/v8/include \
	-I$(EXTERNAL_DIR)/websocketpp \
	-I$(EXTERNAL_DIR)/speex/Linux/include \
	-I$(EXTERNAL_DIR)/speex/include \
	-I$(EXTERNAL_DIR)/SILK/interface \
	-I$(EXTERNAL_DIR)/xmlsec/include \
	-I$(SHARED_DIR)/sipfoundry/main \
	-I$(SHARED_DIR)/sipfoundry/main/resip/recon \
	-I$(SHARED_DIR)/sipfoundry/main/reflow \
	-I$(SHARED_DIR)/sipfoundry/main/reTurn \
	-I$(SHARED_DIR)/sipfoundry/main/repro \
	-I$(SHARED_DIR)/libsrtp/srtp/include \
	-I$(SHARED_DIR)/libsrtp/srtp/crypto/include \
	-I$(SHARED_DIR)/WebRTCv/trunk \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc/voice_engine/include \
	-I$(SHARED_DIR)/WebRTCv/trunk/webrtc/video_engine/include \
	-I$(SHARED_DIR)/WebRTCv/trunk/third_party/libyuv/include \
	-I$(SHARED_DIR)/webrtc_recon \
	-I$(SHARED_DIR)/gloox/src \
	-I$(CPCAPI2_ROOT)/CPCAPI2 \
    -I$(CPCAPI2_IMPL) \
	-I$(CPCAPI2_INTERFACE)/public \
	-I$(CPCAPI2_INTERFACE)/experimental
LIBRARY_DIRECTORIES = -L. \
	-L$(EXTERNAL_DIR)/speex/Linux/libspeex/$(ARCH) \
	-L$(EXTERNAL_DIR)/curl/curl-linux/lib \
	-L$(EXTERNAL_DIR)/OpenSSL/openssl-current/lib/Linux/$(ARCH) \
	-L$(EXTERNAL_DIR)/libxml/Linux/libxml/lib/$(ARCH) \
	-L$(EXTERNAL_DIR)/SILK/Linux/$(ARCH) \
	-L$(EXTERNAL_DIR)/xmlsec/lib/linux/$(ARCH) \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/ \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/base \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/common_audio \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/common_video \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/voice_engine \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/video_engine \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/modules \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/modules/remote_bitrate_estimator \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/modules/video_coding/codecs/vp8 \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/modules/video_coding/codecs/utility \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/modules/video_coding/utility \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/webrtc/system_wrappers \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/third_party/libjpeg_turbo \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/third_party/libvpx \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/third_party/libyuv \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/third_party/opus \
	-L$(SHARED_DIR)/WebRTCv/trunk/out_linux/Debug/obj/third_party/openmax_dl/dl \
	-L$(EXTERNAL_DIR)/bv32 \
	-L$(EXTERNAL_DIR)/SILK \
	-L$(EXTERNAL_DIR)/v8/build/Release/lib
WEBRTC_LIBS = \
	-laudio_coding_module \
	-laudio_conference_mixer \
	-laudio_processing \
	-laudio_processing_sse2 \
	-lbitrate_controller \
	-lCNG \
	-lwebrtc_common \
	-lcommon_audio \
	-lcommon_audio_sse2 \
	-lcommon_video \
	-lG711 \
	-lG722 \
	-ljpeg_turbo \
	-lmedia_file \
	-lneteq \
	-lopus \
	-liLBC \
	-lpaced_sender \
	-lPCM16B \
	-lremote_bitrate_estimator \
	-lsystem_wrappers \
	-lvideo_processing \
	-lvideo_processing_sse2 \
	-lvoice_engine \
	-lvpx \
	-lvpx_intrinsics_mmx \
	-lvpx_intrinsics_sse2 \
	-lvpx_intrinsics_ssse3 \
	-lvpx_intrinsics_sse4_1 \
	-ldesktop_capture \
	-ldesktop_capture_differ_sse2 \
	-lwebrtc_i420 \
	-lwebrtc_opus \
	-lwebrtc_vp8 \
	-lwebrtc_utility \
	-lwebrtc \
	-lyuv \
	-laudio_device \
	-lrtp_rtcp \
	-lwebrtc_video_coding \
	-lvideo_coding_utility \
	-lvideo_engine_core \
	-lvideo_render_module \
	-lvideo_capture_module \
	-lrtc_base_approved \
	-lrtc_base \
	-lmetrics_default \
	-laudio_encoder_interface \
	-laudio_decoder_interface \
	-lwebrtc_speex \
	-lwebrtc_SILK \
	-lred \
	-liSAC \
	-liSACFix \
	-lopenmax_dl
LIBRARY_DEPENDENCIES = \
	-Wl,--start-group \
	-lxml \
   -lxmlsec \
	-lcurl \
	$(WEBRTC_LIBS) \
	-lresip_dum \
	-lresip_stack \
	-lresip_recon \
	-lresip_reflow \
	-lresip_reTurn_boost \
	-lresip_reTurn \
	-lresip_rutil \
	-lresip_ares \
	-lssl \
	-lcrypto \
	-lsrtp \
	-lg729 \
	-lspeex \
	-lSKP_SILK_SDK \
	-lz \
	-ldl \
	-lrt \
	-lXext \
	-lX11 \
	-lXfixes \
	-lXdamage \
	-lXcomposite \
	-lgloox \
	-lz \
	-Wl,--end-group

CXXFLAGS = -std=c++11 $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
CFLAGS = $(CXXFLAGS)

SRCS = \
	$(wildcard $(EXTERNAL_DIR)/libmsrp/*.c ) \
	$(wildcard $(EXTERNAL_DIR)/libmsrp/utils/*.c ) \
	$(wildcard $(EXTERNAL_DIR)/libmsrp/transport/*.c ) \
	$(filter-out $(SHARED_DIR)/webrtc_recon/VideoOverlayEffects.cxx, \
		$(wildcard $(SHARED_DIR)/webrtc_recon/*.cxx )) \
	$(wildcard $(SHARED_DIR)/webrtc_recon/qos/QosSocketManager_Linux.cxx ) \
	$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/G729%.cxx, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/amrwb%.cxx, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/DVI4%.cxx, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/G726%.cxx, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/BroadVoice%.cxx, \
		$(wildcard $(SHARED_DIR)/webrtc_recon/codecs/*.cxx )))))) \
	$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/g729%.cc, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/amrwb%.cc, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/dvi4%.cc, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/g726%.cc, \
		$(filter-out $(SHARED_DIR)/webrtc_recon/codecs/bv32%.cc, \
		$(wildcard $(SHARED_DIR)/webrtc_recon/codecs/*.cc )))))) \
	$(wildcard $(CPCAPI2_IMPL)/cpcstl/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/account/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/call/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/capability/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/chat/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/dialogevent/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/iscomposing/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/cpm/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/event/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/filetransfer/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/im/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/licensing/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/licensing/licensekey/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/licensing/timebomb/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/media/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/mwi/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/peerconnection/*.cpp ) \
	$(filter-out $(CPCAPI2_IMPL)/phone/%_Metro.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/phone/%_Android.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/phone/%_iOS.cpp, \
		$(wildcard $(CPCAPI2_IMPL)/phone/*.cpp )))) \
	$(wildcard $(CPCAPI2_IMPL)/presence/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/provision/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/standalonemessaging/*.cpp ) \
	$(filter-out $(CPCAPI2_IMPL)/util/%_Android.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/util/%_BB10.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/util/%_Mac.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/util/%_Metro.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/util/%_Win32.cpp, \
		$(filter-out $(CPCAPI2_IMPL)/util/%_iOS.cpp, \
		$(wildcard $(CPCAPI2_IMPL)/util/*.cpp ))))))) \
	$(wildcard $(CPCAPI2_IMPL)/util/*.cxx ) \
	$(wildcard $(CPCAPI2_IMPL)/webcall/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/busylampfield/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/xmpp/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/sharedcallappearance/*.cpp ) \
	$(wildcard $(CPCAPI2_IMPL)/recording/*.cpp )
#	$(filter-out $(CPCAPI2_ROOT)/cpcapi2_auto_tests/test_framework/*.cpp )
#	$(filter-out $(CPCAPI2_ROOT)/cpcapi2_auto_tests/stdafx.cpp, \
#		$(filter-out $(CPCAPI2_ROOT)/cpcapi2_auto_tests/fileutils_%_tests.cpp, \
#		$(filter-out $(CPCAPI2_ROOT)/cpcapi2_auto_tests/*.cpp)))
OBJS = $(patsubst %.cc,%.o,$(patsubst %.c,%.o,$(patsubst %.cpp,%.o,$(patsubst %.cxx,%.o,$(SRCS)))))
HEADERS = $(patsubst %.cc,%.h,$(patsubst %.c,%.h,$(patsubst %.cpp,%.h,$(patsubst %.cxx,%.h,$(SRCS)))))
LDFLAGS = -pthread $(LIBRARY_DIRECTORIES) $(LIBRARY_DEPENDENCIES)
TARGET = cpcapi2

.PHONY: all
all : thirdparty $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: thirdparty
thirdparty :
	$(MAKE) -f Makefile.g729
	$(MAKE) -f Makefile.libsrtp
	$(MAKE) -f Makefile.libxml
	$(MAKE) -f Makefile.resip_ares
	$(MAKE) -f Makefile.resip_dum
	$(MAKE) -f Makefile.resip_reTurn
	$(MAKE) -f Makefile.resip_reTurn_boost
	$(MAKE) -f Makefile.resip_recon
	$(MAKE) -f Makefile.resip_reflow
	$(MAKE) -f Makefile.resip_rutil
	$(MAKE) -f Makefile.resip_stack
	$(MAKE) -f Makefile.gloox
	( cd $(SHARED_DIR)/WebRTCv ; ./build-linux.sh )

.PHONY: thirdpartyclean
thirdpartyclean :
	$(MAKE) -f Makefile.g729 clean
	$(MAKE) -f Makefile.libsrtp clean
	$(MAKE) -f Makefile.libxml clean
	$(MAKE) -f Makefile.resip_ares clean
	$(MAKE) -f Makefile.resip_dum clean
	$(MAKE) -f Makefile.resip_reTurn clean
	$(MAKE) -f Makefile.resip_reTurn_boost clean
	$(MAKE) -f Makefile.resip_recon clean
	$(MAKE) -f Makefile.resip_reflow clean
	$(MAKE) -f Makefile.resip_rutil clean
	$(MAKE) -f Makefile.resip_stack clean
	$(MAKE) -f Makefile.gloox clean
	( cd $(SHARED_DIR)/WebRTCv ; ./build-linux.sh -clean )

.PHONY: clean
clean : thirdpartyclean
	$(RM) $(OBJS)
	$(RM) $(TARGET)

#$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(CXX) $(OBJS) -o $(TARGET) $(LDFLAGS)

# Rule to generate a .o file from the .cxx file. (non-standard file suffix)
%.o : %.cxx
	$(CXX) -c $(CXXFLAGS) $< -o $@

# Rule to generate a .o file from the .cc file. (non-standard file suffix)
%.o : %.cc
	$(CXX) -c $(CXXFLAGS) $< -o $@
