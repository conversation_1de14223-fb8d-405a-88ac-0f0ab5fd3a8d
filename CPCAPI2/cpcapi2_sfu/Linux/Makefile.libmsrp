ROOT_PATH = ../..
LIBMSRP_PATH = $(ROOT_PATH)/external/libmsrp
CXXFLAGS_WARNINGS = -Wno-deprecated-declarations
CXXFLAGS_OPTIMIZATIONS = 
CXXFLAGS_DEBUG = -g
CXXFLAGS_MACROS = -D__MSRP_FLOW_C__
LDFLAGS = -L.
INCLUDE_DIRS = -I$(LIBMSRP_PATH)
CXXFLAGS = $(CXXFLAGS_WARNINGS) $(CXXFLAGS_OPTIMIZATIONS) $(CXXFLAGS_DEBUG) $(CXXFLAGS_MACROS) $(INCLUDE_DIRS)
LIBMSRP_SRCS = \
   msrp_byte_range.c msrp_content_disposition.c msrp_content_stuff.c  \
   msrp_content_type.c msrp_ext_header.c msrp_failure_report.c msrp_headers.c  \
   msrp_message.c msrp_other_mime_header.c msrp_request.c msrp_request_start.c  \
   msrp_response.c msrp_resp_start.c msrp_session.c msrp_stack.c msrp_status.c  \
   msrp_success_report.c msrp_transaction.c msrp_uri.c transport/msrp_flow.c  \
   transport/msrp_flow_registry.c transport/msrp_listening_point.c utils/msrp_buf.c  \
   utils/msrp_list.c utils/msrp_log.c utils/msrp_map.c utils/msrp_mem.c  \
   utils/msrp_snprintf.c utils/msrp_strcasestr.c utils/msrp_string.c  \
   utils/msrp_strndup.c utils/msrp_tree.c
SRCS = $(LIBMSRP_SRCS:%=$(LIBMSRP_PATH)/%)
OBJS = $(SRCS:%.c=%.o)
HEADERS = $(SRCS:%.c=%.h)
TARGET = libmsrp.a

.PHONY: all
all : $(OBJS) $(TARGET)

.PHONY: rebuild
rebuild : clean all

.PHONY: clean
clean :
	$(RM) $(OBJS)
	$(RM) $(TARGET)

$(OBJS) : $(SRCS)

$(TARGET) : $(OBJS)
	$(AR) rcs $(TARGET) $(OBJS)

# Rule to generate a .o file from the .c file.
%.o : %.c
	$(CC) -c $(CXXFLAGS) $< -o $@
