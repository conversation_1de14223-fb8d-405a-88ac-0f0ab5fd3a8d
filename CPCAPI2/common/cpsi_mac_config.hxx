/*
 *  config.hxx
 *  MACCPSI
 *
 *  Created by helge on 2007/10/23.
 *  Copyright 2007 Helge @ CTPS. All rights reserved.
 *
 *  common settings that go in all precompiled headers (like the stdafx stuff on windows that I never understood)
 *
 */

//#define TARGET_API_MAC_CARBON 	1
#define TARGET_OS_MAC 1
#define TARGET_API_MAC_OSX	1
#define MAC 1


// global flags used in Windows code

#if DEBUG
   #define _DEBUG 1
   #define XS_DEBUG
#else
   #define XS_RELEASE
#endif //DEBUG

#if RELEASE
	#ifndef NDEBUG
		#define NDEBUG
	#endif
#endif //RELEASE

#define _CONSOLE 1

#define USE_ARES 1
#define USE_IPV6 1
#define USE_SSL 1
#define USE_LIBXML 1
#define VONAGE_FIX 1
#define USE_DNS_VIP 1
#define USE_DTLS 1


#define DEFAULT_BRIDGE_MAX_IN_OUTPUTS 10
#define BOOST_ALL_NO_LIB 1
#define LEAK_CHECK 0
#define _CRT_SECURE_NO_WARNINGS 1


#define UNICODE
#define PIC

#define XS_OPTION_MEMORY_TRACKING 0
#define XS_OPTION_STACK_AND_PROFILE_TRACE 0

// defines for CURL

#define BUILDING_LIBCURL 1
#define CURL_STATICLIB 1

// defines for xmlsec
#define XMLSEC_NO_CRYPTO_DYNAMIC_LOADING 1
#define XMLSEC_NO_TMPL_TEST 1
#define XMLSEC_NO_XSLT 1
#define XMLSEC_NO_SIZE_T 1
#define XMLSEC_CRYPTO_OPENSSL 1
#define XMLSEC_CRYPTO "openssl"
#define XMLSEC_STATIC 1

// defines for resiprocate

#define RESIP_APPLE_USE_SYSTEM_TYPES 1
//#define RESIP_USE_KQUEUE_FDSET 1
#define RESIP_USE_STL_STREAMS 1
#define ASIO_ENABLE_CANCELIO 1


// resip and recon do use windows types how nasty of them

typedef unsigned char                   UInt8;
typedef signed char                     SInt8;
typedef unsigned short                  UInt16;
typedef signed short                    SInt16;
typedef unsigned long						 ULONG;
typedef void									 VOID;
#if __LP64__
typedef unsigned int                    UInt32;
typedef signed int                      SInt32;
#else
//typedef unsigned long                   UInt32; // now defined in rutil/compat.hxx
typedef signed long                     SInt32;
#endif
