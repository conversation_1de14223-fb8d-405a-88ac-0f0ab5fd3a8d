#ifndef ANDROID_FILE_ACCESS_H_
#define ANDROID_FILE_ACCESS_H_

#include "JniHelper.h"

#include <fstream>
#include <iostream>
#include <string>

namespace CPCAPI2
{
class AndroidFileAccess : public std::filebuf
{
public:
  AndroidFileAccess() { std::filebuf(); };

  virtual int64_t size();

  // std::filebuf
  virtual AndroidFileAccess* open(const std::string& s, std::ios_base::openmode mode);
  virtual ~AndroidFileAccess() { close(); }

  virtual AndroidFileAccess& operator=(AndroidFileAccess&& rhs) = delete;
  virtual void swap(AndroidFileAccess& rhs) = delete;

  virtual bool is_open() const;
  virtual AndroidFileAccess* close();

protected:
  virtual std::streamsize showmanyc() override;
  virtual int underflow() override;
  virtual int uflow() override;
  virtual int pbackfail(int c = traits_type::eof()) override;
  virtual int overflow(int c = std::istream::traits_type::eof()) override;
  virtual AndroidFileAccess* setbuf(char*, std::streamsize) override { return this; }
  virtual std::streampos seekoff(std::streamoff off, std::ios_base::seekdir way, std::ios_base::openmode which = std::ios_base::in | std::ios_base::out) override;
  virtual std::streampos seekpos(std::streampos pos, std::ios_base::openmode which = std::ios_base::in | std::ios_base::out) override;
  // virtual int sync() override { return 0; } this caused issues with positioned writes (OBELISK-5550)
  // virtual void imbue(const locale& loc);

  // std::streambuf
  virtual std::streamsize xsgetn(char* s, std::streamsize n) override;
  virtual std::streamsize xsputn(const char* s, std::streamsize n) override;

private:
  bool mFileMode = false;
  std::string mFilename = "";
  std::ios_base::openmode mOpenMode = std::ios_base::in;
  int64_t mSize = 0;

  CPCAPI2::Jni::ScopedGlobalRef<jobject> mFileInputStream;
  CPCAPI2::Jni::ScopedGlobalRef<jobject> mFileOutputStream;
  CPCAPI2::Jni::ScopedGlobalRef<jobject> mAssetFileDescriptor;
};
}  // namespace CPCAPI2

#endif  // ANDROID_FILE_ACCESS_H_
