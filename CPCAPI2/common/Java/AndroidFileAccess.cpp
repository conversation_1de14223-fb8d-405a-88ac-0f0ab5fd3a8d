#include "AndroidFileAccess.h"

#include <sys/types.h>
#include <sys/stat.h>

#include "util/LogSubsystems.h"
#include <rutil/Logger.hxx>

#include "JniHelper.h"

#define RESIPROCATE_SUBSYSTEM CPCAPI2::CPCAPI2_Subsystem::FILE_ACCESS

namespace CPCAPI2
{

AndroidFileAccess* AndroidFileAccess::open(const std::string& filename, std::ios_base::openmode mode)
{
  mFilename = filename;
  mOpenMode = mode;
  std::string fileMode;

  if (0 != (mode & std::ios::in))
  {
    fileMode += "r";
  }

  if (0 != (mode & std::ios::out))
  {
    fileMode += "w";
  }

  if (0 != (mode & std::ios::trunc))
  {
    fileMode += "t";
  }

  if (0 != (mode & std::ios::app))
  {
    fileMode += "a";
  }

  if (std::string::npos == filename.find(":"))
  {
    InfoLog(<< "AndroidFileAccess::open using filebuf mode " << filename.c_str());
    mFileMode = true;
    std::filebuf* f = std::filebuf::open(filename, mode);
    if (f == nullptr)
    {
      return nullptr;
    }
  }
  else
  {
    InfoLog(<< "AndroidFileAccess::open using content resolver mode " << filename.c_str());
    mFileMode = false;
    CPCAPI2::Jni::JniThread env;

    CPCAPI2::Jni::ScopedLocalRef<jstring> filenameString = CPCAPI2::Jni::CreateJavaString(filename.c_str());
    CPCAPI2::Jni::ScopedLocalRef<jobject> uri;
    try
    {
      uri.reset(CPCAPI2::Jni::CallStaticObjectMethod("android/net/Uri", "parse", "(Ljava/lang/String;)Landroid/net/Uri;", *filenameString));
    }
    catch (const CPCAPI2::Jni::JniException& e)
    {
      ErrLog(<< "AndroidFileAccess::open error opening file " << e.what());
      mSize = -1;
      return nullptr;
    }

    Jni::ScopedLocalRef<jobject> contentResolver = CPCAPI2::Jni::CallObjectMethod("android/content/Context", CPCAPI2::Jni::GetContext(), "getContentResolver", "()Landroid/content/ContentResolver;");
    if (CPCAPI2::Jni::IsNull(*contentResolver))
    {
      ErrLog(<< "AndroidFileAccess::open error opening file");
      mSize = -2;
      return nullptr;
    }

    try
    {
      Jni::ScopedLocalRef<jstring> fileModeString = CPCAPI2::Jni::CreateJavaString(fileMode.c_str());

      Jni::ScopedLocalRef<jobject> assetFileDescriptor = CPCAPI2::Jni::CallObjectMethod("android/content/ContentResolver", *contentResolver, "openAssetFileDescriptor", "(Landroid/net/Uri;Ljava/lang/String;)Landroid/content/res/AssetFileDescriptor;", *uri, *fileModeString);
      mAssetFileDescriptor = *assetFileDescriptor;
    }
    catch (const CPCAPI2::Jni::JniException& e)
    {
      ErrLog(<< "AndroidFileAccess::open error opening file " << e.what());
      mSize = -3;
      return nullptr;
    }

    mSize = CPCAPI2::Jni::CallLongMethod("android/content/res/AssetFileDescriptor", *mAssetFileDescriptor, "getLength", "()J");

    if (0 != (mode & std::ios_base::in))
    {
      CPCAPI2::Jni::ScopedLocalRef<jobject> fileInputStream = CPCAPI2::Jni::CallObjectMethod("android/content/res/AssetFileDescriptor", *mAssetFileDescriptor, "createInputStream", "()Ljava/io/FileInputStream;");
      mFileInputStream = *fileInputStream;
    }

    if (0 != (mode & std::ios_base::out))
    {
      Jni::ScopedLocalRef<jobject> fileOutputStream = CPCAPI2::Jni::CallObjectMethod("android/content/res/AssetFileDescriptor", *mAssetFileDescriptor, "createOutputStream", "()Ljava/io/FileOutputStream;");
      mFileOutputStream = *fileOutputStream;
    }
  }

  return this;
}

AndroidFileAccess* AndroidFileAccess::close()
{
  if (mFileMode)
  {
    std::filebuf::close();
  }

  CPCAPI2::Jni::JniThread env;
  if (!CPCAPI2::Jni::IsNull(*mFileOutputStream))
  {
    CPCAPI2::Jni::CallVoidMethod("java/io/FileOutputStream", *mFileOutputStream, "close", "()V");
    mFileOutputStream = nullptr;
  }

  if (!CPCAPI2::Jni::IsNull(*mFileInputStream))
  {
    CPCAPI2::Jni::CallVoidMethod("java/io/FileInputStream", *mFileInputStream, "close", "()V");
    mFileInputStream = nullptr;
  }

  if (!CPCAPI2::Jni::IsNull(*mAssetFileDescriptor))
  {
    CPCAPI2::Jni::CallVoidMethod("android/content/res/AssetFileDescriptor", *mAssetFileDescriptor, "close", "()V");
    mAssetFileDescriptor = nullptr;
  }
  return this;
}

bool AndroidFileAccess::is_open() const
{
  if (mFileMode)
  {
    return std::filebuf::is_open();
  }
  else
  {
    return !CPCAPI2::Jni::IsNull(*mFileInputStream) || !CPCAPI2::Jni::IsNull(*mFileOutputStream);
  }
}

int64_t AndroidFileAccess::size()
{
  if (mFileMode)
  {
    struct stat f_stat;
    if (stat(mFilename.c_str(), &f_stat) == 0)
    {
      return f_stat.st_size;
    }
    return -1;
  }
  else
  {
    return mSize;
  }
}

std::streamsize AndroidFileAccess::showmanyc()
{
  if (mFileMode)
  {
    return std::filebuf::showmanyc();
  }
  else
  {
    CPCAPI2::Jni::JniThread env;
    if (!CPCAPI2::Jni::IsNull(*mFileInputStream))
    {
      return CPCAPI2::Jni::CallIntMethod("java/io/FileInputStream", *mFileInputStream, "available", "()I");
    }
    return 0;
  }
}

int AndroidFileAccess::underflow()
{
  if (mFileMode)
  {
    return std::filebuf::underflow();
  }
  else
  {
    // TODO Is rewind needed and supported
    return uflow();
  }
}

int AndroidFileAccess::uflow()
{
  if (mFileMode)
  {
    return std::filebuf::uflow();
  }
  else
  {
    CPCAPI2::Jni::JniThread env;
    if (0 <= showmanyc())
    {
      return std::istream::traits_type::eof();
    }
    return CPCAPI2::Jni::CallIntMethod("java/io/FileInputStream", *mFileInputStream, "read", "()I");
  }
}

int AndroidFileAccess::pbackfail(int c)
{
  if (mFileMode)
  {
    return std::filebuf::pbackfail(c);
  }
  else
  {
    return std::istream::traits_type::eof();
  }
}

int AndroidFileAccess::overflow(int c)
{
  if (mFileMode)
  {
    return std::filebuf::overflow(c);
  }
  else
  {
    CPCAPI2::Jni::JniThread env;
    if (!CPCAPI2::Jni::IsNull(*mFileOutputStream))
    {
      try
      {
        CPCAPI2::Jni::CallVoidMethod("java/io/FileOutputStream", *mFileOutputStream, "write", "(I)V", c);
      }
      catch (const CPCAPI2::Jni::JniException& e)
      {
        ErrLog(<< "AndroidFileAccess::overflow error writing to file " << e.what());
        return std::istream::traits_type::eof();
      }
      return c;
    }
    return std::istream::traits_type::eof();
  }
}

std::streampos AndroidFileAccess::seekoff(std::streamoff off, std::ios_base::seekdir way, std::ios_base::openmode which)
{
  if (mFileMode)
  {
    return std::filebuf::seekoff(off, way, which);
  }
  else
  {
    if (way == std::ios_base::end)
    {
      return mSize - off;
    }
    else if (way == std::ios_base::beg)
    {
      return 0;
    }

    return -1;
  }
}

std::streampos AndroidFileAccess::seekpos(std::streampos pos, std::ios_base::openmode which)
{
  if (mFileMode)
  {
    return std::filebuf::seekpos(pos, which);
  }
  else
  {
    close();
    if (nullptr != this->open(mFilename, mOpenMode))
    {
      return 0;
    }
    ErrLog(<< "AndroidFileAccess::rewind error reopening file");
    return -1;
  }
}

std::streamsize AndroidFileAccess::xsgetn(char* s, std::streamsize n)
{
  if (mFileMode)
  {
    return std::filebuf::xsgetn(s, n);
  }
  else
  {
    CPCAPI2::Jni::JniThread env;
    if (n > 0 && !CPCAPI2::Jni::IsNull(*mFileInputStream))
    {
      CPCAPI2::Jni::ScopedLocalRef<jobject> byteBuffer = env->NewDirectByteBuffer(s, n);
      CPCAPI2::Jni::ScopedLocalRef<jbyteArray> byteArray = env->NewByteArray(n);
      std::streamsize read = CPCAPI2::Jni::CallIntMethod("java/io/FileInputStream", *mFileInputStream, "read", "([B)I", *byteArray);
      CPCAPI2::Jni::ScopedLocalRef<jobject> byteBuffer2 = CPCAPI2::Jni::CallObjectMethod("java/nio/ByteBuffer", *byteBuffer, "put", "([B)Ljava/nio/ByteBuffer;", *byteArray);

      return read;
    }
    return 0;
  }
}

std::streamsize AndroidFileAccess::xsputn(const char* s, std::streamsize n)
{
  if (mFileMode)
  {
    return std::filebuf::xsputn(s, n);
  }
  else
  {
    CPCAPI2::Jni::JniThread env;
    if (n > 0 && !CPCAPI2::Jni::IsNull(*mFileOutputStream))
    {
      CPCAPI2::Jni::ScopedLocalRef<jbyteArray> byteArray = env->NewByteArray(n);
      jbyte* javaBytes = env->GetByteArrayElements(*byteArray, NULL);
      if (nullptr != javaBytes)
      {
        memcpy(javaBytes, s, n);
        env->ReleaseByteArrayElements(*byteArray, javaBytes, 0);
      }
      else
      {
        ErrLog(<< "AndroidFileAccess::xsputn error writing to file");
        return 0;
      }
      CPCAPI2::Jni::CallVoidMethod("java/io/FileOutputStream", *mFileOutputStream, "write", "([B)V", *byteArray);
      return n;
    }
    return 0;
  }
}

}  // namespace CPCAPI2
