/*
* libjingle
* Copyright 2015 Google Inc.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
*  1. Redistributions of source code must retain the above copyright notice,
*     this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright notice,
*     this list of conditions and the following disclaimer in the documentation
*     and/or other materials provided with the distribution.
*  3. The name of the author may not be used to endorse or promote products
*     derived from this software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
* MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
* EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
* WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
* OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
* ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#include "JniHelper.h"

#include "ClassReferenceHolder.h"

#if defined(ANDROID)
#include <asm/unistd.h>
#include <sys/prctl.h>
#include <sys/syscall.h>
#include <unistd.h>
#include <android/log.h>
#endif // defined(ANDROID)

#define LOG_TAG "JniHelper"

namespace CPCAPI2
{
namespace Jni
{

static JavaVM* g_jvm = nullptr;

extern "C" jint JNIEXPORT JNICALL JNI_OnLoad(JavaVM *jvm, void* /*reserved*/)
{
   JNI_CHECK(!g_jvm) << "InitGlobalJniVariables!";
   g_jvm = jvm;
   JNI_CHECK(g_jvm) << "InitGlobalJniVariables handed NULL?";

   LoadGlobalReferenceHolder();

   return JNI_VERSION_1_6;
}

extern "C" void JNIEXPORT JNICALL JNI_OnUnLoad(JavaVM* /*jvm*/, void* /*reserved*/)
{
   FreeGlobalReferenceHolder();
}

// Java references to "null" can only be distinguished as such in C++ by
// creating a local reference, so this helper wraps that logic.
bool IsNull(jobject obj)
{
   if (nullptr == obj)
      return true;
   JniThread env;
   jobject ref = env->NewLocalRef(obj);
   bool ret = (ref == NULL);
   env->DeleteLocalRef(ref);
   return ret;
}

bool IsInstanceOf(const char* className, jobject obj)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "IsInstanceOf: class " << className << " was not found";

  jboolean r = jni->IsInstanceOf(obj, jcls);
  JNI_CHECK(jcls != NULL) << "IsInstanceOf: error checing instance";

  return r == JNI_TRUE;
}


#define JNI_EXCEPTION_CHECK_RETURN if (JNI_TRUE == jni->ExceptionCheck()) { return true; }

bool ExceptionCheck_Internal(JNIEnv* jni, bool canUseFindClass, std::string& outExceptionString)
{
   bool exception = (JNI_TRUE == jni->ExceptionCheck());
   if (exception)
   {
      Jni::ScopedLocalRef<jthrowable> exceptionObj(jni->ExceptionOccurred());
      jni->ExceptionClear();

      // Log exception
      jclass j_class_object, j_class_stringwriter, j_class_printwriter, j_class_throwable;
      if (canUseFindClass)
      {
        // global ref
        j_class_object = FindClass("java/lang/Object");
        j_class_stringwriter = FindClass("java/io/StringWriter");
        j_class_printwriter = FindClass("java/io/PrintWriter");
        j_class_throwable = FindClass("java/lang/Throwable");
      }
      else
      {
        // local ref
        j_class_object = jni->FindClass("java/lang/Object");
        j_class_stringwriter = jni->FindClass("java/io/StringWriter");
        j_class_printwriter = jni->FindClass("java/io/PrintWriter");
        j_class_throwable = jni->FindClass("java/lang/Throwable");
      }

      jmethodID toString = jni->GetMethodID(j_class_object, "toString", "()Ljava/lang/String;"); JNI_EXCEPTION_CHECK_RETURN
      Jni::ScopedLocalRef<jstring> exceptionString((jstring)jni->CallObjectMethod(*exceptionObj, toString));
      const char* exceptionChars = jni->GetStringUTFChars(*exceptionString, JNI_FALSE); JNI_EXCEPTION_CHECK_RETURN

      outExceptionString += exceptionChars;
      jni->ReleaseStringUTFChars(*exceptionString, exceptionChars); JNI_EXCEPTION_CHECK_RETURN

      jmethodID method = jni->GetMethodID(j_class_stringwriter, "<init>", "()V"); JNI_EXCEPTION_CHECK_RETURN
      Jni::ScopedLocalRef<jobject> j_stringwriter(jni->NewObject(j_class_stringwriter, method)); JNI_EXCEPTION_CHECK_RETURN

      method = jni->GetMethodID(j_class_printwriter, "<init>", "(Ljava/io/Writer;)V"); JNI_EXCEPTION_CHECK_RETURN
      Jni::ScopedLocalRef<jobject> j_printwriter(jni->NewObject(j_class_printwriter, method, *j_stringwriter)); JNI_EXCEPTION_CHECK_RETURN

      method = jni->GetMethodID(j_class_throwable, "printStackTrace", "(Ljava/io/PrintWriter;)V"); JNI_EXCEPTION_CHECK_RETURN
      jni->CallVoidMethod(*exceptionObj, method, *j_printwriter); JNI_EXCEPTION_CHECK_RETURN

      method = jni->GetMethodID(j_class_stringwriter, "toString", "()Ljava/lang/String;"); JNI_EXCEPTION_CHECK_RETURN
      Jni::ScopedLocalRef<jstring> callstack((jstring)jni->CallObjectMethod(*j_stringwriter, method));

      const char* callstackChars = jni->GetStringUTFChars(*callstack, JNI_FALSE); JNI_EXCEPTION_CHECK_RETURN
      outExceptionString += "\n\nCallstack:\n";
      outExceptionString += callstackChars;
      jni->ReleaseStringUTFChars(*callstack, callstackChars); JNI_EXCEPTION_CHECK_RETURN

      if (!canUseFindClass)
      {
         jni->DeleteLocalRef(j_class_object);
         jni->DeleteLocalRef(j_class_stringwriter);
         jni->DeleteLocalRef(j_class_printwriter);
         jni->DeleteLocalRef(j_class_throwable);
      }

      if (JNI_TRUE == jni->ExceptionCheck())
      {
         return true;
      }
   }
   return exception;
}

bool ExceptionCheck(const char* tag, JNIEnv* jni, bool canUseFindClass)
{
   std::string s_exception;
   bool exception = ExceptionCheck_Internal(jni, canUseFindClass, s_exception);
   if (exception)
   {
#if defined(ANDROID)
      __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, "%s: %s", tag, s_exception.c_str());
#endif
   }
   return exception;
}

bool ExceptionCheck(std::ostream& stream, JNIEnv* jni, bool canUseFindClass)
{
   std::string s_exception;
   bool exception = ExceptionCheck_Internal(jni, canUseFindClass, s_exception);
   if (exception)
   {
      stream << std::endl << "# JNI exception detected: " << s_exception << std::endl;
   }
   return exception;
}

void RegisterNativeMethodsForJava(const char* className, int methodCount, ...)
{
  JNI_CHECK(g_jvm != NULL) << "RegisterNativeMethodsForJava: JVM was null";

  JNINativeMethod* methods = new JNINativeMethod[methodCount];
  va_list va;
  va_start(va, methodCount);
  for (int i = 0; i < methodCount; i++)
  {
    char* methodName = (char*)va_arg(va, char*);
    char* sig = (char*)va_arg(va, char*);
    void* fn = (void*)va_arg(va, void*);
    methods[i] = { methodName, sig, fn };
  }
  va_end(va);

  JniThread jni;
  JNI_CHECK(*jni != NULL) << "RegisterNativeMethodsForJava: thread wasn't attached to the JVM";

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "RegisterNativeMethodsForJava: class " << className << " was not found";

  jni->RegisterNatives(jcls, methods, methodCount);
  JNI_CHECK_EXCEPTION(jni) << "RegisterNativeMethodsForJava: could not register methods for " << className;

  delete[] methods;
}

bool GetBooleanField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetBooleanField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetBooleanField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, "Z");
  JNI_CHECK_EXCEPTION(jni) << "GetBooleanField: could not find field " << fieldName;

  jboolean b = jni->GetBooleanField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetBooleanField: could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return b == JNI_TRUE;
}

char GetByteField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetByteField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetByteField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, "B");
  JNI_CHECK_EXCEPTION(jni) << "GetByteField: could not find field " << fieldName;

  jbyte b = jni->GetByteField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "error during could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return b;
}

int32_t GetIntField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetIntField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetIntField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, "I");
  JNI_CHECK_EXCEPTION(jni) << "GetIntField: could not find field " << fieldName;

  jint i = jni->GetIntField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "error during could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return i;
}

int64_t GetLongField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetLongField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetLongField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, "J");
  JNI_CHECK_EXCEPTION(jni) << "GetLongField: could not find field " << fieldName;

  jlong j = jni->GetLongField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "error during could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return j;
}

double GetDoubleField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetDoubleField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetDoubleField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, "D");
  JNI_CHECK_EXCEPTION(jni) << "GetDoubleField: could not find field " << fieldName;

  jdouble d = jni->GetDoubleField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "error during could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return d;
}

ScopedLocalRef<jobject> GetObjectField(const char* className, jobject obj, const char* fieldName, const char* fieldSignature)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "GetObjectField: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not get class";

  jfieldID fieldId = FindFieldID(jcls, className, fieldName, fieldSignature);
  JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not find field " << fieldName << " with signature " << fieldSignature;

  jobject o = (jobject)jni->GetObjectField(obj, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not access field " << fieldName;

  jni->DeleteLocalRef(jcls);

  return o;
}

std::string GetStringField(const char* className, jobject obj, const char* fieldName)
{
  JniThread jni;

  ScopedLocalRef<jobject> o = GetObjectField(className, obj, fieldName, "Ljava/lang/String;");
  jstring jstr = (jstring)*o;

  const char* chars = jni->GetStringUTFChars(jstr, NULL);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringField";

  std::string str = std::string(chars, jni->GetStringUTFLength(jstr));
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringField";

  jni->ReleaseStringUTFChars(jstr, chars);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringField";

  return str;
}

ScopedLocalRef<jbyteArray> GetByteArrayField(const char* className, jobject obj, const char* fieldName)
{
   JniThread jni;

   JNI_CHECK(obj != NULL) << "GetObjectField: object was null";
   jclass jcls = jni->GetObjectClass(obj);
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not get class";

   jfieldID fieldId = FindFieldID(jcls, className, fieldName, "[B");
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not find field " << fieldName << " with signature [B";

   jbyteArray o = (jbyteArray)jni->GetObjectField(obj, fieldId);
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not access field " << fieldName;

   jni->DeleteLocalRef(jcls);

   return o;
}

ScopedLocalRef<jobjectArray> GetObjectArrayField(const char* className, jobject obj, const char* fieldName, const char* fieldSignature)
{
   JniThread jni;

   JNI_CHECK(obj != NULL) << "GetObjectField: object was null";
   jclass jcls = jni->GetObjectClass(obj);
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not get class";

   jfieldID fieldId = FindFieldID(jcls, className, fieldName, fieldSignature);
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not find field " << fieldName << " with signature " << fieldSignature;

   jobjectArray o = (jobjectArray)jni->GetObjectField(obj, fieldId);
   JNI_CHECK_EXCEPTION(jni) << "GetObjectField: could not access field " << fieldName;

   jni->DeleteLocalRef(jcls);

   return o;
}



bool GetStaticBooleanField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticBooleanField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "Z");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticBooleanField: could not find static boolean field " << fieldName << " in class " << className;

  jboolean b = jni->GetStaticBooleanField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticBooleanField: could not access static boolean field " << fieldName << " in class " << className;

  return b == JNI_TRUE;
}

char GetStaticByteField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticByteField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "B");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticByteField: could not find static byte field " << fieldName << " in class " << className;

  jbyte b = jni->GetStaticByteField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticByteField: could not access static byte field " << fieldName << " in class " << className;

  return b;
}

int32_t GetStaticIntField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticIntField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "I");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticIntField: could not find static int field " << fieldName << " in class " << className;

  jint i = jni->GetStaticIntField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticIntField: could not access static int field " << fieldName << " in class " << className;

  return i;
}

int64_t GetStaticLongField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticLongField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "J");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticDoubleField: could not find static long field " << fieldName << " in class " << className;

  jlong i = jni->GetStaticLongField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticLongField: could not access static long field " << fieldName << " in class " << className;

  return i;
}

double GetStaticDoubleField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticDoubleField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "D");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticDoubleField: could not find static double field " << fieldName << " in class " << className;

  jdouble d = jni->GetStaticDoubleField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticDoubleField: could not access static double field " << fieldName << " in class " << className;

  return d;
}


ScopedLocalRef<jobject> GetStaticObjectField(const char* className, const char* fieldName, const char* fieldSignature)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticObjectField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, fieldSignature);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not find field " << fieldName << " with signature " << fieldSignature << " in class " << className;

  jobject obj = jni->GetStaticObjectField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not access field " << fieldName << " with signature " << fieldSignature << " in class " << className;

  return obj;
}

std::string GetStaticStringField(const char* className, const char* fieldName)
{
  JniThread jni;

  ScopedLocalRef<jobject> o = GetStaticObjectField(className, fieldName, "Ljava/lang/String;");
  jstring jstr = (jstring)*o;

  const char* chars = jni->GetStringUTFChars(jstr, NULL);
  JNI_CHECK_EXCEPTION(jni) << "Error GetStaticStringField GetStringField";

  std::string str = std::string(chars, jni->GetStringUTFLength(jstr));
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStaticStringField";

  jni->ReleaseStringUTFChars(jstr, chars);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStaticStringField";

  return str;
}

ScopedLocalRef<jbyteArray> GetStaticByteArrayField(const char* className, const char* fieldName)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticObjectField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, "[B");
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not find field " << fieldName << " with signature [B in class " << className;

  jbyteArray obj = (jbyteArray)jni->GetStaticObjectField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not access field " << fieldName << " with signature [B in class " << className;

  return obj;
}

ScopedLocalRef<jobjectArray> GetStaticObjectArrayField(const char* className, const char* fieldName, const char* fieldSignature)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "GetStaticObjectField: class " << className << " was not found";

  jfieldID fieldId = FindStaticFieldID(jcls, className, fieldName, fieldSignature);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not find field " << fieldName << " with signature " << fieldSignature << " in class " << className;

  jobjectArray obj = (jobjectArray)jni->GetStaticObjectField(jcls, fieldId);
  JNI_CHECK_EXCEPTION(jni) << "GetStaticObjectField: could not access field " << fieldName << " with signature " << fieldSignature << " in class " << className;

  return obj;
}

ScopedLocalRef<jobject> NewObject(const char* className, const char* constructorSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "NewObject: class " << className << " was not found";

  jmethodID methodId = FindMethodID(jcls, className, "<init>", constructorSignature);
  JNI_CHECK_EXCEPTION(jni) << "NewObject: could not find constructor with signature " << constructorSignature;

  va_list args;
  va_start(args, constructorSignature);
  jobject obj = jni->NewObjectV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("NewObject: calling constructor (");
    exceptionDescription.append(constructorSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return obj;
}



void CallVoidMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallVoidMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallVoidMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallVoidMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jni->CallVoidMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallVoidMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallVoidMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }
}

bool CallBooleanMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallBooleanMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallBooleanMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallBooleanMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jboolean b = jni->CallBooleanMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallBooleanMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallBooleanMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return b == JNI_TRUE;
}

char CallByteMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallByteMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallByteMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallByteMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  char b = jni->CallByteMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallByteMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallByteMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return b;
}

int32_t CallIntMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallIntMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallIntMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallIntMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  int32_t i = jni->CallIntMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallIntMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallIntMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return i;
}

int64_t CallLongMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallLongMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallLongMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallLongMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  int64_t i = jni->CallLongMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    jthrowable ex = jni->ExceptionOccurred();
    jni->ExceptionClear();

    exceptionDescription.append("CallLongMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallLongMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return i;
}

double CallDoubleMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallDoubleMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallDoubleMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallDoubleMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  double d = jni->CallDoubleMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallDoubleMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallDoubleMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return d;
}

ScopedLocalRef<jobject> CallObjectMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallObjectMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jobject ret = jni->CallObjectMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallObjectMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return ret;
}

std::string CallStringMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallStringMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  ScopedLocalRef<jstring> ret = (jstring)(jni->CallObjectMethodV(obj, methodId, args));
  va_end(args);

  std::string str;
  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStringMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }
  else
  {
    const char* chars = jni->GetStringUTFChars(*ret, NULL);
    JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: Error during GetStringUTFChars";

    str = std::string(chars, jni->GetStringUTFLength(*ret));
    JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: Error during GetStringUTFLength";
    __android_log_print(ANDROID_LOG_INFO, LOG_TAG, "JniHelper::CallStringMethod str %s", str.c_str());

    jni->ReleaseStringUTFChars(*ret, chars);
    JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: Error during ReleaseStringUTFChars";
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallStringMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return str;
}

ScopedLocalRef<jbyteArray> CallByteArrayMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallByteArrayMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallByteArrayMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallByteArrayMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jbyteArray ret = (jbyteArray)jni->CallObjectMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallByteArrayMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallByteArrayMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return ret;
}

ScopedLocalRef<jobjectArray> CallObjectArrayMethod(const char* className, jobject obj, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  JNI_CHECK(obj != NULL) << "CallObjectArrayMethod: object was null";
  jclass jcls = jni->GetObjectClass(obj);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectArrayMethod: could not get class";

  jmethodID methodId = FindMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectArrayMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jobjectArray ret = (jobjectArray)jni->CallObjectMethodV(obj, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallObjectArrayMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);
  }

  jni->DeleteLocalRef(jcls);
  JNI_CHECK_EXCEPTION(jni) << "CallObjectArrayMethod: calling DeleteLocalRef failed";

  if (exception)
  {
    throw JniException(exceptionDescription);
  }

  return ret;
}



void CallStaticVoidMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticVoidMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticVoidMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jni->CallStaticVoidMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticVoidMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }
}

bool CallStaticBooleanMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticBooleanMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticBooleanMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  bool b = jni->CallStaticBooleanMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticBooleanMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return b;
}

char CallStaticByteMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticByteMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticByteMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  char b = jni->CallStaticByteMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticByteMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return b;
}

int32_t CallStaticIntMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticIntMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticIntMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  int32_t i = jni->CallStaticIntMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticIntMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return i;
}


int64_t CallStaticLongMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticLongMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticLongMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  int64_t i = jni->CallStaticIntMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticLongMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return i;
}

double CallStaticDoubleMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticDoubleMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticDoubleMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  double d = jni->CallStaticIntMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticDoubleMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return d;
}

ScopedLocalRef<jobject> CallStaticObjectMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticObjectMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticObjectMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jobject obj = jni->CallStaticObjectMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticObjectMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return obj;
}

std::string CallStaticStringMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticStringMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticStringMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  ScopedLocalRef<jstring> obj = (jstring)jni->CallStaticObjectMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticStringMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
    return "";
  }

  const char* chars = jni->GetStringUTFChars(*obj, NULL);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticStringMethod: Error during GetStringUTFChars";

  std::string str = std::string(chars, jni->GetStringUTFLength(*obj));
  JNI_CHECK_EXCEPTION(jni) << "CallStaticStringMethod: Error during GetStringUTFLength";

  jni->ReleaseStringUTFChars(*obj, chars);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticStringMethod: Error during ReleaseStringUTFChars";

  return str;
}

ScopedLocalRef<jbyteArray> CallStaticByteArrayMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticByteArrayMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticByteArrayMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jbyteArray obj = (jbyteArray)jni->CallStaticObjectMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticByteArrayMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return obj;
}

ScopedLocalRef<jobjectArray> CallStaticObjectArrayMethod(const char* className, const char* methodName, const char* methodSignature, ...)
{
  JniThread jni;

  jclass jcls = FindClass(className);
  JNI_CHECK(jcls != NULL) << "CallStaticObjectArrayMethod: class " << className << " was not found";

  jmethodID methodId = FindStaticMethodID(jcls, className, methodName, methodSignature);
  JNI_CHECK_EXCEPTION(jni) << "CallStaticObjectArrayMethod: could not find method " << methodName << " with signature " << methodSignature;

  va_list args;
  va_start(args, methodSignature);
  jobjectArray obj = (jobjectArray)jni->CallStaticObjectMethodV(jcls, methodId, args);
  va_end(args);

  bool exception = jni->ExceptionCheck();
  std::string exceptionDescription;
  if (exception)
  {
    exceptionDescription.append("CallStaticObjectArrayMethod: calling method ");
    exceptionDescription.append(methodName);
    exceptionDescription.append(" (");
    exceptionDescription.append(methodSignature);
    exceptionDescription.append(") failed -- ");
    ExceptionCheck_Internal(*jni, true, exceptionDescription);

    throw JniException(exceptionDescription);
  }

  return obj;
}



jsize GetArrayLength(jarray array)
{
  JniThread jni;

  jsize size = jni->GetArrayLength(array);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetArrayLength";

  return size;
}

ScopedLocalRef<jobject> GetObjectArrayElement(jobjectArray array, jsize index)
{
  JniThread jni;

  jobject obj = jni->GetObjectArrayElement(array, index);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetObjectArrayElement";

  return obj;
}

std::string GetStringArrayElement(jobjectArray array, jsize index)
{
  JniThread jni;

  ScopedLocalRef<jstring> jstr = reinterpret_cast<jstring>(jni->GetObjectArrayElement(array, index));
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringArrayElement";

  const char* chars = jni->GetStringUTFChars(*jstr, NULL);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringArrayElement";

  std::string str = std::string(chars, jni->GetStringUTFLength(*jstr));
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringArrayElement";

  jni->ReleaseStringUTFChars(*jstr, chars);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetStringArrayElement";

  return str;
}



ScopedLocalRef<jobject> NewDirectByteBuffer(void* address, jlong capacity)
{
  JniThread jni;

  jobject o = jni->NewDirectByteBuffer(address, capacity);
  JNI_CHECK_EXCEPTION(jni) << "Error during NewDirectByteBuffer";

  return o;
}

void* GetDirectBufferAddress(jobject buffer)
{
  JniThread jni;

  void* a = jni->GetDirectBufferAddress(buffer);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetDirectBufferAddress";

  return a;
}

int64_t GetDirectBufferCapacity(jobject buffer)
{
  JniThread jni;

  int64_t i = jni->GetDirectBufferCapacity(buffer);
  JNI_CHECK_EXCEPTION(jni) << "Error during GetDirectBufferCapacity";

  return i;
}



#if defined(ANDROID)
// Return thread ID as a string.
static std::string GetThreadId()
{
   char buf[21];  // Big enough to hold a kuint64max plus terminating NULL.
   JNI_CHECK_LT((size_t)snprintf(buf, sizeof(buf), "%ld",
      static_cast<long>(syscall(__NR_gettid))),
      sizeof(buf))
      << "Thread id is bigger than uint64??";
   return std::string(buf);
}
// Return the current thread's name.
static std::string GetThreadName()
{
   char name[17] = { 0 };
   if (prctl(PR_GET_NAME, name) != 0)
      return std::string("<noname>");
   return std::string(name);
}
#endif

JniThread::JniThread()
{
  if (NULL == g_jvm)
     return;

  void* jni = NULL;
  jint status = g_jvm->GetEnv(&jni, JNI_VERSION_1_6);
  JNI_CHECK(((jni != NULL) && (status == JNI_OK)) || ((jni == NULL) && (status == JNI_EDETACHED))) << "Unexpected GetEnv return: " << status << ":" << jni;
  env_ = reinterpret_cast<JNIEnv*>(jni);

  if (env_)
  {
    attached_ = false;
    return;
  }

  std::string name = "jni";
#if defined(ANDROID)
  name = GetThreadName() + " - " + GetThreadId();
#endif // defined(ANDROID)

// Deal with difference in signatures between Oracle's jni.h and Android's.
#ifdef _JAVASOFT_JNI_H_  // Oracle's jni.h violates the JNI spec!
 void** env = &env_;
#else
 JNIEnv** env = &env_;
#endif // _JAVASOFT_JNI_H_
  JavaVMAttachArgs args;
  args.version = JNI_VERSION_1_6;
  args.name = &name[0];
  args.group = NULL;
  JNI_CHECK(!g_jvm->AttachCurrentThread(env, &args)) << "Failed to attach thread";
  attached_ = true;
}

JniThread::~JniThread()
{
  if (env_ && attached_)
  {
     g_jvm->DetachCurrentThread();
     attached_ = false;
     env_ = nullptr;
  }
}

JniException::JniException(std::string description)
{
  mDescription = description;
#ifdef ANDROID
 __android_log_print(ANDROID_LOG_ERROR, "AndroidJni", "%s", description.c_str());
#endif
}

JavaVM *GetJVM()
{
   JNI_CHECK(g_jvm) << "JNI_OnLoad failed to run?";
   return g_jvm;
}


ScopedLocalRef<jstring> CreateJavaString(const char* str)
{
  if (nullptr == str)
  {
    return nullptr;
  }

  size_t length = strlen(str) + 1;
  char* nstr = new char[length];
  strncpy(nstr, str, length);
  char* ptr = nstr;

  while (*ptr != '\0')
  {
    uint8_t utf8 = *(ptr++);
    // Switch on the high four bits.
    switch (utf8 >> 4)
    {
      case 0x00:
      case 0x01:
      case 0x02:
      case 0x03:
      case 0x04:
      case 0x05:
      case 0x06:
      case 0x07:
        // Bit pattern 0xxx. No need for any extra bytes.
        break;
      case 0x08:
      case 0x09:
      case 0x0a:
      case 0x0b:
        // Bit patterns 10xx, which are illegal start bytes.
        *(--ptr) = '?';
        break;
      case 0x0f:
        // Bit pattern 1111, which might be the start of a 4 byte sequence.
        if ((utf8 & 0x08) == 0)
        {
          // Bit pattern 1111 0xxx, which is the start of a 4 byte sequence.
          utf8 = *(ptr++);
          if ((utf8 & 0xc0) != 0x80)
          {
            ptr -= 2;
            *ptr = '?';
            break;
          }

          utf8 = *(ptr++);
          if ((utf8 & 0xc0) != 0x80)
          {
            ptr -= 3;
            *ptr = '?';
            break;
          }

          utf8 = *(ptr++);
          if ((utf8 & 0xc0) != 0x80)
          {
            ptr -= 4;
            *ptr = '?';
            break;
          }
        }
        else
        {
          *(--ptr) = '?';
          break;
        }
        break;
      case 0x0e:
        // Bit pattern 1110, so there are two additional bytes.
        utf8 = *(ptr++);
        if ((utf8 & 0xc0) != 0x80)
        {
          ptr -= 2;
          *ptr = '?';
          break;
        }

        utf8 = *(ptr++);
        if ((utf8 & 0xc0) != 0x80)
        {
          ptr -= 3;
          *ptr = '?';
          break;
        }
        break;
      case 0x0c:
      case 0x0d:
        // Bit pattern 110x, so there is one additional byte.
        utf8 = *(ptr++);
        if ((utf8 & 0xc0) != 0x80)
        {
          ptr -= 2;
          *ptr = '?';
        }
        break;
    }
  }

  JniThread jni;
  jstring jstr = jni->NewStringUTF(nstr);
  delete[] nstr;
  return jstr;
}

// Given a (UTF-16) jstring return a new UTF-8 native string.
std::string JavaToStdString(const jstring& j_string)
{
   JniThread env;
   const char* chars = env->GetStringUTFChars(j_string, NULL);
   JNI_CHECK_EXCEPTION(env) << "Error during GetStringUTFChars";
   std::string str(chars, env->GetStringUTFLength(j_string));
   JNI_CHECK_EXCEPTION(env) << "Error during GetStringUTFLength";
   env->ReleaseStringUTFChars(j_string, chars);
   JNI_CHECK_EXCEPTION(env) << "Error during ReleaseStringUTFChars";
   return str;
}


// Return a |jlong| that will correctly convert back to |ptr|.  This is needed
// because the alternative (of silently passing a 32-bit pointer to a vararg
// function expecting a 64-bit param) picks up garbage in the high 32 bits.
jlong jlongFromPointer(void* ptr)
{
   static_assert(sizeof(intptr_t) <= sizeof(jlong),
      "Time to rethink the use of jlongs");
   // Going through intptr_t to be obvious about the definedness of the
   // conversion from pointer to integral type.  intptr_t to jlong is a standard
   // widening by the static_assert above.
   jlong ret = reinterpret_cast<intptr_t>(ptr);
   JNI_DCHECK(reinterpret_cast<void*>(ret) == ptr);
   return ret;
}

#if defined(ANDROID)
void dumpReferenceTables()
{
  CallStaticVoidMethod("dalvik/system/VMDebug", "dumpReferenceTables", "()V");
}

static ScopedGlobalRef<jobject> g_context;

void SetContext(jobject context)
{
  g_context = context;
  SetClassLoader(context);
}

jobject GetContext()
{
  return *g_context;
}
#endif

}
}
