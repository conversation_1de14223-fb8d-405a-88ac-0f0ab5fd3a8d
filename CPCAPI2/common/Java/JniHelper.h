/*
* libjingle
* Copyright 2015 Google Inc.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
*  1. Redistributions of source code must retain the above copyright notice,
*     this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright notice,
*     this list of conditions and the following disclaimer in the documentation
*     and/or other materials provided with the distribution.
*  3. The name of the author may not be used to endorse or promote products
*     derived from this software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
* MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
* EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
* WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
* OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
* ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#pragma once
#ifndef JNI_CORE_H
#define JNI_CORE_H

#include <jni.h>

#include <exception>
#include <memory>
#include "JniChecks.h"

namespace CPCAPI2
{
namespace Jni
{
bool ExceptionCheck(const char* tag, JNIEnv* jni, bool canUseFindClass = true);
bool ExceptionCheck(std::ostream& stream, JNIEnv* jni, bool canUseFindClass = true);

// Java references to "null" can only be distinguished as such in C++ by
// creating a local reference, so this helper wraps that logic.
bool IsNull(jobject obj);

bool IsInstanceOf(const char* className, jobject obj);

class JniThread {
 public:
  explicit JniThread();
  ~JniThread();

  JniThread(const JniThread &) = delete;
  JniThread(JniThread &&) = delete;
  JniThread &operator=(const JniThread &) = delete;
  JniThread &operator=(JniThread &&) = delete;

  JNIEnv* operator*() const { return env_; }
  JNIEnv* operator->() const { return env_; }

 private:
  bool attached_;
  JNIEnv* env_;
};

template<class T> class ScopedGlobalRef {
public:
   ScopedGlobalRef() { obj_ = NULL; }
   ScopedGlobalRef(T obj) { NewGlobalRef(obj); }

   ~ScopedGlobalRef() { DeleteGlobalRef(); }

   ScopedGlobalRef(ScopedGlobalRef &&other) = delete;
   ScopedGlobalRef(const ScopedGlobalRef &) = delete;
   ScopedGlobalRef &operator=(const ScopedGlobalRef &) = delete;
   ScopedGlobalRef &operator=(ScopedGlobalRef &&) = delete;

   void operator=(const T &in)
   {
      DeleteGlobalRef();
      NewGlobalRef(in);
   }

   T operator->() const { return obj_; }
   T operator*() const { return obj_; }

private:
   void NewGlobalRef(T obj)
   {
      JniThread env;
      if (IsNull(obj))
      {
        obj_ = nullptr;
      }
      else
      {
        obj_ = reinterpret_cast<T>(env->NewGlobalRef(obj));
        JNI_CHECK_EXCEPTION(env) << "error during NewGlobalRef";
        JNI_CHECK(obj_);
      }
   }

   void DeleteGlobalRef()
   {
      JniThread env;
      if (nullptr != obj_)
      {
        env->DeleteGlobalRef(obj_);
        JNI_CHECK_EXCEPTION(env) << "error during DeleteGlobalRef";
        obj_ = nullptr;
      }
   }

   T obj_;
};

template<class T> class ScopedLocalRef {
public:
   ScopedLocalRef() : obj_(NULL) {}
   ScopedLocalRef(T obj) : obj_(obj) { }
   ScopedLocalRef(ScopedLocalRef&& other)
   {
     if (this != &other)
     {
       DeleteLocalRef();
       this->obj_ = other.obj_;

       other.obj_ = nullptr;
     }
   }


   ~ScopedLocalRef() { DeleteLocalRef(); }

   ScopedLocalRef(const ScopedLocalRef &) = delete;
   ScopedLocalRef &operator=(const ScopedLocalRef &) = delete;
   ScopedLocalRef &operator=(ScopedLocalRef &&) = delete;

   void operator=(const T &in)
   {
      DeleteLocalRef();
      obj_ = in;
   }

   void reset(ScopedLocalRef<T> &&in)
   {
      DeleteLocalRef();
      obj_ = in.obj_;
      in.obj_ = nullptr;
   }

   T operator->() const { return obj_; }
   T operator*() const { return obj_; }

private:
   void DeleteLocalRef()
   {
      JniThread env;
      if (nullptr != obj_)
      {
         env->DeleteLocalRef(obj_);
         JNI_CHECK_EXCEPTION(env) << "error during DeleteLocalRef";
         obj_ = nullptr;
      }
   }

   T obj_;
};

bool                         GetBooleanField               (const char* className, jobject obj, const char* fieldName);
char                         GetByteField                  (const char* className, jobject obj, const char* fieldName);
int32_t                      GetIntField                   (const char* className, jobject obj, const char* fieldName);
int64_t                      GetLongField                  (const char* className, jobject obj, const char* fieldName);
double                       GetDoubleField                (const char* className, jobject obj, const char* fieldName);
ScopedLocalRef<jobject>      GetObjectField                (const char* className, jobject obj, const char* fieldName, const char* fieldSignature);
std::string                  GetStringField                (const char* className, jobject obj, const char* fieldName);
ScopedLocalRef<jbyteArray>   GetByteArrayField             (const char* className, jobject obj, const char* fieldName);
ScopedLocalRef<jobjectArray> GetObjectArrayField           (const char* className, jobject obj, const char* fieldName, const char* fieldSignature);

bool                         GetStaticBooleanField         (const char* className, const char* fieldName);
char                         GetStaticByteField            (const char* className, const char* fieldName);
int32_t                      GetStaticIntField             (const char* className, const char* fieldName);
int64_t                      GetStaticLongField            (const char* className, const char* fieldName);
double                       GetStaticDoubleField          (const char* className, const char* fieldName);
ScopedLocalRef<jobject>      GetStaticObjectField          (const char* className, const char* fieldName, const char* fieldSignature);
std::string                  GetStaticStringField          (const char* className, const char* fieldName);
ScopedLocalRef<jbyteArray>   GetStaticByteArrayField       (const char* className, const char* fieldName);
ScopedLocalRef<jobjectArray> GetStaticObjectArrayField     (const char* className, const char* fieldName, const char* fieldSignature);

// Returns a local ref. Must be turned into a global ref (NewGlobalRef) to survive past current method execution
ScopedLocalRef<jobject>      NewObject                     (const char* className, const char* constructorSignature, ...);

void                         CallVoidMethod                (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
bool                         CallBooleanMethod             (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
char                         CallByteMethod                (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
int32_t                      CallIntMethod                 (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
int64_t                      CallLongMethod                (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
double                       CallDoubleMethod              (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jobject>      CallObjectMethod              (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
std::string                  CallStringMethod              (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jbyteArray>   CallByteArrayMethod           (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jobjectArray> CallObjectArrayMethod         (const char* className, jobject obj, const char* methodName, const char* methodSignature, ...);

void                         CallStaticVoidMethod          (const char* className, const char* methodName, const char* methodSignature, ...);
bool                         CallStaticBooleanMethod       (const char* className, const char* methodName, const char* methodSignature, ...);
char                         CallStaticByteMethod          (const char* className, const char* methodName, const char* methodSignature, ...);
int32_t                      CallStaticIntMethod           (const char* className, const char* methodName, const char* methodSignature, ...);
int64_t                      CallStaticLongMethod          (const char* className, const char* methodName, const char* methodSignature, ...);
double                       CallStaticDoubleMethod        (const char* className, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jobject>      CallStaticObjectMethod        (const char* className, const char* methodName, const char* methodSignature, ...);
std::string                  CallStaticStringMethod        (const char* className, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jbyteArray>   CallStaticByteArrayMethod     (const char* className, const char* methodName, const char* methodSignature, ...);
ScopedLocalRef<jobjectArray> CallStaticObjectArrayMethod   (const char* className, const char* methodName, const char* methodSignature, ...);

jsize                        GetArrayLength                (jarray array);
ScopedLocalRef<jobject>      GetObjectArrayElement         (jobjectArray array, jsize index);
std::string                  GetStringArrayElement         (jobjectArray array, jsize index);

ScopedLocalRef<jobject>      NewDirectByteBuffer           (void* address, jlong capacity);
void*                        GetDirectBufferAddress        (jobject buffer);
int64_t                      GetDirectBufferCapacity       (jobject buffer);

void RegisterNativeMethodsForJava(const char* className, int methodCount, ...);

class JniException : public std::exception
{
public:
   JniException(std::string description);

   virtual const char* what() const noexcept { return mDescription.c_str(); }

private:
   std::string mDescription;
};

extern "C" {
   JavaVM* GetJVM();
}

ScopedLocalRef<jstring> CreateJavaString(const char* str);
std::string JavaToStdString(const jstring& jstr);

// Return a |jlong| that will correctly convert back to |ptr|.  This is needed
// because the alternative (of silently passing a 32-bit pointer to a vararg
// function expecting a 64-bit param) picks up garbage in the high 32 bits.
jlong jlongFromPointer(void* ptr);


#ifdef ANDROID
// Dumps JNI reference tables. Note! The local reference table dumped will depend on which
// thread this function is invoked on.
void dumpReferenceTables();

void SetContext(jobject context);
jobject GetContext();
#endif // ANDROID

}
}

#endif //JNI_CORE_H
