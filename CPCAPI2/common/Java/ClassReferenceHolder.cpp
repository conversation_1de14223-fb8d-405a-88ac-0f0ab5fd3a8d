/*
* libjingle
* Copyright 2015 Google Inc.
*
* Redistribution and use in source and binary forms, with or without
* modification, are permitted provided that the following conditions are met:
*
*  1. Redistributions of source code must retain the above copyright notice,
*     this list of conditions and the following disclaimer.
*  2. Redistributions in binary form must reproduce the above copyright notice,
*     this list of conditions and the following disclaimer in the documentation
*     and/or other materials provided with the distribution.
*  3. The name of the author may not be used to endorse or promote products
*     derived from this software without specific prior written permission.
*
* THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR IMPLIED
* WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
* MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO
* EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
* SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
* PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
* OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
* WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
* OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF
* ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*
*/
#include "ClassReferenceHolder.h"
#include "JniHelper.h"

#include <map>
#include <mutex>
#include <string>
#include <tuple>

namespace CPCAPI2
{
namespace Jni
{
   // ReferenceHolder holds global reference to Java classes in app/webrtc.
   class ReferenceHolder
   {
   public:
      ReferenceHolder();
      void SetClassLoader(jobject context);

      jclass GetClass(const char* name, bool allowNull = false);
      jfieldID GetFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature);
      jfieldID GetStaticFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature);
      jmethodID GetMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature);
      jmethodID GetStaticMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature);

   private:
      void LoadClass(const std::string name);

      ScopedGlobalRef<jobject> classloader_;
      std::map<std::string, ScopedGlobalRef<jclass>> classes_;
      std::mutex classes_lock_;
      std::map<std::tuple<std::string, std::string, std::string>, jfieldID> fields_;
      std::mutex fields_lock_;
      std::map<std::tuple<std::string, std::string, std::string>, jfieldID> static_fields_;
      std::mutex static_fields_lock_;
      std::map<std::tuple<std::string, std::string, std::string>, jmethodID> methods_;
      std::mutex methods_lock_;
      std::map<std::tuple<std::string, std::string, std::string>, jmethodID> static_methods_;
      std::mutex static_methods_lock_;
   };

   ReferenceHolder::ReferenceHolder()
   {
   }

   void ReferenceHolder::SetClassLoader(jobject context)
   {
      JniThread jni;

      jclass jcls = jni->GetObjectClass(context);
      JNI_CHECK_EXCEPTION(jni) << "SetClassLoader: could not get class";

      jmethodID methodId = jni->GetMethodID(jcls, "getClassLoader", "()Ljava/lang/ClassLoader;");
      JNI_CHECK_EXCEPTION(jni) << "LoadClass: getting method id failed";

      jobject classloader = jni->CallObjectMethod(context, methodId);
      JNI_CHECK_EXCEPTION(jni) << "LoadClass: getting class loader failed";

      classloader_ = classloader;

      jni->DeleteLocalRef(jcls);
      JNI_CHECK_EXCEPTION(jni) << "LoadClass: calling DeleteLocalRef failed";
   }

   jclass ReferenceHolder::GetClass(const char* name, bool allowNull)
   {
      jclass jcls = nullptr;
      {
         std::lock_guard<std::mutex> guard(classes_lock_);
         auto it = classes_.find(name);
         if (it == classes_.end())
         {
            LoadClass(name);
            it = classes_.find(name);
         }

         if (classes_.end() != it)
         {
            jcls = *(it->second);
         }
      }

      if (!allowNull)
      {
         JNI_CHECK(nullptr != jcls) << "Unexpected GetClass() call for: " << name;
      }

      return jcls;
   }

   void ReferenceHolder::LoadClass(const std::string name)
   {
      JniThread jni;

      if (IsNull(*classloader_))
      {
         jclass localRef = jni->FindClass(name.c_str());
         if (!localRef)
         {
            ExceptionCheck(std::string("FindClass: ").append(name).c_str(), *jni, false);
            return;
         }
         JNI_CHECK_EXCEPTION(jni) << "error during FindClass: " << name;
         JNI_CHECK(localRef) << name;

         classes_[name] = localRef;

         jni->DeleteLocalRef(localRef);
      }
      else
      {
         std::string javaName = name;
         std::replace(javaName.begin(), javaName.end(), '/', '.');

         CPCAPI2::Jni::ScopedLocalRef<jstring> className = CPCAPI2::Jni::CreateJavaString(javaName.c_str());
         JNI_CHECK(*jni != NULL) << "LoadClass: thread wasn't attached to the JVM";

         jclass jclassloaderClass = jni->GetObjectClass(*classloader_);
         JNI_CHECK_EXCEPTION(jni) << "LoadClass: could not get class";

         static jmethodID methodId = jni->GetMethodID(jclassloaderClass, "loadClass", "(Ljava/lang/String;)Ljava/lang/Class;");

         jclass jcls = (jclass)jni->CallObjectMethod(*classloader_, methodId, *className);

         JNI_CHECK_EXCEPTION(jni) << "LoadClass: getting class";
         jni->ExceptionClear();

         classes_[name] = jcls;

         jni->DeleteLocalRef(jcls);
         JNI_CHECK_EXCEPTION(jni) << "LoadClass: calling DeleteLocalRef failed";

         jni->DeleteLocalRef(jclassloaderClass);
         JNI_CHECK_EXCEPTION(jni) << "LoadClass: calling DeleteLocalRef failed";
      }
   }

   jfieldID ReferenceHolder::GetFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature)
   {
      std::tuple<std::string, std::string, std::string> key(className, fieldName, fieldSignature);
      std::lock_guard<std::mutex> guard(fields_lock_);
      auto it = fields_.find(key);
      if (it == fields_.end())
      {
         JniThread jni;
         jfieldID field = jni->GetFieldID(jcls, fieldName, fieldSignature);
         JNI_CHECK_EXCEPTION(jni) << "GetFieldID: could not find field " << fieldName << " with signature " << fieldSignature;

         fields_[key] = field;

         it = fields_.find(key);
      }

      return it != fields_.end() ? it->second : nullptr;
   }

   jfieldID ReferenceHolder::GetStaticFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature)
   {
      std::tuple<std::string, std::string, std::string> key(className, fieldName, fieldSignature);
      std::lock_guard<std::mutex> guard(static_fields_lock_);
      auto it = static_fields_.find(key);
      if (it == static_fields_.end())
      {
         JniThread jni;
         jfieldID field = jni->GetStaticFieldID(jcls, fieldName, fieldSignature);
         JNI_CHECK_EXCEPTION(jni) << "GetStaticFieldID: could not find static field " << fieldName << " with signature " << fieldSignature;

         static_fields_[key] = field;
         it = static_fields_.find(key);
      }

      return it != static_fields_.end() ? it->second : nullptr;
   }

   jmethodID ReferenceHolder::GetMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature)
   {
      std::tuple<std::string, std::string, std::string> key(className, methodName, methodSignature);
      std::lock_guard<std::mutex> guard(methods_lock_);
      auto it = methods_.find(key);
      if (it == methods_.end())
      {
         JniThread jni;
         jmethodID method = jni->GetMethodID(jcls, methodName, methodSignature);
         JNI_CHECK_EXCEPTION(jni) << "GetMethodID: could not find method " << methodName << " with signature " << methodSignature;

         methods_[key] = method;
         it = methods_.find(key);
      }

      return it != methods_.end() ? it->second : nullptr;
   }

   jmethodID ReferenceHolder::GetStaticMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature)
   {
      std::tuple<std::string, std::string, std::string> key(className, methodName, methodSignature);
      std::lock_guard<std::mutex> guard(static_methods_lock_);
      auto it = static_methods_.find(key);
      if (it == static_methods_.end())
      {
         JniThread jni;
         jmethodID method = jni->GetStaticMethodID(jcls, methodName, methodSignature);
         JNI_CHECK_EXCEPTION(jni) << "GetStaticMethodID: could not find static method " << methodName << " with signature " << methodSignature;

         static_methods_[key] = method;
         it = static_methods_.find(key);
      }

      return it != static_methods_.end() ? it->second : nullptr;
   }

   // Allocated in LoadGlobalReferenceHolder(),
   // freed in FreeGlobalReferenceHolder().
   static ReferenceHolder* g_class_reference_holder = nullptr;

   void LoadGlobalReferenceHolder()
   {
      JNI_CHECK(g_class_reference_holder == nullptr);
      g_class_reference_holder = new ReferenceHolder();
   }

   void SetClassLoader(jobject context)
   {
      g_class_reference_holder->SetClassLoader(context);
   }

   void FreeGlobalReferenceHolder()
   {
      delete g_class_reference_holder;
      g_class_reference_holder = nullptr;
   }

   // Returns a global reference guaranteed to be valid for the lifetime of the
   // process.
   jclass FindClass(const char* name, bool allowNull)
   {
      return g_class_reference_holder->GetClass(name, allowNull);
   }

   jfieldID FindFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature)
   {
      return g_class_reference_holder->GetFieldID(jcls, className, fieldName, fieldSignature);
   }

   jfieldID FindStaticFieldID(jclass jcls, const char* className, const char* fieldName, const char* fieldSignature)
   {
      return g_class_reference_holder->GetStaticFieldID(jcls, className, fieldName, fieldSignature);
   }

   jmethodID FindMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature)
   {
      return g_class_reference_holder->GetMethodID(jcls, className, methodName, methodSignature);
   }

   jmethodID FindStaticMethodID(jclass jcls, const char* className, const char* methodName, const char* methodSignature)
   {
      return g_class_reference_holder->GetStaticMethodID(jcls, className, methodName, methodSignature);
   }
}  // namespace Jni
}  // namespace CPCAPI2
