// THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
// ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
// THE IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
// PARTICULAR PURPOSE.
//
// Copyright (c) Microsoft Corporation. All rights reserved.

#include "WinRTEmulation.h"

#include <assert.h>
#include <vector>
#include <set>
#include <synchapi.h>
#include <map>

class mutex
{
  CRITICAL_SECTION mLock;
  bool inited = false;

public:
	mutex()
	{
		if(0 != InitializeCriticalSectionEx(&mLock, 0, 0))
      inited = true;
	};

	~mutex()
	{
		DeleteCriticalSection(&mLock);
	}

	void lock()
	{
    if (!inited)
    {
      if (0 != InitializeCriticalSectionEx(&mLock, 0, 0))
        inited = true;
    }

    if (!inited)
      assert(0);

		EnterCriticalSection(&mLock);
	};

	void unlock()
	{
    if (!inited)
      assert(0);

		LeaveCriticalSection(&mLock);
	};

};

class lock_guard {
	mutex* mLock;

public:
	lock_guard(mutex* aLock)
	{
    mLock = aLock;
    mLock->lock();
	}

	~lock_guard()
	{
	  mLock->unlock();
	}
};

using namespace std;
using namespace Platform;
using namespace Windows::Foundation;
using namespace Windows::System::Threading;

// Stored data for CREATE_SUSPENDED and ResumeThread.
struct PendingThreadInfo
{
	LPTHREAD_START_ROUTINE lpStartAddress;
	LPVOID lpParameter;
	HANDLE completionEvent;
	int nPriority;
};

static map<HANDLE, PendingThreadInfo> pendingThreads;
static mutex* pendingThreadsLock = NULL;


// Thread local storage.
typedef vector<void*> ThreadLocalData;

static __declspec(thread) ThreadLocalData* currentThreadData = nullptr;
static set<ThreadLocalData*> allThreadData;
static DWORD nextTlsIndex = 0;
static vector<DWORD> freeTlsIndices;
static mutex* tlsAllocationLock = NULL;

static volatile long nextThreadID = 0;

// Converts a Win32 thread priority to WinRT format.
static WorkItemPriority GetWorkItemPriority(int nPriority)
{
	if (nPriority < 0)
		return WorkItemPriority::Low;
	else if (nPriority > 0)
		return WorkItemPriority::High;
	else
		return WorkItemPriority::Normal;
}


// Helper shared between CreateThread and ResumeThread.
static void StartThread(LPTHREAD_START_ROUTINE lpStartAddress, LPVOID lpParameter, HANDLE completionEvent, int nPriority)
{
	auto workItemHandler = ref new WorkItemHandler([=](IAsyncAction^)
	{
		// Run the user callback.
		try
		{
			lpStartAddress(lpParameter);
		}
    catch (...)
    {
      // Clean up any TLS allocations made by this thread.
      TlsShutdown();
      
      throw;
    }

		// Clean up any TLS allocations made by this thread.
		TlsShutdown();

		// Signal that the thread has completed.
		SetEvent(completionEvent);
		CloseHandle(completionEvent);

	}, CallbackContext::Any);

	ThreadPool::RunAsync(workItemHandler, GetWorkItemPriority(nPriority), WorkItemOptions::TimeSliced);
}

_Use_decl_annotations_ HANDLE WINAPI CreateThread(LPSECURITY_ATTRIBUTES unusedThreadAttributes, SIZE_T unusedStackSize, LPTHREAD_START_ROUTINE lpStartAddress, LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD threadID)
{
  return CreateThreadWithPriority(unusedThreadAttributes, unusedStackSize, lpStartAddress, lpParameter, dwCreationFlags, threadID, 0);
}

// Priority < 0 = Low, 0 = Normal, Priority > 0 = High
_Use_decl_annotations_ HANDLE WINAPI CreateThreadWithPriority(LPSECURITY_ATTRIBUTES unusedThreadAttributes, SIZE_T unusedStackSize, LPTHREAD_START_ROUTINE lpStartAddress, LPVOID lpParameter, DWORD dwCreationFlags, LPDWORD threadID, int priority)
{
  if (NULL == pendingThreadsLock)
    pendingThreadsLock = new mutex();

	// Validate parameters.
	assert(unusedThreadAttributes == nullptr);
	//assert(unusedStackSize == 0);
	assert((dwCreationFlags & ~CREATE_SUSPENDED) == 0);
	//assert(unusedThreadId == nullptr);
  int tID = InterlockedIncrement(&nextThreadID);
  if (nullptr != threadID)
    (*threadID) = tID;

	// Create a handle that will be signalled when the thread has completed.
	HANDLE threadHandle = CreateEventEx(nullptr, nullptr, CREATE_EVENT_MANUAL_RESET, EVENT_ALL_ACCESS);

	if (!threadHandle)
		return nullptr;

	// Make a copy of the handle for internal use. This is necessary because
	// the caller is responsible for closing the handle returned by CreateThread,
	// and they may do that before or after the thread has finished running.
	HANDLE completionEvent;
	
	if (!DuplicateHandle(GetCurrentProcess(), threadHandle, GetCurrentProcess(), &completionEvent, 0, false, DUPLICATE_SAME_ACCESS))
	{
		CloseHandle(threadHandle);
		return nullptr;
	}

	try
	{
		if (dwCreationFlags & CREATE_SUSPENDED)
		{
			// Store info about a suspended thread.
			PendingThreadInfo info;

			info.lpStartAddress = lpStartAddress;
			info.lpParameter = lpParameter;
			info.completionEvent = completionEvent;
			info.nPriority = priority;

			lock_guard lock(pendingThreadsLock);

			pendingThreads[threadHandle] = info;
		}
		else
		{
			// Start the thread immediately.
			StartThread(lpStartAddress, lpParameter, completionEvent, 0);
		}

		return threadHandle;
	}
	catch (...)
	{
		// Clean up if thread creation fails.
		CloseHandle(threadHandle);
		CloseHandle(completionEvent);

		return nullptr;
	}
}


_Use_decl_annotations_ DWORD WINAPI ResumeThread(HANDLE hThread)
{
  assert(pendingThreadsLock != NULL);
	lock_guard lock(pendingThreadsLock);

	// Look up the requested thread.
	auto threadInfo = pendingThreads.find(hThread);

	if (threadInfo == pendingThreads.end())
	{
		// Can only resume threads while they are in CREATE_SUSPENDED state.
		assert(false);
		return (DWORD)-1;
	}

	// Start the thread.
	try
	{
		PendingThreadInfo& info = threadInfo->second;

		StartThread(info.lpStartAddress, info.lpParameter, info.completionEvent, info.nPriority);
	}
	catch (...)
	{
		return (DWORD)-1;
	}

	// Remove this thread from the pending list.
	pendingThreads.erase(threadInfo);

	return 0;
}


_Use_decl_annotations_ BOOL WINAPI SetThreadPriority(HANDLE hThread, int nPriority)
{
  assert(pendingThreadsLock != NULL);
	lock_guard lock(pendingThreadsLock);

	// Look up the requested thread.
	auto threadInfo = pendingThreads.find(hThread);

	if (threadInfo == pendingThreads.end())
	{
		// Can only set priority on threads while they are in CREATE_SUSPENDED state.
		//assert(false);
		return false;
	}

	// Store the new priority.
	threadInfo->second.nPriority = nPriority;

	return true;
}

_Use_decl_annotations_ VOID WINAPI Sleep(DWORD dwMilliseconds)
{
	SleepEx(dwMilliseconds, FALSE);
}

_Use_decl_annotations_ VOID WINAPI SleepEx(DWORD dwMilliseconds, BOOL bAlertable)
{
	static HANDLE singletonEvent = nullptr;

	HANDLE sleepEvent = singletonEvent;

	// Demand create the event.
	if (!sleepEvent)
	{
		sleepEvent = CreateEventEx(nullptr, nullptr, CREATE_EVENT_MANUAL_RESET, EVENT_ALL_ACCESS);

		if (!sleepEvent)
			return;

		HANDLE previousEvent = InterlockedCompareExchangePointerRelease(&singletonEvent, sleepEvent, nullptr);
		
		if (previousEvent)
		{
			// Back out if multiple threads try to demand create at the same time.
			CloseHandle(sleepEvent);
			sleepEvent = previousEvent;
		}
	}

	// Emulate sleep by waiting with timeout on an event that is never signalled.
	WaitForSingleObjectEx(sleepEvent, dwMilliseconds, false);
}


DWORD WINAPI TlsAlloc()
{
  if (NULL == tlsAllocationLock) 
    tlsAllocationLock = new mutex();
  
	lock_guard lock(tlsAllocationLock);
	
	// Can we reuse a previously freed TLS slot?
	if (!freeTlsIndices.empty())
	{
		DWORD result = freeTlsIndices.back();
		freeTlsIndices.pop_back();
		return result;
	}

	// Allocate a new TLS slot.
	return nextTlsIndex++;
}


_Use_decl_annotations_ BOOL WINAPI TlsFree(DWORD dwTlsIndex)
{
  assert(NULL != tlsAllocationLock);
	lock_guard lock(tlsAllocationLock);

	assert(dwTlsIndex < nextTlsIndex);
	assert(find(freeTlsIndices.begin(), freeTlsIndices.end(), dwTlsIndex) == freeTlsIndices.end());

	// Store this slot for reuse by TlsAlloc.
	try
	{
		freeTlsIndices.push_back(dwTlsIndex);
	}
	catch (...)
	{
		return false;
	}

	// Zero the value for all threads that might be using this now freed slot.
	set<ThreadLocalData*>::iterator it;
	for(it = allThreadData.begin(); it != allThreadData.end(); it++)
	//for each (auto threadData in allThreadData)
	{
		ThreadLocalData* threadData = (*it);
		if (threadData->size() > dwTlsIndex)
		{
			threadData->at(dwTlsIndex) = nullptr;
		}
	}

	return true;
}


_Use_decl_annotations_ LPVOID WINAPI TlsGetValue(DWORD dwTlsIndex)
{
	ThreadLocalData* threadData = currentThreadData;

	if (threadData && threadData->size() > dwTlsIndex)
	{
		// Return the value of an allocated TLS slot.
		return threadData->at(dwTlsIndex);
	}
	else
	{
		// Default value for unallocated slots.
		return nullptr;
	}
}


_Use_decl_annotations_ BOOL WINAPI TlsSetValue(DWORD dwTlsIndex, LPVOID lpTlsValue)
{
  assert(NULL != tlsAllocationLock);
	ThreadLocalData* threadData = currentThreadData;

	if (!threadData)
	{
		// First time allocation of TLS data for this thread.
		try
		{
			threadData = new ThreadLocalData(dwTlsIndex + 1, nullptr);
			
			lock_guard lock(tlsAllocationLock);

			allThreadData.insert(threadData);

			currentThreadData = threadData;
		}
		catch (...)
		{
			if (threadData)
				delete threadData;

			return false;
		}
	}
	else if (threadData->size() <= dwTlsIndex)
	{
		// This thread already has a TLS data block, but it must be expanded to fit the specified slot.
		try
		{
			lock_guard lock(tlsAllocationLock);

			threadData->resize(dwTlsIndex + 1, nullptr);
		}
		catch (...)
		{
			return false;
		}
	}

	// Store the new value for this slot.
	threadData->at(dwTlsIndex) = lpTlsValue;

	return true;
}


// Called at thread exit to clean up TLS allocations.
void WINAPI TlsShutdown()
{
  assert(NULL != tlsAllocationLock);
	ThreadLocalData* threadData = currentThreadData;

	if (threadData)
	{
		{
			lock_guard lock(tlsAllocationLock);

			allThreadData.erase(threadData);
		}

		currentThreadData = nullptr;

		delete threadData;
	}
}

// End Microsoft Code


HANDLE _beginthreadex(_In_opt_ LPSECURITY_ATTRIBUTES unusedThreadAttributes, _In_ SIZE_T unusedStackSize, _In_ HANDLE lpStartAddress, _In_opt_ LPVOID lpParameter, _In_ DWORD dwCreationFlags, _Out_opt_ unsigned int* unusedThreadId)
{
	HANDLE ret = CreateThread(unusedThreadAttributes, 0, LPTHREAD_START_ROUTINE(lpStartAddress), lpParameter, CREATE_SUSPENDED, nullptr);
  
  if ((dwCreationFlags & CREATE_SUSPENDED) == 0)
    ResumeThread(ret);
  
  return ret;
}


VOID WINAPI ExitThread(_In_  DWORD dwExitCode)
{
	// Don't have to do anything here
}

// Not Complete - Only attempts to meet requirements for resiprocate - ThreadIf.cxx - join()
BOOL WINAPI GetExitCodeThread(_In_ HANDLE hThread, _Out_ LPDWORD lpExitCode)
{
	lock_guard lock(pendingThreadsLock);

	// Look up the requested thread.
	auto threadInfo = pendingThreads.find(hThread);

	if (threadInfo != pendingThreads.end())
	{
		*lpExitCode = 259; // STILL_ACTIVE
	}
	else
	{
		*lpExitCode = 0;
	}

	return TRUE;
}

HANDLE WINAPI CreateEvent(LPSECURITY_ATTRIBUTES lpEventAttributes, BOOL bManualReset, BOOL bInitialState, LPCTSTR lpName)
{
  return CreateEventEx(lpEventAttributes, lpName, ((bManualReset ? CREATE_EVENT_MANUAL_RESET : 0) | (bInitialState ? CREATE_EVENT_INITIAL_SET : 0)), EVENT_ALL_ACCESS);
}

HANDLE WINAPI CreateMutex(LPSECURITY_ATTRIBUTES lpMutexAttributes, BOOL bInitialOwner, LPCTSTR lpName)
{
	return CreateMutexEx(lpMutexAttributes, lpName, (bInitialOwner) ? CREATE_MUTEX_INITIAL_OWNER : 0, STANDARD_RIGHTS_ALL);
}

HANDLE WINAPI CreateSemaphore(LPSECURITY_ATTRIBUTES lpSemaphoreAttributes, LONG lInitialCount, LONG lMaximumCount, LPCTSTR lpName)
{
	return CreateSemaphoreEx(lpSemaphoreAttributes, lInitialCount, lMaximumCount, lpName, 0, SEMAPHORE_ALL_ACCESS);
}

DWORD WINAPI WaitForSingleObject(HANDLE hHandle, DWORD dwMilliseconds)
{
	return WaitForSingleObjectEx(hHandle, dwMilliseconds, FALSE);
}

DWORD WINAPI WaitForMultipleObjects(DWORD nCount, const HANDLE *lpHandle, BOOL bWaitAll, DWORD dwMilliseconds)
{
	return WaitForMultipleObjectsEx(nCount, lpHandle, bWaitAll, dwMilliseconds, FALSE);
}

BOOL WINAPI InitializeCriticalSectionAndSpinCount(LPCRITICAL_SECTION lpCriticalSection, DWORD dwSpinCount)
{
	return InitializeCriticalSectionEx(lpCriticalSection, dwSpinCount, 0);
}

void WINAPI InitializeCriticalSection(LPCRITICAL_SECTION lpCriticalSection)
{
	InitializeCriticalSectionEx(lpCriticalSection, 0, 0);
}

DWORD timeGetTime(void)
{
	return (DWORD)GetTickCount64();
}

DWORD GetTickCount(void)
{
	return (DWORD)GetTickCount64();
}

DWORD timeSetEvent(UINT uDelay, HANDLE hTimeProc, BOOL bPeriodic, BOOL bSetEvent, BOOL bPulseEvent)
{
	TimeSpan period;
	period.Duration = uDelay * 10000;

	TimerElapsedHandler^ timerElapsedHandler = ref new TimerElapsedHandler
		(
			[hTimeProc, bSetEvent, bPulseEvent](ThreadPoolTimer^ source)
			{
				if(bSetEvent)
					SetEvent(hTimeProc);
				else if(bPulseEvent)
				{
					SetEvent(hTimeProc);
					ResetEvent(hTimeProc);
				}
			}
		);

	if(bPeriodic)
		ThreadPoolTimer ^ PeriodicTimer = ThreadPoolTimer::CreateTimer(timerElapsedHandler, period);
	else
		ThreadPoolTimer ^ PeriodicTimer = ThreadPoolTimer::CreatePeriodicTimer(timerElapsedHandler, period);
	// Should return an identifier
	return 0;
}

DWORD timeSetEvent(UINT uDelay, UINT uResolution, LPTIMECALLBACK lpTimeProc, DWORD_PTR dwUser, UINT fuEvent)
{
	bool bPeriodic = (fuEvent & TIME_PERIODIC) != 0;
  bool bSetEvent = (fuEvent & TIME_CALLBACK_EVENT_SET) != 0;
  bool bPulseEvent = (fuEvent & TIME_PERIODIC) != 0;
	return timeSetEvent(uDelay, lpTimeProc, bPeriodic, bSetEvent, bPulseEvent);
}

BOOL timeKillEvent(UINT uTimerID)
{
	return FALSE;
}

HANDLE WINAPI FindFirstFile(_In_ LPCTSTR lpFileName, _Out_ LPWIN32_FIND_DATA lpFindFileData)
{
	return FindFirstFileEx(lpFileName, FindExInfoStandard, lpFindFileData, FindExSearchNameMatch, NULL, 0);
}

HANDLE WINAPI FindFirstFileA(_In_ LPCTSTR lpFileName, _Out_ LPWIN32_FIND_DATA lpFindFileData)
{
	return FindFirstFile(lpFileName, lpFindFileData);
}

void WINAPI GetSystemInfo(_Out_ LPSYSTEM_INFO lpSystemInfo)
{
	GetNativeSystemInfo(lpSystemInfo);
}

int GetDateFormat(_In_ LCID Locale, _In_ DWORD dwFlags, _In_opt_ const SYSTEMTIME *lpDate, _In_opt_ LPCTSTR lpFormat, _Out_opt_ LPTSTR lpDateStr, _In_ int cchDate)
{
	// The documentation lists LCIDToLocaleName as supported on WP8, but the library is Kernel32. We can't add Kernel32 as a dependency so we will manually handle the cases which are currently required.
	/*LPWSTR localeName = new TCHAR[LOCALE_NAME_MAX_LENGTH];
	LCIDToLocaleName(Locale, localeName, LOCALE_NAME_MAX_LENGTH, 0);
	return GetDateFormatEx(localeName, dwFlags, lpDate, lpFormat, lpDateStr, cchDate, NULL);*/
	switch (Locale) {
	case LOCALE_INVARIANT:
		return GetDateFormatEx(LOCALE_NAME_INVARIANT, dwFlags, lpDate, lpFormat, lpDateStr, cchDate, NULL);
	case LOCALE_SYSTEM_DEFAULT:
		return GetDateFormatEx(LOCALE_NAME_SYSTEM_DEFAULT, dwFlags, lpDate, lpFormat, lpDateStr, cchDate, NULL);
	case LOCALE_USER_DEFAULT:
		return GetDateFormatEx(LOCALE_NAME_USER_DEFAULT, dwFlags, lpDate, lpFormat, lpDateStr, cchDate, NULL);
	}

	return 0;
}

int GetTimeFormat(_In_ LCID Locale, _In_ DWORD dwFlags, _In_opt_ const SYSTEMTIME *lpDate, _In_opt_ LPCTSTR lpFormat, _Out_opt_ LPTSTR lpTimeStr, _In_ int cchDate)
{
	// The documentation lists LCIDToLocaleName as supported on WP8, but the library is Kernel32. We can't add Kernel32 as a dependency so we will manually handle the cases which are currently required.
	/*LPWSTR localeName = new TCHAR[LOCALE_NAME_MAX_LENGTH];
	LCIDToLocaleName(Locale, localeName, LOCALE_NAME_MAX_LENGTH, 0);
	return GetTimeFormatEx(localeName, dwFlags, lpDate, lpFormat, lpTimeStr, cchDate);*/

	switch (Locale) {
	case LOCALE_INVARIANT:
		return GetTimeFormatEx(LOCALE_NAME_INVARIANT, dwFlags, lpDate, lpFormat, lpTimeStr, cchDate);
	case LOCALE_SYSTEM_DEFAULT:
		return GetTimeFormatEx(LOCALE_NAME_SYSTEM_DEFAULT, dwFlags, lpDate, lpFormat, lpTimeStr, cchDate);
	case LOCALE_USER_DEFAULT:
		return GetTimeFormatEx(LOCALE_NAME_USER_DEFAULT, dwFlags, lpDate, lpFormat, lpTimeStr, cchDate);
	}

	return 0;
}

/* Stub Functions */

void timeBeginPeriod(UINT uPeriod) {}
void timeEndPeriod(UINT uPeriod) {}
BOOL WINAPI GetSystemTimeAdjustment(_Out_  PDWORD lpTimeAdjustment, _Out_  PDWORD lpTimeIncrement, _Out_  PBOOL lpTimeAdjustmentDisabled) { return FALSE; }

DWORD_PTR WINAPI SetThreadAffinityMask(_In_ HANDLE hThread, _In_ DWORD_PTR dwThreadAffinityMask) { return 0; }

HMODULE WINAPI LoadLibrary(_In_  LPCTSTR lpFileName) { return NULL; }

char* _getch() { return NULL; }
char* getch() { return NULL; }
char* getcwd() { return NULL; }
char* getenv(const char* value = NULL) { return NULL; }