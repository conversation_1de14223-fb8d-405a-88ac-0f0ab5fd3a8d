// THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
// ANY <PERSON>IND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
// THE IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
// PARTICULAR PURPOSE.
//
// Copyright (c) Microsoft Corporation. All rights reserved.
//
//
// Emulates a subset of the Win32 threading API as a layer on top of WinRT threadpools.
//
// Supported features:
//
//    - CreateThread (returns a standard Win32 handle which can be waited on, then closed)
//    - CREATE_SUSPENDED and ResumeThread
//    - Partial support for SetThreadPriority (see below)
//    - Sleep
//    - Thread local storage (TlsAlloc, TlsFree, TlsGetValue, TlsSetValue)
//
// Differences from Win32:
//
//    - If using TLS other than from this CreateThread emulation, call TlsShutdown before thread/task exit
//    - No ExitThread or TerminateThread (just return from the thread function to exit)
//    - No SuspendThread, so ResumeThread is only useful in combination with CREATE_SUSPENDED
//    - SetThreadPriority is only available while a thread is in CREATE_SUSPENDED state
//    - SetThreadPriority only supports three priority levels (negative, zero, or positive)
//    - No thread identifier APIs (GetThreadId, GetCurrentThreadId, OpenThread)
//    - No affinity APIs
//    - No GetExitCodeThread
//    - Failure cases return error codes but do not always call SetLastError

#pragma once

#include <windows.h>

#ifndef CREATE_SUSPENDED
#define CREATE_SUSPENDED 0x00000004
#endif
	
#ifndef TLS_OUT_OF_INDEXES
#define TLS_OUT_OF_INDEXES 0xFFFFFFFF
#endif

#ifndef LPTIMECALLBACK
#define LPTIMECALLBACK HANDLE
#endif

#define TIME_ONESHOT 2
#define TIME_PERIODIC 4
#define TIME_CALLBACK_FUNCTION 8
#define TIME_CALLBACK_EVENT_SET 16
#define TIME_CALLBACK_EVENT_PULSE 32
#define TIME_KILL_SYNCHRONOUS 64

HANDLE WINAPI CreateThread(_In_opt_ LPSECURITY_ATTRIBUTES unusedThreadAttributes, _In_ SIZE_T unusedStackSize, _In_ LPTHREAD_START_ROUTINE lpStartAddress, _In_opt_ LPVOID lpParameter, _In_ DWORD dwCreationFlags, _Out_opt_ LPDWORD threadID);
// Priority < 0 = Low, 0 = Normal, Priority > 0 = High
HANDLE WINAPI CreateThreadWithPriority(_In_opt_ LPSECURITY_ATTRIBUTES unusedThreadAttributes, _In_ SIZE_T unusedStackSize, _In_ LPTHREAD_START_ROUTINE lpStartAddress, _In_opt_ LPVOID lpParameter, _In_ DWORD dwCreationFlags, _Out_opt_ LPDWORD threadID, _In_ int priority);
DWORD WINAPI ResumeThread(_In_ HANDLE hThread);
BOOL WINAPI SetThreadPriority(_In_ HANDLE hThread, _In_ int nPriority);

VOID WINAPI Sleep(_In_ DWORD dwMilliseconds);
VOID WINAPI SleepEx(_In_ DWORD dwMilliseconds, _In_ BOOL bAlertable);

DWORD WINAPI TlsAlloc();
BOOL WINAPI TlsFree(_In_ DWORD dwTlsIndex);
LPVOID WINAPI TlsGetValue(_In_ DWORD dwTlsIndex);
BOOL WINAPI TlsSetValue(_In_ DWORD dwTlsIndex, _In_opt_ LPVOID lpTlsValue);

void WINAPI TlsShutdown();

// End Microsoft Code

#ifdef __cplusplus
extern "C" 
{
#endif

HANDLE _beginthreadex(_In_opt_ LPSECURITY_ATTRIBUTES unusedThreadAttributes, _In_ SIZE_T unusedStackSize, _In_ HANDLE lpStartAddress, _In_opt_ LPVOID lpParameter, _In_ DWORD dwCreationFlags, _Out_opt_ unsigned int* unusedThreadId);

VOID WINAPI ExitThread(_In_  DWORD dwExitCode);
// Not Complete - Only attempts to meet requirements for resiprocate - ThreadIf.cxx - join()
BOOL WINAPI GetExitCodeThread(_In_ HANDLE hThread, _Out_ LPDWORD lpExitCode);

HANDLE WINAPI CreateEvent(_In_opt_ LPSECURITY_ATTRIBUTES lpEventAttributes, _In_ BOOL bManualReset, _In_  BOOL bInitialState, _In_opt_ LPCTSTR lpName);
HANDLE WINAPI CreateMutex(_In_opt_ LPSECURITY_ATTRIBUTES lpMutexAttributes, _In_ BOOL bInitialOwner, _In_opt_ LPCTSTR lpName);
HANDLE WINAPI CreateSemaphore(_In_opt_ LPSECURITY_ATTRIBUTES lpSemaphoreAttributes, _In_ LONG lInitialCount, _In_ LONG lMaximumCount, _In_opt_ LPCTSTR lpName);


DWORD WINAPI WaitForSingleObject(_In_ HANDLE hHandle, _In_ DWORD dwMilliseconds);
DWORD WINAPI WaitForMultipleObjects(_In_ DWORD nCount, _In_ const HANDLE *lpHandle, _In_ BOOL bWaitAll, _In_ DWORD dwMilliseconds);
BOOL WINAPI InitializeCriticalSectionAndSpinCount(_Out_ LPCRITICAL_SECTION lpCriticalSection, _In_ DWORD dwSpinCount);
void WINAPI InitializeCriticalSection(_Out_ LPCRITICAL_SECTION lpCriticalSection);

DWORD timeGetTime(void);
DWORD GetTickCount(void);

DWORD timeSetEvent(UINT uDelay, UINT uResolution, LPTIMECALLBACK lpTimeProc, DWORD_PTR dwUser, UINT fuEvent);
BOOL timeKillEvent(UINT uTimerID);

HANDLE WINAPI FindFirstFile(_In_ LPCTSTR lpFileName, _Out_ LPWIN32_FIND_DATAA lpFindFileData);
HANDLE WINAPI FindFirstFileA(_In_ LPCSTR lpFileName, _Out_ LPWIN32_FIND_DATAA lpFindFileData);

void WINAPI GetSystemInfo(_Out_ LPSYSTEM_INFO lpSystemInfo);

int GetDateFormat(_In_ LCID Locale, _In_ DWORD dwFlags, _In_opt_ const SYSTEMTIME *lpDate, _In_opt_ LPCTSTR lpFormat, _Out_opt_ LPTSTR lpDateStr, _In_ int cchDate);
int GetTimeFormat(_In_ LCID Locale, _In_ DWORD dwFlags, _In_opt_ const SYSTEMTIME *lpDate, _In_opt_ LPCTSTR lpFormat, _Out_opt_ LPTSTR lpTimeStr, _In_ int cchDate);

/* Stub functions */
void timeBeginPeriod(UINT uPeriod);
void timeEndPeriod(UINT uPeriod);
BOOL WINAPI GetSystemTimeAdjustment(_Out_  PDWORD lpTimeAdjustment, _Out_  PDWORD lpTimeIncrement, _Out_  PBOOL lpTimeAdjustmentDisabled);

DWORD_PTR WINAPI SetThreadAffinityMask(_In_  HANDLE hThread, _In_ DWORD_PTR dwThreadAffinityMask);

HMODULE WINAPI LoadLibrary(_In_  LPCTSTR lpFileName);

char* _getch();
char* getch();
char* getcwd();
char* getenv(const char* value);
#ifdef __cplusplus
}
#endif