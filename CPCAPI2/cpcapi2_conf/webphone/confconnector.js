function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function ConferenceConnectorManager() {
	this.callback = null;
	this.createConferenceConnectorCallback = null;
	this.createConferenceSessionCallback = null;
	this.sdkConnector = null;
}

ConferenceConnectorManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

ConferenceConnectorManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

ConferenceConnectorManager.prototype.createConferenceConnector = function(_callback) {
	this.createConferenceConnectorCallback = _callback;
	var createFunc = {};
	createFunc.moduleId = "ConferenceConnectorJsonApi";
	createFunc.functionObject = {};
	createFunc.functionObject.functionName = "createConferenceConnector";
	this.sdkConnector.send(JSON.stringify(createFunc));
	return this;
}

ConferenceConnectorManager.prototype.makeConferenceConnectorSettings = function(_authServerApiKey, _authServerUrl, _joinUrl, _orchestrationServerUrl, _password, _regionCode, _username) {
	var settings = {};
	settings.authServerApiKey = _authServerApiKey;
	settings.authServerUrl = _authServerUrl;
	settings.joinUrl = _joinUrl;
	settings.orchestrationServerUrl = _orchestrationServerUrl;
	settings.password = _password;
	settings.regionCode = _regionCode;
	settings.username = _username;
	return settings;
}

ConferenceConnectorManager.prototype.setConnectionSettings = function(_conn, _settings) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setConnectionSettings";
	fn.functionObject.conn = _conn;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.connectToConferenceService = function(_conn) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "connectToConferenceService";
	fn.functionObject.conn = _conn;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.disconnectFromConferenceService = function(_conn) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "disconnectFromConferenceService";
	fn.functionObject.conn = _conn;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.queryConferenceList = function(_conn) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryConferenceList";
	fn.functionObject.conn = _conn;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.makeCloudConferenceSettings = function(_conferenceDescription, _conferenceId, _conferenceType) {
	var settings = {};
	settings.conferenceDescription = _conferenceDescription;
	settings.conferenceId = _conferenceId;
	settings.conferenceType = _conferenceType;
	return settings;
}

ConferenceConnectorManager.prototype.createNewConference = function(_conn, _settings) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "createNewConference";
	fn.functionObject.conn = _conn;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.queryParticipantList = function(_conference) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryParticipantList";
	fn.functionObject.conference = _conference;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.createConferenceSession = function(_conference, _callback) {
	this.createConferenceSessionCallback = _callback;
	
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "createConferenceSession";
	fn.functionObject.conference = _conference;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.makeCloudConferenceSessionSettings = function(_role, _displayName) {
	var settings = {};
	settings.role = _role;
	settings.displayName = _displayName;
	return settings;
}

ConferenceConnectorManager.prototype.setSessionSettings = function(_session, _settings) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setSessionSettings";
	fn.functionObject.session = _session;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.makeCloudConferenceSessionMediaSettings = function(_audioDirection, _videoDirection) {
	var settings = {};
	settings.audioDirection = _audioDirection;
	settings.videoDirection = _videoDirection;
	return settings;
}

ConferenceConnectorManager.prototype.setSessionMediaSettings = function(_session, _settings) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setSessionMediaSettings";
	fn.functionObject.session = _session;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.startSession = function(_session) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "startSession";
	fn.functionObject.session = _session;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.endSession = function(_session) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "endSession";
	fn.functionObject.session = _session;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.updateSessionMedia = function(_session) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "updateSessionMedia";
	fn.functionObject.session = _session;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.makeParticipantPermissions = function(_canCreateConference) {
	var settings = {};
	settings.canCreateConference = _canCreateConference;
	return settings;
}

ConferenceConnectorManager.prototype.setParticipantPermissions = function(_session, _participant, _permissions) {
	var fn = {};
	fn.moduleId = "ConferenceConnectorJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setParticipantPermissions";
	fn.functionObject.session = _session;
	fn.functionObject.participant = _participant;
	fn.functionObject.permissions = _permissions;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceConnectorManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "ConferenceConnectorManagerJsonProxy") {
		if (_jsonEventObj.functionObject.functionName == "onCreateConferenceConnector") {
			if (this.createConferenceConnectorCallback) {
				var cb = this.createConferenceConnectorCallback;
				this.createConferenceConnectorCallback = null;
				cb(_jsonEventObj.functionObject.conn);
			}
		}
		else if (_jsonEventObj.functionObject.functionName == "onCreateConferenceSession") {
			if (this.createConferenceSessionCallback) {
				var cb = this.createConferenceSessionCallback;
				this.createConferenceSessionCallback = null;
				cb(_jsonEventObj.functionObject.session);
			}
		}
		else {
			this.callback(_jsonEventObj.functionObject);
		}
	}
	return this;
}
