!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("WSAvcPlayer",[],t):"object"==typeof exports?exports.WSAvcPlayer=t():e.WSAvcPlayer=t()}(this,function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{configurable:!1,enumerable:!0,get:r})},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=10)}([function(e,t,n){"use strict";var r=n(1),i=n(2);function s(){}s.prototype={norm:function(){for(var e=this.elements.length,t=0;e--;)t+=Math.pow(this.elements[e],2);return Math.sqrt(t)},e:function(e){return e<1||e>this.elements.length?null:this.elements[e-1]},dimensions:function(){return{rows:1,cols:this.elements.length}},rows:function(){return 1},cols:function(){return this.elements.length},modulus:function(){return Math.sqrt(this.dot(this))},eql:function(e){var t=this.elements.length,n=e.elements||e;if(t!=n.length)return!1;for(;t--;)if(Math.abs(this.elements[t]-n[t])>r.precision)return!1;return!0},dup:function(){return s.create(this.elements)},map:function(e){var t=[];return this.each(function(n,r){t.push(e(n,r))}),s.create(t)},each:function(e){for(var t=this.elements.length,n=0;n<t;n++)e(this.elements[n],n+1)},toUnitVector:function(){var e=this.modulus();return 0===e?this.dup():this.map(function(t){return t/e})},angleFrom:function(e){var t=e.elements||e,n=this.elements.length;if(n!=t.length)return null;var r=0,i=0,s=0;if(this.each(function(e,n){r+=e*t[n-1],i+=e*e,s+=t[n-1]*t[n-1]}),i=Math.sqrt(i),s=Math.sqrt(s),i*s==0)return null;var o=r/(i*s);return o<-1&&(o=-1),o>1&&(o=1),Math.acos(o)},isParallelTo:function(e){var t=this.angleFrom(e);return null===t?null:t<=r.precision},isAntiparallelTo:function(e){var t=this.angleFrom(e);return null===t?null:Math.abs(t-Math.PI)<=r.precision},isPerpendicularTo:function(e){var t=this.dot(e);return null===t?null:Math.abs(t)<=r.precision},add:function(e){var t=e.elements||e;return this.elements.length!=t.length?this.map(function(t){return t+e}):this.map(function(e,n){return e+t[n-1]})},subtract:function(e){if("number"==typeof e)return this.map(function(t){return t-e});var t=e.elements||e;return this.elements.length!=t.length?null:this.map(function(e,n){return e-t[n-1]})},multiply:function(e){return this.map(function(t){return t*e})},elementMultiply:function(e){return this.map(function(t,n){return e.e(n)*t})},sum:function(){var e=0;return this.map(function(t){e+=t}),e},chomp:function(e){for(var t=[],n=e;n<this.elements.length;n++)t.push(this.elements[n]);return s.create(t)},top:function(e){for(var t=[],n=0;n<e;n++)t.push(this.elements[n]);return s.create(t)},augment:function(e){for(var t=this.elements,n=0;n<e.length;n++)t.push(e[n]);return s.create(t)},x:function(e){return this.multiply(e)},log:function(){return s.log(this)},elementDivide:function(e){return this.map(function(t,n){return t/e.e(n)})},product:function(){var e=1;return this.map(function(t){e*=t}),e},dot:function(e){var t=e.elements||e,n=0,r=this.elements.length;if(r!=t.length)return null;for(;r--;)n+=this.elements[r]*t[r];return n},cross:function(e){var t=e.elements||e;if(3!=this.elements.length||3!=t.length)return null;var n=this.elements;return s.create([n[1]*t[2]-n[2]*t[1],n[2]*t[0]-n[0]*t[2],n[0]*t[1]-n[1]*t[0]])},max:function(){for(var e=0,t=this.elements.length;t--;)Math.abs(this.elements[t])>Math.abs(e)&&(e=this.elements[t]);return e},maxIndex:function(){for(var e=0,t=this.elements.length,n=-1;t--;)Math.abs(this.elements[t])>Math.abs(e)&&(e=this.elements[t],n=t+1);return n},indexOf:function(e){for(var t=null,n=this.elements.length,r=0;r<n;r++)null===t&&this.elements[r]==e&&(t=r+1);return t},toDiagonalMatrix:function(){return i.Diagonal(this.elements)},round:function(){return this.map(function(e){return Math.round(e)})},transpose:function(){for(var e=this.elements.length,t=[],n=0;n<e;n++)t.push([this.elements[n]]);return i.create(t)},snapTo:function(e){return this.map(function(t){return Math.abs(t-e)<=r.precision?e:t})},distanceFrom:function(e){if(e.anchor||e.start&&e.end)return e.distanceFrom(this);var t=e.elements||e;if(t.length!=this.elements.length)return null;var n,r=0;return this.each(function(e,i){n=e-t[i-1],r+=n*n}),Math.sqrt(r)},liesOn:function(e){return e.contains(this)},liesIn:function(e){return e.contains(this)},rotate:function(e,t){var n,r,o,a,u=null;switch(e.determinant&&(u=e.elements),this.elements.length){case 2:return 2!=(n=t.elements||t).length?null:(u||(u=i.Rotation(e).elements),r=this.elements[0]-n[0],o=this.elements[1]-n[1],s.create([n[0]+u[0][0]*r+u[0][1]*o,n[1]+u[1][0]*r+u[1][1]*o]));case 3:if(!t.direction)return null;var l=t.pointClosestTo(this).elements;return u||(u=i.Rotation(e,t.direction).elements),r=this.elements[0]-l[0],o=this.elements[1]-l[1],a=this.elements[2]-l[2],s.create([l[0]+u[0][0]*r+u[0][1]*o+u[0][2]*a,l[1]+u[1][0]*r+u[1][1]*o+u[1][2]*a,l[2]+u[2][0]*r+u[2][1]*o+u[2][2]*a]);default:return null}},reflectionIn:function(e){if(e.anchor){var t=this.elements.slice(),n=e.pointClosestTo(t).elements;return s.create([n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1]),n[2]+(n[2]-(t[2]||0))])}var r=e.elements||e;return this.elements.length!=r.length?null:this.map(function(e,t){return r[t-1]+(r[t-1]-e)})},to3D:function(){var e=this.dup();switch(e.elements.length){case 3:break;case 2:e.elements.push(0);break;default:return null}return e},inspect:function(){return"["+this.elements.join(", ")+"]"},setElements:function(e){return this.elements=(e.elements||e).slice(),this}},s.create=function(e){return(new s).setElements(e)},s.i=s.create([1,0,0]),s.j=s.create([0,1,0]),s.k=s.create([0,0,1]),s.Random=function(e){for(var t=[];e--;)t.push(Math.random());return s.create(t)},s.Fill=function(e,t){for(var n=[];e--;)n.push(t);return s.create(n)},s.Zero=function(e){return s.Fill(e,0)},s.One=function(e){return s.Fill(e,1)},s.log=function(e){return e.map(function(e){return Math.log(e)})},e.exports=s},function(e,t,n){"use strict";Math.sign=function(e){return e<0?-1:1};e.exports={precision:1e-6,approxPrecision:1e-5}},function(e,t,n){"use strict";var r=n(1);n(0);function s(e,t,n,r){for(var i=e.elements,s=r-1;s--;){for(var o=[],a=0;a<n;a++)o.push(a==s?1:0);i.unshift(o)}for(s=r-1;s<t;s++)for(;i[s].length<n;)i[s].unshift(0);return $M(i)}function o(){for(var e=this,t=u.I(e.rows()),n=e.transpose(),r=u.I(e.cols()),i=Number.MAX_VALUE,s=0;i>2.2737e-13&&s<100;){var o=n.transpose().qrJs();n=o.R,t=t.x(o.Q),o=n.transpose().qrJs(),r=r.x(o.Q);var a=(n=o.R).triu(1).unroll().norm(),l=n.diagonal().norm();0==l&&(l=1),i=a/l,s++}var c=n.diagonal(),h=[];for(s=1;s<=c.cols();s++){var f=c.e(s);if(h.push(Math.abs(f)),f<0)for(var m=0;m<r.rows();m++)t.elements[m][s-1]=-t.elements[m][s-1]}return{U:r,S:$V(h).toDiagonalMatrix(),V:t}}function a(){for(var e=this.rows(),t=this.cols(),n=u.I(e),r=this,i=1;i<Math.min(e,t);i++){for(var o=r.slice(i,0,i,i).col(1),a=[1];a.length<=e-i;)a.push(0);a=$V(a);var l=o.add(a.x(o.norm()*Math.sign(o.e(1)))),c=$M(l),h=s(u.I(e-i+1).subtract(c.x(2).x(c.transpose()).div(c.transpose().x(c).e(1,1))),e,t,i);r=h.x(r),n=n.x(h)}return{Q:n,R:r}}function u(){}u.prototype={solve:function(e){var t=this.lu();e=t.P.x(e);var n=t.L.forwardSubstitute(e),r=t.U.backSubstitute(n);return t.P.x(r)},pcaProject:function(e,t){var n=(t=t||function(e){var t=e.transpose().x(e).x(1/e.rows()).svd();return{U:t.U,S:t.S}}(this).U).slice(1,t.rows(),1,e);return{Z:this.x(n),U:t}},pcaRecover:function(e){var t=this.cols(),n=e.slice(1,e.rows(),1,t);return this.x(n.transpose())},triu:function(e){return e||(e=0),this.map(function(t,n,r){return r-n>=e?t:0})},unroll:function(){for(var e=[],t=1;t<=this.cols();t++)for(var n=1;n<=this.rows();n++)e.push(this.e(n,t));return $V(e)},slice:function(e,t,n,r){var s=[];for(0==t&&(t=this.rows()),0==r&&(r=this.cols()),i=e;i<=t;i++){var o=[];for(j=n;j<=r;j++)o.push(this.e(i,j));s.push(o)}return $M(s)},e:function(e,t){return e<1||e>this.elements.length||t<1||t>this.elements[0].length?null:this.elements[e-1][t-1]},row:function(e){return e>this.elements.length?null:$V(this.elements[e-1])},col:function(e){if(e>this.elements[0].length)return null;for(var t=[],n=this.elements.length,r=0;r<n;r++)t.push(this.elements[r][e-1]);return $V(t)},dimensions:function(){return{rows:this.elements.length,cols:this.elements[0].length}},rows:function(){return this.elements.length},cols:function(){return this.elements[0].length},approxEql:function(e){return this.eql(e,r.approxPrecision)},eql:function(e,t){var n=e.elements||e;if(void 0===n[0][0]&&(n=u.create(n).elements),this.elements.length!=n.length||this.elements[0].length!=n[0].length)return!1;for(var i,s=this.elements.length,o=this.elements[0].length;s--;)for(i=o;i--;)if(Math.abs(this.elements[s][i]-n[s][i])>(t||r.precision))return!1;return!0},dup:function(){return u.create(this.elements)},map:function(e){for(var t,n=[],r=this.elements.length,i=this.elements[0].length;r--;)for(t=i,n[r]=[];t--;)n[r][t]=e(this.elements[r][t],r+1,t+1);return u.create(n)},isSameSizeAs:function(e){var t=e.elements||e;return void 0===t[0][0]&&(t=u.create(t).elements),this.elements.length==t.length&&this.elements[0].length==t[0].length},add:function(e){if("number"==typeof e)return this.map(function(t,n,r){return t+e});var t=e.elements||e;return void 0===t[0][0]&&(t=u.create(t).elements),this.isSameSizeAs(t)?this.map(function(e,n,r){return e+t[n-1][r-1]}):null},subtract:function(e){if("number"==typeof e)return this.map(function(t,n,r){return t-e});var t=e.elements||e;return void 0===t[0][0]&&(t=u.create(t).elements),this.isSameSizeAs(t)?this.map(function(e,n,r){return e-t[n-1][r-1]}):null},canMultiplyFromLeft:function(e){var t=e.elements||e;return void 0===t[0][0]&&(t=u.create(t).elements),this.elements[0].length==t.length},mulOp:function(e,t){if(!e.elements)return this.map(function(n){return t(n,e)});var n=!!e.modulus;if(void 0===(p=e.elements||e)[0][0]&&(p=u.create(p).elements),!this.canMultiplyFromLeft(p))return null;for(var r,i,s,o,a,l=this.elements,c=[],h=l.length,f=p[0].length,m=l[0].length,d=h;d--;){for(i=[],r=l[d],o=f;o--;){for(s=0,a=m;a--;)s+=t(r[a],p[a][o]);i[o]=s}c[d]=i}var p=u.create(c);return n?p.col(1):p},div:function(e){return this.mulOp(e,function(e,t){return e/t})},multiply:function(e){return this.mulOp(e,function(e,t){return e*t})},x:function(e){return this.multiply(e)},elementMultiply:function(e){return this.map(function(t,n,r){return e.e(n,r)*t})},sum:function(){var e=0;return this.map(function(t){e+=t}),e},mean:function(){for(var e=this.dimensions(),t=[],n=1;n<=e.cols;n++)t.push(this.col(n).sum()/e.rows);return $V(t)},column:function(e){return this.col(e)},log:function(){return this.map(function(e){return Math.log(e)})},minor:function(e,t,n,r){for(var i,s,o,a=[],l=n,c=this.elements.length,h=this.elements[0].length;l--;)for(a[i=n-l-1]=[],s=r;s--;)o=r-s-1,a[i][o]=this.elements[(e+i-1)%c][(t+o-1)%h];return u.create(a)},transpose:function(){for(var e,t=this.elements.length,n=[],r=this.elements[0].length;r--;)for(e=t,n[r]=[];e--;)n[r][e]=this.elements[e][r];return u.create(n)},isSquare:function(){return this.elements.length==this.elements[0].length},max:function(){for(var e,t=0,n=this.elements.length,r=this.elements[0].length;n--;)for(e=r;e--;)Math.abs(this.elements[n][e])>Math.abs(t)&&(t=this.elements[n][e]);return t},indexOf:function(e){var t,n,r=this.elements.length,i=this.elements[0].length;for(t=0;t<r;t++)for(n=0;n<i;n++)if(this.elements[t][n]==e)return{i:t+1,j:n+1};return null},diagonal:function(){if(!this.isSquare)return null;for(var e=[],t=this.elements.length,n=0;n<t;n++)e.push(this.elements[n][n]);return $V(e)},toRightTriangular:function(){var e,t,n,r,i=this.dup(),s=this.elements.length,o=this.elements[0].length;for(t=0;t<s;t++){if(0==i.elements[t][t])for(n=t+1;n<s;n++)if(0!=i.elements[n][t]){for(e=[],r=0;r<o;r++)e.push(i.elements[t][r]+i.elements[n][r]);i.elements[t]=e;break}if(0!=i.elements[t][t])for(n=t+1;n<s;n++){var a=i.elements[n][t]/i.elements[t][t];for(e=[],r=0;r<o;r++)e.push(r<=t?0:i.elements[n][r]-i.elements[t][r]*a);i.elements[n]=e}}return i},toUpperTriangular:function(){return this.toRightTriangular()},determinant:function(){if(!this.isSquare())return null;if(1==this.cols&&1==this.rows)return this.row(1);if(0==this.cols&&0==this.rows)return 1;for(var e=this.toRightTriangular(),t=e.elements[0][0],n=e.elements.length,r=1;r<n;r++)t*=e.elements[r][r];return t},det:function(){return this.determinant()},isSingular:function(){return this.isSquare()&&0===this.determinant()},trace:function(){if(!this.isSquare())return null;for(var e=this.elements[0][0],t=this.elements.length,n=1;n<t;n++)e+=this.elements[n][n];return e},tr:function(){return this.trace()},rank:function(){for(var e,t=this.toRightTriangular(),n=0,i=this.elements.length,s=this.elements[0].length;i--;)for(e=s;e--;)if(Math.abs(t.elements[i][e])>r.precision){n++;break}return n},rk:function(){return this.rank()},augment:function(e){var t=e.elements||e;void 0===t[0][0]&&(t=u.create(t).elements);var n,r=this.dup(),i=r.elements[0].length,s=r.elements.length,o=t[0].length;if(s!=t.length)return null;for(;s--;)for(n=o;n--;)r.elements[s][i+n]=t[s][n];return r},inverse:function(){if(!this.isSquare()||this.isSingular())return null;for(var e,t,n,r,i,s=this.elements.length,o=s,a=this.augment(u.I(s)).toRightTriangular(),l=a.elements[0].length,c=[];o--;){for(n=[],c[o]=[],r=a.elements[o][o],t=0;t<l;t++)i=a.elements[o][t]/r,n.push(i),t>=s&&c[o].push(i);for(a.elements[o]=n,e=o;e--;){for(n=[],t=0;t<l;t++)n.push(a.elements[e][t]-a.elements[o][t]*a.elements[e][o]);a.elements[e]=n}}return u.create(c)},inv:function(){return this.inverse()},round:function(){return this.map(function(e){return Math.round(e)})},snapTo:function(e){return this.map(function(t){return Math.abs(t-e)<=r.precision?e:t})},inspect:function(){for(var e=[],t=this.elements.length,n=0;n<t;n++)e.push($V(this.elements[n]).inspect());return e.join("\n")},toArray:function(){for(var e=[],t=this.elements.length,n=0;n<t;n++)e.push(this.elements[n]);return e},setElements:function(e){var t,n,r=e.elements||e;if(void 0!==r[0][0]){for(t=r.length,this.elements=[];t--;)for(n=r[t].length,this.elements[t]=[];n--;)this.elements[t][n]=r[t][n];return this}var i=r.length;for(this.elements=[],t=0;t<i;t++)this.elements.push([r[t]]);return this},maxColumnIndexes:function(){for(var e=[],t=1;t<=this.rows();t++){for(var n=null,r=-1,i=1;i<=this.cols();i++)(null===n||this.e(t,i)>n)&&(n=this.e(t,i),r=i);e.push(r)}return $V(e)},maxColumns:function(){for(var e=[],t=1;t<=this.rows();t++){for(var n=null,r=1;r<=this.cols();r++)(null===n||this.e(t,r)>n)&&(n=this.e(t,r));e.push(n)}return $V(e)},minColumnIndexes:function(){for(var e=[],t=1;t<=this.rows();t++){for(var n=null,r=-1,i=1;i<=this.cols();i++)(null===n||this.e(t,i)<n)&&(n=this.e(t,i),r=i);e.push(r)}return $V(e)},minColumns:function(){for(var e=[],t=1;t<=this.rows();t++){for(var n=null,r=1;r<=this.cols();r++)(null===n||this.e(t,r)<n)&&(n=this.e(t,r));e.push(n)}return $V(e)},partialPivot:function(e,t,n,r,i){for(var s=0,o=0,a=e;a<=r.rows();a++)Math.abs(r.e(a,t))>o&&(o=Math.abs(r.e(e,t)),s=a);if(s!=e){var u=r.elements[e-1];r.elements[e-1]=r.elements[s-1],r.elements[s-1]=u,n.elements[e-1][e-1]=0,n.elements[e-1][s-1]=1,n.elements[s-1][s-1]=0,n.elements[s-1][e-1]=1}return n},forwardSubstitute:function(e){for(var t=[],n=1;n<=this.rows();n++){for(var r=0,i=1;i<n;i++)r+=this.e(n,i)*t[i-1];t.push((e.e(n)-r)/this.e(n,n))}return $V(t)},backSubstitute:function(e){for(var t=[],n=this.rows();n>0;n--){for(var r=0,i=this.cols();i>n;i--)r+=this.e(n,i)*t[this.rows()-i];t.push((e.e(n)-r)/this.e(n,n))}return $V(t.reverse())},luJs:l,svdJs:o,qrJs:a};function l(){for(var e=this.dup(),t=u.I(e.rows()),n=u.I(e.rows()),r=u.Zeros(e.rows(),e.cols()),i=1,s=1;s<=Math.min(e.cols(),e.rows());s++){n=e.partialPivot(s,i,n,e,t);for(var o=s+1;o<=e.rows();o++){var a=e.e(o,i)/e.e(s,i);t.elements[o-1][s-1]=a;for(var l=s+1;l<=e.cols();l++)e.elements[o-1][l-1]-=e.e(s,l)*a}for(l=s;l<=e.cols();l++)r.elements[s-1][l-1]=e.e(s,l);i<e.cols()&&i++}return{L:t,U:r,P:n}}u.prototype.svd=o,u.prototype.qr=a,u.prototype.lu=l,u.create=function(e){return(new u).setElements(e)},u.I=function(e){for(var t,n=[],r=e;r--;)for(t=e,n[r]=[];t--;)n[r][t]=r==t?1:0;return u.create(n)},u.loadFile=function(e){for(var t=[],r=n(18).readFileSync(e,"utf-8").split("\n"),i=0;i<r.length;i++){var s=r[i].split(",");s.length>1&&t.push(s)}return(new u).setElements(t)},u.Diagonal=function(e){for(var t=e.length,n=u.I(t);t--;)n.elements[t][t]=e[t];return n},u.Rotation=function(e,t){if(!t)return u.create([[Math.cos(e),-Math.sin(e)],[Math.sin(e),Math.cos(e)]]);var n=t.dup();if(3!=n.elements.length)return null;var r=n.modulus(),i=n.elements[0]/r,s=n.elements[1]/r,o=n.elements[2]/r,a=Math.sin(e),l=Math.cos(e),c=1-l;return u.create([[c*i*i+l,c*i*s-a*o,c*i*o+a*s],[c*i*s+a*o,c*s*s+l,c*s*o-a*i],[c*i*o-a*s,c*s*o+a*i,c*o*o+l]])},u.RotationX=function(e){var t=Math.cos(e),n=Math.sin(e);return u.create([[1,0,0],[0,t,-n],[0,n,t]])},u.RotationY=function(e){var t=Math.cos(e),n=Math.sin(e);return u.create([[t,0,n],[0,1,0],[-n,0,t]])},u.RotationZ=function(e){var t=Math.cos(e),n=Math.sin(e);return u.create([[t,-n,0],[n,t,0],[0,0,1]])},u.Random=function(e,t){return 1===arguments.length&&(t=e),u.Zero(e,t).map(function(){return Math.random()})},u.Fill=function(e,t,n){2===arguments.length&&(n=t,t=e);for(var r,i=[],s=e;s--;)for(r=t,i[s]=[];r--;)i[s][r]=n;return u.create(i)},u.Zero=function(e,t){return u.Fill(e,t,0)},u.Zeros=function(e,t){return u.Zero(e,t)},u.One=function(e,t){return u.Fill(e,t,1)},u.Ones=function(e,t){return u.One(e,t)},e.exports=u},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){e||(0,r.default)(t)};var r=function(e){return e&&e.__esModule?e:{default:e}}(n(4))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){console.error(e),console.trace()}},function(e,t,n){"use strict";var r=n(0),i=n(2),s=n(9),o=n(1);function a(){}a.prototype={eql:function(e){return this.isParallelTo(e)&&this.contains(e.anchor)},dup:function(){return a.create(this.anchor,this.direction)},translate:function(e){var t=e.elements||e;return a.create([this.anchor.elements[0]+t[0],this.anchor.elements[1]+t[1],this.anchor.elements[2]+(t[2]||0)],this.direction)},isParallelTo:function(e){if(e.normal||e.start&&e.end)return e.isParallelTo(this);var t=this.direction.angleFrom(e.direction);return Math.abs(t)<=o.precision||Math.abs(t-Math.PI)<=o.precision},distanceFrom:function(e){if(e.normal||e.start&&e.end)return e.distanceFrom(this);if(e.direction){if(this.isParallelTo(e))return this.distanceFrom(e.anchor);var t=this.direction.cross(e.direction).toUnitVector().elements,n=this.anchor.elements,r=e.anchor.elements;return Math.abs((n[0]-r[0])*t[0]+(n[1]-r[1])*t[1]+(n[2]-r[2])*t[2])}var i=e.elements||e,s=(n=this.anchor.elements,this.direction.elements),o=i[0]-n[0],a=i[1]-n[1],u=(i[2]||0)-n[2],l=Math.sqrt(o*o+a*a+u*u);if(0===l)return 0;var c=(o*s[0]+a*s[1]+u*s[2])/l,h=1-c*c;return Math.abs(l*Math.sqrt(h<0?0:h))},contains:function(e){if(e.start&&e.end)return this.contains(e.start)&&this.contains(e.end);var t=this.distanceFrom(e);return null!==t&&t<=o.precision},positionOf:function(e){if(!this.contains(e))return null;var t=e.elements||e,n=this.anchor.elements,r=this.direction.elements;return(t[0]-n[0])*r[0]+(t[1]-n[1])*r[1]+((t[2]||0)-n[2])*r[2]},liesIn:function(e){return e.contains(this)},intersects:function(e){return e.normal?e.intersects(this):!this.isParallelTo(e)&&this.distanceFrom(e)<=o.precision},intersectionWith:function(e){if(e.normal||e.start&&e.end)return e.intersectionWith(this);if(!this.intersects(e))return null;var t=this.anchor.elements,n=this.direction.elements,i=e.anchor.elements,s=e.direction.elements,o=n[0],a=n[1],u=n[2],l=s[0],c=s[1],h=s[2],f=t[0]-i[0],m=t[1]-i[1],d=t[2]-i[2],p=l*l+c*c+h*h,v=o*l+a*c+u*h,g=((-o*f-a*m-u*d)*p/(o*o+a*a+u*u)+v*(l*f+c*m+h*d))/(p-v*v);return r.create([t[0]+g*o,t[1]+g*a,t[2]+g*u])},pointClosestTo:function(e){if(e.start&&e.end)return null===(f=e.pointClosestTo(this))?null:this.pointClosestTo(f);if(e.direction){if(this.intersects(e))return this.intersectionWith(e);if(this.isParallelTo(e))return null;var t=this.direction.elements,n=e.direction.elements,i=t[0],o=t[1],a=t[2],u=n[0],l=n[1],c=n[2],h=[(g=a*u-i*c)*c-(y=i*l-o*u)*l,y*u-(b=o*c-a*l)*c,b*l-g*u];return(f=s.create(e.anchor,h)).intersectionWith(this)}var f=e.elements||e;if(this.contains(f))return r.create(f);var m=this.anchor.elements,d=(i=(t=this.direction.elements)[0],o=t[1],a=t[2],m[0]),p=m[1],v=m[2],g=i*(f[1]-p)-o*(f[0]-d),y=o*((f[2]||0)-v)-a*(f[1]-p),b=a*(f[0]-d)-i*((f[2]||0)-v),w=r.create([o*g-a*b,a*y-i*g,i*b-o*y]),x=this.distanceFrom(f)/w.modulus();return r.create([f[0]+w.elements[0]*x,f[1]+w.elements[1]*x,(f[2]||0)+w.elements[2]*x])},rotate:function(e,t){void 0===t.direction&&(t=a.create(t.to3D(),r.k));var n=i.Rotation(e,t.direction).elements,s=t.pointClosestTo(this.anchor).elements,o=this.anchor.elements,u=this.direction.elements,l=s[0],c=s[1],h=s[2],f=o[0]-l,m=o[1]-c,d=o[2]-h;return a.create([l+n[0][0]*f+n[0][1]*m+n[0][2]*d,c+n[1][0]*f+n[1][1]*m+n[1][2]*d,h+n[2][0]*f+n[2][1]*m+n[2][2]*d],[n[0][0]*u[0]+n[0][1]*u[1]+n[0][2]*u[2],n[1][0]*u[0]+n[1][1]*u[1]+n[1][2]*u[2],n[2][0]*u[0]+n[2][1]*u[1]+n[2][2]*u[2]])},reverse:function(){return a.create(this.anchor,this.direction.x(-1))},reflectionIn:function(e){if(e.normal){var t=this.anchor.elements,n=this.direction.elements,r=t[0],i=t[1],s=t[2],o=n[0],u=n[1],l=n[2],c=this.anchor.reflectionIn(e).elements,h=r+o,f=i+u,m=s+l,d=e.pointClosestTo([h,f,m]).elements,p=[d[0]+(d[0]-h)-c[0],d[1]+(d[1]-f)-c[1],d[2]+(d[2]-m)-c[2]];return a.create(c,p)}if(e.direction)return this.rotate(Math.PI,e);var v=e.elements||e;return a.create(this.anchor.reflectionIn([v[0],v[1],v[2]||0]),this.direction)},setVectors:function(e,t){if(e=r.create(e),t=r.create(t),2==e.elements.length&&e.elements.push(0),2==t.elements.length&&t.elements.push(0),e.elements.length>3||t.elements.length>3)return null;var n=t.modulus();return 0===n?null:(this.anchor=e,this.direction=r.create([t.elements[0]/n,t.elements[1]/n,t.elements[2]/n]),this)}},a.create=function(e,t){return(new a).setVectors(e,t)},a.X=a.create(r.Zero(3),r.i),a.Y=a.create(r.Zero(3),r.j),a.Z=a.create(r.Zero(3),r.k),e.exports=a},function(e,t,n){"use strict";var r,i,s=e.exports={};function o(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function u(e){if(r===setTimeout)return setTimeout(e,0);if((r===o||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:o}catch(e){r=o}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(e){i=a}}();var l,c=[],h=!1,f=-1;function m(){h&&l&&(h=!1,l.length?c=l.concat(c):f=-1,c.length&&d())}function d(){if(!h){var e=u(m);h=!0;for(var t=c.length;t;){for(l=c,c=[];++f<t;)l&&l[f].run();f=-1,t=c.length}l=null,h=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function v(){}s.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];c.push(new p(e,t)),1!==c.length||h||u(d)},p.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=v,s.addListener=v,s.once=v,s.off=v,s.removeListener=v,s.removeAllListeners=v,s.emit=v,s.prependListener=v,s.prependOnceListener=v,s.listeners=function(e){return[]},s.binding=function(e){throw new Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw new Error("process.chdir is not supported")},s.umask=function(){return 0}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var r=function(e){return e&&e.__esModule?e:{default:e}}(n(3));function i(){}i.createFromElementId=function(e){const t=document.getElementById(e);(0,r.default)(t,"Could not find shader with ID: "+e);let n="",i=t.firstChild;for(;i;)3===i.nodeType&&(n+=i.textContent),i=i.nextSibling;const s=new Scriptor;return s.type=t.type,s.source=n,s},i.createFromSource=function(e,t){const n=new i;return n.type=e,n.source=t,n}},function(e,t,n){"use strict";var r=Function("return this")();t.Vector=n(0),r.$V=t.Vector.create,t.Matrix=n(2),r.$M=t.Matrix.create,t.Line=n(5),r.$L=t.Line.create,t.Plane=n(9),r.$P=t.Plane.create,t.Line.Segment=n(19),t.Sylvester=n(1)},function(e,t,n){"use strict";var r=n(0),i=n(2),s=n(5),o=n(1);function a(){}a.prototype={eql:function(e){return this.contains(e.anchor)&&this.isParallelTo(e)},dup:function(){return a.create(this.anchor,this.normal)},translate:function(e){var t=e.elements||e;return a.create([this.anchor.elements[0]+t[0],this.anchor.elements[1]+t[1],this.anchor.elements[2]+(t[2]||0)],this.normal)},isParallelTo:function(e){var t;return e.normal?(t=this.normal.angleFrom(e.normal),Math.abs(t)<=o.precision||Math.abs(Math.PI-t)<=o.precision):e.direction?this.normal.isPerpendicularTo(e.direction):null},isPerpendicularTo:function(e){var t=this.normal.angleFrom(e.normal);return Math.abs(Math.PI/2-t)<=o.precision},distanceFrom:function(e){if(this.intersects(e)||this.contains(e))return 0;if(e.anchor){var t=this.anchor.elements,n=e.anchor.elements,r=this.normal.elements;return Math.abs((t[0]-n[0])*r[0]+(t[1]-n[1])*r[1]+(t[2]-n[2])*r[2])}var i=e.elements||e;t=this.anchor.elements,r=this.normal.elements;return Math.abs((t[0]-i[0])*r[0]+(t[1]-i[1])*r[1]+(t[2]-(i[2]||0))*r[2])},contains:function(e){if(e.normal)return null;if(e.direction)return this.contains(e.anchor)&&this.contains(e.anchor.add(e.direction));var t=e.elements||e,n=this.anchor.elements,r=this.normal.elements;return Math.abs(r[0]*(n[0]-t[0])+r[1]*(n[1]-t[1])+r[2]*(n[2]-(t[2]||0)))<=o.precision},intersects:function(e){return void 0===e.direction&&void 0===e.normal?null:!this.isParallelTo(e)},intersectionWith:function(e){if(!this.intersects(e))return null;if(e.direction){var t=e.anchor.elements,n=e.direction.elements,o=this.anchor.elements,a=((l=this.normal.elements)[0]*(o[0]-t[0])+l[1]*(o[1]-t[1])+l[2]*(o[2]-t[2]))/(l[0]*n[0]+l[1]*n[1]+l[2]*n[2]);return r.create([t[0]+n[0]*a,t[1]+n[1]*a,t[2]+n[2]*a])}if(e.normal){for(var u=this.normal.cross(e.normal).toUnitVector(),l=this.normal.elements,c=(t=this.anchor.elements,e.normal.elements),h=e.anchor.elements,f=i.Zero(2,2),m=0;f.isSingular();)m++,f=i.create([[l[m%3],l[(m+1)%3]],[c[m%3],c[(m+1)%3]]]);for(var d=f.inverse().elements,p=l[0]*t[0]+l[1]*t[1]+l[2]*t[2],v=c[0]*h[0]+c[1]*h[1]+c[2]*h[2],g=[d[0][0]*p+d[0][1]*v,d[1][0]*p+d[1][1]*v],y=[],b=1;b<=3;b++)y.push(m==b?0:g[(b+(5-m)%3)%3]);return s.create(y,u)}},pointClosestTo:function(e){var t=e.elements||e,n=this.anchor.elements,i=this.normal.elements,s=(n[0]-t[0])*i[0]+(n[1]-t[1])*i[1]+(n[2]-(t[2]||0))*i[2];return r.create([t[0]+i[0]*s,t[1]+i[1]*s,(t[2]||0)+i[2]*s])},rotate:function(e,t){var n=e.determinant?e.elements:i.Rotation(e,t.direction).elements,r=t.pointClosestTo(this.anchor).elements,s=this.anchor.elements,o=this.normal.elements,u=r[0],l=r[1],c=r[2],h=s[0]-u,f=s[1]-l,m=s[2]-c;return a.create([u+n[0][0]*h+n[0][1]*f+n[0][2]*m,l+n[1][0]*h+n[1][1]*f+n[1][2]*m,c+n[2][0]*h+n[2][1]*f+n[2][2]*m],[n[0][0]*o[0]+n[0][1]*o[1]+n[0][2]*o[2],n[1][0]*o[0]+n[1][1]*o[1]+n[1][2]*o[2],n[2][0]*o[0]+n[2][1]*o[1]+n[2][2]*o[2]])},reflectionIn:function(e){if(e.normal){var t=this.anchor.elements,n=this.normal.elements,r=t[0],i=t[1],s=t[2],o=n[0],u=n[1],l=n[2],c=this.anchor.reflectionIn(e).elements,h=r+o,f=i+u,m=s+l,d=e.pointClosestTo([h,f,m]).elements,p=[d[0]+(d[0]-h)-c[0],d[1]+(d[1]-f)-c[1],d[2]+(d[2]-m)-c[2]];return a.create(c,p)}if(e.direction)return this.rotate(Math.PI,e);var v=e.elements||e;return a.create(this.anchor.reflectionIn([v[0],v[1],v[2]||0]),this.normal)},setVectors:function(e,t,n){if(null===(e=(e=r.create(e)).to3D()))return null;if(null===(t=(t=r.create(t)).to3D()))return null;if(void 0===n)n=null;else if(null===(n=(n=r.create(n)).to3D()))return null;var i,s,o=e.elements[0],a=e.elements[1],u=e.elements[2],l=t.elements[0],c=t.elements[1],h=t.elements[2];if(null!==n){var f=n.elements[0],m=n.elements[1],d=n.elements[2];if(0===(s=(i=r.create([(c-a)*(d-u)-(h-u)*(m-a),(h-u)*(f-o)-(l-o)*(d-u),(l-o)*(m-a)-(c-a)*(f-o)])).modulus()))return null;i=r.create([i.elements[0]/s,i.elements[1]/s,i.elements[2]/s])}else{if(0===(s=Math.sqrt(l*l+c*c+h*h)))return null;i=r.create([t.elements[0]/s,t.elements[1]/s,t.elements[2]/s])}return this.anchor=e,this.normal=i,this}},a.create=function(e,t,n){return(new a).setVectors(e,t,n)},a.XY=a.create(r.Zero(3),r.k),a.YZ=a.create(r.Zero(3),r.i),a.ZX=a.create(r.Zero(3),r.j),a.YX=a.XY,a.ZY=a.YZ,a.XZ=a.ZX,a.fromPoints=function(e){var t,n,i,s,u,l,c,h,f,m,d=e.length,p=[],v=r.Zero(3);for(t=0;t<d;t++){if(null===(n=r.create(e[t]).to3D()))return null;if(p.push(n),(i=p.length)>2){if(u=p[i-1].elements,l=p[i-2].elements,c=p[i-3].elements,s=r.create([(u[1]-l[1])*(c[2]-l[2])-(u[2]-l[2])*(c[1]-l[1]),(u[2]-l[2])*(c[0]-l[0])-(u[0]-l[0])*(c[2]-l[2]),(u[0]-l[0])*(c[1]-l[1])-(u[1]-l[1])*(c[0]-l[0])]).toUnitVector(),i>3&&null!==(f=s.angleFrom(m))&&!(Math.abs(f)<=o.precision||Math.abs(f-Math.PI)<=o.precision))return null;v=v.add(s),m=s}}return u=p[1].elements,l=p[0].elements,c=p[i-1].elements,h=p[i-2].elements,v=v.add(r.create([(u[1]-l[1])*(c[2]-l[2])-(u[2]-l[2])*(c[1]-l[1]),(u[2]-l[2])*(c[0]-l[0])-(u[0]-l[0])*(c[2]-l[2]),(u[0]-l[0])*(c[1]-l[1])-(u[1]-l[1])*(c[0]-l[0])]).toUnitVector()).add(r.create([(l[1]-c[1])*(h[2]-c[2])-(l[2]-c[2])*(h[1]-c[1]),(l[2]-c[2])*(h[0]-c[0])-(l[0]-c[0])*(h[2]-c[2]),(l[0]-c[0])*(h[1]-c[1])-(l[1]-c[1])*(h[0]-c[0])]).toUnitVector()),a.create(p[0],v)},e.exports=a},function(e,t,n){"use strict";var r=l(n(11)),i=l(n(12)),s=l(n(20)),o=l(n(21)),a=n(22),u=l(n(23));function l(e){return e&&e.__esModule?e:{default:e}}const c=(0,u.default)("wsavc");e.exports=class extends a.EventEmitter{constructor(e,t,n){super(),this.canvas=e,this.canvastype=t,this.now=(new Date).getTime(),this.avc=new r.default,this.ws,this.pktnum=0,this.avc.onPictureDecoded=((e,t,n,...r)=>this.initCanvas(t,n,[e,t,n,...r]))}decode(e){let t="invalid frame";e.length>4&&(101===e[4]?t="I frame":65===e[4]?t="P frame":103===e[4]?t="SPS":104===e[4]&&(t="PPS")),c(`Passed ${t} to decoder ${31&e[4]}`),this.avc.decode(e)}connect(e){void 0!==this.ws&&(this.ws.close(),delete this.ws),this.ws=new WebSocket(e),this.ws.binaryType="arraybuffer",this.ws.onopen=(()=>{c("Connected to "+e),this.emit("connected",e)});let t=[];this.ws.onmessage=(e=>{if("string"==typeof e.data)return this.cmd(JSON.parse(e.data));this.pktnum++;const n=new Uint8Array(e.data);t.push(n),this.pktnum%10==0&&this.emit("packet_recv",this.pktnum)});let n=!0;const r=function(){if(!n)return;if(t.length>300)t=[];else if(t.length>5){c("Dropping frames",t.length);const e=t.findIndex(e=>7==(31&e[4]));e>=0&&(t=t.slice(e))}const e=t.shift();e&&this.decode(e),requestAnimationFrame(r)}.bind(this);return r(),this.ws.onclose=(()=>{n=!1,this.emit("disconnected"),c("WSAvcPlayer: Connection closed")}),this.ws}initCanvas(e,t,n){const r=new("webgl"===this.canvastype||"YUVWebGLCanvas"===this.canvastype?i.default:s.default)(this.canvas,new o.default(e,t));if(this.avc.onPictureDecoded=((n,i,s,...o)=>i!==e||s!==t?this.initCanvas(i,s,[n,i,s,...o]):r.decode(n,i,s,...o)),n)return r.decode(...n)}cmd(e){switch(c("Incoming request",e),e.action){case"initalize":{const{width:t,height:n}=e.payload;return this.emit("initalized",e.payload)}default:return this.emit(e.action,e.payload)}}disconnect(){this.ws.close()}send(e,t){return this.ws.send(JSON.stringify({action:e,payload:t}))}},e.exports.debug=u.default},function(e,t,n){"use strict";(function(n){var r,i,s;!function(n,o){i=[],void 0===(s="function"==typeof(r=o)?r.apply(t,i):r)||(e.exports=s)}(0,function(){var t;!function(){(t=this)||("undefined"!=typeof window?t=window:"undefined"!=typeof self&&(t=self))}();var r=function(r,i){var s,o=void 0!==o?o:{},a={};for(s in o)o.hasOwnProperty(s)&&(a[s]=o[s]);o.arguments=[],o.thisProgram="./this.program",o.quit=function(e,t){throw t},o.preRun=[],o.postRun=[];var u,l,c=!1,h=!1,f=!1,m=!1;if(o.ENVIRONMENT)if("WEB"===o.ENVIRONMENT)c=!0;else if("WORKER"===o.ENVIRONMENT)h=!0;else if("NODE"===o.ENVIRONMENT)f=!0;else{if("SHELL"!==o.ENVIRONMENT)throw new Error("Module['ENVIRONMENT'] value is not valid. must be one of: WEB|WORKER|NODE|SHELL.");m=!0}else c="object"==typeof window,h="function"==typeof importScripts,f="object"==typeof n&&!1,m=!c&&!f&&!h;if(f)o.read=function(e,t){var n;return u||(u=null("fs")),l||(l=null("path")),e=l.normalize(e),n=u.readFileSync(e),t?n:n.toString()},o.readBinary=function(e){var t=o.read(e,!0);return t.buffer||(t=new Uint8Array(t)),y(t.buffer),t},n.argv.length>1&&(o.thisProgram=n.argv[1].replace(/\\/g,"/")),o.arguments=n.argv.slice(2),e.exports=o,n.on("uncaughtException",function(e){if(!(e instanceof re))throw e}),n.on("unhandledRejection",function(e,t){n.exit(1)}),o.inspect=function(){return"[Emscripten Module object]"};else if(m)"undefined"!=typeof read&&(o.read=function(e){return read(e)}),o.readBinary=function(e){var t;return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(y("object"==typeof(t=read(e,"binary"))),t)},"undefined"!=typeof scriptArgs?o.arguments=scriptArgs:void 0!==arguments&&(o.arguments=arguments),"function"==typeof quit&&(o.quit=function(e,t){quit(e)});else{if(!c&&!h)throw new Error("not compiled for this environment");o.read=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},h&&(o.readBinary=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),o.readAsync=function(e,t,n){var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=function(){200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)},o.setWindowTitle=function(e){document.title=e}}for(s in o.print="undefined"!=typeof console?console.log.bind(console):"undefined"!=typeof print?print:null,o.printErr="undefined"!=typeof printErr?printErr:"undefined"!=typeof console&&console.warn.bind(console)||o.print,o.print=o.print,o.printErr=o.printErr,a)a.hasOwnProperty(s)&&(o[s]=a[s]);a=void 0;var d=16;function p(e,t){return t||(t=d),e=Math.ceil(e/t)*t}var v={"f64-rem":function(e,t){return e%t},debugger:function(){}},g=(new Array(0),0);function y(e,t){e||se("Assertion failed: "+t)}function b(e,t){if(0===t||!e)return"";for(var n,r=0,i=0;r|=n=T[e+i>>0],(0!=n||t)&&(i++,!t||i!=t););t||(t=i);var s="";if(r<128){for(var o;t>0;)o=String.fromCharCode.apply(String,T.subarray(e,e+Math.min(t,1024))),s=s?s+o:o,e+=1024,t-=1024;return s}return function(e){return x(T,e)}(e)}var w="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function x(e,t){for(var n=t;e[n];)++n;if(n-t>16&&e.subarray&&w)return w.decode(e.subarray(t,n));for(var r,i,s,o,a,u="";;){if(!(r=e[t++]))return u;if(128&r)if(i=63&e[t++],192!=(224&r))if(s=63&e[t++],224==(240&r)?r=(15&r)<<12|i<<6|s:(o=63&e[t++],240==(248&r)?r=(7&r)<<18|i<<12|s<<6|o:(a=63&e[t++],r=248==(252&r)?(3&r)<<24|i<<18|s<<12|o<<6|a:(1&r)<<30|i<<24|s<<18|o<<12|a<<6|63&e[t++])),r<65536)u+=String.fromCharCode(r);else{var l=r-65536;u+=String.fromCharCode(55296|l>>10,56320|1023&l)}else u+=String.fromCharCode((31&r)<<6|i);else u+=String.fromCharCode(r)}}"undefined"!=typeof TextDecoder&&new TextDecoder("utf-16le");var M,T,E,A,_,S,P,R,C,I=65536,U=16777216;function F(){o.HEAP8=new Int8Array(M),o.HEAP16=E=new Int16Array(M),o.HEAP32=A=new Int32Array(M),o.HEAPU8=T=new Uint8Array(M),o.HEAPU16=new Uint16Array(M),o.HEAPU32=new Uint32Array(M),o.HEAPF32=new Float32Array(M),o.HEAPF64=new Float64Array(M)}function L(){se("Cannot enlarge memory arrays. Either (1) compile with  -s TOTAL_MEMORY=X  with X higher than the current value "+B+", (2) compile with  -s ALLOW_MEMORY_GROWTH=1  which allows increasing the size at runtime, or (3) if you want malloc to return NULL (0) instead of this abort, compile with  -s ABORTING_MALLOC=0 ")}_=P=0,S=!1;var D=o.TOTAL_STACK||5242880,B=o.TOTAL_MEMORY||52428800;if(B<D&&o.printErr("TOTAL_MEMORY should be larger than TOTAL_STACK, was "+B+"! (TOTAL_STACK="+D+")"),o.buffer?M=o.buffer:("object"==typeof WebAssembly&&"function"==typeof WebAssembly.Memory?(o.wasmMemory=new WebAssembly.Memory({initial:B/I,maximum:B/I}),M=o.wasmMemory.buffer):M=new ArrayBuffer(B),o.buffer=M),F(),A[0]=1668509029,E[1]=25459,115!==T[2]||99!==T[3])throw"Runtime error: expected the system to be little-endian!";function O(e){for(;e.length>0;){var t=e.shift();if("function"!=typeof t){var n=t.func;"number"==typeof n?void 0===t.arg?o.dynCall_v(n):o.dynCall_vi(n,t.arg):n(void 0===t.arg?null:t.arg)}else t()}}var V=[],N=[],W=[],z=[],k=[],q=!1;function j(e){V.unshift(e)}function G(e){k.unshift(e)}Math.abs,Math.cos,Math.sin,Math.tan,Math.acos,Math.asin,Math.atan,Math.atan2,Math.exp,Math.log,Math.sqrt,Math.ceil,Math.floor,Math.pow,Math.imul,Math.fround,Math.round,Math.min,Math.max,Math.clz32,Math.trunc;var Y=0,X=null,H=null;o.preloadedImages={},o.preloadedAudios={};var $="data:application/octet-stream;base64,";function Z(e){return String.prototype.startsWith?e.startsWith($):0===e.indexOf($)}!function(){var e="avc.wast",t="avc.wasm",n="avc.temp.asm.js";"function"==typeof o.locateFile&&(Z(e)||(e=o.locateFile(e)),Z(t)||(t=o.locateFile(t)),Z(n)||(n=o.locateFile(n)));var r={global:null,env:null,asm2wasm:v,parent:o},i=null;function s(e){var t=o.buffer;e.byteLength<t.byteLength&&o.printErr("the new buffer in mergeMemory is smaller than the previous one. in native wasm, we should grow memory here");var n=new Int8Array(t);new Int8Array(e).set(n),function(e){o.buffer=M=e}(e),F()}function a(){try{if(o.wasmBinary)return new Uint8Array(o.wasmBinary);if(o.readBinary)return o.readBinary(t);throw"on the web, we need the wasm binary to be preloaded and set on Module['wasmBinary']. emcc.py will do that for you when generating HTML (but not JS)"}catch(e){se(e)}}function u(e,n,u){if("object"!=typeof WebAssembly)return o.printErr("no native wasm support detected"),!1;if(!(o.wasmMemory instanceof WebAssembly.Memory))return o.printErr("no native wasm Memory in use"),!1;function l(e,t){(i=e.exports).memory&&s(i.memory),o.asm=i,o.usingWasm=!0,function(e){if(Y--,o.monitorRunDependencies&&o.monitorRunDependencies(Y),0==Y&&(null!==X&&(clearInterval(X),X=null),H)){var t=H;H=null,t()}}()}if(n.memory=o.wasmMemory,r.global={NaN:NaN,Infinity:1/0},r["global.Math"]=Math,r.env=n,Y++,o.monitorRunDependencies&&o.monitorRunDependencies(Y),o.instantiateWasm)try{return o.instantiateWasm(r,l)}catch(e){return o.printErr("Module.instantiateWasm callback failed with error: "+e),!1}function f(e){l(e.instance,e.module)}function m(e){(o.wasmBinary||!c&&!h||"function"!=typeof fetch?new Promise(function(e,t){e(a())}):fetch(t,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+t+"'";return e.arrayBuffer()}).catch(function(){return a()})).then(function(e){return WebAssembly.instantiate(e,r)}).then(e).catch(function(e){o.printErr("failed to asynchronously prepare wasm: "+e),se(e)})}return o.wasmBinary||"function"!=typeof WebAssembly.instantiateStreaming||Z(t)||"function"!=typeof fetch?m(f):WebAssembly.instantiateStreaming(fetch(t,{credentials:"same-origin"}),r).then(f).catch(function(e){o.printErr("wasm streaming compile failed: "+e),o.printErr("falling back to ArrayBuffer instantiation"),m(f)}),{}}o.asmPreload=o.asm;var l=o.reallocBuffer;o.reallocBuffer=function(e){return"asmjs"===f?l(e):function(e){e=function(e,t){return e%t>0&&(e+=t-e%t),e}(e,o.usingWasm?I:U);var t=o.buffer.byteLength;if(o.usingWasm)try{return-1!==o.wasmMemory.grow((e-t)/65536)?o.buffer=o.wasmMemory.buffer:null}catch(e){return null}}(e)};var f="";o.asm=function(e,t,n){if(!(t=function(e){return e}(t)).table){var r=o.wasmTableSize;void 0===r&&(r=1024);var i=o.wasmMaxTableSize;"object"==typeof WebAssembly&&"function"==typeof WebAssembly.Table?t.table=void 0!==i?new WebAssembly.Table({initial:r,maximum:i,element:"anyfunc"}):new WebAssembly.Table({initial:r,element:"anyfunc"}):t.table=new Array(r),o.wasmTable=t.table}var s;return t.memoryBase||(t.memoryBase=o.STATIC_BASE),t.tableBase||(t.tableBase=0),y(s=u(0,t),"no binaryen method succeeded."),s}}(),_=10912,N.push();o.STATIC_BASE=1024,o.STATIC_BUMP=9888,_+=16;var J={varargs:0,get:function(e){return J.varargs+=4,A[J.varargs-4>>2]},getStr:function(){return b(J.get())},get64:function(){var e=J.get(),t=J.get();return y(e>=0?0===t:-1===t),e},getZero:function(){y(0===J.get())}};function K(){r()}function Q(e,t,n){i(e,t,n)}o._broadwayOnHeadersDecoded=K,o._broadwayOnPictureDecoded=Q,C=function(e){y(!S);var t=_;return _=_+e+15&-16,t}(4),R=p((P=p(_))+D),A[C>>2]=R,S=!0,o.wasmTableSize=10,o.wasmMaxTableSize=10,o.asmGlobalArg={},o.asmLibraryArg={abort:se,enlargeMemory:function(){L()},getTotalMemory:function(){return B},abortOnCannotGrowMemory:L,___setErrNo:function(e){return o.___errno_location&&(A[o.___errno_location()>>2]=e),e},___syscall140:function(e,t){J.varargs=t;try{var n=J.getStreamFromFD(),r=(J.get(),J.get()),i=J.get(),s=J.get(),o=r;return FS.llseek(n,o,s),A[i>>2]=n.position,n.getdents&&0===o&&0===s&&(n.getdents=null),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||se(e),-e.errno}},___syscall146:function e(t,n){J.varargs=n;try{var r=J.get(),i=J.get(),s=J.get(),a=0;e.buffers||(e.buffers=[null,[],[]],e.printChar=function(t,n){var r=e.buffers[t];y(r),0===n||10===n?((1===t?o.print:o.printErr)(x(r,0)),r.length=0):r.push(n)});for(var u=0;u<s;u++){for(var l=A[i+8*u>>2],c=A[i+(8*u+4)>>2],h=0;h<c;h++)e.printChar(r,T[l+h]);a+=c}return a}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||se(e),-e.errno}},___syscall54:function(e,t){J.varargs=t;try{return 0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||se(e),-e.errno}},___syscall6:function(e,t){J.varargs=t;try{var n=J.getStreamFromFD();return FS.close(n),0}catch(e){return"undefined"!=typeof FS&&e instanceof FS.ErrnoError||se(e),-e.errno}},_broadwayOnHeadersDecoded:K,_broadwayOnPictureDecoded:Q,_emscripten_memcpy_big:function(e,t,n){return T.set(T.subarray(t,t+n),e),e},DYNAMICTOP_PTR:C,STACKTOP:P};var ee=o.asm(o.asmGlobalArg,o.asmLibraryArg,M);o.asm=ee;var te,ne;o._broadwayCreateStream=function(){return o.asm._broadwayCreateStream.apply(null,arguments)},o._broadwayExit=function(){return o.asm._broadwayExit.apply(null,arguments)},o._broadwayGetMajorVersion=function(){return o.asm._broadwayGetMajorVersion.apply(null,arguments)},o._broadwayGetMinorVersion=function(){return o.asm._broadwayGetMinorVersion.apply(null,arguments)},o._broadwayInit=function(){return o.asm._broadwayInit.apply(null,arguments)},o._broadwayPlayStream=function(){return o.asm._broadwayPlayStream.apply(null,arguments)};function re(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}function ie(e){function t(){o.calledRun||(o.calledRun=!0,g||(q||(q=!0,O(N)),O(W),o.onRuntimeInitialized&&o.onRuntimeInitialized(),function(){if(o.postRun)for("function"==typeof o.postRun&&(o.postRun=[o.postRun]);o.postRun.length;)G(o.postRun.shift());O(k)}()))}e=e||o.arguments,Y>0||(!function(){if(o.preRun)for("function"==typeof o.preRun&&(o.preRun=[o.preRun]);o.preRun.length;)j(o.preRun.shift());O(V)}(),Y>0||o.calledRun||(o.setStatus?(o.setStatus("Running..."),setTimeout(function(){setTimeout(function(){o.setStatus("")},1),t()},1)):t()))}function se(e){throw o.onAbort&&o.onAbort(e),void 0!==e?(o.print(e),o.printErr(e),e=JSON.stringify(e)):e="",g=!0,1,"abort("+e+"). Build with -s ASSERTIONS=1 for more info."}if(o.asm=ee,re.prototype=new Error,re.prototype.constructor=re,H=function e(){o.calledRun||ie(),o.calledRun||(H=e)},o.run=ie,o.exit=function(e,t){t&&o.noExitRuntime&&0===e||(o.noExitRuntime||(g=!0,e,P=te,O(z),!0,o.onExit&&o.onExit(e)),f&&n.exit(e),o.quit(e,new re(e)))},o.abort=se,o.preInit)for("function"==typeof o.preInit&&(o.preInit=[o.preInit]);o.preInit.length>0;)o.preInit.pop()();o.noExitRuntime=!0,ie(),void 0!==t&&t.Module&&(ne=t.Module),void 0!==o&&(ne=o),ne._broadwayOnHeadersDecoded=r,ne._broadwayOnPictureDecoded=i;var oe,ae=!1;return ne.onRuntimeInitialized=function(){ae=!0,oe&&oe(ne)},function(e){ae?e(ne):oe=e}};return function(){var e=function(){return(new Date).getTime()};"undefined"!=typeof performance&&performance.now&&(e=function(){return performance.now()});var n=function(t){var n;this.options=t||{},this.now=e;var i,o,a=function(t,r,o){var a,u=this.pictureBuffers[t];u||(u=this.pictureBuffers[t]=i(t,r*o*3/2));var l=!1;if(this.infoAr.length&&(l=!0,a=this.infoAr),this.infoAr=[],this.options.rgb){n||(n=s(r,o)),n.inp.set(u),n.doit();var c=new Uint8Array(n.outSize);return c.set(n.out),l&&(a[0].finishDecoding=e()),void this.onPictureDecoded(c,r,o,a)}l&&(a[0].finishDecoding=e()),this.onPictureDecoded(u,r,o,a)}.bind(this);this.options.sliceMode&&(a=function(t,n,r,s){var a=this.pictureBuffers[t];a||(a=this.pictureBuffers[t]=i(t,n*r*3/2));var u,l=this.pictureBuffers[s];l||(l=this.pictureBuffers[s]=o(s,18));this.infoAr.length&&(!0,u=this.infoAr),this.infoAr=[],u[0].finishDecoding=e();for(var c=[],h=0;h<20;++h)c.push(l[h]);u[0].sliceInfoAr=c,this.onPictureDecoded(a,n,r,u)}.bind(this));var u=r.apply({},[function(){},a]),l=this;this.onPictureDecoded=function(e,t,n,r){},this.onDecoderReady=function(){};var c=[];this.decode=function(e,t,n){c.push([e,t,n])},u(function(t){t.HEAP8;var n=t.HEAPU8;t.HEAP16,t.HEAP32;t._broadwayInit(),i=function(e,t){return n.subarray(e,e+t)},o=function(e,t){return new Uint32Array(n.buffer,e,t)},l.streamBuffer=i(t._broadwayCreateStream(1048576),1048576),l.pictureBuffers={},l.infoAr=[];var r=0;if(l.options.sliceMode?(r=l.options.sliceNum,l.decode=function(n,i,s){l.infoAr.push(i),i.startDecoding=e();var o,a=i.nals;if(!a){a=[],i.nals=a;var u=n.length,c=!1,h=0,f=0;for(o=0;o<u;++o)if(1===n[o]&&0===n[o-1]&&0===n[o-2]){var m=o-2;0===n[o-3]&&(m=o-3),c&&a.push({offset:h,end:m,type:31&n[f]}),h=m,f=m+3,0===n[o-3]&&(f=m+4),c=!0}c&&a.push({offset:h,end:o,type:31&n[f]})}var d,p=0,v=0;for(o=0;o<a.length;++o)1===a[o].type||5===a[o].type?(p===r&&(d=n.subarray(a[o].offset,a[o].end),l.streamBuffer[v]=0,v+=1,l.streamBuffer.set(d,v),v+=d.length),p+=1):(d=n.subarray(a[o].offset,a[o].end),l.streamBuffer[v]=0,v+=1,l.streamBuffer.set(d,v),v+=d.length,t._broadwayPlayStream(v),v=0);s(),t._broadwayPlayStream(v)}):l.decode=function(n,r){r&&(l.infoAr.push(r),r.startDecoding=e()),l.streamBuffer.set(n),t._broadwayPlayStream(n.length)},c.length){var s=0;for(s=0;s<c.length;++s)l.decode(c[s][0],c[s][1],c[s][2]);c=[]}l.onDecoderReady(l)})};n.prototype={};var i={},s=function(e,n){var r=e+"x"+n;if(i[r])return i[r];for(var s=e*n,o=(0|s)>>2,a=s+o+o,u=e*n*4,l=a+u+4*Math.pow(2,24),c=Math.pow(2,24),h=c;h<l;)h+=c;var f=new ArrayBuffer(h),m=function(e,t,n){"use asm";var r=e.Math.imul,i=e.Math.min,s=e.Math.max,o=e.Math.pow,a=new e.Uint8Array(n),u=new e.Uint32Array(n),l=new e.Uint8Array(n),c=new e.Uint8Array(n),h=new e.Uint32Array(n),f=0,m=0,d=0,p=0,v=0,g=0,y=0,b=0,w=0,x=0;function M(e,t){e=e|0;t=t|0;var n=0;var i=0;f=e;w=r(e,4)|0;m=t;d=r(f|0,m|0)|0;p=(d|0)>>2;g=r(r(f,m)|0,4)|0;v=d+p|0+p|0;b=0;y=b+g|0;x=y+v|0;i=~~+o(+2,+24);i=r(i,4)|0;for(n=0|0;(n|0)<(i|0)|0;n=n+4|0){h[(x+n|0)>>2]=0}}function T(){var e=0;var t=0;var n=0;var r=0;var i=0;var s=0;var o=0;var a=0;var u=0;var c=0;var v=0;var g=0;var M=0;var T=0;M=b|0;e=y|0;t=e+d|0|0;n=t+p|0;for(u=0;(u|0)<(m|0);u=u+2|0){v=t;g=n;for(c=0;(c|0)<(f|0);c=c+2|0){r=l[e>>0]|0;i=l[(e+f|0)>>0]|0;s=l[t>>0]|0;o=l[n>>0]|0;T=((r<<16|0)+(s<<8|0)|0)+o|0;a=h[(x+T|0)>>2]|0;if(a){}else{a=E(r,s,o)|0;h[(x+T|0)>>2]=a|0}h[M>>2]=a;T=((i<<16|0)+(s<<8|0)|0)+o|0;a=h[(x+T|0)>>2]|0;if(a){}else{a=E(i,s,o)|0;h[(x+T|0)>>2]=a|0}h[(M+w|0)>>2]=a;M=M+4|0;e=e+1|0;r=l[e>>0]|0;i=l[(e+f|0)>>0]|0;T=((r<<16|0)+(s<<8|0)|0)+o|0;a=h[(x+T|0)>>2]|0;if(a){}else{a=E(r,s,o)|0;h[(x+T|0)>>2]=a|0}h[M>>2]=a;T=((i<<16|0)+(s<<8|0)|0)+o|0;a=h[(x+T|0)>>2]|0;if(a){}else{a=E(i,s,o)|0;h[(x+T|0)>>2]=a|0}h[(M+w|0)>>2]=a;M=M+4|0;e=e+1|0;t=t+1|0;n=n+1|0}M=M+w|0;e=e+f|0}}function E(e,t,n){e=e|0;t=t|0;n=n|0;var o=0;var a=0;var u=0;var l=0;var c=0;var h=0;var f=0;var m=0;var d=0;c=r(1192,e-16|0)|0;h=r(1634,n-128|0)|0;f=r(832,n-128|0)|0;m=r(400,t-128|0)|0;d=r(2066,t-128|0)|0;o=(c+h|0)>>10|0;a=((c-f|0)-m|0)>>10|0;u=(c+d|0)>>10|0;if((o&255|0)!=(o|0)|0){o=i(255,s(0,o|0)|0)|0}if((a&255|0)!=(a|0)|0){a=i(255,s(0,a|0)|0)|0}if((u&255|0)!=(u|0)|0){u=i(255,s(0,u|0)|0)|0}l=255;l=l<<8|0;l=l+u|0;l=l<<8|0;l=l+a|0;l=l<<8|0;l=l+o|0;return l|0}return{init:M,doit:T}}(t,{},f);return m.init(e,n),i[r]=m,m.heap=f,m.out=new Uint8Array(f,0,u),m.inp=new Uint8Array(f,u,a),m.outSize=u,m};if("undefined"!=typeof self){var o,a,u,l,c,h,f=!1,m=!1,d=!1,p=0,v=0,g=0,y=0,b=[],w=[],x=function(e){if(w.length){for(var t=w.shift();t&&t.byteLength!==e;)t=w.shift();if(t)return t}return new ArrayBuffer(e)},M=function(e,t,n,r,i){var s=function(n,i){var s=0;for(s=0;s<16;++s){var o=n+r*s,a=i+r*s;t.set(e.subarray(o,a),o)}},o=function(n,i){var s=0;for(s=0;s<8;++s){var o=n+r/2*s,a=i+r/2*s;t.set(e.subarray(o,a),o)}},a=function(n,r){t.set(e.subarray(n,r),n)},u=n[0],l=n[1];l>0&&(s(u,l),o(n[2],n[3]),o(n[4],n[5])),u=n[6],(l=n[7])>0&&(s(u,l),o(n[8],n[9]),o(n[10],n[11])),u=n[12],(l=n[15])>0&&(a(u,l),a(n[13],n[16]),a(n[14],n[17]))},T=function(e){g=(v=e)-1};self.addEventListener("message",function(t){if(f){if(m&&t.data.reuse&&w.push(t.data.reuse),t.data.buf)return void(d&&0!==y?b.push(t.data):o.decode(new Uint8Array(t.data.buf,t.data.offset||0,t.data.length),t.data.info,function(){d&&p!==g&&postMessage(t.data,[t.data.buf])}));if(t.data.slice){var r=e();if(M(new Uint8Array(t.data.slice),u,t.data.infos[0].sliceInfoAr,t.data.width,t.data.height),t.data.theOne&&(M(u,new Uint8Array(t.data.slice),a,t.data.width,t.data.height),h>t.data.infos[0].timeDecoding&&(t.data.infos[0].timeDecoding=h),t.data.infos[0].timeCopy+=e()-r),postMessage(t.data,[t.data.slice]),0===(y-=1)&&b.length){var i=b.shift();o.decode(new Uint8Array(i.buf,i.offset||0,i.length),i.info,function(){d&&p!==g&&postMessage(i,[i.buf])})}return}if(t.data.setSliceCnt)return void T(t.data.sliceCnt)}else t.data&&"Broadway.js - Worker init"===t.data.type&&(f=!0,o=new n(t.data.options),t.data.options.sliceMode?(m=!0,d=!0,p=t.data.options.sliceNum,T(t.data.options.sliceCnt),o.onPictureDecoded=function(e,t,n,r){var i=new Uint8Array(x(e.length));M(e,i,r[0].sliceInfoAr,t),l=r[0].startDecoding,c=r[0].finishDecoding,h=c-l,r[0].timeDecoding=h,r[0].timeCopy=0,postMessage({slice:i.buffer,sliceNum:p,width:t,height:n,infos:r},[i.buffer]),y=v-1,u=e,a=r[0].sliceInfoAr}):t.data.options.reuseMemory?(m=!0,o.onPictureDecoded=function(e,t,n,r){var i=new Uint8Array(x(e.length));i.set(e,0,e.length),postMessage({buf:i.buffer,length:e.length,width:t,height:n,infos:r},[i.buffer])}):o.onPictureDecoded=function(e,t,n,r){e&&(e=new Uint8Array(e));var i=new Uint8Array(e.length);i.set(e,0,e.length),postMessage({buf:i.buffer,length:e.length,width:t,height:n,infos:r},[i.buffer])},postMessage({consoleLog:"broadway worker initialized"}))},!1)}return n.nowValue=e,n}()})}).call(t,n(6))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=u(n(13)),i=u(n(14)),s=u(n(15)),o=u(n(7)),a=u(n(16));function u(e){return e&&e.__esModule?e:{default:e}}const l=o.default.createFromSource("x-shader/x-vertex","\n  attribute vec3 aVertexPosition;\n  attribute vec2 aTextureCoord;\n  uniform mat4 uMVMatrix;\n  uniform mat4 uPMatrix;\n  varying highp vec2 vTextureCoord;\n  void main(void) {\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n    vTextureCoord = aTextureCoord;\n  }\n"),c=o.default.createFromSource("x-shader/x-fragment","\n  precision highp float;\n  varying highp vec2 vTextureCoord;\n  uniform sampler2D YTexture;\n  uniform sampler2D UTexture;\n  uniform sampler2D VTexture;\n  const mat4 YUV2RGB = mat4\n  (\n   1.1643828125, 0, 1.59602734375, -.87078515625,\n   1.1643828125, -.39176171875, -.81296875, .52959375,\n   1.1643828125, 2.017234375, 0, -1.081390625,\n   0, 0, 0, 1\n  );\n\n  void main(void) {\n   gl_FragColor = vec4( texture2D(YTexture,  vTextureCoord).x, texture2D(UTexture, vTextureCoord).x, texture2D(VTexture, vTextureCoord).x, 1) * YUV2RGB;\n  }\n");t.default=class extends a.default{constructor(e,t){super(e,t),this.decode=((e,t,n)=>{if(!e)return;if(!0===this.hasPerformanceCaveat()&&4!=this.frameCnt++)return;this.frameCnt=0;const r=t*n,i=r>>2;this.YTexture.fill(e.subarray(0,r)),this.UTexture.fill(e.subarray(r,r+i)),this.VTexture.fill(e.subarray(r+i,r+2*i)),this.drawScene()}),this.frameCnt=0}onInitShaders(){this.program=new r.default(this.gl),this.program.attach(new i.default(this.gl,l)),this.program.attach(new i.default(this.gl,c)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)}onInitTextures(){this.YTexture=new s.default(this.gl,this.size),this.UTexture=new s.default(this.gl,this.size.getHalfSize()),this.VTexture=new s.default(this.gl,this.size.getHalfSize())}onInitSceneTextures(){this.YTexture.bind(0,this.program,"YTexture"),this.UTexture.bind(1,this.program,"UTexture"),this.VTexture.bind(2,this.program,"VTexture")}fillYUVTextures(e,t,n){this.YTexture.fill(e),this.UTexture.fill(t),this.VTexture.fill(n)}toString(){return"YUVCanvas Size: "+this.size}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(e){return e&&e.__esModule?e:{default:e}}(n(3));t.default=class{constructor(e){this.gl=e,this.program=this.gl.createProgram()}attach(e){this.gl.attachShader(this.program,e.shader)}link(){this.gl.linkProgram(this.program),(0,r.default)(this.gl.getProgramParameter(this.program,this.gl.LINK_STATUS),"Unable to initialize the shader program.")}use(){this.gl.useProgram(this.program)}getAttributeLocation(e){return this.gl.getAttribLocation(this.program,e)}setMatrixUniform(e,t){const n=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(n,!1,t)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){if("x-shader/x-fragment"===t.type)this.shader=e.createShader(e.FRAGMENT_SHADER);else{if("x-shader/x-vertex"!==t.type)return void(0,r.default)("Unknown shader type: "+t.type);this.shader=e.createShader(e.VERTEX_SHADER)}e.shaderSource(this.shader,t.source),e.compileShader(this.shader),e.getShaderParameter(this.shader,e.COMPILE_STATUS)||(0,r.default)("An error occurred compiling the shaders: "+e.getShaderInfoLog(this.shader))};var r=function(e){return e&&e.__esModule?e:{default:e}}(n(4))},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(e){return e&&e.__esModule?e:{default:e}}(n(3));let i=null;t.default=class{constructor(e,t,n){this.gl=e,this.size=t,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=n||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,t.w,t.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}fill(e,t){const n=this.gl;(0,r.default)(e.length>=this.size.w*this.size.h,"Texture size mismatch, data:"+e.length+", texture: "+this.size.w*this.size.h),n.bindTexture(n.TEXTURE_2D,this.texture),t?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,n.UNSIGNED_BYTE,e)}bind(e,t,n){const r=this.gl;i||(i=[r.TEXTURE0,r.TEXTURE1,r.TEXTURE2]),r.activeTexture(i[e]),r.bindTexture(r.TEXTURE_2D,this.texture),r.uniform1i(r.getUniformLocation(t.program,n),e)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=a(n(7)),i=a(n(4)),s=n(17),o=n(8);function a(e){return e&&e.__esModule?e:{default:e}}const u=r.default.createFromSource("x-shader/x-vertex","\n  attribute vec3 aVertexPosition;\n  attribute vec2 aTextureCoord;\n  uniform mat4 uMVMatrix;\n  uniform mat4 uPMatrix;\n  varying highp vec2 vTextureCoord;\n  void main(void) {\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n    vTextureCoord = aTextureCoord;\n  }\n"),l=r.default.createFromSource("x-shader/x-fragment","\n  precision highp float;\n  varying highp vec2 vTextureCoord;\n  uniform sampler2D texture;\n  void main(void) {\n    gl_FragColor = texture2D(texture, vTextureCoord);\n  }\n");t.default=class{constructor(e,t,n){this.canvas=e,this.size=t,this.canvas.width=t.w,this.canvas.height=t.h,this.performanceCaveat=!1,this.onInitWebGL(),this.onInitShaders(),this.initBuffers(),n&&this.initFramebuffer(),this.onInitTextures(),this.initScene()}initFramebuffer(){const e=this.gl;this.framebuffer=e.createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer),this.framebufferTexture=new Texture(this.gl,this.size,e.RGBA);const t=e.createRenderbuffer();e.bindRenderbuffer(e.RENDERBUFFER,t),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,this.size.w,this.size.h),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this.framebufferTexture.texture,0),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,t)}initBuffers(){let e;const t=this.gl;this.quadVPBuffer=t.createBuffer(),t.bindBuffer(t.ARRAY_BUFFER,this.quadVPBuffer),e=[1,1,0,-1,1,0,1,-1,0,-1,-1,0],t.bufferData(t.ARRAY_BUFFER,new Float32Array(e),t.STATIC_DRAW),this.quadVPBuffer.itemSize=3,this.quadVPBuffer.numItems=4,this.quadVTCBuffer=t.createBuffer(),t.bindBuffer(t.ARRAY_BUFFER,this.quadVTCBuffer),e=[1,0,0,0,1,1,0,1],t.bufferData(t.ARRAY_BUFFER,new Float32Array(e),t.STATIC_DRAW)}mvIdentity(){this.mvMatrix=o.Matrix.I(4)}mvMultiply(e){this.mvMatrix=this.mvMatrix.x(e)}mvTranslate(e){this.mvMultiply(o.Matrix.Translation($V([e[0],e[1],e[2]])).ensure4x4())}setMatrixUniforms(){this.program.setMatrixUniform("uPMatrix",new Float32Array(this.perspectiveMatrix.flatten())),this.program.setMatrixUniform("uMVMatrix",new Float32Array(this.mvMatrix.flatten()))}initScene(){const e=this.gl;this.perspectiveMatrix=(0,s.makePerspective)(45,1,.1,100),this.mvIdentity(),this.mvTranslate([0,0,-2.4]),e.bindBuffer(e.ARRAY_BUFFER,this.quadVPBuffer),e.vertexAttribPointer(this.vertexPositionAttribute,3,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,this.quadVTCBuffer),e.vertexAttribPointer(this.textureCoordAttribute,2,e.FLOAT,!1,0,0),this.onInitSceneTextures(),this.setMatrixUniforms(),this.framebuffer&&(console.log("Bound Frame Buffer"),e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer))}toString(){return"WebGLCanvas Size: "+this.size}checkLastError(e){const t=this.gl.getError();if(t!==this.gl.NO_ERROR){let n=this.glNames[t];n=void 0!==n?n+"("+t+")":"Unknown WebGL ENUM (0x"+value.toString(16)+")",e?console.log("WebGL Error: %s, %s",e,n):console.log("WebGL Error: %s",n),console.trace()}}onInitWebGL(){try{this.gl=this.canvas.getContext("webgl2",{failIfMajorPerformanceCaveat:!0})}catch(e){console.warn("failed to initGL (webgl2 failIfMajorPerformanceCaveat)",e)}if(!this.gl)try{this.gl=this.canvas.getContext("experimental-webgl",{failIfMajorPerformanceCaveat:!0})}catch(e){console.warn("failed to initGL (experimental-webgl failIfMajorPerformanceCaveat)",e)}if(!this.gl){this.performanceCaveat=!0;try{this.gl=this.canvas.getContext("webgl2")}catch(e){console.warn("failed to initGL (webgl2)",e)}}if(!this.gl){this.performanceCaveat=!0;try{this.gl=this.canvas.getContext("experimental-webgl")}catch(e){console.warn("failed to initGL (experimental-webgl)",e)}}if(this.gl||(0,i.default)("Unable to initialize WebGL. Your browser may not support it."),!this.glNames){this.glNames={};for(const e in this.gl)"number"==typeof this.gl[e]&&(this.glNames[this.gl[e]]=e)}}onInitShaders(){this.program=new Program(this.gl),this.program.attach(new Shader(this.gl,u)),this.program.attach(new Shader(this.gl,l)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)}onInitTextures(){const e=this.gl;this.texture=new Texture(e,this.size,e.RGBA)}onInitSceneTextures(){this.texture.bind(0,this.program,"texture")}drawScene(){this.gl.viewport(0,0,this.gl.drawingBufferWidth,this.gl.drawingBufferHeight),this.gl.drawArrays(this.gl.TRIANGLE_STRIP,0,4)}readPixels(e){const t=this.gl;t.readPixels(0,0,this.size.w,this.size.h,t.RGBA,t.UNSIGNED_BYTE,e)}hasPerformanceCaveat(){return this.performanceCaveat}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.makePerspective=function(e,t,n,r){const i=n*Math.tan(e*Math.PI/360),o=-i;return s(o*t,i*t,o,i,n,r)},t.makeFrustum=s;var r=n(8);const i=r.Matrix.create;function s(e,t,n,r,s,o){return i([[2*s/(t-e),0,(t+e)/(t-e),0],[0,2*s/(r-n),(r+n)/(r-n),0],[0,0,-(o+s)/(o-s),-2*o*s/(o-s)],[0,0,-1,0]])}r.Matrix.Translation=function(e){if(2===e.elements.length){const t=r.Matrix.I(3);return t.elements[2][0]=e.elements[0],t.elements[2][1]=e.elements[1],t}if(3===e.elements.length){const t=r.Matrix.I(4);return t.elements[0][3]=e.elements[0],t.elements[1][3]=e.elements[1],t.elements[2][3]=e.elements[2],t}throw new Error("Invalid length for Translation")},r.Matrix.prototype.flatten=function(){const e=[];if(0===this.elements.length)return[];for(let t=0;t<this.elements[0].length;t++)for(let n=0;n<this.elements.length;n++)e.push(this.elements[n][t]);return e},r.Matrix.prototype.ensure4x4=function(){if(4===this.elements.length&&4===this.elements[0].length)return this;if(this.elements.length>4||this.elements[0].length>4)return null;for(let e=0;e<this.elements.length;e++)for(let t=this.elements[e].length;t<4;t++)e===t?this.elements[e].push(1):this.elements[e].push(0);for(let e=this.elements.length;e<4;e++)0===e?this.elements.push([1,0,0,0]):1===e?this.elements.push([0,1,0,0]):2===e?this.elements.push([0,0,1,0]):3===e&&this.elements.push([0,0,0,1]);return this},r.Vector.prototype.flatten=function(){return this.elements}},function(e,t,n){"use strict"},function(e,t,n){"use strict";var r=n(5),i=n(0);r.Segment=function(){},r.Segment.prototype={eql:function(e){return this.start.eql(e.start)&&this.end.eql(e.end)||this.start.eql(e.end)&&this.end.eql(e.start)},dup:function(){return r.Segment.create(this.start,this.end)},length:function(){var e=this.start.elements,t=this.end.elements,n=t[0]-e[0],r=t[1]-e[1],i=t[2]-e[2];return Math.sqrt(n*n+r*r+i*i)},toVector:function(){var e=this.start.elements,t=this.end.elements;return i.create([t[0]-e[0],t[1]-e[1],t[2]-e[2]])},midpoint:function(){var e=this.start.elements,t=this.end.elements;return i.create([(t[0]+e[0])/2,(t[1]+e[1])/2,(t[2]+e[2])/2])},bisectingPlane:function(){return Plane.create(this.midpoint(),this.toVector())},translate:function(e){var t=e.elements||e,n=this.start.elements,i=this.end.elements;return r.Segment.create([n[0]+t[0],n[1]+t[1],n[2]+(t[2]||0)],[i[0]+t[0],i[1]+t[1],i[2]+(t[2]||0)])},isParallelTo:function(e){return this.line.isParallelTo(e)},distanceFrom:function(e){var t=this.pointClosestTo(e);return null===t?null:t.distanceFrom(e)},contains:function(e){if(e.start&&e.end)return this.contains(e.start)&&this.contains(e.end);var t=(e.elements||e).slice();if(2==t.length&&t.push(0),this.start.eql(t))return!0;var n=this.start.elements,r=i.create([n[0]-t[0],n[1]-t[1],n[2]-(t[2]||0)]),s=this.toVector();return r.isAntiparallelTo(s)&&r.modulus()<=s.modulus()},intersects:function(e){return null!==this.intersectionWith(e)},intersectionWith:function(e){if(!this.line.intersects(e))return null;var t=this.line.intersectionWith(e);return this.contains(t)?t:null},pointClosestTo:function(e){if(e.normal){var t=this.line.intersectionWith(e);return null===t?null:this.pointClosestTo(t)}var n=this.line.pointClosestTo(e);return null===n?null:this.contains(n)?n:(this.line.positionOf(n)<0?this.start:this.end).dup()},setPoints:function(e,t){return e=i.create(e).to3D(),t=i.create(t).to3D(),null===e||null===t?null:(this.line=r.create(e,t.subtract(e)),this.start=e,this.end=t,this)}},r.Segment.create=function(e,t){return(new r.Segment).setPoints(e,t)},e.exports=r.Segment},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default=class{constructor(e,t){this.decode=((e,t,n)=>{if(!e)return;const r=t*n,i=r>>2,s=e.subarray(0,r),o=e.subarray(r,r+i),a=e.subarray(r+i,r+2*i);for(let e=0;e<n;e++)for(let n=0;n<t;n++){const r=n+e*t,i=~~(e/2)*~~(t/2)+~~(n/2),u=~~(e/2)*~~(t/2)+~~(n/2),l=1.164*(s[r]-16)+1.596*(a[u]-128),c=1.164*(s[r]-16)-.813*(a[u]-128)-.391*(o[i]-128),h=1.164*(s[r]-16)+2.018*(o[i]-128),f=4*r;this.canvasBuffer.data[f+0]=l,this.canvasBuffer.data[f+1]=c,this.canvasBuffer.data[f+2]=h,this.canvasBuffer.data[f+3]=255}this.canvasCtx.putImageData(this.canvasBuffer,0,0)}),this.canvas=e,this.canvasCtx=this.canvas.getContext("2d"),this.canvasBuffer=this.canvasCtx.createImageData(t.w,t.h)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r{constructor(e,t){this.w=e,this.h=t}toString(){return"("+this.w+", "+this.h+")"}getHalfSize(){return new r(this.w>>>1,this.h>>>1)}length(){return this.w*this.h}}t.default=r},function(e,t,n){"use strict";function r(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(e){return"function"==typeof e}function s(e){return"object"==typeof e&&null!==e}function o(e){return void 0===e}e.exports=r,r.EventEmitter=r,r.prototype._events=void 0,r.prototype._maxListeners=void 0,r.defaultMaxListeners=10,r.prototype.setMaxListeners=function(e){if(!function(e){return"number"==typeof e}(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},r.prototype.emit=function(e){var t,n,r,a,u,l;if(this._events||(this._events={}),"error"===e&&(!this._events.error||s(this._events.error)&&!this._events.error.length)){if((t=arguments[1])instanceof Error)throw t;var c=new Error('Uncaught, unspecified "error" event. ('+t+")");throw c.context=t,c}if(o(n=this._events[e]))return!1;if(i(n))switch(arguments.length){case 1:n.call(this);break;case 2:n.call(this,arguments[1]);break;case 3:n.call(this,arguments[1],arguments[2]);break;default:a=Array.prototype.slice.call(arguments,1),n.apply(this,a)}else if(s(n))for(a=Array.prototype.slice.call(arguments,1),r=(l=n.slice()).length,u=0;u<r;u++)l[u].apply(this,a);return!0},r.prototype.addListener=function(e,t){var n;if(!i(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,i(t.listener)?t.listener:t),this._events[e]?s(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,s(this._events[e])&&!this._events[e].warned&&(n=o(this._maxListeners)?r.defaultMaxListeners:this._maxListeners)&&n>0&&this._events[e].length>n&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace()),this},r.prototype.on=r.prototype.addListener,r.prototype.once=function(e,t){if(!i(t))throw TypeError("listener must be a function");var n=!1;function r(){this.removeListener(e,r),n||(n=!0,t.apply(this,arguments))}return r.listener=t,this.on(e,r),this},r.prototype.removeListener=function(e,t){var n,r,o,a;if(!i(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(o=(n=this._events[e]).length,r=-1,n===t||i(n.listener)&&n.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(s(n)){for(a=o;a-- >0;)if(n[a]===t||n[a].listener&&n[a].listener===t){r=a;break}if(r<0)return this;1===n.length?(n.length=0,delete this._events[e]):n.splice(r,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},r.prototype.removeAllListeners=function(e){var t,n;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(i(n=this._events[e]))this.removeListener(e,n);else if(n)for(;n.length;)this.removeListener(e,n[n.length-1]);return delete this._events[e],this},r.prototype.listeners=function(e){return this._events&&this._events[e]?i(this._events[e])?[this._events[e]]:this._events[e].slice():[]},r.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(i(t))return 1;if(t)return t.length}return 0},r.listenerCount=function(e,t){return e.listenerCount(t)}},function(e,t,n){"use strict";(function(r){function i(){var e;try{e=t.storage.debug}catch(e){}return!e&&void 0!==r&&"env"in r&&(e=r.env.DEBUG),e}(t=e.exports=n(24)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},t.formatArgs=function(e){var n=this.useColors;if(e[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+e[0]+(n?"%c ":" ")+"+"+t.humanize(this.diff),!n)return;var r="color: "+this.color;e.splice(1,0,r,"color: inherit");var i=0,s=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(s=++i)}),e.splice(s,0,r)},t.save=function(e){try{null==e?t.storage.removeItem("debug"):t.storage.debug=e}catch(e){}},t.load=i,t.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},t.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),t.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],t.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},t.enable(i())}).call(t,n(6))},function(e,t,n){"use strict";var r;function i(e){function n(){if(n.enabled){var e=n,i=+new Date,s=i-(r||i);e.diff=s,e.prev=r,e.curr=i,r=i;for(var o=new Array(arguments.length),a=0;a<o.length;a++)o[a]=arguments[a];o[0]=t.coerce(o[0]),"string"!=typeof o[0]&&o.unshift("%O");var u=0;o[0]=o[0].replace(/%([a-zA-Z%])/g,function(n,r){if("%%"===n)return n;u++;var i=t.formatters[r];if("function"==typeof i){var s=o[u];n=i.call(e,s),o.splice(u,1),u--}return n}),t.formatArgs.call(e,o),(n.log||t.log||console.log.bind(console)).apply(e,o)}}return n.namespace=e,n.enabled=t.enabled(e),n.useColors=t.useColors(),n.color=function(e){var n,r=0;for(n in e)r=(r<<5)-r+e.charCodeAt(n),r|=0;return t.colors[Math.abs(r)%t.colors.length]}(e),"function"==typeof t.init&&t.init(n),n}(t=e.exports=i.debug=i.default=i).coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){t.enable("")},t.enable=function(e){t.save(e),t.names=[],t.skips=[];for(var n=("string"==typeof e?e:"").split(/[\s,]+/),r=n.length,i=0;i<r;i++)n[i]&&("-"===(e=n[i].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.substr(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){var n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(25),t.names=[],t.skips=[],t.formatters={}},function(e,t,n){"use strict";var r=1e3,i=60*r,s=60*i,o=24*s,a=365.25*o;function u(e,t,n){if(!(e<t))return e<1.5*t?Math.floor(e/t)+" "+n:Math.ceil(e/t)+" "+n+"s"}e.exports=function(e,t){t=t||{};var n=typeof e;if("string"===n&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!t)return;var n=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return n*a;case"days":case"day":case"d":return n*o;case"hours":case"hour":case"hrs":case"hr":case"h":return n*s;case"minutes":case"minute":case"mins":case"min":case"m":return n*i;case"seconds":case"second":case"secs":case"sec":case"s":return n*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}(e);if("number"===n&&!1===isNaN(e))return t.long?function(e){return u(e,o,"day")||u(e,s,"hour")||u(e,i,"minute")||u(e,r,"second")||e+" ms"}(e):function(e){if(e>=o)return Math.round(e/o)+"d";if(e>=s)return Math.round(e/s)+"h";if(e>=i)return Math.round(e/i)+"m";if(e>=r)return Math.round(e/r)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}])});