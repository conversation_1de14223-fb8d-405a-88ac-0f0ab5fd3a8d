console.info("SdkConnector", "load");

function SdkConnector() {
	console.info("SdkConnector", "ctor");
	this.isActive = false;
	this.url = null;
	this.authCallback = null;
	this.wsResolveCallback = null;
	this.loginCallback = null;
	this.exampleSocket = null;
	this.eventProcessors = [];
	this.connectedCallback = null;
	this.reconnectId = null;
}

SdkConnector.prototype.setUrl = function(_url) {
	this.url = _url;
	return this;
}

SdkConnector.prototype.setAuthCallback = function(_callback) {
	this.authCallback = _callback;
	return this;
}

SdkConnector.prototype.setWebsocketUrlResolveCallback = function(_callback) {
	this.wsResolveCallback = _callback;
	return this;
}

SdkConnector.prototype.setConnectedCallback = function(_callback) {
	this.connectedCallback = _callback;
	return this;
}

SdkConnector.prototype.setLoginCallback = function(_callback) {
	this.loginCallback = _callback;
	return this;
}

SdkConnector.prototype.registerEventProcessor = function(_eventProcessor) {
	this.eventProcessors.push(_eventProcessor);
}

SdkConnector.prototype.isStopped = function () {
	return !this.isActive;
}

function uuidv4() {
  return ([1e7]+-1e3+-4e3+-8e3+-1e11).replace(/[018]/g, c =>
    (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
  )
}

SdkConnector.prototype.addUser = function(_authServerUrl, _pubKey, _username, _password) {
	var dl = this;
	var xhr = new XMLHttpRequest();
	var url = _authServerUrl;
	xhr.open("POST", url, true);
	xhr.setRequestHeader("Content-type", "text/plain");
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			var loginSuccess = (xhr.status === 201 || xhr.status === 401);
			dl.authCallback(loginSuccess);
		}
	};
	var addUserInfo = {};
	addUserInfo.username = _username;
	addUserInfo.password = Sha256.hash(_password);
	addUserInfo.pubKey = _pubKey;
	var data = JSON.stringify(addUserInfo);
	
	xhr.send(data);	
	return this;
}

SdkConnector.prototype.auth = function(_authServerUrl, _username, _password) {
	var dl = this;
	var xhr = new XMLHttpRequest();
	var url = _authServerUrl;
	xhr.open("POST", url, true);
	xhr.setRequestHeader("Content-type", "text/plain");
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			var loginSuccess = (xhr.status === 200);
			var authToken = "";
			if (loginSuccess) {
				var json = JSON.parse(xhr.responseText);
				authToken = json.token;
				if (!authToken || authToken.length == 0) {
					loginSuccess = false;
				}
			}
			dl.authCallback(loginSuccess, authToken);
		}
	};
	var loginInfo = {};
	loginInfo.username = _username;
	loginInfo.password = Sha256.hash(_password);
	loginInfo.device_uuid = uuidv4();
	var data = JSON.stringify(loginInfo);
	xhr.send(data);	
	return this;
}

SdkConnector.prototype.resolveWsUrl = function(_httpUrl, _authToken) {
	var dl = this;
	var xhr = new XMLHttpRequest();
	//var url = "https://cors-anywhere.herokuapp.com/" + _httpUrl;
	var url = _httpUrl;
	//var url = "http://cors-proxy.htmldriven.com/?url=" + _httpUrl;
	xhr.open("POST", url, true);
	xhr.setRequestHeader("Content-type", "text/plain");
	xhr.setRequestHeader("Authorization", "bearer " + _authToken);
	xhr.onreadystatechange = function () {
		if (xhr.readyState === 4) {
			var resolveSuccess = (xhr.status === 200);
			var websocketUrl = "";
			if (resolveSuccess) {
				var json = JSON.parse(xhr.responseText);
				websocketUrl = json.websocketUrl;
				if (!websocketUrl || websocketUrl.length == 0) {
					resolveSuccess = false;
				}
			}
			dl.wsResolveCallback(resolveSuccess, websocketUrl);
		}
	};
	xhr.send();	
	return this;
}

SdkConnector.prototype.start = function(_connConfig) {
	console.info("SdkConnector", "Connecting to WebSocket server: " + _connConfig.url);
	clearTimeout(this.reconnectId);
	this.reconnectId = null;
    var dl = this;
    this.exampleSocket = new WebSocket(_connConfig.url);
	this.exampleSocket.binaryType = "arraybuffer";
    this.exampleSocket.onopen = function (event) {
		console.info("SdkConnector", "Connected to WebSocket server");
		dl.connectedCallback(true);
    };	
	this.exampleSocket.onerror = function (event) {
		console.info("SdkConnector", "Error connecting to WebSocket server");
		dl.connectedCallback(false);
	};
	this.exampleSocket.onclose = function (event) {
		console.info("SdkConnector", "WebSocket connection closed with code " + event.code);
		dl.connectedCallback(false);
		if (dl.isActive === true && (event.code == 1006 || event.code == 1005 || event.code == 1001)) {
			dl.reconnectId = setTimeout(dl.start.bind(dl, _connConfig), 1000);
		}
	};
    this.exampleSocket.onmessage = function (event) {
		console.debug("SdkConnector", "RX (" + event.data.byteLength + " bytes) " + event.data);
		var payloadAsStr;
		if(event.data instanceof ArrayBuffer) {
			payloadAsStr = new TextDecoder("utf-8").decode(new Uint8Array(event.data));
		}
		else {
			payloadAsStr = event.data;
		}
		var eventObj = JSON.parse(payloadAsStr);
		if (eventObj.functionObject.functionName == "onLoginResult") {
			var jsonApiVer = (eventObj.functionObject.hasOwnProperty('jsonApiVersion') ? eventObj.functionObject.jsonApiVersion : -1);
			dl.loginCallback(jsonApiVer);
		}
		else {
			dl.eventProcessors.forEach( function (eventProcessor)
			{
				eventProcessor.processEvent(eventObj);
			});		
		}
    }	
	this.isActive = true;
	return this;
}

SdkConnector.prototype.stop = function() {
	//Log.info("Downloader", "Stopping file download");
	this.isActive = false;
	return this;
}

SdkConnector.prototype.login = function(_authToken, _context) {
	var loginFunc = {};
	loginFunc.moduleId = "JsonApiServer";
	loginFunc.functionObject = {};
	loginFunc.functionObject.functionName = "login";
	loginFunc.functionObject.authToken = _authToken;
	loginFunc.functionObject.context = _context;
	this.send(JSON.stringify(loginFunc));
	return this;
}

SdkConnector.prototype.send = function(_payload) {
	this.exampleSocket.send(_payload);
	return this;
}
