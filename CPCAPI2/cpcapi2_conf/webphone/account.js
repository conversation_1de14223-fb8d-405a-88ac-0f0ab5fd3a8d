
function SipAccountManager() {
	this.callback = null;
	this.sdkConnector = null;
	this.createCallback = null;
}

SipAccountManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

SipAccountManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	//var setHandlerFunc = {};
	//setHandlerFunc.moduleId = "SipConversationJsonApi";
	//setHandlerFunc.functionObject = {};
	//setHandlerFunc.functionObject.functionName = "setHandler";
	//setHandlerFunc.functionObject.account = _account;
	//this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

SipAccountManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "SipAccountManagerJsonProxy") {
		if (_jsonEventObj.functionObject.functionName == "createResult") {
			if (this.createCallback) {
				var cb = this.createCallback;
				this.createCallback = null;
				cb(_jsonEventObj.functionObject.account);
			}
		}
		else {
			this.callback(_jsonEventObj.functionObject);
		}
	}
	return this;
}


SipAccountManager.prototype.makeEmptyTunnelConfig = function() {
	/*
	    JSON_DATA_TYPE(CPCAPI2::SipAccount::TunnelConfig, useTunnel, tunnelType, server, transportType, mediaTransportType, redundancyFactor, 
		doLoadBalancing, ignoreCertVerification, disableNagleAlgorithm, strettoTunnelURL, strettoTunnelToken, strettoTunnelSessionID, 
		strettoTunnelTestConnection, logStrettoTunnelTransportTraces)
	*/
	var ti = {};
	ti.useTunnel = false;
	return ti;
}

/*
    JSON_DATA_TYPE(CPCAPI2::SipAccount::SipAccountSettings, username, domain, password, displayName, auth_username, auth_realm, useRegistrar, 
	               outboundProxy, alwaysRouteViaOutboundProxy, registrationIntervalSeconds, minimumRegistrationIntervalSeconds, maximumRegistrationIntervalSeconds, 
				   useRport, sipTransportType, excludeEncryptedTransports, userAgent, udpKeepAliveTime, tcpKeepAliveTime, useOutbound, useGruu, 
				   otherNonEscapedCharsInUri, nameServers, additionalNameServers, sessionTimerMode, sessionTimeSeconds, stunServerSource, stunServer, 
				   ignoreCertVerification, additionalCertPeerNames, acceptedCertPublicKeys, requiredCertPublicKeys, sipQosSettings, useImsAuthHeader, 
				   minSipPort, maxSipPort, useMethodParamInReferTo, useInstanceId, ipVersion, sslVersion, reRegisterOnResponseTypes, enableRegeventDeregistration, 
				   XCAPRoot, tunnelConfig, capabilities, additionalFromParameters, sourceAddress, preferPAssertedIdentity, autoRetryOnTransportDisconnect, 
				   keepAliveMode, useRinstance, enableNat64Support, usePrivacyHeaderOnlyForAnonymous, transportHoldover, useOptionsPing, optionsPingInterval, 
				   userCertificatePEM, userPrivateKeyPEM, forceListenSocket)
*/
SipAccountManager.prototype.makeSipAccountSettings = function(_username, _domain, _password, _displayName, _outboundProxy, _sipTransportType) {
	var ci = {};
	ci.username = _username;
	ci.domain = _domain;
	ci.password = _password;
	ci.displayName = _displayName;
	ci.auth_username = "";
	ci.auth_realm = "";
	ci.useRegistrar = true;
	ci.outboundProxy = _outboundProxy;
	ci.alwaysRouteViaOutboundProxy = false;
	ci.registrationIntervalSeconds = 3600;
	ci.minimumRegistrationIntervalSeconds = 300;
	ci.maximumRegistrationIntervalSeconds = 3600;
	ci.useRport = true;
	ci.sipTransportType = _sipTransportType;
	ci.excludeEncryptedTransports = false;
	ci.userAgent = "cpcapi2web";
	ci.udpKeepAliveTime = 30;
	ci.tcpKeepAliveTime = 120;
	ci.useOutbound = false;
	ci.useGruu = false;
	ci.otherNonEscapedCharsInUri = "";
	ci.nameServers = [];
	ci.additionalNameServers = [];
	ci.sessionTimerMode = 1;
	ci.sessionTimeSeconds = 0;
	ci.stunServerSource = 0;
	ci.stunServer = "";
	ci.ignoreCertVerification = false;
	ci.additionalCertPeerNames = [];
	ci.acceptedCertPublicKeys = [];
	ci.requiredCertPublicKeys = [];
	ci.sipQosSettings = 40;
	ci.useImsAuthHeader = false;
	ci.minSipPort = 0;
	ci.maxSipPort = 0;
	ci.useMethodParamInReferTo = false;
	ci.useInstanceId = false;
	ci.ipVersion = 0; // IPv4
	ci.sslVersion = 3; // TLS_V1_0
	ci.reRegisterOnResponseTypes = [];
	ci.enableRegeventDeregistration = false;
	ci.XCAPRoot = "";
	ci.tunnelConfig = this.makeEmptyTunnelConfig();
	ci.capabilities = [];
	ci.additionalFromParameters = [];
	ci.sourceAddress = "";
	ci.preferPAssertedIdentity = false;
	ci.autoRetryOnTransportDisconnect = false;
	ci.keepAliveMode = 0;
	ci.useRinstance = true;
	ci.enableNat64Support = false;
	ci.usePrivacyHeaderOnlyForAnonymous = false;
	ci.transportHoldover = 0;
	ci.useOptionsPing = false;
	ci.optionsPingInterval = 0;
	ci.userCertificatePEM = "";
	ci.userPrivateKeyPEM = "";
	ci.forceListenSocket = false;
	return ci;
}

SipAccountManager.prototype.create = function(_sipAcctSettings, _createdCb) {
	this.createCallback = _createdCb;
	var startFunc = {};
	startFunc.moduleId = "SipAccountJsonApi";
	startFunc.functionObject = {};
	startFunc.functionObject.functionName = "create";
	startFunc.functionObject.accountSettings = _sipAcctSettings;
	this.sdkConnector.send(JSON.stringify(startFunc));
	return this;
}

SipAccountManager.prototype.enable = function(_acct) {
	var fn = {};
	fn.moduleId = "SipAccountJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "enable";
	fn.functionObject.account = _acct;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

SipAccountManager.prototype.disable = function(_acct) {
	var fn = {};
	fn.moduleId = "SipAccountJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "disable";
	fn.functionObject.account = _acct;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}
