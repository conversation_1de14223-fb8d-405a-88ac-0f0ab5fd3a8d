function getRandomInt(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function PeerConnectionManager() {
	this.callback = null;
	this.sdkConnector = null;
	this.pcHandle = getRandomInt(1, 65535);
}

PeerConnectionManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

PeerConnectionManager.prototype.setCallback = function(_pc, _callback) {
	this.callback = _callback;
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "PeerConnectionJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	setHandlerFunc.functionObject.pc = _pc;
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

PeerConnectionManager.prototype.createPeerConnection = function() {
	var createFunc = {};
	createFunc.moduleId = "PeerConnectionJsonApi";
	createFunc.functionObject = {};
	createFunc.functionObject.functionName = "createPeerConnection";
	createFunc.functionObject.pc = this.pcHandle;
	this.pcHandle++;
	this.sdkConnector.send(JSON.stringify(createFunc));
	return createFunc.functionObject.pc;
}

PeerConnectionManager.prototype.makePeerConnectionSettings = function(_natTraversalMode, _natTraversalServerType, _natTraversalServerHostname, _natTraversalServerUsername, _natTraversalServerPassword, _sessionName, _certAor, _secureMediaMode, _secureMediaRequired, _localInterface, _mixId, _vidCaptureDeviceId, _adaptVideoCodecOnRemotePacketLoss) {
	var settings = {};
	settings.natTraversalMode = _natTraversalMode;
	settings.natTraversalServerType = _natTraversalServerType;
	settings.natTraversalServerHostname = _natTraversalServerHostname;
	settings.natTraversalServerUsername = _natTraversalServerUsername;
	settings.natTraversalServerPassword = _natTraversalServerPassword;
	settings.sessionName = _sessionName;
	settings.certAor = _certAor;
	settings.secureMediaMode = _secureMediaMode;
	settings.secureMediaRequired = _secureMediaRequired;
	settings.localInterface = _localInterface;
	settings.mixId = _mixId;
	settings.videoCaptureDeviceId = _vidCaptureDeviceId;
   settings.adaptVideoCodecOnRemotePacketLoss = _adaptVideoCodecOnRemotePacketLoss;
	return settings;
}

PeerConnectionManager.prototype.setDefaultSettings = function(_pc, _settings) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setDefaultSettings";
	fn.functionObject.pc = _pc;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.makeAudioCodec = function(_plname) {
	var ac = {};
	ac.plname = _plname;
	return ac;
}

PeerConnectionManager.prototype.makeVideoCodec = function(_plname) {
	var vc = {};
	vc.plName = _plname;
	return vc;
}

PeerConnectionManager.prototype.makeMediaInfo = function(_mediaType, _mediaDirection, _mediaStream, _audioCodec, _videoCodec) {
	var mi = {};
	mi.mediaType = _mediaType;
	mi.mediaDirection = _mediaDirection;
	mi.mediaStream = _mediaStream;
	mi.audioCodec = _audioCodec;
	mi.videoCodec = _videoCodec;
	return mi;
}

PeerConnectionManager.prototype.configureMedia = function(_pc, _mediaStream, _mediaInfo) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "configureMedia";
	fn.functionObject.pc = _pc;
	fn.functionObject.mediaStream = _mediaStream;
	fn.functionObject.mediaDescriptor = _mediaInfo;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.createOffer = function(_pc) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "createOffer";
	fn.functionObject.pc = _pc;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.setLocalDescription = function(_pc, _sdp, _sdpType) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setLocalDescription";
	fn.functionObject.pc = _pc;
	fn.functionObject.sdp = _sdp;
	fn.functionObject.sdpType = _sdpType;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.setRemoteDescription = function(_pc, _sdp, _sdpType) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setRemoteDescription";
	fn.functionObject.pc = _pc;
	fn.functionObject.sdp = _sdp;
	fn.functionObject.sdpType = _sdpType;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.startWebVideoServerForMediaStream = function(_pc, _mediaStream) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "startWebVideoServerForMediaStream";
	fn.functionObject.pc = _pc;
	fn.functionObject.ms = _mediaStream;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.close = function(_pc) {
	var fn = {};
	fn.moduleId = "PeerConnectionJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "close";
	fn.functionObject.pc = _pc;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

PeerConnectionManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "PeerConnectionManagerJsonProxy") {
		if (this.callback) {
			this.callback(_jsonEventObj.functionObject);
		}
	}
	return this;
}
