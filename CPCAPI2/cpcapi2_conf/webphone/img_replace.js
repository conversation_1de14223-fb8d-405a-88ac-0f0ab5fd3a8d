function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
	//return ((l.protocol === "https:") ? "wss://" : "ws://") + "***********" + ":" + port;
}

window.requestAnimFrame = (function(){
  return  window.requestAnimationFrame       ||
          window.webkitRequestAnimationFrame ||
          window.mozRequestAnimationFrame    ||
          function( callback ){
            window.setTimeout(callback, 1000 / 60);
          };
})();

var canvas;
var context;
var imageData;
var scratch;
var isCompleteFrame = false;
var drawRequested = false;
var doCanvasReset = false;
var videoWidth = 0;
var videoHeight = 0;

function drawLoop() {
  requestAnimFrame(drawLoop);
  if (!drawRequested) return;
  myDraw2();  
  drawRequested = false;
}
drawLoop();

function myDraw2()
{
	context.putImageData(imageData, 0, 0);
}

function handleImageData(event) { 
	var buffer = event.data;
	//console.log("compressed length: " + buffer.byteLength);
	if (buffer.byteLength == 12) {
		var hdrbuff32 = new Uint32Array(buffer);
		if (hdrbuff32[0] == 0x4001)
		{
			videoWidth = hdrbuff32[1];
			videoHeight = hdrbuff32[2];
			isCompleteFrame = true;		
			if (videoWidth != canvas.width || videoHeight != canvas.height) {
				console.info("img_replace", "new complete frame with dimensions: " + videoWidth + "x" + videoHeight);
				canvas.width = videoWidth;
				canvas.height = videoHeight;
				imageData = context.getImageData(0, 0, videoWidth, videoHeight);
			}
		}
	}
	else {
		if (isCompleteFrame) {
			SnappyJS.uncompress(buffer, imageData.data);
			drawRequested = true;
			isCompleteFrame = false;
		} else {
			//console.log("appying diff of length " + uncompressed8.length);
			var uncompressedLen = SnappyJS.uncompress(buffer, scratch) / 4;
			var buffer32 = new Uint32Array(scratch.buffer);
			var imageData32 = new Uint32Array(imageData.data.buffer);
			
			for (var i=0; i<uncompressedLen;) {
				var deltaPos = buffer32[i];
				i = i + 1;
				var deltaSize = buffer32[i];
				i = i + 1;
				//console.log("processing delta at pos " + deltaPos + " of length " + deltaSize + " bytes");
				for (var j=deltaPos; j<(deltaPos+deltaSize); j++) {
					imageData32[j] = buffer32[i++]
				}
			}
		
			drawRequested = true;
		}
	}
}

var videoWs = null;
var fastDeltaFrameRecvCnt = 0;
var fastDeltaVideoStreamId = -1;

function startVideoWebsocket(_cbOnOpen) {
	if (videoWs == null) {
		videoWs = new WebSocket("ws://127.0.0.1:2115");
		videoWs.binaryType = "arraybuffer";
		videoWs.onopen = function (event) {
			console.log("connected to video websocket");
			fastDeltaFrameRecvCnt = 0;
			_cbOnOpen();
		};	
		videoWs.onmessage = function (event) {
			//console.log("received a message");
			handleImageData(event);
			
			fastDeltaFrameRecvCnt++;
			if (fastDeltaFrameRecvCnt % 10 == 0) {
				var framercvdobj = {};
				framercvdobj.action = "frames_recvd";
				framercvdobj.payload = {};
				framercvdobj.payload.recvCnt = fastDeltaFrameRecvCnt;
				framercvdobj.payload.videoStreamId = fastDeltaVideoStreamId;
				videoWs.send(JSON.stringify(framercvdobj));
			}
			
		}	
	}
	else {
		_cbOnOpen();
	}
}

function bindWebsocketTochannel(_videoStreamId) {
	fastDeltaVideoStreamId = _videoStreamId;
   // format is:
   // {"action":"stream_bind","payload":{"videoStreamId":1}}
	var bindObj = {};
	bindObj.action = "stream_bind";
	bindObj.payload = {};
	bindObj.payload.videoStreamId = _videoStreamId;
	videoWs.send(JSON.stringify(bindObj));
}



