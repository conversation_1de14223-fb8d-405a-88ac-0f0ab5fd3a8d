
/*
    
*/
[v-cloak] {
    display: none;
  }
/*
    
*/
body {
    font-family: sans-serif;
    background: #FFFFFF;
}

p {
    font-family: sans-serif;
    font-size: 1.1em;
    font-weight: 300;
    line-height: 1.7em;
    color: #706F6E;
}

a, a:hover, a:focus {
    color: inherit;
    text-decoration: none;
    transition: all 0.3s;
}

.login-container {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.conf-join-create-form {
    max-width: 700px;
    padding: 6px;
}

.participant-container {

    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.participant-controls {
    padding: 6px;
}

.conference-list th {
    padding: 5px;
}

.conference-list td {
    padding: 5px;
}

.conference-list-action-btn {
	text-align: right;
}

.btn-participant {
    width: 32px;
    height: 32px;
    padding: 4px;
    margin: 2px;
    background: transparent;
}

.btn-participant:hover {
    background: #CCCCCC;
}

.conference-controls {
    background: #F5F4F1;
    padding: 6px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 6px;
}

.add-participant-controls {
    background: #F5F4F1;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-conf-toggle {
    height: 36px;
    padding: 4px;
    margin: 2px;
    background: transparent;
}

.btn-conf-toggle:hover {
    background: #CCCCCC;
}

.btn-conf {
    width: 36px;
    height: 36px;
    padding: 4px;
    margin: 2px;
    background: transparent;
}

.btn-conf:hover {
    background: #CCCCCC;
}

.img-conf-w {
    width: 100%;
}

.img-conf-h {
    height: 100%;
}

.btn-hangup {
    float: right !important;
    background: red;
    width: 58px;
    padding: 10px;
}

.videoContainer {        
    position: relative;
    width: 100%;
    padding-bottom: 56.25%;   
}
canvas {
    position: absolute;
    width: 100%;
	max-width: 2560px;
}

#sidebarCollapse {
    margin-left: auto;
    margin-right: auto;
    margin-top: 6px;
}

.avatar-cropper {    
    width: 144px;    
    height: 144px;    
    position: relative;    
    overflow: hidden;    
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    margin-left: auto;
    margin-right: auto;
	border: 2px solid #e3e3e3;
	background: #fff;
}

.avatar-cropper-floor {
    width: 144px;    
    height: 144px;    
    position: relative;    
    overflow: hidden;    
    -moz-border-radius: 50%;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    margin-left: auto;
    margin-right: auto;
	border: 2px solid #00ffbf;
	background: #fff;
}

.avatar {    
    display: inline;    
    margin: 0 auto;    
    height: 100%;    
    min-width: 100%;
}

.displayName {
    text-overflow: clip;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 220px;
}

.confLabel {
    text-overflow: clip;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 200px;
}

.vertical-align {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
  }

.btn-kick-participant {
    color: #888888;
    background: transparent;
    font-size: 22px;
    font-weight: 100;
  }
.btn-kick-participant:hover {
    color: rgb(252, 104, 48);
    font-size: 22px;
    font-weight: 100;
  }

/*
--- bootstrap ---
*/

.row {
    margin: 10px 0px;
}

.navbar {
    padding: 15px 10px;
    background: #F5F4F1;
    border: none;
    border-radius: 0;
    margin-bottom: 10px;
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-btn {
    box-shadow: none;
    outline: none !important;
    border: none;
}

.panel-body {
 padding: 30px 50px;  
}

.btn-primary {
    background-color: rgb(252, 104, 48);
}

.btn-primary:hover {
    background-color: rgba(255, 128, 64, 1.0);
}

.btn-primary:focus,
.btn-primary.focus {
  background-color: rgb(252, 104, 48);
}

.btn-primary:active,
.btn-primary:active:focus
{
  background-color: rgb(252, 104, 48);
}

.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: 0px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}

.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0);
}

/* ---------------------------------------------------
    SIDEBAR STYLE
----------------------------------------------------- */
#sidebar {
    width: 250px;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    background: #F5F4F1;
    color: #706F6E;
    transition: all 0.3s;

    border-right: 0px solid #333;
    box-shadow: 3px 0px 0px rgba(0, 0, 0, 0.1);
}

#sidebar.active {
    margin-left: -250px;
}

#sidebar .sidebar-header {
    padding: 10px 22px;
    background: #D8D8D8;
    color: #333;
}

#sidebar p {
    color: #706F6E;
    padding: 10px;
}

#sidebar td {
    color: #706F6E;
    padding: 10px;
}


#sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #47748b;
}

#sidebar ul p {
    color: #706F6E;
    padding: 10px;
}

#sidebar ul li a {
    padding: 10px;
    font-size: 1.1em;
    display: block;
}
#sidebar ul li a:hover {
    color: #333333;
    background: #fff;
}

#sidebar ul li.active > a, a[aria-expanded="true"] {
    color: #706F6E;
    background: #222222;
}


a[data-toggle="collapse"] {
    position: relative;
}

a[aria-expanded="false"]::before, a[aria-expanded="true"]::before {
    content: '\e259';
    display: block;
    position: absolute;
    right: 20px;
    font-family: 'Glyphicons Halflings';
    font-size: 0.6em;
}
a[aria-expanded="true"]::before {
    content: '\e260';
}


ul ul a {
    font-size: 0.9em !important;
    padding-left: 30px !important;
    background: #222222;
}

ul.CTAs {
    padding: 20px;
}

ul.CTAs a {
    text-align: center;
    font-size: 0.9em !important;
    display: block;
    border-radius: 5px;
    margin-bottom: 5px;
}
a.download {
    background: #333;
    color: #FFF;
}
a.download:hover {
    background: #706F6E !important;
    color: #FFF !important;
}


/* ---------------------------------------------------
    CONTENT STYLE
----------------------------------------------------- */
#content {
    width: calc(100% - 250px);
    padding: 20px;
    min-height: 100vh;
    transition: all 0.3s;
    position: absolute;
    top: 0;
    right: 0;
}
#content.active {
    width: 100%;
}


/* ---------------------------------------------------
    MEDIAQUERIES
----------------------------------------------------- */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        width: 100%;
    }
    #content.active {
        width: calc(100% - 250px);
    }
    #sidebarCollapse span {
        display: none;
    }
}

/*!
 * animate.css -http://daneden.me/animate
 * Version - 3.5.2
 * Licensed under the MIT license - http://opensource.org/licenses/MIT
 *
 * Copyright (c) 2017 Daniel Eden
 */

.animated {
  animation-duration: 1s;
  animation-fill-mode: both;
}

.animated.infinite {
  animation-iteration-count: infinite;
}

.animated.hinge {
  animation-duration: 2s;
}

.animated.flipOutX,
.animated.flipOutY,
.animated.bounceIn,
.animated.bounceOut {
  animation-duration: .75s;
}

@keyframes shake {
  from, to {
    transform: translate3d(0, 0, 0);
  }

  10%, 30%, 50%, 70%, 90% {
    transform: translate3d(-10px, 0, 0);
  }

  20%, 40%, 60%, 80% {
    transform: translate3d(10px, 0, 0);
  }
}

.shake {
  animation-name: shake;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

.fadeInLeft {
  animation-name: fadeInLeft;
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }

  to {
    opacity: 1;
    transform: none;
  }
}

.fadeInRight {
  animation-name: fadeInRight;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.fadeOut {
  animation-name: fadeOut;
}

@keyframes fadeOutLeft {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(-100%, 0, 0);
  }
}

.fadeOutLeft {
  animation-name: fadeOutLeft;
}

@keyframes fadeOutRight {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: translate3d(100%, 0, 0);
  }
}

.fadeOutRight {
  animation-name: fadeOutRight;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale3d(.3, .3, .3);
  }

  50% {
    opacity: 1;
  }
}

.zoomIn {
  animation-name: zoomIn;
}
