
function SipConversationManager() {
	this.conversationState = null;
	this.callback = null;
	this.sdkConnector = null;
	this.createConversationHandler = null;
}

SipConversationManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

SipConversationManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	/*
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "SipConversationJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	setHandlerFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	*/
	return this;
}

/*
    JSON_DATA_TYPE(CPCAPI2::SipConversationSettings, sessionName, natTraversalMode, natTraversalServerSource, natTraversalServer, natTraversalServerType, 
	holdMode, prackMode, minRtpPort, maxRtpPort, minRtpPortAudio, maxRtpPortAudio, minRtpPortVideo, maxRtpPortVideo, turnUsername, turnPassword, 
	includePPreferredIdentity, includePAssertedIdentity, includeAttribsForStaticPLs)
*/
SipConversationManager.prototype.makeSipConversationSettings = function() {
	var scs = {};
	scs.sessionName = "cpcapi2webphone";
	scs.natTraversalMode = 2; // NatTraversalMode_STUN
	scs.natTraversalServerSource = 2; // NatTraversalServerSource_Custom
	scs.natTraversalServer = "stun.counterpath.com";
	scs.natTraversalServerType = 1; // NatTraversalServerType_StunOnly
	scs.holdMode = 1; // HoldMode_RFC3264
	scs.prackMode = 0; // PrackMode_Disabled
	scs.minRtpPort = 0;
	scs.maxRtpPort = 0;
	scs.minRtpPortAudio = 0;
	scs.maxRtpPortAudio = 0;
	scs.minRtpPortVideo = 0;
	scs.maxRtpPortVideo = 0;
	scs.turnUsername = "";
	scs.turnPassword = "";
	scs.includePPreferredIdentity = false;
	scs.includePAssertedIdentity = false;
	scs.includeAttribsForStaticPLs = false;
	return scs;
}

SipConversationManager.prototype.setDefaultSettings = function(_account, _settings) {
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "SipConversationJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	setHandlerFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));

	var fn = {};
	fn.moduleId = "SipConversationJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setDefaultSettings";
	fn.functionObject.account = _account;
	fn.functionObject.settings = _settings;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

SipConversationManager.prototype.conversationState = function() {
	return this.conversationState;
}

SipConversationManager.prototype.createConversation = function(_account, _createdCb) {
	this.createConversationHandler = _createdCb;
	var createFunc = {};
	createFunc.moduleId = "SipConversationJsonApi";
	createFunc.functionObject = {};
	createFunc.functionObject.functionName = "createConversation";
	createFunc.functionObject.account = _account;
	this.sdkConnector.send(JSON.stringify(createFunc));
	return this;
}

SipConversationManager.prototype.addParticipant = function(_conversation, _destAddr) {
	var addPartFunc = {};
	addPartFunc.moduleId = "SipConversationJsonApi";
	addPartFunc.functionObject = {};
	addPartFunc.functionObject.functionName = "addParticipant";
	addPartFunc.functionObject.conversation = _conversation;
	addPartFunc.functionObject.participantAddress = _destAddr;
	this.sdkConnector.send(JSON.stringify(addPartFunc));
	return this;
}

SipConversationManager.prototype.start = function(_conversation) {
	var startFunc = {};
	startFunc.moduleId = "SipConversationJsonApi";
	startFunc.functionObject = {};
	startFunc.functionObject.functionName = "start";
	startFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(startFunc));
	return this;
}

SipConversationManager.prototype.end = function(_conversation) {
	var endFunc = {};
	endFunc.moduleId = "SipConversationJsonApi";
	endFunc.functionObject = {};
	endFunc.functionObject.functionName = "end";
	endFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(endFunc));
	return this;
}

SipConversationManager.prototype.requestStateAllConversations = function() {
	var requestStateFunc = {};
	requestStateFunc.moduleId = "SipConversationJsonApi";
	requestStateFunc.functionObject = {};
	requestStateFunc.functionObject.functionName = "requestStateAllConversations";
	this.sdkConnector.send(JSON.stringify(requestStateFunc));
	return this;
}

SipConversationManager.prototype.configureMedia = function(_conversation, _mediaType, _mediaDirection, _mediaCryptoSuite, _mediaEncryptionMode, _secureMediaRequired, _conferenceMixId) {
	var configMediaFunc = {};
	configMediaFunc.moduleId = "SipConversationJsonApi";
    configMediaFunc.functionObject = {};
	configMediaFunc.functionObject.functionName = "configureMedia";
	configMediaFunc.functionObject.conversation = _conversation;
	configMediaFunc.functionObject.mediaDescriptor = {};
	
	/*
	    JSON_DATA_TYPE(CPCAPI2::SipConversation::MediaInfo, mediaType, mediaDirection, mediaCrypto, mediaEncryptionOptions, audioCodec, videoCodec, conferenceMixContribution, isLocallyDisabled, conferenceMixId, mediaStreamId, videoCaptureDeviceId)
	*/
	configMediaFunc.functionObject.mediaDescriptor.mediaType = _mediaType;
	configMediaFunc.functionObject.mediaDescriptor.mediaDirection = _mediaDirection;
	configMediaFunc.functionObject.mediaDescriptor.mediaCrypto = _mediaCryptoSuite;
	configMediaFunc.functionObject.mediaDescriptor.mediaEncryptionOptions = {};
	/*
	    JSON_DATA_TYPE(CPCAPI2::SipConversation::MediaEncryptionOptions, mediaEncryptionMode, secureMediaRequired, mediaCryptoSuites)
	*/
	configMediaFunc.functionObject.mediaDescriptor.mediaEncryptionOptions.mediaEncryptionMode = _mediaEncryptionMode;
	configMediaFunc.functionObject.mediaDescriptor.mediaEncryptionOptions.secureMediaRequired = _secureMediaRequired;
	
	configMediaFunc.functionObject.mediaDescriptor.conferenceMixId = _conferenceMixId;
	this.sdkConnector.send(JSON.stringify(configMediaFunc));
    return this;
}

SipConversationManager.prototype.sendMediaChangeRequest = function(_conversation) {
	var sendMediaChangeRequestFunc = {};
	sendMediaChangeRequestFunc.moduleId = "SipConversationJsonApi";
	sendMediaChangeRequestFunc.functionObject = {};
	sendMediaChangeRequestFunc.functionObject.functionName = "sendMediaChangeRequest";
	sendMediaChangeRequestFunc.functionObject.conversation = _conversation;
	this.sdkConnector.send(JSON.stringify(sendMediaChangeRequestFunc));
	return this;
}

SipConversationManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "SipConversationManagerJsonProxy") {
		if (_jsonEventObj.functionObject.functionName == "createConversationResult") {
			if (this.createConversationHandler) {
				this.createConversationHandler(_jsonEventObj.functionObject.conversation);
			}
			this.createConversationHandler = null;
		}
		else {
			if (_jsonEventObj.functionObject.functionName == "onConversationState") {
				this.conversationState = _jsonEventObj.functionObject.conversationStateArray;
			}
			this.callback(_jsonEventObj.functionObject);
		}
	}
	return this;
}
