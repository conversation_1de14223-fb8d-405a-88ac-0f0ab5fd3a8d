var RemoteConferenceConnectorManager_nextConnHandle = 1;

function RemoteConferenceConnectorManager() {
	this.callback = null;
	this.createConferenceSessionCallback = null;
	this.sdkConnector = new SdkConnector();
	this.confBridgeMgr = new ConferenceBridgeManager();
	this.confBridgeMgr.setCallback(this.onConferenceBridgeManagerEvent.bind(this));
	this.sdkConnector.registerEventProcessor(this.confBridgeMgr);
	this.confBridgeMgr.setSdkConnector(this.sdkConnector);
	this.connHandle = -1;
	this.connSettings = null;
	this.authToken = null;
	this.confConnConferences = [];
	this.mapSessionToConference = new Map();
}

RemoteConferenceConnectorManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

RemoteConferenceConnectorManager.prototype.createConferenceConnector = function(_callback) {
	this.connHandle = RemoteConferenceConnectorManager_nextConnHandle++;
	if (_callback) {
		var cb = _callback;
		cb(this.connHandle);
	}
	return this;
}

RemoteConferenceConnectorManager.prototype.makeConferenceConnectorSettings = function(_authServerApiKey, _authServerUrl, _joinUrl, _orchestrationServerUrl, _password, _regionCode, _username) {
	var settings = {};
	settings.authServerApiKey = _authServerApiKey;
	settings.authServerUrl = _authServerUrl;
	settings.joinUrl = _joinUrl;
	settings.orchestrationServerUrl = _orchestrationServerUrl;
	settings.password = _password;
	settings.regionCode = _regionCode;
	settings.username = _username;
	return settings;
}

RemoteConferenceConnectorManager.prototype.setConnectionSettings = function(_conn, _settings) {
	this.connSettings = _settings;
	return this;
}

RemoteConferenceConnectorManager.prototype.connectToConferenceService = function(_conn) {
	this.sdkConnector.setAuthCallback(this.onAddUser.bind(this));
	var evtObj = {};
	evtObj.functionName = "onServiceConnectionStatusChanged";
	evtObj.connectionStatus = 2; // ServiceConnectionStatus_Connecting
	this.callback(evtObj);
	var addUserUrl = this.connSettings.authServerUrl + "/addUser";
	this.sdkConnector.addUser(addUserUrl, this.connSettings.authServerApiKey, this.connSettings.username, this.connSettings.password);
	return this;
}

RemoteConferenceConnectorManager.prototype.onAddUser = function(_addUserSuccess) {
	if (_addUserSuccess === true) {
		this.sdkConnector.setAuthCallback(this.onAuth.bind(this));
		var authUrl = this.connSettings.authServerUrl + "/login_v1";
		this.sdkConnector.auth(authUrl, this.connSettings.username, this.connSettings.password);
	}
	else {
		var evtObj = {};
		evtObj.functionName = "onServiceConnectionStatusChanged";
		evtObj.connectionStatus = 6; // ServiceConnectionStatus_AuthFailure
		this.callback(evtObj);
	}
	return this;
}

RemoteConferenceConnectorManager.prototype.onAuth = function(_authSuccess, _authToken) {
	if (_authSuccess === true) {
		this.authToken = _authToken;
		this.sdkConnector.setWebsocketUrlResolveCallback(this.onWsUrlResolve.bind(this));
		this.sdkConnector.resolveWsUrl(this.connSettings.joinUrl, this.authToken);
	}
	else {
		var evtObj = {};
		evtObj.functionName = "onServiceConnectionStatusChanged";
		evtObj.connectionStatus = 6; // ServiceConnectionStatus_AuthFailure
		this.callback(evtObj);
	}
	return this;
}

RemoteConferenceConnectorManager.prototype.onWsUrlResolve = function(_resolveSuccess, _wsUrl) {
	if (_resolveSuccess === true) {
		var connConfig = {};
		connConfig.url = _wsUrl;
		this.sdkConnector.setConnectedCallback(this.onSdkConnConnected.bind(this));
		this.sdkConnector.start(connConfig);
	}
	return this;
}

RemoteConferenceConnectorManager.prototype.onSdkConnConnected = function(_connectSuccess) {
	if (_connectSuccess === true) {
		var evtObj = {};
		evtObj.functionName = "onServiceConnectionStatusChanged";
		evtObj.connectionStatus = 3; // ServiceConnectionStatus_Authenticating
		this.callback(evtObj);
		
		this.sdkConnector.setLoginCallback(this.onSdkConnLogin.bind(this));
		this.sdkConnector.login(this.authToken, "RemoteConferenceConnectorManager");
	}
	return this;
}

RemoteConferenceConnectorManager.prototype.onSdkConnLogin = function(_jsonApiVersion) {
	var evtObj = {};
	evtObj.functionName = "onServiceConnectionStatusChanged";
	evtObj.connectionStatus = 4; // ServiceConnectionStatus_Connected
	evtObj.authToken = this.authToken;
	this.callback(evtObj);
	return this;
}

RemoteConferenceConnectorManager.prototype.disconnectFromConferenceService = function(_conn) {
	return this;
}

RemoteConferenceConnectorManager.prototype.queryConferenceList = function(_conn) {
	var thisRef = this;
	this.confBridgeMgr.queryConferenceList(
	    function(resultArgs) {
			var evtObj = {};
			evtObj.functionName = "onConferenceListUpdated";
			evtObj.conferenceList = [];
			
			resultArgs.conferences.forEach(function(confDetailsRes) {
				//console.info("index", "conference: " + confDetailsRes.conferenceInfo.label);
				var cloudConfInfo = {};
				cloudConfInfo.conference = confDetailsRes.conferenceInfo.conference;
				cloudConfInfo.conferenceType = 0; // not used
				cloudConfInfo.displayName = confDetailsRes.conferenceInfo.label;
				cloudConfInfo.joinUrl = ""; // not used
				cloudConfInfo.streamId = confDetailsRes.conferenceInfo.streamId;
				cloudConfInfo.numParticipants = confDetailsRes.conferenceInfo.numParticipants;
				evtObj.conferenceList.push(cloudConfInfo);
			});
			
			thisRef.callback(evtObj);
			
		});
	return this;
}

RemoteConferenceConnectorManager.prototype.makeCloudConferenceSettings = function(_conferenceDescription, _conferenceId, _conferenceType) {
	var settings = {};
	settings.conferenceDescription = _conferenceDescription;
	settings.conferenceId = _conferenceId;
	settings.conferenceType = _conferenceType;
	return settings;
}

RemoteConferenceConnectorManager.prototype.createNewConference = function(_conn, _settings) {
	const CloudConferenceType_AudioVideo_MCU = 1;
	const CloudConferenceType_AudioVideo_SFU = 2;
	const CloudConferenceType_Screenshare = 0;
	
	var tags = [];
	if (_settings.conferenceType === CloudConferenceType_AudioVideo_MCU || _settings.conferenceType === CloudConferenceType_AudioVideo_SFU)
	{
		tags.push(0x1000);
	}
	else if (_settings.conferenceType === CloudConferenceType_Screenshare)
	{
		tags.push(0x2000);
	}
	
	var confBridgeSettings = this.confBridgeMgr.makeConferenceSettings(
	    (_settings.conferenceType === CloudConferenceType_AudioVideo_MCU ? ConferenceMixMode_MCU : ConferenceMixMode_SFU), 
		false, 
		(_settings.conferenceType === CloudConferenceType_AudioVideo_MCU || _settings.conferenceType === CloudConferenceType_AudioVideo_SFU),
		_settings.conferenceDescription,
		tags);
	var confBridgeHdl = this.confBridgeMgr.createConference(confBridgeSettings);
	
	var webPartHdl = this.confBridgeMgr.createWebParticipant(confBridgeHdl);
	var webPartIdentity = this.confBridgeMgr.makeWebParticipantIdentity("web participant", true /*owner*/);
	this.confBridgeMgr.setWebParticipantIdentity(webPartHdl, webPartIdentity);
	
	var evtObj = {};
	evtObj.functionName = "onConferenceCreated";
	evtObj.conference = confBridgeHdl;
	this.callback(evtObj);
	
	return this;
}

RemoteConferenceConnectorManager.prototype.queryParticipantList = function(_conference) {
	this.confBridgeMgr.queryParticipantList(_conference);
	return this;
}

RemoteConferenceConnectorManager.prototype.createConferenceSession = function(_conference, _callback) {
	this.createConferenceSessionCallback = _callback;
	var webPartHdl = this.confBridgeMgr.createWebParticipant(_conference);
	this.mapSessionToConference.set(webPartHdl, _conference);
	var webPartIdentity = this.confBridgeMgr.makeWebParticipantIdentity("DASH web participant", false /*owner*/);
	this.confBridgeMgr.setWebParticipantIdentity(webPartHdl, webPartIdentity);
    
	var confMgr = this.confBridgeMgr;
	this.confConnConferences = [];
	var confConfs = this.confConnConferences;
	confMgr.queryConferenceList(function (confListArgs) {
		confListArgs.conferences.forEach(function (confItem) {
			var confConnConfItem = {};
			confConnConfItem.conference = confItem.conference;
			confConnConfItem.conferenceType = 0; // CloudConferenceType_Screenshare
			confConnConfItem.displayName = confItem.conferenceInfo.label;
			confConnConfItem.streamId = confItem.conferenceInfo.streamId;
			confConnConfItem.joinUrl = confItem.conferenceJoinUrl;
			confConfs.push(confConnConfItem);
		});
		_callback(webPartHdl);
	});
	
	return this;
}

RemoteConferenceConnectorManager.prototype.makeCloudConferenceSessionSettings = function(_role) {
	var settings = {};
	settings.role = _role;
	return settings;
}

RemoteConferenceConnectorManager.prototype.setSessionSettings = function(_session, _settings) {
	return this;
}

RemoteConferenceConnectorManager.prototype.makeCloudConferenceSessionMediaSettings = function(_audioDirection, _videoDirection) {
	var settings = {};
	settings.audioDirection = _audioDirection;
	settings.videoDirection = _videoDirection;
	return settings;
}

RemoteConferenceConnectorManager.prototype.setSessionMediaSettings = function(_session, _settings) {
	return this;
}

RemoteConferenceConnectorManager.prototype.startSession = function(_session) {
	var confHdl = this.mapSessionToConference.get(_session);
	var confConnConfSessionEvt = {};
	confConnConfSessionEvt.functionName = "onConferenceSessionStatusChanged";
	confConnConfSessionEvt.conference = confHdl;
	confConnConfSessionEvt.sessionStatus = 1; // SessionStatus_Connecting
	confConnConfSessionEvt.permissions = {};
	confConnConfSessionEvt.video = {};
	confConnConfSessionEvt.video.mediaStreamId = -1;
	confConnConfSessionEvt.screenshare = {};
	confConnConfSessionEvt.screenshare.mediaStreamId = -1;
	this.callback(confConnConfSessionEvt);
	
	confConnConfSessionEvt.sessionStatus = 2; // SessionStatus_Connected
	this.confConnConferences.forEach(function (confItem) {
		if (confItem.conference === confHdl) {
			if (confItem.conferenceType === 0) {  // CloudConferenceType_Screenshare
				confConnConfSessionEvt.screenshare.mediaStreamId = confItem.streamId;
			}
		}
	});
	this.callback(confConnConfSessionEvt);

	return this;
}

RemoteConferenceConnectorManager.prototype.endSession = function(_session) {
	var confHdl = this.mapSessionToConference.get(_session);
	var confConnConfSessionEvt = {};
	confConnConfSessionEvt.functionName = "onConferenceSessionStatusChanged";
	confConnConfSessionEvt.sessionStatus = 0; // SessionStatus_NotConnected
	confConnConfSessionEvt.conference = confHdl;
	this.callback(confConnConfSessionEvt);
	this.mapSessionToConference.delete(_session);
	return this;
}

RemoteConferenceConnectorManager.prototype.updateSessionMedia = function(_session) {
	var confHdl = this.mapSessionToConference.get(_session);
	var confConnConfSessionEvt = {};
	confConnConfSessionEvt.functionName = "onConferenceSessionStatusChanged";
	confConnConfSessionEvt.sessionStatus = 2; // SessionStatus_Connected
	confConnConfSessionEvt.conference = confHdl;
	confConnConfSessionEvt.permissions = {};
	confConnConfSessionEvt.video = {};
	confConnConfSessionEvt.video.mediaStreamId = -1;
	confConnConfSessionEvt.screenshare = {};
	confConnConfSessionEvt.screenshare.mediaStreamId = -1;
	
	this.confConnConferences.forEach(function (confItem) {
		if (confItem.conference === confHdl) {
			if (confItem.conferenceType === 0) {  // CloudConferenceType_Screenshare
				confConnConfSessionEvt.screenshare.mediaStreamId = confItem.streamId;
			}
		}
	});
	this.callback(confConnConfSessionEvt);
	return this;
}

RemoteConferenceConnectorManager.prototype.processEvent = function(_jsonEventObj) {
	return this;
}

RemoteConferenceConnectorManager.prototype.onConferenceBridgeManagerEvent = function(_args) {
	if (_args.functionName === "onConferenceDetails") {
		var confMgr = this.confBridgeMgr;
		var cb = this.callback;
		var confConnConfs = [];
		confMgr.queryConferenceList(function (confListArgs) {
			confListArgs.conferences.forEach(function (confItem) {
				var confConnConfItem = {};
				confConnConfItem.conference = confItem.conference;
				confConnConfItem.conferenceType = 0; // CloudConferenceType_Screenshare
				confConnConfItem.displayName = confItem.conferenceInfo.label;
				confConnConfItem.streamId = confItem.conferenceInfo.streamId;
				confConnConfItem.joinUrl = confItem.conferenceJoinUrl;
				confConnConfs.push(confConnConfItem);
			});
			this.confConnConferences = confConnConfs;
			var confConnConfListEvt = {};
			confConnConfListEvt.functionName = "onConferenceListUpdated";
			confConnConfListEvt.conferenceList = confConnConfs;
			cb(confConnConfListEvt);
		});
	}
	else if (_args.functionName === "onConferenceEnded") {
		var confEndedEvt = {};
		confEndedEvt.functionName = "onConferenceEnded";
		confEndedEvt.conference = _args.conference;
		this.callback(confEndedEvt);
	}
}
