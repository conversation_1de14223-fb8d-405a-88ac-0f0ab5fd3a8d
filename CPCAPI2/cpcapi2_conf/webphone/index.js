console.info("index", "START");

/* --- */
var displayName;
var authToken;
var thisUserPhotoArrayBuff;
/* --- */

var localSdkConnector = new SdkConnector();
var confConnectorMgr = new ConferenceConnectorManager();
var remoteConfConnectorMgr = new RemoteConferenceConnectorManager();
localSdkConnector.registerEventProcessor(confConnectorMgr);
confConnectorMgr.setSdkConnector(localSdkConnector);

var sipAccountMgr = new SipAccountManager();
localSdkConnector.registerEventProcessor(sipAccountMgr);
sipAccountMgr.setSdkConnector(localSdkConnector);

var sipConvMgr = new SipConversationManager();
localSdkConnector.registerEventProcessor(sipConvMgr);
sipConvMgr.setSdkConnector(localSdkConnector);

var audioMgr = new AudioManager();
localSdkConnector.registerEventProcessor(audioMgr);
audioMgr.setSdkConnector(localSdkConnector);

var videoMgr = new VideoManager();
localSdkConnector.registerEventProcessor(videoMgr);
videoMgr.setSdkConnector(localSdkConnector);

var confConnHandle = null;
var cloudConfHandle = null;
var conferenceHandle = null;
var screenshareConfHandle = null;
var sipAccountHandle = null;
var sipConvHandle = null;
localSdkConnector.setConnectedCallback(onLocalSdkConnected);
localSdkConnector.setLoginCallback(onLocalSdkLogin);
var isScreenshareOwner = false;

// WS-AVC-PLAYER
var wsavc = null;
var frameRecvCnt = 0;
var wsAvcConnected = false;
var wsAvcStreamId = -1;

function doAuth(_username, _password, _joinUrl, _useHelperApp) {
	if (_username == "")
		return;

	var passwd = (_password === "1234" ? _password : Sha256.hash(_password));
	if (_useHelperApp === false) {
		localSdkConnector.stop();
		passwd = _password;
		confConnectorMgr = remoteConfConnectorMgr;
	}
	confConnectorMgr.createConferenceConnector(
		function(connHdl) {
			confConnHandle = connHdl;
			
			confConnectorMgr.setCallback(onConferenceConnectorEvent);
			var confConnSettings = confConnectorMgr.makeConferenceConnectorSettings(getAuthServerApiKey(), getAuthServerUrl(_useHelperApp), _joinUrl, getOrchServerUrl(), passwd, getRegionCode(), _username);
			confConnectorMgr.setConnectionSettings(confConnHandle, confConnSettings);
			confConnectorMgr.connectToConferenceService(confConnHandle);
		}
	);
}

function crackAuthToken(_authToken) {
	if (_authToken) {
		var decoded = window.atob(_authToken.substring(_authToken.indexOf(".")+1, _authToken.lastIndexOf(".")));
		if (decoded.length > 0) {
			return JSON.parse(decoded);
		}
	}
	return null;
}

function onConferenceConnectorEvent(_funcObj) {
	if (_funcObj.functionName == "onServiceConnectionStatusChanged") {
		/*
		   ServiceConnectionStatus_Disconnecting = 0,
		   ServiceConnectionStatus_Disconnected = 1,
		   ServiceConnectionStatus_Connecting = 2,
		   ServiceConnectionStatus_Authenticating = 3,
		   ServiceConnectionStatus_Connected = 4,
		   ServiceConnectionStatus_ConnFailure = 5,
		   ServiceConnectionStatus_AuthFailure = 6
		*/
		if (_funcObj.connectionStatus == 4 /*connected*/) {
			this.authToken = _funcObj.authToken;
		}
		app.$emit("onServiceConnectionStatusChanged", _funcObj.connectionStatus, crackAuthToken(_funcObj.authToken));
	}
	else if (_funcObj.functionName == "onConferenceCreated") {
		app.$emit("onConferenceCreated", _funcObj.conference);
	}
	else if (_funcObj.functionName == "onConferenceEnded") {
		app.$emit("onConferenceEnded", _funcObj.conference);
	}
	else if (_funcObj.functionName == "onConferenceListUpdated") {
		app.$emit("onConferenceListUpdated", _funcObj.conferenceList); // list of CloudConferenceInfo
	}
	else if (_funcObj.functionName == "onConferenceSessionStatusChanged") {
		app.$emit("onConferenceSessionStatusChanged", _funcObj.conference, _funcObj.sessionStatus, _funcObj.permissions, (_funcObj.video.mediaStreamId >= 0 ? _funcObj.video.mediaStreamId : _funcObj.screenshare.mediaStreamId));
	}
	else if (_funcObj.functionName == "onConferenceParticipantListUpdated") {
		app.$emit("onConferenceParticipantListUpdated", _funcObj.conference, _funcObj.participantList, _funcObj.addedParticipants, _funcObj.updatedParticipants, _funcObj.removedParticipants);
	}
}

function doQueryConferenceList() {
	confConnectorMgr.queryConferenceList(confConnHandle);
}

function doQueryParticipantList(conf) {
    confConnectorMgr.queryParticipantList(conf.conferenceHandle);
}

function setCanCreateConferencePermissionSdk(_session, _participant, _enabled) {
	var perm = confConnectorMgr.makeParticipantPermissions(_enabled);
	confConnectorMgr.setParticipantPermissions(_session, _participant, perm);
}

function doCreateScreensharePresenterSession(_confHandle, _displayName) {
	/*
   MediaDirection_None = 0,
   MediaDirection_SendRecv,
   MediaDirection_SendOnly,
   MediaDirection_RecvOnly,
   MediaDirection_Inactive
	*/
	initWsAvc(true, function() {
		confConnectorMgr.createConferenceSession(_confHandle, function(confSession) {
			var sessionSettings = confConnectorMgr.makeCloudConferenceSessionSettings(0 /*CloudConferenceRole_Host*/, _displayName);
			confConnectorMgr.setSessionSettings(confSession, sessionSettings);
			var mediaSettings = confConnectorMgr.makeCloudConferenceSessionMediaSettings(0 /*MediaDirection_None*/, 2 /*MediaDirection_SendOnly*/);
			confConnectorMgr.setSessionMediaSettings(confSession, mediaSettings);
			confConnectorMgr.startSession(confSession);
			app.$emit("onConferenceSessionStarted", _confHandle, confSession);
		});
	});
}

function doCreateAVHostSession(_confHandle, _displayName) {
	initWsAvc(true, function() {
		confConnectorMgr.createConferenceSession(_confHandle, function(confSession) {
			var sessionSettings = confConnectorMgr.makeCloudConferenceSessionSettings(0 /*CloudConferenceRole_Host*/, _displayName);
			confConnectorMgr.setSessionSettings(confSession, sessionSettings);
			var mediaSettings = confConnectorMgr.makeCloudConferenceSessionMediaSettings(1 /*MediaDirection_SendRecv*/, 1 /*MediaDirection_SendRecv*/);
			confConnectorMgr.setSessionMediaSettings(confSession, mediaSettings);
			confConnectorMgr.startSession(confSession);
			app.$emit("onConferenceSessionStarted", _confHandle, confSession);
		});
	});
}

function doCreateConferenceSession(_useHelperApp, _confHandle, _displayName, _conferenceType) {
	initWsAvc(_useHelperApp, function() {
		confConnectorMgr.createConferenceSession(_confHandle, function(confSession) {
			var sessionSettings = confConnectorMgr.makeCloudConferenceSessionSettings(1 /*CloudConferenceRole_Participant*/, _displayName);
			confConnectorMgr.setSessionSettings(confSession, sessionSettings);
			var mediaSettings = _conferenceType === 0 ? 
			                    confConnectorMgr.makeCloudConferenceSessionMediaSettings(0 /*MediaDirection_None*/, 3 /*MediaDirection_RecvOnly*/) :
								confConnectorMgr.makeCloudConferenceSessionMediaSettings(1 /*MediaDirection_SendRecv*/, 1 /*MediaDirection_SendRecv*/);
			confConnectorMgr.setSessionMediaSettings(confSession, mediaSettings);
			confConnectorMgr.startSession(confSession);
			app.$emit("onConferenceSessionStarted", _confHandle, confSession);
		});
	});
}

function doSwitchToAVAsParticipant(_confSessionHandle) {
	var mediaSettings = confConnectorMgr.makeCloudConferenceSessionMediaSettings(1 /*MediaDirection_SendRecv*/, 1 /*MediaDirection_SendRecv*/);
	confConnectorMgr.setSessionMediaSettings(_confSessionHandle, mediaSettings);
	confConnectorMgr.updateSessionMedia(_confSessionHandle);
}

function doSwitchToScreenshareAsParticipant(_confSessionHandle) {
	var mediaSettings = confConnectorMgr.makeCloudConferenceSessionMediaSettings(0 /*MediaDirection_None*/, 3 /*MediaDirection_RecvOnly*/);
	confConnectorMgr.setSessionMediaSettings(_confSessionHandle, mediaSettings);
	confConnectorMgr.updateSessionMedia(_confSessionHandle);
}

function doCloseConferenceSession(_confSessionHandle) {
	console.info("index", "doCloseConferenceSession: " + _confSessionHandle);
	confConnectorMgr.endSession(_confSessionHandle);
}

function onLocalSdkConnected(_connected) {
	console.info("index", "onLocalSdkConnected");
	app.$emit("onLocalSdkConnected", _connected);
	if (_connected) {
		localSdkConnector.login("localclient", "localclient");
	}
}

function onLocalSdkLogin(_jsonApiVer) {
	console.info("index", "onLocalSdkLogin: jsonApiVersion=" + _jsonApiVer);	
	app.$emit("onLocalSdkLogin", _jsonApiVer);
	sipAccountMgr.setCallback(onSipAccountEvent);
	sipConvMgr.setCallback(onSipConversationEvent);
	audioMgr.setCallback(onAudioEvent);
	audioMgr.queryDeviceList();
	videoMgr.setCallback(onVideoEvent);
	videoMgr.queryDeviceList();
	videoMgr.queryScreenshareDeviceList(function (screenshareDevices) {
		let screenshareDeviceList = [{deviceDescription: "Default Monitor", deviceId: "cpc_sys_default"}];

		screenshareDevices.forEach(function(devInfo) {
			screenshareDeviceList.push({deviceDescription: devInfo.deviceDescription, deviceId: devInfo.deviceId});
		});

		app.$emit("onScreenshareDeviceListUpdated", screenshareDeviceList);

	}, true, false);
}

function makeVideoWsUrl(port) {
    var l = window.location;
	if (l.protocol === "file:") {
		return "wss://127.0.0.1:" + port;
	}
	return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
	//return ((l.protocol === "https:") ? "wss://" : "ws://") + "***********" + ":" + port;
}

function getAuthServerUrl(_useHelperApp) {
	return "https://cloudsdk4.bria-x.net:18082";
}

function getAuthServerApiKey() {
	return "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEZzshRNe4gMUuqNBwrE6leU/N5vW8\n+HW/pZw5XfsxvzEtUlGwp+A9sNedJNUtjYD/2gFFY/nFuM4Gmnu6zeteTQ==\n-----END PUBLIC KEY-----";
}

function getOrchServerUrl() {
	return "https://cpclientapi.softphone.com:18090/jsonApi";
	//return "https://screenshare1.bria-x.net/jsonApi"
	//return "http://**************:18090/jsonApi";
}

function getRegionCode() {
	return "LOCAL";
}

function doCreateScreenshareConference(_confLabel) {
	console.info("index", "doCreateScreenshareConference");
	
	/*
   CloudConferenceType_Screenshare = 0,
   CloudConferenceType_AudioVideo_MCU = 1,
   CloudConferenceType_AudioVideo_SFU = 2
	*/
	var confSettings = confConnectorMgr.makeCloudConferenceSettings(_confLabel, _confLabel, 0 /*screenshare*/);
	confConnectorMgr.createNewConference(confConnHandle, confSettings);
}

function doCreateAVConference(_confLabel) {
	console.info("index", "doCreateAVConference");
	
	/*
   CloudConferenceType_Screenshare = 0,
   CloudConferenceType_AudioVideo_MCU = 1,
   CloudConferenceType_AudioVideo_SFU = 2
	*/
	var confSettings = confConnectorMgr.makeCloudConferenceSettings(_confLabel, _confLabel, 1 /*AudioVideo_MCU*/);
	confConnectorMgr.createNewConference(confConnHandle, confSettings);
}

function doRegister() {
	console.info("index", "doRegister");
	// _username, _domain, _password, _displayName, _outboundProxy, _sipTransportType
	var acctSettings = sipAccountMgr.makeSipAccountSettings("1298", "counterpath.com", "ta6Ls86CarXysvX7", "CPCAPI2 Web Phone", "sbc.counterpath.com:15060", 3);
	sipAccountMgr.create(acctSettings, function(_acctHandle) {
		sipAccountHandle = _acctHandle;
		sipConvMgr.setDefaultSettings(_acctHandle, sipConvMgr.makeSipConversationSettings());
		sipAccountMgr.enable(_acctHandle);
	});
}

function doInvite(_uri) {
	console.info("index", "doInvite");
	initWsAvc(true, function() {
		sipConvMgr.createConversation(this.sipAccountHandle, function(_sipConvHandle) {
			sipConvHandle = _sipConvHandle;
			videoMgr.startCapture();
			sipConvMgr.configureMedia(_sipConvHandle, 1 /*audio*/, 1 /* MediaDirection_SendReceive */, 0 /* MediaCryptoSuite_None */, 1 /* MediaEncryptionMode_Unencrypted */, false, -1);
			sipConvMgr.configureMedia(_sipConvHandle, 2 /*video*/, 1 /* MediaDirection_SendReceive */, 0 /* MediaCryptoSuite_None */, 1 /* MediaEncryptionMode_Unencrypted */, false, -1);
			sipConvMgr.addParticipant(_sipConvHandle, _uri);
			sipConvMgr.start(_sipConvHandle);
		});
	});
}

function doEndCall() {
	console.info("index", "doEndCall");
	sipConvMgr.end(this.sipConvHandle);
	sipAccountMgr.disable(this.sipAccountHandle);
	videoMgr.stopCapture();
}

function getAccountStatusString(_statusId) {
/*
      Status_Registered          = 1,
      Status_Failure             = 2, // deprecated; this state will never be reported (Unregistered will be reported instead)
      Status_Unregistered        = 3,
      Status_Registering         = 4,
      Status_Unregistering       = 5,
      Status_WaitingToRegister   = 6,
      Status_Refreshing          = 7
*/
	if (_statusId === 1) {
		return "Registered";
	} else if (_statusId === 2) {
		return "Failure";
	} else if (_statusId === 3) {
		return "Unregistered";
	} else if (_statusId === 4) {
		return "Registering";
	} else if (_statusId === 5) {
		return "Unregistering";
	} else if (_statusId === 6) {
		return "WaitingToRegister";
	} else if (_statusId === 7) {
		return "Refreshing";
	}
	return "Unknown";
}

function onSipAccountEvent(_eventObj) {
	console.info("index", "onSipAccountEvent: " + _eventObj.functionName);
	console.info("index", _eventObj.functionName + " accountStatus: " + getAccountStatusString(_eventObj.args.accountStatus));
	app.$emit("onSipAccountStatusChanged", _eventObj.args.accountStatus, getAccountStatusString(_eventObj.args.accountStatus));
}

function getConversationStatusString(_convStatus) {
	/*
	   ConversationState_None                 = 0,
   ConversationState_LocalOriginated      = 1000, 
   ConversationState_RemoteOriginated     = 1010, 
   ConversationState_RemoteRinging        = 1020, 
   ConversationState_LocalRinging         = 1030, 
   ConversationState_Connected            = 1040, 
   ConversationState_Early                = 1050,
   ConversationState_Ended                = 1060
	*/
	if (_convStatus === 1000) {
		return "Outgoing call initiated";
	}
	else if (_convStatus === 1010) {
		return "Incoming call";
	}
	else if (_convStatus === 1020) {
		return "Ringing";
	}
	else if (_convStatus === 1030) {
		return "Incoming call alerting";
	}
	else if (_convStatus === 1040) {
		return "In Call";
	}
	else if (_convStatus === 1050) {
		return "Early";
	}
	else if (_convStatus === 1060) {
		return "Call Ended";
	}
	return "Unknown";
}

function onSipConversationEvent(_eventObj) {
	console.info("index", "onSipConversationEvent: " + _eventObj.functionName);
	if (_eventObj.functionName == "onNewConversation" ||
	    _eventObj.functionName == "onConversationStateChanged" ||
		_eventObj.functionName == "onConversationEnded") {
		app.$emit("onSipConversationStatusChanged", _eventObj.conversation, _eventObj.args.conversationState, getConversationStatusString(_eventObj.args.conversationState));
	}
	else if (_eventObj.functionName == "onConversationMediaChanged") {
		_eventObj.args.remoteMediaInfo.forEach(function(_mi) {
			if (_mi.mediaType === 2) {
				console.info("index", "found video media stream: " + _mi.mediaStreamId);
				bindWebsocketTochannel_remote(_mi.mediaStreamId);
			}
		});
	}
}

function startCapturePreview() {
	console.info("index", "startCapturePreview");
	initWsAvc(true, function() {
		videoMgr.startCapture();
		videoMgr.startCapturePreview();
	});
}

function stopCapturePreview() {
	console.info("index", "startCapturePreview");
	videoMgr.stopCapturePreview();
}

function startMonitoringAudioCaptureDeviceLevels(_mic) {
	var selectedDev = 1; // kAudioDefaultSystemDeviceId
	if (_mic === "cpc_sys_default") {
		selectedDev = 1; // kAudioDefaultSystemDeviceId
	}
	else {
		selectedDev = parseInt(_mic, 10);
	}
	console.info("index", "startMonitoringAudioCaptureDeviceLevels: " + selectedDev);
	audioMgr.startMonitoringCaptureDeviceLevels(selectedDev);
}

function stopMonitoringAudioCaptureDeviceLevels() {
	console.info("index", "stopMonitoringAudioCaptureDeviceLevels");
	audioMgr.stopMonitoringCaptureDeviceLevels();
}

function onAudioEvent(_eventObj) {
	console.info("index", "onAudioEvent: " + _eventObj.functionName);
	if (_eventObj.functionName == "onAudioDeviceListUpdated") {
		var devices = _eventObj.deviceInfo;
		
		let micList = [{friendlyName: "Microphone - System Default", id: "cpc_sys_default"}];
		let speakerList = [{friendlyName: "Speaker - System Default", id: "cpc_sys_default"}];

		devices.forEach(function(devInfo) {
			if (devInfo.deviceType == 1) {
			    micList.push({friendlyName: devInfo.friendlyName, id: devInfo.id});
		    }
			if (devInfo.deviceType == 2) {
			    speakerList.push({friendlyName: devInfo.friendlyName, id: devInfo.id});
			}
			console.info("index", "added audio device: " + devInfo.friendlyName + " - " + devInfo.id);			
		});

		app.$emit("onAudioDeviceListUpdated", micList, speakerList);
	}
	else if (_eventObj.functionName == "onAudioDeviceLevelChange") {
		var inputLevel = _eventObj.inputDeviceLevel;
		var outputLevel = _eventObj.outputDeviceLevel;
		
		app.$emit("onAudioDeviceLevelChange", inputLevel, outputLevel);
	}
}

function onVideoEvent(_eventObj) {
	console.info("index", "onVideoEvent: " + _eventObj.functionName);
	if (_eventObj.functionName == "onVideoDeviceListUpdated") {
		var devices = _eventObj.deviceInfo;
		
		let videoDeviceList = [{friendlyName: "Camera - System Default", id: "cpc_sys_default"}];

		devices.forEach(function(devInfo) {
			videoDeviceList.push({friendlyName: devInfo.friendlyName, id: devInfo.id});
			console.info("index", "added camera: " + devInfo.friendlyName + " - " + devInfo.id);			
		});

		app.$emit("onVideoDeviceListUpdated", videoDeviceList);
	}
	else if (_eventObj.functionName == "onCapturePreviewStarted") {
		console.info("index", "capture preview started for video stream handle: " + _eventObj.videoStreamHandle);
		bindWebsocketTochannel_remote(_eventObj.videoStreamHandle);
	}
}

function setMyPhoto(myPhotoArrayBuff) {
	this.thisUserPhotoArrayBuff = myPhotoArrayBuff;
}

function configureDevices(participantIdentity, mic, speaker, camera) {
	console.info("index", "configureDevices()");

	if (participantIdentity == "")
		return;

	this.displayName = participantIdentity;

	if (mic != "cpc_sys_default") {
		var selectedDev = parseInt(mic, 10);
		console.info("index", "setting selected mic: " + selectedDev);
		audioMgr.setCaptureDevice(selectedDev, 1);
	}
	
	if (speaker != "cpc_sys_default") {
		var selectedDev = parseInt(speaker, 10);
		console.info("index", "setting selected speaker: " + selectedDev);
		audioMgr.setRenderDevice(selectedDev, 1);
	}
	
	if (camera != "cpc_sys_default") {
		var selectedCamera = parseInt(camera, 10);
		console.info("index", "setting selected camera: " + selectedCamera);
		videoMgr.setCaptureDevice(selectedCamera);
	}
}

function configureMonitorSelection(participantIdentity, monitorDevId) {
	console.info("index", "configureMonitorSelection()");

	if (participantIdentity == "")
		return;

	this.displayName = participantIdentity;

	if (monitorDevId != "cpc_sys_default") {
		var selectedDev = parseInt(monitorDevId, 10);
		console.info("index", "setting selected screen: " + selectedDev);
		videoMgr.setScreenshareCaptureDevice(selectedDev);
	}
}

function doLocalMicMuteSdk(_enabled) {
	console.info("index", "doLocalMicMute(" + _enabled + ")");
	audioMgr.setMicMute(_enabled);
}

function doLocalVideoMuteSdk(_enabled) {
	console.info("index", "doLocalVideoMute(" + _enabled + ")");
	videoMgr.setVideoMute(_enabled);
}

function doDestroyScreenshareConference() {
	console.info("index", "doDestroyScreenshareConference");
}

function getBaseURL() {
   return location.protocol + "//" + location.hostname + 
      (location.port && ":" + location.port) + location.pathname;
   //return location.href;
}

function copyToClipboard(txtToCopy) {
	var inp = document.createElement('textarea');
	document.body.appendChild(inp);
	inp.value = txtToCopy;
	inp.select();
	document.execCommand('copy',false);
	inp.remove();
}

function initCanvasForWsAvc() {
	var videodiv = document.getElementById('videoContainerDiv');
	canvas = document.createElement('canvas');
	canvas.id = "img1";
	canvas.width = 2560;
	canvas.height = 2560;
	//canvas.style.width = "100%";
	//canvas.style.objectFit="contain";
	videodiv.appendChild(canvas);
}

function initCanvasForCpcWebclient() {
	var videodiv = document.getElementById('videoContainerDiv');
	canvas = document.createElement('canvas');
	canvas.id = "img1";
	canvas.width = 2560;
	canvas.height = 2560;
	//canvas.style.width = "100%";
	//canvas.style.objectFit="contain";
	videodiv.appendChild(canvas);

	context = canvas.getContext("2d");
	imageData = context.getImageData(0, 0, 2560, 2560);
	scratch = new Uint8Array((2560 * 2560 * 4) + 1500);
}

function supportsWebGl(_canvas) {
	// !jjg! something strange here ...  other attempts to getContext succeed when they should not if we do this
	/*
	var gl;
	try {
		gl = _canvas.getContext('experimental-webgl');
	} catch (e) {
		//console.info('index', 'failed to initGL', e);
	}

	if (!gl) {
		console.info('index', 'Unable to initialize WebGL. Your browser may not support it.')
		return false;
	}
	console.info('index', 'webgl is supported');
	*/
	return true;
}

function initWsAvc(_useHelperApp, _cb) {
	console.info("index", "initWsAvc(" + _useHelperApp + ")");
	
	if (wsavc != null) {
		_cb();
		return;
	}
	
	initCanvasForWsAvc();
	
	wsAvcConnected = false;
	
	// Create h264 player
	wsavc = new WSAvcPlayer(canvas, (supportsWebGl(canvas) ? "webgl" : "yuvcanvas"), 1, 35);

	//expose instance for button callbacks
	window.wsavc = wsavc;
	
	var uri = makeVideoWsUrl(9005);
	if (_useHelperApp) {
		uri = "wss://cpclientapi.softphone.com:2115";
	}
	wsavc.connect(uri);

	wsavc.on('disconnected',()=>console.log('WS Disconnected'))
	wsavc.on('connected',()=>{
		console.log('WS connected')
		wsAvcConnected = true;
		_cb();
	})
	wsavc.on('packet_recv', (wspacketsRecv)=>{
		frameRecvCnt = frameRecvCnt + 10; // we only get the event every 10 packets in order to save on event firing overhead
		if (wsAvcConnected === true) {
			var framercvdobj = {};
			framercvdobj.recvCnt = frameRecvCnt;
			framercvdobj.videoStreamId = wsAvcStreamId;
			wsavc.send('frames_recvd',framercvdobj);
		}
	})

	wsavc.on('initalized',(payload)=>{
	console.log('Initialized', payload)
	})

	wsavc.on('stream_active',active=>console.log('Stream is ',active?'active':'offline'))
	wsavc.on('custom_event_from_server',event=>console.log('got event from server', event))	
}

function bindWebsocketTochannel_remote(_videoStreamId) {
   // format is:
   // {"action":"stream_bind","payload":{"videoStreamId":1}}
	console.log('bindWebsocketTochannel_remote: ' + _videoStreamId)
	wsAvcStreamId = _videoStreamId;
	frameRecvCnt = 0;
	
	var vsidobj = {};
	vsidobj.videoStreamId = _videoStreamId;
	wsavc.send('stream_bind',vsidobj);
}

console.info("index", "END");
