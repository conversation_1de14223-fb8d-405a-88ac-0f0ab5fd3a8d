#include "stdafx.h"
#include "WatsonTTS.h"

#include "../CPCAPI2/impl/util/FileUtils.h"

#include <stdio.h>
#include <ostream>
#include <fstream>
#include <locale>
#include <iostream>
#include <string>
#include <sstream>
#include <stdlib.h>
#include <errno.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>

// curlpp
#include <curlpp/cURLpp.hpp>
#include <curlpp/Easy.hpp>
#include <curlpp/Options.hpp>
#include <curlpp/Exception.hpp>

namespace CPCAPI2
{
namespace Agent
{
WatsonTTS::WatsonTTS()
{
   // clear all error/access channels
   mEndpoint.clear_access_channels(websocketpp::log::alevel::all);
   mEndpoint.clear_error_channels(websocketpp::log::elevel::all);

   // Initialize the endpoint
   mEndpoint.init_asio();

   // Mark this endpoint as perpetual. Perpetual endpoints will not exit
   // even if there are no connections.
   mEndpoint.start_perpetual();

   mEndpoint.set_tls_init_handler([this](websocketpp::connection_hdl) {
      return websocketpp::lib::make_shared<boost::asio::ssl::context>(boost::asio::ssl::context::tlsv1);
   });

   // Start a background thread and run the endpoint in that thread
   mThread.reset(new thread_type(&tlsClient::run, &mEndpoint));
}

WatsonTTS::~WatsonTTS()
{
}

void WatsonTTS::on_message(websocketpp::connection_hdl hdl, tlsMessagePtr msg)
{
   if (msg->get_opcode() == websocketpp::frame::opcode::TEXT)
   {
      std::shared_ptr<rapidjson::Document> jsonRequest(new rapidjson::Document);
      jsonRequest->Parse<0>(msg->get_payload().c_str());

      if (jsonRequest->HasParseError())
      {
         return;
      }
      std::cout << msg->get_payload() << std::endl;
      FILE* file = fopen("result.wav", "w");
      fclose(file);
   }
   else
   {
      FILE* file = fopen("result.wav", "ab");
      const std::string& pl = msg->get_payload();
      fwrite(pl.data(), sizeof(char), pl.size(), file);
      fclose(file);
   }
}

void WatsonTTS::on_open(websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection on_open" << std::endl;
   mCondConnected.notify_one();
}

void WatsonTTS::on_close(websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection closed" << std::endl;
   mConn.reset();
   mAudioDataCallback("result.wav");
}

void WatsonTTS::on_fail(websocketpp::connection_hdl hdl)
{
   std::cout << "websocket connection failed" << std::endl;
   mConn.reset();
   mCondFailed.notify_one();
}

int WatsonTTS::connect()
{
   bool attemptConnect = true;
   do
   {
      mEndpoint.get_io_service().post(boost::bind(&WatsonTTS::connectImpl, this));
      std::unique_lock<std::mutex> lk(mMutex);
      attemptConnect = (std::cv_status::timeout == mCondConnected.wait_for(lk, std::chrono::milliseconds(16000)));
      if (attemptConnect)
      {
         mCondFailed.wait_for(lk, std::chrono::milliseconds(5000));
      }
   } while (attemptConnect);

   return kSuccess;
}

void WatsonTTS::connectImpl()
{
   //   url << "https://stream.watsonplatform.net/authorization/api/v1/token?url=https://stream.watsonplatform.net/text-to-speech/api";
   /*
   {
   "url": "https://stream.watsonplatform.net/text-to-speech/api",
   "password": "fg3RWZsnpz8N",
   "username": "f8ffd849-f3cf-4a64-9072-8cacad094f6f"
   }
   */

   try {
      curlpp::Cleanup cleaner;
      curlpp::Easy request;

      request.setOpt(new curlpp::options::Url("https://stream.watsonplatform.net/authorization/api/v1/token?url=https://stream.watsonplatform.net/text-to-speech/api"));
      request.setOpt(new curlpp::options::Verbose(true));
      request.setOpt(new curlpp::options::SslEngineDefault());
      request.setOpt(new curlpp::options::SslVerifyPeer(false));
      request.setOpt(new curlpp::options::UserPwd("f8ffd849-f3cf-4a64-9072-8cacad094f6f:fg3RWZsnpz8N"));

      curlpp::types::WriteFunctionFunctor functor(utilspp::make_functor(this, &WatsonTTS::getApiTokenHandler));
      curlpp::options::WriteFunction* writeFunc = new curlpp::options::WriteFunction(functor);
      request.setOpt(writeFunc);

      request.perform();
   }
   catch (curlpp::LogicError & e) {
      std::cout << e.what() << std::endl;
   }
   catch (curlpp::RuntimeError & e) {
      std::cout << e.what() << std::endl;
   }

   websocketpp::lib::error_code ec;
   std::string serverUri;
   {
      std::stringstream ss;
      ss << "wss://stream.watsonplatform.net/text-to-speech/api/v1/synthesize?";
      ss << "watson-token=" << mWatsonToken;
      ss << "&voice=en-GB_KateVoice";
      serverUri = ss.str();
   }
   // connect to this address
   mConn = mEndpoint.get_connection(serverUri.c_str(), ec);
   if (ec) {
      mConn.reset();
      return;
   }
   mConn->set_open_handshake_timeout(30000);
   mConn->set_open_handler(bind(&WatsonTTS::on_open, this, std::placeholders::_1));
   mConn->set_fail_handler(bind(&WatsonTTS::on_fail, this, std::placeholders::_1));
   mConn->set_message_handler(bind(&WatsonTTS::on_message, this, std::placeholders::_1, std::placeholders::_2));
   mConn->set_close_handler(bind(&WatsonTTS::on_close, this, std::placeholders::_1));

   mEndpoint.connect(mConn);
}

size_t WatsonTTS::getApiTokenHandler(char* ptr, size_t size, size_t nmemb)
{
   std::cout << ptr << std::endl;
   mWatsonToken = std::string(ptr, nmemb);
   return nmemb;
}

int WatsonTTS::shutdown()
{
   mEndpoint.get_io_service().post(boost::bind(&WatsonTTS::shutdownImpl, this));
   mThread->join();
   return kSuccess;
}

void WatsonTTS::shutdownImpl()
{
   if (mConn.get() != NULL)
   {
      // for each connection call close
      mConn->set_close_handler([](websocketpp::connection_hdl) {
      });
      mConn->close(0, "");
      mConn.reset();
   }

   // Unflag the endpoint as perpetual. This will instruct it to stop once
   // all connections are finished.
   mEndpoint.stop_perpetual();
}

int WatsonTTS::convert(const cpc::string& textStr, const std::function<void(const std::string&)>& cb)
{
   mEndpoint.get_io_service().post(boost::bind(&WatsonTTS::convertImpl, this, textStr, cb));
   return kSuccess;
}

void WatsonTTS::convertImpl(const cpc::string& textStr, const std::function<void(const std::string&)>& cb)
{
   if (mConn->get_state() == websocketpp::session::state::open)
   {
      mAudioDataCallback = cb;

      rapidjson::Document enableCommand;
      enableCommand.SetObject();

      rapidjson::Value textVal(textStr.c_str(), enableCommand.GetAllocator());
      enableCommand.AddMember("text", textVal, enableCommand.GetAllocator());

      rapidjson::Value acceptVal("audio/l16;rate=16000", enableCommand.GetAllocator());
      enableCommand.AddMember("accept", acceptVal, enableCommand.GetAllocator());

      rapidjson::StringBuffer buffer(0, 1024);
      rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
      enableCommand.Accept(writer);

      mEndpoint.send(mConn, buffer.GetString(), websocketpp::frame::opcode::text);
   }
}
}
}
