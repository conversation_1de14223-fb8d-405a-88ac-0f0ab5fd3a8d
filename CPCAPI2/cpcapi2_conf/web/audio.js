function AudioManager() {
	this.callback = null;
	this.sdkConnector = null;
}

AudioManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

AudioManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "AudioJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

AudioManager.prototype.queryDeviceList = function() {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryDeviceList";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.setCaptureDevice = function(_deviceId, _role) {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setCaptureDevice";
	fn.functionObject.deviceId = _deviceId;
	fn.functionObject.role = _role;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.setRenderDevice = function(_deviceId, _role) {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setRenderDevice";
	fn.functionObject.deviceId = _deviceId;
	fn.functionObject.role = _role;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.setMicMute = function(_enabled) {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setMicMute";
	fn.functionObject.enabled = _enabled;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.setCodecPriority = function(_codecId, _priority) {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setCodecPriority";
	fn.functionObject.codecId = _codecId;
	fn.functionObject.priority = _priority;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.queryCodecList = function() {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryCodecList";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.setCodecEnabled = function(_codecId, _enabled) {
	var fn = {};
	fn.moduleId = "AudioJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setCodecEnabled";
	fn.functionObject.codecId = _codecId;
	fn.functionObject.enabled = _enabled;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

AudioManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "AudioJsonProxy") {
		this.callback(_jsonEventObj.functionObject);
	}
	return this;
}
