<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>CounterPath SDK in the Cloud</title>
    <!-- Include basic JQuery and JQuery UI files -->
    <script src="lib/jquery/jquery.js"></script>
    <link href="lib/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css">
    <script src="lib/jquery-ui/jquery-ui.min.js"></script>
	<script src="sha256.js"></script>

    <link rel="icon" href="favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="style.css">
</head>

<body>

    <div class="wrapper" id="app" v-cloak>
        <!-- Sidebar -->
        <nav id="sidebar">
		    <!--
            <div class="sidebar-header">
                <h3>Participants</h3>
            </div>
			-->

            <!-- participants -->
			
            <div class="container-fluid">
                <transition-group enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">                                                        
                    <div class="participant-container" v-for="part in participants" v-bind:key="part.displayName">
                        <div class="row text-center">
                            <div v-if="part.hasFloor === false" class="avatar-cropper">
                                <img class="avatar" :src="part.avatar" @click="doTakeSnapshot(part.participant)"></img>
                            </div>
                            <div v-if="part.hasFloor === true" class="avatar-cropper-floor">
                                <img class="avatar" :src="part.avatar" @click="doTakeSnapshot(part.participant)"></img>
                            </div>							
                        </div>

                        <div class="row vertical-align">
                            <div class="col-md-9 alighn-left displayName">{{part.displayName}}</div>
                            <!-- div class="col-md-3 align-left">
                                <button v-if="part.participantType === 'SIP'" type='button' class='btn btn-kick-participant' @click="endCall(part.conversation)"
                                    data-toggle='tooltip' title='Kick Participant'> 
                                    <span class='glyphicon glyphicon-remove-circle'></span> 
                                </button>
                                <button v-if="part.participantType === 'Web'" type='button' class='btn btn-kick-participant' @click="endPeerConnection(part.participant)"
                                    data-toggle='tooltip' title='Kick Participant'> 
                                    <span class='glyphicon glyphicon-remove-circle'></span> 
                                </button>
                            </div -->
                        </div>

                        <div class="row vertical-align participant-controls">
                            <button class="btn btn-lg btn-participant" @click="setCanCreateConferencePermissionApp(part.participant, true)" data-toggle="tooltip" title='Make Presenter'>
                                <img class="img-conf-h" src="./assets/images/inactivePresenter.png" ></img>
                            </button>
                            <button class="btn btn-lg btn-participant" @click="setCanCreateConferencePermissionApp(part.participant, false)" data-toggle="tooltip" title='Remove Presenter Role'>
                                <img class="img-conf-h" src="./assets/images/activePresenter.png" ></img>
                            </button>
						<!--
                            <button v-if="part.hasFloor === false" class="btn btn-lg btn-participant" @click="addToFloor(part.participant)" data-toggle="tooltip" title='Add Participant to Floor'>
                                <img class="img-conf-h" src="./assets/images/addToFloor.png" ></img>
                            </button>
                            <button v-if="part.hasFloor === true" class="btn btn-lg btn-participant" @click="removeFromFloor(part.participant)" data-toggle="tooltip" title='Remove Participant from Floor'>
                                <img class="img-conf-h" src="./assets/images/removeFromFloor.png" ></img>
                            </button>
                            <button class="btn btn-lg btn-participant" @click="muteAudio(part.conversation)" data-toggle="tooltip" title='Mute Audio'>
                                <img class="img-conf-h" src="./assets/images/microphone.png" ></img>
                            </button>
                            <button class="btn btn-lg btn-participant" @click="unmuteAudio(part.conversation)" data-toggle="tooltip" title='UnMute Audio'>
                                <img class="img-conf-h" src="./assets/images/muted.png" ></img>
                            </button>
                            <button class="btn btn-lg btn-participant" @click="muteVideo(part.conversation)" data-toggle="tooltip" title='Mute Video'>
                                <img class="img-conf-w" src="./assets/images/startVideo.png" ></img>
                            </button>
                            <button class="btn btn-lg btn-participant" @click="unmuteVideo(part.conversation)" data-toggle="tooltip" title='UnMute Video'>
                                <img class="img-conf-w" src="./assets/images/stopVideo.png" ></img>
                            </button>
						-->
                        </div>     

                    </div>
                </transition-group>
            </div>
			

            <!-- Plugin Download Prompt -->
            <div class="container-fluid" v-if="showPluginDownload">
                <p>
                    Download the CPCAPI2 Web Client for best results.
                </p>

                <ul class="list-unstyled CTAs">
                    <li><a href="cpcapi2_webclient.dmg" class="download">Download for Mac</a></li>
                    <li><a href="cpcapi2_webclient.zip" class="download">Download for Windows</a></li>
                </ul>
            </div>

        </nav>

        <!-- Page Content -->
        <transition enter-active-class="animated fadeIn" leave-active-class="animated fadeOut" appear>
            <div id="content">

                <!-- Top Navbar -->
                <nav class="navbar navbar-default">
                    <div class="container-fluid">

                        <div class="navbar-header">
                            <a href="#">
                                <img id="sidebarCollapse" src="http://www.counterpath.com/assets/images/press_support_graphics/logo-counterpath-primary.png"></img>
                            </a>
                        </div>

                        <div class="collapse navbar-collapse">
                            <ul class="nav navbar-nav navbar-right">
                                <li v-if="conferenceState === 'IDLE' || conferenceState === 'AUTHENTICATED' || conferenceState === 'CONNECTED'"><a href="#"><i>the courage to be unique through innovation</i></a></li>
                                <li v-if="conferenceState === 'JOINING'"><a href="#"><i>Connecting audio and video streams (send/receive) ...</i></a></li>
                                <li v-if="conferenceState === 'JOINED'"><a href="#"><i>Connected</i></a></li>
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- Main Content -->
                <div v-if="showPluginDownload">
                    <p>
                        Want to test drive the future? Download the CPCAPI2 Web Client (<a href="cpcapi2_webclient.dmg">Mac</a>)
                        (<a href="cpcapi2_webclient.zip">Windows</a>), launch it, and refresh this page to join the conference with enhanced audio and video quality.
						<BR>
						<b>NOTE:</b> This server is under active development; if you have the CPCAPI2 Web Client, and still see this notice, it probably means you need to download an updated version.
                    </p>
                </div>

                <!-- Login Prompts -->
                <div class="container-fluid" v-if="conferenceState === 'IDLE' || conferenceState === 'AUTHENTICATED' || conferenceState === 'CONNECTED' || conferenceState === 'CONFIGURE_JOIN_PARAMS'">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default login-container">
                                <div class="panel-heading">
                                    <h3 class="text-center">Conference Bridge</h3>
                                </div>
                                <div class="panel-body panel-body-2">
                
                                    <transition enter-active-class="animated fadeInRight" leave-active-class="animated fadeOutLeft" mode="out-in">
                                        <div v-if="conferenceState === 'IDLE'" :key="1">
										    <p class="text-center">
											<input type="text" class="form-control" placeholder="Username" required="true" autofocus="" v-model="username" /></p>
										    <p class="text-center">
											<input type="password" class="form-control" placeholder="Password" required="" autofocus="" v-model="password" /></p>
										    <p class="text-center">
											<input type="text" class="form-control" placeholder="Display Name" required="" autofocus="" v-model="displayName" /></p>
											<div v-if="showConfJoinUrl">
										    <p class="text-center">
											<input type="text" class="form-control" placeholder="Conference URL" required="" autofocus="" v-model="conferenceUrl" /></p>
											</div>
                                            <br/>
                                            <button class="btn btn-lg btn-primary btn-block" type="submit" v-if="!showPluginDownload" @click="submitLogin">Login</button>
											<button class="btn btn-lg btn-primary btn-block" type="submit" v-if="showPluginDownload" @click="submitLoginNoDownload">Login (no download required)</button>
                                        </div>
                                                      
										<div v-else-if="conferenceState === 'AUTHENTICATED'" :key="2">
											<br/>
											<table width="100%" class="conference-list">
											<tbody v-for="conf in conferences">
												<tr>
												<td><a href="#" v-on:click.stop="selectConferenceForJoin(conf)">{{conf.label}}</a></td>
												<td>{{conf.numParticipants}} participants</td>
												<td class="conference-list-action-btn">
													<button type='button' class='btn btn-kick-participant' @click="selectConferenceForJoin(conf)"
														data-toggle='tooltip' title='Join'> 
														<span class='glyphicon glyphicon-circle-arrow-right'></span> 
													</button>												
												</td>
												</tr>
											</tbody>
											</table>

											<div v-if="confSelectedForJoin.conferenceType > 0">
												<p class="text-center">Select Audio/Video Devices</p>
												<select class="form-control" v-model="micSelection">
													<option v-for="mic in microphones" :value="mic.id">{{mic.friendlyName}}</option>
												</select>
												
												<select class="form-control" v-model="speakerSelection">
													<option v-for="speaker in speakers" :value="speaker.id">{{speaker.friendlyName}}</option>
												</select>
												
												<select class="form-control" v-model="cameraSelection">
													<option v-for="camera in cameras" :value="camera.id">{{camera.friendlyName}}</option>
												</select>
												
												<br/>
												<p class="text-center">Select a Placeholder Photo (PNG format)</p>
												<input type="file" class="form-control" placeholder="Photo" required="" autofocus="" @change="onFileChange" /></p>
												<br/>
												<br/>
												
												<button class="btn btn-lg btn-primary btn-block" type="submit" @click="submitConferenceDetails">Join</button>
											</div>												
											
											
											
											<div v-if="conferences.length == 0 && userContextMatches">
												<table width="100%" class="conference-list">
												    <tr>
													<td>Enter a conference name:</td>
													</tr>
													<tr>
													<td><input type="text" class="form-control" placeholder="conference1" required="true" autofocus="" v-model="confBridgeCreateLabel"/></td>
													</tr>
												</table>

											    <p>
												<input type="radio" id="rdioConfFull" name="confTypeRg" value="conftypefull" v-model="confType" />
												<label for="rdioConfFull">Video Conference</label>
												<div v-if="confType === 'conftypefull'">
													<p class="text-center">Select Audio/Video Devices</p>
													<select class="form-control" v-model="micSelection">
														<option v-for="mic in microphones" :value="mic.id">{{mic.friendlyName}}</option>
													</select>
													
													<select class="form-control" v-model="speakerSelection">
														<option v-for="speaker in speakers" :value="speaker.id">{{speaker.friendlyName}}</option>
													</select>
													
													<select class="form-control" v-model="cameraSelection">
														<option v-for="camera in cameras" :value="camera.id">{{camera.friendlyName}}</option>
													</select>
													
													<br/>
													<p class="text-center">Select a Placeholder Photo (PNG format)</p>
													<input type="file" class="form-control" placeholder="Photo" required="" autofocus="" @change="onFileChange" /></p>
													<br/>
													<br/>
													<!-- button class="btn btn-lg btn-primary btn-block" type="submit" @click="submitMediaDevs">Join</button -->
												</div>												
												</p>
												<p>
												<input type="radio" id="rdioConfScreenShare" name="confTypeRg" value="conftypescr" v-model="confType" />
												<label for="rdioConfScreenShare">Screen Share</label>
												<div v-if="confType === 'conftypescr'">
													<p class="text-center">Select a Screen</p>
													<select class="form-control" v-model="monitorSelection">
														<option v-for="monitor in monitors" :value="monitor.deviceId">{{monitor.deviceDescription}}</option>
													</select>
													
													<br/>
													<br/>
													<!-- button class="btn btn-lg btn-primary btn-block" type="submit" @click="submitMediaDevs">Join</button -->
												</div>														
												</p>						
												<button class="btn btn-lg btn-primary btn-block" type="submit" v-if="!showPluginDownload" @click="submitConferenceCreate(confBridgeCreateLabel, confType)">Create Conference</button>												
											</div>
											
											<div v-if="conferences.length == 0 && !userContextMatches">
												<table width="100%" class="conference-list">
												    <tr>
													<td>No active conferences</td>
													</tr>
												</table>
												
												<br/>
												<button class="btn btn-lg btn-primary btn-block" type="submit" v-if="!showPluginDownload" @click="submitLogin">Refresh</button>
												<button class="btn btn-lg btn-primary btn-block" type="submit" v-if="showPluginDownload" @click="submitLoginNoDownload">Refresh</button>
											</div>
                                        </div>
										

                                    </transition>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Conference Controls -->
                <transition enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">
                    <div class="conference-controls" v-if="conferenceState === 'JOINED'">
					<!--
                        <button class="btn btn-lg btn-conf" v-if="canLocalMute" @click="doLocalMicMute(true)" data-toggle="tooltip" title='Mute Audio'>
                            <img class="img-conf-h" src="./assets/images/microphone.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf" v-if="canLocalUnMute" @click="doLocalMicMute(false)" data-toggle="tooltip" title='UnMute Audio'>
                            <img class="img-conf-w" src="./assets/images/muted.png" ></img>
                        </button>
					-->
                        <button class="btn btn-lg btn-conf" v-if="canLocalPauseVideo" @click="doLocalVideoMute(true)" data-toggle="tooltip" title='Pause Video'>
                            <img class="img-conf-h" src="./assets/images/videoMute.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf" v-if="canLocalResumeVideo" @click="doLocalVideoMute(false)" data-toggle="tooltip" title='Resume Video'>
                            <img class="img-conf-w" src="./assets/images/videoUnmute.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf-toggle" v-if="canAddParticipant" @click="doAddParticipantInput" data-toggle="tooltip" title='Add Participant'>
                            <img class="img-conf-h" src="./assets/images/addParticipant.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf" v-if="canStartScreenShare" @click='doStartScreenshareInput' data-toggle="tooltip" title='Start ScreenShare'>
                            <img class="img-conf-w" src="./assets/images/startScreenShare.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf" v-if="canStopScreenShare" @click='stopScreenShare' data-toggle="tooltip" title='Stop ScreenShare'>
                            <img class="img-conf-w" src="./assets/images/stopScreenShare.png" ></img>
                        </button>

                        <button class="btn btn-lg btn-conf" v-if="canStartTranscription" @click='startTranscription' data-toggle="tooltip" title='Start Transcription'>
                            <img class="img-conf-w" src="./assets/images/transcriptionStart.png" ></img>
                        </button>
                        <button class="btn btn-lg btn-conf" v-if="canStopTranscription" @click='stopTranscription' data-toggle="tooltip" title='Stop Transcription'>
                            <img class="img-conf-w" src="./assets/images/transcriptionStop.png" ></img>
                        </button>
						
						<!--
                        <button class="btn btn-lg btn-conf-toggle" v-if="showConferenceToggles" @click='switchToConferenceApp(false)' data-toggle="tooltip"
                            title='View Conference Video'>
                            <img class="img-conf-h" src="./assets/images/switch dominant - l.png">
                            View Conference Video
                            </img>                    
                        </button>

                        <button class="btn btn-lg btn-conf-toggle" v-if="showConferenceToggles" @click='switchToConferenceApp(true)' data-toggle="tooltip"
                            title="View ScreenShare">
                            <img class="img-conf-h" src="./assets/images/switch dominant reverse - l.png">
                            View ScreenShare
                            </img>                    
                        </button>
						-->
						
						<!-- div class="conference-list" -->
							<template v-for="conf in conferences">
								<button class="btn btn-lg btn-conf-toggle" @click='switchToConference(conf)' data-toggle="tooltip"
									:title='conf.label'>
									<img class="img-conf-h" src="./assets/images/switch dominant reverse - l.png">
									{{conf.label}}
									</img>                    
								</button>
								<button type='button' class='btn btn-lg btn-conf-toggle' @click="closeConference(conf)"
									data-toggle='tooltip' title='Close'> 
									<span class='glyphicon glyphicon-remove'></span> 
								</button>												
								
							</template>
						<!-- /div -->

						<!--
                        <button class="btn btn-lg btn-conf btn-hangup" @click="this.window.location.reload()">
                            <img class="img-conf-w" src="./assets/images/hangup.png" data-toggle="tooltip" title='End Call'></img>
                        </button>
						-->
                    </div>
                </transition>

				<!-- Add Participant -->
				<div class="form-inline add-participant-controls" v-if="addingParticipant">
					<input type="text" class="form-control mb-2 mr-sm-2 mb-sm-0" placeholder="Enter a URI or extension ..." required="" autofocus="" v-model="uriToAdd" />
					<button class="btn btn-lg btn-conf-toggle" @click='doAddParticipant' data-toggle="tooltip" title='Add Participant'>
						invite
					</button>
				</div>
				
				<div class="form-inline add-participant-controls" v-if="startingScreenshareFromAVConference">
					<select class="form-control" v-model="monitorSelection">
						<option v-for="monitor in monitors" :value="monitor.deviceId">{{monitor.deviceDescription}}</option>
					</select>
					<input type="text" class="form-control mb-2 mr-sm-2 mb-sm-0" placeholder="screenshare1" required="" autofocus="" v-model="confBridgeCreateLabel" />
					<button class="btn btn-lg btn-conf-toggle" @click="submitConferenceCreate(confBridgeCreateLabel, 'conftypescr')" data-toggle="tooltip" title="Share my Screen">
						Share my Screen
					</button>
				</div>

				<div id="audioTranscriptionDiv">
				</div>
				
                <!-- Video Canvas -->
                <div class="videoContainer" id="videoContainerDiv">
                    <!-- canvas id="img1" width="1922" height="1080"></canvas -->
                </div>

            </div>
        </transition>
    </div>

    <!-- Bootstrap Js CDN -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- jQuery Nicescroll CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.nicescroll/3.6.8-fix/jquery.nicescroll.min.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#sidebar").niceScroll({
                cursorcolor: '#FC6830',
                cursorwidth: 3,
                cursorborder: 'none'
            });

            $('#sidebarCollapse').on('click', function () {
                $('#sidebar, #content').toggleClass('active');
                $('.collapse.in').toggleClass('in');
                $('a[aria-expanded=true]').attr('aria-expanded', 'false');
            });

            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>

    <script src="lib/vue/vue.js"></script>
    <script src="lib/ws-avc-player/WSAvcPlayer.js?v=10"></script>
	
	<!-- script src="dash_downloader.js?v=9"></script -->
    <script src="sdkconnector.js?v=10"></script>
    <script src="call.js?v=10"></script>
	<script src="confconnector.js?v=10"></script>
	<script src="confconnector_remote.js?v=10"></script>
    <script src="confbridge.js?v=10"></script>
    <script src="peerconnection.js?v=10"></script>
    <script src="audio.js?v=10"></script>
    <script src="video.js?v=10"></script>
    <script src="index.js?v=10"></script>
    <!-- script src="img_replace.js?v=8"></script -->
	<script src="text.min.js?v=10"></script>
	
    <script src="app.js?v=10"></script>

</body>

</html>