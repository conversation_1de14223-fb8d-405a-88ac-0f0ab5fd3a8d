!function(e,r){"object"==typeof exports&&"object"==typeof module?module.exports=r():"function"==typeof define&&define.amd?define("WSAvcPlayer",[],r):"object"==typeof exports?exports.WSAvcPlayer=r():e.WSAvcPlayer=r()}(this,function(){return function(e){var r={};function t(n){if(r[n])return r[n].exports;var i=r[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,t),i.l=!0,i.exports}return t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},t.p="",t(t.s=10)}([function(e,r,t){"use strict";var n=t(1),i=t(2);function a(){}a.prototype={norm:function(){for(var e=this.elements.length,r=0;e--;)r+=Math.pow(this.elements[e],2);return Math.sqrt(r)},e:function(e){return e<1||e>this.elements.length?null:this.elements[e-1]},dimensions:function(){return{rows:1,cols:this.elements.length}},rows:function(){return 1},cols:function(){return this.elements.length},modulus:function(){return Math.sqrt(this.dot(this))},eql:function(e){var r=this.elements.length,t=e.elements||e;if(r!=t.length)return!1;for(;r--;)if(Math.abs(this.elements[r]-t[r])>n.precision)return!1;return!0},dup:function(){return a.create(this.elements)},map:function(e){var r=[];return this.each(function(t,n){r.push(e(t,n))}),a.create(r)},each:function(e){for(var r=this.elements.length,t=0;t<r;t++)e(this.elements[t],t+1)},toUnitVector:function(){var e=this.modulus();return 0===e?this.dup():this.map(function(r){return r/e})},angleFrom:function(e){var r=e.elements||e,t=this.elements.length;if(t!=r.length)return null;var n=0,i=0,a=0;if(this.each(function(e,t){n+=e*r[t-1],i+=e*e,a+=r[t-1]*r[t-1]}),i=Math.sqrt(i),a=Math.sqrt(a),i*a==0)return null;var s=n/(i*a);return s<-1&&(s=-1),s>1&&(s=1),Math.acos(s)},isParallelTo:function(e){var r=this.angleFrom(e);return null===r?null:r<=n.precision},isAntiparallelTo:function(e){var r=this.angleFrom(e);return null===r?null:Math.abs(r-Math.PI)<=n.precision},isPerpendicularTo:function(e){var r=this.dot(e);return null===r?null:Math.abs(r)<=n.precision},add:function(e){var r=e.elements||e;return this.elements.length!=r.length?this.map(function(r){return r+e}):this.map(function(e,t){return e+r[t-1]})},subtract:function(e){if("number"==typeof e)return this.map(function(r){return r-e});var r=e.elements||e;return this.elements.length!=r.length?null:this.map(function(e,t){return e-r[t-1]})},multiply:function(e){return this.map(function(r){return r*e})},elementMultiply:function(e){return this.map(function(r,t){return e.e(t)*r})},sum:function(){var e=0;return this.map(function(r){e+=r}),e},chomp:function(e){for(var r=[],t=e;t<this.elements.length;t++)r.push(this.elements[t]);return a.create(r)},top:function(e){for(var r=[],t=0;t<e;t++)r.push(this.elements[t]);return a.create(r)},augment:function(e){for(var r=this.elements,t=0;t<e.length;t++)r.push(e[t]);return a.create(r)},x:function(e){return this.multiply(e)},log:function(){return a.log(this)},elementDivide:function(e){return this.map(function(r,t){return r/e.e(t)})},product:function(){var e=1;return this.map(function(r){e*=r}),e},dot:function(e){var r=e.elements||e,t=0,n=this.elements.length;if(n!=r.length)return null;for(;n--;)t+=this.elements[n]*r[n];return t},cross:function(e){var r=e.elements||e;if(3!=this.elements.length||3!=r.length)return null;var t=this.elements;return a.create([t[1]*r[2]-t[2]*r[1],t[2]*r[0]-t[0]*r[2],t[0]*r[1]-t[1]*r[0]])},max:function(){for(var e=0,r=this.elements.length;r--;)Math.abs(this.elements[r])>Math.abs(e)&&(e=this.elements[r]);return e},maxIndex:function(){for(var e=0,r=this.elements.length,t=-1;r--;)Math.abs(this.elements[r])>Math.abs(e)&&(e=this.elements[r],t=r+1);return t},indexOf:function(e){for(var r=null,t=this.elements.length,n=0;n<t;n++)null===r&&this.elements[n]==e&&(r=n+1);return r},toDiagonalMatrix:function(){return i.Diagonal(this.elements)},round:function(){return this.map(function(e){return Math.round(e)})},transpose:function(){for(var e=this.elements.length,r=[],t=0;t<e;t++)r.push([this.elements[t]]);return i.create(r)},snapTo:function(e){return this.map(function(r){return Math.abs(r-e)<=n.precision?e:r})},distanceFrom:function(e){if(e.anchor||e.start&&e.end)return e.distanceFrom(this);var r=e.elements||e;if(r.length!=this.elements.length)return null;var t,n=0;return this.each(function(e,i){t=e-r[i-1],n+=t*t}),Math.sqrt(n)},liesOn:function(e){return e.contains(this)},liesIn:function(e){return e.contains(this)},rotate:function(e,r){var t,n,s,o,f=null;switch(e.determinant&&(f=e.elements),this.elements.length){case 2:return 2!=(t=r.elements||r).length?null:(f||(f=i.Rotation(e).elements),n=this.elements[0]-t[0],s=this.elements[1]-t[1],a.create([t[0]+f[0][0]*n+f[0][1]*s,t[1]+f[1][0]*n+f[1][1]*s]));case 3:if(!r.direction)return null;var u=r.pointClosestTo(this).elements;return f||(f=i.Rotation(e,r.direction).elements),n=this.elements[0]-u[0],s=this.elements[1]-u[1],o=this.elements[2]-u[2],a.create([u[0]+f[0][0]*n+f[0][1]*s+f[0][2]*o,u[1]+f[1][0]*n+f[1][1]*s+f[1][2]*o,u[2]+f[2][0]*n+f[2][1]*s+f[2][2]*o]);default:return null}},reflectionIn:function(e){if(e.anchor){var r=this.elements.slice(),t=e.pointClosestTo(r).elements;return a.create([t[0]+(t[0]-r[0]),t[1]+(t[1]-r[1]),t[2]+(t[2]-(r[2]||0))])}var n=e.elements||e;return this.elements.length!=n.length?null:this.map(function(e,r){return n[r-1]+(n[r-1]-e)})},to3D:function(){var e=this.dup();switch(e.elements.length){case 3:break;case 2:e.elements.push(0);break;default:return null}return e},inspect:function(){return"["+this.elements.join(", ")+"]"},setElements:function(e){return this.elements=(e.elements||e).slice(),this}},a.create=function(e){return(new a).setElements(e)},a.i=a.create([1,0,0]),a.j=a.create([0,1,0]),a.k=a.create([0,0,1]),a.Random=function(e){for(var r=[];e--;)r.push(Math.random());return a.create(r)},a.Fill=function(e,r){for(var t=[];e--;)t.push(r);return a.create(t)},a.Zero=function(e){return a.Fill(e,0)},a.One=function(e){return a.Fill(e,1)},a.log=function(e){return e.map(function(e){return Math.log(e)})},e.exports=a},function(e,r,t){"use strict";Math.sign=function(e){return e<0?-1:1};e.exports={precision:1e-6,approxPrecision:1e-5}},function(e,r,t){"use strict";var n=t(1);t(0);function a(e,r,t,n){for(var i=e.elements,a=n-1;a--;){for(var s=[],o=0;o<t;o++)s.push(o==a?1:0);i.unshift(s)}for(a=n-1;a<r;a++)for(;i[a].length<t;)i[a].unshift(0);return $M(i)}function s(){for(var e=this,r=f.I(e.rows()),t=e.transpose(),n=f.I(e.cols()),i=Number.MAX_VALUE,a=0;i>2.2737e-13&&a<100;){var s=t.transpose().qrJs();t=s.R,r=r.x(s.Q),s=t.transpose().qrJs(),n=n.x(s.Q);var o=(t=s.R).triu(1).unroll().norm(),u=t.diagonal().norm();0==u&&(u=1),i=o/u,a++}var l=t.diagonal(),c=[];for(a=1;a<=l.cols();a++){var h=l.e(a);if(c.push(Math.abs(h)),h<0)for(var d=0;d<n.rows();d++)r.elements[d][a-1]=-r.elements[d][a-1]}return{U:n,S:$V(c).toDiagonalMatrix(),V:r}}function o(){for(var e=this.rows(),r=this.cols(),t=f.I(e),n=this,i=1;i<Math.min(e,r);i++){for(var s=n.slice(i,0,i,i).col(1),o=[1];o.length<=e-i;)o.push(0);o=$V(o);var u=s.add(o.x(s.norm()*Math.sign(s.e(1)))),l=$M(u),c=a(f.I(e-i+1).subtract(l.x(2).x(l.transpose()).div(l.transpose().x(l).e(1,1))),e,r,i);n=c.x(n),t=t.x(c)}return{Q:t,R:n}}function f(){}f.prototype={solve:function(e){var r=this.lu();e=r.P.x(e);var t=r.L.forwardSubstitute(e),n=r.U.backSubstitute(t);return r.P.x(n)},pcaProject:function(e,r){var t=(r=r||function(e){var r=e.transpose().x(e).x(1/e.rows()).svd();return{U:r.U,S:r.S}}(this).U).slice(1,r.rows(),1,e);return{Z:this.x(t),U:r}},pcaRecover:function(e){var r=this.cols(),t=e.slice(1,e.rows(),1,r);return this.x(t.transpose())},triu:function(e){return e||(e=0),this.map(function(r,t,n){return n-t>=e?r:0})},unroll:function(){for(var e=[],r=1;r<=this.cols();r++)for(var t=1;t<=this.rows();t++)e.push(this.e(t,r));return $V(e)},slice:function(e,r,t,n){var a=[];for(0==r&&(r=this.rows()),0==n&&(n=this.cols()),i=e;i<=r;i++){var s=[];for(j=t;j<=n;j++)s.push(this.e(i,j));a.push(s)}return $M(a)},e:function(e,r){return e<1||e>this.elements.length||r<1||r>this.elements[0].length?null:this.elements[e-1][r-1]},row:function(e){return e>this.elements.length?null:$V(this.elements[e-1])},col:function(e){if(e>this.elements[0].length)return null;for(var r=[],t=this.elements.length,n=0;n<t;n++)r.push(this.elements[n][e-1]);return $V(r)},dimensions:function(){return{rows:this.elements.length,cols:this.elements[0].length}},rows:function(){return this.elements.length},cols:function(){return this.elements[0].length},approxEql:function(e){return this.eql(e,n.approxPrecision)},eql:function(e,r){var t=e.elements||e;if(void 0===t[0][0]&&(t=f.create(t).elements),this.elements.length!=t.length||this.elements[0].length!=t[0].length)return!1;for(var i,a=this.elements.length,s=this.elements[0].length;a--;)for(i=s;i--;)if(Math.abs(this.elements[a][i]-t[a][i])>(r||n.precision))return!1;return!0},dup:function(){return f.create(this.elements)},map:function(e){for(var r,t=[],n=this.elements.length,i=this.elements[0].length;n--;)for(r=i,t[n]=[];r--;)t[n][r]=e(this.elements[n][r],n+1,r+1);return f.create(t)},isSameSizeAs:function(e){var r=e.elements||e;return void 0===r[0][0]&&(r=f.create(r).elements),this.elements.length==r.length&&this.elements[0].length==r[0].length},add:function(e){if("number"==typeof e)return this.map(function(r,t,n){return r+e});var r=e.elements||e;return void 0===r[0][0]&&(r=f.create(r).elements),this.isSameSizeAs(r)?this.map(function(e,t,n){return e+r[t-1][n-1]}):null},subtract:function(e){if("number"==typeof e)return this.map(function(r,t,n){return r-e});var r=e.elements||e;return void 0===r[0][0]&&(r=f.create(r).elements),this.isSameSizeAs(r)?this.map(function(e,t,n){return e-r[t-1][n-1]}):null},canMultiplyFromLeft:function(e){var r=e.elements||e;return void 0===r[0][0]&&(r=f.create(r).elements),this.elements[0].length==r.length},mulOp:function(e,r){if(!e.elements)return this.map(function(t){return r(t,e)});var t=!!e.modulus;if(void 0===(p=e.elements||e)[0][0]&&(p=f.create(p).elements),!this.canMultiplyFromLeft(p))return null;for(var n,i,a,s,o,u=this.elements,l=[],c=u.length,h=p[0].length,d=u[0].length,b=c;b--;){for(i=[],n=u[b],s=h;s--;){for(a=0,o=d;o--;)a+=r(n[o],p[o][s]);i[s]=a}l[b]=i}var p=f.create(l);return t?p.col(1):p},div:function(e){return this.mulOp(e,function(e,r){return e/r})},multiply:function(e){return this.mulOp(e,function(e,r){return e*r})},x:function(e){return this.multiply(e)},elementMultiply:function(e){return this.map(function(r,t,n){return e.e(t,n)*r})},sum:function(){var e=0;return this.map(function(r){e+=r}),e},mean:function(){for(var e=this.dimensions(),r=[],t=1;t<=e.cols;t++)r.push(this.col(t).sum()/e.rows);return $V(r)},column:function(e){return this.col(e)},log:function(){return this.map(function(e){return Math.log(e)})},minor:function(e,r,t,n){for(var i,a,s,o=[],u=t,l=this.elements.length,c=this.elements[0].length;u--;)for(o[i=t-u-1]=[],a=n;a--;)s=n-a-1,o[i][s]=this.elements[(e+i-1)%l][(r+s-1)%c];return f.create(o)},transpose:function(){for(var e,r=this.elements.length,t=[],n=this.elements[0].length;n--;)for(e=r,t[n]=[];e--;)t[n][e]=this.elements[e][n];return f.create(t)},isSquare:function(){return this.elements.length==this.elements[0].length},max:function(){for(var e,r=0,t=this.elements.length,n=this.elements[0].length;t--;)for(e=n;e--;)Math.abs(this.elements[t][e])>Math.abs(r)&&(r=this.elements[t][e]);return r},indexOf:function(e){var r,t,n=this.elements.length,i=this.elements[0].length;for(r=0;r<n;r++)for(t=0;t<i;t++)if(this.elements[r][t]==e)return{i:r+1,j:t+1};return null},diagonal:function(){if(!this.isSquare)return null;for(var e=[],r=this.elements.length,t=0;t<r;t++)e.push(this.elements[t][t]);return $V(e)},toRightTriangular:function(){var e,r,t,n,i=this.dup(),a=this.elements.length,s=this.elements[0].length;for(r=0;r<a;r++){if(0==i.elements[r][r])for(t=r+1;t<a;t++)if(0!=i.elements[t][r]){for(e=[],n=0;n<s;n++)e.push(i.elements[r][n]+i.elements[t][n]);i.elements[r]=e;break}if(0!=i.elements[r][r])for(t=r+1;t<a;t++){var o=i.elements[t][r]/i.elements[r][r];for(e=[],n=0;n<s;n++)e.push(n<=r?0:i.elements[t][n]-i.elements[r][n]*o);i.elements[t]=e}}return i},toUpperTriangular:function(){return this.toRightTriangular()},determinant:function(){if(!this.isSquare())return null;if(1==this.cols&&1==this.rows)return this.row(1);if(0==this.cols&&0==this.rows)return 1;for(var e=this.toRightTriangular(),r=e.elements[0][0],t=e.elements.length,n=1;n<t;n++)r*=e.elements[n][n];return r},det:function(){return this.determinant()},isSingular:function(){return this.isSquare()&&0===this.determinant()},trace:function(){if(!this.isSquare())return null;for(var e=this.elements[0][0],r=this.elements.length,t=1;t<r;t++)e+=this.elements[t][t];return e},tr:function(){return this.trace()},rank:function(){for(var e,r=this.toRightTriangular(),t=0,i=this.elements.length,a=this.elements[0].length;i--;)for(e=a;e--;)if(Math.abs(r.elements[i][e])>n.precision){t++;break}return t},rk:function(){return this.rank()},augment:function(e){var r=e.elements||e;void 0===r[0][0]&&(r=f.create(r).elements);var t,n=this.dup(),i=n.elements[0].length,a=n.elements.length,s=r[0].length;if(a!=r.length)return null;for(;a--;)for(t=s;t--;)n.elements[a][i+t]=r[a][t];return n},inverse:function(){if(!this.isSquare()||this.isSingular())return null;for(var e,r,t,n,i,a=this.elements.length,s=a,o=this.augment(f.I(a)).toRightTriangular(),u=o.elements[0].length,l=[];s--;){for(t=[],l[s]=[],n=o.elements[s][s],r=0;r<u;r++)i=o.elements[s][r]/n,t.push(i),r>=a&&l[s].push(i);for(o.elements[s]=t,e=s;e--;){for(t=[],r=0;r<u;r++)t.push(o.elements[e][r]-o.elements[s][r]*o.elements[e][s]);o.elements[e]=t}}return f.create(l)},inv:function(){return this.inverse()},round:function(){return this.map(function(e){return Math.round(e)})},snapTo:function(e){return this.map(function(r){return Math.abs(r-e)<=n.precision?e:r})},inspect:function(){for(var e=[],r=this.elements.length,t=0;t<r;t++)e.push($V(this.elements[t]).inspect());return e.join("\n")},toArray:function(){for(var e=[],r=this.elements.length,t=0;t<r;t++)e.push(this.elements[t]);return e},setElements:function(e){var r,t,n=e.elements||e;if(void 0!==n[0][0]){for(r=n.length,this.elements=[];r--;)for(t=n[r].length,this.elements[r]=[];t--;)this.elements[r][t]=n[r][t];return this}var i=n.length;for(this.elements=[],r=0;r<i;r++)this.elements.push([n[r]]);return this},maxColumnIndexes:function(){for(var e=[],r=1;r<=this.rows();r++){for(var t=null,n=-1,i=1;i<=this.cols();i++)(null===t||this.e(r,i)>t)&&(t=this.e(r,i),n=i);e.push(n)}return $V(e)},maxColumns:function(){for(var e=[],r=1;r<=this.rows();r++){for(var t=null,n=1;n<=this.cols();n++)(null===t||this.e(r,n)>t)&&(t=this.e(r,n));e.push(t)}return $V(e)},minColumnIndexes:function(){for(var e=[],r=1;r<=this.rows();r++){for(var t=null,n=-1,i=1;i<=this.cols();i++)(null===t||this.e(r,i)<t)&&(t=this.e(r,i),n=i);e.push(n)}return $V(e)},minColumns:function(){for(var e=[],r=1;r<=this.rows();r++){for(var t=null,n=1;n<=this.cols();n++)(null===t||this.e(r,n)<t)&&(t=this.e(r,n));e.push(t)}return $V(e)},partialPivot:function(e,r,t,n,i){for(var a=0,s=0,o=e;o<=n.rows();o++)Math.abs(n.e(o,r))>s&&(s=Math.abs(n.e(e,r)),a=o);if(a!=e){var f=n.elements[e-1];n.elements[e-1]=n.elements[a-1],n.elements[a-1]=f,t.elements[e-1][e-1]=0,t.elements[e-1][a-1]=1,t.elements[a-1][a-1]=0,t.elements[a-1][e-1]=1}return t},forwardSubstitute:function(e){for(var r=[],t=1;t<=this.rows();t++){for(var n=0,i=1;i<t;i++)n+=this.e(t,i)*r[i-1];r.push((e.e(t)-n)/this.e(t,t))}return $V(r)},backSubstitute:function(e){for(var r=[],t=this.rows();t>0;t--){for(var n=0,i=this.cols();i>t;i--)n+=this.e(t,i)*r[this.rows()-i];r.push((e.e(t)-n)/this.e(t,t))}return $V(r.reverse())},luJs:u,svdJs:s,qrJs:o};function u(){for(var e=this.dup(),r=f.I(e.rows()),t=f.I(e.rows()),n=f.Zeros(e.rows(),e.cols()),i=1,a=1;a<=Math.min(e.cols(),e.rows());a++){t=e.partialPivot(a,i,t,e,r);for(var s=a+1;s<=e.rows();s++){var o=e.e(s,i)/e.e(a,i);r.elements[s-1][a-1]=o;for(var u=a+1;u<=e.cols();u++)e.elements[s-1][u-1]-=e.e(a,u)*o}for(u=a;u<=e.cols();u++)n.elements[a-1][u-1]=e.e(a,u);i<e.cols()&&i++}return{L:r,U:n,P:t}}f.prototype.svd=s,f.prototype.qr=o,f.prototype.lu=u,f.create=function(e){return(new f).setElements(e)},f.I=function(e){for(var r,t=[],n=e;n--;)for(r=e,t[n]=[];r--;)t[n][r]=n==r?1:0;return f.create(t)},f.loadFile=function(e){for(var r=[],n=t(18).readFileSync(e,"utf-8").split("\n"),i=0;i<n.length;i++){var a=n[i].split(",");a.length>1&&r.push(a)}return(new f).setElements(r)},f.Diagonal=function(e){for(var r=e.length,t=f.I(r);r--;)t.elements[r][r]=e[r];return t},f.Rotation=function(e,r){if(!r)return f.create([[Math.cos(e),-Math.sin(e)],[Math.sin(e),Math.cos(e)]]);var t=r.dup();if(3!=t.elements.length)return null;var n=t.modulus(),i=t.elements[0]/n,a=t.elements[1]/n,s=t.elements[2]/n,o=Math.sin(e),u=Math.cos(e),l=1-u;return f.create([[l*i*i+u,l*i*a-o*s,l*i*s+o*a],[l*i*a+o*s,l*a*a+u,l*a*s-o*i],[l*i*s-o*a,l*a*s+o*i,l*s*s+u]])},f.RotationX=function(e){var r=Math.cos(e),t=Math.sin(e);return f.create([[1,0,0],[0,r,-t],[0,t,r]])},f.RotationY=function(e){var r=Math.cos(e),t=Math.sin(e);return f.create([[r,0,t],[0,1,0],[-t,0,r]])},f.RotationZ=function(e){var r=Math.cos(e),t=Math.sin(e);return f.create([[r,-t,0],[t,r,0],[0,0,1]])},f.Random=function(e,r){return 1===arguments.length&&(r=e),f.Zero(e,r).map(function(){return Math.random()})},f.Fill=function(e,r,t){2===arguments.length&&(t=r,r=e);for(var n,i=[],a=e;a--;)for(n=r,i[a]=[];n--;)i[a][n]=t;return f.create(i)},f.Zero=function(e,r){return f.Fill(e,r,0)},f.Zeros=function(e,r){return f.Zero(e,r)},f.One=function(e,r){return f.Fill(e,r,1)},f.Ones=function(e,r){return f.One(e,r)},e.exports=f},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,r){e||(0,n.default)(r)};var n=function(e){return e&&e.__esModule?e:{default:e}}(t(4))},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e){console.error(e),console.trace()}},function(e,r,t){"use strict";var n=t(0),i=t(2),a=t(9),s=t(1);function o(){}o.prototype={eql:function(e){return this.isParallelTo(e)&&this.contains(e.anchor)},dup:function(){return o.create(this.anchor,this.direction)},translate:function(e){var r=e.elements||e;return o.create([this.anchor.elements[0]+r[0],this.anchor.elements[1]+r[1],this.anchor.elements[2]+(r[2]||0)],this.direction)},isParallelTo:function(e){if(e.normal||e.start&&e.end)return e.isParallelTo(this);var r=this.direction.angleFrom(e.direction);return Math.abs(r)<=s.precision||Math.abs(r-Math.PI)<=s.precision},distanceFrom:function(e){if(e.normal||e.start&&e.end)return e.distanceFrom(this);if(e.direction){if(this.isParallelTo(e))return this.distanceFrom(e.anchor);var r=this.direction.cross(e.direction).toUnitVector().elements,t=this.anchor.elements,n=e.anchor.elements;return Math.abs((t[0]-n[0])*r[0]+(t[1]-n[1])*r[1]+(t[2]-n[2])*r[2])}var i=e.elements||e,a=(t=this.anchor.elements,this.direction.elements),s=i[0]-t[0],o=i[1]-t[1],f=(i[2]||0)-t[2],u=Math.sqrt(s*s+o*o+f*f);if(0===u)return 0;var l=(s*a[0]+o*a[1]+f*a[2])/u,c=1-l*l;return Math.abs(u*Math.sqrt(c<0?0:c))},contains:function(e){if(e.start&&e.end)return this.contains(e.start)&&this.contains(e.end);var r=this.distanceFrom(e);return null!==r&&r<=s.precision},positionOf:function(e){if(!this.contains(e))return null;var r=e.elements||e,t=this.anchor.elements,n=this.direction.elements;return(r[0]-t[0])*n[0]+(r[1]-t[1])*n[1]+((r[2]||0)-t[2])*n[2]},liesIn:function(e){return e.contains(this)},intersects:function(e){return e.normal?e.intersects(this):!this.isParallelTo(e)&&this.distanceFrom(e)<=s.precision},intersectionWith:function(e){if(e.normal||e.start&&e.end)return e.intersectionWith(this);if(!this.intersects(e))return null;var r=this.anchor.elements,t=this.direction.elements,i=e.anchor.elements,a=e.direction.elements,s=t[0],o=t[1],f=t[2],u=a[0],l=a[1],c=a[2],h=r[0]-i[0],d=r[1]-i[1],b=r[2]-i[2],p=u*u+l*l+c*c,m=s*u+o*l+f*c,w=((-s*h-o*d-f*b)*p/(s*s+o*o+f*f)+m*(u*h+l*d+c*b))/(p-m*m);return n.create([r[0]+w*s,r[1]+w*o,r[2]+w*f])},pointClosestTo:function(e){if(e.start&&e.end)return null===(h=e.pointClosestTo(this))?null:this.pointClosestTo(h);if(e.direction){if(this.intersects(e))return this.intersectionWith(e);if(this.isParallelTo(e))return null;var r=this.direction.elements,t=e.direction.elements,i=r[0],s=r[1],o=r[2],f=t[0],u=t[1],l=t[2],c=[(w=o*f-i*l)*l-(v=i*u-s*f)*u,v*f-(k=s*l-o*u)*l,k*u-w*f];return(h=a.create(e.anchor,c)).intersectionWith(this)}var h=e.elements||e;if(this.contains(h))return n.create(h);var d=this.anchor.elements,b=(i=(r=this.direction.elements)[0],s=r[1],o=r[2],d[0]),p=d[1],m=d[2],w=i*(h[1]-p)-s*(h[0]-b),v=s*((h[2]||0)-m)-o*(h[1]-p),k=o*(h[0]-b)-i*((h[2]||0)-m),g=n.create([s*w-o*k,o*v-i*w,i*k-s*v]),y=this.distanceFrom(h)/g.modulus();return n.create([h[0]+g.elements[0]*y,h[1]+g.elements[1]*y,(h[2]||0)+g.elements[2]*y])},rotate:function(e,r){void 0===r.direction&&(r=o.create(r.to3D(),n.k));var t=i.Rotation(e,r.direction).elements,a=r.pointClosestTo(this.anchor).elements,s=this.anchor.elements,f=this.direction.elements,u=a[0],l=a[1],c=a[2],h=s[0]-u,d=s[1]-l,b=s[2]-c;return o.create([u+t[0][0]*h+t[0][1]*d+t[0][2]*b,l+t[1][0]*h+t[1][1]*d+t[1][2]*b,c+t[2][0]*h+t[2][1]*d+t[2][2]*b],[t[0][0]*f[0]+t[0][1]*f[1]+t[0][2]*f[2],t[1][0]*f[0]+t[1][1]*f[1]+t[1][2]*f[2],t[2][0]*f[0]+t[2][1]*f[1]+t[2][2]*f[2]])},reverse:function(){return o.create(this.anchor,this.direction.x(-1))},reflectionIn:function(e){if(e.normal){var r=this.anchor.elements,t=this.direction.elements,n=r[0],i=r[1],a=r[2],s=t[0],f=t[1],u=t[2],l=this.anchor.reflectionIn(e).elements,c=n+s,h=i+f,d=a+u,b=e.pointClosestTo([c,h,d]).elements,p=[b[0]+(b[0]-c)-l[0],b[1]+(b[1]-h)-l[1],b[2]+(b[2]-d)-l[2]];return o.create(l,p)}if(e.direction)return this.rotate(Math.PI,e);var m=e.elements||e;return o.create(this.anchor.reflectionIn([m[0],m[1],m[2]||0]),this.direction)},setVectors:function(e,r){if(e=n.create(e),r=n.create(r),2==e.elements.length&&e.elements.push(0),2==r.elements.length&&r.elements.push(0),e.elements.length>3||r.elements.length>3)return null;var t=r.modulus();return 0===t?null:(this.anchor=e,this.direction=n.create([r.elements[0]/t,r.elements[1]/t,r.elements[2]/t]),this)}},o.create=function(e,r){return(new o).setVectors(e,r)},o.X=o.create(n.Zero(3),n.i),o.Y=o.create(n.Zero(3),n.j),o.Z=o.create(n.Zero(3),n.k),e.exports=o},function(e,r,t){"use strict";var n,i,a=e.exports={};function s(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function f(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(r){try{return n.call(null,e,0)}catch(r){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(e){n=s}try{i="function"==typeof clearTimeout?clearTimeout:o}catch(e){i=o}}();var u,l=[],c=!1,h=-1;function d(){c&&u&&(c=!1,u.length?l=u.concat(l):h=-1,l.length&&b())}function b(){if(!c){var e=f(d);c=!0;for(var r=l.length;r;){for(u=l,l=[];++h<r;)u&&u[h].run();h=-1,r=l.length}u=null,c=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===o||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(r){try{return i.call(null,e)}catch(r){return i.call(this,e)}}}(e)}}function p(e,r){this.fun=e,this.array=r}function m(){}a.nextTick=function(e){var r=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)r[t-1]=arguments[t];l.push(new p(e,r)),1!==l.length||c||f(b)},p.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=m,a.addListener=m,a.once=m,a.off=m,a.removeListener=m,a.removeAllListeners=m,a.emit=m,a.prependListener=m,a.prependOnceListener=m,a.listeners=function(e){return[]},a.binding=function(e){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=i;var n=function(e){return e&&e.__esModule?e:{default:e}}(t(3));function i(){}i.createFromElementId=function(e){const r=document.getElementById(e);(0,n.default)(r,"Could not find shader with ID: "+e);let t="",i=r.firstChild;for(;i;)3===i.nodeType&&(t+=i.textContent),i=i.nextSibling;const a=new Scriptor;return a.type=r.type,a.source=t,a},i.createFromSource=function(e,r){const t=new i;return t.type=e,t.source=r,t}},function(e,r,t){"use strict";var n=Function("return this")();r.Vector=t(0),n.$V=r.Vector.create,r.Matrix=t(2),n.$M=r.Matrix.create,r.Line=t(5),n.$L=r.Line.create,r.Plane=t(9),n.$P=r.Plane.create,r.Line.Segment=t(19),r.Sylvester=t(1)},function(e,r,t){"use strict";var n=t(0),i=t(2),a=t(5),s=t(1);function o(){}o.prototype={eql:function(e){return this.contains(e.anchor)&&this.isParallelTo(e)},dup:function(){return o.create(this.anchor,this.normal)},translate:function(e){var r=e.elements||e;return o.create([this.anchor.elements[0]+r[0],this.anchor.elements[1]+r[1],this.anchor.elements[2]+(r[2]||0)],this.normal)},isParallelTo:function(e){var r;return e.normal?(r=this.normal.angleFrom(e.normal),Math.abs(r)<=s.precision||Math.abs(Math.PI-r)<=s.precision):e.direction?this.normal.isPerpendicularTo(e.direction):null},isPerpendicularTo:function(e){var r=this.normal.angleFrom(e.normal);return Math.abs(Math.PI/2-r)<=s.precision},distanceFrom:function(e){if(this.intersects(e)||this.contains(e))return 0;if(e.anchor){var r=this.anchor.elements,t=e.anchor.elements,n=this.normal.elements;return Math.abs((r[0]-t[0])*n[0]+(r[1]-t[1])*n[1]+(r[2]-t[2])*n[2])}var i=e.elements||e;r=this.anchor.elements,n=this.normal.elements;return Math.abs((r[0]-i[0])*n[0]+(r[1]-i[1])*n[1]+(r[2]-(i[2]||0))*n[2])},contains:function(e){if(e.normal)return null;if(e.direction)return this.contains(e.anchor)&&this.contains(e.anchor.add(e.direction));var r=e.elements||e,t=this.anchor.elements,n=this.normal.elements;return Math.abs(n[0]*(t[0]-r[0])+n[1]*(t[1]-r[1])+n[2]*(t[2]-(r[2]||0)))<=s.precision},intersects:function(e){return void 0===e.direction&&void 0===e.normal?null:!this.isParallelTo(e)},intersectionWith:function(e){if(!this.intersects(e))return null;if(e.direction){var r=e.anchor.elements,t=e.direction.elements,s=this.anchor.elements,o=((u=this.normal.elements)[0]*(s[0]-r[0])+u[1]*(s[1]-r[1])+u[2]*(s[2]-r[2]))/(u[0]*t[0]+u[1]*t[1]+u[2]*t[2]);return n.create([r[0]+t[0]*o,r[1]+t[1]*o,r[2]+t[2]*o])}if(e.normal){for(var f=this.normal.cross(e.normal).toUnitVector(),u=this.normal.elements,l=(r=this.anchor.elements,e.normal.elements),c=e.anchor.elements,h=i.Zero(2,2),d=0;h.isSingular();)d++,h=i.create([[u[d%3],u[(d+1)%3]],[l[d%3],l[(d+1)%3]]]);for(var b=h.inverse().elements,p=u[0]*r[0]+u[1]*r[1]+u[2]*r[2],m=l[0]*c[0]+l[1]*c[1]+l[2]*c[2],w=[b[0][0]*p+b[0][1]*m,b[1][0]*p+b[1][1]*m],v=[],k=1;k<=3;k++)v.push(d==k?0:w[(k+(5-d)%3)%3]);return a.create(v,f)}},pointClosestTo:function(e){var r=e.elements||e,t=this.anchor.elements,i=this.normal.elements,a=(t[0]-r[0])*i[0]+(t[1]-r[1])*i[1]+(t[2]-(r[2]||0))*i[2];return n.create([r[0]+i[0]*a,r[1]+i[1]*a,(r[2]||0)+i[2]*a])},rotate:function(e,r){var t=e.determinant?e.elements:i.Rotation(e,r.direction).elements,n=r.pointClosestTo(this.anchor).elements,a=this.anchor.elements,s=this.normal.elements,f=n[0],u=n[1],l=n[2],c=a[0]-f,h=a[1]-u,d=a[2]-l;return o.create([f+t[0][0]*c+t[0][1]*h+t[0][2]*d,u+t[1][0]*c+t[1][1]*h+t[1][2]*d,l+t[2][0]*c+t[2][1]*h+t[2][2]*d],[t[0][0]*s[0]+t[0][1]*s[1]+t[0][2]*s[2],t[1][0]*s[0]+t[1][1]*s[1]+t[1][2]*s[2],t[2][0]*s[0]+t[2][1]*s[1]+t[2][2]*s[2]])},reflectionIn:function(e){if(e.normal){var r=this.anchor.elements,t=this.normal.elements,n=r[0],i=r[1],a=r[2],s=t[0],f=t[1],u=t[2],l=this.anchor.reflectionIn(e).elements,c=n+s,h=i+f,d=a+u,b=e.pointClosestTo([c,h,d]).elements,p=[b[0]+(b[0]-c)-l[0],b[1]+(b[1]-h)-l[1],b[2]+(b[2]-d)-l[2]];return o.create(l,p)}if(e.direction)return this.rotate(Math.PI,e);var m=e.elements||e;return o.create(this.anchor.reflectionIn([m[0],m[1],m[2]||0]),this.normal)},setVectors:function(e,r,t){if(null===(e=(e=n.create(e)).to3D()))return null;if(null===(r=(r=n.create(r)).to3D()))return null;if(void 0===t)t=null;else if(null===(t=(t=n.create(t)).to3D()))return null;var i,a,s=e.elements[0],o=e.elements[1],f=e.elements[2],u=r.elements[0],l=r.elements[1],c=r.elements[2];if(null!==t){var h=t.elements[0],d=t.elements[1],b=t.elements[2];if(0===(a=(i=n.create([(l-o)*(b-f)-(c-f)*(d-o),(c-f)*(h-s)-(u-s)*(b-f),(u-s)*(d-o)-(l-o)*(h-s)])).modulus()))return null;i=n.create([i.elements[0]/a,i.elements[1]/a,i.elements[2]/a])}else{if(0===(a=Math.sqrt(u*u+l*l+c*c)))return null;i=n.create([r.elements[0]/a,r.elements[1]/a,r.elements[2]/a])}return this.anchor=e,this.normal=i,this}},o.create=function(e,r,t){return(new o).setVectors(e,r,t)},o.XY=o.create(n.Zero(3),n.k),o.YZ=o.create(n.Zero(3),n.i),o.ZX=o.create(n.Zero(3),n.j),o.YX=o.XY,o.ZY=o.YZ,o.XZ=o.ZX,o.fromPoints=function(e){var r,t,i,a,f,u,l,c,h,d,b=e.length,p=[],m=n.Zero(3);for(r=0;r<b;r++){if(null===(t=n.create(e[r]).to3D()))return null;if(p.push(t),(i=p.length)>2){if(f=p[i-1].elements,u=p[i-2].elements,l=p[i-3].elements,a=n.create([(f[1]-u[1])*(l[2]-u[2])-(f[2]-u[2])*(l[1]-u[1]),(f[2]-u[2])*(l[0]-u[0])-(f[0]-u[0])*(l[2]-u[2]),(f[0]-u[0])*(l[1]-u[1])-(f[1]-u[1])*(l[0]-u[0])]).toUnitVector(),i>3&&null!==(h=a.angleFrom(d))&&!(Math.abs(h)<=s.precision||Math.abs(h-Math.PI)<=s.precision))return null;m=m.add(a),d=a}}return f=p[1].elements,u=p[0].elements,l=p[i-1].elements,c=p[i-2].elements,m=m.add(n.create([(f[1]-u[1])*(l[2]-u[2])-(f[2]-u[2])*(l[1]-u[1]),(f[2]-u[2])*(l[0]-u[0])-(f[0]-u[0])*(l[2]-u[2]),(f[0]-u[0])*(l[1]-u[1])-(f[1]-u[1])*(l[0]-u[0])]).toUnitVector()).add(n.create([(u[1]-l[1])*(c[2]-l[2])-(u[2]-l[2])*(c[1]-l[1]),(u[2]-l[2])*(c[0]-l[0])-(u[0]-l[0])*(c[2]-l[2]),(u[0]-l[0])*(c[1]-l[1])-(u[1]-l[1])*(c[0]-l[0])]).toUnitVector()),o.create(p[0],m)},e.exports=o},function(e,r,t){"use strict";var n=u(t(11)),i=u(t(12)),a=u(t(20)),s=u(t(21)),o=t(22),f=u(t(23));function u(e){return e&&e.__esModule?e:{default:e}}const l=(0,f.default)("wsavc");e.exports=class extends o.EventEmitter{constructor(e,r,t){super(),this.canvas=e,this.canvastype=r,this.now=(new Date).getTime(),this.avc=new n.default,this.ws,this.pktnum=0,this.avc.onPictureDecoded=((e,r,t,...n)=>this.initCanvas(r,t,[e,r,t,...n]))}decode(e){let r="invalid frame";e.length>4&&(101===e[4]?r="I frame":65===e[4]?r="P frame":103===e[4]?r="SPS":104===e[4]&&(r="PPS")),l(`Passed ${r} to decoder ${31&e[4]}`),this.avc.decode(e)}connect(e){void 0!==this.ws&&(this.ws.close(),delete this.ws),this.ws=new WebSocket(e),this.ws.binaryType="arraybuffer",this.ws.onopen=(()=>{l("Connected to "+e),this.emit("connected",e)});let r=[];this.ws.onmessage=(e=>{if("string"==typeof e.data)return this.cmd(JSON.parse(e.data));this.pktnum++;const t=new Uint8Array(e.data);r.push(t),this.pktnum%10==0&&this.emit("packet_recv",this.pktnum)});let t=!0;const n=function(){if(!t)return;if(r.length>30){l("Dropping frames",r.length);const e=r.findIndex(e=>7==(31&e[4]));e>=0&&(r=r.slice(e))}const e=r.shift();e&&this.decode(e),requestAnimationFrame(n)}.bind(this);return n(),this.ws.onclose=(()=>{t=!1,this.emit("disconnected"),l("WSAvcPlayer: Connection closed")}),this.ws}initCanvas(e,r,t){const n=new("webgl"===this.canvastype||"YUVWebGLCanvas"===this.canvastype?i.default:a.default)(this.canvas,new s.default(e,r));if(this.avc.onPictureDecoded=((t,i,a,...s)=>i!==e||a!==r?this.initCanvas(i,a,[t,i,a,...s]):n.decode(t,i,a,...s)),t)return n.decode(...t)}cmd(e){switch(l("Incoming request",e),e.action){case"initalize":{const{width:r,height:t}=e.payload;return this.emit("initalized",e.payload)}default:return this.emit(e.action,e.payload)}}disconnect(){this.ws.close()}send(e,r){return this.ws.send(JSON.stringify({action:e,payload:r}))}},e.exports.debug=f.default},function(module,exports,__webpack_require__){"use strict";(function(process,__dirname){var __WEBPACK_AMD_DEFINE_FACTORY__,__WEBPACK_AMD_DEFINE_ARRAY__,__WEBPACK_AMD_DEFINE_RESULT__;!function(e,r){__WEBPACK_AMD_DEFINE_ARRAY__=[],void 0===(__WEBPACK_AMD_DEFINE_RESULT__="function"==typeof(__WEBPACK_AMD_DEFINE_FACTORY__=r)?__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports,__WEBPACK_AMD_DEFINE_ARRAY__):__WEBPACK_AMD_DEFINE_FACTORY__)||(module.exports=__WEBPACK_AMD_DEFINE_RESULT__)}(0,function(){var global;function initglobal(){(global=this)||("undefined"!=typeof window?global=window:"undefined"!=typeof self&&(global=self))}function error(e){console.error(e),console.trace()}function assert(e,r){e||error(r)}return initglobal(),function(){var getModule=function(_broadwayOnHeadersDecoded,_broadwayOnPictureDecoded){var window=this;window._broadwayOnHeadersDecoded=_broadwayOnHeadersDecoded,window._broadwayOnPictureDecoded=_broadwayOnPictureDecoded;var Module={print:function(e){console.log("stdout: "+e)},printErr:function(e){console.log("stderr: "+e)}};function d(e){throw e}var g=void 0,i=!0,k=null,m=!1,p;function n(){return function(){}}p||(p=eval("(function() { try { return Module || {} } catch(e) { return {} } })()"));var aa={},r;for(r in p)p.hasOwnProperty(r)&&(aa[r]=p[r]);var t="object"==typeof process&&!1,ba="object"==typeof window,ca="function"==typeof importScripts,da=!ba&&!t&&!ca;if(t){p.print||(p.print=function(e){process.stdout.write(e+"\n")}),p.printErr||(p.printErr=function(e){process.stderr.write(e+"\n")});var fa=null("fs"),ga=null("path");p.read=function(e,r){e=ga.normalize(e);var t=fa.readFileSync(e);return!t&&e!=ga.resolve(e)&&(e=path.join(__dirname,"..","src",e),t=fa.readFileSync(e)),t&&!r&&(t=t.toString()),t},p.readBinary=function(e){return p.read(e,i)},p.load=function(e){ha(read(e))},p.thisProgram=1<process.argv.length?process.argv[1].replace(/\\/g,"/"):"unknown-program",p.arguments=process.argv.slice(2),void 0!==module&&(module.exports=p),process.on("uncaughtException",function(e){e instanceof ia||d(e)})}else da?(p.print||(p.print=print),"undefined"!=typeof printErr&&(p.printErr=printErr),p.read="undefined"!=typeof read?read:function(){d("no read() available (jsc?)")},p.readBinary=function(e){return"function"==typeof readbuffer?new Uint8Array(readbuffer(e)):(w("object"==typeof(e=read(e,"binary"))),e)},"undefined"!=typeof scriptArgs?p.arguments=scriptArgs:void 0!==arguments&&(p.arguments=arguments),this.Module=p,eval("if (typeof gc === 'function' && gc.toString().indexOf('[native code]') > 0) var gc = undefined")):ba||ca?(p.read=function(e){var r=new XMLHttpRequest;return r.open("GET",e,m),r.send(k),r.responseText},void 0!==arguments&&(p.arguments=arguments),"undefined"!=typeof console?(p.print||(p.print=function(e){console.log(e)}),p.printErr||(p.printErr=function(e){console.log(e)})):p.print||(p.print=n()),ba?window.Module=p:p.load=importScripts):d("Unknown runtime environment. Where are we?");function ha(e){eval.call(k,e)}for(r in!p.load&&p.read&&(p.load=function(e){ha(p.read(e))}),p.print||(p.print=n()),p.printErr||(p.printErr=p.print),p.arguments||(p.arguments=[]),p.thisProgram||(p.thisProgram="./this.program"),p.print=p.print,p.fa=p.printErr,p.preRun=[],p.postRun=[],aa)aa.hasOwnProperty(r)&&(p[r]=aa[r]);var z={Yd:function(e){ja=e},xd:function(){return ja},Tb:function(){return y},Sb:function(e){y=e},oc:function(e){switch(e){case"i1":case"i8":return 1;case"i16":return 2;case"i32":return 4;case"i64":return 8;case"float":return 4;case"double":return 8;default:return"*"===e[e.length-1]?z.ia:"i"===e[0]?(w(0==(e=parseInt(e.substr(1)))%8),e/8):0}},vd:function(e){return Math.max(z.oc(e),z.ia)},Qf:16,ng:function(e,r,t){return t||"i64"!=e&&"double"!=e?e?Math.min(r||(e?z.vd(e):0),z.ia):Math.min(r,8):8},Fa:function(e,r,t){return t&&t.length?(t.splice||(t=Array.prototype.slice.call(t)),t.splice(0,0,r),p["dynCall_"+e].apply(k,t)):p["dynCall_"+e].call(k,r)},eb:[],Vc:function(e){for(var r=0;r<z.eb.length;r++)if(!z.eb[r])return z.eb[r]=e,2*(1+r);d("Finished up all reserved function pointers. Use a higher value for RESERVED_FUNCTION_POINTERS.")},Sd:function(e){z.eb[(e-2)/2]=k},og:function(a,b){z.wb||(z.wb={});var c=z.wb[a];if(c)return c;for(var c=[],e=0;e<b;e++)c.push(String.fromCharCode(36)+e);e=ka(a),'"'===e[0]&&(e.indexOf('"',1)===e.length-1?e=e.substr(1,e.length-2):A("invalid EM_ASM input |"+e+"|. Please use EM_ASM(..code..) (no quotes) or EM_ASM({ ..code($0).. }, input) (to input values)"));try{var f=eval("(function(Module, FS) { return function("+c.join(",")+"){ "+e+" } })")(p,void 0!==B?B:k)}catch(r){p.fa("error in executing inline EM_ASM code: "+r+" on: \n\n"+e+"\n\nwith args |"+c+"| (make sure to use the right one out of EM_ASM, EM_ASM_ARGS, etc.)"),d(r)}return z.wb[a]=f},Aa:function(e){z.Aa.Rb||(z.Aa.Rb={}),z.Aa.Rb[e]||(z.Aa.Rb[e]=1,p.fa(e))},Cb:{},rg:function(e,r){w(r),z.Cb[r]||(z.Cb[r]={});var t=z.Cb[r];return t[e]||(t[e]=function(){return z.Fa(r,e,arguments)}),t[e]},Da:function(){var e=[],r=0;this.nb=function(t){if(t&=255,0==e.length)return 0==(128&t)?String.fromCharCode(t):(e.push(t),r=192==(224&t)?1:224==(240&t)?2:3,"");if(r&&(e.push(t),0<--r))return"";t=e[0];var n=e[1],i=e[2],a=e[3];return 2==e.length?t=String.fromCharCode((31&t)<<6|63&n):3==e.length?t=String.fromCharCode((15&t)<<12|(63&n)<<6|63&i):(t=(7&t)<<18|(63&n)<<12|(63&i)<<6|63&a,t=String.fromCharCode(55296+((t-65536)/1024|0),(t-65536)%1024+56320)),e.length=0,t},this.Ac=function(e){e=unescape(encodeURIComponent(e));for(var r=[],t=0;t<e.length;t++)r.push(e.charCodeAt(t));return r}},pg:function(){d("You must build with -s RETAIN_COMPILER_SETTINGS=1 for Runtime.getCompilerSetting or emscripten_get_compiler_setting to work")},pb:function(e){var r=y;return y=(y=y+e|0)+15&-16,r},Ec:function(e){var r=D;return D=(D=D+e|0)+15&-16,r},bb:function(e){var r=E;return(E=(E=E+e|0)+15&-16)>=F&&A("Cannot enlarge memory arrays. Either (1) compile with -s TOTAL_MEMORY=X with X higher than the current value "+F+", (2) compile with ALLOW_MEMORY_GROWTH which adjusts the size at runtime but prevents some optimizations, or (3) set Module.TOTAL_MEMORY before the program runs."),r},ub:function(e,r){return Math.ceil(e/(r||16))*(r||16)},Fg:function(e,r,t){return t?+(e>>>0)+4294967296*+(r>>>0):+(e>>>0)+4294967296*+(0|r)},Pc:8,ia:4,Rf:0};p.Runtime=z,z.addFunction=z.Vc,z.removeFunction=z.Sd;var H=m,la,ma,ja,oa,pa;function w(e,r){e||A("Assertion failed: "+r)}function na(a){var b=p["_"+a];if(!b)try{b=eval("_"+a)}catch(e){}return w(b,"Cannot call unknown function "+a+" (perhaps LLVM optimizations or closure removed it?)"),b}function sa(e,r,t){switch("*"===(t=t||"i8").charAt(t.length-1)&&(t="i32"),t){case"i1":case"i8":I[e>>0]=r;break;case"i16":J[e>>1]=r;break;case"i32":K[e>>2]=r;break;case"i64":ma=[r>>>0,(la=r,1<=+ta(la)?0<la?(0|va(+wa(la/4294967296),4294967295))>>>0:~~+xa((la-+(~~la>>>0))/4294967296)>>>0:0)],K[e>>2]=ma[0],K[e+4>>2]=ma[1];break;case"float":ya[e>>2]=r;break;case"double":za[e>>3]=r;break;default:A("invalid type for setValue: "+t)}}function Aa(e,r){switch("*"===(r=r||"i8").charAt(r.length-1)&&(r="i32"),r){case"i1":case"i8":return I[e>>0];case"i16":return J[e>>1];case"i32":case"i64":return K[e>>2];case"float":return ya[e>>2];case"double":return za[e>>3];default:A("invalid type for setValue: "+r)}return k}!function(){function a(r){return{arguments:(r=r.toString().match(e).slice(1))[0],body:r[1],returnValue:r[2]}}var b={stackSave:function(){z.Tb()},stackRestore:function(){z.Sb()},arrayToC:function(e){var r=z.pb(e.length);return qa(e,r),r},stringToC:function(e){var r=0;return e!==k&&e!==g&&0!==e&&ra(e,r=z.pb(1+(e.length<<2))),r}},c={string:b.stringToC,array:b.arrayToC};pa=function(e,r,t,n){var i=na(e),a=[];e=0;if(n)for(var s=0;s<n.length;s++){var o=c[t[s]];o?(0===e&&(e=z.Tb()),a[s]=o(n[s])):a[s]=n[s]}return t=i.apply(k,a),"string"===r&&(t=ka(t)),0!==e&&z.Sb(e),t};var e=/^function\s*\(([^)]*)\)\s*{\s*([^*]*?)[\s;]*(?:return\s*(.*?)[;\s]*)?}$/,f={},h;for(h in b)b.hasOwnProperty(h)&&(f[h]=a(b[h]));oa=function(b,c,e){var e=e||[],h=na(b),b=e.every(function(e){return"number"===e}),x="string"!==c;if(x&&b)return h;var s=e.map(function(e,r){return"$"+r}),c="(function("+s.join(",")+") {",v=e.length;if(!b)for(var c=c+"var stack = "+f.stackSave.body+";",G=0;G<v;G++){var ua=s[G],ea=e[G];"number"!==ea&&(ea=f[ea+"ToC"],c+="var "+ea.arguments+" = "+ua+";",c+=ea.body+";",c+=ua+"="+ea.returnValue+";")}return e=a(function(){return h}).returnValue,c+="var ret = "+e+"("+s.join(",")+");",x||(e=a(function(){return ka}).returnValue,c+="ret = "+e+"(ret);"),b||(c+=f.stackRestore.body.replace("()","(stack)")+";"),eval(c+"return ret})")}}(),p.cwrap=oa,p.ccall=pa,p.setValue=sa,p.getValue=Aa;var L=2,Ba=4;function M(e,r,t,n){var a,s;"number"==typeof e?(a=i,s=e):(a=m,s=e.length);var o="string"==typeof r?r:k;t=t==Ba?n:[Ca,z.pb,z.Ec,z.bb][t===g?L:t](Math.max(s,o?1:r.length));if(a){for(n=t,w(0==(3&t)),e=t+(-4&s);n<e;n+=4)K[n>>2]=0;for(e=t+s;n<e;)I[n++>>0]=0;return t}if("i8"===o)return e.subarray||e.slice?N.set(e,t):N.set(new Uint8Array(e),t),t;var f,u;for(n=0;n<s;){var l=e[n];"function"==typeof l&&(l=z.sg(l)),0===(a=o||r[n])?n++:("i64"==a&&(a="i32"),sa(t+n,l,a),u!==a&&(f=z.oc(a),u=a),n+=f)}return t}function ka(e,r){if(0===r||!e)return"";for(var t,n=m,a=0;;){if(128<=(t=N[e+a>>0]))n=i;else if(0==t&&!r)break;if(a++,r&&a==r)break}r||(r=a);var s="";if(!n){for(;0<r;)t=String.fromCharCode.apply(String,N.subarray(e,e+Math.min(r,1024))),s=s?s+t:t,e+=1024,r-=1024;return s}for(n=new z.Da,a=0;a<r;a++)t=N[e+a>>0],s+=n.nb(t);return s}function Da(e){var r=!!p.___cxa_demangle;if(r)try{var t=Ca(e.length);ra(e.substr(1),t);var n=Ca(4),a=p.___cxa_demangle(t,0,0,n);if(0===Aa(n,"i32")&&a)return ka(a)}catch(e){}finally{t&&Ea(t),n&&Ea(n),a&&Ea(a)}var s=3,o={v:"void",b:"bool",c:"char",s:"short",i:"int",l:"long",f:"float",d:"double",w:"wchar_t",a:"signed char",h:"unsigned char",t:"unsigned short",j:"unsigned int",m:"unsigned long",x:"long long",y:"unsigned long long",z:"..."},f=[],u=i;t=e;try{if("Object._main"==e||"_main"==e)return"main()";if("number"==typeof e&&(e=ka(e)),"_"!==e[0]||"_"!==e[1]||"Z"!==e[2])return e;switch(e[3]){case"n":return"operator new()";case"d":return"operator delete()"}t=function r(t,n,a){n=n||1/0;var l,c="",h=[];if("N"===e[s]){for("K"===e[++s]&&s++,l=[];"E"!==e[s];)if("S"===e[s]){s++;var b=e.indexOf("_",s);l.push(f[e.substring(s,b)||0]||"?"),s=b+1}else if("C"===e[s])l.push(l[l.length-1]),s+=2;else{var p=(b=parseInt(e.substr(s))).toString().length;if(!b||!p){s--;break}var w=e.substr(s+p,b);l.push(w),f.push(w),s+=p+b}if(s++,l=l.join("::"),0==--n)return t?[l]:l}else("K"===e[s]||u&&"L"===e[s])&&s++,(b=parseInt(e.substr(s)))&&(p=b.toString().length,l=e.substr(s+p,b),s+=p+b);u=m,"I"===e[s]?(s++,b=r(i),c+=(p=r(i,1,i))[0]+" "+l+"<"+b.join(", ")+">"):c=l;e:for(;s<e.length&&0<n--;)if(l=e[s++],l in o)h.push(o[l]);else switch(l){case"P":h.push(r(i,1,i)[0]+"*");break;case"R":h.push(r(i,1,i)[0]+"&");break;case"L":s++,b=e.indexOf("E",s)-s,h.push(e.substr(s,b)),s+=b+2;break;case"A":b=parseInt(e.substr(s)),s+=b.toString().length,"_"!==e[s]&&d("?"),s++,h.push(r(i,1,i)[0]+" ["+b+"]");break;case"E":break e;default:c+="?"+l;break e}return!a&&1===h.length&&"void"===h[0]&&(h=[]),t?(c&&h.push(c+"?"),h):c+"("+h.join(", ")+")"}()}catch(e){t+="?"}return 0<=t.indexOf("?")&&!r&&z.Aa("warning: a problem occurred in builtin C++ name demangling; build with  -s DEMANGLE_SUPPORT=1  to link in libcxxabi demangling"),t}function Fa(){var e;e:{if(!(e=Error()).stack){try{d(Error(0))}catch(r){e=r}if(!e.stack){e="(no stack trace available)";break e}}e=e.stack.toString()}return e.replace(/__Z[\w\d_]+/g,function(e){var r=Da(e);return e===r?e:e+" ["+r+"]"})}p.ALLOC_NORMAL=0,p.ALLOC_STACK=1,p.ALLOC_STATIC=L,p.ALLOC_DYNAMIC=3,p.ALLOC_NONE=Ba,p.allocate=M,p.Pointer_stringify=ka,p.UTF16ToString=function(e){for(var r=0,t="";;){var n=J[e+2*r>>1];if(0==n)return t;++r,t+=String.fromCharCode(n)}},p.stringToUTF16=function(e,r){for(var t=0;t<e.length;++t)J[r+2*t>>1]=e.charCodeAt(t);J[r+2*e.length>>1]=0},p.UTF32ToString=function(e){for(var r=0,t="";;){var n=K[e+4*r>>2];if(0==n)return t;++r,65536<=n?(n-=65536,t+=String.fromCharCode(55296|n>>10,56320|1023&n)):t+=String.fromCharCode(n)}},p.stringToUTF32=function(e,r){for(var t=0,n=0;n<e.length;++n){if(55296<=(i=e.charCodeAt(n))&&57343>=i)var i=65536+((1023&i)<<10)|1023&e.charCodeAt(++n);K[r+4*t>>2]=i,++t}K[r+4*t>>2]=0},p.stackTrace=function(){return Fa()};for(var I,N,J,Ga,K,Ha,ya,za,Ia=0,D=0,Ja=0,y=0,Ka=0,La=0,E=0,Ma=p.TOTAL_STACK||5242880,F=p.TOTAL_MEMORY||52428800,O=65536;O<F||O<2*Ma;)O=16777216>O?2*O:O+16777216;O!==F&&(p.fa("increasing TOTAL_MEMORY to "+O+" to be compliant with the asm.js spec"),F=O),w("undefined"!=typeof Int32Array&&"undefined"!=typeof Float64Array&&!!new Int32Array(1).subarray&&!!new Int32Array(1).set,"JS engine does not provide full typed array support");var Q=new ArrayBuffer(F);function Na(e){for(;0<e.length;){var r=e.shift();if("function"==typeof r)r();else{var t=r.ja;"number"==typeof t?r.Xa===g?z.Fa("v",t):z.Fa("vi",t,[r.Xa]):t(r.Xa===g?k:r.Xa)}}}I=new Int8Array(Q),J=new Int16Array(Q),K=new Int32Array(Q),N=new Uint8Array(Q),Ga=new Uint16Array(Q),Ha=new Uint32Array(Q),ya=new Float32Array(Q),za=new Float64Array(Q),K[0]=255,w(255===N[0]&&0===N[3],"Typed arrays 2 must be run on a little-endian system"),p.HEAP=g,p.buffer=Q,p.HEAP8=I,p.HEAP16=J,p.HEAP32=K,p.HEAPU8=N,p.HEAPU16=Ga,p.HEAPU32=Ha,p.HEAPF32=ya,p.HEAPF64=za;var Oa=[],R=[],Pa=[],Qa=[],Ra=[],Sa=m;function Ta(e){Oa.unshift(e)}function Ua(e){Ra.unshift(e)}function Va(e,r,t){return e=(new z.Da).Ac(e),t&&(e.length=t),r||e.push(0),e}function ra(e,r,t){for(e=Va(e,t),t=0;t<e.length;)I[r+t>>0]=e[t],t+=1}function qa(e,r){for(var t=0;t<e.length;t++)I[r+t>>0]=e[t]}p.addOnPreRun=p.Xf=Ta,p.addOnInit=p.Uf=function(e){R.unshift(e)},p.addOnPreMain=p.Wf=function(e){Pa.unshift(e)},p.addOnExit=p.Tf=function(e){Qa.unshift(e)},p.addOnPostRun=p.Vf=Ua,p.intArrayFromString=Va,p.intArrayToString=function(e){for(var r=[],t=0;t<e.length;t++){var n=e[t];255<n&&(n&=255),r.push(String.fromCharCode(n))}return r.join("")},p.writeStringToMemory=ra,p.writeArrayToMemory=qa,p.writeAsciiToMemory=function(e,r,t){for(var n=0;n<e.length;n++)I[r+n>>0]=e.charCodeAt(n);t||(I[r+e.length>>0]=0)},Math.imul&&-5===Math.imul(4294967295,5)||(Math.imul=function(e,r){var t=65535&e,n=65535&r;return t*n+((e>>>16)*n+t*(r>>>16)<<16)|0}),Math.vg=Math.imul;var ta=Math.abs,xa=Math.ceil,wa=Math.floor,va=Math.min,S=0,Wa=k,Xa=k;function Ya(){S++,p.monitorRunDependencies&&p.monitorRunDependencies(S)}function Za(){if(S--,p.monitorRunDependencies&&p.monitorRunDependencies(S),0==S&&(Wa!==k&&(clearInterval(Wa),Wa=k),Xa)){var e=Xa;Xa=k,e()}}p.addRunDependency=Ya,p.removeRunDependency=Za,p.preloadedImages={},p.preloadedAudios={};var T=k,Ia=8,D=Ia+7808;R.push(),M([0,0,0,0,0,0,1,1,1,1,1,1,2,2,2,2,2,2,3,3,3,3,3,3,4,4,4,4,4,4,5,5,5,5,5,5,6,6,6,6,6,6,7,7,7,7,7,7,8,8,8,8,0,0,0,0,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,4,5,0,1,2,3,0,0,0,0,10,0,0,0,13,0,0,0,16,0,0,0,11,0,0,0,14,0,0,0,18,0,0,0,13,0,0,0,16,0,0,0,20,0,0,0,14,0,0,0,18,0,0,0,23,0,0,0,16,0,0,0,20,0,0,0,25,0,0,0,18,0,0,0,23,0,0,0,29,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,6,0,0,0,7,0,0,0,8,0,0,0,9,0,0,0,10,0,0,0,11,0,0,0,12,0,0,0,13,0,0,0,14,0,0,0,15,0,0,0,16,0,0,0,17,0,0,0,18,0,0,0,19,0,0,0,20,0,0,0,21,0,0,0,22,0,0,0,23,0,0,0,24,0,0,0,25,0,0,0,26,0,0,0,27,0,0,0,28,0,0,0,29,0,0,0,29,0,0,0,30,0,0,0,31,0,0,0,32,0,0,0,32,0,0,0,33,0,0,0,34,0,0,0,34,0,0,0,35,0,0,0,35,0,0,0,36,0,0,0,36,0,0,0,37,0,0,0,37,0,0,0,37,0,0,0,38,0,0,0,38,0,0,0,38,0,0,0,39,0,0,0,39,0,0,0,39,0,0,0,39,0,0,0,1,0,0,0,2,0,0,0,4,0,0,0,8,0,0,0,16,0,0,0,32,0,0,0,64,0,0,0,128,0,0,0,1,0,0,0,1,0,0,0,2,0,0,0,2,0,0,0,3,0,0,0,3,0,0,0,3,0,0,0,3,0,0,0,0,0,0,0,1,0,0,0,4,0,0,0,5,0,0,0,2,0,0,0,3,0,0,0,6,0,0,0,7,0,0,0,8,0,0,0,9,0,0,0,12,0,0,0,13,0,0,0,10,0,0,0,11,0,0,0,14,0,0,0,15,0,0,0,47,31,15,0,23,27,29,30,7,11,13,14,39,43,45,46,16,3,5,10,12,19,21,26,28,35,37,42,44,1,2,4,8,17,18,20,24,6,9,22,25,32,33,34,36,40,38,41,0,16,1,2,4,8,32,3,5,10,12,15,47,7,11,13,14,6,9,31,35,37,42,44,33,34,36,40,39,43,45,46,17,18,20,24,19,21,26,28,23,27,29,30,22,25,38,41,17,1,0,0,0,0,0,0,34,18,1,1,0,0,0,0,50,34,18,2,0,0,0,0,67,51,34,34,18,18,2,2,83,67,51,35,18,18,2,2,19,35,67,51,99,83,2,2,0,0,101,85,68,68,52,52,35,35,35,35,19,19,19,19,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,249,233,217,200,200,184,184,167,167,167,167,151,151,151,151,134,134,134,134,134,134,134,134,118,118,118,118,118,118,118,118,230,214,198,182,165,165,149,149,132,132,132,132,116,116,116,116,100,100,100,100,84,84,84,84,67,67,67,67,67,67,67,67,51,51,51,51,51,51,51,51,35,35,35,35,35,35,35,35,19,19,19,19,19,19,19,19,3,3,3,3,3,3,3,3,214,182,197,197,165,165,149,149,132,132,132,132,84,84,84,84,68,68,68,68,4,4,4,4,115,115,115,115,115,115,115,115,99,99,99,99,99,99,99,99,51,51,51,51,51,51,51,51,35,35,35,35,35,35,35,35,19,19,19,19,19,19,19,19,197,181,165,5,148,148,116,116,52,52,36,36,131,131,131,131,99,99,99,99,83,83,83,83,67,67,67,67,19,19,19,19,181,149,164,164,132,132,36,36,20,20,4,4,115,115,115,115,99,99,99,99,83,83,83,83,67,67,67,67,51,51,51,51,166,6,21,21,132,132,132,132,147,147,147,147,147,147,147,147,115,115,115,115,115,115,115,115,99,99,99,99,99,99,99,99,83,83,83,83,83,83,83,83,67,67,67,67,67,67,67,67,51,51,51,51,51,51,51,51,35,35,35,35,35,35,35,35,150,6,21,21,116,116,116,116,131,131,131,131,131,131,131,131,99,99,99,99,99,99,99,99,67,67,67,67,67,67,67,67,51,51,51,51,51,51,51,51,35,35,35,35,35,35,35,35,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,134,6,37,37,20,20,20,20,115,115,115,115,115,115,115,115,99,99,99,99,99,99,99,99,51,51,51,51,51,51,51,51,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,82,66,66,66,66,66,66,66,66,66,66,66,66,66,66,66,66,22,6,117,117,36,36,36,36,83,83,83,83,83,83,83,83,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,66,66,66,66,66,66,66,66,66,66,66,66,66,66,66,66,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,50,21,5,100,100,35,35,35,35,82,82,82,82,82,82,82,82,66,66,66,66,66,66,66,66,50,50,50,50,50,50,50,50,4,20,35,35,51,51,83,83,65,65,65,65,65,65,65,65,4,20,67,67,34,34,34,34,49,49,49,49,49,49,49,49,3,19,50,50,33,33,33,33,2,18,33,33,0,0,0,0,0,0,0,0,0,0,102,32,38,16,6,8,101,24,101,24,67,16,67,16,67,16,67,16,67,16,67,16,67,16,67,16,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,34,8,0,0,0,0,0,0,0,0,106,64,74,48,42,40,10,32,105,56,105,56,73,40,73,40,41,32,41,32,9,24,9,24,104,48,104,48,104,48,104,48,72,32,72,32,72,32,72,32,40,24,40,24,40,24,40,24,8,16,8,16,8,16,8,16,103,40,103,40,103,40,103,40,103,40,103,40,103,40,103,40,71,24,71,24,71,24,71,24,71,24,71,24,71,24,71,24,110,96,78,88,46,80,14,80,110,88,78,80,46,72,14,72,13,64,13,64,77,72,77,72,45,64,45,64,13,56,13,56,109,80,109,80,77,64,77,64,45,56,45,56,13,48,13,48,107,72,107,72,107,72,107,72,107,72,107,72,107,72,107,72,75,56,75,56,75,56,75,56,75,56,75,56,75,56,75,56,43,48,43,48,43,48,43,48,43,48,43,48,43,48,43,48,11,40,11,40,11,40,11,40,11,40,11,40,11,40,11,40,0,0,0,0,47,104,47,104,16,128,80,128,48,128,16,120,112,128,80,120,48,120,16,112,112,120,80,112,48,112,16,104,111,112,111,112,79,104,79,104,47,96,47,96,15,96,15,96,111,104,111,104,79,96,79,96,47,88,47,88,15,88,15,88,0,0,0,0,0,0,0,0,102,56,70,32,38,32,6,16,102,48,70,24,38,24,6,8,101,40,101,40,37,16,37,16,100,32,100,32,100,32,100,32,100,24,100,24,100,24,100,24,67,16,67,16,67,16,67,16,67,16,67,16,67,16,67,16,0,0,0,0,0,0,0,0,105,72,73,56,41,56,9,48,8,40,8,40,72,48,72,48,40,48,40,48,8,32,8,32,103,64,103,64,103,64,103,64,71,40,71,40,71,40,71,40,39,40,39,40,39,40,39,40,7,24,7,24,7,24,7,24,0,0,0,0,109,120,109,120,110,128,78,128,46,128,14,128,46,120,14,120,78,120,46,112,77,112,77,112,13,112,13,112,109,112,109,112,77,104,77,104,45,104,45,104,13,104,13,104,109,104,109,104,77,96,77,96,45,96,45,96,13,96,13,96,12,88,12,88,12,88,12,88,76,88,76,88,76,88,76,88,44,88,44,88,44,88,44,88,12,80,12,80,12,80,12,80,108,96,108,96,108,96,108,96,76,80,76,80,76,80,76,80,44,80,44,80,44,80,44,80,12,72,12,72,12,72,12,72,107,88,107,88,107,88,107,88,107,88,107,88,107,88,107,88,75,72,75,72,75,72,75,72,75,72,75,72,75,72,75,72,43,72,43,72,43,72,43,72,43,72,43,72,43,72,43,72,11,64,11,64,11,64,11,64,11,64,11,64,11,64,11,64,107,80,107,80,107,80,107,80,107,80,107,80,107,80,107,80,75,64,75,64,75,64,75,64,75,64,75,64,75,64,75,64,43,64,43,64,43,64,43,64,43,64,43,64,43,64,43,64,11,56,11,56,11,56,11,56,11,56,11,56,11,56,11,56,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,6,24,70,56,38,56,6,16,102,72,70,48,38,48,6,8,37,40,37,40,69,40,69,40,37,32,37,32,69,32,69,32,37,24,37,24,101,64,101,64,69,24,69,24,37,16,37,16,100,56,100,56,100,56,100,56,100,48,100,48,100,48,100,48,100,40,100,40,100,40,100,40,100,32,100,32,100,32,100,32,100,24,100,24,100,24,100,24,68,16,68,16,68,16,68,16,36,8,36,8,36,8,36,8,4,0,4,0,4,0,4,0,0,0,10,128,106,128,74,128,42,128,10,120,106,120,74,120,42,120,10,112,106,112,74,112,42,112,10,104,41,104,41,104,9,96,9,96,73,104,73,104,41,96,41,96,9,88,9,88,105,104,105,104,73,96,73,96,41,88,41,88,9,80,9,80,104,96,104,96,104,96,104,96,72,88,72,88,72,88,72,88,40,80,40,80,40,80,40,80,8,72,8,72,8,72,8,72,104,88,104,88,104,88,104,88,72,80,72,80,72,80,72,80,40,72,40,72,40,72,40,72,8,64,8,64,8,64,8,64,7,56,7,56,7,56,7,56,7,56,7,56,7,56,7,56,7,48,7,48,7,48,7,48,7,48,7,48,7,48,7,48,71,72,71,72,71,72,71,72,71,72,71,72,71,72,71,72,7,40,7,40,7,40,7,40,7,40,7,40,7,40,7,40,103,80,103,80,103,80,103,80,103,80,103,80,103,80,103,80,71,64,71,64,71,64,71,64,71,64,71,64,71,64,71,64,39,64,39,64,39,64,39,64,39,64,39,64,39,64,39,64,7,32,7,32,7,32,7,32,7,32,7,32,7,32,7,32,6,8,38,8,0,0,6,0,6,16,38,16,70,16,0,0,6,24,38,24,70,24,102,24,6,32,38,32,70,32,102,32,6,40,38,40,70,40,102,40,6,48,38,48,70,48,102,48,6,56,38,56,70,56,102,56,6,64,38,64,70,64,102,64,6,72,38,72,70,72,102,72,6,80,38,80,70,80,102,80,6,88,38,88,70,88,102,88,6,96,38,96,70,96,102,96,6,104,38,104,70,104,102,104,6,112,38,112,70,112,102,112,6,120,38,120,70,120,102,120,6,128,38,128,70,128,102,128,0,0,67,16,2,0,2,0,33,8,33,8,33,8,33,8,103,32,103,32,72,32,40,32,71,24,71,24,39,24,39,24,6,32,6,32,6,32,6,32,6,24,6,24,6,24,6,24,6,16,6,16,6,16,6,16,102,24,102,24,102,24,102,24,38,16,38,16,38,16,38,16,6,8,6,8,6,8,6,8,3,0,0,0,15,0,0,0,1,0,0,0,10,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,4,0,0,0,0,0,0,0,7,0,0,0,4,0,0,0,2,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,8,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,12,0,0,0,3,0,0,0,19,0,0,0,1,0,0,0,18,0,0,0,0,0,0,0,17,0,0,0,4,0,0,0,16,0,0,0,3,0,0,0,23,0,0,0,1,0,0,0,22,0,0,0,0,0,0,0,21,0,0,0,4,0,0,0,20,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,4,0,0,0,1,0,0,0,255,0,0,0,4,0,0,0,1,0,0,0,15,0,0,0,2,0,0,0,10,0,0,0,4,0,0,0,5,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,9,0,0,0,255,0,0,0,12,0,0,0,4,0,0,0,7,0,0,0,255,0,0,0,2,0,0,0,4,0,0,0,13,0,0,0,255,0,0,0,8,0,0,0,1,0,0,0,19,0,0,0,2,0,0,0,18,0,0,0,4,0,0,0,17,0,0,0,255,0,0,0,16,0,0,0,1,0,0,0,23,0,0,0,2,0,0,0,22,0,0,0,4,0,0,0,21,0,0,0,255,0,0,0,20,0,0,0,1,0,0,0,10,0,0,0,1,0,0,0,11,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,1,0,0,0,1,0,0,0,14,0,0,0,1,0,0,0,15,0,0,0,4,0,0,0,4,0,0,0,4,0,0,0,5,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,8,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,7,0,0,0,4,0,0,0,12,0,0,0,4,0,0,0,13,0,0,0,1,0,0,0,18,0,0,0,1,0,0,0,19,0,0,0,4,0,0,0,16,0,0,0,4,0,0,0,17,0,0,0,1,0,0,0,22,0,0,0,1,0,0,0,23,0,0,0,4,0,0,0,20,0,0,0,4,0,0,0,21,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,4,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,8,0,0,0,0,0,0,0,15,0,0,0,4,0,0,0,10,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,12,0,0,0,4,0,0,0,11,0,0,0,4,0,0,0,14,0,0,0,0,0,0,0,17,0,0,0,4,0,0,0,16,0,0,0,0,0,0,0,19,0,0,0,4,0,0,0,18,0,0,0,0,0,0,0,21,0,0,0,4,0,0,0,20,0,0,0,0,0,0,0,23,0,0,0,4,0,0,0,22,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,8,0,0,0,12,0,0,0,8,0,0,0,12,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,8,0,0,0,12,0,0,0,8,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,4,0,0,0,8,0,0,0,8,0,0,0,12,0,0,0,12,0,0,0,8,0,0,0,8,0,0,0,12,0,0,0,12,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,255,3,0,0,0,15,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,3,0,0,0,15,0,0,0,0,0,0,0,5,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,3,0,0,0,15,0,0,0,1,0,0,0,10,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,3,0,0,0,15,0,0,0,1,0,0,0,10,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,4,0,0,0,1,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,4,0,0,0,0,0,0,0,7,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,0,0,0,0,13,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,4,0,0,0,2,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,4,0,0,0,2,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,8,0,0,0,4,0,0,0,3,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,9,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,12,0,0,0,1,0,0,0,14,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,14,0,0,0,255,0,0,0,4,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,11,0,0,0,1,0,0,0,14,0,0,0,4,0,0,0,1,0,0,0,255,0,0,0,4,0,0,0,2,0,0,0,10,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,2,0,0,0,10,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,15,0,0,0,2,0,0,0,10,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,15,0,0,0,2,0,0,0,10,0,0,0,4,0,0,0,5,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,6,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,6,0,0,0,255,0,0,0,12,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,9,0,0,0,255,0,0,0,12,0,0,0,255,0,0,0,2,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,2,0,0,0,255,0,0,0,8,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,7,0,0,0,255,0,0,0,2,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,7,0,0,0,255,0,0,0,2,0,0,0,4,0,0,0,13,0,0,0,255,0,0,0,8,0,0,0,1,0,0,0,10,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,10,0,0,0,4,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,10,0,0,0,1,0,0,0,11,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,10,0,0,0,1,0,0,0,11,0,0,0,4,0,0,0,0,0,0,0,4,0,0,0,1,0,0,0,1,0,0,0,14,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,14,0,0,0,4,0,0,0,4,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,14,0,0,0,1,0,0,0,15,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,1,0,0,0,14,0,0,0,1,0,0,0,15,0,0,0,4,0,0,0,4,0,0,0,4,0,0,0,5,0,0,0,4,0,0,0,2,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,8,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,3,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,8,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,6,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,12,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,7,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,6,0,0,0,4,0,0,0,7,0,0,0,4,0,0,0,12,0,0,0,4,0,0,0,13,0,0,0,0,0,0,0,5,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,0,0,0,0,7,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,5,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,7,0,0,0,4,0,0,0,2,0,0,0,4,0,0,0,1,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,3,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,4,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,1,0,0,0,4,0,0,0,4,0,0,0,4,0,0,0,3,0,0,0,4,0,0,0,6,0,0,0,0,0,0,0,13,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,0,0,0,0,15,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,8,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,0,0,0,0,13,0,0,0,4,0,0,0,8,0,0,0,0,0,0,0,15,0,0,0,4,0,0,0,10,0,0,0,4,0,0,0,9,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,11,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,12,0,0,0,255,0,0,0,0,0,0,0,255,0,0,0,0,0,0,0,4,0,0,0,9,0,0,0,4,0,0,0,12,0,0,0,4,0,0,0,11,0,0,0,4,0,0,0,14,0,0,0,0,0,0,0,1,0,0,0,2,0,0,0,3,0,0,0,4,0,0,0,5,0,0,0,6,0,0,0,7,0,0,0,8,0,0,0,9,0,0,0,10,0,0,0,11,0,0,0,12,0,0,0,13,0,0,0,14,0,0,0,15,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,5,6,7,8,9,10,12,13,15,17,20,22,25,28,32,36,40,45,50,56,63,71,80,90,101,113,127,144,162,182,203,226,255,255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,2,2,3,3,3,3,4,4,4,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,14,14,15,15,16,16,17,17,18,18,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,1,1,2,1,1,2,1,1,2,1,2,3,1,2,3,2,2,3,2,2,4,2,3,4,2,3,4,3,3,5,3,4,6,3,4,6,4,5,7,4,5,8,4,6,9,5,7,10,6,8,11,6,8,13,7,10,14,8,11,16,9,12,18,10,13,20,11,15,23,13,17,25,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,68,69,67,79,68,69,82,32,73,78,73,84,73,65,76,73,90,65,84,73,79,78,32,70,65,73,76,69,68,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],"i8",Ba,z.Pc);var $a=z.ub(M(12,"i8",L),8);w(0==$a%8);var U={O:1,Q:2,Ef:3,De:4,ha:5,Zb:6,be:7,$e:8,V:9,oe:10,Ca:11,Of:11,Mc:12,qb:13,ye:14,mf:15,ga:16,Xb:17,Oc:18,Qa:19,Sa:20,pa:21,B:22,Ve:23,Lc:24,Nc:25,Lf:26,ze:27,hf:28,Ua:29,Bf:30,Oe:31,uf:32,ve:33,yf:34,df:42,Be:43,pe:44,Fe:45,Ge:46,He:47,Ne:48,Mf:49,Ye:50,Ee:51,te:35,af:37,ge:52,je:53,Pf:54,We:55,ke:56,le:57,ue:35,me:59,kf:60,Ze:61,If:62,jf:63,ef:64,ff:65,Af:66,bf:67,ee:68,Ff:69,qe:70,vf:71,Qe:72,we:73,ie:74,qf:76,he:77,zf:78,Ie:79,Je:80,Me:81,Le:82,Ke:83,lf:38,sb:39,Re:36,rb:40,Ta:95,tf:96,se:104,Xe:105,fe:97,xf:91,of:88,gf:92,Cf:108,Wb:111,ce:98,re:103,Ue:101,Se:100,Jf:110,Ae:112,Yb:113,Jc:115,Hc:114,Ic:89,Pe:90,wf:93,Df:94,de:99,Te:102,Kc:106,Ra:107,Kf:109,Nf:87,xe:122,Gf:116,pf:95,cf:123,Ce:84,rf:75,ne:125,nf:131,sf:130,Hf:86},ab={0:"Success",1:"Not super-user",2:"No such file or directory",3:"No such process",4:"Interrupted system call",5:"I/O error",6:"No such device or address",7:"Arg list too long",8:"Exec format error",9:"Bad file number",10:"No children",11:"No more processes",12:"Not enough core",13:"Permission denied",14:"Bad address",15:"Block device required",16:"Mount device busy",17:"File exists",18:"Cross-device link",19:"No such device",20:"Not a directory",21:"Is a directory",22:"Invalid argument",23:"Too many open files in system",24:"Too many open files",25:"Not a typewriter",26:"Text file busy",27:"File too large",28:"No space left on device",29:"Illegal seek",30:"Read only file system",31:"Too many links",32:"Broken pipe",33:"Math arg out of domain of func",34:"Math result not representable",35:"File locking deadlock error",36:"File or path name too long",37:"No record locks available",38:"Function not implemented",39:"Directory not empty",40:"Too many symbolic links",42:"No message of desired type",43:"Identifier removed",44:"Channel number out of range",45:"Level 2 not synchronized",46:"Level 3 halted",47:"Level 3 reset",48:"Link number out of range",49:"Protocol driver not attached",50:"No CSI structure available",51:"Level 2 halted",52:"Invalid exchange",53:"Invalid request descriptor",54:"Exchange full",55:"No anode",56:"Invalid request code",57:"Invalid slot",59:"Bad font file fmt",60:"Device not a stream",61:"No data (for no delay io)",62:"Timer expired",63:"Out of streams resources",64:"Machine is not on the network",65:"Package not installed",66:"The object is remote",67:"The link has been severed",68:"Advertise error",69:"Srmount error",70:"Communication error on send",71:"Protocol error",72:"Multihop attempted",73:"Cross mount point (not really error)",74:"Trying to read unreadable message",75:"Value too large for defined data type",76:"Given log. name not unique",77:"f.d. invalid for this operation",78:"Remote address changed",79:"Can   access a needed shared lib",80:"Accessing a corrupted shared lib",81:".lib section in a.out corrupted",82:"Attempting to link in too many libs",83:"Attempting to exec a shared library",84:"Illegal byte sequence",86:"Streams pipe error",87:"Too many users",88:"Socket operation on non-socket",89:"Destination address required",90:"Message too long",91:"Protocol wrong type for socket",92:"Protocol not available",93:"Unknown protocol",94:"Socket type not supported",95:"Not supported",96:"Protocol family not supported",97:"Address family not supported by protocol family",98:"Address already in use",99:"Address not available",100:"Network interface is not configured",101:"Network is unreachable",102:"Connection reset by network",103:"Connection aborted",104:"Connection reset by peer",105:"No buffer space available",106:"Socket is already connected",107:"Socket is not connected",108:"Can't send after socket shutdown",109:"Too many references",110:"Connection timed out",111:"Connection refused",112:"Host is down",113:"Host is unreachable",114:"Socket already connected",115:"Connection already in progress",116:"Stale file handle",122:"Quota exceeded",123:"No medium (in tape drive)",125:"Operation canceled",130:"Previous owner died",131:"State not recoverable"},bb=0;function V(e){return K[bb>>2]=e}function cb(e,r){for(var t=0,n=e.length-1;0<=n;n--){var i=e[n];"."===i?e.splice(n,1):".."===i?(e.splice(n,1),t++):t&&(e.splice(n,1),t--)}if(r)for(;t--;t)e.unshift("..");return e}function db(e){var r="/"===e.charAt(0),t="/"===e.substr(-1);return!(e=cb(e.split("/").filter(function(e){return!!e}),!r).join("/"))&&!r&&(e="."),e&&t&&(e+="/"),(r?"/":"")+e}function eb(e){e=(r=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1))[0];var r=r[1];return e||r?(r&&(r=r.substr(0,r.length-1)),e+r):"."}function W(e){if("/"===e)return"/";var r=e.lastIndexOf("/");return-1===r?e:e.substr(r+1)}function fb(){return db(Array.prototype.slice.call(arguments,0).join("/"))}function X(e,r){return db(e+"/"+r)}function gb(){for(var e="",r=m,t=arguments.length-1;-1<=t&&!r;t--){if("string"!=typeof(r=0<=t?arguments[t]:B.yb())&&d(new TypeError("Arguments to path.resolve must be strings")),!r)return"";e=r+"/"+e,r="/"===r.charAt(0)}return e=cb(e.split("/").filter(function(e){return!!e}),!r).join("/"),(r?"/":"")+e||"."}function hb(e,r){function t(e){for(var r=0;r<e.length&&""===e[r];r++);for(var t=e.length-1;0<=t&&""===e[t];t--);return r>t?[]:e.slice(r,t-r+1)}e=gb(e).substr(1),r=gb(r).substr(1);for(var n=t(e.split("/")),i=t(r.split("/")),a=Math.min(n.length,i.length),s=a,o=0;o<a;o++)if(n[o]!==i[o]){s=o;break}for(a=[],o=s;o<n.length;o++)a.push("..");return(a=a.concat(i.slice(s))).join("/")}var ib=[];function jb(e,r){ib[e]={input:[],K:[],sa:r},B.Ob(e,kb)}var kb={open:function(e){var r=ib[e.g.ob];r||d(new B.e(U.Qa)),e.N=r,e.seekable=m},close:function(e){e.N.sa.flush(e.N)},flush:function(e){e.N.sa.flush(e.N)},M:function(e,r,t,n){(!e.N||!e.N.sa.rc)&&d(new B.e(U.Zb));for(var i=0,a=0;a<n;a++){var s;try{s=e.N.sa.rc(e.N)}catch(e){d(new B.e(U.ha))}if(s===g&&0===i&&d(new B.e(U.Ca)),s===k||s===g)break;i++,r[t+a]=s}return i&&(e.g.timestamp=Date.now()),i},write:function(e,r,t,n){(!e.N||!e.N.sa.Lb)&&d(new B.e(U.Zb));for(var i=0;i<n;i++)try{e.N.sa.Lb(e.N,r[t+i])}catch(e){d(new B.e(U.ha))}return n&&(e.g.timestamp=Date.now()),i}},mb={rc:function(e){if(!e.input.length){var r=k;if(t){if(!(r=process.stdin.read()))return process.stdin._readableState&&process.stdin._readableState.ended?k:void 0}else void 0!==window&&"function"==typeof window.prompt?(r=window.prompt("Input: "))!==k&&(r+="\n"):"function"==typeof readline&&((r=readline())!==k&&(r+="\n"));if(!r)return k;e.input=Va(r,i)}return e.input.shift()},flush:function(e){e.K&&0<e.K.length&&(p.print(e.K.join("")),e.K=[])},Lb:function(e,r){r===k||10===r?(p.print(e.K.join("")),e.K=[]):e.K.push(lb.nb(r))}},nb={Lb:function(e,r){r===k||10===r?(p.printErr(e.K.join("")),e.K=[]):e.K.push(lb.nb(r))},flush:function(e){e.K&&0<e.K.length&&(p.printErr(e.K.join("")),e.K=[])}},Y={U:k,F:function(){return Y.createNode(k,"/",16895,0)},createNode:function(e,r,t,n){return(B.Bd(t)||B.Cd(t))&&d(new B.e(U.O)),Y.U||(Y.U={dir:{g:{S:Y.n.S,I:Y.n.I,ra:Y.n.ra,ba:Y.n.ba,rename:Y.n.rename,za:Y.n.za,Oa:Y.n.Oa,Na:Y.n.Na,ca:Y.n.ca},A:{$:Y.p.$}},file:{g:{S:Y.n.S,I:Y.n.I},A:{$:Y.p.$,M:Y.p.M,write:Y.p.write,Ea:Y.p.Ea,Ja:Y.p.Ja}},link:{g:{S:Y.n.S,I:Y.n.I,ta:Y.n.ta},A:{}},ec:{g:{S:Y.n.S,I:Y.n.I},A:B.bd}}),t=B.createNode(e,r,t,n),B.J(t.mode)?(t.n=Y.U.dir.g,t.p=Y.U.dir.A,t.k={}):B.isFile(t.mode)?(t.n=Y.U.file.g,t.p=Y.U.file.A,t.q=0,t.k=k):B.Ia(t.mode)?(t.n=Y.U.link.g,t.p=Y.U.link.A):B.ib(t.mode)&&(t.n=Y.U.ec.g,t.p=Y.U.ec.A),t.timestamp=Date.now(),e&&(e.k[r]=t),t},ud:function(e){if(e.k&&e.k.subarray){for(var r=[],t=0;t<e.q;++t)r.push(e.k[t]);return r}return e.k},qg:function(e){return e.k?e.k.subarray?e.k.subarray(0,e.q):new Uint8Array(e.k):new Uint8Array},lc:function(e,r){if(e.k&&e.k.subarray&&r>e.k.length&&(e.k=Y.ud(e),e.q=e.k.length),!e.k||e.k.subarray){var t=e.k?e.k.buffer.byteLength:0;t>=r||(r=Math.max(r,t*(1048576>t?2:1.125)|0),0!=t&&(r=Math.max(r,256)),t=e.k,e.k=new Uint8Array(r),0<e.q&&e.k.set(t.subarray(0,e.q),0))}else for(!e.k&&0<r&&(e.k=[]);e.k.length<r;)e.k.push(0)},Ud:function(e,r){if(e.q!=r)if(0==r)e.k=k,e.q=0;else{if(!e.k||e.k.subarray){var t=e.k;e.k=new Uint8Array(new ArrayBuffer(r)),t&&e.k.set(t.subarray(0,Math.min(r,e.q)))}else if(e.k||(e.k=[]),e.k.length>r)e.k.length=r;else for(;e.k.length<r;)e.k.push(0);e.q=r}},n:{S:function(e){var r={};return r.gg=B.ib(e.mode)?e.id:1,r.wg=e.id,r.mode=e.mode,r.Ig=1,r.uid=0,r.ug=0,r.ob=e.ob,r.size=B.J(e.mode)?4096:B.isFile(e.mode)?e.q:B.Ia(e.mode)?e.link.length:0,r.Zf=new Date(e.timestamp),r.Hg=new Date(e.timestamp),r.eg=new Date(e.timestamp),r.Zc=4096,r.$f=Math.ceil(r.size/r.Zc),r},I:function(e,r){r.mode!==g&&(e.mode=r.mode),r.timestamp!==g&&(e.timestamp=r.timestamp),r.size!==g&&Y.Ud(e,r.size)},ra:function(){d(B.Db[U.Q])},ba:function(e,r,t,n){return Y.createNode(e,r,t,n)},rename:function(e,r,t){if(B.J(e.mode)){var n;try{n=B.aa(r,t)}catch(e){}if(n)for(var i in n.k)d(new B.e(U.sb))}delete e.parent.k[e.name],e.name=t,r.k[t]=e,e.parent=r},za:function(e,r){delete e.k[r]},Oa:function(e,r){var t,n=B.aa(e,r);for(t in n.k)d(new B.e(U.sb));delete e.k[r]},Na:function(e){var r,t=[".",".."];for(r in e.k)e.k.hasOwnProperty(r)&&t.push(r);return t},ca:function(e,r,t){return(e=Y.createNode(e,r,41471,0)).link=t,e},ta:function(e){return B.Ia(e.mode)||d(new B.e(U.B)),e.link}},p:{M:function(e,r,t,n,i){var a=e.g.k;if(i>=e.g.q)return 0;if(w(0<=(e=Math.min(e.g.q-i,n))),8<e&&a.subarray)r.set(a.subarray(i,i+e),t);else for(n=0;n<e;n++)r[t+n]=a[i+n];return e},write:function(e,r,t,n,i,a){if(!n)return 0;if((e=e.g).timestamp=Date.now(),r.subarray&&(!e.k||e.k.subarray)){if(a)return e.k=r.subarray(t,t+n),e.q=n;if(0===e.q&&0===i)return e.k=new Uint8Array(r.subarray(t,t+n)),e.q=n;if(i+n<=e.q)return e.k.set(r.subarray(t,t+n),i),n}if(Y.lc(e,i+n),e.k.subarray&&r.subarray)e.k.set(r.subarray(t,t+n),i);else for(a=0;a<n;a++)e.k[i+a]=r[t+a];return e.q=Math.max(e.q,i+n),n},$:function(e,r,t){return 1===t?r+=e.position:2===t&&B.isFile(e.g.mode)&&(r+=e.g.q),0>r&&d(new B.e(U.B)),r},Ea:function(e,r,t){Y.lc(e.g,r+t),e.g.q=Math.max(e.g.q,r+t)},Ja:function(e,r,t,n,a,s,o){return B.isFile(e.g.mode)||d(new B.e(U.Qa)),t=e.g.k,2&o||t.buffer!==r&&t.buffer!==r.buffer?((0<a||a+n<e.g.q)&&(t=t.subarray?t.subarray(a,a+n):Array.prototype.slice.call(t,a,a+n)),e=i,(n=Ca(n))||d(new B.e(U.Mc)),r.set(t,n)):(e=m,n=t.byteOffset),{Lg:n,Yf:e}}}},ob=M(1,"i32*",L),pb=M(1,"i32*",L),qb=M(1,"i32*",L),B={root:k,La:[],ic:[k],oa:[],Jd:1,T:k,hc:"/",hb:m,vc:i,H:{},Gc:{yc:{Rc:1,Sc:2}},e:k,Db:{},sc:function(e){return e instanceof B.e||d(e+" : "+Fa()),V(e.cb)},u:function(e,r){if(e=gb(B.yb(),e),r=r||{},!e)return{path:"",g:k};var t,n={Bb:i,Nb:0};for(t in n)r[t]===g&&(r[t]=n[t]);8<r.Nb&&d(new B.e(U.rb));n=cb(e.split("/").filter(function(e){return!!e}),m);var a=B.root;t="/";for(var s=0;s<n.length;s++){var o=s===n.length-1;if(o&&r.parent)break;if(a=B.aa(a,n[s]),t=X(t,n[s]),B.ka(a)&&(!o||o&&r.Bb)&&(a=a.Ka.root),!o||r.R)for(o=0;B.Ia(a.mode);)a=B.ta(t),t=gb(eb(t),a),a=B.u(t,{Nb:r.Nb}).g,40<o++&&d(new B.e(U.rb))}return{path:t,g:a}},da:function(e){for(var r;;){if(B.jb(e))return e=e.F.Id,r?"/"!==e[e.length-1]?e+"/"+r:e+r:e;r=r?e.name+"/"+r:e.name,e=e.parent}},Fb:function(e,r){for(var t=0,n=0;n<r.length;n++)t=(t<<5)-t+r.charCodeAt(n)|0;return(e+t>>>0)%B.T.length},tc:function(e){var r=B.Fb(e.parent.id,e.name);e.ma=B.T[r],B.T[r]=e},uc:function(e){var r=B.Fb(e.parent.id,e.name);if(B.T[r]===e)B.T[r]=e.ma;else for(r=B.T[r];r;){if(r.ma===e){r.ma=e.ma;break}r=r.ma}},aa:function(e,r){var t=B.Gd(e);for(t&&d(new B.e(t,e)),t=B.T[B.Fb(e.id,r)];t;t=t.ma){var n=t.name;if(t.parent.id===e.id&&n===r)return t}return B.ra(e,r)},createNode:function(e,r,t,n){return B.Va||(B.Va=function(e,r,t,n){e||(e=this),this.parent=e,this.F=e.F,this.Ka=k,this.id=B.Jd++,this.name=r,this.mode=t,this.n={},this.p={},this.ob=n},B.Va.prototype={},Object.defineProperties(B.Va.prototype,{M:{get:function(){return 365==(365&this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146==(146&this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},Dd:{get:function(){return B.J(this.mode)}},Gb:{get:function(){return B.ib(this.mode)}}})),e=new B.Va(e,r,t,n),B.tc(e),e},zb:function(e){B.uc(e)},jb:function(e){return e===e.parent},ka:function(e){return!!e.Ka},isFile:function(e){return 32768==(61440&e)},J:function(e){return 16384==(61440&e)},Ia:function(e){return 40960==(61440&e)},ib:function(e){return 8192==(61440&e)},Bd:function(e){return 24576==(61440&e)},Cd:function(e){return 4096==(61440&e)},Ed:function(e){return 49152==(49152&e)},rd:{r:0,rs:1052672,"r+":2,w:577,wx:705,xw:705,"w+":578,"wx+":706,"xw+":706,a:1089,ax:1217,xa:1217,"a+":1090,"ax+":1218,"xa+":1218},wc:function(e){var r=B.rd[e];return void 0===r&&d(Error("Unknown file open mode: "+e)),r},sd:function(e){var r=["r","w","rw"][2097155&e];return 512&e&&(r+="w"),r},na:function(e,r){return B.vc?0:(-1===r.indexOf("r")||292&e.mode)&&(-1===r.indexOf("w")||146&e.mode)&&(-1===r.indexOf("x")||73&e.mode)?0:U.qb},Gd:function(e){var r=B.na(e,"x");return r||(e.n.ra?0:U.qb)},Jb:function(e,r){try{return B.aa(e,r),U.Xb}catch(e){}return B.na(e,"wx")},kb:function(e,r,t){var n;try{n=B.aa(e,r)}catch(e){return e.cb}if(e=B.na(e,"wx"))return e;if(t){if(!B.J(n.mode))return U.Sa;if(B.jb(n)||B.da(n)===B.yb())return U.ga}else if(B.J(n.mode))return U.pa;return 0},Hd:function(e,r){return e?B.Ia(e.mode)?U.rb:B.J(e.mode)&&(0!=(2097155&r)||512&r)?U.pa:B.na(e,B.sd(r)):U.Q},Qc:4096,Kd:function(e,r){r=r||B.Qc;for(var t=e||0;t<=r;t++)if(!B.oa[t])return t;d(new B.e(U.Lc))},qa:function(e){return B.oa[e]},fc:function(e,r,t){B.Wa||(B.Wa=n(),B.Wa.prototype={},Object.defineProperties(B.Wa.prototype,{object:{get:function(){return this.g},set:function(e){this.g=e}},yg:{get:function(){return 1!=(2097155&this.D)}},zg:{get:function(){return 0!=(2097155&this.D)}},xg:{get:function(){return 1024&this.D}}}));var i,a=new B.Wa;for(i in e)a[i]=e[i];return e=a,r=B.Kd(r,t),e.C=r,B.oa[r]=e},dd:function(e){B.oa[e]=k},pc:function(e){return B.oa[e-1]},Eb:function(e){return e?e.C+1:0},bd:{open:function(e){e.p=B.td(e.g.ob).p,e.p.open&&e.p.open(e)},$:function(){d(new B.e(U.Ua))}},Ib:function(e){return e>>8},Gg:function(e){return 255&e},la:function(e,r){return e<<8|r},Ob:function(e,r){B.ic[e]={p:r}},td:function(e){return B.ic[e]},nc:function(e){var r=[];for(e=[e];e.length;){var t=e.pop();r.push(t),e.push.apply(e,t.La)}return r},Fc:function(e,r){function t(e){if(e){if(!t.pd)return t.pd=i,r(e)}else++a>=n.length&&r(k)}"function"==typeof e&&(r=e,e=m);var n=B.nc(B.root.F),a=0;n.forEach(function(r){if(!r.type.Fc)return t(k);r.type.Fc(r,e,t)})},F:function(e,r,t){var n,i="/"===t,a=!t;return i&&B.root&&d(new B.e(U.ga)),!i&&!a&&(t=(n=B.u(t,{Bb:m})).path,n=n.g,B.ka(n)&&d(new B.e(U.ga)),B.J(n.mode)||d(new B.e(U.Sa))),r={type:e,Kg:r,Id:t,La:[]},(e=e.F(r)).F=r,r.root=e,i?B.root=e:n&&(n.Ka=r,n.F&&n.F.La.push(r)),e},Qg:function(e){e=B.u(e,{Bb:m}),B.ka(e.g)||d(new B.e(U.B));var r=(e=e.g).Ka,t=B.nc(r);Object.keys(B.T).forEach(function(e){for(e=B.T[e];e;){var r=e.ma;-1!==t.indexOf(e.F)&&B.zb(e),e=r}}),e.Ka=k,w(-1!==(r=e.F.La.indexOf(r))),e.F.La.splice(r,1)},ra:function(e,r){return e.n.ra(e,r)},ba:function(e,r,t){var n=B.u(e,{parent:i}).g;(!(e=W(e))||"."===e||".."===e)&&d(new B.e(U.B));var a=B.Jb(n,e);return a&&d(new B.e(a)),n.n.ba||d(new B.e(U.O)),n.n.ba(n,e,r,t)},create:function(e,r){return r=4095&(r!==g?r:438),r|=32768,B.ba(e,r,0)},ea:function(e,r){return r=1023&(r!==g?r:511),r|=16384,B.ba(e,r,0)},lb:function(e,r,t){return void 0===t&&(t=r,r=438),B.ba(e,8192|r,t)},ca:function(e,r){gb(e)||d(new B.e(U.Q));var t=B.u(r,{parent:i}).g;t||d(new B.e(U.Q));var n=W(r),a=B.Jb(t,n);return a&&d(new B.e(a)),t.n.ca||d(new B.e(U.O)),t.n.ca(t,n,e)},rename:function(e,r){var t,n,a,s,o=eb(e),f=eb(r),u=W(e),l=W(r);try{n=(t=B.u(e,{parent:i})).g,a=(t=B.u(r,{parent:i})).g}catch(e){d(new B.e(U.ga))}(!n||!a)&&d(new B.e(U.Q)),n.F!==a.F&&d(new B.e(U.Oc)),t=B.aa(n,u),"."!==(f=hb(e,f)).charAt(0)&&d(new B.e(U.B)),"."!==(f=hb(r,o)).charAt(0)&&d(new B.e(U.sb));try{s=B.aa(a,l)}catch(e){}if(t!==s){o=B.J(t.mode),(u=B.kb(n,u,o))&&d(new B.e(u)),(u=s?B.kb(a,l,o):B.Jb(a,l))&&d(new B.e(u)),n.n.rename||d(new B.e(U.O)),(B.ka(t)||s&&B.ka(s))&&d(new B.e(U.ga)),a!==n&&(u=B.na(n,"w"))&&d(new B.e(u));try{B.H.willMovePath&&B.H.willMovePath(e,r)}catch(t){console.log("FS.trackingDelegate['willMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}B.uc(t);try{n.n.rename(t,a,l)}catch(e){d(e)}finally{B.tc(t)}try{B.H.onMovePath&&B.H.onMovePath(e,r)}catch(t){console.log("FS.trackingDelegate['onMovePath']('"+e+"', '"+r+"') threw an exception: "+t.message)}}},Oa:function(e){var r=B.u(e,{parent:i}).g,t=W(e),n=B.aa(r,t),a=B.kb(r,t,i);a&&d(new B.e(a)),r.n.Oa||d(new B.e(U.O)),B.ka(n)&&d(new B.e(U.ga));try{B.H.willDeletePath&&B.H.willDeletePath(e)}catch(r){console.log("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.n.Oa(r,t),B.zb(n);try{B.H.onDeletePath&&B.H.onDeletePath(e)}catch(r){console.log("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},Na:function(e){return(e=B.u(e,{R:i}).g).n.Na||d(new B.e(U.Sa)),e.n.Na(e)},za:function(e){var r=B.u(e,{parent:i}).g,t=W(e),n=B.aa(r,t),a=B.kb(r,t,m);a&&(a===U.pa&&(a=U.O),d(new B.e(a))),r.n.za||d(new B.e(U.O)),B.ka(n)&&d(new B.e(U.ga));try{B.H.willDeletePath&&B.H.willDeletePath(e)}catch(r){console.log("FS.trackingDelegate['willDeletePath']('"+e+"') threw an exception: "+r.message)}r.n.za(r,t),B.zb(n);try{B.H.onDeletePath&&B.H.onDeletePath(e)}catch(r){console.log("FS.trackingDelegate['onDeletePath']('"+e+"') threw an exception: "+r.message)}},ta:function(e){return(e=B.u(e).g)||d(new B.e(U.Q)),e.n.ta||d(new B.e(U.B)),e.n.ta(e)},Dc:function(e,r){var t=B.u(e,{R:!r}).g;return t||d(new B.e(U.Q)),t.n.S||d(new B.e(U.O)),t.n.S(t)},Eg:function(e){return B.Dc(e,i)},Ya:function(e,r,t){(e="string"==typeof e?B.u(e,{R:!t}).g:e).n.I||d(new B.e(U.O)),e.n.I(e,{mode:4095&r|-4096&e.mode,timestamp:Date.now()})},Bg:function(e,r){B.Ya(e,r,i)},jg:function(e,r){var t=B.qa(e);t||d(new B.e(U.V)),B.Ya(t.g,r)},dc:function(e,r,t,n){(e="string"==typeof e?B.u(e,{R:!n}).g:e).n.I||d(new B.e(U.O)),e.n.I(e,{timestamp:Date.now()})},Cg:function(e,r,t){B.dc(e,r,t,i)},kg:function(e,r,t){(e=B.qa(e))||d(new B.e(U.V)),B.dc(e.g,r,t)},truncate:function(e,r){var t;0>r&&d(new B.e(U.B)),(t="string"==typeof e?B.u(e,{R:i}).g:e).n.I||d(new B.e(U.O)),B.J(t.mode)&&d(new B.e(U.pa)),B.isFile(t.mode)||d(new B.e(U.B));var n=B.na(t,"w");n&&d(new B.e(n)),t.n.I(t,{size:r,timestamp:Date.now()})},mg:function(e,r){var t=B.qa(e);t||d(new B.e(U.V)),0==(2097155&t.D)&&d(new B.e(U.B)),B.truncate(t.g,r)},Rg:function(e,r,t){(e=B.u(e,{R:i}).g).n.I(e,{timestamp:Math.max(r,t)})},open:function(e,r,t,n,a){""===e&&d(new B.e(U.Q));var s;t=64&(r="string"==typeof r?B.wc(r):r)?4095&(void 0===t?438:t)|32768:0;if("object"==typeof e)s=e;else{e=db(e);try{s=B.u(e,{R:!(131072&r)}).g}catch(e){}}var o=m;64&r&&(s?128&r&&d(new B.e(U.Xb)):(s=B.ba(e,t,0),o=i)),s||d(new B.e(U.Q)),B.ib(s.mode)&&(r&=-513),o||(t=B.Hd(s,r))&&d(new B.e(t)),512&r&&B.truncate(s,0),r&=-641,(n=B.fc({g:s,path:B.da(s),D:r,seekable:i,position:0,p:s.p,$d:[],error:m},n,a)).p.open&&n.p.open(n),p.logReadFiles&&!(1&r)&&(B.Mb||(B.Mb={}),e in B.Mb||(B.Mb[e]=1,p.printErr("read file: "+e)));try{B.H.onOpenFile&&(a=0,1!=(2097155&r)&&(a|=B.Gc.yc.Rc),0!=(2097155&r)&&(a|=B.Gc.yc.Sc),B.H.onOpenFile(e,a))}catch(r){console.log("FS.trackingDelegate['onOpenFile']('"+e+"', flags) threw an exception: "+r.message)}return n},close:function(e){try{e.p.close&&e.p.close(e)}catch(e){d(e)}finally{B.dd(e.C)}},$:function(e,r,t){return(!e.seekable||!e.p.$)&&d(new B.e(U.Ua)),e.position=e.p.$(e,r,t),e.$d=[],e.position},M:function(e,r,t,n,a){(0>n||0>a)&&d(new B.e(U.B)),1==(2097155&e.D)&&d(new B.e(U.V)),B.J(e.g.mode)&&d(new B.e(U.pa)),e.p.M||d(new B.e(U.B));var s=i;return void 0===a?(a=e.position,s=m):e.seekable||d(new B.e(U.Ua)),r=e.p.M(e,r,t,n,a),s||(e.position+=r),r},write:function(e,r,t,n,a,s){(0>n||0>a)&&d(new B.e(U.B)),0==(2097155&e.D)&&d(new B.e(U.V)),B.J(e.g.mode)&&d(new B.e(U.pa)),e.p.write||d(new B.e(U.B)),1024&e.D&&B.$(e,0,2);var o=i;void 0===a?(a=e.position,o=m):e.seekable||d(new B.e(U.Ua)),r=e.p.write(e,r,t,n,a,s),o||(e.position+=r);try{e.path&&B.H.onWriteToFile&&B.H.onWriteToFile(e.path)}catch(e){console.log("FS.trackingDelegate['onWriteToFile']('"+path+"') threw an exception: "+e.message)}return r},Ea:function(e,r,t){(0>r||0>=t)&&d(new B.e(U.B)),0==(2097155&e.D)&&d(new B.e(U.V)),!B.isFile(e.g.mode)&&!B.J(node.mode)&&d(new B.e(U.Qa)),e.p.Ea||d(new B.e(U.Ta)),e.p.Ea(e,r,t)},Ja:function(e,r,t,n,i,a,s){return 1==(2097155&e.D)&&d(new B.e(U.qb)),e.p.Ja||d(new B.e(U.Qa)),e.p.Ja(e,r,t,n,i,a,s)},Ha:function(e,r,t){return e.p.Ha||d(new B.e(U.Nc)),e.p.Ha(e,r,t)},Mg:function(e,r){(r=r||{}).D=r.D||"r",r.encoding=r.encoding||"binary","utf8"!==r.encoding&&"binary"!==r.encoding&&d(Error('Invalid encoding type "'+r.encoding+'"'));var t,n=B.open(e,r.D),i=B.Dc(e).size,a=new Uint8Array(i);if(B.M(n,a,0,i,0),"utf8"===r.encoding){t="";for(var s=new z.Da,o=0;o<i;o++)t+=s.nb(a[o])}else"binary"===r.encoding&&(t=a);return B.close(n),t},Sg:function(e,r,t){(t=t||{}).D=t.D||"w",t.encoding=t.encoding||"utf8","utf8"!==t.encoding&&"binary"!==t.encoding&&d(Error('Invalid encoding type "'+t.encoding+'"')),e=B.open(e,t.D,t.mode),"utf8"===t.encoding?(r=new Uint8Array((new z.Da).Ac(r)),B.write(e,r,0,r.length,0,t.ad)):"binary"===t.encoding&&B.write(e,r,0,r.length,0,t.ad),B.close(e)},yb:function(){return B.hc},bg:function(e){e=B.u(e,{R:i}),B.J(e.g.mode)||d(new B.e(U.Sa));var r=B.na(e.g,"x");r&&d(new B.e(r)),B.hc=e.path},fd:function(){B.ea("/tmp"),B.ea("/home"),B.ea("/home/<USER>")},ed:function(){var e;if(B.ea("/dev"),B.Ob(B.la(1,3),{M:function(){return 0},write:function(){return 0}}),B.lb("/dev/null",B.la(1,3)),jb(B.la(5,0),mb),jb(B.la(6,0),nb),B.lb("/dev/tty",B.la(5,0)),B.lb("/dev/tty1",B.la(6,0)),"undefined"!=typeof crypto){var r=new Uint8Array(1);e=function(){return crypto.getRandomValues(r),r[0]}}else e=t?function(){return null("crypto").randomBytes(1)[0]}:function(){return 256*Math.random()|0};B.X("/dev","random",e),B.X("/dev","urandom",e),B.ea("/dev/shm"),B.ea("/dev/shm/tmp")},od:function(){p.stdin?B.X("/dev","stdin",p.stdin):B.ca("/dev/tty","/dev/stdin"),p.stdout?B.X("/dev","stdout",k,p.stdout):B.ca("/dev/tty","/dev/stdout"),p.stderr?B.X("/dev","stderr",k,p.stderr):B.ca("/dev/tty1","/dev/stderr");var e=B.open("/dev/stdin","r");K[ob>>2]=B.Eb(e),w(0===e.C,"invalid handle for stdin ("+e.C+")"),e=B.open("/dev/stdout","w"),K[pb>>2]=B.Eb(e),w(1===e.C,"invalid handle for stdout ("+e.C+")"),e=B.open("/dev/stderr","w"),K[qb>>2]=B.Eb(e),w(2===e.C,"invalid handle for stderr ("+e.C+")")},jc:function(){B.e||(B.e=function(e,r){this.g=r,this.Xd=function(e){for(var r in this.cb=e,U)if(U[r]===e){this.code=r;break}},this.Xd(e),this.message=ab[e]},B.e.prototype=Error(),[U.Q].forEach(function(e){B.Db[e]=new B.e(e),B.Db[e].stack="<generic error, no stack>"}))},Zd:function(){B.jc(),B.T=Array(4096),B.F(Y,{},"/"),B.fd(),B.ed()},Ga:function(e,r,t){w(!B.Ga.hb,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)"),B.Ga.hb=i,B.jc(),p.stdin=e||p.stdin,p.stdout=r||p.stdout,p.stderr=t||p.stderr,B.od()},Qd:function(){B.Ga.hb=m;for(var e=0;e<B.oa.length;e++){var r=B.oa[e];r&&B.close(r)}},fb:function(e,r){var t=0;return e&&(t|=365),r&&(t|=146),t},Ag:function(e,r){var t=fb.apply(k,e);return r&&"/"==t[0]&&(t=t.substr(1)),t},Sf:function(e,r){return gb(r,e)},Pg:function(e){return db(e)},lg:function(e,r){var t=B.vb(e,r);return t.Ab?t.object:(V(t.error),k)},vb:function(e,r){try{var t=B.u(e,{R:!r});e=t.path}catch(e){}var n={jb:m,Ab:m,error:0,name:k,path:k,object:k,Md:m,Od:k,Nd:k};try{t=B.u(e,{parent:i}),n.Md=i,n.Od=t.path,n.Nd=t.g,n.name=W(e),t=B.u(e,{R:!r}),n.Ab=i,n.path=t.path,n.object=t.g,n.name=t.g.name,n.jb="/"===t.path}catch(e){n.error=e.cb}return n},hd:function(e,r,t,n){return e=X("string"==typeof e?e:B.da(e),r),B.ea(e,B.fb(t,n))},ld:function(e,r){e="string"==typeof e?e:B.da(e);for(var t=r.split("/").reverse();t.length;){var n=t.pop();if(n){var i=X(e,n);try{B.ea(i)}catch(e){}e=i}}return i},gd:function(e,r,t,n,i){return e=X("string"==typeof e?e:B.da(e),r),B.create(e,B.fb(n,i))},xb:function(e,r,t,n,i,a){if(e=r?X("string"==typeof e?e:B.da(e),r):e,n=B.fb(n,i),i=B.create(e,n),t){if("string"==typeof t){e=Array(t.length),r=0;for(var s=t.length;r<s;++r)e[r]=t.charCodeAt(r);t=e}B.Ya(i,146|n),e=B.open(i,"w"),B.write(e,t,0,t.length,0,a),B.close(e),B.Ya(i,n)}return i},X:function(e,r,t,n){e=X("string"==typeof e?e:B.da(e),r),r=B.fb(!!t,!!n),B.X.Ib||(B.X.Ib=64);var i=B.la(B.X.Ib++,0);return B.Ob(i,{open:function(e){e.seekable=m},close:function(){n&&n.buffer&&n.buffer.length&&n(10)},M:function(e,r,n,i){for(var a=0,s=0;s<i;s++){var o;try{o=t()}catch(e){d(new B.e(U.ha))}if(o===g&&0===a&&d(new B.e(U.Ca)),o===k||o===g)break;a++,r[n+s]=o}return a&&(e.g.timestamp=Date.now()),a},write:function(e,r,t,i){for(var a=0;a<i;a++)try{n(r[t+a])}catch(e){d(new B.e(U.ha))}return i&&(e.g.timestamp=Date.now()),a}}),B.lb(e,r,i)},kd:function(e,r,t){return e=X("string"==typeof e?e:B.da(e),r),B.ca(t,e)},mc:function(e){if(e.Gb||e.Dd||e.link||e.k)return i;var r=i;if("undefined"!=typeof XMLHttpRequest&&d(Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")),p.read)try{e.k=Va(p.read(e.url),i),e.q=e.k.length}catch(e){r=m}else d(Error("Cannot load without read() or XMLHttpRequest."));return r||V(U.ha),r},jd:function(e,r,t,n,a){function s(){this.Hb=m,this.Za=[]}if(s.prototype.get=function(e){if(!(e>this.length-1||0>e)){var r=e%this.cd;return this.yd(e/this.cd|0)[r]}},s.prototype.Wd=function(e){this.yd=e},s.prototype.bc=function(){var e=new XMLHttpRequest;e.open("HEAD",t,m),e.send(k),200<=e.status&&300>e.status||304===e.status||d(Error("Couldn't load "+t+". Status: "+e.status));var r,n=Number(e.getResponseHeader("Content-length")),a=1048576;(r=e.getResponseHeader("Accept-Ranges"))&&"bytes"===r||(a=n);var s=this;s.Wd(function(e){var r=e*a,o=(e+1)*a-1;o=Math.min(o,n-1);if(void 0===s.Za[e]){var f=s.Za;r>o&&d(Error("invalid range ("+r+", "+o+") or no bytes requested!")),o>n-1&&d(Error("only "+n+" bytes available! programmer error!"));var u=new XMLHttpRequest;u.open("GET",t,m),n!==a&&u.setRequestHeader("Range","bytes="+r+"-"+o),"undefined"!=typeof Uint8Array&&(u.responseType="arraybuffer"),u.overrideMimeType&&u.overrideMimeType("text/plain; charset=x-user-defined"),u.send(k),200<=u.status&&300>u.status||304===u.status||d(Error("Couldn't load "+t+". Status: "+u.status)),r=u.response!==g?new Uint8Array(u.response||[]):Va(u.responseText||"",i),f[e]=r}return void 0===s.Za[e]&&d(Error("doXHR failed!")),s.Za[e]}),this.Uc=n,this.Tc=a,this.Hb=i},"undefined"!=typeof XMLHttpRequest){ca||d("Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc");var o=new s;Object.defineProperty(o,"length",{get:function(){return this.Hb||this.bc(),this.Uc}}),Object.defineProperty(o,"chunkSize",{get:function(){return this.Hb||this.bc(),this.Tc}}),o={Gb:m,k:o}}else o={Gb:m,url:t};var f=B.gd(e,r,o,n,a);o.k?f.k=o.k:o.url&&(f.k=k,f.url=o.url),Object.defineProperty(f,"usedBytes",{get:function(){return this.k.length}});var u={};return Object.keys(f.p).forEach(function(e){var r=f.p[e];u[e]=function(){return B.mc(f)||d(new B.e(U.ha)),r.apply(k,arguments)}}),u.M=function(e,r,t,n,i){if(B.mc(f)||d(new B.e(U.ha)),i>=(e=e.g.k).length)return 0;if(w(0<=(n=Math.min(e.length-i,n))),e.slice)for(var a=0;a<n;a++)r[t+a]=e[i+a];else for(a=0;a<n;a++)r[t+a]=e.get(i+a);return n},f.p=u,f},md:function(e,r,t,a,s,o,f,u,l){function c(){rb=document.pointerLockElement===d||document.mozPointerLockElement===d||document.webkitPointerLockElement===d||document.msPointerLockElement===d}function h(t){function n(t){u||B.xb(e,r,t,a,s,l),o&&o(),Za()}var c=m;p.preloadPlugins.forEach(function(e){!c&&e.canHandle(b)&&(e.handle(t,b,n,function(){f&&f(),Za()}),c=i)}),c||n(t)}if(p.preloadPlugins||(p.preloadPlugins=[]),!tb){tb=i;try{new Blob,ub=i}catch(e){ub=m,console.log("warning: no blob constructor, cannot create blobs with mimetypes")}vb="undefined"!=typeof MozBlobBuilder?MozBlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:ub?k:console.log("warning: no BlobBuilder"),wb=void 0!==window?window.URL?window.URL:window.webkitURL:g,!p.xc&&void 0===wb&&(console.log("warning: Browser does not support creating object URLs. Built-in browser image decoding will not be available."),p.xc=i),p.preloadPlugins.push({canHandle:function(e){return!p.xc&&/\.(jpg|jpeg|png|bmp)$/i.test(e)},handle:function(e,r,t,n){var i=k;if(ub)try{(i=new Blob([e],{type:xb(r)})).size!==e.length&&(i=new Blob([new Uint8Array(e).buffer],{type:xb(r)}))}catch(e){z.Aa("Blob constructor present but fails: "+e+"; falling back to blob builder")}i||((i=new vb).append(new Uint8Array(e).buffer),i=i.getBlob());var a=wb.createObjectURL(i),s=new Image;s.onload=function(){w(s.complete,"Image "+r+" could not be decoded");var n=document.createElement("canvas");n.width=s.width,n.height=s.height,n.getContext("2d").drawImage(s,0,0),p.preloadedImages[r]=n,wb.revokeObjectURL(a),t&&t(e)},s.onerror=function(){console.log("Image "+a+" could not be decoded"),n&&n()},s.src=a}}),p.preloadPlugins.push({canHandle:function(e){return!p.Jg&&e.substr(-4)in{".ogg":1,".wav":1,".mp3":1}},handle:function(e,r,t,n){function a(n){o||(o=i,p.preloadedAudios[r]=n,t&&t(e))}function s(){o||(o=i,p.preloadedAudios[r]=new Audio,n&&n())}var o=m;if(!ub)return s();try{var f=new Blob([e],{type:xb(r)})}catch(e){return s()}f=wb.createObjectURL(f);var u=new Audio;u.addEventListener("canplaythrough",function(){a(u)},m),u.onerror=function(){if(!o){console.log("warning: browser could not fully decode audio "+r+", trying slower base64 approach");for(var t="",n=0,i=0,s=0;s<e.length;s++)for(n=n<<8|e[s],i+=8;6<=i;){var f=n>>i-6&63;i=i-6,t=t+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[f]}2==i?(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(3&n)<<4],t+="=="):4==i&&(t+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[(15&n)<<2],t+="="),u.src="data:audio/x-"+r.substr(-3)+";base64,"+t,a(u)}},u.src=f,p.noExitRuntime=i,setTimeout(function(){H||a(u)},1e4)}});var d=p.canvas;d&&(d.Pb=d.requestPointerLock||d.mozRequestPointerLock||d.webkitRequestPointerLock||d.msRequestPointerLock||n(),d.kc=document.exitPointerLock||document.mozExitPointerLock||document.webkitExitPointerLock||document.msExitPointerLock||n(),d.kc=d.kc.bind(document),document.addEventListener("pointerlockchange",c,m),document.addEventListener("mozpointerlockchange",c,m),document.addEventListener("webkitpointerlockchange",c,m),document.addEventListener("mspointerlockchange",c,m),p.elementPointerLock&&d.addEventListener("click",function(e){!rb&&d.Pb&&(d.Pb(),e.preventDefault())},m))}var b=r?gb(X(e,r)):e;Ya(),"string"==typeof t?yb(t,function(e){h(e)},f):h(t)},indexedDB:function(){return window.indexedDB||window.mozIndexedDB||window.webkitIndexedDB||window.msIndexedDB},Ub:function(){return"EM_FS_"+window.location.pathname},Vb:20,Ba:"FILE_DATA",Og:function(e,r,t){r=r||n(),t=t||n();var i=B.indexedDB();try{var a=i.open(B.Ub(),B.Vb)}catch(e){return t(e)}a.Ld=function(){console.log("creating db"),a.result.createObjectStore(B.Ba)},a.onsuccess=function(){var n=a.result.transaction([B.Ba],"readwrite"),i=n.objectStore(B.Ba),s=0,o=0,f=e.length;e.forEach(function(e){(e=i.put(B.vb(e).object.k,e)).onsuccess=function(){++s+o==f&&(0==o?r():t())},e.onerror=function(){s+ ++o==f&&(0==o?r():t())}}),n.onerror=t},a.onerror=t},Dg:function(e,r,t){r=r||n(),t=t||n();var a=B.indexedDB();try{var s=a.open(B.Ub(),B.Vb)}catch(e){return t(e)}s.Ld=t,s.onsuccess=function(){var n=s.result;try{var a=n.transaction([B.Ba],"readonly")}catch(e){return void t(e)}var o=a.objectStore(B.Ba),f=0,u=0,l=e.length;e.forEach(function(e){var n=o.get(e);n.onsuccess=function(){B.vb(e).Ab&&B.za(e),B.xb(eb(e),W(e),n.result,i,i,i),++f+u==l&&(0==u?r():t())},n.onerror=function(){f+ ++u==l&&(0==u?r():t())}}),a.onerror=t},s.onerror=t}};function zb(){d("TODO")}var Z={F:function(){return p.websocket=p.websocket&&"object"==typeof p.websocket?p.websocket:{},p.websocket.tb={},p.websocket.on=function(e,r){return"function"==typeof r&&(this.tb[e]=r),this},p.websocket.P=function(e,r){"function"==typeof this.tb[e]&&this.tb[e].call(this,r)},B.createNode(k,"/",16895,0)},nd:function(e,r,t){return t&&w(1==r==(6==t)),e={qd:e,type:r,protocol:t,G:k,error:k,Ma:{},Kb:[],ua:[],wa:Z.L},r=Z.mb(),(t=B.createNode(Z.root,r,49152,0)).va=e,r=B.fc({path:r,g:t,D:B.wc("r+"),seekable:m,p:Z.p}),e.A=r,e},wd:function(e){return(e=B.qa(e))&&B.Ed(e.g.mode)?e.g.va:k},p:{zc:function(e){return(e=e.g.va).wa.zc(e)},Ha:function(e,r,t){return(e=e.g.va).wa.Ha(e,r,t)},M:function(e,r,t,n){return(n=(e=e.g.va).wa.Rd(e,n))?(r.set(n.buffer,t),n.buffer.length):0},write:function(e,r,t,n){return(e=e.g.va).wa.Vd(e,r,t,n)},close:function(e){(e=e.g.va).wa.close(e)}},mb:function(){return Z.mb.gc||(Z.mb.gc=0),"socket["+Z.mb.gc+++"]"},L:{$a:function(e,r,n){var i;if("object"==typeof r&&(i=r,n=r=k),i)i._socket?(r=i._socket.remoteAddress,n=i._socket.remotePort):((n=/ws[s]?:\/\/([^:]+):(\d+)/.exec(i.url))||d(Error("WebSocket URL must be in the format ws(s)://address:port")),r=n[1],n=parseInt(n[2],10));else try{var a=p.websocket&&"object"==typeof p.websocket,s="ws:#".replace("#","//");if(a&&"string"==typeof p.websocket.url&&(s=p.websocket.url),"ws://"===s||"wss://"===s)s=s+(o=r.split("/"))[0]+":"+n+"/"+o.slice(1).join("/");o="binary",a&&"string"==typeof p.websocket.subprotocol&&(o=p.websocket.subprotocol);var o=o.replace(/^ +| +$/g,"").split(/ *, */),f=t?{protocol:o.toString()}:o;(i=new(t?null("ws"):window.WebSocket)(s,f)).binaryType="arraybuffer"}catch(e){d(new B.e(U.Yb))}return r={W:r,port:n,o:i,ab:[]},Z.L.$b(e,r),Z.L.zd(e,r),2===e.type&&void 0!==e.ya&&r.ab.push(new Uint8Array([255,255,255,255,112,111,114,116,(65280&e.ya)>>8,255&e.ya])),r},gb:function(e,r,t){return e.Ma[r+":"+t]},$b:function(e,r){e.Ma[r.W+":"+r.port]=r},Bc:function(e,r){delete e.Ma[r.W+":"+r.port]},zd:function(e,r){function n(){p.websocket.P("open",e.A.C);try{for(var t=r.ab.shift();t;)r.o.send(t),t=r.ab.shift()}catch(e){r.o.close()}}function a(t){w("string"!=typeof t&&t.byteLength!==g);t=new Uint8Array(t);var n=s;s=m,n&&10===t.length&&255===t[0]&&255===t[1]&&255===t[2]&&255===t[3]&&112===t[4]&&111===t[5]&&114===t[6]&&116===t[7]?(t=t[8]<<8|t[9],Z.L.Bc(e,r),r.port=t,Z.L.$b(e,r)):(e.ua.push({W:r.W,port:r.port,data:t}),p.websocket.P("message",e.A.C))}var s=i;t?(r.o.on("open",n),r.o.on("message",function(e,r){r.binary&&a(new Uint8Array(e).buffer)}),r.o.on("close",function(){p.websocket.P("close",e.A.C)}),r.o.on("error",function(){e.error=U.Wb,p.websocket.P("error",[e.A.C,e.error,"ECONNREFUSED: Connection refused"])})):(r.o.onopen=n,r.o.onclose=function(){p.websocket.P("close",e.A.C)},r.o.onmessage=function(e){a(e.data)},r.o.onerror=function(){e.error=U.Wb,p.websocket.P("error",[e.A.C,e.error,"ECONNREFUSED: Connection refused"])})},zc:function(e){if(1===e.type&&e.G)return e.Kb.length?65:0;var r=0,t=1===e.type?Z.L.gb(e,e.Y,e.Z):k;return(e.ua.length||!t||t&&t.o.readyState===t.o.Pa||t&&t.o.readyState===t.o.CLOSED)&&(r|=65),(!t||t&&t.o.readyState===t.o.OPEN)&&(r|=4),(t&&t.o.readyState===t.o.Pa||t&&t.o.readyState===t.o.CLOSED)&&(r|=16),r},Ha:function(e,r,t){switch(r){case 21531:return r=0,e.ua.length&&(r=e.ua[0].data.length),K[t>>2]=r,0;default:return U.B}},close:function(e){if(e.G){try{e.G.close()}catch(e){}e.G=k}for(var r=Object.keys(e.Ma),t=0;t<r.length;t++){var n=e.Ma[r[t]];try{n.o.close()}catch(e){}Z.L.Bc(e,n)}return 0},bind:function(e,r,t){if((void 0!==e.Qb||void 0!==e.ya)&&d(new B.e(U.B)),e.Qb=r,e.ya=t||zb(),2===e.type){e.G&&(e.G.close(),e.G=k);try{e.wa.Fd(e,0)}catch(e){e instanceof B.e||d(e),e.cb!==U.Ta&&d(e)}}},cg:function(e,r,t){if(e.G&&d(new B.e(U.Ta)),void 0!==e.Y&&void 0!==e.Z){var n=Z.L.gb(e,e.Y,e.Z);n&&(n.o.readyState===n.o.CONNECTING&&d(new B.e(U.Hc)),d(new B.e(U.Kc)))}r=Z.L.$a(e,r,t),e.Y=r.W,e.Z=r.port,d(new B.e(U.Jc))},Fd:function(e){t||d(new B.e(U.Ta)),e.G&&d(new B.e(U.B));var r=null("ws").Server;e.G=new r({host:e.Qb,port:e.ya}),p.websocket.P("listen",e.A.C),e.G.on("connection",function(r){if(1===e.type){var t=Z.nd(e.qd,e.type,e.protocol);r=Z.L.$a(t,r);t.Y=r.W,t.Z=r.port,e.Kb.push(t),p.websocket.P("connection",t.A.C)}else Z.L.$a(e,r),p.websocket.P("connection",e.A.C)}),e.G.on("closed",function(){p.websocket.P("close",e.A.C),e.G=k}),e.G.on("error",function(){e.error=U.Yb,p.websocket.P("error",[e.A.C,e.error,"EHOSTUNREACH: Host is unreachable"])})},accept:function(e){e.G||d(new B.e(U.B));var r=e.Kb.shift();return r.A.D=e.A.D,r},tg:function(e,r){var t,n;return r?((e.Y===g||e.Z===g)&&d(new B.e(U.Ra)),t=e.Y,n=e.Z):(t=e.Qb||0,n=e.ya||0),{W:t,port:n}},Vd:function(e,r,t,n,i,a){2===e.type?(i!==g&&a!==g||(i=e.Y,a=e.Z),(i===g||a===g)&&d(new B.e(U.Ic))):(i=e.Y,a=e.Z);var s=Z.L.gb(e,i,a);if(1===e.type&&((!s||s.o.readyState===s.o.Pa||s.o.readyState===s.o.CLOSED)&&d(new B.e(U.Ra)),s.o.readyState===s.o.CONNECTING&&d(new B.e(U.Ca))),r=r instanceof Array||r instanceof ArrayBuffer?r.slice(t,t+n):r.buffer.slice(r.byteOffset+t,r.byteOffset+t+n),2===e.type&&(!s||s.o.readyState!==s.o.OPEN))return s&&s.o.readyState!==s.o.Pa&&s.o.readyState!==s.o.CLOSED||(s=Z.L.$a(e,i,a)),s.ab.push(r),n;try{return s.o.send(r),n}catch(e){d(new B.e(U.B))}},Rd:function(e,r){1===e.type&&e.G&&d(new B.e(U.Ra));var t=e.ua.shift();if(!t){if(1===e.type){if(n=Z.L.gb(e,e.Y,e.Z)){if(n.o.readyState===n.o.Pa||n.o.readyState===n.o.CLOSED)return k;d(new B.e(U.Ca))}d(new B.e(U.Ra))}d(new B.e(U.Ca))}var n=t.data.byteLength||t.data.length,i=t.data.byteOffset||0,a=t.data.buffer||t.data,s=Math.min(r,n),o={buffer:new Uint8Array(a,i,s),W:t.W,port:t.port};return 1===e.type&&s<n&&(t.data=new Uint8Array(a,i+s,n-s),e.ua.unshift(t)),o}}};function Ab(e,r,t){if(!(e=B.qa(e)))return V(U.V),-1;try{return B.write(e,I,r,t)}catch(e){return B.sc(e),-1}}function Cb(e){return(e=B.pc(e))?e.C:-1}function Db(e,r){return Ab(Cb(r),e,Bb(e))}function Eb(e,r){var t;return t=0<=(t=255&e)?t:Math.pow(2,g)+t,I[Eb.Cc>>0]=t,-1==Ab(Cb(r),Eb.Cc,1)?((t=B.pc(r))&&(t.error=i),-1):t}function Fb(e){Fb.$c||(E=E+4095&-4096,Fb.$c=i,w(z.bb),Fb.Wc=z.bb,z.bb=function(){A("cannot dynamically allocate, sbrk now has control")});var r=E;return 0!=e&&Fb.Wc(e),r}function Hb(e,r,t){window._broadwayOnPictureDecoded(e,r,t)}function Ib(){window._broadwayOnHeadersDecoded()}function Jb(e,r){return Kb=e,Lb=r,Mb?(0==e?(Nb=function(){setTimeout(Ob,r)},Pb="timeout"):1==e&&(Nb=function(){Qb(Ob)},Pb="rAF"),0):1}function Rb(e,r,t,n){p.noExitRuntime=i,w(!Mb,"emscripten_set_main_loop: there can only be one main loop function at once: call emscripten_cancel_main_loop to cancel the previous one before setting a new one with different parameters."),Mb=e,Sb=n;var a=Tb;Ob=function(){if(!H)if(0<Ub.length){var r=Date.now(),t=Ub.shift();if(t.ja(t.Xa),Vb){var i=Vb,s=0==i%1?i-1:Math.floor(i);Vb=t.dg?s:(8*i+(s+.5))/9}console.log('main loop blocker "'+t.name+'" took '+(Date.now()-r)+" ms"),p.setStatus&&(r=p.statusMessage||"Please wait...",t=Vb,i=Wb.ig,t?t<i?p.setStatus(r+" ("+(i-t)+"/"+i+")"):p.setStatus(r):p.setStatus("")),setTimeout(Ob,0)}else if(!(a<Tb))if(Xb=Xb+1|0,1==Kb&&1<Lb&&0!=Xb%Lb)Nb();else{"timeout"===Pb&&p.fg&&(p.fa("Looks like you are rendering without using requestAnimationFrame for the main loop. You should use 0 for the frame rate in emscripten_set_main_loop in order to use requestAnimationFrame, as that can greatly improve your frame rates!"),Pb="");e:if(!(H||p.preMainLoop&&p.preMainLoop()===m)){try{void 0!==n?z.Fa("vi",e,[n]):z.Fa("v",e)}catch(e){if(e instanceof ia)break e;e&&"object"==typeof e&&e.stack&&p.fa("exception thrown: "+[e,e.stack]),d(e)}p.postMainLoop&&p.postMainLoop()}a<Tb||("object"==typeof SDL&&SDL.ac&&SDL.ac.Pd&&SDL.ac.Pd(),Nb())}},r&&0<r?Jb(0,1e3/r):Jb(1,1),Nb(),t&&d("SimulateInfiniteLoop")}p._strlen=Bb,p._memset=Gb,p._broadwayOnPictureDecoded=Hb,p._broadwayOnHeadersDecoded=Ib;var Nb=k,Pb="",Tb=0,Mb=k,Sb=0,Kb=0,Lb=0,Xb=0,Ub=[],Wb={},Ob,Vb,Yb=m,rb=m,Zb=m,$b=g,ac=g,bc=0;function cc(e){var r=Date.now();if(0===bc)bc=r+1e3/60;else for(;r+2>=bc;)bc+=1e3/60;r=Math.max(bc-r,0),setTimeout(e,r)}function Qb(e){void 0===window?cc(e):(window.requestAnimationFrame||(window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||cc),window.requestAnimationFrame(e))}function xb(e){return{jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",bmp:"image/bmp",ogg:"audio/ogg",wav:"audio/wav",mp3:"audio/mpeg"}[e.substr(e.lastIndexOf(".")+1)]}function yb(e,r,t){function n(){t?t():d('Loading data file "'+e+'" failed.')}var a=new XMLHttpRequest;a.open("GET",e,i),a.responseType="arraybuffer",a.onload=function(){if(200==a.status||0==a.status&&a.response){var t=a.response;w(t,'Loading data file "'+e+'" failed (no arrayBuffer).'),r(new Uint8Array(t)),Za()}else n()},a.onerror=n,a.send(k),Ya()}var dc=[],tb,ub,vb,wb;function ec(){var e=p.canvas;dc.forEach(function(r){r(e.width,e.height)})}function fc(e,r,t){r&&t?(e.ae=r,e.Ad=t):(r=e.ae,t=e.Ad);var n=r,i=t;if(p.forcedAspectRatio&&0<p.forcedAspectRatio&&(n/i<p.forcedAspectRatio?n=Math.round(i*p.forcedAspectRatio):i=Math.round(n/p.forcedAspectRatio)),(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e.parentNode&&"undefined"!=typeof screen){var a=Math.min(screen.width/n,screen.height/i);n=Math.round(n*a),i=Math.round(i*a)}ac?(e.width!=n&&(e.width=n),e.height!=i&&(e.height=i),void 0!==e.style&&(e.style.removeProperty("width"),e.style.removeProperty("height"))):(e.width!=r&&(e.width=r),e.height!=t&&(e.height=t),void 0!==e.style&&(n!=r||i!=t?(e.style.setProperty("width",n+"px","important"),e.style.setProperty("height",i+"px","important")):(e.style.removeProperty("width"),e.style.removeProperty("height"))))}p._memcpy=gc,B.Zd(),R.unshift({ja:function(){!p.noFSInit&&!B.Ga.hb&&B.Ga()}}),Pa.push({ja:function(){B.vc=m}}),Qa.push({ja:function(){B.Qd()}}),p.FS_createFolder=B.hd,p.FS_createPath=B.ld,p.FS_createDataFile=B.xb,p.FS_createPreloadedFile=B.md,p.FS_createLazyFile=B.jd,p.FS_createLink=B.kd,p.FS_createDevice=B.X,bb=z.Ec(4),K[bb>>2]=0,R.unshift({ja:n()}),Qa.push({ja:n()});var lb=new z.Da;t&&(null("fs"),process.platform.match(/^win/)),R.push({ja:function(){Z.root=B.F(Z,{},k)}}),Eb.Cc=M([0],"i8",L),p.requestFullScreen=function(e,r){function t(){Yb=m;var e=a.parentNode;(document.webkitFullScreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.mozFullscreenElement||document.fullScreenElement||document.fullscreenElement||document.msFullScreenElement||document.msFullscreenElement||document.webkitCurrentFullScreenElement)===e?(a.cc=document.cancelFullScreen||document.mozCancelFullScreen||document.webkitCancelFullScreen||document.msExitFullscreen||document.exitFullscreen||n(),a.cc=a.cc.bind(document),$b&&a.Pb(),Yb=i,ac&&("undefined"!=typeof SDL&&(e=Ha[SDL.screen+0*z.ia>>2],K[SDL.screen+0*z.ia>>2]=8388608|e),ec())):(e.parentNode.insertBefore(a,e),e.parentNode.removeChild(e),ac&&("undefined"!=typeof SDL&&(e=Ha[SDL.screen+0*z.ia>>2],K[SDL.screen+0*z.ia>>2]=-8388609&e),ec())),p.onFullScreen&&p.onFullScreen(Yb),fc(a)}ac=r,void 0===($b=e)&&($b=i),void 0===ac&&(ac=m);var a=p.canvas;Zb||(Zb=i,document.addEventListener("fullscreenchange",t,m),document.addEventListener("mozfullscreenchange",t,m),document.addEventListener("webkitfullscreenchange",t,m),document.addEventListener("MSFullscreenChange",t,m));var s=document.createElement("div");a.parentNode.insertBefore(s,a),s.appendChild(a),s.Td=s.requestFullScreen||s.mozRequestFullScreen||s.msRequestFullscreen||(s.webkitRequestFullScreen?function(){s.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT)}:k),s.Td()},p.requestAnimationFrame=function(e){Qb(e)},p.setCanvasSize=function(e,r,t){fc(p.canvas,e,r),t||ec()},p.pauseMainLoop=function(){Nb=k,Tb++},p.resumeMainLoop=function(){Tb++;var e=Kb,r=Lb,t=Mb;Mb=k,Rb(t,0,m,Sb),Jb(e,r)},p.getUserMedia=function(){window.qc||(window.qc=navigator.getUserMedia||navigator.mozGetUserMedia),window.qc(g)},Ja=y=z.ub(D),Ka=Ja+Ma,La=E=z.ub(Ka),w(La<F,"TOTAL_MEMORY not big enough for stack"),p.Xc={Math:Math,Int8Array:Int8Array,Int16Array:Int16Array,Int32Array:Int32Array,Uint8Array:Uint8Array,Uint16Array:Uint16Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array},p.Yc={abort:A,assert:w,min:va,invoke_viiiii:function(e,r,t,n,i,a){try{p.dynCall_viiiii(e,r,t,n,i,a)}catch(e){"number"!=typeof e&&"longjmp"!==e&&d(e),$.setThrew(1,0)}},_broadwayOnPictureDecoded:Hb,_puts:function(e){var r=K[pb>>2];return 0>(e=Db(e,r))?e:0>Eb(10,r)?-1:e+1},_fflush:n(),_fputc:Eb,_send:function(e,r,t){return Z.wd(e)?Ab(e,r,t):(V(U.V),-1)},_pwrite:function(e,r,t,n){if(!(e=B.qa(e)))return V(U.V),-1;try{return B.write(e,I,r,t,n)}catch(e){return B.sc(e),-1}},_fputs:Db,_emscripten_set_main_loop:Rb,_abort:function(){p.abort()},___setErrNo:V,_sbrk:Fb,_mkport:zb,_emscripten_set_main_loop_timing:Jb,_emscripten_memcpy_big:function(e,r,t){return N.set(N.subarray(r,r+t),e),e},_fileno:Cb,_broadwayOnHeadersDecoded:Ib,_write:Ab,_time:function(e){var r=Date.now()/1e3|0;return e&&(K[e>>2]=r),r},_sysconf:function(e){switch(e){case 30:return 4096;case 132:case 133:case 12:case 137:case 138:case 15:case 235:case 16:case 17:case 18:case 19:case 20:case 149:case 13:case 10:case 236:case 153:case 9:case 21:case 22:case 159:case 154:case 14:case 77:case 78:case 139:case 80:case 81:case 79:case 82:case 68:case 67:case 164:case 11:case 29:case 47:case 48:case 95:case 52:case 51:case 46:return 200809;case 27:case 246:case 127:case 128:case 23:case 24:case 160:case 161:case 181:case 182:case 242:case 183:case 184:case 243:case 244:case 245:case 165:case 178:case 179:case 49:case 50:case 168:case 169:case 175:case 170:case 171:case 172:case 97:case 76:case 32:case 173:case 35:return-1;case 176:case 177:case 7:case 155:case 8:case 157:case 125:case 126:case 92:case 93:case 129:case 130:case 131:case 94:case 91:return 1;case 74:case 60:case 69:case 70:case 4:return 1024;case 31:case 42:case 72:return 32;case 87:case 26:case 33:return 2147483647;case 34:case 1:return 47839;case 38:case 36:return 99;case 43:case 37:return 2048;case 0:return 2097152;case 3:return 65536;case 28:return 32768;case 44:return 32767;case 75:return 16384;case 39:return 1e3;case 89:return 700;case 71:return 256;case 40:return 255;case 2:return 100;case 180:return 64;case 25:return 20;case 5:return 16;case 6:return 6;case 73:return 4;case 84:return"object"==typeof navigator&&navigator.hardwareConcurrency||1}return V(U.B),-1},___errno_location:function(){return bb},STACKTOP:y,STACK_MAX:Ka,tempDoublePtr:$a,ABORT:H,NaN:NaN,Infinity:1/0};var $=function(e,r,t){"use asm";var n=new e.Int8Array(t);var i=new e.Int16Array(t);var a=new e.Int32Array(t);var s=new e.Uint8Array(t);var o=new e.Uint16Array(t);var f=new e.Uint32Array(t);var u=new e.Float32Array(t);var l=new e.Float64Array(t);var c=r.STACKTOP|0;var h=r.STACK_MAX|0;var d=r.tempDoublePtr|0;var b=r.ABORT|0;var p=0;var m=0;var w=0;var v=0;var k=+r.NaN,g=+r.Infinity;var y=0,B=0,_=0,A=0,M=0.0,x=0,E=0,T=0,S=0.0;var U=0;var P=0;var R=0;var F=0;var C=0;var L=0;var D=0;var I=0;var O=0;var N=0;var z=e.Math.floor;var V=e.Math.abs;var q=e.Math.sqrt;var j=e.Math.pow;var Y=e.Math.cos;var H=e.Math.sin;var W=e.Math.tan;var G=e.Math.acos;var Z=e.Math.asin;var K=e.Math.atan;var X=e.Math.atan2;var $=e.Math.exp;var J=e.Math.log;var Q=e.Math.ceil;var ee=e.Math.imul;var re=r.abort;var te=r.assert;var ne=r.min;var ie=r.invoke_viiiii;var ae=r._broadwayOnPictureDecoded;var se=r._puts;var oe=r._fflush;var fe=r._fputc;var ue=r._send;var le=r._pwrite;var ce=r._fputs;var he=r._emscripten_set_main_loop;var de=r._abort;var be=r.___setErrNo;var pe=r._sbrk;var me=r._mkport;var we=r._emscripten_set_main_loop_timing;var ve=r._emscripten_memcpy_big;var ke=r._fileno;var ge=r._broadwayOnHeadersDecoded;var ye=r._write;var Be=r._time;var _e=r._sysconf;var Ae=r.___errno_location;var Me=0.0;function xe(e,r,t,n,f,u){e=e|0;r=r|0;t=t|0;n=n|0;f=f|0;u=u|0;var l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0,N=0,z=0,V=0,q=0,j=0,Y=0,H=0,W=0,G=0,Z=0,K=0;K=c;c=c+32|0;Z=K;p=a[f+4>>2]|0;G=(n>>>0)/(p>>>0)|0;W=G<<4;G=n-(ee(G,p)|0)<<4;a[Z+4>>2]=p;a[Z+8>>2]=a[f+8>>2];p=a[e>>2]|0;do{if((p|0)==1|(p|0)==0){x=a[r+144>>2]|0;l=e+4|0;d=a[e+200>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[l>>2]|0):0){if((a[d>>2]|0)>>>0<6){b=d+152|0;b=o[b>>1]|o[b+2>>1]<<16;h=1;y=b&65535;b=b>>>16&65535;v=a[d+104>>2]|0}else{h=1;y=0;b=0;v=-1}}else{h=0;y=0;b=0;v=-1}d=a[e+204>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[l>>2]|0):0){if((a[d>>2]|0)>>>0<6){B=d+172|0;B=o[B>>1]|o[B+2>>1]<<16;g=B&65535;m=1;w=a[d+108>>2]|0;B=B>>>16&65535}else{g=0;m=1;w=-1;B=0}}else{g=0;m=0;w=-1;B=0}do{if(!p){if(!((h|0)==0|(m|0)==0)){if((v|0)==0?((b&65535)<<16|y&65535|0)==0:0){d=0;b=0;break}if((w|0)==0?((B&65535)<<16|g&65535|0)==0:0){d=0;b=0}else H=16}else{d=0;b=0}}else H=16}while(0);if((H|0)==16){A=i[r+160>>1]|0;M=i[r+162>>1]|0;d=a[e+208>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[l>>2]|0):0){if((a[d>>2]|0)>>>0<6){k=d+172|0;p=a[d+108>>2]|0;k=o[k>>1]|o[k+2>>1]<<16;H=25}else{p=-1;k=0;H=25}}else H=20;do{if((H|0)==20){p=a[e+212>>2]|0;if((p|0)!=0?(a[p+4>>2]|0)==(a[l>>2]|0):0){if((a[p>>2]|0)>>>0>=6){p=-1;k=0;H=25;break}k=p+192|0;p=a[p+112>>2]|0;k=o[k>>1]|o[k+2>>1]<<16;H=25;break}if((h|0)==0|(m|0)!=0){p=-1;k=0;H=25}else d=y}}while(0);do{if((H|0)==25){h=(v|0)==(x|0);d=(w|0)==(x|0);if(((d&1)+(h&1)+((p|0)==(x|0)&1)|0)==1){if(h|d){d=h?y:g;b=h?b:B;break}d=k&65535;b=k>>>16&65535;break}d=y<<16>>16;l=g<<16>>16;p=k<<16>>16;if(g<<16>>16>y<<16>>16)h=l;else{h=d;d=(l|0)<(d|0)?l:d}if((h|0)<(p|0))p=h;else p=(d|0)>(p|0)?d:p;d=b<<16>>16;h=B<<16>>16;l=k>>16;if(B<<16>>16>b<<16>>16)b=h;else{b=d;d=(h|0)<(d|0)?h:d}if((b|0)>=(l|0))b=(d|0)>(l|0)?d:l;d=p&65535;b=b&65535}}while(0);d=(d&65535)+(A&65535)|0;b=(b&65535)+(M&65535)|0;if(((d<<16>>16)+8192|0)>>>0>16383){R=1;c=K;return R|0}if(((b<<16>>16)+2048|0)>>>0>4095){R=1;c=K;return R|0}else{d=d&65535;b=b&65535}}l=Ye(t,x)|0;if(!l){R=1;c=K;return R|0}else{R=e+132|0;U=e+136|0;S=e+140|0;T=e+144|0;E=e+148|0;M=e+152|0;A=e+156|0;_=e+160|0;B=e+164|0;y=e+168|0;h=e+172|0;p=e+176|0;m=e+180|0;w=e+184|0;v=e+188|0;P=e+192|0;i[e+192>>1]=d;i[e+194>>1]=b;P=o[P>>1]|o[P+2>>1]<<16;i[v>>1]=P;i[v+2>>1]=P>>>16;i[w>>1]=P;i[w+2>>1]=P>>>16;i[m>>1]=P;i[m+2>>1]=P>>>16;i[p>>1]=P;i[p+2>>1]=P>>>16;i[h>>1]=P;i[h+2>>1]=P>>>16;i[y>>1]=P;i[y+2>>1]=P>>>16;i[B>>1]=P;i[B+2>>1]=P>>>16;i[_>>1]=P;i[_+2>>1]=P>>>16;i[A>>1]=P;i[A+2>>1]=P>>>16;i[M>>1]=P;i[M+2>>1]=P>>>16;i[E>>1]=P;i[E+2>>1]=P>>>16;i[T>>1]=P;i[T+2>>1]=P>>>16;i[S>>1]=P;i[S+2>>1]=P>>>16;i[U>>1]=P;i[U+2>>1]=P>>>16;i[R>>1]=P;i[R+2>>1]=P>>>16;a[e+100>>2]=x;a[e+104>>2]=x;a[e+108>>2]=x;a[e+112>>2]=x;a[e+116>>2]=l;a[e+120>>2]=l;a[e+124>>2]=l;a[e+128>>2]=l;a[Z>>2]=l;Ne(u,e+132|0,Z,G,W,0,0,16,16);break}}else if((p|0)==3){_=i[r+160>>1]|0;A=i[r+162>>1]|0;T=a[r+144>>2]|0;g=e+4|0;b=a[e+200>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[g>>2]|0):0){if((a[b>>2]|0)>>>0<6){B=b+152|0;B=o[B>>1]|o[B+2>>1]<<16;d=1;v=B&65535;B=B>>>16&65535;b=a[b+104>>2]|0}else{d=1;v=0;B=0;b=-1}}else{d=0;v=0;B=0;b=-1}e:do{if((b|0)==(T|0)){d=v;b=B}else{b=a[e+204>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[g>>2]|0):0){if((a[b>>2]|0)>>>0<6){R=b+172|0;R=o[R>>1]|o[R+2>>1]<<16;k=b+188|0;p=a[b+108>>2]|0;l=a[b+112>>2]|0;d=R&65535;b=R>>>16&65535;k=o[k>>1]|o[k+2>>1]<<16}else{p=-1;l=-1;d=0;b=0;k=0}}else H=107;do{if((H|0)==107){b=a[e+212>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[g>>2]|0):0){if((a[b>>2]|0)>>>0>=6){p=-1;l=-1;d=0;b=0;k=0;break}k=b+192|0;p=-1;l=a[b+112>>2]|0;d=0;b=0;k=o[k>>1]|o[k+2>>1]<<16;break}if(!d){p=-1;l=-1;d=0;b=0;k=0}else{d=v;b=B;break e}}}while(0);h=(p|0)==(T|0);if(((h&1)+((l|0)==(T|0)&1)|0)==1){if(h)break;d=k&65535;b=k>>>16&65535;break}l=v<<16>>16;p=d<<16>>16;m=k<<16>>16;if(d<<16>>16>v<<16>>16){h=p;d=l}else{h=l;d=(p|0)<(l|0)?p:l}if((h|0)<(m|0))m=h;else m=(d|0)>(m|0)?d:m;d=B<<16>>16;h=b<<16>>16;l=k>>16;if(b<<16>>16>B<<16>>16)b=h;else{b=d;d=(h|0)<(d|0)?h:d}if((b|0)>=(l|0))b=(d|0)>(l|0)?d:l;d=m&65535;b=b&65535}}while(0);d=(d&65535)+(_&65535)|0;b=(b&65535)+(A&65535)|0;if(((d<<16>>16)+8192|0)>>>0>16383){R=1;c=K;return R|0}if(((b<<16>>16)+2048|0)>>>0>4095){R=1;c=K;return R|0}h=Ye(t,T)|0;if(!h){R=1;c=K;return R|0}_=e+132|0;M=e+136|0;x=e+140|0;A=e+144|0;R=e+164|0;P=e+168|0;U=e+172|0;B=e+176|0;i[e+176>>1]=d;i[e+178>>1]=b;B=o[B>>1]|o[B+2>>1]<<16;i[U>>1]=B;i[U+2>>1]=B>>>16;i[P>>1]=B;i[P+2>>1]=B>>>16;i[R>>1]=B;i[R+2>>1]=B>>>16;i[A>>1]=B;i[A+2>>1]=B>>>16;i[x>>1]=B;i[x+2>>1]=B>>>16;i[M>>1]=B;i[M+2>>1]=B>>>16;i[_>>1]=B;i[_+2>>1]=B>>>16;a[e+100>>2]=T;a[e+108>>2]=T;_=e+116|0;a[_>>2]=h;a[e+124>>2]=h;M=i[r+164>>1]|0;x=i[r+166>>1]|0;A=a[r+148>>2]|0;b=a[e+208>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[g>>2]|0):0){if((a[b>>2]|0)>>>0<6){w=b+172|0;b=a[b+108>>2]|0;p=1;w=o[w>>1]|o[w+2>>1]<<16}else{b=-1;p=1;w=0}}else{b=a[e+204>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[g>>2]|0):0){if((a[b>>2]|0)>>>0<6){w=b+176|0;b=a[b+108>>2]|0;p=1;w=o[w>>1]|o[w+2>>1]<<16}else{b=-1;p=1;w=0}}else{b=-1;p=0;w=0}}do{if((b|0)!=(A|0)){v=B&65535;b=B>>>16;y=b&65535;d=a[e+204>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[g>>2]|0):0){if((a[d>>2]|0)>>>0<6){g=d+188|0;g=o[g>>1]|o[g+2>>1]<<16;p=a[d+112>>2]|0;l=g&65535;g=g>>>16&65535}else{p=-1;l=0;g=0}}else if(!p){h=B;break}else{p=-1;l=0;g=0}h=(T|0)==(A|0);d=(p|0)==(A|0);if(((d&1)+(h&1)|0)==1){if(h){h=B;break}if(d){b=g&65535;h=b<<16|l&65535;break}else{h=w;b=w>>>16;break}}b=B<<16>>16;p=l<<16>>16;m=w<<16>>16;if(l<<16>>16>v<<16>>16)h=p;else{h=b;b=(p|0)<(b|0)?p:b}if((h|0)>=(m|0))h=(b|0)>(m|0)?b:m;d=B>>16;l=g<<16>>16;p=w>>16;if(g<<16>>16>y<<16>>16)b=l;else{b=d;d=(l|0)<(d|0)?l:d}if((b|0)>=(p|0))b=(d|0)>(p|0)?d:p}else{h=w;b=w>>>16}}while(0);h=(h&65535)+(M&65535)|0;d=(b&65535)+(x&65535)|0;if(((h<<16>>16)+8192|0)>>>0>16383){R=1;c=K;return R|0}if(((d<<16>>16)+2048|0)>>>0>4095){R=1;c=K;return R|0}b=Ye(t,A)|0;if(!b){R=1;c=K;return R|0}else{R=e+148|0;U=e+152|0;S=e+156|0;T=e+160|0;E=e+180|0;x=e+184|0;M=e+188|0;P=e+192|0;i[e+192>>1]=h;i[e+194>>1]=d;P=o[P>>1]|o[P+2>>1]<<16;i[M>>1]=P;i[M+2>>1]=P>>>16;i[x>>1]=P;i[x+2>>1]=P>>>16;i[E>>1]=P;i[E+2>>1]=P>>>16;i[T>>1]=P;i[T+2>>1]=P>>>16;i[S>>1]=P;i[S+2>>1]=P>>>16;i[U>>1]=P;i[U+2>>1]=P>>>16;i[R>>1]=P;i[R+2>>1]=P>>>16;a[e+104>>2]=A;a[e+112>>2]=A;P=e+120|0;a[P>>2]=b;a[e+128>>2]=b;a[Z>>2]=a[_>>2];Ne(u,e+132|0,Z,G,W,0,0,8,16);a[Z>>2]=a[P>>2];Ne(u,R,Z,G,W,8,0,8,16);break}}else if((p|0)==2){M=i[r+160>>1]|0;x=i[r+162>>1]|0;T=a[r+144>>2]|0;E=e+4|0;b=a[e+204>>2]|0;if((b|0)!=0?(a[b+4>>2]|0)==(a[E>>2]|0):0){if((a[b>>2]|0)>>>0<6){B=b+172|0;B=o[B>>1]|o[B+2>>1]<<16;h=1;b=a[b+108>>2]|0;w=B&65535;B=B>>>16&65535}else{h=1;b=-1;w=0;B=0}}else{h=0;b=-1;w=0;B=0}e:do{if((b|0)==(T|0)){d=w;b=B}else{d=a[e+200>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[E>>2]|0):0){if((a[d>>2]|0)>>>0<6){b=d+152|0;b=o[b>>1]|o[b+2>>1]<<16;m=1;v=b&65535;b=b>>>16&65535;p=a[d+104>>2]|0}else{m=1;v=0;b=0;p=-1}}else{m=0;v=0;b=0;p=-1}d=a[e+208>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[E>>2]|0):0){if((a[d>>2]|0)>>>0<6){k=d+172|0;d=a[d+108>>2]|0;k=o[k>>1]|o[k+2>>1]<<16}else{d=-1;k=0}}else H=54;do{if((H|0)==54){d=a[e+212>>2]|0;if((d|0)!=0?(a[d+4>>2]|0)==(a[E>>2]|0):0){if((a[d>>2]|0)>>>0>=6){d=-1;k=0;break}k=d+192|0;d=a[d+112>>2]|0;k=o[k>>1]|o[k+2>>1]<<16;break}if((m|0)==0|(h|0)!=0){d=-1;k=0}else{d=v;break e}}}while(0);h=(p|0)==(T|0);if((((d|0)==(T|0)&1)+(h&1)|0)==1){if(h){d=h?v:w;b=h?b:B;break}d=k&65535;b=k>>>16&65535;break}d=v<<16>>16;l=w<<16>>16;p=k<<16>>16;if(w<<16>>16>v<<16>>16)h=l;else{h=d;d=(l|0)<(d|0)?l:d}if((h|0)<(p|0))m=h;else m=(d|0)>(p|0)?d:p;d=b<<16>>16;h=B<<16>>16;l=k>>16;if(B<<16>>16>b<<16>>16)b=h;else{b=d;d=(h|0)<(d|0)?h:d}if((b|0)>=(l|0))b=(d|0)>(l|0)?d:l;d=m&65535;b=b&65535}}while(0);d=(d&65535)+(M&65535)|0;b=(b&65535)+(x&65535)|0;if(((d<<16>>16)+8192|0)>>>0>16383){R=1;c=K;return R|0}if(((b<<16>>16)+2048|0)>>>0>4095){R=1;c=K;return R|0}h=Ye(t,T)|0;if(!h){R=1;c=K;return R|0}x=e+132|0;_=e+136|0;A=e+140|0;M=e+144|0;m=e+148|0;p=e+152|0;R=e+156|0;y=e+160|0;i[e+160>>1]=d;i[e+162>>1]=b;y=o[y>>1]|o[y+2>>1]<<16;i[R>>1]=y;i[R+2>>1]=y>>>16;i[p>>1]=y;i[p+2>>1]=y>>>16;i[m>>1]=y;i[m+2>>1]=y>>>16;i[M>>1]=y;i[M+2>>1]=y>>>16;i[A>>1]=y;i[A+2>>1]=y>>>16;i[_>>1]=y;i[_+2>>1]=y>>>16;i[x>>1]=y;i[x+2>>1]=y>>>16;a[e+100>>2]=T;a[e+104>>2]=T;x=e+116|0;a[x>>2]=h;a[e+120>>2]=h;_=i[r+164>>1]|0;A=i[r+166>>1]|0;M=a[r+148>>2]|0;m=a[e+200>>2]|0;p=(m|0)==0;if((!p?(a[m+4>>2]|0)==(a[E>>2]|0):0)?(a[m>>2]|0)>>>0<6:0){B=m+184|0;B=o[B>>1]|o[B+2>>1]<<16;w=B&65535;B=B>>>16&65535;b=a[m+112>>2]|0}else{w=0;B=0;b=-1}do{if((b|0)!=(M|0)){v=y&65535;d=y>>>16;g=d&65535;if((!p?(a[m+4>>2]|0)==(a[E>>2]|0):0)?(a[m>>2]|0)>>>0<6:0){k=m+160|0;p=a[m+104>>2]|0;k=o[k>>1]|o[k+2>>1]<<16}else{p=-1;k=0}b=(T|0)==(M|0);if((((p|0)==(M|0)&1)+(b&1)|0)==1){h=b?y:k;b=b?d:k>>>16;break}b=w<<16>>16;p=y<<16>>16;m=k<<16>>16;if(v<<16>>16>w<<16>>16)h=p;else{h=b;b=(p|0)<(b|0)?p:b}if((h|0)>=(m|0))h=(b|0)>(m|0)?b:m;d=B<<16>>16;l=y>>16;p=k>>16;if(g<<16>>16>B<<16>>16)b=l;else{b=d;d=(l|0)<(d|0)?l:d}if((b|0)>=(p|0))b=(d|0)>(p|0)?d:p}else{b=B&65535;h=b<<16|w&65535}}while(0);h=(h&65535)+(_&65535)|0;d=(b&65535)+(A&65535)|0;if(((h<<16>>16)+8192|0)>>>0>16383){R=1;c=K;return R|0}if(((d<<16>>16)+2048|0)>>>0>4095){R=1;c=K;return R|0}b=Ye(t,M)|0;if(!b){R=1;c=K;return R|0}else{R=e+164|0;U=e+168|0;S=e+172|0;T=e+176|0;E=e+180|0;A=e+184|0;_=e+188|0;P=e+192|0;i[e+192>>1]=h;i[e+194>>1]=d;P=o[P>>1]|o[P+2>>1]<<16;i[_>>1]=P;i[_+2>>1]=P>>>16;i[A>>1]=P;i[A+2>>1]=P>>>16;i[E>>1]=P;i[E+2>>1]=P>>>16;i[T>>1]=P;i[T+2>>1]=P>>>16;i[S>>1]=P;i[S+2>>1]=P>>>16;i[U>>1]=P;i[U+2>>1]=P>>>16;i[R>>1]=P;i[R+2>>1]=P>>>16;a[e+108>>2]=M;a[e+112>>2]=M;P=e+124|0;a[P>>2]=b;a[e+128>>2]=b;a[Z>>2]=a[x>>2];Ne(u,e+132|0,Z,G,W,0,0,16,8);a[Z>>2]=a[P>>2];Ne(u,R,Z,G,W,0,8,16,8);break}}else{Y=e+4|0;F=0;e:while(1){S=r+(F<<2)+176|0;R=zr(a[S>>2]|0)|0;U=r+(F<<2)+192|0;a[e+(F<<2)+100>>2]=a[U>>2];P=Ye(t,a[U>>2]|0)|0;a[e+(F<<2)+116>>2]=P;if(!P){l=1;H=212;break}if(R){L=F<<2;D=e+(L<<2)+132|0;z=e+(L<<2)+134|0;V=L|1;I=e+(V<<2)+132|0;V=e+(V<<2)+134|0;q=L|2;O=e+(q<<2)+132|0;q=e+(q<<2)+134|0;j=L|3;N=e+(j<<2)+132|0;j=e+(j<<2)+134|0;C=0;do{T=i[r+(F<<4)+(C<<2)+208>>1]|0;E=i[r+(F<<4)+(C<<2)+210>>1]|0;P=jr(a[S>>2]|0)|0;d=a[U>>2]|0;v=tt(e,a[6288+(F<<7)+(P<<5)+(C<<3)>>2]|0)|0;w=s[6288+(F<<7)+(P<<5)+(C<<3)+4>>0]|0;if((v|0)!=0?(a[v+4>>2]|0)==(a[Y>>2]|0):0){if((a[v>>2]|0)>>>0<6){m=v+(w<<2)+132|0;m=o[m>>1]|o[m+2>>1]<<16;x=a[v+(w>>>2<<2)+100>>2]|0;b=m&65535;M=1;m=m>>>16&65535}else{x=-1;b=0;M=1;m=0}}else{x=-1;b=0;M=0;m=0}y=tt(e,a[5776+(F<<7)+(P<<5)+(C<<3)>>2]|0)|0;l=s[5776+(F<<7)+(P<<5)+(C<<3)+4>>0]|0;if((y|0)!=0?(a[y+4>>2]|0)==(a[Y>>2]|0):0){if((a[y>>2]|0)>>>0<6){p=y+(l<<2)+132|0;p=o[p>>1]|o[p+2>>1]<<16;A=1;_=a[y+(l>>>2<<2)+100>>2]|0;h=p&65535;p=p>>>16&65535}else{A=1;_=-1;h=0;p=0}}else{A=0;_=-1;h=0;p=0}B=tt(e,a[5264+(F<<7)+(P<<5)+(C<<3)>>2]|0)|0;y=s[5264+(F<<7)+(P<<5)+(C<<3)+4>>0]|0;if((B|0)!=0?(a[B+4>>2]|0)==(a[Y>>2]|0):0){if((a[B>>2]|0)>>>0<6){M=B+(y<<2)+132|0;M=o[M>>1]|o[M+2>>1]<<16;y=a[B+(y>>>2<<2)+100>>2]|0;H=180}else{M=0;y=-1;H=180}}else H=175;do{if((H|0)==175){H=0;B=tt(e,a[4752+(F<<7)+(P<<5)+(C<<3)>>2]|0)|0;y=s[4752+(F<<7)+(P<<5)+(C<<3)+4>>0]|0;if((B|0)!=0?(a[B+4>>2]|0)==(a[Y>>2]|0):0){if((a[B>>2]|0)>>>0>=6){M=0;y=-1;H=180;break}M=B+(y<<2)+132|0;M=o[M>>1]|o[M+2>>1]<<16;y=a[B+(y>>>2<<2)+100>>2]|0;H=180;break}if((M|0)==0|(A|0)!=0){M=0;y=-1;H=180}else{y=b;k=m}}}while(0);do{if((H|0)==180){l=(x|0)==(d|0);B=(_|0)==(d|0);if(((B&1)+(l&1)+((y|0)==(d|0)&1)|0)==1){if(l|B){y=l?b:h;k=l?m:p;break}y=M&65535;k=M>>>16&65535;break}g=b<<16>>16;B=h<<16>>16;l=M<<16>>16;if(h<<16>>16>b<<16>>16)y=B;else{y=g;g=(B|0)<(g|0)?B:g}if((y|0)<(l|0))B=y;else B=(g|0)>(l|0)?g:l;k=m<<16>>16;y=p<<16>>16;l=M>>16;if(p<<16>>16>m<<16>>16)v=y;else{v=k;k=(y|0)<(k|0)?y:k}if((v|0)>=(l|0))v=(k|0)>(l|0)?k:l;y=B&65535;k=v&65535}}while(0);T=(y&65535)+(T&65535)|0;m=T&65535;v=(k&65535)+(E&65535)|0;w=v&65535;if(((T<<16>>16)+8192|0)>>>0>16383){l=1;H=212;break e}if(((v<<16>>16)+2048|0)>>>0>4095){l=1;H=212;break e}if(!P){i[D>>1]=m;i[z>>1]=w;i[I>>1]=m;i[V>>1]=w;i[O>>1]=m;i[q>>1]=w;i[N>>1]=m;i[j>>1]=w}else if((P|0)==1){P=(C<<1)+L|0;i[e+(P<<2)+132>>1]=m;i[e+(P<<2)+134>>1]=w;P=P|1;i[e+(P<<2)+132>>1]=m;i[e+(P<<2)+134>>1]=w}else if((P|0)==2){P=C+L|0;i[e+(P<<2)+132>>1]=m;i[e+(P<<2)+134>>1]=w;P=P+2|0;i[e+(P<<2)+132>>1]=m;i[e+(P<<2)+134>>1]=w}else if((P|0)==3){P=C+L|0;i[e+(P<<2)+132>>1]=m;i[e+(P<<2)+134>>1]=w}C=C+1|0}while(C>>>0<R>>>0)}F=F+1|0;if(F>>>0>=4){H=201;break}}if((H|0)==201){b=0;do{a[Z>>2]=a[e+(b<<2)+116>>2];h=jr(a[r+(b<<2)+176>>2]|0)|0;l=b<<3&8;d=b>>>0<2?0:8;if(!h)Ne(u,e+(b<<2<<2)+132|0,Z,G,W,l,d,8,8);else if((h|0)==1){R=b<<2;Ne(u,e+(R<<2)+132|0,Z,G,W,l,d,8,4);Ne(u,e+((R|2)<<2)+132|0,Z,G,W,l,d|4,8,4)}else if((h|0)==2){R=b<<2;Ne(u,e+(R<<2)+132|0,Z,G,W,l,d,4,8);Ne(u,e+((R|1)<<2)+132|0,Z,G,W,l|4,d,4,8)}else{U=b<<2;Ne(u,e+(U<<2)+132|0,Z,G,W,l,d,4,4);P=l|4;Ne(u,e+((U|1)<<2)+132|0,Z,G,W,P,d,4,4);R=d|4;Ne(u,e+((U|2)<<2)+132|0,Z,G,W,l,R,4,4);Ne(u,e+((U|3)<<2)+132|0,Z,G,W,P,R,4,4)}b=b+1|0}while((b|0)!=4)}else if((H|0)==212){c=K;return l|0}}}while(0);if((a[e+196>>2]|0)>>>0>1){R=0;c=K;return R|0}if(!(a[e>>2]|0)){er(f,u);R=0;c=K;return R|0}else{rr(f,n,u,r+328|0);R=0;c=K;return R|0}return 0}function Ee(e,r,t,i,a,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0;E=c;c=c+144|0;h=E;if((t|0)>=0?!((t+1+u|0)>>>0>a>>>0|(i|0)<0|(l+i|0)>>>0>o>>>0):0)h=e;else{x=u+1|0;Te(e,h,t,i,a,o,x,l,x);Te(e+(ee(o,a)|0)|0,h+(ee(x,l)|0)|0,t,i,a,o,x,l,x);o=l;a=x;t=0;i=0}x=8-f|0;y=l>>>1;M=(y|0)==0;B=u>>>1;A=(B|0)==0;_=16-u|0;g=(a<<1)-u|0;v=a+1|0;k=a+2|0;p=B<<1;w=0;do{l=h+((ee((ee(w,o)|0)+i|0,a)|0)+t)|0;if(!(M|A)){m=r+(w<<6)|0;b=y;while(1){u=m;e=l;d=B;while(1){S=s[e>>0]|0;U=s[e+v>>0]|0;P=e;e=e+2|0;T=s[P+1>>0]|0;n[u+8>>0]=(((ee(U,f)|0)+(ee(s[P+a>>0]|0,x)|0)<<3)+32|0)>>>6;n[u>>0]=(((ee(T,f)|0)+(ee(S,x)|0)<<3)+32|0)>>>6;S=s[e>>0]|0;n[u+9>>0]=(((ee(s[P+k>>0]|0,f)|0)+(ee(U,x)|0)<<3)+32|0)>>>6;n[u+1>>0]=(((ee(S,f)|0)+(ee(T,x)|0)<<3)+32|0)>>>6;d=d+-1|0;if(!d)break;else u=u+2|0}b=b+-1|0;if(!b)break;else{m=m+(p+_)|0;l=l+(p+g)|0}}}w=w+1|0}while((w|0)!=2);c=E;return}function Te(e,r,t,n,i,a,s,o,f){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;a=a|0;s=s|0;o=o|0;f=f|0;var u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0;k=c;u=s+t|0;b=o+n|0;v=(t|0)<0|(u|0)>(i|0)?2:1;h=(b|0)<0?0-o|0:n;n=(u|0)<0?0-s|0:t;h=(h|0)>(a|0)?a:h;n=(n|0)>(i|0)?i:n;u=n+s|0;l=h+o|0;if((n|0)>0)e=e+n|0;if((h|0)>0)e=e+(ee(h,i)|0)|0;w=(n|0)<0?0-n|0:0;m=(u|0)>(i|0)?u-i|0:0;p=s-w-m|0;s=0-h|0;h=(h|0)<0?s:0;t=l-a|0;d=(l|0)>(a|0)?t:0;u=o-h|0;n=u-d|0;if(h){h=o+-1-((b|0)>0?b:0)|0;l=~a;l=(h|0)>(l|0)?h:l;h=~l;h=ee(l+((h|0)>0?h:0)+1|0,f)|0;l=r;while(1){gn[v&3](e,l,w,p,m);s=s+-1|0;if(!s)break;else l=l+f|0}r=r+h|0}if((u|0)!=(d|0)){l=o+-1|0;s=l-((b|0)>0?b:0)|0;u=~a;u=(s|0)>(u|0)?s:u;l=l-u|0;s=~u;s=o+a+-1-((l|0)<(a|0)?a:l)-u-((s|0)>0?s:0)|0;u=ee(s,f)|0;s=ee(s,i)|0;l=r;h=e;while(1){gn[v&3](h,l,w,p,m);n=n+-1|0;if(!n)break;else{l=l+f|0;h=h+i|0}}r=r+u|0;e=e+s|0}e=e+(0-i)|0;if(!d){c=k;return}while(1){gn[v&3](e,r,w,p,m);t=t+-1|0;if(!t)break;else r=r+f|0}c=k;return}function Se(e,r,t,i,a,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0;T=c;c=c+144|0;h=T;if(((t|0)>=0?!((u+t|0)>>>0>a>>>0|(i|0)<0):0)?(i+1+l|0)>>>0<=o>>>0:0)h=e;else{x=l+1|0;Te(e,h,t,i,a,o,u,x,u);Te(e+(ee(o,a)|0)|0,h+(ee(x,u)|0)|0,t,i,a,o,u,x,u);o=x;a=u;t=0;i=0}E=8-f|0;B=l>>>1;x=(B|0)==0;_=u>>>1;M=(_|0)==0;A=16-u|0;y=a<<1;g=y-u|0;k=y|1;v=a+1|0;p=_<<1;w=0;do{l=h+((ee((ee(w,o)|0)+i|0,a)|0)+t)|0;if(!(x|M)){m=r+(w<<6)|0;b=B;while(1){u=m;e=l;d=_;while(1){S=s[e+a>>0]|0;U=s[e>>0]|0;n[u+8>>0]=(((ee(S,E)|0)+(ee(s[e+y>>0]|0,f)|0)<<3)+32|0)>>>6;n[u>>0]=(((ee(U,E)|0)+(ee(S,f)|0)<<3)+32|0)>>>6;S=s[e+v>>0]|0;U=s[e+1>>0]|0;n[u+9>>0]=(((ee(S,E)|0)+(ee(s[e+k>>0]|0,f)|0)<<3)+32|0)>>>6;n[u+1>>0]=(((ee(U,E)|0)+(ee(S,f)|0)<<3)+32|0)>>>6;d=d+-1|0;if(!d)break;else{u=u+2|0;e=e+2|0}}b=b+-1|0;if(!b)break;else{m=m+(p+A)|0;l=l+(p+g)|0}}}w=w+1|0}while((w|0)!=2);c=T;return}function Ue(e,r,t,i,a,o,f,u,l,h){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;h=h|0;var d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0;C=c;c=c+176|0;d=C;if(((t|0)>=0?!((t+1+l|0)>>>0>a>>>0|(i|0)<0):0)?(i+1+h|0)>>>0<=o>>>0:0)d=e;else{E=l+1|0;x=h+1|0;Te(e,d,t,i,a,o,E,x,E);Te(e+(ee(o,a)|0)|0,d+(ee(x,E)|0)|0,t,i,a,o,E,x,E);o=x;a=E;t=0;i=0}R=8-f|0;F=8-u|0;E=h>>>1;U=(E|0)==0;x=a<<1;T=l>>>1;P=(T|0)==0;S=16-l|0;M=x-l|0;y=a+1|0;B=x|1;_=a+2|0;A=x+2|0;v=T<<1;g=0;do{l=d+((ee((ee(g,o)|0)+i|0,a)|0)+t)|0;if(!(U|P)){k=r+(g<<6)|0;w=E;while(1){p=s[l+a>>0]|0;h=k;e=l;b=(ee(p,u)|0)+(ee(s[l>>0]|0,F)|0)|0;p=(ee(s[l+x>>0]|0,u)|0)+(ee(p,F)|0)|0;m=T;while(1){D=s[e+y>>0]|0;L=(ee(D,u)|0)+(ee(s[e+1>>0]|0,F)|0)|0;D=(ee(s[e+B>>0]|0,u)|0)+(ee(D,F)|0)|0;O=((ee(b,R)|0)+32+(ee(L,f)|0)|0)>>>6;n[h+8>>0]=((ee(p,R)|0)+32+(ee(D,f)|0)|0)>>>6;n[h>>0]=O;O=e;e=e+2|0;I=s[O+_>>0]|0;b=(ee(I,u)|0)+(ee(s[e>>0]|0,F)|0)|0;p=(ee(s[O+A>>0]|0,u)|0)+(ee(I,F)|0)|0;L=((ee(L,R)|0)+32+(ee(b,f)|0)|0)>>>6;n[h+9>>0]=((ee(D,R)|0)+32+(ee(p,f)|0)|0)>>>6;n[h+1>>0]=L;m=m+-1|0;if(!m)break;else h=h+2|0}w=w+-1|0;if(!w)break;else{k=k+(v+S)|0;l=l+(v+M)|0}}}g=g+1|0}while((g|0)!=2);c=C;return}function Pe(e,r,t,i,a,o,f,u){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;var l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0;w=c;c=c+448|0;l=w;if(((t|0)>=0?!((f+t|0)>>>0>a>>>0|(i|0)<0):0)?(i+5+u|0)>>>0<=o>>>0:0)l=e;else{Te(e,l,t,i,a,o,f,u+5|0,f);a=f;t=0;i=0}o=t+a+(ee(i,a)|0)|0;e=u>>>2;if(!e){c=w;return}d=a<<2;m=0-a|0;b=m<<1;p=a<<1;if(!f){c=w;return}else{h=l+o|0;t=l+(o+(a*5|0))|0}while(1){l=f;o=r;u=h;i=t;while(1){g=s[i+b>>0]|0;y=s[i+m>>0]|0;B=s[i+a>>0]|0;A=s[i>>0]|0;_=B+g|0;v=s[u+p>>0]|0;n[o+48>>0]=n[((s[i+p>>0]|0)+16-_-(_<<2)+v+((A+y|0)*20|0)>>5)+3984>>0]|0;_=v+A|0;k=s[u+a>>0]|0;n[o+32>>0]=n[(B+16-_-(_<<2)+k+((y+g|0)*20|0)>>5)+3984>>0]|0;_=k+y|0;B=s[u>>0]|0;n[o+16>>0]=n[(A+16-_-(_<<2)+B+((v+g|0)*20|0)>>5)+3984>>0]|0;g=B+g|0;n[o>>0]=n[(y+16-g-(g<<2)+(s[u+m>>0]|0)+((k+v|0)*20|0)>>5)+3984>>0]|0;l=l+-1|0;if(!l)break;else{o=o+1|0;u=u+1|0;i=i+1|0}}e=e+-1|0;if(!e)break;else{r=r+64|0;h=h+d|0;t=t+d|0}}c=w;return}function Re(e,r,t,i,a,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0;y=c;c=c+448|0;h=y;if(((t|0)>=0?!((f+t|0)>>>0>a>>>0|(i|0)<0):0)?(i+5+u|0)>>>0<=o>>>0:0)h=e;else{Te(e,h,t,i,a,o,f,u+5|0,f);a=f;t=0;i=0}o=t+a+(ee(i,a)|0)|0;e=u>>>2;if(!e){c=y;return}g=(f|0)==0;v=(a<<2)-f|0;k=64-f|0;w=0-a|0;p=w<<1;m=a<<1;t=h+o|0;i=h+(o+(ee(a,l+2|0)|0))|0;h=h+(o+(a*5|0))|0;while(1){if(g){o=r;u=i}else{u=i+f|0;o=r+f|0;l=f;b=t;d=h;while(1){A=s[d+p>>0]|0;M=s[d+w>>0]|0;x=s[d+a>>0]|0;T=s[d>>0]|0;E=x+A|0;B=s[b+m>>0]|0;n[r+48>>0]=((s[((s[d+m>>0]|0)+16-E-(E<<2)+B+((T+M|0)*20|0)>>5)+3984>>0]|0)+1+(s[i+m>>0]|0)|0)>>>1;E=B+T|0;_=s[b+a>>0]|0;n[r+32>>0]=((s[(x+16-E-(E<<2)+_+((M+A|0)*20|0)>>5)+3984>>0]|0)+1+(s[i+a>>0]|0)|0)>>>1;E=_+M|0;x=s[b>>0]|0;n[r+16>>0]=((s[(T+16-E-(E<<2)+x+((B+A|0)*20|0)>>5)+3984>>0]|0)+1+(s[i>>0]|0)|0)>>>1;A=x+A|0;n[r>>0]=((s[(M+16-A-(A<<2)+(s[b+w>>0]|0)+((_+B|0)*20|0)>>5)+3984>>0]|0)+1+(s[i+w>>0]|0)|0)>>>1;l=l+-1|0;if(!l)break;else{r=r+1|0;b=b+1|0;i=i+1|0;d=d+1|0}}t=t+f|0;h=h+f|0}e=e+-1|0;if(!e)break;else{r=o+k|0;t=t+v|0;i=u+v|0;h=h+v|0}}c=y;return}function Fe(e,r,t,i,a,o,f,u){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;var l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0;g=c;c=c+448|0;l=g;if((t|0)>=0?!((t+5+f|0)>>>0>a>>>0|(i|0)<0|(u+i|0)>>>0>o>>>0):0)l=e;else{d=f+5|0;Te(e,l,t,i,a,o,d,u,d);a=d;t=0;i=0}if(!u){c=g;return}w=f>>>2;k=(w|0)==0;v=a-f|0;m=16-f|0;p=w<<2;e=r;l=l+(t+5+(ee(i,a)|0))|0;b=u;while(1){if(k)o=e;else{o=e+p|0;t=l;a=s[l+-1>>0]|0;u=s[l+-2>>0]|0;h=s[l+-3>>0]|0;d=s[l+-4>>0]|0;f=s[l+-5>>0]|0;r=w;while(1){i=d+a|0;y=d;d=s[t>>0]|0;n[e>>0]=n[(f+16-i-(i<<2)+d+((h+u|0)*20|0)>>5)+3984>>0]|0;f=d+h|0;i=h;h=s[t+1>>0]|0;n[e+1>>0]=n[(y+16-f-(f<<2)+h+((u+a|0)*20|0)>>5)+3984>>0]|0;f=h+u|0;y=u;u=s[t+2>>0]|0;n[e+2>>0]=n[(i+16-f-(f<<2)+u+((d+a|0)*20|0)>>5)+3984>>0]|0;f=u+a|0;i=s[t+3>>0]|0;n[e+3>>0]=n[(y+16-f-(f<<2)+i+((h+d|0)*20|0)>>5)+3984>>0]|0;r=r+-1|0;if(!r)break;else{f=a;e=e+4|0;t=t+4|0;a=i}}l=l+p|0}b=b+-1|0;if(!b)break;else{e=o+m|0;l=l+v|0}}c=g;return}function Ce(e,r,t,i,a,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0;y=c;c=c+448|0;h=y;if((t|0)>=0?!((t+5+f|0)>>>0>a>>>0|(i|0)<0|(u+i|0)>>>0>o>>>0):0)h=e;else{b=f+5|0;Te(e,h,t,i,a,o,b,u,b);a=b;t=0;i=0}if(!u){c=y;return}v=f>>>2;g=(v|0)==0;k=a-f|0;w=16-f|0;m=(l|0)!=0;p=v<<2;e=r;h=h+(t+5+(ee(i,a)|0))|0;while(1){if(g)o=e;else{o=e+p|0;l=h;t=s[h+-1>>0]|0;a=s[h+-2>>0]|0;d=s[h+-3>>0]|0;b=s[h+-4>>0]|0;f=s[h+-5>>0]|0;r=v;while(1){i=b+t|0;B=b;b=s[l>>0]|0;n[e>>0]=((m?a:d)+1+(s[(f+16-i-(i<<2)+b+((d+a|0)*20|0)>>5)+3984>>0]|0)|0)>>>1;f=b+d|0;i=d;d=s[l+1>>0]|0;n[e+1>>0]=((m?t:a)+1+(s[(B+16-f-(f<<2)+d+((a+t|0)*20|0)>>5)+3984>>0]|0)|0)>>>1;f=d+a|0;B=a;a=s[l+2>>0]|0;n[e+2>>0]=((m?b:t)+1+(s[(i+16-f-(f<<2)+a+((b+t|0)*20|0)>>5)+3984>>0]|0)|0)>>>1;f=a+t|0;i=s[l+3>>0]|0;n[e+3>>0]=((m?d:b)+1+(s[(B+16-f-(f<<2)+i+((d+b|0)*20|0)>>5)+3984>>0]|0)|0)>>>1;r=r+-1|0;if(!r)break;else{f=t;e=e+4|0;l=l+4|0;t=i}}h=h+p|0}u=u+-1|0;if(!u)break;else{e=o+w|0;h=h+k|0}}c=y;return}function Le(e,r,t,i,a,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0;M=c;c=c+448|0;h=M;if(((t|0)>=0?!((t+5+f|0)>>>0>a>>>0|(i|0)<0):0)?(i+5+u|0)>>>0<=o>>>0:0)h=e;else{A=f+5|0;Te(e,h,t,i,a,o,A,u+5|0,A);a=A;t=0;i=0}e=(ee(i,a)|0)+t|0;A=(l&1|2)+a+e|0;d=h+A|0;if(!u){c=M;return}g=f>>>2;B=(g|0)==0;y=a-f|0;_=16-f|0;k=g<<2;e=h+((ee(a,l>>>1&1|2)|0)+5+e)|0;v=u;while(1){if(!B){w=r+k|0;o=e;t=s[e+-1>>0]|0;i=s[e+-2>>0]|0;b=s[e+-3>>0]|0;p=s[e+-4>>0]|0;l=s[e+-5>>0]|0;m=g;while(1){x=p+t|0;E=p;p=s[o>>0]|0;n[r>>0]=n[(l+16-x-(x<<2)+p+((b+i|0)*20|0)>>5)+3984>>0]|0;x=p+b|0;l=b;b=s[o+1>>0]|0;n[r+1>>0]=n[(E+16-x-(x<<2)+b+((i+t|0)*20|0)>>5)+3984>>0]|0;x=b+i|0;E=i;i=s[o+2>>0]|0;n[r+2>>0]=n[(l+16-x-(x<<2)+i+((p+t|0)*20|0)>>5)+3984>>0]|0;x=i+t|0;l=s[o+3>>0]|0;n[r+3>>0]=n[(E+16-x-(x<<2)+l+((b+p|0)*20|0)>>5)+3984>>0]|0;m=m+-1|0;if(!m)break;else{x=t;r=r+4|0;o=o+4|0;t=l;l=x}}r=w;e=e+k|0}v=v+-1|0;if(!v)break;else{r=r+_|0;e=e+y|0}}e=u>>>2;if(!e){c=M;return}k=(f|0)==0;p=(a<<2)-f|0;b=64-f|0;m=0-a|0;v=m<<1;w=a<<1;r=r+(_-(u<<4))|0;o=h+(A+(a*5|0))|0;l=e;while(1){if(k){e=r;h=d}else{e=r+f|0;h=r;t=d;i=o;r=f;while(1){_=s[i+v>>0]|0;B=s[i+m>>0]|0;g=s[i+a>>0]|0;E=s[i>>0]|0;A=g+_|0;u=s[t+w>>0]|0;x=h+48|0;n[x>>0]=((s[((s[i+w>>0]|0)+16-A-(A<<2)+u+((E+B|0)*20|0)>>5)+3984>>0]|0)+1+(s[x>>0]|0)|0)>>>1;x=u+E|0;A=s[t+a>>0]|0;y=h+32|0;n[y>>0]=((s[(g+16-x-(x<<2)+A+((B+_|0)*20|0)>>5)+3984>>0]|0)+1+(s[y>>0]|0)|0)>>>1;y=s[t>>0]|0;x=A+B|0;g=h+16|0;n[g>>0]=((s[(E+16-x-(x<<2)+y+((u+_|0)*20|0)>>5)+3984>>0]|0)+1+(s[g>>0]|0)|0)>>>1;_=y+_|0;n[h>>0]=((s[(B+16-_-(_<<2)+(s[t+m>>0]|0)+((A+u|0)*20|0)>>5)+3984>>0]|0)+1+(s[h>>0]|0)|0)>>>1;r=r+-1|0;if(!r)break;else{h=h+1|0;t=t+1|0;i=i+1|0}}h=d+f|0;o=o+f|0}l=l+-1|0;if(!l)break;else{r=e+b|0;d=h+p|0;o=o+p|0}}c=M;return}function De(e,r,t,i,o,f,u,l){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0;_=c;c=c+1792|0;h=_+1344|0;B=_;if(((t|0)>=0?!((t+5+u|0)>>>0>o>>>0|(i|0)<0):0)?(i+5+l|0)>>>0<=f>>>0:0){b=l+5|0;h=e;d=t+5|0}else{d=u+5|0;b=l+5|0;Te(e,h,t,i,o,f,d,b,d);o=d;d=5;i=0}if(b){k=u>>>2;y=(k|0)==0;v=o-u|0;g=k<<2;t=B;h=h+(d+(ee(i,o)|0))|0;while(1){if(y)e=t;else{e=t+(g<<2)|0;i=h;o=s[h+-1>>0]|0;f=s[h+-2>>0]|0;p=s[h+-3>>0]|0;m=s[h+-4>>0]|0;d=s[h+-5>>0]|0;w=k;while(1){A=m+o|0;M=m;m=s[i>>0]|0;a[t>>2]=d-A-(A<<2)+m+((p+f|0)*20|0);A=m+p|0;d=p;p=s[i+1>>0]|0;a[t+4>>2]=M-A+p-(A<<2)+((f+o|0)*20|0);A=p+f|0;M=f;f=s[i+2>>0]|0;a[t+8>>2]=d-A+f-(A<<2)+((m+o|0)*20|0);A=f+o|0;d=s[i+3>>0]|0;a[t+12>>2]=M-A+d-(A<<2)+((p+m|0)*20|0);w=w+-1|0;if(!w)break;else{A=o;t=t+16|0;i=i+4|0;o=d;d=A}}h=h+g|0}b=b+-1|0;if(!b)break;else{t=e;h=h+v|0}}}o=l>>>2;if(!o){c=_;return}g=(u|0)==0;v=64-u|0;p=u*3|0;k=0-u|0;m=k<<1;w=u<<1;i=r;e=B+(u<<2)|0;h=B+(u*6<<2)|0;b=o;while(1){if(g)o=i;else{o=i+u|0;t=e;f=h;d=u;while(1){r=a[f+(m<<2)>>2]|0;B=a[f+(k<<2)>>2]|0;M=a[f+(u<<2)>>2]|0;x=a[f>>2]|0;A=M+r|0;y=a[t+(w<<2)>>2]|0;n[i+48>>0]=n[((a[f+(w<<2)>>2]|0)+512-A-(A<<2)+y+((x+B|0)*20|0)>>10)+3984>>0]|0;A=y+x|0;l=a[t+(u<<2)>>2]|0;n[i+32>>0]=n[(M+512-A-(A<<2)+l+((B+r|0)*20|0)>>10)+3984>>0]|0;A=a[t>>2]|0;M=l+B|0;n[i+16>>0]=n[(x+512-M-(M<<2)+A+((y+r|0)*20|0)>>10)+3984>>0]|0;r=A+r|0;n[i>>0]=n[(B+512-r-(r<<2)+(a[t+(k<<2)>>2]|0)+((l+y|0)*20|0)>>10)+3984>>0]|0;d=d+-1|0;if(!d)break;else{i=i+1|0;t=t+4|0;f=f+4|0}}e=e+(u<<2)|0;h=h+(u<<2)|0}b=b+-1|0;if(!b)break;else{i=o+v|0;e=e+(p<<2)|0;h=h+(p<<2)|0}}c=_;return}function Ie(e,r,t,i,o,f,u,l,h){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;u=u|0;l=l|0;h=h|0;var d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0;A=c;c=c+1792|0;d=A+1344|0;_=A;if(((t|0)>=0?!((t+5+u|0)>>>0>o>>>0|(i|0)<0):0)?(i+5+l|0)>>>0<=f>>>0:0){b=l+5|0;d=e;t=t+5|0}else{m=u+5|0;b=l+5|0;Te(e,d,t,i,o,f,m,b,m);o=m;t=5;i=0}if(b){y=u>>>2;k=(y|0)==0;g=o-u|0;B=y<<2;p=_;d=d+(t+(ee(i,o)|0))|0;v=b;while(1){if(k)e=p;else{e=p+(B<<2)|0;i=d;t=s[d+-1>>0]|0;o=s[d+-2>>0]|0;f=s[d+-3>>0]|0;m=s[d+-4>>0]|0;b=s[d+-5>>0]|0;w=y;while(1){M=m+t|0;x=m;m=s[i>>0]|0;a[p>>2]=b-M-(M<<2)+m+((f+o|0)*20|0);M=m+f|0;b=f;f=s[i+1>>0]|0;a[p+4>>2]=x-M+f-(M<<2)+((o+t|0)*20|0);M=f+o|0;x=o;o=s[i+2>>0]|0;a[p+8>>2]=b-M+o-(M<<2)+((m+t|0)*20|0);M=o+t|0;b=s[i+3>>0]|0;a[p+12>>2]=x-M+b-(M<<2)+((f+m|0)*20|0);w=w+-1|0;if(!w)break;else{M=t;p=p+16|0;i=i+4|0;t=b;b=M}}d=d+B|0}v=v+-1|0;if(!v)break;else{p=e;d=d+g|0}}}t=l>>>2;if(!t){c=A;return}B=(u|0)==0;g=64-u|0;m=u*3|0;y=0-u|0;k=y<<1;v=u<<1;e=_+(u<<2)|0;d=_+((ee(h+2|0,u)|0)+u<<2)|0;o=_+(u*6<<2)|0;w=t;while(1){if(B){i=r;t=d}else{t=d+(u<<2)|0;i=r+u|0;p=e;f=o;b=u;while(1){h=a[f+(k<<2)>>2]|0;l=a[f+(y<<2)>>2]|0;E=a[f+(u<<2)>>2]|0;T=a[f>>2]|0;x=E+h|0;M=a[p+(v<<2)>>2]|0;n[r+48>>0]=((s[((a[f+(v<<2)>>2]|0)+512-x-(x<<2)+M+((T+l|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[d+(v<<2)>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;x=M+T|0;_=a[p+(u<<2)>>2]|0;n[r+32>>0]=((s[(E+512-x-(x<<2)+_+((l+h|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[d+(u<<2)>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;x=a[p>>2]|0;E=_+l|0;n[r+16>>0]=((s[(T+512-E-(E<<2)+x+((M+h|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[d>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;h=x+h|0;n[r>>0]=((s[(l+512-h-(h<<2)+(a[p+(y<<2)>>2]|0)+((_+M|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[d+(y<<2)>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;b=b+-1|0;if(!b)break;else{r=r+1|0;p=p+4|0;d=d+4|0;f=f+4|0}}e=e+(u<<2)|0;o=o+(u<<2)|0}w=w+-1|0;if(!w)break;else{r=i+g|0;e=e+(m<<2)|0;d=t+(m<<2)|0;o=o+(m<<2)|0}}c=A;return}function Oe(e,r,t,i,o,f,u,l,h){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;u=u|0;l=l|0;h=h|0;var d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0;E=c;c=c+1792|0;d=E+1344|0;x=E;M=u+5|0;if(((t|0)>=0?!((t+5+u|0)>>>0>o>>>0|(i|0)<0):0)?(i+5+l|0)>>>0<=f>>>0:0)d=e;else{Te(e,d,t,i,o,f,M,l+5|0,M);o=M;t=0;i=0}v=t+o+(ee(i,o)|0)|0;i=l>>>2;if(i){A=(M|0)==0;_=(o<<2)-u+-5|0;k=M*3|0;p=0-o|0;B=p<<1;y=o<<1;g=M<<1;m=-5-u|0;f=x+(M<<2)|0;w=d+v|0;t=d+(v+(o*5|0))|0;while(1){if(A)v=f;else{v=f+(M<<2)|0;d=w;e=t;b=M;while(1){U=s[e+B>>0]|0;P=s[e+p>>0]|0;F=s[e+o>>0]|0;C=s[e>>0]|0;R=F+U|0;T=s[d+y>>0]|0;a[f+(g<<2)>>2]=(s[e+y>>0]|0)-R-(R<<2)+T+((C+P|0)*20|0);R=T+C|0;S=s[d+o>>0]|0;a[f+(M<<2)>>2]=F-R+S-(R<<2)+((P+U|0)*20|0);R=s[d>>0]|0;F=S+P|0;a[f>>2]=C-F+R-(F<<2)+((T+U|0)*20|0);U=R+U|0;a[f+(m<<2)>>2]=P-U+(s[d+p>>0]|0)-(U<<2)+((S+T|0)*20|0);b=b+-1|0;if(!b)break;else{f=f+4|0;d=d+1|0;e=e+1|0}}w=w+M|0;t=t+M|0}i=i+-1|0;if(!i)break;else{f=v+(k<<2)|0;w=w+_|0;t=t+_|0}}}if(!l){c=E;return}y=u>>>2;B=(y|0)==0;g=16-u|0;k=y<<2;o=x+(h+2<<2)|0;i=x+20|0;while(1){if(B)t=o;else{t=o+(k<<2)|0;v=r;d=i;f=a[i+-4>>2]|0;b=a[i+-8>>2]|0;p=a[i+-12>>2]|0;m=a[i+-16>>2]|0;e=a[i+-20>>2]|0;w=y;while(1){h=m+f|0;u=m;m=a[d>>2]|0;n[v>>0]=((s[(e+512-h-(h<<2)+m+((p+b|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[o>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;h=m+p|0;e=p;p=a[d+4>>2]|0;n[v+1>>0]=((s[(u+512-h-(h<<2)+p+((b+f|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[o+4>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;h=p+b|0;u=b;b=a[d+8>>2]|0;n[v+2>>0]=((s[(e+512-h-(h<<2)+b+((m+f|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[o+8>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;h=b+f|0;e=a[d+12>>2]|0;n[v+3>>0]=((s[(u+512-h-(h<<2)+e+((p+m|0)*20|0)>>10)+3984>>0]|0)+1+(s[((a[o+12>>2]|0)+16>>5)+3984>>0]|0)|0)>>>1;w=w+-1|0;if(!w)break;else{h=f;v=v+4|0;o=o+16|0;d=d+16|0;f=e;e=h}}r=r+k|0;i=i+(k<<2)|0}l=l+-1|0;if(!l)break;else{r=r+g|0;o=t+20|0;i=i+20|0}}c=E;return}function Ne(e,r,t,n,s,o,f,u,l){e=e|0;r=r|0;t=t|0;n=n|0;s=s|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0;_=c;m=e+((f<<4)+o)|0;g=i[r>>1]|0;B=r+2|0;k=i[B>>1]|0;v=t+4|0;p=a[v>>2]<<4;w=t+8|0;b=a[w>>2]<<4;n=o+n|0;h=n+(g>>2)|0;s=f+s|0;d=s+(k>>2)|0;do{switch(a[6800+((g&3)<<4)+((k&3)<<2)>>2]|0){case 10:{De(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l);break}case 6:{Oe(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,0);break}case 4:{Ce(a[t>>2]|0,m,h+-2|0,d,p,b,u,l,0);break}case 1:{Re(a[t>>2]|0,m,h,d+-2|0,p,b,u,l,0);break}case 2:{Pe(a[t>>2]|0,m,h,d+-2|0,p,b,u,l);break}case 12:{Ce(a[t>>2]|0,m,h+-2|0,d,p,b,u,l,1);break}case 14:{Oe(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,1);break}case 7:{Le(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,2);break}case 13:{Le(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,1);break}case 5:{Le(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,0);break}case 0:{Te(a[t>>2]|0,m,h,d,p,b,u,l,16);break}case 9:{Ie(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,0);break}case 8:{Fe(a[t>>2]|0,m,h+-2|0,d,p,b,u,l);break}case 3:{Re(a[t>>2]|0,m,h,d+-2|0,p,b,u,l,1);break}case 11:{Ie(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,1);break}default:Le(a[t>>2]|0,m,h+-2|0,d+-2|0,p,b,u,l,3)}}while(0);g=(o>>>1)+256+(f>>>1<<3)|0;y=e+g|0;k=a[t>>2]|0;h=a[v>>2]|0;f=a[w>>2]|0;w=h<<3;v=f<<3;p=i[r>>1]|0;t=(p>>3)+(n>>>1)|0;m=i[B>>1]|0;b=(m>>3)+(s>>>1)|0;p=p&7;m=m&7;s=u>>>1;o=l>>>1;f=ee(h<<8,f)|0;n=k+f|0;h=(p|0)!=0;d=(m|0)!=0;if(h&d){Ue(n,y,t,b,w,v,p,m,s,o);c=_;return}if(h){Ee(n,y,t,b,w,v,p,s,o);c=_;return}if(d){Se(n,y,t,b,w,v,m,s,o);c=_;return}else{Te(n,y,t,b,w,v,s,o,8);Te(k+((ee(v,w)|0)+f)|0,e+(g+64)|0,t,b,w,v,s,o,8);c=_;return}}function ze(e,r,t,i,a){e=e|0;r=r|0;t=t|0;i=i|0;a=a|0;var s=0,o=0,f=0;f=c;if(t){mn(r|0,n[e>>0]|0,t|0)|0;r=r+t|0}if(i){o=r+i|0;s=i;t=e;while(1){n[r>>0]=n[t>>0]|0;s=s+-1|0;if(!s)break;else{r=r+1|0;t=t+1|0}}r=o;e=e+i|0}if(!a){c=f;return}mn(r|0,n[e+-1>>0]|0,a|0)|0;c=f;return}function Ve(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;t=c;ln(r,e,n);c=t;return}function qe(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0;m=c;i=a[e+40>>2]|0;if(i){o=a[e>>2]|0;u=e+32|0;f=0;do{if(((a[o+(f*40|0)+20>>2]|0)+-1|0)>>>0<2){s=a[o+(f*40|0)+12>>2]|0;if(s>>>0>t>>>0)s=s-(a[u>>2]|0)|0;a[o+(f*40|0)+8>>2]=s}f=f+1|0}while((f|0)!=(i|0))}if(!(a[r>>2]|0)){p=0;c=m;return p|0}s=a[r+4>>2]|0;if(s>>>0>=3){p=0;c=m;return p|0}b=e+32|0;p=e+24|0;d=e+4|0;i=t;h=0;e:while(1){r:do{if(s>>>0<2){u=a[r+(h*12|0)+8>>2]|0;if(!s){s=i-u|0;if((s|0)<0)s=(a[b>>2]|0)+s|0}else{l=u+i|0;s=a[b>>2]|0;s=l-((l|0)<(s|0)?0:s)|0}if(s>>>0>t>>>0)i=s-(a[b>>2]|0)|0;else i=s;f=a[p>>2]|0;if(!f){i=1;s=37;break e}u=a[e>>2]|0;l=0;while(1){o=a[u+(l*40|0)+20>>2]|0;if((o+-1|0)>>>0<2?(a[u+(l*40|0)+8>>2]|0)==(i|0):0){i=s;break r}l=l+1|0;if(l>>>0>=f>>>0){i=1;s=37;break e}}}else{f=a[r+(h*12|0)+12>>2]|0;o=a[p>>2]|0;if(!o){i=1;s=37;break e}u=a[e>>2]|0;s=0;while(1){if((a[u+(s*40|0)+20>>2]|0)==3?(a[u+(s*40|0)+8>>2]|0)==(f|0):0){o=3;l=s;break r}s=s+1|0;if(s>>>0>=o>>>0){i=1;s=37;break e}}}}while(0);if(!((l|0)>-1&o>>>0>1)){i=1;s=37;break}if(h>>>0<n>>>0){u=n;do{f=u;u=u+-1|0;o=a[d>>2]|0;a[o+(f<<2)>>2]=a[o+(u<<2)>>2]}while(u>>>0>h>>>0);u=a[e>>2]|0}a[(a[d>>2]|0)+(h<<2)>>2]=u+(l*40|0);h=h+1|0;if(h>>>0<=n>>>0){s=h;u=h;do{f=a[d>>2]|0;o=a[f+(s<<2)>>2]|0;if((o|0)!=((a[e>>2]|0)+(l*40|0)|0)){a[f+(u<<2)>>2]=o;u=u+1|0}s=s+1|0}while(s>>>0<=n>>>0)}s=a[r+(h*12|0)+4>>2]|0;if(s>>>0>=3){i=0;s=37;break}}if((s|0)==37){c=m;return i|0}return 0}function je(e,r,t,n,i,s,o,f){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;s=s|0;o=o|0;f=f|0;var u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0;D=c;C=a[t>>2]|0;L=a[e+8>>2]|0;if((C|0)!=(a[L>>2]|0)){S=1;c=D;return S|0}E=e+52|0;a[E>>2]=0;R=a[e+56>>2]|0;b=(R|0)==0;F=b&1;do{if(!r){a[L+20>>2]=0;a[L+12>>2]=n;a[L+8>>2]=n;a[L+16>>2]=i;a[L+24>>2]=F;if(b){t=e+44|0;a[t>>2]=(a[t>>2]|0)+1;t=0;w=0}else{t=R;w=0}}else{if(s){u=e+20|0;a[u>>2]=0;l=e+16|0;a[l>>2]=0;w=a[e>>2]|0;v=e+44|0;m=0;do{p=w+(m*40|0)+20|0;if((a[p>>2]|0)!=0?(a[p>>2]=0,(a[w+(m*40|0)+24>>2]|0)==0):0)a[v>>2]=(a[v>>2]|0)+-1;m=m+1|0}while((m|0)!=16);e:do{if(b){d=a[e+28>>2]|0;h=e+12|0;m=0;while(1){n=0;p=2147483647;b=0;do{if(a[w+(n*40|0)+24>>2]|0){T=a[w+(n*40|0)+16>>2]|0;S=(T|0)<(p|0);p=S?T:p;b=S?w+(n*40|0)|0:b}n=n+1|0}while(n>>>0<=d>>>0);if(!b){t=0;break e}S=a[h>>2]|0;a[S+(m<<4)>>2]=a[b>>2];a[S+(m<<4)+12>>2]=a[b+36>>2];a[S+(m<<4)+4>>2]=a[b+28>>2];a[S+(m<<4)+8>>2]=a[b+32>>2];m=m+1|0;a[l>>2]=m;a[b+24>>2]=0;if(a[b+20>>2]|0)continue;a[v>>2]=(a[v>>2]|0)+-1}}else t=R}while(0);p=e+40|0;a[p>>2]=0;b=e+36|0;a[b>>2]=65535;a[e+48>>2]=0;if(!(a[r>>2]|t))t=0;else{a[l>>2]=0;a[u>>2]=0}w=(a[r+4>>2]|0)==0;a[L+20>>2]=w?2:3;a[b>>2]=w?65535:0;a[L+12>>2]=0;a[L+8>>2]=0;a[L+16>>2]=0;a[L+24>>2]=F;a[v>>2]=1;a[p>>2]=1;w=0;break}if(!(a[r+8>>2]|0)){t=e+40|0;m=a[t>>2]|0;p=a[e+24>>2]|0;if(m>>>0>=p>>>0){if(m){l=a[e>>2]|0;h=0;b=-1;d=0;do{if(((a[l+(h*40|0)+20>>2]|0)+-1|0)>>>0<2){S=a[l+(h*40|0)+8>>2]|0;T=(S|0)<(d|0)|(b|0)==-1;b=T?h:b;d=T?S:d}h=h+1|0}while((h|0)!=(m|0));if((b|0)>-1){m=m+-1|0;a[l+(b*40|0)+20>>2]=0;a[t>>2]=m;if(!(a[l+(b*40|0)+24>>2]|0)){t=e+44|0;a[t>>2]=(a[t>>2]|0)+-1;t=R;d=0}else{t=R;d=0}}else{t=R;d=1}}else{m=0;t=R;d=1}}else{t=R;d=0}}else{U=e+24|0;T=e+40|0;y=e+44|0;_=e+36|0;x=e+48|0;g=e+28|0;A=e+16|0;M=e+12|0;w=R;t=R;k=0;B=0;e:while(1){switch(a[r+(k*20|0)+12>>2]|0){case 4:{l=a[r+(k*20|0)+28>>2]|0;a[_>>2]=l;h=a[U>>2]|0;if(!h)v=B;else{d=a[e>>2]|0;v=l;b=0;do{u=d+(b*40|0)+20|0;do{if((a[u>>2]|0)==3){if((a[d+(b*40|0)+8>>2]|0)>>>0<=l>>>0)if((v|0)==65535)v=65535;else break;a[u>>2]=0;a[T>>2]=(a[T>>2]|0)+-1;if(!(a[d+(b*40|0)+24>>2]|0))a[y>>2]=(a[y>>2]|0)+-1}}while(0);b=b+1|0}while((b|0)!=(h|0));v=B}break}case 1:{d=n-(a[r+(k*20|0)+16>>2]|0)|0;l=a[U>>2]|0;if(!l){d=1;break e}h=a[e>>2]|0;v=0;while(1){u=h+(v*40|0)+20|0;if(((a[u>>2]|0)+-1|0)>>>0<2?(a[h+(v*40|0)+8>>2]|0)==(d|0):0)break;v=v+1|0;if(v>>>0>=l>>>0){d=1;break e}}if((v|0)<0){d=1;break e}a[u>>2]=0;a[T>>2]=(a[T>>2]|0)+-1;if(!(a[h+(v*40|0)+24>>2]|0)){a[y>>2]=(a[y>>2]|0)+-1;v=B}else v=B;break}case 6:{h=a[r+(k*20|0)+24>>2]|0;v=a[_>>2]|0;if((v|0)==65535|v>>>0<h>>>0){d=1;P=101;break e}w=a[U>>2]|0;r:do{if(w){l=a[e>>2]|0;v=0;while(1){u=l+(v*40|0)+20|0;if((a[u>>2]|0)==3?(a[l+(v*40|0)+8>>2]|0)==(h|0):0)break;v=v+1|0;if(v>>>0>=w>>>0){P=88;break r}}a[u>>2]=0;u=(a[T>>2]|0)+-1|0;a[T>>2]=u;if(!(a[l+(v*40|0)+24>>2]|0)){a[y>>2]=(a[y>>2]|0)+-1;v=u}else v=u}else{w=0;P=88}}while(0);if((P|0)==88){P=0;v=a[T>>2]|0}if(v>>>0>=w>>>0){d=1;P=101;break e}a[L+12>>2]=n;a[L+8>>2]=h;a[L+16>>2]=i;a[L+20>>2]=3;a[L+24>>2]=F;a[T>>2]=v+1;a[y>>2]=(a[y>>2]|0)+1;w=R;t=R;v=1;break}case 2:{l=a[r+(k*20|0)+20>>2]|0;h=a[U>>2]|0;if(!h){d=1;break e}d=a[e>>2]|0;v=0;while(1){u=d+(v*40|0)+20|0;if((a[u>>2]|0)==3?(a[d+(v*40|0)+8>>2]|0)==(l|0):0)break;v=v+1|0;if(v>>>0>=h>>>0){d=1;break e}}if((v|0)<0){d=1;break e}a[u>>2]=0;a[T>>2]=(a[T>>2]|0)+-1;if(!(a[d+(v*40|0)+24>>2]|0)){a[y>>2]=(a[y>>2]|0)+-1;v=B}else v=B;break}case 3:{v=a[r+(k*20|0)+16>>2]|0;d=a[r+(k*20|0)+24>>2]|0;u=a[_>>2]|0;if((u|0)==65535|u>>>0<d>>>0){d=1;break e}b=a[U>>2]|0;if(!b){d=1;break e}p=a[e>>2]|0;u=0;while(1){h=p+(u*40|0)+20|0;if((a[h>>2]|0)==3?(a[p+(u*40|0)+8>>2]|0)==(d|0):0){P=47;break}l=u+1|0;if(l>>>0<b>>>0)u=l;else break}if((P|0)==47?(P=0,a[h>>2]=0,a[T>>2]=(a[T>>2]|0)+-1,(a[p+(u*40|0)+24>>2]|0)==0):0)a[y>>2]=(a[y>>2]|0)+-1;h=n-v|0;v=0;while(1){l=p+(v*40|0)+20|0;u=a[l>>2]|0;if((u+-1|0)>>>0<2?(S=p+(v*40|0)+8|0,(a[S>>2]|0)==(h|0)):0)break;v=v+1|0;if(v>>>0>=b>>>0){d=1;break e}}if(!((v|0)>-1&u>>>0>1)){d=1;break e}a[l>>2]=3;a[S>>2]=d;v=B;break}case 5:{d=a[e>>2]|0;m=0;do{p=d+(m*40|0)+20|0;if((a[p>>2]|0)!=0?(a[p>>2]=0,(a[d+(m*40|0)+24>>2]|0)==0):0)a[y>>2]=(a[y>>2]|0)+-1;m=m+1|0}while((m|0)!=16);r:do{if(!t){l=a[g>>2]|0;h=w;while(1){t=0;v=2147483647;u=0;do{if(a[d+(t*40|0)+24>>2]|0){b=a[d+(t*40|0)+16>>2]|0;n=(b|0)<(v|0);v=n?b:v;u=n?d+(t*40|0)|0:u}t=t+1|0}while(t>>>0<=l>>>0);if(!u){w=h;t=0;break r}v=a[A>>2]|0;t=a[M>>2]|0;a[t+(v<<4)>>2]=a[u>>2];a[t+(v<<4)+12>>2]=a[u+36>>2];a[t+(v<<4)+4>>2]=a[u+28>>2];a[t+(v<<4)+8>>2]=a[u+32>>2];a[A>>2]=v+1;a[u+24>>2]=0;if(!(a[u+20>>2]|0))a[y>>2]=(a[y>>2]|0)+-1;if(!h)h=0;else{w=h;t=h;break}}}}while(0);a[T>>2]=0;a[_>>2]=65535;a[x>>2]=0;a[E>>2]=1;n=0;v=B;break}case 0:{d=0;P=101;break e}default:{d=1;break e}}k=k+1|0;B=v}if(B){w=d;break}m=a[T>>2]|0;p=a[U>>2]|0}if(m>>>0<p>>>0){a[L+12>>2]=n;a[L+8>>2]=n;a[L+16>>2]=i;a[L+20>>2]=2;a[L+24>>2]=F;w=e+44|0;a[w>>2]=(a[w>>2]|0)+1;a[e+40>>2]=m+1;w=d}else w=1}}while(0);a[L+36>>2]=s;a[L+28>>2]=o;a[L+32>>2]=f;if(!t){b=e+44|0;t=a[b>>2]|0;u=a[e+28>>2]|0;if(t>>>0>u>>>0){p=e+16|0;m=e+12|0;do{d=a[e>>2]|0;n=0;l=2147483647;h=0;do{if(a[d+(n*40|0)+24>>2]|0){T=a[d+(n*40|0)+16>>2]|0;S=(T|0)<(l|0);l=S?T:l;h=S?d+(n*40|0)|0:h}n=n+1|0}while(n>>>0<=u>>>0);if((h|0)!=0?(S=a[p>>2]|0,T=a[m>>2]|0,a[T+(S<<4)>>2]=a[h>>2],a[T+(S<<4)+12>>2]=a[h+36>>2],a[T+(S<<4)+4>>2]=a[h+28>>2],a[T+(S<<4)+8>>2]=a[h+32>>2],a[p>>2]=S+1,a[h+24>>2]=0,(a[h+20>>2]|0)==0):0){t=t+-1|0;a[b>>2]=t}}while(t>>>0>u>>>0)}}else{u=e+16|0;S=a[u>>2]|0;T=a[e+12>>2]|0;a[T+(S<<4)>>2]=C;a[T+(S<<4)+12>>2]=s;a[T+(S<<4)+4>>2]=o;a[T+(S<<4)+8>>2]=f;a[u>>2]=S+1;u=a[e+28>>2]|0}Qe(a[e>>2]|0,u+1|0);S=w;c=D;return S|0}function Ye(e,r){e=e|0;r=r|0;var t=0,n=0;n=c;if((r>>>0<=16?(t=a[(a[e+4>>2]|0)+(r<<2)>>2]|0,(t|0)!=0):0)?(a[t+20>>2]|0)>>>0>1:0)t=a[t>>2]|0;else t=0;c=n;return t|0}function He(e){e=e|0;var r=0;r=(a[e>>2]|0)+((a[e+28>>2]|0)*40|0)|0;a[e+8>>2]=r;return a[r>>2]|0}function We(e,r,t,n,i,s){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;s=s|0;var o=0,f=0;f=c;a[e+36>>2]=65535;n=n>>>0>1?n:1;a[e+24>>2]=n;o=e+28|0;a[o>>2]=(s|0)==0?t:n;a[e+32>>2]=i;a[e+56>>2]=s;a[e+44>>2]=0;a[e+40>>2]=0;a[e+48>>2]=0;s=fn(680)|0;a[e>>2]=s;if(!s){s=65535;c=f;return s|0}cn(s,0,680);e:do{if((a[o>>2]|0)!=-1){i=r*384|47;n=0;while(1){t=fn(i)|0;s=a[e>>2]|0;a[s+(n*40|0)+4>>2]=t;if(!t){s=65535;break}a[s+(n*40|0)>>2]=t+(0-t&15);n=n+1|0;if(n>>>0>=((a[o>>2]|0)+1|0)>>>0)break e}c=f;return s|0}}while(0);s=e+4|0;a[s>>2]=fn(68)|0;i=fn((a[o>>2]<<4)+16|0)|0;a[e+12>>2]=i;s=a[s>>2]|0;if((s|0)==0|(i|0)==0){s=65535;c=f;return s|0}cn(s,0,68);a[e+20>>2]=0;a[e+16>>2]=0;s=0;c=f;return s|0}function Ge(e,r,t,n,i,s){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;s=s|0;var o=0,f=0,u=0,l=0;l=c;o=a[e>>2]|0;if(o){u=e+28|0;if((a[u>>2]|0)!=-1){f=0;do{un(a[o+(f*40|0)+4>>2]|0);o=a[e>>2]|0;a[o+(f*40|0)+4>>2]=0;f=f+1|0}while(f>>>0<((a[u>>2]|0)+1|0)>>>0)}}else o=0;un(o);a[e>>2]=0;o=e+4|0;un(a[o>>2]|0);a[o>>2]=0;o=e+12|0;un(a[o>>2]|0);a[o>>2]=0;o=We(e,r,t,n,i,s)|0;c=l;return o|0}function Ze(e){e=e|0;var r=0,t=0,n=0,i=0;i=c;r=a[e>>2]|0;if(r){n=e+28|0;if((a[n>>2]|0)!=-1){t=0;do{un(a[r+(t*40|0)+4>>2]|0);r=a[e>>2]|0;a[r+(t*40|0)+4>>2]=0;t=t+1|0}while(t>>>0<((a[n>>2]|0)+1|0)>>>0)}}else r=0;un(r);a[e>>2]=0;r=e+4|0;un(a[r>>2]|0);a[r>>2]=0;r=e+12|0;un(a[r>>2]|0);a[r>>2]=0;c=i;return}function Ke(e){e=e|0;var r=0,t=0,n=0,i=0;i=c;r=a[e+40>>2]|0;if(!r){c=i;return}n=e+4|0;t=0;do{a[(a[n>>2]|0)+(t<<2)>>2]=(a[e>>2]|0)+(t*40|0);t=t+1|0}while(t>>>0<r>>>0);c=i;return}function Xe(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0;A=c;y=e+16|0;a[y>>2]=0;a[e+20>>2]=0;if(!n){r=0;c=A;return r|0}_=e+48|0;n=a[_>>2]|0;i=(n|0)==(r|0);e:do{if(!i?(g=e+32|0,l=a[g>>2]|0,u=((n+1|0)>>>0)%(l>>>0)|0,(u|0)!=(r|0)):0){k=e+28|0;B=a[(a[e>>2]|0)+((a[k>>2]|0)*40|0)>>2]|0;w=e+40|0;p=e+24|0;b=e+44|0;m=e+56|0;v=e+12|0;d=u;while(1){u=a[w>>2]|0;if(!u)u=0;else{f=a[e>>2]|0;o=0;do{if(((a[f+(o*40|0)+20>>2]|0)+-1|0)>>>0<2){n=a[f+(o*40|0)+12>>2]|0;a[f+(o*40|0)+8>>2]=n-(n>>>0>d>>>0?l:0)}o=o+1|0}while((o|0)!=(u|0))}if(u>>>0>=(a[p>>2]|0)>>>0){if(!u){n=1;s=46;break}i=a[e>>2]|0;o=0;l=-1;f=0;while(1){if(((a[i+(o*40|0)+20>>2]|0)+-1|0)>>>0<2){n=a[i+(o*40|0)+8>>2]|0;h=(n|0)<(f|0)|(l|0)==-1;s=h?o:l;f=h?n:f}else s=l;o=o+1|0;if((o|0)==(u|0))break;else l=s}if((s|0)<=-1){n=1;s=46;break}l=u+-1|0;a[i+(s*40|0)+20>>2]=0;a[w>>2]=l;if(!(a[i+(s*40|0)+24>>2]|0)){a[b>>2]=(a[b>>2]|0)+-1;u=l}else u=l}l=a[b>>2]|0;h=a[k>>2]|0;if(l>>>0>=h>>>0){n=(a[m>>2]|0)==0;do{if(n){s=a[e>>2]|0;i=0;f=2147483647;o=0;do{if(a[s+(i*40|0)+24>>2]|0){x=a[s+(i*40|0)+16>>2]|0;M=(x|0)<(f|0);f=M?x:f;o=M?s+(i*40|0)|0:o}i=i+1|0}while(i>>>0<=h>>>0);if((o|0)!=0?(i=a[y>>2]|0,s=a[v>>2]|0,a[s+(i<<4)>>2]=a[o>>2],a[s+(i<<4)+12>>2]=a[o+36>>2],a[s+(i<<4)+4>>2]=a[o+28>>2],a[s+(i<<4)+8>>2]=a[o+32>>2],a[y>>2]=i+1,a[o+24>>2]=0,(a[o+20>>2]|0)==0):0){l=l+-1|0;a[b>>2]=l}}}while(l>>>0>=h>>>0)}n=a[e>>2]|0;a[n+(h*40|0)+20>>2]=1;a[n+(h*40|0)+12>>2]=d;a[n+(h*40|0)+8>>2]=d;a[n+(h*40|0)+16>>2]=0;a[n+(h*40|0)+24>>2]=0;a[b>>2]=l+1;a[w>>2]=u+1;Qe(n,h+1|0);l=a[g>>2]|0;d=((d+1|0)>>>0)%(l>>>0)|0;if((d|0)==(r|0)){s=31;break}}if((s|0)==31){s=a[y>>2]|0;if(!s){s=41;break}n=a[v>>2]|0;o=a[k>>2]|0;f=a[e>>2]|0;l=f+(o*40|0)|0;u=a[l>>2]|0;i=0;while(1){if((a[n+(i<<4)>>2]|0)==(u|0))break;i=i+1|0;if(i>>>0>=s>>>0){s=41;break e}}if(!o){s=41;break}else n=0;while(1){i=f+(n*40|0)|0;n=n+1|0;if((a[i>>2]|0)==(B|0))break;if(n>>>0>=o>>>0){s=41;break e}}a[i>>2]=u;a[l>>2]=B;s=41;break}else if((s|0)==46){c=A;return n|0}}else s=39}while(0);if((s|0)==39)if(t)if(i){r=1;c=A;return r|0}else s=41;do{if((s|0)==41){if(!t){n=a[_>>2]|0;break}a[_>>2]=r;r=0;c=A;return r|0}}while(0);if((n|0)==(r|0)){r=0;c=A;return r|0}e=a[e+32>>2]|0;a[_>>2]=((r+-1+e|0)>>>0)%(e>>>0)|0;r=0;c=A;return r|0}function $e(e){e=e|0;var r=0,t=0,n=0;n=c;t=e+20|0;r=a[t>>2]|0;if(r>>>0>=(a[e+16>>2]|0)>>>0){r=0;c=n;return r|0}e=a[e+12>>2]|0;a[t>>2]=r+1;r=e+(r<<4)|0;c=n;return r|0}function Je(e){e=e|0;var r=0,t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0;u=c;i=a[e>>2]|0;if(!i){c=u;return}a[e+60>>2]=1;if(a[e+56>>2]|0){c=u;return}s=a[e+28>>2]|0;o=e+16|0;f=e+12|0;n=e+44|0;e=0;r=2147483647;t=0;while(1){if(a[i+(e*40|0)+24>>2]|0){h=a[i+(e*40|0)+16>>2]|0;l=(h|0)<(r|0);r=l?h:r;t=l?i+(e*40|0)|0:t}e=e+1|0;if(e>>>0<=s>>>0)continue;if(!t)break;l=a[o>>2]|0;r=a[f>>2]|0;a[r+(l<<4)>>2]=a[t>>2];a[r+(l<<4)+12>>2]=a[t+36>>2];a[r+(l<<4)+4>>2]=a[t+28>>2];a[r+(l<<4)+8>>2]=a[t+32>>2];a[o>>2]=l+1;a[t+24>>2]=0;if(a[t+20>>2]|0){e=0;r=2147483647;t=0;continue}a[n>>2]=(a[n>>2]|0)+-1;e=0;r=2147483647;t=0}c=u;return}function Qe(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0;y=c;c=c+32|0;m=y+16|0;k=y;b=7;do{if(b>>>0<r>>>0){d=b;do{h=e+(d*40|0)|0;l=a[h>>2]|0;h=a[h+4>>2]|0;p=a[e+(d*40|0)+8>>2]|0;u=e+(d*40|0)+12|0;v=a[u+4>>2]|0;w=m;a[w>>2]=a[u>>2];a[w+4>>2]=v;w=a[e+(d*40|0)+20>>2]|0;v=a[e+(d*40|0)+24>>2]|0;u=e+(d*40|0)+28|0;a[k+0>>2]=a[u+0>>2];a[k+4>>2]=a[u+4>>2];a[k+8>>2]=a[u+8>>2];e:do{if(d>>>0<b>>>0){t=d;g=8}else{i=(v|0)==0;f=w+-1|0;u=f>>>0<2;r:do{if(!w){n=d;while(1){t=n-b|0;if(a[e+(t*40|0)+20>>2]|0){t=n;break r}if((a[e+(t*40|0)+24>>2]|0)!=0|i){t=n;break r}n=e+(n*40|0)+0|0;s=e+(t*40|0)+0|0;o=n+40|0;do{a[n>>2]=a[s>>2];n=n+4|0;s=s+4|0}while((n|0)<(o|0));if(t>>>0<b>>>0){g=8;break e}else n=t}}else{t=d;while(1){i=t-b|0;n=a[e+(i*40|0)+20>>2]|0;do{if(n){n=n+-1|0;if((n|f)>>>0<2){n=a[e+(i*40|0)+8>>2]|0;if((n|0)>(p|0))break r;t=e+(t*40|0)|0;if((n|0)<(p|0))break;else break e}if(n>>>0<2)break r;if(!u?(a[e+(i*40|0)+8>>2]|0)<=(p|0):0)break r;else g=16}else g=16}while(0);if((g|0)==16){g=0;t=e+(t*40|0)|0}n=t+0|0;s=e+(i*40|0)+0|0;o=n+40|0;do{a[n>>2]=a[s>>2];n=n+4|0;s=s+4|0}while((n|0)<(o|0));if(i>>>0<b>>>0){t=i;g=8;break e}else t=i}}}while(0);t=e+(t*40|0)|0}}while(0);if((g|0)==8){g=0;t=e+(t*40|0)|0}u=t;a[u>>2]=l;a[u+4>>2]=h;a[t+8>>2]=p;u=m;l=a[u+4>>2]|0;h=t+12|0;a[h>>2]=a[u>>2];a[h+4>>2]=l;a[t+20>>2]=w;a[t+24>>2]=v;h=t+28|0;a[h+0>>2]=a[k+0>>2];a[h+4>>2]=a[k+4>>2];a[h+8>>2]=a[k+8>>2];d=d+1|0}while((d|0)!=(r|0))}b=b>>>1}while((b|0)!=0);c=y;return}function er(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0;l=c;n=a[e+4>>2]|0;i=a[e+16>>2]|0;s=a[e+20>>2]|0;f=n<<2;u=r+256|0;o=16;e=a[e+12>>2]|0;t=r;while(1){h=a[t+4>>2]|0;a[e>>2]=a[t>>2];a[e+4>>2]=h;h=a[t+12>>2]|0;a[e+8>>2]=a[t+8>>2];a[e+12>>2]=h;o=o+-1|0;if(!o)break;else{e=e+(f<<2)|0;t=t+16|0}}f=n<<1&2147483646;o=a[r+260>>2]|0;a[i>>2]=a[u>>2];a[i+4>>2]=o;u=a[r+268>>2]|0;a[i+(f<<2)>>2]=a[r+264>>2];a[i+((f|1)<<2)>>2]=u;u=n<<2;o=a[r+276>>2]|0;a[i+(u<<2)>>2]=a[r+272>>2];a[i+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+284>>2]|0;a[i+(u<<2)>>2]=a[r+280>>2];a[i+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+292>>2]|0;a[i+(u<<2)>>2]=a[r+288>>2];a[i+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+300>>2]|0;a[i+(u<<2)>>2]=a[r+296>>2];a[i+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+308>>2]|0;a[i+(u<<2)>>2]=a[r+304>>2];a[i+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+316>>2]|0;a[i+(u<<2)>>2]=a[r+312>>2];a[i+((u|1)<<2)>>2]=o;u=a[r+324>>2]|0;a[s>>2]=a[r+320>>2];a[s+4>>2]=u;u=a[r+332>>2]|0;a[s+(f<<2)>>2]=a[r+328>>2];a[s+((f|1)<<2)>>2]=u;u=n<<2;o=a[r+340>>2]|0;a[s+(u<<2)>>2]=a[r+336>>2];a[s+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+348>>2]|0;a[s+(u<<2)>>2]=a[r+344>>2];a[s+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+356>>2]|0;a[s+(u<<2)>>2]=a[r+352>>2];a[s+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+364>>2]|0;a[s+(u<<2)>>2]=a[r+360>>2];a[s+((u|1)<<2)>>2]=o;u=u+f|0;o=a[r+372>>2]|0;a[s+(u<<2)>>2]=a[r+368>>2];a[s+((u|1)<<2)>>2]=o;u=u+f|0;f=a[r+380>>2]|0;a[s+(u<<2)>>2]=a[r+376>>2];a[s+((u|1)<<2)>>2]=f;c=l;return}function rr(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0;A=c;w=a[e+4>>2]|0;v=ee(a[e+8>>2]|0,w)|0;B=(r>>>0)%(w>>>0)|0;_=a[e>>2]|0;g=r-B|0;e=(g<<8)+(B<<4)|0;y=v<<8;B=B<<3;p=w<<4;b=w<<2&1073741820;l=b<<1;h=l+b|0;d=0;do{u=a[3344+(d<<2)>>2]|0;f=a[3408+(d<<2)>>2]|0;r=(f<<4)+u|0;o=t+r|0;f=e+u+(ee(f,p)|0)|0;u=_+f|0;m=a[i+(d<<6)>>2]|0;if((m|0)==16777215){f=a[t+(r+16)>>2]|0;a[u>>2]=a[o>>2];a[u+(b<<2)>>2]=f;f=a[t+(r+48)>>2]|0;a[u+(l<<2)>>2]=a[t+(r+32)>>2];a[u+(h<<2)>>2]=f}else{x=s[t+(r+1)>>0]|0;M=a[i+(d<<6)+4>>2]|0;n[u>>0]=n[3472+(m+512+(s[o>>0]|0))>>0]|0;m=s[t+(r+2)>>0]|0;k=a[i+(d<<6)+8>>2]|0;n[_+(f+1)>>0]=n[3472+((x|512)+M)>>0]|0;u=s[t+(r+3)>>0]|0;o=a[i+(d<<6)+12>>2]|0;n[_+(f+2)>>0]=n[3472+(k+512+m)>>0]|0;n[_+(f+3)>>0]=n[3472+(o+512+u)>>0]|0;u=f+p|0;o=s[t+(r+17)>>0]|0;f=a[i+(d<<6)+20>>2]|0;n[_+u>>0]=n[3472+((a[i+(d<<6)+16>>2]|0)+512+(s[t+(r+16)>>0]|0))>>0]|0;m=s[t+(r+18)>>0]|0;k=a[i+(d<<6)+24>>2]|0;n[_+(u+1)>>0]=n[3472+((o|512)+f)>>0]|0;f=s[t+(r+19)>>0]|0;o=a[i+(d<<6)+28>>2]|0;n[_+(u+2)>>0]=n[3472+(k+512+m)>>0]|0;n[_+(u+3)>>0]=n[3472+(o+512+f)>>0]|0;u=u+p|0;f=s[t+(r+33)>>0]|0;o=a[i+(d<<6)+36>>2]|0;n[_+u>>0]=n[3472+((a[i+(d<<6)+32>>2]|0)+512+(s[t+(r+32)>>0]|0))>>0]|0;m=s[t+(r+34)>>0]|0;k=a[i+(d<<6)+40>>2]|0;n[_+(u+1)>>0]=n[3472+((f|512)+o)>>0]|0;o=s[t+(r+35)>>0]|0;f=a[i+(d<<6)+44>>2]|0;n[_+(u+2)>>0]=n[3472+(k+512+m)>>0]|0;n[_+(u+3)>>0]=n[3472+(f+512+o)>>0]|0;u=u+p|0;o=s[t+(r+49)>>0]|0;f=a[i+(d<<6)+52>>2]|0;n[_+u>>0]=n[3472+((a[i+(d<<6)+48>>2]|0)+512+(s[t+(r+48)>>0]|0))>>0]|0;m=s[t+(r+50)>>0]|0;k=a[i+(d<<6)+56>>2]|0;n[_+(u+1)>>0]=n[3472+((o|512)+f)>>0]|0;f=s[t+(r+51)>>0]|0;o=a[i+(d<<6)+60>>2]|0;n[_+(u+2)>>0]=n[3472+(k+512+m)>>0]|0;n[_+(u+3)>>0]=n[3472+(o+512+f)>>0]|0}d=d+1|0}while((d|0)!=16);k=v<<6;v=w<<3&2147483640;w=t+256|0;t=t+320|0;l=B+y+(g<<6)|0;p=v>>>2;f=v>>>1;u=f+p|0;b=16;do{m=b&3;o=a[3344+(m<<2)>>2]|0;m=a[3408+(m<<2)>>2]|0;r=b>>>0>19;h=r?t:w;d=(m<<3)+o|0;e=h+d|0;m=l+(r?k:0)+o+(ee(m,v)|0)|0;o=_+m|0;r=a[i+(b<<6)>>2]|0;if((r|0)==16777215){M=a[h+(d+8)>>2]|0;a[o>>2]=a[e>>2];a[o+(p<<2)>>2]=M;M=a[h+(d+24)>>2]|0;a[o+(f<<2)>>2]=a[h+(d+16)>>2];a[o+(u<<2)>>2]=M}else{y=s[h+(d+1)>>0]|0;M=a[i+(b<<6)+4>>2]|0;n[o>>0]=n[3472+(r+512+(s[e>>0]|0))>>0]|0;B=s[h+(d+2)>>0]|0;g=a[i+(b<<6)+8>>2]|0;n[_+(m+1)>>0]=n[3472+((y|512)+M)>>0]|0;M=s[h+(d+3)>>0]|0;y=a[i+(b<<6)+12>>2]|0;n[_+(m+2)>>0]=n[3472+(g+512+B)>>0]|0;n[_+(m+3)>>0]=n[3472+(y+512+M)>>0]|0;M=m+v|0;y=s[h+(d+9)>>0]|0;B=a[i+(b<<6)+20>>2]|0;n[_+M>>0]=n[3472+((a[i+(b<<6)+16>>2]|0)+512+(s[h+(d+8)>>0]|0))>>0]|0;g=s[h+(d+10)>>0]|0;m=a[i+(b<<6)+24>>2]|0;n[_+(M+1)>>0]=n[3472+((y|512)+B)>>0]|0;B=s[h+(d+11)>>0]|0;y=a[i+(b<<6)+28>>2]|0;n[_+(M+2)>>0]=n[3472+(m+512+g)>>0]|0;n[_+(M+3)>>0]=n[3472+(y+512+B)>>0]|0;M=M+v|0;B=s[h+(d+17)>>0]|0;y=a[i+(b<<6)+36>>2]|0;n[_+M>>0]=n[3472+((a[i+(b<<6)+32>>2]|0)+512+(s[h+(d+16)>>0]|0))>>0]|0;g=s[h+(d+18)>>0]|0;m=a[i+(b<<6)+40>>2]|0;n[_+(M+1)>>0]=n[3472+((B|512)+y)>>0]|0;y=s[h+(d+19)>>0]|0;B=a[i+(b<<6)+44>>2]|0;n[_+(M+2)>>0]=n[3472+(m+512+g)>>0]|0;n[_+(M+3)>>0]=n[3472+(B+512+y)>>0]|0;M=M+v|0;y=s[h+(d+25)>>0]|0;B=a[i+(b<<6)+52>>2]|0;n[_+M>>0]=n[3472+((a[i+(b<<6)+48>>2]|0)+512+(s[h+(d+24)>>0]|0))>>0]|0;g=s[h+(d+26)>>0]|0;m=a[i+(b<<6)+56>>2]|0;n[_+(M+1)>>0]=n[3472+((y|512)+B)>>0]|0;B=s[h+(d+27)>>0]|0;y=a[i+(b<<6)+60>>2]|0;n[_+(M+2)>>0]=n[3472+(m+512+g)>>0]|0;n[_+(M+3)>>0]=n[3472+(y+512+B)>>0]|0}b=b+1|0}while((b|0)!=24);c=A;return}function tr(e,r){e=e|0;r=r|0;var t=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0,N=0,z=0,V=0,q=0,j=0,Y=0,H=0,W=0,G=0,Z=0,K=0,X=0,$=0,J=0,Q=0,re=0,te=0,ne=0,ie=0,ae=0,se=0,oe=0,fe=0,ue=0,le=0,ce=0,he=0,de=0,be=0,pe=0,me=0,we=0,ve=0,ke=0,ge=0,ye=0,Be=0,_e=0,Ae=0,Me=0,xe=0,Ee=0,Te=0,Se=0,Ue=0,Pe=0,Re=0,Fe=0,Ce=0,Le=0,De=0,Ie=0,Oe=0,Ne=0,ze=0,Ve=0,qe=0,je=0,Ye=0,He=0,We=0,Ge=0,Ze=0,Ke=0,Xe=0,$e=0,Je=0,Qe=0,er=0;er=c;c=c+176|0;fe=er+40|0;Ie=er;ie=a[e+4>>2]|0;le=e+8|0;Ge=a[le>>2]|0;t=ee(Ge,ie)|0;if(!Ge){c=er;return}Xe=fe+24|0;$e=fe+16|0;Je=fe+8|0;ce=fe+100|0;he=fe+68|0;de=fe+36|0;be=fe+4|0;Oe=fe+120|0;Ne=fe+112|0;ze=fe+104|0;Ve=fe+96|0;qe=fe+88|0;je=fe+80|0;Ye=fe+72|0;He=fe+64|0;We=fe+56|0;Ge=fe+48|0;Ze=fe+40|0;Ke=fe+32|0;pe=fe+124|0;me=fe+116|0;we=fe+108|0;ve=fe+92|0;ke=fe+84|0;ge=fe+76|0;ye=fe+60|0;Be=fe+52|0;_e=fe+44|0;Ae=fe+28|0;Me=fe+20|0;xe=fe+12|0;oe=Ie+28|0;ue=Ie+32|0;De=Ie+24|0;Se=ie<<4;Le=0-Se|0;Te=Le<<1;Fe=ee(ie,-48)|0;Ce=ie<<5;Ue=Le<<2;Re=ie*48|0;Ee=ie<<6;se=Ie+24|0;ae=Ie+12|0;Pe=t<<8;re=t<<6;te=ie<<3;Z=Se|4;X=Ie+16|0;J=Ie+20|0;ne=Ie+12|0;K=Ie+4|0;$=Ie+8|0;W=0;Q=0;G=r;while(1){r=a[G+8>>2]|0;do{if((r|0)!=1){H=G+200|0;d=a[H>>2]|0;do{if(!d)t=1;else{if((r|0)==2?(a[G+4>>2]|0)!=(a[d+4>>2]|0):0){t=1;break}t=5}}while(0);Y=G+204|0;l=a[Y>>2]|0;do{if(l){if((r|0)==2?(a[G+4>>2]|0)!=(a[l+4>>2]|0):0)break;t=t|2}}while(0);j=(t&2|0)==0;do{if(j){a[Xe>>2]=0;a[$e>>2]=0;a[Je>>2]=0;a[fe>>2]=0;h=0}else{if((a[G>>2]|0)>>>0<=5?(a[l>>2]|0)>>>0<=5:0){if((i[G+28>>1]|0)==0?(i[l+48>>1]|0)==0:0){if((a[G+116>>2]|0)==(a[l+124>>2]|0)?(E=(i[G+132>>1]|0)-(i[l+172>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){o=(i[G+134>>1]|0)-(i[l+174>>1]|0)|0;o=(((o|0)>-1?o:0-o|0)|0)>3&1}else o=1}else o=2;a[fe>>2]=o;if((i[G+30>>1]|0)==0?(i[l+50>>1]|0)==0:0){if((a[G+116>>2]|0)==(a[l+124>>2]|0)?(E=(i[G+136>>1]|0)-(i[l+176>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){f=(i[G+138>>1]|0)-(i[l+178>>1]|0)|0;f=(((f|0)>-1?f:0-f|0)|0)>3&1}else f=1}else f=2;a[Je>>2]=f;if((i[G+36>>1]|0)==0?(i[l+56>>1]|0)==0:0){if((a[G+120>>2]|0)==(a[l+128>>2]|0)?(E=(i[G+148>>1]|0)-(i[l+188>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){u=(i[G+150>>1]|0)-(i[l+190>>1]|0)|0;u=(((u|0)>-1?u:0-u|0)|0)>3&1}else u=1}else u=2;a[$e>>2]=u;if((i[G+38>>1]|0)==0?(i[l+58>>1]|0)==0:0){if((a[G+120>>2]|0)==(a[l+128>>2]|0)?(E=(i[G+152>>1]|0)-(i[l+192>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){r=(i[G+154>>1]|0)-(i[l+194>>1]|0)|0;r=(((r|0)>-1?r:0-r|0)|0)>3&1}else r=1}else r=2;a[Xe>>2]=r;h=(f|o|u|r|0)!=0&1;break}a[Xe>>2]=4;a[$e>>2]=4;a[Je>>2]=4;a[fe>>2]=4;h=1}}while(0);q=(t&4|0)==0;do{if(q){a[ce>>2]=0;a[he>>2]=0;a[de>>2]=0;a[be>>2]=0;u=a[G>>2]|0}else{u=a[G>>2]|0;if(u>>>0<=5?(a[d>>2]|0)>>>0<=5:0){if((i[G+28>>1]|0)==0?(i[d+38>>1]|0)==0:0){if((a[G+116>>2]|0)==(a[d+120>>2]|0)?(E=(i[G+132>>1]|0)-(i[d+152>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){f=(i[G+134>>1]|0)-(i[d+154>>1]|0)|0;f=(((f|0)>-1?f:0-f|0)|0)>3&1}else f=1}else f=2;a[be>>2]=f;if((i[G+32>>1]|0)==0?(i[d+42>>1]|0)==0:0){if((a[G+116>>2]|0)==(a[d+120>>2]|0)?(E=(i[G+140>>1]|0)-(i[d+160>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){o=(i[G+142>>1]|0)-(i[d+162>>1]|0)|0;o=(((o|0)>-1?o:0-o|0)|0)>3&1}else o=1}else o=2;a[de>>2]=o;if((i[G+44>>1]|0)==0?(i[d+54>>1]|0)==0:0){if((a[G+124>>2]|0)==(a[d+128>>2]|0)?(E=(i[G+164>>1]|0)-(i[d+184>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){r=(i[G+166>>1]|0)-(i[d+186>>1]|0)|0;r=(((r|0)>-1?r:0-r|0)|0)>3&1}else r=1}else r=2;a[he>>2]=r;if((i[G+48>>1]|0)==0?(i[d+58>>1]|0)==0:0){if((a[G+124>>2]|0)==(a[d+128>>2]|0)?(E=(i[G+172>>1]|0)-(i[d+192>>1]|0)|0,(((E|0)>-1?E:0-E|0)|0)<=3):0){l=(i[G+174>>1]|0)-(i[d+194>>1]|0)|0;l=(((l|0)>-1?l:0-l|0)|0)>3&1}else l=1}else l=2;a[ce>>2]=l;if(h)break;h=(o|f|r|l|0)!=0&1;break}a[ce>>2]=4;a[he>>2]=4;a[de>>2]=4;a[be>>2]=4;h=1}}while(0);if(u>>>0<=5){do{if((Nr(u)|0)!=1){r=a[G>>2]|0;if((r|0)==2){B=G+28|0;_=i[G+32>>1]|0;if(!(_<<16>>16))r=(i[B>>1]|0)!=0?2:0;else r=2;a[Ke>>2]=r;m=i[G+34>>1]|0;x=m<<16>>16==0;if(x)r=(i[G+30>>1]|0)!=0?2:0;else r=2;a[Ze>>2]=r;t=i[G+40>>1]|0;M=t<<16>>16==0;if(M)l=(i[G+36>>1]|0)!=0?2:0;else l=2;a[Ge>>2]=l;y=i[G+42>>1]|0;A=y<<16>>16==0;if(A)l=(i[G+38>>1]|0)!=0?2:0;else l=2;a[We>>2]=l;o=i[G+48>>1]|0;if(!(o<<16>>16))l=(i[G+44>>1]|0)!=0?2:0;else l=2;a[Ve>>2]=l;u=i[G+50>>1]|0;E=u<<16>>16==0;if(E)l=(i[G+46>>1]|0)!=0?2:0;else l=2;a[ze>>2]=l;p=i[G+56>>1]|0;r=p<<16>>16==0;if(r)d=(i[G+52>>1]|0)!=0?2:0;else d=2;a[Ne>>2]=d;f=(i[G+58>>1]|0)==0;if(f)d=(i[G+54>>1]|0)!=0?2:0;else d=2;a[Oe>>2]=d;w=i[G+44>>1]|0;b=i[G+166>>1]|0;d=i[G+142>>1]|0;do{if(!((w|_)<<16>>16)){g=(i[G+164>>1]|0)-(i[G+140>>1]|0)|0;if((((g|0)>-1?g:0-g|0)|0)>3){d=1;break}g=b-d|0;if((((g|0)>-1?g:0-g|0)|0)>3){d=1;break}d=(a[G+124>>2]|0)!=(a[G+116>>2]|0)&1}else d=2}while(0);a[He>>2]=d;v=i[G+46>>1]|0;b=i[G+170>>1]|0;d=i[G+146>>1]|0;do{if(!((v|m)<<16>>16)){g=(i[G+168>>1]|0)-(i[G+144>>1]|0)|0;if((((g|0)>-1?g:0-g|0)|0)>3){b=1;break}g=b-d|0;if((((g|0)>-1?g:0-g|0)|0)>3){b=1;break}b=(a[G+124>>2]|0)!=(a[G+116>>2]|0)&1}else b=2}while(0);a[Ye>>2]=b;k=i[G+52>>1]|0;b=i[G+182>>1]|0;d=i[G+158>>1]|0;do{if(!((k|t)<<16>>16)){g=(i[G+180>>1]|0)-(i[G+156>>1]|0)|0;if((((g|0)>-1?g:0-g|0)|0)>3){b=1;break}g=b-d|0;if((((g|0)>-1?g:0-g|0)|0)>3){b=1;break}b=(a[G+128>>2]|0)!=(a[G+120>>2]|0)&1}else b=2}while(0);a[je>>2]=b;g=i[G+54>>1]|0;b=i[G+186>>1]|0;d=i[G+162>>1]|0;do{if(!((g|y)<<16>>16)){y=(i[G+184>>1]|0)-(i[G+160>>1]|0)|0;if((((y|0)>-1?y:0-y|0)|0)>3){b=1;break}y=b-d|0;if((((y|0)>-1?y:0-y|0)|0)>3){b=1;break}b=(a[G+128>>2]|0)!=(a[G+120>>2]|0)&1}else b=2}while(0);a[qe>>2]=b;l=i[G+30>>1]|0;if(!(l<<16>>16))b=(i[B>>1]|0)!=0?2:0;else b=2;a[xe>>2]=b;d=i[G+36>>1]|0;if(!(d<<16>>16))b=l<<16>>16!=0?2:0;else b=2;a[Me>>2]=b;if(!(i[G+38>>1]|0))b=d<<16>>16!=0?2:0;else b=2;a[Ae>>2]=b;if(x)d=_<<16>>16!=0?2:0;else d=2;a[_e>>2]=d;if(M)l=m<<16>>16!=0?2:0;else l=2;a[Be>>2]=l;if(A)l=t<<16>>16!=0?2:0;else l=2;a[ye>>2]=l;if(!(v<<16>>16))l=w<<16>>16!=0?2:0;else l=2;a[ge>>2]=l;if(!(k<<16>>16))l=v<<16>>16!=0?2:0;else l=2;a[ke>>2]=l;if(!(g<<16>>16))l=k<<16>>16!=0?2:0;else l=2;a[ve>>2]=l;if(E)o=o<<16>>16!=0?2:0;else o=2;a[we>>2]=o;if(r)r=u<<16>>16!=0?2:0;else r=2;a[me>>2]=r;if(f)r=p<<16>>16!=0?2:0;else r=2;a[pe>>2]=r;break}else if((r|0)==3){f=G+28|0;y=i[G+32>>1]|0;if(!(y<<16>>16))r=(i[f>>1]|0)!=0?2:0;else r=2;a[Ke>>2]=r;E=i[G+34>>1]|0;p=E<<16>>16==0;if(p)o=(i[G+30>>1]|0)!=0?2:0;else o=2;a[Ze>>2]=o;M=i[G+40>>1]|0;if(!(M<<16>>16))u=(i[G+36>>1]|0)!=0?2:0;else u=2;a[Ge>>2]=u;b=i[G+42>>1]|0;o=b<<16>>16==0;if(o)l=(i[G+38>>1]|0)!=0?2:0;else l=2;a[We>>2]=l;r=i[G+44>>1]|0;if(!(r<<16>>16))l=y<<16>>16!=0?2:0;else l=2;a[He>>2]=l;x=i[G+46>>1]|0;t=x<<16>>16==0;if(t)l=E<<16>>16!=0?2:0;else l=2;a[Ye>>2]=l;A=i[G+52>>1]|0;if(!(A<<16>>16))l=M<<16>>16!=0?2:0;else l=2;a[je>>2]=l;u=i[G+54>>1]|0;m=u<<16>>16==0;if(m)l=b<<16>>16!=0?2:0;else l=2;a[qe>>2]=l;w=i[G+48>>1]|0;if(!(w<<16>>16))d=r<<16>>16!=0?2:0;else d=2;a[Ve>>2]=d;_=i[G+50>>1]|0;v=_<<16>>16==0;if(v)d=x<<16>>16!=0?2:0;else d=2;a[ze>>2]=d;B=i[G+56>>1]|0;if(!(B<<16>>16))b=A<<16>>16!=0?2:0;else b=2;a[Ne>>2]=b;g=(i[G+58>>1]|0)==0;if(g)b=u<<16>>16!=0?2:0;else b=2;a[Oe>>2]=b;k=i[G+30>>1]|0;if(!(k<<16>>16))b=(i[f>>1]|0)!=0?2:0;else b=2;a[xe>>2]=b;if(!(i[G+38>>1]|0))b=(i[G+36>>1]|0)!=0?2:0;else b=2;a[Ae>>2]=b;if(p)d=y<<16>>16!=0?2:0;else d=2;a[_e>>2]=d;if(o)d=M<<16>>16!=0?2:0;else d=2;a[ye>>2]=d;if(t)l=r<<16>>16!=0?2:0;else l=2;a[ge>>2]=l;if(m)l=A<<16>>16!=0?2:0;else l=2;a[ve>>2]=l;if(v)l=w<<16>>16!=0?2:0;else l=2;a[we>>2]=l;if(g)l=B<<16>>16!=0?2:0;else l=2;a[pe>>2]=l;l=i[G+150>>1]|0;u=i[G+138>>1]|0;do{if(!((i[G+36>>1]|k)<<16>>16)){y=(i[G+148>>1]|0)-(i[G+136>>1]|0)|0;if((((y|0)>-1?y:0-y|0)|0)>3){l=1;break}y=l-u|0;if((((y|0)>-1?y:0-y|0)|0)>3){l=1;break}l=(a[G+120>>2]|0)!=(a[G+116>>2]|0)&1}else l=2}while(0);a[Me>>2]=l;l=i[G+158>>1]|0;u=i[G+146>>1]|0;do{if(!((M|E)<<16>>16)){E=(i[G+156>>1]|0)-(i[G+144>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){u=1;break}E=l-u|0;if((((E|0)>-1?E:0-E|0)|0)>3){u=1;break}u=(a[G+120>>2]|0)!=(a[G+116>>2]|0)&1}else u=2}while(0);a[Be>>2]=u;l=i[G+182>>1]|0;u=i[G+170>>1]|0;do{if(!((A|x)<<16>>16)){E=(i[G+180>>1]|0)-(i[G+168>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){r=1;break}E=l-u|0;if((((E|0)>-1?E:0-E|0)|0)>3){r=1;break}r=(a[G+128>>2]|0)!=(a[G+124>>2]|0)&1}else r=2}while(0);a[ke>>2]=r;r=i[G+190>>1]|0;t=i[G+178>>1]|0;do{if(!((B|_)<<16>>16)){E=(i[G+188>>1]|0)-(i[G+176>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){r=1;break}E=r-t|0;if((((E|0)>-1?E:0-E|0)|0)>3){r=1;break}r=(a[G+128>>2]|0)!=(a[G+124>>2]|0)&1}else r=2}while(0);a[me>>2]=r;break}else{D=i[G+32>>1]|0;M=i[G+28>>1]|0;V=i[G+142>>1]|0;m=i[G+134>>1]|0;if(!((M|D)<<16>>16)){E=(i[G+140>>1]|0)-(i[G+132>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3)u=1;else{u=V-m|0;u=(((u|0)>-1?u:0-u|0)|0)>3&1}}else u=2;a[Ke>>2]=u;I=i[G+34>>1]|0;A=i[G+30>>1]|0;z=i[G+146>>1]|0;w=i[G+138>>1]|0;if(!((A|I)<<16>>16)){E=(i[G+144>>1]|0)-(i[G+136>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3)l=1;else{l=z-w|0;l=(((l|0)>-1?l:0-l|0)|0)>3&1}}else l=2;a[Ze>>2]=l;O=i[G+40>>1]|0;_=i[G+36>>1]|0;N=i[G+158>>1]|0;v=i[G+150>>1]|0;if(!((_|O)<<16>>16)){E=(i[G+156>>1]|0)-(i[G+148>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3)d=1;else{d=N-v|0;d=(((d|0)>-1?d:0-d|0)|0)>3&1}}else d=2;a[Ge>>2]=d;d=i[G+42>>1]|0;E=i[G+38>>1]|0;L=i[G+162>>1]|0;x=i[G+154>>1]|0;if(!((E|d)<<16>>16)){B=(i[G+160>>1]|0)-(i[G+152>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3)b=1;else{b=L-x|0;b=(((b|0)>-1?b:0-b|0)|0)>3&1}}else b=2;a[We>>2]=b;S=i[G+44>>1]|0;C=i[G+166>>1]|0;do{if(!((S|D)<<16>>16)){B=(i[G+164>>1]|0)-(i[G+140>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3)b=1;else{B=C-V|0;if((((B|0)>-1?B:0-B|0)|0)>3){b=1;break}b=(a[G+124>>2]|0)!=(a[G+116>>2]|0)&1}}else b=2}while(0);a[He>>2]=b;U=i[G+46>>1]|0;F=i[G+170>>1]|0;do{if(!((U|I)<<16>>16)){B=(i[G+168>>1]|0)-(i[G+144>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){b=1;break}B=F-z|0;if((((B|0)>-1?B:0-B|0)|0)>3){b=1;break}b=(a[G+124>>2]|0)!=(a[G+116>>2]|0)&1}else b=2}while(0);a[Ye>>2]=b;P=i[G+52>>1]|0;R=i[G+182>>1]|0;do{if(!((P|O)<<16>>16)){B=(i[G+180>>1]|0)-(i[G+156>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){b=1;break}B=R-N|0;if((((B|0)>-1?B:0-B|0)|0)>3){b=1;break}b=(a[G+128>>2]|0)!=(a[G+120>>2]|0)&1}else b=2}while(0);a[je>>2]=b;t=i[G+54>>1]|0;l=i[G+186>>1]|0;do{if(!((t|d)<<16>>16)){B=(i[G+184>>1]|0)-(i[G+160>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){g=1;break}B=l-L|0;if((((B|0)>-1?B:0-B|0)|0)>3){g=1;break}g=(a[G+128>>2]|0)!=(a[G+120>>2]|0)&1}else g=2}while(0);a[qe>>2]=g;r=i[G+48>>1]|0;T=i[G+174>>1]|0;do{if(!((r|S)<<16>>16)){B=(i[G+172>>1]|0)-(i[G+164>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){g=1;break}g=T-C|0;g=(((g|0)>-1?g:0-g|0)|0)>3&1}else g=2}while(0);a[Ve>>2]=g;o=i[G+50>>1]|0;u=i[G+178>>1]|0;do{if(!((o|U)<<16>>16)){B=(i[G+176>>1]|0)-(i[G+168>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){g=1;break}g=u-F|0;g=(((g|0)>-1?g:0-g|0)|0)>3&1}else g=2}while(0);a[ze>>2]=g;f=i[G+56>>1]|0;p=i[G+190>>1]|0;do{if(!((f|P)<<16>>16)){B=(i[G+188>>1]|0)-(i[G+180>>1]|0)|0;if((((B|0)>-1?B:0-B|0)|0)>3){g=1;break}g=p-R|0;g=(((g|0)>-1?g:0-g|0)|0)>3&1}else g=2}while(0);a[Ne>>2]=g;B=i[G+58>>1]|0;k=i[G+194>>1]|0;do{if(!((B|t)<<16>>16)){y=(i[G+192>>1]|0)-(i[G+184>>1]|0)|0;if((((y|0)>-1?y:0-y|0)|0)>3){y=1;break}y=k-l|0;y=(((y|0)>-1?y:0-y|0)|0)>3&1}else y=2}while(0);a[Oe>>2]=y;do{if(!((A|M)<<16>>16)){M=(i[G+136>>1]|0)-(i[G+132>>1]|0)|0;if((((M|0)>-1?M:0-M|0)|0)>3){g=1;break}g=w-m|0;g=(((g|0)>-1?g:0-g|0)|0)>3&1}else g=2}while(0);a[xe>>2]=g;do{if(!((_|A)<<16>>16)){M=(i[G+148>>1]|0)-(i[G+136>>1]|0)|0;if((((M|0)>-1?M:0-M|0)|0)>3){g=1;break}M=v-w|0;if((((M|0)>-1?M:0-M|0)|0)>3){g=1;break}g=(a[G+120>>2]|0)!=(a[G+116>>2]|0)&1}else g=2}while(0);a[Me>>2]=g;do{if(!((E|_)<<16>>16)){E=(i[G+152>>1]|0)-(i[G+148>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){g=1;break}g=x-v|0;g=(((g|0)>-1?g:0-g|0)|0)>3&1}else g=2}while(0);a[Ae>>2]=g;do{if(!((I|D)<<16>>16)){E=(i[G+144>>1]|0)-(i[G+140>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){b=1;break}b=z-V|0;b=(((b|0)>-1?b:0-b|0)|0)>3&1}else b=2}while(0);a[_e>>2]=b;do{if(!((O|I)<<16>>16)){E=(i[G+156>>1]|0)-(i[G+144>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){b=1;break}E=N-z|0;if((((E|0)>-1?E:0-E|0)|0)>3){b=1;break}b=(a[G+120>>2]|0)!=(a[G+116>>2]|0)&1}else b=2}while(0);a[Be>>2]=b;do{if(!((d|O)<<16>>16)){E=(i[G+160>>1]|0)-(i[G+156>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){b=1;break}b=L-N|0;b=(((b|0)>-1?b:0-b|0)|0)>3&1}else b=2}while(0);a[ye>>2]=b;do{if(!((U|S)<<16>>16)){E=(i[G+168>>1]|0)-(i[G+164>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){d=1;break}d=F-C|0;d=(((d|0)>-1?d:0-d|0)|0)>3&1}else d=2}while(0);a[ge>>2]=d;do{if(!((P|U)<<16>>16)){E=(i[G+180>>1]|0)-(i[G+168>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){d=1;break}E=R-F|0;if((((E|0)>-1?E:0-E|0)|0)>3){d=1;break}d=(a[G+128>>2]|0)!=(a[G+124>>2]|0)&1}else d=2}while(0);a[ke>>2]=d;do{if(!((t|P)<<16>>16)){E=(i[G+184>>1]|0)-(i[G+180>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){l=1;break}l=l-R|0;l=(((l|0)>-1?l:0-l|0)|0)>3&1}else l=2}while(0);a[ve>>2]=l;do{if(!((o|r)<<16>>16)){E=(i[G+176>>1]|0)-(i[G+172>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){l=1;break}l=u-T|0;l=(((l|0)>-1?l:0-l|0)|0)>3&1}else l=2}while(0);a[we>>2]=l;do{if(!((f|o)<<16>>16)){E=(i[G+188>>1]|0)-(i[G+176>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){u=1;break}E=p-u|0;if((((E|0)>-1?E:0-E|0)|0)>3){u=1;break}u=(a[G+128>>2]|0)!=(a[G+124>>2]|0)&1}else u=2}while(0);a[me>>2]=u;do{if(!((B|f)<<16>>16)){E=(i[G+192>>1]|0)-(i[G+188>>1]|0)|0;if((((E|0)>-1?E:0-E|0)|0)>3){r=1;break}r=k-p|0;r=(((r|0)>-1?r:0-r|0)|0)>3&1}else r=2}while(0);a[pe>>2]=r;break}}else nr(G,fe)}while(0);if(!(h|a[Ke>>2]|a[Ze>>2]|a[Ge>>2]|a[We>>2]|a[He>>2]|a[Ye>>2]|a[je>>2]|a[qe>>2]|a[Ve>>2]|a[ze>>2]|a[Ne>>2]|a[Oe>>2]|a[xe>>2]|a[Me>>2]|a[Ae>>2]|a[_e>>2]|a[Be>>2]|a[ye>>2]|a[ge>>2]|a[ke>>2]|a[ve>>2]|a[we>>2]|a[me>>2]|a[pe>>2]))break}else{a[Oe>>2]=3;a[Ne>>2]=3;a[ze>>2]=3;a[Ve>>2]=3;a[qe>>2]=3;a[je>>2]=3;a[Ye>>2]=3;a[He>>2]=3;a[We>>2]=3;a[Ge>>2]=3;a[Ze>>2]=3;a[Ke>>2]=3;a[pe>>2]=3;a[me>>2]=3;a[we>>2]=3;a[ve>>2]=3;a[ke>>2]=3;a[ge>>2]=3;a[ye>>2]=3;a[Be>>2]=3;a[_e>>2]=3;a[Ae>>2]=3;a[Me>>2]=3;a[xe>>2]=3}L=G+20|0;t=a[L>>2]|0;I=G+12|0;u=Br(0,51,(a[I>>2]|0)+t|0)|0;D=G+16|0;o=Br(0,51,(a[D>>2]|0)+t|0)|0;f=s[6864+u>>0]|0;a[oe>>2]=f;o=s[6920+o>>0]|0;a[ue>>2]=o;u=6976+(u*3|0)|0;a[De>>2]=u;do{if(!j){l=a[(a[Y>>2]|0)+20>>2]|0;if((l|0)==(t|0)){a[K>>2]=f;a[$>>2]=o;a[Ie>>2]=u;break}else{x=(t+1+l|0)>>>1;E=Br(0,51,(a[I>>2]|0)+x|0)|0;x=Br(0,51,(a[D>>2]|0)+x|0)|0;a[K>>2]=s[6864+E>>0];a[$>>2]=s[6920+x>>0];a[Ie>>2]=6976+(E*3|0);break}}}while(0);do{if(!q){r=a[(a[H>>2]|0)+20>>2]|0;if((r|0)==(t|0)){a[X>>2]=a[oe>>2];a[J>>2]=a[ue>>2];a[ne>>2]=a[De>>2];break}else{x=(t+1+r|0)>>>1;E=Br(0,51,(a[I>>2]|0)+x|0)|0;x=Br(0,51,(a[D>>2]|0)+x|0)|0;a[X>>2]=s[6864+E>>0];a[J>>2]=s[6920+x>>0];a[ne>>2]=6976+(E*3|0);break}}}while(0);O=ee(Q,ie)|0;V=3;b=0;z=(a[e>>2]|0)+((O<<8)+(W<<4))|0;N=fe;while(1){l=a[N+4>>2]|0;if(l)ir(z,l,ae,Se);l=a[N+12>>2]|0;if(l)ir(z+4|0,l,se,Se);u=N+16|0;h=a[N+20>>2]|0;if(h)ir(z+8|0,h,se,Se);f=N+24|0;h=a[N+28>>2]|0;if(h)ir(z+12|0,h,se,Se);d=a[N>>2]|0;l=N+8|0;h=a[l>>2]|0;e:do{if(((d|0)==(h|0)?(d|0)==(a[u>>2]|0):0)?(d|0)==(a[f>>2]|0):0){if(!d)break;A=a[Ie+(b*12|0)+4>>2]|0;_=a[Ie+(b*12|0)+8>>2]|0;if(d>>>0<4){k=s[(a[Ie+(b*12|0)>>2]|0)+(d+-1)>>0]|0;u=0-k|0;f=k+1|0;r=z;o=16;while(1){b=r+Te|0;v=s[b>>0]|0;g=r+Le|0;w=s[g>>0]|0;m=s[r>>0]|0;h=r+Se|0;t=s[h>>0]|0;E=w-m|0;do{if(((E|0)>-1?E:0-E|0)>>>0<A>>>0){E=v-w|0;if(((E|0)>-1?E:0-E|0)>>>0>=_>>>0)break;E=t-m|0;if(((E|0)>-1?E:0-E|0)>>>0>=_>>>0)break;d=s[r+Fe>>0]|0;E=d-w|0;if(((E|0)>-1?E:0-E|0)>>>0<_>>>0){n[b>>0]=(Br(u,k,((w+1+m|0)>>>1)-(v<<1)+d>>1)|0)+v;b=f}else b=k;d=s[r+Ce>>0]|0;E=d-m|0;if(((E|0)>-1?E:0-E|0)>>>0<_>>>0){n[h>>0]=(Br(u,k,((w+1+m|0)>>>1)-(t<<1)+d>>1)|0)+t;b=b+1|0}x=Br(0-b|0,b,v+4-t+(m-w<<2)>>3)|0;E=n[3472+((m|512)-x)>>0]|0;n[g>>0]=n[3472+(x+(w|512))>>0]|0;n[r>>0]=E}}while(0);o=o+-1|0;if(!o)break e;else r=r+1|0}}d=(A>>>2)+2|0;k=z;g=16;while(1){h=k+Te|0;r=s[h>>0]|0;l=k+Le|0;t=s[l>>0]|0;p=s[k>>0]|0;u=k+Se|0;m=s[u>>0]|0;b=t-p|0;b=(b|0)>-1?b:0-b|0;r:do{if(b>>>0<A>>>0){E=r-t|0;if(((E|0)>-1?E:0-E|0)>>>0>=_>>>0)break;E=m-p|0;if(((E|0)>-1?E:0-E|0)>>>0>=_>>>0)break;f=k+Fe|0;w=s[f>>0]|0;o=k+Ce|0;v=s[o>>0]|0;do{if(b>>>0<d>>>0){E=w-t|0;if(((E|0)>-1?E:0-E|0)>>>0<_>>>0){E=t+r+p|0;n[l>>0]=(m+4+(E<<1)+w|0)>>>3;n[h>>0]=(E+2+w|0)>>>2;n[f>>0]=(E+4+(w*3|0)+(s[k+Ue>>0]<<1)|0)>>>3}else n[l>>0]=(t+2+(r<<1)+m|0)>>>2;E=v-p|0;if(((E|0)>-1?E:0-E|0)>>>0>=_>>>0)break;E=p+t+m|0;n[k>>0]=(r+4+(E<<1)+v|0)>>>3;n[u>>0]=(E+2+v|0)>>>2;n[o>>0]=(E+4+(v*3|0)+(s[k+Re>>0]<<1)|0)>>>3;break r}else n[l>>0]=(t+2+(r<<1)+m|0)>>>2}while(0);n[k>>0]=(r+2+p+(m<<1)|0)>>>2}}while(0);g=g+-1|0;if(!g)break;else k=k+1|0}}else Qe=311}while(0);do{if((Qe|0)==311){Qe=0;if(d){ar(z,d,Ie+(b*12|0)|0,Se);h=a[l>>2]|0}if(h)ar(z+4|0,h,Ie+(b*12|0)|0,Se);h=a[u>>2]|0;if(h)ar(z+8|0,h,Ie+(b*12|0)|0,Se);l=a[f>>2]|0;if(!l)break;ar(z+12|0,l,Ie+(b*12|0)|0,Se)}}while(0);if(!V)break;else{V=V+-1|0;b=2;z=z+Ee|0;N=N+32|0}}o=a[G+24>>2]|0;t=a[192+((Br(0,51,(a[L>>2]|0)+o|0)|0)<<2)>>2]|0;h=Br(0,51,(a[I>>2]|0)+t|0)|0;f=Br(0,51,(a[D>>2]|0)+t|0)|0;u=s[6864+h>>0]|0;a[oe>>2]=u;f=s[6920+f>>0]|0;a[ue>>2]=f;h=6976+(h*3|0)|0;a[De>>2]=h;do{if(!j){l=a[(a[Y>>2]|0)+20>>2]|0;if((l|0)==(a[L>>2]|0)){a[K>>2]=u;a[$>>2]=f;a[Ie>>2]=h;break}else{x=(t+1+(a[192+((Br(0,51,l+o|0)|0)<<2)>>2]|0)|0)>>>1;E=Br(0,51,x+(a[I>>2]|0)|0)|0;x=Br(0,51,(a[D>>2]|0)+x|0)|0;a[K>>2]=s[6864+E>>0];a[$>>2]=s[6920+x>>0];a[Ie>>2]=6976+(E*3|0);break}}}while(0);do{if(!q){r=a[(a[H>>2]|0)+20>>2]|0;if((r|0)==(a[L>>2]|0)){a[X>>2]=a[oe>>2];a[J>>2]=a[ue>>2];a[ne>>2]=a[De>>2];break}else{x=(t+1+(a[192+((Br(0,51,r+o|0)|0)<<2)>>2]|0)|0)>>>1;E=Br(0,51,x+(a[I>>2]|0)|0)|0;x=Br(0,51,(a[D>>2]|0)+x|0)|0;a[X>>2]=s[6864+E>>0];a[J>>2]=s[6920+x>>0];a[ne>>2]=6976+(E*3|0);break}}}while(0);f=a[e>>2]|0;l=(W<<3)+Pe+(O<<6)|0;d=f+l|0;l=f+(l+re)|0;f=0;o=fe;b=0;while(1){t=o+4|0;r=a[t>>2]|0;if(r){xt(d,r,ae,te);xt(l,a[t>>2]|0,ae,te)}t=o+36|0;r=a[t>>2]|0;if(r){xt(d+Se|0,r,ae,te);xt(l+Se|0,a[t>>2]|0,ae,te)}h=o+16|0;t=o+20|0;r=a[t>>2]|0;if(r){xt(d+4|0,r,se,te);xt(l+4|0,a[t>>2]|0,se,te)}t=o+52|0;r=a[t>>2]|0;if(r){xt(d+Z|0,r,se,te);xt(l+Z|0,a[t>>2]|0,se,te)}t=a[o>>2]|0;u=o+8|0;r=a[u>>2]|0;do{if((t|0)==(r|0)){if((t|0)!=(a[h>>2]|0)){Qe=342;break}if((t|0)!=(a[o+24>>2]|0)){Qe=342;break}if(!t)break;E=Ie+(f*12|0)|0;Et(d,t,E,te);Et(l,a[o>>2]|0,E,te)}else Qe=342}while(0);do{if((Qe|0)==342){Qe=0;if(t){r=Ie+(f*12|0)|0;Tt(d,t,r,te);Tt(l,a[o>>2]|0,r,te);r=a[u>>2]|0}if(r){E=Ie+(f*12|0)|0;Tt(d+2|0,r,E,te);Tt(l+2|0,a[u>>2]|0,E,te)}r=a[h>>2]|0;if(r){E=Ie+(f*12|0)|0;Tt(d+4|0,r,E,te);Tt(l+4|0,a[h>>2]|0,E,te)}t=o+24|0;r=a[t>>2]|0;if(!r)break;E=Ie+(f*12|0)|0;Tt(d+6|0,r,E,te);Tt(l+6|0,a[t>>2]|0,E,te)}}while(0);b=b+1|0;if((b|0)==2)break;else{d=d+Ce|0;l=l+Ce|0;f=2;o=o+64|0}}}}while(0);r=W+1|0;t=(r|0)==(ie|0);Q=(t&1)+Q|0;if(Q>>>0>=(a[le>>2]|0)>>>0)break;else{W=t?0:r;G=G+216|0}}c=er;return}function nr(e,r){e=e|0;r=r|0;var t=0,n=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0;E=c;l=e+28|0;A=i[e+32>>1]|0;if(!(A<<16>>16))t=(i[l>>1]|0)!=0?2:0;else t=2;a[r+32>>2]=t;M=i[e+34>>1]|0;_=M<<16>>16==0;if(_)t=(i[e+30>>1]|0)!=0?2:0;else t=2;a[r+40>>2]=t;x=i[e+40>>1]|0;y=x<<16>>16==0;if(y)t=(i[e+36>>1]|0)!=0?2:0;else t=2;a[r+48>>2]=t;n=i[e+42>>1]|0;B=n<<16>>16==0;if(B)t=(i[e+38>>1]|0)!=0?2:0;else t=2;a[r+56>>2]=t;v=i[e+44>>1]|0;if(!(v<<16>>16))t=A<<16>>16!=0?2:0;else t=2;a[r+64>>2]=t;k=i[e+46>>1]|0;p=k<<16>>16==0;if(p)t=M<<16>>16!=0?2:0;else t=2;a[r+72>>2]=t;g=i[e+52>>1]|0;m=g<<16>>16==0;if(m)t=x<<16>>16!=0?2:0;else t=2;a[r+80>>2]=t;s=i[e+54>>1]|0;w=s<<16>>16==0;if(w)t=n<<16>>16!=0?2:0;else t=2;a[r+88>>2]=t;h=i[e+48>>1]|0;if(!(h<<16>>16))t=v<<16>>16!=0?2:0;else t=2;a[r+96>>2]=t;d=i[e+50>>1]|0;o=d<<16>>16==0;if(o)t=k<<16>>16!=0?2:0;else t=2;a[r+104>>2]=t;b=i[e+56>>1]|0;f=b<<16>>16==0;if(f)n=g<<16>>16!=0?2:0;else n=2;a[r+112>>2]=n;u=(i[e+58>>1]|0)==0;if(u)n=s<<16>>16!=0?2:0;else n=2;a[r+120>>2]=n;s=i[e+30>>1]|0;if(!(s<<16>>16))n=(i[l>>1]|0)!=0?2:0;else n=2;a[r+12>>2]=n;t=i[e+36>>1]|0;if(!(t<<16>>16))n=s<<16>>16!=0?2:0;else n=2;a[r+20>>2]=n;if(!(i[e+38>>1]|0))t=t<<16>>16!=0?2:0;else t=2;a[r+28>>2]=t;if(_)t=A<<16>>16!=0?2:0;else t=2;a[r+44>>2]=t;if(y)t=M<<16>>16!=0?2:0;else t=2;a[r+52>>2]=t;if(B)t=x<<16>>16!=0?2:0;else t=2;a[r+60>>2]=t;if(p)t=v<<16>>16!=0?2:0;else t=2;a[r+76>>2]=t;if(m)t=k<<16>>16!=0?2:0;else t=2;a[r+84>>2]=t;if(w)t=g<<16>>16!=0?2:0;else t=2;a[r+92>>2]=t;if(o)t=h<<16>>16!=0?2:0;else t=2;a[r+108>>2]=t;if(f)t=d<<16>>16!=0?2:0;else t=2;a[r+116>>2]=t;if(!u){A=2;M=r+124|0;a[M>>2]=A;c=E;return}A=b<<16>>16!=0?2:0;M=r+124|0;a[M>>2]=A;c=E;return}function ir(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0;B=c;g=a[t+4>>2]|0;y=a[t+8>>2]|0;if(r>>>0<4){l=s[(a[t>>2]|0)+(r+-1)>>0]|0;d=0-l|0;h=l+1|0;u=4;while(1){t=e+-2|0;v=s[t>>0]|0;k=e+-1|0;w=s[k>>0]|0;m=s[e>>0]|0;f=e+1|0;o=s[f>>0]|0;b=w-m|0;if((((b|0)>-1?b:0-b|0)>>>0<g>>>0?(b=v-w|0,((b|0)>-1?b:0-b|0)>>>0<y>>>0):0)?(b=o-m|0,((b|0)>-1?b:0-b|0)>>>0<y>>>0):0){r=s[e+-3>>0]|0;p=s[e+2>>0]|0;b=r-w|0;if(((b|0)>-1?b:0-b|0)>>>0<y>>>0){n[t>>0]=(Br(d,l,((w+1+m|0)>>>1)-(v<<1)+r>>1)|0)+v;t=h}else t=l;b=p-m|0;if(((b|0)>-1?b:0-b|0)>>>0<y>>>0){n[f>>0]=(Br(d,l,((w+1+m|0)>>>1)-(o<<1)+p>>1)|0)+o;t=t+1|0}f=Br(0-t|0,t,v+4-o+(m-w<<2)>>3)|0;b=n[3472+((m|512)-f)>>0]|0;n[k>>0]=n[3472+((w|512)+f)>>0]|0;n[e>>0]=b}u=u+-1|0;if(!u)break;else e=e+i|0}c=B;return}k=(g>>>2)+2|0;v=4;while(1){u=e+-2|0;m=s[u>>0]|0;l=e+-1|0;w=s[l>>0]|0;h=s[e>>0]|0;r=e+1|0;d=s[r>>0]|0;t=w-h|0;t=(t|0)>-1?t:0-t|0;do{if((t>>>0<g>>>0?(b=m-w|0,((b|0)>-1?b:0-b|0)>>>0<y>>>0):0)?(b=d-h|0,((b|0)>-1?b:0-b|0)>>>0<y>>>0):0){o=e+-3|0;b=s[o>>0]|0;f=e+2|0;p=s[f>>0]|0;if(t>>>0<k>>>0){t=b-w|0;if(((t|0)>-1?t:0-t|0)>>>0<y>>>0){t=w+m+h|0;n[l>>0]=(d+4+(t<<1)+b|0)>>>3;n[u>>0]=(t+2+b|0)>>>2;n[o>>0]=(t+4+(b*3|0)+((s[e+-4>>0]|0)<<1)|0)>>>3}else n[l>>0]=(w+2+(m<<1)+d|0)>>>2;b=p-h|0;if(((b|0)>-1?b:0-b|0)>>>0<y>>>0){b=h+w+d|0;n[e>>0]=(m+4+(b<<1)+p|0)>>>3;n[r>>0]=(b+2+p|0)>>>2;n[f>>0]=(b+4+(p*3|0)+((s[e+3>>0]|0)<<1)|0)>>>3;break}}else n[l>>0]=(w+2+(m<<1)+d|0)>>>2;n[e>>0]=(m+2+h+(d<<1)|0)>>>2}}while(0);v=v+-1|0;if(!v)break;else e=e+i|0}c=B;return}function ar(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0;x=c;g=s[(a[t>>2]|0)+(r+-1)>>0]|0;B=0-i|0;y=B<<1;k=t+4|0;b=t+8|0;m=ee(i,-3)|0;v=0-g|0;p=g+1|0;w=i<<1;d=4;while(1){r=e+y|0;u=e+B|0;f=e+i|0;t=n[f>>0]|0;l=s[u>>0]|0;h=s[e>>0]|0;o=l-h|0;if((((o|0)>-1?o:0-o|0)>>>0<(a[k>>2]|0)>>>0?(A=s[r>>0]|0,o=A-l|0,_=a[b>>2]|0,((o|0)>-1?o:0-o|0)>>>0<_>>>0):0)?(M=t&255,t=M-h|0,((t|0)>-1?t:0-t|0)>>>0<_>>>0):0){t=s[e+m>>0]|0;o=t-l|0;if(((o|0)>-1?o:0-o|0)>>>0<_>>>0){n[r>>0]=(Br(v,g,((l+1+h|0)>>>1)-(A<<1)+t>>1)|0)+A;r=a[b>>2]|0;t=p}else{r=_;t=g}o=s[e+w>>0]|0;E=o-h|0;if(((E|0)>-1?E:0-E|0)>>>0<r>>>0){n[f>>0]=(Br(v,g,((l+1+h|0)>>>1)-(M<<1)+o>>1)|0)+M;t=t+1|0}t=Br(0-t|0,t,4-M+(h-l<<2)+A>>3)|0;r=n[3472+((h|512)-t)>>0]|0;n[u>>0]=n[3472+((l|512)+t)>>0]|0;n[e>>0]=r}d=d+-1|0;if(!d)break;else e=e+1|0}c=x;return}function sr(e){e=e|0;var r=0;r=c;c=c+e|0;c=c+15&-16;return r|0}function or(){return c|0}function fr(e){e=e|0;c=e}function ur(e,r){e=e|0;r=r|0;if(!p){p=e;m=r}}function lr(e){e=e|0;n[d>>0]=n[e>>0];n[d+1>>0]=n[e+1>>0];n[d+2>>0]=n[e+2>>0];n[d+3>>0]=n[e+3>>0]}function cr(e){e=e|0;n[d>>0]=n[e>>0];n[d+1>>0]=n[e+1>>0];n[d+2>>0]=n[e+2>>0];n[d+3>>0]=n[e+3>>0];n[d+4>>0]=n[e+4>>0];n[d+5>>0]=n[e+5>>0];n[d+6>>0]=n[e+6>>0];n[d+7>>0]=n[e+7>>0]}function hr(e){e=e|0;U=e}function dr(){return U|0}function br(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0;u=c;i=s[8+r>>0]|0;f=s[64+r>>0]|0;r=a[120+(f*12|0)>>2]<<i;o=a[124+(f*12|0)>>2]<<i;i=a[128+(f*12|0)>>2]<<i;if(!t)a[e>>2]=ee(a[e>>2]|0,r)|0;e:do{if(!(n&65436)){if(n&98){d=e+4|0;l=ee(a[d>>2]|0,o)|0;f=e+20|0;h=ee(a[f>>2]|0,r)|0;t=e+24|0;i=ee(a[t>>2]|0,o)|0;o=a[e>>2]|0;r=(l>>1)-i|0;i=l+(i>>1)|0;l=h+o+32|0;n=l+i>>6;a[e>>2]=n;h=o-h+32|0;o=h+r>>6;a[d>>2]=o;r=h-r>>6;a[e+8>>2]=r;i=l-i>>6;a[e+12>>2]=i;a[e+48>>2]=n;a[e+32>>2]=n;a[e+16>>2]=n;a[e+52>>2]=o;a[e+36>>2]=o;a[f>>2]=o;a[e+56>>2]=r;a[e+40>>2]=r;a[t>>2]=r;a[e+60>>2]=i;a[e+44>>2]=i;a[e+28>>2]=i;if((n+512|0)>>>0>1023|(o+512|0)>>>0>1023|(r+512|0)>>>0>1023|(i+512|0)>>>0>1023)i=1;else break;c=u;return i|0}i=(a[e>>2]|0)+32>>6;if((i+512|0)>>>0>1023){h=1;c=u;return h|0}else{a[e+60>>2]=i;a[e+56>>2]=i;a[e+52>>2]=i;a[e+48>>2]=i;a[e+44>>2]=i;a[e+40>>2]=i;a[e+36>>2]=i;a[e+32>>2]=i;a[e+28>>2]=i;a[e+24>>2]=i;a[e+20>>2]=i;a[e+16>>2]=i;a[e+12>>2]=i;a[e+8>>2]=i;a[e+4>>2]=i;a[e>>2]=i;break}}else{M=e+4|0;v=e+56|0;B=e+60|0;k=a[B>>2]|0;g=ee(a[M>>2]|0,o)|0;a[v>>2]=ee(a[v>>2]|0,o)|0;a[B>>2]=ee(k,i)|0;B=e+8|0;k=a[B>>2]|0;v=e+16|0;A=ee(a[e+20>>2]|0,r)|0;b=ee(a[v>>2]|0,i)|0;m=e+12|0;p=a[m>>2]|0;n=ee(a[e+32>>2]|0,o)|0;t=ee(a[e+24>>2]|0,o)|0;w=a[e+28>>2]|0;f=ee(a[e+48>>2]|0,i)|0;d=ee(a[e+36>>2]|0,o)|0;l=a[e+44>>2]|0;h=ee(a[e+40>>2]|0,i)|0;i=ee(a[e+52>>2]|0,o)|0;_=a[e>>2]|0;y=A+_|0;A=_-A|0;_=(g>>1)-t|0;g=(t>>1)+g|0;t=g+y|0;a[e>>2]=t;a[M>>2]=_+A;a[B>>2]=A-_;a[m>>2]=y-g;m=ee(o,w+k|0)|0;w=ee(k-w|0,o)|0;o=(b>>1)-f|0;b=(f>>1)+b|0;f=b+m|0;a[v>>2]=f;a[e+20>>2]=o+w;a[e+24>>2]=w-o;a[e+28>>2]=m-b;b=ee(r,l+p|0)|0;r=ee(p-l|0,r)|0;l=(n>>1)-i|0;n=(i>>1)+n|0;o=n+b|0;a[e+32>>2]=o;a[e+36>>2]=l+r;a[e+40>>2]=r-l;a[e+44>>2]=b-n;n=e+56|0;b=a[n>>2]|0;l=b+d|0;b=d-b|0;r=e+60|0;i=a[r>>2]|0;d=(h>>1)-i|0;h=(i>>1)+h|0;i=h+l|0;a[e+48>>2]=i;a[e+52>>2]=d+b;a[n>>2]=b-d;a[r>>2]=l-h;r=f;f=3;while(1){y=(r>>1)-i|0;i=(i>>1)+r|0;B=o+t+32|0;_=B+i>>6;a[e>>2]=_;r=t-o+32|0;A=r+y>>6;a[e+16>>2]=A;r=r-y>>6;a[e+32>>2]=r;i=B-i>>6;a[e+48>>2]=i;if((_+512|0)>>>0>1023|(A+512|0)>>>0>1023){i=1;r=14;break}if((r+512|0)>>>0>1023|(i+512|0)>>>0>1023){i=1;r=14;break}n=e+4|0;if(!f)break e;t=a[n>>2]|0;o=a[e+36>>2]|0;r=a[e+20>>2]|0;i=a[e+52>>2]|0;e=n;f=f+-1|0}if((r|0)==14){c=u;return i|0}}}while(0);A=0;c=u;return A|0}function pr(e,r){e=e|0;r=r|0;var t=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0;E=c;t=n[64+r>>0]|0;v=n[8+r>>0]|0;S=e+8|0;g=a[S>>2]|0;f=a[e+20>>2]|0;m=e+16|0;A=a[m>>2]|0;w=e+32|0;M=a[w>>2]|0;U=e+12|0;y=a[U>>2]|0;i=a[e+24>>2]|0;h=a[e+28>>2]|0;b=e+48|0;s=a[b>>2]|0;T=a[e+36>>2]|0;x=a[e+40>>2]|0;P=a[e+44>>2]|0;o=a[e+52>>2]|0;p=a[e>>2]|0;l=f+p|0;f=p-f|0;p=e+4|0;_=a[p>>2]|0;k=_-i|0;_=i+_|0;i=_+l|0;a[e>>2]=i;u=k+f|0;a[p>>2]=u;k=f-k|0;a[S>>2]=k;_=l-_|0;a[U>>2]=_;U=h+g|0;h=g-h|0;g=A-s|0;A=s+A|0;s=A+U|0;a[m>>2]=s;l=g+h|0;a[e+20>>2]=l;g=h-g|0;a[e+24>>2]=g;A=U-A|0;a[e+28>>2]=A;U=P+y|0;P=y-P|0;y=M-o|0;M=o+M|0;o=M+U|0;a[e+32>>2]=o;h=y+P|0;a[e+36>>2]=h;y=P-y|0;a[e+40>>2]=y;M=U-M|0;a[e+44>>2]=M;U=e+56|0;P=a[U>>2]|0;S=P+T|0;P=T-P|0;T=e+60|0;f=a[T>>2]|0;B=x-f|0;x=f+x|0;f=x+S|0;a[e+48>>2]=f;d=B+P|0;a[e+52>>2]=d;B=P-B|0;a[U>>2]=B;x=S-x|0;a[T>>2]=x;v=v&255;t=a[120+((t&255)*12|0)>>2]|0;if(r>>>0>11){r=t<<v+-2;v=o+i|0;o=i-o|0;i=s-f|0;t=f+s|0;a[e>>2]=ee(t+v|0,r)|0;a[m>>2]=ee(i+o|0,r)|0;a[w>>2]=ee(o-i|0,r)|0;a[b>>2]=ee(v-t|0,r)|0;w=h+u|0;t=u-h|0;m=l-d|0;v=d+l|0;a[p>>2]=ee(v+w|0,r)|0;a[e+20>>2]=ee(m+t|0,r)|0;a[e+36>>2]=ee(t-m|0,r)|0;a[e+52>>2]=ee(w-v|0,r)|0;v=y+k|0;k=k-y|0;y=g-B|0;B=B+g|0;a[e+8>>2]=ee(B+v|0,r)|0;a[e+24>>2]=ee(y+k|0,r)|0;a[e+40>>2]=ee(k-y|0,r)|0;a[e+56>>2]=ee(v-B|0,r)|0;B=M+_|0;y=_-M|0;_=A-x|0;A=x+A|0;a[e+12>>2]=ee(A+B|0,r)|0;a[e+28>>2]=ee(_+y|0,r)|0;a[e+44>>2]=ee(y-_|0,r)|0;a[e+60>>2]=ee(B-A|0,r)|0;c=E;return}else{T=(r+-6|0)>>>0<6?1:2;r=2-v|0;v=o+i|0;S=i-o|0;o=s-f|0;i=f+s|0;a[e>>2]=(ee(i+v|0,t)|0)+T>>r;a[m>>2]=(ee(o+S|0,t)|0)+T>>r;a[w>>2]=(ee(S-o|0,t)|0)+T>>r;a[b>>2]=(ee(v-i|0,t)|0)+T>>r;w=h+u|0;i=u-h|0;m=l-d|0;v=d+l|0;a[p>>2]=(ee(v+w|0,t)|0)+T>>r;a[e+20>>2]=(ee(m+i|0,t)|0)+T>>r;a[e+36>>2]=(ee(i-m|0,t)|0)+T>>r;a[e+52>>2]=(ee(w-v|0,t)|0)+T>>r;v=y+k|0;k=k-y|0;y=g-B|0;B=B+g|0;a[e+8>>2]=(ee(B+v|0,t)|0)+T>>r;a[e+24>>2]=(ee(y+k|0,t)|0)+T>>r;a[e+40>>2]=(ee(k-y|0,t)|0)+T>>r;a[e+56>>2]=(ee(v-B|0,t)|0)+T>>r;B=M+_|0;y=_-M|0;_=A-x|0;A=x+A|0;a[e+12>>2]=(ee(A+B|0,t)|0)+T>>r;a[e+28>>2]=(ee(_+y|0,t)|0)+T>>r;a[e+44>>2]=(ee(y-_|0,t)|0)+T>>r;a[e+60>>2]=(ee(B-A|0,t)|0)+T>>r;c=E;return}}function mr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,o=0,f=0,u=0,l=0,c=0,h=0;t=a[120+((s[64+r>>0]|0)*12|0)>>2]|0;if(r>>>0>5){t=t<<(s[8+r>>0]|0)+-1;r=0}else r=1;l=a[e>>2]|0;i=e+8|0;o=a[i>>2]|0;h=o+l|0;o=l-o|0;l=e+4|0;u=a[l>>2]|0;c=e+12|0;n=a[c>>2]|0;f=u-n|0;u=n+u|0;a[e>>2]=(ee(u+h|0,t)|0)>>r;a[l>>2]=(ee(h-u|0,t)|0)>>r;a[i>>2]=(ee(f+o|0,t)|0)>>r;a[c>>2]=(ee(o-f|0,t)|0)>>r;c=e+16|0;f=a[c>>2]|0;o=e+24|0;i=a[o>>2]|0;l=i+f|0;i=f-i|0;f=e+20|0;u=a[f>>2]|0;e=e+28|0;h=a[e>>2]|0;n=u-h|0;u=h+u|0;a[c>>2]=(ee(u+l|0,t)|0)>>r;a[f>>2]=(ee(l-u|0,t)|0)>>r;a[o>>2]=(ee(n+i|0,t)|0)>>r;a[e>>2]=(ee(i-n|0,t)|0)>>r;return}function wr(e,r){e=e|0;r=r|0;var t=0,n=0;n=c;r=1<<r+-1;if(!(r&e)){t=r;r=0}else{r=0;c=n;return r|0}do{r=r+1|0;t=t>>>1}while((t|0)!=0&(t&e|0)==0);c=n;return r|0}function vr(e){e=e|0;var r=0,t=0;t=c;r=8-(a[e+8>>2]|0)|0;e=Hr(e,r)|0;if((e|0)==-1){e=1;c=t;return e|0}e=(e|0)!=(a[400+(r+-1<<2)>>2]|0)&1;c=t;return e|0}function kr(e){e=e|0;var r=0,t=0,n=0,i=0;t=c;i=a[e+12>>2]<<3;n=a[e+16>>2]|0;r=i-n|0;if((i|0)==(n|0)){e=0;c=t;return e|0}if(r>>>0>8){e=1;c=t;return e|0}else{e=((Wr(e)|0)>>>(32-r|0)|0)!=(1<<r+-1|0)&1;c=t;return e|0}return 0}function gr(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0;i=c;n=a[e+(t<<2)>>2]|0;do{t=t+1|0;if(t>>>0>=r>>>0)break}while((a[e+(t<<2)>>2]|0)!=(n|0));c=i;return((t|0)==(r|0)?0:t)|0}function yr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0;n=a[e+4>>2]|0;i=(r>>>0)%(n>>>0)|0;t=r-i|0;r=ee(a[e+8>>2]|0,n)|0;n=a[e>>2]|0;a[e+12>>2]=n+((t<<8)+(i<<4));t=(i<<3)+(r<<8)+(t<<6)|0;a[e+16>>2]=n+t;a[e+20>>2]=n+(t+(r<<6));return}function Br(e,r,t){e=e|0;r=r|0;t=t|0;if((t|0)>=(e|0))e=(t|0)>(r|0)?r:t;return e|0}function _r(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0;v=c;e:do{if(((r>>>0>3?(n[e>>0]|0)==0:0)?(n[e+1>>0]|0)==0:0)?(o=n[e+2>>0]|0,(o&255)<2):0){r:do{if((r|0)!=3){p=-3;m=3;u=e+3|0;f=2;while(1){if(o<<24>>24){if(o<<24>>24==1&f>>>0>1){b=m;o=0;h=0;l=0;break}else f=0}else f=f+1|0;l=m+1|0;if((l|0)==(r|0))break r;o=n[u>>0]|0;p=~m;m=l;u=u+1|0}while(1){w=n[u>>0]|0;d=b+1|0;f=w<<24>>24!=0;l=(f&1^1)+l|0;o=w<<24>>24==3&(l|0)==2?1:o;if(w<<24>>24==1&l>>>0>1){w=14;break}if(f){h=l>>>0>2?1:h;l=0}if((d|0)==(r|0)){w=18;break}else{b=d;u=u+1|0}}if((w|0)==14){d=p+b-l|0;a[t+12>>2]=d;f=m;l=l-(l>>>0<3?l:3)|0;break e}else if((w|0)==18){d=p+r-l|0;a[t+12>>2]=d;f=m;break e}}}while(0);a[i>>2]=r;m=1;c=v;return m|0}else w=19}while(0);if((w|0)==19){a[t+12>>2]=r;d=r;o=1;f=0;h=0;l=0}u=e+f|0;a[t>>2]=u;a[t+4>>2]=u;a[t+8>>2]=0;a[t+16>>2]=0;b=t+12|0;a[i>>2]=l+f+d;if(h){m=1;c=v;return m|0}if(!o){m=0;c=v;return m|0}l=a[b>>2]|0;o=u;h=u;f=0;e:while(1){while(1){m=l;l=l+-1|0;if(!m){w=31;break e}u=n[o>>0]|0;if((f|0)!=2)break;if(u<<24>>24!=3){w=29;break}if(!l){o=1;w=32;break e}o=o+1|0;if((s[o>>0]|0)>3){o=1;w=32;break e}else f=0}if((w|0)==29){w=0;if((u&255)<3){o=1;w=32;break}else f=2}n[h>>0]=u;o=o+1|0;h=h+1|0;f=u<<24>>24==0?f+1|0:0}if((w|0)==31){a[b>>2]=h-o+(a[b>>2]|0);m=0;c=v;return m|0}else if((w|0)==32){c=v;return o|0}return 0}function Ar(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0;b=c;c=c+16|0;d=b;cn(r,0,92);i=Hr(e,8)|0;e:do{if((((i|0)!=-1?(a[r>>2]=i,Hr(e,1)|0,Hr(e,1)|0,(Hr(e,1)|0)!=-1):0)?(Hr(e,5)|0)!=-1:0)?(n=Hr(e,8)|0,(n|0)!=-1):0){h=r+4|0;a[h>>2]=n;i=r+8|0;t=Kr(e,i)|0;if(!t)if((a[i>>2]|0)>>>0<=31){t=Kr(e,d)|0;if(!t){i=a[d>>2]|0;if(i>>>0<=12){a[r+12>>2]=1<<i+4;t=Kr(e,d)|0;if(!t){i=a[d>>2]|0;if(i>>>0<=2){a[r+16>>2]=i;r:do{if(!i){t=Kr(e,d)|0;if(t)break e;i=a[d>>2]|0;if(i>>>0>12){t=1;break e}a[r+20>>2]=1<<i+4}else if((i|0)==1){i=Hr(e,1)|0;if((i|0)==-1){t=1;break e}a[r+24>>2]=(i|0)==1&1;t=Xr(e,r+28|0)|0;if(t)break e;t=Xr(e,r+32|0)|0;if(t)break e;o=r+36|0;t=Kr(e,o)|0;if(t)break e;i=a[o>>2]|0;if(i>>>0>255){t=1;break e}if(!i){a[r+40>>2]=0;break}i=fn(i<<2)|0;s=r+40|0;a[s>>2]=i;if(!i){t=65535;break e}if(a[o>>2]|0){n=0;while(1){t=Xr(e,i+(n<<2)|0)|0;n=n+1|0;if(t)break e;if(n>>>0>=(a[o>>2]|0)>>>0)break r;i=a[s>>2]|0}}}}while(0);l=r+44|0;t=Kr(e,l)|0;if(!t)if((a[l>>2]|0)>>>0<=16?(u=Hr(e,1)|0,(u|0)!=-1):0){a[r+48>>2]=(u|0)==1&1;t=Kr(e,d)|0;if(!t){n=r+52|0;a[n>>2]=(a[d>>2]|0)+1;t=Kr(e,d)|0;if(!t){u=r+56|0;a[u>>2]=(a[d>>2]|0)+1;o=Hr(e,1)|0;if((!((o|0)==0|(o|0)==-1)?(Hr(e,1)|0)!=-1:0)?(f=Hr(e,1)|0,(f|0)!=-1):0){f=(f|0)==1;a[r+60>>2]=f&1;if(f){f=r+64|0;t=Kr(e,f)|0;if(t)break;i=r+68|0;t=Kr(e,i)|0;if(t)break;o=r+72|0;t=Kr(e,o)|0;if(t)break;s=r+76|0;t=Kr(e,s)|0;if(t)break;n=a[n>>2]|0;if((a[f>>2]|0)>((n<<3)+~a[i>>2]|0)){t=1;break}i=a[u>>2]|0;if((a[o>>2]|0)>((i<<3)+~a[s>>2]|0)){t=1;break}}else{n=a[n>>2]|0;i=a[u>>2]|0}t=ee(i,n)|0;do{switch(a[h>>2]|0){case 11:{i=396;n=345600;s=58;break}case 12:{i=396;n=912384;s=58;break}case 13:{i=396;n=912384;s=58;break}case 20:{i=396;n=912384;s=58;break}case 21:{i=792;n=1824768;s=58;break}case 22:{i=1620;n=3110400;s=58;break}case 30:{i=1620;n=3110400;s=58;break}case 31:{i=3600;n=6912e3;s=58;break}case 32:{i=5120;n=7864320;s=58;break}case 40:{i=8192;n=12582912;s=58;break}case 41:{i=8192;n=12582912;s=58;break}case 42:{i=8704;n=13369344;s=58;break}case 50:{i=22080;n=42393600;s=58;break}case 51:{i=36864;n=70778880;s=58;break}case 10:{i=99;n=152064;s=58;break}default:s=60}}while(0);do{if((s|0)==58){if(i>>>0<t>>>0){s=60;break}n=(n>>>0)/((t*384|0)>>>0)|0;n=n>>>0<16?n:16;a[d>>2]=n;i=a[l>>2]|0;if(i>>>0>n>>>0){n=i;s=61}}}while(0);if((s|0)==60){a[d>>2]=2147483647;n=a[l>>2]|0;s=61}if((s|0)==61)a[d>>2]=n;s=r+88|0;a[s>>2]=n;n=Hr(e,1)|0;if((n|0)==-1){t=1;break}d=(n|0)==1;a[r+80>>2]=d&1;do{if(d){n=fn(952)|0;i=r+84|0;a[i>>2]=n;if(!n){t=65535;break e}t=Rt(e,n)|0;if(t)break e;t=a[i>>2]|0;if(!(a[t+920>>2]|0))break;n=a[t+948>>2]|0;if((a[t+944>>2]|0)>>>0>n>>>0){t=1;break e}if(n>>>0<(a[l>>2]|0)>>>0){t=1;break e}if(n>>>0>(a[s>>2]|0)>>>0){t=1;break e}a[s>>2]=(n|0)==0?1:n}}while(0);vr(e)|0;t=0}else t=1}}}else t=1}else t=1}}else t=1}}else t=1}else t=1}while(0);c=b;return t|0}function Mr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0;f=c;if((a[e>>2]|0)!=(a[r>>2]|0)){t=1;c=f;return t|0}if((a[e+4>>2]|0)!=(a[r+4>>2]|0)){t=1;c=f;return t|0}if((a[e+12>>2]|0)!=(a[r+12>>2]|0)){t=1;c=f;return t|0}t=a[e+16>>2]|0;if((t|0)!=(a[r+16>>2]|0)){t=1;c=f;return t|0}if((a[e+44>>2]|0)!=(a[r+44>>2]|0)){t=1;c=f;return t|0}if((a[e+48>>2]|0)!=(a[r+48>>2]|0)){t=1;c=f;return t|0}if((a[e+52>>2]|0)!=(a[r+52>>2]|0)){t=1;c=f;return t|0}if((a[e+56>>2]|0)!=(a[r+56>>2]|0)){t=1;c=f;return t|0}o=a[e+60>>2]|0;if((o|0)!=(a[r+60>>2]|0)){t=1;c=f;return t|0}if((a[e+80>>2]|0)!=(a[r+80>>2]|0)){t=1;c=f;return t|0}e:do{if(!t){if((a[e+20>>2]|0)!=(a[r+20>>2]|0)){t=1;c=f;return t|0}}else if((t|0)==1){if((a[e+24>>2]|0)!=(a[r+24>>2]|0)){t=1;c=f;return t|0}if((a[e+28>>2]|0)!=(a[r+28>>2]|0)){t=1;c=f;return t|0}if((a[e+32>>2]|0)!=(a[r+32>>2]|0)){t=1;c=f;return t|0}t=a[e+36>>2]|0;if((t|0)!=(a[r+36>>2]|0)){t=1;c=f;return t|0}if(t){n=a[e+40>>2]|0;i=a[r+40>>2]|0;s=0;while(1){if((a[n+(s<<2)>>2]|0)!=(a[i+(s<<2)>>2]|0)){t=1;break}s=s+1|0;if(s>>>0>=t>>>0)break e}c=f;return t|0}}}while(0);if(o){if((a[e+64>>2]|0)!=(a[r+64>>2]|0)){t=1;c=f;return t|0}if((a[e+68>>2]|0)!=(a[r+68>>2]|0)){t=1;c=f;return t|0}if((a[e+72>>2]|0)!=(a[r+72>>2]|0)){t=1;c=f;return t|0}if((a[e+76>>2]|0)!=(a[r+76>>2]|0)){t=1;c=f;return t|0}}t=0;c=f;return t|0}function xr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0;l=c;c=c+16|0;f=l+4|0;u=l;cn(r,0,72);t=Kr(e,r)|0;if(t){c=l;return t|0}if((a[r>>2]|0)>>>0>255){t=1;c=l;return t|0}n=r+4|0;t=Kr(e,n)|0;if(t){c=l;return t|0}if((a[n>>2]|0)>>>0>31){t=1;c=l;return t|0}if(Hr(e,1)|0){t=1;c=l;return t|0}t=Hr(e,1)|0;if((t|0)==-1){t=1;c=l;return t|0}a[r+8>>2]=(t|0)==1&1;t=Kr(e,f)|0;if(t){c=l;return t|0}t=(a[f>>2]|0)+1|0;o=r+12|0;a[o>>2]=t;if(t>>>0>8){t=1;c=l;return t|0}e:do{if(t>>>0>1){t=r+16|0;n=Kr(e,t)|0;if(n){t=n;c=l;return t|0}t=a[t>>2]|0;if(t>>>0>6){t=1;c=l;return t|0}switch(t|0){case 5:case 4:case 3:{t=Hr(e,1)|0;if((t|0)==-1){t=1;c=l;return t|0}a[r+32>>2]=(t|0)==1&1;t=Kr(e,f)|0;if(!t){a[r+36>>2]=(a[f>>2]|0)+1;break e}else{c=l;return t|0}}case 0:{t=fn(a[o>>2]<<2)|0;i=r+20|0;a[i>>2]=t;if(!t){t=65535;c=l;return t|0}if(!(a[o>>2]|0))break e;else n=0;while(1){t=Kr(e,f)|0;if(t)break;a[(a[i>>2]|0)+(n<<2)>>2]=(a[f>>2]|0)+1;n=n+1|0;if(n>>>0>=(a[o>>2]|0)>>>0)break e}c=l;return t|0}case 2:{n=r+24|0;a[n>>2]=fn((a[o>>2]<<2)+-4|0)|0;t=fn((a[o>>2]<<2)+-4|0)|0;s=r+28|0;a[s>>2]=t;if((a[n>>2]|0)==0|(t|0)==0){t=65535;c=l;return t|0}if((a[o>>2]|0)==1)break e;else i=0;while(1){t=Kr(e,f)|0;if(t){n=46;break}a[(a[n>>2]|0)+(i<<2)>>2]=a[f>>2];t=Kr(e,f)|0;if(t){n=46;break}a[(a[s>>2]|0)+(i<<2)>>2]=a[f>>2];i=i+1|0;if(i>>>0>=((a[o>>2]|0)+-1|0)>>>0)break e}if((n|0)==46){c=l;return t|0}break}case 6:{t=Kr(e,f)|0;if(t){c=l;return t|0}n=(a[f>>2]|0)+1|0;t=r+40|0;a[t>>2]=n;n=fn(n<<2)|0;s=r+44|0;a[s>>2]=n;if(!n){t=65535;c=l;return t|0}i=a[432+((a[o>>2]|0)+-1<<2)>>2]|0;if(!(a[t>>2]|0))break e;else n=0;while(1){h=Hr(e,i)|0;a[(a[s>>2]|0)+(n<<2)>>2]=h;n=n+1|0;if(h>>>0>=(a[o>>2]|0)>>>0){t=1;break}if(n>>>0>=(a[t>>2]|0)>>>0)break e}c=l;return t|0}default:break e}}}while(0);t=Kr(e,f)|0;if(t){e=t;c=l;return e|0}t=a[f>>2]|0;if(t>>>0>31){e=1;c=l;return e|0}a[r+48>>2]=t+1;t=Kr(e,f)|0;if(t){e=t;c=l;return e|0}if((a[f>>2]|0)>>>0>31){e=1;c=l;return e|0}if(Hr(e,1)|0){e=1;c=l;return e|0}if((Hr(e,2)|0)>>>0>2){e=1;c=l;return e|0}t=Xr(e,u)|0;if(t){e=t;c=l;return e|0}t=(a[u>>2]|0)+26|0;if(t>>>0>51){e=1;c=l;return e|0}a[r+52>>2]=t;t=Xr(e,u)|0;if(t){e=t;c=l;return e|0}if(((a[u>>2]|0)+26|0)>>>0>51){e=1;c=l;return e|0}t=Xr(e,u)|0;if(t){e=t;c=l;return e|0}t=a[u>>2]|0;if((t+12|0)>>>0>24){e=1;c=l;return e|0}a[r+56>>2]=t;t=Hr(e,1)|0;if((t|0)==-1){e=1;c=l;return e|0}a[r+60>>2]=(t|0)==1&1;t=Hr(e,1)|0;if((t|0)==-1){e=1;c=l;return e|0}a[r+64>>2]=(t|0)==1&1;t=Hr(e,1)|0;if((t|0)==-1){e=1;c=l;return e|0}a[r+68>>2]=(t|0)==1&1;vr(e)|0;e=0;c=l;return e|0}function Er(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0;B=c;c=c+32|0;v=B+20|0;m=B+16|0;b=B+12|0;l=B+8|0;y=B+4|0;k=B;cn(r,0,988);g=ee(a[t+56>>2]|0,a[t+52>>2]|0)|0;u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}d=a[y>>2]|0;a[r>>2]=d;if(d>>>0>=g>>>0){i=1;c=B;return i|0}u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}u=a[y>>2]|0;h=r+4|0;a[h>>2]=u;if((u|0)==5|(u|0)==0)f=5;else if(!((u|0)==7|(u|0)==2)){i=1;c=B;return i|0}if((f|0)==5){if((a[i>>2]|0)==5){i=1;c=B;return i|0}if(!(a[t+44>>2]|0)){i=1;c=B;return i|0}}u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}d=a[y>>2]|0;a[r+8>>2]=d;if((d|0)!=(a[n>>2]|0)){i=1;c=B;return i|0}d=t+12|0;u=a[d>>2]|0;f=0;while(1)if(!(u>>>f))break;else f=f+1|0;u=Hr(e,f+-1|0)|0;if((u|0)==-1){i=1;c=B;return i|0}f=(a[i>>2]|0)==5;if(f&(u|0)!=0){i=1;c=B;return i|0}a[r+12>>2]=u;if(f){u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}u=a[y>>2]|0;a[r+16>>2]=u;if(u>>>0>65535){i=1;c=B;return i|0}}s=t+16|0;u=a[s>>2]|0;if(!u){o=t+20|0;u=a[o>>2]|0;f=0;while(1)if(!(u>>>f))break;else f=f+1|0;u=Hr(e,f+-1|0)|0;if((u|0)==-1){i=1;c=B;return i|0}f=r+20|0;a[f>>2]=u;do{if(a[n+8>>2]|0){u=Xr(e,k)|0;if(!u){a[r+24>>2]=a[k>>2];break}else{i=u;c=B;return i|0}}}while(0);if((a[i>>2]|0)==5){u=a[f>>2]|0;if(u>>>0>(a[o>>2]|0)>>>1>>>0){i=1;c=B;return i|0}f=a[r+24>>2]|0;if((u|0)!=(((f|0)>0?0:0-f|0)|0)){i=1;c=B;return i|0}}u=a[s>>2]|0}if((u|0)==1?(a[t+24>>2]|0)==0:0){u=Xr(e,k)|0;if(u){i=u;c=B;return i|0}u=r+28|0;a[u>>2]=a[k>>2];do{if(a[n+8>>2]|0){f=Xr(e,k)|0;if(!f){a[r+32>>2]=a[k>>2];break}else{i=f;c=B;return i|0}}}while(0);if((a[i>>2]|0)==5?(f=a[u>>2]|0,u=(a[t+32>>2]|0)+f+(a[r+32>>2]|0)|0,(((f|0)<(u|0)?f:u)|0)!=0):0){i=1;c=B;return i|0}}if(a[n+68>>2]|0){u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}u=a[y>>2]|0;a[r+36>>2]=u;if(u>>>0>127){i=1;c=B;return i|0}}u=a[h>>2]|0;if((u|0)==5|(u|0)==0){u=Hr(e,1)|0;if((u|0)==-1){i=1;c=B;return i|0}a[r+40>>2]=u;do{if(!u){u=a[n+48>>2]|0;if(u>>>0>16){i=1;c=B;return i|0}else{a[r+44>>2]=u;break}}else{u=Kr(e,y)|0;if(u){i=u;c=B;return i|0}u=a[y>>2]|0;if(u>>>0>15){i=1;c=B;return i|0}else{a[r+44>>2]=u+1;break}}}while(0);u=a[h>>2]|0}do{if((u|0)==5|(u|0)==0){s=a[r+44>>2]|0;f=a[d>>2]|0;u=Hr(e,1)|0;if((u|0)==-1){i=1;c=B;return i|0}a[r+68>>2]=u;if(u){o=0;e:while(1){if(o>>>0>s>>>0){w=1;f=110;break}u=Kr(e,l)|0;if(u){w=u;f=110;break}u=a[l>>2]|0;if(u>>>0>3){w=1;f=110;break}a[r+(o*12|0)+72>>2]=u;do{if(u>>>0<2){u=Kr(e,b)|0;if(u){w=u;f=110;break e}u=a[b>>2]|0;if(u>>>0>=f>>>0){w=1;f=110;break e}a[r+(o*12|0)+76>>2]=u+1}else{if((u|0)!=2)break;u=Kr(e,b)|0;if(u){w=u;f=110;break e}a[r+(o*12|0)+80>>2]=a[b>>2]}}while(0);if((a[l>>2]|0)==3){f=61;break}else o=o+1|0}if((f|0)==61){if(!o)w=1;else break;c=B;return w|0}else if((f|0)==110){c=B;return w|0}}}}while(0);do{if(a[i+4>>2]|0){d=a[t+44>>2]|0;i=(a[i>>2]|0)==5;u=Hr(e,1)|0;f=(u|0)==-1;if(i){if(f){i=1;c=B;return i|0}a[r+276>>2]=u;s=Hr(e,1)|0;if((s|0)==-1){i=1;c=B;return i|0}a[r+280>>2]=s;if((d|0)!=0|(s|0)==0)break;else w=1;c=B;return w|0}if(f){i=1;c=B;return i|0}a[r+284>>2]=u;if(u){f=(d<<1)+2|0;o=0;t=0;s=0;l=0;h=0;while(1){if(o>>>0>f>>>0){w=1;f=110;break}u=Kr(e,m)|0;if(u){w=u;f=110;break}u=a[m>>2]|0;if(u>>>0>6){w=1;f=110;break}a[r+(o*20|0)+288>>2]=u;if((u&-3|0)==1){u=Kr(e,v)|0;if(u){w=u;f=110;break}a[r+(o*20|0)+292>>2]=(a[v>>2]|0)+1;u=a[m>>2]|0}if((u|0)==2){u=Kr(e,v)|0;if(u){w=u;f=110;break}a[r+(o*20|0)+296>>2]=a[v>>2];u=a[m>>2]|0}if((u|0)==3|(u|0)==6){u=Kr(e,v)|0;if(u){w=u;f=110;break}a[r+(o*20|0)+300>>2]=a[v>>2];u=a[m>>2]|0}if((u|0)==4){u=Kr(e,v)|0;if(u){w=u;f=110;break}u=a[v>>2]|0;if(u>>>0>d>>>0){w=1;f=110;break}if(!u)a[r+(o*20|0)+304>>2]=65535;else a[r+(o*20|0)+304>>2]=u+-1;u=a[m>>2]|0;p=s+1|0}else p=s;l=((u|0)==5&1)+l|0;t=((u|0)!=0&u>>>0<4&1)+t|0;h=((u|0)==6&1)+h|0;if(!u){f=90;break}else{o=o+1|0;s=p}}if((f|0)==90){if(p>>>0>1|l>>>0>1|h>>>0>1){i=1;c=B;return i|0}if((t|0)!=0&(l|0)!=0)w=1;else break;c=B;return w|0}else if((f|0)==110){c=B;return w|0}}}}while(0);s=Xr(e,k)|0;if(s){i=s;c=B;return i|0}i=a[k>>2]|0;a[r+48>>2]=i;i=i+(a[n+52>>2]|0)|0;a[k>>2]=i;if(i>>>0>51){i=1;c=B;return i|0}do{if(a[n+60>>2]|0){s=Kr(e,y)|0;if(s){i=s;c=B;return i|0}s=a[y>>2]|0;a[r+52>>2]=s;if(s>>>0>2){i=1;c=B;return i|0}if((s|0)==1)break;s=Xr(e,k)|0;if(s){i=s;c=B;return i|0}s=a[k>>2]|0;if((s+6|0)>>>0>12){i=1;c=B;return i|0}a[r+56>>2]=s<<1;s=Xr(e,k)|0;if(s){i=s;c=B;return i|0}s=a[k>>2]|0;if((s+6|0)>>>0>12){i=1;c=B;return i|0}else{a[r+60>>2]=s<<1;break}}}while(0);do{if((a[n+12>>2]|0)>>>0>1?((a[n+16>>2]|0)+-3|0)>>>0<3:0){u=n+36|0;f=a[u>>2]|0;f=(((g>>>0)%(f>>>0)|0|0)==0?1:2)+((g>>>0)/(f>>>0)|0)|0;o=0;while(1){s=o+1|0;if(!(-1<<s&f))break;else o=s}s=Hr(e,((1<<o)+-1&f|0)==0?o:s)|0;a[y>>2]=s;if((s|0)==-1){i=1;c=B;return i|0}a[r+64>>2]=s;i=a[u>>2]|0;if(s>>>0>(((g+-1+i|0)>>>0)/(i>>>0)|0)>>>0)w=1;else break;c=B;return w|0}}while(0);i=0;c=B;return i|0}function Tr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0;i=c;c=c+32|0;n=i+20|0;t=i;a[t+0>>2]=a[e+0>>2];a[t+4>>2]=a[e+4>>2];a[t+8>>2]=a[e+8>>2];a[t+12>>2]=a[e+12>>2];a[t+16>>2]=a[e+16>>2];e=Kr(t,n)|0;if(!e){e=Kr(t,n)|0;if(!e){e=Kr(t,n)|0;if(!e){e=a[n>>2]|0;if(e>>>0>255)e=1;else{a[r>>2]=e;e=0}}}}c=i;return e|0}function Sr(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0;s=c;c=c+32|0;n=s+20|0;i=s;a[i+0>>2]=a[e+0>>2];a[i+4>>2]=a[e+4>>2];a[i+8>>2]=a[e+8>>2];a[i+12>>2]=a[e+12>>2];a[i+16>>2]=a[e+16>>2];e=Kr(i,n)|0;if(e){c=s;return e|0}e=Kr(i,n)|0;if(e){c=s;return e|0}e=Kr(i,n)|0;if(!e)e=0;else{c=s;return e|0}while(1)if(!(r>>>e))break;else e=e+1|0;e=Hr(i,e+-1|0)|0;if((e|0)==-1){e=1;c=s;return e|0}a[t>>2]=e;e=0;c=s;return e|0}function Ur(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0;o=c;c=c+32|0;i=o+20|0;s=o;if((t|0)!=5){t=1;c=o;return t|0}a[s+0>>2]=a[e+0>>2];a[s+4>>2]=a[e+4>>2];a[s+8>>2]=a[e+8>>2];a[s+12>>2]=a[e+12>>2];a[s+16>>2]=a[e+16>>2];t=Kr(s,i)|0;if(t){c=o;return t|0}t=Kr(s,i)|0;if(t){c=o;return t|0}t=Kr(s,i)|0;if(!t)t=0;else{c=o;return t|0}while(1)if(!(r>>>t))break;else t=t+1|0;if((Hr(s,t+-1|0)|0)==-1){t=1;c=o;return t|0}t=Kr(s,n)|0;c=o;return t|0}function Pr(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0;u=c;c=c+32|0;o=u+20|0;f=u;a[f+0>>2]=a[e+0>>2];a[f+4>>2]=a[e+4>>2];a[f+8>>2]=a[e+8>>2];a[f+12>>2]=a[e+12>>2];a[f+16>>2]=a[e+16>>2];e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=a[r+12>>2]|0;i=0;while(1)if(!(e>>>i))break;else i=i+1|0;if((Hr(f,i+-1|0)|0)==-1){i=1;c=u;return i|0}if((t|0)==5?(s=Kr(f,o)|0,(s|0)!=0):0){i=s;c=u;return i|0}i=a[r+20>>2]|0;e=0;while(1)if(!(i>>>e))break;else e=e+1|0;i=Hr(f,e+-1|0)|0;if((i|0)==-1){i=1;c=u;return i|0}a[n>>2]=i;i=0;c=u;return i|0}function Rr(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0;u=c;c=c+32|0;o=u+20|0;f=u;a[f+0>>2]=a[e+0>>2];a[f+4>>2]=a[e+4>>2];a[f+8>>2]=a[e+8>>2];a[f+12>>2]=a[e+12>>2];a[f+16>>2]=a[e+16>>2];e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=Kr(f,o)|0;if(e){i=e;c=u;return i|0}e=a[r+12>>2]|0;i=0;while(1)if(!(e>>>i))break;else i=i+1|0;if((Hr(f,i+-1|0)|0)==-1){i=1;c=u;return i|0}if((t|0)==5?(s=Kr(f,o)|0,(s|0)!=0):0){i=s;c=u;return i|0}i=a[r+20>>2]|0;e=0;while(1)if(!(i>>>e))break;else e=e+1|0;if((Hr(f,e+-1|0)|0)==-1){i=1;c=u;return i|0}i=Xr(f,n)|0;c=u;return i|0}function Fr(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0;h=c;c=c+32|0;f=h+20|0;l=h;a[l+0>>2]=a[e+0>>2];a[l+4>>2]=a[e+4>>2];a[l+8>>2]=a[e+8>>2];a[l+12>>2]=a[e+12>>2];a[l+16>>2]=a[e+16>>2];s=Kr(l,f)|0;if(s){l=s;c=h;return l|0}s=Kr(l,f)|0;if(s){l=s;c=h;return l|0}s=Kr(l,f)|0;if(s){l=s;c=h;return l|0}s=a[r+12>>2]|0;e=0;while(1)if(!(s>>>e))break;else e=e+1|0;if((Hr(l,e+-1|0)|0)==-1){l=1;c=h;return l|0}if((t|0)==5?(o=Kr(l,f)|0,(o|0)!=0):0){l=o;c=h;return l|0}s=Xr(l,i)|0;if(s){l=s;c=h;return l|0}if((n|0)!=0?(u=Xr(l,i+4|0)|0,(u|0)!=0):0){l=u;c=h;return l|0}l=0;c=h;return l|0}function Cr(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0;h=c;c=c+32|0;l=h+24|0;f=h+20|0;u=h;a[u+0>>2]=a[r+0>>2];a[u+4>>2]=a[r+4>>2];a[u+8>>2]=a[r+8>>2];a[u+12>>2]=a[r+12>>2];a[u+16>>2]=a[r+16>>2];i=Kr(u,l)|0;if(i){d=i;c=h;return d|0}i=Kr(u,l)|0;if(i){d=i;c=h;return d|0}i=Kr(u,l)|0;if(i){d=i;c=h;return d|0}i=a[t+12>>2]|0;r=0;while(1)if(!(i>>>r))break;else r=r+1|0;if((Hr(u,r+-1|0)|0)==-1){d=1;c=h;return d|0}i=Kr(u,l)|0;if(i){d=i;c=h;return d|0}s=t+16|0;i=a[s>>2]|0;if(!i){r=a[t+20>>2]|0;i=0;while(1)if(!(r>>>i))break;else i=i+1|0;if((Hr(u,i+-1|0)|0)==-1){d=1;c=h;return d|0}if((a[n+8>>2]|0)!=0?(o=Xr(u,f)|0,(o|0)!=0):0){d=o;c=h;return d|0}i=a[s>>2]|0}if((i|0)==1?(a[t+24>>2]|0)==0:0){i=Xr(u,f)|0;if(i){d=i;c=h;return d|0}if((a[n+8>>2]|0)!=0?(d=Xr(u,f)|0,(d|0)!=0):0){c=h;return d|0}}if((a[n+68>>2]|0)!=0?(b=Kr(u,l)|0,(b|0)!=0):0){d=b;c=h;return d|0}d=Hr(u,1)|0;a[e>>2]=d;d=(d|0)==-1&1;c=h;return d|0}function Lr(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0;T=c;c=c+448|0;p=T+8|0;_=T+4|0;y=T;p=p+(0-p&15)|0;d=a[r+3376>>2]|0;f=a[n>>2]|0;a[_>>2]=0;A=r+1192|0;a[A>>2]=(a[A>>2]|0)+1;m=r+1200|0;a[m>>2]=0;b=r+12|0;a[y>>2]=(a[n+48>>2]|0)+(a[(a[b>>2]|0)+52>>2]|0);B=n+36|0;w=r+1212|0;v=n+52|0;k=n+56|0;g=n+60|0;M=n+4|0;l=n+44|0;o=r+1220|0;h=r+1172|0;E=r+1176|0;u=d+12|0;x=0;i=0;while(1){n=a[w>>2]|0;if((a[B>>2]|0)==0?(a[n+(f*216|0)+196>>2]|0)!=0:0){i=1;n=22;break}s=a[(a[b>>2]|0)+56>>2]|0;P=a[v>>2]|0;U=a[k>>2]|0;S=a[g>>2]|0;a[n+(f*216|0)+4>>2]=a[A>>2];a[n+(f*216|0)+8>>2]=P;a[n+(f*216|0)+12>>2]=U;a[n+(f*216|0)+16>>2]=S;a[n+(f*216|0)+24>>2]=s;n=a[M>>2]|0;if((n|0)!=2?!((n|0)==7|(i|0)!=0):0){i=Kr(e,_)|0;if(i){n=22;break}n=a[_>>2]|0;if(n>>>0>((a[E>>2]|0)-f|0)>>>0){i=1;n=22;break}if(!n)i=0;else{cn(u,0,164);a[d>>2]=0;i=1}}n=a[_>>2]|0;if(!n){i=Ir(e,d,(a[w>>2]|0)+(f*216|0)|0,a[M>>2]|0,a[l>>2]|0)|0;if(!i)s=0;else{n=22;break}}else{a[_>>2]=n+-1;s=i}i=qr((a[w>>2]|0)+(f*216|0)|0,d,t,o,y,f,a[(a[b>>2]|0)+64>>2]|0,p)|0;if(i){n=22;break}x=((a[(a[w>>2]|0)+(f*216|0)+196>>2]|0)==1&1)+x|0;if(!(kr(e)|0))n=(a[_>>2]|0)!=0;else n=1;i=a[M>>2]|0;if((i|0)==7|(i|0)==2)a[m>>2]=f;f=gr(a[h>>2]|0,a[E>>2]|0,f)|0;if(!((f|0)!=0|n^1)){i=1;n=22;break}if(!n){n=20;break}else i=s}if((n|0)==20){n=r+1196|0;i=(a[n>>2]|0)+x|0;if(i>>>0>(a[E>>2]|0)>>>0){A=1;c=T;return A|0}a[n>>2]=i;A=0;c=T;return A|0}else if((n|0)==22){c=T;return i|0}return 0}function Dr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0;u=c;o=a[e+1192>>2]|0;t=a[e+1200>>2]|0;f=e+1212|0;e:do{if(!t)t=r;else{n=e+16|0;i=0;do{do{t=t+-1|0;if(t>>>0<=r>>>0)break e}while((a[(a[f>>2]|0)+(t*216|0)+4>>2]|0)!=(o|0));i=i+1|0;s=a[(a[n>>2]|0)+52>>2]|0}while(i>>>0<(s>>>0>10?s:10)>>>0)}}while(0);s=e+1172|0;r=e+1176|0;while(1){n=a[f>>2]|0;if((a[n+(t*216|0)+4>>2]|0)!=(o|0)){t=11;break}i=n+(t*216|0)+196|0;n=a[i>>2]|0;if(!n){t=11;break}a[i>>2]=n+-1;t=gr(a[s>>2]|0,a[r>>2]|0,t)|0;if(!t){t=11;break}}if((t|0)==11){c=u;return}}function Ir(e,r,t,n,s){e=e|0;r=r|0;t=t|0;n=n|0;s=s|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0;E=c;c=c+32|0;g=E+20|0;y=E+16|0;m=E+12|0;p=E+8|0;M=E+4|0;A=E;cn(r,0,2088);l=Kr(e,M)|0;h=a[M>>2]|0;do{if((n|0)==2|(n|0)==7){h=h+6|0;if(h>>>0>31|(l|0)!=0){t=1;c=E;return t|0}else{a[r>>2]=h;b=h;break}}else{h=h+1|0;if(h>>>0>31|(l|0)!=0){t=1;c=E;return t|0}else{a[r>>2]=h;b=h;break}}}while(0);e:do{if((b|0)!=31){r:do{if(b>>>0>=6){b=(b|0)!=6;p=b&1;if(!p){a[y>>2]=0;m=0;while(1){n=Wr(e)|0;a[g>>2]=n;k=n>>>31;a[r+(m<<2)+12>>2]=k;if(!k){a[r+(m<<2)+76>>2]=n>>>28&7;h=n<<4;d=1}else{h=n<<1;d=0}n=m|1;k=h>>>31;a[r+(n<<2)+12>>2]=k;if(!k){a[r+(n<<2)+76>>2]=h>>>28&7;l=h<<4;d=d+1|0}else l=h<<1;h=n+1|0;k=l>>>31;a[r+(h<<2)+12>>2]=k;if(!k){a[r+(h<<2)+76>>2]=l>>>28&7;h=l<<4;d=d+1|0}else h=l<<1;l=m|3;k=h>>>31;a[r+(l<<2)+12>>2]=k;if(!k){a[r+(l<<2)+76>>2]=h>>>28&7;n=h<<4;d=d+1|0}else n=h<<1;h=l+1|0;k=n>>>31;a[r+(h<<2)+12>>2]=k;if(!k){a[r+(h<<2)+76>>2]=n>>>28&7;n=n<<4;d=d+1|0}else n=n<<1;h=l+2|0;k=n>>>31;a[r+(h<<2)+12>>2]=k;if(!k){a[r+(h<<2)+76>>2]=n>>>28&7;n=n<<4;d=d+1|0}else n=n<<1;h=l+3|0;k=n>>>31;a[r+(h<<2)+12>>2]=k;if(!k){a[r+(h<<2)+76>>2]=n>>>28&7;n=n<<4;d=d+1|0}else n=n<<1;h=m|7;k=n>>>31;a[r+(h<<2)+12>>2]=k;if(!k){a[r+(h<<2)+76>>2]=n>>>28&7;h=n<<4;d=d+1|0}else h=n<<1;a[g>>2]=h;if((Gr(e,(d*3|0)+8|0)|0)==-1){B=1;k=68;break r}k=(a[y>>2]|0)+1|0;a[y>>2]=k;if((k|0)<2)m=m+8|0;else{k=52;break}}}else if((p|0)==1)k=52;if((k|0)==52){y=(Kr(e,g)|0)!=0;l=a[g>>2]|0;if(y|l>>>0>3){B=1;k=68;break}a[r+140>>2]=l}if(b){y=a[r>>2]|0;v=y+-7|0;g=v>>>2;a[r+4>>2]=(v>>>0>11?g+268435453|0:g)<<4|(y>>>0>18?15:0)}else{_=p;k=70}}else{if((b|0)==0|(b|0)==1){w=y;v=g}else if(!((b|0)==3|(b|0)==2)){n=0;do{l=(Kr(e,m)|0)!=0;h=a[m>>2]|0;if(l|h>>>0>3){d=1;k=96;break}a[r+(n<<2)+176>>2]=h;n=n+1|0}while(n>>>0<4);if((k|0)==96){c=E;return d|0}t:do{if(s>>>0>1&(b|0)!=5){h=s>>>0>2&1;n=0;while(1){if(Jr(e,m,h)|0){d=1;k=96;break}d=a[m>>2]|0;if(d>>>0>=s>>>0){d=1;k=96;break}a[r+(n<<2)+192>>2]=d;n=n+1|0;if(n>>>0>=4){o=0;break t}}if((k|0)==96){c=E;return d|0}}else o=0}while(0);t:while(1){d=a[r+(o<<2)+176>>2]|0;if(!d)d=0;else if((d|0)==2|(d|0)==1)d=1;else d=3;a[m>>2]=d;h=0;while(1){d=Xr(e,p)|0;if(d){k=96;break t}i[r+(o<<4)+(h<<2)+208>>1]=a[p>>2];d=Xr(e,p)|0;if(d){k=96;break t}i[r+(o<<4)+(h<<2)+210>>1]=a[p>>2];k=a[m>>2]|0;a[m>>2]=k+-1;if(!k)break;else h=h+1|0}o=o+1|0;if(o>>>0>=4){_=2;k=70;break r}}if((k|0)==96){c=E;return d|0}}else{w=y;v=g}if(s>>>0>1){if((b|0)==0|(b|0)==1)d=0;else if((b|0)==3|(b|0)==2)d=1;else d=3;l=s>>>0>2&1;n=0;while(1){if(Jr(e,g,l)|0){B=1;k=68;break r}h=a[g>>2]|0;if(h>>>0>=s>>>0){B=1;k=68;break r}a[r+(n<<2)+144>>2]=h;if(!d)break;else{d=d+-1|0;n=n+1|0}}}if((b|0)==0|(b|0)==1){l=0;h=0}else if((b|0)==3|(b|0)==2){l=1;h=0}else{l=3;h=0}while(1){n=Xr(e,y)|0;if(n){B=n;k=68;break r}i[r+(h<<2)+160>>1]=a[y>>2];n=Xr(e,y)|0;if(n){B=n;k=68;break r}i[r+(h<<2)+162>>1]=a[y>>2];if(!l){_=2;k=70;break}else{l=l+-1|0;h=h+1|0}}}}while(0);if((k|0)==68){t=B;c=E;return t|0}do{if((k|0)==70){o=$r(e,M,(_|0)==0&1)|0;if(!o){M=a[M>>2]|0;a[r+4>>2]=M;if(!M)break e;else break}else{t=o;c=E;return t|0}}}while(0);M=(Xr(e,A)|0)!=0;o=a[A>>2]|0;if(M|(o|0)<-26|(o|0)>25){t=1;c=E;return t|0}a[r+8>>2]=o;l=a[r+4>>2]|0;b=r+272|0;r:do{if((a[r>>2]|0)>>>0>=7){o=Qr(e,r+1864|0,Yr(t,0,b)|0,16)|0;if(!(o&15)){i[r+320>>1]=o>>>4&255;o=0;h=3;while(1){d=l>>>1;if(!(l&1))o=o+4|0;else{n=3;while(1){l=Qr(e,r+(o<<6)+332|0,Yr(t,o,b)|0,15)|0;a[r+(o<<2)+1992>>2]=l>>>15;if(l&15){f=l;break r}i[r+(o<<1)+272>>1]=l>>>4&255;o=o+1|0;if(!n)break;else n=n+-1|0}}if(!h){u=o;x=d;k=87;break}else{l=d;h=h+-1|0}}}else f=o}else{o=0;h=3;while(1){d=l>>>1;if(!(l&1))o=o+4|0;else{n=3;while(1){l=Qr(e,r+(o<<6)+328|0,Yr(t,o,b)|0,16)|0;a[r+(o<<2)+1992>>2]=l>>>16;if(l&15){f=l;break r}i[r+(o<<1)+272>>1]=l>>>4&255;o=o+1|0;if(!n)break;else n=n+-1|0}}if(!h){u=o;x=d;k=87;break}else{l=d;h=h+-1|0}}}}while(0);r:do{if((k|0)==87){if(x&3){f=Qr(e,r+1928|0,-1,4)|0;if(f&15)break;i[r+322>>1]=f>>>4&255;f=Qr(e,r+1944|0,-1,4)|0;if(f&15)break;i[r+324>>1]=f>>>4&255}if(!(x&2))f=0;else{o=7;while(1){f=Qr(e,r+(u<<6)+332|0,Yr(t,u,b)|0,15)|0;if(f&15)break r;i[r+(u<<1)+272>>1]=f>>>4&255;a[r+(u<<2)+1992>>2]=f>>>15;if(!o){f=0;break}else{u=u+1|0;o=o+-1|0}}}}}while(0);a[e+16>>2]=((a[e+4>>2]|0)-(a[e>>2]|0)<<3)+(a[e+8>>2]|0);if(f){t=f;c=E;return t|0}}else{while(1){if(Zr(e)|0)break;if(Hr(e,1)|0){d=1;k=96;break}}if((k|0)==96){c=E;return d|0}u=0;f=r+328|0;while(1){o=Hr(e,8)|0;a[M>>2]=o;if((o|0)==-1){d=1;break}a[f>>2]=o;u=u+1|0;if(u>>>0>=384)break e;else f=f+4|0}c=E;return d|0}}while(0);t=0;c=E;return t|0}function Or(e){e=e|0;if(e>>>0<6)e=2;else e=(e|0)!=6&1;return e|0}function Nr(e){e=e|0;var r=0;r=c;if((e|0)==0|(e|0)==1)e=1;else if((e|0)==3|(e|0)==2)e=2;else e=4;c=r;return e|0}function zr(e){e=e|0;var r=0;r=c;if(!e)e=1;else if((e|0)==2|(e|0)==1)e=2;else e=4;c=r;return e|0}function Vr(e){e=e|0;return e+1&3|0}function qr(e,r,t,s,o,f,u,l){e=e|0;r=r|0;t=t|0;s=s|0;o=o|0;f=f|0;u=u|0;l=l|0;var h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0;y=c;v=a[r>>2]|0;a[e>>2]=v;b=e+196|0;a[b>>2]=(a[b>>2]|0)+1;yr(t,f);if((v|0)==31){h=e+28|0;a[e+20>>2]=0;if((a[b>>2]|0)>>>0>1){i[h>>1]=16;i[e+30>>1]=16;i[e+32>>1]=16;i[e+34>>1]=16;i[e+36>>1]=16;i[e+38>>1]=16;i[e+40>>1]=16;i[e+42>>1]=16;i[e+44>>1]=16;i[e+46>>1]=16;i[e+48>>1]=16;i[e+50>>1]=16;i[e+52>>1]=16;i[e+54>>1]=16;i[e+56>>1]=16;i[e+58>>1]=16;i[e+60>>1]=16;i[e+62>>1]=16;i[e+64>>1]=16;i[e+66>>1]=16;i[e+68>>1]=16;i[e+70>>1]=16;i[e+72>>1]=16;i[e+74>>1]=16;k=0;c=y;return k|0}b=23;o=r+328|0;d=l;while(1){i[h>>1]=16;n[d>>0]=a[o>>2];n[d+1>>0]=a[o+4>>2];n[d+2>>0]=a[o+8>>2];n[d+3>>0]=a[o+12>>2];n[d+4>>0]=a[o+16>>2];n[d+5>>0]=a[o+20>>2];n[d+6>>0]=a[o+24>>2];n[d+7>>0]=a[o+28>>2];n[d+8>>0]=a[o+32>>2];n[d+9>>0]=a[o+36>>2];n[d+10>>0]=a[o+40>>2];n[d+11>>0]=a[o+44>>2];n[d+12>>0]=a[o+48>>2];n[d+13>>0]=a[o+52>>2];n[d+14>>0]=a[o+56>>2];n[d+15>>0]=a[o+60>>2];if(!b)break;else{b=b+-1|0;o=o+64|0;d=d+16|0;h=h+2|0}}er(t,l);k=0;c=y;return k|0}h=e+28|0;if(v){ln(h,r+272|0,54);d=a[r+8>>2]|0;b=a[o>>2]|0;do{if(d){b=b+d|0;a[o>>2]=b;if((b|0)<0){b=b+52|0;a[o>>2]=b;break}if((b|0)>51){b=b+-52|0;a[o>>2]=b}}}while(0);w=e+20|0;a[w>>2]=b;d=r+328|0;o=r+1992|0;e:do{if((a[e>>2]|0)>>>0<7){m=15;b=h;while(1){if(i[b>>1]|0){if(br(d,a[w>>2]|0,0,a[o>>2]|0)|0){h=1;break}}else a[d>>2]=16777215;d=d+64|0;b=b+2|0;o=o+4|0;if(!m)break e;else m=m+-1|0}c=y;return h|0}else{if(!(i[e+76>>1]|0)){m=464;p=15;b=h}else{pr(r+1864|0,b);m=464;p=15;b=h}while(1){h=a[r+(a[m>>2]<<2)+1864>>2]|0;m=m+4|0;a[d>>2]=h;if((h|0)==0?(i[b>>1]|0)==0:0)a[d>>2]=16777215;else g=18;if((g|0)==18?(g=0,(br(d,a[w>>2]|0,1,a[o>>2]|0)|0)!=0):0){h=1;break}d=d+64|0;b=b+2|0;o=o+4|0;if(!p)break e;else p=p+-1|0}c=y;return h|0}}while(0);m=a[192+((Br(0,51,(a[e+24>>2]|0)+(a[w>>2]|0)|0)|0)<<2)>>2]|0;if((i[e+78>>1]|0)==0?(i[e+80>>1]|0)==0:0){p=r+1928|0;h=7}else{p=r+1928|0;mr(p,m);h=7}while(1){w=a[p>>2]|0;p=p+4|0;a[d>>2]=w;if((w|0)==0?(i[b>>1]|0)==0:0)a[d>>2]=16777215;else g=31;if((g|0)==31?(g=0,(br(d,m,1,a[o>>2]|0)|0)!=0):0){h=1;g=39;break}if(!h)break;else{d=d+64|0;o=o+4|0;h=h+-1|0;b=b+2|0}}if((g|0)==39){c=y;return h|0}if(v>>>0>=6){b=gt(e,r,t,f,u,l)|0;if(b){k=b;c=y;return k|0}}else g=37}else{cn(h,0,54);a[e+20>>2]=a[o>>2];g=37}if((g|0)==37?(k=xe(e,r,s,f,t,l)|0,(k|0)!=0):0){c=y;return k|0}k=0;c=y;return k|0}function jr(e){e=e|0;return e|0}function Yr(e,r,t){e=e|0;r=r|0;t=t|0;var s=0,o=0,f=0,u=0,l=0;u=c;l=nt(r)|0;s=it(r)|0;o=n[l+4>>0]|0;f=n[s+4>>0]|0;s=(a[s>>2]|0)==4;if((a[l>>2]|0)==4){r=i[t+((o&255)<<1)>>1]|0;if(s){r=r+1+(i[t+((f&255)<<1)>>1]|0)>>1;c=u;return r|0}s=e+204|0;if(!(ot(e,a[s>>2]|0)|0)){c=u;return r|0}r=r+1+(i[(a[s>>2]|0)+((f&255)<<1)+28>>1]|0)>>1;c=u;return r|0}if(s){r=i[t+((f&255)<<1)>>1]|0;s=e+200|0;if(!(ot(e,a[s>>2]|0)|0)){c=u;return r|0}r=r+1+(i[(a[s>>2]|0)+((o&255)<<1)+28>>1]|0)>>1;c=u;return r|0}s=e+200|0;if(!(ot(e,a[s>>2]|0)|0)){o=0;t=0}else{o=i[(a[s>>2]|0)+((o&255)<<1)+28>>1]|0;t=1}s=e+204|0;if(!(ot(e,a[s>>2]|0)|0)){r=o;c=u;return r|0}r=i[(a[s>>2]|0)+((f&255)<<1)+28>>1]|0;if(!t){c=u;return r|0}r=o+1+r>>1;c=u;return r|0}function Hr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0;p=c;d=e+4|0;f=a[d>>2]|0;h=a[e+12>>2]<<3;b=e+16|0;l=a[b>>2]|0;i=h-l|0;if((i|0)>31){t=e+8|0;i=a[t>>2]|0;n=(s[f+1>>0]|0)<<16|(s[f>>0]|0)<<24|(s[f+2>>0]|0)<<8|(s[f+3>>0]|0);if(!i)o=t;else{o=t;n=(s[f+4>>0]|0)>>>(8-i|0)|n<<i}}else{o=e+8|0;if((i|0)>0){t=a[o>>2]|0;u=t+24|0;n=(s[f>>0]|0)<<u;i=i+-8+t|0;if((i|0)>0){t=i;i=u;do{f=f+1|0;i=i+-8|0;n=(s[f>>0]|0)<<i|n;t=t+-8|0}while((t|0)>0)}}else n=0}t=l+r|0;a[b>>2]=t;a[o>>2]=t&7;if(t>>>0>h>>>0){d=-1;c=p;return d|0}a[d>>2]=(a[e>>2]|0)+(t>>>3);d=n>>>(32-r|0);c=p;return d|0}function Wr(e){e=e|0;var r=0,t=0,n=0,i=0,o=0;i=c;n=a[e+4>>2]|0;t=(a[e+12>>2]<<3)-(a[e+16>>2]|0)|0;if((t|0)>31){r=a[e+8>>2]|0;e=(s[n+1>>0]|0)<<16|(s[n>>0]|0)<<24|(s[n+2>>0]|0)<<8|(s[n+3>>0]|0);if(!r){r=e;c=i;return r|0}r=(s[n+4>>0]|0)>>>(8-r|0)|e<<r;c=i;return r|0}if((t|0)<=0){r=0;c=i;return r|0}o=a[e+8>>2]|0;e=o+24|0;r=(s[n>>0]|0)<<e;t=t+-8+o|0;if((t|0)<=0){c=i;return r|0}do{n=n+1|0;e=e+-8|0;r=(s[n>>0]|0)<<e|r;t=t+-8|0}while((t|0)>0);c=i;return r|0}function Gr(e,r){e=e|0;r=r|0;var t=0,n=0;t=c;n=e+16|0;r=(a[n>>2]|0)+r|0;a[n>>2]=r;a[e+8>>2]=r&7;if(r>>>0>a[e+12>>2]<<3>>>0){r=-1;c=t;return r|0}a[e+4>>2]=(a[e>>2]|0)+(r>>>3);r=0;c=t;return r|0}function Zr(e){e=e|0;return(a[e+8>>2]|0)==0|0}function Kr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0;s=c;t=Wr(e)|0;do{if((t|0)>=0){if(t>>>0>1073741823){if((Gr(e,3)|0)==-1){t=1;break}a[r>>2]=(t>>>29&1)+1;t=0;break}if(t>>>0>536870911){if((Gr(e,5)|0)==-1){t=1;break}a[r>>2]=(t>>>27&3)+3;t=0;break}if(t>>>0>268435455){if((Gr(e,7)|0)==-1){t=1;break}a[r>>2]=(t>>>25&7)+7;t=0;break}t=wr(t,28)|0;n=t+4|0;if((n|0)!=32){Gr(e,t+5|0)|0;t=Hr(e,n)|0;if((t|0)==-1){t=1;break}a[r>>2]=(1<<n)+-1+t;t=0;break}a[r>>2]=0;Gr(e,32)|0;if((Hr(e,1)|0)==1?(i=Wr(e)|0,(Gr(e,32)|0)!=-1):0){if((i|0)==1){a[r>>2]=-1;t=1;break}else if(!i){a[r>>2]=-1;t=0;break}else{t=1;break}}else t=1}else{Gr(e,1)|0;a[r>>2]=0;t=0}}while(0);c=s;return t|0}function Xr(e,r){e=e|0;r=r|0;var t=0,n=0,i=0;n=c;c=c+16|0;i=n;a[i>>2]=0;t=Kr(e,i)|0;e=a[i>>2]|0;t=(t|0)==0;if((e|0)==-1){if(t)e=1;else{a[r>>2]=-2147483648;e=0}}else if(t){t=(e+1|0)>>>1;a[r>>2]=(e&1|0)!=0?t:0-t|0;e=0}else e=1;c=n;return e|0}function $r(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0;i=c;c=c+16|0;n=i;if(Kr(e,n)|0){n=1;c=i;return n|0}n=a[n>>2]|0;if(n>>>0>47){n=1;c=i;return n|0}a[r>>2]=s[((t|0)==0?576:528)+n>>0];n=0;c=i;return n|0}function Jr(e,r,t){e=e|0;r=r|0;t=t|0;var n=0;n=c;if(!t){t=Hr(e,1)|0;a[r>>2]=t;if((t|0)==-1)t=1;else{a[r>>2]=t^1;t=0}}else t=Kr(e,r)|0;c=n;return t|0}function Qr(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0,N=0,z=0;z=c;c=c+128|0;O=z+64|0;N=z;d=Wr(e)|0;p=d>>>16;do{if(t>>>0<2){if((d|0)>=0){if(d>>>0>201326591){b=o[1264+(d>>>26<<1)>>1]|0;u=25;break}if(d>>>0>16777215){b=o[1328+(d>>>22<<1)>>1]|0;u=25;break}if(d>>>0>2097151){b=o[1424+((d>>>18)+-8<<1)>>1]|0;u=25;break}else{b=o[1536+(p<<1)>>1]|0;u=25;break}}else m=1}else if(t>>>0<4){if((d|0)<0){m=(p&16384|0)!=0?2:2082;break}if(d>>>0>268435455){b=o[1600+(d>>>26<<1)>>1]|0;u=25;break}if(d>>>0>33554431){b=o[1664+(d>>>23<<1)>>1]|0;u=25;break}else{b=o[1728+(d>>>18<<1)>>1]|0;u=25;break}}else{if(t>>>0<8){t=d>>>26;if((t+-8|0)>>>0<56){b=o[1984+(t<<1)>>1]|0;u=25;break}b=o[2112+(d>>>22<<1)>>1]|0;u=25;break}if(t>>>0<17){b=o[2368+(d>>>26<<1)>>1]|0;u=25;break}t=d>>>29;if(t){b=o[2496+(t<<1)>>1]|0;u=25;break}b=o[2512+(d>>>24<<1)>>1]|0;u=25;break}}while(0);if((u|0)==25)if(!b){T=1;c=z;return T|0}else m=b;b=m&31;t=d<<b;p=32-b|0;C=m>>>11&31;if(C>>>0>n>>>0){T=1;c=z;return T|0}y=m>>>5&63;do{if(C){if(!y)b=0;else{do{if(p>>>0<y>>>0)if((Gr(e,b)|0)==-1){T=1;c=z;return T|0}else{p=32;t=Wr(e)|0;break}}while(0);d=t>>>(32-y|0);t=t<<y;u=0;b=1<<y+-1;do{a[O+(u<<2)>>2]=(b&d|0)!=0?-1:1;b=b>>>1;u=u+1|0}while((b|0)!=0);p=p-y|0;b=u}g=y>>>0<3;e:do{if(b>>>0<C>>>0){k=b;v=C>>>0>10&g&1;r:while(1){if(p>>>0<16){if((Gr(e,32-p|0)|0)==-1){L=1;u=127;break}w=32;t=Wr(e)|0}else w=p;do{if((t|0)>=0){if(t>>>0<=1073741823){if(t>>>0<=536870911){if(t>>>0<=268435455){if(t>>>0<=134217727){if(t>>>0<=67108863){if(t>>>0<=33554431){if(t>>>0<=16777215){if(t>>>0<=8388607){if(t>>>0>4194303){F=9;u=59}else{if(t>>>0>2097151){F=10;u=59;break}if(t>>>0>1048575){F=11;u=59;break}if(t>>>0>524287){F=12;u=59;break}if(t>>>0>262143){F=13;u=59;break}if(t>>>0>131071){p=14;b=t<<15;d=w+-15|0;m=v;u=(v|0)!=0?v:4}else{if(t>>>0<65536){L=1;u=127;break r}p=15;b=t<<16;d=w+-16|0;m=(v|0)!=0?v:1;u=12}R=p<<m;E=b;A=d;M=m;_=u;B=(m|0)==0;u=60}}else{F=8;u=59}}else{F=7;u=59}}else{F=6;u=59}}else{F=5;u=59}}else{F=4;u=59}}else{F=3;u=59}}else{F=2;u=59}}else{F=1;u=59}}else{F=0;u=59}}while(0);if((u|0)==59){u=0;p=F+1|0;b=t<<p;p=w-p|0;t=F<<v;if(!v){U=p;P=b;x=t;T=0;S=1}else{R=t;E=b;A=p;M=v;_=v;B=0;u=60}}if((u|0)==60){if(A>>>0<_>>>0){if((Gr(e,32-A|0)|0)==-1){L=1;u=127;break}b=32;t=Wr(e)|0}else{b=A;t=E}U=b-_|0;P=t<<_;x=(t>>>(32-_|0))+R|0;T=M;S=B}v=(k|0)==(y|0)&g?x+2|0:x;b=(v+2|0)>>>1;d=S?1:T;a[O+(k<<2)>>2]=(v&1|0)==0?b:0-b|0;k=k+1|0;if(k>>>0>=C>>>0){l=U;h=P;break e}else{p=U;t=P;v=((b|0)>(3<<d+-1|0)&d>>>0<6&1)+d|0}}if((u|0)==127){c=z;return L|0}}else{l=p;h=t}}while(0);if(C>>>0<n>>>0){do{if(l>>>0<9)if((Gr(e,32-l|0)|0)==-1){T=1;c=z;return T|0}else{l=32;h=Wr(e)|0;break}}while(0);u=h>>>23;e:do{if((n|0)==4){if((h|0)>=0){if((C|0)!=3){if(h>>>0<=1073741823){if((C|0)==2)u=34;else u=h>>>0>536870911?35:51}else u=18}else u=17}else u=1}else{do{switch(C|0){case 8:{u=s[1056+(h>>>26)>>0]|0;break}case 9:{u=s[1120+(h>>>26)>>0]|0;break}case 2:{u=s[736+(h>>>26)>>0]|0;break}case 1:{if(h>>>0>268435455)u=s[672+(h>>>27)>>0]|0;else u=s[704+u>>0]|0;break}case 13:{u=s[1248+(h>>>29)>>0]|0;break}case 14:{u=s[1256+(h>>>30)>>0]|0;break}case 3:{u=s[800+(h>>>26)>>0]|0;break}case 4:{u=s[864+(h>>>27)>>0]|0;break}case 5:{u=s[896+(h>>>27)>>0]|0;break}case 10:{u=s[1184+(h>>>27)>>0]|0;break}case 6:{u=s[928+(h>>>26)>>0]|0;break}case 7:{u=s[992+(h>>>26)>>0]|0;break}case 11:{u=s[1216+(h>>>28)>>0]|0;break}case 12:{u=s[1232+(h>>>28)>>0]|0;break}default:{u=h>>31&16|1;break e}}}while(0);if(!u){T=1;c=z;return T|0}}}while(0);d=u&15;l=l-d|0;h=h<<d;d=u>>>4&15}else d=0;p=C+-1|0;t=(p|0)==0;if(t){a[r+(d<<2)>>2]=a[O+(p<<2)>>2];D=l;i=1<<d;break}else{u=h;b=0}e:while(1){if(!d){a[N+(b<<2)>>2]=1;I=l;f=0}else{if(l>>>0<11){if((Gr(e,32-l|0)|0)==-1){L=1;u=127;break}l=32;u=Wr(e)|0}switch(d|0){case 4:{h=s[648+(u>>>29)>>0]|0;break}case 5:{h=s[656+(u>>>29)>>0]|0;break}case 6:{h=s[664+(u>>>29)>>0]|0;break}case 1:{h=s[624+(u>>>31)>>0]|0;break}case 2:{h=s[632+(u>>>30)>>0]|0;break}case 3:{h=s[640+(u>>>30)>>0]|0;break}default:{do{if(u>>>0<=536870911){if(u>>>0<=268435455){if(u>>>0<=134217727){if(u>>>0<=67108863){if(u>>>0<=33554431){if(u>>>0>16777215)h=184;else{if(u>>>0>8388607){h=201;break}if(u>>>0>4194303){h=218;break}h=u>>>0<2097152?0:235}}else h=167}else h=150}else h=133}else h=116}else h=u>>>29<<4^115}while(0);if((h>>>4&15)>>>0>d>>>0){L=1;u=127;break e}}}if(!h){L=1;u=127;break}T=h&15;f=h>>>4&15;a[N+(b<<2)>>2]=f+1;I=l-T|0;u=u<<T;f=d-f|0}b=b+1|0;if(b>>>0>=p>>>0){u=122;break}else{l=I;d=f}}if((u|0)==122){a[r+(f<<2)>>2]=a[O+(p<<2)>>2];i=1<<f;if(t){D=I;break}u=C+-2|0;while(1){f=(a[N+(u<<2)>>2]|0)+f|0;i=1<<f|i;a[r+(f<<2)>>2]=a[O+(u<<2)>>2];if(!u){D=I;break}else u=u+-1|0}}else if((u|0)==127){c=z;return L|0}}else{D=p;i=0}}while(0);if(Gr(e,32-D|0)|0){T=1;c=z;return T|0}T=i<<16|C<<4;c=z;return T|0}function et(e,r){e=e|0;r=r|0;var t=0,n=0,i=0;i=c;e:do{if((Hr(e,1)|0)!=-1?(n=r+4|0,a[n>>2]=Hr(e,2)|0,t=Hr(e,5)|0,a[r>>2]=t,(t+-2|0)>>>0>=3):0){switch(t|0){case 6:case 9:case 10:case 11:case 12:{if(a[n>>2]|0){t=1;break e}break}case 5:case 7:case 8:{if(!(a[n>>2]|0)){t=1;break e}switch(t|0){case 6:case 9:case 10:case 11:case 12:{t=1;break e}default:{}}break}default:{}}t=0}else t=1}while(0);c=i;return t|0}function rt(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0;b=c;if(!t){c=b;return}h=r+-1|0;f=1-r|0;u=~r;s=0;o=0;l=0;while(1){i=(s|0)!=0;if(i)a[e+(o*216|0)+200>>2]=e+((o+-1|0)*216|0);else a[e+(o*216|0)+200>>2]=0;n=(l|0)!=0;if(n){a[e+(o*216|0)+204>>2]=e+((o-r|0)*216|0);if(s>>>0<h>>>0)a[e+(o*216|0)+208>>2]=e+((f+o|0)*216|0);else d=10}else{a[e+(o*216|0)+204>>2]=0;d=10}if((d|0)==10){d=0;a[e+(o*216|0)+208>>2]=0}if(n&i)a[e+(o*216|0)+212>>2]=e+((o+u|0)*216|0);else a[e+(o*216|0)+212>>2]=0;n=s+1|0;i=(n|0)==(r|0);o=o+1|0;if((o|0)==(t|0))break;else{s=i?0:n;l=(i&1)+l|0}}c=b;return}function tt(e,r){e=e|0;r=r|0;var t=0;t=c;switch(r|0){case 1:{e=a[e+204>>2]|0;break}case 3:{e=a[e+212>>2]|0;break}case 4:break;case 2:{e=a[e+208>>2]|0;break}case 0:{e=a[e+200>>2]|0;break}default:e=0}c=t;return e|0}function nt(e){e=e|0;return 3152+(e<<3)|0}function it(e){e=e|0;return 2960+(e<<3)|0}function at(e){e=e|0;return 2768+(e<<3)|0}function st(e){e=e|0;return 2576+(e<<3)|0}function ot(e,r){e=e|0;r=r|0;var t=0;t=c;if(!r){c=t;return 0}else{c=t;return(a[e+4>>2]|0)==(a[r+4>>2]|0)|0}return 0}function ft(e){e=e|0;var r=0;r=c;cn(e,0,3388);a[e+8>>2]=32;a[e+4>>2]=256;a[e+1332>>2]=1;c=r;return}function ut(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0;o=c;i=a[r+8>>2]|0;s=e+(i<<2)+20|0;n=a[s>>2]|0;do{if(!n){t=fn(92)|0;a[s>>2]=t;if(!t){t=65535;c=o;return t|0}}else{t=e+8|0;if((i|0)!=(a[t>>2]|0)){un(a[n+40>>2]|0);a[(a[s>>2]|0)+40>>2]=0;un(a[(a[s>>2]|0)+84>>2]|0);a[(a[s>>2]|0)+84>>2]=0;break}i=e+16|0;if(Mr(r,a[i>>2]|0)|0){un(a[(a[s>>2]|0)+40>>2]|0);a[(a[s>>2]|0)+40>>2]=0;un(a[(a[s>>2]|0)+84>>2]|0);a[(a[s>>2]|0)+84>>2]=0;a[t>>2]=33;a[e+4>>2]=257;a[i>>2]=0;a[e+12>>2]=0;break}t=r+40|0;un(a[t>>2]|0);a[t>>2]=0;t=r+84|0;un(a[t>>2]|0);a[t>>2]=0;t=0;c=o;return t|0}}while(0);i=(a[s>>2]|0)+0|0;t=r+0|0;n=i+92|0;do{a[i>>2]=a[t>>2];i=i+4|0;t=t+4|0}while((i|0)<(n|0));t=0;c=o;return t|0}function lt(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0;o=c;i=a[r>>2]|0;s=e+(i<<2)+148|0;t=a[s>>2]|0;do{if(!t){t=fn(72)|0;a[s>>2]=t;if(!t){t=65535;c=o;return t|0}}else{n=e+4|0;if((i|0)!=(a[n>>2]|0)){un(a[t+20>>2]|0);a[(a[s>>2]|0)+20>>2]=0;un(a[(a[s>>2]|0)+24>>2]|0);a[(a[s>>2]|0)+24>>2]=0;un(a[(a[s>>2]|0)+28>>2]|0);a[(a[s>>2]|0)+28>>2]=0;un(a[(a[s>>2]|0)+44>>2]|0);a[(a[s>>2]|0)+44>>2]=0;break}if((a[r+4>>2]|0)!=(a[e+8>>2]|0)){a[n>>2]=257;t=a[s>>2]|0}un(a[t+20>>2]|0);a[(a[s>>2]|0)+20>>2]=0;un(a[(a[s>>2]|0)+24>>2]|0);a[(a[s>>2]|0)+24>>2]=0;un(a[(a[s>>2]|0)+28>>2]|0);a[(a[s>>2]|0)+28>>2]=0;un(a[(a[s>>2]|0)+44>>2]|0);a[(a[s>>2]|0)+44>>2]=0}}while(0);i=(a[s>>2]|0)+0|0;t=r+0|0;n=i+72|0;do{a[i>>2]=a[t>>2];i=i+4|0;t=t+4|0}while((i|0)<(n|0));t=0;c=o;return t|0}function ct(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0;m=c;b=e+(r<<2)+148|0;i=a[b>>2]|0;if(!i){b=1;c=m;return b|0}d=a[i+4>>2]|0;s=a[e+(d<<2)+20>>2]|0;if(!s){b=1;c=m;return b|0}l=a[s+52>>2]|0;h=ee(a[s+56>>2]|0,l)|0;o=a[i+12>>2]|0;e:do{if(o>>>0>1){s=a[i+16>>2]|0;if((s|0)==2){u=a[i+24>>2]|0;f=a[i+28>>2]|0;o=o+-1|0;n=0;while(1){i=a[u+(n<<2)>>2]|0;s=a[f+(n<<2)>>2]|0;if(!(i>>>0<=s>>>0&s>>>0<h>>>0)){n=1;s=33;break}n=n+1|0;if(((i>>>0)%(l>>>0)|0)>>>0>((s>>>0)%(l>>>0)|0)>>>0){n=1;s=33;break}if(n>>>0>=o>>>0)break e}if((s|0)==33){c=m;return n|0}}else if(!s){s=a[i+20>>2]|0;i=0;while(1){if((a[s+(i<<2)>>2]|0)>>>0>h>>>0){n=1;break}i=i+1|0;if(i>>>0>=o>>>0)break e}c=m;return n|0}else{if((s+-3|0)>>>0<3){if((a[i+36>>2]|0)>>>0>h>>>0)n=1;else break;c=m;return n|0}if((s|0)!=6)break;if((a[i+40>>2]|0)>>>0<h>>>0)n=1;else break;c=m;return n|0}}}while(0);i=e+4|0;s=a[i>>2]|0;do{if((s|0)!=256){n=e+3380|0;if(!(a[n>>2]|0)){if((s|0)==(r|0))break;s=e+8|0;if((d|0)==(a[s>>2]|0)){a[i>>2]=r;a[e+12>>2]=a[b>>2];break}if(!t){b=1;c=m;return b|0}else{a[i>>2]=r;b=a[b>>2]|0;a[e+12>>2]=b;b=a[b+4>>2]|0;a[s>>2]=b;b=a[e+(b<<2)+20>>2]|0;a[e+16>>2]=b;d=a[b+52>>2]|0;b=a[b+56>>2]|0;a[e+1176>>2]=ee(b,d)|0;a[e+1340>>2]=d;a[e+1344>>2]=b;a[n>>2]=1;break}}a[n>>2]=0;n=e+1212|0;un(a[n>>2]|0);a[n>>2]=0;i=e+1172|0;un(a[i>>2]|0);a[i>>2]=0;s=e+1176|0;a[n>>2]=fn((a[s>>2]|0)*216|0)|0;b=fn(a[s>>2]<<2)|0;a[i>>2]=b;i=a[n>>2]|0;if((i|0)==0|(b|0)==0){b=65535;c=m;return b|0}cn(i,0,(a[s>>2]|0)*216|0);i=e+16|0;rt(a[n>>2]|0,a[(a[i>>2]|0)+52>>2]|0,a[s>>2]|0);i=a[i>>2]|0;do{if((a[e+1216>>2]|0)==0?(a[i+16>>2]|0)!=2:0){if(((a[i+80>>2]|0)!=0?(p=a[i+84>>2]|0,(a[p+920>>2]|0)!=0):0)?(a[p+944>>2]|0)==0:0){n=1;break}n=0}else n=1}while(0);b=ee(a[i+56>>2]|0,a[i+52>>2]|0)|0;n=Ge(e+1220|0,b,a[i+88>>2]|0,a[i+44>>2]|0,a[i+12>>2]|0,n)|0;if(n){b=n;c=m;return b|0}}else{a[i>>2]=r;b=a[b>>2]|0;a[e+12>>2]=b;b=a[b+4>>2]|0;a[e+8>>2]=b;b=a[e+(b<<2)+20>>2]|0;a[e+16>>2]=b;d=a[b+52>>2]|0;b=a[b+56>>2]|0;a[e+1176>>2]=ee(b,d)|0;a[e+1340>>2]=d;a[e+1344>>2]=b;a[e+3380>>2]=1}}while(0);b=0;c=m;return b|0}function ht(e){e=e|0;var r=0,t=0,n=0;n=c;a[e+1196>>2]=0;a[e+1192>>2]=0;t=a[e+1176>>2]|0;if(!t){c=n;return}e=a[e+1212>>2]|0;r=0;do{a[e+(r*216|0)+4>>2]=0;a[e+(r*216|0)+196>>2]=0;r=r+1|0}while(r>>>0<t>>>0);c=n;return}function dt(e){e=e|0;return(a[e+1188>>2]|0)==0|0}function bt(e){e=e|0;var r=0,t=0,n=0,i=0;i=c;if(!(a[e+1404>>2]|0)){if((a[e+1196>>2]|0)==(a[e+1176>>2]|0)){e=1;c=i;return e|0}}else{n=a[e+1176>>2]|0;if(!n){e=1;c=i;return e|0}e=a[e+1212>>2]|0;r=0;t=0;do{t=((a[e+(r*216|0)+196>>2]|0)!=0&1)+t|0;r=r+1|0}while(r>>>0<n>>>0);if((t|0)==(n|0)){e=1;c=i;return e|0}}e=0;c=i;return e|0}function pt(e,r){e=e|0;r=r|0;var t=0,n=0;t=c;n=a[e+16>>2]|0;vt(a[e+1172>>2]|0,a[e+12>>2]|0,r,a[n+52>>2]|0,a[n+56>>2]|0);c=t;return}function mt(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0;k=c;c=c+32|0;s=k+24|0;f=k+20|0;u=k+16|0;d=k+12|0;w=k+8|0;m=k;a[n>>2]=0;switch(a[r>>2]|0){case 5:case 1:{v=t+1300|0;o=t+1332|0;if(a[o>>2]|0){a[n>>2]=1;a[o>>2]=0}o=Tr(e,s)|0;if(o){b=o;c=k;return b|0}l=a[t+(a[s>>2]<<2)+148>>2]|0;if(!l){b=65520;c=k;return b|0}o=a[l+4>>2]|0;h=a[t+(o<<2)+20>>2]|0;if(!h){b=65520;c=k;return b|0}s=a[t+8>>2]|0;if(!((s|0)==32|(o|0)==(s|0))?(a[r>>2]|0)!=5:0){b=65520;c=k;return b|0}s=a[t+1304>>2]|0;o=a[r+4>>2]|0;if((s|0)!=(o|0)?(s|0)==0|(o|0)==0:0)a[n>>2]=1;o=(a[r>>2]|0)==5;if((a[v>>2]|0)==5){if(!o)i=16}else if(o)i=16;if((i|0)==16)a[n>>2]=1;s=h+12|0;if(Sr(e,a[s>>2]|0,f)|0){b=1;c=k;return b|0}i=t+1308|0;o=a[f>>2]|0;if((a[i>>2]|0)!=(o|0)){a[i>>2]=o;a[n>>2]=1}if((a[r>>2]|0)==5){if(Ur(e,a[s>>2]|0,5,u)|0){b=1;c=k;return b|0}if((a[v>>2]|0)==5){o=t+1312|0;i=a[o>>2]|0;s=a[u>>2]|0;if((i|0)==(s|0))s=i;else a[n>>2]=1}else{s=a[u>>2]|0;o=t+1312|0}a[o>>2]=s}s=a[h+16>>2]|0;if((s|0)==1){if(!(a[h+24>>2]|0)){o=l+8|0;s=Fr(e,h,a[r>>2]|0,a[o>>2]|0,m)|0;if(s){b=s;c=k;return b|0}i=t+1324|0;s=a[m>>2]|0;if((a[i>>2]|0)!=(s|0)){a[i>>2]=s;a[n>>2]=1}if((a[o>>2]|0)!=0?(p=t+1328|0,b=a[m+4>>2]|0,(a[p>>2]|0)!=(b|0)):0){a[p>>2]=b;a[n>>2]=1}}}else if(!s){if(Pr(e,h,a[r>>2]|0,d)|0){b=1;c=k;return b|0}i=t+1316|0;s=a[d>>2]|0;if((a[i>>2]|0)!=(s|0)){a[i>>2]=s;a[n>>2]=1}if(a[l+8>>2]|0){i=Rr(e,h,a[r>>2]|0,w)|0;if(i){b=i;c=k;return b|0}s=t+1320|0;i=a[w>>2]|0;if((a[s>>2]|0)!=(i|0)){a[s>>2]=i;a[n>>2]=1}}}d=r;e=a[d+4>>2]|0;b=v;a[b>>2]=a[d>>2];a[b+4>>2]=e;b=0;c=k;return b|0}case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:{a[n>>2]=1;b=0;c=k;return b|0}default:{b=0;c=k;return b|0}}return 0}function wt(e){e=e|0;var r=0,t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0;d=c;l=0;e:while(1){r=a[e+(l<<2)+148>>2]|0;r:do{if((r|0)!=0?(u=a[e+(a[r+4>>2]<<2)+20>>2]|0,(u|0)!=0):0){f=a[u+52>>2]|0;h=ee(a[u+56>>2]|0,f)|0;i=a[r+12>>2]|0;if(i>>>0<=1){r=0;t=18;break e}t=a[r+16>>2]|0;if((t|0)==2){o=a[r+24>>2]|0;s=a[r+28>>2]|0;i=i+-1|0;n=0;while(1){r=a[o+(n<<2)>>2]|0;t=a[s+(n<<2)>>2]|0;if(!(r>>>0<=t>>>0&t>>>0<h>>>0))break r;n=n+1|0;if(((r>>>0)%(f>>>0)|0)>>>0>((t>>>0)%(f>>>0)|0)>>>0)break r;if(n>>>0>=i>>>0){r=0;t=18;break e}}}else if(t){if((t+-3|0)>>>0<3)if((a[r+36>>2]|0)>>>0>h>>>0)break;else{r=0;t=18;break e}if((t|0)!=6){r=0;t=18;break e}if((a[r+40>>2]|0)>>>0<h>>>0)break;else{r=0;t=18;break e}}else{t=a[r+20>>2]|0;r=0;while(1){if((a[t+(r<<2)>>2]|0)>>>0>h>>>0)break r;r=r+1|0;if(r>>>0>=i>>>0){r=0;t=18;break e}}}}}while(0);l=l+1|0;if(l>>>0>=256){r=1;t=18;break}}if((t|0)==18){c=d;return r|0}return 0}function vt(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0;y=c;k=ee(i,n)|0;b=a[r+12>>2]|0;if((b|0)==1){cn(e,0,k<<2);c=y;return}u=a[r+16>>2]|0;if((u+-3|0)>>>0<3){t=ee(a[r+36>>2]|0,t)|0;t=t>>>0<k>>>0?t:k;if((u&-2|0)==4){d=(a[r+32>>2]|0)==0?t:k-t|0;g=t}else{d=0;g=t}}else{d=0;g=0}switch(u|0){case 0:{l=a[r+20>>2]|0;if(!k){c=y;return}else{o=0;f=0}while(1){while(1)if(o>>>0<b>>>0)break;else o=0;r=l+(o<<2)|0;t=a[r>>2]|0;e:do{if(!t)t=0;else{u=0;do{s=u+f|0;if(s>>>0>=k>>>0)break e;a[e+(s<<2)>>2]=o;u=u+1|0;t=a[r>>2]|0}while(u>>>0<t>>>0)}}while(0);f=t+f|0;if(f>>>0>=k>>>0)break;else o=o+1|0}c=y;return}case 4:{o=a[r+32>>2]|0;if(!k){c=y;return}t=1-o|0;s=0;do{a[e+(s<<2)>>2]=s>>>0<d>>>0?o:t;s=s+1|0}while((s|0)!=(k|0));c=y;return}case 1:{if(!k){c=y;return}else o=0;do{a[e+(o<<2)>>2]=((((ee((o>>>0)/(n>>>0)|0,b)|0)>>>1)+((o>>>0)%(n>>>0)|0)|0)>>>0)%(b>>>0)|0;o=o+1|0}while((o|0)!=(k|0));c=y;return}case 2:{d=a[r+24>>2]|0;h=a[r+28>>2]|0;o=b+-1|0;if(k){t=0;do{a[e+(t<<2)>>2]=o;t=t+1|0}while((t|0)!=(k|0))}if(!o){c=y;return}s=b+-2|0;while(1){f=a[d+(s<<2)>>2]|0;t=(f>>>0)/(n>>>0)|0;f=(f>>>0)%(n>>>0)|0;o=a[h+(s<<2)>>2]|0;l=(o>>>0)/(n>>>0)|0;o=(o>>>0)%(n>>>0)|0;e:do{if(t>>>0<=l>>>0){if(f>>>0>o>>>0)while(1){t=t+1|0;if(t>>>0>l>>>0)break e}do{u=ee(t,n)|0;r=f;do{a[e+(r+u<<2)>>2]=s;r=r+1|0}while(r>>>0<=o>>>0);t=t+1|0}while(t>>>0<=l>>>0)}}while(0);if(!s)break;else s=s+-1|0}c=y;return}case 5:{t=a[r+32>>2]|0;if(!n){c=y;return}u=1-t|0;if(!i){c=y;return}else{s=0;f=0}while(1){o=0;r=f;while(1){h=e+((ee(o,n)|0)+s<<2)|0;a[h>>2]=r>>>0<d>>>0?t:u;o=o+1|0;if((o|0)==(i|0))break;else r=r+1|0}s=s+1|0;if((s|0)==(n|0))break;else f=f+i|0}c=y;return}case 3:{h=a[r+32>>2]|0;if(k){t=0;do{a[e+(t<<2)>>2]=1;t=t+1|0}while((t|0)!=(k|0))}l=(n-h|0)>>>1;d=(i-h|0)>>>1;if(!g){c=y;return}k=h<<1;w=k+-1|0;v=n+-1|0;k=1-k|0;m=i+-1|0;b=d;p=0;s=l;i=l;u=d;r=l;f=h+-1|0;t=d;while(1){d=e+((ee(t,n)|0)+r<<2)|0;l=(a[d>>2]|0)==1;o=l&1;if(l)a[d>>2]=0;do{if(!((f|0)==-1&(r|0)==(s|0))){if((f|0)==1&(r|0)==(i|0)){r=i+1|0;r=(r|0)<(v|0)?r:v;d=b;l=s;i=r;f=0;h=k;break}if((h|0)==-1&(t|0)==(u|0)){t=u+-1|0;t=(t|0)>0?t:0;d=b;l=s;u=t;f=k;h=0;break}if((h|0)==1&(t|0)==(b|0)){t=b+1|0;t=(t|0)<(m|0)?t:m;d=t;l=s;f=w;h=0;break}else{d=b;l=s;r=r+f|0;t=t+h|0;break}}else{r=s+-1|0;r=(r|0)>0?r:0;d=b;l=r;f=0;h=w}}while(0);p=o+p|0;if(p>>>0>=g>>>0)break;else{b=d;s=l}}c=y;return}default:{if(!k){c=y;return}s=a[r+44>>2]|0;o=0;do{a[e+(o<<2)>>2]=a[s+(o<<2)>>2];o=o+1|0}while((o|0)!=(k|0));c=y;return}}}function kt(){return 3472}function gt(e,r,t,n,i,s){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;s=s|0;var o=0,f=0,u=0;u=c;c=c+80|0;o=u+32|0;f=u;yt(t,o,f,n);if((Or(a[e>>2]|0)|0)==1){n=Bt(e,s,r+328|0,o,f,i)|0;if(n){c=u;return n|0}}else{n=_t(e,s,r,o,f,i)|0;if(n){c=u;return n|0}}n=At(e,s+256|0,r+1352|0,o+21|0,f+16|0,a[r+140>>2]|0,i)|0;if(n){c=u;return n|0}if((a[e+196>>2]|0)>>>0>1){n=0;c=u;return n|0}er(t,s);n=0;c=u;return n|0}function yt(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0;v=c;if(!i){c=v;return}p=a[e+4>>2]|0;m=ee(a[e+8>>2]|0,p)|0;d=(i>>>0)/(p>>>0)|0;s=ee(d,p)|0;b=i-s|0;u=p<<4;o=a[e>>2]|0;f=(b<<4)+(ee(p<<8,d)|0)|0;w=(d|0)!=0;if(w){h=f-(u|1)|0;n[r>>0]=n[o+h>>0]|0;n[r+1>>0]=n[o+(h+1)>>0]|0;n[r+2>>0]=n[o+(h+2)>>0]|0;n[r+3>>0]=n[o+(h+3)>>0]|0;n[r+4>>0]=n[o+(h+4)>>0]|0;n[r+5>>0]=n[o+(h+5)>>0]|0;n[r+6>>0]=n[o+(h+6)>>0]|0;n[r+7>>0]=n[o+(h+7)>>0]|0;n[r+8>>0]=n[o+(h+8)>>0]|0;n[r+9>>0]=n[o+(h+9)>>0]|0;n[r+10>>0]=n[o+(h+10)>>0]|0;n[r+11>>0]=n[o+(h+11)>>0]|0;n[r+12>>0]=n[o+(h+12)>>0]|0;n[r+13>>0]=n[o+(h+13)>>0]|0;n[r+14>>0]=n[o+(h+14)>>0]|0;n[r+15>>0]=n[o+(h+15)>>0]|0;n[r+16>>0]=n[o+(h+16)>>0]|0;n[r+17>>0]=n[o+(h+17)>>0]|0;n[r+18>>0]=n[o+(h+18)>>0]|0;n[r+19>>0]=n[o+(h+19)>>0]|0;n[r+20>>0]=n[o+(h+20)>>0]|0;h=r+21|0}else h=r;l=(s|0)!=(i|0);if(l){f=f+-1|0;n[t>>0]=n[o+f>>0]|0;f=f+u|0;n[t+1>>0]=n[o+f>>0]|0;f=f+u|0;n[t+2>>0]=n[o+f>>0]|0;f=f+u|0;n[t+3>>0]=n[o+f>>0]|0;f=f+u|0;n[t+4>>0]=n[o+f>>0]|0;f=f+u|0;n[t+5>>0]=n[o+f>>0]|0;f=f+u|0;n[t+6>>0]=n[o+f>>0]|0;f=f+u|0;n[t+7>>0]=n[o+f>>0]|0;f=f+u|0;n[t+8>>0]=n[o+f>>0]|0;f=f+u|0;n[t+9>>0]=n[o+f>>0]|0;f=f+u|0;n[t+10>>0]=n[o+f>>0]|0;f=f+u|0;n[t+11>>0]=n[o+f>>0]|0;f=f+u|0;n[t+12>>0]=n[o+f>>0]|0;f=f+u|0;n[t+13>>0]=n[o+f>>0]|0;f=f+u|0;n[t+14>>0]=n[o+f>>0]|0;n[t+15>>0]=n[o+(f+u)>>0]|0;t=t+16|0}r=p<<3&2147483640;i=a[e>>2]|0;s=(ee(d<<3,r)|0)+(m<<8)+(b<<3)|0;if(w){e=s-(r|1)|0;n[h>>0]=n[i+e>>0]|0;n[h+1>>0]=n[i+(e+1)>>0]|0;n[h+2>>0]=n[i+(e+2)>>0]|0;n[h+3>>0]=n[i+(e+3)>>0]|0;n[h+4>>0]=n[i+(e+4)>>0]|0;n[h+5>>0]=n[i+(e+5)>>0]|0;n[h+6>>0]=n[i+(e+6)>>0]|0;n[h+7>>0]=n[i+(e+7)>>0]|0;n[h+8>>0]=n[i+(e+8)>>0]|0;e=e+(m<<6)|0;n[h+9>>0]=n[i+e>>0]|0;n[h+10>>0]=n[i+(e+1)>>0]|0;n[h+11>>0]=n[i+(e+2)>>0]|0;n[h+12>>0]=n[i+(e+3)>>0]|0;n[h+13>>0]=n[i+(e+4)>>0]|0;n[h+14>>0]=n[i+(e+5)>>0]|0;n[h+15>>0]=n[i+(e+6)>>0]|0;n[h+16>>0]=n[i+(e+7)>>0]|0;n[h+17>>0]=n[i+(e+8)>>0]|0}if(!l){c=v;return}h=s+-1|0;n[t>>0]=n[i+h>>0]|0;h=h+r|0;n[t+1>>0]=n[i+h>>0]|0;h=h+r|0;n[t+2>>0]=n[i+h>>0]|0;h=h+r|0;n[t+3>>0]=n[i+h>>0]|0;h=h+r|0;n[t+4>>0]=n[i+h>>0]|0;h=h+r|0;n[t+5>>0]=n[i+h>>0]|0;h=h+r|0;n[t+6>>0]=n[i+h>>0]|0;h=h+r|0;n[t+7>>0]=n[i+h>>0]|0;h=h+(r+((m<<6)-(p<<6)))|0;n[t+8>>0]=n[i+h>>0]|0;h=h+r|0;n[t+9>>0]=n[i+h>>0]|0;h=h+r|0;n[t+10>>0]=n[i+h>>0]|0;h=h+r|0;n[t+11>>0]=n[i+h>>0]|0;h=h+r|0;n[t+12>>0]=n[i+h>>0]|0;h=h+r|0;n[t+13>>0]=n[i+h>>0]|0;h=h+r|0;n[t+14>>0]=n[i+h>>0]|0;n[t+15>>0]=n[i+(h+r)>>0]|0;c=v;return}function Bt(e,r,t,i,o,f){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;var u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0;M=c;u=e+200|0;l=ot(e,a[u>>2]|0)|0;b=(f|0)!=0;if((l|0)!=0&b){d=(Or(a[a[u>>2]>>2]|0)|0)==2;d=d?0:l}else d=l;f=e+204|0;l=ot(e,a[f>>2]|0)|0;if((l|0)!=0&b){p=(Or(a[a[f>>2]>>2]|0)|0)==2;p=p?0:l}else p=l;f=e+212|0;l=ot(e,a[f>>2]|0)|0;if((l|0)!=0&b){w=(Or(a[a[f>>2]>>2]|0)|0)==2;l=w?0:l}f=Vr(a[e>>2]|0)|0;if(!f){if(!p){w=1;c=M;return w|0}e=i+1|0;h=i+2|0;v=i+3|0;k=i+4|0;g=i+5|0;y=i+6|0;B=i+7|0;_=i+8|0;A=i+9|0;o=i+10|0;l=i+11|0;f=i+12|0;u=i+13|0;m=i+14|0;w=i+15|0;p=i+16|0;b=r;d=0;while(1){n[b>>0]=n[e>>0]|0;n[b+1>>0]=n[h>>0]|0;n[b+2>>0]=n[v>>0]|0;n[b+3>>0]=n[k>>0]|0;n[b+4>>0]=n[g>>0]|0;n[b+5>>0]=n[y>>0]|0;n[b+6>>0]=n[B>>0]|0;n[b+7>>0]=n[_>>0]|0;n[b+8>>0]=n[A>>0]|0;n[b+9>>0]=n[o>>0]|0;n[b+10>>0]=n[l>>0]|0;n[b+11>>0]=n[f>>0]|0;n[b+12>>0]=n[u>>0]|0;n[b+13>>0]=n[m>>0]|0;n[b+14>>0]=n[w>>0]|0;n[b+15>>0]=n[p>>0]|0;d=d+1|0;if((d|0)==16)break;else b=b+16|0}}else if((f|0)==2){l=i+1|0;u=(d|0)!=0;f=(p|0)!=0;do{if(!(u&f)){if(u){u=((s[o>>0]|0)+8+(s[o+1>>0]|0)+(s[o+2>>0]|0)+(s[o+3>>0]|0)+(s[o+4>>0]|0)+(s[o+5>>0]|0)+(s[o+6>>0]|0)+(s[o+7>>0]|0)+(s[o+8>>0]|0)+(s[o+9>>0]|0)+(s[o+10>>0]|0)+(s[o+11>>0]|0)+(s[o+12>>0]|0)+(s[o+13>>0]|0)+(s[o+14>>0]|0)+(s[o+15>>0]|0)|0)>>>4;break}if(f)u=((s[l>>0]|0)+8+(s[i+2>>0]|0)+(s[i+3>>0]|0)+(s[i+4>>0]|0)+(s[i+5>>0]|0)+(s[i+6>>0]|0)+(s[i+7>>0]|0)+(s[i+8>>0]|0)+(s[i+9>>0]|0)+(s[i+10>>0]|0)+(s[i+11>>0]|0)+(s[i+12>>0]|0)+(s[i+13>>0]|0)+(s[i+14>>0]|0)+(s[i+15>>0]|0)+(s[i+16>>0]|0)|0)>>>4;else u=128}else{f=0;u=0;do{w=f;f=f+1|0;u=(s[i+f>>0]|0)+u+(s[o+w>>0]|0)|0}while((f|0)!=16);u=(u+16|0)>>>5}}while(0);mn(r|0,u&255|0,256)|0}else if((f|0)==1){if(!d){w=1;c=M;return w|0}else{u=r;f=0;while(1){w=o+f|0;n[u>>0]=n[w>>0]|0;n[u+1>>0]=n[w>>0]|0;n[u+2>>0]=n[w>>0]|0;n[u+3>>0]=n[w>>0]|0;n[u+4>>0]=n[w>>0]|0;n[u+5>>0]=n[w>>0]|0;n[u+6>>0]=n[w>>0]|0;n[u+7>>0]=n[w>>0]|0;n[u+8>>0]=n[w>>0]|0;n[u+9>>0]=n[w>>0]|0;n[u+10>>0]=n[w>>0]|0;n[u+11>>0]=n[w>>0]|0;n[u+12>>0]=n[w>>0]|0;n[u+13>>0]=n[w>>0]|0;n[u+14>>0]=n[w>>0]|0;n[u+15>>0]=n[w>>0]|0;f=f+1|0;if((f|0)==16)break;else u=u+16|0}}}else{if(!((d|0)!=0&(p|0)!=0&(l|0)!=0)){w=1;c=M;return w|0}f=s[i+16>>0]|0;h=s[o+15>>0]|0;b=s[i>>0]|0;p=(((s[i+9>>0]|0)-(s[i+7>>0]|0)+((s[i+10>>0]|0)-(s[i+6>>0]|0)<<1)+(((s[i+11>>0]|0)-(s[i+5>>0]|0)|0)*3|0)+((s[i+12>>0]|0)-(s[i+4>>0]|0)<<2)+(((s[i+13>>0]|0)-(s[i+3>>0]|0)|0)*5|0)+(((s[i+14>>0]|0)-(s[i+2>>0]|0)|0)*6|0)+(((s[i+15>>0]|0)-(s[i+1>>0]|0)|0)*7|0)+(f-b<<3)|0)*5|0)+32>>6;b=(((s[o+8>>0]|0)-(s[o+6>>0]|0)+(h-b<<3)+((s[o+9>>0]|0)-(s[o+5>>0]|0)<<1)+(((s[o+10>>0]|0)-(s[o+4>>0]|0)|0)*3|0)+((s[o+11>>0]|0)-(s[o+3>>0]|0)<<2)+(((s[o+12>>0]|0)-(s[o+2>>0]|0)|0)*5|0)+(((s[o+13>>0]|0)-(s[o+1>>0]|0)|0)*6|0)+(((s[o+14>>0]|0)-(s[o>>0]|0)|0)*7|0)|0)*5|0)+32>>6;f=(h+f<<4)+16|0;h=0;do{u=f+(ee(h+-7|0,b)|0)|0;d=h<<4;e=0;do{l=u+(ee(e+-7|0,p)|0)>>5;if((l|0)<0)l=0;else l=(l|0)>255?-1:l&255;n[r+(e+d)>>0]=l;e=e+1|0}while((e|0)!=16);h=h+1|0}while((h|0)!=16)}Mt(r,t,0);Mt(r,t+64|0,1);Mt(r,t+128|0,2);Mt(r,t+192|0,3);Mt(r,t+256|0,4);Mt(r,t+320|0,5);Mt(r,t+384|0,6);Mt(r,t+448|0,7);Mt(r,t+512|0,8);Mt(r,t+576|0,9);Mt(r,t+640|0,10);Mt(r,t+704|0,11);Mt(r,t+768|0,12);Mt(r,t+832|0,13);Mt(r,t+896|0,14);Mt(r,t+960|0,15);w=0;c=M;return w|0}function _t(e,r,t,i,o,f){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;var u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0,N=0;N=c;O=(f|0)!=0;I=0;e:while(1){l=nt(I)|0;d=a[l+4>>2]|0;l=tt(e,a[l>>2]|0)|0;f=ot(e,l)|0;if((f|0)!=0&O){U=(Or(a[l>>2]|0)|0)==2;f=U?0:f}b=it(I)|0;h=a[b+4>>2]|0;b=tt(e,a[b>>2]|0)|0;u=ot(e,b)|0;if((u|0)!=0&O){U=(Or(a[b>>2]|0)|0)==2;u=U?0:u}R=(f|0)!=0;F=(u|0)!=0;C=R&F;if(C){if(!(Or(a[l>>2]|0)|0))d=s[l+(d&255)+82>>0]|0;else d=2;if(!(Or(a[b>>2]|0)|0))f=s[b+(h&255)+82>>0]|0;else f=2;f=d>>>0<f>>>0?d:f}else f=2;if(!(a[t+(I<<2)+12>>2]|0)){U=a[t+(I<<2)+76>>2]|0;f=(U>>>0>=f>>>0&1)+U|0}n[e+I+82>>0]=f;l=a[(at(I)|0)>>2]|0;l=tt(e,l)|0;h=ot(e,l)|0;if((h|0)!=0&O){U=(Or(a[l>>2]|0)|0)==2;h=U?0:h}l=a[(st(I)|0)>>2]|0;l=tt(e,l)|0;d=ot(e,l)|0;if((d|0)!=0&O){U=(Or(a[l>>2]|0)|0)==2;d=U?0:d}L=a[3344+(I<<2)>>2]|0;D=a[3408+(I<<2)>>2]|0;w=(1285>>>I&1|0)!=0;if(w){b=o+D|0;l=o+(D+1)|0;p=o+(D+2)|0;m=o+(D+3)|0}else{m=(D<<4)+L|0;b=r+(m+-1)|0;l=r+(m+15)|0;p=r+(m+31)|0;m=r+(m+47)|0}x=n[b>>0]|0;A=n[l>>0]|0;P=n[p>>0]|0;U=n[m>>0]|0;do{if(!(51>>>I&1)){_=D+-1|0;B=(_<<4)+L|0;b=n[r+B>>0]|0;m=n[r+(B+1)>>0]|0;v=n[r+(B+2)>>0]|0;g=n[r+(B+3)>>0]|0;k=n[r+(B+4)>>0]|0;l=n[r+(B+5)>>0]|0;y=n[r+(B+6)>>0]|0;p=n[r+(B+7)>>0]|0;if(w){S=y;T=g;g=p;E=m;M=v;y=o+_|0;break}else{S=y;T=g;g=p;E=m;M=v;y=r+(B+-1)|0;break}}else{S=n[i+(L+7)>>0]|0;T=n[i+(L+4)>>0]|0;k=n[i+(L+5)>>0]|0;l=n[i+(L+6)>>0]|0;b=n[i+(L+1)>>0]|0;g=n[i+(L+8)>>0]|0;E=n[i+(L+2)>>0]|0;M=n[i+(L+3)>>0]|0;y=i+L|0}}while(0);y=n[y>>0]|0;switch(f|0){case 4:{if(!(C&(d|0)!=0)){u=1;f=51;break e}f=b&255;b=y&255;m=x&255;v=f+2|0;S=(v+m+(b<<1)|0)>>>2;g=S&255;u=E&255;b=b+2|0;y=((f<<1)+u+b|0)>>>2&255;f=M&255;v=((u<<1)+f+v|0)>>>2&255;M=A&255;b=(M+(m<<1)+b|0)>>>2;k=b&255;x=P&255;E=(m+2+(M<<1)+x|0)>>>2;m=g;p=k;d=E&255;h=y;l=v;f=((T&255)+2+u+(f<<1)|0)>>>2&255;u=g;w=y;b=(M+2+(x<<1)+(U&255)|0)>>>2&255|E<<8&65280|S<<24|b<<16&16711680;break}case 6:{if(!(C&(d|0)!=0)){u=1;f=51;break e}l=y&255;v=x&255;g=v+1|0;w=(g+l|0)>>>1&255;T=A&255;y=((v<<1)+2+T+l|0)>>>2&255;g=(g+T|0)>>>1&255;S=P&255;v=v+2|0;A=(v+(T<<1)+S|0)>>>2;x=(T+1+S|0)>>>1;U=U&255;f=b&255;v=(v+f+(l<<1)|0)>>>2&255;u=E&255;m=w;p=g;d=x&255;h=v;l=(u+2+(f<<1)+l|0)>>>2&255;f=((M&255)+2+(u<<1)+f|0)>>>2&255;u=y;k=A&255;b=A<<24|x<<16&16711680|(S+1+U|0)>>>1&255|T+2+(S<<1)+U<<6&65280;break}case 2:{do{if(!C){if(R){f=((x&255)+2+(A&255)+(P&255)+(U&255)|0)>>>2;break}if(F)f=((T&255)+2+(M&255)+(E&255)+(b&255)|0)>>>2;else f=128}else f=((x&255)+4+(A&255)+(P&255)+(U&255)+(T&255)+(M&255)+(E&255)+(b&255)|0)>>>3}while(0);b=ee(f&255,16843009)|0;d=b&255;k=b>>>8&255;g=b>>>16&255;y=b>>>24&255;m=d;p=d;h=k;l=g;f=y;u=k;w=g;v=y;break}case 0:{if(!u){u=1;f=51;break e}m=b;p=b;d=b;h=E;l=M;f=T;u=E;w=M;v=T;k=E;g=M;y=T;b=(M&255)<<16|(T&255)<<24|(E&255)<<8|b&255;break}case 1:{if(!R){u=1;f=51;break e}f=ee(x&255,16843009)|0;v=ee(A&255,16843009)|0;y=ee(P&255,16843009)|0;m=f&255;p=v&255;d=y&255;h=f>>>8&255;l=f>>>16&255;f=f>>>24&255;u=v>>>8&255;w=v>>>16&255;v=v>>>24&255;k=y>>>8&255;g=y>>>16&255;y=y>>>24&255;b=ee(U&255,16843009)|0;break}case 7:{if(!u){u=1;f=51;break e}x=(h|0)==0;d=b&255;p=E&255;M=M&255;h=(M+1+p|0)>>>1&255;b=T&255;g=b+1|0;y=(g+M|0)>>>1&255;E=(x?T:k)&255;g=(g+E|0)>>>1&255;f=M+2|0;A=b+2|0;M=(A+p+(M<<1)|0)>>>2;b=(f+(b<<1)+E|0)>>>2;U=(x?T:l)&255;A=(A+U+(E<<1)|0)>>>2;m=(p+1+d|0)>>>1&255;p=(f+d+(p<<1)|0)>>>2&255;d=h;l=y;f=g;u=M&255;w=b&255;v=A&255;k=y;y=(E+1+U|0)>>>1&255;b=A<<16&16711680|M&255|(E+2+((x?T:S)&255)+(U<<1)|0)>>>2<<24|b<<8&65280;break}case 3:{if(!u){u=1;f=51;break e}d=(h|0)==0;m=E&255;h=M&255;p=h+2|0;u=T&255;E=u+2|0;h=(E+m+(h<<1)|0)>>>2&255;y=(d?T:k)&255;u=(p+(u<<1)+y|0)>>>2&255;U=(d?T:l)&255;E=(E+U+(y<<1)|0)>>>2;k=E&255;x=(d?T:S)&255;S=(y+2+x+(U<<1)|0)>>>2;y=S&255;T=(d?T:g)&255;U=(U+2+T+(x<<1)|0)>>>2;m=(p+(b&255)+(m<<1)|0)>>>2&255;p=h;d=u;l=u;f=k;w=k;v=y;g=y;y=U&255;b=(x+2+(T*3|0)|0)>>>2<<24|E&255|S<<8&65280|U<<16&16711680;break}case 5:{if(!(C&(d|0)!=0)){u=1;f=51;break e}d=y&255;g=b&255;k=(g+1+d|0)>>>1&255;_=E&255;U=(_+2+(g<<1)+d|0)>>>2;E=x&255;x=g+2|0;b=(x+E+(d<<1)|0)>>>2;g=(_+1+g|0)>>>1&255;v=M&255;x=((_<<1)+v+x|0)>>>2;y=(v+1+_|0)>>>1&255;T=T&255;S=A&255;m=k;p=b&255;d=(S+2+(E<<1)+d|0)>>>2&255;h=g;l=y;f=(T+1+v|0)>>>1&255;u=U&255;w=x&255;v=(T+2+_+(v<<1)|0)>>>2&255;b=x<<24|(E+2+(P&255)+(S<<1)|0)>>>2&255|U<<16&16711680|b<<8&65280;break}default:{if(!R){u=1;f=51;break e}v=x&255;f=A&255;h=P&255;l=(f+1+h|0)>>>1&255;b=U&255;u=(f+2+(h<<1)+b|0)>>>2&255;w=(h+1+b|0)>>>1&255;k=(h+2+(b*3|0)|0)>>>2&255;m=(v+1+f|0)>>>1&255;p=l;d=w;h=(v+2+(f<<1)+h|0)>>>2&255;f=u;v=k;g=U;y=U;b=b<<8|b|b<<16|b<<24}}U=(D<<4)+L|0;a[r+U>>2]=(l&255)<<16|(f&255)<<24|(h&255)<<8|m&255;a[r+(U+16)>>2]=(w&255)<<16|(v&255)<<24|(u&255)<<8|p&255;a[r+(U+32)>>2]=(g&255)<<16|(y&255)<<24|(k&255)<<8|d&255;a[r+(U+48)>>2]=b;Mt(r,t+(I<<6)+328|0,I);I=I+1|0;if(I>>>0>=16){u=0;f=51;break}}if((f|0)==51){c=N;return u|0}return 0}function At(e,r,t,i,o,f,u){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;u=u|0;var l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0;A=c;h=e+200|0;l=ot(e,a[h>>2]|0)|0;d=(u|0)!=0;if((l|0)!=0&d){b=(Or(a[a[h>>2]>>2]|0)|0)==2;l=b?0:l}h=e+204|0;u=ot(e,a[h>>2]|0)|0;if((u|0)!=0&d){b=(Or(a[a[h>>2]>>2]|0)|0)==2;b=b?0:u}else b=u;h=e+212|0;u=ot(e,a[h>>2]|0)|0;if((u|0)!=0&d){d=(Or(a[a[h>>2]>>2]|0)|0)==2;u=d?0:u}B=(l|0)!=0;_=(b|0)!=0;y=B&_;g=y&(u|0)!=0;k=(l|0)==0;v=(b|0)==0;p=i;m=16;w=0;while(1){if((f|0)==1){if(k){l=1;u=29;break}else{d=r;e=8;h=o}while(1){e=e+-1|0;n[d>>0]=n[h>>0]|0;n[d+1>>0]=n[h>>0]|0;n[d+2>>0]=n[h>>0]|0;n[d+3>>0]=n[h>>0]|0;n[d+4>>0]=n[h>>0]|0;n[d+5>>0]=n[h>>0]|0;n[d+6>>0]=n[h>>0]|0;n[d+7>>0]=n[h>>0]|0;if(!e)break;else{d=d+8|0;h=h+1|0}}}else if((f|0)==2){if(v){l=1;u=29;break}else{d=p;e=r;h=8}while(1){d=d+1|0;h=h+-1|0;n[e>>0]=n[d>>0]|0;n[e+8>>0]=n[d>>0]|0;n[e+16>>0]=n[d>>0]|0;n[e+24>>0]=n[d>>0]|0;n[e+32>>0]=n[d>>0]|0;n[e+40>>0]=n[d>>0]|0;n[e+48>>0]=n[d>>0]|0;n[e+56>>0]=n[d>>0]|0;if(!h)break;else e=e+1|0}}else if(!f){h=p+1|0;do{if(!y){if(_){d=((s[h>>0]|0)+2+(s[p+2>>0]|0)+(s[p+3>>0]|0)+(s[p+4>>0]|0)|0)>>>2;e=((s[p+5>>0]|0)+2+(s[p+6>>0]|0)+(s[p+7>>0]|0)+(s[p+8>>0]|0)|0)>>>2;break}if(B){e=((s[o>>0]|0)+2+(s[o+1>>0]|0)+(s[o+2>>0]|0)+(s[o+3>>0]|0)|0)>>>2;d=e}else{d=128;e=128}}else{d=((s[h>>0]|0)+4+(s[p+2>>0]|0)+(s[p+3>>0]|0)+(s[p+4>>0]|0)+(s[o>>0]|0)+(s[o+1>>0]|0)+(s[o+2>>0]|0)+(s[o+3>>0]|0)|0)>>>3;e=((s[p+5>>0]|0)+2+(s[p+6>>0]|0)+(s[p+7>>0]|0)+(s[p+8>>0]|0)|0)>>>2}}while(0);d=d&255;b=e&255;mn(r|0,d|0,4)|0;mn(r+4|0,b|0,4)|0;mn(r+8|0,d|0,4)|0;mn(r+12|0,b|0,4)|0;mn(r+16|0,d|0,4)|0;mn(r+20|0,b|0,4)|0;i=r+32|0;mn(r+24|0,d|0,4)|0;mn(r+28|0,b|0,4)|0;if(B){b=s[o+4>>0]|0;d=s[o+5>>0]|0;e=s[o+6>>0]|0;h=s[o+7>>0]|0;u=(b+2+d+e+h|0)>>>2;if(_){l=u;d=(b+4+d+e+h+(s[p+5>>0]|0)+(s[p+6>>0]|0)+(s[p+7>>0]|0)+(s[p+8>>0]|0)|0)>>>3}else{l=u;d=u}}else if(_){l=((s[h>>0]|0)+2+(s[p+2>>0]|0)+(s[p+3>>0]|0)+(s[p+4>>0]|0)|0)>>>2;d=((s[p+5>>0]|0)+2+(s[p+6>>0]|0)+(s[p+7>>0]|0)+(s[p+8>>0]|0)|0)>>>2}else{l=128;d=128}e=l&255;b=d&255;mn(i|0,e|0,4)|0;mn(r+36|0,b|0,4)|0;mn(r+40|0,e|0,4)|0;mn(r+44|0,b|0,4)|0;mn(r+48|0,e|0,4)|0;mn(r+52|0,b|0,4)|0;mn(r+56|0,e|0,4)|0;mn(r+60|0,b|0,4)|0}else{if(!g){l=1;u=29;break}d=s[p+8>>0]|0;e=s[o+7>>0]|0;l=s[p>>0]|0;u=(((s[p+5>>0]|0)-(s[p+3>>0]|0)+((s[p+6>>0]|0)-(s[p+2>>0]|0)<<1)+(((s[p+7>>0]|0)-(s[p+1>>0]|0)|0)*3|0)+(d-l<<2)|0)*17|0)+16>>5;l=(((s[o+4>>0]|0)-(s[o+2>>0]|0)+(e-l<<2)+((s[o+5>>0]|0)-(s[o+1>>0]|0)<<1)+(((s[o+6>>0]|0)-(s[o>>0]|0)|0)*3|0)|0)*17|0)+16>>5;b=ee(u,-3)|0;d=(e+d<<4)+16+(ee(l,-3)|0)|0;e=r;h=8;while(1){h=h+-1|0;i=d+b|0;n[e>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+1>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+2>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+3>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+4>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+5>>0]=n[(i>>5)+3984>>0]|0;i=i+u|0;n[e+6>>0]=n[(i>>5)+3984>>0]|0;n[e+7>>0]=n[(i+u>>5)+3984>>0]|0;if(!h)break;else{d=d+l|0;e=e+8|0}}}Mt(r,t,m);i=m|1;Mt(r,t+64|0,i);Mt(r,t+128|0,i+1|0);Mt(r,t+192|0,m|3);w=w+1|0;if(w>>>0>=2){l=0;u=29;break}else{p=p+9|0;m=m+4|0;r=r+64|0;o=o+8|0;t=t+256|0}}if((u|0)==29){c=A;return l|0}return 0}function Mt(e,r,t){e=e|0;r=r|0;t=t|0;var i=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0;o=c;i=a[r>>2]|0;if((i|0)==16777215){c=o;return}h=t>>>0<16;l=h?16:8;h=h?t:t&3;h=(ee(a[3408+(h<<2)>>2]|0,l)|0)+(a[3344+(h<<2)>>2]|0)|0;d=e+h|0;p=a[r+4>>2]|0;f=e+(h+1)|0;t=s[f>>0]|0;n[d>>0]=n[3472+(i+512+(s[d>>0]|0))>>0]|0;d=a[r+8>>2]|0;u=e+(h+2)|0;b=s[u>>0]|0;n[f>>0]=n[3472+(p+512+t)>>0]|0;i=e+(h+3)|0;f=n[3472+((a[r+12>>2]|0)+512+(s[i>>0]|0))>>0]|0;n[u>>0]=n[3472+(d+512+b)>>0]|0;n[i>>0]=f;i=h+l|0;h=e+i|0;f=a[r+20>>2]|0;u=e+(i+1)|0;b=s[u>>0]|0;n[h>>0]=n[3472+((a[r+16>>2]|0)+512+(s[h>>0]|0))>>0]|0;h=a[r+24>>2]|0;d=e+(i+2)|0;t=s[d>>0]|0;n[u>>0]=n[3472+(f+512+b)>>0]|0;u=e+(i+3)|0;b=n[3472+((a[r+28>>2]|0)+512+(s[u>>0]|0))>>0]|0;n[d>>0]=n[3472+(h+512+t)>>0]|0;n[u>>0]=b;i=i+l|0;u=e+i|0;b=a[r+36>>2]|0;d=e+(i+1)|0;t=s[d>>0]|0;n[u>>0]=n[3472+((a[r+32>>2]|0)+512+(s[u>>0]|0))>>0]|0;u=a[r+40>>2]|0;h=e+(i+2)|0;f=s[h>>0]|0;n[d>>0]=n[3472+(b+512+t)>>0]|0;d=e+(i+3)|0;t=n[3472+((a[r+44>>2]|0)+512+(s[d>>0]|0))>>0]|0;n[h>>0]=n[3472+(u+512+f)>>0]|0;n[d>>0]=t;i=i+l|0;l=e+i|0;d=a[r+52>>2]|0;t=e+(i+1)|0;h=s[t>>0]|0;n[l>>0]=n[3472+((a[r+48>>2]|0)+512+(s[l>>0]|0))>>0]|0;l=a[r+56>>2]|0;f=e+(i+2)|0;u=s[f>>0]|0;n[t>>0]=n[3472+(d+512+h)>>0]|0;i=e+(i+3)|0;t=n[3472+((a[r+60>>2]|0)+512+(s[i>>0]|0))>>0]|0;n[f>>0]=n[3472+(l+512+u)>>0]|0;n[i>>0]=t;c=o;return}function xt(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0;m=c;l=e+-1|0;u=n[e+1>>0]|0;h=s[l>>0]|0;d=s[e>>0]|0;w=h-d|0;p=t+4|0;do{if((((w|0)>-1?w:0-w|0)>>>0<(a[p>>2]|0)>>>0?(b=s[e+-2>>0]|0,w=b-h|0,f=a[t+8>>2]|0,((w|0)>-1?w:0-w|0)>>>0<f>>>0):0)?(o=u&255,u=o-d|0,((u|0)>-1?u:0-u|0)>>>0<f>>>0):0)if(r>>>0<4){u=s[(a[t>>2]|0)+(r+-1)>>0]|0;u=Br(~u,u+1|0,4-o+(d-h<<2)+b>>3)|0;b=n[3472+((d|512)-u)>>0]|0;n[l>>0]=n[3472+((h|512)+u)>>0]|0;n[e>>0]=b;break}else{n[l>>0]=(h+2+o+(b<<1)|0)>>>2;n[e>>0]=(d+2+(o<<1)+b|0)>>>2;break}}while(0);l=e+i|0;h=e+(i+-1)|0;b=s[h>>0]|0;d=s[l>>0]|0;u=b-d|0;if(((u|0)>-1?u:0-u|0)>>>0>=(a[p>>2]|0)>>>0){c=m;return}u=s[e+(i+-2)>>0]|0;p=u-b|0;f=a[t+8>>2]|0;if(((p|0)>-1?p:0-p|0)>>>0>=f>>>0){c=m;return}o=s[e+(i+1)>>0]|0;i=o-d|0;if(((i|0)>-1?i:0-i|0)>>>0>=f>>>0){c=m;return}if(r>>>0<4){e=s[(a[t>>2]|0)+(r+-1)>>0]|0;e=Br(~e,e+1|0,4-o+(d-b<<2)+u>>3)|0;i=n[3472+((d|512)-e)>>0]|0;n[h>>0]=n[3472+((b|512)+e)>>0]|0;n[l>>0]=i;c=m;return}else{n[h>>0]=(b+2+o+(u<<1)|0)>>>2;n[l>>0]=(d+2+(o<<1)+u|0)>>>2;c=m;return}}function Et(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0;y=c;if(r>>>0<4){d=s[(a[t>>2]|0)+(r+-1)>>0]|0;o=d+1|0;m=0-i|0;r=t+4|0;p=m<<1;b=t+8|0;d=~d;f=8;while(1){h=e+m|0;t=n[e+i>>0]|0;l=s[h>>0]|0;u=s[e>>0]|0;w=l-u|0;if((((w|0)>-1?w:0-w|0)>>>0<(a[r>>2]|0)>>>0?(k=s[e+p>>0]|0,w=k-l|0,v=a[b>>2]|0,((w|0)>-1?w:0-w|0)>>>0<v>>>0):0)?(g=t&255,t=g-u|0,((t|0)>-1?t:0-t|0)>>>0<v>>>0):0){w=Br(d,o,4-g+(u-l<<2)+k>>3)|0;t=n[3472+((u|512)-w)>>0]|0;n[h>>0]=n[3472+((l|512)+w)>>0]|0;n[e>>0]=t}f=f+-1|0;if(!f)break;else e=e+1|0}c=y;return}else{b=0-i|0;h=t+4|0;d=b<<1;t=t+8|0;l=8;while(1){o=e+b|0;r=n[e+i>>0]|0;f=s[o>>0]|0;u=s[e>>0]|0;v=f-u|0;if((((v|0)>-1?v:0-v|0)>>>0<(a[h>>2]|0)>>>0?(p=s[e+d>>0]|0,v=p-f|0,m=a[t>>2]|0,((v|0)>-1?v:0-v|0)>>>0<m>>>0):0)?(w=r&255,r=w-u|0,((r|0)>-1?r:0-r|0)>>>0<m>>>0):0){n[o>>0]=(f+2+w+(p<<1)|0)>>>2;n[e>>0]=(u+2+(w<<1)+p|0)>>>2}l=l+-1|0;if(!l)break;else e=e+1|0}c=y;return}}function Tt(e,r,t,i){e=e|0;r=r|0;t=t|0;i=i|0;var o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0;k=c;w=s[(a[t>>2]|0)+(r+-1)>>0]|0;v=w+1|0;l=0-i|0;o=t+4|0;m=l<<1;r=t+8|0;w=~w;l=e+l|0;h=n[e+i>>0]|0;u=s[l>>0]|0;f=s[e>>0]|0;g=u-f|0;t=a[o>>2]|0;if((((g|0)>-1?g:0-g|0)>>>0<t>>>0?(b=s[e+m>>0]|0,g=b-u|0,d=a[r>>2]|0,((g|0)>-1?g:0-g|0)>>>0<d>>>0):0)?(p=h&255,h=p-f|0,((h|0)>-1?h:0-h|0)>>>0<d>>>0):0){p=Br(w,v,4-p+(f-u<<2)+b>>3)|0;t=n[3472+((f|512)-p)>>0]|0;n[l>>0]=n[3472+((u|512)+p)>>0]|0;n[e>>0]=t;t=a[o>>2]|0}h=e+1|0;f=e+(1-i)|0;u=s[f>>0]|0;l=s[h>>0]|0;p=u-l|0;if(((p|0)>-1?p:0-p|0)>>>0>=t>>>0){c=k;return}o=s[e+(m|1)>>0]|0;p=o-u|0;t=a[r>>2]|0;if(((p|0)>-1?p:0-p|0)>>>0>=t>>>0){c=k;return}r=s[e+(i+1)>>0]|0;p=r-l|0;if(((p|0)>-1?p:0-p|0)>>>0>=t>>>0){c=k;return}b=Br(w,v,4-r+(l-u<<2)+o>>3)|0;p=n[3472+((l|512)-b)>>0]|0;n[f>>0]=n[3472+((u|512)+b)>>0]|0;n[h>>0]=p;c=k;return}function St(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0;w=c;p=a[r+4>>2]|0;m=a[r+8>>2]|0;if(!((t|0)==0|(t|0)==5)?(a[e+3384>>2]|0)==0:0)i=0;else{s=e+1220|0;n=0;do{i=Ye(s,n)|0;n=n+1|0}while(n>>>0<16&(i|0)==0)}l=e+1176|0;d=a[l>>2]|0;if(d){h=a[e+1212>>2]|0;n=0;f=0;s=0;do{if(a[h+(f*216|0)+196>>2]|0)break;f=f+1|0;n=n+1|0;b=(n|0)==(p|0);s=(b&1)+s|0;n=b?0:n}while(f>>>0<d>>>0);if((f|0)!=(d|0)){b=e+1212|0;d=a[b>>2]|0;f=ee(s,p)|0;if(n){l=e+1204|0;o=n;do{o=o+-1|0;h=o+f|0;Ut(d+(h*216|0)|0,r,s,o,t,i);a[d+(h*216|0)+196>>2]=1;a[l>>2]=(a[l>>2]|0)+1}while((o|0)!=0)}n=n+1|0;if(n>>>0<p>>>0){h=e+1204|0;do{l=n+f|0;u=d+(l*216|0)+196|0;if(!(a[u>>2]|0)){Ut(d+(l*216|0)|0,r,s,n,t,i);a[u>>2]=1;a[h>>2]=(a[h>>2]|0)+1}n=n+1|0}while((n|0)!=(p|0))}if(s){if(p){d=s+-1|0;o=ee(d,p)|0;n=e+1204|0;l=0-p|0;u=0;do{h=d;f=(a[b>>2]|0)+((u+o|0)*216|0)|0;while(1){Ut(f,r,h,u,t,i);a[f+196>>2]=1;a[n>>2]=(a[n>>2]|0)+1;if(!h)break;else{h=h+-1|0;f=f+(l*216|0)|0}}u=u+1|0}while((u|0)!=(p|0))}}else s=0;s=s+1|0;if(s>>>0>=m>>>0){c=w;return 0}h=e+1204|0;if(!p){c=w;return 0}do{n=a[b>>2]|0;l=ee(s,p)|0;u=0;do{o=u+l|0;f=n+(o*216|0)+196|0;if(!(a[f>>2]|0)){Ut(n+(o*216|0)|0,r,s,u,t,i);a[f>>2]=1;a[h>>2]=(a[h>>2]|0)+1}u=u+1|0}while((u|0)!=(p|0));s=s+1|0}while((s|0)!=(m|0));c=w;return 0}}if((t|0)==2|(t|0)==7){if((a[e+3384>>2]|0)==0|(i|0)==0)s=13;else s=14}else if(!i)s=13;else s=14;if((s|0)==13)cn(a[r>>2]|0,128,ee(p*384|0,m)|0);else if((s|0)==14)ln(a[r>>2]|0,i,ee(p*384|0,m)|0);s=a[l>>2]|0;a[e+1204>>2]=s;if(!s){c=w;return 0}n=a[e+1212>>2]|0;i=0;do{a[n+(i*216|0)+8>>2]=1;i=i+1|0}while(i>>>0<s>>>0);c=w;return 0}function Ut(e,r,t,i,o,f){e=e|0;r=r|0;t=t|0;i=i|0;o=o|0;f=f|0;var u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0,O=0,N=0,z=0,V=0,q=0,j=0,Y=0,H=0,W=0,G=0,Z=0,K=0,X=0,$=0,J=0,Q=0,re=0,te=0,ne=0,ie=0,ae=0,se=0,oe=0,fe=0,ue=0,le=0,ce=0,he=0,de=0,be=0,pe=0,me=0,we=0,ve=0,ke=0;ve=c;c=c+480|0;me=ve+96|0;we=ve+32|0;h=ve+24|0;d=ve;de=a[r+4>>2]|0;g=a[r+8>>2]|0;yr(r,(ee(de,t)|0)+i|0);p=a[r>>2]|0;u=t<<4;l=i<<4;b=(ee(t<<8,de)|0)+l|0;a[e+20>>2]=40;a[e+8>>2]=0;a[e>>2]=6;a[e+12>>2]=0;a[e+16>>2]=0;a[e+24>>2]=0;do{if((o|0)==2|(o|0)==7)cn(me,0,384);else{a[h>>2]=0;a[d+4>>2]=de;a[d+8>>2]=g;a[d>>2]=f;if(!f){cn(me,0,384);break}Ne(me,h,d,l,u,0,0,16,16);er(r,me);c=ve;return}}while(0);cn(we,0,64);if((t|0)!=0?(a[e+((0-de|0)*216|0)+196>>2]|0)!=0:0){y=b-(de<<4)|0;U=y|1;S=y|3;U=(s[p+U>>0]|0)+(s[p+y>>0]|0)+(s[p+(U+1)>>0]|0)+(s[p+S>>0]|0)|0;J=y|7;S=(s[p+(S+2)>>0]|0)+(s[p+(S+1)>>0]|0)+(s[p+(S+3)>>0]|0)+(s[p+J>>0]|0)|0;P=(s[p+(J+2)>>0]|0)+(s[p+(J+1)>>0]|0)+(s[p+(J+3)>>0]|0)+(s[p+(J+4)>>0]|0)|0;y=(s[p+(J+6)>>0]|0)+(s[p+(J+5)>>0]|0)+(s[p+(J+7)>>0]|0)+(s[p+(y|15)>>0]|0)|0;J=S+U|0;a[we>>2]=P+J+(a[we>>2]|0)+y;v=we+4|0;a[v>>2]=J-P-y+(a[v>>2]|0);v=1}else{U=0;S=0;P=0;y=0;v=0}if((g+-1|0)!=(t|0)?(a[e+(de*216|0)+196>>2]|0)!=0:0){M=b+(de<<8)|0;B=M|1;_=M|3;B=(s[p+B>>0]|0)+(s[p+M>>0]|0)+(s[p+(B+1)>>0]|0)+(s[p+_>>0]|0)|0;w=M|7;_=(s[p+(_+2)>>0]|0)+(s[p+(_+1)>>0]|0)+(s[p+(_+3)>>0]|0)+(s[p+w>>0]|0)|0;A=(s[p+(w+2)>>0]|0)+(s[p+(w+1)>>0]|0)+(s[p+(w+3)>>0]|0)+(s[p+(w+4)>>0]|0)|0;M=(s[p+(w+6)>>0]|0)+(s[p+(w+5)>>0]|0)+(s[p+(w+7)>>0]|0)+(s[p+(M|15)>>0]|0)|0;w=_+B|0;a[we>>2]=A+w+(a[we>>2]|0)+M;k=we+4|0;a[k>>2]=w-A-M+(a[k>>2]|0);k=1;w=v+1|0}else{k=0;B=0;_=0;A=0;M=0;w=v}if((i|0)!=0?(a[e+-20>>2]|0)!=0:0){$=b+-1|0;J=de<<4;f=de<<5;he=de*48|0;T=(s[p+($+J)>>0]|0)+(s[p+$>>0]|0)+(s[p+($+f)>>0]|0)+(s[p+($+he)>>0]|0)|0;o=de<<6;$=$+o|0;E=(s[p+($+J)>>0]|0)+(s[p+$>>0]|0)+(s[p+($+f)>>0]|0)+(s[p+($+he)>>0]|0)|0;$=$+o|0;x=(s[p+($+J)>>0]|0)+(s[p+$>>0]|0)+(s[p+($+f)>>0]|0)+(s[p+($+he)>>0]|0)|0;o=$+o|0;he=(s[p+(o+J)>>0]|0)+(s[p+o>>0]|0)+(s[p+(o+f)>>0]|0)+(s[p+(o+he)>>0]|0)|0;o=E+T|0;a[we>>2]=x+o+(a[we>>2]|0)+he;f=we+16|0;a[f>>2]=o-x-he+(a[f>>2]|0);f=w+1|0;o=1}else{f=w;T=0;E=0;x=0;he=0;o=0}do{if((de+-1|0)!=(i|0)?(a[e+412>>2]|0)!=0:0){J=b+16|0;d=de<<4;h=de<<5;b=de*48|0;e=(s[p+(J+d)>>0]|0)+(s[p+J>>0]|0)+(s[p+(J+h)>>0]|0)+(s[p+(J+b)>>0]|0)|0;m=de<<6;J=J+m|0;l=(s[p+(J+d)>>0]|0)+(s[p+J>>0]|0)+(s[p+(J+h)>>0]|0)+(s[p+(J+b)>>0]|0)|0;J=J+m|0;u=(s[p+(J+d)>>0]|0)+(s[p+J>>0]|0)+(s[p+(J+h)>>0]|0)+(s[p+(J+b)>>0]|0)|0;m=J+m|0;b=(s[p+(m+d)>>0]|0)+(s[p+m>>0]|0)+(s[p+(m+h)>>0]|0)+(s[p+(m+b)>>0]|0)|0;p=f+1|0;m=o+1|0;f=l+e|0;a[we>>2]=u+f+(a[we>>2]|0)+b;h=we+16|0;f=f-u-b+(a[h>>2]|0)|0;a[h>>2]=f;h=(w|0)==0;d=(o|0)!=0;if(!(h&d)){if(!h){h=1;f=p;o=m;l=21;break}}else a[we+4>>2]=x+he+E+T-e-l-u-b>>5;h=1;b=(v|0)!=0;e=(k|0)!=0;o=m;l=27}else l=17}while(0);if((l|0)==17){d=(o|0)!=0;if(!w){h=0;p=f;l=23}else{h=0;l=21}}if((l|0)==21){p=we+4|0;a[p>>2]=a[p>>2]>>w+3;p=f;l=23}do{if((l|0)==23){f=(o|0)==0;b=(v|0)!=0;e=(k|0)!=0;if(f&b&e){a[we+16>>2]=P+y+S+U-M-A-_-B>>5;pe=h;o=p;be=d;b=1;e=1;break}if(f){pe=h;o=p;be=d}else{f=a[we+16>>2]|0;l=27}}}while(0);if((l|0)==27){a[we+16>>2]=f>>o+3;pe=h;o=p;be=d}if((o|0)==1)a[we>>2]=a[we>>2]>>4;else if((o|0)==2)a[we>>2]=a[we>>2]>>5;else if((o|0)==3)a[we>>2]=(a[we>>2]|0)*21>>10;else a[we>>2]=a[we>>2]>>6;Pt(we);d=0;f=me;h=we;while(1){o=a[h+((d>>>2&3)<<2)>>2]|0;if((o|0)<0)o=0;else o=(o|0)>255?-1:o&255;n[f>>0]=o;o=d+1|0;if((o|0)==256)break;else{d=o;f=f+1|0;h=(o&63|0)==0?h+16|0:h}}ke=ee(g,de)|0;G=de<<3;X=0-G|0;R=X|1;$=R+1|0;J=X|3;Q=J+1|0;re=J+2|0;te=J+3|0;ne=X|7;Z=we+4|0;le=de<<6;F=le|1;ie=F+1|0;ae=le|3;se=ae+1|0;oe=ae+2|0;fe=ae+3|0;ue=le|7;C=G+-1|0;W=de<<4;L=W+-1|0;D=L+G|0;I=L+W|0;O=I+G|0;N=I+W|0;z=N+G|0;K=we+16|0;V=G+8|0;q=W|8;j=q+G|0;Y=q+W|0;H=Y+G|0;W=Y+W|0;G=W+G|0;ce=ke<<6;m=U;p=S;o=P;k=y;d=B;f=_;l=A;g=M;P=0;h=T;u=E;w=x;v=he;U=(a[r>>2]|0)+((ee(t<<6,de)|0)+(i<<3)+(ke<<8))|0;while(1){cn(we,0,64);if(b){m=(s[U+R>>0]|0)+(s[U+X>>0]|0)|0;p=(s[U+J>>0]|0)+(s[U+$>>0]|0)|0;M=(s[U+re>>0]|0)+(s[U+Q>>0]|0)|0;x=(s[U+ne>>0]|0)+(s[U+te>>0]|0)|0;k=p+m|0;a[we>>2]=M+k+(a[we>>2]|0)+x;a[Z>>2]=k-M-x+(a[Z>>2]|0);k=1}else{M=o;x=k;k=0}if(e){E=(s[U+F>>0]|0)+(s[U+le>>0]|0)|0;T=(s[U+ae>>0]|0)+(s[U+ie>>0]|0)|0;S=(s[U+oe>>0]|0)+(s[U+se>>0]|0)|0;g=(s[U+ue>>0]|0)+(s[U+fe>>0]|0)|0;o=T+E|0;a[we>>2]=S+o+(a[we>>2]|0)+g;a[Z>>2]=o-S-g+(a[Z>>2]|0);o=k+1|0}else{E=d;T=f;S=l;o=k}if(be){y=(s[U+C>>0]|0)+(s[U+-1>>0]|0)|0;B=(s[U+D>>0]|0)+(s[U+L>>0]|0)|0;_=(s[U+O>>0]|0)+(s[U+I>>0]|0)|0;A=(s[U+z>>0]|0)+(s[U+N>>0]|0)|0;k=B+y|0;a[we>>2]=_+k+(a[we>>2]|0)+A;a[K>>2]=k-_-A+(a[K>>2]|0);k=o+1|0;v=1}else{k=o;y=h;B=u;_=w;A=v;v=0}do{if(pe){l=(s[U+V>>0]|0)+(s[U+8>>0]|0)|0;h=(s[U+j>>0]|0)+(s[U+q>>0]|0)|0;f=(s[U+H>>0]|0)+(s[U+Y>>0]|0)|0;d=(s[U+G>>0]|0)+(s[U+W>>0]|0)|0;k=k+1|0;v=v+1|0;u=h+l|0;a[we>>2]=f+u+(a[we>>2]|0)+d;u=u-f-d+(a[K>>2]|0)|0;a[K>>2]=u;w=(o|0)==0;if(!(w&be)){if(w){l=54;break}else{l=49;break}}else{a[Z>>2]=_+A+B+y-l-h-f-d>>4;l=54;break}}else if(!o){w=v;l=50}else l=49}while(0);if((l|0)==49){a[Z>>2]=a[Z>>2]>>o+2;w=v;l=50}do{if((l|0)==50){l=0;v=(w|0)==0;if(v&b&e){a[K>>2]=M+x+p+m-g-S-T-E>>4;break}if(!v){u=a[K>>2]|0;v=w;l=54}}}while(0);if((l|0)==54)a[K>>2]=u>>v+2;if((k|0)==1)a[we>>2]=a[we>>2]>>3;else if((k|0)==2)a[we>>2]=a[we>>2]>>4;else if((k|0)==3)a[we>>2]=(a[we>>2]|0)*21>>9;else a[we>>2]=a[we>>2]>>5;Pt(we);v=0;w=me+((P<<6)+256)|0;u=we;while(1){k=a[u+((v>>>1&3)<<2)>>2]|0;if((k|0)<0)k=0;else k=(k|0)>255?-1:k&255;n[w>>0]=k;k=v+1|0;if((k|0)==64)break;else{v=k;w=w+1|0;u=(k&15|0)==0?u+16|0:u}}P=P+1|0;if((P|0)==2)break;else{o=M;k=x;d=E;f=T;l=S;h=y;u=B;w=_;v=A;U=U+ce|0}}er(r,me);c=ve;return}function Pt(e){e=e|0;var r=0,t=0,n=0,i=0,s=0,o=0,f=0,u=0;o=c;i=e+4|0;r=a[i>>2]|0;s=e+16|0;t=a[s>>2]|0;n=a[e>>2]|0;if(!(r|t)){a[e+60>>2]=n;a[e+56>>2]=n;a[e+52>>2]=n;a[e+48>>2]=n;a[e+44>>2]=n;a[e+40>>2]=n;a[e+36>>2]=n;a[e+32>>2]=n;a[e+28>>2]=n;a[e+24>>2]=n;a[e+20>>2]=n;a[s>>2]=n;a[e+12>>2]=n;a[e+8>>2]=n;a[i>>2]=n;c=o;return}else{u=r+n|0;s=r>>1;f=s+n|0;s=n-s|0;r=n-r|0;a[e>>2]=t+u;n=t>>1;a[e+16>>2]=n+u;a[e+32>>2]=u-n;a[e+48>>2]=u-t;a[i>>2]=t+f;a[e+20>>2]=n+f;a[e+36>>2]=f-n;a[e+52>>2]=f-t;a[e+8>>2]=t+s;a[e+24>>2]=n+s;a[e+40>>2]=s-n;a[e+56>>2]=s-t;a[e+12>>2]=t+r;a[e+28>>2]=n+r;a[e+44>>2]=r-n;a[e+60>>2]=r-t;c=o;return}}function Rt(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0;o=c;cn(r,0,952);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r>>2]=t&1;do{if(t){t=Hr(e,8)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+4>>2]=t;if((t|0)==255){t=Hr(e,16)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+8>>2]=t;t=Hr(e,16)|0;if((t|0)==-1){t=1;c=o;return t|0}else{a[r+12>>2]=t;break}}}}while(0);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+16>>2]=t&1;do{if(t){t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}else{a[r+20>>2]=(t|0)==1&1;break}}}while(0);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+24>>2]=t&1;do{if(t){t=Hr(e,3)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+28>>2]=t;t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+32>>2]=(t|0)==1&1;t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+36>>2]=t&1;if(!t){a[r+40>>2]=2;a[r+44>>2]=2;a[r+48>>2]=2;break}t=Hr(e,8)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+40>>2]=t;t=Hr(e,8)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+44>>2]=t;t=Hr(e,8)|0;if((t|0)==-1){t=1;c=o;return t|0}else{a[r+48>>2]=t;break}}else{a[r+28>>2]=5;a[r+40>>2]=2;a[r+44>>2]=2;a[r+48>>2]=2}}while(0);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+52>>2]=t&1;if(t){t=r+56|0;n=Kr(e,t)|0;if(n){t=n;c=o;return t|0}if((a[t>>2]|0)>>>0>5){t=1;c=o;return t|0}t=r+60|0;n=Kr(e,t)|0;if(n){t=n;c=o;return t|0}if((a[t>>2]|0)>>>0>5){t=1;c=o;return t|0}}t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+64>>2]=t&1;do{if(t){t=Wr(e)|0;if((Gr(e,32)|0)==-1|(t|0)==0){t=1;c=o;return t|0}a[r+68>>2]=t;t=Wr(e)|0;if((Gr(e,32)|0)==-1|(t|0)==0){t=1;c=o;return t|0}a[r+72>>2]=t;t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}else{a[r+76>>2]=(t|0)==1&1;break}}}while(0);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;i=r+80|0;a[i>>2]=t&1;if(t){n=Ft(e,r+84|0)|0;if(n){t=n;c=o;return t|0}}else{a[r+84>>2]=1;a[r+96>>2]=288000001;a[r+224>>2]=288000001;a[r+480>>2]=24;a[r+484>>2]=24;a[r+488>>2]=24;a[r+492>>2]=24}n=Hr(e,1)|0;if((n|0)==-1){t=1;c=o;return t|0}n=(n|0)==1;t=r+496|0;a[t>>2]=n&1;if(n){n=Ft(e,r+500|0)|0;if(n){t=n;c=o;return t|0}}else{a[r+500>>2]=1;a[r+512>>2]=240000001;a[r+640>>2]=240000001;a[r+896>>2]=24;a[r+900>>2]=24;a[r+904>>2]=24;a[r+908>>2]=24}if(!((a[i>>2]|0)==0?(a[t>>2]|0)==0:0))s=46;do{if((s|0)==46){t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}else{a[r+912>>2]=(t|0)==1&1;break}}}while(0);t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+916>>2]=(t|0)==1&1;t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}t=(t|0)==1;a[r+920>>2]=t&1;do{if(t){t=Hr(e,1)|0;if((t|0)==-1){t=1;c=o;return t|0}a[r+924>>2]=(t|0)==1&1;n=r+928|0;t=Kr(e,n)|0;if(t){c=o;return t|0}if((a[n>>2]|0)>>>0>16){t=1;c=o;return t|0}n=r+932|0;t=Kr(e,n)|0;if(t){c=o;return t|0}if((a[n>>2]|0)>>>0>16){t=1;c=o;return t|0}n=r+936|0;t=Kr(e,n)|0;if(t){c=o;return t|0}if((a[n>>2]|0)>>>0>16){t=1;c=o;return t|0}n=r+940|0;t=Kr(e,n)|0;if(t){c=o;return t|0}if((a[n>>2]|0)>>>0>16){t=1;c=o;return t|0}t=Kr(e,r+944|0)|0;if(t){c=o;return t|0}t=Kr(e,r+948|0)|0;if(!t)break;c=o;return t|0}else{a[r+924>>2]=1;a[r+928>>2]=2;a[r+932>>2]=1;a[r+936>>2]=16;a[r+940>>2]=16;a[r+944>>2]=16;a[r+948>>2]=16}}while(0);t=0;c=o;return t|0}function Ft(e,r){e=e|0;r=r|0;var t=0,n=0,i=0,s=0,o=0,f=0,u=0;u=c;t=Kr(e,r)|0;if(t){c=u;return t|0}t=(a[r>>2]|0)+1|0;a[r>>2]=t;if(t>>>0>32){t=1;c=u;return t|0}t=Hr(e,4)|0;if((t|0)==-1){t=1;c=u;return t|0}f=r+4|0;a[f>>2]=t;n=Hr(e,4)|0;if((n|0)==-1){t=1;c=u;return t|0}o=r+8|0;a[o>>2]=n;e:do{if(a[r>>2]|0){s=0;while(1){i=r+(s<<2)+12|0;t=Kr(e,i)|0;if(t){n=17;break}n=a[i>>2]|0;if((n|0)==-1){t=1;n=17;break}t=n+1|0;a[i>>2]=t;a[i>>2]=t<<(a[f>>2]|0)+6;i=r+(s<<2)+140|0;t=Kr(e,i)|0;if(t){n=17;break}n=a[i>>2]|0;if((n|0)==-1){t=1;n=17;break}n=n+1|0;a[i>>2]=n;a[i>>2]=n<<(a[o>>2]|0)+4;n=Hr(e,1)|0;if((n|0)==-1){t=1;n=17;break}a[r+(s<<2)+268>>2]=(n|0)==1&1;s=s+1|0;if(s>>>0>=(a[r>>2]|0)>>>0)break e}if((n|0)==17){c=u;return t|0}}}while(0);t=Hr(e,5)|0;if((t|0)==-1){t=1;c=u;return t|0}a[r+396>>2]=t+1;t=Hr(e,5)|0;if((t|0)==-1){t=1;c=u;return t|0}a[r+400>>2]=t+1;t=Hr(e,5)|0;if((t|0)==-1){t=1;c=u;return t|0}a[r+404>>2]=t+1;t=Hr(e,5)|0;if((t|0)==-1){t=1;c=u;return t|0}a[r+408>>2]=t;t=0;c=u;return t|0}function Ct(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0;p=c;e:do{if(!(a[t+284>>2]|0))b=0;else{o=0;while(1){f=a[t+(o*20|0)+288>>2]|0;if((f|0)==5){b=1;break e}else if(!f)break;o=o+1|0}b=0}}while(0);f=a[r+16>>2]|0;if((f|0)==1){if((a[n>>2]|0)!=5){i=a[e+12>>2]|0;if((a[e+8>>2]|0)>>>0>(a[t+12>>2]|0)>>>0)i=(a[r+12>>2]|0)+i|0}else i=0;h=a[r+36>>2]|0;o=(h|0)==0;if(o)f=0;else f=(a[t+12>>2]|0)+i|0;n=(a[n+4>>2]|0)==0;u=((n&(f|0)!=0)<<31>>31)+f|0;l=(u|0)!=0;if(l){s=u+-1|0;d=(s>>>0)%(h>>>0)|0;s=(s>>>0)/(h>>>0)|0}else{d=0;s=0}if(o)f=0;else{u=a[r+40>>2]|0;f=0;o=0;do{f=(a[u+(o<<2)>>2]|0)+f|0;o=o+1|0}while(o>>>0<h>>>0)}if(l){s=ee(f,s)|0;u=a[r+40>>2]|0;f=0;do{s=(a[u+(f<<2)>>2]|0)+s|0;f=f+1|0}while(f>>>0<=d>>>0)}else s=0;if(n)f=(a[r+28>>2]|0)+s|0;else f=s;s=(a[t+32>>2]|0)+(a[r+32>>2]|0)|0;o=e+12|0;if(!b){r=((s|0)<0?s:0)+f+(a[t+28>>2]|0)|0;a[o>>2]=i;a[e+8>>2]=a[t+12>>2];c=p;return r|0}else{a[o>>2]=0;a[e+8>>2]=0;r=0;c=p;return r|0}}else if(!f){if((a[n>>2]|0)!=5){o=a[e>>2]|0;f=a[t+20>>2]|0;if(o>>>0>f>>>0?(u=a[r+20>>2]|0,(o-f|0)>>>0>=u>>>1>>>0):0){o=(a[e+4>>2]|0)+u|0;u=e}else{u=e;h=11}}else{a[e+4>>2]=0;a[e>>2]=0;f=a[t+20>>2]|0;o=0;u=e;h=11}do{if((h|0)==11){if(f>>>0>o>>>0?(s=a[r+20>>2]|0,(f-o|0)>>>0>s>>>1>>>0):0){o=(a[e+4>>2]|0)-s|0;break}o=a[e+4>>2]|0}}while(0);if(!(a[n+4>>2]|0)){r=a[t+24>>2]|0;r=f+o+((r|0)<0?r:0)|0;c=p;return r|0}a[e+4>>2]=o;i=a[t+24>>2]|0;s=(i|0)<0;if(!b){a[u>>2]=f;r=f+o+(s?i:0)|0;c=p;return r|0}else{a[e+4>>2]=0;a[u>>2]=s?0-i|0:0;r=0;c=p;return r|0}}else{if((a[n>>2]|0)==5){u=0;s=0;i=e+12|0}else{f=a[t+12>>2]|0;i=e+12|0;o=a[i>>2]|0;if((a[e+8>>2]|0)>>>0>f>>>0)o=(a[r+12>>2]|0)+o|0;u=o;s=(f+o<<1)+(((a[n+4>>2]|0)==0)<<31>>31)|0}if(!b){a[i>>2]=u;a[e+8>>2]=a[t+12>>2];r=s;c=p;return r|0}else{a[i>>2]=0;a[e+8>>2]=0;r=0;c=p;return r|0}}return 0}function Lt(e,r){e=e|0;r=r|0;var t=0,n=0;t=c;ft(e);n=fn(2112)|0;a[e+3376>>2]=n;if(n){if(!r)r=0;else{a[e+1216>>2]=1;r=0}}else r=1;c=t;return r|0}function Dt(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;var s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0;w=c;c=c+208|0;l=w+204|0;p=w;s=w+112|0;o=w+40|0;m=w+16|0;f=w+12|0;d=w+8|0;a[f>>2]=0;b=e+3344|0;if((a[b>>2]|0)!=0?(a[e+3348>>2]|0)==(r|0):0){r=e+3356|0;a[m+0>>2]=a[r+0>>2];a[m+4>>2]=a[r+4>>2];a[m+8>>2]=a[r+8>>2];a[m+12>>2]=a[r+12>>2];a[m+4>>2]=a[m>>2];a[m+8>>2]=0;a[m+16>>2]=0;a[i>>2]=a[e+3352>>2]}else u=4;do{if((u|0)==4)if(!(_r(r,t,m,i)|0)){t=e+3356|0;a[t+0>>2]=a[m+0>>2];a[t+4>>2]=a[m+4>>2];a[t+8>>2]=a[m+8>>2];a[t+12>>2]=a[m+12>>2];a[t+16>>2]=a[m+16>>2];a[e+3352>>2]=a[i>>2];a[e+3348>>2]=r;break}else{d=3;c=w;return d|0}}while(0);a[b>>2]=0;if(et(m,p)|0){d=3;c=w;return d|0}if(((a[p>>2]|0)+-1|0)>>>0>11){d=0;c=w;return d|0}r=mt(m,p,e,f)|0;if(!r){do{if(!(a[f>>2]|0))u=19;else{if((a[e+1184>>2]|0)!=0?(a[e+16>>2]|0)!=0:0){if(a[e+3380>>2]|0){d=3;c=w;return d|0}if(!(a[e+1188>>2]|0)){h=e+1220|0;d=e+1336|0;a[d>>2]=He(h)|0;Ke(h);St(e,d,0)|0}else St(e,e+1336|0,a[e+1372>>2]|0)|0;a[i>>2]=0;a[b>>2]=1;a[e+1180>>2]=0;s=e+1336|0;r=e+1360|0;break}a[e+1188>>2]=0;a[e+1180>>2]=0;u=19}}while(0);do{if((u|0)==19){r=a[p>>2]|0;if((r|0)==7){if(!(Ar(m,s)|0)){ut(e,s)|0;d=0;c=w;return d|0}else{d=s+40|0;un(a[d>>2]|0);a[d>>2]=0;d=s+84|0;un(a[d>>2]|0);a[d>>2]=0;d=3;c=w;return d|0}}else if((r|0)==1|(r|0)==5){u=e+1180|0;if(a[e+1180>>2]|0){d=0;c=w;return d|0}a[e+1184>>2]=1;if(dt(e)|0){a[e+1204>>2]=0;a[e+1208>>2]=n;Tr(m,l)|0;f=e+8|0;t=a[f>>2]|0;r=ct(e,a[l>>2]|0,(a[p>>2]|0)==5&1)|0;if(r){a[e+4>>2]=256;a[e+12>>2]=0;a[f>>2]=32;a[e+16>>2]=0;a[e+3380>>2]=0;d=(r|0)==65535?5:4;c=w;return d|0}if((t|0)!=(a[f>>2]|0)){t=a[e+16>>2]|0;a[d>>2]=1;r=a[e>>2]|0;if(r>>>0<32)r=a[e+(r<<2)+20>>2]|0;else r=0;a[i>>2]=0;a[b>>2]=1;if((((((a[p>>2]|0)==5?(l=Cr(d,m,t,a[e+12>>2]|0,5)|0,(a[d>>2]|l|0)==0):0)?(h=e+1220|0,!((a[e+1276>>2]|0)!=0|(r|0)==0)):0)?(a[r+52>>2]|0)==(a[t+52>>2]|0):0)?(a[r+56>>2]|0)==(a[t+56>>2]|0):0)?(a[r+88>>2]|0)==(a[t+88>>2]|0):0)Je(h);else a[e+1280>>2]=0;a[e>>2]=a[f>>2];d=2;c=w;return d|0}}if(a[e+3380>>2]|0){d=3;c=w;return d|0}o=e+1368|0;f=e+2356|0;r=e+16|0;if(Er(m,f,a[r>>2]|0,a[e+12>>2]|0,p)|0){d=3;c=w;return d|0}if(!(dt(e)|0))t=e+1220|0;else{t=e+1220|0;if((a[p>>2]|0)!=5?(Xe(t,a[e+2368>>2]|0,(a[p+4>>2]|0)!=0&1,a[(a[r>>2]|0)+48>>2]|0)|0)!=0:0){d=3;c=w;return d|0}a[e+1336>>2]=He(t)|0}wn(o|0,f|0,988)|0;a[e+1188>>2]=1;r=e+1360|0;l=p;h=a[l+4>>2]|0;d=r;a[d>>2]=a[l>>2];a[d+4>>2]=h;pt(e,a[e+1432>>2]|0);Ke(t);if(qe(t,e+1436|0,a[e+1380>>2]|0,a[e+1412>>2]|0)|0){d=3;c=w;return d|0}s=e+1336|0;if(Lr(m,e,s,o)|0){Dr(e,a[o>>2]|0);d=3;c=w;return d|0}if(!(bt(e)|0)){d=0;c=w;return d|0}else{a[u>>2]=1;break}}else if((r|0)==8){if(!(xr(m,o)|0)){lt(e,o)|0;d=0;c=w;return d|0}else{d=o+20|0;un(a[d>>2]|0);a[d>>2]=0;d=o+24|0;un(a[d>>2]|0);a[d>>2]=0;d=o+28|0;un(a[d>>2]|0);a[d>>2]=0;d=o+44|0;un(a[d>>2]|0);a[d>>2]=0;d=3;c=w;return d|0}}else{d=0;c=w;return d|0}}}while(0);tr(s,a[e+1212>>2]|0);ht(e);f=Ct(e+1284|0,a[e+16>>2]|0,e+1368|0,r)|0;t=e+1188|0;do{if(a[t>>2]|0){o=e+1220|0;if(!(a[e+1364>>2]|0)){je(o,0,s,a[e+1380>>2]|0,f,(a[r>>2]|0)==5&1,a[e+1208>>2]|0,a[e+1204>>2]|0)|0;break}else{je(o,e+1644|0,s,a[e+1380>>2]|0,f,(a[r>>2]|0)==5&1,a[e+1208>>2]|0,a[e+1204>>2]|0)|0;break}}}while(0);a[e+1184>>2]=0;a[t>>2]=0;d=1;c=w;return d|0}else if((r|0)==65520){d=4;c=w;return d|0}else{d=3;c=w;return d|0}return 0}function It(e){e=e|0;var r=0,t=0,n=0,i=0;i=c;n=0;do{t=e+(n<<2)+20|0;r=a[t>>2]|0;if(r){un(a[r+40>>2]|0);a[(a[t>>2]|0)+40>>2]=0;un(a[(a[t>>2]|0)+84>>2]|0);a[(a[t>>2]|0)+84>>2]=0;un(a[t>>2]|0);a[t>>2]=0}n=n+1|0}while((n|0)!=32);n=0;do{t=e+(n<<2)+148|0;r=a[t>>2]|0;if(r){un(a[r+20>>2]|0);a[(a[t>>2]|0)+20>>2]=0;un(a[(a[t>>2]|0)+24>>2]|0);a[(a[t>>2]|0)+24>>2]=0;un(a[(a[t>>2]|0)+28>>2]|0);a[(a[t>>2]|0)+28>>2]=0;un(a[(a[t>>2]|0)+44>>2]|0);a[(a[t>>2]|0)+44>>2]=0;un(a[t>>2]|0);a[t>>2]=0}n=n+1|0}while((n|0)!=256);r=e+3376|0;un(a[r>>2]|0);a[r>>2]=0;r=e+1212|0;un(a[r>>2]|0);a[r>>2]=0;r=e+1172|0;un(a[r>>2]|0);a[r>>2]=0;Ze(e+1220|0);c=i;return}function Ot(e,r,t,n){e=e|0;r=r|0;t=t|0;n=n|0;var i=0;i=c;e=$e(e+1220|0)|0;if(!e){e=0;c=i;return e|0}a[r>>2]=a[e+4>>2];a[t>>2]=a[e+12>>2];a[n>>2]=a[e+8>>2];e=a[e>>2]|0;c=i;return e|0}function Nt(e){e=e|0;var r=0;r=c;e=a[e+16>>2]|0;if(!e){e=0;c=r;return e|0}e=a[e+52>>2]|0;c=r;return e|0}function zt(e){e=e|0;var r=0;r=c;e=a[e+16>>2]|0;if(!e){e=0;c=r;return e|0}e=a[e+56>>2]|0;c=r;return e|0}function Vt(e){e=e|0;var r=0;r=c;Je(e+1220|0);c=r;return}function qt(e){e=e|0;var r=0;r=c;e=(wt(e)|0)==0&1;c=r;return e|0}function jt(e){e=e|0;var r=0,t=0;t=c;e=a[e+16>>2]|0;if(((((e|0)!=0?(a[e+80>>2]|0)!=0:0)?(r=a[e+84>>2]|0,(r|0)!=0):0)?(a[r+24>>2]|0)!=0:0)?(a[r+32>>2]|0)!=0:0){e=1;c=t;return e|0}e=0;c=t;return e|0}function Yt(e){e=e|0;var r=0,t=0;t=c;e=a[e+16>>2]|0;if(((((e|0)!=0?(a[e+80>>2]|0)!=0:0)?(r=a[e+84>>2]|0,(r|0)!=0):0)?(a[r+24>>2]|0)!=0:0)?(a[r+36>>2]|0)!=0:0)e=a[r+48>>2]|0;else e=2;c=t;return e|0}function Ht(e,r,t,n,i,s){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;s=s|0;var o=0;o=c;e=a[e+16>>2]|0;if((e|0)!=0?(a[e+60>>2]|0)!=0:0){a[r>>2]=1;r=e+64|0;a[t>>2]=a[r>>2]<<1;a[n>>2]=(a[e+52>>2]<<4)-((a[e+68>>2]|0)+(a[r>>2]|0)<<1);r=e+72|0;a[i>>2]=a[r>>2]<<1;e=(a[e+56>>2]<<4)-((a[e+76>>2]|0)+(a[r>>2]|0)<<1)|0;a[s>>2]=e;c=o;return}a[r>>2]=0;a[t>>2]=0;a[n>>2]=0;a[i>>2]=0;e=0;a[s>>2]=e;c=o;return}function Wt(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0;i=c;e=a[e+16>>2]|0;e:do{if((((e|0)!=0?(a[e+80>>2]|0)!=0:0)?(n=a[e+84>>2]|0,(n|0)!=0):0)?(a[n>>2]|0)!=0:0){e=a[n+4>>2]|0;do{switch(e|0){case 8:{n=11;e=32;break e}case 13:{n=99;e=160;break e}case 12:{n=33;e=64;break e}case 6:{n=11;e=24;break e}case 7:{n=11;e=20;break e}case 255:{e=a[n+8>>2]|0;n=a[n+12>>2]|0;s=(e|0)==0|(n|0)==0;n=s?0:n;e=s?0:e;break e}case 5:{n=33;e=40;break e}case 4:{n=11;e=16;break e}case 3:{n=11;e=10;break e}case 1:case 0:{n=e;break e}case 2:{n=11;e=12;break e}case 10:{n=11;e=18;break e}case 9:{n=33;e=80;break e}case 11:{n=11;e=15;break e}default:{n=0;e=0;break e}}}while(0)}else{n=1;e=1}}while(0);a[r>>2]=e;a[t>>2]=n;c=i;return}function Gt(e){e=e|0;e=a[e+16>>2]|0;if(!e)e=0;else e=a[e>>2]|0;return e|0}function Zt(e,r){e=e|0;r=r|0;var t=0,n=0,i=0;i=c;do{if(e){t=fn(3396)|0;if(t){n=t+8|0;if(!(Lt(n,r)|0)){a[t>>2]=1;a[t+4>>2]=0;a[e>>2]=t;t=0;break}else{It(n);un(t);t=-4;break}}else t=-4}else t=-1}while(0);c=i;return t|0}function Kt(e,r){e=e|0;r=r|0;var t=0,n=0;n=c;if((e|0)==0|(r|0)==0){e=-1;c=n;return e|0}t=e+8|0;if(!(a[e+24>>2]|0)){e=-6;c=n;return e|0}if(!(a[e+20>>2]|0)){e=-6;c=n;return e|0}a[r+4>>2]=(Nt(t)|0)<<4;a[r+8>>2]=(zt(t)|0)<<4;a[r+12>>2]=jt(t)|0;a[r+16>>2]=Yt(t)|0;Ht(t,r+28|0,r+32|0,r+36|0,r+40|0,r+44|0);Wt(t,r+20|0,r+24|0);a[r>>2]=Gt(t)|0;e=0;c=n;return e|0}function Xt(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0;h=c;c=c+16|0;f=h;e:do{if((!((r|0)==0|(t|0)==0)?(i=a[r>>2]|0,(i|0)!=0):0)?(s=a[r+4>>2]|0,(s|0)!=0):0){if((e|0)!=0?(n=a[e>>2]|0,(n|0)!=0):0){a[t>>2]=0;a[f>>2]=0;u=e+8|0;a[e+3392>>2]=a[r+12>>2];o=r+8|0;r=1;while(1){if((n|0)==2){l=8;break}n=Dt(u,i,s,a[o>>2]|0,f)|0;d=a[f>>2]|0;i=i+d|0;s=s-d|0;s=(s|0)<0?0:s;a[t>>2]=i;if((n|0)==5){r=-4;break e}else if((n|0)==4){n=(qt(u)|0|s|0)==0;r=n?-2:r}else if((n|0)==2)break;else if((n|0)==1){l=13;break}if(!s)break e;n=a[e>>2]|0}if((l|0)==8){a[e>>2]=1;a[t>>2]=i+(a[f>>2]|0)}else if((l|0)==13){r=e+4|0;a[r>>2]=(a[r>>2]|0)+1;r=(s|0)==0?2:3;break}r=e+1288|0;if((a[r>>2]|0)!=0?(a[e+1244>>2]|0)!=(a[e+1248>>2]|0):0){a[r>>2]=0;a[e>>2]=2;r=3}else r=4}else r=-3}else r=-1}while(0);c=h;return r|0}function $t(e){e=e|0;a[e>>2]=2;a[e+4>>2]=3;return}function Jt(e,r,t){e=e|0;r=r|0;t=t|0;var n=0,i=0,s=0,o=0;o=c;c=c+16|0;i=o+8|0;n=o+4|0;s=o;if((e|0)==0|(r|0)==0){e=-1;c=o;return e|0}e=e+8|0;if(t)Vt(e);e=Ot(e,s,n,i)|0;if(!e){e=0;c=o;return e|0}a[r>>2]=e;a[r+4>>2]=a[s>>2];a[r+8>>2]=a[n>>2];a[r+12>>2]=a[i>>2];e=2;c=o;return e|0}function Qt(e){e=e|0;var r=0,t=0;t=c;r=hn(e)|0;a[1792]=r;a[1791]=r;a[1790]=e;a[1793]=r+e;c=t;return r|0}function en(e){e=e|0;a[1790]=e;return}function rn(){var e=0;e=c;a[1786]=a[1791];a[1787]=a[1790];do{nn()|0}while((a[1787]|0)!=0);c=e;return}function tn(){var e=0,r=0;r=c;if(Zt(7176,0)|0){se(7280)|0;e=a[1784]|0;if(e)dn(e)}else{a[1796]=1;a[1798]=1}c=r;return-1}function nn(){var e=0,r=0,t=0;r=c;a[1788]=a[1798];e=Xt(a[1794]|0,7144,7200)|0;switch(e|0){case 1:case-2:{a[1787]=0;c=r;return e|0}case 4:{if(Kt(a[1794]|0,7208)|0){e=-1;c=r;return e|0}a[1814]=(ee((a[1803]|0)*3|0,a[1804]|0)|0)>>>1;ge();e=a[1800]|0;a[1787]=(a[1786]|0)-e+(a[1787]|0);a[1786]=e;e=0;c=r;return e|0}case 2:{a[1787]=0;break}case 3:{t=a[1800]|0;a[1787]=(a[1786]|0)-t+(a[1787]|0);a[1786]=t;break}default:{c=r;return e|0}}a[1798]=(a[1798]|0)+1;if((Jt(a[1794]|0,7264,0)|0)!=2){c=r;return e|0}do{a[1796]=(a[1796]|0)+1;ae(a[1816]|0,a[1803]|0,a[1804]|0)}while((Jt(a[1794]|0,7264,0)|0)==2);c=r;return e|0}function an(){var e=0,r=0;r=c;e=a[1784]|0;if(e)dn(e);c=r;return}function sn(){var e=0,r=0;r=c;c=c+16|0;e=r;$t(e);c=r;return a[e>>2]|0}function on(){var e=0,r=0;r=c;c=c+16|0;e=r;$t(e);c=r;return a[e+4>>2]|0}function fn(e){e=e|0;var r=0;r=c;e=hn(e)|0;c=r;return e|0}function un(e){e=e|0;var r=0;r=c;dn(e);c=r;return}function ln(e,r,t){e=e|0;r=r|0;t=t|0;var n=0;n=c;wn(e|0,r|0,t|0)|0;c=n;return}function cn(e,r,t){e=e|0;r=r|0;t=t|0;var n=0;n=c;mn(e|0,r&255|0,t|0)|0;c=n;return}function hn(e){e=e|0;var r=0,t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0,_=0,A=0,M=0,x=0,E=0,T=0,S=0,U=0,P=0,R=0,F=0,C=0,L=0,D=0,I=0;I=c;do{if(e>>>0<245){if(e>>>0<11)p=16;else p=e+11&-8;e=p>>>3;l=a[1828]|0;u=l>>>e;if(u&3){s=(u&1^1)+e|0;r=s<<1;o=7352+(r<<2)|0;r=7352+(r+2<<2)|0;n=a[r>>2]|0;f=n+8|0;i=a[f>>2]|0;do{if((o|0)!=(i|0)){if(i>>>0<(a[1832]|0)>>>0)de();t=i+12|0;if((a[t>>2]|0)==(n|0)){a[t>>2]=o;a[r>>2]=i;break}else de()}else a[1828]=l&~(1<<s)}while(0);_=s<<3;a[n+4>>2]=_|3;_=n+(_|4)|0;a[_>>2]=a[_>>2]|1;_=f;c=I;return _|0}f=a[1830]|0;if(p>>>0>f>>>0){if(u){s=2<<e;s=u<<e&(s|0-s);s=(s&0-s)+-1|0;e=s>>>12&16;s=s>>>e;o=s>>>5&8;s=s>>>o;t=s>>>2&4;s=s>>>t;n=s>>>1&2;s=s>>>n;i=s>>>1&1;i=(o|e|t|n|i)+(s>>>i)|0;s=i<<1;n=7352+(s<<2)|0;s=7352+(s+2<<2)|0;t=a[s>>2]|0;e=t+8|0;o=a[e>>2]|0;do{if((n|0)!=(o|0)){if(o>>>0<(a[1832]|0)>>>0)de();f=o+12|0;if((a[f>>2]|0)==(t|0)){a[f>>2]=n;a[s>>2]=o;h=a[1830]|0;break}else de()}else{a[1828]=l&~(1<<i);h=f}}while(0);_=i<<3;u=_-p|0;a[t+4>>2]=p|3;r=t+p|0;a[t+(p|4)>>2]=u|1;a[t+_>>2]=u;if(h){n=a[1833]|0;s=h>>>3;o=s<<1;i=7352+(o<<2)|0;f=a[1828]|0;s=1<<s;if(f&s){f=7352+(o+2<<2)|0;o=a[f>>2]|0;if(o>>>0<(a[1832]|0)>>>0)de();else{d=f;b=o}}else{a[1828]=f|s;d=7352+(o+2<<2)|0;b=i}a[d>>2]=n;a[b+12>>2]=n;a[n+8>>2]=b;a[n+12>>2]=i}a[1830]=u;a[1833]=r;_=e;c=I;return _|0}u=a[1829]|0;if(u){l=(u&0-u)+-1|0;B=l>>>12&16;l=l>>>B;y=l>>>5&8;l=l>>>y;_=l>>>2&4;l=l>>>_;f=l>>>1&2;l=l>>>f;h=l>>>1&1;h=a[7616+((y|B|_|f|h)+(l>>>h)<<2)>>2]|0;l=(a[h+4>>2]&-8)-p|0;f=h;while(1){t=a[f+16>>2]|0;if(!t){t=a[f+20>>2]|0;if(!t)break}f=(a[t+4>>2]&-8)-p|0;_=f>>>0<l>>>0;l=_?f:l;f=t;h=_?t:h}u=a[1832]|0;if(h>>>0<u>>>0)de();r=h+p|0;if(h>>>0>=r>>>0)de();e=a[h+24>>2]|0;s=a[h+12>>2]|0;do{if((s|0)==(h|0)){o=h+20|0;f=a[o>>2]|0;if(!f){o=h+16|0;f=a[o>>2]|0;if(!f){n=0;break}}while(1){i=f+20|0;s=a[i>>2]|0;if(s){f=s;o=i;continue}i=f+16|0;s=a[i>>2]|0;if(!s)break;else{f=s;o=i}}if(o>>>0<u>>>0)de();else{a[o>>2]=0;n=f;break}}else{i=a[h+8>>2]|0;if(i>>>0<u>>>0)de();f=i+12|0;if((a[f>>2]|0)!=(h|0))de();o=s+8|0;if((a[o>>2]|0)==(h|0)){a[f>>2]=s;a[o>>2]=i;n=s;break}else de()}}while(0);do{if(e){f=a[h+28>>2]|0;o=7616+(f<<2)|0;if((h|0)==(a[o>>2]|0)){a[o>>2]=n;if(!n){a[1829]=a[1829]&~(1<<f);break}}else{if(e>>>0<(a[1832]|0)>>>0)de();f=e+16|0;if((a[f>>2]|0)==(h|0))a[f>>2]=n;else a[e+20>>2]=n;if(!n)break}o=a[1832]|0;if(n>>>0<o>>>0)de();a[n+24>>2]=e;f=a[h+16>>2]|0;do{if(f)if(f>>>0<o>>>0)de();else{a[n+16>>2]=f;a[f+24>>2]=n;break}}while(0);i=a[h+20>>2]|0;if(i)if(i>>>0<(a[1832]|0)>>>0)de();else{a[n+20>>2]=i;a[i+24>>2]=n;break}}}while(0);if(l>>>0<16){_=l+p|0;a[h+4>>2]=_|3;_=h+(_+4)|0;a[_>>2]=a[_>>2]|1}else{a[h+4>>2]=p|3;a[h+(p|4)>>2]=l|1;a[h+(l+p)>>2]=l;t=a[1830]|0;if(t){n=a[1833]|0;s=t>>>3;o=s<<1;i=7352+(o<<2)|0;f=a[1828]|0;s=1<<s;if(f&s){f=7352+(o+2<<2)|0;o=a[f>>2]|0;if(o>>>0<(a[1832]|0)>>>0)de();else{w=f;m=o}}else{a[1828]=f|s;w=7352+(o+2<<2)|0;m=i}a[w>>2]=n;a[m+12>>2]=n;a[n+8>>2]=m;a[n+12>>2]=i}a[1830]=l;a[1833]=r}_=h+8|0;c=I;return _|0}}}else if(e>>>0<=4294967231){e=e+11|0;p=e&-8;h=a[1829]|0;if(h){o=0-p|0;e=e>>>8;if(e){if(p>>>0>16777215)l=31;else{m=(e+1048320|0)>>>16&8;w=e<<m;b=(w+520192|0)>>>16&4;w=w<<b;l=(w+245760|0)>>>16&2;l=14-(b|m|l)+(w<<l>>>15)|0;l=p>>>(l+7|0)&1|l<<1}}else l=0;f=a[7616+(l<<2)>>2]|0;e:do{if(!f){e=0;u=0}else{if((l|0)==31)u=0;else u=25-(l>>>1)|0;i=o;e=0;n=p<<u;u=0;while(1){s=a[f+4>>2]&-8;o=s-p|0;if(o>>>0<i>>>0){if((s|0)==(p|0)){e=f;u=f;break e}else u=f}else o=i;w=a[f+20>>2]|0;f=a[f+(n>>>31<<2)+16>>2]|0;e=(w|0)==0|(w|0)==(f|0)?e:w;if(!f)break;else{i=o;n=n<<1}}}}while(0);if((e|0)==0&(u|0)==0){e=2<<l;e=h&(e|0-e);if(!e)break;w=(e&0-e)+-1|0;d=w>>>12&16;w=w>>>d;h=w>>>5&8;w=w>>>h;b=w>>>2&4;w=w>>>b;m=w>>>1&2;w=w>>>m;e=w>>>1&1;e=a[7616+((h|d|b|m|e)+(w>>>e)<<2)>>2]|0}if(!e){d=o;h=u}else while(1){w=(a[e+4>>2]&-8)-p|0;f=w>>>0<o>>>0;o=f?w:o;u=f?e:u;f=a[e+16>>2]|0;if(f){e=f;continue}e=a[e+20>>2]|0;if(!e){d=o;h=u;break}}if((h|0)!=0?d>>>0<((a[1830]|0)-p|0)>>>0:0){u=a[1832]|0;if(h>>>0<u>>>0)de();b=h+p|0;if(h>>>0>=b>>>0)de();e=a[h+24>>2]|0;s=a[h+12>>2]|0;do{if((s|0)==(h|0)){o=h+20|0;f=a[o>>2]|0;if(!f){o=h+16|0;f=a[o>>2]|0;if(!f){r=0;break}}while(1){i=f+20|0;s=a[i>>2]|0;if(s){f=s;o=i;continue}i=f+16|0;s=a[i>>2]|0;if(!s)break;else{f=s;o=i}}if(o>>>0<u>>>0)de();else{a[o>>2]=0;r=f;break}}else{i=a[h+8>>2]|0;if(i>>>0<u>>>0)de();f=i+12|0;if((a[f>>2]|0)!=(h|0))de();o=s+8|0;if((a[o>>2]|0)==(h|0)){a[f>>2]=s;a[o>>2]=i;r=s;break}else de()}}while(0);do{if(e){f=a[h+28>>2]|0;o=7616+(f<<2)|0;if((h|0)==(a[o>>2]|0)){a[o>>2]=r;if(!r){a[1829]=a[1829]&~(1<<f);break}}else{if(e>>>0<(a[1832]|0)>>>0)de();f=e+16|0;if((a[f>>2]|0)==(h|0))a[f>>2]=r;else a[e+20>>2]=r;if(!r)break}o=a[1832]|0;if(r>>>0<o>>>0)de();a[r+24>>2]=e;f=a[h+16>>2]|0;do{if(f)if(f>>>0<o>>>0)de();else{a[r+16>>2]=f;a[f+24>>2]=r;break}}while(0);f=a[h+20>>2]|0;if(f)if(f>>>0<(a[1832]|0)>>>0)de();else{a[r+20>>2]=f;a[f+24>>2]=r;break}}}while(0);e:do{if(d>>>0>=16){a[h+4>>2]=p|3;a[h+(p|4)>>2]=d|1;a[h+(d+p)>>2]=d;f=d>>>3;if(d>>>0<256){s=f<<1;t=7352+(s<<2)|0;o=a[1828]|0;f=1<<f;do{if(!(o&f)){a[1828]=o|f;k=7352+(s+2<<2)|0;g=t}else{i=7352+(s+2<<2)|0;n=a[i>>2]|0;if(n>>>0>=(a[1832]|0)>>>0){k=i;g=n;break}de()}}while(0);a[k>>2]=b;a[g+12>>2]=b;a[h+(p+8)>>2]=g;a[h+(p+12)>>2]=t;break}t=d>>>8;if(t){if(d>>>0>16777215)i=31;else{B=(t+1048320|0)>>>16&8;_=t<<B;g=(_+520192|0)>>>16&4;_=_<<g;i=(_+245760|0)>>>16&2;i=14-(g|B|i)+(_<<i>>>15)|0;i=d>>>(i+7|0)&1|i<<1}}else i=0;o=7616+(i<<2)|0;a[h+(p+28)>>2]=i;a[h+(p+20)>>2]=0;a[h+(p+16)>>2]=0;f=a[1829]|0;s=1<<i;if(!(f&s)){a[1829]=f|s;a[o>>2]=b;a[h+(p+24)>>2]=o;a[h+(p+12)>>2]=b;a[h+(p+8)>>2]=b;break}f=a[o>>2]|0;if((i|0)==31)t=0;else t=25-(i>>>1)|0;r:do{if((a[f+4>>2]&-8|0)!=(d|0)){i=d<<t;while(1){s=f+(i>>>31<<2)+16|0;o=a[s>>2]|0;if(!o)break;if((a[o+4>>2]&-8|0)==(d|0)){y=o;break r}else{i=i<<1;f=o}}if(s>>>0<(a[1832]|0)>>>0)de();else{a[s>>2]=b;a[h+(p+24)>>2]=f;a[h+(p+12)>>2]=b;a[h+(p+8)>>2]=b;break e}}else y=f}while(0);r=y+8|0;t=a[r>>2]|0;_=a[1832]|0;if(y>>>0>=_>>>0&t>>>0>=_>>>0){a[t+12>>2]=b;a[r>>2]=b;a[h+(p+8)>>2]=t;a[h+(p+12)>>2]=y;a[h+(p+24)>>2]=0;break}else de()}else{_=d+p|0;a[h+4>>2]=_|3;_=h+(_+4)|0;a[_>>2]=a[_>>2]|1}}while(0);_=h+8|0;c=I;return _|0}}}else p=-1}while(0);u=a[1830]|0;if(u>>>0>=p>>>0){t=u-p|0;r=a[1833]|0;if(t>>>0>15){a[1833]=r+p;a[1830]=t;a[r+(p+4)>>2]=t|1;a[r+u>>2]=t;a[r+4>>2]=p|3}else{a[1830]=0;a[1833]=0;a[r+4>>2]=u|3;_=r+(u+4)|0;a[_>>2]=a[_>>2]|1}_=r+8|0;c=I;return _|0}u=a[1831]|0;if(u>>>0>p>>>0){B=u-p|0;a[1831]=B;_=a[1834]|0;a[1834]=_+p;a[_+(p+4)>>2]=B|1;a[_+4>>2]=p|3;_=_+8|0;c=I;return _|0}do{if(!(a[1946]|0)){u=_e(30)|0;if(!(u+-1&u)){a[1948]=u;a[1947]=u;a[1949]=-1;a[1950]=-1;a[1951]=0;a[1939]=0;a[1946]=(Be(0)|0)&-16^1431655768;break}else de()}}while(0);l=p+48|0;s=a[1948]|0;i=p+47|0;o=s+i|0;s=0-s|0;h=o&s;if(h>>>0<=p>>>0){_=0;c=I;return _|0}e=a[1938]|0;if((e|0)!=0?(g=a[1936]|0,y=g+h|0,y>>>0<=g>>>0|y>>>0>e>>>0):0){_=0;c=I;return _|0}e:do{if(!(a[1939]&4)){f=a[1834]|0;r:do{if(f){e=7760|0;while(1){u=a[e>>2]|0;if(u>>>0<=f>>>0?(v=e+4|0,(u+(a[v>>2]|0)|0)>>>0>f>>>0):0)break;e=a[e+8>>2]|0;if(!e){x=181;break r}}if(e){u=o-(a[1831]|0)&s;if(u>>>0<2147483647){f=pe(u|0)|0;if((f|0)==((a[e>>2]|0)+(a[v>>2]|0)|0))x=190;else x=191}else u=0}else x=181}else x=181}while(0);do{if((x|0)==181){f=pe(0)|0;if((f|0)!=(-1|0)){e=f;u=a[1947]|0;o=u+-1|0;if(!(o&e))u=h;else u=h-e+(o+e&0-u)|0;e=a[1936]|0;o=e+u|0;if(u>>>0>p>>>0&u>>>0<2147483647){y=a[1938]|0;if((y|0)!=0?o>>>0<=e>>>0|o>>>0>y>>>0:0){u=0;break}o=pe(u|0)|0;if((o|0)==(f|0))x=190;else{f=o;x=191}}else u=0}else u=0}}while(0);r:do{if((x|0)==190){if((f|0)!=(-1|0)){B=f;v=u;x=201;break e}}else if((x|0)==191){e=0-u|0;do{if((f|0)!=(-1|0)&u>>>0<2147483647&l>>>0>u>>>0?(t=a[1948]|0,t=i-u+t&0-t,t>>>0<2147483647):0)if((pe(t|0)|0)==(-1|0)){pe(e|0)|0;u=0;break r}else{u=t+u|0;break}}while(0);if((f|0)==(-1|0))u=0;else{B=f;v=u;x=201;break e}}}while(0);a[1939]=a[1939]|4;x=198}else{u=0;x=198}}while(0);if((((x|0)==198?h>>>0<2147483647:0)?(B=pe(h|0)|0,_=pe(0)|0,(B|0)!=(-1|0)&(_|0)!=(-1|0)&B>>>0<_>>>0):0)?(M=_-B|0,A=M>>>0>(p+40|0)>>>0,A):0){v=A?M:u;x=201}if((x|0)==201){f=(a[1936]|0)+v|0;a[1936]=f;if(f>>>0>(a[1937]|0)>>>0)a[1937]=f;b=a[1834]|0;e:do{if(b){i=7760|0;while(1){u=a[i>>2]|0;s=i+4|0;f=a[s>>2]|0;if((B|0)==(u+f|0)){x=213;break}o=a[i+8>>2]|0;if(!o)break;else i=o}if(((x|0)==213?(a[i+12>>2]&8|0)==0:0)?b>>>0>=u>>>0&b>>>0<B>>>0:0){a[s>>2]=f+v;t=(a[1831]|0)+v|0;r=b+8|0;if(!(r&7))r=0;else r=0-r&7;_=t-r|0;a[1834]=b+r;a[1831]=_;a[b+(r+4)>>2]=_|1;a[b+(t+4)>>2]=40;a[1835]=a[1950];break}u=a[1832]|0;if(B>>>0<u>>>0){a[1832]=B;u=B}o=B+v|0;s=7760|0;while(1){if((a[s>>2]|0)==(o|0)){x=223;break}f=a[s+8>>2]|0;if(!f)break;else s=f}if((x|0)==223?(a[s+12>>2]&8|0)==0:0){a[s>>2]=B;f=s+4|0;a[f>>2]=(a[f>>2]|0)+v;f=B+8|0;if(!(f&7))w=0;else w=0-f&7;f=B+(v+8)|0;if(!(f&7))r=0;else r=0-f&7;f=B+(r+v)|0;m=w+p|0;d=B+m|0;t=f-(B+w)-p|0;a[B+(w+4)>>2]=p|3;r:do{if((f|0)!=(b|0)){if((f|0)==(a[1833]|0)){_=(a[1830]|0)+t|0;a[1830]=_;a[1833]=d;a[B+(m+4)>>2]=_|1;a[B+(_+m)>>2]=_;break}l=v+4|0;o=a[B+(l+r)>>2]|0;if((o&3|0)==1){h=o&-8;n=o>>>3;t:do{if(o>>>0>=256){e=a[B+((r|24)+v)>>2]|0;s=a[B+(v+12+r)>>2]|0;do{if((s|0)==(f|0)){s=r|16;i=B+(l+s)|0;o=a[i>>2]|0;if(!o){s=B+(s+v)|0;o=a[s>>2]|0;if(!o){F=0;break}}else s=i;while(1){n=o+20|0;i=a[n>>2]|0;if(i){o=i;s=n;continue}n=o+16|0;i=a[n>>2]|0;if(!i)break;else{o=i;s=n}}if(s>>>0<u>>>0)de();else{a[s>>2]=0;F=o;break}}else{i=a[B+((r|8)+v)>>2]|0;if(i>>>0<u>>>0)de();u=i+12|0;if((a[u>>2]|0)!=(f|0))de();o=s+8|0;if((a[o>>2]|0)==(f|0)){a[u>>2]=s;a[o>>2]=i;F=s;break}else de()}}while(0);if(!e)break;u=a[B+(v+28+r)>>2]|0;o=7616+(u<<2)|0;do{if((f|0)!=(a[o>>2]|0)){if(e>>>0<(a[1832]|0)>>>0)de();u=e+16|0;if((a[u>>2]|0)==(f|0))a[u>>2]=F;else a[e+20>>2]=F;if(!F)break t}else{a[o>>2]=F;if(F)break;a[1829]=a[1829]&~(1<<u);break t}}while(0);o=a[1832]|0;if(F>>>0<o>>>0)de();a[F+24>>2]=e;f=r|16;u=a[B+(f+v)>>2]|0;do{if(u)if(u>>>0<o>>>0)de();else{a[F+16>>2]=u;a[u+24>>2]=F;break}}while(0);f=a[B+(l+f)>>2]|0;if(!f)break;if(f>>>0<(a[1832]|0)>>>0)de();else{a[F+20>>2]=f;a[f+24>>2]=F;break}}else{s=a[B+((r|8)+v)>>2]|0;i=a[B+(v+12+r)>>2]|0;o=7352+(n<<1<<2)|0;do{if((s|0)!=(o|0)){if(s>>>0<u>>>0)de();if((a[s+12>>2]|0)==(f|0))break;de()}}while(0);if((i|0)==(s|0)){a[1828]=a[1828]&~(1<<n);break}do{if((i|0)==(o|0))S=i+8|0;else{if(i>>>0<u>>>0)de();u=i+8|0;if((a[u>>2]|0)==(f|0)){S=u;break}de()}}while(0);a[s+12>>2]=i;a[S>>2]=s}}while(0);f=B+((h|r)+v)|0;u=h+t|0}else u=t;f=f+4|0;a[f>>2]=a[f>>2]&-2;a[B+(m+4)>>2]=u|1;a[B+(u+m)>>2]=u;f=u>>>3;if(u>>>0<256){s=f<<1;i=7352+(s<<2)|0;o=a[1828]|0;f=1<<f;do{if(!(o&f)){a[1828]=o|f;C=7352+(s+2<<2)|0;L=i}else{f=7352+(s+2<<2)|0;o=a[f>>2]|0;if(o>>>0>=(a[1832]|0)>>>0){C=f;L=o;break}de()}}while(0);a[C>>2]=d;a[L+12>>2]=d;a[B+(m+8)>>2]=L;a[B+(m+12)>>2]=i;break}t=u>>>8;do{if(!t)i=0;else{if(u>>>0>16777215){i=31;break}y=(t+1048320|0)>>>16&8;_=t<<y;g=(_+520192|0)>>>16&4;_=_<<g;i=(_+245760|0)>>>16&2;i=14-(g|y|i)+(_<<i>>>15)|0;i=u>>>(i+7|0)&1|i<<1}}while(0);o=7616+(i<<2)|0;a[B+(m+28)>>2]=i;a[B+(m+20)>>2]=0;a[B+(m+16)>>2]=0;f=a[1829]|0;s=1<<i;if(!(f&s)){a[1829]=f|s;a[o>>2]=d;a[B+(m+24)>>2]=o;a[B+(m+12)>>2]=d;a[B+(m+8)>>2]=d;break}f=a[o>>2]|0;if((i|0)==31)o=0;else o=25-(i>>>1)|0;t:do{if((a[f+4>>2]&-8|0)!=(u|0)){i=u<<o;while(1){s=f+(i>>>31<<2)+16|0;o=a[s>>2]|0;if(!o)break;if((a[o+4>>2]&-8|0)==(u|0)){D=o;break t}else{i=i<<1;f=o}}if(s>>>0<(a[1832]|0)>>>0)de();else{a[s>>2]=d;a[B+(m+24)>>2]=f;a[B+(m+12)>>2]=d;a[B+(m+8)>>2]=d;break r}}else D=f}while(0);r=D+8|0;t=a[r>>2]|0;_=a[1832]|0;if(D>>>0>=_>>>0&t>>>0>=_>>>0){a[t+12>>2]=d;a[r>>2]=d;a[B+(m+8)>>2]=t;a[B+(m+12)>>2]=D;a[B+(m+24)>>2]=0;break}else de()}else{_=(a[1831]|0)+t|0;a[1831]=_;a[1834]=d;a[B+(m+4)>>2]=_|1}}while(0);_=B+(w|8)|0;c=I;return _|0}f=7760|0;while(1){o=a[f>>2]|0;if(o>>>0<=b>>>0?(E=a[f+4>>2]|0,T=o+E|0,T>>>0>b>>>0):0)break;f=a[f+8>>2]|0}f=o+(E+-39)|0;if(!(f&7))f=0;else f=0-f&7;s=o+(E+-47+f)|0;s=s>>>0<(b+16|0)>>>0?b:s;o=s+8|0;f=B+8|0;if(!(f&7))f=0;else f=0-f&7;i=v+-40-f|0;a[1834]=B+f;a[1831]=i;a[B+(f+4)>>2]=i|1;a[B+(v+-36)>>2]=40;a[1835]=a[1950];a[s+4>>2]=27;a[o+0>>2]=a[1940];a[o+4>>2]=a[1941];a[o+8>>2]=a[1942];a[o+12>>2]=a[1943];a[1940]=B;a[1941]=v;a[1943]=0;a[1942]=o;i=s+28|0;a[i>>2]=7;if((s+32|0)>>>0<T>>>0)do{_=i;i=i+4|0;a[i>>2]=7}while((_+8|0)>>>0<T>>>0);if((s|0)!=(b|0)){u=s-b|0;f=b+(u+4)|0;a[f>>2]=a[f>>2]&-2;a[b+4>>2]=u|1;a[b+u>>2]=u;f=u>>>3;if(u>>>0<256){s=f<<1;i=7352+(s<<2)|0;o=a[1828]|0;f=1<<f;do{if(!(o&f)){a[1828]=o|f;U=7352+(s+2<<2)|0;P=i}else{t=7352+(s+2<<2)|0;r=a[t>>2]|0;if(r>>>0>=(a[1832]|0)>>>0){U=t;P=r;break}de()}}while(0);a[U>>2]=b;a[P+12>>2]=b;a[b+8>>2]=P;a[b+12>>2]=i;break}t=u>>>8;if(t){if(u>>>0>16777215)s=31;else{B=(t+1048320|0)>>>16&8;_=t<<B;y=(_+520192|0)>>>16&4;_=_<<y;s=(_+245760|0)>>>16&2;s=14-(y|B|s)+(_<<s>>>15)|0;s=u>>>(s+7|0)&1|s<<1}}else s=0;o=7616+(s<<2)|0;a[b+28>>2]=s;a[b+20>>2]=0;a[b+16>>2]=0;n=a[1829]|0;f=1<<s;if(!(n&f)){a[1829]=n|f;a[o>>2]=b;a[b+24>>2]=o;a[b+12>>2]=b;a[b+8>>2]=b;break}n=a[o>>2]|0;if((s|0)==31)t=0;else t=25-(s>>>1)|0;r:do{if((a[n+4>>2]&-8|0)!=(u|0)){f=u<<t;while(1){o=n+(f>>>31<<2)+16|0;t=a[o>>2]|0;if(!t)break;if((a[t+4>>2]&-8|0)==(u|0)){R=t;break r}else{f=f<<1;n=t}}if(o>>>0<(a[1832]|0)>>>0)de();else{a[o>>2]=b;a[b+24>>2]=n;a[b+12>>2]=b;a[b+8>>2]=b;break e}}else R=n}while(0);r=R+8|0;t=a[r>>2]|0;_=a[1832]|0;if(R>>>0>=_>>>0&t>>>0>=_>>>0){a[t+12>>2]=b;a[r>>2]=b;a[b+8>>2]=t;a[b+12>>2]=R;a[b+24>>2]=0;break}else de()}}else{_=a[1832]|0;if((_|0)==0|B>>>0<_>>>0)a[1832]=B;a[1940]=B;a[1941]=v;a[1943]=0;a[1837]=a[1946];a[1836]=-1;r=0;do{_=r<<1;y=7352+(_<<2)|0;a[7352+(_+3<<2)>>2]=y;a[7352+(_+2<<2)>>2]=y;r=r+1|0}while((r|0)!=32);r=B+8|0;if(!(r&7))r=0;else r=0-r&7;_=v+-40-r|0;a[1834]=B+r;a[1831]=_;a[B+(r+4)>>2]=_|1;a[B+(v+-36)>>2]=40;a[1835]=a[1950]}}while(0);r=a[1831]|0;if(r>>>0>p>>>0){B=r-p|0;a[1831]=B;_=a[1834]|0;a[1834]=_+p;a[_+(p+4)>>2]=B|1;a[_+4>>2]=p|3;_=_+8|0;c=I;return _|0}}a[(Ae()|0)>>2]=12;_=0;c=I;return _|0}function dn(e){e=e|0;var r=0,t=0,n=0,i=0,s=0,o=0,f=0,u=0,l=0,h=0,d=0,b=0,p=0,m=0,w=0,v=0,k=0,g=0,y=0,B=0;B=c;if(!e){c=B;return}i=e+-8|0;o=a[1832]|0;if(i>>>0<o>>>0)de();s=a[e+-4>>2]|0;n=s&3;if((n|0)==1)de();m=s&-8;w=e+(m+-8)|0;do{if(!(s&1)){s=a[i>>2]|0;if(!n){c=B;return}f=-8-s|0;h=e+f|0;d=s+m|0;if(h>>>0<o>>>0)de();if((h|0)==(a[1833]|0)){i=e+(m+-4)|0;s=a[i>>2]|0;if((s&3|0)!=3){y=h;l=d;break}a[1830]=d;a[i>>2]=s&-2;a[e+(f+4)>>2]=d|1;a[w>>2]=d;c=B;return}t=s>>>3;if(s>>>0<256){n=a[e+(f+8)>>2]|0;i=a[e+(f+12)>>2]|0;s=7352+(t<<1<<2)|0;if((n|0)!=(s|0)){if(n>>>0<o>>>0)de();if((a[n+12>>2]|0)!=(h|0))de()}if((i|0)==(n|0)){a[1828]=a[1828]&~(1<<t);y=h;l=d;break}if((i|0)!=(s|0)){if(i>>>0<o>>>0)de();s=i+8|0;if((a[s>>2]|0)==(h|0))r=s;else de()}else r=i+8|0;a[n+12>>2]=i;a[r>>2]=n;y=h;l=d;break}r=a[e+(f+24)>>2]|0;n=a[e+(f+12)>>2]|0;do{if((n|0)==(h|0)){i=e+(f+20)|0;s=a[i>>2]|0;if(!s){i=e+(f+16)|0;s=a[i>>2]|0;if(!s){u=0;break}}while(1){t=s+20|0;n=a[t>>2]|0;if(n){s=n;i=t;continue}t=s+16|0;n=a[t>>2]|0;if(!n)break;else{s=n;i=t}}if(i>>>0<o>>>0)de();else{a[i>>2]=0;u=s;break}}else{t=a[e+(f+8)>>2]|0;if(t>>>0<o>>>0)de();s=t+12|0;if((a[s>>2]|0)!=(h|0))de();i=n+8|0;if((a[i>>2]|0)==(h|0)){a[s>>2]=n;a[i>>2]=t;u=n;break}else de()}}while(0);if(r){s=a[e+(f+28)>>2]|0;i=7616+(s<<2)|0;if((h|0)==(a[i>>2]|0)){a[i>>2]=u;if(!u){a[1829]=a[1829]&~(1<<s);y=h;l=d;break}}else{if(r>>>0<(a[1832]|0)>>>0)de();s=r+16|0;if((a[s>>2]|0)==(h|0))a[s>>2]=u;else a[r+20>>2]=u;if(!u){y=h;l=d;break}}i=a[1832]|0;if(u>>>0<i>>>0)de();a[u+24>>2]=r;s=a[e+(f+16)>>2]|0;do{if(s)if(s>>>0<i>>>0)de();else{a[u+16>>2]=s;a[s+24>>2]=u;break}}while(0);s=a[e+(f+20)>>2]|0;if(s){if(s>>>0<(a[1832]|0)>>>0)de();else{a[u+20>>2]=s;a[s+24>>2]=u;y=h;l=d;break}}else{y=h;l=d}}else{y=h;l=d}}else{y=i;l=m}}while(0);if(y>>>0>=w>>>0)de();s=e+(m+-4)|0;i=a[s>>2]|0;if(!(i&1))de();if(!(i&2)){if((w|0)==(a[1834]|0)){h=(a[1831]|0)+l|0;a[1831]=h;a[1834]=y;a[y+4>>2]=h|1;if((y|0)!=(a[1833]|0)){c=B;return}a[1833]=0;a[1830]=0;c=B;return}if((w|0)==(a[1833]|0)){h=(a[1830]|0)+l|0;a[1830]=h;a[1833]=y;a[y+4>>2]=h|1;a[y+h>>2]=h;c=B;return}o=(i&-8)+l|0;t=i>>>3;do{if(i>>>0>=256){r=a[e+(m+16)>>2]|0;s=a[e+(m|4)>>2]|0;do{if((s|0)==(w|0)){i=e+(m+12)|0;s=a[i>>2]|0;if(!s){i=e+(m+8)|0;s=a[i>>2]|0;if(!s){p=0;break}}while(1){t=s+20|0;n=a[t>>2]|0;if(n){s=n;i=t;continue}t=s+16|0;n=a[t>>2]|0;if(!n)break;else{s=n;i=t}}if(i>>>0<(a[1832]|0)>>>0)de();else{a[i>>2]=0;p=s;break}}else{i=a[e+m>>2]|0;if(i>>>0<(a[1832]|0)>>>0)de();n=i+12|0;if((a[n>>2]|0)!=(w|0))de();t=s+8|0;if((a[t>>2]|0)==(w|0)){a[n>>2]=s;a[t>>2]=i;p=s;break}else de()}}while(0);if(r){s=a[e+(m+20)>>2]|0;i=7616+(s<<2)|0;if((w|0)==(a[i>>2]|0)){a[i>>2]=p;if(!p){a[1829]=a[1829]&~(1<<s);break}}else{if(r>>>0<(a[1832]|0)>>>0)de();s=r+16|0;if((a[s>>2]|0)==(w|0))a[s>>2]=p;else a[r+20>>2]=p;if(!p)break}s=a[1832]|0;if(p>>>0<s>>>0)de();a[p+24>>2]=r;i=a[e+(m+8)>>2]|0;do{if(i)if(i>>>0<s>>>0)de();else{a[p+16>>2]=i;a[i+24>>2]=p;break}}while(0);t=a[e+(m+12)>>2]|0;if(t)if(t>>>0<(a[1832]|0)>>>0)de();else{a[p+20>>2]=t;a[t+24>>2]=p;break}}}else{n=a[e+m>>2]|0;i=a[e+(m|4)>>2]|0;s=7352+(t<<1<<2)|0;if((n|0)!=(s|0)){if(n>>>0<(a[1832]|0)>>>0)de();if((a[n+12>>2]|0)!=(w|0))de()}if((i|0)==(n|0)){a[1828]=a[1828]&~(1<<t);break}if((i|0)!=(s|0)){if(i>>>0<(a[1832]|0)>>>0)de();s=i+8|0;if((a[s>>2]|0)==(w|0))b=s;else de()}else b=i+8|0;a[n+12>>2]=i;a[b>>2]=n}}while(0);a[y+4>>2]=o|1;a[y+o>>2]=o;if((y|0)==(a[1833]|0)){a[1830]=o;c=B;return}else s=o}else{a[s>>2]=i&-2;a[y+4>>2]=l|1;a[y+l>>2]=l;s=l}n=s>>>3;if(s>>>0<256){i=n<<1;s=7352+(i<<2)|0;t=a[1828]|0;n=1<<n;if(t&n){t=7352+(i+2<<2)|0;r=a[t>>2]|0;if(r>>>0<(a[1832]|0)>>>0)de();else{v=t;k=r}}else{a[1828]=t|n;v=7352+(i+2<<2)|0;k=s}a[v>>2]=y;a[k+12>>2]=y;a[y+8>>2]=k;a[y+12>>2]=s;c=B;return}t=s>>>8;if(t){if(s>>>0>16777215)i=31;else{l=(t+1048320|0)>>>16&8;h=t<<l;u=(h+520192|0)>>>16&4;h=h<<u;i=(h+245760|0)>>>16&2;i=14-(u|l|i)+(h<<i>>>15)|0;i=s>>>(i+7|0)&1|i<<1}}else i=0;r=7616+(i<<2)|0;a[y+28>>2]=i;a[y+20>>2]=0;a[y+16>>2]=0;t=a[1829]|0;n=1<<i;e:do{if(t&n){r=a[r>>2]|0;if((i|0)==31)t=0;else t=25-(i>>>1)|0;r:do{if((a[r+4>>2]&-8|0)!=(s|0)){i=s<<t;while(1){n=r+(i>>>31<<2)+16|0;t=a[n>>2]|0;if(!t)break;if((a[t+4>>2]&-8|0)==(s|0)){g=t;break r}else{i=i<<1;r=t}}if(n>>>0<(a[1832]|0)>>>0)de();else{a[n>>2]=y;a[y+24>>2]=r;a[y+12>>2]=y;a[y+8>>2]=y;break e}}else g=r}while(0);t=g+8|0;r=a[t>>2]|0;h=a[1832]|0;if(g>>>0>=h>>>0&r>>>0>=h>>>0){a[r+12>>2]=y;a[t>>2]=y;a[y+8>>2]=r;a[y+12>>2]=g;a[y+24>>2]=0;break}else de()}else{a[1829]=t|n;a[r>>2]=y;a[y+24>>2]=r;a[y+12>>2]=y;a[y+8>>2]=y}}while(0);h=(a[1836]|0)+-1|0;a[1836]=h;if(!h)r=7768|0;else{c=B;return}while(1){r=a[r>>2]|0;if(!r)break;else r=r+8|0}a[1836]=-1;c=B;return}function bn(){}function pn(e){e=e|0;var r=0;r=e;while(n[r>>0]|0)r=r+1|0;return r-e|0}function mn(e,r,t){e=e|0;r=r|0;t=t|0;var i=0,s=0,o=0,f=0;i=e+t|0;if((t|0)>=20){r=r&255;o=e&3;f=r|r<<8|r<<16|r<<24;s=i&~3;if(o){o=e+4-o|0;while((e|0)<(o|0)){n[e>>0]=r;e=e+1|0}}while((e|0)<(s|0)){a[e>>2]=f;e=e+4|0}}while((e|0)<(i|0)){n[e>>0]=r;e=e+1|0}return e-t|0}function wn(e,r,t){e=e|0;r=r|0;t=t|0;var i=0;if((t|0)>=4096)return ve(e|0,r|0,t|0)|0;i=e|0;if((e&3)==(r&3)){while(e&3){if(!t)return i|0;n[e>>0]=n[r>>0]|0;e=e+1|0;r=r+1|0;t=t-1|0}while((t|0)>=4){a[e>>2]=a[r>>2];e=e+4|0;r=r+4|0;t=t-4|0}}while((t|0)>0){n[e>>0]=n[r>>0]|0;e=e+1|0;r=r+1|0;t=t-1|0}return i|0}function vn(e,r,t,n,i,a){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;a=a|0;gn[e&3](r|0,t|0,n|0,i|0,a|0)}function kn(e,r,t,n,i){e=e|0;r=r|0;t=t|0;n=n|0;i=i|0;re(0)}var gn=[kn,Ve,ze,kn];return{_strlen:pn,_free:dn,_broadwayGetMajorVersion:sn,_get_h264bsdClip:kt,_broadwayExit:an,_memset:mn,_broadwayCreateStream:Qt,_malloc:hn,_memcpy:wn,_broadwayGetMinorVersion:on,_broadwayPlayStream:rn,_broadwaySetStreamLength:en,_broadwayInit:tn,runPostSets:bn,stackAlloc:sr,stackSave:or,stackRestore:fr,setThrew:ur,setTempRet0:hr,getTempRet0:dr,dynCall_viiiii:vn}}(p.Xc,p.Yc,Q),Bb=p._strlen=$._strlen,Ea=p._free=$._free;p._broadwayGetMajorVersion=$._broadwayGetMajorVersion,p._get_h264bsdClip=$._get_h264bsdClip,p._broadwayExit=$._broadwayExit;var Gb=p._memset=$._memset;p._broadwayCreateStream=$._broadwayCreateStream;var Ca=p._malloc=$._malloc,gc=p._memcpy=$._memcpy;if(p._broadwayGetMinorVersion=$._broadwayGetMinorVersion,p._broadwayPlayStream=$._broadwayPlayStream,p._broadwaySetStreamLength=$._broadwaySetStreamLength,p._broadwayInit=$._broadwayInit,p.runPostSets=$.runPostSets,p.dynCall_viiiii=$.dynCall_viiiii,z.pb=$.stackAlloc,z.Tb=$.stackSave,z.Sb=$.stackRestore,z.Yd=$.setTempRet0,z.xd=$.getTempRet0,T)if("function"==typeof p.locateFile?T=p.locateFile(T):p.memoryInitializerPrefixURL&&(T=p.memoryInitializerPrefixURL+T),t||da){var hc=p.readBinary(T);N.set(hc,Ia)}else Ya(),yb(T,function(e){N.set(e,Ia),Za()},function(){d("could not load memory initializer "+T)});function ia(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}ia.prototype=Error();var ic,jc=k,Xa=function e(){!p.calledRun&&lc&&mc(),p.calledRun||(Xa=e)};function mc(e){function r(){if(!p.calledRun&&(p.calledRun=i,!H)){if(Sa||(Sa=i,Na(R)),Na(Pa),ba&&jc!==k&&p.fa("pre-main prep time: "+(Date.now()-jc)+" ms"),p.onRuntimeInitialized&&p.onRuntimeInitialized(),p._main&&lc&&p.callMain(e),p.postRun)for("function"==typeof p.postRun&&(p.postRun=[p.postRun]);p.postRun.length;)Ua(p.postRun.shift());Na(Ra)}}if(e=e||p.arguments,jc===k&&(jc=Date.now()),!(0<S)){if(p.preRun)for("function"==typeof p.preRun&&(p.preRun=[p.preRun]);p.preRun.length;)Ta(p.preRun.shift());Na(Oa),!(0<S)&&!p.calledRun&&(p.setStatus?(p.setStatus("Running..."),setTimeout(function(){setTimeout(function(){p.setStatus("")},1),r()},1)):r())}}function nc(e){p.noExitRuntime||(H=i,y=ic,Na(Qa),t?(process.stdout.once("drain",function(){process.exit(e)}),console.log(" "),setTimeout(function(){process.exit(e)},500)):da&&"function"==typeof quit&&quit(e),d(new ia(e)))}function A(e){e&&(p.print(e),p.fa(e)),H=i,d("abort() at "+Fa()+"\nIf this abort() is unexpected, build with -s ASSERTIONS=1 which can give more information.")}if(p.callMain=p.ag=function(e){function r(){for(var e=0;3>e;e++)n.push(0)}w(0==S,"cannot call main when async dependencies remain! (listen on __ATMAIN__)"),w(0==Oa.length,"cannot call main when preRun functions remain to be called"),e=e||[],Sa||(Sa=i,Na(R));var t=e.length+1,n=[M(Va(p.thisProgram),"i8",0)];r();for(var a=0;a<t-1;a+=1)n.push(M(Va(e[a]),"i8",0)),r();n.push(0),n=M(n,"i32",0),ic=y;try{nc(p._main(t,n,0))}catch(e){e instanceof ia||("SimulateInfiniteLoop"==e?p.noExitRuntime=i:(e&&"object"==typeof e&&e.stack&&p.fa("exception thrown: "+[e,e.stack]),d(e)))}},p.run=p.Ng=mc,p.exit=p.hg=nc,p.abort=p.abort=A,p.preInit)for("function"==typeof p.preInit&&(p.preInit=[p.preInit]);0<p.preInit.length;)p.preInit.pop()();var lc=m;p.noInitialRun&&(lc=m),mc();var resultModule=window.Module||global.Module||Module;return resultModule},nowValue=function(){return(new Date).getTime()};"undefined"!=typeof performance&&performance.now&&(nowValue=function(){return performance.now()});var Broadway=function(e){var r;this.options=e||{},this.now=nowValue;var t=getModule.apply({},[function(){},function(e,t,n){var a,s=this.pictureBuffers[e];s||(s=this.pictureBuffers[e]=i(e,t*n*3/2));var o=!1;if(this.infoAr.length&&(o=!0,a=this.infoAr),this.infoAr=[],this.options.rgb){r||(r=getAsm(t,n)),r.inp.set(s),r.doit();var f=new Uint8Array(r.outSize);return f.set(r.out),o&&(a[0].finishDecoding=nowValue()),void this.onPictureDecoded(f,t,n,a)}o&&(a[0].finishDecoding=nowValue()),this.onPictureDecoded(s,t,n,a)}.bind(this)]),n=(t.HEAP8,t.HEAPU8);t.HEAP16,t.HEAP32,t._get_h264bsdClip();function i(e,r){return n.subarray(e,e+r)}t._broadwayInit(),this.streamBuffer=i(t._broadwayCreateStream(1048576),1048576),this.pictureBuffers={},this.infoAr=[],this.onPictureDecoded=function(e,r,t,n){},this.decode=function(e,r){r&&(this.infoAr.push(r),r.startDecoding=nowValue()),this.streamBuffer.set(e),t._broadwaySetStreamLength(e.length),t._broadwayPlayStream()}};Broadway.prototype={configure:function(e){console.info("Broadway Configured: "+JSON.stringify(e))}};var asmInstances={},getAsm=function(e,r){var t=e+"x"+r;if(asmInstances[t])return asmInstances[t];for(var n=e*r,i=(0|n)>>2,a=n+i+i,s=e*r*4,o=a+s+4*Math.pow(2,24),f=Math.pow(2,24),u=f;u<o;)u+=f;var l=new ArrayBuffer(u),c=asmFactory(global,{},l);return c.init(e,r),asmInstances[t]=c,c.heap=l,c.out=new Uint8Array(l,0,s),c.inp=new Uint8Array(l,s,a),c.outSize=s,c};function asmFactory(e,r,t){"use asm";var n=e.Math.imul;var i=e.Math.min;var a=e.Math.max;var s=e.Math.pow;var o=new e.Uint8Array(t);var f=new e.Uint32Array(t);var u=new e.Uint8Array(t);var l=new e.Uint8Array(t);var c=new e.Uint32Array(t);var h=0;var d=0;var b=0;var p=0;var m=0;var w=0;var v=0;var k=0;var g=0;var y=0;function B(e,r){e=e|0;r=r|0;var t=0;var i=0;h=e;g=n(e,4)|0;d=r;b=n(h|0,d|0)|0;p=(b|0)>>2;w=n(n(h,d)|0,4)|0;m=b+p|0+p|0;k=0;v=k+w|0;y=v+m|0;i=~~+s(+2,+24);i=n(i,4)|0;for(t=0|0;(t|0)<(i|0)|0;t=t+4|0){c[(y+t|0)>>2]=0}}function _(){var e=0;var r=0;var t=0;var n=0;var i=0;var a=0;var s=0;var o=0;var f=0;var l=0;var m=0;var w=0;var B=0;var _=0;B=k|0;e=v|0;r=e+b|0|0;t=r+p|0;for(f=0;(f|0)<(d|0);f=f+2|0){m=r;w=t;for(l=0;(l|0)<(h|0);l=l+2|0){n=u[e>>0]|0;i=u[(e+h|0)>>0]|0;a=u[r>>0]|0;s=u[t>>0]|0;_=((n<<16|0)+(a<<8|0)|0)+s|0;o=c[(y+_|0)>>2]|0;if(o){}else{o=A(n,a,s)|0;c[(y+_|0)>>2]=o|0}c[B>>2]=o;_=((i<<16|0)+(a<<8|0)|0)+s|0;o=c[(y+_|0)>>2]|0;if(o){}else{o=A(i,a,s)|0;c[(y+_|0)>>2]=o|0}c[(B+g|0)>>2]=o;B=B+4|0;e=e+1|0;n=u[e>>0]|0;i=u[(e+h|0)>>0]|0;_=((n<<16|0)+(a<<8|0)|0)+s|0;o=c[(y+_|0)>>2]|0;if(o){}else{o=A(n,a,s)|0;c[(y+_|0)>>2]=o|0}c[B>>2]=o;_=((i<<16|0)+(a<<8|0)|0)+s|0;o=c[(y+_|0)>>2]|0;if(o){}else{o=A(i,a,s)|0;c[(y+_|0)>>2]=o|0}c[(B+g|0)>>2]=o;B=B+4|0;e=e+1|0;r=r+1|0;t=t+1|0}B=B+g|0;e=e+h|0}}function A(e,r,t){e=e|0;r=r|0;t=t|0;var s=0;var o=0;var f=0;var u=0;var l=0;var c=0;var h=0;var d=0;var b=0;l=n(1192,e-16|0)|0;c=n(1634,t-128|0)|0;h=n(832,t-128|0)|0;d=n(400,r-128|0)|0;b=n(2066,r-128|0)|0;s=(l+c|0)>>10|0;o=((l-h|0)-d|0)>>10|0;f=(l+b|0)>>10|0;if((s&255|0)!=(s|0)|0){s=i(255,a(0,s|0)|0)|0}if((o&255|0)!=(o|0)|0){o=i(255,a(0,o|0)|0)|0}if((f&255|0)!=(f|0)|0){f=i(255,a(0,f|0)|0)|0}u=255;u=u<<8|0;u=u+f|0;u=u<<8|0;u=u+o|0;u=u<<8|0;u=u+s|0;return u|0}return{init:B,doit:_}}if("undefined"!=typeof self){var isWorker=!1,decoder,reuseMemory=!1,memAr=[],getMem=function(e){if(memAr.length){for(var r=memAr.shift();r&&r.byteLength!==e;)r=memAr.shift();if(r)return r}return new ArrayBuffer(e)};self.addEventListener("message",function(e){isWorker?(reuseMemory&&e.data.reuse&&memAr.push(e.data.reuse),e.data.buf&&decoder.decode(new Uint8Array(e.data.buf,e.data.offset||0,e.data.length),e.data.info)):e.data&&"Broadway.js - Worker init"===e.data.type&&(isWorker=!0,decoder=new Broadway(e.data.options),e.data.options.reuseMemory?(reuseMemory=!0,decoder.onPictureDecoded=function(e,r,t,n){var i=new Uint8Array(getMem(e.length));i.set(e,0,e.length),postMessage({buf:i.buffer,length:e.length,width:r,height:t,infos:n},[i.buffer])}):decoder.onPictureDecoded=function(e,r,t,n){e&&(e=new Uint8Array(e));var i=new Uint8Array(e.length);i.set(e,0,e.length),postMessage({buf:i.buffer,length:e.length,width:r,height:t,infos:n},[i.buffer])},postMessage({consoleLog:"broadway worker initialized"}))},!1)}return Broadway.nowValue=nowValue,Broadway}()})}).call(exports,__webpack_require__(6),"/")},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=f(t(13)),i=f(t(14)),a=f(t(15)),s=f(t(7)),o=f(t(16));function f(e){return e&&e.__esModule?e:{default:e}}const u=s.default.createFromSource("x-shader/x-vertex","\n  attribute vec3 aVertexPosition;\n  attribute vec2 aTextureCoord;\n  uniform mat4 uMVMatrix;\n  uniform mat4 uPMatrix;\n  varying highp vec2 vTextureCoord;\n  void main(void) {\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n    vTextureCoord = aTextureCoord;\n  }\n"),l=s.default.createFromSource("x-shader/x-fragment","\n  precision highp float;\n  varying highp vec2 vTextureCoord;\n  uniform sampler2D YTexture;\n  uniform sampler2D UTexture;\n  uniform sampler2D VTexture;\n  const mat4 YUV2RGB = mat4\n  (\n   1.1643828125, 0, 1.59602734375, -.87078515625,\n   1.1643828125, -.39176171875, -.81296875, .52959375,\n   1.1643828125, 2.017234375, 0, -1.081390625,\n   0, 0, 0, 1\n  );\n\n  void main(void) {\n   gl_FragColor = vec4( texture2D(YTexture,  vTextureCoord).x, texture2D(UTexture, vTextureCoord).x, texture2D(VTexture, vTextureCoord).x, 1) * YUV2RGB;\n  }\n");r.default=class extends o.default{constructor(e,r){super(e,r),this.decode=((e,r,t)=>{if(!e)return;const n=r*t,i=n>>2;this.YTexture.fill(e.subarray(0,n)),this.UTexture.fill(e.subarray(n,n+i)),this.VTexture.fill(e.subarray(n+i,n+2*i)),this.drawScene()})}onInitShaders(){this.program=new n.default(this.gl),this.program.attach(new i.default(this.gl,u)),this.program.attach(new i.default(this.gl,l)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)}onInitTextures(){this.YTexture=new a.default(this.gl,this.size),this.UTexture=new a.default(this.gl,this.size.getHalfSize()),this.VTexture=new a.default(this.gl,this.size.getHalfSize())}onInitSceneTextures(){this.YTexture.bind(0,this.program,"YTexture"),this.UTexture.bind(1,this.program,"UTexture"),this.VTexture.bind(2,this.program,"VTexture")}fillYUVTextures(e,r,t){this.YTexture.fill(e),this.UTexture.fill(r),this.VTexture.fill(t)}toString(){return"YUVCanvas Size: "+this.size}}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(t(3));r.default=class{constructor(e){this.gl=e,this.program=this.gl.createProgram()}attach(e){this.gl.attachShader(this.program,e.shader)}link(){this.gl.linkProgram(this.program),(0,n.default)(this.gl.getProgramParameter(this.program,this.gl.LINK_STATUS),"Unable to initialize the shader program.")}use(){this.gl.useProgram(this.program)}getAttributeLocation(e){return this.gl.getAttribLocation(this.program,e)}setMatrixUniform(e,r){const t=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(t,!1,r)}}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.default=function(e,r){if("x-shader/x-fragment"===r.type)this.shader=e.createShader(e.FRAGMENT_SHADER);else{if("x-shader/x-vertex"!==r.type)return void(0,n.default)("Unknown shader type: "+r.type);this.shader=e.createShader(e.VERTEX_SHADER)}e.shaderSource(this.shader,r.source),e.compileShader(this.shader),e.getShaderParameter(this.shader,e.COMPILE_STATUS)||(0,n.default)("An error occurred compiling the shaders: "+e.getShaderInfoLog(this.shader))};var n=function(e){return e&&e.__esModule?e:{default:e}}(t(4))},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=function(e){return e&&e.__esModule?e:{default:e}}(t(3));let i=null;r.default=class{constructor(e,r,t){this.gl=e,this.size=r,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=t||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,r.w,r.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}fill(e,r){const t=this.gl;(0,n.default)(e.length>=this.size.w*this.size.h,"Texture size mismatch, data:"+e.length+", texture: "+this.size.w*this.size.h),t.bindTexture(t.TEXTURE_2D,this.texture),r?t.texSubImage2D(t.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,t.UNSIGNED_BYTE,e):t.texImage2D(t.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,t.UNSIGNED_BYTE,e)}bind(e,r,t){const n=this.gl;i||(i=[n.TEXTURE0,n.TEXTURE1,n.TEXTURE2]),n.activeTexture(i[e]),n.bindTexture(n.TEXTURE_2D,this.texture),n.uniform1i(n.getUniformLocation(r.program,t),e)}}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n=o(t(7)),i=o(t(4)),a=t(17),s=t(8);function o(e){return e&&e.__esModule?e:{default:e}}const f=n.default.createFromSource("x-shader/x-vertex","\n  attribute vec3 aVertexPosition;\n  attribute vec2 aTextureCoord;\n  uniform mat4 uMVMatrix;\n  uniform mat4 uPMatrix;\n  varying highp vec2 vTextureCoord;\n  void main(void) {\n    gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);\n    vTextureCoord = aTextureCoord;\n  }\n"),u=n.default.createFromSource("x-shader/x-fragment","\n  precision highp float;\n  varying highp vec2 vTextureCoord;\n  uniform sampler2D texture;\n  void main(void) {\n    gl_FragColor = texture2D(texture, vTextureCoord);\n  }\n");r.default=class{constructor(e,r,t){this.canvas=e,this.size=r,this.canvas.width=r.w,this.canvas.height=r.h,this.onInitWebGL(),this.onInitShaders(),this.initBuffers(),t&&this.initFramebuffer(),this.onInitTextures(),this.initScene()}initFramebuffer(){const e=this.gl;this.framebuffer=e.createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer),this.framebufferTexture=new Texture(this.gl,this.size,e.RGBA);const r=e.createRenderbuffer();e.bindRenderbuffer(e.RENDERBUFFER,r),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,this.size.w,this.size.h),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this.framebufferTexture.texture,0),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,r)}initBuffers(){let e;const r=this.gl;this.quadVPBuffer=r.createBuffer(),r.bindBuffer(r.ARRAY_BUFFER,this.quadVPBuffer),e=[1,1,0,-1,1,0,1,-1,0,-1,-1,0],r.bufferData(r.ARRAY_BUFFER,new Float32Array(e),r.STATIC_DRAW),this.quadVPBuffer.itemSize=3,this.quadVPBuffer.numItems=4,this.quadVTCBuffer=r.createBuffer(),r.bindBuffer(r.ARRAY_BUFFER,this.quadVTCBuffer),e=[1,0,0,0,1,1,0,1],r.bufferData(r.ARRAY_BUFFER,new Float32Array(e),r.STATIC_DRAW)}mvIdentity(){this.mvMatrix=s.Matrix.I(4)}mvMultiply(e){this.mvMatrix=this.mvMatrix.x(e)}mvTranslate(e){this.mvMultiply(s.Matrix.Translation($V([e[0],e[1],e[2]])).ensure4x4())}setMatrixUniforms(){this.program.setMatrixUniform("uPMatrix",new Float32Array(this.perspectiveMatrix.flatten())),this.program.setMatrixUniform("uMVMatrix",new Float32Array(this.mvMatrix.flatten()))}initScene(){const e=this.gl;this.perspectiveMatrix=(0,a.makePerspective)(45,1,.1,100),this.mvIdentity(),this.mvTranslate([0,0,-2.4]),e.bindBuffer(e.ARRAY_BUFFER,this.quadVPBuffer),e.vertexAttribPointer(this.vertexPositionAttribute,3,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,this.quadVTCBuffer),e.vertexAttribPointer(this.textureCoordAttribute,2,e.FLOAT,!1,0,0),this.onInitSceneTextures(),this.setMatrixUniforms(),this.framebuffer&&(console.log("Bound Frame Buffer"),e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer))}toString(){return"WebGLCanvas Size: "+this.size}checkLastError(e){const r=this.gl.getError();if(r!==this.gl.NO_ERROR){let t=this.glNames[r];t=void 0!==t?t+"("+r+")":"Unknown WebGL ENUM (0x"+value.toString(16)+")",e?console.log("WebGL Error: %s, %s",e,t):console.log("WebGL Error: %s",t),console.trace()}}onInitWebGL(){try{this.gl=this.canvas.getContext("experimental-webgl")}catch(e){console.warn("failed to initGL",e)}if(this.gl||(0,i.default)("Unable to initialize WebGL. Your browser may not support it."),!this.glNames){this.glNames={};for(const e in this.gl)"number"==typeof this.gl[e]&&(this.glNames[this.gl[e]]=e)}}onInitShaders(){this.program=new Program(this.gl),this.program.attach(new Shader(this.gl,f)),this.program.attach(new Shader(this.gl,u)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)}onInitTextures(){const e=this.gl;this.texture=new Texture(e,this.size,e.RGBA)}onInitSceneTextures(){this.texture.bind(0,this.program,"texture")}drawScene(){this.gl,this.gl.canvas,this.gl.viewport(0,0,this.gl.drawingBufferWidth,this.gl.drawingBufferHeight),this.gl.drawArrays(this.gl.TRIANGLE_STRIP,0,4)}readPixels(e){const r=this.gl;r.readPixels(0,0,this.size.w,this.size.h,r.RGBA,r.UNSIGNED_BYTE,e)}}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.makePerspective=function(e,r,t,n){const i=t*Math.tan(e*Math.PI/360),s=-i;return a(s*r,i*r,s,i,t,n)},r.makeFrustum=a;var n=t(8);const i=n.Matrix.create;function a(e,r,t,n,a,s){return i([[2*a/(r-e),0,(r+e)/(r-e),0],[0,2*a/(n-t),(n+t)/(n-t),0],[0,0,-(s+a)/(s-a),-2*s*a/(s-a)],[0,0,-1,0]])}n.Matrix.Translation=function(e){if(2===e.elements.length){const r=n.Matrix.I(3);return r.elements[2][0]=e.elements[0],r.elements[2][1]=e.elements[1],r}if(3===e.elements.length){const r=n.Matrix.I(4);return r.elements[0][3]=e.elements[0],r.elements[1][3]=e.elements[1],r.elements[2][3]=e.elements[2],r}throw new Error("Invalid length for Translation")},n.Matrix.prototype.flatten=function(){const e=[];if(0===this.elements.length)return[];for(let r=0;r<this.elements[0].length;r++)for(let t=0;t<this.elements.length;t++)e.push(this.elements[t][r]);return e},n.Matrix.prototype.ensure4x4=function(){if(4===this.elements.length&&4===this.elements[0].length)return this;if(this.elements.length>4||this.elements[0].length>4)return null;for(let e=0;e<this.elements.length;e++)for(let r=this.elements[e].length;r<4;r++)e===r?this.elements[e].push(1):this.elements[e].push(0);for(let e=this.elements.length;e<4;e++)0===e?this.elements.push([1,0,0,0]):1===e?this.elements.push([0,1,0,0]):2===e?this.elements.push([0,0,1,0]):3===e&&this.elements.push([0,0,0,1]);return this},n.Vector.prototype.flatten=function(){return this.elements}},function(e,r,t){"use strict"},function(e,r,t){"use strict";var n=t(5),i=t(0);n.Segment=function(){},n.Segment.prototype={eql:function(e){return this.start.eql(e.start)&&this.end.eql(e.end)||this.start.eql(e.end)&&this.end.eql(e.start)},dup:function(){return n.Segment.create(this.start,this.end)},length:function(){var e=this.start.elements,r=this.end.elements,t=r[0]-e[0],n=r[1]-e[1],i=r[2]-e[2];return Math.sqrt(t*t+n*n+i*i)},toVector:function(){var e=this.start.elements,r=this.end.elements;return i.create([r[0]-e[0],r[1]-e[1],r[2]-e[2]])},midpoint:function(){var e=this.start.elements,r=this.end.elements;return i.create([(r[0]+e[0])/2,(r[1]+e[1])/2,(r[2]+e[2])/2])},bisectingPlane:function(){return Plane.create(this.midpoint(),this.toVector())},translate:function(e){var r=e.elements||e,t=this.start.elements,i=this.end.elements;return n.Segment.create([t[0]+r[0],t[1]+r[1],t[2]+(r[2]||0)],[i[0]+r[0],i[1]+r[1],i[2]+(r[2]||0)])},isParallelTo:function(e){return this.line.isParallelTo(e)},distanceFrom:function(e){var r=this.pointClosestTo(e);return null===r?null:r.distanceFrom(e)},contains:function(e){if(e.start&&e.end)return this.contains(e.start)&&this.contains(e.end);var r=(e.elements||e).slice();if(2==r.length&&r.push(0),this.start.eql(r))return!0;var t=this.start.elements,n=i.create([t[0]-r[0],t[1]-r[1],t[2]-(r[2]||0)]),a=this.toVector();return n.isAntiparallelTo(a)&&n.modulus()<=a.modulus()},intersects:function(e){return null!==this.intersectionWith(e)},intersectionWith:function(e){if(!this.line.intersects(e))return null;var r=this.line.intersectionWith(e);return this.contains(r)?r:null},pointClosestTo:function(e){if(e.normal){var r=this.line.intersectionWith(e);return null===r?null:this.pointClosestTo(r)}var t=this.line.pointClosestTo(e);return null===t?null:this.contains(t)?t:(this.line.positionOf(t)<0?this.start:this.end).dup()},setPoints:function(e,r){return e=i.create(e).to3D(),r=i.create(r).to3D(),null===e||null===r?null:(this.line=n.create(e,r.subtract(e)),this.start=e,this.end=r,this)}},n.Segment.create=function(e,r){return(new n.Segment).setPoints(e,r)},e.exports=n.Segment},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});r.default=class{constructor(){this.decode=((e,r,t)=>{if(!e)return;const n=r*t,i=n>>2,a=e.subarray(0,n),s=e.subarray(n,n+i),o=e.subarray(n+i,n+2*i);for(let e=0;e<t;e++)for(let t=0;t<r;t++){const n=t+e*r,i=~~(e/2)*~~(r/2)+~~(t/2),f=~~(e/2)*~~(r/2)+~~(t/2),u=1.164*(a[n]-16)+1.596*(o[f]-128),l=1.164*(a[n]-16)-.813*(o[f]-128)-.391*(s[i]-128),c=1.164*(a[n]-16)+2.018*(s[i]-128),h=4*n;this.canvasBuffer.data[h+0]=u,this.canvasBuffer.data[h+1]=l,this.canvasBuffer.data[h+2]=c,this.canvasBuffer.data[h+3]=255}this.canvasCtx.putImageData(this.canvasBuffer,0,0)})}initialize(e,r){this.canvas=e,this.canvasCtx=this.canvas.getContext("2d"),this.canvasBuffer=this.canvasCtx.createImageData(r.w,r.h)}}},function(e,r,t){"use strict";Object.defineProperty(r,"__esModule",{value:!0});class n{constructor(e,r){this.w=e,this.h=r}toString(){return"("+this.w+", "+this.h+")"}getHalfSize(){return new n(this.w>>>1,this.h>>>1)}length(){return this.w*this.h}}r.default=n},function(e,r,t){"use strict";function n(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function i(e){return"function"==typeof e}function a(e){return"object"==typeof e&&null!==e}function s(e){return void 0===e}e.exports=n,n.EventEmitter=n,n.prototype._events=void 0,n.prototype._maxListeners=void 0,n.defaultMaxListeners=10,n.prototype.setMaxListeners=function(e){if(!function(e){return"number"==typeof e}(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},n.prototype.emit=function(e){var r,t,n,o,f,u;if(this._events||(this._events={}),"error"===e&&(!this._events.error||a(this._events.error)&&!this._events.error.length)){if((r=arguments[1])instanceof Error)throw r;var l=new Error('Uncaught, unspecified "error" event. ('+r+")");throw l.context=r,l}if(s(t=this._events[e]))return!1;if(i(t))switch(arguments.length){case 1:t.call(this);break;case 2:t.call(this,arguments[1]);break;case 3:t.call(this,arguments[1],arguments[2]);break;default:o=Array.prototype.slice.call(arguments,1),t.apply(this,o)}else if(a(t))for(o=Array.prototype.slice.call(arguments,1),n=(u=t.slice()).length,f=0;f<n;f++)u[f].apply(this,o);return!0},n.prototype.addListener=function(e,r){var t;if(!i(r))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,i(r.listener)?r.listener:r),this._events[e]?a(this._events[e])?this._events[e].push(r):this._events[e]=[this._events[e],r]:this._events[e]=r,a(this._events[e])&&!this._events[e].warned&&(t=s(this._maxListeners)?n.defaultMaxListeners:this._maxListeners)&&t>0&&this._events[e].length>t&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace()),this},n.prototype.on=n.prototype.addListener,n.prototype.once=function(e,r){if(!i(r))throw TypeError("listener must be a function");var t=!1;function n(){this.removeListener(e,n),t||(t=!0,r.apply(this,arguments))}return n.listener=r,this.on(e,n),this},n.prototype.removeListener=function(e,r){var t,n,s,o;if(!i(r))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(s=(t=this._events[e]).length,n=-1,t===r||i(t.listener)&&t.listener===r)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,r);else if(a(t)){for(o=s;o-- >0;)if(t[o]===r||t[o].listener&&t[o].listener===r){n=o;break}if(n<0)return this;1===t.length?(t.length=0,delete this._events[e]):t.splice(n,1),this._events.removeListener&&this.emit("removeListener",e,r)}return this},n.prototype.removeAllListeners=function(e){var r,t;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(r in this._events)"removeListener"!==r&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events={},this}if(i(t=this._events[e]))this.removeListener(e,t);else if(t)for(;t.length;)this.removeListener(e,t[t.length-1]);return delete this._events[e],this},n.prototype.listeners=function(e){return this._events&&this._events[e]?i(this._events[e])?[this._events[e]]:this._events[e].slice():[]},n.prototype.listenerCount=function(e){if(this._events){var r=this._events[e];if(i(r))return 1;if(r)return r.length}return 0},n.listenerCount=function(e,r){return e.listenerCount(r)}},function(e,r,t){"use strict";(function(n){function i(){var e;try{e=r.storage.debug}catch(e){}return!e&&void 0!==n&&"env"in n&&(e=n.env.DEBUG),e}(r=e.exports=t(24)).log=function(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},r.formatArgs=function(e){var t=this.useColors;if(e[0]=(t?"%c":"")+this.namespace+(t?" %c":" ")+e[0]+(t?"%c ":" ")+"+"+r.humanize(this.diff),!t)return;var n="color: "+this.color;e.splice(1,0,n,"color: inherit");var i=0,a=0;e[0].replace(/%[a-zA-Z%]/g,function(e){"%%"!==e&&"%c"===e&&(a=++i)}),e.splice(a,0,n)},r.save=function(e){try{null==e?r.storage.removeItem("debug"):r.storage.debug=e}catch(e){}},r.load=i,r.useColors=function(){if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)return!0;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},r.storage="undefined"!=typeof chrome&&void 0!==chrome.storage?chrome.storage.local:function(){try{return window.localStorage}catch(e){}}(),r.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],r.formatters.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}},r.enable(i())}).call(r,t(6))},function(e,r,t){"use strict";var n;function i(e){function t(){if(t.enabled){var e=t,i=+new Date,a=i-(n||i);e.diff=a,e.prev=n,e.curr=i,n=i;for(var s=new Array(arguments.length),o=0;o<s.length;o++)s[o]=arguments[o];s[0]=r.coerce(s[0]),"string"!=typeof s[0]&&s.unshift("%O");var f=0;s[0]=s[0].replace(/%([a-zA-Z%])/g,function(t,n){if("%%"===t)return t;f++;var i=r.formatters[n];if("function"==typeof i){var a=s[f];t=i.call(e,a),s.splice(f,1),f--}return t}),r.formatArgs.call(e,s),(t.log||r.log||console.log.bind(console)).apply(e,s)}}return t.namespace=e,t.enabled=r.enabled(e),t.useColors=r.useColors(),t.color=function(e){var t,n=0;for(t in e)n=(n<<5)-n+e.charCodeAt(t),n|=0;return r.colors[Math.abs(n)%r.colors.length]}(e),"function"==typeof r.init&&r.init(t),t}(r=e.exports=i.debug=i.default=i).coerce=function(e){return e instanceof Error?e.stack||e.message:e},r.disable=function(){r.enable("")},r.enable=function(e){r.save(e),r.names=[],r.skips=[];for(var t=("string"==typeof e?e:"").split(/[\s,]+/),n=t.length,i=0;i<n;i++)t[i]&&("-"===(e=t[i].replace(/\*/g,".*?"))[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")))},r.enabled=function(e){var t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1},r.humanize=t(25),r.names=[],r.skips=[],r.formatters={}},function(e,r,t){"use strict";var n=1e3,i=60*n,a=60*i,s=24*a,o=365.25*s;function f(e,r,t){if(!(e<r))return e<1.5*r?Math.floor(e/r)+" "+t:Math.ceil(e/r)+" "+t+"s"}e.exports=function(e,r){r=r||{};var t=typeof e;if("string"===t&&e.length>0)return function(e){if((e=String(e)).length>100)return;var r=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(e);if(!r)return;var t=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return t*o;case"days":case"day":case"d":return t*s;case"hours":case"hour":case"hrs":case"hr":case"h":return t*a;case"minutes":case"minute":case"mins":case"min":case"m":return t*i;case"seconds":case"second":case"secs":case"sec":case"s":return t*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}(e);if("number"===t&&!1===isNaN(e))return r.long?function(e){return f(e,s,"day")||f(e,a,"hour")||f(e,i,"minute")||f(e,n,"second")||e+" ms"}(e):function(e){if(e>=s)return Math.round(e/s)+"d";if(e>=a)return Math.round(e/a)+"h";if(e>=i)return Math.round(e/i)+"m";if(e>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}}])});