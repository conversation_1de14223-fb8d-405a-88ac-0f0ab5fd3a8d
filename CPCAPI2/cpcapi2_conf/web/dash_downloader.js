function Downloader(_streamId) {
	this.isActive = false;
	this.realtime = false;
	this.chunkStart = 0;
	this.chunkSize = 0;
	this.totalLength = 0;
	this.chunkTimeout = 1000;
	this.url = null;
	this.callback = null;
	this.eof = false;
	this.setDownloadTimeoutCallback = null;
	this.exampleSocket = null;
	this.nextSeqNum = 1;
	this.decodeTimeOffset = new ArrayBuffer(8);
	this.decodeTimeOffset32 = 0;
	this.scratch = {};
	this.streamId = _streamId;
}

Downloader.prototype.setDownloadTimeoutCallback = function(callback) {
	this.setDownloadTimeoutCallback = callback;
	return this;
}

Downloader.prototype.reset = function() {
	this.chunkStart = 0;
	this.totalLength = 0;
	this.eof = false;
	return this;
}

Downloader.prototype.setRealTime = function(_realtime) {
	this.realtime = _realtime;
	return this;
}

Downloader.prototype.setChunkSize = function(_size) {
	this.chunkSize = _size;
	return this;
}

Downloader.prototype.setChunkStart = function(_start) {
	this.chunkStart = _start;
	this.eof = false;
	return this;
}

Downloader.prototype.setInterval = function(_timeout) {
	this.chunkTimeout = _timeout;
	return this;
}

Downloader.prototype.setUrl = function(_url) {
	this.url = _url;
	return this;
}

Downloader.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

Downloader.prototype.isStopped = function () {
	return !this.isActive;
}

Downloader.prototype.getFileLength = function () {
	return this.totalLength;
}

Downloader.prototype.getFile = function() {
    return;
}

function makeWsUrl(port) {
    var l = window.location;
    return ((l.protocol === "https:") ? "wss://" : "ws://") + l.hostname + ":" + port;
}

Downloader.prototype.start = function() {
	//Log.info("Downloader", "Starting file download");
    var dl = this;
	this.chunkStart = 0;
    this.exampleSocket = new WebSocket(makeWsUrl(9005));
	this.exampleSocket.binaryType = "arraybuffer";
    this.exampleSocket.onopen = function (event) {
        // log something
		//Log.info("Downloader", "Connected to WebSocket server");
		var videoStreamObj = {};
		videoStreamObj.videoStream = dl.streamId;
		dl.exampleSocket.send(JSON.stringify(videoStreamObj));
    };	
    this.exampleSocket.onmessage = function (event) {
        //Log.info("Downloader", "Received data from WebSocket server (" + event.data.byteLength + " bytes)");
		var mp4data = event.data.slice(3); // !jjg! TODO: horribly inefficient alloc for each frame of video here!
		var hdr8 = new Uint8Array(event.data, 0, 3);
		dl.scratch.magicbyte0 = hdr8[0];
		dl.scratch.magicbyte1 = hdr8[1];
		dl.scratch.magicbyte2 = hdr8[2];
		var mp4box = new Uint8Array(mp4data);
		
		// moof
		// bytes 4, 5, 6, 7 have values 6d 6f 6f 66
		if (mp4box[4] == 0x6d &&
		    mp4box[5] == 0x6f &&
			mp4box[6] == 0x6f &&
			mp4box[7] == 0x66)
		{
			var iTfdt = 0;
			// locate 'tfdt' box
			for (; iTfdt<mp4box.length-3; iTfdt++) {
				if (mp4box[iTfdt] == 0x74 &&
				    mp4box[iTfdt+1] == 0x66 &&
					mp4box[iTfdt+2] == 0x64 &&
					mp4box[iTfdt+3] == 0x74) {
					break;
				}
			}
			// decode time is only 4 bytes if we are using version '0' (which apparently Shaka Packager does)
			var decodeTime32 = new DataView(mp4data, iTfdt+8, 4); // should be byte 76
			
			if (dl.decodeTimeOffset32 == 0) {
				dl.decodeTimeOffset32 = decodeTime32.getUint32(0);
				decodeTime32.setUint32(0, 0);
			}
			else {
				var thisSampleDecodeTime = decodeTime32.getUint32(0) - dl.decodeTimeOffset32;
				decodeTime32.setUint32(0, thisSampleDecodeTime);
				//console.info("dash_downloader", "got sample with decode time " + thisSampleDecodeTime);
			}
					
			// sequence number is 4 bytes, starting with byte 20 (0-based)
			var iMfhd = 0;
			// locate 'mfhd' box
			for (; iMfhd<mp4box.length-3; iMfhd++) {
				if (mp4box[iMfhd] == 0x6d &&
				    mp4box[iMfhd+1] == 0x66 &&
					mp4box[iMfhd+2] == 0x68 &&
					mp4box[iMfhd+3] == 0x64) {
					break;
				}
			}
			var seqNumArr = new DataView(mp4data, iMfhd+8, 4); // should be byte 20
			seqNumArr.setUint32(0, dl.nextSeqNum);
			dl.nextSeqNum = dl.nextSeqNum + 1;
		}
		dl.scratch.mp4data = mp4data;
		dl.callback(dl.scratch, false);
    }	
	this.isActive = true;
	//this.resume();
	return this;
}

Downloader.prototype.resume = function() {
	//Log.info("Downloader", "Resuming file download");
	this.isActive = true;
	if (this.chunkSize === 0) {
		this.chunkSize = Infinity;
	}
	//this.getFile();
	return this;
}

Downloader.prototype.stop = function() {
	//Log.info("Downloader", "Stopping file download");
	this.isActive = false;
	if (this.timeoutID) {
		window.clearTimeout(this.timeoutID);
		delete this.timeoutID;
	}
	if (this.exampleSocket) {
		this.exampleSocket.close();
		this.exampleSocket = null;
	}
	return this;
}
