<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>CounterPath SDK in the Cloud</title>
    <!-- Include basic JQuery and JQuery UI files -->
    <script src="lib/jquery/jquery.js"></script>
    <link href="lib/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css">
    <script src="lib/jquery-ui/jquery-ui.min.js"></script>
	<script src="sha256.js"></script>

    <link rel="icon" href="favicon.ico" type="image/x-icon" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="wrapper" id="app">

	<div v-if="showConfJoinUrl === true">
	<input type="text" v-model="conferenceUrl" />
	<button @click="submitLoginNoDownload()" data-toggle="tooltip" title="Join">
						Join
					</button>
	</div>


		<!-- Video Canvas -->
		<div class="videoContainer" id="videoContainerDiv">
			<!-- canvas id="img1" width="1922" height="1080"></canvas -->
		</div>
	
	</div>

    <!-- Bootstrap Js CDN -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    <!-- jQuery Nicescroll CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.nicescroll/3.6.8-fix/jquery.nicescroll.min.js"></script>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#sidebar").niceScroll({
                cursorcolor: '#FC6830',
                cursorwidth: 3,
                cursorborder: 'none'
            });

            $('#sidebarCollapse').on('click', function () {
                $('#sidebar, #content').toggleClass('active');
                $('.collapse.in').toggleClass('in');
                $('a[aria-expanded=true]').attr('aria-expanded', 'false');
            });

            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>

    <script src="lib/vue/vue.js"></script>
    <script src="lib/ws-avc-player/WSAvcPlayer.js?v=10"></script>
	
	<!-- script src="dash_downloader.js?v=9"></script -->
    <script src="sdkconnector.js?v=10"></script>
    <script src="call.js?v=10"></script>
	<script src="confconnector.js?v=10"></script>
	<script src="confconnector_remote.js?v=10"></script>
    <script src="confbridge.js?v=10"></script>
    <script src="peerconnection.js?v=10"></script>
    <script src="audio.js?v=10"></script>
    <script src="video.js?v=10"></script>
    <script src="index.js?v=10"></script>
    <!-- script src="img_replace.js?v=8"></script -->
	<!-- script src="text.min.js?v=10"></script -->
	
    <script src="app.js?v=10"></script>
	
	<script type="text/javascript">
	if (app.showConfJoinUrl === false) {
		app.submitLoginNoDownload();
	}
	   
		app.$on("onAppConferenceListPopulated", function () {
			app.selectConferenceForJoin(app.conferences[0]);
		});	   
	</script>

</body>

</html>