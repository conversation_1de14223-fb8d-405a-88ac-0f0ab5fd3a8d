function VideoManager() {
	this.callback = null;
	this.screenshareDevListCallback = null;
	this.sdkConnector = null;
}

VideoManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

VideoManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	var setHandlerFunc = {};
	setHandlerFunc.moduleId = "VideoJsonApi";
	setHandlerFunc.functionObject = {};
	setHandlerFunc.functionObject.functionName = "setHandler";
	this.sdkConnector.send(JSON.stringify(setHandlerFunc));
	return this;
}

VideoManager.prototype.queryDeviceList = function() {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryDeviceList";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.setCaptureDevice = function(_deviceId) {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setCaptureDevice";
	fn.functionObject.deviceId = _deviceId;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.startCapture = function() {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "startCapture";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.stopCapture = function() {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "stopCapture";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.startScreenshare = function() {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "startScreenshare";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.stopScreenshare = function() {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "stopScreenshare";
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.queryScreenshareDeviceList = function(_callback, _includeMonitors, _includeWindows) {
	this.screenshareDevListCallback = _callback;
	
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryScreenshareDeviceList";
	fn.functionObject.includeMonitors = _includeMonitors;
	fn.functionObject.includeWindows = _includeWindows;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.setScreenshareCaptureDevice = function(_deviceId) {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setScreenshareCaptureDevice";
	fn.functionObject.deviceId = _deviceId;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.setVideoMute = function(_enabled) {
	var fn = {};
	fn.moduleId = "VideoJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setVideoMute";
	fn.functionObject.enabled = _enabled;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

VideoManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "VideoJsonProxy") {
		if (_jsonEventObj.functionObject.functionName == "onScreenshareDeviceList") {
			if (this.screenshareDevListCallback) {
				var cb = this.screenshareDevListCallback;
				this.screenshareDevListCallback = null;
				cb(_jsonEventObj.functionObject.devices);
			}
		}
		else {
			this.callback(_jsonEventObj.functionObject);
		}
	}
	return this;
}
