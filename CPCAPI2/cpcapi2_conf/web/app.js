
var ConferenceState = Object.freeze({
	IDLE: "IDLE",
	AUTHENTICATED: "AUTHENTICATED",
	CONNECTED: "CONNECTED",
	CREATING: "CREATING",
	JOINING: "JOINING",
	JOINED: "JOINED"
});

var app = new Vue({
	el: '#app',
	data: {
		/* visibility triggers */
		showPluginDownload: true,
		browserOnly: true,
		conferenceState: ConferenceState.IDLE,

		canStartScreenShare: false,
		canStopScreenShare: false,
		canStartTranscription: false,
		canStopTranscription: false,
		canAddParticipant: false,
		addingParticipant: false,
		startingScreenshareFromAVConference: false,
		showConfCreateControls: false,
		showConfJoinUrl: (window.location.protocol === 'file:'),
		doAutoJoin: false,
		confType: '',
		userFromAuthToken: '',
		userContextMatches: true,
		
		isLocalMuted: false,
		isLocalVideoMuted: false,

		participants: [],

		/* conferences */
		conferences: [],
		conferenceId: '',
		screenshareId: '',
		activeConference: null,
		confSelectedForJoin: { conferenceType: '' },

		/* input fields */
		conferenceBridge: "",
		username: "counterpath_guest",
		password: "password",
		confAction: "",
		displayName: localStorage.getItem("displayName"),
		conferenceUrl: window.location.href,
		thisUserPhoto: new ArrayBuffer(),
		confSelection: "none",
		cameras: [{ friendlyName: "Camera - System Default", id: "cpc_sys_default" }],
		cameraSelection: "cpc_sys_default",
		microphones: [{ friendlyName: "Microphone - System Default", id: "cpc_sys_default" }],
		micSelection: "cpc_sys_default",
		speakers: [{ friendlyName: "Speaker - System Default", id: "cpc_sys_default" }],
		speakerSelection: "cpc_sys_default",
		monitors: [{ deviceDescription: "Default Monitor", deviceId: 0 }],
		monitorSelection: "cpc_sys_default",
		uriToAdd: "",
		confBridgeCreateLabel: "conference1",

	},
	mounted() {
		var usernameFromQS = this.getParameterByName('username', window.location.href);
		if (usernameFromQS != null) {
			this.username = usernameFromQS;
		}
		var passwordFromQS = this.getParameterByName('password', window.location.href);
		if (passwordFromQS != null) {
			this.password = passwordFromQS;
		}
		
		var localSdkConnConfig = {};
		localSdkConnConfig.url = "wss://cpclientapi.softphone.com:2114";
		localSdkConnector.start(localSdkConnConfig);	
	},
	computed: {
		canLocalMute: function() {
			return this.conferenceId != '' && !this.isLocalMuted;
		},
		canLocalUnMute: function() {
			return this.conferenceId != '' && this.isLocalMuted;
		},
		canLocalPauseVideo: function() {
			if (this.browserOnly === true) {
				return false;
			}
			var pauseAllowed = isScreenshareOwner;
			return pauseAllowed && !this.isLocalVideoMuted;
		},
		canLocalResumeVideo: function() {
			if (this.browserOnly === true) {
				return false;
			}			
			var resumeAllowed = isScreenshareOwner;
			return resumeAllowed && this.isLocalVideoMuted;
		},		
		showConferenceToggles: function() {
			return this.conferenceId != '' && this.screenshareId != '';
		},
	},
	methods: {
		onFileChange(e) {
			var files = e.target.files || e.dataTransfer.files;
			if (!files.length)
				return;
			this.createImage(files[0]);
		},
		createImage(file) {
			var image = new Image();
			var reader = new FileReader();
			var vm = this;

			reader.onload = (e) => {
				vm.thisUserPhoto = e.target.result;
			};
			reader.readAsArrayBuffer(file);
		},		
		getParameterByName: function(name, url) {
			name = name.replace(/[\[\]]/g, "\\$&");
			var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
				results = regex.exec(url);
			if (!results) return null;
			if (!results[2]) return '';
			return decodeURIComponent(results[2].replace(/\+/g, " "));
		},
		submitLogin: function () {
			this.browserOnly = false;
			localStorage.setItem("username", this.username);
			localStorage.setItem("password", this.password);
			localStorage.setItem("displayName", this.displayName);
			doAuth(this.username, this.password, this.conferenceUrl, true);
		},
		submitLoginNoDownload: function () {
			this.browserOnly = true;
			localStorage.setItem("username", this.username);
			localStorage.setItem("password", this.password);
			localStorage.setItem("displayName", this.displayName);
			doAuth(this.username, this.password, this.conferenceUrl, false);
		},		
		selectConferenceForJoin: function(appConf) {
			if (appConf) {
				this.confSelectedForJoin = appConf;
				if (appConf.conferenceType === 0) {
					var thisvm = this;
					if (appConf.confSessionHandle) {
						thisvm.rejoinConference();
					}
					else {
					thisvm.submitConferenceDetails();
				}
			}
			}
		},
		rejoinConference: function() {
			this.conferenceBridge = this.confSelectedForJoin.conferenceHandle;
			if (this.browserOnly === true) {
				app.conferenceState = ConferenceState.JOINED;
			}
			else {
			}
			doRejoinConferenceSession(this.confSelectedForJoin.conferenceHandle, this.confSelectedForJoin.confSessionHandle, app.displayName, this.confSelectedForJoin.conferenceType);
		},
		submitConferenceDetails: function() {
			this.conferenceBridge = this.confSelectedForJoin.conferenceHandle;
			if (this.browserOnly === true) {
				doCreateConferenceSession(false, this.confSelectedForJoin.conferenceHandle, app.displayName, this.confSelectedForJoin.conferenceType);
				app.conferenceState = ConferenceState.JOINED;
			}
			else {
				if (this.confSelectedForJoin.conferenceType > 0) {
					configureDevices(this.displayName, this.micSelection, this.speakerSelection, this.cameraSelection);
				}
				doCreateConferenceSession(true, this.confSelectedForJoin.conferenceHandle, app.displayName, this.confSelectedForJoin.conferenceType);
			}
		},
		submitConferenceCreate: function(confLabel, conferenceType) {
			this.startingScreenshareFromAVConference = false;
			if (this.displayName === '') {
				this.displayName = "anonymous"
			}
			app.conferenceState = ConferenceState.CREATING;
			app.confType = conferenceType;
			setMyPhoto(this.thisUserPhoto);
			
			if (conferenceType === "conftypescr") {
				configureMonitorSelection(this.displayName, this.monitorSelection);
				doCreateScreenshareConference(confLabel);
			}
			else if (conferenceType === "conftypefull") {
				configureDevices(this.displayName, this.micSelection, this.speakerSelection, this.cameraSelection);            
				doCreateAVConference(confLabel);
			}
		},
		setCanCreateConferencePermissionApp: function(participant, permissionEnabled) {
			setCanCreateConferencePermissionSdk(this.activeConference.confSessionHandle, participant, permissionEnabled);
		},
		doTakeSnapshot: function (participant) {
			doTakeSnapshot(participant);
		},
		doLocalMicMute: function (isMuted) {
			this.isLocalMuted = isMuted;
			doLocalMicMuteSdk(isMuted);
		},
		doLocalVideoMute: function (isMuted) {
			this.isLocalVideoMuted = isMuted;
			doLocalVideoMuteSdk(isMuted);
		},		
		doAddParticipantInput: function () {
			this.addingParticipant = true;
		},
		doAddParticipant: function () {
			this.addingParticipant = false;
			makeCall(this.uriToAdd);
		},
		doStartScreenshareInput: function () {
			//this.canStopScreenShare = true;
			this.canStartScreenShare = false;
			this.startingScreenshareFromAVConference = true;
			this.confBridgeCreateLabel = "screenshare1";
		},
		endCall: function (conversation) {
			endCall(conversation);
		},
		endPeerConnection: function (participant) {
			endPeerConnection(participant);
		},
		muteAudio: function (conversation) {
			muteAudio(conversation);
		},
		unmuteAudio: function (conversation) {
			unmuteAudio(conversation);
		},
		muteVideo: function (conversation) {
			muteVideo(conversation);
		},
		unmuteVideo: function (conversation) {
			unmuteVideo(conversation);
		},
		addToFloor: function (participant) {
			addToFloor(participant);
		},
		removeFromFloor: function (participant) {
			removeFromFloor(participant);
		},
		switchToConference: function (conf) {
			if (conf.confSessionHandle) {
				if (conf.conferenceType > 0) {
					doSwitchToAVAsParticipant(conf.confSessionHandle);
				}
				else {
					doSwitchToScreenshareAsParticipant(conf.confSessionHandle);
				}
			}
			else {
				this.confSelectedForJoin = conf;
				this.conferenceBridge = this.confSelectedForJoin.conferenceHandle;
				if (this.browserOnly === false && this.confSelectedForJoin.conferenceType > 0) {
					configureDevices(this.displayName, this.micSelection, this.speakerSelection, this.cameraSelection);
				}
				doCreateConferenceSession(true, this.confSelectedForJoin.conferenceHandle, app.displayName, this.confSelectedForJoin.conferenceType);
			}
			
			this.conferences.forEach(function(appConf) {
				if (appConf.conferenceHandle === conf.conferenceHandle) {
					this.activeConference = appConf;
					this.participants = appConf.participants;
				}
			});
		},
		closeConference: function (conf) {
			if (this.browserOnly === true) {
			}
			else {
				doCloseConferenceSession(conf.confSessionHandle);
			}
		},
		stopScreenShare: function() {
			this.canStopScreenShare = false;
			//doDestroyScreenshareConference();
		},
		startTranscription: function() {
			doStartTranscription();
		},
		stopTranscription: function() {
			doStopTranscription();
		},
		copyScreenshareLink: function() {
			var pluginPartLink = getBaseURL() + '?confUri=' + this.conferenceBridge;
			var browserOnlyPartLink = getBaseURL() + '?browserOnly=true&confUri=' + this.conferenceBridge;
			var linkText = "Join via the web using the CounterPath webclient (full duplex, best quality): " + pluginPartLink + " \n" + "View via the web (no download required): " + browserOnlyPartLink;
			copyToClipboard(linkText);
		},
	}
});


app.$on('onServiceConnectionStatusChanged', function (connStatus, authTokenPubClaims) {
	
	if (authTokenPubClaims) {
		app.userFromAuthToken = authTokenPubClaims.cp_user;
		app.userContextMatches = (window.location.href.indexOf(app.userFromAuthToken) >= 0);
		if (window.location.protocol === "file:") {
			app.userContextMatches = true;
		}
	}
	/*
	   ServiceConnectionStatus_Disconnecting = 0,
	   ServiceConnectionStatus_Disconnected = 1,
	   ServiceConnectionStatus_Connecting = 2,
	   ServiceConnectionStatus_Authenticating = 3,
	   ServiceConnectionStatus_Connected = 4,
	   ServiceConnectionStatus_ConnFailure = 5,
	   ServiceConnectionStatus_AuthFailure = 6
	*/
	if (connStatus == 2) {
		app.conferenceState = ConferenceState.JOINING;
	}
	else if (connStatus == 4) {
		app.conferenceState = ConferenceState.AUTHENTICATED;
		doQueryConferenceList();
	}
	else if (connStatus == 5 || connStatus == 6) {
		app.conferenceState = ConferenceState.IDLE;
	}
});

app.$on("onConferenceListUpdated", function (confList) {
	var newConfList = [];
	
	confList.forEach(function(confDetailsRes) {
		//console.info("index", "conference: " + confDetailsRes.conferenceInfo.label);
		var appConf = {};
		appConf.participants = [];
		appConf.label = confDetailsRes.displayName;
		if (confDetailsRes.numParticipants) {
			appConf.numParticipants = confDetailsRes.numParticipants;
		}
		else {
			appConf.numParticipants = 0;
		}
		appConf.conferenceHandle = confDetailsRes.conference;
		if (confDetailsRes.streamId) {
			appConf.streamId = confDetailsRes.streamId;
		}
		appConf.conferenceType = confDetailsRes.conferenceType;
		if (app.browserOnly === false || confDetailsRes.streamId != -1) {
		    newConfList.push(appConf);
		}
	});	
	
	newConfList.forEach(function(ncli) {
		app.conferences.forEach(function(excli) {
			if (ncli.conferenceHandle === excli.conferenceHandle) {
				ncli.confSessionHandle = excli.confSessionHandle;
				ncli.participants = excli.participants;
			}
		});
	});
	app.conferences.length = 0;
	app.conferences = newConfList;
	
	if (app.conferenceState === ConferenceState.JOINED) {
	}
	
	app.$emit("onAppConferenceListPopulated");
});

app.$on("onConferenceSessionStatusChanged", function (confHandle, sessStatus, permissions, videoStreamId) {
	/*
	SessionStatus_NotConnected,
	SessionStatus_Connecting,
	SessionStatus_Connected,
	SessionStatus_ConnectionFailed
	*/
	if (sessStatus == 2) {
		app.conferenceState = ConferenceState.JOINED;
		app.canStartScreenShare = permissions.canCreateConference;

		app.conferences.forEach(function(appConf) {
			if (appConf.conferenceHandle === confHandle) {
				app.activeConference = appConf;
				app.participants = appConf.participants;
			}
		});
		bindWebsocketTochannel_remote(videoStreamId);
	}
	else if (sessStatus == 0 || sessStatus == 3) {
		//app.conferenceState = ConferenceState.IDLE;
	}
});

app.$on("onConferenceSessionStarted", function (confHandle, confSessionHandle) {
	var found = false;
	app.conferences.forEach(function(appConf) {
		if (appConf.conferenceHandle === confHandle) {
			appConf.confSessionHandle = confSessionHandle;
			found = true;
		}
	});
	if (!found) {
		var tempAppConf = {};
		tempAppConf.confSessionHandle = confSessionHandle;
		app.conferences.push(tempAppConf);
	}
});

app.$on("onConferenceParticipantListUpdated", function (confHandle, partList, addedParticipants, updatedParticipants, removedParticipants) {
	app.conferences.forEach(function(appConf) {
		if (appConf.conferenceHandle === confHandle) {
			if (partList.length > 0) {
				// participants have changed, update the the entire list
				appConf.participants.length = 0;

				partList.forEach(function(partInfo) {
					var part = {};

					part.participant = partInfo.participant;
					//if (partInfo.snapshotFilenameUtf8.length > 0) {
					//    var d = new Date();
					//    var n = d.getTime();
					//    part.avatar = partInfo.snapshotFilenameUtf8 + "?refreshme=" + n;
					//}
					//else {
						part.avatar = "counterpath.png";
					//}
					
					part.displayName = partInfo.displayName;
					
					part.hasFloor = false;

					part.participantType = "Web";

					appConf.participants.push(part);
				});	
			}
			else
			{
				if (addedParticipants.length > 0) {
					addedParticipants.forEach(function(partInfo) {
						var part = {};

						part.participant = partInfo.participant;
						//if (partInfo.snapshotFilenameUtf8.length > 0) {
						//    var d = new Date();
						//    var n = d.getTime();
						//    part.avatar = partInfo.snapshotFilenameUtf8 + "?refreshme=" + n;
						//}
						//else {
							part.avatar = "counterpath.png";
						//}
						
						part.displayName = partInfo.displayName;
						
						part.hasFloor = false;

						part.participantType = "Web";

						appConf.participants.push(part);
					});
				}
				if (updatedParticipants.length > 0) {
					updatedParticipants.forEach(function(partInfo) {
						var part = {};

						part.participant = partInfo.participant;
						
						appConf.participants.forEach(function(existingPartInfo) {
							if (existingPartInfo.participant === partInfo.participant) {
								existingPartInfo.displayName = partInfo.displayName;
							}
						});
					});
				}
				if (removedParticipants.length > 0) {
					removedParticipants.forEach(function(partInfo) {
						var part = {};

						part.participant = partInfo.participant;
						
						var idx = appConf.participants.findIndex(function(existingPartInfo) {
							return (existingPartInfo.participant === partInfo.participant);
						});
						if (idx != -1) {
							appConf.participants.splice(idx, 1);
						}
					});
				}		
			}		
		}
	});



});

app.$on("onConferenceCreated", function(confHandle) {
	if (app.conferenceState === ConferenceState.CREATING) {
		app.conferenceState = ConferenceState.JOINING;
		if (app.confType === "conftypescr") {
			doCreateScreensharePresenterSession(confHandle, app.displayName);
		}
		else if (app.confType === "conftypefull") {
			doCreateAVHostSession(confHandle, app.displayName);
		}
	}
});

app.$on('onAudioDeviceListUpdated', function (micList, speakerList) {
	// update list of audio devices, this gets called soon after onLocalSdkConnected == true
	app.microphones = micList;
	app.speakers = speakerList;
});

app.$on('onVideoDeviceListUpdated', function (deviceList) {
	// update list of cameras, this gets called soon after onLocalSdkConnected == true
	app.cameras = deviceList;
});

app.$on('onScreenshareDeviceListUpdated', function (deviceList) {
	// update list of monitors, this gets called soon after onLocalSdkConnected == true
	app.monitors = deviceList;
});

app.$on('onLocalSdkLogin', function (jsonApiVersion) {
	app.showPluginDownload = (jsonApiVersion < 9);
	initCanvasForCpcWebclient();
});

app.$on('onLocalSdkConnected', function (connected) {
	// web page is connected to local sdk plugin
	// show/hide prompts to download + run the plugin!
	app.showPluginDownload = !connected;
});




