function getRandomIntConf(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

function ConferenceBridgeManager() {
	this.callback = null;
	this.confDetailsCallback = null;
	this.confListCallback = null;
	this.sdkConnector = null;
	this.wpHandle = getRandomIntConf(1, 65535);
	this.confHandle = getRandomIntConf(1, 65535);
	console.info("confbridge", "handle: " + this.wpHandle);
}

ConferenceBridgeManager.prototype.setSdkConnector = function(_sdkConnector) {
	this.sdkConnector = _sdkConnector;
	return this;
}

ConferenceBridgeManager.prototype.setCallback = function(_callback) {
	this.callback = _callback;
	return this;
}

ConferenceBridgeManager.prototype.makeConferenceSettings = function(_mixMode, _persistent, _adaptVideo, _label, _tags) {
	var ci = {};
	ci.mixMode = _mixMode;
	ci.persistent = _persistent;
	ci.adaptVideoCodecOnRemotePacketLoss = _adaptVideo;
	ci.label = _label;
	ci.tags = _tags;
	return ci;
}

ConferenceBridgeManager.prototype.createConference = function(_confSettings) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "createConference";
	fn.functionObject.conference = this.confHandle++;
	fn.functionObject.conferenceSettings = _confSettings;

	this.sdkConnector.send(JSON.stringify(fn));
	return fn.functionObject.conference;
}

ConferenceBridgeManager.prototype.destroyConference = function(_conf) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "destroyConference";
	
	fn.functionObject.conference = _conf;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.setStreamingEnabled = function(_conf, _enabled) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setStreamingEnabled";
	
	fn.functionObject.conference = _conf;
	fn.functionObject.enabled = _enabled;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.setTranscriptionEnabled = function(_conf, _enabled) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setTranscriptionEnabled";
	
	fn.functionObject.conference = _conf;
	fn.functionObject.enabled = _enabled;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.addAssociatedConference = function(_conf, _associated) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "addAssociatedConference";
	
	fn.functionObject.conference = _conf;
	fn.functionObject.associatedConference = _associated;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.addToFloor = function(_participant) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "addToFloor";
	fn.functionObject.participant = _participant;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.removeFromFloor = function(_participant) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "removeFromFloor";
	fn.functionObject.participant = _participant;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.queryConferenceDetails_handle = function(_confHandle, _callback) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryConferenceDetails";
	fn.functionObject.conference = _confHandle;
	this.confDetailsCallback = _callback;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.queryConferenceDetails = function(_confToken, _callback) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryConferenceDetails2";
	fn.functionObject.conferenceToken = _confToken;
	this.confDetailsCallback = _callback;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.queryConferenceList = function(_callback) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryConferenceList";
	this.confListCallback = _callback;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.queryParticipantList = function(_conf) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "queryParticipantList";
	fn.functionObject.conference = _conf;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.createWebParticipant = function(_conf) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "createWebParticipant";
	fn.functionObject.conference = _conf;
	fn.functionObject.participant = this.wpHandle++;
	this.sdkConnector.send(JSON.stringify(fn));
	return fn.functionObject.participant;
}

ConferenceBridgeManager.prototype.destroyWebParticipant = function(_webParticipant) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "destroyWebParticipant";
	fn.functionObject.webParticipant = _webParticipant;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.sendPeerConnectionOffer = function(_webParticipant, _sdpOffer) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "sendPeerConnectionOffer";
	fn.functionObject.webParticipant = _webParticipant;
	fn.functionObject.sdpOffer = _sdpOffer;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.makeWebParticipantIdentity = function(_displayName, _owner) {
	var wpi = {};
	wpi.displayName = _displayName;
	wpi.owner = _owner;
	return wpi;
}

ConferenceBridgeManager.prototype.setWebParticipantIdentity = function(_webParticipant, _webPartIdentity) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setWebParticipantIdentity";
	fn.functionObject.webParticipant = _webParticipant;
	fn.functionObject.displayName = _webPartIdentity.displayName;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.takeParticipantSnapshot = function(_participant) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "takeParticipantSnapshot";
	fn.functionObject.participant = _participant;
	this.sdkConnector.send(JSON.stringify(fn));
	return this;
}

ConferenceBridgeManager.prototype.setParticipantPhoto = function(_participant, _photoArrayBuff) {
	var fn = {};
	fn.moduleId = "ConferenceBridgeJsonApi";
	fn.functionObject = {};
	fn.functionObject.functionName = "setParticipantPhoto";
	fn.functionObject.participant = _participant;
	fn.functionObject.photoUri = "blob";
	var enc = new TextEncoder();
	var jsonUint8Array = enc.encode(JSON.stringify(fn));
	var tmp = new Uint8Array(jsonUint8Array.length + _photoArrayBuff.byteLength + 1);
	tmp.set(jsonUint8Array, 0);
	tmp.set([0], jsonUint8Array.length);
	tmp.set(new Uint8Array(_photoArrayBuff), jsonUint8Array.length + 1);
	this.sdkConnector.send(tmp);
	return this;
}

ConferenceBridgeManager.prototype.processEvent = function(_jsonEventObj) {
	if (_jsonEventObj.moduleId == "ConferenceBridgeManagerJsonProxy") {
		if (_jsonEventObj.functionObject.functionName == "onConferenceDetails" ||
		    _jsonEventObj.functionObject.functionName == "onConferenceNotFound") {
			if (this.confDetailsCallback) {
				var cb = this.confDetailsCallback;
				this.confDetailsCallback = null;
				var args = {};
				args.conference = _jsonEventObj.functionObject.conference;
				if (_jsonEventObj.functionObject.functionName == "onConferenceDetails") {
					args.conferenceToken = _jsonEventObj.functionObject.conferenceToken;
					args.conferenceInfo = _jsonEventObj.functionObject.conferenceInfo;
				}
				cb(_jsonEventObj.functionObject.conference, args);
			}
			else {
				if (this.callback) {
					this.callback(_jsonEventObj.functionObject);
				}
			}
		}
		else if (_jsonEventObj.functionObject.functionName == "onConferenceList") {
			if (this.confListCallback) {
				var cb = this.confListCallback;
				this.confListCallback = null;
				var args = {};
				args.conferences = _jsonEventObj.functionObject.conferences;
				cb(args);
			}
		}
		else {
			if (this.callback) {
				this.callback(_jsonEventObj.functionObject);
			}
		}
	}
	return this;
}
