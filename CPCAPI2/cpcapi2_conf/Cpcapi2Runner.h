#pragma once

// SDK includes
#include <cpcapi2.h>
#include <jsonapi/JsonApiServer.h>
#include <media/MediaManagerInternal.h>
#include <phone/PhoneInternal.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace Agent
{
class WatsonTTS;
class SdkManager;

struct JsonApiConfig
{
   resip::Data jsonApiWebsocketHostname;
   int jsonApiWebsocketPort=0;
   bool jsonApiWebsocketUseSecure=false;
   resip::Data jsonApiHttpHostname;
   int jsonApiHttpPort=0;
   bool jsonApiHttpUseSecure=false;
   resip::Data joinUrlBase;
   bool sdkLoggingEnabled=false;
   resip::Data serverUid;
};

class Cpcapi2Runner : public CPCAPI2::PhoneHandler,
                      public CPCAPI2::Licensing::LicensingClient<PERSON><PERSON><PERSON>,
                      public CPCAPI2::SipAccount::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                      public CPCAPI2::SipConversation::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
                      public CPCAPI2::ConferenceBridge::ConferenceBridge<PERSON><PERSON><PERSON>,
                      public CPCAPI2::Media::VideoHandler,
                      public CPCAPI2::Media::AudioHandler
{
public:
   Cpcapi2Runner(const resip::Data& userIdentity, 
      const resip::Data& resource,
      const resip::Data& acctSettingsJson, 
      const resip::Data& confConfigFilename,
      int sdkThreadPoolThreadIdx, 
      SdkManager* sdkMgr,
      resip::MultiReactor* appReactor, 
      CPCAPI2::Phone* masterSdkPhone, 
      CPCAPI2::JsonApi::JsonApiServer* jsonApiServer, 
      CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory, 
      const JsonApiConfig& jsonApiConfig,
      const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& natServerInfo,
      const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& screenshareBitrateConfig,
      const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& cameraBitrateConfig,
      const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mediaEncryptionMode, 
      bool useServerUidInJoinUrls,
      bool createRootConference,
      const cpc::string& realm = "",
      const cpc::vector<cpc::string>& requestedResources = cpc::vector<cpc::string>());
   virtual ~Cpcapi2Runner();

   resip::Data getContext() const;

   void handleSdkCallback();
   void shutdown();

   CPCAPI2::Phone* getPhone() const {
      return mPhone;
   }

   resip::Data getUserIdentity() const {
      return mUserIdentity;
   }

   bool isValidResource(const cpc::vector<cpc::string>& requestedResources);

   // PhoneHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);
   virtual int onLicensingSuccess() {
      return 0;
   }

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // SipAccountHandler
   virtual int onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args);
   virtual int onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args);

   // SipConversationHandler
   virtual int onNewConversation(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::NewConversationEvent& args);
   virtual int onConversationEnded(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationEndedEvent& args);
   virtual int onIncomingTransferRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferRequestEvent& args);
   virtual int onIncomingRedirectRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::RedirectRequestEvent& args);
   virtual int onIncomingTargetChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TargetChangeRequestEvent& args);
   virtual int onIncomingHangupRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::HangupRequestEvent& args);
   virtual int onIncomingBroadsoftTalkRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftTalkEvent& args);
   virtual int onIncomingBroadsoftHoldRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::BroadsoftHoldEvent& args);
   virtual int onTransferProgress(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::TransferProgressEvent& args);
   virtual int onConversationStateChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangeRequestEvent& args);
   virtual int onConversationStateChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStateChangedEvent& args);
   virtual int onConversationMediaChangeRequest(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangeRequestEvent& args);
   virtual int onConversationMediaChanged(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationMediaChangedEvent& args);
   virtual int onConversationStatisticsUpdated(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ConversationStatisticsUpdatedEvent& args);
   virtual int onError(CPCAPI2::SipConversation::SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args);

   // VideoHandler
   virtual int onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args);
   virtual int onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args);

   // AudioHandler
   virtual int onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args);
   virtual int onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip);
   virtual int onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args);
   virtual int onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args);

   // ConferenceBridgeHandler
   virtual int onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args);
   virtual int onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args);
   virtual int onPeerConnectionAnswer(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::PeerConnectionAnswerEvent& args) {
      return kSuccess;
   }
   virtual int onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args);
   virtual int onConferenceTranscriptionResult(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceTranscriptionEvent& args) {
      return kSuccess;
   }

   void addJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::vector<cpc::string>& requestedResources);
   void removeJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser);
   size_t countJsonApiUsers() const;

   int createFullConference();
   int createScreenshare();

   void updateJoinUrl(const cpc::string& joinUrlBase);
   void setLoggingEnabled(bool enable);

private:
   void appInit();
   void appShutdown();

   void handleSdkCallbackImpl();

   CPCAPI2::PhoneInternal* getPhoneInternalForLogger() const;
   void initAccountSettings();
   void initConfSettings();
   std::string getWebsockUrl() const;

   void playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName);

private:
   resip::Data mUserIdentity;
   resip::Data mResource;
   resip::Data mAcctSettingsJson;
   resip::Data mConfConfigFilename;
   int mSdkThreadPoolThreadIdx;
   SdkManager* mSdkMgr;
   resip::MultiReactor* mAppReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Phone* mMasterSdkPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::JsonApi::JsonApiServer* mJsonApiServer;
   CPCAPI2::Media::MediaTransportsReactorFactory* mMediaReactorFactory;
   CPCAPI2::Media::MediaManager* mMedia;
   CPCAPI2::ConferenceBridge::ConferenceBridgeManager* mConfBridge;
   CPCAPI2::ConferenceBridge::ConferenceHandle mConference;
   CPCAPI2::PeerConnection::PeerConnectionManager* mPeerConnIf;

   struct CallInfo
   {
      cpc::string address;
      cpc::string displayName;
   };
   std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo> mCalls;

   std::unique_ptr<WatsonTTS> mTTSApi;
   std::map<CPCAPI2::JsonApi::JsonApiUserHandle, resip::Data> mJsonApiUsers;
   std::map<CPCAPI2::ConferenceBridge::ConferenceHandle, resip::Data> mMapConfHdlToJoinUrl;
   CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo mNatServerInfo;
   CPCAPI2::ConferenceBridge::ConferenceBitrateConfig mScreenshareBitrateConfig;
   CPCAPI2::ConferenceBridge::ConferenceBitrateConfig mCameraVideoBitrateConfig;
   JsonApiConfig mJsonApiConfig;
   CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode mMediaEncryptionMode;
   bool mUseServerUidInJoinUrls;
   bool mCreateRootConference;

   cpc::string mRealm;
   cpc::vector<cpc::string> mRequestedResources;
   CPCAPI2::ConferenceBridge::ConferenceBridgeConfig mConfBridgeConfig;
};
}
}
