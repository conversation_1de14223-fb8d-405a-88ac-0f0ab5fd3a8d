#if _WIN32
#include "stdafx.h"
#endif

#include "Cpcapi2Runner.h"

#include "brand_branded.h"
#include "Logger.h"
#include "WatsonTTS.h"
#include "PlaySoundFileStream.h"
#include "Cpcapi2ServerLicense.h"
#include "SdkManager.h"

#include <interface/experimental/account/SipAccountManagerInternal.h>
#include <interface/experimental/account/SipAccountJsonApi.h>
#include <interface/experimental/call/SipConversationJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeJsonApi.h>
#include <interface/experimental/confbridge/ConferenceBridgeInternal.h>
#include <interface/experimental/media/MediaManagerInternal.h>
#include <interface/experimental/peerconnection/PeerConnectionManagerInternal.h>
#include <interface/experimental/confbridge/ConferenceRegistrar.h>

#include "rutil/ConfigParse.hxx"

#include <fstream>

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::Media;
using namespace CPCAPI2::PeerConnection;
using namespace CPCAPI2::ConferenceBridge;

namespace CPCAPI2
{
namespace Agent
{
class ConfConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

Cpcapi2Runner::Cpcapi2Runner(const resip::Data& userIdentity, 
   const resip::Data& resource,
   const resip::Data& acctSettingsJson, 
   const resip::Data& confConfigFilename,
   int sdkThreadPoolThreadIdx, 
   SdkManager* sdkMgr,
   resip::MultiReactor* appReactor, 
   CPCAPI2::Phone* masterSdkPhone, 
   CPCAPI2::JsonApi::JsonApiServer* jsonApiServer, 
   CPCAPI2::Media::MediaTransportsReactorFactory* mediaReactorFactory, 
   const JsonApiConfig& jsonApiConfig,
   const CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo& natServerInfo,
   const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& screenshareBitrateConfig,
   const CPCAPI2::ConferenceBridge::ConferenceBitrateConfig& cameraBitrateConfig, 
   const CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode& mediaEncryptionMode,
   bool useServerUidInJoinUrls,
   bool createRootConference,
   const cpc::string& realm,
   const cpc::vector<cpc::string>& requestedResources)
   : mUserIdentity(userIdentity), 
   mResource(resource),
   mAcctSettingsJson(acctSettingsJson), 
   mConfConfigFilename(confConfigFilename),
   mSdkThreadPoolThreadIdx(sdkThreadPoolThreadIdx), 
   mSdkMgr(sdkMgr),
   mAppReactor(appReactor), 
   mPhone(NULL), 
   mMasterSdkPhone(masterSdkPhone), 
   mJsonApiServer(jsonApiServer), 
   mMediaReactorFactory(mediaReactorFactory), 
   mNatServerInfo(natServerInfo),
   mScreenshareBitrateConfig(screenshareBitrateConfig),
   mCameraVideoBitrateConfig(cameraBitrateConfig),
   mJsonApiConfig(jsonApiConfig),
   mMediaEncryptionMode(mediaEncryptionMode),
   mUseServerUidInJoinUrls(useServerUidInJoinUrls),
   mCreateRootConference(createRootConference),
   //, mTTSApi(new WatsonTTS())
   mRealm(realm),
   mRequestedResources(requestedResources)
{
   appInit();
}

Cpcapi2Runner::~Cpcapi2Runner()
{
   mAppReactor->detach();
}

CPCAPI2::PhoneInternal* Cpcapi2Runner::getPhoneInternalForLogger() const
{
   return mPhone;
}

void Cpcapi2Runner::shutdown()
{
   assert(mAppReactor->isCurrentThread());
   appShutdown();
}

void Cpcapi2Runner::handleSdkCallback()
{
   mAppReactor->post(resip::resip_bind(&Cpcapi2Runner::handleSdkCallbackImpl, this));
}

void Cpcapi2Runner::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mMedia->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mConfBridge->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mPeerConnIf->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
}

void sdkCallbackHook(void* context)
{
   Cpcapi2Runner* cpcRunner = (Cpcapi2Runner*)context;
   cpcRunner->handleSdkCallback();
}

class RegisterConferenceResultHandlerFuncObj : public RegisterConferenceResultHandler
{
public:
   RegisterConferenceResultHandlerFuncObj(const std::function<void(ConferenceRegistrarHandle, const RegisterConferenceResult&)>& func) : mFunc(func) {}
   virtual ~RegisterConferenceResultHandlerFuncObj() {}

   virtual int onRegisterConferenceComplete(ConferenceRegistrarHandle registrar, const RegisterConferenceResult& args) override {
      mFunc(registrar, args);
      delete this;
      return kSuccess;
   }
   virtual bool synchronous() const override {
      return true;
   }

private:
   std::function<void(ConferenceRegistrarHandle, const RegisterConferenceResult&)> mFunc;
};

std::string Cpcapi2Runner::getWebsockUrl() const
{
   std::stringstream ssWebsock;
   ssWebsock << (mJsonApiConfig.jsonApiWebsocketUseSecure ? "wss://" : "ws://");
   ssWebsock << mJsonApiConfig.jsonApiWebsocketHostname << ":" << mJsonApiConfig.jsonApiWebsocketPort;
   return ssWebsock.str();
}

void Cpcapi2Runner::appInit()
{
   mLicensingMgr = LicensingClientManager::getInterface(mMasterSdkPhone);
   mPhone = CPCAPI2::PhoneInternal::create(mSdkThreadPoolThreadIdx);
   mPhone->setCallbackHook(sdkCallbackHook, this);
   mPhone->initialize(mLicensingMgr, this, false);
   mPhone->setLoggingEnabled("cpcconf", false);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy phone instance initialized.");
   CPCAPI2::Media::MediaManagerInternal::getInterface(mPhone, mMediaReactorFactory);

   // need to explicitly create the modules we want to expose
   CPCAPI2::ConferenceBridge::ConferenceBridgeJsonApi::getInterface(mPhone);
   // setup a mapping between the SDK instance for this user (mPhone) and the single-instance WebSocket server managed by mMasterSdkPhone (i.e. jsonApiServer)
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone)->setJsonApiServer(mJsonApiServer);
   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->setVideoStreamingServer(CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mMasterSdkPhone));
   }

   mConfBridgeConfig.userContext = mResource.c_str();

   CPCAPI2::Media::MediaStackSettings mediaSettings;
   mediaSettings.audioLayer = CPCAPI2::Media::AudioLayers_File;
   mediaSettings.audioOutputDisabled = true;
   mediaSettings.numAudioEncoderThreads = std::thread::hardware_concurrency() * 2;
   mMedia = MediaManager::getInterface(mPhone);
   if (!mMedia)
   {
      InfoLog(<< "Cpcapi2Runner::thread: MediaManager interface not created successfully");
   }
   dynamic_cast<MediaManagerInternal*>(mMedia)->setCallbackHook(sdkCallbackHook, this);
   AudioExt* audioExt = AudioExt::getInterface(mMedia);
   if (!audioExt)
   {
      InfoLog(<< "Cpcapi2Runner::thread: AudioExt interface not created successfully");
   }
   audioExt->setAudioDeviceFile("silence16.pcm", "");
   mMedia->initializeMediaStack(mediaSettings);

   CPCAPI2::Media::Audio* audioIf = Audio::getInterface(mMedia);
   audioIf->setHandler(this);
   audioIf->setEchoCancellationMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::EchoCancellationMode_None);
   audioIf->setNoiseSuppressionMode(CPCAPI2::Media::AudioDeviceRole_Headset, CPCAPI2::Media::NoiseSuppressionMode_None);
   AudioExt::getInterface(mMedia)->setMicAGCEnabled(false);
   audioIf->queryCodecList();
   //audioIf->setMicMute(true);
   //audioIf->setSpeakerMute(true);
   CPCAPI2::Media::OpusConfig opusConfig;
   opusConfig.complexity = 2;
   opusConfig.enableDtx = false;
   opusConfig.application = 2;
   opusConfig.bitrate = 32000;
   audioIf->setCodecConfig(opusConfig);

   Video::getInterface(mMedia)->setHandler(this);
   Video::getInterface(mMedia)->setCaptureDevice(CPCAPI2::Media::kCustomVideoSourceDeviceId);
   //Video::getInterface(mMedia)->startCapture();
   //Video::getInterface(mMedia)->setVideoMixMode(CPCAPI2::Media::VideoMixMode_MCU);
   Video::getInterface(mMedia)->queryCodecList();

   mConfBridge = CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getInterface(mPhone);
   mConfBridge->setHandler(this);
   dynamic_cast<CPCAPI2::ConferenceBridge::ConferenceBridgeManagerInternal*>(mConfBridge)->setCallbackHook(sdkCallbackHook, this);

   mConfBridgeConfig.httpJoinUrlBase = !mRealm.empty() ? mRealm : cpc::string(mJsonApiConfig.joinUrlBase.c_str()); //"localhost:18090";
   mConfBridgeConfig.wsUrlBase = getWebsockUrl().c_str(); //"localhost:9003";
   mConfBridgeConfig.serverUid = mJsonApiConfig.serverUid.c_str();
   mConfBridgeConfig.useServerUidInJoinUrls = mUseServerUidInJoinUrls;
   mConfBridgeConfig.natTraversalServerInfo = mNatServerInfo;
   mConfBridgeConfig.screenshareBitrateConfig = mScreenshareBitrateConfig;
   mConfBridgeConfig.cameraBitrateConfig = mCameraVideoBitrateConfig;
   mConfBridgeConfig.mediaEncryptionMode = mMediaEncryptionMode;
   mConfBridgeConfig.mediaInactivityTimeoutMs = 5000; // only applies to audio conferences
   mConfBridge->start(mConfBridgeConfig);

   mPeerConnIf = CPCAPI2::PeerConnection::PeerConnectionManager::getInterface(mPhone);
   mPeerConnIf->setCallbackHook(sdkCallbackHook, this);

   InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy media module initialized");

   if (mConfConfigFilename.empty())
   {
      if (mCreateRootConference)
      {
         ConferenceBridge::ConferenceSettings confSettings;
         confSettings.mixMode = CPCAPI2::ConferenceBridge::ConferenceMixMode_NoMixing;
         confSettings.persistent = true;
         confSettings.isPublic = true;
         confSettings.label = "root";
         confSettings.conferenceToken = "root";
         confSettings.tags.push_back(0x4000); // audio only

         mConference = mConfBridge->createConference(confSettings);
         mConfBridge->queryConferenceDetails(mConference);
      }
   }
   else if (mConfConfigFilename == "jsonApi")
   {
      // do nothing
   }
   else
   {
      initConfSettings();
   }
}

void Cpcapi2Runner::appShutdown()
{
   //InfoLog(<< "Cpcapi2Runner::thread: cpcapi2 rtp proxy is shutdown!");
   mConfBridge->shutdown();
   Video::getInterface(mMedia)->stopCapture();
   mPhone->process(100);
}

void Cpcapi2Runner::initAccountSettings()
{
}

void Cpcapi2Runner::initConfSettings()
{
   ConfConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, mConfConfigFilename);

   //fileConfig.getConfigValue("sdk_logging_enabled", mJsonApiConfig->sdkLoggingEnabled);
   resip::Data confLabel = fileConfig.getConfigData("conference_label", "root", true);
   int mixMode = fileConfig.getConfigInt("mix_mode", CPCAPI2::ConferenceBridge::ConferenceMixMode_MCU);
   int aecMode = fileConfig.getConfigInt("per_channel_echo_cancellation", CPCAPI2::Media::EchoCancellationMode_None);
   int nsMode = fileConfig.getConfigInt("per_channel_noise_suppression", CPCAPI2::Media::NoiseSuppressionMode_None);

   ConferenceBridge::ConferenceSettings confSettings;
   confSettings.mixMode = (CPCAPI2::ConferenceBridge::ConferenceMixMode)mixMode;
   confSettings.persistent = true;
   confSettings.isPublic = true;
   confSettings.label = confLabel.c_str();
   confSettings.conferenceToken = confLabel.c_str();
   confSettings.tags.push_back(0x4000); // audio only
   confSettings.mixerSettings.perChannelAecMode = (CPCAPI2::Media::EchoCancellationMode)aecMode;
   confSettings.mixerSettings.perChannelNsMode = (CPCAPI2::Media::NoiseSuppressionMode)nsMode;

   mConference = mConfBridge->createConference(confSettings);
   mConfBridge->queryConferenceDetails(mConference);
}

void Cpcapi2Runner::updateJoinUrl(const cpc::string& joinUrlBase)
{
   if (mConfBridge != NULL)
   {
      if (!joinUrlBase.empty())
      {
         mConfBridge->updateJoinUrlBase(joinUrlBase);
      }
   }
}

void Cpcapi2Runner::setLoggingEnabled(bool enable)
{
   mPhone->setLoggingEnabled("cpcconf", enable);
}

int Cpcapi2Runner::createFullConference()
{
   ConferenceBridge::ConferenceSettings confSettings;
   confSettings.participantJoinSound = "file:confjoin.wav";
   confSettings.participantLeaveSound = "file:confleave.wav";
   confSettings.mixMode = CPCAPI2::ConferenceBridge::ConferenceMixMode_MCU;
   confSettings.adaptVideoCodecOnRemotePacketLoss = true;
   confSettings.persistent = true;
   {
      confSettings.label = "videoconference";
   }
   mConference = mConfBridge->createConference(confSettings);
   //mConfBridge->setHandler(mConference, this);
   //mConfBridge->setStreamingEnabled(mConference, true);
   mConfBridge->queryConferenceDetails(mConference);
   return 0;
}

int Cpcapi2Runner::createScreenshare()
{
   ConferenceBridge::ConferenceSettings confSettings;
   confSettings.participantJoinSound = "file:confjoin.wav";
   confSettings.participantLeaveSound = "file:confleave.wav";
   confSettings.mixMode = CPCAPI2::ConferenceBridge::ConferenceMixMode_SFU;
   confSettings.adaptVideoCodecOnRemotePacketLoss = false;
   confSettings.persistent = true;
   confSettings.label = "screenshare";
   mConference = mConfBridge->createConference(confSettings);
   //mConfBridge->setHandler(mConference, this);
   //mConfBridge->setStreamingEnabled(mConference, true);
   mConfBridge->queryConferenceDetails(mConference);
   return 0;
}

resip::Data Cpcapi2Runner::getContext() const
{
   resip::Data contextStr;
   return contextStr;
}

bool Cpcapi2Runner::isValidResource(const cpc::vector<cpc::string>& requestedResources)
{
   for (const cpc::string& joinUrl : requestedResources)
   {
      if (mConfBridge->isValidJoinUrl(joinUrl))
      {
         return true;
      }
   }
   return false;
}

int Cpcapi2Runner::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}
int Cpcapi2Runner::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}
int Cpcapi2Runner::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}


///////////////////////////////////////////////////////////////////////////////
// SipAccountHandler
int Cpcapi2Runner::onAccountStatusChanged(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::SipAccountStatusChangedEvent& args)
{
   DebugLog(<< "onAccountStatusChanged: " << account);
   return 0;
}

int Cpcapi2Runner::onError(CPCAPI2::SipAccount::SipAccountHandle account, const CPCAPI2::SipAccount::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// ConferenceBridgeHandler
int Cpcapi2Runner::onConferenceDetails(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceDetailsResult& args)
{
   DebugLog(<< "Cpcapi2Runner::onConferenceDetails: " << conference);
   std::cout << "conference join URL: " << args.conferenceJoinUrl << std::endl;
   mMapConfHdlToJoinUrl[conference] = args.conferenceJoinUrl.c_str();
   return 0;
}

int Cpcapi2Runner::onParticipantListState(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ParticipantListState& args)
{
   mSdkMgr->writeSessionSummary();
   return 0;
}

int Cpcapi2Runner::onConferenceEnded(CPCAPI2::ConferenceBridge::ConferenceHandle conference, const CPCAPI2::ConferenceBridge::ConferenceEndedEvent& args) {
   DebugLog(<< "Cpcapi2Runner::onConferenceEnded: " << conference);
   mSdkMgr->writeSessionSummary();

   auto it = mMapConfHdlToJoinUrl.find(conference);
   if (it != mMapConfHdlToJoinUrl.end())
   {
      bool shouldDestroyUser = true;

      // are there any other conferences with this join URL?
      auto it2 = mMapConfHdlToJoinUrl.begin();
      for (; it2 != mMapConfHdlToJoinUrl.end(); ++it2)
      {
         if (it2->second == it->second)
         {
            // if so, then don't destroy the user
            shouldDestroyUser = false;
         }
      }

      if (shouldDestroyUser)
      {
         auto itUsers = mJsonApiUsers.begin();
         for (; itUsers != mJsonApiUsers.end(); ++itUsers)
         {
            if (itUsers->second == it->second)
            {
               mJsonApiServer->destroyUser(itUsers->first);
            }
         }
      }
      mMapConfHdlToJoinUrl.erase(it);
   }
   return kSuccess;
}

///////////////////////////////////////////////////////////////////////////////
// SipConversationHandler
int Cpcapi2Runner::onNewConversation(SipConversationHandle conversation, const NewConversationEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onNewConversation: " << conversation << " from " << args.remoteAddress);
   //CallInfo callInfo;
   //callInfo.address = args.remoteAddress;
   //callInfo.displayName = args.remoteDisplayName;
   //mCalls[conversation] = callInfo;

   //mConversation->setMediaEnabled(conversation, SipConversation::MediaType_Audio, true);
   //mConversation->setMediaEnabled(conversation, SipConversation::MediaType_Video, true);

   //mConversation->accept(conversation);

   //InfoLog(<< "Cpcapi2Runner::onNewConversation: Conversation from " << args.remoteAddress << "accepted");

   return 0;
}

int Cpcapi2Runner::onConversationEnded(SipConversationHandle conversation, const ConversationEndedEvent& args)
{
   InfoLog(<< "Cpcapi2Runner::onConversationEnded: Conversation " << conversation << "ended");
   //std::map<CPCAPI2::SipConversation::SipConversationHandle, CallInfo>::iterator callsIt = mCalls.find(conversation);
   //mCalls.erase(callsIt);
   //mConversation->refreshConversationStatistics(conversation, true, true, true);
   return 0;
}

int Cpcapi2Runner::onIncomingTransferRequest(SipConversationHandle conversation, const TransferRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingRedirectRequest(SipConversationHandle conversation, const RedirectRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingTargetChangeRequest(SipConversationHandle conversation, const TargetChangeRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingHangupRequest(SipConversationHandle conversation, const HangupRequestEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftTalkRequest(SipConversationHandle conversation, const BroadsoftTalkEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onIncomingBroadsoftHoldRequest(SipConversationHandle conversation, const BroadsoftHoldEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onTransferProgress(SipConversationHandle conversation, const TransferProgressEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStateChangeRequest(SipConversationHandle conversation, const ConversationStateChangeRequestEvent& args)
{
   return 0;
}

void Cpcapi2Runner::playWelcome(CPCAPI2::SipConversation::SipConversationHandle conversation, const std::string& fileName)
{
   //PlaySoundFileStream* playSoundStream = new PlaySoundFileStream();
   //if (playSoundStream->OpenFile(fileName.c_str(), true, false, false) == 0)
   //{
   //   mConversation->playSound(conversation, playSoundStream, false);
   //}
}

int Cpcapi2Runner::onConversationStateChanged(SipConversationHandle conversation, const ConversationStateChangedEvent& args)
{
   //if (args.conversationState == CPCAPI2::SipConversation::ConversationState_Connected)
   //{
      ////mTTSApi->connect();
      //std::string welcomeStr;
      //{
      //   SipConversationState convState;
      //   mConvStateMan->getState(conversation, convState);
      //   std::stringstream ss;
      //   ss << "Welcome ";
      //   ss << convState.remoteDisplayName.c_str();
      //   welcomeStr = ss.str();
      //}
      ////mTTSApi->convert(welcomeStr.c_str(), std::bind(&Cpcapi2Runner::playWelcome, this, conversation, std::placeholders::_1));
   //}
   return 0;
}

int Cpcapi2Runner::onConversationMediaChangeRequest(SipConversationHandle conversation, const ConversationMediaChangeRequestEvent& args)
{
   //mConversation->accept(conversation);
   return 0;
}

int Cpcapi2Runner::onConversationMediaChanged(SipConversationHandle conversation, const ConversationMediaChangedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onConversationStatisticsUpdated(SipConversationHandle conversation, const ConversationStatisticsUpdatedEvent& args)
{
   //DebugLog(<< "=========== CONVERSATION STATISTICS =============");
   //CPCAPI2::SipConversation::ConversationStatistics conversationStatistics = args.conversationStatistics;
   //if (conversationStatistics.audioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL ----------"
   //      << "cumulativeLost:      " << conversationStatistics.audioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.audioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.audioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.audioChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:     " << conversationStatistics.audioChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:         " << conversationStatistics.audioChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:      " << conversationStatistics.audioChannels[0].decoder.plname << std::endl
   //      << "encoder.plname:      " << conversationStatistics.audioChannels[0].encoder.plname << std::endl
   //      << "localEndpoint:       " << conversationStatistics.audioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.audioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.videoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- LOCAL (video) ----------"
   //      << "cumulativeLost:       " << conversationStatistics.videoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:         " << conversationStatistics.videoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:        " << conversationStatistics.videoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:                " << conversationStatistics.videoChannels[0].streamStatistics.rttMs << std::endl
   //      << "packetsReceived:      " << conversationStatistics.videoChannels[0].streamDataCounters.packetsReceived << std::endl
   //      << "packetsSent:          " << conversationStatistics.videoChannels[0].streamDataCounters.packetsSent << std::endl
   //      << "decoder.plname:       " << conversationStatistics.videoChannels[0].decoder.plName << std::endl
   //      << "encoder.plname:       " << conversationStatistics.videoChannels[0].encoder.plName << std::endl
   //      << "currentTargetBitrate: " << conversationStatistics.videoChannels[0].currentTargetBitrate << std::endl
   //      << "localEndpoint:        " << conversationStatistics.videoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.videoChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteAudioChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE ----------"
   //      << "cumulativeLost:      " << conversationStatistics.remoteAudioChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteAudioChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteAudioChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteAudioChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteAudioChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteAudioChannels[0].endpoint.port);
   //}
   //if (conversationStatistics.remoteVideoChannels.size() > 0)
   //{
   //   DebugLog(<< "---------- REMOTE (video) ----------" << std::endl
   //      << "cumulativeLost:      " << conversationStatistics.remoteVideoChannels[0].streamStatistics.cumulativeLost << std::endl
   //      << "fractionLost:        " << conversationStatistics.remoteVideoChannels[0].streamStatistics.fractionLost << std::endl
   //      << "jitterSamples:       " << conversationStatistics.remoteVideoChannels[0].streamStatistics.jitterSamples << std::endl
   //      << "rttMs:               " << conversationStatistics.remoteVideoChannels[0].streamStatistics.rttMs << std::endl
   //      << "remoteEndpoint:      " << conversationStatistics.remoteVideoChannels[0].endpoint.ipAddress << ":" << conversationStatistics.remoteVideoChannels[0].endpoint.port);
   //}
   //DebugLog(<< "======> Call Quality: " << conversationStatistics.callQuality);

   return 0;
}

int Cpcapi2Runner::onError(SipConversationHandle conversation, const CPCAPI2::SipConversation::ErrorEvent& args)
{
   return 0;
}

///////////////////////////////////////////////////////////////////////////////
// VideoHandler
int Cpcapi2Runner::onVideoDeviceListUpdated(const CPCAPI2::Media::VideoDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onVideoCodecListUpdated(const CPCAPI2::Media::VideoCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::VideoCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName, "H.264") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, true);
         CPCAPI2::Media::H264Config h264config;
         h264config.enableNonInterleavedMode = false;
         Video::getInterface(mMedia)->setCodecConfig(h264config);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else if (strcmp(it->codecName, "VP8") == 0)
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
         Video::getInterface(mMedia)->setPreferredResolution(it->id, CPCAPI2::Media::VideoCaptureResolution_HD_1280x720p);
      }
      else
      {
         Video::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

////////////////////////////////////////////////////////////////////////////////
// AudioHandler
int Cpcapi2Runner::onAudioDeviceListUpdated(const CPCAPI2::Media::AudioDeviceListUpdatedEvent& args)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundComplete(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onPlaySoundFailure(CPCAPI2::Media::PlaySoundHandle soundClip)
{
   return 0;
}

int Cpcapi2Runner::onAudioCodecListUpdated(const CPCAPI2::Media::AudioCodecListUpdatedEvent& args)
{
   cpc::vector<CPCAPI2::Media::AudioCodecInfo>::const_iterator it = args.codecInfo.begin();
   for (; it != args.codecInfo.end(); ++it)
   {
      if (strcmp(it->codecName, "OPUS") == 0)
      {
         Audio::getInterface(mMedia)->setCodecEnabled(it->id, true);
      }
      else
      {
         Audio::getInterface(mMedia)->setCodecEnabled(it->id, false);
      }
   }
   return 0;
}

int Cpcapi2Runner::onAudioDeviceVolume(const CPCAPI2::Media::AudioDeviceVolumeEvent& args)
{
   return 0;
}

void Cpcapi2Runner::addJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const cpc::vector<cpc::string>& requestedResources)
{
   if (requestedResources.empty())
   {
      mJsonApiUsers[jsonApiUser] = "default";
   }
   else
   {
      mJsonApiUsers[jsonApiUser] = requestedResources[0].c_str();
   }
}

void Cpcapi2Runner::removeJsonApiUser(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser)
{
   mJsonApiUsers.erase(jsonApiUser);
}

size_t Cpcapi2Runner::countJsonApiUsers() const
{
   return mJsonApiUsers.size();
}

}
}
