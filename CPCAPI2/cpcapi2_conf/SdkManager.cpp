#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2Runner.h"
#include "Cpcapi2ServerLicense.h"
#include "cpcapi2_conf_buildinfo.h"
#include "Logger.h"

#include <cpcapi2.h>
#include <cloudserviceconfig/CloudServiceConfig.h>
#include <confbridge/ConferenceBridgeInternal.h>
#include <confbridge/ConferenceRegistrar.h>

#include "rutil/ConfigParse.hxx"
#include "rutil/Random.hxx"

#include <boost/preprocessor/stringize.hpp>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>
#include <prettywriter.h>

#include <fstream>

#ifndef WIN32
#include <signal.h>
#endif

#ifdef __linux__
#include <sys/stat.h>
#include <sys/types.h> // setuid()
#include <unistd.h> // setuid()
#endif

#include <thread>
#include <functional>
#include <filesystem>

#define CPCAPI2_CONF_NUM_SDK_REACTORS std::thread::hardware_concurrency()
#define RANDOM_PATH_COMPONENT_LENGTH 8
#define SDKMANAGER_LOGGING_TIMER 1

#ifdef BUILD_INFO
#define BUILD_INFO_STRING BOOST_PP_STRINGIZE(BUILD_INFO)
#else
#define BUILD_INFO_STRING "No build info"
#endif

using namespace CPCAPI2;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::Licensing;
using namespace CPCAPI2::CloudServiceConfig;

namespace CPCAPI2
{
namespace Agent
{
class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

std::atomic_int SdkManager::sNextSdkThreadPoolIdx(0);
std::function<void(int)> signal_callback_wrapper;
void signal_callback_function(int val)
{
   signal_callback_wrapper(val);
}

SdkManager::SdkManager(StartupConfig& startupConfig)
   : mPhone(NULL),
     mLicensingMgr(NULL),
     mAuthServer(NULL),
     mConfRegistrar(NULL),
     mJsonApiConfig(new JsonApiConfig),
     mMediaEncryptionMode(CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS),
     mRestAsyncResultHandler(this),
     mBuildInfo(this),
     mSessionSummaryReqHandler(this),
     mConnectedEndpointSummaryReqHandler(this),
     mCrashReqHandler(this),
     mMaintMode(this),
     mLoggingEnableHandler(this, true),
     mLoggingDisableHandler(this, false),
     mLoggingStatusReqHandler(this),
     mRaftListNodes(this),
     mMaintenanceModeEnabled(false),
     mUseServerUidInJoinUrls(false),
     mCreateRootConference(true),
     mTotalPrivilegedAccesses(0),
     mLoggingTimer(mReactor),
     mIsLoggingEnabled(false),
     mStartupConfig(startupConfig),
     mIsLeader(false),
     mNodeId(-1)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mJsonApiServer->process(CPCAPI2::JsonApi::JsonApiServer::kBlockingModeNonBlocking);
   if (mConfRegistrar != NULL)
   {
      mConfRegistrar->process(CPCAPI2::ConferenceBridge::ConferenceRegistrar::kBlockingModeNonBlocking);
   }
}

void SdkManager::sigtermHandler(int signum)
{
   shutdown();
}

void SdkManager::onPrivilegedAccessCompletedImpl()
{
#ifdef __linux__
   assert(mTotalPrivilegedAccesses > 0);

   if (--mTotalPrivilegedAccesses == 0)
   {

      char buffer[1024];
      memset(buffer, 0, sizeof(buffer));
      readlink("/proc/self/exe", buffer, sizeof(buffer) - 1);

      struct stat _stat;
      stat(buffer, &_stat);

      setgid(_stat.st_gid);
      setuid(_stat.st_uid);

   }
#endif
}

void SdkManager::onPrivilegedAccessCompleted(void* context)
{
   auto sdkManager = static_cast<SdkManager*>(context);
   sdkManager->mReactor.post(resip::resip_bind(&SdkManager::onPrivilegedAccessCompletedImpl, sdkManager));
}

void SdkManager::appInit()
{
#ifndef WIN32
   // OBELISK-4439: prevent SIGPIPE from killing the whole process by ignoring SIGPIPE;
   // write errors will be handled locally instead
   signal(SIGPIPE, SIG_IGN);

   // systemd will send us SIGTERM when it wants us to shutdown
   struct sigaction sthandler;
   signal_callback_wrapper = std::bind(&SdkManager::sigtermHandler, this, std::placeholders::_1);
   sthandler.sa_handler = signal_callback_function;
   sigemptyset(&sthandler.sa_mask);
   sthandler.sa_flags = 0;
   sigaction(SIGTERM, &sthandler, NULL);
#endif

   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "/tmp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->setLoggingEnabled("cpcconf", mStartupConfig.logging);
   mPhone->initialize(licenseInfo, this, false);
   mIsLoggingEnabled = mStartupConfig.logging;

   if (mIsLoggingEnabled) setLoggingTimer(1000 * 30); // 30 seconds

   initAuth();
   initConf();
}

void SdkManager::initAuth()
{
   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_auth_service.config");

   mAuthServer = CPCAPI2::AuthServer::AuthServer::getInterface(mPhone);
   mAuthServer->setHandler(this);
   mAuthServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::AuthServer::AuthServerConfig serverConfig;
   serverConfig.numThreads = 4;

   serverConfig.port = 18082;
   fileConfig.getConfigValue("auth_service_http_port", serverConfig.port);
   fileConfig.getConfigValue("token_expiry_time", serverConfig.tokenExpiry);

   int backendTypeInt = 0;
   if (fileConfig.getConfigValue("auth_service_backend_type", backendTypeInt))
   {
      serverConfig.backend = (CPCAPI2::AuthServer::BackendType)backendTypeInt;
   }

   resip::Data httpsCertificateFilePath;
   if (fileConfig.getConfigValue("auth_service_http_cert_file_path", httpsCertificateFilePath))
   {
      serverConfig.httpsCertificateFilePath = httpsCertificateFilePath.c_str();
   }
   if (!std::filesystem::exists(serverConfig.httpsCertificateFilePath.c_str()))
   {
      std::cerr << "auth_service_http_cert_file_path file not found: " << serverConfig.httpsCertificateFilePath << std::endl;
      exit(1);
   }

   resip::Data httpsPrivateKeyFilePath;
   if (fileConfig.getConfigValue("auth_service_http_priv_key_file_path", httpsPrivateKeyFilePath))
   {
      serverConfig.httpsPrivateKeyFilePath = httpsPrivateKeyFilePath.c_str();
   }
   if (!std::filesystem::exists(serverConfig.httpsPrivateKeyFilePath.c_str()))
   {
      std::cerr << "auth_service_http_priv_key_file_path file not found: " << serverConfig.httpsPrivateKeyFilePath << std::endl;
      exit(1);
   }

   resip::Data httpsDiffieHellmanParamsFilePath;
   if (fileConfig.getConfigValue("auth_service_http_dh_params_file_path", httpsDiffieHellmanParamsFilePath))
   {
      serverConfig.httpsDiffieHellmanParamsFilePath = httpsDiffieHellmanParamsFilePath.c_str();
   }
   if (!std::filesystem::exists(serverConfig.httpsDiffieHellmanParamsFilePath.c_str()))
   {
      std::cerr << "auth_service_http_dh_params_file_path file not found: " << serverConfig.httpsDiffieHellmanParamsFilePath << std::endl;
      exit(1);
   }

   resip::Data certFilePath = "p256-private-key.p8";
   serverConfig.certificateFilePath = certFilePath.c_str();
   if (!std::filesystem::exists(serverConfig.certificateFilePath.c_str()))
   {
      std::cerr << "certificate_file_path file not found: " << serverConfig.certificateFilePath << std::endl;
      exit(1);
   }

   ++mTotalPrivilegedAccesses;
   mAuthServer->setOnPrivilegedAccessCompleted(&SdkManager::onPrivilegedAccessCompleted, this);
   mAuthServer->start(serverConfig);

   mAuthServer->addUser("counterpath_guest", "password");
}

int getRaftNodeId()
{
   std::ifstream in("raftNodeId.txt");
   if (in.is_open())
   {
      std::ostringstream oss;
      oss << in.rdbuf() << std::flush;

      resip::Data doc(oss.str().c_str(), oss.str().size());
      in.close();
      return doc.convertInt();
   }
   int newNodeId = 1 + (std::abs(resip::Random::getCryptoRandom()) % 0x7ffe);
   std::ofstream nodeidout("raftNodeId.txt", std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
   if (nodeidout.is_open())
   {
      nodeidout << newNodeId;
      nodeidout.close();
      return newNodeId;
   }
   return -1;
}

void SdkManager::initConf()
{
   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_conf.config");

   fileConfig.getConfigValue("sdk_logging_enabled", mJsonApiConfig->sdkLoggingEnabled);

   mMediaReactorFactory = CPCAPI2::Media::MediaTransportsReactorFactory::create();
   mMediaReactorFactory->initialize();
   mMediaReactorFactory->addRef();

   mOrchServer = CPCAPI2::OrchestrationServer::OrchestrationServer::getInterface(mPhone);
   mOrchServer->setHandler(this);
   CPCAPI2::OrchestrationServer::OrchestrationServerConfig orchServerConfig;
   orchServerConfig.redisIp = "mock";
   orchServerConfig.redisPort = 0;

   mOrchServer->start(orchServerConfig);

   if (!fileConfig.getConfigValue("json_api_websocket_port", mJsonApiConfig->jsonApiWebsocketPort))
   {
      return;
   }
   if (!fileConfig.getConfigValue("json_api_http_port", mJsonApiConfig->jsonApiHttpPort))
   {
      return;
   }
   int videoWebsocketPort = 9005;
   if (!fileConfig.getConfigValue("video_websocket_port", videoWebsocketPort))
   {
      return;
   }

   bool httpUseSecure = false;
   fileConfig.getConfigValue("json_api_http_use_secure", httpUseSecure);

   resip::Data httpCertFile;
   fileConfig.getConfigValue("json_api_http_certificate_file", httpCertFile);
   if (!std::filesystem::exists(httpCertFile.c_str()))
   {
      std::cerr << "json_api_http_certificate_file file not found: " << httpCertFile << std::endl;
      exit(1);
   }

   resip::Data httpPrivKeyFile;
   fileConfig.getConfigValue("json_api_http_private_key_file", httpPrivKeyFile);
   if (!std::filesystem::exists(httpPrivKeyFile.c_str()))
   {
      std::cerr << "json_api_http_private_key_file file not found: " << httpPrivKeyFile << std::endl;
      exit(1);
   }

   resip::Data httpsDiffieHellmanParamsFilePath;
   fileConfig.getConfigValue("json_api_http_diffie_hellman_params", httpsDiffieHellmanParamsFilePath);

   bool wsUseSecure = false;
   fileConfig.getConfigValue("json_api_websocket_use_secure", wsUseSecure);

   resip::Data wsCertFile;
   fileConfig.getConfigValue("json_api_websocket_certificate_file", wsCertFile);
   if (!std::filesystem::exists(wsCertFile.c_str()))
   {
      std::cerr << "json_api_websocket_certificate_file file not found: " << wsCertFile << std::endl;
      exit(1);
   }

   resip::Data wsPrivKeyFile;
   fileConfig.getConfigValue("json_api_websocket_private_key_file", wsPrivKeyFile);
   if (!std::filesystem::exists(wsPrivKeyFile.c_str()))
   {
      std::cerr << "json_api_websocket_private_key_file file not found: " << wsPrivKeyFile << std::endl;
      exit(1);
   }

   resip::Data wsDiffieHellmanParams;
   fileConfig.getConfigValue("json_api_websocket_diffie_hellman_params", wsDiffieHellmanParams);

   if (!std::filesystem::exists("p256-public-key.spki"))
   {
      std::cerr << "p256-public-key.spki file not found" << std::endl;
      exit(1);
   }

   mJsonApiServer = CPCAPI2::JsonApi::JsonApiServer::getInterface(mPhone);
   mJsonApiServer->setHandler(this);
   mJsonApiServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::JsonApi::JsonApiServerConfig serverConfig;
   serverConfig.jsonApiVersion = 7;
   serverConfig.websocketPort = mJsonApiConfig->jsonApiWebsocketPort;
   serverConfig.httpPort = mJsonApiConfig->jsonApiHttpPort;
   serverConfig.certificateFilePath = "p256-public-key.spki";
   serverConfig.apiLoggingEnabled = false;
   serverConfig.numThreads = std::min<int>(4, std::thread::hardware_concurrency());

   if (httpUseSecure)
   {
      ++mTotalPrivilegedAccesses;
      serverConfig.httpsCertificateFilePath = httpCertFile.c_str();
      serverConfig.httpsPrivateKeyFilePath = httpPrivKeyFile.c_str();
      serverConfig.httpsDiffieHellmanParamsFilePath = httpsDiffieHellmanParamsFilePath.c_str();
   }
   if (wsUseSecure)
   {
      ++mTotalPrivilegedAccesses;
      serverConfig.wssCertificateFilePath = wsCertFile.c_str();
      serverConfig.wssPrivateKeyFilePath = wsPrivKeyFile.c_str();
      serverConfig.wssDiffieHellmanParamsFilePath = wsDiffieHellmanParams.c_str();
   }
   mJsonApiServer->setOnPrivilegedAccessCompleted(&SdkManager::onPrivilegedAccessCompleted, this);
   mJsonApiServer->start(serverConfig);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(mPhone)->setJsonApiServer(mJsonApiServer);

   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      ++mTotalPrivilegedAccesses;
      CPCAPI2::VideoStreaming::VideoStreamingServerConfig vsConfig;
      vsConfig.listenPort = videoWebsocketPort;
      vsConfig.wssCertificateFilePath = wsCertFile.c_str();
      vsConfig.wssPrivateKeyFilePath = wsPrivKeyFile.c_str();
      vsConfig.wssDiffieHellmanParamsFilePath = wsDiffieHellmanParams.c_str();
      videoStreamingMgr->setOnPrivilegedAccessCompleted(&SdkManager::onPrivilegedAccessCompleted, this);
      videoStreamingMgr->startVideoStreamingServer(vsConfig);
   }

   //CPCAPI2::AudioTrans::AudioTransManager* audioTransMgr = CPCAPI2::AudioTrans::AudioTransManager::getInterface(mPhone);
   //if (audioTransMgr != NULL)
   //{
   //   audioTransMgr->startAudioTransServer();
   //}

   //mJsonApiServer->setJsonApiUserContext((CPCAPI2::JsonApi::JsonApiUserHandle)UINT_MAX, mPhone);

   if (auto httpServer = dynamic_cast<CPCAPI2::JsonApi::HttpServerInternal*>(mJsonApiServer))
   {
      httpServer->addRequestHandler("^/ccApi/getAsyncResult$", &mRestAsyncResultHandler);
      httpServer->addRequestHandler("^/statusApi/build$", &mBuildInfo);
      httpServer->addRequestHandler("^/ccApi/sessionSummary$", &mSessionSummaryReqHandler);
      httpServer->addRequestHandler("^/ccApi/crash$", &mCrashReqHandler);
      httpServer->addRequestHandler("^/ccApi/connectedEndpoints$", &mConnectedEndpointSummaryReqHandler);
      httpServer->addRequestHandler("^/ccApi/enterMaintenanceMode$", &mMaintMode);
      httpServer->addRequestHandler("^/ccApi/enableLogging$", &mLoggingEnableHandler);
      httpServer->addRequestHandler("^/ccApi/disableLogging$", &mLoggingDisableHandler);
      httpServer->addRequestHandler("^/ccApi/getLoggingStatus$", &mLoggingStatusReqHandler);
      httpServer->addRequestHandler("^/ccApi/raftListNodes$", &mRaftListNodes);
   }

   resip::Data defaultServerHostname;
   if (!fileConfig.getConfigValue("server_hostname", defaultServerHostname))
   {
      return;
   }

   resip::Data natServerHostname;
   if (!fileConfig.getConfigValue("nat_traversal_server_hostname", natServerHostname))
   {
      return;
   }
   else
   {
      resip::Data natServerUsername;
      if (!fileConfig.getConfigValue("nat_traversal_server_username", natServerUsername))
      {
         return;
      }
      resip::Data natServerPassword;
      if (!fileConfig.getConfigValue("nat_traversal_server_password", natServerPassword))
      {
         return;
      }
      int natServerType = (int)CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn;
      if (!fileConfig.getConfigValue("nat_traversal_server_type", natServerType))
      {
         return;
      }
      int natServerType2 = (int)CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType_StunAndTurn;
      if (!fileConfig.getConfigValue("nat_traversal_server_type2", natServerType2))
      {
         return;
      }
      mNatServerInfo.natTraversalServerHostname = natServerHostname.c_str();
      mNatServerInfo.natTraversalServerUsername = natServerUsername.c_str();
      mNatServerInfo.natTraversalServerPassword = natServerPassword.c_str();
      mNatServerInfo.natTraversalServerType = (CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType)natServerType;
      mNatServerInfo.natTraversalServerType2 = (CPCAPI2::ConferenceBridge::ConferenceNatTraversalServerInfo::NatTraversalServerType)natServerType2;
   }

   resip::Data serverPublicIp;
   if (fileConfig.getConfigValue("server_public_ip_for_media", serverPublicIp))
   {
      mNatServerInfo.serverPublicIpAddress = serverPublicIp.c_str();
   }

   int conferenceMediaEncryptionMode = 2; // DTLS
   if (fileConfig.getConfigValue("conference_media_encryption_mode", conferenceMediaEncryptionMode))
   {
      conferenceMediaEncryptionMode = std::max<int>(0, conferenceMediaEncryptionMode);
      conferenceMediaEncryptionMode = std::min<int>(2, conferenceMediaEncryptionMode);
      mMediaEncryptionMode = (CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode)conferenceMediaEncryptionMode;
   }
   else
   {
      mMediaEncryptionMode = CPCAPI2::ConferenceBridge::ConferenceMediaEncryptionMode_SRTP_DTLS;
   }

   int screenshareVideoMaxBitrateKbps = -1;
   if (fileConfig.getConfigValue("screenshare_video_max_bitrate_kbps", screenshareVideoMaxBitrateKbps))
   {
      mScreenshareBitrateConfig.videoMaxBitrateKbps = screenshareVideoMaxBitrateKbps;
   }
   int screenshareVideoTargetBitrateKbps = -1;
   if (fileConfig.getConfigValue("screenshare_video_target_bitrate_kbps", screenshareVideoTargetBitrateKbps))
   {
      mScreenshareBitrateConfig.videoTargetBitrateKbps = screenshareVideoTargetBitrateKbps;
   }
   int screenshareVideoStartBitrateKbps = -1;
   if (fileConfig.getConfigValue("screenshare_video_start_bitrate_kbps", screenshareVideoStartBitrateKbps))
   {
      mScreenshareBitrateConfig.videoStartBitrateKbps = screenshareVideoStartBitrateKbps;
   }
   int screenshareVideoMinBitrateKbps = -1;
   if (fileConfig.getConfigValue("screenshare_video_min_bitrate_kbps", screenshareVideoMinBitrateKbps))
   {
      mScreenshareBitrateConfig.videoMinBitrateKbps = screenshareVideoMinBitrateKbps;
   }

   int videoMaxBitrateKbps = -1;
   if (fileConfig.getConfigValue("camera_video_max_bitrate_kbps", videoMaxBitrateKbps))
   {
      mCameraVideoBitrateConfig.videoMaxBitrateKbps = videoMaxBitrateKbps;
   }
   int videoTargetBitrateKbps = -1;
   if (fileConfig.getConfigValue("camera_video_target_bitrate_kbps", videoTargetBitrateKbps))
   {
      mCameraVideoBitrateConfig.videoTargetBitrateKbps = videoTargetBitrateKbps;
   }
   int videoStartBitrateKbps = -1;
   if (fileConfig.getConfigValue("camera_video_start_bitrate_kbps", videoStartBitrateKbps))
   {
      mCameraVideoBitrateConfig.videoStartBitrateKbps = videoStartBitrateKbps;
   }
   int videoMinBitrateKbps = -1;
   if (fileConfig.getConfigValue("camera_video_min_bitrate_kbps", videoMinBitrateKbps))
   {
      mCameraVideoBitrateConfig.videoMinBitrateKbps = videoMinBitrateKbps;
   }

   fileConfig.getConfigValue("use_server_uid_in_join_urls", mUseServerUidInJoinUrls);

   fileConfig.getConfigValue("json_api_websocket_use_secure", mJsonApiConfig->jsonApiWebsocketUseSecure);
   fileConfig.getConfigValue("json_api_http_use_secure", mJsonApiConfig->jsonApiHttpUseSecure);
   fileConfig.getConfigValue("json_api_websocket_hostname", mJsonApiConfig->jsonApiWebsocketHostname);
   if (mJsonApiConfig->jsonApiWebsocketHostname.empty())
   {
      mJsonApiConfig->jsonApiWebsocketHostname = defaultServerHostname;
   }
   fileConfig.getConfigValue("json_api_http_hostname", mJsonApiConfig->jsonApiHttpHostname);
   if (mJsonApiConfig->jsonApiHttpHostname.empty())
   {
      mJsonApiConfig->jsonApiHttpHostname = defaultServerHostname;
   }

   resip::Data joinUrlBase;
   fileConfig.getConfigValue("conference_join_url", joinUrlBase);

   if (joinUrlBase.empty())
   {
      resip::DataStream ds(joinUrlBase);
      ds << (httpUseSecure ? "https://" : "http://");
      ds << mJsonApiConfig->jsonApiHttpHostname;
      if (mJsonApiConfig->jsonApiHttpPort != 443 && mJsonApiConfig->jsonApiHttpPort != 80)
      {
         ds << ":" << mJsonApiConfig->jsonApiHttpPort;
      }
   }
   mJsonApiConfig->joinUrlBase = joinUrlBase;

   resip::Data joinClusterUrl;
   fileConfig.getConfigValue("join_cluster_url", joinClusterUrl);

   resip::Data authUrl;
   fileConfig.getConfigValue("auth_service_url", authUrl);

   resip::Data authKey;
   fileConfig.getConfigValue("auth_service_api_key", authKey);

   resip::Data serverUid;
   fileConfig.getConfigValue("server_uid", serverUid);

   if (serverUid.empty())
   {
      serverUid = "AA";
   }
   mJsonApiConfig->serverUid = serverUid;

   std::stringstream ssThisServerUrl;
   ssThisServerUrl << (mJsonApiConfig->jsonApiWebsocketUseSecure ? "wss://" : "ws://");
   ssThisServerUrl << mJsonApiConfig->jsonApiWebsocketHostname;
   ssThisServerUrl << ":";
   ssThisServerUrl << mJsonApiConfig->jsonApiWebsocketPort;

   mConfRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mPhone);
   mNodeId = getRaftNodeId();
   if (mConfRegistrar != NULL && mNodeId != -1)
   {
      mConfRegistrar->setHandler(this);
      mConfRegistrar->setCallbackHook(SdkManager_sdkCallbackHook, this);

      resip::Data raftInterfaceIp = "0.0.0.0";
      fileConfig.getConfigValue("raft_interface_ip", raftInterfaceIp);

      CPCAPI2::ConferenceBridge::ConferenceRegistrarConfig confRegistrarConfig;
      confRegistrarConfig.conferenceRegistrarServiceIp = raftInterfaceIp.c_str();
      confRegistrarConfig.conferenceRegistrarServicePort = 2114; // raftPort;
      fileConfig.getConfigValue("raft_port", confRegistrarConfig.conferenceRegistrarServicePort);
      confRegistrarConfig.nodeId = mNodeId;
      confRegistrarConfig.wsUrlBase = ssThisServerUrl.str().c_str();
      confRegistrarConfig.joinClusterUrl = joinClusterUrl.c_str();
      confRegistrarConfig.jsonApiHostname = mJsonApiConfig->jsonApiHttpHostname.c_str();
      confRegistrarConfig.authServiceUrl = authUrl.c_str();
      confRegistrarConfig.authServiceApiKey = authKey.c_str();
      mConfRegistrar->start(confRegistrarConfig);
   }

   fileConfig.getConfigValue("create_root_conference", mCreateRootConference);

   cpc::string confServiceId = CPCAPI2::ConferenceBridge::ConferenceBridgeManager::getServiceId();
   registerWithOrchServer(confServiceId, "", "", "", "inproc.local", "LOCAL", ssThisServerUrl.str().c_str());

   initFromSettings();

   writeSessionSummary();
}

void SdkManager::registerWithOrchServer(const cpc::string& serviceId, const cpc::string& authServerUrl, const cpc::string& username, const cpc::string& password, const cpc::string& orchServerUrl, const cpc::string& thisServerRegion, const cpc::string& thisServerUrl)
{
   CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudServiceConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(mPhone);
   if (cloudServiceConfigMgr != NULL)
   {
      CPCAPI2::OrchestrationServer::ServerInfo thisServerInfo;
      thisServerInfo.region = thisServerRegion;
      thisServerInfo.uri = thisServerUrl;
      thisServerInfo.services.push_back(serviceId);
      ServiceConfigSettings serviceConfigSettings;
      serviceConfigSettings.authServerUrl = authServerUrl;
      serviceConfigSettings.orchestrationServerUrl = orchServerUrl;
      serviceConfigSettings.username = username;
      serviceConfigSettings.password = password;
      cloudServiceConfigMgr->setServerInfo(serviceConfigSettings, thisServerInfo);
      {
         //CloudServiceConfigHandle h;
         //SetServerInfoResult args;
         //cpcExpectEvent((&cloudConfigEvents), "CloudServiceConfigHandler::onSetServerInfoSuccess", 5000, CPCAPI2::test::AlwaysTruePred(), h, args);
      }
   }
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   mLoggingTimer.cancel();
   mIsLoggingEnabled = false;

   std::map<resip::Data, Cpcapi2Runner*> contextMapCpy = mMapContextToRunner;
   std::map<resip::Data, Cpcapi2Runner*>::iterator it = contextMapCpy.begin();
   for (; it != contextMapCpy.end(); ++it)
   {
      Cpcapi2Runner* runner = it->second;
      runner->shutdown();
      delete runner;
   }

   mJsonApiServer->shutdown();
   CPCAPI2::VideoStreaming::VideoStreamingManager* videoStreamingMgr = CPCAPI2::VideoStreaming::VideoStreamingManager::getInterface(mPhone);
   if (videoStreamingMgr != NULL)
   {
      videoStreamingMgr->stopVideoStreamingServer();
   }

   mMediaReactorFactory->releaseRef();

   mAuthServer->shutdown();
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
   for (int i = 0; i < 10; i++)
   {
      resip::Data settingsFileName;
      {
         resip::DataStream ds(settingsFileName);
         ds << "settings";
         ds << i;
         ds << ".json";
      }
      std::ifstream in(settingsFileName.c_str());
      if (in.is_open())
      {
         std::ostringstream oss;
         oss << in.rdbuf() << std::flush;

         resip::Data doc(oss.str().c_str(), oss.str().size());

         Cpcapi2Runner* sdk = new Cpcapi2Runner("counterpath", "counterpath", doc, "", i%CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mJsonApiServer, mMediaReactorFactory, *mJsonApiConfig, mNatServerInfo, mScreenshareBitrateConfig, mCameraVideoBitrateConfig, mMediaEncryptionMode, mUseServerUidInJoinUrls, mCreateRootConference);
         sdk->createFullConference();
         mMapContextToRunner["counterpath"] = sdk;
      }
      else
      {
         break;
      }
   }

   if (std::filesystem::exists("confsettings"))
   {
      int j = 0;
      for (auto& p : std::filesystem::directory_iterator("confsettings"))
      {
         std::error_code ec;
         if (p.is_regular_file(ec))
         {
            if (!ec)
            {
               std::cout << p.path().filename() << std::endl;
               std::ifstream in(p.path());
               if (in.is_open())
               {
                  std::ostringstream oss;
                  oss << in.rdbuf() << std::flush;

                  resip::Data doc(oss.str().c_str(), oss.str().size());
                  std::string contextStr = p.path().stem().generic_string();
                  Cpcapi2Runner* sdk = new Cpcapi2Runner("", contextStr.c_str(), "", p.path().generic_string().c_str(), j % CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mJsonApiServer, mMediaReactorFactory, *mJsonApiConfig, mNatServerInfo, mScreenshareBitrateConfig, mCameraVideoBitrateConfig, mMediaEncryptionMode, mUseServerUidInJoinUrls, mCreateRootConference);
                  mMapContextToRunner[contextStr.c_str()] = sdk;
                  j++;
               }
            }
         }
      }
   }
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onStartupComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ConferenceRegistrarStartupResult& args)
{
   InfoLog(<< "ConferenceRegistrarHandler::onStartupComplete() - result: " << args.success << "; localIsLeader: " + args.localIsLeader);

   mIsLeader = args.localIsLeader;
   return 0;
}

int SdkManager::onNewLogin(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::NewLoginEvent& args)
{
   resip::Data contextStr;
   resip::Data confConfigFilename;

   if (args.resource.size() > 1) // might be empty or '/'
   {
      contextStr = args.resource.c_str();
      if (contextStr.prefix("/"))
      {
         contextStr = contextStr.substr(1);
      }
      if (contextStr.prefix("screenshare"))
      {
         contextStr = contextStr.substr(12);
      }
      if (contextStr.at(2) == '/')
      {
         contextStr = contextStr.substr(3);
      }
      if (contextStr.postfix("/"))
      {
         contextStr = contextStr.substr(0, contextStr.size() - 1);
      }
      if (contextStr.prefix("jsonApi"))
      {
         contextStr = args.userIdentityAnonymized;
         confConfigFilename = "jsonApi";
      }
   }
   else
   {
      contextStr = args.userIdentityAnonymized;
   }

   InfoLog(<< "onNewLogin - context: " << contextStr);

   bool loginSuccess = false;
   bool delayLoginResult = false;

   if (jsonApiUser != 0)
   {
      std::map<resip::Data, Cpcapi2Runner*>::const_iterator it = mMapContextToRunner.find(contextStr);
      if (it != mMapContextToRunner.end())
      {
         Cpcapi2Runner* sdkInst = it->second;
         if (args.requestedResources.empty() || sdkInst->isValidResource(args.requestedResources))
         {
            DebugLog(<< "onNewLogin - found existing context for " << contextStr);

            mMapJsonApiUserToRunner[jsonApiUser] = sdkInst;
            sdkInst->addJsonApiUser(jsonApiUser, args.requestedResources);
            sdkInst->updateJoinUrl(args.realm);
            loginSuccess = true;

            cpc::vector<cpc::string> permissions;
            permissions.push_back("*");
            mJsonApiServer->setJsonApiUserContext(jsonApiUser, sdkInst->getPhone(), permissions);
         }
         else
         {
            DebugLog(<< "onNewLogin - requested resource is not a valid match: " << (args.requestedResources.size() > 0 ? args.requestedResources[0] : ""));
         }
      }
      else
      {
         if (args.userIdentity == "counterpath_guest")
         {
            // guest users are not allowed to create a context for themselves (no API key)
         }
         else
         {
            Cpcapi2Runner* sdk = new Cpcapi2Runner(args.userIdentity.c_str(), contextStr, resip::Data::Empty, confConfigFilename, sNextSdkThreadPoolIdx % CPCAPI2_CONF_NUM_SDK_REACTORS, this, &mReactor, mPhone, mJsonApiServer, mMediaReactorFactory, *mJsonApiConfig, mNatServerInfo, mScreenshareBitrateConfig, mCameraVideoBitrateConfig, mMediaEncryptionMode, mUseServerUidInJoinUrls, mCreateRootConference, args.realm, args.requestedResources);
            sdk->setLoggingEnabled(false /*mIsLoggingEnabled*/);
            sNextSdkThreadPoolIdx++;
            mMapContextToRunner[contextStr] = sdk;
            DebugLog(<< "onNewLogin - creating a new context for " << contextStr);
            mMapJsonApiUserToRunner[jsonApiUser] = sdk;
            sdk->addJsonApiUser(jsonApiUser, args.requestedResources);
            loginSuccess = true;

            cpc::vector<cpc::string> permissions;
            permissions.push_back("*");
            mJsonApiServer->setJsonApiUserContext(jsonApiUser, sdk->getPhone(), permissions);

            writeSessionSummaryImpl();
         }
      }
   }

   if (!delayLoginResult)
   {
      CPCAPI2::JsonApi::LoginResultEvent loginResult;
      loginResult.success = loginSuccess;
      mJsonApiServer->sendLoginResult(jsonApiUser, loginResult);
   }

   if (loginSuccess && !args.isHttp)
   {
      mMapJsonApiUserToEndpointInfo[jsonApiUser].userIdentity = args.userIdentity;
      writeConnectedEndpointSummary();
   }

   return 0;
}

int SdkManager::onConnectionClosed(CPCAPI2::JsonApi::JsonApiUserHandle jsonApiUser, const CPCAPI2::JsonApi::ConnectionClosedEvent& args)
{
   mMapJsonApiUserToEndpointInfo.erase(jsonApiUser);
   writeConnectedEndpointSummary();
   return 0;
}

void SdkManager::writeSessionSummary()
{
   mReactor.post(resip::resip_bind(&SdkManager::writeSessionSummaryImpl, this));
}

void SdkManager::writeSessionSummaryImpl()
{
   // format: { contexts: [ { context: <EMAIL>, conferenceSummary: { ... } }, ...] }
   rapidjson::Document respJson;
   respJson.SetObject();

   rapidjson::Value contextsArray(rapidjson::kArrayType);
   auto it = mMapContextToRunner.begin();
   for (; it != mMapContextToRunner.end(); ++it)
   {
      cpc::string confSummary;
      CPCAPI2::Phone* sdkPhone = it->second->getPhone();
      CPCAPI2::ConferenceBridge::ConferenceBridgeManagerInternal* confBridgeMgr = CPCAPI2::ConferenceBridge::ConferenceBridgeManagerInternal::getInternalInterface(sdkPhone);
      confBridgeMgr->getConferenceSummary(confSummary);

      rapidjson::Document confSummaryDoc(&respJson.GetAllocator());
      confSummaryDoc.Parse<0>(confSummary.c_str());

      rapidjson::Value contextVal(rapidjson::kObjectType);
      std::string userContextStr = it->first.c_str();
      rapidjson::Value userContextVal(userContextStr.c_str(), respJson.GetAllocator());
      contextVal.AddMember("context", userContextVal, respJson.GetAllocator());
      resip::Data userIdentityStr = it->second->getUserIdentity();
      rapidjson::Value contextOwnerVal(userIdentityStr.c_str(), respJson.GetAllocator());
      contextVal.AddMember("owner", contextOwnerVal, respJson.GetAllocator());
      contextVal.AddMember("conferenceSummary", confSummaryDoc, respJson.GetAllocator());
      contextsArray.PushBack(contextVal, respJson.GetAllocator());
   }

   respJson.AddMember("contexts", contextsArray, respJson.GetAllocator());

   rapidjson::StringBuffer rjbuffer(0, 2048);
   rapidjson::PrettyWriter<rapidjson::StringBuffer> rjwriter(rjbuffer);
   respJson.Accept(rjwriter);

   resip::Lock lck(mSessionSummaryMtx);
   mSessionSummary = cpc::string(rjbuffer.GetString(), rjbuffer.GetSize());
}

void SdkManager::writeConnectedEndpointSummary()
{
   // format: { endpoints: [ { jsonApiUser: 1234, userIdentity: <EMAIL> ), ... ] }
   rapidjson::Document respJson;
   respJson.SetObject();

   rapidjson::Value endpointsArray(rapidjson::kArrayType);
   auto it = mMapJsonApiUserToEndpointInfo.begin();
   for (; it != mMapJsonApiUserToEndpointInfo.end(); ++it)
   {
      rapidjson::Value endpointVal(rapidjson::kObjectType);
      endpointVal.AddMember("jsonApiUser", it->first, respJson.GetAllocator());
      rapidjson::Value userIdentityVal(it->second.userIdentity.c_str(), respJson.GetAllocator());
      endpointVal.AddMember("userIdentity", userIdentityVal, respJson.GetAllocator());
      endpointsArray.PushBack(endpointVal, respJson.GetAllocator());
   }

   respJson.AddMember("endpoints", endpointsArray, respJson.GetAllocator());

   rapidjson::StringBuffer rjbuffer(0, 2048);
   rapidjson::PrettyWriter<rapidjson::StringBuffer> rjwriter(rjbuffer);
   respJson.Accept(rjwriter);

   resip::Lock lck(mConnectedEndpointSummaryMtx);
   mConnectedEndpointSummary = cpc::string(rjbuffer.GetString(), rjbuffer.GetSize());
}
int SdkManager::onSetServerInfoResult(int requestHandle, const CPCAPI2::OrchestrationServer::SetServerInfoResult& args)
{
   if (!args.success)
   {
      //ErrLog(<< "error registering server in Orch Service database");
   }
   return 0;
}

void SdkManager::onTimer(unsigned short timerId, void* appState)
{
   if (timerId == SDKMANAGER_LOGGING_TIMER)
   {
      mPhone->setLoggingEnabled("cpcconf", false);
      mIsLoggingEnabled = false;

      std::map<resip::Data, Cpcapi2Runner*> contextMapCpy = mMapContextToRunner;
      std::map<resip::Data, Cpcapi2Runner*>::iterator it = contextMapCpy.begin();
      for (; it != contextMapCpy.end(); ++it)
      {
         Cpcapi2Runner* runner = it->second;
         runner->setLoggingEnabled(false);
      }
   }
}

void SdkManager::setLoggingTimer(int expires_ms)
{
   mLoggingTimer.cancel();
   mReactor.post(resip::resip_bind(&SdkManager::setLoggingTimerImpl, this, expires_ms));
}

void SdkManager::setLoggingTimerImpl(int expires_ms)
{
   mLoggingTimer.expires_from_now(expires_ms);
   mLoggingTimer.async_wait(this, SDKMANAGER_LOGGING_TIMER, NULL);
}

int SdkManager::nextTicketId()
{
   return mRestAsyncResultHandler.nextTicketId();
}

void SdkManager::postAsyncResult(int ticketId, const cpc::string& res)
{
   mRestAsyncResultHandler.setResult(ticketId, res);
}

class MyListNodesHandler : public CPCAPI2::ConferenceBridge::ListNodesHandler
{
public:
   MyListNodesHandler(const std::function<void(const cpc::vector<cpc::string>&)>& fn) : mFn(fn) {}
   virtual ~MyListNodesHandler() {}

   virtual int onListNodesComplete(CPCAPI2::ConferenceBridge::ConferenceRegistrarHandle registrar, const CPCAPI2::ConferenceBridge::ListNodesResult& args) override {
      mFn(args.nodes);
      delete this;
      return 0;
   }

private:
   std::function<void(const cpc::vector<cpc::string>&)> mFn;
};

void SdkManager::listNodes(const std::function<void(const cpc::vector<cpc::string>&)>& fn)
{
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mPhone);
   confRegistrar->listNodes(new MyListNodesHandler(fn));
}

SdkManager::RestAsyncResult::RestAsyncResult(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

int SdkManager::RestAsyncResult::processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString)
{
   int ticketId = -1;
   mRespCode = 404;

   for (const CPCAPI2::JsonApi::QueryStringParam& qsp : queryString)
   {
      if (qsp.name == "ticketId")
      {
         ticketId = resip::Data::from(qsp.value.c_str()).convertInt();
      }
   }

   if (ticketId != -1)
   {
      auto it = mResultMap.find(ticketId);
      if (it != mResultMap.end())
      {
         mNextResult = it->second;
         mResultMap.erase(it);
         mRespCode = 200;
      }
   }
   return 0;
}

cpc::string SdkManager::RestAsyncResult::output()
{
   cpc::string ret = mNextResult;
   mNextResult = "";
   if (ret.empty())
   {
      ret = "ERR";
   }
   return ret;
}

int SdkManager::RestAsyncResult::response_code()
{
   return mRespCode;
}

SdkManager::BuildInfo::BuildInfo(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::BuildInfo::output()
{
   return BUILD_INFO_STRING;
}

int SdkManager::BuildInfo::response_code()
{
   if (mSdkMgr->mMaintenanceModeEnabled)
   {
      return 410; // client_error_gone
   }

   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mSdkMgr->mPhone);
   if (confRegistrar == NULL)
   {
      return 410;
   }
   if (!confRegistrar->isInService())
   {
      return 410;
   }
   return 200;
}

SdkManager::MaintMode::MaintMode(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::MaintMode::output()
{
   mSdkMgr->mMaintenanceModeEnabled = true;
   CPCAPI2::ConferenceBridge::ConferenceRegistrar* confRegistrar = CPCAPI2::ConferenceBridge::ConferenceRegistrar::getInterface(mSdkMgr->mPhone);
   confRegistrar->shutdown();
   return "OK";
}

SdkManager::LoggingToggle::LoggingToggle(SdkManager* sdkMgr, bool enable) : mSdkMgr(sdkMgr), mEnable(enable)
{
}

cpc::string SdkManager::LoggingToggle::output()
{
   mSdkMgr->mPhone->setLoggingEnabled("cpcconf", mEnable);
   mSdkMgr->mIsLoggingEnabled = mEnable;

   std::map<resip::Data, Cpcapi2Runner*> contextMapCpy = mSdkMgr->mMapContextToRunner;
   std::map<resip::Data, Cpcapi2Runner*>::iterator it = contextMapCpy.begin();
   for (; it != contextMapCpy.end(); ++it)
   {
      Cpcapi2Runner* runner = it->second;
      runner->setLoggingEnabled(mEnable);
   }
   if (mEnable)
   {
      mSdkMgr->setLoggingTimer(1000 * 60 * 60 * 48); // 48 hours
   }
   return "OK";
}

SdkManager::LoggingStatus::LoggingStatus(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::LoggingStatus::output()
{
   return resip::Data::from(mSdkMgr->mIsLoggingEnabled).c_str();
}

SdkManager::SessionSummaryRequestHandler::SessionSummaryRequestHandler(SdkManager* sdkMgr)
   : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::SessionSummaryRequestHandler::output()
{
   cpc::string retVal;
   {
      resip::Lock lck(mSdkMgr->mSessionSummaryMtx);
      retVal = mSdkMgr->mSessionSummary;
   }
   mSdkMgr->writeSessionSummary();
   return retVal;
}

SdkManager::ConnectedEndpointSummaryRequestHandler::ConnectedEndpointSummaryRequestHandler(SdkManager* sdkMgr)
   : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::ConnectedEndpointSummaryRequestHandler::output()
{
   cpc::string retVal;
   {
      resip::Lock lck(mSdkMgr->mConnectedEndpointSummaryMtx);
      retVal = mSdkMgr->mConnectedEndpointSummary;
   }
   return retVal;
}

SdkManager::CrashRequest::CrashRequest(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

cpc::string SdkManager::CrashRequest::output()
{
   int* a = 0;
   *a = NULL;

   return cpc::string();
}

SdkManager::RaftListNodes::RaftListNodes(SdkManager* sdkMgr) : mSdkMgr(sdkMgr)
{
}

int SdkManager::RaftListNodes::processRequest(const cpc::string& path, const cpc::string& body, const CPCAPI2::JsonApi::AuthTokenInfo& authTokenInfo, const cpc::vector<CPCAPI2::JsonApi::QueryStringParam>& queryString)
{
   int ticketId = mSdkMgr->nextTicketId();
   mTicketId = ticketId;
   mSdkMgr->listNodes([this, ticketId](const cpc::vector<cpc::string>& nodes) {
      std::ofstream ofs ("raftNodeList.txt", std::ios_base::out | std::ios_base::binary | std::ios_base::trunc);
      std::stringstream ss;

      if (ofs.is_open())
      {
         // format: { nodeId: ..., isLeader: ..., nodes: [ nodeID, ... ] }
         rapidjson::Document doc;
         doc.SetObject();

         doc.AddMember("nodeId", mSdkMgr->mNodeId, doc.GetAllocator());
         doc.AddMember("isLeader", mSdkMgr->mIsLeader, doc.GetAllocator());

         rapidjson::Value nodeList (rapidjson::kArrayType);
         for (const cpc::string& n : nodes)
         {
            rapidjson::Document _doc;
            if (_doc.Parse<0>(n.c_str()).HasParseError() /*|| !_doc.IsObject()*/) continue;
            nodeList.PushBack(_doc.GetObj(), doc.GetAllocator());
         }
         doc.AddMember("nodes", nodeList, doc.GetAllocator());

         rapidjson::StringBuffer buffer(0, 2048);
         rapidjson::PrettyWriter<rapidjson::StringBuffer> writer (buffer);
         doc.Accept(writer);

         ss << buffer.GetString() << std::endl;
         ofs << ss.rdbuf();
      }

      mSdkMgr->postAsyncResult(ticketId, ss.str().c_str());
   });
   return 0;
}

cpc::string SdkManager::RaftListNodes::output()
{
   return resip::Data::from(mTicketId).c_str();
}

}
}
