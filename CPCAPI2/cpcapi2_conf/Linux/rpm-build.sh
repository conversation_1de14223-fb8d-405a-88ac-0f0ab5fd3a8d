#!/bin/bash
VERSION=1.0.0
RELEASE=1
ARCH=noarch

SCRIPT_DIR=$(dirname $(readlink -f $0))

options=hv:r:a:
longoptions=help,version:,release:,arch:

opts=$(getopt --options $options --longoptions $longoptions --name "$0" -- "$@")

[[ $? != 0 ]] && { echo "Invalid parameters" >&2; exit 1; }

eval set -- "$opts"

while true; do
  case "$1" in
    -h|--help)
      cat <<-EOF
Usage: $0 [OPTION]

  -h, --help:
                display this help and exit

  -v, --version=<version number>:
                specify the version number

  -r, --release=<release number>:
                specify the release number

  -a, --arch=<architecture>:
                specify the target architecture

EOF
      exit 0
      ;;

    -v|--version)
      VERSION="$2"
      shift 2
      ;;

    -r|--release)
      RELEASE="$2"
      shift 2
      ;;

    -a|--arch)
      ARCH="$2"
      shift 2
      ;;

    --)
      shift
      break
      ;;

    *)
      echo "Internal error"
      exit 1
      ;;
  esac
done

rpmbuild -bb --define "_topdir $SCRIPT_DIR/rpmbuild" --define "_VERSION $VERSION" --define "_RELEASE $RELEASE" $SCRIPT_DIR/rpmbuild/cpcconf.spec --target $ARCH
