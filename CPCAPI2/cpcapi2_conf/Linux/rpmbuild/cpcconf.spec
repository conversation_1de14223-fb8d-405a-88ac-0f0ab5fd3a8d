Summary: cpcconf
Name: cpcconf
Version: %_VERSION
Release: %_RELEASE
License: Commercial
URL: http://www.counterpath.com
Group: Applications/Communications
Packager: CounterPath Corporation
Requires: policycoreutils
Requires: openssl
Requires: systemd
Requires: httpd-tools

#%define _binaries_in_noarch_packages_terminate_build 0

%description
This is a cpcconf service installation package.

%prep
echo "_topdir = %_topdir"
echo "BUILDROOT = $RPM_BUILD_ROOT"
mkdir -p $RPM_BUILD_ROOT/opt/cpcconf
mkdir -p $RPM_BUILD_ROOT/opt/cpcconf/confsettings
mkdir -p $RPM_BUILD_ROOT/opt/cpcconf/setup
mkdir -p $RPM_BUILD_ROOT/etc/sysctl.d
mkdir -p $RPM_BUILD_ROOT/etc/security/limits.d
#mkdir -p $RPM_BUILD_ROOT/etc/pam.d
mkdir -p $RPM_BUILD_ROOT/usr/lib/systemd/system
mkdir -p $RPM_BUILD_ROOT/etc/nginx/conf.d

cp %_topdir/../build/cpcapi2_conf $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/cpcapi2_conf.config $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/cpcapi2_auth_service.config $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/../../p256-private-key.p8 $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/../../confjoin.wav $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/../../confleave.wav $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/../../logo.png $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/../../silence16.pcm $RPM_BUILD_ROOT/opt/cpcconf
cp %_topdir/setup.sh $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/config_value.sh $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/setup.wizard $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/keepalived-1.3.6-1.x86_64.rpm $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/createVIP $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/addVIP $RPM_BUILD_ROOT/opt/cpcconf/setup
cp %_topdir/default-context.config $RPM_BUILD_ROOT/opt/cpcconf/confsettings
cp %_topdir/cpcconf.sysctl.conf $RPM_BUILD_ROOT/etc/sysctl.d/66.cpcconf.sysctl.conf
cp %_topdir/cpcconf.limits.conf $RPM_BUILD_ROOT/etc/security/limits.d/66.cpcconf.limits.conf
cp %_topdir/cpcconf.service $RPM_BUILD_ROOT/usr/lib/systemd/system
cp %_topdir/cpcconf.nginx.conf $RPM_BUILD_ROOT/etc/nginx/conf.d/66.cpcconf.nginx.conf
exit

%files
%attr(0755, cpcconf, cpcconf) %dir /opt/cpcconf
%attr(0755, cpcconf, cpcconf) /opt/cpcconf/cpcapi2_conf
%attr(0666, cpcconf, cpcconf) %config(noreplace) /opt/cpcconf/cpcapi2_conf.config
%attr(0666, cpcconf, cpcconf) %config(noreplace) /opt/cpcconf/cpcapi2_auth_service.config
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/p256-private-key.p8
%attr(0644, cpcconf, cpcconf) %ghost /opt/cpcconf/p256-public-key.spki
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/confjoin.wav
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/confleave.wav
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/logo.png
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/silence16.pcm
%attr(0755, cpcconf, cpcconf) %dir /opt/cpcconf/setup
%attr(0755, cpcconf, cpcconf) /opt/cpcconf/setup/setup.sh
%attr(0755, cpcconf, cpcconf) /opt/cpcconf/setup/config_value.sh
%attr(0666, cpcconf, cpcconf) %config(noreplace) /opt/cpcconf/setup/setup.wizard
%attr(0644, cpcconf, cpcconf) /opt/cpcconf/setup/keepalived-1.3.6-1.x86_64.rpm
%attr(0755, cpcconf, cpcconf) /opt/cpcconf/setup/createVIP
%attr(0755, cpcconf, cpcconf) /opt/cpcconf/setup/addVIP
%attr(0644, cpcconf, cpcconf) %ghost /opt/cpcconf/dh2048.pem
%attr(0755, cpcconf, cpcconf) %dir /opt/cpcconf/confsettings
%attr(0666, cpcconf, cpcconf) /opt/cpcconf/confsettings/default-context.config
%attr(0666, -, -) /etc/sysctl.d/66.cpcconf.sysctl.conf
%attr(0666, -, -) /etc/security/limits.d/66.cpcconf.limits.conf
%attr(0666, -, -) /usr/lib/systemd/system/cpcconf.service
%attr(0666, -, -) %config(noreplace) /etc/nginx/conf.d/66.cpcconf.nginx.conf

%pre
useradd cpcconf -U || true

%post
exec 1>/proc/$PPID/fd/1
exec 2>/proc/$PPID/fd/2

echo ""

openssl dhparam -out /opt/cpcconf/dh2048.pem 2048
chown cpcconf.cpcconf /opt/cpcconf/dh2048.pem

openssl pkey -pubout -inform pem -outform pem -in /opt/cpcconf/p256-private-key.p8 -out /opt/cpcconf/p256-public-key.spki
chown cpcconf.cpcconf /opt/cpcconf/p256-public-key.spki

#cp /etc/ssh/sshd_config /etc/ssh/sshd_config.`date +%%Y%%m%%d%%H%%M%%S`
#sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/ssh/sshd_config
#echo "" >> /etc/ssh/sshd_config
#echo "#CPCCONF BEGIN *** DO NOT EDIT ***" >> /etc/ssh/sshd_config
#echo "UsePAM yes" >> /etc/ssh/sshd_config
#echo "#CPCCONF END   *** DO NOT EDIT ***" >> /etc/ssh/sshd_config

#sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/pam.d/sshd
#echo "" >> /etc/pam.d/sshd
#echo "#CPCCONF BEGIN *** DO NOT EDIT ***" >> /etc/pam.d/sshd
#echo "session required pam_limits.so" >> /etc/pam.d/sshd
#echo "#CPCCONF END   *** DO NOT EDIT ***" >> /etc/pam.d/sshd

cp /etc/rc.local /etc/rc.local.`date +%%Y%%m%%d%%H%%M%%S`
sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/rc.local
echo "" >> /etc/rc.local
echo "#CPCCONF BEGIN *** DO NOT EDIT ***" >> /etc/rc.local
echo "defrt=`ip route | grep '^default' | head -1`" >> /etc/rc.local
echo "ip route change $defrt initcwnd 10" >> /etc/rc.local
echo "#CPCCONF END   *** DO NOT EDIT ***" >> /etc/rc.local

sysctl --system

setsebool -P httpd_can_network_connect 1

systemctl enable cpcconf.service

echo "*** IMPORTANT *** Please run /opt/cpcconf/setup/setup.sh to proceed with the setup procedures"

%preun
service stop cpcconf.service

if [ "$1" == "0" ]; then
  systemctl disable cpcconf.service
fi

%postun
if [ "$1" == "0" ]; then
  userdel cpcconf

#  sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/ssh/sshd_config
#  sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/pam.d/sshd
  sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/rc.local
  sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/coturn/turnserver.conf
fi

sysctl --system

%clean
rm -rf $RPM_BUILD_ROOT

%changelog
#* :r!date "+\%a \%b \%d \%Y" Bill Liu <<EMAIL>>
#  - <Placeholder>
