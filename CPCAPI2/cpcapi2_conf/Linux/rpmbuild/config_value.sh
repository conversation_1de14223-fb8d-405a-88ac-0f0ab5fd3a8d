#!/bin/bash

trap 'echo "config_value error [$(basename $0):$LINENO]: $BASH_COMMAND"; exit 1;' ERR

#$1: file, $2: match_pattern, $3: replacement, $4: replace Nth match, default (blank) as exact one match, * for all
[[ ! -f "$1" ]] && { echo "Configuration file [$1] doesn't exist. Skip this configuration."; exit 1; }

pattern="$2"

count=`grep -c "$pattern" $1`
[[ $count -eq 0 ]] && { echo "Configuration file [$1] doesn't contain any pattern match of [$2]. Skip this configuration."; exit 1; }

[[ $4 = "*" ]] && { echo "Configuration file [$1]: Replacing all $count pattern match(es) of [$2]."; sed -i "s#$pattern#$3#" $1; exit; }

if [[ -z $4 ]]; then
  [[ $count -eq 1 ]]\
    && { echo "Configuration file [$1]: Replacing exact one pattern match of [$2]."; sed -i "s#$pattern#$3#" $1; exit; }\
    || { echo "Configuration file [$1]: More than exact one pattern match of [$2]. Skip this configuration."; exit 1; }
fi

[[ $4 -gt $count ]] && { echo "Configuration file [$1]: trying to replace Nth (N=$4) pattern match of [$2] while only contains $count match(es). Skip this configuration."; exit 1; }

echo "Configuration file [$1]: Replacing Nth (N=$4) out of $count pattern match(es) of [$2]."
sed -z -i "s#$pattern#$3#$4" $1
