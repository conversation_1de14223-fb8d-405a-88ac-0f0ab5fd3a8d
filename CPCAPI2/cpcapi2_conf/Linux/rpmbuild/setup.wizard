#*** DO NOT EDIT *** while setup.sh is running
#No method (MESSAGE, RESPOND, EXECUT<PERSON>, REQUI<PERSON>) can be nested
#RESPOND or REQUI<PERSON> must be single lined
#REQUI<PERSON> must return a boolean
#EXECUTE should not call any function which may fail internally
#----------------------------------------------------------------


EXECUTE
#openssl genpkey -algorithm EC -pkeyopt ec_paramgen_curve:P-256 -pkeyopt ec_param_enc:named_curve | openssl pkcs8 -topk8 -nocrypt -outform pem > /opt/cpcconf/p256-private-key.p8
#openssl pkey -pubout -inform pem -outform pem -in /opt/cpcconf/p256-private-key.p8 -out /opt/cpcconf/p256-public-key.spki
#letsencrypt and certbot
#----------------------------------------------------------------


MESSAGE Please enter the hostname of the current server which shall be referenced by various configurations throughout the entire setup process:
RESPOND  $HOSTNAME
EXECUTE SERVER_NAME=$RESPOND
#----------------------------------------------------------------


MESSAGE Please enter the public IP address of the current server which shall be referenced by various configurations throughout the entire setup process:
RESPOND  
EXECUTE PUBLIC_IP_ADDRESS=$RESPOND
#----------------------------------------------------------------


MESSAGE Please enter SSL certificate .key filename which is located under /etc/pki/tls/private (e.g. STAR_counterpath_com.key):
RESPOND  
EXECUTE
[[ ! -z $RESPOND && ${RESPOND#./} = $RESPOND && $(dirname "$RESPOND") = "." ]] && KEY_FILE_PATH="/etc/pki/tls/private/$RESPOND" || KEY_FILE_PATH=$(readlink -f "$RESPOND")
[[ ! -f $KEY_FILE_PATH ]] && add_reminder "Need to provide SSL certificate .key file [$KEY_FILE_PATH] to run PTT properly" || true
#----------------------------------------------------------------
MESSAGE Please enter SSL certificate .crt filename which is located under /etc/pki/tls/certs (e.g. STAR_counterpath_com.crt):
RESPOND  
EXECUTE
[[ ! -z $RESPOND && ${RESPOND#./} = $RESPOND && $(dirname "$RESPOND") = "." ]] && CRT_FILE_PATH="/etc/pki/tls/certs/$RESPOND" || CRT_FILE_PATH=$(readlink -f "$RESPOND")
[[ ! -f $CRT_FILE_PATH ]] && add_reminder "Need to provide SSL certificate .crt file [$CRT_FILE_PATH] to run PTT properly" || true
#----------------------------------------------------------------


MESSAGE Installing epel-release which is required by coturn and nginx...
EXECUTE yum list installed epel-release || yum install -y epel-release
#----------------------------------------------------------------


MESSAGE Would you like to configure a TURN server (coturn) on the current server for NAT traversal?
RESPOND <^(y|yes|n|no)$> y
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping configuration of coturn"; return; }

CONFIG_COTURN=1
if yum list installed coturn.*; then
  add_reminder "coturn has already been installed previously. Manual configuration of /etc/coturn/turnserver.conf may be required to run coturn properly"
else
  yum install -y coturn
fi

systemctl enable coturn
#----------------------------------------------------------------
REQUIRE [[ $CONFIG_COTURN -eq 1 ]]
#----------------------------------------------------------------
#MESSAGE Please enter coturn TURN listener port for UDP and TCP (Default: 3478):
#RESPOND  3478
#EXECUTE COTURN_PORT=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter coturn realm (e.g. counterpath.com):
RESPOND  $SERVER_NAME
EXECUTE COTURN_REALM=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter a username for coturn:
RESPOND  coturn_username
EXECUTE TURN_USERNAME=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter a password for the above user:
RESPOND  coturn_password
EXECUTE TURN_PASSWORD=$RESPOND
#----------------------------------------------------------------
MESSAGE Configuring /etc/coturn/turnserver.conf...
EXECUTE
sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/coturn/turnserver.conf

echo "" >> /etc/coturn/turnserver.conf
echo "#CPCCONF BEGIN *** DO NOT EDIT ***" >> /etc/coturn/turnserver.conf
#echo "listening-port=$COTURN_PORT" >> /etc/coturn/turnserver.conf
echo "external-ip=$PUBLIC_IP_ADDRESS" >> /etc/coturn/turnserver.conf
echo "lt-cred-mech" >> /etc/coturn/turnserver.conf
echo "no-tcp" >> /etc/coturn/turnserver.conf
echo "no-tls" >> /etc/coturn/turnserver.conf
echo "no-tcp-relay" >> /etc/coturn/turnserver.conf
echo "no-cli" >> /etc/coturn/turnserver.conf
echo "realm=$COTURN_REALM" >> /etc/coturn/turnserver.conf
echo "user=$TURN_USERNAME:$TURN_PASSWORD" >> /etc/coturn/turnserver.conf
echo "#CPCCONF END   *** DO NOT EDIT ***" >> /etc/coturn/turnserver.conf
# the above section shall be removed during RPM uninstallation
#----------------------------------------------------------------
REQUIRE
#----------------------------------------------------------------


MESSAGE Configuring nginx for HTTP proxy...
EXECUTE
if yum list installed nginx.*; then
  add_reminder "nginx has already been installed previously. Manual configuration of /etc/nginx/nginx.conf may be required to run nginx properly"
else
  yum install -y nginx
  #comment out the default site on port 80
  sed -i "/^    server {/,/^    }/s#.*#\#&#" /etc/nginx/nginx.conf
fi

systemctl enable nginx
#----------------------------------------------------------------
MESSAGE Please enter a nginx username which will be used when checking PTT status via nginx:
RESPOND cpcconf
EXECUTE NGINX_USERNAME=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter a password for the above username:
RESPOND cpcconf
EXECUTE NGINX_PASSWORD=$RESPOND
#----------------------------------------------------------------
MESSAGE Creating the above nginx user...
EXECUTE htpasswd -b -c /etc/nginx/.htpasswd $NGINX_USERNAME $NGINX_PASSWORD
#----------------------------------------------------------------
MESSAGE Configuring /etc/nginx/conf.d/66.cpcconf.nginx.conf...
EXECUTE
config_value /etc/nginx/conf.d/66.cpcconf.nginx.conf "\<server_name\>\([^;]*\)" "server_name $SERVER_NAME" 1
config_value /etc/nginx/conf.d/66.cpcconf.nginx.conf "\<server_name\>\([^;]*\)" "server_name $SERVER_NAME" 2
config_value /etc/nginx/conf.d/66.cpcconf.nginx.conf "\<ssl_certificate_key\>\([^;]*\)" "ssl_certificate_key $KEY_FILE_PATH"
config_value /etc/nginx/conf.d/66.cpcconf.nginx.conf "\<ssl_certificate\>\([^;]*\)" "ssl_certificate $CRT_FILE_PATH"
#----------------------------------------------------------------


MESSAGE Would you like to configure a Virtual IP server (keepalived) on the current server for failover?
RESPOND <^(y|yes|n|no)$> y
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping configuration of keepalived"; return; }

CONFIG_KEEPALIVED=1
if yum list installed keepalived; then
  add_reminder "keepalived has already been installed previously. Manual configuration of /opt/keepalived-1.3.6/etc/keepalived.conf may be required to run keepalived properly"
else
  yum install -y $SETUP_DIR/keepalived-1.3.6-1.x86_64.rpm
fi

add_reminder "run $SETUP_DIR/createVIP or addVIP to modify /opt/keepalived-1.3.6/etc/keepalived.conf, e.g. createVIP -n cpcconf -v ********* -b *********** -m 24 -i ens33 -p 200 -r 25 -c '/usr/bin/pgrep -f cpcconf'"
#----------------------------------------------------------------


REQUIRE [[ -z CONFIG_COTURN ]]
#----------------------------------------------------------------
MESSAGE Please enter a TURN server name for NAT traversal:
RESPOND  
EXECUTE TURN_SERVER=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter a username to access the above TURN server:
RESPOND  
EXECUTE TURN_USERNAME=$RESPOND
#----------------------------------------------------------------
MESSAGE Please enter a password for the above user:
RESPOND  
EXECUTE TURN_PASSWORD=$RESPOND
#----------------------------------------------------------------
REQUIRE
#----------------------------------------------------------------


MESSAGE Would you like to turn on logging for PTT server?
RESPOND <^(y|yes|n|no)$> y
EXECUTE
[[ $RESPOND =~ y|yes ]] && PTT_LOGGING="true" || PTT_LOGGING="false"
#----------------------------------------------------------------
MESSAGE Please enter PTT conference join url (e.g. http://counterpath.com):
RESPOND  http://$SERVER_NAME
EXECUTE PTT_JOIN_URL=$RESPOND
#----------------------------------------------------------------
MESSAGE Configuring /opt/cpcconf/cpcapi2_conf.config...
EXECUTE
# Per Jeremy: nat_traversal_server_type should always be 1, nat_traversal_server_type2 should be 0 if coturn is installed, and 1 otherwise
NAT_SERVER_TYPE1=1
[[ $CONFIG_COTURN -eq 1 ]] && { NAT_SERVER_TYPE2=0; TURN_SERVER=$SERVER_NAME; } || NAT_SERVER_TYPE2=1

config_value /opt/cpcconf/cpcapi2_conf.config "\<server_hostname\>\s*=\(.*\)" "server_hostname = $SERVER_NAME"
config_value /opt/cpcconf/cpcapi2_conf.config "\<sdk_logging_enabled\>\s*=\(.*\)" "sdk_logging_enabled = $PTT_LOGGING"
config_value /opt/cpcconf/cpcapi2_conf.config "\<nat_traversal_server_hostname\>\s*=\(.*\)" "nat_traversal_server_hostname = $TURN_SERVER"
config_value /opt/cpcconf/cpcapi2_conf.config "\<nat_traversal_server_username\>\s*=\(.*\)" "nat_traversal_server_username = $TURN_USERNAME"
config_value /opt/cpcconf/cpcapi2_conf.config "\<nat_traversal_server_password\>\s*=\(.*\)" "nat_traversal_server_password = $TURN_PASSWORD"
config_value /opt/cpcconf/cpcapi2_conf.config "\<nat_traversal_server_type\>\s*=\(.*\)" "nat_traversal_server_type = $NAT_SERVER_TYPE1"
config_value /opt/cpcconf/cpcapi2_conf.config "\<nat_traversal_server_type2\>\s*=\(.*\)" "nat_traversal_server_type2 = $NAT_SERVER_TYPE2"
config_value /opt/cpcconf/cpcapi2_conf.config "\<server_public_ip_for_media\>\s*=\(.*\)" "server_public_ip_for_media = $PUBLIC_IP_ADDRESS"
config_value /opt/cpcconf/cpcapi2_conf.config "\<json_api_websocket_private_key_file\>\s*=\(.*\)" "json_api_websocket_private_key_file = $KEY_FILE_PATH"
config_value /opt/cpcconf/cpcapi2_conf.config "\<json_api_websocket_certificate_file\>\s*=\(.*\)" "json_api_websocket_certificate_file = $CRT_FILE_PATH"
config_value /opt/cpcconf/cpcapi2_conf.config "\<json_api_http_private_key_file\>\s*=\(.*\)" "json_api_http_private_key_file = $KEY_FILE_PATH"
config_value /opt/cpcconf/cpcapi2_conf.config "\<json_api_http_certificate_file\>\s*=\(.*\)" "json_api_http_certificate_file = $CRT_FILE_PATH"
config_value /opt/cpcconf/cpcapi2_conf.config "\<conference_join_url\>\s*=\(.*\)" "conference_join_url = $PTT_JOIN_URL"
config_value /opt/cpcconf/cpcapi2_conf.config "\<raft_interface_ip\>\s*=\(.*\)" "raft_interface_ip = $PUBLIC_IP_ADDRESS"
config_value /opt/cpcconf/cpcapi2_auth_service.config "\<auth_service_http_priv_key_file_path\>\s*=\(.*\)" "auth_service_http_priv_key_file_path = $KEY_FILE_PATH"
config_value /opt/cpcconf/cpcapi2_auth_service.config "\<auth_service_http_cert_file_path\>\s*=\(.*\)" "auth_service_http_cert_file_path = $CRT_FILE_PATH"
#----------------------------------------------------------------


MESSAGE Starting all installed services...
MESSAGE Would you like to start nginx now?
RESPOND <^(y|yes|n|no)$> n
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping starting nginx"; return; }
systemctl start nginx
#----------------------------------------------------------------
REQUIRE [[ $CONFIG_COTURN -eq 1 ]]
#----------------------------------------------------------------
MESSAGE Would you like to start coturn now?
RESPOND <^(y|yes|n|no)$> n
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping starting coturn"; return; }
systemctl start coturn
#----------------------------------------------------------------
REQUIRE [[ $CONFIG_KEEPALIVED -eq 1 ]]
#----------------------------------------------------------------
MESSAGE Would you like to start keepalived now?
RESPOND <^(y|yes|n|no)$> n
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping starting keepalived"; return; }
/etc/init.d/keepalived start
#----------------------------------------------------------------
REQUIRE
#----------------------------------------------------------------
MESSAGE Would you like to start PTT now?
RESPOND <^(y|yes|n|no)$> n
EXECUTE
[[ ! $RESPOND =~ y|yes ]] && { echo "Skipping starting PTT"; return; }
systemctl start cpcconf
#----------------------------------------------------------------


EXECUTE
REMINDER_ECHO=off
add_reminder "Need to open ports in the firewall as configured"
REMINDER_ECHO=on
