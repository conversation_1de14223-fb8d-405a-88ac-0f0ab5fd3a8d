server_hostname = 
sdk_logging_enabled = false
nat_traversal_server_hostname = 
nat_traversal_server_username = 
nat_traversal_server_password = 
nat_traversal_server_type = 1
nat_traversal_server_type2 = 1
server_public_ip_for_media = 
# 0 = Unencrypted, 1 = SDES, 2 = DTLS
conference_media_encryption_mode = 2
screenshare_video_max_bitrate_kbps = 2000
screenshare_video_target_bitrate_kbps = 2000
screenshare_video_start_bitrate_kbps = 2000
screenshare_video_min_bitrate_kbps = 1000
camera_video_max_bitrate_kbps = 2000
camera_video_target_bitrate_kbps = 2000
camera_video_start_bitrate_kbps = 2000
camera_video_min_bitrate_kbps = 500
json_api_websocket_hostname = 
json_api_websocket_port = 9003
json_api_websocket_use_secure = true
json_api_websocket_certificate_file = server.crt
json_api_websocket_private_key_file = server.key
# The use of Di<PERSON>ie-<PERSON><PERSON> key exchange is desirable, since this provides Perfect Forward Secrecy (PFS).
# Without a DH parameter file, no Diffie-Hellman based ciphers will be used, even if configured to do so.
json_api_websocket_diffie_hellman_params = dh2048.pem
json_api_http_certificate_file = server.crt
json_api_http_private_key_file = server.key
json_api_http_hostname = 
json_api_http_port = 18090
json_api_http_use_secure = true
video_websocket_hostname = 
video_websocket_port = 9005
video_websocket_use_secure = true
conference_join_url = 
server_uid = AA
raft_node_id = 1
raft_interface_ip = 
create_root_conference = true