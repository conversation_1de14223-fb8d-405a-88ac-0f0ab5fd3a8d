# CounterPath Service Setup Script: setup.h
# Created by <PERSON> (<EMAIL>) on Sep 01, 2020.
# Copyright 2020 CounterPath Corporation. All rights reserved.

#!/bin/bash

if [[ ! -z $(tput colors) ]]; then
  _SUCCESS=$(tput setaf 2)
  _FAILURE=$(tput setaf 1)$(tput blink)
  _SKIPPED=$(tput setaf 6)
  _REMINDER=$(tput rev)$(tput bold)
  _NORMAL=$(tput sgr0)
fi

SETUP_DIR=$(dirname $(readlink -f $0))
SETUP_WIZARD=$SETUP_DIR/setup.wizard
SETUP_TEMP=$SETUP_DIR/setup.temp

shopt -s expand_aliases
alias config_value="$SETUP_DIR/config_value.sh"

trap 'rm -f $SETUP_TEMP' EXIT

fail()
{
  echo ""
  echo "[$_FAILURE FAILURE $_NORMAL] Setup is not completed."
  exit $1
}

on_failure()
{
  FAILURE_LINENO=$1
  FAILURE_LINE="$2"
}

#$1: method
perform()
{
  case $1 in
    MESSAGE)
      cat $SETUP_TEMP
      ;;

    EXECUTE)
      echo ""
      source $SETUP_TEMP || {
        [[ -z $FAILURE_LINENO ]]\
          && add_reminder "<$_FAILURE FAILURE $_NORMAL> EXECUTE script failure [$(basename $SETUP_WIZARD):$(($EXECUTE_LINENO))] invalid script or a 'false' condition"\
          || add_reminder "<$_FAILURE FAILURE $_NORMAL> EXECUTE failure [$(basename $SETUP_WIZARD):$(($EXECUTE_LINENO+$FAILURE_LINENO-3))] $FAILURE_LINE"

        [[ -z $SILENT ]] && fail
      }

      echo ""
      ;;

    "")
      ;;

    *)
      echo "Internal error: cannot perform unrecognized method [$1]"
      fail
  esac
}

add_reminder()
{
  [[ ! $REMINDER_ECHO =~ off|0 ]] && echo "$1"
  [[ -z $REMINDER ]] && REMINDER=" - $1" || REMINDER="$REMINDER
 - $1"
}

options=hvqsbf:
longoptions=help,verbose,quiet,silent,file:

opts=$(getopt --options $options --longoptions $longoptions --name "$0" -- "$@")

[[ $? != 0 ]] && { echo "Invalid parameters." >&2; exit 1; }

eval set -- "$opts"

while true; do
  case "$1" in
    -h|--help)
      cat <<-EOF
Usage: $0 [OPTION]

  -h, --help:
                display this help and exit

  -v, --verbose:
                verbose mode for showing more detailed execution information

  -q, --quiet:
                quiet mode for non-interactive setup

  -s, --silent:
                silent mode for ignoring errors

  -f, --file=<filename>:
                specify a $(basename $SETUP_WIZARD)

EOF
      exit 0
      ;;

    -v|--verbose)
      VERBOSE=1
      shift
      ;;

    -q|--quiet)
      QUIET=1
      shift
      ;;

    -s|--silent)
      SILENT=1
      shift
      ;;

    -f|--file)
      SETUP_WIZARD="$2"
      shift 2
      ;;

    --)
      shift
      break
      ;;

    *)
      echo "Internal error: unrecognized option [$1]"
      exit 1
      ;;
  esac
done

echo "Loading $SETUP_WIZARD..."

[[ -z $SETUP_WIZARD || ! -f $SETUP_WIZARD ]] && { echo "[$SETUP_WIZARD] required by setup.sh is missing."; fail; }

i=0
while read -r <&3 LINE; do
  ((i++))
  LOG_LINE="[$(basename $SETUP_WIZARD):$i] $LINE"

  [[ "$LINE" =~ ^[[:space:]]*(\#) ]]
  COMMENT=${BASH_REMATCH[1]}

  OLD_METHOD=$METHOD
  MATCHED=
  CONTENT=

  if [[ "$LINE" =~ ^[[:space:]]*(MESSAGE|RESPOND|EXECUTE|REQUIRE)($|[[:space:]]+(.*)) ]]; then
    MATCHED=1
    METHOD=${BASH_REMATCH[1]}
    CONTENT=${BASH_REMATCH[3]}

    [[ "$METHOD" = "REQUIRE" ]] && REQUIRE=
    perform "$OLD_METHOD"
  fi

  [[ ! -z $VERBOSE ]] && { [[ -z $REQUIRE ]] && echo "<VERBOSE> $LOG_LINE" || echo "<$_SKIPPED SKIPPED $_NORMAL> $LOG_LINE"; }
  [[ ! -z $REQUIRE && ! "$METHOD" = "REQUIRE" ]] && { METHOD=; continue; }
  [[ ! -z $COMMENT || -z $MATCHED ]] && { echo "$LINE" >> "$SETUP_TEMP"; continue; }

  case "$METHOD" in
    REQUIRE)
      METHOD=
      [[ -z $CONTENT ]] || eval "$CONTENT" && REQUIRE= || REQUIRE=1
      ;;

    RESPOND)
      METHOD=
      [[ ! "$CONTENT" =~ (\<(.*)\>)?[[:space:]]*(.*) ]] && { echo "Invalid RESPOND $LOG_LINE"; fail; }
      VALIDATION=${BASH_REMATCH[1]}
      VALIDATOR=${BASH_REMATCH[2]}
      ANSWER=${BASH_REMATCH[3]}
      eval "RESPOND=\"$ANSWER\""

      if [[ -z $QUIET ]]; then
        while true; do
          read -rp "Answer $VALIDATION [$RESPOND]: " ANSWER
          [[ -z $ANSWER ]] && ANSWER=${BASH_REMATCH[3]} || eval "RESPOND=\"$ANSWER\""
          [[ ! "$RESPOND" =~ $VALIDATOR ]] && echo "Invalid RESPOND [$RESPOND]." || break
        done
      else
        [[ ! "$RESPOND" =~ $VALIDATOR ]] && { echo "Invalid RESPOND $LOG_LINE"; fail; }
        echo "Answer [$RESPOND]."
      fi

      # TODO need to check success of update
      sed -i "${i}s#RESPOND.*#RESPOND $VALIDATION $ANSWER#" $SETUP_WIZARD || { echo "Cannot update RESPOND $LOG_LINE"; fail; }
      ;;

    MESSAGE)
      echo "$CONTENT" > "$SETUP_TEMP"
      ;;

    EXECUTE)
      EXECUTE_LINENO=$i
      echo 'trap '\''on_failure $LINENO "$BASH_COMMAND"; false; return;'\'' ERR' > "$SETUP_TEMP"
      echo 'trap '\''trap - RETURN ERR;'\'' RETURN' >> "$SETUP_TEMP"
      echo "$CONTENT" >> "$SETUP_TEMP"
      ;;

    "")
      ;;

    *)
      echo "Unrecognized method $LOG_LINE"
      fail
  esac
done 3< "$SETUP_WIZARD"

perform "$METHOD"

if [[ ! -z $REMINDER ]]; then
  echo ""
  echo "$_REMINDER***** REMINDER *****$_NORMAL"
  echo "$REMINDER"
  echo "$_REMINDER********************$_NORMAL"
fi

echo ""

echo "[$_SUCCESS SUCCESS $_NORMAL] Setup completed."

