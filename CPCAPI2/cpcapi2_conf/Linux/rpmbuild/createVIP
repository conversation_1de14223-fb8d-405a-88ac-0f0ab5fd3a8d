#!/bin/bash

function help()
{
        echo
        echo "Script to configure a VIP in a new keepalived"
        echo "Format:"
        echo "   createVIP [-h|--help] -n name -v vip -b broadcast -m netmask  -i interface -p priority -r routerid -c \"check\" "
        echo "     -h|--help      Show help"
        echo "     -n name        the service name for this vip "
        echo "     -v vip         the virtual ip address "
        echo "     -b broadcast   the broadcast ip address "
        echo "     -m netmask     network mask for vip "
        echo "     -i interface   the network interface to work on)"
        echo "     -p priority    priority when conflict occurs"
        echo "     -r routerid    the virtual router id value"
        echo "     -c check       command used to check application is running"
        echo ""
        echo "The check command typically return the PID of the running application, an"
        echo "empty response from the command will trigger a VIP change"
        echo
}

appname=
vip=
broadcast=
netmask=
interface=
priority=
routerid=
checkcommand=

while getopts ":hn:v:b:m:i:p:r:c:" Option
do
    case $Option in
    h    ) help
           exit 0;;
    n    ) appname=$OPTARG;;
    v    ) vip=$OPTARG;;
    b    ) broadcast=$OPTARG;;
    m    ) netmask=$OPTARG;;
    i    ) interface=$OPTARG;;
    p    ) priority=$OPTARG;;
    r    ) routerid=$OPTARG;;
    c    ) checkcommand=$OPTARG;;
    esac
done

if [ "$appname" == "" ]; then
 echo "-n option is required"
 exit 1
fi
if [ "$vip" == "" ]; then
 echo "-v option is required"
 exit 1
fi
if [ "$broadcast" == "" ]; then
 echo "-b option is required"
 exit 1
fi
if [ "$netmask" == "" ]; then
 echo "-m option is required"
 exit 1
fi
if [ "$interface" == "" ]; then
 echo "-i option is required"
 exit 1
fi
if [ "$priority" == "" ]; then
 echo "-p option is required"
 exit 1
fi
if [ "$routerid" == "" ]; then
 echo "-r option is required"
 exit 1
fi
if [ "$checkcommand" == "" ]; then
 echo "-c option is required"
 exit 1
fi

KAFILE=/opt/keepalived-1.3.6/etc/keepalived.conf
cp ${KAFILE} ${KAFILE}.bak
echo "! Configuration File for keepalived" > ${KAFILE}
echo "" >>  ${KAFILE}
echo "vrrp_script chk_${appname}-vip {" >>  ${KAFILE}
echo "  script \"${checkcommand}\" "  >>  ${KAFILE}
echo "  interval 2 "  >>  ${KAFILE}
echo "}"  >>  ${KAFILE}
echo "" >>  ${KAFILE}
echo "vrrp_instance ${appname}-vip {" >>  ${KAFILE}
echo "    state BACKUP" >>  ${KAFILE}
echo "    nopreempt" >>  ${KAFILE}
echo "    interface ${interface}" >>  ${KAFILE}
echo "    virtual_router_id ${routerid}" >>  ${KAFILE}
echo "    priority ${priority}" >>  ${KAFILE}
echo "    advert_int 1" >>  ${KAFILE}
echo "    authentication {" >>  ${KAFILE}
echo "        auth_type PASS" >>  ${KAFILE}
echo "        auth_pass BpnVrrp" >>  ${KAFILE}
echo "    }" >>  ${KAFILE}
echo "    virtual_ipaddress {" >>  ${KAFILE}
echo "        ${vip}/24 brd ${broadcast} dev ${interface}" >>  ${KAFILE}
echo "    }" >>  ${KAFILE}
echo "    track_script {" >>  ${KAFILE}
echo "      chk_${appname}-vip" >>  ${KAFILE}
echo "    }" >>  ${KAFILE}
echo "}" >>  ${KAFILE}
echo "" >>  ${KAFILE}


exit 0

