types {
    application/wasm wasm;
}
server {
        listen 80 default_server;
        server_name;
        return 301 https://$host$request_uri;
}
server {
    listen 443 ssl default_server;
    server_name;
    index index.html;
    ssl_certificate;
    ssl_certificate_key;
    ssl_ciphers EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH;
    ssl_protocols TLSv1.1 TLSv1.2;
    #charset koi8-r; access_log /var/log/nginx/host.access.log main; location /jsonApi {
    #    proxy_pass http://127.0.0.1:18090;
    #}
    location / {
        if ($http_authorization) {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        alias /usr/share/nginx/html/browser/;
    }
    location ~ ^\/statusApi\/build {
        proxy_pass https://127.0.0.1:18090;
    }
    location ~ ^\/statusApi\/sessionSummary {
        proxy_pass https://127.0.0.1:18090;
        auth_basic "Status API Access";
        auth_basic_user_file /etc/nginx/.htpasswd;
    }
    location ~ ^\/confbridge\/([^\/]*)\/status {
        proxy_pass https://127.0.0.1:18090;
    }
    location ~ ^\/screenshare\/([^\/]*)\/([^\/]*)\/status {
        proxy_pass https://127.0.0.1:18090;
    }

    #location ~ ^\/users\/([^\/]*)\/([^\/]*)\/([^\/]*)\/(?<relpath>.*) { location ~ ^\/users\/([^\/]*)\/screenshare[^\/]?(?<relpath>.*) { location ~
    #^\/confbridge\/([^\/]*)[^\/]?(?<relpath>.*) {
    location ~ ^\/confbridge\/([^\/]*)\/([^\/]*)[^\/]?(?<relpath>.*) {
        if ($http_authorization) {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        if ($request_method = 'OPTIONS') {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        alias /usr/share/nginx/html/browser/$relpath;
    }
    location ~ ^\/screenshare\/([^\/]*)\/([^\/]*)[^\/]?(?<relpath>.*) {
        if ($http_authorization) {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        if ($request_method = 'OPTIONS') {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        alias /usr/share/nginx/html/browser/$relpath;
    }
    location ~ ^\/screenshare\/([^\/]*)[^\/]?(?<relpath>.*) {
        if ($http_authorization) {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        if ($request_method = 'OPTIONS') {
            proxy_pass https://127.0.0.1:18090;
            break;
        }
        alias /usr/share/nginx/html/browser/$relpath;
    }
    location ~ ^\/addUser$ {
        proxy_pass https://127.0.0.1:18082;
    }
    location ~ ^\/login_v1$ {
        proxy_pass https://127.0.0.1:18082;
    }
    location ~ ^\/login_v2$ {
        proxy_pass https://127.0.0.1:18082;
    }

    #error_page 404 /404.html;
    # redirect server error pages to the static page /50x.html
    #
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass http://127.0.0.1;
    #}
    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root html; fastcgi_pass 127.0.0.1:9000; fastcgi_index index.php; fastcgi_param SCRIPT_FILENAME /scripts$fastcgi_script_name; include fastcgi_params;
    #}
    # deny access to .htaccess files, if Apache's document root concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny all;
    #}
}

