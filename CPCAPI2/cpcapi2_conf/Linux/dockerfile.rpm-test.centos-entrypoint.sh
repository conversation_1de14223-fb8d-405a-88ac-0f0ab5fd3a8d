#!/bin/bash

set -e
trap 'echo RPM TEST FAILURE -- return code $? on line $LINENO; exit 1;' ERR

yum install -y *.rpm || true
cd /opt/cpcconf
sed -i "/^MESSAGE Please enter SSL certificate .key/,/^EXECUTE/s/\<RESPOND\>.*/RESPOND server.key/" /opt/cpcconf/setup/setup.wizard
sed -i "/^MESSAGE Please enter SSL certificate .crt/,/^EXECUTE/s/\<RESPOND\>.*/RESPOND server.crt/" /opt/cpcconf/setup/setup.wizard
openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout /etc/pki/tls/private/server.key -out /etc/pki/tls/certs/server.crt -subj "/C=US/ST=Denial/L=Springfield/O=Dis/CN=$HOSTNAME"
setup/setup.sh -s -q
mkdir -p artifacts
./cpcapi2_conf logging
sleep 3

count=`pgrep -c cpcapi2_conf`
[[ $count -eq 1 ]] || { [[ $count -eq 0 ]] && { echo "No running instance of cpcapi2_conf"; false; } || { echo "Multiple running instances of cpcapi2_conf"; false; } }

user=`ps aux | grep cpcapi2_conf | grep -v grep | cut -f1 -d' '`
[[ ! $user = "root" ]] || { echo "cpcapi2_conf running as root"; false; }

curl -k https://localhost:18090/statusApi/build
SESSION_SUMMARY_RESULT=$(curl -v -k https://localhost:18090/ccApi/sessionSummary)
echo "sessionSummary response: $SESSION_SUMMARY_RESULT"
echo $SESSION_SUMMARY_RESULT | grep -q '"context": "default-context"' && echo RPM TEST SUCCESS || false

cp *.log artifacts
