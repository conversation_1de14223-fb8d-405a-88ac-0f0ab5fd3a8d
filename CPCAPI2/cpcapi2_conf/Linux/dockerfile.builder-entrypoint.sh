#!/bin/bash
set -e
NUMBER_OF_CPUS=`grep -c ^processor "/proc/cpuinfo"`
CMAKE=cmake
export CCACHE_DIR=/.ccache  # mount is setup by docker-build.sh
export CCACHE_BASEDIR=/CPCAPI2 # mount is setup by docker-build.sh
export CCACHE_MAXSIZE=20G 

if command -v cmake3 >/dev/null 2>&1;
then
  CMAKE=cmake3
fi

set +e
ccache -z
set -e

python3 ../../../tools/conan_detect.py

$CMAKE -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCPCAPI2_CONAN=1 ../../

ninja

objcopy --only-keep-debug cpcapi2_conf cpcapi2_conf.debug
objcopy --strip-debug cpcapi2_conf cpcapi2_conf
objcopy --add-gnu-debuglink=cpcapi2_conf.debug cpcapi2_conf

set +e
ccache -s
set -e

cd ..

rm -rf rpmbuild/RPMS

if [ -z "$BUILD_NUMBER" ]; then
  BUILD_NUMBER=0
fi

./rpm-build.sh --release $BUILD_NUMBER

echo Build Complete
echo Built$TARGETS_BUILT
