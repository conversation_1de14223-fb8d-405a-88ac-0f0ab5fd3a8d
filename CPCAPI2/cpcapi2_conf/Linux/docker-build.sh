if [ ! -d build ]
then
  mkdir build
fi

# ccache not required to be installed on host; we just want to share cache files outside of the container
mkdir -p /home/<USER>/.ccache

printf "#pragma once\n#define BUILD_INFO \"Job=$JOB_BASE_NAME Revision=$SVN_REVISION_1 Build=$BUILD_NUMBER\"" > ../cpcapi2_conf_buildinfo.h

docker build -t cpcapi2_builder:ubuntu.clang -f ../../projects/docker_build/Dockerfile.ubuntu.clang ../../projects/docker_build
docker build -t cpcapi2_conf_builder:ubuntu.clang -f Dockerfile.builder.ubuntu.clang .
docker run --name cpcapi2_conf_builder --user $(id -u):$(id -g) -v /home/<USER>/.ccache:/.ccache -e CCACHE_DIR=/.ccache -e BUILD_NUMBER=$BUILD_NUMBER --rm --mount type=bind,source="$(pwd)"/../../../,target=/CPCAPI2 --mount type=bind,source=$HOME/.conan/,target=/tmp/conan/.conan --env CPCAPI2_CONAN_USER --env CPCAPI2_CONAN_APIKEY cpcapi2_conf_builder:ubuntu.clang -u $(whoami) "$@"
