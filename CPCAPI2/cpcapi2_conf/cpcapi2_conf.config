server_hostname = cpclientapi.softphone.com
sdk_logging_enabled = false
nat_traversal_server_hostname = screenshare1.bria-x.net
nat_traversal_server_username = cpcapi2
nat_traversal_server_password = nopasswordsinsvnplease
nat_traversal_server_type = 0
nat_traversal_server_type2 = 0
# 0 = Unencrypted, 1 = SDES, 2 = DTLS
conference_media_encryption_mode = 2
screenshare_video_max_bitrate_kbps = 2000
screenshare_video_target_bitrate_kbps = 2000
screenshare_video_start_bitrate_kbps = 2000
screenshare_video_min_bitrate_kbps m = 500
camera_video_max_bitrate_kbps = 2000
camera_video_target_bitrate_kbps = 2000
camera_video_start_bitrate_kbps = 2000
camera_video_min_bitrate_kbps = 500
json_api_websocket_hostname = 
json_api_websocket_port = 9003
json_api_websocket_use_secure = true
json_api_websocket_certificate_file = cpclientapi_softphone_com.crt
json_api_websocket_private_key_file = cpclientapi_softphone_com.key
# The use of Diffie-Hellman key exchange is desirable, since this provides Perfect Forward Secrecy (PFS). 
# Without a DH parameter file, no Diffie-Hellman based ciphers will be used, even if configured to do so.
json_api_websocket_diffie_hellman_params = dh2048.pem
json_api_http_certificate_file = cpclientapi_softphone_com.crt
json_api_http_private_key_file = cpclientapi_softphone_com.key
json_api_http_hostname = 
json_api_http_port = 18090
json_api_http_use_secure = true
video_websocket_hostname = 
video_websocket_port = 9005
video_websocket_use_secure = true
conference_join_url = 
server_uid = AB
raft_interface_ip = 127.0.0.1:18090
join_cluster_url = https://cpclientapi.softphone.com:18090/statusApi/joinCluster
auth_service_url = https://127.0.0.1:18082/login_v2
auth_service_api_key = p256-public-key.spki
