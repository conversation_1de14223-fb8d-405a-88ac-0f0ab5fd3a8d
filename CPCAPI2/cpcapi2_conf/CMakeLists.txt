cmake_minimum_required(VERSION 3.8.0)

add_definitions(-DCPCAPI2_SERVER_CONF -DCPCAPI2_INCLUDE_UNRELEASED_HEADERS)
include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/toolchains/configure.cmake) # Must be included before project()

project(cpcapi2_conf LANGUAGES ${CPCAPI2_LANGUAGES})
init_project()

include_directories(${INCLUDE_DIRS})

option(DISABLE_WSS_JSON_SERVER "Disable use of secure websocket JSON server" OFF)

# App sources
set(SOURCES
  cpcapi2_conf.cpp
  Cpcapi2Runner.cpp
  PlaySoundFileStream.cpp
  SdkManager.cpp
)


set(CMAKE_THREAD_PREFER_PTHREAD ON)
set(THREADS_PREFER_PTHREAD_FLAG ON)

find_package(Threads REQUIRED)

add_executable(${CMAKE_PROJECT_NAME} ${SOURCES})
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CXX_STANDARD ${CPCAPI2_CXX_STANDARD})

set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CXX_STANDARD 17)
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CMAKE_CXX_STANDARD_REQUIRED ON)
set_property(TARGET ${CMAKE_PROJECT_NAME} PROPERTY CMAKE_CXX_EXTENSIONS OFF)
set(CMAKE_OSX_DEPLOYMENT_TARGET "11.6" CACHE STRING "Minimum OS X deployment version" FORCE)

if (OS_LINUX)
  SET(USE_X11 ON CACHE BOOL "Build with X11 support")
endif()

include(${CMAKE_CURRENT_SOURCE_DIR}/../projects/cmake/CPCAPI2/CPCAPI2.cmake)
target_link_libraries(${CMAKE_PROJECT_NAME} CPCAPI2_Static rapidjson Threads::Threads)
if (NOT DEFINED APPLE)
  target_link_libraries(${PROJECT_NAME} -lstdc++fs)
endif()
