#include "stdafx.h"
#include "PlaySoundFileStream.h"

#include <assert.h>
#include <string.h>

namespace CPCAPI2
{
namespace Agent
{
PlaySoundFileStream::PlaySoundFileStream()
   : rw_lock_(new std::mutex), 
   id_(NULL),
   managed_file_handle_(true),
   open_(false),
   looping_(false),
   read_only_(false),
   max_size_in_bytes_(0),
   size_in_bytes_(0) {
   memset(file_name_utf8_, 0, kMaxFileNameSize);
}

PlaySoundFileStream::~PlaySoundFileStream() {
   if (id_ != NULL && managed_file_handle_) {
      fclose(id_);
   }
}

int PlaySoundFileStream::CloseFile() {
   std::unique_lock<std::mutex> write(*rw_lock_);
   return CloseFileImpl();
}

int PlaySoundFileStream::Rewind() {
   std::unique_lock<std::mutex> write(*rw_lock_);
   return 0;
}

int PlaySoundFileStream::SetMaxFileSize(size_t bytes) {
   std::unique_lock<std::mutex> write(*rw_lock_);
   max_size_in_bytes_ = bytes;
   return 0;
}

int PlaySoundFileStream::Flush() {
   std::unique_lock<std::mutex> write(*rw_lock_);
   return FlushImpl();
}

bool PlaySoundFileStream::Open() const {
   std::unique_lock<std::mutex> read(*rw_lock_);
   return open_;
}

int PlaySoundFileStream::OpenFile(const char* file_name_utf8, bool read_only,
   bool loop, bool text) {
   std::unique_lock<std::mutex> write(*rw_lock_);
   if (id_ != NULL && !managed_file_handle_)
      return -1;
   size_t length = strlen(file_name_utf8);
   if (length > kMaxFileNameSize - 1) {
      return -1;
   }

   read_only_ = read_only;

   FILE* tmp_id = NULL;
#if defined _WIN32
   wchar_t wide_file_name[kMaxFileNameSize];
   wide_file_name[0] = 0;

   MultiByteToWideChar(CP_UTF8,
      0,  // UTF8 flag
      file_name_utf8,
      -1,  // Null terminated string
      wide_file_name,
      kMaxFileNameSize);
   if (text) {
      if (read_only) {
         tmp_id = _wfopen(wide_file_name, L"rt");
      }
      else {
         tmp_id = _wfopen(wide_file_name, L"wt");
      }
   }
   else {
      if (read_only) {
         tmp_id = _wfopen(wide_file_name, L"rb");
      }
      else {
         tmp_id = _wfopen(wide_file_name, L"wb");
      }
   }
#else
   if (text) {
      if (read_only) {
         tmp_id = fopen(file_name_utf8, "rt");
      }
      else {
         tmp_id = fopen(file_name_utf8, "wt");
      }
   }
   else {
      if (read_only) {
         tmp_id = fopen(file_name_utf8, "rb");
      }
      else {
         tmp_id = fopen(file_name_utf8, "wb");
      }
   }
#endif

   if (tmp_id != NULL) {
      // +1 comes from copying the NULL termination character.
      memcpy(file_name_utf8_, file_name_utf8, length + 1);
      if (id_ != NULL) {
         fclose(id_);
      }
      id_ = tmp_id;
      managed_file_handle_ = true;
      looping_ = loop;
      open_ = true;
      return 0;
   }
   return -1;
}

int PlaySoundFileStream::OpenFromFileHandle(FILE* handle,
   bool manage_file,
   bool read_only,
   bool loop) {
   std::unique_lock<std::mutex> write(*rw_lock_);
   if (!handle)
      return -1;

   if (id_ != NULL) {
      if (managed_file_handle_)
         fclose(id_);
      else
         return -1;
   }

   id_ = handle;
   managed_file_handle_ = manage_file;
   read_only_ = read_only;
   looping_ = loop;
   open_ = true;
   return 0;
}

int PlaySoundFileStream::Read(void* buf, size_t length) {
   std::unique_lock<std::mutex> write(*rw_lock_);
   if (id_ == NULL)
      return -1;

   size_t bytes_read = fread(buf, 1, length, id_);
   if (bytes_read != length && !looping_) {
      CloseFileImpl();
   }
   return static_cast<int>(bytes_read);
}

int PlaySoundFileStream::CloseFileImpl() {
   if (id_ != NULL) {
      if (managed_file_handle_)
         fclose(id_);
      id_ = NULL;
   }
   memset(file_name_utf8_, 0, kMaxFileNameSize);
   open_ = false;
   return 0;
}

int PlaySoundFileStream::FlushImpl() {
   if (id_ != NULL) {
      return fflush(id_);
   }
   return -1;
}

}
}
