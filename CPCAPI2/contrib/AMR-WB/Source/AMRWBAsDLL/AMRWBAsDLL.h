
#include <windows.h>

// The following ifdef block is the standard way of creating macros which make exporting 
// from a DLL simpler. All files within this DLL are compiled with the AMRWBASDLL_EXPORTS
// symbol defined on the command line. this symbol should not be defined on any project
// that uses this DLL. This way any other project whose source files include this file see 
// AMRWBASDLL_API functions as being imported from a DLL, whereas this DLL sees symbols
// defined with this macro as being exported.
#ifdef AMRWBASDLL_EXPORTS
#define AMRWBASDLL_API __declspec(dllexport)
#else
#define AMRWBASDLL_API __declspec(dllimport)
#endif

#pragma once

struct ICodecLibAMRWBEncoder
{
   virtual void SelfDelete() = 0;

   virtual void StartEncoder(
      ULONG &ulOutSamplesPerFrame,
      ULONG &ulOutMaxOutputFrameSize,
      short defaultMode = 8
   ) = 0;

   virtual void Encode(
      const signed short *pInSamples,     // samples must be ulOutSamplesPerFrame
      BYTE *pIoEncodedBuffer,             // buffer must be at least ulOutMaxOutputFrameSize in size
      ULONG &ulOutEncodedFilledSize
   ) = 0;

   virtual void ChangeMode(
      unsigned int uiInMode,
      unsigned long &ulOutSamplesPerFrame
   ) = 0;

   virtual void StopEncoder(
   ) = 0;
};

struct ICodecLibAMRWBDecoder
{
   virtual void SelfDelete() = 0;

   virtual void StartDecoder(
      ULONG &ulOutSamplesInFrame
   ) = 0;

   virtual void Decode(
      const BYTE *pInEncodedBuffer,          // encoded buffer
      ULONG ulInEncodedBufferSizeInBytes,    // how many bytes are left in encoded buffer
      ULONG &ulOutConsumedBuffer,            // how many bytes of the buffer was consumed
      signed short *pIoSamples         // samples must be ulOutSamplesInFrame
   ) = 0;

   virtual void StopDecoder(
   ) = 0;
};

AMRWBASDLL_API ICodecLibAMRWBEncoder *CreateICodecLibAMRWBEncoder();
AMRWBASDLL_API ICodecLibAMRWBDecoder *CreateICodecLibAMRWBDecoder();
