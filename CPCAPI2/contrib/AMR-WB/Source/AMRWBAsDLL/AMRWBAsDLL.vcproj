<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="AMRWBAsDLL"
	ProjectGUID="{66A6EEE4-8BC7-42D0-B705-6C042768BFA8}"
	RootNamespace="AMRWBAsDLL"
	Keyword="Win32Proj"
	TargetFrameworkVersion="131072"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\..\..\..\common\InheritedDebugSettings_branded.vsprops"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="..\CodecLib"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;AMRWBASDLL_EXPORTS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				TreatWChar_tAsBuiltInType="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				BrowseInformation="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="amrwb.lib CodecLib.lib unicows.lib OS_depend_func.lib"
				OutputFile="$(ProjectDir)..\..\..\..\cpsi\dll\debug\AMRWBAsDLL.dll"
				LinkIncremental="2"
				AdditionalLibraryDirectories="..\CodecLib"
				IgnoreDefaultLibraryNames=""
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(OutDir)/AMRWBAsDLL.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary="$(ProjectDir)..\..\..\..\cpsi\lib\debug\AMRWBAsDLL.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops;..\..\..\..\common\InheritedReleaseSetttings_branded.vsprops"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="..\CodecLib"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;AMRWBASDLL_EXPORTS"
				TreatWChar_tAsBuiltInType="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="amrwb.lib CodecLib.lib unicows.lib OS_depend_func.lib"
				OutputFile="$(ProjectDir)..\..\..\..\cpsi\dll\release\AMRWBAsDLL.dll"
				LinkIncremental="1"
				AdditionalLibraryDirectories="..\CodecLib"
				IgnoreDefaultLibraryNames=""
				GenerateDebugInformation="false"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary="$(ProjectDir)..\..\..\..\cpsi\lib\release\AMRWBAsDLL.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="DebugNoSSL|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="&quot;..\..\..\..\..\Library\AudioCodecs\AMR-WB\Source\CodecLib&quot;"
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;AMRWBASDLL_EXPORTS"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="1"
				TreatWChar_tAsBuiltInType="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				BrowseInformation="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="amrwb.lib CodecLib.lib unicows.lib OS_depend_func.lib"
				OutputFile="$(OutDir)/AMRWBAsDLL.dll"
				LinkIncremental="2"
				AdditionalLibraryDirectories="&quot;..\..\..\..\..\Library\AudioCodecs\AMR-WB\Source\CodecLib&quot;"
				IgnoreDefaultLibraryNames="libc.lib;libcmt.lib;msvcrt.lib;libcd.lib;msvcrtd.lib"
				GenerateDebugInformation="true"
				ProgramDatabaseFile="$(OutDir)/AMRWBAsDLL.pdb"
				SubSystem="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary="$(OutDir)/AMRWBAsDLL.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="ReleaseNoSSL|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="2"
			InheritedPropertySheets="$(VCInstallDir)VCProjectDefaults\UpgradeFromVC71.vsprops"
			CharacterSet="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalIncludeDirectories="&quot;..\..\..\..\..\Library\AudioCodecs\AMR-WB\Source\CodecLib&quot;"
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;AMRWBASDLL_EXPORTS"
				RuntimeLibrary="0"
				TreatWChar_tAsBuiltInType="true"
				RuntimeTypeInfo="true"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="false"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				AdditionalDependencies="amrwb.lib CodecLib.lib unicows.lib OS_depend_func.lib"
				OutputFile="$(OutDir)/AMRWBAsDLL.dll"
				LinkIncremental="1"
				AdditionalLibraryDirectories="&quot;..\..\..\..\..\Library\AudioCodecs\AMR-WB\Source\CodecLib&quot;"
				IgnoreDefaultLibraryNames="libc.lib;msvcrt.lib;libcd.lib;libcmtd.lib;msvcrtd.lib"
				GenerateDebugInformation="false"
				SubSystem="2"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				RandomizedBaseAddress="1"
				DataExecutionPrevention="0"
				ImportLibrary="$(OutDir)/AMRWBAsDLL.lib"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\AMRWBAsDLL.cpp"
				>
			</File>
			<Filter
				Name="Samples"
				>
				<File
					RelativePath="..\CodecLib\main_simpleAMRWB.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="DebugNoSSL|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="ReleaseNoSSL|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="..\CodecLib\OS_depend_func.c"
					>
					<FileConfiguration
						Name="Debug|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="DebugNoSSL|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
					<FileConfiguration
						Name="ReleaseNoSSL|Win32"
						ExcludedFromBuild="true"
						>
						<Tool
							Name="VCCLCompilerTool"
						/>
					</FileConfiguration>
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\AMRWBAsDLL.h"
				>
			</File>
			<File
				RelativePath="..\CodecLib\bas_type.h"
				>
			</File>
			<File
				RelativePath="..\CodecLib\codecLib_if.h"
				>
			</File>
			<File
				RelativePath="..\CodecLib\OS_depend_func.h"
				>
			</File>
			<File
				RelativePath="..\CodecLib\typedef.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
