// AMRWBAsDLL.cpp : Defines the entry point for the DLL application.
//

#include "AMRWBAsDLL.h"

#ifdef __cplusplus
extern "C"
{
#include <codecLib_if.h>
};
#endif //__cplusplus


BOOL APIENTRY DllMain( HANDLE hModule, 
                       DWORD  ul_reason_for_call, 
                       LPVOID lpReserved
					 )
{
	switch (ul_reason_for_call)
	{
	case DLL_PROCESS_ATTACH:
	case DLL_THREAD_ATTACH:
	case DLL_THREAD_DETACH:
	case DLL_PROCESS_DETACH:
		break;
	}
    return TRUE;
}

class CCodecLibAMRWBEncoder : public ICodecLibAMRWBEncoder
{
   void *m_pEncoderHandle;
   codecOption m_tCodecOptions;
   Word16 m_wVerbose;

   ULONG m_ulSamplesPerFrame;
   ULONG m_ulMaxOutputFrameSize;

public:
   virtual void SelfDelete() {delete this;}

   virtual void StartEncoder(
      ULONG &ulOutSamplesPerFrame,
      ULONG &ulOutMaxOutputFrameSize,
      short ulDefaultMode
   )
   {
      m_pEncoderHandle = NULL;

      ZeroMemory(&m_tCodecOptions, sizeof(m_tCodecOptions));
      m_wVerbose = 0;

      codecLibQuery(
         &m_pEncoderHandle,
         AMRWB,
         ENC,
         NULL,
         &m_tCodecOptions,
         m_wVerbose
      );

      m_tCodecOptions.mode = ulDefaultMode; // n/a
      m_tCodecOptions.bfi = 0;	           // turn off bad frame indicator
      m_tCodecOptions.packing = 0;          // n/a	

      codecLibOpen(
         &m_pEncoderHandle,
         AMRWB,
         ENC,
         m_wVerbose
      ); 

      codecProperties tCodecProperties;
      ZeroMemory(&tCodecProperties, sizeof(tCodecProperties));

   	codecLibQuery(
         &m_pEncoderHandle,
         AMRWB,
         ENC,
         &tCodecProperties,
         &m_tCodecOptions,
         m_wVerbose
      );

      ulOutSamplesPerFrame = m_ulSamplesPerFrame = tCodecProperties.codecInput.samplesPerFrame;
      ulOutMaxOutputFrameSize = m_ulMaxOutputFrameSize = (tCodecProperties.codecOutput.maxBufferSize);
   }

   virtual void Encode(
      const signed short *pInSamples,
      BYTE *pIoEncodedBuffer,
      ULONG &ulOutEncodedFilledSize
   )
   {
      ulOutEncodedFilledSize = 0;

      Word32 lCompressedSize = m_ulMaxOutputFrameSize;
      Word32 lUnencodedFilledLength = m_ulSamplesPerFrame;

      codecLibEncode(
         &m_pEncoderHandle,
         (Word16 *)(&(pInSamples[0])),
         &lUnencodedFilledLength,
         (Word8 *)(&(pIoEncodedBuffer[0])),
         &lCompressedSize,
         &m_tCodecOptions,
         m_wVerbose
      );

      ulOutEncodedFilledSize = ((ULONG)lCompressedSize);
   }

   virtual void ChangeMode(
      unsigned int uiInMode,
      unsigned long &ulOutSamplesPerFrame
   )
   {
      if( uiInMode > 8 )
         return;
      
      // Change the mode on the parameters used for encoding
      m_tCodecOptions.mode = uiInMode;
   }

   virtual void StopEncoder()
   {
      if (NULL != m_pEncoderHandle)
      {
	      codecLibClose(
            &m_pEncoderHandle,
            m_wVerbose
         );
         m_pEncoderHandle = NULL;
      }
   }
};

class CCodecLibAMRWBDecoder : public ICodecLibAMRWBDecoder
{
   void *m_pDecoderHandle;
   codecOption m_tCodecOptions;
   Word16 m_wVerbose;

   ULONG m_ulSamplesPerFrame;
   ULONG m_ulBytesPerFrame;

public:
   CCodecLibAMRWBDecoder()
   {
      m_ulSamplesPerFrame = 0;
      m_ulBytesPerFrame = 0;
   }

   virtual void SelfDelete() {delete this;}

   virtual void StartDecoder(
      ULONG &ulOutSamplesInFrame
   )
   {
      m_pDecoderHandle = NULL;

      ZeroMemory(&m_tCodecOptions, sizeof(m_tCodecOptions));
      m_wVerbose = 0;

      m_ulSamplesPerFrame = 0;

	   m_tCodecOptions.mode = 8;		   // n/a
	   m_tCodecOptions.bfi = 0;			// turn off bad frame indicator
	   m_tCodecOptions.packing = 0;		// n/a
	   m_tCodecOptions.DTX_mode = 1;	   // n/a	

      codecLibQuery(
         &m_pDecoderHandle,
         AMRWB,
         DEC,
         NULL,
         &m_tCodecOptions,
         m_wVerbose
      );

      m_tCodecOptions.mode = 8; // ignored anyways

      codecLibOpen(
         &m_pDecoderHandle,
         AMRWB,
         DEC,
         m_wVerbose
      ); 

      codecProperties tCodecProperties;
      ZeroMemory(&tCodecProperties, sizeof(tCodecProperties));

   	codecLibQuery(
         &m_pDecoderHandle,
         AMRWB,
         DEC,
         &tCodecProperties,
         &m_tCodecOptions,
         m_wVerbose
      );

      ulOutSamplesInFrame = m_ulSamplesPerFrame = tCodecProperties.codecOutput.samplesPerFrame;
      m_ulBytesPerFrame = tCodecProperties.codecInput.bufferSize;
   }

   virtual void Decode(
      const BYTE *pInEncodedBuffer,       // encoded buffer
      ULONG ulInEncodedBufferSizeInBytes, // how many bytes are left in encoded buffer
      ULONG &ulOutConsumedBuffer,         // how many bytes of the buffer was consumed
      signed short *pIoSamples            // samples must be ulOutSamplesInFrame
   )
   {
      ulOutConsumedBuffer = 0;

      Word32 lInputSize = (Word32)min(m_ulBytesPerFrame, ulInEncodedBufferSizeInBytes);
      Word32 lOutputSize = (Word32)(m_ulSamplesPerFrame);

      Word32 lResult = codecLibDecode(
         &m_pDecoderHandle,
         (Word8 *)(pInEncodedBuffer),
         &lInputSize,
         (Word16 *)(&(pIoSamples[0])),
         &lOutputSize,
         &m_tCodecOptions,
         m_wVerbose
      );

      while (lResult == WAR_CM_DEC_OUT_FULL)
      {
         Word32 lResult = codecLibDecode(
            &m_pDecoderHandle,
            NULL,
            0,
            (Word16 *)(&(pIoSamples[0])),
            &lOutputSize,
            &m_tCodecOptions,
            m_wVerbose
         );
         if (lResult != WAR_CM_DEC_OUT_FULL)
            break;
      }

      // subtract from what needs to be done         
      ulOutConsumedBuffer = min(ulInEncodedBufferSizeInBytes, ((DWORD)lInputSize));
   }

   virtual void StopDecoder()
   {
      if (NULL != m_pDecoderHandle)
      {
	      codecLibClose(
            &m_pDecoderHandle,
            m_wVerbose
         );
         m_pDecoderHandle = NULL;
      }
   }
};


// This is an example of an exported function.
AMRWBASDLL_API ICodecLibAMRWBEncoder *CreateICodecLibAMRWBEncoder()
{
	return new CCodecLibAMRWBEncoder;
}

AMRWBASDLL_API ICodecLibAMRWBDecoder *CreateICodecLibAMRWBDecoder()
{
	return new CCodecLibAMRWBDecoder;
}
