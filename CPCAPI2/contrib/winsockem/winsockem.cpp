﻿/*
terms:
-	winsock = original winsock
-	winsockem = winsock emulation

design keynotes:
-	cannot use GetHashCode() for SOCKET value since StreamSocket might be converted to StreamSocketListener with SOCKET value remained same

-	recv() and recvfrom() share the same implementation as recv_impl().

-	an UDP socket is implemented with WinRT DatagramSocket and data retrieval
	is implemented with DatagramSocket::MessageReceived event handling.

-	a client TCP socket is implemented with WinRT StreamSocket and data retrieval is implemented
	with an extra data receiving thread.

-	a server TCP socket is implemented with WinRT StreamSocketListener.

-	if bind() is called on a client TCP socket, this socket will be converted to a server TCP socket.

-	select() is implemented with Concurrency::event::wait_for_multiple().

-	event objects are used as event** for event::wait_for_multiple() call, and during the call,
	a socket may be closed, therefore shared_ptr<event> is used instead of an event object.

-	the similar reason for shared_ptr<SocketData> as above.

-	all IAsyncAction and IAsyncOperation should be wrapped as async_to_task() for synchronization.

special cases:
-	after a socket is converted to a server socket, connect() cannot be called on this socket as a client.

-	bind() and connect() cannot be applied to a socket at the same time.

-	it is possible that a socket is closed during select().

-	WSAGetLastError() and getsockopt() with SO_ERROR will *clear* the errors.

-	recv() and recvfrom() on unconnected stream socket will return different error code,
	recvfrom() will check this in itself, but recv() will check this in recv_impl()

differences from winsock:
-	send() and sendto() cannot send zero size of data as in winsock, because WinRT does
	nothing at all for zero size of data.

-	INADDR_ANY stands for "::" in WinRT, so it cannot be directly translated to "0.0.0.0" as winsock.

-	StreamSocket canno be bound to a specific port therefore a client socket cannot be bound to a specific
	port as in winsock.

-	in winsock, listen() will convert a socket to be a server socket;
	in winsockem, bind() will convert a socket to be a server socket.

-	(fixed) in winsock, bind() cannot be called on a client socket;
	in winsockem, bind() will convert a client socket to be a server socket.

-	in winsock, reuse on a used port is possible;
	in winsockem, it's impossible.

todo list:
-	(done) convert "0.0.0.0" to "::"
-	(done) WSAGetLastError()
-	setsockopt() and getsockopt()
-	IPv6
-	(done) performance optimization, especially data receiver thread
-	narrow down lock scopes
-	(done) potential concurrency issue when multipthreads call send() or sendto()
*/

#include "pch.h"
#include "winsockem.h"

#include <cstdlib>
#include <cassert>
#include <list>
#include <vector>
#include <set>
#include <sstream>
#include <mutex> // std::recursive_mutex
#include <type_traits> // std::remove_reference
#include <thread>
#include <atomic>

using namespace Platform;
using namespace Windows::Networking;
using namespace Windows::Networking::Sockets;
using namespace Windows::Foundation;
using namespace Concurrency;

#pragma region utils

static Platform::String^ ToNetString(const char* str)
{
	int req_size = ::MultiByteToWideChar(
		CP_ACP,
		0,
		str,
		-1,
		NULL,
		0);

	// need the extra element at the end since MultiByteToWideChar might return a result with ending L'\0' already
	std::vector<wchar_t> outStr (req_size + 1);
	::MultiByteToWideChar(
		CP_ACP,
		0,
		str,
		-1,
		&outStr[0],
		req_size);

		outStr[req_size] = L'\0';

	return ref new String(&outStr[0]);
}

static const std::string ToAsciiString(Platform::String^ netString)
{
	int req_size = ::WideCharToMultiByte(
		CP_ACP,
		0,
		netString->Data(),
		netString->Length(),
		NULL,
		0, NULL, NULL);

	// need the extra element at the end since WideCharToMultiByte might return a result with ending L'\0' already
	std::vector<char> outStr (req_size + 1);
	::WideCharToMultiByte(
		CP_ACP,
		0,
		netString->Data(),
		netString->Length(),
		&outStr[0],
		req_size,
		NULL, NULL);

	outStr[req_size] = '\0';

	return std::string(&outStr[0]);
}

static const std::string basename(const std::string& path)
{
	char filename [MAX_PATH];
	char extension [MAX_PATH];
	_splitpath_s(path.c_str(), NULL, 0, NULL, 0, filename, MAX_PATH, extension, MAX_PATH);

	return std::string(filename) + extension;
}

#define __debug__(msg)\
{\
	std::ostringstream oss;\
	oss << basename(__FILE__) << "(" << __LINE__ << "): [Thread 0x"\
		<< std::hex << ::GetCurrentThreadId() << std::dec << "] - " << msg << std::endl;\
	::OutputDebugStringA(oss.str().c_str());\
}

#define __show__(value) __debug__(#value << ": " << value)

// synchronize may throw exceptions
template<typename ResultType>
static ResultType synchronize(Concurrency::task<ResultType>& t)
{
	Concurrency::event synchronizer;

	t.then([&](Concurrency::task<ResultType> previous) {
		// must catch exceptions caused by the task in then() clause,
		// otherwise the exception will be propagated and suppress synchronizer.set();
		// previous might raise an exception internally which can raise unobserved_task_exception;
		// don't handle the exception here, instead, t.get() will raise the same exception
		try {
			previous.wait();
		}
		catch (Platform::COMException^ e) {
			__debug__(ToAsciiString(e->Message));
		}

		synchronizer.set();
	});

	synchronizer.wait();
	return t.get();
}

static void synchronize(Windows::Foundation::IAsyncAction^ async)
{
	Concurrency::event synchronizer;
	async->Completed = ref new Windows::Foundation::AsyncActionCompletedHandler(
		[&](decltype(async) asyncInfo, Windows::Foundation::AsyncStatus asyncStatus) {
			synchronizer.set();
		}
	);

	synchronizer.wait();
	return async->GetResults();
}

template<typename ResultType>
static ResultType synchronize(Windows::Foundation::IAsyncOperation<ResultType>^ async)
{
	Concurrency::event synchronizer;
	async->Completed = ref new Windows::Foundation::AsyncOperationCompletedHandler<ResultType>(
		[&](decltype(async) asyncInfo, Windows::Foundation::AsyncStatus asyncStatus) {
			synchronizer.set();
		}
	);

	synchronizer.wait();
	return async->GetResults();
}

#define async_to_task(async)\
	create_task([](){}\
	).then([=]() -> decltype(create_task(async)) {\
		return create_task(async);\
	}, Concurrency::task_continuation_context::use_arbitrary()\
	)

template<typename LockType>
struct locker_t {
	locker_t(LockType& lock) : lock(lock) { lock.lock(); }
	~locker_t() { lock.unlock(); };

	LockType& lock;
};

template<typename LockType>
struct unlocker_t {
	unlocker_t(LockType& lock) : lock(lock) { lock.unlock(); }
	~unlocker_t() { lock.lock(); };

	operator bool () const { return false; }
	LockType& lock;
};

// __wse_lock__ can be used alone as a single statement, however, __wse_unlock__ has to be used with another statement, e.g. __wse_unlock__ <statement> or __wse_unlock__ { <statements> }
#define __wse_lock__ locker_t<std::remove_reference<decltype(WinSockEmulation::instance().lock())>::type> __locker__ (WinSockEmulation::instance().lock())
#define __wse_unlock__ if (auto __unlocker__ = unlocker_t<std::remove_reference<decltype(WinSockEmulation::instance().lock())>::type>(WinSockEmulation::instance().lock())) ::exit(-1); else

struct finalizer_t {
	std::function<void()> f;
	finalizer_t(decltype(f) f) : f(f) {}
	~finalizer_t() { f(); }
};

#pragma endregion

struct WinSockEmSocketData
{
	WinSockEmSocketData(Platform::IDisposable^ wrtResource) :
		wrtResource(wrtResource),
		readableEvent(std::make_shared<Concurrency::event>()),
		writeableEvent(std::make_shared<Concurrency::event>()),
		exceptableEvent(std::make_shared<Concurrency::event>()),
		isNonBlocking(false),
		isActive(false),
		lastError(0)
	{
		__debug__("wrtSock " << wrtResource->GetHashCode() << " is created");
	}

	virtual ~WinSockEmSocketData()
	{
		if (wrtResource) {
			__debug__("wrtSock " << wrtResource->GetHashCode() << " is released");

			delete wrtResource;
			wrtResource = nullptr;
		}
	}

	Platform::IDisposable^ wrtResource;
	std::shared_ptr<Concurrency::event> readableEvent;
	std::shared_ptr<Concurrency::event> writeableEvent;
	std::shared_ptr<Concurrency::event> exceptableEvent; // the naming just follows except
	bool isNonBlocking; // blocking by default; changeable using ioctlsocket(..)
	bool isActive; // this flag can be turned off by closesocket() or shutdown() or after an error occurs; when it's flagged off, all operations on this socket should be terminated
	int lastError;
	in_addr bindAddress;
};

struct WinSockEmDatagramSocketData : WinSockEmSocketData
{
public:
	WinSockEmDatagramSocketData(DatagramSocket^ wrtSock) :
		WinSockEmSocketData(wrtSock),
		wrtSock(wrtSock)
	{
		// HACK: datagram is always writeable
		writeableEvent->set();
	}

	DatagramSocket^ wrtSock;
	std::list<DatagramSocketMessageReceivedEventArgs^> messageReceivedArgs;
};

struct WinSockEmStreamSocketData : WinSockEmSocketData
{
	WinSockEmStreamSocketData(StreamSocket^ wrtSock) :
		WinSockEmSocketData(wrtSock),
		wrtSock(wrtSock),
		onReadAsyncCompletedEvent(std::make_shared<Concurrency::event>()),
		isReadAsyncPending(false),
		unsentData(0)
	{
	}

	StreamSocket^ wrtSock;
	std::shared_ptr<Concurrency::event> onReadAsyncCompletedEvent;
	bool isReadAsyncPending; // if it's flagged, no more ReadAsync should be issued for this socket
	std::list<Windows::Storage::Streams::IBuffer^> messageReceivedArgs;
	u_int unsentData;
};

struct WinSockEmStreamSocketListenerData : WinSockEmSocketData
{
	WinSockEmStreamSocketListenerData(StreamSocketListener^ wrtSock) :
		WinSockEmSocketData(wrtSock),
		wrtSock(wrtSock),
		backlog(1)
	{
	}

	StreamSocketListener^ wrtSock;
	int backlog;
	std::list<StreamSocketListenerConnectionReceivedEventArgs^> connectionReceivedArgs;
};

class WinSockEmulation
{
public:
	// use std::shared_ptr in case when an event is fired after a socket has been closed
	typedef std::map<SOCKET, std::shared_ptr<WinSockEmDatagramSocketData>> DatagramSocketMap;

	// use std::shared_ptr in case when an event is fired after a socket has been closed
	typedef std::map<SOCKET, std::shared_ptr<WinSockEmStreamSocketData>> StreamSocketMap;

	// use std::shared_ptr in case when an event is fired after a socket has been closed
	typedef std::map<SOCKET, std::shared_ptr<WinSockEmStreamSocketListenerData>> StreamSocketListenerMap;

private:
	DatagramSocketMap mDatagramSockets;
	StreamSocketMap mStreamSockets;
	StreamSocketListenerMap mStreamSocketListeners;
	std::recursive_mutex mLock;

	// this event is triggered in socket(), accept(), closesocket(), shutdown(); it is turned off in stream_socket_data_receiver
	std::shared_ptr<Concurrency::event> mStreamSocketsChangedEvent;

	//IAsyncAction^ mReceiverWorker;
	std::atomic<bool> mIsClosing;
	std::thread mDataReceiverThread;
	SOCKET mNewSocket;

	static WinSockEmulation* sInstance;

public:
	WinSockEmulation() :
		mIsClosing(false),
		mStreamSocketsChangedEvent(std::make_shared<Concurrency::event>()),
		mDataReceiverThread(&WinSockEmulation::stream_socket_data_receiver, this),
		mNewSocket(1000) {}

	~WinSockEmulation()
	{
		try {
			assert(!mIsClosing);
			mIsClosing = true;
			mStreamSocketsChangedEvent->set();
			mDataReceiverThread.join(); // may throw
		}
		catch (Exception^ e) {
			__debug__(ToAsciiString(e->Message));
		}
	}

	static WinSockEmulation& instance()
	{
		if (!sInstance) sInstance = new WinSockEmulation;
		return *sInstance;
	}

public:
	DatagramSocketMap& datagram_socket_map() { return mDatagramSockets; }

	StreamSocketMap& stream_socket_map() { return mStreamSockets; }

	StreamSocketListenerMap& stream_socket_listener_map() { return mStreamSocketListeners; }

	decltype(mLock)& lock() { return mLock; }

	decltype(mStreamSocketsChangedEvent)& stream_sockets_changed_event() { return mStreamSocketsChangedEvent; }

	SOCKET new_socket(); // implemented later in "delayed implementation" since it relies on some of the helpers

private:
	void stream_socket_data_receiver()
	{
		__debug__("stream_socket_data_receiver starts");

		while (!mIsClosing) {
			__wse_lock__;

			std::vector<std::shared_ptr<Concurrency::event>> events;
			events.push_back(mStreamSocketsChangedEvent);

			for (StreamSocketMap::value_type& pair : mStreamSockets) {
				auto sockData = pair.second;

				// may only happen after shutdown (not closesocket)
				if (!sockData->isActive) continue;

				auto wrtSock = sockData->wrtSock;

				if (sockData->messageReceivedArgs.size() < 128 && !sockData->isReadAsyncPending) {
					sockData->isReadAsyncPending = true;
					auto buffer = ref new Windows::Storage::Streams::Buffer(4096);

					async_to_task(wrtSock->InputStream->ReadAsync(buffer, buffer->Capacity, Windows::Storage::Streams::InputStreamOptions::Partial)
					).then([sockData](Windows::Storage::Streams::IBuffer^ buffer) {
						// closed connection will raise an exception instead of returning an empty buffer
						__wse_lock__;
						sockData->messageReceivedArgs.push_back(buffer);
					}).then([sockData](task<void> previous) {
						__wse_lock__;

						try {
							previous.wait();
							sockData->lastError = 0;
						}
						catch (COMException^ e) {
							__debug__(ToAsciiString(e->Message));
							sockData->isActive = false; // TODO: maybe only apply to specific type of exceptions

							// to be handled in recv_impl()

							if (e->Message == L"An existing connection was forcibly closed by the remote host.\r\n") {
								sockData->lastError = WSAEDISCON;
							}

							sockData->lastError = SOCKET_ERROR; // TODO:
						}

						sockData->isReadAsyncPending = false;
						sockData->onReadAsyncCompletedEvent->set();
						sockData->readableEvent->set();
					});
				}

				events.push_back(sockData->onReadAsyncCompletedEvent);
			}

			std::vector<Concurrency::event*> waitable_events;
			for (std::shared_ptr<Concurrency::event>& event : events) waitable_events.push_back(event.get());
			assert(!waitable_events.empty());

			int index = Concurrency::COOPERATIVE_WAIT_TIMEOUT;

			__wse_unlock__
			index = Concurrency::event::wait_for_multiple(&waitable_events[0], waitable_events.size(), false, Concurrency::COOPERATIVE_TIMEOUT_INFINITE);

			assert(index != Concurrency::COOPERATIVE_WAIT_TIMEOUT);
			assert(index < waitable_events.size());

			waitable_events[index]->reset();
		}

		__debug__("stream_socket_data_receiver exits");
	}
};

WinSockEmulation* WinSockEmulation::sInstance = NULL;

__declspec(thread) static int last_error = 0;

#pragma region helpers

// bliu: all set_wse_result() without sockData as an input parameter are thread-safe

template<typename ResultType>
static ResultType set_wse_result(int error_code, ResultType result, std::shared_ptr<WinSockEmSocketData> sockData)
{
	sockData->lastError = error_code;
	error_code ? sockData->exceptableEvent->set() : sockData->exceptableEvent->reset();

	last_error = error_code;

	return result;
}

template<typename SocketDataType>
static int set_wse_result(int error_code, std::shared_ptr<SocketDataType> sockData)
{
	sockData->lastError = error_code;
	error_code ? sockData->exceptableEvent->set() : sockData->exceptableEvent->reset();

	last_error = error_code;

	return error_code;
}

template<typename ResultType>
static ResultType set_wse_result(int error_code, ResultType result)
{
	last_error = error_code;

	return result;
}

static std::shared_ptr<WinSockEmDatagramSocketData> findDatagramSocketData(SOCKET s)
{
	__wse_lock__;

	auto it = WinSockEmulation::instance().datagram_socket_map().find(s);
	if (it != WinSockEmulation::instance().datagram_socket_map().end()) {
		assert(it->first == s);
		return it->second;
	}

	return std::shared_ptr<WinSockEmDatagramSocketData>();
}

static std::shared_ptr<WinSockEmStreamSocketData> findStreamSocketData(SOCKET s)
{
	__wse_lock__;

	auto it = WinSockEmulation::instance().stream_socket_map().find(s);
	if (it != WinSockEmulation::instance().stream_socket_map().end()) {
		assert(it->first == s);
		return it->second;
	}

	return std::shared_ptr<WinSockEmStreamSocketData>();
}

static std::shared_ptr<WinSockEmStreamSocketListenerData> findStreamSocketListenerData(SOCKET s)
{
	__wse_lock__;

	auto it = WinSockEmulation::instance().stream_socket_listener_map().find(s);
	if (it != WinSockEmulation::instance().stream_socket_listener_map().end()) {
		assert(it->first == s);
		return it->second;
	}

	return std::shared_ptr<WinSockEmStreamSocketListenerData>();
}

std::shared_ptr<WinSockEmSocketData> findSocketData(SOCKET s)
{
	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) return sockData;
	if (auto sockData = findStreamSocketData(s)) return sockData;
	if (auto sockData = findStreamSocketListenerData(s)) return sockData;

	return std::shared_ptr<WinSockEmSocketData>();
}

#pragma endregion

#pragma region delayed implementation

SOCKET WinSockEmulation::new_socket()
{
	__wse_lock__;

	while (true) {
		if (mNewSocket < 1000) mNewSocket = 1000;

		if (!findSocketData(mNewSocket++)) break;
	}

	return mNewSocket;
}

#pragma endregion

#pragma region event handlers

// bliu: performance issue if a lot of sockets are created with tons of packets received
void
DatagramSocketMessageReceivedHandler(DatagramSocket^ wrtSock, DatagramSocketMessageReceivedEventArgs^ args)
{
	__wse_lock__;

	for (WinSockEmulation::DatagramSocketMap::value_type& pair : WinSockEmulation::instance().datagram_socket_map()) {
		if (!Platform::Object::ReferenceEquals(pair.second->wrtSock, wrtSock)) continue;

		auto socket = pair.first;
		auto sockData = pair.second;
		auto& args_list = sockData->messageReceivedArgs;

		try {
			args->GetDataReader();
		}
		catch (COMException^ e) {
			sockData->isActive = false;
		}

		// drop the latest message to keep the size of queue under limit
		if (args_list.size() < 128 || !sockData->isActive) {
			args_list.push_back(args);
		}
		else {
			__debug__("drop the oldest message because the queue is too large: " << args_list.size());
		}

		assert(!args_list.empty());

		sockData->readableEvent->set();
		__debug__("set readableEvent for socket " << socket);

		return;
	}

	__debug__("please check if wrtSock " << wrtSock->GetHashCode() << " has ever been released before. if yes, ignore this message");
}

// TODO: this approach has one gotcha when a client actively closes a connection while the request is still in backlog
void
StreamSocketListenerConnectionReceivedHandler(StreamSocketListener^ wrtSock, StreamSocketListenerConnectionReceivedEventArgs^ args)
{
	__wse_lock__;

	for (WinSockEmulation::StreamSocketListenerMap::value_type& pair : WinSockEmulation::instance().stream_socket_listener_map()) {
		if (!Platform::Object::ReferenceEquals(pair.second->wrtSock, wrtSock)) continue;

		auto socket = pair.first;
		auto sockData = pair.second;
		auto& args_list = sockData->connectionReceivedArgs;

		// drop the latest connection to keep the size of queue under limit
		if (args_list.size() < sockData->backlog || !sockData->isActive) {
			args_list.push_back(args);
		}
		else {
			__debug__("drop the latest connection because the queue is too large: " << args_list.size());
		}

		assert(!args_list.empty());

		sockData->readableEvent->set();
		__debug__("set connectionReceivedEvent for socket " << socket);

		return;
	}

	__debug__("please check if wrtSock " << wrtSock->GetHashCode() << " has ever been released before. if yes, ignore this message");
}

#pragma endregion

#pragma region WSA helpers

WINSOCK_API_LINKAGE
u_long
WSAAPI
htonl(
    _In_ u_long hostlong
    )
{
	return _byteswap_ulong(hostlong);
}

WINSOCK_API_LINKAGE
u_short
WSAAPI
htons(
    _In_ u_short hostshort
    )
{
	return _byteswap_ushort(hostshort);
}

WINSOCK_API_LINKAGE
u_short
WSAAPI
ntohs(
    _In_ u_short netshort
    )
{
	return _byteswap_ushort(netshort);
}

WINSOCK_API_LINKAGE
u_long
WSAAPI
ntohl(
    _In_ u_long netlong
    )
{
   return _byteswap_ulong(netlong);
}

/* 
 * Check whether "cp" is a valid ascii representation
 * of an Internet address and convert to a binary address.
 * Returns 1 if the address is valid, 0 if not.
 * This replaces inet_addr, the return value from which
 * cannot distinguish between failure and a local broadcast address.
 */
/*  */
/* inet_aton */
int inet_aton(const char *cp, struct in_addr *addr)
{
    u_long val;
    int base, n;
    char c;
//    u_int parts[4];
//    u_int *pp = parts;
    u_long parts[4];
    u_long* pp = parts;

    c = *cp;
    for (;;) {
        /*
         * Collect number up to ``.''.
         * Values are specified as for C:
         * 0x=hex, 0=octal, isdigit=decimal.
         */
        if (!isdigit(c))
            return (0);
        val = 0; base = 10;
        if (c == '0') {
            c = *++cp;
            if (c == 'x' || c == 'X')
                base = 16, c = *++cp;
            else
                base = 8;
        }
        for (;;) {
            if (isascii(c) && isdigit(c)) {
                val = (val * base) + (c - '0');
                c = *++cp;
            } else if (base == 16 && isascii(c) && isxdigit(c)) {
                val = (val << 4) |
                    (c + 10 - (islower(c) ? 'a' : 'A'));
                c = *++cp;
            } else
            break;
        }
        if (c == '.') {
            /*
             * Internet format:
             *  a.b.c.d
             *  a.b.c   (with c treated as 16 bits)
             *  a.b (with b treated as 24 bits)
             */
            if (pp >= parts + 3)
                return (0);
            *pp++ = val;
            c = *++cp;
        } else
            break;
    }
    /*
     * Check for trailing characters.
     */
    if (c != '\0' && (!isascii(c) || !isspace(c)))
        return (0);
    /*
     * Concoct the address according to
     * the number of parts specified.
     */
    n = pp - parts + 1;
    switch (n) {

    case 0:
        return (0);     /* initial nondigit */

    case 1:             /* a -- 32 bits */
        break;

    case 2:             /* a.b -- 8.24 bits */
        if (val > 0xffffff)
            return (0);
        val |= parts[0] << 24;
        break;

    case 3:             /* a.b.c -- 8.8.16 bits */
        if (val > 0xffff)
            return (0);
        val |= (parts[0] << 24) | (parts[1] << 16);
        break;

    case 4:             /* a.b.c.d -- ******* bits */
        if (val > 0xff)
            return (0);
        val |= (parts[0] << 24) | (parts[1] << 16) | (parts[2] << 8);
        break;
    }
    if (addr)
        addr->s_addr = htonl(val);
    return (1);
}

/* inet_addr */
WINSOCK_API_LINKAGE
unsigned long
WSAAPI
inet_addr(
    _In_z_ const char FAR * cp
    )
{
    struct in_addr val;

    if (inet_aton(cp, &val)) {
        return (val.s_addr);
    }
    return (INADDR_NONE);
}

WINSOCK_API_LINKAGE
char FAR *
WSAAPI
inet_ntoa(
    _In_ struct in_addr in
    )
{
	unsigned char* bytes = (unsigned char *)&in;
	__declspec(thread) static char buffer [17];
	buffer[16] = '\0';
	sprintf_s(buffer, 16, "%d.%d.%d.%d", bytes[0], bytes[1], bytes[2], bytes[3]); 
	return buffer;
}

int fdissetfunc(SOCKET fd, fd_set FAR * set)
{
	u_int __i;
    for (__i = 0; __i < ((fd_set FAR *)(set))->fd_count; __i++) {
        if (((fd_set FAR *)(set))->fd_array[__i] == (fd)) {
            return 1;
        }
    }
	return 0;
}

#pragma endregion

WINSOCK_API_LINKAGE
int
WSAAPI
WSAGetLastError()
{
	int result = last_error;
	last_error = 0;
	return result;
}

WINSOCK_API_LINKAGE
_Must_inspect_result_
SOCKET
WSAAPI
socket(
    _In_ int af,
    _In_ int type,
    _In_ int protocol
    )
{
	if (af != AF_INET) return set_wse_result(WSAEAFNOSUPPORT, INVALID_SOCKET);
	if (type != SOCK_DGRAM && type != SOCK_STREAM) return set_wse_result(WSAESOCKTNOSUPPORT, INVALID_SOCKET);

	__wse_lock__;

	if (type == SOCK_DGRAM) {
		if (WinSockEmulation::instance().datagram_socket_map().size() >= 1024) return set_wse_result(WSAEMFILE, INVALID_SOCKET);

		Windows::Networking::Sockets::DatagramSocket^ wrtSock = ref new Windows::Networking::Sockets::DatagramSocket();
		SOCKET socket = WinSockEmulation::instance().new_socket();
		wrtSock->MessageReceived += ref new TypedEventHandler<DatagramSocket^, DatagramSocketMessageReceivedEventArgs^>(&DatagramSocketMessageReceivedHandler);

		auto insert = WinSockEmulation::instance().datagram_socket_map().insert(std::make_pair(socket, std::make_shared<WinSockEmDatagramSocketData>(wrtSock)));
		assert(insert.second);

		return set_wse_result(0, socket);
	}
	else if (type == SOCK_STREAM) {
		if (WinSockEmulation::instance().stream_socket_map().size() >= 1024) return set_wse_result(WSAEMFILE, INVALID_SOCKET);

		Windows::Networking::Sockets::StreamSocket^ wrtSock = ref new Windows::Networking::Sockets::StreamSocket();
		SOCKET socket = WinSockEmulation::instance().new_socket();

		auto insert = WinSockEmulation::instance().stream_socket_map().insert(std::make_pair(socket, std::make_shared<WinSockEmStreamSocketData>(wrtSock)));
		assert(insert.second);

		WinSockEmulation::instance().stream_sockets_changed_event()->set();
		return set_wse_result(0, socket);
	}
	
	assert(false);
	return set_wse_result(WSAESOCKTNOSUPPORT, INVALID_SOCKET);
}

WINSOCK_API_LINKAGE
int
WSAAPI
closesocket(
    _In_ SOCKET s
    )
{
	__wse_lock__;

	// don't need __wse_unlock__ because shutdown doesn't cause waiting
	::shutdown(s, 2);

	if (WinSockEmulation::instance().datagram_socket_map().erase(s) > 0
		|| WinSockEmulation::instance().stream_socket_map().erase(s) > 0
		|| WinSockEmulation::instance().stream_socket_listener_map().erase(s) > 0) {
		return set_wse_result(0, 0);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
bind(
    _In_ SOCKET s,
    _In_reads_bytes_(namelen) const struct sockaddr FAR * name,
    _In_ int namelen
    )
{
	if (name == NULL) return set_wse_result(WSAEFAULT, SOCKET_ERROR);
	if (namelen != sizeof(sockaddr_in)) return set_wse_result(WSAEFAULT, SOCKET_ERROR);
	const sockaddr_in* sai = reinterpret_cast<const sockaddr_in*>(name);
	if (sai->sin_family != AF_INET)  return set_wse_result(WSAEFAULT, SOCKET_ERROR);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		auto wrtSock = sockData->wrtSock;
		char* ipchars = inet_ntoa(sai->sin_addr);
		String^ ipAddress = ToNetString(ipchars);
		HostName^ hostName = ref new HostName(ipAddress);
		wchar_t portbuff[6];
		::_itow_s(ntohs(sai->sin_port), portbuff, 6, 10);
		String^ localServiceName = ref new String(portbuff);

		// bind() is always synced
		try {
			__wse_unlock__
			synchronize(ipAddress == L"0.0.0.0" ? wrtSock->BindServiceNameAsync(localServiceName) : wrtSock->BindEndpointAsync(hostName, localServiceName));

			sockData->isActive = true;
			sockData->bindAddress = sai->sin_addr;

			return set_wse_result(0, 0, sockData);
		}
		catch (Platform::COMException^ e) {
			__debug__(ToAsciiString(e->Message));

			if (e->Message == L"Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n") {
				return set_wse_result(WSAEADDRINUSE, SOCKET_ERROR, sockData);
			}

			return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);
			//WSAEADDRNOTAVAIL sai is not a local IP
		}
	}

	// convert StreamSocket to StreamSocketListener
	if (auto sockData = findStreamSocketData(s)) {
		// the socket is already bound if connected
		if (sockData->isActive) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);

		auto new_socket_data = std::make_shared<WinSockEmStreamSocketListenerData>(ref new StreamSocketListener);
		new_socket_data->isNonBlocking = sockData->isNonBlocking;
		new_socket_data->lastError = sockData->lastError;

		::closesocket(s);

		auto stream_listener_insert = WinSockEmulation::instance().stream_socket_listener_map().insert(std::make_pair(s, new_socket_data));
		assert(stream_listener_insert.second);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		auto wrtSock = sockData->wrtSock;

		// detect duplicate bind attempt
		try {
			wrtSock->ConnectionReceived += ref new TypedEventHandler<StreamSocketListener^, StreamSocketListenerConnectionReceivedEventArgs^>(StreamSocketListenerConnectionReceivedHandler);
		}
		catch (COMException^ e) {
			return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);
		}

		char* ipchars = inet_ntoa(sai->sin_addr);
		String^ ipAddress = ToNetString(ipchars);
		HostName^ hostName = ref new HostName(ipAddress);
		wchar_t portbuff[6];
		::_itow_s(ntohs(sai->sin_port), portbuff, 6, 10);
		String^ localServiceName = ref new String(portbuff);

		// bind() is always synced
		try {
			__wse_unlock__
			synchronize(ipAddress == L"0.0.0.0" ? wrtSock->BindServiceNameAsync(localServiceName) : wrtSock->BindEndpointAsync(hostName, localServiceName));

			sockData->isActive = true;
			sockData->bindAddress = sai->sin_addr;

			return set_wse_result(0, 0, sockData);
		}
		catch (Platform::COMException^ e) {
			__debug__(ToAsciiString(e->Message));

			if (e->Message == L"Only one usage of each socket address (protocol/network address/port) is normally permitted.\r\n") {
				return set_wse_result(WSAEADDRINUSE, SOCKET_ERROR, sockData);
			}

			return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);
			//WSAEADDRNOTAVAIL sai is not a local IP
		}
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
connect(
    _In_ SOCKET s,
    _In_reads_bytes_(namelen) const struct sockaddr FAR * name,
    _In_ int namelen
    )
{
	auto check_args = [&]() -> int {
		if (name == NULL) return WSAEFAULT;
		if (namelen != sizeof(sockaddr_in)) WSAEFAULT;
		if (name->sa_family != AF_INET) WSAEFAULT;

		return 0;
	};

	const sockaddr_in* sai = reinterpret_cast<const sockaddr_in*>(name);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto wrtSock = sockData->wrtSock;
		char* ipchars = inet_ntoa(sai->sin_addr);
		String^ ipAddress = ToNetString(ipchars);
		HostName^ hostName = ref new HostName(ipAddress);
		wchar_t portbuff[6];
		::_itow_s(htons(sai->sin_port), portbuff, 6, 10);
		String^ remoteServiceName = ref new String(portbuff);

		try {
			synchronize(Windows::Networking::Sockets::DatagramSocket::GetEndpointPairsAsync(hostName, remoteServiceName));
		}
		catch (Platform::COMException^ e) {
			__debug__(ToAsciiString(e->Message));
			// TODO: need to set socket error which can be retrieved by getsockopt

			if (e->Message == L"No such host is known.\r\n") {
				return set_wse_result(WSAEADDRNOTAVAIL, SOCKET_ERROR, sockData);
			}

			return SOCKET_ERROR; // TODO:
		}

		auto t = async_to_task(wrtSock->ConnectAsync(hostName, remoteServiceName)
		).then([sockData](Concurrency::task<void> previous) {
			__wse_lock__;

			try {
				previous.wait();
				sockData->isActive = true;
				return set_wse_result(0, sockData);
			}
			catch (Platform::COMException^ e) {
				__debug__(ToAsciiString(e->Message));
				// TODO: need to set socket error which can be retrieved by getsockopt

				if (e->Message == L"No such host is known.\r\n") {
					return set_wse_result(WSAEADDRNOTAVAIL, sockData);
				}
				
				return set_wse_result(WSAECONNABORTED, sockData);
			}
		});

		if (sockData->isNonBlocking) return set_wse_result(0, 0, sockData);

		int result = 0;

		// don't put set_wse_result into __wse_unlock__, otherwise it's not threadsafe any more
		__wse_unlock__
		result = synchronize(t);

		return result == 0 ? set_wse_result(0, 0, sockData) : set_wse_result(result, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto wrtSock = sockData->wrtSock;
		char* ipchars = inet_ntoa(sai->sin_addr);
		String^ ipAddress = ToNetString(ipchars);
		HostName^ hostName = ref new HostName(ipAddress);
		wchar_t portbuff[6];
		::_itow_s(htons(sai->sin_port), portbuff, 6, 10);
		String^ remoteServiceName = ref new String(portbuff);

		auto t = async_to_task(wrtSock->ConnectAsync(hostName, remoteServiceName)
		).then([sockData](Concurrency::task<void> previous) {
			__wse_lock__;

			try {
				previous.wait();
				sockData->isActive = true;
				sockData->writeableEvent->set();
				return set_wse_result(0, sockData);
			}
			catch (Platform::COMException^ e) {
				__debug__(ToAsciiString(e->Message));
				
				if (e->Message == L"No connection could be made because the target machine actively refused it.\r\n") {
					return set_wse_result(WSAECONNREFUSED, sockData);
				}
				
				if (e->Message == L"No such host is known.\r\n") {
					return set_wse_result(WSAEADDRNOTAVAIL, sockData);
				}

				return set_wse_result(WSAEISCONN, sockData);
			}
		});

		if (sockData->isNonBlocking) return set_wse_result(WSAEWOULDBLOCK, SOCKET_ERROR, sockData);

		int result = 0;

		// don't put set_wse_result into __wse_unlock__, otherwise it's not threadsafe any more
		__wse_unlock__
		result = synchronize(t);

		return result == 0 ? set_wse_result(0, 0, sockData) : set_wse_result(result, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		return set_wse_result(WSAEINVAL, SOCKET_ERROR);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

static
int
recv_impl(
    _In_ SOCKET s,
    _Out_writes_bytes_to_(len, return) __out_data_source(NETWORK) char FAR * buf,
    _In_ int len,
    _In_ int flags,
    _In_ sockaddr_in* sai,
    _In_ int* sai_len,
    _In_ bool with_sai
    )
{
	auto check_args = [&]() -> int {
		if (with_sai) {
			if (sai == NULL && sai_len != NULL) return WSAEFAULT;
			if (sai != NULL && sai_len == NULL) return WSAEFAULT;
			if (sai_len != NULL && *sai_len != sizeof(sockaddr_in)) return WSAEINVAL;
		}

		if (buf == NULL) return WSAEINVAL;
		if (len <= 0) return WSAEINVAL;

		return 0;
	};

	__wse_lock__;

	// from http://msdn.microsoft.com/en-us/library/windows/desktop/ms740120(v=vs.85).aspx
	// For message-oriented sockets, data is extracted from the first enqueued message, up to the size of the buffer specified.
	// If the datagram or message is larger than the buffer specified, the buffer is filled with the first part of the datagram, and recvfrom generates the error WSAEMSGSIZE.
	// For unreliable protocols (for example, UDP) the excess data is lost.
	// For UDP if the packet received contains no data (empty), the return value from the recvfrom function function is zero.
	if (auto sockData = findDatagramSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto wrtSock = sockData->wrtSock;
		auto& args_list = sockData->messageReceivedArgs;

		while (args_list.empty()) {
			if (wrtSock->Information->LocalAddress == nullptr) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);
			if (sockData->isNonBlocking) return set_wse_result(WSAEWOULDBLOCK, SOCKET_ERROR, sockData);

			__debug__("waiting for readableEvent for socket " << s);

			__wse_unlock__
			sockData->readableEvent->wait();

			if (!sockData->isActive && args_list.empty()) return set_wse_result(WSAESHUTDOWN, SOCKET_ERROR, sockData);

			__debug__("got readableEvent for socket " << s);
		}

		assert(!args_list.empty());

		auto args = args_list.front();

		// this finalizer protects the case where args->GetDataReader() raises an exception
		finalizer_t finalizer ([&]() {
			// consume the args and event afterwards
			args_list.pop_front();

			if (args_list.empty()) {
				sockData->readableEvent->reset();
				__debug__("reset readableEvent for socket " << s);
			}
		});

		try {
			args->GetDataReader();
		}
		catch (COMException^ e) {
			__debug__(ToAsciiString(e->Message));
			sockData->isActive = false; // HACK: any exception will deactivate this socket

			if (e->Message == L"An existing connection was forcibly closed by the remote host.\r\n") {
				return set_wse_result(WSAECONNRESET, SOCKET_ERROR, sockData);
			}

			return set_wse_result(WSAENETRESET, SOCKET_ERROR, sockData); // TODO:
		}

		auto reader = args->GetDataReader();
		int unconsumed = reader->UnconsumedBufferLength;

		// truncate the received message if buffer size is not large enough
		bool truncated = unconsumed > len;
		
		if (truncated) unconsumed = len;

		if (unconsumed > 0) {
			Platform::Array<byte>^ woArray = ref new Platform::Array<byte>(unconsumed);
			reader->ReadBytes(woArray);
			reader->DetachStream();
			memcpy(buf, woArray->Data, woArray->Length);
		}

		if (sai != NULL) {
			// cannot use getpeername() here because for udp server the remote address might be null
			// sai can only be extracted from args
			const std::string address = ToAsciiString(args->RemoteAddress->CanonicalName);
			inet_aton(address.c_str(), &sai->sin_addr);
			const std::string port = ToAsciiString(args->RemotePort);
			sai->sin_port = htons(atoi(port.c_str()));
		}

		return truncated ? set_wse_result(WSAEMSGSIZE, SOCKET_ERROR, sockData) : set_wse_result(0, unconsumed, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (!sockData->isActive) {
			// if not connected
			if (wrtSock->Information->LocalAddress == nullptr) return set_wse_result(with_sai ? WSAEINVAL : WSAENOTCONN, SOCKET_ERROR, sockData);

			// if connected, but closed or disconnected by the remote endpoint
			return sockData->wrtResource != nullptr ? set_wse_result(0, 0, sockData) : set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);
		}

		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto& args_list = sockData->messageReceivedArgs;

		while (args_list.empty()) {
			if (wrtSock->Information->LocalAddress == nullptr) return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);

			if (sockData->isNonBlocking) return set_wse_result(WSAEWOULDBLOCK, SOCKET_ERROR, sockData);

			__debug__("waiting for readableEvent for socket " << s);

			__wse_unlock__
			sockData->readableEvent->wait();

			// if sockData->wrtResource is nullptr, it means the socket has been closed or shutdown actively; otherwise, it's a remote disconnection
			if (!sockData->isActive) return sockData->wrtResource ? set_wse_result(0, 0, sockData) : set_wse_result(WSAEINTR, SOCKET_ERROR, sockData);

			__debug__("got readableEvent for socket " << s);
		}

		assert(!args_list.empty());

		auto buffer = args_list.front();
		assert(buffer->Length > 0);

		int consuming = std::min<int>(buffer->Length, len);
		assert(consuming > 0);

		auto reader = Windows::Storage::Streams::DataReader::FromBuffer(buffer);
		Platform::Array<byte>^ woArray = ref new Platform::Array<byte>(consuming);
		reader->ReadBytes(woArray);
		//reader->DetachStream();
		memcpy(buf, woArray->Data, woArray->Length);

		if (with_sai) {
			::getpeername(s, (sockaddr*)sai, sai_len);
			// TODO: need to check the result of getpeername()
		}

		// consume the args and event after success
		args_list.pop_front();

		// put unconsumed data back to the front of args_list for next recv call
		if (reader->UnconsumedBufferLength > 0) {
			args_list.push_front(reader->ReadBuffer(reader->UnconsumedBufferLength));
		}

		if (args_list.empty()) {
			sockData->readableEvent->reset();
			__debug__("reset readableEvent for socket " << s);
		}

		return set_wse_result(0, consuming, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
recv(
    _In_ SOCKET s,
    _Out_writes_bytes_to_(len, return) __out_data_source(NETWORK) char FAR * buf,
    _In_ int len,
    _In_ int flags
    )
{
	return recv_impl(s, buf, len, flags, NULL, NULL, false);
}

WINSOCK_API_LINKAGE
int
WSAAPI
recvfrom(
    _In_ SOCKET s,
    _Out_writes_bytes_to_(len, return) __out_data_source(NETWORK) char FAR * buf,
    _In_ int len,
    _In_ int flags,
    _Out_writes_bytes_to_opt_(*fromlen, *fromlen) struct sockaddr FAR * from,
    _Inout_opt_ int FAR * fromlen
    )
{
	return recv_impl(s, buf, len, flags, (sockaddr_in*)from, fromlen, true);
}

WINSOCK_API_LINKAGE
int
WSAAPI
send(
    _In_ SOCKET s,
    _In_reads_bytes_(len) const char FAR * buf,
    _In_ int len,
    _In_ int flags
    )
{
	auto check_args = [&]() -> int {
		if (buf == NULL && len) return WSAEFAULT;
		if (len < 0) return WSAEINVAL;

		return 0;
	};

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (wrtSock->Information->RemoteAddress == nullptr) return set_wse_result(WSAENOTCONN, SOCKET_ERROR);

		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		if (len == 0) return set_wse_result(0, 0, sockData);

		Windows::Storage::Streams::DataWriter^ dw = ref new Windows::Storage::Streams::DataWriter(wrtSock->OutputStream);
		Platform::Array<unsigned char>^ wbuf = ref new Platform::Array<unsigned char>((byte*)buf, len);
		dw->WriteBytes(wbuf);

		auto t = async_to_task(dw->StoreAsync()
		).then([dw](unsigned int) {
			return async_to_task(dw->FlushAsync());
		}).then([dw](bool) {
			dw->DetachStream();
		}).then([sockData](Concurrency::task<void> previous) {
			__wse_lock__;

			try {
				previous.wait();
				return set_wse_result(0, sockData);
			}
			catch (Platform::COMException^ e) {
				__debug__(ToAsciiString(e->Message));
				return set_wse_result(WSAENOTCONN, sockData);
			}
		});

		if (sockData->isNonBlocking) return set_wse_result(0, len, sockData);

		int result = 0;

		// don't put set_wse_result into __wse_unlock__, otherwise it's not threadsafe any more
		__wse_unlock__
		result = synchronize(t);
		
		return result == 0 ? set_wse_result(0, len, sockData) : set_wse_result(result, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (!sockData->isActive && wrtSock->Information->LocalAddress == nullptr) return set_wse_result(WSAENOTCONN, SOCKET_ERROR);

		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		if (len == 0) return set_wse_result(0, 0, sockData);

		Windows::Storage::Streams::DataWriter^ dw = ref new Windows::Storage::Streams::DataWriter(wrtSock->OutputStream);
		Platform::Array<unsigned char>^ wbuf = ref new Platform::Array<unsigned char>((byte*)buf, len);
		dw->WriteBytes(wbuf);

		auto send_completed_event = std::make_shared<Concurrency::event>();
		sockData->unsentData += len;

		auto t = async_to_task(dw->StoreAsync()
		).then([dw](unsigned int result) {
			__debug__("dw->StoreAsync(): " << result);
			return async_to_task(dw->FlushAsync());
		}).then([dw](bool result) {
			__debug__("dw->FlushAsync(): " << result);
			dw->DetachStream();
		}).then([sockData, len, send_completed_event](Concurrency::task<void> previous) {
			__wse_lock__;

			finalizer_t finalizer ([sockData, len, send_completed_event]() {
				sockData->unsentData -= len;
				send_completed_event->reset();
				if (sockData->unsentData < sockData->wrtSock->Control->OutboundBufferSizeInBytes) sockData->writeableEvent->set();
			});

			try {
				previous.wait();
				return set_wse_result(0, sockData);
			}
			catch (Platform::COMException^ e) {
				__debug__(ToAsciiString(e->Message));
				sockData->isActive = false;

				if (e->Message == L"An existing connection was forcibly closed by the remote host.\r\n") {
					return set_wse_result(WSAENOTCONN, sockData);
				}

				// TODO: other errors?
				return set_wse_result(WSAENOTCONN, sockData);
			}
		});

		finalizer_t finalizer ([sockData]() {
			if (sockData->unsentData >= sockData->wrtSock->Control->OutboundBufferSizeInBytes) {
				sockData->writeableEvent->reset();
			}
		});

		if (sockData->isNonBlocking) {
			if (sockData->unsentData - len >= wrtSock->Control->OutboundBufferSizeInBytes) {
				return set_wse_result(WSAEWOULDBLOCK, SOCKET_ERROR, sockData);
			}

			return set_wse_result(0, len, sockData);
		}

		// both send_completed_event and sockData are local variables, so it's safe to use the pointers directly
		std::vector<Concurrency::event*> events;
		events.push_back(send_completed_event.get());
		events.push_back(sockData->writeableEvent.get());

		int index = Concurrency::COOPERATIVE_WAIT_TIMEOUT;

		__wse_unlock__
		index = Concurrency::event::wait_for_multiple(&events[0], events.size(), false, Concurrency::COOPERATIVE_TIMEOUT_INFINITE);

		// index == 0 means the send task completes so t.get() is able to be called without blocking
		int result = index == 0 ? t.get() : sockData->isActive ? 0 : WSAECONNABORTED;

		// don't put set_wse_result into __wse_unlock__, otherwise it will not be threadsafe any more
		return result == 0 ? set_wse_result(0, len, sockData) : set_wse_result(result, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
sendto(
	_In_ SOCKET s,
	_In_reads_bytes_(len) const char FAR * buf,
	_In_ int len,
	_In_ int flags,
	_In_reads_bytes_(tolen) const struct sockaddr FAR * to,
	_In_ int tolen
	)
{
	auto check_args = [&]() -> int {
		if (tolen != sizeof(sockaddr_in)) return WSAEFAULT;
		if (!to) return WSAEDESTADDRREQ;
		if (to->sa_family != AF_INET) return WSAEAFNOSUPPORT;

		return 0;
	};

	const sockaddr_in* sai = reinterpret_cast<const sockaddr_in*>(to);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		if (len == 0) return set_wse_result(0, 0, sockData);

		auto wrtSock = sockData->wrtSock;

		String^ ipAddress = ToNetString(inet_ntoa(sai->sin_addr));
		HostName^ hostName = ref new HostName(ipAddress);
		String^ remoteServiceName = htons(sai->sin_port).ToString();

		Platform::Array<unsigned char>^ wbuf = ref new Platform::Array<unsigned char>((byte*)buf, len);
		Windows::Storage::Streams::DataWriter^ dw = nullptr;

		Windows::Storage::Streams::IOutputStream^ outputStream;

		try {
			// if the socket is connected, this call will ignore the input parameters
			__wse_unlock__
			outputStream = synchronize(wrtSock->GetOutputStreamAsync(hostName, remoteServiceName));
		}
		catch (COMException^ e) {
			__debug__(ToAsciiString(e->Message));

			if (e->Message == L"No such host is known.\r\n") {
				return set_wse_result(WSAEADDRNOTAVAIL, SOCKET_ERROR, sockData);
			}

			return SOCKET_ERROR; // TODO
		}

		dw = ref new Windows::Storage::Streams::DataWriter(outputStream);
		dw->WriteBytes(wbuf);

		auto t = async_to_task(dw->StoreAsync()
		).then([dw](unsigned int) {
			return async_to_task(dw->FlushAsync());
		}).then([dw](bool) {
			dw->DetachStream();
		}).then([sockData](Concurrency::task<void> previous) {
			__wse_lock__;

			try {
				previous.wait();
				return set_wse_result(0, sockData);
			}
			catch (COMException^ e) {
				__debug__(ToAsciiString(e->Message));
				return set_wse_result(SOCKET_ERROR, sockData); // TODO
			}
		});

		if (sockData->isNonBlocking) return set_wse_result(0, len, sockData);

		int result = 0;

		// don't put set_wse_result into __wse_unlock__, otherwise it's not threadsafe any more
		__wse_unlock__
		result = synchronize(t);

		return result == 0 ? set_wse_result(0, len, sockData) : set_wse_result(result, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		// this unlock is necessary since send() will have another lock which would prevent synchronize() inside send() to block
		__wse_unlock__
		return ::send(s, buf, len, flags);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
setsockopt(
    _In_ SOCKET s,
    _In_ int level,
    _In_ int optname,
    _In_reads_bytes_opt_(optlen) const char FAR * optval,
    _In_ int optlen
    )
{
	return set_wse_result(0, 0);
}

WINSOCK_API_LINKAGE
struct hostent FAR *
WSAAPI
gethostbyname(
    _In_z_ const char FAR * name
    )
{
	if (!name) return set_wse_result(WSAEFAULT, (hostent*)NULL);

	__declspec(thread) static hostent he;

	memset(&he, 0, sizeof(hostent));

	Platform::String^ remoteHostName = ToNetString(name);
	HostName^ hn = ref new HostName(remoteHostName);

	// TODO: need a revamp since it only works with udp and all the memory allocated are not released
	try {
		auto res = synchronize(Windows::Networking::Sockets::DatagramSocket::GetEndpointPairsAsync(hn, "80"));

		he.h_name = (char*)malloc(256);
		memcpy(he.h_name, name, strlen(name));
		he.h_aliases = NULL;
		he.h_addrtype = AF_INET;
		he.h_length = 16;
		he.h_addr_list = (char**)malloc(16*2);
		const std::string resultAddr = ToAsciiString(res->GetAt(0)->RemoteHostName->CanonicalName);
		unsigned int resultAddrSize = res->GetAt(0)->RemoteHostName->CanonicalName->Length();
		memcpy(he.h_addr_list[0], resultAddr.c_str(), resultAddrSize);
		he.h_addr_list[1] = NULL;

		return set_wse_result(0, &he);
	}
	catch (COMException^ e) {
		__debug__(ToAsciiString(e->Message));

		// TODO
	}

	return set_wse_result(WSAEINVAL, (hostent*)NULL); // TODO: error code
}

WINSOCK_API_LINKAGE
int
WSAAPI
gethostname(
    _Out_writes_bytes_(namelen) char FAR * name,
    _In_ int namelen
    )
{
	if (!name) set_wse_result(WSAEFAULT, SOCKET_ERROR);

   using namespace Windows::Foundation::Collections;
   using namespace Windows::Networking;
   using namespace Windows::Networking::Connectivity;
   IVectorView<HostName^>^ hostNames = Windows::Networking::Connectivity::NetworkInformation::GetHostNames();
   memcpy(name, ToAsciiString(hostNames->GetAt(0)->CanonicalName).c_str(), std::max<int>(namelen,hostNames->GetAt(0)->CanonicalName->Length()));
   return set_wse_result(0, 0);
}

WINSOCK_API_LINKAGE
int
WSAAPI
select(
	_In_ int nfds,
	_Inout_opt_ fd_set FAR * readfds,
	_Inout_opt_ fd_set FAR * writefds,
	_Inout_opt_ fd_set FAR * exceptfds,
	_In_opt_ const struct timeval FAR * timeout
	)
{
	__wse_lock__;

	struct socket_with_event_context_t {
		enum fdset_type_t { Read, Write, Except };
		SOCKET socket;
		std::shared_ptr<Concurrency::event> event;
		fdset_type_t fdset_type;
	};

	// sockets *with* events will be stored in the context
	std::vector<socket_with_event_context_t> event_contexts;

	// use std::set to get rid of redundant sockets in fd_set
	std::set<SOCKET> unique_sockets;

#pragma region setup phase

	for (u_int i = 0; readfds && i < readfds->fd_count; ++i) {
		SOCKET& socket = readfds->fd_array[i];

		if (unique_sockets.find(socket) != unique_sockets.end()) continue;

		if (auto sockData = findSocketData(socket)) {
			socket_with_event_context_t event_context = { socket, sockData->readableEvent, socket_with_event_context_t::Read };
			event_contexts.push_back(event_context);
			unique_sockets.insert(socket);
			continue;
		}

		return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
	}

	unique_sockets.clear();

	for (u_int i = 0; writefds && i < writefds->fd_count; ++i) {
		SOCKET& socket = writefds->fd_array[i];

		if (unique_sockets.find(socket) != unique_sockets.end()) continue;

		if (auto sockData = findSocketData(socket)) {
			socket_with_event_context_t event_context = { socket, sockData->writeableEvent, socket_with_event_context_t::Write };
			event_contexts.push_back(event_context);
			unique_sockets.insert(socket);

			continue;
		}

		return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
	}

	unique_sockets.clear();

	for (u_int i = 0; exceptfds && i < exceptfds->fd_count; ++i) {
		SOCKET& socket = exceptfds->fd_array[i];

		if (unique_sockets.find(socket) != unique_sockets.end()) continue;

		if (auto sockData = findSocketData(socket)) {
			socket_with_event_context_t event_context = { socket, sockData->exceptableEvent, socket_with_event_context_t::Except };
			event_contexts.push_back(event_context);
			unique_sockets.insert(socket);

			continue;
		}

		return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
	}

#pragma endregion

#pragma region wait phase

	// nothing to be selected at all
	if (event_contexts.empty()) return set_wse_result(WSAEINVAL, SOCKET_ERROR);

	u_int timeout_in_milliseconds = timeout == NULL ? COOPERATIVE_TIMEOUT_INFINITE : timeout->tv_sec * 1000 + timeout->tv_usec / 1000;

	std::vector<Concurrency::event*> waitable_events;
	for (socket_with_event_context_t& event_context : event_contexts) waitable_events.push_back(event_context.event.get());
	assert(waitable_events.size() == event_contexts.size());

	size_t index;

	__wse_unlock__
	index = waitable_events.empty() ? COOPERATIVE_WAIT_TIMEOUT : Concurrency::event::wait_for_multiple(&waitable_events[0], waitable_events.size(), false, timeout_in_milliseconds);

#pragma endregion

#pragma region result phase

	SOCKET socket_with_event = index == COOPERATIVE_WAIT_TIMEOUT ? INVALID_SOCKET : event_contexts[index].socket;

	u_int readfds_count = 0;
	u_int writefds_count = 0;
	u_int exceptfds_count = 0;
	bool has_invalid_socket = false;

	unique_sockets.clear();

	for (u_int i = 0; readfds && i < readfds->fd_count; ++i) {
		SOCKET& socket = readfds->fd_array[i];
		assert(socket != INVALID_SOCKET);
		assert(unique_sockets.find(socket) == unique_sockets.end());

		if (socket != socket_with_event || event_contexts[index].fdset_type != socket_with_event_context_t::Read) continue;

		// do not check has_invalid_socket for readfds according to winsock implementation
		readfds->fd_array[readfds_count++] = socket;
		unique_sockets.insert(socket);
	}

	if (readfds) readfds->fd_count = readfds_count;

	unique_sockets.clear();

	for (u_int i = 0; writefds && i < writefds->fd_count; ++i) {
		SOCKET& socket = writefds->fd_array[i];
		assert(socket != INVALID_SOCKET);
		assert(unique_sockets.find(socket) == unique_sockets.end());

		if (socket != socket_with_event || event_contexts[index].fdset_type != socket_with_event_context_t::Write) continue;

		if (auto sockData = findSocketData(socket)) {
			writefds->fd_array[writefds_count++] = socket;
			unique_sockets.insert(socket);

			continue;
		}

		has_invalid_socket = true;
	}

	if (writefds) writefds->fd_count = writefds_count;

	unique_sockets.clear();

	for (u_int i = 0; exceptfds && i < exceptfds->fd_count; ++i) {
		SOCKET& socket = exceptfds->fd_array[i];
		assert(socket != INVALID_SOCKET);
		assert(unique_sockets.find(socket) == unique_sockets.end());

		if (socket != socket_with_event || event_contexts[index].fdset_type != socket_with_event_context_t::Except) continue;

		if (auto sockData = findSocketData(socket)) {
			exceptfds->fd_array[exceptfds_count++] = socket;
			unique_sockets.insert(socket);

			continue;
		}

		has_invalid_socket = true;
	}

	if (exceptfds) exceptfds->fd_count = exceptfds_count;

	return has_invalid_socket && (!readfds || readfds->fd_count == 0) ? set_wse_result(WSAENOTSOCK, SOCKET_ERROR) : set_wse_result(0, readfds_count + writefds_count + exceptfds_count);

#pragma endregion

}

WINSOCK_API_LINKAGE
int
WSAAPI
getsockname(
	_In_ SOCKET s,
	_Out_writes_bytes_to_(*namelen,*namelen) struct sockaddr FAR * name,
	_Inout_ int FAR * namelen
	)
{
	auto check_args = [&]() -> int {
		if (name == NULL || namelen == NULL) return WSAEINVAL;
		if (*namelen != sizeof(sockaddr_in)) return WSAEINVAL;

		return 0;
	};

	if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

	sockaddr_in* sai = reinterpret_cast<sockaddr_in*>(name);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (!wrtSock->Information->LocalAddress || !wrtSock->Information->LocalPort) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);

		sai->sin_family = AF_INET;
		//const std::string address = ToAsciiString(wrtSock->Information->LocalAddress->CanonicalName);
		//inet_aton(address.c_str(), &sai->sin_addr);
		sai->sin_addr = sockData->bindAddress;
		const std::string port = ToAsciiString(wrtSock->Information->LocalPort);
		sai->sin_port = htons(atoi(port.c_str()));

		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (!wrtSock->Information->LocalAddress || !wrtSock->Information->LocalPort) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);

		sai->sin_family = AF_INET;
		const std::string address = ToAsciiString(wrtSock->Information->LocalAddress->CanonicalName);
		inet_aton(address.c_str(), &sai->sin_addr);
		const std::string port = ToAsciiString(wrtSock->Information->LocalPort);
		sai->sin_port = htons(atoi(port.c_str()));

		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		auto wrtSock = sockData->wrtSock;

		if (!wrtSock->Information->LocalPort) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);

		sai->sin_family = AF_INET;
		sai->sin_addr = sockData->bindAddress;
		const std::string port = ToAsciiString(wrtSock->Information->LocalPort);
		sai->sin_port = htons(atoi(port.c_str()));

		return set_wse_result(0, 0, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
getpeername(
	_In_ SOCKET s,
	_Out_writes_bytes_to_(*namelen,*namelen) struct sockaddr FAR * name,
	_Inout_ int FAR * namelen
	)
{
	auto check_args = [&]() -> int {
		if (name == NULL || namelen == NULL) return WSAEINVAL;
		if (*namelen != sizeof(sockaddr_in)) return WSAEINVAL;

		return 0;
	};

	sockaddr_in* sai = reinterpret_cast<sockaddr_in*>(name);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto wrtSock = sockData->wrtSock;

		// TODO: need to refer to stream socket for better handling
		if (!wrtSock->Information->RemoteAddress || !wrtSock->Information->RemotePort) return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);

		sai->sin_family = AF_INET;
		const std::string address = ToAsciiString(wrtSock->Information->RemoteAddress->CanonicalName);
		inet_aton(address.c_str(), &sai->sin_addr);
		const std::string port = ToAsciiString(wrtSock->Information->RemotePort);
		sai->sin_port = htons(atoi(port.c_str()));

		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		auto wrtSock = sockData->wrtSock;

		if (!sockData->isActive) return set_wse_result(WSAENOTCONN, SOCKET_ERROR, sockData);

		if (!wrtSock->Information->LocalAddress || !wrtSock->Information->LocalPort) return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);

		sai->sin_family = AF_INET;
		const std::string address = ToAsciiString(wrtSock->Information->RemoteAddress->CanonicalName);
		inet_aton(address.c_str(), &sai->sin_addr);
		const std::string port = ToAsciiString(wrtSock->Information->RemotePort);
		sai->sin_port = htons(atoi(port.c_str()));

		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		return set_wse_result(WSAENOTCONN, SOCKET_ERROR);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
getsockopt(
	_In_ SOCKET s,
	_In_ int level,
	_In_ int optname,
	_Out_writes_bytes_(*optlen) char FAR * optval,
	_Inout_ int FAR * optlen
	)
{
	if (level != SOL_SOCKET || optname != SO_ERROR) return set_wse_result(0, 0);
	if (optval == NULL || optlen == NULL || *optlen != sizeof(int)) return set_wse_result(WSAEFAULT, SOCKET_ERROR);

	__wse_lock__;

	if (auto sockData = findSocketData(s)) {
		*optval = sockData->lastError;

		return set_wse_result(0, 0, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
int
WSAAPI
listen(
	_In_ SOCKET s,
	_In_ int backlog
	)
{
	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		return set_wse_result(WSAEOPNOTSUPP, SOCKET_ERROR, sockData);
	}

	// TODO StreamSocket not bound yet
	if (auto sockData = findStreamSocketData(s)) {
		if (sockData->isActive) return set_wse_result(WSAEISCONN, SOCKET_ERROR, sockData);

		return set_wse_result(WSAEINVAL, SOCKET_ERROR, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		if (backlog > 1) sockData->backlog = backlog;

		return set_wse_result(0, 0, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

WINSOCK_API_LINKAGE
_Must_inspect_result_
SOCKET
WSAAPI
accept(
	_In_ SOCKET s,
	_Out_writes_bytes_opt_(*addrlen) struct sockaddr FAR * addr,
	_Inout_opt_ int FAR * addrlen
	)
{
	auto check_args = [&]() -> int {
		if (addr == NULL || addrlen == NULL || *addrlen != sizeof(sockaddr_in)) return WSAEINVAL;

		return 0;
	};

	sockaddr_in* sai = reinterpret_cast<sockaddr_in*>(addr);

	__wse_lock__;

	if (auto sockData = findDatagramSocketData(s)) {
		return set_wse_result(WSAEOPNOTSUPP , INVALID_SOCKET, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		return set_wse_result(WSAEINVAL, INVALID_SOCKET, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		if (int result = check_args()) return set_wse_result(result, SOCKET_ERROR);

		while (sockData->connectionReceivedArgs.empty()) {
			if (sockData->isNonBlocking) return set_wse_result(WSAEWOULDBLOCK, INVALID_SOCKET, sockData);

			__wse_unlock__
			sockData->readableEvent->wait();

			if (!sockData->isActive) return set_wse_result(WSAEINTR, INVALID_SOCKET, sockData);
		}

		assert(!sockData->connectionReceivedArgs.empty());

		auto connection = sockData->connectionReceivedArgs.front();

		auto new_wrtSock = connection->Socket;
		SOCKET socket = WinSockEmulation::instance().new_socket();

		auto insert = WinSockEmulation::instance().stream_socket_map().insert(std::make_pair(socket, std::make_shared<WinSockEmStreamSocketData>(new_wrtSock)));
		assert(insert.second);
		auto client_sockData = insert.first->second;

		client_sockData->isActive = true;
		client_sockData->writeableEvent->set();
		WinSockEmulation::instance().stream_sockets_changed_event()->set();

		// consume arg and event after success
		sockData->connectionReceivedArgs.pop_front();
		if (sockData->connectionReceivedArgs.empty()) sockData->readableEvent->reset();

		if (::getsockname(socket, addr, addrlen)) {
			int error = ::WSAGetLastError();
			::closesocket(socket);
			return set_wse_result(error, INVALID_SOCKET, sockData); // TODO:
		}

		return set_wse_result(0, socket, sockData);
	}

	return set_wse_result(WSAENOTSOCK, INVALID_SOCKET);
}

WINSOCK_API_LINKAGE
int
WSAAPI
ioctlsocket(
    _In_ SOCKET s,
    _In_ long cmd,
    _Inout_ u_long FAR * argp
    )
{
	if (cmd != FIONBIO) return set_wse_result(WSAEOPNOTSUPP, SOCKET_ERROR);

	__wse_lock__;

	// this could be simplified with findSockData(), however, leave as it is for coming up implementations
	if (auto sockData = findDatagramSocketData(s)) {
		sockData->isNonBlocking = *argp;
		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketData(s)) {
		sockData->isNonBlocking = *argp;
		return set_wse_result(0, 0, sockData);
	}

	if (auto sockData = findStreamSocketListenerData(s)) {
		sockData->isNonBlocking = *argp;
		return set_wse_result(0, 0, sockData);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}

// TODO: implement 'how'
WINSOCK_API_LINKAGE
int
WSAAPI
shutdown(
    _In_ SOCKET s,
    _In_ int how
    )
{
	if (how < 0 || how > 2) return set_wse_result(WSAEINVAL, SOCKET_ERROR);

	__wse_lock__;

	if (auto sockData = findStreamSocketData(s)) {
		//sockData->onReadAsyncCompletedEvent->set(); // stream_sockets_changed_event will awake receiver thread
		WinSockEmulation::instance().stream_sockets_changed_event()->set();
	}

	// move the check to here so errorRaisedEvent is fired *after* all other events
	// refer to select_read_except_after_closesocket_tcp
	if (auto sockData = findSocketData(s)) {
		sockData->isActive = false;
		sockData->readableEvent->set();
		sockData->writeableEvent->set();
		sockData->exceptableEvent->set();

		if (sockData->wrtResource != nullptr) {
			__debug__("wrtSock " << sockData->wrtResource->GetHashCode() << " is released");

			delete sockData->wrtResource;
			sockData->wrtResource = nullptr;
		}

		return set_wse_result(0, 0);
	}

	return set_wse_result(WSAENOTSOCK, SOCKET_ERROR);
}