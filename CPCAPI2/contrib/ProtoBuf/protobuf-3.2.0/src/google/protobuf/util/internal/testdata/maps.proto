// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package google.protobuf.testing;

// Top-level test cases proto used by MarshallingTest. See description
// at the top of the class MarshallingTest for details on how to write
// test cases.
message MapsTestCases {
  EmptyMap empty_map = 1;
  StringtoInt string_to_int = 2;
  IntToString int_to_string = 3;
  Mixed1 mixed1 = 4;
  Mixed2 mixed2 = 5;
  MapOfObjects map_of_objects = 6;

  // Empty key tests
  StringtoInt empty_key_string_to_int1 = 7;
  StringtoInt empty_key_string_to_int2 = 8;
  StringtoInt empty_key_string_to_int3 = 9;
  BoolToString empty_key_bool_to_string = 10;
  IntToString empty_key_int_to_string = 11;
  Mixed1 empty_key_mixed = 12;
  MapOfObjects empty_key_map_objects = 13;
}

message EmptyMap {
  map<int32, int32> map = 1;
}

message StringtoInt {
  map<string, int32> map = 1;
}

message IntToString {
  map<int32, string> map = 1;
}

message BoolToString {
  map<bool, string> map = 1;
}

message Mixed1 {
  string msg = 1;
  map<string, float> map = 2;
}

message Mixed2 {
  enum E {
    E0 = 0;
    E1 = 1;
    E2 = 2;
    E3 = 3;
  }
  map<int32, bool> map = 1;
  E ee = 2;
}

message MapOfObjects {
  message M {
    string inner_text = 1;
  }
  map<string, M> map = 1;
}

message DummyRequest {
}

service MapsTestService {
  rpc Call(DummyRequest) returns (MapsTestCases);
}

message MapIn {
  string other = 1;
  repeated string things = 2;
  map<string, string> map_input = 3;
}

message MapOut {
  map<string, MapM> map1 = 1;
  map<string, MapOut> map2 = 2;
  map<int32, string> map3 = 3;
  map<bool, string> map4 = 5;
  string bar = 4;
}

// A message with exactly the same wire representation as MapOut, but using
// repeated message fields instead of map fields. We use this message to test
// the wire-format compatibility of the JSON transcoder (e.g., whether it
// handles missing keys correctly).
message MapOutWireFormat {
  message Map1Entry {
    string key = 1;
    MapM value = 2;
  }
  repeated Map1Entry map1 = 1;
  message Map2Entry {
    string key = 1;
    MapOut value = 2;
  }
  repeated Map2Entry map2 = 2;
  message Map3Entry {
    int32 key = 1;
    string value = 2;
  }
  repeated Map3Entry map3 = 3;
  message Map4Entry {
    bool key = 1;
    string value = 2;
  }
  repeated Map4Entry map4 = 5;
  string bar = 4;
}

message MapM {
  string foo = 1;
}
