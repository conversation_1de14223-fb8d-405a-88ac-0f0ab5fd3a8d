// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package google.protobuf.testing;

import "google/protobuf/any.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/wrappers.proto";

// Top-level test cases proto used by MarshallingTest. See description
// at the top of the class MarshallingTest for details on how to write
// test cases.
message AnyTestCases {
  AnyWrapper empty_any = 1;
  AnyWrapper type_only_any = 2;
  AnyWrapper wrapper_any = 3;
  AnyWrapper any_with_timestamp_value = 4;
  AnyWrapper any_with_duration_value = 5;
  AnyWrapper any_with_struct_value = 6;
  AnyWrapper recursive_any = 7;
  AnyWrapper any_with_message_value = 8;
  AnyWrapper any_with_nested_message = 9;
  AnyWrapper any_with_message_with_wrapper_type = 10;
  AnyWrapper any_with_message_with_timestamp = 11;
  AnyWrapper any_with_message_containing_map = 12;
  AnyWrapper any_with_message_containing_struct = 13;
  AnyWrapper any_with_message_containing_repeated_message = 14;
  AnyWrapper recursive_any_with_type_field_at_end = 15;

  google.protobuf.Any top_level_any = 50;
  google.protobuf.Any top_level_any_with_type_field_at_end = 51;
}

message AnyWrapper {
  google.protobuf.Any any = 1;
}

// Hack to make sure the types we put into the any are included in the types.
// Real solution is to add these types to the service config.
message Imports {
  google.protobuf.DoubleValue dbl = 1;
  google.protobuf.Struct struct = 2;
  google.protobuf.Timestamp timestamp = 3;
  google.protobuf.Duration duration = 4;
  google.protobuf.Int32Value i32 = 5;
  Data data = 100;
}

message Data {
  int32 attr = 1;
  string str = 2;
  repeated string msgs = 3;
  Data nested_data = 4;
  google.protobuf.Int32Value int_wrapper = 5;
  google.protobuf.Timestamp time = 6;
  map<string, string> map_data = 7;
  google.protobuf.Struct struct_data = 8;
  repeated Data repeated_data = 9;
}

service AnyTestService {
  rpc Call(AnyTestCases) returns (AnyTestCases);
  rpc Call1(Imports) returns (Imports);
}

message AnyIn {
  string something = 1;
  google.protobuf.Any any = 2;
}

message AnyOut {
  google.protobuf.Any any = 1;
}

message AnyM {
  string foo = 1;
}
