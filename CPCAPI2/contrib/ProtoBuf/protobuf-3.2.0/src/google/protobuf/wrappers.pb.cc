// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google/protobuf/wrappers.proto

#define INTERNAL_SUPPRESS_PROTOBUF_FIELD_DEPRECATION
#include <google/protobuf/wrappers.pb.h>

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)

namespace google {
namespace protobuf {
class DoubleValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<DoubleValue> {
} _DoubleValue_default_instance_;
class FloatValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<FloatValue> {
} _FloatValue_default_instance_;
class Int64ValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<Int64Value> {
} _Int64Value_default_instance_;
class UInt64ValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UInt64Value> {
} _UInt64Value_default_instance_;
class Int32ValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<Int32Value> {
} _Int32Value_default_instance_;
class UInt32ValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<UInt32Value> {
} _UInt32Value_default_instance_;
class BoolValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BoolValue> {
} _BoolValue_default_instance_;
class StringValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<StringValue> {
} _StringValue_default_instance_;
class BytesValueDefaultTypeInternal : public ::google::protobuf::internal::ExplicitlyConstructed<BytesValue> {
} _BytesValue_default_instance_;

namespace protobuf_google_2fprotobuf_2fwrappers_2eproto {


namespace {

::google::protobuf::Metadata file_level_metadata[9];

}  // namespace

const ::google::protobuf::uint32 TableStruct::offsets[] = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(DoubleValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FloatValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(FloatValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int64Value, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int64Value, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt64Value, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt64Value, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int32Value, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(Int32Value, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt32Value, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(UInt32Value, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BoolValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(StringValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BytesValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(BytesValue, value_),
};

static const ::google::protobuf::internal::MigrationSchema schemas[] = {
  { 0, -1, sizeof(DoubleValue)},
  { 5, -1, sizeof(FloatValue)},
  { 10, -1, sizeof(Int64Value)},
  { 15, -1, sizeof(UInt64Value)},
  { 20, -1, sizeof(Int32Value)},
  { 25, -1, sizeof(UInt32Value)},
  { 30, -1, sizeof(BoolValue)},
  { 35, -1, sizeof(StringValue)},
  { 40, -1, sizeof(BytesValue)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&_DoubleValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_FloatValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_Int64Value_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UInt64Value_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_Int32Value_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_UInt32Value_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BoolValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_StringValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&_BytesValue_default_instance_),
};

namespace {

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "google/protobuf/wrappers.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 9);
}

}  // namespace

void TableStruct::Shutdown() {
  _DoubleValue_default_instance_.Shutdown();
  delete file_level_metadata[0].reflection;
  _FloatValue_default_instance_.Shutdown();
  delete file_level_metadata[1].reflection;
  _Int64Value_default_instance_.Shutdown();
  delete file_level_metadata[2].reflection;
  _UInt64Value_default_instance_.Shutdown();
  delete file_level_metadata[3].reflection;
  _Int32Value_default_instance_.Shutdown();
  delete file_level_metadata[4].reflection;
  _UInt32Value_default_instance_.Shutdown();
  delete file_level_metadata[5].reflection;
  _BoolValue_default_instance_.Shutdown();
  delete file_level_metadata[6].reflection;
  _StringValue_default_instance_.Shutdown();
  delete file_level_metadata[7].reflection;
  _BytesValue_default_instance_.Shutdown();
  delete file_level_metadata[8].reflection;
}

void TableStruct::InitDefaultsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  ::google::protobuf::internal::InitProtobufDefaults();
  _DoubleValue_default_instance_.DefaultConstruct();
  _FloatValue_default_instance_.DefaultConstruct();
  _Int64Value_default_instance_.DefaultConstruct();
  _UInt64Value_default_instance_.DefaultConstruct();
  _Int32Value_default_instance_.DefaultConstruct();
  _UInt32Value_default_instance_.DefaultConstruct();
  _BoolValue_default_instance_.DefaultConstruct();
  _StringValue_default_instance_.DefaultConstruct();
  _BytesValue_default_instance_.DefaultConstruct();
}

void InitDefaults() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &TableStruct::InitDefaultsImpl);
}
void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] = {
      "\n\036google/protobuf/wrappers.proto\022\017google"
      ".protobuf\"\034\n\013DoubleValue\022\r\n\005value\030\001 \001(\001\""
      "\033\n\nFloatValue\022\r\n\005value\030\001 \001(\002\"\033\n\nInt64Val"
      "ue\022\r\n\005value\030\001 \001(\003\"\034\n\013UInt64Value\022\r\n\005valu"
      "e\030\001 \001(\004\"\033\n\nInt32Value\022\r\n\005value\030\001 \001(\005\"\034\n\013"
      "UInt32Value\022\r\n\005value\030\001 \001(\r\"\032\n\tBoolValue\022"
      "\r\n\005value\030\001 \001(\010\"\034\n\013StringValue\022\r\n\005value\030\001"
      " \001(\t\"\033\n\nBytesValue\022\r\n\005value\030\001 \001(\014B|\n\023com"
      ".google.protobufB\rWrappersProtoP\001Z*githu"
      "b.com/golang/protobuf/ptypes/wrappers\370\001\001"
      "\242\002\003GPB\252\002\036Google.Protobuf.WellKnownTypesb"
      "\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 447);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "google/protobuf/wrappers.proto", &protobuf_RegisterTypes);
  ::google::protobuf::internal::OnShutdown(&TableStruct::Shutdown);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at static initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;

}  // namespace protobuf_google_2fprotobuf_2fwrappers_2eproto


// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DoubleValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DoubleValue::DoubleValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.DoubleValue)
}
DoubleValue::DoubleValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.DoubleValue)
}
DoubleValue::DoubleValue(const DoubleValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.DoubleValue)
}

void DoubleValue::SharedCtor() {
  value_ = 0;
  _cached_size_ = 0;
}

DoubleValue::~DoubleValue() {
  // @@protoc_insertion_point(destructor:google.protobuf.DoubleValue)
  SharedDtor();
}

void DoubleValue::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void DoubleValue::ArenaDtor(void* object) {
  DoubleValue* _this = reinterpret_cast< DoubleValue* >(object);
  (void)_this;
}
void DoubleValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void DoubleValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DoubleValue::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[0].descriptor;
}

const DoubleValue& DoubleValue::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

DoubleValue* DoubleValue::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<DoubleValue>(arena);
}

void DoubleValue::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.DoubleValue)
  value_ = 0;
}

bool DoubleValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.DoubleValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double value = 1;
      case 1: {
        if (tag == 9u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.DoubleValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.DoubleValue)
  return false;
#undef DO_
}

void DoubleValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.DoubleValue)
  // double value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.DoubleValue)
}

::google::protobuf::uint8* DoubleValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.DoubleValue)
  // double value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.DoubleValue)
  return target;
}

size_t DoubleValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.DoubleValue)
  size_t total_size = 0;

  // double value = 1;
  if (this->value() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DoubleValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.DoubleValue)
  GOOGLE_DCHECK_NE(&from, this);
  const DoubleValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DoubleValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.DoubleValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.DoubleValue)
    MergeFrom(*source);
  }
}

void DoubleValue::MergeFrom(const DoubleValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.DoubleValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void DoubleValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.DoubleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DoubleValue::CopyFrom(const DoubleValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.DoubleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoubleValue::IsInitialized() const {
  return true;
}

void DoubleValue::Swap(DoubleValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    DoubleValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void DoubleValue::UnsafeArenaSwap(DoubleValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void DoubleValue::InternalSwap(DoubleValue* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DoubleValue::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[0];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// DoubleValue

// double value = 1;
void DoubleValue::clear_value() {
  value_ = 0;
}
double DoubleValue::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.DoubleValue.value)
  return value_;
}
void DoubleValue::set_value(double value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.DoubleValue.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FloatValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FloatValue::FloatValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.FloatValue)
}
FloatValue::FloatValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.FloatValue)
}
FloatValue::FloatValue(const FloatValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.FloatValue)
}

void FloatValue::SharedCtor() {
  value_ = 0;
  _cached_size_ = 0;
}

FloatValue::~FloatValue() {
  // @@protoc_insertion_point(destructor:google.protobuf.FloatValue)
  SharedDtor();
}

void FloatValue::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void FloatValue::ArenaDtor(void* object) {
  FloatValue* _this = reinterpret_cast< FloatValue* >(object);
  (void)_this;
}
void FloatValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void FloatValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FloatValue::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[1].descriptor;
}

const FloatValue& FloatValue::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

FloatValue* FloatValue::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<FloatValue>(arena);
}

void FloatValue::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.FloatValue)
  value_ = 0;
}

bool FloatValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.FloatValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // float value = 1;
      case 1: {
        if (tag == 13u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   float, ::google::protobuf::internal::WireFormatLite::TYPE_FLOAT>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.FloatValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.FloatValue)
  return false;
#undef DO_
}

void FloatValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.FloatValue)
  // float value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteFloat(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.FloatValue)
}

::google::protobuf::uint8* FloatValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.FloatValue)
  // float value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteFloatToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.FloatValue)
  return target;
}

size_t FloatValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.FloatValue)
  size_t total_size = 0;

  // float value = 1;
  if (this->value() != 0) {
    total_size += 1 + 4;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FloatValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.FloatValue)
  GOOGLE_DCHECK_NE(&from, this);
  const FloatValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FloatValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.FloatValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.FloatValue)
    MergeFrom(*source);
  }
}

void FloatValue::MergeFrom(const FloatValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.FloatValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void FloatValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.FloatValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FloatValue::CopyFrom(const FloatValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.FloatValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FloatValue::IsInitialized() const {
  return true;
}

void FloatValue::Swap(FloatValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    FloatValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void FloatValue::UnsafeArenaSwap(FloatValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void FloatValue::InternalSwap(FloatValue* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FloatValue::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[1];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// FloatValue

// float value = 1;
void FloatValue::clear_value() {
  value_ = 0;
}
float FloatValue::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.FloatValue.value)
  return value_;
}
void FloatValue::set_value(float value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.FloatValue.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Int64Value::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Int64Value::Int64Value()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.Int64Value)
}
Int64Value::Int64Value(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.Int64Value)
}
Int64Value::Int64Value(const Int64Value& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.Int64Value)
}

void Int64Value::SharedCtor() {
  value_ = GOOGLE_LONGLONG(0);
  _cached_size_ = 0;
}

Int64Value::~Int64Value() {
  // @@protoc_insertion_point(destructor:google.protobuf.Int64Value)
  SharedDtor();
}

void Int64Value::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void Int64Value::ArenaDtor(void* object) {
  Int64Value* _this = reinterpret_cast< Int64Value* >(object);
  (void)_this;
}
void Int64Value::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Int64Value::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Int64Value::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[2].descriptor;
}

const Int64Value& Int64Value::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

Int64Value* Int64Value::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<Int64Value>(arena);
}

void Int64Value::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.Int64Value)
  value_ = GOOGLE_LONGLONG(0);
}

bool Int64Value::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.Int64Value)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 value = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.Int64Value)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.Int64Value)
  return false;
#undef DO_
}

void Int64Value::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.Int64Value)
  // int64 value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.Int64Value)
}

::google::protobuf::uint8* Int64Value::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.Int64Value)
  // int64 value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.Int64Value)
  return target;
}

size_t Int64Value::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.Int64Value)
  size_t total_size = 0;

  // int64 value = 1;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Int64Value::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.Int64Value)
  GOOGLE_DCHECK_NE(&from, this);
  const Int64Value* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Int64Value>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.Int64Value)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.Int64Value)
    MergeFrom(*source);
  }
}

void Int64Value::MergeFrom(const Int64Value& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.Int64Value)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void Int64Value::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.Int64Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Int64Value::CopyFrom(const Int64Value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.Int64Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Int64Value::IsInitialized() const {
  return true;
}

void Int64Value::Swap(Int64Value* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Int64Value* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Int64Value::UnsafeArenaSwap(Int64Value* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Int64Value::InternalSwap(Int64Value* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Int64Value::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[2];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Int64Value

// int64 value = 1;
void Int64Value::clear_value() {
  value_ = GOOGLE_LONGLONG(0);
}
::google::protobuf::int64 Int64Value::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.Int64Value.value)
  return value_;
}
void Int64Value::set_value(::google::protobuf::int64 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.Int64Value.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UInt64Value::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UInt64Value::UInt64Value()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.UInt64Value)
}
UInt64Value::UInt64Value(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.UInt64Value)
}
UInt64Value::UInt64Value(const UInt64Value& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.UInt64Value)
}

void UInt64Value::SharedCtor() {
  value_ = GOOGLE_ULONGLONG(0);
  _cached_size_ = 0;
}

UInt64Value::~UInt64Value() {
  // @@protoc_insertion_point(destructor:google.protobuf.UInt64Value)
  SharedDtor();
}

void UInt64Value::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void UInt64Value::ArenaDtor(void* object) {
  UInt64Value* _this = reinterpret_cast< UInt64Value* >(object);
  (void)_this;
}
void UInt64Value::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void UInt64Value::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UInt64Value::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[3].descriptor;
}

const UInt64Value& UInt64Value::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

UInt64Value* UInt64Value::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<UInt64Value>(arena);
}

void UInt64Value::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.UInt64Value)
  value_ = GOOGLE_ULONGLONG(0);
}

bool UInt64Value::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.UInt64Value)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint64 value = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.UInt64Value)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.UInt64Value)
  return false;
#undef DO_
}

void UInt64Value::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.UInt64Value)
  // uint64 value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.UInt64Value)
}

::google::protobuf::uint8* UInt64Value::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.UInt64Value)
  // uint64 value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.UInt64Value)
  return target;
}

size_t UInt64Value::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.UInt64Value)
  size_t total_size = 0;

  // uint64 value = 1;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UInt64Value::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.UInt64Value)
  GOOGLE_DCHECK_NE(&from, this);
  const UInt64Value* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UInt64Value>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.UInt64Value)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.UInt64Value)
    MergeFrom(*source);
  }
}

void UInt64Value::MergeFrom(const UInt64Value& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.UInt64Value)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void UInt64Value::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.UInt64Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UInt64Value::CopyFrom(const UInt64Value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.UInt64Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UInt64Value::IsInitialized() const {
  return true;
}

void UInt64Value::Swap(UInt64Value* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UInt64Value* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void UInt64Value::UnsafeArenaSwap(UInt64Value* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UInt64Value::InternalSwap(UInt64Value* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UInt64Value::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[3];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UInt64Value

// uint64 value = 1;
void UInt64Value::clear_value() {
  value_ = GOOGLE_ULONGLONG(0);
}
::google::protobuf::uint64 UInt64Value::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.UInt64Value.value)
  return value_;
}
void UInt64Value::set_value(::google::protobuf::uint64 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.UInt64Value.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Int32Value::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Int32Value::Int32Value()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.Int32Value)
}
Int32Value::Int32Value(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.Int32Value)
}
Int32Value::Int32Value(const Int32Value& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.Int32Value)
}

void Int32Value::SharedCtor() {
  value_ = 0;
  _cached_size_ = 0;
}

Int32Value::~Int32Value() {
  // @@protoc_insertion_point(destructor:google.protobuf.Int32Value)
  SharedDtor();
}

void Int32Value::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void Int32Value::ArenaDtor(void* object) {
  Int32Value* _this = reinterpret_cast< Int32Value* >(object);
  (void)_this;
}
void Int32Value::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void Int32Value::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Int32Value::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[4].descriptor;
}

const Int32Value& Int32Value::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

Int32Value* Int32Value::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<Int32Value>(arena);
}

void Int32Value::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.Int32Value)
  value_ = 0;
}

bool Int32Value::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.Int32Value)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 value = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.Int32Value)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.Int32Value)
  return false;
#undef DO_
}

void Int32Value::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.Int32Value)
  // int32 value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.Int32Value)
}

::google::protobuf::uint8* Int32Value::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.Int32Value)
  // int32 value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.Int32Value)
  return target;
}

size_t Int32Value::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.Int32Value)
  size_t total_size = 0;

  // int32 value = 1;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Int32Value::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.Int32Value)
  GOOGLE_DCHECK_NE(&from, this);
  const Int32Value* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Int32Value>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.Int32Value)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.Int32Value)
    MergeFrom(*source);
  }
}

void Int32Value::MergeFrom(const Int32Value& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.Int32Value)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void Int32Value::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.Int32Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Int32Value::CopyFrom(const Int32Value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.Int32Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Int32Value::IsInitialized() const {
  return true;
}

void Int32Value::Swap(Int32Value* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    Int32Value* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void Int32Value::UnsafeArenaSwap(Int32Value* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void Int32Value::InternalSwap(Int32Value* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Int32Value::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[4];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// Int32Value

// int32 value = 1;
void Int32Value::clear_value() {
  value_ = 0;
}
::google::protobuf::int32 Int32Value::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.Int32Value.value)
  return value_;
}
void Int32Value::set_value(::google::protobuf::int32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.Int32Value.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int UInt32Value::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

UInt32Value::UInt32Value()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.UInt32Value)
}
UInt32Value::UInt32Value(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.UInt32Value)
}
UInt32Value::UInt32Value(const UInt32Value& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.UInt32Value)
}

void UInt32Value::SharedCtor() {
  value_ = 0u;
  _cached_size_ = 0;
}

UInt32Value::~UInt32Value() {
  // @@protoc_insertion_point(destructor:google.protobuf.UInt32Value)
  SharedDtor();
}

void UInt32Value::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void UInt32Value::ArenaDtor(void* object) {
  UInt32Value* _this = reinterpret_cast< UInt32Value* >(object);
  (void)_this;
}
void UInt32Value::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void UInt32Value::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* UInt32Value::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[5].descriptor;
}

const UInt32Value& UInt32Value::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

UInt32Value* UInt32Value::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<UInt32Value>(arena);
}

void UInt32Value::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.UInt32Value)
  value_ = 0u;
}

bool UInt32Value::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.UInt32Value)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 value = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.UInt32Value)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.UInt32Value)
  return false;
#undef DO_
}

void UInt32Value::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.UInt32Value)
  // uint32 value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.UInt32Value)
}

::google::protobuf::uint8* UInt32Value::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.UInt32Value)
  // uint32 value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.UInt32Value)
  return target;
}

size_t UInt32Value::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.UInt32Value)
  size_t total_size = 0;

  // uint32 value = 1;
  if (this->value() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void UInt32Value::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.UInt32Value)
  GOOGLE_DCHECK_NE(&from, this);
  const UInt32Value* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const UInt32Value>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.UInt32Value)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.UInt32Value)
    MergeFrom(*source);
  }
}

void UInt32Value::MergeFrom(const UInt32Value& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.UInt32Value)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void UInt32Value::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.UInt32Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UInt32Value::CopyFrom(const UInt32Value& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.UInt32Value)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UInt32Value::IsInitialized() const {
  return true;
}

void UInt32Value::Swap(UInt32Value* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    UInt32Value* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void UInt32Value::UnsafeArenaSwap(UInt32Value* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void UInt32Value::InternalSwap(UInt32Value* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata UInt32Value::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[5];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// UInt32Value

// uint32 value = 1;
void UInt32Value::clear_value() {
  value_ = 0u;
}
::google::protobuf::uint32 UInt32Value::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.UInt32Value.value)
  return value_;
}
void UInt32Value::set_value(::google::protobuf::uint32 value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.UInt32Value.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BoolValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BoolValue::BoolValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.BoolValue)
}
BoolValue::BoolValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.BoolValue)
}
BoolValue::BoolValue(const BoolValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:google.protobuf.BoolValue)
}

void BoolValue::SharedCtor() {
  value_ = false;
  _cached_size_ = 0;
}

BoolValue::~BoolValue() {
  // @@protoc_insertion_point(destructor:google.protobuf.BoolValue)
  SharedDtor();
}

void BoolValue::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

}

void BoolValue::ArenaDtor(void* object) {
  BoolValue* _this = reinterpret_cast< BoolValue* >(object);
  (void)_this;
}
void BoolValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BoolValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BoolValue::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[6].descriptor;
}

const BoolValue& BoolValue::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

BoolValue* BoolValue::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<BoolValue>(arena);
}

void BoolValue::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.BoolValue)
  value_ = false;
}

bool BoolValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.BoolValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool value = 1;
      case 1: {
        if (tag == 8u) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.BoolValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.BoolValue)
  return false;
#undef DO_
}

void BoolValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.BoolValue)
  // bool value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.BoolValue)
}

::google::protobuf::uint8* BoolValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.BoolValue)
  // bool value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.BoolValue)
  return target;
}

size_t BoolValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.BoolValue)
  size_t total_size = 0;

  // bool value = 1;
  if (this->value() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BoolValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.BoolValue)
  GOOGLE_DCHECK_NE(&from, this);
  const BoolValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BoolValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.BoolValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.BoolValue)
    MergeFrom(*source);
  }
}

void BoolValue::MergeFrom(const BoolValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.BoolValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value() != 0) {
    set_value(from.value());
  }
}

void BoolValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.BoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BoolValue::CopyFrom(const BoolValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.BoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BoolValue::IsInitialized() const {
  return true;
}

void BoolValue::Swap(BoolValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BoolValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BoolValue::UnsafeArenaSwap(BoolValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BoolValue::InternalSwap(BoolValue* other) {
  std::swap(value_, other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BoolValue::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[6];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BoolValue

// bool value = 1;
void BoolValue::clear_value() {
  value_ = false;
}
bool BoolValue::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.BoolValue.value)
  return value_;
}
void BoolValue::set_value(bool value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:google.protobuf.BoolValue.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StringValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StringValue::StringValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.StringValue)
}
StringValue::StringValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.StringValue)
}
StringValue::StringValue(const StringValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.value().size() > 0) {
    value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:google.protobuf.StringValue)
}

void StringValue::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

StringValue::~StringValue() {
  // @@protoc_insertion_point(destructor:google.protobuf.StringValue)
  SharedDtor();
}

void StringValue::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  value_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
}

void StringValue::ArenaDtor(void* object) {
  StringValue* _this = reinterpret_cast< StringValue* >(object);
  (void)_this;
}
void StringValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void StringValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StringValue::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[7].descriptor;
}

const StringValue& StringValue::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

StringValue* StringValue::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<StringValue>(arena);
}

void StringValue::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.StringValue)
  value_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}

bool StringValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.StringValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string value = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->value().data(), this->value().length(),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "google.protobuf.StringValue.value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.StringValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.StringValue)
  return false;
#undef DO_
}

void StringValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.StringValue)
  // string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.StringValue.value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.StringValue)
}

::google::protobuf::uint8* StringValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.StringValue)
  // string value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->value().data(), this->value().length(),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "google.protobuf.StringValue.value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.StringValue)
  return target;
}

size_t StringValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.StringValue)
  size_t total_size = 0;

  // string value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StringValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.StringValue)
  GOOGLE_DCHECK_NE(&from, this);
  const StringValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StringValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.StringValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.StringValue)
    MergeFrom(*source);
  }
}

void StringValue::MergeFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.StringValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value().size() > 0) {
    set_value(from.value());
  }
}

void StringValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.StringValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringValue::CopyFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.StringValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringValue::IsInitialized() const {
  return true;
}

void StringValue::Swap(StringValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    StringValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void StringValue::UnsafeArenaSwap(StringValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void StringValue::InternalSwap(StringValue* other) {
  value_.Swap(&other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StringValue::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[7];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// StringValue

// string value = 1;
void StringValue::clear_value() {
  value_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& StringValue::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.StringValue.value)
  return value_.Get();
}
void StringValue::set_value(const ::std::string& value) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:google.protobuf.StringValue.value)
}
void StringValue::set_value(const char* value) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:google.protobuf.StringValue.value)
}
void StringValue::set_value(const char* value,
    size_t size) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.StringValue.value)
}
::std::string* StringValue::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.StringValue.value)
  return value_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* StringValue::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.StringValue.value)
  
  return value_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* StringValue::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:google.protobuf.StringValue.value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return value_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void StringValue::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.StringValue.value)
}
void StringValue::unsafe_arena_set_allocated_value(
    ::std::string* value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (value != NULL) {
    
  } else {
    
  }
  value_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:google.protobuf.StringValue.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// ===================================================================

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BytesValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BytesValue::BytesValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:google.protobuf.BytesValue)
}
BytesValue::BytesValue(::google::protobuf::Arena* arena)
  : ::google::protobuf::Message(),
  _internal_metadata_(arena) {
#ifdef GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
#endif  // GOOGLE_PROTOBUF_NO_STATIC_INITIALIZER
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:google.protobuf.BytesValue)
}
BytesValue::BytesValue(const BytesValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.value().size() > 0) {
    value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.value(),
      GetArenaNoVirtual());
  }
  // @@protoc_insertion_point(copy_constructor:google.protobuf.BytesValue)
}

void BytesValue::SharedCtor() {
  value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

BytesValue::~BytesValue() {
  // @@protoc_insertion_point(destructor:google.protobuf.BytesValue)
  SharedDtor();
}

void BytesValue::SharedDtor() {
  ::google::protobuf::Arena* arena = GetArenaNoVirtual();
  if (arena != NULL) {
    return;
  }

  value_.Destroy(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), arena);
}

void BytesValue::ArenaDtor(void* object) {
  BytesValue* _this = reinterpret_cast< BytesValue* >(object);
  (void)_this;
}
void BytesValue::RegisterArenaDtor(::google::protobuf::Arena* arena) {
}
void BytesValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BytesValue::descriptor() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[8].descriptor;
}

const BytesValue& BytesValue::default_instance() {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::InitDefaults();
  return *internal_default_instance();
}

BytesValue* BytesValue::New(::google::protobuf::Arena* arena) const {
  return ::google::protobuf::Arena::CreateMessage<BytesValue>(arena);
}

void BytesValue::Clear() {
// @@protoc_insertion_point(message_clear_start:google.protobuf.BytesValue)
  value_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}

bool BytesValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:google.protobuf.BytesValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes value = 1;
      case 1: {
        if (tag == 10u) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_value()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0 ||
            ::google::protobuf::internal::WireFormatLite::GetTagWireType(tag) ==
            ::google::protobuf::internal::WireFormatLite::WIRETYPE_END_GROUP) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormatLite::SkipField(input, tag));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:google.protobuf.BytesValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:google.protobuf.BytesValue)
  return false;
#undef DO_
}

void BytesValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:google.protobuf.BytesValue)
  // bytes value = 1;
  if (this->value().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->value(), output);
  }

  // @@protoc_insertion_point(serialize_end:google.protobuf.BytesValue)
}

::google::protobuf::uint8* BytesValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic;  // Unused
  // @@protoc_insertion_point(serialize_to_array_start:google.protobuf.BytesValue)
  // bytes value = 1;
  if (this->value().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->value(), target);
  }

  // @@protoc_insertion_point(serialize_to_array_end:google.protobuf.BytesValue)
  return target;
}

size_t BytesValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:google.protobuf.BytesValue)
  size_t total_size = 0;

  // bytes value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->value());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BytesValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:google.protobuf.BytesValue)
  GOOGLE_DCHECK_NE(&from, this);
  const BytesValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BytesValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:google.protobuf.BytesValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:google.protobuf.BytesValue)
    MergeFrom(*source);
  }
}

void BytesValue::MergeFrom(const BytesValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:google.protobuf.BytesValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.value().size() > 0) {
    set_value(from.value());
  }
}

void BytesValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:google.protobuf.BytesValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BytesValue::CopyFrom(const BytesValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:google.protobuf.BytesValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BytesValue::IsInitialized() const {
  return true;
}

void BytesValue::Swap(BytesValue* other) {
  if (other == this) return;
  if (GetArenaNoVirtual() == other->GetArenaNoVirtual()) {
    InternalSwap(other);
  } else {
    BytesValue* temp = New(GetArenaNoVirtual());
    temp->MergeFrom(*other);
    other->CopyFrom(*this);
    InternalSwap(temp);
    if (GetArenaNoVirtual() == NULL) {
      delete temp;
    }
  }
}
void BytesValue::UnsafeArenaSwap(BytesValue* other) {
  if (other == this) return;
  GOOGLE_DCHECK(GetArenaNoVirtual() == other->GetArenaNoVirtual());
  InternalSwap(other);
}
void BytesValue::InternalSwap(BytesValue* other) {
  value_.Swap(&other->value_);
  std::swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BytesValue::GetMetadata() const {
  protobuf_google_2fprotobuf_2fwrappers_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_google_2fprotobuf_2fwrappers_2eproto::file_level_metadata[8];
}

#if PROTOBUF_INLINE_NOT_IN_HEADERS
// BytesValue

// bytes value = 1;
void BytesValue::clear_value() {
  value_.ClearToEmpty(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
const ::std::string& BytesValue::value() const {
  // @@protoc_insertion_point(field_get:google.protobuf.BytesValue.value)
  return value_.Get();
}
void BytesValue::set_value(const ::std::string& value) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set:google.protobuf.BytesValue.value)
}
void BytesValue::set_value(const char* value) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value),
              GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_char:google.protobuf.BytesValue.value)
}
void BytesValue::set_value(const void* value,
    size_t size) {
  
  value_.Set(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size), GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_pointer:google.protobuf.BytesValue.value)
}
::std::string* BytesValue::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:google.protobuf.BytesValue.value)
  return value_.Mutable(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* BytesValue::release_value() {
  // @@protoc_insertion_point(field_release:google.protobuf.BytesValue.value)
  
  return value_.Release(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), GetArenaNoVirtual());
}
::std::string* BytesValue::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:google.protobuf.BytesValue.value)
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  
  return value_.UnsafeArenaRelease(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      GetArenaNoVirtual());
}
void BytesValue::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value,
      GetArenaNoVirtual());
  // @@protoc_insertion_point(field_set_allocated:google.protobuf.BytesValue.value)
}
void BytesValue::unsafe_arena_set_allocated_value(
    ::std::string* value) {
  GOOGLE_DCHECK(GetArenaNoVirtual() != NULL);
  if (value != NULL) {
    
  } else {
    
  }
  value_.UnsafeArenaSetAllocated(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      value, GetArenaNoVirtual());
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:google.protobuf.BytesValue.value)
}

#endif  // PROTOBUF_INLINE_NOT_IN_HEADERS

// @@protoc_insertion_point(namespace_scope)

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)
