﻿<?xml version="1.0" encoding="UTF-8"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="libprotoc"
	ProjectGUID="{B84FF31A-5F9A-46F8-AB22-DBFC9BECE3BE}"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				Optimization="0"
				AdditionalIncludeDirectories="../src;."
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;LIBPROTOC_EXPORTS;"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				AdditionalIncludeDirectories="../src;."
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;LIBPROTOC_EXPORTS;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="..\src\google\protobuf\compiler\code_generator.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\command_line_interface.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\subprocess.h"
				>
			</File>
      <File
        RelativePath="..\src\google\protobuf\compiler\zip_writer.h"
        >
      </File>
			<File
				RelativePath="..\src\google\protobuf\compiler\plugin.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\plugin.pb.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_enum.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_enum_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_extension.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_file.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_generator.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_helpers.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_message.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_message_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_options.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_primitive_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_service.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_string_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_enum.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_enum_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_extension.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_file.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_generator.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_helpers.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_message.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_message_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_primitive_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_service.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_string_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_doc_comment.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_doc_comment.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\python\python_generator.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\src\google\protobuf\compiler\code_generator.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\command_line_interface.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\subprocess.cc"
				>
			</File>
      <File
        RelativePath="..\src\google\protobuf\compiler\zip_writer.cc"
        >
      </File>
			<File
				RelativePath="..\src\google\protobuf\compiler\plugin.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\plugin.pb.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_enum.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_enum_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_extension.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_file.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_generator.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_helpers.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_message.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_message_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_primitive_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_service.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_string_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_enum.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_enum_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_extension.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_file.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_generator.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_helpers.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_message.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_message_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_primitive_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_service.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_string_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\python\python_generator.cc"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
