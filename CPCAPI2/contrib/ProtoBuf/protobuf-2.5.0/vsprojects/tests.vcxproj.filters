﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\testing\file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\testing\googletest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\test_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\mock_code_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_custom_options.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_embed_optimize_for.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_import.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_import_public.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_lite_imports_nonline.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_mset.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_optimize_for.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="google\protobuf\unittest_no_generic_services.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\google\protobuf\io\coded_stream_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\command_line_interface_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\mock_code_generator.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\common_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_bootstrap_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_plugin_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_plugin_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_doc_comment_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\python\python_plugin_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\descriptor_database_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\descriptor_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\dynamic_message_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\extension_set_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\testing\file.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\generated_message_reflection_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\testing\googletest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\importer_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\message_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\once_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\parser_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\io\printer_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\reflection_ops_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\repeated_field_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\repeated_field_reflection_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\structurally_valid_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\stringprintf_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\template_util_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\type_traits_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\strutil_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\test_util.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\text_format_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\io\tokenizer_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_custom_options.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_embed_optimize_for.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_import.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_import_public.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_lite_imports_nonlite.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_mset.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_optimize_for.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="google\protobuf\unittest_no_generic_services.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\unknown_field_set_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\wire_format_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\io\zero_copy_stream_unittest.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="..\src\google\protobuf\compiler\cpp\cpp_test_bad_identifiers.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_custom_options.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_embed_optimize_for.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_import.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_import_public.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_lite_imports_nonlite.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_mset.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_optimize_for.proto" />
    <CustomBuild Include="..\src\google\protobuf\unittest_no_generic_services.proto" />
  </ItemGroup>
</Project>