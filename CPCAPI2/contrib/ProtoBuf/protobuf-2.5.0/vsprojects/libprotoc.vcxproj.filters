﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\google\protobuf\compiler\code_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\command_line_interface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\subprocess.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\zip_writer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\plugin.pb.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_enum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_enum_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_extension.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_helpers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_message.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_message_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_options.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_primitive_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_service.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\cpp\cpp_string_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_enum.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_enum_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_extension.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_file.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_helpers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_message.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_message_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_primitive_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_service.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_string_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\java\java_doc_comment.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\compiler\python\python_generator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_doc_comment.cc">
      <Filter>Header Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\code_generator.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\command_line_interface.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\subprocess.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\zip_writer.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\plugin.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\plugin.pb.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_enum.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_enum_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_extension.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_file.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_generator.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_helpers.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_message.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_message_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_primitive_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_service.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\cpp\cpp_string_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_enum.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_enum_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_extension.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_file.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_generator.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_helpers.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_message.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_message_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_primitive_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_service.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\java\java_string_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\compiler\python\python_generator.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>