﻿<?xml version="1.0" encoding="UTF-8"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="libprotobuf"
	ProjectGUID="{3E283F37-A4ED-41B7-A3E6-A2D89D131A30}"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="4"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				Optimization="0"
				AdditionalIncludeDirectories="../src;."
				PreprocessorDefinitions="WIN32;_DEBUG;_WINDOWS;_USRDLL;LIBPROTOBUF_EXPORTS;"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="4"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				AdditionalIncludeDirectories="../src;."
				PreprocessorDefinitions="WIN32;NDEBUG;_WINDOWS;_USRDLL;LIBPROTOBUF_EXPORTS;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLibrarianTool"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath="..\src\google\protobuf\io\coded_stream.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\coded_stream_inl.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\common.h"
				>
			</File>
			<File
				RelativePath=".\config.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor.pb.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor_database.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\dynamic_message.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\extension_set.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\generated_message_reflection.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\generated_message_util.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\gzip_stream.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\hash.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\importer.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\map-util.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\message.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\message_lite.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\atomicops.h"
			>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.h"
			>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\platform_macros.h"
			>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\once.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\parser.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\printer.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\reflection_ops.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\repeated_field.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\service.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\stl_util.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\stringprintf.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\template_util.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\type_traits.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\strutil.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\substitute.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\text_format.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\tokenizer.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\unknown_field_set.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format_lite.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format_lite_inl.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream_impl.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream_impl_lite.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\src\google\protobuf\io\coded_stream.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\common.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor.pb.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor_database.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\dynamic_message.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\extension_set.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\extension_set_heavy.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\generated_message_reflection.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\generated_message_util.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\gzip_stream.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\importer.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\message.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\message_lite.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\once.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\parser.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\printer.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\reflection_ops.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\repeated_field.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\service.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\structurally_valid.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\strutil.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\substitute.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\text_format.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\tokenizer.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\unknown_field_set.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format_lite.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream_impl.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream_impl_lite.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\stringprintf.cc"
				>
			</File>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
