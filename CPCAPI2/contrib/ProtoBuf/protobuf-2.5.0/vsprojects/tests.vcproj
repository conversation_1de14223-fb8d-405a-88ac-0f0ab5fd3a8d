﻿<?xml version="1.0" encoding="UTF-8"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="8.00"
	Name="tests"
	ProjectGUID="{4DF72760-C055-40A5-A77E-30A17E2AC2DB}"
	RootNamespace="tests"
	Keyword="Win32Proj"
	TargetFrameworkVersion="0"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="Debug"
			IntermediateDirectory="Debug"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				Optimization="0"
				AdditionalIncludeDirectories="../src;.;../gtest/include"
				PreprocessorDefinitions="WIN32;_DEBUG;_CONSOLE;_VARIADIC_MAX=10;"
				MinimalRebuild="true"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="4"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="1"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="Release"
			IntermediateDirectory="Release"
			ConfigurationType="1"
			>
			<Tool
				Name="VCPreBuildEventTool"
			/>
			<Tool
				Name="VCCustomBuildTool"
				Description=""
				CommandLine=""
				AdditionalDependencies=""
				Outputs=""
			/>
			<Tool
				Name="VCXMLDataGeneratorTool"
			/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"
			/>
			<Tool
				Name="VCMIDLTool"
			/>
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305"
				AdditionalIncludeDirectories="../src;.;../gtest/include"
				PreprocessorDefinitions="WIN32;NDEBUG;_CONSOLE;_VARIADIC_MAX=10;"
				RuntimeLibrary="2"
				UsePrecompiledHeader="0"
				WarningLevel="3"
				Detect64BitPortabilityProblems="true"
				DebugInformationFormat="3"
			/>
			<Tool
				Name="VCManagedResourceCompilerTool"
			/>
			<Tool
				Name="VCResourceCompilerTool"
			/>
			<Tool
				Name="VCPreLinkEventTool"
			/>
			<Tool
				Name="VCLinkerTool"
				LinkIncremental="2"
				GenerateDebugInformation="true"
				SubSystem="1"
				OptimizeReferences="2"
				EnableCOMDATFolding="2"
				TargetMachine="1"
			/>
			<Tool
				Name="VCALinkTool"
			/>
			<Tool
				Name="VCManifestTool"
			/>
			<Tool
				Name="VCXDCMakeTool"
			/>
			<Tool
				Name="VCBscMakeTool"
			/>
			<Tool
				Name="VCFxCopTool"
			/>
			<Tool
				Name="VCAppVerifierTool"
			/>
			<Tool
				Name="VCPostBuildEventTool"
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="Header Files"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\testing\file.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\testing\googletest.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\test_util.h"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\mock_code_generator.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_custom_options.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_embed_optimize_for.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_import.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_import_public.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_lite_imports_nonline.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_mset.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_optimize_for.pb.h"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_no_generic_services.pb.h"
				>
			</File>
		</Filter>
		<Filter
			Name="Resource Files"
			Filter="rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx"
			UniqueIdentifier="{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}"
			>
		</Filter>
		<Filter
			Name="Source Files"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath="..\src\google\protobuf\io\coded_stream_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\command_line_interface_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\mock_code_generator.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\common_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_bootstrap_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_unittest.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\cpp\cpp_plugin_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_plugin_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\java\java_doc_comment_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\python\python_plugin_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor_database_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\descriptor_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\dynamic_message_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\extension_set_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\testing\file.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\generated_message_reflection_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\testing\googletest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\importer_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\message_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\once_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\compiler\parser_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\printer_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\reflection_ops_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\repeated_field_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\repeated_field_reflection_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\structurally_valid_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\stringprintf_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\template_util_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\type_traits_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\stubs\strutil_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\test_util.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\text_format_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\tokenizer_unittest.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_custom_options.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_embed_optimize_for.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_import.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_import_public.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_lite_imports_nonlite.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_mset.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_optimize_for.pb.cc"
				>
			</File>
			<File
				RelativePath=".\google\protobuf\unittest_no_generic_services.pb.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\unknown_field_set_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\wire_format_unittest.cc"
				>
			</File>
			<File
				RelativePath="..\src\google\protobuf\io\zero_copy_stream_unittest.cc"
				>
			</File>
		</Filter>
		<File
			RelativePath="..\src\google\protobuf\compiler\cpp\cpp_test_bad_identifiers.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating cpp_test_bad_identifiers.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.h;google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating cpp_test_bad_identifiers.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/compiler/cpp/cpp_test_bad_identifiers.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.h;google\protobuf\compiler\cpp\cpp_test_bad_identifiers.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest.pb.h;google\protobuf\unittest.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest.pb.h;google\protobuf\unittest.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_custom_options.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_custom_options.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_custom_options.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_custom_options.pb.h;google\protobuf\unittest_custom_options.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_custom_options.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_custom_options.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_custom_options.pb.h;google\protobuf\unittest_custom_options.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_embed_optimize_for.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_embed_optimize_for.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_embed_optimize_for.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_embed_optimize_for.pb.h;google\protobuf\unittest_embed_optimize_for.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_embed_optimize_for.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_embed_optimize_for.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_embed_optimize_for.pb.h;google\protobuf\unittest_embed_optimize_for.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_import.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_import.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_import.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_import.pb.h;google\protobuf\unittest_import.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_import.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_import.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_import.pb.h;google\protobuf\unittest_import.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_import_public.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_import_public.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_import_public.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_import_public.pb.h;google\protobuf\unittest_import_public.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_import_public.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_import_public.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_import_public.pb.h;google\protobuf\unittest_import_public.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_lite_imports_nonlite.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_lite_imports_nonlite.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_lite_imports_nonlite.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_lite_imports_nonlite.pb.h;google\protobuf\unittest_lite_imports_nonlite.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_lite_imports_nonlite.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_lite_imports_nonlite.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_lite_imports_nonlite.pb.h;google\protobuf\unittest_lite_imports_nonlite.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_mset.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_mset.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_mset.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_mset.pb.h;google\protobuf\unittest_mset.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_mset.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_mset.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_mset.pb.h;google\protobuf\unittest_mset.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_optimize_for.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_optimize_for.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_optimize_for.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_optimize_for.pb.h;google\protobuf\unittest_optimize_for.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_optimize_for.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_optimize_for.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_optimize_for.pb.h;google\protobuf\unittest_optimize_for.pb.cc"
				/>
			</FileConfiguration>
		</File>
		<File
			RelativePath="..\src\google\protobuf\unittest_no_generic_services.proto"
			>
			<FileConfiguration
				Name="Debug|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_no_generic_services.pb.{h,cc}..."
					CommandLine="Debug\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_no_generic_services.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_no_generic_services.pb.h;google\protobuf\unittest_no_generic_services.pb.cc"
				/>
			</FileConfiguration>
			<FileConfiguration
				Name="Release|Win32"
				>
				<Tool
					Name="VCCustomBuildTool"
					Description="Generating unittest_no_generic_services.pb.{h,cc}..."
					CommandLine="Release\protoc -I../src --cpp_out=. ../src/google/protobuf/unittest_no_generic_services.proto&#x0D;&#x0A;"
					Outputs="google\protobuf\unittest_no_generic_services.pb.h;google\protobuf\unittest_no_generic_services.pb.cc"
				/>
			</FileConfiguration>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
