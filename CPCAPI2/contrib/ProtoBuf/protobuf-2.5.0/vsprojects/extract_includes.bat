md include
md include\google
md include\google\protobuf
md include\google\protobuf\stubs
md include\google\protobuf\io
md include\google\protobuf\compiler
md include\google\protobuf\compiler\cpp
md include\google\protobuf\compiler\java
md include\google\protobuf\compiler\python
copy ..\src\google\protobuf\stubs\atomicops.h include\google\protobuf\stubs\atomicops.h
copy ..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.h include\google\protobuf\stubs\atomicops_internals_x86_msvc.h
copy ..\src\google\protobuf\stubs\common.h include\google\protobuf\stubs\common.h
copy ..\src\google\protobuf\stubs\once.h include\google\protobuf\stubs\once.h
copy ..\src\google\protobuf\stubs\platform_macros.h include\google\protobuf\stubs\platform_macros.h
copy ..\src\google\protobuf\stubs\template_util.h include\google\protobuf\stubs\template_util.h
copy ..\src\google\protobuf\stubs\type_traits.h include\google\protobuf\stubs\type_traits.h
copy ..\src\google\protobuf\descriptor.h include\google\protobuf\descriptor.h
copy ..\src\google\protobuf\descriptor.pb.h include\google\protobuf\descriptor.pb.h
copy ..\src\google\protobuf\descriptor_database.h include\google\protobuf\descriptor_database.h
copy ..\src\google\protobuf\dynamic_message.h include\google\protobuf\dynamic_message.h
copy ..\src\google\protobuf\extension_set.h include\google\protobuf\extension_set.h
copy ..\src\google\protobuf\generated_enum_reflection.h include\google\protobuf\generated_enum_reflection.h
copy ..\src\google\protobuf\generated_message_util.h include\google\protobuf\generated_message_util.h
copy ..\src\google\protobuf\generated_message_reflection.h include\google\protobuf\generated_message_reflection.h
copy ..\src\google\protobuf\message.h include\google\protobuf\message.h
copy ..\src\google\protobuf\message_lite.h include\google\protobuf\message_lite.h
copy ..\src\google\protobuf\reflection_ops.h include\google\protobuf\reflection_ops.h
copy ..\src\google\protobuf\repeated_field.h include\google\protobuf\repeated_field.h
copy ..\src\google\protobuf\service.h include\google\protobuf\service.h
copy ..\src\google\protobuf\text_format.h include\google\protobuf\text_format.h
copy ..\src\google\protobuf\unknown_field_set.h include\google\protobuf\unknown_field_set.h
copy ..\src\google\protobuf\wire_format.h include\google\protobuf\wire_format.h
copy ..\src\google\protobuf\wire_format_lite.h include\google\protobuf\wire_format_lite.h
copy ..\src\google\protobuf\wire_format_lite_inl.h include\google\protobuf\wire_format_lite_inl.h
copy ..\src\google\protobuf\io\coded_stream.h include\google\protobuf\io\coded_stream.h
copy ..\src\google\protobuf\io\gzip_stream.h include\google\protobuf\io\gzip_stream.h
copy ..\src\google\protobuf\io\printer.h include\google\protobuf\io\printer.h
copy ..\src\google\protobuf\io\tokenizer.h include\google\protobuf\io\tokenizer.h
copy ..\src\google\protobuf\io\zero_copy_stream.h include\google\protobuf\io\zero_copy_stream.h
copy ..\src\google\protobuf\io\zero_copy_stream_impl.h include\google\protobuf\io\zero_copy_stream_impl.h
copy ..\src\google\protobuf\io\zero_copy_stream_impl_lite.h include\google\protobuf\io\zero_copy_stream_impl_lite.h
copy ..\src\google\protobuf\compiler\code_generator.h include\google\protobuf\compiler\code_generator.h
copy ..\src\google\protobuf\compiler\command_line_interface.h include\google\protobuf\compiler\command_line_interface.h
copy ..\src\google\protobuf\compiler\importer.h include\google\protobuf\compiler\importer.h
copy ..\src\google\protobuf\compiler\parser.h include\google\protobuf\compiler\parser.h
copy ..\src\google\protobuf\compiler\cpp\cpp_generator.h include\google\protobuf\compiler\cpp\cpp_generator.h
copy ..\src\google\protobuf\compiler\java\java_generator.h include\google\protobuf\compiler\java\java_generator.h
copy ..\src\google\protobuf\compiler\python\python_generator.h include\google\protobuf\compiler\python\python_generator.h
copy ..\src\google\protobuf\compiler\plugin.h include\google\protobuf\compiler\plugin.h
