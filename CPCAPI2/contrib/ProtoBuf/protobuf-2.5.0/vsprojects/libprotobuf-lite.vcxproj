﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{49EA010D-706F-4BE2-A397-77854B72A040}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v140</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>11.0.51106.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>Debug\</OutDir>
    <IntDir>$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>Release\</OutDir>
    <IntDir>$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <AdditionalOptions>/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305 %(AdditionalOptions)</AdditionalOptions>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>../src;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;LIBPROTOBUF_EXPORTS;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <AdditionalOptions>/wd4244 /wd4267 /wd4018 /wd4355 /wd4800 /wd4251 /wd4996 /wd4146 /wd4305 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalIncludeDirectories>../src;.;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;LIBPROTOBUF_EXPORTS;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\src\google\protobuf\io\coded_stream.h" />
    <ClInclude Include="..\src\google\protobuf\io\coded_stream_inl.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\common.h" />
    <ClInclude Include="config.h" />
    <ClInclude Include="..\src\google\protobuf\extension_set.h" />
    <ClInclude Include="..\src\google\protobuf\generated_message_util.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\hash.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\map-util.h" />
    <ClInclude Include="..\src\google\protobuf\message_lite.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\once.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\atomicops.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\platform_macros.h" />
    <ClInclude Include="..\src\google\protobuf\repeated_field.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\stl_util.h" />
    <ClInclude Include="..\src\google\protobuf\wire_format_lite.h" />
    <ClInclude Include="..\src\google\protobuf\wire_format_lite_inl.h" />
    <ClInclude Include="..\src\google\protobuf\io\zero_copy_stream.h" />
    <ClInclude Include="..\src\google\protobuf\io\zero_copy_stream_impl_lite.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\stringprintf.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\template_util.h" />
    <ClInclude Include="..\src\google\protobuf\stubs\type_traits.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\google\protobuf\io\coded_stream.cc" />
    <ClCompile Include="..\src\google\protobuf\stubs\common.cc" />
    <ClCompile Include="..\src\google\protobuf\extension_set.cc" />
    <ClCompile Include="..\src\google\protobuf\generated_message_util.cc" />
    <ClCompile Include="..\src\google\protobuf\message_lite.cc" />
    <ClCompile Include="..\src\google\protobuf\stubs\once.cc" />
    <ClCompile Include="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.cc" />
    <ClCompile Include="..\src\google\protobuf\repeated_field.cc" />
    <ClCompile Include="..\src\google\protobuf\wire_format_lite.cc" />
    <ClCompile Include="..\src\google\protobuf\io\zero_copy_stream.cc" />
    <ClCompile Include="..\src\google\protobuf\io\zero_copy_stream_impl_lite.cc" />
    <ClCompile Include="..\src\google\protobuf\stubs\stringprintf.cc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>