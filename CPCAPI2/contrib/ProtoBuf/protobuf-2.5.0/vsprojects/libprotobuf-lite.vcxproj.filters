﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx</Extensions>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\google\protobuf\io\coded_stream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\io\coded_stream_inl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\common.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="config.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\extension_set.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\generated_message_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\hash.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\map-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\message_lite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\once.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\atomicops.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\platform_macros.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\repeated_field.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\stl_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\wire_format_lite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\wire_format_lite_inl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\io\zero_copy_stream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\io\zero_copy_stream_impl_lite.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\stringprintf.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\template_util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\google\protobuf\stubs\type_traits.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\google\protobuf\io\coded_stream.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\common.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\extension_set.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\generated_message_util.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\message_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\once.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\atomicops_internals_x86_msvc.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\repeated_field.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\wire_format_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\io\zero_copy_stream.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\io\zero_copy_stream_impl_lite.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\google\protobuf\stubs\stringprintf.cc">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
</Project>