// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Max Cai)

package protobuf_unittest;

option java_package = "com.google.protobuf";
option java_outer_classname = "NanoRepeatedPackables";

enum Enum {
  OPTION_ONE = 1;
  OPTION_TWO = 2;
}

// Two almost identical messages with all packable repeated field types.
// One with none marked as packed and the other all packed. For
// compatibility, they should be able to parse each other's serialized
// forms.

message NonPacked {

  // All packable types, none marked as packed.

  repeated    int32 int32s    = 1;
  repeated    int64 int64s    = 2;
  repeated   uint32 uint32s   = 3;
  repeated   uint64 uint64s   = 4;
  repeated   sint32 sint32s   = 5;
  repeated   sint64 sint64s   = 6;
  repeated  fixed32 fixed32s  = 7;
  repeated  fixed64 fixed64s  = 8;
  repeated sfixed32 sfixed32s = 9;
  repeated sfixed64 sfixed64s = 10;
  repeated    float floats    = 11;
  repeated   double doubles   = 12;
  repeated     bool bools     = 13;
  repeated     Enum enums     = 14;

  // Noise for testing merged deserialization.
  optional int32 noise = 15;

}

message Packed {

  // All packable types, all matching the field numbers in NonPacked,
  // all marked as packed.

  repeated    int32 int32s    = 1  [ packed = true ];
  repeated    int64 int64s    = 2  [ packed = true ];
  repeated   uint32 uint32s   = 3  [ packed = true ];
  repeated   uint64 uint64s   = 4  [ packed = true ];
  repeated   sint32 sint32s   = 5  [ packed = true ];
  repeated   sint64 sint64s   = 6  [ packed = true ];
  repeated  fixed32 fixed32s  = 7  [ packed = true ];
  repeated  fixed64 fixed64s  = 8  [ packed = true ];
  repeated sfixed32 sfixed32s = 9  [ packed = true ];
  repeated sfixed64 sfixed64s = 10 [ packed = true ];
  repeated    float floats    = 11 [ packed = true ];
  repeated   double doubles   = 12 [ packed = true ];
  repeated     bool bools     = 13 [ packed = true ];
  repeated     Enum enums     = 14 [ packed = true ];

  // Noise for testing merged deserialization.
  optional int32 noise = 15;

}
