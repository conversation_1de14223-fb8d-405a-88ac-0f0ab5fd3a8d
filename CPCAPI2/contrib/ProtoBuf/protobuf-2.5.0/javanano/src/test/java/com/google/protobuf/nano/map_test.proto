// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

syntax = "proto3";

package map_test;

option java_package = "com.google.protobuf";
option java_outer_classname = "MapTestProto";

message TestMap {
  message MessageValue {
    int32 value = 1;
    int32 value2 = 2;
  }
  enum EnumValue {
    FOO = 0;
    BAR = 1;
    BAZ = 2;
    QUX = 3;
  }

  map<int32, int32>        int32_to_int32_field = 1;
  map<int32, string>       int32_to_string_field = 2;
  map<int32, bytes>        int32_to_bytes_field = 3;
  map<int32, EnumValue>    int32_to_enum_field = 4;
  map<int32, MessageValue> int32_to_message_field = 5;
  map<string, int32>       string_to_int32_field = 6;
  map<bool, bool>          bool_to_bool_field = 7;

  // Test all the other primitive types. As the key and value are not coupled in
  // the implementation, we do not test all the combinations of key/value pairs,
  // so that we can keep the number of test cases manageable
  map<uint32, uint32>      uint32_to_uint32_field = 11;
  map<sint32, sint32>      sint32_to_sint32_field = 12;
  map<fixed32, fixed32>    fixed32_to_fixed32_field = 13;
  map<sfixed32, sfixed32>  sfixed32_to_sfixed32_field = 14;
  map<int64, int64>        int64_to_int64_field = 15;
  map<uint64, uint64>      uint64_to_uint64_field = 16;
  map<sint64, sint64>      sint64_to_sint64_field = 17;
  map<fixed64, fixed64>    fixed64_to_fixed64_field = 18;
  map<sfixed64, sfixed64>  sfixed64_to_sfixed64_field = 19;
}
