syntax = "proto2";

option java_multiple_files = true;
option java_package = "com.google.protobuf";

import "google/protobuf/nano/unittest_extension_nano.proto";

// Must be compiled separately due to extension number reuse.
// The reuse is deliberate, for testing wire compatibility.

message PackedExtensions {
  extend ExtendableMessage {
    repeated int32    packed_int32    = 10 [ packed = true ];
    repeated uint32   packed_uint32   = 11 [ packed = true ];
    repeated sint32   packed_sint32   = 12 [ packed = true ];
    repeated int64    packed_int64    = 13 [ packed = true ];
    repeated uint64   packed_uint64   = 14 [ packed = true ];
    repeated sint64   packed_sint64   = 15 [ packed = true ];
    repeated fixed32  packed_fixed32  = 16 [ packed = true ];
    repeated sfixed32 packed_sfixed32 = 17 [ packed = true ];
    repeated fixed64  packed_fixed64  = 18 [ packed = true ];
    repeated sfixed64 packed_sfixed64 = 19 [ packed = true ];
    repeated bool     packed_bool     = 20 [ packed = true ];
    repeated float    packed_float    = 21 [ packed = true ];
    repeated double   packed_double   = 22 [ packed = true ];
    repeated AnEnum   packed_enum     = 23 [ packed = true ];
    // Non-packable types omitted.
  }
}
