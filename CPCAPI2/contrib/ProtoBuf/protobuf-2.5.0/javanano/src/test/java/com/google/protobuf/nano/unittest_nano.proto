// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Wink Saville)

package protobuf_unittest;

import "google/protobuf/nano/unittest_import_nano.proto";

option java_package = "com.google.protobuf";
option java_outer_classname = "NanoOuterClass";

// Same as TestAllTypes but with the nano runtime.
message TestAllTypesNano {

  message NestedMessage {
    optional int32 bb = 1;
  }

  enum NestedEnum {
    FOO = 1;
    BAR = 2;
    BAZ = 3;
  }

  // Singular
  optional    int32 optional_int32    =  1;
  optional    int64 optional_int64    =  2;
  optional   uint32 optional_uint32   =  3;
  optional   uint64 optional_uint64   =  4;
  optional   sint32 optional_sint32   =  5;
  optional   sint64 optional_sint64   =  6;
  optional  fixed32 optional_fixed32  =  7;
  optional  fixed64 optional_fixed64  =  8;
  optional sfixed32 optional_sfixed32 =  9;
  optional sfixed64 optional_sfixed64 = 10;
  optional    float optional_float    = 11;
  optional   double optional_double   = 12;
  optional     bool optional_bool     = 13;
  optional   string optional_string   = 14;
  optional    bytes optional_bytes    = 15;

  optional group OptionalGroup = 16 {
    optional int32 a = 17;
  }

  optional NestedMessage      optional_nested_message  = 18;
  optional ForeignMessageNano optional_foreign_message = 19;
  optional protobuf_unittest_import.ImportMessageNano
    optional_import_message = 20;

  optional NestedEnum      optional_nested_enum     = 21;
  optional ForeignEnumNano optional_foreign_enum    = 22;
  optional protobuf_unittest_import.ImportEnumNano optional_import_enum = 23;

  optional string optional_string_piece = 24 [ctype=STRING_PIECE];
  optional string optional_cord = 25 [ctype=CORD];

  // Repeated
  repeated    int32 repeated_int32    = 31;
  repeated    int64 repeated_int64    = 32;
  repeated   uint32 repeated_uint32   = 33;
  repeated   uint64 repeated_uint64   = 34;
  repeated   sint32 repeated_sint32   = 35;
  repeated   sint64 repeated_sint64   = 36;
  repeated  fixed32 repeated_fixed32  = 37;
  repeated  fixed64 repeated_fixed64  = 38;
  repeated sfixed32 repeated_sfixed32 = 39;
  repeated sfixed64 repeated_sfixed64 = 40;
  repeated    float repeated_float    = 41;
  repeated   double repeated_double   = 42;
  repeated     bool repeated_bool     = 43;
  repeated   string repeated_string   = 44;
  repeated    bytes repeated_bytes    = 45;

  repeated group RepeatedGroup = 46 {
    optional int32 a = 47;
  }

  repeated NestedMessage      repeated_nested_message  = 48;
  repeated ForeignMessageNano repeated_foreign_message = 49;
  repeated protobuf_unittest_import.ImportMessageNano
    repeated_import_message = 50;

  repeated NestedEnum      repeated_nested_enum  = 51;
  repeated ForeignEnumNano repeated_foreign_enum = 52;
  repeated protobuf_unittest_import.ImportEnumNano repeated_import_enum = 53;

  repeated string repeated_string_piece = 54 [ctype=STRING_PIECE];
  repeated string repeated_cord = 55 [ctype=CORD];

  // Repeated packed
  repeated    int32 repeated_packed_int32    = 87 [packed=true];
  repeated sfixed64 repeated_packed_sfixed64 = 88 [packed=true];

  repeated NestedEnum repeated_packed_nested_enum  = 89 [packed=true];

  // Singular with defaults
  optional    int32 default_int32    = 61 [default =  41    ];
  optional    int64 default_int64    = 62 [default =  42    ];
  optional   uint32 default_uint32   = 63 [default =  43    ];
  optional   uint64 default_uint64   = 64 [default =  44    ];
  optional   sint32 default_sint32   = 65 [default = -45    ];
  optional   sint64 default_sint64   = 66 [default =  46    ];
  optional  fixed32 default_fixed32  = 67 [default =  47    ];
  optional  fixed64 default_fixed64  = 68 [default =  48    ];
  optional sfixed32 default_sfixed32 = 69 [default =  49    ];
  optional sfixed64 default_sfixed64 = 70 [default = -50    ];
  optional    float default_float    = 71 [default =  51.5  ];
  optional   double default_double   = 72 [default =  52e3  ];
  optional     bool default_bool     = 73 [default = true   ];
  optional   string default_string   = 74 [default = "hello"];
  optional    bytes default_bytes    = 75 [default = "world"];

  optional string default_string_nonascii = 76 [default = "dünya"];
  optional  bytes default_bytes_nonascii  = 77 [default = "dünyab"];

  optional  float default_float_inf      = 97  [default =  inf];
  optional  float default_float_neg_inf  = 98  [default = -inf];
  optional  float default_float_nan      = 99  [default =  nan];
  optional double default_double_inf     = 100 [default =  inf];
  optional double default_double_neg_inf = 101 [default = -inf];
  optional double default_double_nan     = 102 [default =  nan];

  optional NestedEnum default_nested_enum = 81 [default = BAR];
  optional ForeignEnumNano default_foreign_enum = 82
      [default = FOREIGN_NANO_BAR];
  optional protobuf_unittest_import.ImportEnumNano
      default_import_enum = 83 [default = IMPORT_NANO_BAR];

  optional string default_string_piece = 84 [ctype=STRING_PIECE,default="abc"];
  optional string default_cord = 85 [ctype=CORD,default="123"];

  required int32 id = 86;

  // Try to cause conflicts.
  optional int32 tag = 93;
  optional int32 get_serialized_size = 94;
  optional int32 write_to = 95;

  // Try to fail with java reserved keywords
  optional int32 synchronized = 96;

  oneof oneof_field {
    uint32 oneof_uint32 = 111;
    NestedMessage oneof_nested_message = 112;
    string oneof_string = 123;
    bytes oneof_bytes = 124;
    fixed64 oneof_fixed64 = 115;
    NestedEnum oneof_enum = 116;
  }
}

message ForeignMessageNano {
  optional int32 c = 1;
}

enum ForeignEnumNano {
  FOREIGN_NANO_FOO = 4;
  FOREIGN_NANO_BAR = 5;
  FOREIGN_NANO_BAZ = 6;
}

// Test that deprecated fields work.  We only verify that they compile (at one
// point this failed).
message TestDeprecatedNano {
  optional int32 deprecated_field = 1 [deprecated = true];
}
