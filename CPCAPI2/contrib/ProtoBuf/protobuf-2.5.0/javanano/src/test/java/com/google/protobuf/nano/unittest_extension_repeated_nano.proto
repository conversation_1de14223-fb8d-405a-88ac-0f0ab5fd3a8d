syntax = "proto2";

option java_multiple_files = true;
option java_package = "com.google.protobuf";

import "google/protobuf/nano/unittest_extension_nano.proto";

// Must be compiled separately due to extension number reuse.
// The reuse is deliberate, for testing wire compatibility.

message RepeatedExtensions {
  extend ExtendableMessage {
    repeated int32          repeated_int32    = 10;
    repeated uint32         repeated_uint32   = 11;
    repeated sint32         repeated_sint32   = 12;
    repeated int64          repeated_int64    = 13;
    repeated uint64         repeated_uint64   = 14;
    repeated sint64         repeated_sint64   = 15;
    repeated fixed32        repeated_fixed32  = 16;
    repeated sfixed32       repeated_sfixed32 = 17;
    repeated fixed64        repeated_fixed64  = 18;
    repeated sfixed64       repeated_sfixed64 = 19;
    repeated bool           repeated_bool     = 20;
    repeated float          repeated_float    = 21;
    repeated double         repeated_double   = 22;
    repeated AnEnum         repeated_enum     = 23;
    repeated string         repeated_string   = 24;
    repeated bytes          repeated_bytes    = 25;
    repeated AnotherMessage repeated_message  = 26;
    repeated group          RepeatedGroup     = 27 {
      optional int32 a = 1;
    }
  }
}
