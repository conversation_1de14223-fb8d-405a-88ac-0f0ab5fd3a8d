// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// https://developers.google.com/protocol-buffers/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Max Cai)

package protobuf_unittest;

option java_package = "com.google.protobuf";
option java_outer_classname = "NanoAccessorsOuterClass";

message TestNanoAccessors {

  message NestedMessage {
    optional int32 bb = 1;
  }

  enum NestedEnum {
    FOO = 1;
    BAR = 2;
    BAZ = 3;
  }

  // Singular
  optional int32  optional_int32    =  1;
  optional float  optional_float    = 11;
  optional double optional_double   = 12;
  optional string optional_string   = 14;
  optional bytes  optional_bytes    = 15;

  optional NestedMessage optional_nested_message = 18;

  optional NestedEnum optional_nested_enum = 21;

  // Repeated
  repeated int32  repeated_int32    = 31;
  repeated string repeated_string   = 44;
  repeated bytes  repeated_bytes    = 45;

  repeated NestedMessage repeated_nested_message  = 48;

  repeated NestedEnum repeated_nested_enum  = 51;

  // Singular with defaults
  optional int32  default_int32    = 61 [default =  41    ];
  optional string default_string   = 74 [default = "hello"];
  optional bytes  default_bytes    = 75 [default = "world"];

  optional float default_float_nan = 99  [default =  nan];

  optional NestedEnum default_nested_enum = 81 [default = BAR];

  // Required
  required int32 id = 86;

  // Add enough optional fields to make 2 bit fields in total
  optional int32 filler100 = 100;
  optional int32 filler101 = 101;
  optional int32 filler102 = 102;
  optional int32 filler103 = 103;
  optional int32 filler104 = 104;
  optional int32 filler105 = 105;
  optional int32 filler106 = 106;
  optional int32 filler107 = 107;
  optional int32 filler108 = 108;
  optional int32 filler109 = 109;
  optional int32 filler110 = 110;
  optional int32 filler111 = 111;
  optional int32 filler112 = 112;
  optional int32 filler113 = 113;
  optional int32 filler114 = 114;
  optional int32 filler115 = 115;
  optional int32 filler116 = 116;
  optional int32 filler117 = 117;
  optional int32 filler118 = 118;
  optional int32 filler119 = 119;
  optional int32 filler120 = 120;
  optional int32 filler121 = 121;
  optional int32 filler122 = 122;
  optional int32 filler123 = 123;
  optional int32 filler124 = 124;
  optional int32 filler125 = 125;
  optional int32 filler126 = 126;
  optional int32 filler127 = 127;
  optional int32 filler128 = 128;
  optional int32 filler129 = 129;
  optional int32 filler130 = 130;

  optional int32 before_bit_field_check = 139;
  optional int32 bit_field_check = 140;
  optional int32 after_bit_field_check = 141;
}
