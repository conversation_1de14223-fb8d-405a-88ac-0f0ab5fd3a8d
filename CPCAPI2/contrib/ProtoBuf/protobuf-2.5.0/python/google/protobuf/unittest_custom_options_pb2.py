# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/unittest_custom_options.proto

from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import service as _service
from google.protobuf import service_reflection
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)


import google.protobuf.descriptor_pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='google/protobuf/unittest_custom_options.proto',
  package='protobuf_unittest',
  serialized_pb='\n-google/protobuf/unittest_custom_options.proto\x12\x11protobuf_unittest\x1a google/protobuf/descriptor.proto\"\x8d\x01\n\x1cTestMessageWithCustomOptions\x12\x1e\n\x06\x66ield1\x18\x01 \x01(\tB\x0e\x08\x01\xc1\xe0\xc3\x1d-\xe1u\n\x02\x00\x00\x00\";\n\x06\x41nEnum\x12\x0f\n\x0b\x41NENUM_VAL1\x10\x01\x12\x16\n\x0b\x41NENUM_VAL2\x10\x02\x1a\x05\xb0\x86\xfa\x05{\x1a\x08\xc5\xf6\xc9\x1d\xeb\xfc\xff\xff:\x10\x08\x00\xe0\xe9\xc2\x1d\xc8\xff\xff\xff\xff\xff\xff\xff\xff\x01\"\x18\n\x16\x43ustomOptionFooRequest\"\x19\n\x17\x43ustomOptionFooResponse\"\x1e\n\x1c\x43ustomOptionFooClientMessage\"\x1e\n\x1c\x43ustomOptionFooServerMessage\"m\n\x1a\x44ummyMessageContainingEnum\"O\n\x0cTestEnumType\x12\x1a\n\x16TEST_OPTION_ENUM_TYPE1\x10\x16\x12#\n\x16TEST_OPTION_ENUM_TYPE2\x10\xe9\xff\xff\xff\xff\xff\xff\xff\xff\x01\"!\n\x1f\x44ummyMessageInvalidAsOptionType\"\x8a\x01\n\x1c\x43ustomOptionMinIntegerValues:j\xd0\xde\xb2\x1d\x00\xe8\xc6\xb2\x1d\x80\x80\x80\x80\xf8\xff\xff\xff\xff\x01\xb0\xbc\xb2\x1d\x80\x80\x80\x80\x80\x80\x80\x80\x80\x01\x80\x93\xb2\x1d\x00\xf8\xf5\xb0\x1d\x00\x80\xc4\xb0\x1d\xff\xff\xff\xff\x0f\xf8\x97\xb0\x1d\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x9d\xf5\xaf\x1d\x00\x00\x00\x00\x91\xee\xaf\x1d\x00\x00\x00\x00\x00\x00\x00\x00\xad\x8d\xaf\x1d\x00\x00\x00\x80\x99\xd6\xa8\x1d\x00\x00\x00\x00\x00\x00\x00\x80\"\x91\x01\n\x1c\x43ustomOptionMaxIntegerValues:q\xd0\xde\xb2\x1d\x01\xe8\xc6\xb2\x1d\xff\xff\xff\xff\x07\xb0\xbc\xb2\x1d\xff\xff\xff\xff\xff\xff\xff\xff\x7f\x80\x93\xb2\x1d\xff\xff\xff\xff\x0f\xf8\xf5\xb0\x1d\xff\xff\xff\xff\xff\xff\xff\xff\xff\x01\x80\xc4\xb0\x1d\xfe\xff\xff\xff\x0f\xf8\x97\xb0\x1d\xfe\xff\xff\xff\xff\xff\xff\xff\xff\x01\x9d\xf5\xaf\x1d\xff\xff\xff\xff\x91\xee\xaf\x1d\xff\xff\xff\xff\xff\xff\xff\xff\xad\x8d\xaf\x1d\xff\xff\xff\x7f\x99\xd6\xa8\x1d\xff\xff\xff\xff\xff\xff\xff\x7f\"n\n\x17\x43ustomOptionOtherValues:S\xe8\xc6\xb2\x1d\x9c\xff\xff\xff\xff\xff\xff\xff\xff\x01\xf5\xdf\xa3\x1d\xe7\x87\x45\x41\xe9\xdc\xa2\x1d\xfbY\x8c\x42\xca\xc0\xf3?\xaa\xdc\xa2\x1d\x0eHello, \"World\"\xb2\xd9\xa2\x1d\x0bHello\x00World\x88\xd9\xa2\x1d\xe9\xff\xff\xff\xff\xff\xff\xff\xff\x01\"4\n\x1cSettingRealsFromPositiveInts:\x14\xf5\xdf\xa3\x1d\x00\x00@A\xe9\xdc\xa2\x1d\x00\x00\x00\x00\x00@c@\"4\n\x1cSettingRealsFromNegativeInts:\x14\xf5\xdf\xa3\x1d\x00\x00@\xc1\xe9\xdc\xa2\x1d\x00\x00\x00\x00\x00@c\xc0\"G\n\x12\x43omplexOptionType1\x12\x0b\n\x03\x66oo\x18\x01 \x01(\x05\x12\x0c\n\x04\x66oo2\x18\x02 \x01(\x05\x12\x0c\n\x04\x66oo3\x18\x03 \x01(\x05*\x08\x08\x64\x10\x80\x80\x80\x80\x02\"\xc1\x02\n\x12\x43omplexOptionType2\x12\x32\n\x03\x62\x61r\x18\x01 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType1\x12\x0b\n\x03\x62\x61z\x18\x02 \x01(\x05\x12\x46\n\x04\x66red\x18\x03 \x01(\x0b\x32\x38.protobuf_unittest.ComplexOptionType2.ComplexOptionType4\x1a\x97\x01\n\x12\x43omplexOptionType4\x12\r\n\x05waldo\x18\x01 \x01(\x05\x32r\n\x0c\x63omplex_opt4\x12\x1f.google.protobuf.MessageOptions\x18\x8a\xf5\xd1\x03 \x01(\x0b\x32\x38.protobuf_unittest.ComplexOptionType2.ComplexOptionType4*\x08\x08\x64\x10\x80\x80\x80\x80\x02\"\x9c\x01\n\x12\x43omplexOptionType3\x12\x0b\n\x03qux\x18\x01 \x01(\x05\x12T\n\x12\x63omplexoptiontype5\x18\x02 \x01(\n28.protobuf_unittest.ComplexOptionType3.ComplexOptionType5\x1a#\n\x12\x43omplexOptionType5\x12\r\n\x05plugh\x18\x03 \x01(\x05\"\x1f\n\x0b\x43omplexOpt6\x12\x10\n\x05xyzzy\x18\xdf\xbf\xcf\x03 \x01(\x05\"\xd0\x01\n\x15VariousComplexOptions:\xb6\x01\xa2\xe2\x95\x1d\x02\x08*\xa2\xe2\x95\x1d\x06\xd8\x85\x9e\x1d\xc4\x02\xa2\xe2\x95\x1d\x08\x92\xf5\x9d\x1d\x03\x08\xec\x06\xaa\xfd\x90\x1d\x03\x10\xdb\x07\xaa\xfd\x90\x1d\x06\xf8\xe6\x97\x1d\x8e\x05\xaa\xfd\x90\x1d\x05\n\x03\x08\xe7\x05\xaa\xfd\x90\x1d\x08\n\x06\xd8\x85\x9e\x1d\xcf\x0f\xaa\xfd\x90\x1d\n\n\x08\x92\xf5\x9d\x1d\x03\x08\xd8\x0f\xaa\xfd\x90\x1d\x08\xc2\xac\x97\x1d\x03\x08\xe5\x05\xaa\xfd\x90\x1d\x0b\xc2\xac\x97\x1d\x06\xd8\x85\x9e\x1d\xce\x0f\xaa\xfd\x90\x1d\r\xc2\xac\x97\x1d\x08\x92\xf5\x9d\x1d\x03\x08\xc9\x10\xd2\xa8\x8f\x1d\x03\x08\xb3\x0f\xaa\xfd\x90\x1d\x05\x1a\x03\x08\xc1\x02\xfa\xde\x90\x1d\x02\x08\t\xfa\xde\x90\x1d\x04\x13\x18\x16\x14\xe3\xdc\xfc\x1c\xf8\xfd\xfb\x1c\x18\xe4\xdc\xfc\x1c\"#\n\x13\x41ggregateMessageSet*\x08\x08\x04\x10\xff\xff\xff\xff\x07:\x02\x08\x01\"\xa0\x01\n\x1a\x41ggregateMessageSetElement\x12\t\n\x01s\x18\x01 \x01(\t2w\n\x15message_set_extension\x12&.protobuf_unittest.AggregateMessageSet\x18\xf6\xeb\xae\x07 \x01(\x0b\x32-.protobuf_unittest.AggregateMessageSetElement\"\xfd\x01\n\tAggregate\x12\t\n\x01i\x18\x01 \x01(\x05\x12\t\n\x01s\x18\x02 \x01(\t\x12)\n\x03sub\x18\x03 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate\x12*\n\x04\x66ile\x18\x04 \x01(\x0b\x32\x1c.google.protobuf.FileOptions\x12\x34\n\x04mset\x18\x05 \x01(\x0b\x32&.protobuf_unittest.AggregateMessageSet2M\n\x06nested\x12\x1c.google.protobuf.FileOptions\x18\xa7\xd1\xb0\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate\"Y\n\x10\x41ggregateMessage\x12)\n\tfieldname\x18\x01 \x01(\x05\x42\x16\xf2\xa1\x87;\x11\x12\x0f\x46ieldAnnotation:\x1a\xc2\xd1\x86;\x15\x08\x65\x12\x11MessageAnnotation\"\xc9\x01\n\x10NestedOptionType\x1a;\n\rNestedMessage\x12\"\n\x0cnested_field\x18\x01 \x01(\x05\x42\x0c\xc1\xe0\xc3\x1d\xea\x03\x00\x00\x00\x00\x00\x00:\x06\xe0\xe9\xc2\x1d\xe9\x07\"5\n\nNestedEnum\x12\x1d\n\x11NESTED_ENUM_VALUE\x10\x01\x1a\x06\xb0\x86\xfa\x05\xec\x07\x1a\x08\xc5\xf6\xc9\x1d\xeb\x03\x00\x00\x32\x41\n\x10nested_extension\x12\x1c.google.protobuf.FileOptions\x18\xfd\xf8\xe2\x03 \x01(\x05\x42\x06\xc8\x8b\xca\x1d\xed\x07*6\n\nMethodOpt1\x12\x13\n\x0fMETHODOPT1_VAL1\x10\x01\x12\x13\n\x0fMETHODOPT1_VAL2\x10\x02*M\n\rAggregateEnum\x12%\n\x05VALUE\x10\x01\x1a\x1a\xca\xfc\x89;\x15\x12\x13\x45numValueAnnotation\x1a\x15\x92\x95\x88;\x10\x12\x0e\x45numAnnotation2\x8e\x01\n\x1cTestServiceWithCustomOptions\x12\x63\n\x03\x46oo\x12).protobuf_unittest.CustomOptionFooRequest\x1a*.protobuf_unittest.CustomOptionFooResponse\"\x05\xe0\xfa\x8c\x1e\x02\x1a\t\x90\xb2\x8b\x1e\xd3\xdb\x80\xcbI2\x99\x01\n\x10\x41ggregateService\x12k\n\x06Method\x12#.protobuf_unittest.AggregateMessage\x1a#.protobuf_unittest.AggregateMessage\"\x17\xca\xc8\x96;\x12\x12\x10MethodAnnotation\x1a\x18\xca\xfb\x8e;\x13\x12\x11ServiceAnnotation:2\n\tfile_opt1\x12\x1c.google.protobuf.FileOptions\x18\x8e\x9d\xd8\x03 \x01(\x04:8\n\x0cmessage_opt1\x12\x1f.google.protobuf.MessageOptions\x18\x9c\xad\xd8\x03 \x01(\x05:4\n\nfield_opt1\x12\x1d.google.protobuf.FieldOptions\x18\x88\xbc\xd8\x03 \x01(\x06:8\n\nfield_opt2\x12\x1d.google.protobuf.FieldOptions\x18\xb9\xa1\xd9\x03 \x01(\x05:\x02\x34\x32:2\n\tenum_opt1\x12\x1c.google.protobuf.EnumOptions\x18\xe8\x9e\xd9\x03 \x01(\x0f:<\n\x0f\x65num_value_opt1\x12!.google.protobuf.EnumValueOptions\x18\xe6\xa0_ \x01(\x05:8\n\x0cservice_opt1\x12\x1f.google.protobuf.ServiceOptions\x18\xa2\xb6\xe1\x03 \x01(\x12:U\n\x0bmethod_opt1\x12\x1e.google.protobuf.MethodOptions\x18\xac\xcf\xe1\x03 \x01(\x0e\x32\x1d.protobuf_unittest.MethodOpt1:4\n\x08\x62ool_opt\x12\x1f.google.protobuf.MessageOptions\x18\xea\xab\xd6\x03 \x01(\x08:5\n\tint32_opt\x12\x1f.google.protobuf.MessageOptions\x18\xed\xa8\xd6\x03 \x01(\x05:5\n\tint64_opt\x12\x1f.google.protobuf.MessageOptions\x18\xc6\xa7\xd6\x03 \x01(\x03:6\n\nuint32_opt\x12\x1f.google.protobuf.MessageOptions\x18\xb0\xa2\xd6\x03 \x01(\r:6\n\nuint64_opt\x12\x1f.google.protobuf.MessageOptions\x18\xdf\x8e\xd6\x03 \x01(\x04:6\n\nsint32_opt\x12\x1f.google.protobuf.MessageOptions\x18\xc0\x88\xd6\x03 \x01(\x11:6\n\nsint64_opt\x12\x1f.google.protobuf.MessageOptions\x18\xff\x82\xd6\x03 \x01(\x12:7\n\x0b\x66ixed32_opt\x12\x1f.google.protobuf.MessageOptions\x18\xd3\xfe\xd5\x03 \x01(\x07:7\n\x0b\x66ixed64_opt\x12\x1f.google.protobuf.MessageOptions\x18\xe2\xfd\xd5\x03 \x01(\x06:8\n\x0csfixed32_opt\x12\x1f.google.protobuf.MessageOptions\x18\xd5\xf1\xd5\x03 \x01(\x0f:8\n\x0csfixed64_opt\x12\x1f.google.protobuf.MessageOptions\x18\xe3\x8a\xd5\x03 \x01(\x10:5\n\tfloat_opt\x12\x1f.google.protobuf.MessageOptions\x18\xfe\xbb\xd4\x03 \x01(\x02:6\n\ndouble_opt\x12\x1f.google.protobuf.MessageOptions\x18\xcd\xab\xd4\x03 \x01(\x01:6\n\nstring_opt\x12\x1f.google.protobuf.MessageOptions\x18\xc5\xab\xd4\x03 \x01(\t:5\n\tbytes_opt\x12\x1f.google.protobuf.MessageOptions\x18\x96\xab\xd4\x03 \x01(\x0c:p\n\x08\x65num_opt\x12\x1f.google.protobuf.MessageOptions\x18\x91\xab\xd4\x03 \x01(\x0e\x32:.protobuf_unittest.DummyMessageContainingEnum.TestEnumType:p\n\x10message_type_opt\x12\x1f.google.protobuf.MessageOptions\x18\xaf\xf2\xd3\x03 \x01(\x0b\x32\x32.protobuf_unittest.DummyMessageInvalidAsOptionType:6\n\x04quux\x12%.protobuf_unittest.ComplexOptionType1\x18\xdb\xe0\xd3\x03 \x01(\x05:^\n\x05\x63orge\x12%.protobuf_unittest.ComplexOptionType1\x18\xd2\xde\xd3\x03 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType3:8\n\x06grault\x12%.protobuf_unittest.ComplexOptionType2\x18\xef\xfc\xd2\x03 \x01(\x05:_\n\x06garply\x12%.protobuf_unittest.ComplexOptionType2\x18\xc8\xf5\xd2\x03 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType1:_\n\x0c\x63omplex_opt1\x12\x1f.google.protobuf.MessageOptions\x18\xa4\xdc\xd2\x03 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType1:_\n\x0c\x63omplex_opt2\x12\x1f.google.protobuf.MessageOptions\x18\xd5\x8f\xd2\x03 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType2:_\n\x0c\x63omplex_opt3\x12\x1f.google.protobuf.MessageOptions\x18\xef\x8b\xd2\x03 \x01(\x0b\x32%.protobuf_unittest.ComplexOptionType3:W\n\x0b\x63omplexopt6\x12\x1f.google.protobuf.MessageOptions\x18\xcc\xcb\xcf\x03 \x01(\n2\x1e.protobuf_unittest.ComplexOpt6:N\n\x07\x66ileopt\x12\x1c.google.protobuf.FileOptions\x18\xcf\xdd\xb0\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:P\n\x06msgopt\x12\x1f.google.protobuf.MessageOptions\x18\x98\xea\xb0\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:P\n\x08\x66ieldopt\x12\x1d.google.protobuf.FieldOptions\x18\x9e\xf4\xb0\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:N\n\x07\x65numopt\x12\x1c.google.protobuf.EnumOptions\x18\xd2\x82\xb1\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:V\n\nenumvalopt\x12!.google.protobuf.EnumValueOptions\x18\xc9\x9f\xb1\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:T\n\nserviceopt\x12\x1f.google.protobuf.ServiceOptions\x18\xb9\xef\xb1\x07 \x01(\x0b\x32\x1c.protobuf_unittest.Aggregate:R\n\tmethodopt\x12\x1e.google.protobuf.MethodOptions\x18\x89\xe9\xb2\x07 \x01(\x0b\x32\x1c.protobuf_unittest.AggregateB\x87\x01\x80\x01\x01\x88\x01\x01\x90\x01\x01\xf0\xe8\xc1\x1d\xea\xad\xc0\xe5$\xfa\xec\x85;p\x08\x64\x12\x0e\x46ileAnnotation\x1a\x16\x12\x14NestedFileAnnotation\"\x1e\xfa\xec\x85;\x19\x12\x17\x46ileExtensionAnnotation*$\x0b\x10\xf6\xeb\xae\x07\x1a\x1b\n\x19\x45mbeddedMessageSetElement\x0c')

_METHODOPT1 = _descriptor.EnumDescriptor(
  name='MethodOpt1',
  full_name='protobuf_unittest.MethodOpt1',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='METHODOPT1_VAL1', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='METHODOPT1_VAL2', index=1, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2569,
  serialized_end=2623,
)

MethodOpt1 = enum_type_wrapper.EnumTypeWrapper(_METHODOPT1)
_AGGREGATEENUM = _descriptor.EnumDescriptor(
  name='AggregateEnum',
  full_name='protobuf_unittest.AggregateEnum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='VALUE', index=0, number=1,
      options=_descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\312\374\211;\025\022\023EnumValueAnnotation'),
      type=None),
  ],
  containing_type=None,
  options=_descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\222\225\210;\020\022\016EnumAnnotation'),
  serialized_start=2625,
  serialized_end=2702,
)

AggregateEnum = enum_type_wrapper.EnumTypeWrapper(_AGGREGATEENUM)
METHODOPT1_VAL1 = 1
METHODOPT1_VAL2 = 2
VALUE = 1

FILE_OPT1_FIELD_NUMBER = 7736974
file_opt1 = _descriptor.FieldDescriptor(
  name='file_opt1', full_name='protobuf_unittest.file_opt1', index=0,
  number=7736974, type=4, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
MESSAGE_OPT1_FIELD_NUMBER = 7739036
message_opt1 = _descriptor.FieldDescriptor(
  name='message_opt1', full_name='protobuf_unittest.message_opt1', index=1,
  number=7739036, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FIELD_OPT1_FIELD_NUMBER = 7740936
field_opt1 = _descriptor.FieldDescriptor(
  name='field_opt1', full_name='protobuf_unittest.field_opt1', index=2,
  number=7740936, type=6, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FIELD_OPT2_FIELD_NUMBER = 7753913
field_opt2 = _descriptor.FieldDescriptor(
  name='field_opt2', full_name='protobuf_unittest.field_opt2', index=3,
  number=7753913, type=5, cpp_type=1, label=1,
  has_default_value=True, default_value=42,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
ENUM_OPT1_FIELD_NUMBER = 7753576
enum_opt1 = _descriptor.FieldDescriptor(
  name='enum_opt1', full_name='protobuf_unittest.enum_opt1', index=4,
  number=7753576, type=15, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
ENUM_VALUE_OPT1_FIELD_NUMBER = 1560678
enum_value_opt1 = _descriptor.FieldDescriptor(
  name='enum_value_opt1', full_name='protobuf_unittest.enum_value_opt1', index=5,
  number=1560678, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SERVICE_OPT1_FIELD_NUMBER = 7887650
service_opt1 = _descriptor.FieldDescriptor(
  name='service_opt1', full_name='protobuf_unittest.service_opt1', index=6,
  number=7887650, type=18, cpp_type=2, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
METHOD_OPT1_FIELD_NUMBER = 7890860
method_opt1 = _descriptor.FieldDescriptor(
  name='method_opt1', full_name='protobuf_unittest.method_opt1', index=7,
  number=7890860, type=14, cpp_type=8, label=1,
  has_default_value=False, default_value=1,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
BOOL_OPT_FIELD_NUMBER = 7706090
bool_opt = _descriptor.FieldDescriptor(
  name='bool_opt', full_name='protobuf_unittest.bool_opt', index=8,
  number=7706090, type=8, cpp_type=7, label=1,
  has_default_value=False, default_value=False,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
INT32_OPT_FIELD_NUMBER = 7705709
int32_opt = _descriptor.FieldDescriptor(
  name='int32_opt', full_name='protobuf_unittest.int32_opt', index=9,
  number=7705709, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
INT64_OPT_FIELD_NUMBER = 7705542
int64_opt = _descriptor.FieldDescriptor(
  name='int64_opt', full_name='protobuf_unittest.int64_opt', index=10,
  number=7705542, type=3, cpp_type=2, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
UINT32_OPT_FIELD_NUMBER = 7704880
uint32_opt = _descriptor.FieldDescriptor(
  name='uint32_opt', full_name='protobuf_unittest.uint32_opt', index=11,
  number=7704880, type=13, cpp_type=3, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
UINT64_OPT_FIELD_NUMBER = 7702367
uint64_opt = _descriptor.FieldDescriptor(
  name='uint64_opt', full_name='protobuf_unittest.uint64_opt', index=12,
  number=7702367, type=4, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SINT32_OPT_FIELD_NUMBER = 7701568
sint32_opt = _descriptor.FieldDescriptor(
  name='sint32_opt', full_name='protobuf_unittest.sint32_opt', index=13,
  number=7701568, type=17, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SINT64_OPT_FIELD_NUMBER = 7700863
sint64_opt = _descriptor.FieldDescriptor(
  name='sint64_opt', full_name='protobuf_unittest.sint64_opt', index=14,
  number=7700863, type=18, cpp_type=2, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FIXED32_OPT_FIELD_NUMBER = 7700307
fixed32_opt = _descriptor.FieldDescriptor(
  name='fixed32_opt', full_name='protobuf_unittest.fixed32_opt', index=15,
  number=7700307, type=7, cpp_type=3, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FIXED64_OPT_FIELD_NUMBER = 7700194
fixed64_opt = _descriptor.FieldDescriptor(
  name='fixed64_opt', full_name='protobuf_unittest.fixed64_opt', index=16,
  number=7700194, type=6, cpp_type=4, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SFIXED32_OPT_FIELD_NUMBER = 7698645
sfixed32_opt = _descriptor.FieldDescriptor(
  name='sfixed32_opt', full_name='protobuf_unittest.sfixed32_opt', index=17,
  number=7698645, type=15, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SFIXED64_OPT_FIELD_NUMBER = 7685475
sfixed64_opt = _descriptor.FieldDescriptor(
  name='sfixed64_opt', full_name='protobuf_unittest.sfixed64_opt', index=18,
  number=7685475, type=16, cpp_type=2, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FLOAT_OPT_FIELD_NUMBER = 7675390
float_opt = _descriptor.FieldDescriptor(
  name='float_opt', full_name='protobuf_unittest.float_opt', index=19,
  number=7675390, type=2, cpp_type=6, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
DOUBLE_OPT_FIELD_NUMBER = 7673293
double_opt = _descriptor.FieldDescriptor(
  name='double_opt', full_name='protobuf_unittest.double_opt', index=20,
  number=7673293, type=1, cpp_type=5, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
STRING_OPT_FIELD_NUMBER = 7673285
string_opt = _descriptor.FieldDescriptor(
  name='string_opt', full_name='protobuf_unittest.string_opt', index=21,
  number=7673285, type=9, cpp_type=9, label=1,
  has_default_value=False, default_value=unicode("", "utf-8"),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
BYTES_OPT_FIELD_NUMBER = 7673238
bytes_opt = _descriptor.FieldDescriptor(
  name='bytes_opt', full_name='protobuf_unittest.bytes_opt', index=22,
  number=7673238, type=12, cpp_type=9, label=1,
  has_default_value=False, default_value="",
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
ENUM_OPT_FIELD_NUMBER = 7673233
enum_opt = _descriptor.FieldDescriptor(
  name='enum_opt', full_name='protobuf_unittest.enum_opt', index=23,
  number=7673233, type=14, cpp_type=8, label=1,
  has_default_value=False, default_value=22,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
MESSAGE_TYPE_OPT_FIELD_NUMBER = 7665967
message_type_opt = _descriptor.FieldDescriptor(
  name='message_type_opt', full_name='protobuf_unittest.message_type_opt', index=24,
  number=7665967, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
QUUX_FIELD_NUMBER = 7663707
quux = _descriptor.FieldDescriptor(
  name='quux', full_name='protobuf_unittest.quux', index=25,
  number=7663707, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
CORGE_FIELD_NUMBER = 7663442
corge = _descriptor.FieldDescriptor(
  name='corge', full_name='protobuf_unittest.corge', index=26,
  number=7663442, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
GRAULT_FIELD_NUMBER = 7650927
grault = _descriptor.FieldDescriptor(
  name='grault', full_name='protobuf_unittest.grault', index=27,
  number=7650927, type=5, cpp_type=1, label=1,
  has_default_value=False, default_value=0,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
GARPLY_FIELD_NUMBER = 7649992
garply = _descriptor.FieldDescriptor(
  name='garply', full_name='protobuf_unittest.garply', index=28,
  number=7649992, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
COMPLEX_OPT1_FIELD_NUMBER = 7646756
complex_opt1 = _descriptor.FieldDescriptor(
  name='complex_opt1', full_name='protobuf_unittest.complex_opt1', index=29,
  number=7646756, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
COMPLEX_OPT2_FIELD_NUMBER = 7636949
complex_opt2 = _descriptor.FieldDescriptor(
  name='complex_opt2', full_name='protobuf_unittest.complex_opt2', index=30,
  number=7636949, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
COMPLEX_OPT3_FIELD_NUMBER = 7636463
complex_opt3 = _descriptor.FieldDescriptor(
  name='complex_opt3', full_name='protobuf_unittest.complex_opt3', index=31,
  number=7636463, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
COMPLEXOPT6_FIELD_NUMBER = 7595468
complexopt6 = _descriptor.FieldDescriptor(
  name='complexopt6', full_name='protobuf_unittest.complexopt6', index=32,
  number=7595468, type=10, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FILEOPT_FIELD_NUMBER = 15478479
fileopt = _descriptor.FieldDescriptor(
  name='fileopt', full_name='protobuf_unittest.fileopt', index=33,
  number=15478479, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
MSGOPT_FIELD_NUMBER = 15480088
msgopt = _descriptor.FieldDescriptor(
  name='msgopt', full_name='protobuf_unittest.msgopt', index=34,
  number=15480088, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
FIELDOPT_FIELD_NUMBER = 15481374
fieldopt = _descriptor.FieldDescriptor(
  name='fieldopt', full_name='protobuf_unittest.fieldopt', index=35,
  number=15481374, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
ENUMOPT_FIELD_NUMBER = 15483218
enumopt = _descriptor.FieldDescriptor(
  name='enumopt', full_name='protobuf_unittest.enumopt', index=36,
  number=15483218, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
ENUMVALOPT_FIELD_NUMBER = 15486921
enumvalopt = _descriptor.FieldDescriptor(
  name='enumvalopt', full_name='protobuf_unittest.enumvalopt', index=37,
  number=15486921, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SERVICEOPT_FIELD_NUMBER = 15497145
serviceopt = _descriptor.FieldDescriptor(
  name='serviceopt', full_name='protobuf_unittest.serviceopt', index=38,
  number=15497145, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
METHODOPT_FIELD_NUMBER = 15512713
methodopt = _descriptor.FieldDescriptor(
  name='methodopt', full_name='protobuf_unittest.methodopt', index=39,
  number=15512713, type=11, cpp_type=10, label=1,
  has_default_value=False, default_value=None,
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)

_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM = _descriptor.EnumDescriptor(
  name='AnEnum',
  full_name='protobuf_unittest.TestMessageWithCustomOptions.AnEnum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ANENUM_VAL1', index=0, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='ANENUM_VAL2', index=1, number=2,
      options=_descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\260\206\372\005{'),
      type=None),
  ],
  containing_type=None,
  options=_descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\305\366\311\035\353\374\377\377'),
  serialized_start=167,
  serialized_end=226,
)

_DUMMYMESSAGECONTAININGENUM_TESTENUMTYPE = _descriptor.EnumDescriptor(
  name='TestEnumType',
  full_name='protobuf_unittest.DummyMessageContainingEnum.TestEnumType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TEST_OPTION_ENUM_TYPE1', index=0, number=22,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='TEST_OPTION_ENUM_TYPE2', index=1, number=-23,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=393,
  serialized_end=472,
)

_NESTEDOPTIONTYPE_NESTEDENUM = _descriptor.EnumDescriptor(
  name='NestedEnum',
  full_name='protobuf_unittest.NestedOptionType.NestedEnum',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NESTED_ENUM_VALUE', index=0, number=1,
      options=_descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\260\206\372\005\354\007'),
      type=None),
  ],
  containing_type=None,
  options=_descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\305\366\311\035\353\003\000\000'),
  serialized_start=2447,
  serialized_end=2500,
)


_TESTMESSAGEWITHCUSTOMOPTIONS = _descriptor.Descriptor(
  name='TestMessageWithCustomOptions',
  full_name='protobuf_unittest.TestMessageWithCustomOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='field1', full_name='protobuf_unittest.TestMessageWithCustomOptions.field1', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=unicode("", "utf-8"),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\010\001\301\340\303\035-\341u\n\002\000\000\000')),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM,
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\010\000\340\351\302\035\310\377\377\377\377\377\377\377\377\001'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=103,
  serialized_end=244,
)


_CUSTOMOPTIONFOOREQUEST = _descriptor.Descriptor(
  name='CustomOptionFooRequest',
  full_name='protobuf_unittest.CustomOptionFooRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=246,
  serialized_end=270,
)


_CUSTOMOPTIONFOORESPONSE = _descriptor.Descriptor(
  name='CustomOptionFooResponse',
  full_name='protobuf_unittest.CustomOptionFooResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=272,
  serialized_end=297,
)


_CUSTOMOPTIONFOOCLIENTMESSAGE = _descriptor.Descriptor(
  name='CustomOptionFooClientMessage',
  full_name='protobuf_unittest.CustomOptionFooClientMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=299,
  serialized_end=329,
)


_CUSTOMOPTIONFOOSERVERMESSAGE = _descriptor.Descriptor(
  name='CustomOptionFooServerMessage',
  full_name='protobuf_unittest.CustomOptionFooServerMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=331,
  serialized_end=361,
)


_DUMMYMESSAGECONTAININGENUM = _descriptor.Descriptor(
  name='DummyMessageContainingEnum',
  full_name='protobuf_unittest.DummyMessageContainingEnum',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _DUMMYMESSAGECONTAININGENUM_TESTENUMTYPE,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=363,
  serialized_end=472,
)


_DUMMYMESSAGEINVALIDASOPTIONTYPE = _descriptor.Descriptor(
  name='DummyMessageInvalidAsOptionType',
  full_name='protobuf_unittest.DummyMessageInvalidAsOptionType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=474,
  serialized_end=507,
)


_CUSTOMOPTIONMININTEGERVALUES = _descriptor.Descriptor(
  name='CustomOptionMinIntegerValues',
  full_name='protobuf_unittest.CustomOptionMinIntegerValues',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\320\336\262\035\000\350\306\262\035\200\200\200\200\370\377\377\377\377\001\260\274\262\035\200\200\200\200\200\200\200\200\200\001\200\223\262\035\000\370\365\260\035\000\200\304\260\035\377\377\377\377\017\370\227\260\035\377\377\377\377\377\377\377\377\377\001\235\365\257\035\000\000\000\000\221\356\257\035\000\000\000\000\000\000\000\000\255\215\257\035\000\000\000\200\231\326\250\035\000\000\000\000\000\000\000\200'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=510,
  serialized_end=648,
)


_CUSTOMOPTIONMAXINTEGERVALUES = _descriptor.Descriptor(
  name='CustomOptionMaxIntegerValues',
  full_name='protobuf_unittest.CustomOptionMaxIntegerValues',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\320\336\262\035\001\350\306\262\035\377\377\377\377\007\260\274\262\035\377\377\377\377\377\377\377\377\177\200\223\262\035\377\377\377\377\017\370\365\260\035\377\377\377\377\377\377\377\377\377\001\200\304\260\035\376\377\377\377\017\370\227\260\035\376\377\377\377\377\377\377\377\377\001\235\365\257\035\377\377\377\377\221\356\257\035\377\377\377\377\377\377\377\377\255\215\257\035\377\377\377\177\231\326\250\035\377\377\377\377\377\377\377\177'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=651,
  serialized_end=796,
)


_CUSTOMOPTIONOTHERVALUES = _descriptor.Descriptor(
  name='CustomOptionOtherValues',
  full_name='protobuf_unittest.CustomOptionOtherValues',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\350\306\262\035\234\377\377\377\377\377\377\377\377\001\365\337\243\035\347\207EA\351\334\242\035\373Y\214B\312\300\363?\252\334\242\035\016Hello, \"World\"\262\331\242\035\013Hello\000World\210\331\242\035\351\377\377\377\377\377\377\377\377\001'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=798,
  serialized_end=908,
)


_SETTINGREALSFROMPOSITIVEINTS = _descriptor.Descriptor(
  name='SettingRealsFromPositiveInts',
  full_name='protobuf_unittest.SettingRealsFromPositiveInts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\365\337\243\035\000\000@A\351\334\242\035\000\000\000\000\000@c@'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=910,
  serialized_end=962,
)


_SETTINGREALSFROMNEGATIVEINTS = _descriptor.Descriptor(
  name='SettingRealsFromNegativeInts',
  full_name='protobuf_unittest.SettingRealsFromNegativeInts',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\365\337\243\035\000\000@\301\351\334\242\035\000\000\000\000\000@c\300'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=964,
  serialized_end=1016,
)


_COMPLEXOPTIONTYPE1 = _descriptor.Descriptor(
  name='ComplexOptionType1',
  full_name='protobuf_unittest.ComplexOptionType1',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='foo', full_name='protobuf_unittest.ComplexOptionType1.foo', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='foo2', full_name='protobuf_unittest.ComplexOptionType1.foo2', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='foo3', full_name='protobuf_unittest.ComplexOptionType1.foo3', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=True,
  extension_ranges=[(100, 536870912), ],
  serialized_start=1018,
  serialized_end=1089,
)


_COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4 = _descriptor.Descriptor(
  name='ComplexOptionType4',
  full_name='protobuf_unittest.ComplexOptionType2.ComplexOptionType4',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='waldo', full_name='protobuf_unittest.ComplexOptionType2.ComplexOptionType4.waldo', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
    _descriptor.FieldDescriptor(
      name='complex_opt4', full_name='protobuf_unittest.ComplexOptionType2.ComplexOptionType4.complex_opt4', index=0,
      number=7633546, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=True, extension_scope=None,
      options=None),
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1252,
  serialized_end=1403,
)

_COMPLEXOPTIONTYPE2 = _descriptor.Descriptor(
  name='ComplexOptionType2',
  full_name='protobuf_unittest.ComplexOptionType2',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='bar', full_name='protobuf_unittest.ComplexOptionType2.bar', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='baz', full_name='protobuf_unittest.ComplexOptionType2.baz', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='fred', full_name='protobuf_unittest.ComplexOptionType2.fred', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4, ],
  enum_types=[
  ],
  options=None,
  is_extendable=True,
  extension_ranges=[(100, 536870912), ],
  serialized_start=1092,
  serialized_end=1413,
)


_COMPLEXOPTIONTYPE3_COMPLEXOPTIONTYPE5 = _descriptor.Descriptor(
  name='ComplexOptionType5',
  full_name='protobuf_unittest.ComplexOptionType3.ComplexOptionType5',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='plugh', full_name='protobuf_unittest.ComplexOptionType3.ComplexOptionType5.plugh', index=0,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1537,
  serialized_end=1572,
)

_COMPLEXOPTIONTYPE3 = _descriptor.Descriptor(
  name='ComplexOptionType3',
  full_name='protobuf_unittest.ComplexOptionType3',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='qux', full_name='protobuf_unittest.ComplexOptionType3.qux', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='complexoptiontype5', full_name='protobuf_unittest.ComplexOptionType3.complexoptiontype5', index=1,
      number=2, type=10, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_COMPLEXOPTIONTYPE3_COMPLEXOPTIONTYPE5, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1416,
  serialized_end=1572,
)


_COMPLEXOPT6 = _descriptor.Descriptor(
  name='ComplexOpt6',
  full_name='protobuf_unittest.ComplexOpt6',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='xyzzy', full_name='protobuf_unittest.ComplexOpt6.xyzzy', index=0,
      number=7593951, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1574,
  serialized_end=1605,
)


_VARIOUSCOMPLEXOPTIONS = _descriptor.Descriptor(
  name='VariousComplexOptions',
  full_name='protobuf_unittest.VariousComplexOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\242\342\225\035\002\010*\242\342\225\035\006\330\205\236\035\304\002\242\342\225\035\010\222\365\235\035\003\010\354\006\252\375\220\035\003\020\333\007\252\375\220\035\006\370\346\227\035\216\005\252\375\220\035\005\n\003\010\347\005\252\375\220\035\010\n\006\330\205\236\035\317\017\252\375\220\035\n\n\010\222\365\235\035\003\010\330\017\252\375\220\035\010\302\254\227\035\003\010\345\005\252\375\220\035\013\302\254\227\035\006\330\205\236\035\316\017\252\375\220\035\r\302\254\227\035\010\222\365\235\035\003\010\311\020\322\250\217\035\003\010\263\017\252\375\220\035\005\032\003\010\301\002\372\336\220\035\002\010\t\372\336\220\035\004\023\030\026\024\343\334\374\034\370\375\373\034\030\344\334\374\034'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1608,
  serialized_end=1816,
)


_AGGREGATEMESSAGESET = _descriptor.Descriptor(
  name='AggregateMessageSet',
  full_name='protobuf_unittest.AggregateMessageSet',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\010\001'),
  is_extendable=True,
  extension_ranges=[(4, 2147483647), ],
  serialized_start=1818,
  serialized_end=1853,
)


_AGGREGATEMESSAGESETELEMENT = _descriptor.Descriptor(
  name='AggregateMessageSetElement',
  full_name='protobuf_unittest.AggregateMessageSetElement',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='s', full_name='protobuf_unittest.AggregateMessageSetElement.s', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=unicode("", "utf-8"),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
    _descriptor.FieldDescriptor(
      name='message_set_extension', full_name='protobuf_unittest.AggregateMessageSetElement.message_set_extension', index=0,
      number=15447542, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=True, extension_scope=None,
      options=None),
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=1856,
  serialized_end=2016,
)


_AGGREGATE = _descriptor.Descriptor(
  name='Aggregate',
  full_name='protobuf_unittest.Aggregate',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='i', full_name='protobuf_unittest.Aggregate.i', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='s', full_name='protobuf_unittest.Aggregate.s', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=unicode("", "utf-8"),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sub', full_name='protobuf_unittest.Aggregate.sub', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='file', full_name='protobuf_unittest.Aggregate.file', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='mset', full_name='protobuf_unittest.Aggregate.mset', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
    _descriptor.FieldDescriptor(
      name='nested', full_name='protobuf_unittest.Aggregate.nested', index=0,
      number=15476903, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=True, extension_scope=None,
      options=None),
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=2019,
  serialized_end=2272,
)


_AGGREGATEMESSAGE = _descriptor.Descriptor(
  name='AggregateMessage',
  full_name='protobuf_unittest.AggregateMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='fieldname', full_name='protobuf_unittest.AggregateMessage.fieldname', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\362\241\207;\021\022\017FieldAnnotation')),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\302\321\206;\025\010e\022\021MessageAnnotation'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=2274,
  serialized_end=2363,
)


_NESTEDOPTIONTYPE_NESTEDMESSAGE = _descriptor.Descriptor(
  name='NestedMessage',
  full_name='protobuf_unittest.NestedOptionType.NestedMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='nested_field', full_name='protobuf_unittest.NestedOptionType.NestedMessage.nested_field', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\301\340\303\035\352\003\000\000\000\000\000\000')),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=_descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\340\351\302\035\351\007'),
  is_extendable=False,
  extension_ranges=[],
  serialized_start=2386,
  serialized_end=2445,
)

_NESTEDOPTIONTYPE = _descriptor.Descriptor(
  name='NestedOptionType',
  full_name='protobuf_unittest.NestedOptionType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
    _descriptor.FieldDescriptor(
      name='nested_extension', full_name='protobuf_unittest.NestedOptionType.nested_extension', index=0,
      number=7912573, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=True, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\310\213\312\035\355\007')),
  ],
  nested_types=[_NESTEDOPTIONTYPE_NESTEDMESSAGE, ],
  enum_types=[
    _NESTEDOPTIONTYPE_NESTEDENUM,
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=2366,
  serialized_end=2567,
)

_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM.containing_type = _TESTMESSAGEWITHCUSTOMOPTIONS;
_DUMMYMESSAGECONTAININGENUM_TESTENUMTYPE.containing_type = _DUMMYMESSAGECONTAININGENUM;
_COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4.containing_type = _COMPLEXOPTIONTYPE2;
_COMPLEXOPTIONTYPE2.fields_by_name['bar'].message_type = _COMPLEXOPTIONTYPE1
_COMPLEXOPTIONTYPE2.fields_by_name['fred'].message_type = _COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4
_COMPLEXOPTIONTYPE3_COMPLEXOPTIONTYPE5.containing_type = _COMPLEXOPTIONTYPE3;
_COMPLEXOPTIONTYPE3.fields_by_name['complexoptiontype5'].message_type = _COMPLEXOPTIONTYPE3_COMPLEXOPTIONTYPE5
_AGGREGATE.fields_by_name['sub'].message_type = _AGGREGATE
_AGGREGATE.fields_by_name['file'].message_type = google.protobuf.descriptor_pb2._FILEOPTIONS
_AGGREGATE.fields_by_name['mset'].message_type = _AGGREGATEMESSAGESET
_NESTEDOPTIONTYPE_NESTEDMESSAGE.containing_type = _NESTEDOPTIONTYPE;
_NESTEDOPTIONTYPE_NESTEDENUM.containing_type = _NESTEDOPTIONTYPE;
DESCRIPTOR.message_types_by_name['TestMessageWithCustomOptions'] = _TESTMESSAGEWITHCUSTOMOPTIONS
DESCRIPTOR.message_types_by_name['CustomOptionFooRequest'] = _CUSTOMOPTIONFOOREQUEST
DESCRIPTOR.message_types_by_name['CustomOptionFooResponse'] = _CUSTOMOPTIONFOORESPONSE
DESCRIPTOR.message_types_by_name['CustomOptionFooClientMessage'] = _CUSTOMOPTIONFOOCLIENTMESSAGE
DESCRIPTOR.message_types_by_name['CustomOptionFooServerMessage'] = _CUSTOMOPTIONFOOSERVERMESSAGE
DESCRIPTOR.message_types_by_name['DummyMessageContainingEnum'] = _DUMMYMESSAGECONTAININGENUM
DESCRIPTOR.message_types_by_name['DummyMessageInvalidAsOptionType'] = _DUMMYMESSAGEINVALIDASOPTIONTYPE
DESCRIPTOR.message_types_by_name['CustomOptionMinIntegerValues'] = _CUSTOMOPTIONMININTEGERVALUES
DESCRIPTOR.message_types_by_name['CustomOptionMaxIntegerValues'] = _CUSTOMOPTIONMAXINTEGERVALUES
DESCRIPTOR.message_types_by_name['CustomOptionOtherValues'] = _CUSTOMOPTIONOTHERVALUES
DESCRIPTOR.message_types_by_name['SettingRealsFromPositiveInts'] = _SETTINGREALSFROMPOSITIVEINTS
DESCRIPTOR.message_types_by_name['SettingRealsFromNegativeInts'] = _SETTINGREALSFROMNEGATIVEINTS
DESCRIPTOR.message_types_by_name['ComplexOptionType1'] = _COMPLEXOPTIONTYPE1
DESCRIPTOR.message_types_by_name['ComplexOptionType2'] = _COMPLEXOPTIONTYPE2
DESCRIPTOR.message_types_by_name['ComplexOptionType3'] = _COMPLEXOPTIONTYPE3
DESCRIPTOR.message_types_by_name['ComplexOpt6'] = _COMPLEXOPT6
DESCRIPTOR.message_types_by_name['VariousComplexOptions'] = _VARIOUSCOMPLEXOPTIONS
DESCRIPTOR.message_types_by_name['AggregateMessageSet'] = _AGGREGATEMESSAGESET
DESCRIPTOR.message_types_by_name['AggregateMessageSetElement'] = _AGGREGATEMESSAGESETELEMENT
DESCRIPTOR.message_types_by_name['Aggregate'] = _AGGREGATE
DESCRIPTOR.message_types_by_name['AggregateMessage'] = _AGGREGATEMESSAGE
DESCRIPTOR.message_types_by_name['NestedOptionType'] = _NESTEDOPTIONTYPE

class TestMessageWithCustomOptions(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _TESTMESSAGEWITHCUSTOMOPTIONS

  # @@protoc_insertion_point(class_scope:protobuf_unittest.TestMessageWithCustomOptions)

class CustomOptionFooRequest(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONFOOREQUEST

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooRequest)

class CustomOptionFooResponse(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONFOORESPONSE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooResponse)

class CustomOptionFooClientMessage(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONFOOCLIENTMESSAGE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooClientMessage)

class CustomOptionFooServerMessage(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONFOOSERVERMESSAGE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionFooServerMessage)

class DummyMessageContainingEnum(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _DUMMYMESSAGECONTAININGENUM

  # @@protoc_insertion_point(class_scope:protobuf_unittest.DummyMessageContainingEnum)

class DummyMessageInvalidAsOptionType(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _DUMMYMESSAGEINVALIDASOPTIONTYPE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.DummyMessageInvalidAsOptionType)

class CustomOptionMinIntegerValues(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONMININTEGERVALUES

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionMinIntegerValues)

class CustomOptionMaxIntegerValues(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONMAXINTEGERVALUES

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionMaxIntegerValues)

class CustomOptionOtherValues(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _CUSTOMOPTIONOTHERVALUES

  # @@protoc_insertion_point(class_scope:protobuf_unittest.CustomOptionOtherValues)

class SettingRealsFromPositiveInts(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _SETTINGREALSFROMPOSITIVEINTS

  # @@protoc_insertion_point(class_scope:protobuf_unittest.SettingRealsFromPositiveInts)

class SettingRealsFromNegativeInts(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _SETTINGREALSFROMNEGATIVEINTS

  # @@protoc_insertion_point(class_scope:protobuf_unittest.SettingRealsFromNegativeInts)

class ComplexOptionType1(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _COMPLEXOPTIONTYPE1

  # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType1)

class ComplexOptionType2(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType

  class ComplexOptionType4(_message.Message):
    __metaclass__ = _reflection.GeneratedProtocolMessageType
    DESCRIPTOR = _COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4

    # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType2.ComplexOptionType4)
  DESCRIPTOR = _COMPLEXOPTIONTYPE2

  # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType2)

class ComplexOptionType3(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType

  class ComplexOptionType5(_message.Message):
    __metaclass__ = _reflection.GeneratedProtocolMessageType
    DESCRIPTOR = _COMPLEXOPTIONTYPE3_COMPLEXOPTIONTYPE5

    # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType3.ComplexOptionType5)
  DESCRIPTOR = _COMPLEXOPTIONTYPE3

  # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOptionType3)

class ComplexOpt6(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _COMPLEXOPT6

  # @@protoc_insertion_point(class_scope:protobuf_unittest.ComplexOpt6)

class VariousComplexOptions(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _VARIOUSCOMPLEXOPTIONS

  # @@protoc_insertion_point(class_scope:protobuf_unittest.VariousComplexOptions)

class AggregateMessageSet(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _AGGREGATEMESSAGESET

  # @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessageSet)

class AggregateMessageSetElement(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _AGGREGATEMESSAGESETELEMENT

  # @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessageSetElement)

class Aggregate(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _AGGREGATE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.Aggregate)

class AggregateMessage(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _AGGREGATEMESSAGE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.AggregateMessage)

class NestedOptionType(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType

  class NestedMessage(_message.Message):
    __metaclass__ = _reflection.GeneratedProtocolMessageType
    DESCRIPTOR = _NESTEDOPTIONTYPE_NESTEDMESSAGE

    # @@protoc_insertion_point(class_scope:protobuf_unittest.NestedOptionType.NestedMessage)
  DESCRIPTOR = _NESTEDOPTIONTYPE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.NestedOptionType)

google.protobuf.descriptor_pb2.FileOptions.RegisterExtension(file_opt1)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(message_opt1)
google.protobuf.descriptor_pb2.FieldOptions.RegisterExtension(field_opt1)
google.protobuf.descriptor_pb2.FieldOptions.RegisterExtension(field_opt2)
google.protobuf.descriptor_pb2.EnumOptions.RegisterExtension(enum_opt1)
google.protobuf.descriptor_pb2.EnumValueOptions.RegisterExtension(enum_value_opt1)
google.protobuf.descriptor_pb2.ServiceOptions.RegisterExtension(service_opt1)
method_opt1.enum_type = _METHODOPT1
google.protobuf.descriptor_pb2.MethodOptions.RegisterExtension(method_opt1)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(bool_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(int32_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(int64_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(uint32_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(uint64_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(sint32_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(sint64_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(fixed32_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(fixed64_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(sfixed32_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(sfixed64_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(float_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(double_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(string_opt)
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(bytes_opt)
enum_opt.enum_type = _DUMMYMESSAGECONTAININGENUM_TESTENUMTYPE
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(enum_opt)
message_type_opt.message_type = _DUMMYMESSAGEINVALIDASOPTIONTYPE
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(message_type_opt)
ComplexOptionType1.RegisterExtension(quux)
corge.message_type = _COMPLEXOPTIONTYPE3
ComplexOptionType1.RegisterExtension(corge)
ComplexOptionType2.RegisterExtension(grault)
garply.message_type = _COMPLEXOPTIONTYPE1
ComplexOptionType2.RegisterExtension(garply)
complex_opt1.message_type = _COMPLEXOPTIONTYPE1
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(complex_opt1)
complex_opt2.message_type = _COMPLEXOPTIONTYPE2
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(complex_opt2)
complex_opt3.message_type = _COMPLEXOPTIONTYPE3
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(complex_opt3)
complexopt6.message_type = _COMPLEXOPT6
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(complexopt6)
fileopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.FileOptions.RegisterExtension(fileopt)
msgopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(msgopt)
fieldopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.FieldOptions.RegisterExtension(fieldopt)
enumopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.EnumOptions.RegisterExtension(enumopt)
enumvalopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.EnumValueOptions.RegisterExtension(enumvalopt)
serviceopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.ServiceOptions.RegisterExtension(serviceopt)
methodopt.message_type = _AGGREGATE
google.protobuf.descriptor_pb2.MethodOptions.RegisterExtension(methodopt)
_COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4.extensions_by_name['complex_opt4'].message_type = _COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4
google.protobuf.descriptor_pb2.MessageOptions.RegisterExtension(_COMPLEXOPTIONTYPE2_COMPLEXOPTIONTYPE4.extensions_by_name['complex_opt4'])
_AGGREGATEMESSAGESETELEMENT.extensions_by_name['message_set_extension'].message_type = _AGGREGATEMESSAGESETELEMENT
AggregateMessageSet.RegisterExtension(_AGGREGATEMESSAGESETELEMENT.extensions_by_name['message_set_extension'])
_AGGREGATE.extensions_by_name['nested'].message_type = _AGGREGATE
google.protobuf.descriptor_pb2.FileOptions.RegisterExtension(_AGGREGATE.extensions_by_name['nested'])
google.protobuf.descriptor_pb2.FileOptions.RegisterExtension(_NESTEDOPTIONTYPE.extensions_by_name['nested_extension'])

DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), '\200\001\001\210\001\001\220\001\001\360\350\301\035\352\255\300\345$\372\354\205;p\010d\022\016FileAnnotation\032\026\022\024NestedFileAnnotation\"\036\372\354\205;\031\022\027FileExtensionAnnotation*$\013\020\366\353\256\007\032\033\n\031EmbeddedMessageSetElement\014')
_AGGREGATEENUM.has_options = True
_AGGREGATEENUM._options = _descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\222\225\210;\020\022\016EnumAnnotation')
_AGGREGATEENUM.values_by_name["VALUE"].has_options = True
_AGGREGATEENUM.values_by_name["VALUE"]._options = _descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\312\374\211;\025\022\023EnumValueAnnotation')
_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM.has_options = True
_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM._options = _descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\305\366\311\035\353\374\377\377')
_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM.values_by_name["ANENUM_VAL2"].has_options = True
_TESTMESSAGEWITHCUSTOMOPTIONS_ANENUM.values_by_name["ANENUM_VAL2"]._options = _descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\260\206\372\005{')
_TESTMESSAGEWITHCUSTOMOPTIONS.fields_by_name['field1'].has_options = True
_TESTMESSAGEWITHCUSTOMOPTIONS.fields_by_name['field1']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\010\001\301\340\303\035-\341u\n\002\000\000\000')
_TESTMESSAGEWITHCUSTOMOPTIONS.has_options = True
_TESTMESSAGEWITHCUSTOMOPTIONS._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\010\000\340\351\302\035\310\377\377\377\377\377\377\377\377\001')
_CUSTOMOPTIONMININTEGERVALUES.has_options = True
_CUSTOMOPTIONMININTEGERVALUES._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\320\336\262\035\000\350\306\262\035\200\200\200\200\370\377\377\377\377\001\260\274\262\035\200\200\200\200\200\200\200\200\200\001\200\223\262\035\000\370\365\260\035\000\200\304\260\035\377\377\377\377\017\370\227\260\035\377\377\377\377\377\377\377\377\377\001\235\365\257\035\000\000\000\000\221\356\257\035\000\000\000\000\000\000\000\000\255\215\257\035\000\000\000\200\231\326\250\035\000\000\000\000\000\000\000\200')
_CUSTOMOPTIONMAXINTEGERVALUES.has_options = True
_CUSTOMOPTIONMAXINTEGERVALUES._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\320\336\262\035\001\350\306\262\035\377\377\377\377\007\260\274\262\035\377\377\377\377\377\377\377\377\177\200\223\262\035\377\377\377\377\017\370\365\260\035\377\377\377\377\377\377\377\377\377\001\200\304\260\035\376\377\377\377\017\370\227\260\035\376\377\377\377\377\377\377\377\377\001\235\365\257\035\377\377\377\377\221\356\257\035\377\377\377\377\377\377\377\377\255\215\257\035\377\377\377\177\231\326\250\035\377\377\377\377\377\377\377\177')
_CUSTOMOPTIONOTHERVALUES.has_options = True
_CUSTOMOPTIONOTHERVALUES._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\350\306\262\035\234\377\377\377\377\377\377\377\377\001\365\337\243\035\347\207EA\351\334\242\035\373Y\214B\312\300\363?\252\334\242\035\016Hello, \"World\"\262\331\242\035\013Hello\000World\210\331\242\035\351\377\377\377\377\377\377\377\377\001')
_SETTINGREALSFROMPOSITIVEINTS.has_options = True
_SETTINGREALSFROMPOSITIVEINTS._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\365\337\243\035\000\000@A\351\334\242\035\000\000\000\000\000@c@')
_SETTINGREALSFROMNEGATIVEINTS.has_options = True
_SETTINGREALSFROMNEGATIVEINTS._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\365\337\243\035\000\000@\301\351\334\242\035\000\000\000\000\000@c\300')
_VARIOUSCOMPLEXOPTIONS.has_options = True
_VARIOUSCOMPLEXOPTIONS._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\242\342\225\035\002\010*\242\342\225\035\006\330\205\236\035\304\002\242\342\225\035\010\222\365\235\035\003\010\354\006\252\375\220\035\003\020\333\007\252\375\220\035\006\370\346\227\035\216\005\252\375\220\035\005\n\003\010\347\005\252\375\220\035\010\n\006\330\205\236\035\317\017\252\375\220\035\n\n\010\222\365\235\035\003\010\330\017\252\375\220\035\010\302\254\227\035\003\010\345\005\252\375\220\035\013\302\254\227\035\006\330\205\236\035\316\017\252\375\220\035\r\302\254\227\035\010\222\365\235\035\003\010\311\020\322\250\217\035\003\010\263\017\252\375\220\035\005\032\003\010\301\002\372\336\220\035\002\010\t\372\336\220\035\004\023\030\026\024\343\334\374\034\370\375\373\034\030\344\334\374\034')
_AGGREGATEMESSAGESET.has_options = True
_AGGREGATEMESSAGESET._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\010\001')
_AGGREGATEMESSAGE.fields_by_name['fieldname'].has_options = True
_AGGREGATEMESSAGE.fields_by_name['fieldname']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\362\241\207;\021\022\017FieldAnnotation')
_AGGREGATEMESSAGE.has_options = True
_AGGREGATEMESSAGE._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\302\321\206;\025\010e\022\021MessageAnnotation')
_NESTEDOPTIONTYPE_NESTEDMESSAGE.fields_by_name['nested_field'].has_options = True
_NESTEDOPTIONTYPE_NESTEDMESSAGE.fields_by_name['nested_field']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\301\340\303\035\352\003\000\000\000\000\000\000')
_NESTEDOPTIONTYPE_NESTEDMESSAGE.has_options = True
_NESTEDOPTIONTYPE_NESTEDMESSAGE._options = _descriptor._ParseOptions(descriptor_pb2.MessageOptions(), '\340\351\302\035\351\007')
_NESTEDOPTIONTYPE_NESTEDENUM.has_options = True
_NESTEDOPTIONTYPE_NESTEDENUM._options = _descriptor._ParseOptions(descriptor_pb2.EnumOptions(), '\305\366\311\035\353\003\000\000')
_NESTEDOPTIONTYPE_NESTEDENUM.values_by_name["NESTED_ENUM_VALUE"].has_options = True
_NESTEDOPTIONTYPE_NESTEDENUM.values_by_name["NESTED_ENUM_VALUE"]._options = _descriptor._ParseOptions(descriptor_pb2.EnumValueOptions(), '\260\206\372\005\354\007')
_NESTEDOPTIONTYPE.extensions_by_name['nested_extension'].has_options = True
_NESTEDOPTIONTYPE.extensions_by_name['nested_extension']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), '\310\213\312\035\355\007')

_TESTSERVICEWITHCUSTOMOPTIONS = _descriptor.ServiceDescriptor(
  name='TestServiceWithCustomOptions',
  full_name='protobuf_unittest.TestServiceWithCustomOptions',
  file=DESCRIPTOR,
  index=0,
  options=_descriptor._ParseOptions(descriptor_pb2.ServiceOptions(), '\220\262\213\036\323\333\200\313I'),
  serialized_start=2705,
  serialized_end=2847,
  methods=[
  _descriptor.MethodDescriptor(
    name='Foo',
    full_name='protobuf_unittest.TestServiceWithCustomOptions.Foo',
    index=0,
    containing_service=None,
    input_type=_CUSTOMOPTIONFOOREQUEST,
    output_type=_CUSTOMOPTIONFOORESPONSE,
    options=_descriptor._ParseOptions(descriptor_pb2.MethodOptions(), '\340\372\214\036\002'),
  ),
])

class TestServiceWithCustomOptions(_service.Service):
  __metaclass__ = service_reflection.GeneratedServiceType
  DESCRIPTOR = _TESTSERVICEWITHCUSTOMOPTIONS
class TestServiceWithCustomOptions_Stub(TestServiceWithCustomOptions):
  __metaclass__ = service_reflection.GeneratedServiceStubType
  DESCRIPTOR = _TESTSERVICEWITHCUSTOMOPTIONS


_AGGREGATESERVICE = _descriptor.ServiceDescriptor(
  name='AggregateService',
  full_name='protobuf_unittest.AggregateService',
  file=DESCRIPTOR,
  index=1,
  options=_descriptor._ParseOptions(descriptor_pb2.ServiceOptions(), '\312\373\216;\023\022\021ServiceAnnotation'),
  serialized_start=2850,
  serialized_end=3003,
  methods=[
  _descriptor.MethodDescriptor(
    name='Method',
    full_name='protobuf_unittest.AggregateService.Method',
    index=0,
    containing_service=None,
    input_type=_AGGREGATEMESSAGE,
    output_type=_AGGREGATEMESSAGE,
    options=_descriptor._ParseOptions(descriptor_pb2.MethodOptions(), '\312\310\226;\022\022\020MethodAnnotation'),
  ),
])

class AggregateService(_service.Service):
  __metaclass__ = service_reflection.GeneratedServiceType
  DESCRIPTOR = _AGGREGATESERVICE
class AggregateService_Stub(AggregateService):
  __metaclass__ = service_reflection.GeneratedServiceStubType
  DESCRIPTOR = _AGGREGATESERVICE

# @@protoc_insertion_point(module_scope)
