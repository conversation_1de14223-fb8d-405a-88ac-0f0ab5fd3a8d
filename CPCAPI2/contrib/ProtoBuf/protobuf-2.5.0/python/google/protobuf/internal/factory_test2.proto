// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// http://code.google.com/p/protobuf/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Matt Toia)


package google.protobuf.python.internal;

import "google/protobuf/internal/factory_test1.proto";


enum Factory2Enum {
  FACTORY_2_VALUE_0 = 0;
  FACTORY_2_VALUE_1 = 1;
}

message Factory2Message {
  required int32 mandatory = 1;
  optional Factory2Enum factory_2_enum = 2;
  enum NestedFactory2Enum {
    NESTED_FACTORY_2_VALUE_0 = 0;
    NESTED_FACTORY_2_VALUE_1 = 1;
  }
  optional NestedFactory2Enum nested_factory_2_enum = 3;
  message NestedFactory2Message {
    optional string value = 1;
  }
  optional NestedFactory2Message nested_factory_2_message = 4;
  optional Factory1Message factory_1_message = 5;
  optional Factory1Enum factory_1_enum = 6;
  optional Factory1Message.NestedFactory1Enum nested_factory_1_enum = 7;
  optional Factory1Message.NestedFactory1Message nested_factory_1_message = 8;
  optional Factory2Message circular_message = 9;
  optional string scalar_value = 10;
  repeated string list_value = 11;
  repeated group Grouped = 12 {
    optional string part_1 = 13;
    optional string part_2 = 14;
  }
  optional LoopMessage loop = 15;
  optional int32 int_with_default = 16 [default = 1776];
  optional double double_with_default = 17 [default = 9.99];
  optional string string_with_default = 18 [default = "hello world"];
  optional bool bool_with_default = 19 [default = false];
  optional Factory2Enum enum_with_default = 20 [default = FACTORY_2_VALUE_1];
}

message LoopMessage {
  optional Factory2Message loop = 1;
}
