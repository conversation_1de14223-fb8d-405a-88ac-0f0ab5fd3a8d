# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/internal/test_bad_identifiers.proto

from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import service as _service
from google.protobuf import service_reflection
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)




DESCRIPTOR = _descriptor.FileDescriptor(
  name='google/protobuf/internal/test_bad_identifiers.proto',
  package='protobuf_unittest',
  serialized_pb='\n3google/protobuf/internal/test_bad_identifiers.proto\x12\x11protobuf_unittest\"\x1e\n\x12TestBadIdentifiers*\x08\x08\x64\x10\x80\x80\x80\x80\x02\"\x10\n\x0e\x41notherMessage2\x10\n\x0e\x41notherService:;\n\x07message\x12%.protobuf_unittest.TestBadIdentifiers\x18\x64 \x01(\t:\x03\x66oo:>\n\ndescriptor\x12%.protobuf_unittest.TestBadIdentifiers\x18\x65 \x01(\t:\x03\x62\x61r:>\n\nreflection\x12%.protobuf_unittest.TestBadIdentifiers\x18\x66 \x01(\t:\x03\x62\x61z:;\n\x07service\x12%.protobuf_unittest.TestBadIdentifiers\x18g \x01(\t:\x03quxB\x03\x90\x01\x01')


MESSAGE_FIELD_NUMBER = 100
message = _descriptor.FieldDescriptor(
  name='message', full_name='protobuf_unittest.message', index=0,
  number=100, type=9, cpp_type=9, label=1,
  has_default_value=True, default_value=unicode("foo", "utf-8"),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
DESCRIPTOR_FIELD_NUMBER = 101
descriptor = _descriptor.FieldDescriptor(
  name='descriptor', full_name='protobuf_unittest.descriptor', index=1,
  number=101, type=9, cpp_type=9, label=1,
  has_default_value=True, default_value=unicode("bar", "utf-8"),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
REFLECTION_FIELD_NUMBER = 102
reflection = _descriptor.FieldDescriptor(
  name='reflection', full_name='protobuf_unittest.reflection', index=2,
  number=102, type=9, cpp_type=9, label=1,
  has_default_value=True, default_value=unicode("baz", "utf-8"),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)
SERVICE_FIELD_NUMBER = 103
service = _descriptor.FieldDescriptor(
  name='service', full_name='protobuf_unittest.service', index=3,
  number=103, type=9, cpp_type=9, label=1,
  has_default_value=True, default_value=unicode("qux", "utf-8"),
  message_type=None, enum_type=None, containing_type=None,
  is_extension=True, extension_scope=None,
  options=None)


_TESTBADIDENTIFIERS = _descriptor.Descriptor(
  name='TestBadIdentifiers',
  full_name='protobuf_unittest.TestBadIdentifiers',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=True,
  extension_ranges=[(100, 536870912), ],
  serialized_start=74,
  serialized_end=104,
)


_ANOTHERMESSAGE = _descriptor.Descriptor(
  name='AnotherMessage',
  full_name='protobuf_unittest.AnotherMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  extension_ranges=[],
  serialized_start=106,
  serialized_end=122,
)

DESCRIPTOR.message_types_by_name['TestBadIdentifiers'] = _TESTBADIDENTIFIERS
DESCRIPTOR.message_types_by_name['AnotherMessage'] = _ANOTHERMESSAGE

class TestBadIdentifiers(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _TESTBADIDENTIFIERS

  # @@protoc_insertion_point(class_scope:protobuf_unittest.TestBadIdentifiers)

class AnotherMessage(_message.Message):
  __metaclass__ = _reflection.GeneratedProtocolMessageType
  DESCRIPTOR = _ANOTHERMESSAGE

  # @@protoc_insertion_point(class_scope:protobuf_unittest.AnotherMessage)

TestBadIdentifiers.RegisterExtension(message)
TestBadIdentifiers.RegisterExtension(descriptor)
TestBadIdentifiers.RegisterExtension(reflection)
TestBadIdentifiers.RegisterExtension(service)

DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), '\220\001\001')

_ANOTHERSERVICE = _descriptor.ServiceDescriptor(
  name='AnotherService',
  full_name='protobuf_unittest.AnotherService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=124,
  serialized_end=140,
  methods=[
])

class AnotherService(_service.Service):
  __metaclass__ = service_reflection.GeneratedServiceType
  DESCRIPTOR = _ANOTHERSERVICE
class AnotherService_Stub(AnotherService):
  __metaclass__ = service_reflection.GeneratedServiceStubType
  DESCRIPTOR = _ANOTHERSERVICE

# @@protoc_insertion_point(module_scope)
