// Protocol Buffers - Google's data interchange format
// Copyright 2008 Google Inc.  All rights reserved.
// http://code.google.com/p/protobuf/
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//     * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//     * Neither the name of Google Inc. nor the names of its
// contributors may be used to endorse or promote products derived from
// this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

// Author: <EMAIL> (Jon Perlow)

// This file tests that various identifiers work as field and type names even
// though the same identifiers are used internally by the java code generator.


// Some generic_services option(s) added automatically.
// See:  http://go/proto2-generic-services-default
option java_generic_services = true;   // auto-added

package io_protocol_tests;

option java_package = "com.google.protobuf";
option java_outer_classname = "TestBadIdentifiersProto";

message TestMessage {
}

message Descriptor {
  option no_standard_descriptor_accessor = true;
  optional string descriptor = 1;
  message NestedDescriptor {
    option no_standard_descriptor_accessor = true;
    optional string descriptor = 1;
  }
  optional NestedDescriptor nested_descriptor = 2;
}

message Parser {
  enum ParserEnum {
    PARSER = 1;
  }
  optional ParserEnum parser = 1;
}

message Deprecated {
  enum TestEnum {
    FOO = 1;
  }

  optional int32 field1 = 1 [deprecated=true];
  optional TestEnum field2 = 2 [deprecated=true];
  optional TestMessage field3 = 3 [deprecated=true];
}

message Override {
  optional int32 override = 1;
}

message Object {
  optional int32 object = 1;
  optional string string_object = 2;
}

message String {
  optional string string = 1;
}

message Integer {
  optional int32 integer = 1;
}

message Long {
  optional int32 long = 1;
}

message Float {
  optional float float = 1;
}

message Double {
  optional double double = 1;
}

service TestConflictingMethodNames {
  rpc Override(TestMessage) returns (TestMessage);
}

