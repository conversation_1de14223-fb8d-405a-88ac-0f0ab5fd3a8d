Changes for 1.6.0:

* New feature: ADD_FAILURE_AT() for reporting a test failure at the
  given source location -- useful for writing testing utilities.
* New feature: the universal value printer is moved from Google Mock
  to Google Test.
* New feature: type parameters and value parameters are reported in
  the XML report now.
* A gtest_disable_pthreads CMake option.
* Colored output works in GNU Screen sessions now.
* Parameters of value-parameterized tests are now printed in the
  textual output.
* Failures from ad hoc test assertions run before RUN_ALL_TESTS() are
  now correctly reported.
* Arguments of ASSERT_XY and EXPECT_XY no longer need to support << to
  ostream.
* More complete handling of exceptions.
* GTEST_ASSERT_XY can be used instead of ASSERT_XY in case the latter
  name is already used by another library.
* --gtest_catch_exceptions is now true by default, allowing a test
  program to continue after an exception is thrown.
* Value-parameterized test fixtures can now derive from Test and
  WithParamInterface<T> separately, easing conversion of legacy tests.
* Death test messages are clearly marked to make them more
  distinguishable from other messages.
* Compatibility fixes for Android, Google Native Client, MinGW, HP UX,
  PowerPC, Lucid autotools, libCStd, Sun C++, Borland C++ Builder (Code Gear),
  IBM XL C++ (Visual Age C++), and C++0x.
* Bug fixes and implementation clean-ups.
* Potentially incompatible changes: disables the harmful 'make install'
  command in autotools.

Changes for 1.5.0:

 * New feature: assertions can be safely called in multiple threads
   where the pthreads library is available.
 * New feature: predicates used inside EXPECT_TRUE() and friends
   can now generate custom failure messages.
 * New feature: Google Test can now be compiled as a DLL.
 * New feature: fused source files are included.
 * New feature: prints help when encountering unrecognized Google Test flags.
 * Experimental feature: CMake build script (requires CMake 2.6.4+).
 * Experimental feature: the Pump script for meta programming.
 * double values streamed to an assertion are printed with enough precision
   to differentiate any two different values.
 * Google Test now works on Solaris and AIX.
 * Build and test script improvements.
 * Bug fixes and implementation clean-ups.

 Potentially breaking changes:

 * Stopped supporting VC++ 7.1 with exceptions disabled.
 * Dropped support for 'make install'.

Changes for 1.4.0:

 * New feature: the event listener API
 * New feature: test shuffling
 * New feature: the XML report format is closer to junitreport and can
   be parsed by Hudson now.
 * New feature: when a test runs under Visual Studio, its failures are
   integrated in the IDE.
 * New feature: /MD(d) versions of VC++ projects.
 * New feature: elapsed time for the tests is printed by default.
 * New feature: comes with a TR1 tuple implementation such that Boost
   is no longer needed for Combine().
 * New feature: EXPECT_DEATH_IF_SUPPORTED macro and friends.
 * New feature: the Xcode project can now produce static gtest
   libraries in addition to a framework.
 * Compatibility fixes for Solaris, Cygwin, minGW, Windows Mobile,
   Symbian, gcc, and C++Builder.
 * Bug fixes and implementation clean-ups.

Changes for 1.3.0:

 * New feature: death tests on Windows, Cygwin, and Mac.
 * New feature: ability to use Google Test assertions in other testing
   frameworks.
 * New feature: ability to run disabled test via
   --gtest_also_run_disabled_tests.
 * New feature: the --help flag for printing the usage.
 * New feature: access to Google Test flag values in user code.
 * New feature: a script that packs Google Test into one .h and one
   .cc file for easy deployment.
 * New feature: support for distributing test functions to multiple
   machines (requires support from the test runner).
 * Bug fixes and implementation clean-ups.

Changes for 1.2.1:

 * Compatibility fixes for Linux IA-64 and IBM z/OS.
 * Added support for using Boost and other TR1 implementations.
 * Changes to the build scripts to support upcoming release of Google C++
   Mocking Framework.
 * Added Makefile to the distribution package.
 * Improved build instructions in README.

Changes for 1.2.0:

 * New feature: value-parameterized tests.
 * New feature: the ASSERT/EXPECT_(NON)FATAL_FAILURE(_ON_ALL_THREADS)
   macros.
 * Changed the XML report format to match JUnit/Ant's.
 * Added tests to the Xcode project.
 * Added scons/SConscript for building with SCons.
 * Added src/gtest-all.cc for building Google Test from a single file.
 * Fixed compatibility with Solaris and z/OS.
 * Enabled running Python tests on systems with python 2.3 installed,
   e.g. Mac OS X 10.4.
 * Bug fixes.

Changes for 1.1.0:

 * New feature: type-parameterized tests.
 * New feature: exception assertions.
 * New feature: printing elapsed time of tests.
 * Improved the robustness of death tests.
 * Added an Xcode project and samples.
 * Adjusted the output format on Windows to be understandable by Visual Studio.
 * Minor bug fixes.

Changes for 1.0.1:

 * Added project files for Visual Studio 7.1.
 * Fixed issues with compiling on Mac OS X.
 * Fixed issues with compiling on Cygwin.

Changes for 1.0.0:

 * Initial Open Source release of Google Test
