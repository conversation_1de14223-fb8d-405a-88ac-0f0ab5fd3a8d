# A Makefile for fusing Google Test and building a sample test against it.
#
# SYNOPSIS:
#
#   make [all]  - makes everything.
#   make TARGET - makes the given target.
#   make check  - makes everything and runs the built sample test.
#   make clean  - removes all files generated by make.

# Points to the root of fused Google Test, relative to where this file is.
FUSED_GTEST_DIR = output

# Paths to the fused gtest files.
FUSED_GTEST_H = $(FUSED_GTEST_DIR)/gtest/gtest.h
FUSED_GTEST_ALL_CC = $(FUSED_GTEST_DIR)/gtest/gtest-all.cc

# Where to find the sample test.
SAMPLE_DIR = ../../samples

# Where to find gtest_main.cc.
GTEST_MAIN_CC = ../../src/gtest_main.cc

# Flags passed to the preprocessor.
# We have no idea here whether pthreads is available in the system, so
# disable its use.
CPPFLAGS += -I$(FUSED_GTEST_DIR) -DGTEST_HAS_PTHREAD=0

# Flags passed to the C++ compiler.
CXXFLAGS += -g

all : sample1_unittest

check : all
	./sample1_unittest

clean :
	rm -rf $(FUSED_GTEST_DIR) sample1_unittest *.o

$(FUSED_GTEST_H) :
	../fuse_gtest_files.py $(FUSED_GTEST_DIR)

$(FUSED_GTEST_ALL_CC) :
	../fuse_gtest_files.py $(FUSED_GTEST_DIR)

gtest-all.o : $(FUSED_GTEST_H) $(FUSED_GTEST_ALL_CC)
	$(CXX) $(CPPFLAGS) $(CXXFLAGS) -c $(FUSED_GTEST_DIR)/gtest/gtest-all.cc

gtest_main.o : $(FUSED_GTEST_H) $(GTEST_MAIN_CC)
	$(CXX) $(CPPFLAGS) $(CXXFLAGS) -c $(GTEST_MAIN_CC)

sample1.o : $(SAMPLE_DIR)/sample1.cc $(SAMPLE_DIR)/sample1.h
	$(CXX) $(CPPFLAGS) $(CXXFLAGS) -c $(SAMPLE_DIR)/sample1.cc

sample1_unittest.o : $(SAMPLE_DIR)/sample1_unittest.cc \
                     $(SAMPLE_DIR)/sample1.h $(FUSED_GTEST_H)
	$(CXX) $(CPPFLAGS) $(CXXFLAGS) -c $(SAMPLE_DIR)/sample1_unittest.cc

sample1_unittest : sample1.o sample1_unittest.o gtest-all.o gtest_main.o
	$(CXX) $(CPPFLAGS) $(CXXFLAGS) $^ -o $@
