﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{4F4EC88D-69E0-4578-8FA4-AF49CF6BAF0B}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>cpcapi2_auth_service</RootNamespace>
    <WindowsTargetPlatformVersion>8.1</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v140</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WIN32_WINNT=0x0600;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;_WEBSOCKETPP_NOEXCEPT_;BOOST_ALL_NO_LIB;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;CURL_STATICLIB;CURL_DISABLE_LDAP;CURLPP_STATICLIB;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;ASIO_ENABLE_CANCELIO;BOOST_ASIO_ENABLE_CANCELIO;CPCAPI2_STATIC_LIB;_VARIADIC_MAX=10;USE_IPV6;USE_ARES;DEFAULT_BRIDGE_MAX_IN_OUTPUTS=10;USE_DTLS;TSC_WINDOWS;CPCAPI2_NO_DEPRECATIONS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\CPCAPI2\interface\public;..\..\..\CPCAPI2\interface\experimental;..\..\..\shared\sipfoundry\main;..\..\..\CPCAPI2\impl;..\..\..\CPCAPI2;..\..\..\external\websocketpp;..\..\..\external\boost\include;..\..\..\external\rapidjson;..\..\..\..\windows_libs\curl\include..\..\..\..\windows_libs\x86\curlpp\debug\include;..\..\..\..\windows_libs\x86\openssl\debug\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\build;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>CPCAPI2_SharedLibrary_Debug.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
    <PostBuildEvent>
      <Command>if exist  "$(ProjectDir)..\..\..\build\" xcopy "$(ProjectDir)..\..\..\build\*.*" "$(OutDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WIN32_WINNT=0x0600;USE_SSL;USE_DNS_VIP;RESIP_USE_STL_STREAMS;CPCAPI2_INCLUDE_UNRELEASED_HEADERS;_WEBSOCKETPP_CPP11_CHRONO_;_WEBSOCKETPP_CPP11_FUNCTIONAL_;_WEBSOCKETPP_CPP11_MEMORY_;_WEBSOCKETPP_CPP11_RANDOM_DEVICE_;_WEBSOCKETPP_CPP11_REGEX_;_WEBSOCKETPP_CPP11_SYSTEM_ERROR_;_WEBSOCKETPP_CPP11_THREAD_;_WEBSOCKETPP_NOEXCEPT_;BOOST_ALL_NO_LIB;_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS;CURL_STATICLIB;CURL_DISABLE_LDAP;CURLPP_STATICLIB;_CRT_SECURE_NO_WARNINGS;_SCL_SECURE_NO_WARNINGS;ASIO_ENABLE_CANCELIO;BOOST_ASIO_ENABLE_CANCELIO;CPCAPI2_STATIC_LIB;_VARIADIC_MAX=10;USE_IPV6;USE_ARES;DEFAULT_BRIDGE_MAX_IN_OUTPUTS=10;USE_DTLS;TSC_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <SDLCheck>true</SDLCheck>
      <AdditionalIncludeDirectories>..\..\..\CPCAPI2\interface\public;..\..\..\CPCAPI2\interface\experimental;..\..\..\shared\sipfoundry\main;..\..\..\CPCAPI2\impl;..\..\..\CPCAPI2;..\..\..\external\websocketpp;..\..\..\external\boost\include;..\..\..\external\rapidjson;..\..\..\..\windows_libs\x86\curl\release\include;..\..\..\..\windows_libs\x86\curlpp\release\include;..\..\..\..\windows_libs\x86\openssl\release\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <DisableSpecificWarnings>4996</DisableSpecificWarnings>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>..\..\..\build;..\..\..\lib;..\..\..\..\windows_libs\x86\curlpp\release\lib;..\..\..\..\windows_libs\x86\curl\release\lib;..\..\..\..\windows_libs\x86\openssl\release\lib;..\..\..\..\windows_libs\voiceage\AmrwbFull_13.0;..\..\..\..\windows_libs\x86\zlib\release\lib;;..\..\..\..\windows_libs\webrtc\Release;..\..\..\..\windows_libs\openh264;..\..\..\..\windows_libs\tsc;..\..\..\external\Microsoft\v110\Samples\multimedia\directshow\baseclasses\Release;..\..\..\external\speex\win32\libspeex\Release;..\..\..\external\bv32;..\..\..\external\SILK;..\..\..\external\VSoft\HD_H264\Windows\lib;..\..\..\external\VSoft\HD_H264\Windows\lib-icl32;..\..\..\external\UBVideo\H263\dec\Library\Release;..\..\..\external\UBVideo\H263\enc\Library\Release;..\..\..\..\windows_libs\codecpro\g729\x86;..\..\..\external\g726\G726AsDLL\Release\;..\..\..\external\libmsrp\Win32\VS_2012\Release;..\..\..\..\windows_libs\x86\libxml2\release\lib;..\..\..\external\v8\build\Release\lib;..\..\..\..\windows_libs\x86\xmlsec\release\lib;..\..\..\external\vqmon\vqmon\lib\win-i386-VS2012\release;..\..\..\external\sopi\Release;..\..\..\..\windows_libs\shaka_packager\Release;..\..\..\..\windows_libs\breakpad\release;..\..\..\..\windows_libs\x86\snappy\release\lib;..\..\..\external\Beamr\H265\lib;..\..\..\external\Beamr\H265\lib-icl32;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>CPCAPI2_SharedLibrary.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;Ws2_32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalOptions>/alternatename:___iob_func=___intel_lib_iob_func %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <PostBuildEvent>
      <Command>if exist  "$(ProjectDir)..\..\..\build\" xcopy "$(ProjectDir)..\..\..\build\*.*" "$(OutDir)" /Y</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\Cpcapi2Runner.h" />
    <ClInclude Include="..\..\SdkManager.h" />
    <ClInclude Include="cpcapi2_auth_service.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="targetver.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\BaseException.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Condition.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ConfigParse.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Data.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\DataStream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\FdPoll.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Lock.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Log.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\MD5Stream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Mutex.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseBuffer.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseException.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SelectInterruptor.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Socket.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Subsystem.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogBuf.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogStream.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ThreadIf.cxx" />
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\vmd5.cxx" />
    <ClCompile Include="..\..\SdkManager.cpp" />
    <ClCompile Include="cpcapi2_auth_service.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="cpcapi2_auth_service.rc" />
  </ItemGroup>
  <ItemGroup>
    <Image Include="cpcapi2_auth_service.ico" />
    <Image Include="small.ico" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
