﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="Source Files\rutil">
      <UniqueIdentifier>{fe97887b-e585-49af-bcd3-1a0c3f2e5c3f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <Text Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="cpcapi2_auth_service.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\Cpcapi2Runner.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\..\SdkManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="cpcapi2_auth_service.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\BaseException.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Condition.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Data.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\DataStream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\FdPoll.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Lock.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Log.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\MD5Stream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Mutex.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseBuffer.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ParseException.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SelectInterruptor.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Socket.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\Subsystem.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogBuf.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\SysLogStream.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ThreadIf.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\vmd5.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
    <ClCompile Include="..\..\SdkManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\..\..\shared\sipfoundry\main\rutil\ConfigParse.cxx">
      <Filter>Source Files\rutil</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="cpcapi2_auth_service.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Image Include="small.ico">
      <Filter>Resource Files</Filter>
    </Image>
    <Image Include="cpcapi2_auth_service.ico">
      <Filter>Resource Files</Filter>
    </Image>
  </ItemGroup>
</Project>