#!/bin/bash
VERSION=1.0.0
RELEASE=1
ARCH=noarch

options=hv:r:a:
longoptions=help,version:,release:,arch:

opts=$(getopt --options $options --longoptions $longoptions --name "$0" -- "$@")

if [ $? != 0 ]; then echo "Invalid parameters" >&2; exit 1; fi

eval set -- "$opts"

while true; do
  case "$1" in
    -h|--help)
      cat <<-EOF
Usage: $0 [-?|-h|--help]
  [-v|--version <version number>]
  [-r|--release <release number>]
  [-a|--arch <architecture>]

  -h|--help:	print this help screen
  -v|--version:	specify the version number
  -r|--release:	specify the release number
  -a|--arch:	specify the target achitecture
EOF

      exit 0
      ;;
    -v|--version)
      VERSION="$2"
      shift 2
      ;;
    -r|--release)
      RELEASE="$2"
      shift 2
      ;;
    -a|--arch)
      ARCH="$2"
      shift 2
      ;;
    --)
      shift
      break
      ;;
    *)
      echo "Internal error"
      exit 1
      ;;
  esac
done

rpmbuild -bb --define "_topdir %(pwd)/rpmbuild" --define "_VERSION $VERSION" --define "_RELEASE $RELEASE" rpmbuild/cpcauth.spec --target $ARCH
