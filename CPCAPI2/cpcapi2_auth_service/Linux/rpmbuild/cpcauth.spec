Summary: cpcauth
Name: cpcauth
Version: %_VERSION
Release: %_RELEASE
License: Commercial
URL: http://www.counterpath.com
Group: Applications/Communications
Packager: CounterPath Corporation
Requires: openssl
Requires: systemd

#%define _binaries_in_noarch_packages_terminate_build 0

%description
This is a cpcauth service installation package.

%prep
echo "_topdir = %_topdir"
echo "BUILDROOT = $RPM_BUILD_ROOT"
mkdir -p $RPM_BUILD_ROOT/opt/cpcauth
mkdir -p $RPM_BUILD_ROOT/etc/sysctl.d
mkdir -p $RPM_BUILD_ROOT/etc/security/limits.d
#mkdir -p $RPM_BUILD_ROOT/etc/pam.d
mkdir -p $RPM_BUILD_ROOT/usr/lib/systemd/system

cp %_topdir/../build/cpcapi2_auth_service $RPM_BUILD_ROOT/opt/cpcauth
cp %_topdir/cpcapi2_auth_service.config $RPM_BUILD_ROOT/opt/cpcauth
cp %_topdir/cpcauth.sysctl.conf $RPM_BUILD_ROOT/etc/sysctl.d/66.cpcauth.sysctl.conf
cp %_topdir/cpcauth.limits.conf $RPM_BUILD_ROOT/etc/security/limits.d/66.cpcauth.limits.conf
cp %_topdir/cpcauth.service $RPM_BUILD_ROOT/usr/lib/systemd/system/cpcauth.service
exit

%files
%attr(0744, cpcauth, cpcauth) /opt/cpcauth/cpcapi2_auth_service
%attr(0644, cpcauth, cpcauth) %config(noreplace) /opt/cpcauth/cpcapi2_auth_service.config
%attr(0644, cpcauth, cpcauth) %ghost /opt/cpcauth/p256-private-key.p8
%attr(0644, cpcauth, cpcauth) %ghost /opt/cpcauth/p256-public-key.spki
%attr(0644, -, -) /etc/sysctl.d/66.cpcauth.sysctl.conf
%attr(0644, -, -) /etc/security/limits.d/66.cpcauth.limits.conf
%attr(0644, -, -) /usr/lib/systemd/system/cpcauth.service

%pre
useradd cpcauth -U || true

%post
exec 1>/proc/$PPID/fd/1
exec 2>/proc/$PPID/fd/2
openssl genpkey -algorithm EC -pkeyopt ec_paramgen_curve:P-256 -pkeyopt ec_param_enc:named_curve | openssl pkcs8 -topk8 -nocrypt -outform pem > /opt/cpcauth/p256-private-key.p8
openssl pkey -pubout -inform pem -outform pem -in /opt/cpcauth/p256-private-key.p8 -out /opt/cpcauth/p256-public-key.spki
chown cpcauth.cpcauth /opt/cpcauth/p256-private-key.p8 /opt/cpcauth/p256-public-key.spki
#letsencrypt and certbot
echo

cp /etc/rc.local /etc/rc.local.`date +%%Y%%m%%d%%H%%M%%S`
sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/rc.local
echo "#CPCCONF BEGIN *** DO NOT EDIT ***" >> /etc/rc.local
echo "defrt=`ip route | grep '^default' | head -1`" >> /etc/rc.local
echo "ip route change $defrt initcwnd 10" >> /etc/rc.local
echo "#CPCCONF END   *** DO NOT EDIT ***" >> /etc/rc.local

sysctl --system

systemctl enable cpcauth.service
#DO NOT START YET
#service cpcauth.service start

echo "*** IMPORTANT *** Need to open ports in the firewall as configured"
echo "*** IMPORTANT *** Need to modify /opt/cpcauth/cpcapi2_conf.config prior to start cpcauth, then type 'service cpcauth.service start' to run the cpcauth service"

%preun
service stop cpcauth.service

if [ "$1" == "0" ]; then
  systemctl disable cpcauth.service
fi

%postun
if [ "$1" == "0" ]; then
  userdel cpcauth

  sed -i '/^#CPCCONF BEGIN/,/^#CPCCONF END/d' /etc/rc.local
fi

sysctl --system

%clean
rm -rf $RPM_BUILD_ROOT

%changelog
#* :r!date "+\%a \%b \%d \%Y" Bill Liu <<EMAIL>>
#  - <Placeholder>
