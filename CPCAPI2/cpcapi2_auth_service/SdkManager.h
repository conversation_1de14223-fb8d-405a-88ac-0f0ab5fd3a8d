#pragma once

#include <cpcapi2.h>
#include <auth_server/AuthServer.h>
#include <auth_server/AuthServerHandler.h>
#include <phone/PhoneInternal.h>
#include <licensing/LicensingClientHandler.h>
#include <licensing/LicensingClientManager.h>

// rutil includes
#include <rutil/MultiReactor.hxx>
#include <rutil/Data.hxx>

namespace CPCAPI2
{
namespace AuthService
{
class SdkManager : public CPCAPI2::PhoneErrorHandler,
                   public CPCAPI2::Licensing::LicensingClientHandler,
                   public CPCAPI2::AuthServer::AuthServerHandler
{
public:
   SdkManager();
   ~SdkManager();

   void run();
   void shutdown();
   void join();

   void handleSdkCallback();

   void sigtermHandler(int signum);

   // PhoneErrorHandler
   virtual int onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args);
   virtual int onLicensingError(const CPCAPI2::LicensingErrorEvent& args);

   // LicensingClientHandler
   virtual int onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args);
   virtual int onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args);
   virtual int onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args);

   // AuthServerHandler
   

private:
   void appInit();
   void appShutdown();
   void handleSdkCallbackImpl();

   void initFromSettings();

private:
   resip::MultiReactor mReactor;
   CPCAPI2::PhoneInternal* mPhone;
   CPCAPI2::Licensing::LicensingClientManager* mLicensingMgr;
   CPCAPI2::AuthServer::AuthServer* mAuthServer;
};
}
}
