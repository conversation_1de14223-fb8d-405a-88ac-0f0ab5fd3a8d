#include "stdafx.h"
#include "SdkManager.h"
#include "Cpcapi2ServerLicense.h"
#include "rutil/ConfigParse.hxx"

#include <cpcapi2.h>

#include <fstream>
#include <functional>

#ifndef WIN32
#include <signal.h>
#endif

#define CPCAPI2_CONF_NUM_SDK_REACTORS 4

using namespace CPCAPI2;
using namespace CPCAPI2::Licensing;

namespace CPCAPI2
{
namespace AuthService
{

class MyConfigParse : public resip::ConfigParse
{
private:
   void parseCommandLine(int argc, char** argv, int skipCount = 0) {}
   void printHelpText(int argc, char **argv) {}
};

std::function<void(int)> signal_callback_wrapper;
void signal_callback_function(int val)
{
   signal_callback_wrapper(val);
}

SdkManager::SdkManager() : mAuthServer(NULL), mLicensingMgr(NULL)
{
}

SdkManager::~SdkManager()
{
}

void SdkManager::run()
{
   mReactor.start();
   mReactor.post(resip::resip_bind(&SdkManager::appInit, this));
}

void SdkManager_sdkCallbackHook(void* context)
{
   SdkManager* cpcRunner = (SdkManager*)context;
   cpcRunner->handleSdkCallback();
}

void SdkManager::handleSdkCallback()
{
   mReactor.post(resip::resip_bind(&SdkManager::handleSdkCallbackImpl, this));
}

void SdkManager::handleSdkCallbackImpl()
{
   mPhone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   mAuthServer->process(CPCAPI2::AuthServer::AuthServer::kBlockingModeNonBlocking);
}

void SdkManager::sigtermHandler(int signum)
{
   shutdown();
}

void SdkManager::appInit()
{
#ifndef WIN32
   // OBELISK-4439: prevent SIGPIPE from killing the whole process by ignoring SIGPIPE;
   // write errors will be handled locally instead
   signal(SIGPIPE, SIG_IGN);

   // systemd will send us SIGTERM when it wants us to shutdown
   struct sigaction sthandler;
   signal_callback_wrapper = std::bind(&SdkManager::sigtermHandler, this, std::placeholders::_1);
   sthandler.sa_handler = signal_callback_function;
   sigemptyset(&sthandler.sa_mask);
   sthandler.sa_flags = 0;
   sigaction(SIGTERM, &sthandler, NULL);
#endif

   mPhone = CPCAPI2::PhoneInternal::create(0);
   LicenseInfo licenseInfo;
   licenseInfo.licenseKey = "lkj";
   licenseInfo.licenseDocumentLocation = "/tmp";
   licenseInfo.licenseAor = "";
   mPhone->setCallbackHook(SdkManager_sdkCallbackHook, this);
   mPhone->initialize(licenseInfo, this, false);
   mPhone->setLoggingEnabled("cpcapi2auth", false);
   
   MyConfigParse fileConfig;
   fileConfig.parseConfig(0, NULL, "cpcapi2_auth_service.config");   

   mLicensingMgr = LicensingClientManager::getInterface(mPhone);
   mLicensingMgr->setCallbackHook(SdkManager_sdkCallbackHook, this);
   LicensingClientHandle client = mLicensingMgr->create();
   mLicensingMgr->setHandler(client, this);

   LicensingClientSettings settings;
   settings.licenseKeys.push_back(LICENSE_KEY_1);
   settings.licenseDocumentLocation = LICENSE_DOCUMENT_LOCATION;
   settings.provisioningId = LICENSE_AOR;
   settings.osVersion = "Windows for Workgroups 3.1";
   settings.keySourceUrl = "http://license.counterpath.com/generate";
   mLicensingMgr->applySettings(client, settings);
   mLicensingMgr->validateLicenses(client);

   mAuthServer = CPCAPI2::AuthServer::AuthServer::getInterface(mPhone);
   mAuthServer->setHandler(this);
   mAuthServer->setCallbackHook(SdkManager_sdkCallbackHook, this);
   CPCAPI2::AuthServer::AuthServerConfig serverConfig;
   serverConfig.numThreads = 12;
   
   serverConfig.port = 18082;
   fileConfig.getConfigValue("auth_service_http_port", serverConfig.port);
   fileConfig.getConfigValue("token_expiry_time", serverConfig.tokenExpiry);

   resip::Data httpsCertificateFilePath;
   if (fileConfig.getConfigValue("auth_service_http_cert_file_path", httpsCertificateFilePath))
   {
      serverConfig.httpsCertificateFilePath = httpsCertificateFilePath.c_str();
   }
   
   resip::Data httpsPrivateKeyFilePath;
   if (fileConfig.getConfigValue("auth_service_http_priv_key_file_path", httpsPrivateKeyFilePath))
   {
      serverConfig.httpsPrivateKeyFilePath = httpsPrivateKeyFilePath.c_str();
   }

   resip::Data httpsDiffieHellmanParamsFilePath;
   if (fileConfig.getConfigValue("auth_service_http_dh_params_file_path", httpsDiffieHellmanParamsFilePath))
   {
      serverConfig.httpsDiffieHellmanParamsFilePath = httpsDiffieHellmanParamsFilePath.c_str();
   }

   resip::Data certFilePath;
   fileConfig.getConfigValue("certificate_file_path", certFilePath);
   serverConfig.certificateFilePath = certFilePath.c_str();

   mAuthServer->start(serverConfig);

   initFromSettings();
}

void SdkManager::shutdown()
{
   //mTTSApi->shutdown();
   mReactor.execute(resip::resip_bind(&SdkManager::appShutdown, this));
   mReactor.stop();
}

void SdkManager::appShutdown()
{
   mAuthServer->shutdown();
}

void SdkManager::join()
{
   mReactor.join();
}

void SdkManager::initFromSettings()
{
}

int SdkManager::onError(const cpc::string& sourceModule, const CPCAPI2::PhoneErrorEvent& args)
{
   return 0;
}

int SdkManager::onLicensingError(const CPCAPI2::LicensingErrorEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesSuccess(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesSuccessEvent& args)
{
   return 0;
}

int SdkManager::onValidateLicensesFailure(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ValidateLicensesFailureEvent& args)
{
   return 0;
}

int SdkManager::onError(CPCAPI2::Licensing::LicensingClientHandle client, const CPCAPI2::Licensing::ErrorEvent& args)
{
   return 0;
}
}
}
