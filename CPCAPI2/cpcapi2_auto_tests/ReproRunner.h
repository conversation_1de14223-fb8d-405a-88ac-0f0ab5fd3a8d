#pragma once

#ifndef CPCAPI2_REPRORUNNER_H
#define CPCAPI2_REPRORUNNER_H

#include <repro/ReproRunner.hxx>
#include "resip/stack/ssl/Security.hxx"

namespace CPCAPI2
{
class ReproRunner : public repro::ReproRunner
{
   public:
      bool isRunning() { return mRunning; }
      virtual void restart();
      virtual void restart(resip::Data configFile);
      virtual void restart(resip::Data configFile, resip::BaseSecurity::CipherList cipherList);

};
} // namespace CPCAPI2
#endif // CPCAPI2_REPRORUNNER_H
