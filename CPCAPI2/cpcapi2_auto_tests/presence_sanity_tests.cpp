#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_events.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipAccount;

class PresenceSanityTests : public CpcapiAutoTest
{
public:
   PresenceSanityTests() {}
   virtual ~PresenceSanityTests() {}
};


// test that graceful failure occurs when a presence handlers is not added before account is enabled
TEST_F(PresenceSanityTests, MissingSetPresenceHandler) {
   TestAccount alice("alice", Account_NoInit);
   alice.config.registerHandlers = false;
   alice.init();
   alice.account->setHandler(alice.handle, (SipAccountHandler*)0xDEADBEEF); 
   alice.enable();

   // now set the handler for presence TOO LATE (after account is enabled)
   alice.presence->setHandler(alice.handle, (SipPresenceSubscriptionHandler*)0xDEADBEEF);
   assertPhoneError_ex(alice, "SipAccountInterface", [](const PhoneErrorEvent& evt){
      // error should contain information about the failed method
      ASSERT_TRUE(evt.errorText.find("setHandler") >= 0);
   });
}

TEST_F(PresenceSanityTests, preparePresence_InvalidSubscriptionHandle) {
   TestAccount alice("alice");
   PhoneErrorEvent evt;
   cpc::string module;
   alice.presence->preparePresence(-1, CannedStatus_Available);
   ASSERT_TRUE(alice.phoneEvents->expectEvent("PhoneHandler::onError", 15000, StrEqualsPred("SipAccountInterface"), module, evt));
}

TEST_F(PresenceSanityTests, addParticipant_MissingSipPrefix) {
   TestAccount alice("alice");
   alice.phone->setAddressTransformer(NULL);
	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
   // should fail due to missing sip: prefix
	alice.presence->addParticipant(aliceSubs, "<EMAIL>");
   SipPresence::ErrorEvent evt;
   SipEventSubscriptionHandle hdl;
   ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onError", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
}

