#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if 0 //(CPCAPI2_BRAND_ORCHESTRATION_SERVER_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include "test_call_events.h"
#include "test_events.h"
#include "test_account_events.h"
#include "test_framework/xmpp_test_helper.h"

#include "../../impl/util/CurlPPHelper.h"
#include "../../impl/util/CurlPPProgress.h"
#include "../../impl/util/CurlPPSSL.h"
#include "../../impl/auth_server/AuthServerJwtUtils.h"
#include "../../impl/auth_server/AuthServerDbAccess.h"

#include <orchestration_server/OrchestrationServer.h>
#include <orchestration_server/OrchestrationServerHandler.h>
#include <cloudwatchdog/CloudWatchdogService.h>
#include <cloudwatchdog/CloudWatchdogServiceHandler.h>

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::OrchestrationServer;
using namespace CPCAPI2::JsonApi;


class WatchdogServerTests : public CpcapiAutoTest
{
   
public:
   
   WatchdogServerTests() {}
   virtual~ WatchdogServerTests() {}

};

TEST_F(WatchdogServerTests, StartupWatchdog)
{
   TestAccount watchdog("watchdog", Account_Init);
   
   auto watchdogSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog);
      // Initial watchdog query is sent 60 seconds after startup
      unsigned int requestHandle = 0;
      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 65000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);
   });
   
   waitForMs(watchdogSetupEvent, std::chrono::milliseconds(70000));
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

// disabled due to sqlite exception on table flush
TEST_F(WatchdogServerTests, DISABLED_StartupWatchdogWithExistingServer)
{
   XmppTestAccount agent("agent", Account_Init);

   // Setup the xmpp agent and the associated orchestration and push notification server
   auto agentSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupXmppAgent(agent);

      cpc::vector<CPCAPI2::CloudConnector::ServiceDesc> services;
      XmppTestAccountConfig::getDefaultCloudServices(services);
      agent.orchestrationServer->queryServers(services);

      unsigned int requestHandle = 0;
      CPCAPI2::OrchestrationServer::QueryServersResult args;
      ASSERT_TRUE(cpcWaitForEvent(agent.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.servers.size(), 2);
   });

   waitFor(agentSetupEvent);

   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;

   XmppTestAccountConfig::setupJsonApiServer(watchdog.phone, XMPP_WATCHDOG_WS_PORT, XMPP_WATCHDOG_ORCH_SERVER_HTTP_PORT);
   CPCAPI2::JsonApi::JsonApiServerSendTransport::getInterface(watchdog.phone)->setJsonApiServer(watchdog.jsonApiServer);

   XmppTestAccountConfig::setupPushServer(watchdog.phone);
   XmppTestAccountConfig::setupOrchServer(watchdog.phone);

   // Update the watchdog orchestrator with the server info as well, as no common redis server is running, mimic a discovered xmpp agent
   auto watchdogSetServerEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
   });

   waitFor(watchdogSetServerEvent);

   auto watchdogSetupEvent = std::async(std::launch::async, [&]()
   {
      CPCAPI2::CloudWatchdog::CloudWatchdogService* watchdogServer = CPCAPI2::CloudWatchdog::CloudWatchdogService::getInterface(watchdog.phone);
      watchdogServer->start(watchdogConfig);

      unsigned int requestHandle = 0;
      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 65000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 2);
      
      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt1;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt1));
      std::string server1(upEvt1.server.server.server);
      std::string region1(upEvt1.server.server.region);
      ASSERT_EQ(server1, json);
      ASSERT_EQ(region1, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt1.server.services.size(), 1);
      std::string service1 = upEvt1.server.services[0].c_str();
      ASSERT_EQ(service1, xmppAgentServiceId);
   
      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt2;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt2));
      std::string server2(upEvt2.server.server.server);
      std::string region2(upEvt2.server.server.region);
      ASSERT_EQ(region2, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt2.server.services.size(), 1);
      std::string service2 = upEvt2.server.services[0].c_str();
      ASSERT_EQ(service2, pushNotificationEndpointServiceId);
   });
   
   waitForMs(watchdogSetupEvent, std::chrono::milliseconds(70000));
   
   XmppTestAccountConfig::destroyXmppAgent(agent);
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, DetectNewServer)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;

   auto watchdogSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);

      unsigned int requestHandle = 0;
      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);
   });

   waitFor(watchdogSetupEvent);

   XmppTestAccount agent("agent", Account_Init);

   // Setup the xmpp agent and the associated orchestration and push notification server
   auto agentSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupXmppAgent(agent);

      cpc::vector<CPCAPI2::CloudConnector::ServiceDesc> services;
      XmppTestAccountConfig::getDefaultCloudServices(services);
      agent.orchestrationServer->queryServers(services);

      unsigned int requestHandle = 0;
      CPCAPI2::OrchestrationServer::QueryServersResult args;
      ASSERT_TRUE(cpcWaitForEvent(agent.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.servers.size(), 2);
   });

   waitFor(agentSetupEvent);

   // Update the watchdog orchestrator with the server info as well, as no common redis server is running, mimic a discovered xmpp agent
   auto watchdogSetServerEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
   });

   waitFor(watchdogSetServerEvent);

   auto serverUpEvent = std::async(std::launch::async, [&]()
   {
      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

      unsigned int requestHandle = 0;

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt1;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt1));
      std::string server1(upEvt1.server.server.server);
      std::string region1(upEvt1.server.server.region);
      ASSERT_EQ(server1, json);
      ASSERT_EQ(region1, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt1.server.services.size(), 1);
      std::string service1 = upEvt1.server.services[0].c_str();
      ASSERT_EQ(service1, xmppAgentServiceId);

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt2;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt2));
      std::string server2(upEvt2.server.server.server);
      std::string region2(upEvt2.server.server.region);
      ASSERT_EQ(server2, json);
      ASSERT_EQ(region2, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt2.server.services.size(), 1);
      std::string service2 = upEvt2.server.services[0].c_str();
      ASSERT_EQ(service2, pushNotificationEndpointServiceId);
   });
   
   waitFor(serverUpEvent);
   
   XmppTestAccountConfig::destroyXmppAgent(agent);
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, DetectMissedServerRestart)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 20;

   auto watchdogEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);

      unsigned int requestHandle = 0;

      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);

      CPCAPI2::OrchestrationServer::QueryServersResult orchResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);

      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 2);
      ASSERT_TRUE(orchResult.servers[0].started);
      ASSERT_TRUE(orchResult.servers[1].started);

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt1;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt1));
      std::string server1(upEvt1.server.server.server);
      std::string region1(upEvt1.server.server.region);
      ASSERT_EQ(server1, json);
      ASSERT_EQ(region1, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt1.server.services.size(), 1);
      std::string service1 = upEvt1.server.services[0].c_str();
      ASSERT_EQ(service1, xmppAgentServiceId);

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt2;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt2));
      std::string server2(upEvt2.server.server.server);
      std::string region2(upEvt2.server.server.region);
      ASSERT_EQ(server2, json);
      ASSERT_EQ(region2, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt2.server.services.size(), 1);
      std::string service2 = upEvt2.server.services[0].c_str();
      ASSERT_EQ(service2, pushNotificationEndpointServiceId);

      CPCAPI2::CloudWatchdog::SetServerInfoResult setInfoResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setInfoResult));
      ASSERT_TRUE(setInfoResult.success);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setInfoResult));
      ASSERT_TRUE(setInfoResult.success);

      CPCAPI2::CloudWatchdog::NotificationSent notificationSent;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);

      // Reset the server info so that it mimics a missed restart, in actual scenario, it would be the xmpp agent restart that would have reset the startup flag.
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 2);
      ASSERT_TRUE(orchResult.servers[0].started);
      ASSERT_TRUE(orchResult.servers[1].started);

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt3;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt3));
      std::string server3(upEvt3.server.server.server);
      std::string region3(upEvt3.server.server.region);
      ASSERT_EQ(server3, json);
      ASSERT_EQ(region3, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt3.server.services.size(), 1);
      std::string service3 = upEvt3.server.services[0].c_str();
      ASSERT_EQ(service3, xmppAgentServiceId);

      CPCAPI2::CloudWatchdog::ServerUpEvent upEvt4;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerUp", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, upEvt4));
      std::string server4(upEvt4.server.server.server);
      std::string region4(upEvt4.server.server.region);
      ASSERT_EQ(server4, json);
      ASSERT_EQ(region4, XMPP_AGENT_REGION);
      ASSERT_EQ(upEvt4.server.services.size(), 1);
      std::string service4 = upEvt4.server.services[0].c_str();
      ASSERT_EQ(service4, pushNotificationEndpointServiceId);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setInfoResult));
      ASSERT_TRUE(setInfoResult.success);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setInfoResult));
      ASSERT_TRUE(setInfoResult.success);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 2);
      ASSERT_FALSE(orchResult.servers[0].started);
      ASSERT_FALSE(orchResult.servers[1].started);
   });
   
   waitForMs(watchdogEvent, std::chrono::milliseconds(300000));
   
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, DetectServerDown)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;
   
   auto watchdogSetupEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);

      unsigned int requestHandle = 0;

      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);

      CPCAPI2::OrchestrationServer::QueryServersResult orchResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);

      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);

      CPCAPI2::CloudWatchdog::NotificationSent notificationSent;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
   });

   waitForMs(watchdogSetupEvent, std::chrono::milliseconds(300000));

   std::string auth("");
   std::string orch("");
   std::string json("");
   XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
   std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
   std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

   auto watchdogDownEvent = std::async(std::launch::async, [&]()
   {
      unsigned int requestHandle = 0;

      CPCAPI2::OrchestrationServer::QueryServersResult orchResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 2);
      unsigned int ttl = (unsigned int)orchResult.servers[0].ttlSeconds + (3 * (watchdogConfig.monitorFrequency * 1000));
      
      // Prevent the TTL from being updated, default ttl duration is 180 seconds, so will be a while before the service down is detected
      cpc::vector<CPCAPI2::CloudConnector::ServiceDesc> services;
      XmppTestAccountConfig::getDefaultCloudServices(services);
      CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(watchdog.phone);
      cloudConfigMgr->stopTtlService();

      CPCAPI2::CloudWatchdog::ServerDownEvent downEvt;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerDown", ttl, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      std::string server(downEvt.server.server.server);
      std::string region(downEvt.server.server.region);
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      std::string service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, pushNotificationEndpointServiceId);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerDown", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      server = downEvt.server.server.server;
      region = downEvt.server.server.region;
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, xmppAgentServiceId);
   });

   waitForMs(watchdogDownEvent, std::chrono::milliseconds(300000));

   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, DetectServerShutdown)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;
   
   auto watchdogDownEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);
      
      unsigned int requestHandle = 0;
      
      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);
      
      CPCAPI2::OrchestrationServer::QueryServersResult orchResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);
      
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
      
      CPCAPI2::CloudWatchdog::NotificationSent notificationSent;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
      
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, false, true);
      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();
      
      CPCAPI2::CloudWatchdog::ServerShutdownEvent downEvt;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerShutdown", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      std::string server(downEvt.server.server.server);
      std::string region(downEvt.server.server.region);
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      std::string service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, xmppAgentServiceId);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerShutdown", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      server = downEvt.server.server.server;
      region = downEvt.server.server.region;
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, pushNotificationEndpointServiceId);
   });
   
   waitForMs(watchdogDownEvent, std::chrono::milliseconds(300000));
   
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, DetectSetServerInfo)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;

   auto watchdogSetEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);

      unsigned int requestHandle = 0;

      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);

      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);

      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

      CPCAPI2::CloudWatchdog::SetServerInfoResult setEvt;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setEvt));
      std::string server(setEvt.server.server.server);
      std::string region(setEvt.server.server.region);
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(setEvt.server.services.size(), 1);
      std::string service = setEvt.server.services[0].c_str();
      ASSERT_EQ(service, xmppAgentServiceId);
      ASSERT_TRUE(setEvt.success);

      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onSetServerInfoResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, setEvt));
      server = setEvt.server.server.server;
      region = setEvt.server.server.region;
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(setEvt.server.services.size(), 1);
      service = setEvt.server.services[0].c_str();
      ASSERT_EQ(service, pushNotificationEndpointServiceId);
      ASSERT_TRUE(setEvt.success);
   });

   waitForMs(watchdogSetEvent, std::chrono::milliseconds(300000));

   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, EnableServerQueryResult)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;
   watchdogConfig.queryResponseEnabled = true;

   auto watchdogQueryEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);

      unsigned int requestHandle = 0;

      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);

      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();

      CPCAPI2::CloudWatchdog::ServerQueryResult queryEvt;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerQueryResult", 60000, CPCAPI2::test::AlwaysTruePred(), requestHandle, queryEvt));
      ASSERT_EQ(queryEvt.serverList.size(), 0);

      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerQueryResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, queryEvt));
      ASSERT_EQ(queryEvt.serverList.size(), 2);
      std::string server(queryEvt.serverList[0].server.server);
      std::string region(queryEvt.serverList[0].server.region);
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(queryEvt.serverList[0].services.size(), 1);
      std::string service = queryEvt.serverList[0].services[0].c_str();
      ASSERT_EQ(service, xmppAgentServiceId);

      server= queryEvt.serverList[1].server.server;
      region = queryEvt.serverList[1].server.region;
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(queryEvt.serverList[1].services.size(), 1);
      service = queryEvt.serverList[1].services[0].c_str();
      ASSERT_EQ(service, pushNotificationEndpointServiceId);
   });

   waitForMs(watchdogQueryEvent, std::chrono::milliseconds(300000));

   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

TEST_F(WatchdogServerTests, VerifyServerUpPushNotificationAndServerDown)
{
   TestAccount watchdog("watchdog", Account_Init);
   CPCAPI2::CloudWatchdog::CloudWatchdogConfig watchdogConfig;
   XmppTestAccountConfig::getDefaultWatchdogConfig(watchdogConfig);
   watchdogConfig.monitorFrequency = 10;
   
   auto notificationEvent = std::async(std::launch::async, [&]()
   {
      XmppTestAccountConfig::setupWatchdogServer(watchdog, watchdogConfig);
      
      unsigned int requestHandle = 0;
      
      CPCAPI2::CloudWatchdog::WatchdogStartEvent args;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onWatchdogStart", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, args));
      ASSERT_EQ(args.serverList.size(), 0);
      
      CPCAPI2::OrchestrationServer::QueryServersResult orchResult;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 0);
      
      XmppTestAccountConfig::setCloudServicesInfo(watchdog.phone, true, true);
      
      CPCAPI2::CloudWatchdog::NotificationSent notificationSent;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onNotificationSent", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, notificationSent));
      ASSERT_EQ(notificationSent.state, CPCAPI2::CloudWatchdog::NotificationSent::UP);
      ASSERT_EQ(notificationSent.type, CPCAPI2::CloudWatchdog::NotificationSent::REGISTER);
      
      // Stop ttl service so that the TTL times out, to mimic a down xmpp agent
      cpc::vector<CPCAPI2::CloudConnector::ServiceDesc> services;
      XmppTestAccountConfig::getDefaultCloudServices(services);
      CPCAPI2::CloudServiceConfig::CloudServiceConfigManager* cloudConfigMgr = CPCAPI2::CloudServiceConfig::CloudServiceConfigManager::getInterface(watchdog.phone);
      cloudConfigMgr->stopTtlService();
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.orchestrationServerEvents, "OrchestrationServerHandler::onQueryServersResult", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, orchResult));
      ASSERT_EQ(orchResult.servers.size(), 2);
      unsigned int ttl = (unsigned int)orchResult.servers[0].ttlSeconds + (3 * (watchdogConfig.monitorFrequency * 1000));

      std::string auth("");
      std::string orch("");
      std::string json("");
      XmppTestAccountConfig::getWatchdogServerUrls(auth, orch, json);
      std::string xmppAgentServiceId = CPCAPI2::XmppAgent::XmppAgentManager::getServiceId().c_str();
      std::string pushNotificationEndpointServiceId = CPCAPI2::PushEndpoint::PushNotificationEndpointManager::getServiceId().c_str();
      
      CPCAPI2::CloudWatchdog::ServerDownEvent downEvt;
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerDown", ttl, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      std::string server(downEvt.server.server.server);
      std::string region(downEvt.server.server.region);
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      std::string service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, pushNotificationEndpointServiceId);
      
      ASSERT_TRUE(cpcWaitForEvent(watchdog.cloudWatchdogServerEvents, "CloudWatchdogHandler::onServerDown", 30000, CPCAPI2::test::AlwaysTruePred(), requestHandle, downEvt));
      server = downEvt.server.server.server;
      region = downEvt.server.server.region;
      ASSERT_EQ(server, json);
      ASSERT_EQ(region, XMPP_AGENT_REGION);
      ASSERT_EQ(downEvt.server.services.size(), 1);
      service = downEvt.server.services[0].c_str();
      ASSERT_EQ(service, xmppAgentServiceId);
   });
   
   waitForMs(notificationEvent, std::chrono::milliseconds(600000));
   
   XmppTestAccountConfig::destroyWatchdogServer(watchdog);
}

#endif
