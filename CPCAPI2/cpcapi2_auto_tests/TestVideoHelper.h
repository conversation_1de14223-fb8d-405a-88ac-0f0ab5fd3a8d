#pragma once

#include <gtest/gtest.h>
#include "cocoa_helpers.h"
#include "x11_helpers.h"

#define WINDOW_HEIGHT 288
#define WINDOW_WIDTH 352
#define OFFSET 20

namespace {
#if _WIN32
   LRESULT CALLBACK ViEAutoTestWinProc(HWND hWnd, UINT uMsg, WPARAM wParam,
      LPARAM lParam)
   {
      switch (uMsg) {
      case WM_DESTROY:
         PostQuitMessage(WM_QUIT);
         break;
      case WM_COMMAND:
         break;
      }
      return DefWindowProc(hWnd, uMsg, wParam, lParam);
   }

   int ViECreateWindow(HWND &hwndMain, int xPos,
      int yPos, int width, int height,
      TCHAR* className, HWND hwndUnused = 0)
   {
      HINSTANCE hinst = GetModuleHandle(0);
      WNDCLASSEX wcx;
      wcx.hInstance = hinst;
      wcx.lpszClassName = className;
      wcx.lpfnWndProc = (WNDPROC)ViEAutoTestWinProc;
      wcx.style = CS_DBLCLKS;
      wcx.hIcon = LoadIcon(NULL, IDI_APPLICATION);
      wcx.hIconSm = LoadIcon(NULL, IDI_APPLICATION);
      wcx.hCursor = LoadCursor(NULL, IDC_ARROW);
      wcx.lpszMenuName = NULL;
      wcx.cbSize = sizeof(WNDCLASSEX);
      wcx.cbClsExtra = 0;
      wcx.cbWndExtra = 0;
      wcx.hbrBackground = GetSysColorBrush(COLOR_3DFACE);

      RegisterClassEx(&wcx);

      // Create the main window.
      hwndMain = CreateWindowEx(0,          // no extended styles
         className,  // class name
         className,  // window name
         WS_OVERLAPPED | WS_THICKFRAME,  // overlapped window
         xPos,    // horizontal position
         yPos,    // vertical position
         width,   // width
         height,  // height
         (HWND)NULL,   // no parent or owner window
         (HMENU)NULL,  // class menu used
         hinst,  // instance handle
         NULL);  // no window creation data

      if (!hwndMain)
         return -1;

      // Show the window using the flag specified by the program
      // that started the application, and send the application
      // a WM_PAINT message.
      ShowWindow(hwndMain, SW_SHOWDEFAULT);
      UpdateWindow(hwndMain);

      ::SetWindowPos(hwndMain, HWND_TOP, xPos, yPos, width, height,
         SWP_FRAMECHANGED);

      return 0;
   }

   typedef HWND CpTestWindowHandle;

   void ViEDestroyWindow(CpTestWindowHandle unusedHwnd, CpTestWindowHandle hwndVideo)
   {
      ::DestroyWindow(hwndVideo);
   }

   time_t TestWindowSecondsSinceLastRender(CpTestWindowHandle videoWindow)
   {
      // not supported
      return 0;
   }

#elif defined(__APPLE__)
   typedef void* CpTestWindowHandle;

   int ViECreateWindow(CpTestWindowHandle& videoLayer, int xPos,
      int yPos, int width, int height,
      const char* className, CpTestWindowHandle& vidWindow)
   {
      CPCAPI2::test::WindowAndLayerHolder holder = CPCAPI2::test::TestCocoaApp::createWindow(xPos, yPos, width, height, className);
      videoLayer = holder.layer;
      vidWindow = holder.window;
      return 0;
   }

   void ViEDestroyWindow(CpTestWindowHandle videoWindow, CpTestWindowHandle videoLayer)
   {
      CPCAPI2::test::TestCocoaApp::destroyWindow(videoWindow);
   }

   long TestWindowSecondsSinceLastRender(CpTestWindowHandle videoWindow)
   {
      return CPCAPI2::test::TestCocoaApp::secondsSinceLastRender(videoWindow);
   }

#elif defined( __linux__ ) && !defined( ANDROID )
   typedef void* CpTestWindowHandle;

   int ViECreateWindow(CpTestWindowHandle& window, int xPos,
      int yPos, int width, int height,
      const char* className, CpTestWindowHandle& display)
   {
      CPCAPI2::test::WindowAndDisplayHolder holder = CPCAPI2::test::TestX11App::createWindow(xPos, yPos, width, height, className);
      if (holder.window == nullptr || holder.display == nullptr)
      {
         return -1;
      }
      window = holder.window;
      display = holder.display;
      return 0;
   }

   void ViEDestroyWindow(CpTestWindowHandle display, CpTestWindowHandle window)
   {
	   CPCAPI2::test::TestX11App::destroyWindow(display, window);
   }

   time_t TestWindowSecondsSinceLastRender(CpTestWindowHandle videoWindow)
   {
      // not supported
      return 0;
   }
#elif defined(ANDROID)
typedef void* CpTestWindowHandle;

int ViECreateWindow(CpTestWindowHandle& window, int xPos,
   int yPos, int width, int height,
   const char* className, CpTestWindowHandle& display)
{
   return 0;
}

void ViEDestroyWindow(CpTestWindowHandle display, CpTestWindowHandle window)
{
}

time_t TestWindowSecondsSinceLastRender(CpTestWindowHandle videoWindow)
{
   // not supported
   return 0;
}

#else // Default empty functions to avoid build errors.
typedef void* CpTestWindowHandle;

int ViECreateWindow(CpTestWindowHandle& videoLayer, int xPos,
   int yPos, int width, int height,
   const char* className, CpTestWindowHandle& vidWindow)
{
   // CPCAPI2::test::WindowAndLayerHolder holder = CPCAPI2::test::TestCocoaApp::createWindow(xPos, yPos, width, height, className);
   // videoLayer = holder.layer;
   // vidWindow = holder.window;
   return 0;
}

void ViEDestroyWindow(CpTestWindowHandle videoWindow, CpTestWindowHandle videoLayer)
{
   // CPCAPI2::test::TestCocoaApp::destroyWindow(videoWindow);
}

long TestWindowSecondsSinceLastRender(CpTestWindowHandle videoWindow)
{
   return 0; // CPCAPI2::test::TestCocoaApp::secondsSinceLastRender(videoWindow);
}

#endif

}
class TestAccount;

namespace CPCAPI2
{
   namespace test
   {
      class TestVideoHelper
      {
      public:
         TestVideoHelper(const std::string& name);
         ~TestVideoHelper();
         void startVideo(bool createLocalWindow = true, bool createIncomingWindow = true);
         CpTestWindowHandle getHwndLocalWindow() {return hwndLocalWindow;}
         CpTestWindowHandle getHwndIncomingWindow() { return hwndIncomingWindow; }
         CpTestWindowHandle getNsLocalWindow() { return nsLocalWindow; }
         CpTestWindowHandle getNsIncomingWindow() { return nsIncomingWindow; }
         void setTestAccount(TestAccount* account) { testAccount = account; }
         time_t LocalWindowSecondsSinceLastRender() { return TestWindowSecondsSinceLastRender(nsLocalWindow); }
         time_t IncomingWindowSecondsSinceLastRender() { return TestWindowSecondsSinceLastRender(nsIncomingWindow); }
         
         static void setFixedLocalVideoRenderHandle(CpTestWindowHandle h) { sFixedLocalVideoRenderHandle = h; }
         static void setFixedRemoteVideoRenderHandle(CpTestWindowHandle h) { sFixedRemoteVideoRenderHandle = h; }
      private:
         CpTestWindowHandle hwndLocalWindow;
         CpTestWindowHandle hwndIncomingWindow;
         CpTestWindowHandle nsLocalWindow;
         CpTestWindowHandle nsIncomingWindow;
         unsigned int xPosition;
         unsigned int yPosition;
         static unsigned int accountCount;
         unsigned int accountIndex;
         int localOffset;
         std::string accountName;
         TestAccount* testAccount;
         bool ownsFixedLocalVideoRenderHandle;
         bool ownsFixedRemoteVideoRenderHandle;
         static CpTestWindowHandle sFixedLocalVideoRenderHandle;
         static CpTestWindowHandle sFixedRemoteVideoRenderHandle;
         static std::atomic_bool sFixedLocalVideoRenderInuse;
         static std::atomic_bool sFixedRemoteVideoRenderInuse;
      };
   }
}



