#pragma once

#include <string>
#include <thread>
#include <future>

namespace CPCAPI2
{
   class SpeechQualityTestConfig
   {
   public:
      int referenceWavLengthSeconds;
      std::string referenceWavFilename;
      std::string outputWavFilename;
      std::string outputSummaryFilename;

      SpeechQualityTestConfig()
      {
         referenceWavFilename = "ctclip316kfixed15secStereo.wav";
         outputWavFilename = "speechQualityOutput.wav";
         outputSummaryFilename = "speechQualityTestSummary.txt";

         referenceWavLengthSeconds = 17;
      }
   };

   class SpeechQualityTestResult
   {
   public:
      bool exception;
      float pesqmos;
      float moslqo;
      
      static SpeechQualityTestResult makeFailure()
      {
         SpeechQualityTestResult result;
         result.exception = true;
         result.pesqmos = 0;
         result.moslqo = 0;

         return result;
      }

      static SpeechQualityTestResult makeSuccess(float pesqmos, float moslqo)
      {
         SpeechQualityTestResult result;
         result.exception = false;
         result.pesqmos = pesqmos;
         result.moslqo = moslqo;

         return result;
      }

   };

   class SpeechQualityTestRunner
   {
   public:
      void configure(const SpeechQualityTestConfig& config);
      SpeechQualityTestResult runTest();
   private:
      SpeechQualityTestConfig mConfig;
   };
}
