#ifdef ANDROID
#include <android/log.h>
#include <stdio.h>
#include <unistd.h>
#include <pthread.h>
#include <fstream>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <assert.h>
#include "stdoutToFileRedirector.h"

using namespace CPCAPI2;

int StdoutToFileRedirector::pfd[2];
const char* StdoutToFileRedirector::LOGOUTPUT = "  CPCAPI2_AUTOTESTS";
const char kStdoutWriteComplete = 0x7;


StdoutToFileRedirector::StdoutToFileRedirector(const std::string& filePath) :
   mShutdown(false),
   mRedirectDebugLoggingEnabled(false),
   mFilePath(filePath)
{
   runLoggingThread();
}

void StdoutToFileRedirector::setRedirectDebugLoggingEnabled(bool enabled)
{
   mRedirectDebugLoggingEnabled = enabled;
}

StdoutToFileRedirector::~StdoutToFileRedirector()
{
   // we want to ensure we capture the tail end of stdout logging near process exit --
   // because that would typically include gtest output regarding test result.
   // in order to know when to exit the loop in thread_func, we write a special
   // character (that hopefully is not used during regular logging) to stdout.
   mShutdown = true;
   if (mRedirectDebugLoggingEnabled)
   {
      std::cout << "[stdout write complete]" << std::endl;
   }
   std::cout << kStdoutWriteComplete << std::flush;

   pthread_join(thr, NULL);
   
   close(pfd[0]);
   close(pfd[1]);
}
void* StdoutToFileRedirector::thread_func(void* arg)
{
   StdoutToFileRedirector* self = reinterpret_cast<StdoutToFileRedirector*>(arg);

   std::ofstream alog;
   alog.open(self->mFilePath, std::ios_base::app);
   ssize_t readSize;
   char buf[1024];
   const int maxRequestedReadSize = sizeof(buf) - 1;
   while ((readSize = read(pfd[0], buf, maxRequestedReadSize)) > 0)
   {
      // note that there will be no timestamp printed for gtest log lines.
      // originally this code added a timestamp (in addition to timestamp emitted from
      // SDK logging) but this insertion of timestamp here is problematic because it
      // can cause stdout/stderr output to be non-contiguous
   
      bool stop = false;
      if (self->mShutdown)
      {
         for (int i = 0; i < readSize; ++i)
         {
            if (buf[i] == kStdoutWriteComplete)
            {
               if (self->mRedirectDebugLoggingEnabled)
               {
                  __android_log_print(ANDROID_LOG_INFO, LOGOUTPUT, "========>>> Found write complete char <<<========");
               }
               buf[i] = '\0'; // don't print the control character
               stop = true;
            }
         }
      }
      
      buf[readSize] = '\0';
      alog << buf;
      
      if (stop)
      {
         break;
      }
   }
   
   if (self->mRedirectDebugLoggingEnabled)
   {
      alog << "[log write complete]";
   }
   
   return 0;
}
void StdoutToFileRedirector::runLoggingThread(){
   /* make stdout line-buffered and stderr unbuffered */
   setvbuf(stdout, 0, _IOLBF, 0);
   setvbuf(stderr, 0, _IONBF,0);
   /*create the pipe and redirect stdout and stderr */
   pipe(pfd);
   dup2(pfd[1], 1);
   dup2(pfd[1], 2);

   /*spawn the logging thread */
   if(pthread_create(&thr, 0, thread_func, this)== -1)
      __android_log_print(ANDROID_LOG_ERROR, LOGOUTPUT, "========>>> The pthread_create() for logging failed! <<<========");
   if ((errNum = pthread_detach(thr)) != 0)
   {
      __android_log_print(ANDROID_LOG_ERROR, LOGOUTPUT, "========>>> The pthread_detach() for logging failed with error code %d <<<========", errNum);
   }
   pthread_detach(thr);
}

#endif
