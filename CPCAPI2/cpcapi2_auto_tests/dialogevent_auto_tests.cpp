#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_call_events.h"

#include <thread>
#include <future>

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;

namespace {

class DialogEventTest : public CpcapiAutoTest
{
public:
   DialogEventTest() {}
   virtual ~DialogEventTest() {}
};

class DialogEventTestAccount : public TestAccount
{
public:
   DialogEventTestAccount(const std::string& name) : TestAccount(name, Account_Init)
   {
      dialogEventSubscriptionManager = SipDialogEventSubscriptionManager::getInterface(phone);
      dialogEventSubscriptionManager->setHandler(handle, (SipDialogEventSubscriptionHandler*) 0xDEADBEEF);
      dialogEventSubscriptionEvents = new test::EventHandler((config.name).c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(dialogEventSubscriptionManager));
      enable();
   }

   SipDialogEventSubscriptionManager* dialogEventSubscriptionManager;
   CPCAPI2::test::EventHandler* dialogEventSubscriptionEvents;
};

TEST_F(DialogEventTest, SubscribeFailureInvalidAccount)
{
   DialogEventTestAccount alice("alice");

   // Create a new subscription using an invalid account
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(123);

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.phoneEvents->expectEvent("PhoneHandler::onError", 15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "SipEventManagerInterface::createSubscription called with invalid account handle: 123, SipEventSubscriptionHandle invalid: " + cpc::to_string(aliceSubs));
}

TEST_F(DialogEventTest, SubscribeFailureAccountDisabled)
{
   DialogEventTestAccount alice("alice");
   alice.account->disable(alice.handle);

   // Create a new subscription using a disabled account
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);

   // Expect an error
   PhoneErrorEvent evt;
   cpc::string module;
   ASSERT_TRUE(alice.phoneEvents->expectEvent("PhoneHandler::onError", 15000, StrEqualsPred("SipAccountInterface"), module, evt));
   ASSERT_EQ(evt.errorText, "SipEventManagerInterface::createSubscription called before account enabled: " + cpc::to_string(alice.handle) + 
                              ", SipEventSubscriptionHandle invalid: " + cpc::to_string(aliceSubs));
}

TEST_F(DialogEventTest, DISABLED_SubscribeFailureNoParticipant) 
{
   DialogEventTestAccount alice("alice");

   // Start a subscription without any participant
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->start(aliceSubs);

   // Expect an error
   SipDialogEventSubscriptionHandle h;
   SipEvent::ErrorEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.subsEvents, "SipEventSubscriptionHandler::onError",
      5000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(h, aliceSubs);
	ASSERT_EQ(evt.errorText, "Cannot start subscription. No participants have been added");

   alice.dialogEventSubscriptionManager->end(aliceSubs);
}

TEST_F(DialogEventTest, DISABLED_SubscribeFailureInvalidParticipantAddress)
{
   DialogEventTestAccount alice("alice");

   // Start a subscription with an invalid participant address
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->addParticipant(aliceSubs, "b");
   alice.dialogEventSubscriptionManager->start(aliceSubs);

   // Wait for the subscription state to transition to Terminated
	SipDialogEventSubscriptionHandle h;
	DialogEventSubscriptionEndedEvent evt;
   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
		15000,
		AlwaysTruePred(), h, evt));
	ASSERT_EQ(aliceSubs, h);
   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
}

TEST_F(DialogEventTest, DISABLED_SubscribeRejected)
{
   DialogEventTestAccount alice("alice");
   DialogEventTestAccount bob("bob");

   // Alice subscribes to Bob's calls
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->addParticipant(aliceSubs, bob.config.uri());
	alice.dialogEventSubscriptionManager->start(aliceSubs);

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
  		ASSERT_EQ(bob.dialogEventSubscriptionManager->reject(bobSubs, 486), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Terminated);
      }

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(DialogEventTest, DISABLED_SubscribeAcceptedNoCall)
{
   DialogEventTestAccount alice("alice");
   DialogEventTestAccount bob("bob");

   // Alice subscribes to Bob's calls
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->addParticipant(aliceSubs, bob.config.uri());
	alice.dialogEventSubscriptionManager->start(aliceSubs);

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
  		ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for a new subscription
		   SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
		   ASSERT_EQ(h, aliceSubs);
		   ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		   ASSERT_EQ(evt.remoteAddress, bob.config.uri());
      }

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         // Wait for a NOTIFY
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 0);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 0);
      }

      // Terminate the subscription
      ASSERT_EQ(alice.dialogEventSubscriptionManager->end(aliceSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(DialogEventTest, DISABLED_SubscribeAcceptedNewCall)
{
   DialogEventTestAccount alice("alice");
   DialogEventTestAccount bob("bob");
   DialogEventTestAccount charlie("charlie");

   // Alice subscribes to Bob's calls
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->addParticipant(aliceSubs, bob.config.uri());
   alice.dialogEventSubscriptionManager->start(aliceSubs);

   auto bobEvents = std::async(std::launch::async, [&] () {
		SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
  		ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // Bob calls Charlie then Charlie hangs up
      SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCall, charlie.config.uri());
      bob.conversation->start(bobCall);
      auto bobConversationEvents = std::async(std::launch::async, [&] () {
         // Wait for the expected state changes and for the call to end
         assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
         assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);   
      });
      auto charlieConversationEvents = std::async(std::launch::async, [&] () {
         // Wait for the expected state changes and for the call to end
         SipConversationHandle charlieCall;
         assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
         charlie.conversation->sendRingingResponse(charlieCall);
         assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
         charlie.conversation->accept(charlieCall);
         assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
         assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
         charlie.conversation->end(charlieCall);
         assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
      });
      waitFor2(bobConversationEvents, charlieConversationEvents);

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
     }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for a new subscription
		   SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
		   ASSERT_EQ(h, aliceSubs);
		   ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		   ASSERT_EQ(evt.remoteAddress, bob.config.uri());
      }

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         // Wait for a NOTIFY
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 0);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 0);
      }

      {
         // Wait for the Trying state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 1);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Trying);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.reason, DialogStateReason_NotSpecified);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.code, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.callId.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.localTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].dialogId.remoteTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].referredBy.address.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].routeSet.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.callId.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.localTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.remoteTag.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.cseq, 0);
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
      }

      {
         // Wait for the Early state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 2);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Early);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.reason, DialogStateReason_NotSpecified);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.code, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.callId.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.localTag.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.remoteTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].referredBy.address.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].routeSet.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.callId.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.localTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.remoteTag.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
      }

      {
         // Wait for the Confirmed state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 3);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Confirmed);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.reason, DialogStateReason_NotSpecified);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.code, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.callId.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.localTag.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.remoteTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].referredBy.address.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].routeSet.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.callId.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.localTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.remoteTag.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
      }

      {
         // Wait for the Terminated state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 4);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Terminated);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.reason, DialogStateReason_RemoteBye);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.code, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.callId.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.localTag.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].dialogId.remoteTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].referredBy.address.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].routeSet.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.callId.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.localTag.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].replaces.remoteTag.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.cseq, 0);
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.params.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
      }

      // Terminate the subscription
      ASSERT_EQ(alice.dialogEventSubscriptionManager->end(aliceSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());
}

// Test case disabled because when including the SDP on the dialog-info XML the underyling UDP packets
// sometimes exceed the 1400 byte limit and in that case the proxy may send a 513 - Message Too Large SIP response.
// 
TEST_F(DialogEventTest, DISABLED_SubscribeWithSdpParameterAcceptedNewCall)
{
   DialogEventTestAccount alice("alice");
   DialogEventTestAccount bob("bob");
   DialogEventTestAccount charlie("charlie");

   // Alice subscribes to Bob's calls
   SipDialogEventSubscriptionHandle aliceSubs = alice.dialogEventSubscriptionManager->createSubscription(alice.handle);
   SipDialogEventSubscriptionSettings subsSettings;
   subsSettings.includeSessionDescription = true;
   alice.dialogEventSubscriptionManager->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.dialogEventSubscriptionManager->addParticipant(aliceSubs, bob.config.uri());
	alice.dialogEventSubscriptionManager->start(aliceSubs);

	auto bobEvents = std::async(std::launch::async, [&] () {
		SipDialogEventSubscriptionHandle bobSubs = 0;
      {
         // Wait for a new subscription
         SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   5000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, bob.handle);
         bobSubs = h;
			ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
			ASSERT_EQ(evt.remoteAddress, alice.config.uri());
      }

      // Accept the subscription
  		ASSERT_EQ(bob.dialogEventSubscriptionManager->accept(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      // Bob calls Charlie then charlie hangs up
      SipConversationHandle bobCall = bob.conversation->createConversation(bob.handle);
      bob.conversation->addParticipant(bobCall, charlie.config.uri());
      bob.conversation->start(bobCall);
      auto bobConversationEvents = std::async(std::launch::async, [&] () {
         // Wait for the expected state changes and for the call to end
         assertNewConversationOutgoing(bob, bobCall, charlie.config.uri());
         assertConversationStateChanged(bob, bobCall, ConversationState_RemoteRinging);
         assertConversationMediaChanged(bob, bobCall, MediaDirection_SendReceive);
         assertConversationStateChanged(bob, bobCall, ConversationState_Connected);
         assertConversationEnded(bob, bobCall, ConversationEndReason_UserTerminatedRemotely);   

         {
            // The Terminated state transition for the call between Charlie and Bob failed sending because
            // the dialog-info document is too large for the UDP transport
   	      SipDialogEventSubscriptionHandle h;
               NotifyDialogInfoFailureEvent evt;
		         ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNotifyDialogInfoFailure",
			         5000,
			         AlwaysTruePred(), h, evt));
               ASSERT_EQ(evt.sipResponseCode, 513);
         }
      });
      auto charlieConversationEvents = std::async(std::launch::async, [&] () {
         // Wait for the expected state changes and for the call to end
         SipConversationHandle charlieCall;
         assertNewConversationIncoming(charlie, &charlieCall, bob.config.uri());
         charlie.conversation->sendRingingResponse(charlieCall);
         assertConversationStateChanged(charlie, charlieCall, ConversationState_LocalRinging);
         charlie.conversation->accept(charlieCall);
         assertConversationMediaChanged(charlie, charlieCall, MediaDirection_SendReceive);
         assertConversationStateChanged(charlie, charlieCall, ConversationState_Connected);
         charlie.conversation->end(charlieCall);
         assertConversationEnded(charlie, charlieCall, ConversationEndReason_UserTerminatedLocally);
      });
      waitFor2(bobConversationEvents, charlieConversationEvents);

      // Terminate the subscription
      ASSERT_EQ(bob.dialogEventSubscriptionManager->end(bobSubs), kSuccess);

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(bob.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(bobSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
      {
         // Wait for a new subscription
		   SipDialogEventSubscriptionHandle h;
		   NewDialogEventSubscriptionEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onNewSubscription",
			   15000,
			   AlwaysTruePred(), h, evt));
         ASSERT_EQ(evt.account, alice.handle);
		   ASSERT_EQ(h, aliceSubs);
		   ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		   ASSERT_EQ(evt.remoteAddress, bob.config.uri());
      }

      {
         // Wait for the subscription state to transition to Active
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
         // Wait for a NOTIFY
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 0);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 0);
      }

      {
         // Wait for the Trying state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 1);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Trying);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType, "application/sdp");
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
      }

      {
         // Wait for the Early state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 2);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Early);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
         ASSERT_TRUE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
      }

      {
         // Wait for the Confirmed state transition for the call between Charlie and Bob
			SipDialogEventSubscriptionHandle h;
			IncomingDialogInfoEvent evt;
		   ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onIncomingDialogInfo",
			   5000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
         ASSERT_EQ(evt.dialogInfoDoc.version, 3);
         ASSERT_EQ(evt.dialogInfoDoc.state, DialogInfoDocumentState_Full);
         ASSERT_EQ(evt.dialogInfoDoc.entity, bob.config.uri());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs.size(), 1);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].direction, DialogDirection_Initiator);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].stateInfo.state, DialogState_Confirmed);
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].localParticipant.identities[0].address, bob.config.uri());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.target.uri.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.contentType.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].localParticipant.sessionDescription.empty());
         ASSERT_EQ(evt.dialogInfoDoc.dialogs[0].remoteParticipant.identities[0].address, charlie.config.uri());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.target.uri.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.contentType.empty());
         ASSERT_FALSE(evt.dialogInfoDoc.dialogs[0].remoteParticipant.sessionDescription.empty());
      }

      // The other dialog-info notification (Terminated) is not received because the dialog-info 
      // document in the outgoing SIP NOTIFY is too large for the UDP transport

      {
         // Wait for the subscription state to transition to Terminated
			SipDialogEventSubscriptionHandle h;
			DialogEventSubscriptionEndedEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.dialogEventSubscriptionEvents, "SipDialogEventSubscriptionHandler::onSubscriptionEnded",
			   15000,
			   AlwaysTruePred(), h, evt));
			ASSERT_EQ(aliceSubs, h);
			ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());
}

}

#endif // CPCAPI2_BRAND_SIP_DIALOG_EVENT_MODULE
