// bliu: how to simulate 480 and 404 from server

#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_RCSPROVISIONING_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "../../impl/provision/RcsProvisionInterface.h"

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"

#include <thread>
#include <future>
#include <string>
#include <set>
#include <fstream>
#include <iostream>

//#include <curl/curl.h>

using namespace CPCAPI2;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::RcsProvision;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::test;

extern "C" int _ExtractProvisionOptions(const std::string& xml, CPCAPI2::RcsProvision::RcsProvisionOptions& options);
extern "C" size_t _RecvData(char* ptr, size_t size, size_t nmemb, cpc::vector<char>* buffer);

namespace {

class ProvisionModuleTest : public CpcapiAutoTest
{
public:
   ProvisionModuleTest() {}
   virtual ~ProvisionModuleTest() {}
};

#if 0
TEST_F(ProvisionModuleTest, Cookie) {
  CURL *curl;
  CURLcode res;

  curl_global_init(CURL_GLOBAL_ALL);
  curl = curl_easy_init();
  if (curl) {
    char nline[256];

    curl_easy_setopt(curl, CURLOPT_URL, "http://www.example.com/");
    curl_easy_setopt(curl, CURLOPT_VERBOSE, 1L);
    curl_easy_setopt(curl, CURLOPT_COOKIEFILE, ""); /* just to start the cookie engine */ 
    res = curl_easy_perform(curl);
    if (res != CURLE_OK) {
      fprintf(stderr, "Curl perform failed: %s\n", curl_easy_strerror(res));
      return;
    }

    print_cookies(curl);

    std::cout << "Erasing curl's knowledge of cookies!" << std::endl;
    curl_easy_setopt(curl, CURLOPT_COOKIELIST, "ALL"); /* clean cookie list */

    print_cookies(curl);

    std::cout << ("-----------------------------------------------" << std::endl <<
           "Setting a cookie \"PREF\" via cookie interface:" << std::endl;

    /* Netscape format cookie */
    snprintf(nline, sizeof(nline), "%s\t%s\t%s\t%s\t%lu\t%s\t%s",
      ".google.com", "TRUE", "/", "FALSE", (unsigned long)time(NULL) + 31337UL, "PREF", "hello google, i like you very much!");
    res = curl_easy_setopt(curl, CURLOPT_COOKIELIST, nline);
    if (res != CURLE_OK) {
      fprintf(stderr, "Curl curl_easy_setopt failed: %s\n", curl_easy_strerror(res));
      return;
    }

    /* HTTP-header style cookie */ 
    snprintf(nline, sizeof(nline),
      "Set-Cookie: OLD_PREF=3d141414bf4209321; "
      "expires=Sun, 17-Jan-2038 19:14:07 GMT; path=/; domain=.google.com");
    res = curl_easy_setopt(curl, CURLOPT_COOKIELIST, nline);
    if (res != CURLE_OK) {
      fprintf(stderr, "Curl curl_easy_setopt failed: %s\n", curl_easy_strerror(res));
      return;
    }

    print_cookies(curl);

    res = curl_easy_perform(curl);
    if (res != CURLE_OK) {
      fprintf(stderr, "Curl perform failed: %s\n", curl_easy_strerror(res));
      return;
    }
  }
  else {
    fprintf(stderr, "Curl init failed!\n");
    return;
  }

  curl_global_cleanup();
  return;
}
#endif

#if 0
TEST_F(ProvisionModuleTest, ProvisionVendor) {
   CURLcode res;

   auto curl = curl_easy_init();
   ASSERT_TRUE(curl);

   std::cout << "\r\nhttp" << std::endl;
   curl_easy_setopt(curl, CURLOPT_URL, "http://config.rcs.mnc001.mcc208.pub.3gppnetwork.org");
   //curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
   curl_easy_setopt(curl, CURLOPT_COOKIEFILE, ""); /* just to start the cookie engine */ 
   res = curl_easy_perform(curl);
   if (res != CURLE_OK) std::cout << "Curl perform failed: " << curl_easy_strerror(res) << std::endl;
   ASSERT_EQ(res, CURLE_OK);

   curl_slist* cookies;
   res = curl_easy_getinfo(curl, CURLINFO_COOKIELIST, &cookies);
   if (res != CURLE_OK) std::cout << "Curl curl_easy_getinfo failed: " << curl_easy_strerror(res)  << std::endl;
   ASSERT_EQ(res, CURLE_OK);

   cpc::vector<char> charSet;
   for (char c = 'A'; c <= 'Z'; ++c) charSet.push_back(c);
   for (char c = 'a'; c <= 'z'; ++c) charSet.push_back(c);

   cpc::vector<char> allSet = charSet;
   for (char c = '0'; c <= '9'; ++c) allSet.push_back(c);

   std::ofstream of ("vendor.txt");

   for (char c1 : charSet)
   for (char c2 : charSet)
   for (char c3 : charSet)
   for (char c4 : allSet)
   {
      std::string url = "https://config.rcs.mnc001.mcc208.pub.3gppnetwork.org?client_version=RCSAndrd-1.0&vers=0&IMEI=355794051948346&IMSI=208011701265577&client_vendor=";
      std::string vendor;
      vendor += c1;
      vendor += c2;
      vendor += c3;
      vendor += c4;
      url += vendor;

      cpc::vector<char> buffer;
      curl_easy_setopt(curl, CURLOPT_URL, url.c_str()); //terminal_vendor=Samsung&terminal_model=SGH-I337M&terminal_sw_version=4.2.2
      curl_easy_setopt(curl, CURLOPT_COOKIELIST, cookies);
      curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, FALSE); // disable cert checking
      curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, _RecvData);
      curl_easy_setopt(curl, CURLOPT_WRITEDATA, &buffer);

      res = curl_easy_perform(curl);
      if (res != CURLE_OK) std::cout << "Curl perform failed: " << curl_easy_strerror(res) << std::endl;
      ASSERT_EQ(res, CURLE_OK);

      std::string s (&buffer[0], buffer.size());
      if (s != "CLIENT_NOT_IN_WHITE_LIST")
      {
         std::cout << vendor << " - " << s << std::endl;
      }

      of << vendor << " - " << s << std::endl;

      std::this_thread::sleep_for(std::chrono::milliseconds(100));
   }

   curl_slist_free_all(cookies);
   curl_easy_cleanup(curl);
}
#endif

#if 0
TEST_F(ProvisionModuleTest, BasicProvision) {
   CURLcode res;

   auto curl = curl_easy_init();
   ASSERT_TRUE(curl);

   std::cout << "\r\nhttp" << std::endl;
   curl_easy_setopt(curl, CURLOPT_URL, "http://config.rcs.mnc001.mcc208.pub.3gppnetwork.org");
   //curl_easy_setopt(curl, CURLOPT_VERBOSE, 0L);
   curl_easy_setopt(curl, CURLOPT_COOKIEFILE, ""); /* just to start the cookie engine */ 
   res = curl_easy_perform(curl);
   if (res != CURLE_OK) std::cout << "Curl perform failed: " << curl_easy_strerror(res) << std::endl;
   ASSERT_EQ(res, CURLE_OK);

   curl_slist* cookies;
   res = curl_easy_getinfo(curl, CURLINFO_COOKIELIST, &cookies);
   if (res != CURLE_OK) std::cout << "Curl curl_easy_getinfo failed: " << curl_easy_strerror(res)  << std::endl;
   ASSERT_EQ(res, CURLE_OK);

   std::string url = "https://config.rcs.mnc001.mcc208.pub.3gppnetwork.org?rcs_version=5.1&client_version=RCSAndrd-1.0&vers=0&IMEI=355794051948346&IMSI=208011701265577&client_vendor=";
   std::string vendor = "CPAH";
   url += vendor;

   cpc::vector<char> buffer;
   curl_easy_setopt(curl, CURLOPT_URL, url.c_str()); //terminal_vendor=Samsung&terminal_model=SGH-I337M&terminal_sw_version=4.2.2
   curl_easy_setopt(curl, CURLOPT_COOKIELIST, cookies);
   curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, FALSE); // disable cert checking
   curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, _RecvData);
   curl_easy_setopt(curl, CURLOPT_WRITEDATA, &buffer);

   res = curl_easy_perform(curl);
   if (res != CURLE_OK) std::cout << "Curl perform failed: " << curl_easy_strerror(res) << std::endl;
   ASSERT_EQ(res, CURLE_OK);

   std::string s (&buffer[0], buffer.size());
   if (s != "CLIENT_NOT_IN_WHITE_LIST")
   {
      std::cout << vendor << " - " << s << std::endl;
   }

   curl_slist_free_all(cookies);
   curl_easy_cleanup(curl);
}
#endif

#if 1
// test a basic capability discovery sent from alice to bob
TEST_F(ProvisionModuleTest, BasicProvisionOption) {
   std::ifstream in ("SampleProvisionXml.xml");
   assert(in.is_open());

   std::ostringstream iss;
   iss << in.rdbuf() << std::flush;

   CPCAPI2::RcsProvision::RcsProvisionOptions options;
   _ExtractProvisionOptions(iss.str(), options);

   TestAccount dummy ("");
}
#endif

}

#endif // CPCAPI2_PROVISION_MODULE
