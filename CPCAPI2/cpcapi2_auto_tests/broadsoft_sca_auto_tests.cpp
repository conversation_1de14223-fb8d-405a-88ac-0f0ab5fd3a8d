#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_SIP_SHARED_CALL_APPEARANCE_MODULE == 1)

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/bare_bones_sip_endpoint.h"
#include "test_account_events.h"
#include "test_call_events.h"

#include <thread>
#include <future>

/* Toggle between Broadsoft/Megapath accounts.
 * Both use BroadWorks platforms.
 */
#define USE_BROADSOFT_ACCOUNTS 1

using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::SipConversation;
using namespace CPCAPI2::SipDialogEvent;
using namespace CPCAPI2::SipSharedCallAppearance;

namespace {

#if (USE_BROADSOFT_ACCOUNTS == 1)

class ScaAccount : public TestAccount
{
public:
   ScaAccount(const std::string& name, bool disableOnDestruct = true) 
      : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "Shared")
      {
         config.settings.displayName = "8195";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser15";
         config.settings.password = "8Ik3020713J9OXZ97zcTS0F7FQ79iK9B7N3G2UX5";
      }
      else if (name == "Private1")
      {
         config.settings.displayName = "8196";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser16";
         config.settings.password = "01X36wTH62L01M2v1MWcP316M6W06ZBA9JQ3q2O3";
      }
      else if (name == "Shared1")
      {
         config.settings.displayName = "8195_2";
         config.settings.username = "**********_2";
         config.settings.auth_username = "counterpathuser15";
         config.settings.password = "8Ik3020713J9OXZ97zcTS0F7FQ79iK9B7N3G2UX5";
      }
      else if (name == "Private2")
      {
         config.settings.displayName = "8197";
         config.settings.username = "2417778197";
         config.settings.auth_username = "counterpathuser17";
         config.settings.password = "h73A96C7425Z91H6Q1Ta89dDJC97R87NF3CMiEv8";
      }
      else if (name == "Shared2")
      {
         config.settings.displayName = "8195_3";
         config.settings.username = "**********_3";
         config.settings.auth_username = "counterpathuser15";
         config.settings.password = "8Ik3020713J9OXZ97zcTS0F7FQ79iK9B7N3G2UX5";
      }
      // broadsoft
      config.settings.domain = "as.iop1.broadworks.net";
      config.settings.outboundProxy = "************";
      config.settings.registrationIntervalSeconds = 3600;
      config.settings.useRport = true;
      config.settings.sipTransportType = SipAccountTransport_TCP;
      
      init();

      scaManager = SipSharedCallAppearanceManager::getInterface(phone);
      scaManager->setHandler(handle, (SipSharedCallAppearanceHandler*) 0xDEADBEEF);
      scaEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(scaManager));
      scaStateManager = SipSharedCallAppearanceStateManager::getInterface(scaManager);

      enable();
   }

   SipSharedCallAppearanceManager* scaManager;
   CPCAPI2::test::EventHandler* scaEvents;
   SipSharedCallAppearanceStateManager* scaStateManager;

   const cpc::string fromUri() { return "sip:8195@" + config.settings.domain + ";user=phone"; }
   static const cpc::string sharedLineUri() { return "sip:<EMAIL>"; }
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         config.settings.displayName = "9198";
         config.settings.username = "**********";
         config.settings.auth_username = "counterpathuser8";
         config.settings.password = "W92JW44M10T5fD0KatW827Iq1743DFD9Z72c70kQ";
      }
      else
      {
         assert(false);
      }

      config.settings.domain = "as.iop1.broadworks.net";
      config.settings.outboundProxy = "************";
      config.settings.registrationIntervalSeconds = 300;
      config.settings.useRport = true;
      config.settings.sipTransportType = SipAccountTransport_TCP;

      init();
      enable();
   }

   const cpc::string fromUri() { return "sip:" + config.settings.displayName + "@" + config.settings.domain + ";user=phone"; }
   const cpc::string uri() { return config.uri(); }
};

#else // Megapath uses Broadsoft platform also
class ScaAccount : public TestAccount
{
public:
   ScaAccount(const std::string& name, bool disableOnDestruct = true) 
      : TestAccount(name, Account_NoInit, disableOnDestruct)
   {
      if (name == "Private1")
      {
         config.settings.displayName = "UC1 Private";
         config.settings.username = "156795_307743_698814_VVX300_P001";
         config.settings.auth_username = "**********";
         config.settings.password = "skemFXuBcvYUx5Kk";
      }
      else if (name == "Shared1")
      {
         config.settings.displayName = "UC1 Shared";
         config.settings.username = "156795_307743_698814_VVX300_S002";
         config.settings.auth_username = "**********";
         config.settings.password = "HzShebCuw2RQlTqo";
      }
      else if (name == "Private2")
      {
         config.settings.displayName = "UC2 Private";
         config.settings.username = "156795_307743_698809_VVX410_P001";
         config.settings.auth_username = "**********";
         config.settings.password = "TMysxiJTZPOg7Mve";
      }
      else if (name == "Shared2")
      {
         config.settings.displayName = "UC2 Shared";
         config.settings.username = "156795_307743_698809_VVX410_S002";
         config.settings.auth_username = "**********";
         config.settings.password = "HzShebCuw2RQlTqo";
      }
      
      //Megapath
      config.settings.registrationIntervalSeconds = 3600;
      config.settings.domain = "megapathvoice.com";
      config.settings.outboundProxy = "************";
      //config.settings.useRport = true;
      config.settings.useRport = false;
      config.settings.useOutbound = false;
      config.settings.stunServerSource = StunServerSource_Custom;
      config.settings.stunServer = "stun.counterpath.com";
      config.settings.sipTransportType = SipAccountTransport_UDP;
      
      init();
      scaManager = SipSharedCallAppearanceManager::getInterface(phone);
      scaManager->setHandler(handle, (SipSharedCallAppearanceHandler*) 0xDEADBEEF);
      scaEvents = new test::EventHandler(name.c_str(), dynamic_cast<CPCAPI2::AutoTestProcessor*>(scaManager));
      scaStateManager = SipSharedCallAppearanceStateManager::getInterface(scaManager);
      enable();
   }
   SipSharedCallAppearanceManager* scaManager;
   CPCAPI2::test::EventHandler* scaEvents;
   SipSharedCallAppearanceStateManager* scaStateManager;

   const cpc::string fromUri() { return "sip:" + config.settings.displayName + "@" + config.settings.domain + ";user=phone"; }
   static const cpc::string sharedLineUri() { return "sip:<EMAIL>"; }
};

class StandardLineTestAccount : public TestAccount
{
public:
   StandardLineTestAccount(const std::string& name) : TestAccount(name, Account_NoInit)
   {
      if (name == "alice")
      {
         config.settings.displayName = "6773";
         config.settings.username = "156795_307743_698811_VVX600_P001";
         config.settings.auth_username = "**********";
         config.settings.domain = "megapathvoice.com";
         config.settings.outboundProxy = "************";
         config.settings.password = "aofk9thadkC8BQY1";
         config.settings.registrationIntervalSeconds = 3600;
         config.settings.useRport = true;
      }
      else
      {
         assert(false);
      }

      init();
      enable();
   }

   const cpc::string fromUri() { return "sip:" + config.settings.displayName + "@" + config.settings.outboundProxy + ";user=phone"; }
   const cpc::string uri() { return "sip:" + config.settings.auth_username + "@" + config.settings.outboundProxy; }
};

#endif

#define assertSCANewSubscription(account, scaSet, sca) \
   BroadsoftScaTest::expectSCANewSubscription(__LINE__, account, scaSet, sca)
#define assertSCAState(account, scaSet, sca, subscriptionStarted, subscriptionState) \
   BroadsoftScaTest::expectSCAState(__LINE__, account, scaSet, sca, subscriptionStarted, subscriptionState)
#define assertSCASubscriptionStateChanged(account, scaSet, sca, subscriptionState) \
   BroadsoftScaTest::expectSCASubscriptionStateChanged(__LINE__, account, scaSet, sca, subscriptionState)
#define assertSCAStateChanged(account, scaSet, sca) \
   BroadsoftScaTest::expectSCAStateChanged(__LINE__, account, scaSet, sca, DialogDirection_NotSpecified, DialogState_NotSpecified, "", "", 0)
#define assertSCAStateChanged_ex(account, scaSet, sca, direction, dialogState, localAddress, remoteAddress, appearance) \
   BroadsoftScaTest::expectSCAStateChanged(__LINE__, account, scaSet, sca, direction, dialogState, localAddress, remoteAddress, appearance)
#define waitForSCASubscriptionEnded(account, scaSet, sca) \
   BroadsoftScaTest::waitForSCASubscriptionEnded_(__LINE__, account, scaSet, sca)
#define waitForConversationStateChanged(account, conversation, conversationState) \
   BroadsoftScaTest::waitForConversationStateChanged_(__LINE__, account, conversation, conversationState)
#define waitForConversationEnded(account, conversation, endReason) \
   BroadsoftScaTest::waitForConversationEnded_(__LINE__, account, conversation, endReason)
#define assertSCACallHeldState(account, scaSet, sca, appearance, held) \
   BroadsoftScaTest::expectSCACallHeldState(account, scaSet, sca, appearance, held);
#define assertSCACallParkState(account, scaSet, sca, parked) \
   BroadsoftScaTest::expectSCACallParkState(account, scaSet, sca, parked);



class BroadsoftScaTest : public CpcapiAutoTest
{
public:
   BroadsoftScaTest() {}
   virtual ~BroadsoftScaTest() {}

   void startSubscription(ScaAccount& account, SipSharedCallAppearanceSetHandle& scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceSetSettings scaSetSettings = SipSharedCallAppearanceSetSettings(), SipSharedCallAppearanceSettings scaSettings = SipSharedCallAppearanceSettings());
   void terminateSubscription(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);

   static void expectSCANewSubscription(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   static void expectSCAState(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool subscriptionStarted, SipSubscriptionState subscriptionState);
   static void expectSCASubscriptionStateChanged(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSubscriptionState subscriptionState);
   static void expectSCAStateChanged(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, int appearance);
   static void waitForSCASubscriptionEnded_(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca);
   static void waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState);
   static void waitForConversationEnded_(int line, TestAccount& account, SipConversationHandle conversation, ConversationEndReason endReason);
   static void expectSCACallHeldState(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool held);
   static void expectSCACallParkState(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool parked);
};

#define PARK_CALL_SERVICE_CODE "*68"
#define RETRIEVE_CALL_SERVICE_CODE "*88"
/*
* Incoming call to shared line. 
* Broadworks Shared Call Appearance Interface Spec 8.3.6
*/
TEST_F(BroadsoftScaTest, IncomingCallToSharedLine)
{
   // Register and establish subscriptions to shared line
   ScaAccount private1("Private1");
   ScaAccount shared1("Shared1");
   SipSharedCallAppearanceHandle scaHandle1 = shared1.config.uri();
   SipSharedCallAppearanceSetHandle setHandle1;
   startSubscription(shared1, setHandle1, scaHandle1);

   ScaAccount private2("Private2");
   ScaAccount shared2("Shared2");
   SipSharedCallAppearanceHandle scaHandle2 = shared2.config.uri();
   SipSharedCallAppearanceSetHandle setHandle2;
   startSubscription(shared2, setHandle2, scaHandle2);

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Alice calls the shared line
   cpc::string scaLine = ScaAccount::sharedLineUri();
   StandardLineTestAccount alice("alice");
   SipConversationHandle aliceCall = alice.conversation->createConversation(alice.handle);
   alice.conversation->addParticipant(aliceCall, scaLine);
   alice.conversation->start(aliceCall);
   assertNewConversationOutgoing(alice, aliceCall, scaLine);

   // URI as presented by server in incoming requests
   cpc::string aliceFromUri = alice.fromUri();

   // Shared1 and Shared2 phones ringing
   SipConversationHandle shared1Call;
   assertNewConversationIncoming_ex(shared1, &shared1Call, aliceFromUri, [&](const NewConversationEvent& evt) {
      // Extract the appearance number from the incoming INVITE. Note that the appearance number can also be obtained from SipSharedCallApperanceStateChangedEvent
      int appearance = shared1.scaManager->getAppearanceForScapCall(evt.alertInfoHeader);
      ASSERT_EQ(1, appearance);
   });
   assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Recipient, DialogState_Proceeding, scaLine, aliceFromUri, 1);
   shared1.conversation->sendRingingResponse(shared1Call);
   assertConversationStateChanged(shared1, shared1Call, ConversationState_LocalRinging);

   SipConversationHandle shared2Call;
   assertNewConversationIncoming_ex(shared2, &shared2Call, aliceFromUri, [&](const NewConversationEvent& evt) {
      // Extract the appearance number from the incoming INVITE. Note that the appearance number can also be obtained from SipSharedCallApperanceStateChangedEvent
      int appearance = shared2.scaManager->getAppearanceForScapCall(evt.alertInfoHeader);
      ASSERT_EQ(1, appearance);
   });
   assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Recipient, DialogState_Proceeding, scaLine, aliceFromUri, 1);
   shared2.conversation->sendRingingResponse(shared2Call);
   assertConversationStateChanged(shared2, shared2Call, ConversationState_LocalRinging);

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // Shared Line 2 answers first
   shared2.conversation->accept(shared2Call);

   // Shared Line 1 call is terminated
   waitForConversationEnded(shared1, shared1Call, ConversationEndReason_UserTerminatedRemotely);

   /// Shared Line 2 call established with Alice
   assertConversationMediaChanged(shared2, shared2Call, MediaDirection_SendReceive);
   waitForConversationStateChanged(shared2, shared2Call, ConversationState_Connected);
   assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Recipient, DialogState_Confirmed, scaLine, aliceFromUri, 1);

   std::this_thread::sleep_for(std::chrono::milliseconds(5000));

   shared2.conversation->end(shared2Call);
   waitForConversationEnded(shared2, shared2Call, ConversationEndReason_UserTerminatedLocally);
   // NOTE: no appearance or direction for terminated call in SCA state

   std::this_thread::sleep_for(std::chrono::milliseconds(1000));

   // clean up before disabling
   terminateSubscription(shared1, setHandle1, scaHandle1);
   terminateSubscription(shared2, setHandle2, scaHandle2);
}

/*
* Outgoing call to Alice from Shared 1; Shared 1 puts call on hold, Shared 2 retrieves call from hold. 
* Broadsoft SCA spec 8.3.7.
*/
TEST_F(BroadsoftScaTest, OutgoingCallWithHoldRetrieve)
{
   // Register and establish subscriptions to shared line
   ScaAccount private1("Private1");
   ScaAccount shared1("Shared1");
   SipSharedCallAppearanceHandle scaHandle1 = shared1.config.uri();
   SipSharedCallAppearanceSetHandle setHandle1;
   startSubscription(shared1, setHandle1, scaHandle1);

   ScaAccount private2("Private2");
   ScaAccount shared2("Shared2");
   SipSharedCallAppearanceHandle scaHandle2 = shared2.config.uri();
   SipSharedCallAppearanceSetHandle setHandle2;
   startSubscription(shared2, setHandle2, scaHandle2);

   StandardLineTestAccount alice("alice");

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Shared 1 calls Alice
   cpc::string scaLine = ScaAccount::sharedLineUri();   
   // URI as presented by server in incoming requests
   cpc::string aliceFromUri = alice.fromUri();

   SipConversationHandle shared1Call = shared1.conversation->createConversation(shared1.handle);
   shared1.conversation->addParticipant(shared1Call, alice.uri());
   shared1.conversation->start(shared1Call);

   auto shared1Events = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(shared1, shared1Call, alice.uri());
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1); // This notification contains the appearance
      assertConversationStateChanged(shared1, shared1Call, ConversationState_RemoteRinging);
      assertConversationStateChanged(shared1, shared1Call, ConversationState_Connected);
      assertConversationMediaChanged(shared1, shared1Call, MediaDirection_SendReceive);
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      shared1.conversation->hold(shared1Call);
      assertConversationMediaChanged(shared1, shared1Call, MediaDirection_SendOnly);
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);
      assertSCACallHeldState(shared1, setHandle1, scaHandle1, 1, true);

      // Shared 1 call ends
      waitForConversationEnded(shared1, shared1Call, ConversationEndReason_UserTerminatedRemotely);
   });
   

   auto aliceEvents = std::async(std::launch::async, [&] () {
      // alice ringing
      SipConversationHandle aliceCall;
      assertNewConversationIncoming(alice, &aliceCall, shared1.fromUri());
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      // alice accepts call
      alice.conversation->accept(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      // hold request
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendOnly);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_ReceiveOnly);

      // Alice accepts unhold request
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->end(aliceCall);
   });
   
   auto shared2Events = std::async(std::launch::async, [&] () {
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1); // This notification contains the appearance
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);

      // expect to see held state (+sip.rendering = no)

      assertSCACallHeldState(shared2, setHandle2, scaHandle2, 1, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));
      SipConversationHandle shared2Call = shared2.scaManager->scapJoin(setHandle2, scaHandle2, 1);
      assertNewConversationOutgoing(shared2, shared2Call, scaHandle2);
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1);
      assertConversationMediaChanged(shared2, shared2Call, MediaDirection_SendReceive);
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);

      // call is no longer held

      assertSCACallHeldState(shared2, setHandle2, scaHandle2, 1, false);
   });

   waitFor3(shared1Events, aliceEvents, shared2Events);

   // clean up before disabling
   terminateSubscription(shared1, setHandle1, scaHandle1);
   terminateSubscription(shared2, setHandle2, scaHandle2);
}

/*
* Outgoing call to Alice from Shared 1; Shared 2 barges in
* Broadsoft SCA spec 8.3.8.
*/
TEST_F(BroadsoftScaTest, BargeIn)
{
   // Register and establish subscriptions to shared line
   ScaAccount private1("Private1");
   ScaAccount shared1("Shared1");
   SipSharedCallAppearanceHandle scaHandle1 = shared1.config.uri();
   SipSharedCallAppearanceSetHandle setHandle1;
   startSubscription(shared1, setHandle1, scaHandle1);

   ScaAccount private2("Private2");
   ScaAccount shared2("Shared2");
   SipSharedCallAppearanceHandle scaHandle2 = shared2.config.uri();
   SipSharedCallAppearanceSetHandle setHandle2;
   startSubscription(shared2, setHandle2, scaHandle2);

   StandardLineTestAccount alice("alice");

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Shared 1 calls Alice
   cpc::string scaLine = ScaAccount::sharedLineUri();   
   // URI as presented by server in incoming requests
   cpc::string aliceFromUri = alice.fromUri();

   SipConversationHandle shared1Call = shared1.conversation->createConversation(shared1.handle);
   shared1.conversation->addParticipant(shared1Call, alice.config.uri());
   shared1.conversation->start(shared1Call);

   auto shared1Events = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(shared1, shared1Call, alice.config.uri());
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1); // This notification contains the appearance
      assertConversationStateChanged(shared1, shared1Call, ConversationState_RemoteRinging);
      assertConversationStateChanged(shared1, shared1Call, ConversationState_Connected);
      assertConversationMediaChanged(shared1, shared1Call, MediaDirection_SendReceive);
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      // shared 2 barges in..
      assertConversationMediaChangeRequest(shared1, shared1Call, MediaDirection_SendReceive);
      shared1.conversation->accept(shared1Call);
      assertConversationMediaChanged(shared1, shared1Call, MediaDirection_SendReceive);
   });


   auto aliceEvents = std::async(std::launch::async, [&] () {
      // alice ringing
      SipConversationHandle aliceCall;
      assertNewConversationIncoming(alice, &aliceCall, shared1.fromUri());
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      // alice accepts call
      alice.conversation->accept(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      // request to join conference due to barge in
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      // alice, shared1 and shared2 are now in a conference
      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->end(aliceCall);
   });

   auto shared2Events = std::async(std::launch::async, [&] () {
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1); // This notification contains the appearance
      assertSCAStateChanged_ex(shared2, setHandle2, scaHandle2, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);

      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      // barge in shared1's call
      SipConversationHandle shared2Call = shared2.scaManager->scapBridgeIn(setHandle2, scaHandle2, 1);
      assertNewConversationOutgoing(shared2, shared2Call, scaHandle2);
      assertConversationMediaChanged(shared2, shared2Call, MediaDirection_SendReceive);
   });

   waitFor3(shared1Events, aliceEvents, shared2Events);

   // clean up before disabling
   terminateSubscription(shared1, setHandle1, scaHandle1);
   terminateSubscription(shared2, setHandle2, scaHandle2);
}

/*
* Outgoing call to Alice from Shared 1; Call is parked then retrieved by Shared 2
* Broadsoft SCA spec 8.3.9.
*/
TEST_F(BroadsoftScaTest, CallPark)
{
   // Register and establish subscriptions to shared line
   ScaAccount private1("Private1");
   ScaAccount shared1("Shared1");
   SipSharedCallAppearanceHandle scaHandle1 = shared1.config.uri();
   SipSharedCallAppearanceSetHandle setHandle1;
   startSubscription(shared1, setHandle1, scaHandle1);

   StandardLineTestAccount alice("alice");

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   // Shared 1 calls Alice
   cpc::string scaLine = ScaAccount::sharedLineUri();   
   // URI as presented by server in incoming requests
   cpc::string aliceFromUri = alice.fromUri();

   SipConversationHandle shared1Call = shared1.conversation->createConversation(shared1.handle);
   shared1.conversation->addParticipant(shared1Call, alice.config.uri());
   shared1.conversation->start(shared1Call);

   auto shared1Events = std::async(std::launch::async, [&] () {
      assertNewConversationOutgoing(shared1, shared1Call, alice.config.uri());
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Proceeding, scaLine, aliceFromUri, 1); // This notification contains the appearance
      assertConversationStateChanged(shared1, shared1Call, ConversationState_RemoteRinging);
      assertConversationStateChanged(shared1, shared1Call, ConversationState_Connected);
      assertConversationMediaChanged(shared1, shared1Call, MediaDirection_SendReceive);
      assertSCAStateChanged_ex(shared1, setHandle1, scaHandle1, DialogDirection_Initiator, DialogState_Confirmed, scaLine, aliceFromUri, 1);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));

      cpc::string callParkUri = "sip:" + cpc::string(PARK_CALL_SERVICE_CODE) + "@" + shared1.config.settings.domain;
      SipConversationHandle parkRequestCall = shared1.conversation->createConversation(shared1.handle);
      shared1.conversation->addParticipant(parkRequestCall, callParkUri);
      shared1.conversation->start(parkRequestCall);
      assertSCAStateChanged(shared1, setHandle1, scaHandle1);
      assertConversationMediaChanged(shared1, parkRequestCall, MediaDirection_SendReceive);
      
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
      // Send "#" to park against own line
      shared1.conversation->startDtmfTone(parkRequestCall, 11, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      shared1.conversation->stopDtmfTone();
      // Call is parked
      waitForConversationEnded(shared1, parkRequestCall, ConversationEndReason_UserTerminatedRemotely);
      // expect to see a parked call
      assertSCACallParkState(shared1, setHandle1, scaHandle1, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(3000));

      // Retrieve parked call
      cpc::string retrieveParkUri = "sip:" + cpc::string(RETRIEVE_CALL_SERVICE_CODE) + "@" + shared1.config.settings.domain;
      SipConversationHandle retrieveParkCall = shared1.conversation->createConversation(shared1.handle);
      shared1.conversation->addParticipant(retrieveParkCall, retrieveParkUri);
      shared1.conversation->start(retrieveParkCall);
      std::this_thread::sleep_for(std::chrono::milliseconds(2000));
      // Send "#" to retrieve call parked against own line
      shared1.conversation->startDtmfTone(retrieveParkCall, 11, true);
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
      shared1.conversation->stopDtmfTone();

      // Call is retrieved
      assertConversationMediaChangeRequest(shared1, retrieveParkCall, MediaDirection_SendReceive);
      shared1.conversation->accept(retrieveParkCall);
      assertConversationMediaChanged(shared1, retrieveParkCall, MediaDirection_SendReceive);      

      assertSCACallParkState(shared1, setHandle1, scaHandle1, false);
   });


   auto aliceEvents = std::async(std::launch::async, [&] () {
      // alice ringing
      SipConversationHandle aliceCall;
      assertNewConversationIncoming(alice, &aliceCall, shared1.fromUri());
      alice.conversation->sendRingingResponse(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_LocalRinging);
      // alice accepts call
      alice.conversation->accept(aliceCall);
      assertConversationStateChanged(alice, aliceCall, ConversationState_Connected);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      // request to get parked     
      assertConversationMediaChangeRequest(alice, aliceCall, MediaDirection_SendReceive);
      alice.conversation->accept(aliceCall);
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);      

      // parked call retrieved
      assertConversationMediaChanged(alice, aliceCall, MediaDirection_SendReceive);

      std::this_thread::sleep_for(std::chrono::milliseconds(5000));
      alice.conversation->end(aliceCall);
   });

   waitFor2(shared1Events, aliceEvents);
   std::this_thread::sleep_for(std::chrono::milliseconds(10000));
   // clean up before disabling
   terminateSubscription(shared1, setHandle1, scaHandle1);
}

void BroadsoftScaTest::startSubscription(ScaAccount& account, SipSharedCallAppearanceSetHandle& scaSet, SipSharedCallAppearanceHandle sca, SipSharedCallAppearanceSetSettings scaSetSettings, SipSharedCallAppearanceSettings scaSettings)
{
   // Alice subscribes to the SCA
   scaSetSettings.useSharedDialogParameter = false; // Broadsoft does not use the dialog;shared parameter
   scaSet = account.scaManager->createSharedCallAppearanceSet(account.handle, scaSetSettings);
   ASSERT_TRUE(scaSet > 0);
   assertSuccess(account.scaManager->addSharedCallAppearance(scaSet, sca, scaSettings));

   // Start the subscription
   assertSuccess(account.scaManager->start(scaSet));

   // Wait for the new subscription notification
   assertSCANewSubscription(account, scaSet, sca);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Pending);

   // Wait for the subscription state change notification
   assertSCASubscriptionStateChanged(account, scaSet, sca, SipSubscriptionState_Active);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Active);

   // Wait for the initial SCA state change notification
   assertSCAStateChanged(account, scaSet, sca);

   // Validate the state of the SCA
   assertSCAState(account, scaSet, sca, true, SipSubscriptionState_Active);
}


void BroadsoftScaTest::expectSCANewSubscription(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceNewSubscriptionEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceNewSubscription",
      15000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(account.handle, evt.account);
   ASSERT_EQ(sca, evt.sca);
}

void BroadsoftScaTest::expectSCAState(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool subscriptionStarted, SipSubscriptionState subscriptionState)
{
   // Validate the state of the SCA
   SipSharedCallAppearanceState scaState;
   ASSERT_EQ(account.scaStateManager->getState(scaSet, sca, scaState), kSuccess);
   ASSERT_EQ(sca, scaState.sca);
   ASSERT_EQ(subscriptionStarted, scaState.subscriptionStarted);
   ASSERT_EQ(subscriptionState, scaState.subscriptionState);
}

void BroadsoftScaTest::expectSCASubscriptionStateChanged(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, SipSubscriptionState subscriptionState)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceSubscriptionStateChangedEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionStateChanged",
      15000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(account.handle, evt.account);
   ASSERT_EQ(sca, evt.sca);
   ASSERT_EQ(subscriptionState, evt.subscriptionState);
}

void BroadsoftScaTest::expectSCAStateChanged(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, DialogDirection direction, DialogState dialogState, const cpc::string& localAddress, const cpc::string& remoteAddress, int appearance)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceStateChangedEvent evt;
   ASSERT_TRUE(account.scaEvents->expectEvent(line,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceStateChanged",
      10000, AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(sca, evt.sca);

   if (direction != DialogDirection_NotSpecified)
   {
      DialogInfo dialog;
      for (cpc::vector<SipSharedCallAppearanceCallInfo>::const_iterator it = evt.calls.begin(); it != evt.calls.end(); it++)
      {
         if (it->dialog.appearance == appearance)
         {
            dialog = it->dialog;
            break;
         }
      }
      ASSERT_EQ(appearance, dialog.appearance);
      ASSERT_EQ(direction, dialog.direction);
      ASSERT_EQ(dialogState, dialog.stateInfo.state);
      ASSERT_EQ(0, dialog.stateInfo.code);
      if (dialogState == DialogState_Trying)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
      }
      else if (dialogState == DialogState_Early || dialogState == DialogState_Confirmed || dialogState == DialogState_Terminated)
      {
         ASSERT_FALSE(dialog.dialogId.callId.empty());
         ASSERT_FALSE(dialog.dialogId.localTag.empty());
         ASSERT_FALSE(dialog.dialogId.remoteTag.empty());
      }
      //ASSERT_EQ(localAddress, dialog.localParticipant.identity.address);
      if (!cpc::string(remoteAddress).empty())
      {
         ASSERT_EQ(remoteAddress, dialog.remoteParticipant.identity.address);
      }
   }
}

void BroadsoftScaTest::terminateSubscription(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
   // Terminate subscription
   assertSuccess(account.scaManager->end(scaSet));

   // Wait for the subscription termination notification
   waitForSCASubscriptionEnded(account, scaSet, sca);
}

void BroadsoftScaTest::waitForSCASubscriptionEnded_(int line, ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca)
{
   SipSharedCallAppearanceSetHandle h;
   SharedCallAppearanceSubscriptionEndedEvent evt;
   ASSERT_TRUE(account.scaEvents->waitForEvent(line,
      "SipSharedCallAppearanceHandler::onSharedCallAppearanceSubscriptionEnded",
      15000,
      AlwaysTruePred(), h, evt));
   ASSERT_EQ(scaSet, h);
   ASSERT_EQ(SipSubscriptionEndReason_ServerEnded, evt.endReason);
}

void BroadsoftScaTest::waitForConversationStateChanged_(int line, TestAccount& account, SipConversationHandle conversation, ConversationState conversationState)
{
   while(true)
   {
      SipConversationHandle h;
      ConversationStateChangedEvent evt;
      ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
         "SipConversationHandler::onConversationStateChanged",
         10000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
      ASSERT_EQ(h, conversation);
      if (evt.conversationState == conversationState)
      {
         break;
      }
   }
}

void BroadsoftScaTest::waitForConversationEnded_(int line, TestAccount& account, SipConversationHandle conversation, ConversationEndReason endReason)
{
   SipConversationHandle h;
   ConversationEndedEvent evt;
   ASSERT_TRUE(account.conversationEvents->waitForEvent(line,
      "SipConversationHandler::onConversationEnded",
      5000, HandleEqualsPred<SipConversationHandle>(conversation), h, evt));
   ASSERT_EQ(h, conversation);
   ASSERT_EQ(endReason, evt.endReason);
}

void BroadsoftScaTest::expectSCACallHeldState(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, int appearance, bool held)
{
   SipSharedCallAppearanceCallInfo callInfo;
   assertSuccess(account.scaStateManager->getCall(scaSet, sca, appearance, callInfo));
   ASSERT_EQ(callInfo.dialog.appearance, appearance);
   ASSERT_EQ(callInfo.isHeld, held);
}

void BroadsoftScaTest::expectSCACallParkState(ScaAccount& account, SipSharedCallAppearanceSetHandle scaSet, SipSharedCallAppearanceHandle sca, bool parked)
{
   SipSharedCallAppearanceState scaState;
   assertSuccess(account.scaStateManager->getState(scaSet, sca, scaState));
   
   // check for at least one parked call
   bool callParked = false;
   for (cpc::vector<SipSharedCallAppearanceCallInfo>::const_iterator it = scaState.calls.begin(); it != scaState.calls.end(); it++)
   {
      callParked = it->isParked;
      if (callParked)
         break;
   }

   ASSERT_EQ(callParked, parked);
}

}
#endif
