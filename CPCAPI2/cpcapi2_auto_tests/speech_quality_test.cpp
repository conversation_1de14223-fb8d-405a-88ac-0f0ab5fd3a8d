#include "speech_quality_test.h"

// disable build on Android until we switch to NDK 22+? https://github.com/android/ndk/issues/609
#ifndef ANDROID

#if _WIN32
#include <windows.h>
#endif

#include <filesystem>

#include <boost/algorithm/string.hpp>
#include <fstream>
#include <sstream>
#include <iostream>
#include <vector>

using namespace CPCAPI2;

#include <filesystem>

const std::string kReferenceFilename = "ctclip316kfixed15secStereo.wav";
const std::string kRawRecordFilename = "raw_record5.wav";
const std::filesystem::path kToolPath = std::filesystem::current_path() / "PESQ" / "sox";
const std::filesystem::path kLogPath = std::filesystem::current_path() / "logs";
const std::filesystem::path kToolSummary = kToolPath / "pesq_results.txt"; // where the tool will output a summary text file

#ifdef _WIN32
const std::string kSoxBinaryFilename = "sox.exe";
const std::string kPesqBinaryFilename = "pesq.exe";
#else
const std::string kSoxBinaryFilename = "sox";
const std::string kPesqBinaryFilename = "pesq";
const std::filesystem::path ffmpegPath = std::filesystem::current_path() / "PESQ" / "sox" / "ffmpeg";
const std::filesystem::path ffplayPath = std::filesystem::current_path() / "PESQ" / "sox" / "ffplay";
const std::filesystem::path pesqPath = std::filesystem::current_path() / "PESQ" / "sox" / "PESQ";
const std::filesystem::path soxPath = std::filesystem::current_path() / "PESQ" / "sox" / "sox";
const std::filesystem::path kRawRecOsx = kToolPath / kRawRecordFilename;
const std::filesystem::path kRefOsx = kToolPath / kReferenceFilename;
#endif


class ToolRunner
{
public:
   ~ToolRunner();

   bool runPlaybackToolAsync(const std::string& inFilename);
   bool stopPlaybackTool();

   bool runRecordToolAsync(const std::string& outFilename);
   bool stopRecordTool();

   bool runConvertTool(const std::string& inFilename, const std::string& outFilename);

   void runAnalysisTool(const std::string& kReferenceFilename, const std::string& testFilename, SpeechQualityTestResult& outResult);

private:
#if _WIN32
   bool runProcessAsync(const std::string& workingDir, const std::string& processName, PROCESS_INFORMATION& outProcInfo);
   bool stopProcess(PROCESS_INFORMATION pi);

   bool runProcess(const std::string& workingDir, const std::string& processName);

   std::shared_ptr<PROCESS_INFORMATION> mPlaybackTool;
   std::shared_ptr<PROCESS_INFORMATION> mRecordTool;
#else
   static int GetDeviceId(std::string deviceName)
   {
      int id = -1;

      std::string tempTextFile = "temp.txt";

      try
      {
         std::filesystem::remove(tempTextFile);
      }
      catch (std::filesystem::filesystem_error e)
      {

      }

      std::string ss;
      ss.assign(ffmpegPath.string());
      ss = ss + " -f avfoundation -list_devices true -i \"\" 2>&1 | tee " + tempTextFile;

      system(ss.c_str());

      std::ifstream myfile;
      myfile.open(tempTextFile);

      std::string line;
      if (myfile.is_open())
      {
         while ( myfile.good() )
         {
            getline (myfile,line);
            if (line.find(deviceName) != std::string::npos)
            {
               std::vector<std::string> results;
               boost::split(results, line, [](char c){return c == ' ';});

               id = int(results[5][1]) - 48;
            }
         }
      }
      myfile.close();

      return id;
   }
#endif
};


void SpeechQualityTestRunner::configure(const SpeechQualityTestConfig& config)
{
   mConfig = config;
}

SpeechQualityTestResult SpeechQualityTestRunner::runTest()
{
   // 1. delete old output files

   // remove should throw on underlying OS errors

   try
   {
      std::filesystem::remove(kToolPath / kRawRecordFilename);
      std::filesystem::remove(kToolPath / mConfig.outputWavFilename);
      std::filesystem::remove(kToolPath / mConfig.outputSummaryFilename);
      std::filesystem::remove(kToolSummary);
   }
   catch (std::filesystem::filesystem_error e)
   {
      return SpeechQualityTestResult::makeFailure();
   }

   // 2. run playback, run record

   ToolRunner toolRunner;

#ifdef _WIN32
   if (!toolRunner.runRecordToolAsync(kRawRecordFilename))
   {
      return SpeechQualityTestResult::makeFailure();
   }
   if (!toolRunner.runPlaybackToolAsync(kReferenceFilename))
   {
      return SpeechQualityTestResult::makeFailure();
   }

   std::this_thread::sleep_for(std::chrono::seconds(mConfig.referenceWavLengthSeconds));

   if (!toolRunner.stopPlaybackTool())
   {
      return SpeechQualityTestResult::makeFailure();
   }

   if (!toolRunner.stopRecordTool())
   {
      return SpeechQualityTestResult::makeFailure();
   }


   // 3. convert result
   if (!toolRunner.runConvertTool(kRawRecordFilename, mConfig.outputWavFilename))
   {
      return SpeechQualityTestResult::makeFailure();
   }


   // 4. run comparison test
   SpeechQualityTestResult result;
   toolRunner.runAnalysisTool(kReferenceFilename, mConfig.outputWavFilename, result);
#else

   if (!toolRunner.runPlaybackToolAsync(kRefOsx.string()))
   {
      return SpeechQualityTestResult::makeFailure();
   }

   if (!toolRunner.runRecordToolAsync(kRawRecOsx.string()))
   {
      return SpeechQualityTestResult::makeFailure();
   }

   std::this_thread::sleep_for(std::chrono::seconds(mConfig.referenceWavLengthSeconds));

   if (!toolRunner.stopPlaybackTool())
   {
      return SpeechQualityTestResult::makeFailure();
   }

   if (!toolRunner.stopRecordTool())
   {
      return SpeechQualityTestResult::makeFailure();
   }


   // 3. convert result
   if (!toolRunner.runConvertTool(kRawRecOsx.string(), mConfig.outputWavFilename))
   {
      return SpeechQualityTestResult::makeFailure();
   }


   // 4. run comparison test
   SpeechQualityTestResult result;
   toolRunner.runAnalysisTool(kRefOsx.string(), mConfig.outputWavFilename, result);
#endif


   // 5. copy summary report, output wav to log directory
   std::filesystem::copy(kToolSummary, kLogPath / mConfig.outputSummaryFilename, std::filesystem::copy_options::overwrite_existing);
   std::filesystem::copy(kToolPath / mConfig.outputWavFilename, kLogPath / mConfig.outputWavFilename, std::filesystem::copy_options::overwrite_existing);

   return result;
}

bool ToolRunner::runPlaybackToolAsync(const std::string& inFilename)
{
#if _WIN32
   std::stringstream ss;
   ss << (kToolPath / kSoxBinaryFilename).u8string();
   ss << " " << inFilename;
   std::string refDeviceName = getenv("CPCAPI2_AQ_PLAYOUT_DEVICENAME");
   ss << " -t waveaudio \"" << refDeviceName << "\" ";

   mPlaybackTool.reset(new PROCESS_INFORMATION());
   if (!runProcessAsync(kToolPath.u8string(), ss.str(), *mPlaybackTool.get()))
   {
      mPlaybackTool.reset();
      return false;
   }

   return true;
#else
   std::string ss;
   ss.assign(ffplayPath.string());
   ss = ss + " -autoexit -t 15 " + inFilename + " &";

   system(ss.c_str());

   return true;
#endif
}

ToolRunner::~ToolRunner()
{
   stopRecordTool();
   stopPlaybackTool();
}

bool ToolRunner::stopPlaybackTool()
{
#if _WIN32
   if (!mPlaybackTool)
   {
      return false;
   }

   // ignore errors; the process is probably done already
   stopProcess(*mPlaybackTool.get());

   mPlaybackTool.reset();
   return true;
#else
   return true;
#endif
}

bool ToolRunner::runRecordToolAsync(const std::string& outFilename)
{
#if _WIN32
   std::stringstream ss;
   ss << (kToolPath / kSoxBinaryFilename).u8string();
   std::string refDeviceName = getenv("CPCAPI2_AQ_RECORD_DEVICENAME");
   ss << " -t waveaudio \"" << refDeviceName << "\" ";
   ss << " " << outFilename;

   mRecordTool.reset(new PROCESS_INFORMATION());
   if (!runProcessAsync(kToolPath.u8string(), ss.str(), *mRecordTool.get()))
   {
      mRecordTool.reset();
      return false;
   }

   return true;
#else

   std::string ss;
   ss.assign(soxPath.string());
   ss = ss + " -d " + outFilename + " trim 0 15 &";

   system(ss.c_str());

   return true;
#endif
}

bool ToolRunner::stopRecordTool()
{
#if _WIN32
   if (!mRecordTool)
   {
      return false;
   }

   if (!stopProcess(*mRecordTool.get()))
   {
      return false;
   }

   mRecordTool.reset();
   return true;
#else
   return true;
#endif
}

bool ToolRunner::runConvertTool(const std::string& inFilename, const std::string& outFilename)
{

#if _WIN32
   std::stringstream ss;
   ss << (kToolPath / kSoxBinaryFilename).u8string();
   ss << " " << inFilename;
   ss << " -r 16k";
   ss << " " << outFilename;

   if (!runProcess(kToolPath.u8string(), ss.str()))
   {
      return false;
   }

   return true;
#else

   std::string ss;
   ss.assign(ffmpegPath.string());
   ss = ss + " -i " + inFilename + " -ar 16000 " + outFilename;

   system(ss.c_str());

   return true;
#endif
}

// TODO: audit/rewrite
static std::string getLastLineFromFile(std::string fileName)
{
   std::ifstream fin;
   fin.open(fileName);
   if (fin.is_open()) {
      fin.seekg(-3, std::ios_base::end);                // go to one spot before the EOF

      bool keepLooping = true;
      while (keepLooping) {
         char ch;
         fin.get(ch);                            // Get current byte's data

         if ((int)fin.tellg() <= 1) {             // If the data was at or before the 0th byte
            fin.seekg(0);                       // The first line is the last line
            keepLooping = false;                // So stop there
         }
         else if (ch == '\n') {                   // If the data was a newline
            keepLooping = false;                // Stop at the current position.
         }
         else {                                  // If the data was neither a newline nor at the 0 byte
            fin.seekg(-2, std::ios_base::cur);        // Move to the front of that data, then to the front of the data before it
         }
      }

      std::string lastLine;
      std::getline(fin, lastLine);                      // Read the current line

      fin.close();

      return lastLine;
   }

   return "";
}

// TODO: audit/rewrite
static int nthSubstr(int n, const std::string& s, const std::string& p)
{
   std::string::size_type i = s.find(p);     // Find the first occurrence

   int j;
   for (j = 1; j < n && i != std::string::npos; ++j)
      i = s.find(p, i + 1); // Find the next occurrence

   if (j == n)
      return(i);
   else
      return(-1);
}

void ToolRunner::runAnalysisTool(const std::string& kReferenceFilename, const std::string& testFilename, SpeechQualityTestResult& outResult)
{
#if _WIN32
   std::stringstream ss;
   ss << (kToolPath / kPesqBinaryFilename).u8string();
   ss << " +16000 " << kReferenceFilename << " " << testFilename;

   if (!runProcess(kToolPath.u8string(), ss.str()))
   {
      outResult = SpeechQualityTestResult::makeFailure();
      return;
   }

   std::string currentResult = getLastLineFromFile(kToolSummary.u8string());

#else
   std::string ss;
   ss.assign(pesqPath.string());
   ss = ss + " +16000 " + kReferenceFilename + " " + testFilename;

   system(ss.c_str());
   std::string currentResult = getLastLineFromFile(kToolSummary.string());

#endif


   int rawScoreIndex = nthSubstr(2, currentResult, "\t");
   int mosLQOIndex = nthSubstr(3, currentResult, "\t");

   std::string rawMos = currentResult.substr(rawScoreIndex + 2, 5);
   std::string mosLQO = currentResult.substr(mosLQOIndex + 2, 5);

   std::string::size_type sz;

   outResult = SpeechQualityTestResult::makeSuccess(stof(rawMos, &sz), stof(mosLQO, &sz));
}

#if _WIN32
bool ToolRunner::runProcessAsync(const std::string& workingDir, const std::string& commandLine, PROCESS_INFORMATION& outProcInfo)
{
   char commandLineBuf[256];
   strncpy(commandLineBuf, commandLine.c_str(), sizeof(commandLineBuf));
   commandLineBuf[255] = 0;

   STARTUPINFO si;
   PROCESS_INFORMATION pi;

   ZeroMemory(&si, sizeof(si));
   si.cb = sizeof(si);
   ZeroMemory(&pi, sizeof(pi));

   // Start the child process.
   if (!CreateProcessA(NULL,   // No module name (use command line)
      commandLineBuf,       // Command line
      NULL,           // Process handle not inheritable
      NULL,           // Thread handle not inheritable
      FALSE,          // Set handle inheritance to FALSE
      0,              // No creation flags
      NULL,           // Use parent's environment block
      workingDir.c_str(),           // Use parent's starting directory
      &si,            // Pointer to STARTUPINFO structure
      &pi)           // Pointer to PROCESS_INFORMATION structure
      )
   {
      std::cerr << "CreateProcess failed: " <<  GetLastError();
      return false;
   }

   outProcInfo = pi;
   return true;
}

bool ToolRunner::stopProcess(PROCESS_INFORMATION pi)
{
   if (::TerminateProcess(pi.hProcess, 0) == 0)
   {
      return false;
   }

   ::CloseHandle(pi.hProcess);

   return true;
}

bool ToolRunner::runProcess(const std::string& workingDir, const std::string& commandLine)
{
   PROCESS_INFORMATION pi;
   if (!runProcessAsync(kToolPath.u8string(), commandLine, pi))
   {
      return false;
   }

   // wait for the converstion tool proces to finish running
   if (WaitForSingleObject(pi.hProcess, 30000) != WAIT_OBJECT_0)
   {
      return false;
   }

   DWORD piRetCode;
   if (!::GetExitCodeProcess(pi.hProcess, &piRetCode))
   {
      return false;
   }

   ::CloseHandle(pi.hProcess);

   if (piRetCode != 0)
   {
      return false;
   }

   return true;
}
#endif

#endif // #ifndef ANDROID
