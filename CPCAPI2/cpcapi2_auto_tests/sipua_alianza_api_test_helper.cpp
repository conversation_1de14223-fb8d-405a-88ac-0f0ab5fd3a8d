#include "sipua_alianza_api_test_helper.h"
#include "test_account_events.h"
#include "test_events.h"
#include "test_call_events.h"

#include <sstream>

#include "util/CurlPPHelper.h"
#include "util/CurlPPProgress.h"
#include "util/CurlPPSSL.h"
#include "util/CurlURI.h"

// rapidjson
#include <writer.h>
#include <stringbuffer.h>
#include <document.h>


using namespace CPCAPI2;
using namespace CPCAPI2::test;
using namespace CPCAPI2::SipAccount;
using namespace ::testing;

using namespace std::chrono;
using namespace curlpp::options;

#define ALIANZA_API_PARTITION_PREFIX urlStr << config.api.publicApiUrl << "partition/" << config.partitionId

#define ALIANZA_API_ACCOUNT_PREFIX ALIANZA_API_PARTITION_PREFIX << "/account/" << info.accountId

#define ALIANZA_API_CUSTOMER_SERVICE_RECORD \
   "\"customerServiceRecord\":{" << "\"firstName\":\"" << ua.firstName \
      << "\",\"lastName\":\"" << ua.lastName << "\",\"businessName\":\"" << ua.businessName << "\"," \
      << "\"streetNumber\":\"333\",\"streetNumberSuffix\":\"S\",\"streetName\":\"520 W\",\"city\":\"Lindon\"," \
      << "\"state\":\"UT\",\"country\":\"USA\",\"postalCode\":\"84042\",\"customerType\":\"RESIDENTIAL\"," \
      << "\"blockCustomerName\":false,\"customerName\":\"" << ua.name << "\"}"

#define ALIANZA_API_DIRECTORY_LISTING "\"directoryListing\":{\"listed\":false}"

AlianzaApiTestHelper::AlianzaApiTestHelper()
{
}

AlianzaApiTestHelper::~AlianzaApiTestHelper()
{
}

void AlianzaApiTestHelper::createMessageForAuthorization(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageForAuthorization(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageForAuthorization(const std::string& username, const std::string& password, const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   urlStr << config.api.publicApiUrl << "authorize";
   url = urlStr.str();

   // safeCout("createMessageForAuthorization(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"username\":\"" << username << "\",\"password\":\"" << password << "\"}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageForAuthorization(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageForAuthorization(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   createMessageForAuthorization(config.api.apiUsername, config.api.apiPassword, config, info, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToAddNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToAddNumberInPartition(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToAddNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << "/telephonenumber";
   url = urlStr.str();
   // safeCout("createMessageToAddNumberInPartition(): command: " << url.c_str());
   AlianzaSipUaInfo ua("");
   info.getUa(ua);

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"partitionId\":\"" << config.partitionId << "\",\"phoneNumber\":\"" << ua.phoneNumber << "\","
      << "\"carrierId\":\"" << config.carrierId << "\",\"functionType\":\"ELS\"}";

   messageBody = messageBodyStr.str();
   // safeCout("createMessageToAddNumberInPartition(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToAddNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToAddNumberInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToAddNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/telephonenumber";
   url = urlStr.str();
   // safeCout("createMessageToAddNumberInAccount(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"partitionId\":\"" << config.partitionId << "\",\"accountId\":\"" << info.accountId << "\","
      << ALIANZA_API_CUSTOMER_SERVICE_RECORD << "," << ALIANZA_API_DIRECTORY_LISTING
      << ",\"telephoneNumbers\":[{\"phoneNumber\":\"" << ua.phoneId << "\","
      << "\"referenceType\":\"END_USER\",\"referenceId\":\"" << ua.userId << "\"}]}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageToAddNumberInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToUpdateNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToUpdateNumberInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToUpdateNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/telephonenumber/" << ua.phoneId;
   url = urlStr.str();
   // safeCout("createMessageToUpdateNumberInAccount(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"phoneNumber\":\"" << ua.phoneId << "\",\"referenceType\":\"END_USER\","
      << "\"referenceId\":\"" << ua.userId << "\",\"functionType\":\"ELS\","
      // << "\"servicePackageType\":\"PrimaryLocalAndDomesticLDFlatRate\","
      << "\"accountId\":\"" << info.accountId << "\","
      << "\"partitionId\":\"" << config.partitionId << "\","
      << ALIANZA_API_CUSTOMER_SERVICE_RECORD
      << ",\"carrierStatus\":\"ACTIVATION_PENDING\","
      << "\"directoryListing\":{\"listed\":false,\"type\":\"NOT_LIST_NOT_PUBLISH\"},"
      << "\"ringType\":\"StandardRing\",\"carrierId\":\"" << config.carrierId << "\","
      << "\"tollFree\":false,\"id\":\"" << ua.phoneId << "\"}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageToUpdateNumberInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToUpdateUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToUpdateUserInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToUpdateUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/user/" << ua.userId;
   url = urlStr.str();
   // safeCout("createMessageToUpdateUserInAccount(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"id\":\"" << ua.userId << "\",\"username\":\"" << ua.username << "\","
      << "\"firstName\":\"" << ua.firstName << "\",\"lastName\":\"" << ua.lastName << "\","
      << "\"emailAddress\":\"" << ua.emailAddress << "\",\"mustChangePassword\":false,"
      << "\"languageTag\":\"en-US\",\"partitionId\":\"" << config.partitionId << "\",\"accountId\":\"" << info.accountId << "\","
      << "\"callingPlans\":[{\"referenceId\":\"" << ua.userId << "\",\"referenceType\":\"END_USER\","
      << "\"callingPlanProductId\":\"" << info.plan.callingPlanProductId
      << "\",\"startDate\":\"" << info.plan.startDate << "\",\"planMinutes\":" << info.plan.planMinutes << ","
      << "\"secondsRemaining\":" << info.plan.secondsRemaining << "}],\"devices\":[{\"id\":\"" << /*ua.deviceId << */"\","
      << "\"deviceTypeId\":\"CCSoftphone\",\"accountId\":\"" << info.accountId << "\","
      << "\"partitionId\":\"" << config.partitionId << "\",\"deviceName\":\"" << ua.sipUsername << "\","
      << "\"faxEnabled\":false,\"lineNumber\":0,\"userId\":\"" << ua.userId << "\",\"sipUsername\":\"" << ua.sipUsername << "\","
      << "\"lineType\":\"Line\"}],\"timeZone\":\"US/Mountain\",\"extension\":\"" << ua.extension << "\","
      << "\"callerIdConfig\":{\"callerIdNumber\":\"" << ua.phoneId << "\",\"externalCallerIdVisible\":true},"
      << "\"callHandlingSettings\":{\"callWaitingEnabled\":true,\"doNotDisturbEnabled\":false,"
      << "\"callHandlingOptionType\":\"RingPhone\",\"ringPhoneCallHandling\":{\"busyCallHandling\":{\"type\":\"Busy\"},"
      << "\"noAnswerCallHandling\":{\"type\":\"RingForever\",\"timeout\":20},"
      << "\"unregisteredCallHandling\":{\"type\":\"Voicemail\"}}},"
      << "\"callScreeningSettings\":{\"anonymousCallScreen\":\"Allow\",\"anonymousRingType\":\"StandardRing\","
      << "\"tollFreeCallScreen\":\"Allow\",\"tollFreeRingType\":\"StandardRing\",\"defaultCallScreen\":\"Allow\","
      << "\"defaultRingType\":\"StandardRing\"},\"voicemailBoxId\":\"" << ua.voicemailId << "\","
      << "\"endUserType\":\"ADMIN\",\"userProductPlan\":\"ADVANCED\",\"pinLockedOut\":false}";

   messageBody = messageBodyStr.str();
   // safeCout("createMessageToUpdateUserInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToDeleteNumberInPartition(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToDeleteNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << "/telephonenumber/" << ua.phoneId;
   url = urlStr.str();
   // safeCout("createMessageToDeleteNumberInPartition(): command: " << url.c_str());

   // safeCout("createMessageToDeleteNumberInPartition(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToAddAccountInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToAddAccountInPartition(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToAddAccountInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << "/account";
   url = urlStr.str();
   // safeCout("createMessageToAddAccountInPartition(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"partitionId\":\"" << config.partitionId << "\",\"accountNumber\":\"" << info.accountNumber << "\","
         << "\"accountName\":\"CreateAccount\",\"billingCycleDay\":1,\"status\":\"ACTIVE\",\"timeZone\":\"US/Mountain\","
         << "\"accountType\":\"ADVANCED\",\"platformType\":\"" << config.platformType << "\","
         << "\"inboundRatePlanProductId\":\"" << config.inboundRatePlanProductId << "\"," << "\"extensionLength\":" << config.extensionLength << ","
         << "\"dialingBehaviorType\":\"OPEN_DIAL_PLAN\",\"callScreeningSettings\":" << "{\"anonymousCallScreen\":\"Allow\","
         << "\"anonymousRingType\":\"StandardRing\",\"tollFreeCallScreen\":\"Allow\"," << "\"tollFreeRingType\":\"StandardRing\","
         << "\"defaultCallScreen\":\"Allow\",\"defaultRingType\":\"StandardRing\"},\"holdTimeoutSeconds\":300}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageToAddAccountInPartition(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToAddUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToAddUserInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToAddUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/user";
   url = urlStr.str();
   // safeCout("createMessageToAddUserInAccount(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << "{\"username\":\"" << ua.username << "\",\"password\":\"" << ua.password << "\",\"firstName\":\"" << ua.firstName << "\",\"lastName\":\"" << ua.lastName << "\","
      << "\"emailAddress\":\"" << ua.emailAddress << "\",\"mustChangePassword\":false,\"languageTag\":\"en-US\","
      << "\"partitionId\":\"" << config.partitionId << "\",\"accountId\":\"" << info.accountId << "\","
      << "\"timeZone\":\"US/Mountain\",\"extension\":\"" << ua.extension << "\",\"callerIdConfig\":{\"extensionCallerIdVisible\":true},"
      << "\"callHandlingSettings\":{\"callWaitingEnabled\":true,\"doNotDisturbEnabled\":false,\"callHandlingOptionType\":"
      << "\"RingPhone\",\"ringPhoneCallHandling\":{\"busyCallHandling\":{\"type\":\"Busy\"},\"noAnswerCallHandling\":"
      << "{\"type\":\"RingForever\"},\"unregisteredCallHandling\":{\"type\":\"Voicemail\"}}},\"callHandlingSchedules\":[],"
      << "\"endUserType\":\"ADMIN\",\"userProductPlan\":\"ADVANCED\"}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageToAddUserInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToUpdateGroupNameInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToUpdateGroupNameInAccount(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToUpdateGroupNameInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   urlStr << config.api.publicApiUrl << "cymbus/account-group";
   url = urlStr.str();
   // safeCout("createMessageToAddUserInAccount(): command: " << url.c_str());

   std::string groupName = info.accountGroupName;
   
   std::stringstream messageBodyStr;
   messageBodyStr << "{\"groupName\":\"" << groupName << "\",\"id\":\"" << info.accountId << "\",\"partitionId\":\"" << config.partitionId << "\"}";
   messageBody = messageBodyStr.str();
   // safeCout("createMessageToAddUserInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToGetClientRegistrationStatus(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   //urlStr << config.api.publicApiUrl << "cymbus-clients/partition/" << config.partitionId << "/account/" << info.accountId << "/cymbus/registrationstatus?clientId=" << ua.clientId;
   urlStr << config.api.publicApiUrl << "cymbus-clients/partition/" << config.partitionId << "/account/" << info.accountId << "/cymbus/" << ua.clientId << "/registrationstatus";
   url = urlStr.str();
   safeCout("createMessageToGetClientRegistrationStatus(): command: " << url.c_str());

   messageBody = "";
   //safeCout("createMessageToGetClientRegistrationStatus(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToFetchConfigurationInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToFetchConfigurationInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToFetchConfigurationInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   urlStr << config.api.publicApiUrl << "cymbus-clients/configuration/" << ua.clientId;

   url = urlStr.str();
   // safeCout("createMessageToFetchConfigurationInAccount(): command: " << url.c_str());

   std::stringstream messageBodyStr;
   messageBodyStr << R"({"description": "CPCAPI2 large tests" })";

   messageBody = messageBodyStr.str();
   // safeCout("createMessageToFetchConfigurationInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToDeleteAccountInPartition(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToDeleteAccountInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << "/account/" << info.accountId;
   url = urlStr.str();
   // safeCout("createMessageToDeleteAccountInPartition(): command: " << url.c_str());

   // safeCout("createMessageToDeleteAccountInPartition(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToGetNumberInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToGetNumberInPartition(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToGetNumberInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   AlianzaSipUaInfo ua("");
   info.getUa(ua);

   ALIANZA_API_PARTITION_PREFIX << "/telephonenumber" << "/" << ua.phoneNumber;
   url = urlStr.str();
   messageBody = "";
   // safeCout("createMessageToGetNumberInPartition(): command: " << url.c_str());

   // std::stringstream messageBodyStr;
   // messageBodyStr << "{\"partitionId\":\"" << config.partitionId << "\",\"phoneNumber\":\"" << info.getUa().phoneNumber << "\","
   //   << "\"carrierId\":\"" << config.carrierId << "\",\"functionType\":\"ELS\"}";

   // messageBody = messageBodyStr.str();
   // safeCout("createMessageToGetNumberInPartition(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToDeleteNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToDeleteNumberInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToDeleteNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/telephonenumber/" << ua.phoneId;
   url = urlStr.str();
   // safeCout("createMessageToDeleteNumberInAccount(): command: " << url.c_str());

   // safeCout("createMessageToDeleteNumberInAccount(): message-body: " << messageBody.c_str());
}

void AlianzaApiTestHelper::createMessageToGetAccountsInPartition(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToGetAccountsInPartition(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToGetAccountsInPartition(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << config.partitionId << "/account";
   // urlStr << config.publicApiUrl << "partition/" << config.partitionId << "/telephonenumber/statussearch";
   url = urlStr.str();
   messageBody = "";
   // safeCout("createMessageToGetAccountsInPartition(): command: " << url.c_str());
}

void AlianzaApiTestHelper::createMessageToGetAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   createMessageToGetAccount(account.config.alianzaConfig, account.config.alianzaSession, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToGetAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_PARTITION_PREFIX << "/account/" << info.accountId;
   url = urlStr.str();
   messageBody = "";
   // safeCout("createMessageToGetAccount(): command: " << url.c_str());
}

void AlianzaApiTestHelper::createMessageToGetUserInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToGetUserInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToGetUserInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/user/" << ua.userId;
   url = urlStr.str();
   messageBody = "";
   // safeCout("createMessageToGetUserInAccount(): command: " << url.c_str());
}

void AlianzaApiTestHelper::createMessageToGetNumberInAccount(const TestAccount& account, std::string& url, std::string& messageBody)
{
   AlianzaSipUaInfo ua("");
   account.config.alianzaSession.getUa(ua);

   createMessageToGetNumberInAccount(account.config.alianzaConfig, account.config.alianzaSession, ua, url, messageBody);
}

void AlianzaApiTestHelper::createMessageToGetNumberInAccount(const AlianzaAccountConfig& config, const AlianzaSessionInfo& info, const AlianzaSipUaInfo& ua, std::string& url, std::string& messageBody)
{
   std::stringstream urlStr;
   ALIANZA_API_ACCOUNT_PREFIX << "/telephonenumber/" << ua.phoneId;
   url = urlStr.str();
   messageBody = "";
   // safeCout("createMessageToGetNumberInAccount(): command: " << url.c_str());
}

/*
Sample Response:
400 response: {
  "status" : 400,
  "messages" : [ "PhoneNumberAlreadyExists" ]
}
*/

bool AlianzaApiTestHelper::extractStatusMessagesInResponse(const std::string& response, int& status, std::vector<std::string>& messages)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("status"))
   {
      safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): node missing: status, aborting decode");
      return false;
   }
   const rapidjson::Value& statusVal = (*jsonResponse)["status"];
   if (!statusVal.IsInt())
   {
      safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): invalid status format, aborting decode");
      return false;
   }
   status = (*jsonResponse)["status"].GetInt();

   if (!jsonResponse->HasMember("messages"))
   {
      safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): node missing: messages, aborting decode");
      return false;
   }
   const rapidjson::Value& messagesVal = (*jsonResponse)["messages"];
   if (!messagesVal.IsArray())
   {
      safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): invalid messages format, aborting decode");
      return false;
   }

   for (rapidjson::Value::ConstValueIterator i = messagesVal.Begin(); i != messagesVal.End(); ++i)
   {
      if (!i->IsString())
      {
         safeCout("AlianzaApiTestHelper::extractStatusMessagesInResponse(): invalid messages element format, aborting decode");
         return false;
      }
      messages.push_back(i->GetString());
   }
   return true;
}

bool AlianzaApiTestHelper::isError(const std::string& response, const int statusCode, const std::string& message)
{
   int status(0);
   std::vector<std::string> messages;
   std::string errorType("");
   bool found = false;
   if (extractStatusMessagesInResponse(response, status, messages))
   {
      if (status == statusCode)
      {
         for (auto i = messages.begin(); i != messages.end(); ++i)
         {
            if ((*i).compare(message) == 0)
            {
               found = true;
               break;
            }
         }
      }
   }
   return found;
}

bool AlianzaApiTestHelper::isErrorPhoneNumberAlreadyExists(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "PhoneNumberAlreadyExists"));
}

bool AlianzaApiTestHelper::isErrorDuplicateAccountNumber(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "DuplicateAccountNumber"));
}

bool AlianzaApiTestHelper::isErrorDuplicateUsername(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "DuplicateUsername"));
}

bool AlianzaApiTestHelper::isErrorSipUsernameInUse(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "SipUsernameInUse"));
}

bool AlianzaApiTestHelper::isErrorEmailAddressInUse(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "EmailAddressInUse"));
}

bool AlianzaApiTestHelper::isErrorTNDisconnectEventInProgress(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "TNDisconnectEventInProgress"));
}

bool AlianzaApiTestHelper::isErrorInvalidStatus(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "InvalidStatus"));
}

bool AlianzaApiTestHelper::isErrorTelephoneNumbersExist(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "TelephoneNumbersExist"));
}

bool AlianzaApiTestHelper::isErrorPortRequiredTelephoneNumberNotInInventory(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "PortRequiredTelephoneNumberNotInInventory"));
}

/*
Sample Response:
404 response: {
  "status" : 404,
  "messages" : [ "EntityNotFound" ],
  "data" : {
    "type" : "TelephoneNumberInventory",
    "id" : "***********"
  }
}
*/

bool AlianzaApiTestHelper::extractDataTypeInResponse(const std::string& response, std::string& dataType)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractDataTypeInResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }

   if (!jsonResponse->HasMember("data"))
   {
      safeCout("AlianzaApiTestHelper::extractDataTypeInResponse(): node missing: data, aborting decode");
      return false;
   }
   const rapidjson::Value& dataVal = (*jsonResponse)["data"];
   if (!dataVal.IsObject())
   {
      safeCout("AlianzaApiTestHelper::extractDataTypeInResponse(): invalid data format, aborting decode");
      return false;
   }

   if (!dataVal.HasMember("type"))
   {
      safeCout("AlianzaApiTestHelper::extractDataTypeInResponse(): node missing: type, aborting decode");
      return false;
   }
   const rapidjson::Value& typeVal = dataVal["type"];
   if (!typeVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractDataTypeInResponse(): invalid type format, aborting decode");
      return false;
   }
   dataType = typeVal.GetString();
   return true;
}

bool AlianzaApiTestHelper::isErrorTelephoneNumberEntityNotFound(const std::string& response)
{
   if (AlianzaApiTestHelper::isError(response, 404, "EntityNotFound"))
   {
      std::string dataType("");
      if (extractDataTypeInResponse(response, dataType))
      {
         if (dataType.compare("TelephoneNumberInventory") == 0)
         {
            return true;
         }
      }
   }
   return false;
}

/*
400 response: {
  "status" : 400,
  "messages" : [ "ServiceActivationEventInProgress" ],
  "data" : [ "Telephone number '12228998210' has a service activation event type of 'ACTIVATION' in a 'IN_PROGRESS' state. Please correct and retry." ]
}
*/
bool AlianzaApiTestHelper::isErrorServiceActivationEventInProgress(const std::string& response)
{
   return (AlianzaApiTestHelper::isError(response, 400, "ServiceActivationEventInProgress"));
}

bool AlianzaApiTestHelper::extractPhoneIdFromCreateNumberInPartitionResponse(const std::string& response, std::string& phoneId)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractPhoneIdFromCreateNumberInPartitionResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("id"))
   {
      safeCout("AlianzaApiTestHelper::extractPhoneIdFromCreateNumberInPartitionResponse(): node missing: id, aborting decode");
      return false;
   }
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractPhoneIdFromCreateNumberInPartitionResponse(): invalid id format, aborting decode");
      return false;
   }
   phoneId = (*jsonResponse)["id"].GetString();
   return true;
}

bool AlianzaApiTestHelper::extractAccountIdFromCreateAccountInPartitionResponse(const std::string& response, std::string& accountId)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractAccountIdFromCreateAccountInPartitionResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("id"))
   {
      safeCout("AlianzaApiTestHelper::extractAccountIdFromCreateAccountInPartitionResponse(): node missing: id, aborting decode");
      return false;
   }
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractAccountIdFromCreateAccountInPartitionResponse(): invalid id format, aborting decode");
      return false;
   }
   accountId = (*jsonResponse)["id"].GetString();
   return true;
}

bool AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(const std::string& response, std::string& userId, std::string& voicemailId, std::string& extension, AlianzaCallingPlanInfo& callingPlan)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("id"))
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: id, aborting decode");
      return false;
   }
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["id"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid id format, aborting decode");
      return false;
   }
   userId = (*jsonResponse)["id"].GetString();

   if (!jsonResponse->HasMember("voicemailBoxId"))
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: voicemailBoxId, aborting decode");
      return false;
   }
   const rapidjson::Value& voicemailBoxIdVal = (*jsonResponse)["voicemailBoxId"];
   if (!voicemailBoxIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid voicemailBoxId format, aborting decode");
      return false;
   }
   voicemailId = voicemailBoxIdVal.GetString();

   if (!jsonResponse->HasMember("extension"))
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: extension, aborting decode");
      return false;
   }
   const rapidjson::Value& extensionVal = (*jsonResponse)["extension"];
   if (!extensionVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid extension format, aborting decode");
      return false;
   }
   extension = extensionVal.GetString();

   // Populate calling plan
   if (!jsonResponse->HasMember("callingPlans"))
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: callingPlans, aborting decode");
      return false;
   }
   const rapidjson::Value& callingPlansVal = (*jsonResponse)["callingPlans"];
   if (!callingPlansVal.IsArray())
   {
      safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid callingPlans format, aborting decode");
      return false;
   }

   for (rapidjson::Value::ConstValueIterator i = callingPlansVal.Begin(); i != callingPlansVal.End(); ++i)
   {
      if (!i->IsObject())
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid callingPlans format, aborting decode");
         return false;
      }
      if (!i->HasMember("callingPlanProductId"))
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: callingPlanProductId, aborting decode");
         return false;
      }
      const rapidjson::Value& callingPlanProductIdVal = (*i)["callingPlanProductId"];
      if (!callingPlanProductIdVal.IsString())
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid callingPlanProductId format, aborting decode");
         return false;
      }
      callingPlan.callingPlanProductId = callingPlanProductIdVal.GetString();

      if (!i->HasMember("startDate"))
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: startDate, aborting decode");
         return false;
      }
      const rapidjson::Value& startDateVal = (*i)["startDate"];
      if (!startDateVal.IsString())
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid startDate format, aborting decode");
         return false;
      }
      callingPlan.startDate = startDateVal.GetString();

      if (!i->HasMember("planMinutes"))
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: planMinutes, aborting decode");
         return false;
      }
      const rapidjson::Value& planMinutesVal = (*i)["planMinutes"];
      if (!planMinutesVal.IsInt())
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid planMinutes format, aborting decode");
         return false;
      }
      callingPlan.planMinutes = planMinutesVal.GetInt();

      if (!i->HasMember("secondsRemaining"))
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): node missing: secondsRemaining, aborting decode");
         return false;
      }
      const rapidjson::Value& secondsRemainingVal = (*i)["secondsRemaining"];
      if (!secondsRemainingVal.IsInt())
      {
         safeCout("AlianzaApiTestHelper::extractDataFromAddUserInAccountResponse(): invalid secondsRemaining format, aborting decode");
         return false;
      }
      callingPlan.secondsRemaining = secondsRemainingVal.GetInt();
      break;
   }
   return true;
}

bool AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse(const std::string& response, std::string& authToken)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());

   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse: invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode: " << response.c_str());
      return false;
   }
   if (!jsonResponse->HasMember("authToken"))
   {
      safeCout("AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse: node missing: authToken, aborting decode");
      return false;
   }
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["authToken"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse: invalid authToken format, aborting decode");
      return false;
   }

   authToken = (*jsonResponse)["authToken"].GetString();
   if (authToken.size() < 1)
   {
      safeCout("AlianzaApiTestHelper::extractUserAuthTokenFromUserAuthResponse: authToken not populated");
      return false;
   }
   return true;
}

/*
{
  "pushSipAccount": {
    "username": "pushuser",
    "password": "pushpass",
    "domain": "pushdomian.net"
  },
  "traditionalSipAccount": {
    "username": "sipuser",
    "password": "sippass",
    "domain": "sipdomain.net"
  },
  "conversation": {
    "holdMode": "RFC3264",
    "prackMode": "DISABLED",
    "dtmfModes": [
      "RFC2833"
    ]
  },
  "voiceMail": {
    "voiceMailNumber": "*36"
  },
  "autoUpdate": {
    "url": "https://autoupdate.com"
  },
  "pushServer": {
    "url": "https://pushserver.com/push"
  }
}
*/
bool AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(const std::string& response, AlianzaSipUaInfo& uaInfo)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("pushSipAccount") || !jsonResponse->HasMember("traditionalSipAccount") || !jsonResponse->HasMember("conversation") ||
         !jsonResponse->HasMember("voiceMail") || !jsonResponse->HasMember("autoUpdate"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing, aborting decode");
      return false;
   }
   const rapidjson::Value& pushSipAccount = (*jsonResponse)["pushSipAccount"];
   if (!pushSipAccount.HasMember("username") || !pushSipAccount.HasMember("password") || !pushSipAccount.HasMember("domain"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing in <pushSipAccount>, aborting decode");
      return false;
   }
   const rapidjson::Value& traditionalSipAccount = (*jsonResponse)["traditionalSipAccount"];
   if (!traditionalSipAccount.HasMember("username") || !traditionalSipAccount.HasMember("password") || !traditionalSipAccount.HasMember("domain"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing in <traditionalSipAccount>, aborting decode");
      return false;
   }
   const rapidjson::Value& conversation = (*jsonResponse)["conversation"];
   if (!conversation.HasMember("holdMode") || !conversation.HasMember("prackMode") || !conversation.HasMember("dtmfModes"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing in <conversation>, aborting decode");
      return false;
   }
   const rapidjson::Value& voiceMail = (*jsonResponse)["voiceMail"];
   if (!voiceMail.HasMember("voiceMailNumber"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing in <voiceMail>, aborting decode");
      return false;
   }
   const rapidjson::Value& autoUpdate = (*jsonResponse)["autoUpdate"];
   if (!autoUpdate.HasMember("url"))
   {
      safeCout("AlianzaApiTestHelper::extractClientConfigFromQueryClientConfigResponse(): node missing in <autoUpdate>, aborting decode");
      return false;
   }
   
   uaInfo.pushSipUsername = pushSipAccount["username"].GetString();
   uaInfo.pushSipPassword = pushSipAccount["password"].GetString();
   uaInfo.pushSipDomain = pushSipAccount["domain"].GetString();

   uaInfo.sipUsername = traditionalSipAccount["username"].GetString();
   uaInfo.sipPassword = traditionalSipAccount["password"].GetString();
   uaInfo.sipDomain = traditionalSipAccount["domain"].GetString();

   uaInfo.conversationHoldMode = conversation["holdMode"].GetString();
   uaInfo.conversationPrackMode = conversation["prackMode"].GetString();

   uaInfo.autoUpdateUrl = autoUpdate["url"].GetString();

   return true;
}

bool AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse(const std::string& response, std::string& serviceStatus)
{
   std::shared_ptr<rapidjson::Document> jsonResponse(new rapidjson::Document);
   jsonResponse->Parse<0>(response.c_str());
   if (jsonResponse->HasParseError())
   {
      safeCout("AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse(): invalid request format, parse error occured:" << jsonResponse->GetParseError() << " aborting decode");
      return false;
   }
   if (!jsonResponse->HasMember("carrierStatus"))
   {
      safeCout("AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse: node missing: carrierStatus, aborting decode");
      return false;
   }
   const rapidjson::Value& moduleIdVal = (*jsonResponse)["carrierStatus"];
   if (!moduleIdVal.IsString())
   {
      safeCout("AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse: invalid carrierStatus format, aborting decode");
      return false;
   }

   serviceStatus = (*jsonResponse)["carrierStatus"].GetString();
   if (serviceStatus.size() < 1)
   {
      safeCout("AlianzaApiTestHelper::extractServiceStatusForNumberInAccountResponse: carrierStatus not populated");
      return false;
   }
   
   return true;
}
/*
400 response: {
  "status" : 400,
  "messages" : [ "PhoneNumberAlreadyExists" ]
}
*/
void AlianzaApiTestHelper::createErrorResponse(const int status, const std::string& reason, std::string& response)
{
   std::stringstream message;
   message << "{\"status\" : " << status << ", \"messages\" : [ \"" << reason << "\" ]}";
   response = message.str();
   // safeCout("AlianzaApiTestHelper::createErrorResponse(): response: " << response.c_str());
}

/*
404 response: {
  "status" : 404,
  "messages" : [ "EntityNotFound" ],
  "data" : {
    "type" : "TelephoneNumberInventory",
    "id" : "***********"
  }
}
*/
void AlianzaApiTestHelper::createErrorResponseWithDataObject(const int status, const std::string& reason, const std::string& type, const std::string& id, std::string& response)
{
   std::stringstream message;
   message << "{\"status\" : " << status << ", \"messages\" : [ \"" << reason << "\" ],";
   message << "\"data\" : { \"type\" : \"" << type << "\", \"id\" : \"" << id << "\" } }";
   response = message.str();
   // safeCout("AlianzaApiTestHelper::createErrorResponseWithDataObject(): response: " << response.c_str());
}

/*
400 response: {
  "status" : 400,
  "messages" : [ "ServiceActivationEventInProgress" ],
  "data" : [ "Telephone number '12228998210' has a service activation event type of 'ACTIVATION' in a 'IN_PROGRESS' state. Please correct and retry." ]
}
*/
void AlianzaApiTestHelper::createErrorResponseWithDataList(const int status, const std::string& reason, const std::string& data, std::string& response)
{
   std::stringstream message;
   message << "{\"status\" : " << status << ", \"messages\" : [ \"" << reason << "\" ],";
   message << "\"data\" : [ \"" << data << "\" ] }";
   response = message.str();
   // safeCout("AlianzaApiTestHelper::createErrorResponseWithDataList(): response: " << response.c_str());
}

void AlianzaApiTestHelper::createResponseWithId(const std::string& id, std::string& response)
{
   std::stringstream message;
   message << "{\"id\" : \"" << id << "\"}";
   response = message.str();
   // safeCout("AlianzaApiTestHelper::createResponseWithId(): response: " << response.c_str());
}

void AlianzaApiTestHelper::createResponseForAddUserRequest(const std::string& userId, const std::string& voicemailId, const std::string& planId, const std::string& extension, std::string& response)
{
   std::stringstream message;
   message << "{\"id\" : \"" << userId << "\", \"voicemailBoxId\" : \"" << voicemailId << "\", \"extension\" : \"" << extension << "\", \"callingPlans\" : ";
   message << "[{\"callingPlanProductId\" : \"" << planId << "\", \"startDate\" : \"2020-01-01\", \"planMinutes\" : 99999, \"secondsRemaining\" : 5999940}]}";
   response = message.str();
   // safeCout("AlianzaApiTestHelper::createResponseForAddUserRequest(): response: " << response.c_str());
}

void AlianzaApiTestHelper::createResponseForUserAuthRequest(const std::string& authToken, std::string& response)
{
   std::stringstream message;
   message << "{\"authToken\" : \"" << authToken << "\"}";
   response = message.str();
   //safeCout("AlianzaApiTestHelper::createResponseForUserAuthRequest(): response: " << response.c_str());
}

void AlianzaApiTestHelper::createResponseForQueryUserConfigRequest(const std::string& sipUser, const std::string& sipPassword, const std::string& sipDomain, std::string& response)
{
   std::stringstream message;
   message << "{\"pushSipAccount\" : {\"username\" : \"pushUser\", \"password\" : \"pushPassword\", \"domain\" : \"pushDomain.net\"}, ";
   message << "\"traditionalSipAccount\" : {\"username\":\"" << sipUser << "\", \"password\" : \"" << sipPassword << "\", \"domain\" : \"" << sipDomain << "\"}, ";
   message << "\"conversation\" : {\"holdMode\":\"RFC3264\", \"prackMode\" : \"DISABLED\", \"dtmfModes\" : [\"RFC2833\"] },";
   message << "\"voiceMail\" : {\"voiceMailNumber\":\"vmn\"}, \"autoUpdate\" : {\"url\":\"https://autoupdateurl.com\"}, \"pushServer\" : {\"url\":\"https://pushserverurl.com\"}}";
   response = message.str();
   //safeCout("AlianzaApiTestHelper::createResponseForQueryUserConfigRequest(): response: " << response.c_str());
}

void AlianzaApiTestHelper::createResponseForCheckNumberInAccountRequest(const std::string& status, std::string& response)
{
   std::stringstream message;
   message << "{\"carrierStatus\" : \"" << status << "\"}";
   response = message.str();
   //safeCout("AlianzaApiTestHelper::createResponseForUserAuthRequest(): response: " << response.c_str());
}
