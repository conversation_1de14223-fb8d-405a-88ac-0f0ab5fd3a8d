#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#include "cpcapi2_test_fixture.h"
#include "test_framework/sipp_runner.h"

using namespace CPCAPI2;
using namespace CPCAPI2::SipPresence;
using namespace CPCAPI2::SipEvent;
using namespace CPCAPI2::test;

class PresenceSubscriptionTests : public CpcapiAutoTest
{
public:
   PresenceSubscriptionTests() {}
   virtual ~PresenceSubscriptionTests() {}
};


TEST_F(PresenceSubscriptionTests, CannedStatusParseUnparse) {
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

   std::list<CannedStatus> status;
   status.push_back(CannedStatus_Available);
   status.push_back(CannedStatus_Busy);
   status.push_back(CannedStatus_Away);
   status.push_back(CannedStatus_OnThePhone);
   status.push_back(CannedStatus_NotAvailable);
   status.push_back(CannedStatus_DND);
   status.push_back(CannedStatus_AppearOffline);
   status.push_back(CannedStatus_Other);

   // First we set up an active subscription from alice to bob

   SipEventSubscriptionHandle bobSubs;
   SipEventSubscriptionHandle hdl;
   { // bob receive new sub
      NewPresenceSubscriptionEvent evt;
	   ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription",
            5000, AlwaysTruePred(), bobSubs, evt));
      Presence empty;
      empty.entity = bob.config.uri();
      // bob send first presence
	   ASSERT_EQ(bob.presence->accept(bobSubs, empty), kSuccess);
   }
   { // alice sub started
      NewPresenceSubscriptionEvent evt;
	   ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription",
            5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
   { // bob subscription activate
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   5000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   { // alice subscription activate
      PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
            5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   { // alice receives bobs empty presence
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			   5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }

   // subscription alice->bob is ready, now test each canned status
   for(std::list<CannedStatus>::iterator it = status.begin(); it != status.end(); it++)
   {
      SipEventSubscriptionHandle hdl;

      CannedStatus stat = *it;
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend",
               15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			      5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bob.config.uri(), evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
   }

   // clean up afterwards

   { // bob ends presence
      ASSERT_EQ(bob.presence->end(bobSubs), kSuccess);
		PresenceSubscriptionEndedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionEnded",
		   	5000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
   }

   { // alice receives end
		PresenceSubscriptionEndedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionEnded",
		   	5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
}

TEST_F(PresenceSubscriptionTests, NonConformantEntityUri)
{
   TestAccount alice("alice");
   TestAccount bob("bob");

   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.presence->addParticipant(aliceSubs, bob.config.uri());
   alice.presence->start(aliceSubs);

   cpc::string bobUsername = bob.config.settings.username;
   cpc::string bobSipSchemeUsername = "sip:" + bob.config.settings.username;
   cpc::string bobPresenceSchemeUsername = "pres:" + bob.config.settings.username;
   cpc::string bobUri = bob.config.uri();
   cpc::string bobSipSchemeUri = "sip:" + bob.config.uri();
   cpc::string bobPresenceSchemeUri = "pres:" + bob.config.uri();

   std::list<CannedStatus> status;
   status.push_back(CannedStatus_Available);
   status.push_back(CannedStatus_Busy);
   status.push_back(CannedStatus_Away);
   status.push_back(CannedStatus_OnThePhone);
   status.push_back(CannedStatus_NotAvailable);
   status.push_back(CannedStatus_DND);
   status.push_back(CannedStatus_AppearOffline);
   status.push_back(CannedStatus_Other);

   // First we set up an active subscription from alice to bob

   SipEventSubscriptionHandle bobSubs;
   SipEventSubscriptionHandle hdl;
   {
      // bob receive new sub
      NewPresenceSubscriptionEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription", 5000, AlwaysTruePred(), bobSubs, evt));
      Presence empty;
      empty.entity = bobUsername;

      // bob send first presence
      ASSERT_EQ(bob.presence->accept(bobSubs, empty), kSuccess);
   }
   {
      // alice sub started
      NewPresenceSubscriptionEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
   {
      // bob subscription activate
      PresenceSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   {
      // alice subscription activate
      PresenceSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   {
      // alice receives bobs empty presence
      IncomingPresenceStatusEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }

   // subscription alice->bob is ready, now test each canned status with unique entity formats
   for (std::list<CannedStatus>::iterator it = status.begin(); it != status.end(); it++)
   {
      SipEventSubscriptionHandle hdl;

      CannedStatus stat = *it;
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobUsername;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobUsername, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobSipSchemeUsername;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobSipSchemeUsername, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobPresenceSchemeUsername;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobPresenceSchemeUsername, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobUri;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobUri, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobSipSchemeUri;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobSipSchemeUri, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
      {
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, stat), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend", 15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         evt.presence.entity = bobPresenceSchemeUri;
         ASSERT_EQ(bob.presence->notify(bobSubs, evt.presence), kSuccess);
      }
      {
         IncomingPresenceStatusEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(bobPresenceSchemeUri, evt.presence.entity);
         ASSERT_EQ(stat, evt.status);
      }
   }

   {
      // bob ends presence
      ASSERT_EQ(bob.presence->end(bobSubs), kSuccess);
      PresenceSubscriptionEndedEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionEnded", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
   }

   {
      // alice receives end
      PresenceSubscriptionEndedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionEnded", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
}

TEST_F(PresenceSubscriptionTests, BasicSubscribe_BuildSanity) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
      ASSERT_EQ(bob.presence->provisionalAccept(bobSubs), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }

      {
      Presence evt1234;
      Person persBob;
      Note bobNote;
      bobNote.text = "heya wassap";
      persBob.notes.push_back(bobNote);
      evt1234.persons.push_back(persBob);
      evt1234.entity = bob.config.uri();
		ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      {
      Presence evt5678;
      Person persBob;
      Note bobNote;
      bobNote.text = "second message";
      persBob.notes.push_back(bobNote);
      evt5678.persons.push_back(persBob);
      evt5678.entity = bob.config.uri();
      ASSERT_EQ(bob.presence->notify(bobSubs, evt5678), kSuccess);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		ASSERT_EQ(bob.presence->end(bobSubs), kSuccess);

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionEndedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionEnded",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
		ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "");
      }

	  {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	    ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Pending);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	    ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionEndedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionEnded",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PresenceSubscriptionTests, BasicSubscribeEndButNoWaitBeforeDisable) {
   TestAccount alice("alice", Account_Enable, false);
   TestAccount bob("bob", Account_Enable, false);

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
      Presence evt1234;
      Person persBob;
      Note bobNote;
      bobNote.text = "heya wassap";
      persBob.notes.push_back(bobNote);
      evt1234.persons.push_back(persBob);
      evt1234.entity = bob.config.uri();
		ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      {
      Presence evt5678;
      Person persBob;
      Note bobNote;
      bobNote.text = "second message";
      persBob.notes.push_back(bobNote);
      evt5678.persons.push_back(persBob);
      evt5678.entity = bob.config.uri();
      ASSERT_EQ(bob.presence->notify(bobSubs, evt5678), kSuccess);
      }

	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      alice.presence->end(aliceSubs);
      alice.disable();

	});


	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

   bob.disable();

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PresenceSubscriptionTests, BasicSubscribeEndUASButNoWaitBeforeDisable) {
   TestAccount alice("alice", Account_Enable, false);
   TestAccount bob("bob", Account_Enable, false);

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
      Presence evt1234;
      Person persBob;
      Note bobNote;
      bobNote.text = "heya wassap";
      persBob.notes.push_back(bobNote);
      evt1234.persons.push_back(persBob);
      evt1234.entity = bob.config.uri();
		ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      {
      Presence evt5678;
      Person persBob;
      Note bobNote;
      bobNote.text = "second message";
      persBob.notes.push_back(bobNote);
      evt5678.persons.push_back(persBob);
      evt5678.entity = bob.config.uri();
      ASSERT_EQ(bob.presence->notify(bobSubs, evt5678), kSuccess);
      }

      bob.presence->end(bobSubs);
      bob.disable();

	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

	});


	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

   alice.disable();

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PresenceSubscriptionTests, BasicSubscribeNoEndBeforeDisable) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
      Presence evt1234;
      Person persBob;
      Note bobNote;
      bobNote.text = "heya wassap";
      persBob.notes.push_back(bobNote);
      evt1234.persons.push_back(persBob);
      evt1234.entity = bob.config.uri();
		ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      {
      Presence evt5678;
      Person persBob;
      Note bobNote;
      bobNote.text = "second message";
      persBob.notes.push_back(bobNote);
      evt5678.persons.push_back(persBob);
      evt5678.entity = bob.config.uri();
      ASSERT_EQ(bob.presence->notify(bobSubs, evt5678), kSuccess);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		//ASSERT_EQ(bob.presence->end(bobSubs), kSuccess);

  //    {
		//SipEventSubscriptionHandle h;
		//PresenceSubscriptionEndedEvent evt;
		//ASSERT_TRUE(bob.presenceEvents->expectEvent(
		//	"SipPresenceSubscriptionHandler::onSubscriptionEnded",
		//	5000,
  //       HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
  //       h, evt));
		//ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
  //    }
	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      }

  //    {
		//SipEventSubscriptionHandle h;
		//PresenceSubscriptionEndedEvent evt;
  //    ASSERT_TRUE(alice.presenceEvents->expectEvent(
		//	"SipPresenceSubscriptionHandler::onSubscriptionEnded",
		//	15000,
  //       HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
  //       h, evt));
		//ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
  //    }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}



// Test a subscription with a sort expiry to ensure that it re-subscribes as expected and doesn't end
TEST_F(PresenceSubscriptionTests, DISABLED_SubscriptionExpiryTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;

   // note: cannot be <= 5, see Helper::aBitSmallerThan
   subsSettings.expiresSeconds = 7;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

   auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      SipEventSubscriptionHandle hdl;
      { // receive subscription
		   NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent( "SipPresenceSubscriptionHandler::onNewSubscription",
            15000, AlwaysTruePred(), bobSubs, evt));
		   ASSERT_EQ(SipSubscriptionType_Incoming, evt.subscriptionType);
		   ASSERT_EQ(alice.config.uri(), evt.remoteAddress);
      }
      
      
      { // send a presence update to accept subscription (and send 200 OK)
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, SipPresence::CannedStatus_Available), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(bob.config.uri(), evt.presence.entity);
         ASSERT_EQ(kSuccess, bob.presence->accept(bobSubs, evt.presence));
      }

      { // expect the subscription change to active now that we sent presence
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(SipSubscriptionState_Active, evt.subscriptionState);
      }

      eventDebug("Bob waiting 8 seconds to send next presence update");

      // wait until first subscription would have ended and we must have re-subscribed
      std::this_thread::sleep_for(std::chrono::seconds(8));

      { // send a new presence update to make sure that subscription is still active
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, SipPresence::CannedStatus_Busy), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(bob.config.uri(), evt.presence.entity);
         ASSERT_EQ(kSuccess, bob.presence->notify(bobSubs, evt.presence));
      }
   });



   auto aliceEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle aliceSubs = 0;
      SipEventSubscriptionHandle hdl;
      { // subscription to bob success
         NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent( "SipPresenceSubscriptionHandler::onNewSubscription",
            15000, AlwaysTruePred(), aliceSubs, evt));
		   ASSERT_EQ(SipSubscriptionType_Outgoing, evt.subscriptionType);
		   ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      { // expect to receive the subscription change
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(SipSubscriptionState_Active, evt.subscriptionState);
      }

      { // receive first status update from bob
		   IncomingPresenceStatusEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ("Available", evt.presence.persons[0].notes[0].text);
         ASSERT_FALSE(evt.presence.persons[0].activities.present);
      }

      { // receive first status update from bob
		   IncomingPresenceStatusEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_TRUE(evt.presence.persons[0].activities.present);
         ASSERT_EQ(evt.presence.persons[0].activities->activities[0].activity, SipPresence::ActivityType_Busy);
         ASSERT_EQ(evt.presence.entity, bob.config.uri());
      }
   });

	ASSERT_EQ(std::future_status::ready, bobEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(std::future_status::ready, aliceEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(aliceEvents.get());
}


// Test a subscription which is rejected
TEST_F(PresenceSubscriptionTests, SubscriptionRejected) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;

   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

   auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      SipEventSubscriptionHandle hdl;
      { // receive subscription attempt
		   NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onNewSubscription", 
            15000, AlwaysTruePred(), bobSubs, evt));
		   ASSERT_EQ(SipSubscriptionType_Incoming, evt.subscriptionType);
		   ASSERT_EQ(alice.config.uri(), evt.remoteAddress);
      }
      
      { // send a rejection of the subscription
         ASSERT_EQ(kSuccess, bob.presence->reject(bobSubs));
      }

      { // expect the subscription to change to terminated
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(SipSubscriptionState_Terminated, evt.subscriptionState);
      }

      { // subscription has ended
         PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionEnded",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
		   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

   auto aliceEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle s = 0;
      { // alice terminates the subscription
         ASSERT_EQ(kSuccess, alice.presence->end(aliceSubs));
         PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionEnded",
			   15000, AlwaysTruePred(), s, evt));
         ASSERT_EQ(s, aliceSubs);
         ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

	ASSERT_EQ(std::future_status::ready, bobEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(std::future_status::ready, aliceEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(aliceEvents.get());
}


// Test a subscription in which the subscriber (alice) ends the subscription manually
TEST_F(PresenceSubscriptionTests, DISABLED_SubscriptionEndBySubscriber) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;

   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

   auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      SipEventSubscriptionHandle hdl;
      { // receive subscription
		   NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent( "SipPresenceSubscriptionHandler::onNewSubscription",
            15000, AlwaysTruePred(), bobSubs, evt));
		   ASSERT_EQ(SipSubscriptionType_Incoming, evt.subscriptionType);
		   ASSERT_EQ(alice.config.uri(), evt.remoteAddress);
      }
      
      
      { // send a presence update to accept subscription (and send 200 OK)
         PresenceReadyToSendEvent evt;
         ASSERT_EQ(bob.presence->preparePresence(bobSubs, SipPresence::CannedStatus_Available), kSuccess);
         ASSERT_TRUE(bob.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onPresenceReadyToSend",
            15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(bob.config.uri(), evt.presence.entity);
         ASSERT_EQ(kSuccess, bob.presence->accept(bobSubs, evt.presence));
      }

      { // expect the subscription change to active now that we sent presence
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
         ASSERT_EQ(SipSubscriptionState_Active, evt.subscriptionState);
      }

      { // wait for alice to end the subscription
		   PresenceSubscriptionEndedEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionEnded",
			   5000, HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs), hdl, evt));
		   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });



   auto aliceEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle aliceSubs = 0;
      SipEventSubscriptionHandle hdl;
      { // subscription to bob success
         NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent( "SipPresenceSubscriptionHandler::onNewSubscription",
            15000, AlwaysTruePred(), aliceSubs, evt));
		   ASSERT_EQ(SipSubscriptionType_Outgoing, evt.subscriptionType);
		   ASSERT_EQ(bob.config.uri(), evt.remoteAddress);
      }

      { // expect to receive the subscription change
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ(SipSubscriptionState_Active, evt.subscriptionState);
      }

      { // receive first status update from bob
		   IncomingPresenceStatusEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
         ASSERT_EQ("Available", evt.presence.persons[0].notes[0].text);
         ASSERT_FALSE(evt.presence.persons[0].activities.present);
      }

      { // alice terminates the subscription
         ASSERT_EQ(kSuccess, alice.presence->end(aliceSubs));
         PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionEnded",
			   15000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
		   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
   });

	ASSERT_EQ(std::future_status::ready, bobEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(std::future_status::ready, aliceEvents.wait_for(std::chrono::milliseconds(60000)));
	ASSERT_NO_THROW(aliceEvents.get());
}

TEST_F(PresenceSubscriptionTests, DISABLED_CannedStatusTest) {
   TestAccount alice("alice");
   TestAccount bob("bob");

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

      // Bob prepares a canned presence status update
      Presence bobPres1;
      {
      ASSERT_EQ(bob.presence->preparePresence(bobSubs, SipPresence::CannedStatus_Available), kSuccess);
      SipEventSubscriptionHandle h;
      PresenceReadyToSendEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent(
         "SipPresenceSubscriptionHandler::onPresenceReadyToSend",
         5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.presence.entity, bob.config.uri());
      bobPres1 = evt.presence;
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
		ASSERT_EQ(bob.presence->accept(bobSubs, bobPres1), kSuccess);
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      // Bob prepares and sends another canned presence status update
      {
      Presence bobPres2;
      ASSERT_EQ(bob.presence->preparePresence(bobSubs, SipPresence::CannedStatus_Busy), kSuccess);
      SipEventSubscriptionHandle h;
      PresenceReadyToSendEvent evt;
      ASSERT_TRUE(bob.presenceEvents->expectEvent(
         "SipPresenceSubscriptionHandler::onPresenceReadyToSend",
         5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
      ASSERT_EQ(evt.presence.entity, bob.config.uri());
      bobPres2 = evt.presence;
      ASSERT_EQ(bob.presence->notify(bobSubs, bobPres2), kSuccess);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

		ASSERT_EQ(bob.presence->end(bobSubs), kSuccess);

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionEndedEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionEnded",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
         h, evt));
		ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// Overview of Alice's thread:
	//  -
	auto aliceEvents = std::async(std::launch::async, [&] () {
      {
		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionStateChangedEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
   	ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      ASSERT_EQ(evt.presence.persons[0].notes[0].text, "Available");
      ASSERT_FALSE(evt.presence.persons[0].activities.present);
      }

      {
		SipEventSubscriptionHandle h;
		IncomingPresenceStatusEvent evt;
		ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			5000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
      ASSERT_TRUE(evt.presence.persons[0].activities.present);
      ASSERT_EQ(evt.presence.persons[0].activities->activities[0].activity, SipPresence::ActivityType_Busy);
      ASSERT_EQ(evt.presence.entity, bob.config.uri());
      }

      {
		SipEventSubscriptionHandle h;
		PresenceSubscriptionEndedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onSubscriptionEnded",
			15000,
         HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
         h, evt));
		ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }
	});

	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PresenceSubscriptionTests, Deactivated) {
   TestAccount alice("alice", Account_Enable, false);
   TestAccount bob("bob", Account_Enable, false);

	// make an outgoing subscription from Alice to Bob using the demo.xten.com server
	SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
	alice.presence->addParticipant(aliceSubs, bob.config.uri());
	alice.presence->start(aliceSubs);

	// Overview of Bob's thread:
	//  - 
	auto bobEvents = std::async(std::launch::async, [&] () 
   {
		SipEventSubscriptionHandle bobSubs = 0;
      {
		   SipEventSubscriptionHandle h;
		   NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onNewSubscription", 
			   5000,
			   AlwaysTruePred(),
            h, evt));
		   bobSubs = h;
		   ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		   ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		   ASSERT_EQ(evt.remoteDisplayName, "alice");
      }

		// Bob accepts the subscription (200 OK) -- this should trigger state transitions for both Bob and Alice
      {
         Presence evt1234;
         Person persBob;
         Note bobNote;
         bobNote.text = "heya wassap";
         persBob.notes.push_back(bobNote);
         evt1234.persons.push_back(persBob);
         evt1234.entity = bob.config.uri();
		   ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

      {
		   SipEventSubscriptionHandle h;
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(bob.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   15000,
            HandleEqualsPred<SipEventSubscriptionHandle>(bobSubs),
            h, evt));
         ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));

      {
         Presence evt5678;
         Person persBob;
         Note bobNote;
         bobNote.text = "second message";
         persBob.notes.push_back(bobNote);
         evt5678.persons.push_back(persBob);
         evt5678.entity = bob.config.uri();
         ASSERT_EQ(bob.presence->notify(bobSubs, evt5678), kSuccess);
      }

		std::this_thread::sleep_for(std::chrono::milliseconds(500));
      
      // N.B. using subscription interface here (a bit of a hack) so we can use deactivate as a reason
      bob.subs->end(bobSubs, SipEvent::SipSubscriptionTerminateReason_Deactivate);

		SipEventSubscriptionHandle h;
		NewPresenceSubscriptionEvent evt;
		ASSERT_TRUE(bob.presenceEvents->expectEvent(
			"SipPresenceSubscriptionHandler::onNewSubscription", 
			5000,
			AlwaysTruePred(),
         h, evt));
		bobSubs = h;
		ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
		ASSERT_EQ(evt.remoteAddress, alice.config.uri());
		ASSERT_EQ(evt.remoteDisplayName, "alice");

      // bob accepts so that alice will get a new subscription event
      {
         Presence evt1234;
         Person persBob;
         Note bobNote;
         bobNote.text = "heya wassap";
         persBob.notes.push_back(bobNote);
         evt1234.persons.push_back(persBob);
         evt1234.entity = bob.config.uri();
         ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
      }

	});

	// Overview of Alice's thread:
	//  - 
	auto aliceEvents = std::async(std::launch::async, [&] () 
   {
      {
         // for alice sending the initial subscription to bob

		   SipEventSubscriptionHandle h;
		   NewPresenceSubscriptionEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onNewSubscription", 
			   15000,
            HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
            h, evt));
		   ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
		   ASSERT_EQ(evt.remoteAddress, bob.config.uri());
		   ASSERT_EQ(evt.remoteDisplayName, "");
      }

      {
         // for alice seeing that bob has accepted the subscription

		   SipEventSubscriptionHandle h;
		   PresenceSubscriptionStateChangedEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionStateChanged",
			   5000,
            HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
            h, evt));
   	   ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
      }

      {
		   SipEventSubscriptionHandle h;
		   IncomingPresenceStatusEvent evt;
		   ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onIncomingPresenceStatus",
			   5000,
            HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
            h, evt));
      }

	   {
         // for bob ending the subscription

		   SipEventSubscriptionHandle h;
		   PresenceSubscriptionEndedEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
			   "SipPresenceSubscriptionHandler::onSubscriptionEnded",
			   15000,
            HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs),
            h, evt));
		   ASSERT_EQ(evt.endReason, SipSubscriptionEndReason_ServerEnded);
      }

      {
         // for alice automatically re-subscribing to bob, due to terminate reason deactivated

         SipEventSubscriptionHandle h;
         NewPresenceSubscriptionEvent evt;
         ASSERT_TRUE(alice.presenceEvents->expectEvent(
            "SipPresenceSubscriptionHandler::onNewSubscription",
            5000,
            AlwaysTruePred(),
            h, evt));
         aliceSubs = h;
         ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Outgoing);
         ASSERT_EQ(evt.remoteAddress, bob.config.uri());
         ASSERT_EQ(evt.remoteDisplayName, "");
      }
	});


	// std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
	ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(bobEvents.get());
	ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(15000)), std::future_status::ready);
	ASSERT_NO_THROW(aliceEvents.get());

   alice.disable();

	// not needed, but handy sometimes when debugging ...
	//std::this_thread::sleep_for(std::chrono::milliseconds(10000));
}

TEST_F(PresenceSubscriptionTests, PresenceRemoveHandler) {
   Phone* phone = Phone::create();
   phone->initialize(LicenseInfo(), (PhoneHandler*)NULL);
   
   CPCAPI2::SipAccount::SipAccountManager* acct = CPCAPI2::SipAccount::SipAccountManager::getInterface(phone);
   
   TestAccountConfig config("alice");
   TestAccount bob("bob", Account_Enable, false);
   CPCAPI2::SipAccount::SipAccountHandle accountHandle = acct->create(config.settings);
   
   class MySipPresenceSubscriptionHandler : public SipPresenceSubscriptionHandler
   {
   public:
      MySipPresenceSubscriptionHandler() : receivedEvent(false) {}
      
      bool receivedEvent;
      
      int onNewSubscription(SipEventSubscriptionHandle subscription, const NewPresenceSubscriptionEvent& args) { receivedEvent = true; return kSuccess; }
      int onSubscriptionEnded(SipEventSubscriptionHandle subscription, const PresenceSubscriptionEndedEvent& args) { return kSuccess; }
      int onIncomingPresenceStatus(SipEventSubscriptionHandle subscription, const IncomingPresenceStatusEvent& args) { return kSuccess; }
      int onSubscriptionStateChanged(SipEventSubscriptionHandle subscription, const PresenceSubscriptionStateChangedEvent& args) { return kSuccess; }
      int onPresenceReadyToSend(SipEventSubscriptionHandle subscription, const PresenceReadyToSendEvent& args) { return kSuccess; }
      int onError(SipEventSubscriptionHandle publication, const CPCAPI2::SipPresence::ErrorEvent& args) { return kSuccess; }
   };
   
   
   std::chrono::high_resolution_clock::time_point start, end;

   SipPresenceManager* presenceManager = SipPresenceManager::getInterface(phone);
   std::unique_ptr<MySipPresenceSubscriptionHandler> handler(new MySipPresenceSubscriptionHandler());

   {
      presenceManager->setHandler(accountHandle, handler.get());
      acct->enable(accountHandle);
      
      SipEventSubscriptionHandle subHandle = presenceManager->createSubscription(accountHandle);
      SipPresenceSubscriptionSettings subsSettings;
      subsSettings.expiresSeconds = 3600;
      presenceManager->applySubscriptionSettings(subHandle, subsSettings);
      presenceManager->addParticipant(subHandle, bob.config.uri());
      ASSERT_EQ(kSuccess, presenceManager->start(subHandle));
      
      // Overview of Bob's thread:
      //  -
      auto bobEvents = std::async(std::launch::async, [&] () {
         SipEventSubscriptionHandle bobSubs = 0;
         {
            SipEventSubscriptionHandle h;
            NewPresenceSubscriptionEvent evt;
            ASSERT_TRUE(bob.presenceEvents->expectEvent(
                                                "SipPresenceSubscriptionHandler::onNewSubscription",
                                                10000,
                                                AlwaysTruePred(),
                                                h, evt));
            bobSubs = h;
            ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
            ASSERT_EQ(evt.remoteAddress, config.uri());
            ASSERT_EQ(evt.remoteDisplayName, "alice");
         }
         
         {
            Presence evt1234;
            Person persBob;
            Note bobNote;
            bobNote.text = "heya wassap";
            persBob.notes.push_back(bobNote);
            evt1234.persons.push_back(persBob);
            evt1234.entity = bob.config.uri();
            ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
         }
      });
   
      start = std::chrono::high_resolution_clock::now();
      
      std::atomic_bool threadStopFlag(false);
      auto presenceEvent = std::async(std::launch::async, [&] ()
      {
         while (handler->receivedEvent == false)
         {
           phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);

           if (threadStopFlag) return;

           std::this_thread::sleep_for(std::chrono::milliseconds(100));
         }
      });
      
      flaggableWaitFor(presenceEvent, threadStopFlag);
      end = std::chrono::high_resolution_clock::now();
   }
   
   
   {
      SipEventSubscriptionHandle subHandle = presenceManager->createSubscription(accountHandle);
      SipPresenceSubscriptionSettings subsSettings;
      subsSettings.expiresSeconds = 3600;
      presenceManager->applySubscriptionSettings(subHandle, subsSettings);
      presenceManager->addParticipant(subHandle, bob.config.uri());
      ASSERT_EQ(kSuccess, presenceManager->start(subHandle));
      
      // Overview of Bob's thread:
      //  -
      auto bobEvents = std::async(std::launch::async, [&] () {
         SipEventSubscriptionHandle bobSubs = 0;
         {
            SipEventSubscriptionHandle h;
            NewPresenceSubscriptionEvent evt;
            ASSERT_TRUE(bob.presenceEvents->expectEvent(
                                                "SipPresenceSubscriptionHandler::onNewSubscription",
                                                5000,
                                                AlwaysTruePred(),
                                                h, evt));
            bobSubs = h;
            ASSERT_EQ(evt.subscriptionType, SipSubscriptionType_Incoming);
            ASSERT_EQ(evt.remoteAddress, config.uri());
            ASSERT_EQ(evt.remoteDisplayName, "alice");
         }
         
         {
            Presence evt1234;
            Person persBob;
            Note bobNote;
            bobNote.text = "heya wassap";
            persBob.notes.push_back(bobNote);
            evt1234.persons.push_back(persBob);
            evt1234.entity = bob.config.uri();
            ASSERT_EQ(bob.presence->accept(bobSubs, evt1234), kSuccess);
         }
      });
      
      
      presenceManager->setHandler(accountHandle, NULL);
      handler.reset();
      
      // wait about as long as it took before
      std::this_thread::sleep_for((end-start) * 2);
      
      phone->process(CPCAPI2::Phone::kBlockingModeNonBlocking);
   }
   
   Phone::release(phone);
}

#ifdef __APPLE__
// an attempt to reproduce OBELISK-5855 -- doesn't seem to reproduce
TEST_F(PresenceSubscriptionTests, MalformedRequestLine)
{
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   sippRunnerSettings.scenarioFileName = "PresenceSubscriptionTests.MalformedRequestLine.xml";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = CPCAPI2::SipAccount::SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   
   std::stringstream domain;
   domain << "127.0.0.1:" << sippRunnerSettings.sipListenPort;
   alice.config.settings.domain = domain.str().c_str();
   alice.init();
   alice.enable();
      
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.presence->addParticipant(aliceSubs, "sip:foo");
   alice.presence->start(aliceSubs);
  
   SipEventSubscriptionHandle hdl;
   {
      // alice sub started
      NewPresenceSubscriptionEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
   
   {
      // alice subscription activate
      PresenceSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   {
      // alice receives bobs empty presence
      IncomingPresenceStatusEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
   
   std::this_thread::sleep_for(std::chrono::seconds(5));
}
#endif // __APPLE__


#ifdef __APPLE__

// Created to help troubleshoot complaint from BT in https://alianza.zendesk.com/agent/tickets/288883
// BT is sending presence NOTIFY body with no namespace declarations for dm, rpid
TEST_F(PresenceSubscriptionTests, OnThePhone)
{
   SippRunnerSettings sippRunnerSettings;
   sippRunnerSettings.sipListenPort = 50010;
   sippRunnerSettings.mediaPort = 50020;
   sippRunnerSettings.timeoutSec = 120;
   // scenario file has namespace declarations for dm, rpid; remove these declarations
   // to see SDK fail to parse / test case to fail
   sippRunnerSettings.scenarioFileName = "PresenceSubscriptionTests.OnThePhone.xml";

   TestAccount alice("alice", Account_NoInit);
   alice.config.settings.sipTransportType = CPCAPI2::SipAccount::SipAccountTransport_UDP;
   alice.config.settings.useRegistrar = false;
   alice.config.settings.outboundProxy = "";
   
   std::stringstream domain;
   domain << "127.0.0.1:" << sippRunnerSettings.sipListenPort;
   alice.config.settings.domain = domain.str().c_str();
   alice.init();
   alice.enable();
      
   SippRunner sippRunner(sippRunnerSettings);
   ASSERT_EQ(kSuccess, sippRunner.start());

   SipEventSubscriptionHandle aliceSubs = alice.subs->createSubscription(alice.handle);
   SipPresenceSubscriptionSettings subsSettings;
   subsSettings.expiresSeconds = 3600;
   alice.presence->applySubscriptionSettings(aliceSubs, subsSettings);
   alice.presence->addParticipant(aliceSubs, "sip:foo");
   alice.presence->start(aliceSubs);
  
   SipEventSubscriptionHandle hdl;
   {
      // alice sub started
      NewPresenceSubscriptionEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onNewSubscription", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
   }
   
   {
      // alice subscription activate
      PresenceSubscriptionStateChangedEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onSubscriptionStateChanged", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
      ASSERT_EQ(evt.subscriptionState, SipSubscriptionState_Active);
   }
   {
      // alice receives bobs empty presence
      IncomingPresenceStatusEvent evt;
      ASSERT_TRUE(alice.presenceEvents->expectEvent("SipPresenceSubscriptionHandler::onIncomingPresenceStatus", 5000, HandleEqualsPred<SipEventSubscriptionHandle>(aliceSubs), hdl, evt));
      ASSERT_EQ(evt.status, CannedStatus_OnThePhone);
   }
   
   std::this_thread::sleep_for(std::chrono::seconds(5));
}
#endif // __APPLE__
