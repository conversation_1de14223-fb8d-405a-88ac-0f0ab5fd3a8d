#if _WIN32
#include "stdafx.h"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>
#include <util/FileUtils.h>
#include <string>

#include "test_framework/cpcapi2_test_framework.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace {

   class FileUtilsTest : public CpcapiAutoTest
   {
   public:
      FileUtilsTest() {}
      ~FileUtilsTest() {}
   };

   TEST_F(FileUtilsTest, DISABLED_FileUtilsBasicTests) {

      char data[] = "Test data to encrypt and decrypt. Test data to encrypt and decrypt. Test data to encrypt and decrypt. Test data to encrypt and decrypt. Test data to encrypt and decrypt.\n\
                    Test data to encrypt and decrypt.Test data to encrypt and decrypt.Test data to encrypt and decrypt.Test data to encrypt and decrypt.Test data to encrypt and decrypt.";

      std::string strdata(data); // for comparison

      std::string key("123456789");

      bool saved = FileUtils::SaveMemoryToFile("c:\\development\\samplefile.bin", (unsigned char *)data,  strdata.size(), true, key.c_str());
      ASSERT_TRUE(saved);

      int datasize = FileUtils::GetFileSize("c:\\development\\samplefile.bin");

      ASSERT_TRUE(datasize > 0);

      char *unencrypted = new char[datasize];

      std::string unencryptedstr;
      unsigned long outsz = 0;
      bool read = FileUtils::LoadTextFileToMemory("c:\\development\\samplefile.bin", unencrypted, true, key.c_str(), &outsz);
      ASSERT_TRUE(read);

      unencryptedstr = std::string(unencrypted, outsz);


      ASSERT_TRUE(strdata == unencryptedstr);

      delete[] unencrypted;
   }
}
