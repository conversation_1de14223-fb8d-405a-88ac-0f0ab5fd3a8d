
#include "test_events.h"
#include "impl/phone/EventQueue.h"

using namespace CPCAPI2;
using namespace CPCAPI2::test;

namespace
{
   class EventQueueTest : public CpcapiAutoTest
   {
   public:
      EventQueueTest() {}
      virtual ~EventQueueTest() {}
   };


   TEST_F(EventQueueTest, VerifyEventSourcePollerEmptyQueue)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerEmptyQueue(): verify poller behaviour with empty queue");
         std::vector<std::string> events;
         int queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, 0); // Blocking
            ASSERT_EQ(event.type(), "None");
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 4500) << " queue did not block";
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            poller->destroyEventQueue(queueId);
         });
         waitFor2Ms(pollerBlocking, pollerUnblocker, std::chrono::seconds(10000));

         queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerTimeout = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, 5000); // Timeout
            ASSERT_EQ(event.type(), "None");
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 4500) << " queue returned before timeout";
         });
         waitForMs(pollerTimeout, std::chrono::seconds(10000));

         auto pollerImmediate = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, -1); // Immediate
            ASSERT_EQ(event.type(), "None");
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            ASSERT_LE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 500) << " queue did not return immediately";
         });
         waitForMs(pollerImmediate, std::chrono::seconds(10000));

         auto pollerDestroy = std::async(std::launch::async, [&]()
         {
            poller->destroyEventQueue(queueId);
         });
         waitForMs(pollerDestroy, std::chrono::seconds(10000));
      }
   }

   TEST_F(EventQueueTest, VerifyEventSourcePollerInvalidQueueId)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerInvalidQueueId(): verify poller behaviour with invalid queue-id");
         std::vector<std::string> events;
         int queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            SdkEvent event = poller->getEvent(0, 0); // Blocking
            ASSERT_EQ(event.type(), "Phone.onEventQueueError");
            event = poller->getEvent(queueId, 0); // Blocking
            ASSERT_EQ(event.type(), "None");
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            poller->destroyEventQueue(queueId);
         });
         waitFor2Ms(pollerBlocking, pollerUnblocker, std::chrono::seconds(10000));
      }
   }

   TEST_F(EventQueueTest, VerifyEventSourcePollerUnsubscribedEvent)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerUnsubscribedEvent(): verify poller behaviour with unsubscribed event");
         std::vector<std::string> events;
         events.push_back("Phone.onError");
         int queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, 0); // Blocking

            // Should not receive the Phone.onError event, the None event is expected due to queue destruction
            ASSERT_EQ(event.type(), "None");
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 9000) << " queue exiting too early";
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            SdkEvent event("Phone.onLicensingError");
            poller->addEvent(std::move(event));
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            poller->destroyEventQueue(queueId);
         });
         waitFor2Ms(pollerBlocking, pollerUnblocker, std::chrono::seconds(20));
      }
   }

   TEST_F(EventQueueTest, VerifyEventSourcePollerSubscribedEvent)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerSubscribedEvent(): verify poller behaviour with subscribed event");
         std::vector<std::string> events;
         events.push_back("Phone.onError");
         int queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, 0); // Blocking

            // Should receive the Phone.onError event
            ASSERT_EQ(event.type(), "Phone.onError");
            auto end = std::chrono::system_clock::now();
            std::chrono::duration<double> diff = end - start;
            ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 4500) << " queue exiting too early";

            event = poller->getEvent(queueId, 0); // Blocking
            ASSERT_EQ(event.type(), "None");
            end = std::chrono::system_clock::now();
            diff = end - start;
            ASSERT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(diff).count(), 9500) << " queue exiting too early";
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            SdkEvent event("Phone.onError");
            poller->addEvent(std::move(event));
            std::this_thread::sleep_for(std::chrono::milliseconds(5000));
            poller->destroyEventQueue(queueId);
         });
         waitFor2Ms(pollerBlocking, pollerUnblocker, std::chrono::seconds(20));
      }
   }

   TEST_F(EventQueueTest, VerifyEventSourcePollerWildcardSubscribedEvent)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerSubscribedEvent(): verify poller behaviour with subscribed event");
         std::vector<std::string> events;
         events.push_back("Phone.*");
         int queueId = poller->createEventQueue(events);
         ASSERT_TRUE(queueId > 0);

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            auto start = std::chrono::system_clock::now();
            SdkEvent event = poller->getEvent(queueId, 0); // Blocking

            // Should receive the Phone.onError event but not Account.onAccountStatusChanged
            ASSERT_EQ(event.type(), "Phone.onError");

            event = poller->getEvent(queueId, 5000);
            ASSERT_EQ(event.type(), "None");
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            SdkEvent event("Account.onAccountStatusChanged");
            poller->addEvent(std::move(event));
            SdkEvent event2("Phone.onError");
            poller->addEvent(std::move(event2));
         });
         waitFor2Ms(pollerBlocking, pollerUnblocker, std::chrono::seconds(20));
         poller->destroyEventQueue(queueId);
      }
   }

   // test has threading issues resulting in it being flaky
   TEST_F(EventQueueTest, DISABLED_VerifyEventSourcePollerMultipleQueues)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerMultipleQueues(): verify poller behaviour with multiple queues");
         std::vector<std::string> events;
         events.push_back("Phone.onError");
         std::vector<int> queueIds;
         int threadCount = 5;
         std::atomic<int> waitForErrorThreads = 0;
         std::atomic<int> waitForNoneThreads = 0;
         std::mutex mMutex;
         std::condition_variable mCondition;

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            for (int i = 0; i < threadCount; ++i)
            {
               int queueId = poller->createEventQueue(events);
               ASSERT_TRUE(queueId > 0);
               queueIds.push_back(queueId);
            }

            for (int i = 0; i < threadCount; ++i)
            {
               std::async(std::launch::async, [&]()
               {
                  int queueId = queueIds[i];
                  SdkEvent event = poller->getEvent(queueId, 0); // Blocking
                  // Should receive the Phone.onError event
                  ASSERT_EQ(event.type(), "Phone.onError");
                  waitForErrorThreads++;
               });
            }

            auto start = std::chrono::system_clock::now();
            std::chrono::duration<double> diff;
            while ((waitForErrorThreads != threadCount) && (std::chrono::duration_cast<std::chrono::milliseconds>(diff).count() < 15000))
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
               auto end = std::chrono::system_clock::now();
               diff = end - start;
            }
            ASSERT_EQ(waitForErrorThreads, threadCount);
            mCondition.notify_all();

            for (int i = 0; i < threadCount; ++i)
            {
               std::async(std::launch::async, [&]()
               {
                  int queueId = queueIds[i];
                  SdkEvent event = poller->getEvent(queueId, 0); // Blocking
                  ASSERT_EQ(event.type(), "None");
                  waitForNoneThreads++;
               });
            }

            start = std::chrono::system_clock::now();
            while ((waitForNoneThreads != threadCount) && (std::chrono::duration_cast<std::chrono::milliseconds>(diff).count() < 15000))
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
               auto end = std::chrono::system_clock::now();
               diff = end - start;
            }
            ASSERT_EQ(waitForNoneThreads, threadCount);
            mCondition.notify_all();
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::unique_lock<std::mutex> lock(mMutex);
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (queueIds.size() == threadCount); });
            SdkEvent event("Phone.onError");
            poller->addEvent(std::move(event));
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitForNoneThreads == threadCount); });
         });
         auto destroyQueues = std::async(std::launch::async, [&]()
         {
            std::unique_lock<std::mutex> lock(mMutex);
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (queueIds.size() == threadCount); }); // All queues blocking
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitForNoneThreads == threadCount); }); // All queues handled event

            for (int i = 0; i < queueIds.size(); ++i)
            {
               poller->destroyEventQueue(queueIds[i]);
            }
         });
         waitFor3Ms(pollerBlocking, pollerUnblocker, destroyQueues, std::chrono::seconds(60));
      }
   }

   TEST_F(EventQueueTest, VerifyEventSourcePollerMultipleQueuesVariableEvents)
   {
      resip::MultiReactor* reactor = new resip::MultiReactor();
      reactor->start();
      std::shared_ptr<EventQueue> poller(EventQueue::create(*reactor));

      {
         safeCout("EventQueueTest::VerifyEventSourcePollerMultipleQueuesVariableEvents(): verify poller behaviour with multiple queues and variable events");
         std::vector<std::string> events1;
         std::vector<std::string> events2;
         std::vector<std::string> events3;
         events1.push_back("Phone.onError");
         events2.push_back("Phone.onLicensingError");
         events3.push_back("Phone.onLicensingError");
         events3.push_back("Phone.onLicensingSuccess");
         std::vector<int> queueIds;
         std::atomic<int> waitingThreads = 0;
         std::atomic<int> waitForNoneThreads = 0;
         int threadCount = 3;
         std::mutex mMutex;
         std::condition_variable mCondition;

         auto pollerBlocking = std::async(std::launch::async, [&]()
         {
            int queueId1 = poller->createEventQueue(events1);
            int queueId2 = poller->createEventQueue(events2);
            int queueId3 = poller->createEventQueue(events3);
            queueIds.push_back(queueId1);
            queueIds.push_back(queueId2);
            queueIds.push_back(queueId3);

            std::async(std::launch::async, [&]()
            {
               waitingThreads++;
               SdkEvent event = poller->getEvent(queueId1, 0); // Blocking
               ASSERT_EQ(event.type(), "Phone.onError");
               waitForNoneThreads++;
               event = poller->getEvent(queueId1, 0); // Blocking
               ASSERT_EQ(event.type(), "None");
            });
            std::async(std::launch::async, [&]()
            {
               waitingThreads++;
               SdkEvent event = poller->getEvent(queueId2, 0); // Blocking
               ASSERT_EQ(event.type(), "Phone.onLicensingError");
               waitForNoneThreads++;
               event = poller->getEvent(queueId2, 0); // Blocking
               ASSERT_EQ(event.type(), "None");
            });
            std::async(std::launch::async, [&]()
            {
               waitingThreads++;
               SdkEvent event = poller->getEvent(queueId3, 0); // Blocking
               ASSERT_EQ(event.type(), "Phone.onLicensingError");
               event = poller->getEvent(queueId3, 0); // Blocking
               ASSERT_EQ(event.type(), "Phone.onLicensingSuccess");
               waitForNoneThreads++;
               event = poller->getEvent(queueId3, 0); // Blocking
               ASSERT_EQ(event.type(), "None");
            });

            auto start = std::chrono::system_clock::now();
            std::chrono::duration<double> diff;
            while ((waitingThreads != threadCount) && (std::chrono::duration_cast<std::chrono::milliseconds>(diff).count() < 15000))
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
               auto end = std::chrono::system_clock::now();
               diff = end - start;
            }
            ASSERT_EQ(waitingThreads, threadCount);
            mCondition.notify_all();

            start = std::chrono::system_clock::now();
            while ((waitForNoneThreads != threadCount) && (std::chrono::duration_cast<std::chrono::milliseconds>(diff).count() < 15000))
            {
               std::this_thread::sleep_for(std::chrono::milliseconds(500));
               auto end = std::chrono::system_clock::now();
               diff = end - start;
            }
            ASSERT_EQ(waitForNoneThreads, threadCount);
            mCondition.notify_all();
         });
         auto pollerUnblocker = std::async(std::launch::async, [&]()
         {
            std::unique_lock<std::mutex> lock(mMutex);

            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitingThreads == threadCount); });
            std::this_thread::sleep_for(std::chrono::milliseconds(2000));
            SdkEvent event1("Phone.onError");
            SdkEvent event2("Phone.onLicensingError");
            SdkEvent event3("Phone.onLicensingSuccess");
            SdkEvent event4("Phone.Log");
            poller->addEvent(std::move(event1));
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            poller->addEvent(std::move(event2));
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            poller->addEvent(std::move(event3));
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
            poller->addEvent(std::move(event4));
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitForNoneThreads == threadCount); });
         });
         auto destroyQueues = std::async(std::launch::async, [&]()
         {
            std::unique_lock<std::mutex> lock(mMutex);
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitingThreads == threadCount); }); // All queues blocking
            mCondition.wait_for(lock, std::chrono::milliseconds(20000), [&]{ return (waitForNoneThreads == threadCount); }); // All queues handled event

            for (int i = 0; i < queueIds.size(); ++i)
            {
               poller->destroyEventQueue(queueIds[i]);
            }
         });
         waitFor3Ms(pollerBlocking, pollerUnblocker, destroyQueues, std::chrono::seconds(60));
      }

   }

   TEST_F(EventQueueTest, DISABLED_VerifyEventSourcePollerPatternMatch)
   {
      // TODO: verify * code pattern matching
   }

}