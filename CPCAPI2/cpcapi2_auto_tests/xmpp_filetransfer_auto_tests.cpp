#if _WIN32
#include "stdafx.h"
#else
#include "brand_branded.h"
#endif

#if (CPCAPI2_BRAND_XMPP_FILE_TRANSFER_MODULE == 1)

#if defined(__GNUC__) || defined(__clang__)
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
#endif

#include "cpcapi2_test_fixture.h"
#include <gtest/gtest.h>
#include <cpcapi2.h>

#include "test_framework/cpcapi2_test_framework.h"
#include "test_framework/xmpp_test_helper.h"
#include "xmpp/XmppAccountInterface.h"
#include "util/CurlPPSSL.h"

#ifndef WIN32
#include <sys/resource.h>
#endif

#include <thread>
#include <future>
#include <fstream>
#include <sstream>

using namespace CPCAPI2;
using namespace CPCAPI2::XmppFileTransfer;
using namespace CPCAPI2::test;

namespace {

class XmppFileTransferTest : public CpcapiAutoTest
{
public:
   XmppFileTransferTest() {}
   virtual ~XmppFileTransferTest() {}

   virtual void SetUp() OVERRIDE
   {
      start();

      cpc::string pathDelimeter = "/";
#ifdef _WIN32
      pathDelimeter = "\\";
#endif

      std::ofstream file((TestEnvironmentConfig::tempPath() + pathDelimeter + LargeFileName.c_str()).c_str(), std::ios::binary);
      file.seekp(LargeFileSize - 1);
      file.write("", 1);

      std::ofstream file2((TestEnvironmentConfig::tempPath() + pathDelimeter + XEP0363FileName.c_str()).c_str(), std::ios::binary);
      file2.seekp(XEP0363FileSize - 1);
      file2.write("", 1);
   }

   virtual void TearDown() OVERRIDE { end(); }

   const uintmax_t LargeFileSize = 100 * 1024 * 1024; // 100MB
   const cpc::string LargeFileName = "large.file";

   const uintmax_t XEP0363FileSize = ******** - 1; // less than ********B allowed by server
   const cpc::string XEP0363FileName = "xep0363.file";
};

TEST_F(XmppFileTransferTest, DISABLED_BasicFileSend) { // disabled, require another client as a receiver
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableLocalSocks5Proxy = false;
   alice.config.settings.enableRemoteStreamHostDiscovery = true;
   alice.enable();
   XmppTestAccount bob("bob", Account_NoInit);

   std::this_thread::sleep_for(std::chrono::seconds(5));

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            50000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               50000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_BasicFileReceive) { // disabled, require another client as a sender
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle aliceTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            50000, AlwaysTruePred(), h, evt ));
         aliceTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         //ASSERT_EQ(evt.remoteAddress, bob.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      //items[ 0 ].streamTypes = FileTransferStreamType_InBand;
      alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

      // The file transfer is then accepted
      alice.fileTransferManager->accept( aliceTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               50000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_ManualCancelSend) { // disabled, require another client as a receiver
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);

   std::this_thread::sleep_for(std::chrono::seconds(5));

   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = LargeFileName;
   itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, LargeFileName);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemProgress",
            20000, AlwaysTruePred(), aliceTransfer, evt));
      }

      {
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         20000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_ManualCancelReceive) { // disabled, require another client as a sender
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob", Account_NoInit);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle aliceTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         aliceTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, bob.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

      // The file transfer is then accepted
      alice.fileTransferManager->accept( aliceTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemProgress",
            30000, AlwaysTruePred(), aliceTransfer, evt));
      }

      {
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         20000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bobTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_Socks5_All) {
  XmppTestAccount alice("alice");
  XmppTestAccount bob("bob");

  // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
  XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
  XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

  // Setup the file transfer items
  XmppFileTransferItems items;
  XmppFileTransferItemDetail itemDetail;
  itemDetail.handle = aliceTransferItem;
  itemDetail.localfileName = "send.png";
  itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
  itemDetail.streamTypes = FileTransferStreamType_Socks5;
  items.push_back(itemDetail);

  alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
  alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
  alice.fileTransferManager->start(aliceTransfer);

  // Overview of Bob's thread:
  //  -
  auto bobEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferHandle bobTransfer = 0;
    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      bobTransfer = h;
      items = evt.fileItems;
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      ASSERT_EQ(evt.remoteAddress, alice.config.full());
    }

    // Bob then proceeds to ask the user what should be the filename and location
    // and whether or not they want to accept etc. This is then "configured".
    items[0].acceptedState = ftitem_accepted;
    items[0].localfileName = "recv.png";
    items[0].localfilePath = TestEnvironmentConfig::tempPath();
    items[0].streamTypes = FileTransferStreamType_Auto;
    bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

    // The file transfer is then accepted
    bob.fileTransferManager->accept(bobTransfer);

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.percent = 0;
      evt.fileTransferItem = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(bob.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), bobTransfer, evt));
      }
    }

    {
      // Wait for the transfer to finish (it will be disconnected from Alice's side)
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), bobTransfer, fte));
    }
  });

  // Overview of Alice's thread:
  //  -
  auto aliceEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      items = evt.fileItems;
      ASSERT_EQ(h, aliceTransfer);
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      ASSERT_EQ(evt.fileItems.size(), 1);
      ASSERT_EQ(items[0].localfileName, "send.png");
    }

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.fileTransferItem = aliceTransferItem;
      evt.percent = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(alice.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), aliceTransfer, evt));
      }
    }

    {
      // Wait for the transfer of the item to finish
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      // Wait for the transfer to finish
      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), aliceTransfer, fte));
    }
  });

  // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
  ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(bobEvents.get());
  ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(aliceEvents.get());

  // not needed, but handy sometimes when debugging ...
  //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_All_Socks5) {
  XmppTestAccount alice("alice");
  XmppTestAccount bob("bob");

  // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
  XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
  XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

  // Setup the file transfer items
  XmppFileTransferItems items;
  XmppFileTransferItemDetail itemDetail;
  itemDetail.handle = aliceTransferItem;
  itemDetail.localfileName = "send.png";
  itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
  itemDetail.streamTypes = FileTransferStreamType_Auto;
  items.push_back(itemDetail);

  alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
  alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
  alice.fileTransferManager->start(aliceTransfer);

  // Overview of Bob's thread:
  //  -
  auto bobEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferHandle bobTransfer = 0;
    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      bobTransfer = h;
      items = evt.fileItems;
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      ASSERT_EQ(evt.remoteAddress, alice.config.full());
    }

    // Bob then proceeds to ask the user what should be the filename and location
    // and whether or not they want to accept etc. This is then "configured".
    items[0].acceptedState = ftitem_accepted;
    items[0].localfileName = "recv.png";
    items[0].localfilePath = TestEnvironmentConfig::tempPath();
    items[0].streamTypes = FileTransferStreamType_Socks5;
    bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

    // The file transfer is then accepted
    bob.fileTransferManager->accept(bobTransfer);

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.percent = 0;
      evt.fileTransferItem = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(bob.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), bobTransfer, evt));
      }
    }

    {
      // Wait for the transfer to finish (it will be disconnected from Alice's side)
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), bobTransfer, fte));
    }
  });

  // Overview of Alice's thread:
  //  -
  auto aliceEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      items = evt.fileItems;
      ASSERT_EQ(h, aliceTransfer);
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      ASSERT_EQ(evt.fileItems.size(), 1);
      ASSERT_EQ(items[0].localfileName, "send.png");
    }

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.fileTransferItem = aliceTransferItem;
      evt.percent = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(alice.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), aliceTransfer, evt));
      }
    }

    {
      // Wait for the transfer of the item to finish
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      // Wait for the transfer to finish
      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), aliceTransfer, fte));
    }
  });

  // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
  ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(bobEvents.get());
  ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(aliceEvents.get());

  // not needed, but handy sometimes when debugging ...
  //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_InBand_All) {
  XmppTestAccount alice("alice");
  XmppTestAccount bob("bob");

  // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
  XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
  XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

  // Setup the file transfer items
  XmppFileTransferItems items;
  XmppFileTransferItemDetail itemDetail;
  itemDetail.handle = aliceTransferItem;
  itemDetail.localfileName = "send.png";
  itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
  itemDetail.streamTypes = FileTransferStreamType_InBand;
  items.push_back(itemDetail);

  alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
  alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
  alice.fileTransferManager->start(aliceTransfer);

  // Overview of Bob's thread:
  //  -
  auto bobEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferHandle bobTransfer = 0;
    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      bobTransfer = h;
      items = evt.fileItems;
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      ASSERT_EQ(evt.remoteAddress, alice.config.full());
    }

    // Bob then proceeds to ask the user what should be the filename and location
    // and whether or not they want to accept etc. This is then "configured".
    items[0].acceptedState = ftitem_accepted;
    items[0].localfileName = "recv.png";
    items[0].localfilePath = TestEnvironmentConfig::tempPath();
    items[0].streamTypes = FileTransferStreamType_Auto;
    bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

    // The file transfer is then accepted
    bob.fileTransferManager->accept(bobTransfer);

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.percent = 0;
      evt.fileTransferItem = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(bob.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), bobTransfer, evt));
      }
    }

    {
      // Wait for the transfer to finish (it will be disconnected from Alice's side)
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), bobTransfer, fte));
    }
  });

  // Overview of Alice's thread:
  //  -
  auto aliceEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      items = evt.fileItems;
      ASSERT_EQ(h, aliceTransfer);
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      ASSERT_EQ(evt.fileItems.size(), 1);
      ASSERT_EQ(items[0].localfileName, "send.png");
    }

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.fileTransferItem = aliceTransferItem;
      evt.percent = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(alice.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), aliceTransfer, evt));
      }
    }

    {
      // Wait for the transfer of the item to finish
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      // Wait for the transfer to finish
      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), aliceTransfer, fte));
    }
  });

  // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
  ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(bobEvents.get());
  ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(aliceEvents.get());

  // not needed, but handy sometimes when debugging ...
  //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_All_InBand) {
  XmppTestAccount alice("alice");
  XmppTestAccount bob("bob");

  // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
  XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
  XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

  // Setup the file transfer items
  XmppFileTransferItems items;
  XmppFileTransferItemDetail itemDetail;
  itemDetail.handle = aliceTransferItem;
  itemDetail.localfileName = "send.png";
  itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
  itemDetail.streamTypes = FileTransferStreamType_Auto;
  items.push_back(itemDetail);

  alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
  alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
  alice.fileTransferManager->start(aliceTransfer);

  // Overview of Bob's thread:
  //  -
  auto bobEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferHandle bobTransfer = 0;
    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      bobTransfer = h;
      items = evt.fileItems;
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      ASSERT_EQ(evt.remoteAddress, alice.config.full());
    }

    // Bob then proceeds to ask the user what should be the filename and location
    // and whether or not they want to accept etc. This is then "configured".
    items[0].acceptedState = ftitem_accepted;
    items[0].localfileName = "recv.png";
    items[0].localfilePath = TestEnvironmentConfig::tempPath();
    items[0].streamTypes = FileTransferStreamType_InBand;
    bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

    // The file transfer is then accepted
    bob.fileTransferManager->accept(bobTransfer);

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.percent = 0;
      evt.fileTransferItem = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(bob.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), bobTransfer, evt));
      }
    }

    {
      // Wait for the transfer to finish (it will be disconnected from Alice's side)
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), bobTransfer, fte));
    }
  });

  // Overview of Alice's thread:
  //  -
  auto aliceEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        30000, AlwaysTruePred(), h, evt));
      items = evt.fileItems;
      ASSERT_EQ(h, aliceTransfer);
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      ASSERT_EQ(evt.fileItems.size(), 1);
      ASSERT_EQ(items[0].localfileName, "send.png");
    }

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.fileTransferItem = aliceTransferItem;
      evt.percent = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(alice.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          30000, AlwaysTruePred(), aliceTransfer, evt));
      }
    }

    {
      // Wait for the transfer of the item to finish
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      // Wait for the transfer to finish
      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), aliceTransfer, fte));
    }
  });

  // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
  ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(bobEvents.get());
  ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(aliceEvents.get());

  // not needed, but handy sometimes when debugging ...
  //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_All_All_external_SOCKS5) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableLocalSocks5Proxy = false;
   alice.config.settings.enableRemoteStreamHostDiscovery = true;
   alice.config.settings.fileTransfileProxies.push_back("*******:1;jid=dummy");
   alice.enable();
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   itemDetail.streamTypes = FileTransferStreamType_Auto;
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            50000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      items[0].streamTypes = FileTransferStreamType_Auto;
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

      // The file transfer is then accepted
      bob.fileTransferManager->accept(bobTransfer);

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(bob.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               50000, AlwaysTruePred(), bobTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         //ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5); // bliu: TODO re-enable after jabber.diesel.counterpath.net supports SOCKS5
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            50000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               50000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         //ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5); // bliu: TODO re-enable after jabber.diesel.counterpath.net supports SOCKS5
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_All_All_InBand) {
  XmppTestAccount alice("alice", Account_NoInit);
  alice.config.settings.enableLocalSocks5Proxy = false;
  alice.config.settings.enableRemoteStreamHostDiscovery = false;
  alice.config.settings.fileTransfileProxies.push_back("*******:1;jid=dummy");
  alice.enable();
  XmppTestAccount bob("bob");

  // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
  XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
  XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

  // Setup the file transfer items
  XmppFileTransferItems items;
  XmppFileTransferItemDetail itemDetail;
  itemDetail.handle = aliceTransferItem;
  itemDetail.localfileName = "send.png";
  itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
  itemDetail.streamTypes = FileTransferStreamType_Auto;
  items.push_back(itemDetail);

  alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
  alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
  alice.fileTransferManager->start(aliceTransfer);

  // Overview of Bob's thread:
  //  -
  auto bobEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferHandle bobTransfer = 0;
    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        50000, AlwaysTruePred(), h, evt));
      bobTransfer = h;
      items = evt.fileItems;
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
      ASSERT_EQ(evt.remoteAddress, alice.config.full());
    }

    // Bob then proceeds to ask the user what should be the filename and location
    // and whether or not they want to accept etc. This is then "configured".
    items[0].acceptedState = ftitem_accepted;
    items[0].localfileName = "recv.png";
    items[0].localfilePath = TestEnvironmentConfig::tempPath();
    items[0].streamTypes = FileTransferStreamType_Auto;
    bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

    // The file transfer is then accepted
    bob.fileTransferManager->accept(bobTransfer);

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.percent = 0;
      evt.fileTransferItem = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(bob.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          50000, AlwaysTruePred(), bobTransfer, evt));
      }
    }

    {
      // Wait for the transfer to finish (it will be disconnected from Alice's side)
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5 | FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), bobTransfer, fte));
    }
  });

  // Overview of Alice's thread:
  //  -
  auto aliceEvents = std::async(std::launch::async, [&]() {

    XmppFileTransferItems items;
    {
      XmppFileTransferHandle h;
      NewFileTransferEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onNewFileTransfer",
        50000, AlwaysTruePred(), h, evt));
      items = evt.fileItems;
      ASSERT_EQ(h, aliceTransfer);
      ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      ASSERT_EQ(evt.fileItems.size(), 1);
      ASSERT_EQ(items[0].localfileName, "send.png");
    }

    {
      // Watch the progress
      FileTransferItemProgressEvent evt;
      evt.fileTransferItem = aliceTransferItem;
      evt.percent = 0;

      while (evt.percent < 100)
      {
        ASSERT_TRUE(cpcExpectEvent(alice.events,
          "XmppFileTransferHandler::onFileTransferItemProgress",
          50000, AlwaysTruePred(), aliceTransfer, evt));
      }
    }

    {
      // Wait for the transfer of the item to finish
      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferItemEnded",
        30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5 | FileTransferStreamType_InBand);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

      // Wait for the transfer to finish
      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
        "XmppFileTransferHandler::onFileTransferEnded",
        30000, AlwaysTruePred(), aliceTransfer, fte));
    }
  });

  // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
  ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(bobEvents.get());
  ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
  ASSERT_NO_THROW(aliceEvents.get());

  // not needed, but handy sometimes when debugging ...
  //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, BasicFileTransfer_Socks5_All_NoFallback) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.enableLocalSocks5Proxy = false;
   alice.config.settings.enableRemoteStreamHostDiscovery = false;
   alice.enable();
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   itemDetail.streamTypes = FileTransferStreamType_Socks5;
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      items[0].streamTypes = FileTransferStreamType_Auto;
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

      // The file transfer is then accepted
      bob.fileTransferManager->accept(bobTransfer);

      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), bobTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), bobTransfer, fte));
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      FileTransferItemEndedEvent ftie;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), aliceTransfer, ftie));
      ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
      ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), aliceTransfer, fte));
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// Test works against local XMPP servers but will have problems running against NewPace etc
// servers due to timing and/or message delivery order differences.
TEST_F(XmppFileTransferTest, SendTwoFilesOneINVITE) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail, itemDetail2;
   itemDetail.handle         = aliceTransferItem;
   itemDetail.localfileName  = "send.png";
   itemDetail.localfilePath  = TestEnvironmentConfig::testResourcePath();
   itemDetail2.handle        = aliceTransferItem2;
   itemDetail2.localfileName = "send2.png";
   itemDetail2.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );
   items.push_back( itemDetail2 );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to be finished (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Should be two of these events (one per file)
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 2);
         ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
         ASSERT_EQ( items[ 1 ].localfileName, "send2.png" );
      }

      {
         // Wait for the transfer of the items to finish (two of them)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// Test works against local XMPP servers but will have problems running against NewPace etc
// servers due to timing and/or message delivery order differences.
TEST_F(XmppFileTransferTest, SendTwoFilesTwoINVITEs) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferHandle aliceTransfer2 = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle         = aliceTransferItem;
   itemDetail.localfileName  = "send.png";
   itemDetail.localfilePath  = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );

   items.clear();
   itemDetail.handle         = aliceTransferItem2;
   itemDetail.localfileName  = "send2.png";
   itemDetail.localfilePath  = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer2, items );
   alice.fileTransferManager->addParticipant( aliceTransfer2, bob.config.full() );

   // Make 2 calls
   alice.fileTransferManager->start( aliceTransfer );
   std::this_thread::sleep_for(std::chrono::milliseconds(30000)); // to help the timing
   alice.fileTransferManager->start( aliceTransfer2 );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      XmppFileTransferHandle bobTransfer2 = 0;
      XmppFileTransferItems items2;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer2 = h;
         items2 = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items2[ 0 ].acceptedState = ftitem_accepted;
      items2[ 0 ].localfileName = "recv2.png";
      items2[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();

      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      std::this_thread::sleep_for(std::chrono::milliseconds(500)); // to help the timing
      bob.fileTransferManager->configureFileTransferItems( bobTransfer2, items2 );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );
      std::this_thread::sleep_for(std::chrono::milliseconds(500)); // to help the timing
      bob.fileTransferManager->accept( bobTransfer2 );

      {
         // Wait for the transfer to be finished (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte ));

         // Should be two of these events (one per file)
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), bobTransfer2, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer2, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         items = evt.fileItems;
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      }

      XmppFileTransferItems items2;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items2 = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer2);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items2[ 0 ].localfileName, "send2.png" );
      }

      {
         // Wait for the transfer of the items to finish (two of them)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer2, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer2, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(100000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(100000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// test case for performing paralell file transfers at the same time
TEST_F(XmppFileTransferTest, MultiFileTransfer) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");
   XmppTestAccount alice2(alice, "alice2");
   XmppTestAccount bob2(bob, "bob2");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferHandle alice2Transfer = alice2.fileTransferManager->createFileTransfer(alice2.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle alice2TransferItem = alice2.fileTransferManager->createFileTransferItem( alice2.handle );

   // configure the items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

   items.clear();
   itemDetail.handle        = alice2TransferItem;
   itemDetail.localfileName = "send2.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );
   alice2.fileTransferManager->configureFileTransferItems( alice2Transfer, items );

   // start the file transfers
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice2.fileTransferManager->addParticipant( alice2Transfer, bob2.config.full() );
   alice.fileTransferManager->start( aliceTransfer );
   alice2.fileTransferManager->start( alice2Transfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(items.size(), 1);
      }

      // Bob reconfigures the filename and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bobTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Bob2's thread:
   //  -
   auto bob2Events = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bob2Transfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob2.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bob2Transfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice2.config.full());
      }

      // Bob reconfigures the filename and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv2.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob2.fileTransferManager->configureFileTransferItems( bob2Transfer, items );
      bob2.fileTransferManager->accept( bob2Transfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob2.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bob2Transfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob2.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bob2Transfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob2.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bob2Transfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // Overview of Alice2's thread:
   //  -
   auto alice2Events = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice2.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, alice2Transfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = alice2TransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice2.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), alice2Transfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice2.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), alice2Transfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice2.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), alice2Transfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(bob2Events.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bob2Events.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());
   ASSERT_EQ(alice2Events.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(alice2Events.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_MultiFileTransfer_Cancel) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   const int numItems = 10;

   auto aliceEvents = std::async(std::launch::async, [&]() {
      // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
      XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);

      XmppFileTransferItems items;
      for (int i = 0; i < numItems; ++i)
      {
         XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

         // configure the items
         XmppFileTransferItemDetail itemDetail;
         itemDetail.handle = aliceTransferItem;
         itemDetail.localfileName = LargeFileName;
         itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
         items.push_back(itemDetail);
      }

      alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);

      // start the file transfers
      alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
      alice.fileTransferManager->start(aliceTransfer);

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), numItems);
      }

      // eat all progress events
      while (true)
      {
         FileTransferItemProgressEvent evt;
         if (!cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemProgress",
            1000, AlwaysTruePred(), aliceTransfer, evt)) break;
      }

      // Wait for the transfer to finish
      for (int i = 0; i < numItems; ++i)
      {
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            50000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);
      }

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte));
   });

   auto bobEvents = std::async(std::launch::async, [&]() {
      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(items.size(), numItems);
      }

      for (int i = 0; i < numItems; ++i)
      {
         // Bob reconfigures the filename and accepts
         items[i].acceptedState = ftitem_accepted;
         std::stringstream ss;
         ss << "recv" << i << ".png";
         items[i].localfileName = ss.str().c_str();
         items[i].localfilePath = TestEnvironmentConfig::testResourcePath();
      }

      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);
      bob.fileTransferManager->accept(bobTransfer);

      std::this_thread::sleep_for(std::chrono::milliseconds(1000));

      bob.fileTransferManager->end(bobTransfer);

      // eat all progress events
      while (true)
      {
         FileTransferItemProgressEvent evt;
         if (!cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemProgress",
            1000, AlwaysTruePred(), bobTransfer, evt)) break;
      }

      for (int i = 0; i < numItems; ++i)
      {
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            50000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);
      }

      FileTransferEndedEvent fte;
      ASSERT_TRUE(cpcExpectEvent(bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte));
   });

   waitFor2(aliceEvents, bobEvents);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// test case for multiple sequential file transfers
TEST_F(XmppFileTransferTest, MultiSequentialFileTransfer) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // configure the items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bobTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items[ 0 ].localfileName, "send.png" );
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // NOW: do it all over again using the same alice and bob, and a different file.

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   items.clear();
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send2.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back( itemDetail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv2.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( bob.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bobTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   aliceEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ( items[ 0 ].localfileName, "send2.png" );
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while( evt.percent < 100 )
         {
            ASSERT_TRUE(cpcExpectEvent( alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, CancelFileTransferItemBySender) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );
   detail.handle        = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv1.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Cancel the file transfer
         alice.fileTransferManager->cancelItem( aliceTransfer, aliceTransferItem1 );
         alice.fileTransferManager->cancelItem( aliceTransfer, aliceTransferItem2 );

         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, NetworkLossFileTransferItemBySender) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );
   detail.handle        = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );


   const bool usingDocker = TestEnvironmentConfig::dockerContainerized();

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv1.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      bob.disconnectNetwork(false);

      for (;;)
      {
         XmppAccount::XmppAccountHandle h;
         XmppAccount::XmppAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 8000, CPCAPI2::test::AlwaysTruePred(), h, evt));

         if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Failure || evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Disconnected)
         {
            break;
         }
      }

      bob.connectNetwork(true);

      // bob may have disabled this container's network -- there will be
      // a bunch of account status changed events; just wait for the connected event
      for (;;)
      {
         XmppAccount::XmppAccountHandle h;
         XmppAccount::XmppAccountStatusChangedEvent evt;
         ASSERT_TRUE(bob.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 8000, CPCAPI2::test::AlwaysTruePred(), h, evt));

         if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected)
         {
            break;
         }
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // simulate loss of network
         alice.disconnectNetwork(usingDocker);

         
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_TRUE(ftie.endReason == FileTransferItemEndReason_LocalCancel || ftie.endReason == FileTransferItemEndReason_BadConnection);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_TRUE(ftie.endReason == FileTransferItemEndReason_LocalCancel || ftie.endReason == FileTransferItemEndReason_BadConnection);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
         
         std::this_thread::sleep_for(std::chrono::seconds(2));
         
         // simulate regain of network
         alice.connectNetwork(usingDocker);
      
         // might get failed or disconnected events, so just wait for account status to go back to connected
         for (;;)
         {
            XmppAccount::XmppAccountHandle h;
            XmppAccount::XmppAccountStatusChangedEvent evt;
            ASSERT_TRUE(alice.events->expectEvent(__LINE__, "XmppAccountHandler::onAccountStatusChanged", 15000, CPCAPI2::test::AlwaysTruePred(), h, evt));

            if (evt.accountStatus == CPCAPI2::XmppAccount::XmppAccountStatusChangedEvent::Status_Connected)
            {
               break;
            }
         }
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, CancelFileTransferItemByReceiver) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );
   detail.handle        = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv1.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      //{
      //   XmppFileTransferHandle h;
      //   FileTransferItemProgressEvent evt;
      //   cpcWaitForEvent(bob.events, "XmppFileTransferHandler::onFileTransferItemProgress", 1000, AlwaysTruePred(), h, evt);
      //}

      bob.fileTransferManager->cancelItem( bobTransfer, items[ 0 ].handle );
      bob.fileTransferManager->cancelItem( bobTransfer, items[ 1 ].handle );

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, CancelFileTransferBySender) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );
   detail.handle        = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv1.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Cancel the file transfer
         alice.fileTransferManager->end( aliceTransfer );

         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, CancelFileTransferByReceiver) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem( alice.handle );
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem( alice.handle );

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle        = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );
   detail.handle        = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( detail );

   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv1.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      items[ 1 ].acceptedState = ftitem_accepted;
      items[ 1 ].localfileName = "recv2.png";
      items[ 1 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );
      bob.fileTransferManager->accept( bobTransfer );

      std::this_thread::sleep_for(std::chrono::seconds(3));

      bob.fileTransferManager->end( bobTransfer );

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         ASSERT_TRUE(cpcWaitForEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, RejectFileTransfer) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle );

   // configure the items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle        = aliceTransferItem;
   itemDetail.localfileName = LargeFileName;
   itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back( itemDetail );
   alice.fileTransferManager->configureFileTransferItems( aliceTransfer, items );

   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      // Instead of configuring the items and accepting, bob just rejects.
      bob.fileTransferManager->reject( bobTransfer );

      {
         // Wait for the item to be cancelled
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ( ftie.endReason, FileTransferItemEndReason_LocalCancel );
      }

      {
         // Wait for the transfer to be finished
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the item to be cancelled
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), aliceTransfer, ftie ));
         ASSERT_EQ( ftie.endReason, FileTransferItemEndReason_RemoteCancel );
      }

      {
         // Wait for the transfer to finish (bob rejects)
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), aliceTransfer, fte ));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, RejectFileTransferItemButAcceptFileTransferByReceiver) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(detail);
   detail.handle = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[0].acceptedState = ftitem_rejected;
      items[0].localfileName = "recv1.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      items[1].acceptedState = ftitem_rejected;
      items[1].localfileName = "recv2.png";
      items[1].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);
      bob.fileTransferManager->accept(bobTransfer);

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, AcceptFileTransferItemButRejectFileTransferByReceiver) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(detail);
   detail.handle = aliceTransferItem2;
   detail.localfileName = LargeFileName;
   detail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      // Bob configures the items and accepts
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv1.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      items[1].acceptedState = ftitem_accepted;
      items[1].localfileName = "recv2.png";
      items[1].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);
      bob.fileTransferManager->reject(bobTransfer, 0);

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, FileTransferNoFiles) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   //XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem( alice.handle, aliceTransfer, ".", "send.png" );
   alice.fileTransferManager->addParticipant( aliceTransfer, bob.config.full() );
   alice.fileTransferManager->start( aliceTransfer );

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&] () {

      {
         XmppFileTransferHandle h;
         XmppFileTransfer::ErrorEvent evt;
         ASSERT_TRUE(cpcExpectEvent( alice.events,
            "XmppFileTransferHandler::onError",
            30000, AlwaysTruePred(), h, evt ));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.errorText, "Cannot start file transfer. No files have been added");
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

#ifdef _WIN32
TEST_F(XmppFileTransferTest, SendingFileInUse) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   std::string sendFile = TestEnvironmentConfig::testResourcePath() + "send.png";

   FILE* f = _fsopen(sendFile.c_str(), "r", _SH_DENYRD);
   ASSERT_TRUE(f != NULL);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
      XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

      // Setup the file transfer items
      XmppFileTransferItems items;
      XmppFileTransferItemDetail itemDetail;
      itemDetail.handle = aliceTransferItem;
      itemDetail.localfileName = "send.png";
      itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
      items.push_back(itemDetail);

      alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
      alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
      alice.fileTransferManager->start(aliceTransfer);

      {
         XmppFileTransferItems items;
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadFile);
         safeCout("alice item ended correctly");

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
         safeCout("alice transfer ended correctly");
      }
   });

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&] () {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt ));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[ 0 ].acceptedState = ftitem_accepted;
      items[ 0 ].localfileName = "recv.png";
      items[ 0 ].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems( bobTransfer, items );

      // The file transfer is then accepted
      bob.fileTransferManager->accept( bobTransfer );

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferItemEnded",
         30000, AlwaysTruePred(), bobTransfer, ftie ));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);
         safeCout("bob item ended correctly");

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent( bob.events,
         "XmppFileTransferHandler::onFileTransferEnded",
         30000, AlwaysTruePred(), bobTransfer, fte ));
         safeCout("bob transfer ended correctly");
      }
   });

   waitFor2(aliceEvents, bobEvents);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));

   fclose(f);

   safeCout("before termination");
}
#endif // _WIN32

#ifdef _WIN32
TEST_F(XmppFileTransferTest, ReceivingFileInUse) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   FILE* f = _fsopen("recv.png", "a", _SH_DENYRW);
   ASSERT_TRUE(f != NULL);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
      XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
      XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

      // Setup the file transfer items
      XmppFileTransferItems items;
      XmppFileTransferItemDetail itemDetail;
      itemDetail.handle = aliceTransferItem;
      itemDetail.localfileName = "send.png";
      itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
      items.push_back(itemDetail);

      alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
      alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
      alice.fileTransferManager->start(aliceTransfer);

      {
         XmppFileTransferItems items;
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         //ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel); // BadConnection??
         safeCout("alice item ended correctly");

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
         safeCout("alice transfer ended correctly");
      }
   });

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

      // The file transfer is then accepted
      bob.fileTransferManager->accept(bobTransfer);

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadFile);
         safeCout("bob item ended correctly");

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
         safeCout("bob transfer ended correctly");
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   waitFor2(aliceEvents, bobEvents);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));

   fclose(f);

   safeCout("before termination"); 
}
#endif // WIN32

TEST_F(XmppFileTransferTest, InvalidSendingFile) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   FileTransferItemEndedEvent ftie;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), aliceTransfer, ftie));
   ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadFile);

   FileTransferEndedEvent fte;
   ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), aliceTransfer, fte));
   ASSERT_EQ(fte.endReason, FileTransferEndReason_Unknown);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_Broadcast) { // need online resource
   XmppTestAccount alice("alice");
   XmppTestAccount bob("", Account_NoInit);
   XmppTestAccount bob1(bob, "bob1");
   XmppTestAccount bob2(bob, "bob2");

   // Overview of Bob1's thread:
   //  -
   auto bob1Events = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bob1Transfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob1.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bob1Transfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      bob1.fileTransferManager->configureFileTransferItems(bob1Transfer, items);

      std::this_thread::sleep_for(std::chrono::seconds(2));

      // The file transfer is then accepted
      bob1.fileTransferManager->accept(bob1Transfer);

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(bob1.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bob1Transfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_RemoteCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob1.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bob1Transfer, fte));
      }
   });

   // Overview of Bob2's thread:
   //  -
   auto bob2Events = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bob2Transfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob2.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bob2Transfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv.png";
      items[0].localfilePath = TestEnvironmentConfig::tempPath();
      bob2.fileTransferManager->configureFileTransferItems(bob2Transfer, items);

      // The file transfer is then accepted
      bob2.fileTransferManager->accept(bob2Transfer);

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.percent = 0;
         evt.fileTransferItem = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(bob2.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), bob2Transfer, evt));
         }
      }

      {
         // Wait for the transfer to finish (it will be disconnected from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(bob2.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bob2Transfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob2.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bob2Transfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
      XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
      XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

      {
         // Setup the file transfer items
         XmppFileTransferItems items;
         XmppFileTransferItemDetail itemDetail;
         itemDetail.handle = aliceTransferItem;
         itemDetail.localfileName = "send.png";
         itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
         items.push_back(itemDetail);

         alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
         alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
         alice.fileTransferManager->start(aliceTransfer);
      }

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   waitFor3(aliceEvents, bob1Events, bob2Events);

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// jabber.diesel doesn't publish offline presence after logoff
TEST_F(XmppFileTransferTest, DISABLED_CancelFileTransferBySenderBeforeAcceptAndAfterUnregistering) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 1);
      }

      std::this_thread::sleep_for(std::chrono::seconds(10));

      // Bob configures the items and accepts
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv1.png";
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);
      bob.fileTransferManager->accept(bobTransfer);

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      alice.disable();
      alice.enable();

      {
         // Cancel the file transfer
         alice.fileTransferManager->cancelItem(aliceTransfer, aliceTransferItem1);

         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }

      // Cancel the file transfer
      alice.fileTransferManager->cancelItem(aliceTransfer, aliceTransferItem1);
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, CancelFileTransferItemTwiceBySender) {
   XmppTestAccount alice("alice");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);

   alice.fileTransferManager->cancelItem(aliceTransfer, aliceTransferItem1);
   alice.fileTransferManager->cancelItem(aliceTransfer, aliceTransferItem1);

   // not needed, but handy sometimes when debugging ...
   std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// jabber.diesel doesn't publish offline presence after logoff
TEST_F(XmppFileTransferTest, DISABLED_CancelFileTransferByReceiverBeforeAcceptAndAfterUnregistering) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);
   detail.handle = aliceTransferItem2;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      bob.disable();
      //bob.enable();

#if 0
      // Bob configures the items and accepts
      items[0].acceptedState = ftitem_accepted;
      items[0].localfileName = "recv1.png";
      items[1].acceptedState = ftitem_accepted;
      items[1].localfileName = "recv2.png";
      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);
      bob.fileTransferManager->accept(bobTransfer);
#else
      //bob.fileTransferManager->cancelItem(bobTransfer, items[0].handle);
      //bob.fileTransferManager->cancelItem(bobTransfer, items[1].handle);
#endif

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            100000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// Test works against local XMPP servers but will have problems running against NewPace etc
// servers due to timing and/or message delivery order differences.
TEST_F(XmppFileTransferTest, SendManyFilesInOneTransfer) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;

   for (int i = 0; i < 60; ++i)
   {
      XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

      XmppFileTransferItemDetail itemDetail;
      itemDetail.handle = aliceTransferItem;
      itemDetail.localfileName = "send.png";
      itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
      items.push_back(itemDetail);
   }

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), items.size());
      }

      // Bob then proceeds to ask the user what should be the filename and location
      // and whether or not they want to accept etc. This is then "configured".

      for (size_t i = 0; i < items.size(); ++i)
      {
         items[i].acceptedState = ftitem_accepted;
         std::stringstream ss;
         ss << i << ".png";
         items[i].localfileName = ss.str().c_str();
         items[i].localfilePath = TestEnvironmentConfig::tempPath();
      }

      bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

      // The file transfer is then accepted
      bob.fileTransferManager->accept(bobTransfer);

      {
         // Wait for the transfer to be finished (from Alice's side)
         for (size_t i = 0; i < items.size(); ++i)
         {
            FileTransferItemEndedEvent ftie;
            ASSERT_TRUE(cpcWaitForEvent(bob.events,
               "XmppFileTransferHandler::onFileTransferItemEnded",
               600000, AlwaysTruePred(), bobTransfer, ftie));
            ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);
         }

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            120000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), items.size());
      }

      {
         // Wait for the transfer of the items to finish (two of them)
         for (size_t i = 0; i < items.size(); ++i)
         {
            FileTransferItemEndedEvent ftie;
            ASSERT_TRUE(cpcWaitForEvent(alice.events,
               "XmppFileTransferHandler::onFileTransferItemEnded",
               600000, AlwaysTruePred(), aliceTransfer, ftie));
            ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);
         }

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            120000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(1000000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(1000000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// jabber.diesel doesn't publish offline presence after logoff
TEST_F(XmppFileTransferTest, DISABLED_ReceiverUnregister) {
   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   XmppFileTransferItems items;
   XmppFileTransferItemDetail detail;
   detail.handle = aliceTransferItem1;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);
   detail.handle = aliceTransferItem2;
   detail.localfileName = "send.png";
   detail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(detail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Bob's thread:
   //  -
   auto bobEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferHandle bobTransfer = 0;
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         bobTransfer = h;
         items = evt.fileItems;
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
         ASSERT_EQ(evt.remoteAddress, alice.config.full());
         ASSERT_EQ(evt.fileItems.size(), 2);
      }

      bob.disable();

      {
         // Wait for the transfer to be canceled (from Alice's side)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         ASSERT_TRUE(cpcWaitForEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), bobTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(bob.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), bobTransfer, fte));
      }
   });

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            100000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(bobEvents.get());
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, XEP0363FileTransfer) {
   XmppTestAccount alice("alice");

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(alice.events,
               "XmppFileTransferHandler::onFileTransferItemProgress",
               30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

// OBELISK-6155: test is failing
TEST_F(XmppFileTransferTest, DISABLED_XEP0363FileTransfer_Cancel) {
   XmppTestAccount alice("alice");

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem1;
   itemDetail.localfileName = LargeFileName;
   itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(itemDetail);
   itemDetail.handle = aliceTransferItem2;
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 2);
         ASSERT_EQ(items[0].localfileName, LargeFileName);
      }

      std::this_thread::sleep_for(std::chrono::seconds(1));
      alice.fileTransferManager->end(aliceTransfer);

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         // Wait for the transfer of the item to finish
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_LocalCancel);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, XEP0363FileTransfer_OBELISK_5930_InteruptAtConnectingStage) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.httpFileUploadTransferRate = 10 * 1024;
   alice.enable();

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem1;
   itemDetail.localfileName = XEP0363FileName;
   itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, XEP0363FileName);
      }

      alice.disconnectNetwork(false);
      assertXmppDisconnecting(alice);
      assertXmppDisconnected(alice);

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            120000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_TRUE(ftie.endReason == FileTransferItemEndReason_LocalCancel || ftie.endReason == FileTransferItemEndReason_BadConnection); // TODO bliu: be exact

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, XEP0363FileTransfer_OBELISK_5930_InteruptAtTransferringStage) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.httpFileUploadTransferRate = 10 * 1024;
   alice.enable();

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem1;
   itemDetail.localfileName = XEP0363FileName;
   itemDetail.localfilePath = TestEnvironmentConfig::tempPath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, XEP0363FileName);
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;

         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemProgress", 30000, AlwaysTruePred(), aliceTransfer, evt));
      }

      alice.disconnectNetwork(false);
      assertXmppDisconnecting(alice);
      assertXmppDisconnected(alice);

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            120000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_TRUE(ftie.endReason == FileTransferItemEndReason_LocalCancel || ftie.endReason == FileTransferItemEndReason_BadConnection); // TODO bliu: be exact

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(600000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, XEP0363FileTransfer_SendMultipleFilesOneINVITE) {
   XmppTestAccount alice("alice");

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem1 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem2 = alice.fileTransferManager->createFileTransferItem(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem3 = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail1, itemDetail2, itemDetail3;
   itemDetail1.handle = aliceTransferItem1;
   itemDetail1.localfileName = "send.png";
   itemDetail1.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail1);
   itemDetail2.handle = aliceTransferItem2;
   itemDetail2.localfileName = "send.png";
   itemDetail2.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail2);
   itemDetail3.handle = aliceTransferItem3;
   itemDetail3.localfileName = "send.png";
   itemDetail3.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail3);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 3);
         ASSERT_EQ(items[0].localfileName, "send.png");
         ASSERT_EQ(items[1].localfileName, "send.png");
         ASSERT_EQ(items[2].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the items to finish (three of them)
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         ASSERT_TRUE(cpcWaitForEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            600000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

#ifndef ANDROID
TEST_F(XmppFileTransferTest, XEP0363FileTransfer_FWS144_AcceptSelfSignedCertificate) {
   XmppTestAccount alice("alice");

   // use OS specific certificate storage, and accept self signed certificate
   // override XmppTestAccount default E_CERT_WHATEVER_ERROR with E_CERT_NOT_TRUSTED
   // override XmppTestAccount default CERT_FILESYSTEM_STORAGE with CERT_OS_SPECIFIC_STORAGE
   XmppAccount::XmppAccountSettingsInternal settings;
   settings.httpFileUploadCertVerification = XmppAccount::XmppAccountSettingsInternal::CertVerification_Verify;
   settings.acceptableFailures = CurlPPSSL::E_CERT_NOT_TRUSTED;
   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->setAccountSettingsInternal(alice.handle, settings);

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}
#endif

TEST_F(XmppFileTransferTest, XEP0363FileTransfer_FWS144_InvalidCertificate) {
   XmppTestAccount alice("alice");

   XmppAccount::XmppAccountSettingsInternal settings;
   settings.httpFileUploadCertVerification = XmppAccount::XmppAccountSettingsInternal::CertVerification_Verify;
   settings.acceptableFailures = 0; // override XmppTestAccount default E_CERT_WHATEVER_ERROR
   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->setAccountSettingsInternal(alice.handle, settings);

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_BadConnection);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

TEST_F(XmppFileTransferTest, DISABLED_XEP0363FileTransfer_FWS144_QA_Server) {
   XmppTestAccount alice("alice", Account_NoInit);
   alice.config.settings.domain = "frontline.mobilevoiplive.com";
   alice.config.settings.proxy = "qa-frontline.mobilevoiplive.com";
   alice.config.settings.port = 5222;
   alice.config.settings.username = "flw04";
   alice.config.settings.password = "hcq5r3xaFvrlqX1WIxLwUiui7V7ptL44";
   alice.config.settings.ignoreCertVerification = false;
   alice.enable();

   // override XmppTestAccount default E_CERT_WHATEVER_ERROR with 0
   // override XmppTestAccount default CERT_FILESYSTEM_STORAGE with CERT_OS_SPECIFIC_STORAGE
   XmppAccount::XmppAccountSettingsInternal settings;
   settings.httpFileUploadCertVerification = XmppAccount::XmppAccountSettingsInternal::CertVerification_Verify;
   XmppAccount::XmppAccountManagerInternal::getInternalInterface(alice.phone)->setAccountSettingsInternal(alice.handle, settings);

   cpc::string service;

   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   // Overview of Alice's thread:
   //  -
   auto aliceEvents = std::async(std::launch::async, [&]() {

      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onNewFileTransfer",
            30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferItemEnded",
            30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events,
            "XmppFileTransferHandler::onFileTransferEnded",
            30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   // not needed, but handy sometimes when debugging ...
   //std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

#if defined __APPLE__ || defined _WIN32
#include "memoryUsageHelper.h"

double MAX_MEMORY_INCREASE_MEGABYTE = 0;
int TOTAL_TRANSFERS = 1;
uint32_t LARGE_FILE_SIZE = (1024 * 1024 * 512); // 0.5G File
std::string SEND_FILE_NAME("large_send.txt");
std::string RECV_FILE_NAME("large_recv.txt");


bool destroyLargeTestFiles()
{
   std::string sendPath = TestEnvironmentConfig::testResourcePath().c_str();
   sendPath.append(SEND_FILE_NAME);
   int sendStatus = remove(sendPath.c_str());
   bool recvStatus = true;
   for (int i = 0; i < TOTAL_TRANSFERS; i++)
   {
      std::stringstream recvPath;
      recvPath << TestEnvironmentConfig::testResourcePath() << RECV_FILE_NAME << i;
      int recvStatusCode = remove(recvPath.str().c_str());
      recvStatus = recvStatus & (recvStatusCode == 0);
   }

   return ((sendStatus == 0) && recvStatus); // will return false if it does not exist */
}

bool createLargeTestFile()
{
   destroyLargeTestFiles();
   std::string path = TestEnvironmentConfig::testResourcePath().c_str();
   path.append(SEND_FILE_NAME);
   uint32_t fillerSize = (4 * 1024);
   std::string fillerData = std::string(fillerSize, 'a');
   FILE* fp = fopen(path.c_str(), "w");
   if (!fp)
   {
      safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): error creating sample file, errno: " << errno);
      return false;
   }

   uint32_t remainingSize = LARGE_FILE_SIZE;
   while (remainingSize > 0)
   {
      uint32_t leftover = std::min(fillerSize, remainingSize);
      uint32_t count = fwrite(fillerData.data(), 1, leftover, fp);
      if (count == 0)
      {
         safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): error populating sample file, errno: " << errno);
         return false;
      }
      remainingSize -= count;
   }

   int status = fclose(fp);
   if (status < 0)
   {
      safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): error closing sample file, errno: " << errno);
      return false;
   }

   return true;
}

TEST_F(XmppFileTransferTest, LargeFileTransferWithMemoryUsageCheck)
{
   if (TestEnvironmentConfig::pageHeap())
   {
      MAX_MEMORY_INCREASE_MEGABYTE = 65;
   }
   else
   {
      MAX_MEMORY_INCREASE_MEGABYTE = 3;
   }

   bool memoryCheckSucceeded;
   memoryUsageHelper::MemoryUsageInfo memoryUsageInfo;  // contains memory usage after the first call
   double memoryUsageBeforeAccountCreationInMegabyte = 0;
   double memoryUsageBeforeTransferInMegabyte = 0;
   double memoryUsageAfterAccountDestructionInMegabyte = 0;
   std::vector<double> memoryAfterFileTransfer;

   {

   if (memoryUsageHelper::getMemoryUsage(memoryUsageInfo) == 0)
   {
      memoryUsageBeforeAccountCreationInMegabyte = memoryUsageInfo.currentProcessMemoryUsage;
      memoryCheckSucceeded = true;
   }
   else
   {
      memoryCheckSucceeded = false;
   }

   ASSERT_TRUE(memoryCheckSucceeded);
      
   ASSERT_TRUE(createLargeTestFile());

   safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): memory before account creation: " << memoryUsageBeforeAccountCreationInMegabyte << " MB");

   XmppTestAccount alice("alice");
   XmppTestAccount bob("bob");

   std::this_thread::sleep_for(std::chrono::milliseconds(500));

   if (memoryUsageHelper::getMemoryUsage(memoryUsageInfo) == 0)
   {
      memoryUsageBeforeTransferInMegabyte = memoryUsageInfo.currentProcessMemoryUsage;
      memoryCheckSucceeded = true;
   }
   else
   {
      memoryCheckSucceeded = false;
   }

   ASSERT_TRUE(memoryCheckSucceeded);

   safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): memory after account creation (before transfer): " << memoryUsageBeforeTransferInMegabyte << " MB");

   for (int i = 0; i < TOTAL_TRANSFERS; i++)
   {
      // make an outgoing file transfer from Alice to Bob using the demo.xten.com server
      XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
      XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

      // Setup the file transfer items
      XmppFileTransferItems items;
      XmppFileTransferItemDetail itemDetail;
      itemDetail.handle = aliceTransferItem;
      itemDetail.localfileName = SEND_FILE_NAME.c_str();
      itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
      items.push_back(itemDetail);

      alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
      alice.fileTransferManager->addParticipant(aliceTransfer, bob.config.full());
      alice.fileTransferManager->start(aliceTransfer);

      auto bobEvents = std::async(std::launch::async, [&] ()
      {
         XmppFileTransferHandle bobTransfer = 0;
         XmppFileTransferItems items;
         {
            XmppFileTransferHandle h;
            NewFileTransferEvent evt;
            ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onNewFileTransfer", 30000, AlwaysTruePred(), h, evt));
            bobTransfer = h;
            items = evt.fileItems;
            ASSERT_EQ(evt.fileTransferType, FileTransferType_Incoming);
            ASSERT_EQ(evt.remoteAddress, alice.config.full());
         }

         // Bob then proceeds to ask the user what should be the filename and location
         // and whether or not they want to accept etc. This is then "configured".
         items[0].acceptedState = ftitem_accepted;
         std::stringstream recvPath;
         recvPath << RECV_FILE_NAME.c_str() << i;
         items[0].localfileName = recvPath.str().c_str();
         items[0].localfilePath = TestEnvironmentConfig::testResourcePath();
         bob.fileTransferManager->configureFileTransferItems(bobTransfer, items);

         // The file transfer is then accepted
         bob.fileTransferManager->accept(bobTransfer);
         {
            // Watch the progress
            FileTransferItemProgressEvent evt;
            evt.percent = 0;
            evt.fileTransferItem = 0;

            while (evt.percent < 100)
            {
               ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onFileTransferItemProgress", 30000, AlwaysTruePred(), bobTransfer, evt));
            }
         }

         {
            // Wait for the transfer to finish (it will be disconnected from Alice's side)
            FileTransferItemEndedEvent ftie;
            ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), bobTransfer, ftie));
            ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
            ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

            FileTransferEndedEvent fte;
            ASSERT_TRUE(cpcExpectEvent(bob.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), bobTransfer, fte));
         }
      });

      auto aliceEvents = std::async(std::launch::async, [&] ()
      {
         XmppFileTransferItems items;
         {
            XmppFileTransferHandle h;
            NewFileTransferEvent evt;
            ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onNewFileTransfer", 30000, AlwaysTruePred(), h, evt));
            items = evt.fileItems;
            ASSERT_EQ(h, aliceTransfer);
            ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
            ASSERT_EQ(evt.fileItems.size(), 1);
            ASSERT_EQ(items[0].localfileName, SEND_FILE_NAME.c_str());
         }

         {
            // Watch the progress
            FileTransferItemProgressEvent evt;
            evt.fileTransferItem = aliceTransferItem;
            evt.percent = 0;
            int progressCount = 0;

            while (evt.percent < 100)
            {
               ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemProgress", 30000, AlwaysTruePred(), aliceTransfer, evt));
               progressCount++;
               if (progressCount % 5 == 0)
               {
                  if (memoryUsageHelper::getMemoryUsage(memoryUsageInfo) == 0)
                  {
                     safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): memory while file transfer #" << (i + 1) << " is in progress: " << memoryUsageInfo.currentProcessMemoryUsage << " MB, file percentage: " << evt.percent);
                  }
                  else
                  {
                     memoryCheckSucceeded = false;
                  }

                  ASSERT_TRUE(memoryCheckSucceeded);
               }
            }
         }

         {
            // Wait for the transfer of the item to finish
            FileTransferItemEndedEvent ftie;
            ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), aliceTransfer, ftie));
            ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_Socks5);
            ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

            // Wait for the transfer to finish
            FileTransferEndedEvent fte;
            ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), aliceTransfer, fte));
         }

         if (memoryUsageHelper::getMemoryUsage(memoryUsageInfo) == 0)
         {
            memoryAfterFileTransfer.push_back(memoryUsageInfo.currentProcessMemoryUsage);
            memoryCheckSucceeded = true;
         }
         else
         {
            memoryCheckSucceeded = false;
         }

         ASSERT_TRUE(memoryCheckSucceeded);

         safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): memory after file transfer #" << (i + 1) << ": " << memoryAfterFileTransfer[i] << " MB");
      });

      // std::async returned a std::future, which we now wait on to be sure that Bob and Alice's threads actually did exit properly
      ASSERT_EQ(bobEvents.wait_for(std::chrono::milliseconds(TOTAL_TRANSFERS * 600000)), std::future_status::ready); // 0.5G should be complete within 10 minutes
      ASSERT_NO_THROW(bobEvents.get());
      ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(TOTAL_TRANSFERS * 600000)), std::future_status::ready); // 0.5G should be complete within 10 minutes
      ASSERT_NO_THROW(aliceEvents.get());
   }

   } // accounts destroyed

   if (memoryUsageHelper::getMemoryUsage(memoryUsageInfo) == 0)
   {
      memoryUsageAfterAccountDestructionInMegabyte = memoryUsageInfo.currentProcessMemoryUsage;
      memoryCheckSucceeded = true;
   }
   else
   {
      memoryCheckSucceeded = false;
   }

   ASSERT_TRUE(memoryCheckSucceeded);
   ASSERT_TRUE(memoryAfterFileTransfer.size() == TOTAL_TRANSFERS);
   double memoryDelta = memoryAfterFileTransfer[TOTAL_TRANSFERS - 1] - memoryUsageBeforeTransferInMegabyte;
   std::stringstream memoryTransferInfoString;
   memoryTransferInfoString << "memory at start: " << memoryUsageBeforeAccountCreationInMegabyte << " MB, memory after account creation: " << memoryUsageBeforeTransferInMegabyte << " MB, ";
   for (int i = 0; i < TOTAL_TRANSFERS; i++)
   {
      memoryTransferInfoString << "transfer #" << (i + 1) << ": " << memoryAfterFileTransfer[i] << " MB, ";
   }
   memoryTransferInfoString << " memory after account destruction: " << memoryUsageAfterAccountDestructionInMegabyte << " MB, threshold: " << MAX_MEMORY_INCREASE_MEGABYTE << " MB, memory delta: " << memoryDelta << " MB";

   safeCout("XmppFileTransferTest::LargeFileTransferWithMemoryUsageCheck(): " << memoryTransferInfoString.str().c_str());

   ASSERT_LT(memoryDelta, MAX_MEMORY_INCREASE_MEGABYTE);
   ASSERT_TRUE(destroyLargeTestFiles());

   // not needed, but handy sometimes when debugging ...
   // std::this_thread::sleep_for(std::chrono::milliseconds(30000));
}

#endif // __APPLE__ || defined _WIN32

#if (defined(__linux__) && !defined(ANDROID)) || defined(__APPLE__)

static std::atomic<bool> ftServerDidStart(false);
static std::atomic<int>  ftServerPort(7999);
static std::atomic<bool> ftServerShouldQuit(false);
static std::future<void> ftStartServer(int serverPort)
{
   ftServerDidStart = false;
   ftServerShouldQuit = false;
   return std::async(std::launch::async, [] (int serverPort)
   {
      int listenFd;
      struct sockaddr_in servaddr;

      listenFd = ::socket(AF_INET, SOCK_STREAM, 0);

      const int sockopt = 1;
      ::setsockopt(listenFd, SOL_SOCKET, SO_REUSEADDR, (const void*)&sockopt, sizeof(sockopt));

      for (int tries = 0; tries <= 5; ++tries)
      {
         memset(&servaddr, 0, sizeof(servaddr));
         servaddr.sin_family = AF_INET;
         servaddr.sin_addr.s_addr = htonl(INADDR_ANY);
         servaddr.sin_port = htons(serverPort);
         int ret = ::bind(listenFd, (struct sockaddr*)&servaddr, sizeof(servaddr));
         if (ret != 0)
         {
            safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): server: bind for startServer failed for port: " << serverPort << " trying next port" << std::endl);
            ++serverPort;
         }
         else
         {
            break;
         }
      }

      ftServerPort = serverPort;

      int rc = ::listen(listenFd, 1024);
      ASSERT_GE(rc, 0) << "XmppFileTransferTest::FileUploadAfterFdThreshold(): error: server: failed to start listening on server on port: " << serverPort << " with listen fd: " << listenFd;
      ftServerDidStart = true;
      safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): server: started successfully on port: " << serverPort << " with listen fd: " << listenFd);

      auto listenThread = std::async(std::launch::async, [listenFd, serverPort] ()
      {
         sockaddr_in clientAddr;
         socklen_t clientAddrSize = sizeof(clientAddr);
         while (!ftServerShouldQuit)
         {
            int acceptRc = ::accept(listenFd, (sockaddr*)&clientAddr, &clientAddrSize);
            if (acceptRc >= 0)
            {
               safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): server: received connection on port: " << serverPort << " with listen fd: " << listenFd << " from " << inet_ntoa(clientAddr.sin_addr) << ":" << ntohs(clientAddr.sin_port));
            }
            else
            {
               safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): server: failed to receive connection on port: " << serverPort << " with listen fd: " << listenFd << " with accept rc: " << acceptRc);
               ftServerShouldQuit = true;
            }
         }
      });

      while (!ftServerShouldQuit)
      {
         std::this_thread::sleep_for(std::chrono::milliseconds(500));
      }

      ::shutdown(listenFd, SHUT_RDWR);
      resip::closeSocket(listenFd);
      safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): server: successfully closed the socket on port: " << serverPort << " with listen fd: " << listenFd);
      listenThread.wait();
   }, serverPort);
}

#ifndef __APPLE__ // disable since fix for OBELISK-6021 was rolled back in OBELISK-6020
TEST_F(XmppFileTransferTest, FileUploadAfterFdThreshold_NPE)
{
   rlimit fdLimit;
   ASSERT_EQ(getrlimit(RLIMIT_NOFILE, &fdLimit), 0) << "XmppFileTransferTest::FileUploadAfterFdThreshold(): error getting system fd limit";
   safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): system fd limits: current-fd: " << fdLimit.rlim_cur << " fd-max: " << fdLimit.rlim_max << " FD_SETSIZE: " << FD_SETSIZE);

   fdLimit.rlim_cur = fdLimit.rlim_max = 10 * FD_SETSIZE;
   ASSERT_EQ(setrlimit(RLIMIT_NOFILE, &fdLimit), 0) << "XmppFileTransferTest::FileUploadAfterFdThreshold(): error setting system fd limit to: " << fdLimit.rlim_max << " system error: " << strerror(resip::getErrno());

   XmppTestAccount alice("alice");
   cpc::string service;
   {
      XmppAccount::XmppAccountHandle h;
      ServiceAvailabilityEvent evt;
      ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onServiceAvailability", 5000, CPCAPI2::test::AlwaysTruePred(), h, evt));
      ASSERT_EQ(h, alice.handle);
      ASSERT_TRUE(evt.XEP0363_available);
      ASSERT_TRUE(!evt.XEP0363_service.empty());
      service = evt.XEP0363_service;
   }

   ftServerShouldQuit = false;
   std::string serverAddr = "127.0.0.1";
   int serverPort = 7999;

   auto serverHandle = ftStartServer(serverPort);
   while (!ftServerDidStart)
   {
      std::this_thread::sleep_for(std::chrono::milliseconds(500));
   }

   std::vector<int> connections;
   int fd = 0;
   while (fd <= FD_SETSIZE)
   {
      struct hostent* he;
      struct sockaddr_in addr;
      fd = ::socket(AF_INET, SOCK_STREAM, 0);
      // ASSERT_GE(fd, 0) << "XmppFileTransferTest::FileUploadAfterFdThreshold(): client: error: failed to create fd";
      if (fd < 0)
      {
         safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): client: error: failed to create fd");
         break;
      }

      addr.sin_family = AF_INET;
      addr.sin_port = htons(serverPort);
      inet_pton(AF_INET, serverAddr.c_str(), &(addr.sin_addr));

      bzero(&(addr.sin_zero), 8);

      int rc = ::connect(fd, (struct sockaddr*)&addr, sizeof(struct sockaddr));
      if (rc < 0)
      {
         safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): client: error: failed to connect on fd: " << fd);
         break;
      }
      safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): client: successfully connected to server on fd: " << fd);
      connections.push_back(fd);
   }

   safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): created " << connections.size() << " connections");

   // initiate a file upload to the xmpp file server
   XmppFileTransferHandle aliceTransfer = alice.fileTransferManager->createFileTransfer(alice.handle);
   XmppFileTransferItemHandle aliceTransferItem = alice.fileTransferManager->createFileTransferItem(alice.handle);

   // Setup the file transfer items
   XmppFileTransferItems items;
   XmppFileTransferItemDetail itemDetail;
   itemDetail.handle = aliceTransferItem;
   itemDetail.localfileName = "send.png";
   itemDetail.localfilePath = TestEnvironmentConfig::testResourcePath();
   items.push_back(itemDetail);

   alice.fileTransferManager->configureFileTransferItems(aliceTransfer, items);
   alice.fileTransferManager->addParticipant(aliceTransfer, service);
   alice.fileTransferManager->start(aliceTransfer);

   auto aliceEvents = std::async(std::launch::async, [&]()
   {
      XmppFileTransferItems items;
      {
         XmppFileTransferHandle h;
         NewFileTransferEvent evt;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onNewFileTransfer", 30000, AlwaysTruePred(), h, evt));
         items = evt.fileItems;
         ASSERT_EQ(h, aliceTransfer);
         ASSERT_EQ(evt.fileTransferType, FileTransferType_Outgoing);
         ASSERT_EQ(evt.fileItems.size(), 1);
         ASSERT_EQ(items[0].localfileName, "send.png");
      }

      {
         // Watch the progress
         FileTransferItemProgressEvent evt;
         evt.fileTransferItem = aliceTransferItem;
         evt.percent = 0;

         while (evt.percent < 100)
         {
            ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemProgress", 30000, AlwaysTruePred(), aliceTransfer, evt));
         }
      }

      {
         // Wait for the transfer of the item to finish
         FileTransferItemEndedEvent ftie;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferItemEnded", 30000, AlwaysTruePred(), aliceTransfer, ftie));
         ASSERT_EQ(ftie.streamTypeAttempted, FileTransferStreamType_XEP0363);
         ASSERT_EQ(ftie.endReason, FileTransferItemEndReason_Complete);

         // Wait for the transfer to finish
         FileTransferEndedEvent fte;
         ASSERT_TRUE(cpcExpectEvent(alice.events, "XmppFileTransferHandler::onFileTransferEnded", 30000, AlwaysTruePred(), aliceTransfer, fte));
      }
   });

   ASSERT_EQ(aliceEvents.wait_for(std::chrono::milliseconds(60000)), std::future_status::ready);
   ASSERT_NO_THROW(aliceEvents.get());

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));

   for (std::vector<int>::iterator i = connections.begin(); i != connections.end(); ++i)
   {
      int closeRc = resip::closeSocket(*i);
      if (closeRc < 0)
      {
         safeCout("XmppFileTransferTest::FileUploadAfterFdThreshold(): client: error closing connection with fd: " << (*i));
      }
   }

   ftServerShouldQuit = true;
   serverHandle.wait();

   std::this_thread::sleep_for(std::chrono::milliseconds(2000));
}
#endif // #ifndef __APPLE__

#endif // (__linux__ && !ANDROID) || __APPLE__

}

#endif // CPCAPI2_XMPP_FILE_TRANSFER_MODULE
