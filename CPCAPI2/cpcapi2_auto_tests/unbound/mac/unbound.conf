# Unbound configuration file
# See example.conf for more settings and syntax
server:
    # verbosity number, 0 is least verbose. 1 is default.
    verbosity: 1

    # the log file, "" means log to stderr.
    logfile: ./unbound.log
    # use-syslog: yes
    # print one line with time, IP, name, type, class for every query.
    # log-queries: yes
    # val-log-level: 2
    # Detach from the terminal, run in background, "yes" or "no".
    do-daemonize: no

    username: root
    
    interface: 127.0.0.1
    # 0.0.0.0 replaced by j<PERSON><PERSON> mac runs
    interface: 0.0.0.0
    interface: ::0
    
    access-control: 0.0.0.0/0 allow
 
#ipv4:
    include: "../cpcapi2.local.txt"    
    include: "../pribroken.local.txt"    
    include: "../pribroken2.local.txt"    
    include: "../pribrokentcp.local.txt"    
    include: "../priudpsectcp.local.txt"    
    include: "../cp.sipit.net.txt"    
    include: "../pribrokennosec.local.txt"    
    include: "../pribrokennosectcp.local.txt"    
    include: "../pribrokensecbroken.local.txt"    
    include: "../pribrokensecbrokentcp.local.txt"
    include: "../priinvalidporttcp.local.txt"    
    include: "../pritls.local.txt"    
    include: "../mutualtls.local.txt"
    include: "../nodomain.local.txt"    
    include: "../notransport.local.txt"
    include: "../ipv4only.arpa.txt"
    include: "../prisecwithauth.local.txt" 
    include: "../rfc2782.local.txt"
    include: "../multiplearecord.local.txt"
    include: "../multia4multirepro.local.txt"
    include: "../multia4multirepro2.local.txt"
    include: "../tcpdns.local.txt"
    include: "../tcpdnsv6.local.txt"
    include: "../prisecwithauthtcpdns.local.txt"
    include: "../equalweights.local.txt"
    include: "../equalweights_samerepro.local.txt"
    include: "../multialastworks.local.txt"
    include: "../pribrokensecbrokenterworking.local.txt"
    include: "../naptr.local.txt"
    include: "../singlearecord.local.txt"
    include: "../singletcpsrvrecord.txt"
    include: "../tripletcpsrvrecord.txt"
#mixed:
    include: "../priv6secv4.local.txt"    
    include: "../priv4secv6.local.txt"    
    include: "../priv4.local.txt"    
    include: "../priv6.local.txt"    
    include: "../priv6broken.local.txt"    
    include: "../priv4broken.local.txt"
    include: "../dualstack.local.txt"    
    include: "../dualstackbroken.local.txt"    
    include: "../dualstackv4broken.local.txt"    
    include: "../dualstackv6broken.local.txt"    
    include: "../dualstackinvalid.local.txt"    
    include: "../dualstackv4invalid.local.txt"    
    include: "../dualstackv6invalid.local.txt"    
    include: "../invalid.local.txt"
    include: "../dualstacknoaaaaresponse.local.txt"
#ipv6:    
    include: "../cpcapi2.ipv6.local.txt"    
    include: "../pribroken.ipv6.local.txt"    
    include: "../pribrokentcp.ipv6.local.txt"    
    include: "../priudpsectcp.ipv6.local.txt"    
    include: "../pribrokennosec.ipv6.local.txt"    
    include: "../pribrokennosectcp.ipv6.local.txt"    
    include: "../pribrokensecbroken.ipv6.local.txt"    
    include: "../pribrokensecbrokentcp.ipv6.local.txt"
    include: "../priinvalidporttcp.ipv6.local.txt"    
    include: "../pritls.ipv6.local.txt"    
    include: "../nodomain.ipv6.local.txt"    
    include: "../notransport.ipv6.local.txt"
    include: "../multipleaaaarecord.ipv6.local.txt"
    include: "../prisecwithauth.ipv6.local.txt"
#switch:
    include: "../pritcpv4sectcpv4.local.txt"
    include: "../pritcpv4secudpv4.local.txt"
    include: "../pritcpv4sectcpv6.local.txt"
    include: "../pritcpv4secudpv6.local.txt"
    include: "../priudpv4sectcpv4.local.txt"
    include: "../priudpv4secudpv4.local.txt"
    include: "../priudpv4sectcpv6.local.txt"
    include: "../priudpv4secudpv6.local.txt"
    include: "../pritcpv6sectcpv4.local.txt"
    include: "../pritcpv6secudpv4.local.txt"
    include: "../pritcpv6sectcpv6.local.txt"
    include: "../pritcpv6secudpv6.local.txt"
    include: "../priudpv6sectcpv4.local.txt"
    include: "../priudpv6secudpv4.local.txt"
    include: "../priudpv6sectcpv6.local.txt"
    include: "../priudpv6secudpv6.local.txt"
    include: "../pritcpv4sectcpv4_v2.local.txt"
    include: "../pritcpv4sectcpv6_v2.local.txt"
    include: "../pritcpv4sectlsv4.txt"
#xmpp:
    include: "../xmpp.srv.failover.txt"
    include: "../xmpp.dns.failover.txt"
    include: "../xmpp.blanksrv.failover.txt"
    include: "../xmpp.nosrv.failover.txt"
    include: "../xmpp.dns.failover.afterhandshake.txt"
    include: "../xmpp.onlyarecords.txt"
#cpe:
    include: "../qa.cpe.alz.ninja.txt"
    
 forward-zone:
    name: "."
    # forward anything else to CP servers
    forward-addr: 10.20.5.165
    forward-addr: 10.20.5.160
