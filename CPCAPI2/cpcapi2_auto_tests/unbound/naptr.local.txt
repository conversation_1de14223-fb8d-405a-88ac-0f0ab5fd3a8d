local-zone: "naptr.local." static
local-data: "naptr.local. NAPTR 10 100 'S' SIP+D2T '' _sip._tcp.naptr.local."
local-data: "naptr.local. NAPTR 10 150 'S' SIP+D2T '' _sip._udp.naptr.local."
local-data: "working.naptr.local. A 127.0.0.1"
local-data: "working2.naptr.local. A 127.0.0.1"
local-data: "fail.naptr.local. A *********"
local-data: "_sip._tcp.naptr.local. SRV 0 0 6060 fail.naptr.local."
local-data: "_sip._udp.naptr.local. SRV 0 0 6060 working.naptr.local."
local-data: "_sip._udp.naptr.local. SRV 10 0 6060 working2.naptr.local."
local-data: "_sips._tcp.naptr.local. SRV 0 0 6060 working.naptr.local."
local-data: "_sips._tcp.naptr.local. SRV 10 0 6060 working2.naptr.local."
# NAPTR record format: preference order flag services regex replacement
#    preference: preference for the record, lower number should be tried first
#    order: order to try the records, only relevant if multiple records have the same preference
#    flag: refers to queries required based on result, "S" for SRV, "A" "AAAA" "A6" for hosts, "U" for absolute URI, "P" for additional NAPTRs
#    services: for services e.g. "SIP+D2U" sip over UDP, "SIP+D2T" sip over TCP, "E2U+email" for email
#    regex: regular expression used to mutate the request domain into something different
#    replacement: mutually exclusive to regex, is the result of the NAPTR lookup on the domain

