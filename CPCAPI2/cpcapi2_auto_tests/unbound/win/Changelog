8 December 2016: Wouter
	- <PERSON>x downcast warnings from visual studio in sldns code.

7 December 2016: Ralph
	- Add DSA support for OpenSSL 1.1.0
	- Fix remote control without cert for LibreSSL

6 December 2016: George
	- Added generic EDNS code for registering known EDNS option codes,
	  bypassing the cache response stage and uniquifying mesh states. Four EDNS
	  option lists were added to module_qstate (module_qstate.edns_opts_*) to
	  store EDNS options from/to front/back side.
	- Added two flags to module_qstate (no_cache_lookup, no_cache_store) that
	  control the modules' cache interactions.
	- Added code for registering inplace callback functions. The registered
	  functions can be called just before replying with local data or Chaos,
	  replying from cache, replying with SERVFAIL, replying with a resolved
	  query, sending a query to a nameserver. The functions can inspect the
	  available data and maybe change response/query related data (i.e. append
	  EDNS options).
	- Updated Python module for the above.
	- Updated Python documentation.

5 December 2016: Ralph
	- Fix #1173: differ local-zone type deny from unset
	  tag_actions element.

5 December 2016: Wouter
	- Fix #1170: document that 'inform' local-zone uses local-data.

1 December 2016: Ralph
	- hyphen as minus fix, by <PERSON>

30 November 2016: Ralph
	- Added local-zones and local-data bulk addition and removal
	  functionality in unbound-control (local_zones, local_zones_remove,
	  local_datas and local_datas_remove).
	- iana portlist update

29 November 2016: Wouter
	- version 1.6.0 is in the development branch.
	- braces in view.c around lock statements.

28 November 2016: Wouter
	- new install-sh.

25 November 2016: Wouter
	- Fix that with openssl 1.1 control-use-cert: no uses less cpu, by
	  using no encryption over the unix socket.

22 Novenber 2016: Ralph
	- Make access-control-tag-data RDATA absolute. This makes the RDATA
	  origin consistent between local-data and access-control-tag-data.
	- Fix NSEC ENT wildcard check. Matching wildcard does not have to be a
	  subdomain of the NSEC owner.
	- QNAME minimisation uses QTYPE=A, therefore always check cache for
	  this type in harden-below-nxdomain functionality.
	- Added unit test for QNAME minimisation + harden below nxdomain
	  synergy.

22 November 2016: Wouter
	- iana portlist update.
	- Fix unit tests for DS hash processing for fake-dsa test option.
	- patch from Dag-Erling Smorgrav that removes code that relies
	  on sbrk().

21 November 2016: Wouter
	- Fix #1158: reference RFC 8020 "NXDOMAIN: There Really Is Nothing
	  Underneath" for the harden-below-nxdomain option.

10 November 2016: Ralph
	- Fix #1155: test status code of unbound-control in 04-checkconf,
	  not the status code from the tee command.

4 November 2016: Ralph
	- Added stub-ssl-upstream and forward-ssl-upstream options.

4 November 2016: Wouter
	- configure detects ssl security level API function in the autoconf
	  manner.  Every function on its own, so that other libraries (eg.
	  LibreSSL) can develop their API without hindrance.
	- Fix #1154: segfault when reading config with duplicate zones.
	- Note that for harden-below-nxdomain the nxdomain must be secure,
	  this means nsec3 with optout is insufficient.

3 November 2016: Ralph
	- Set OpenSSL security level to 0 when using aNULL ciphers.

3 November 2016: Wouter
	- .gitattributes line for githubs code language display.
	- log-identity: config option to set sys log identity, patch from
	  "Robin H. Johnson" <<EMAIL>>

2 November 2016: Wouter
	- iana portlist update.

31 October 2016: Wouter
	- Fix failure to build on arm64 with no sbrk.
	- iana portlist update.

28 October 2016: Wouter
	- Patch for server.num.zero_ttl stats for count of expired replies,
	  from Pavel Odintsov.

26 October 2016: Wouter
	- Fix unit tests for openssl 1.1, with no DSA, by faking DSA, enabled
	  with the undocumented switch 'fake-dsa'.  It logs a warning.

25 October 2016: Wouter
	- Fix #1134: unbound-control set_option -- val-override-date: -1 works
	  immediately to ignore datetime, or back to 0 to enable it again.
	  The -- is to ignore the '-1' as an option flag.

24 October 2016: Wouter
	- serve-expired config option: serve expired responses with TTL 0.
	- g.root-servers.net has AAAA address.

21 October 2016: Wouter
	- Ported tests for local_cname unit test to testbound framework.

20 October 2016: Wouter
	- suppress compile warning in lex files.
	- init lzt variable, for older gcc compiler warnings.
	- fix --enable-dsa to work, instead of copying ecdsa enable.
	- Fix DNSSEC validation of query type ANY with DNAME answers.
	- Fixup query_info local_alias init.

19 October 2016: Wouter
	- Fix #1130: whitespace in example.conf.in more consistent.

18 October 2016: Wouter
	- Patch that resolves CNAMEs entered in local-data conf statements that
	  point to data on the internet, from Jinmei Tatuya (Infoblox).
	- Removed patch comments from acllist.c and msgencode.c
	- Added documentation doc/CNAME-basedRedirectionDesignNotes.pdf,
	  from Jinmei Tatuya (Infoblox).
	- Fix #1125: unbound could reuse an answer packet incorrectly for
	  clients with different EDNS parameters, from Jinmei Tatuya.
	- Fix #1118: libunbound.pc sets strange Libs, Libs.private values.
	- Added Requires line to libunbound.pc
	- Please doxygen by modifying mesh.h

17 October 2016: Wouter
	- Re-fix #839 from view commit overwrite.
	- Fixup const void cast warning.

12 October 2016: Ralph
	- Free view config elements.

11 October 2016: Ralph
	- Added qname-minimisation-strict config option.
	- iana portlist update.
	- fix memoryleak logfile when in debug mode.

5 October 2016: Ralph
	- Added views functionality.
	- Fix #1117: spelling errors, from Robert Edmonds.

30 September 2016: Wouter
	- Fix Nits for 1.5.10 reported by Dag-Erling Smorgrav.

29 September 2016: Wouter
	- Fix #838: 1.5.10 cannot be built on Solaris, undefined PATH_MAX.
	- Fix #839: Memory grows unexpectedly with large RPZ files.
	- Fix #840: infinite loop in unbound_munin_ plugin on unowned lockfile.
	- Fix #841: big local-zone's make it consume large amounts of memory.

27 September 2016: Wouter
	- tag for 1.5.10 release
	- trunk contains 1.5.11 in development.
	- Fix dnstap relaying "random" messages instead of resolver/forwarder
	  responses, from Nikolay Edigaryev.
	- Fix #836: unbound could echo back EDNS options in an error response.

20 September 2016: Wouter
	- iana portlist update.
	- Fix #835: fix --disable-dsa with nettle verify.
	- tag for 1.5.10rc1 release.

15 September 2016: Wouter
	- Fix 883: error for duplicate local zone entry.
	- Test for openssl init_crypto and init_ssl functions.

15 September 2016: Ralph
	- fix potential memory leak in daemon/remote.c and nullpointer
	  dereference in validator/autotrust.
	- iana portlist update.

13 September 2016: Wouter
	- Silenced flex-generated sign-unsigned warning print with gcc
	  diagnostic pragma.
	- Fix for new splint on FreeBSD.  Fix cast for sockaddr_un.sun_len.

9 September 2016: Wouter
	- Fix #831: workaround for spurious fread_chk warning against petal.c

5 September 2016: Ralph
	- Take configured minimum TTL into consideration when reducing TTL
	  to original TTL from RRSIG.

5 September 2016: Wouter
	- Fix #829: doc of sldns_wire2str_rdata_buf() return value has an
	  off-by-one typo, from Jinmei Tatuya (Infoblox).
	- Fix incomplete prototypes reported by Dag-Erling Smørgrav.
	- Fix #828: missing type in access-control-tag-action redirect results
	  in NXDOMAIN.

2 September 2016: Wouter
	- Fix compile with openssl 1.1.0 with api=1.1.0.

1 September 2016: Wouter
	- RFC 7958 is now out, updated docs for unbound-anchor.
	- Fix for compile without warnings with openssl 1.1.0.
	- Fix #826: Fix refuse_non_local could result in a broken response.
	- iana portlist update.

29 August 2016: Wouter
	- Fix #777: OpenSSL 1.1.0 compatibility, patch from Sebastian A.
	  Siewior.
	- Add default root hints for IPv6 E.ROOT-SERVERS.NET, 2001:500:a8::e.

25 August 2016: Ralph
	- Clarify local-zone-override entry in unbound.conf.5 
	
25 August 2016: Wouter
	- 64bit build option for makedist windows compile, -w64.

24 August 2016: Ralph
	- Fix #820: set sldns_str2wire_rr_buf() dual meaning len parameter
	  in each iteration in find_tag_datas().
	- unbound.conf.5 entries for define-tag, access-control-tag,
	  access-control-tag-action, access-control-tag-data, local-zone-tag,
	  and local-zone-override.
	  
23 August 2016: Wouter
	- Fix #804: unbound stops responding after outage.  Fixes queries
	  that attempt to wait for an empty list of subqueries.
	- Fix #804: lower num_target_queries for iterator also for failed
	  lookups.

8 August 2016: Wouter
	- Note that OPENPGPKEY type is RFC 7929.

4 August 2016: Wouter
	- Fix #807: workaround for possible some "unused" function parameters
	  in test code, from Jinmei Tatuya.

3 August 2016: Wouter
	- use sendmsg instead of sendto for TFO.

28 July 2016: Wouter
	- Fix #806: wrong comment removed.

26 July 2016: Wouter
	- nicer ratelimit-below-domain explanation.

22 July 2016: Wouter
	- Fix #801: missing error condition handling in
	  daemon_create_workers().
	- Fix #802: workaround for function parameters that are "unused"
	  without log_assert.
	- Fix #803: confusing (and incorrect) code comment in daemon_cleanup().

20 July 2016: Wouter
	- Fix typo in unbound.conf.

18 July 2016: Wouter
	- Fix #798: Client-side TCP fast open fails (Linux).

14 July 2016: Wouter
	- TCP Fast open patch from Sara Dickinson.
	- Fixed unbound.doxygen for 1.8.11.

7 July 2016: Wouter
	- access-control-tag-data implemented. verbose(4) prints tag debug.

5 July 2016: Wouter
	- Fix dynamic link of anchor-update.exe on windows.
	- Fix detect of mingw for MXE package build.
	- Fixes for 64bit windows compile.
	- Fix #788 for nettle 3.0: Failed to build with Nettle >= 3.0 and
	  --with-libunbound-only --with-nettle.

4 July 2016: Wouter
	- For #787: prefer-ip6 option for unbound.conf prefers to send
	  upstream queries to ipv6 servers.
	- Fix #787: outgoing-interface netblock/64 ipv6 option to use linux
	  freebind to use 64bits of entropy for every query with random local
	  part.

30 June 2016: Wouter
	- Document always_transparent, always_refuse, always_nxdomain types.

29 June 2016: Wouter
	- Fix static compile on windows missing gdi32.

28 June 2016: Wouter
	- Create a pkg-config file for libunbound in contrib.

27 June 2016: Wouter
	- Fix #784: Build configure assumess that having getpwnam means there
	  is endpwent function available.
	- Updated repository with newer flex and bison output.

24 June 2016: Ralph
	- Possibility to specify local-zone type for an acl/tag pair
	- Possibility to specify (override) local-zone type for a source address
	  block
16 June 2016: Ralph
	- Decrease dp attempts at each QNAME minimisation iteration

16 June 2016: Wouter
	- Fix tcp timeouts in tv.usec.

15 June 2016: Wouter
	- TCP_TIMEOUT is specified in milliseconds.
	- If more than half of tcp connections are in use, a shorter timeout
	  is used (200 msec, vs 2 minutes) to pressure tcp for new connects.

14 June 2016: Ralph
	- QNAME minimisation unit test for dropped QTYPE=A queries.

14 June 2016: Wouter
	- Fix 775: unbound-host and unbound-anchor crash on windows, ignore
	  null delete for wsaevent.
	- Fix spelling in freebind option man page text.
	- Fix windows link of ssl with crypt32.
	- Fix 779: Union casting is non-portable.
	- Fix 780: MAP_ANON not defined in HP-UX 11.31.
	- Fix 781: prealloc() is an HP-UX system library call.

13 June 2016: Ralph
	- Use QTYPE=A for QNAME minimisation.
	- Keep track of number of time-outs when performing QNAME minimisation.
	  Stop minimising when number of time-outs for a QNAME/QTYPE pair is
	  more than three.

13 June 2016: Wouter
	- Fix #778: unbound 1.5.9: -h segfault (null deref).
	- Fix directory: fix for unbound-checkconf, it restores cwd.

10 June 2016: Wouter
	- And delete service.conf.shipped on uninstall.
	- In unbound.conf directory: dir immediately changes to that directory,
	  so that include: file below that is relative to that directory.
	  With chroot, make the directory an absolute path inside chroot.
	- keep debug symbols in windows build.
	- do not delete service.conf on windows uninstall.
	- document directory immediate fix and allow EXECUTABLE syntax in it
	  on windows.

9 June 2016: Wouter
	- Trunk is called 1.5.10 (with previous fixes already in there to 2
	  june).
	- Revert fix for NetworkService account on windows due to breakage
	  it causes.
	- Fix that windows install will not overwrite existing service.conf
	  file (and ignore gui config choices if it exists).

7 June 2016: Ralph
	- Lookup localzones by taglist from acl.
	- Possibility to lookup local_zone, regardless the taglist.
	- Added local_zone/taglist/acl unit test.

7 June 2016: Wouter
	- Fix #773: Non-standard Python location build failure with pyunbound.
	- Improve threadsafety for openssl 0.9.8 ecdsa dnssec signatures.

6 June 2016: Wouter
	- Better help text from -h (from Ray Griffith).
	- access-control-tag config directive.
	- local-zone-override config directive.
	- access-control-tag-action and access-control-tag-data config
	  directives.
	- free acl-tags, acltag-action and acltag-data config lists during
	  initialisation to free up memory for more entries.

3 June 2016: Wouter
	- Fix to not ignore return value of chown() in daemon startup.

2 June 2016: Wouter
	- Fix libubound for edns optlist feature.
	- Fix distinction between free and CRYPTO_free in dsa and ecdsa alloc.
	- Fix #752: retry resource temporarily unavailable on control pipe.
	- un-document localzone tags.
	- tag for release 1.5.9rc1.
	  And this also became release 1.5.9.
	- Fix (for 1.5.10): Fix unbound-anchor.exe file location defaults to
	  Program Files with (x86) appended.
	- re-documented localzone tags in example.conf.

31 May 2016: Wouter
	- Fix windows service to be created run with limited rights, as a
	  network service account, from Mario Turschmann.
	- compat strsep implementation.
	- generic edns option parse and store code.
	- and also generic edns options for upstream messages (and replies).
	  after parse use edns_opt_find(edns.opt_list, LDNS_EDNS_NSID),
	  to insert use edns_opt_append(edns, region, code, len, bindata) on
	  the opt_list passed to send_query, or in edns_opt_inplace_reply.

30 May 2016: Wouter
	- Fix time in case answer comes from cache in ub_resolve_event().
	- Attempted fix for #765: _unboundmodule missing for python3.

27 May 2016: Wouter
	- Fix #770: Small subgroup attack on DH used in unix pipe on localhost
	  if unbound control uses a unix local named pipe.
	- Document write permission to directory of trust anchor needed.
	- Fix #768:  Unbound Service Sometimes Can Not Shutdown
	  Completely, WER Report Shown Up.  Close handle before closing WSA.

26 May 2016: Wouter
	- Updated patch from Charles Walker.

24 May 2016: Wouter
	- disable-dnssec-lame-check config option from Charles Walker.
	- remove memory leak from lame-check patch.
	- iana portlist update.

23 May 2016: Wouter
	- Fix #767:  Reference to an expired Internet-Draft in
	  harden-below-nxdomain documentation.

20 May 2016: Ralph
	- No QNAME minimisation fall-back for NXDOMAIN answers from DNSSEC 
	  signed zones.
	- iana portlist update.

19 May 2016: Wouter
	- Fix #766: dns64 should synthesize results on timeout/errors.

18 May 2016: Wouter
	- Fix #761: DNSSEC LAME false positive resolving nic.club.

17 May 2016: Wouter
	- trunk updated with output of flex 2.6.0.

6 May 2016: Wouter
	- Fix memory leak in out-of-memory conditions of local zone add.

29 April 2016: Wouter
	- Fix sldns with static checking fixes copied from getdns.

28 April 2016: Wouter
	- Fix #759: 0x20 capsforid no longer checks type PTR, for
	  compatibility with cisco dns guard.  This lowers false positives.

18 April 2016: Wouter
	- Fix some malformed reponses to edns queries get fallback to nonedns.

15 April 2016: Wouter
	- cachedb module event handling design.

14 April 2016: Wouter
	- cachedb module framework (empty).
	- iana portlist update.

12 April 2016: Wouter
	- Fix #753: document dump_requestlist is for first thread.

24 March 2016: Wouter
	- Document permit-small-holddown for 5011 debug.
	- Fix #749: unbound-checkconf gets SIGSEGV when use against a
	  malformatted conf file.

23 March 2016: Wouter
	- OpenSSL 1.1.0 portability, --disable-dsa configure option.

21 March 2016: Wouter
	- Fix compile of getentropy_linux for SLES11 servicepack 4.
	- Fix dnstap-log-resolver-response-messages, from Nikolay Edigaryev.
	- Fix test for openssl to use HMAC_Update for 1.1.0.
	- acx_nlnetlabs.m4 to v33, with HMAC_Update.
	- acx_nlnetlabs.m4 to v34, with -ldl -pthread test for libcrypto.
	- ERR_remove_state deprecated since openssl 1.0.0.
	- OPENSSL_config is deprecated, removing.

18 March 2016: Ralph
	- Validate QNAME minimised NXDOMAIN responses.
	- If QNAME minimisation is enabled, do cache lookup for QTYPE NS in
	  harden-below-nxdomain.

17 March 2016: Ralph
	- Limit number of QNAME minimisation iterations.

17 March 2016: Wouter
	- Fix #746: Fix unbound sets CD bit on all forwards.
	  If no trust anchors, it'll not set CD bit when forwarding to another
	  server.  If a trust anchor, no CD bit on the first attempt to a
	  forwarder, but CD bit thereafter on repeated attempts to get DNSSEC.
	- iana portlist update.

16 March 2016: Wouter
	- Fix ip-transparent for ipv6 on FreeBSD, thanks to Nick Hibma.
	- Fix ip-transparent for tcp on freebsd.

15 March 2016: Wouter
	- ip_freebind: yesno option in unbound.conf sets IP_FREEBIND for
	  binding to an IP address while the interface or address is down.

14 March 2016: Wouter
	- Fix warnings in ifdef corner case, older or unknown libevent.
	- Fix compile for ub_event code with older libev.

11 March 2016: Wouter
	- Remove warning about unused parameter in event_pluggable.c.
	- Fix libev usage of dispatch return value.
	- No side effects in tolower() call, in case it is a macro.
	- For test put free in pluggable api in parenthesis.

10 March 2016: Wouter
	- Fixup backend2str for libev.

09 March 2016: Willem
	- User defined pluggable event API for libunbound
	- Fixup of compile fix for pluggable event API from P.Y. Adi
	  Prasaja.

09 March 2016: Wouter
	- Updated configure and ltmain.sh.
	- Updated L root IPv6 address.

07 March 2016: Wouter
	- Fix #747: assert in outnet_serviced_query_stop.
	- iana ports fetched via https.
	- iana portlist update.

03 March 2016: Wouter
	- configure tests for the weak attribute support by the compiler.

02 March 2016: Wouter
	- 1.5.8 release tag
	- trunk contains 1.5.9 in development.
	- iana portlist update.
	- Fix #745: unbound.py - idn2dname throws UnicodeError when idnname
	  contains trailing dot.

24 February 2016: Wouter
	- Fix OpenBSD asynclook lock free that gets used later (fix test code).
	- Fix that NSEC3 negative cache is used when there is no salt.

23 February 2016: Wouter
	- ub_ctx_set_stub() function for libunbound to config stub zones.
	- sorted ubsyms.def file with exported libunbound functions.

19 February 2016: Wouter
	- Print understandable debug log when unusable DS record is seen.
	- load gost algorithm if digest is seen before key algorithm.
	- iana portlist update.

17 February 2016: Wouter
	- Fix that "make install" fails due to "text file busy" error.

16 February 2016: Wouter
	- Set IPPROTO_IP6 for ipv6 sockets otherwise invalid argument error.

15 February 2016: Wouter
	- ip-transparent option for FreeBSD with IP_BINDANY socket option.
	- wait for sendto to drain socket buffers when they are full.

9 February 2016: Wouter
	- Test for type OPENPGPKEY.
	- insecure-lan-zones: yesno config option, patch from Dag-Erling
	  Smørgrav.

8 February 2016: Wouter
	- Fix patch typo in prevuous commit for 734 from Adi Prasaja.
	- RR Type CSYNC support RFC 7477, in debug printout and config input.
	- RR Type OPENPGPKEY support (draft-ietf-dane-openpgpkey-07).

29 January 2016: Wouter
	- Neater cmdline_verbose increment patch from Edgar Pettijohn.

27 January 2016: Wouter
	- Made netbsd sendmsg test nonfatal, in case of false positives.
	- Fix #741: log message for dnstap socket connection is more clear.

26 January 2016: Wouter
	- Fix #734: chown the pidfile if it resides inside the chroot.
	- Use arc4random instead of random in tests (because it is
	  available, possibly as compat, anyway).
	- Fix cmsg alignment for argument to sendmsg on NetBSD.
	- Fix that unbound complains about unimplemented IP_PKTINFO for
	  sendmsg on NetBSD (for interface-automatic).

25 January 2016: Wouter
	- Fix #738: Swig should not be invoked with CPPFLAGS.

19 January 2016: Wouter
	- Squelch 'cannot assign requested address' log messages unless
	  verbosity is high, it was spammed after network down.

14 January 2016: Wouter
	- Fix to simplify empty string checking from Michael McConville.
	- iana portlist update.

12 January 2016: Wouter
	- Fix #734: Do not log an error when the PID file cannot be chown'ed.
	  Patch from Simon Deziel.

11 January 2016: Wouter
	- Fix test if -pthreads unused to use better grep for portability.

06 January 2016: Wouter
	- Fix mingw crosscompile for recent mingw.
	- Update aclocal, autoconf output with new versions (1.15, 2.4.6).

05 January 2016: Wouter
	- #731: tcp-mss, outgoing-tcp-mss options for unbound.conf, patch
	  from Daisuke Higashi.
	- Support RFC7686: handle ".onion" Special-Use Domain. It is blocked
	  by default, and can be unblocked with "nodefault" localzone config.

04 January 2016: Wouter
	- Define DEFAULT_SOURCE together with BSD_SOURCE when that is defined,
	  for Linux glibc 2.20.
	- Fixup contrib/aaaa-filter-iterator.patch for moved contents in the
	  source code, so it applies cleanly again.  Removed unused variable
	  warnings.

15 December 2015: Ralph
	- Fix #729: omit use of escape sequences in echo since they are not 
	  portable (unbound-control-setup).

11 December 2015: Wouter
	- remove NULL-checks before free, patch from Michael McConville.
	- updated ax_pthread.m4 to version 21 with clang support, this
	  removes a warning from compilation.
	- OSX portability, detect if sbrk is deprecated.
	- OSX clang, stop -pthread unused during link stage warnings.
	- OSX clang new flto check.

10 December 2015: Wouter
	- 1.5.7 release
	- trunk has 1.5.8 in development.

8 December 2015: Wouter
	- Fixup 724 for unbound-control.

7 December 2015: Ralph
	- Do not minimise forwarded requests.

4 December 2015: Wouter
	- Removed unneeded whitespace from example.conf.

3 December 2015: Ralph
	- (after rc1 tag)
	- Committed fix to qname minimisation and unit test case for it.
	
3 December 2015: Wouter
	- iana portlist update.
	- 1.5.7rc1 prerelease tag.

2 December 2015: Wouter
	- Fixup 724: Fix PCA prompt for unbound-service-install.exe.
	  re-enable stdout printout.
	- For 724: Add Changelog to windows binary dist.

1 December 2015: Ralph
	- Qname minimisation review fixes

1 December 2015: Wouter
	- Fixup 724 fix for fname_after_chroot() calls.
	- Remove stdout printout for unbound-service-install.exe
	- .gitignore for git users.

30 November 2015: Ralph
	- Implemented qname minimisation

30 November 2015: Wouter
	- Fix for #724: conf syntax to read files from run dir (on Windows).

25 November 2015: Wouter
	- Fix for #720, fix unbound-control-setup windows batch file.

24 November 2015: Wouter
	- Fix #720: add windows scripts to zip bundle.
	- iana portlist update.

20 November 2015: Wouter
	- Added assert on rrset cache correctness.
	- Fix that malformed EDNS query gets a response without malformed EDNS.

18 November 2015: Wouter
	- newer acx_nlnetlabs.m4.
	- spelling fixes from Igor Sobrado Delgado.

17 November 2015: Wouter
	- Fix #594. libunbound: optionally use libnettle for crypto.
	  Contributed by Luca Bruno.  Added --with-nettle for use with
	  --with-libunbound-only.
	- refactor nsec3 hash implementation to be more library-portable.
	- iana portlist update.
	- Fixup DER encoded DSA signatures for libnettle.

16 November 2015: Wouter
	- Fix for lenient accept of reverse order DNAME and CNAME.

6 November 2015: Wouter
	- Change example.conf: ftp.internic.net to https://www.internic.net

5 November 2015: Wouter
	- ACX_SSL_CHECKS no longer adds -ldl needlessly.

3 November 2015: Wouter
	- Fix #718: Fix unbound-control-setup with support for env
	  without HEREDOC bash support.

29 October 2015: Wouter
	- patch from Doug Hogan for SSL_OP_NO_SSLvx options.
	- Fix #716: nodata proof with empty non-terminals and wildcards.

28 October 2015: Wouter
	- Fix checklock testcode for linux threads on exit.

27 October 2015: Wouter
	- isblank() compat implementation.
	- detect libexpat without xml_StopParser function.
	- portability fixes.
	- portability, replace snprintf if return value broken.

23 October 2015: Wouter
	- Fix #714: Document config to block private-address for IPv4
	  mapped IPv6 addresses.

22 October 2015: Wouter
	- Fix #712: unbound-anchor appears to not fsync root.key.

20 October 2015: Wouter
	- 1.5.6 release.
	- trunk tracks development of 1.5.7.

15 October 2015: Wouter
	- Fix segfault in the dns64 module in the formaterror error path.
	- Fix sldns_wire2str_rdata_scan for malformed RRs.
	- tag for 1.5.6rc1 release.

14 October 2015: Wouter
	- ANY responses include DNAME records if present, as per Evan Hunt's
	  remark in dnsop.
	- Fix manpage to suggest using SIGTERM to terminate the server.

9 October 2015: Wouter
	- Default for ssl-port is port 853, the temporary port assignment
	  for secure domain name system traffic.
	  If you used to rely on the older default of port 443, you have
	  to put a clause in unbound.conf for that.  The new value is likely
	  going to be the standardised port number for this traffic.
	- iana portlist update.

6 October 2015: Wouter
	- 1.5.5 release.
	- trunk tracks the development of 1.5.6.

28 September 2015: Wouter
	- MAX_TARGET_COUNT increased to 64, to fix up sporadic resolution
	  failures.
	- tag for 1.5.5rc1 release.
	- makedist.sh: pgp sig echo commands.

25 September 2015: Wouter
	- Fix unbound-control flush that does not succeed in removing data.

22 September 2015: Wouter
	- Fix config globbed include chroot treatment, this fixes reload of
	  globs (patch from Dag-Erling Smørgrav).
	- iana portlist update.
	- Fix #702: New IPs for for h.root-servers.net.
	- Remove confusion comment from canonical_compare() function.
	- Fix #705: ub_ctx_set_fwd() return value mishandled on windows.
	- testbound selftest also works in non-debug mode.
	- Fix minor error in unbound.conf.5.in
	- Fix unbound.conf(5) access-control description for precedence
	  and default.

31 August 2015: Wouter
	- changed windows setup compression to be more transparent.

28 August 2015: Wouter
	- Fix #697: Get PY_MAJOR_VERSION failure at configure for python
	  2.4 to 2.6.
	- Feature #699: --enable-pie option to that builds PIE binary.
	- Feature #700: --enable-relro-now option that enables full read-only
	  relocation.

24 August 2015: Wouter
	- Fix deadlock for local data add and zone add when unbound-control
	  list_local_data printout is interrupted.
	- iana portlist update.
	- Change default of harden-algo-downgrade to off.  This is lenient
	  for algorithm rollover.

13 August 2015: Wouter
	- 5011 implementation does not insist on all algorithms, when
	  harden-algo-downgrade is turned off.
	- Reap the child process that libunbound spawns.

11 August 2015: Wouter
	- Fix #694: configure script does not detect LibreSSL 2.2.2

4 August 2015: Wouter
	- Document that local-zone nodefault matches exactly and transparent
	  can be used to release a subzone.

3 August 2015: Wouter
	- Document in the manual more text about configuring locally served
	  zones.
	- Fix 5011 anchor update timer after reload.
	- Fix mktime in unbound-anchor not using UTC.

30 July 2015: Wouter
	- please afl-gcc (llvm) for uninitialised variable warning.
	- Added permit-small-holddown config to debug fast 5011 rollover.

24 July 2015: Wouter
	- Fix #690: Reload fails when so-reuseport is yes after changing
	  num-threads.
	- iana portlist update.

21 July 2015: Wouter
	- Fix configure to detect SSL_CTX_set_ecdh_auto.
	- iana portlist update.

20 July 2015: Wouter
	- Enable ECDHE for servers.  Where available, use
	  SSL_CTX_set_ecdh_auto() for TLS-wrapped server configurations to
	  enable ECDHE.  Otherwise, manually offer curve p256.
	  Client connections should automatically use ECDHE when available.
	  (thanks Daniel Kahn Gillmor)

18 July 2015: Willem
	- Allow certificate chain files to allow for intermediate certificates.
	  (thanks Daniel Kahn Gillmor)

13 July 2015: Wouter
	- makedist produces sha1 and sha256 files for created binaries too.

9 July 2015: Wouter
	- 1.5.4 release tag
	- trunk has 1.5.5 in development.
	- Fix #681: Setting forwarders with unbound-control forward
	  implicitly turns on forward-first.

29 June 2015: Wouter
	- iana portlist update.
	- Fix alloc with log for allocation size checks.

26 June 2015: Wouter
	- Fix #677 Fix DNAME responses from cache that failed internal chain
	  test.
	- iana portlist update.

22 June 2015: Wouter
	- Fix #677 Fix CNAME corresponding to a DNAME was checked incorrectly
	  and was therefore always synthesized (thanks to Valentin Dietrich).

4 June 2015: Wouter
	- RFC 7553 RR type URI support, is now enabled by default.

2 June 2015: Wouter
	- Fix #674: Do not free pointers given by getenv.

29 May 2015: Wouter
	- Fix that unparseable error responses are ratelimited.
	- SOA negative TTL is capped at minimumttl in its rdata section.
	- cache-max-negative-ttl config option, default 3600.

26 May 2015: Wouter
	- Document that ratelimit works with unbound-control set_option.

21 May 2015: Wouter
	- iana portlist update.
	- documentation proposes ratelimit of 1000 (closer to what upstream
	  servers expect from us).

20 May 2015: Wouter
	- DLV is going to be decommissioned.  Advice to stop using it, and
	  put text in the example configuration and man page to that effect.

10 May 2015: Wouter
	- Change syntax of particular validator error to be easier for
	  machine parse, swap rrset and ip adres info so it looks like:
	  validation failure <www.example.nl. TXT IN>: signature crypto
	  failed from 2001:DB8:7:bba4::53 for <*.example.nl. NSEC IN>

1 May 2015: Wouter
	- caps-whitelist in unbound.conf allows whitelist of loadbalancers
	  that cannot work with caps-for-id or its fallback.

30 April 2015: Wouter
	- Unit test for type ANY synthesis.

22 April 2015: Wouter
	- Removed contrib/unbound_unixsock.diff, because it has been
	  integrated, use control-interface: /path in unbound.conf.
	- iana portlist update.

17 April 2015: Wouter
	- Synthesize ANY responses from cache.  Does not search exhaustively,
	  but MX,A,AAAA,SOA,NS also CNAME.
	- Fix leaked dns64prefix configuration string.

16 April 2015: Wouter
	- Add local-zone type inform_deny, that logs query and drops answer.
	- Ratelimit does not apply to prefetched queries, and ratelimit-factor
	  is default 10.  Repeated normal queries get resolved and with
	  prefetch stay in the cache.
	- Fix bug#664: libunbound python3 related fixes (from Tomas Hozza)
	  Use print_function also for Python2.
	  libunbound examples: produce sorted output.
	  libunbound-Python: libldns is not used anymore.
	  Fix issue with Python 3 mapping of FILE* using file_py3.i from ldns.

10 April 2015: Wouter
	- unbound-control ratelimit_list lists high rate domains.
	- ratelimit feature, ratelimit: 100, or some sensible qps, can be
	  used to turn it on.  It ratelimits recursion effort per zone.
	  For particular names you can configure exceptions in unbound.conf.
	- Fix that get_option for cache-sizes does not print double newline.
	- Fix#663: ssl handshake fails when using unix socket because dh size
	  is too small.

8 April 2015: Wouter
	- Fix crash in dnstap: Do not try to log TCP responses after timeout.

7 April 2015: Wouter
	- Libunbound skips dos-line-endings from etc/hosts.
	- Unbound exits with a fatal error when the auto-trust-anchor-file
	  fails to be writable.  This is seconds after startup.  You can
	  load a readonly auto-trust-anchor-file with trust-anchor-file.
	  The file has to be writable to notice the trust anchor change,
	  without it, a trust anchor change will be unnoticed and the system
	  will then become inoperable.
	- unbound-control list_insecure command shows the negative trust
	  anchors currently configured, patch from Jelte Jansen.

2 April 2015: Wouter
	- Fix #660: Fix interface-automatic broken in the presence of
	  asymmetric routing.

26 March 2015: Wouter
	- remote.c probedelay line is easier to read.
	- rename ldns subdirectory to sldns to avoid name collision.

25 March 2015: Wouter
	- Fix #657:  libunbound(3) recommends deprecated
	  CRYPTO_set_id_callback.
	- If unknown trust anchor algorithm, and libressl is used, error
	  message encourages upgrade of the libressl package.

23 March 2015: Wouter
	- Fix segfault on user not found at startup (from Maciej Soltysiak).

20 March 2015: Wouter
	- Fixed to add integer overflow checks on allocation (defense in depth).

19 March 2015: Wouter
	- Add ip-transparent config option for bind to non-local addresses.

17 March 2015: Wouter
	- Use reallocarray for integer overflow protection, patch submitted
	  by Loganaden Velvindron.

16 March 2015: Wouter
	- Fixup compile on cygwin, more portable openssl thread id.

12 March 2015: Wouter
	- Updated default keylength in unbound-control-setup to 3k.

10 March 2015: Wouter
	- Fix lintian warning in unbound-checkconf man page (from Andreas
	  Schulze).
	- print svnroot when building windows dist.
	- iana portlist update.
	- Fix warning on sign compare in getentropy_linux.

9 March 2015: Wouter
	- Fix #644: harden-algo-downgrade option, if turned off, fixes the
	  reported excessive validation failure when multiple algorithms
	  are present.  It allows the weakest algorithm to validate the zone.
	- iana portlist update.

5 March 2015: Wouter
	- contrib/unbound_smf22.tar.gz: Solaris SMF installation/removal
	  scripts.  Contributed by Yuri Voinov.
	- Document that incoming-num-tcp increase is good for large servers.
	- stats reports tcp usage, of incoming-num-tcp buffers.

4 March 2015: Wouter
	- Patch from Brad Smith that syncs compat/getentropy_linux with
	  OpenBSD's version (2015-03-04).
	- 0x20 fallback improved: servfail responses do not count as missing
	  comparisons (except if all responses are errors),
	  inability to find nameservers does not fail equality comparisons,
	  many nameservers does not try to compare more than max-sent-count,
	  parse failures start 0x20 fallback procedure.
	- store caps_response with best response in case downgrade response
	  happens to be the last one.
	- Document windows 8 tests.

3 March 2015: Wouter
	- tag 1.5.3rc1
	[ This became 1.5.3 on 10 March, trunk is 1.5.4 in development ]

2 March 2015: Wouter
	- iana portlist update.

20 February 2015: Wouter
	- Use the getrandom syscall introduced in Linux 3.17 (from Heiner
	  Kallweit).
	- Fix #645 Portability to Solaris 10, use AF_LOCAL.
	- Fix #646 Portability to Solaris, -lrt for getentropy_solaris.
	- Fix #647 crash in 1.5.2 because pwd.db no longer accessible after
	  reload.

19 February 2015: Wouter
	- 1.5.2 release tag.
	- svn trunk contains 1.5.3 under development.

13 February 2015: Wouter
	- Fix #643: doc/example.conf.in: unnecessary whitespace.

12 February 2015: Wouter
	- tag 1.5.2rc1

11 February 2015: Wouter
	- iana portlist update.

10 February 2015: Wouter
	- Fix scrubber with harden-glue turned off to reject NS (and other
	  not-address) records.

9 February 2015: Wouter
	- Fix validation failure in case upstream forwarder (ISC BIND) does
	  not have the same trust anchors and decides to insert unsigned NS
	  record in authority section.

2 February 2015: Wouter
	- infra-cache-min-rtt patch from Florian Riehm, for expected long
	  uplink roundtrip times.

30 January 2015: Wouter
	- Fix 0x20 capsforid fallback to omit gratuitous NS and additional
	  section changes.
	- Portability fix for Solaris ('sun' is not usable for a variable).

29 January 2015: Wouter
	- Fix pyunbound byte string representation for python3.

26 January 2015: Wouter
	- Fix unintended use of gcc extension for incomplete enum types,
	  compile with pedantic c99 compliance (from Daniel Dickman).

23 January 2015: Wouter
	- windows port fixes, no AF_LOCAL, no chown, no chmod(grp).

16 January 2015: Wouter
	- unit test for local unix connection.  Documentation and log_addr
	  does not inspect port for AF_LOCAL.
	- unbound-checkconf -f prints chroot with pidfile path.

13 January 2015: Wouter
	- iana portlist update.

12 January 2015: Wouter
	- Cast sun_len sizeof to socklen_t.
	- Fix pyunbound ord call, portable for python 2 and 3.

7 January 2015: Wouter
	- Fix warnings in pythonmod changes.

6 January 2015: Wouter
	- iana portlist update.
	- patch for remote control over local sockets, from Dag-Erling
	  Smorgrav, Ilya Bakulin.  Use control-interface: /path/sock and
	  control-use-cert: no.
	- Fixup that patch and uid lookup (only for daemon).
	- coded the default of control-use-cert, to yes.

5 January 2015: Wouter
	- getauxval test for ppc64 linux compatibility.
	- make strip works for unbound-host and unbound-anchor.
	- patch from Stephane Lapie that adds to the python API, that
	  exposes struct delegpt, and adds the find_delegation function.
	- print query name when max target count is exceeded.
	- patch from Stuart Henderson that fixes DESTDIR in
	  unbound-control-setup for installs where config is not in
	  the prefix location.
	- Fix #634: fix fail to start on Linux LTS 3.14.X, ignores missing
	  IP_MTU_DISCOVER OMIT option (fix from Remi Gacogne).
	- Updated contrib warmup.cmd/sh to support two modes - load
	  from pre-defined list of domains or (with filename as argument)
	  load from user-specified list of domains, and updated contrib
	  unbound_cache.sh/cmd to support loading/save/reload cache to/from
	  default path or (with secondary argument) arbitrary path/filename,
	  from Yuri Voinov.
	- Patch from Philip Paeps to contrib/unbound_munin_ that uses
	  type ABSOLUTE.  Allows munin.conf: [idleserver.example.net]
	  unbound_munin_hits.graph_period minute

9 December 2014: Wouter
	- svn trunk has 1.5.2 in development.
	- config.guess and config.sub update from libtoolize.
	- local-zone: example.com inform makes unbound log a message with
	  client IP for queries in that zone.  Eg. for finding infected hosts.

8 December 2014: Wouter
	- Fix CVE-2014-8602: denial of service by making resolver chase
	  endless series of delegations.

1 December 2014: Wouter
	- Fix bug#632: unbound fails to build on AArch64, protects
	  getentropy compat code from calling sysctl if it is has been removed.

29 November 2014: Wouter
	- Add include to getentropy_linux.c, hopefully fixing debian build.

28 November 2014: Wouter
	- Fix makefile for build from noexec source tree.

26 November 2014: Wouter
	- Fix libunbound undefined symbol errors for main.
	  Referencing main does not seem to be possible for libunbound.

24 November 2014: Wouter
	- Fix log at high verbosity and memory allocation failure.
	- iana portlist update.

21 November 2014: Wouter
	- Fix crash on multiple thread random usage on systems without
	  arc4random.

20 November 2014: Wouter
	- fix compat/getentropy_win.c check if CryptGenRandom works and no
	  immediate exit on windows.

19 November 2014: Wouter
	- Fix cdflag dns64 processing.

18 November 2014: Wouter
	- Fix that CD flag disables DNS64 processing, returning the DNSSEC
	  signed AAAA denial.
	- iana portlist update.

17 November 2014: Wouter
	- Fix #627: SSL_CTX_load_verify_locations return code not properly
	  checked.

14 November 2014: Wouter
	- parser with bison 2.7

13 November 2014: Wouter
	- Patch from Stephane Lapie for ASAHI Net that implements aaaa-filter,
	added to contrib/aaaa-filter-iterator.patch.

12 November 2014: Wouter
	- trunk has 1.5.1 in development.
	- Patch from Robert Edmonds to build pyunbound python module
	  differently.  No versioninfo, with -shared and without $(LIBS).
	- Patch from Robert Edmonds fixes hyphens in unbound-anchor man page.
	- Removed 'increased limit open files' log message that is written
	  to console.  It is only written on verbosity 4 and higher.
	  This keeps system bootup console cleaner.
	- Patch from James Raftery, always print stats for rcodes 0..5.

11 November 2014: Wouter
	- iana portlist update.
	- Fix bug where forward or stub addresses with same address but
	  different port number were not tried.
	- version number in svn trunk is 1.5.0
	- tag 1.5.0rc1
	- review fix from Ralph.

7 November 2014: Wouter
	- dnstap fixes by Robert Edmonds:
		dnstap/dnstap.m4: cosmetic fixes
		dnstap/: Remove compiled protoc-c output files
		dnstap/dnstap.m4: Error out if required libraries are not found
		dnstap: Fix ProtobufCBufferSimple usage that is incorrect as of
			protobuf-c 1.0.0
		dnstap/: Adapt to API changes in latest libfstrm (>= 0.2.0)

4 November 2014: Wouter
	- Add ub_ctx_add_ta_autr function to add a RFC5011 automatically
	  tracked trust anchor to libunbound.
	- Redefine internal minievent symbols to unique symbols that helps
	  linking on platforms where the linker leaks names across modules.

27 October 2014: Wouter
	- Disabled use of SSLv3 in remote-control and ssl-upstream.
	- iana portlist update.

16 October 2014: Wouter
	- Documented dns64 configuration in unbound.conf man page.

13 October 2014: Wouter
	- Fix #617: in ldns in unbound, lowercase WKS services.
	- Fix ctype invocation casts.

10 October 2014: Wouter
	- Fix unbound-checkconf check for module config with dns64 module.
	- Fix unbound capsforid fallback, it ignores TTLs in comparison.

6 October 2014: Wouter
	- Fix #614: man page variable substitution bug.
6 October 2014: Willem
	- Whitespaces after $ORIGIN are not part of the origin dname (ldns).
	- $TTL's value starts at position 5 (ldns).

1 October 2014: Wouter
	- fix #613: Allow tab ws in var length last rdfs (in ldns str2wire).

29 September 2014: Wouter
	- Fix #612: create service with service.conf in present directory and
	  auto load it.
	- Fix for mingw compile openssl ranlib.

25 September 2014: Wouter
	- updated configure and aclocal with newer autoconf 1.13.

22 September 2014: Wouter
	- Fix swig and python examples for Python 3.x.
	- Fix for mingw compile with openssl-1.0.1i.

19 September 2014: Wouter
	- improve python configuration detection to build on Fedora 22.

18 September 2014: Wouter
	- patches to also build with Python 3.x (from Pavel Simerda).

16 September 2014: Wouter
	- Fix tcp timer waiting list removal code.
	- iana portlist update.
	- Updated the TCP_BACLOG from 5 to 256, so that the tcp accept queue
	  is longer and more tcp connections can be handled.

15 September 2014: Wouter
	- Fix unit test for CDS typecode.

5 September 2014: Wouter
	- type CDS and CDNSKEY types in sldns.

25 August 2014: Wouter
	- Fixup checklock code for log lock and its mutual initialization
	  dependency.
	- iana portlist update.
	- Removed necessity for pkg-config from the dnstap.m4, new are
	  the --with-libfstrm and --with-protobuf-c configure options.

19 August 2014: Wouter
	- Update unbound manpage with more explanation (from Florian Obser).

18 August 2014: Wouter
	- Fix #603: unbound-checkconf -o <option> should skip verification
	  checks.
	- iana portlist update.
	- Fixup doc/unbound.doxygen to remove obsolete 1.8.7 settings.

5 August 2014: Wouter
	- dnstap support, with a patch from Farsight Security, written by
	  Robert Edmonds. The --enable-dnstap needs libfstrm and protobuf-c.
	  It is BSD licensed (see dnstap/dnstap.c).
	  Building with --enable-dnstap needs pkg-config with this patch.
	- Noted dnstap in doc/README and doc/CREDITS.
	- Changes to the dnstap patch.
	  - lint fixes.
	  - dnstap/dnstap_config.h should not have been added to the repo,
	    because is it generated.

1 August 2014: Wouter
	- Patch add msg, rrset, infra and key cache sizes to stats command
	  from Maciej Soltysiak.
	- iana portlist update.

31 July 2014: Wouter
	- DNS64 from Viagenie (BSD Licensed), written by Simon Perrault.
	  Initial commit of the patch from the FreeBSD base (with its fixes).
	  This adds a module (for module-config in unbound.conf) dns64 that
	  performs DNS64 processing, see README.DNS64.
	- Changes from DNS64:
	  strcpy changed to memmove.
	  arraybound check fixed from prefix_net/8/4 to prefix_net/8+4.
	  allocation of result consistently in the correct region.
	  time_t is now used for ttl in unbound (since the patch's version).
	- testdata/dns64_lookup.rpl for unit test for dns64 functionality.

29 July 2014: Wouter
	- Patch from Dag-Erling Smorgrav that implements feature, unbound -dd
	  does not fork in the background and also logs to stderr.

21 July 2014: Wouter
	- Fix endian.h include for OpenBSD.

16 July 2014: Wouter
	- And Fix#596: Bail out of unbound-control dump_infra when ssl
	  write fails.

15 July 2014: Wouter
	- Fix #596: Bail out of unbound-control list_local_zones when ssl
	  write fails.
	- iana portlist update.

13 July 2014: Wouter
	- Configure tests if main can be linked to from getentropy compat.

12 July 2014: Wouter
	- Fix getentropy compat code, function refs were not portable.
	- Fix to check openssl version number only for OpenSSL.
	- LibreSSL provides compat items, check for that in configure.
	- Fix bug in fix for log locks that caused deadlock in signal handler.
	- update compat/getentropy and arc4random to the most recent ones from OpenBSD.

11 July 2014: Matthijs
	- fake-rfc2553 patch (thanks Benjamin Baier).

11 July 2014: Wouter
	- arc4random in compat/ and getentropy, explicit_bzero, chacha for
	  dependencies, from OpenBSD.  arc4_lock and sha512 in compat.
	  This makes arc4random available on all platforms, except when
	  compiled with LIBNSS (it uses libNSS crypto random).
	- fix strptime implicit declaration error on OpenBSD.
	- arc4random, getentropy and explicit_bzero compat for Windows.

4 July 2014: Wouter
	- Fix #593: segfault or crash upon rotating logfile.

3 July 2014: Wouter
	- DLV tests added.
	- signit tool fixup for compile with libldns library.
	- iana portlist updated.

27 June 2014: Wouter
	- so-reuseport is available on BSDs(such as FreeBSD 10) and OS/X.

26 June 2014: Wouter
	- unbound-control status reports if so-reuseport was successful.
	- iana portlist updated.

24 June 2014: Wouter
	- Fix caps-for-id fallback, and added fallback attempt when servers
	  drop 0x20 perturbed queries.
	- Fixup testsetup for VM tests (run testcode/run_vm.sh).

17 June 2014: Wouter
	- iana portlist updated.

3 June 2014: Wouter
	- Add AAAA for B root server to default root hints.

2 June 2014: Wouter
	- Remove unused define from iterator.h

30 May 2014: Wouter
	- Fixup sldns_enum_edns_option typedef definition.

28 May 2014: Wouter
	- Code cleanup patch from Dag-Erling Smorgrav, with compiler issue
	  fixes from FreeBSD's copy of Unbound, he notes:
	  Generate unbound-control-setup.sh at build time so it respects
	  prefix and sysconfdir from the configure script.  Also fix the
	  umask to match the comment, and the comment to match the umask.
	  Add const and static where needed.  Use unions instead of
	  playing pointer poker.  Move declarations that are needed in
	  multiple source files into a shared header.  Move sldns_bgetc()
	  from parse.c to buffer.c where it belongs.  Introduce a new
	  header file, worker.h, which declares the callbacks that
	  all workers must define.  Remove those declarations from
	  libworker.h.	Include the correct headers in the correct places.
	  Fix a few dummy callbacks that don't match their prototype.
	  Fix some casts.  Hide the sbrk madness behind #ifdef HAVE_SBRK.
	  Remove a useless printf which breaks reproducible builds.
	  Get rid of CONFIGURE_{TARGET,DATE,BUILD_WITH} now that they're
	  no longer used.  Add unbound-control-setup.sh to the list of
	  generated files.  The prototype for libworker_event_done_cb()
	  needs to be moved from libunbound/libworker.h to
	  libunbound/worker.h.
	- Fixup out-of-directory compile with unbound-control-setup.sh.in.
	- make depend.

23 May 2014: Wouter
	- unbound-host -D enabled dnssec and reads root trust anchor from
	  the default root key file that was compiled in.

20 May 2014: Wouter
	- Feature, unblock-lan-zones: yesno that you can use to make unbound
	  perform 10.0.0.0/8 and other reverse lookups normally, for use if
	  unbound is running service for localhost on localhost.

16 May 2014: Wouter
	- Updated create_unbound_ad_servers and unbound_cache scripts from
	  Yuri Voinov in the source/contrib directory. Added
	  warmup.cmd (and .sh): warm up the DNS cache with your MRU domains.

9 May 2014: Wouter
	- Implement draft-ietf-dnsop-rfc6598-rfc6303-01.
	- iana portlist updated.

8 May 2014: Wouter
	- Contrib windows scripts from Yuri Voinov added to src/contrib:
	  create_unbound_ad_servers.cmd: enters anti-ad server lists.
	  unbound_cache.cmd: saves and loads the cache.
	- Added unbound-control-setup.cmd from Yuri Voinov to the windows
	  unbound distribution set.  It requires openssl installed in %PATH%.

6 May 2014: Wouter
	- Change MAX_SENT_COUNT from 16 to 32 to resolve some cases easier.

5 May 2014: Wouter
	- More #567: remove : from output of stub and forward lists, this is
	  easier to parse.

29 April 2014: Wouter
	- iana portlist updated.
	- Add unbound-control flush_negative that flushed nxdomains, nodata,
	  and errors from the cache.  For dnssec-trigger and NetworkManager,
	  fixes cases where network changes have localdata that was already
	  negatively cached from the previous network.

23 April 2014: Wouter
	- Patch from Jeremie Courreges-Anglas to use arc4random_uniform
	  if available on the OS, it gets entropy from the OS.

15 April 2014: Wouter
	- Fix compile with libevent2 on FreeBSD.

11 April 2014: Wouter
	- Fix #502: explain that do-ip6 disable does not stop AAAA lookups,
	  but it stops the use of the ipv6 transport layer for DNS traffic.
	- iana portlist updated.

10 April 2014: Wouter
	- iana portlist updated.
	- Patch from Hannes Frederic Sowa for Linux 3.15 fragmentation
	  option for DNS fragmentation defense.
	- Document that dump_requestlist only prints queries from thread 0.
	- unbound-control stats prints num.query.tcpout with number of TCP
	  outgoing queries made in the previous statistics interval.
	- Fix #567: unbound lists if forward zone is secure or insecure with
	  +i annotation in output of list_forwards, also for list_stubs
	  (for NetworkManager integration.)
	- Fix #554: use unsigned long to print 64bit statistics counters on
	  64bit systems.
	- Fix #558: failed prefetch lookup does not remove cached response
	  but delays next prefetch (in lieu of caching a SERVFAIL).
	- Fix #545: improved logging, the ip address of the error is printed
	  on the same log-line as the error.

8 April 2014: Wouter
	- Fix #574: make test fails on Ubuntu 14.04.  Disabled remote-control
	  in testbound scripts.
	- iana portlist updated.

7 April 2014: Wouter
	- C.ROOT-SERVERS.NET has an IPv6 address, and we updated the root
	  hints (patch from Anand Buddhdev).
	- Fix #572: Fix unit test failure for systems with different
	  /etc/services.

28 March 2014: Wouter
	- Fix #569: do_tcp is do-tcp in unbound.conf man page.

25 March 2014: Wouter
	- Patch from Stuart Henderson to build unbound-host man from .1.in.

24 March 2014: Wouter
	- Fix print filename of encompassing config file on read failure.

12 March 2014: Wouter
	- tag 1.4.22
	- trunk has 1.4.23 in development.

10 March 2014: Wouter
	- Fix bug#561: contrib/cacti plugin did not report SERVFAIL rcodes
	  because of spelling.  Patch from Chris Coates.

27 February 2014: Wouter
	- tag 1.4.22rc1

21 February 2014: Wouter
	- iana portlist updated.

20 February 2014: Matthijs
	- Be lenient when a NSEC NameError response with RCODE=NXDOMAIN is
	  received. This is okay according 4035, but not after revising
	  existence in 4592.  NSEC empty non-terminals exist and thus the
	  RCODE should have been NOERROR. If this occurs, and the RRsets
	  are secure, we set the RCODE to NOERROR and the security status
	  of the response is also considered secure.

14 February 2014: Wouter
	- Works on Minix (3.2.1).

11 February 2014: Wouter
	- Fix parse of #553(NSD) string in sldns, quotes without spaces.

7 February 2014: Wouter
	- iana portlist updated.
	- add body to ifstatement if locks disabled.
	- add TXT string"string" test case to unit test.
	- Fix #551: License change "Regents" to "Copyright holder", matching
	  the BSD license on opensource.org.

6 February 2014: Wouter
	- sldns has type HIP.
	- code documentation on the module interface.

5 February 2014: Wouter
	- Fix sldns parse tests on osx.

3 February 2014: Wouter
	- Detect libevent2 install automatically by configure.
	- Fixup link with lib/event2 subdir.
	- Fix parse in sldns of quoted parenthesized text strings.

31 January 2014: Wouter
	- unit test for ldns wire to str and back with zones, root, nlnetlabs
	  and types.sidnlabs.
	- Fix for hex to string in unknown, atma and nsap.
	- fixup nss compile (no ldns in it).
	- fixup warning in unitldns
	- fixup WKS and rdata type service to print unsigned because strings
	  are not portable; they cannot be read (for sure) on other computers.
	- fixup type EUI48 and EUI64, type APL and type IPSECKEY in string
	  parse sldns.

30 January 2014: Wouter
	- delay-close does not act if there are udp-wait queries, so that
	  it does not make a socketdrain DoS easier.

28 January 2014: Wouter
	- iana portlist updated.
	- iana portlist test updated so it does not touch the source
	  if there are no changes.
	- delay-close: msec option that delays closing ports for which
	  the UDP reply has timed out.  Keeps the port open, only accepts
	  the correct reply.  This correct reply is not used, but the port
	  is open so that no port-denied ICMPs are generated.

27 January 2014: Wouter
	- reuseport is attempted, then fallback to without on failure.

24 January 2014: Wouter
	- Change unbound-event.h to use void* buffer, length idiom.
	- iana portlist updated.
	- unbound-event.h is installed if you configure --enable-event-api.
	- speed up unbound (reports say it could be up to 10%), by reducing
	  lock contention on localzones.lock.  It is changed to an rwlock.
	- so-reuseport: yesno option to distribute queries evenly over
	  threads on Linux (Thanks Robert Edmonds).
	- made lint clean.

21 January 2014: Wouter
	- Fix #547: no trustanchor written if filesystem full, fclose checked.

17 January 2014: Wouter
	- Fix isprint() portability in sldns, uses unsigned int.
	- iana portlist updated.

16 January 2014: Wouter
	- fix #544: Fixed +i causes segfault when running with module conf
	  "iterator".
	- Windows port, adjust %lld to %I64d, and warning in win_event.c.

14 January 2014: Wouter
	- iana portlist updated.

5 Dec 2013: Wouter
	- Fix bug in cachedump that uses sldns.
	- update pythonmod for ldns_ to sldns_ name change.

3 Dec 2013: Wouter
	- Fix sldns to use sldns_ prefix for all ldns_ variables.
	- Fix windows compile to compile with sldns.

30 Nov 2013: Wouter
	- Fix sldns to make globals use sldns_ prefix.  This fixes
	  linking with libldns that uses global variables ldns_ .

13 Nov 2013: Wouter
	- Fix bug#537: compile python plugin without ldns library.

12 Nov 2013: Wouter
	- Fix bug#536: acl_deny_non_local and refuse_non_local added.

5 Nov 2013: Wouter
	- Patch from Neel Goyal to fix async id assignment if callback
	  is called by libunbound in the mesh attach.
	- Accept ip-address: as an alternative for interface: for
	  consistency with nsd.conf syntax.

4 Nov 2013: Wouter
	- Patch from Neel Goyal to fix callback in libunbound.

3 Nov 2013: Wouter
	- if configured --with-libunbound-only fix make install.

31 Oct 2013: Wouter
	- Fix #531: Set SO_REUSEADDR so that the wildcard interface and a 
	  more specific interface port 53 can be used at the same time, and
	  one of the daemons is unbound.
	- iana portlist update.
	- separate ldns into core ldns inside ldns/ subdirectory.  No more
	  --with-ldns is needed and unbound does not rely on libldns.
	- portability fixes for new USE_SLDNS ldns subdir codebase.

22 Oct 2013: Wouter
	- Patch from Neel Goyal: Add an API call to set an event base on an
	  existing ub_ctx.  This basically just destroys the current worker and
	  sets the event base to the current.  And fix a deadlock in
	  ub_resolve_event – the cfglock is held when libworker_create is
	  called.  This ends up trying to acquire the lock again in
	  context_obtain_alloc in the call chain.
	- Fix #528: if very high logging (4 or more) segfault on allow_snoop.

26 Sep 2013: Wouter
	- unbound-event.h is installed if configured --with-libevent.  It
	  contains low-level library calls, that use libevent's event_base
	  and an ldns_buffer for the wire return packet to perform async
	  resolution in the client's eventloop.

19 Sep 2013: Wouter
	- 1.4.21 tag created.
	- trunk has 1.4.22 number inside it.
	- iana portlist updated.
	- acx_nlnetlabs.m4 to 26; improve FLTO help text.

16 Sep 2013: Wouter
	- Fix#524: max-udp-size not effective to non-EDNS0 queries, from
	  Daisuke HIGASHI.

10 Sep 2013: Wouter
	- MIN_TTL and MAX_TTL also in time_t.
	- tag 1.4.21rc1 made again.

26 Aug 2013: Wouter
	- More fixes for bug#519: for the threaded case test if the bg
	  thread has been killed, on ub_ctx_delete, to avoid hangs.

22 Aug 2013: Wouter
	- more fixes that I overlooked.
	- review fixes from Willem.

21 Aug 2013: Wouter
	- Fix#520: Errors found by static analysis from Tomas Hozza(redhat).

20 Aug 2013: Wouter
	- Fix for 2038, with time_t instead of uint32_t.

19 Aug 2013: Wouter
	- Fix#519 ub_ctx_delete may hang in some scenarios (libunbound).

14 Aug 2013: Wouter
	- Fix uninit variable in fix#516.

8 Aug 2013: Wouter
	- Fix#516 dnssec lameness detection for answers that are improper.

30 Jun 2013: Wouter
	- tag 1.4.21rc1

29 Jun 2013: Wouter
	- Fix#512 memleak in testcode for testbound (if it fails).
	- Fix#512 NSS returned arrays out of setup function to be statics.

26 Jun 2013: Wouter
	- max include of 100.000 files (depth and globbed at one time).
	  This is to preserve system memory in bug cases, or endless cases.
	- iana portlist updated.

19 Jun 2013: Wouter
	- streamtcp man page, contributed by Tomas Hozza.
	- iana portlist updated.
	- libunbound documentation on how to avoid openssl race conditions.

25 Jun 2013: Wouter
	- Squelch sendto-permission denied errors when the network is
	  not connected, to avoid spamming syslog.
	- configure --disable-flto option (from Robert Edmonds).

18 Jun 2013: Wouter
	- Fix for const string literals in C++ for libunbound, from Karel
	  Slany.
	- iana portlist updated.

17 Jun 2013: Wouter
	- Fixup manpage syntax.

14 Jun 2013: Wouter
	- get_option and set_option support for log-time-ascii, python-script
	  val-sig-skew-min and val-sig-skew-max.  log-time-ascii takes effect
	  immediately.  The others are mostly useful for libunbound users.

13 Jun 2013: Wouter
	- get_option, set_option, unbound-checkconf -o and libunbound
	  getoption and setoption support cache-min-ttl and cache-max-ttl.

10 Jun 2013: Wouter
	- Fix#501: forward-first does not recurse, when forward name is ".".
	- iana portlist update.
	- Max include depth is unlimited.

27 May 2013: Wouter
	- Update acx_pthreads.m4 to ax_pthreads.4 (2013-03-29), and apply
	  patch to it to not fail when -Werror is also specified, from the
	  autoconf-archives.
	- iana portlist update.

21 May 2013: Wouter
	- Explain bogus and secure flags in libunbound more.

16 May 2013: Wouter
	- Fix#499 use-after-free in out-of-memory handling code (thanks Jake
	  Montgomery).
	- Fix#500 use on non-initialised values on socket bind failures.

15 May 2013: Wouter
	- Fix round-robin doesn't work with some Windows clients (from Ilya
	  Bakulin).

3 May 2013: Wouter
	- update acx_nlnetlabs.m4 to v23, sleep w32 fix.

26 April 2013: Wouter
	- add unbound-control insecure_add and insecure_remove for the
	  administration of negative trust anchors.

25 April 2013: Wouter
	- Implement max-udp-size config option, default 4096 (thanks
	  Daisuke Higashi).
	- Robust checks on dname validity from rdata for dname compare.
	- updated iana portlist.

19 April 2013: Wouter
	- Fixup snprintf return value usage, fixed libunbound_get_option.

18 April 2013: Wouter
	- fix bug #491: pick program name (0th argument) as syslog identity.
	- own implementation of compat/snprintf.c.

15 April 2013: Wouter
	- Fix so that for a configuration line of include: "*.conf" it is not
	  an error if there are no files matching the glob pattern.
	- unbound-anchor review: BIO_write can return 0 successfully if it
	  has successfully appended a zero length string.

11 April 2013: Wouter
	- Fix queries leaking up for stubs and forwards, if the configured
	  nameservers all fail to answer.

10 April 2013: Wouter
	- code improve for minimal responses, small speed increase.

9 April 2013: Wouter
	- updated iana portlist.
	- Fix crash in previous private address fixup of 22 March.

28 March 2013: Wouter
	- Make reverse zones easier by documenting the nodefault statements
	  commented-out in the example config file.

26 March 2013: Wouter
	- more fixes to lookup3.c endianness detection.

25 March 2013: Wouter
	- #492: Fix endianness detection, revert to older lookup3.c detection
	  and put new detect lines after previous tests, to avoid regressions
	  but allow new detections to succeed.
	  And add detection for machine/endian.h to it.

22 March 2013: Wouter
	- Fix resolve of names that use a mix of public and private addresses.
	- iana portlist update.
	- Fix makedist for new svn for -d option.
	- unbound.h header file has UNBOUND_VERSION_MAJOR define.
	- Fix windows RSRC version for long version numbers.

21 March 2013: Wouter
	- release 1.4.20
	- trunk has 1.4.21
	- committed libunbound version 4:1:2 for binary API updated in 1.4.20
	- install copy of unbound-control.8 man page for unbound-control-setup

14 March 2013: Wouter
	- iana portlist update.
	- tag 1.4.20rc1

12 March 2013: Wouter
	- Fixup makedist.sh for windows compile.

11 March 2013: Wouter
	- iana portlist update.
	- testcode/ldns-testpkts.c check for makedist is informational.

15 February 2013: Wouter
	- fix defines in lookup3 for bigendian bsd alpha

11 February 2013: Wouter
	- Fixup openssl_thread init code to only run if compiled with SSL.

7 February 2013: Wouter
	- detect endianness in lookup3 on BSD.
	- add libunbound.ttl at end of result structure, version bump for
	  libunbound and binary backwards compatible, but 1.4.19 is not
	  forward compatible with 1.4.20.
	- update iana port list.

30 January 2013: Wouter
	- includes and have_ssl fixes for nss.

29 January 2013: Wouter
	- printout name of zone with duplicate fwd and hint errors.

28 January 2013: Wouter
	- updated fwd_zero for newer nc. Updated common.sh for newer netstat.

17 January 2013: Wouter
	- unbound-anchors checks the emailAddress of the signer of the
	  root.xml file, <NAME_EMAIL>.  It also checks that
	  the signer has the correct key usage for a digital signature.
	- update iana port list.

3 January 2013: Wouter
	- Test that unbound-control checks client credentials.
	- Test that unbound can handle a CNAME at an intermediate node in
	  the chain of trust (where it seeks a DS record).
	- Check the commonName of the signer of the root.xml file in
	  unbound-anchor, <NAME_EMAIL>.

2 January 2013: Wouter
	- Fix openssl lock free on exit (reported by Robert Fleischman).
	- iana portlist updated.
	- Tested that unbound implements the RFC5155 Technical Errata id 3441.
	  Unbound already implements insecure classification of an empty
	  nonterminal in NSEC3 optout zone.

20 December 2012: Wouter
	- Fix unbound-anchor xml parse of entity declarations for safety.

19 December 2012: Wouter
	- iana portlist updated.

18 December 2012: Wouter
	- iana portlist updated.

14 December 2012: Wouter
	- Change of D.ROOT-SERVERS.NET A address in default root hints.

12 December 2012: Wouter
	- 1.4.19 release.
	- trunk has 1.4.20 under development.

5 December 2012: Wouter
	- note support for AAAA RR type RFC.

4 December 2012: Wouter
	- 1.4.19rc1 tag.

30 November 2012: Wouter
	- bug 481: fix python example0.
	- iana portlist updated.

27 November 2012: Wouter
	- iana portlist updated.

9 November 2012: Wouter
	- Fix unbound-control forward disables configured stubs below it.

7 November 2012: Wouter
	- Fixup ldns-testpkts, identical to ldns/examples.
	- iana portlist updated.

30 October 2012: Wouter
	- Fix bug #477: unbound-anchor segfaults if EDNS is blocked.

29 October 2012: Matthijs
	- Fix validation for responses with both CNAME and wildcard
	  expanded CNAME records in answer section.

8 October 2012: Wouter
	- update ldns-testpkts.c to ldns 1.6.14 version.
	- fix build of pythonmod in objdir, for unbound.py.
	- make clean and makerealclean remove generated python and docs.

5 October 2012: Wouter
	- fix build of pythonmod in objdir (thanks Jakob Schlyter).

3 October 2012: Wouter
	- fix text in unbound-anchor man page.

1 October 2012: Wouter
	- ignore trusted-keys globs that have no files (from Paul Wouters).

27 September 2012: Wouter
	- include: directive in config file accepts wildcards.  Patch from
	  Paul Wouters.  Suggested use: include: "/etc/unbound.d/conf.d/*"
	- unbound-control -q option is quiet, patch from Mariano Absatz.
	- iana portlist updated.
	- updated contrib/unbound.spec, patch from Valentin Bud.

21 September 2012: Wouter
	- chdir to / after chroot call (suggested by Camiel Dobbelaar).

17 September 2012: Wouter
	- patch_rsamd5_enable.diff: this patch enables RSAMD5 validation
	  otherwise it is treated as insecure.  The RSAMD5 algorithm is
	  deprecated (RFC6725).  The MD5 hash is considered weak for some
	  purposes, if you want to sign your zone, then RSASHA256 is an
	  uncontested hash.

30 August 2012: Wouter
	- RFC6725 deprecates RSAMD5: this DNSKEY algorithm is disabled.
	- iana portlist updated.

29 August 2012: Wouter
	- Nicer comments outgoing-port-avoid, thanks Stu (bug #465).

22 August 2012: Wouter
	- Fallback to 1472 and 1232, one fragment size without headers.

21 August 2012: Wouter
	- Fix timeouts so that when a server has been offline for a while
	  and is probed to see it works, it becomes fully available for
	  server selection again.

17 August 2012: Wouter
	- Add documentation to libunbound for default nonuse of resolv.conf.

2 August 2012: Wouter
	- trunk has 1.4.19 under development (fixes from 1 aug and 31 july
	are for 1.4.19).
	- iana portlist updated.

1 August 2012: Wouter
	- Fix openssl race condition, initializes openssl locks, reported
	  by Einar Lonn and Patrik Wallstrom.

31 July 2012: Wouter
	- Improved forward-first and stub-first documentation.
	- Fix that enables modules to register twice for the same
	  serviced_query, without race conditions or administration issues.
	  This should not happen with the current codebase, but it is robust.
	- Fix forward-first option where it sets the RD flag wrongly.
	- added manpage links for libunbound calls (Thanks Paul Wouters).

30 July 2012: Wouter
	- tag 1.4.18rc2 (became 1.4.18 release at 2 august 2012).

27 July 2012: Wouter
	- unbound-host works with libNSS
	- fix bogus nodata cname chain not reported as bogus by validator,
	  (Thanks Peter van Dijk).

26 July 2012: Wouter
	- iana portlist updated.
	- tag 1.4.18rc1.

25 July 2012: Wouter
	- review fix for libnss, check hash prefix allocation size.

23 July 2012: Wouter
	- fix missing break for GOST DS hash function.
	- implemented forward_first for the root.

20 July 2012: Wouter
	- Fix bug#452 and another assertion failure in mesh.c, makes
	  assertions in mesh.c resist duplicates.  Fixes DS NS search to
	  not generate duplicate sub queries.

19 July 2012: Willem
	- Fix bug#454: Remove ACX_CHECK_COMPILER_FLAG from configure.ac,
	  if CFLAGS is specified at configure time then '-g -O2' is not
	  appended to CFLAGS, so that the user can override them.

18 July 2012: Willem
	- Fix libunbound report of errors when in background mode.

11 July 2012: Willem
	- updated iana ports list.

9 July 2012: Willem
	- Add flush_bogus option for unbound-control

6 July 2012: Wouter
	- Fix validation of qtype DS queries that result in no data for
	  non-optout NSEC3 zones.

4 July 2012: Wouter
	- compile libunbound with libnss on Suse, passes regression tests.

3 July 2012: Wouter
	- FIPS_mode openssl does not use arc4random but RAND_pseudo_bytes.

2 July 2012: Wouter
	- updated iana ports list.

29 June 2012: Wouter
	- patch for unbound_munin_ script to handle arbitrary thread count by
	  Sven Ulland.

28 June 2012: Wouter
	- detect if openssl has FIPS_mode.
	- code review: return value of cache_store can be ignored for better
	  performance in out of memory conditions.
	- fix edns-buffer-size and msg-buffer-size manpage documentation.
	- updated iana ports list.

25 June 2012: Wouter
	- disable RSAMD5 if in FIPS mode (for openssl and for libnss).

22 June 2012: Wouter
	- implement DS records, NSEC3 and ECDSA for compile with libnss.

21 June 2012: Wouter
	- fix error handling of alloc failure during rrsig verification.
	- nss check for verification failure.
	- nss crypto works for RSA and DSA.

20 June 2012: Wouter
	- work on --with-nss build option (for now, --with-libunbound-only).

19 June 2012: Wouter
	- --with-libunbound-only build option, only builds the library and
	  not the daemon and other tools.

18 June 2012: Wouter
	- code review.

15 June 2012: Wouter
	- implement log-time-ascii on windows.
	- The key-cache bad key ttl is now 60 seconds.
	- updated iana ports list.
	- code review.

11 June 2012: Wouter
	- bug #452: fix crash on assert in mesh_state_attachment.

30 May 2012: Wouter
	- silence warning from swig-generated code (md set but not used in
	  swig initmodule, due to ifdefs in swig-generated code).

27 May 2012: Wouter
	- Fix debian-bugs-658021: Please enable hardened build flags.

25 May 2012: Wouter
	- updated iana ports list.

24 May 2012: Wouter
	- tag for 1.4.17 release.
	- trunk is 1.4.18 in development.

18 May 2012: Wouter
	- Review comments, removed duplicate memset to zero in delegpt.

16 May 2012: Wouter
	- Updated doc/FEATURES with RFCs that are implemented but not listed.
	- Protect if statements in val_anchor for compile without locks.
	- tag for 1.4.17rc1.

15 May 2012: Wouter
	- fix configure ECDSA support in ldns detection for windows compile.
	- fix possible uninitialised variable in windows pipe implementation.

9 May 2012: Wouter
	- Fix alignment problem in util/random on sparc64/freebsd.

8 May 2012: Wouter
	- Fix for accept spinning reported by OpenBSD.
	- iana portlist updated.

2 May 2012: Wouter
	- Fix validation of nodata for DS query in NSEC zones, reported by
	  Ondrej Mikle.

13 April 2012: Wouter
	- ECDSA support (RFC 6605) by default. Use --disable-ecdsa for older
	  openssl.

10 April 2012: Wouter
	- Applied patch from Daisuke HIGASHI for rrset-roundrobin and
	  minimal-responses features.
	- iana portlist updated.

5 April 2012: Wouter
	- fix bug #443: --with-chroot-dir not honoured by configure.
	- fix bug #444: setusercontext was called too late (thanks Bjorn
	  Ketelaars).

27 March 2012: Wouter
	- fix bug #442: Fix that Makefile depends on pythonmod headers
	  even using --without-pythonmodule.

22 March 2012: Wouter
	- contrib/validation-reporter follows rotated log file (patch from
	  Augie Schwer).

21 March 2012: Wouter
	- new approach to NS fetches for DS lookup that works with
	  cornercases, and is more robust and considers forwarders.

19 March 2012: Wouter
	- iana portlist updated.
	- fix to locate nameservers for DS lookup with NS fetches.

16 March 2012: Wouter
	- Patch for access to full DNS packet data in unbound python module
	  from Ondrej Mikle.

9 March 2012: Wouter
	- Applied line-buffer patch from Augie Schwer to validation.reporter.sh.

2 March 2012: Wouter
	- flush_infra cleans timeouted servers from the cache too.
	- removed warning from --enable-ecdsa.

1 March 2012: Wouter
	- forward-first option.  Tries without forward if a query fails.
	  Also stub-first option that is similar.

28 February 2012: Wouter
	- Fix from code review, if EINPROGRESS not defined chain if statement
	  differently.

27 February 2012: Wouter
	- Fix bug#434: on windows check registry for config file location
	  for unbound-control.exe, and unbound-checkconf.exe.

23 February 2012: Wouter
	- Fix to squelch 'network unreachable' errors from tcp connect in
	  logs, high verbosity will show them.

16 February 2012: Wouter
	- iter_hints is now thread-owned in module env, and thus threadsafe.
	- Fix prefetch and sticky NS, now the prefetch works.  It picks
	  nameservers that 'would be valid in the future', and if this makes
	  the NS timeout, it updates that NS by asking delegation from the
	  parent again.  If child NS has longer TTL, that TTL does not get
	  refreshed from the lookup to the child nameserver.

15 February 2012: Wouter
	- Fix forward-zone memory, uses malloc and frees original root dp.
	- iter hints (stubs) uses malloc inside for more dynamicity.
	- unbound-control forward_add, forward_remove, stub_add, stub_remove
	  can modify stubs and forwards for running unbound (on mobile computer)
	  they can also add and remove domain-insecure for the zone.

14 February 2012: Wouter
	- Fix sticky NS (ghost domain problem) if prefetch is yes.
	- iter forwards uses malloc inside for more dynamicity.

13 February 2012: Wouter
	- RT#2955. Fix for cygwin compilation. 
	- iana portlist updated.

10 February 2012: Wouter
	- Slightly smaller critical region in one case in infra cache.
	- Fix timeouts to keep track of query type, A, AAAA and other, if
	  another has caused timeout blacklist, different type can still probe.
	- unit test fix for nomem_cnametopos.rpl race condition.

9 February 2012: Wouter
	- Fix AHX_BROKEN_MEMCMP for autoheader mess up of #undef in config.h.

8 February 2012: Wouter
	- implement draft-ietf-dnsext-ecdsa-04; which is in IETF LC; This
	  implementation is experimental at this time and not recommended
	  for use on the public internet (the protocol numbers have not
	  been assigned).  Needs recent ldns with --enable-ecdsa.
	- fix memory leak in errorcase for DSA signatures.
	- iana portlist updated.
	- workaround for openssl 0.9.8 ecdsa sha2 and evp problem.

3 February 2012: Wouter
	- fix for windows, rename() is not posix compliant on windows.

2 February 2012: Wouter
	- 1.4.16 release tag.
	- svn trunk is 1.4.17 in development.
	- iana portlist updated.

1 February 2012: Wouter
	- Fix validation failures (like: validation failure xx: no NSEC3
	  closest encloser from yy for DS zz. while building chain of trust,
	  because of a bug in the TTL-fix in 1.4.15, it picked the wrong rdata
	  for an NSEC3.  Now it does not change rdata, and fixes TTL.

30 January 2012: Wouter
	- Fix version-number in libtool to be version-info so it produces
	  libunbound.so.2 like it should.

26 January 2012: Wouter
	- Tag 1.4.15 (same as 1.4.15rc1), for 1.4.15 release.
	- trunk 1.4.16; includes changes memset testcode, #424 openindiana,
	  and keyfile write fixup.
	- applied patch to support outgoing-interface with ub_ctx_set_option.

23 January 2012: Wouter
	- Fix memset in test code.

20 January 2012: Wouter
	- Fix bug #424: compile on OpenIndiana OS with gcc 4.6.2.

19 January 2012: Wouter
	- Fix to write key files completely to a temporary file, and if that
	  succeeds, replace the real key file.  So failures leave a useful file.

18 January 2012: Wouter
	- tag 1.4.15rc1 created
	- updated libunbound/ubsyms.def and remade tag 1.4.15rc1.

17 January 2012: Wouter
	- Fix bug where canonical_compare of RRSIG did not downcase the
	  signer-name.  This is mostly harmless because RRSIGs do not have
	  to be sorted in canonical order, usually.

12 January 2012: Wouter
	- bug#428: add ub_version() call to libunbound.  API version increase,
	  with (binary) backwards compatibility for the previous version.

10 January 2012: Wouter
	- Fix bug #425: unbound reports wrong TTL in reply, it reports a TTL
	  that would be permissible by the RFCs but it is not the TTL in the
	  cache.
	- iana portlist updated.
	- uninitialised variable in reprobe for rtt blocked domains fixed.
	- lintfix and new flex output.

2 January 2012: Wouter
	- Fix to randomize hash function, based on 28c3 congress, reported
	  by Peter van Dijk.

24 December 2011: Wouter
	- Fix for memory leak (about 20 bytes when a tcp or udp send operation
	  towards authority servers failed, takes about 50.000 such failures to
	  leak one Mb, such failures are also usually logged), reported by
	  Robert Fleischmann.
	- iana portlist updated.

19 December 2011: Wouter
	- Fix for VU#209659 CVE-2011-4528: Unbound denial of service
	  vulnerabilities from nonstandard redirection and denial of existence
	  http://www.unbound.net/downloads/CVE-2011-4528.txt
	- robust checks for next-closer NSEC3s.
	- tag 1.4.14 created.
	- trunk has 1.4.15 in development.

15 December 2011: Wouter
	- remove uninit warning from cachedump code.
	- Fix parse error on negative SOA RRSIGs if badly ordered in the packet.

13 December 2011: Wouter
	- iana portlist updated.
	- svn tag 1.4.14rc1
	- fix infra cache comparison.
	- Fix to constrain signer_name to be a parent of the lookupname.

5 December 2011: Wouter
	- Fix getaddrinfowithincludes on windows with fedora16 mingw32-gcc.
	- Fix warnings with gcc 4.6 in compat/inet_ntop.c.
	- Fix warning unused in compat/strptime.c.
	- Fix malloc detection and double definition.

2 December 2011: Wouter
	- configure generated with autoconf 2.68.

30 November 2011: Wouter
	- Fix for tcp-upstream and ssl-upstream for if a laptop sleeps, causes
	  SERVFAILs.  Also fixed for UDP (but less likely).

28 November 2011: Wouter
	- Fix quartile time estimate, it was too low, (thanks Jan Komissar).
	- iana ports updated.

11 November 2011: Wouter
	- Makefile compat with SunOS make, BSD make and GNU make.
	- iana ports updated.

10 November 2011: Wouter
	- Makefile changed for BSD make compatibility.

9 November 2011: Wouter
	- added unit test for SSL service and SSL-upstream.

8 November 2011: Wouter
	- can configure ssl service to one port number, and not on others.
	- fixup windows compile with ssl support.
	- Fix double free in unbound-host, reported by Steve Grubb.
	- iana portlist updated.

1 November 2011: Wouter
	- dns over ssl support as a client, ssl-upstream yes turns it on.
	  It performs an SSL transaction for every DNS query (250 msec).
	- documentation for new options: ssl-upstream, ssl-service-key and
	  ssl-service.pem.
	- iana portlist updated.
	- fix -flto detection on Lion for llvm-gcc.

31 October 2011: Wouter
	- dns over ssl support, ssl-service-pem and ssl-service-key files
	  can be given and then TCP queries are serviced wrapped in SSL.

27 October 2011: Wouter
	- lame-ttl and lame-size options no longer exist, it is integrated
	  with the host info.  They are ignored (with verbose warning) if
	  encountered to keep the config file backwards compatible.
	- fix iana-update for changing gzip compression of results.
	- fix export-all-symbols on OSX.

26 October 2011: Wouter
	- iana portlist updated.
	- Infra cache stores information about ping and lameness per IP, zone.
	  This fixes bug #416.
	- fix iana_update target for gzipped file on iana site.

24 October 2011: Wouter
	- Fix resolve of partners.extranet.microsoft.com with a fix for the
	  server selection for choosing out of a (particular) list of bad
	  choices. (bug#415)
	- Fix make_new_space function so that the incoming query is not
	  overwritten if a jostled out query causes a waiting query to be
	  resumed that then fails and sends an error message.  (Thanks to
	  Matthew Lee).

21 October 2011: Wouter
	- fix --enable-allsymbols, fptr wlist is disabled on windows with this 
	  option enabled because of memory layout exe vs dll.

19 October 2011: Wouter
	- fix unbound-anchor for broken strptime on OSX lion, detected
	  in configure.
	- Detect if GOST really works, openssl1.0 on OSX fails.
	- Implement ipv6%interface notation for scope_id usage.

17 October 2011: Wouter
	- better documentation for inform_super (Thanks Yang Zhe).

14 October 2011: Wouter
	- Fix for out-of-memory condition in libunbound (thanks
	  Robert Fleischman).

13 October 2011: Wouter
	- Fix --enable-allsymbols, it depended on link specifics of the
	  target platform, or fptr_wlist assertion failures could occur.

12 October 2011: Wouter
	- updated contrib/unbound_munin_ to family=auto so that it works with
	  munin-node-configure automatically (if installed as
	  /usr/local/share/munin/plugins/unbound_munin_ ).

27 September 2011: Wouter
	- unbound.exe -w windows option for start and stop service.

23 September 2011: Wouter
	- TCP-upstream calculates tcp-ping so server selection works if there
	  are alternatives.

20 September 2011: Wouter
	- Fix classification of NS set in answer section, where there is a
	  parent-child server, and the answer has the AA flag for dir.slb.com.
	  Thanks to Amanda Constant from Secure64.

16 September 2011: Wouter
	- fix bug #408: accept patch from Steve Snyder that comments out
	  unused functions in lookup3.c.
	- iana portlist updated.
	- fix EDNS1480 change memleak and TCP fallback.
	- fix various compiler warnings (reported by Paul Wouters).
	- max sent count.  EDNS1480 only for rtt < 5000.  No promiscuous
	  fetch if sentcount > 3, stop query if sentcount > 16.  Count is
	  reset when referral or CNAME happens.  This makes unbound better
	  at managing large NS sets, they are explored when there is continued
	  interest (in the form of queries).

15 September 2011: Wouter
	- release 1.4.13.
	- trunk contains 1.4.14 in development.
	- Unbound probes at EDNS1480 if there an EDNS0 timeout.

12 September 2011: Wouter
	- Reverted dns EDNS backoff fix, it did not help and needs
	  fragmentation fixes instead.
	- tag 1.4.13rc2

7 September 2011: Wouter
	- Fix operation in ipv6 only (do-ip4: no) mode.

6 September 2011: Wouter
	- fedora specfile updated.

5 September 2011: Wouter
	- tag 1.4.13rc1

2 September 2011: Wouter
	- iana portlist updated.

26 August 2011: Wouter
	- Fix num-threads 0 does not segfault, reported by Simon Deziel.
	- Fix validation failures due to EDNS backoff retries, the retry
	  for fetch of data has want_dnssec because the iter_indicate_dnssec
	  function returns true when validation failure retry happens, and
	  then the serviced query code does not fallback to noEDNS, even if
	  the cache says it has this.  This helps for DLV deployment when
	  the DNSSEC status is not known for sure before the lookup concludes.

24 August 2011: Wouter
	- Applied patch from Karel Slany that fixes a memory leak in the
	  unbound python module, in string conversions.

22 August 2011: Wouter
	- Fix validation of qtype ANY responses with CNAMEs (thanks Cathy
	  Zhang and Luo Ce).  Unbound responds with the RR types that are
	  available at the name for qtype ANY and validates those RR types.
	  It does not test for completeness (i.e. with NSEC or NSEC3 query),
	  and it does not follow the CNAME or DNAME to another name (with
	  even more data for the already large response).
	- Fix that internally, CNAMEs with NXDOMAIN have that as rcode.
	- Documented the options that work with control set_option command.
	- tcp-upstream yes/no option (works with set_option) for tunnels.

18 August 2011: Wouter
	- fix autoconf call in makedist crosscompile to RC or snapshot.

17 August 2011: Wouter
	- Fix validation of . DS query.
	- new xml format at IANA, new awk for iana_update.
	- iana portlist updated.

10 August 2011: Wouter
	- Fix python site-packages path to /usr/lib64.
	- updated patch from Tom.
	- fix memory and fd leak after out-of-memory condition.

9 August 2011: Wouter
	- patch from Tom Hendrikx fixes load of python modules.

8 August 2011: Wouter
	- make clean had ldns-src reference, removed.

1 August 2011: Wouter
	- Fix autoconf 2.68 warnings

14 July 2011: Wouter
	- Unbound implements RFC6303 (since version 1.4.7).
	- tag 1.4.12rc1 is released as 1.4.12 (without the other fixes in the
	  meantime, those are for 1.4.13).
	- iana portlist updated.

13 July 2011: Wouter
	- Quick fix for contrib/unbound.spec example, no ldns-builtin any more.

11 July 2011: Wouter
	- Fix wildcard expansion no-data reply under an optout NSEC3 zone is
	  validated as insecure, reported by Jia Li (<EMAIL>).

4 July 2011: Wouter
	- 1.4.12rc1 tag created.

1 July 2011: Wouter
	- version number in example config file.
	- fix that --enable-static-exe does not complain about it unknown.

30 June 2011: Wouter
	- tag relase 1.4.11, trunk is 1.4.12 development.
	- iana portlist updated.
	- fix bug#395: id bits of other query may leak out under conditions
	- fix replyaddr count wrong after jostled queries, which leads to
	  eventual starvation where the daemon has no replyaddrs left to use.
	- fix comment about rndc port, that referred to the old port number.
	- fix that the listening socket is not closed when too many remote
	  control connections are made at the same time.
	- removed ldns-src tarball inside the unbound tarball.

23 June 2011: Wouter
	- Changed -flto check to support clang compiler.
	- tag 1.4.11rc3 created.

17 June 2011: Wouter
	- tag 1.4.11rc1 created.
	- remove warning about signed/unsigned from flex (other flex version).
	- updated aclocal.m4 and libtool to match.
	- tag 1.4.11rc2 created.

16 June 2011: Wouter
	- log-queries: yesno option, default is no, prints querylog.
	- version is 1.4.11.

14 June 2011: Wouter
	- Use -flto compiler flag for link time optimization, if supported.
	- iana portlist updated.

12 June 2011: Wouter
	- IPv6 service address for d.root-servers.net (2001:500:2D::D).

10 June 2011: Wouter
	- unbound-control has version number in the header,
	  UBCT[version]_space_ is the header sent by the client now.
	- Unbound control port number is registered with IANA:
	  ub-dns-control  8953/tcp    unbound dns nameserver control
	  This is the new default for the control-port config setting.
	- statistics-interval prints the number of jostled queries to log.

30 May 2011: Wouter
	- Fix Makefile for U in environment, since wrong U is more common than
	  deansification necessity.
	- iana portlist updated.
	- updated ldns tarball to 1.6.10rc2 snapshot of today.

25 May 2011: Wouter
	- Fix assertion failure when unbound generates an empty error reply
	  in response to a query, CVE-2011-1922 VU#531342.
	- This fix is in tag 1.4.10.
	- defense in depth against the above bug, an error is printed to log
	  instead of an assertion failure.

10 May 2011: Wouter
	- bug#386: --enable-allsymbols option links all binaries to libunbound
	  and reduces install size significantly.
	- feature, ignore-cd-flag: yesno to provide dnssec to legacy servers.
	- iana portlist updated.
	- Fix TTL of SOA so negative TTL is separately cached from normal TTL.

14 April 2011: Wouter
	- configure created with newer autoconf 2.66.

12 April 2011: Wouter
	- bug#378: Fix that configure checks for ldns_get_random presence.

8 April 2011: Wouter
	- iana portlist updated.
	- queries with CD flag set cause DNSSEC validation, but the answer is
	  not withheld if it is bogus.  Thus, unbound will retry if it is bad
	  and curb the TTL if it is bad, thus protecting the cache for use by
	  downstream validators.
	- val-override-date: -1 ignores dates entirely, for NTP usage.

29 March 2011: Wouter
	- harden-below-nxdomain: changed so that it activates when the
	  cached nxdomain is dnssec secure.  This avoids backwards
	  incompatibility because those old servers do not have dnssec.

24 March 2011: Wouter
	- iana portlist updated.
	- release 1.4.9.
	- trunk is 1.5.0

17 March 2011: Wouter
	- bug#370: new unbound.spec for CentOS 5.x from Harold Jones.
	  Applied but did not do the --disable-gost.

10 March 2011: Wouter
	- tag 1.4.9 release candidate 1 created.

3 March 2011: Wouter
	- updated ldns to today.

1 March 2011: Wouter
	- Fix no ADflag for NXDOMAIN in NSEC3 optout. And wildcard in optout.
	- give config parse error for multiple names on a stub or forward zone.
	- updated ldns tarball to 1.6.9(todays snapshot).

24 February 2011: Wouter
	- bug #361: Fix, time.elapsed variable not reset with stats_noreset.

23 February 2011: Wouter
	- iana portlist updated.
	- common.sh to version 3.

18 February 2011: Wouter
	- common.sh in testdata updated to version 2.

15 February 2011: Wouter
	- Added explicit note on unbound-anchor usage:
	  Please note usage of unbound-anchor root anchor is at your own risk
	  and under the terms of our LICENSE (see that file in the source).

11 February 2011: Wouter
	- iana portlist updated.
	- tpkg updated with common.sh for common functionality.

7 February 2011: Wouter
	- Added regression test for addition of a .net DS to the root, and
	  cache effects with different TTL for glue and DNSKEY.
	- iana portlist updated.

28 January 2011: Wouter
	- Fix remove private address does not throw away entire response.

24 January 2011: Wouter
	- release 1.4.8

19 January 2011: Wouter
	- fix bug#349: no -L/usr for ldns.

18 January 2011: Wouter
	- ldns 1.6.8 tarball included.
	- release 1.4.8rc1.

17 January 2011: Wouter
	- add get and set option for harden-below-nxdomain feature.
	- iana portlist updated.

14 January 2011: Wouter
	- Fix so a changed NS RRset does not get moved name stuck on old
	  server, for type NS the TTL is not increased.

13 January 2011: Wouter
	- Fix prefetch so it does not get stuck on old server for moved names.

12 January 2011: Wouter
	- iana portlist updated.

11 January 2011: Wouter
	- Fix insecure CNAME sequence marked as secure, reported by Bert
	  Hubert.

10 January 2011: Wouter
	- faster lruhash get_mem routine.

4 January 2011: Wouter
	- bug#346: remove ITAR scripts from contrib, the service is discontinued, use the root.
	- iana portlist updated.

23 December 2010: Wouter
	- Fix in infra cache that could cause rto larger than TOP_TIMEOUT kept.

21 December 2010: Wouter
	- algorithm compromise protection using the algorithms signalled in
	  the DS record.  Also, trust anchors, DLV, and RFC5011 receive this,
	  and thus, if you have multiple algorithms in your trust-anchor-file
	  then it will now behave different than before.  Also, 5011 rollover
	  for algorithms needs to be double-signature until the old algorithm
	  is revoked.
	  It is not an option, because I see no use to turn the security off.
	- iana portlist updated.

17 December 2010: Wouter
	- squelch 'tcp connect: bla' in logfile, (set verbosity 2 to see them).
	- fix validation in this case: CNAME to nodata for co-hosted opt-in
	  NSEC3 insecure delegation, was bogus, fixed to be insecure.

16 December 2010: Wouter
	- Fix our 'BDS' license (typo reported by Xavier Belanger).

10 December 2010: Wouter
	- iana portlist updated.
	- review changes for unbound-anchor.

2 December 2010: Wouter
	- feature typetransparent localzone, does not block other RR types.

1 December 2010: Wouter
	- Fix bug#338: print address when socket creation fails.

30 November 2010: Wouter
	- Fix storage of EDNS failures in the infra cache.
	- iana portlist updated.

18 November 2010: Wouter
	- harden-below-nxdomain option, default off (because very old
	  software may be incompatible).  We could enable it by default in
	  the future.

17 November 2010: Wouter
	- implement draft-vixie-dnsext-resimprove-00, we stop on NXDOMAIN.
	- make test output nicer.

15 November 2010: Wouter
	- silence 'tcp connect: broken pipe' and 'net down' at low verbosity.
	- iana portlist updated.
	- so-sndbuf option for very busy servers, a bit like so-rcvbuf.

9 November 2010: Wouter
	- unbound-anchor compiles with openssl 0.9.7.

8 November 2010: Wouter
	- release tag 1.4.7.
	- trunk is version 1.4.8.
	- Be lenient and accept imgw.pl malformed packet (like BIND).

5 November 2010: Wouter
	- do not synthesize a CNAME message from cache for qtype DS.

4 November 2010: Wouter
	- Use central entropy to seed threads.

3 November 2010: Wouter
	- Change the rtt used to probe EDNS-timeout hosts to 1000 msec.

2 November 2010: Wouter
	- tag 1.4.7rc1.
	- code review.

1 November 2010: Wouter
	- GOST code enabled by default (RFC 5933).

27 October 2010: Wouter
	- Fix uninit value in dump_infra print.
	- Fix validation failure for parent and child on same server with an
	  insecure childzone and a CNAME from parent to child.
	- Configure detects libev-4.00.

26 October 2010: Wouter
	- dump_infra and flush_infra commands for unbound-control.
	- no timeout backoff if meanwhile a query succeeded.
	- Change of timeout code.  No more lost and backoff in blockage.
	  At 12sec timeout (and at least 2x lost before) one probe per IP
	  is allowed only.  At 120sec, the IP is blocked.  After 15min, a
	  120sec entry has a single retry packet.

25 October 2010: Wouter
	- Configure errors if ldns is not found.

22 October 2010: Wouter
	- Windows 7 fix for the installer.

21 October 2010: Wouter
	- Fix bug where fallback_tcp causes wrong roundtrip and edns
	  observation to be noted in cache.  Fix bug where EDNSprobe halted
	  exponential backoff if EDNS status unknown.
	- new unresponsive host method, exponentially increasing block backoff.
	- iana portlist updated.

20 October 2010: Wouter
	- interface automatic works for some people with ip6 disabled.
	  Therefore the error check is removed, so they can use the option.

19 October 2010: Wouter
	- Fix for request list growth, if a server has long timeout but the
	  lost counter is low, then its effective rtt is the one without
	  exponential backoff applied.  Because the backoff is not working.
	  The lost counter can then increase and the server is blacklisted,
	  or the lost counter does not increase and the server is working
	  for some queries.

18 October 2010: Wouter
	- iana portlist updated.

13 October 2010: Wouter
	- Fix TCP so it uses a random outgoing-interface.
	- unbound-anchor handles ADDPEND keystate.

11 October 2010: Wouter
	- Fix bug when DLV below a trust-anchor that uses NSEC3 optout where
	  the zone has a secure delegation hosted on the same server did not
	  verify as secure (it was insecure by mistake).
	- iana portlist updated.
	- ldns tarball updated (for reading cachedumps with bad RR data).

1 October 2010: Wouter
	- test for unbound-anchor. fix for reading certs.
	- Fix alloc_reg_release for longer uptime in out of memory conditions.

28 September 2010: Wouter
	- unbound-anchor working, it creates or updates a root.key file.
	  Use it before you start the validator (e.g. at system boot time).

27 September 2010: Wouter
	- iana portlist updated.

24 September 2010: Wouter
	- bug#329: in example.conf show correct ipv4 link-local 169.254/16.

23 September 2010: Wouter
	- unbound-anchor app, unbound requires libexpat (xml parser library).

22 September 2010: Wouter
	- compliance with draft-ietf-dnsop-default-local-zones-14, removed
	  reverse ipv6 orchid prefix from builtin list.
	- iana portlist updated.

17 September 2010: Wouter
	- DLV has downgrade protection again, because the RFC says so.
	- iana portlist updated.

16 September 2010: Wouter
	- Algorithm rollover operational reality intrudes, for trust-anchor,
	  5011-store, and DLV-anchor if one key matches it's good enough.
	- iana portlist updated.
	- Fix reported validation error in out of memory condition.

15 September 2010: Wouter
	- Abide RFC5155 section 9.2: no AD flag for replies with NSEC3 optout.

14 September 2010: Wouter
	- increased mesh-max-activation from 1000 to 3000 for crazy domains
	  like _tcp.slb.com with 262 servers.
	- iana portlist updated.

13 September 2010: Wouter
	- bug#327: Fix for cannot access stub zones until the root is primed.

9 September 2010: Wouter
	- unresponsive servers are not completely blacklisted (because of
	  firewalls), but also not probed all the time (because of the request
	  list size it generates).  The probe rate is 1%.
	- iana portlist updated.

20 August 2010: Wouter
	- openbsd-lint fixes: acl_list_get_mem used if debug-alloc enabled.
	  iterator get_mem includes priv_get_mem.  delegpt nodup removed.
	  listen_pushback, query_info_allocqname, write_socket, send_packet,
	  comm_point_set_cb_arg and listen_resume removed.

19 August 2010: Wouter
	- Fix bug#321: resolution of rs.ripe.net artifacts with 0x20.
	  Delegpt structures checked for duplicates always.
	  No more nameserver lookups generated when depth is full anyway.
	- example.conf notes how to do DNSSEC validation and track the root.
	- iana portlist updated.

18 August 2010: Wouter
	- Fix bug#322: configure does not respect CFLAGS on Solaris.
	  Pass CFLAGS="-xO4 -xtarget=generic" on the configure command line
	  if use sun-cc, but some systems need different flags.

16 August 2010: Wouter
	- Fix acx_nlnetlabs.m4 configure output for autoconf-2.66 AS_TR_CPP
	  changes, uses m4_bpatsubst now.
	- make test (or make check) should be more portable and run the unit 
	  test and testbound scripts. (make longtest has special requirements).

13 August 2010: Wouter
	- More pleasant remote control command parsing.
	- documentation added for return values reported by doxygen 1.7.1.
	- iana portlist updated.

9 August 2010: Wouter
	- Fix name of rrset printed that failed validation.

5 August 2010: Wouter
	- Return NXDOMAIN after chain of CNAMEs ends at name-not-found.

4 August 2010: Wouter
	- Fix validation in case a trust anchor enters into a zone with
	  unsupported algorithms.

3 August 2010: Wouter
	- updated ldns tarball with bugfixes.
	- release tag 1.4.6.
	- trunk becomes 1.4.7 develop.
	- iana portlist updated.

22 July 2010: Wouter
	- more error details on failed remote control connection.

15 July 2010: Wouter
	- rlimit adjustments for select and ulimit can happen at the same time.

14 July 2010: Wouter
	- Donation text added to README.
	- Fix integer underflow in prefetch ttl creation from cache.  This
	  fixes a potential negative prefetch ttl.

12 July 2010: Wouter
	- Changed the defaults for num-queries-per-thread/outgoing-range.
	  For builtin-select: 512/960, for libevent 1024/4096 and for
	  windows 24/48 (because of win api).  This makes the ratio this way
	  to improve resilience under heavy load.  For high performance, use
	  libevent and possibly higher numbers.

10 July 2010: Wouter
	- GOST enabled if SSL is recent and ldns has GOST enabled too.
	- ldns tarball updated.

9 July 2010: Wouter
	- iana portlist updated.
	- Fix validation of qtype DNSKEY when a key-cache entry exists but
	  no rr-cache entry is used (it expired or prefetch), it then goes
	  back up to the DS or trust-anchor to validate the DNSKEY.

7 July 2010: Wouter
	- Neat function prototypes, unshadowed local declarations.

6 July 2010: Wouter
	- failure to chown the pidfile is not fatal any more.
	- testbound uses UTC timezone.
	- ldns tarball updated (ports and works on Minix 3.1.7).  On Minix, add
	  /usr/gnu/bin to PATH, use ./configure AR=/usr/gnu/bin/gar and gmake.

5 July 2010: Wouter
	- log if a server is skipped because it is on the donotquery list,
	  at verbosity 4, to enable diagnosis why no queries to 127.0.0.1.
	- added feature to print configure date, target and options with -h.
	- added feature to print event backend system details with -h.
	- wdiff is not actually required by make test, updated requirements.

1 July 2010: Wouter
	- Fix RFC4035 compliance with 2.2 statement that the DNSKEY at apex
	  must be signed with all algorithms from the DS rrset at the parent.
	  This is now checked and becomes bogus if not.

28 June 2010: Wouter
	- Fix jostle list bug found by Vince (luoce@cnnic), it caused the qps
	  in overload situations to be about 5 qps for the class of shortly
	  serviced queries.
	  The capacity of the resolver is then about (numqueriesperthread / 2)
	  / (average time for such long queries) qps for long queries.
	  And about (numqueriesperthread / 2)/(jostletimeout in whole seconds)
	  qps for short queries, per thread.
	- Fix the max number of reply-address count to be applied for duplicate
	  queries, and not for new query list entries.  This raises the memory
	  usage to a max of (16+1)*numqueriesperthread reply addresses.

25 June 2010: Wouter
	- Fix handling of corner case reply from lame server, follows rfc2308.
	  It could lead to a nodata reply getting into the cache if the search
	  for a non-lame server turned up other misconfigured servers.
	- unbound.h has extern "C" statement for easier include in c++.

23 June 2010: Wouter
	- iana portlist updated.
	- makedist upgraded cross compile openssl option, like this: 
	  ./makedist.sh -s -wssl openssl-1.0.0a.tar.gz -w --enable-gost

22 June 2010: Wouter
	- Unbound reports libev or libevent correctly in logs in verbose mode.
	- Fix to unload gost dynamic library module for leak testing.

18 June 2010: Wouter
	- iana portlist updated.

17 June 2010: Wouter
	- Add AAAA to root hints for I.ROOT-SERVERS.NET.

16 June 2010: Wouter
	- Fix assertion failure reported by Kai Storbeck from XS4ALL, the
	  assertion was wrong.
	- updated ldns tarball.

15 June 2010: Wouter
	- tag 1.4.5 created.
	- trunk contains 1.4.6 in development.
	- Fix TCPreply on systems with no writev, if just 1 byte could be sent.
	- Fix to use one pointer less for iterator query state store_parent_NS.
	- makedist crosscompile to windows uses builtin ldns not host ldns.
	- Max referral count from 30 to 130, because 128 one character domains
	  is valid DNS.
	- added documentation for the histogram printout to syslog.

11 June 2010: Wouter
	- When retry to parent the retrycount is not wiped, so failed 
	  nameservers are not tried again.
	- iana portlist updated.

10 June 2010: Wouter
	- Fix bug where a long loop could be entered, now cycle detection
	  has a loop-counter and maximum search amount.

4 June 2010: Wouter
	- iana portlist updated.
	- 1.4.5rc1 tag created.

3 June 2010: Wouter
	- ldns tarball updated, 1.6.5.
	- review comments, split dependency cycle tracking for parentside
	  last resort lookups for A and AAAA so there are more lookup options.

2 June 2010: Wouter
	- Fix compile warning if compiled without threads.
	- updated ldns-tarball with current ldns svn (pre 1.6.5).
	- GOST disabled-by-default, the algorithm number is allocated but the
	  RFC is still has to pass AUTH48 at the IETF.

1 June 2010: Wouter
	- Ignore Z flag in incoming messages too.
	- Fix storage of negative parent glue if that last resort fails.
	- libtoolize 2.2.6b, autoconf 2.65 applied to configure.
	- new splint flags for newer splint install.

31 May 2010: Wouter
	- Fix AD flag handling, it could in some cases mistakenly copy the AD 
	  flag from upstream servers.
	- alloc_special_obtain out of memory is not a fatal error any more,
	  enabling unbound to continue longer in out of memory conditions.
	- parentside names are dispreferred but not said to be dnssec-lame.
	- parentside check for cached newname glue.
	- fix parentside and querytargets modulestate, for dump_requestlist.
	- unbound-control-setup makes keys -rw-r--- so not all users permitted.
	- fix parentside from cache to be marked dispreferred for bad names.

28 May 2010: Wouter
	- iana portlist updated.
	- parent-child disagreement approach altered.  Older fixes are
	  removed in place of a more exhaustive search for misconfigured data
	  available via the parent of a delegation.
	  This is designed to be throttled by cache entries, with TTL from the
	  parent if possible.  Additionally the loop-counter is used.
	  It also tests for NS RRset differences between parent and child.
	  The fetch of misconfigured data should be more reliable and thorough.
	  It should work reliably even with no or only partial data in cache.
	  Data received from the child (as always) is deemed more
	  authoritative than information received from the delegation parent.
	  The search for misconfigured data is not performed normally.

26 May 2010: Wouter
	- Contribution from Migiel de Vos (Surfnet): nagios patch for
	  unbound-host, in contrib/ (in the source tarball).  Makes
	  unbound-host suitable for monitoring dnssec(-chain) status.

21 May 2010: Wouter
	- EDNS timeout code will not fire if EDNS status already known.
	- EDNS failure not stored if EDNS status known to work.

19 May 2010: Wouter
	- Fix resolution for domains like safesvc.com.cn.  If the iterator
	  can not recurse further and it finds the delegation in a state
	  where it would otherwise have rejected it outhand if so received
	  from a cache lookup, then it can try to ask higherup (with loop
	  protection).
	- Fix comments in iter_utils:dp_is_useless.

18 May 2010: Wouter
	- Fix various compiler warnings from the clang llvm compiler.
	- iana portlist updated.

6 May 2010: Wouter
	- Fix bug#308: spelling error in variable name in parser and lexer.

4 May 2010: Wouter
	- Fix dnssec-missing detection that was turned off by server selection.
	- Conforms to draft-ietf-dnsop-default-local-zones-13.  Added default
	  reverse lookup blocks for IPv4 test nets 100.51.198.in-addr.arpa,
	  113.0.203.in-addr.arpa and Orchid prefix *******.0.2.ip6.arpa.

29 April 2010: Wouter
	- Fix for dnssec lameness detection to use the key cache.
	- infra cache entries that are expired are wiped clean.  Previously
	  it was possible to not expire host data (if accessed often).

28 April 2010: Wouter
	- ldns tarball updated and GOST support is detected and then enabled. 
	- iana portlist updated.
	- Fix detection of gost support in ldns (reported by Chris Smith).

27 April 2010: Wouter
	- unbound-control get_option domain-insecure shows config file items.
	- fix retry sequence if prime hints are recursion-lame.
	- autotrust anchor file can be initialized with a ZSK key as well.
	- harden-referral-path does not result in failures due to max-depth.
	  You can increase the max-depth by adding numbers (' 0') after the
	  target-fetch-policy, this increases the depth to which is checked.

26 April 2010: Wouter
	- Compile fix using Sun Studio 12 compiler on Solaris 5.9, use
	  CPPFLAGS during configure process.
	- if libev is installed on the base system (not libevent), detect
	  it from the event.h header file and link with -lev.
	- configlexer.lex gets config.h, and configyyrename.h added by make,
	  no more double include.
	- More strict scrubber (Thanks to George Barwood for the idea):
	  NS set must be pertinent to the query (qname subdomain nsname).
	- Fix bug#307: In 0x20 backoff fix fallback so the number of 
	  outstanding queries does not become -1 and block the request.
	  Fixed handling of recursion-lame in combination with 0x20 fallback.
	  Fix so RRsets are compared canonicalized and sorted if the immediate
	  comparison fails, this makes it work around round-robin sites.

23 April 2010: Wouter
	- Squelch log message: sendto failed permission denied for
	  ***************, it is visible in VERB_DETAIL (verbosity 2).
	- Fix to fetch data as last resort more tenaciously.  When cycle
	  targets cause the server selection to believe there are more options
	  when they really are not there, the server selection is reinitiated.
	- Fix fetch from blacklisted dnssec lame servers as last resort.  The
	  server's IP address is then given in validator errors as well.
	- Fix local-zone type redirect that did not use the query name for
	  the answer rrset.

22 April 2010: Wouter
	- tag 1.4.4.
	- trunk contains 1.4.5 in development.
	- Fix validation failure for qtype ANY caused by a RRSIG parse failure.
	  The validator error message was 'no signatures from ...'.

16 April 2010: Wouter
	- more portability defines for CMSG_SPACE, CMSG_ALIGN, CMSG_LEN.
	- tag 1.4.4rc1.

15 April 2010: Wouter
	- ECC-GOST algorithm number 12 that is assigned by IANA.  New test
	  example key and signatures for GOST.  GOST requires openssl-1.0.0.
	  GOST is still disabled by default.

9 April 2010: Wouter
	- Fix bug#305: pkt_dname_tolower could read beyond end of buffer or
	  get into an endless loop, if 0x20 was enabled, and buffers are small
	  or particular broken packets are received.
	- Fix chain of trust with CNAME at an intermediate step, for the DS
	  processing proof.

8 April 2010: Wouter
	- Fix validation of queries with wildcard names (*.example).

6 April 2010: Wouter
	- Fix EDNS probe for .de DNSSEC testbed failure, where the infra
	  cache timeout coincided with a server update, the current EDNS 
	  backoff is less sensitive, and does not cache the backoff unless 
	  the backoff actually works and the domain is not expecting DNSSEC.
	- GOST support with correct algorithm numbers.

1 April 2010: Wouter
	- iana portlist updated.

24 March 2010: Wouter
	- unbound control flushed items are not counted when flushed again.

23 March 2010: Wouter
	- iana portlist updated.

22 March 2010: Wouter
	- unbound-host disables use-syslog from config file so that the
	  config file for the main server can be used more easily.
	- fix bug#301: unbound-checkconf could not parse interface
	  '0.0.0.0@5353', even though unbound itself worked fine.

19 March 2010: Wouter
	- fix fwd_ancil test to pass if the socket options are not supported.

18 March 2010: Wouter
	- Fixed random numbers for port, interface and server selection.
	  Removed very small bias.
	- Refer to the listing in unbound-control man page in the extended
	  statistics entry in the unbound.conf man page.

16 March 2010: Wouter
	- Fix interface-automatic for OpenBSD: msg.controllen was too small,
	  also assertions on ancillary data buffer.
	- check for IP_SENDSRCADDR for interface-automatic or IP_PKTINFO.
	- for NSEC3 check if signatures are cached.

15 March 2010: Wouter
	- unit test for util/regional.c.

12 March 2010: Wouter
	- Reordered configure checks so fork and -lnsl -lsocket checks are
	  earlier, and thus later checks benefit from and do not hinder them.
	- iana portlist updated.
	- ldns tarball updated.
	- Fix python use when multithreaded.
	- Fix solaris python compile.
	- Include less in config.h and include per code file for ldns, ssl.

11 March 2010: Wouter
	- another memory allocation option: --enable-alloc-nonregional.
	  exposes the regional allocations to other memory purifiers.
	- fix for memory alignment in struct sock_list allocation.
	- Fix for MacPorts ldns without ssl default, unbound checks if ldns
	  has dnssec functionality and uses the builtin if not.
	- Fix daemonize on Solaris 10, it did not detach from terminal.
	- tag 1.4.3 created.
	- trunk is 1.4.4 in development.
	- spelling fix in validation error involving cnames.

10 March 2010: Wouter
	- --enable-alloc-lite works with test set.
	- portability in the testset: printf format conversions, prototypes.

9 March 2010: Wouter
	- tag 1.4.2 created.
	- trunk is 1.4.3 in development.
	- --enable-alloc-lite debug option.

8 March 2010: Wouter
	- iana portlist updated.

4 March 2010: Wouter
	- Fix crash in control channel code.

3 March 2010: Wouter
	- better casts in pipe code, brackets placed wrongly.
	- iana portlist updated.

1 March 2010: Wouter
	- make install depends on make all.
	- Fix 5011 auto-trust-anchor-file initial read to skip RRSIGs.
	- --enable-checking: enables assertions but does not look nonproduction.
	- nicer VERB_DETAIL (verbosity 2, unbound-host -d) output, with
	  nxdomain and nodata distinguished.
	- ldns tarball updated.
	- --disable-rpath fixed for libtool not found errors.
	- new fedora specfile from Fedora13 in contrib from Paul Wouters.

26 February 2010: Wouter
	- Fixup prototype for lexer cleanup in daemon code.
	- unbound-control list_stubs, list_forwards, list_local_zones and
	  list_local_data.

24 February 2010: Wouter
	- Fix scrubber bug that potentially let NS records through.  Reported
	  by Amanda Constant.
	- Also delete potential poison references from additional.
	- Fix: no classification of a forwarder as lame, throw away instead.

23 February 2010: Wouter
	- libunbound ub_ctx_get_option() added.
	- unbound-control set_option and get_option commands.
	- iana portlist updated.

18 February 2010: Wouter
	- A little more strict DS scrubbing.
	- No more blacklisting of unresponsive servers, a 2 minute timeout
	  is backed off to.
	- RD flag not enabled for dnssec-blacklisted tries, unless necessary.
	- pickup ldns compile fix, libdl for libcrypto.
	- log 'tcp connect: connection timed out' only in high verbosity.
	- unbound-control log_reopen command.
	- moved get_option code from unbound-checkconf to util/config_file.c

17 February 2010: Wouter
	- Disregard DNSKEY from authority section for chain of trust.
	  DS records that are irrelevant to a referral scrubbed.  Anti-poison.
	- iana portlist updated.

16 February 2010: Wouter
	- Check for 'no space left on device' (or other errors) when 
	  writing updated autotrust anchors and print errno to log.

15 February 2010: Wouter
	- Fixed the requery protection, the TTL was 0, it is now 900 seconds,
	  hardcoded.  We made the choice to send out more conservatively,
	  protecting against an aggregate effect more than protecting a
	  single user (from their own folly, perhaps in case of misconfig).

12 February 2010: Wouter
	- Re-query pattern changed on validation failure.  To protect troubled
	  authority servers, unbound caches a failure for the DNSKEY or DS
	  records for the entire zone, and only retries that 900 seconds later.
	  This implies that only a handful of packets are sent extra to the
	  authority if the zone fails.

11 February 2010: Wouter
	- ldns tarball update for long label length syntax error fix.
	- iana portlist updated.

9 February 2010: Wouter
	- Fixup in compat snprintf routine, %f 1.02 and %g support.
	- include math.h for testbound test compile portability.

2 February 2010: Wouter
	- Updated url of IANA itar, interim trust anchor repository, in script.

1 February 2010: Wouter
	- iana portlist updated.
	- configure test for memcmp portability.

27 January 2010: Wouter
	- removed warning on format string in validator error log statement.
	- iana portlist updated.

22 January 2010: Wouter
	- libtool finish the install of unbound python dynamic library.

21 January 2010: Wouter
	- acx_nlnetlabs.m4 synchronised with nsd's version.

20 January 2010: Wouter
	- Fixup lookup trouble for parent-child domains on the first query.

14 January 2010: Wouter
	- Fixup ldns detection to also check for header files.

13 January 2010: Wouter
	- prefetch-key option that performs DNSKEY queries earlier in the
	  validation process, and that could halve the latency on DNSSEC
	  queries.  It takes some extra processing (CPU, a cache is needed).

12 January 2010: Wouter
	- Fix unbound-checkconf for auto-trust-anchor-file present checks.

8 January 2010: Wouter
	- Fix for parent-child disagreement code which could have trouble
	  when (a) ipv6 was disabled and (b) the TTL for parent and child
	  were different.  There were two bugs, the parent-side information
	  is fixed to no longer block lookup of child side information and
	  the iterator is fixed to no longer attempt to get ipv6 when it is
	  not enabled and then give up in failure.
	- test and fixes to make prefetch actually store the answer in the
	  cache.  Considers some rrsets 'already expired' but does not allow
	  overwriting of rrsets considered more secure.

7 January 2010: Wouter
	- Fixup python documentation (thanks Leo Vandewoestijne).
	- Work on cache prefetch feature.
	- Stats for prefetch, in log print stats, unbound-control stats
	  and in unbound_munin plugin.

6 January 2010: Wouter
	- iana portlist updated.
	- bug#291: DNS wireformat max is 255. dname_valid allowed 256 length.
	- verbose output includes parent-side-address notion for lameness.
	- documented val-log-level: 2 setting in example.conf and man page.
	- change unbound-control-setup from 1024(sha1) to 1536(sha256).

1 January 2010: Wouter
	- iana portlist updated.

22 December 2009: Wouter
	- configure with newer libtool 2.2.6b.

17 December 2009: Wouter
	- review comments.
	- tag 1.4.1.
	- trunk to version 1.4.2.
	
15 December 2009: Wouter
	- Answer to qclass=ANY queries, with class IN contents.
	  Test that validation also works.
	- updated ldns snapshot tarball with latest fixes (parsing records).

11 December 2009: Wouter
	- on IPv4 UDP turn off DF flag.

10 December 2009: Wouter
	- requirements.txt updated with design choice explanations.
	- Reading fixes: fix to set unlame when child confirms parent glue,
	  and fix to avoid duplicate addresses in delegation point.
	- verify_rrsig routine checks expiration last.

9 December 2009: Wouter
	- Fix Bug#287(reopened): update of ldns tarball with fix for parse
	  errors generated for domain names like '.example.com'.
	- Fix SOA excluded from negative DS responses.  Reported by Hauke
	  Lampe.  The negative cache did not include proper SOA records for
	  negative qtype DS responses which makes BIND barf on it, such
	  responses are now only used internally.
	- Fix negative cache lookup of closestencloser check of DS type bit.

8 December 2009: Wouter
	- Fix for lookup of parent-child disagreement domains, where the
	  parent-side glue works but it does not provide proper NS, A or AAAA
	  for itself, fixing domains such as motorcaravanners.eu.
	- Feature: you can specify a port number in the interface: line, so
	  you can bind the same interface multiple times at different ports.

7 December 2009: Wouter
	- Bug#287: Fix segfault when unbound-control remove nonexistent local
	  data.  Added check to tests.

1 December 2009: Wouter
	- Fix crash with module-config "iterator".
	- Added unit test that has "iterator" module-config.

30 November 2009: Wouter
	- bug#284: fix parse of # without end-of-line at end-of-file.

26 November 2009: Wouter
	- updated ldns with release candidate for version 1.6.3.
	- tag for 1.4.0 release.
	- 1.4.1 version in trunk.
	- Fixup major libtool version to 2 because of why_bogus change.
	  It was 1:5:0 but should have been 2:0:0.

23 November 2009: Wouter
	- Patch from David Hubbard for libunbound manual page.
	- Fixup endless spinning in unbound-control stats reported by
	  Attila Nagy.  Probably caused by clock reversal.

20 November 2009: Wouter
	- contrib/split-itar.sh contributed by Tom Hendrikx.

19 November 2009: Wouter
	- better argument help for unbound-control.
	- iana portlist updated.

17 November 2009: Wouter
	- noted multiple entries for multiple domain names in example.conf.
	- iana portlist updated.

16 November 2009: Wouter
	- Fixed signer detection of CNAME responses without signatures.
	- Fix#282 libunbound memleak on error condition by Eric Sesterhenn.
	- Tests for CNAMEs to deeper trust anchors, secure and bogus.
	- svn tag 1.4.0rc1 made.

13 November 2009: Wouter
	- Fixed validation failure for CNAME to optout NSEC3 nodata answer.
	- unbound-host does not fail on type ANY.
	- Fixed wireparse failure to put RRSIGs together with data in some
	  long ANY mix cases, which fixes validation failures.

12 November 2009: Wouter
	- iana portlist updated.
	- fix manpage errors reported by debian lintian.
	- review comments.
	- fixup very long vallog2 level error strings.
	
11 November 2009: Wouter
	- ldns tarball updated (to 1.6.2).
	- review comments.

10 November 2009: Wouter
	- Thanks to Surfnet found bug in new dnssec-retry code that failed
	  to combine well when combined with DLV and a particular failure. 
	- Fixed unbound-control -h output about argument optionality.
	- review comments.

5 November 2009: Wouter
	- lint fixes and portability tests.
	- better error text for multiple domain keys in one autotrust file.

2 November 2009: Wouter
	- Fix bug where autotrust does not work when started with a DS.
	- Updated GOST unit tests for unofficial algorithm number 249
	  and DNSKEY-format changes in draft version -01.

29 October 2009: Wouter
	- iana portlist updated.
	- edns-buffer-size option, default 4096.
	- fixed do-udp: no.

28 October 2009: Wouter
	- removed abort on prealloc failure, error still printed but softfail.
	- iana portlist updated.
	- RFC 5702: RSASHA256 and RSASHA512 support enabled by default.
	- ldns tarball updated (which also enables rsasha256 support).

27 October 2009: Wouter
	- iana portlist updated.

8 October 2009: Wouter
	- please doxygen
	- add val-log-level print to corner case (nameserver.epost.bg).
	- more detail to errors from insecure delegation checks.
	- Fix double time subtraction in negative cache reported by 
	  Amanda Constant and Hugh Mahon.
	- Made new validator error string available from libunbound for
	  applications.  It is in result->why_bogus, a zero-terminated string.
	  unbound-host prints it by default if a result is bogus.
	  Also the errinf is public in module_qstate (for other modules).

7 October 2009: Wouter
	- retry for validation failure in DS and prime results. Less mem use.
	  unit test.  Provisioning in other tests for requeries.
	- retry for validation failure in DNSKEY in middle of chain of trust.
	  unit test.
	- retry for empty non terminals in chain of trust and unit test.
	- Fixed security bug where the signatures for NSEC3 records were not
	  checked when checking for absence of DS records.  This could have
	  enabled the substitution of an insecure delegation.
	- moved version number to 1.4.0 because of 1.3.4 release with only
	  the NSEC3 patch from the entry above.
	- val-log-level: 2 shows extended error information for validation
	  failures, but still one (longish) line per failure.  For example:
	  validation failure <example.com. DNSKEY IN>: signature expired from
	  ********* for trust anchor example.com. while building chain of trust
	  validation failure <www.example.com. A IN>: no signatures from
	  ********* for key example.com. while building chain of trust

6 October 2009: Wouter
	- Test set updated to provide additional ns lookup result.
	  The retry would attempt to fetch the data from other nameservers
	  for bogus data, and this needed to be provisioned in the tests.

5 October 2009: Wouter
	- first validation failure retry code.  Retries for data failures.
	  And unit test.

2 October 2009: Wouter
	- improve 5011 modularization.
	- fix unbound-host so -d can be given before -C.
	- iana portlist updated.

28 September 2009: Wouter
	- autotrust-anchor-file can read multiline input and $ORIGIN.
	- prevent integer overflow in holddown calculation. review fixes.
	- fixed race condition in trust point revocation. review fix.
	- review fixes to comments, removed unused code.

25 September 2009: Wouter
	- so-rcvbuf: 4m option added.  Set this on large busy servers to not
	  drop the occasional packet in spikes due to full socket buffers.
	  netstat -su keeps a counter of UDP dropped due to full buffers.
	- review of validator/autotrust.c, small fixes and comments.

23 September 2009: Wouter
	- 5011 query failed counts verification failures, not lookup failures.
	- 5011 probe failure handling fixup.
	- test unbound reading of original autotrust data.
	  The metadata per-key, such as key state (PENDING, MISSING, VALID) is
	  picked up, otherwise performs initial probe like usual.

22 September 2009: Wouter
	- autotrust test with algorithm rollover, new ordering of checks
	  assists in orderly rollover.
	- autotrust test with algorithm rollover to unknown algorithm.
	  checks if new keys are supported before adding them.
	- autotrust test with trust point revocation, becomes unsigned.
	- fix DNSSEC-missing-signature detection for minimal responses
	  for qtype DNSKEY (assumes DNSKEY occurs at zone apex).

18 September 2009: Wouter
	- autotrust tests, fix trustpoint timer deletion code.
	  fix count of valid anchors during missing remove.
	- autotrust: pick up REVOKE even if not signed with known other keys.

17 September 2009: Wouter
	- fix compile of unbound-host when --enable-alloc-checks.
	- Fix lookup problem reported by Koh-ichi Ito and Jaap Akkerhuis.
	- Manual page fixes reported by Tony Finch.

16 September 2009: Wouter
	- Fix memory leak reported by Tao Ma.
	- Fix memstats test tool for log-time-ascii log format.

15 September 2009: Wouter
	- iana portlist updated.

10 September 2009: Wouter
	- increased MAXSYSLOGLEN so .bg key can be printed in debug output.
	- use linebuffering for log-file: output, this can be significantly
	  faster than the previous fflush method and enable some class of
	  resolvers to use high verbosity (for short periods).
	  Not on windows, because line buffering does not work there.

9 September 2009: Wouter
	- Fix bug where DNSSEC-bogus messages were marked with too high TTL.
	  The RRsets would still expire at the normal time, but this would
	  keep messages bogus in the cache for too long.
	- regression test for that bug.
	- documented that load_cache is meant for debugging.

8 September 2009: Wouter
	- fixup printing errors when load_cache, they were printed to the
	  SSL connection which broke, now to the log.
	- new ldns - with fixed parse of large SOA values.

7 September 2009: Wouter
	- autotrust testbound scenarios.
	- autotrust fix that failure count is written to file.
	- autotrust fix that keys may become valid after add holddown time
	  alone, before the probe returns.

4 September 2009: Wouter
	- Changes to make unbound work with libevent-2.0.3 alpha. (in
	  configure detection due to new ssl dependency in libevent)
	- do not call sphinx for documentation when python is disabled.
	- remove EV_PERSIST from libevent timeout code to make the code
	  compatible with the libevent-2.0.  Works with older libevent too.
	- fix memory leak in python code.

3 September 2009: Wouter
	- Got a patch from Luca Bruno for libunbound support on windows to
	  pick up the system resolvconf nameservers and hosts there.
	- included ldns updated (enum warning fixed).
	- makefile fix for parallel makes.
	- Patch from Zdenek Vasicek and Attila Nagy for using the source IP
	  from python scripts.  See pythonmod/examples/resip.py.
	- doxygen comment fixes.

2 September 2009: Wouter
	- TRAFFIC keyword for testbound. Simplifies test generation.
	  ${range lower val upper} to check probe timeout values.
	- test with 5011-prepublish rollover and revocation.
	- fix revocation of RR for autotrust, stray exclamation mark.

1 September 2009: Wouter
	- testbound variable arithmetic.
	- autotrust probe time is randomised.
	- autotrust: the probe is active and does not fetch from cache.

31 August 2009: Wouter
	- testbound variable processing.

28 August 2009: Wouter
	- fixup unbound-control lookup to print forward and stub servers.

27 August 2009: Wouter
	- autotrust: mesh answer callback is empty.

26 August 2009: Wouter
	- autotrust probing.
	- iana portlist updated.

25 August 2009: Wouter
	- fixup memleak in trust anchor unsupported algorithm check.
	- iana portlist updated.
	- autotrust options: add-holddown, del-holddown, keep-missing.
	- autotrust store revoked status of trust points.
	- ctime_r compat definition.
	- detect yylex_destroy() in configure.
	- detect SSL_get_compression_methods declaration in configure.
	- fixup DS lookup at anchor point with unsigned parent.
	- fixup DLV lookup for DS queries to unsigned domains.

24 August 2009: Wouter
	- cleaner memory allocation on exit. autotrust test routines.
	- free all memory on program exit, fix for ssl and flex.

21 August 2009: Wouter
	- autotrust: debug routines. Read,write and conversions work.

20 August 2009: Wouter
	- autotrust: save and read trustpoint variables.

19 August 2009: Wouter
	- autotrust: state table updates.
	- iana portlist updated.

17 August 2009: Wouter
	- autotrust: process events.

17 August 2009: Wouter
	- Fix so that servers are only blacklisted if they fail to reply 
	  to 16 queries in a row and the timeout gets above 2 minutes.
	- autotrust work, split up DS verification of DNSKEYs.

14 August 2009: Wouter
	- unbound-control lookup prints out infra cache information, like RTT.
	- Fix bug in DLV lookup reported by Amanda from Secure64.
	  It could sometimes wrongly classify a domain as unsigned, which
	  does not give the AD bit on replies.

13 August 2009: Wouter
	- autotrust read anchor files. locked trust anchors.

12 August 2009: Wouter
	- autotrust import work.

11 August 2009: Wouter
	- Check for openssl compatible with gost if enabled.
	- updated unit test for GOST=211 code.
	  Nicer naming of test files.
	- iana portlist updated.

7 August 2009: Wouter
	- call OPENSSL_config() in unbound and unit test so that the
	  operator can use openssl.cnf for configuration options.
	- removed small memory leak from config file reader.

6 August 2009: Wouter
	- configure --enable-gost for GOST support, experimental
	  implementation of draft-dolmatov-dnsext-dnssec-gost-01.
	- iana portlist updated.
	- ldns tarball updated (with GOST support).

5 August 2009: Wouter
	- trunk moved to 1.3.4.

4 August 2009: Wouter
	- Added test that the examples from draft rsasha256-14 verify.
	- iana portlist updated.
	- tagged 1.3.3

3 August 2009: Wouter
	- nicer warning when algorithm not supported, tells you to upgrade.
	- iana portlist updated.

27 July 2009: Wouter
	- Updated unbound-cacti contribution from Dmitriy Demidov, with
	  the queue statistics displayed in its own graph.
	- iana portlist updated.

22 July 2009: Wouter
	- Fix bug found by Michael Tokarev where unbound would try to
	  prime the root servers even though forwarders are configured for
	  the root.
	- tagged 1.3.3rc1

21 July 2009: Wouter
	- Fix server selection, so that it waits for open target queries when
	  faced with lameness.

20 July 2009: Wouter
	- Ignore transient sendto errors, no route to host, and host, net down.
	- contrib/update-anchor.sh has -r option for root-hints.
	- feature val-log-level: 1 prints validation failures so you can
	  keep track of them during dnssec deployment.

16 July 2009: Wouter
	- fix replacement malloc code.  Used in crosscompile.
	- makedist -w creates crosscompiled setup.exe on fedora11.

15 July 2009: Wouter
	- dependencies for compat items, for crosscompile.
	- mingw32 crosscompile changes, dependencies and zipfile creation.
	  and with System.dll from the windows NSIS you can make setup.exe.
	- package libgcc_s_sjlj exception handler for NSISdl.dll.

14 July 2009: Wouter
	- updated ldns tarball for solaris x64 compile assistance.
	- no need to define RAND_MAX from config.h.
	- iana portlist updated.
	- configure changes and ldns update for mingw32 crosscompile.

13 July 2009: Wouter
	- Fix for crash at start on windows.
	- tag for release 1.3.2.
	- trunk has version 1.3.3.
	- Fix for ID bits on windows to use all 16. RAND_MAX was not
	  defined like you'd expect on mingw. Reported by Mees de Roo.

9 July 2009: Wouter
	- tag for release 1.3.1.
	- trunk has version 1.3.2.

7 July 2009: Wouter
	- iana portlist updated.

6 July 2009: Wouter
	- prettier error handling in SSL setup.
	- makedist.sh uname fix (same as ldns).
	- updated fedora spec file.

3 July 2009: Wouter
	- fixup linking when ldnsdir is "".

30 June 2009: Wouter
	- more lenient truncation checks.

29 June 2009: Wouter
	- ldns trunk r2959 imported as tarball, because of solaris cc compile
	  support for c99.  r2960 for better configure.
	- better wrongly_truncated check.
	- On Linux, fragment IPv6 datagrams to the IPv6 minimum MTU, to
	  avoid dropped packets at routers.

26 June 2009: Wouter
	- Fix EDNS fallback when EDNS works for short answers but long answers
	  are dropped.

22 June 2009: Wouter
	- fixup iter priv strict aliasing while preserving size of sockaddr.
	- iana portlist updated.  (one less port allocated, one more fraction
	  of a bit for security!)
	- updated fedora specfile in contrib from Paul Wouters.
	
19 June 2009: Wouter
	- Fixup strict aliasing warning in iter priv code.
	  and config_file code.
	- iana portlist updated.
	- harden-referral-path: handle cases where NS is in answer section.

18 June 2009: Wouter
	- Fix of message parse bug where (specifically) an NSEC and RRSIG
	  in the wrong order would be parsed, but put wrongly into internal
	  structures so that later validation would fail.
	- Extreme lenience for wrongly truncated replies where a positive
	  reply has an NS in the authority but no signatures.  They are
	  turned into minimal responses with only the (secure) answer.
	- autoconf 2.63 for configure.
	- python warnings suppress.  Keep python API away from header files.

17 June 2009: Wouter
	- CREDITS entry for cz.nic, sponsoring a 'summer of code' that was
	  used for the python code in unbound. (http://www.nic.cz/vip/ in cz).

16 June 2009: Wouter
	- Fixup opportunistic target query generation to it does not
	  generate queries that are known to fail.
	- Touchup on munin total memory report.
	- messages picked out of the cache by the iterator are checked
	  if their cname chain is still correct and if validation status
	  has to be reexamined.

15 June 2009: Wouter
	- iana portlist updated.

14 June 2009: Wouter
	- Fixed bug where cached responses would lose their security
	  status on second validation, which especially impacted dlv
	  lookups.  Reported by Hauke Lampe.

13 June 2009: Wouter
	- bug #254. removed random whitespace from example.conf.

12 June 2009: Wouter
	- Fixup potential wrong NSEC picked out of the cache.
	- If unfulfilled callbacks are deleted they are called with an error.
	- fptr wlist checks for mesh callbacks.
	- fwd above stub in configuration works.

11 June 2009: Wouter
	- Fix queries for type DS when forward or stub zones are there.
	  They are performed to higherup domains, and thus treated as if
	  going to higher zones when looking up the right forward or stub
	  server.  This makes a stub pointing to a local server that has
	  a local view of example.com signed with the same keys as are
	  publicly used work.  Reported by Johan Ihren.
	- Added build-unbound-localzone-from-hosts.pl to contrib, from
	  Dennis DeDonatis.  It converts /etc/hosts into config statements.
	- same thing fixed for forward-zone and DS, chain of trust from
	  public internet into the forward-zone works now.  Added unit test.

9 June 2009: Wouter
	- openssl key files are opened apache-style, when user is root and
	  before chrooting.  This makes permissions on remote-control key 
	  files easier to set up.  Fixes bug #251.
	- flush_type and flush_name remove msg cache entries.
	- codereview - dp copy bogus setting fix.

8 June 2009: Wouter
	- Removed RFC5011 REVOKE flag support. Partial 5011 support may cause
	  inadvertant behaviour.
	- 1.3.0 tarball for release created.
	- 1.3.1 development in svn trunk.
	- iana portlist updated.
	- fix lint from complaining on ldns/sha.h.
	- help compiler figure out aliasing in priv_rrset_bad() routine.
	- fail to configure with python if swig is not found.
	- unbound_munin_ in contrib uses ps to show rss if sbrk does not work.

3 June 2009: Wouter
	- fixup bad free() when wrongly encoded DSA signature is seen.
	  Reported by Paul Wouters.
	- review comments from Matthijs.

2 June 2009: Wouter
	- --enable-sha2 option. The draft rsasha256 changed its algorithm
	  numbers too often.  Therefore it is more prudent to disable the
	  RSASHA256 and RSASHA512 support by default.
	- ldns trunk included as new tarball.
	- recreated the 1.3.0 tag in svn. rc1 tarball generated at this point.

29 May 2009: Wouter
	- fixup doc bug in README reported by Matthew Dempsky.

28 May 2009: Wouter
	- update iana port list
	- update ldns lib tarball

27 May 2009: Wouter
	- detect lack of IPv6 support on XP (with a different error code).
	- Fixup a crash-on-exit which was triggered by a very long queue.
	  Unbound would try to re-use ports that came free, but this is
	  of course not really possible because everything is deleted.
	  Most easily triggered on XP (not Vista), maybe because of the
	  network stack encouraging large messages backlogs.
	- change in debug statements.
	- Fixed bug that could cause a crash if root prime failed when there
	  were message backlogs.

26 May 2009: Wouter
	- Thanks again to Brett Carr, found an assertion that was not true.
	  Assertion checked if recursion parent query still existed.

29 April 2009: Wouter
	- Thanks to Brett Carr, caught windows resource leak, use 
	  closesocket() and not close() on sockets or else the network stack
	  starts to leak handles.
	- Removed usage of windows Mutex because windows cannot handle enough
	  mutexes open.  Provide own mutex implementation using primitives.

28 April 2009: Wouter
	- created svn tag for 1.3.0.

27 April 2009: Wouter
	- optimised cname from cache.
	- ifdef windows functions in testbound.

23 April 2009: Wouter
	- fix for threadsafety in solaris thr_key_create() in tests.
	- iana portlist updated.
	- fix pylib test for Darwin.
	- fix pymod test for Darwin and a python threading bug in pymod init.
	- check python >= 2.4 in configure.
	- -ldl check for libcrypto 1.0.0beta.

21 April 2009: Wouter
	- fix for build outside sourcedir.
	- fix for configure script swig detection.

17 April 2009: Wouter
	- Fix reentrant in minievent handler for unix. Could have resulted
	  in spurious event callbacks.
	- timers do not take up a fd slot for winsock handler.
	- faster fix for winsock reentrant check.
	- fix rsasha512 unit test for new (interim) algorithm number.
	- fix test:ldns doesn't like DOS line endings in keyfiles on unix.
	- fix compile warning on ubuntu (configlexer fwrite return value).
	- move python include directives into CPPFLAGS instead of CFLAGS.

16 April 2009: Wouter
	- winsock event handler exit very quickly on signal, even if
	  under heavy load.
	- iana portlist updated.
	- fixup windows winsock handler reentrant problem.

14 April 2009: Wouter
	- bug #245: fix munin plugin, perform cleanup of stale lockfiles.
	- makedist.sh; better help text.
	- cache-min-ttl option and tests.
	- mingw detect error condition on TCP sockets (NOTCONN).

9 April 2009: Wouter
	- Fix for removal of RSASHA256_NSEC3 protonumber from ldns.
	- ldns tarball updated.
	- iana portlist update.
	- detect GOST support in openssl-1.0.0-beta1, and fix compile problem
	  because that openssl defines the name STRING for itself.

6 April 2009: Wouter
	- windows compile fix.
	- Detect FreeBSD jail without ipv6 addresses assigned.
	- python libunbound wrapper unit test.
	- installs the following files. Default is to not build them.
	  	from configure --with-pythonmodule:
	  /usr/lib/python2.x/site-packages/unboundmodule.py
	  	from configure --with-pyunbound:
	  /usr/lib/python2.x/site-packages/unbound.py
	  /usr/lib/python2.x/site-packages/_unbound.so*
	  The example python scripts (pythonmod/examples and
	  libunbound/python/examples) are not installed.
	- python invalidate routine respects packed rrset ids and locks.
	- clock skew checks in unbound, config statements.
	- nxdomain ttl considerations in requirements.txt

3 April 2009: Wouter
	- Fixed a bug that caused messages to be stored in the cache too 
	  long.  Hard to trigger, but NXDOMAINs for nameservers or CNAME
	  targets have been more vulnerable to the TTL miscalculation bug. 
	- documentation test fixed for python addition.

2 April 2009: Wouter
	- pyunbound (libunbound python plugin) compiles using libtool.
	- documentation for pythonmod and pyunbound is generated in doc/html.
	- iana portlist updated.
	- fixed bug in unbound-control flush_zone where it would not flush
	  every message in the target domain.  This especially impacted 
	  NXDOMAIN messages which could remain in the cache regardless.
	- python module test package.

1 April 2009: Wouter
	- suppress errors when trying to contact authority servers that gave
	  ipv6 AAAA records for their nameservers with ipv4 mapped contents.
	  Still tries to do so, could work when deployed in intranet.
	  Higher verbosity shows the error.
	- new libunbound calls documented.
	- pyunbound in libunbound/python. Removed compile warnings.
	  Makefile to make it.

30 March 2009: Wouter
	- Fixup LDFLAGS from libevent sourcedir compile configure restore.
	- Fixup so no non-absolute rpaths are added.
	- Fixup validation of RRSIG queries, they are let through.
	- read /dev/random before chroot
	- checkconf fix no python checks when no python module enabled.
	- fix configure, pthread first, so other libs do not change outcome.

27 March 2009: Wouter
	- nicer -h output. report linked libraries and modules.
	- prints modules in intuitive order (config file friendly).
	- python compiles easily on BSD.

26 March 2009: Wouter
	- ignore swig varargs warnings with gcc.
	- remove duplicate example.conf text from python example configs.
	- outofdir compile fix for python.
	- pyunbound works.
	- print modules compiled in on -h. manpage.

25 March 2009: Wouter
	- initial import of the python contribution from Zdenek Vasicek and
	  Marek Vavrusa.
	- pythonmod in Makefile; changes to remove warnings/errors for 1.3.0.

24 March 2009: Wouter
	- more neat configure.ac. Removed duplicate config.h includes.
	- neater config.h.in.
	- iana portlist updated.
	- fix util/configlexer.c and solaris -std=c99 flag.
	- fix postcommit aclocal errors.
	- spaces stripped. Makefile cleaner, /usr omitted from -I, -L, -R.
	- swap order of host detect and libtool generation.

23 March 2009: Wouter
	- added launchd plist example file for MacOSX to contrib.
	- deprecation test for daemon(3).
	- moved common configure actions to m4 include, prettier Makefile.

20 March 2009: Wouter
	- bug #239: module-config entries order is important. Documented.
	- build fix for test asynclook.

19 March 2009: Wouter
	- winrc/README.txt dos-format text file.
	- iana portlist updated.
	- use _beginthreadex() when available (performs stack alignment).
	- defaults for windows baked into configure.ac (used if on mingw).

18 March 2009: Wouter
	- Added tests, unknown algorithms become insecure. fallback works.
	- Fix for and test for unknown algorithms in a trust anchor
	  definition.  Trust anchors with no supported algos are ignored.
	  This means a (higher)DS or DLV entry for them could succeed, and
	  otherwise they are treated as insecure.
	- domain-insecure: "example.com" statement added. Sets domain
	  insecure regardless of chain of trust DSs or DLVs. The inverse
	  of a trust-anchor.

17 March 2009: Wouter
	- unit test for unsupported algorithm in anchor warning.
	- fixed so queries do not fail on opportunistic target queries.

16 March 2009: Wouter
	- fixup diff error printout in contrib/update-itar.sh.
	- added contrib/unbound_cacti for statistics support in cacti,
	  contributed by Dmitriy Demidov.

13 March 2009: Wouter
	- doxygen and lex/yacc on linux.
	- strip update-anchor on makedist -w.
	- fix testbound on windows.
	- default log to syslog for windows.
	- uninstaller can stop unbound - changed text on it to reflect that.
	- remove debugging from windows 'cron' actions.

12 March 2009: Wouter
	- log to App.logs on windows prints executable identity.
	- fixup tests.
	- munin plugin fix benign locking error printout.
	- anchor-update for windows, called every 24 hours; unbound reloads.

11 March 2009: Wouter
	- winsock event handler resets WSAevents after signalled.
	- winsock event handler tests if signals are really signalled.
	- install and service with log to file works on XP and Vista on 
	  default install location.
	- on windows logging to the Application logbook works (as a service).
	- fix RUN_DIR on windows compile setting in makedist.
	- windows registry has Software\Unbound\ConfigFile element.
	  If does not exist, the default is used. The -c switch overrides it.
	- fix makedist version cleanup function.

10 March 2009: Wouter
	- makedist -w strips out old rc.. and snapshot info from version.
	- setup.exe starts and stops unbound after install, before uninstall.
	- unbound-checkconf recognizes absolute pathnames on windows (C:...).

9 March 2009: Wouter
	- Nullsoft NSIS installer creation script.

5 March 2009: Wouter
	- fixup memory leak introduced on 18feb in mesh reentrant fix.

3 March 2009: Wouter
	- combined icon with 16x16(4) 32x32(4) 48x48(8) 64x64(8).
	- service works on xp/vista, no config necessary (using defaults).
	- windows registry settings.

2 March 2009: Wouter
	- fixup --export-symbols to be -export-symbls for libtool.
	  This should fix extraneous symbols exported from libunbound.
	  Thanks to Ondrej Sury and Robert Edmonds for finding it.
	- iana portlist updated.
	- document FAQ entry on stub/forward zones and default blocking.
	- fix asynclook test app for libunbound not exporting symbols.
	- service install and remove utils that work with vista UAC.
		
27 February 2009: Wouter
	- Fixup lexer, to not give warnings about fwrite. Appeared in
	  new lexer features.
	- makedistro functionality for mingw. Has RC support.
	- support spaces and backslashes in configured defaults paths.
	- register, deregister in service control manager.

25 February 2009: Wouter
	- windres usage for application resources.

24 February 2009: Wouter
	- isc moved their dlv key download location.
	- fixup warning on vista/mingw.
	- makedist -w for window zip distribution first version.

20 February 2009: Wouter
	- Fixup contrib/update-itar.sh, the exit codes 1 and 0 were swapped.
	  Nicer script layout.  Added url to site in -h output.

19 February 2009: Wouter
	- unbound-checkconf and unbound print warnings when trust anchors
	  have unsupported algorithms.
	- added contrib/update-itar.sh  This script is similar to
	  update-anchor.sh, and updates from the IANA ITAR repository.
	  You can provide your own PGP key and trust repo, or can use the
	  builtin.  The program uses wget and gpg to work.
	- iana portlist updated.
	- update-itar.sh: using ftp:// urls because https godaddy certificate
	  is not available everywhere and then gives fatal errors.  The 
	  security is provided by pgp signature.

18 February 2009: Wouter
	- more cycle detection. Also for target queries.
	- fixup bug where during deletion of the mesh queries the callbacks
	  that were reentrant caused assertion failures. Keep the mesh in 
	  a reentrant safe state.  Affects libunbound, reload of server,
	  on quit and flush_requestlist.
	- iana portlist updated.

13 February 2009: Wouter
	- forwarder information now per-thread duplicated.
	  This keeps it read only for speed, with no locking necessary.
	- forward command for unbound control to change forwarders to use
	  on the fly.
	- document that unbound-host reads no config file by default.
	- updated iana portlist.

12 February 2009: Wouter
	- call setusercontext if available (on BSD).
	- small refactor of stats clearing.
	- #227: flush_stats feature for unbound-control.
	- stats_noreset feature for unbound-control.
	- flush_requestlist feature for unbound-control.
	- libunbound version upped API (was changed 5 feb).
	- unbound-control status shows if root forwarding is in use.
	- slightly nicer memory management in iter-fwd code.

10 February 2009: Wouter
	- keys with rfc5011 REVOKE flag are skipped and not considered when
	  validating data.
	- iana portlist updated
	- #226: dump_requestlist feature for unbound-control.

6 February 2009: Wouter
	- contrib contains specfile for fedora 1.2.1 (from Paul Wouters).
	- iana portlist updated.
	- fixup EOL in include directive (reported by Paul Wouters).
	  You can no longer specify newlines in the names of included files.
	- config parser changed. Gives some syntax errors closer to where they 
	  occurred. Does not enforce a space after keyword anymore.
	  Does not allow literal newlines inside quoted strings anymore.
	- verbosity level 5 logs customer IP for new requestlist entries.
	- test fix, lexer and cancel test.
	- new option log-time-ascii: yes  if you enable it prints timestamps
	  in the log file as Feb 06 13:45:26 (like syslog does).
	- detect event_base_new in libevent-1.4.1 and later and use it.
	- #231 unbound-checkconf -o option prints that value from config file.
	  Useful for scripting in management scripts and the like.

5 February 2009: Wouter
	- ldns 1.5.0 rc as tarball included.
	- 1.3.0 development continues:
	  change in libunbound API: ub_cancel can return an error, that
	  the async_id did not exist, or that it was already delivered.
	  The result could have been delivered just before the cancel 
	  routine managed to acquire the lock, so a caller may get the
	  result at the same time they call cancel.  For this case, 
	  ub_cancel tries to return an error code.
	  Fixes race condition in ub_cancel() libunbound function.
	- MacOSX Leopard cleaner text output from configure.
	- initgroups(3) is called to drop secondary group permissions, if
	  applicable.
	- configure option --with-ldns-builtin forces the use of the 
	  inluded ldns package with the unbound source.  The -I include
	  is put before the others, so it avoids bad include files from
	  an older ldns install.
	- daemon(3) posix call is used when available.
	- testbound test for older fix added.

4 February 2009: Wouter
	- tag for release 1.2.1.
	- trunk setup for 1.3.0 development.

3 February 2009: Wouter
	- noted feature requests in doc/TODO.
	- printout more detailed errors on ssl certificate loading failures.
	- updated IANA portlist.

16 January 2009: Wouter
	- more quiet about ipv6 network failures, i.e. when ipv6 is not
	  available (network unreachable). Debug still printed on high
	  verbosity.
	- unbound-host -4 and -6 options. Stops annoying ipv6 errors when
	  debugging with unbound-host -4 -d ... 
	- more cycle detection for NS-check, addr-check, root-prime and
	  stub-prime queries in the iterator.  Avoids possible deadlock
	  when priming fails.

15 January 2009: Wouter
	- bug #229: fixup configure checks for compilation with Solaris 
	  Sun cc compiler, ./configure CC=/opt/SUNWspro/bin/cc
	- fixup suncc warnings.
	- fix bug where unbound could crash using libevent 1.3 and older.
	- update testset for recent retry change.

14 January 2009: Wouter
	- 1.2.1 feature: negative caching for failed queries.
	  Queries that failed are cached for 5 seconds (NORR_TTL).
	  If the failure is local, like out of memory, it is not cached.
	- the TTL comparison for the cache used different comparisons,
	  causing many cache responses that used the iterator and validator
	  state machines unnecessarily.
	- retry from 4 to 5 so that EDNS drop retry is part of the first
	  query resolve attempt, and cached error does not stop EDNS fallback.
	- remove debug prints that protect against bad referrals.
	- honor QUIET=no on make commandline (or QUIET=yes ).

13 January 2009: Wouter
	- fixed bug in lameness marking, removed printouts.
	- find NS rrset more cleanly for qtype NS.
	- Moved changes to 1.2.0 for release. Thanks to Mark Zealey for
	  reporting and logs.
	- 1.2.1 feature: stops resolving AAAAs promiscuously when they
	  are in the negative cache.

12 January 2009: Wouter
	- fixed bug in infrastructure lameness cache, did not lowercase
	  name of zone to hash when setting lame.
	- lameness debugging printouts.

9 January 2009: Wouter
	- created svn tag for 1.2.0 release.
	- svn trunk contains 1.2.1 version number.
	- iana portlist updated for todays list.
	- removed debug print.

8 January 2009: Wouter
	- new version of ldns-trunk (today) included as tarball, fixed 
	  bug #224, building with -j race condition.
	- remove possible race condition in the test for race conditions.

7 January 2009: Wouter
	- version 1.2.0 in preparation.
	- feature to allow wildcards (*, ?, [], {}. ~) in trusted-keys-file
	  statements. (Adapted from patch by Paul Wouters).
	- typo fix and iana portlist updated.
	- porting testsuite; unused var warning, and type fixup.

6 January 2009: Wouter
	- fixup packet-of-death when compiled with --enable-debug.
	  A malformed packet could cause an internal assertion failure.
	- added test for HINFO canonicalisation behaviour.
	- fixup reported problem with transparent local-zone data where
	  queries with different type could get nxdomain. Now queries
	  with a different name get resolved normally, with different type
	  get a correct NOERROR/NODATA answer.
	- HINFO no longer downcased for validation, making unbound compatible
	  with bind and ldns.
	- fix reading included config files when chrooted.
	  Give full path names for include files.
	  Relative path names work if the start dir equals the working dir.
	- fix libunbound message transport when no packet buffer is available.

5 January 2009: Wouter
	- fixup getaddrinfo failure handling for remote control port.
	- added L.ROOT-SERVERS.NET. AAAA 2001:500:3::42 to builtin root hints.
	- fixup so it works with libev-3.51 from http://dist.schmorp.de/libev/
	- comm_timer_set performs base_set operation after event_add.

18 December 2008: Wouter
	- fixed bug reported by Duane Wessels: error in DLV lookup, would make
	  some zones that had correct DLV keys as insecure.
	- follows -rc makedist from ldns changes (no _rc).
	- ldns tarball updated with 1.4.1rc for DLV unit test.
	- verbose prints about recursion lame detection and server selection.
	- fixup BSD port for infra host storage. It hashed wrongly.
	- fixup makedist snapshot name generation.
	- do not reopen syslog to avoid dev/log dependency.

17 December 2008: Wouter
	- follows ldns makedist.sh. -rc option. autom4te dir removed.
	- unbound-control status command.
	- extended statistics has a number of ipv6 queries counter.
	  contrib/unbound_munin_ was updated to draw ipv6 in the hits graph.

16 December 2008: Wouter
	- follow makedist improvements from ldns, for maintainers prereleases.
	- snapshot version uses _ not - to help rpm distinguish the
	  version number.

11 December 2008: Wouter
	- better fix for bug #219: use LOG_NDELAY with openlog() call.
	  Thanks to Tamas Tevesz.

9 December 2008: Wouter
	- bug #221 fixed: unbound checkconf checks if key files exist if
	  remote control is enabled. Also fixed NULL printf when not chrooted.
	- iana portlist updated.

3 December 2008: Wouter
	- Fix problem reported by Jaco Engelbrecht where unbound-control stats
	  freezes up unbound if this was compiled without threading, and
	  was using multiple processes.
	- iana portlist updated.
	- test for remote control with interprocess communication.
	- created command distribution mechanism so that remote control
	  commands other than 'stats' work on all processes in a nonthreaded
	  compiled version. dump/load cache work, on the first process.
	- fixup remote control local_data addition memory corruption bug.

1 December 2008: Wouter
	- SElinux policy files in contrib/selinux for the unbound daemon,
	  by Paul Wouters and Adam Tkac.

25 November 2008: Wouter
	- configure complains when --without-ssl is given (bug #220).
	- skip unsupported feature tests on vista/mingw.
	- fixup testcode/streamtcp to work on vista/mingw.
	- root-hints test checks version of dig required.
	- blacklisted servers are polled at a low rate (1%) to see if they
	  come back up. But not if there is some other working server.

24 November 2008: Wouter
	- document that the user of the server daemon needs read privileges
	  on the keys and certificates generated by unbound-control-setup.
	  This is different per system or distribution, usually, running the
	  script under the same username as the server uses suffices.
	  i.e.  sudo -u unbound unbound-control-setup
	- testset port to vista/mingw.
	- tcp_sigpipe to freebsd port.

21 November 2008: Wouter
	- fixed tcp accept, errors were printed when they should not.
	- unbound-control-setup.sh removes read/write permissions other
	  from the keys it creates (as suggested by Dmitriy Demidov).

20 November 2008: Wouter
	- fixup fatal error due to faulty error checking after tcp accept.
	- add check in rlimit to avoid integer underflow.
	- rlimit check with new formula; better estimate for number interfaces
	- nicer comments in rlimit check.
	- tag 1.1.1 created in svn.
	- trunk label is 1.1.2

19 November 2008: Wouter
	- bug #219: fixed so that syslog which delays opening until the first
	  log line is written, gets a log line while not chroot'ed yet.

18 November 2008: Wouter
	- iana portlist updated.
	- removed cast in unit test debug print that was not 64bit safe.
	- trunk back to 1.1.0; copied to tags 1.1.0 release.
	- trunk to has version number 1.1.1 again.
	- in 1.1.1; make clean nicer. grammar in manpage.

17 November 2008: Wouter
	- theoretical fix for problems reported on mailing list.
	  If a delegation point has no A but only AAAA and do-ip6 is no,
	  resolution would fail. Fixed to ask for the A and AAAA records.
	  It has to ask for both always, so that it can fail quietly, from
	  TLD perspective, when a zone is only reachable on one transport.
	- test for above, only AAAA and doip6 is no. Fix causes A record
	  for nameserver to be fetched.
	- fixup address duplication on cache fillup for delegation points.
	- testset updated for new query answer requirements.

14 November 2008: Wouter
	- created 1.1.0 release tag in svn.
	- trunk moved to 1.1.1
	- fixup unittest-neg for locking.

13 November 2008: Wouter
	- added fedora init and specfile to contrib (by Paul Wouters).
	- added configure check for ldns 1.4.0 (using its compat funcs).
	- neater comments in worker.h.
	- removed doc/plan and updated doc/TODO.
	- silenced EHOSTDOWN (verbosity 2 or higher to see it).
	- review comments from Jelte, Matthijs. Neater code.

12 November 2008: Wouter
	- add unbound-control manpage to makedist replace list.

11 November 2008: Wouter
	- unit test for negative cache, stress tests the refcounting.
	- fix for refcounting error that could cause fptr_wlist fatal exit
	  in the negative cache rbtree (upcoming 1.1 feature). (Thanks to 
	  Attila Nagy for testing).
	- nicer comments in cachedump about failed RR to string conversion.
	- fix 32bit wrap around when printing large (4G and more) mem usage
	  for extended statistics.

10 November 2008: Wouter
	- fixup the getaddrinfo compat code rename.

8 November 2008: Wouter
	- added configure check for eee build warning.

7 November 2008: Wouter
	- fix bug 217: fixed, setreuid and setregid do not work on MacOSX10.4.
	- detect nonblocking problems in network stack in configure script.

6 November 2008: Wouter
	- dname_priv must decompress the name before comparison.
	- iana portlist updated.

5 November 2008: Wouter
	- fixed possible memory leak in key_entry_key deletion.
	  Would leak a couple bytes when trust anchors were replaced.
	- if query and reply qname overlap, the bytes are skipped not copied.
	- fixed file descriptor leak when messages were jostled out that
	  had outstanding (TCP) replies.
	- DNAMEs used from cache have their synthesized CNAMEs initialized
	  properly.
	- fixed file descriptor leak for localzone type deny (for TCP).
	- fixed memleak at exit for nsec3 negative cached zones.
	- fixed memleak for the keyword 'nodefault' when reading config.
	- made verbosity of 'edns incapable peer' warning higher, so you
	  do not get spammed by it.
	- caught elusive Bad file descriptor error bug, that would print the
	  error while unnecessarily try to listen to a closed fd. Fixed.

4 November 2008: Wouter
	- fixed -Wwrite-strings warnings that result in better code.

3 November 2008: Wouter
	- fixup build process for Mac OSX linker, use ldns b32 compat funcs.
	- generated configure with autoconf-2.61.
	- iana portlist updated.
	- detect if libssl needs libdl.  For static linking with libssl.
	- changed to use new algorithm identifiers for sha256/sha512
	  from ldns 1.4.0 (need very latest version).
	- updated the included ldns tarball.
	- proper detection of SHA256 and SHA512 functions (not just sizes).

23 October 2008: Wouter
	- a little more debug info for failure on signer names. prints names.

22 October 2008: Wouter
	- CFLAGS are picked up by configure from the environment.
	- iana portlist updated.
	- updated ldns to use 1.4.0-pre20081022 so it picks up CFLAGS too.
	- new stub-prime: yesno option. Default is off, so it does not prime.
	  can be turned on to get same behaviour as previous unbound release.
	- made automated test that checks if builtin root hints are uptodate.
	- finished draft-wijngaards-dnsext-resolver-side-mitigation
	  implementation. The unwanted-reply-threshold can be set.
	- fixup so fptr_whitelist test in alloc.c works.

21 October 2008: Wouter
	- fix update-anchors.sh, so it does not report different RR order
	  as an update.  Sorts the keys in the file.  Updated copyright.
	- fixup testbound on windows, the command control pipe doesn't exist.
	- skip 08hostlib test on windows, no fork() available.
	- made unbound-remote work on windows.

20 October 2008: Wouter
	- quench a log message that is debug only.
	- iana portlist updated.
	- do not query bogus nameservers.  It is like nameservers that have 
	  the NS or A or AAAA record bogus are listed as donotquery.
	- if server selection is faced with only bad choices, it will
	  attempt to get more options to be fetched.
	- changed bogus-ttl default value from 900 to 60 seconds.
	  In anticipation that operator caused failures are more likely than
	  actual attacks at this time.  And thus repeated validation helps
	  the operators get the problem fixed sooner.  It makes validation
	  failures go away sooner (60 seconds after the zone is fixed).
	  Also it is likely to try different nameserver targets every minute,
	  so that if a zone is bad on one server but not another, it is 
	  likely to pick up the 'correct' one after a couple minutes,
	  and if the TTL is big enough that solves validation for the zone.
	- fixup unbound-control compilation on windows.

17 October 2008: Wouter
	- port Leopard/G5: fixup type conversion size_t/uint32.
	  please ranlib, stop file without symbols warning.
	- harden referral path now also validates the root after priming.
	  It looks up the root NS authoritatively as well as the root servers
	  and attemps to validate the entries.

16 October 2008: Wouter
	- Fixup negative TTL values appearing (reported by Attila Nagy).

15 October 2008: Wouter
	- better documentation for 0x20; remove fallback TODO, it is done.
	- harden-referral-path feature includes A, AAAA queries for glue,
	  as well as very careful NS caching (only when doing NS query).
	  A, AAAA use the delegation from the NS-query.

14 October 2008: Wouter
	- fwd_three.tpkg test was flaky.  If the three requests hit the
	  wrong threads by chance (or bad OS) then the test would fail.
	  Made less flaky by increasing number of retries.
	- stub_udp.tpkg changed to work, give root hints. fixed ldns_dname_abs.
	- ldns tarball is snapshot of ldns r2759 (1.4.0-pre-20081014).
	  Which includes the ldns_dname_absolute fix.
	- fwd_three test remains flaky now that unbound does not stop
	  listening when full.  Thus, removed timeout problem.
	  It may be serviced by three threads, or maybe by one.
	  Mostly only useful for lock-check testing now.

13 October 2008: Wouter
	- fixed recursion servers deployed as authoritative detection, so
	  that as a last resort, a +RD query is sent there to get the 
	  correct answer.
	- iana port list update.
	- ldns tarball is snapshot of ldns r2759 (1.4.0-pre-20081013).

10 October 2008: Wouter
	- fixup tests - the negative cache contained the correct NSEC3s for
	  two tests that are supposed to fail to validate.

9 October 2008: Wouter
	- negative cache caps max iterations of NSEC3 done.
	- NSEC3 negative cache for qtype DS works.

8 October 2008: Wouter
	- NSEC negative cache for DS.

6 October 2008: Wouter
	- jostle-timeout option, so you can config for slow links.
	- 0x20 fallback code.  Tries 3xnumber of nameserver addresses
	  queries that must all be the same.  Sent to random nameservers.
	- documented choices for DoS, EDNS, 0x20.

2 October 2008: Wouter
	- fixup unlink of pidfile.
	- fixup SHA256 algorithm collation code.
	- contrib/update-anchor.sh does not overwrite anchors if not needed.
	  exits 0 when a restart is needed, other values if not.
	  so,  update-anchor.sh -d mydir && /etc/rc.d/unbound restart
	  can restart unbound exactly when needed.

30 September 2008: Wouter
	- fixup SHA256 DS downgrade, no longer possible to downgrade to SHA1.
	- tests for sha256 support and downgrade resistance.
	- RSASHA256 and RSASHA512 support (using the draft in dnsext),
	  using the drafted protocol numbers.
	- when using stub on localhost (127.0.0.1@10053) unbound works.
	  Like when running NSD to host a local zone, on the same machine.
	  The noprime feature. manpages more explanation. Added a test for it.
	- shorthand for reverse PTR,  local-data-ptr: "******* www.ex.com"

29 September 2008: Wouter
	- EDNS lameness detection, if EDNS packets are dropped this is
	  detected, eventually.
	- multiple query timeout rtt backoff does not backoff too much.

26 September 2008: Wouter
	- tests for remote-control.
	- small memory leak in exception during remote control fixed.
	- fixup for lock checking but not unchecking in remote control.
	- iana portlist updated.

23 September 2008: Wouter
	- Msg cache is loaded. A cache load enables cache responses.
	- unbound-control flush [name], flush_type and flush_zone.

22 September 2008: Wouter
	- dump_cache and load_cache statements in unbound-control.
	  RRsets are dumped and loaded correctly.
	  Msg cache is dumped.

19 September 2008: Wouter
	- locking on the localdata structure.
	- add and remove local zone and data with unbound-control.
	- ldns trunk snapshot updated, make tests work again.

18 September 2008: Wouter
	- fixup error in time calculation.
	- munin plugin improvements.
	- nicer abbreviations for high query types values (ixfr, axfr, any...)
	- documented the statistics output in unbound-control man page.
	- extended statistics prints out histogram, over unbound-control.

17 September 2008: Wouter
	- locking for threadsafe bogus rrset counter.
	- ldns trunk no longer exports b32 functions, provide compat.
	- ldns tarball updated.
	- testcode/ldns-testpkts.c const fixups.
	- fixed rcode stat printout.
	- munin plugin in contrib.
	- stats always printout uptime, because stats plugins need it.

16 September 2008: Wouter
	- extended-statistics: yesno config option.
	- unwanted replies spoof nearmiss detector.
	- iana portlist updated.

15 September 2008: Wouter
	- working start, stop, reload commands for unbound-control.
	- test for unbound-control working; better exit value for control.
	- verbosity control via unbound-control.
	- unbound-control stats.

12 September 2008: Wouter
	- removed browser control mentions. Proto speccy.

11 September 2008: Wouter
	- set nonblocking on new TCP streams, because linux does not inherit
	  the socket options to the accepted socket.
	- fix TCP timeouts.
	- SSL protected connection between server and unbound-control.

10 September 2008: Wouter
	- remove memleak in privacy addresses on reloads and quits.
	- remote control work.

9 September 2008: Wouter
	- smallapp/unbound-control-setup.sh script to set up certificates.

4 September 2008: Wouter
	- scrubber scrubs away private addresses.
	- test for private addresses. man page entry.
	- code refactored for name and address tree lookups.

3 September 2008: Wouter
	- options for 'DNS Rebinding' protection: private-address and
	  private-domain.
	- dnstree for reuse of routines that help with domain, addr lookups.
	- private-address and private-domain config option read, stored.

2 September 2008: Wouter
	- DoS protection features. Queries are jostled out to make room.
	- testbound can pass time, increasing the internal timer.
	- do not mark unsigned additionals bogus, leave unchecked, which
	  is removed too.

1 September 2008: Wouter
	- disallow nonrecursive queries for cache snooping by default.
	  You can allow is using access-control: <subnet> allow_snoop.
	  The defaults do allow access no authoritative data without RD bit.
	- two tests for it and fixups of tests for nonrec refused.

29 August 2008: Wouter
	- version 1.1 number in trunk.
	- harden-referral-path option for query for NS records.
	  Default turns off expensive, experimental option.

28 August 2008: Wouter
	- fixup logfile handling; it is created with correct permissions
	  again. (from bugfix#199).
	  Some errors are not written to logfile (pidfile writing, forking),
	  and these are only visible by using the -d commandline flag.

27 August 2008: Wouter
	- daemon(3) is causing problems for people. Reverting the patch.
	  bug#200, and 199 and 203 contain sideline discussion on it.
	- bug#199 fixed: pidfile can be outside chroot. openlog is done before
	  chroot and drop permissions.
	- config option to set size of aggressive negative cache,
	  neg-cache-size.
	- bug#203 fixed: dlv has been implemented.

26 August 2008: Wouter
	- test for insecure zone when DLV is in use, also does negative cache.
	- test for trustanchor when DLV is in use (the anchor works).
	- test for DLV used for a zone below a trustanchor.
	- added scrub filter for overreaching NSEC records and unit test.
	- iana portlist update
	- use of setresuid or setreuid when available.
	- use daemon(3) if available.

25 August 2008: Wouter
	- realclean patch from Robert Edmonds.

22 August 2008: Wouter
	- nicer debuglogging of DLV.
	- test with secure delegation inside the DLV repository.

21 August 2008: Wouter
	- negative cache code linked into validator, for DLV use.
	  negative cache works for DLV.
	- iana portlist update.
	- dlv-anchor option for unit tests.
	- fixup NSEC_AT_APEX classification for short typemaps.
	- ldns-testns has subdomain checks, for unit tests.

20 August 2008: Wouter
	- negative cache code, reviewed.

18 August 2008: Wouter
	- changes info: in logfile to notice: info: or debug: depending on 
	  the verbosity of the statements.  Better logfile message
	  classification.
	- bug #208: extra rc.d unbound flexibility for freebsd/nanobsd.

15 August 2008: Wouter
	- DLV nsec code fixed for better detection of closest existing 
	  enclosers from NSEC responses.
	- DLV works, straight to the dlv repository, so not for production.
	- Iana port update.

14 August 2008: Wouter
	- synthesize DLV messages from the rrset cache, like done for DS.

13 August 2008: Wouter
	- bug #203: nicer do-auto log message when user sets incompatible
	  options.
	- bug #204: variable name ameliorated in log.c.
	- bug #206: in iana_update, no egrep, but awk use.
	- ldns snapshot r2699 taken (includes DLV type).
	- DLV work, config file element, trust anchor read in.

12 August 2008: Wouter
	- finished adjusting testset to provide qtype NS answers.

11 August 2008: Wouter
	- Fixup rrset security updates overwriting 2181 trust status.
	  This makes validated to be insecure data just as worthless as
	  nonvalidated data, and 2181 rules prevent cache overwrites to them.
	- Fix assertion fail on bogus key handling.
	- dnssec lameness detection works on first query at trust apex.
	- NS queries get proper cache and dnssec lameness treatment.
	- fixup compilation without pthreads on linux.

8 August 2008: Wouter
	- NS queries are done after every referral.
	  validator is used on those NS records (if anchors enabled).

7 August 2008: Wouter
	- Scrubber more strict. CNAME chains, DNAMEs from cache, other 
	  irrelevant rrsets removed.
	- 1.0.2 released from 1.0 support branch.
	- fixup update-anchor.sh to work both in BSD shell and bash.

5 August 2008: Wouter
	- fixup DS test so apex nodata works again.

4 August 2008: Wouter
	- iana port update. 
	- TODO update.
	- fix bug 201: null ptr deref on cleanup while udp pkts wait for port.
	- added explanatory text for outgoing-port-permit in manpage.

30 July 2008: Wouter
	- fixup bug qtype DS for unsigned zone and signed parent validation.

25 July 2008: Wouter
	- added original copyright statement of OpenBSD arc4random code.
	- created tube signaling solution on windows, as a pipe replacement.
	  this makes background asynchronous resolution work on windows.
	- removed very insecure socketpair compat code. It also did not
	  work with event_waiting. Solved by pipe replacement.
	- unbound -h prints openssl version number as well.

22 July 2008: Wouter
	- moved pipe actions to util/tube.c. easier porting and shared code.
	- check _raw() commpoint callbacks with fptr_wlist.
	- iana port update.

21 July 2008: Wouter
	- #198: nicer entropy warning message. manpage OS hints.

19 July 2008: Wouter
	- #198: fixup man page to suggest chroot entropy fix.

18 July 2008: Wouter
	- branch for 1.0 support.
	- trunk work on tube.c.

17 July 2008: Wouter
	- fix bug #196, compile outside source tree.
	- fix bug #195, add --with-username=user configure option.
	- print error and exit if started with config that requires more
	  fds than the builtin minievent can handle.

16 July 2008: Wouter
	- made svn tag 1.0.1, trunk now 1.0.2
	- sha256 checksums enabled in makedist.sh

15 July 2008: Wouter
	- Follow draft-ietf-dnsop-default-local-zones-06 added reverse
	  IPv6 example prefix to AS112 default blocklist.
	- fixup lookup of DS records by client with trustanchor for same.
	- libunbound ub_resolve, fix handling of error condition during setup.
	- lowered log_hex blocksize to fit through BSD syslog linesize.
	- no useless initialisation if getpwnam not available.
	- iana, ldns snapshot updated.

3 July 2008: Wouter
	- Matthijs fixed memory leaks in root hints file reading.

26 June 2008: Wouter
	- fixup streamtcp bounds setting for udp mode, in the test framework.
	- contrib item for updating trust anchors.

25 June 2008: Wouter
	- fixup fwd_ancil test typos.
	- Fix for newegg lameness : ok for qtype=A, but lame for others.
	- fixup unit test for infra cache, test lame merging.
	- porting to mingw, bind, listen, getsockopt and setsockopt error
	  handling.

24 June 2008: Wouter
	- removed testcode/checklocks from production code compilation path.
	- streamtcp can use UDP mode (connected UDP socket), for testing IPv6
	  on windows.
	- fwd_ancil test fails if platform support is lacking.

23 June 2008: Wouter
	- fixup minitpkg to cleanup on windows with its file locking troubles.
	- minitpkg shows skipped tests in report.
	- skip ipv6 tests on ipv4 only hosts (requires only ipv6 localhost not
	  ipv6 connectivity).
	- winsock event handler keeps track of sticky TCP events, that have
	  not been fully handled yet. when interest in the event(s) resumes,
	  they are sent again. When WOULDBLOCK is returned events are cleared.
	- skip tests that need signals when testing on mingw.

18 June 2008: Wouter
	- open testbound replay files in binary mode, because fseek/ftell
	  do not work in ascii-mode on windows. The b does nothing on unix.
	  unittest and testbound tests work on windows (xp too).
	- ioctlsocket prints nicer error message.
	- fixed up some TCP porting for winsock.
	- lack of IPv6 gives a warning, no fatal error.
	- use WSAGetLastError() on windows instead of errno for some errors.

17 June 2008: Wouter
	- outgoing num fds 32 by default on windows ; it supports less
	  fds for waiting on than unixes.
	- winsock_event minievent handler for windows. (you could also
	  attempt to link with libevent/libev ports for windows).
	- neater crypto check and gdi32 detection.
	- unbound.exe works to resolve and validate www.nlnetlabs.nl on vista.

16 June 2008: Wouter
	- on windows, use windows threads, mutex and thread-local-storage(Tls).
	- detect if openssl needs gdi32.
	- if no threading, THREADS_DISABLED is defined for use in the code.
	- sets USE_WINSOCK if using ws2_32 on windows.
	- wsa_strerror() function for more readable errors.
	- WSA Startup and Cleanup called in unbound.exe.

13 June 2008: Wouter
	- port mingw32, more signal ifdefs, detect sleep, usleep, 
	  random, srandom (used inside the tests).
	- signed or unsigned FD_SET is cast.

10 June 2008: Wouter
	- fixup warnings compiling on eeepc xandros linux.

9 June 2008: Wouter
	- in iteration response type code
	  * first check for SOA record (negative answer) before NS record
	    and lameness.
	  * check if no AA bit for non-forwarder, and thus lame zone.
	    In response to error report by Richard Doty for mail.opusnet.com.
	- fixup unput warning from lexer on freeBSD.
	- bug#183. pidfile, rundir, and chroot configure options. Also the
	  example.conf and manual pages get the configured defaults.
	  You can use: (or accept the defaults to /usr/local/etc/unbound/)
	  --with-conf-file=filename
	  --with-pidfile=filename
	  --with-run-dir=path
	  --with-chroot-dir=path

8 June 2008: Wouter
	- if multiple CNAMEs, use the first one. Fixup akamai CNAME bug.
	  Reported by Robert Edmonds.
	- iana port updated.

4 June 2008: Wouter
	- updated libtool files with newer version.
	- iana portlist updated.

3 June 2008: Wouter
	- fixup local-zone: "30.172.in-addr.arpa." nodefault, so that the
	  trailing dot is not used during comparison.

2 June 2008: Wouter
	- Jelte fixed bugs in my absence
	  - bug 178: fixed unportable shell usage in configure (relied on 
	    bash shell).
	  - bug 180: fixed buffer overflow in unbound-checkconf use of strncat.
	  - bug 181: fixed buffer overflow in ldns (called by unbound to parse
	    config file parts).
	- fixes by Wouter
	  - bug 177: fixed compilation failure on opensuse, the 
	    --disable-static configure flag caused problems.  (Patch from 
	    Klaus Singvogel)
	  - bug 179: same fix as 177.
	  - bug 185: --disable-shared not passed along to ldns included with 
	    unbound. Fixed so that configure parameters are passed to the
	    subdir configure script.
	    fixed that ./libtool is used always, you can still override
	    manually with ./configure libtool=mylibtool or set $libtool in
	    the environment.
	- update of the ldns tarball to current ldns svn version (fix 181).
	- bug 184: -r option for unbound-host, read resolv.conf for 
	  forwarder. (Note that forwarder must support DNSSEC for validation
	  to succeed).

23 May 2008: Wouter
	- mingw32 porting.
	  - test for sys/wait.h
	  - WSAEWOULDBLOCK test after nonblocking TCP connect.
	  - write_iov_buffer removed: unused and no struct iov on windows.
	  - signed/unsigned warning fixup mini_event.
	  - use ioctlsocket to set nonblocking I/O if fnctl is unavailable.
	  - skip signals that are not defined
	  - detect pwd.h.
	  - detect getpwnam, getrlimit, setsid, sbrk, chroot.
	  - default config has no chroot if chroot() unavailable.
	  - if no kill() then no pidfile is read or written.
	  - gmtime_r is replaced by nonthreadsafe alternative if unavail.
	    used in rrsig time validation errors.

22 May 2008: Wouter
	- contrib unbound.spec from Patrick Vande Walle.
	- fixup bug#175: call tzset before chroot to have correct timestamps
	  in system log.
	- do not generate lex input and lex unput functions.
	- mingw port. replacement functions labelled _unbound.
	- fix bug 174 - check for tcp_sigpipe that ldns-testns is installed.

19 May 2008: Wouter
	- fedora 9, check in6_pktinfo define in configure.
	- CREDITS fixup of history.
	- ignore ldns-1.2.2 if installed, use builtin 1.3.0-pre alternative.

16 May 2008: Wouter
	- fixup for MacOSX hosts file reading (reported by John Dickinson).
	- created 1.0.0 svn tag.
	- trunk version 1.0.1.

14 May 2008: Wouter
	- accepted patch from Ondrej Sury for library version libtool option.
	- configure --disable-rpath fixes up libtool for rpath trouble.
	  Adapted from debian package patch file.

13 May 2008: Wouter
	- Added root ipv6 addresses to builtin root hints.
	- TODO modified for post 1.0 plans.
	- trunk version set to 1.0.0.
	- no unnecessary linking with librt (only when libevent/libev used).

7 May 2008: Wouter
	- fixup no-ip4 problem with error callback in outside network.

25 April 2008: Wouter
	- DESTDIR is honored by the Makefile for rpms.
	- contrib files unbound.spec and unbound.init, builds working RPM
	  on FC7 Linux, a chrooted caching resolver, and libunbound.
	- iana ports update.

24 April 2008: Wouter
	- chroot checks improved. working directory relative to chroot.
	  checks if config file path is inside chroot. Documentation on it.
	- nicer example.conf text.
	- created 0.11 tag.

23 April 2008: Wouter
	- parseunbound.pl contrib update from Kai Storbeck for threads.
	- iana ports update

22 April 2008: Wouter
	- ignore SIGPIPE.
	- unit test for SIGPIPE ignore.

21 April 2008: Wouter
	- FEATURES document.
	- fixup reread of config file if it was given as a full path
	  and chroot was used.

16 April 2008: Wouter
	- requirements doc, updated clean query returns.
	- parseunbound.pl update from Kai Storbeck.
	- sunos4 porting changes.

15 April 2008: Wouter
	- fixup default rc.d pidfile location to /usr/local/etc.
	- iana ports updated.
	- copyright updated in ldns-testpkts to keep same as in ldns.
	- fixup checkconf chroot tests a bit more, chdir must be inside
	  chroot dir.
	- documented 'gcc: unrecognized -KPIC option' errors on Solaris.
	- example.conf values changed to /usr/local/etc/unbound
	- DSA test work.
	- DSA signatures: unbound is compatible with both encodings found.
	  It will detect and convert when necessary.

14 April 2008: Wouter
	- got update for parseunbound.pl statistics script from Kai Storbeck.
	- tpkg tests for udp wait list.
	- documented 0x20 status.
	- fixup chroot and checkconf, it is much smarter now.
	- fixup DSA EVP signature decoding. Solution that Jelte found copied.
	- and check first sig byte for the encoding type.

11 April 2008: Wouter
	- random port selection out of the configged ports.
	- fixup threadsafety for libevent-1.4.3+ (event_base_get_method).
	- removed base_port.
	- created 256-port ephemeral space for the OS, 59802 available.
	- fixup consistency of port_if out array during heavy use.

10 April 2008: Wouter
	- --with-libevent works with latest libevent 1.4.99-trunk.
	- added log file statistics perl script to contrib.
	- automatic iana ports update from makefile. 60058 available.

9 April 2008: Wouter
	- configure can detect libev(from its build directory) when passed
	  --with-libevent=/home/<USER>/libev-3.2
	  libev-3.2 is a little faster than libevent-1.4.3-stable (about 5%).
	- unused commpoints not listed in epoll list.
	- statistics-cumulative option so that the values are not reset.
	- config creates array of available ports, 61841 available,
	  it excludes <1024 and iana assigned numbers.
	  config statements to modify the available port numbers.

8 April 2008: Wouter
	- unbound tries to set the ulimit fds when started as server.
	  if that does not work, it will scale back its requirements.

27 March 2008: Wouter
	- documented /dev/random symlink from chrootdir as FAQ entry.

26 March 2008: Wouter
	- implemented AD bit signaling. If a query sets AD bit (but not DO)
	  then the AD bit is set in the reply if the answer validated.
	  Without including DNSSEC signatures. Useful if you have a trusted
	  path from the client to the resolver. Follows dnssec-updates draft.

25 March 2008: Wouter
	- implemented check that for NXDOMAIN and NOERROR answers a query
	  section must be present in the reply (by the scrubber). And it must
	  be equal to the question sent, at least lowercase folded.
	  Previously this feature happened because the cache code refused
	  to store such messages. However blocking by the scrubber makes 
	  sure nothing gets into the RRset cache. Also, this looks like a
	  timeout (instead of an allocation failure) and this retries are
	  done (which is useful in a spoofing situation).
	- RTT banding. Band size 400 msec, this makes band around zero (fast)
	  include unknown servers. This makes unbound explore unknown servers.

7 March 2008: Wouter
	- -C config feature for harvest program. 
	- harvest handles CNAMEs too.

5 March 2008: Wouter
	- patch from Hugo Koji Kobayashi for iterator logs spelling.

4 March 2008: Wouter
	- From report by Jinmei Tatuya, rfc2181 trust value for remainder
	  of a cname trust chain is lower; not full answer_AA. 
	- test for this fix.
	- default config file location is /usr/local/etc/unbound.
	  Thus prefix is used to determine the location. This is also the
	  chroot and pidfile default location.

3 March 2008: Wouter
	- Create 0.10 svn tag.
	- 0.11 version in trunk.
	- indentation nicer.

29 February 2008: Wouter
	- documentation update.
	- fixup port to Solaris of perf test tool.
	- updated ldns-tarball with decl-after-statement fixes.

28 February 2008: Wouter
	- fixed memory leaks in libunbound (during cancellation and wait).
	- libunbound returns the answer packet in full.
	- snprintf compat update.
	- harvest performs lookup.
	- ldns-tarball update with fix for ldns_dname_label.
	- installs to sbin by default.
	- install all manual pages (unbound-host and libunbound too).

27 February 2008: Wouter
	- option to use caps for id randomness.
	- config file option use-caps-for-id: yes
	- harvest debug tool

26 February 2008: Wouter
	- delay utility delays TCP as well. If the server that is forwarded 
	  to has a TCP error, the delay utility closes the connection.
	- delay does REUSE_ADDR, and can handle a server that closes its end.
	- answers use casing from query.

25 February 2008: Wouter
	- delay utility works. Gets decent thoughput too (>20000).

22 February 2008: Wouter
	- +2% for recursions, if identical queries (except for destination
	  and query ID) in the reply list, avoid re-encoding the answer.
	- removed TODO items for optimizations that do not show up in
	  profile reports.
	- default is now minievent - not libevent. As its faster and
	  not needed for regular installs, only for very large port ranges.
	- loop check different speedup pkt-dname-reading, 1% faster for
	  nocache-recursion check.
	- less hashing during msg parse, 4% for recursion.
	- small speed fix for dname_count_size_labels, +1 or +2% recursion.
	- some speed results noted:
	  optimization resulted in +40% for recursion (cache miss) and
	  +70 to +80 for cache hits, and +96% for version.bind.
	  zone nsec3 example, 100 NXDOMAIN queries, NSD 35182.8 Ub 36048.4
	  www.nlnetlabs.nl from cache: BIND 8987.99 Ub 31218.3
	  www with DO bit set : BIND 8269.31 Ub 28735.6 qps.
	  So, unbound can be about equal qps to NSD in cache hits.
	  And about 3.4x faster than BIND in cache performance.
	- delay utility for testing.

21 February 2008: Wouter
	- speedup of root-delegation message encoding by 15%.
	- minor speedup of compress tree_lookup, maybe 1%.
	- speedup of dname_lab_cmp and memlowercmp - the top functions in 
	  profiler output, maybe a couple percent when it matters.

20 February 2008: Wouter
	- setup speec_cache for need-ldns-testns in dotests.
	- check number of queued replies on incoming queries to avoid overload
	  on that account.
	- fptr whitelist checks are not disabled in optimize mode.
	- do-daemonize config file option.
	- minievent time share initializes time at start.
	- updated testdata for nsec3 new algorithm numbers (6, 7).
	- small performance test of packet encoding (root delegation).

19 February 2008: Wouter
	- applied patch to unbound-host man page from Jan-Piet Mens.
	- fix donotquery-localhost: yes default (it erroneously was switched
	  to default 'no').
	- time is only gotten once and the value is shared across unbound.
	- unittest cleans up crypto, so that it has no memory leaks.
	- mini_event shares the time value with unbound this results in 
	  +3% speed for cache responses and +9% for recursions.
	- ldns tarball update with new NSEC3 sign code numbers.
	- perform several reads per UDP operation. This improves performance
	  in DoS conditions, and costs very little in normal conditions.
	  improves cache response +50%, and recursions +10%.
	- modified asynclook test. because the callback from async is not
	  in any sort of lock (and thus can use all library functions freely),
	  this causes a tiny race condition window when the last lock is 
	  released for a callback and a new cancel() for that callback.
	  The only way to remove this is by putting callbacks into some 
	  lock window. I'd rather have the small possibility of a callback
	  for a cancelled function then no use of library functions in 
	  callbacks. Could be possible to only outlaw process(), wait(),
	  cancel() from callbacks, by adding another lock, but I'd rather not.

18 February 2008: Wouter
	- patch to unbound-host from Jan-Piet Mens.
	- unbound host prints errors if fails to configure context.
	- fixup perf to resend faster, so that long waiting requests do
	  not hold up the queue, they become lost packets or SERVFAILs,
	  or can be sent a little while later (i.e. processing time may 
	  take long, but throughput has to be high).
	- fixup iterator operating in no cache conditions (RD flag unset
	  after a CNAME).
	- streamlined code for RD flag setting.
	- profiled code and changed dname compares to be faster. 
	  The speedup is about +3% to +8% (depending on the test).
	- minievent tests for eintr and eagain.

15 February 2008: Wouter
	- added FreeBSD rc.d script to contrib.
	- --prefix option for configure also changes directory: pidfile:
	  and chroot: defaults in config file.
	- added cache speed test, for cache size OK and cache too small.

14 February 2008: Wouter
	- start without a config file (will complain, but start with
	  defaults).
	- perf test program works.

13 February 2008: Wouter
	- 0.9 released.
	- 1.0 development. Printout ldns version on unbound -h.
	- start of perf tool.
	- bugfix to read empty lines from /etc/hosts.

12 February 2008: Wouter
	- fixup problem with configure calling itself if ldns-src tarball
	  is not present.

11 February 2008: Wouter
	- changed library to use ub_ instead of ub_val_ as prefix.
	- statistics output text nice.
	- etc/hosts handling.
	- library function to put logging to a stream.
	- set any option interface.

8 February 2008: Wouter
	- test program for multiple queries over a TCP channel.
	- tpkg test for stream tcp queries.
	- unbound replies to multiple TCP queries on a TCP channel.
	- fixup misclassification of root referral with NS in answer
	  when validating a nonrec query.
	- tag 0.9
	- layout of manpages, spelling fix in header, manpages process by
	  makedist, list asynclook and tcpstream tests as ldns-testns
	  required.

7 February 2008: Wouter
	- moved up all current level 2 to be level 3. And 3 to 4.
	  to make room for new debug level 2 for detailed information 
	  for operators.
	- verbosity level 2. Describes recursion and validation.
	- cleaner configure script and fixes for libevent solaris.
	- signedness for log output memory sizes in high verbosity.

6 February 2008: Wouter
	- clearer explanation of threading configure options.
	- fixup asynclook test for nothreading (it creates only one process
	  to do the extended test).
	- changed name of ub_val_result_free to ub_val_resolve_free.
	- removes warning message during library linking, renamed
	  libunbound/unbound.c -> libunbound.c and worker to libworker.
	- fallback without EDNS if result is NOTIMPL as well as on FORMERR.

5 February 2008: Wouter
	- statistics-interval: seconds option added.
	- test for statistics option
	- ignore errors making directories, these can occur in parallel builds
	- fixup Makefile strip command and libunbound docs typo.

31 January 2008: Wouter
	- bg thread/process reads and writes the pipe nonblocking all the time
	  so that even if the pipe is buffered or so, the bg thread does not
	  block, and services both pipes and queries.

30 January 2008: Wouter
	- check trailing / on chrootdir in checkconf.
	- check if root hints and anchor files are in chrootdir.
	- no route to host tcp error is verbosity level 2. 
	- removed unused send_reply_iov. and its configure check.
	- added prints of 'remote address is ******* port 53' to errors
	  from netevent; the basic socket errors.

28 January 2008: Wouter
	- fixup uninit use of buffer by libunbound (query id, flags) for
	  local_zone answers.
	- fixup uninit warning from random.c; also seems to fix sporadic
	  sigFPE coming out of openssl.
	- made openssl entropy warning more silent for library use. Needs
	  verbosity 1 now.
	- fixup forgotten locks for rbtree_searches on ctx->query tree.
	- random generator cleanup - RND_STATE_SIZE removed, and instead
	  a super-rnd can be passed at init to chain init random states.
	- test also does lock checks if available.
	- protect config access in libworker_setup().
	- libevent doesn't like comm_base_exit outside of runloop.
	- close fds after removing commpoints only (for epoll, kqueue).

25 January 2008: Wouter
	- added tpkg for asynclook and library use. 
	- allows localhost to be queried when as a library.
	- fixup race condition between cancel and answer (in case of
	  really fast answers that beat the cancel).
	- please doxygen, put doxygen comment in one place.
	- asynclook -b blocking mode and test.
	- refactor asynclook, nicer code.
	- fixup race problems from opensll in rand init from library, with
	  a mutex around the rand init.
	- fix pass async_id=NULL to _async resolve().
	- rewrote _wait() routine, so that it is threadsafe.
	- cancelation is threadsafe.
	- asynclook extended test in tpkg.
	- fixed two races where forked bg process waits for (somehow shared?)
	  locks, so does not service the query pipe on the bg side.
	  Now those locks are only held for fg_threads and for bg_as_a_thread.

24 January 2008: Wouter
	- tested the cancel() function.
	- asynclook -c (cancel) feature.
	- fix fail to allocate context actions.
	- make pipe nonblocking at start.
	- update plane for retry mode with caution to limit bandwidth.
	- fix Makefile for concurrent make of unbound-host.
	- renamed ub_val_ctx_wait/poll/process/fd to ub_val*.
	- new calls to set forwarding added to header and docs.

23 January 2008: Wouter
	- removed debug prints from if-auto, verb-algo enables some.
	- libunbound QUIT setup, remove memory leaks, when using threads
	  will share memory for passing results instead of writing it over
	  the pipe, only writes ID number over the pipe (towards the handler
	  thread that does process() ).

22 January 2008: Wouter
	- library code for async in libunbound/unbound.c.
	- fix link testbound.
	- fixup exit bug in mini_event.
	- background worker query enter and result functions.
	- bg query test application asynclook, it looks up multiple
	  hostaddresses (A records) at the same time.

21 January 2008: Wouter
	- libworker work, netevent raw commpoints, write_msg, serialize.

18 January 2008: Wouter
	- touch up of manpage for libunbound.
	- support for IP_RECVDSTADDR (for *BSD ip4).
	- fix for BSD, do not use ip4to6 mapping, make two sockets, once
	  ip6 and once ip4, uses socket options.
	- goodbye ip4to6 mapping.
	- update ldns-testpkts with latest version from ldns-trunk.
	- updated makedist for relative ldns pathnames.
	- library API with more information inside the result structure.
	- work on background resolves.

17 January 2008: Wouter
	- fixup configure in case -lldns is installed.
	- fixup a couple of doxygen warnings, about enum variables.
	- interface-automatic now copies the interface address from the
	  PKT_INFO structure as well.
	- manual page with library API, all on one page 'man libunbound'.
	- rewrite of PKTINFO structure, it also captures IP4 PKTINFO.

16 January 2008: Wouter
	- incoming queries to the server with TC bit on are replied FORMERR.
	- interface-automatic replied the wrong source address on localhost
	  queries. Seems to be due to ifnum=0 in recvmsg PKTINFO. Trying
	  to use ifnum=-1 to mean 'no interface, use kernel route'.

15 January 2008: Wouter
	- interface-automatic feature. experimental. Nice for anycast.
	- tpkg test for ip6 ancillary data.
	- removed debug prints.
	- porting experience, define for Solaris, test refined for BSD
	  compatibility. The feature probably will not work on OpenBSD.
	- makedist fixup for ldns-src in build-dir.

14 January 2008: Wouter
	- in no debug sets NDEBUG to remove asserts.
	- configure --enable-debug is needed for dependency generation
	  for assertions and for compiler warnings.
	- ldns.tgz updated with ldns-trunk (where buffer.h is updated).
	- fix lint, unit test in optimize mode.
	- default access control allows ::ffff:127.0.0.1 v6mapped localhost.
	
11 January 2008: Wouter
	- man page, warning removed.
	- added text describing the use of stub zones for private zones.
	- checkconf tests for bad hostnames (IP address), and for doubled
	  interface lines.
	- memory sizes can be given with 'k', 'Kb', or M or G appended.

10 January 2008: Wouter
	- typo in example.conf.
	- made using ldns-src that is included the package more portable
	  by linking with .lo instead of .o files in the ldns package.
	- nicer do-ip6: yes/no documentation.
	- nicer linking of libevent .o files.
	- man pages render correctly on solaris.

9 January 2008: Wouter
	- fixup openssl RAND problem, when the system is not configured to
	  give entropy, and the rng needs to be seeded.

8 January 2008: Wouter
	- print median and quartiles with extensive logging.

4 January 2008: Wouter
	- document misconfiguration in private network.

2 January 2008: Wouter
	- fixup typo in requirements.
	- document that 'refused' is a better choice than 'drop' for 
	  the access control list, as refused will stop retries.

7 December 2007: Wouter
	- unbound-host has a -d option to show what happens. This can help
	  with debugging (why do I get this answer).
	- fixup CNAME handling, on nodata, sets and display canonname.
	- dot removed from CNAME display.
	- respect -v for NXDOMAINs.
	- updated ldns-src.tar.gz with ldns-trunk today (1.2.2 fixes).
	- size_t to int for portability of the header file.
	- fixup bogus handling.
	- dependencies and lint for unbound-host.

6 December 2007: Wouter
	- library resolution works in foreground mode, unbound-host app
	  receives data.
	- unbound-host prints rdata using ldns.
	- unbound-host accepts trust anchors, and prints validation
	  information when you give -v.

5 December 2007: Wouter
	- locking in context_new() inside the function.
	- setup of libworker.

4 December 2007: Wouter
	- minor Makefile fixup.
	- moved module-stack code out of daemon/daemon into services/modstack,
	  preparing for code-reuse.
	- move context into own header file.
	- context query structure.
	- removed unused variable pwd from checkconf.
	- removed unused assignment from outside netw.
	- check timeval length of string.
	- fixup error in val_utils getsigner.
	- fixup same (*var) error in netblocktostr.
	- fixup memleak on parse error in localzone.
	- fixup memleak on packet parse error.
	- put ; after union in parser.y.
	- small hardening in iter_operate against iq==NULL.
	- hardening, if error reply with rcode=0 (noerror) send servfail.
	- fixup same (*var) error in find_rrset in msgparse, was harmless.
	- check return value of evtimer_add().
	- fixup lockorder in lruhash_reclaim(), building up a list of locked
	  entries one at a time. Instead they are removed and unlocked.
	- fptr_wlist for markdelfunc.
	- removed is_locked param from lruhash delkeyfunc.
	- moved bin_unlock during bin_split purely to please.

3 December 2007: Wouter
	- changed checkconf/ to smallapp/ to make room for more support tools.
	  (such as unbound-host).
	- install dirs created with -m 755 because they need to be accessible.
	- library extensive featurelist added to TODO.
	- please doxygen, lint.
	- library test application, with basic functionality.
	- fix for building in a subdirectory. 
	- link lib fix for Leopard.

30 November 2007: Wouter
	- makefile that creates libunbound.la, basic file or libunbound.a
	  when creating static executables (no libtool).
	- more API setup.

29 November 2007: Wouter
	- 0.9 public API start.

28 November 2007: Wouter
	- Changeup plan for 0.8 - no complication needed, a simple solution
	  has been chosen for authoritative features.
	- you can use single quotes in the config file, so it is possible
	  to specify TXT records in local data.
	- fixup small memory problem in implicit transparent zone creation.
	- test for implicit zone creation and multiple RR RRsets local data.
	- local-zone nodefault test.
	- show testbound testlist on commit.
	- iterator normalizer changes CNAME chains ending in NXDOMAIN where
	  the packet got rcode NXDOMAIN into rcode NOERROR. (since the initial
	  domain exists).
	- nicer verbosity: 0 and 1 levels.
	- lower nonRDquery chance of eliciting wrongly typed validation
	  requiring message from the cache.
	- fix for nonRDquery validation typing; nodata is detected when
	  SOA record in auth section (all validation-requiring nodata messages
	  have a SOA record in authority, so this is OK for the validator),
	  and NS record is needed to be a referral.
	- duplicate checking when adding NSECs for a CNAME, and test.
	- created svn tag 0.8, after completing testbed tests.

27 November 2007: Wouter
	- per suggestion in rfc2308, replaced default max-ttl value with 1 day.
	- set size of msgparse lookup table to 32, from 1024, so that its size
	  is below the 2048 regional large size threshold, and does not cause
	  a call to malloc when a message is parsed.
	- update of memstats tool to print number of allocation calls.
	  This is what is taking time (not space) and indicates the avg size
	  of the allocations as well. region_alloc stat is removed.

22 November 2007: Wouter
	- noted EDNS in-the-middle dropping trouble as a TODO.
	  At this point theoretical, no user trouble has been reported.
	- added all default AS112 zones.
	- answers from local zone content.
		* positive answer, the rrset in question
		* nodata answer (exist, but not that type).
		* nxdomain answer (domain does not exist).
		* empty-nonterminal answer.
		* But not: wildcard, nsec, referral, rrsig, cname/dname,
			or additional section processing, NS put in auth.
	- test for correct working of static and transparent and couple
	  of important defaults (localhost, as112, reverses). 
	  Also checks deny and refuse settings.
	- fixup implicit zone generation and AA bit for NXDOMAIN on localdata.

21 November 2007: Wouter
	- local zone internal data setup.

20 November 2007: Wouter
	- 0.8 - str2list config support for double string config options.
	- local-zone and local-data options, config storage and documentation.

19 November 2007: Wouter
	- do not downcase NSEC and RRSIG for verification. Follows 
	  draft-ietf-dnsext-dnssec-bis-updates-06.txt.
	- fixup leaking unbound daemons at end of tests.
	- README file updated.
	- nice libevent not found error.
	- README talks about gnu make.
	- 0.8: unit test for addr_mask and fixups for it.
	  and unit test for addr_in_common().
	- 0.8: access-control config file element.
	  and unit test rpl replay file.
	- 0.8: fixup address reporting from netevent.

16 November 2007: Wouter
	- privilege separation is not needed in unbound at this time.
	  TODO item marked as such.
	- created beta-0.7 branch for support.
	- tagged 0.7 for beta release.
	- moved trunk to 0.8 for 0.8(auth features) development.
	- 0.8: access control list setup.

15 November 2007: Wouter
	- review fixups from Jelte.

14 November 2007: Wouter
	- testbed script does not recreate configure, since its in svn now.
	- fixup checkconf test so that it does not test 
	  /etc/unbound/unbound.conf.
	- tag 0.6.

13 November 2007: Wouter
	- remove debug print.
	- fixup testbound exit when LIBEVENT_SIGNAL_PROBLEM exists.

12 November 2007: Wouter
	- fixup signal handling where SIGTERM could be ignored if a SIGHUP
	  arrives later on.
	- <NAME_EMAIL>
	- fixup testbound so it exits cleanly.
	- cleanup the caches on a reload, so that rrsetID numbers won't clash.

9 November 2007: Wouter
	- took ldns snapshot in repo.
	- default config file is /etc/unbound/unbound.conf.
	  If it doesn't exist, it is installed with the doc/example.conf file.
	  The file is not deleted on uninstall.
	- default listening is not all, but localhost interfaces.
	
8 November 2007: Wouter
	- Fixup chroot and drop user privileges.
	- new L root ip address in default hints.

1 November 2007: Wouter
	- Fixup of crash on reload, due to anchors in env not NULLed after
	  dealloc during deinit.
	- Fixup of chroot call. Happens after privileges are dropped, so
	  that checking the passwd entry still works.
	- minor touch up of clear() hashtable function.
	- VERB_DETAIL prints out what chdir, username, chroot is being done.
	- when id numbers run out, caches are cleared, as in design notes.
	  Tested with a mock setup with very few bits in id, it worked.
	- harden-dnssec-stripped: yes is now default. It insists on dnssec
	  data for trust anchors. Included tests for the feature.

31 October 2007: Wouter
	- cache-max-ttl config option.
	- building outside sourcedir works again.
	- defaults more secure:
		username: "unbound"
		chroot: "/etc/unbound"
	  The operator can override them to be less secure ("") if necessary.
	- fix horrible oversight in sorting rrset references in a message,
	  sort per reference key pointer, not on referencepointer itself.
	- pidfile: "/etc/unbound/unbound.pid" is now the default.
	- tests changed to reflect the updated default.
	- created hashtable clear() function that respects locks.

30 October 2007: Wouter
	- fixup assertion failure that relied on compressed names to be
	  smaller than uncompressed names. A packet from comrite.com was seen
	  to be compressed to a larger size. Added it as unit test.
	- quieter logging at low verbosity level for common tcp messages.
	- no greedy TTL update.

23 October 2007: Wouter
	- fixup (grand-)parent problem for dnssec-lameness detection.
	- fixup tests to do additional section processing for lame replies,
	  since the detection needs that.
	- no longer trust in query section in reply during dnssec lame detect.
	- dnssec lameness does not make the server never ever queried, but
	  non-preferred. If no other servers exist or answer, the dnssec lame
	  server is used; the fastest dnssec lame server is chosen.
	- added test then when trust anchor cannot be primed (nodata), the
	  insecure mode from unbound works.
	- Fixup max queries per thread, any more are dropped.

22 October 2007: Wouter
	- added donotquerylocalhost config option. Can be turned off for
	  out test cases.
	- ISO C compat changes.
	- detect RA-no-AA lameness, as LAME.
	- DNSSEC-lameness detection, as LAME.
	  See notes in requirements.txt for choices made.
	- tests for lameness detection.
	- added all to make test target; need unbound for fwd tests.
	- testbound does not pollute /etc/unbound.

19 October 2007: Wouter
	- added configure (and its files) to svn, so that the trunk is easier
	  to use. ./configure, config.guess, config.sub, ltmain.sh,
	  and config.h.in.
	- added yacc/lex generated files, util/configlexer.c, 
	  util/configparser.c util/configparser.h, to svn. 
	- without lex no attempt to use it.
	- unsecure response validation collated into one block.
	- remove warning about const cast of cfgfile name.
	- outgoing-interfaces can be different from service interfaces.
	- ldns-src configure is done during unbound configure and
	  ldns-src make is done during unbound make, and so inherits the
	  make arguments from the unbound make invocation.
	- nicer error when libevent problem causes instant exit on signal.
	- read root hints from a root hint file (like BIND does).
	  
18 October 2007: Wouter
	- addresses are logged with errors.
	- fixup testcode fake event to remove pending before callback
	  since the callback may create new pending items.
	- tests updated because retries are now in iterator module.
	- ldns-testpkts code is checked for differences between unbound
	  and ldns by makedist.sh.
	- ldns trunk from today added in svn repo for fallback in case
	  no ldns is installed on the system.
	  make download_ldns refreshes the tarball with ldns svn trunk.
	- ldns-src.tar.gz is used if no ldns is found on the system, and
	  statically linked into unbound.
	- start of regional allocator code.
	- regional uses less memory and variables, simplified code.
	- remove of region-allocator.
	- alloc cache keeps a cache of recently released regional blocks,
	  up to a maximum.
	- make unit test cleanly free memory.

17 October 2007: Wouter
	- fixup another cycle detect and ns-addr timeout resolution bug.
	  This time by refusing delegations from the cache without addresses
	  when resolving a mandatory-glue nameserver-address for that zone.
	  We're going to have to ask a TLD server anyway; might as well be
	  the TLD server for this name. And this resolves a lot of cases where
	  the other nameserver names lead to cycles or are not available.
	- changed random generator from random(3) clone to arc4random wrapped
	  for thread safety. The random generator is initialised with
	  entropy from the system.
	- fix crash where failure to prime DNSKEY tried to print null pointer
	  in the log message.
	- removed some debug prints, only verb_algo (4) enables them.
	- fixup test; new random generator took new paths; such as one 
	  where no scripted answer was available.
	- mark insecure RRs as insecure.
	- fixup removal of nonsecure items from the additional.
	- reduced timeout values to more realistic, 376 msec (262 msec has
	  90% of roundtrip times, 512 msec has 99% of roundtrip times.)
	- server selection failover to next server after timeout (376 msec).

16 October 2007: Wouter
	- no malloc in log_hex.
	- assertions around system calls.
	- protect against gethostname without ending zero.
	- ntop output is null terminated by unbound.
	- pidfile content null termination
	- various snprintf use sizeof(stringbuf) instead of fixed constant.
	- changed loopdetect % 8 with & 0x7 since % can become negative for
	  weird negative input and particular interpretation of integer math.
	- dname_pkt_copy checks length of result, to protect result buffers.
	  prints an error, this should not happen. Bad strings should have
	  been rejected earlier in the program.
	- remove a size_t underflow from msgreply size func.

15 October 2007: Wouter
	- nicer warning.
	- fix IP6 TCP, wrong definition check. With test package.
	- fixup the fact that the query section was not compressed to,
	  the code was there but was called by value instead of by reference.
	  And test for the case, uses xxd and nc.
	- more portable ip6 check for sockaddr types.

8 October 2007: Wouter
	- --disable-rpath option in configure for 64bit systems with
	  several dynamic lib dirs.

7 October 2007: Wouter
	- fixup tests for no AD bit in non-DO queries.
	- test that makes sure AD bit is not set on non-DO query.

6 October 2007: Wouter
	- removed logfile open early. It did not have the proper permissions;
	  it was opened as root instead of the user. And we cannot change user
	  id yet, since chroot and bind ports need to be done.
	- callback checks for event callbacks done from mini_event. Because
	  of deletions cannot do this from netevent. This means when using
	  libevent the protection does not work on event-callbacks.
	- fixup too small reply (did not zero counts).
	- fixup reply no longer AD bit when query without DO bit.

5 October 2007: Wouter
	- function pointer whitelist.

4 October 2007: Wouter
	- overwrite sensitive random seed value after use.
	- switch to logfile very soon if not -d (console attached).
	- error messages do not reveal the trustanchor contents.
	- start work on function pointer whitelists.

3 October 2007: Wouter
	- fix for multiple empty nonterminals, after multiple DSes in the
	  chain of trust.
	- mesh checks if modules are looping, and stops them.
	- refetch with CNAMEd nameserver address regression test added.
	- fixup line count bug in testcode, so testbound prints correct line
	  number with parse errors.
	- unit test for multiple ENT case.
	- fix for cname out of validated unsec zone.
	- fixup nasty id=0 reuse. Also added assertions to detect its
	  return (the assertion catches in the existing test cases).

1 October 2007: Wouter
	- skip F77, CXX, objC tests in configure step.
	- fixup crash in refetch glue after a CNAME.
	  and protection against similar failures (with error print).

28 September 2007: Wouter
	- test case for unbound-checkconf, fixed so it also checks the
	  interface: statements.

26 September 2007: Wouter
	- SIGHUP will reopen the log file.
	- Option to log to syslog.
	- please lint, fixup tests (that went to syslog on open, oops).
	- config check program.

25 September 2007: Wouter
	- tests for NSEC3. Fixup bitmap checks for NSEC3.
	- positive ANY response needs to check if wildcard expansion, and
	  check that original data did not exist.
	- tests for NSEC3 that wrong use of OPTOUT is bad. For insecure
	  delegation, for abuse of child zone apex nsec3.
	- create 0.5 release tag.

24 September 2007: Wouter
	- do not make test programs by default.
	- But 'make test' will perform all of the tests.
	- Advertise builtin select libevent alternative when no libevent
	  is found.
	- signit can generate NSEC3 hashes, for generating tests.
	- multiple nsec3 parameters in message test.
	- too high nsec3 iterations becomes insecure test.

21 September 2007: Wouter
	- fixup empty_DS_name allocated in wrong region (port DEC Alpha).
	- fixup testcode lock safety (port FreeBSD).
	- removes subscript has type char warnings (port Solaris 9).
	- fixup of field with format type to int (port MacOS/X intel).
	- added test for infinite loop case in nonRD answer validation.
	  It was a more general problem, but hard to reproduce. When an
	  unsigned rrset is being validated and the key fetched, the DS
	  sequence is followed, but if the final name has no DS, then no
	  proof is possible - the signature has been stripped off.

20 September 2007: Wouter
	- fixup and test for NSEC wildcard with empty nonterminals. 
	- makedist.sh fixup for svn info.
	- acl features request in plan.
	- improved DS empty nonterminal handling.
	- compat with ANS nxdomain for empty nonterminals. Attempts the nodata
	  proof anyway, which succeeds in ANS failure case.
	- striplab protection in case it becomes -1.
	- plans for static and blacklist config.

19 September 2007: Wouter
	- comments about non-packed usage.
	- plan for overload support in 0.6.
	- added testbound tests for a failed resolution from the logs
	  and for failed prime when missing glue.
	- fixup so useless delegation points are not returned from the
	  cache. Also the safety belt is used if priming fails to complete.
	- fixup NSEC rdata not to be lowercased, bind compat.

18 September 2007: Wouter
	- wildcard nsec3 testcases, and fixup to get correct wildcard name.
	- validator prints subtype classification for debug.

17 September 2007: Wouter
	- NSEC3 hash cache unit test.
	- validator nsec3 nameerror test.

14 September 2007: Wouter
	- nsec3 nodata proof, nods proof, wildcard proof.
	- nsec3 support for cname chain ending in noerror or nodata.
	- validator calls nsec3 proof routines if no NSECs prove anything.
	- fixup iterator bug where it stored the answer to a cname under
	  the wrong qname into the cache. When prepending the cnames, the
	  qname has to be reset to the original qname.

13 September 2007: Wouter
	- nsec3 find matching and covering, ce proof, prove namerror msg.

12 September 2007: Wouter
	- fixup of manual page warnings, like for NSD bugreport.
	- nsec3 work, config, max iterations, filter, and hash cache. 

6 September 2007: Wouter
	- fixup to find libevent on mac port install.
	- fixup size_t vs unsigned portability in validator/sigcrypt.
	- please compiler on different platforms, for unreachable code.
	- val_nsec3 file.
	- pthread_rwlock type is optional, in case of old pthread libs.

5 September 2007: Wouter
	- cname, name error validator tests.
	- logging of qtype ANY works.
	- ANY type answers get RRSIG in answer section of replies (but not
	  in other sections, unless DO bit is on).
	- testbound can replay a TCP query (set MATCH TCP in the QUERY).
	- DS and noDS referral validation test.
	- if you configure many trust anchors, parent trust anchors can
	  securely deny existence of child trust anchors, if validated.
	- not all *.name NSECs are present because a wildcard was matched,
	  and *.name NSECs can prove nodata for empty nonterminals.
	  Also, for wildcard name NSECs, check they are not from the parent
	  zone (for wildcarded zone cuts), and check absence of CNAME bit,
	  for a nodata proof.
	- configure option for memory allocation debugging.
	- port configure option for memory allocation to solaris10.

4 September 2007: Wouter
	- fixup of Leakage warning when serviced queries processed multiple
	  callbacks for the same query from the same server.
	- testbound removes config file from /tmp on failed exit.
	- fixup for referral cleanup of the additional section.
	- tests for cname, referral validation.
	- neater testbound tpkg output.
	- DNAMEs no longer match their apex when synthesized from the cache.
	- find correct signer name for DNAME responses.
	- wildcarded DNAME test and fixup code to detect.
	- prepend NSEC and NSEC3 rrsets in the iterator while chasing CNAMEs.
	  So that wildcarded CNAMEs get their NSEC with them to the answer.
	- test for a CNAME to a DNAME to a CNAME to an answer, all from
	  different domains, for key fetching and signature checking of
	  CNAME'd messages.

3 September 2007: Wouter
	- Fixed error in iterator that would cause assertion failure in 
	  validator. CNAME to a NXDOMAIN response was collated into a response
	  with both a CNAME and the NXDOMAIN rcode. Added a test that the
	  rcode is changed to NOERROR (because of the CNAME).
	- timeout on tcp does not lead to spurious leakage detect.
	- account memory for name of lame zones, so that memory leakages does
	  not show lame cache growth as a leakage growth.
	- config setting for lameness cache expressed in bytes, instead of
	  number of entries.
	- tool too summarize allocations per code line.

31 August 2007: Wouter
	- can read bind trusted-keys { ... }; files, in a compatibility mode. 
	- iterator should not detach target queries that it still could need.
	  the protection against multiple outstanding queries is moved to a
	  current_query num check.
	- validator nodata, positive, referral tests.
	- dname print can print '*' wildcard.

30 August 2007: Wouter
	- fixup override date config option.
	- config options to control memory usage.
	- caught bad free of un-alloced data in worker_send error case.
	- memory accounting for key cache (trust anchors and temporary cache).
	- memory accounting fixup for outside network tcp pending waits.
	- memory accounting fixup for outside network tcp callbacks.
	- memory accounting for iterator fixed storage.
	- key cache size and slabs config options.
	- lib crypto cleanups at exit. 

29 August 2007: Wouter
	- test tool to sign rrsets for testing validator with.
	- added RSA and DSA test keys, public and private pairs, 512 bits.
	- default configuration is with validation enabled.
	  Only a trust-anchor needs to be configured for DNSSEC to work.
	- do not convert to DER for DSA signature verification.
	- validator replay test file, for a DS to DNSKEY DSA key prime and
	  positive response.

28 August 2007: Wouter
	- removed double use for udp buffers, that could fail,
	  instead performs a malloc to do the backup.
	- validator validates referral messages, by validating all the rrsets
	  and stores the rrsets in the cache. Further referral (nonRD queries)
	  replies are made from the rrset cache directly. Unless unchecked
	  rrsets are encountered, there are then validated.
	- enforce that signing is done by a parent domain (or same domain).
	- adjust TTL downwards if rrset TTL bigger than signature allows.
	- permissive mode feature, sets AD bit for secure, but bogus does
	  not give servfail (bogus is changed into indeterminate).
	- optimization of rrset verification. rr canonical sorting is reused,
	  for the same rrset. canonical rrset image in buffer is reused for
	  the same signature.
	- if the rrset is too big (64k exactly + large owner name) the
	  canonicalization routine will fail if it does not fit in buffer.
	- faster verification for large sigsets.
	- verb_detail mode reports validation failures, but not the entire
	  algorithm for validation. Key prime failures are reported as 
	  verb_ops level.

27 August 2007: Wouter
	- do not garble the edns if a cache answer fails.
	- answer norecursive from cache if possible.
	- honor clean_additional setting when returning secure non-recursive
	  referrals.
	- do not store referral in msg cache for nonRD queries.
	- store verification status in the rrset cache to speed up future
	  verification.
	- mark rrsets indeterminate and insecure if they are found to be so.
	  and store this in the cache.

24 August 2007: Wouter
	- message is bogus if unsecure authority rrsets are present.
	- val-clean-additional option, so you can turn it off.
	- move rrset verification out of the specific proof types into one
	  routine. This makes the proof routines prettier.
	- fixup cname handling in validator, cname-to-positive and cname-to-
	  nodata work.
	- Do not synthesize DNSKEY and DS responses from the rrset cache if
	  the rrset is from the additional section. Signatures may have
	  fallen off the packet, and cause validation failure.
	- more verbose signature date errors (with the date attached).
	- increased default infrastructure cache size. It is important for
	  performance, and 1000 entries are only 212k (or a 400 k total cache
	  size). To 10000 entries (for 2M entries, 4M cache size).

23 August 2007: Wouter
	- CNAME handling - move needs_validation to before val_new().
	  val_new() setups the chase-reply to be an edited copy of the msg.
	  new classification, and find signer can find for it. 
	  removal of unsigned crap from additional, and query restart for
	  cname.
	- refuse to follow wildcarded DNAMEs when validating.
	  But you can query for qtype ANY, or qtype DNAME and validate that.

22 August 2007: Wouter
	- bogus TTL.
	- review - use val_error().

21 August 2007: Wouter
	- ANY response validation.
	- store security status in cache.
	- check cache security status and either send the query to be
	  validated, return the query to client, or send servfail to client.
	  Sets AD bit on validated replies.
	- do not examine security status on an error reply in mesh_done.
	- construct DS, DNSKEY messages from rrset cache.
	- manual page entry for override-date.

20 August 2007: Wouter
	- validate and positive validation, positive wildcard NSEC validation.
	- nodata validation, nxdomain validation.

18 August 2007: Wouter
	- process DNSKEY response in FINDKEY state.

17 August 2007: Wouter
	- work on DS2KE routine.
	- val_nsec.c for validator NSEC proofs.
	- unit test for NSEC bitmap reading.
	- dname iswild and canonical_compare with unit tests.

16 August 2007: Wouter
	- DS sig unit test.
	- latest release libevent 1.3c and 1.3d have threading fixed.
	- key entry fixup data pointer and ttl absolute.
	- This makes a key-prime succeed in validator, with DS or DNSKEY as
	  trust-anchor.
	- fixup canonical compare byfield routine, fix bug and also neater.
	- fixed iterator response type classification for queries of type
	  ANY and NS.
	  dig ANY gives sometimes NS rrset in AN and NS section, and parser
	  removes the NS section duplicate. dig NS gives sometimes the NS
	  in the answer section, as referral.
	- validator FINDKEY state.

15 August 2007: Wouter
	- crypto calls to verify signatures.
	- unit test for rrsig verification.

14 August 2007: Wouter
	- default outgoing ports changed to avoid port 2049 by default.
	  This port is widely blocked by firewalls.
	- count infra lameness cache in memory size.
	- accounting of memory improved
	- outbound entries are allocated in the query region they are for.
	- extensive debugging for memory allocations.
	- --enable-lock-checks can be used to enable lock checking.
	- protect undefs in config.h from autoheaders ministrations.
	- print all received udp packets. log hex will print on multiple
	  lines if needed.
	- fixed error in parser with backwards rrsig references.
	- mark cycle targets for iterator did not have CD flag so failed
	  its task.

13 August 2007: Wouter
	- fixup makefile, if lexer is missing give nice error and do not
	  mess up the dependencies.
	- canonical compare routine updated.
	- canonical hinfo compare.
	- printout list of the queries that the mesh is working on.

10 August 2007: Wouter
	- malloc and free overrides that track total allocation and frees.
	  for memory debugging.
	- work on canonical sort.

9 August 2007: Wouter
	- canonicalization, signature checks
	- dname signature label count and unit test.
	- added debug heap size print to memory printout.
	- typo fixup in worker.c
	- -R needed on solaris.
	- validator override option for date check testing.

8 August 2007: Wouter
	- ldns _raw routines created (in ldns trunk).
	- sigcrypt DS digest routines
	- val_utils uses sigcrypt to perform signature cryptography.
	- sigcrypt keyset processing

7 August 2007: Wouter
	- security status type.
	- security status is copied when rdata is equal for rrsets.
	- rrset id is updated to invalidate all the message cache entries
	  that refer to NSEC, NSEC3, DNAME rrsets that have changed.
	- val_util work
	- val_sigcrypt file for validator signature checks.

6 August 2007: Wouter
	- key cache for validator.
	- moved isroot and dellabel to own dname routines, with unit test.

3 August 2007: Wouter
	- replanning.
	- scrubber check section of lame NS set.
	- trust anchors can be in config file or read from zone file,
	  DS and DNSKEY entries.
	- unit test trust anchor storage.
	- trust anchors converted to packed rrsets.
	- key entry definition.

2 August 2007: Wouter
	- configure change for latest libevent trunk version (needs -lrt).
	- query_done and walk_supers are moved out of module interface.
	- fixup delegation point duplicates.
	- fixup iterator scrubber; lame NS set is let through the scrubber
	  so that the classification is lame.
	- validator module exists, and does nothing but pass through,
	  with calling of next module and return.
	- validator work.

1 August 2007: Wouter
	- set version to 0.5
	- module work for module to module interconnections.
	- config of modules.
	- detect cycle takes flags.

31 July 2007: Wouter
	- updated plan
	- release 0.4 tag.

30 July 2007: Wouter
	- changed random state init, so that sequential process IDs are not
	  cancelled out by sequential thread-ids in the random number seed.
	- the fwd_three test, which sends three queries to unbound, and 
	  unbound is kept waiting by ldns-testns for 3 seconds, failed
	  because the retry timeout for default by unbound is 3 seconds too,
	  it would hit that timeout and fail the test. Changed so that unbound
	  is kept waiting for 2 seconds instead.

27 July 2007: Wouter
	- removed useless -C debug option. It did not work.
	- text edit of documentation.
	- added doc/CREDITS file, referred to by the manpages.
	- updated planning.

26 July 2007: Wouter
	- cycle detection, for query state dependencies. Will attempt to
	  circumvent the cycle, but if no other targets available fails.
	- unit test for AXFR, IXFR response.
	- test for cycle detection.

25 July 2007: Wouter
	- testbound read ADDRESS and check it.
	- test for version.bind and friends.
	- test for iterator chaining through several referrals.
	- test and fixup for refetch for glue. Refetch fails if glue
	  is still not provided.

24 July 2007: Wouter
	- Example section in config manual.
	- Addr stored for range and moment in replay.

20 July 2007: Wouter
	- Check CNAME chain before returning cache entry with CNAMEs.
	- Option harden-glue, default is on. It will discard out of zone
	  data. If disabled, performance is faster, but spoofing attempts
	  become a possibility. Note that still normalize scrubbing is done,
	  and that the potentially spoofed data is used for infrastructure
	  and not returned to the client.
	- if glue times out, refetch by asking parent of delegation again.
	  Much like asking for DS at the parent side.
	- TODO items from forgery-resilience draft.
	  and on memory handling improvements.
	- renamed module_event_timeout to module_event_noreply.
	- memory reporting code; reports on memory usage after handling
	  a network packet (not on cache replies).

19 July 2007: Wouter
	- shuffle NS selection when getting nameserver target addresses.
	- fixup of deadlock warnings, yield cpu in checklock code so that
	  freebsd scheduler selects correct process to run.
	- added identity and version config options and replies.
	- store cname messages complete answers.

18 July 2007: Wouter
	- do not query addresses, 127.0.0.1, and ::1 by default.

17 July 2007: Wouter
	- forward zone options in config file.
	- forward per zone in iterator. takes precedence over stubs.
	- fixup commithooks.
	- removed forward-to and forward-to-port features, subsumed by
	  new forward zones.
	- fix parser to handle absent server: clause.
	- change untrusted rrset test to account for scrubber that is now
	  applied during the test (which removes the poison, by the way).
	- feature, addresses can be specified with @portnumber, like nsd.conf.
	- test config files changed over to new forwarder syntax.

27 June 2007: Wouter
	- delete of mesh does a postorder traverse of the tree.
	- found and fixed a memory leak. For TTL=0 messages, that would
	  not be cached, instead the msg-replyinfo structure was leaked.
	- changed server selection so it will filter out hosts that are
	  unresponsive. This is defined as a host with the maximum rto value.
	  This means that unbound tried the host for retries up to 120 secs.
	  The rto value will time out after host-ttl seconds from the cache.
	  This keeps such unresolvable queries from taking up resources.
	- utility for keeping histogram.

26 June 2007: Wouter
	- mesh is called by worker, and iterator uses it.
	  This removes the hierarchical code.
	  QueryTargets state and Finished state are merged for iterator.
	- forwarder mode no longer sets AA bit on first reply.
	- rcode in walk_supers is not needed.

25 June 2007: Wouter
	- more mesh work.
	- error encode routine for ease.

22 June 2007: Wouter
	- removed unused _node iterator value from rbtree_t. Takes up space.
	- iterator can handle querytargets state without a delegation point
	  set, so that a priming(stub) subquery error can be handled.
	- iterator stores if it is priming or not.
	- log_query_info() neater logging.
	- changed iterator so that it does not alter module_qstate.qinfo
	  but keeps a chase query info. Also query_flags are not altered,
	  the iterator uses chase_flags.
	- fixup crash in case no ports for the family exist.

21 June 2007: Wouter
	- Fixup secondary buffer in case of error callback.
	- cleanup slumber list of runnable states.
	- module_subreq_depth fails to work in slumber list.
	- fixup query release for cached results to sub targets.
	- neater error for tcp connection failure, shows addr in verbose.
	- rbtree_init so that it can be used with preallocated memory.

20 June 2007: Wouter
	- new -C option to enable coredumps after forking away.
	- doc update.
	- fixup CNAME generation by scrubber, and memory allocation of it.
	- fixup deletion of serviced queries when all callbacks delete too.
	- set num target queries to 0 when you move them to slumber list.
	- typo in check caused subquery errors to be ignored, fixed.
	- make lint happy about rlim_t.
	- freeup of modules after freeup of module-states.
	- duplicate replies work, this uses secondary udp buffer in outnet.

19 June 2007: Wouter
	- nicer layout in stats.c, review 0.3 change.
	- spelling improvement, review 0.3 change.
	- uncapped timeout for server selection, so that very fast or slow
	  servers will stand out from the rest.
	- target-fetch-policy: "3 2 1 0 0" config setting.
	- fixup queries answered without RD bit (for root prime results).
	- refuse AXFR and IXFR requests.
	- fixup RD flag in error reply from iterator. fixup RA flag from
	  worker error reply.
	- fixup encoding of very short edns buffer sizes, now sets TC bit.
	- config options harden-short-bufsize and harden-large-queries.

18 June 2007: Wouter
	- same, move subqueries to slumber list when first has resolved.
	- fixup last fix for duplicate callbacks.
	- another offbyone in targetcounter. Also in Java prototype by the way.

15 June 2007: Wouter
	- if a query asks to be notified of the same serviced query result
	  multiple times, this will succeed. Only one callback will happen;
	  multiple outbound-list entries result (but the double cleanup of it
	  will not matter).
	- when iterator moves on due to CNAME or referral, it will remove
	  the subqueries (for other targets). These are put on the slumber
	  list.
	- state module wait subq is OK with no new subqs, an old one may have
	  stopped, with an error, and it is still waiting for other ones.
	- if a query loops, halt entire query (easy way to clean up properly).

14 June 2007: Wouter
	- num query targets was > 0 , not >= 0 compared, so that fetch
	  policy of 0 did nothing.

13 June 2007: Wouter
	- debug option: configure --enable-static-exe for compile where
	  ldns and libevent are linked statically. Default is off.
	- make install and make uninstall. Works with static-exe and without.
	  installation of unbound binary and manual pages.
	- alignment problem fix on solaris 64.
	- fixup address in case of TCP error.

12 June 2007: Wouter
	- num target queries was set to 0 at a bad time. Default it to 0 and
	  increase as target queries are done.
	- synthesize CNAME and DNAME responses from the cache.
	- Updated doxygen config for doxygen 1.5.
	- aclocal newer version.
	- doxygen 1.5 fixes for comments (for the strict check on docs).

11 June 2007: Wouter
	- replies on TCP queries have the address field set in replyinfo,
	  for serviced queries, because the initiator does not know that
	  a TCP fallback has occured.
	- omit DNSSEC types from nonDO replies, except if qtype is ANY or
	  if qtype directly queries for the type (and then only show that
	  'unknown type' in the answer section).
	- fixed message parsing where rrsigs on their own would be put
	  in the signature list over the rrsig type.

7 June 2007: Wouter
	- fixup error in double linked list insertion for subqueries and
	  for outbound list of serviced queries for iterator module.
	- nicer printout of outgoing port selection. 
	- fixup cname target readout.
	- nicer debug output.
	- fixup rrset counts when prepending CNAMEs to the answer.
	- fixup rrset TTL for prepended CNAMEs.
	- process better check for looping modules, and which submodule to
	  run next.
	- subreq insertion code fixup for slumber list.
	- VERB_DETAIL, verbosity: 2 level gives short but readable output.
	  VERB_ALGO, verbosity: 3 gives extensive output.
	- fixup RA bit in cached replies.
	- fixup CNAME responses from the cache no longer partial response.
	- error in network send handled without leakage.
	- enable ip6 from config, and try ip6 addresses if available,
	  if ip6 is not connected, skips to next server.

5 June 2007: Wouter
	- iterator state finished.
	- subrequests without parent store in cache and stop.
	- worker slumber list for ongoing promiscuous queries.
	- subrequest error handling.
	- priming failure returns SERVFAIL.
	- priming gives LAME result, returns SERVFAIL.
	- debug routine to print dns_msg as handled by iterator.
	- memleak in config file stubs fixup.
	- more small bugs, in scrubber, query compare no ID for lookup,
	  in dname validation for NS targets.
	- sets entry.key for new special allocs.
	- lognametypeclass can display unknown types and classes.

4 June 2007: Wouter
	- random selection of equally preferred nameserver targets.
	- reply info copy routine. Reuses existing code.
	- cache lameness in response handling.
	- do not touch qstate after worker_process_query because it may have
	  been deleted by that routine.
	- Prime response state.
	- Process target response state.
	- some memcmp changed to dname_compare for case preservation.

1 June 2007: Wouter
	- normalize incoming messages. Like unbound-java, with CNAME chain
	  checked, DNAME checked, CNAME's synthesized, glue checked.
	- sanitize incoming messages.
	- split msgreply encode functions into own file msgencode.c.
	- msg_parse to queryinfo/replyinfo conversion more versatile.
	- process_response, classify response, delegpt_from_message. 

31 May 2007: Wouter
	- querytargets state.
	- dname_subdomain_c() routine.
	- server selection, based on RTT. ip6 is filtered out if not available,
	  and lameness is checked too.
	- delegation point copy routine.

30 May 2007: Wouter
	- removed FLAG_CD from message and rrset caches. This was useful for
	  an agnostic forwarder, but not for a sophisticated (trust value per
	  rrset enabled) cache.
	- iterator response typing.
	- iterator cname handle.
	- iterator prime start.
	- subquery work.
	- processInitRequest and processInitRequest2.
	- cache synthesizes referral messages, with DS and NSEC.
	- processInitRequest3.
	- if a request creates multiple subrequests these are all activated.

29 May 2007: Wouter
	- routines to lock and unlock array of rrsets moved to cache/rrset.
	- lookup message from msg cache (and copy to region).
	- fixed cast error in dns msg lookup.
	- message with duplicate rrset does not increase its TTLs twice.
	- 'qnamesize' changed to 'qname_len' for similar naming scheme.

25 May 2007: Wouter
	- Acknowledge use of unbound-java code in iterator. Nicer readme.
	- services/cache/dns.c DNS Cache. Hybrid cache uses msgcache and
	  rrset cache from module environment.
	- packed rrset key has type and class as easily accessible struct
	  members. They are still kept in network format for fast msg encode.
	- dns cache find_delegation routine.
	- iterator main functions setup.
	- dns cache lookup setup.

24 May 2007: Wouter
	- small changes to prepare for subqueries.
	- iterator forwarder feature separated out.
	- iterator hints stub code, config file stub code, so that first
	  testing can proceed locally.
	- replay tests now have config option to enable forwarding mode.

23 May 2007: Wouter
	- outside network does precise timers for roundtrip estimates for rtt
	  and for setting timeout for UDP. Pending_udp takes milliseconds.
	- cleaner iterator sockaddr conversion of forwarder address.
	- iterator/iter_utils and iter_delegpt setup.
	- root hints.

22 May 2007: Wouter
	- outbound query list for modules and support to callback with the
	  outbound entry to the module.
	- testbound support for new serviced queries.
	- test for retry to TCP cannot use testbound any longer.
	- testns test for EDNS fallback, test for TCP fallback already exists.
	- fixes for no-locking compile.
	- mini_event timer precision and fix for change in timeouts during
	  timeout callback. Fix for fwd_three tests, performed nonexit query.

21 May 2007: Wouter
	- small comment on hash table locking.
	- outside network serviced queries, contain edns and tcp fallback,
	  and udp retries and rtt timing.

16 May 2007: Wouter
	- lruhash_touch() would cause locking order problems. Fixup in 
	  lock-verify in case locking cycle is found.
	- services/cache/rrset.c for rrset cache code.
	- special rrset_cache LRU updating function that uses the rrset id.
	- no dependencies calculation when make clean is called.
	- config settings for infra cache.
	- daemon code slightly cleaner, only creates caches once.

15 May 2007: Wouter
	- host cache code.
	- unit test for host cache.

14 May 2007: Wouter
	- Port to OS/X and Dec Alpha. Printf format and alignment fixes.
	- extensive lock debug report on join timeout.
	- proper RTT calculation, in utility code.
	- setup of services/cache/infra, host cache.

11 May 2007: Wouter
	- iterator/iterator.c module.
	- fixup to pass reply_info in testcode and in netevent.

10 May 2007: Wouter
	- created release-0.3 svn tag.
	- util/module.h
	- fixed compression - no longer compresses root name.

9 May 2007: Wouter
	- outside network cleans up waiting tcp queries on exit.
	- fallback to TCP.
	- testbound replay with retry in TCP mode.
	- tpkg test for retry in TCP mode, against ldns-testns server.
	- daemon checks max number of open files and complains if not enough.
	- test where data expires in the cache.
	- compiletests: fixed empty body ifstatements in alloc.c, in case
	  locks are disabled.

8 May 2007: Wouter
	- outgoing network keeps list of available tcp buffers for outgoing 
	  tcp queries.
	- outgoing-num-tcp config option.
	- outgoing network keeps waiting list of queries waiting for buffer.
	- netevent supports outgoing tcp commpoints, nonblocking connects.

7 May 2007: Wouter
	- EDNS read from query, used to make reply smaller.
	- advertised edns value constants.
	- EDNS BADVERS response, if asked for too high edns version.
	- EDNS extended error responses once the EDNS record from the query
	  has successfully been parsed.

4 May 2007: Wouter
	- msgreply sizefunc is more accurate.
	- config settings for rrset cache size and slabs.
	- hashtable insert takes argument so that a thread can use its own
	  alloc cache to store released keys.
	- alloc cache special_release() locks if necessary.
	- rrset trustworthiness type added.
	- thread keeps a scratchpad region for handling messages.
	- writev used in netevent to write tcp length and data after another.
	  This saves a roundtrip on tcp replies.
	- test for one rrset updated in the cache.
	- test for one rrset which is not updated, as it is not deemed
	  trustworthy enough.
	- test for TTL refreshed in rrset.

3 May 2007: Wouter
	- fill refs. Use new parse and encode to answer queries.
	- stores rrsets in cache.
	- uses new msgreply format in cache.

2 May 2007: Wouter
	- dname unit tests in own file and spread out neatly in functions.
	- more dname unit tests.
	- message encoding creates truncated TC flagged messages if they do 
	  not fit, and will leave out (whole)rrsets from additional if needed.

1 May 2007: Wouter
	- decompress query section, extremely lenient acceptance.
	  But only for answers from other servers, not for plain queries.
	- compression and decompression test cases.
	- some stats added.
	- example.conf interface: line is changed from 127.0.0.1 which leads
	  to problems if used (restricting communication to the localhost),
	  to a documentation and test address.

27 April 2007: Wouter
	- removed iov usage, it is not good for dns message encoding.
	- owner name compression more optimal.
	- rrsig owner name compression.
	- rdata domain name compression.

26 April 2007: Wouter
	- floating point exception fix in lock-verify.
	- lint uses make dependency
	- fixup lint in dname owner domain name compression code.
	- define for offset range that can be compressed to.

25 April 2007: Wouter
	- prettier code; parse_rrset->type kept in host byte order.
	- datatype used for hashvalue of converted rrsig structure.
	- unit test compares edns section data too.

24 April 2007: Wouter
	- ttl per RR, for RRSIG rrsets and others.
	- dname_print debug function.
	- if type is not known, size calc will skip DNAME decompression.
	- RRSIG parsing and storing and putting in messages.
	- dnssec enabled unit tests (from nlnetlabs.nl and se queries).
	- EDNS extraction routine.

20 April 2007: Wouter
	- code comes through all of the unit tests now.
	- disabled warning about spurious extra data.
	- documented the RRSIG parse plan in msgparse.h.
	- rrsig reading and outputting.

19 April 2007: Wouter
	- fix unit test to actually to tests.
	- fix write iov helper, and fakevent code.
	- extra builtin testcase (small packet).
	- ttl converted to network format in packets.
	- flags converted correctly
	- rdatalen off by 2 error fixup.
	- uses less iov space for header.

18 April 2007: Wouter
	- review of msgparse code.
	- smaller test cases.

17 April 2007: Wouter
	- copy and decompress dnames.
	- store calculated hash value too.
	- routine to create message out of stored information.
	- util/data/msgparse.c for message parsing code.
	- unit test, and first fixes because of test.
		* forgot rrset_count addition.
		* did & of ptr on stack for memory position calculation.
		* dname_pkt_copy forgot to read next label length.
	- test from file and fixes
		* double frees fixed in error conditions.
		* types with less than full rdata allowed by parser.
		  Some dynamic update packets seem to use it.

16 April 2007: Wouter
	- following a small change in LDNS, parsing code calculates the
	  memory size to allocate for rrs.
	- code to handle ID creation.

13 April 2007: Wouter
	- parse routines. Code that parses rrsets, rrs.

12 April 2007: Wouter
	- dname compare routine that preserves case, with unit tests.
	
11 April 2007: Wouter
	- parse work - dname packet parse, msgparse, querysection parse,
	  start of sectionparse.

10 April 2007: Wouter
	- Improved alignment of reply_info packet, nice for 32 and 64 bit.
	- Put RRset counts in reply_info, because the number of RRs can change
	  due to RRset updates.
	- import of region-allocator code from nsd.
	- set alloc special type to ub_packed_rrset_key.
	  Uses lruhash entry overflow chain next pointer in alloc cache.
	- doxygen documentation for region-allocator.
	- setup for parse scratch data.

5 April 2007: Wouter
	- discussed packed rrset with Jelte.

4 April 2007: Wouter
	- moved to version 0.3.
	- added util/data/dname.c
	- layout of memory for rrsets.

3 April 2007: Wouter
	- detect sign of msghdr.msg_iovlen so that the cast to that type
	  in netevent (which is there to please lint) can be correct.
	  The type on several OSes ranges from int, int32, uint32, size_t.
	  Detects unsigned or signed using math trick.
	- constants for DNS flags. 
	- compilation without locks fixup.
	- removed include of unportable header from lookup3.c.
	- more portable use of struct msghdr.
	- casts for printf warning portability.
	- tweaks to tests to port them to the testbed.
	- 0.2 tag created.

2 April 2007: Wouter
	- check sizes of udp received messages, not too short.
	- review changes. Some memmoves can be memcpys: 4byte aligned.
	  set id correctly on cached answers. 
	- review changes msgreply.c, memleak on error condition. AA flag
	  clear on cached reply. Lowercase queries on hashing.
	  unit test on lowercasing. Test AA bit not set on cached reply.
	  Note that no TTLs are managed.

29 March 2007: Wouter
	- writev or sendmsg used when answering from cache.
	  This avoids a copy of the data.
	- do not do useless byteswap on query id. Store reply flags in uint16
	  for easier access (and no repeated byteswapping).
	- reviewed code.
	- configure detects and config.h includes sys/uio.h for writev decl.

28 March 2007: Wouter
	- new config option: num-queries-per-thread.
	- added tpkg test for answering three queries at the same time
	  using one thread (from the query service list).

27 March 2007: Wouter
	- added test for cache and not cached answers, in testbound replays.
	- testbound can give config file and commandline options from the
	  replay file to unbound.
	- created test that checks if items drop out of the cache.
	- added word 'partitioned hash table' to documentation on slab hash.
	  A slab hash is a partitioned hash table.
	- worker can handle multiple queries at a time.

26 March 2007: Wouter
	- config settings for slab hash message cache.
	- test for cached answer.
	- Fixup deleting fake answer from testbound list.

23 March 2007: Wouter
	- review of yesterday's commits.
	- covered up memory leak of the entry locks.
	- answers from the cache correctly. Copies flags correctly.
	- sanity check for incoming query replies.
	- slabbed hash table. Much nicer contention, need dual cpu to see.

22 March 2007: Wouter
	- AIX configure check.
	- lock-verify can handle references to locks that are created
	  in files it has not yet read in.
	- threaded hash table test. 
	- unit test runs lock-verify afterwards and checks result.
	- need writelock to update data on hash_insert.
	- message cache code, msgreply code.

21 March 2007: Wouter
	- unit test of hash table, fixup locking problem in table_grow().
	- fixup accounting of sizes for removing items from hashtable.
	- unit test for hash table, single threaded test of integrity.
	- lock-verify reports errors nicely. More quiet in operation.

16 March 2007: Wouter
	- lock-verifier, checks consistent order of locking.

14 March 2007: Wouter
	- hash table insert (and subroutines) and lookup implemented.
	- hash table remove.
	- unit tests for hash internal bin, lru functions.

13 March 2007: Wouter
	- lock_unprotect in checklocks.
	- util/storage/lruhash.h for LRU hash table structure.

12 March 2007: Wouter
	- configure.ac moved to 0.2.
	- query_info and replymsg util/data structure.

9 March 2007: Wouter
	- added rwlock writelock checking.
	  So it will keep track of the writelock, and readlocks are enforced
	  to not change protected memory areas.
	- log_hex function to dump hex strings to the logfile.
	- checklocks zeroes its destroyed lock after checking memory areas.
	- unit test for alloc.
	- identifier for union in checklocks to please older compilers.
	- created 0.1 tag.

8 March 2007: Wouter
	- Reviewed checklock code.

7 March 2007: Wouter
	- created a wrapper around thread calls that performs some basic
	  checking for data race and deadlock, and basic performance 
	  contention measurement.

6 March 2007: Wouter
	- Testbed works with threading (different machines, different options).
	- alloc work, does the special type.

2 March 2007: Wouter
	- do not compile fork funcs unless needed. Otherwise will give
	  type errors as their typedefs have not been enabled.
	- log shows thread numbers much more nicely (and portably).
	- even on systems with nonthreadsafe libevent signal handling,
	  unbound will exit if given a signal.
	  Reloads will not work, and exit is not graceful.
	- start of alloc framework layout.

1 March 2007: Wouter
	- Signals, libevent and threads work well, with libevent patch and
	  changes to code (close after event_del).
	- set ipc pipes nonblocking.

27 February 2007: Wouter
	- ub_thread_join portable definition.
	- forking is used if no threading is available.
	  Tested, it works, since pipes work across processes as well.
	  Thread_join is replaced with waitpid. 
	- During reloads the daemon will temporarily handle signals,
	  so that they do not result in problems.
	- Also randomize the outgoing port range for tests.
	- If query list is full, will stop selecting listening ports for read.
	  This makes all threads service incoming requests, instead of one.
	  No memory is leaking during reloads, service of queries, etc.
	- test that uses ldns-testns -f to test threading. Have to answer
	  three queries at the same time.
	- with verbose=0 operates quietly.

26 February 2007: Wouter
	- ub_random code used to select ID and port.
	- log code prints thread id.
	- unbound can thread itself, with reload(HUP) and quit working
	  correctly.
	- don't open pipes for #0, doesn't need it.
	- listens to SIGTERM, SIGQUIT, SIGINT (all quit) and SIGHUP (reload).

23 February 2007: Wouter
	- Can do reloads on sigHUP. Everything is stopped, and freed,
	  except the listening ports. Then the config file is reread.
	  And everything is started again (and listening ports if needed).
	- Ports for queries are shared.
	- config file added interface:, chroot: and username:.
	- config file: directory, logfile, pidfile. And they work too.
	- will daemonize by default now. Use -d to stay in the foreground.
	- got BSD random[256 state] code, made it threadsafe. util/random.

22 February 2007: Wouter
	- Have a config file. Removed commandline options, moved to config.
	- tests use config file.

21 February 2007: Wouter
	- put -c option in man page.
	- minievent fd array capped by FD_SETSIZE.

20 February 2007: Wouter
	- Added locks code and pthread spinlock detection.
	- can use no locks, or solaris native thread library.
	- added yacc and lex configure, and config file parsing code.
	  also makedist.sh, and manpage.
	- put include errno.h in config.h

19 February 2007: Wouter
	- Created 0.0 svn tag.
	- added acx_pthread.m4 autoconf check for pthreads from 
	  the autoconf archive. It is GPL-with-autoconf-exception Licensed.
	  You can specify --with-pthreads, or --without-pthreads to configure.

16 February 2007: Wouter
	- Updated testbed script, works better by using make on remote end.
	- removed check decls, we can compile without them.
	- makefile supports LIBOBJ replacements.
	- docs checks ignore compat code.
	- added util/mini-event.c and .h, a select based alternative used with
	  ./configure --with-libevent=no
	  It is limited to 1024 file descriptors, and has less features.
	- will not create ip6 sockets if ip6 not on the machine.

15 February 2007: Wouter
	- port to FreeBSD 4.11 Dec Alpha. Also works on Solaris 10 sparc64,
	  Solaris 9, FreeBSD 6, Linux i386 and OSX powerpc.
	- malloc rndstate, so that it is aligned for access.
	- fixed rbtree cleanup with postorder traverse.
	- fixed pending messages are deleted when handled.
	- You can control verbosity; default is not verbose, every -v
	  adds more verbosity.

14 February 2007: Wouter
	- Included configure.ac changes from ldns.
	- detect (some) headers before the standards check.
	- do not use isblank to test c99, since its not available on solaris9.
	- review of testcode.
		* entries in a RANGE are no longer reversed.
		* print name of file with replay entry parse errors.
	- port to OSX: cast to int for some prints of sizet.
	- Makefile copies ldnstestpkts.c before doing dependencies on it.

13 February 2007: Wouter
	- work on fake events, first fwd replay works.
	- events can do timeouts and errors on queries to servers.
	- test package that runs replay scenarios.

12 February 2007: Wouter
	- work on fake events.

9 February 2007: Wouter
	- replay file reading.
	- fake event setup, it creates fake structures, and teardowns,
	  added signal callbacks to reply to be able to fake those,
	  and main structure of event replay routines.

8 February 2007: Wouter
	- added tcp test.
	- replay storage.
	- testcode/fake_event work.

7 February 2007: Wouter
	- return answer with the same ID as query was sent with.
	- created udp forwarder test. I've done some effort to make it perform
	  quickly. After servers are created, no big sleep statements but
	  it checks the logfiles to see if servers have come up. Takes 0.14s.
	- set addrlen value when calling recvfrom.
	- comparison of addrs more portable.
	- LIBEVENT option for testbed to set libevent directory.
	- work on tcp input.

6 February 2007: Wouter
	- reviewed code and improved in places.

5 February 2007: Wouter
	- Picked up stdc99 and other define tests from ldns. Improved
	  POSIX define test to include getaddrinfo.
	- defined constants for netevent callback error code.
	- unit test for strisip6.

2 February 2007: Wouter
	- Created udp4 and udp6 port arrays to provide service for both
	  address families.
	- uses IPV6_USE_MIN_MTU for udp6 ,IPV6_V6ONLY to make ip6 sockets.
	- listens on both ip4 and ip6 ports to provide correct return address.
	- worker fwder address filled correctly.
	- fixup timer code.
	- forwards udp queries and sends answer.

1 February 2007: Wouter
	- outside network more UDP work.
	- moved * closer to type.
	- comm_timer object and events.

31 January 2007: Wouter
	- Added makedist.sh script to make release tarball.
	- Removed listen callback layer, did not add anything.
	- Added UDP recv to netevent, worker callback for udp.
	- netevent communication reply storage structure.
	- minimal query header sanity checking for worker.
	- copied over rbtree implementation from NSD (BSD licensed too).
	- outgoing network query service work.

30 January 2007: Wouter
	- links in example/ldns-testpkts.c and .h for premade packet support.
	- added callback argument to listen_dnsport and daemon/worker.

29 January 2007: Wouter
	- unbound.8 a short manpage.

26 January 2007: Wouter
	- fixed memleak.
	- make lint works on BSD and Linux (openssl defines).
	- make tags works.
	- testbound program start.

25 January 2007: Wouter
	- fixed lint so it may work on BSD.
	- put license into header of every file.
	- created verbosity flag.
	- fixed libevent configure flag.
	- detects event_base_free() in new libevent 1.2 version.
	- getopt in daemon. fatal_exit() and verbose() logging funcs.
	- created log_assert, that throws assertions to the logfile.
	- listen_dnsport service. Binds ports.

24  January 2007: Wouter
	- cleaned up configure.ac.

23  January 2007: Wouter
	- added libevent to configure to link with.
	- util/netevent setup work.
	- configure searches for libevent.
	- search for libs at end of configure (when other headers and types
	  have been found).
	- doxygen works with ATTR_UNUSED().
	- util/netevent implementation.

22  January 2007: Wouter
	- Designed header file for network communication.

16  January 2007: Wouter
	- added readme.svn and readme.tests.

4 January 2007: Wouter
	- Testbed script (run on multiple platforms the test set).
	  Works on Sunos9, Sunos10, FreeBSD 6.1, Fedora core 5.
	- added unit test tpkg.

3 January 2007: Wouter
	- committed first set of files into subversion repository.
	  svn co svn+ssh://unbound.net/svn/unbound
	  You need a ssh login.  There is no https access yet.
	- Added LICENSE, the BSD license.
	- Added doc/README with compile help.
	- main program stub and quiet makefile.
	- minimal logging service (to stderr).
	- added postcommit hook that serves emails.
	- added first test 00-lint. postcommit also checks if build succeeds.
	- 01-doc: doxygen doc target added for html docs. And stringent test
	  on documented files, functions and parameters.

15 December 2006: Wouter
	- Created Makefile.in and configure.ac.
