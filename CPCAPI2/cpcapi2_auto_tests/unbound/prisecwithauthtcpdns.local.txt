local-zone: "prisecwithauthtcpdnstcpdns.local." static
local-data: "working.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working2.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working3.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working4.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working5.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working6.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working7.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working8.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working9.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working10.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working11.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working12.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working13.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working14.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working15.prisecwithauthtcpdns.local. A 127.0.0.1"
local-data: "working16.prisecwithauthtcpdns.local. A 127.0.0.1"
# SRV record format: priority, weight, port, target
#    priority: the priority of the target host, lower value means more preferred.
#    weight: A relative weight for records with the same priority, higher value means more preferred.
# needs to have two repro instances running; one with repro_auth1.config, one with repro_auth2.config
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 0 0 7080 working.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working2.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working3.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working4.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working5.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working6.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working7.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working8.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working9.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working10.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working11.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working12.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working13.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working14.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working15.prisecwithauthtcpdns.local."
local-data: "_sip._tcp.prisecwithauthtcpdns.local. SRV 10 0 7090 working16.prisecwithauthtcpdns.local."
