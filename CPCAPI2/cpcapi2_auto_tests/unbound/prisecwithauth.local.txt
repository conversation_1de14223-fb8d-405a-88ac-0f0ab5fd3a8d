local-zone: "prisecwithauth.local." static
# local-data: "prisecwithauth.local. NAPTR 10 100 SIP+D2T '' _sip._tcp.prisecwithauth.local."
local-data: "working.prisecwithauth.local. 10 A 127.0.0.1"
local-data: "working2.prisecwithauth.local. 10 A 127.0.0.1"
local-data: "working3.prisecwithauth.local. 10 A 127.0.0.1"
# SRV record format: priority, weight, port, target
#    priority: the priority of the target host, lower value means more preferred.
#    weight: A relative weight for records with the same priority, higher value means more preferred.
# needs to have two repro instances running; one with repro_auth1.config, one with repro_auth2.config
local-data: "_sip._tcp.prisecwithauth.local. 10 SRV 0 0 7080 working.prisecwithauth.local."
local-data: "_sip._tcp.prisecwithauth.local. 10 SRV 10 0 7090 working2.prisecwithauth.local."
local-data: "_sip._tcp.prisecwithauth.local. 10 SRV 20 0 7070 working3.prisecwithauth.local."
local-data: "_sip._udp.prisecwithauth.local. 10 SRV 0 0 7080 working.prisecwithauth.local."
local-data: "_sip._udp.prisecwithauth.local. 10 SRV 10 0 7090 working2.prisecwithauth.local."
local-data: "_sip._udp.prisecwithauth.local. 10 SRV 20 0 7070 working3.prisecwithauth.local."
local-data: "_sips._tcp.prisecwithauth.local. 10 SRV 0 0 7081 working.prisecwithauth.local."
local-data: "_sips._tcp.prisecwithauth.local. 10 SRV 10 0 7091 working2.prisecwithauth.local."
local-data: "_sips._tcp.prisecwithauth.local. 10 SRV 20 0 7071 working3.prisecwithauth.local."
# NAPTR record format: preference order flag services regex replacement
#    preference: preference for the record, lower number should be tried first
#    order: order to try the records, only relevant if multiple records have the same preference
#    flag: refers to queries required based on result, "S" for SRV, "A" "AAAA" "A6" for hosts, "U" for absolute URI, "P" for additional NAPTRs
#    services: for services e.g. "SIP+D2U" sip over UDP, "SIP+D2T" sip over TCP, "E2U+email" for email
#    regex: regular expression used to mutate the request domain into something different
#    replacement: mutually exclusive to regex, is the result of the NAPTR lookup on the domain

