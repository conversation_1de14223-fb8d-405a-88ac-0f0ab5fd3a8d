local-zone: "tcpdnsv6.local." static

local-data: "manysrvrecords2.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords3.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords4.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords5.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords6.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords7.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords8.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords9.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords10.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords11.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords12.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords13.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords14.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords15.tcpdnsv6.local. AAAA 0100::"
local-data: "manysrvrecords.tcpdnsv6.local. AAAA ::1"


# SRV record format: priority, weight, port, target
#    priority: the priority of the target host, lower value means more preferred.
#    weight: A relative weight for records with the same priority, higher value means more preferred.
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords2.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords3.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords4.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords5.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords6.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords7.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords8.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords9.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords10.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords11.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords12.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords13.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords14.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords15.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 10 0 6060 manysrvrecords16.tcpdnsv6.local."
local-data: "_sip._udp.tcpdnsv6.local. SRV 0 0 6060 manysrvrecords.tcpdnsv6.local."